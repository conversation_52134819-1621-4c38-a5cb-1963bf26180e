<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.finance.dao.CapitalBillDetailMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.finance.model.CapitalBillDetail" >
    <id column="CAPITAL_BILL_DETAIL_ID" property="capitalBillDetailId" jdbcType="INTEGER" />
    <result column="CAPITAL_BILL_ID" property="capitalBillId" jdbcType="INTEGER" />
    <result column="BUSSINESS_TYPE" property="bussinessType" jdbcType="INTEGER" />
    <result column="ORDER_TYPE" property="orderType" jdbcType="BIT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="RELATED_ID" property="relatedId" jdbcType="INTEGER" />
    <result column="AMOUNT" property="amount" jdbcType="DECIMAL" />
    <result column="PAYER" property="payer" jdbcType="VARCHAR" />
    <result column="PAYEE" property="payee" jdbcType="VARCHAR" />
    <result column="COMMENTS" property="comments" jdbcType="VARCHAR" />
    <result column="TRADER_ID" property="traderId" jdbcType="INTEGER" />
    <result column="TRADER_TYPE" property="traderType" jdbcType="BIT" />
    <result column="USER_ID" property="userId" jdbcType="INTEGER" />
    <result column="ORG_ID" property="orgId" jdbcType="INTEGER" />
    <result column="ORG_NAME" property="orgName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    CAPITAL_BILL_DETAIL_ID, CAPITAL_BILL_ID, BUSSINESS_TYPE, ORDER_TYPE, ORDER_NO, RELATED_ID, 
    AMOUNT,TRADER_ID,TRADER_TYPE,USER_ID,ORG_ID,ORG_NAME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from T_CAPITAL_BILL_DETAIL
    where CAPITAL_BILL_DETAIL_ID = #{capitalBillDetailId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from T_CAPITAL_BILL_DETAIL
    where CAPITAL_BILL_DETAIL_ID = #{capitalBillDetailId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.finance.model.CapitalBillDetail" >
    insert into T_CAPITAL_BILL_DETAIL (CAPITAL_BILL_DETAIL_ID, CAPITAL_BILL_ID, 
      BUSSINESS_TYPE, ORDER_TYPE, ORDER_NO, RELATED_ID, 
      AMOUNT,TRADER_ID,TRADER_TYPE,USER_ID)
    values (#{capitalBillDetailId,jdbcType=INTEGER}, #{capitalBillId,jdbcType=INTEGER}, 
      #{bussinessType,jdbcType=INTEGER}, #{orderType,jdbcType=BIT}, #{orderNo,jdbcType=VARCHAR}, #{relatedId,jdbcType=INTEGER}, 
      #{amount,jdbcType=DECIMAL},#{traderId,jdbcType=INTEGER}, #{traderType,jdbcType=BIT},#{userId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.finance.model.CapitalBillDetail" >
    insert into T_CAPITAL_BILL_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="capitalBillDetailId != null" >
        CAPITAL_BILL_DETAIL_ID,
      </if>
      <if test="capitalBillId != null" >
        CAPITAL_BILL_ID,
      </if>
      <if test="bussinessType != null" >
        BUSSINESS_TYPE,
      </if>
      <if test="orderType != null" >
        ORDER_TYPE,
      </if>
      <if test="orderNo != null" >
        ORDER_NO,
      </if>
      <if test="relatedId != null" >
        RELATED_ID,
      </if>
      <if test="amount != null" >
        AMOUNT,
      </if>
      <if test="traderId != null" >
        TRADER_ID,
      </if>
      <if test="traderType != null" >
        TRADER_TYPE,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="orgId != null" >
        ORG_ID,
      </if>
      <if test="orgName != null" >
        ORG_NAME,
      </if>
      <if test="afterSalesInstallstionId != null" >
        AFTER_SALES_INSTALLSTION_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="capitalBillDetailId != null" >
        #{capitalBillDetailId,jdbcType=INTEGER},
      </if>
      <if test="capitalBillId != null" >
        #{capitalBillId,jdbcType=INTEGER},
      </if>
      <if test="bussinessType != null" >
        #{bussinessType,jdbcType=INTEGER},
      </if>
      <if test="orderType != null" >
        #{orderType,jdbcType=BIT},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="relatedId != null" >
        #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="amount != null" >
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="traderId != null" >
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderType != null" >
        #{traderType,jdbcType=BIT},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="orgId != null" >
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="orgName != null" >
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="afterSalesInstallstionId != null" >
        #{afterSalesInstallstionId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.finance.model.CapitalBillDetail" >
    update T_CAPITAL_BILL_DETAIL
    <set >
      <if test="capitalBillId != null" >
        CAPITAL_BILL_ID = #{capitalBillId,jdbcType=INTEGER},
      </if>
      <if test="bussinessType != null" >
        BUSSINESS_TYPE = #{bussinessType,jdbcType=INTEGER},
      </if>
      <if test="orderType != null" >
        ORDER_TYPE = #{orderType,jdbcType=BIT},
      </if>
      <if test="orderNo != null" >
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="relatedId != null" >
        RELATED_ID = #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="amount != null" >
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="traderId != null" >
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderType != null" >
        TRADER_TYPE = #{traderType,jdbcType=BIT},
      </if>
      <if test="userId != null" >
         USER_ID = #{userId,jdbcType=INTEGER},
      </if>
      <if test="orgId != null" >
         ORG_ID = #{orgId,jdbcType=INTEGER},
      </if>
      <if test="orgName != null" >
        ORG_NAME = #{orgName,jdbcType=VARCHAR},
      </if>
    </set>
    where CAPITAL_BILL_DETAIL_ID = #{capitalBillDetailId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.finance.model.CapitalBillDetail" >
    update T_CAPITAL_BILL_DETAIL
    set CAPITAL_BILL_ID = #{capitalBillId,jdbcType=INTEGER},
      BUSSINESS_TYPE = #{bussinessType,jdbcType=INTEGER},
      ORDER_TYPE = #{orderType,jdbcType=BIT},
      ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      RELATED_ID = #{relatedId,jdbcType=INTEGER},
      AMOUNT = #{amount,jdbcType=DECIMAL},
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      TRADER_TYPE = #{traderType,jdbcType=BIT},
      USER_ID = #{userId,jdbcType=INTEGER}
    where CAPITAL_BILL_DETAIL_ID = #{capitalBillDetailId,jdbcType=INTEGER}
  </update>
  
   <resultMap id="CapitalBillDetailResultMap" type="com.vedeng.finance.model.CapitalBillDetail" extends="BaseResultMap">
  	<association property="saleorder" javaType="com.vedeng.order.model.Saleorder">
	    <id column="SALEORDER_ID" property="saleorderId" jdbcType="INTEGER" />
	    <result column="QUOTEORDER_ID" property="quoteorderId" jdbcType="INTEGER" />
	    <result column="PARENT_ID" property="parentId" jdbcType="INTEGER" />
	    <result column="SALEORDER_NO" property="saleorderNo" jdbcType="VARCHAR" />
	    <result column="ORDER_TYPE" property="orderType" jdbcType="BIT" />
	    <result column="ORG_ID" property="orgId" jdbcType="INTEGER" />
	    <result column="USER_ID" property="userId" jdbcType="INTEGER" />
	    <result column="VALID_STATUS" property="validStatus" jdbcType="BIT" />
	    <result column="VALID_TIME" property="validTime" jdbcType="BIGINT" />
	    <result column="STATUS" property="status" jdbcType="BIT" />
	    <result column="PURCHASE_STATUS" property="purchaseStatus" jdbcType="BIT" />
	    <result column="LOCKED_STATUS" property="lockedStatus" jdbcType="BIT" />
	    <result column="INVOICE_STATUS" property="invoiceStatus" jdbcType="BIT" />
	    <result column="INVOICE_TIME" property="invoiceTime" jdbcType="BIGINT" />
	    <result column="PAYMENT_STATUS" property="paymentStatus" jdbcType="BIT" />
	    <result column="PAYMENT_TIME" property="paymentTime" jdbcType="BIGINT" />
	    <result column="DELIVERY_STATUS" property="deliveryStatus" jdbcType="BIT" />
	    <result column="DELIVERY_TIME" property="deliveryTime" jdbcType="BIGINT" />
	    <result column="ARRIVAL_STATUS" property="arrivalStatus" jdbcType="BIT" />
	    <result column="ARRIVAL_TIME" property="arrivalTime" jdbcType="BIGINT" />
	    <result column="SERVICE_STATUS" property="serviceStatus" jdbcType="BIT" />
	    <result column="HAVE_ACCOUNT_PERIOD" property="haveAccountPeriod" jdbcType="BIT" />
	    <result column="TOTAL_AMOUNT" property="totalAmount" jdbcType="DECIMAL" />
	    <result column="TRADER_ID" property="traderId" jdbcType="INTEGER" />
	    <result column="CUSTOMER_TYPE" property="customerType" jdbcType="INTEGER" />
	    <result column="CUSTOMER_NATURE" property="customerNature" jdbcType="INTEGER" />
	    <result column="TRADER_NAME" property="traderName" jdbcType="VARCHAR" />
	    <result column="TRADER_CONTACT_ID" property="traderContactId" jdbcType="INTEGER" />
	    <result column="TRADER_CONTACT_NAME" property="traderContactName" jdbcType="VARCHAR" />
	    <result column="TRADER_CONTACT_MOBILE" property="traderContactMobile" jdbcType="VARCHAR" />
	    <result column="TRADER_CONTACT_TELEPHONE" property="traderContactTelephone" jdbcType="VARCHAR" />
	    <result column="TRADER_ADDRESS_ID" property="traderAddressId" jdbcType="INTEGER" />
	    <result column="TRADER_ADDRESS" property="traderAddress" jdbcType="VARCHAR" />
	    <result column="TRADER_COMMENTS" property="traderComments" jdbcType="VARCHAR" />
	    <result column="TAKE_TRADER_ID" property="takeTraderId" jdbcType="INTEGER" />
	    <result column="TAKE_TRADER_NAME" property="takeTraderName" jdbcType="VARCHAR" />
	    <result column="TAKE_TRADER_CONTACT_ID" property="takeTraderContactId" jdbcType="INTEGER" />
	    <result column="TAKE_TRADER_CONTACT_NAME" property="takeTraderContactName" jdbcType="VARCHAR" />
	    <result column="TAKE_TRADER_CONTACT_MOBILE" property="takeTraderContactMobile" jdbcType="VARCHAR" />
	    <result column="TAKE_TRADER_CONTACT_TELEPHONE" property="takeTraderContactTelephone" jdbcType="VARCHAR" />
	    <result column="TAKE_TRADER_ADDRESS_ID" property="takeTraderAddressId" jdbcType="INTEGER" />
	    <result column="TAKE_TRADER_ADDRESS" property="takeTraderAddress" jdbcType="VARCHAR" />
	    <result column="IS_SEND_INVOICE" property="isSendInvoice" jdbcType="BIT" />
	    <result column="INVOICE_TRADER_ID" property="invoiceTraderId" jdbcType="INTEGER" />
	    <result column="INVOICE_TRADER_NAME" property="invoiceTraderName" jdbcType="VARCHAR" />
	    <result column="INVOICE_TRADER_CONTACT_ID" property="invoiceTraderContactId" jdbcType="INTEGER" />
	    <result column="INVOICE_TRADER_CONTACT_NAME" property="invoiceTraderContactName" jdbcType="VARCHAR" />
	    <result column="INVOICE_TRADER_CONTACT_MOBILE" property="invoiceTraderContactMobile" jdbcType="VARCHAR" />
	    <result column="INVOICE_TRADER_CONTACT_TELEPHONE" property="invoiceTraderContactTelephone" jdbcType="VARCHAR" />
	    <result column="INVOICE_TRADER_ADDRESS_ID" property="invoiceTraderAddressId" jdbcType="INTEGER" />
	    <result column="INVOICE_TRADER_ADDRESS" property="invoiceTraderAddress" jdbcType="VARCHAR" />
	    <result column="SALES_AREA_ID" property="salesAreaId" jdbcType="INTEGER" />
	    <result column="SALES_AREA" property="salesArea" jdbcType="VARCHAR" />
	    <result column="TERMINAL_TRADER_ID" property="terminalTraderId" jdbcType="INTEGER" />
	    <result column="TERMINAL_TRADER_NAME" property="terminalTraderName" jdbcType="VARCHAR" />
	    <result column="TERMINAL_TRADER_TYPE" property="terminalTraderType" jdbcType="INTEGER" />
	    <result column="INVOICE_TYPE" property="invoiceType" jdbcType="INTEGER" />
	    <result column="FREIGHT_DESCRIPTION" property="freightDescription" jdbcType="INTEGER" />
	    <result column="DELIVERY_TYPE" property="deliveryType" jdbcType="INTEGER" />
	    <result column="LOGISTICS_ID" property="logisticsId" jdbcType="INTEGER" />
	    <result column="PAYMENT_TYPE" property="paymentType" jdbcType="INTEGER" />
	    <result column="PREPAID_AMOUNT" property="prepaidAmount" jdbcType="DECIMAL" />
	    <result column="ACCOUNT_PERIOD_AMOUNT" property="accountPeriodAmount" jdbcType="DECIMAL" />
	    <result column="LOGISTICS_COLLECTION" property="logisticsCollection" jdbcType="BIT" />
	    <result column="RETAINAGE_AMOUNT" property="retainageAmount" jdbcType="DECIMAL" />
	    <result column="RETAINAGE_AMOUNT_MONTH" property="retainageAmountMonth" jdbcType="INTEGER" />
	    <result column="PAYMENT_COMMENTS" property="paymentComments" jdbcType="VARCHAR" />
	    <result column="ADDITIONAL_CLAUSE" property="additionalClause" jdbcType="VARCHAR" />
	    <result column="LOGISTICS_COMMENTS" property="logisticsComments" jdbcType="VARCHAR" />
	    <result column="FINANCE_COMMENTS" property="financeComments" jdbcType="VARCHAR" />
	    <result column="COMMENTS" property="comments" jdbcType="VARCHAR" />
	    <result column="INVOICE_COMMENTS" property="invoiceComments" jdbcType="VARCHAR" />
	    <result column="DELIVERY_DIRECT" property="deliveryDirect" jdbcType="BIT" />
	    <result column="SUPPLIER_CLAUSE" property="supplierClause" jdbcType="VARCHAR" />
	    <result column="HAVE_ADVANCE_PURCHASE" property="haveAdvancePurchase" jdbcType="BIT" />
	    <result column="IS_URGENT" property="isUrgent" jdbcType="BIT" />
	    <result column="URGENT_AMOUNT" property="urgentAmount" jdbcType="DECIMAL" />
	    <result column="HAVE_COMMUNICATE" property="haveCommunicate" jdbcType="BIT" />
	    <result column="PREPARE_COMMENTS" property="prepareComments" jdbcType="VARCHAR" />
	    <result column="MARKETING_PLAN" property="marketingPlan" jdbcType="VARCHAR" />
	    <result column="SYNC_STATUS" property="syncStatus" jdbcType="BIT" />
	    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
	    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
	    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
	    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
	    <result column="RECEIVED_AMOUNT" property="receivedAmount" jdbcType="DECIMAL" />
  	</association>
  	<association property="buyorder" javaType="com.vedeng.order.model.Buyorder">
	  	<id column="BUYORDER_ID" property="buyorderId" jdbcType="INTEGER" />
	    <result column="BUYORDER_NO" property="buyorderNo" jdbcType="VARCHAR" />
	    <result column="ORDER_TYPE" property="orderType" jdbcType="BIT" />
	    <result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
	    <result column="ORG_ID" property="orgId" jdbcType="INTEGER" />
	    <result column="USER_ID" property="userId" jdbcType="INTEGER" />
	    <result column="VALID_STATUS" property="validStatus" jdbcType="BIT" />
	    <result column="VALID_TIME" property="validTime" jdbcType="BIGINT" />
	    <result column="STATUS" property="status" jdbcType="BIT" />
	    <result column="LOCKED_STATUS" property="lockedStatus" jdbcType="BIT" />
	    <result column="INVOICE_STATUS" property="invoiceStatus" jdbcType="BIT" />
	    <result column="INVOICE_TIME" property="invoiceTime" jdbcType="BIGINT" />
	    <result column="PAYMENT_STATUS" property="paymentStatus" jdbcType="BIT" />
	    <result column="PAYMENT_TIME" property="paymentTime" jdbcType="BIGINT" />
	    <result column="DELIVERY_STATUS" property="deliveryStatus" jdbcType="BIT" />
	    <result column="DELIVERY_TIME" property="deliveryTime" jdbcType="BIGINT" />
	    <result column="ARRIVAL_STATUS" property="arrivalStatus" jdbcType="BIT" />
	    <result column="ARRIVAL_TIME" property="arrivalTime" jdbcType="BIGINT" />
	    <result column="SERVICE_STATUS" property="serviceStatus" jdbcType="BIT" />
	    <result column="HAVE_ACCOUNT_PERIOD" property="haveAccountPeriod" jdbcType="BIT" />
	    <result column="DELIVERY_DIRECT" property="deliveryDirect" jdbcType="BIT" />
	    <result column="TOTAL_AMOUNT" property="totalAmount" jdbcType="DECIMAL" />
	    <result column="TRADER_ID" property="traderId" jdbcType="INTEGER" />
	    <result column="TRADER_NAME" property="traderName" jdbcType="VARCHAR" />
	    <result column="TRADER_CONTACT_ID" property="traderContactId" jdbcType="INTEGER" />
	    <result column="TRADER_CONTACT_NAME" property="traderContactName" jdbcType="VARCHAR" />
	    <result column="TRADER_CONTACT_MOBILE" property="traderContactMobile" jdbcType="VARCHAR" />
	    <result column="TRADER_CONTACT_TELEPHONE" property="traderContactTelephone" jdbcType="VARCHAR" />
	    <result column="TRADER_ADDRESS_ID" property="traderAddressId" jdbcType="INTEGER" />
	    <result column="TRADER_ADDRESS" property="traderAddress" jdbcType="VARCHAR" />
	    <result column="TRADER_COMMENTS" property="traderComments" jdbcType="VARCHAR" />
	    <result column="TAKE_TRADER_ID" property="takeTraderId" jdbcType="INTEGER" />
	    <result column="TAKE_TRADER_NAME" property="takeTraderName" jdbcType="VARCHAR" />
	    <result column="TAKE_TRADER_CONTACT_ID" property="takeTraderContactId" jdbcType="INTEGER" />
	    <result column="TAKE_TRADER_CONTACT_NAME" property="takeTraderContactName" jdbcType="VARCHAR" />
	    <result column="TAKE_TRADER_CONTACT_MOBILE" property="takeTraderContactMobile" jdbcType="VARCHAR" />
	    <result column="TAKE_TRADER_CONTACT_TELEPHONE" property="takeTraderContactTelephone" jdbcType="VARCHAR" />
	    <result column="TAKE_TRADER_ADDRESS_ID" property="takeTraderAddressId" jdbcType="INTEGER" />
	    <result column="TAKE_TRADER_ADDRESS" property="takeTraderAddress" jdbcType="VARCHAR" />
	    <result column="PAYMENT_TYPE" property="paymentType" jdbcType="INTEGER" />
	    <result column="PREPAID_AMOUNT" property="prepaidAmount" jdbcType="DECIMAL" />
	    <result column="ACCOUNT_PERIOD_AMOUNT" property="accountPeriodAmount" jdbcType="DECIMAL" />
	    <result column="RETAINAGE_AMOUNT" property="retainageAmount" jdbcType="DECIMAL" />
	    <result column="RETAINAGE_AMOUNT_MONTH" property="retainageAmountMonth" jdbcType="INTEGER" />
	    <result column="LOGISTICS_ID" property="logisticsId" jdbcType="INTEGER" />
	    <result column="INVOICE_TYPE" property="invoiceType" jdbcType="INTEGER" />
	    <result column="FREIGHT_DESCRIPTION" property="freightDescription" jdbcType="INTEGER" />
	    <result column="PAYMENT_COMMENTS" property="paymentComments" jdbcType="VARCHAR" />
	    <result column="LOGISTICS_COMMENTS" property="logisticsComments" jdbcType="VARCHAR" />
	    <result column="INVOICE_COMMENTS" property="invoiceComments" jdbcType="VARCHAR" />
	    <result column="COMMENTS" property="comments" jdbcType="VARCHAR" />
	    <result column="ADDITIONAL_CLAUSE" property="additionalClause" jdbcType="VARCHAR" />
	    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
	    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
	    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
	    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
	    <result column="TAKE_TRADER_AREA" property="takeTraderArea" jdbcType="VARCHAR" />
	    <result column="TRADER_AREA" property="traderArea" jdbcType="VARCHAR" />
	    <result column="SATISFY_DELIVERY_TIME" property="satisfyDeliveryTime" jdbcType="BIGINT" />
  	</association>
  	<association property="afterSales" javaType="com.vedeng.aftersales.model.AfterSales">
  		<id column="AFTER_SALES_ID" property="afterSalesId" jdbcType="INTEGER" />
    	<result column="AFTER_SALES_NO" property="afterSalesNo" jdbcType="VARCHAR" />
    	<result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
    	<result column="SUBJECT_TYPE" property="subjectType" jdbcType="INTEGER" />
    	<result column="TYPE" property="type" jdbcType="INTEGER" />
    	<result column="ORDER_ID" property="orderId" jdbcType="INTEGER" />
	    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
	    <result column="SERVICE_USER_ID" property="serviceUserId" jdbcType="INTEGER" />
	    <result column="VALID_STATUS" property="validStatus" jdbcType="BIT" />
	    <result column="VALID_TIME" property="validTime" jdbcType="BIGINT" />
	    <result column="STATUS" property="status" jdbcType="BIT" />
	    <result column="ATFER_SALES_STATUS" property="atferSalesStatus" jdbcType="BIT" />
	    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
	    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
	    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
	    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
  	</association>
  </resultMap>
  
  <select id="selectByBankBillId" resultMap="CapitalBillDetailResultMap" parameterType="java.lang.Integer">
  		SELECT cb.PAYER,cb.PAYEE,cb.COMMENTS,
			cbd.*, s.*,e.RECEIVED_AMOUNT
		FROM
			T_CAPITAL_BILL cb
		LEFT JOIN T_CAPITAL_BILL_DETAIL cbd ON cb.CAPITAL_BILL_ID = cbd.CAPITAL_BILL_ID
		LEFT JOIN T_SALEORDER s ON cbd.RELATED_ID = s.SALEORDER_ID
		LEFT JOIN (
			SELECT
			c.SALEORDER_ID,sum(ABS(b.AMOUNT)) AS RECEIVED_AMOUNT
			FROM
				T_CAPITAL_BILL a
			LEFT JOIN T_CAPITAL_BILL_DETAIL b ON a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
			LEFT JOIN T_SALEORDER c ON b.RELATED_ID = c.SALEORDER_ID
			WHERE
				 b.ORDER_TYPE = 1
				AND a.BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
			GROUP BY c.SALEORDER_ID
		) e on e.SALEORDER_ID = s.SALEORDER_ID
		WHERE
			1 = 1
		AND cbd.ORDER_TYPE = 1
		AND cb.BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
  </select>
  
  <select id="selectByBankBillIdAndNo" resultMap="CapitalBillDetailResultMap">
  		SELECT cb.PAYER,cb.PAYEE,cb.COMMENTS,
			cbd.*, s.*,e.RECEIVED_AMOUNT
		FROM
			T_CAPITAL_BILL cb
		LEFT JOIN T_CAPITAL_BILL_DETAIL cbd ON cb.CAPITAL_BILL_ID = cbd.CAPITAL_BILL_ID
		LEFT JOIN T_SALEORDER s ON cbd.RELATED_ID = s.SALEORDER_ID
		LEFT JOIN (
			SELECT
			c.SALEORDER_ID,sum(ABS(b.AMOUNT)) AS RECEIVED_AMOUNT
			FROM
				T_CAPITAL_BILL a
			LEFT JOIN T_CAPITAL_BILL_DETAIL b ON a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
			LEFT JOIN T_SALEORDER c ON b.RELATED_ID = c.SALEORDER_ID
			WHERE
				 b.ORDER_TYPE = 1
				AND a.BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
			GROUP BY c.SALEORDER_ID
		) e on e.SALEORDER_ID = s.SALEORDER_ID
		WHERE
			1 = 1
		AND cbd.ORDER_TYPE = 1
		AND cb.BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
		<if test="orderNo != null and orderNo != ''">
			AND cbd.ORDER_NO LIKE CONCAT('%',#{orderNo, jdbcType=VARCHAR},'%' )
		</if>
  </select>
  
  <select id="selectBuyorderByBankBillId" resultMap="CapitalBillDetailResultMap" parameterType="java.lang.Integer">
  		SELECT cb.PAYER,cb.PAYEE,cb.COMMENTS,
			cbd.*, s.*,e.RECEIVED_AMOUNT
		FROM
			T_CAPITAL_BILL cb
		LEFT JOIN T_CAPITAL_BILL_DETAIL cbd ON cb.CAPITAL_BILL_ID = cbd.CAPITAL_BILL_ID
		LEFT JOIN T_BUYORDER s ON cbd.RELATED_ID = s.BUYORDER_ID
		LEFT JOIN (
			SELECT
			c.BUYORDER_ID,sum(ABS(b.AMOUNT)) AS RECEIVED_AMOUNT
			FROM
				T_CAPITAL_BILL a
			LEFT JOIN T_CAPITAL_BILL_DETAIL b ON a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
			LEFT JOIN T_BUYORDER c ON b.RELATED_ID = c.BUYORDER_ID
			WHERE
				 b.ORDER_TYPE = 1
				AND a.BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
			GROUP BY c.BUYORDER_ID
		) e on e.BUYORDER_ID = s.BUYORDER_ID
		WHERE
			1 = 1
		AND cbd.ORDER_TYPE = 2
		AND cb.BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
  </select>
  
  <select id="selectBuyorderByBankBillIdAndNo" resultMap="CapitalBillDetailResultMap">
  		SELECT cb.PAYER,cb.PAYEE,cb.COMMENTS,
			cbd.*, s.*,e.RECEIVED_AMOUNT
		FROM
			T_CAPITAL_BILL cb
		LEFT JOIN T_CAPITAL_BILL_DETAIL cbd ON cb.CAPITAL_BILL_ID = cbd.CAPITAL_BILL_ID
		LEFT JOIN T_BUYORDER s ON cbd.RELATED_ID = s.BUYORDER_ID
		LEFT JOIN (
			SELECT
			c.BUYORDER_ID,sum(ABS(b.AMOUNT)) AS RECEIVED_AMOUNT
			FROM
				T_CAPITAL_BILL a
			LEFT JOIN T_CAPITAL_BILL_DETAIL b ON a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
			LEFT JOIN T_BUYORDER c ON b.RELATED_ID = c.BUYORDER_ID
			WHERE
				 b.ORDER_TYPE = 1
				AND a.BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
			GROUP BY c.BUYORDER_ID
		) e on e.BUYORDER_ID = s.BUYORDER_ID
		WHERE
			1 = 1
		AND cbd.ORDER_TYPE = 2
		AND cb.BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
		<if test="orderNo != null and orderNo != ''">
			AND cbd.ORDER_NO LIKE CONCAT('%',#{orderNo, jdbcType=VARCHAR},'%' )
		</if>
  </select>
  
  <select id="selectAfterSaleByBankBillId" resultMap="CapitalBillDetailResultMap" parameterType="java.lang.Integer">
  		SELECT cb.PAYER,cb.PAYEE,cb.COMMENTS,
			cbd.*, s.*,e.RECEIVED_AMOUNT
		FROM
			T_CAPITAL_BILL cb
		LEFT JOIN T_CAPITAL_BILL_DETAIL cbd ON cb.CAPITAL_BILL_ID = cbd.CAPITAL_BILL_ID
		LEFT JOIN T_AFTER_SALES s ON cbd.RELATED_ID = s.AFTER_SALES_ID
		LEFT JOIN (
			SELECT
			c.AFTER_SALES_ID,sum(ABS(b.AMOUNT)) AS RECEIVED_AMOUNT
			FROM
				T_CAPITAL_BILL a
			LEFT JOIN T_CAPITAL_BILL_DETAIL b ON a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
			LEFT JOIN T_AFTER_SALES c ON b.RELATED_ID = c.AFTER_SALES_ID
			WHERE
				 b.ORDER_TYPE = 1
				AND a.BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
			GROUP BY c.AFTER_SALES_ID
		) e on e.AFTER_SALES_ID = s.AFTER_SALES_ID
		WHERE
			1 = 1
		AND cbd.ORDER_TYPE = 3
		AND cb.BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
  </select>
  
  <select id="selectAfterSaleByBankBillIdAndNo" resultMap="CapitalBillDetailResultMap">
  		SELECT cb.PAYER,cb.PAYEE,cb.COMMENTS,
			cbd.*, s.*,e.RECEIVED_AMOUNT
		FROM
			T_CAPITAL_BILL cb
		LEFT JOIN T_CAPITAL_BILL_DETAIL cbd ON cb.CAPITAL_BILL_ID = cbd.CAPITAL_BILL_ID
		LEFT JOIN T_AFTER_SALES s ON cbd.RELATED_ID = s.AFTER_SALES_ID
		LEFT JOIN (
			SELECT
			c.AFTER_SALES_ID,sum(ABS(b.AMOUNT)) AS RECEIVED_AMOUNT
			FROM
				T_CAPITAL_BILL a
			LEFT JOIN T_CAPITAL_BILL_DETAIL b ON a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
			LEFT JOIN T_AFTER_SALES c ON b.RELATED_ID = c.AFTER_SALES_ID
			WHERE
				 b.ORDER_TYPE = 1
				AND a.BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
			GROUP BY c.AFTER_SALES_ID
		) e on e.AFTER_SALES_ID = s.AFTER_SALES_ID
		WHERE
			1 = 1
		AND cbd.ORDER_TYPE = 3
		AND cb.BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
		<if test="orderNo != null and orderNo != ''">
			AND cbd.ORDER_NO LIKE CONCAT('%',#{orderNo, jdbcType=VARCHAR},'%' )
		</if>
  </select>
  
  <select id="selectListByBankBillId" resultMap="CapitalBillDetailResultMap">
  		SELECT cbd.*
		FROM
			T_CAPITAL_BILL cb
		LEFT JOIN T_CAPITAL_BILL_DETAIL cbd ON cb.CAPITAL_BILL_ID = cbd.CAPITAL_BILL_ID
		WHERE
			1 = 1
		AND cb.BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
  </select>
  <select id="getCapitalUser" parameterType="java.lang.Integer" resultType="com.vedeng.finance.model.CapitalBillDetail">
  	SELECT
		a.USER_ID,
		d.ORG_ID,
		d.ORG_NAME
	FROM
		T_USER a
	LEFT JOIN T_R_USER_POSIT b ON a.USER_ID = b.USER_ID
	LEFT JOIN T_POSITION c ON b.POSITION_ID = c.POSITION_ID
	LEFT JOIN T_ORGANIZATION d ON c.ORG_ID = d.ORG_ID
	WHERE
		a.USER_ID = #{userId,jdbcType=INTEGER}
	LIMIT 1
  </select>
	<select id="getCapitalBillList" resultType="com.vedeng.finance.model.CapitalBill">
		select
		a.*, b.CAPITAL_BILL_DETAIL_ID, b.BUSSINESS_TYPE, b.ORDER_NO
		from T_CAPITAL_BILL a
		join T_CAPITAL_BILL_DETAIL b on a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
		<where>
			1=1
			<if
					test="capitalBillDetail.bussinessType != null and capitalBillDetail.bussinessType != ''">
				<!-- 业务类型 -->
				AND b.BUSSINESS_TYPE =
				#{capitalBillDetail.bussinessType,jdbcType=INTEGER}
			</if>
			<if
					test="capitalBillDetail.orderType != null and capitalBillDetail.orderType != ''">
				<!-- 订单类型 -->
				AND b.ORDER_TYPE = #{capitalBillDetail.orderType,jdbcType=BIT}
			</if>
			<if
					test="capitalBillDetail.relatedId != null and capitalBillDetail.relatedId != ''">
				<!-- 关联表ID -->
				AND b.RELATED_ID = #{capitalBillDetail.relatedId,jdbcType=INTEGER}
			</if>
			<if
					test="capitalBillDetail.traderId != null and capitalBillDetail.traderId != ''">
				<!-- 关联表ID -->
				AND b.TRADER_ID = #{capitalBillDetail.traderId,jdbcType=INTEGER}
			</if>
			<if
					test="capitalBillDetail.traderType != null and capitalBillDetail.traderType != ''">
				<!-- 关联表ID -->
				AND b.TRADER_TYPE = #{capitalBillDetail.traderType,jdbcType=BIT}
			</if>
			<if test="addTime != null and addTime > 0">
				AND a.ADD_TIME &lt; #{addTime,jdbcType=BIGINT}
			</if>
		</where>
		order by a.CAPITAL_BILL_ID DESC
	</select>
	<select id="getAfterReturnCapitalBillList" resultType="com.vedeng.finance.model.CapitalBill">
		<!-- 销售 -->
		<if test="operationType != null and operationType == 'finance_sale_detail'">
			SELECT C.*,
			B.CAPITAL_BILL_DETAIL_ID,
			B.BUSSINESS_TYPE,
			B.ORDER_NO
			FROM T_AFTER_SALES A
			INNER JOIN T_CAPITAL_BILL_DETAIL B ON A.AFTER_SALES_ID = B.RELATED_ID
			INNER JOIN T_CAPITAL_BILL C ON B.CAPITAL_BILL_ID = C.CAPITAL_BILL_ID
			WHERE A.ORDER_ID = #{capitalBillDetail.relatedId,jdbcType=INTEGER}
			AND A.SUBJECT_TYPE = 535
			AND C.TRADER_TYPE IN (3,5)	<!-- 转移、转出 -->
			AND C.TRADER_MODE IN (521, 529, 530)	<!-- 退还银行、信用、余额 -->
			AND B.ORDER_TYPE = 3	<!-- 订单类型3售后 -->
			<!-- 交易者ID -->
			<if
					test="capitalBillDetail.traderId != null and capitalBillDetail.traderId != ''">
				AND B.TRADER_ID = #{capitalBillDetail.traderId,jdbcType=INTEGER}
			</if>
			<!-- 所属类型 1::经销商（包含终端）2:供应商 -->
			<if
					test="capitalBillDetail.traderType != null and capitalBillDetail.traderType != ''">
				AND B.TRADER_TYPE = #{capitalBillDetail.traderType,jdbcType=BIT}
			</if>
			<if test="addTime != null and addTime > 0">
				AND C.ADD_TIME &lt; #{addTime,jdbcType=BIGINT}
			</if>
			AND B.BUSSINESS_TYPE IN (531,533)	<!-- 退款,信用还款 -->
			AND A.VALID_STATUS = 1	<!-- 生效 -->
			AND A.ATFER_SALES_STATUS IN (1, 2)	<!-- 进行中，已完结 -->
			ORDER BY B.CAPITAL_BILL_ID DESC
		</if>
		<!-- 采购 -->
		<if test="operationType != null and operationType == 'finance_buy_detail'">
			SELECT C.*,
			B.CAPITAL_BILL_DETAIL_ID,
			B.BUSSINESS_TYPE,
			B.ORDER_NO
			FROM T_AFTER_SALES A
			INNER JOIN T_CAPITAL_BILL_DETAIL B ON A.AFTER_SALES_ID = B.RELATED_ID
			INNER JOIN T_CAPITAL_BILL C ON B.CAPITAL_BILL_ID = C.CAPITAL_BILL_ID
			WHERE A.ORDER_ID = #{capitalBillDetail.relatedId,jdbcType=INTEGER}
			AND A.SUBJECT_TYPE = 536
			AND C.TRADER_TYPE IN (1, 3, 4)	<!-- 收入、转移、转入 -->
			AND C.TRADER_MODE IN (521, 529, 530)	<!-- 退还银行、信用、余额 -->
			AND B.ORDER_TYPE = 3	<!-- 订单类型3售后 -->
			<!-- 交易者ID -->
			<if
					test="capitalBillDetail.traderId != null and capitalBillDetail.traderId != ''">
				AND B.TRADER_ID = #{capitalBillDetail.traderId,jdbcType=INTEGER}
			</if>
			<!-- 所属类型 1::经销商（包含终端）2:供应商 -->
			<if
					test="capitalBillDetail.traderType != null and capitalBillDetail.traderType != ''">
				AND B.TRADER_TYPE = #{capitalBillDetail.traderType,jdbcType=BIT}
			</if>
			<if test="addTime != null and addTime > 0">
				AND C.ADD_TIME &lt; #{addTime,jdbcType=BIGINT}
			</if>
			AND B.BUSSINESS_TYPE IN (531,533)	<!-- 退款,信用还款 -->
			AND A.VALID_STATUS = 1	<!-- 生效 -->
			AND A.ATFER_SALES_STATUS IN (1, 2)	<!-- 进行中，已完结 -->
			ORDER BY B.CAPITAL_BILL_ID DESC
		</if>
	</select>
	<select id="checkAfterCapitalBillAmountOfHcOrder" resultType="com.vedeng.finance.model.CapitalBillDetail">
		select c.AMOUNT, s.SALEORDER_NO as ORDER_NO
			from T_CAPITAL_BILL_DETAIL c
         join T_CAPITAL_BILL b
              on c.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID and c.ORDER_TYPE = 3 and b.TRADER_TYPE in (3, 5) and
                 b.TRADER_MODE in (521, 529, 530)
         join T_AFTER_SALES a on c.RELATED_ID = a.AFTER_SALES_ID
         join T_SALEORDER s on a.ORDER_ID = s.SALEORDER_ID
    	and s.ORDER_TYPE = 5
		where c.CAPITAL_BILL_DETAIL_ID = #{capitalBillDetailId,jdbcType=INTEGER}
	</select>
	<select id="getMatchAliPayInfo" resultType="com.vedeng.finance.model.CapitalBillDetail">
		SELECT CBD.*,CB.TRADER_SUBJECT AS traderSubject,CB.PAYER AS payer,CB.COMMENTS AS comments
		FROM T_CAPITAL_BILL CB
				 LEFT JOIN T_CAPITAL_BILL_DETAIL CBD ON CB.CAPITAL_BILL_ID = CBD.CAPITAL_BILL_ID
		WHERE
			CB.CAPITAL_SEARCH_FLOW = #{tranFlow,jdbcType = VARCHAR}
		  AND CB.BANK_BILL_ID = 0
	</select>
	<select id="selectByCapitalBillId" resultType="com.vedeng.finance.model.CapitalBillDetail">
		SELECT *
		FROM T_CAPITAL_BILL_DETAIL CB
		WHERE
			CB.CAPITAL_BILL_ID = #{capitalBillId,jdbcType = INTEGER}
	</select>
</mapper>