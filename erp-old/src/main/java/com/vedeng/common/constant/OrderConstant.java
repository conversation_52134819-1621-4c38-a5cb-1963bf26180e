
package com.vedeng.common.constant;

import com.google.common.collect.Lists;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <b>Description: 订单常量</b><br>
 * <b>Author: <PERSON><PERSON><PERSON></b>
 * 
 * @fileName OrderConstant.java <br>
 *           <b>Date: 2018年10月26日 上午10:48:27 </b>
 *
 */
public class OrderConstant {

	/**
	 * 订单单个商品价格和总额限制
	 */
	public static final BigDecimal AMOUNT_LIMIT=new BigDecimal("300000000.00");
    /**
     * 订单单个商品数量限制
     */
	public static final int GOODS_NUM_LIMIT=100000000;
	/**
	 * 订单类型 0--销售订单
	 */
	public static final Integer ORDER_TYPE_SALE = 0;

	/**
	 * 订单类型 2--BD订单
	 */
	public static final Integer ORDER_TYPE_BD = 1;

	/**
	 * 订单类型 2--备货订单
	 */
	public static final Integer ORDER_TYPE_BH = 2;

	/**
	 * 订单类型 3--订货订单
	 */
	public static final Integer ORDER_TYPE_DH = 3;

	/**
	 * 订单类型 4--经销商订单
	 */
	public static final Integer ORDER_TYPE_JX = 4;

	/**
	 * 订单类型 5--耗材订单
	 */
	public static final Integer ORDER_TYPE_HC = 5;

	/**
	 * 订单类型 6--小医院订单
	 */
	public static final Integer ORDER_TYPE_EL = 6;

	/**
	 * 订单类型 7--集采线上订单
	 */
	public static final Integer ORDER_TYPE_JCO = 7;
	/**
	 * 订单类型 8--集采线下订单
	 */
	public static final Integer ORDER_TYPE_JCF = 8;

	/**
	 * 订单类型 9--线下直销订单
	 */
	public static final Integer ORDER_TYPE_ZXF = 9;

	public static final Integer ORDER_SOURCE_ADK_3 = 3;

	public static final Integer ORDER_SOURCE_EL_4 = 4;

	// 订单的一些初始化状态
	public static final Integer ORDER_STATUS_INIT0 = 0;
	//生效
	public static final Integer ORDER_STATUS_VALID = 1;
	/**
	 * 订单类型 1--订货订单
	 */
	//public static final Integer ORDER_TYPE_BD = 1;

	public static final Integer CANCEL_BD_TIMEOUT=1;
	public static final Integer CANCEL_BD_HAND=2;

	//集采订单-待用户确认
	public static final Integer ORDER_STATUS_CONFITM = -1;

	//订单状态-待确认
	public static final Integer ORDER_STATUS_UNCONFIRM = 0;

	//订单状态-进行中
	public static final Integer ORDER_STATUS_PROCESSING = 1;

	//订单状态-已关闭
	public static final Integer ORDER_STATUS_CLOSE = 3;
	// 采购单状态-已完结
	public static final Integer ATFER_SALES_STATUS_CONFIRM = 2;

	//付款状态-未付款
	public static final Integer ORDER_PAYMENT_STATUS_UNPAY = 0;

	//直发
	public static final Integer DELEVIRY_STATUS_1 = 1;

	//订单限制改价
	public static final Integer SALEORDER_LIMIT_PRICE = 1;
	//全部付款
	public static final Integer ORDER_ALL_PAYMENT = 2;
	//部分付款
	public static final Integer ORDER_SOME_PAYMENT = 1;

	//全部开票
	public static final Integer ORDER_ALL_INVOICE = 2;
	public static final Integer ORDER_ALL_DELIVERY = 2;

	/**
	 * 商机未被合并
	 */
	public static final Integer UNMERGE_STATUS=0;
	/**
	 * 商机被合并
	 */
	public static final Integer BE_MERGED_STATUS=1;
	/**
	 * 商机合并其他
	 */
	public static final Integer NEW_MERGED_STATUS=2;
	/**
	 * 报价单关联bd订单状态为审核中
	 */
	public static final Integer QUOTE_LINK_BD_CHECKIND_STATUS=1;
	/**
	 * 报价单关联bd订单状态为审核通过
	 */
	public static final Integer QUOTE_LINK_BD_PASS_STATUS=2;

	/**
	 * 报价单关联bd订单状态为驳回
	 */
	public static final Integer QUOTE_LINK_BD_REJECT_STATUS=3;



	/**
	 * 付款方式 - 先款后货，预付100%
	 */
	public static final Integer PREPAY_100_PERCENT = SysOptionConstant.ID_419;

	/**
	 * 付款方式 - 先货后款，预付80%
	 */
	public static final Integer PREPAY_80_PERCENT = 420;

	/**
	 * 付款方式 - 先货后款，预付50%
	 */
	public static final Integer PREPAY_50_PERCENT = 421;

	/**
	 * 付款方式 - 先货后款，预付30%
	 */
	public static final Integer PREPAY_30_PERCENT = 422;

	/**
	 * 先货后款，预付0%
	 */
	public static final Integer PREPAY_0_PERCENT = SysOptionConstant.ID_423;

	/**
	 * 自定义
	 */
	public static final Integer CUSTOMIZE_PAYMENT = 424;


	/**
	 * 销售订单付款计划
	 */
	public static final List PAYMENET_TYPE_FOR_SALEORDER = Collections.unmodifiableList(
			Lists.newArrayList(PREPAY_100_PERCENT, PREPAY_80_PERCENT, PREPAY_50_PERCENT , PREPAY_30_PERCENT,
			PREPAY_0_PERCENT, CUSTOMIZE_PAYMENT)
	);

	public static final String LOCKED_REASON = "售后锁定";

	public static final Integer UN_LOCKED = 0;

	public static final Integer LOCKED = 1;

    /**
     * 发票类型 - 13%增值税普通发票
     */
    public static final Integer INVOICE_TYPE_13_PERCENT_NORMAL = SysOptionConstant.ID_971;

    /**
     * 发票类型 - 13%增值税普通发票
     */
    public static final Integer INVOICE_TYPE_13_PERCENT_SPECIAL = SysOptionConstant.ID_972;


	/**
	 * 订单来源 0.ERP 1.前台
	 */
	public static final Integer ORDER_SOURCE_ERP = 0;
	public static final Integer ORDER_SOURCE_WEB = 1;
	/** 订单合同已回传 **/
	public static final Integer ORDER_CONTRACT_ISRETURN = 1;
	/** 订单合同未回传 **/
	public static final Integer ORDER_CONTRACT_NORETURN = 0;
	/** 订单合同审核通过 **/
	public static final Integer CONTRACT_VERIFY_PASS = 1;
	/** 订单合同审核驳回 **/
	public static final Integer CONTRACT_VERIFY_REJECT = 2;
	/** 订单送货单已回传 **/
	public static final Integer ORDER_DELIVERYORDER_ISRETURN = 1;
	/** 订单送货单未回传 **/
	public static final Integer ORDER_DELIVERYORDER_NORETURN = 0;
	/** 合同回传 attachment表标识码 **/
	public static final Integer ORDER_CONTRACT = 492;
	/** 送货单回传 attachment表标识码 **/
	public static final Integer ORDER_DELIVERY = 493;

	public static final Integer AFTERSALE_ORDER_SOURCE_FRONT = 2;

	public static final String APPLY_AUDIT_NAME = "申请人";

	public static Map<String,String> ORDER_ADDITIONAL_CLAUSES_MAP = new HashMap<String,String>();
	static{
		ORDER_ADDITIONAL_CLAUSES_MAP.put("101", "甲方承诺本次采购的AED仅限甲方企业内部接受过心肺复苏和自动体外除额器使用培训合格的人员使用，绝不涉及对外销售、转赠、交换等经营行为，AED的安装地址为：${installationAddress} 。");
		ORDER_ADDITIONAL_CLAUSES_MAP.put("102", "甲方承诺购买本合同产品后销往客户${traderName}，客户${traderName}不用于医疗用途，乙方将按照甲方提供的收货信息送货，甲方为提供的收货信息真实性负责。");
		ORDER_ADDITIONAL_CLAUSES_MAP.put("103", "${prodName103}产品为非医疗器械，不按照《医疗器械监督管理条例》进行管理。甲方承诺购买本合同产品后不得销往医疗机构，不得用于医疗用途。");
		ORDER_ADDITIONAL_CLAUSES_MAP.put("104", "甲方承诺本合同产品只限甲方企业内部使用，绝不对外销售，不用于医疗用途。");
		ORDER_ADDITIONAL_CLAUSES_MAP.put("105", "${prodName105}产品为非医疗器械，不按照《医疗器械监督管理条例》进行管理。甲方承诺本合同产品只限甲方企业内部使用，绝不对外销售，不用于医疗用途。");
		ORDER_ADDITIONAL_CLAUSES_MAP.put("106", "甲方承诺，所购以上产品，仅限于出口，不在中华人民共和国境内使用。");
		ORDER_ADDITIONAL_CLAUSES_MAP.put("201", "乙方为终端用户提供最优质的售后服务，凡是在乙方购买的AED在设备正常使用过程中发生丢失并可提供公安机关出示的丢失证明材料及购买发票的用户，乙方可免费提供一台AED进行补偿。");
		ORDER_ADDITIONAL_CLAUSES_MAP.put("202", "凡是在乙方购买的AED在设备正常使用过程中发生施救并可提供终端的施救证明的甲方客户，乙方可免费提供一副全新AED电极片进行补偿（每台AED可享1次此补偿机会）。");
		ORDER_ADDITIONAL_CLAUSES_MAP.put("301", "甲方承诺遵守以下约定：本合同内采购的五分类血球试剂只销售至${saleProvinceName}${saleCityName}${terminalTraderName}，且只使用在序列号为${snCode}迈瑞BC-5000血球仪上；若甲方违反本约定，自愿向乙方缴纳不低于5000元/次违约金。");
		ORDER_ADDITIONAL_CLAUSES_MAP.put("302", "①合同签署后，甲方应同步向乙方预付试剂款${prepaidReagentAmount}元。后续甲方向乙方采购试剂时，双方另行签署试剂合同确认具体采购试剂的型号及数量。在上述甲方预付试剂款金额内，甲方无需向乙方支付试剂款。②甲方承诺在本合同生效之日起12个月内，向乙方采购并消耗完毕本条第1款约定预付试剂款；甲方如在前述期限内未消耗完试剂款的，剩余未消耗试剂款作为甲方违约金，乙方不予退还。");
		ORDER_ADDITIONAL_CLAUSES_MAP.put("401", "${selfAdditionalClause}");
	}

}
