package com.vedeng.erp.finance.dto;

import com.vedeng.erp.finance.enums.CheckChainEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 发票校验请求Dto
 */
@Data
public class InvoiceCheckRequestDto {

    /**
     * INVOICE_APPLY_SALES 销售发票申请
     * INVOICE_APPLY_AFTER 售后发票申请
     * INVOICE_OPEN_SALES 销售发票开票
     * INVOICE_OPEN_AFTER 售后发票开票
     */
    private CheckChainEnum checkChainEnum;

    /**
     * 发票申请id（申请校验无）
     */
    private Integer invoiceApplyId;

    /**
     * 1纸质发票 2电子发票 3数电发票
     */
    private Integer invoiceProperty;

    /**
     * 503采购开票
     * 504售后开票
     * 505销售开票
     */
    private Integer type;

    /**
     * 关联表主键ID
     */
    private Integer relatedId;

    /**
     * 开票信息类型(0-标准开票信息,1-自定义开票信息)
     */
    private Integer invoiceInfoType;

    /**
     * 开票留言
     */
    private String invoiceMessage;

    /**
     * 发票校验请求明细
     */
    private List<InvoiceCheckRequestDetailDto> detailList;

    @Data
    public static class InvoiceCheckRequestDetailDto {

        /**
         * 发票申请明细id（申请校验无）
         */
        private Integer invoiceApplyDetailId;

        /**
         * 发票申请明细关联的商品主键ID
         */
        private Integer detailGoodsId;

        /**
         * 开票单价
         */
        private BigDecimal price;

        /**
         * 开票数量
         */
        private BigDecimal num;

        /**
         * 开票总额
         */
        private BigDecimal totalAmount;

    }

}
