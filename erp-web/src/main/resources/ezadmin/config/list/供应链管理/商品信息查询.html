<!doctype html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>商品搜索</title>
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
 </head>
 <body id="x2F8d7ZtUys" datasource="erp-reportdatasource" fixednumber="2" append_column_url="" append_row_url="" empty_show="" firstcol="numbers">
  <div id="appendHead"></div>
  <div class="layui-fluid">
   <div class="layui-tab">
    <ul class="layui-tab-title" id="tab">
    </ul>
   </div>
   <form class="layui-form" id="search">
       <div class=" layui-inline "><label class="layui-form-label">合并搜索</label>
           <div class="layui-input-inline">
               <object class=" layui-input list-search-item " type="union" name="SKU_NAME,MATERIAL_CODE,SKU_NO,SKU_ID,MODEL,SPEC,REGISTRATION_NUMBER,CATENAME" placeholder="空格或%代替任意多个字符" style="" alias="" jdbctype="VARCHAR" data="" datatype="" oper=""></object>
           </div>
       </div>
       <div class=" layui-inline ">
           <label class="layui-form-label">贝登分类</label>
           <div class="layui-input-inline"><object class=" layui-input list-search-item " type="search-category" name="CATEGORY_ID" placeholder="" style="" alias="B" jdbctype="NUMBER" data="" datatype="" oper="IN"></object>
           </div>
       </div>
       <div class=" layui-inline "><label class="layui-form-label">品牌</label>
           <div class="layui-input-inline">
               <object class=" layui-input list-search-item " type="20" name="BRAND_ID" placeholder="" style="" alias="B" jdbctype="NUMBER" data="SELECT BRAND_ID AS `K`,BRAND_NAME AS `V`  FROM T_BRAND WHERE COMPANY_ID=1 AND IS_DELETE=0" datatype="KVSQLCACHE" oper=""></object>
           </div>
       </div>
       <div class=" layui-inline ">
           <label class="layui-form-label">型号</label>
           <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="MODEL" placeholder="" style="" alias="A" jdbctype="VARCHAR" data="" datatype="" oper="LIKE"></object>
           </div>
       </div>
       <div class=" layui-inline "><label class="layui-form-label">商品名称</label>
           <div class="layui-input-inline">
               <object class=" layui-input list-search-item " type="hidden" name="SKU_NAME" placeholder="" style="" alias="A" jdbctype="VARCHAR" data="" datatype="" oper="LIKE"></object>
           </div>
       </div>

    <div class=" layui-inline ">
     <label class="layui-form-label">审核状态</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="21" name="CHECK_STATUS" placeholder="" style="" alias="A" jdbctype="" data="[{&quot;V&quot;:&quot;待完善&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;3&quot;}]" datatype="JSON" oper=""></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">商品档位</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="21" name="GOODS_POSITION_NO" placeholder="" style="" alias="A" jdbctype="NUMBER" data="[{&quot;V&quot;:&quot;无档位&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;独家产品&quot;,&quot;K&quot;:&quot;5&quot;},{&quot;V&quot;:&quot;代理产品&quot;,&quot;K&quot;:&quot;4&quot;},{&quot;V&quot;:&quot;经销产品&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;代销产品&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;未签约&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;低等级产品&quot;,&quot;K&quot;:&quot;6&quot;}]" datatype="JSON" oper="="></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">注册证号</label>
     <div class="layui-input-inline">
         <input class=" layui-input list-search-item " type="text" name="REGISTRATION_NUMBER" placeholder="" style="" alias="D" jdbctype="VARCHAR" data="" datatype="" oper="LIKE"></input>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">可用库存</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="21" name="AVAILABLE_STOCK" placeholder="" style="" alias="IF(A.AVAILABLE_STOCK_NUM > 0, 1, 0)" jdbctype="NUMBER" data="[{&quot;V&quot;: &quot;无&quot;,&quot;K&quot;: &quot;0&quot;},{&quot;V&quot;: &quot;有&quot;,&quot;K&quot;: &quot;1&quot;}]" datatype="JSON" oper="EQ"></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">是否核价</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="21" name="CHECK_PRICE" placeholder="" style="" alias="IF(A.TERMINAL_PRICE > 0, 1, 0)" jdbctype="NUMBER" data="[{&quot;V&quot;: &quot;未核价&quot;,&quot;K&quot;: &quot;0&quot;},{&quot;V&quot;: &quot;已核价&quot;,&quot;K&quot;: &quot;1&quot;}]" datatype="JSON" oper="EQ"></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">ID</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="hidden" name="SKU_ID" placeholder="" style="" alias="A" jdbctype="NUMBER" data="" datatype="" oper="="></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">订货号</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="hidden" name="SKU_NO" placeholder="请输入订货号" style="" alias="A" jdbctype="VARCHAR" data="" datatype="" oper="LIKE"></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">物料编号</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="MATERIAL_CODE" placeholder="" style="" alias="A" jdbctype="VARCHAR" data="" datatype="" oper=""></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">规格</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="SPEC" placeholder="" style="" alias="A" jdbctype="VARCHAR" data="" datatype="" oper="LIKE"></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">商品等级</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="21" name="GOODS_LEVEL_NO" placeholder="" style="" alias="A" jdbctype="NUMBER" data="select LEVEL_NAME AS `V`,ID AS `K` FROM V_GOODS_LEVEL" datatype="KVSQLCACHE" oper="="></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">资料描述</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="RICH_TEXT" placeholder="空格或%代替任意多个字符" style="" alias="concat_ws( spi.DOC_TEXT, BD.RICH_TEXT, CD.RICH_TEXT )" jdbctype="" data="" datatype="" oper="LIKE"></object>
     </div>
    </div>





   </form>
   <hr class="layui-border-blue">
   <div class="btn-group   bd-highlight" id="tableButton"> <button item_name="调价列表" name="调价列表" url="/price/skuPriceModifyRecord/index.do?fromMenuFlag=0" opentype="MODEL" windowname="调价列表" style type="table">调价列表</button>
   </div>
   <table id="table" class="layui-table" style=" width:100%">
    <thead>
     <tr id="column">
      <th type="rowbutton" id="rowbutton"></th>
      <th item_name="SKU" name="SKU" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">订货号</th>
      <th item_name="SKU_NAME" name="SKU_NAME" body="td-link" order="1" url="/goods/vgoods/viewSku.do?skuId=${SKU_ID}&amp;pageType=1&amp;from=ez&amp;p=${currentPage}" opentype="PARENT" windowname="" datatype="" data="" style="text-align:left" head="18">商品名称</th>
      <th item_name="BRAND_NAME" name="BRAND_NAME" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">品牌名称</th>
      <th item_name="SPEC" name="SPEC" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">规格</th>
      <th item_name="MODEL" name="MODEL" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">型号</th>
      <th item_name="UNIT_NAME" name="UNIT_NAME" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">单位</th>
      <th item_name="TERMINAL_PRICE" name="TERMINAL_PRICE" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">终端价</th>
      <th item_name="DISTRIBUTION_PRICE" name="DISTRIBUTION_PRICE" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">经销价</th>
      <th item_name="ELECTRONIC_COMMERCE_PRICE" name="ELECTRONIC_COMMERCE_PRICE" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">电商价</th>
      <th item_name="RESEARCH_TERMINAL_PRICE" name="RESEARCH_TERMINAL_PRICE" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">科研终端价</th>
      <th item_name="AVAILABLE_STOCK_NUM" name="AVAILABLE_STOCK_NUM" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">可用库存</th>
      <th item_name="ONWAYNUM" name="ONWAYNUM" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">在途库存</th>
      <th item_name="STOCK_NUM" name="STOCK_NUM" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">库存量</th>
      <th item_name="DIRECT_DELIVERY_TIME" name="DIRECT_DELIVERY_TIME" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">直发货期（天）</th>
      <th item_name="COMMON_DELIVERY_TIME" name="COMMON_DELIVERY_TIME" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">普发货期（天）</th>
      <th item_name="PURCHASE_TIME" name="PURCHASE_TIME" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">采购到货工作日</th>
      <th item_name="ALL_LOCK_NUM" name="ALL_LOCK_NUM" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">占用/活动锁定</th>
      <th item_name="SALE_NUM" name="SALE_NUM" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">近一年销量</th>
      <th item_name="AVGPRICE" name="AVGPRICE" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">最近一年单价</th>
      <th item_name="ASSNAME" name="ASSNAME" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">产品归属</th>
      <th item_name="USERNAME" name="USERNAME" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">最近成单销售</th>
      <th item_name="REGISTRATION_NUMBER" name="REGISTRATION_NUMBER" body="td-link" order="1" url="/firstengage/baseinfo/getFirstSearchDetail.do?firstEngageId=${FIRST_ENGAGE_ID}" opentype="PARENT" windowname="" datatype="" data="" style="" head="18">注册证号</th>
      <th item_name="GOODS_LEVEL_NO" name="GOODS_LEVEL_NO" body="td-select" order="1" url="" opentype="MODEL" windowname="" datatype="KVSQLCACHE" data="select LEVEL_NAME AS `V`,ID AS `K` FROM V_GOODS_LEVEL" style="" head="18">商品等级</th>
      <th item_name="GOODS_POSITION_NO" name="GOODS_POSITION_NO" body="td-select" order="1" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;无档位&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;独家产品&quot;,&quot;K&quot;:&quot;5&quot;},{&quot;V&quot;:&quot;代理产品&quot;,&quot;K&quot;:&quot;4&quot;},{&quot;V&quot;:&quot;经销产品&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;代销产品&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;未签约&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;低等级产品&quot;,&quot;K&quot;:&quot;6&quot;}]" style="" head="18">商品档位</th>
      <th item_name="SPU_TYPE" name="SPU_TYPE" body="td-select" order="1" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;设备&quot;,&quot;K&quot;:316},{&quot;V&quot;:&quot;耗材&quot;,&quot;K&quot;:317},{&quot;V&quot;:&quot;试剂&quot;,&quot;K&quot;:318},{&quot;V&quot;:&quot;配件&quot;,&quot;K&quot;:1008}]" style="" head="18">商品类型</th>
      <th item_name="CHECK_STATUS" name="CHECK_STATUS" body="td-select" order="1" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;待完善&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;3&quot;}]" style="" head="18">审核状态</th>
      <th item_name="CATENAME" name="CATENAME" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">贝登分类</th>
      <th item_name="TAX_CATEGORY_NO" name="TAX_CATEGORY_NO" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">税收编码</th>
      <th item_name="CLASSIFICATION_ABBREVIATION" name="CLASSIFICATION_ABBREVIATION" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">商品和服务分类简称</th>
     </tr>
    </thead>
    <tbody>
     <tr>
      <td colspan="100"> express: <pre id="express" class="layui-code" orderby=" ORDER BY ( CASE   WHEN A.SPU_TYPE IS NULL OR A.SPU_TYPE = 0 THEN 999 ELSE A.SPU_TYPE END )  ASC, A.ONE_YEAR_SALE_NUM DESC, A.AVAILABLE_STOCK_NUM DESC " groupby="">StringBuilder sql=new StringBuilder(" SELECT ");

if(isNotBlank("RICH_TEXT")){
	sql.append(" concat_ws( spi.DOC_TEXT, BD.RICH_TEXT, CD.RICH_TEXT )  RICH_TEXT ,");



}


sql.append("  A.SPEC,concat(A.ORDER_OCCUPY_NUM,'/',A.ACTION_LOCK_NUM) ALL_LOCK_NUM,
       A.MODEL,
       A.MATERIAL_CODE,
       A.SKU_ID                                                                                     ID,
       A.SKU_ID                                                                                     SKU_ID,
       A.SPU_ID,
       B.SPU_TYPE ,
       A.TERMINAL_PRICE,
       A.DISTRIBUTION_PRICE,
       IF(A.TERMINAL_PRICE &gt; 0, 1, 0)                                                               CHECK_PRICE,
       A.SKU_NO                                                                                     SKU,
       A.ONE_YEAR_SALE_NUM                 as                                                       SALE_NUM,
       A.AVAILABLE_STOCK_NUM,
       A.STOCK_NUM,
       IF(AVAILABLE_STOCK_NUM &gt; 0, 1, 0)                                                            AVAILABLE_STOCK,
 
       C.FIRST_ENGAGE_ID,
       A.SKU_NAME,
       E.BRAND_NAME,
       D.REGISTRATION_NUMBER,
       TT.ONWAYNUM,
       IFNULL(A.AVGPRICE, '-')             AS                                                       'AVGPRICE',
       CONCAT(F.USERNAME, '&amp;', G.USERNAME) AS                                                       'ASSNAME',
       SALE.USERNAME,
       A.CHECK_STATUS * 1                                                                           CHECK_STATUS,
       F.USERNAME                          AS                                                       'ASSMA',
       G.USERNAME                          AS                                                       'ASS',
       CB.BASE_CATEGORY_ID                                                                          'C3',
       DB.BASE_CATEGORY_ID                                                                          'C2',
       EB.BASE_CATEGORY_ID                                                                          'C1',
       CONCAT(EB.BASE_CATEGORY_NAME, '/', DB.BASE_CATEGORY_NAME, '/', CB.BASE_CATEGORY_NAME)        CATENAME,
       UU.UNIT_NAME,
       TCC.FINAL_CODE AS TAX_CATEGORY_NO,
       TCC.CLASSIFICATION_ABBREVIATION,
       B.BRAND_ID,
       A.GOODS_LEVEL_NO,
       A.GOODS_POSITION_NO,
        A.PURCHASE_TIME,
IF (TSDD.DIRECT_DELIVERY_TIME_START = TSDD.DIRECT_DELIVERY_TIME_END,TSDD.DIRECT_DELIVERY_TIME_START,CONCAT(TSDD.DIRECT_DELIVERY_TIME_START, '-', TSDD.DIRECT_DELIVERY_TIME_END)) AS DIRECT_DELIVERY_TIME,
IF (TSDD.COMMON_DELIVERY_TIME_START = TSDD.COMMON_DELIVERY_TIME_END,TSDD.COMMON_DELIVERY_TIME_START,CONCAT(TSDD.COMMON_DELIVERY_TIME_START, '-', TSDD.COMMON_DELIVERY_TIME_END))  AS COMMON_DELIVERY_TIME
FROM V_CORE_SKU A
          LEFT JOIN T_TAXCODE_CLASSIFICATION TCC ON RPAD(A.TAX_CATEGORY_NO, 19, '0') = TCC.FINAL_CODE
            JOIN V_CORE_SPU B ON A.SPU_ID = B.SPU_ID
           LEFT JOIN T_SKU_DELIVERY_DATA TSDD ON TSDD.SKU_ID = A.SKU_ID AND TSDD.IS_DELETE = 0
         LEFT JOIN T_FIRST_ENGAGE C ON C.FIRST_ENGAGE_ID = B.FIRST_ENGAGE_ID
         LEFT JOIN T_REGISTRATION_NUMBER D ON C.REGISTRATION_NUMBER_ID = D.REGISTRATION_NUMBER_ID
         LEFT JOIN T_BRAND E ON B.BRAND_ID = E.BRAND_ID
         LEFT JOIN T_USER F ON B.ASSIGNMENT_MANAGER_ID = F.USER_ID
         LEFT JOIN T_USER G ON B.ASSIGNMENT_ASSISTANT_ID = G.USER_ID
         LEFT JOIN V_BASE_CATEGORY CB ON B.CATEGORY_ID = CB.BASE_CATEGORY_ID
         LEFT JOIN V_BASE_CATEGORY DB ON CB.PARENT_ID = DB.BASE_CATEGORY_ID
         LEFT JOIN V_BASE_CATEGORY EB ON DB.PARENT_ID = EB.BASE_CATEGORY_ID
         LEFT JOIN T_USER SALE ON SALE.USER_ID = A.LATEST_VALID_ORDER_USER
         LEFT JOIN T_UNIT UU ON UU.UNIT_ID = A.BASE_UNIT_ID
         LEFT JOIN (SELECT SUM(T.ONWAYNUM) AS 'ONWAYNUM',
                           T.GOODS_ID
                    FROM (
                             SELECT a.GOODS_ID, 
                                    COALESCE(
                                            SUM((
                                                a.NUM - IFNULL(a.ARRIVAL_NUM, 0) - IFNULL(a.AFTER_RETURN_NUM , 0))),
                                            0
                                        ) ONWAYNUM,
                                    b.BUYORDER_NO,
                                    a.ESTIMATE_ARRIVAL_TIME,
                                    b.VALID_TIME,
                                    b.BUYORDER_ID
                             FROM T_BUYORDER_GOODS a
                                      LEFT JOIN T_BUYORDER b ON a.BUYORDER_ID = b.BUYORDER_ID
                                      
                             WHERE b.VALID_STATUS = 1
                               AND b.PAYMENT_STATUS IN (1, 2)
                               AND b.DELIVERY_DIRECT = 0
                               AND b.ARRIVAL_STATUS IN (0, 1)
                               AND b.STATUS != 3  and (a.NUM - IFNULL(a.ARRIVAL_NUM, 0) - IFNULL(a.AFTER_RETURN_NUM, 0))&gt;0
                             GROUP BY a.BUYORDER_GOODS_ID
                         ) T   WHERE T.ONWAYNUM &gt; 0  GROUP BY T.GOODS_ID) TT ON TT.GOODS_ID = A.SKU_ID ");

if(isNotBlank("RICH_TEXT")){
	
	sql.append("	left join T_DOC_OF_GOODS spi on   A.SKU_ID=spi.SKU_ID  
				left join T_DOC_GOODS_BRAND_DETAIL BD ON BD.BRAND_ID = B.BRAND_ID
				left join T_DOC_GOODS_THIRD_CATEGORY_DETAIL CD ON CD.THIRD_CATEGORY_ID = B.CATEGORY_ID ");
	 
	
}


 sql.append("  where A.STATUS = 1  AND B.STATUS = 1   " );
list=search(sql);

return list;</pre> count: <pre id="count" class="layui-code">SELECT count(1)
from V_CORE_SKU A
         
          JOIN V_CORE_SPU B ON A.SPU_ID = B.SPU_ID    
         LEFT JOIN T_FIRST_ENGAGE C ON C.FIRST_ENGAGE_ID = B.FIRST_ENGAGE_ID
         LEFT JOIN T_REGISTRATION_NUMBER D ON C.REGISTRATION_NUMBER_ID = D.REGISTRATION_NUMBER_ID
 LEFT JOIN V_BASE_CATEGORY CB ON B.CATEGORY_ID = CB.BASE_CATEGORY_ID
         LEFT JOIN V_BASE_CATEGORY DB ON CB.PARENT_ID = DB.BASE_CATEGORY_ID
         LEFT JOIN V_BASE_CATEGORY EB ON DB.PARENT_ID = EB.BASE_CATEGORY_ID
 left join T_DOC_OF_GOODS spi on   A.SKU_ID=spi.SKU_ID  
				left join T_DOC_GOODS_BRAND_DETAIL BD ON BD.BRAND_ID = B.BRAND_ID
				left join T_DOC_GOODS_THIRD_CATEGORY_DETAIL CD ON CD.THIRD_CATEGORY_ID = B.CATEGORY_ID
where A.STATUS = 1 AND B.STATUS = 1</pre></td>
     </tr>
    </tbody>
   </table>
  </div>
  <div id="appendFoot">
   <script type="text/javascript">
function read(isopen){
	//前往按钮的回调
	$.ajax({
		url:'/price/skuPriceModifyRecord/readPriceChangeRecord.do',
		type:"POST",
		dataType : "json",
		success:function(data)
		{         $.Deferred().resolve(data);
			if(data.code == 0 && isopen == 1){
				window.location.href = '/price/skuPriceModifyRecord/index.do?fromMenuFlag=0';
			}
		}
	});
}


	$.ajax({
		url:'/price/skuPriceModifyRecord/isNeedNotify.do',
		type:"POST",
		dataType : "json",
		success:function(data){
                        $.Deferred().resolve(data)
			if(data.code == 1){
				index = layer.confirm('商品价格发生变动，请前往查看', {
					btn: ['前往', '关闭']
					,btn2: function(index, layero){
						read(0);
					}
				}, function(index, layero){
					//前往按钮的回调
					read(1);
				});
			}
		}
	})


$(function(){
    var skuIds = [];
    $('td[item_name="SKU"]').each(function(){
        skuIds.push($(this).attr('title').replace('V', ''));
    });

    if (skuIds.length > 0) {
        $.ajax({
            url: '/goodsInfo/price/skuIdList.do',
            data: JSON.stringify(skuIds),
            type: 'post',
            contentType: 'application/json',
            success: function (res) {
                var s = $.parseJSON(res);
                if(s.code === 0){
                    var resData = s.data;
                    for(var item in resData){
                        var $parent = $('td[title="V' + item + '"]').parents('tr:first');
                        if (resData[item].electronicCommercePrice) {
                            $parent.find('td[item_name="ELECTRONIC_COMMERCE_PRICE"] .layui-elip').html(Number(resData[item].electronicCommercePrice).toFixed(2));
                        }
                        if (resData[item].researchTerminalPrice) {
                            $parent.find('td[item_name="RESEARCH_TERMINAL_PRICE"] .layui-elip').html(Number(resData[item].researchTerminalPrice).toFixed(2));
                        }
                    }
                }
            }
        })
    }

});
</script>
  </div>
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
  <script>
    layui.use(function () {

    })
</script>
 </body>
</html>