<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.system.dao.MessageTemplateMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.system.model.MessageTemplate" >
    <id column="MESSAGE_TEMPLATE_ID" property="messageTemplateId" jdbcType="INTEGER" />
    <result column="TYPE" property="type" jdbcType="BIT" />
    <result column="PARAMS" property="params" jdbcType="VARCHAR" />
    <result column="TITLE" property="title" jdbcType="VARCHAR" />
    <result column="IS_ENABLE" property="isEnable" jdbcType="BIT" />
    <result column="CONTENT" property="content" jdbcType="VARCHAR" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    MESSAGE_TEMPLATE_ID, TYPE, PARAMS, TITLE, CONTENT, ADD_TIME, CREATOR, MOD_TIME, UPDATER
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from T_MESSAGE_TEMPLATE
    where MESSAGE_TEMPLATE_ID = #{messageTemplateId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from T_MESSAGE_TEMPLATE
    where MESSAGE_TEMPLATE_ID = #{messageTemplateId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.system.model.MessageTemplate" >
    insert into T_MESSAGE_TEMPLATE (MESSAGE_TEMPLATE_ID, TYPE, PARAMS, 
      TITLE, CONTENT, ADD_TIME, 
      CREATOR, MOD_TIME, UPDATER
      )
    values (#{messageTemplateId,jdbcType=INTEGER}, #{type,jdbcType=BIT}, #{params,jdbcType=VARCHAR}, 
      #{title,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR}, #{addTime,jdbcType=BIGINT}, 
      #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.system.model.MessageTemplate" >
    insert into T_MESSAGE_TEMPLATE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="messageTemplateId != null" >
        MESSAGE_TEMPLATE_ID,
      </if>
      <if test="type != null" >
        TYPE,
      </if>
      <if test="params != null" >
        PARAMS,
      </if>
      <if test="title != null" >
        TITLE,
      </if>
      <if test="content != null" >
        CONTENT,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
       <if test="isEnable != null" >
        IS_ENABLE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="messageTemplateId != null" >
        #{messageTemplateId,jdbcType=INTEGER},
      </if>
      <if test="type != null" >
        #{type,jdbcType=BIT},
      </if>
      <if test="params != null" >
        #{params,jdbcType=VARCHAR},
      </if>
      <if test="title != null" >
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="content != null" >
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
       <if test="isEnable != null" >
        #{isEnable,jdbcType=BIT},
      </if>
    </trim>
     <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="messageTemplateId">
		SELECT LAST_INSERT_ID() AS messageTemplateId
    </selectKey>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.system.model.MessageTemplate" >
    update T_MESSAGE_TEMPLATE
    <set >
      <if test="type != null" >
        TYPE = #{type,jdbcType=BIT},
      </if>
      <if test="params != null" >
        PARAMS = #{params,jdbcType=VARCHAR},
      </if>
      <if test="title != null" >
        TITLE = #{title,jdbcType=VARCHAR},
      </if>
      <if test="content != null" >
        CONTENT = #{content,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
       <if test="isEnable != null" >
        IS_ENABLE = #{isEnable,jdbcType=BIT},
      </if>
    </set>
    where MESSAGE_TEMPLATE_ID = #{messageTemplateId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.system.model.MessageTemplate" >
    update T_MESSAGE_TEMPLATE
    set TYPE = #{type,jdbcType=BIT},
      PARAMS = #{params,jdbcType=VARCHAR},
      TITLE = #{title,jdbcType=VARCHAR},
      CONTENT = #{content,jdbcType=VARCHAR},
      IS_ENABLE = #{isEnable,jdbcType=BIT},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER}
    where MESSAGE_TEMPLATE_ID = #{messageTemplateId,jdbcType=INTEGER}
  </update>
  <!-- 分页查询消息模板列表 -->
   <select id="querylistPage" resultMap="BaseResultMap" parameterType="com.vedeng.system.model.MessageTemplate" >
    select 
    <include refid="Base_Column_List" />
    from T_MESSAGE_TEMPLATE
    where IS_ENABLE =1 
    <if test="messageTemplate.title != null and messageTemplate.title != ''" >
        and TITLE like CONCAT('%',#{messageTemplate.title},'%' ) 
    </if>
    <if test="messageTemplate.type != null and messageTemplate.type !=-1" >
        and TYPE = #{messageTemplate.type,jdbcType=BIT}
    </if>
  </select>
</mapper>