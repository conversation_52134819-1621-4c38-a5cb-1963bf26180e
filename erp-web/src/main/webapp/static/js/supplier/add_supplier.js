$(function() {
	//品牌信息
	initBrand();
    clickOne("twoMedicalType");
    clickOne("threeMedicalType");
    $("#file_1").change(function () {
        checkLogin();
        $("#uri_1").val($("#file_1").val());
    })

    $("#file_2").change(function () {
        checkLogin();
        $("#uri_2").val($("#file_2").val());
    })

    $("#file_3").change(function () {
        checkLogin();
        $("#uri_3").val($("#file_3").val());
    })

    $("#file_4").change(function () {
        checkLogin();
        $("#uri_4").val($("#file_4").val());
    })
    $("#file_5").change(function () {
        checkLogin();
        $("#uri_5").val($("#file_5").val());
    })

    $("#file_8").change(function () {
        checkLogin();
        $("#uri_8").val($("#file_8").val());
    })

    $(".addtags").hide();
    $("#one").click(function () {
        checkLogin();
        $("#name_2").attr("disabled", true);
        $("#tax label").addClass("bg-opcity");
        $("#tax .Wdate").addClass("bg-opcity");
        $("#tax .Wdate").css("cursor", "not-allowed");
        $("#taxUpload").css("cursor", "not-allowed");
        $("#taxUpload").prop("onclick", null).off("click");
        $("#name_3").attr("disabled", true);
        $("#org label").addClass("bg-opcity");
        $("#org .Wdate").addClass("bg-opcity");
        $("#org .Wdate").css("cursor", "not-allowed");
        $("#orgUpload").css("cursor", "not-allowed");
        $("#orgUpload").prop("onclick", null).off("click");
    })
    $("#zero").click(function () {
        checkLogin();
        $("#name_2").removeAttr("disabled");
        $("#tax label").removeClass("bg-opcity");
        $("#tax .Wdate").removeClass("bg-opcity");
        $("#tax .Wdate").css("cursor", "");
        $("#tax .Wdate").removeAttr("disabled");
        $("#taxUpload").css("cursor", "pointer");
        $('#taxUpload').attr("onclick", "file_2.click()");
        $("#name_3").removeAttr("disabled");
        $("#org label").removeClass("bg-opcity");
        $("#org .Wdate").removeClass("bg-opcity");
        $("#org .Wdate").removeAttr("disabled");
        $("#org .Wdate").css("cursor", "");
        $("#orgUpload").css("cursor", "pointer");
        $("#orgUpload").attr("onclick", "file_3.click()");

    })

    $("#med1").click(function () {
        checkLogin();
        $("#three_medical input").attr("disabled", true);
        $("#three_medical label").addClass("bg-opcity");
        $("#three_medical .Wdate").addClass("bg-opcity");
        $("#three_medical .Wdate").css("cursor", "not-allowed");
        $("#threeUpload").css("cursor", "not-allowed");
        $("#threeUpload").prop("onclick", null).off("click");
    })
    $("#med0").click(function () {
        checkLogin();
        $("#three_medical input").removeAttr("disabled");
        $("#three_medical label").removeClass("bg-opcity");
        $("#three_medical .Wdate").removeClass("bg-opcity");
        $("#three_medical .Wdate").removeAttr("disabled");
        $("#three_medical .Wdate").css("cursor", "");
        $("#threeUpload").css("cursor", "pointer");
        $("#threeUpload").attr("onclick", "file_5.click()");
    })
    var canSubmit=true;
	$("#supplierForm").submit(function(){
		checkLogin();
		$(".warning").remove();
		$("select").removeClass("errorbor");
		var sb =true;
		var traderName = $("input[name='traderName']").val();
		var traderNameReg = /^[a-zA-Z0-9\u4e00-\u9fa5\.\(\)\,\（\）\s]{2,128}$/;
		if(traderName == ""){
			warnTips("traderName","供应商名称不允许为空");
			return false;
		}
		if(traderName.length < 2 || traderName.length > 128){
			warnTips("traderName","供应商名称长度为2-128个字符长度");
			return false;
		}
		if(!traderNameReg.test(traderName)){
			warnTips("traderName","供应商名称不允许使用特殊字符");
			return false;
		}
		
		//是否存在
		$.ajax({
			type : "POST",
			url : page_url+"/trader/customer/gettraderbytradername.do",
			data : {traderName:traderName,'traderType':2},
			dataType : 'json',
			async : false,
			success : function(data) {
				if(data.code == 0){
					//已经存在
					if(data.data.traderSupplier !=null && data.data.traderSupplier.traderSupplierId != undefined && data.data.traderSupplier.traderSupplierId > 0){
						sb = false;
						return false;
					}
				}
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
		
		if(!sb){
			warnTips("traderName","供应商名称不允许重复");
			return false;
		}
		
		if($("select[name='province']").val() == 0){
			$("select[name='province']").addClass("errorbor");
			$("#zone").siblings('.warning').remove();
			$("#zone").after('<div class="warning">省份不允许为空</div>');
			return false;
		}
		if($("select[name='city']").val() == 0){
			$("select[name='city']").addClass("errorbor");
			$("#zone").siblings('.warning').remove();
			$("#zone").after('<div class="warning">地市不允许为空</div>');
			return false;
		}
		if($("select[name='zone']").val() == 0 && $("select[name='zone'] option").length > 1){
			$("select[name='zone']").addClass("errorbor");
			$("#zone").siblings('.warning').remove();
			$("#zone").after('<div class="warning">区县不允许为空</div>');
			return false;
		}

		if($("select[name='warehouseCity']").val() == 0){
			$("select[name='warehouseCity']").addClass("errorbor");
			$("#warehouseAreaId").siblings('.warning').remove();
			$("#warehouseAreaId").after('<div class="warning">地市不允许为空</div>');
			return false;
		}
		if($("select[name='warehouseAreaId']").val() == 0 && $("select[name='warehouseAreaId'] option").length > 1){
			$("select[name='warehouseAreaId']").addClass("errorbor");
			$("#warehouseAreaId").siblings('.warning').remove();
			$("#warehouseAreaId").after('<div class="warning">区县不允许为空</div>');
			return false;
		}

		var warehouseAddress = $('#warehouseAddress').val();
		if (warehouseAddress == null || warehouseAddress == undefined || warehouseAddress.length == 0){
			$("#warehouseAddress").addClass("errorbor");
			$("#warehouseAddress").siblings('.warning').remove();
			$("#warehouseAddress").after('<div class="warning">库房详细地址不允许为空</div>');
			return false;
		}else if (warehouseAddress.length > 100){
			$("#warehouseAddress").addClass("errorbor");
			$("#warehouseAddress").siblings('.warning').remove();
			$("#warehouseAddress").after('<div class="warning">库房详细地址输入不可超过100字符</div>');
			return false;
		}

		var traderType = $('#traderType').val();
		if (traderType == 0){
			$("#traderType").addClass("errorbor");
			$("#traderType").siblings('.warning').remove();
			$("#traderType").after('<div class="warning">供应商类型不允许为空</div>');
			return false;
		}




		var re = /^(\d{3,4}-?)?\d{7,9}(-?\d{2,6})?$|^$/;
		var hotTelephone = $("#hotTelephone").val();
		/*if(hotTelephone == ''){
			warnErrorTips("hotTelephone","hotTelephoneError","热线电话不允许为空");
			return false;
		}*/
		if(!re.test(hotTelephone)){
			warnErrorTips("hotTelephone","hotTelephoneError","热线电话只能包含数字或-");
			return false;
		}
		if(hotTelephone.length > 32){
			warnErrorTips("hotTelephone","hotTelephoneError","热线电话长度不允许超过32位");
			return false;
		}

		var serviceTelephone = $("#serviceTelephone").val();
		if(serviceTelephone == ''){
			warnErrorTips("serviceTelephone","serviceTelephoneError","售后总对接人电话不允许为空");
			return false;
		}

		var afterSaleManager = $("#afterSaleManager").val();
		if (afterSaleManager == '') {
			warnErrorTips("afterSaleManager", "serviceTelephoneError", "售后总对接人名称不允许为空");
			return false;
		}

		if(!re.test(serviceTelephone)){
			warnErrorTips("serviceTelephone","serviceTelephoneError","售后总对接人电话只能包含数字或-");
			return false;
		}
		if(serviceTelephone.length > 32){
			warnErrorTips("serviceTelephone","serviceTelephoneError","售后总对接人电话长度不允许超过32位");
			return false;
		}

		var installServiceContactName = $("#installServiceContactName").val();
		if (installServiceContactName == '') {
			warnErrorTips("installServiceContactName", "installServiceContactNameError", "安装服务名称不允许为空");
			return false;
		}

		var installServiceContactWay = $("#installServiceContactWay").val();
		if (!re.test(installServiceContactWay)) {
			warnErrorTips("installServiceContactWay", "installServiceContactNameError", "安装服务联系电话只能包含数字或-");
			return false;
		}


		var technicalDirectContactWay = $("#technicalDirectContactWay").val();
		if (technicalDirectContactWay != '' && !re.test(technicalDirectContactWay)) {
			warnErrorTips("technicalDirectContactWay", "technicalDirectContactNameError", "技术支持联系电话只能包含数字或-");
			return false;
		}

		var maintenanceContactWay = $("#maintenanceContactWay").val();
		if (maintenanceContactWay != '' && !re.test(maintenanceContactWay)) {
			warnErrorTips("maintenanceContactWay", "maintenanceContactNameError", "维修服务联系电话只能包含数字或-");
			return false;
		}

		var exchangeContactWay = $("#exchangeContactWay").val();
		if (exchangeContactWay != '' && !re.test(exchangeContactWay)) {
			warnErrorTips("exchangeContactWay", "exchangeContactNameError", "退换货联系电话只能包含数字或-");
			return false;
		}

		var otherContactWay = $("#otherContactWay").val();
		if (otherContactWay != '' && !re.test(otherContactWay)) {
			warnErrorTips("otherContactWay", "otherContactNameError", "其他联系电话只能包含数字或-");
			return false;
		}

	/*	var threeInOne = $('input:radio[name="threeInOne"]:checked').val();
		if (threeInOne == undefined || threeInOne == null || threeInOne == '') {
			warnErrorTips("threeInOneError", "threeInOneError", "三证合一不允许为空");
			return false;
		}*/

		var taxPayerType = $('#taxPayerType').val();
		if (taxPayerType == 0){
			$("#taxPayerType").addClass("errorbor");
			$("#taxPayerType").siblings('.warning').remove();
			$("#taxPayerType").after('<div class="warning">请选择纳税人分类</div>');
			return false;
		}



		var busUri = $("#uri_1").val();
		if (busUri == undefined || busUri == null || busUri == '') {
			warnErrorTips("name_1","businessError","请上传营业执照");
			return false;
		}

	/*	var supplyProduct = $("#supplyProduct").val();
		if(supplyProduct.length > 1024){
			warnTips("supplyProduct","供应产品长度不允许超过1024个字符");
			return false;
		}
		var logisticsName = $("#logisticsName").val();
		if(logisticsName != '' && logisticsName.length > 256){
			warnErrorTips("logisticsName","logisticsNameError","承运商名称不允许超过256个字符");
			return false;
		}
		var website = $("#website").val();
		if(website != '' && website.length > 256){
			warnErrorTips("website","websiteError","官方网址不允许超过256个字符");
			return false;
		}*/
		
		var comments = $("#comments").val();
		 
		if(comments.length > 128){
			warnTips("comments","备注长度不允许超过128个字符");
			return false;
		}
		
	/*	var brief = $("#brief").val();
		if(brief.length > 512){
			warnTips("brief","简介长度不允许超过512个字符");
			return false;
		}*/

		var medicalQualification = $('input:radio[name="medicalQualification"]:checked').val();
		var threeInOne = $('input:radio[name="threeInOne"]:checked').val();
		/*if (threeInOne == 1) {
			if ($("#busStartTime").val() == '') {
				$("#busStartTime").addClass("errorbor");
				$("#busStartTime").parent('div').siblings("div").css("display", "");
				return false;
			} else {
				$("#busStartTime").removeClass("errorbor");
				$("#busStartTime").parent('div').siblings("div").css("display", "none");
			}

		} else {
			if ($("#busStartTime").val() == '' && ($("#name_1").val() != '' || $("#busEndTime").val() != '' || $("input[name='isMedical']").prop("checked"))) {
				$("#busStartTime").addClass("errorbor");
				$("#busStartTime").parent('div').siblings("div").css("display", "");
				return false;
			} else {
				$("#busStartTime").removeClass("errorbor");
				$("#busStartTime").parent('div').siblings("div").css("display", "none");
			}
			if ($("#taxStartTime").val() == '' && ($("#name_2").val() != '' || $("#taxEndTime").val() != '')) {
				$("#taxStartTime").addClass("errorbor");
				$("#taxStartTime").parent('div').siblings("div").css("display", "");
				return false;
			} else {
				$("#taxStartTime").removeClass("errorbor");
				$("#taxStartTime").parent('div').siblings("div").css("display", "none");
			}

			if ($("#orgaStartTime").val() == '' && ($("#name_3").val() != '' || $("#orgaEndTime").val() != '')) {
				$("#orgaStartTime").addClass("errorbor");
				$("#orgaStartTime").parent('div').siblings("div").css("display", "");
				return false;
			} else {
				$("#orgaStartTime").removeClass("errorbor");
				$("#orgaStartTime").parent('div').siblings("div").css("display", "none");
			}
		}*/
		/*var testReg = /^[0-9a-zA-Z]{1,128}$/;
		if (medicalQualification == 1) {
			if ($("#twoStartTime").val() == '') {
				$("#twoStartTime").addClass("errorbor");
				$("#twoStartTime").parent('div').siblings("div").css("display", "");
				return false;
			} else {
				$("#twoStartTime").removeClass("errorbor");
				$("#twoStartTime").parent('div').siblings("div").css("display", "none");
			}

			if ($("#twoStartTime").val() != '') {
				var flag = false;
				$.each($("input[name='twoMedicalType']"), function (i, n) {
					if ($(this).prop("checked")) {
						flag = true;
						return false;
					}
				});
				if (!flag) {
					layer.alert("请选择医疗器械二类备案凭证");
					return false;
				}
				var flag1 = false;
				$.each($("input[name='threeMedicalType']"), function (i, n) {
					if ($(this).prop("checked")) {
						flag1 = true;
						return false;
					}
				});
				if (!flag1) {
					layer.alert("请选择三类医疗资质");
					return false;
				}
			}
		} else {
			if($("#twoStartTime").val()!=''){
				var flag = false;
				$.each($("input[name='twoMedicalType']"),function(i,n){
					if($(this).prop("checked")){
						flag = true;
						return false;
					}
				});
				if(!flag){
					layer.alert("请选择医疗器械二类备案凭证");
					return false;
				}
			}
			var nameFlag = false;
			$("#name_4").parent().parent().parent().find("input[name='name_4']").each( function(){//循环判断后添加的输入框其中是否有
				if($(this).val() != ''){
					nameFlag = true;
				}
			});

			if($("#twoStartTime").val()=='' && (nameFlag||$("#twoEndTime").val()!=''||$("#twoSn").val()!='')){
				$("#twoStartTime").addClass("errorbor");
				$("#twoStartTime").parent('div').siblings("div").css("display","");
				return false;
			}else{
				$("#twoStartTime").removeClass("errorbor");
				$("#twoStartTime").parent('div').siblings("div").css("display","none");
			}

			if($("#threeStartTime").val()==''&&($("#name_5").val()!=''||$("#threeEndTime").val()!=''||$("#threeSn").val()!='')){
				$("#name_5").removeClass("errorbor");
				$("#name_5").siblings("div").css("display","none");
				$("#threeStartTime").addClass("errorbor");
				$("#threeStartTime").parent('div').siblings("div").css("display","");
				return false;
			}else{
				$("#threeStartTime").removeClass("errorbor");
				$("#threeStartTime").parent('div').siblings("div").css("display","none");
			}
			if($("#threeStartTime").val()!=''){
				var flag = false;
				$.each($("input[name='threeMedicalType']"),function(i,n){
					if($(this).prop("checked")){
						flag = true;
						return false;
					}
				});
				if(!flag){
					layer.alert("请选择三类医疗资质");
					return false;
				}
			}
		}*/

        if ($("#busStartTime").val() == '') {
            $("#busStartTime").addClass("errorbor");
            $("#busStartTime").parent('div').siblings("div").css("display", "");
            return false;
        }

        if (medicalQualification == 1) {
            var isContain = 0;
            var twoUris = $( "[id^='uri_4']" );
            for (var index = 0; index < twoUris.length; index++) {
                var twoUri = twoUris[index].value;
                if (twoUri != undefined && twoUri != null && twoUri != ''){
                    isContain = 1;
                }
            }
            if (isContain == 0) {
                warnErrorTips("twoName","twoMedicalError","请上传多证合一辅助证明");
                return false;
            }
            if (isContain == 1 && $("#twoStartTime").val() == '') {
                $("#twoStartTime").addClass("errorbor");
                $("#twoStartTime").parent('div').siblings("div").css("display", "");
                return false;
            }
        }

		if($("#productStartTime").val()==''&&($("#name_6").val()!=''||$("#productEndTime").val()!=''||$("#productSn").val()!='')){
			$("#productStartTime").addClass("errorbor");
			$("#productStartTime").parent('div').siblings("div").css("display","");
			return false;
		}else{
			$("#productStartTime").removeClass("errorbor");
			$("#productStartTime").parent('div').siblings("div").css("display","none");
		}

		if($("#operateStartTime").val()==''&&($("#name_7").val()!=''||$("#operateEndTime").val()!=''||$("#operateSn").val()!='')){
			$("#operateStartTime").addClass("errorbor");
			$("#operateStartTime").parent('div').siblings("div").css("display","");
			return false;
		}else{
			$("#operateStartTime").removeClass("errorbor");
			$("#operateStartTime").parent('div').siblings("div").css("display","none");
		}
		if($("#name_9").val() != '' && $("#brandBookStartTime").val() == ''){
			$("#brandBookStartTime").addClass("errorbor");
			$("#brandBook_time_div").css("display","");
			$("#brandBook_time_div").text("开始时间不能为空");

			return false;
		}else{
			$("#brandBookStartTime").removeClass("errorbor");
			$("#brandBook_time_div").css("display","none");
		}

		if($("#name_9").val() == '' && $("#brandBookStartTime").val() != ''){
			$("#name_9").addClass("errorbor");
			$("#brand_book").css("display","");
			return false;
		}else{
			$("#name_9").removeClass("errorbor");
			$("#brand_book").css("display","none");
		}

		if(($("#name_9").val() != '' && $("#brandBookStartTime").val() != '' ) || ($("#name_9").val() == '' && $("#brandBookEndTime").val() == '')){
			$("#name_9").removeClass("errorbor");
			$("#brand_book").css("display","none");
		}else{
			$("#name_9").addClass("errorbor");
			$("#brand_book").css("display","");
			return false;
		}

		if($("#name_104").val() != '' && $("#qualityAssuranceStartTime").val() == '') {
			$("#qualityAssuranceStartTime").addClass("errorbor");
			$("#qualityAssuranc_time_div").css("display", "");
			return false;
		}else{
			$("#qualityAssuranceStartTime").removeClass("errorbor");
			$("#qualityAssuranc_time_div").css("display","none");
		}

		if($("#name_105").val() != '' && $("#afterSalesBookStartTime").val() == '') {
			$("#afterSalesBookStartTime").addClass("errorbor");
			$("#afterSalesBook_time_div").css("display", "");
			return false;
		}else{
			$("#afterSalesBookStartTime").removeClass("errorbor");
			$("#afterSalesBook_time_div").css("display","none");
		}
		var recordNo = $('#recordNo').val();
		if (recordNo != null && recordNo != undefined && recordNo != '' && recordNo.length > 100){
			warnTips("recordNo", "备案号长度不允许超过100个字符");
			return false
		}
		if(canSubmit){
		    canSubmit=false;
        }else{
		    return false;
        }
		return true;
	});
	
	$("input[name='traderName']").change(function(){
		checkLogin();
		$(".warning").remove();
		$("input[name='traderName']").removeClass("errorbor");
		if($(this).val() != ''){
			var traderNameReg = /^[a-zA-Z0-9\u4e00-\u9fa5\.\(\)\,\（\）\s]{2,128}$/;
			if($(this).val() == ""){
				warnTips("traderName","供应商名称不允许为空");
				return false;
			}else if($(this).val().length < 2 || $(this).val().length > 128){
				warnTips("traderName","供应商名称长度为2-128个字符长度");
				return false;
			}else if(!traderNameReg.test($(this).val())){
				warnTips("traderName","供应商名称不允许使用特殊字符");
				return false;
			}else{
				$.ajax({
					type : "POST",
					url : page_url+"/trader/customer/gettraderbytradername.do",
					data : {traderName:$(this).val(),'traderType':2},
					dataType : 'json',
					async : false,
					success : function(data) {
						if(data.code == 0){
							//已经存在
							if(data.data.traderSupplier !=null && data.data.traderSupplier.traderSupplierId != undefined && data.data.traderSupplier.traderSupplierId > 0){
								warnTips("traderName","供应商不允许重复");
								return false;
							}
							//已经是客户
							if(data.data.traderCustomer != null && data.data.traderCustomer.traderCustomerId != undefined && data.data.traderCustomer.traderCustomerId > 0){
								var regionIds = data.data.areaIds.split(",");
								if(regionIds[0] != undefined){
									$("select[name='province']").val(regionIds[0]);
									$.ajax({
										type : "POST",
										url : page_url+"/system/region/getregion.do",
										data :{'regionId':regionIds[0]},
										dataType : 'json',
										async : false,
										success : function(data) {
											$option = "<option value='0'>请选择</option>";
											$.each(data.listData,function(i,n){
												var selected = data.listData[i]['regionId'] == regionIds[1] ? "selected" : "";
												$option += "<option value='"+data.listData[i]['regionId']+"' "+selected+">"+data.listData[i]['regionName']+"</option>";
											});
											$("select[name='city'] option:gt(0)").remove();
											$("select[name='zone'] option:gt(0)").remove();
											$("select[name='city']").html($option);
											$("select[name='zone']").html("<option value='0'>请选择</option>");
										}
									});
								}
								if(regionIds[1] != undefined){
									$.ajax({
										type : "POST",
										url : page_url+"/system/region/getregion.do",
										data :{'regionId':regionIds[1]},
										dataType : 'json',
										async : false,
										success : function(data) {
											$option = "<option value='0'>请选择</option>";
											$.each(data.listData,function(i,n){
												var selected = data.listData[i]['regionId'] == regionIds[2] ? "selected" : "";
												$option += "<option value='"+data.listData[i]['regionId']+"' "+selected+">"+data.listData[i]['regionName']+"</option>";
											});
											$("select[name='zone'] option:gt(0)").remove();
											$("select[name='zone']").html($option);
										}
									});
								}
							}
						}
					},
					error:function(data){
						if(data.status ==1001){
							layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
						}
					}
				});
			}
		}
	});


    var editId = null;
	//新增地址
    $('.J-add-address').click(function () {
        editId = null;
        layer.open({
            shadeClose: false, //点击遮罩关闭
            //area: 'auto',
            area: [680, 600],
            title: 'test',
            btn: null,
            content: $('.J-add-address-tmpl').html(),
            success: function () {

            }
        });
    });

    var layerWarnTips = function(ele, txt){
        $(".J-form :input").parents('li').find('.warning').remove();
        $(".J-form :input").removeClass("errorbor");
        $(ele).siblings('.warning').remove();
        $(ele).after('<div class="warning">'+txt+'</div>');
        $(ele).focus();
        $(ele).addClass("errorbor");
        return false;
    };

    function layerdelWarnTips(ele){
        $(".J-form :input").parents('li').find('.warning').remove();
        $(".J-form :input").removeClass("errorbor");
        $(ele).siblings('.warning').remove();
    }

    var checkAddressList = function(){
        var listLen = $('.J-address-item').length;

        if(listLen){
            $('.J-no-address').hide();
        }else{
            $('.J-no-address').show();
        }

        setAddressData();
    };

    var setAddressData = function(){
        var dataArr = [];
        $('.J-address-item').each(function () {
            dataArr.push($(this).data('json'));
        });

        $('.J-address-data').val(JSON.stringify(dataArr));
    };

    checkAddressList();

    $(document).on('click', '.J-address-add', function () {
        if($(".J-province").val() == 0){
            $(".J-province").addClass("errorbor");
            $(".J-province").parent('div').siblings("div").find(".pr").css("display","");
            return false;
        }else{
            $(".J-province").removeClass("errorbor");
            $(".J-province").parent('div').siblings("div").find(".pr").css("display","none");
        }
        if($(".J-city").val() == 0){
            $(".J-city").addClass("errorbor");
            $(".J-city").parent('div').siblings("div").find(".ci").css("display","");
            return false;
        }else{
            $(".J-city").removeClass("errorbor");
            $(".J-city").parent('div').siblings("div").find(".ci").css("display","none");
        }
        if($(".J-zone").val()==0&&$(".J-zone")[0].options.length>1){
            $(".J-zone").addClass("errorbor");
            $(".J-zone").parent('div').siblings("div").find(".zo").css("display","");
            return false;
        }else{
            $(".J-zone").removeClass("errorbor");
            $(".J-zone").parent('div').siblings("div").find(".zo").css("display","none");
        }
        //var pattern = new RegExp("[`~!@$^&*=|{}':;'\\[\\].<>/?~！@￥……&*——|{}【】‘；：”“'。、？]");
        if($(".J-address").val()==''){
            layerWarnTips(".J-address","地址不允许为空");
            return  false;
        }else{
            layerdelWarnTips(".J-address");
        }
        if($(".J-address").val().length >128){
            layerWarnTips(".J-address","地址长度不能超过128个字符");
            return false;
        }else{
            layerdelWarnTips(".J-address");
        }
        var re= /^[0-9][0-9]{5}$/;
        if($(".J-zipCode").val()!='' && !re.test($(".J-zipCode").val())){
            layerWarnTips(".J-zipCode","邮编格式不正确");
            return  false;
        }else{
            layerdelWarnTips(".J-zipCode");
        }
        if($(".J-comments").val()!='' && $(".J-comments").val().length > 128){
            layerWarnTips("#commentsError2","备注长度不能超过128字符");
            return  false;
        }else{
            layerdelWarnTips("#commentsError2");
        }

        var addressData = {
            areaId: $('.J-zone').val(),
            areaIds: [$('.J-province').val(),$('.J-city').val(),$('.J-zone').val()].join(','),
            areaName: [$('.J-province option:selected').html(),$('.J-city option:selected').html(),$('.J-zone option:selected').html()].join(' '),
            address: $('.J-address').val(),
            zipCode: $('.J-zipCode').val(),
            comments: $(".J-comments").val()
        };
        var addressTmpl = template($('.J-address-tmpl').html());

        if(editId !== 0 && !editId){
            $('.J-address-list').append(addressTmpl({data: addressData}));
        }else{
            var $item = $('.J-address-list .J-address-item').eq(editId - 1);
            $item.after(addressTmpl({data: addressData}));
            $item.remove();
        }
        checkAddressList();

        layer.closeAll();
    });

    //删除地址
    $(document).on('click', '.J-address-del', function () {
        var $this = $(this);
        layer.confirm('确定删除？', function() {
            $this.parents('.J-address-item:first').remove();
            checkAddressList();
            layer.closeAll();
        })
    });

    //默认地址
    $(document).on('change', '[name=address-default]', function () {
        $('.J-address-item').each(function () {
            var data = $(this).data('json');

            if($(this).find('[name=address-default]')[0].checked){
                data.isDefault = 1;
            }else{
                data.isDefault = 0;
            }

            $(this).data('json', data);
        })
        checkAddressList();
    });

    //编辑地址
    $(document).on('click', '.J-address-edit', function () {
        var data = $(this).parents('.J-address-item').data('json');
        editId = $(this).parents('.J-address-item').index();
        layer.open({
            shadeClose: false, //点击遮罩关闭
            //area: 'auto',
            area: [680, 600],
            title: 'test',
            btn: null,
            content: $('.J-add-address-tmpl').html(),
            success: function () {
                $('.J-address').val(data.address);
                $('.J-comments').val(data.command);
                $('.J-zipCode').val(data.zipCode);
                var areaIds = data.areaIds.split(',');
                $('.J-province').val(areaIds[0]);

                getCity(areaIds[0], function () {
                    $('.J-city').val(areaIds[1]);

                    getZone(areaIds[1], function () {
                        $('.J-zone').val(areaIds[2]);
                    });
                });
            }
        });

    });

    $(document).on('click', '.J-address-tip span', function () {
        $(".J-comments").val($(this).text());
    });

    $(document).on('click', '.J-position-wrap span', function () {
        $(".J-position").val($(this).text());
    });

    $(document).on('click', '.J-department-wrap span', function () {
        $(".J-department").val($(this).text());
    });

    $(document).on('click', '.J-layer-close', function () {
        layer.closeAll();
    });

    var getCity = function(regionId, callback){
        $.ajax({
            type : "POST",
            url : page_url+"/system/region/getregion.do",
            data :{'regionId':regionId},
            dataType : 'json',
            success : function(data) {
                $option = "<option value='0'>请选择</option>";
                $.each(data.listData,function(i,n){
                    $option += "<option value='"+data.listData[i]['regionId']+"'>"+data.listData[i]['regionName']+"</option>";
                });
                $(".J-city option:gt(0)").remove();
                $(".J-zone option:gt(0)").remove();
                $(".J-zone").val("0").trigger("change");
                $(".J-city").val("0").trigger("change");

                $(".J-city").html($option);
                $(".J-zone").html("<option value='0'>请选择</option>");

                callback && callback();
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    };

    var getZone = function(regionId, callback){
        $.ajax({
            type : "POST",
            url : page_url+"/system/region/getregion.do",
            data :{'regionId':regionId},
            dataType : 'json',
            success : function(data) {
                $option = "<option value='0'>请选择</option>";
                $.each(data.listData,function(i,n){
                    $option += "<option value='"+data.listData[i]['regionId']+"'>"+data.listData[i]['regionName']+"</option>";
                });
                $(".J-zone option:gt(0)").remove();

                $(".J-zone").html($option);
                callback && callback();
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    };

    $(document).on('change','.J-province', function(){
        checkLogin();
        var regionId = $(this).val();
        if(regionId > 0){
            getCity(regionId);
        }else if(regionId==0){
            $("select[name='city'] option:gt(0)").remove();
            $("select[name='zone'] option:gt(0)").remove();
        }
    });

    $(document).on('change','.J-city', function(){
        checkLogin();
        var regionId = $(this).val();
        if(regionId > 0){
            getZone(regionId)
        }
    });

    //新增联系人
    var setConatctData = function () {
        var dataArr = [];
        $('.J-contact-item').each(function () {
            dataArr.push($(this).data('json'));
        });

        $('.J-contact-data').val(JSON.stringify(dataArr));
    };

    var checkContactList = function(){
        var listLen = $('.J-contact-item').length;

        if(listLen){
            $('.J-no-contact').hide();
        }else{
            $('.J-no-contact').show();
        }

        setConatctData();
    };

    checkContactList();
    var editContactId = null;

    $('.J-add-contact').click(function () {
        editContactId = null;
        layer.open({
            shadeClose: false, //点击遮罩关闭
            //area: 'auto',
            area: [680, 600],
            title: 'test',
            btn: null,
            content: $('.J-add-contact-tmpl').html()
        });
    });


    $(document).on('click', '.J-contact-add', function () {
        var realName = $(".J-name").val();
        var realNameReg = /^[a-zA-Z0-9\u4e00-\u9fa5\.]{2,16}$/;
        var realMessage = "员工姓名不允许使用特殊字符";
        if(realName == ''){
            layerWarnTips(".J-name","员工姓名不允许为空");
            return  false;
        }else{
            layerdelWarnTips(".J-name");
        }
        if(realName.length < 2 || realName.length > 16){
            layerWarnTips(".J-name","员工姓名长度应该在2-16个字符之间");
            return false;
        }else{
            layerdelWarnTips(".J-name");
        }
        if(!realNameReg.test(realName)){
            layerWarnTips(".J-name",realMessage);
            return  false;
        }else{
            layerdelWarnTips(".J-name");
        }
        if($(".J-department").val().length > 64){
            layerWarnTips("#departmentError","部门名称长度不能超过64个字符");
            return  false;
        }else{
            layerdelWarnTips("#departmentError");
        }
        if($(".J-position").val().length > 64){
            layerWarnTips("#positionError","职位名称长度不能超过64个字符");
            return  false;
        }else{
            layerdelWarnTips("#positionError");
        }

        //电话
        var telephone = $(".J-telephone").val();
        var telephoneReg = /^(\d{3,4}-?)?\d{7,9}(-?\d{2,6})?$|^$/;
        var telephoneMessage = "电话格式错误";

        if(telephone != '' && !telephoneReg.test(telephone)){
            layerWarnTips(".J-telephone",telephoneMessage);
            return  false;
        }else{
            layerdelWarnTips(".J-telephone");
        }


        //手机
        var mobile = $(".J-mobile").val();
        var mobileReg = /^1\d{10}$|^$/;
        var mobileMessage = "手机格式错误";
        if(mobile == ''){
            layerWarnTips(".J-mobile","手机号不允许为空");
            return  false;
        }else{
            layerdelWarnTips(".J-mobile");
        }
        if(!mobileReg.test(mobile)){
            layerWarnTips(".J-mobile",mobileMessage);
            return  false;
        }else{
            layerdelWarnTips(".J-mobile");
        }
        //手机2
        var mobile2 = $(".J-mobile2").val();
        if(!mobileReg.test(mobile2)){
            layerWarnTips(".J-mobile2",mobileMessage);
            return  false;
        }else{
            layerdelWarnTips(".J-mobile2");
        }
        //邮箱
        var email = $(".J-email").val();
        var emailReg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$|^$/;
        var emailMessage = "邮箱格式错误";
        if(email != '' && !emailReg.test(email)){
            layerWarnTips(".J-email",emailMessage);
            return  false;
        }else{
            layerdelWarnTips(".J-email");
        }
        var qqReg=/^\d{5,16}$/;
        if($(".J-qq").val()!='' && !qqReg.test($(".J-qq").val())){
            layerWarnTips(".J-qq","qq格式不正确");
            return  false;
        }else{
            layerdelWarnTips(".J-qq");
        }
        if($(".J-comments").val()!='' && $(".J-comments").val().length > 128){
            layerWarnTips(".J-comments","备注长度不能超过128字符");
            return  false;
        }else{
            layerdelWarnTips(".J-comments");
        }

        var contactData = {};
        $('.J-form input,.J-form textarea').each(function(){
            if($(this).attr('name') !== 'sex'){
                contactData[$(this).attr('name')] = $(this).val();
            }else{
                contactData['sex'] = $('.J-form [name=sex]:checked').val() || ''
            }
        });

        var contactTmpl = template($('.J-contact-tmpl').html());

        if(editContactId !== 0 && !editContactId){
            $('.J-contact-list').append(contactTmpl({data: contactData}));
        }else{
            var $item = $('.J-contact-list .J-contact-item').eq(editContactId - 1);
            $item.after(contactTmpl({data: contactData}));
            $item.remove();
        }
        checkContactList();
        layer.closeAll();
    });

    //删除联系人
    $(document).on('click', '.J-contact-del', function () {
        var $this = $(this);
        layer.confirm('确定删除？', function() {
            $this.parents('.J-contact-item:first').remove();
            checkContactList();
            layer.closeAll();
        })
    });
    //编辑联系人
    $(document).on('click', '.J-contact-edit', function () {
        var data = $(this).parents('.J-contact-item').data('json');
        editContactId = $(this).parents('.J-contact-item').index();
        layer.open({
            shadeClose: false, //点击遮罩关闭
            //area: 'auto',
            area: [680, 600],
            title: 'test',
            btn: null,
            content: $('.J-add-contact-tmpl').html(),
            success: function () {
               for(var item in data){
                   if(item == 'sex'){
                       if(data[item] || data[item] === 0){
                           $('.J-form [name=sex][value=2]')[0].checked = false;
                           $('.J-form [name=sex][value=' + data[item] + ']')[0].checked = true;
                       }
                   }else{
                       $('.J-form [name=' + item + ']').val(data[item])
                   }
               }
            }
        });

    });

    //默认联系人
    $(document).on('change', '[name=default-contact]', function () {
        $('.J-contact-item').each(function () {
            var data = $(this).data('json');

            if($(this).find('[name=default-contact]')[0].checked){
                data.isDefault = 1;
            }else{
                data.isDefault = 0;
            }

            $(this).data('json', data);
        });
        checkContactList();
    });

	$("select[name='warehouseProvince']").change(function(){
		checkLogin();
		var regionId = $(this).val();
		if(regionId > 0){
			$.ajax({
				type : "POST",
				url : page_url+"/system/region/getregion.do",
				data :{'regionId':regionId},
				dataType : 'json',
				success : function(data) {
					$option = "<option value='0'>请选择</option>";
					$.each(data.listData,function(i,n){
						$option += "<option value='"+data.listData[i]['regionId']+"'>"+data.listData[i]['regionName']+"</option>";
					});
					$("select[name='warehouseCity'] option:gt(0)").remove();
					$("select[name='warehouseAreaId'] option:gt(0)").remove();
					$("#warehouseAreaId").val("0").trigger("change");
					$("#warehouseCity").val("0").trigger("change");

					$("select[name='warehouseCity']").html($option);
					$("select[name='warehouseAreaId']").html("<option value='0'>请选择</option>");
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
		}else if(regionId==0){
			$("select[name='warehouseCity'] option:gt(0)").remove();
			$("select[name='warehouseAreaId'] option:gt(0)").remove();
		}
	});

	$("select[name='warehouseCity']").change(function(){
		checkLogin();
		var regionId = $(this).val();
		if(regionId > 0){
			$.ajax({
				type : "POST",
				url : page_url+"/system/region/getregion.do",
				data :{'regionId':regionId},
				dataType : 'json',
				success : function(data) {
					$option = "<option value='0'>请选择</option>";
					$.each(data.listData,function(i,n){
						$option += "<option value='"+data.listData[i]['regionId']+"'>"+data.listData[i]['regionName']+"</option>";
					});
					$("select[name='warehouseAreaId'] option:gt(0)").remove();

					$("select[name='warehouseAreaId']").html($option);
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
		}
	});


});


//品牌列表
function initBrand(){
	checkLogin();
	$.ajax({
		type : "POST",
		url : page_url+"/goods/brand/getallbrand.do",
		dataType : 'json',
		success : function(data) {
			$option = "<option value='0'>请选择</option>";
			$.each(data.listData,function(i,n){
				$option += "<option value='"+data.listData[i]['brandId']+"'>"+data.listData[i]['brandName']+"</option>";
			});
			$("select[name='bussinessBrands']").html($option);
			$("select[name='agentBrands']").html($option);
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});
}

function conaddBase(){
	var num = Number($("#conadd").siblings(".form-blanks").length)+Number(1);
	var div = '<div class="form-blanks mt10">'+
            	'<div class="pos_rel f_left">'+
                    '<input type="text" class="input-largest" id="name_'+num+'" name="companyUri" >'+
    			'</div>'+
            	'<div class="f_left"><span class="font-red cursor-pointer ml10 mt4" onclick="delBase('+num+')" id="img_del_'+num+'">删除</span></div><div class="clear"></div>'+
            '</div>';
	$("#conadd").before(div);
}

//搜索经营品牌
function searchBussinessBrand(){
	checkLogin();
	var brand = $("input[name='bussinessBrandKey']").val();
	$.ajax({
		type : "POST",
		url : page_url+"/goods/brand/getallbrand.do",
		data : {brandName:brand},
		dataType : 'json',
		success : function(data) {
			$option = "<option value='0'>请选择</option>";
			$.each(data.listData,function(i,n){
				var selected = "";
				if(brand == data.listData[i]['brandName']){
					selected = "selected";
				}
				$option += "<option value='"+data.listData[i]['brandId']+"' "+selected+">"+data.listData[i]['brandName']+"</option>";
			});
			$("select[name='bussinessBrands']").html($option);
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});
}

//添加经营品牌
function addBussinessBrand(){
	checkLogin();
	var brandId = $("select[name='bussinessBrands']").val();
	if(brandId == 0){
		return false;
	}
	
	brandName = $('select[name="bussinessBrands"]').find(':selected').text();
	
	var ok = true;
	$.each($("input[name='bussinessBrandId']"),function(i,n){
		if($(this).val() == brandId){
			ok = false;
		}
	});
	
	if(ok){
		var bussinsessLi = "<li class='bluetag'>"+brandName
		+"<input type='hidden' name='bussinessBrandId' value='"+brandId+"'><i class='iconbluecha' onclick='delTag(this);'></i></li>";
		
		$("#bussinessBrand_show").append(bussinsessLi);
		$('select[name="bussinessBrands"]').val(0);
		$("#bussinessBrand_show").parent(".addtags").show();
	}else{
		layer.tips("选择的品牌已经存在","#addBussinessBrand",{time:1000});
	}
}

function delBase(num){
	var sum = Number($("#conadd").siblings(".form-blanks").length);
	if( sum > 1){
		index = layer.confirm("您是否确认该操作？", {
			  btn: ['确定','取消'] //按钮
			}, function(){
				$("#img_del_"+num).parent().parent(".form-blanks").remove();
				layer.close(index);
			}, function(){
			});
	}
}
function checkTraderName(){
	var traderName=$("input[name='traderName']").val();
	var traderNameReg = /^[a-zA-Z0-9\u4e00-\u9fa5\.\(\)\,\（\）\s]{2,128}$/;
	if(traderName == ""){
		warnTips("traderName","供应商名称不允许为空");
		return false;
	}else if(traderName.length < 2 ||traderName.length > 128){
		warnTips("traderName","供应商名称长度为2-128个字符长度");
		return false;
	}else if(!traderNameReg.test(traderName)){
		warnTips("traderName","供应商名称不允许使用特殊字符");
		return false;
	}else{
		$("input[name='traderName']").next().remove();
		$("input[name='traderName']").removeClass("errorbor");
	}
}


function uploadFile(obj, num) {
	var imgPath = $(obj).val();
	if (imgPath == '' || imgPath == undefined) {
		return false;
	}
	var oldName = imgPath.substr(imgPath.lastIndexOf('\\') + 1);
	var domain = $("#domain").val();
	//判断上传文件的后缀名
	var strExtension = imgPath.substr(imgPath.lastIndexOf('.') + 1);
	// 转换为小写格式
	strExtension = strExtension.toLowerCase();
	if (strExtension != 'jpg' && strExtension != 'gif' && strExtension != 'png' && strExtension != 'bmp' && strExtension != 'pdf' && strExtension != 'jpeg') {
		layer.alert("文件格式不正确");
		return;
	}

	var fileSize = 0;
	var isIE = /msie/i.test(navigator.userAgent) && !window.opera;
	if (isIE && !obj.files) {
		var filePath = obj.value;
		var fileSystem = new ActiveXObject("Scripting.FileSystemObject");
		var file = fileSystem.GetFile(filePath);
		fileSize = file.Size;
	} else {
		fileSize = obj.files[0].size;
	}
	fileSize = Math.round(fileSize / 1024 * 100) / 100; //单位为KB

	//生产企业生产产品登记表附件调整为30M
	if(num==101){
		if (fileSize > 30720) {
			layer.alert("上传附件不得超过30M");
			return false;
		}
	}else {
		if (fileSize > 5120) {
			layer.alert("上传附件不得超过5M");
			return false;
		}
	}

	$(obj).parent().parent().find("i").attr("class", "iconloading mt5").show();
	$(obj).parent().parent().find("a").hide();
	$(obj).parent().parent().find("span").hide();
	var objCopy1 = $(obj).parent();
	var objCopy2 = $(obj).parent().parent();
	$.ajaxFileUpload({
		url: page_url + '/fileUpload/ajaxFileUpload.do', //用于文件上传的服务器端请求地址
		secureuri: false, //一般设置为false
		fileElementId: $(obj).attr("id"), //文件上传控件的id属性  <input type="file" id="file" name="file" /> 注意，这里一定要有name值   //$("form").serialize(),表单序列化。指把所有元素的ID，NAME 等全部发过去
		dataType: 'json',//返回值类型 一般设置为json
		//服务器成功响应处理函数
		success: function (data) {
			if (data.code == 0) {

				objCopy1.find("input[type='text']").val(data.fileName);
				objCopy1.find("input[type='hidden']").val(data.filePath);
				$("#domain").val(data.httpUrl);
				objCopy2.find("i").attr("class", "iconsuccesss ml7").show();
				objCopy2.find("a").attr("href", 'http://' + data.httpUrl + data.filePath).show();
				objCopy2.find("span").show();
				$("#brand_book").css("display", "none");
				$("#name_" + num).removeClass("errorbor");
			} else {
				layer.alert(data.message);
			}
		},
		//服务器响应失败处理函数
		error: function (data, status, e) {

			if (data.status == 1001) {
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			} else {
				layer.alert(data.responseText);
			}

		}
	});

}

// 纳税人附件个数
let taxPayerAttachmentCount = 1;

function del(num) {
	checkLogin();
	index = layer.confirm("您是否确认该操作？", {
		btn: ['确定', '取消'] //按钮
	}, function () {
		var threeInOne = $('input:radio[name="threeInOne"]:checked').val();
		var medicalQualification = $('input:radio[name="medicalQualification"]:checked').val();

		if (threeInOne == 1 && num == 1) {
			$("#img_icon_" + 1).hide();
			$("#img_view_" + 1).hide();
			$("#img_del_" + 1).hide();
			$("#name_" + 1).val("");
			$("#uri_" + 1).val("");
			$("#busStartTime").val("");
			$("#busEndTime").val("");
			$("input[name='isMedical']").removeAttr("checked");
			$("#img_icon_" + 2).hide();
			$("#img_view_" + 2).hide();
			$("#img_del_" + 2).hide();
			$("#name_" + 2).val("");
			$("#uri_" + 2).val("");
			$("#taxStartTime").val("");
			$("#taxEndTime").val("");
			$("#img_icon_" + 3).hide();
			$("#img_view_" + 3).hide();
			$("#img_del_" + 3).hide();
			$("#name_" + 3).val("");
			$("#uri_" + 3).val("");
			$("#orgaStartTime").val("");
			$("#orgaEndTime").val("");
			$("#file_" + num).val("");
		} else if (threeInOne == 0) {
			$("#img_icon_" + num).hide();
			$("#img_view_" + num).hide();
			$("#img_del_" + num).hide();
			$("#name_" + num).val("");
			$("#uri_" + num).val("");
			$("#file_" + num).val("");
			if (num == 1) {
				$("#busStartTime").val("");
				$("#busEndTime").val("");
				$("input[name='isMedical']").removeAttr("checked");
			} else if (num == 2) {
				$("#taxStartTime").val("");
				$("#taxEndTime").val("");
			} else if (num == 3) {
				$("#orgaStartTime").val("");
				$("#orgaEndTime").val("");
			}


		}
		if (medicalQualification != undefined && medicalQualification == 1 && num == 4) {
			$("#img_icon_" + 4).hide();
			$("#img_view_" + 4).hide();
			$("#img_del_" + 4).hide();
			$("#name_" + 4).val("");
			$("#uri_" + 4).val("");
			$("#twoStartTime").val("");
			$("#twoEndTime").val("");
			$("#twoSn").val("");
			$("#img_icon_" + 5).hide();
			$("#img_view_" + 5).hide();
			$("#img_del_" + 5).hide();
			$("#name_" + 5).val("");
			$("#uri_" + 5).val("");
			$("#threeStartTime").val("");
			$("#threeEndTime").val("");
			$("#threeSn").val("");
			$("#file_" + num).val("");
		} else if (medicalQualification == 0) {
			$("#img_icon_" + num).hide();
			$("#img_view_" + num).hide();
			$("#img_del_" + num).hide();
			$("#name_" + num).val("");
			$("#uri_" + num).val("");
			$("#file_" + num).val("");
			if (num == 4) {
				$("#twoStartTime").val("");
				$("#twoEndTime").val("");
				$("#twoSn").val("");
			} else if (num == 5) {
				$("#threeStartTime").val("");
				$("#threeEndTime").val("");
				$("#threeSn").val("");
			}
		}
		if (num == 6) {
			$("#img_icon_" + num).hide();
			$("#img_view_" + num).hide();
			$("#img_del_" + num).hide();
			$("#name_" + num).val("");
			$("#uri_" + num).val("");
			$("#productStartTime").val("");
			$("#productEndTime").val("");
			$("#productSn").val("");
			$("#file_" + num).val("");
		} else if (num == 7) {
			$("#img_icon_" + num).hide();
			$("#img_view_" + num).hide();
			$("#img_del_" + num).hide();
			$("#name_" + num).val("");
			$("#uri_" + num).val("");
			$("#operateStartTime").val("");
			$("#operateEndTime").val("");
			$("#operateSn").val("");
			$("#file_" + num).val("");
		}
		layer.close(index);
	}, function () {
	});
}

function medOne() {
	checkLogin();
	$("#img_del_5").removeClass("cursor-pointer");
	$("#img_del_5").prop("onclick", null).off("click");
	$("#threeStartTime").attr("disabled", "disabled");
	$("#threeEndTime").attr("disabled", "disabled");
	$("#twoSn").attr("disabled", "disabled");
	$("#twoSn").val('');
	$('#twoMedicalLab').html('多证合一辅助证明');
	$('#twoMedicalSpan').html('*');
	$('#medicalQualificationFont').css('display', '');
	$("#twoSnLab").addClass("bg-opcity");
}

function medZero() {
	checkLogin();
	$("#img_del_5").addClass("cursor-pointer");
	$("#img_del_5").attr("onclick", "del(5)");
	$("#threeStartTime").removeAttr("disabled");
	$("#threeEndTime").removeAttr("disabled");
	$("#twoSn").removeAttr("disabled");
	$('#twoMedicalSpan').html('');
	$('#twoMedicalLab').html('医疗器械二类备案凭证');
	$('#medicalQualificationFont').css('display', 'none');
	$("#twoSnLab").removeClass("bg-opcity");
}

function thOne() {
	checkLogin();
	$("#img_del_2").removeClass("cursor-pointer");
	$("#img_del_2").prop("onclick", null).off("click");
	$("#taxStartTime").attr("disabled", "disabled");
	$("#taxEndTime").attr("disabled", "disabled");
	$("#img_del_3").removeClass("cursor-pointer");
	$("#img_del_3").prop("onclick", null).off("click");
	$("#orgaStartTime").attr("disabled", "disabled");
	$("#orgaEndTime").attr("disabled", "disabled");
}

function thZero() {
	checkLogin();
	$("#img_del_2").addClass("cursor-pointer");
	$("#img_del_2").attr("onclick", "del(2)");
	$("#taxStartTime").removeAttr("disabled");
	$("#taxEndTime").removeAttr("disabled");
	$("#img_del_3").addClass("cursor-pointer");
	$("#img_del_3").attr("onclick", "del(3)");
	$("#orgaStartTime").removeAttr("disabled");
	$("#orgaEndTime").removeAttr("disabled");
}

//点击继续添加按钮
function con_add(id) {
	var desc = '';
	if (id == 4) {
		desc = '请上传二类备案凭证';
	}else if (id == 9) {
		desc = '请上传品牌授权书';
	} else if (id == 10) {
		desc = '请上传其他资质图片';
	}else if (id == 103){
		desc = '请上传随货同行单模板';
	}else if(id == 104){
		desc = '请上传质量保证协议';
	} else if (id == 105){
		desc = '请上传售后服务承诺书';
	}else if (id == 106){
		desc = '请上传质量体系调查表或合格供应商档案';
	}else if (id == 1) {
		desc = '请上传营业执照';
	}else if (id == 5) {
		desc = '请上传医疗器械经营许可证 (三类)';
	}else if (id == 6) {
		desc = '请上传医疗器械生产许可证';
	}else if (id == 100) {
		desc = '请上传第一类医疗器械生产备案凭证';
	}else if (id == 101) {
		desc = '请上传生产企业生产产品登记表';
	}else if (id == 8) {
		desc = '请上传销售人员授权书';
	}else if (id == 20) {
		desc = '请上传纳税人附件';
		taxPayerAttachmentCount++;
		if (taxPayerAttachmentCount >= 10) {
			$("#conadd20").css("display", "none");
		}
	}


	var rndNum = RndNum(8);
	var html = '<div >' +
		'<div class="pos_rel f_left mb8">' +
		'<input type="file" class=" uploadErp"  name="lwfile" id="lwfile_' + id + '_' + rndNum + '" onchange="uploadFile(this, ' + id + ')">' +
		'<input type="text" class="input-middle" id="name_' + id + '_' + rndNum + '" readonly="readonly" placeholder="' + desc + '" name="name_' + id + '" onclick="lwfile_' + id + '_' + rndNum + '.click();" value ="">' +
		'<input type="hidden" class="input-middle mr5" id="uri_' + id + '_' + rndNum + '" name="uri_' + id + '" value="">' +
		'<label class="bt-bg-style bt-middle bg-light-blue " type="file" style="margin:0 17px 0 14px;">浏览</label>' +
		'</div>' +
		'<div class="f_left ">' +
		'<i class="iconsuccesss mt5 none" id="img_icon_' + id + '"></i>' +
		'<a href="" target="_blank" class="font-blue cursor-pointer mr10  ml10 mt4 none" id="img_view_' + id + '" style="margin:0 8px 0 12px">查看</a>' +
		'<span class="font-red cursor-pointer mt4" onclick="delAttachment(this)" id="img_del_' + id + '">删除</span>' +
		'</div>' +
		'<div class="clear "></div></div>';
	$("#conadd" + id).before(html);

}

function RndNum(n) {
	var rnd = "";
	for (var i = 0; i < n; i++)
		rnd += Math.floor(Math.random() * 10);
	return rnd;
}

function delAttachment(obj) {
	var uri = $(obj).parent().find("a").attr("href");
	let id = $(obj).parent().find("span").attr("id");
	if (uri == '') {
		$(obj).parent().parent().remove();
		if (id == 'img_del_20') {
			taxPayerAttachmentCount --;
		}
		if (taxPayerAttachmentCount < 10) {
			$("#conadd20").css('display', '');
		}
	} else {
		index = layer.confirm("您是否确认该操作？", {
			btn: ['确定', '取消'] //按钮
		}, function () {
			var length = $(obj).parent().parent().parent().find("input[type='file']").length;
			if (length == 1) {
				$(obj).parent().find("i").hide();
				$(obj).parent().find("a").hide();
				$(obj).parent().find("span").hide();
				$(obj).parent().parent().parent().find("input[type='text']").val('');
				if (id == 'img_del_20') {
					taxPayerAttachmentCount --;
				}
			} else {
				$(obj).parent().parent().remove();
				if (id == 'img_del_20') {
					taxPayerAttachmentCount --;
				}
			}
			if (taxPayerAttachmentCount < 10) {
				$("#conadd20").css('display', '');
			}
			layer.close(index);
		}, function () {
		});
	}
}

//全选
function clickAll(type) {
	//全选
	$("input[name=" + type + "All]").click(function () {
		var thisChecked = $(this).prop('checked');
		$('input[name=' + type + ']').prop('checked', thisChecked);
	})
	var thisChecked = $("input[name=" + type + "All]").prop('checked');
	$('input[name=' + type + ']').prop('checked', thisChecked);
}

//单选
function clickOne(type) {
	var num = $('input[name=' + type + ']:checked').length;
	var sum = $('input[name=' + type + ']').length;
	if (num == sum) {
		$('input[name=' + type + 'All]').prop('checked', true);
	} else {
		$('input[name=' + type + 'All]').prop('checked', false);
	}
}

/**
 * 获取贝登天眼查库房地区信息
 */
function getTyWarehouseArea() {
	$('#referenceInfo').html('');
	var traderName = $('#traderName').val();
	if (traderName == null || traderName == undefined || traderName == ''){
		layer.alert('请输入供应商名称！')
		return;
	}
	$.ajax({
		type : "POST",
		url : page_url+"/trader/supplier/getOperateInfo.do",
		data : {'traderName':traderName},
		dataType : 'json',
		async : false,
		success : function(result) {
			if(result.code == 0){
				if (result.data == null || result.data == ''){
					$('#referenceInfo').html('库房地址参考：无');
				} else {
					$('#referenceInfo').html('库房地址参考：' + result.data);
				}
			} else {
				layer.alert(result.message);
			}
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});
}
