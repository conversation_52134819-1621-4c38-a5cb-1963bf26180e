package com.vedeng.logistics.dao;

import com.vedeng.logistics.model.WarehouseGoodsOutIn;
import com.vedeng.logistics.model.WarehouseGoodsOutInItem;
import com.vedeng.logistics.model.vo.WarehouseGoodsInLogVo;
import com.vedeng.logistics.model.vo.WarehouseGoodsOutLogVo;
import com.vedeng.logistics.model.vo.WarehouseGoodsOutVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WarehouseGoodsOutInMapper {
    /**
     * delete by primary key
     * @param warehouseGoodsOutInId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long warehouseGoodsOutInId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(WarehouseGoodsOutIn record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(WarehouseGoodsOutIn record);

    /**
     * select by primary key
     * @param warehouseGoodsOutInId primary key
     * @return object by primary key
     */
    WarehouseGoodsOutIn selectByPrimaryKey(Long warehouseGoodsOutInId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(WarehouseGoodsOutIn record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(WarehouseGoodsOutIn record);

    /**
     * 根据 出入库单号查询
     * @param outInNo 出入库单号
     * @return WarehouseGoodsOutIn
     */
    WarehouseGoodsOutIn selectByOutInNo(@Param("outInNo") String outInNo);

    String getBuyTraderNameByAftersale(String relateNo);

    /**
     * 根据出入库单号 和 类型 查询出库单据详情
     * @param outInType
     * @param outInNo
     * @return
     */
    WarehouseGoodsOutVo selectWarehouseGoodsOutDetail(@Param("outInType") Integer outInType,@Param("outInNo") String outInNo);

    /**
     * 根据关联单号查询关联的出入库单详情
     * @param outInType 出库类型
     * @param relatedNo 关联单号
     * @return 出入库单列表
     */
    List<WarehouseGoodsOutVo> selectWarehouseGoodsOutDetailByRelatedNo(@Param("outInType") Integer outInType, @Param("relatedNo") String relatedNo);

    /**
     * 根据关联单号查询关联的出入库记录
     * @param outInType 出库类型
     * @param relatedNo 关联单号
     * @return 出库记录列表
     */
    List<WarehouseGoodsOutLogVo> selectWarehouseGoodsOutLogListByRelatedNo(@Param("outInType") Integer outInType, @Param("relatedNo") String relatedNo);

    /**
     * 根据关联单号查询关联的出入库记录
     * @param outInType 出库类型
     * @param relatedNo 关联单号
     * @return 入库记录列表
     */
    List<WarehouseGoodsInLogVo> selectWarehouseGoodsInLogListByRelatedNo(@Param("outInType") Integer outInType, @Param("relatedNo") String relatedNo);

    /**
     * 根据关联单号查询出库详情记录
     * @param orderNos
     * @param goodsId
     * @return
     */
    List<String> getBarcodFactoryListByOrderNo(@Param("orderNos") List<String> orderNos, @Param("goodsId") Integer goodsId);

    List<String> getExistBarcodFactoryList(@Param("orderNo") String orderNo, @Param("goodsId") Integer goodsId);

    List<String> getBatchNumberListByOrderNo(@Param("orderNo") String orderNo,@Param("goodsId") Integer goodsId);

    String getSaleTraderNameBySaleOrder(@Param("orderNo") String orderNo);

    /**
     * 获取销售售后退换货入库记录已使用的sn码
     * @param orderNo
     * @param goodsId
     * @return
     */
    List<String> getExistBarcodFactoryForInList(@Param("orderNo") String orderNo, @Param("goodsId") Integer goodsId);

    /**
     * 获取售后单号对应的销售单对应的采购单号
     * @param afterSalesNo
     * @return
     */
    List<String> getBuyOrderOrderNo(@Param("afterSalesNo") String afterSalesNo);

    /**
     * 获取厂商批次号
     * @param orderNos
     * @param goodsId
     * @return
     */
    List<String> getVedengBatchListByOrderNo(@Param("orderNos") List<String> orderNos, @Param("goodsId") Integer goodsId);

    String getSaleTraderNameByAftersale(String orderNo);

    /**
     * 根据销售售后退货单号获取销售单号,一个售后单只可能关联一个销售单
     * @param afterSalesNo
     * @return
     */
    String getSaleOrderNoByAfterSalesNo(@Param("afterSalesNo") String afterSalesNo);

    /**
     * 根据销售单号，查询销售出库时的批次码
     * @param orderNos
     * @param goodsId
     * @return
     */
    List<String> getBarcodFactoryListBySaleOrderNo(@Param("orderNos") List<String> orderNos, @Param("goodsId") Integer goodsId,@Param("relatedId") Integer relatedId);


    /**
     * 获取销售出库厂商批号
     * @param orderNos
     * @param goodsId
     * @return
     */
    List<String> getVedengBatchListBySaleOrderNo(@Param("orderNos") List<String> orderNos, @Param("goodsId") Integer goodsId ,@Param("relatedId") Integer relatedId);

    /**
     * 根据销售订单商品的主键id，查询销售出库时的SN码
     *
     * @param relatedId saleodergoods的主键id
     * @return
     */
    List<String> getBarcodFactoryListBySaleOrderGoodsId(@Param("relatedId") Integer relatedId);

    /**
     * 根据销售订单商品的主键id，查询销售出库单
     *
     * @param relatedId saleodergoods的主键id
     * @return
     */
    List<WarehouseGoodsOutInItem> getWarehouseGoodsOutInBySaleOrderGoodsId(@Param("relatedId") Integer relatedId);


    /**
     * 根据销售订单商品的主键id，查询销售换货出库单
     *
     * @param relatedId saleodergoods的主键id
     * @return
     */
    List<WarehouseGoodsOutInItem> getWarehouseGoodsOutInChangeBySaleOrderGoodsId(@Param("relatedId") Integer relatedId);

    /**
     * 逻辑删除
     * @param warehouseGoodsOutInId
     * @return
     */
    int logicalDeleteByWarehouseGoodsOutInId(@Param("warehouseGoodsOutInId")Long warehouseGoodsOutInId);

    /**
     * 查询主单号
     * @param list
     * @return
     */
    List<String> findOutInNoByWarehouseGoodsOutInId(@Param("list")List<Integer> list);




}