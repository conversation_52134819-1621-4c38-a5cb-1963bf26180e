package com.vedeng.order.service.validator.impl;

import com.vedeng.goods.model.CoreSpuGenerate;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.order.service.validator.dto.QualifyAutoAudtioException;
import org.springframework.stereotype.Service;

/**
 * 	match:若采购订单产品【SPU信息中"是否在《医疗器械分类目录》"字段为'否'】
 * 	validator:
 * 	该供应商须满足以下资质条件才能自动审核通过：
 *    1）营业执照已上传，
 *    2）执照在有效期内
 * 备注输出内容为：订单中产品为非医疗产品
 */
@Service
public class QualifyAutoAudtioValidator1 extends AbstractQualifyAutoAudtioValidator{

    @Override
    public boolean isMatch(BuyorderGoodsVo buyOrderGoods) {

        CoreSpuGenerate coreSpuGenerate = super.getSpuInfo(buyOrderGoods.getSku());

        return coreSpuGenerate.getMedicalInstrumentCatalogIncluded() == 0 ? true : false;

    }

    @Override
    public String validator(BuyorderGoodsVo buyOrderGoods) throws QualifyAutoAudtioException {

        Integer traderId = super.getTradeInfo(buyOrderGoods).getTraderId();

        if(!super.isBusinessLicenseUpload(traderId)){
            throw new QualifyAutoAudtioException("该供应商的营业执照缺失");
        }

        if(!super.isBusinessLicenseInValidityPeriod(traderId)){
            throw new QualifyAutoAudtioException("该供应商的营业执照过期");
        }

        return "订单中产品为非医疗产品";
    }
    
    @Override
    public boolean isMatch(Integer traderId, String skuNo) {
        CoreSpuGenerate coreSpuGenerate = super.getSpuInfo(skuNo);
        
        return coreSpuGenerate.getMedicalInstrumentCatalogIncluded() == 0 ? true : false;
    }
    
    @Override
    public String validator(Integer traderId, String skuNo) throws QualifyAutoAudtioException {
        if(!super.isBusinessLicenseUpload(traderId)){
            throw new QualifyAutoAudtioException("该供应商的营业执照缺失");
        }

        if(!super.isBusinessLicenseInValidityPeriod(traderId)){
            throw new QualifyAutoAudtioException("该供应商的营业执照过期");
        }

        return "订单中产品为非医疗产品";
    }
}
