void function () {

    new Vue({
        el: '#page-container',
        data: {
            title: '新增场景方案',
            prodListHeader: [
                {
                    label: "基础信息",
                    key: "baseinfo",
                    width: "249px"
                },
                {
                    label: "主要参数",
                    key: "mainparam",
                    width: "230px"
                },
                {
                    label: "价格信息",
                    key: "priceinfo",
                    width: "167px"
                },
                {
                    label: "售后信息",
                    key: "saleinfo",
                    width: "120px"
                },
                {
                    label: "产品状态",
                    key: "prodstatus",
                    width: "150px"
                },
                {
                    label: "产品负责人",
                    key: "manager",
                    width: "180px"
                },
                {
                    label: "操作",
                    key: "option",
                    width: "68px"
                }
            ],
            tableFixedTop: 136,
            tabInfo: [],
            currentProdList: [],
            currentTabIndex: 0,
            isloading: true,
            optionFixed: false,
            sceneName: '',
            sceneNameError: '',
            sceneDescription: '',
            prodSelected: [],
            isShowEditDialog: false,
            editCategoryName: '',
            editCategoryIndex: -1,
            editCategoryType: '',
            isShowMultiAdd: false, //批量添加
            multiValue: '',
            multiAddErrorMsg: '',
            id: '',
            copyid: '',
            cansubmit: true, //防重
            replaceProdSkuNo: '',
            prodSelectType: '', //商品选型类型，默认为空：单选替换；为1：批量选择 
        },
        mounted() {
            if (VD_UI_GLOBAL.getQuery('id')) {
                this.id = VD_UI_GLOBAL.getQuery('id');
                this.title = "编辑场景方案"
                this.initList();
            } else if (VD_UI_GLOBAL.getQuery('copyid')) {
                this.copyid = VD_UI_GLOBAL.getQuery('copyid');
                this.initList();
            } else {
                this.addEmptyCategory();
                this.isloading = false;
            }

            window.addEventListener('scroll', this.checkOptionFixed);

            window.onbeforeunload = () => {
                return false;
            }
        },
        methods: {
            initList() {
                VD_UI_GLOBAL.showGlobalLoading();
                this.isloading = true;
                axios.post('/sku/scene/get.do?id=' + (this.id || this.copyid)).then(({ data }) => {
                    VD_UI_GLOBAL.hideGlobalLoading();
                    this.isloading = false;
                    if (data.success) {
                        this.sceneName = data.data.name;
                        this.sceneDescription = data.data.description || '';
                        this.tabInfo = data.data.skuSceneCategoryDtoList || [];

                        let idTime = new Date().getTime();

                        this.tabInfo.forEach((item, index) => {
                            item.index = 'id' + (idTime + index);
                        });

                        if (this.tabInfo) {
                            this.changeTab(this.tabInfo[0].index);
                            this.currentProdList = this.tabInfo[0].productInfoList;
                        }
                    } else {
                        this.$message.warn(data.message)
                    }
                })
            },
            async validSceneName() {
                this.sceneNameError = '';

                if (!this.sceneName) {
                    this.sceneNameError = '请填写场景名称';
                    return false;
                }

                let flag = true;

                await axios.post(`/sku/scene/check.do?name=${this.sceneName}&id=${this.id}`).then(({ data }) => {
                    if (data.success && !data.data) {
                        this.sceneNameError = '该名称已存在';
                        flag = false;
                    }
                })

                return flag;
            },
            checkOptionFixed() {
                if (window.scrollY > this.$refs.optionWrap.offsetTop - 73) {
                    this.optionFixed = true;
                } else {
                    this.optionFixed = false;
                }
            },
            changeTab(index) {
                this.currentTabIndex = index;
                this.tabInfo.forEach(item => {
                    if (item.index == index) {
                        this.currentProdList = item.productInfoList;
                    }
                })
                this.prodSelected = [];
            },
            prodSelectChange(data) {
                this.prodSelected = data;
            },
            handlerTabSort() {
                // console.log(this.tabInfo)
            },
            deleteCategory(index, item) {
                let _this = this;
                this.$popup.warn({
                    message: '删除会将分类下的产品全部删除，确定删除吗？',
                    buttons: [{
                        txt: '删除',
                        btnClass: 'delete',
                        callback() {
                            _this.tabInfo.splice(index, 1);
                            if (item.index == _this.currentTabIndex && _this.tabInfo.length) {
                                _this.changeTab(_this.tabInfo[0].index);
                            }
                        }
                    }, {
                        txt: '取消',
                    }]
                })
            },
            deleteProduct(sku, toast) {
                this.tabInfo.forEach(item => {
                    if (item.index === this.currentTabIndex) {
                        item.productInfoList.forEach((prod, pindex) => {
                            if (prod.basicInfo.skuNo == sku) {
                                item.productInfoList.splice(pindex, 1);

                                if (toast == 1) {
                                    this.$message.success('删除成功');
                                }
                            }
                        })
                    }
                })
            },
            multiDeleteProduct() {
                let _this = this;

                if (!this.prodSelected.length) {
                    this.$message.warn('请选择产品');
                    return;
                }

                this.$popup.warn({
                    message: '确定删除选中的产品吗？',
                    buttons: [{
                        txt: '删除',
                        btnClass: 'delete',
                        callback() {
                            _this.prodSelected.forEach(item => {
                                _this.deleteProduct(item.basicInfo.skuNo);
                            });
                            _this.prodSelected = [];
                            _this.$message.success('删除成功');
                        }
                    }, {
                        txt: '取消',
                    }]
                })
            },
            addCategory() {
                if(this.tabInfo.length >= 50) {
                    this.$message.warn('最多添加50个分类');
                    return
                }

                this.showEditDialog();
                this.editCategoryName = "";
                this.editCategoryIndex = -1;
                this.editCategoryType = 'add';
            },
            copyCategory(item, index) {
                if(this.tabInfo.length >= 50) {
                    this.$message.warn('最多添加50个分类');
                    return
                }

                this.showEditDialog();
                this.editCategoryName = item.name;
                this.editCategoryIndex = index;
                this.editCategoryType = 'copy';
            },
            editCategory(item, index) {
                this.showEditDialog();
                this.editCategoryName = item.name;
                this.editCategoryIndex = index;
                this.editCategoryType = 'rename';
            },
            showEditDialog() {
                this.isShowEditDialog = true;
                let _this = this;
                this.$form.rules({
                    editCategoryName: {
                        required: '请填写分类名称',
                        custom: {
                            valid() {
                                let flag = true;
                                _this.tabInfo.forEach(item => {
                                    if(_this.editCategoryType === 'add' || _this.editCategoryType === 'copy') {
                                        if (item.name == _this.editCategoryName.trim()) {
                                            flag = false;
                                        }
                                    } else if (_this.editCategoryType === 'edit') {
                                        if (item.name == _this.editCategoryName.trim() && item.index !== _this.editCategoryIndex) {
                                            flag = false;
                                        }
                                    }
                                })

                                return flag;
                            },
                            message: '该分类名称已存在'
                        },
                    },
                }, 'categoryFrom', this);
            },
            addEmptyCategory() {
                this.tabInfo.push({
                    name: '未命名',
                    productInfoList: [],
                    index: 0
                })

                this.changeTab(0);
            },
            saveCategory() {
                if (!this.$form.validForm('categoryFrom')) {
                    return;
                }

                if (this.editCategoryType == 'add') {
                    let id = 'id' + new Date().getTime();
                    this.tabInfo.push({
                        name: this.editCategoryName,
                        productInfoList: [],
                        index: id
                    })

                    this.changeTab(id);
                } else if (this.editCategoryType == 'rename') {
                    this.tabInfo[this.editCategoryIndex].name = this.editCategoryName;
                } else if (this.editCategoryType == 'copy') {
                    let id = 'id' + new Date().getTime();
                    let list = JSON.parse(JSON.stringify(this.tabInfo[this.editCategoryIndex].productInfoList));

                    this.tabInfo.push({
                        name: this.editCategoryName,
                        productInfoList: list,
                        index: id
                    })

                    console.log(this.tabInfo)

                    this.changeTab(id);
                }

                this.isShowEditDialog = false;
                this.$message.success({ add: '添加成功', copy: '复制成功', rename: '重命名成功' }[this.editCategoryType]);
            },
            showMultiAdd() {
                this.isShowMultiAdd = true;
                this.multiValue = "";
                this.multiAddErrorMsg = "";
            },
            validMultiValue() {
                if (!this.multiValue) {
                    this.multiAddErrorMsg = "请输入需要添加商品的订货号";
                    return false;
                }

                let multiSkuNo = this.multiValue.split('\n');

                if (multiSkuNo.length > 200) {
                    this.multiAddErrorMsg = "单次操作最多支持选择200个订货号";
                    return false;
                }

                let styleRight = true;
                let sameList = [];
                let filterObj = {};
                let seletedArr = [];

                multiSkuNo.forEach(item => {
                    if (!/^[Vv]\d{6}$/.test(item)) {
                        styleRight = false;
                    }

                    let toUpperCaseItem = item.toUpperCase();

                    if (!filterObj[toUpperCaseItem]) {
                        filterObj[toUpperCaseItem] = 1;
                    } else if (sameList.indexOf(toUpperCaseItem) === -1) {
                        sameList.push(toUpperCaseItem)
                    }

                    this.tabInfo.forEach(tab => {
                        if (tab.index === this.currentTabIndex) {
                            tab.productInfoList.forEach(prod => {
                                if (prod.basicInfo.skuNo === toUpperCaseItem && seletedArr.indexOf(toUpperCaseItem) === -1) {
                                    seletedArr.push(toUpperCaseItem);
                                }
                            })
                        }
                    });
                })

                if (!styleRight) {
                    this.multiAddErrorMsg = "输入格式错误，请检查并处理内容格式后重新提交";
                    return false;
                }

                if (sameList.length) {
                    this.multiAddErrorMsg = `订货号“${sameList.join(',')}”存在重复，请检查并处理后重新提交`;
                    return false;
                }

                if (seletedArr.length) {
                    this.multiAddErrorMsg = `订货号“${seletedArr.join(',')}”商品已选择，请检查并处理后重新提交`;
                    return false;
                }

                return true;
            },
            hanlderMultiAddInputBlur() {
                let multiValue = this.multiValue.trim().replace(/[^vV\d]+/g, '');
                multiValue = multiValue.toUpperCase();
                if(multiValue) {
                    let skuIds = multiValue.split('V');
                    let values = [];
                    skuIds.forEach(item => {
                        if(item) {
                            values.push('V' + item);
                        }
                    })
                    this.multiValue = values.join('\n');
                } else {
                    this.multiValue = "";
                }
                
                this.multiAddErrorMsg = "";

                this.validMultiValue();
            },
            multiSkuAddProdConfirm() {
                if (this.validMultiValue()) {
                    let skuNos = this.multiValue.trim().split('\n');
                    this.multiAddProds(skuNos);
                }
            },
            multiAddProds(skuNos, isReplace) {
                VD_UI_GLOBAL.showGlobalLoading();

                axios.post('/sku/scene/getProductInfo.do?skuNos=' + skuNos.join(',')).then(({ data }) => {
                    VD_UI_GLOBAL.hideGlobalLoading();

                    if (data.code === 0) {
                        this.tabInfo.forEach(tab => {
                            if (tab.index === this.currentTabIndex) {
                                if (isReplace == 1) {
                                    tab.productInfoList.forEach((prod, prodIndex) => {
                                        if (prod.basicInfo.skuNo == this.replaceProdSkuNo) {
                                            tab.productInfoList[prodIndex] = data.data[0];
                                        }
                                    })
                                } else {
                                    tab.productInfoList = tab.productInfoList.concat(data.data || []);
                                }

                                if(tab.productInfoList.length > 500) {
                                    tab.productInfoList = tab.productInfoList.splice(0, 500);
                                    this.$message.warn('单个分类下最多添加500个产品')
                                }

                                this.currentProdList = tab.productInfoList;
                                this.$refs.tableList.initListData();
                            }
                        });

                        this.isShowMultiAdd = false;
                    }
                })
            },
            showSelectProd(type) {
                let skuNos = [];
                this.tabInfo.forEach(tab => {
                    if (tab.index === this.currentTabIndex) {
                        tab.productInfoList.forEach(prod => {
                            skuNos.push(prod.basicInfo.skuNo);
                        })
                    }
                });
                this.prodSelectType = type || '';
                this.$refs.selectProd.show({ skuNos, isMulti: type == '1' ? true : false });
            },
            handlerProdSelect(data) {
                if (this.prodSelectType == 1) {
                    this.multiAddProds(data);
                } else {
                    this.multiAddProds(data, 1);
                }
            },
            replaceProd(skuNo) {
                this.replaceProdSkuNo = skuNo;
                this.showSelectProd();
            },
            async submitScene() {
                if (!await this.validSceneName()) {
                    return false;
                }

                if (!this.tabInfo.length) {
                    this.$message.warn('您需要添加分类后进行保存');
                    return;
                }

                let prodFlag = true;

                this.tabInfo.forEach(item => {
                    if (!item.productInfoList.length) {
                        prodFlag = false;
                    }
                })

                if (!prodFlag) {
                    this.$message.warn('场景中有分类未添加产品，请返回页面作调整完成后再提交');
                    return;
                }

                if (!this.cansubmit) {
                    return;
                }
                this.cansubmit = false;

                let skuSceneCategoryDtoList = [];

                this.tabInfo.forEach((item, index) => {
                    let categoryItem = {
                        name: item.name,
                        sceneId: this.id,
                        sort: index + 1,
                        id: item.id || ''
                    }

                    let skuNos = [];

                    item.productInfoList.forEach(prod => {
                        skuNos.push(prod.basicInfo.skuNo);
                    })

                    categoryItem.skuNos = skuNos.join(',');

                    skuSceneCategoryDtoList.push(categoryItem);
                })

                VD_UI_GLOBAL.showGlobalLoading();

                let url = '/sku/scene/create.do';

                if(this.id) {
                    url = '/sku/scene/update.do';
                }

                axios.post(url, {
                    id: this.id,
                    name: this.sceneName,
                    description: this.sceneDescription,
                    skuSceneCategoryDtoList
                }).then(({ data }) => {
                    VD_UI_GLOBAL.hideGlobalLoading();
                    if (data.code === 0) {
                        this.$message.success('保存成功');
                        window.onbeforeunload = null;
                        setTimeout(() => {
                            window.location.href = "/sku/scene/detail.do?id=" + (this.id || data.data.id);
                        })
                    } else {
                        this.cansubmit = true;
                        this.$message.warn(data.message);
                    }
                })
            },
            gotoGoodsDetail(skuNo) {
                VD_UI_GLOBAL.openLink("/goods/vgoods/viewSku.do?skuId=" + skuNo.replace(/[vV]/, ''), {
                    name: '查看SKU'
                })
            },
        }
    })
}.call(this);