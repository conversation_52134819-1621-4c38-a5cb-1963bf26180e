<%@ tag language="java" pageEncoding="UTF-8"%>

<%@attribute name="geActionLogs" type="java.util.List" required="true" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<div class="parts">
    <div class="title-container title-container-green">
        <div class="table-title nobor">操作日志</div>
    </div>
    <table class="table table-bordered table-striped table-condensed table-centered">
        <thead>
            <tr>
                <td class="table-smaller">操作者</td>
                <td class="table-smaller">操作时间</td>
                <td class="table-smaller">操作事项</td>
                <td class="table-smaller">事项详情</td>
            </tr>
        </thead>
        <tbody>
            <c:forEach items="${geActionLogs}" var="geActionLog">
                <tr>
                    <td>${geActionLog.creatorName}</td>
                    <td><fmt:formatDate value="${geActionLog.addTime}" pattern="yyyy-MM-dd HH:mm:ss"/></td>
                    <td>${geActionLog.operation}</td>
                    <td>${geActionLog.content}</td>
                </tr>
            </c:forEach>
        </tbody>
    </table>
</div>