package com.vedeng.erp.mobile.mapper;

import com.vedeng.erp.mobile.domain.Express;
import java.util.List;

import com.vedeng.erp.mobile.dto.SaleOrderExpressInfoDto;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;

@Named("mobileExpressMapper")
public interface ExpressMapper {
    int deleteByPrimaryKey(Integer expressId);

    int insert(Express record);

    int insertOrUpdate(Express record);

    int insertOrUpdateSelective(Express record);

    int insertSelective(Express record);

    Express selectByPrimaryKey(Integer expressId);

    int updateByPrimaryKeySelective(Express record);

    int updateByPrimaryKey(Express record);

    int updateBatch(List<Express> list);

    int updateBatchSelective(List<Express> list);

    int batchInsert(@Param("list") List<Express> list);

    List<SaleOrderExpressInfoDto> getExpressInfoBySaleOrder(@Param("saleOrderId") Integer saleOrderId, @Param("saleOrderGoodsIds") List<Integer> saleOrderGoodsIds);

    List<SaleOrderExpressInfoDto> getDirectExpressInfoBySaleOrder(@Param("saleOrderId") Integer saleOrderId, @Param("saleOrderGoodsIds") List<Integer> saleOrderGoodsIds);

    SaleOrderExpressInfoDto getExpressByExpressIdAndLogisticsNo(@Param("expressId") Integer expressId, @Param("logisticsNo") String logisticsNo);

    List<String> getSaleOrderExpressPhoneByExpressId(Integer expressId);

    List<String> getBuyOrderExpressPhoneByExpressId(Integer expressId);

    List<String> getInvoiceExpressPhoneByExpressId(Integer expressId);

    String getLogisticsCode(String logisticsName);
}