package com.vedeng.erp.buyorder.service.impl;

import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.exception.ShowErrorMsgException;
import com.vedeng.erp.buyorder.domain.entity.GeActionLog;
import com.vedeng.erp.buyorder.domain.entity.GeBusinessChance;
import com.vedeng.erp.buyorder.domain.entity.GeBusinessChanceFeedBack;
import com.vedeng.erp.buyorder.mapper.GeActionLogMapper;
import com.vedeng.erp.buyorder.mapper.GeBusinessChanceFeedBackMapper;
import com.vedeng.erp.buyorder.mapper.GeBusinessChanceMapper;
import com.vedeng.erp.buyorder.service.GeBusinessChanceFeedbackService;
import com.vedeng.erp.buyorder.dto.GeExamineFeedBackDto;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/2/16 13:13
 * @describe
 */
@Service
public class GeBusinessChanceFeedbackServiceImpl implements GeBusinessChanceFeedbackService {

    @Resource
    private GeBusinessChanceFeedBackMapper geBusinessChanceFeedBackMapper;

    @Resource
    private GeBusinessChanceMapper geBusinessChanceMapper;

    @Resource
    private GeActionLogMapper geActionLogMapper;

    @Override
    public GeExamineFeedBackDto queryFednBackInfo(Integer geBussinessChanceId) {

        return geBusinessChanceFeedBackMapper.queryFednBackInfo(geBussinessChanceId);
    }

    @Override
    public void checkGeExamine(GeExamineFeedBackDto geExamineFeedBackDto) throws ShowErrorMsgException {
        if (null == geExamineFeedBackDto) {
            throw new ShowErrorMsgException(CommonConstants.RESULTINFO_CODE_FAIL_1 + "", "请填写GE审核信息！");
        }

        //重复审核
        GeExamineFeedBackDto geExamineFeedBackDto1 = geBusinessChanceFeedBackMapper.queryFednBackInfo(geExamineFeedBackDto.getGeBussinessChanceId());
        if ( !StringUtils.isEmpty(geExamineFeedBackDto1) && (geExamineFeedBackDto1.getStatus()==1 || geExamineFeedBackDto1.getStatus()==2) ) {
            String oldStatusStr="";
            if(geExamineFeedBackDto1.getStatus()==1){
                oldStatusStr="可跟进";
            }else if(geExamineFeedBackDto1.getStatus()==2){
                oldStatusStr="不可跟进";
            }
            throw new ShowErrorMsgException(CommonConstants.RESULTINFO_CODE_FAIL_1 + "", "报单状态已置为"+oldStatusStr);
        }

        //报单状态备注 account名 地址详情 MPC详情 长度不可超过100
        if(geExamineFeedBackDto.getContent().length()>100){
            throw new ShowErrorMsgException(CommonConstants.RESULTINFO_CODE_FAIL_1 + "", "报单状态长度不可超过100");
        }

        if(geExamineFeedBackDto.getAccountName().length()>100){
            throw new ShowErrorMsgException(CommonConstants.RESULTINFO_CODE_FAIL_1 + "", "account名长度不可超过100");
        }

        if(geExamineFeedBackDto.getAccountAddress().length()>100){
            throw new ShowErrorMsgException(CommonConstants.RESULTINFO_CODE_FAIL_1 + "", "地址详情长度不可超过100");
        }

        if(geExamineFeedBackDto.getMpcDetail().length()>100){
            throw new ShowErrorMsgException(CommonConstants.RESULTINFO_CODE_FAIL_1 + "", "MPC详情长度不可超过100");
        }

        if (geExamineFeedBackDto.getStatus()==null) {
            throw new ShowErrorMsgException(CommonConstants.RESULTINFO_CODE_FAIL_1 + "", "请填写&报单状态&");
        }
        if (geExamineFeedBackDto.getStatus()==2) {
            if (StringUtils.isEmpty(geExamineFeedBackDto.getContent())) {
                throw new ShowErrorMsgException(CommonConstants.RESULTINFO_CODE_FAIL_1 + "", "请填写&报单状态备注&");
            }
        }

        if (geExamineFeedBackDto.getIsHavingAccount()==null) {
            throw new ShowErrorMsgException(CommonConstants.RESULTINFO_CODE_FAIL_1 + "", " 请填写&是否有account&");
        }
        if (geExamineFeedBackDto.getIsHavingAccount()==1) {

            if (StringUtils.isEmpty(geExamineFeedBackDto.getAccountName())) {
                throw new ShowErrorMsgException(CommonConstants.RESULTINFO_CODE_FAIL_1 + "", " 请填写&account名&");
            }

            if (geExamineFeedBackDto.getAccountAreaId()==null) {
                throw new ShowErrorMsgException(CommonConstants.RESULTINFO_CODE_FAIL_1 + "", " 请填写&account地址&");

            }
        }

        if( geExamineFeedBackDto.getIsHavingMpc()==null) {
            throw new ShowErrorMsgException(CommonConstants.RESULTINFO_CODE_FAIL_1 + "", " 请填写&是否有MPC&");
        }
        if (geExamineFeedBackDto.getIsHavingMpc()==1) {

            if (StringUtils.isEmpty(geExamineFeedBackDto.getMpcDetail())) {
                throw new ShowErrorMsgException(CommonConstants.RESULTINFO_CODE_FAIL_1 + "", " 请填写&MPC详情&");
            }

        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveGeExamine(GeExamineFeedBackDto geExamineFeedBackDto, User user) {
        // 查反馈表是否有
        GeExamineFeedBackDto isExists = geBusinessChanceFeedBackMapper.queryFednBackInfo(geExamineFeedBackDto.getGeBussinessChanceId());
        if(isExists==null){
            //保存ge反馈 插入一条记录
            GeBusinessChanceFeedBack geBusinessChanceFeedBack1 = new GeBusinessChanceFeedBack();
            geBusinessChanceFeedBack1.setGeBussinessChanceId(geExamineFeedBackDto.getGeBussinessChanceId());
            geBusinessChanceFeedBackMapper.insertSelective(geBusinessChanceFeedBack1);
        }

        //更新 T_GE_BUSINESS_CHANCE_FEEDBACK GE商机审核反馈表
        GeBusinessChanceFeedBack geBusinessChanceFeedBack = new GeBusinessChanceFeedBack();
        BeanUtils.copyProperties(geExamineFeedBackDto,geBusinessChanceFeedBack);
        geBusinessChanceFeedBack.setAddTime(new Date());
        geBusinessChanceFeedBack.setModTime(new Date());
        geBusinessChanceFeedBack.setCreator(user.getUserId());
        geBusinessChanceFeedBack.setUpdater(user.getUserId());
        geBusinessChanceFeedBack.setCreatorName(user.getUsername());
        geBusinessChanceFeedBack.setUpdaterName(user.getUsername());
        geBusinessChanceFeedBackMapper.updateByGeBussinessChanceId(geBusinessChanceFeedBack);
        //更新 T_GE_BUSINESS_CHANCE GE商机主表 Status
        GeBusinessChance geBusinessChance = new GeBusinessChance();
        geBusinessChance.setGeBussinessChanceId(geBusinessChanceFeedBack.getGeBussinessChanceId());
        geBusinessChance.setStatus(geBusinessChanceFeedBack.getStatus());
        geBusinessChance.setAddTime(new Date());
        geBusinessChance.setModTime(new Date());
        geBusinessChance.setCreator(user.getUserId());
        geBusinessChance.setUpdater(user.getUserId());
        geBusinessChance.setCreatorName(user.getUsername());
        geBusinessChance.setUpdaterName(user.getUsername());
        geBusinessChanceMapper.updateStatusByGeBussinessChanceId(geBusinessChance);

        //记录日志
        GeActionLog geActionLog = new GeActionLog();
        geActionLog.setRelatedId(geBusinessChanceFeedBack.getGeBussinessChanceId());
        geActionLog.setRelatedType(1);
        geActionLog.setAddTime(new Date());
        geActionLog.setCreator(user.getUserId());
        geActionLog.setCreatorName(user.getRealName());
        geActionLog.setOperation("GE反馈");

        String statusStr="";
        if(geBusinessChanceFeedBack.getStatus()==1){
            statusStr="可跟进";
        }else if(geBusinessChanceFeedBack.getStatus()==2){
            statusStr="不可跟进("+geBusinessChanceFeedBack.getContent()+")";
        }
        geActionLog.setContent("报单状态："+statusStr);
        geActionLogMapper.insertSelective(geActionLog);


    }
}
