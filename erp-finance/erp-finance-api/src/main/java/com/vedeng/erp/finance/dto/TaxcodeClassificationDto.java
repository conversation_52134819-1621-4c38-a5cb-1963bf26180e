package com.vedeng.erp.finance.dto;

import com.vedeng.common.core.base.BaseDto;
import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.*;

import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TaxcodeClassificationDto extends BaseDto {
    /**
     * Column: TAXCODE_CLASSIFICATION_ID
     * Type: INT UNSIGNED
     * Remark: 主键
     */
    private Integer taxcodeClassificationId;

    /**
     * Column: SUMMARY_CODE
     * Type: VARCHAR(64)
     * Remark: 汇总编码
     */
    private String summaryCode;

    /**
     * Column: GOODS_SERVICES_CLASSIFICATION_ABBREVIATION
     * Type: VARCHAR(64)
     * Remark: 汇总项（商品和服务分类简称)
     */
    private String goodsServicesClassificationAbbreviation;

    /**
     * Column: GOODS_SERVICES_NAME_ABBREVIATION
     * Type: VARCHAR(255)
     * Remark: 汇总项（货物和劳务名称)
     */
    private String goodsServicesNameAbbreviation;

    /**
     * Column: FINAL_CODE
     * Type: VARCHAR(255)
     * Remark: 末级编码
     */
    private String finalCode;

    /**
     * Column: GOODS_SERVICES_NAME
     * Type: VARCHAR(255)
     * Remark: 货物和劳务名称
     */
    private String goodsServicesName;

    /**
     * Column: CLASSIFICATION_ABBREVIATION
     * Type: VARCHAR(64)
     * Remark: 商品和服务分类简称
     */
    private String classificationAbbreviation;

    /**
     * Column: DESCRIPTION
     * Type: VARCHAR(255)
     * Remark: 说明
     */
    private String description;

    /**
     * Column: KEYWORD
     * Type: VARCHAR(255)
     * Remark: 关键字
     */
    private String keyword;

    /**
     * Column: VATRATE
     * Type: VARCHAR(64)
     * Remark: 增值税税率
     */
    private String vatrate;

    /**
     * Column: VAT_SPECIAL_MANAGEMENT
     * Type: VARCHAR(255)
     * Remark: 增值税特殊管理
     */
    private String vatSpecialManagement;

    /**
     * Column: VAT_POLICY_BASIS
     * Type: VARCHAR(255)
     * Remark: 增值税政策依据
     */
    private String vatPolicyBasis;

    /**
     * Column: VAT_SPECIAL_CONTENT_CODE
     * Type: VARCHAR(255)
     * Remark: 增值税特殊内容代码
     */
    private String vatSpecialContentCode;

    /**
     * Column: CONSUMPTION_TAX_MANAGEMENT
     * Type: VARCHAR(255)
     * Remark: 消费税管理
     */
    private String consumptionTaxManagement;

    /**
     * Column: CONSUMPTION_TAX_POLICY_BASIS
     * Type: VARCHAR(255)
     * Remark: 消费税政策依据
     */
    private String consumptionTaxPolicyBasis;

    /**
     * Column: CONSUMPTION_TAX_SPECIAL_CONTENT_CODE
     * Type: VARCHAR(64)
     * Remark: 消费税特殊内容代码
     */
    private String consumptionTaxSpecialContentCode;

    /**
     * Column: IS_SUMMARY_ITEM
     * Type: VARCHAR(16)
     * Default value: N
     * Remark: 是否汇总项: Y N
     */
    private String isSummaryItem;

    /**
     * Column: NATIONAL_INDUSTRY_CODE
     * Type: VARCHAR(255)
     * Remark: 对应统计局编码或国民行业代码
     */
    private String nationalIndustryCode;

    /**
     * Column: EXPORT_COMMODITY_ITEM
     * Type: VARCHAR(255)
     * Remark: 海关进出口商品品目
     */
    private String exportCommodityItem;

    /**
     * Column: START_DATE
     * Type: DATE
     * Remark: 启用时间
     */
    private Date startDate;

    /**
     * Column: END_DATE
     * Type: DATE
     * Remark: 过渡期截止时间
     */
    private Date endDate;

    /**
     * Column: IS_COMMON
     * Type: VARCHAR(32)
     * Default value: N
     * Remark: 是否常用: Y N
     */
    private String isCommon;

    /**
     * Column: IS_DELETE
     * Type: BIT
     * Default value: 0
     * Remark: 是否删除 0否 1是
     */
    private Boolean isDelete;

    /**
     * Column: ADD_TIME
     * Type: DATETIME
     * Default value: CURRENT_TIMESTAMP
     * Remark: 创建时间
     */
    private Date addTime;

    /**
     * Column: MOD_TIME
     * Type: DATETIME
     * Default value: CURRENT_TIMESTAMP
     * Remark: 修改时间
     */
    private Date modTime;

    /**
     * Column: CREATOR
     * Type: INT
     * Default value: 0
     * Remark: 添加人ID
     */
    private Integer creator;

    /**
     * Column: CREATOR_NAME
     * Type: VARCHAR(64)
     * Remark: 添加人名称
     */
    private String creatorName;

    /**
     * Column: UPDATER
     * Type: INT
     * Default value: 0
     * Remark: 更新人ID
     */
    private Integer updater;

    /**
     * Column: UPDATER_NAME
     * Type: VARCHAR(64)
     * Remark: 更新人名称
     */
    private String updaterName;

    /**
     * Column: UPDATE_REMARK
     * Type: VARCHAR(255)
     * Remark: 更新备注
     */
    private String updateRemark;
}