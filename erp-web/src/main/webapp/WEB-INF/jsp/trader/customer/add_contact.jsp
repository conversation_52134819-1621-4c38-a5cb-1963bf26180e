<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html lang="en">
<head>
    <%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
    <%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
    <%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
    <%@ taglib prefix="shiro" uri="http://shiro.apache.org/tags"%>
    <%@ taglib prefix="tags" tagdir="/WEB-INF/tags"%>
    <%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>
    <%@ taglib uri="http://com.vedeng.common.util/definetags" prefix="define"%>
    <%@taglib prefix="sf" uri="http://www.springframework.org/tags/form"%>

    <%@ page trimDirectiveWhitespaces="true" %>

        <%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
%>
    <c:set var="path" value="<%=basePath%>" scope="application" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta http-equiv="Cache-Control" content="no-siteapp" />
    <title>${title}</title>
</head>
<c:set var="title" value="新增联系人" scope="application" />
<%--<%@ include file="../../common/common.jsp"%>--%>

<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css">
<%--<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/general.css?rnd=${resourceVersionKey}" />--%>

<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/manage.css" />
<link rel="stylesheet"
      href="${pageContext.request.contextPath}/static/new/css/pages/firstengage/first/add.css?rnd=${resourceVersionKey}">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/suggestSelect.css?rnd=${resourceVersionKey}">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/trader/add_contact.css?rnd=${resourceVersionKey}">
<script type="text/javascript" src='${pageContext.request.contextPath}/static/js/jquery.min.js'></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/jquery/validation/jquery-form.js"></script>
<script type="text/javascript" src='${pageContext.request.contextPath}/static/libs/jquery/plugins/layer/layer.js'></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/libs/jquery/plugins/DatePicker/WdatePicker.js"></script>
<script type="text/javascript" charset="UTF-8" src='${pageContext.request.contextPath}/static/js/form.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" charset="UTF-8" src='${pageContext.request.contextPath}/static/js/closable-tab.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src='${pageContext.request.contextPath}/static/js/common.js?rnd=${resourceVersionKey}'></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/jquery.validate.js"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/customer/add_contact.js?rnd=${resourceVersionKey}"></script>
<%--<script src="${pageContext.request.contextPath}/static/js/customer/upload.js?rnd=${resourceVersionKey}"></script>--%>
<script src="${pageContext.request.contextPath}/static/js/customer/upload.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
<style>
    .vd-upload-wrap.vd-upload-file-wrap .vd-upload-item {
        background: white;
    }
    /*i {*/
    /*    display: inline-block;*/
    /*    height: 12px;*/
    /*    margin-bottom: -2px;*/
    /*    background: white;*/
    /*}*/
    .position{
        width: 600px;
    }
    .position label{
        width: 100px;
        padding: 1px 10px;
    }
</style>
<body>
    <div class="formpublic">
        <form method="post" action="" id="myform">
        	
            <ul>
                <li>
                    <div class="infor_name">
                        <span>*</span>
                        <lable>姓名</lable>
                    </div>
                    <div class="f_left">
                        <input type="text" class="input-largest" name="name" id="name" value="${traderContact.name}"/>
                    </div>
                </li>
                <li>
                    <div class="infor_name mt0">
                        <lable>性别</lable>
                    </div>
                    <div class="f_left inputfloat">
                        <input type="radio" name="sex" value="1" <c:if test="${traderContact.sex eq 1}">checked="checked"</c:if>/>
                        <label class="mr8">男</label>
                        <input type="radio" name="sex" value="0" <c:if test="${traderContact.sex eq 0}">checked="checked"</c:if>>
                        <label class="mr8">女</label>
                        <input type="radio" name="sex" value="2" <c:if test="${traderContact.sex eq 2}">checked="checked"</c:if>>
                        <label>未知</label>
                    </div>
                </li>
                <%--<li>--%>
                    <%--<div class="infor_name">--%>
                        <%--<lable>部门</lable>--%>
                    <%--</div>--%>
                    <%--<div class="f_left commonuse table-largest" id="dept">--%>
                    	<%--<div>--%>
	                        <%--<input type="text" name="department" id="department" value="${traderContact.department}" class="input-largest" placeholder="您可以点击常用部门进行选择"/>--%>
	                        <%--<label>常用部门:</label>--%>
	                        <%--<span>采购部</span>--%>
	                        <%--<span>销售部</span>--%>
	                        <%--<span>生产部</span>--%>
	                        <%--<span>财务部</span>--%>
	                        <%--<span>仓储部</span>--%>
	                        <%--<span>研发部</span>--%>
	                        <%--<span>商务部</span>--%>
	                        <%--<span>研究院</span>--%>
                        <%--</div>--%>
                        <%--<div id="departmentError"></div>--%>
                    <%--</div>--%>
                <%--</li>--%>
<c:if test="${traderCustomer.customerNature == 465 || traderCustomer.customerNature == 466}">
                <li>
                    <div class="infor_name">
                        <label><span style="color: red;margin-right: 5px">*</span>职位</label>
                    </div>
                    <div class="f_left commonuse table-largest">
                        <div class="position">
                            <!-- 复选框选项 -->
                        <c:if test="${traderCustomer.customerNature == 465}">
                            <label>
                                <input type="checkbox" name="position" value="老板" /> 老板
                            </label>
                            <label>
                                <input type="checkbox" name="position" value="销售负责人" /> 销售负责人
                            </label>
                            <label>
                                <input type="checkbox" name="position" value="销售经理" /> 销售经理
                            </label>
                            <label>
                                <input type="checkbox" name="position" value="采购负责人" /> 采购负责人
                            </label>
                            <label>
                                <input type="checkbox" name="position" value="采购经理" /> 采购经理
                            </label>
                            <label>
                                <input type="checkbox" name="position" value="商务人员" /> 商务人员
                            </label>
                            <label>
                                <input type="checkbox" name="position" value="财务人员" /> 财务人员
                            </label>
                            <label>
                                <input type="checkbox" name="position" value="物流人员" /> 物流人员
                            </label>
                            <label>
                                <input type="checkbox" name="position" value="售后人员" /> 售后人员
                            </label>
                            <label>
                                <input type="checkbox" name="position" value="临床工程师" /> 临床工程师
                            </label>
                        </c:if>
                        <c:if test="${traderCustomer.customerNature == 466}">
                            <label>
                                <input type="checkbox" name="position" value="院长" /> 院长
                            </label>
                            <label>
                                <input type="checkbox" name="position" value="设备科人员" /> 设备科人员
                            </label>
                            <label>
                                <input type="checkbox" name="position" value="临床医生" /> 临床医生
                            </label>
                            <label>
                                <input type="checkbox" name="position" value="采购负责人" /> 采购负责人
                            </label>
                            <label>
                                <input type="checkbox" name="position" value="采购经理" /> 采购经理
                            </label>
                            <label>
                                <input type="checkbox" name="position" value="运营人员" /> 运营人员
                            </label>
                            <label>
                                <input type="checkbox" name="position" value="财务人员" /> 财务人员
                            </label>
                            <label>
                                <input type="checkbox" name="position" value="医院库管" /> 医院库管
                            </label>
                        </c:if>
                            <label>
                                <input type="checkbox" name="position" value="其他" id="otherCheckbox" /> 其他
                            </label>
                            <!-- 其他职位的输入框 -->
                            <input type="text" name="otherPosition" id="otherPosition" class="input-largest" placeholder="请填写具体职位，最多50字" maxlength="50" style="display: none;" />
                        </div>
                        <div id="positionError" style="color: red;"></div> <!-- 错误提示 -->
                    </div>
                </li>
</c:if>
                <li>
                    <div class="infor_name">
                        <lable>电话</lable>
                    </div>
                    <div class="f_left">
                        <input type="text" class="input-largest errobor" name="telephone" id="telephone" value="${traderContact.telephone}"/>
                        <input type="hidden" name="formToken" value="${formToken}"/>
                    </div>
                </li>
                <li>
                    <div class="infor_name">
                        <span>*</span>
                        <lable>手机</lable>
                    </div>
                    <div class="f_left">
                        <input type="text" class="input-largest errobor" name="mobile" id="mobile" value="${traderContact.mobile}"/>
                        
                    </div>
                </li>
                <li>
                    <div class="infor_name">
                        <lable>手机2</lable>
                    </div>
                    <div class="f_left">
                        <input type="text" class="input-largest errobor"  name="mobile2" id="mobile2" value="${traderContact.mobile2}"/>
                        
                    </div>
                </li>
                <li>
                    <div class="infor_name">
                        <lable>名片</lable>
                    </div>
                    <div class="f_left">
                        <input type="hidden" class="J-upload-data" value='${command.skuCheckFilesJson}'>
                        <div class="J-upload" type="businessCards"></div>
                        <div class="feedback-block J-upload-error">
                            <label  class="error" style="display: none;"></label>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="infor_name">
                        <lable>邮箱</lable>
                    </div>
                    <div class="f_left">
                        <input type="text" class="input-largest errobor"  name="email" id="email" value="${traderContact.email}"/>
                        
                    </div>
                </li>
                 
                  <li>
                    <div class="infor_name">
                        <lable>QQ</lable>
                    </div>
                    <div class="f_left">
                        <input type="text" class="input-largest errobor"  name="qq" id="qq" value="${traderContact.qq}" maxlength="16"/>
                    </div>
                </li>
                 <li>
                    <div class="infor_name">
                        <lable>备注</lable>
                    </div>
                    <div class="f_left">
                        <textarea class="input-largest textarea-smallest" name="comments" id="comments">${traderContact.comments}</textarea> 
                    </div>
                </li>
            </ul>
            <div class="add-tijiao tcenter">
                <button type="submit">提交</button>
                <button type="button" class="dele" id="close-layer">取消</button>
            </div>

            <input type="hidden" name="traderId" value="${traderCustomer.traderId}">
            <input type="hidden" name="traderCustomerId" value="${traderCustomer.traderCustomerId}">
            <input type="hidden" id="pageType" value="${pageType}">
        	<input type="hidden" name="traderType" value="1">
        	<input type="hidden" id="add" name="op" value="add"/>
        </form>
    </div>
</body>

</html>
