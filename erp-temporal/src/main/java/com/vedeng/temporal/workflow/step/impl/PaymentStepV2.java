package com.vedeng.temporal.workflow.step.impl;

import com.vedeng.temporal.context.CompanyExecutionContext;
import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import com.vedeng.temporal.domain.dto.FlowOrderInfoUpdateRequest;
import com.vedeng.temporal.polling.universal.enums.DataSourceType;
import com.vedeng.temporal.polling.universal.request.UniversalPollingRequest;
import com.vedeng.temporal.polling.universal.result.UniversalPollingResult;
import com.vedeng.temporal.polling.universal.workflow.UniversalPollingWorkflow;
import com.vedeng.temporal.enums.BusinessStepType;
import com.vedeng.temporal.exception.BusinessProcessException;
import com.vedeng.temporal.workflow.activity.PaymentActivity;
import com.vedeng.temporal.workflow.step.BusinessStep;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 付款步骤 V2 - 新架构版本
 * 
 * 架构优化说明：
 * - 使用新的 PaymentActivity 替代直接的 CompanyBusinessActivity 调用
 * - 每个业务操作都是独立的 Activity 方法
 * - 通过 CompanyBusinessRequest.businessData 传递数据
 * - 支持数据串联：查询付款状态 → 创建付款申请 → 状态确认
 * - 异常处理和重试由 Temporal 和 UniversalBusinessTemplate 统一管理
 * 
 * 业务流程：
 * 1. 查询采购单号和销售单号并存储到扩展属性
 * 2. 等待销售单结款完成
 * 3. 创建付款申请，获取付款单ID
 * 
 * 功能特点：
 * - 简化的付款流程，专注于核心业务逻辑
 * - 保持与其他StepV2的架构一致性
 * - 优化的轮询机制和前置条件等待逻辑
 * 
 * 错误处理：
 * - Activity层：技术异常自动重试（网络、超时等）
 * - Step层：业务异常处理（数据校验、业务规则等）
 * - 完整的状态追踪和日志记录
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 2.0 (新架构版本)
 * @since 2025-01-21
 */
@Slf4j
public class PaymentStepV2 implements BusinessStep {
    
    private final PaymentActivity paymentActivity;
    
    public PaymentStepV2(PaymentActivity paymentActivity) {
        this.paymentActivity = paymentActivity;
    }
    
    @Override
    public CompanyBusinessResponse execute(CompanyBusinessRequest request, CompanyExecutionContext context) {
        log.info("开始执行付款步骤V2，业务ID: {}, 目标公司: {}", 
                request.getBusinessId(), request.getTargetCompanyCode());
        String currentCompany = context.getCurrentCompany();
        String nextCompany = context.getNextCompany();
        
        // 使用不可变对象创建新的请求副本，避免状态突变问题
        CompanyBusinessRequest processRequest = request.withSourceAndTargetCompany(
            currentCompany, 
            context.getNextCompany()
        );
        if (context.isLast()){
            log.info("执行最后一家公司：{}，无需处理下一家付款单", currentCompany);
            return CompanyBusinessResponse.success("下一家公司为空无需处理，跳过执行", request.getBusinessId());
        }
        
        log.info("执行付款步骤，当前公司: {}, 下一公司: {}", currentCompany, nextCompany);

        // 第1步：查询采购单号
        awaitPurchaseOrderQueryCompletion(nextCompany, processRequest, "buyOrderNo");

        // 第2步：查询销售单号
        awaitSaleOrderQueryCompletion(nextCompany, processRequest, "saleOrderNo");

        // 第3步：等待销售单结款完成
        log.info("等待销售单结款，公司: {}", nextCompany);
        waitForSaleOrderPayment(nextCompany, processRequest);
        log.info("等待销售单结款已完成，公司: {}", nextCompany);

        // 第4步：创建付款申请
        CompanyBusinessResponse createResult = paymentActivity.createPayment(processRequest);
        
        return createResult;
    }

    /**
     * 等待采购单号查询完成
     */
    private void awaitPurchaseOrderQueryCompletion(String companyCode, CompanyBusinessRequest request, String aliasName) {
        log.info("开始轮询等待采购单号，公司: {}, 业务ID: {}", companyCode, request.getBusinessId());
        String businessId = request.getBusinessId();

        try {
            // 构建查询参数
            Map<String, Object> queryParameters = new HashMap<>();
            queryParameters.put("queryTypes", Arrays.asList("BUY_ORDER"));
            queryParameters.put("currentCompany", companyCode);

            // 构建统一轮询请求
            UniversalPollingRequest pollingRequest = UniversalPollingRequest.builder()
                    .businessId(businessId)
                    .businessType(getStepType().getCode())
                    .dataSourceType(DataSourceType.LOCAL_DATABASE)
                    .companyCode(companyCode)
                    .queryType("TEMPORAL_FLOW_ORDER_QUERY")
                    .queryParameters(queryParameters)
                    .completionCheckConfig("buyOrderNo:isNotBlank")
                    .build();

            // 执行统一轮询
            UniversalPollingResult<Map<String, Object>> result = UniversalPollingWorkflow.universalPoll(pollingRequest);

            // 轮询成功后，需要手动获取最终数据并设置到扩展属性
            if (result.isSuccess()) {
                Map<String, Object> data = result.getData();
                String buyOrderNo = (String) data.get("buyOrderNo");
                // 存储到扩展属性
                Map<String, Object> extendedProperties = request.getExtendedProperties();
                if (extendedProperties == null) {
                    extendedProperties = new HashMap<>();
                }
                extendedProperties.put(aliasName, buyOrderNo);
                request.setExtendedProperties(extendedProperties);

                log.info("采购单号轮询完成，公司: {}, 业务ID: {}, 采购单号: {}",
                        companyCode, businessId, buyOrderNo);
            } else {
                String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
                throw BusinessProcessException.retryable("采购单号轮询未完成", "POLLING_INCOMPLETE", context);
            }

        } catch (BusinessProcessException e) {
            // 重新抛出业务流程异常
            throw e;
        } catch (Exception e) {
            log.error("采购单号轮询异常，公司: {}, 业务ID: {}", companyCode, businessId, e);
            String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
            throw BusinessProcessException.retryable("采购单号轮询系统异常", "POLLING_ERROR", context);
        }
    }

    /**
     * 等待销售单号查询完成
     */
    private void awaitSaleOrderQueryCompletion(String companyCode, CompanyBusinessRequest request, String aliasName) {
        log.info("开始轮询等待销售单号，公司: {}, 业务ID: {}", companyCode, request.getBusinessId());
        String businessId = request.getBusinessId();

        try {
            // 构建查询参数
            Map<String, Object> queryParameters = new HashMap<>();
            queryParameters.put("queryTypes", Arrays.asList("SALE_ORDER"));
            queryParameters.put("currentCompany", companyCode);

            // 构建统一轮询请求
            UniversalPollingRequest pollingRequest = UniversalPollingRequest.builder()
                    .businessId(businessId)
                    .businessType(getStepType().getCode())
                    .dataSourceType(DataSourceType.LOCAL_DATABASE)
                    .companyCode(companyCode)
                    .queryType("TEMPORAL_FLOW_ORDER_QUERY")
                    .queryParameters(queryParameters)
                    .completionCheckConfig("saleOrderNo:isNotBlank")
                    .build();

            // 执行统一轮询
            UniversalPollingResult<Map<String, Object>> result = UniversalPollingWorkflow.universalPoll(pollingRequest);

            // 轮询成功后，需要手动获取最终数据并设置到扩展属性
            if (result.isSuccess()) {
                Map<String, Object> data = result.getData();
                Object saleOrderNo = data.get("saleOrderNo");
                // 存储到扩展属性
                Map<String, Object> extendedProperties = request.getExtendedProperties();
                if (extendedProperties == null) {
                    extendedProperties = new HashMap<>();
                }
                extendedProperties.put(aliasName, saleOrderNo);
                request.setExtendedProperties(extendedProperties);

                log.info("销售单号轮询完成，公司: {}, 业务ID: {}, 销售单号: {}", companyCode, businessId, saleOrderNo);
            } else {
                String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
                throw BusinessProcessException.retryable("销售单号轮询未完成", "POLLING_INCOMPLETE", context);
            }

        } catch (BusinessProcessException e) {
            // 重新抛出业务流程异常
            throw e;
        } catch (Exception e) {
            log.error("销售单号轮询异常，公司: {}, 业务ID: {}", companyCode, businessId, e);
            String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
            throw BusinessProcessException.retryable("销售单号轮询系统异常", "POLLING_ERROR", context);
        }
    }
    
    @Override
    public String getStepName() {
        return "付款步骤V2";
    }

    @Override
    public BusinessStepType getStepType() {
        return BusinessStepType.PAYMENT_ORDER;
    }

    @Override
    public String getStepDescription() {
        return "执行付款完整流程：查询单号 → 等待结款 → 创建付款申请";
    }
    


    /**
     * 等待销售单结款完成
     */
    private void waitForSaleOrderPayment(String companyCode, CompanyBusinessRequest request) {
        log.info("开始等待销售单结款完成，公司: {}, 业务ID: {}", companyCode, request.getBusinessId());
        String businessId = request.getBusinessId();

        try {
            // 构建统一轮询请求
            UniversalPollingRequest pollingRequest = UniversalPollingRequest.builder()
                    .businessId(businessId)
                    .businessType(BusinessStepType.PAYMENT_ORDER.getCode())
                    .dataSourceType(DataSourceType.REMOTE_API)
                    .companyCode(companyCode)
                    .apiPath("/api/v1/pay/check.do")
                    .apiParameters(request.getExtendedProperties())
                    // 销售订单完成条件：全部付款（paymentStatus = 2）
                    .completionCheckConfig("data.paymentStatus:2")
                    .build();

            log.info("使用销售订单结款完成检查器，公司: {}", companyCode);
            
            // 执行统一轮询等待
            UniversalPollingResult<Map<String, Object>> result = UniversalPollingWorkflow.universalPoll(pollingRequest);

            // 验证最终状态
            if (!result.isSuccess()) {
                String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
                throw BusinessProcessException.retryable("销售订单付款轮询失败", "POLLING_INCOMPLETE", context);
            }

            log.info("销售订单付款完成，公司: {}, 业务ID: {}, 最终状态: {}", companyCode, businessId, result.isSuccess());

        } catch (BusinessProcessException e) {
            // 重新抛出业务流程异常
            throw e;
        } catch (Exception e) {
            log.error("等待销售订单付款异常，公司: {}, 业务ID: {}", companyCode, businessId, e);
            String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
            throw BusinessProcessException.retryable("销售订单付款系统异常", "POLLING_ERROR", context);
        }
    }

}
