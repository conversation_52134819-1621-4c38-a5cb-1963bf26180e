package com.vedeng.erp.trader.dto;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Data;

@Data
public class CommunicateVoiceTaskDto extends BaseEntity {
    /**
     * 主键
     */
    private Long id;

    /**
     * T_COMMUNICATE_RECORD表的ID
     */
    private Integer communicateRecordId;

    /**
     * 录音时长
     */
    private Integer coidLength;

    /**
     * 录音地址
     */
    private String coidUri;

    /**
     * 录音状态（0初始|1上传完成（转写中）|2上传失败（需重新上传）|3讯飞转写失败|4讯飞转写成功|5本地提取可读文本失败|6本地提取可读文本成功|8GPT获取标签失败)|9GPT获取标签成功|10查询讯飞转写结果报错|11查询讯飞转写结果成功）
     */
    private String voiceStatus;

    /**
     * 转写中的状态获取url
     */
    private String voiceStatusRequestUri;

    /**
     * 上传完成时间
     */
    private Long uploadTimestamp;

    /**
     * 转写完成时间
     */
    private Long voiceTimestamp;

    /**
     * 获取标签成功时间
     */
    private Long gptTimestamp;

    /**
     * 备注
     */
    private String remark;

    /**
     * 原始返回报文
     */
    private String voiceTextOrder;

    /**
     * 解析后的报文
     */
    private String voiceTextParse;

    /**
     * 讯飞转写订单Id
     */
    private String orderId;

    private Long promptTokens;

    private Long completionTokens;

    private Long totalTokens;

    /**
     * 场景
     */
    private String senceCode;

    /**
     * GPT版本
     */
    private String gptVersion;
}
