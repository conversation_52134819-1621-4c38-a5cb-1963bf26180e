package com.wms.service.input;

import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.util.StringUtil;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Buyorder;
import com.wms.constant.WMSContant;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.dto.PutPurchaseOrderDto;
import com.wms.dto.PutPurchaseOrderGoodsDto;
import com.wms.dto.WmsRequest;
import com.wms.service.AbstractWmsInterface;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


/**
 * WMS采购入库单接口
 */
@Service
public class PutPurchaseOrderInterface extends AbstractWmsInterface {

    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private BuyorderMapper buyorderMapper;
    @Override
    protected void setMethodAndMessageId(WmsRequest wmsRequest) {
        wmsRequest.setMessageId(WMSContant.PUT_PURCHASE_ORDER);
        wmsRequest.setMethod(WMSContant.PUT_PURCHASE_ORDER);
    }

    /**
     * 获取请求参数
     * @return
     */
    @Override
    protected JSONObject getXmlDate(Object ... param) {

        PutPurchaseOrderDto putPurchaseOrderDto = (PutPurchaseOrderDto)param[0];

        JSONArray headArray = new JSONArray();

        JSONObject headItem = new JSONObject();
        headItem.put("customerId","VEDENG");
        String warehouseId = putPurchaseOrderDto.getWarehouseId();
        if(StringUtil.isBlank(warehouseId)){
            warehouseId = "NJ01";
        }
        headItem.put("warehouseId",warehouseId);

        String poType = putPurchaseOrderDto.getPoType();
        String docNo = putPurchaseOrderDto.getDocNo();

        boolean vpbuyOrder = false;
        //如果是采购单下发 且是VP的类型
        Buyorder buyorder = buyorderMapper.getBuyOrderByOrderNo(docNo);
        if(WmsInterfaceOrderType.INPUT_PURCHASE.equals(poType) && buyorder != null && ErpConst.ZERO.equals(buyorder.getOrderType())){
            vpbuyOrder  = true;
        }

        headItem.put("poType",poType);
        headItem.put("docNo", docNo);
        headItem.put("poCreationTime", putPurchaseOrderDto.getPoCreationTime());
        headItem.put("expectedArriveTime1",putPurchaseOrderDto.getExpectedArriveTime1());
        headItem.put("expectedArriveTime2",putPurchaseOrderDto.getExpectedArriveTime2());
        headItem.put("supplierId",putPurchaseOrderDto.getSupplierId());
        headItem.put("supplierName",putPurchaseOrderDto.getSupplierName());
        headItem.put("poReferenceA", StringUtil.isEmpty(putPurchaseOrderDto.getPoReferenceA()) ? "/" :putPurchaseOrderDto.getPoReferenceA());
        headItem.put("poReferenceB",putPurchaseOrderDto.getPoReferenceB());
        headItem.put("poReferenceC",putPurchaseOrderDto.getPoReferenceC());
        headItem.put("notes",putPurchaseOrderDto.getNotes());
        headItem.put("hedi01",putPurchaseOrderDto.getHedi01());

        JSONArray detailArray = new JSONArray();

        List<PutPurchaseOrderGoodsDto> goodsList = putPurchaseOrderDto.getDetails();

        for(PutPurchaseOrderGoodsDto putPurchaseOrderGoodsDto : goodsList) {
            JSONObject detailItem = new JSONObject();
            detailItem.put("customerId","VEDENG");
            detailItem.put("lineNo",putPurchaseOrderGoodsDto.getLineNo());
            detailItem.put("sku",putPurchaseOrderGoodsDto.getSku());
            detailItem.put("orderedQty",putPurchaseOrderGoodsDto.getOrderedQty());
            detailItem.put("lotAtt01",putPurchaseOrderGoodsDto.getLotAtt01());
            detailItem.put("lotAtt02",putPurchaseOrderGoodsDto.getLotAtt02());
            detailItem.put("lotAtt03",putPurchaseOrderGoodsDto.getLotAtt03());
            detailItem.put("lotAtt04",putPurchaseOrderGoodsDto.getLotAtt04());
            detailItem.put("lotAtt05",putPurchaseOrderGoodsDto.getLotAtt05());
            detailItem.put("lotAtt06",putPurchaseOrderGoodsDto.getLotAtt06());

            if(vpbuyOrder){
                List<String> saleOrderNoList = saleorderMapper.findRelateSaleOrderNo(Integer.valueOf(putPurchaseOrderGoodsDto.getDedi04()));
                detailItem.put("dedi05", CollectionUtils.isEmpty(saleOrderNoList)? "": StringUtils.join(saleOrderNoList.toArray(), ","));
            }

            detailItem.put("dedi04",putPurchaseOrderGoodsDto.getDedi04());
            detailItem.put("lotAtt07", StringUtils.isNotBlank(putPurchaseOrderGoodsDto.getLotAtt07()) ? putPurchaseOrderGoodsDto.getLotAtt07() : "*");
            detailItem.put("lotAtt08",putPurchaseOrderGoodsDto.getLotAtt08());
            detailItem.put("lotAtt12",putPurchaseOrderGoodsDto.getLotAtt012());
            detailArray.add(detailItem);
        };

        headItem.put("details",detailArray);
        headArray.add(headItem);

        JSONObject headObject = new JSONObject();
        headObject.put("header",headArray);
        return headObject;
    }

    /**
     * 解析响应数据，如果有的话
     * @param returnObject
     * @return
     */
    @Override
    protected String parseResponseDate(JSONObject returnObject) {
        return null;
    }
}
