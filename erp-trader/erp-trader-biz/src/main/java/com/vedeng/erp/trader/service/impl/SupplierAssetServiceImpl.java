package com.vedeng.erp.trader.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.enums.SupplierAssetEnum;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.trader.common.annotation.SupplierAssetLogAnnotation;
import com.vedeng.erp.trader.constant.TraderConstant;
import com.vedeng.erp.trader.domain.dto.SupplierAssetDto;
import com.vedeng.erp.trader.domain.entity.SupplierAssetEntity;
import com.vedeng.erp.trader.dto.SupplierAssetChangeDto;
import com.vedeng.erp.trader.dto.TraderSupplierDto;
import com.vedeng.erp.trader.mapper.SupplierAssetMapper;
import com.vedeng.erp.trader.mapstruct.SupplierAssetConvertor;
import com.vedeng.erp.trader.service.SupplierAssetService;
import com.vedeng.erp.trader.service.TraderSupplierApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import springfox.documentation.spring.web.json.Json;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 资产
 * @date 2023/11/22 16:59
 */
@Service
@Slf4j
public class SupplierAssetServiceImpl implements SupplierAssetService {

    @Autowired
    private SupplierAssetMapper supplierAssetMapper;
    @Autowired
    private SupplierAssetConvertor supplierAssetConvertor;

    @Autowired
    private TraderSupplierApiService traderSupplierApiService;

    @Override
    public SupplierAssetDto getSupplierAsset(Integer traderSupplierId,Integer assetType) {
        SupplierAssetEntity entity = supplierAssetMapper.findByTraderSupplierIdAndAssetType(traderSupplierId,assetType);
        return supplierAssetConvertor.toDto(entity);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    @SupplierAssetLogAnnotation(type = TraderConstant.OPER_ADD)
    public void add(SupplierAssetChangeDto supplierAssetChangeDto) {
        log.info("供应商资产增加：{}", JSON.toJSONString(supplierAssetChangeDto));
        SupplierAssetDto supplierAsset = getSupplierAsset(supplierAssetChangeDto.getTraderSupplierId(),
                supplierAssetChangeDto.getSupplierAsset().getCode());
        if (supplierAsset == null) {
            SupplierAssetEntity entity = new SupplierAssetEntity();
            entity.setTraderId(supplierAssetChangeDto.getTraderId());
            entity.setTraderSupplierId(supplierAssetChangeDto.getTraderSupplierId());
            entity.setAssetType(supplierAssetChangeDto.getSupplierAsset().getCode());
            // 账期 余额 初始值
            switch (supplierAssetChangeDto.getSupplierAsset()) {
                case balance:
                    TraderSupplierDto traderSupplierByTraderId = traderSupplierApiService.getTraderSupplierByTraderId(supplierAssetChangeDto.getTraderId());
                    TraderSupplierDto traderSupplierDto = Optional.ofNullable(traderSupplierByTraderId).orElse(new TraderSupplierDto());
                    entity.setAsset(Optional.ofNullable(traderSupplierDto.getAmount()).orElse(BigDecimal.ZERO));
                    break;
                case rebate:
                    entity.setApplyAsset(supplierAssetChangeDto.getQuantity());
                    entity.setAsset(supplierAssetChangeDto.getQuantity());
                    break;
                default:
            }
            supplierAssetMapper.insertSelective(entity);
        } else {
            supplierAsset.setAsset(supplierAsset.getAsset().add(supplierAssetChangeDto.getQuantity()));
            supplierAsset.setApplyAsset(supplierAsset.getApplyAsset().add(supplierAssetChangeDto.getQuantity()));
            supplierAssetMapper.updateByPrimaryKeySelective(supplierAssetConvertor.toEntity(supplierAsset));
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    @SupplierAssetLogAnnotation(type = TraderConstant.OPER_UNCHANGING)
    public void occupy(SupplierAssetChangeDto supplierAssetChangeDto) {
        log.info("供应商资产占用：{}", JSON.toJSONString(supplierAssetChangeDto));
        SupplierAssetDto supplierAsset = getSupplierAsset(supplierAssetChangeDto.getTraderSupplierId(),
                supplierAssetChangeDto.getSupplierAsset().getCode());

        if (supplierAsset == null) {
            throw new ServiceException("无可用资产");
        }

        supplierAsset.setApplyAsset(supplierAsset.getApplyAsset().subtract(supplierAssetChangeDto.getQuantity()));
        supplierAsset.setOccupyAsset(supplierAsset.getOccupyAsset().add(supplierAssetChangeDto.getQuantity()));
        supplierAssetMapper.updateByPrimaryKeySelective(supplierAssetConvertor.toEntity(supplierAsset));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    @SupplierAssetLogAnnotation(type = TraderConstant.OPER_UNCHANGING)
    public void relieveOccupy(SupplierAssetChangeDto supplierAssetChangeDto) {
        log.info("供应商资产释放：{}", JSON.toJSONString(supplierAssetChangeDto));
        SupplierAssetDto supplierAsset = getSupplierAsset(supplierAssetChangeDto.getTraderSupplierId(),
                supplierAssetChangeDto.getSupplierAsset().getCode());

        if (supplierAsset == null) {
            throw new ServiceException("无可用资产");
        }
        supplierAsset.setApplyAsset(supplierAsset.getApplyAsset().add(supplierAssetChangeDto.getQuantity()));
        supplierAsset.setOccupyAsset(supplierAsset.getOccupyAsset().subtract(supplierAssetChangeDto.getQuantity()));
        supplierAssetMapper.updateByPrimaryKeySelective(supplierAssetConvertor.toEntity(supplierAsset));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    @SupplierAssetLogAnnotation(type = TraderConstant.OPER_LESSEN)
    public void sub(SupplierAssetChangeDto supplierAssetChangeDto) {
        log.info("供应商资产减少：{}", JSON.toJSONString(supplierAssetChangeDto));
        SupplierAssetDto supplierAsset = getSupplierAsset(supplierAssetChangeDto.getTraderSupplierId(),
                supplierAssetChangeDto.getSupplierAsset().getCode());

        if (supplierAsset == null) {
            throw new ServiceException("无可用资产");
        }

        if (supplierAsset.getApplyAsset().compareTo(supplierAssetChangeDto.getQuantity()) < 0) {
            throw new ServiceException("资产不足");
        }
        supplierAsset.setApplyAsset(supplierAsset.getApplyAsset().subtract(supplierAssetChangeDto.getQuantity()));
        supplierAsset.setAsset(supplierAsset.getAsset().subtract(supplierAssetChangeDto.getQuantity()));
        supplierAssetMapper.updateByPrimaryKeySelective(supplierAssetConvertor.toEntity(supplierAsset));
    }

}
