package com.vedeng.workbench.model.vo.base;

import com.vedeng.workbench.model.dto.BussinessCommunicateRecordDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 工作台通用商机信息对象.
 * @jira: VDERP-3073【商机管理中心】ERP销售工作台.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/10/25 7:52 下午.
 * @author: Tomcat.Hui.
 */
@Data
public class WorkbenchChanceInfo {

    /**
     * 商机ID
     */
    private Integer bussinessChanceId;

    /**
     * 商机编号
     */
    private String bussinessChanceNo;

    /**
     * 客户ID
     */
    private Integer customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 归属销售
     */
    private WorkbenchUserInfo belongUser;

    /**
     * 产品名称
     */
    private String goodsName;

    /**
     * 询价产品
     */
    private String quoteGoods;

    /**
     * 商机类型
     */

    private String bussinessType;

    /**
     * 商机等级
     */
    private Integer bussinessLevel;

    /**
     * 商机状态
     */
    private Integer bussinessStatus;

    /**
     * 商机来源
     */
    private Integer bussinessSource;


    /**
     * 商机金额
     */
    private BigDecimal bussinessAmount;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 最后一次沟通时间
     */
    private Long lastCommunicateTime;

    /*
     * 下一次沟通内容
     */
    private String nextContactContent;

//    /**
//     * 商机关闭时间
//     */
//    private Timestamp closeTime;

//    /**
//     * 预计成单时间
//     */
//    private Long expectedOrderTime;

    /**
     * 联系人姓名
     */
    private String ContactName;

    /**
     * 联系人号码
     */
    private String ContactNum;


    /**
     * 沟通记录
     */
    private List<BussinessCommunicateRecordDto> bussinessCommunicateRecordDtoList;

}
