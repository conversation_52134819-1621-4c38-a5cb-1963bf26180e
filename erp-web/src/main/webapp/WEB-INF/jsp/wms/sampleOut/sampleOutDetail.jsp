<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="样品申请详情页" scope="application"/>
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>
<%@ include file="../../common/common.jsp" %>
<script type="text/javascript">

    function closeLendout() {
        checkLogin();
        index = layer.confirm("您是否确认关闭该样品申请单？", {
            btn: ['确定', '取消'] //按钮
        }, function () {

            $.ajax({
                url: page_url + '/wms/commodityLendOut/closeOrder.do',
                data: {"lendOutId": "${outputOrder.id}"},
                type: "POST",
                dataType: "json",
                async: false,
                success: function (data) {
                    debugger;
                    if (data.code == 0) {
                        window.location.reload();
                    } else {
                        layer.alert(data.message);
                    }
                },
                error: function (data) {
                    if (data.status == 1001) {
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });

            layer.close(index);

        }, function () {
        });
    }

    function printSHOutOrder(id, orderId) {
        $("#expressId_xs").val(id);
        $("#searchSh").submit();
    }
</script>

<div class="content">
    <div class="formtitle"></div>
    <div id="desc_div">
        <ul class="payplan">

            <div class="parts">

                <div class="title-container title-container-blue">
                    <div class="table-title nobor">
                        基本信息
                    </div>
                </div>

                <table class="table table-bordered table-striped table-condensed table-centered">
                    <tbody>
                    <tr>
                        <td class="table-smaller">样品申请单号</td>
                        <td>${outputOrder.orderNo}</td>
                        <td class="table-smaller">审核状态</td>
                        <td>
                            <c:choose>
                                <c:when test="${outputOrder.verifyStatus eq 0}">
                                    待审核
                                </c:when>
                                <c:when test="${outputOrder.verifyStatus eq 1}">
                                    审核中
                                </c:when>
                                <c:when test="${outputOrder.verifyStatus eq 2}">
                                    审核通过
                                </c:when>
                                <c:when test="${outputOrder.verifyStatus eq 3}">
                                    审核不通过
                                </c:when>
                                <c:when test="${outputOrder.verifyStatus eq 4}">
                                    已关闭
                                </c:when>
                            </c:choose>
                        </td>
                    </tr>
                    <tr>
                        <td>样品申请企业</td>
                        <td>${outputOrder.borrowTraderName}</td>
                        <td>创建人</td>
                        <td>${outputOrder.creator}</td>
                    </tr>
                    <tr>
                        <td>创建部门</td>
                        <td>${outputOrder.belongDepartment}</td>
                        <td>创建时间</td>
                        <td>${outputOrder.addTime}</td>
                    </tr>
                    <tr>
                        <td>申请类型</td>
                        <td>
                            <c:forEach items="${applyTypeList}" var="applyType">
                                <c:if test="${applyType.sysOptionDefinitionId eq outputOrder.wmsOutputOrderExtra.applyType}">
                                    ${applyType.title}
                                </c:if>
                            </c:forEach>

                        </td>
                        <td>样品申请原因</td>
                        <td>${outputOrder.borrowReason}</td>
                    </tr>

                    <tr>
                        <td>活动编码</td>
                        <td>${outputOrder.wmsOutputOrderExtra.activityCode}</td>
                        <td>活动名称</td>
                        <td>${outputOrder.wmsOutputOrderExtra.activityName}</td>
                    </tr>


                    <tr>
                        <td>出库状态</td>
                        <td>
                            <c:choose>
                                <c:when test="${outputOrder.outStatus eq 0}">
                                    未出库
                                </c:when>
                                <c:when test="${outputOrder.outStatus eq 1}">
                                    部分出库
                                </c:when>
                                <c:when test="${outputOrder.outStatus eq 2}">
                                    全部出库
                                </c:when>
                            </c:choose>
                        </td>
                        <td>附件</td>
                        <td>
                            <c:if test="${not empty attachment.uri}">
                                <a href="http://${attachment.domain}${attachment.uri}"
                                   target="_blank">${attachment.name}</a>
                            </c:if>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>

            <div class="parts">
                <div class="title-container title-container-blue">
                    <div class="table-title nobor">
                        收货信息
                    </div>
                </div>
                <table class="table table-bordered table-striped table-condensed table-centered">
                    <tbody>
                    <tr>
                        <td class="table-smaller">收货客户</td>
                        <td>${outputOrder.borrowTraderName}</td>
                        <td class="table-smaller">收货联系人</td>
                        <td>${outputOrder.receiver}</td>
                    </tr>
                    <tr>
                        <td>电话</td>
                        <td>${outputOrder.receiverPhone}</td>
                        <td>手机</td>
                        <td>${outputOrder.receiverTelphone}</td>
                    </tr>
                    <tr>
                        <td>收货地区</td>
                        <td>${outputOrder.receiverAddress}</td>
                        <td></td>
                        <td>
                        </td>
                    </tr>
                    <tr>
                        <td>收货地址</td>
                        <td colspan="3">${outputOrder.detailAddress}</td>
                    </tr>
                    <tr>
                        <td>物流备注</td>
                        <td colspan="3">${outputOrder.logisticCommnet}</td>
                    </tr>
                    </tbody>
                </table>
            </div>

            <li>
                <div class="parts">
                    <div class="title-container title-container-blue">
                        <div class="table-title nobor">产品信息</div>
                    </div>
                    <table class="table  table-bordered table-striped table-condensed table-centered">
                        <thead>
                        <tr>
                            <th style="width:80px">序号</th>
                            <th style="width:80px">产品名称</th>
                            <th style="width:80px">订货号</th>
                            <th style="width:80px">品牌</th>
                            <th style="width:80px">型号</th>
                            <th style="width:80px">数量</th>
                            <th style="width:80px">单位</th>
                            <th style="width:80px">销售价</th>
                            <th style="width:80px">申请总价</th>
                            <c:if test="${isSales eq 0}">
                                <th style="width:80px">参考成本价</th>
                            </c:if>
                            <th style="width:80px">可用/库存</th>
                            <th style="width:80px">发货方式</th>
                            <th style="width:80px">出库数量</th>
                            <th style="width:80px">出库状态</th>
                        </tr>
                        </thead>
                        <tbody id="thisTimeUpdateTbody">
                        <c:forEach var="lendOutGood" items="${lendOutGoodList}" varStatus="staut">
                            <tr>
                                <td>${staut.count}</td>
                                <td class="text-left">
                                    <a class="addtitle" href="javascript:void(0);"
                                       tabTitle='{"num":"viewgoods${lendOutGood.skuId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${lendOutGood.skuId}","title":"产品信息"}'>${lendOutGood.showName}</a>
                                </td>
                                <td>${lendOutGood.skuNo}</td>
                                <td>${lendOutGood.brandName}</td>
                                <td>${lendOutGood.model}</td>
                                <td>${lendOutGood.outputNum}</td>
                                <td>${lendOutGood.unitName}</td>
                                <td>${lendOutGood.sampleOrderGoodsExtra.price}</td>
                                <td>${lendOutGood.sampleOrderGoodsExtra.price * lendOutGood.outputNum}</td>
                                <c:if test="${isSales eq 0}">
                                    <td>${lendOutGood.sampleOrderGoodsExtra.purchasePrice}</td>
                                </c:if>
                                <td>${lendOutGood.availableStockNum}/${lendOutGood.stockNum}</td>
                                <td>普发</td>
                                <td>${lendOutGood.alreadyOutputNum}</td>
                                <td>
                                    <c:choose>
                                        <c:when test="${lendOutGood.outStatus eq 0}">
                                            未出库
                                        </c:when>
                                        <c:when test="${lendOutGood.outStatus eq 1}">
                                            部分出库
                                        </c:when>
                                        <c:when test="${lendOutGood.outStatus eq 2}">
                                            全部出库
                                        </c:when>
                                    </c:choose>
                                </td>
                            </tr>
                        </c:forEach>
                        </tbody>
                    </table>
                </div>
            </li>

            <%--                出库记录--%>
            <li>
                <div class="parts">
                    <div class="title-container title-container-blue">
                        <div class="table-title nobor">出库记录</div>
                    </div>
                    <table class="table  table-bordered table-striped table-condensed table-centered">
                        <thead>
                        <tr>
                            <th style="width:80px">出库单号</th>
                            <th class="wid5">序号</th>
                            <th>产品名称</th>
                            <th class="wid10">订货号</th>
                            <th>品牌</th>
                            <th>型号</th>
                            <th>实际出库数量</th>
                            <th class="wid4">单位</th>
                            <th>贝登批次码</th>
                            <th>生产日期</th>
                            <th>有效期至</th>
                            <th>出库时间</th>
                            <th>批次号</th>
                            <th>SN码</th>
                            <th>灭菌编号</th>
                            <th class="wid12">注册证号</th>
                        </tr>
                        </thead>
                        <tbody>
                        <c:forEach var="listout" items="${warehouseOutList}" varStatus="num1">
                            <tr>
                                <td class="text-left">
                                        ${listout.outInNo}
                                </td>
                                <td>${num1.count}</td>
                                <td class="text-left">
                                    <a class="addtitle" href="javascript:void(0);"
                                       tabTitle='{"num":"viewgoods${listout.skuId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${listout.skuId}","title":"产品信息"}'>${newSkuInfosMap[listout.skuId].SHOW_NAME}</a>
                                </td>
                                <td>${newSkuInfosMap[listout.skuId].SKU_NO}</td>

                                <td>${newSkuInfosMap[listout.skuId].BRAND_NAME}</td>
                                <td>${newSkuInfosMap[listout.skuId].MODEL}</td>
                                <td>${listout.num}</td>
                                <td>${newSkuInfosMap[listout.skuId].UNIT_NAME}</td>
                                <td>${ listout.vedengBatchNumber}</td>
                                <td><date:date value="${listout.productDate}" format="yyyy-MM-dd HH:mm:ss"/></td>
                                <td><date:date value="${listout.expirationDate}" format="yyyy-MM-dd HH:mm:ss"/></td>
                                <td>${listout.addTimeStr}</td>
                                <td>${ listout.batchNumber}</td>
                                <td>${ listout.barcodeFactory}</td>
                                <td>${ listout.sterilizationBatchNumber}</td>
                                <td class="text-left">
                                        ${newSkuInfosMap[listout.skuId].REGISTRATION_NUMBER}
                                </td>
                            </tr>
                        </c:forEach>
                        <!-- 查询无结果弹出 -->
                        <c:if test="${empty warehouseOutList}">
                            <tr>
                                <td colspan="17">暂无出库记录</td>
                            </tr>
                        </c:if>
                        </tbody>
                    </table>
                </div>
            </li>

            <li>
                <div class="parts">
                    <div class="title-container">
                        <div class="table-title nobor">物流信息</div>
                    </div>
                    <table class="table">
                        <thead>
                        <tr>
                            <th class="wid15" style="width: 15%">快递公司</th>
                            <th class="wid15" style="width: 15%">快递单号</th>
                            <th class="wid15" style="width: 10%">发货时间</th>
                            <th class="wid15" style="width: 15%">商品</th>
                            <th class="wid10" style="width: 10%">快递状态</th>
                            <th class="wid15" style="width: 15%">操作</th>
                        </tr>
                        </thead>
                        <tbody id="wl">
                        <c:forEach var="express" items="${expressList}">
                            <tr>
                                <td>${express.logisticsName}</td>
                                <td>${express.logisticsNo}</td>

                                <td><date:date value="${express.deliveryTime}"
                                               format="yyyy-MM-dd"/></td>
                                <td class="text-left"><c:forEach var="expressDetails"
                                                                 items="${express.expressDetail}">
                                    <div>${expressDetails.goodName}&nbsp;&nbsp;&nbsp;&nbsp;${expressDetails.num}
                                            ${expressDetails.unitName}</div>
                                </c:forEach></td>
                                    <%--<td>${express.updaterUsername}</td>--%>
                                <td><c:if test="${express.arrivalStatus == 0}">
                                    未收货
                                </c:if> <c:if test="${express.arrivalStatus == 1}">
                                    部分收货
                                </c:if> <c:if test="${express.arrivalStatus == 2}">
                                    全部收货
                                </c:if>
                                </td>
                                <td>
                                    <div class="print-record" style="display: inline-block;">
                                        <form method="get" id="searchSh"
                                              action="<%= basePath %>wms/sampleOut/printOutOrder.do?expressId=${express.expressId}&orderId=${sampleOrderId}&bussinessType=880&bussinessNo=${outputOrder.orderNo}&expressType=0">
                                            <input type="hidden" name="orderId" id="orderId"
                                                   value="${outputOrder.id}"/> <input type="hidden"
                                                                                      name="bussinessNo"
                                                                                      id="bussinessNo"
                                                                                      value="${outputOrder.orderNo}"/>
                                            <input type="hidden"
                                                   name="bussinessType" id="btype_sh" value="880"/> <input
                                                type="hidden" name="expressId" id="expressId_xs" value=""/>
                                            <span class="bt-border-style border-blue"
                                                  onclick="printSHOutOrder('${express.expressId}',${outputOrder.id});"
                                                  id="searchSpan">打印送货单</span>
                                        </form>
                                        <div class="customername pos_rel">
                                            <div class="brand-color1">
                                                <i class="bt-smaller bt-border-style border-blue pop-new-data"
                                                   layerParams='{"width":"40%","height":"420px","title":"查看物流","link":"${pageContext.request.contextPath}/warehouse/warehousesout/queryExpressInfo.do?LogisticsNo==${express.logisticsNo}&logisticsId=${express.logisticsId}"}'>查看物流</i>

                                            </div>
                                            <div class="pos_abs customernameshow mouthControlPos">
                                                最新信息：${express.contentNew}</div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </c:forEach>

                        </tbody>
                    </table>
                </div>
            </li>

            <%--                审核记录--%>
            <li>
                <div class="parts">
                    <div class="title-container title-container-blue">
                        <div class="table-title nobor ">审核记录</div>
                    </div>
                    <table class="table  table-bordered table-striped table-condensed table-centered">
                        <thead>
                        <tr>
                            <th style="width:50px">操作人</th>
                            <th style="width:80px">操作时间</th>
                            <th style="width:80px">操作事项</th>
                            <th style="width:80px">备注</th>
                        </tr>
                        </thead>
                        <tbody>
                        <c:forEach var="hi" items="${historicActivityInstance}" varStatus="status">
                            <c:if test="${not empty  hi.activityName}">
                                <tr>
                                    <td>
                                        <c:choose>
                                            <c:when test="${hi.activityType == 'startEvent'}">
                                                ${startUser}
                                            </c:when>
                                            <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                                            </c:when>
                                            <c:otherwise>
                                                <c:if test="${historicActivityInstance.size() == status.count}">
                                                    <c:forEach var="vs" items="${verifyUsersList}"
                                                               varStatus="status">
                                                        <c:if test="${fn:contains(verifyUserList, vs)}">
                                                            <span class="font-green">${vs}</span>&nbsp;
                                                        </c:if>
                                                        <c:if test="${!fn:contains(verifyUserList, vs)}">
                                                            <span>${vs}</span>&nbsp;
                                                        </c:if>
                                                    </c:forEach>

                                                    <c:if test="${empty verifyUsersList && empty hi.assignee}">
                                                        ${verifyUsers}
                                                    </c:if>
                                                </c:if>
                                                <c:if test="${historicActivityInstance.size() != status.count}">
                                                    ${hi.assignee}
                                                </c:if>
                                            </c:otherwise>
                                        </c:choose>


                                    </td>
                                    <td><fmt:formatDate value="${hi.endTime}" pattern="yyyy-MM-dd HH:mm:ss"/></td>
                                    <td>
                                        <c:choose>
                                            <c:when test="${hi.activityType == 'startEvent'}">
                                                开始
                                            </c:when>
                                            <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                                                结束
                                            </c:when>
                                            <c:otherwise>
                                                ${hi.activityName}
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                    <td class="font-red">${commentMap[hi.taskId]}</td>
                                </tr>
                            </c:if>
                        </c:forEach>
                        <!-- 查询无结果弹出 -->

                        <c:if test="${empty historicActivityInstance}">
                            <!-- 查询无结果弹出 -->
                            <tr>
                                <td colspan="4">暂无审核记录。</td>
                            </tr>
                        </c:if>
                        </tbody>
                    </table>
                </div>
            </li>

        </ul>
        <div class="add-tijiao tcenter mt10">
            <c:if test="${currentUser.userId eq outputOrder.applyerId && outputOrder.verifyStatus eq 3}">
                <button type="button" class="bt-bg-style bg-light-green bt-small mr10"
                        onclick="javascrtpt:window.location.href='${pageContext.request.contextPath}/wms/sampleOut/editSampleOutOrder.do?sampleOrderId=${sampleOrderId}'">
                    编辑
                </button>
            </c:if>

            <c:if test="${null!=historicInfo.taskInfo and (taskInfo.assignee == currentUser.username or historicInfo.candidateUserMap['belong'])}">

                <button type="button" class="bt-bg-style bg-light-green bt-small mr10 pop-new-data"
                        layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./auditResult.do?taskId=${taskId}&sampleOutId=${outputOrder.id}&pass=true"}'>
                    审核通过
                </button>
                <button type="button" class="bt-bg-style bg-light-orange bt-small mr10 pop-new-data"
                        layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./auditResult.do?taskId=${taskId}&sampleOutId=${outputOrder.id}&pass=false"}'>
                    审核不通过
                </button>
            </c:if>
        </div>
    </div>
</div>
<%@ include file="../../common/footer.jsp" %>