package com.wms.service.stockcalculate;

import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.wms.dto.StockCalculateDto;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 外接单出库
 */
@Service
public class LendOutCaculateImpl extends AbstractStockinfoCaculateInterace{

    @Override
    protected void calculateStockNum(List<StockCalculateDto> details, List<WarehouseDto> warehouseStockList) {

        /*details.stream().forEach(stockCalculateDto->{

            WarehouseDto warehouseStock = super.getWarehouseStockBySkuAndLogcic(warehouseStockList,
                    stockCalculateDto.getSku(),
                    stockCalculateDto.getLogicalWarehouseId());

            //减少可用数量
            warehouseStock.setStockNum(-Math.abs(stockCalculateDto.getStockNum()));
        });*/
        super.calculateStockOrOccupyNumBase(details,warehouseStockList,true,true);
    }

    @Override
    protected void calculateOcupyNum(List<StockCalculateDto> details, List<WarehouseDto> warehouseStockList) {

        /*details.stream().forEach(stockCalculateDto->{

            WarehouseDto warehouseStock = super.getWarehouseStockBySkuAndLogcic(warehouseStockList,
                    stockCalculateDto.getSku(),
                    stockCalculateDto.getLogicalWarehouseId());

            //减少库存占用数量
            warehouseStock.setOccupyNum(-Math.abs(stockCalculateDto.getOccupyNum()));
        });*/
        super.calculateStockOrOccupyNumBase(details, warehouseStockList, true,false);

    }
}
