package com.vedeng.erp.finance.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
    * 财务结款通知记录表
    */
@Data
public class SettlementNoticeRecordEntity extends BaseEntity {
    /**
    * 主键
    */
    private Long settlementNoticeRecordId;

    /**
    * 银行ID
    */
    private Integer bankBillId;

    /**
    * 通知类型
    */
    private Integer noticeType;

    /**
    * 通知次数
    */
    private Integer noticeCount;

    /**
    * 最近一次通知时间
    */
    private Date lastNoticeTime;

    /**
    * 是否删除 0否 1是
    */
    private Integer isDelete;

    /**
    * 更新备注
    */
    private String updateRemark;

   
}
