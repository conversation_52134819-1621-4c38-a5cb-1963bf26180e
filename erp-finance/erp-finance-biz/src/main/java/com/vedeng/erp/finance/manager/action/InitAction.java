package com.vedeng.erp.finance.manager.action;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.finance.domain.context.InvoiceRedConfirmationContext;
import com.vedeng.erp.finance.enums.InvoiceRedConfirmationEvent;
import com.vedeng.erp.finance.enums.InvoiceRedConfirmationStateEnum;
import com.vedeng.erp.finance.manager.InvoiceRedConfirmationAction;
import com.vedeng.erp.finance.service.InvoiceRedConfirmationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 初始化事件
 * @date 2023/10/23 13:09
 */
@Component
@Slf4j
public class InitAction implements InvoiceRedConfirmationAction {

    @Autowired
    InvoiceRedConfirmationService invoiceRedConfirmationService;

    @Override
    public void execute(InvoiceRedConfirmationStateEnum from, InvoiceRedConfirmationStateEnum to,
                        InvoiceRedConfirmationEvent event, InvoiceRedConfirmationContext context) {
        log.info("InitAction入参{}", JSON.toJSONString(context));
        invoiceRedConfirmationService.add(context.getInvoiceRedConfirmationDto());
    }
}
