package com.vedeng.temporal.workflow.activity.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import com.vedeng.temporal.workflow.activity.PaymentActivity;
import com.vedeng.temporal.workflow.activity.core.UniversalActivityTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 付款 Activity 实现类
 *
 * 架构迁移说明：
 * - 从 PaymentFlow 迁移核心业务逻辑到 Activity 层
 * - 使用 UniversalBusinessTemplate 处理所有业务逻辑：固定参数、API调用、异常处理、通知发送
 * - 每个方法都是独立的 Activity，由 Temporal 管理重试
 * - 直接使用 CompanyBusinessRequest 传递数据，自动解析 JSON
 * - 保持与原 PaymentFlow 完全一致的业务逻辑和API调用
 *
 * 业务流程：
 * 1. createPayment - 创建付款申请，包含完整的付款流程逻辑，返回付款单ID
 * 2. queryPaymentStatus - 查询付款状态，用于流程控制
 *
 * 迁移内容：
 * - execute 方法迁移为 createPayment 方法
 * - executePaymentApplication 逻辑集成到 createPayment 中
 * - extractPaymentId 方法保持不变
 * - 保持原有的API路径和参数结构
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0 (架构迁移版本，从 PaymentFlow 迁移)
 * @since 2025-01-21
 */
@Component
@Slf4j
public class PaymentActivityImpl implements PaymentActivity {

    @Autowired
    private UniversalActivityTemplate universalActivityTemplate;

    @Override
    public CompanyBusinessResponse createPayment(CompanyBusinessRequest request) {
        // 设置用户信息，与原 PaymentFlow 中使用 "admin" 用户保持一致
        request.setUserName("admin");

        // 配置业务操作 - 迁移自 PaymentFlow.execute 和 executePaymentApplication
        UniversalActivityTemplate.BusinessOperationConfig config =
            UniversalActivityTemplate.BusinessOperationConfig.create()
                .operationName("创建付款申请")
                .apiPath("/api/v1/pay/apply.do")
                .dataPreparer(this::preparePaymentData);
            return universalActivityTemplate.execute(request, config);

    }


    // ========== 私有数据准备方法 ==========

    /**
     * 准备付款数据
     * 迁移自 PaymentFlow.executePaymentApplication 方法
     */
    private Map<String, Object> preparePaymentData(CompanyBusinessRequest request) {
        log.info("准备付款数据，业务ID: {}, 付款公司: {}, 收款公司: {}", 
                request.getBusinessId(), request.getSourceCompanyCode(), request.getTargetCompanyCode());

        // 直接使用原始请求中的扩展属性作为付款请求数据
        // 这与原 PaymentFlow 中的逻辑完全一致
        Map<String, Object> paymentRequestData = request.getExtendedProperties();
        log.info(" 付款公司: {}, 收款公司: {}，准备付款数据extendedProperties：{}",request.getBusinessId(), request.getSourceCompanyCode(),JSON.toJSON(paymentRequestData));
        
        if (paymentRequestData == null) {
            paymentRequestData = new HashMap<>();
        }

        Map<String, Object> params = new HashMap<>();
        params.put("buyOrderNo",paymentRequestData.get("buyOrderNo"));
        log.info("业务ID: {},{}公司付款数据: {}", request.getBusinessId(), request.getTargetCompanyCode(), JSON.toJSONString(params));
        
        return params;
    }

    /**
     * 准备查询数据
     */
    private Map<String, Object> prepareQueryData(CompanyBusinessRequest request) {
        Map<String, Object> queryData = new HashMap<>();

        // 从 businessData 中获取付款单ID（businessData 是 JSON 字符串）
        Map<String, Object> businessData = parseBusinessData(request.getBusinessData());
        if (businessData != null && businessData.containsKey("paymentId")) {
            Object paymentId = businessData.get("paymentId");
            queryData.put("paymentId", paymentId);
            queryData.put("id", paymentId);
        }

        queryData.put("queryTime", System.currentTimeMillis());

        log.debug("准备查询数据完成, 业务ID: {}", request.getBusinessId());
        return queryData;
    }

    /**
     * 从API响应中提取付款单ID
     * 迁移自 PaymentFlow.extractPaymentId 方法，保持逻辑完全一致
     * 集成验证逻辑：先验证返回码，再提取ID
     */
    private String extractPaymentId(Map<String, Object> apiResponse) {
        if (apiResponse == null) {
            return null;
        }
        log.info("付款申请提交结果: {}", JSON.toJSON(apiResponse));
        
        // 先进行验证：检查API返回的code字段是否为0（与PaymentFlow第78行逻辑一致）
        if (!Objects.equals(apiResponse.get("code"), 0)) {
            log.warn("付款申请提交失败，API返回码不为0: {}", apiResponse.get("code"));
            return null; // 验证失败返回null，会导致业务操作失败
        }
        
        // 验证通过后，提取付款单ID
        // 尝试从不同的可能字段中提取付款单ID
        Object data = apiResponse.get("data");
        if (data instanceof Map) {
            Map<String, Object> dataMap = (Map<String, Object>) data;
            
            // 常见的付款单ID字段名
            Object paymentId = dataMap.get("paymentId");
            if (paymentId != null) {
                return paymentId.toString();
            }
            
            paymentId = dataMap.get("id");
            if (paymentId != null) {
                return paymentId.toString();
            }
            
            paymentId = dataMap.get("billId");
            if (paymentId != null) {
                return paymentId.toString();
            }
        }
        
        // 如果data直接是String或Number
        if (data != null && !(data instanceof Map)) {
            return data.toString();
        }
        
        return null;
    }

    /**
     * 解析业务数据 JSON 字符串
     * 与 SalesOrderActivityImpl 和 PurchaseOrderActivityImpl 保持一致的实现
     */
    private Map<String, Object> parseBusinessData(String businessDataJson) {
        if (!StringUtils.hasText(businessDataJson)) {
            return new HashMap<>();
        }

        try {
            return JSON.parseObject(businessDataJson, new TypeReference<Map<String, Object>>() {});
        } catch (Exception e) {
            log.warn("解析业务数据JSON失败: {}", businessDataJson, e);
            return new HashMap<>();
        }
    }
}
