<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>可发货白名单设置</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">

</head>
<body id="traderperoidwhite" datasource="erp-datasourcetarget"
      SUCCESS_URL="/ezadmin/form/form-userAdd?ID=${ID}"
>
<div class="layui-container">
    <form ID="inputForm"   method="post" class="layui-form">
        <div  class=" layui-form-item">
            <label class="layui-form-label"  >客户id</label>
            <div class="layui-input-inline form-group">
                <input  readonly="readonly" lay-verify="required"
                         type="span"  placeholder="请输入用户名" class="layui-input form-item"
                         value=""  name="TRADER_ID" item_desc="登录系统的名称"
                          >
            </div>
        </div>
        <div  class=" layui-form-item">
            <label class="layui-form-label"  >状态</label>
            <div class="layui-input-block form-group">
                <SELECT   lay-verify="required"
                          placeholder="" class="layui-input form-item"
                         value=""  data="yesno" name="DISABLED_REASON"
                ></SELECT>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"  >表单初始化</label>
            <div class="layui-input-block">

                 <pre id="init" class="layui-code">
list=select("SELECT user_id ID,username  , IS_DISABLED ,DISABLED_REASON FROM T_USER_EZ_TEST WHERE USER_ID=${ID}");
return list.get(0);
                 </pre>

            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"  >表单提交</label>
            <div class="layui-input-block">

                 <pre id="submit" class="layui-code" SUCCESS_URL="" >
                  import com.ezadmin.web.EzResult;
                  if(isNotBlank("TRADER_ID")){
                        list=  select("select trader_id from T_TRADER_PERIOD_WHITE where TRADER_ID=#{TRADER_ID}");
                      if(list!=null && list.size()>0){
                          update("update T_TRADER_PERIOD_WHITE set is_del=#{IS_DEL},ADD_TIME=NOW(),MOD_TIME=NOW(),IS_ISSUE_WMS=0 WHERE TRADER_ID=#{TRADER_ID}");
                      }else{
                         insert("insert into T_TRADER_PERIOD_WHITE(TRADER_ID,ADD_TIME,MOD_TIME,CREATOR,CREATOR_NAME,IS_DEL)
                     values(#{TRADER_ID},NOW(),NOW(),#{EZ_SESSION_USER_ID_KEY},#{EZ_SESSION_USER_NAME_KEY},#{IS_DEL})");
                      }
                  }
                  return EzResult.instance();
                 </pre>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"  >表单删除</label>
            <div class="layui-input-block">
                <pre id="delete" class="layui-code">
                    update("update T_USER_EZ_TEST SET IS_DISABLED=1 WHERE USER_ID=${ID}");
                    return 1;
                 </pre>
            </div>
        </div>
    </form>

    <form id="configForm" class="layui-form">
        <div class="layui-card" group_name="表单数据配置">
            <div class="layui-card-header">
                表单数据配置
            </div>
            <div class="layui-card-body">
            </div>
        </div>
        <div class="layui-form-item "><label class="layui-form-label">初始表达式</label>
            <div class="layui-input-block form-group">
      <pre class="form-item layui-code" id="init" name="INIT_EXPRESS" type="">list=select("SELECT user_id ID,username  , IS_DISABLED ,DISABLED_REASON FROM T_USER_EZ_TEST WHERE USER_ID=${ID}");
return list.get(0);</pre>
            </div>
        </div>
        <div class="layui-form-item ">
            <label class="layui-form-label">提交表达式</label>
            <div class="layui-input-block form-group">
      <pre class="form-item layui-code" id="submit" name="SUBMIT_EXPRESS" type="">
           import com.ezadmin.web.EzResult;
                  if(isNotBlank("TRADER_ID")){
                        list=  select("select trader_id from T_TRADER_PERIOD_WHITE where TRADER_ID=#{TRADER_ID}");
                      if(list!=null && list.size()>0){
                          update("update T_TRADER_PERIOD_WHITE set is_del=#{IS_DEL},ADD_TIME=NOW(),MOD_TIME=NOW(),IS_ISSUE_WMS=0 WHERE TRADER_ID=#{TRADER_ID}");
                      }else{
                         insert("insert into T_TRADER_PERIOD_WHITE(TRADER_ID,ADD_TIME,MOD_TIME,CREATOR,CREATOR_NAME,IS_DEL)
                     values(#{TRADER_ID},NOW(),NOW(),#{EZ_SESSION_USER_ID_KEY},#{EZ_SESSION_USER_NAME_KEY},#{IS_DEL})");
                      }
                  }
                  return EzResult.instance();
          </pre>
            </div>
        </div>
        <div class="layui-form-item ">
            <label class="layui-form-label">删除表达式</label>
            <div class="layui-input-block form-group">
                <pre class="form-item layui-code" id="delete" name="DELETE_EXPRESS" type=""></pre>
            </div>
        </div>
        <div class="layui-form-item "><label class="layui-form-label">分组表达式</label>
            <div class="layui-input-block form-group">
                <pre class="form-item layui-code" name="GROUP_DATA" type=""></pre>
            </div>
        </div>
    </form>

</div>
</body>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function(){

    })
</script>
<!--<div id="appendFoot">-->
<!--    这是一段放在后面的自定义的html代码片段-->
<!--</div>-->
</html>