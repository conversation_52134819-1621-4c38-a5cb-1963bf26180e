package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.annotation.BusinessID;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@NoArgsConstructor
public class KingDeeWorkflowDto extends KingDeeMqBaseDto {

   @BusinessID
   public String payApplyId;

   public String formId;

    public KingDeeWorkflowDto(KingDeeBizEnums kingDeeBizEnums, String payApplyId, String formId) {
        super(kingDeeBizEnums);
        this.payApplyId = payApplyId;
        this.formId = formId;
    }
}
