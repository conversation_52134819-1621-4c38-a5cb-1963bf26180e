package com.vedeng.goods.model;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.*;

import java.io.Serializable;

/**
 * V_CORE_SKU_HISTORY
 *
 * <AUTHOR>
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CoreSkuHistory implements Serializable {
    private Integer skuHistoryId;

    /**
     * V_CORE_SKU.SKU_ID
     */
    private Integer skuId;

    /**
     * SkuAddCommand历史信息
     */

    private JSONObject skuAddCommand;

    /**
     * CoreSkuGenerate历史信息
     */
    private JSONObject coreSkuGenerate;

    /**
     * GoodsStorageConditionVo历史信息
     */
    private JSONObject goodsStorageConditionVo;

    /**
     * SkuAuthorizationRequestVo历史信息
     */
    private JSONObject skuAuthorizationRequestVo;

    /**
     * 最近一次修改时间
     */
    private Long modTime;

    /**
     * 最近一次修改人
     */
    private Integer updater;

    /**
     * 检测报告文件
     */
    private String skuCheckFilesJson;

    private static final long serialVersionUID = 1L;
}