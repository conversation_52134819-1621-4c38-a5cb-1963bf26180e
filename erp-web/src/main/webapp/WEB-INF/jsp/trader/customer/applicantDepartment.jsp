<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="申请人/部门信用记录" scope="application" />
<%@ include file="../../common/common.jsp"%>
<style>
    .bottom-c{
        bottom: 10px;
        right: 0px;
        left: 0px;
    }
    .bottom-bottom{
        position: fixed;
        bottom: 0px;
        right: 0px;
    }
</style>
<div>

<div class="layui-layer-title">申请人/部门信用记录</div>
<div class="form-list  form-tips8 trader-customer-accountperiodapply">
    <div>
        <form action="" method="post" id="search" action="<%=basePath%>trader/customer/applicantDepartment.do">

            <div class="infor_name" style="width: auto;">开始时间：</div>
            <input class="Wdate f_left input-smaller96 mr5" type="text" placeholder="请选择日期" autocomplete="off" onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'searchEndtimeStr\')}'})" name="startTime" id="searchBegintimeStr" value='<date:date value ="${startTime}" format="yyyy-MM-dd"/>'>

            <div class="infor_name">截至时间：</div>
            <input class="Wdate f_left input-smaller96" type="text" placeholder="请选择日期" autocomplete="off" onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'searchBegintimeStr\')}'})" name="endTime" id="searchEndtimeStr" value='<date:date value ="${endTime}" format="yyyy-MM-dd"/>'>
            <input type="hidden" value="${billPeriodAppliers}" id="billPeriodAppliers"/>
            <%--开始时间  截至时间 搜索--%>
            <div class="f_left" style="margin: -2px 10px 0px 20px;">
                <span class="bt-small bg-light-blue bt-bg-style mr20" onclick="search()">搜索</span>
            </div>

        </form>
    </div>
    <div class="title-container" style="width: 100%">
        <div class="table-title nobor">归属销售名:  ${saleName}</div>
    </div>

    <table class="table table-bordered table-striped table-condensed table-centered">
        <thead>
        <tr>
            <th class="table-smallest">账期类型</th>
            <th class="table-smallest">订单实际金额</th>
            <th class="table-smallest">未还账期金额</th>
            <th class="table-smallest">逾期未还金额</th>
            <th class="table-smallest">逾期订单数</th>
            <th class="table-smallest">逾期天数</th>
            <th class="table-smallest">订单平均逾期天数</th>
            <th class="table-smallest">账期现有额度</th>
            <th class="table-smallest">账期剩余额度</th>
            <th class="table-smallest">历史逾期次数</th>
            <th class="table-smallest">账期历史使用次数</th>
            <th class="table-smallest">账期次数逾期率</th>
            <th class="table-smallest">历史账期订单总金额</th>
            <th class="table-smallest">历史账期订单逾期总额</th>
            <th class="table-smallest">账期金额逾期率</th>
            <th class="table-smallest">历史逾期订单数</th>
            <th class="table-smallest">历史逾期天数</th>
            <th class="table-smallest">历史订单平均逾期天数</th>
        </tr>
        </thead>

        <tbody>
        <tr>
            <td>正式账期</td>
            <td>
                <c:if test="${empty applyFormalReal}">-</c:if>
                <fmt:formatNumber type="number" value="${applyFormalReal}" pattern="0.00" maxFractionDigits="2" />
            </td>
            <td>
                <c:if test="${empty applyFormal.unreturnedAmount}">-</c:if>
                <fmt:formatNumber type="number" value="${applyFormal.unreturnedAmount}" pattern="0.00" maxFractionDigits="2" />
            </td>
            <td>
                <c:if test="${empty applyFormal.overdueAmount}">-</c:if>
                <fmt:formatNumber type="number" value="${applyFormal.overdueAmount}" pattern="0.00" maxFractionDigits="2" />
            </td>
            <td><c:if test="${empty applyFormal.orderCountOfOverdue}">-</c:if>${applyFormal.orderCountOfOverdue}</td>
            <td><c:if test="${empty applyFormal.daysOfOverdue}">-</c:if>${applyFormal.daysOfOverdue}</td>
            <td>
                <c:choose>
                    <c:when test="${(empty applyFormal.avgOverdueDaysByOrder || applyFormal.avgOverdueDaysByOrder eq 0) && (empty applyFormal.orderCountOfOverdue || empty applyFormal.daysOfOverdue || applyFormal.orderCountOfOverdue eq 0 || applyFormal.daysOfOverdue eq 0)}">
                        -
                    </c:when>
                    <c:when test="${(empty applyFormal.avgOverdueDaysByOrder || applyFormal.avgOverdueDaysByOrder eq 0) && (not empty applyFormal.orderCountOfOverdue && not empty applyFormal.daysOfOverdue && applyFormal.orderCountOfOverdue ne 0 && applyFormal.daysOfOverdue ne 0) }">
                        <fmt:formatNumber type="number" value="${(applyFormal.daysOfOverdue/applyFormal.orderCountOfOverdue) }"  maxFractionDigits="2" />
                    </c:when>
                    <c:otherwise>
                        ${applyFormal.avgOverdueDaysByOrder}
                    </c:otherwise>
                </c:choose>
            </td>
            <td>
                <c:if test="${empty applyFormal.applyAmount}">-</c:if>
                <fmt:formatNumber type="number" value="${applyFormal.applyAmount}" pattern="0.00" maxFractionDigits="2" />
            </td>
            <td>
                <c:if test="${empty applyFormal.availableAmount}">-</c:if>
                <fmt:formatNumber type="number" value="${applyFormal.availableAmount}" pattern="0.00" maxFractionDigits="2" />
            </td>
            <td><c:if test="${empty applyFormal.historyCountOfOverdue}">-</c:if>${applyFormal.historyCountOfOverdue}</td>
            <td><c:if test="${empty applyFormal.historyUsedCount}">-</c:if>${applyFormal.historyUsedCount}</td>
            <%--<td><c:if test="${empty applyFormal.historyPercentOverdueByUsedCount}">0</c:if>${applyFormal.historyPercentOverdueByUsedCount}%</td>--%>
            <td>
                <c:choose>
                    <c:when test="${empty applyFormal.historyPercentOverdueByUsedCount || applyFormal.historyPercentOverdueByUsedCount eq 0 }">0%</c:when>
                    <c:otherwise>
                        <fmt:formatNumber type="number" value="${(applyFormal.historyPercentOverdueByUsedCount)*100}"  maxFractionDigits="5" />%
                    </c:otherwise>
                </c:choose>
            </td>
            <td>
                <c:if test="${empty applyFormalHistoryReal}">-</c:if>
                <fmt:formatNumber type="number" value="${applyFormalHistoryReal}" pattern="0.00" maxFractionDigits="2" />
            </td>
            <td>
                <c:if test="${empty applyFormal.historyAmountOfOverdue}">-</c:if>
                <fmt:formatNumber type="number" value="${applyFormal.historyAmountOfOverdue}" pattern="0.00" maxFractionDigits="2" />
            </td>
            <td>
                <c:choose>
                    <c:when test="${empty applyFormalHistoryReal || applyFormalHistoryReal == 0  || empty (applyFormal.historyAmountOfOverdue)}">0%</c:when>
                    <c:otherwise><fmt:formatNumber type="number" value="${(applyFormal.historyAmountOfOverdue*100/applyFormalHistoryReal)}"  maxFractionDigits="5" />%</c:otherwise>
                </c:choose>
            </td>
            <td><c:if test="${empty applyFormal.historyOrderCountOfOverdue}">-</c:if>${applyFormal.historyOrderCountOfOverdue}</td>
            <td><c:if test="${empty applyFormal.historyDaysOfOverdue}">-</c:if>${applyFormal.historyDaysOfOverdue}</td>
            <td><c:if test="${empty applyFormal.historyAvgDaysOfOverdueByOrder}">-</c:if>${applyFormal.historyAvgDaysOfOverdueByOrder}</td>
        </tr>
        <tr>
            <td>临时账期</td>
            <td>
                <c:if test="${empty applyTemporaryReal}">-</c:if>
                <fmt:formatNumber type="number" value="${applyTemporaryReal}" pattern="0.00" maxFractionDigits="2" />
            </td>
            <td>
                <c:if test="${empty applyTemporary.unreturnedAmount}">-</c:if>
                <fmt:formatNumber type="number" value="${applyTemporary.unreturnedAmount}" pattern="0.00" maxFractionDigits="2" />
            </td>
            <td>
                <c:if test="${empty applyTemporary.overdueAmount}">-</c:if>
                <fmt:formatNumber type="number" value="${applyTemporary.overdueAmount}" pattern="0.00" maxFractionDigits="2" />
            </td>
            <td><c:if test="${empty applyTemporary.orderCountOfOverdue}">-</c:if>${applyTemporary.orderCountOfOverdue}</td>
            <td><c:if test="${empty applyTemporary.daysOfOverdue}">-</c:if>${applyTemporary.daysOfOverdue}</td>
            <td>
                <c:choose>
                    <c:when test="${(empty applyTemporary.avgOverdueDaysByOrder || applyTemporary.avgOverdueDaysByOrder eq 0) && (empty applyTemporary.orderCountOfOverdue || empty applyTemporary.daysOfOverdue || applyTemporary.orderCountOfOverdue eq 0  || applyTemporary.daysOfOverdue eq 0)}">
                        -
                    </c:when>
                    <c:when test="${(empty applyTemporary.avgOverdueDaysByOrder || applyTemporary.avgOverdueDaysByOrder eq 0) && (not empty applyTemporary.orderCountOfOverdue && not empty applyTemporary.daysOfOverdue && applyTemporary.orderCountOfOverdue ne 0 && applyTemporary.daysOfOverdue ne 0) }">
                        <fmt:formatNumber type="number" value="${(applyTemporary.daysOfOverdue/applyTemporary.orderCountOfOverdue) }"  maxFractionDigits="2" />
                    </c:when>
                    <c:otherwise>
                        ${applyTemporary.avgOverdueDaysByOrder}
                    </c:otherwise>
                </c:choose>
            </td>
            <td>
                <c:if test="${empty applyTemporary.applyAmount}">-</c:if>
                <fmt:formatNumber type="number" value="${applyTemporary.applyAmount}" pattern="0.00" maxFractionDigits="2" />
            </td>
            <td>
                <c:if test="${empty applyTemporary.availableAmount}">-</c:if>
                <fmt:formatNumber type="number" value="${applyTemporary.availableAmount}" pattern="0.00" maxFractionDigits="2" />
            </td>
            <td><c:if test="${empty applyTemporary.historyCountOfOverdue}">-</c:if>${applyTemporary.historyCountOfOverdue}</td>
            <td><c:if test="${empty applyTemporary.historyUsedCount}">-</c:if>${applyTemporary.historyUsedCount}</td>

            <td>
                <c:choose>
                    <c:when test="${empty applyTemporary.historyPercentOverdueByUsedCount || applyTemporary.historyPercentOverdueByUsedCount eq 0 }">0%</c:when>
                    <c:otherwise>
                        <fmt:formatNumber type="number" value="${(applyTemporary.historyPercentOverdueByUsedCount)*100}"  maxFractionDigits="5" />%
                    </c:otherwise>
                </c:choose>
            </td>
            <td>
                <c:if test="${empty applyTemporaryHistoryReal}">-</c:if>
                <fmt:formatNumber type="number" value="${applyTemporaryHistoryReal}" pattern="0.00" maxFractionDigits="2" />
            </td>
            <td>
                <c:if test="${empty applyTemporary.historyAmountOfOverdue}">-</c:if>
                <fmt:formatNumber type="number" value="${applyTemporary.historyAmountOfOverdue}" pattern="0.00" maxFractionDigits="2" />
            </td>
            <td>
                <c:choose>
                    <c:when test="${empty applyTemporaryHistoryReal || applyTemporaryHistoryReal == 0|| empty (applyTemporary.historyAmountOfOverdue)}">0%</c:when>
                    <c:otherwise><fmt:formatNumber type="number" value="${(applyTemporary.historyAmountOfOverdue*100/applyTemporaryHistoryReal)}"  maxFractionDigits="5" />%</c:otherwise>
                </c:choose>
            </td>

            <td><c:if test="${empty applyTemporary.historyOrderCountOfOverdue}">-</c:if>${applyTemporary.historyOrderCountOfOverdue}</td>
            <td><c:if test="${empty applyTemporary.historyDaysOfOverdue}">-</c:if>${applyTemporary.historyDaysOfOverdue}</td>
            <td><c:if test="${empty applyTemporary.historyAvgDaysOfOverdueByOrder}">-</c:if>${applyTemporary.historyAvgDaysOfOverdueByOrder}</td>
        </tr>
        <tr>
            <td>订单账期</td>
            <td>
                <c:if test="${empty applyOrderReal}">-</c:if>
                <fmt:formatNumber type="number" value="${applyOrderReal}" pattern="0.00" maxFractionDigits="2" />
            </td>
            <td>
                <c:if test="${empty applyOrder.unreturnedAmount}">-</c:if>
                <fmt:formatNumber type="number" value="${applyOrder.unreturnedAmount}" pattern="0.00" maxFractionDigits="2" />
            </td>
            <td>
                <c:if test="${empty applyOrder.overdueAmount}">-</c:if>
                <fmt:formatNumber type="number" value="${applyOrder.overdueAmount}" pattern="0.00" maxFractionDigits="2" />
            </td>
            <td><c:if test="${empty applyOrder.orderCountOfOverdue}">-</c:if>${applyOrder.orderCountOfOverdue}</td>
            <td><c:if test="${empty applyOrder.daysOfOverdue}">-</c:if>${applyOrder.daysOfOverdue}</td>
            <td>
                <c:choose>
                    <c:when test="${(empty applyOrder.avgOverdueDaysByOrder || applyOrder.avgOverdueDaysByOrder eq 0) && (empty applyOrder.orderCountOfOverdue || empty applyOrder.daysOfOverdue || applyOrder.orderCountOfOverdue eq 0 ||  applyOrder.daysOfOverdue eq 0)}">
                        -
                    </c:when>
                    <c:when test="${(empty applyOrder.avgOverdueDaysByOrder || applyOrder.avgOverdueDaysByOrder eq 0) && (not empty applyOrder.orderCountOfOverdue && not empty applyOrder.daysOfOverdue && applyOrder.orderCountOfOverdue ne 0 && applyOrder.daysOfOverdue ne 0) }">
                        <fmt:formatNumber type="number" value="${(applyOrder.daysOfOverdue/applyOrder.orderCountOfOverdue) }"  maxFractionDigits="2" />
                    </c:when>
                    <c:otherwise>
                        ${applyOrder.avgOverdueDaysByOrder}
                    </c:otherwise>
                </c:choose>
            </td>
            <td>
                <c:if test="${empty applyOrder.applyAmount}">-</c:if>
                <fmt:formatNumber type="number" value="${applyOrder.applyAmount}" pattern="0.00" maxFractionDigits="2" />
            </td>
            <td>
                <c:if test="${empty applyOrder.availableAmount}">-</c:if>
                <fmt:formatNumber type="number" value="${applyOrder.availableAmount}" pattern="0.00" maxFractionDigits="2" />
            </td>
            <td><c:if test="${empty applyOrder.historyCountOfOverdue}">-</c:if>${applyOrder.historyCountOfOverdue}</td>
            <td><c:if test="${empty applyOrder.historyUsedCount}">-</c:if>${applyOrder.historyUsedCount}</td>

            <td>
                <c:choose>
                    <c:when test="${empty applyOrder.historyPercentOverdueByUsedCount ||  applyOrder.historyPercentOverdueByUsedCount eq 0}">0%</c:when>
                    <c:otherwise>
                        <fmt:formatNumber type="number" value="${(applyOrder.historyPercentOverdueByUsedCount)*100}"  maxFractionDigits="5" />%
                    </c:otherwise>
                </c:choose>
            </td>
            <td>
                <c:if test="${empty applyOrderHistoryReal}">-</c:if>
                <fmt:formatNumber type="number" value="${applyOrderHistoryReal}" pattern="0.00" maxFractionDigits="2" />
            </td>
            <td>
                <c:if test="${empty applyOrder.historyAmountOfOverdue}">-</c:if>
                <fmt:formatNumber type="number" value="${applyOrder.historyAmountOfOverdue}" pattern="0.00" maxFractionDigits="2" />
            </td>
            <td>
                <c:choose>
                    <c:when test="${empty applyOrderHistoryReal || applyOrderHistoryReal == 0 || empty applyOrder.historyAmountOfOverdue}">0%</c:when>
                    <c:otherwise><fmt:formatNumber type="number" value="${(applyOrder.historyAmountOfOverdue*100/applyOrderHistoryReal)}"  maxFractionDigits="5" />%</c:otherwise>
                </c:choose>
            </td>

            <td><c:if test="${empty applyOrder.historyOrderCountOfOverdue}">-</c:if>${applyOrder.historyOrderCountOfOverdue}</td>
            <td><c:if test="${empty applyOrder.historyDaysOfOverdue}">-</c:if>${applyOrder.historyDaysOfOverdue}</td>
            <td><c:if test="${empty applyOrder.historyAvgDaysOfOverdueByOrder}">-</c:if>${applyOrder.historyAvgDaysOfOverdueByOrder}</td>
        </tr>
        <tr>
            <td>合计</td>
            <td>
                <c:if test="${empty appCountReal}">-</c:if>
                <fmt:formatNumber type="number" value="${appCountReal}" pattern="0.00" maxFractionDigits="2" />
            </td>
            <td>
                <c:if test="${empty appCount.unreturnedAmount}">-</c:if>
                <fmt:formatNumber type="number" value="${appCount.unreturnedAmount}" pattern="0.00" maxFractionDigits="2" />
            </td>
            <td>
                <c:if test="${empty appCount.overdueAmount}">-</c:if>
                <fmt:formatNumber type="number" value="${appCount.overdueAmount}" pattern="0.00" maxFractionDigits="2" />
            </td>
            <td><c:if test="${empty appCount.orderCountOfOverdue}">-</c:if>${appCount.orderCountOfOverdue}</td>
            <td><c:if test="${empty appCount.daysOfOverdue}">-</c:if>${appCount.daysOfOverdue}</td>
            <td>
                <c:choose>
                    <c:when test="${(empty appCount.avgOverdueDaysByOrder || appCount.avgOverdueDaysByOrder eq 0) && (empty appCount.orderCountOfOverdue || empty appCount.daysOfOverdue || appCount.orderCountOfOverdue eq 0 || appCount.daysOfOverdue eq 0 )}">
                        -
                    </c:when>
                    <c:when test="${(empty appCount.avgOverdueDaysByOrder || appCount.avgOverdueDaysByOrder eq 0) && (not empty appCount.orderCountOfOverdue && not empty appCount.daysOfOverdue && appCount.orderCountOfOverdue ne 0 && appCount.daysOfOverdue ne 0) }">
                        <fmt:formatNumber type="number" value="${(appCount.daysOfOverdue/appCount.orderCountOfOverdue) }"  maxFractionDigits="2" />
                    </c:when>
                    <c:otherwise>
                        ${appCount.avgOverdueDaysByOrder}
                    </c:otherwise>
                </c:choose>
            </td>
            <td>
                <c:if test="${empty appCount.applyAmount}">-</c:if>
                <fmt:formatNumber type="number" value="${appCount.applyAmount}" pattern="0.00" maxFractionDigits="2" />
            </td>
            <td>
                <c:if test="${empty appCount.availableAmount}">-</c:if>
                <fmt:formatNumber type="number" value="${appCount.availableAmount}" pattern="0.00" maxFractionDigits="2" />
            </td>
            <td><c:if test="${empty appCount.historyCountOfOverdue}">-</c:if>${appCount.historyCountOfOverdue}</td>
            <td><c:if test="${empty appCount.historyUsedCount}">-</c:if>${appCount.historyUsedCount}</td>

            <td>

                <c:choose>
                    <c:when test="${(empty appCount.historyPercentOverdueByUsedCount || appCount.historyPercentOverdueByUsedCount eq 0) || (empty appCount.historyCountOfOverdue || appCount.historyCountOfOverdue eq 0) || (empty appCount.historyUsedCount || appCount.historyUsedCount eq 0)}">0%</c:when>
                    <c:otherwise>
                        <fmt:formatNumber type="number" value="${(appCount.historyCountOfOverdue/appCount.historyUsedCount)*100}"  maxFractionDigits="2" />%
                    </c:otherwise>
                </c:choose>
            </td>
            <td>
                <c:if test="${empty appCountHistoryReal}">-</c:if>
                <fmt:formatNumber type="number" value="${appCountHistoryReal}" pattern="0.00" maxFractionDigits="2" />
            </td>
            <td>
                <c:if test="${empty appCount.historyAmountOfOverdue}">-</c:if>
                <fmt:formatNumber type="number" value="${appCount.historyAmountOfOverdue}" pattern="0.00" maxFractionDigits="2" />
            </td>
            <td>
                <c:choose>
                    <c:when test="${empty appCountHistoryReal || appCountHistoryReal == 0 || empty (appCount.historyAmountOfOverdue)}">0%</c:when>
                    <c:otherwise> <fmt:formatNumber type="number" value="${(appCount.historyAmountOfOverdue*100/appCountHistoryReal)}" maxFractionDigits="5" />%</c:otherwise>
                </c:choose>
            </td>

            <td><c:if test="${empty appCount.historyOrderCountOfOverdue}">-</c:if>${appCount.historyOrderCountOfOverdue}</td>
            <td><c:if test="${empty appCount.historyDaysOfOverdue}">-</c:if>${appCount.historyDaysOfOverdue}</td>
            <td><c:if test="${empty appCount.historyAvgDaysOfOverdueByOrder}">-</c:if>${appCount.historyAvgDaysOfOverdueByOrder}</td>
        </tr>
        </tbody>
    </table>

    <c:if test="${empty isEnable and isEnable != 1}">

        <c:forEach var="list" items="${resultList}"  varStatus="status">
            <div class="title-container">
                <div class="table-title nobor">部门名: ${list.orgName}</div>
            </div>

            <table class="table table-bordered table-striped table-condensed table-centered">
                <thead>
                <tr>
                    <th class="table-smallest">账期类型</th>
                    <th class="table-smallest">订单实际金额</th>
                    <th class="table-smallest">未还账期金额</th>
                    <th class="table-smallest">逾期未还金额</th>
                    <th class="table-smallest">逾期订单数</th>
                    <th class="table-smallest">逾期天数</th>
                    <th class="table-smallest">订单平均逾期天数</th>
                    <th class="table-smallest">账期现有额度</th>
                    <th class="table-smallest">账期剩余额度</th>
                    <th class="table-smallest">历史逾期次数</th>
                    <th class="table-smallest">账期历史使用次数</th>
                    <th class="table-smallest">账期次数逾期率</th>
                    <th class="table-smallest">历史账期订单总金额</th>
                    <th class="table-smallest">历史账期订单逾期总额</th>
                    <th class="table-smallest">账期金额逾期率</th>
                    <th class="table-smallest">历史逾期订单数</th>
                    <th class="table-smallest">历史逾期天数</th>
                    <th class="table-smallest">历史订单平均逾期天数</th>
                </tr>
                </thead>

                <tbody>
                <tr>
                    <td>正式账期</td>
                    <td>
                        <c:if test="${empty list.applyFormalsReal}">-</c:if>
                        <fmt:formatNumber type="number" value="${list.applyFormalsReal}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td>
                        <c:if test="${empty list.formal.unreturnedAmount}">-</c:if>
                        <fmt:formatNumber type="number" value="${list.formal.unreturnedAmount}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td>
                        <c:if test="${empty list.formal.overdueAmount}">-</c:if>
                        <fmt:formatNumber type="number" value="${list.formal.overdueAmount}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td><c:if test="${empty list.formal.orderCountOfOverdue}">-</c:if> ${ list.formal.orderCountOfOverdue}</td>
                    <td><c:if test="${empty list.formal.daysOfOverdue}">-</c:if>${ list.formal.daysOfOverdue}</td>
                    <td>
                        <c:choose>
                            <c:when test="${(empty list.formal.avgOverdueDaysByOrder || list.formal.avgOverdueDaysByOrder eq 0) && (empty list.formal.orderCountOfOverdue || empty list.formal.daysOfOverdue || list.formal.orderCountOfOverdue eq 0 || list.formal.daysOfOverdue eq 0)}">
                                -
                            </c:when>
                            <c:when test="${(empty list.formal.avgOverdueDaysByOrder || list.formal.avgOverdueDaysByOrder eq 0) && (not empty list.formal.orderCountOfOverdue && not empty list.formal.daysOfOverdue && list.formal.orderCountOfOverdue ne 0 && list.formal.daysOfOverdue ne 0) }">
                                <fmt:formatNumber type="number" value="${(list.formal.daysOfOverdue/list.formal.orderCountOfOverdue) }"  maxFractionDigits="2" />
                            </c:when>
                            <c:otherwise>
                                ${list.formal.avgOverdueDaysByOrder}
                            </c:otherwise>
                        </c:choose>
                    </td>
                    <td>
                        <c:if test="${empty list.formal.applyAmount}">-</c:if>
                        <fmt:formatNumber type="number" value="${list.formal.applyAmount}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td>
                        <c:if test="${empty list.formal.availableAmount}">-</c:if>
                        <fmt:formatNumber type="number" value="${list.formal.availableAmount}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td><c:if test="${empty list.formal.historyCountOfOverdue}">-</c:if>${  list.formal.historyCountOfOverdue}</td>
                    <td><c:if test="${empty list.formal.historyUsedCount}">-</c:if>${  list.formal.historyUsedCount}</td>

                    <td>
                        <c:choose>
                            <c:when test="${empty list.formal.historyPercentOverdueByUsedCount || list.formal.historyPercentOverdueByUsedCount eq 0 }">0%</c:when>
                            <c:otherwise>
                                <fmt:formatNumber type="number" value="${(list.formal.historyPercentOverdueByUsedCount)*100}"  maxFractionDigits="5" />%
                            </c:otherwise>
                        </c:choose>
                    </td>
                    <td>
                        <c:if test="${empty list.applyFormalsHistoryReal}">-</c:if>
                        <fmt:formatNumber type="number" value="${list.applyFormalsHistoryReal}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td>
                        <c:if test="${empty list.formal.historyAmountOfOverdue}">-</c:if>
                        <fmt:formatNumber type="number" value="${list.formal.historyAmountOfOverdue}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td>
                        <c:choose>
                            <c:when test="${ empty list.applyFormalsHistoryReal || list.applyFormalsHistoryReal == 0 || empty (list.formal.historyAmountOfOverdue)}">0%</c:when>
                            <c:otherwise> <fmt:formatNumber type="number" value="${(list.formal.historyAmountOfOverdue*100/list.applyFormalsHistoryReal)}" maxFractionDigits="5" />%</c:otherwise>
                        </c:choose>
                    </td>

                    <td><c:if test="${empty list.formal.historyOrderCountOfOverdue}">-</c:if>${  list.formal.historyOrderCountOfOverdue}</td>
                    <td><c:if test="${empty list.formal.historyDaysOfOverdue}">-</c:if>${  list.formal.historyDaysOfOverdue}</td>
                    <td><c:if test="${empty list.formal.historyAvgDaysOfOverdueByOrder}">-</c:if>${  list.formal.historyAvgDaysOfOverdueByOrder}</td>
                </tr>
                <tr>
                    <td>临时账期</td>
                    <td>
                        <c:if test="${empty list.applyTemporarysReal}">-</c:if>
                        <fmt:formatNumber type="number" value="${list.applyTemporarysReal}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td>
                        <c:if test="${empty list.temporarys.unreturnedAmount}">-</c:if>
                        <fmt:formatNumber type="number" value="${list.temporarys.unreturnedAmount}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td>
                        <c:if test="${empty list.temporarys.overdueAmount}">-</c:if>
                        <fmt:formatNumber type="number" value="${list.temporarys.overdueAmount}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td><c:if test="${empty list.temporarys.orderCountOfOverdue}">-</c:if>${ list.temporarys.orderCountOfOverdue}</td>
                    <td><c:if test="${empty list.temporarys.daysOfOverdue}">-</c:if>${ list.temporarys.daysOfOverdue}</td>
                    <td>
                        <c:choose>
                            <c:when test="${(empty list.temporarys.avgOverdueDaysByOrder || list.temporarys.avgOverdueDaysByOrder eq 0) && (empty list.temporarys.orderCountOfOverdue || empty list.temporarys.daysOfOverdue || list.temporarys.orderCountOfOverdue eq 0 || list.temporarys.daysOfOverdue eq 0 )}">
                                -
                            </c:when>
                            <c:when test="${(empty list.temporarys.avgOverdueDaysByOrder || list.temporarys.avgOverdueDaysByOrder eq 0) && (not empty list.temporarys.orderCountOfOverdue && not empty list.temporarys.daysOfOverdue && list.temporarys.orderCountOfOverdue ne 0 && list.temporarys.daysOfOverdue ne 0 ) }">
                                <fmt:formatNumber type="number" value="${(list.temporarys.daysOfOverdue/list.temporarys.orderCountOfOverdue) }"  maxFractionDigits="2" />
                            </c:when>
                            <c:otherwise>
                                ${list.temporarys.avgOverdueDaysByOrder}
                            </c:otherwise>
                        </c:choose>
                    </td>
                    <td>
                        <c:if test="${empty list.temporarys.applyAmount}">-</c:if>
                        <fmt:formatNumber type="number" value="${list.temporarys.applyAmount}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td>
                        <c:if test="${empty list.temporarys.availableAmount}">-</c:if>
                        <fmt:formatNumber type="number" value="${list.temporarys.availableAmount}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td><c:if test="${empty list.temporarys.historyCountOfOverdue}">-</c:if>${  list.temporarys.historyCountOfOverdue}</td>
                    <td><c:if test="${empty list.temporarys.historyUsedCount}">-</c:if>${  list.temporarys.historyUsedCount}</td>

                    <td>
                        <c:choose>
                            <c:when test="${empty list.temporarys.historyPercentOverdueByUsedCount || list.temporarys.historyPercentOverdueByUsedCount eq 0 }">0%</c:when>
                            <c:otherwise>
                                <fmt:formatNumber type="number" value="${(list.temporarys.historyPercentOverdueByUsedCount)*100}"  maxFractionDigits="5" />%
                            </c:otherwise>
                        </c:choose>
                    </td>
                    <td>
                        <c:if test="${empty list.applyTemporarysHistoryReal}">-</c:if>
                        <fmt:formatNumber type="number" value="${list.applyTemporarysHistoryReal}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td>
                        <c:if test="${empty list.temporarys.historyAmountOfOverdue}">-</c:if>
                        <fmt:formatNumber type="number" value="${list.temporarys.historyAmountOfOverdue}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td>
                        <c:choose>
                            <c:when test="${empty list.applyTemporarysHistoryReal || list.applyTemporarysHistoryReal == 0|| empty (list.temporarys.historyAmountOfOverdue)}">0%</c:when>
                            <c:otherwise><fmt:formatNumber type="number" value="${(list.temporarys.historyAmountOfOverdue*100/list.applyTemporarysHistoryReal)}" maxFractionDigits="5" />%</c:otherwise>
                        </c:choose>
                    </td>

                    <td><c:if test="${empty list.temporarys.historyOrderCountOfOverdue}">-</c:if>${ list.temporarys.historyOrderCountOfOverdue}</td>
                    <td><c:if test="${empty list.temporarys.historyDaysOfOverdue}">-</c:if>${ list.temporarys.historyDaysOfOverdue}</td>
                    <td><c:if test="${empty list.temporarys.historyAvgDaysOfOverdueByOrder}">-</c:if>${ list.temporarys.historyAvgDaysOfOverdueByOrder}</td>
                </tr>
                <tr>
                    <td>订单账期</td>
                    <td>
                        <c:if test="${empty list.applyOrdersReal}">-</c:if>
                        <fmt:formatNumber type="number" value="${list.applyOrdersReal}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td>
                        <c:if test="${empty list.order.unreturnedAmount}">-</c:if>
                        <fmt:formatNumber type="number" value="${list.order.unreturnedAmount}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td>
                        <c:if test="${empty list.order.overdueAmount}">-</c:if>
                        <fmt:formatNumber type="number" value="${list.order.overdueAmount}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td><c:if test="${empty list.order.orderCountOfOverdue}">-</c:if>${  list.order.orderCountOfOverdue}</td>
                    <td><c:if test="${empty list.order.daysOfOverdue}">-</c:if>${  list.order.daysOfOverdue}</td>
                    <td>
                        <c:choose>
                            <c:when test="${(empty list.order.avgOverdueDaysByOrder || list.order.avgOverdueDaysByOrder eq 0) && (empty list.order.orderCountOfOverdue || empty list.order.daysOfOverdue || list.order.orderCountOfOverdue eq 0 || list.order.daysOfOverdue eq 0 )}">
                                -
                            </c:when>
                            <c:when test="${(empty list.order.avgOverdueDaysByOrder || list.order.avgOverdueDaysByOrder eq 0) && (not empty list.order.orderCountOfOverdue && not empty list.order.daysOfOverdue && list.order.orderCountOfOverdue ne 0 && list.order.daysOfOverdue ne 0 ) }">
                                <fmt:formatNumber type="number" value="${(list.order.daysOfOverdue/list.order.orderCountOfOverdue) }"  maxFractionDigits="2" />
                            </c:when>
                            <c:otherwise>
                                ${list.order.avgOverdueDaysByOrder}
                            </c:otherwise>
                        </c:choose>
                    </td>
                    <td>
                        <c:if test="${empty list.order.applyAmount}">-</c:if>
                        <fmt:formatNumber type="number" value="${list.order.applyAmount}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td>
                        <c:if test="${empty list.order.availableAmount}">-</c:if>
                        <fmt:formatNumber type="number" value="${list.order.availableAmount}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td><c:if test="${empty list.order.historyCountOfOverdue}">-</c:if>${  list.order.historyCountOfOverdue}</td>
                    <td><c:if test="${empty list.order.historyUsedCount}">-</c:if>${  list.order.historyUsedCount}</td>

                    <td>
                        <c:choose>
                            <c:when test="${empty list.order.historyPercentOverdueByUsedCount || list.order.historyPercentOverdueByUsedCount eq 0}">0%</c:when>
                            <c:otherwise>
                                <fmt:formatNumber type="number" value="${(list.order.historyPercentOverdueByUsedCount)*100}"  maxFractionDigits="5" />%
                            </c:otherwise>
                        </c:choose>
                    </td>
                    <td>
                        <c:if test="${empty list.applyOrdersHistoryReal}">-</c:if>
                        <fmt:formatNumber type="number" value="${list.applyOrdersHistoryReal}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td>
                        <c:if test="${empty list.order.historyAmountOfOverdue}">-</c:if>
                        <fmt:formatNumber type="number" value="${list.order.historyAmountOfOverdue}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td>
                        <c:choose>
                            <c:when test="${empty list.applyOrdersHistoryReal || list.applyOrdersHistoryReal == 0 || empty (list.order.historyAmountOfOverdue)}">0%</c:when>
                            <c:otherwise><fmt:formatNumber type="number" value="${(list.order.historyAmountOfOverdue*100/list.applyOrdersHistoryReal)}"  maxFractionDigits="5" />%</c:otherwise>
                        </c:choose>
                    </td>

                    <td><c:if test="${empty list.order.historyOrderCountOfOverdue}">-</c:if>${  list.order.historyOrderCountOfOverdue}</td>
                    <td><c:if test="${empty list.order.historyDaysOfOverdue}">-</c:if>${  list.order.historyDaysOfOverdue}</td>
                    <td><c:if test="${empty list.order.historyAvgDaysOfOverdueByOrder}">-</c:if>${  list.order.historyAvgDaysOfOverdueByOrder}</td>
                </tr>
                <tr>
                    <td>合计</td>
                    <td>
                        <c:if test="${empty list.appCountsReal}">-</c:if>
                        <fmt:formatNumber type="number" value="${list.appCountsReal}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td>
                        <c:if test="${empty list.total.unreturnedAmount}">-</c:if>
                        <fmt:formatNumber type="number" value="${list.total.unreturnedAmount}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td>
                        <c:if test="${empty list.total.overdueAmount}">-</c:if>
                        <fmt:formatNumber type="number" value="${list.total.overdueAmount}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td><c:if test="${empty list.total.orderCountOfOverdue}">-</c:if>${  list.total.orderCountOfOverdue}</td>
                    <td><c:if test="${empty list.total.daysOfOverdue}">-</c:if>${  list.total.daysOfOverdue}</td>
                    <td>
                        <c:choose>
                            <c:when test="${(empty list.total.avgOverdueDaysByOrder || list.total.avgOverdueDaysByOrder eq 0) && (empty list.total.orderCountOfOverdue || empty list.total.daysOfOverdue || list.total.orderCountOfOverdue eq 0 || list.total.daysOfOverdue eq 0)}">
                                -
                            </c:when>
                            <c:when test="${(empty list.total.avgOverdueDaysByOrder || list.total.avgOverdueDaysByOrder eq 0) && (not empty list.total.orderCountOfOverdue && not empty list.total.daysOfOverdue && list.total.orderCountOfOverdue ne 0 && list.total.daysOfOverdue ne 0) }">
                                <fmt:formatNumber type="number" value="${(list.total.daysOfOverdue/list.total.orderCountOfOverdue) }"  maxFractionDigits="2" />
                            </c:when>
                            <c:otherwise>
                                ${list.total.avgOverdueDaysByOrder}
                            </c:otherwise>
                        </c:choose>
                    </td>
                    <td>
                        <c:if test="${empty list.total.applyAmount}">-</c:if>
                        <fmt:formatNumber type="number" value="${list.total.applyAmount}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td>
                        <c:if test="${empty list.total.availableAmount}">-</c:if>
                        <fmt:formatNumber type="number" value="${list.total.availableAmount}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td><c:if test="${empty list.total.historyCountOfOverdue}">-</c:if>${  list.total.historyCountOfOverdue}</td>
                    <td><c:if test="${empty list.total.historyUsedCount}">-</c:if>${  list.total.historyUsedCount}</td>

                    <td>
                        <c:choose>
                            <c:when test="${(empty list.total.historyPercentOverdueByUsedCount || list.total.historyPercentOverdueByUsedCount eq 0) || (empty list.total.historyCountOfOverdue || list.total.historyCountOfOverdue eq 0) || (empty list.total.historyUsedCount || list.total.historyUsedCount eq 0)}">0%</c:when>
                            <c:otherwise>
                                <fmt:formatNumber type="number" value="${(list.total.historyCountOfOverdue/list.total.historyUsedCount)*100}"  maxFractionDigits="2" />%
                            </c:otherwise>
                        </c:choose>
                    </td>
                    <td>
                        <c:if test="${empty list.appCountsHistoryReal}">-</c:if>
                        <fmt:formatNumber type="number" value="${list.appCountsHistoryReal}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td>
                        <c:if test="${empty list.total.historyAmountOfOverdue}">-</c:if>
                        <fmt:formatNumber type="number" value="${list.total.historyAmountOfOverdue}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td>
                        <c:choose>
                            <c:when test="${empty list.appCountsHistoryReal || list.appCountsHistoryReal == 0 || empty (list.total.historyAmountOfOverdue)}">0%</c:when>
                            <c:otherwise> <fmt:formatNumber type="number" value="${(list.total.historyAmountOfOverdue*100/list.appCountsHistoryReal)}"  maxFractionDigits="5" />%</c:otherwise>
                        </c:choose>
                    </td>

                    <td><c:if test="${empty list.total.historyOrderCountOfOverdue}">-</c:if>${  list.total.historyOrderCountOfOverdue}</td>
                    <td><c:if test="${empty list.total.historyDaysOfOverdue}">-</c:if>${  list.total.historyDaysOfOverdue}</td>
                    <td><c:if test="${empty list.total.historyAvgDaysOfOverdueByOrder}">-</c:if>${  list.total.historyAvgDaysOfOverdueByOrder}</td>
                </tr>
                </tbody>
            </table>
        </c:forEach>
        <div style="height: 30px;"></div>
    </c:if>
</div>
    <div class="add-tijiao pt25 layui-layer-title bottom-bottom" style="width: 100%;">
        <button class="bt-small bt-bg-style bt-middle bg-light-blue bottom-c"  id="close-layer" type="button" onclick="closeGoBack();">确定</button>
    </div>
</div>
<script type="text/javascript">
    function closeGoBack() {
        window.parent.closableTab.close($(window.parent.document).find('[role=presentation].active .glyphicon.small'),window.parent.closableTab.resizeMove);
    }
</script>
<%@ include file="../../common/footer.jsp"%>