package com.vedeng.erp.saleorder.enums;

public enum SubStatusEnum {
    TO_BE_CONFIRMED("0","待确认"),
    TO_AUDIT("1","待审核"),
    TO_COLLECTION("2", "待收款"),
    TO_SEND_GOODS("3", "待发货"),
    TO_THE_GOODS("4","待收货"),
    TO_MAKE_OUT_INVOICE("5","待开票"),
    FINISHED("6","已完结"),
    CLOSE("7","已关闭"),
    WAIT_CONFIRM("8","待客户确认");





    private String code;
    private String status;

    SubStatusEnum(String code, String status) {
        this.code = code;
        this.status = status;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}