package com.vedeng.erp.kingdee.domain.command;

import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 预处理金蝶费用应付单明细 dto  金蝶入参
 * @date
 */
@NoArgsConstructor
@Data
@Getter
@Setter
public class KingDeePayCommonCommand {

    /**
     * fId 0：表示新增
     * 非0：云星空系统单据FID值，表示修改
     */
    private String fId;

    /**
     * 单据日期 格式yyyy-MM-dd
     * fDate
     */
    private String fDate;
    /**
     * 贝登单据头ID
     */
    private String F_QZOK_BDDJTID;

    /**
     * 业务类型 默认CG
     */
    private String fBusinessType;
    /**
     * 对应入库单 默认CG
     */
    private String fInStockBusType;

    /**
     * 立账类型 默认填写1
     */
    private String fSetAccountType;
    /**
     * fEntityDetail
     */
    private List<FEntityDetail> fEntityDetail;

    /**
     * 单据类型 填单据类型编码，默认YFD01_SYS
     */

    private KingDeeNumberCommand FBillTypeID = new KingDeeNumberCommand();

    /**
     * 供应商 填写供应商编码
     */
    private KingDeeNumberCommand FSUPPLIERID = new KingDeeNumberCommand();

    /**
     * 结算组织 默认101,配置化
     */
    private KingDeeNumberCommand FSETTLEORGID = new KingDeeNumberCommand();

    /**
     * 付款组织 默认101,配置化
     */
    private KingDeeNumberCommand FPAYORGID = new KingDeeNumberCommand();

    /**
     * FEntityDetail
     */
    @NoArgsConstructor
    @Data
    public static class FEntityDetail {

        /**
         * 计价数量 填写数量（退料负数）
         */
        private String fPriceQty;
        /**
         * 含税单价 填写单价 FEntityDetail_Link单价求平均
         */
        private String fTaxPrice;
        /**
         * 税率% 采购税率
         */
        private String fEntryTaxRate;
        /**
         * 贝登单据行ID
         */
        private String F_QZOK_BDDJHID;
        /**
         * 源单类型 入库单默认：STK_InStock 退料单默认：PUR_MRB
         */
        private String fSourceType;

        /**
         * 不含税金额
         */
        private String fNoTaxAmountFor_D;

        /**
         * 税额
         */
        private String fTaxAmountFor_D;

        /**
         * 价税合计
         */
        private String fAllAmountFor_D;

        /**
         * 物料 填写物料编码 SKU
         */
        private KingDeeNumberCommand FMATERIALID = new KingDeeNumberCommand();

        /**
         * FEntityDetail_Link
         */
        private List<FEntityDetailLink> FEntityDetail_Link;

        /**
         * FEntityDetailLink
         */
        @NoArgsConstructor
        @Data
        public static class FEntityDetailLink {
            /**
             * 关联入库必填，默认0
             */
            private String fLinkId;
            /**
             * 关联入库必填默认：AP_InStockToPayableMap 关联退料必填默认：AP_MRBToPayableMap
             */
            private String FEntityDetail_Link_FRuleId;
            /**
             * 关联入库必填，默认0
             */
            private String FEntityDetail_Link_FFlowLineId;
            /**
             * 关联入库必填，默认0
             */
            private String FEntityDetail_Link_FSTableId;
            /**
             * 关联入库必填默认：T_STK_INSTOCKENTRY 关联退料必填：T_PUR_MRBENTRY
             */
            private String FEntityDetail_Link_FSTableName;
            /**
             * 关联入库必填，关联入库内码，填写金蝶入库单内码FID
             */
            private String FEntityDetail_Link_FSBillId;
            /**
             * 关联入库必填，关联入库单行内码,填写金蝶入库单行内码FEntryID
             */
            private String FEntityDetail_Link_FSId;
            /**
             * 取入库可关联数量（退料负数）
             */
            private String FEntityDetail_Link_FBASICUNITQTYOld;
            /**
             * 取入库关联数量（退料负数）
             */
            private String FEntityDetail_Link_FBASICUNITQTY;
            /**
             * 取入库可关联数量（退料负数）
             */
            private String FEntityDetail_Link_FStockBaseQtyOld;
            /**
             * 取入库关联数量（退料负数）
             */
            private String FEntityDetail_Link_FStockBaseQty;
            /**
             * 取入库关联金额 （可关联入库数量*新入库含税单价）（退料负数）
             */
            private String FEntityDetail_Link_FALLAMOUNTFOR_DOld;
            /**
             * 取入库可关联金额 （可关联入库数量*原入库含税单价）（退料负数）
             */
            private String FEntityDetail_Link_FALLAMOUNTFOR_D;
        }
    }
}
