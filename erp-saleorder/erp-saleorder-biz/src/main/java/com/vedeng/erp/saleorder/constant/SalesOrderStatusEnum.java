package com.vedeng.erp.saleorder.constant;

/**
 * <AUTHOR>
 * @ClassName SalesOrderStatusEnum.java
 * @Description TODO 销售订单状态
 * @createTime 2021年10月19日 15:05:00
 */
public enum SalesOrderStatusEnum {
    ORDER_READY(0,"待确认"),
    ORDER_WAY(1,"进行中"),
    ORDER_FINISH(2,"已完结"),
    ORDER_CLOSE(3,"已关闭"),


    LOCKED_STATUS(1,"订单锁定"),
    UNLOCKED_STATUS(0,"订单未锁定"),

    PAYMENT_STATUS_UN(0,"未付款"),
    PAYMENT_STATUS_HALF(1,"部分付款"),
    PAYMENT_STATUS_ALL(2,"全部付款"),

    INVOICE_STATUS_UN(0,"未开票"),
    INVOICE_STATUS_HALF(1,"部分开票"),
    INVOICE_STATUS_ALL(2,"全部开票"),

    DELIVERY_STATUS_UN(0,"未发货"),
    DELIVERY_STATUS_HALF(1,"部分发货"),
    DELIVERY_STATUS_ALL(2,"全部发货"),

    ARRIVAL_STATUS_UN(0,"未到货"),
    ARRIVAL_STATUS_HALF(1,"部分到货"),
    ARRIVAL_STATUS_ALL(2,"全部到货"),
    ;
    private Integer status;

    private String desc;

    SalesOrderStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
