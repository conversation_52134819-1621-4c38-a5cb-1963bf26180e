package com.newtask;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.wms.service.LogicalSaleorderChooseService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description 白名单中客户的销售订单（账期类型）下发WMS
 * <AUTHOR>
 */
@Component
@JobHandler(value = "whitelist2DeliverWMSTask")
public class whitelist2DeliverWMSTask extends AbstractJobHandler {


    @Autowired
    private LogicalSaleorderChooseService logicalSaleorderChooseServiceImpl;

    @Autowired
    private SaleorderMapper saleorderMapper;


    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        XxlJobLogger.log("===================白名单中客户的销售订单（账期类型）下发WMS开始===================");
        // 获取白名单中有效的客户id集合（未下发WMS）
        List<Integer> traderIdList = saleorderMapper.getTraderIdListInWhitelist();
        if (CollectionUtils.isNotEmpty(traderIdList)) {
            XxlJobLogger.log("有效的白名单客户id集合：{}", traderIdList.stream().map(Object::toString).collect(Collectors.joining(",")));
            // 获取白名单客户的销售订单（账期类型）,且满足可发货的校验规则
            List<Saleorder> saleOrderList = saleorderMapper.getSaleOrderByTraderIdListInWhitelist(traderIdList);
            if (CollectionUtils.isNotEmpty(saleOrderList)) {
                List<String> saleOrderNoList = saleOrderList.stream().map(Saleorder::getSaleorderNo).collect(Collectors.toList());
                XxlJobLogger.log("满足可发货校验规则的销售订单（账期类型）订单单号：{}", saleOrderNoList);
                for (Saleorder saleorder : saleOrderList) {
                    // 下发WMS
                    try {
                        logicalSaleorderChooseServiceImpl.chooseLogicalSaleorder(saleorder, null);
                    } catch (Exception e) {
                        XxlJobLogger.log("销售订单单号：{}，白名单中客户的销售订单（账期类型）下发WMS异常：{}",
                                saleorder.getSaleorderNo(), e.getMessage());
                    }
                }
            }
            // 更新白名单中的是否下发WMS状态
            saleorderMapper.updateIssueWMSByTraderIdList(traderIdList);
        }
        XxlJobLogger.log("===================白名单中客户的销售订单（账期类型）下发WMS结束===================");
        return SUCCESS;
    }

}
