<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.mapper.SkuMainDeptMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.goods.domain.entity.SkuMainDept">
    <!--@mbg.generated-->
    <!--@Table T_SKU_MAIN_DEPT-->
    <id column="SKU_MAIN_DEPT_ID" jdbcType="INTEGER" property="skuMainDeptId" />
    <result column="SKU_NO" jdbcType="VARCHAR" property="skuNo" />
    <result column="MAIN_DEPT" jdbcType="VARCHAR" property="mainDept" />
    <result column="IS_DELETE" jdbcType="TINYINT" property="isDelete" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    SKU_MAIN_DEPT_ID, SKU_NO, MAIN_DEPT, IS_DELETE, CREATOR, ADD_TIME, UPDATER, MOD_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_SKU_MAIN_DEPT
    where SKU_MAIN_DEPT_ID = #{skuMainDeptId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_SKU_MAIN_DEPT
    where SKU_MAIN_DEPT_ID = #{skuMainDeptId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="SKU_MAIN_DEPT_ID" keyProperty="skuMainDeptId" parameterType="com.vedeng.goods.domain.entity.SkuMainDept" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SKU_MAIN_DEPT (SKU_NO, MAIN_DEPT, IS_DELETE, 
      CREATOR, ADD_TIME, UPDATER, 
      MOD_TIME)
    values (#{skuNo,jdbcType=VARCHAR}, #{mainDept,jdbcType=VARCHAR}, #{isDelete,jdbcType=TINYINT},
      #{creator,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER}, 
      #{modTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="SKU_MAIN_DEPT_ID" keyProperty="skuMainDeptId" parameterType="com.vedeng.goods.domain.entity.SkuMainDept" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SKU_MAIN_DEPT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="skuNo != null and skuNo != ''">
        SKU_NO,
      </if>
      <if test="mainDept != null and mainDept != ''">
        MAIN_DEPT,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="skuNo != null and skuNo != ''">
        #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="mainDept != null and mainDept != ''">
        #{mainDept,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.domain.entity.SkuMainDept">
    <!--@mbg.generated-->
    update T_SKU_MAIN_DEPT
    <set>
      <if test="skuNo != null and skuNo != ''">
        SKU_NO = #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="mainDept != null and mainDept != ''">
        MAIN_DEPT = #{mainDept,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where SKU_MAIN_DEPT_ID = #{skuMainDeptId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.domain.entity.SkuMainDept">
    <!--@mbg.generated-->
    update T_SKU_MAIN_DEPT
    set SKU_NO = #{skuNo,jdbcType=VARCHAR},
      MAIN_DEPT = #{mainDept,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=TINYINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP}
    where SKU_MAIN_DEPT_ID = #{skuMainDeptId,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_SKU_MAIN_DEPT
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="SKU_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_MAIN_DEPT_ID = #{item.skuMainDeptId,jdbcType=INTEGER} then #{item.skuNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="MAIN_DEPT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_MAIN_DEPT_ID = #{item.skuMainDeptId,jdbcType=INTEGER} then #{item.mainDept,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_MAIN_DEPT_ID = #{item.skuMainDeptId,jdbcType=INTEGER} then #{item.isDelete,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_MAIN_DEPT_ID = #{item.skuMainDeptId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_MAIN_DEPT_ID = #{item.skuMainDeptId,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_MAIN_DEPT_ID = #{item.skuMainDeptId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_MAIN_DEPT_ID = #{item.skuMainDeptId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
    </trim>
    where SKU_MAIN_DEPT_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.skuMainDeptId,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_SKU_MAIN_DEPT
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="SKU_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.skuNo != null">
            when SKU_MAIN_DEPT_ID = #{item.skuMainDeptId,jdbcType=INTEGER} then #{item.skuNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="MAIN_DEPT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.mainDept != null">
            when SKU_MAIN_DEPT_ID = #{item.skuMainDeptId,jdbcType=INTEGER} then #{item.mainDept,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDelete != null">
            when SKU_MAIN_DEPT_ID = #{item.skuMainDeptId,jdbcType=INTEGER} then #{item.isDelete,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when SKU_MAIN_DEPT_ID = #{item.skuMainDeptId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when SKU_MAIN_DEPT_ID = #{item.skuMainDeptId,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when SKU_MAIN_DEPT_ID = #{item.skuMainDeptId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when SKU_MAIN_DEPT_ID = #{item.skuMainDeptId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
    </trim>
    where SKU_MAIN_DEPT_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.skuMainDeptId,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="SKU_MAIN_DEPT_ID" keyProperty="skuMainDeptId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SKU_MAIN_DEPT
    (SKU_NO, MAIN_DEPT, IS_DELETE, CREATOR, ADD_TIME, UPDATER, MOD_TIME)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.skuNo,jdbcType=VARCHAR}, #{item.mainDept,jdbcType=VARCHAR}, #{item.isDelete,jdbcType=TINYINT},
        #{item.creator,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.updater,jdbcType=INTEGER}, 
        #{item.modTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="SKU_MAIN_DEPT_ID" keyProperty="skuMainDeptId" parameterType="com.vedeng.goods.domain.entity.SkuMainDept" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SKU_MAIN_DEPT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="skuMainDeptId != null">
        SKU_MAIN_DEPT_ID,
      </if>
      SKU_NO,
      MAIN_DEPT,
      IS_DELETE,
      CREATOR,
      ADD_TIME,
      UPDATER,
      MOD_TIME,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="skuMainDeptId != null">
        #{skuMainDeptId,jdbcType=INTEGER},
      </if>
      #{skuNo,jdbcType=VARCHAR},
      #{mainDept,jdbcType=VARCHAR},
      #{isDelete,jdbcType=TINYINT},
      #{creator,jdbcType=INTEGER},
      #{addTime,jdbcType=TIMESTAMP},
      #{updater,jdbcType=INTEGER},
      #{modTime,jdbcType=TIMESTAMP},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="skuMainDeptId != null">
        SKU_MAIN_DEPT_ID = #{skuMainDeptId,jdbcType=INTEGER},
      </if>
      SKU_NO = #{skuNo,jdbcType=VARCHAR},
      MAIN_DEPT = #{mainDept,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=TINYINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="SKU_MAIN_DEPT_ID" keyProperty="skuMainDeptId" parameterType="com.vedeng.goods.domain.entity.SkuMainDept" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SKU_MAIN_DEPT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="skuMainDeptId != null">
        SKU_MAIN_DEPT_ID,
      </if>
      <if test="skuNo != null and skuNo != ''">
        SKU_NO,
      </if>
      <if test="mainDept != null and mainDept != ''">
        MAIN_DEPT,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="skuMainDeptId != null">
        #{skuMainDeptId,jdbcType=INTEGER},
      </if>
      <if test="skuNo != null and skuNo != ''">
        #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="mainDept != null and mainDept != ''">
        #{mainDept,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="skuMainDeptId != null">
        SKU_MAIN_DEPT_ID = #{skuMainDeptId,jdbcType=INTEGER},
      </if>
      <if test="skuNo != null and skuNo != ''">
        SKU_NO = #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="mainDept != null and mainDept != ''">
        MAIN_DEPT = #{mainDept,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <select id="getMainDeptBySkuNo" resultType="com.vedeng.goods.dto.SkuMainDeptDto">
    select
    <include refid="Base_Column_List" />
    from T_SKU_MAIN_DEPT
    where SKU_NO = #{skuNo,jdbcType=VARCHAR}
  </select>
</mapper>