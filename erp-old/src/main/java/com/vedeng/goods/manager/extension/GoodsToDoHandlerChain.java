package com.vedeng.goods.manager.extension;


import com.vedeng.goods.manager.extension.handler.GoodsTodoHandler;
import com.vedeng.goods.manager.rule.GoodsExtValidElement;

import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class GoodsToDoHandlerChain {

    private final GoodsToDoHandlerContext context = GoodsToDoHandlerContext.getInstance();

    private List<GoodsTodoHandler> handlers;

    private boolean failFast = false;

    private List<GoodsExtValidElement> goodsExtValidElements;

    /**
     * Do handlers.
     *
     * @return
     */
    public void handle(Integer skuId) {
        if (handlers == null || handlers.isEmpty()) {
            return;
        }

        context.setGoodsId(skuId);
        try {
            for (GoodsTodoHandler handler : handlers) {
                GoodsExtValidElement goodsExtValidElement = getByServiceId(handler.getServiceId());
                if (!goodsExtValidElement.getRequired()) {
                    continue;
                }
                handler.onHandler();
            }
        } finally {
            context.remove();
        }
    }


    public GoodsValidResultVo checkTodoItems(Integer skuId) {
        GoodsValidResultVo goodsValidResultVo = new GoodsValidResultVo();
        if (handlers == null || handlers.isEmpty()) {
            return goodsValidResultVo;
        }

        context.setGoodsId(skuId);
        try {
            for (GoodsTodoHandler handler : handlers) {
                GoodsExtValidElement goodsExtValidElement = getByServiceId(handler.getServiceId());
                if (!goodsExtValidElement.getRequired()) {
                    continue;
                }

                boolean done = handler.isDone();
                if (!done) {
                    goodsValidResultVo.addItemFailed(goodsExtValidElement.getServiceId(), goodsExtValidElement.getServiceName(), goodsExtValidElement.getMessage());
                } else {
                    goodsValidResultVo.addItemSuccessfully(goodsExtValidElement.getServiceId(), goodsExtValidElement.getServiceName());
                }

                if (!done && failFast) {
                    break;
                }
            }
        } finally {
            context.remove();
        }
        return goodsValidResultVo;
    }


    public List<GoodsTodoResultVo> hasTodoItems(Integer skuId) {
        List<GoodsTodoResultVo> goodsTodoItemVoList = new LinkedList<>();
        if (handlers == null || handlers.isEmpty()) {
            return goodsTodoItemVoList;
        }

        context.setGoodsId(skuId);
        try {
            for (GoodsTodoHandler handler : handlers) {
                GoodsExtValidElement goodsExtValidElement = getByServiceId(handler.getServiceId());

                boolean finished = handler.hasTodoItems();
                GoodsTodoResultVo goodsTodoItemVo = new GoodsTodoResultVo();
                goodsTodoItemVo.setServiceId(goodsExtValidElement.getServiceId());
                goodsTodoItemVo.setServiceName(goodsExtValidElement.getServiceName());
                goodsTodoItemVo.setDone(!finished);
                goodsTodoItemVoList.add(goodsTodoItemVo);
            }
        } finally {
            context.remove();
        }
        return goodsTodoItemVoList;
    }


    public void addHandler(GoodsTodoHandler handler) {
        if (handler == null) {
            return;
        }
        if (this.handlers == null) {
            handlers = new LinkedList<>();
        }
        handlers.add(handler);
    }

    public void setGoodsExtValidElements(List<GoodsExtValidElement> goodsExtValidElements) {
        this.goodsExtValidElements = Collections.unmodifiableList(goodsExtValidElements);
    }

    public void setFailFast(boolean failFast) {
        this.failFast = failFast;
    }

    private GoodsExtValidElement getByServiceId(Integer serviceId) {
        Optional<GoodsExtValidElement> optional = goodsExtValidElements.stream().filter(item -> item.getServiceId().equals(serviceId)).findFirst();
        if (!optional.isPresent()) {
            throw new IllegalStateException();
        }
        return optional.get();
    }
}
