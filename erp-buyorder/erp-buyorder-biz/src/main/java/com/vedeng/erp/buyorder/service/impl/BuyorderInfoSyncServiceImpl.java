package com.vedeng.erp.buyorder.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.dao.AfterSalesInvoiceMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesGoods;
import com.vedeng.aftersales.model.AfterSalesInvoice;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.erp.buyorder.service.BuyorderInfoSyncService;
import com.vedeng.finance.dao.InvoiceMapper;
import com.vedeng.logistics.dao.ExpressDetailMapper;
import com.vedeng.logistics.dao.ExpressMapper;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.BuyorderGoods;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.order.model.vo.BuyorderVo;
import com.vedeng.order.model.vo.SaleorderGoodsVo;
import com.vedeng.order.service.BuyorderDataService;
import com.vedeng.order.service.BuyorderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Description com.vedeng.erp.buyorder.service.impl
 * @Date 2021/11/15 9:49
 */
@Slf4j
@Service
public class BuyorderInfoSyncServiceImpl implements BuyorderInfoSyncService {

    @Resource
    private BuyorderMapper buyorderMapper;

    @Resource
    private ExpressMapper expressMapper;

    @Resource
    private BuyorderGoodsMapper buyorderGoodsMapper;

    @Autowired
    private BuyorderService buyorderService;

    @Autowired
    private AfterSalesGoodsMapper afterSalesGoodsMapper;

    @Autowired
    private ExpressDetailMapper expressDetailMapper;

    @Autowired
    private InvoiceMapper invoiceMapper;

    @Autowired
    private BuyorderDataService buyorderDataService;

    @Resource
    private AfterSalesMapper afterSalesMapper;

    @Resource
    private AfterSalesInvoiceMapper afterSalesInvoiceMapper;

    private static final Integer TH_BUYORDER_AFTER_SALE_CODE = 546;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncDeliveryStatus(Integer buyorderId, Integer point) {
        calculateDeliveryStatus(buyorderId,point);
    }

    @Override
    public void syncDeliveryStatusForExecutionListener(Integer buyorderId, Integer point) {
        calculateDeliveryStatus(buyorderId,point);
    }


    private void calculateDeliveryStatus(Integer buyorderId, Integer point){
        BuyorderVo buyorderVo = buyorderMapper.getBuyorderVoById(buyorderId);
        buyorderVo.setBuyorderGoodsVoList(buyorderGoodsMapper.getBuyorderGoodsVoListByBuyorderId(buyorderId));
        if (buyorderVo.getIsNew() == 1) {
            Buyorder buyorder = new Buyorder();
            buyorder.setBuyorderId(buyorderId);
            buyorder.setDeliveryStatus(buyorderVo.getDeliveryStatus()); // 先赋上原来的值，防止一个条件都没满足，传入mybatis造成sql错误

            Integer totalArrivalNum = 0; // 收货数量之和
            Integer totalPurchaseNum = 0; // 采购数量（现值）之和
            List<BuyorderGoodsVo> buyorderGoodsVoList = buyorderVo.getBuyorderGoodsVoList();
            Integer num = expressMapper.getTotalNumInExpressDetailByBuyorderId(buyorderId);

            for (BuyorderGoodsVo buyorderGoodsVo : buyorderGoodsVoList) {
                totalArrivalNum += buyorderGoodsVo.getArrivalNum();
                totalPurchaseNum += buyorderGoodsVo.getNum() - buyorderGoodsVo.getAfterReturnNum();
            }
            if (point == 1) { // 确认收货 或wms回传入库记录
                if (totalArrivalNum.equals(totalPurchaseNum) && buyorderVo.getArrivalStatus() == 2) {
                    buyorder.setDeliveryStatus(2);
                } else if (totalArrivalNum < totalPurchaseNum && buyorderVo.getArrivalStatus() == 1) {
                    if (Objects.isNull(num) || num == 0) {
                        buyorder.setDeliveryStatus(1);
                    } else if (num < totalPurchaseNum) {
                        buyorder.setDeliveryStatus(1);
                    } else if (num.equals(totalPurchaseNum)) {
                        buyorder.setDeliveryStatus(2);
                    }
                }
                buyorder.setDeliveryTime(System.currentTimeMillis());
                log.info("订单流-确认收货或wms回传入库记录更改发货状态{}",buyorderVo.getBuyorderNo());
            } else if (point == 2) { // 新增/编辑/删除快递信息
                if (Objects.isNull(num) || num == 0) {
                    buyorder.setDeliveryStatus(0);
                } else if (num < totalPurchaseNum) {
                    buyorder.setDeliveryStatus(1);
                } else if (num.equals(totalPurchaseNum)) {
                    buyorder.setDeliveryStatus(2);
                }
                buyorder.setDeliveryTime(System.currentTimeMillis());
                log.info("订单流-新增编辑删除快递信息更改发货状态{}",buyorderVo.getBuyorderNo());
            } else if (point == 3) { // 采购售后退货完结
                if (buyorderVo.getDeliveryStatus() == 1 && (totalPurchaseNum <= totalArrivalNum || (!Objects.isNull(num) && totalPurchaseNum <= num))) {
                    buyorder.setDeliveryStatus(2);
                    buyorder.setDeliveryTime(System.currentTimeMillis());
                    log.info("订单流-采购售后退货完结更改发货状态{}-totalPurchaseNum{}-totalArrivalNum{}-num{}" +
                            "", buyorderVo.getBuyorderNo(),totalPurchaseNum,totalArrivalNum,num);
                }
            }
            buyorderMapper.updateByPrimaryKeySelective(buyorder);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void closeBuyorderOnCondition(Integer buyorderId) {
        closeBuyOrder(buyorderId);
    }

    @Override
    public void closeBuyorderOnConditionForExecutionListener(Integer buyorderId) {
        closeBuyOrder(buyorderId);
    }


    private void closeBuyOrder(Integer buyorderId){
        List<BuyorderGoodsVo> buyorderGoodsListByBuyorderId = buyorderGoodsMapper.getBuyorderGoodsListByBuyorderId(buyorderId);
        if(CollectionUtils.isEmpty(buyorderGoodsListByBuyorderId)){
            return;
        }
        boolean closeFlag = true;
        Optional<BuyorderGoodsVo> first = buyorderGoodsListByBuyorderId.stream().filter(e->!ErpConst.ZERO.equals(e.getRealNum())).findFirst();
        if(first.isPresent()){
            closeFlag = false;
        }
        if(closeFlag){
            buyorderService.dealExpeditingByCloseOrder(buyorderId);
            Buyorder buyorder = new Buyorder();
            buyorder.setBuyorderId(buyorderId);
            buyorder.setStatus(3);
            buyorderMapper.updateByPrimaryKeySelective(buyorder);
            log.info("采购单已经全部退货完成，关闭采购单，buyorderId：{}",buyorderId);

            buyorderService.dealUrgeTicketBuyCloseOrder(buyorderMapper.selectByPrimaryKey(buyorderId).getBuyorderNo());
        }
    }


    @Override
    public void updateBuyorderForBuyorderReturnOver(Integer afterSalesId, Integer buyorderId) {
        log.info("updateBuyorderForBuyorderReturnOver afterSalesId:{},buyorderId:{}", afterSalesId, buyorderId);

        Buyorder so = buyorderMapper.selectByPrimaryKey(buyorderId);
        //采购
        Buyorder buyorder = new Buyorder();

        buyorder.setBuyorderId(buyorderId);
        buyorder.setServiceStatus(ErpConst.TWO);//售后关闭
        buyorder.setLockedStatus(ErpConst.ZERO);
        //退货时刷新采购单的发货、收货、付款、开票状态
        //采购单商品发货收货状态
        List<SaleorderGoodsVo> slist = afterSalesGoodsMapper.getReturnGoodsList(afterSalesId);

        if(slist != null && slist.size() > 0){

            //当前售后单中采购商品和总退货数量
            List<BuyorderGoodsVo> buyAfterList = afterSalesGoodsMapper.getBuyorderAfterSalesNumByIds(slist);
            List<BuyorderGoodsVo> bgList = buyorderGoodsMapper.getBuyorderGoodsVoListByBuyorderIdNoSpecial(buyorderId);

            //商品已发货数量
            List<BuyorderGoodsVo> deliveryList = expressDetailMapper.batchBuyorderAllDeliveryNum(bgList);

            BuyorderGoods buyorderGoods = null;

            for (BuyorderGoodsVo bg : bgList) {

                //每个商品的发货数量,后面刷采购单的发货状态用
                for (BuyorderGoodsVo dbg : deliveryList) {

                    if(bg.getBuyorderGoodsId().equals(dbg.getBuyorderGoodsId())){
                        bg.setDeliveryNum(dbg.getDeliveryNum());
                        break;
                    }

                }

                //排除售后刷新采购数量
                for (BuyorderGoodsVo bgv : buyAfterList) {

                    if(bg.getBuyorderGoodsId().equals(bgv.getBuyorderGoodsId()) && ErpConst.ONE.equals(bg.getArrivalStatus())){

                        buyorderGoods = new BuyorderGoods();
                        buyorderGoods.setBuyorderGoodsId(bg.getBuyorderGoodsId());

                        if(bg.getNum() - bgv.getAfterReturnNum() <= bg.getArrivalNum() && bg.getArrivalNum() > 0){

                            buyorderGoods.setArrivalStatus(ErpConst.TWO);
                            bg.setArrivalStatus(ErpConst.TWO);
                            bg.setNum(bg.getNum() - bgv.getAfterReturnNum());

                            log.info("采购售后完结更新售后商品状态信息 buyorderGoods:{}", JSON.toJSONString(buyorderGoods));
                            buyorderGoodsMapper.updateByPrimaryKeySelectiveDB(buyorderGoods);

                        }

                    }

                }

            }
            //订单发货状态
            if(ErpConst.ONE.equals(so.getDeliveryStatus())){

                boolean isAllDelivery = true;//默认全部发货

                for (BuyorderGoodsVo bg : bgList) {

                    if((bg.getDeliveryNum() != null && bg.getNum() > bg.getDeliveryNum()) || bg.getDeliveryNum() == null){
                        isAllDelivery = false;
                        break;
                    }

                }

                if(isAllDelivery){
                    buyorder.setDeliveryStatus(ErpConst.TWO);
                }

            }

            //订单收货状态
            if(ErpConst.ONE.equals(so.getArrivalStatus())){

                boolean isAllArrival = true;//默认全部收货

                for (BuyorderGoodsVo bg : bgList) {

                    if(bg.getArrivalStatus() == 1 || bg.getArrivalStatus() == 0){
                        isAllArrival = false;
                        break;
                    }

                }

                if(isAllArrival){
                    buyorder.setArrivalStatus(ErpConst.TWO);
                }

            }

            BigDecimal afterAmount = invoiceMapper.getBuyOrderAfterRecord(buyorderId);

            //录票状态
            setInvoiceStatus(buyorderId, so, buyorder, afterAmount);

            if(ErpConst.ONE.equals(so.getPaymentStatus())){

                //付款状态
                BigDecimal realAmount = buyorderDataService.getPaymentAndPeriodAmount(buyorderId);

                if(so.getTotalAmount().subtract(afterAmount).compareTo(realAmount) <= 0 && realAmount.compareTo(new BigDecimal(0)) > 0){
                    buyorder.setPaymentStatus(ErpConst.TWO);
                }

            }

        }

        buyorderMapper.updateByPrimaryKeySelective(buyorder);

    }

    /**
     * 更新采购收票状态
	 *
     * @param buyorderId
	 * @param so
	 * @param buyorder
	 * @param afterAmount
	 */
    private void setInvoiceStatus(Integer buyorderId, Buyorder so, Buyorder buyorder, BigDecimal afterAmount) {

        if(so == null || so.getInvoiceStatus() == null || so.getInvoiceStatus() != 1){
            return;
        }

        /**
         * 采购单录票金额
         */
        BigDecimal amount = invoiceMapper.getBuyRecordInvoiceAmount(buyorderId);

        log.info("参数：{}录票金额{}，总金额{}，售后金额{}，buyOrderId:{}",afterAmount,amount,so.getTotalAmount(),afterAmount,buyorderId);

        if (amount == null || amount.compareTo(BigDecimal.ZERO) < 1){
            log.info("采购售后单完结，采购单未进行有效录票 buyOrderId:{}", buyorderId);
            return;
        }

        if (amount.subtract(so.getTotalAmount().subtract(afterAmount)).abs().compareTo(BigDecimal.ONE) > 0){
            log.info("采购售后单完结，采购单录票金额不符合微小差异 buyOrderId:{}", buyorderId);
            return;
        }

        log.info("采购售后单完结，更新采购收票状态为全部收票 buyOrderId:{}", buyorderId);
        buyorder.setInvoiceStatus(ErpConst.TWO);

    }


    @Override
    public void calculateAndUpdateInvoiceRefundStatus(Integer afterSalesId) {
        // step1 先查出售后单信息
        AfterSales afterSales = afterSalesMapper.selectByPrimaryKey(afterSalesId);
        if (TH_BUYORDER_AFTER_SALE_CODE.equals(afterSales.getType())) {
            AfterSales afterSalesInfo = new AfterSales();
            afterSalesInfo.setAfterSalesId(afterSalesId);

            // VDERP-16204 供应商返利 校验售后单的退票状态
            log.info("采购退货售后单审核通过，校验返利逻辑，售后单id：{}", afterSalesId);
            if (this.judgeIsAllRebateGoods(afterSalesId)) {
                log.info("采后退货售后单审核通过，全部返利更新为全部退票，售后单id：{}", afterSalesId);
                afterSalesInfo.setInvoiceRefundStatus(ErpConst.THREE);
            } else {
                //获取售后单的所有退票信息
                List<AfterSalesInvoice> afterSalesInvoiceList = afterSalesInvoiceMapper.getAfterSalesInvoiceByAfterSaleId(afterSales.getAfterSalesId());
                log.info("售后单ID{},查询到的售后退票信息{},", afterSalesId, JSON.toJSONString(afterSalesInvoiceList));
                if (CollectionUtils.isEmpty(afterSalesInvoiceList)) {
                    afterSalesInfo.setInvoiceRefundStatus(ErpConst.ZERO);
                } else {
                    List<AfterSalesInvoice> afterSalesNeedInvoiceList = afterSalesInvoiceList.stream().filter(
                            item -> ErpConst.ONE.equals(item.getIsRefundInvoice())
                    ).collect(Collectors.toList());

                    Optional<AfterSalesInvoice> needReturn = afterSalesNeedInvoiceList.stream().filter(item -> ErpConst.ONE.equals(item.getIsRefundInvoice())
                            && ErpConst.ZERO.equals(item.getStatus())).findAny();
                    Optional<AfterSalesInvoice> alreadyReturn = afterSalesNeedInvoiceList.stream().filter(item -> ErpConst.ONE.equals(item.getStatus())).findAny();

                    // step4 更新退票状态
                    if (needReturn.isPresent()) {
                        //存在需要退票的发票
                        if (alreadyReturn.isPresent()) {
                            //存在已经退票的发票
                            //部分退票
                            afterSalesInfo.setInvoiceRefundStatus(ErpConst.TWO);
                        } else {
                            //未退票
                            afterSalesInfo.setInvoiceRefundStatus(ErpConst.ONE);
                        }
                    } else {
                        //全部退票
                        afterSalesInfo.setInvoiceRefundStatus(ErpConst.THREE);
                    }
                }
            }
            afterSalesMapper.updateByPrimaryKeySelective(afterSalesInfo);
            log.info("计算并更新采购售后退货订单退票状态，售后单信息：{}", JSON.toJSONString(afterSalesInfo));
        }
    }

    /**
     * 判断采购售后单sku是否全部为 返利商品
     *
     * @param afterSalesId 售后单id
     * @return true|| false
     */
    private Boolean judgeIsAllRebateGoods(Integer afterSalesId) {
        List<AfterSalesGoods> afterSaleGoodsList = afterSalesGoodsMapper.getAfterSalesGoodsByAfterSalesId(afterSalesId);
        List<Integer> buyOrderGoodsIdList = afterSaleGoodsList.stream().map(AfterSalesGoods::getOrderDetailId).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(buyOrderGoodsIdList)) {
            List<BuyorderGoodsVo> buyOrderGoodsList = buyorderGoodsMapper.getByBuyOrderGoodsIdList(buyOrderGoodsIdList);
            // 只要存在任意一个返利单价 不等于 采购商品单价的，就认为不满足 “该采购单的商品全是返利商品”
            Optional<BuyorderGoodsVo> buyOrderGoodsVo = buyOrderGoodsList.stream().filter(item -> item.getPrice().compareTo(item.getRebatePrice()) != 0).findAny();
            return !buyOrderGoodsVo.isPresent();
        } else {
            return false;
        }
    }


    @Override
    public void backBuyorderStatus(Integer buyorderId) {
        Buyorder buyorder = buyorderMapper.selectByPrimaryKey(buyorderId);
        if(!ErpConst.TWO.equals(buyorder.getInvoiceStatus())){
            //未全部收票
            if(ErpConst.TWO.equals(buyorder.getStatus())){
                //已完结的情况下恢复到进行中
                Buyorder update = new Buyorder();
                update.setBuyorderId(buyorderId);
                update.setStatus(ErpConst.ONE);
                buyorderMapper.updateByPrimaryKeySelective(update);
            }
        }
    }
}
