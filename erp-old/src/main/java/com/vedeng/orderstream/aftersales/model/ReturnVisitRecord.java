package com.vedeng.orderstream.aftersales.model;

import java.io.Serializable;
import lombok.Data;

/**
 * T_RETURN_VISIT_RECORD
 * <AUTHOR>
@Data
public class ReturnVisitRecord implements Serializable {
    /**
     * 回访记录ID
     */
    private Integer returnVisitRecordId;

    /**
     * 售后订单ID
     */
    private Integer afterSalesId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户性质 1终端 2经销商
     */
    private Integer customerNature;

    /**
     * 手机
     */
    private String customerMobile;

    /**
     * 回访状态, 1已回访,2不知情,3拒绝回答,4拒接,5不知情
     */
    private Integer status;

    /**
     * 回访部门, 1医修帮,2售后
     */
    private Integer returnVisitDepartment;

    /**
     * 录音ID
     */
    private Integer soundRecordId;

    /**
     * 服务响应分值
     */
    private Integer serviceResponseScore;

    /**
     * 服务态度分值
     */
    private Integer serviceAttitudeScore;

    /**
     * 服务能力分值
     */
    private Integer serviceCapabilityScore;

    /**
     * 是否有投诉 0 无, 1 有
     */
    private Integer isComplaint;

    /**
     * 是否有推荐 0 无, 1 有
     */
    private Integer isRecommend;

    /**
     * 最终分值
     */
    private Double totalScore;

    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private Long modTime;

    /**
     * 更新人
     */
    private Integer updater;

    /**
     * 备注
     */
    private String comments;

    private static final long serialVersionUID = 1L;
}