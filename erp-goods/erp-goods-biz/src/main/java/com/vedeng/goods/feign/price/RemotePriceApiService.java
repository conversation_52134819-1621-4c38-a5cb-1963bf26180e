package com.vedeng.goods.feign.price;

import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.common.feign.constants.ServerConstants;
import com.vedeng.price.api.price.dto.ResultInfo;
import com.vedeng.price.api.price.dto.price.*;
import feign.Headers;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description com.vedeng.goods.feginApi
 * @Date 2022/1/10 17:35
 */
@FeignApi(serverName = ServerConstants.PRICE_SERVER)
public interface RemotePriceApiService {

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /sku_price_info/findSkuPriceInfoBySkuNo")
    RestfulResult<SkuPriceInfoDetailResponseDto> findSkuPriceInfoBySkuNo(@RequestBody SkuPriceInfoDetailRequestDto var1);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /sku_price_info/findSkuPriceInfoBySkuNos")
    RestfulResult<List<SkuPriceInfoDetailResponseDto>> findSkuPriceInfoBySkuNos(@RequestBody BatchSkuPriceInfoDetailRequestDto var1);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /sku_price_change_apply/disablePrice")
    ResultInfo disablePrice(@RequestBody SkuPriceChangeApplyDto skuPriceChangeApplyDto);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /sku_price_change_apply/listSkuPriceChangeApplyBySkuIds")
    ResultInfo<List<SkuPriceInfoPurchaseDetailDto>> listSkuPriceChangeApplyBySkuIds(@RequestBody UpdatePriceChangeAuditorDto updatePriceChangeAuditorDto);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /referencePrice/list/page")
    ResultInfo<Map<String,Object>> findByPage(@RequestBody SkuReferencePricePageQueryDto skuReferencePricePageQueryDto);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /sku_price_change_apply/list/page")
    ResultInfo<Map<String, Object>> findBySkuPriceChangeApplyPage(@RequestBody SkuPriceChangeApplyPageQueryDto skuPriceChangeApplyPageQueryDto);


    /**
     * 批量查基础价 含审核状态
     * @param skuPriceInfoDetailDto
     * @return
     */
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /sku_price_info/batchFindSkuPriceInfoBySkuNos")
    RestfulResult<List<SkuPriceInfoDetailResponseDto>> batchFindSkuPriceInfoBySkuNos(@RequestBody SkuPriceInfoBatchQueryDto skuPriceInfoDetailDto);


}
