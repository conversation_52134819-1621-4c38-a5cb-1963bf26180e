package com.vedeng.temporal.domain.dto;

import java.math.BigDecimal;

public class SaleFlowSkuDto {
    private Long flowOrderDetailId;
    private Integer skuId;
    private String skuNo;
    private String productName;
    private String brand;
    private String model;
    private String unit;
    private Integer quantity;
    private BigDecimal price;
    private BigDecimal markupRate;
    private BigDecimal grossProfitRate;

    // getter/setter
    public Long getFlowOrderDetailId() { return flowOrderDetailId; }
    public void setFlowOrderDetailId(Long flowOrderDetailId) { this.flowOrderDetailId = flowOrderDetailId; }
    public Integer getSkuId() { return skuId; }
    public void setSkuId(Integer skuId) { this.skuId = skuId; }
    public String getSkuNo() { return skuNo; }
    public void setSkuNo(String skuNo) { this.skuNo = skuNo; }
    public String getProductName() { return productName; }
    public void setProductName(String productName) { this.productName = productName; }
    public String getBrand() { return brand; }
    public void setBrand(String brand) { this.brand = brand; }
    public String getModel() { return model; }
    public void setModel(String model) { this.model = model; }
    public String getUnit() { return unit; }
    public void setUnit(String unit) { this.unit = unit; }
    public Integer getQuantity() { return quantity; }
    public void setQuantity(Integer quantity) { this.quantity = quantity; }
    public BigDecimal getPrice() { return price; }
    public void setPrice(BigDecimal price) { this.price = price; }
    public BigDecimal getMarkupRate() { return markupRate; }
    public void setMarkupRate(BigDecimal markupRate) { this.markupRate = markupRate; }
    public BigDecimal getGrossProfitRate() { return grossProfitRate; }
    public void setGrossProfitRate(BigDecimal grossProfitRate) { this.grossProfitRate = grossProfitRate; }
} 