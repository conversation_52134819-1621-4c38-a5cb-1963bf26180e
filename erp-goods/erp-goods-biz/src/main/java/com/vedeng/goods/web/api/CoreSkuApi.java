package com.vedeng.goods.web.api;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.goods.dto.CoreSkuDto;
import com.vedeng.goods.service.CoreSkuApiService;
import com.vedeng.goods.vo.SkuForSmartQuoteVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("/coreSkuApi")
@Slf4j
@ExceptionController
@RestController
public class CoreSkuApi {

    @Autowired
    private CoreSkuApiService coreSkuApiService;

    /**
     * 根据skuNo获取是否包含售后，是否直发
     * @param sku skuNo
     */
    @RequestMapping(value = "/getSkuInfoForSmartQuote")
    @NoNeedAccessAuthorization
    public R<?> getSkuInfoForAi(String sku){
        CoreSkuDto skuDto = coreSkuApiService.getCoreSkuDtoBySkuNo(sku);
        if (ObjectUtil.isNull(skuDto)){
            return R.error("未查询到sku信息,sku:"+sku);
        }
        if (!ErpConstant.THREE.equals(skuDto.getCheckStatus()) || !ErpConstant.ONE.equals(skuDto.getStatus())){
            skuDto.setRefusedReason("sku:"+sku+"未审核通过或未启用");
        }
        SkuForSmartQuoteVo vo = new SkuForSmartQuoteVo();
        vo.setCanInstall(Convert.toBool(skuDto.getIsInstallable()));
        vo.setDeliveryDirect(skuDto.getIsDirect());
        vo.setRefusedReason(skuDto.getRefusedReason());
        return R.success(vo);
    }
}
