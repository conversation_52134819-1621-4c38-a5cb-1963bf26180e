package com.vedeng.infrastructure.kingdee.common.sdk.entity;


import java.util.ArrayList;

/**
 * <AUTHOR>
 */
public class SaveParamBase extends JsonBase {
    int Creator;
    ArrayList<String> NeedReturnFields = new ArrayList<>();
    ArrayList<String> NeedUpDateFields = new ArrayList<>();

    public int getCreator() {
        return this.Creator;
    }

    public void setCreator(int creator) {
        this.Creator = creator;
    }

    public ArrayList<String> getNeedReturnFields() {
        return this.NeedReturnFields;
    }

    public void setNeedReturnFields(ArrayList<String> needReturnFields) {
        this.NeedReturnFields = needReturnFields;
    }

    public ArrayList<String> getNeedUpDateFields() {
        return this.NeedUpDateFields;
    }

    public void setNeedUpDateFields(ArrayList<String> needUpDateFields) {
        this.NeedUpDateFields = needUpDateFields;
    }
}
