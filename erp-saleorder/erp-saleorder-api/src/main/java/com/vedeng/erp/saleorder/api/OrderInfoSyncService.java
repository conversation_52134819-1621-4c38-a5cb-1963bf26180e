package com.vedeng.erp.saleorder.api;


import com.vedeng.erp.buyorder.dto.ModifyOrderMessageDto;
import com.vedeng.erp.saleorder.enums.PCOrderStatusEnum;
import com.vedeng.erp.saleorder.enums.SaleorderSyncEnum;

/**
 * 订单信息同步
 * <AUTHOR>
 */
public interface OrderInfoSyncService {


    void unlockSaleOrderGoods(String sku, Integer orderId, Integer userId);

    /**
     * 同步更新订单的支付状态
     * @param saleOrderId 订单id
     */
    void syncPaymentStatusOfSaleOrder(Integer saleOrderId);

    /**
     * 同步更新订单的发货状态、收货状态
     * 销售订单商品-完结的售后退货 与 物流商品数量 进行比较
     * 1、直发商品与对应的物流信息作比较，判断收发货
     * 2、普发商品与对应的出入库记录作比较，判断收发货
     * @param saleOrderId 销售订单id
     */
    void syncDeliveryAndArrivalDetailOfSaleOrder(Integer saleOrderId);

    /**
     * 同步更新订单的发货状态
     * @param saleOrderId 销售订单id
     */
    void syncInvoiceDetailOfSaleOrder(Integer saleOrderId);

    /**
     * 新增销售售后单（退货、换货、退款）时，
     * 1、更新销售单的售后状态 SERVICE_STATUS 为售后中
     * 2、更新销售单锁定状态，更新订单的预警状态
     * 3、如果售后存在商品，则同时锁定销售单商品
     * @param afterSalesId 售后单id
     */
    void lockSaleOrderWhenAddAfterSales(Integer afterSalesId);

    /**
     * 完结/关闭售后单时（退货、换货、退款），
     * 1、更新销售单的售后状态 SERVICE_STATUS 为售后关闭
     * 2、（退货、换货、退款）售后单->当前销售订单下面是否还有其他退换货的售后单->没有则解锁销售单商品锁定状态和销售单锁定状态
     * 3、更新销售单商品的预警状态
     * 4、售后单完结时，刷新销售单的票货款状态
     * @param afterSalesId 售后单id
     * @param operateType 售后单操作类型，1：关闭，2：完结
     */
    void unlockSaleOrderWhenAfterSalesClosedOrFinished(Integer afterSalesId, Integer operateType);


    /**
     * 采购单修改发货方式更新销售订单信息
     * @param modifyOrderMessageDto
     * @return
     */
    void editBuyOrderInfoSync(ModifyOrderMessageDto modifyOrderMessageDto);

    /**
     * .完结采购售后退货单时更新相应销售单信息
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2021/12/30 17:33.
     * @author: Randy.Xu.
     * @param afterSalesId
     * @param buyorderId
     * @return: void.
     * @throws:  .
     */
    void updateSaleorderForbuyorderReturnOver(Integer afterSalesId,Integer buyorderId);
    /**
     * <AUTHOR>
     * @desc 根据销售单traderid判断是否采用新订单流
     * @param traderId
     * @return
     */
    Integer setSaleorderIsNewByOrg(String saleorderNo,Integer traderId);
}
