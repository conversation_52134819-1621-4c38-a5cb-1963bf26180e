package com.vedeng.erp.buyorder.task.buyorder;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.buyorder.task.buyorder.statusSync.*;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description 维护数据表T_BUYORDER_DATA
 * @Date 2021/10/19 14:59
 */
@Component
@JobHandler(value = "BuyorderDataSyncTask")
public class BuyorderDataSyncTask extends AbstractJobHandler {

    public static final String YESTERDAY = "yesterday";
    @Resource
    BuyorderIsAllLogisticsStatus buyorderIsAllLogisticsStatus;

    @Resource
    BuyorderIsContractReturnStatusSync buyorderIsContractReturnStatusSync;

    @Resource
    BuyorderIsFinanceAlreadyStatus buyorderIsFinanceAlreadyStatus;

    @Resource
    BuyorderLackPeriodStatus buyorderLackPeriodStatus;

    @Resource
    BuyorderRecordInvoiceApplyStatus buyorderRecordInvoiceApplyStatus;

    @Resource
    BuyorderLackAccountPeriodAmountSync buyorderLackAccountPeriodAmountSync;

    @Resource
    BuyorderSubStatusSync buyorderSubStatusSync;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        // 增加当日时间，固定参数为yesterday
        if (StrUtil.isNotEmpty(param) && YESTERDAY.equals(param)) {
            long startTime = DateUtil.beginOfDay(DateUtil.yesterday()).getTime();
            long endTime = DateUtil.endOfDay(DateUtil.yesterday()).getTime();
            param = startTime + "-" + endTime;
        }
        excuteUpdate(param);
        return SUCCESS;
    }

    private void excuteUpdate(String longDate) {

        XxlJobLogger.log("更新子状态");
        buyorderSubStatusSync.updateStatusProcess(longDate);

        XxlJobLogger.log("更新是否全部添加物流信息");
        buyorderIsAllLogisticsStatus.updateStatusProcess(longDate);

        XxlJobLogger.log("更新是否合同回传");
        buyorderIsContractReturnStatusSync.updateStatusProcess(longDate);

        XxlJobLogger.log("更新付款申请到财务");
        buyorderIsFinanceAlreadyStatus.updateStatusProcess(longDate);

        XxlJobLogger.log("更新已偿还账期状态");
        buyorderLackPeriodStatus.updateStatusProcess(longDate);

        XxlJobLogger.log("更新录票状态");
        buyorderRecordInvoiceApplyStatus.updateStatusProcess(longDate);

        XxlJobLogger.log("更新未还账期款");
        buyorderLackAccountPeriodAmountSync.updateStatusProcess(longDate);
    }


    public static void main(String[] args) {
        long startTime = DateUtil.endOfDay(DateUtil.yesterday()).getTime();
        long endTime = DateUtil.beginOfDay(DateUtil.yesterday()).getTime();
        String param = startTime + "-" + endTime;
        String [] splitTime =  param.split("-");
        if(splitTime.length>=2){
            startTime =Long.parseLong(splitTime[0]) ;
            endTime =Long.parseLong(splitTime[1]) ;
            System.out.println(startTime+"--"+endTime);
        }
    }

}
