package com.vedeng.goods.manager.validator.InvalidatorChain.spu.Invalid;

import com.google.common.base.CharMatcher;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.goods.exception.InvalidatorChainException;
import com.vedeng.goods.manager.validator.GoodsInvalidatorChain;
import com.wms.dao.WmsInputOrderMapper;
import com.wms.model.po.WmsInputOrder;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.vedeng.goods.manager.validator.enums.GoodsInvalidatorErrorEnum.*;
/**
 * .(8)	所属的所有SKU没有（审核状态为待审核，审核中，审核通过，且入库状态为未入库，部分入库）的盘盈入库单；
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2021/9/2 9:14.
 * @author: <PERSON><PERSON>.
 */
@Service
public class SpuNoRelatedInorderInvalidator extends GoodsInvalidatorChain {

    @Resource
    WmsInputOrderMapper wmsInputOrderMapper;

    @Override
    public void doInvalid() {
        String spuNo = getSpuNo();
        List<String> skuNoList = getSkuNoList();
        if(CollectionUtils.isEmpty(skuNoList)){
            return ;
        }
        StringBuffer skus = new StringBuffer();
        StringBuffer orders = new StringBuffer();
        for (String skuNo : skuNoList) {
            List<WmsInputOrder> wmsInputOrderList = wmsInputOrderMapper.getValidAndUnInInpuorderBySkuNos(skuNo);
            if(CollectionUtils.isNotEmpty(wmsInputOrderList)){
                skus.append(skuNo);
                skus.append(",");
                String collect = wmsInputOrderList.stream().map(e -> e.getOrderNo()).collect(Collectors.joining(","));
                orders.append(collect);
                orders.append(",");
            }
        }
        if(skus.length()>0){
            String tempSkusList = SpuNoRelatedInorderInvalidatorError.getMessage().replace("skus", skus.substring(0,skus.length()-1));
            String skusList = tempSkusList.replace("orderNo", orders.substring(0,orders.length()-1));
            throw new InvalidatorChainException(SpuNoRelatedInorderInvalidatorError.getCode(), skusList);
        }
    }
}
