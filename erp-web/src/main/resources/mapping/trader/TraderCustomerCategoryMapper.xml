<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.trader.dao.TraderCustomerCategoryMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.trader.model.TraderCustomerCategory">
        <!--@mbg.generated-->
        <!--@Table T_TRADER_CUSTOMER_CATEGORY-->
        <id column="TRADER_CUSTOMER_CATEGORY_ID" jdbcType="INTEGER" property="traderCustomerCategoryId"/>
        <result column="PARENT_ID" jdbcType="INTEGER" property="parentId"/>
        <result column="CUSTOMER_CATEGORY_NAME" jdbcType="VARCHAR" property="customerCategoryName"/>
        <result column="SORT" jdbcType="INTEGER" property="sort"/>
    </resultMap>

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        TRADER_CUSTOMER_CATEGORY_ID, PARENT_ID, CUSTOMER_CATEGORY_NAME, SORT, IS_HIDDEN
    </sql>

    <select id="getYxgCategory" resultType="com.vedeng.trader.model.TraderCustomerCategory">
        SELECT *
        FROM T_TRADER_CUSTOMER_CATEGORY
        WHERE TRADER_CUSTOMER_CATEGORY_ID > 10;
    </select>

    <!--auto generated by MybatisCodeHelper on 2022-04-22-->
    <select id="getByAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_TRADER_CUSTOMER_CATEGORY
    </select>

    <!--auto generated by MybatisCodeHelper on 2022-04-22-->
    <select id="selectAllByTraderCustomerCategoryId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_TRADER_CUSTOMER_CATEGORY
        where TRADER_CUSTOMER_CATEGORY_ID = #{traderCustomerCategoryId,jdbcType=INTEGER}
    </select>
</mapper>
