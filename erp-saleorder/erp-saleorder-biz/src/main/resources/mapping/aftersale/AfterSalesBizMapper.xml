<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.aftersale.mapper.AfterSalesBizMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.aftersale.domain.entity.AfterSalesEntity">
        <!--@mbg.generated-->
        <!--@Table T_AFTER_SALES-->
        <id column="AFTER_SALES_ID" jdbcType="INTEGER" property="afterSalesId"/>
        <result column="AFTER_SALES_NO" jdbcType="VARCHAR" property="afterSalesNo"/>
        <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId"/>
        <result column="SUBJECT_TYPE" jdbcType="INTEGER" property="subjectType"/>
        <result column="TYPE" jdbcType="INTEGER" property="type"/>
        <result column="ORDER_ID" jdbcType="INTEGER" property="orderId"/>
        <result column="ORDER_NO" jdbcType="VARCHAR" property="orderNo"/>
        <result column="SERVICE_USER_ID" jdbcType="INTEGER" property="serviceUserId"/>
        <result column="VALID_STATUS" jdbcType="INTEGER" property="validStatus"/>
        <result column="VALID_TIME" jdbcType="BIGINT" property="validTime"/>
        <result column="STATUS" jdbcType="INTEGER" property="status"/>
        <result column="ATFER_SALES_STATUS" jdbcType="INTEGER" property="atferSalesStatus"/>
        <result column="ATFER_SALES_STATUS_RESON" jdbcType="INTEGER" property="atferSalesStatusReson"/>
        <result column="ATFER_SALES_STATUS_USER" jdbcType="INTEGER" property="atferSalesStatusUser"/>
        <result column="ATFER_SALES_STATUS_COMMENTS" jdbcType="VARCHAR" property="atferSalesStatusComments"/>
        <result column="FIRST_VALID_STATUS" jdbcType="INTEGER" property="firstValidStatus"/>
        <result column="FIRST_VALID_TIME" jdbcType="BIGINT" property="firstValidTime"/>
        <result column="FIRST_VALID_USER" jdbcType="INTEGER" property="firstValidUser"/>
        <result column="FIRST_VALID_COMMENTS" jdbcType="VARCHAR" property="firstValidComments"/>
        <result column="SOURCE" jdbcType="INTEGER" property="source"/>
        <result column="ADD_TIME" jdbcType="BIGINT" property="addTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="MOD_TIME" jdbcType="BIGINT" property="modTime"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="IS_OUT_AFTER" jdbcType="INTEGER" property="isOutAfter"/>
        <result column="INVOICE_SEND_STATUS" jdbcType="INTEGER" property="invoiceSendStatus"/>
        <result column="INVOICE_ARRIVAL_STATUS" jdbcType="INTEGER" property="invoiceArrivalStatus"/>
        <result column="UPDATE_DATA_TIME" jdbcType="TIMESTAMP" property="updateDataTime"/>
        <result column="IS_LIGHTNING" jdbcType="INTEGER" property="isLightning"/>
        <result column="CREATE_TYPE" jdbcType="INTEGER" property="createType"/>
        <result column="CREATE_FRONT_END_USER" jdbcType="VARCHAR" property="createFrontEndUser"/>
        <result column="CLOSE_FRONT_END_MOBILE" jdbcType="VARCHAR" property="closeFrontEndMobile"/>
        <result column="VERIFIES_NOT_PASS_REASON" jdbcType="VARCHAR" property="verifiesNotPassReason"/>
        <result column="HANDLE_STATUS" jdbcType="INTEGER" property="handleStatus"/>
        <result column="INVOICE_REFUND_STATUS" jdbcType="INTEGER" property="invoiceRefundStatus"/>
        <result column="AMOUNT_REFUND_STATUS" jdbcType="INTEGER" property="amountRefundStatus"/>
        <result column="AMOUNT_COLLECTION_STATUS" jdbcType="INTEGER" property="amountCollectionStatus"/>
        <result column="AMOUNT_PAY_STATUS" jdbcType="INTEGER" property="amountPayStatus"/>
        <result column="INVOICE_MAKEOUT_STATUS" jdbcType="INTEGER" property="invoiceMakeoutStatus"/>
        <result column="IS_NEW" jdbcType="INTEGER" property="isNew"/>
        <result column="DELIVERY_DIRECT_AFTER_SALES_ID" jdbcType="INTEGER" property="deliveryDirectAfterSalesId"/>
        <result column="CLIENT_STATUS" jdbcType="TINYINT" property="clientStatus"/>
    </resultMap>

    <resultMap id="PushInfoResultMap" type="com.vedeng.erp.aftersale.domain.dto.PushAfterSalesDto">
        <result column="orderNo" jdbcType="VARCHAR" property="orderNo"/>
        <result column="afterSalesId" jdbcType="INTEGER" property="afterSalesId"/>
        <result column="afterSaleOrderNo" jdbcType="VARCHAR" property="afterSaleOrderNo"/>
        <result column="afterSalesTypeId" jdbcType="INTEGER" property="afterSalesTypeId"/>
        <result column="afterSalesComment" jdbcType="VARCHAR" property="afterSalesComment"/>
        <result column="afterSalesReasonId" jdbcType="INTEGER" property="afterSalesReasonId"/>
        <result column="afterSalesReasonName" jdbcType="VARCHAR" property="afterSalesReasonName"/>
        <result column="afterSalesStatus" jdbcType="INTEGER" property="afterSalesStatus"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="verifiesNotPassReason" jdbcType="VARCHAR" property="verifiesNotPassReason"/>
        <result column="contactUserName" jdbcType="VARCHAR" property="contactUserName"/>
        <result column="contactMobile" jdbcType="VARCHAR" property="contactMobile"/>
        <result column="areaId" jdbcType="INTEGER" property="areaId"/>
        <result column="contactAddress" jdbcType="VARCHAR" property="contactAddress"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        AFTER_SALES_ID,
        AFTER_SALES_NO,
        COMPANY_ID,
        SUBJECT_TYPE,
        `TYPE`,
        ORDER_ID,
        ORDER_NO,
        SERVICE_USER_ID,
        VALID_STATUS,
        VALID_TIME,
        `STATUS`,
        ATFER_SALES_STATUS,
        ATFER_SALES_STATUS_RESON,
        ATFER_SALES_STATUS_USER,
        ATFER_SALES_STATUS_COMMENTS,
        FIRST_VALID_STATUS,
        FIRST_VALID_TIME,
        FIRST_VALID_USER,
        FIRST_VALID_COMMENTS,
        `SOURCE`,
        ADD_TIME,
        CREATOR,
        MOD_TIME,
        UPDATER,
        IS_OUT_AFTER,
        INVOICE_SEND_STATUS,
        INVOICE_ARRIVAL_STATUS,
        UPDATE_DATA_TIME,
        IS_LIGHTNING,
        CREATE_TYPE,
        CREATE_FRONT_END_USER,
        CLOSE_FRONT_END_MOBILE,
        VERIFIES_NOT_PASS_REASON,
        HANDLE_STATUS,
        INVOICE_REFUND_STATUS,
        AMOUNT_REFUND_STATUS,
        AMOUNT_COLLECTION_STATUS,
        AMOUNT_PAY_STATUS,
        INVOICE_MAKEOUT_STATUS,
        IS_NEW,
        DELIVERY_DIRECT_AFTER_SALES_ID,
        CLIENT_STATUS
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from T_AFTER_SALES
        where AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
    </select>

    <select id="selectByAfterSalesNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from T_AFTER_SALES
        where AFTER_SALES_NO = #{afterSalesNo,jdbcType=VARCHAR} limit 1
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete
        from T_AFTER_SALES
        where AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
    </delete>
    <insert id="insertSelective" keyColumn="AFTER_SALES_ID" keyProperty="afterSalesId"
            parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSalesEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_AFTER_SALES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="afterSalesNo != null">
                AFTER_SALES_NO,
            </if>
            <if test="companyId != null">
                COMPANY_ID,
            </if>
            <if test="subjectType != null">
                SUBJECT_TYPE,
            </if>
            <if test="type != null">
                `TYPE`,
            </if>
            <if test="orderId != null">
                ORDER_ID,
            </if>
            <if test="orderNo != null">
                ORDER_NO,
            </if>
            <if test="serviceUserId != null">
                SERVICE_USER_ID,
            </if>
            <if test="validStatus != null">
                VALID_STATUS,
            </if>
            <if test="validTime != null">
                VALID_TIME,
            </if>
            <if test="status != null">
                `STATUS`,
            </if>
            <if test="atferSalesStatus != null">
                ATFER_SALES_STATUS,
            </if>
            <if test="atferSalesStatusReson != null">
                ATFER_SALES_STATUS_RESON,
            </if>
            <if test="atferSalesStatusUser != null">
                ATFER_SALES_STATUS_USER,
            </if>
            <if test="atferSalesStatusComments != null">
                ATFER_SALES_STATUS_COMMENTS,
            </if>
            <if test="firstValidStatus != null">
                FIRST_VALID_STATUS,
            </if>
            <if test="firstValidTime != null">
                FIRST_VALID_TIME,
            </if>
            <if test="firstValidUser != null">
                FIRST_VALID_USER,
            </if>
            <if test="firstValidComments != null">
                FIRST_VALID_COMMENTS,
            </if>
            <if test="source != null">
                `SOURCE`,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="isOutAfter != null">
                IS_OUT_AFTER,
            </if>
            <if test="invoiceSendStatus != null">
                INVOICE_SEND_STATUS,
            </if>
            <if test="invoiceArrivalStatus != null">
                INVOICE_ARRIVAL_STATUS,
            </if>
            <if test="updateDataTime != null">
                UPDATE_DATA_TIME,
            </if>
            <if test="isLightning != null">
                IS_LIGHTNING,
            </if>
            <if test="createType != null">
                CREATE_TYPE,
            </if>
            <if test="createFrontEndUser != null">
                CREATE_FRONT_END_USER,
            </if>
            <if test="closeFrontEndMobile != null">
                CLOSE_FRONT_END_MOBILE,
            </if>
            <if test="verifiesNotPassReason != null">
                VERIFIES_NOT_PASS_REASON,
            </if>
            <if test="handleStatus != null">
                HANDLE_STATUS,
            </if>
            <if test="invoiceRefundStatus != null">
                INVOICE_REFUND_STATUS,
            </if>
            <if test="amountRefundStatus != null">
                AMOUNT_REFUND_STATUS,
            </if>
            <if test="amountCollectionStatus != null">
                AMOUNT_COLLECTION_STATUS,
            </if>
            <if test="amountPayStatus != null">
                AMOUNT_PAY_STATUS,
            </if>
            <if test="invoiceMakeoutStatus != null">
                INVOICE_MAKEOUT_STATUS,
            </if>
            <if test="isNew != null">
                IS_NEW,
            </if>
            <if test="deliveryDirectAfterSalesId != null">
                DELIVERY_DIRECT_AFTER_SALES_ID,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="afterSalesNo != null">
                #{afterSalesNo,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null">
                #{companyId,jdbcType=INTEGER},
            </if>
            <if test="subjectType != null">
                #{subjectType,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="orderId != null">
                #{orderId,jdbcType=INTEGER},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="serviceUserId != null">
                #{serviceUserId,jdbcType=INTEGER},
            </if>
            <if test="validStatus != null">
                #{validStatus,jdbcType=INTEGER},
            </if>
            <if test="validTime != null">
                #{validTime,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="atferSalesStatus != null">
                #{atferSalesStatus,jdbcType=INTEGER},
            </if>
            <if test="atferSalesStatusReson != null">
                #{atferSalesStatusReson,jdbcType=INTEGER},
            </if>
            <if test="atferSalesStatusUser != null">
                #{atferSalesStatusUser,jdbcType=INTEGER},
            </if>
            <if test="atferSalesStatusComments != null">
                #{atferSalesStatusComments,jdbcType=VARCHAR},
            </if>
            <if test="firstValidStatus != null">
                #{firstValidStatus,jdbcType=INTEGER},
            </if>
            <if test="firstValidTime != null">
                #{firstValidTime,jdbcType=BIGINT},
            </if>
            <if test="firstValidUser != null">
                #{firstValidUser,jdbcType=INTEGER},
            </if>
            <if test="firstValidComments != null">
                #{firstValidComments,jdbcType=VARCHAR},
            </if>
            <if test="source != null">
                #{source,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="isOutAfter != null">
                #{isOutAfter,jdbcType=INTEGER},
            </if>
            <if test="invoiceSendStatus != null">
                #{invoiceSendStatus,jdbcType=INTEGER},
            </if>
            <if test="invoiceArrivalStatus != null">
                #{invoiceArrivalStatus,jdbcType=INTEGER},
            </if>
            <if test="updateDataTime != null">
                #{updateDataTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isLightning != null">
                #{isLightning,jdbcType=INTEGER},
            </if>
            <if test="createType != null">
                #{createType,jdbcType=INTEGER},
            </if>
            <if test="createFrontEndUser != null">
                #{createFrontEndUser,jdbcType=VARCHAR},
            </if>
            <if test="closeFrontEndMobile != null">
                #{closeFrontEndMobile,jdbcType=VARCHAR},
            </if>
            <if test="verifiesNotPassReason != null">
                #{verifiesNotPassReason,jdbcType=VARCHAR},
            </if>
            <if test="handleStatus != null">
                #{handleStatus,jdbcType=INTEGER},
            </if>
            <if test="invoiceRefundStatus != null">
                #{invoiceRefundStatus,jdbcType=INTEGER},
            </if>
            <if test="amountRefundStatus != null">
                #{amountRefundStatus,jdbcType=INTEGER},
            </if>
            <if test="amountCollectionStatus != null">
                #{amountCollectionStatus,jdbcType=INTEGER},
            </if>
            <if test="amountPayStatus != null">
                #{amountPayStatus,jdbcType=INTEGER},
            </if>
            <if test="invoiceMakeoutStatus != null">
                #{invoiceMakeoutStatus,jdbcType=INTEGER},
            </if>
            <if test="isNew != null">
                #{isNew,jdbcType=INTEGER},
            </if>
            <if test="deliveryDirectAfterSalesId != null">
                #{deliveryDirectAfterSalesId,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSalesEntity">
        <!--@mbg.generated-->
        update T_AFTER_SALES
        <set>
            <if test="afterSalesNo != null">
                AFTER_SALES_NO = #{afterSalesNo,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null">
                COMPANY_ID = #{companyId,jdbcType=INTEGER},
            </if>
            <if test="subjectType != null">
                SUBJECT_TYPE = #{subjectType,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                `TYPE` = #{type,jdbcType=INTEGER},
            </if>
            <if test="orderId != null">
                ORDER_ID = #{orderId,jdbcType=INTEGER},
            </if>
            <if test="orderNo != null">
                ORDER_NO = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="serviceUserId != null">
                SERVICE_USER_ID = #{serviceUserId,jdbcType=INTEGER},
            </if>
            <if test="validStatus != null">
                VALID_STATUS = #{validStatus,jdbcType=INTEGER},
            </if>
            <if test="validTime != null">
                VALID_TIME = #{validTime,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                `STATUS` = #{status,jdbcType=INTEGER},
            </if>
            <if test="atferSalesStatus != null">
                ATFER_SALES_STATUS = #{atferSalesStatus,jdbcType=INTEGER},
            </if>
            <if test="atferSalesStatusReson != null">
                ATFER_SALES_STATUS_RESON = #{atferSalesStatusReson,jdbcType=INTEGER},
            </if>
            <if test="atferSalesStatusUser != null">
                ATFER_SALES_STATUS_USER = #{atferSalesStatusUser,jdbcType=INTEGER},
            </if>
            <if test="atferSalesStatusComments != null">
                ATFER_SALES_STATUS_COMMENTS = #{atferSalesStatusComments,jdbcType=VARCHAR},
            </if>
            <if test="firstValidStatus != null">
                FIRST_VALID_STATUS = #{firstValidStatus,jdbcType=INTEGER},
            </if>
            <if test="firstValidTime != null">
                FIRST_VALID_TIME = #{firstValidTime,jdbcType=BIGINT},
            </if>
            <if test="firstValidUser != null">
                FIRST_VALID_USER = #{firstValidUser,jdbcType=INTEGER},
            </if>
            <if test="firstValidComments != null">
                FIRST_VALID_COMMENTS = #{firstValidComments,jdbcType=VARCHAR},
            </if>
            <if test="source != null">
                `SOURCE` = #{source,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="isOutAfter != null">
                IS_OUT_AFTER = #{isOutAfter,jdbcType=INTEGER},
            </if>
            <if test="invoiceSendStatus != null">
                INVOICE_SEND_STATUS = #{invoiceSendStatus,jdbcType=INTEGER},
            </if>
            <if test="invoiceArrivalStatus != null">
                INVOICE_ARRIVAL_STATUS = #{invoiceArrivalStatus,jdbcType=INTEGER},
            </if>
            <if test="updateDataTime != null">
                UPDATE_DATA_TIME = #{updateDataTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isLightning != null">
                IS_LIGHTNING = #{isLightning,jdbcType=INTEGER},
            </if>
            <if test="createType != null">
                CREATE_TYPE = #{createType,jdbcType=INTEGER},
            </if>
            <if test="createFrontEndUser != null">
                CREATE_FRONT_END_USER = #{createFrontEndUser,jdbcType=VARCHAR},
            </if>
            <if test="closeFrontEndMobile != null">
                CLOSE_FRONT_END_MOBILE = #{closeFrontEndMobile,jdbcType=VARCHAR},
            </if>
            <if test="verifiesNotPassReason != null">
                VERIFIES_NOT_PASS_REASON = #{verifiesNotPassReason,jdbcType=VARCHAR},
            </if>
            <if test="handleStatus != null">
                HANDLE_STATUS = #{handleStatus,jdbcType=INTEGER},
            </if>
            <if test="invoiceRefundStatus != null">
                INVOICE_REFUND_STATUS = #{invoiceRefundStatus,jdbcType=INTEGER},
            </if>
            <if test="amountRefundStatus != null">
                AMOUNT_REFUND_STATUS = #{amountRefundStatus,jdbcType=INTEGER},
            </if>
            <if test="amountCollectionStatus != null">
                AMOUNT_COLLECTION_STATUS = #{amountCollectionStatus,jdbcType=INTEGER},
            </if>
            <if test="amountPayStatus != null">
                AMOUNT_PAY_STATUS = #{amountPayStatus,jdbcType=INTEGER},
            </if>
            <if test="invoiceMakeoutStatus != null">
                INVOICE_MAKEOUT_STATUS = #{invoiceMakeoutStatus,jdbcType=INTEGER},
            </if>
            <if test="isNew != null">
                IS_NEW = #{isNew,jdbcType=INTEGER},
            </if>
            <if test="deliveryDirectAfterSalesId != null">
                DELIVERY_DIRECT_AFTER_SALES_ID = #{deliveryDirectAfterSalesId,jdbcType=INTEGER},
            </if>
        </set>
        where AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSalesEntity">
        <!--@mbg.generated-->
        update T_AFTER_SALES
        set AFTER_SALES_NO                 = #{afterSalesNo,jdbcType=VARCHAR},
            COMPANY_ID                     = #{companyId,jdbcType=INTEGER},
            SUBJECT_TYPE                   = #{subjectType,jdbcType=INTEGER},
            `TYPE`                         = #{type,jdbcType=INTEGER},
            ORDER_ID                       = #{orderId,jdbcType=INTEGER},
            ORDER_NO                       = #{orderNo,jdbcType=VARCHAR},
            SERVICE_USER_ID                = #{serviceUserId,jdbcType=INTEGER},
            VALID_STATUS                   = #{validStatus,jdbcType=INTEGER},
            VALID_TIME                     = #{validTime,jdbcType=BIGINT},
            `STATUS`                       = #{status,jdbcType=INTEGER},
            ATFER_SALES_STATUS             = #{atferSalesStatus,jdbcType=INTEGER},
            ATFER_SALES_STATUS_RESON       = #{atferSalesStatusReson,jdbcType=INTEGER},
            ATFER_SALES_STATUS_USER        = #{atferSalesStatusUser,jdbcType=INTEGER},
            ATFER_SALES_STATUS_COMMENTS    = #{atferSalesStatusComments,jdbcType=VARCHAR},
            FIRST_VALID_STATUS             = #{firstValidStatus,jdbcType=INTEGER},
            FIRST_VALID_TIME               = #{firstValidTime,jdbcType=BIGINT},
            FIRST_VALID_USER               = #{firstValidUser,jdbcType=INTEGER},
            FIRST_VALID_COMMENTS           = #{firstValidComments,jdbcType=VARCHAR},
            `SOURCE`                       = #{source,jdbcType=INTEGER},
            ADD_TIME                       = #{addTime,jdbcType=BIGINT},
            CREATOR                        = #{creator,jdbcType=INTEGER},
            MOD_TIME                       = #{modTime,jdbcType=BIGINT},
            UPDATER                        = #{updater,jdbcType=INTEGER},
            IS_OUT_AFTER                   = #{isOutAfter,jdbcType=INTEGER},
            INVOICE_SEND_STATUS            = #{invoiceSendStatus,jdbcType=INTEGER},
            INVOICE_ARRIVAL_STATUS         = #{invoiceArrivalStatus,jdbcType=INTEGER},
            UPDATE_DATA_TIME               = #{updateDataTime,jdbcType=TIMESTAMP},
            IS_LIGHTNING                   = #{isLightning,jdbcType=INTEGER},
            CREATE_TYPE                    = #{createType,jdbcType=INTEGER},
            CREATE_FRONT_END_USER          = #{createFrontEndUser,jdbcType=VARCHAR},
            CLOSE_FRONT_END_MOBILE         = #{closeFrontEndMobile,jdbcType=VARCHAR},
            VERIFIES_NOT_PASS_REASON       = #{verifiesNotPassReason,jdbcType=VARCHAR},
            HANDLE_STATUS                  = #{handleStatus,jdbcType=INTEGER},
            INVOICE_REFUND_STATUS          = #{invoiceRefundStatus,jdbcType=INTEGER},
            AMOUNT_REFUND_STATUS           = #{amountRefundStatus,jdbcType=INTEGER},
            AMOUNT_COLLECTION_STATUS       = #{amountCollectionStatus,jdbcType=INTEGER},
            AMOUNT_PAY_STATUS              = #{amountPayStatus,jdbcType=INTEGER},
            INVOICE_MAKEOUT_STATUS         = #{invoiceMakeoutStatus,jdbcType=INTEGER},
            IS_NEW                         = #{isNew,jdbcType=INTEGER},
            DELIVERY_DIRECT_AFTER_SALES_ID = #{deliveryDirectAfterSalesId,jdbcType=INTEGER}
        where AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
    </update>

    <!--auto generated by MybatisCodeHelper on 2023-09-21-->
    <select id="findByOrderIdGetSaleOrderAfterSale" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_AFTER_SALES
        where ORDER_ID = #{orderId,jdbcType=INTEGER}
          and SUBJECT_TYPE = 535
          and TYPE = 539
    </select>


    <!--auto generated by MybatisCodeHelper on 2023-09-21-->
    <select id="getSaleOrderAmountDto"
            resultType="com.vedeng.erp.aftersale.domain.dto.SaleOrderGoodsAmountDto">
        SELECT TS.SALEORDER_ID                      saleOrderId,
               TS.ORDER_TYPE                        orderType,
               IFNULL(SH.NUM, 0)                    num,
               IFNULL(TSG.MAX_SKU_REFUND_AMOUNT, 0) maxSkuRefundAmount,
               IFNULL(TSG.NUM, 0)                   saleOrderGoodsNum,
               IFNULL(TSG.PRICE, 0)                 price,
               IFNULL(SH.ALL_AFTER_AMOUNT, 0)       refundAmount
        FROM T_SALEORDER_GOODS TSG
                 left join T_SALEORDER TS on TSG.SALEORDER_ID = TS.SALEORDER_ID
                 LEFT JOIN
             (SELECT SUM(TASG.NUM)                          AS NUM,
                     TAS.AFTER_SALES_ID,
                     TASG.ORDER_DETAIL_ID,
                     TASG.AFTER_SALES_GOODS_ID,
                     IFNULL(SUM(TASG.SKU_REFUND_AMOUNT), 0) AS ALL_AFTER_AMOUNT
              FROM T_AFTER_SALES TAS
                       left JOIN T_AFTER_SALES_GOODS TASG
                                 ON TAS.AFTER_SALES_ID = TASG.AFTER_SALES_ID
                                     AND TAS.SUBJECT_TYPE = 535
                                     AND TAS.TYPE = 539
                                     AND TAS.ATFER_SALES_STATUS = 2
                                     AND TAS.ORDER_ID = #{saleOrderId,jdbcType=INTEGER}
                                     AND TASG.GOODS_TYPE = 0
              GROUP BY TASG.ORDER_DETAIL_ID) SH
             ON TSG.SALEORDER_GOODS_ID = SH.ORDER_DETAIL_ID
        WHERE TSG.SALEORDER_ID = #{saleOrderId,jdbcType=INTEGER}
          AND TSG.IS_DELETE = 0;
    </select>

  <select id="getAfterSalesListByOrderIdAndSubjectType"
          resultType="com.vedeng.erp.mobile.dto.AfterSalesListResultDto">
    select TAS.AFTER_SALES_ID                                                           as afterSalesId,
    TAS.AFTER_SALES_NO                                                                  as afterSalesNo,
    TAS.TYPE                                                                            as type,
    SYS1.TITLE                                                                          as typeName,
    TAS.ORDER_ID                                                                        as orderId,
    TAS.ORDER_NO                                                                        as orderNo,
    TAS.ATFER_SALES_STATUS                                                              as afterSalesStatus,
    FROM_UNIXTIME(IF(TAS.ADD_TIME = 0, NULL, TAS.ADD_TIME) / 1000, '%Y-%m-%d %H:%i:%s') as addTime,
    TASD.REASON                                                                         as reason,
    SYS2.TITLE                                                                          as reasonName
    from T_AFTER_SALES TAS
    left join T_AFTER_SALES_DETAIL TASD on TAS.AFTER_SALES_ID = TASD.AFTER_SALES_ID
    left join T_SYS_OPTION_DEFINITION SYS1 on TAS.TYPE = SYS1.SYS_OPTION_DEFINITION_ID
    left join T_SYS_OPTION_DEFINITION SYS2 on TASD.REASON = SYS2.SYS_OPTION_DEFINITION_ID
    where 1=1
    <if test="orderIds != null and orderIds.size() != 0">
      and TAS.ORDER_ID in
      <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
        #{orderId}
      </foreach>
    </if>
    <if test="subjectType != null" >
      and TAS.SUBJECT_TYPE = #{subjectType,jdbcType=INTEGER}
    </if>
    order by TAS.ADD_TIME desc
  </select>

    <select id="findUnderwayThOrTpByOrderIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_AFTER_SALES
        where
        SUBJECT_TYPE = 535
        and TYPE in (539, 542)
        and ATFER_SALES_STATUS = 1
        and ORDER_ID in
        <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </select>

    <select id="findAfterSalesDetailByAfterSalesId" resultType="com.vedeng.erp.aftersale.domain.dto.AfterSalesDetailDto">
        SELECT A.SUBJECT_TYPE,A.TYPE AS afterType,A.ORDER_ID,A.AFTER_SALES_NO,A.COMPANY_ID, B.*
        FROM T_AFTER_SALES A
        LEFT JOIN T_AFTER_SALES_DETAIL B ON A.AFTER_SALES_ID = B.AFTER_SALES_ID
        WHERE A.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER} limit 1
    </select>

    <update id="updateDetailByAfterSalesId" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSalesDetailEntity">
        <!--@mbg.generated-->
        update T_AFTER_SALES_DETAIL
        <set>
            <if test="invoiceStatus != null">
                INVOICE_STATUS = #{invoiceStatus,jdbcType=INTEGER},
            </if>
            <if test="invoiceTime != null">
                INVOICE_TIME = #{invoiceTime,jdbcType=BIGINT},
            </if>
        </set>
        where AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
    </update>

    <update id="updateByAfterSalesId" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSalesDetailEntity">
        <!--@mbg.generated-->
        update T_AFTER_SALES
        <set>
            <if test="invoiceMakeoutStatus != null">
                INVOICE_MAKEOUT_STATUS = #{invoiceMakeoutStatus,jdbcType=INTEGER},
            </if>
            <if test="modeTime != null">
                MOD_TIME = #{modeTime,jdbcType=BIGINT},
            </if>
        </set>
        where AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
    </update>

    <select id="getAftersaleFinanceInfo" resultType="com.vedeng.erp.saleorder.dto.OrderFinanceInfoDto">
        SELECT
        C.INVOICE_TYPE,
        D.TRADER_ID,
        D.TRADER_NAME,
        B.TAX_NUM,
        B.REG_ADDRESS,
        B.REG_TEL,
        B.BANK,
        B.BANK_ACCOUNT,
        B.AVERAGE_TAXPAYER_URI,
        B.BANK_CODE
        FROM T_AFTER_SALES A
        LEFT JOIN T_AFTER_SALES_DETAIL C ON C.AFTER_SALES_ID = A.AFTER_SALES_ID
        LEFT JOIN T_TRADER_FINANCE B ON C.TRADER_ID = B.TRADER_ID
        LEFT JOIN T_TRADER D ON C.TRADER_ID = D.TRADER_ID
        WHERE A.AFTER_SALES_ID = #{aftersaleId,jdbcType=INTEGER}
    </select>

    <select id="getOngoingAfterSalesByOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
            T_AFTER_SALES
        where
            ATFER_SALES_STATUS = 1
          and TYPE in (539,540,542,543)
          and ORDER_ID = #{saleOrderId,jdbcType=INTEGER}
    </select>

    <select id="getCompletedAfterSalesByOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        T_AFTER_SALES
        where
        ATFER_SALES_STATUS = 2
        and TYPE = 542
        and ORDER_ID = #{saleOrderId,jdbcType=INTEGER}
    </select>

<!--auto generated by MybatisCodeHelper on 2024-04-10-->
    <update id="updateClientStatusByAfterSalesId">
        update T_AFTER_SALES
        set CLIENT_STATUS=#{updatedClientStatus,jdbcType=TINYINT}
        where AFTER_SALES_ID=#{afterSalesId,jdbcType=INTEGER}
    </update>

    <select id="getPushInfo" resultMap="PushInfoResultMap">
        SELECT ASS.ORDER_NO                 AS orderNo,
               ASS.AFTER_SALES_ID           AS afterSalesId,
               ASS.AFTER_SALES_NO           AS afterSaleOrderNo,
               ASS.TYPE                     AS afterSalesTypeId,
               ASD.COMMENTS                 AS afterSalesComment,
               ASD.REASON                   AS afterSalesReasonId,
               SOD.TITLE                    AS afterSalesReasonName,
               ASS.ATFER_SALES_STATUS       AS afterSalesStatus,
               ASS.STATUS                   AS status,
               ASS.VERIFIES_NOT_PASS_REASON AS verifiesNotPassReason,
               ASD.AFTER_CONNECT_USERNAME   AS contactUserName,
               ASD.AFTER_CONNECT_PHONE      AS contactMobile,
               ASD.AREA_ID                  AS areaId,
               ASD.ADDRESS                  AS contactAddress
        FROM T_AFTER_SALES ASS
                 LEFT JOIN T_AFTER_SALES_DETAIL ASD ON ASS.AFTER_SALES_ID = ASD.AFTER_SALES_ID
                 LEFT JOIN T_SYS_OPTION_DEFINITION SOD ON ASD.REASON = SOD.SYS_OPTION_DEFINITION_ID
        WHERE ASS.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
    </select>

    <select id="getAfterSalesAttachmentById"
            resultType="com.vedeng.erp.aftersale.domain.dto.PushAfterSalesAttachmentDto">
        SELECT `NAME`                as attachmentsName,
               CONCAT(`DOMAIN`, URI) as attachmentsUrl
        FROM T_ATTACHMENT
        WHERE RELATED_ID = #{afterSalesId,jdbcType=INTEGER}
          AND ATTACHMENT_FUNCTION = #{afterSalesTypeId,jdbcType=INTEGER}
          AND IS_DELETED = 0
    </select>

    <select id="getPushGoodsByAfterSalesId" resultType="com.vedeng.erp.aftersale.domain.dto.PushAfterSalesGoodsDto">
        SELECT
        ASG.NUM AS goodsNum, G.SKU AS goodsSku, S.IS_GIFT AS isGift
        FROM T_AFTER_SALES_GOODS ASG
        LEFT JOIN T_GOODS G ON ASG.GOODS_ID = G.GOODS_ID
        LEFT JOIN V_CORE_SKU SKU ON G.SKU = SKU.SKU_NO
        LEFT JOIN T_SALEORDER_GOODS S ON S.SALEORDER_GOODS_ID = ASG.ORDER_DETAIL_ID
        WHERE ASG.AFTER_SALES_ID = #{afterSalesId,jdbcType = INTEGER}
        AND SKU.SKU_ID NOT IN (
        SELECT COMMENTS FROM T_SYS_OPTION_DEFINITION
        WHERE PARENT_ID = 693
        )
    </select>
</mapper>