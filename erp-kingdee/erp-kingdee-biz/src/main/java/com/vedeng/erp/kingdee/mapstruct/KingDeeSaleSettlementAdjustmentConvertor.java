package com.vedeng.erp.kingdee.mapstruct;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.kingdee.domain.entity.KingDeeSaleSettlementAdjustmentEntity;
import com.vedeng.erp.kingdee.dto.KingDeeSaleSettlementAdjustmentDto;
import com.vedeng.erp.kingdee.dto.KingDeeSaleSettlementAdjustmentEntityDto;
import org.mapstruct.*;

import java.util.List;

/**
 * @description: 销售结算调整单 dto转entity
 * @author: Suqin
 * @date: 19:05
 **/
@Mapper(componentModel = "spring")
public interface KingDeeSaleSettlementAdjustmentConvertor extends BaseMapStruct<KingDeeSaleSettlementAdjustmentEntity, KingDeeSaleSettlementAdjustmentDto> {

    @Override
    @Mapping(target = "FQzokBddjtid", source = "FQzokBddjtid")
    @Mapping(target = "FEntity", source = "FEntityList", qualifiedByName = "listToJsonArray")
    KingDeeSaleSettlementAdjustmentEntity toEntity(KingDeeSaleSettlementAdjustmentDto kingDeeSaleSettlementAdjustmentDto);

    @Named("listToJsonArray")
    default JSONArray listToJsonArray(List<KingDeeSaleSettlementAdjustmentEntityDto> fEntityList) {
        if (CollUtil.isEmpty(fEntityList)) {
            return null;
        }
        return JSON.parseArray(JSON.toJSONString(fEntityList));
    }
}
