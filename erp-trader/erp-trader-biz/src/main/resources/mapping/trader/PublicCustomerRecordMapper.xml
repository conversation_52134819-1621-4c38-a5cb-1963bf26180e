<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.erp.trader.mapper.PublicCustomerRecordMapper">

    <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.PublicCustomerRecord" >
        <!--          -->
        <id column="PUBLIC_CUSTOMER_RECORD_ID" property="publicCustomerRecordId" jdbcType="BIGINT" />
        <result column="TRADER_CUSTOMER_ID" property="traderCustomerId" jdbcType="INTEGER" />
        <result column="ORIGIN_USER_ID" property="originUserId" jdbcType="INTEGER" />
        <result column="IS_PRIVATIZED" property="isPrivatized" jdbcType="INTEGER" />
        <result column="PRIVATIZED_TIME" property="privatizedTime" jdbcType="BIGINT" />
        <result column="REVOCATION_PROTECTION_DEADLINE" property="revocationProtectionDeadline" jdbcType="BIGINT" />
        <result column="IS_UNLOCK" property="isUnlock" jdbcType="INTEGER" />
        <result column="UNLOCK_TIME" property="unLockTime" jdbcType="BIGINT" />
        <result column="UNLOCK_RELATED_ID" property="unlockRelatedId" jdbcType="INTEGER" />
        <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
        <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
        <result column="UPDATER" property="updater" jdbcType="INTEGER" />
    </resultMap>
    <sql id="Base_Column_List" >
        <!--          -->
        PUBLIC_CUSTOMER_RECORD_ID, TRADER_CUSTOMER_ID, ORIGIN_USER_ID, IS_PRIVATIZED, PRIVATIZED_TIME,
        REVOCATION_PROTECTION_DEADLINE, IS_UNLOCK, UNLOCK_TIME, UNLOCK_RELATED_ID, ADD_TIME,
        MOD_TIME, UPDATER
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
        <!--          -->
        select
        <include refid="Base_Column_List" />
        from T_PUBLIC_CUSTOMER_RECORD
        where PUBLIC_CUSTOMER_RECORD_ID = #{publicCustomerRecordId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
        <!--          -->
        delete from T_PUBLIC_CUSTOMER_RECORD
        where PUBLIC_CUSTOMER_RECORD_ID = #{publicCustomerRecordId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.vedeng.erp.trader.domain.PublicCustomerRecord" >
        <!--          -->
        insert into T_PUBLIC_CUSTOMER_RECORD (PUBLIC_CUSTOMER_RECORD_ID, TRADER_CUSTOMER_ID,
        ORIGIN_USER_ID, IS_PRIVATIZED, PRIVATIZED_TIME,
        REVOCATION_PROTECTION_DEADLINE, IS_UNLOCK,
        UNLOCK_TIME, UNLOCK_RELATED_ID, ADD_TIME,
        MOD_TIME, UPDATER)
        values (#{publicCustomerRecordId,jdbcType=BIGINT}, #{traderCustomerId,jdbcType=INTEGER},
        #{originUserId,jdbcType=INTEGER}, #{isPrivatized,jdbcType=INTEGER}, #{privatizedTime,jdbcType=BIGINT},
        #{revocationProtectionDeadline,jdbcType=BIGINT}, #{isUnlock,jdbcType=INTEGER},
        #{unLockTime,jdbcType=BIGINT}, #{unlockRelatedId,jdbcType=INTEGER}, #{addTime,jdbcType=BIGINT},
        #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" parameterType="com.vedeng.erp.trader.domain.PublicCustomerRecord" >
        <!--          -->
        insert into T_PUBLIC_CUSTOMER_RECORD
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="publicCustomerRecordId != null" >
                PUBLIC_CUSTOMER_RECORD_ID,
            </if>
            <if test="traderCustomerId != null" >
                TRADER_CUSTOMER_ID,
            </if>
            <if test="originUserId != null" >
                ORIGIN_USER_ID,
            </if>
            <if test="isPrivatized != null" >
                IS_PRIVATIZED,
            </if>
            <if test="privatizedTime != null" >
                PRIVATIZED_TIME,
            </if>
            <if test="revocationProtectionDeadline != null" >
                REVOCATION_PROTECTION_DEADLINE,
            </if>
            <if test="isUnlock != null" >
                IS_UNLOCK,
            </if>
            <if test="unLockTime != null" >
                UNLOCK_TIME,
            </if>
            <if test="unlockRelatedId != null" >
                UNLOCK_RELATED_ID,
            </if>
            <if test="addTime != null" >
                ADD_TIME,
            </if>
            <if test="modTime != null" >
                MOD_TIME,
            </if>
            <if test="updater != null" >
                UPDATER,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="publicCustomerRecordId != null" >
                #{publicCustomerRecordId,jdbcType=BIGINT},
            </if>
            <if test="traderCustomerId != null" >
                #{traderCustomerId,jdbcType=INTEGER},
            </if>
            <if test="originUserId != null" >
                #{originUserId,jdbcType=INTEGER},
            </if>
            <if test="isPrivatized != null" >
                #{isPrivatized,jdbcType=INTEGER},
            </if>
            <if test="privatizedTime != null" >
                #{privatizedTime,jdbcType=BIGINT},
            </if>
            <if test="revocationProtectionDeadline != null" >
                #{revocationProtectionDeadline,jdbcType=BIGINT},
            </if>
            <if test="isUnlock != null" >
                #{isUnlock,jdbcType=INTEGER},
            </if>
            <if test="unLockTime != null" >
                #{unLockTime,jdbcType=BIGINT},
            </if>
            <if test="unlockRelatedId != null" >
                #{unlockRelatedId,jdbcType=INTEGER},
            </if>
            <if test="addTime != null" >
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="modTime != null" >
                #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null" >
                #{updater,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.PublicCustomerRecord" >
        <!--          -->
        update T_PUBLIC_CUSTOMER_RECORD
        <set >
            <if test="traderCustomerId != null" >
                TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER},
            </if>
            <if test="originUserId != null" >
                ORIGIN_USER_ID = #{originUserId,jdbcType=INTEGER},
            </if>
            <if test="isPrivatized != null" >
                IS_PRIVATIZED = #{isPrivatized,jdbcType=INTEGER},
            </if>
            <if test="privatizedTime != null" >
                PRIVATIZED_TIME = #{privatizedTime,jdbcType=BIGINT},
            </if>
            <if test="revocationProtectionDeadline != null" >
                REVOCATION_PROTECTION_DEADLINE = #{revocationProtectionDeadline,jdbcType=BIGINT},
            </if>
            <if test="isUnlock != null" >
                IS_UNLOCK = #{isUnlock,jdbcType=INTEGER},
            </if>
            <if test="unLockTime != null" >
                UNLOCK_TIME = #{unLockTime,jdbcType=BIGINT},
            </if>
            <if test="unlockRelatedId != null" >
                UNLOCK_RELATED_ID = #{unlockRelatedId,jdbcType=INTEGER},
            </if>
            <if test="addTime != null" >
                ADD_TIME = #{addTime,jdbcType=BIGINT},
            </if>
            <if test="modTime != null" >
                MOD_TIME = #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null" >
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
        </set>
        where PUBLIC_CUSTOMER_RECORD_ID = #{publicCustomerRecordId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.PublicCustomerRecord" >
        <!--          -->
        update T_PUBLIC_CUSTOMER_RECORD
        set TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER},
        ORIGIN_USER_ID = #{originUserId,jdbcType=INTEGER},
        IS_PRIVATIZED = #{isPrivatized,jdbcType=INTEGER},
        PRIVATIZED_TIME = #{privatizedTime,jdbcType=BIGINT},
        REVOCATION_PROTECTION_DEADLINE = #{revocationProtectionDeadline,jdbcType=BIGINT},
        IS_UNLOCK = #{isUnlock,jdbcType=INTEGER},
        UNLOCK_TIME = #{unLockTime,jdbcType=BIGINT},
        UNLOCK_RELATED_ID = #{unlockRelatedId,jdbcType=INTEGER},
        ADD_TIME = #{addTime,jdbcType=BIGINT},
        MOD_TIME = #{modTime,jdbcType=BIGINT},
        UPDATER = #{updater,jdbcType=INTEGER}
        where PUBLIC_CUSTOMER_RECORD_ID = #{publicCustomerRecordId,jdbcType=BIGINT}
    </update>
    <update id="unlockPublicCustomer">
        update T_PUBLIC_CUSTOMER_RECORD
        set IS_UNLOCK = #{isUnlock}, UNLOCK_TIME = unix_timestamp() * 1000, UNLOCK_RELATED_ID = #{unlockRelatedId}, MOD_TIME = unix_timestamp() * 1000
        where IS_PRIVATIZED = 1 and TRADER_CUSTOMER_ID in
        <foreach collection="traderCustomerIdList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>
    <insert id="batchSavePublicCustomerRecords">
        INSERT INTO T_PUBLIC_CUSTOMER_RECORD ( `TRADER_CUSTOMER_ID`, `ORIGIN_USER_ID`, `ADD_TIME` )
        VALUES
        <foreach collection="traderCustomerDtoList" separator="," item="traderCustomer">
            (#{traderCustomer.traderCustomerId,jdbcType=INTEGER},
            #{traderCustomer.userId,jdbcType=INTEGER},
            unix_timestamp( now( ) ) * 1000)
        </foreach>
    </insert>

    <select id="getProtectedCustomerIds" resultType="java.lang.Integer">
        SELECT DISTINCT TRADER_CUSTOMER_ID
        FROM `T_PUBLIC_CUSTOMER_RECORD`
        WHERE REVOCATION_PROTECTION_DEADLINE &gt; unix_timestamp(now()) * 1000
    </select>

    <select id="queryInfoByCustId" resultType="com.vedeng.erp.trader.domain.PublicCustomerRecord">
        SELECT
            <include refid="Base_Column_List" />
        FROM T_PUBLIC_CUSTOMER_RECORD
        WHERE TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER}
        ORDER BY PUBLIC_CUSTOMER_RECORD_ID DESC LIMIT 1
    </select>
    <select id="getUnPrivatizedByCustomerId" resultType="java.lang.Integer">
        select count(1)
        from T_PUBLIC_CUSTOMER_RECORD where IS_PRIVATIZED = 0 and TRADER_CUSTOMER_ID = #{traderCustomerId}
    </select>
    <select id="getPublicCustomerRecordListByTraderCustomerId" resultType="com.vedeng.erp.trader.domain.PublicCustomerRecord">
        select *
        from T_PUBLIC_CUSTOMER_RECORD where TRADER_CUSTOMER_ID = #{traderCustomerId}
    </select>
    <select id="checkTraderCustomerIsPrivatizedAndNotUnlocked" resultType="java.lang.Integer">
        select distinct TRADER_CUSTOMER_ID
        from T_PUBLIC_CUSTOMER_RECORD where IS_PRIVATIZED = 1 and IS_UNLOCK = 0 and TRADER_CUSTOMER_ID in
        <foreach collection="traderCustomerIdList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    
    <select id="getByTraderCustomerId" resultType="com.vedeng.erp.business.dto.PublicCustomerRecordDto">
        select a.*, b.USERNAME originUserName, b.NUMBER originUserNumber,d.TRADER_ID,d.TRADER_NAME
        from T_PUBLIC_CUSTOMER_RECORD a
                 left join T_USER b on b.USER_ID = a.ORIGIN_USER_ID
                 left join T_TRADER_CUSTOMER c on c.TRADER_CUSTOMER_ID = a.TRADER_CUSTOMER_ID
                 left join T_TRADER d on d.TRADER_ID = c.TRADER_ID
        where 1 = 1
            <if test="traderCustomerId != null">
                and a.TRADER_CUSTOMER_ID = #{traderCustomerId}
            </if>
            <if test="traderId != null ">
                and c.TRADER_ID = #{traderId}
            </if>
            <if test="traderName != null ">
                and d.TRADER_NAME = #{traderName}
            </if>
        order by a.PUBLIC_CUSTOMER_RECORD_ID desc
        limit 1;
    </select>
    
    <select id="getMaxRecordList" resultType="com.vedeng.erp.business.dto.PublicCustomerRecordDto">
        select *
        from T_PUBLIC_CUSTOMER_RECORD
        where PUBLIC_CUSTOMER_RECORD_ID in (select max(a.PUBLIC_CUSTOMER_RECORD_ID) PUBLIC_CUSTOMER_RECORD_ID
                                            from T_PUBLIC_CUSTOMER_RECORD a
                                                     left join T_USER b on b.USER_ID = a.ORIGIN_USER_ID
                                                     left join T_TRADER_CUSTOMER c on c.TRADER_CUSTOMER_ID = a.TRADER_CUSTOMER_ID
                                            where c.TRADER_ID in
                                            <foreach collection="traderIdList" item="item" index="index" open="(" close=")" separator=",">
                                                #{item}
                                            </foreach>
                                            group by TRADER_ID)
    </select>
    
    <select id="getPublicCustomerNum" resultType="java.lang.Integer">
        select count(1) from (
            select a.TRADER_CUSTOMER_ID
            from T_PUBLIC_CUSTOMER_RECORD a
            left join T_TRADER_CUSTOMER b on a.TRADER_CUSTOMER_ID = b.TRADER_CUSTOMER_ID
            left join T_TRADER c on c.TRADER_ID = b.TRADER_ID
            where a.IS_PRIVATIZED = 0
            and a.IS_UNLOCK = 0
            and c.COMPANY_ID = 1
            <if test="areaId != null and areaId != 0">
                and FIND_IN_SET(#{areaId},c.AREA_IDS)
            </if>
            and a.ORIGIN_USER_ID = #{userId,jdbcType=INTEGER}
            group by a.TRADER_CUSTOMER_ID
        )cc
    </select>

    <select id="getPublicCustomerList" resultType="com.vedeng.erp.business.dto.PublicCustomerRecordDto">
        select a.*, d.USERNAME originUserName, d.NUMBER originUserNumber,b.TRADER_ID
        from T_PUBLIC_CUSTOMER_RECORD a
        left join T_TRADER_CUSTOMER b on a.TRADER_CUSTOMER_ID = b.TRADER_CUSTOMER_ID
        left join T_TRADER c on c.TRADER_ID = b.TRADER_ID
        left join T_USER d on d.USER_ID = a.ORIGIN_USER_ID
        where a.IS_PRIVATIZED = 0
        and a.IS_UNLOCK = 0
        and a.ORIGIN_USER_ID = #{userId,jdbcType=INTEGER}
        and c.COMPANY_ID = 1
        <if test="areaId != null and areaId != 0">
             and FIND_IN_SET(#{areaId},c.AREA_IDS)
        </if>
    </select>
    
    <update id="updateOrgUserId">
        update T_PUBLIC_CUSTOMER_RECORD
        set ORIGIN_USER_ID = #{originUserId}
        where PUBLIC_CUSTOMER_RECORD_ID in 
        <foreach collection="ids" item="item" index="index" open="(" close=")" separator=",">
             #{item}
        </foreach>
    </update>
</mapper>
