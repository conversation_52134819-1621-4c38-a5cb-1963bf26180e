package com.wms.unitconversionorder.dao;

import java.util.List;

import com.wms.unitconversionorder.model.dto.WmsUnitConversionOrderDto;
import com.wms.unitconversionorder.model.entity.WmsUnitConversionOrder;
import org.apache.ibatis.annotations.Param;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2023/2/17 10:57
 **/
public interface WmsUnitConversionOrderMapper {
    /**
     * delete by primary key
     * @param wmsUnitConversionOrderId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer wmsUnitConversionOrderId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(WmsUnitConversionOrder record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(WmsUnitConversionOrder record);

    /**
     * select by primary key
     * @param wmsUnitConversionOrderId primary key
     * @return object by primary key
     */
    WmsUnitConversionOrder selectByPrimaryKey(Integer wmsUnitConversionOrderId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(WmsUnitConversionOrder record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(WmsUnitConversionOrder record);

    int batchInsert(@Param("list") List<WmsUnitConversionOrder> list);

    /**
     * 查询 订单信息
     * @param wmsUnitConversionOrderId
     * @return
     */
    WmsUnitConversionOrderDto selectByWmsUnitConversionOrderId(Integer wmsUnitConversionOrderId);

    WmsUnitConversionOrder selectByWmsUnitConversionOrderNo(@Param("wmsUnitConversionOrderNo")String wmsUnitConversionOrderNo);


}