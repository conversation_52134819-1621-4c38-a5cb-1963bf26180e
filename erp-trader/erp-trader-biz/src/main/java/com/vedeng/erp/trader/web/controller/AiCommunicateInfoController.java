package com.vedeng.erp.trader.web.controller;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.BaseController;
import com.vedeng.common.core.base.ExceptionController;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/18 15:26
 */
@Controller
@ExceptionController
@RequestMapping("/ai")
public class AiCommunicateInfoController extends BaseController {

    @RequestMapping(value = "/communicateInfo")
    @NoNeedAccessAuthorization
    public ModelAndView communicateInfo(@RequestParam(value = "communicateRecordId", required = false) Integer communicateRecordId,
                                        @RequestParam(value = "selectedTab",required = false) String selectedTab) {
        ModelAndView mv = new ModelAndView("vue/view/ai/communicateInfo");
        mv.addObject("communicateRecordId", communicateRecordId);
        mv.addObject("selectedTab", selectedTab);
        return mv;
    }

}