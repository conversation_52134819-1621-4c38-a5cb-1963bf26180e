package com.newtask.model;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/11/16 10:54
 **/
public class IndexData {
    private Integer traderId;
    /**
     * 企业类型名称
     */
    private String typeName;
    /**
     * 代付款证明审核状态
     */
    private Integer payofStatus;
    /**
     * 代付款证明审核不通过原因
     */
    private String payofCheckMsg;
    /**
     * 资质审核状态
     */
    private Integer status;
    /**
     * 资质审核不通过原因
     */
    private String customerCheckMsg;
    /**
     * 是否展示会员价
     */
    private Integer showPrice;


    /**
     * ssoAccountId，修改注册人姓名使用
     */
    private Integer ssoAccountId;
    /**
     * 注册人姓名
     */
    private String registerName;

    public Integer getTraderId() {
        return traderId;
    }

    public void setTraderId(Integer traderId) {
        this.traderId = traderId;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public Integer getPayofStatus() {
        return payofStatus;
    }

    public void setPayofStatus(Integer payofStatus) {
        this.payofStatus = payofStatus;
    }

    public String getPayofCheckMsg() {
        return payofCheckMsg;
    }

    public void setPayofCheckMsg(String payofCheckMsg) {
        this.payofCheckMsg = payofCheckMsg;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCustomerCheckMsg() {
        return customerCheckMsg;
    }

    public void setCustomerCheckMsg(String customerCheckMsg) {
        this.customerCheckMsg = customerCheckMsg;
    }

    public Integer getShowPrice() {
        return showPrice;
    }

    public void setShowPrice(Integer showPrice) {
        this.showPrice = showPrice;
    }

    public Integer getSsoAccountId() {
        return ssoAccountId;
    }

    public void setSsoAccountId(Integer ssoAccountId) {
        this.ssoAccountId = ssoAccountId;
    }

    public String getRegisterName() {
        return registerName;
    }

    public void setRegisterName(String registerName) {
        this.registerName = registerName;
    }
}
