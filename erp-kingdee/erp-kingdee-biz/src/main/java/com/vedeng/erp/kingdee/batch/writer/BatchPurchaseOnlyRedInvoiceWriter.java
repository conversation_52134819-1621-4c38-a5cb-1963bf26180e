package com.vedeng.erp.kingdee.batch.writer;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceVoucherDto;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceVoucherMapper;
import com.vedeng.erp.kingdee.domain.command.KingDeePurchaseVatPlainInvoiceCommand;
import com.vedeng.erp.kingdee.domain.command.KingDeePurchaseVatSpecialInvoiceCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeePurchaseVatPlainInvoiceEntity;
import com.vedeng.erp.kingdee.domain.entity.KingDeePurchaseVatSpecialInvoiceEntity;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.erp.kingdee.repository.mappers.KingDeePurchaseVatPlainInvoiceMapper;
import com.vedeng.erp.kingdee.repository.mappers.KingDeePurchaseVatSpecialInvoiceMapper;
import com.vedeng.erp.kingdee.mapstruct.KingDeePurchaseVatPlainInvoiceCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeePurchaseVatPlainInvoiceConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeePurchaseVatSpecialInvoiceCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeePurchaseVatSpecialInvoiceConvertor;
import com.vedeng.erp.kingdee.service.KingDeePurchaseInvoicePlainApiService;
import com.vedeng.erp.kingdee.service.KingDeePurchaseInvoiceSpecialApiService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购仅退票推送金蝶
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchPurchaseOnlyRedInvoiceWriter extends BaseWriter<KingDeePayCommonAndInvoiceDto> {


    @Autowired
    private KingDeePurchaseInvoiceSpecialApiService kingDeePurchaseInvoiceSpecialApiService;

    @Autowired
    private KingDeePurchaseInvoicePlainApiService kingDeePurchaseInvoicePlainApiService;


    @Override
    public void doWrite(KingDeePayCommonAndInvoiceDto item, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("采购仅退票红票发票推送：{}", JSON.toJSONString(item));
        if (item.getSpecial()) {
            if (Objects.isNull(item.getPurchaseVatSpecialInvoiceDto())) {
                return;
            }
        } else {
            if (Objects.isNull(item.getPurchaseVatPlainInvoiceDto())) {
                return;
            }
        }
        // 推票
        if (item.getSpecial()) {
            // 专票推送
            PurchaseVatSpecialInvoiceDto purchaseVatSpecialInvoiceDto = item.getPurchaseVatSpecialInvoiceDto();
            specialWrite(purchaseVatSpecialInvoiceDto);
        } else {
            // 普票推送
            PurchaseVatPlainInvoiceDto purchaseVatPlainInvoiceDto = item.getPurchaseVatPlainInvoiceDto();
            plainWrite(purchaseVatPlainInvoiceDto);

        }

    }

    /**
     * 专票
     * @param dto
     */
    private void specialWrite(PurchaseVatSpecialInvoiceDto dto) {
        log.info("采购仅退票红字专票入参-->{}",JSON.toJSON(dto));
        dto.setKingDeeBizEnums(KingDeeBizEnums.savePurchaseInvoiceSpecial);
        kingDeePurchaseInvoiceSpecialApiService.register(dto,true);
    }


    /**
     * 普票
     * @param dto
     */
    private void plainWrite(PurchaseVatPlainInvoiceDto dto) {
        log.info("采购仅退票红字普票入参-->{}",JSON.toJSON(dto));
        dto.setKingDeeBizEnums(KingDeeBizEnums.savePurchaseInvoicePlain);
        kingDeePurchaseInvoicePlainApiService.register(dto,true);
    }

}
