package com.vedeng.erp.buyorder.service;

import com.vedeng.erp.buyorder.dto.BuyOrderRebateChargeApplyApiDto;

/**
 * 采购返利结算收款申请 api接口
 * Description: 采购返利结算收款申请 api接口
 */
public interface BuyOrderRebateChargeApplyApiService {


    /**
     * 根据主键id查询 采购返利结算收款申请 详情
     *
     * @param buyOrderRebateChargeId 主键id
     * @return BuyOrderRebateChargeApplyDto
     */
    BuyOrderRebateChargeApplyApiDto getBuyOrderRebateChargeApplyApiDtoById(Integer buyOrderRebateChargeId);

}
