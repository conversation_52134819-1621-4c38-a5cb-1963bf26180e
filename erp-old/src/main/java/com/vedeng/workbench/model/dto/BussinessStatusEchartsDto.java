package com.vedeng.workbench.model.dto;


import lombok.Data;

import java.math.BigDecimal;


/**
 * 传输商机状态属性值
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2020/11/8 5:11.
 * @author: <PERSON><PERSON>.
 */
@Data
public class BussinessStatusEchartsDto {

    private Integer orgId;

    private String statusName;

    private Integer statusId;

    private BigDecimal statusAmount;

    private Integer statusNum;

    private String rate;
}
