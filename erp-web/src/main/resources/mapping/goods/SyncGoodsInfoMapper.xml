<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.dao.SyncGoodsInfoMapper">
    <resultMap id="syncSkuInfo2EsResultMap" type="com.vedeng.goods.model.dto.SyncSkuInfo2EsDto">
        <result column="SKU_ID" jdbcType="INTEGER" property="skuId"/>
        <result column="SKU_NO" jdbcType="VARCHAR" property="skuNo"/>
        <result column="SKU_NO_WS" jdbcType="VARCHAR" property="skuNoWs"/>
        <result column="SPU_ID" jdbcType="INTEGER" property="spuId"/>
        <result column="SPU_TYPE" jdbcType="INTEGER" property="spuType"/>
        <result column="TITLE" jdbcType="VARCHAR" property="title"/>
        <result column="PRICE" javaType="DECIMAL" property="price"/>
        <result column="IS_VERIFIED_PRICE" javaType="INTEGER" property="isVerifiedPrice"/>
        <result column="IS_AUTHORIZED" jdbcType="INTEGER" property="isAuthorized"/>
        <result column="IS_SUPPORT_INSTALLATION" jdbcType="INTEGER" property="isSupportInstallation"/>
        <result column="SOURCE" jdbcType="INTEGER" property="source"/>
        <result column="ONE_YEAR_SALE_NUM" jdbcType="INTEGER" property="oneYearSaleNum"/>
        <result column="DISTRIBUTION_PRICE" javaType="DECIMAL" property="distributionPrice"/>
        <result column="TERMINAL_PRICE" javaType="DECIMAL" property="terminalPrice"/>
        <result column="AVGPRICE" javaType="DECIMAL" property="oneYearAvgPrice"/>
        <result column="AVAILABLE_STOCK_NUM" jdbcType="INTEGER" property="availableStockNum"/>
        <result column="ONWAYNUM" jdbcType="INTEGER" property="onWayNum"/>
        <result column="STOCK_NUM" jdbcType="INTEGER" property="stockNum"/>
        <result column="RULE_LEVEL" jdbcType="INTEGER" property="ruleLevel"/>
        <result column="RULE_GEAR" jdbcType="INTEGER" property="ruleGear"/>
        <result column="PLATFORM_SKU_ID" jdbcType="INTEGER" property="platformSkuId"/>
        <result column="MODEL" jdbcType="VARCHAR" property="model"/>
        <result column="SPEC" jdbcType="VARCHAR" property="spec"/>
        <result column="HAS_STOCK" jdbcType="INTEGER" property="hasStock"/>
        <result column="erpCatId3" jdbcType="INTEGER" property="erpCatId3"/>
        <result column="erpCatId2" jdbcType="INTEGER" property="erpCatId2"/>
        <result column="erpCatId1" jdbcType="INTEGER" property="erpCatId1"/>
        <result column="catName3" jdbcType="VARCHAR" property="catName3"/>
        <result column="catName2" jdbcType="VARCHAR" property="catName2"/>
        <result column="catName1" jdbcType="VARCHAR" property="catName1"/>
        <result column="BRAND_ID" jdbcType="INTEGER" property="brandId"/>
        <result column="BRAND_NAME" jdbcType="VARCHAR" property="brandName"/>
        <result column="BRAND_NAME_EN" jdbcType="VARCHAR" property="brandNameEn"/>
        <result column="INSTITUTION_LEVEL_IDS" jdbcType="VARCHAR" property="mechanismIds"/>
        <result column="SEO_KEYWORDS" jdbcType="VARCHAR" property="keyword"/>
        <result column="SIX_MONTH_SALE_NUM" jdbcType="INTEGER" property="ruleCategorySaleCount"/>
    </resultMap>


    <select id="getSyncSkuInfoBySkuIds" resultMap="syncSkuInfo2EsResultMap">
        SELECT
        A.SKU_ID,A.SKU_NO,CONCAT( A.SKU_NO, ' ', A.SKU_ID ) SKU_NO_WS,A.SPU_ID,A.INSTITUTION_LEVEL_IDS,
        A.SKU_NAME TITLE,IF ( A.DISTRIBUTION_PRICE = 0 || A.DISTRIBUTION_PRICE is NULL, -1, A.DISTRIBUTION_PRICE ) PRICE,
        IF ( A.TERMINAL_PRICE > 0, 1, 0 ) IS_VERIFIED_PRICE,IF (B.SPU_TYPE > 0,B.SPU_TYPE,-1) SPU_TYPE,
        A.IS_AUTHORIZED,A.IS_INSTALLABLE IS_SUPPORT_INSTALLATION,IF ( E.BRAND_NATURE = 1, 2, 1 ) SOURCE,
        A.ONE_YEAR_SALE_NUM,A.DISTRIBUTION_PRICE,A.TERMINAL_PRICE,A.AVGPRICE,COI.SEO_KEYWORDS,
        A.AVAILABLE_STOCK_NUM,TT.ONWAYNUM,A.STOCK_NUM,IFNULL(LV.ID,0) RULE_LEVEL,IFNULL(POS.ID,0) RULE_GEAR,
        A.SKU_ID PLATFORM_SKU_ID,A.MODEL,A.SPEC, IF ( A.AVAILABLE_STOCK_NUM > 0, 1, 0 ) HAS_STOCK,
        CB.BASE_CATEGORY_ID erpCatId3,DB.BASE_CATEGORY_ID erpCatId2,EB.BASE_CATEGORY_ID erpCatId1,
        CB.BASE_CATEGORY_NAME catName3,DB.BASE_CATEGORY_NAME catName2,EB.BASE_CATEGORY_NAME catName1,
        E.BRAND_ID,IFNULL(E.BRAND_NAME,'') BRAND_NAME,E.BRAND_NAME_EN,
        CB.SIX_MONTH_SALE_NUM
        FROM
        V_CORE_SKU A
        LEFT JOIN V_GOODS_LEVEL LV ON A.GOODS_LEVEL_NO = LV.ID
        LEFT JOIN V_GOODS_POSITION POS ON A.GOODS_POSITION_NO = POS.ID
        LEFT JOIN V_CORE_SPU B ON A.SPU_ID = B.SPU_ID
        LEFT JOIN T_SYS_OPTION_DEFINITION PT ON B.SPU_TYPE = PT.SYS_OPTION_DEFINITION_ID
        LEFT JOIN T_FIRST_ENGAGE C ON C.FIRST_ENGAGE_ID = B.FIRST_ENGAGE_ID
        LEFT JOIN T_REGISTRATION_NUMBER D ON C.REGISTRATION_NUMBER_ID = D.REGISTRATION_NUMBER_ID
        LEFT JOIN T_BRAND E ON B.BRAND_ID = E.BRAND_ID
        AND E.COMPANY_ID = 1
        LEFT JOIN V_BASE_CATEGORY CB ON B.CATEGORY_ID = CB.BASE_CATEGORY_ID
        LEFT JOIN V_BASE_CATEGORY DB ON CB.PARENT_ID = DB.BASE_CATEGORY_ID
        LEFT JOIN V_BASE_CATEGORY EB ON DB.PARENT_ID = EB.BASE_CATEGORY_ID
        LEFT JOIN V_CORE_OPERATE_INFO COI ON A.SKU_ID = COI.SKU_ID
        AND COI.OPERATE_INFO_TYPE = 2
        LEFT JOIN (
        SELECT
        SUM( T.ONWAYNUM ) AS 'ONWAYNUM',
        T.GOODS_ID
        FROM
        (
        SELECT
        a.GOODS_ID,
        COALESCE (
        SUM( ( a.NUM - IFNULL( a.ARRIVAL_NUM, 0 ) - IFNULL( TT.SHNUM, 0 ) ) ),
        0
        ) ONWAYNUM,
        b.BUYORDER_NO,
        a.ESTIMATE_ARRIVAL_TIME,
        b.VALID_TIME,
        b.BUYORDER_ID
        FROM
        T_BUYORDER_GOODS a
        LEFT JOIN T_BUYORDER b ON a.BUYORDER_ID = b.BUYORDER_ID
        LEFT JOIN (
        SELECT
        IFNULL( SUM( d.NUM ), 0 ) SHNUM,
        c.ORDER_ID,
        d.GOODS_ID,
        d.ORDER_DETAIL_ID
        FROM
        T_AFTER_SALES c
        LEFT JOIN T_AFTER_SALES_GOODS d ON c.AFTER_SALES_ID = d.AFTER_SALES_ID
        WHERE
        c.TYPE = 546
        AND c.VALID_STATUS = 1
        AND c.ATFER_SALES_STATUS = 2
        AND c.SUBJECT_TYPE = 536
        AND d.GOODS_ID in
        <foreach collection="skuIds" item="skuId" index="index" open="(" close=")" separator=",">
            #{skuId}
        </foreach>
        GROUP BY
        d.ORDER_DETAIL_ID
        ) TT ON a.BUYORDER_GOODS_ID = TT.ORDER_DETAIL_ID
        WHERE
        b.VALID_STATUS = 1
        AND b.COMPANY_ID = 1
        AND b.PAYMENT_STATUS IN ( 1, 2 )
        AND b.DELIVERY_DIRECT = 0
        AND b.ARRIVAL_STATUS IN ( 0, 1 )
        AND b.STATUS != 3
        AND a.GOODS_ID IN
        <foreach collection="skuIds" item="skuId" index="index" open="(" close=")" separator=",">
            #{skuId}
        </foreach>
        GROUP BY
        a.BUYORDER_GOODS_ID
        ) T
        WHERE
        T.ONWAYNUM > 0
        AND T.GOODS_ID IN
        <foreach collection="skuIds" item="skuId" index="index" open="(" close=")" separator=",">
            #{skuId}
        </foreach>
        GROUP BY
        T.GOODS_ID
        ) TT ON TT.GOODS_ID = A.SKU_ID
        WHERE
        A.STATUS = 1
        AND B.STATUS = 1
        AND A.SKU_ID IN
        <foreach collection="skuIds" item="skuId" index="index" open="(" close=")" separator=",">
            #{skuId}
        </foreach>
        GROUP BY
        A.SKU_ID
    </select>

    <select id="getValidSkuIds" resultType="java.lang.Integer">
        SELECT T1.SKU_ID
        FROM V_CORE_SKU T1
                 LEFT JOIN V_CORE_SPU T2 ON T1.SPU_ID = T2.SPU_ID
        WHERE T1.`STATUS` = 1
          AND T2.`STATUS` = 1
        GROUP BY T1.SKU_ID
    </select>

    <select id="getNotValidSkuIds" resultType="java.lang.Integer">
        SELECT T1.SKU_ID
        FROM V_CORE_SKU T1
                 LEFT JOIN V_CORE_SPU T2 ON T1.SPU_ID = T2.SPU_ID
        WHERE (T1.`STATUS` != 1
          OR T2.`STATUS` != 1)
        GROUP BY T1.SKU_ID
    </select>

    <select id="getSyncSkuAttrBySkuIds" resultType="com.vedeng.goods.model.dto.SyncSkuAttrDto">
        SELECT
        b.SKU_ID,a.BASE_ATTRIBUTE_ID,a.BASE_ATTRIBUTE_VALUE_ID,a.ATTR_VALUE,u.UNIT_NAME
        FROM
        V_SKU_ATTR_MAPPING b left join V_BASE_ATTRIBUTE_VALUE a
        on a.BASE_ATTRIBUTE_VALUE_ID = b.BASE_ATTRIBUTE_VALUE_ID
        LEFT JOIN T_UNIT u ON a.UNIT_ID=u.UNIT_ID
        WHERE
        b.SKU_ID IN
        <foreach collection="skuIds" item="skuId" index="index" open="(" close=")" separator=",">
            #{skuId}
        </foreach>
        and a.IS_DELETED = 0
        AND b.`STATUS` =1
        GROUP BY a.BASE_ATTRIBUTE_VALUE_ID,b.SKU_ID
        order by a.BASE_ATTRIBUTE_ID desc ,a.SORT desc
    </select>

    <select id="getSyncSkuDeptBySkuIds" resultType="com.vedeng.goods.model.dto.SyncSkuDeptDto">
        SELECT
        T1.SKU_ID,
        T5.DEPARTMENT_ID,
        T5.DEPARTMENT_NAME
        FROM
        V_CORE_SKU T1
        LEFT JOIN V_CORE_SPU T2 ON T1.SPU_ID = T2.SPU_ID
        LEFT JOIN V_BASE_CATEGORY T3 ON T2.CATEGORY_ID = T3.BASE_CATEGORY_ID
        AND T3.BASE_CATEGORY_LEVEL = 3
        LEFT JOIN V_CATEGORY_DEPARTMENT T4 ON T3.BASE_CATEGORY_ID = T4.CATEGORY_ID
        AND T4.IS_DELETED = 0
        LEFT JOIN T_DEPARTMENTS_HOSPITAL T5 ON T4.DEPARTMENT_ID = T5.DEPARTMENT_ID
        AND T5.IS_DELETE = 0
        WHERE
        T5.DEPARTMENT_ID IS NOT NULL
        AND T1.SKU_ID IN
        <foreach collection="skuIds" item="skuId" index="index" open="(" close=")" separator=",">
            #{skuId}
        </foreach>
        GROUP BY
        T1.SKU_ID,
        T5.DEPARTMENT_ID
    </select>

    <select id="getSyncSkuInspectionBySkuIds" resultType="com.vedeng.goods.model.dto.SyncSkuInspectionDto">
        SELECT
        T1.SKU_ID,
        T4.ID,
        T4.NAME
        FROM
        V_CORE_SKU T1
        LEFT JOIN V_CORE_SPU T2 ON T1.SPU_ID = T2.SPU_ID
        AND T2.STATUS = 1
        LEFT JOIN V_CATEGORY_INSPECTION T3 ON T2.CATEGORY_ID = T3.CATEGORY_ID
        LEFT JOIN T_INSPECTION_ITEM T4 ON T3.INSPECTION_ID = T4.ID
        WHERE
        T1.SKU_ID IN
        <foreach collection="skuIds" item="skuId" index="index" open="(" close=")" separator=",">
            #{skuId}
        </foreach>
        AND T4.ID IS NOT NULL
        GROUP BY
        T1.SKU_ID,
        T4.ID
    </select>

    <select id="getSyncSkuSaleInfoRecent6MonthBySkuIds" resultType="com.vedeng.goods.model.dto.SyncSkuSaleInfoDto">
        SELECT
        T1.GOODS_ID SKU_ID,
        SUM( T1.NUM ) SALE_COUNT,
        SUM( IF ( T2.ORDER_TYPE = 1, T1.MAX_SKU_REFUND_AMOUNT, T1.PRICE * T1.NUM ) ) SALE_TOTAL_MONEY
        FROM
        T_SALEORDER_GOODS T1
        LEFT JOIN T_SALEORDER T2 ON T1.SALEORDER_ID = T2.SALEORDER_ID
        WHERE
        T1.GOODS_ID IN
        <foreach collection="skuIds" item="skuId" index="index" open="(" close=")" separator=",">
            #{skuId}
        </foreach>
        AND T2.PAYMENT_STATUS > 0
        AND T2.ADD_TIME > UNIX_TIMESTAMP(date_sub( curdate(), INTERVAL + 6 MONTH )) * 1000
        GROUP BY
        T1.GOODS_ID
    </select>

    <select id="getSyncSkuCategorySaleInfoBySkuIds"
            resultType="com.vedeng.goods.model.dto.SyncSkuCategorySaleInfoDto">
        SELECT T1.SKU_ID, IFNULL(T3.MYCOUNT,0) MYCOUNT
        FROM V_CORE_SKU T1
        LEFT JOIN V_CORE_SPU T2 ON T1.SPU_ID = T2.SPU_ID
        LEFT JOIN (
        SELECT p.CATEGORY_ID AS categoryId,
        sum(g.NUM) AS MYCOUNT
        FROM V_CORE_SKU k
        LEFT JOIN V_CORE_SPU p ON k.SPU_ID = p.SPU_ID
        LEFT JOIN T_SALEORDER_GOODS g ON g.GOODS_ID = k.SKU_ID
        LEFT JOIN T_SALEORDER s ON s.SALEORDER_ID = g.SALEORDER_ID
        WHERE s.PAYMENT_STATUS > 0
        AND s.ADD_TIME > UNIX_TIMESTAMP(date_sub(curdate(), INTERVAL + 6 MONTH )) * 1000
        GROUP BY p.CATEGORY_ID
        ) T3 ON T2.CATEGORY_ID = T3.categoryId
        WHERE T1.SKU_ID IN
        <foreach collection="skuIds" item="skuId" index="index" open="(" close=")" separator=",">
            #{skuId}
        </foreach>
    </select>

    <select id="getSkuIdsByBrandIds" resultType="java.lang.Integer">
        SELECT
            T2.SKU_ID
        FROM
            V_CORE_SPU T1
                LEFT JOIN V_CORE_SKU T2 ON T1.SPU_ID = T2.SPU_ID
        WHERE
            T1.`STATUS` = 1
          AND T2.`STATUS` = 1
          AND T1.BRAND_ID IN
        <foreach collection="brandIds" item="brandId" index="index" open="(" close=")" separator=",">
            #{brandId}
        </foreach>
        GROUP BY
            T2.SKU_ID
    </select>

    <select id="getSkuIdsByBaseCategoryIds" resultType="java.lang.Integer">
        SELECT
            T2.SKU_ID
        FROM
            V_CORE_SPU T1
                LEFT JOIN V_CORE_SKU T2 ON T1.SPU_ID = T2.SPU_ID
        WHERE
            T1.`STATUS` = 1
          AND T2.`STATUS` = 1
          AND T1.CATEGORY_ID IN
        <foreach collection="baseCategoryIds" item="baseCategoryId" index="index" open="(" close=")" separator=",">
            #{baseCategoryId}
        </foreach>
        GROUP BY
            T2.SKU_ID
    </select>

    <select id="getSkuIdsByDepartmentIds" resultType="java.lang.Integer">
        SELECT
            T3.SKU_ID
        FROM
            V_CATEGORY_DEPARTMENT T1
                INNER JOIN V_CORE_SPU T2 ON T1.CATEGORY_ID = T2.CATEGORY_ID
                AND T2.`STATUS` = 1
                INNER JOIN V_CORE_SKU T3 ON T2.SPU_ID = T3.SPU_ID
        WHERE
            T1.DEPARTMENT_ID IN
        <foreach collection="departmentIds" item="departmentId" index="index" open="(" close=")" separator=",">
            #{departmentId}
        </foreach>
        GROUP BY
            T3.SKU_ID
    </select>

    <select id="getSkuIdsByBaseAttributeIds" resultType="java.lang.Integer">
        SELECT
            SKU_ID
        FROM
            V_SKU_ATTR_MAPPING
        WHERE
            BASE_ATTRIBUTE_ID IN
        <foreach collection="baseAttributeIds" item="baseAttributeId" index="index" open="(" close=")" separator=",">
            #{baseAttributeId}
        </foreach>
        GROUP BY
            SKU_ID
    </select>

    <select id="getThirdBaseCategoryIdsByIdAndLevel" resultType="java.lang.Integer">
        SELECT
            T3.BASE_CATEGORY_ID
        FROM
            V_BASE_CATEGORY T1
            INNER JOIN V_BASE_CATEGORY T2 ON T1.BASE_CATEGORY_ID = T2.PARENT_ID
            INNER JOIN V_BASE_CATEGORY T3 ON T2.BASE_CATEGORY_ID = T3.PARENT_ID
        WHERE
            T1.BASE_CATEGORY_LEVEL = 1
        <choose>
            <when test="level == 1">
                AND T1.BASE_CATEGORY_ID = #{baseCategoryId}
            </when>
            <when test="level == 2">
                AND T2.BASE_CATEGORY_ID = #{baseCategoryId}
            </when>
            <when test="level == 3">
                AND T3.BASE_CATEGORY_ID = #{baseCategoryId}
            </when>
            <otherwise>
                1 == 0
            </otherwise>
        </choose>
        GROUP BY
            T3.BASE_CATEGORY_ID
    </select>

    <select id="selectSkuSceneCategoryBySkuNos" resultType="com.vedeng.goods.model.dto.SyncSkuSceneCategoryDto">
        SELECT DISTINCT
        t.ID AS skuSceneCategoryId,
        s.skuNo AS skuNo,
        CAST(SUBSTRING(s.skuNo, 2) AS UNSIGNED) AS skuId
        FROM T_SKU_SCENE_CATEGORY t
        JOIN (
        <foreach collection="skuNos" item="skuNo" separator="UNION ALL">
            SELECT #{skuNo} AS skuNo
        </foreach>
        ) s
        WHERE FIND_IN_SET(s.skuNo, t.SKU_NOS) > 0
    </select>

</mapper>