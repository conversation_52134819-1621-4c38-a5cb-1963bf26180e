package com.vedeng.esign.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.authorization.model.User;
import com.vedeng.base.api.dto.reqParam.BusinessInfo;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.firstengage.dao.FirstEngageMapper;
import com.vedeng.firstengage.model.FirstEngage;
import com.vedeng.goods.manufacturer.model.Manufacturer;
import com.vedeng.goods.service.ManufacturerService;
import com.vedeng.infrastructure.esign.domain.ElectronicSignParam;
import com.vedeng.infrastructure.esign.enums.ElectronicSignBusinessEnums;
import com.vedeng.infrastructure.esign.enums.VendorCertificateEnums;
import com.vedeng.infrastructure.esign.handle.AbstractElectronicSignHandle;
import com.vedeng.system.dao.AttachmentMapper;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.service.ThreeCertificatesStamp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * 三证签章
 * <AUTHOR>
 */
@Service("threeCertificatesStamp")
@Slf4j
public class ThreeCertificatesStampImpl implements ThreeCertificatesStamp {


    public static final String ATTACHMENT_TYPE = "attachmentType";
    public static final String CERTIFICATE_TYPE = "certificateType";
    public static final String MANUFACTURER_ID = "manufacturerId";
    public static final String USER_ID = "userId";
    public static final String REGISTRATION_NUMBER_ID = "registrationNumberId";
    public static final String ATTACHMENT_FUNCTION = "attachmentFunction";
    public static final String FIRST_ENGAGE_ID = "firstEngageId";
    @Autowired
    private ManufacturerService manufacturerService;
    @Autowired
    private AbstractElectronicSignHandle manufacturerElectronicSignHandle;



    @Resource
    private FirstEngageMapper firstEngageMapper;

    @Resource
    private AttachmentMapper attachmentMapper;



    @Override
    public void certificatesStamp(Integer manufacturerId, User user) {
        if (null == manufacturerId) {
            return;
        }
        Manufacturer manufacturerDetail = manufacturerService.getManufacturerDetail(manufacturerId);
        // 营业执照 1307
        List<Attachment> yzAttachments = manufacturerDetail.getYzAttachments();
        // 生产企业生产许可证 1306
        List<Attachment> scAttachments = manufacturerDetail.getScAttachments();
        // 注册登记表附件 1305
        List<Attachment> djbAttachments = manufacturerDetail.getDjbAttachments();
        // 生产备案证 1308
        List<Attachment> rcAttachments = manufacturerDetail.getRcAttachments();

        // 1. 图片转pdf // 2. 调用远程电子签章
        if (!CollectionUtils.isEmpty(yzAttachments)) {
            BusinessInfo businessInfo = new BusinessInfo();
            Map<String, Object> useData = new HashMap<>(4);
            useData.put(ATTACHMENT_TYPE, CommonConstants.ATTACHMENT_TYPE_974);
            useData.put(CERTIFICATE_TYPE, CommonConstants.ATTACHMENT_FUNCTION_1302);
            useData.put(MANUFACTURER_ID, manufacturerId);
            useData.put(USER_ID, user.getUserId());
            businessInfo.setThirdData(JSON.toJSONString(useData));
            businessInfo.setOperator(user.getUsername());
            businessInfo.setOrderNo(manufacturerId.toString());
            ElectronicSignParam yzAttach = ElectronicSignParam.builder()
                    .flowType(2)
                    .vendorId(manufacturerId)
                    .electronicSignBusinessEnums(ElectronicSignBusinessEnums.VENDOR)
                    .vendorCertificateEnums(VendorCertificateEnums.BUSINESS_LICENSE)
                    .attachmentType("974")
                    .businessInfo(businessInfo).build();
            manufacturerElectronicSignHandle.electronicSign(yzAttach);

        }
        if (!CollectionUtils.isEmpty(scAttachments)) {
            BusinessInfo businessInfo = new BusinessInfo();
            Map<String, Object> useData = new HashMap<>(4);
            useData.put(ATTACHMENT_TYPE, CommonConstants.ATTACHMENT_TYPE_974);
            useData.put(CERTIFICATE_TYPE, CommonConstants.ATTACHMENT_FUNCTION_1303);
            useData.put(MANUFACTURER_ID, manufacturerId);
            useData.put(USER_ID, user.getUserId());
            businessInfo.setThirdData(JSON.toJSONString(useData));
            businessInfo.setOperator(user.getUsername());
            businessInfo.setOrderNo(manufacturerId.toString());
            ElectronicSignParam scAttach = ElectronicSignParam.builder()
                    .flowType(2)
                    .vendorId(manufacturerId)
                    .electronicSignBusinessEnums(ElectronicSignBusinessEnums.VENDOR)
                    .vendorCertificateEnums(VendorCertificateEnums.PRODUCTION_LICENSE)
                    .attachmentType(CommonConstants.ATTACHMENT_TYPE_974.toString())
                    .businessInfo(businessInfo).build();
            manufacturerElectronicSignHandle.electronicSign(scAttach);

        }
        if (!CollectionUtils.isEmpty(djbAttachments)) {
            BusinessInfo businessInfo = new BusinessInfo();
            Map<String, Object> useData = new HashMap<>(4);
            useData.put(ATTACHMENT_TYPE, CommonConstants.ATTACHMENT_TYPE_974);
            useData.put(CERTIFICATE_TYPE, CommonConstants.ATTACHMENT_FUNCTION_1304);
            useData.put(MANUFACTURER_ID, manufacturerId);
            useData.put(USER_ID, user.getUserId());
            businessInfo.setThirdData(JSON.toJSONString(useData));
            businessInfo.setOperator(user.getUsername());
            businessInfo.setOrderNo(manufacturerId.toString());
            ElectronicSignParam djbAttach = ElectronicSignParam.builder()
                    .flowType(2)
                    .vendorId(manufacturerId)
                    .electronicSignBusinessEnums(ElectronicSignBusinessEnums.VENDOR)
                    .vendorCertificateEnums(VendorCertificateEnums.REGISTRATION_FORM)
                    .attachmentType(CommonConstants.ATTACHMENT_TYPE_974.toString())
                    .businessInfo(businessInfo).build();
            manufacturerElectronicSignHandle.electronicSign(djbAttach);

        }
        if (!CollectionUtils.isEmpty(rcAttachments)) {
            BusinessInfo businessInfo = new BusinessInfo();
            Map<String, Object> useData = new HashMap<>(4);
            useData.put(ATTACHMENT_TYPE, CommonConstants.ATTACHMENT_TYPE_974);
            useData.put(CERTIFICATE_TYPE, CommonConstants.ATTACHMENT_FUNCTION_1309);
            useData.put(MANUFACTURER_ID, manufacturerId);
            useData.put(USER_ID, user.getUserId());
            businessInfo.setThirdData(JSON.toJSONString(useData));
            businessInfo.setOperator(user.getUsername());
            businessInfo.setOrderNo(manufacturerId.toString());
            ElectronicSignParam rcAttach = ElectronicSignParam.builder()
                    .flowType(2)
                    .vendorId(manufacturerId)
                    .electronicSignBusinessEnums(ElectronicSignBusinessEnums.VENDOR)
                    .vendorCertificateEnums(VendorCertificateEnums.PRODUCTION_RECORD_CERTIFICATE)
                    .attachmentType(CommonConstants.ATTACHMENT_TYPE_974.toString())
                    .businessInfo(businessInfo).build();
            manufacturerElectronicSignHandle.electronicSign(rcAttach);

        }

    }

    @Override
    public void certificatesStampRegistration(Integer firstEngageId, User user) {
        // 查询当前附件信息
        FirstEngage firstEngage = firstEngageMapper.selectByPrimaryKey(firstEngageId);
        if (null == firstEngage || firstEngage.getRegistrationNumberId() == null) {
            return;
        }
        Map<String, Object> paramMap = new HashMap<>(4);
        // 附件类型
        paramMap.put(ATTACHMENT_TYPE, CommonConstants.ATTACHMENT_TYPE_974);
        List<Integer> attachmentFunction = new ArrayList<>();
        // 注册证附件/备案凭证附件
        attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_975);
        //注册证附件/备案凭证附件（贝）
        paramMap.put(ATTACHMENT_FUNCTION, attachmentFunction);
        paramMap.put(REGISTRATION_NUMBER_ID, firstEngage.getRegistrationNumberId());
        List<Attachment> attachments = attachmentMapper.getAttachmentsList(paramMap);
        // 2. 调用远程电子签章
        if (!CollectionUtils.isEmpty(attachments)) {
            BusinessInfo businessInfo = new BusinessInfo();
            Map<String, Object> useData = new HashMap<>(4);
            useData.put(ATTACHMENT_TYPE, CommonConstants.ATTACHMENT_TYPE_974);
            useData.put(CERTIFICATE_TYPE, CommonConstants.ATTACHMENT_FUNCTION_1301);
            useData.put(FIRST_ENGAGE_ID, firstEngageId);
            useData.put(REGISTRATION_NUMBER_ID, firstEngage.getRegistrationNumberId());
            useData.put(USER_ID, user.getUserId());
            businessInfo.setThirdData(JSON.toJSONString(useData));
            businessInfo.setOperator(user.getUsername());
            businessInfo.setOrderNo(firstEngageId.toString());
            ElectronicSignParam fjAttach = ElectronicSignParam.builder()
                    .flowType(2)
                    .vendorId(firstEngageId)
                    .electronicSignBusinessEnums(ElectronicSignBusinessEnums.VENDOR)
                    .vendorCertificateEnums(VendorCertificateEnums.REGISTRATION_CERTIFICATE)
                    .attachmentType(CommonConstants.ATTACHMENT_TYPE_974.toString())
                    .businessInfo(businessInfo).build();
            manufacturerElectronicSignHandle.electronicSign(fjAttach);
        }

    }
}
