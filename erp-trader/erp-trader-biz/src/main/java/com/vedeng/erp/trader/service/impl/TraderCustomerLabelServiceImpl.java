package com.vedeng.erp.trader.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.common.annotation.MethodLock;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.business.common.constant.BusinessCluesConstant;
import com.vedeng.erp.trader.domain.dto.TraderCustomerTuokeLabel;
import com.vedeng.erp.trader.domain.vo.TraderCustomerTuokeLabelVo;
import com.vedeng.erp.trader.mapper.TraderCustomerTuokeLabelMapper;
import com.vedeng.erp.trader.service.TraderCustomerLabelService;
import com.vedeng.goods.dto.BaseCategoryDto;
import com.vedeng.goods.dto.DepartmentsHospitalDto;
import com.vedeng.goods.service.BaseCategoryApiService;
import com.vedeng.goods.service.DepartmentsHospitalApiService;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/3/10 17:13
 **/
@Service
@Slf4j
public class TraderCustomerLabelServiceImpl implements TraderCustomerLabelService {


    @Autowired
    private DepartmentsHospitalApiService departmentsHospitalApiService;

    @Autowired
    private BaseCategoryApiService baseCategoryApiService;

    @Resource
    private TraderCustomerTuokeLabelMapper traderCustomerTuokeLabelMapper;

    @Override
    public List<DepartmentsHospitalDto> findAllLabel() {
        return departmentsHospitalApiService.findAll();
    }

    @Override
    public List<BaseCategoryDto> findLevelCategory(@NonNull Integer isDelete, @NonNull Integer level) {
        return baseCategoryApiService.findLevelCategory(0,1);
    }

    @Override
    public void getTraderCustomerLabel(TraderCustomerTuokeLabelVo traderCustomerTuokeLabelVo) {

        log.info("TraderCustomerLabelServiceImpl getTraderCustomerLabel 入参{}", JSONObject.toJSONString(traderCustomerTuokeLabelVo));
        if (Objects.isNull(traderCustomerTuokeLabelVo.getTraderId())) {
            throw new ServiceException("客户id不可为空");
        }
        TraderCustomerTuokeLabel traderCustomerTuokeLabel = traderCustomerTuokeLabelMapper.selectByTraderId(traderCustomerTuokeLabelVo.getTraderId());
        log.info("TraderCustomerLabelServiceImpl getTraderCustomerLabel 客群db{}", JSONObject.toJSONString(traderCustomerTuokeLabel));

        List<DepartmentsHospitalDto> allLabel = this.findAllLabel();
        Map<Integer, DepartmentsHospitalDto> allLabelToMap = allLabel.stream().collect(Collectors.toMap(DepartmentsHospitalDto::getDepartmentId, c -> c, (key1, key2) -> key1));
        List<BaseCategoryDto> levelCategory = this.findLevelCategory(0, 1);
        Map<Integer, BaseCategoryDto> levelCategoryToMap = levelCategory.stream().collect(Collectors.toMap(BaseCategoryDto::getBaseCategoryId, c -> c, (key1, key2) -> key1));
        if (!Objects.isNull(traderCustomerTuokeLabel)) {
            BeanUtils.copyProperties(traderCustomerTuokeLabel, traderCustomerTuokeLabelVo);

            // 复合经营对象
            if (!StringUtils.isEmpty(traderCustomerTuokeLabel.getBusinessTarget())) {

                List<String> collect = Arrays.stream(traderCustomerTuokeLabel.getBusinessTarget().split(",")).collect(Collectors.toList());
                traderCustomerTuokeLabelVo.setBusinessTargets(collect);
            }

            // 复合医院等级
            if (!StringUtils.isEmpty(traderCustomerTuokeLabel.getHospitalLevel())) {

                List<String> collect = Arrays.stream(traderCustomerTuokeLabel.getHospitalLevel().split(",")).collect(Collectors.toList());
                traderCustomerTuokeLabelVo.setHospitalLevels(collect);
            }
            // 业务模式
            if (!StringUtils.isEmpty(traderCustomerTuokeLabel.getBusinessModel())) {

                List<String> collect = Arrays.stream(traderCustomerTuokeLabel.getBusinessModel().split(",")).collect(Collectors.toList());
                traderCustomerTuokeLabelVo.setBusinessModels(collect);
            }

            if (!StringUtils.isEmpty(traderCustomerTuokeLabel.getSalesModel())) {

                List<String> collect = Arrays.stream(traderCustomerTuokeLabel.getSalesModel().split(",")).collect(Collectors.toList());
                traderCustomerTuokeLabelVo.setSalesModels(collect);
            }

            // 复合经营科室
            if (!StringUtils.isEmpty(traderCustomerTuokeLabel.getBusinessDepartment())) {

                List<Integer> collect = new ArrayList<>();
                for (String s : traderCustomerTuokeLabel.getBusinessDepartment().split(",")) {
                    collect.add(Integer.valueOf(s.trim()));
                }

                collect.stream().forEach(c->{
                    DepartmentsHospitalDto departmentsHospital = allLabelToMap.get(c);
                    if (!Objects.isNull(departmentsHospital)) {
                        departmentsHospital.setChecked(true);
                    }

                });


            }

            // 复合经营产品
            if (!StringUtils.isEmpty(traderCustomerTuokeLabel.getBusinessGoods())) {

                List<Integer> collect = new ArrayList<>();
                for (String s : traderCustomerTuokeLabel.getBusinessGoods().split(",")) {
                    collect.add(Integer.valueOf(s.trim()));
                }

                collect.stream().forEach(c->{
                    BaseCategoryDto baseCategory = levelCategoryToMap.get(c);
                    if (!Objects.isNull(baseCategory)) {
                        baseCategory.setChecked(true);
                    }
                });


            }
        }

        traderCustomerTuokeLabelVo.setAllLabel(allLabel);
        traderCustomerTuokeLabelVo.setLevelCategory(levelCategory);
        log.info("TraderCustomerLabelServiceImpl getTraderCustomerLabel 返回值绑定值{}", JSONObject.toJSONString(traderCustomerTuokeLabelVo));
    }

    @Override
    @MethodLock(className = TraderCustomerTuokeLabelVo.class,field = "traderId")
    public void updateOrSaveTraderCustomerTuokeLabel(TraderCustomerTuokeLabelVo traderCustomerTuokeLabelVo) {

        log.info("TraderCustomerLabelServiceImpl updateOrSaveTraderCustomerTuokeLabel 入参{}", JSONObject.toJSONString(traderCustomerTuokeLabelVo));
        if (!Objects.isNull(traderCustomerTuokeLabelVo.getTraderId())) {
            TraderCustomerTuokeLabel traderCustomerTuokeLabel = traderCustomerTuokeLabelMapper.selectByTraderId(traderCustomerTuokeLabelVo.getTraderId());
            if (traderCustomerTuokeLabel != null) {
                traderCustomerTuokeLabelVo.setTraderCustomerTuokeLabelId(traderCustomerTuokeLabel.getTraderCustomerTuokeLabelId());
            }
        }
        // 新增
        if (Objects.isNull(traderCustomerTuokeLabelVo.getTraderCustomerTuokeLabelId())) {
            TraderCustomerTuokeLabel traderCustomerTuokeLabel = this.bindTraderCustomerTuokeLabel(traderCustomerTuokeLabelVo);

            traderCustomerTuokeLabelMapper.insertSelective(traderCustomerTuokeLabel);
        } else {
            // 更新
            TraderCustomerTuokeLabel traderCustomerTuokeLabel = this.bindTraderCustomerTuokeLabel(traderCustomerTuokeLabelVo);
            traderCustomerTuokeLabelMapper.updateByPrimaryKeySelective(traderCustomerTuokeLabel);
        }

    }

    /**
     * vo->entity 数据转换，封装
     * @param traderCustomerTuokeLabelVo 页面表单的数据
     * @return TraderCustomerTuokeLabel 数据库客群
     */
    private TraderCustomerTuokeLabel bindTraderCustomerTuokeLabel(TraderCustomerTuokeLabelVo traderCustomerTuokeLabelVo) {

        log.info("TraderCustomerLabelServiceImpl bindTraderCustomerTuokeLabel 入参{}", JSONObject.toJSONString(traderCustomerTuokeLabelVo));

        TraderCustomerTuokeLabel traderCustomerTuokeLabel = new TraderCustomerTuokeLabel();
        BeanUtils.copyProperties(traderCustomerTuokeLabelVo, traderCustomerTuokeLabel);

        log.debug("TraderCustomerLabelServiceImpl bindTraderCustomerTuokeLabel 中间值{}", JSONObject.toJSONString(traderCustomerTuokeLabel));

        // 经营对象
        if (!Objects.isNull(traderCustomerTuokeLabelVo.getBusinessTarget()) && traderCustomerTuokeLabelVo.getBusinessTarget().length > 0) {

            String join = String.join(",", traderCustomerTuokeLabelVo.getBusinessTarget());
            log.debug("BusinessTarget:{}",join);
            traderCustomerTuokeLabel.setBusinessTarget(join);
        }else {
            traderCustomerTuokeLabel.setBusinessTarget("");
        }

        // 医院等级
        if (!Objects.isNull(traderCustomerTuokeLabelVo.getHospitalLevel()) && traderCustomerTuokeLabelVo.getHospitalLevel().length > 0) {

            String join = String.join(",", traderCustomerTuokeLabelVo.getHospitalLevel());
            log.debug("HospitalLevel:{}",join);
            traderCustomerTuokeLabel.setHospitalLevel(join);
        }else {
            traderCustomerTuokeLabel.setHospitalLevel("");
        }

        // 销售模式
        if (!Objects.isNull(traderCustomerTuokeLabelVo.getBusinessModel()) && traderCustomerTuokeLabelVo.getBusinessModel().length > 0) {

            String join = String.join(",", traderCustomerTuokeLabelVo.getBusinessModel());
            log.debug("BusinessModel:{}",join);
            traderCustomerTuokeLabel.setBusinessModel(join);
        }else {
            traderCustomerTuokeLabel.setBusinessModel("");
        }

        // 经营科室
        if (!Objects.isNull(traderCustomerTuokeLabelVo.getBusinessDepartment()) && traderCustomerTuokeLabelVo.getBusinessDepartment().length > 0) {

            String join = String.join(",", traderCustomerTuokeLabelVo.getBusinessDepartment());
            log.debug("BusinessDepartment:{}",join);
            traderCustomerTuokeLabel.setBusinessDepartment(join);
        }else {
            traderCustomerTuokeLabel.setBusinessDepartment("");
        }

        // 经营产品
        if (!Objects.isNull(traderCustomerTuokeLabelVo.getBusinessGoods()) && traderCustomerTuokeLabelVo.getBusinessGoods().length > 0) {

            String join = String.join(",", traderCustomerTuokeLabelVo.getBusinessGoods());
            log.debug("BusinessGoods:{}",join);
            traderCustomerTuokeLabel.setBusinessGoods(join);
        }else {
            traderCustomerTuokeLabel.setBusinessGoods("");
        }

        // 销售模式
        if (!Objects.isNull(traderCustomerTuokeLabelVo.getSalesModel()) && traderCustomerTuokeLabelVo.getSalesModel().length > 0) {

            String join = String.join(",", traderCustomerTuokeLabelVo.getSalesModel());
            log.debug("SalesModel:{}",join);
            traderCustomerTuokeLabel.setSalesModel(join);
        }else {
            traderCustomerTuokeLabel.setSalesModel("");
        }

        log.info("TraderCustomerLabelServiceImpl bindTraderCustomerTuokeLabel bind值：{}", JSON.toJSONString(traderCustomerTuokeLabel));
        return traderCustomerTuokeLabel;
    }

    @Override
    public TraderCustomerTuokeLabelVo getTuokeLabelInfo(Integer traderId, Integer businessCluesId) {
        TraderCustomerTuokeLabelVo labelInfo = traderCustomerTuokeLabelMapper.getTuokeLabelInfo(traderId, businessCluesId);
        // 处理客群标签字符串
        labelInfo.setBusinessTargetStr(handleLabelStr(labelInfo.getBusinessTargetStr(), BusinessCluesConstant.BUSINESS_TARGET));
        labelInfo.setHospitalLevelStr(handleLabelStr(labelInfo.getHospitalLevelStr(), BusinessCluesConstant.HOSPITAL_LEVEL));
        labelInfo.setBusinessDepartmentStr(handleLabelStr(labelInfo.getBusinessDepartmentStr(), BusinessCluesConstant.BUSINESS_DEPARTMENT));
        labelInfo.setBusinessGoodsStr(handleLabelStr(labelInfo.getBusinessGoodsStr(), BusinessCluesConstant.BUSINESS_GOODS));
        labelInfo.setBusinessModelStr(handleLabelStr(labelInfo.getBusinessModelStr(), BusinessCluesConstant.BUSINESS_MODEL));
        labelInfo.setSalesModelStr(handleLabelStr(labelInfo.getSalesModelStr(), BusinessCluesConstant.SALES_MODEL));
        return labelInfo;
    }

    private String handleLabelStr(String source, String type) {

        if (!StringUtils.isEmpty(source)) {
            String[] split = source.split(",");
            StringBuilder labelStr = new StringBuilder();
            for (String s : split) {
                switch (type) {
                    case BusinessCluesConstant.BUSINESS_TARGET:
                        if (s.equals(BusinessCluesConstant.ONE)) {
                            labelStr.append(BusinessCluesConstant.PUBLIC).append(" ");
                        } else if (s.equals(BusinessCluesConstant.TWO)) {
                            labelStr.append(BusinessCluesConstant.PRIVATELY).append(" ");
                        }
                        break;
                    case BusinessCluesConstant.HOSPITAL_LEVEL:
                        switch (s) {
                            case BusinessCluesConstant.ONE:
                                labelStr.append(BusinessCluesConstant.ABOVE_MUNICIPAL_LEVEL).append(" ");
                                break;
                            case BusinessCluesConstant.TWO:
                                labelStr.append(BusinessCluesConstant.DISTRICT_AND_COUNTY_LEVEL).append(" ");
                                break;
                            case BusinessCluesConstant.THREE:
                                labelStr.append(BusinessCluesConstant.PRIMARY_MEDICAL_TREATMENT).append(" ");
                                break;
                            case BusinessCluesConstant.FOUR:
                                labelStr.append(BusinessCluesConstant.PUBLIC_HEALTH).append(" ");
                                break;
                        }
                        break;
                    case BusinessCluesConstant.BUSINESS_DEPARTMENT:
                        labelStr.append(departmentsHospitalApiService.selectByPrimaryKey(Integer.valueOf(s)).getDepartmentName()).append(" ");
                        break;
                    case BusinessCluesConstant.BUSINESS_GOODS:
                        labelStr.append(baseCategoryApiService.selectByPrimaryKey(Integer.valueOf(s)).getBaseCategoryName()).append(" ");
                        break;
                    case BusinessCluesConstant.BUSINESS_MODEL:
                        if (s.equals(BusinessCluesConstant.ONE)) {
                            labelStr.append(BusinessCluesConstant.DIRECT_SELLING).append(" ");
                        } else if (s.equals(BusinessCluesConstant.TWO)) {
                            labelStr.append(BusinessCluesConstant.DISTRIBUTION).append(" ");
                        }
                        break;
                    case BusinessCluesConstant.SALES_MODEL:
                        if (s.equals(BusinessCluesConstant.ONE)) {
                            labelStr.append(BusinessCluesConstant.DEALERSHIP).append(" ");
                        } else if (s.equals(BusinessCluesConstant.TWO)) {
                            labelStr.append(BusinessCluesConstant.RELATIONSHIP).append(" ");
                        }
                        break;

                }
            }
            return labelStr.toString();
        }


        return null;
    }

}
