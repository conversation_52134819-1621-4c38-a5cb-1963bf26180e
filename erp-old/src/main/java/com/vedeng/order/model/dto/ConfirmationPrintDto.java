package com.vedeng.order.model.dto;


import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.logistics.model.vo.BatchExpressVo;
import com.vedeng.order.model.Saleorder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 确认单打印用dto
 */
@Data
public class ConfirmationPrintDto {

    private Integer orderPrintOutType;

    private Integer priceFlag;

    private Saleorder saleorder;

    private String bussinessNo;

    private String message;

    private String year;

    private String month;

    private String day;

    private BigDecimal totalPrice;

    private String chineseTotal;

    private BigDecimal expressPrice;


    private BigDecimal realTotalPrice;

    private BigDecimal couponsPrice;

    private Boolean hcPrintOutflag;

    private Boolean jcPrintOutExpress;

    private Long currTime;

    private String timeStr;

    private Boolean isExpressPrint;

    private String outName;

    private String userName;

    private List<WarehouseGoodsOperateLog> woList;

    private List<BatchExpressVo> batchExpressVos;

    private List<WarehouseGoodsOperateLog> firstInfo;

    private String type;

    private Integer bussinessType = 496;

    private Integer titleType;

    private Integer printFlag;

    private String wmsflag;

    private String printerButtonFlag;
}
