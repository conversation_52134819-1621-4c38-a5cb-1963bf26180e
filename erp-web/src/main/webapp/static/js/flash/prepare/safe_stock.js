$(function () {
    let deployType = Number($('#deployType').val());
    if (deployType == 1) {
        $('#safe_type_sys').show();
        $('#safe_type_input').hide();
    } else {
        $('#safe_type_sys').hide();
        $('#safe_type_input').show();
    }
});

function changeDeployType() {
    let deployType = Number($('#deployType').val());
    if (deployType == 1) {
        $('#safe_type_sys').hide();
        $('#safe_type_input').show();
        $('#deployType').val(0);
    } else {
        $('#safe_type_input').hide();
        $('#safe_type_sys').show();
        $('#deployType').val(1);
    }
}

function setSafeStockVal(obj, num) {
    let safeStock = Number($(obj).val());
    $('#safeStockSys').val(safeStock*Number(num));
}

function setSafeStock() {
    checkLogin();
    if (!checkForm()){
        return false;
    }
    let formVal = $('#set_safe_stock').serialize();
      console.log(formVal)
    $.ajax({
        url: page_url + '/flash/prepare/setSafeStock.do',
        type: 'POST',
        dataType :'json',
        async : false,
        data:$('#set_safe_stock').serialize(),
        success: function (data) {
            if(data.code == 0){
                layerPFF(window.parent.location.href);
                $("#close-layer").click();
            }else{
                layer.alert(data.message)
            }
        },error:function (data) {
            if(data.code==-1){
                layer.alert("设置安全库存失败！！！")
            }else if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }

        }
    })
}

function checkForm() {
    $('.safe_stock_error').hide();
    $('.operate_type_error').hide();
    $('.operate_reason_error').hide();
    let safeStock = $('#safeStock').val();
    if (!safeStock) {
        $('.safe_stock_error').show();
        return false;
    }
    let operateType = $('#operateType').val();
    if ('-1' === operateType) {
        $('.operate_type_error').show();
        return false;
    }

    // 调整原因为其他，输入调整原因必填
    if ('4' === operateType) {
        let operateReason = $('#operateReason').val();
        if (!operateReason) {
            $('.operate_reason_error').show();
            return false;
        }
    }
    return true;
}


