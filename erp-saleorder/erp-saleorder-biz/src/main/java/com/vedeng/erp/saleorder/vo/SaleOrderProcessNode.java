package com.vedeng.erp.saleorder.vo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: daniel
 * @Date: 2021/11/23 17 31
 * @Description: 销售单进度节点类
 */
@Data
public class SaleOrderProcessNode {

    /**
     * 标签文字
     */
    private String label;

    /**
     * 文字下面的备注
     */
    private String tip;

    /**
     * 是否需要上面的锁和文字
     */
    private String lock;

    /**
     * 当前的状态，默认是0  0：未进行 1：已进行 2：正在进行 3：正在同步进行
     */
    private Integer status;

    /**
     * 流程主节点
     */
    private Boolean mainProcessNode;

    public SaleOrderProcessNode() {
        status = 0;
    }
}
