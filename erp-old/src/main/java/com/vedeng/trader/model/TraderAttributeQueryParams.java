package com.vedeng.trader.model;

import java.util.List;

public class TraderAttributeQueryParams {
    private List<String> departments;
    private List<String> products;
    private List<String> productTypes;
    private List<String> customerTypes;
    private Integer traderContract;
    private String area;
    private List<String> brands;
    private Integer belongPlatform=1;
    private List<Integer> organizations;
    /**
     *  客户评级
     */
    private List<Integer> customerGradeIds;

    /**
     *  客户旅程
     */
    private List<Integer> customerTripIds;


    public List<Integer> getCustomerGradeIds() {
        return customerGradeIds;
    }

    public void setCustomerGradeIds(List<Integer> customerGradeIds) {
        this.customerGradeIds = customerGradeIds;
    }

    public List<Integer> getCustomerTripIds() {
        return customerTripIds;
    }

    public void setCustomerTripIds(List<Integer> customerTripIds) {
        this.customerTripIds = customerTripIds;
    }

    public Integer getBelongPlatform() {
        return belongPlatform;
    }

    public void setBelongPlatform(Integer belongPlatform) {
        this.belongPlatform = belongPlatform;
    }

    public List<Integer> getOrganizations() {
        return organizations;
    }

    public void setOrganizations(List<Integer> organizationIds) {
        this.organizations = organizationIds;
    }

    public List<String> getDepartments() {
        return departments;
    }

    public void setDepartments(List<String> departments) {
        this.departments = departments;
    }

    public List<String> getProducts() {
        return products;
    }

    public void setProducts(List<String> products) {
        this.products = products;
    }

    public List<String> getProductTypes() {
        return productTypes;
    }

    public void setProductTypes(List<String> productTypes) {
        this.productTypes = productTypes;
    }

    public List<String> getCustomerTypes() {
        return customerTypes;
    }

    public void setCustomerTypes(List<String> customerTypes) {
        this.customerTypes = customerTypes;
    }

    public Integer getTraderContract() {
        return traderContract;
    }

    public void setTraderContract(Integer traderContract) {
        this.traderContract = traderContract;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public List<String> getBrands() {
        return brands;
    }

    public void setBrands(List<String> brands) {
        this.brands = brands;
    }
}
