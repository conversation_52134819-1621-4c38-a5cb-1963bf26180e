package com.vedeng.erp.saleorder.service;

import com.github.pagehelper.PageInfo;
import com.vedeng.authorization.model.User;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.erp.saleorder.dto.SaleOrderTerminalCustomerDto;
import com.vedeng.erp.saleorder.dto.SaleOrderTerminalDto;

import java.util.List;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.saleorder.service
 * @Date 2023/9/5 18:43
 */
public interface OrderTerminalService {

    /**
     * 分页查询大数据终端库终端信息
     *
     * @param terminalName 终端名称
     * @param pageSize     页大小
     * @param pageNum      当前页
     * @return PageResult<OrderTerminalDto>
     */
    PageInfo getOneDataTerminalInfo(String terminalName, Integer pageSize, Integer pageNum);

    /**
     * 分页查询天眼查终端信息
     *
     * @param terminalName 终端名称
     * @param pageSize     页大小
     * @param pageNum      当前页
     * @return PageResult<OrderTerminalDto>
     */
    PageInfo getTycTerminalInfo(String terminalName, Integer pageSize, Integer pageNum);

    /**
     * 保存销售单生效后完善终端信息
     *
     * @param saleOrderTerminalDto 终端信息
     */
    void saveSaleOrderTerminal(SaleOrderTerminalDto saleOrderTerminalDto);

    SaleOrderTerminalDto findSaleOrderTerminalById(Integer id);

    List<SaleOrderTerminalDto> findSaleOrderTerminalBySaleOrderId(Integer saleOrderId);

    /**
     * 删除某个销售订单对应的终端
     * @param id
     * @return
     */
    boolean deleteTerminalForSaleOrderId(Integer id, CurrentUser user );

    int saveSaleOrderTerminalForNew(SaleOrderTerminalDto saleOrderTerminalDto);

    /**
     * 根据销售单id查询终端信息
     *
     * @param saleOrderId 销售单id
     * @return SaleOrderTerminalDto
     */
    SaleOrderTerminalDto getBySaleOrderId(Integer saleOrderId);

    /**
     * 根据销售单id查询终端备注客户信息
     *
     * @param saleOrderId 销售单id
     * @return SaleOrderTerminalCustomerDto
     */
    SaleOrderTerminalCustomerDto getSaleOrderTerminalCustomerByOrderId(Integer saleOrderId);

    /**
     * 保存销售单终端客户信息
     *
     * @param terminalCustomerDto SaleOrderTerminalCustomerDto
     */
    void saveSaleOrderTerminalCustomer(SaleOrderTerminalCustomerDto terminalCustomerDto);

    /**
     * 根据销售单id查询终端性质
     * @param saleOrderId
     * @return
     */
    Integer getNatureBySaleOrderId(Integer saleOrderId);
}
