package com.vedeng.common.trace.util;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Objects;
import java.util.Map.Entry;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;

/**
 * 埋点相关工具类
 * @ClassName:  TrackUtils     
 * @author: <PERSON>.yang
 * @date:   2024年6月4日 下午1:55:34    
 * @Copyright:
 */
public final class TrackUtils {
	
	private TrackUtils() {}
	
	private static final Logger LOGGER = LoggerFactory.getLogger(TrackUtils.class); 
	
	/**
     * Object转成指定的类型
     * @param obj
     * @param type
     * @param <T>
     * @return
     */
    @SuppressWarnings("unchecked")
	public static<T> T convert(Object obj, Class<T> type) {
        if (obj != null && StringUtils.isNotBlank(obj.toString())) {
            if (type.equals(Integer.class)||type.equals(int.class)) {
                return (T)Integer.valueOf(StringUtils.trim(obj.toString()));
            } else if (type.equals(Long.class)||type.equals(long.class)) {
                return (T)Long.valueOf(StringUtils.trim(obj.toString()));
            } else if (type.equals(Boolean.class)||type.equals(boolean.class)) {
                return (T)Boolean.valueOf(StringUtils.trim(obj.toString()));
            } else if (type.equals(Short.class)||type.equals(short.class)) {
                return (T)Short.valueOf(StringUtils.trim(obj.toString()));
            } else if (type.equals(Float.class)||type.equals(float.class)) {
                return (T)Float.valueOf(StringUtils.trim(obj.toString()));
            } else if (type.equals(Double.class)||type.equals(double.class)) {
                return (T)Double.valueOf(StringUtils.trim(obj.toString()));
            } else if (type.equals(Byte.class)||type.equals(byte.class)) {
                return (T)Byte.valueOf(StringUtils.trim(obj.toString()));
            } else if (type.equals(Character.class)||type.equals(char.class)) {
                return (T)Character.valueOf(obj.toString().charAt(0));
            } else if (type.equals(String.class)) {
                return (T) String.valueOf(obj);
            } else if (type.equals(BigDecimal.class)) {
                return (T) new BigDecimal(StringUtils.trim(obj.toString()));
            } else if (type.equals(LocalDateTime.class)) {
                return (T) LocalDateTime.parse(obj.toString());
            } else if (type.equals(Date.class)) {
                try {
                    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    return (T) formatter.parse(obj.toString());
                } catch (ParseException e) {
                	LOGGER.error("类型转换出错！",e);
                }
                return null;
            }else{
                return null;
            }
        } else {
            return null;

        }
    }
    
    //递归方法，用于获取指定key的value
    public static String getJsonValue(JSONObject jsonObject, String key) throws JSONException {
        //检查当前JSON对象是否包含指定key
        if(Objects.isNull(jsonObject)) {
        	return null;
        }
    	if (jsonObject.containsKey(key) && Objects.nonNull(jsonObject.get(key))) {
        	// 如果包含，返回对应的value,全部转成String类型
        	return TrackUtils.convert(jsonObject.get(key),String.class);
        }
        //遍历当前JSON对象的所有key
        for (Entry<String, Object> entry : jsonObject.entrySet()) {
        	JSONObject value = null;
        	try {
        		value = JSON.parseObject(JSON.toJSONString(jsonObject.get(entry.getKey())));
        	}catch(Exception e) {
        		//转化异常，说明已经无数据
        	}
            if(Objects.isNull(value)) {
            	continue;
            }
            //如果当前key对应的value是一个JSONObject，则递归搜索
            String result = getJsonValue(value, key);
            if (result != null) {
                return result;
            }
        }
        return null;
    }

}
