package com.vedeng.finance.enums;

/**
 * <AUTHOR>
 * @description:
 * @date 2021/7/12 10:30
 */
public enum HxInvoiceEnum {


    /**
     * 开户行
     */
    BANK(2,"建设银行南京市中山南路支行"),

    /**
     * 账户
     */
    ACCOUNT(3,"32001881236052503686"),

    /**
     * 注册地址
     */
    ADDRESS(4,"南京市秦淮区永顺路2号2幢三楼301室"),

    /**
     * 注册电话
     */
    PHONE(5,"025-********");

    private Integer type;

    private String content;

    HxInvoiceEnum(Integer type, String content) {
        this.type = type;
        this.content = content;
    }

    public static String getName(int type) {
        for (HxInvoiceEnum hxInvoiceEnum : HxInvoiceEnum.values()) {
            if (hxInvoiceEnum.getType() == type) {
                return hxInvoiceEnum.content;
            }
        }
        return null;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
