Vue.component('ui-input', {
    template: `<div :class="[
                type === 'textarea' ? 'vd-ui-textarea' : 'vd-ui-input',
                size ? 'vd-ui-input--' + size : '',
                {
                    'is-focus': focused,
                    'is-disabled': disabled,
                    'vd-ui-input-group': $slots.prepend || $slots.append || prepend || append,
                    'vd-ui-input-group--prepend': $slots.prepend || prepend,
                    'vd-ui-input-group--append': $slots.append || append,
                    'vd-ui-input--prefix': $slots.prefix || prefixIcon,
                    'vd-ui-input--suffix': $slots.suffix || suffixIcon || clearable || showPwdVisible,
                    'vd-ui-input--error': errorable && !disabled
                }
            ]"
            :style="style"
            @mouseenter="handleEnter"
            @mouseleave="handleLeave"
        >
            <template v-if="type !== 'textarea'">
                <!-- 前置元素 -->
                <div class="vd-ui-input-group__prepend" v-if="$slots.prepend || prepend">
                    <slot name="prepend">
                        <span>{{prepend}}</span>
                    </slot>
                </div>
                
                <input
                    class="vd-ui-input__inner"
                    :type="trueType"
                    v-bind="$attrs"
                    :disabled="disabled"
                    :readonly="readonly"
                    ref="input"
                    @input="handleInput"
                    @focus="handleFocus"
                    @blur="handleBlur"
                    @change="handleChange"
                    @compositionend="commentPressEnd"
                    @compositionstart="commentPressStart"
                    @keyup="handleKeyup"
                >

                <!-- 前置内容 -->
                <span class="vd-ui-input__prefix" v-if="$slots.prefix || prefixIcon">
                    <slot name="prefix">
                        <i class="vd-ui-input__icon vd-ui_icon"
                            v-if="prefixIcon"
                            :class="prefixIcon">
                        </i>
                    </slot>               
                </span>
                <!-- 后置内容 -->
                <span class="vd-ui-input__suffix"
                    v-if="getSuffixVisible"
                >
                <span class="vd-ui-input__suffix-inner">
                    <i v-if="showClear"
                        class="vd-ui-input__icon vd-ui_icon icon-error2"
                        @mousedown.prevent
                        @click.prevent.stop="clear"
                    ></i>
                    <template v-if="showPwdVisible">
                        <i class="vd-ui-input__icon vd-ui_icon icon-hide"
                            v-if="!passwordVisible"
                            @click="handlePasswordVisible"
                            @mousedown.prevent
                        ></i>
                        <i class="vd-ui-input__icon vd-ui_icon icon-display"
                            v-else
                            @click="handlePasswordVisible"
                            @mousedown.prevent
                        ></i>
                    </template>
                </span>
                <span class="vd-ui-input__suffix-slot" v-if="$slots.suffix || suffixIcon">
                    <template>
                        <slot name="suffix">
                            <i class="vd-ui-input__icon vd-ui_icon"
                                v-if="suffixIcon"
                                :class="suffixIcon">
                            </i>
                        </slot>                       
                    </template>
                </span>    
            </span>
            <!-- 后置元素 -->
            <div class="vd-ui-input-group__append" v-if="$slots.append || append">
                <slot name="append"> 
                    <span>{{append}}</span>
                </slot>
            </div>
            <!-- 后面自定义内容 -->
            <div class="icon-type" v-if="$slots.hintIcon && !disabled">
                <slot name="hintIcon">
                </slot>
            </div>
            <!-- 下面文案说明 -->
            <template v-if="text">
            <p class="vd-ui-input-text">{{text}}</p>
            </template>
            <!-- 报错提示 -->
            <div class="vd-ui-input-error" v-if="errorable && (!disabled || (disabled && inDisabledValid))">
                <i class="vd-ui_icon icon-error2 vd-ui-input-error--icon"></i>
                <p class="vd-ui-input-error--errmsg">{{errorMsg}}</p>
            </div>
        </template>
        <template v-else>
            <div class="vd-ui-textarea-place">
                <textarea
                    ref="textarea"
                    class="vd-ui-textarea__inner"
                    v-bind="$attrs"
                    :disabled="disabled"
                    :readonly="readonly"
                    :style="textareaStyle"
                    @input="handleInput"
                    @compositionend="commentPress"

                    @focus="handleFocus"
                    @blur.trim="handleBlur"
                    @change="handleChange"
                ></textarea>
                <span
                    v-if="isWordLimitVisible && type === 'textarea'"
                    class="vd-ui-input__count"
                    :class="{
                        'upper-limit': textLength == upperLimit
                    }"
                >
                    {{ textLength }}/{{ upperLimit }}
                </span>
            </div>
            <div class="vd-ui-input-error" v-if="errorable && (!disabled || (disabled && inDisabledValid)) && errorMsg">
                <i class="vd-ui_icon icon-error2 vd-ui-input-error--icon"></i>
                <p class="vd-ui-input-error--errmsg">{{errorMsg}}</p>
            </div>
        </template>
    </div>`,
    data() {
        return {
            focused: false,
            entered: false,
            // 密码可见
            passwordVisible: false,
            validKey: '',
            validValue: '',
            onCompressing: false
        }
    },
    props: {
        value: [String, Number],
        // 输入框尺寸 large small
        size: String,
        type: {
            type: String,
            default: 'text'
        },
        // none, both, horizontal, vertical
        // resize: String,
        disabled: {
            type: Boolean,
            default: false,
        },
        readonly: {
            type: Boolean,
            default: false,
        },
        prepend: {
            type: String,
            default: ''
        },
        append: {
            type: String,
            default: ''
        },
        // 前置图标
        prefixIcon: String,
        // 后置图标
        suffixIcon: String,
        clearable: {
            type: Boolean,
            default: false
        },
        showWordLimit: {
            type: Boolean,
            default: false
        },
        errorMsg: {
            type: String,
            default: ''
        },
        errorable: {
            type: Boolean,
            default: false
        },
        text: String,
        selectAll: {
            type: Boolean,
            default: false
        },
        showPassword: {
            type: Boolean,
            default: false
        },
        heightAuto: {
            type: Boolean,
            default: false
        },
        selectClear: {//select组件清除按钮判断
            type: Boolean,
            default: false
        },
        inDisabledValid: {//disabled仍须交验
            type: Boolean,
            default: false
        }
    },

    computed: {
        style() {
            let style = {}
            if (this.type == 'textarea') {
                style.width = '';
            } else {
                style.width = this.$attrs.width ? this.$attrs.width : '100%'
            }
            return style

        },
        parent() {
            return this.$parent
        },
        textareaStyle() {
            let style = {}
            style.width = this.$attrs.width
            // style.height = this.$attrs.height
            !this.disabled ? style.resize = this.$attrs.resize : style.resize = 'none'
            style['min-height'] = this.$attrs['min-height']
            style['max-height'] = this.$attrs['max-height']
            style['min-width'] = this.$attrs['min-width']
            style['max-width'] = this.$attrs['max-width']
            this.errorable ? style['border-color'] = '#e64545' : ''
            return style

        },
        nativeInputValue() {
            return this.value === null || this.value === undefined ? '' : String(this.value);
        },
        showClear() {
            return this.clearable &&
                !this.disabled &&
                (this.selectClear || this.nativeInputValue) &&
                (this.entered || this.focused)
        },
        showPwdVisible() {
            return !this.disabled &&
                !this.readonly &&
                this.type == 'password' &&
                this.showPassword &&
                (this.entered || this.focused || this.nativeInputValue)
        },
        isWordLimitVisible() {
            return this.showWordLimit &&
                this.$attrs.maxlength &&
                (this.type === 'text' || this.type === 'textarea') &&
                !this.disabled &&
                !this.readonly &&
                !this.showPassword
        },
        upperLimit() {
            return this.$attrs.maxlength;
        },
        textLength() {
            if (typeof this.value === 'number') {
                return String(this.value).length;
            }

            return (this.value || '').length;
        },
        getSuffixVisible() {
            return this.$slots.suffix ||
                this.suffixIcon ||
                this.showClear ||
                this.type == 'password'
        },
        trueType() {
            if (this.passwordVisible || this.type === 'number') {
                return 'text';
            } else {
                return this.type;
            }
        }
    },
    watch: {
        nativeInputValue() {
            this.setNativeInputValue();
        },
    },
    mounted() {
        this.setNativeInputValue()
        if (this.type == 'textarea') {
            if (this.value && this.heightAuto) {
                this.getInput().style.height = this.$refs.textarea.scrollHeight + 2 + 'px' || this.$attrs.height || '';
            } else {
                this.getInput().style.height = this.$attrs.height || '';
            }
        }
        this.$form.setValidEl(this);
    },
    methods: {
        focus() {
            this.getInput().focus()
        },
        blur() {
            this.focused = false
            this.getInput().blur()
        },
        select() {
            this.getInput().select()
        },
        triggerFocus() {
            this.focus();
            this.handleFocus();
        },
        handleInput(event) {
            if(this.onCompressing) {
                return;
            }

            // textarea 中文输入完才计算高度
            if (this.type == 'textarea' && event.inputType == 'insertCompositionText') {
                return;
            }

            if(this.type == 'textarea' && this.heightAuto == true && parseFloat(this.getInput().style.height.replace('px', '')) < this.$refs.textarea.scrollHeight - 5) {
                this.getInput().style.height = this.$refs.textarea.scrollHeight + 2 + 'px';
            }

            if (this.type == 'number') {
                console.log(event.target.value)
                event.target.value = event.target.value.replace(/[^\d]/g, '');
            }

            if (event.target.value === this.nativeInputValue) return;
            this.$emit('input', event.target.value)
            this.$nextTick(this.setNativeInputValue);
        },
        commentPressStart() {
            this.onCompressing = true;
        },
        commentPressEnd(e) {
            this.onCompressing = false;
            this.handleInput(e)
        },
        // 中文结束
        commentPress(event) {
            if(this.type == 'textarea' && this.heightAuto == true && parseFloat(this.getInput().style.height.replace('px', '')) < this.$refs.textarea.scrollHeight - 5) {
                this.getInput().style.height = this.$refs.textarea.scrollHeight + 2 + 'px';
            }

            if (this.type == 'number') {
                event.target.value = event.target.value.replace(/[^\d]/g, '');
            }

            if (event.target.value === this.nativeInputValue) return;
            this.$emit('input', event.target.value)
            this.$nextTick(this.setNativeInputValue);
        },

        handleFocus(event) {
            this.focused = true
            this.selectAll ? this.getInput().select() : ''
            this.$emit('focus', event)
        },
        handleBlur(event) {
            this.cancelFocus()
            let prevValue = event.target.value;
            let trimValue = event.target.value.trim();

            if(prevValue !== trimValue) {
                event.target.value = trimValue;
                this.$emit('input', event.target.value);
            }
            
            this.$emit('blur', event);
            setTimeout(() => {
                this.checkValid();
            })
        },
        cancelFocus() {
            this.focused = false
        },
        handleChange(event) {
            this.$emit('change', event)
        },
        handleEnter() {
            this.entered = true
        },
        handleLeave() {
            this.entered = false
        },
        handlePasswordVisible() {
            this.focus();
            setTimeout(() => {
                this.passwordVisible = !this.passwordVisible
            })
        },
        // 选中赋值 阻止触发blur事件
        preventFunc(e) {
            e.preventDefault()
        },
        clear() {
            if (!this.selectClear) {
                this.focus()
            }
            this.$emit('input', '');
            this.$emit('change', '');
            this.$emit('clear');
        },
        setNativeInputValue() {
            const input = this.getInput();
            if (!input) return;
            if (input.value === this.nativeInputValue) return;
            // console.log(this.nativeInputValue)
            input.value = this.nativeInputValue;
        },
        getInput() {
            return this.$refs.input || this.$refs.textarea
        },
        checkValid() {
            if (this.validKey && this.validValue) {
                if (this.$form.validList[this.validKey].indexOf(this.validValue) !== -1) {
                    let validData = this.$form.checkValid(this.validValue, this.value, this.validKey);

                    this.triggerError(validData);
                }
            }
        },
        triggerError(validData) {
            if (validData.result) {
                this.errorMsg = '';
                this.errorable = false;
            } else {
                this.errorMsg = validData.message;
                this.errorable = true;
            }
        },
        handleKeyup(e) {
            if(e.keyCode === 13) {
                this.$emit('enter')
            } 
        }
    },
})

Vue.component('ui-range-input', {
    template: `<div class="vd-ui-number-range">
        <ui-input v-model="min" :placeholder="placeholderMin || placeholder" @blur="checkRangeData('min')"></ui-input>
        <span class="range-gap">-</span>
        <ui-input v-model="max" :placeholder="placeholderMax || placeholder"  @blur="checkRangeData('max')"></ui-input>
    </div>`,
    props: {
        max: {
            type: String || Number,
            default: ''
        },
        min: {
            type: String || Number,
            default: ''
        },
        placeholderMin: {
            type: String,
            default: ''
        },
        placeholderMax: {
            type: String,
            default: ''
        },
        placeholder: {
            type: String,
            default: ''
        }
    },
    methods: {
        checkRangeData(type) {
            if (type === 'min') {
                if(!this.checkValue(this.min.trim())) {
                    this.min = "";
                } else if(this.max.trim() && parseFloat(this.min) > parseFloat(this.max)) {
                    this.min = this.max;
                }
                
                this.changeValueMin();
            }

            if (type === 'max') {
                if(!this.checkValue(this.max.trim())) {
                    this.max = "";
                } else if(this.min.trim() && parseFloat(this.min) > parseFloat(this.max)) {
                    this.max = this.min;
                }
                
                this.changeValueMax();
            }
        },
        checkValue(value) {
            if(!/^\d+(\.\d+)?$/.test(value)) {
                return false;
            }

            return true;
        },
        changeValueMin() {
            this.$emit('update:min', this.min.trim());
        },
        changeValueMax() {
            this.$emit('update:max', this.max.trim());
        }
    }
})

Vue.component('ui-number-input', {
    template: `<div class="vd-ui-number-input">
        <ui-button class="left" :disabled="minDisabled" @click="minus">-</ui-button>
        <ui-input type="number" @blur="handlerBlur" @focus="handlerFocus" @input="handlerInput" size="small" width="auto" v-model="number"></ui-input>
        <ui-button class="right" :disabled="maxDisabled" @click="plus">+</ui-button>
        <div class="bubble-tip-wrap top" :class="{show: isShowTip}">最大可输入{{ max }}</div>
    </div>`,
    props: {
        size: String,
        max: {
            type: Number,
            default: 99999
        },
        min: {
            type: Number,
            default: 1
        },
        value: {
            type: Number,
            default: 0,
        },
    },
    computed: {
        minDisabled() {
            return this.min >= parseInt(this.number);
        },
        maxDisabled() {
            return this.max <= parseInt(this.number);
        }
    },
    data() {
        return {
            number: 1,
            isShowTip: false,
            timeout: null
        }
    },
    watch: {
        value() {
            this.number = this.value;
        }
    },
    mounted() {
        this.number = this.value || this.min;
    },
    methods: {
        minus() {
            if (!this.minDisabled) {
                this.number = parseInt(this.number) - 1;
                this.valueChange();
            }
        },
        plus() {
            if (!this.maxDisabled) {
                this.number = parseInt(this.number) + 1;
                this.valueChange();
            }
        },
        handlerBlur() {
            let num = parseInt(this.number);

            if (num < this.min) {
                this.number = this.min;
                return;
            }

            this.number = parseInt(this.number) || this.min;

            this.valueChange();
            this.$emit('blur');
        },
        handlerFocus() {
            this.$emit('focus')
        },
        valueChange() {
            this.$emit('input', this.number);
            this.$emit('change', this.number);
        },
        handlerInput() {
            let num = parseInt(this.number);

            if (num > this.max) {
                this.number = this.max;
                this.isShowTip = true;
                this.timeout && clearTimeout(this.timeout);
                this.timeout = setTimeout(() => {
                    this.isShowTip = false;
                }, 3000)
            }

            this.$emit('input', this.number)
        }
    }
})