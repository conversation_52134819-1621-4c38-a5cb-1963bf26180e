package com.vedeng.temporal.dto;

import java.math.BigDecimal;

public class SaleFlowOrderGoodsDto {
    private String delilveryDirectComments;
    private String deliveryCycle;
    private Integer deliveryDirect;
    private String goodsComments;
    private String insideComments;
    private Integer haveInstallation;
    private Integer isCoupons;
    private BigDecimal jxSalePrice;
    private Integer productNum;
    private BigDecimal skuAmount;
    private String skuNo;

    // getter/setter
    public String getDelilveryDirectComments() { return delilveryDirectComments; }
    public void setDelilveryDirectComments(String delilveryDirectComments) { this.delilveryDirectComments = delilveryDirectComments; }
    public String getDeliveryCycle() { return deliveryCycle; }
    public void setDeliveryCycle(String deliveryCycle) { this.deliveryCycle = deliveryCycle; }
    public Integer getDeliveryDirect() { return deliveryDirect; }
    public void setDeliveryDirect(Integer deliveryDirect) { this.deliveryDirect = deliveryDirect; }
    public String getGoodsComments() { return goodsComments; }
    public void setGoodsComments(String goodsComments) { this.goodsComments = goodsComments; }
    public String getInsideComments() { return insideComments; }
    public void setInsideComments(String insideComments) { this.insideComments = insideComments; }
    public Integer getHaveInstallation() { return haveInstallation; }
    public void setHaveInstallation(Integer haveInstallation) { this.haveInstallation = haveInstallation; }
    public Integer getIsCoupons() { return isCoupons; }
    public void setIsCoupons(Integer isCoupons) { this.isCoupons = isCoupons; }
    public BigDecimal getJxSalePrice() { return jxSalePrice; }
    public void setJxSalePrice(BigDecimal jxSalePrice) { this.jxSalePrice = jxSalePrice; }
    public Integer getProductNum() { return productNum; }
    public void setProductNum(Integer productNum) { this.productNum = productNum; }
    public BigDecimal getSkuAmount() { return skuAmount; }
    public void setSkuAmount(BigDecimal skuAmount) { this.skuAmount = skuAmount; }
    public String getSkuNo() { return skuNo; }
    public void setSkuNo(String skuNo) { this.skuNo = skuNo; }
} 