package com.vedeng.temporal.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vedeng.common.core.http.ISystemApiClient;
import com.vedeng.common.core.enums.SystemSourceEnum;
import com.vedeng.temporal.config.CompanyApiConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 系统API调用客户端 (多公司支持版)
 * 专门用于XXL-Job、Temporal等系统任务调用erp-api-standard模块的API
 * 支持动态指定真实用户信息、多公司API配置和参数化系统来源，无需复杂的密钥认证
 *
 * 功能特性：
 * - 支持多公司API配置
 * - 支持参数化系统来源（XXL-JOB、TEMPORAL、SYSTEM等）
 * - 支持用户身份模拟
 * - 线程安全设计
 *
 * <AUTHOR>
 * @version 4.1 (参数化系统来源版)
 * @since 2024-12-20
 */
@Component
public class SystemApiClient implements ISystemApiClient {
    
    private static final Logger logger = LoggerFactory.getLogger(SystemApiClient.class);
    
    private static final String SYSTEM_SOURCE_HEADER = "X-System-Source";
    private static final String SYSTEM_USER_ID_HEADER = "X-System-User-Id";
    private static final String SYSTEM_USER_NAME_HEADER = "X-System-User-Name";
    private static final String CONTENT_TYPE_HEADER = "Content-Type";
    private static final String CONTENT_TYPE_JSON = "application/json;charset=UTF-8";

    private static final String DEFAULT_SYSTEM_SOURCE = "XXL-JOB";
    private static final int CONNECT_TIMEOUT = 30000; // 30秒
    private static final int READ_TIMEOUT = 60000; // 60秒

    /**
     * 多公司API配置服务
     */
    @Autowired
    private CompanyApiConfigService companyApiConfigService;

    /**
     * 当前请求的用户信息（线程安全）
     */
    private ThreadLocal<Long> currentUserId = new ThreadLocal<>();
    private ThreadLocal<String> currentUserName = new ThreadLocal<>();

    /**
     * 当前请求的公司信息（线程安全）
     */
    private ThreadLocal<String> currentCompanyCode = new ThreadLocal<>();

    /**
     * API基础URL（默认配置，作为后备）
     */
    private String apiBaseUrl = "http://localhost";
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 指定用户ID进行API调用
     *
     * @param userId 用户ID
     * @return SystemApiClient实例（支持链式调用）
     */
    @Override
    public ISystemApiClient withUser(Long userId) {
        this.currentUserId.set(userId);
        this.currentUserName.remove();
        return this;
    }

    /**
     * 指定用户名进行API调用
     *
     * @param userName 用户名
     * @return SystemApiClient实例（支持链式调用）
     */
    @Override
    public ISystemApiClient withUser(String userName) {
        this.currentUserName.set(userName);
        this.currentUserId.remove();
        return this;
    }

    /**
     * 清除当前用户信息
     *
     * @return SystemApiClient实例（支持链式调用）
     */
    @Override
    public ISystemApiClient clearUser() {
        this.currentUserId.remove();
        this.currentUserName.remove();
        return this;
    }

    /**
     * 指定公司代码进行API调用
     *
     * @param companyCode 公司代码
     * @return SystemApiClient实例（支持链式调用）
     */
    @Override
    public ISystemApiClient withCompany(String companyCode) {
        this.currentCompanyCode.set(companyCode);
        logger.debug("设置公司代码: {}", companyCode);
        return this;
    }


    /**
     * 清除当前公司信息
     *
     * @return SystemApiClient实例（支持链式调用）
     */
    @Override
    public ISystemApiClient clearCompany() {
        this.currentCompanyCode.remove();
        return this;
    }

    /**
     * 发送POST请求到系统API
     *
     * @param apiPath API路径（如：/api/v1/temporal/workflow）
     * @param requestData 请求数据
     * @return 响应结果
     */
    public String postToSystemApi(String apiPath, Object requestData) {
        try {
            return postToSystemApi(apiPath, requestData, null, DEFAULT_SYSTEM_SOURCE);
        } finally {
            // 清理ThreadLocal，避免内存泄漏
            clearThreadLocalData();
        }
    }

    /**
     * 发送POST请求到系统API（指定系统来源）
     *
     * @param apiPath API路径（如：/api/v1/temporal/workflow）
     * @param requestData 请求数据
     * @param systemSource 系统来源（如：XXL-JOB、TEMPORAL、SYSTEM等）
     * @return 响应结果
     */
    public String postToSystemApi(String apiPath, Object requestData, String systemSource) {
        try {
            return postToSystemApi(apiPath, requestData, null, systemSource);
        } finally {
            // 清理ThreadLocal，避免内存泄漏
            clearThreadLocalData();
        }
    }

    /**
     * 发送POST请求到系统API（使用枚举指定系统来源）
     *
     * @param apiPath API路径（如：/api/v1/temporal/workflow）
     * @param requestData 请求数据
     * @param systemSource 系统来源枚举
     * @return 响应结果
     */
    public String postToSystemApi(String apiPath, Object requestData, SystemSourceEnum systemSource) {
        try {
            String sourceCode = (systemSource != null) ? systemSource.getCode() : DEFAULT_SYSTEM_SOURCE;
            return postToSystemApi(apiPath, requestData, null, sourceCode);
        } finally {
            // 清理ThreadLocal，避免内存泄漏
            clearThreadLocalData();
        }
    }
    
    /**
     * 发送POST请求到系统API（带额外请求头）
     *
     * @param apiPath API路径
     * @param requestData 请求数据
     * @param additionalHeaders 额外的请求头
     * @return 响应结果
     */
    public String postToSystemApi(String apiPath, Object requestData, Map<String, String> additionalHeaders) {
        return postToSystemApi(apiPath, requestData, additionalHeaders, DEFAULT_SYSTEM_SOURCE);
    }

    /**
     * 发送POST请求到系统API（带额外请求头和系统来源）
     *
     * @param apiPath API路径
     * @param requestData 请求数据
     * @param additionalHeaders 额外的请求头
     * @param systemSource 系统来源（如：XXL-JOB、TEMPORAL、SYSTEM等）
     * @return 响应结果
     */
    public String postToSystemApi(String apiPath, Object requestData, Map<String, String> additionalHeaders, String systemSource) {
        String fullUrl = buildFullUrl(apiPath);
        
        logger.info("系统API调用开始，URL: {}", fullUrl);
        
        HttpURLConnection connection = null;
        try {
            // 创建连接
            URL url = new URL(fullUrl);
            connection = (HttpURLConnection) url.openConnection();
            
            // 设置请求方法和属性
            connection.setRequestMethod("POST");
            connection.setDoOutput(true);
            connection.setDoInput(true);
            connection.setConnectTimeout(CONNECT_TIMEOUT);
            connection.setReadTimeout(READ_TIMEOUT);
            
            // 设置系统来源头和用户信息头
            String effectiveSystemSource = (systemSource != null && !systemSource.trim().isEmpty()) ? systemSource : DEFAULT_SYSTEM_SOURCE;
            connection.setRequestProperty(SYSTEM_SOURCE_HEADER, effectiveSystemSource);
            connection.setRequestProperty(CONTENT_TYPE_HEADER, CONTENT_TYPE_JSON);

            // 设置用户信息头
            setUserHeaders(connection);
            
            // 设置额外的请求头
            if (additionalHeaders != null && !additionalHeaders.isEmpty()) {
                for (Map.Entry<String, String> entry : additionalHeaders.entrySet()) {
                    connection.setRequestProperty(entry.getKey(), entry.getValue());
                }
            }
            
            // 发送请求数据
            if (requestData != null) {
                String jsonData = objectMapper.writeValueAsString(requestData);
                logger.info("请求数据: {}", jsonData);
                
                try (OutputStream os = connection.getOutputStream()) {
                    byte[] input = jsonData.getBytes(StandardCharsets.UTF_8);
                    os.write(input, 0, input.length);
                }
            }
            
            // 获取响应
            int responseCode = connection.getResponseCode();
            logger.debug("响应状态码: {}", responseCode);
            
            // 读取响应内容
            StringBuilder response = new StringBuilder();
            try (BufferedReader br = new BufferedReader(
                    new InputStreamReader(
                            responseCode >= 200 && responseCode < 300 ? 
                                connection.getInputStream() : connection.getErrorStream(),
                            StandardCharsets.UTF_8))) {
                
                String responseLine;
                while ((responseLine = br.readLine()) != null) {
                    response.append(responseLine.trim());
                }
            }
            
            String responseBody = response.toString();
            logger.info("系统API调用完成，状态码: {}, 响应长度: {}", responseCode, responseBody.length());
            logger.info("响应内容: {}", responseBody);
            
            if (responseCode >= 200 && responseCode < 300) {
                return responseBody;
            } else {
                logger.error("系统API调用失败，状态码: {}, 响应: {}", responseCode, responseBody);
                throw new RuntimeException("API调用失败，状态码: " + responseCode + ", 响应: " + responseBody);
            }
            
        } catch (Exception e) {
            logger.error("系统API调用异常，URL: {}", fullUrl, e);
            throw new RuntimeException("系统API调用异常: " + e.getMessage(), e);
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }
    
    /**
     * 发送GET请求到系统API
     *
     * @param apiPath API路径
     * @return 响应结果
     */
    public String getFromSystemApi(String apiPath) {
        try {
            return getFromSystemApi(apiPath, null, DEFAULT_SYSTEM_SOURCE);
        } finally {
            // 清理ThreadLocal，避免内存泄漏
            clearThreadLocalData();
        }
    }

    /**
     * 发送GET请求到系统API（指定系统来源）
     *
     * @param apiPath API路径
     * @param systemSource 系统来源（如：XXL-JOB、TEMPORAL、SYSTEM等）
     * @return 响应结果
     */
    public String getFromSystemApi(String apiPath, String systemSource) {
        try {
            return getFromSystemApi(apiPath, null, systemSource);
        } finally {
            // 清理ThreadLocal，避免内存泄漏
            clearThreadLocalData();
        }
    }

    /**
     * 发送GET请求到系统API（使用枚举指定系统来源）
     *
     * @param apiPath API路径
     * @param systemSource 系统来源枚举
     * @return 响应结果
     */
    public String getFromSystemApi(String apiPath, SystemSourceEnum systemSource) {
        try {
            String sourceCode = (systemSource != null) ? systemSource.getCode() : DEFAULT_SYSTEM_SOURCE;
            return getFromSystemApi(apiPath, null, sourceCode);
        } finally {
            // 清理ThreadLocal，避免内存泄漏
            clearThreadLocalData();
        }
    }
    
    /**
     * 发送GET请求到系统API（带额外请求头）
     *
     * @param apiPath API路径
     * @param additionalHeaders 额外的请求头
     * @return 响应结果
     */
    public String getFromSystemApi(String apiPath, Map<String, String> additionalHeaders) {
        return getFromSystemApi(apiPath, additionalHeaders, DEFAULT_SYSTEM_SOURCE);
    }

    /**
     * 发送GET请求到系统API（带额外请求头和系统来源）
     *
     * @param apiPath API路径
     * @param additionalHeaders 额外的请求头
     * @param systemSource 系统来源（如：XXL-JOB、TEMPORAL、SYSTEM等）
     * @return 响应结果
     */
    public String getFromSystemApi(String apiPath, Map<String, String> additionalHeaders, String systemSource) {
        String fullUrl = buildFullUrl(apiPath);
        
        logger.info("系统API GET调用开始，URL: {}", fullUrl);
        
        HttpURLConnection connection = null;
        try {
            // 创建连接
            URL url = new URL(fullUrl);
            connection = (HttpURLConnection) url.openConnection();
            
            // 设置请求方法和属性
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(CONNECT_TIMEOUT);
            connection.setReadTimeout(READ_TIMEOUT);
            
            // 设置系统来源头和用户信息头
            String effectiveSystemSource = (systemSource != null && !systemSource.trim().isEmpty()) ? systemSource : DEFAULT_SYSTEM_SOURCE;
            connection.setRequestProperty(SYSTEM_SOURCE_HEADER, effectiveSystemSource);

            // 设置用户信息头
            setUserHeaders(connection);
            
            // 设置额外的请求头
            if (additionalHeaders != null && !additionalHeaders.isEmpty()) {
                for (Map.Entry<String, String> entry : additionalHeaders.entrySet()) {
                    connection.setRequestProperty(entry.getKey(), entry.getValue());
                }
            }
            
            // 获取响应
            int responseCode = connection.getResponseCode();
            logger.debug("响应状态码: {}", responseCode);
            
            // 读取响应内容
            StringBuilder response = new StringBuilder();
            try (BufferedReader br = new BufferedReader(
                    new InputStreamReader(
                            responseCode >= 200 && responseCode < 300 ? 
                                connection.getInputStream() : connection.getErrorStream(),
                            StandardCharsets.UTF_8))) {
                
                String responseLine;
                while ((responseLine = br.readLine()) != null) {
                    response.append(responseLine.trim());
                }
            }
            
            String responseBody = response.toString();
            logger.info("系统API GET调用完成，状态码: {}, 响应长度: {}", responseCode, responseBody.length());
            logger.debug("响应内容: {}", responseBody);
            
            if (responseCode >= 200 && responseCode < 300) {
                return responseBody;
            } else {
                logger.error("系统API GET调用失败，状态码: {}, 响应: {}", responseCode, responseBody);
                throw new RuntimeException("API调用失败，状态码: " + responseCode + ", 响应: " + responseBody);
            }
            
        } catch (Exception e) {
            logger.error("系统API GET调用异常，URL: {}", fullUrl, e);
            throw new RuntimeException("系统API调用异常: " + e.getMessage(), e);
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }
    
    /**
     * 设置用户信息到请求头
     */
    private void setUserHeaders(HttpURLConnection connection) {
        // 使用用户ID
        Long userId = currentUserId.get();
        if (userId != null) {
            connection.setRequestProperty(SYSTEM_USER_ID_HEADER, userId.toString());
            logger.debug("设置用户ID到请求头: {}", userId);
            return;
        }

        // 使用用户名
        String userName = currentUserName.get();
        if (userName != null) {
            connection.setRequestProperty(SYSTEM_USER_NAME_HEADER, userName);
            logger.debug("设置用户名到请求头: {}", userName);
            return;
        }

        logger.debug("未设置用户信息，将使用默认系统用户");
    }

    /**
     * 清理ThreadLocal数据，避免内存泄漏
     */
    private void clearThreadLocalData() {
        currentUserId.remove();
        currentUserName.remove();
        currentCompanyCode.remove();
    }

    /**
     * 获取有效的BaseUrl
     * 优先级：公司域名配置 > 默认apiBaseUrl
     */
    private String getEffectiveBaseUrl() {
        String companyCode = currentCompanyCode.get();

        // 如果指定了公司代码，尝试从CompanyApiConfigService获取域名配置
        if (companyCode != null) {
            try {
                String companyDomain = companyApiConfigService.getCompanyDomain(companyCode);
                if (companyDomain != null && !companyDomain.trim().isEmpty()) {
                    logger.debug("使用公司域名配置: {} -> {}", companyCode, companyDomain);
                    return companyDomain;
                }
                logger.warn("未找到公司域名配置: {}，使用默认配置", companyCode);
            } catch (Exception e) {
                logger.error("获取公司域名配置失败: {}，使用默认配置", companyCode, e);
            }
        }

        // 使用默认配置
        logger.debug("使用默认API配置: {}", apiBaseUrl);
        return apiBaseUrl;
    }

    /**
     * 构建完整的URL
     */
    private String buildFullUrl(String apiPath) {
        // 如果apiPath已经是完整URL，直接返回
        if (apiPath.startsWith("http://") || apiPath.startsWith("https://")) {
            logger.debug("使用完整URL: {}", apiPath);
            return apiPath;
        }

        // 获取有效的BaseUrl
        String effectiveBaseUrl = getEffectiveBaseUrl();

        // 处理URL拼接
        String baseUrl = effectiveBaseUrl.endsWith("/") ?
            effectiveBaseUrl.substring(0, effectiveBaseUrl.length() - 1) : effectiveBaseUrl;
        String path = apiPath.startsWith("/") ? apiPath : "/" + apiPath;

        String fullUrl = baseUrl + path;
        logger.debug("构建完整URL: {} + {} = {}", baseUrl, path, fullUrl);
        return fullUrl;
    }

    /**
     * 检查当前实现是否支持自动公司配置
     * SystemApiClient 完整版支持通过 CompanyApiConfigService 自动获取公司API地址
     *
     * @return true，表示支持自动公司配置
     */
    @Override
    public boolean supportsAutoCompanyConfig() {
        return true;
    }

    /**
     * 获取当前实现的类型描述
     *
     * @return 实现类型描述
     */
    @Override
    public String getImplementationType() {
        return "SystemApiClient-Full (with CompanyApiConfigService)";
    }
}
