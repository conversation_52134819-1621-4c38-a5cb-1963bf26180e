package com.vedeng.kpi.threadLocal;

import com.vedeng.kpi.model.DTO.KpiDataQueryDto;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 线程对象.
 * @jira: VDERP-2376【五行剑法】规则修改.
 * @notes: 由于执行团队数据刷新时,对于每一个成员都得计算一次团队平均值,
 *          因此需要重复查询团队数据,这里使用threadlocale共享团队数据,避免同一次定时任务里多次重复查询.
 * @version: 1.0.
 * @date: 2020/7/11 10:45 上午.
 * @author: Tomcat.Hui.
 */
public class KpiThreadParams {

    private KpiDataQueryDto query;

    private List<KpiDataQueryDto> groupOrderAmountMonth;

    private List<KpiDataQueryDto> groupCoCustomersMonth;

    private List<KpiDataQueryDto> groupBdCoCustomersMonth;

    private List<KpiDataQueryDto> groupChanceMonth;

    private List<KpiDataQueryDto> groupRangeNewChanceMonth;

    public void clearAll(){
        this.setGroupCoCustomersMonth(null);
        this.setGroupOrderAmountMonth(null);
        this.setQuery(null);
    }

    public KpiDataQueryDto getQuery() {
        return query;
    }

    public void setQuery(KpiDataQueryDto query) {
        this.query = query;
    }

    public List<KpiDataQueryDto> getGroupOrderAmountMonth() {
        return groupOrderAmountMonth;
    }

    public void setGroupOrderAmountMonth(List<KpiDataQueryDto> groupOrderAmountMonth) {
        this.groupOrderAmountMonth = groupOrderAmountMonth;
    }

    public List<KpiDataQueryDto> getGroupCoCustomersMonth() {
        return groupCoCustomersMonth;
    }

    public void setGroupCoCustomersMonth(List<KpiDataQueryDto> groupCoCustomersMonth) {
        this.groupCoCustomersMonth = groupCoCustomersMonth;
    }

    public List<KpiDataQueryDto> getGroupBdCoCustomersMonth() {
        return groupBdCoCustomersMonth;
    }

    public void setGroupBdCoCustomersMonth(List<KpiDataQueryDto> groupBdCoCustomersMonth) {
        this.groupBdCoCustomersMonth = groupBdCoCustomersMonth;
    }

    public List<KpiDataQueryDto> getGroupChanceMonth() {
        return groupChanceMonth;
    }

    public void setGroupChanceMonth(List<KpiDataQueryDto> groupChanceMonth) {
        this.groupChanceMonth = groupChanceMonth;
    }

    public List<KpiDataQueryDto> getGroupRangeNewChanceMonth() {
        return groupRangeNewChanceMonth;
    }

    public void setGroupRangeNewChanceMonth(List<KpiDataQueryDto> groupRangeNewChanceMonth) {
        this.groupRangeNewChanceMonth = groupRangeNewChanceMonth;
    }
}
