package com.vedeng.erp.trader.service;

import com.vedeng.erp.trader.dto.TraderCustomerMarketingPrincipalDto;
import com.vedeng.erp.trader.dto.TraderDealerFrontDto;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 客户营销-主营属性
 * @Date 2023/8/16 14:54
 */
public interface TraderCustomerMarketingPrincipalApiService {


    /**
     * 根据前台传回的对象进行解析
     * @param traderDealerFrontDto 页面封装对象
     * @return TraderCustomerMarketingPrincipalDto
     */
    TraderCustomerMarketingPrincipalDto processFrontData(TraderDealerFrontDto traderDealerFrontDto);

    /**
     * 保存标签 经销商 终端 一个
     * @param traderCustomerMarketingPrincipalDto 数据
     */
    void add(TraderCustomerMarketingPrincipalDto traderCustomerMarketingPrincipalDto);

    /**
     * 根据客户id封装
     * @param traderCustomerId 客户id
     * @return TraderDealerFrontDto.Principal
     */
    TraderDealerFrontDto.Principal stealthFrontData(Integer traderCustomerId);

    /**
     * 根据traderId查询主营属性
     *
     * @param traderId traderId
     * @return TraderCustomerMarketingPrincipalDto
     */
    TraderCustomerMarketingPrincipalDto getByTraderId(Integer traderId);

    /**
     * 更新
     * @param principalDto
     */
    void update(TraderCustomerMarketingPrincipalDto principalDto);

    /**
     * 清空原始数据
     * @param traderCustomerId
     */
    void clearByTraderCustomer(Integer traderCustomerId);
}
