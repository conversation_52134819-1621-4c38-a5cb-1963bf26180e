<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
            + path + "/";
%>
<c:set var="path" value="<%=basePath%>" scope="application"/>
<link rel="stylesheet" href="<%=basePath%>static/css/general.css?rnd=${resourceVersionKey}"/>
<link rel="stylesheet" href="<%=basePath%>static/css/manage.css"/>

<div id="app">
    <el-row style="margin-left: 30px;" v-if="this.hasCustomerInfo == 1">
        <div>客户名称：{{this.chosenCustomer}}
            <el-button type="primary" size="mini" @click="resetting()">重新搜索</el-button>
        </div>
        <div style="margin-top: 100px; text-align: center">
            <el-button type="primary" size="mini" @click="saveTerminal()">提交</el-button>
        </div>
    </el-row>

    <el-row style="margin-left: 10px" v-if="this.hasCustomerInfo == 0">
        <el-row style="margin-bottom: 30px">
            <el-input
                    v-model="traderName"
                    placeholder="输入客户名称搜索"
                    maxlength="30"
                    style="width: 300px">
            </el-input>
            <el-button type="primary" @click="doSearch()">搜索</el-button>
        </el-row>

        <el-table
                :data="tableData"
                style="width: 100%">
            <el-table-column
                    label="客户名称"
                    prop="traderName">
            </el-table-column>
            <el-table-column
                    prop="address"
                    label="地区"
                    width="220">
            </el-table-column>
            <el-table-column
                    prop="addTime"
                    label="创建时间"
                    width="220">
                <template slot-scope="scope">
                    <span>{{parseTime(scope.row.addTime)}}</span>
                </template>
            </el-table-column>
            <el-table-column
                    prop="userName"
                    label="归属销售"
                    width="220">
            </el-table-column>
            <el-table-column
                    label="选择"
                    width="100">
                <template slot-scope="scope">
                    <el-button type="text" @click="chooseCustomer(scope.row)">选择</el-button>
                </template>
            </el-table-column>
        </el-table>

        <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPageNo"
                :page-sizes="[5,10,20]"
                :page-size="currentSize"
                layout="->, total, sizes, prev, pager, next, jumper"
                :total="totalLines">
        </el-pagination>
    </el-row>
</div>
<script src="${pageContext.request.contextPath}/static/api/trader/traderCustomerTerminal.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript">
    const hasCustomerInfo = '${hasCustomerInfo}';
    const businessId = '${businessId}';

    new Vue({
        el: '#app',
        data() {
            return {
                traderName: '',
                currentSize: 10,
                currentPageNo: 1,
                totalLines: 0,
                tableData: [],
                hasCustomerInfo: hasCustomerInfo,
                businessId: businessId,
                chosenCustomer: '',
                chosenTraderId: 0,
                chosenCustomerId: 0
            }
        },

        mounted() {
            loadingApp()
        },

        created() {
            this.initPageData();
        },

        methods: {
            // 销售订单列表页进入时，若已维护，则需要展示已维护的信息
            initPageData() {
                if (this.hasCustomerInfo == 1) {
                    getSaleOrderTerminalCustomerByOrderId({"saleOrderId" : this.businessId}).then(res => {
                        this.chosenCustomer = res.data.data.traderName;
                    })
                }
            },

            doSearch() {
                var pageParam = {
                    "pageSize": this.currentSize,
                    "pageNum": this.currentPageNo,
                    "param": {
                        traderName: this.traderName
                    }
                };

                getTraderCustomerPage(pageParam).then(res => {
                    this.tableData = res.data.data.list;
                    this.totalLines = res.data.data.total;
                })
            },

            resetting() {
                this.hasCustomerInfo = 0;
            },

            saveTerminal() {
                var customer = {
                    "saleOrderId": this.businessId,
                    "traderName": this.chosenCustomer,
                    "traderId": this.chosenTraderId,
                    "traderCustomerId": this.chosenCustomerId
                };
                saveSaleOrderTerminalCustomer(customer).then(res => {
                    //先得到当前iframe层的索引
                    var index = parent.layer.getFrameIndex(window.name);
                    parent.layer.close(index);
                    window.parent.location.reload();
                })
            },

            // pageSize切换
            handleSizeChange(val) {
                this.currentSize = val;
                this.doSearch();
            },

            // 下一页上一页切换
            handleCurrentChange(val) {
                this.currentPageNo = val;
                this.doSearch();
            },

            // 选择按钮
            chooseCustomer(row) {
                this.chosenCustomer = row.traderName;
                this.chosenTraderId = row.traderId;
                this.chosenCustomerId = row.traderCustomerId;
                this.hasCustomerInfo = 1;
            }
        }
    })
</script>

<style>
    .el-input--mini .el-input__icon {
        line-height: 0;
    }

    .el-pagination .btn-next .el-icon, .el-pagination .btn-prev .el-icon {
        margin-right: 5px;
    }
</style>
