package com.vedeng.goods.manager.validator.InvalidatorChain.sku.Invalid;

import com.vedeng.common.model.ResultInfo;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.exception.InvalidatorChainException;
import com.vedeng.goods.manager.validator.GoodsInvalidatorChain;
import com.vedeng.goods.model.CoreSku;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.vedeng.goods.manager.validator.enums.GoodsInvalidatorErrorEnum.SkuUsingInvalidatorError;


/**
 * .(11)	该SKU的启用状态为已启用；
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2021/9/1 16:05.
 * @author: Randy.Xu.
 */
@Service
public class SkuUsingInvalidator extends GoodsInvalidatorChain {

    @Resource
    CoreSkuMapper coreSkuMapper;

    @Override
    public void doInvalid() {
        String SkuNo = getSkuNo();
        List<String> skuNos = Collections.singletonList(SkuNo);
        List<CoreSku> skuList = coreSkuMapper.selectSkuBySkuNoList(skuNos);
        Optional<CoreSku> first = skuList.stream().filter(e -> e.getStatus() != 1).findFirst();
        if(first.isPresent()){
            throw new InvalidatorChainException(SkuUsingInvalidatorError.getCode(), SkuUsingInvalidatorError.getMessage());
        }
    }
}
