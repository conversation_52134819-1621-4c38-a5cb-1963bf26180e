package com.wms.constant;

/**
 * <AUTHOR>
 * @ClassName LogicalEnum.java
 * @Description TODO 逻辑仓枚举
 * @createTime 2020年06月28日 11:37:00
 */
public enum LogicalEnum {
    HG(1701, "合格库", "HG"),
    J<PERSON><PERSON>(1708, "近效期", "JXQ"),
    CJXQ(1703, "超近效期", "CJXQ"),
    B(1704, "等级B", "B"),
    C(1705, "等级C", "C"),
    D(1706, "样品库", "D"),
    THREE(1707, "三方库", "SPL"),
    HDC(1702, "活动库", "HDC"),
    DJC(1709, "待检测库", "DJC"),
    BHG(1710, "不合格库", "BHG"),
    DCL(1711, "待处理库", "DCL"),
    XC(1800, "电商仓", "XC"),
    BJ(1801, "成都仓" ,"BJ");

    LogicalEnum(Integer logicalWarehouseId, String logicalWarehouseName, String logicalWarehouseCode) {
        this.logicalWarehouseId = logicalWarehouseId;
        this.logicalWarehouseName = logicalWarehouseName;
        this.logicalWarehouseCode = logicalWarehouseCode;
    }

    private Integer logicalWarehouseId;

    private String logicalWarehouseName;

    private String logicalWarehouseCode;

    public static String getLogicalWarehouseName(Integer warehouseId) {
        String logicalWarehouseName = "";

        for (LogicalEnum enumItem : LogicalEnum.values()) {
            if (enumItem.getLogicalWarehouseId().equals(warehouseId)) {
                logicalWarehouseName = enumItem.getLogicalWarehouseName();
                break;
            }
        }

        return logicalWarehouseName;
    }

    public static String getLogicalWarehouseCode(Integer warehouseId) {
        String logicalWarehouseCode = "";

        for (LogicalEnum enumItem : LogicalEnum.values()) {
            if (enumItem.getLogicalWarehouseId().equals(warehouseId)) {
                logicalWarehouseCode = enumItem.getLogicalWarehouseCode();
                break;
            }
        }

        return logicalWarehouseCode;
    }

    public static Integer getLogicalWarehouseIdByName(String logicalWarehouseName) {
        Integer logicalWarehouseId = 0;

        for (LogicalEnum enumItem : LogicalEnum.values()) {
            if (enumItem.getLogicalWarehouseName().equals(logicalWarehouseName)) {
                logicalWarehouseId = enumItem.getLogicalWarehouseId();
                break;
            }
        }

        return logicalWarehouseId;
    }

    public static Integer getLogicalWarehouseIdByCode(String logicalWarehouseCode) {
        Integer logicalWarehouseId = 0;

        for (LogicalEnum enumItem : LogicalEnum.values()) {
            if (enumItem.getLogicalWarehouseCode().equals(logicalWarehouseCode)) {
                logicalWarehouseId = enumItem.getLogicalWarehouseId();
                break;
            }
        }

        return logicalWarehouseId;
    }

    public Integer getLogicalWarehouseId() {
        return logicalWarehouseId;
    }

    public String getLogicalWarehouseName() {
        return logicalWarehouseName;
    }

    public String getLogicalWarehouseCode() {
        return logicalWarehouseCode;
    }
}
