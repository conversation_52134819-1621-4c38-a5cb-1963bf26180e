package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.enums.OtherTypeConst;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInItemDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeeStorageOutDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeStorageOutDto;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 报废出库单历史数据处理
 * @date 2023/5/26 13:55
 */
@Service
@Slf4j
public class BatchScrapHistoryProcessor implements ItemProcessor<BatchWarehouseGoodsOutInDto, KingDeeStorageOutDto> {
    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;
    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;

    @Override
    public KingDeeStorageOutDto process(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto) throws Exception {
        KingDeeStorageOutDto dto = new KingDeeStorageOutDto();
        dto.setFBillNo(batchWarehouseGoodsOutInDto.getOutInNo());
        if (!OtherTypeConst.UPDATE_REMARK_ORTHER_TYPE.equals(batchWarehouseGoodsOutInDto.getUpdateRemark())){
            return null;
        }
        // 判断是否数据已存在
        boolean old = kingDeeBaseApi.isExist(dto);
        if(old){
            log.info("报废出库单,数据已存在:{}", JSON.toJSONString(batchWarehouseGoodsOutInDto));
            return null;
        }
        log.info("报废出库单,BatchReceiveProcessorService.process:{}", JSON.toJSONString(batchWarehouseGoodsOutInDto));

        batchWarehouseGoodsOutInDto.setBatchWarehouseGoodsOutInItemDtos(batchWarehouseGoodsOutInItemDtoMapper.findByOutInNo(batchWarehouseGoodsOutInDto.getOutInNo()));
        dto.setFId("0");
        dto.setFDate(DateUtil.formatDateTime(batchWarehouseGoodsOutInDto.getOutInTime()));
        dto.setFBillNo(batchWarehouseGoodsOutInDto.getOutInNo());

        List<KingDeeStorageOutDetailDto> detailList = new ArrayList<>();
        if (CollUtil.isEmpty(batchWarehouseGoodsOutInDto.getBatchWarehouseGoodsOutInItemDtos())) {
            return null;
        }
        batchWarehouseGoodsOutInDto.getBatchWarehouseGoodsOutInItemDtos().forEach(d -> {
            KingDeeStorageOutDetailDto detailDto = new KingDeeStorageOutDetailDto();
            detailDto.setFMaterialId("V" + d.getGoodsId());
            detailDto.setFQty(d.getNum().abs().toString());
            detailDto.setFQzokYsddh(batchWarehouseGoodsOutInDto.getRelateNo());
            detailDto.setFQzokGsywdh(batchWarehouseGoodsOutInDto.getRelateNo());
            detailDto.setFQzokPch(d.getVedengBatchNumber());
            detailDto.setFQzokXlh(d.getBarcodeFactory());
            detailDto.setFQzokYwlx("报废单");
            detailDto.setFQzokSfzf("否");
            detailDto.setFStockId("CK9999");
            detailList.add(detailDto);
        });
        dto.setFEntity(detailList);
        return dto;
    }
}
