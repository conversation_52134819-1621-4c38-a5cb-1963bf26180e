package com.vedeng.erp.business.service;

import com.vedeng.crm.admin.api.dto.tuoke.BusinessCluesDetailDTO;
import com.vedeng.erp.business.domain.entity.BusinessClues;
import com.vedeng.erp.business.domain.vo.BusinessCluesVo;
import com.vedeng.trader.model.vo.TraderContactVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/3/10 17:09
 **/

public interface BusinessCluesService {

    /**
     * 分页查询线索列表
     * @param queryParam 分页查询参数
     * @return
     */
    Map<String, Object> getBusinessCluesListPage(Map<String, Object> queryParam);

    /**
     * 修改线索的置顶状态
     * @param businessCluesVo
     * @return
     */
    int changeTopStatus(BusinessCluesVo businessCluesVo);

    /**
     * 保存修改线索价值
     * @param businessCluesVo
     * @return
     */
    int saveCluesWorth(BusinessCluesVo businessCluesVo);

    /**
     * 查询线索信息
     * @param businessCluesId
     * @return
     */
    BusinessClues getCluesInfo(Integer businessCluesId);

    /**
     * 线索详情页-组装中标联系人信息集合
     * @param traderId 客户id
     * @param cluesDetailDTOS crm查出来的 中标信息详情
     * @return
     */
    List<TraderContactVo> getTraderContactList(Integer traderId, List<BusinessCluesDetailDTO> cluesDetailDTOS);
}
