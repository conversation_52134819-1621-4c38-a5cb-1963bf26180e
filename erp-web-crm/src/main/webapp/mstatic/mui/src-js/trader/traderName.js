// 详情回显
Vue.component('ui-trader-name', {
    template: `
        <div class="vd-ui-trader-name-wrap">
            <template v-if="info.traderName">
                <div class="trader-name-wrap">
                    <span class="vd-ui-trader-txt" :class="{ 'text-line-1': info.nameFlex }">{{info.traderName}}</span>
                    <i v-if="info.tycFlag == 'Y'" @click.stop="openTyc" class="icon-tyc vd-ui_icon icon-tianyancha"></i>
                    <i v-if="info.baidu" @click.stop="openBaidu" class="icon-baidu vd-ui_icon icon-baidu2" title="百度搜索客户信息"></i>
                </div>
                <!-- <template v-if="info.traderStatus">
                    <ui-tip v-if="info.traderId">已选择建档客户</ui-tip>
                    <ui-tip>当前录入客户为非建档客户</ui-tip>
                </template> -->
            </template>
            <template v-else>-</template>
            <ui-tyc-detail ref="tycDetail"></ui-tyc-detail>
        </div>
    `,
    props: {
        info: {
            type: Object,
            default() {
                return {};
            }
        },
        // traderStatus 是否展示客户建档状态
    },
    data() {
        return {
        };
    },
    computed: {

    },
    mounted() {

    },
    methods: {
        openTyc() {
            this.$refs.tycDetail.show(this.info.traderName);
        },
        openBaidu() {
            window.open('https://www.baidu.com/s?wd=' + this.info.traderName)
        }
    }
});
