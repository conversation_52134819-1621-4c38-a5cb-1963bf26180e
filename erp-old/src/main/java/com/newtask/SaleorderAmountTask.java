package com.newtask;

import com.vedeng.common.constant.OrderDataUpdateConstant;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.orderstrategy.BuyOrderAmountStrategy;
import com.vedeng.common.orderstrategy.StrategyContext;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.Saleorder;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 *订单实际金额
 * <AUTHOR>
 * @date $
 */
@Component
@JobHandler(value="SaleorderAmountTask")
public class SaleorderAmountTask extends AbstractJobHandler {
    private Logger logger = LoggerFactory.getLogger(SaleorderAmountTask.class);
    @Resource
    private SaleorderMapper saleorderMapper;
    @Resource
    private BuyorderMapper buyorderMapper;

    @Autowired
    private BuyOrderAmountStrategy buyOrderAmountStrategy;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("XXL-JOB, Hello World.");
        String[] split = param.split(":");
        String pa = split[1];
        switch (split[0]){
            case "sale":
                updateSaleOrderAmountInfo(pa);
                break;
            case "buy":
                updateBuyorderAmountInfo(pa);
                break;
            case "buyId":
                updateBuyorderAmountInfoById(Integer.valueOf(pa.trim()));
                break;
            case "saleId":
                updateSaleOrderAmountInfoById(Integer.valueOf(pa.trim()));
                break;
        }
        return SUCCESS;
    }

    private void updateBuyorderAmountInfo(String param) {
        XxlJobLogger.log("SaleorderAmountTask.updateSaleOrderAmountInfo | begin ...............");
        try {
            List<Integer> buyorderIdList = new ArrayList<>();
            if("all".equals(param)) {
                buyorderIdList = buyorderMapper.getAllBuyorderId();
                executeBuyorder(buyorderIdList);
            }else{
                int day = Integer.parseInt(param.trim());
                List<Integer> ids = buyorderMapper.getrecentDayOrder(day);
                executeBuyorder(ids);
            }
            XxlJobLogger.log("SaleorderAmountTask.updateSaleOrderAmountInfo | end ...............");
        }catch (Exception e){
            XxlJobLogger.log("SaleorderAmountTask.updateSaleOrderAmountInfo error:{}",e);
        }
    }

    private void executeBuyorder(List<Integer> buyorderIdList) {
        for (Integer orderId : buyorderIdList) {
            updateBuyorderAmountInfoById(orderId);
        }
    }

    private void updateSaleOrderAmountInfo(String param) {
        XxlJobLogger.log("SaleorderAmountTask.updateSaleOrderAmountInfo | begin ...............");

        Integer start = 0;
        try {
            List<Integer> saleorderIdList = new ArrayList<>();
            if("all".equals(param)) {
                Saleorder saleorder = new Saleorder();
                if (start == null) {
                    start = 0;
                }
                saleorder.setOrgId(start);
                int allCount = saleorderMapper.getSaleorderidAll();
                for (int i = start; i < allCount / 1000 + 1; i++) {
                    saleorderIdList = saleorderMapper.getSaleorderidAllLimit(i * 1000);
                    execAmount(saleorderIdList);
                    XxlJobLogger.log("SaleorderAmountTask 当前更新到行页数 :{}", i);
                }
            }else{
                int day = Integer.parseInt(param.trim());
                saleorderIdList = saleorderMapper.getrecentDayOrder(day);
                execAmount(saleorderIdList);
                XxlJobLogger.log("更新"+saleorderIdList.size()+"条数据");
            }
            XxlJobLogger.log("SaleorderAmountTask.updateSaleOrderAmountInfo | end ...............");
        }catch (Exception e){
            XxlJobLogger.log("SaleorderAmountTask.updateSaleOrderAmountInfo error:{}",e);
        }
    }

    private void execAmount(List<Integer> saleorderIdList) {
        for (Integer saleorderId : saleorderIdList) {
            updateSaleOrderAmountInfoById(saleorderId);
        }
    }

    private void updateBuyorderAmountInfoById(Integer orderId) {
        Buyorder buyorder = buyorderMapper.selectByPrimaryKey(orderId);
        buyOrderAmountStrategy.execute(buyorder);
    }
    private void updateSaleOrderAmountInfoById(Integer orderId) {
        StrategyContext strategyContext = new StrategyContext();
        strategyContext.add(StrategyContext.AMOUNT_STRATEGY, OrderDataUpdateConstant.SALE_ORDER_VAILD);
        strategyContext.executeAll(orderId);
    }

}
