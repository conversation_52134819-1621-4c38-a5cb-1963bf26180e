package com.vedeng.erp.buyorder.dto;

import com.vedeng.erp.buyorder.domain.entity.Express;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class BuyOrderExpressDto extends Express {

    /**
     * 查询条件-快递单号
     */
    private String searchExpressNo;

    /**
     * 查询条件-关联单号
     */
    private String searchRelatedNo;

    /**
     * 发货日期开始
     */
    private Date searchDeliveryDateStart;

    /**
     * 发货日期结束
     */
    private Date searchDeliveryDateEnd;

    /**
     * 发货日期开始时间戳
     */
    private Long searchDeliveryDateStartStr;

    /**
     * 发货日期结束时间戳
     */
    private Long searchDeliveryDateEndStr;

    /**
     * 新疆/西藏/内蒙古/青海 REGION_ID
     */
    private List<Integer> remoteAreaRegionId;

    /**
     * 快递公司名称
     */
    private String logisticsName;

    /**
     * 销售单号
     */
    private String saleorderNo;

    /**
     * 销售订单id
     */
    private Integer saleorderId;

    /**
     * 发件人
     */
    private String addresser;

    /**
     * 收件人
     */
    private String receiver;

    /**
     * 收货地址
     */
    private String receiverAddress;

    /**
     * 寄送时间
     */
    private String deliveryTimeStr;

}
