package com.vedeng.erp.kingdee.dto;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 付款退款单 明细
 *
 * <AUTHOR>
 */

@Getter
@Setter
public class KingDeePayRefundEntryDto {

    /**
     * 结算方式
     */
    private String fsettletypeid;

    /**
     * 原付款用途
     */
    private String fpurposeid;

    /**
     * 金额
     */
    private BigDecimal frefundamountfor;

    /**
     * 我方银行账号
     */
    private String faccountid;

    /**
     * 手续费
     */
    private BigDecimal fhandlingchargefor;

    /**
     * 原始订单号
     */
    private String fQzokYsddh;

    /**
     * 归属业务单号
     */
    private String fQzokGsywdh;

    /**
     * 业务类型
     */
    private String fQzokYwlx;

    /**
     * 贝登订单行id
     */
    private String fQzokBddjhid;

}
