package com.wms.unitconversionorder.model.dto;

import com.vedeng.common.core.base.BaseDto;
import lombok.*;

import java.math.BigDecimal;

/**
 * @description 库存转换换单明细表
 * <AUTHOR>
 * @date 2023/2/17 10:57
 **/

@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WmsUnitConversionOrderItemDto extends BaseDto {
    /**
    * 主键
    */
    private Integer wmsUnitConversionOrderItemId;

    /**
    * 主单id
    */
    private Integer wmsUnitConversionOrderId;

    /**
    * 来源sku id
    */
    private Integer sourceSkuId;

    /**
    * 来源sku
    */
    private String sourceSkuNo;

    /**
    * 来源sku名
    */
    private String sourceGoodsName;

    /**
    * 来源单位id
    */
    private Integer sourceUnitId;

    /**
    * 来源单位名
    */
    private String sourceUnitName;

    /**
    * 来源转换数量
    */
    private BigDecimal sourceNum;

    /**
    * 来源单价
    */
    private BigDecimal sourcePrice;

    /**
    * wms实际出库数量
    */
    private BigDecimal wmsRealOutNum;

    /**
    * 0未出 1部分出 2全部出
    */
    private Integer outStatus;

    /**
    * 目标sku Id
    */
    private Integer targetSkuId;

    /**
    * 目标sku
    */
    private String targetSkuNo;

    /**
    * 目标sku名
    */
    private String targetGoodsName;

    /**
    * 目标单位id
    */
    private Integer targetUnitId;

    /**
    * 目标单位名
    */
    private String targetUnitName;

    /**
    * 目标转换数量
    */
    private BigDecimal targetNum;

    /**
    * 目标单价
    */
    private BigDecimal targetPrice;

    /**
    * wms实际入库数量
    */
    private BigDecimal wmsRealInNum;

    /**
    * 0未入 1部分入 2全部入
    */
    private Integer inStatus;

    /**
    * 税率
    */
    private Integer taxRate;

    /**
    * 是否删除 0未删除 1删除
    */
    private Integer isDelete;

    /**
     * @组合对象@ 库存数量 合格+近效期
     */
    private Integer stockNum;


}