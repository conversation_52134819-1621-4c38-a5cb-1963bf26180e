package com.vedeng.order.model.pi;

public class CustomerLog {
	
    private Integer customerLogId;

    //二级渠道商代码
    private Integer traderCustomerId;

    //二级渠道商名称
    private String traderName;

    //联系人
    private String traderContactName;

    //电话
    private String mobile;

    //二级渠道商所在地
    private String area;

    private Long addTime;

    //返回信息类型，S为成功，E为报错
    private String status;

    //返回信息内容
    private String message;
    
    private Integer currentCount;

    public Integer getCurrentCount() {
		return currentCount;
	}

	public void setCurrentCount(Integer currentCount) {
		this.currentCount = currentCount;
	}

	public Integer getCustomerLogId() {
        return customerLogId;
    }

    public void setCustomerLogId(Integer customerLogId) {
        this.customerLogId = customerLogId;
    }

    public Integer getTraderCustomerId() {
        return traderCustomerId;
    }

    public void setTraderCustomerId(Integer traderCustomerId) {
        this.traderCustomerId = traderCustomerId;
    }

    public String getTraderName() {
        return traderName;
    }

    public void setTraderName(String traderName) {
        this.traderName = traderName == null ? null : traderName.trim();
    }

    public String getTraderContactName() {
        return traderContactName;
    }

    public void setTraderContactName(String traderContactName) {
        this.traderContactName = traderContactName == null ? null : traderContactName.trim();
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile == null ? null : mobile.trim();
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area == null ? null : area.trim();
    }

    public Long getAddTime() {
        return addTime;
    }

    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message == null ? null : message.trim();
    }
}
