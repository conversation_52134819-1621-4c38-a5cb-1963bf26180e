package com.vedeng.goods.model.vo;

import java.io.Serializable;
import java.util.List;

/**
 * SKU报备信息视图
 *
 * <AUTHOR>
 * @date 2020/9/24 14:24:16
 */
public class SkuAuthorizationVo implements Serializable {

    /**
     * SKU
     */
    private Integer skuId;

    /**
     * SKU报备信息明细
     */
    private List<SkuAuthorizationItemVo> skuAuthorizationItemVoList;


    public SkuAuthorizationVo() {
    }

    public SkuAuthorizationVo(Integer skuId, List<SkuAuthorizationItemVo> skuAuthorizationItemVoList) {
        this.skuId = skuId;
        this.skuAuthorizationItemVoList = skuAuthorizationItemVoList;
    }

    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    public List<SkuAuthorizationItemVo> getSkuAuthorizationItemVoList() {
        return skuAuthorizationItemVoList;
    }

    public void setSkuAuthorizationItemVoList(List<SkuAuthorizationItemVo> skuAuthorizationItemVoList) {
        this.skuAuthorizationItemVoList = skuAuthorizationItemVoList;
    }
}
