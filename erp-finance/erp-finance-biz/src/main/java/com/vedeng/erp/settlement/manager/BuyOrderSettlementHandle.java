package com.vedeng.erp.settlement.manager;

import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.enums.SupplierAssetEnum;
import com.vedeng.common.core.enums.TraderTypeEnum;
import com.vedeng.erp.buyorder.dto.BuyOrderApiDto;
import com.vedeng.erp.buyorder.dto.BuyorderGoodsApiDto;
import com.vedeng.erp.buyorder.service.BuyorderApiService;
import com.vedeng.erp.finance.cmd.SettlementBillCreateCmd;
import com.vedeng.erp.finance.dto.CapitalBillDetailDto;
import com.vedeng.erp.finance.dto.CapitalBillDto;
import com.vedeng.erp.settlement.domain.context.SettlementBillSettleContext;
import com.vedeng.erp.settlement.domain.dto.SettlementBillDto;
import com.vedeng.erp.settlement.domain.dto.SettlementBillItemDto;
import com.vedeng.erp.settlement.enums.CapitalBusinessTypeEnum;
import com.vedeng.erp.settlement.enums.CapitalOrderTypeEnum;
import com.vedeng.erp.settlement.enums.CapitalTraderModeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购结算单
 * @date 2023/12/7 17:41
 */
@Component
@Slf4j
public class BuyOrderSettlementHandle extends AbstractSettlementHandle {

    @Autowired
    BuyorderApiService buyorderApiService;


    @Override
    protected SettlementBillDto preCreate(SettlementBillCreateCmd settlementBillCreateCmd) {
        // 结算主单
        SettlementBillDto settlementBillDto = new SettlementBillDto();
        BuyOrderApiDto buyOrderApiDto = buyorderApiService.getBuyorderByBuyorderId(settlementBillCreateCmd.getBusinessSourceId());
        settlementBillDto.setTraderId(buyOrderApiDto.getTraderId());
        settlementBillDto.setTraderSubjectId(buyOrderApiDto.getTraderSupplierId());
        settlementBillDto.setTraderSubjectType(ErpConstant.TWO);
        settlementBillDto.setSourceType(settlementBillCreateCmd.getSourceTypeEnum().getCode());
        settlementBillDto.setSettleAmount(buyOrderApiDto.getTotalAmount());
        settlementBillDto.setAlreadySettleAmount(BigDecimal.ZERO);
        settlementBillDto.setAccountPeriod(buyOrderApiDto.getPeriodDay());
        settlementBillDto.setAccountPeriodAmount(buyOrderApiDto.getAccountPeriodAmount());
        settlementBillDto.setAlreadyRepaidAmount(BigDecimal.ZERO);
        settlementBillDto.setBusinessSourceTypeId(buyOrderApiDto.getBuyorderId());
        settlementBillDto.setBusinessSourceTypeNo(buyOrderApiDto.getBuyorderNo());
        // 生成结算明细单
        List<BuyorderGoodsApiDto> buyorderGoodsApiDtos = buyOrderApiDto.getBuyorderGoodsApiDtos();
        buyorderGoodsApiDtos = buyorderGoodsApiDtos.stream().filter(dto -> dto.getRebateAmount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        List<SettlementBillItemDto> settlementBillItemDtoList = new ArrayList<>();
        settlementBillDto.setSettlementBillItemDtoList(settlementBillItemDtoList);

        buyorderGoodsApiDtos.forEach(dto -> {
            SettlementBillItemDto settlementBillItemDto = new SettlementBillItemDto();
            settlementBillItemDto.setBusinessType(settlementBillCreateCmd.getSourceTypeEnum().getCode());
            settlementBillItemDto.setBusinessId(buyOrderApiDto.getBuyorderId());
            settlementBillItemDto.setBusinessNo(buyOrderApiDto.getBuyorderNo());
            settlementBillItemDto.setBusinessItemId(dto.getBuyorderGoodsId());
            settlementBillItemDto.setProductName(dto.getGoodsName());
            settlementBillItemDto.setPrice(dto.getRebatePrice());
            settlementBillItemDto.setAmount(dto.getRebateAmount());
            settlementBillItemDto.setNumber(BigDecimal.valueOf(dto.getNum()));
            settlementBillItemDto.setTraderDirection(TraderTypeEnum.rollOut.getType());
            settlementBillItemDto.setSettlementType(SupplierAssetEnum.rebate.getCode());
            settlementBillItemDtoList.add(settlementBillItemDto);
        });

        return settlementBillDto;
    }

    @Override
    protected void postCreate(SettlementBillDto settlementBillDto) {

    }

    @Override
    protected BigDecimal buildPayAmount(SettlementBillSettleContext context) {
        return context.getSettlementBillDto().getSettlementBillItemDtoList().stream().map(SettlementBillItemDto::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    protected void postSettlement(SettlementBillSettleContext context) {

    }

    @Override
    protected void buildCapitalBill(SettlementBillSettleContext context) {
        CapitalBillDto capitalBillDto = context.getCapitalBillDto();
        capitalBillDto.setTraderMode(CapitalTraderModeEnum.REBATE.getCode());
        CapitalBillDetailDto capitalBillDetailDto = capitalBillDto.getCapitalBillDetailDto();
        capitalBillDetailDto.setBussinessType(CapitalBusinessTypeEnum.ORDER_PAYMENT.getCode());
        capitalBillDetailDto.setOrderType(CapitalOrderTypeEnum.BUY_ORDER.getCode());
    }


}
