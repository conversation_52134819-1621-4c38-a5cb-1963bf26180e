package com.vedeng.crm.business.business.domain.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class SaleorderDto implements Serializable {
    private static final long serialVersionUID = 1L;

    // Basic order information
    private Boolean onlineFlag;
    private Integer saleorderId;
    private String elSaleordreNo;
    private Integer quoteorderId;
    private Integer parentId;
    private String saleorderNo;
    private String msaleorderNo;
    private Integer productBelongUserId;
    private Integer orderType;
    private Integer isCustomerArrival;
    private Integer source;
    private Integer orgId;
    private String orgName;
    private Integer userId;
    private Integer validStatus;
    private Long validTime;
    private Integer status;
    private Integer purchaseStatus;
    private Integer lockedStatus;
    private Integer invoiceStatus;
    private Long invoiceTime;
    private Integer paymentStatus;
    private Long paymentTime;
    private Integer deliveryStatus;
    private Long deliveryTime;
    private Integer arrivalStatus;
    private Long arrivalTime;
    private Integer serviceStatus;
    private Integer haveAccountPeriod;
    private BigDecimal totalAmount;
    private Integer isNew;
    private String typef;
    private String isSendSms;
    private String searchBeginDate;
    private String searchEndDate;
    private Integer syncType;
    private String originBuyOrderNo;

    // Trader/customer information
    private Integer traderId;
    private Integer customerType;
    private Integer customerNature;
    private String traderName;
    private Integer traderContactId;
    private String traderContactName;
    private String groupContactPositions;
    private String traderContactMobile;
    private String traderContactTelephone;
    private Integer traderAddressId;
    private String traderAddress;
    private String traderComments;
    private Integer takeTraderId;
    private String takeTraderName;
    private Integer takeTraderContactId;
    private String takeTraderContactName;
    private String takeTraderContactMobile;
    private String takeTraderContactTelephone;
    private Integer takeTraderAddressId;
    private String takeTraderAddress;
    private Integer isSendInvoice;
    private Integer invoiceTraderId;
    private String invoiceTraderName;
    private Integer invoiceTraderContactId;
    private String invoiceTraderContactName;
    private String invoiceTraderContactMobile;
    private String invoiceTraderContactTelephone;
    private String invoiceTraderContactEmail;
    private Integer invoiceTraderAddressId;
    private String invoiceTraderAddress;
    private Integer salesAreaId;
    private String salesArea;
    private Integer terminalTraderId;
    private String terminalTraderName;
    private Integer terminalTraderType;
    private Integer terminalTraderNature;
    private Integer groupCustomerId;
    private String groupContactName;
    private Integer traderStatus;
    private Integer traderSubject;

    // Payment information
    private Integer paymentType;
    private BigDecimal prepaidAmount;
    private BigDecimal accountPeriodAmount;
    private Integer periodDay;
    private Integer logisticsCollection;
    private BigDecimal retainageAmount;
    private Integer retainageAmountMonth;
    private String paymentComments;
    private BigDecimal retentionMoney;
    private Integer retentionMoneyDay;
    private Integer payType;
    private Integer paymentMode;
    private BigDecimal couponAmount;
    private Integer couponType;
    private Integer billPeriodSettlementType;

    // Order details
    private String additionalClause;
    private String logisticsComments;
    private String financeComments;
    private String comments;
    private String batchNoComments;
    private String invoiceComments;
    private Integer deliveryDirect;
    private String supplierClause;
    private Integer haveAdvancePurchase;
    private Integer isUrgent;
    private BigDecimal urgentAmount;
    private Integer haveCommunicate;
    private String prepareComments;
    private String marketingPlan;
    private Integer statusComments;
    private Integer syncStatus;
    private Long addTime;
    private Integer creator;
    private Long satisfyInvoiceTime;
    private Long satisfyDeliveryTime;
    private String creatorName;
    private Long modTime;
    private Integer updater;
    private String traderArea;
    private String takeTraderArea;
    private String invoiceTraderArea;
    private Integer traderAreaId;
    private Integer takeTraderAreaId;
    private Integer invoiceTraderAreaId;
    private Integer invoiceType;
    private Integer invoiceMethod;
    private Integer freightDescription;
    private Integer deliveryType;
    private Integer logisticsId;
    private Integer invoiceSendNode;
    private Integer deliveryMethod;
    private Integer isSameAddress;
    private Integer deliveryClaim;
    private Long deliveryDelayTime;
    private String deliveryDelayTimeStr;

    // Product information
    private List<Integer> keyIds;
    private String traderContact;
    private String salesName;
    private String salesDeptId;
    private String salesDeptName;
    private List<String> salesDeptUser;
    private Integer communicateNum;
    private String sku;
    private String goodsName;
    private Integer goodsId;
    private String brandName;
    private String model;
    private Integer searchDateType;
    private Long searchBegintime;
    private Long searchEndtime;
    private String quoteorderNo;
    private Integer bussinessChanceId;
    private String bussinessChanceNo;
    private String customerTypeStr;
    private String customerNatureStr;
    private String customerLevelStr;
    private String terminalTraderTypeStr;
    private Integer isContractReturn;
    private Integer isDeliveryOrderReturn;
    private List<Integer> traderIdList;
    private Integer optUserId;
    private String optUserName;
    private String materialCode;
    private String optor;
    private String optType;
    private String flag = "0";
    private String show;
    private String pickNums;
    private Long addTimejh;
    private Integer creatorjh;
    private Long modTimejh;
    private Integer updaterjh;
    private Integer companyId;
    private String companyName;
    private String idCnt;
    private String isSM = "0";
    private String isOut = "0";
    private int isSearchCount = 0;
    private BigDecimal allTotalAmount;
    private Integer allNum;
    private Integer allDeliveryNum;
    private BigDecimal receivedAmount;
    private Integer searchType;
    private BigDecimal startAmount;
    private BigDecimal endAmount;
    private BigDecimal residueAmount;
    private String eFlag = "0";
    private String outGoodsTime;
    private String search;
    private Integer isPayment;
    private Integer isWeiXin;
    private BigDecimal paymentAmount;
    private Integer bussinessId;
    private Integer bussinessType = 0;
    private Integer orderId;
    private String bussinessNo;
    private BigDecimal accountPayable;
    private Integer isOpenInvoice;
    private Integer shType;
    private Integer isOverSettlementPrice;
    private Integer overLimit;
    private Integer verifiesType;
    private String verifyUsername;
    private Integer verifyStatus;
    private Integer contractStatus;
    private List<String> verifyUsernameList;
    private String addTimeStr;
    private String validTimeStr;
    private String invoiceTypeStr;
    private Integer purchase;
    private Integer advancePurchaseStatus;
    private String advancePurchaseComments;
    private Long advancePurchaseTime;
    private List<Integer> invoiceId;
    private BigDecimal realAmount;
    private BigDecimal realTotalAmount;
    private Integer saleorderModifyApplyId;
    private String saleorderModifyApplyNo;
    private String extraType;
    private String regAddress;
    private Integer isAftersale = 0;
    private String creatorOrgName;
    private Integer creatorOrgId;
    private Integer validOrgId;
    private String validOrgName;
    private Integer validUserId;
    private Integer accountPeriod;
    private Long endTime;
    private Integer isSaleOut = 0;
    private Integer openInvoiceApply;
    private Integer isCloseSale;
    private Integer goodsType;
    private Integer isDelayInvoice;
    private Integer isHaveInvoiceApply;
    private Integer isHaveInvoice;
    private String lockedReason;
    private Integer isSalesPerformance;
    private Long salesPerformanceTime;
    private Integer isLocked;
    private Integer hasReturnMoney;
    private Long salesPerformanceModTime;
    private BigDecimal fiveTotalAmount;
    private Integer reqType;
    private String logisticsName;
    private String logisticsNo;
    private String phoneNo;
    private String costUserIds;
    private List<String> costUserIdsList;
    private Integer isReferenceCostPrice;
    private String invoiceEmail;
    private Integer ownerUserId;
    private String ownerUserName;
    private Integer webAccountId;
    private Integer ssoAccountId;
    private String userName;
    private BigDecimal couponMoney;
    private BigDecimal originalAmount;
    private Integer type;
    private Integer expressId;
    private List<Integer> expressIds;
    private Integer isPrintout;
    private Integer outIsFlag;
    private Integer bhVerifyStatus;
    private Long searchPaymentBeginTime;
    private Long searchPaymentEndTime;
    private Integer loginUserBelongToProductManager = 0;
    private Integer autoAudit;
    private Integer isRisk;
    private String riskComments;
    private Long riskTime;
    private String riskKey;
    private BigDecimal realPayAmount;
    private Integer isHandMatch;
    private Integer capitalBillId;
    private BigDecimal capitalBillAmount;
    private Integer operateType;
    private Integer consultStatus;
    private String contractUrl;
    private String contractNoStampUrl;
    private String componentHtml;
    private Integer isCopy;
    private String batchNoCommentArray;
    private Boolean needShowConfirm;
    private String orderTestContent;
    private String reasonNo;
    private Integer isPrint;
    private Integer isIncludePrice;
    private Date updateDataTime;
    private String isConfirmTime;
    private Integer componentId;
    private List<Integer> userIdList;
    private Integer isAdvance;
    private Integer advanceValidStatus;
    private Integer prepareReaseonType;
    private Integer meetInvoiceConditions;
    private String saleorderGoodsIds;
    private Boolean phtxFlag;
    private Integer sendToPc;
    private Integer lendOutId;
    private String createMobile;
    private String bdtraderComments;
    private String closeComments;
    private Long bdMobileTime;
    private Long webTakeDeliveryTime;
    private List<Integer> orgIdList;
    private List<String> createMobileList;
    private List<String> orderNos;
    private Date confirmTime;
    private Integer confirmStatus;
    private Integer orderStreamStatus;
    private Integer isCalInvoiceGoods;
    private Integer confirmationFormUpload;
    private Integer confirmationFormAudit;
    private Long confirmationSubmitTime;
    private Integer wheatherJdOrder;

}
