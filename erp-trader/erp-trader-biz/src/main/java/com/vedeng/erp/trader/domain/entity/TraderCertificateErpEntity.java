package com.vedeng.erp.trader.domain.entity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;


/**
 * @description 交易者资质
 * <AUTHOR>
 * @date 2024/2/4 14:31
 **/

@Getter
@Setter
@ToString
@NoArgsConstructor
public class TraderCertificateErpEntity {
    /**
     * 资质ID
     */
    private Integer traderCertificateId;

    /**
     * 交易者ID
     */
    private Integer traderId;

    /**
     * scope :1001
     */
    private Integer sysOptionDefinitionId;

    /**
     * 是否含有医疗器械 0不含 1含
     */
    private Integer isMedical;

    /**
     * 证书开始时间
     */
    private Long begintime;

    /**
     * 证书结束时间
     */
    private Long endtime;

    /**
     * 证书编号
     */
    private String sn;

    /**
     * 附件名称
     */
    private String name;

    /**
     * 证书域名
     */
    private String domain;

    /**
     * 文件地址
     */
    private String uri;

    /**
     * 扩展信息
     */
    private String extra;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 添加人
     */
    private Integer creator;

    /**
     * 最近一次编辑时间
     */
    private Long modTime;

    /**
     * 最近一次编辑人
     */
    private Integer updater;

    /**
     * 供应商资质[授权销售人-职位]
     */
    private String authPost;

    /**
     * 供应商资质[授权销售人-姓名]
     */
    private String authUsername;

    /**
     * 供应商资质[授权销售人-联系方式]
     */
    private String authContactinfo;

    /**
     * OSS的唯一id
     */
    private String ossResourceId;

    /**
     * 原始的文件路径
     */
    private String originalFilepath;

    /**
     * 同步标志:0-未同步 1-同步成功 2-同步失败
     */
    private Integer synSuccess;

    /**
     * 耗时
     */
    private Long costTime;

    /**
     * 备案号
     */
    private String recordNo;

    /**
     * 是否删除0否1是
     */
    private Integer isDelete;

    /**
     * 发证日期
     */
    private Long issueDate;

    /**
     * 文件后缀
     */
    private String suffix;

    /**
     * 所属类型 1::经销商（包含终端）2:供应商
     */
    private Integer traderType;

    /**
     * 关联主表字段ID
     */
    private Integer relatedId;
}