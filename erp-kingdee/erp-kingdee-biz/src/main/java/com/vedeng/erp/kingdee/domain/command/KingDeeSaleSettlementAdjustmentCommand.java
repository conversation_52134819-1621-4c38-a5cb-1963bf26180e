package com.vedeng.erp.kingdee.domain.command;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @description: 销售调整单接收参数
 * @author: <PERSON>qin
 * @date: 2023/2/23 13:23
 **/
@Getter
@Setter
public class KingDeeSaleSettlementAdjustmentCommand {
    /**
     * 单据内码
     */
    private Integer FID;
    /**
     * 单关联金蝶销售出库单表头ID
     */
    private Integer F_QZOK_JDXSCKDID;
    /**
     * 调整日期 yyyy-MM-dd
     */
    private String F_QZOK_Date;

    /**
     * 贝登单据头ID
     */
    private String F_QZOK_BDDJTID;

    private String FBillNo;

    /**
     * fEntity
     */
    private List<KingDeeSaleSettlementAdjustmentEntityCommand> FEntity = new ArrayList<>();

    @Getter
    @Setter
    public static class KingDeeSaleSettlementAdjustmentEntityCommand {
        /**
         * 归属业务单号
         */
        private String F_QZOK_GSYWDH;
        /**
         * 原始订单号
         */
        private String F_QZOK_YSDDH;
        /**
         * 业务类型
         */
        private String F_QZOK_YWLX;
        /**
         * 商品代码
         */
        private String F_QZOK_SPDM;
        /**
         * 商品名称
         */
        private String F_QZOK_SPMC;
        /**
         * 批次号
         */
        private String F_QZOK_PCH;
        /**
         * SN码
         */
        private String F_QZOK_SN;
        /**
         * 贝登单据行ID
         */
        private Integer F_QZOK_BDDJHID;
        /**
         * 关联金蝶销售出库单行ID
         */
        private Integer F_QZOK_JDCKDEntryid;
        /**
         * 调整金额
         */
        private BigDecimal F_QZOK_TZJE;
        /**
         * 调整税额
         */
        private BigDecimal F_QZOK_TZSE;
        /**
         * 调整价税合计
         */
        private BigDecimal F_QZOK_TZJSHJ;
    }
}
