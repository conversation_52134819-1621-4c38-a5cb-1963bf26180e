function zhongzhi() {
	checkLogin();
	var comments = $('#comment').val().length;
	if (comments==0){
		layer.confirm("终止理由不能为空", {
			btn : [ '确定']
			//按钮
		}, function() {
			layer.closeAll();
		});
		return;
	}

	$.ajax({
		type: "POST",
		url: page_url + "/order/quote/authorizationTermination.do",
		data: $('#zzform').serialize(),
		dataType: 'json',
		success: function (data) {
			if (data.code == -1) {
				layer.alert(data.message);
				return false;
			}

			if (data.code == 0){
				window.parent.location.reload();
			}
		},
		error: function (data) {


			if (data.status == 1001) {
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http")) == true ? (new URL(this.url).pathname) : this.url));
			}
		}
	});

}

function zuofei() {
	checkLogin();
	var comments = $('#comment').val().length;
	if (comments==0){
		layer.confirm("作废理由不能为空", {
			btn : [ '确定']
			//按钮
		}, function() {
			layer.closeAll();
		});
		return;
	}
	$.ajax({
		type : "POST",
		url : page_url+"/order/quote/authorizationAbandon.do",
		data: $('#zfform').serialize(),
		dataType : 'json',
		success : function(data) {
			window.parent.location.reload();
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});

}

function pageShenhe(authorizationApplyId,formToken,checker,canCheck){
	checkLogin();
	var content="当前处理人是$"+checker+"$,你无权操作审核"
	if(canCheck==0){
		layer.alert(content)
		return
	}

	var frontPageId = $(window.parent.document).find('.active').eq(1).attr('id');
	var id = 'authorizationapply_' + authorizationApplyId;
	var uri = page_url+"/order/quote/authorizationExamine.do?authorizationApplyId=" + authorizationApplyId;
	var closable = 1;
	var item = { 'id': id, 'name': '授权书详情', 'url': uri, 'closable': closable == 1 ? true : false };
	if (typeof(self.parent.closableTab) != 'undefined') {
		self.parent.closableTab.addTab(item);
		self.parent.closableTab.resizeMove();
		$(window.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
	}else{
		try{
			var uniqueName = uri.replace('./', '').replace(/["&'./:=%?[\]]/gi, '-').replace(/(--)/gi, '');
			window.parent.postMessage({
				from:'ez',
				name: title,
				url:uri,
				id:"tab-"+uniqueName
			}, '*');
		}catch (e){}
	}
}

function shenhe(authorizationApplyId,formToken,checker,canCheck) {
	checkLogin();
	var content="当前处理人是$"+checker+"$,你无权操作审核"
	if(canCheck==0){
		layer.alert(content)
		return
	}
	$.ajax({
		type : "POST",
		url : page_url+"/order/quote/authorizationShenhe.do",
		data :{'authorizationApplyId':authorizationApplyId,'formToken':formToken},
		dataType : 'json',
		success : function(data) {
			if(data.data){
				window.location.reload();
			}else{
				layer.confirm(data.message, {
					btn : [ '确定' ]
					//按钮
				}, function() {
					layer.closeAll();
					window.location.reload();
				}, function() {
				});
			}
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});

}

function tiaozhuan(authorizationApplyId, quoteorderId, applyStatus) {
	url = page_url + '/order/quote/authorizationView.do?authorizationApplyId=' + authorizationApplyId + '&quoteorderId=' + quoteorderId;
	$("#authorizationIndex").attr('tabTitle', '{"num":"authorizationIndex' + authorizationApplyId + '","title":"授权书详情","link":"' + url + '"}');
	$("#authorizationIndex").click();
}

function uploadFile(obj,num){
	checkLogin();
	var imgPath = $(obj).val();
	if(imgPath == '' || imgPath == undefined){
		return false;
	}
	var oldName=imgPath.substr(imgPath.lastIndexOf('\\')+1);
	//判断上传文件的后缀名
	var strExtension = imgPath.substr(imgPath.lastIndexOf('.') + 1);
	if (strExtension != 'jpg'&& strExtension != 'gif' && strExtension != 'png' && strExtension != 'pdf' && strExtension != 'doc' && strExtension != 'docx' && strExtension != 'xls' && strExtension != 'xlsx') {
		$('#upload1').css("display","");
		return false;
	}else{
		$('#upload1').css("display","none");
	}
	var fileSize = 0;
	var isIE = /msie/i.test(navigator.userAgent) && !window.opera;
	if (isIE && !obj.files) {
		var filePath = obj.value;
		var fileSystem = new ActiveXObject("Scripting.FileSystemObject");
		var file = fileSystem.GetFile (filePath);
		fileSize = file.Size;
	}else {
		fileSize = obj.files[0].size;
	}
	fileSize=Math.round(fileSize/1024*100)/100; //单位为KB
	if(fileSize>20480){
		layer.alert("图片文件大小应为20MB以内",{ icon: 2 });
		return false;
	}
	var domain = $("#domain").val();
	$.ajaxFileUpload({
		url : page_url + '/fileUpload/ajaxFileUpload.do', //用于文件上传的服务器端请求地址
		secureuri : false, //一般设置为false
		fileElementId : $(obj).attr("id"), //文件上传控件的id属性  <input type="file" id="file" name="file" /> 注意，这里一定要有name值   //$("form").serialize(),表单序列化。指把所有元素的ID，NAME 等全部发过去
		dataType : 'json',//返回值类型 一般设置为json
		complete : function() {//只要完成即执行，最后执行
		},
		//服务器成功响应处理函数
		success : function(data) {
			if (data.code == 0) {
				$("#name_"+num ).val(oldName);
				$("#uri_"+num ).val(data.filePath);
			} else {
				layer.alert(data.message);
			}
		},
		//服务器响应失败处理函数
		error : function(data, status, e) {
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}else{
				layer.alert(data.responseText);
			}

		}
	});

}