package com.vedeng.infrastructure.kingdee.common.sdk.entity;


/* loaded from: k3cloud-webapi-sdk7.9.2.jar:com/kingdee/bos/webapi/entity/QueryRequestBodyObject.class */
public class QueryRequestBodyObject extends RequestBodyObject {
    String beginmethod = ConstDefine.BEGINMETHOD_Method;
    String querymethod = ConstDefine.QUERYMETHOD_Method;

    public QueryRequestBodyObject(Object[] parameters) {
        super(parameters);
    }

    public String getBeginmethod() {
        return this.beginmethod;
    }

    public void setBeginmethod(String beginmethod) {
        this.beginmethod = beginmethod;
    }

    public String getQuerymethod() {
        return this.querymethod;
    }

    public void setQuerymethod(String querymethod) {
        this.querymethod = querymethod;
    }
}
