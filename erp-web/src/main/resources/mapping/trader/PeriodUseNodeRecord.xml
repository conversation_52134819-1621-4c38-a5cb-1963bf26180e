<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.trader.dao.PeriodUseNodeRecordMapper">

    <insert id="insertRecord" parameterType="com.vedeng.trader.model.po.PeriodUseNodeRecordPo" useGeneratedKeys="true" keyProperty="recordId">
        insert into T_PERIOD_USE_NODE_RECORD
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="orderType != null" >
                ORDER_TYPE,
            </if>
            <if test="orderId != null" >
                ORDER_ID,
            </if>
            <if test="relatedId != null" >
                RELATED_ID,
            </if>
            <if test="amount != null" >
                AMOUNT,
            </if>
            <if test="handlingStatus != null" >
                HANDLING_STATUS,
            </if>
            <if test="addTime != null" >
                ADD_TIME,
            </if>
            <if test="creator != null" >
                CREATOR,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="orderType != null" >
                #{orderType,jdbcType=INTEGER},
            </if>
            <if test="orderId != null" >
                #{orderId,jdbcType=BIGINT},
            </if>
            <if test="relatedId != null" >
                #{relatedId,jdbcType=BIGINT},
            </if>
            <if test="amount != null" >
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="handlingStatus != null" >
                #{handlingStatus,jdbcType=INTEGER},
            </if>
            <if test="addTime != null" >
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null" >
                #{creator,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>


    <select id="getUnHandledRecord" resultType="com.vedeng.trader.model.po.PeriodUseNodeRecordPo">
        SELECT
            *
        FROM
            T_PERIOD_USE_NODE_RECORD
        WHERE
            HANDLING_STATUS = 0
    </select>

    <update id="updateHandleStatusByRecordId">
        UPDATE T_PERIOD_USE_NODE_RECORD
        SET HANDLING_STATUS = 1
        WHERE
            RECORD_ID = #{recordId,jdbcType=BIGINT}
    </update>

</mapper>