package com.wms.service.chain.step;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.util.DateUtil;
import com.vedeng.logistics.model.WarehouseStock;
import com.vedeng.logistics.service.WarehouseStockService;
import com.wms.constant.LogicalEnum;
import com.wms.constant.VerifyStatusEnum;
import com.wms.constant.WmsLogicalOperateTypeEnum;
import com.wms.dao.WmsLogicalOrdergoodsMapper;
import com.wms.dao.WmsOutputOrderGoodsMapper;
import com.wms.dao.WmsOutputOrderMapper;
import com.wms.dto.StockAllocationResult;
import com.wms.model.po.WmsLogicalOrdergoods;
import com.wms.model.po.WmsOutputOrder;
import com.wms.model.po.WmsOutputOrderGoods;
import com.wms.service.chain.AbstractHandlerStep;
import com.wms.service.chain.HandlerStepContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 样品出库 逻辑单处理
 */
@Service
public class SampleOutOrderHandler extends AbstractHandlerStep {

    private static final Logger LOGGER = LoggerFactory.getLogger(SampleOutOrderHandler.class);

    @Resource
    private WmsLogicalOrdergoodsMapper wmsLogicalOrdergoodsMapper;

    @Autowired
    private WmsOutputOrderMapper outputOrderMapper;

    @Autowired
    private WmsOutputOrderGoodsMapper outputOrderGoodsMapper;

    @Autowired
    private WarehouseStockService warehouseStockService;

    @Override
    protected void doDealWith(HandlerStepContext context) throws Exception {

        Long sampleOutOrderId = context.get("sampleOutOrderId");


        //更新出库单
        WmsOutputOrder updateOrder = new WmsOutputOrder();
        updateOrder.setId(sampleOutOrderId);
        updateOrder.setVerifyStatus(VerifyStatusEnum.Approved.getValue());

        String updateTime = DateUtil.convertString(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss");
        updateOrder.setUpdateTime(updateTime);
        updateOrder.setApprovalTime(updateTime);

        LOGGER.info("样品出库单下发wms 更新样品出库单:" + sampleOutOrderId + ",的订单数据=====================:" + JSON.toJSONString(updateOrder));
        outputOrderMapper.updateByPrimaryKeySelective(updateOrder);

        List<WmsOutputOrderGoods> outputOrderGoodList = this.outputOrderGoodsMapper.queryOutputGoodsByLendOutId(sampleOutOrderId);

        context.put("outputOrderGoodList", outputOrderGoodList);

        //库存分配结果
        List<StockAllocationResult> allocationResultList = new ArrayList<>();


        Map<String, WarehouseStock> logicalStockMapInfo = warehouseStockService.getLogicalStockMapInfo(outputOrderGoodList.stream().map(WmsOutputOrderGoods::getSkuNo).collect(Collectors.toList()));
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        for (WmsOutputOrderGoods wmsOutputOrderGood : outputOrderGoodList) {
            WarehouseStock warehouseStock = logicalStockMapInfo.get(wmsOutputOrderGood.getSkuNo() + LogicalEnum.JXQ.getLogicalWarehouseId());
            int stockNum = Objects.isNull(warehouseStock) ? 0 : warehouseStock.getAvailableStockNum() < 0 ? 0 : warehouseStock.getAvailableStockNum();
            if (wmsOutputOrderGood.getOutputNum() <= stockNum) {
                StockAllocationResult stockAllocationResult = new StockAllocationResult();
                stockAllocationResult.setSku(wmsOutputOrderGood.getSkuNo());
                stockAllocationResult.setLogicalWarehouseId(LogicalEnum.JXQ.getLogicalWarehouseId());
                stockAllocationResult.setNum(wmsOutputOrderGood.getOutputNum());
                allocationResultList.add(stockAllocationResult);
            } else {
                // 剩余的时候其他数量给合格仓
                warehouseStock.setAvailableStockNum(0);
                if (stockNum > 0) {
                    StockAllocationResult stockAllocationResult = new StockAllocationResult();
                    stockAllocationResult.setSku(wmsOutputOrderGood.getSkuNo());
                    stockAllocationResult.setLogicalWarehouseId(LogicalEnum.JXQ.getLogicalWarehouseId());
                    stockAllocationResult.setNum(stockNum);
                    allocationResultList.add(stockAllocationResult);
                }

                int hgNum = wmsOutputOrderGood.getOutputNum() - stockNum;

                StockAllocationResult stockAllocationResult = new StockAllocationResult();
                stockAllocationResult.setSku(wmsOutputOrderGood.getSkuNo());
                stockAllocationResult.setLogicalWarehouseId(LogicalEnum.HG.getLogicalWarehouseId());
                stockAllocationResult.setNum(hgNum);
                allocationResultList.add(stockAllocationResult);
            }
        }

        LOGGER.info("样品出库单下发wms 样品出库单单:" + sampleOutOrderId + ",的库存分配结果=====================:" + JSON.toJSONString(allocationResultList));
        context.put("allocationResultList", allocationResultList);


        allocationResultList.forEach(allocationResult -> {

            //新增逻辑订单表数据
            WmsLogicalOrdergoods wmsLogicalOrdergoods = new WmsLogicalOrdergoods();
            Long realateId = getRelateId(outputOrderGoodList, allocationResult.getSku());

            wmsLogicalOrdergoods.setRelatedId(realateId.intValue());
            wmsLogicalOrdergoods.setOperateType(WmsLogicalOperateTypeEnum.SAMPLE_OUT.getOperateTypeCode());
            wmsLogicalOrdergoods.setSku(allocationResult.getSku());
            wmsLogicalOrdergoods.setGoodsId(Integer.valueOf(allocationResult.getSku().substring(1)));
            wmsLogicalOrdergoods.setNum(allocationResult.getNum());
            wmsLogicalOrdergoods.setLogicalWarehouseId(allocationResult.getLogicalWarehouseId());
            wmsLogicalOrdergoods.setOccupyNum(allocationResult.getNum());
            wmsLogicalOrdergoods.setAddTime(new Date());
            wmsLogicalOrdergoods.setModeTime(new Date());

            LOGGER.info("样品出库单下发wms 新增占用信息:" + sampleOutOrderId + ",新增逻辑订单数据=====================:" + JSON.toJSONString(wmsLogicalOrdergoods));
            wmsLogicalOrdergoodsMapper.insertSelective(wmsLogicalOrdergoods);
        });

    }

    public Long getRelateId(List<WmsOutputOrderGoods> outputOrderGoodList, String sku) {
        return outputOrderGoodList
                .stream()
                .filter(outputOrderGood -> {
                    return outputOrderGood.getSkuNo().equals(sku);
                }).map(outputOrderGood -> outputOrderGood.getId())
                .findFirst()
                .orElse(0L);
    }

}
