package com.vedeng.erp.finance.mapper;


import com.vedeng.erp.finance.domain.entity.TaxClassificationEntity;
import com.vedeng.erp.finance.dto.TaxClassificationDto;
import org.springframework.stereotype.Repository;


import java.util.List;
@Repository
public interface TaxClassificationMapper {

    int deleteByPrimaryKey(Integer taxClassificationId);


    int insert(TaxClassificationEntity row);


    TaxClassificationEntity selectByPrimaryKey(Integer taxClassificationId);

    /**
     * 根据编码查询
     */
    TaxClassificationEntity findByCode(String code);


    List<TaxClassificationEntity> selectAll();

    List<TaxClassificationDto> findByAll(TaxClassificationDto taxClassificationDto);


    int updateByPrimaryKey(TaxClassificationEntity row);
}