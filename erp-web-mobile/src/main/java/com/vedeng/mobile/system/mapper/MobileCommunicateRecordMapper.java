package com.vedeng.mobile.system.mapper;


import com.vedeng.mobile.system.model.CommunicateRecord;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 沟通记录(CommunicateRecord)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-07-12 15:44:47
 */
@Repository("MobileCommunicateRecordMapper")
public interface MobileCommunicateRecordMapper {



    /**
     * 新增数据
     *
     * @param tCommunicateRecord 实例对象
     * @return 影响行数
     */
    int insert(CommunicateRecord tCommunicateRecord);

    /**
     * 新增数据
     *
     * @param communicateRecord 实例对象
     * @return 影响行数
     */
    int insertSelective(CommunicateRecord communicateRecord);


}

