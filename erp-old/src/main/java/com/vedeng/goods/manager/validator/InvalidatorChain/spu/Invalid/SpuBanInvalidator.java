package com.vedeng.goods.manager.validator.InvalidatorChain.spu.Invalid;

import com.google.common.base.CharMatcher;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.exception.InvalidatorChainException;
import com.vedeng.goods.manager.validator.GoodsInvalidatorChain;
import com.vedeng.goods.model.CoreSku;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static com.vedeng.goods.manager.validator.enums.GoodsInvalidatorErrorEnum.SpuBanInvalidatorError;
import static com.vedeng.goods.manager.constants.GoodsValidConstants.*;

/**
 * (3)	所属的所有SKU在所有的平台（除了区域商城平台）上为全部下架状态，或者未推送状态；
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2021/9/1 17:23.
 * @author: <PERSON><PERSON>.
 */
@Service
public class SpuBanInvalidator extends GoodsInvalidatorChain {

    @Resource
    CoreSkuMapper coreSkuMapper;

    @Override
    public void doInvalid() {
        String spuNo = getSpuNo();
        List<String> skuNoList = getSkuNoList();
        if(CollectionUtils.isEmpty(skuNoList)){
            return;
        }
        StringBuffer skus = new StringBuffer();
        for (String skuNo : skuNoList) {
            CoreSku sku = coreSkuMapper.selectBySkuNo(skuNo);
            if(!(NOT_ON_SALE.equals(Integer.valueOf(sku.getOnSale())) || NOT_PUSH_STATUS.equals(Integer.valueOf(sku.getPushStatus())))){
                skus.append(skuNo);
                skus.append("，");
            }
        }
        if(skus.length()>0){
            String skusList = SpuBanInvalidatorError.getMessage().replace("skus", skus.substring(0, skus.length() - 1));
            throw new InvalidatorChainException(SpuBanInvalidatorError.getCode(),skusList);
        }
    }
}
