<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchExpenseAfterSalesInvoiceMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchExpenseAfterSalesInvoiceDto">
    <!--@mbg.generated-->
    <!--@Table T_EXPENSE_AFTER_SALES_INVOICE-->
    <id column="EXPENSE_AFTER_SALES_INVOICE_ID" jdbcType="BIGINT" property="expenseAfterSalesInvoiceId" />
    <result column="EXPENSE_AFTER_SALES_ID" jdbcType="BIGINT" property="expenseAfterSalesId" />
    <result column="BUYORDER_EXPENSE_ITEM_ID" jdbcType="INTEGER" property="buyorderExpenseItemId" />
    <result column="INVOICE_CODE" jdbcType="VARCHAR" property="invoiceCode" />
    <result column="INVOICE_NO" jdbcType="VARCHAR" property="invoiceNo" />
    <result column="SKU" jdbcType="VARCHAR" property="sku" />
    <result column="RETURN_NUM" jdbcType="DECIMAL" property="returnNum" />
    <result column="IS_REFUND_INVOICE" jdbcType="BOOLEAN" property="isRefundInvoice" />
    <result column="RETURN_INVOICE_STATUS" jdbcType="BOOLEAN" property="returnInvoiceStatus" />
    <result column="INVOICE_ID" jdbcType="INTEGER" property="invoiceId" />
    <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    EXPENSE_AFTER_SALES_INVOICE_ID, EXPENSE_AFTER_SALES_ID, BUYORDER_EXPENSE_ITEM_ID, 
    INVOICE_CODE, INVOICE_NO, SKU, RETURN_NUM, IS_REFUND_INVOICE, RETURN_INVOICE_STATUS, 
    INVOICE_ID, IS_DELETE, CREATOR, CREATOR_NAME, MOD_TIME, UPDATER, UPDATER_NAME, REMARK, 
    UPDATE_REMARK, ADD_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_EXPENSE_AFTER_SALES_INVOICE
    where EXPENSE_AFTER_SALES_INVOICE_ID = #{expenseAfterSalesInvoiceId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_EXPENSE_AFTER_SALES_INVOICE
    where EXPENSE_AFTER_SALES_INVOICE_ID = #{expenseAfterSalesInvoiceId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="EXPENSE_AFTER_SALES_INVOICE_ID" keyProperty="expenseAfterSalesInvoiceId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchExpenseAfterSalesInvoiceDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_EXPENSE_AFTER_SALES_INVOICE (EXPENSE_AFTER_SALES_ID, BUYORDER_EXPENSE_ITEM_ID, 
      INVOICE_CODE, INVOICE_NO, SKU, 
      RETURN_NUM, IS_REFUND_INVOICE, RETURN_INVOICE_STATUS, 
      INVOICE_ID, IS_DELETE, CREATOR, 
      CREATOR_NAME, MOD_TIME, UPDATER, 
      UPDATER_NAME, REMARK, UPDATE_REMARK, 
      ADD_TIME)
    values (#{expenseAfterSalesId,jdbcType=BIGINT}, #{buyorderExpenseItemId,jdbcType=INTEGER}, 
      #{invoiceCode,jdbcType=VARCHAR}, #{invoiceNo,jdbcType=VARCHAR}, #{sku,jdbcType=VARCHAR}, 
      #{returnNum,jdbcType=DECIMAL}, #{isRefundInvoice,jdbcType=BOOLEAN}, #{returnInvoiceStatus,jdbcType=BOOLEAN}, 
      #{invoiceId,jdbcType=INTEGER}, #{isDelete,jdbcType=BOOLEAN}, #{creator,jdbcType=INTEGER}, 
      #{creatorName,jdbcType=VARCHAR}, #{modTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER}, 
      #{updaterName,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{updateRemark,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="EXPENSE_AFTER_SALES_INVOICE_ID" keyProperty="expenseAfterSalesInvoiceId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchExpenseAfterSalesInvoiceDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_EXPENSE_AFTER_SALES_INVOICE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="expenseAfterSalesId != null">
        EXPENSE_AFTER_SALES_ID,
      </if>
      <if test="buyorderExpenseItemId != null">
        BUYORDER_EXPENSE_ITEM_ID,
      </if>
      <if test="invoiceCode != null">
        INVOICE_CODE,
      </if>
      <if test="invoiceNo != null">
        INVOICE_NO,
      </if>
      <if test="sku != null">
        SKU,
      </if>
      <if test="returnNum != null">
        RETURN_NUM,
      </if>
      <if test="isRefundInvoice != null">
        IS_REFUND_INVOICE,
      </if>
      <if test="returnInvoiceStatus != null">
        RETURN_INVOICE_STATUS,
      </if>
      <if test="invoiceId != null">
        INVOICE_ID,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="expenseAfterSalesId != null">
        #{expenseAfterSalesId,jdbcType=BIGINT},
      </if>
      <if test="buyorderExpenseItemId != null">
        #{buyorderExpenseItemId,jdbcType=INTEGER},
      </if>
      <if test="invoiceCode != null">
        #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNo != null">
        #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="returnNum != null">
        #{returnNum,jdbcType=DECIMAL},
      </if>
      <if test="isRefundInvoice != null">
        #{isRefundInvoice,jdbcType=BOOLEAN},
      </if>
      <if test="returnInvoiceStatus != null">
        #{returnInvoiceStatus,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceId != null">
        #{invoiceId,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchExpenseAfterSalesInvoiceDto">
    <!--@mbg.generated-->
    update T_EXPENSE_AFTER_SALES_INVOICE
    <set>
      <if test="expenseAfterSalesId != null">
        EXPENSE_AFTER_SALES_ID = #{expenseAfterSalesId,jdbcType=BIGINT},
      </if>
      <if test="buyorderExpenseItemId != null">
        BUYORDER_EXPENSE_ITEM_ID = #{buyorderExpenseItemId,jdbcType=INTEGER},
      </if>
      <if test="invoiceCode != null">
        INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNo != null">
        INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="returnNum != null">
        RETURN_NUM = #{returnNum,jdbcType=DECIMAL},
      </if>
      <if test="isRefundInvoice != null">
        IS_REFUND_INVOICE = #{isRefundInvoice,jdbcType=BOOLEAN},
      </if>
      <if test="returnInvoiceStatus != null">
        RETURN_INVOICE_STATUS = #{returnInvoiceStatus,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceId != null">
        INVOICE_ID = #{invoiceId,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where EXPENSE_AFTER_SALES_INVOICE_ID = #{expenseAfterSalesInvoiceId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchExpenseAfterSalesInvoiceDto">
    <!--@mbg.generated-->
    update T_EXPENSE_AFTER_SALES_INVOICE
    set EXPENSE_AFTER_SALES_ID = #{expenseAfterSalesId,jdbcType=BIGINT},
      BUYORDER_EXPENSE_ITEM_ID = #{buyorderExpenseItemId,jdbcType=INTEGER},
      INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR},
      INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR},
      SKU = #{sku,jdbcType=VARCHAR},
      RETURN_NUM = #{returnNum,jdbcType=DECIMAL},
      IS_REFUND_INVOICE = #{isRefundInvoice,jdbcType=BOOLEAN},
      RETURN_INVOICE_STATUS = #{returnInvoiceStatus,jdbcType=BOOLEAN},
      INVOICE_ID = #{invoiceId,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP}
    where EXPENSE_AFTER_SALES_INVOICE_ID = #{expenseAfterSalesInvoiceId,jdbcType=BIGINT}
  </update>
</mapper>