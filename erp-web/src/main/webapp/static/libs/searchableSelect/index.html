<!DOCTYPE html>
<html>
  <head>
    <meta http-equiv="content-type" content="text/html; charset=utf-8">
    <title>带搜索框的jQuery下拉框美化插件 searchableSelect</title>
    <link href="jquery.searchableSelect.css" rel="stylesheet" type="text/css">
    <script src="jquery-1.11.1.min.js"></script>
    <script src="jquery.searchableSelect.js"></script>
  </head>
  <body>
    <select>
      <option value="jQuery插件库">jQuery插件库</option>
      <option value="BlackBerry">BlackBerry</option>
      <option value="device">device</option>
      <option value="with">with</option>
      <option value="entertainment">entertainment</option>
      <option value="and">and</option>
      <option value="social">social</option>
      <option value="networking">networking</option>
      <option value="apps">apps</option>
      <option value="or">or</option>
      <option value="apps">apps</option>
      <option value="that">that</option>
      <option value="will">will</option>
      <option value="boost">boost</option>
      <option value="your">your</option>
      <option value="productivity">productivity</option>
      <option value="Download">Download</option>
      <option value="or">or</option>
      <option value="buy">buy</option>
      <option value="apps">apps</option>
      <option value="from">from</option>
      <option value="Afbb">Afbb</option>
      <option value="Akademie">Akademie</option>
      <option value="Berlin">Berlin</option>
      <option value="reviews">reviews</option>
      <option value="by">by</option>
      <option value="real">real</option>
    </select>
    <script>
		$(function(){
			$('select').searchableSelect();
		});
    </script>
  </body>
</html>