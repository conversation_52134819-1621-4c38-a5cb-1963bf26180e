package com.vedeng.goods.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 科室信息
 * <AUTHOR>
 */
@Data
public class DepartmentsHospitalDto implements Serializable {
    /**
     * 科室id
     */
    private Integer departmentId;

    /**
     * 科室名称
     */
    private String departmentName;

    /**
     * 科室说明
     */
    private String description;

    /**
     * 删除状态 ： 0 未删除  1 已删除
     */
    private Integer isDelete;

    /**
     * 更新人
     */
    private Integer updater;

    /**
     * 更新时间
     */
    private Long modTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 排序
     */
    private Integer sort;

    private Boolean checked;

    private static final long serialVersionUID = 1L;
}