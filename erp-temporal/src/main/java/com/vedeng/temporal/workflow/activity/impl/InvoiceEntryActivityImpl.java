package com.vedeng.temporal.workflow.activity.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import com.vedeng.temporal.workflow.activity.InvoiceEntryActivity;
import com.vedeng.temporal.workflow.activity.core.UniversalActivityTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 发票录入 Activity 实现类
 *
 * 架构迁移说明：
 * - 从 InvoiceEntryFlow 迁移核心业务逻辑到 Activity 层
 * - 使用 UniversalBusinessTemplate 处理所有业务逻辑：固定参数、API调用、异常处理、通知发送
 * - 每个方法都是独立的 Activity，由 Temporal 管理重试
 * - 直接使用 CompanyBusinessRequest 传递数据，自动解析 JSON
 * - 保持与原 InvoiceEntryFlow 完全一致的业务逻辑和API调用
 *
 * 业务流程：
 * 1. createInvoiceEntry - 执行完整发票录入流程，包含查询详情、录入、审核
 * 2. queryInvoiceDetail - 查询发票详情，用于流程控制
 * 3. approveInvoice - 审核发票，用于流程控制
 * 4. createInvoiceWithDetails - 基于预查询数据创建发票录入
 *
 * 迁移内容：
 * - execute 方法迁移为 createInvoiceEntry 方法
 * - executeInvoiceEntry、executeSubmitApproval、executeApproval 逻辑集成
 * - extractInvoiceId、extractApproveResult 方法保持不变
 * - 保持原有的API路径和参数结构
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0 (架构迁移版本，从 InvoiceEntryFlow 迁移)
 * @since 2025-01-21
 */
@Component
@Slf4j
public class InvoiceEntryActivityImpl implements InvoiceEntryActivity {

    @Autowired
    private UniversalActivityTemplate universalActivityTemplate;



    @Override
    public CompanyBusinessResponse createInvoiceWithDetails(CompanyBusinessRequest request,
                                                            Map<String, Object> invoiceDetailData) {
        request.setUserName("admin");
        // 配置业务操作 - 创建发票
        UniversalActivityTemplate.BusinessOperationConfig config =
                UniversalActivityTemplate.BusinessOperationConfig.create()
                        .operationName("创建发票")
                        .apiPath("/api/v1/receiptInvoice/create.do")
                        .dataPreparer(req -> prepareInvoiceCreateData(req, invoiceDetailData));

        // 直接调用，异常处理由模板统一处理
        CompanyBusinessResponse result = universalActivityTemplate.execute(request, config);
        return result;
    }


    @Override
    public CompanyBusinessResponse approveInvoice(CompanyBusinessRequest request,Map<String, Object> invoiceDetailData) {
        // 配置业务操作 - 审核发票
        UniversalActivityTemplate.BusinessOperationConfig config =
            UniversalActivityTemplate.BusinessOperationConfig.create()
                .operationName("审核发票")
                .apiPath("/api/v1/receiptInvoice/approve.do")
                .dataPreparer(req -> prepareInvoiceApproveData(req, invoiceDetailData));

        // 直接调用，异常处理由模板统一处理
        return universalActivityTemplate.execute(request, config);
    }

    @Override
    public CompanyBusinessResponse updateInvoiceHref(CompanyBusinessRequest request) {
        request.setUserName("admin");
        // 配置业务操作 - 更新发票InvoiceHref字段
        UniversalActivityTemplate.BusinessOperationConfig config =
            UniversalActivityTemplate.BusinessOperationConfig.create()
                .operationName("更新发票InvoiceHref字段")
                .apiPath("/api/v1/receiptInvoice/updateInvoiceHref.do")
                .dataPreparer(req -> prepareInvoiceHrefUpdateData(req));

        // 直接调用，异常处理由模板统一处理
        return universalActivityTemplate.execute(request, config);
    }

    /**
     * 准备发票创建数据
     */
    private Map<String, Object> prepareInvoiceCreateData(CompanyBusinessRequest request,
                                                         Map<String, Object> invoiceDetailData) {
        Map<String, Object> createData = new HashMap<>();

        // 处理与InvoiceEntryFlow完全一致的嵌套数据结构 (第107-111行逻辑)
        Map<String, Object> data = (Map<String, Object>) invoiceDetailData.get("data");
        if (data != null) {
            // 组装录票字段 (第119-121行逻辑)
            createData.put("invoiceNo", data.get("invoiceNo"));
            createData.put("invoiceGoods", data.get("invoiceGoods"));
        }

        // 从扩展属性中获取采购单号
        String buyOrderNo = getBuyOrderNoFromExtendedProperties(request);
        if (buyOrderNo != null) {
            createData.put("buyOrderNo", buyOrderNo);
        }

        log.info("准备发票创建数据完成，业务ID: {},{}公司执行发票创建入参：{}", request.getBusinessId(), request.getTargetCompanyCode(),JSON.toJSON(createData));
        return createData;
    }

    /**
     * 准备发票审核数据
     */
    private Map<String, Object> prepareInvoiceApproveData(CompanyBusinessRequest request, Map<String, Object> invoiceDetailData) {
        Map<String, Object> approveData = new HashMap<>();
        Map<String, Object> data = (Map<String, Object>) invoiceDetailData.get("data");
        if (data != null) {
            // 组装录票字段 (第119-121行逻辑)
            approveData.put("invoiceNo", data.get("invoiceNo"));
        }
        // 从扩展属性中获取采购单号
        String buyOrderNo = getBuyOrderNoFromExtendedProperties(request);
        if (buyOrderNo != null) {
            approveData.put("buyOrderNo", buyOrderNo);
        }
        log.info("准备发票审核数据完成，业务ID: {},入参：{}", request.getBusinessId(), JSON.toJSON(approveData));
        return approveData;
    }


    /**
     * 从扩展属性中获取采购单号（修正为String类型，与InvoiceEntryFlow保持一致）
     */
    private String getBuyOrderNoFromExtendedProperties(CompanyBusinessRequest request) {
        if (request.getExtendedProperties() != null) {
            return (String) request.getExtendedProperties().get("buyOrderNo");
        }
        return null;
    }

    /**
     * 准备发票InvoiceHref更新数据
     */
    private Map<String, Object> prepareInvoiceHrefUpdateData(CompanyBusinessRequest request) {
        Map<String, Object> updateData = new HashMap<>();
        
        // 从业务数据中获取发票号
        Map<String, Object> businessData = parseBusinessData(request.getBusinessData());
        String invoiceNo = (String) businessData.get("invoiceNo");
        
        if (invoiceNo != null) {
            updateData.put("invoiceNo", invoiceNo);
        }
        
        // 从扩展属性中获取采购单号
        String saleOrderNo = (String) request.getExtendedProperties().get("saleOrderNo");

        // 构建API参数
        if (saleOrderNo != null) {
            updateData.put("saleOrderNo", saleOrderNo);
        }
        
        log.info("准备发票InvoiceHref更新数据完成，业务ID: {}, 入参：{}", request.getBusinessId(), JSON.toJSON(updateData));
        return updateData;
    }
    
    /**
     * 解析业务数据 JSON 字符串
     */
    private Map<String, Object> parseBusinessData(String businessDataJson) {
        if (!StringUtils.hasText(businessDataJson)) {
            return new HashMap<>();
        }

        try {
            return JSON.parseObject(businessDataJson, new TypeReference<Map<String, Object>>() {});
        } catch (Exception e) {
            log.warn("解析业务数据JSON失败: {}", businessDataJson, e);
            return new HashMap<>();
        }
    }
}
