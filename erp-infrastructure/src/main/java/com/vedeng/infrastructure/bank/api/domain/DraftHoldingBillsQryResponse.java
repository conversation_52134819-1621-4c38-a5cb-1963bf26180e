package com.vedeng.infrastructure.bank.api.domain;

import com.vedeng.infrastructure.bank.api.domain.base.BankResponse;
import lombok.Data;

import java.util.List;

/**
 * @Author: Patric.Cheng
 * @CreateTime: 2024-09-09
 * @Description: 持有票据查询(B2eNbsDraftHoldingBillsQry)响应实体
 * @Version: 1.0
 */
@Data
public class DraftHoldingBillsQryResponse extends BankResponse {
    private DraftHoldingBillsQryResBody xDataBody;

    @Data
    public static class DraftHoldingBillsQryResBody{
        /**
         * 返回的总条数（全部数据量）
         */
        private Integer total;

        /**
         *银行渠道交易流水号
         */
        private String svrId;

        /**
         *客户技术请求流水号，同一客户请勿重复
         */
        private String trnId;

        /**
         * 记录列表
         */
        private List<MapEntry> list;
    }


    @Data
    public static class MapEntry {
        /** 票据（包）号码 */
        private String billNo;

        /** 票据类型AC01：银票 AC02：商票 */
        private String billType;

        /** 票据介质
         ME01：纸票
         ME02：电票 */
        private String billClass;

        /** 持有人名称 */
        private String hldrName;

        /** 持有人账号 */
        private String hldrAcctNo;

        /** 持有人开户行行号 */
        private String hldrBankNo;

        /** 是否可分包
         0：否
         1：是 */
        private String isAllowSplitBill;

        /** 出票日 */
        private String remitDt;

        /** 票面到期日 */
        private String dueDt;

        /** 票据（包）金额 */
        private String billMoney;

        /** 出票人名称 */
        private String drwrName;

        /** 出票人账号 */
        private String drwrAcctNo;

        /** 出票人账户名称 */
        private String drwrAcctName;

        /** 出票人开户行名称 */
        private String drwrBankName;

        /** 出票人开户行行号 */
        private String drwrBankNo;

        /** 票面收款人名称 */
        private String pyeeName;

        /** 票面收款人账号名称 */
        private String pyeeAcctNo;

        /** 票面收款人开户行名称 */
        private String pyeeBankName;

        /** 票面收款人开户行行号 */
        private String pyeeBankNo;

        /** 承兑人名称 */
        private String acptName;

        /** 承兑人账号 */
        private String acptAcctNo;

        /**
         * 承兑人账号名称
         */
        private String acptAcctName;

        /** 承兑人开户行名称 */
        private String acptBankName;

        /** 承兑人开户行行号 */
        private String acptBankNo;

        /** 子票区间起始 */
        private String billRangeStart;

        /** 子票区间截止 */
        private String billRangeEnd;

        /** 交易金额 */
        private String transAmt;

        /** 是否可转让:
         EM00：可再转
         EM01：不得转让 */
        private String banEndrsmtMark;

        /** 新一代票据状态
         已出票:CS01
         已承兑:CS02
         已收票:CS03
         已到期:CS04
         已终止:CS05
         已结清:CS06 */
        private String billStatus;

        /** 票据流通状态
         TF0301-可流通
         TF0302-已锁定
         TF0303-不可转让
         TF0304-已质押
         TF0305-待赎回
         TF0401-托收在
         TF0402-追索中
         TF0501-已结束 */
        private String grayDisplay;

        /** 是否可操作:
         0：允许
         1：不允许 */
        private String cirStatus;
    }
}
