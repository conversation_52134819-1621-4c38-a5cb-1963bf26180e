package com.vedeng.erp.mobile.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 售后商品api返回dto
 */
@Data
public class AfterSalesGoodsListResultDto {

    /**
     * 售后商品主键id
     */
    private Integer afterSalesGoodsId;

    /**
     * 售后订单主键id
     */
    private Integer afterSalesId;

    /**
     * 销售/采购订单详情ID
     */
    private Integer orderDetailId;

    /**
     * 产品类型0普通产品1特殊产品（手续费）
     */
    private Integer goodsType;

    /**
     * 产品ID
     */
    private Integer goodsId;

    /**
     * 售后数量
     */
    private Integer num;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * SKU
     */
    private String sku;

    /**
     * 商品名称
     */
    private String goodsName;

}
