<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.report.dao.PublicCustomerCalculateMapper">

    <select id="getB2bCustomer" resultType="com.vedeng.trader.model.dto.TraderCustomerAssociateInfoDto">
        SELECT T1.TRADER_CUSTOMER_ID,
        T2.TRADER_ID,
        T3.USER_ID,
        T1.ASSOCIATED_CUSTOMER_GROUP
        FROM T_TRADER_CUSTOMER T1
        JOIN T_TRADER T2 ON T1.TRADER_ID = T2.TRADER_ID
        LEFT JOIN T_R_TRADER_J_USER T3 ON T2.TRADER_ID = T3.TRADER_ID AND T3.TRADER_TYPE = 1
        WHERE T2.BELONG_PLATFORM = 1
        <if test="traderCustomerIdList != null and traderCustomerIdList.size > 0">
            AND T1.TRADER_CUSTOMER_ID IN
            <foreach collection="traderCustomerIdList" open="(" close=")" item="item" index="index" separator=",">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
    </select>

    <select id="getB2bCustomerExceptExempt" resultType="com.vedeng.trader.model.dto.TraderCustomerAssociateInfoDto">
        SELECT T1.TRADER_CUSTOMER_ID,
        T2.TRADER_ID,
        T3.USER_ID,
        T1.ASSOCIATED_CUSTOMER_GROUP
        FROM T_TRADER_CUSTOMER T1
        JOIN T_TRADER T2 ON T1.TRADER_ID = T2.TRADER_ID
        LEFT JOIN T_R_TRADER_J_USER T3 ON T2.TRADER_ID = T3.TRADER_ID AND T3.TRADER_TYPE = 1
        LEFT JOIN T_PUBLIC_CUSTOMER_EXEMPT_USERS PE on T3.USER_ID = PE.USER_ID and PE.DELETED = 0
        WHERE T2.BELONG_PLATFORM = 1
        AND T1.PUBLIC_CUSTOMER_EARLY_WARNING_COUNT >= 10
        <if test="traderCustomerIdList != null and traderCustomerIdList.size > 0">
            AND T1.TRADER_CUSTOMER_ID IN
            <foreach collection="traderCustomerIdList" open="(" close=")" item="item" index="index" separator=",">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        AND PE.USER_ID IS NULL
    </select>

    <select id="getPublicCustomerLockedRecently" resultType="java.lang.Integer">
        select  TRADER_CUSTOMER_ID
        from T_PUBLIC_CUSTOMER_RECORD where IS_PRIVATIZED = 1 and (unix_timestamp()*1000 - PRIVATIZED_TIME) &lt; #{recentDays}*24*3600*1000
    </select>

    <select id="getOldTraderCustomerByCondition"
            resultType="com.vedeng.trader.model.dto.TraderCustomerAssociateInfoDto">
        SELECT
            T1.TRADER_CUSTOMER_ID,
            T2.TRADER_ID,
            T3.USER_ID,
            T1.ASSOCIATED_CUSTOMER_GROUP
        FROM
            T_TRADER_CUSTOMER T1
                LEFT JOIN T_TRADER T2 ON T1.TRADER_ID = T2.TRADER_ID
                LEFT JOIN T_R_TRADER_J_USER T3 ON T2.TRADER_ID = T3.TRADER_ID
        WHERE
            T2.BELONG_PLATFORM = 1
          AND (unix_timestamp( now( ) ) * 1000 - T2.ADD_TIME) &lt; #{customerCreatedDays,jdbcType=BIGINT} * 24 * 60 * 60 * 1000
    </select>

    <select id="getUnPrivateCustomerRecords" resultType="java.lang.Integer">
        SELECT
            TRADER_CUSTOMER_ID
        FROM
            T_PUBLIC_CUSTOMER_RECORD
        WHERE
            IS_PRIVATIZED = 0
    </select>

    <select id="getCustomerListUnlockedByBusinessChance" resultType="java.lang.Integer">
        select distinct TRADER_CUSTOMER_ID
        from T_PUBLIC_CUSTOMER_RECORD
        where IS_UNLOCK = 1
          and (unix_timestamp(now()) * 1000 - UNLOCK_TIME) <![CDATA[<=]]> #{unlockedDays} * 24 * 3600 * 1000
    </select>

    <select id="getLockProtectDays" resultType="java.lang.Integer">
        select distinct TRADER_CUSTOMER_ID
        from T_PUBLIC_CUSTOMER_RECORD
        where IS_UNLOCK > 0 and (unix_timestamp(now()) * 1000 - UNLOCK_TIME) <![CDATA[<=]]> #{lockProtectDays} * 24 * 3600 * 1000
    </select>

    <select id="getCustomerIdsOfGrateThenProtectedDays" resultType="java.lang.Integer">
        select distinct TRADER_CUSTOMER_ID
        from T_PUBLIC_CUSTOMER_RECORD
        where IS_PRIVATIZED = 2
          and (REVOCATION_PROTECTION_DEADLINE - unix_timestamp(now()) * 1000) &gt; #{protectedDays} * 24 * 3600 * 1000
    </select>

    <select id="getValidOrderCountOfB2bCustomer" resultType="com.vedeng.order.model.dto.CustomerCountDto">
        select tc.TRADER_CUSTOMER_ID , count(s.SALEORDER_ID) myCount
        from T_TRADER_CUSTOMER tc
                 join T_TRADER t on tc.TRADER_ID = t.TRADER_ID and t.BELONG_PLATFORM = 1
                 left join T_SALEORDER s on tc.TRADER_ID = s.TRADER_ID and s.VALID_STATUS = 1 and
                                            (unix_timestamp(now()) * 1000 - VALID_TIME) &lt; #{validOrderDays} * 24 * 3600 * 1000
        group by tc.TRADER_CUSTOMER_ID
    </select>

    <select id="getCustomerListByAssociatedGroup" resultType="com.vedeng.trader.model.TraderCustomer">
        select ASSOCIATED_CUSTOMER_GROUP ,group_concat(TRADER_CUSTOMER_ID) comments
        from T_TRADER_CUSTOMER where ASSOCIATED_CUSTOMER_GROUP > 0 group by ASSOCIATED_CUSTOMER_GROUP;
    </select>

    <select id="getValidCommunicationCountOfB2bCustomer" resultType="com.vedeng.order.model.dto.CustomerCountDto">
        select tc.TRADER_CUSTOMER_ID, count(cr.COMMUNICATE_RECORD_ID) myCount
        from T_TRADER_CUSTOMER tc
                 join T_TRADER t on tc.TRADER_ID = t.TRADER_ID and t.BELONG_PLATFORM = 1
                 left join T_COMMUNICATE_RECORD cr on tc.TRADER_ID = cr.TRADER_ID and cr.COID_LENGTH >= 120 and
                                                      (unix_timestamp(now()) * 1000 - cr.ADD_TIME)  &lt; #{communicationDays} * 24 * 3600 * 1000
        group by tc.TRADER_CUSTOMER_ID
    </select>
</mapper>
