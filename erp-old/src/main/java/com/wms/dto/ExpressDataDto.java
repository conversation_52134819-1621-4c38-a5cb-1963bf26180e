package com.wms.dto;

import java.math.BigDecimal;

/**
 * 包裹单数据
 */
public class ExpressDataDto {
    /**
     * 包裹运单号
     */
    private String DeliveryNo;

    /**
     * 商品内码
     */
    private String SKU;

    /**
     * 发货数量
     */
    private BigDecimal qtyShipped;

    /**
     * 生产日期
     */
    private String LotAtt01;

    /**
     * 有效期至
     */
    private String LotAtt02;

    /**
     * 入库日期
     */
    private String LotAtt03;

    /**
     * 厂家批号
     */
    private String LotAtt04;

    /**
     * 灭菌批号
     */
    private String LotAtt05;

    /**
     * 注册证号
     */
    private String LotAtt06;

    /**
     * 预留字段
     */
    private String LotAtt07;

    /**
     * 质量状态
     */
    private String LotAtt08;

    /**
     * 销售订单号
     */
    private String LotAtt09;

    /**
     * 入库单号
     */
    private  String LotAtt10;

    /**
     * 贝登批次码
     */
    private String LotAtt11;

    /**
     * 订单商品ID
     */
    private String UserDefine1;

    /**
     * 预留字段
     */
    private String UserDefine2;

    /**
     * 预留字段
     */
    private String UserDefine3;

    /**
     * 预留字段
     */
    private String UserDefine4;

    /**
     * 预留字段
     */
    private String UserDefine5;

    public String getDeliveryNo() {
        return DeliveryNo;
    }

    public void setDeliveryNo(String deliveryNo) {
        DeliveryNo = deliveryNo;
    }

    public String getSKU() {
        return SKU;
    }

    public void setSKU(String SKU) {
        this.SKU = SKU;
    }

    public BigDecimal getQtyShipped() {
        return qtyShipped;
    }

    public void setQtyShipped(BigDecimal qtyShipped) {
        this.qtyShipped = qtyShipped;
    }

    public String getLotAtt01() {
        return LotAtt01;
    }

    public void setLotAtt01(String lotAtt01) {
        LotAtt01 = lotAtt01;
    }

    public String getLotAtt02() {
        return LotAtt02;
    }

    public void setLotAtt02(String lotAtt02) {
        LotAtt02 = lotAtt02;
    }

    public String getLotAtt03() {
        return LotAtt03;
    }

    public void setLotAtt03(String lotAtt03) {
        LotAtt03 = lotAtt03;
    }

    public String getLotAtt04() {
        return LotAtt04;
    }

    public void setLotAtt04(String lotAtt04) {
        LotAtt04 = lotAtt04;
    }

    public String getLotAtt05() {
        return LotAtt05;
    }

    public void setLotAtt05(String lotAtt05) {
        LotAtt05 = lotAtt05;
    }

    public String getLotAtt06() {
        return LotAtt06;
    }

    public void setLotAtt06(String lotAtt06) {
        LotAtt06 = lotAtt06;
    }

    public String getLotAtt07() {
        return LotAtt07;
    }

    public void setLotAtt07(String lotAtt07) {
        LotAtt07 = lotAtt07;
    }

    public String getLotAtt08() {
        return LotAtt08;
    }

    public void setLotAtt08(String lotAtt08) {
        LotAtt08 = lotAtt08;
    }

    public String getLotAtt09() {
        return LotAtt09;
    }

    public void setLotAtt09(String lotAtt09) {
        LotAtt09 = lotAtt09;
    }

    public String getLotAtt10() {
        return LotAtt10;
    }

    public void setLotAtt10(String lotAtt10) {
        LotAtt10 = lotAtt10;
    }

    public String getLotAtt11() {
        return LotAtt11;
    }

    public void setLotAtt11(String lotAtt11) {
        LotAtt11 = lotAtt11;
    }

    public String getUserDefine1() {
        return UserDefine1;
    }

    public void setUserDefine1(String userDefine1) {
        UserDefine1 = userDefine1;
    }

    public String getUserDefine2() {
        return UserDefine2;
    }

    public void setUserDefine2(String userDefine2) {
        UserDefine2 = userDefine2;
    }

    public String getUserDefine3() {
        return UserDefine3;
    }

    public void setUserDefine3(String userDefine3) {
        UserDefine3 = userDefine3;
    }

    public String getUserDefine4() {
        return UserDefine4;
    }

    public void setUserDefine4(String userDefine4) {
        UserDefine4 = userDefine4;
    }

    public String getUserDefine5() {
        return UserDefine5;
    }

    public void setUserDefine5(String userDefine5) {
        UserDefine5 = userDefine5;
    }

    @Override
    public String toString() {
        return "ExpressDataDto{" +
                "DeliveryNo='" + DeliveryNo + '\'' +
                ", SKU='" + SKU + '\'' +
                ", qtyShipped=" + qtyShipped +
                ", LotAtt01='" + LotAtt01 + '\'' +
                ", LotAtt02='" + LotAtt02 + '\'' +
                ", LotAtt03='" + LotAtt03 + '\'' +
                ", LotAtt04='" + LotAtt04 + '\'' +
                ", LotAtt05='" + LotAtt05 + '\'' +
                ", LotAtt06='" + LotAtt06 + '\'' +
                ", LotAtt07='" + LotAtt07 + '\'' +
                ", LotAtt08='" + LotAtt08 + '\'' +
                ", LotAtt09='" + LotAtt09 + '\'' +
                ", LotAtt10='" + LotAtt10 + '\'' +
                ", LotAtt11='" + LotAtt11 + '\'' +
                ", UserDefine1='" + UserDefine1 + '\'' +
                ", UserDefine2='" + UserDefine2 + '\'' +
                ", UserDefine3='" + UserDefine3 + '\'' +
                ", UserDefine4='" + UserDefine4 + '\'' +
                ", UserDefine5='" + UserDefine5 + '\'' +
                '}';
    }
}
