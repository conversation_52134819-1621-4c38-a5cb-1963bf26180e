package com.vedeng.erp.finance.web.api;

import com.vedeng.common.core.annotation.ExcludeAuthorization;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.finance.domain.dto.AcceptanceBillDto;
import com.vedeng.erp.finance.service.AcceptanceBillService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 承兑汇票
 * @date 2024/9/9 9:24
 */
@ExceptionController
@RestController
@Slf4j
@RequestMapping("/acceptanceBill")
public class AcceptanceBillApi {


    @Autowired
    AcceptanceBillService acceptanceBillService;

    /**
     * 列表查询
     *
     * @param query
     * @return
     */
    @RequestMapping(value = "/page")
    @ExcludeAuthorization
    public R<?> page(@RequestBody PageParam<AcceptanceBillDto> query) {
        return R.success(acceptanceBillService.page(query));
    }

    /**
     * 签收
     * @param billNumber 票据编号
     * @return
     */
    @RequestMapping(value = "/signUp")
    @ExcludeAuthorization
    public R<?> signUp(@RequestParam String billNumber) {
        try{
            boolean result = acceptanceBillService.signup(billNumber);
            if(result){
                return R.success("签收成功");
            }
            return R.error("签收失败");
        }catch (Exception e){
            log.error("签收失败",e);
            return R.error("签收失败");
        }
    }

    /**
     * 进行贴现处理
     * 接口名称：贴现申请(B2eNbsDraftDiscount)<br/>
     * 接口说明：<br/>
     * 1. 本接口用于对票据做贴现申请<br/>
     * 2. 交易后使用《票据交易状态查询(B2eNbsQryDraftTransStatus)》查询交易结果向我行贴现<br/>
     * 3. 向我行贴现,目前需线下与我行签署贴现协议，具体流程请联系客户经理；向他行贴现，请向他行咨询具体流程。<br/>
     * @param billNumber 票据编号
     * @return
     */
    @RequestMapping(value = "/discount")
    @ExcludeAuthorization
    public R<?> discount(@RequestParam String billNumber,@RequestParam String rate) {
        CurrentUser user = CurrentUser.getCurrentUser();
        try{
            boolean discountResult = acceptanceBillService.draftDiscount(billNumber,rate,user.getId());
            if(discountResult){
                return R.success("贴现处理成功");
            }
            return R.error("贴现处理失败");
        }catch (Exception e){
            log.error("贴现处理失败",e);
            return R.error(e.getMessage());
        }
    }
}
