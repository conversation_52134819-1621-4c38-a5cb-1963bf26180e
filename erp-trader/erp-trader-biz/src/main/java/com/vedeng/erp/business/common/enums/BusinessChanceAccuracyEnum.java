package com.vedeng.erp.business.common.enums;
/**
 * <AUTHOR>
 * @description 商机精准度枚举类
 * @date 2023/9/12 15:28
 **/
public enum BusinessChanceAccuracyEnum {
    /**
     * 0无法判断
     */
    UNABLEJUDGE(0, "无法判断"),

    /**
     * 1不精准
     */
    INACCURATE(1, "不精准"),

    /**
     * 2一般精准
     */
    GENERALLYACCURATE(2, "一般精准"),

    /**
     * 3高精准
     */
    HIGHLYACCURATE(3, "高精准");

    /**
     * code
     */
    private final Integer code;

    /**
     * 商机精准度
     */
    private final String businessChanceAccuracy;

    BusinessChanceAccuracyEnum(Integer code, String businessChanceAccuracy) {
        this.code = code;
        this.businessChanceAccuracy = businessChanceAccuracy;
    }

    public Integer getCode() {
        return code;
    }


    public String getBusinessChanceAccuracy() {
        return businessChanceAccuracy;
    }

    public static String getTextByCode(int code) {
        for (BusinessChanceAccuracyEnum b : BusinessChanceAccuracyEnum.values()) {
            if (b.getCode() == code) {
                return b.getBusinessChanceAccuracy();
            }
        }
        return "";
    }
}
