<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.system.mapper.BankMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.system.domain.entity.BankEntity">
    <!--@mbg.generated-->
    <!--@Table T_BANK-->
    <id column="BANK_ID" jdbcType="INTEGER" property="bankId" />
    <result column="BANK_NO" jdbcType="VARCHAR" property="bankNo" />
    <result column="BANK_NAME" jdbcType="VARCHAR" property="bankName" />
    <result column="SOURCE" jdbcType="INTEGER" property="source" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="IS_DEL" jdbcType="TINYINT" property="isDel" />
    <result column="DISABLE_FLAG" jdbcType="BOOLEAN" property="disableFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    BANK_ID, BANK_NO, BANK_NAME, `SOURCE`, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME,
    UPDATER_NAME, IS_DEL, DISABLE_FLAG
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_BANK
    where BANK_ID = #{bankId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_BANK
    where BANK_ID = #{bankId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.erp.system.domain.entity.BankEntity">
    <!--@mbg.generated-->
    insert into T_BANK (BANK_ID, BANK_NO, BANK_NAME,
    `SOURCE`, ADD_TIME, MOD_TIME,
    CREATOR, UPDATER, CREATOR_NAME,
    UPDATER_NAME, IS_DEL, DISABLE_FLAG
    )
    values (#{bankId,jdbcType=INTEGER}, #{bankNo,jdbcType=VARCHAR}, #{bankName,jdbcType=VARCHAR},
    #{source,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP},
    #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR},
    #{updaterName,jdbcType=VARCHAR}, #{isDel,jdbcType=TINYINT}, #{disableFlag,jdbcType=BOOLEAN}
    )
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.erp.system.domain.entity.BankEntity">
    <!--@mbg.generated-->
    insert into T_BANK
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bankId != null">
        BANK_ID,
      </if>
      <if test="bankNo != null">
        BANK_NO,
      </if>
      <if test="bankName != null">
        BANK_NAME,
      </if>
      <if test="source != null">
        `SOURCE`,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="isDel != null">
        IS_DEL,
      </if>
      <if test="disableFlag != null">
        DISABLE_FLAG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bankId != null">
        #{bankId,jdbcType=INTEGER},
      </if>
      <if test="bankNo != null">
        #{bankNo,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=TINYINT},
      </if>
      <if test="disableFlag != null">
        #{disableFlag,jdbcType=BOOLEAN},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.system.domain.entity.BankEntity">
    <!--@mbg.generated-->
    update T_BANK
    <set>
      <if test="bankNo != null">
        BANK_NO = #{bankNo,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        BANK_NAME = #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        `SOURCE` = #{source,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="isDel != null">
        IS_DEL = #{isDel,jdbcType=TINYINT},
      </if>
      <if test="disableFlag != null">
        DISABLE_FLAG = #{disableFlag,jdbcType=BOOLEAN},
      </if>
    </set>
    where BANK_ID = #{bankId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.system.domain.entity.BankEntity">
    <!--@mbg.generated-->
    update T_BANK
    set BANK_NO = #{bankNo,jdbcType=VARCHAR},
    BANK_NAME = #{bankName,jdbcType=VARCHAR},
    `SOURCE` = #{source,jdbcType=INTEGER},
    ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
    MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
    CREATOR = #{creator,jdbcType=INTEGER},
    UPDATER = #{updater,jdbcType=INTEGER},
    CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
    UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
    IS_DEL = #{isDel,jdbcType=TINYINT},
    DISABLE_FLAG = #{disableFlag,jdbcType=BOOLEAN}
    where BANK_ID = #{bankId,jdbcType=INTEGER}
  </update>
  <select id="findByAll" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List"/>
    from T_BANK
    <where>
      <if test="bankId != null">
        and BANK_ID=#{bankId,jdbcType=INTEGER}
      </if>
      <if test="bankNo != null">
        and BANK_NO=#{bankNo,jdbcType=VARCHAR}
      </if>
      <if test="bankName != null">
        and BANK_NAME=#{bankName,jdbcType=VARCHAR}
      </if>
      <if test="source != null">
        and `SOURCE`=#{source,jdbcType=INTEGER}
      </if>
      <if test="addTime != null">
        and ADD_TIME=#{addTime,jdbcType=TIMESTAMP}
      </if>
      <if test="modTime != null">
        and MOD_TIME=#{modTime,jdbcType=TIMESTAMP}
      </if>
      <if test="creator != null">
        and CREATOR=#{creator,jdbcType=INTEGER}
      </if>
      <if test="updater != null">
        and UPDATER=#{updater,jdbcType=INTEGER}
      </if>
      <if test="creatorName != null">
        and CREATOR_NAME=#{creatorName,jdbcType=VARCHAR}
      </if>
      <if test="updaterName != null">
        and UPDATER_NAME=#{updaterName,jdbcType=VARCHAR}
      </if>
      <if test="isDel != null">
        and IS_DEL=#{isDel,jdbcType=TINYINT}
      </if>
      <if test="disableFlag != null">
        and DISABLE_FLAG=#{disableFlag,jdbcType=BOOLEAN}
      </if>
    </where>
  </select>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_BANK
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="BANK_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.bankNo != null">
            when BANK_ID = #{item.bankId,jdbcType=INTEGER} then #{item.bankNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="BANK_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.bankName != null">
            when BANK_ID = #{item.bankId,jdbcType=INTEGER} then #{item.bankName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`SOURCE` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.source != null">
            when BANK_ID = #{item.bankId,jdbcType=INTEGER} then #{item.source,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when BANK_ID = #{item.bankId,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when BANK_ID = #{item.bankId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when BANK_ID = #{item.bankId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when BANK_ID = #{item.bankId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when BANK_ID = #{item.bankId,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when BANK_ID = #{item.bankId,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DEL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDel != null">
            when BANK_ID = #{item.bankId,jdbcType=INTEGER} then #{item.isDel,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="DISABLE_FLAG = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.disableFlag != null">
            when BANK_ID = #{item.bankId,jdbcType=INTEGER} then #{item.disableFlag,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
    </trim>
    where BANK_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.bankId,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into T_BANK
    (BANK_ID, BANK_NO, BANK_NAME, `SOURCE`, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME,
    UPDATER_NAME, IS_DEL, DISABLE_FLAG)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.bankId,jdbcType=INTEGER}, #{item.bankNo,jdbcType=VARCHAR}, #{item.bankName,jdbcType=VARCHAR},
      #{item.source,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP},
      #{item.creator,jdbcType=INTEGER}, #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR},
      #{item.updaterName,jdbcType=VARCHAR}, #{item.isDel,jdbcType=TINYINT}, #{item.disableFlag,jdbcType=BOOLEAN}
      )
    </foreach>
  </insert>


<!--auto generated by MybatisCodeHelper on 2022-08-16-->
  <select id="findByBankNoAndIsDel" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_BANK
    where BANK_NO=#{bankNo,jdbcType=VARCHAR} and IS_DEL=#{isDel,jdbcType=TINYINT}
  </select>

<!--auto generated by MybatisCodeHelper on 2022-08-17-->
  <select id="findByBankNameLike" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_BANK
    where
    IS_DEL = 0
    and DISABLE_FLAG = 0
    and ((BANK_NAME like #{likeBankName,jdbcType=VARCHAR})
    or (BANK_NO like #{likeBankName,jdbcType=VARCHAR})
    )
    limit 10
  </select>

<!--auto generated by MybatisCodeHelper on 2023-11-15-->
  <select id="selectByModTime" resultType="com.vedeng.erp.system.domain.dto.BankDto">
        select
        <include refid="Base_Column_List"/>
        from T_BANK
        where MOD_TIME <![CDATA[>=]]> #{start,jdbcType=TIMESTAMP} and MOD_TIME
         <![CDATA[<=]]> #{end,jdbcType=TIMESTAMP}
    </select>

  <select id="findByBankName" resultType="com.vedeng.erp.system.dto.BankInfoDto">
    select * from T_BANK where IS_DEL = 0 and DISABLE_FLAG = 0 and BANK_NAME in
    <foreach collection="bankNames" item="item" open="(" separator="," close=")">
        #{item,jdbcType=VARCHAR}
    </foreach>
 </select>

<!--auto generated by MybatisCodeHelper on 2024-08-31-->
  <select id="findByBankNameLikeOrBankNoLike" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_BANK
    where
    (BANK_NAME like concat('%',#{keyWords,jdbcType=VARCHAR},'%')
    or BANK_NO like concat('%',#{keyWords,jdbcType=VARCHAR},'%'))
    and IS_DEL = 0
    and DISABLE_FLAG = 0
    limit 20
  </select>

<!--auto generated by MybatisCodeHelper on 2024-09-04-->
  <select id="findByBankId" resultType="com.vedeng.erp.system.dto.BankInfoDto">
    select
    *
    from T_BANK
    where BANK_ID=#{bankId,jdbcType=INTEGER}
  </select>
</mapper>
