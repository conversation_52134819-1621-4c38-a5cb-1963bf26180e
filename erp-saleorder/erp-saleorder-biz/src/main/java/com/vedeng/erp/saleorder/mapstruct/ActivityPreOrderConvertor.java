package com.vedeng.erp.saleorder.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.saleorder.domain.entity.ActivityPreOrderEntity;
import com.vedeng.erp.saleorder.dto.ActivityPreOrderDto;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * dto 转 entity
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface ActivityPreOrderConvertor extends BaseMapStruct<ActivityPreOrderEntity, ActivityPreOrderDto> {



}
