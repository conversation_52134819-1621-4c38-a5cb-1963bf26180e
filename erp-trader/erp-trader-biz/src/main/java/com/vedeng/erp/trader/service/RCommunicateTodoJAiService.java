package com.vedeng.erp.trader.service;

import com.vedeng.erp.trader.dto.RCommunicateTodoJAiDto;
import com.vedeng.erp.trader.domain.entity.RCommunicateTodoJAiEntity;
public interface RCommunicateTodoJAiService{

    int deleteByPrimaryKey(Integer communicateInfoId);

    int insert(RCommunicateTodoJAiEntity record);

    int insertSelective(RCommunicateTodoJAiEntity record);

    RCommunicateTodoJAiEntity selectByPrimaryKey(Integer communicateInfoId);

    int updateByPrimaryKeySelective(RCommunicateTodoJAiEntity record);

    int updateByPrimaryKey(RCommunicateTodoJAiEntity record);

    /**
     * ai分析处理结果：生成商机
     * @param record
     */
    void createBusinessChange(RCommunicateTodoJAiDto record);

    /**
     * ai分析处理结果：无需生成商机
     * @param record
     */
    void noCreateBusinessChange(RCommunicateTodoJAiDto record);
    /**
     * ai分析处理结果：添加修改客户标签
     * @param record
     */
    void updateTraderSign(RCommunicateTodoJAiDto record);

    /**
     * ai分析处理结果：无需添加修改客户标签
     * @param record
     */
    void noUpdateTraderSign(RCommunicateTodoJAiDto record);

    /**
     * ai分析处理结果：同步联系人职位信息
     * @param record
     */
    void syncContactPosition(RCommunicateTodoJAiDto record);
    /**
     * ai分析处理结果：添加沟通记录
     */
    void addCommunicateRecord(RCommunicateTodoJAiDto rCommunicateTodoJAiDto);
}
