package com.vedeng.common.core.config;

import com.google.common.eventbus.EventBus;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 事件监听器
 * @date 2023/10/20 16:43
 */
@Configuration
public class EventBusConfiguration {

    @Bean(name = "eventBusCenter")
    public EventBus eventBusCenter(List<IObserver> observers) {
        EventBus eventBus = new EventBus();
        if (CollectionUtils.isEmpty(observers)) {
            return eventBus;
        }
        for (IObserver observer : observers) {
            eventBus.register(observer);
        }
        return eventBus;
    }
}
