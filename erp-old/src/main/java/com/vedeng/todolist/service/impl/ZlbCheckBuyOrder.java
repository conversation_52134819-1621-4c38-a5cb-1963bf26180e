package com.vedeng.todolist.service.impl;

import com.vedeng.common.util.DateUtil;
import com.vedeng.todolist.constant.TodoListBuzSceneEnum;
import com.vedeng.todolist.dao.TodoListMapper;
import com.vedeng.todolist.model.TodoList;
import com.vedeng.todolist.service.ITodoInstance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Author: daniel
 * @Date: 2020/12/11 10 36
 * @Description:
 */
@Service
public class ZlbCheckBuyOrder implements ITodoInstance {

    private static final Logger logger = LoggerFactory.getLogger(ZlbCheckBuyOrder.class);

    @Resource
    private TodoListMapper todoListMapper;

    @Override
    public synchronized void add(Integer buzId, String buzExtra, String comment, String buzProperty) {
        logger.info("待办事项---质量部审核采购订单，buzId:{}生成待办事项",buzId);
        if (todoListMapper.selectUnHandledByBuzTypeAndBuzId(TodoListBuzSceneEnum.ZLB_CHECK_BUY_ORDER.getBuzSceneId(), buzId).size() > 0) {
            todoListMapper.deleteByBuzTypeAndBuzId(TodoListBuzSceneEnum.ZLB_CHECK_BUY_ORDER.getBuzSceneId(), buzId);
        }
        TodoList instance = new TodoList(TodoListBuzSceneEnum.ZLB_CHECK_BUY_ORDER,buzId,buzExtra,buzProperty,comment, DateUtil.sysTimeMillis(),2);
        todoListMapper.insertSelective(instance);
    }

    @Override
    public void finish(Integer buzId) {
        todoListMapper.updateStatusByBuzTypeAndBuzId(TodoListBuzSceneEnum.ZLB_CHECK_BUY_ORDER.getBuzSceneId(),buzId,1,
                                                    getCurrentRequestUser().getUserId(),
                                                    System.currentTimeMillis());
        logger.info("待办事项---质量部审核采购订单，buzId;{}更新为已完成",buzId);
    }

    public void finish(Integer buzId, Integer checkStatus){
        todoListMapper.finishZlbTodoList(TodoListBuzSceneEnum.ZLB_CHECK_BUY_ORDER.getBuzSceneId(), buzId,checkStatus,DateUtil.sysTimeMillis());
        logger.info("待办事项---质量部审核采购订单，buzId;{}更新为已完成",buzId);
    }
}
