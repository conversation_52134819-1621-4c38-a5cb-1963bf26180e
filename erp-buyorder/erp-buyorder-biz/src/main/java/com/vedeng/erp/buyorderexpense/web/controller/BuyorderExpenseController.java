package com.vedeng.erp.buyorderexpense.web.controller;

import com.common.constants.Contant;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.BaseController;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.exception.ShowErrorMsgException;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.DigitToChineseUppercaseNumberUtils;
import com.vedeng.common.validator.FormToken;
import com.vedeng.erp.buyorder.common.utils.NewBuyOrderUtils;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseDto;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseItemDto;
import com.vedeng.erp.buyorderexpense.service.BuyorderExpenseService;
import com.vedeng.erp.system.dto.SysOptionDefinitionDto;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.SysOptionDefinitionApiService;
import com.vedeng.erp.system.service.UserApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.Transformer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @description 视图跳转
 * @date 2022/8/22 13:18
 **/
@Slf4j
@Controller
@RequestMapping("/buyorderExpense")
public class BuyorderExpenseController extends BaseController {

    @Resource
    private BuyorderExpenseService buyorderExpenseService;

    @Resource
    private UserApiService userApiService;

    @Resource
    private SysOptionDefinitionApiService sysOptionDefinitionApiService;

    @Value("${vedeng_address_phone}")
    protected String vedeng_address_phone;

    /**
     * 采购费用单详情页
     *
     * @param buyorderExpenseId
     * @return
     */
    @FormToken(save = true)
    @RequestMapping(value = "/details")
    public ModelAndView detail(Integer buyorderExpenseId) {
        ModelAndView mv = new ModelAndView("vue/view/buyorderexpense/details");
        mv.addObject("buyorderExpenseId", buyorderExpenseId);
        return mv;
    }

    /**
     * 采购费用单新增/编辑页
     *
     * @param buyorderExpenseId buyorderExpenseId
     * @return ModelAndView
     */
    @RequestMapping(value = "/edit")
    public ModelAndView edit(@RequestParam(required = false, defaultValue = "0") Integer buyorderExpenseId) {
        ModelAndView mv = new ModelAndView("vue/view/buyorderexpense/edit");
        mv.addObject("buyorderExpenseId", buyorderExpenseId);
        return mv;
    }

    /**
     * 待采购列表跳转 采购费用单新增页
     *
     * @param  saleorderGoodsIds
     * @return ModelAndView
     */
    @RequestMapping(value = "/preBuyOrder/edit")
    public ModelAndView preBuyOrderToEdit(@RequestParam(required = false, defaultValue = "0") String saleorderGoodsIds) {
        ModelAndView mv = new ModelAndView("vue/view/buyorderexpense/edit");
        List<Integer> saleorderGoodsIdList = new ArrayList<>();
        try{
            String[] saleorderGoodsIdsArray = saleorderGoodsIds.split(",");
            CollectionUtils.collect(Arrays.asList(saleorderGoodsIdsArray), new Transformer() {
                @Override
                public Object transform(Object input) {
                    return Integer.valueOf(input.toString().split("\\|")[1]);
                }
            }, saleorderGoodsIdList);
        }catch(Exception e){
            log.error("获取销售单明细失败...",e);
        }
        mv.addObject("saleOrderGoodsIdList", saleorderGoodsIdList);
        mv.addObject("buyorderExpenseId", 0);
        return mv;
    }


    /**
     * 采购费用单合同
     *
     * @param buyorderExpenseId
     * @return
     */
    @RequestMapping(value = "/printContract")
    @NoNeedAccessAuthorization
    public ModelAndView printContract(Integer buyorderExpenseId, @RequestParam(required = false) Boolean autoGenerate) {
        ModelAndView mv = new ModelAndView("order/buyorderexpense/printContract");
        mv.addObject("buyorderExpenseId", buyorderExpenseId);
        BuyorderExpenseDto buyorderExpenseDto = buyorderExpenseService.printDetail(buyorderExpenseId);
        mv.addObject("buyorderExpenseDto", buyorderExpenseDto);
        List<BuyorderExpenseItemDto> buyorderExpenseGoodsList = buyorderExpenseDto.getBuyorderExpenseItemDtos();
        // 获取采购人员信息
        UserDto detail = userApiService.getUserById(buyorderExpenseDto.getCreator());
        mv.addObject("orgId", detail.getOrgId());

        // 发票类型
        SysOptionDefinitionDto invoiceType = sysOptionDefinitionApiService.getOptionDefinitionById(buyorderExpenseDto.getBuyorderExpenseDetailDto().getInvoiceType());
        mv.addObject("invoiceType", invoiceType);
        mv.addObject("detail", detail);

        BigDecimal pageTotalPrice = BigDecimal.ZERO;
        BigDecimal zioe = pageTotalPrice;
        for (BuyorderExpenseItemDto buyorderExpenseGoodsDto : buyorderExpenseGoodsList) {
            String price = NewBuyOrderUtils.getCommaFormat(buyorderExpenseGoodsDto.getBuyorderExpenseItemDetailDto().getPrice());
            if (!price.contains(ErpConstant.PERIOD)) {
                price += ErpConstant.ZEROSTR;
            }
            buyorderExpenseGoodsDto.setPriceStr(price);
            String allprice = NewBuyOrderUtils.getCommaFormat(buyorderExpenseGoodsDto.getBuyorderExpenseItemDetailDto().getPrice().multiply(new BigDecimal(buyorderExpenseGoodsDto.getNum())));
            if (!allprice.contains(ErpConstant.PERIOD)) {
                allprice += ErpConstant.ZEROSTR;
            }
            buyorderExpenseGoodsDto.setAllPrice(allprice);
            pageTotalPrice = pageTotalPrice.add(buyorderExpenseGoodsDto.getBuyorderExpenseItemDetailDto().getPrice().multiply(new BigDecimal(buyorderExpenseGoodsDto.getNum())));
        }
        String totalAmount = NewBuyOrderUtils.getCommaFormat(pageTotalPrice);
        if (!totalAmount.contains(ErpConstant.PERIOD)) {
            totalAmount += ErpConstant.ZEROSTR;
        }
        mv.addObject("totalAmount", totalAmount);
        try {
            mv.addObject("chineseNumberTotalPrice", pageTotalPrice.compareTo(zioe) > 0
                    ? DigitToChineseUppercaseNumberUtils.numberToChineseNumber(pageTotalPrice) : null);
        } catch (ShowErrorMsgException e) {
            log.error(Contant.ERROR_MSG, e);
        }
        mv.addObject("vedeng_address_phone", vedeng_address_phone.trim());
        mv.addObject("buyorderExpenseGoodsList", buyorderExpenseGoodsList);
        mv.addObject("currTime", DateUtil.convertString(DateUtil.sysTimeMillis(), "yyyy-MM-dd"));
        mv.addObject("autoGenerate", autoGenerate);
        return mv;
    }
}
