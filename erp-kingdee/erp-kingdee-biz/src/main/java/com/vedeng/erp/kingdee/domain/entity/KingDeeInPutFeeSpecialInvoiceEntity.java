package com.vedeng.erp.kingdee.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;

import java.util.Date;

import com.vedeng.common.mybatis.jbatis.annotation.GeneratedValue;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.common.mybatis.jbatis.annotation.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 进项费用专用发票
 */
@Getter
@Setter
@ToString
@Table(name = "KING_DEE_IN_PUT_FEE_SPECIAL_INVOICE_ENTITY")
public class KingDeeInPutFeeSpecialInvoiceEntity extends BaseEntity {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer inPutFeeSpecialInvoiceId;

    /**
     * fid
     */
    private String fid;

    /**
     * 贝登erp对应的单据头ID
     */
    private String fQzokBddjtid;

    /**
     * 发票号
     */
    private String finvoiceno;

    /**
     * 发票代码
     */
    private String fQzokFpdm;

    /**
     * 发票日期
     */
    private String finvoicedate;

    /**
     * 业务日期
     */
    private String fdate;

    /**
     * 往来单位类型
     */
    private String fcontactunittype;

    /**
     * 单据类型
     */
    private String fBillTypeId;

    /**
     * 供应商
     */
    private String fsupplierid;

    /**
     * 结算组织
     */
    private String fsettleorgid;

    /**
     * 采购组织
     */
    private String fpurchaseorgid;

    /**
     * 单据状态
     */
    private String fdocumentstatus;

    /**
     * 红蓝字标识
     */
    private String fRedBlue;

    /**
     * 发票明细
     */
    private String fpurexpinventry;
}