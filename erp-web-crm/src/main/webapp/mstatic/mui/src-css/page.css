/* 线索/商机卡片 */
.business-card .business-top {
  border-bottom: solid 1px #F5F7FA;
  padding: 10px;
}
.business-card .business-top .row {
  display: flex;
  justify-content: space-between;
}
.business-card .business-top .row.mt {
  margin-top: 5px;
}
.business-card .business-top .row .business-trader {
  flex: 1;
  min-width: 0;
  margin-right: 10px;
  display: flex;
  align-items: center;
}
.business-card .business-top .row .business-trader .trader-name {
  font-size: 14px;
  color: #000;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.business-card .business-top .row .business-trader .icon-tianyancha {
  width: 38px;
  flex-shrink: 0;
  font-size: 16px;
  color: #0084FF;
  display: flex;
  justify-content: center;
  align-items: center;
}
.business-card .business-top .row .business-status {
  flex-shrink: 0;
}
.business-card .business-top .row .business-status .leads-status {
  height: 22px;
  font-size: 12px;
  line-height: 22px;
  padding: 0 5px;
  border-radius: 3px;
}
.business-card .business-top .row .business-status .leads-status.s0,
.business-card .business-top .row .business-status .leads-status.s1 {
  background: #FFEDE0;
  color: #FF6600;
}
.business-card .business-top .row .business-status .leads-status.s2 {
  background: #E0F3FF;
  color: #09F;
}
.business-card .business-top .row .business-status .leads-status.s4 {
  background: #E3F7E3;
  color: #13BF13;
}
.business-card .business-top .row .business-status .leads-status.s3 {
  background: #E3EAF0;
  color: #1A4D80;
}
.business-card .business-top .row .business-status .change-status {
  height: 22px;
  font-size: 12px;
  line-height: 22px;
  padding: 0 5px;
  border-radius: 3px;
}
.business-card .business-top .row .business-status .change-status.s1,
.business-card .business-top .row .business-status .change-status.s2 {
  background: #FFEDE0;
  color: #FF6600;
}
.business-card .business-top .row .business-status .change-status.s3,
.business-card .business-top .row .business-status .change-status.s4 {
  background: #E0F3FF;
  color: #09F;
}
.business-card .business-top .row .business-status .change-status.s5 {
  background: #E3F7E3;
  color: #13BF13;
}
.business-card .business-top .row .business-status .change-status.s6 {
  background: #E3EAF0;
  color: #1A4D80;
}
.business-card .business-top .row .business-no {
  font-size: 12px;
  color: #999;
}
.business-card .business-top .row .business-time {
  font-size: 12px;
  color: #999;
  margin-left: 10px;
}
.business-card .business-bottom {
  padding: 10px;
}
.business-card .business-bottom .business-attrs {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: -5px;
}
.business-card .business-bottom .business-attrs .item-attr {
  flex: 0 0 50%;
  min-width: 0;
  font-size: 12px;
  display: flex;
  margin-bottom: 5px;
}
.business-card .business-bottom .business-attrs .item-attr:nth-child(2n-1) {
  padding-right: 5px;
}
.business-card .business-bottom .business-attrs .item-attr .label {
  color: #999;
  flex-shrink: 0;
}
.business-card .business-bottom .business-attrs .item-attr .value {
  flex: 1;
  min-width: 0;
}
.business-card .business-bottom .pro-name {
  font-size: 12px;
  margin-top: 5px;
}
.business-card .business-bottom .trader-check {
  margin-top: 10px;
}
