package com.vedeng.erp.finance.mapper;

import com.vedeng.erp.finance.domain.entity.InvoiceReversalEntity;
import org.apache.ibatis.annotations.Param;

public interface InvoiceReversalMapper {
    int deleteByPrimaryKey(Integer invoiceReversalId);

    int insert(InvoiceReversalEntity record);

    int insertSelective(InvoiceReversalEntity record);

    InvoiceReversalEntity selectByPrimaryKey(Integer invoiceReversalId);

    int updateByPrimaryKeySelective(InvoiceReversalEntity record);

    int updateByPrimaryKey(InvoiceReversalEntity record);

    /**
     * 根据申请冲销的发票ID，售后单类型，售后单号查询信息
     * <AUTHOR>
     * @param invoiceId
     * @param reversalBillNo
     * @param reversalBillType
     * @return
     */
    InvoiceReversalEntity queryByInvoiceAndRelated(@Param("invoiceId")Integer invoiceId,@Param("reversalBillNo")String reversalBillNo,@Param("reversalBillType")Integer reversalBillType);
}