<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="新增GE商机" scope="application" />
<%@ include file="../common/common.jsp"%>
<script type="text/javascript" src="<%= basePath %>static/js/ge/add_gebusinesschance.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src='<%= basePath %>static/js/region/newIndex.js?rnd=${resourceVersionKey}'></script>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<div class="form-list  form-tips12">
    <form method="post" action="<%=basePath%>/businesschance/ge/savegebusinesschance.do" id="geBusinessChanceForm">
        <div class="parts">
            <div class="title-container title-container-blue">
                <div class="table-title nobor">
                    基础信息
                </div>
            </div>
            <ul style="margin-top: 10px">
                <li style="margin-top: 10px">
                    <div class="form-tips">
                        <span style="color: red">*</span>
                        <label>报价单号：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" style="width: 500px" name="quoteorderNo" id="quoteorderNo" onclick="searchVdInfo()" readonly placeholder="请输入报价单号">
                            <input type="hidden" name="quoteorderId" id="quoteorderId">
                        </div>
                    </div>
                </li>
                <li style="margin-top: 20px">
                    <div class="form-tips">
                        <span style="color: red">*</span>
                        <label>终端医院名称：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" style="width: 500px" autocomplete="off" name="terminalTraderName" id="terminalTraderName" placeholder="请输入终端医院名称">
                            <input type="hidden" name="terminalTraderId" id="terminalTraderId">
                        </div>
                    </div>
                </li>
                <li style="margin-top: 20px">
                    <div class="form-tips">
                        <span style="color: red">*</span>
                        <label>医院性质：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <select name="hospitalType" id="hospitalType" style="width: 200px">
                                <option value="0">请选择</option>
                                <option value="1">公立</option>
                                <option value="2">非公</option>
                            </select>
                        </div>
                    </div>
                </li>
                <li style="margin-top: 20px">
                    <div class="form-tips">
                        <span style="color: red">*</span>
                        <label>商机来源：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <select name="geBusinessChanceSource" id="geBusinessChanceSource" style="width: 200px">
                                <option value="0">请选择</option>
                                <c:forEach items="${geBusinesschanceSources}" var="geBusinesschanceSource">
                                    <option value="${geBusinesschanceSource.sysOptionDefinitionId}">${geBusinesschanceSource.title}</option>
                                </c:forEach>
                            </select>
                        </div>
                    </div>
                </li>
                <li style="margin-top: 20px">
                    <div class="form-tips">
                        <label>经销商名称：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" style="width: 500px" name="traderName" id="traderName" autocomplete="off" onclick="" placeholder="请输入经销商名称">
                            <input type="hidden" name="traderId" id="traderId">
                        </div>
                    </div>
                </li>
                <li style="margin-top: 20px">
                    <div class="form-tips">
                        <span style="color: red">*</span>
                        <label>意向型号：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" style="width: 500px" name="goodsName" id="goodsName" autocomplete="off" onclick="" placeholder="请输入意向型号">
                            <input type="hidden" name="goodsId" id="goodsId">
                            <input type="hidden" name="sku" id="sku">
                        </div>
                    </div>
                </li>
                <li style="margin-top: 20px">
                    <div class="form-tips">
                        <span style="color: red">*</span>
                        <label>所属地区：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <select class="input-small f_left mr10" name="province" id="province">
                                <option value="0">请选择</option>
                                <c:if test="${not empty provinceList }">
                                    <c:forEach items="${provinceList }" var="prov">
                                        <option value="${prov.regionId }" <c:if test="${province eq prov.regionId }">selected="selected"</c:if>>${prov.regionName }</option>
                                    </c:forEach>
                                </c:if>
                            </select>
                            <select class="input-small f_left mr10" name="city" id="city">
                                <option value="0">请选择</option>
                            </select>
                            <select class="input-small f_left" name="zone" id="zone">
                                <option value="0">请选择</option>
                            </select>
                            <input type="hidden" name="salesArea" id="salesArea">
                            <input type="hidden" name="salesAreaId" id="salesAreaId">
                        </div>
                    </div>
                </li>
                <li style="margin-top: 20px">
                    <div class="form-tips">
                        <span style="color: red">*</span>
                        <label>具体地址：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" style="width: 500px" name="address" autocomplete="off" id="address" onclick="" placeholder="请输入具体地址">
                        </div>
                    </div>
                </li>
            </ul>
            <input type="hidden" name="formToken" value="${formToken}"/>
            <div class="add-tijiao tcenter">
                <button type="button" class="bt-bg-style bg-light-grey" onclick="closeTab();">取消</button>
                <button type="button" class="bt-bg-style bg-light-blue" onclick="editSubmit();">提交</button>
            </div>
        </div>
        <input class="child" type="hidden" id="layerIndex">
    </form>
</div>
</body>
