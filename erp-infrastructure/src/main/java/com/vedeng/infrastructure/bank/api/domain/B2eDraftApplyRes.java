package com.vedeng.infrastructure.bank.api.domain;

import com.vedeng.infrastructure.bank.api.domain.base.BankResponse;
import lombok.*;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 融资申请返回体
 * @date 2024/10/12 11:26
 */
@Getter
@Setter
@XmlRootElement(name = "CMBC")
@XmlAccessorType(XmlAccessType.FIELD)
public class B2eDraftApplyRes extends BankResponse {

    /**
     * 响应体
     */
    @XmlElement(name = "xDataBody")
    private B2eBasicDraftApplyResBody xDataBody;

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class B2eBasicDraftApplyResBody {

        /**
         * 银行渠道交易流水号
         */
        private String svrId;

        /**
         * 客户业务请求流水号，同一业务请求请勿重复
         */
        private String insId;

        /**
         * 客户技术请求流水号，同一客户请勿重复
         */
        private String trnId;

        /**
         * 错误码
         * 1：成功
         * 0：失败
         */
        private String retCode;

        /**
         * 返回信息
         * 1：交易成功
         * 0：具体失败错误信息
         */
        private String retMsg;
    }


}
