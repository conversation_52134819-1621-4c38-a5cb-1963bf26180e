<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.doc.mapper.DocSupplierMapper">


    <select id="selectListByBrandId" resultType="com.vedeng.doc.domain.entity.DocSupplier">
        SELECT supplier.*
        FROM T_DOC_SUPPLIER supplier
            LEFT JOIN T_DOC_SUPPLIER_BRAND brand ON supplier.ID = brand.DOC_SUPPLIER_ID
        WHERE brand.BRAND_ID = #{brandId,jdbcType=INTEGER}
            and supplier.DISABLE = 0
            and supplier.IS_DELETE = 0
        ORDER BY supplier.ADD_TIME ASC
    </select>
    <select id="selectByPrimaryKey" resultType="com.vedeng.doc.domain.entity.DocSupplier">
        select *
        from T_DOC_SUPPLIER
        where ID = #{docSupplierId,jdbcType=INTEGER}
          and IS_DELETE = 0
          and DISABLE = 0
    </select>
</mapper>
