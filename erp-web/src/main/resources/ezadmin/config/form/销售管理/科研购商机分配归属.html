<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>科研商机分配</title> 
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="kyg_sj" datasource="erp-datasourcetarget" success_url="reload"> 
  <div id="APPEND_HEAD" class="append"></div> 
  <div class="layui-container"> 
   <form id="inputForm" method="post" class="layui-form"> 
    <div class="layui-card" group_name="EZ_DEFAULT_GROUP"> 
     <div class="layui-card-header">
       EZ_DEFAULT_GROUP 
     </div> 
     <div class="layui-card-body"> 
      <div class="layui-form-item "><label class="layui-form-label">归属销售</label>
       <div class="layui-input-block form-group"><select class="form-item" name="BELONG_SALES_ID" type="search" value="" item_name="BELONG_SALES_ID" placeholder="" style="" alias="" jdbctype="" data="select A.USER_ID K,A.USERNAME V from T_USER A LEFT JOIN T_R_USER_POSIT TRUP on A.USER_ID = TRUP.USER_ID
LEFT JOIN T_POSITION C ON C.POSITION_ID=TRUP.POSITION_ID
LEFT JOIN T_ORGANIZATION D ON D.ORG_ID=C.ORG_ID
WHERE D.ORG_NAME LIKE '科研购事业部%' AND A.IS_DISABLED=0" datatype="KVSQL" lay_verify="required" item_desc="" group_data="" valid_rule="" valid_msg="" item_max_upload=""></select>
       </div>
      </div>
      <div class="layui-form-item ">
       <label class="layui-form-label">_CHECKD_IDS</label>
       <div class="layui-input-block form-group">
        <input class="form-item" name="_CHECKD_IDS" type="hidden" value="" item_name="_CHECKD_IDS" placeholder="" style="display:none" alias="" jdbctype="" data="" datatype="" lay_verify="" item_desc="" group_data="" valid_rule="" valid_msg="" item_max_upload="">
       </div>
      </div>
     </div> 
    </div> 
   </form> 
   <form id="configForm" class="layui-form"> 
    <div class="layui-card" group_name="表单数据配置"> 
     <div class="layui-card-header">
       表单数据配置 
     </div> 
     <div class="layui-card-body"> 
     </div> 
    </div> 
    <div class="layui-form-item ">
     <label class="layui-form-label">初始表达式</label> 
     <div class="layui-input-block form-group"> 
      <pre class="form-item layui-code" id="init" name="INIT_EXPRESS" type="">select 1</pre> 
     </div> 
    </div>
    <div class="layui-form-item "> <label class="layui-form-label">提交表达式</label> 
     <div class="layui-input-block form-group"> 
      <pre class="form-item layui-code" id="submit" name="SUBMIT_EXPRESS" type="">StringBuilder sql=new StringBuilder();

ids=$("_CHECKD_IDS");


sql.append(" update  T_ACTIVITY_PRE_ORDER  set BELONG_SALES_ID=#{BELONG_SALES_ID} 
  where ACTIVITY_PRE_ORDER_ID in("+ids+")");

update(sql);
return 1;</pre> 
     </div> 
    </div>
    <div class="layui-form-item "> <label class="layui-form-label">删除表达式</label> 
     <div class="layui-input-block form-group"> 
      <pre class="form-item layui-code" id="delete" name="DELETE_EXPRESS" type=""></pre> 
     </div> 
    </div>
    <div class="layui-form-item ">
     <label class="layui-form-label">分组表达式</label> 
     <div class="layui-input-block form-group"> 
      <pre class="form-item layui-code" name="GROUP_DATA" type=""></pre> 
     </div> 
    </div>
   </form> 
  </div> 
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script> 
  <script>
    layui.use(function () {

    })
</script> 
  <div id="APPEND_FOOT" class="append"> 
  </div> 
 </body>
</html>