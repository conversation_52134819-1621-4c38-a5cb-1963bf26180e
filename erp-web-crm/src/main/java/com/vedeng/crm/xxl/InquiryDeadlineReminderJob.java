package com.vedeng.crm.xxl;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.crm.common.domain.DeadlineRole;
import com.vedeng.crm.common.enums.TaskTypeEnum;
import com.vedeng.crm.task.service.TaskService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 初步产品方案、单品询价、综合询价-临期提醒与超时提醒
 */
@Component
@JobHandler(value="InquiryDeadlineReminderJob")
@Slf4j
public class InquiryDeadlineReminderJob extends AbstractJobHandler {

    @Autowired
    private TaskService taskService;
    
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("==================初步产品方案、单品询价、综合询价-临期提醒与超时提醒job开始====================");
        // 临期提醒
        taskService.deadlineReminder();
        // 超时提醒
        taskService.timeoutReminder();
        XxlJobLogger.log("==================初步产品方案、单品询价、综合询价-临期提醒与超时提醒job结束====================");
        return SUCCESS;
    }
}
