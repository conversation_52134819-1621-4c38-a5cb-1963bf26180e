package com.vedeng.common.core.utils.numgenerator;

import cn.hutool.core.util.StrUtil;
import com.vedeng.common.core.utils.numgenerator.bean.NoGeneratorBean;
import com.vedeng.common.redis.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Calendar;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description 时间信息（yyMMdd）+ redis 当日顺序码（X位，数字）
 * @date 2022/9/27 17:31
 **/
@Slf4j
public class RedisNumGenerator extends NumGenerator{


    @Override
    public String compose(NoGeneratorBean t) {
        return this.getDateFormatNo(t) + this.getDayAutoIncrementNoByRedis(t);
    }

    @Override
    public String getPrefix(NoGeneratorBean t) {
        if (StrUtil.isNotEmpty(t.getPrefix())) {
            return t.getPrefix();
        }
        return "";
    }

    @Override
    public boolean persistence() {
        return false;
    }


}
