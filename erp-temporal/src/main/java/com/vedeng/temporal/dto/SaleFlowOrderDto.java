package com.vedeng.temporal.dto;

import java.math.BigDecimal;
import java.util.List;

public class SaleFlowOrderDto {
    private String additionalClause;
    private String comments;
    private String companyName;
    private Integer delayDelivery;
    private String deliveryDirect;
    private String deliveryLevel1Id;
    private String deliveryLevel2Id;
    private String deliveryLevel3Id;
    private Integer deliveryType;
    private String deliveryUserAddress;
    private String deliveryUserArea;
    private String deliveryUserName;
    private String deliveryUserPhone;
    private String deliveryUserTel;
    private List<SaleFlowOrderGoodsDto> goodsList;
    private Integer invoiceMethod;
    private String invoiceTraderContactMobile;
    private String invoiceTraderContactName;
    private Integer invoiceType;
    private Integer isCoupons;
    private Integer isPrintout;
    private Integer isSendInvoice;
    private String logisticsComments;
    private String orderNo;
    private String phone;
    private String remakes;
    private Integer totalCouponedAmount;
    private BigDecimal totalMoney;
    private Integer traderId;
    private String username;

    /**
     * 是否暂缓开票
     */
    private Integer isDelayInvoice;

    public Integer getIsDelayInvoice() {
        return isDelayInvoice;
    }

    public void setIsDelayInvoice(Integer isDelayInvoice) {
        this.isDelayInvoice = isDelayInvoice;
    }


    // getter/setter
    public String getAdditionalClause() { return additionalClause; }
    public void setAdditionalClause(String additionalClause) { this.additionalClause = additionalClause; }
    public String getComments() { return comments; }
    public void setComments(String comments) { this.comments = comments; }
    public String getCompanyName() { return companyName; }
    public void setCompanyName(String companyName) { this.companyName = companyName; }
    public Integer getDelayDelivery() { return delayDelivery; }
    public void setDelayDelivery(Integer delayDelivery) { this.delayDelivery = delayDelivery; }
    public String getDeliveryDirect() { return deliveryDirect; }
    public void setDeliveryDirect(String deliveryDirect) { this.deliveryDirect = deliveryDirect; }
    public String getDeliveryLevel1Id() { return deliveryLevel1Id; }
    public void setDeliveryLevel1Id(String deliveryLevel1Id) { this.deliveryLevel1Id = deliveryLevel1Id; }
    public String getDeliveryLevel2Id() { return deliveryLevel2Id; }
    public void setDeliveryLevel2Id(String deliveryLevel2Id) { this.deliveryLevel2Id = deliveryLevel2Id; }
    public String getDeliveryLevel3Id() { return deliveryLevel3Id; }
    public void setDeliveryLevel3Id(String deliveryLevel3Id) { this.deliveryLevel3Id = deliveryLevel3Id; }
    public Integer getDeliveryType() { return deliveryType; }
    public void setDeliveryType(Integer deliveryType) { this.deliveryType = deliveryType; }
    public String getDeliveryUserAddress() { return deliveryUserAddress; }
    public void setDeliveryUserAddress(String deliveryUserAddress) { this.deliveryUserAddress = deliveryUserAddress; }
    public String getDeliveryUserArea() { return deliveryUserArea; }
    public void setDeliveryUserArea(String deliveryUserArea) { this.deliveryUserArea = deliveryUserArea; }
    public String getDeliveryUserName() { return deliveryUserName; }
    public void setDeliveryUserName(String deliveryUserName) { this.deliveryUserName = deliveryUserName; }
    public String getDeliveryUserPhone() { return deliveryUserPhone; }
    public void setDeliveryUserPhone(String deliveryUserPhone) { this.deliveryUserPhone = deliveryUserPhone; }
    public String getDeliveryUserTel() { return deliveryUserTel; }
    public void setDeliveryUserTel(String deliveryUserTel) { this.deliveryUserTel = deliveryUserTel; }
    public List<SaleFlowOrderGoodsDto> getGoodsList() { return goodsList; }
    public void setGoodsList(List<SaleFlowOrderGoodsDto> goodsList) { this.goodsList = goodsList; }
    public Integer getInvoiceMethod() { return invoiceMethod; }
    public void setInvoiceMethod(Integer invoiceMethod) { this.invoiceMethod = invoiceMethod; }
    public String getInvoiceTraderContactMobile() { return invoiceTraderContactMobile; }
    public void setInvoiceTraderContactMobile(String invoiceTraderContactMobile) { this.invoiceTraderContactMobile = invoiceTraderContactMobile; }
    public String getInvoiceTraderContactName() { return invoiceTraderContactName; }
    public void setInvoiceTraderContactName(String invoiceTraderContactName) { this.invoiceTraderContactName = invoiceTraderContactName; }
    public Integer getInvoiceType() { return invoiceType; }
    public void setInvoiceType(Integer invoiceType) { this.invoiceType = invoiceType; }
    public Integer getIsCoupons() { return isCoupons; }
    public void setIsCoupons(Integer isCoupons) { this.isCoupons = isCoupons; }
    public Integer getIsPrintout() { return isPrintout; }
    public void setIsPrintout(Integer isPrintout) { this.isPrintout = isPrintout; }
    public Integer getIsSendInvoice() { return isSendInvoice; }
    public void setIsSendInvoice(Integer isSendInvoice) { this.isSendInvoice = isSendInvoice; }
    public String getLogisticsComments() { return logisticsComments; }
    public void setLogisticsComments(String logisticsComments) { this.logisticsComments = logisticsComments; }
    public String getOrderNo() { return orderNo; }
    public void setOrderNo(String orderNo) { this.orderNo = orderNo; }
    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }
    public String getRemakes() { return remakes; }
    public void setRemakes(String remakes) { this.remakes = remakes; }
    public Integer getTotalCouponedAmount() { return totalCouponedAmount; }
    public void setTotalCouponedAmount(Integer totalCouponedAmount) { this.totalCouponedAmount = totalCouponedAmount; }
    public BigDecimal getTotalMoney() { return totalMoney; }
    public void setTotalMoney(BigDecimal totalMoney) { this.totalMoney = totalMoney; }
    public Integer getTraderId() { return traderId; }
    public void setTraderId(Integer traderId) { this.traderId = traderId; }
    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }
} 