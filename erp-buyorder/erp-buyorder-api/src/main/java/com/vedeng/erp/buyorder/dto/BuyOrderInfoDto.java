package com.vedeng.erp.buyorder.dto;

import lombok.Data;

/**
 * 订单主表信息，可扩充
 */
@Data
public class BuyOrderInfoDto {

    private Integer buyorderId;

    private String buyorderNo;

    /**
     * 订单状态：0待确认（默认）、1进行中、2已完结、3已关闭
     */
    private Integer status;

    /**
     * 审核状态 是否生效 0否 1是
     */
    private Integer validStatus;

    /**
     * 生效时间
     */
    private Long validTime;

    /**
     * 付款状态 付款状态 0未付款 1部分付款 2全部付款
     */
    private Integer paymentStatus;

    /**
     * 供应商ID
     */
    private Integer traderId;

    /**
     * 订单类型 0销售订单采购 1备货订单采购
     */
    private Integer orderType;

    /**
     * 锁定状态 0未锁定 1锁定
     */
    private Integer lockedStatus;
    
    /**
     * 收票状态0未收票 1部分收票 2全部收票
     */
    private Integer invoiceStatus;
    
    /**
     * 收货状态 0未收货 1部分收货 2全部收货
     */
    private Integer arrivalStatus;
}
