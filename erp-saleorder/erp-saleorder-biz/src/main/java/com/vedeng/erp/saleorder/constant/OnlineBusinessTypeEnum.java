package com.vedeng.erp.saleorder.constant;

/**
 * 在线开票操作类型枚举
 *
 * <AUTHOR>
 */
public enum OnlineBusinessTypeEnum {
    /**
     * 在线签收
     */
    ONLINE_EXPRESS_RECEIPT(1, "在线签收"),

    /**
     * 在线开票
     */
    ONLINE_INVOICE_OPEN(2, "在线开票"),

    /**
     * 默认枚举
     */
    DEFAULT_ENUM(0, "默认枚举");


    /**
     * 代码
     */
    private Integer code;

    /**
     * 描述
     */
    private String desc;

    OnlineBusinessTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * code获取操作类型
     *
     * @param code
     * @return
     */
    public static OnlineBusinessTypeEnum getInstance(Integer code) {
        for (OnlineBusinessTypeEnum v : values()) {
            if (v.code.equals(code)) {
                return v;
            }
        }
        return DEFAULT_ENUM;
    }
}
