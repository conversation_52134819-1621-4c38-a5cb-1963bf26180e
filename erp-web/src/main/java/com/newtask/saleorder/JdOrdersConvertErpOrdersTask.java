package com.newtask.saleorder;

import com.google.common.collect.Lists;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.Constants;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.trace.data.TrackParamsData;
import com.vedeng.common.trace.enums.EventTrackingEnum;
import com.vedeng.common.trace.track.TrackStrategy;
import com.vedeng.common.trace.track.factory.TrackStrategyFactory;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.customerbillperiod.dto.CustomerBillPeriodAvailableAmountDto;
import com.vedeng.customerbillperiod.dto.CustomerBillPeriodAvailableAmountQueryDto;
import com.vedeng.customerbillperiod.service.CustomerBillPeriodService;
import com.vedeng.erp.saleorder.dao.RegionJdMapper;
import com.vedeng.erp.saleorder.dao.VJdGoodsMapper;
import com.vedeng.erp.saleorder.dao.VJdSaleorderMapper;
import com.vedeng.erp.saleorder.dto.OrderTerminalDto;
import com.vedeng.erp.saleorder.dto.SaleOrderTerminalDto;
import com.vedeng.erp.saleorder.model.po.RegionJd;
import com.vedeng.erp.saleorder.model.po.VJdSaleorder;
import com.vedeng.erp.saleorder.service.OrderTerminalApiService;
import com.vedeng.erp.saleorder.service.OrderTerminalService;
import com.vedeng.erp.trader.dto.TraderContactDto;
import com.vedeng.erp.trader.service.TraderContactService;
import com.vedeng.goods.dto.CoreSkuDto;
import com.vedeng.goods.mapper.CoreSkuMapper;
import com.vedeng.order.api.constant.OrderChannelEnum;
import com.vedeng.order.api.constant.ProductionLineEnum;
import com.vedeng.order.api.constant.TraderTypeEnum;
import com.vedeng.order.api.fallback.OrderFallbackService;
import com.vedeng.order.api.order.dto.SaleOrderNoGenerateReqDto;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.vo.OrderData;
import com.vedeng.order.model.vo.OrderGoodsData;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.system.service.UserService;
import com.vedeng.trader.dao.TraderCustomerMapper;
import com.vedeng.trader.dao.TraderMapper;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.model.TraderCustomer;
import com.vedeng.trader.service.TraderCustomerService;
import com.wms.service.LogicalSaleorderChooseService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Description 京东订单转换为Erp订单
 * <AUTHOR>
 */
@Component
@JobHandler(value = "jdOrdersConvertErpOrdersTask")
public class JdOrdersConvertErpOrdersTask extends AbstractJobHandler {

    @Autowired
    private CoreSkuMapper coreSkuMapper;

    @Autowired
    private VJdSaleorderMapper vJdSaleorderMapper;

    @Autowired
    private VJdGoodsMapper vJdGoodsMapper;

    @Autowired
    private RegionJdMapper regionJdMapper;

    @Autowired
    private UserService userService;

    @Autowired
    @Qualifier("userMapper")
    private UserMapper userMapper;

    @Autowired
    private SaleorderService saleorderService;

    @Autowired
    private OrderFallbackService orderFallbackService;

    @Autowired
    private TraderMapper traderMapper;

//    @Value("${jd_order_trader_phone}")
//    private String PHONE;

    public static final Logger logger = LoggerFactory.getLogger(JdOrdersConvertErpOrdersTask.class);
    @Autowired
    private TraderCustomerMapper traderCustomerMapper;
    @Autowired
    private LogicalSaleorderChooseService logicalSaleorderChooseServiceImpl;
    @Resource
    private SaleorderMapper saleorderMapper;
//    @Value("${jd.upload.tradeId:194723}")
//    private Integer traderId; //北京京东弘健健康有限公司

    @Resource
    private CustomerBillPeriodService customerBillPeriodService;
    @Resource
    private   TraderCustomerService traderCustomerService;

    @Resource
    private TraderContactService traderContactService;

    @Autowired
    private OrderTerminalApiService orderTerminalApiService;


    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        XxlJobLogger.log("===================京东订单转换为Erp订单任务开始===================");
        // 从JD订单中获取未生成Erp订单的数据（每次时间升序取1000条）
        List<VJdSaleorder> jdSaleorderList = vJdSaleorderMapper.findByOrderGenerateStatusInAndIsDeleteFalseOrderByAddTime(Lists.newArrayList(1));
        if (CollectionUtils.isEmpty(jdSaleorderList)) {
            XxlJobLogger.log("本次任务查询出的待生成京东订单数量为0");
            return ReturnT.SUCCESS;
        }


        AtomicInteger total = new AtomicInteger(0);
        AtomicInteger success = new AtomicInteger(0);
        AtomicInteger error = new AtomicInteger(0);
        for(int i=0;i<jdSaleorderList.size();i++){ //不使用并发
            VJdSaleorder jdSaleorder=jdSaleorderList.get(i);
            try {
                logger.info("JD订单={},开始封装saleorder信息", jdSaleorder);
                Integer traderId = jdSaleorder.getTraderId();
                User belongUser = traderCustomerService.getPersonalUser(traderId);
                User saleInfo = userMapper.getUserByName(belongUser.getUsername());//获取归属销售的部门信息
                if(saleInfo.getOrgId() == null){
                    XxlJobLogger.log("客户id{},归属人三级部门部门信息为空",traderId);
                    doError(jdSaleorder.getSaleorderId(), "归属人部门信息不完整");
                    return ReturnT.FAIL;
                }
                belongUser.setTraderId(traderId);
                belongUser.setOrgId(saleInfo.getOrgId());
                belongUser.setOrgName(saleInfo.getOrgName());

                TraderContactDto traderContactDto = traderContactService.queryTraderContractDefault(traderId);


                //User belongUser = userService.getBDUserInfoByMobile(PHONE);
                if (belongUser == null || traderContactDto == null) {
                    XxlJobLogger.log("京东订单转换找不到客户ID为：{}对应的注册用户信息或客户未查询到", traderId);
                    doError(jdSaleorder.getSaleorderId(), "归属人部门信息不完整");
                    return ReturnT.FAIL;
                }


                // 封装saleorder信息
                OrderData orderData = this.encapsulateSaleOrderInfo(jdSaleorder, belongUser,traderContactDto );
                logger.info("JD订单号={},封装orderData信息为={}", jdSaleorder.getJdSaleorderNo(), orderData);
                //判断账期
                CustomerBillPeriodAvailableAmountQueryDto queryParam = new CustomerBillPeriodAvailableAmountQueryDto();
                queryParam.setCompanyId(1);
                queryParam.setOrderId(null);
                TraderCustomer customer = traderCustomerMapper.getTraderCustomerByTraderId(belongUser.getTraderId());
                if(customer==null){
                    XxlJobLogger.log("客户变更");
                    doError(jdSaleorder.getSaleorderId(), "客户信息不完整");
                    error.incrementAndGet();
                    continue;
                }
                queryParam.setCustomerId(Long.parseLong(customer.getTraderCustomerId()+"") );
                CustomerBillPeriodAvailableAmountDto result = customerBillPeriodService.getCustomerBillPeriodAvailableAmount(queryParam);

                if (Objects.isNull(result) || Objects.isNull(result.getTotalAvailableAmount())) {
                    XxlJobLogger.log("账期不足");
                    VJdSaleorder errorJdSaleorder = new VJdSaleorder();
                    errorJdSaleorder.setSaleorderId(jdSaleorder.getSaleorderId());
                    errorJdSaleorder.setErrorReason("账期不足");
                    errorJdSaleorder.setModTime(new Date());
                    vJdSaleorderMapper.updateByPrimaryKeySelective(errorJdSaleorder);
                    error.incrementAndGet();
                    continue;
                }
                if(result.getTotalAvailableAmount().compareTo(orderData.getTotalCouponedAmount())<0)
                {
                    XxlJobLogger.log("账期不足");
                    VJdSaleorder errorJdSaleorder = new VJdSaleorder();
                    errorJdSaleorder.setSaleorderId(jdSaleorder.getSaleorderId());
                    errorJdSaleorder.setErrorReason("账期不足");
                    errorJdSaleorder.setModTime(new Date());
                    vJdSaleorderMapper.updateByPrimaryKeySelective(errorJdSaleorder);
                    error.incrementAndGet();
                    continue;
                }

                // 调用前台推送保存销售订单接口
                Saleorder saleorder = saleorderService.saveJDAddSaleorder(orderData,belongUser);

                if(Objects.nonNull(saleorder)){
                    addTrackCreateSaleOrder(saleorder.getSaleorderNo(),saleorder.getTraderId());
                }

                logger.info("JD订单号={},生成的saleorder信息为={}", jdSaleorder.getJdSaleorderNo(), saleorder);

                //VDERP-16215  京东订单导入逻辑优化。
                OrderTerminalDto orderTerminalDto = new OrderTerminalDto();
                orderTerminalDto.setBusinessId(saleorder.getSaleorderId());
                orderTerminalDto.setBusinessType(0);//0销售订单
                orderTerminalDto.setDwhTerminalId(orderData.getTerminalTraderId());
                orderTerminalDto.setTerminalName(orderData.getTerminalTraderName());
                orderTerminalDto.setIsDeleted(0);
                orderTerminalDto.setAddTime(new Date());
                orderTerminalDto.setModTime(new Date());
                orderTerminalApiService.save(orderTerminalDto);




                // 根据结果更新JDSaleorder信息
                VJdSaleorder successJdSaleorder = new VJdSaleorder();
                successJdSaleorder.setSaleorderId(jdSaleorder.getSaleorderId());
                successJdSaleorder.setSaleorderNo(saleorder.getSaleorderNo());
                successJdSaleorder.setOrderGenerateStatus(Constants.FIVE);
                successJdSaleorder.setVdSaleorderId(Long.parseLong(saleorder.getSaleorderId()+""));
                successJdSaleorder.setModTime(new Date());
                vJdSaleorderMapper.updateByPrimaryKeySelective(successJdSaleorder);
                success.incrementAndGet();
            } catch (IllegalArgumentException WW) {
                logger.error("JD订单生成Erp订单发生异常,jdSaleorder={}", jdSaleorder, WW);
                VJdSaleorder errorJdSaleorder = new VJdSaleorder();
                errorJdSaleorder.setSaleorderId(jdSaleorder.getSaleorderId());
                errorJdSaleorder.setOrderGenerateStatus(Constants.THREE);
                errorJdSaleorder.setErrorReason(WW.getMessage());
                errorJdSaleorder.setModTime(new Date());
                vJdSaleorderMapper.updateByPrimaryKeySelective(errorJdSaleorder);
                error.incrementAndGet();
            } catch (Exception e) {
                logger.error("JD订单生成Erp订单发生异常,jdSaleorder={}", jdSaleorder, e);
                VJdSaleorder errorJdSaleorder = new VJdSaleorder();
                errorJdSaleorder.setSaleorderId(jdSaleorder.getSaleorderId());
                errorJdSaleorder.setOrderGenerateStatus(Constants.THREE);
                errorJdSaleorder.setErrorReason("服务器异常，请联系开发人员");
                errorJdSaleorder.setModTime(new Date());
                vJdSaleorderMapper.updateByPrimaryKeySelective(errorJdSaleorder);
                error.incrementAndGet();
            } finally {
                total.incrementAndGet();
            }
        };
        XxlJobLogger.log("京东订单转换为Erp订单任务结束:一共读取到{}条数据，处理了{}条数据，其中{}条成功，{}条失败。"
                , jdSaleorderList.size(), total.get(), success.get(), error.get());
        return ReturnT.SUCCESS;
    }

    public void doError(Long saleOrderId,String errorMessage){
        VJdSaleorder errorJdSaleorder = new VJdSaleorder();
        errorJdSaleorder.setSaleorderId(saleOrderId);
        errorJdSaleorder.setErrorReason(errorMessage);
        errorJdSaleorder.setModTime(new Date());
        vJdSaleorderMapper.updateByPrimaryKeySelective(errorJdSaleorder);

    }


    @Autowired
    private TrackStrategyFactory trackStrategyFactory;

    private void addTrackCreateSaleOrder( String orderNo, Integer traderId) {
        EventTrackingEnum eventTrackingEnum = EventTrackingEnum.SALE_CREATE_ORDER_FRONT;
        try {
            TrackParamsData trackParamsData = new TrackParamsData();
            Map<String, Object> trackParams = new HashMap<>();
            trackParams.put("traderId",traderId);
            trackParams.put("orderNo",orderNo);
            TrackStrategy trackStrategy = trackStrategyFactory.getStrategyByType(eventTrackingEnum);
            trackParamsData.setEventTrackingEnum(eventTrackingEnum);
            trackParamsData.setTrackParams(trackParams);
            trackParamsData.setTrackResult(ResultInfo.success());
            trackStrategy.track(trackParamsData);
        }catch(Exception e) {
            logger.error("埋点：{}，失败，不影响正常业务",eventTrackingEnum.getArchivedName(),e);
        }
    }


    private OrderData encapsulateSaleOrderInfo(VJdSaleorder jdSaleorder, User belongUser,TraderContactDto traderContactDto) throws Exception {
        // 生成订单编号
        Integer traderId = belongUser.getTraderId();
        SaleOrderNoGenerateReqDto saleOrderNoGenerateReqDto = new SaleOrderNoGenerateReqDto();
        saleOrderNoGenerateReqDto.setProductionLineCode(ProductionLineEnum.B2B.getCode());
        saleOrderNoGenerateReqDto.setOrderChannelCode(OrderChannelEnum.ON_LINE.getCode());
        saleOrderNoGenerateReqDto.setTraderTypeCode(TraderTypeEnum.DISTRIBUTION.getCode());
        saleOrderNoGenerateReqDto.setTraderId(traderId);
        String saleOrderNo = orderFallbackService.saleOrderNoGenerate(saleOrderNoGenerateReqDto);
        Saleorder saleorder = new Saleorder();
        saleorder.setSaleorderNo(saleOrderNo);
        Saleorder saleorderBySaleorderNo = saleorderService.getSaleorderBySaleorderNo(saleorder);
        if (saleorderBySaleorderNo != null) {
            throw new IllegalArgumentException("订单号重复");
        }
        // 封装orderData信息
        OrderData orderData = new OrderData();
        orderData.setPhone(traderContactDto.getMobile());
        orderData.setOrderNo(saleOrderNo);
        Trader traderInfo = traderMapper.getTraderInfoByTraderId(traderId);
        if (traderInfo == null) {
            throw new IllegalArgumentException("未查询到交易者信息");
        }
        orderData.setTraderId(traderId);
        orderData.setUsername(traderInfo.getTraderName());
        orderData.setDeliveryUserName(jdSaleorder.getArrivalUserName());

        orderData.setDeliveryUserArea(jdSaleorder.getProvince() + " " + jdSaleorder.getCity() + " " + jdSaleorder.getCounty());

        orderData.setDeliveryUserAddress(jdSaleorder.getConsigneeAddress());
        orderData.setDeliveryUserTel(jdSaleorder.getArrivalUserPhone());
        orderData.setDeliveryUserPhone(jdSaleorder.getArrivalUserPhone());
        orderData.setRemakes(jdSaleorder.getRemark());
        orderData.setTotalCouponedAmount(jdSaleorder.getPurchasingPrice().multiply(BigDecimal.valueOf(jdSaleorder.getNum())));

        orderData.setOrderStatus(Constants.ZERO);
        orderData.setOrderSrc(Constants.ONE);
        orderData.setJxSalePrice(BigDecimal.ZERO);
        RegionJd regionJd = regionJdMapper.findFirstByVdRegionId(jdSaleorder.getRegionId());
        if (regionJd == null) {
            throw new IllegalArgumentException("未查询到地区信息");
        }
        orderData.setDeliveryLevel3Id(String.valueOf(regionJd.getVdRegionId()));
        orderData.setDeliveryLevel2Id(String.valueOf(regionJd.getVdCityId()));
        orderData.setDeliveryLevel1Id(String.valueOf(regionJd.getVdProvinceId()));
        orderData.setIsCoupons(Constants.ZERO);
        orderData.setCouponInfo(null);

        CoreSkuDto coreSku = coreSkuMapper.getCoreSkuInfoBySkuNo(jdSaleorder.getSkuNo());
        if (coreSku == null) {
            throw new IllegalArgumentException("未查询到skuId信息");
        }
        CoreSkuDto coreSkuDto = coreSkuMapper.getCoreSkuInfoBySkuId(coreSku.getSkuId());
        if (coreSkuDto == null) {
            throw new IllegalArgumentException("未查询到sku信息");
        }
        OrderGoodsData orderGoodsData = new OrderGoodsData();
        orderGoodsData.setJdSaleorderNo(jdSaleorder.getJdSaleorderNo());
        orderGoodsData.setSkuNo(jdSaleorder.getSkuNo());
        orderGoodsData.setProductNum(jdSaleorder.getNum());
        orderGoodsData.setIsCoupons(Constants.ZERO);
        orderGoodsData.setSkuAmount(jdSaleorder.getPurchasingPrice().multiply(BigDecimal.valueOf(jdSaleorder.getNum())));
        orderGoodsData.setJxSalePrice(jdSaleorder.getPurchasingPrice());
        orderGoodsData.setDeliveryCycle(jdSaleorder.getPurchaseTime());//货期
        orderData.setGoodsList(Lists.newArrayList(orderGoodsData));

        orderData.setInvoiceTraderContactName(traderContactDto.getName());
        orderData.setInvoiceTraderContactTelephone(traderContactDto.getTelephone());
        orderData.setInvoiceTraderContactMobile(traderContactDto.getMobile());
        orderData.setInvoiceTraderAddress("江苏省宿迁市宿豫区顺河街道京东科技园二期（收票）");
        orderData.setInvoiceTraderArea("江苏省 宿迁市 宿豫区");
        orderData.setIsSendInvoice(Constants.ONE);
        orderData.setInvoiceTraderDeliveryLevel1Id("320000");
        orderData.setInvoiceTraderDeliveryLevel2Id("321300");
        orderData.setInvoiceTraderDeliveryLevel3Id("321311");
        orderData.setInvoiceType(972);
        orderData.setPurchaseTime(jdSaleorder.getPurchaseTime());
        orderData.setTerminalTraderId(jdSaleorder.getTerminalTraderId());
        orderData.setTerminalTraderName(jdSaleorder.getTerminalTraderName());


        return orderData;
    }

}
