<%--
  Created by IntelliJ IDEA.
  User: dhs
  Date: 2021/10/22
  Time: 4:55 下午
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<div class="parts">
    <div class="title-container title-container-blue">
        <div class="table-title nobor">基本信息</div>
    </div>
    <table class="table table-bordered table-striped table-condensed table-centered">
        <tbody>
        <tr>
            <td class="table-smaller">修改申请单号</td>
            <td>${saleorderModifyApplyInfo.saleorderModifyApplyNo}</td>
            <td class="table-smaller">销售订单</td>
            <td>
                <a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewsaleorder${saleorder.saleorderId}","link":"./orderstream/saleorder/detail.do?saleOrderId=${saleorder.saleorderId}","title":"订单信息"}'>${saleorder.saleorderNo}</a>
            </td>
        </tr>
        <tr>
            <td>创建时间</td>
            <td><date:date value ="${saleorderModifyApplyInfo.addTime}"/></td>
            <td>审核状态</td>
            <td>
                <c:choose>
                    <c:when test="${saleorderModifyApplyInfo.verifyStatus == null || saleorderModifyApplyInfo.verifyStatus eq 3}">待审核</c:when>
                    <c:when test="${saleorderModifyApplyInfo.verifyStatus eq 0}">审核中</c:when>
                    <c:when test="${saleorderModifyApplyInfo.verifyStatus eq 1}">审核通过</c:when>
                    <c:when test="${saleorderModifyApplyInfo.verifyStatus eq 2}">审核不通过</c:when>
                    <c:otherwise></c:otherwise>
                </c:choose>
            </td>
        </tr>
        </tbody>
    </table>
</div>