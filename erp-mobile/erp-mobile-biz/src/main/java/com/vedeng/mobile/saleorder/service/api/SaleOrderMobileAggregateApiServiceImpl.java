package com.vedeng.mobile.saleorder.service.api;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.vedeng.erp.common.Constants;
import com.vedeng.erp.mobile.api.AfterSalesMobileApiService;
import com.vedeng.erp.mobile.api.SaleOrderGoodsMobileApiService;
import com.vedeng.erp.mobile.api.SaleOrderMobileApiService;
import com.vedeng.erp.mobile.dto.*;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.mobile.dto.*;
import com.vedeng.mobile.query.*;
import com.vedeng.mobile.service.SaleOrderMobileAggregateApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


@Slf4j
@Service
public class SaleOrderMobileAggregateApiServiceImpl implements SaleOrderMobileAggregateApiService {

    @Autowired
    private SaleOrderMobileApiService saleOrderMobileApiService;

    @Autowired
    private SaleOrderGoodsMobileApiService saleOrderGoodsMobileApiService;

    @Autowired
    private AfterSalesMobileApiService afterSalesMobileApiService;

    @Override
    public SaleorderListPageDto querySaleorderList(SaleorderListQuery query) {
        SaleorderListPageDto saleorderListPageDto = new SaleorderListPageDto();

        if (Objects.isNull(query) || Objects.isNull(query.getUserId())) {
            log.info("查询销售订单列表参数错误");
            return saleorderListPageDto;
        }

        // 查询归属信息
        SaleOrderAttributionInfoDto saleOrderAttributionInfo = saleOrderMobileApiService.getSaleOrderAttributionInfo(query.getUserId());
        if (Objects.isNull(saleOrderAttributionInfo)) {
            log.info("查询销售订单列表归属信息为空");
            return saleorderListPageDto;
        }
        List<Integer> currentOrgIdList = saleOrderAttributionInfo.getCurrentOrgIdList();
        List<Integer> currentUserIdList = saleOrderAttributionInfo.getCurrentUserIdList();
        if (CollUtil.isEmpty(currentOrgIdList) && CollUtil.isEmpty(currentUserIdList)) {
            log.info("查询销售订单列表归属信息为空");
            return saleorderListPageDto;
        }
        // 查询销售订单列表
        SaleOrderListQueryDto saleOrderListQueryDto = SaleOrderListQueryDto.builder()
                .currentOrgIdList(currentOrgIdList)
                .currentUserIdList(currentUserIdList)
                .pageNum(query.getPageNum())
                .pageSize(query.getPageSize())
                .orderStatusList(query.getOrderStatusList())
                .searchKey(query.getSearchKey())
                .build();

        PageInfo<SaleOrderListResultDto> saleOrderListResultDtoPageInfo = saleOrderMobileApiService.getSaleOrderListPage(saleOrderListQueryDto);

        if (saleOrderListResultDtoPageInfo.getTotal() == 0) {
            log.info("查询销售订单列表为空");
            return saleorderListPageDto;
        }

        saleorderListPageDto.setPageNum(saleOrderListResultDtoPageInfo.getPageNum());
        saleorderListPageDto.setPageSize(saleOrderListResultDtoPageInfo.getPageSize());
        saleorderListPageDto.setTotalPage(saleOrderListResultDtoPageInfo.getPages());
        saleorderListPageDto.setTotal(Math.toIntExact(saleOrderListResultDtoPageInfo.getTotal()));

        List<SaleOrderListResultDto> saleOrderListPage = saleOrderListResultDtoPageInfo.getList();

        List<SaleorderListDto> saleorderListDtoList = saleOrderListPage.stream().map(SaleOrderMobileAggregateApiServiceImpl::toSaleorderListDto).collect(Collectors.toList());

        List<Integer> saleorderIdList = saleOrderListPage.stream().map(SaleOrderListResultDto::getSaleorderId).collect(Collectors.toList());
        // 查询销售订单商品信息
        List<SaleOrderGoodsListResultDto> saleOrderGoodsList = saleOrderGoodsMobileApiService.getSaleOrderGoodsBySaleorderIds(saleorderIdList);
        Map<Integer, List<SaleOrderGoodsListResultDto>> goodsMap = CollUtil.isEmpty(saleOrderGoodsList) ? Collections.emptyMap() : saleOrderGoodsList.stream().collect(Collectors.groupingBy(SaleOrderGoodsListResultDto::getSaleorderId));
        // 查询售后信息
        List<AfterSalesListResultDto> afterSalesList = afterSalesMobileApiService.getAfterSalesListByOrderIdAndSubjectType(saleorderIdList, Constants.SALES);
        Map<Integer, List<AfterSalesListResultDto>> afterSalesMap = CollUtil.isEmpty(afterSalesList) ? Collections.emptyMap() : afterSalesList.stream().collect(Collectors.groupingBy(AfterSalesListResultDto::getOrderId));
        // 查询发票信息
        List<SaleOrderInvoiceInfoDto> invoiceList = saleOrderMobileApiService.getInvoiceInfoOfSaleOrder(saleorderIdList);
        Map<Integer, List<SaleOrderInvoiceInfoDto>> invoiceMap = CollUtil.isEmpty(invoiceList) ? Collections.emptyMap() : invoiceList.stream().collect(Collectors.groupingBy(SaleOrderInvoiceInfoDto::getRelatedId));

        for (SaleorderListDto saleorderListDto : saleorderListDtoList) {
            // 封装商品信息
            List<SaleOrderGoodsListResultDto> saleOrderGoodsListResultDtos = goodsMap.get(saleorderListDto.getSaleOrderId());
            if (CollUtil.isNotEmpty(saleOrderGoodsListResultDtos)) {
                List<SaleorderListOfGoodsDto> saleorderListOfGoodsDtoList = saleOrderGoodsListResultDtos.stream().map(SaleOrderMobileAggregateApiServiceImpl::toSaleorderListOfGoodsDto).collect(Collectors.toList());
                saleorderListDto.setGoodsNum(saleorderListOfGoodsDtoList.size());
                // 截取前三条数据
                List<SaleorderListOfGoodsDto> firstThreeItems = saleorderListOfGoodsDtoList.subList(0, Math.min(3, saleorderListOfGoodsDtoList.size()));
                saleorderListDto.setSaleOrderGoodsList(firstThreeItems);

                // 查询物流信息
                List<Integer> saleOrderGoodsIds = saleOrderGoodsListResultDtos.stream().map(SaleOrderGoodsListResultDto::getSaleordergoodsId).collect(Collectors.toList());
                List<SaleOrderExpressInfoDto> expressInfoOfSaleOrder = saleOrderMobileApiService.getExpressInfoOfSaleOrder(saleorderListDto.getSaleOrderId(), saleOrderGoodsIds);

                // 是否有物流信息
                saleorderListDto.setHaveExpressInfo(CollUtil.isNotEmpty(expressInfoOfSaleOrder));

            } else {
                // 没有商品信息
                saleorderListDto.setGoodsNum(Constants.ZERO);
                saleorderListDto.setHaveExpressInfo(false);
            }

            // 是否有售后信息
            List<AfterSalesListResultDto> afterSalesListResultDtoList = afterSalesMap.get(saleorderListDto.getSaleOrderId());
            saleorderListDto.setHaveAfterSalesInfo(CollUtil.isNotEmpty(afterSalesListResultDtoList));

            // 是否有发票信息
            Integer invoiceMethod = saleorderListDto.getInvoiceMethod();
            if (Constants.ONE.equals(invoiceMethod) || Constants.TWO.equals(invoiceMethod)) {
                saleorderListDto.setHaveInvoiceInfo(false);
            } else {
                List<SaleOrderInvoiceInfoDto> saleOrderInvoiceInfoDtoList = invoiceMap.get(saleorderListDto.getSaleOrderId());
                saleorderListDto.setHaveInvoiceInfo(CollUtil.isNotEmpty(saleOrderInvoiceInfoDtoList) && saleOrderInvoiceInfoDtoList.stream()
                        .anyMatch(infoDto -> StringUtils.isNotBlank(infoDto.getInvoiceHref()) || StringUtils.isNotBlank(infoDto.getOssFileUrl())));
            }

            // 订单总价
            saleorderListDto.setTotalAmount(saleOrderMobileApiService.getRealTotalAmountOfSaleOrder(saleorderListDto.getSaleOrderId()));

        }

        saleorderListPageDto.setSaleOrderList(saleorderListDtoList);

        return saleorderListPageDto;
    }

    @Override
    public List<SaleorderOfAftersalesListDto> querySaleorderOfAftersaleList(SaleorderOfAftersalesListQuery query) {
        if (Objects.isNull(query) || Objects.isNull(query.getSaleOrderId())) {
            log.info("查询销售订单售后列表参数错误");
            return Collections.emptyList();
        }
        // 查询售后列表
        List<AfterSalesListResultDto> afterSalesListResultDtoList = afterSalesMobileApiService.getAfterSalesListByOrderIdAndSubjectType(Collections.singletonList(query.getSaleOrderId()), Constants.SALES);
        if (CollUtil.isEmpty(afterSalesListResultDtoList)) {
            log.info("查询销售订单售后列表为空");
            return Collections.emptyList();
        }

        List<SaleorderOfAftersalesListDto> saleorderOfAftersalesListDtoList = afterSalesListResultDtoList.stream().map(SaleOrderMobileAggregateApiServiceImpl::toSaleorderOfAftersalesListDto).collect(Collectors.toList());
        // 查询售后商品信息
        List<Integer> afterSalesIds = saleorderOfAftersalesListDtoList.stream().map(SaleorderOfAftersalesListDto::getAfterSalesId).collect(Collectors.toList());
        List<AfterSalesGoodsListResultDto> afterSalesGoodsListResultDtoList = afterSalesMobileApiService.getAfterSalesGoodsListByAfterSalesIds(afterSalesIds);

        if (CollUtil.isNotEmpty(afterSalesGoodsListResultDtoList)) {
            Map<Integer, List<AfterSalesGoodsListResultDto>> goodsMap = afterSalesGoodsListResultDtoList.stream().collect(Collectors.groupingBy(AfterSalesGoodsListResultDto::getAfterSalesId));
            for (SaleorderOfAftersalesListDto saleorderOfAftersalesListDto : saleorderOfAftersalesListDtoList) {
                List<AfterSalesGoodsListResultDto> afterSalesGoodsListResultDtos = goodsMap.get(saleorderOfAftersalesListDto.getAfterSalesId());
                if (CollUtil.isNotEmpty(afterSalesGoodsListResultDtos)) {
                    List<SaleorderOfAftersalesGoodsListDto> saleorderOfAftersalesGoodsListDtoList = afterSalesGoodsListResultDtos.stream().map(SaleOrderMobileAggregateApiServiceImpl::toSaleorderOfAftersalesGoodsListDto).collect(Collectors.toList());
                    saleorderOfAftersalesListDto.setAfterSalesGoodsList(saleorderOfAftersalesGoodsListDtoList);
                }
            }
        }
        return saleorderOfAftersalesListDtoList;
    }

    @Override
    public SaleorderDetailDto querySaleorderDetail(SaleorderDetailQuery query) {
        if (Objects.isNull(query) || Objects.isNull(query.getSaleOrderId())) {
            log.info("查询销售订单详情参数错误");
            return null;
        }
        SaleOrderDetailInfoDto saleOrderDetail = saleOrderMobileApiService.getSaleOrderDetail(query.getSaleOrderId());
        if (Objects.isNull(saleOrderDetail)) {
            log.info("查询销售订单详情为空");
            return null;
        }
        SaleorderDetailDto saleorderDetailDto = toSaleorderDetailDto(saleOrderDetail);

        // 封装销售订单商品信息
        List<SaleOrderGoodsListResultDto> saleOrderGoodsList = saleOrderGoodsMobileApiService.getSaleOrderGoodsBySaleorderIds(Collections.singletonList(query.getSaleOrderId()));
        if (CollUtil.isNotEmpty(saleOrderGoodsList)) {
            List<SaleorderListOfGoodsDto> saleorderListOfGoodsDtoList = saleOrderGoodsList.stream().map(SaleOrderMobileAggregateApiServiceImpl::toSaleorderListOfGoodsDto).collect(Collectors.toList());
            saleorderDetailDto.setSaleOrderGoodsList(saleorderListOfGoodsDtoList);
            // 查询物流信息
            List<Integer> saleOrderGoodsIdList = saleorderListOfGoodsDtoList.stream().map(SaleorderListOfGoodsDto::getSaleOrderGoodsId).collect(Collectors.toList());
            List<SaleOrderExpressInfoDto> expressInfoOfSaleOrder = saleOrderMobileApiService.getExpressInfoOfSaleOrder(query.getSaleOrderId(), saleOrderGoodsIdList);
            // 是否有物流信息
            saleorderDetailDto.setHaveExpressInfo(CollUtil.isNotEmpty(expressInfoOfSaleOrder));
        } else {
            // 没有商品信息
            saleorderDetailDto.setHaveExpressInfo(false);
        }
        // 设置订单总价
        BigDecimal realTotalAmountOfSaleOrder = saleOrderMobileApiService.getRealTotalAmountOfSaleOrder(query.getSaleOrderId());
        saleorderDetailDto.setTotalAmount(realTotalAmountOfSaleOrder);
        // 是否有售后信息
        List<AfterSalesListResultDto> afterSalesList = afterSalesMobileApiService.getAfterSalesListByOrderIdAndSubjectType(Collections.singletonList(query.getSaleOrderId()), Constants.SALES);
        saleorderDetailDto.setHaveAfterSalesInfo(CollUtil.isNotEmpty(afterSalesList));
        // 是否有发票信息
        Integer invoiceMethod = saleOrderDetail.getInvoiceMethod();
        if (Constants.ONE.equals(invoiceMethod) || Constants.TWO.equals(invoiceMethod)) {
            saleorderDetailDto.setInvoiceUrl(Collections.emptyList());
        } else {
            List<SaleOrderInvoiceInfoDto> invoiceInfoList = saleOrderMobileApiService.getInvoiceInfoOfSaleOrder(Collections.singletonList(query.getSaleOrderId()));
            if (CollUtil.isNotEmpty(invoiceInfoList) && invoiceInfoList.stream().anyMatch(infoDto -> StringUtils.isNotBlank(infoDto.getInvoiceHref()) || StringUtils.isNotBlank(infoDto.getOssFileUrl()))) {
                List<String> invoiceUrl = invoiceInfoList.stream()
                        .map(invoice -> StringUtils.isNotBlank(invoice.getOssFileUrl()) ? invoice.getOssFileUrl() :
                                StringUtils.isNotBlank(invoice.getInvoiceHref()) ? invoice.getInvoiceHref() : "")
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(invoiceUrl)) {
                    AtomicInteger i = new AtomicInteger(1);
                    List<ResourceFileDto> resourceFileDtoList = invoiceUrl.stream().map(url -> {
                        ResourceFileDto resourceFileDto = new ResourceFileDto();
                        resourceFileDto.setUrl(url);
                        resourceFileDto.setFileType(Constants.PDF);
                        resourceFileDto.setFileName(String.format(Constants.INVOICE_FILE_NAME, saleorderDetailDto.getSaleOrderNo(), i.getAndIncrement()));
                        resourceFileDto.setIsInternalResource(url.contains(Constants.FILE_VEDENG_COM) || url.contains(Constants.FILE_IVEDENG_COM));
                        return resourceFileDto;
                    }).collect(Collectors.toList());
                    saleorderDetailDto.setInvoiceUrl(resourceFileDtoList);
                }
            } else {
                saleorderDetailDto.setInvoiceUrl(Collections.emptyList());
            }
        }
        return saleorderDetailDto;
    }

    @Override
    public List<SaleorderOfLogisticsInfoDto> querylogisticsInfo(SaleorderOfLogisticsInfoQuery query) {
        if (Objects.isNull(query) || Objects.isNull(query.getSaleOrderId())) {
            log.info("查询销售订单物流信息参数错误");
            return Collections.emptyList();
        }
        List<SaleOrderGoodsListResultDto> saleOrderGoodsList = saleOrderGoodsMobileApiService.getSaleOrderGoodsBySaleorderIds(Collections.singletonList(query.getSaleOrderId()));
        if (CollUtil.isEmpty(saleOrderGoodsList)) {
            log.info("查询销售订单商品信息为空");
            return Collections.emptyList();
        }
        List<Integer> saleOrderGoodsIdList = saleOrderGoodsList.stream().map(SaleOrderGoodsListResultDto::getSaleordergoodsId).collect(Collectors.toList());
        // 查询物流信息
        List<SaleOrderExpressInfoDto> saleOrderExpressInfoDtoList = saleOrderMobileApiService.getExpressInfoOfSaleOrder(query.getSaleOrderId(), saleOrderGoodsIdList);
        if (CollUtil.isEmpty(saleOrderExpressInfoDtoList)) {
            log.info("查询销售订单物流信息为空");
            return Collections.emptyList();
        }
        List<SaleorderOfLogisticsInfoDto> saleorderOfLogisticsInfoDtoList = saleOrderExpressInfoDtoList.stream().map(SaleOrderMobileAggregateApiServiceImpl::toSaleorderOfLogisticsInfoDto).collect(Collectors.toList());

        // 封装物流详细信息
        for (SaleOrderExpressInfoDto dto : saleOrderExpressInfoDtoList) {
            if (Objects.isNull(dto.getExpressId()) || StringUtils.isBlank(dto.getLogisticsNo())) {
                log.info("查询销售订单物流详细信息参数错误");
                continue;
            }
            SaleOrderExpressInfoDto expressResult = saleOrderMobileApiService.getExpressByExpressIdAndLogisticsNo(dto.getExpressId(), dto.getLogisticsNo());
            if (Objects.isNull(expressResult)) {
                log.info("查询销售订单物流详细信息非真实物流信息");
                continue;
            }
            List<SaleOrderExpressDetailInfoDto> expressDetailInfoList = dto.getExpressDetailInfoList();
            if (CollUtil.isEmpty(expressDetailInfoList)) {
                log.info("查询销售订单物流详细信息下的快递详情信息为空");
                continue;
            }
            String phone = getPhoneByBusinessType(expressDetailInfoList);
            String logisticsName = dto.getLogisticsName();
            String logisticsNo = dto.getLogisticsNo();

            // 获取快递100的物流信息
            String logisticsDetailStr = saleOrderMobileApiService.getLogisticsDetail(logisticsName, logisticsNo, phone);
            List<LogisticsDetailDto> logisticsDetailList = new ArrayList<>();

            if (StringUtils.isNotBlank(logisticsDetailStr)) {
                try {
                    JSONObject detailJsonObject = JSON.parseObject(logisticsDetailStr);
                    if (detailJsonObject.containsKey(Constants.DATA) && (detailJsonObject.get(Constants.DATA) instanceof JSONArray)) {
                        JSONArray data = detailJsonObject.getJSONArray(Constants.DATA);
                        if (CollUtil.isNotEmpty(data)) {
                            for (Object datum : data) {
                                LogisticsDetailDto logisticsDetailDto = new LogisticsDetailDto();
                                JSONObject job = (JSONObject) datum;
                                String time = job.getString("time");
                                String[] arr = time.split("\\s+");

                                logisticsDetailDto.setLogisticsNo(logisticsNo);
                                logisticsDetailDto.setDateTime(arr[0]);
                                logisticsDetailDto.setTimeMillis(arr[1]);
                                logisticsDetailDto.setDetail(job.getString("context"));
                                logisticsDetailList.add(logisticsDetailDto);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("解析物流信息异常", e);
                }
            }

            // 封装物流详细信息
            saleorderOfLogisticsInfoDtoList.stream()
                    .filter(saleorderOfLogisticsInfoDto -> Objects.equals(saleorderOfLogisticsInfoDto.getExpressId(), dto.getExpressId()))
                    .findFirst()
                    .ifPresent(saleorderOfLogisticsInfoDto -> saleorderOfLogisticsInfoDto.setLogisticsDetailList(logisticsDetailList));
        }

        return saleorderOfLogisticsInfoDtoList;
    }

    @Override
    public ResourceFileDto viewContract(ViewContractQuery query) {
        ResourceFileDto resourceFileDto = new ResourceFileDto();
        if (Objects.isNull(query) || Objects.isNull(query.getSaleOrderId())) {
            log.info("查询销售订单合同参数错误");
            return resourceFileDto;
        }
        SaleOrderDetailInfoDto saleOrderDetail = saleOrderMobileApiService.getSaleOrderDetail(query.getSaleOrderId());
        if (Objects.isNull(saleOrderDetail)) {
            log.info("查询销售订单合同为空");
            return resourceFileDto;
        }

        if (StringUtils.isNotBlank(saleOrderDetail.getContractUrl())) {
            resourceFileDto.setUrl(saleOrderDetail.getContractUrl());
            resourceFileDto.setFileType(Constants.PDF);
            resourceFileDto.setFileName(String.format(Constants.CONTRACT_FILE_NAME, saleOrderDetail.getSaleOrderNo(), Constants.ONE));
            resourceFileDto.setIsInternalResource(true);
        }
        return resourceFileDto;
    }

    @Override
    public List<ResourceFileDto> viewInvoice(ViewInvoiceQuery query) {
        if (Objects.isNull(query) || Objects.isNull(query.getSaleOrderId())) {
            log.info("查询销售订单发票参数错误");
            return Collections.emptyList();
        }
        SaleOrderDetailInfoDto saleOrderDetail = saleOrderMobileApiService.getSaleOrderDetail(query.getSaleOrderId());
        if (Objects.isNull(saleOrderDetail)) {
            log.info("查询销售订单详情为空");
            return Collections.emptyList();
        }
        // 是否有发票信息
        Integer invoiceMethod = saleOrderDetail.getInvoiceMethod();
        if (Constants.ONE.equals(invoiceMethod) || Constants.TWO.equals(invoiceMethod)) {
            log.info("查询销售订单发票为纸质发票");
            return Collections.emptyList();
        }
        // 查询发票信息
        List<SaleOrderInvoiceInfoDto> invoiceInfoList = saleOrderMobileApiService.getInvoiceInfoOfSaleOrder(Collections.singletonList(query.getSaleOrderId()));
        if (CollUtil.isEmpty(invoiceInfoList)) {
            log.info("查询销售订单发票信息为空");
            return Collections.emptyList();
        }

        List<String> invoiceUrls = invoiceInfoList.stream()
                .filter(infoDto -> StringUtils.isNotBlank(infoDto.getInvoiceHref()) || StringUtils.isNotBlank(infoDto.getOssFileUrl()))
                .map(infoDto -> StringUtils.isNotBlank(infoDto.getOssFileUrl()) ? infoDto.getOssFileUrl() : infoDto.getInvoiceHref())
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(invoiceUrls)) {
            log.info("查询销售订单发票url为空");
            return Collections.emptyList();
        }

        AtomicInteger i = new AtomicInteger(1);
        List<ResourceFileDto> resourceFileDtoList = invoiceUrls.stream()
                .map(url -> {
                    ResourceFileDto resourceFileDto = new ResourceFileDto();
                    resourceFileDto.setUrl(url);
                    resourceFileDto.setFileType(Constants.PDF);
                    resourceFileDto.setFileName(String.format(Constants.INVOICE_FILE_NAME, saleOrderDetail.getSaleOrderNo(), i.getAndIncrement()));
                    resourceFileDto.setIsInternalResource(url.contains(Constants.FILE_VEDENG_COM) || url.contains(Constants.FILE_IVEDENG_COM));
                    return resourceFileDto;
                })
                .collect(Collectors.toList());

        return resourceFileDtoList;

    }

    @Override
    public SaleorderInfoDto querySaleorderByCustomerName(String customerName) {
        return saleOrderMobileApiService.querySaleorderByCustomerName(customerName);
    }

    public String getPhoneByBusinessType(List<SaleOrderExpressDetailInfoDto> expressDetailInfoDto) {
        //获取手机号
        String phone = "";
        if (CollUtil.isEmpty(expressDetailInfoDto)) {
            return phone;
        }
        Integer businessType = expressDetailInfoDto.get(0).getBusinessType();
        Integer expressId = expressDetailInfoDto.get(0).getExpressId();
        if (Objects.isNull(businessType) || Objects.isNull(expressId)) {
            return phone;
        }
        List<String> phoneList = saleOrderMobileApiService.getExpressPhoneByBusinessType(businessType, expressId);
        if (CollUtil.isNotEmpty(phoneList) && Constants.ONE.equals(phoneList.size())) {
            phone = phoneList.get(0);
        }
        return phone;
    }


    public static SaleorderOfLogisticsInfoDto toSaleorderOfLogisticsInfoDto(SaleOrderExpressInfoDto saleOrderExpressInfoDto) {
        if (saleOrderExpressInfoDto == null) {
            return null;
        }
        SaleorderOfLogisticsInfoDto saleorderOfLogisticsInfoDto = new SaleorderOfLogisticsInfoDto();
        saleorderOfLogisticsInfoDto.setExpressId(saleOrderExpressInfoDto.getExpressId());
        saleorderOfLogisticsInfoDto.setLogisticsNo(saleOrderExpressInfoDto.getLogisticsNo());
        saleorderOfLogisticsInfoDto.setLogisticsName(saleOrderExpressInfoDto.getLogisticsName());
        saleorderOfLogisticsInfoDto.setExpressStatus(saleOrderExpressInfoDto.getExpressStatus());

        if (CollUtil.isNotEmpty(saleOrderExpressInfoDto.getExpressDetailInfoList())) {
            List<SaleorderListOfGoodsDto> saleOrderGoodsList = saleOrderExpressInfoDto.getExpressDetailInfoList().stream().map(SaleOrderMobileAggregateApiServiceImpl::toSaleorderListOfGoodsDto).collect(Collectors.toList());
            saleorderOfLogisticsInfoDto.setSaleOrderGoodsList(saleOrderGoodsList);
        }

        return saleorderOfLogisticsInfoDto;
    }


    public static SaleorderListOfGoodsDto toSaleorderListOfGoodsDto(SaleOrderExpressDetailInfoDto saleOrderExpressDetailInfoDto) {
        if (saleOrderExpressDetailInfoDto == null) {
            return null;
        }
        SaleorderListOfGoodsDto saleorderListOfGoodsDto = new SaleorderListOfGoodsDto();
        saleorderListOfGoodsDto.setSku(saleOrderExpressDetailInfoDto.getSku());
        saleorderListOfGoodsDto.setGoodsName(saleOrderExpressDetailInfoDto.getGoodsName());
        saleorderListOfGoodsDto.setPrice(saleOrderExpressDetailInfoDto.getPrice());
        saleorderListOfGoodsDto.setNum(saleOrderExpressDetailInfoDto.getExpressDetailNum());

        return saleorderListOfGoodsDto;
    }



    public static SaleorderDetailDto toSaleorderDetailDto(SaleOrderDetailInfoDto saleOrderDetailInfoDto) {
        if (saleOrderDetailInfoDto == null) {
            return null;
        }
        SaleorderDetailDto saleorderDetailDto = new SaleorderDetailDto();
        saleorderDetailDto.setSaleOrderId(saleOrderDetailInfoDto.getSaleOrderId());
        saleorderDetailDto.setSaleOrderNo(saleOrderDetailInfoDto.getSaleOrderNo());
        saleorderDetailDto.setOrderStatus(saleOrderDetailInfoDto.getOrderStatus());
        saleorderDetailDto.setTraderName(saleOrderDetailInfoDto.getTraderName());
        saleorderDetailDto.setCurrentUserId(saleOrderDetailInfoDto.getCurrentUserId());
        saleorderDetailDto.setCurrentUserName(saleOrderDetailInfoDto.getCurrentUserName());
        saleorderDetailDto.setComments(saleOrderDetailInfoDto.getComments());
        saleorderDetailDto.setAddTime(saleOrderDetailInfoDto.getAddTime());

        // 合同资源封装
        ResourceFileDto resourceFileDto = new ResourceFileDto();
        if (StringUtils.isNotBlank(saleOrderDetailInfoDto.getContractUrl())) {
            resourceFileDto.setUrl(saleOrderDetailInfoDto.getContractUrl());
            resourceFileDto.setFileType(Constants.PDF);
            resourceFileDto.setFileName(String.format(Constants.CONTRACT_FILE_NAME, saleOrderDetailInfoDto.getSaleOrderNo(), Constants.ONE));
            resourceFileDto.setIsInternalResource(true);
        }
        saleorderDetailDto.setContractUrl(resourceFileDto);

        // 封装收货信息
        TakeTraderDto takeTraderDto = getTakeTraderDto(saleOrderDetailInfoDto);
        saleorderDetailDto.setTakeTrader(takeTraderDto);

        // 封装发票信息
        InvoiceInfoDto invoiceInfoDto = getInvoiceInfoDto(saleOrderDetailInfoDto);
        saleorderDetailDto.setInvoiceInfo(invoiceInfoDto);

        return saleorderDetailDto;
    }

    private static TakeTraderDto getTakeTraderDto(SaleOrderDetailInfoDto saleOrderDetailInfoDto) {
        TakeTraderDto takeTraderDto = new TakeTraderDto();
        takeTraderDto.setTakeTraderContactName(saleOrderDetailInfoDto.getTakeTraderContactName());
        takeTraderDto.setTakeTraderContactMobile(saleOrderDetailInfoDto.getTakeTraderContactMobile());
        takeTraderDto.setTakeTraderArea(saleOrderDetailInfoDto.getTakeTraderArea());
        takeTraderDto.setTakeTraderAddress(saleOrderDetailInfoDto.getTakeTraderAddress());
        return takeTraderDto;
    }

    private static InvoiceInfoDto getInvoiceInfoDto(SaleOrderDetailInfoDto saleOrderDetailInfoDto) {
        InvoiceInfoDto invoiceInfoDto = new InvoiceInfoDto();
        invoiceInfoDto.setInvoiceTraderName(saleOrderDetailInfoDto.getInvoiceTraderName());
        invoiceInfoDto.setInvoiceType(saleOrderDetailInfoDto.getInvoiceType());
        invoiceInfoDto.setInvoiceTraderContactName(saleOrderDetailInfoDto.getInvoiceTraderContactName());
        invoiceInfoDto.setInvoiceTraderContactMobile(saleOrderDetailInfoDto.getInvoiceTraderContactMobile());
        invoiceInfoDto.setInvoiceTraderContactEmail(saleOrderDetailInfoDto.getInvoiceTraderContactEmail());
        invoiceInfoDto.setInvoiceTraderArea(saleOrderDetailInfoDto.getInvoiceTraderArea());
        invoiceInfoDto.setInvoiceTraderAddress(saleOrderDetailInfoDto.getInvoiceTraderAddress());
        invoiceInfoDto.setInvoiceMethod(saleOrderDetailInfoDto.getInvoiceMethod());
        invoiceInfoDto.setIsSendInvoice(saleOrderDetailInfoDto.getIsSendInvoice());
        return invoiceInfoDto;
    }

    public static SaleorderOfAftersalesGoodsListDto toSaleorderOfAftersalesGoodsListDto(AfterSalesGoodsListResultDto afterSalesGoodsListResultDto) {
        if (afterSalesGoodsListResultDto == null) {
            return null;
        }
        SaleorderOfAftersalesGoodsListDto saleorderOfAftersalesGoodsListDto = new SaleorderOfAftersalesGoodsListDto();
        saleorderOfAftersalesGoodsListDto.setAfterSalesGoodsId(afterSalesGoodsListResultDto.getAfterSalesGoodsId());
        saleorderOfAftersalesGoodsListDto.setAfterSalesId(afterSalesGoodsListResultDto.getAfterSalesId());
        saleorderOfAftersalesGoodsListDto.setOrderDetailId(afterSalesGoodsListResultDto.getOrderDetailId());
        saleorderOfAftersalesGoodsListDto.setGoodsType(afterSalesGoodsListResultDto.getGoodsType());
        saleorderOfAftersalesGoodsListDto.setGoodsId(afterSalesGoodsListResultDto.getGoodsId());
        saleorderOfAftersalesGoodsListDto.setNum(afterSalesGoodsListResultDto.getNum());
        saleorderOfAftersalesGoodsListDto.setPrice(afterSalesGoodsListResultDto.getPrice());
        saleorderOfAftersalesGoodsListDto.setSKU(afterSalesGoodsListResultDto.getSku());
        saleorderOfAftersalesGoodsListDto.setGoodsName(afterSalesGoodsListResultDto.getGoodsName());
        return saleorderOfAftersalesGoodsListDto;
    }

    public static SaleorderOfAftersalesListDto toSaleorderOfAftersalesListDto(AfterSalesListResultDto afterSalesListResultDto) {
        if (afterSalesListResultDto == null) {
            return null;
        }
        SaleorderOfAftersalesListDto saleorderOfAftersalesListDto = new SaleorderOfAftersalesListDto();
        saleorderOfAftersalesListDto.setAfterSalesId(afterSalesListResultDto.getAfterSalesId());
        saleorderOfAftersalesListDto.setAfterSalesNo(afterSalesListResultDto.getAfterSalesNo());
        saleorderOfAftersalesListDto.setType(afterSalesListResultDto.getType());
        saleorderOfAftersalesListDto.setTypeName(afterSalesListResultDto.getTypeName());
        saleorderOfAftersalesListDto.setOrderNo(afterSalesListResultDto.getOrderNo());
        saleorderOfAftersalesListDto.setOrderId(afterSalesListResultDto.getOrderId());
        saleorderOfAftersalesListDto.setAfterSalesStatus(afterSalesListResultDto.getAfterSalesStatus());
        saleorderOfAftersalesListDto.setAddTime(afterSalesListResultDto.getAddTime());
        saleorderOfAftersalesListDto.setReason(afterSalesListResultDto.getReason());
        saleorderOfAftersalesListDto.setReasonName(afterSalesListResultDto.getReasonName());
        return saleorderOfAftersalesListDto;
    }

    public static SaleorderListDto toSaleorderListDto(SaleOrderListResultDto saleOrderListResultDto) {
        if (saleOrderListResultDto == null) {
            return null;
        }
        SaleorderListDto saleorderListDto = new SaleorderListDto();
        saleorderListDto.setSaleOrderId(saleOrderListResultDto.getSaleorderId());
        saleorderListDto.setSaleOrderNo(saleOrderListResultDto.getSaleorderNo());
        saleorderListDto.setOrderStatus(saleOrderListResultDto.getOrderStatus());
        saleorderListDto.setTraderName(saleOrderListResultDto.getTraderName());
        saleorderListDto.setCurrentUserId(saleOrderListResultDto.getCurrentUserId());
        saleorderListDto.setCurrentUserName(saleOrderListResultDto.getCurrentUserName());
        saleorderListDto.setHaveContractInfo(StringUtils.isNotBlank(saleOrderListResultDto.getContractUrl()));
        saleorderListDto.setInvoiceMethod(saleOrderListResultDto.getInvoiceMethod());
        saleorderListDto.setIsSendInvoice(saleOrderListResultDto.getIsSendInvoice());

        // Not mapped SaleorderListDto fields:
        // saleorderListOfGoodsDtoList
        return saleorderListDto;
    }

    public static SaleorderListOfGoodsDto toSaleorderListOfGoodsDto(SaleOrderGoodsListResultDto saleOrderGoodsListResultDto) {
        if (saleOrderGoodsListResultDto == null) {
            return null;
        }
        SaleorderListOfGoodsDto saleorderListOfGoodsDto = new SaleorderListOfGoodsDto();
        saleorderListOfGoodsDto.setSaleOrderGoodsId(saleOrderGoodsListResultDto.getSaleordergoodsId());
        saleorderListOfGoodsDto.setSaleOrderId(saleOrderGoodsListResultDto.getSaleorderId());
        saleorderListOfGoodsDto.setGoodsId(saleOrderGoodsListResultDto.getGoodsId());
        saleorderListOfGoodsDto.setSku(saleOrderGoodsListResultDto.getSku());
        saleorderListOfGoodsDto.setGoodsName(saleOrderGoodsListResultDto.getGoodsName());
        saleorderListOfGoodsDto.setPrice(saleOrderGoodsListResultDto.getPrice());
        saleorderListOfGoodsDto.setNum(saleOrderGoodsListResultDto.getNum());
        return saleorderListOfGoodsDto;
    }



}
