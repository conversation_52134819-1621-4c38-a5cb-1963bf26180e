<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.SkuSupplyAuthMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.dto.SkuSupplyAuth">
    <id column="SKU_SUPPLY_AUTH_ID" jdbcType="INTEGER" property="skuSupplyAuthId" />
    <result column="TRADER_SUPPLY_ID" jdbcType="INTEGER" property="traderSupplyId" />
    <result column="BRAND_IDS" jdbcType="VARCHAR" property="brandIds" />
    <result column="VALID_START_TIME" jdbcType="DATE" property="validStartTime" />
    <result column="VALID_END_TIME" jdbcType="DATE" property="validEndTime" />
    <result column="AUTH_TYPE" jdbcType="INTEGER" property="authType" />
    <result column="IS_DELETE" jdbcType="TINYINT" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
  </resultMap>

  <resultMap id="skuSupplierAuthMap" type="com.vedeng.erp.trader.domain.vo.SkuSupplyAuthVo" extends="BaseResultMap">
    <collection property="skuSupplyAuthDetails" javaType="java.util.List" ofType="com.vedeng.erp.trader.domain.dto.SkuSupplyAuthDetail">
      <id column="SKU_SUPPLY_AUTH_DETAIL_ID" jdbcType="INTEGER" property="skuSupplyAuthDetailId"/>
      <result column="SKU_SUPPLY_AUTH_ID" jdbcType="INTEGER" property="skuSupplyAuthId"/>
      <result column="SKU_ID" jdbcType="INTEGER" property="skuId"/>
      <result column="SKU_NO" jdbcType="VARCHAR" property="skuNo"/>
      <result column="SKU_NAME" jdbcType="VARCHAR" property="skuName"/>
      <result column="MODEL" jdbcType="VARCHAR" property="model"/>
    </collection>
  </resultMap>

  <sql id="Base_Column_List">
    SKU_SUPPLY_AUTH_ID, TRADER_SUPPLY_ID, BRAND_IDS, VALID_START_TIME, VALID_END_TIME, 
    AUTH_TYPE, IS_DELETE, ADD_TIME, CREATOR, MOD_TIME, UPDATER
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_SKU_SUPPLY_AUTH
    where SKU_SUPPLY_AUTH_ID = #{skuSupplyAuthId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_SKU_SUPPLY_AUTH
    where SKU_SUPPLY_AUTH_ID = #{skuSupplyAuthId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="SKU_SUPPLY_AUTH_ID" keyProperty="skuSupplyAuthId" parameterType="com.vedeng.erp.trader.domain.dto.SkuSupplyAuth" useGeneratedKeys="true">
    insert into T_SKU_SUPPLY_AUTH (TRADER_SUPPLY_ID, BRAND_IDS, VALID_START_TIME, 
      VALID_END_TIME, AUTH_TYPE, IS_DELETE, 
      ADD_TIME, CREATOR, MOD_TIME, 
      UPDATER)
    values (#{traderSupplyId,jdbcType=INTEGER}, #{brandIds,jdbcType=VARCHAR}, #{validStartTime,jdbcType=DATE}, 
      #{validEndTime,jdbcType=DATE}, #{authType,jdbcType=INTEGER}, #{isDelete,jdbcType=TINYINT}, 
      #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=TIMESTAMP}, 
      #{updater,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="SKU_SUPPLY_AUTH_ID" keyProperty="skuSupplyAuthId" parameterType="com.vedeng.erp.trader.domain.dto.SkuSupplyAuth" useGeneratedKeys="true">
    insert into T_SKU_SUPPLY_AUTH
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="traderSupplyId != null">
        TRADER_SUPPLY_ID,
      </if>
      <if test="brandIds != null and brandIds != ''">
        BRAND_IDS,
      </if>
      <if test="validStartTime != null">
        VALID_START_TIME,
      </if>
      <if test="validEndTime != null">
        VALID_END_TIME,
      </if>
      <if test="authType != null">
        AUTH_TYPE,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="traderSupplyId != null">
        #{traderSupplyId,jdbcType=INTEGER},
      </if>
      <if test="brandIds != null and brandIds != ''">
        #{brandIds,jdbcType=VARCHAR},
      </if>
      <if test="validStartTime != null">
        #{validStartTime,jdbcType=DATE},
      </if>
      <if test="validEndTime != null">
        #{validEndTime,jdbcType=DATE},
      </if>
      <if test="authType != null">
        #{authType,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.dto.SkuSupplyAuth">
    update T_SKU_SUPPLY_AUTH
    <set>
      <if test="traderSupplyId != null">
        TRADER_SUPPLY_ID = #{traderSupplyId,jdbcType=INTEGER},
      </if>
      <if test="brandIds != null">
        BRAND_IDS = #{brandIds,jdbcType=VARCHAR},
      </if>
      <if test="validStartTime != null">
        VALID_START_TIME = #{validStartTime,jdbcType=DATE},
      </if>
      <if test="validEndTime != null">
        VALID_END_TIME = #{validEndTime,jdbcType=DATE},
      </if>
      <if test="authType != null">
        AUTH_TYPE = #{authType,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where SKU_SUPPLY_AUTH_ID = #{skuSupplyAuthId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.dto.SkuSupplyAuth">
    update T_SKU_SUPPLY_AUTH
    set TRADER_SUPPLY_ID = #{traderSupplyId,jdbcType=INTEGER},
      BRAND_IDS = #{brandIds,jdbcType=VARCHAR},
      VALID_START_TIME = #{validStartTime,jdbcType=DATE},
      VALID_END_TIME = #{validEndTime,jdbcType=DATE},
      AUTH_TYPE = #{authType,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=TINYINT},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER}
    where SKU_SUPPLY_AUTH_ID = #{skuSupplyAuthId,jdbcType=INTEGER}
  </update>
  <select id="queryAuthInfoBySupplierId" resultMap="skuSupplierAuthMap">
    SELECT *
    FROM T_SKU_SUPPLY_AUTH A
    LEFT JOIN T_SKU_SUPPLY_AUTH_DETAIL B ON A.SKU_SUPPLY_AUTH_ID = B.SKU_SUPPLY_AUTH_ID AND B.IS_DELETE = 0
    WHERE TRADER_SUPPLY_ID = #{supplierId,jdbcType=INTEGER}
    AND A.IS_DELETE = 0
    ORDER BY A.ADD_TIME DESC
  </select>
  <update id="delSkuSupplyAuthById" parameterType="com.vedeng.erp.trader.domain.dto.SkuSupplyAuth">
    UPDATE T_SKU_SUPPLY_AUTH
    SET
    <if test="modTime != null">
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
    </if>
    <if test="updater != null">
      UPDATER = #{updater,jdbcType=INTEGER},
    </if>
    IS_DELETE = 1
    WHERE SKU_SUPPLY_AUTH_ID = #{skuSupplyAuthId,jdbcType=INTEGER}
  </update>
</mapper>