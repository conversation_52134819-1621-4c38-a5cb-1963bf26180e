package com.vedeng.goods.model;

public class CoreSkuDescGenerate {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_DESC.GOODS_DESC_ID
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    private Integer goodsDescId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_DESC.GOODS_SPU_ID
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    private Integer goodsSpuId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_DESC.GOODS_SKU_ID
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    private Integer goodsSkuId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_DESC.DESC_TYPE
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    private Byte descType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_DESC.GOODS_NAME
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    private String goodsName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_DESC.SEO_DESCRIPT
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    private String seoDescript;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_DESC.SEO_KEYWORDS
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    private String seoKeywords;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_DESC.SEO_TITLE
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    private String seoTitle;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_DESC.GOODS_DESC
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    private String goodsDesc;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_DESC.GOODS_DESC_ID
     *
     * @return the value of V_CORE_SKU_DESC.GOODS_DESC_ID
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    public Integer getGoodsDescId() {
        return goodsDescId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_DESC.GOODS_DESC_ID
     *
     * @param goodsDescId the value for V_CORE_SKU_DESC.GOODS_DESC_ID
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    public void setGoodsDescId(Integer goodsDescId) {
        this.goodsDescId = goodsDescId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_DESC.GOODS_SPU_ID
     *
     * @return the value of V_CORE_SKU_DESC.GOODS_SPU_ID
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    public Integer getGoodsSpuId() {
        return goodsSpuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_DESC.GOODS_SPU_ID
     *
     * @param goodsSpuId the value for V_CORE_SKU_DESC.GOODS_SPU_ID
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    public void setGoodsSpuId(Integer goodsSpuId) {
        this.goodsSpuId = goodsSpuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_DESC.GOODS_SKU_ID
     *
     * @return the value of V_CORE_SKU_DESC.GOODS_SKU_ID
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    public Integer getGoodsSkuId() {
        return goodsSkuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_DESC.GOODS_SKU_ID
     *
     * @param goodsSkuId the value for V_CORE_SKU_DESC.GOODS_SKU_ID
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    public void setGoodsSkuId(Integer goodsSkuId) {
        this.goodsSkuId = goodsSkuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_DESC.DESC_TYPE
     *
     * @return the value of V_CORE_SKU_DESC.DESC_TYPE
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    public Byte getDescType() {
        return descType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_DESC.DESC_TYPE
     *
     * @param descType the value for V_CORE_SKU_DESC.DESC_TYPE
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    public void setDescType(Byte descType) {
        this.descType = descType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_DESC.GOODS_NAME
     *
     * @return the value of V_CORE_SKU_DESC.GOODS_NAME
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    public String getGoodsName() {
        return goodsName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_DESC.GOODS_NAME
     *
     * @param goodsName the value for V_CORE_SKU_DESC.GOODS_NAME
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_DESC.SEO_DESCRIPT
     *
     * @return the value of V_CORE_SKU_DESC.SEO_DESCRIPT
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    public String getSeoDescript() {
        return seoDescript;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_DESC.SEO_DESCRIPT
     *
     * @param seoDescript the value for V_CORE_SKU_DESC.SEO_DESCRIPT
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    public void setSeoDescript(String seoDescript) {
        this.seoDescript = seoDescript;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_DESC.SEO_KEYWORDS
     *
     * @return the value of V_CORE_SKU_DESC.SEO_KEYWORDS
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    public String getSeoKeywords() {
        return seoKeywords;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_DESC.SEO_KEYWORDS
     *
     * @param seoKeywords the value for V_CORE_SKU_DESC.SEO_KEYWORDS
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    public void setSeoKeywords(String seoKeywords) {
        this.seoKeywords = seoKeywords;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_DESC.SEO_TITLE
     *
     * @return the value of V_CORE_SKU_DESC.SEO_TITLE
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    public String getSeoTitle() {
        return seoTitle;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_DESC.SEO_TITLE
     *
     * @param seoTitle the value for V_CORE_SKU_DESC.SEO_TITLE
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    public void setSeoTitle(String seoTitle) {
        this.seoTitle = seoTitle;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_DESC.GOODS_DESC
     *
     * @return the value of V_CORE_SKU_DESC.GOODS_DESC
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    public String getGoodsDesc() {
        return goodsDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_DESC.GOODS_DESC
     *
     * @param goodsDesc the value for V_CORE_SKU_DESC.GOODS_DESC
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    public void setGoodsDesc(String goodsDesc) {
        this.goodsDesc = goodsDesc;
    }
}