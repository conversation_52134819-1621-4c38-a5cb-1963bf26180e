<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="订单账期申请" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src="<%= basePath %>static/libs/jquery/plugins/DatePicker/WdatePicker.js"></script>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/steps/steps.min.css?rnd=${resourceVersionKey}">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/pages/goods/vgoods/spu/spu_edit.css?rnd=${resourceVersionKey}">
<div class="form-list  form-tips8 trader-customer-accountperiodapply">
    <div class="detail-block-tip vd-tip tip-orange" style="display:inline-block;width: 85%;">
        <i class="vd-tip-icon vd-icon icon-info2"></i>
        <div class="vd-tip-cnt">订单账期可以有多个,但只能用于指定的订单(创建商机/订单时，付款<br>
            计划可先选择先款后货,以获取订单号)</div>
    </div>
    <div class="form-block form-info" style="text-align: center">
        <span class="bt-small bg-light-blue bt-bg-style" onclick="resetEmpty();" id="addBtn" style="width: 120px">新增账期</span>
        <span class="bt-small bg-light-blue bt-bg-style" onclick="uptAccountPeriod();" id="uptBtn" style="width: 120px">已有账期调整</span>
    </div>
    <div class="form-block form-info" style="margin-top: 10px">
        <form method="post" action="" id="myform">
            <ul>
                <li>
                    <div class="form-tips">
                        <span>*</span>
                        <lable>订单号：</lable>
                    </div>
                    <div class="f_left ">
                        <div class="form-blanks">
                            <input type="text" name="saleorderNo" value="" id="saleorderNo">
                            <input type="hidden" name="relatedOrderId" value="" id="relatedOrderId">
                            <span class="bt-small bg-light-blue bt-bg-style" onclick="serOrderAccountPeriod();" id="serBtn" >查询</span>
                        </div>
                    </div>

                </li>
                <li>
                    <div class="pop-friend-tips">
                        <span id="orderTips">
                        --设置所绑定的订单号
                        </span>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <span>*</span>
                        <lable id="amountLabel">申请额度：</lable>
                    </div>
                    <div class="f_left ">
                        <div class="form-blanks" id="amountChange">
                            <select id="changeType" >
                                <option value="上调">上调</option>
                                <option value="下调">下调</option>
                            </select>
                            <input type="text" id="changeMount" value="0.00" placeholder="请输入需调整的额度">
                            <span>可用额/总额：</span><span id="orderAmountInfo"></span>
                            <br>
                            <span>调后额度：</span>
                            <span id="applyAmount" >
                            0
                            </span>
                        </div>
                        <div class="form-blanks" id="amountAdd">
                            <input type="text" id="insertApplyAmount" value="0.00" placeholder="请输入需申请的额度">元
                        </div>
                        <input type="hidden" name="applyAmount" value="" id="finalApplyAmount">
                    </div>
                </li>
                <li>
                    <div class="pop-friend-tips">
                        <span id="amountTips">
                            --仅可输入正数。支持2位小数
                        </span>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <span>*</span>
                        <lable>结算周期:</lable>
                    </div>
                    <div class="f_left ">
                        <div class="form-blanks" >
                            <input type="text" name="settlementPeriod" id="settlementPeriod" placeholder="请设置结算周期" value=""> 天
                        </div>
                    </div>
                </li>
                <li>
                    <div class="pop-friend-tips">
                        <span id="settleTips">
                            --基于结算标准+结算周期，确定所使用的账期理应归还的截止时间
                        </span>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <lable>预期毛利率：</lable>
                    </div>
                    <div class="f_left ">
                        <div class="form-blanks">
                            <input type="text" name="expectedMargin" id="expectedMargin" value=""> %
                        </div>
                    </div>
                </li>
                <li>
                    <div class="pop-friend-tips">
                        <span id="expectTips">
                            --请如实填写数字,作为审批人判断依据
                        </span>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <span>*</span>
                        <lable>申请原因</lable>
                    </div>
                    <div class="f_left ">
                        <div class="form-blanks">
                        <textarea name="applyReason" placeholder="请填写申请原因" id="applyReason"  style="width: 500px;height: 50px"></textarea>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="pop-friend-tips">
                        --请完整填写申请原因,便于上级审核,提高通过率
                    </div>
                </li>
            </ul>
                <div class="pop-friend-tips">
                    友情提醒<br/> 1、订单关闭，所关联的订单账期将自动清零;<br/>
                    2、逾期不还,将对客户信用造成影响;
                </div>
                <div class="add-tijiao tcenter">
                    <input type="hidden" name="customerId" id="customerId" value="${traderCustomer.traderCustomerId}" />
                    <input type="hidden" name="traderId" value="${traderCustomer.traderId}" />
                    <input type="hidden" name="companyId" value="1" />
                    <input type="hidden" name="billPeriodType" value="3" />
                    <input type="hidden" id="unReturnAmount" value="" />
                    <input type="hidden" name="billPeriodId" id="billPeriodId" value="" />
                    <input type="hidden" name="operateType" value="1" id="operateType"/>
                    <button type="submit" id="submit">提交</button>
                    <button class="dele" id="close-layer" type="button">取消</button>
                </div>
        </form>
    </div>
</div>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/customer/apply_order_accountperiod.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript">
    function serOrderAccountPeriod() {
        var saleorderNo = $("#saleorderNo").val();
        var customerId = $("#customerId").val();
        jQuery.ajax({
            url:'./queryOrderAccountPeriod.do',
            data: {"saleorderNo":saleorderNo,"traderCustomerId":customerId},
            type:"POST",
            dataType : "json",
            success:function(data){
                if(data.code == 0){
                    $("#relatedOrderId").val(data.param.relatedOrderId);
                    $("#finalApplyAmount").val(data.param.totalAmount);
                    $("#applyAmount").text(data.param.totalAmount);
                    $("#unReturnAmount").text(data.param.unReturnAmount);
                    $("#settlementPeriod").val(data.param.settlementPeriod);
                    $("#billPeriodId").val(data.param.billPeriodId);
                    $("#orderAmountInfo").text(data.param.applyAmount+'/'+data.param.totalAmount)
                    // $("#expectedMargin").val(data.param.expectedMargin);
                    // $("#applyReason").text(data.param.applyReason);
                }else if(data.code == -1){
                    layer.alert(data.message);
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    }
</script>
<%@ include file="../../common/footer.jsp"%>