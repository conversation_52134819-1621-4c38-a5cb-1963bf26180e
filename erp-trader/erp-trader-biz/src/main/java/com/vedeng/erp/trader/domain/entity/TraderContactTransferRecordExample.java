package com.vedeng.erp.trader.domain.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TraderContactTransferRecordExample {
    /**
     * @mbg.generated generated automatically, do not modify!
     */
    protected String orderByClause;

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    protected boolean distinct;

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    protected List<Criteria> oredCriteria;

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    public TraderContactTransferRecordExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andTraderContactTransferRecordIdIsNull() {
            addCriterion("TRADER_CONTACT_TRANSFER_RECORD_ID is null");
            return (Criteria) this;
        }

        public Criteria andTraderContactTransferRecordIdIsNotNull() {
            addCriterion("TRADER_CONTACT_TRANSFER_RECORD_ID is not null");
            return (Criteria) this;
        }

        public Criteria andTraderContactTransferRecordIdEqualTo(Integer value) {
            addCriterion("TRADER_CONTACT_TRANSFER_RECORD_ID =", value, "traderContactTransferRecordId");
            return (Criteria) this;
        }

        public Criteria andTraderContactTransferRecordIdNotEqualTo(Integer value) {
            addCriterion("TRADER_CONTACT_TRANSFER_RECORD_ID <>", value, "traderContactTransferRecordId");
            return (Criteria) this;
        }

        public Criteria andTraderContactTransferRecordIdGreaterThan(Integer value) {
            addCriterion("TRADER_CONTACT_TRANSFER_RECORD_ID >", value, "traderContactTransferRecordId");
            return (Criteria) this;
        }

        public Criteria andTraderContactTransferRecordIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("TRADER_CONTACT_TRANSFER_RECORD_ID >=", value, "traderContactTransferRecordId");
            return (Criteria) this;
        }

        public Criteria andTraderContactTransferRecordIdLessThan(Integer value) {
            addCriterion("TRADER_CONTACT_TRANSFER_RECORD_ID <", value, "traderContactTransferRecordId");
            return (Criteria) this;
        }

        public Criteria andTraderContactTransferRecordIdLessThanOrEqualTo(Integer value) {
            addCriterion("TRADER_CONTACT_TRANSFER_RECORD_ID <=", value, "traderContactTransferRecordId");
            return (Criteria) this;
        }

        public Criteria andTraderContactTransferRecordIdIn(List<Integer> values) {
            addCriterion("TRADER_CONTACT_TRANSFER_RECORD_ID in", values, "traderContactTransferRecordId");
            return (Criteria) this;
        }

        public Criteria andTraderContactTransferRecordIdNotIn(List<Integer> values) {
            addCriterion("TRADER_CONTACT_TRANSFER_RECORD_ID not in", values, "traderContactTransferRecordId");
            return (Criteria) this;
        }

        public Criteria andTraderContactTransferRecordIdBetween(Integer value1, Integer value2) {
            addCriterion("TRADER_CONTACT_TRANSFER_RECORD_ID between", value1, value2, "traderContactTransferRecordId");
            return (Criteria) this;
        }

        public Criteria andTraderContactTransferRecordIdNotBetween(Integer value1, Integer value2) {
            addCriterion("TRADER_CONTACT_TRANSFER_RECORD_ID not between", value1, value2, "traderContactTransferRecordId");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("ADD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("ADD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("ADD_TIME =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("ADD_TIME <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("ADD_TIME >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("ADD_TIME >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("ADD_TIME <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("ADD_TIME <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("ADD_TIME in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("ADD_TIME not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("ADD_TIME between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("ADD_TIME not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andModTimeIsNull() {
            addCriterion("MOD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andModTimeIsNotNull() {
            addCriterion("MOD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andModTimeEqualTo(Date value) {
            addCriterion("MOD_TIME =", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotEqualTo(Date value) {
            addCriterion("MOD_TIME <>", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeGreaterThan(Date value) {
            addCriterion("MOD_TIME >", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("MOD_TIME >=", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeLessThan(Date value) {
            addCriterion("MOD_TIME <", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeLessThanOrEqualTo(Date value) {
            addCriterion("MOD_TIME <=", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeIn(List<Date> values) {
            addCriterion("MOD_TIME in", values, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotIn(List<Date> values) {
            addCriterion("MOD_TIME not in", values, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeBetween(Date value1, Date value2) {
            addCriterion("MOD_TIME between", value1, value2, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotBetween(Date value1, Date value2) {
            addCriterion("MOD_TIME not between", value1, value2, "modTime");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("CREATOR is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("CREATOR is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(Integer value) {
            addCriterion("CREATOR =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(Integer value) {
            addCriterion("CREATOR <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(Integer value) {
            addCriterion("CREATOR >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(Integer value) {
            addCriterion("CREATOR >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(Integer value) {
            addCriterion("CREATOR <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(Integer value) {
            addCriterion("CREATOR <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<Integer> values) {
            addCriterion("CREATOR in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<Integer> values) {
            addCriterion("CREATOR not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNull() {
            addCriterion("UPDATER is null");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNotNull() {
            addCriterion("UPDATER is not null");
            return (Criteria) this;
        }

        public Criteria andUpdaterEqualTo(Integer value) {
            addCriterion("UPDATER =", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotEqualTo(Integer value) {
            addCriterion("UPDATER <>", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThan(Integer value) {
            addCriterion("UPDATER >", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThanOrEqualTo(Integer value) {
            addCriterion("UPDATER >=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThan(Integer value) {
            addCriterion("UPDATER <", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThanOrEqualTo(Integer value) {
            addCriterion("UPDATER <=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterIn(List<Integer> values) {
            addCriterion("UPDATER in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotIn(List<Integer> values) {
            addCriterion("UPDATER not in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterBetween(Integer value1, Integer value2) {
            addCriterion("UPDATER between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotBetween(Integer value1, Integer value2) {
            addCriterion("UPDATER not between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andCreatorNameIsNull() {
            addCriterion("CREATOR_NAME is null");
            return (Criteria) this;
        }

        public Criteria andCreatorNameIsNotNull() {
            addCriterion("CREATOR_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorNameEqualTo(String value) {
            addCriterion("CREATOR_NAME =", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameNotEqualTo(String value) {
            addCriterion("CREATOR_NAME <>", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameGreaterThan(String value) {
            addCriterion("CREATOR_NAME >", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameGreaterThanOrEqualTo(String value) {
            addCriterion("CREATOR_NAME >=", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameLessThan(String value) {
            addCriterion("CREATOR_NAME <", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameLessThanOrEqualTo(String value) {
            addCriterion("CREATOR_NAME <=", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameLike(String value) {
            addCriterion("CREATOR_NAME like", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameNotLike(String value) {
            addCriterion("CREATOR_NAME not like", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameIn(List<String> values) {
            addCriterion("CREATOR_NAME in", values, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameNotIn(List<String> values) {
            addCriterion("CREATOR_NAME not in", values, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameBetween(String value1, String value2) {
            addCriterion("CREATOR_NAME between", value1, value2, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameNotBetween(String value1, String value2) {
            addCriterion("CREATOR_NAME not between", value1, value2, "creatorName");
            return (Criteria) this;
        }

        public Criteria andUpdaterNameIsNull() {
            addCriterion("UPDATER_NAME is null");
            return (Criteria) this;
        }

        public Criteria andUpdaterNameIsNotNull() {
            addCriterion("UPDATER_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andUpdaterNameEqualTo(String value) {
            addCriterion("UPDATER_NAME =", value, "updaterName");
            return (Criteria) this;
        }

        public Criteria andUpdaterNameNotEqualTo(String value) {
            addCriterion("UPDATER_NAME <>", value, "updaterName");
            return (Criteria) this;
        }

        public Criteria andUpdaterNameGreaterThan(String value) {
            addCriterion("UPDATER_NAME >", value, "updaterName");
            return (Criteria) this;
        }

        public Criteria andUpdaterNameGreaterThanOrEqualTo(String value) {
            addCriterion("UPDATER_NAME >=", value, "updaterName");
            return (Criteria) this;
        }

        public Criteria andUpdaterNameLessThan(String value) {
            addCriterion("UPDATER_NAME <", value, "updaterName");
            return (Criteria) this;
        }

        public Criteria andUpdaterNameLessThanOrEqualTo(String value) {
            addCriterion("UPDATER_NAME <=", value, "updaterName");
            return (Criteria) this;
        }

        public Criteria andUpdaterNameLike(String value) {
            addCriterion("UPDATER_NAME like", value, "updaterName");
            return (Criteria) this;
        }

        public Criteria andUpdaterNameNotLike(String value) {
            addCriterion("UPDATER_NAME not like", value, "updaterName");
            return (Criteria) this;
        }

        public Criteria andUpdaterNameIn(List<String> values) {
            addCriterion("UPDATER_NAME in", values, "updaterName");
            return (Criteria) this;
        }

        public Criteria andUpdaterNameNotIn(List<String> values) {
            addCriterion("UPDATER_NAME not in", values, "updaterName");
            return (Criteria) this;
        }

        public Criteria andUpdaterNameBetween(String value1, String value2) {
            addCriterion("UPDATER_NAME between", value1, value2, "updaterName");
            return (Criteria) this;
        }

        public Criteria andUpdaterNameNotBetween(String value1, String value2) {
            addCriterion("UPDATER_NAME not between", value1, value2, "updaterName");
            return (Criteria) this;
        }

        public Criteria andTraderIdIsNull() {
            addCriterion("TRADER_ID is null");
            return (Criteria) this;
        }

        public Criteria andTraderIdIsNotNull() {
            addCriterion("TRADER_ID is not null");
            return (Criteria) this;
        }

        public Criteria andTraderIdEqualTo(Integer value) {
            addCriterion("TRADER_ID =", value, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdNotEqualTo(Integer value) {
            addCriterion("TRADER_ID <>", value, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdGreaterThan(Integer value) {
            addCriterion("TRADER_ID >", value, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("TRADER_ID >=", value, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdLessThan(Integer value) {
            addCriterion("TRADER_ID <", value, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdLessThanOrEqualTo(Integer value) {
            addCriterion("TRADER_ID <=", value, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdIn(List<Integer> values) {
            addCriterion("TRADER_ID in", values, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdNotIn(List<Integer> values) {
            addCriterion("TRADER_ID not in", values, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdBetween(Integer value1, Integer value2) {
            addCriterion("TRADER_ID between", value1, value2, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdNotBetween(Integer value1, Integer value2) {
            addCriterion("TRADER_ID not between", value1, value2, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderCustomerIdIsNull() {
            addCriterion("TRADER_CUSTOMER_ID is null");
            return (Criteria) this;
        }

        public Criteria andTraderCustomerIdIsNotNull() {
            addCriterion("TRADER_CUSTOMER_ID is not null");
            return (Criteria) this;
        }

        public Criteria andTraderCustomerIdEqualTo(Integer value) {
            addCriterion("TRADER_CUSTOMER_ID =", value, "traderCustomerId");
            return (Criteria) this;
        }

        public Criteria andTraderCustomerIdNotEqualTo(Integer value) {
            addCriterion("TRADER_CUSTOMER_ID <>", value, "traderCustomerId");
            return (Criteria) this;
        }

        public Criteria andTraderCustomerIdGreaterThan(Integer value) {
            addCriterion("TRADER_CUSTOMER_ID >", value, "traderCustomerId");
            return (Criteria) this;
        }

        public Criteria andTraderCustomerIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("TRADER_CUSTOMER_ID >=", value, "traderCustomerId");
            return (Criteria) this;
        }

        public Criteria andTraderCustomerIdLessThan(Integer value) {
            addCriterion("TRADER_CUSTOMER_ID <", value, "traderCustomerId");
            return (Criteria) this;
        }

        public Criteria andTraderCustomerIdLessThanOrEqualTo(Integer value) {
            addCriterion("TRADER_CUSTOMER_ID <=", value, "traderCustomerId");
            return (Criteria) this;
        }

        public Criteria andTraderCustomerIdIn(List<Integer> values) {
            addCriterion("TRADER_CUSTOMER_ID in", values, "traderCustomerId");
            return (Criteria) this;
        }

        public Criteria andTraderCustomerIdNotIn(List<Integer> values) {
            addCriterion("TRADER_CUSTOMER_ID not in", values, "traderCustomerId");
            return (Criteria) this;
        }

        public Criteria andTraderCustomerIdBetween(Integer value1, Integer value2) {
            addCriterion("TRADER_CUSTOMER_ID between", value1, value2, "traderCustomerId");
            return (Criteria) this;
        }

        public Criteria andTraderCustomerIdNotBetween(Integer value1, Integer value2) {
            addCriterion("TRADER_CUSTOMER_ID not between", value1, value2, "traderCustomerId");
            return (Criteria) this;
        }

        public Criteria andTraderCustomerNameIsNull() {
            addCriterion("TRADER_CUSTOMER_NAME is null");
            return (Criteria) this;
        }

        public Criteria andTraderCustomerNameIsNotNull() {
            addCriterion("TRADER_CUSTOMER_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andTraderCustomerNameEqualTo(String value) {
            addCriterion("TRADER_CUSTOMER_NAME =", value, "traderCustomerName");
            return (Criteria) this;
        }

        public Criteria andTraderCustomerNameNotEqualTo(String value) {
            addCriterion("TRADER_CUSTOMER_NAME <>", value, "traderCustomerName");
            return (Criteria) this;
        }

        public Criteria andTraderCustomerNameGreaterThan(String value) {
            addCriterion("TRADER_CUSTOMER_NAME >", value, "traderCustomerName");
            return (Criteria) this;
        }

        public Criteria andTraderCustomerNameGreaterThanOrEqualTo(String value) {
            addCriterion("TRADER_CUSTOMER_NAME >=", value, "traderCustomerName");
            return (Criteria) this;
        }

        public Criteria andTraderCustomerNameLessThan(String value) {
            addCriterion("TRADER_CUSTOMER_NAME <", value, "traderCustomerName");
            return (Criteria) this;
        }

        public Criteria andTraderCustomerNameLessThanOrEqualTo(String value) {
            addCriterion("TRADER_CUSTOMER_NAME <=", value, "traderCustomerName");
            return (Criteria) this;
        }

        public Criteria andTraderCustomerNameLike(String value) {
            addCriterion("TRADER_CUSTOMER_NAME like", value, "traderCustomerName");
            return (Criteria) this;
        }

        public Criteria andTraderCustomerNameNotLike(String value) {
            addCriterion("TRADER_CUSTOMER_NAME not like", value, "traderCustomerName");
            return (Criteria) this;
        }

        public Criteria andTraderCustomerNameIn(List<String> values) {
            addCriterion("TRADER_CUSTOMER_NAME in", values, "traderCustomerName");
            return (Criteria) this;
        }

        public Criteria andTraderCustomerNameNotIn(List<String> values) {
            addCriterion("TRADER_CUSTOMER_NAME not in", values, "traderCustomerName");
            return (Criteria) this;
        }

        public Criteria andTraderCustomerNameBetween(String value1, String value2) {
            addCriterion("TRADER_CUSTOMER_NAME between", value1, value2, "traderCustomerName");
            return (Criteria) this;
        }

        public Criteria andTraderCustomerNameNotBetween(String value1, String value2) {
            addCriterion("TRADER_CUSTOMER_NAME not between", value1, value2, "traderCustomerName");
            return (Criteria) this;
        }

        public Criteria andAfterTraderCustomerIdIsNull() {
            addCriterion("AFTER_TRADER_CUSTOMER_ID is null");
            return (Criteria) this;
        }

        public Criteria andAfterTraderCustomerIdIsNotNull() {
            addCriterion("AFTER_TRADER_CUSTOMER_ID is not null");
            return (Criteria) this;
        }

        public Criteria andAfterTraderCustomerIdEqualTo(Integer value) {
            addCriterion("AFTER_TRADER_CUSTOMER_ID =", value, "afterTraderCustomerId");
            return (Criteria) this;
        }

        public Criteria andAfterTraderCustomerIdNotEqualTo(Integer value) {
            addCriterion("AFTER_TRADER_CUSTOMER_ID <>", value, "afterTraderCustomerId");
            return (Criteria) this;
        }

        public Criteria andAfterTraderCustomerIdGreaterThan(Integer value) {
            addCriterion("AFTER_TRADER_CUSTOMER_ID >", value, "afterTraderCustomerId");
            return (Criteria) this;
        }

        public Criteria andAfterTraderCustomerIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("AFTER_TRADER_CUSTOMER_ID >=", value, "afterTraderCustomerId");
            return (Criteria) this;
        }

        public Criteria andAfterTraderCustomerIdLessThan(Integer value) {
            addCriterion("AFTER_TRADER_CUSTOMER_ID <", value, "afterTraderCustomerId");
            return (Criteria) this;
        }

        public Criteria andAfterTraderCustomerIdLessThanOrEqualTo(Integer value) {
            addCriterion("AFTER_TRADER_CUSTOMER_ID <=", value, "afterTraderCustomerId");
            return (Criteria) this;
        }

        public Criteria andAfterTraderCustomerIdIn(List<Integer> values) {
            addCriterion("AFTER_TRADER_CUSTOMER_ID in", values, "afterTraderCustomerId");
            return (Criteria) this;
        }

        public Criteria andAfterTraderCustomerIdNotIn(List<Integer> values) {
            addCriterion("AFTER_TRADER_CUSTOMER_ID not in", values, "afterTraderCustomerId");
            return (Criteria) this;
        }

        public Criteria andAfterTraderCustomerIdBetween(Integer value1, Integer value2) {
            addCriterion("AFTER_TRADER_CUSTOMER_ID between", value1, value2, "afterTraderCustomerId");
            return (Criteria) this;
        }

        public Criteria andAfterTraderCustomerIdNotBetween(Integer value1, Integer value2) {
            addCriterion("AFTER_TRADER_CUSTOMER_ID not between", value1, value2, "afterTraderCustomerId");
            return (Criteria) this;
        }

        public Criteria andAfterTraderCustomerNameIsNull() {
            addCriterion("AFTER_TRADER_CUSTOMER_NAME is null");
            return (Criteria) this;
        }

        public Criteria andAfterTraderCustomerNameIsNotNull() {
            addCriterion("AFTER_TRADER_CUSTOMER_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andAfterTraderCustomerNameEqualTo(String value) {
            addCriterion("AFTER_TRADER_CUSTOMER_NAME =", value, "afterTraderCustomerName");
            return (Criteria) this;
        }

        public Criteria andAfterTraderCustomerNameNotEqualTo(String value) {
            addCriterion("AFTER_TRADER_CUSTOMER_NAME <>", value, "afterTraderCustomerName");
            return (Criteria) this;
        }

        public Criteria andAfterTraderCustomerNameGreaterThan(String value) {
            addCriterion("AFTER_TRADER_CUSTOMER_NAME >", value, "afterTraderCustomerName");
            return (Criteria) this;
        }

        public Criteria andAfterTraderCustomerNameGreaterThanOrEqualTo(String value) {
            addCriterion("AFTER_TRADER_CUSTOMER_NAME >=", value, "afterTraderCustomerName");
            return (Criteria) this;
        }

        public Criteria andAfterTraderCustomerNameLessThan(String value) {
            addCriterion("AFTER_TRADER_CUSTOMER_NAME <", value, "afterTraderCustomerName");
            return (Criteria) this;
        }

        public Criteria andAfterTraderCustomerNameLessThanOrEqualTo(String value) {
            addCriterion("AFTER_TRADER_CUSTOMER_NAME <=", value, "afterTraderCustomerName");
            return (Criteria) this;
        }

        public Criteria andAfterTraderCustomerNameLike(String value) {
            addCriterion("AFTER_TRADER_CUSTOMER_NAME like", value, "afterTraderCustomerName");
            return (Criteria) this;
        }

        public Criteria andAfterTraderCustomerNameNotLike(String value) {
            addCriterion("AFTER_TRADER_CUSTOMER_NAME not like", value, "afterTraderCustomerName");
            return (Criteria) this;
        }

        public Criteria andAfterTraderCustomerNameIn(List<String> values) {
            addCriterion("AFTER_TRADER_CUSTOMER_NAME in", values, "afterTraderCustomerName");
            return (Criteria) this;
        }

        public Criteria andAfterTraderCustomerNameNotIn(List<String> values) {
            addCriterion("AFTER_TRADER_CUSTOMER_NAME not in", values, "afterTraderCustomerName");
            return (Criteria) this;
        }

        public Criteria andAfterTraderCustomerNameBetween(String value1, String value2) {
            addCriterion("AFTER_TRADER_CUSTOMER_NAME between", value1, value2, "afterTraderCustomerName");
            return (Criteria) this;
        }

        public Criteria andAfterTraderCustomerNameNotBetween(String value1, String value2) {
            addCriterion("AFTER_TRADER_CUSTOMER_NAME not between", value1, value2, "afterTraderCustomerName");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}