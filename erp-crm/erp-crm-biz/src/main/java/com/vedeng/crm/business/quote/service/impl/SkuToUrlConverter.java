package com.vedeng.crm.business.quote.service.impl;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.metadata.data.HyperlinkData;
import com.alibaba.excel.metadata.data.WriteCellData;

/**
 * <AUTHOR>
 * @description <p>报价单下载excel sku添加链接转换器</p>
 * @date 2024-09-27 16:43
 **/
public class SkuToUrlConverter implements Converter<String> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public WriteCellData<?> convertToExcelData(WriteConverterContext<String> context) throws Exception {
        String skuNo = context.getValue();
        WriteCellData<String> hyperlink = new WriteCellData<>(skuNo);
        HyperlinkData hyperlinkData = new HyperlinkData();
        hyperlink.setHyperlinkData(hyperlinkData);
        hyperlinkData.setAddress(String.format("https://www.vedeng.com/p/%s.html", skuNo));
        hyperlinkData.setHyperlinkType(HyperlinkData.HyperlinkType.URL);
        return hyperlink;
    }
}
