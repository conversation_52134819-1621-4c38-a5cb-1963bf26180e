<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
<c:set var="title" value="备货计划列表" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='<%= basePath %>static/js/flash/prepare/prepare_list.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/inputSuggest.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript">

</script>

<div class="main-container">
    <div class="searchfunc">
        <form method="post" id="search" action="<%=basePath%>flash/prepare/list.do" >
            <ul>
                <li>
                    <label class="infor_name">安全库存预警</label>
                    <select class="input-middle" name="warnLevel" id="warnLevel">
                        <option value="-1">全部</option>
                        <option <c:if test="${command.warnLevel eq 0}">selected</c:if> value="0">安全</option>
                        <option <c:if test="${command.warnLevel eq 2}">selected</c:if> value="2">一级预警</option>
                        <option <c:if test="${command.warnLevel eq 1}">selected</c:if> value="1">二级预警</option>
                    </select>
                </li>
                <li>
                    <label class="infor_name">处理</label>
                    <select class="input-middle" name="dealType" id="dealType">
                        <option value="-1">请选择</option>
                        <option <c:if test="${command.dealType eq 4}">selected</c:if> value="4">备货</option>
                        <option <c:if test="${command.dealType eq 3}">selected</c:if> value="3">暂不备货</option>
                        <option <c:if test="${command.dealType eq 1}">selected</c:if> value="1">替换</option>
                        <option <c:if test="${command.dealType eq 2}">selected</c:if> value="2">库存转换</option>
                        <option <c:if test="${command.dealType eq 0}">selected</c:if> value="0">删除</option>
                    </select>
                </li>
                <li>
                    <label class="infor_name">订货号</label>
                    <input type="text" class="input-middle" placeholder="请输入订货号" name="skuNo" id="skuNo" value="${command.skuNo}"/>
                </li>
                <li>
                    <label class="infor_name">商品名称</label>
                    <input type="text" class="input-middle" placeholder="请输入商品名称" name="skuName" id="skuName" value="${command.skuName}"/>
                </li>
                <li>
                    <label class="infor_name">品牌</label>
                    <input type="text" class="input-middle J-suggest-input" placeholder="请输入品牌" data-url="/firstengage/brand/brandName.do" name="brandName" id="brandName" value="${command.brandName}"/>
                </li>
                <li>
                    <label class="infor_name">商品类型</label>
                    <select class="input-middle" name="skuType" id="skuType">
                        <option value="-1">全部</option>
                        <c:forEach var="spuType" items="${spuTypeList}" varStatus="status">
                            <option value="${spuType.sysOptionDefinitionId }" <c:if test="${command.skuType == spuType.sysOptionDefinitionId}"> selected </c:if>>
                                    ${spuType.title}</option>
                        </c:forEach>
                    </select>
                </li>
                <li>
                    <label class="infor_name">归属人</label>
                    <select class="input-middle" name="assignmentManagerId" id="assignmentManagerId">
                        <option value="-1">全部</option>
                        <c:forEach var="user" items="${assUser}">
                            <option value="${user.userId}" <c:if test="${command.assignmentManagerId == user.userId}"> selected </c:if>>${user.username}</option>
                        </c:forEach>
                    </select>
                </li>

                <li>
                    <label class="infor_name" style="width: 105px;">安全库存调整记录</label>
                    <select class="input-middle" name="isAdjustStock" id="isAdjustStock">
                        <option value="-1">全部</option>
                        <option <c:if test="${command.isAdjustStock eq 0}">selected</c:if> value="0">无</option>
                        <option <c:if test="${command.isAdjustStock eq 1}">selected</c:if> value="1">有</option>
                    </select>
                </li>
                <li>
                    <label class="infor_name">安全库存</label>
                    <input type="text" class="input-middle" name="startSafeStock" id="startSafeStock" value="${command.startSafeStock}" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"/>-
                    <input type="text" class="input-middle" name="endSafeStock" id="endSafeStock" value="${command.endSafeStock}" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"/>
                </li>
            </ul>

            <div class="tcenter">
                <span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();" id="searchSpan">搜索</span>
                <span class="bt-small bg-light-blue bt-bg-style mr20" onclick="reset();">重置</span>
            </div>
        </form>
    </div>

    <div class="tleft" style="padding: 10px 0 10px 10px;">
        <span class="bt-largest bt-bg-style bg-light-blue" onclick="initExportPrepareStockPage();" id="">导出备货列表</span>
        <span class="bt-largest bg-light-blue bt-bg-style" onclick="iniForecastSafeRatioPage();">配置预测安全库存系数</span>
    </div>

    <div class="content">
        <div class="fixdiv">
            <div class="superdiv" style='width:2200px;'>
                <table class="table table-bordered table-striped table-condensed table-centered">
                    <thead>
                        <tr>
                            <th class="wid5">
                                <input type="checkbox"  name="b_checknox" autocomplete="off" onclick="selectAll(this);" >
                            </th>
                            <th class="wid8">订货号</th>
                            <th class="wid30">商品名称</th>
                            <th class="wid10">品牌</th>
                            <th class="wid8">型号</th>
                            <th class="wid8">商品单位</th>
                            <th class="wid6">商品类型</th>
                            <th class="wid6">归属人</th>
                            <th class="wid6">库存数量</th>
                            <th class="wid6">在途数量</th>
                            <th class="wid10">订单占用数量</th>
                            <th class="wid10">预测安全库存</th>
                            <th class="wid6">安全库存</th>
                            <th class="wid10">安全库存预警</th>
                            <th class="wid10">建议补充库存数量</th>
                            <th class="wid10">参考采购成本</th>
                            <th class="wid14">平均到货时间（天）</th>
                            <th class="wid6">断货天数</th>
                            <th class="wid25">操作项</th>
                        </tr>
                    </thead>
                    <tbody>
                    <c:choose>
                        <c:when test="${not empty skuList}">
                            <c:forEach var="skuList" items="${skuList}" varStatus="num">
                                <tr>
                                    <td>
                                        <input type="checkbox"  name="checkOne" autocomplete="off" alt="<date:date value ="${skuList.addTime}" format="yyyy.MM.dd"/>" value="${skuList.skuId}"  regularId="${skuList.regularId}" supplementStock="${skuList.safeStock - skuList.stock - skuList.intransitStock + skuList.orderStock}">
                                    </td>
                                    <td>${skuList.skuNo}</td>
                                    <td>${skuList.skuName}</td>
                                    <td>${skuList.brandName}</td>
                                    <td>${skuList.spec}</td>
                                    <td>${skuList.unit}</td>
                                    <td>${skuList.spuType}</td>
                                    <td>${skuList.owerUser}</td>
                                    <td>${skuList.stock}</td>
                                    <td>
                                        <a href="javascript:void(0);"
                                           onclick="getIntransitStock(${skuList.skuId});">
                                                ${skuList.intransitStock}
                                        </a>
                                    </td>
                                    <td>${skuList.orderStock}</td>
                                    <td>${skuList.forecastSafeStock}</td>
                                    <td>
                                        <div class="customername pos_rel">
                                        <span>${skuList.safeStock}
                                        <c:if test="${!empty skuList.isAdjustStock && skuList.isAdjustStock == 1}">
                                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                                            <div class="pos_abs customernameshow">原值：${skuList.originSafeStock}</div>
                                        </c:if>
                                        </div>
                                    <td>
                                        <c:choose>
                                            <c:when test="${empty skuList.stockWarn || skuList.stockWarn == 0}">
                                                <span style="color:green;">安全</span>
                                            </c:when>
                                            <c:when test="${skuList.stockWarn == 1}">
                                                <span style="color:orange;">二级预警-需补货</span>
                                            </c:when>
                                            <c:when test="${skuList.stockWarn == 2}">
                                                <span style="color:red;">一级预警-缺货</span>
                                            </c:when>
                                        </c:choose>
                                    </td>
                                    <td class="supplement-stock">
                                        ${skuList.supplementStock}
                                    </td>
                                    <td>
                                        <c:choose>
                                            <c:when test="${empty skuList.cost}">
                                                -
                                            </c:when>
                                            <c:otherwise >
                                                ${skuList.cost}
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                    <td>${skuList.receiveTimes}</td>
                                    <td>${skuList.outStockTimes}</td>
                                    <td>
                                        <c:choose>
                                            <c:when test="${!empty skuList.skuStatus && skuList.skuStatus == 0}">
                                                <a href="javascript:void(0);" style="margin-right: 10px;"
                                                   onclick="initSafeStockPage(this,${skuList.regularId},${skuList.threeMonthDaysSaleNum});">
                                                    设置安全库存
                                                </a>
                                            </c:when>
                                            <c:otherwise >
                                                <span style="margin-right: 10px;color:#333;">设置安全库存</span>
                                            </c:otherwise>
                                        </c:choose>
                                        <a href="javascript:void(0);" style="margin-right: 10px;"
                                           onclick="initSafeStockLogPage(this,${skuList.regularId});">
                                            安全库存日志
                                        </a>
                                        <c:choose>
                                            <c:when test="${!empty skuList.skuStatus && skuList.skuStatus == 0}">
                                                <a class="deal-a" href="javascript:void(0);" style="margin-right: 10px;" onclick="dealPrepare(this, event)">
                                                    处理
                                                </a>
                                                <div class="deal-tab">
                                                    <ul>
                                                        <li>
                                                            <a onclick="addBHOrder(this,'single');" style="margin-right: 10px;" skuId="${skuList.skuId}" regularId="${skuList.regularId}" supplementStock="${skuList.supplementStock}"
                                                               tabTitle='{"num":"addbhorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"./order/buyorder/addBhOrderNew.do","title":"备货信息"}'
                                                            >生成备货单</a>
                                                        </li>
                                                        <li>
                                                            <a href="javascript:void(0);" style="margin-right: 10px;"
                                                               onclick="initDealPrepareStockPage(this,3,${skuList.regularId},${skuList.stock},${skuList.threeMonthDaysSaleNum},${skuList.intransitStock},${skuList.orderStock},${skuList.receiveTimes},${skuList.outStockTimes},${skuList.cost});">
                                                                暂不备货
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </c:when>
                                            <c:otherwise >
                                                <span style="margin-right: 10px;color:#333;">处理</span>
                                            </c:otherwise>
                                        </c:choose>
                                        <a href="javascript:void(0);"
                                           onclick="initDealDetailPage(this,${skuList.regularId});">
                                            处理详情
                                        </a>
                                    </td>
                                </tr>
                            </c:forEach>
                        </c:when>
                        <c:otherwise>
                            <tr><td colspan="19">暂无数据</td></tr>
                        </c:otherwise>
                    </c:choose>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="parts" style="height: 30px;">
        <tags:page page="${page}" />
    </div>

    <div class="tcenter" style="padding: 10px 0 10px 10px;">
        <span class="bt-largest bt-bg-style bg-light-blue" onclick="addBHOrder(this,'batch');" id="createOrder"
              tabTitle='{"num":"addbhorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"./order/buyorder/addBhOrderNew.do","title":"备货信息"}'
        >生成备货单</span>
    </div>

    <div style="display:none;">
        <!--弹框 -->
        <div class="title-click nobor  pop-new-data" id="prepare_stock"></div>
    </div>

</div>

<style>
    .suggest-list-wrap { position: absolute; background: rgb(255, 255, 255); padding: 10px 0px; box-shadow: rgba(0, 33, 66, 0.1) 2px 2px 3px; overflow-x: unset; z-index: 11; border: 1px solid rgb(206, 210, 217); width: 100%; display: none; max-height: 275px; overflow-y: auto; left: 100px;}
    .suggest-wrap { position: relative; }
    .suggest-list-wrap .suggest-item { padding: 5px 10px; color: rgb(102, 102, 102); display: block; text-decoration: none; text-overflow: ellipsis; overflow: hidden; white-space: nowrap; cursor: pointer; }
    .suggest-list-wrap .suggest-item strong { color: rgb(255, 102, 0); font-size: 12px; }
    .suggest-list-wrap .suggest-item.disabled { color: rgb(194, 194, 194); cursor: default; }
    .suggest-list-wrap .suggest-item.disabled strong { color: rgb(194, 194, 194); cursor: default; font-weight: normal; }
    .suggest-list-wrap .suggest-item.disabled:hover { color: rgb(194, 194, 194); background: rgb(255, 255, 255); }
    .suggest-list-wrap .suggest-item:hover { color: rgb(102, 102, 102); background: rgb(230, 236, 242); }
    .deal-tab {
        display: none;
        position: absolute;
        z-index: 100;
        width: 100px;
        border: 1px solid #ddd;
        background: #fff;
        right: 10px;
    }
    .deal-tab ul li{
        height: 30px;
        line-height: 30px;
        border-bottom: 1px solid #ddd;
    }

    .deal-tab ul li:hover{
        background-color: #f3f3f3;
    }

    .deal-tab ul li a{
        color: gray;
    }

    .deal-tab ul li a:hover {
        color: #3384ef;
    }


</style>

</html>
