package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.BatchBankBillDto;
import com.vedeng.erp.kingdee.batch.dto.BatchCapitalBillDto;
import com.vedeng.erp.kingdee.batch.repository.BatchBankBillDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchCapitalBillDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeeAliReceiveBillDto;
import com.vedeng.erp.kingdee.dto.KingDeeAliReceiveBillEntryDto;
import com.vedeng.erp.kingdee.enums.KingDeeBankTagEnum;
import com.vedeng.erp.kingdee.enums.KingDeeReceiveBillTypeEnum;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeCustomerMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class AliReceiptBillProcessor implements ItemProcessor<BatchBankBillDto, KingDeeAliReceiveBillDto> {

    @Autowired
    private BatchCapitalBillDtoMapper batchCapitalBillDtoMapper;

    @Autowired
    private KingDeeCustomerMapper kingDeeCustomerMapper;

    @Autowired
    private BatchBankBillDtoMapper batchBankBillDtoMapper;

    @Override
    public KingDeeAliReceiveBillDto process(BatchBankBillDto batchBankBillDto) throws Exception {
        log.info("金蝶处理支付宝收款流水信息{},", JSON.toJSONString(batchBankBillDto));
            //流水处理
             KingDeeAliReceiveBillDto kingdeeReceiveBillDto = new KingDeeAliReceiveBillDto();
            //单据编号
            kingdeeReceiveBillDto.setFBillNo(batchBankBillDto.getTranFlow());
            //单据类型
            kingdeeReceiveBillDto.setFBillTypeId(KingDeeReceiveBillTypeEnum.SALE_RECEIVE.getCode());
            //单据日期
            kingdeeReceiveBillDto.setFDate(DateUtil.formatDate(batchBankBillDto.getTranDate()) + " " + DateUtil.formatTime(batchBankBillDto.getTranTime()));
            //流水号
            kingdeeReceiveBillDto.setFQzokLsh(batchBankBillDto.getTranFlow());
            //往来单位类型
            kingdeeReceiveBillDto.setFContactUnitType("BD_Customer");
            //往来单位
          kingdeeReceiveBillDto.setFContactUnit(getContactUnit(kingdeeReceiveBillDto.getFContactUnitType(), batchBankBillDto.getAccName1()));

            //交易主体
            kingdeeReceiveBillDto.setFQzokJyzt("");
            //交易类型
            kingdeeReceiveBillDto.setFQzokJylx("");
            //交易方式
            kingdeeReceiveBillDto.setFQzokJyfs(KingDeeBankTagEnum.matchFQzokJyfsByCode(batchBankBillDto.getBankTag()));
            //单据内码
            kingdeeReceiveBillDto.setFId(0);

            kingdeeReceiveBillDto.setBankBillId(batchBankBillDto.getBankBillId());

            //回单地址
            kingdeeReceiveBillDto.setReceiptUrl(batchBankBillDto.getReceiptUrl());
            List<KingDeeAliReceiveBillEntryDto> kingdeeReceiveBillEntryDtos = new ArrayList<>();
            KingDeeAliReceiveBillEntryDto kingdeeReceiveBillEntryDto = new KingDeeAliReceiveBillEntryDto();
            //结算方式
            List<BatchCapitalBillDto> capitalBillDtoList = batchCapitalBillDtoMapper.queryCapitalBillByBankBillId(batchBankBillDto.getBankBillId());

            if (capitalBillDtoList != null && capitalBillDtoList.size() > 0) {
                BatchCapitalBillDto batchCapitalBillDto = capitalBillDtoList.get(0);
                kingdeeReceiveBillEntryDto.setFAccountId("<EMAIL>");
                kingdeeReceiveBillEntryDto.setFPurposeId("SFKYT01_SYS");
                kingdeeReceiveBillEntryDto.setFRecTotalAmountFor(batchBankBillDto.getAmt());
                kingdeeReceiveBillEntryDto.setFQzokBddjhid(batchCapitalBillDto.getCapitalBillId().toString());
                kingdeeReceiveBillEntryDto.setFQzokYsddh(batchCapitalBillDto.getOrderNo());
                kingdeeReceiveBillEntryDto.setFQzokGsywdh(batchCapitalBillDto.getOrderNo());
                kingdeeReceiveBillEntryDto.setFQzokYwlx("");
                kingdeeReceiveBillEntryDto.setFQzokSkyw("HL033");
                if(StringUtils.isNotBlank(batchBankBillDto.getOrderNo())) {
                    BatchBankBillDto fee = batchBankBillDtoMapper.getAliFeeBankBillByOrderNo(batchBankBillDto.getOrderNo());
                    kingdeeReceiveBillEntryDto.setFHandlingChargeFor(Objects.isNull(fee) ? null : fee.getAmt().toString());
                }
                kingdeeReceiveBillEntryDtos.add(kingdeeReceiveBillEntryDto);
            }

            kingdeeReceiveBillDto.setFReceiveBillEntry(kingdeeReceiveBillEntryDtos);
            return kingdeeReceiveBillDto;
    }

    private String getContactUnit(String contactUnitType,String contact){
        log.info("查询往来单位类型，参数{},{},",contactUnitType,contact);
            //客户
            String kingDeeCustomerDto = kingDeeCustomerMapper.queryFNUMBERByCustomerName(contact);
            if (kingDeeCustomerDto == null){
                log.info("查询客户信息为空，参数{},{},",contactUnitType,contact);
                return null;
            }
            return kingDeeCustomerDto;

    }

}
