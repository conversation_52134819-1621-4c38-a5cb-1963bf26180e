package com.vedeng.erp.mobile.api;

import com.vedeng.erp.mobile.dto.*;

import java.util.List;

public interface AfterSalesMobileApiService {

    /**
     * 根据订单号与售后主体类型查询售后订单
     */
    List<AfterSalesListResultDto> getAfterSalesListByOrderIdAndSubjectType(List<Integer> orderIds, Integer subjectType);

    /**
     * 根据售后订单id集合查询售后商品
     */
    List<AfterSalesGoodsListResultDto> getAfterSalesGoodsListByAfterSalesIds(List<Integer> afterSalesIds);


}
