package com.vedeng.erp.kingdee.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.utils.numgenerator.BillNumGenerator;
import com.vedeng.common.core.utils.numgenerator.bean.BillGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.enums.BillType;
import com.vedeng.erp.kingdee.batch.common.enums.WarehouseGoodsInEnum;
import com.vedeng.erp.kingdee.batch.common.enums.WarehouseOutInSourceEnum;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.*;
import com.vedeng.erp.kingdee.common.utils.HashUtils;
import com.vedeng.erp.kingdee.service.KingDeeWarehouseGoodsInService;
import com.vedeng.infrastructure.file.domain.Attachment;
import com.vedeng.infrastructure.file.mapper.AttachmentMapper;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import com.vedeng.infrastructure.oss.service.OssUtilsService;
import com.vedeng.infrastructure.oss.service.domain.UrlToPdfParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class KingDeeWarehouseGoodsInServiceImpl implements KingDeeWarehouseGoodsInService {

	private static final double SCALE = 0.6;

	private static final int LT_LENGTH = 10;
	
	private static final int AT_LENGTH = 16;
	
	private static final String RENDER_URL = "/api/render";

	public static final String str_6840 = "6840体外诊断试剂";
	
	@Value("${oss_http}")
    private String ossHttp;
	
	@Value("${html2Pdf.domain}")
    private String html2PdfDomain;
	
	//入库单验收报告pdf中图片地址
	@Value("${warehouseReport.imgpath}")
	private String imgPath;
	
	@Autowired
    private OssUtilsService ossUtilsService;
	
	@Autowired
	private AttachmentMapper attachmentMapper;

	@Autowired
	private BatchBuyorderDtoMapper batchBuyorderDtoMapper;

	@Autowired
	private BatchBuyorderGoodsDtoMapper batchBuyorderGoodsDtoMapper;

	@Autowired
	private BatchWarehouseGoodsOutInDtoMapper batchWarehouseGoodsOutInDtoMapper;

	@Autowired
	private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;

	@Autowired
	private BatchRExpressWarehouseGoodsOutInDtoMapper batchRExpressWarehouseGoodsOutInDtoMapper;

	@Autowired
	private BatchCoreSkuDtoMapper batchCoreSkuDtoMapper;

	@Autowired
	private BatchUserDtoMapper batchUserDtoMapper;

	@Autowired
	private BatchCoreSpuDtoMapper batchCoreSpuDtoMapper;

	@Autowired
	private BatchWmsInOrderMapper batchWmsInOrderMapper;

	@Autowired
	private BatchWmsInOutPersonMapper batchWmsInOutPersonMapper;

	@Autowired
	private BatchWmsOutputOrderDtoMapper batchWmsOutputOrderDtoMapper;

	private static final List<Integer> EQUIPMENT_OR_ACCESSORIES = Arrays.asList(316, 1008);

	/**
	 * 采购直发入库（维护物流信息节点）
	 *
	 * @param user              ： 用户信息
	 * @param pkId              ：  采购订单为 T_BUYORDER 主键buyOrderId 售后订单为 T_AFTER_SALES 主键AFTER_SALES_ID
	 * @param goodsIdAndSendNum ：  byOrderGoodsId 为T_BUYORDER_GOODS中的主键或者为T_AFTER_SALES_GOODS中的主键 ，sendNum 为发货数量
	 * @param erpOutInType
	 * @return
	 */
	@Override
	@Transactional
	public Map<Integer, Boolean> insertWarehouseGoodsPurchaseDirectOutInDirect(BatchUserDto user, Integer pkId,
																			   List<Map<String, Object>> goodsIdAndSendNum, WarehouseGoodsInEnum erpOutInType, BatchExpressDto express) throws Exception {
		//step1：新增：T_WAREHOUSE_GOODS_OUT_IN 
		//step2：遍历商品信息 新增：T_WAREHOUSE_GOODS_OPERATE_LOG_DIRECT
		BatchWarehouseGoodsOutInDto warehouseGoodsOutIn = new BatchWarehouseGoodsOutInDto();
		//ERP出入库单号
		BillGeneratorBean billGeneratorBean = new BillGeneratorBean(BillType.WAREHOUSE_GOODS_OUT_IN_RK);
        String outInNo = new BillNumGenerator().distribution(billGeneratorBean);
		warehouseGoodsOutIn.setOutInNo(outInNo);
		//wmsNo直发的给默认值
		warehouseGoodsOutIn.setWmsNo("");
		//根据erpOutInType，配置采购单号或者售后单号,以及出入库方
		String relateNo = "";
		String outInCompany = "";
		//根据采购订单表，查询采购订单收发货方
		BatchBuyorderDto buyorder = batchBuyorderDtoMapper.selectByPrimaryKey(pkId);
		log.info("采购订单表信息：{}",JSON.toJSONString(buyorder));
		if(Objects.nonNull(buyorder)) {
			relateNo = buyorder.getBuyorderNo();
			outInCompany = buyorder.getTraderName();
		}
		warehouseGoodsOutIn.setRelateNo(relateNo);
		warehouseGoodsOutIn.setOutInCompany(outInCompany);
		Date currentDate = new Date();
		warehouseGoodsOutIn.setOutInTime(new Date(express.getArrivalTime()));
		warehouseGoodsOutIn.setSource(WarehouseOutInSourceEnum.DIRECT_DELIVERY.getSource());
		warehouseGoodsOutIn.setAddTime(currentDate);
		//维护物流信息的人
		if(Objects.nonNull(user)) {
			warehouseGoodsOutIn.setCreator(user.getUserId());
			warehouseGoodsOutIn.setCreatorName(user.getUsername());
		}
		//如果是采购入库，并且采购单赠品为是：入库类型改为采购赠品入库
		if(Objects.nonNull(buyorder) && Objects.equals(buyorder.getIsGift(), 1) && Objects.equals(erpOutInType, WarehouseGoodsInEnum.PURCHASE_IN)) {
			warehouseGoodsOutIn.setOutInType(WarehouseGoodsInEnum.PURCHASE_GIFT_IN.getErpCode());
		}else {
			warehouseGoodsOutIn.setOutInType(erpOutInType.getErpCode());
		}

		log.info("直发采购或者采购售后换货入库日志主表插入数据：{}",JSON.toJSONString(warehouseGoodsOutIn));
		batchWarehouseGoodsOutInDtoMapper.insertSelective(warehouseGoodsOutIn);
		BatchWarehouseGoodsOutInDto insertFlag = batchWarehouseGoodsOutInDtoMapper.queryInfoByNo(warehouseGoodsOutIn.getOutInNo(), warehouseGoodsOutIn.getOutInType());
		if (Objects.isNull(insertFlag)){
			log.error("入库单主表数据插入失败warehouseGoodsOutIn:{}",JSON.toJSONString(warehouseGoodsOutIn));
			throw new KingDeeException("入库单主表数据插入失败");
		}
		BatchRExpressWarehouseGoodsOutInDto rExpressWarehouseGoodsOutInDto = new BatchRExpressWarehouseGoodsOutInDto();
		rExpressWarehouseGoodsOutInDto.setWarehouseGoodsOutInId(warehouseGoodsOutIn.getWarehouseGoodsOutInId().intValue());
		rExpressWarehouseGoodsOutInDto.setExpressId(express.getExpressId());
		CurrentUser currentUser = CurrentUser.getCurrentUser();
		rExpressWarehouseGoodsOutInDto.setCreator(currentUser.getId());
		rExpressWarehouseGoodsOutInDto.setCreatorName(currentUser.getUsername());
		rExpressWarehouseGoodsOutInDto.setUpdater(currentUser.getId());
		rExpressWarehouseGoodsOutInDto.setUpdaterName(currentUser.getUsername());
		batchRExpressWarehouseGoodsOutInDtoMapper.insertSelective(rExpressWarehouseGoodsOutInDto);
		//异步生成入库单验收报告
		List<BatchWarehouseGoodsOutInItemDto> warehouseGoodsOutInItemList = new ArrayList<>();
		Map<Integer, Boolean> splitOrNotMap = new HashMap<>();
		for (Map<String, Object> map : goodsIdAndSendNum) {
			Integer sendNum = (Integer) map.get("sendNum");
			//插入明细表T_WAREHOUSE_GOODS_OPERATE_LOG_DIRECT
			BatchWarehouseGoodsOutInItemDto warehouseGoodsOutInItem = new BatchWarehouseGoodsOutInItemDto();
			warehouseGoodsOutInItem.setCompanyId(1);
			warehouseGoodsOutInItem.setOperateType(warehouseGoodsOutIn.getOutInType());

			//relatedId 关联采购、销售、售后产品ID  表T_BUYORDER_GOODS 字段BUYORDER_GOODS_ID  表T_AFTER_SALES_GOODS字段GOODS_ID
			Integer relatedId = 0;
			Integer goodsId = 0;
			relatedId = (Integer) map.get("byOrderGoodsId");
			BatchBuyorderGoodsDto byOrderGoods = batchBuyorderGoodsDtoMapper.selectByPrimaryKey(relatedId);
			if (Objects.nonNull(byOrderGoods)) {
				goodsId = byOrderGoods.getGoodsId();
			}

			warehouseGoodsOutInItem.setRelatedId(relatedId);
			warehouseGoodsOutInItem.setGoodsId(goodsId);
			warehouseGoodsOutInItem.setCheckStatusTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(express.getArrivalTime())));
			warehouseGoodsOutInItem.setAddTime(currentDate);
			//维护物流信息的人
			if (Objects.nonNull(user)) {
				warehouseGoodsOutInItem.setCreator(user.getUserId());
			} else {
				warehouseGoodsOutInItem.setCreator(2);
			}
			BatchCoreSkuDto coreSku = batchCoreSkuDtoMapper.selectByPrimaryKey(goodsId);

			boolean spuTypeFlag = false;

			if (Objects.nonNull(coreSku)) {
				BatchCoreSpuDto coreSpu = batchCoreSpuDtoMapper.selectByPrimaryKey(coreSku.getSpuId());
				// 商品类型 = 设备 或 配件
				spuTypeFlag = Objects.nonNull(coreSpu) && EQUIPMENT_OR_ACCESSORIES.contains(coreSpu.getSpuType());
			}

			// 该采购订单关联的销售订单中存在该SKU行 报价含安调 = 是
			boolean haveInstallationFlag = CollUtil.isNotEmpty(batchBuyorderGoodsDtoMapper.findHaveInstallationByBuyorderGoodsId(relatedId));

			String skuNo = Objects.nonNull(coreSku) ? coreSku.getSkuNo() : "";

			boolean getSn = Objects.nonNull(coreSku) && spuTypeFlag && (Boolean.TRUE.equals(coreSku.getIsFactorySnCode()) || haveInstallationFlag);

			String sn = "";
			//厂商批次号、贝登批次码 默认为空
			String batchNumber = "";
			warehouseGoodsOutInItem.setLogType(0);
			warehouseGoodsOutInItem.setBarcodeFactory(sn);
			warehouseGoodsOutInItem.setOutInNo(outInNo);
			//是否厂家赋SN码”为“是，一物一sn码，厂商批次号为空
			if (getSn) {
				splitOrNotMap.put(relatedId, true);
				List<BatchWarehouseGoodsOutInItemDto> snItemList = new ArrayList<>();
				for (int size = 0; size < sendNum; size++){
					warehouseGoodsOutInItem.setNum(new BigDecimal(1));
					warehouseGoodsOutInItem.setBatchNumber(batchNumber);
					warehouseGoodsOutInItem.setVedengBatchNumber(batchNumber);
					snItemList.add(warehouseGoodsOutInItem);
				}
				batchWarehouseGoodsOutInItemDtoMapper.batchInsert(snItemList);

				List<BatchWarehouseGoodsOutInItemDto> updateItemList = batchWarehouseGoodsOutInItemDtoMapper.findByAll(warehouseGoodsOutInItem);
				if (CollUtil.isEmpty(updateItemList) || updateItemList.size() != snItemList.size()) {
					log.error("入库单明细表数据批量插入失败snItemList:{},updateItemList:{}", JSON.toJSONString(snItemList),JSON.toJSONString(updateItemList));
					throw new KingDeeException("生成采购直发入库单,入库单明细表数据批量插入失败");
				}

				List<BatchWarehouseGoodsOutInItemDto> toUpdateList = updateItemList.stream()
						.peek(item -> item.setBarcodeFactory("AT" + HashUtils.md5ToHexString(String.valueOf(item.getWarehouseGoodsOutInDetailId()), AT_LENGTH)))
						.collect(Collectors.toList());

				log.info("是否厂家赋SN码”为“是，一物一sn码,更新sn码:{}", toUpdateList.stream().map(BatchWarehouseGoodsOutInItemDto::getBarcodeFactory).collect(Collectors.toList()));
				batchWarehouseGoodsOutInItemDtoMapper.updateBatchBarcodeFactory(toUpdateList);
				warehouseGoodsOutInItemList.addAll(toUpdateList);
			}
			//是否厂家赋SN码”为“否，sn码为空，共一个厂商批次号
			else {
				splitOrNotMap.put(relatedId, false);
				//厂商批次号、贝登批次码 LT+(订单号+sku订货号+出库时间)维度生成的12位码
				String dateStr = new SimpleDateFormat("yyyyMMdd").format(currentDate);
				batchNumber = "LT" + HashUtils.md5ToHexString(relateNo + skuNo + dateStr, LT_LENGTH);
				log.info("厂商批号、贝登批次码：{}", batchNumber);
				warehouseGoodsOutInItem.setBatchNumber(batchNumber);
				warehouseGoodsOutInItem.setVedengBatchNumber(batchNumber);
				warehouseGoodsOutInItem.setNum(new BigDecimal(sendNum));
				log.info("直发采购或者采购售后换货入库日志明细表插入数据：{}", JSON.toJSONString(warehouseGoodsOutInItem));
				batchWarehouseGoodsOutInItemDtoMapper.insertSelective(warehouseGoodsOutInItem);
				warehouseGoodsOutInItemList.add(warehouseGoodsOutInItem);
			}
		}
		try {
			// 生成入库单验收报告
			createWarehouseGoodsInReport(warehouseGoodsOutIn, warehouseGoodsOutInItemList);
		} catch (Exception e) {
			log.error("生成入库单验收报告失败", e);
		}

		splitOrNotMap.put(-1, WarehouseGoodsInEnum.PURCHASE_IN.getErpCode().equals(warehouseGoodsOutIn.getOutInType()));

		return splitOrNotMap;
	}

	@Override
	public void createWarehouseGoodsInReport(BatchWarehouseGoodsOutInDto warehouse,List<BatchWarehouseGoodsOutInItemDto> warehouseGoodsOutInItemList) {
		log.info("入库单主信息：{}，入库单明细信息：{}",warehouse,JSON.toJSONString(warehouseGoodsOutInItemList));
		//只生成普发采购入库和普发采购换货入库的验收报告
		Integer outInType = warehouse.getOutInType();
		if(CollectionUtils.isEmpty(warehouseGoodsOutInItemList)) {
			return;
		}
		//拼接HTML文档
		StringBuilder sb = new StringBuilder();
		boolean isOne = outInType.equals(WarehouseGoodsInEnum.PURCHASE_IN.getErpCode()) ||
				outInType.equals(WarehouseGoodsInEnum.BUYORDER_WAREHOUSE_CHANGE_IN.getErpCode()) ||
				outInType.equals(WarehouseGoodsInEnum.ORDER_WAREHOUSE_CHANGE_IN.getErpCode()) ||
				outInType.equals(WarehouseGoodsInEnum.ORDER_WAREHOUSE_BACK_IN.getErpCode()) ||
				outInType.equals(WarehouseGoodsInEnum.PURCHASE_GIFT_IN.getErpCode());


		if (isOne) {
			templateOneIn(warehouse, warehouseGoodsOutInItemList, outInType, sb);
		} else {
			templateOtherIn(warehouse, warehouseGoodsOutInItemList, outInType, sb);
		}
		log.info("create html in param:{}",sb);
		try {
			htmlStrToPdfGetFile(sb.toString(),warehouse);
		} catch (Exception e) {
			log.error("转换PDF失败...",e);
		}
	}
	private void templateOneIn(BatchWarehouseGoodsOutInDto warehouse, List<BatchWarehouseGoodsOutInItemDto> warehouseGoodsOutInItemList, Integer outInType, StringBuilder sb) {
		//替换：验收报告编号（outInNo）、关联订单号(relateNo)、供应商名称(outInCompany)
		String preFix = "<html><head><title>入库验收报告</title><meta http-equiv=\"X-UA-Compatible\"content=\"IE=edge\"/><meta http-equiv=\"content-type\"content=\"text/html; charset=utf-8\"/></head><style>.ax_default{font-family:'Arial Normal','Arial',sans-serif;font-weight:400;font-style:normal;font-size:13px;letter-spacing:normal;color:#333333;vertical-align:none;text-align:center;line-height:normal;text-transform:none;margin-left:180px}.H2{font-family:'Arial Normal','Arial',sans-serif;font-weight:bold;font-style:normal;font-size:24px;text-align:left;margin-left:680px}.label{font-size:14px;text-align:left}.imgs{font-size:14px;text-align:right}body{margin:0px;background-image:none;position:relative;left:-176px;width:1371px;margin-left:auto;margin-right:auto;text-align:left}#base{position:absolute;z-index:0}td{text-align-last:center;border:solid 1px#000}th{border:solid 1px#000}table{border-spacing:0;border-collapse:collapse}</style><body><div id=\"base\"class=\"\"><!--Unnamed(矩形)--><div id=\"u0\"class=\"ax_default H2\"><div id=\"u0_text\"class=\"text \"><p><span>入库验收报告</span></p></div></div><!--Unnamed(矩形)--><div id=\"u1\"class=\"ax_default label\"><div id=\"u0_text\"class=\"text \"><p><span >单据类型：outInType</span><span style=\"margin-left:60px\">入库单编号：outInNo</span><span style=\"margin-left:60px\">关联订单号：relateNo</span><span style=\"margin-left:60px\">供应商/客户名称：outInCompany</span></p></div></div><!--Unnamed(表格)--><div id=\"u3\"class=\"ax_default\"><table><thead><tr><th rowspan=2 style=\"width:50px;height:30px\">序号</th><th rowspan=2 style=\"width:20%\">产品名称</th><th rowspan=2 style=\"width:80px\">订货号</th><th rowspan=2 style=\"width:5%\">规格型号</th><th rowspan=2 style=\"width:80px\">单位</th><th rowspan=2 style=\"width:15%\">注册证编号</th><th rowspan=2 style=\"width:80px\">收货数量</th><th rowspan=2 style=\"width:80px\">验收数量</th><th rowspan=2 style=\"width:80px\">验收结果</th><th colspan=5 style=\"width:80px;height:35px\">验收项目</th><th rowspan=2 style=\"width:80px\">入库状态</th><tr><th style=\"width:80px\">外观</th><th style=\"width:80px\">包装</th><th style=\"width:80px\">标签</th><th style=\"width:80px\">合格证明文件</th><th style=\"width:80px\">效期</th></tr></tr></thead><tbody id=\"effect\">";
		String typeByCode = WarehouseGoodsInEnum.getTypeByCode(outInType);
		sb.append(preFix.replace("outInNo", warehouse.getOutInNo()).replace("relateNo", warehouse.getRelateNo()).replace("outInCompany", warehouse.getOutInCompany()).replace("outInType", typeByCode));


		//替换：验收报告明细 序号（pageIndex）、产品名称（goodsName）、订货号（skuNo）、规格型号（goodsModel）、注册证编号（registerNo）、收货数量（receiveNum）、验收数量（checkNum）
		//验收结果（checkResult）、入库状态（goodsStorageStatus）、外观（goodsFace）、包装（goodsPackage）、标签（goodsSign）、合格证明文件（certificateFile）、效期（goodsPeriod）
		boolean has6840 = false;
		for (int i = 0; i < warehouseGoodsOutInItemList.size(); i++) {
			BatchWarehouseGoodsOutInItemDto warehouseGoodsOutInItem = warehouseGoodsOutInItemList.get(i);
			//如果为采购入库
			BatchOutInDetailDto skuInfo = new BatchOutInDetailDto();
			Integer skuId =  warehouseGoodsOutInItem.getGoodsId();
			querySkuInfo(skuInfo, skuId);
			boolean is6840 = skuInfo.isIs6840();
			if (is6840) {
				has6840 = true;
			}
			String middleFix = "<tr><td>pageIndex</td><td>goodsName</td><td>skuNo</td><td>goodsModel</td><td>unit</td><td>registerNo</td><td>receiveNum</td><td>checkNum</td><td>checkResult</td><td>goodsFace</td><td>goodsPackage</td><td>goodsSign</td><td>certificateFile</td><td>goodsPeriod</td><td>goodsStorageStatus</td></tr>";
			StringBuilder model = new StringBuilder();
			if (StrUtil.isNotEmpty(skuInfo.getSpec())) {
				model.append(skuInfo.getSpec());
			}
			if (StrUtil.isNotEmpty(skuInfo.getSpec())&&StrUtil.isNotEmpty(skuInfo.getModel())) {
				model.append("/");
			}
			model.append(skuInfo.getModel());
			sb.append(middleFix.replace("pageIndex", String.valueOf(i+1))
					.replace("goodsName", skuInfo.getSkuName()).replace("skuNo", skuInfo.getSkuNo())
					.replace("goodsModel", model.toString())
					.replace("registerNo", skuInfo.getRegistrationNumber()).replace("receiveNum", String.valueOf(warehouseGoodsOutInItem.getNum())).replace("checkNum", String.valueOf(warehouseGoodsOutInItem.getNum()))
					.replace("checkResult", "合格").replace("goodsStorageStatus", "已入库").replace("goodsFace", "合格").replace("goodsPackage", "合格").replace("goodsSign", "合格").replace("certificateFile", "合格")
					.replace("goodsPeriod", "合格"));
		}

		String surFix = "</tbody></table></div><!--Unnamed(矩形)--><div id=\"u43\"class=\"ax_default label\"><div id=\"u0_text\"class=\"text \"><p><span >收货员：receiver</span><span style=\"margin-left:300px\">验收员：checker</span><span style=\"margin-left:300px\">验收入库时间：checkTime</span></p></div></div><div class=\"ax_default imgs\"><div><span style=\"color: red;border: 3px solid red;font-size: 25px;padding: 10px\">入库验收完成</span></div></div></div></body></html>";
		//获取收货员 验收员
		BatchWmsInOrderDto data = new BatchWmsInOrderDto();
		getConsigneeAndInspector(warehouse, has6840, data);

		//替换：收货员（receiver）、验收员（checker）、验收入库时间（checkTime）
		sb.append(surFix.replace("receiver", StrUtil.isEmpty(data.getConsignee())?"":data.getConsignee())
				.replace("checker", StrUtil.isEmpty(data.getInspector())?"":data.getInspector())
				.replace("checkTime", DateUtil.formatDate(warehouse.getOutInTime())));
	}

	private void templateOtherIn(BatchWarehouseGoodsOutInDto warehouse, List<BatchWarehouseGoodsOutInItemDto> warehouseGoodsOutInItemList, Integer outInType, StringBuilder sb) {
		//替换：验收报告编号（outInNo）、关联订单号(relateNo)、供应商名称(outInCompany)
		String preFix = "<html><head><title>入库报告</title><meta http-equiv=\"X-UA-Compatible\"content=\"IE=edge\"/><meta http-equiv=\"content-type\"content=\"text/html; charset=utf-8\"/></head><style>.ax_default{font-family:'Arial Normal','Arial',sans-serif;font-weight:400;font-style:normal;font-size:13px;letter-spacing:normal;color:#333333;vertical-align:none;text-align:center;line-height:normal;text-transform:none;margin-left:180px}.H2{font-family:'Arial Normal','Arial',sans-serif;font-weight:bold;font-style:normal;font-size:24px;text-align:left;margin-left:680px}.label{font-size:14px;text-align:left}.imgs{font-size:14px;text-align:right}body{margin:0px;background-image:none;position:relative;left:-176px;width:1371px;margin-left:auto;margin-right:auto;text-align:left}#base{position:absolute;z-index:0}td{text-align-last:center;border:solid 1px#000}th{border:solid 1px#000}table{border-spacing:0;border-collapse:collapse}</style><body><div id=\"base\"class=\"\"><!--Unnamed(矩形)--><div id=\"u0\"class=\"ax_default H2\"><div id=\"u0_text\"class=\"text \"><p><span>入库报告</span></p></div></div><!--Unnamed(矩形)--><div id=\"u1\"class=\"ax_default label\"><div id=\"u0_text\"class=\"text \"><p><span>单据类型：outInType</span><span style=\"margin-left:60px\">入库单编号：outInNo</span><span style=\"margin-left:60px\">关联订单号：relateNo</span><span style=\"margin-left:60px\">供应商/客户名称：outInCompany</span></p></div></div><!--Unnamed(表格)--><div id=\"u3\"class=\"ax_default\"><table><thead><tr><th rowspan=2 style=\"width:50px;height:30px\">序号</th><th rowspan=2 style=\"width:20%\">产品名称</th><th rowspan=2 style=\"width:100px\">订货号</th><th rowspan=2 style=\"width:100px\">规格型号</th><th rowspan=2 style=\"width:80px\">单位</th><th rowspan=2 style=\"width:20%\">注册证编号</th><th rowspan=2 style=\"width:120px\">入库数量</th><th rowspan=2 style=\"width:150px\">入库状态</th></tr></thead><tbody id=\"effect\">";
		String outInCompany = "/";
		if (outInType.equals(WarehouseGoodsInEnum.LENDOUT_WAREHOUSE_IN.getErpCode()) ) {
			BatchWmsOutputOrderDto byOrderNo = batchWmsOutputOrderDtoMapper.findByOrderNo(warehouse.getRelateNo());
			if (Objects.nonNull(byOrderNo)) {
				outInCompany = byOrderNo.getBorrowTraderName();
			}
		}
		String typeByCode = WarehouseGoodsInEnum.getTypeByCode(outInType);
		sb.append(preFix.replace("outInType", typeByCode).replace("outInNo", warehouse.getOutInNo()).replace("relateNo", warehouse.getRelateNo()).replace("outInCompany", outInCompany));
		boolean has6840 = false;
		if(CollUtil.isNotEmpty(warehouseGoodsOutInItemList)) {
			for (int i = 0; i < warehouseGoodsOutInItemList.size(); i++) {
				BatchWarehouseGoodsOutInItemDto warehouseGoodsOutInItem = warehouseGoodsOutInItemList.get(i);
				BatchOutInDetailDto skuInfo = new BatchOutInDetailDto();
				Integer skuId =  warehouseGoodsOutInItem.getGoodsId();
				querySkuInfo(skuInfo, skuId);
				boolean is6840 = skuInfo.isIs6840();
				if (is6840) {
					has6840 = true;
				}
				String middleFix = "<tr><td>pageIndex</td><td>goodsName</td><td>skuNo</td><td>goodsModel</td><td>unit</td><td>registerNo</td><td>receiveNum</td><td>goodsStorageStatus</td></tr>";
				StringBuilder model = new StringBuilder();
				if (StrUtil.isNotEmpty(skuInfo.getSpec())) {
					model.append(skuInfo.getSpec());
				}
				if (StrUtil.isNotEmpty(skuInfo.getSpec())&&StrUtil.isNotEmpty(skuInfo.getModel())) {
					model.append("/");
				}
				model.append(skuInfo.getModel());

				sb.append(middleFix.replace("pageIndex", String.valueOf(i+1))
						.replace("goodsName", skuInfo.getSkuName())
						.replace("skuNo", skuInfo.getSkuNo())
						.replace("goodsModel", model.toString())
						.replace("registerNo", skuInfo.getRegistrationNumber()).replace("receiveNum", String.valueOf(warehouseGoodsOutInItem.getNum()))
						.replace("unit", skuInfo.getUnitName()).replace("goodsStorageStatus", "已入库"));
			}
		}

		BatchWmsInOrderDto data = new BatchWmsInOrderDto();
		getConsigneeAndInspector(warehouse, has6840, data);

		String surFix = "</tbody></table></div><!--Unnamed(矩形)--><div id=\"u43\"class=\"ax_default label\"><div id=\"u0_text\"class=\"text \"><p><span>收货员：receiver</span><span style=\"margin-left:600px\">入库时间：checkTime</span></p></div></div><div class=\"ax_default imgs\"><div><span style=\"color: red;border: 3px solid red;font-size: 25px;padding: 10px\">入库完成</span></div></div></div></body></html>";

		//替换：收货员（receiver）、验收员（checker）、验收入库时间（checkTime）
		sb.append(surFix.replace("receiver", StrUtil.isEmpty(data.getConsignee())?"":data.getConsignee())
				.replace("checkTime", DateUtil.formatDate(warehouse.getOutInTime())));
	}

	private void getConsigneeAndInspector(BatchWarehouseGoodsOutInDto warehouse, boolean has6840, BatchWmsInOrderDto data) {
		if (StrUtil.isNotEmpty(warehouse.getWmsNo())) {
			List<BatchWmsInOrderDto> wmsInOrderDtos = batchWmsInOrderMapper.selectByWmsNo(warehouse.getWmsNo());
			if (CollUtil.isNotEmpty(wmsInOrderDtos)) {
				data.setConsignee(wmsInOrderDtos.get(0).getConsignee());
				data.setInspector(wmsInOrderDtos.get(0).getInspector());
			}
		}
		if (StrUtil.isEmpty(data.getConsignee())) {
			DateTime dateTime = DateUtil.parseDate("2023-11-28");
			DateTime outInDate = DateUtil.parseDate(DateUtil.formatDateTime(warehouse.getOutInTime()));
			String s = DateUtil.formatDate(outInDate);
			int compare = DateUtil.compare(dateTime, outInDate);
			if (compare<0) {
				List<BatchWmsInOutPersonDto> wmsInOutPersonDtos = batchWmsInOutPersonMapper.selectByType(1001);
				if (CollUtil.isNotEmpty(wmsInOutPersonDtos)) {
					data.setConsignee(wmsInOutPersonDtos.get((int) (Math.random() * wmsInOutPersonDtos.size())).getName());
				}
			} else {
				List<BatchWmsInOutPersonDto> wmsInOutPersonDtos = batchWmsInOutPersonMapper.selectByTypeAndDate(1, s);
				if (CollUtil.isNotEmpty(wmsInOutPersonDtos)) {
					data.setConsignee(wmsInOutPersonDtos.get((int) (Math.random() * wmsInOutPersonDtos.size())).getName());
				}

			}
		}

		if (StrUtil.isEmpty(data.getInspector())) {
			if (!has6840) {
				DateTime dateTime = DateUtil.parseDate("2023-11-28");
				DateTime outInDate = DateUtil.parseDate(DateUtil.formatDateTime(warehouse.getOutInTime()));
				int compare = DateUtil.compare(dateTime, outInDate);
				String s = DateUtil.formatDate(outInDate);
				if (compare<0) {
					List<BatchWmsInOutPersonDto> wmsInOutPersonDtos = batchWmsInOutPersonMapper.selectByType(1002);
					if (CollUtil.isNotEmpty(wmsInOutPersonDtos)) {
						data.setInspector(wmsInOutPersonDtos.get((int) (Math.random() * wmsInOutPersonDtos.size())).getName());
					}
				} else {
					List<BatchWmsInOutPersonDto> wmsInOutPersonDtos = batchWmsInOutPersonMapper.selectByTypeAndDate(2, s);
					if (CollUtil.isNotEmpty(wmsInOutPersonDtos)) {
						data.setInspector(wmsInOutPersonDtos.get((int) (Math.random() * wmsInOutPersonDtos.size())).getName());
					}
				}
			} else {
				data.setInspector("桑阿敏");
			}

		}
	}
	/**
		 * 获取商品明细信息
		 * @param skuInfo
		 * @param skuId
		 */
		@Override
		public void querySkuInfo(BatchOutInDetailDto skuInfo, Integer skuId) {
			List<Integer> skuIdList = Arrays.asList(skuId);
			List<Map<String, Object>> maps = batchCoreSkuDtoMapper.skuTipList(skuIdList);
			// List<Map<String, Object>> maps = goodsMapper.skuTipList(skuIdList);
			if(CollUtil.isNotEmpty(maps)) {
				Map<String,Object> sku = maps.get(0);
				if(Objects.nonNull(sku.get("FIRST_ENGAGE_ID"))) {
					skuInfo.setFirstEngageId(Integer.valueOf(sku.get("FIRST_ENGAGE_ID").toString()));
					Integer new_standard_category_id = Integer.valueOf(sku.get("NEW_STANDARD_CATEGORY_ID").toString());
					Integer old_standard_category_id = Integer.valueOf(sku.get("OLD_STANDARD_CATEGORY_ID").toString());

					// 新国标
					if( null != new_standard_category_id && new_standard_category_id > 0){
						// 根据新国标分类id查询新国标分类
						String s = batchCoreSkuDtoMapper.selectStandardCategoryByCategoryId(new_standard_category_id);
						if (str_6840.equals(s)) {
							skuInfo.setIs6840(true);
						}
					}
					// 旧国标
					if(null != old_standard_category_id && old_standard_category_id > 0){
						String title= batchCoreSkuDtoMapper.selectByOldStandardCategory(old_standard_category_id);
						if (str_6840.equals(title)) {
							skuInfo.setIs6840(true);
						}
					}
				}
				skuInfo.setSkuId(Integer.valueOf(sku.get("SKU_ID").toString()));
				skuInfo.setSpuId(Integer.valueOf(sku.get("SPU_ID").toString()));
				skuInfo.setSkuNo(sku.get("SKU_NO").toString());

				if(Objects.nonNull(sku.get("SHOW_NAME"))){
					skuInfo.setSkuName(sku.get("SHOW_NAME").toString());
				}
				if(Objects.nonNull(sku.get("BRAND_NAME"))){
					skuInfo.setBrandName(sku.get("BRAND_NAME").toString());
				}
				if(Objects.nonNull(sku.get("SPEC"))){
					skuInfo.setSpec(sku.get("SPEC").toString());
				}
				if(Objects.nonNull(sku.get("MODEL"))){
					skuInfo.setModel(sku.get("MODEL").toString());
				}
				if(Objects.nonNull(sku.get("REGISTRATION_NUMBER"))){
					skuInfo.setRegistrationNumber(sku.get("REGISTRATION_NUMBER").toString());
				}
				if(Objects.nonNull(sku.get("UNIT_NAME"))){
					skuInfo.setUnitName(sku.get("UNIT_NAME").toString());
				}
				if(Objects.nonNull(sku.get("FIRST_ENGAGE_ID"))){
					skuInfo.setFirstEngageId(Integer.valueOf(sku.get("FIRST_ENGAGE_ID").toString()));
				}
			}
		}


		/**
		 * HTML文本转PDF
		 * @param html 文本
		 * @param warehouseGoodsOutIn 入库单主表数据
		 */
		@Retryable(value=NullPointerException.class,maxAttempts = 3,backoff = @Backoff(delay = 2000L,multiplier = 1.5))
		public void htmlStrToPdfGetFile(String html,BatchWarehouseGoodsOutInDto warehouseGoodsOutIn) {
			String html2PdfUrl = html2PdfDomain + RENDER_URL;
			UrlToPdfParam urlToPdfParam = new UrlToPdfParam();
			urlToPdfParam.setHtml(html);
			UrlToPdfParam.Pdf pdf = new UrlToPdfParam.Pdf();
			UrlToPdfParam.Pdf.PdfMargin margin = new UrlToPdfParam.Pdf.PdfMargin("1cm" , "1cm" , "1cm" , "0cm");
			pdf.setMargin(margin);
			pdf.setScale(SCALE);
			urlToPdfParam.setPdf(pdf);
			// 上传入库单验收报告返回oss链接
			String ossUrl = ossUtilsService.migrateFile2Oss(html2PdfUrl, "pdf","验收单" + warehouseGoodsOutIn.getOutInNo(), urlToPdfParam);
			log.info("生成入库验收报告,入库单号：{},返回地址ossUrl:{}" , warehouseGoodsOutIn.getOutInNo(),ossUrl);
			if(StringUtils.isBlank(ossUrl)){
				log.error("生成入库验收报告失败");
				throw new NullPointerException("生成入库验收报告失败");
			}

			//新增验收报告附件
			Attachment attachment = new Attachment();
			attachment.setAttachmentType(KingDeeConstant.WAREHOUSER_ATTACHMET_TYPE);
			attachment.setAttachmentFunction(KingDeeConstant.WAREHOUSER_ATTACHMET_FUNCTION);
			attachment.setRelatedId(Integer.parseInt(String.valueOf(warehouseGoodsOutIn.getWarehouseGoodsOutInId())));

			String domainAndUri = ossUrl.split(ossHttp)[1];
			int domainIndex = domainAndUri.indexOf('/');
			String domain = domainAndUri.substring(0, domainIndex);
			String uri = domainAndUri.substring(domainIndex);
			attachment.setDomain(domain);
			attachment.setUri(uri);
			attachment.setName("验收单");
			attachment.setSuffix("pdf");
			attachment.setAddTime(System.currentTimeMillis());
			attachmentMapper.insertSelective(attachment);
    }

}
