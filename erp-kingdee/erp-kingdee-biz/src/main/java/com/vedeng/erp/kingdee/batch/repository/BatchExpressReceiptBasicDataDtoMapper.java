package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchExpressReceiptBasicDataDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BatchExpressReceiptBasicDataDtoMapper {
    int deleteByPrimaryKey(Integer expressReceiptBasicDataId);

    int insert(BatchExpressReceiptBasicDataDto record);

    int insertOrUpdate(BatchExpressReceiptBasicDataDto record);

    int insertOrUpdateSelective(BatchExpressReceiptBasicDataDto record);

    int insertSelective(BatchExpressReceiptBasicDataDto record);

    BatchExpressReceiptBasicDataDto selectByPrimaryKey(Integer expressReceiptBasicDataId);

    int updateByPrimaryKeySelective(BatchExpressReceiptBasicDataDto record);

    int updateByPrimaryKey(BatchExpressReceiptBasicDataDto record);

    int updateBatch(List<BatchExpressReceiptBasicDataDto> list);

    int updateBatchSelective(List<BatchExpressReceiptBasicDataDto> list);

    int batchInsert(@Param("list") List<BatchExpressReceiptBasicDataDto> list);

    List<BatchExpressReceiptBasicDataDto> findByArrivalActFlagListAndAddTimeAndIsPushKingDee(BatchExpressReceiptBasicDataDto dto);
}