package com.wms.model.po;

import java.util.Date;

public class WmsInputOrderGoods {
    /**   WMS_INPUT_ORDER_GOODS_ID **/
    private Integer wmsInputOrderGoodsId;

    /** 入库单的id  WMS_INPUT_ORDER_ID **/
    private Integer wmsInputOrderId;

    /** sku编号  SKU_NO **/
    private String skuNo;

    /** 商品id  GOODS_ID **/
    private Integer goodsId;

    /** 需入库数量  INPUT_NUM **/
    private Integer inputNum;

    /** 入库数量  ARRIVAL_NUM **/
    private Integer arrivalNum;

    /** 入库状态:0-未入库 1-部分入库 2 全部入库  ARRIVAL_STATUS **/
    private Integer arrivalStatus;

    /** 创建时间  ADD_TIME **/
    private Date addTime;

    /** 更新时间  MODE_TIME **/
    private Date modeTime;

    /** 创建人  CREATOR **/
    private Integer creator;

    /** 更新人  UPDATER **/
    private Integer updater;

    /** 是否删除:0-未删除 1-已删除  IS_DELTET **/
    private Integer isDeltet;

    /**     WMS_INPUT_ORDER_GOODS_ID   **/
    public Integer getWmsInputOrderGoodsId() {
        return wmsInputOrderGoodsId;
    }

    /**     WMS_INPUT_ORDER_GOODS_ID   **/
    public void setWmsInputOrderGoodsId(Integer wmsInputOrderGoodsId) {
        this.wmsInputOrderGoodsId = wmsInputOrderGoodsId;
    }

    /**   入库单的id  WMS_INPUT_ORDER_ID   **/
    public Integer getWmsInputOrderId() {
        return wmsInputOrderId;
    }

    /**   入库单的id  WMS_INPUT_ORDER_ID   **/
    public void setWmsInputOrderId(Integer wmsInputOrderId) {
        this.wmsInputOrderId = wmsInputOrderId;
    }

    /**   sku编号  SKU_NO   **/
    public String getSkuNo() {
        return skuNo;
    }

    /**   sku编号  SKU_NO   **/
    public void setSkuNo(String skuNo) {
        this.skuNo = skuNo == null ? null : skuNo.trim();
    }

    /**   商品id  GOODS_ID   **/
    public Integer getGoodsId() {
        return goodsId;
    }

    /**   商品id  GOODS_ID   **/
    public void setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
    }

    /**   需入库数量  INPUT_NUM   **/
    public Integer getInputNum() {
        return inputNum;
    }

    /**   需入库数量  INPUT_NUM   **/
    public void setInputNum(Integer inputNum) {
        this.inputNum = inputNum;
    }

    /**   入库数量  ARRIVAL_NUM   **/
    public Integer getArrivalNum() {
        return arrivalNum;
    }

    /**   入库数量  ARRIVAL_NUM   **/
    public void setArrivalNum(Integer arrivalNum) {
        this.arrivalNum = arrivalNum;
    }

    /**   入库状态:0-未入库 1-部分入库 2 全部入库  ARRIVAL_STATUS   **/
    public Integer getArrivalStatus() {
        return arrivalStatus;
    }

    /**   入库状态:0-未入库 1-部分入库 2 全部入库  ARRIVAL_STATUS   **/
    public void setArrivalStatus(Integer arrivalStatus) {
        this.arrivalStatus = arrivalStatus;
    }

    /**   创建时间  ADD_TIME   **/
    public Date getAddTime() {
        return addTime;
    }

    /**   创建时间  ADD_TIME   **/
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**   更新时间  MODE_TIME   **/
    public Date getModeTime() {
        return modeTime;
    }

    /**   更新时间  MODE_TIME   **/
    public void setModeTime(Date modeTime) {
        this.modeTime = modeTime;
    }

    /**   创建人  CREATOR   **/
    public Integer getCreator() {
        return creator;
    }

    /**   创建人  CREATOR   **/
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**   更新人  UPDATER   **/
    public Integer getUpdater() {
        return updater;
    }

    /**   更新人  UPDATER   **/
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    /**   是否删除:0-未删除 1-已删除  IS_DELTET   **/
    public Integer getIsDeltet() {
        return isDeltet;
    }

    /**   是否删除:0-未删除 1-已删除  IS_DELTET   **/
    public void setIsDeltet(Integer isDeltet) {
        this.isDeltet = isDeltet;
    }
}