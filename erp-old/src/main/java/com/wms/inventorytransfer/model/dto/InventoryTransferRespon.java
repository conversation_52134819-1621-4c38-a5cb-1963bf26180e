package com.wms.inventorytransfer.model.dto;

import com.vedeng.stock.api.stock.dto.PromotionActionDto;

import java.io.Serializable;
import java.util.List;

/**
 * 库存转移单响应对象
 *
 * <AUTHOR>
 * @date 2020/7/20 11:09:20
 */
public class InventoryTransferRespon implements Serializable {
    /**
     * 操作类型 1 编辑  2 删除
     */
    private Integer typeFlag;

    /**
     * 活动ID
     */
    private Integer actionId;

    /**
     * 库存转移单数据传输
     */
    private List<InventoryTransferDto> inventoryTransferDtos;

    /**
     * 限购活动运输类
     */
    private PromotionActionDto promotionActionDto;

    public Integer getTypeFlag() {
        return typeFlag;
    }

    public void setTypeFlag(Integer typeFlag) {
        this.typeFlag = typeFlag;
    }

    public List<InventoryTransferDto> getInventoryTransferDtos() {
        return inventoryTransferDtos;
    }

    public void setInventoryTransferDtos(List<InventoryTransferDto> inventoryTransferDtos) {
        this.inventoryTransferDtos = inventoryTransferDtos;
    }

    public Integer getActionId() {
        return actionId;
    }

    public void setActionId(Integer actionId) {
        this.actionId = actionId;
    }

    public PromotionActionDto getPromotionActionDto() {
        return promotionActionDto;
    }

    public void setPromotionActionDto(PromotionActionDto promotionActionDto) {
        this.promotionActionDto = promotionActionDto;
    }

    @Override
    public String toString() {
        return "InventoryTransferRespon{" +
                "typeFlag=" + typeFlag +
                ", actionId=" + actionId +
                ", inventoryTransferDtos=" + inventoryTransferDtos +
                ", promotionActionDto=" + promotionActionDto +
                '}';
    }
}
