package com.vedeng.crm.visitrecord.service.impl;

import com.vedeng.crm.business.quote.domain.dto.SendMessageDto;
import com.vedeng.crm.common.service.JumpService;
import com.vedeng.crm.task.service.TaskService;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.UserApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2025/3/10
 */
@Service(value = "BaseMessageSendServiceImpl")
public class BaseMessageSendServiceImpl {

    @Autowired
    protected TaskService taskService;

    @Value("${lxcrmUrl}")
    protected String lxcrmUrl;


    @Value("${crmApplicationMessageJumpUrl}")
    protected String crmApplicationMessageJumpUrl;  //qa.lxcrm.vedeng.com  一定是公网地址，需要在企业微信中打开的

    @Autowired
    protected JumpService jumpService;

    @Autowired
    protected UserApiService userApiService;

    public void sendCardMsg(SendMessageDto message, String title) {
        taskService.sendCardMsg(message, title);
    }

    protected List<UserDto> getUserInfoByUserIds(List<Integer> userIdList){
        return userApiService.getUserInfoByUserIds(userIdList);
    }

}
