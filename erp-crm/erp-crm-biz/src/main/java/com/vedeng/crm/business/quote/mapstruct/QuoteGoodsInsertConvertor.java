package com.vedeng.crm.business.quote.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.crm.business.quote.domain.dto.QuoteGoodsInsertRequestDto;
import com.vedeng.crm.business.quote.domain.dto.QuoteGoodsRequestDto;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface QuoteGoodsInsertConvertor extends BaseMapStruct<QuoteGoodsRequestDto, QuoteGoodsInsertRequestDto> {

}
