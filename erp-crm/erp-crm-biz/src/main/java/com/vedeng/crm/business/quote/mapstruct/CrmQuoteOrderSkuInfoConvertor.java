package com.vedeng.crm.business.quote.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.crm.business.quote.domain.dto.CrmQuoteOrderCoreSkuDto;
import com.vedeng.crm.business.quote.domain.entity.CrmQuoteorderGoods;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface CrmQuoteOrderSkuInfoConvertor extends BaseMapStruct<CrmQuoteorderGoods, CrmQuoteOrderCoreSkuDto> {

    /**
     * 部分
     * @param entity
     * @return
     */
    @Mapping(target = "skuNo", source = "sku")
    @Mapping(target = "skuName", source = "goodsName")
    @Mapping(target = "skuId", source = "goodsId")
    @Mapping(target = "salePrice", source = "price")
    @Mapping(target = "expectDeliveryTime", source = "deliveryCycle")
    CrmQuoteOrderCoreSkuDto toDto(CrmQuoteorderGoods entity);

}
