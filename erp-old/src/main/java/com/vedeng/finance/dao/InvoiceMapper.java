package com.vedeng.finance.dao;

import com.vedeng.aftersales.component.dto.AfterSaleOrderAddDto;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.vo.AfterSalesInvoiceVo;
import com.vedeng.erp.aftersale.dto.AfterBuyorderInvoiceGoodsDto;
import com.vedeng.finance.dto.InvoiceDetailDto;
import com.vedeng.finance.dto.InvoiceRelationWarehouseLogDto;
import com.vedeng.finance.dto.InvoiceVoucherDto;
import com.vedeng.finance.model.Invoice;
import com.vedeng.finance.model.InvoiceDetail;
import com.vedeng.finance.model.InvoiceTimeDto;
import com.vedeng.order.model.SaleorderGoods;
import com.wms.dto.WmsInvoiceInfoDto;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Named("invoiceMapper")
public interface InvoiceMapper {

    int insertSelective(Invoice record);

    int updateInvoiceByKey(Invoice record);

    List<Invoice> getInvoiceListByInvoiceIdList(@Param("invoiceIdList") List<Integer> invoiceIdList);

    /**
     * 获取当前该订单审核中的申请开票id
     *
     * @Author:strange
     * @Date:10:33 2019-11-23
     */
    List<Integer> getInvoiceApllyNum(Integer saleorderId);


    BigDecimal getSaleOpenInvoiceAmount(@Param(value = "relatedId") Integer relatedId);

    /**
     * <b>Description:</b><br> 修改销售订单发票状态
     *
     * @param invoice
     * @return
     * @Note <b>Author:</b> duke
     * <br><b>Date:</b> 2017年10月9日 下午4:32:19
     */
    int updateSaleInvoiceStatus(Invoice invoice);

    /**
     * 获取寄送发票快递信息
     *
     * @return
     * <AUTHOR>
     * @Date 3:33 下午 2020/5/8
     * @Param
     **/
    List<Invoice> getInvoiceExpress(@Param("invoiceIdList") List<Integer> invoiceIdList);

    /**
     * 获取满足票货同行并且没有推送的发票信息
     *
     * @param invoiceGoodsOrderTypes
     * @return
     */
    List<WmsInvoiceInfoDto> getWmsInvoiceInfoDtos(@Param("invoiceGoodsOrderTypes") List<Integer> invoiceGoodsOrderTypes);

    /**
     * 根据发票ID获取发票基本信息
     *
     * @param invoiceIds
     * @return
     */
    List<Invoice> getInvoiceBaseInfoByInvoiceIds(@Param("invoiceIds") List<Integer> invoiceIds);

    /**
     * 发票关联航信获取航信发票信息(航信认证使用)
     *
     * @param invoiceIds
     * @return
     */
    List<Invoice> getInvoiceBaseInfoFromHxByInvoiceIds(@Param("invoiceIds") List<Integer> invoiceIds);

    /**
     * 条件获取航信发票基本信息（发票认证使用）
     * @param invoice
     * @return
     */
    Invoice getHxInvoiceBaseInfoByCondition(Invoice invoice);

    /**
     * 保存发票接口认证结果信息
     *
     * @param invoice
     * @return
     */
    int saveInvoiceAuthInfo(Invoice invoice);

    /**
     * 保存航信接口认证结果信息
     * @param invoice
     * @return
     */
    int saveHxInvoiceAuthInfo(Invoice invoice);

    /**
     * 标记发票是否正在认证的状态
     *
     * @param hxInvoiceIds 发票IDS
     * @param isAuthStatus 认证状态
     * @return
     */
    int signInvoiceAuthStatus(@Param("hxInvoiceIds") List<Integer> hxInvoiceIds, @Param("isAuthStatus") Integer isAuthStatus);

    /**
     * 标记航信发票是否正在认证的状态
     *
     * @param hxInvoiceIds
     * @param isAuthStatus
     * @return
     */
    int signHxInvoiceAuthStatus(@Param("hxInvoiceIds") List<Integer> hxInvoiceIds, @Param("isAuthStatus") Integer isAuthStatus);

    /**
     * 获取发票的基本信息
     *
     * @param invoiceId
     * @return
     */
    Invoice getInvoiceBaseInfoByInvoiceId(Integer invoiceId);

    /**
     * 保存发票地址信息
     *
     * @param invoice
     * @return
     */
    int saveInvoiceHref(Invoice invoice);

    /**
     * 根据外键查询发票记录（对应产品开票记录）
     *
     * @param record
     * @return
     */
    List<InvoiceDetail> getInvoiceListByRelatedId(Invoice record);

    /**
     * 根据外键查询发票记录（对应产品开票记录）
     *
     * @param relateId
     * @return
     */
    List<Invoice> listByRelatedId(@Param("relateId") Integer relateId, @Param("type") Integer type);


    /**
     * 根据外键查询发票记录（对应产品开票记录）
     *
     * @param invoiceId
     * @return
     */
    List<InvoiceDetail> getInvoiceListByInvoiceId(@Param("invoiceId") Integer invoiceId);

    /**
     * 获取需要下载的纸质销项发票
     * @param start
     * @param end
     * @return
     */
    List<Invoice> getSaleInvoiceOfNeedDownload(@Param("start") Long start, @Param("end") Long end);

    void updateInvoiceOssUrl(@Param("invoiceId") Integer invoiceId, @Param("ossUrl") String ossUrl);

    List<Invoice> getInvoiceListByNumAndColorType(@Param("invoiceNum") String invoiceNum, @Param("invoiceCode") String invoiceCode, @Param("colorType") Integer colorType, @Param("isEnable") Integer isEnable);

    void updateHxInvoiceIdByPrimaryKey(@Param("invoiceId") Integer invoiceId, @Param("hxInvoiceId") Integer hxInvoiceId);


    /**
     * 修改关联表类型和关联表id为采购售后费用单
     * @param invoiceId
     * @param relatedId
     */
    void updateRelatedIdByPrimaryKey(@Param("invoiceId") Integer invoiceId, @Param("relatedId") Integer relatedId);


    Invoice selectByPrimaryKey(Integer invoiceId);

    Invoice selectAmountByPrimaryKey(Integer invoiceId);

    /**
     * 查询已开票产品数量
     *
     * @param invoice
     * @return
     */
    List<InvoiceDetail> getInvoiceGoodsNum(Invoice invoice);

    /**
     * 根据售后单号查询售后详情
     *
     * @param afterId
     * @return
     */
    AfterSales getAfterSalesByAfterInvoiceId(Integer afterId);
    /**
     * 获取销售单商品已开票金额
     *
     * @param orderDetailId
     * @return
     */
    BigDecimal getSaleorderHaveInvoiceTotalAmount(Integer orderDetailId);

    /**
     * 处理ERP发票中的航信发票信息
     *
     * @param invoiceIds
     * @param hxInvoiceId
     * @return
     */
    Integer setHxInvoiceInfoByPrimaryKeys(@Param("invoiceIds")  List<Integer> invoiceIds, @Param("hxInvoiceId") Integer hxInvoiceId);

    /**
     *  查询已作废
     * @param invoiceIds
     * @return
     */
    List<Invoice> getInValidInvoiceListByIds(@Param("ids") List<Integer> invoiceIds);

    /**
     *
     * @param afterSalesId
     * @return
     */
    List<AfterSalesInvoiceVo> getAfterSalesInvoiceForInValid(Integer afterSalesId);


    List<AfterSalesInvoiceVo> getBuyorderExpenseSalesByRelatedId(Integer relatedId);

    List<Invoice> selectInvoiceListByParam(@Param("invoice") Invoice invoice,@Param("saleOrderGoodsList") List<SaleorderGoods> saleOrderGoodsList,@Param("typeList") List<Integer> typeList);

    List<Integer> selectIngAfterSaleOrderByInvoiceIdList(@Param("invalidInvoiceIdList") List<Integer> invalidInvoiceIdList,@Param("afterSaleOrderAddDto") AfterSaleOrderAddDto afterSaleOrderAddDto);

    /**
     * @description: 获取销售订单发票
     * @return: Invoice
     * @author: Strange
     * @date: 2021/8/6
     **/
    List<Invoice> getInvoiceListBySaleorderId(Integer saleorderId);

    /**
     * 检索一定时间内审核通过的采购发票信息
     * （VDERP-7303补偿使用）
     * @param beginTime
     * @param endTime
     * @return
     */
    List<Invoice> getInvoiceListByValidTime(@Param("beginTime") Long beginTime, @Param("endTime") Long endTime);

    /**
     * <b>Description:</b><br> 根据条件查询发票列表
     * VDERP-8806  剔除发票号码为空的发票记录
     * @param invoice
     * @return
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2017年10月13日 下午3:01:09
     */
    List<Invoice> getInvoiceListByParam(Invoice invoice);


    List<Invoice> getInvoiceListByEntity(Invoice invoice);
    /**
     * <b>Description:</b><br> 验证销售单未完成的售后单
     * @param saleorderId
     * @return
     * @Note
     */
    Integer getSaleOrderAfterStatus(@Param("companyId")Integer companyId,@Param("saleorderId")Integer saleorderId, @Param("atferStatus")Integer atferStatus);

    /**
     * <b>Description:</b><br> 查询是否有开票申请记录
     * @param saleorderId
     * @return
     */
    Map<String, Integer> getOrderOpenInvoiceApply(@Param(value="saleorderId")Integer saleorderId);

    /**
     * <b>Description:</b><br> 根据订单号查询已开票金额
     * @param saleorderId
     * @return
     */
    Invoice getSaleInvoiceAmount(@Param("saleorderId")Integer saleorderId);

    int insert(Invoice invoice);

    /**
     * <b>Description:</b><br> 查询销售订单售后退货总金额
     * @param saleorderId
     * @return
     * @Note
     * <b>Author:</b> Administrator
     * <br><b>Date:</b> 2018年2月1日 下午7:23:17
     */
    BigDecimal getSaleOrderAfterRecord(@Param("saleorderId") Integer saleorderId);

    /**
     * <b>Description:</b><br> 查询采购订单售后退货总金额
     * @param buyorderId
     * @return
     * @Note
     * <b>Author:</b> Administrator
     * <br><b>Date:</b> 2018年2月1日 下午7:23:17
     */
    BigDecimal getBuyOrderAfterRecord(@Param("buyorderId") Integer buyorderId);

    BigDecimal getBuyRecordInvoiceAmount(@Param(value="relatedId") Integer buyorderId);

    /**
     * 获取寄送发票快递信息
     * @param invoiceIdList
     * @return
     */
    List<Invoice> getInvoiceExpressInfo(@Param("invoiceIdList") List<Integer> invoiceIdList);

    /**
     * 条件检索采购蓝字作废发票信息
     *
     * @param invoiceNo
     * @param invoiceCode
     * @param relatedId
     * @param invoiceFrom
     * @return
     */
    List<Invoice> getBlueInvalidInvoiceByCondition(@Param("invoiceNo") String invoiceNo,
                                                   @Param("invoiceCode") String invoiceCode,
                                                   @Param("relatedId") Integer relatedId,
                                                   @Param("invoiceFrom") Integer invoiceFrom);

    /**
     * 发票属性获取发票实际金额
     * @param invoiceNo
     * @param invoiceCode
     * @return
     */
    BigDecimal getTotalAmountByInvoiceNoAndCode(@Param("invoiceNo") String invoiceNo, @Param("invoiceCode") String invoiceCode);

    /**
     * 发票属性获取有效发票详情
     * @param invoiceNo
     * @param invoiceCode
     * @return
     */
    List<InvoiceDetailDto> getValidInvoiceDetailByInvoiceNoAndCode(@Param("invoiceNo") String invoiceNo, @Param("invoiceCode") String invoiceCode);

    /**
     * 航信发票有效发票详情
     * @param hxInvoiceId
     * @return
     */
    List<InvoiceDetailDto> getValidInvoiceDetailByHxInvoiceId(Integer hxInvoiceId);

    /**
     * 发票相关审核中的冲销审核
     * @param invoiceId
     * @return
     */
    List<Integer> getInReviewInvoiceReversalByInvoiceId(Integer invoiceId);

    /**
     * 发票相关审核中的冲销审核
     * @param invoiceNo
     * @param invoiceCode
     * @return
     */
    List<Integer> getInReviewInvoiceReversalByInvoiceNoAndCode(@Param("invoiceNo")String invoiceNo, @Param("invoiceCode") String invoiceCode);

    /**
     * 号码和代码获取已录票信息
     * @param invoiceNo
     * @param invoiceCode
     * @return
     */
    List<Invoice> listInvoiceByInvoiceNoAndCode(@Param("invoiceNo")String invoiceNo, @Param("invoiceCode") String invoiceCode);

    /**
     * 根据采购单id查询采购单正向有效蓝票信息（sku+发票纬度）
     * <AUTHOR>
     * @param buyorderId
     * @return
     */
    List<AfterBuyorderInvoiceGoodsDto> queryPlusInvoiceBuyorderGoods(Integer buyorderId);

    /**
     * 根据采购单id查询采购单负向发票信息（sku+发票纬度）
     * <AUTHOR>
     * @param buyorderId
     * @return
     */
    List<AfterBuyorderInvoiceGoodsDto> queryMinusInvoiceBuyorderGoods(Integer buyorderId);

    /**
     * 本次审核发票列表
     * @param invoiceId
     * @return
     */
    List<Invoice> getValidInvoiceListThisAudit(Integer invoiceId);

    /**
     * 条件获取所有相关发票信息（包含所有审核状态）
     * @param invoiceNo
     * @param invoiceCode
     * @return
     */
    List<Invoice> getValidInvoiceByCondition(@Param("invoiceNo") String invoiceNo, @Param("invoiceCode") String invoiceCode);

    /**
     * 所有录票相关联的发票信息
     * 非统计使用
     * @param invoiceNo
     * @param invoiceCode
     * @return
     */
    List<Invoice> getAllRelationInvoiceByInvoiceNoAndCode(@Param("invoiceNo")String invoiceNo, @Param("invoiceCode") String invoiceCode);

    /**
     * 采购订单号管理入库日志信息
     * @param invoiceNoList
     * @return
     */
    List<InvoiceRelationWarehouseLogDto> getRelationOperateLog(@Param("invoiceNoList") List<String> invoiceNoList, @Param("orderId") Integer orderId);

    /**
     * 自定义获取发票信息
     * @param invoice
     * @return
     */
    Invoice getInvoiceInfoByCustomCondition(@Param("invoice") Invoice invoice);

    /**
     * 获取售后付款申请中客户名称(dbcenter移植)
     * @param afterSalesId
     * @param companyId
     * @return
     */
    String getAfterPayTraderName(@Param("afterSalesId")Integer afterSalesId, @Param("companyId")Integer companyId);

    /**
     * 根据invoiceId查询发票基本信息
     * @param invoiceId
     * @return
     */
    InvoiceTimeDto getInvoiceInfoByInvoiceId(Integer invoiceId);

    /**
     * 发票ID获取凭证号信息
     * @param invoiceIdList
     * @return
     */
    List<InvoiceVoucherDto> getInvoiceVoucherInfo(@Param("invoiceIdList") List<Integer> invoiceIdList);
}
