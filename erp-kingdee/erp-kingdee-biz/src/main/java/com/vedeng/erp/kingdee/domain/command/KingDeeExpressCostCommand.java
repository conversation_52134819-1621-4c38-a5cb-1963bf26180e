package com.vedeng.erp.kingdee.domain.command;

import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class KingDeeExpressCostCommand {

    /**
     * fid
     */
    private Integer FID;
    /**
     * fBillNo
     */
    private String FBillNo;

    private KingDeeNumberCommand F_QZOK_OrgId = new KingDeeNumberCommand();
    /**
     * fQzokYsddh
     */
    private String f_QZOK_YSDDH;
    /**
     * fQzokGsywdh
     */
    private String f_QZOK_GSYWDH;
    /**
     * fQzokYwlx
     */
    private String f_QZOK_YWLX;
    /**
     * fQzokCrkdh
     */
    private String f_QZOK_CRKDH;
    /**
     * fQzokKddh
     */
    private String f_QZOK_KDDH;

    /**
     * fQzokWlbm
     */
    private KingDeeNumberCommand f_QZOK_WLBM = new KingDeeNumberCommand();
    /**
     * fQzokXlh
     */
    private String f_QZOK_XLH;
    /**
     * fQzokPch
     */
    private String f_QZOK_PCH;
    /**
     * fQzokFhsl
     */
    private String f_QZOK_FHSL;
    /**
     * fQzokFhsl
     */
    private String f_QZOK_CB;
    /**
     * fQzokSjr
     */
    private String f_QZOK_SJR;
    /**
     * fQzokDh
     */
    private String f_QZOK_DH;
    /**
     * fQzokDz
     */
    private String f_QZOK_DZ;
    /**
     * fQzokBddjbh
     */
    private String f_QZOK_BDDJBH;

    /**
     * fQzokSfsc
     */
    private String f_QZOK_SFSC;
    /**
     * fQzokSfjrcb
     */
    private String f_QZOK_SFJRCB;

    /**
     * fQzokWlgs
     */
    private String f_QZOK_WLGS;

    /**
     * fQzokSfzp
     */
    private Integer f_QZOK_SFZP;

}
