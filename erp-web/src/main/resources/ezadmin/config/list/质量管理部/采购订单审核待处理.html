<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>采购订单审核待处理</title> 
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="_9hXlsDHwao" datasource="erp-reportdatasource" fixednumber="2" append_column_url="" append_row_url="" empty_show="" firstcol> 
  <div id="appendHead"></div> 
  <div class="layui-fluid"> 
   <div class="layui-tab"> 
    <ul class="layui-tab-title" id="tab"> 
    </ul> 
   </div> 
   <form class="layui-form" id="search">
    <div class=" layui-inline "><label class="layui-form-label">送达时间</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="daterange" name="ADD_TIME" placeholder="" style="" alias="FROM_UNIXTIME( L.ADD_TIME / 1000 )" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">供应商名称</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="TRADER_NAME" placeholder="" style="" alias="T" jdbctype="" data="" datatype="" oper="LIKE"></object> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">订单号</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="BUYORDER_NO" placeholder="" style="" alias="B" jdbctype="" data="" datatype="" oper="LIKE"></object> 
     </div>
    </div> 
   </form> 
   <hr class="layui-border-blue"> 
   <div class="btn-group   bd-highlight" id="tableButton"> 
   </div> 
   <table id="table" class="layui-table" style=" width:100%"> 
    <thead> 
     <tr id="column"> 
      <th type="rowbutton" id="rowbutton"></th> 
      <th item_name="BUYORDER_NO" name="BUYORDER_NO" body="td-link-color" order="0" url="/order/buyorder/viewBuyorder.do?buyorderId=${BUYORDER_ID}" opentype="PARENT" windowname="${BUYORDER_NO}" datatype="" data="" style="" head="18">订单号</th>
      <th item_name="TRADER_NAME" name="TRADER_NAME" body="td-link-color" order="0" url="/trader/supplier/baseinfo.do?traderId=${TRADER_ID}" opentype="PARENT" windowname="" datatype="" data="" style="" head="18">供应商名称</th>
      <th item_name="USERNAME" name="USERNAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">创建者</th>
      <th item_name="ADD_TIME" name="ADD_TIME" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">送达时间</th>
      <th item_name="OPERATION" name="OPERATION" body="td-link-color" order="0" url="/order/buyorder/viewBuyorder.do?buyorderId=${BUYORDER_ID}" opentype="PARENT" windowname="" datatype="" data="" style="" head="18">操作</th>
     </tr> 
    </thead> 
    <tbody> 
     <tr> 
      <td colspan="100"> express: <pre id="express" class="layui-code" orderby="ORDER BY 	L.ADD_TIME DESC" groupby="">SELECT
	B.BUYORDER_ID,
	B.BUYORDER_NO,
	T.TRADER_ID,
	T.TRADER_NAME,
	U.USERNAME,
	FROM_UNIXTIME( L.ADD_TIME / 1000 ) ADD_TIME,
	'去审核' OPERATION
FROM
	T_TODO_LIST L
	JOIN T_BUYORDER B ON L.BUZ_ID = B.BUYORDER_ID
	AND L.BUZ_TYPE = 3
	AND L.`STATUS` = 0 AND B.COMPANY_ID=1
	JOIN T_TRADER T ON B.TRADER_ID = T.TRADER_ID
	JOIN T_USER U ON B.CREATOR = U.USER_ID
where 1=1</pre> count: <pre id="count" class="layui-code"></pre></td> 
     </tr> 
    </tbody> 
   </table> 
  </div> 
  <div id="appendFoot"></div> 
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script> 
  <script>
    layui.use(function () {

    })
</script> 
 </body>
</html>