.scrollbar() {
    &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }

    &::-webkit-scrollbar-track {
        background: transparent;
        width: 6px;
        height: 6px;
    }

    &::-webkit-scrollbar-thumb {
        background: #D7DADE;
        width: 6px;
        height: 6px;
        border-radius: 3px;

        &:hover {
            background: #BABFC2;
        }

        &:active {
            background: #969B9E;
        }
    }
}

.detail-prod-tabs {
    padding: 15px 0;
    margin-left: -20px;
    padding-left: 20px;

    &.fixed {
        position: fixed;
        top: 73px;
        z-index: 100;
        background: #fff;
        width: 1200px;
    }

    .tabs-list {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: -10px;
    }

    .tab-item {
        padding: 5px 16px;
        border-radius: 3px;
        border: solid 1px #BABFC2;
        background: #F5F7FA;
        margin-right: 10px;
        margin-bottom: 10px;
        cursor: pointer;
        display: flex;
        align-items: center;

        &:hover {
            background: #EBEFF2;
        }

        &.active {
            background: #E0F3FF;
            border-color: #09f;
            color: #09f;

            &:hover {
                .tab-option:hover {
                    background: #b3e1ff;
                    color: #09f;
                }
            }
        }

        .tab-option {
            width: 33px;
            height: 31px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: -5px -16px -5px 0;
            position: relative;

            .icon-app-more {
                transform: rotate(90deg);
            }

            &:hover {
                color: #f60;
                background: #e1e5e8;

                .tab-option-drop {
                    display: block;
                }
            }

            .tab-option-drop {
                background: #fff !important;
                position: absolute;
                top: 31px;
                left: 0;
                padding: 5px 0;
                border: solid 1px #C9CED1;
                border-radius: 3px;
                box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
                cursor: default;
                z-index: 10;
                color: #333;
                display: none;

                .tab-option-drop-item {
                    padding: 6px 10px;
                    cursor: pointer;
                    white-space: nowrap;

                    &:hover {
                        background: #f5f7fa;
                    }
                }
            }
        }

        &.sortable {
            cursor: move;

            .tab-option {
                cursor: pointer;
            }
        }

        &.placehodler {
            opacity: .1;
        }
    }
}

.detail-prod-list-wrap {
    margin: 0 -20px -40px -20px;

    .detail-prod-options-placeholder {
        height: 64px;
    }

    .detail-prod-options {
        display: flex;
        align-items: center;
        padding: 15px 20px;
        border-top: solid 1px #E1E5E8;

        .prod-select-num {
            color: #999;
            margin-left: 10px;
        }

        &.fixed {
            position: fixed;
            top: 72px;
            z-index: 100;
            background: #fff;
            width: 1200px;
        }
    }

    .vd-ui-table-wrap {
        .vd-ui-table-header {
            top: 1px;
        }

        .vd-ui-table {
            .vd-ui-tr {
                .vd-ui-th {
                    border: 1px solid#E1E5E8;
                    padding: 8px 10px;
                    font-size: 12px;
                }

                .vd-ui-td {
                    border: 1px solid#E1E5E8;
                    padding: 10px 3px 10px 10px;
                    font-size: 12px;

                    .option-item {
                        margin-right: 0;
                        margin-bottom: 5px;

                        &:last-child {
                            margin-bottom: 0;
                        }

                        &::before {
                            display: none;
                        }
                    }
                }
            }
        }
    }

    .td-cnt-wrap {
        height: 108px;
        overflow: auto;
        overscroll-behavior: contain;
        .scrollbar;
    }

    .prod-info-wrap {
        display: flex;

        .prod-img {
            width: 60px;
            height: 60px;
            margin-right: 10px;

            img {
                width: 100%;
                height: 100%;
                object-fit: contain;
            }
        }

        .prod-info {
            flex: 1;

            .prod-sku {
                color: #09f;
                cursor: pointer;
                margin-bottom: 5px;

                &:hover {
                    color: #f60;
                }
            }

            .prod-name {
                margin-bottom: 5px;
            }

        }
    }

    .prod-detail {
        margin-bottom: 5px;
        display: flex;

        .detail-label {
            color: #999;
        }

        .detail-txt {
            flex: 1;
        }

        &:last-child {
            margin-bottom: 0;
        }

        .user-wrap {
            display: flex;
            align-items: center;

            .user-avatar {
                width: 18px;
                height: 18px;
                border-radius: 3px;
                overflow: hidden;
                margin-right: 5px;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }
        }
    }

    .prod-empty-wrap {
        text-align: center;
        padding: 100px 0;

        .empty-img {
            width: 110px;
            height: 95px;
            background: url(../../images/search-empty.svg);
            background-size: 100% 100%;
            display: inline-block;
            vertical-align: top;
            margin-bottom: 20px;
        }
    }
}

.multi-add-wrap {
    display: flex;

    .multi-add-l {
        width: 180px;
        margin-right: 20px;
    }

    .multi-add-r {
        flex: 1;

        .multi-add-tips {
            color: #999;
            margin-bottom: 15px;
            line-height: 24px;
        }

        .multi-error-wrap {
            display: flex;
            color: #e64545;
            word-break: break-all;

            .icon-error2 {
                font-size: 16px;
                line-height: 1;
                margin-right: 5px;
                margin-top: 3px;
            }
        }
    }
}