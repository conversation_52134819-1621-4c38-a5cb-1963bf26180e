<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.CustomerBankAccountMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.CustomerBankAccountEntity">
    <!--@mbg.generated-->
    <!--@Table T_CUSTOMER_BANK_ACCOUNT-->
    <id column="CUSTOMER_BANK_ACCOUNT_ID" jdbcType="BIGINT" property="customerBankAccountId" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="ACCOUNT_TYPE" jdbcType="INTEGER" property="accountType" />
    <result column="ACCOUNT_NO" jdbcType="VARCHAR" property="accountNo" />
    <result column="ACCOUNT_NAME" jdbcType="VARCHAR" property="accountName" />
    <result column="BANK_ID" jdbcType="INTEGER" property="bankId" />
    <result column="IS_VERIFY" jdbcType="INTEGER" property="isVerify" />
    <result column="IS_ENABLE" jdbcType="INTEGER" property="isEnable" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="LAST_USE_TIME" jdbcType="TIMESTAMP" property="lastUseTime" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    CUSTOMER_BANK_ACCOUNT_ID, TRADER_ID, ACCOUNT_TYPE, ACCOUNT_NO, ACCOUNT_NAME, BANK_ID,
    IS_VERIFY, IS_ENABLE, IS_DELETE, LAST_USE_TIME, ADD_TIME, MOD_TIME, CREATOR, CREATOR_NAME, UPDATER,
    UPDATER_NAME, UPDATE_REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_CUSTOMER_BANK_ACCOUNT
    where CUSTOMER_BANK_ACCOUNT_ID = #{customerBankAccountId,jdbcType=BIGINT} and IS_DELETE=0
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_CUSTOMER_BANK_ACCOUNT
    where CUSTOMER_BANK_ACCOUNT_ID = #{customerBankAccountId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="CUSTOMER_BANK_ACCOUNT_ID" keyProperty="customerBankAccountId" parameterType="com.vedeng.erp.trader.domain.entity.CustomerBankAccountEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_CUSTOMER_BANK_ACCOUNT (TRADER_ID, ACCOUNT_TYPE, ACCOUNT_NO,
      ACCOUNT_NAME, BANK_ID, IS_VERIFY,
      IS_ENABLE, IS_DELETE,LAST_USE_TIME, ADD_TIME,
      MOD_TIME, CREATOR, CREATOR_NAME,
      UPDATER, UPDATER_NAME, UPDATE_REMARK
      )
    values (#{traderId,jdbcType=INTEGER}, #{accountType,jdbcType=INTEGER}, #{accountNo,jdbcType=VARCHAR},
      #{accountName,jdbcType=VARCHAR}, #{bankId,jdbcType=INTEGER}, #{isVerify,jdbcType=INTEGER},
      #{isEnable,jdbcType=INTEGER}, #{isDelete,jdbcType=INTEGER}, #{lastUseTime,jdbcType=TIMESTAMP},#{addTime,jdbcType=TIMESTAMP},
      #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR},
      #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR}, #{updateRemark,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="CUSTOMER_BANK_ACCOUNT_ID" keyProperty="customerBankAccountId" parameterType="com.vedeng.erp.trader.domain.entity.CustomerBankAccountEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_CUSTOMER_BANK_ACCOUNT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="accountType != null">
        ACCOUNT_TYPE,
      </if>
      <if test="accountNo != null">
        ACCOUNT_NO,
      </if>
      <if test="accountName != null">
        ACCOUNT_NAME,
      </if>
      <if test="bankId != null">
        BANK_ID,
      </if>
      <if test="isVerify != null">
        IS_VERIFY,
      </if>
      <if test="isEnable != null">
        IS_ENABLE,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="lastUseTime != null">
        LAST_USE_TIME,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="accountType != null">
        #{accountType,jdbcType=INTEGER},
      </if>
      <if test="accountNo != null">
        #{accountNo,jdbcType=VARCHAR},
      </if>
      <if test="accountName != null">
        #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="bankId != null">
        #{bankId,jdbcType=INTEGER},
      </if>
      <if test="isVerify != null">
        #{isVerify,jdbcType=INTEGER},
      </if>
      <if test="isEnable != null">
        #{isEnable,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="lastUseTime != null">
        #{lastUseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.CustomerBankAccountEntity">
    <!--@mbg.generated-->
    update T_CUSTOMER_BANK_ACCOUNT
    <set>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="accountType != null">
        ACCOUNT_TYPE = #{accountType,jdbcType=INTEGER},
      </if>
      <if test="accountNo != null">
        ACCOUNT_NO = #{accountNo,jdbcType=VARCHAR},
      </if>
      <if test="accountName != null">
        ACCOUNT_NAME = #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="bankId != null">
        BANK_ID = #{bankId,jdbcType=INTEGER},
      </if>
      <if test="isVerify != null">
        IS_VERIFY = #{isVerify,jdbcType=INTEGER},
      </if>
      <if test="isEnable != null">
        IS_ENABLE = #{isEnable,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="lastUseTime != null">
        LAST_USE_TIME = #{lastUseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where CUSTOMER_BANK_ACCOUNT_ID = #{customerBankAccountId,jdbcType=BIGINT}
  </update>

  <update id="updateByPrimaryKeySelectiveWithLock" parameterType="com.vedeng.erp.trader.domain.entity.CustomerBankAccountEntity">
    <!--@mbg.generated-->
    update T_CUSTOMER_BANK_ACCOUNT
    <set>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="accountType != null">
        ACCOUNT_TYPE = #{accountType,jdbcType=INTEGER},
      </if>
      <if test="accountNo != null">
        ACCOUNT_NO = #{accountNo,jdbcType=VARCHAR},
      </if>
      <if test="accountName != null">
        ACCOUNT_NAME = #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="bankId != null">
        BANK_ID = #{bankId,jdbcType=INTEGER},
      </if>
      <if test="isVerify != null">
        IS_VERIFY = #{isVerify,jdbcType=INTEGER},
      </if>
      <if test="isEnable != null">
        IS_ENABLE = #{isEnable,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="lastUseTime != null">
        LAST_USE_TIME = #{lastUseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where CUSTOMER_BANK_ACCOUNT_ID = #{customerBankAccountId,jdbcType=BIGINT} and IS_VERIFY = 0
  </update>

  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.CustomerBankAccountEntity">
    <!--@mbg.generated-->
    update T_CUSTOMER_BANK_ACCOUNT
    set TRADER_ID = #{traderId,jdbcType=INTEGER},
      ACCOUNT_TYPE = #{accountType,jdbcType=INTEGER},
      ACCOUNT_NO = #{accountNo,jdbcType=VARCHAR},
      ACCOUNT_NAME = #{accountName,jdbcType=VARCHAR},
      BANK_ID = #{bankId,jdbcType=INTEGER},
      IS_VERIFY = #{isVerify,jdbcType=INTEGER},
      IS_ENABLE = #{isEnable,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      LAST_USE_TIME = #{lastUseTime,jdbcType=TIMESTAMP},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where CUSTOMER_BANK_ACCOUNT_ID = #{customerBankAccountId,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_CUSTOMER_BANK_ACCOUNT
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="TRADER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CUSTOMER_BANK_ACCOUNT_ID = #{item.customerBankAccountId,jdbcType=BIGINT} then #{item.traderId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ACCOUNT_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CUSTOMER_BANK_ACCOUNT_ID = #{item.customerBankAccountId,jdbcType=BIGINT} then #{item.accountType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ACCOUNT_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CUSTOMER_BANK_ACCOUNT_ID = #{item.customerBankAccountId,jdbcType=BIGINT} then #{item.accountNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ACCOUNT_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CUSTOMER_BANK_ACCOUNT_ID = #{item.customerBankAccountId,jdbcType=BIGINT} then #{item.accountName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="BANK_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CUSTOMER_BANK_ACCOUNT_ID = #{item.customerBankAccountId,jdbcType=BIGINT} then #{item.bankId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="IS_VERIFY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CUSTOMER_BANK_ACCOUNT_ID = #{item.customerBankAccountId,jdbcType=BIGINT} then #{item.isVerify,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="IS_ENABLE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CUSTOMER_BANK_ACCOUNT_ID = #{item.customerBankAccountId,jdbcType=BIGINT} then #{item.isEnable,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CUSTOMER_BANK_ACCOUNT_ID = #{item.customerBankAccountId,jdbcType=BIGINT} then #{item.isDelete,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CUSTOMER_BANK_ACCOUNT_ID = #{item.customerBankAccountId,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CUSTOMER_BANK_ACCOUNT_ID = #{item.customerBankAccountId,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CUSTOMER_BANK_ACCOUNT_ID = #{item.customerBankAccountId,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CUSTOMER_BANK_ACCOUNT_ID = #{item.customerBankAccountId,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CUSTOMER_BANK_ACCOUNT_ID = #{item.customerBankAccountId,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CUSTOMER_BANK_ACCOUNT_ID = #{item.customerBankAccountId,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATE_REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CUSTOMER_BANK_ACCOUNT_ID = #{item.customerBankAccountId,jdbcType=BIGINT} then #{item.updateRemark,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where CUSTOMER_BANK_ACCOUNT_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.customerBankAccountId,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_CUSTOMER_BANK_ACCOUNT
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="TRADER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.traderId != null">
            when CUSTOMER_BANK_ACCOUNT_ID = #{item.customerBankAccountId,jdbcType=BIGINT} then #{item.traderId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ACCOUNT_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.accountType != null">
            when CUSTOMER_BANK_ACCOUNT_ID = #{item.customerBankAccountId,jdbcType=BIGINT} then #{item.accountType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ACCOUNT_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.accountNo != null">
            when CUSTOMER_BANK_ACCOUNT_ID = #{item.customerBankAccountId,jdbcType=BIGINT} then #{item.accountNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ACCOUNT_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.accountName != null">
            when CUSTOMER_BANK_ACCOUNT_ID = #{item.customerBankAccountId,jdbcType=BIGINT} then #{item.accountName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="BANK_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.bankId != null">
            when CUSTOMER_BANK_ACCOUNT_ID = #{item.customerBankAccountId,jdbcType=BIGINT} then #{item.bankId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_VERIFY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isVerify != null">
            when CUSTOMER_BANK_ACCOUNT_ID = #{item.customerBankAccountId,jdbcType=BIGINT} then #{item.isVerify,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_ENABLE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isEnable != null">
            when CUSTOMER_BANK_ACCOUNT_ID = #{item.customerBankAccountId,jdbcType=BIGINT} then #{item.isEnable,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDelete != null">
            when CUSTOMER_BANK_ACCOUNT_ID = #{item.customerBankAccountId,jdbcType=BIGINT} then #{item.isDelete,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when CUSTOMER_BANK_ACCOUNT_ID = #{item.customerBankAccountId,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when CUSTOMER_BANK_ACCOUNT_ID = #{item.customerBankAccountId,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when CUSTOMER_BANK_ACCOUNT_ID = #{item.customerBankAccountId,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when CUSTOMER_BANK_ACCOUNT_ID = #{item.customerBankAccountId,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when CUSTOMER_BANK_ACCOUNT_ID = #{item.customerBankAccountId,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when CUSTOMER_BANK_ACCOUNT_ID = #{item.customerBankAccountId,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATE_REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateRemark != null">
            when CUSTOMER_BANK_ACCOUNT_ID = #{item.customerBankAccountId,jdbcType=BIGINT} then #{item.updateRemark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where CUSTOMER_BANK_ACCOUNT_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.customerBankAccountId,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="CUSTOMER_BANK_ACCOUNT_ID" keyProperty="customerBankAccountId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_CUSTOMER_BANK_ACCOUNT
    (TRADER_ID, ACCOUNT_TYPE, ACCOUNT_NO, ACCOUNT_NAME, BANK_ID, IS_VERIFY, IS_ENABLE,
      IS_DELETE,LAST_USE_TIME, ADD_TIME, MOD_TIME, CREATOR, CREATOR_NAME, UPDATER, UPDATER_NAME, UPDATE_REMARK
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.traderId,jdbcType=INTEGER}, #{item.accountType,jdbcType=INTEGER}, #{item.accountNo,jdbcType=VARCHAR},
        #{item.accountName,jdbcType=VARCHAR}, #{item.bankId,jdbcType=INTEGER}, #{item.isVerify,jdbcType=INTEGER},
        #{item.isEnable,jdbcType=INTEGER}, #{item.isDelete,jdbcType=INTEGER}, #{item.lastUseTime,jdbcType=TIMESTAMP},#{item.addTime,jdbcType=TIMESTAMP},
        #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR},
        #{item.updater,jdbcType=INTEGER}, #{item.updaterName,jdbcType=VARCHAR}, #{item.updateRemark,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2024-08-26-->
  <select id="findByAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_CUSTOMER_BANK_ACCOUNT
    <where>
      <if test="customerBankAccountId != null">
        and CUSTOMER_BANK_ACCOUNT_ID=#{customerBankAccountId,jdbcType=BIGINT}
      </if>
      <if test="traderId != null">
        and TRADER_ID=#{traderId,jdbcType=INTEGER}
      </if>
      <if test="accountType != null">
        and ACCOUNT_TYPE=#{accountType,jdbcType=INTEGER}
      </if>
      <if test="accountNo != null">
        and ACCOUNT_NO=#{accountNo,jdbcType=VARCHAR}
      </if>
      <if test="accountName != null">
        and ACCOUNT_NAME=#{accountName,jdbcType=VARCHAR}
      </if>
      <if test="bankId != null">
        and BANK_ID=#{bankId,jdbcType=INTEGER}
      </if>
      <if test="isVerify != null">
        and IS_VERIFY=#{isVerify,jdbcType=INTEGER}
      </if>
      <if test="isEnable != null">
        and IS_ENABLE=#{isEnable,jdbcType=INTEGER}
      </if>
      <if test="isDelete != null">
        and IS_DELETE=#{isDelete,jdbcType=INTEGER}
      </if>
      <if test="updateRemark != null">
        and UPDATE_REMARK=#{updateRemark,jdbcType=VARCHAR}
      </if>
      <if test="lastUseTime != null">
        and LAST_USE_TIME=#{lastUseTime,jdbcType=TIMESTAMP}
      </if>
      <if test="addTime != null">
        and ADD_TIME=#{addTime,jdbcType=TIMESTAMP}
      </if>
      <if test="modTime != null">
        and MOD_TIME=#{modTime,jdbcType=TIMESTAMP}
      </if>
      <if test="creator != null">
        and CREATOR=#{creator,jdbcType=INTEGER}
      </if>
      <if test="updater != null">
        and UPDATER=#{updater,jdbcType=INTEGER}
      </if>
      <if test="creatorName != null">
        and CREATOR_NAME=#{creatorName,jdbcType=VARCHAR}
      </if>
      <if test="updaterName != null">
        and UPDATER_NAME=#{updaterName,jdbcType=VARCHAR}
      </if>
    </where>
  </select>

<!--auto generated by MybatisCodeHelper on 2024-08-27-->
  <select id="findByTraderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_CUSTOMER_BANK_ACCOUNT
        where TRADER_ID=#{traderId,jdbcType=INTEGER} and IS_DELETE=0
    </select>

  <select id="findByAllToPage" resultType="com.vedeng.erp.trader.domain.dto.CustomerBankAccountDto">
    select TCBA.CUSTOMER_BANK_ACCOUNT_ID,
           TCBA.TRADER_ID,
            TRADER.TRADER_NAME,
           TCBA.ACCOUNT_TYPE,
           case TCBA.ACCOUNT_TYPE
             when 1 then '银行'
             when 2 then '微信'
             when 3 then '支付宝'
             else ''
             end as accountTypeName,
           TCBA.ACCOUNT_NAME,
           TCBA.ACCOUNT_NO,
           TCBA.BANK_ID,
           TB.BANK_NAME,
           TB.BANK_NO,
           TCBA.IS_VERIFY,
           case TCBA.IS_VERIFY
             when 0 then '未验证'
             when 1 then '已验证'
             else ''
             end as isVerifyName,
           TCBA.IS_ENABLE
    from T_CUSTOMER_BANK_ACCOUNT TCBA
    LEFT JOIN T_TRADER TRADER ON TCBA.TRADER_ID = TRADER.TRADER_ID
           left join T_BANK TB on TCBA.BANK_ID = TB.BANK_ID
    where TCBA.IS_DELETE = 0
    <if test="accountName != null and accountName != ''">
      and TCBA.ACCOUNT_NAME like concat('%', #{accountName,jdbcType=VARCHAR}, '%')
    </if>
    <if test="traderName != null and traderName != ''">
      and TRADER.TRADER_NAME like concat('%', #{traderName,jdbcType=VARCHAR}, '%')
    </if>
    <if test="accountNo != null and accountNo != ''">
      and TCBA.ACCOUNT_NO like concat('%', #{accountNo,jdbcType=VARCHAR}, '%')
    </if>
    <if test="isVerify != null and isVerify!=-1">
      and TCBA.IS_VERIFY = #{isVerify,jdbcType=INTEGER}
    </if>
    order by TCBA.TRADER_ID, TCBA.ACCOUNT_NO
  </select>

<!--auto generated by MybatisCodeHelper on 2024-08-27-->
  <update id="updateIsDeleteByCustomerBankAccountId">
    update T_CUSTOMER_BANK_ACCOUNT
    set IS_DELETE=#{updatedIsDelete,jdbcType=INTEGER},MOD_TIME=now()
    where CUSTOMER_BANK_ACCOUNT_ID=#{customerBankAccountId,jdbcType=BIGINT}
  </update>

<!--auto generated by MybatisCodeHelper on 2024-08-27-->
  <select id="findByAccountNameAndAccountNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_CUSTOMER_BANK_ACCOUNT
    where ACCOUNT_NAME=#{accountName,jdbcType=VARCHAR} and ACCOUNT_NO=#{accountNo,jdbcType=VARCHAR} and IS_DELETE=0
  </select>

<!--auto generated by MybatisCodeHelper on 2024-08-27-->
  <update id="updateIsVerifyByCustomerBankAccountIdIn">
    update T_CUSTOMER_BANK_ACCOUNT
    set IS_VERIFY=#{updatedIsVerify,jdbcType=INTEGER}
    where CUSTOMER_BANK_ACCOUNT_ID in
    <foreach item="item" index="index" collection="customerBankAccountIdCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=BIGINT}
    </foreach>
  </update>

<!--auto generated by MybatisCodeHelper on 2024-08-31-->
  <select id="findByTraderIdAndAccountType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_CUSTOMER_BANK_ACCOUNT
    where TRADER_ID=#{traderId,jdbcType=INTEGER} and ACCOUNT_TYPE=#{accountType,jdbcType=INTEGER}
    and LAST_USE_TIME >= DATE_SUB(CURDATE(), INTERVAL 2 YEAR)
    and IS_DELETE = 0
    order by LAST_USE_TIME desc
  </select>
</mapper>
