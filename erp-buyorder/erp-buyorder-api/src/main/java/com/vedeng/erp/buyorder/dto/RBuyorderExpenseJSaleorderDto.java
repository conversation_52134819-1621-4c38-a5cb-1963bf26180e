package com.vedeng.erp.buyorder.dto;

import com.vedeng.common.core.base.BaseDto;
import lombok.*;

/**
 * @description 销售单和采购费用单采购数量关系表（细粒度到商品）
 * <AUTHOR>
 * @date 2023/1/5 13:46
 **/

@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RBuyorderExpenseJSaleorderDto extends BaseDto {
    /**
    * 主键
    */
    private Integer tRBuyorderExpenseJSaleorderId;


    /**
    * 销售单表id
    */
    private Integer saleorderId;

    /**
    * 销售单明细表id
    */
    private Integer saleorderGoodsId;

    /**
    * 采购费用单id
    */
    private Integer buyorderExpenseId;

    /**
    * 采购费用单明细id
    */
    private Integer buyorderExpenseItemId;

    /**
    * skuId
    */
    private Integer skuId;

    /**
    * sku
    */
    private String skuNo;

    /**
    * 关联采购数量
    */
    private Integer num;

    /**
     * @组合对象@
     * 是否一键转单
     */
    private Integer isAuto;

    /**
     * @组合对象@ 可采购时间
     */
    private Long satisfyDeliveryTime;
}