$(function () {

    // 控制发货方式修改原因
    var lastDeliveryDirect = $("#currentDeliveryDirect").val()
    $('input[type=radio][name=deliveryDirect]').change(function () {
        if (this.value != lastDeliveryDirect) {
            $("#deliveryDirectChangeReason").show();
        } else {
            $("#deliveryDirectChangeReason").hide();
        }
    });


    $('input:radio[name="deliveryDirect"]').click(function () {

        checkLogin();
        $(".warning").remove();
        var oldDeliveryDirect = $("input[name='oldDeliveryDirect']").val();
        var deliveryDirect = $('input:radio[name="deliveryDirect"]:checked').val();
        var buyorderId = $("input[name='buyorderId']").val();
        var type = 0;
        if (oldDeliveryDirect == 0 && deliveryDirect == 1) {
            type = 1;
        } else if (oldDeliveryDirect == 1 && deliveryDirect == 0) {
            type = 2
        }
        var pass = true;
        $.ajax({
            type: "POST",
            url: "/order/newBuyorder/checkChangeDeliveryType.do",
            data: {'buyorderId': buyorderId, 'type': type},
            dataType: 'json',
            async: false,
            success: function (data) {
                if (data.code == 0) {
                    if (oldDeliveryDirect == 0 && deliveryDirect == 1) {
                        $("li.ptz").removeClass("none");
                        $("li.pf").addClass("none");

                        $("input[name='takeTraderId']").val($("#ptz_takeTraderId").val());
                        $("input[name='takeTraderName']").val($("#ptz_takeTraderName").val());
                        $("input[name='takeTraderContactId']").val($("#ptz_takeTraderContactId").val());
                        $("input[name='takeTraderAddressId']").val($("#ptz_takeTraderAddressId").val());
                        $("input[name='takeTraderArea']").val($("#ptz_takeTraderArea").val());
                        $("input[name='takeTraderAddress']").val($("#ptz_takeTraderAddress").val());
                        $("input[name='takeTraderContactName']").val($("#ptz_takeTraderContactName").val());
                        $("input[name='takeTraderContactMobile']").val($("#ptz_takeTraderContactMobile").val());
                        $("input[name='takeTraderContactTelephone']").val($("#ptz_takeTraderContactTelephone").val());

                    } else if (oldDeliveryDirect == 0 && deliveryDirect == 0) {
                        $("li.ptz").addClass("none");
                        $("li.pf").removeClass("none");
                        var str = $("#address_pf").val();
                        if (str != undefined && str != '') {
                            $("input[name='takeTraderName']").val($("#companyName").val());
                            $("input[name='takeTraderAddressId']").val(str.split('|')[0]);
                            $("input[name='takeTraderArea']").val(str.split('|')[1]);
                            $("input[name='takeTraderAddress']").val(str.split('|')[2]);
                            $("input[name='takeTraderContactName']").val(str.split('|')[3]);
                            $("input[name='takeTraderContactMobile']").val(str.split('|')[4]);
                            $("input[name='takeTraderContactTelephone']").val(str.split('|')[5]);
                        }
                    } else if (oldDeliveryDirect == 1 && deliveryDirect == 0) {
                        $("li.zf").addClass("none");
                        $("li.ztp").removeClass("none");
                        var str = $("#address_pf").val();
                        if (str != undefined && str != '') {
                            $("input[name='takeTraderName']").val($("#companyName").val());
                            $("input[name='takeTraderAddressId']").val(str.split('|')[0]);
                            $("input[name='takeTraderArea']").val(str.split('|')[1]);
                            $("input[name='takeTraderAddress']").val(str.split('|')[2]);
                            $("input[name='takeTraderContactName']").val(str.split('|')[3]);
                            $("input[name='takeTraderContactMobile']").val(str.split('|')[4]);
                            $("input[name='takeTraderContactTelephone']").val(str.split('|')[5]);
                        }
                    } else if (oldDeliveryDirect == 1 && deliveryDirect == 1) {
                        $("li.zf").removeClass("none");
                        $("li.ztp").addClass("none");
                        $("input[name='takeTraderId']").val($("input[name='oldTakeTraderId']").val());
                        $("input[name='takeTraderName']").val($("input[name='oldTakeTraderName']").val());
                        $("input[name='takeTraderContactId']").val($("input[name='oldTakeTraderContactId']").val());
                        $("input[name='takeTraderAddressId']").val($("input[name='oldTakeTraderAddressId").val());
                        $("input[name='takeTraderArea']").val($("input[name='oldTakeTraderArea']").val());
                        $("input[name='takeTraderAddress']").val($("input[name='oldTakeTraderAddress']").val());
                        $("input[name='takeTraderContactName']").val($("input[name='oldTakeTraderContactName']").val());
                        $("input[name='takeTraderContactMobile']").val($("input[name='oldTakeTraderContactMobile']").val());
                        $("input[name='takeTraderContactTelephone']").val($("input[name='oldTakeTraderContactTelephone']").val());
                    }
                } else if (oldDeliveryDirect != deliveryDirect) {
                    pass = false;
                    warnErrorTips("isUpdateDeliveryDirect", "isUpdateDeliveryDirectError", data.message);
                    $('input:radio:last').attr('checked', 'true');

                }
            },
            error: function (data) {
                if (data.status == 1001) {
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
                }
            }
        });
        return pass;
    })

    init();

})

function init() {

    let deliveryDirect = $('input:radio[name="deliveryDirect"]:checked').val();
    let oldDeliveryDirect = $("input[name='oldDeliveryDirect']").val();

    // 普发 -> 普发
    if (deliveryDirect == 0 && oldDeliveryDirect == 0) {
        let address_pf = $("#address_pf").val();
        if (address_pf != undefined && address_pf != '') {
            $("input[name='takeTraderName']").val($("#companyName").val());
            $("input[name='takeTraderAddressId']").val(address_pf.split('|')[0]);
            $("input[name='takeTraderArea']").val(address_pf.split('|')[1]);
            $("input[name='takeTraderAddress']").val(address_pf.split('|')[2]);
            $("input[name='takeTraderContactName']").val(address_pf.split('|')[3]);
            $("input[name='takeTraderContactMobile']").val(address_pf.split('|')[4]);
            $("input[name='takeTraderContactTelephone']").val(address_pf.split('|')[5]);
        }
    }

    // 直发 -> 普发
    if (deliveryDirect == 0 && oldDeliveryDirect == 1) {
        let address_ztp = $("#address_ztp").val();
        if (address_ztp != undefined && address_ztp != '') {
            $("input[name='takeTraderName']").val($("#companyName").val());
            $("input[name='takeTraderAddressId']").val(address_ztp.split('|')[0]);
            $("input[name='takeTraderArea']").val(address_ztp.split('|')[1]);
            $("input[name='takeTraderAddress']").val(address_ztp.split('|')[2]);
            $("input[name='takeTraderContactName']").val(address_ztp.split('|')[3]);
            $("input[name='takeTraderContactMobile']").val(address_ztp.split('|')[4]);
            $("input[name='takeTraderContactTelephone']").val(address_ztp.split('|')[5]);
        }
    }


    // 直发
    if (deliveryDirect == 1) {
        $("input[name='takeTraderId']").val($("input[name='oldTakeTraderId']").val());
        $("input[name='takeTraderName']").val($("input[name='oldTakeTraderName']").val());
        $("input[name='takeTraderContactId']").val($("input[name='oldTakeTraderContactId']").val());
        $("input[name='takeTraderAddressId']").val($("input[name='oldTakeTraderAddressId").val());
        $("input[name='takeTraderArea']").val($("input[name='oldTakeTraderArea']").val());
        $("input[name='takeTraderAddress']").val($("input[name='oldTakeTraderAddress']").val());
        $("input[name='takeTraderContactName']").val($("input[name='oldTakeTraderContactName']").val());
        $("input[name='takeTraderContactMobile']").val($("input[name='oldTakeTraderContactMobile']").val());
        $("input[name='takeTraderContactTelephone']").val($("input[name='oldTakeTraderContactTelephone']").val());
    }
}

function editSubmit() {

    checkLogin();
    var $form = $("#myform");
    $("form").find('.warning').remove();
    var oldDeliveryDirect = $("input[name='oldDeliveryDirect']").val();
    var deliveryDirect = $('input:radio[name="deliveryDirect"]:checked').val();

    var deliveryCycleFlag = true;
    var deliveryCycleLengthFlag = true;
    var deliveryCycleTypeFlag = true;
    var insideCommentsFlag = true;

    /*【采购预计发货日】和【采购预计收货日】校验*/
    var sendGoodsTimeFlag = true;
    var dateCompareFlag = true;
    var receiveGoodsTimeFlag = true;
    $.each($("input[name$='.sendGoodsTimeShow']"), function () {
        var sendTimeStr = $(this).val();
        if (sendTimeStr == '' || sendTimeStr == undefined) {
            $(this).addClass("errorbor");
            sendGoodsTimeFlag = false;
            return false;
        }
        var receviceDateStr = $(this).parent().next().children().val();
        if (receviceDateStr != '' && receviceDateStr < sendTimeStr) {
            $(this).parent().next().children().addClass("errorbor");
            dateCompareFlag = false;
            return false;
        }
    });

    $.each($("input[name$='.receiveGoodsTimeShow']"), function () {
        var receiveTimeStr = $(this).val();
        if (receiveTimeStr == '' || receiveTimeStr == undefined) {
            $(this).addClass("errorbor");
            receiveGoodsTimeFlag = false;
            return false;
        }
    });

    $.each($("input[name$='.deliveryCycle']"), function () {
        var deliveryCycle = $(this).val();
        if (deliveryCycle == '' || deliveryCycle == undefined) {
            $(this).addClass("errorbor");
            deliveryCycleFlag = false;
            return false;
        } else if (!(/(^[1-9]\d*$)/.test(deliveryCycle))) {
            $(this).addClass("errorbor");
            deliveryCycleTypeFlag = false;
            return false;
        } else if (deliveryCycle.length > 10) {
            $(this).addClass("errorbor");
            deliveryCycleLengthFlag = false;
            return false;
        }
    });

    $.each($("textarea[name$='.insideComments']"), function () {
        var insideComments = $(this).val();
        if (insideComments != '' && insideComments.length > 512) {
            $(this).addClass("errorbor");
            insideCommentsFlag = false;
            return false;
        }
    });

    // 校验单价
    var priceRequired = true;
    var priceFormat = true;
    var maxPrice = true;
    var maxTotalPrice = true;
    var priceReg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
    $.each($("input[name$='.price']"), function () {
        // 注意: 在单价 和 数量中间加一列的时候 这个js需要调整
        var price = $(this).val();
        var num = $(this).parent().prev().prev().text();
        if (price == '' || price == undefined) {
            $(this).addClass("errorbor");
            priceRequired = false;
            return false;
        } else if (price.length > 13 || !priceReg.test(price)) {
            $(this).addClass("errorbor");
            priceFormat = false;
            return false;
        } else if (price.length > 13 || !priceReg.test(price)) {
            $(this).addClass("errorbor");
            priceFormat = false;
            return false;
        }else if (Number(price) > 300000000) {
            $(this).addClass("errorbor");
            maxPrice = false;
            return false;
        } else if (Number(price) * Number(num) > 300000000) {
            $(this).addClass("errorbor");
            maxTotalPrice = false;
            return false;
        }
    });

    var flagIsgift1 = true;
    var flagIsgift2 = true;
    var flagIsgift3 = true;
    var isGift = $("#isGiftOrder").val();
    if(isGift == 1){
        $.each($("input[name$='.referPrice']"), function () {
            var referPrice = $(this).val();
            if (referPrice === '' && isGift == 1) {
                $(this).addClass("errorbor");
                flagIsgift1 = false;
                return false;
            }

            if (referPrice == 0 && isGift == 1) {
                $(this).addClass("errorbor");
                flagIsgift2 = false;
                return false;
            }

            if (!priceReg.test(referPrice)) {
                $(this).addClass("errorbor");
                flagIsgift3 = false;
                return false;
            }
            var a = /(\d{11})\d*/;
            var b =/(\.\d{3})\d*/;
            if (!(referPrice != '' && (priceReg.test(referPrice) && !a.test(referPrice) && !b.test(referPrice)))){
                flagIsgift3 = false;
                return false;
            }

        });
    }

    if(!flagIsgift1){
        layer.alert("请填写赠品参考价");
        return false;
    }
    if(!flagIsgift2){
        layer.alert("赠品参考价不可为0");
        return false;
    }
    if(!flagIsgift3){
        layer.alert("赠品参考价输入错误！仅允许使用数字，最多精确到小数点后两位");
        return false;
    }

    if (!sendGoodsTimeFlag) {
        layer.alert("请填写采购预计发货日");
        return false;
    }

    if (!receiveGoodsTimeFlag) {
        layer.alert("请填写采购预计到货日");
        return false;
    }

    if (!dateCompareFlag) {
        layer.alert("收货时间应该大于或等于发货时间");
        return false;
    }

    if (!insideCommentsFlag) {
        layer.alert("采购备注不超过512个字符");
        return false;
    }

    if (!deliveryCycleFlag) {
        layer.alert("请输入货期");
        return false;
    }

    if (!deliveryCycleTypeFlag) {
        layer.alert("货期仅支持输入数字");
        return false;
    }

    if (!deliveryCycleLengthFlag) {
        layer.alert("货期不允许超过10个字符");
        return false;
    }

    if (!priceRequired) {
        layer.alert("单价不允许为空");
        return false;
    }

    if (!priceFormat) {
        layer.alert("单价输入错误！仅允许使用数字，最多精确到小数点后两位");
        return false;
    }

    if (!maxPrice) {
        layer.alert("单价不允许超过三亿");
        return false;
    }

    if (!maxTotalPrice) {
        layer.alert("单个商品总价不允许超过三亿");
        return false;
    }


    var logisticsComments = $("#logisticsComments").val();
    if (logisticsComments != '' && logisticsComments.length > 256) {
        warnTips("logisticsComments", "物流备注不允许超过256个字符");
        return false;
    }

    var invoiceComments = $("#invoiceComments").val();
    if (invoiceComments != '' && invoiceComments.length > 256) {
        warnTips("invoiceComments", "开票备注不允许超过256个字符");
        return false;
    }

    // 只有当发货方式改变了 才校验改变原因必填
    var lastDeliveryDirect = $("#currentDeliveryDirect").val()
    var nowDeliveryDirect = $('input:radio[name="deliveryDirect"]:checked').val()
    if (lastDeliveryDirect != nowDeliveryDirect) {
        var deliveryDirectChangeReason = $("#deliveryDirectChangeReason").val();
        if (deliveryDirectChangeReason == '' || deliveryDirectChangeReason == undefined) {
            warnTips("deliveryDirectChangeReasonError", "请填写修改发货方式的原因");
            return false;
        } else if (deliveryDirectChangeReason.length > 256) {
            warnTips("deliveryDirectChangeReasonError", "修改发货方式的原因不允许超过256个字符");
            return false;
        }
    }

    $form.submit();

}
