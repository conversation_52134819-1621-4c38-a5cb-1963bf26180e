package com.vedeng.infrastructure.bank.api.domain;

import com.vedeng.infrastructure.bank.api.domain.base.BankResponse;
import lombok.*;

import javax.xml.bind.annotation.*;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 融资申请结果查询响应
 * @date 2024/10/14 14:48
 */
@Getter
@Setter
@XmlRootElement(name = "CMBC")
@XmlAccessorType(XmlAccessType.FIELD)
public class B2eQueryBasicDraftResultRes extends BankResponse {

    /**
     * 响应体
     */
    @XmlElement(name = "xDataBody")
    private B2eQueryBasicDraftResultResBody xDataBody;


    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class B2eQueryBasicDraftResultResBody {

        private String trnId;
        private Integer total;

        @XmlElementWrapper(name = "List")
        @XmlElement(name = "Map")
        private List<B2eQueryBasicDraft> list;

    }

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class B2eQueryBasicDraft {

        /**
         * 客户号
         */
        private String customerNo;

        /**
         * 借据号/银承协议号
         */
        private String creditCode;

        /**
         * 融资申请编号
         */
        private String outApplicationNo;

        /**
         * 融资金额
         */
        private String loanAmt;

        /**
         * 币种，使用 ISO 标准货币符
         * 号。如：CNY 表示人民币。
         */
        private String currencyType;

        /**
         * 出账起始日
         */
        private String vaildFrom;

        /**
         * 出账到期日
         */
        private String validTo;

        /**
         * 担保方式
         * 00-无担保
         * 01-100%保证金
         * 02-理财产品质押
         * 03-电子存单质押
         * 04-票据池质押
         */
        private String assureWay;

        /**
         * 审批状态
         * S-审批通过
         * E-审批拒绝
         * R-审批中
         */
        private String status;

        /**
         * 申请日期，格式 yyyy-MM- dd
         */
        private String applyDt;

        /**
         * 审批意见
         */
        private String auditMessage;
    }
}
