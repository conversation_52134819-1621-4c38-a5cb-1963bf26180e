package com.wms.dao.generate;

import com.wms.model.ddi.VWmsOutSncodeOrderExtDto;

public interface VWmsOutSncodeOrderMapper {
    int deleteByPrimaryKey(Integer outSncodeOrderId);

    int insert(VWmsOutSncodeOrderExtDto record);

    int insertSelective(VWmsOutSncodeOrderExtDto record);

    VWmsOutSncodeOrderExtDto selectByPrimaryKey(Integer outSncodeOrderId);

    int updateByPrimaryKeySelective(VWmsOutSncodeOrderExtDto record);

    int updateByPrimaryKey(VWmsOutSncodeOrderExtDto record);
}