package com.vedeng.erp.trader.domain.vo;

import com.vedeng.erp.trader.domain.PublicCustomerCalculateRules;
import com.vedeng.erp.trader.domain.dto.PublicCustomerCalculateRulesChangeDto;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.trader.domain.vo
 * @Date 2022/2/16 14:19
 */
@Data
public class PublicCustomerCalculateRulesDetailVo {

    /**
     * 当前生效的计算规则（最新插入）
     */
    private PublicCustomerCalculateRules publicCustomerCalculateRules;

    /**
     * 计算规则变更记录集合
     */
    private List<PublicCustomerCalculateRulesChangeDto> publicCustomerCalculateRulesChangeDtoList;

}
