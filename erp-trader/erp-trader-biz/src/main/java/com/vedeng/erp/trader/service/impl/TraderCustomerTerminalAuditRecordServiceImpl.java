package com.vedeng.erp.trader.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.trader.domain.entity.TraderCustomerTerminalAuditRecordEntity;
import com.vedeng.erp.trader.dto.TraderCustomerTerminalAuditRecordDto;
import com.vedeng.erp.trader.mapper.TraderCustomerTerminalAuditRecordMapper;
import com.vedeng.erp.trader.mapstruct.TraderCustomerTerminalAuditRecordConvertor;
import com.vedeng.erp.trader.service.TraderCustomerTerminalAuditRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2023/9/5 14:33
 **/
@Service
@Slf4j
public class TraderCustomerTerminalAuditRecordServiceImpl implements TraderCustomerTerminalAuditRecordService {

    @Autowired
    private TraderCustomerTerminalAuditRecordMapper traderCustomerTerminalAuditRecordMapper;

    @Autowired
    private TraderCustomerTerminalAuditRecordConvertor traderCustomerTerminalAuditRecordConvertor;

    @Override
    public String queryLastRejectDesc(Integer traderCustomerTerminalId) {
        return traderCustomerTerminalAuditRecordMapper.queryLastRejectDesc(traderCustomerTerminalId);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void add(TraderCustomerTerminalAuditRecordDto add) {

        log.info("保存终端审核日志：{}", JSON.toJSONString(add));
        TraderCustomerTerminalAuditRecordEntity traderCustomerTerminalAuditRecordEntity = traderCustomerTerminalAuditRecordConvertor.toEntity(add);
        traderCustomerTerminalAuditRecordMapper.insertSelective(traderCustomerTerminalAuditRecordEntity);

    }

    @Override
    public List<TraderCustomerTerminalAuditRecordDto> getAuditRecordList(Integer traderCustomerTerminalId) {

        if (Objects.isNull(traderCustomerTerminalId)) {
            return Collections.emptyList();
        }
        List<TraderCustomerTerminalAuditRecordEntity> traderCustomerTerminalAuditRecordEntities = traderCustomerTerminalAuditRecordMapper.selectByTraderCustomerTerminalId(traderCustomerTerminalId);
        return traderCustomerTerminalAuditRecordConvertor.toDto(traderCustomerTerminalAuditRecordEntities);
    }
}
