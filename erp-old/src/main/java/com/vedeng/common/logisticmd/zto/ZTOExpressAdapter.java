package com.vedeng.common.logisticmd.zto;

import com.vedeng.aftersales.model.AfterSalesDetail;
import com.vedeng.common.logisticmd.ZopExpressService;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.ExpressUtil;
import com.vedeng.logistics.model.Express;
import com.vedeng.order.model.Saleorder;
import com.vedeng.system.model.SysOptionDefinition;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * todo 中通快递接口适配
 *
 * <AUTHOR> [<EMAIL>]
 */
public class ZTOExpressAdapter implements ZTOExpressApi {

    private final static Logger logger = LoggerFactory.getLogger(ZTOExpressAdapter.class);

    private final ZTOExpressConfig config = ZTOExpressConfig.getInstance();

    private final static int DEFAULT_FINANCE_PRINTER_TYPE = 0;


    /**
     * 获取中通快递单号
     *
     * @param companyId
     * @param av
     * @param saleorder
     * @param cwtype
     * @param bizType
     * @param ep
     * @return
     */
    public String getExpressNo(Saleorder saleorder, AfterSalesDetail av, Express ep, Map<String, String> map, Integer companyId, Integer cwtype, Integer bizType) {

        String parameterUseJson = buildRequestParameter(GET_EXPRESS_NO_API_TYPE, saleorder, av, ep, companyId, map, cwtype, bizType);

        final String resultJson;
        try {
            resultJson = ZopExpressService.getZopInfo(GET_EXPRESS_NO_API_TYPE, parameterUseJson, getConfigList());
        } catch (Exception e) {
            logger.error("调用中通快递获取快递单接口发生错误", e);
            throw new IllegalStateException("调用中通快递获取快递单接口发生错误");
        }

        return parserJsonExpressNo(resultJson);
    }

    private Map<String, String> getBigAddressNotes(Integer operatorCompanyId, AfterSalesDetail afterSalesDetail, Saleorder saleOrder,
                                                   Map<String, String> map, Integer cwtype, Integer bizType, Express ep) {
        String parameterUseJson = buildRequestParameter(GET_BIG_ADDR_NODE_API_TYPE, saleOrder, afterSalesDetail, ep, operatorCompanyId, map, cwtype, bizType);

        final String resultJson;
        Map<String, String> returnedMap = new HashMap<>();

        try {
            resultJson = ZopExpressService.getZopInfo(GET_BIG_ADDR_NODE_API_TYPE, parameterUseJson, getConfigList());
            returnedMap = parserJsonMark(resultJson);
        } catch (IllegalStateException ex){
            logger.error("无法解析json时发生错误", ex);
        } catch (Exception e) {
            logger.error("调用中通快递获取大头笔发生错误", e);
            throw new IllegalStateException("调用中通快递获取大头笔发生错误");
        }
        return  returnedMap;
    }


    /**
     * 云打印接口
     *
     * @param operatorCompanyId
     * @param afterSalesDetail
     * @param saleOrder
     * @param map
     * @param cwtype
     * @param bizType
     * @param ep
     */
    public void printTrackingOrder(Saleorder saleOrder, AfterSalesDetail afterSalesDetail, Express ep, Map<String, String> map, Integer operatorCompanyId,
                                   Integer cwtype, Integer bizType) {

        //step1：获取大头笔
        Map<String, String> returnedMap = getBigAddressNotes(operatorCompanyId, afterSalesDetail, saleOrder, map, cwtype, bizType, ep);
        String parameterUseJson = buildRequestParameter(PRINT_CLOUD_ORDER_API_TYPE, saleOrder, afterSalesDetail, ep, operatorCompanyId, returnedMap, cwtype, bizType);

        //step1：调用在线打印机
        String resultJson;
        try {
            resultJson = ZopExpressService.getZopInfo(PRINT_CLOUD_ORDER_API_TYPE, parameterUseJson, getConfigList());
        } catch (Exception e) {
            logger.error("调用中通快递获取大头笔发生错误", e);
            throw new IllegalStateException("调用中通快递获取大头笔发生错误");
        }

        parserJsonPrint(resultJson);
    }


    //~=====================================================================================Private method


    private List<SysOptionDefinition> getConfigList() {
        return config.getSysOptionList();
    }

    private String parserJsonExpressNo(String resultUseJson) {
        String expressNo = null;
        try {
            JSONObject jsonObject = new JSONObject(resultUseJson);
            if (jsonObject.getBoolean(RESULT_FLAG)) {
                JSONObject object = jsonObject.getJSONObject(DATA);
                expressNo = object.getString(TRACKING_ORDER_CODE);
            }
        } catch (Exception e) {
            logger.error("无法解析json时发生错误"+resultUseJson, e);
            throw new IllegalStateException("无法解析json时发生错误"+resultUseJson);
        }

        if (StringUtils.isEmpty(expressNo)) {
            logger.error("快递单日志："+resultUseJson);
            throw new IllegalStateException("返回快单单号为空"+resultUseJson);
        }

        return expressNo;
    }

    private Map<String, String> parserJsonMark(String resultUseJson) {
        Map<String, String> returnedMap = new HashMap<>();
        String errorMessage = null;
        try {
            JSONObject jsonObject = new JSONObject(resultUseJson);
            JSONObject resultBody = jsonObject.getJSONObject("result");
            if (resultBody != null) {
                returnedMap.put("mark", resultBody.getString("mark"));
                returnedMap.put("bagAddr", resultBody.getString("bagAddr"));
            } else {
                errorMessage = jsonObject.getString(MESSAGE);
            }

        } catch (Exception e) {
            logger.error("无法解析json时发生错误", e);
            throw new IllegalStateException("无法解析json时发生错误");
        }

        if (MapUtils.isEmpty(returnedMap)) {
            throw new IllegalStateException("返回大头笔为空,message: " + errorMessage);
        }
        return returnedMap;
    }


    private String buildRequestParameter(Integer apiType, Saleorder saleOrder, AfterSalesDetail afterSalesDetail, Express ep, Integer operatorCompanyId,
                                         Map<String, String> map, Integer cwtype, Integer bizType) {

        final ResultInfo<?> resultInfo = new ExpressUtil().createJson(saleOrder, ep, cwtype, operatorCompanyId, bizType, afterSalesDetail, 0,
                apiType, map, this.getConfigList(), DEFAULT_FINANCE_PRINTER_TYPE);
        if (resultInfo == null || resultInfo.getCode() != 0) {
            throw new IllegalStateException(resultInfo != null ? resultInfo.getMessage() : "请求参数失败");
        }

        if ((resultInfo.getData() == null || !(resultInfo.getData() instanceof String))) {
            throw new IllegalStateException("参数类型为空");
        }

        return (String) resultInfo.getData();
    }


    private void parserJsonPrint(String expressJson) {
        String errorMessage = null;
        boolean successFlag = true;
        try {
            JSONObject jsonObject = new JSONObject(expressJson);
            if (!jsonObject.getBoolean("status")) {
                successFlag = false;
                errorMessage = jsonObject.getString("message");
            }
        } catch (Exception e) {
            logger.error("无法解析json时发生错误", e);
            throw new IllegalStateException("无法解析json时发生错误");
        }

        if (!successFlag) {
            throw new IllegalStateException("中通云打印接口返回结果失败，message: " + errorMessage);
        }
    }

}
