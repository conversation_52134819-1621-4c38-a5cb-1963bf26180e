package com.vedeng.erp.kingdee.service.impl;

import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeePayRefundCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeePayRefundBillEntity;
import com.vedeng.erp.kingdee.dto.KingDeePayRefundBillDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeePayRefundCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeePayRefundConvertor;
import com.vedeng.erp.kingdee.repository.KingDeePayRefundRepository;
import com.vedeng.erp.kingdee.service.KingDeePayRefundApiService;
import com.vedeng.erp.kingdee.service.KingDeePayRefundBillService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/24 16:35
 */
@Service
public class KingDeePayRefundBillServiceImpl extends KingDeeBaseServiceImpl<
        KingDeePayRefundBillEntity,
        KingDeePayRefundBillDto,
        KingDeePayRefundCommand,
        KingDeePayRefundRepository,
        KingDeePayRefundConvertor,
        KingDeePayRefundCommandConvertor> implements KingDeePayRefundBillService, KingDeePayRefundApiService {
}