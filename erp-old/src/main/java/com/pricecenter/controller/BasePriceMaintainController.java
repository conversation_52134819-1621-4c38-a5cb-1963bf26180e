package com.pricecenter.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.pricecenter.constant.VerifyStatusEnum;
import com.pricecenter.dto.*;
import com.pricecenter.service.BasePriceMaintainService;
import com.pricecenter.service.validator.*;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.aftersales.model.AfterSaleSupplyPolicy;
import com.vedeng.aftersales.model.dto.AfterSaleServiceStandardInfoDto;
import com.vedeng.aftersales.service.AfterSaleServiceStandardService;
import com.vedeng.authorization.model.Role;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ApolloCommon;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.goods.GoodsConstants;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.exception.ShowErrorMsgException;
import com.vedeng.common.model.FileInfo;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.validator.FormToken;
import com.vedeng.docSync.service.SyncGoodsService;
import com.vedeng.goods.dao.CoreSkuGenerateMapper;
import com.vedeng.goods.dto.SkuMainDeptDto;
import com.vedeng.goods.manager.extension.handler.GoodsPriceTodoHandler;
import com.vedeng.goods.model.CoreSkuGenerate;
import com.vedeng.goods.model.CoreSpuGenerate;
import com.vedeng.goods.model.vo.SkuAuthorizationVo;
import com.vedeng.goods.service.GoodsService;
import com.vedeng.goods.service.SkuAuthorizationService;
import com.vedeng.goods.service.SkuMainDeptApiService;
import com.vedeng.goods.service.VgoodsService;
import com.vedeng.price.api.price.dto.price.SkuPriceInfoPurchaseHistoryDto;
import com.vedeng.system.service.RegionService;
import com.vedeng.system.service.RoleService;
import com.vedeng.system.service.UserService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.task.IdentityLink;
import org.activiti.engine.task.Task;
import org.activiti.engine.task.TaskQuery;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.File;
import java.io.FileInputStream;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.util.*;
import java.util.stream.Collectors;


@Controller
@RequestMapping("/price/basePriceMaintain")
public class BasePriceMaintainController extends BaseController {

    //定义日志
    private static final Logger LOGGER = LoggerFactory.getLogger(BasePriceMaintainController.class);

    @Autowired
    private BasePriceMaintainService basePriceMaintainService;

    @Autowired
    private UserService userService;

    @Autowired
    @Qualifier("coreSkuGenerateMapper")
    private CoreSkuGenerateMapper coreSkuGenerateMapper;

    @Autowired
    @Qualifier("actionProcdefService")
    private ActionProcdefService actionProcdefService;

    @Autowired
    private RoleService roleService;

    @Autowired // 自动装载
    private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

    @Autowired
    private PriceValidator priceValidator;

    @Autowired
    private SkuNoValidator skuNoValidator;

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private TraderNameValidator traderNameValidator;

    @Autowired
    private RegionService regionService;

    @Autowired
    private SkuAuthorizationService skuAuthorizationService;

    @Autowired
    private VgoodsService vGoodsService;

    public static final List<String> SupplyChainRoleName = Arrays.asList("产品专员", "产品主管", "产品总监", "供应主管");

    public static final List<String> FinanceRoleName = Arrays.asList("财务专员", "财务总监");

    public static final List<String> SaleRoleName = Arrays.asList("销售经理", "医院销售工程师", "耗材销售小组", "耗材销售工程师", "市场部京东销售", "集团销售", "线下销售", "销售工程师", "销售主管", "销售总监");

    /**
     * erp->价格中心->基础价格维护->查看页
     */
    public static final String BASEPRICEMAINTAIN_DETAIL_URL = "./price/basePriceMaintain/detail.do?skuPriceChangeApplyId=";

    @Autowired
    private AfterSaleServiceStandardService afterSaleServiceStandardService;

    @Resource
    private GoodsPriceTodoHandler goodsPriceTodoHandler;

    @Autowired
    private SkuMainDeptApiService skuMainDeptApiService;
    
    @Autowired
    private ApolloCommon apolloCommon;
    
    @Autowired
    SyncGoodsService  syncGoodsService;
    

    /**
     * 基础价格维护列表页
     *
     * @Rock
     */
    @ResponseBody
    @RequestMapping(value = "/index", produces = "application/json;charset=UTF-8")
    public ModelAndView index(HttpServletRequest request, SkuPriceChangeApplyPageQueryDto queryDto,
                              @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                              @RequestParam(required = false) Integer pageSize) throws Exception {

        LOGGER.info("基础价格维护列表页");
        queryDto.setPageSize(pageSize);
        queryDto.setPageNo(pageNo);
        PageResultDto<SkuPriceChangeApplyDto> pageResultVo = this.basePriceMaintainService.findByPage(queryDto);
        List<SkuPriceChangeApplyDto> skuPriceChangeApplyDtos = pageResultVo.getDatas();
        List<SkuPriceChangeApplyDtoVo> skuPriceChangeApplyDtoVos = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(skuPriceChangeApplyDtos)) {
            for (SkuPriceChangeApplyDto skuPriceChangeApplyDto : skuPriceChangeApplyDtos) {
                dealWithSalePriceInfo(skuPriceChangeApplyDto);
                String unitName = goodsService.getUnitNameBySkuNo(skuPriceChangeApplyDto.getSkuNo());
                SkuPriceChangeApplyDtoVo skuPriceChangeApplyDtoVo = new SkuPriceChangeApplyDtoVo();
                BeanUtils.copyProperties(skuPriceChangeApplyDto, skuPriceChangeApplyDtoVo);
                skuPriceChangeApplyDtoVo.setUnitName(unitName);
                if (skuPriceChangeApplyDtoVo.getDisabled() == 1 || CollectionUtils.isEmpty(skuPriceChangeApplyDtoVo.getEffectPurchaseList())) {
                    skuPriceChangeApplyDtoVo.setAlreadyPriced(0);
                } else {
                    skuPriceChangeApplyDtoVo.setAlreadyPriced(1);
                }

                skuPriceChangeApplyDtoVos.add(skuPriceChangeApplyDtoVo);
            }
        }

        Page page = super.getPageTag(request, pageNo, pageSize);
        page.setTotalRecord(pageResultVo.getTotalRecords());

        ModelAndView mv = new ModelAndView("/price/index");

        setSkuAuthorizationInfo(skuPriceChangeApplyDtoVos);
        skuPriceChangeApplyDtoVos = dealSkuListByReport(queryDto, skuPriceChangeApplyDtoVos);

        mv.addObject("priceChangeApplyList", skuPriceChangeApplyDtoVos);
        mv.addObject("queryDto", queryDto);
        mv.addObject("page", page);
        mv.addObject("regions", regionService.getRegionByParentId(1));
        mv.addObject("terminalTypes", skuAuthorizationService.getAllTerminalTypes());

        List<User> manages = userService.selectAllAssignUser();
        //所属产品经理
        mv.addObject("managerUserList", manages);
        //所属产品助理
        mv.addObject("assUserList", manages);
        //审核人
        mv.addObject("checkPersonList", userService.selectAllcheckPreson());
        return mv;
    }

    /**
     * 应产品要求(@naruto ) 对于sku报备信息的同步差异需要进行筛选
     *
     * @param queryDto
     * @param skuPriceChangeApplyDtoVos
     * @return
     */
    private List<SkuPriceChangeApplyDtoVo> dealSkuListByReport(SkuPriceChangeApplyPageQueryDto queryDto,
                                                               List<SkuPriceChangeApplyDtoVo> skuPriceChangeApplyDtoVos) {
        if (CollectionUtils.isNotEmpty(skuPriceChangeApplyDtoVos) && queryDto.getIsNeedReport() != null) {
            final Integer notNeedReport = 0;
            final Integer needReport = 1;
            if (notNeedReport.equals(queryDto.getIsNeedReport())) {
                skuPriceChangeApplyDtoVos = skuPriceChangeApplyDtoVos.stream()
                        .filter(skuPriceChangeApplyDtoVo ->
                                skuPriceChangeApplyDtoVo.getIsNeedReport() == null ||
                                        notNeedReport.equals(skuPriceChangeApplyDtoVo.getIsNeedReport()))
                        .collect(Collectors.toList());
            } else if (needReport.equals(queryDto.getIsNeedReport())) {
                skuPriceChangeApplyDtoVos = skuPriceChangeApplyDtoVos.stream()
                        .filter(skuPriceChangeApplyDtoVo ->
                                needReport.equals(skuPriceChangeApplyDtoVo.getIsNeedReport()))
                        .collect(Collectors.toList());
            }
        }
        return skuPriceChangeApplyDtoVos;
    }

    /**
     * 设置sku的报备信息
     *
     * @param skuPriceChangeApplyDtoVos
     */
    private void setSkuAuthorizationInfo(List<SkuPriceChangeApplyDtoVo> skuPriceChangeApplyDtoVos) {
        if (CollectionUtils.isEmpty(skuPriceChangeApplyDtoVos)) {
            return;
        }
        skuPriceChangeApplyDtoVos.stream().forEach(skuPriceChangeApplyDtoVo -> {
            CoreSkuGenerate coreSkuGenerate = goodsService.getSkuAuthotizationInfoBySku(skuPriceChangeApplyDtoVo.getSkuId());
            if (coreSkuGenerate == null) {
                skuPriceChangeApplyDtoVo.setIsNeedReport(null);
                skuPriceChangeApplyDtoVo.setIsAuthorized(0);
                return;
            }

            skuPriceChangeApplyDtoVo.setIsNeedReport(coreSkuGenerate.getIsNeedReport());
            skuPriceChangeApplyDtoVo.setIsAuthorized(coreSkuGenerate.getIsAuthorized() == null ? 0 : coreSkuGenerate.getIsAuthorized());
            if (skuPriceChangeApplyDtoVo.getIsNeedReport() != null && skuPriceChangeApplyDtoVo.getIsNeedReport() == 1 &&
                    skuPriceChangeApplyDtoVo.getIsAuthorized() == 1) {
                SkuAuthorizationVo skuAuthorizationInfo = skuAuthorizationService.getSkuAuthorizationInfoBySkuId(coreSkuGenerate.getSkuId().intValue());
                skuPriceChangeApplyDtoVo.setSkuAuthorizationVo(skuAuthorizationInfo);
            }
        });
    }

    @ResponseBody
    @RequestMapping(value = "/toEditValidator")
    public ResultInfo toEditValidator(HttpServletRequest request, @Param("skuPriceChangeApplyId") Integer skuPriceChangeApplyId) {
        try {
            User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
            SkuPriceChangeApplyDto skuPriceChangeApplyDto = this.basePriceMaintainService.getBasicPriceChangeInfoById(skuPriceChangeApplyId);
            //VDERP-17941 管理员可以针对所有价格进行编辑，并且去除了禁用状态下无法编辑的限制
            if(apolloCommon.priceWorkFlowAdminUsers.contains(user.getUsername())) {
            	return new ResultInfo(0, "成功", skuPriceChangeApplyDto.getVerifyStatusName());
            }
            UserInfo userInfo = this.coreSkuGenerateMapper.getAssignAndManageId(skuPriceChangeApplyDto.getSkuId());
            //没有归属用户直接报错
            if (userInfo == null) {
                return new ResultInfo(-1, "您没有操作权限，申请开通权限请联系研发部Aadi");
            }
            if (!user.getUserId().equals(userInfo.getAssistantId()) && !user.getUserId().equals(userInfo.getManagerId())) {
                return new ResultInfo(-1, "您没有操作权限，申请开通权限请联系研发部Aadi");
            }
            return new ResultInfo(0, "成功", skuPriceChangeApplyDto.getVerifyStatusName());
        } catch (Exception e) {
            logger.error("toEditValidator", e);
            return new ResultInfo(-1, "失败");
        }
    }


    @ResponseBody
    @RequestMapping(value = "/toEffectPriceValidator")
    public ResultInfo toEffectPriceValidator(HttpServletRequest request, @Param("skuPriceChangeApplyId") Integer skuPriceChangeApplyId) {
        try {

            User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);

            SkuPriceChangeApplyDto skuPriceChangeApplyDto = this.basePriceMaintainService.findBasePriceMaintainById(skuPriceChangeApplyId);

            if (CollectionUtils.isEmpty(skuPriceChangeApplyDto.getEffectPurchaseList())) {
                return new ResultInfo(-1, "尚未核价，无法查看");
            }

            return new ResultInfo(0, "成功", skuPriceChangeApplyDto.getVerifyStatusName());

        } catch (Exception e) {
            logger.error("toEditValidator", e);
            return new ResultInfo(-1, "失败");
        }
    }

    @ResponseBody
    @RequestMapping(value = "/toDetailValidator")
    public ResultInfo toDetailValidator(HttpServletRequest request, @Param("skuPriceChangeApplyId") Integer skuPriceChangeApplyId) {
        try {
            User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
            if (!(userBelongToRolelist(user, SupplyChainRoleName) || userBelongToRolelist(user, FinanceRoleName) || userBelongToRolelist(user, SaleRoleName))) {
                return new ResultInfo(-1, "您没有操作权限，申请开通权限请联系研发部Aadi");
            }
            return new ResultInfo(0, "成功");
        } catch (Exception e) {
            logger.error("toEditValidator", e);
            return new ResultInfo(-1, "失败");
        }
    }


    /**
     * 价格中心编辑页
     */
    @RequestMapping(value = "/edit")
    @FormToken(save = true)
    public ModelAndView edit(@Param("skuPriceChangeApplyId") Integer skuPriceChangeApplyId) {
        ModelAndView mv = new ModelAndView();
        SkuPriceChangeApplyDto skuPriceChangeApplyDto = this.basePriceMaintainService.findBasePriceMaintainById(skuPriceChangeApplyId);
        getMainDept(skuPriceChangeApplyDto);
        //如果是审核中,就删除现在的流程实例，同事将变更申请的状态改为待完善
        if (VerifyStatusEnum.Reviewing.getName().equals(skuPriceChangeApplyDto.getVerifyStatusName())) {
            mv.addObject("message", "当前sku审核中,不可进入编辑，请返回列表页刷新！");
            return fail(mv);
        }
        mv.setViewName("price/priceEdit");
        mv.addObject("skuPriceChangeApplyDto", skuPriceChangeApplyDto);
        //是否首次核价
        boolean isFirst = CollectionUtils.isEmpty(skuPriceChangeApplyDto.getEffectPurchaseList());
        //是否首次核价 如果为1 就表示是
        mv.addObject("firstVerifyPirce", isFirst ? "1" : "0");
        mv.addObject("firstEffectTime", isFirst ? "无" :skuPriceChangeApplyDto.getEffectSaleInfoList().stream().findFirst().map(SkuPriceInfoSaleDetailDto::getModTime).orElse("无"));

        //查看上架状态
        Integer onSaleStatus = this.coreSkuGenerateMapper.getSkuOnSale(skuPriceChangeApplyDto.getSkuNo());
        if(Objects.isNull(onSaleStatus)) {
        	mv.addObject("message", "当前sku异常，无法编辑或查看！");
            return fail(mv);
        }
        
        if (onSaleStatus != 0) {
            StringBuffer platStr = new StringBuffer();
            if ((onSaleStatus & GoodsConstants.ONE) == 1) {
                platStr.append("贝登/");
            }
            if ((onSaleStatus >>> GoodsConstants.ONE & GoodsConstants.ONE) == 1) {
                platStr.append("医械购/");
            }
            if ((onSaleStatus >>> GoodsConstants.TWO & GoodsConstants.ONE) == 1) {
                platStr.append("科研购/");
            }
            if ((onSaleStatus >>> GoodsConstants.THREE & GoodsConstants.ONE) == 1) {
                platStr.append("集采/");
            }
            if (StringUtils.isNotBlank(platStr.toString())) {
                mv.addObject("platStr", platStr.toString().substring(0, platStr.toString().lastIndexOf("/")));
            }
        }

        //是否已上架
        mv.addObject("isOnSale", onSaleStatus != 0 ? "true" : "false");
        //处理下采购均价
        dealWithPurchasePriceAvgPrice(mv, skuPriceChangeApplyDto);
        //获取最近一次审核通过的贝登售后标准
        AfterSaleServiceStandardInfoDto afterSaleServiceStandardInfoDto = this.afterSaleServiceStandardService.getEffectAfterSalePolicy(skuPriceChangeApplyDto.getSkuNo());
        mv.addObject("installFee", "0");
        if (afterSaleServiceStandardInfoDto != null && afterSaleServiceStandardInfoDto.getInstallPolicyInstallFee() != null) {
            mv.addObject("installFee", afterSaleServiceStandardInfoDto.getInstallPolicyInstallFee());
        }
        //已生效列表 供应商采购价是否维护
        if (CollectionUtils.isNotEmpty(skuPriceChangeApplyDto.getEffectPurchaseList())) {
            skuPriceChangeApplyDto.getEffectPurchaseList().stream().forEach(purchase -> {
                AfterSaleSupplyPolicy afterSaleSupplyPolicy = this.afterSaleServiceStandardService.
                        findSupplyAfterSalePolicyBySkuNoAndTraderId(purchase.getTraderId().longValue(), skuPriceChangeApplyDto.getSkuNo());
                purchase.setSupplyPolicyMaintained(afterSaleSupplyPolicy == null ? 0 : 1);
            });
        }
        //本次更新模块 供应商采购价是否维护
        if (CollectionUtils.isNotEmpty(skuPriceChangeApplyDto.getPurchaseInfoList())) {
            skuPriceChangeApplyDto.getPurchaseInfoList().stream().forEach(purchase -> {
                AfterSaleSupplyPolicy afterSaleSupplyPolicy = this.afterSaleServiceStandardService.
                        findSupplyAfterSalePolicyBySkuNoAndTraderId(purchase.getTraderId().longValue(), skuPriceChangeApplyDto.getSkuNo());
                purchase.setSupplyPolicyMaintained(afterSaleSupplyPolicy == null ? 0 : 1);
            });
        }

        return mv;
    }

    /**
     * 处理采购均价
     *
     * @param mv
     * @param skuPriceChangeApplyDto
     */
    private void dealWithPurchasePriceAvgPrice(ModelAndView mv, SkuPriceChangeApplyDto skuPriceChangeApplyDto) {

        //从本次更新价格总计算平均价格
        double avgPrice = skuPriceChangeApplyDto.getPurchaseInfoList().stream().mapToDouble(purchase -> purchase.getPurchasePrice().doubleValue()).average().orElse(0);
        BigDecimal avgPurchasePriceBd = BigDecimal.valueOf(avgPrice).setScale(2, BigDecimal.ROUND_HALF_UP);
        mv.addObject("avgPurchasePrice", avgPurchasePriceBd);
        //VDERP-5632:基础价格毛利率调整，从6%改成8%
        mv.addObject("minGrossPrice", avgPurchasePriceBd.divide(new BigDecimal("0.92"), 2, BigDecimal.ROUND_HALF_UP));
        if (skuPriceChangeApplyDto.getTerminalPrice() != null) {
            BigDecimal terminalPriceRate = skuPriceChangeApplyDto.getTerminalPrice().subtract(avgPurchasePriceBd)
                    .divide(skuPriceChangeApplyDto.getTerminalPrice(), 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(BigDecimal.valueOf(100))
                    .setScale(2, BigDecimal.ROUND_HALF_UP);
            mv.addObject("terminalPriceRate", terminalPriceRate);
        }

        if (skuPriceChangeApplyDto.getDistributionPrice() != null){
            BigDecimal distributionPriceRate = skuPriceChangeApplyDto.getDistributionPrice().subtract(avgPurchasePriceBd)
                    .divide(skuPriceChangeApplyDto.getDistributionPrice(), 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(BigDecimal.valueOf(100))
                    .setScale(2, BigDecimal.ROUND_HALF_UP);
            mv.addObject("distributionPriceRate", distributionPriceRate);
        }
        if (skuPriceChangeApplyDto.getGroupPrice() != null) {
            BigDecimal groupPriceRate = skuPriceChangeApplyDto.getGroupPrice().subtract(avgPurchasePriceBd)
                    .divide(skuPriceChangeApplyDto.getGroupPrice(), 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(BigDecimal.valueOf(100))
                    .setScale(2, BigDecimal.ROUND_HALF_UP);
            mv.addObject("groupPriceRate", groupPriceRate);
        }
        if (skuPriceChangeApplyDto.getElectronicCommercePrice() != null) {
            BigDecimal eCommercePriceRate = skuPriceChangeApplyDto.getElectronicCommercePrice().subtract(avgPurchasePriceBd)
                    .divide(skuPriceChangeApplyDto.getElectronicCommercePrice(), 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(BigDecimal.valueOf(100))
                    .setScale(2, BigDecimal.ROUND_HALF_UP);
            mv.addObject("eCommercePriceRate", eCommercePriceRate);
        }
        if (skuPriceChangeApplyDto.getResearchTerminalPrice() != null) {
            BigDecimal researchTerminalPriceRate = skuPriceChangeApplyDto.getResearchTerminalPrice().subtract(avgPurchasePriceBd)
                    .divide(skuPriceChangeApplyDto.getResearchTerminalPrice(), 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(BigDecimal.valueOf(100))
                    .setScale(2, BigDecimal.ROUND_HALF_UP);
            mv.addObject("researchTerminalPriceRate", researchTerminalPriceRate);
        }
    }

    /**
     * 获取价格的毛利范围
     *
     * @param sku       sku编号
     * @param priceType 价格类型: 1.经销价 2.终端价
     * @return
     */
    private List<BigDecimal> getSuggestRange(String sku, int priceType) {
        String suggestRangeStr = null;
        CoreSpuGenerate spuInfo = this.vGoodsService.findSpuInfoBySkuNo(sku);
        //经销价
        if (priceType == ErpConst.ONE) {
            switch (spuInfo.getSpuType()) {
                //器械设备
                case 316:
                    suggestRangeStr = "1.05,1.15";
                    break;
                //配件
                case 1008:
                    suggestRangeStr = "1.05,1.15";
                    break;
                //试剂
                case 318:
                    suggestRangeStr = "1.05,1.15";
                    break;
                //耗材
                case 317:
                    suggestRangeStr = "1.15,1.25";
                    break;
                default:
                    break;
            }
        }
        //终端价
        if (priceType == ErpConst.TWO) {
            switch (spuInfo.getSpuType()) {
                //器械设备
                case 316:
                    suggestRangeStr = "1.12,1.3";
                    break;
                //配件
                case 1008:
                    suggestRangeStr = "1.12,1.3";
                    break;
                //试剂
                case 318:
                    suggestRangeStr = "1.12,1.3";
                    break;
                //耗材
                case 317:
                    suggestRangeStr = "1.15,1.3";
                    break;
                default:
                    break;
            }
        }
        return Arrays.stream(suggestRangeStr.split(",")).map(range -> new BigDecimal(range)).collect(Collectors.toList());
    }


    /**
     * 价格中心编辑
     *
     * @param editBasePriceDto
     */
    @RequestMapping(value = "/editBasePrice")
    @FormToken(remove = true)
    public ModelAndView editBasePrice(HttpServletRequest request, EditBasePriceDto editBasePriceDto) {
        ModelAndView mv = new ModelAndView();
        try {
            //通知价格中心,这次变动的数据
            boolean success = basePriceMaintainService.informPriceChangeApply(editBasePriceDto);

            if (!success) {
                //开始工作流
                throw new Exception("请求价格中心失败");
            }
            //编辑保存采购价格时，解除商品待办
            if (!ArrayUtils.isEmpty(editBasePriceDto.getPurchasePrice())) {
                goodsPriceTodoHandler.finish(editBasePriceDto.getSkuNo());
            }
            //开始工作流
            startProcessInstance(request, editBasePriceDto, false);
            mv.addObject("url", "./detail.do?skuPriceChangeApplyId="+editBasePriceDto.getSkuPriceChangeApplyId());
            return success(mv);

        } catch (ShowErrorMsgException e2) {
            LOGGER.error("editBasePrice warn", e2);
            mv.addObject("message", e2.getMessage());
            return fail(mv);
        } catch (Exception e) {
            LOGGER.error("editBasePrice warn", e);
            mv.addObject("message", e.getMessage());
            return fail(mv);
        }
    }


    //开始工作流
    private void startProcessInstance(HttpServletRequest request, EditBasePriceDto editBasePriceDto, boolean batch) throws Exception {
        //初始化工作流变量
        Map<String, Object> variableMap = new HashMap<String, Object>();
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        variableMap.put("currentAssinee", user.getUsername());
        variableMap.put("processDefinitionKey", "priceChangeApply");
        variableMap.put("skuNo", editBasePriceDto.getSkuNo());
        CoreSkuGenerate skuInfo = coreSkuGenerateMapper.selectBySkuNo(editBasePriceDto.getSkuNo());
        variableMap.put("skuId", skuInfo.getSkuId());
        variableMap.put("secondChangePrice", editBasePriceDto.isSecondChangePrice());
        variableMap.put("skuPriceChangeApplyId", editBasePriceDto.getSkuPriceChangeApplyId());
        variableMap.put("firstVerifyPirce", batch ? "1" : editBasePriceDto.getFirstVerifyPirce());
        String businessKey = "priceChangeApply_" + editBasePriceDto.getSkuPriceChangeApplyId();
        variableMap.put("businessKey", businessKey);
        variableMap.put("pass",true);
        //VDERP-17941 获取用户名称，判断是否为apollo中配置的管理员相同，如果相同，直接审核通过，如果不同，走原审批流程
        boolean isAdmin = false;
        if(apolloCommon.priceWorkFlowAdminUsers.contains(user.getUsername())) {
        	isAdmin = true;
        }
        variableMap.put("isAdmin", isAdmin);
        actionProcdefService.createProcessInstance(request, variableMap.get("processDefinitionKey").toString(), variableMap.get("businessKey").toString(), variableMap);
        //获取当前活动节点
        Task taskInfo = processEngine.getTaskService().createTaskQuery().processInstanceBusinessKey(variableMap.get("businessKey").toString()).singleResult();
        //完成当前任务
        actionProcdefService.complementTask(request, taskInfo.getId(), "", user.getUsername(), variableMap);
        //VDERP-17941 管理员直接审批通过，非管理员，按照原始逻辑处理
        if(isAdmin) {
        	//下一个任务ID
        	Task task = processEngine.getTaskService().createTaskQuery().processInstanceBusinessKey(businessKey).singleResult();
    		actionProcdefService.complementTask(null, task.getId(), "","njadmin", variableMap);
    		Task task2 = processEngine.getTaskService().createTaskQuery().processInstanceBusinessKey(businessKey).singleResult();
    		actionProcdefService.complementTask(null, task2.getId(), "","njadmin", variableMap);
    		//更新sku的核价审核状态为审核通过
        	this.coreSkuGenerateMapper.updateSkuPriceVerifyStatusBySkuNo(editBasePriceDto.getSkuNo(), VerifyStatusEnum.Approved.getValue());
        }else {
        	//产品经理为申请人自动审核通过
        	String manager=userService.getManagerBySkuId(skuInfo.getSkuId());
        	if(user.getUsername().equals(manager)){
        		//下一个任务ID
        		String taskId = processEngine.getTaskService().createTaskQuery().processInstanceBusinessKey(businessKey).singleResult().getId();
        		Authentication.setAuthenticatedUserId(user.getUsername());
        		actionProcdefService.complementTask(null, taskId, "",manager, variableMap);
        	}
        	//更新sku的核价审核状态为审核中
        	this.coreSkuGenerateMapper.updateSkuPriceVerifyStatusBySkuNo(editBasePriceDto.getSkuNo(), VerifyStatusEnum.Reviewing.getValue());
        	//审核中时，设置价格禁用
        	int  skuPriceChangeApplyId = Integer.parseInt(String.valueOf(editBasePriceDto.getSkuPriceChangeApplyId()));
        	this.basePriceMaintainService.disablePrice(skuPriceChangeApplyId, "");
        }

        //VDERP-17941,非管理需要走这段逻辑
        if (!batch && !isAdmin) {
            //VDERP-11712【faq4585】【erp】价格中心-基础价格维护，筛选条件审核人 搜索与里面审核人不一致start
            //重新获取审核流中的审核人信息
            List<Integer> userIds = new ArrayList<>();
            Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, businessKey);
            taskInfo = processEngine.getTaskService().createTaskQuery().processInstanceBusinessKey(businessKey).singleResult();
            //获取审核人候选组
            Map<String, Object> candidateUserMap = (Map<String, Object>) historicInfo.get("candidateUserMap");
            //获取审核人候选组
            List<IdentityLink> candidateUserList = (List<IdentityLink>) candidateUserMap.get(taskInfo.getId());
            if (CollectionUtils.isNotEmpty(candidateUserList)) {
                List<String> userNameList = candidateUserList.stream().map(identityLink -> identityLink.getUserId()).collect(Collectors.toList());
                logger.info("价格变更审核任务信息{},查询到审核流审核人信息{},", taskInfo.getId(),JSON.toJSONString(userNameList));
                for(String verifyUser : userNameList){
                    User cur = userService.getByUsername(verifyUser,ErpConst.ONE);
                    userIds.add(cur.getUserId());
                }
            }
            //VDERP-11712【faq4585】【erp】价格中心-基础价格维护，筛选条件审核人 搜索与里面审核人不一致
            //更新策略变更的申请人
            if(CollectionUtils.isEmpty(userIds)){
                userIds = roleService.getUserIdByRoleName("供应主管", 1);
            }
            this.basePriceMaintainService.updatePriceChangeAuditorByIds(Arrays.asList(editBasePriceDto.getSkuPriceChangeApplyId()), userIds);
        }
        //更新商品中的是否包含运费，批量的在batchDealwithPriceInfo方法中执行
        if(!batch) {
        	List<Integer> skuIds = Arrays.asList(skuInfo.getSkuId());
        	syncGoodsService.syncSkuInfo2EsBySkuIds(skuIds);
        }

    }

    /**
     * 价格中心详情页
     */
    @RequestMapping(value = "/detail")
    public ModelAndView detail(HttpServletRequest request, @Param("skuPriceChangeApplyId") Integer skuPriceChangeApplyId) {

        SkuPriceChangeApplyDto skuPriceChangeApplyDto = this.basePriceMaintainService.findBasePriceMaintainById(skuPriceChangeApplyId);

        getMainDept(skuPriceChangeApplyDto);

        ModelAndView mv = new ModelAndView();
        mv.setViewName("price/priceDetail");
        mv.addObject("skuPriceChangeApplyDto", skuPriceChangeApplyDto);

        dealWithSkuPriceChange(skuPriceChangeApplyDto);

        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, "priceChangeApply_" + skuPriceChangeApplyId);

        List<HistoricActivityInstance> list = (List<HistoricActivityInstance>) historicInfo.get("historicActivityInstance");
        if (CollUtil.isNotEmpty(list)) {
            CollUtil.reverse(list);
        }
        //是否首次核价
        boolean isFirst = CollectionUtils.isEmpty(skuPriceChangeApplyDto.getEffectPurchaseList());
        mv.addObject("firstEffectTime", isFirst ? "无" :
                skuPriceChangeApplyDto.getEffectSaleInfoList().stream().findFirst().map(SkuPriceInfoSaleDetailDto::getModTime).orElse("无"));
        mv.addObject("historicInfo", historicInfo);
        mv.addObject("historicActivityInstance", list);
        mv.addObject("commentMap", historicInfo.get("commentMap"));

        if (historicInfo.get("taskInfo") != null) {

            Task taskInfo = (Task) historicInfo.get("taskInfo");
            mv.addObject("taskId", taskInfo.getId());
            mv.addObject("taskInfo", taskInfo);

            //获取审核人候选组
            Map<String, Object> candidateUserMap = (Map<String, Object>) historicInfo.get("candidateUserMap");

            //获取审核人候选组
            List<IdentityLink> candidateUserList = (List<IdentityLink>) candidateUserMap.get(taskInfo.getId());

            if (CollectionUtils.isNotEmpty(candidateUserList)) {

                List<String> userNameList = candidateUserList.stream().map(identityLink -> identityLink.getUserId()).collect(Collectors.toList());

                mv.addObject("verifyUsers", StringUtils.join(userNameList, ","));
            }
        }

        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        mv.addObject("curr_user", user);


        //获取最近一次审核通过的贝登售后标准
        AfterSaleServiceStandardInfoDto afterSaleServiceStandardInfoDto = this.afterSaleServiceStandardService.getEffectAfterSalePolicy(skuPriceChangeApplyDto.getSkuNo());
        mv.addObject("afterSaleServiceStandardInfoDto", afterSaleServiceStandardInfoDto);

        mv.addObject("installFee", "0");
        if (afterSaleServiceStandardInfoDto != null && afterSaleServiceStandardInfoDto.getInstallPolicyInstallFee() != null) {
            mv.addObject("installFee", afterSaleServiceStandardInfoDto.getInstallPolicyInstallFee());
        }

        //已生效列表 供应商采购价是否维护
        if (CollectionUtils.isNotEmpty(skuPriceChangeApplyDto.getEffectPurchaseList())) {
            skuPriceChangeApplyDto.getEffectPurchaseList().stream().forEach(purchase -> {
                AfterSaleSupplyPolicy afterSaleSupplyPolicy = this.afterSaleServiceStandardService.
                        findSupplyAfterSalePolicyBySkuNoAndTraderId(purchase.getTraderId().longValue(), skuPriceChangeApplyDto.getSkuNo());
                purchase.setSupplyPolicyMaintained(afterSaleSupplyPolicy == null ? 0 : 1);
            });
        }

        //本次更新模块 供应商采购价是否维护
        if (CollectionUtils.isNotEmpty(skuPriceChangeApplyDto.getPurchaseInfoList())) {
            skuPriceChangeApplyDto.getPurchaseInfoList().stream().forEach(purchase -> {
                AfterSaleSupplyPolicy afterSaleSupplyPolicy = this.afterSaleServiceStandardService.
                        findSupplyAfterSalePolicyBySkuNoAndTraderId(purchase.getTraderId().longValue(), skuPriceChangeApplyDto.getSkuNo());
                purchase.setSupplyPolicyMaintained(afterSaleSupplyPolicy == null ? 0 : 1);
            });
        }

        //VDERP-17941 获取用户名称，判断是否为apollo中配置的管理员相同，如果相同，直接审核通过，如果不同，走原审批流程
        boolean isAdmin = false;
        if(apolloCommon.priceWorkFlowAdminUsers.contains(user.getUsername())) {
        	isAdmin = true;
        }
        
        if (isAdmin || userBelongToRolelist(user, SupplyChainRoleName)) {
            UserInfo userInfo = this.coreSkuGenerateMapper.getAssignAndManageId(skuPriceChangeApplyDto.getSkuId());

            mv.addObject("productMangerOrAsisit", "false");

            if (userInfo != null && (user.getUserId().equals(userInfo.getAssistantId()) || user.getUserId().equals(userInfo.getManagerId()))) {
                mv.addObject("productMangerOrAsisit", "true");
            }

            mv.setViewName("price/priceDetailForSupply");
        } else if (userBelongToRolelist(user, FinanceRoleName)) {
            mv.setViewName("price/priceDetailForFinace");
        } else if (userBelongToRolelist(user, SaleRoleName)) {

            //销售要看到生效的价格
            if (CollectionUtils.isEmpty(skuPriceChangeApplyDto.getEffectSaleInfoList())) {
                skuPriceChangeApplyDto.setTerminalPrice(null);
                skuPriceChangeApplyDto.setDistributionPrice(null);
                skuPriceChangeApplyDto.setMarketPrice(null);
            } else {

                SkuPriceInfoSaleDetailDto saleInfoDto = skuPriceChangeApplyDto.getEffectSaleInfoList().stream().findFirst().get();
                skuPriceChangeApplyDto.setTerminalPrice(saleInfoDto.getTerminalPrice());
                skuPriceChangeApplyDto.setDistributionPrice(saleInfoDto.getDistributionPrice());
                skuPriceChangeApplyDto.setMarketPrice(saleInfoDto.getMarketPrice());
            }
            mv.setViewName("price/priceDetailForSale");
        }
        return mv;
    }

    private void getMainDept(SkuPriceChangeApplyDto skuPriceChangeApplyDto) {
        // 获取SKU的主销售部门
        List<SkuMainDeptDto> mainDeptBySkuNo = skuMainDeptApiService.getMainDeptBySkuNo(skuPriceChangeApplyDto.getSkuNo());
        if (CollectionUtils.isNotEmpty(mainDeptBySkuNo) && Objects.nonNull(mainDeptBySkuNo.get(0).getMainDept())) {
            skuPriceChangeApplyDto.setMainDept(mainDeptBySkuNo.get(0).getMainDept());
        }
    }


    /**
     * 处理价格
     *
     * @param skuPriceChangeApplyDto
     */
    private void dealWithSkuPriceChange(SkuPriceChangeApplyDto skuPriceChangeApplyDto) {

        //处理采购价
        dealWithPurchasePriceInfo(skuPriceChangeApplyDto);

        //处理销售价
        dealWithSalePriceInfo(skuPriceChangeApplyDto);
    }

    /**
     * 处理销售价
     *
     * @param skuPriceChangeApplyDto
     */
    private void dealWithSalePriceInfo(SkuPriceChangeApplyDto skuPriceChangeApplyDto) {

        //如果没有生效的销售价 第一次维护
        if (CollectionUtils.isEmpty(skuPriceChangeApplyDto.getEffectSaleInfoList())) {
            skuPriceChangeApplyDto.setMarketPriceChange(false);
            skuPriceChangeApplyDto.setTerminalPriceChange(false);
            skuPriceChangeApplyDto.setDistributionPriceChange(false);
            skuPriceChangeApplyDto.setGroupPriceChange(false);
            skuPriceChangeApplyDto.setElectronicCommercePriceChange(false);
            skuPriceChangeApplyDto.setResearchTerminalPriceChange(false);
            return;
        }

        SkuPriceInfoSaleDetailDto detail = new SkuPriceInfoSaleDetailDto();

        if (!VerifyStatusEnum.Approved.getName().equals(skuPriceChangeApplyDto.getVerifyStatusName())) {

            detail = skuPriceChangeApplyDto.getEffectSaleInfoList().stream().findFirst().orElse(null);

        } else {

            //取倒数第二次价格
            SkuPriceInfoSaleDetailHistoryDto historyDto = null;

            if (CollectionUtils.isNotEmpty(skuPriceChangeApplyDto.getEffectSaleInfoHistoryList()) && skuPriceChangeApplyDto.getEffectSaleInfoHistoryList().size() >= 2) {

                historyDto = skuPriceChangeApplyDto.getEffectSaleInfoHistoryList().get(1);
            }

            if (historyDto == null) {
                setDefaultSalePrice(skuPriceChangeApplyDto);
                return;
            }

            BeanUtils.copyProperties(historyDto, detail);
        }

        if (detail != null) {
            skuPriceChangeApplyDto.setLastMarketPrice(detail.getMarketPrice());
            if(skuPriceChangeApplyDto.getMarketPrice()!=null &&skuPriceChangeApplyDto.getLastMarketPrice()!=null ) {
                skuPriceChangeApplyDto.setMarketPriceChange(skuPriceChangeApplyDto.getMarketPrice().compareTo(skuPriceChangeApplyDto.getLastMarketPrice()) != 0);
            }
            skuPriceChangeApplyDto.setLastTerminalPrice(detail.getTerminalPrice());
            if(skuPriceChangeApplyDto.getLastTerminalPrice()!=null &&skuPriceChangeApplyDto.getTerminalPrice()!=null ) {
                skuPriceChangeApplyDto.setTerminalPriceChange(skuPriceChangeApplyDto.getTerminalPrice().compareTo(skuPriceChangeApplyDto.getLastTerminalPrice()) != 0);
            }
            skuPriceChangeApplyDto.setLastDistributionPrice(detail.getDistributionPrice());
            if(skuPriceChangeApplyDto.getDistributionPrice()!= null && skuPriceChangeApplyDto.getLastDistributionPrice()!=null) {
                skuPriceChangeApplyDto.setDistributionPriceChange(skuPriceChangeApplyDto.getDistributionPrice().compareTo(skuPriceChangeApplyDto.getLastDistributionPrice()) != 0);
            }

            skuPriceChangeApplyDto.setLastGroupPrice(detail.getGroupPrice());
            if (skuPriceChangeApplyDto.getGroupPrice() != null && skuPriceChangeApplyDto.getLastGroupPrice() != null) {
                skuPriceChangeApplyDto.setGroupPriceChange(skuPriceChangeApplyDto.getGroupPrice().compareTo(skuPriceChangeApplyDto.getLastGroupPrice()) != 0);
            }

            skuPriceChangeApplyDto.setLastElectronicCommercePrice(detail.getElectronicCommercePrice());
            if (skuPriceChangeApplyDto.getElectronicCommercePrice() != null && skuPriceChangeApplyDto.getLastElectronicCommercePrice() != null) {
                skuPriceChangeApplyDto.setElectronicCommercePriceChange(skuPriceChangeApplyDto.getElectronicCommercePrice().compareTo(skuPriceChangeApplyDto.getLastElectronicCommercePrice()) != 0);
            }
            skuPriceChangeApplyDto.setLastResearchTerminalPrice(detail.getResearchTerminalPrice());
            if (skuPriceChangeApplyDto.getResearchTerminalPrice() != null && skuPriceChangeApplyDto.getLastResearchTerminalPrice() != null) {
                skuPriceChangeApplyDto.setResearchTerminalPriceChange(skuPriceChangeApplyDto.getResearchTerminalPrice().compareTo(skuPriceChangeApplyDto.getLastResearchTerminalPrice()) != 0);
            }

        }
    }

    /**
     * 判断当前已生效的采购列表是否包含 本次采购变更申请的供应商
     *
     * @return
     */
    private boolean effectPurchaseListContainsPurchaseChange(List<SkuPriceInfoPurchaseDetailDto> effectPurchaseList, Integer traderId) {
        if (CollectionUtils.isEmpty(effectPurchaseList)) {
            return false;
        }

        SkuPriceInfoPurchaseDetailDto effectPrice = effectPurchaseList.stream()
                .filter(purchase -> {
                    return purchase.getTraderId().equals(traderId);
                }).findFirst()
                .orElse(null);

        return effectPrice == null ? false : true;

    }

    /**
     * 处理采购价信息
     *
     * @param skuPriceChangeApplyDto
     */
    private void dealWithPurchasePriceInfo(SkuPriceChangeApplyDto skuPriceChangeApplyDto) {

        for (SkuPriceChangePurchaseInfoDto purchaseInfoDto : skuPriceChangeApplyDto.getPurchaseInfoList()) {

            //没有生效的采购价 说明第一次维护
            if (CollectionUtils.isEmpty(skuPriceChangeApplyDto.getEffectPurchaseList())) {
                purchaseInfoDto.setPurchasePriceChange(false);
                continue;
            }

            //已有的生效的采购价 不包含本次变更的采购价 说明是第一次新增
            if (!effectPurchaseListContainsPurchaseChange(skuPriceChangeApplyDto.getEffectPurchaseList(), purchaseInfoDto.getTraderId())) {
                purchaseInfoDto.setPurchasePriceChange(false);
                continue;
            }

            //如果不是审核通过 显示上一次审核通过价格
            if (!VerifyStatusEnum.Approved.getName().equals(skuPriceChangeApplyDto.getVerifyStatusName())) {

                purchaseInfoDto.setLastPurchasePrice(
                        skuPriceChangeApplyDto.getEffectPurchaseList()
                                .stream()
                                .filter(purchaseDetail -> {
                                    //return purchaseDetail.getTraderName();
                                    return purchaseDetail.getTraderId().equals(purchaseInfoDto.getTraderId());
                                })
                                .findFirst()
                                .map(purchaseDetail -> purchaseDetail.getPurchasePrice())
                                .orElse(BigDecimal.ZERO)
                );

                //取倒数第二次审核通过的价格
            } else {

                if (CollectionUtils.isNotEmpty(skuPriceChangeApplyDto.getPurchaseInfoHistoryList())) {
                    List<SkuPriceInfoPurchaseHistoryDto> purchaseHistoryList = skuPriceChangeApplyDto.getPurchaseInfoHistoryList()
                            .stream()
                            .filter(purchaseDetail -> {
                                return purchaseDetail.getTraderId().intValue() == purchaseInfoDto.getTraderId();
                            })
                            .sorted(Comparator.comparing(SkuPriceInfoPurchaseHistoryDto::getAddTime).reversed())
                            .collect(Collectors.toList());

                    //获取倒数第二次的采购价
                    SkuPriceInfoPurchaseHistoryDto secondPurchasePrice = null;

                    if (CollectionUtils.isNotEmpty(purchaseHistoryList) && purchaseHistoryList.size() >= 2) {

                        secondPurchasePrice = purchaseHistoryList.get(1);
                    }

                    //没获取到 那就是第一次审核通过
                    if (secondPurchasePrice == null) {
                        purchaseInfoDto.setPurchasePriceChange(true);
                        purchaseInfoDto.setLastPurchasePrice(BigDecimal.ZERO);
                        continue;
                    }

                    purchaseInfoDto.setLastPurchasePrice(secondPurchasePrice.getPurchasePrice());
                }

            }

            if (Objects.nonNull(purchaseInfoDto.getPurchasePrice())&&Objects.nonNull(purchaseInfoDto.getLastPurchasePrice())) {
                purchaseInfoDto.setPurchasePriceChange(purchaseInfoDto.getPurchasePrice().compareTo(purchaseInfoDto.getLastPurchasePrice()) != 0);
            }


        }


    }


    private void setDefaultSalePrice(SkuPriceChangeApplyDto skuPriceChangeApplyDto) {
        skuPriceChangeApplyDto.setLastMarketPrice(BigDecimal.ZERO);
        skuPriceChangeApplyDto.setMarketPriceChange(true);
        skuPriceChangeApplyDto.setLastTerminalPrice(BigDecimal.ZERO);
        skuPriceChangeApplyDto.setTerminalPriceChange(true);
        skuPriceChangeApplyDto.setLastDistributionPrice(BigDecimal.ZERO);
        skuPriceChangeApplyDto.setDistributionPriceChange(true);
        skuPriceChangeApplyDto.setLastGroupPrice(BigDecimal.ZERO);
        skuPriceChangeApplyDto.setGroupPriceChange(true);
        skuPriceChangeApplyDto.setLastElectronicCommercePrice(BigDecimal.ZERO);
        skuPriceChangeApplyDto.setElectronicCommercePriceChange(true);
        skuPriceChangeApplyDto.setLastResearchTerminalPrice(BigDecimal.ZERO);
        skuPriceChangeApplyDto.setResearchTerminalPriceChange(true);
    }

    private boolean userBelongToRolelist(User user, List<String> roleNameList) {

        List<String> userRoleNameLists = user.getRoles().stream().map(Role::getRoleName).collect(Collectors.toList());

        for (String roleName : roleNameList) {
            if (userRoleNameLists.contains(roleName)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 历史轨迹图
     */
    @RequestMapping(value = "/purchasePriceTrace")
    public ModelAndView purchasePriceTrace(@Param("skuId") Integer skuId) {
        ModelAndView mv = new ModelAndView();
        CoreSkuGenerate skuInfo = coreSkuGenerateMapper.selectByPrimaryKey(skuId);
        mv.addObject("skuInfo", skuInfo);
        //查询sku已经生效的采购列表
        List<PurchaseHistoryDto> purchaseHistoryListDto = basePriceMaintainService.findPurchaseHistoryListBySkuId(skuId);
        List<String> supplyNameList = purchaseHistoryListDto.stream().map(purchaseHistory -> purchaseHistory.getSupplyName()).collect(Collectors.toList());
        //处理日期相关格式
        Set<String> addTimeSet = new HashSet<>();
        purchaseHistoryListDto.stream().forEach(purchaseHistory -> {
            purchaseHistory.getHistoryList().stream().forEach(history -> {

                String dateTime = DateUtil.formatStrDate(history.getAddTime(), DateUtil.TIME_FORMAT, DateUtil.DATE_FORMAT);
                history.setAddTime(dateTime);

                addTimeSet.add(dateTime);

            });
        });
        List addTimeList = new ArrayList(addTimeSet);
        Collections.sort(addTimeList);
        mv.addObject("purchaseHistoryListDto", purchaseHistoryListDto);
        mv.addObject("supplyNameList", supplyNameList);
        mv.addObject("addTimeList", addTimeList);
        mv.setViewName("price/purchasePriceTrace");
        return mv;
    }


    /**
     * 审核结果页面
     *
     * @param taskId
     * @param pass
     * @param skuPriceChangeApplyId
     * @return
     */
    @RequestMapping(value = "/auditResult")
    public ModelAndView complement(String taskId, Boolean pass, Integer skuPriceChangeApplyId) {
        ModelAndView mv = new ModelAndView();
        mv.addObject("taskId", taskId);
        mv.addObject("pass", pass);
        mv.addObject("skuPriceChangeApplyId", skuPriceChangeApplyId);
        SkuPriceChangeApplyDto skuPriceChangeApplyDto = this.basePriceMaintainService.findBasePriceMaintainById(skuPriceChangeApplyId);
        mv.addObject("lessThanMinGross", "false");
        //VDERP-5632:毛利率由取原先已生效的成本价调整为取当前提交的成本价进行计算
        if (CollectionUtils.isNotEmpty(skuPriceChangeApplyDto.getPurchaseInfoList())) {
            double avgPrice = skuPriceChangeApplyDto.getPurchaseInfoList()
                    .stream()
                    .mapToDouble(purchase -> purchase.getPurchasePrice().doubleValue())
                    .average()
                    .orElse(0);
            //原来的计算方法错了，给改一下
            BigDecimal maxCostTerminal = skuPriceChangeApplyDto.getTerminalPrice().multiply(new BigDecimal("0.92"));
            BigDecimal maxCostDistribution = skuPriceChangeApplyDto.getDistributionPrice().multiply(new BigDecimal("0.92"));
            boolean lessThanMinGross = false;
            if ((ObjectUtil.isNotNull(skuPriceChangeApplyDto.getResearchTerminalPrice()) && skuPriceChangeApplyDto.getResearchTerminalPrice().multiply(new BigDecimal("0.92")).compareTo(BigDecimal.valueOf(avgPrice))<0)
                || (ObjectUtil.isNotNull(skuPriceChangeApplyDto.getElectronicCommercePrice()) && skuPriceChangeApplyDto.getElectronicCommercePrice().multiply(new BigDecimal("0.92")).compareTo(BigDecimal.valueOf(avgPrice))<0)
                || maxCostTerminal.compareTo(BigDecimal.valueOf(avgPrice)) < 0
                || maxCostDistribution.compareTo(BigDecimal.valueOf(avgPrice)) < 0){
                lessThanMinGross = true;
            }
            mv.addObject("lessThanMinGross", lessThanMinGross);
        }

        //查看上架状态
        Integer onSaleStatus = this.coreSkuGenerateMapper.getSkuOnSale(skuPriceChangeApplyDto.getSkuNo());

        if (onSaleStatus != 0) {
            StringBuffer platStr = new StringBuffer();
            if ((onSaleStatus & GoodsConstants.ONE) == 1) {
                platStr.append("贝登/");
            }
            if ((onSaleStatus >>> GoodsConstants.ONE & GoodsConstants.ONE) == 1) {
                platStr.append("医械购/");
            }
            if ((onSaleStatus >>> GoodsConstants.TWO & GoodsConstants.ONE) == 1) {
                platStr.append("科研购/");
            }
            if ((onSaleStatus >>> GoodsConstants.THREE & GoodsConstants.ONE) == 1) {
                platStr.append("集采/");
            }
            if (StringUtils.isNotBlank(platStr.toString())) {
                mv.addObject("platStr", platStr.toString().substring(0, platStr.toString().lastIndexOf("/")));
            }
        }

        //是否已上架
        mv.addObject("isOnSale", onSaleStatus != 0 ? "true" : "false");

        mv.setViewName("price/auditResult");
        return mv;
    }


    /**
     * 价格变更审核操作
     */
    @SuppressWarnings({ "unchecked", "deprecation" })
	@ResponseBody
    @RequestMapping(value = "/complementTask")
    public ResultInfo<?> complementTask(HttpServletRequest request, Long skuPriceChangeApplyId, String taskId, String comment, Boolean pass,
                                        HttpSession session) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        // 审批操作
        try {
            Map<String, Object> taskVaribles = new HashMap<String, Object>();
            taskVaribles.put("pass", pass);
            //VDERP-11712：能够进入审批，说明走之前的逻辑，不走管理员审核
            taskVaribles.put("isAdmin", false);
            actionProcdefService.complementTask(request, taskId, comment,user.getUsername(), taskVaribles);
            //提交时已执行过该逻辑，此处如果非审核通过，则不需要执行
            if(pass) {
            	SkuPriceChangeApplyDto skuPriceChangeApplyDto = this.basePriceMaintainService.findBasePriceMaintainById(Integer.parseInt(String.valueOf(skuPriceChangeApplyId)));
            	List<Integer> skuIds = Arrays.asList(Integer.parseInt(String.valueOf(skuPriceChangeApplyDto.getSkuId())));
            	syncGoodsService.syncSkuInfo2EsBySkuIds(skuIds);
            }
            // 获取当前活动节点
            Task taskInfo = processEngine.getTaskService().createTaskQuery().processInstanceBusinessKey("priceChangeApply_" + skuPriceChangeApplyId).singleResult();
            List<Integer> userIdList = new ArrayList<>();
            if (taskInfo != null) {
                Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, "priceChangeApply_" + skuPriceChangeApplyId);
                Map<String, Object> candidateUserMap = (Map<String, Object>) historicInfo.get("candidateUserMap");
                //获取审核人候选组
                List<IdentityLink> candidateUserList = (List<IdentityLink>) candidateUserMap.get(taskInfo.getId());
                if (CollectionUtils.isNotEmpty(candidateUserList)) {
                    List<String> userNameList = candidateUserList.stream().map(identityLink -> identityLink.getUserId()).collect(Collectors.toList());
                    userNameList.stream().forEach(useName -> {
                        User userDb = this.userService.getByUsername(useName, 1);
                        userIdList.add(userDb.getUserId());
                    });
                }
                //更新策略变更的申请人
                this.basePriceMaintainService.updatePriceChangeAuditorByIds(Arrays.asList(skuPriceChangeApplyId), userIdList);
            }
            return new ResultInfo(0, "操作成功");
        } catch (Exception e) {
            logger.error("complementTask:" + skuPriceChangeApplyId, e);
            return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
        }
    }

    /**
     * 批量上传页面
     */
    @RequestMapping(value = "/batchUploadPage")
    public ModelAndView batchUploadPage(HttpServletRequest request) {
        ModelAndView mv = new ModelAndView();
        mv.setViewName("price/batchUpload");
        return mv;
    }

    /**
     * 批量上传
     *
     * @param request
     * @param lwfile
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/batchUpload")
    public ResultInfo<?> batchUpload(HttpServletRequest request,
                                     @RequestParam("lwfile") MultipartFile lwfile) {
        ResultInfo<?> resultInfo = new ResultInfo<>();
        try {
            //处理文件
            List<PriceInfoUploadDto> priceInfoUploadDtoList = dealwithFile(request, lwfile);
            //批量上传
            List<PriceInfoUploadResponseDto> priceInfoUploadResponseDto = basePriceMaintainService.batchAddPriceInfo(priceInfoUploadDtoList);
            //批量处理上传信息
            batchDealwithPriceInfo(request, priceInfoUploadResponseDto);
            resultInfo.setCode(0);
            resultInfo.setMessage("批量导入成功");
        } catch (Exception e) {
            logger.error("batchUpload price file error", e);
            resultInfo.setMessage(e.getMessage());
        }
        return resultInfo;
    }

    /**
     * 批量处理核价信息
     */
    private void batchDealwithPriceInfo(HttpServletRequest request, List<PriceInfoUploadResponseDto> priceInfoUploadResponseList) throws Exception {
        //如果没有处理中的审核信息
        if (CollectionUtils.isEmpty(priceInfoUploadResponseList)) {
            return;
        }
        List<Long> skuPriceChangeIds = new ArrayList<>();
        for (PriceInfoUploadResponseDto priceInfoUploadResponseDto : priceInfoUploadResponseList) {
            skuPriceChangeIds.add(priceInfoUploadResponseDto.getSkuPriceChangeApplyId());
            //审核中的变更,删除现有的流程实例
            if (priceInfoUploadResponseDto.isVerifying()) {
                // 获取当前活动节点
                Task taskInfo = processEngine.getTaskService().createTaskQuery().processInstanceBusinessKey("priceChangeApply_" + priceInfoUploadResponseDto.getSkuPriceChangeApplyId()).singleResult();
                if (taskInfo != null) {
                    //删除流程实例
                    actionProcdefService.deleteProcessInstance(taskInfo.getId());
                }
            }
            EditBasePriceDto editBasePriceDto = new EditBasePriceDto();
            editBasePriceDto.setSkuPriceChangeApplyId(priceInfoUploadResponseDto.getSkuPriceChangeApplyId());
            editBasePriceDto.setSecondChangePrice(priceInfoUploadResponseDto.isSecondChangePrice());
            editBasePriceDto.setSkuNo(priceInfoUploadResponseDto.getSkuNo());
            startProcessInstance(request, editBasePriceDto, true);
        }
        List<Integer> userIds = roleService.getUserIdByRoleName("供应主管", 1);
        //更新策略变更的申请人
        this.basePriceMaintainService.updatePriceChangeAuditorByIds(skuPriceChangeIds, userIds);
        //批量更新商品的运费信息
        List<Integer> skuIds = priceInfoUploadResponseList.stream().map(temp->Integer.parseInt(String.valueOf(temp.getSkuId()))).collect(Collectors.toList());
    	syncGoodsService.syncSkuInfo2EsBySkuIds(skuIds);
    }

    /**
     * 处理文件
     *
     * @param request
     * @return
     * @throws Exception
     */
    private List<PriceInfoUploadDto> dealwithFile(HttpServletRequest request, MultipartFile lwfile) throws Exception {


        List<Row> rows = getRows(request, lwfile);
        if (CollectionUtils.isEmpty(rows)) {
            throw new Exception("上传文件不能为空");
        }

        PriceInfoUploadValidatorDto priceInfoUploadValidatorDto = new PriceInfoUploadValidatorDto();
        priceInfoUploadValidatorDto.setUser((User) request.getSession().getAttribute(ErpConst.CURR_USER));
        priceInfoUploadValidatorDto.setRows(rows);

        ValidatorChain validatorChain = ValidatorChainBuild.newBuild()
                .setSkuNoValidator(skuNoValidator)
                .setTraderNameValidator(this.traderNameValidator)
                .setPriceValidator(this.priceValidator)
                .create();

        ValidatorResult validatorResult = validatorChain.validator(priceInfoUploadValidatorDto);

        //检验不通过返回结果
        if (!validatorResult.getResult()) {
            throw new Exception(validatorResult.getMessage());
        }

        return processRow(rows, priceInfoUploadValidatorDto);
    }

    /**
     * 获取行结果
     *
     * @param rows
     * @return
     */
    private List<PriceInfoUploadDto> processRow(List<Row> rows, PriceInfoUploadValidatorDto priceInfoUploadValidatorDto) {


        List<PriceInfoUploadDto> priceInfoUploadDtoList = new ArrayList<>();

        Map<String, Long> traderNameAndIdMap = priceInfoUploadValidatorDto.getTraderNameAndIdMap();

        rows.forEach(row -> {

            PriceInfoUploadDto priceInfoUploadDto = new PriceInfoUploadDto();
            priceInfoUploadDtoList.add(priceInfoUploadDto);

            priceInfoUploadDto.setSkuNo(row.getCell(0).getStringCellValue());
            priceInfoUploadDto.setTraderName(row.getCell(1).getStringCellValue());
            priceInfoUploadDto.setTraderId(traderNameAndIdMap.get(row.getCell(1).getStringCellValue()));

            priceInfoUploadDto.setPurchasePrice(new BigDecimal(row.getCell(2).getStringCellValue()));
            priceInfoUploadDto.setMarketPrice(new BigDecimal(row.getCell(3).getStringCellValue()));
            priceInfoUploadDto.setTerminalPrice(new BigDecimal(row.getCell(4).getStringCellValue()));
            priceInfoUploadDto.setDistributionPrice(new BigDecimal(row.getCell(5).getStringCellValue()));

            if (row.getCell(6) != null && row.getCell(6).getCellType() != CellType.BLANK && !"".equals(row.getCell(6).getStringCellValue())) {
                priceInfoUploadDto.setGroupPrice(new BigDecimal(row.getCell(6).getStringCellValue()));
            }

            if (row.getCell(7) != null && row.getCell(7).getCellType() != CellType.BLANK && !"".equals(row.getCell(7).getStringCellValue())) {
                priceInfoUploadDto.setElectronicCommercePrice(new BigDecimal(row.getCell(7).getStringCellValue()));
            }

            if (row.getCell(8) != null && row.getCell(8).getCellType() != CellType.BLANK && !"".equals(row.getCell(8).getStringCellValue())) {
                priceInfoUploadDto.setResearchTerminalPrice(new BigDecimal(row.getCell(8).getStringCellValue()));
            }
        });

        return priceInfoUploadDtoList;
    }

    /**
     * 解析行
     *
     * @param request
     * @param lwfile
     * @return
     * @throws Exception
     */
    private List<Row> getRows(HttpServletRequest request, MultipartFile lwfile) throws Exception {

        // 临时文件存放地址
        String path = request.getSession().getServletContext().getRealPath("/upload/saleorder");
        FileInfo fileInfo = ftpUtilService.fileUploadServe(path, lwfile);
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);

        if (fileInfo.getCode() != 0) {
            throw new Exception("文件上传失败");
        }
        Workbook workbook = null;
        List<Row> rows = new ArrayList<>();
        FileInputStream inputStream = new FileInputStream(new File(fileInfo.getFilePath()));
        try {
            // 获取excel路径
            workbook = WorkbookFactory.create(inputStream);
            // 获取第一面sheet
            Sheet sheet = workbook.getSheetAt(0);
            // 起始行
            int startRowNum = sheet.getFirstRowNum() + 1;// 第一行标题
            int endRowNum = sheet.getLastRowNum();// 结束行

            for (int rowNum = startRowNum; rowNum <= endRowNum; rowNum++) {
                rows.add(sheet.getRow(rowNum));
            }
        } catch (Exception e) {
            logger.error("", e);
        } finally {
            try {
                inputStream.close();
            } catch (Exception e2) {
            }
            try {
                workbook.close();
            } catch (Exception e2) {
            }

        }
        return rows;
    }

    /**
     * 查看修改前的价格
     *
     * @param skuPriceChangeApplyId
     */
    @RequestMapping(value = "/viewEffectPrice")
    public ModelAndView viewEffectPrice(HttpServletRequest request, @Param("skuPriceChangeApplyId") Integer skuPriceChangeApplyId) {

        SkuPriceChangeApplyDto skuPriceChangeApplyDto = this.basePriceMaintainService.findBasePriceMaintainById(skuPriceChangeApplyId);

        if (CollectionUtils.isNotEmpty(skuPriceChangeApplyDto.getEffectSaleInfoList())) {
            SkuPriceInfoSaleDetailDto saleInfoDto = skuPriceChangeApplyDto.getEffectSaleInfoList().stream().findFirst().get();
            skuPriceChangeApplyDto.setTerminalPrice(saleInfoDto.getTerminalPrice());
            skuPriceChangeApplyDto.setDistributionPrice(saleInfoDto.getDistributionPrice());
            skuPriceChangeApplyDto.setMarketPrice(saleInfoDto.getMarketPrice());
            skuPriceChangeApplyDto.setGroupPrice(saleInfoDto.getGroupPrice());
        } else {
            skuPriceChangeApplyDto.setMarketPrice(null);
            skuPriceChangeApplyDto.setTerminalPrice(null);
            skuPriceChangeApplyDto.setDistributionPrice(null);
            skuPriceChangeApplyDto.setGroupPrice(null);
        }

        ModelAndView mv = new ModelAndView();

        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, "priceChangeApply_" + skuPriceChangeApplyId);
        mv.addObject("historicActivityInstance", historicInfo.get("historicActivityInstance"));

        mv.addObject("skuPriceChangeApplyDto", skuPriceChangeApplyDto);

        mv.setViewName("price/effectPrice");
        return mv;
    }

    @RequestMapping(value = "/toDisablePricePage")
    public ModelAndView toDisablePricePage(HttpServletRequest request,
                                           @Param("skuPriceChangeApplyId") Integer skuPriceChangeApplyId,
                                           @Param("verifyStatusName") String verifyStatusName,
                                           @Param("skuNo") String skuNo) {
        try {

            ModelAndView mv = new ModelAndView();

            verifyStatusName = URLDecoder.decode(URLDecoder.decode(verifyStatusName, "UTF-8"), "UTF-8").trim();

            mv.addObject("verifyStatus", VerifyStatusEnum.getValueByName(verifyStatusName));
            mv.addObject("skuPriceChangeApplyId", skuPriceChangeApplyId);
            mv.addObject("skuNo", skuNo);

            //查看上架状态
            Integer onSaleStatus = this.coreSkuGenerateMapper.getSkuOnSale(skuNo);
            if (onSaleStatus != 0) {
                StringBuffer platStr = new StringBuffer();
                if ((onSaleStatus & GoodsConstants.ONE) == 1) {
                    platStr.append("贝登,");
                }
                if ((onSaleStatus >>> GoodsConstants.ONE & GoodsConstants.ONE) == 1) {
                    platStr.append("医械购,");
                }
                if ((onSaleStatus >>> GoodsConstants.TWO & GoodsConstants.ONE) == 1) {
                    platStr.append("科研购,");
                }
                if ((onSaleStatus >>> GoodsConstants.THREE & GoodsConstants.ONE) == 1) {
                    platStr.append("集采,");
                }

                if (StringUtils.isNotBlank(platStr.toString())) {
                    mv.addObject("platStr", platStr.toString().substring(0, platStr.toString().lastIndexOf(",")));
                }
            }

            mv.addObject("onSaleStatus", onSaleStatus);

            mv.setViewName("price/disablePrice");
            return mv;

        } catch (Exception e) {
            logger.error("toDisablePricePage error:", e);
            return null;
        }
    }

    @RequestMapping(value = "/disablePrice")
    @ResponseBody
    public ResultInfo disablePrice(@Param("skuPriceChangeApplyId") Integer skuPriceChangeApplyId,
                                   @Param("disableReason") String disableReason,
                                   @Param("verifyStatus") Integer verifyStatus,
                                   @Param("skuNo") String skuNo) {
        try {
            this.basePriceMaintainService.disablePrice(skuPriceChangeApplyId, disableReason);
            //如果是审核中,就删除现在的流程实例，同时将变更申请的状态改为待完善
            if (VerifyStatusEnum.Reviewing.getValue() == verifyStatus) {
                basePriceMaintainService.updatePriceChangeStatus(skuPriceChangeApplyId, VerifyStatusEnum.Reject.getValue());
                coreSkuGenerateMapper.updateSkuPriceVerifyStatusBySkuNo(skuNo, VerifyStatusEnum.Reject.getValue());
                // 获取当前活动节点
                Task taskInfo = processEngine.getTaskService()
                        .createTaskQuery()
                        .processInstanceBusinessKey("priceChangeApply_" + skuPriceChangeApplyId)
                        .singleResult();
                if (taskInfo != null) {
                    //删除流程实例
                    actionProcdefService.deleteProcessInstance(taskInfo.getId());
                }
            }
            return new ResultInfo(0, "成功");

        } catch (Exception e) {
            logger.error("disablePrice", e);
            return new ResultInfo(-1, "失败");
        }
    }

    @RequestMapping(value = "/enablePrice")
    @ResponseBody
    public ResultInfo enablePrice(@Param("skuPriceChangeApplyId") Integer skuPriceChangeApplyId) {

        try {
            this.basePriceMaintainService.enablePrice(skuPriceChangeApplyId);
            return new ResultInfo(0, "成功");

        } catch (Exception e) {
            logger.error("disablePrice", e);
            return new ResultInfo(-1, "失败");
        }
    }

    @RequestMapping(value = "/skuIsShelfOnYXG")
    @ResponseBody
    public ResultInfo skuIsShelfOnYXG(@Param("skuNo") String skuNo) {

        try {
            //请求医械购的接口 暂时还没提供 先留空
            Integer onSaleStatus = this.coreSkuGenerateMapper.getSkuOnSale(skuNo);
            if (onSaleStatus != 0 && (onSaleStatus >>> GoodsConstants.ONE & GoodsConstants.ONE) == 1) {
                return new ResultInfo(0, "该商品已在医械购上架，无法禁用。");
            }
            return new ResultInfo(-1, "成功");

        } catch (Exception e) {
            logger.error("skuIsShelfOnYXG", e);
            return new ResultInfo(-1, "失败");
        }
    }
    
    /**
     * 切换商城可见性
     * @param skuPriceChangeApplyId 价格变更申请ID
     * @param isVisible 可见状态（1-可见，0-不可见）
     * @return 操作结果
     */
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/toggleVdVisibility")
    @ResponseBody
    public ResultInfo toggleVdVisibility(@Param("skuPriceChangeApplyId") Integer skuPriceChangeApplyId,
                                        @Param("isVdVisible") Integer isVdVisible) {
    	LOGGER.info("切换商城可见性,入参：{}，{}",skuPriceChangeApplyId,isVdVisible);
        try {
            // 更新商城可见状态
            this.basePriceMaintainService.updateVdVisibility(skuPriceChangeApplyId, isVdVisible);
            return new ResultInfo(0, "商城可见状态更新成功");
        } catch (Exception e) {
        	LOGGER.error("toggleVdVisibility error:", e);
            return new ResultInfo(-1, "更新商城可见状态失败: " + e.getMessage());
        }
    }

}
