<%--
  Created by IntelliJ IDEA.
  User: lecon
  Date: 2020-08-27
  Time: 18:20
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<div class="form-item">
    <div class="form-label">
        <c:if test="${fromViewType eq 'sku'}">
            <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
        </c:if>
        <span class="must">*</span>存储条件(温度)：
    </div>
    <div class="form-fields">
        <div class="input-radio">
            <label class="input-wrap">
                <input type="radio" name="storageConditionTemperature" lay-filter="storageConditionTemperature"  <c:if test="${goodsStorageConditionVo.storageConditionTemperature eq 1}">checked</c:if>
                       value='1'>
                <span class="input-ctnr"></span>常温0ºC-30ºC
            </label>
            <label class="input-wrap">
                <input type="radio" name="storageConditionTemperature" lay-filter="storageConditionTemperature"
                       <c:if test="${goodsStorageConditionVo.storageConditionTemperature eq 2}">checked</c:if>
                       value='2'>
                <span class="input-ctnr"></span>阴凉0ºC-20ºC
            </label>
            <label class="input-wrap">
                <input type="radio" name="storageConditionTemperature" lay-filter="storageConditionTemperature"
                       <c:if test="${goodsStorageConditionVo.storageConditionTemperature eq 3}">checked</c:if>
                       value='3'>
                <span class="input-ctnr"></span>冷藏2ºC-10ºC
            </label>
            <label class="input-wrap">
                <c:choose>
                    <c:when test="${goodsStorageConditionVo.storageConditionTemperature eq 4}">
                        <input type="radio" lay-filter="storageConditionTemperature" name="storageConditionTemperature" checked value='4'>
                        <span class="input-ctnr"></span>其他温度
                        <input type="text" id="lessTemperature" name="storageConditionTemperatureLowerValue"
                               class="input-text"
                               value='<fmt:formatNumber value="${goodsStorageConditionVo.storageConditionTemperatureLowerValue}" type="number"/>'>&nbsp;ºC
                        <span class="input-ctnr"></span>&nbsp;-&nbsp;
                        <input type="text" id="greatTemperature" name="storageConditionTemperatureUpperValue"
                               class="input-text"
                               value='<fmt:formatNumber value="${goodsStorageConditionVo.storageConditionTemperatureUpperValue}" type="number"/>'>&nbsp;ºC
                    </c:when>
                    <c:otherwise>
                        <input type="radio" lay-filter="storageConditionTemperature"  name="storageConditionTemperature" value='4'>
                        <span class="input-ctnr"></span>其他温度
                        <input type="text" id="lessTemperature" name="storageConditionTemperatureLowerValue"
                               class="input-text" disabled="disabled">&nbsp;ºC
                        <span class="input-ctnr"></span>&nbsp;-&nbsp;
                        <input type="text" id="greatTemperature" name="storageConditionTemperatureUpperValue"
                               class="input-text" disabled="disabled">&nbsp;ºC
                    </c:otherwise>
                </c:choose>
            </label>
        </div>
        <div class="feedback-block" wrapfor="storageConditionTemperature"></div>
    </div>
    <div class="form-fields">
        <span class="form-block-title-tip" style="margin-left: 0px;color: rgb(153, 153, 153);">--请按照注册证/备案信息或产品说明书或标签上注明的存储条件进行填写; </span>
    </div>
</div>

<div class="form-item">
    <div class="form-label">
        <c:if test="${fromViewType eq 'sku'}">
            <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
        </c:if>
        <span class="must">*</span>存储条件(湿度)：
    </div>
    <div class="form-fields">
        <c:choose>
            <c:when test="${not empty goodsStorageConditionVo.storageConditionHumidityUpperValue}">
                <input type="text" name="storageConditionHumidityLowerValue" class="input-text"
                       value='<fmt:formatNumber value="${goodsStorageConditionVo.storageConditionHumidityLowerValue}" type="number"/>'>&nbsp;%
            </c:when>
            <c:otherwise>
                <input type="text" name="storageConditionHumidityLowerValue" class="input-text"
                       value="40">&nbsp;%
            </c:otherwise>
        </c:choose>
        <span class="input-ctnr"></span>&nbsp;-&nbsp;
        <c:choose>
            <c:when test="${not empty goodsStorageConditionVo.storageConditionHumidityUpperValue}">
                <input type="text" name="storageConditionHumidityUpperValue" class="input-text"
                       value='<fmt:formatNumber value="${goodsStorageConditionVo.storageConditionHumidityUpperValue}" type="number"/>'>&nbsp;%
            </c:when>
            <c:otherwise>
                <input type="text" name="storageConditionHumidityUpperValue" class="input-text"
                       value="80">&nbsp;%
            </c:otherwise>
        </c:choose>
        <div class="feedback-block" wrapfor="storageConditionHumidity"></div>
    </div>
    <div class="form-fields">
        <span class="form-block-title-tip" style="margin-left: 0px;color: rgb(153, 153, 153);">--常温库与阴凉库的相对湿度为40%-80%，特殊产品的湿度按产品说明书要求设置。</span>
    </div>
</div>

<div class="form-item">
    <div class="form-label">
        <c:if test="${fromViewType eq 'sku'}">
            <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
        </c:if>
        <span class="must">*</span>存储条件（其他）：
    </div>
    <div class="form-fields">
        <div class="input-checkbox">
            <c:if test="${not empty goodsStorageConditionVo.storageConditionOthersArray}">

                    <input type="checkbox"
                    <c:forEach var="condition" items="${goodsStorageConditionVo.storageConditionOthersArray}">
                           <c:if test="${1 eq condition}">checked</c:if>
                    </c:forEach>
                           name="storageConditionOthersArray" title="通风" value="1" lay-skin="primary">




                    <input type="checkbox"
                    <c:forEach var="condition" items="${goodsStorageConditionVo.storageConditionOthersArray}">
                           <c:if test="${condition eq 2}">checked</c:if>
                    </c:forEach>
                           name="storageConditionOthersArray" title="干燥" lay-skin="primary" value="2">



                    <input type="checkbox"
                    <c:forEach var="condition" items="${goodsStorageConditionVo.storageConditionOthersArray}">
                           <c:if test="${3 eq condition}">checked</c:if>
                    </c:forEach>
                           name="storageConditionOthersArray" value="3" title="避光" lay-skin="primary">


                    <input type="checkbox"
                    <c:forEach var="condition" items="${goodsStorageConditionVo.storageConditionOthersArray}">
                           <c:if test="${4 eq condition}">checked</c:if>
                    </c:forEach>
                           name="storageConditionOthersArray" value="4" title="防潮" lay-skin="primary">


                    <input type="checkbox"
                    <c:forEach var="condition" items="${goodsStorageConditionVo.storageConditionOthersArray}">
                           <c:if test="${5 eq condition}">checked</c:if>
                    </c:forEach>
                           name="storageConditionOthersArray" value="5" title="避热" lay-skin="primary">


                    <input type="checkbox"
                    <c:forEach var="condition" items="${goodsStorageConditionVo.storageConditionOthersArray}">
                           <c:if test="${6 eq condition}">checked</c:if>
                    </c:forEach>
                           name="storageConditionOthersArray" value="6"  title="密封" lay-skin="primary">

                    <input type="checkbox"
                    <c:forEach var="condition" items="${goodsStorageConditionVo.storageConditionOthersArray}">
                           <c:if test="${7 eq condition}">checked</c:if>
                    </c:forEach>
                           name="storageConditionOthersArray" value="7"  title="密闭" lay-skin="primary">


                    <input type="checkbox"
                    <c:forEach var="condition" items="${goodsStorageConditionVo.storageConditionOthersArray}">
                           <c:if test="${8 eq condition}">checked</c:if>
                    </c:forEach>
                           name="storageConditionOthersArray" value="8" title="严封" lay-skin="primary">


                    <input type="checkbox"
                    <c:forEach var="condition" items="${goodsStorageConditionVo.storageConditionOthersArray}">
                           <c:if test="${9 eq condition}">checked</c:if>
                    </c:forEach>
                           name="storageConditionOthersArray" value="9" title="遮光" lay-skin="primary" >
            </c:if>


            <c:if test="${empty goodsStorageConditionVo.storageConditionOthersArray}">

                    <input type="checkbox" name="storageConditionOthersArray" value="1" title="通风" lay-skin="primary">



                    <input type="checkbox" name="storageConditionOthersArray" value="2" title="干燥" lay-skin="primary">



                    <input type="checkbox" name="storageConditionOthersArray" value="3" title="避光" lay-skin="primary">



                    <input type="checkbox" name="storageConditionOthersArray" value="4" title="防潮" lay-skin="primary">



                    <input type="checkbox" name="storageConditionOthersArray" value="5" title="避热" lay-skin="primary">



                    <input type="checkbox" name="storageConditionOthersArray" value="6" title="密封" lay-skin="primary">



                    <input type="checkbox" name="storageConditionOthersArray" value="7" title="密闭" lay-skin="primary">



                    <input type="checkbox" name="storageConditionOthersArray" value="8" title="严封" lay-skin="primary">



                    <input type="checkbox" name="storageConditionOthersArray" value="9" title="遮光" lay-skin="primary">

            </c:if>
        </div>
        <div class="feedback-block" wrapfor="storageConditionOthersArray"></div>
    </div>
</div>

