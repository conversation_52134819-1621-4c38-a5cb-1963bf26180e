package com.vedeng.crm.feign.terminal;

import com.github.pagehelper.PageInfo;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.core.base.R;
import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.erp.saleorder.dto.OrderTerminalDto;
import com.vedeng.erp.saleorder.dto.TerminalQueryDto;
import com.vedeng.goods.dto.CategoryQueryDto;
import com.vedeng.goods.dto.CategoryResultDto;
import com.vedeng.infrastructure.tyc.domain.dto.TycResultDto;
import feign.Headers;
import feign.Param;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 终端服务-调用ERP接口
 */
@FeignApi(serverName = "erpServer")
public interface TerminalApiService {

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /api/terminal/search.do")
    R<PageInfo<OrderTerminalDto>> search(@RequestBody TerminalQueryDto terminalQueryDto);


    @RequestLine("GET /api/terminal/query.do?businessId={businessId}&businessType={businessType}")
    R<OrderTerminalDto> query(@Param("businessId") Integer businessId, @Param("businessType") Integer businessType);
}
