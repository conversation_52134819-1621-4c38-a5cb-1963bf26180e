package com.vedeng.api.standard.duplicate.constants;

/**
 * 幂等性HTTP头部字段常量定义
 * 定义标准的幂等性相关HTTP头部字段名称
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-15
 */
public final class IdempotencyHeaders {
    
    /**
     * 公司代码头部字段
     * 用于标识幂等性检查的公司范围
     */
    public static final String COMPANY_CODE = "x-idempotency-company-code";
    
    /**
     * 用户ID头部字段
     * 用于标识幂等性检查的用户范围
     */
    public static final String USER_ID = "x-idempotency-user-id";
    
    /**
     * 流程订单ID头部字段
     * 用于Temporal工作流的幂等性控制
     */
    public static final String FLOW_ORDER_ID = "x-idempotency-flow-order-id";
    
    /**
     * 自定义幂等性键头部字段
     * 允许调用方指定完全自定义的幂等性键
     */
    public static final String CUSTOM_KEY = "x-idempotency-key";
    
    /**
     * 业务类型头部字段
     * 用于标识幂等性检查的业务类型范围
     */
    public static final String BUSINESS_TYPE = "x-idempotency-business-type";
    
    /**
     * 幂等性策略头部字段
     * 指定使用的幂等性处理策略：strict, relaxed, custom
     */
    public static final String STRATEGY = "x-idempotency-strategy";
    
    /**
     * 私有构造函数，防止实例化
     */
    private IdempotencyHeaders() {
        throw new AssertionError("此类不应被实例化");
    }
}