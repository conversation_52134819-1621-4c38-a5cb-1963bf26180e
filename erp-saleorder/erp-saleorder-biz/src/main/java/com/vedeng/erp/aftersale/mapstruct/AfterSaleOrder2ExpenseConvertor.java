package com.vedeng.erp.aftersale.mapstruct;

import com.vedeng.erp.aftersale.domain.entity.AfterSalesEntity;
import com.vedeng.erp.aftersale.domain.entity.AfterSalesGoodsEntity;
import com.vedeng.erp.aftersale.dto.AfterSaleGoods2ExpenseDto;
import com.vedeng.erp.aftersale.dto.AfterSaleOrder2ExpenseDto;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface AfterSaleOrder2ExpenseConvertor {

    /**
     *
     * @param afterSalesGoodsEntity
     * @return AfterSaleGoods2ExpenseDto
     */
    AfterSaleGoods2ExpenseDto toDto(AfterSalesGoodsEntity afterSalesGoodsEntity);

    /**
     *
     * @param afterSalesEntity
     * @return AfterSaleOrder2ExpenseDto
     */
    AfterSaleOrder2ExpenseDto toDto(AfterSalesEntity afterSalesEntity);

    /**
     *
     * @param afterSalesGoodsEntitys
     * @return List<AfterSaleGoods2ExpenseDto>
     */
    List<AfterSaleGoods2ExpenseDto> toDto(List<AfterSalesGoodsEntity> afterSalesGoodsEntitys);

}
