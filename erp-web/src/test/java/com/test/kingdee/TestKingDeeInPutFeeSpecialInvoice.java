package com.test.kingdee;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.domain.command.KingDeeInPutFeeSpecialInvoiceCommand;
import com.vedeng.erp.kingdee.dto.InPutFeeSpecialInvoiceDetailDto;
import com.vedeng.erp.kingdee.dto.InPutFeeSpecialInvoiceDetailLinkDto;
import com.vedeng.erp.kingdee.dto.InPutFeeSpecialInvoiceDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeInPutFeeSpecialInvoiceMapper;
import com.vedeng.erp.kingdee.mapstruct.KingDeeInPutFeeSpecialInvoiceCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeInPutFeeSpecialInvoiceConvertor;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.RepoStatus;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;


/**
 * 金蝶  进项费用专用发票
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring.xml", "classpath:spring-mybatis.xml"})
public class TestKingDeeInPutFeeSpecialInvoice {

    @Autowired
    private KingDeeInPutFeeSpecialInvoiceCommandConvertor inPutFeeSpecialInvoiceCommandConvertor;

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private KingDeeInPutFeeSpecialInvoiceMapper inPutFeeSpecialInvoiceMapper;

    @Autowired
    private KingDeeInPutFeeSpecialInvoiceConvertor inPutFeeSpecialInvoiceConvertor;

    @Test
    public void kingDeOutPutFeePlainInvoice() {

        InPutFeeSpecialInvoiceDto dto = new InPutFeeSpecialInvoiceDto();
        dto.setFid("0");
        dto.setFQzokBddjtid("BD6655");
        dto.setFinvoiceno("BD8778");
        dto.setFQzokFpdm("FP12343");
        dto.setFinvoicedate("2022-11-09 00:00:00");
        dto.setFdate("2022-11-09 00:00:00");
        dto.setFcontactunittype("BD_Supplier");
        dto.setFsupplierid("VEN0006");
        dto.setFsettleorgid("101");
        dto.setFdocumentstatus("Z");
        dto.setFBillTypeID("FYZZSFP01_SYS");
        dto.setFRedBlue("0");

        List<InPutFeeSpecialInvoiceDetailDto> FPUREXPINVENTRY = new ArrayList<>();
        InPutFeeSpecialInvoiceDetailDto sonDto = new InPutFeeSpecialInvoiceDetailDto();
        sonDto.setFexpenseid("026");
        sonDto.setFQty("1");
        sonDto.setFunitprice(new BigDecimal(106));
        sonDto.setFtaxrate("13");
        sonDto.setFQzokBddjhid("44");
        sonDto.setFQzokYsddh("BD99988");
        sonDto.setFQzokGsywdh("BD0099");
        sonDto.setFQzokYwlx("ADD_E");
        sonDto.setFQzokBdsku("V444");
        sonDto.setFsourcetype("AP_Payable");

        List<InPutFeeSpecialInvoiceDetailLinkDto> FPUREXPINVENTRY_Link = new ArrayList<>();
        InPutFeeSpecialInvoiceDetailLinkDto inSon = new InPutFeeSpecialInvoiceDetailLinkDto();
        inSon.setFLinkId("0");
        inSon.setFpurexpinventryLinkFflowid("");
        inSon.setFpurexpinventryLinkFflowlineid("0");
        inSon.setFpurexpinventryLinkFruleid("IV_PayableToPUREXVATIN");
        inSon.setFpurexpinventryLinkFstableid("0");
        inSon.setFpurexpinventryLinkFstablename("T_AP_PAYABLEENTRY");
        inSon.setFpurexpinventryLinkFsbillid("100091");
        inSon.setFpurexpinventryLinkFsid("100096");
        inSon.setFpurexpinventryLinkFamountforDold("106");
        inSon.setFpurexpinventryLinkFamountforD("106");
        FPUREXPINVENTRY_Link.add(inSon);
        sonDto.setFPUREXPINVENTRY_Link(FPUREXPINVENTRY_Link);
        FPUREXPINVENTRY.add(sonDto);

        dto.setFPUREXPINVENTRY(FPUREXPINVENTRY);

        System.out.println("dto-->"+JSON.toJSON(dto));
        //转换为金蝶入参
        KingDeeInPutFeeSpecialInvoiceCommand command = inPutFeeSpecialInvoiceCommandConvertor.toCommand(dto);
        System.out.println("入参commad-->"+JSON.toJSON(command));
        //调用金蝶方法
        RepoStatus save = kingDeeBaseApi.save(new SaveExtCommand<>(command, dto.getFormId()));
        ArrayList<SuccessEntity> successEntities = save.getSuccessEntitys();

        System.out.println("金蝶返回-->"+JSON.toJSON(successEntities));

        if (CollUtil.isNotEmpty(successEntities)) {
            SuccessEntity successEntity = CollUtil.getFirst(successEntities);
            dto.setFid(successEntity.getId());
            //金蝶业务数据入表
            inPutFeeSpecialInvoiceMapper.insertSelective(inPutFeeSpecialInvoiceConvertor.toEntity(dto));
        System.out.println("finish-->");
        }
    }


    //调用记录- 202211-10 14.21
    //{"fID":"0","fSUPPLIERID":{"fNumber":"VEN0006"},"f_QZOK_FPDM":"FP12343","fPUREXPINVENTRY":[{"fEXPENSEID":{"fNumber":"026"},"fSOURCETYPE":"AP_Payable","f_QZOK_GSYWDH":"BD0099","f_QZOK_YSDDH":"BD99988","f_QZOK_YWLX":"ADD_E","fPUREXPINVENTRY_Link":[{"fPUREXPINVENTRY_Link_FSTableName":"T_AP_PAYABLEENTRY","fPUREXPINVENTRY_Link_FAMOUNTFOR_D":106,"fPUREXPINVENTRY_Link_FRuleId":"IV_PayableToPUREXVATIN","fPUREXPINVENTRY_Link_FSTableId":"0","fLinkId":"0","fPUREXPINVENTRY_Link_FAMOUNTFOR_DOLD":106,"fPUREXPINVENTRY_Link_FFlowId":"","fPUREXPINVENTRY_Link_FSBillId":"100091","fPUREXPINVENTRY_Link_FFlowLineId":"0","fPUREXPINVENTRY_Link_FSId":"100096"}],"fTAXRATE":"13","f_QZOK_BDSKU":"V444","fUNITPRICE":106,"fQty":"1","f_QZOK_BDDJHID":"44"}],"f_QZOK_BDDJTID":"BD6655","fINVOICENO":"BD8778","fSETTLEORGID":{"fNumber":"101"},"fDATE":"2022-11-09 00:00:00","fCONTACTUNITTYPE":"BD_Supplier","fPURCHASEORGID":{},"fDOCUMENTSTATUS":"Z","fINVOICEDATE":"2022-11-09 00:00:00","fRedBlue":"0","fBillTypeID":{"fNumber":"FYZZSFP01_SYS"}}

    //调用金蝶接口，返回值[{"Result":{"ResponseStatus":{"IsSuccess":true,"Errors":[],"SuccessEntitys":[{"Id":100034,"Number":"EXVATIN00000006","DIndex":0}],"SuccessMessages":[],"MsgCode":0},"Id":100034,"Number":"EXVATIN00000006","NeedReturnData":[{}]}}]
    //金蝶返回-->[{"number":"EXVATIN00000006","dIndex":0,"id":"100034"}]
}
