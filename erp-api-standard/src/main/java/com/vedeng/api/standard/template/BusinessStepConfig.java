package com.vedeng.api.standard.template;

import com.vedeng.api.standard.converter.ResponseMappingConfig;
import com.vedeng.api.standard.validation.ValidationRule;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * 业务步骤配置类
 * 封装业务执行过程中各个步骤的配置参数
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-27
 */
@Data
public class BusinessStepConfig {
    
    /**
     * 请求类型
     */
    private Class<?> requestType;
    
    /**
     * 响应类型
     */
    private Class<?> responseType;
    
    /**
     * 验证规则数组
     */
    @SuppressWarnings("rawtypes")
    private Class<? extends ValidationRule>[] validationRules;

    
    /**
     * Controller Bean 名称
     */
    private String controllerBeanName;

    /**
     * Controller 方法名
     */
    private String controllerMethodName;
    
    /**
     * 调用参数
     */
    private Map<String, Object> callParams;

    /**
     * 自定义参数类型数组（用于多参数Controller方法）
     */
    private Class<?>[] customParameterTypes;

    /**
     * 自定义参数值数组（用于多参数Controller方法）
     */
    private Object[] customParameterValues;
    
    /**
     * 响应映射配置
     */
    private ResponseMappingConfig responseConfig;
    
    /**
     * 是否跳过验证
     */
    private boolean skipValidation = false;
    
    
    /**
     * 验证上下文
     */
    private Map<String, Object> validationContext;
    
    /**
     * 幂等性上下文
     */
    private Map<String, Object> idempotencyContext;

    /**
     * 原始参数配置数组（用于延迟解析）
     */
    private ParameterConfig[] originalParameterConfigs;

    /**
     * 是否包含HttpServletRequest参数
     */
    private boolean includeHttpRequest = false;

    /**
     * 是否启用幂等性异常统一处理
     */
    private boolean idempotencyHandlingEnabled = false;

    /**
     * 业务类型名称（用于幂等性异常处理）
     */
    private String businessTypeName;

    /**
     * 幂等性ID字段名（用于从响应中提取文档ID）
     */
    private String idempotencyIdField = "id";

    /**
     * 幂等性编号字段名（用于从响应中提取文档编号）
     */
    private String idempotencyNoField = "documentNo";

    /**
     * 构造函数
     */
    public BusinessStepConfig() {
        this.callParams = new HashMap<>();
        this.validationContext = new HashMap<>();
        this.idempotencyContext = new HashMap<>();
    }
    
    /**
     * 创建创建操作的默认配置
     *
     * @return 创建操作配置
     */
    public static BusinessStepConfig forCreateOperation() {
        BusinessStepConfig config = new BusinessStepConfig();
        // 创建操作的默认配置
        config.skipValidation = false;
        return config;
    }
    
    /**
     * 创建更新操作的默认配置
     *
     * @return 更新操作配置
     */
    public static BusinessStepConfig forUpdateOperation() {
        BusinessStepConfig config = new BusinessStepConfig();
        // 更新操作的默认配置
        config.skipValidation = false;
        return config;
    }
    
    /**
     * 创建查询操作的默认配置
     *
     * @return 查询操作配置
     */
    public static BusinessStepConfig forQueryOperation() {
        BusinessStepConfig config = new BusinessStepConfig();
        // 查询操作的默认配置
        config.skipValidation = false;
        return config;
    }
    
    /**
     * 创建删除操作的默认配置
     *
     * @return 删除操作配置
     */
    public static BusinessStepConfig forDeleteOperation() {
        BusinessStepConfig config = new BusinessStepConfig();
        // 删除操作的默认配置
        config.skipValidation = false;
        return config;
    }
    
    /**
     * 获取幂等性上下文
     *
     * @return 幂等性上下文
     */
    public Map<String, Object> getIdempotencyContext() {
        return idempotencyContext;
    }

    /**
     * 设置幂等性上下文
     *
     * @param idempotencyContext 幂等性上下文
     */
    public void setIdempotencyContext(Map<String, Object> idempotencyContext) {
        this.idempotencyContext = idempotencyContext;
    }

}
