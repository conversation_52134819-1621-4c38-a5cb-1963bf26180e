package com.vedeng.erp.buyorder.domain.entity;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
    * 采购产品
    */
@Data
public class BuyorderGoods {
    private Integer buyorderGoodsId;

    /**
    * 采购订单ID
    */
    private Integer buyorderId;

    /**
    * 商品ID
    */
    private Integer goodsId;

    /**
    * 唯一编码
    */
    private String sku;

    /**
    * 商品名称
    */
    private String goodsName;

    /**
    * 品牌名称
    */
    private String brandName;

    /**
    * 商品型号
    */
    private String model;

    /**
    * 单位中文名
    */
    private String unitName;

    /**
    * 单价
    */
    private BigDecimal price;

    /**
    * 货币单位ID
    */
    private Integer currencyUnitId;

    /**
    * 数量
    */
    private Integer num;

    /**
    * 已到货数量
    */
    private Integer arrivalNum;

    /**
    * 预计供应商发货时间
    */
    private Long estimateDeliveryTime;

    /**
    * 预计到货时间
    */
    private Long estimateArrivalTime;

    /**
    * 确认收货人
    */
    private Integer arrivalUserId;

    /**
    * 收货状态0未收货 1部分收货 2全部收货
    */
    private Boolean arrivalStatus;

    /**
    * 收货时间
    */
    private Long arrivalTime;

    /**
    * 是否删除0否1是
    */
    private Boolean isDelete;

    /**
    * 采购备注
    */
    private String insideComments;

    /**
    * 货期
    */
    private String deliveryCycle;

    /**
    * 安调信息
    */
    private String installation;

    /**
    * 内部备注
    */
    private String comments;

    /**
    * 添加时间
    */
    private Long addTime;

    /**
    * 添加人
    */
    private Integer creator;

    /**
    * 最近一次编辑时间
    */
    private Long modTime;

    /**
    * 最近一次编辑人
    */
    private Integer updater;

    /**
    * 采购单关联业务更新时间
    */
    private Date updateDataTime;

    /**
    * 原始采购价
    */
    private BigDecimal originalPurchasePrice;

    /**
    * 采购价优惠原因
    */
    private String couponReason;

    /**
    * 售后金额
    */
    private BigDecimal afterReturnAmount;

    /**
    * 售后退货数量
    */
    private Integer afterReturnNum;

    /**
    * 实际录票金额
    */
    private BigDecimal realInvoiceAmount;

    /**
    * 实际录票数量
    */
    private BigDecimal realInvoiceNum;

    /**
    * GE合同编号
    */
    private String geContractNo;

    /**
    * GE销售合同编号
    */
    private String geSaleContractNo;

    /**
    * 产品经理或者助理是否审核 0-否 1-是
    */
    private Integer productAudit;

    /**
    * 首次记录的预计发货时间
    */
    private Long firstSendGoodsTime;

    /**
    * 首次记录的预计到货时间
    */
    private Long firstReceiveGoodsTime;

    /**
    * 预计发货时间
    */
    private Long sendGoodsTime;

    /**
    * 预计到货时间
    */
    private Long receiveGoodsTime;

    /**
    * 采购进程描述
    */
    private String processDescription;

    /**
    * 是否发送过创建单站内信 0否1是
    */
    private Boolean isSendCreateFlag;

    /**
    * 是否生成过催货预警任务
    */
    private Boolean alreadyExpeditingAlarm;

    /**
    * 添加产品归属人ID信息
    */
    private String productBelongIdInfo;

    /**
    * 添加产品归属人name信息
    */
    private String productBelongNameInfo;

    /**
    * 规格
    */
    private String spec;

    /**
    * 生产企业名称
    */
    private String manufacturerName;

    /**
    * 生产企业许可证号
    */
    private String registrationNumber;

    /**
    * 是否有授权，0否1是
    */
    private Integer isHaveAuth;

    /**
    * 是否赠品，0否1是
    */
    private Boolean isGift;

    /**
    * 采购赠品商品参考价
    */
    private BigDecimal referPrice;

    /**
     * 返利单价
     */
    private BigDecimal rebatePrice;

    /**
     * 返利总额
     */
    private BigDecimal rebateAmount;

    /**
     * 实际采购价
     */
    private BigDecimal actualPurchasePrice;
}