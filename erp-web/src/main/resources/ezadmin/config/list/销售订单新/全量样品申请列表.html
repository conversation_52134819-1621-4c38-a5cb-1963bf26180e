<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>样品申请列表</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="allSampleOrderList" datasource="erp-datasourcetarget" fixednumber="" fixednumberright="" append_column_url=""
      append_row_url="" empty_show="" firstcol="numbers">
<div id="appendHead"></div>
<div class="layui-fluid">
    <div class="layui-tab">
        <ul class="layui-tab-title" id="tab">
        </ul>
    </div>
    <form class="layui-form" id="search">
        <div class=" layui-inline ">
            <label class="layui-form-label">我的待办</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " name="IS_MY"
                        placeholder="全部" style=""   jdbctype="body" data="[{
                    &quot;V&quot;: &quot;是&quot;,
                    &quot;K&quot;: &quot;1&quot;
                }, {
                    &quot;V&quot;: &quot;全部&quot;,
                    &quot;K&quot;: &quot;&quot;
                }]" datatype="JSON" oper="EQ"></select>
                <object class=" layui-input list-search-item " type="hidden" name="IDS" placeholder style=""
                        alias="T.ID" jdbctype="INTEGER" data="" datatype="" oper="IN"></object>
            </div>
        </div>

        <div class=" layui-inline "><label class="layui-form-label">申请类型</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="xm-select" name="APPLY_TYPE" placeholder="" style="" alias="WOOE" jdbctype=""
                        data="select SYS_OPTION_DEFINITION_ID K,TITLE V
                            from T_SYS_OPTION_DEFINITION WHERE PARENT_ID=4220"
                        datatype="KVSQLCACHE" oper="IN"></object>
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">申请单号</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="ORDER_NO" placeholder style alias="T"
                       jdbctype data datatype oper="LIKE">
            </div>
        </div>

        <div class=" layui-inline "><label class="layui-form-label">创建人</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="21" name="CREATOR" placeholder="" style="" alias="T" jdbctype=""
                        data="SELECT
                            DISTINCT T.CREATOR K,
                            T.CREATOR V
                        FROM
                            T_WMS_OUTPUT_ORDER T
                        WHERE
                            T.TYPE = 5
                            AND T.IS_DELETE = 0;" datatype="KVSQLCACHE" oper=""></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">归属部门</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="belong_department" placeholder style alias="T"
                       jdbctype data datatype oper="LIKE">
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">申请客户</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="borrow_trader_name" placeholder style alias="T"
                       jdbctype data datatype oper="LIKE">
            </div>
        </div>

        <div class=" layui-inline "><label class="layui-form-label">创建时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" name="ADD_TIME" placeholder style=""
                        alias="T" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">审核状态</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="xm-select" name="VERIFY_STATUS" placeholder="" style="" alias="T" jdbctype="" data="[{&quot;V&quot;:&quot;待审核&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;已关闭&quot;,&quot;K&quot;:&quot;4&quot;}]" datatype="JSON" oper="IN"></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">出库状态</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="xm-select" name="out_status" placeholder="" style="" alias="T" jdbctype="" data="[{&quot;V&quot;:&quot;未出库&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分出库&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;已出库&quot;,&quot;K&quot;:&quot;2&quot;}]" datatype="JSON" oper="IN"></object>
            </div>
        </div>

    </form>
    <hr class="layui-border-blue">
    <div class="btn-group   bd-highlight" id="tableButton">
        <button type="table" url="/wms/sampleOut/toAddSampleOut.do" opentype="PARENT" windowname="新增申请" style
                class="layui-btn">新增申请
        </button>
    </div>
    <table id="table" class="layui-table" style=" width:100%">
        <thead>
        <tr id="column">
            <th type="rowbutton" id="rowbutton"></th>
            <th item_name="APPLY_TYPE_STR" name="APPLY_TYPE_STR" body="td-text" order="" url="" opentype="" windowname="" datatype=""
                data="" style="" head="th">申请类型
            </th>
            <th item_name="ORDER_NO" name="ORDER_NO" body="td-link" order=""
                url="/wms/sampleOut/detail.do?sampleOrderId=${ID}" opentype="PARENT" windowname="" datatype=""
                data="" style="" head="th">申请单号
            </th>
            <th item_name="CREATOR" name="CREATOR" body="td-text" order="" url="" opentype="" windowname="" datatype=""
                data="" style="" head="th">创建人
            </th>
            <th item_name="APPLYER_DEPARTMENT" name="APPLYER_DEPARTMENT" body="td-text" order="" url="" opentype=""
                windowname="" datatype="" data="" style="" head="th">归属部门
            </th>
            <th item_name="ADD_TIME" name="ADD_TIME" body="td-text" order="" url="" opentype="" windowname=""
                datatype="" data="" style="" head="th">创建时间
            </th>
            <th item_name="TRADER_NAME" name="TRADER_NAME" body="td-text" order="" url="" opentype="" windowname=""
                datatype="" data="" style="" head="th">申请客户
            </th>
            <th item_name="VERIFY_STATUS" name="VERIFY_STATUS" body="td-text" order="" url="" opentype="" windowname=""
                datatype="" data="" style="" head="th">审核状态
            </th>
              <th item_name="out_status" name="out_status" body="td-text" order="" url="" opentype="" windowname=""
                datatype="" data="" style="" head="th">出库状态
            </th>
            <th item_name="operate" name="operate" body="td-link" order=""
                url="/wms/sampleOut/detail.do?sampleOrderId=${ID}" opentype="PARENT" windowname="" datatype=""
                data="" style="" head="th">操作
            </th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td> express:</td>
            <td colspan="100"><pre id="express" class="layui-code" orderby="ORDER BY ADD_TIME DESC" groupby="GOODS_ID">StringBuilder sql=new StringBuilder();
sql.append("SELECT
                SD.TITLE APPLY_TYPE_STR,T.ID,T.ORDER_NO, T.CREATOR, T.belong_department APPLYER_DEPARTMENT,
                T.ADD_TIME, T.VERIFY_STATUS AS VERIFY_STATUS_CODE,
                CASE WHEN T.VERIFY_STATUS = 0 THEN '待审核' WHEN T.VERIFY_STATUS = 1 THEN '审核中' WHEN T.VERIFY_STATUS = 2 THEN '审核通过' WHEN T.VERIFY_STATUS = 3 THEN '审核不通过' WHEN T.VERIFY_STATUS = 4 THEN '已关闭' END AS VERIFY_STATUS,
                CASE WHEN T.out_status =0 THEN '未出库' WHEN T.out_status = 1 THEN '部分出库' WHEN T.out_status = 2 THEN '已出库' END AS out_status,
                T.borrow_trader_name TRADER_NAME, '查看审核' AS operate
       FROM T_WMS_OUTPUT_ORDER T
       LEFT JOIN T_WMS_OUTPUT_ORDER_EXTRA WOOE ON WOOE.WMS_OUTPUT_ORDER_ID = T.ID
       AND WOOE.IS_DELETE = 0
       LEFT JOIN T_SYS_OPTION_DEFINITION SD ON WOOE.APPLY_TYPE = SD.SYS_OPTION_DEFINITION_ID
       WHERE T.TYPE = 5 AND T.IS_DELETE = 0 ");

return search(sql);</pre>
            </td>
        </tr>
        <tr>
            <td> count:</td>
            <td colspan="100">
                <pre id="count" class="layui-code"></pre>
            </td>
        </tr>
        </tbody>
    </table>
</div>
<div id="appendFoot">
    <script>
        function submitHandle() {
            var status = $("[name='IS_MY']").val();
            $('input[name="IDS"][type="hidden"]').val('');
            if (status == "") {  //选择了全部
                return true;
            }
            $.ajax({
                url: '/wms/sampleOut/getOrderIdUnderReview.do',
                data: {'processDefinitionKey': 'sampleOutAudit'},
                type: 'post',
                async: false,
                dataType: "json",
                success: function (res) {
                    if (res.code == 0) {
                        if (res.data.length < 1) {
                            if (status == 1) { // 接口没有返回归属我的代办，此时筛选项选择 “是我的代办”，那么此时肯定是搜不到数据的
                                $('input[name="IDS"][type="hidden"]').val(0);
                            }
                        } else {
                            if (status == 1) { // 选择了是我的代办
                                $('input[name="IDS"][type="hidden"]').val(res.data.join(","));
                            }
                        }
                    }
                }
            });
            return true;
        }

        $(document).ready(function() {
            var currentUrl = window.location.href;
            if(currentUrl.endsWith("/ezadmin/list/list-allSampleOrderList")){
                $('select[name="IS_MY"]').val('1');
                $("#submitBtn").click();
            }
        });
    </script>

</div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    })
</script>
</body>
</html>