package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.LogDataKingDee;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface LogDataKingDeeMapper {
    int deleteByPrimaryKey(Long logDataId);

    int insert(LogDataKingDee record);

    int insertSelective(LogDataKingDee record);

    LogDataKingDee selectByPrimaryKey(Long logDataId);

    int updateByPrimaryKeySelective(LogDataKingDee record);

    int updateByPrimaryKey(LogDataKingDee record);

    int batchInsert(@Param("list") List<LogDataKingDee> list);

    //根据outInNo和goodsId和orderType查询
    LogDataKingDee selectByOutInNoAndGoodsIdAndOrderType(LogDataKingDee logDataKingDee);

    List<LogDataKingDee> findByOutInNoAndGoodsIdAndOrderType(@Param("outInNo")String outInNo,@Param("goodsId")Integer goodsId,@Param("orderType")String orderType);


}