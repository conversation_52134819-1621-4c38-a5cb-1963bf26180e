package com.vedeng.api.standard.adapter.buyorder.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 采购单通用响应DTO
 * 支持所有操作类型的响应：创建、更新、删除、查询、详情、审核等
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-27
 */
@Data
public class BuyOrderResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    // ========== 基础响应字段 ==========
    /**
     * 操作是否成功
     */
    private Boolean success;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 跳转URL
     */
    private String redirectUrl;

    // ========== 采购单基础信息 ==========
    /**
     * 采购单ID
     */
    private Integer buyOrderId;

    /**
     * 采购单编号
     */
    private String buyOrderNo;

    /**
     * 供应商ID
     */
    private Integer traderId;

    /**
     * 供应商名称
     */
    private String traderName;

    /**
     * 采购状态
     */
    private Integer status;

    /**
     * 状态名称
     */
    private String statusName;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 总数量
     */
    private Integer totalQuantity;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 备注
     */
    private String remark;

    // ========== 查询响应字段 ==========

    /**
     * 总记录数（查询时使用）
     */
    private Long total;

    /**
     * 页码（查询时使用）
     */
    private Integer pageNum;

    /**
     * 页大小（查询时使用）
     */
    private Integer pageSize;

    // ========== 详情响应字段 ==========
    /**
     * 商品列表（详情时使用）
     */
    private List<BuyOrderGoodsRequest> goodsList;

    // ========== 构造函数 ==========

    /**
     * 默认构造函数
     */
    public BuyOrderResponse() {
    }

    /**
     * 基础构造函数
     */
    public BuyOrderResponse(Boolean success, String message) {
        this.success = success;
        this.message = message;
    }

    // ========== 静态工厂方法 ==========

    /**
     * 创建成功响应
     */
    public static BuyOrderResponse success(String message) {
        return new BuyOrderResponse(true, message);
    }

    /**
     * 创建成功响应（带ID）
     */
    public static BuyOrderResponse success(Integer buyOrderId, String message) {
        BuyOrderResponse response = new BuyOrderResponse(true, message);
        response.setBuyOrderId(buyOrderId);
        return response;
    }

    /**
     * 创建失败响应
     */
    public static BuyOrderResponse failure(String message) {
        return new BuyOrderResponse(false, message);
    }


    /**
     * 创建详情成功响应
     */
    public static BuyOrderResponse detailSuccess(String message) {
        return new BuyOrderResponse(true, message);
    }


}
