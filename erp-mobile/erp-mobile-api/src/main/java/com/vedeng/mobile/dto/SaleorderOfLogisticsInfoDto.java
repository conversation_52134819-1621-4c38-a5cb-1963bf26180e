package com.vedeng.mobile.dto;

import lombok.Data;

import java.util.List;


/**
 * 销售订单对应的物流信息返回类
 */
@Data
public class SaleorderOfLogisticsInfoDto {

    /**
     * 快递id
     */
    private Integer expressId;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 物流公司名称
     */
    private String logisticsName;

    /**
     * 快递状态
     */
    private String expressStatus;

    /**
     * 销售商品信息
     */
    List<SaleorderListOfGoodsDto> saleOrderGoodsList;

    /**
     * 物流详细信息
     */
    List<LogisticsDetailDto> logisticsDetailList;

}
