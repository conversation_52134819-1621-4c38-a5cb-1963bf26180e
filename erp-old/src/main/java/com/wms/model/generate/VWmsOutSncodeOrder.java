package com.wms.model.generate;

import java.io.Serializable;
import java.util.Date;

/**
 * V_WMS_OUT_SNCODE_ORDER
 * <AUTHOR>
public class VWmsOutSncodeOrder implements Serializable {
    /**
     * 出库SN码追溯表id
     */
    private Integer outSncodeOrderId;

    /**
     * 关联订单id
     */
    private Integer orderId;

    /**
     * 关联订单商品id
     */
    private Integer orderGoodsId;

    /**
     * 业务类型 0销售出库 1销售售后出库 2采购售后出库 3借货出库 4报废出库 5调拔出库 6样品出库 7领用出库
     */
    private Integer operateType;

    /**
     * WMS上架交易号
     */
    private String wmsOrderNo;

    /**
     * SKU
     */
    private String sku;

    /**
     * 商品id
     */
    private Integer skuId;

    /**
     * 厂家SN码
     */
    private String snCode;

    /**
     * 贝登追溯码
     */
    private String serialNo;

    /**
     * 出库时间
     */
    private Date outTime;

    /**
     * 生产日期
     */
    private Date productDate;

    /**
     * 有效期至
     */
    private Date expirationDate;

    /**
     * 厂家批号
     */
    private String batchNumber;

    /**
     * 贝登批次码
     */
    private String vedengBatchNumer;

    /**
     * 备注
     */
    private String comments;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 更新时间
     */
    private Date modeTime;

    /**
     * 是否删除  0否  1是
     */
    private Integer isDelete;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 更新人
     */
    private Integer updater;

    private static final long serialVersionUID = 1L;

    public Integer getOutSncodeOrderId() {
        return outSncodeOrderId;
    }

    public void setOutSncodeOrderId(Integer outSncodeOrderId) {
        this.outSncodeOrderId = outSncodeOrderId;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Integer getOrderGoodsId() {
        return orderGoodsId;
    }

    public void setOrderGoodsId(Integer orderGoodsId) {
        this.orderGoodsId = orderGoodsId;
    }

    public Integer getOperateType() {
        return operateType;
    }

    public void setOperateType(Integer operateType) {
        this.operateType = operateType;
    }

    public String getWmsOrderNo() {
        return wmsOrderNo;
    }

    public void setWmsOrderNo(String wmsOrderNo) {
        this.wmsOrderNo = wmsOrderNo;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    public String getSnCode() {
        return snCode;
    }

    public void setSnCode(String snCode) {
        this.snCode = snCode;
    }

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    public Date getOutTime() {
        return outTime;
    }

    public void setOutTime(Date outTime) {
        this.outTime = outTime;
    }

    public Date getProductDate() {
        return productDate;
    }

    public void setProductDate(Date productDate) {
        this.productDate = productDate;
    }

    public Date getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(Date expirationDate) {
        this.expirationDate = expirationDate;
    }

    public String getBatchNumber() {
        return batchNumber;
    }

    public void setBatchNumber(String batchNumber) {
        this.batchNumber = batchNumber;
    }

    public String getVedengBatchNumer() {
        return vedengBatchNumer;
    }

    public void setVedengBatchNumer(String vedengBatchNumer) {
        this.vedengBatchNumer = vedengBatchNumer;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Date getModeTime() {
        return modeTime;
    }

    public void setModeTime(Date modeTime) {
        this.modeTime = modeTime;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer delete) {
        this.isDelete = delete;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Integer getUpdater() {
        return updater;
    }

    public void setUpdater(Integer updater) {
        this.updater = updater;
    }
}