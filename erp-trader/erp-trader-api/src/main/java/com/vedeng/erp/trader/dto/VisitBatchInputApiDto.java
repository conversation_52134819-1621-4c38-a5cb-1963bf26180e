package com.vedeng.erp.trader.dto;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class VisitBatchInputApiDto {
    //当前用户
    private Integer userId;

    /**
     * 计划拜访时间
     */
    private String planVisitDate;

    /**
     * 拜访目标 (A新客开发B商机跟进C老客客情维护D签约会员E产品推广，以逗号隔开)
     */
    private String visitTarget;

    /**
     * 所有的拜访单位列表
     */
    private List<VisitInputTraderDto> traderList;


    public static void main(String[] args) {
        List<VisitInputTraderDto> traderList = new ArrayList<>();
        VisitInputTraderDto dto1 = new VisitInputTraderDto();
        dto1.setCustomerFrom(1);//erp来源-客户
        dto1.setCustomerNature(465);
        dto1.setTraderId(1);
        dto1.setTraderCustomerId(1);
        dto1.setCustomerName("客户名称");
        dto1.setProvinceCode(123);
        dto1.setProvinceName("北京市");
        dto1.setCityCode(2);
        dto1.setCityName("");
        dto1.setVisitorId(1);
        traderList.add(dto1);

        VisitInputTraderDto dto2 = new VisitInputTraderDto();
        dto2.setCustomerFrom(2);//终端库
        dto2.setCustomerNature(466);
        dto2.setTraderId(null);//这个字段没值
        dto2.setTraderCustomerId(null);//这个字段没值
        dto2.setCustomerName("客户名称");
        dto2.setProvinceCode(123);
        dto2.setProvinceName("北京市");
        dto2.setCityCode(2);
        dto2.setCityName("");
        dto2.setVisitorId(1);
        traderList.add(dto2);

        VisitBatchInputApiDto visitBatchInputApiDto = new VisitBatchInputApiDto();
        visitBatchInputApiDto.setPlanVisitDate("2024-10-01");
        visitBatchInputApiDto.setUserId(1);
        visitBatchInputApiDto.setVisitTarget("A,E");
        visitBatchInputApiDto.setTraderList(traderList);

        System.out.println(JSONObject.toJSONString(visitBatchInputApiDto));



    }

}
