package com.vedeng.goods.manager.extension.handler;

import com.vedeng.goods.dao.CoreSkuGenerateMapper;
import com.vedeng.goods.manager.constants.GoodsValidConstants;
import com.vedeng.goods.model.CoreSkuGenerate;
import com.vedeng.goods.model.vo.SkuAuthorizationVo;
import com.vedeng.goods.service.SkuAuthorizationService;
import com.vedeng.todolist.service.ITodoInstance;
import com.vedeng.todolist.service.impl.MaintainDataReportInfo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Component
public class GoodsReportTodoHandler extends BaseGoodsTodoHandler {

    @Resource
    private MaintainDataReportInfo maintainDataReportInfo;
    @Resource
    private SkuAuthorizationService skuAuthorizationService;

    @Resource
    private CoreSkuGenerateMapper coreSkuGenerateMapper;

    @Override
    public int getServiceId() {
        return REPORT_SERVICE_ID;
    }

    @Override
    public ITodoInstance getITodoInstance() {
        return maintainDataReportInfo;
    }

    @Override
    public boolean isDone() {
        if (!preCondition()) {
            //未满足保存报备信息条件
            return true;
        }
        SkuAuthorizationVo skuAuthorizationQuery = skuAuthorizationService.getSkuAuthorizationInfoBySkuId(getGoodId());
        return skuAuthorizationQuery != null;
    }

    @Override
    protected boolean preCondition() {
        CoreSkuGenerate coreSkuGenerate = coreSkuGenerateMapper.selectByPrimaryKey(getGoodId());
        return coreSkuGenerate != null && Objects.equals(coreSkuGenerate.getIsNeedReport(), GoodsValidConstants.ENABLE)
                && Objects.equals(coreSkuGenerate.getIsAuthorized(), GoodsValidConstants.ENABLE);
    }

    @Override
    public void onHandler() {
        if (preCondition() && !isDone()) {
            addGoodsTodoRecord(getGoodId(), getSkuNo());
        }
    }

    @Override
    public void finish(Object param) {
        if (param instanceof List) {
            List<?> skuIdList = (List<?>) param;
            for (Object skuId : skuIdList) {
                if (skuId instanceof Integer) {
                    maintainDataReportInfo.finish((Integer) skuId);
                }
            }
        }
    }
}
