package com.vedeng.erp.settlement.enums;

/**
 * 交易方式
 *
 */
public enum CapitalTraderModeEnum {

    /**
     * 交易方式
     */
    ALI_PAY(520, "支付宝"),
    BANK_PAY(521, "银行"),
    WE_CHAT_PAY(522, "微信"),
    CASHE(523, "现金"),
    CREDIT_PAY(527, "信用支付"),
    BALANCE_PAY(528, "余额支付"),
    CREDIT_RETURN(529, "退还信用"),
    BALANCE_RETURN(530, "退还余额"),
    REBATE(10000, "返利"),
    ;

    private Integer code;

    private String desc;

    CapitalTraderModeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }}
