
async function submitInfo(){
    checkLogin();

    var array = [];
    let detailList = [];
    $.each($("input[name='count']").not(':disabled'),function (){
        var count = $(this).val();
        let AfterSalesInstallServiceRecordDetail = {
            "afterSalesServiceDetailId": $("#afterSalesServiceDetailId_" + count).val(),
            "snSelectType": $("#serialNumber_" + count).val() > 0 || 0,
            "serialNumber": $("#serialNumber_" + count).val() > 0 || '',
            "supplCode": $("#serialNumber_" + count).val() || ''
        }
        detailList.push(AfterSalesInstallServiceRecordDetail)
        array.push(count);
    });
    var checkTimes = $("#checkTimes").val();
    if(checkTimes == null || checkTimes == ''){
        layer.alert("本次验收时间不可为空");
        return false;
    }
    var checkType = $("#checkType").val();
    var recordId = $("#recordId").val();
    if(recordId.length > 30){
        layer.alert("录音id超过30个字符")
        return false;
    }

    var falg = true;
    for (let i = 0; i < array.length; i++) {
        var count = array[i];
        var checkTimes = $("#checkTimes").val();
        let deliveryTime = $("#deliveryTime_" + count).val();
        let arrivalTime = $("#arrivalTime_" + count).val();

        if (deliveryTime != null && deliveryTime != '' && deliveryTime != 0) {
            let date = new Date(checkTimes);
            if (date.getTime() < deliveryTime) {

                function backStop(ms) {
                    let callback = null;
                    layer.confirm("本次服务时间小于" + $("#sku_" + count).val() + "的出库时间,请确认", {icon: 3, title:'提示'}, function(index){
                        if(typeof callback == 'function')callback();

                        falg = true;
                        layer.close(index);
                    },function (index) {
                        if(typeof callback == 'function')callback();
                        falg = false;
                        layer.close(index);
                    });
                    return new Promise(resolve => callback = resolve);
                }
                await backStop();
            }
        }
        if (!falg) {
            return false;
        }

        if (arrivalTime != null && arrivalTime != '' && arrivalTime != 0) {
            let date = new Date(checkTimes);
            if (date.getTime() < arrivalTime) {

                function backStop(ms) {
                    let callback = null;
                    layer.confirm("本次服务时间小于" + $("#sku_" + count).val() + "的签收时间,请确认", {icon: 3, title:'提示'}, function(index){
                        if(typeof callback == 'function')callback();
                        falg = true;
                        layer.close(index);
                    },function (index) {
                        if(typeof callback == 'function')callback();
                        falg = false;
                        layer.close(index);
                    });
                    return new Promise(resolve => callback = resolve);
                }
                await backStop();
            }
        }
        if (!falg) {
            return false;
        }

    }
    if (!falg) {
        return false;
    }

    var checkConclusion = $("#checkConclusion").val();
    var afterSalesServiceId = $("#afterSalesServiceId").val();
    var fileNames = $("#fileNames").val();
    var fileUris = $("#fileUris").val();


    let dto = {
        afterSalesServiceId:afterSalesServiceId,
        checkDate: checkTimes,
        checkType: checkType,
        recordId: recordId,
        checkConclusion: checkConclusion,
        fileNames: fileNames,
        fileUris: fileUris
    }
    $.ajax({
        url:page_url+'/order/afterSalesCommon/saveEditServiceRecord.do',
        data: JSON.stringify(dto),
        type:"POST",
        contentType: "application/json",
        dataType : "json",
        async: false,
        success:function(data) {
            if(data.code==0){
                window.parent.location.reload();
            }else{
                layer.alert(data.message);
            }
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    })
}
