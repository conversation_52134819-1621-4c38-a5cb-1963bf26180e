package com.wms.dto;

import java.math.BigDecimal;
import java.util.List;

public class PutSaleOrderDto {
    private String warehouseId;
    //ERP单据号
    private String docNo;

    /*
      SO 销售订单
      PT 采购售后退货单
      SA 领用出库单
      TT 调拔出库单
      SB 报废出库单
      TR 秒杀活动移仓单
      SD 样品出库单
      SA  发货通知（三方仓）
      SC ERP售后移仓单
      PK 盘亏出库
    */
    private String orderType;
    //订单付款时间
    private String orderTime;
    //ERP预期发货时间   默认写入当天日期时间
    private String expectedShipmentTime1;

    private String expectedShipmentTime2;

    //部门名称
    private String soReferenceA;
    //制单员
    private String soReferenceB;
    //客户ID
    private String consigneeId;

    private String consigneeName;

    private String consigneeProvince;

    private String consigneeCity;

    private String consigneeDistrict;

    private String consigneeAddress1;

    private String consigneeContact;

    private String consigneeTel1;

    private String consigneeTel2;

    /**
     * 是否签回单
     */
    private String hedi01;

    /**
     * 目标逻辑仓
     */
    private String hedi02;

    //是否打印4联随货同行单
    private String  hedi03;
    //是否货齐发货
    private String hedi04;
    //客户是否首次交易
    private String hedi05;
    //报废处理方式
    private String hedi06;
    //发货类型 A 正常发货  B 等通知发货   C 多地址发货
    private String hedi07;

    // 是否账期账单 Y 是 N 否
    private String hedi08;
    //ERP订单备注信息
    private String notes;

    private String carrierId;

    private String carrierName;

    private List<PutSaleOrderGoodsDto> details;

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getHedi07() {
        return hedi07;
    }

    public void setHedi07(String hedi07) {
        this.hedi07 = hedi07;
    }

    public String getDocNo() {
        return docNo;
    }

    public void setDocNo(String docNo) {
        this.docNo = docNo;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(String orderTime) {
        this.orderTime = orderTime;
    }

    public String getExpectedShipmentTime1() {
        return expectedShipmentTime1;
    }

    public void setExpectedShipmentTime1(String expectedShipmentTime1) {
        this.expectedShipmentTime1 = expectedShipmentTime1;
    }

    public String getSoReferenceA() {
        return soReferenceA;
    }

    public void setSoReferenceA(String soReferenceA) {
        this.soReferenceA = soReferenceA;
    }

    public String getSoReferenceB() {
        return soReferenceB;
    }

    public void setSoReferenceB(String soReferenceB) {
        this.soReferenceB = soReferenceB;
    }

    public String getConsigneeId() {
        return consigneeId;
    }

    public void setConsigneeId(String consigneeId) {
        this.consigneeId = consigneeId;
    }

    public String getHedi03() {
        return hedi03;
    }

    public void setHedi03(String hedi03) {
        this.hedi03 = hedi03;
    }

    public String getHedi04() {
        return hedi04;
    }

    public void setHedi04(String hedi04) {
        this.hedi04 = hedi04;
    }

    public String getHedi05() {
        return hedi05;
    }

    public void setHedi05(String hedi05) {
        this.hedi05 = hedi05;
    }

    public String getHedi06() {
        return hedi06;
    }

    public void setHedi06(String hedi06) {
        this.hedi06 = hedi06;
    }

    public List<PutSaleOrderGoodsDto> getDetails() {
        return details;
    }

    public void setDetails(List<PutSaleOrderGoodsDto> details) {
        this.details = details;
    }

    public String getHedi01() {
        return hedi01;
    }

    public void setHedi01(String hedi01) {
        this.hedi01 = hedi01;
    }

    public String getHedi02() {
        return hedi02;
    }

    public void setHedi02(String hedi02) {
        this.hedi02 = hedi02;
    }

    public String getConsigneeName() {
        return consigneeName;
    }

    public void setConsigneeName(String consigneeName) {
        this.consigneeName = consigneeName;
    }

    public String getConsigneeProvince() {
        return consigneeProvince;
    }

    public void setConsigneeProvince(String consigneeProvince) {
        this.consigneeProvince = consigneeProvince;
    }

    public String getConsigneeCity() {
        return consigneeCity;
    }

    public void setConsigneeCity(String consigneeCity) {
        this.consigneeCity = consigneeCity;
    }

    public String getConsigneeDistrict() {
        return consigneeDistrict;
    }

    public void setConsigneeDistrict(String consigneeDistrict) {
        this.consigneeDistrict = consigneeDistrict;
    }

    public String getConsigneeAddress1() {
        return consigneeAddress1;
    }

    public void setConsigneeAddress1(String consigneeAddress1) {
        this.consigneeAddress1 = consigneeAddress1;
    }

    public String getConsigneeContact() {
        return consigneeContact;
    }

    public void setConsigneeContact(String consigneeContact) {
        this.consigneeContact = consigneeContact;
    }

    public String getConsigneeTel1() {
        return consigneeTel1;
    }

    public void setConsigneeTel1(String consigneeTel1) {
        this.consigneeTel1 = consigneeTel1;
    }

    public String getConsigneeTel2() {
        return consigneeTel2;
    }

    public void setConsigneeTel2(String consigneeTel2) {
        this.consigneeTel2 = consigneeTel2;
    }

    public String getExpectedShipmentTime2() {
        return expectedShipmentTime2;
    }

    public void setExpectedShipmentTime2(String expectedShipmentTime2) {
        this.expectedShipmentTime2 = expectedShipmentTime2;
    }

    public String getCarrierId() {
        return carrierId;
    }

    public void setCarrierId(String carrierId) {
        this.carrierId = carrierId;
    }

    public String getCarrierName() {
        return carrierName;
    }

    public void setCarrierName(String carrierName) {
        this.carrierName = carrierName;
    }

    public String getWarehouseId() {
        return warehouseId;
    }

    public String getHedi08() {
        return hedi08;
    }

    public void setHedi08(String hedi08) {
        this.hedi08 = hedi08;
    }

    public void setWarehouseId(String warehouseId) {
        this.warehouseId = warehouseId;
    }

    @Override
    public String toString() {
        return "PutSaleOrderDto{" +
                "warehouseId='" + warehouseId + '\'' +
                ", docNo='" + docNo + '\'' +
                ", orderType='" + orderType + '\'' +
                ", orderTime='" + orderTime + '\'' +
                ", expectedShipmentTime1='" + expectedShipmentTime1 + '\'' +
                ", expectedShipmentTime2='" + expectedShipmentTime2 + '\'' +
                ", soReferenceA='" + soReferenceA + '\'' +
                ", soReferenceB='" + soReferenceB + '\'' +
                ", consigneeId='" + consigneeId + '\'' +
                ", hedi01='" + hedi01 + '\'' +
                ", hedi02='" + hedi02 + '\'' +
                ", hedi03='" + hedi03 + '\'' +
                ", hedi04='" + hedi04 + '\'' +
                ", hedi05='" + hedi05 + '\'' +
                ", hedi06='" + hedi06 + '\'' +
                ", hedi07='" + hedi07 + '\'' +
                ", hedi08='" + hedi08 + '\'' +
                ", notes='" + notes + '\'' +
                ", carrierId='" + carrierId + '\'' +
                ", carrierName='" + carrierName + '\'' +
                ", details=" + details +
                '}';
    }
}
