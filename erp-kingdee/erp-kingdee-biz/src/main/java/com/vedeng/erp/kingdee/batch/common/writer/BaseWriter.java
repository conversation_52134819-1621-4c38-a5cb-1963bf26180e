package com.vedeng.erp.kingdee.batch.common.writer;

import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.annotation.BeforeStep;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.batch.item.ItemWriter;

import java.util.List;

/**
 * <AUTHOR>
 */
public abstract class BaseWriter<T> implements ItemWriter<T> {

	protected StepExecution stepExecution;

	@BeforeStep
	public void saveStepExecution(StepExecution stepExecution) {
		this.stepExecution = stepExecution;
	}

	@Override
	public void write(List<? extends T> items) throws Exception {

		JobParameters params = stepExecution.getJobParameters();
		ExecutionContext stepContext = stepExecution.getExecutionContext();

		for (T item : items) {
			doWrite(item, params, stepContext);
		}
	}

	public abstract void doWrite(T item, JobParameters params, ExecutionContext stepContext) throws Exception;

	/**
	 * STEP参数保存
	 * 
	 * @param  
	 */
	public void saveStepParameter(String key, String value) {
		this.stepExecution.getJobExecution().getExecutionContext().putString(key, value);
	}

	/**
	 * STEP参数保存
	 * 
	 * @param  
	 */
	public void saveStepParameter(String key, long value) {
		this.stepExecution.getJobExecution().getExecutionContext().putLong(key, value);
	}

	/**
	 * STEP参数保存
	 * 
	 * @param  
	 */
	public void saveStepParameter(String key, int value) {
		this.stepExecution.getJobExecution().getExecutionContext().putInt(key, value);
	}

	/**
	 * STEP参数保存
	 * 
	 * @param  
	 */
	public void saveStepParameter(String key, Object value) {
		this.stepExecution.getJobExecution().getExecutionContext().put(key, value);
	}

	/**
	 * STEP参数保存
	 * 
	 * @param  
	 */
	public void saveStepParameter(String key, List<Object> value) {
		this.stepExecution.getJobExecution().getExecutionContext().put(key, value);
	}

	/**
	 * STEP参数取得
	 * 
	 * @param  
	 */
	public Object getStepParameter(String key) {
		return this.stepExecution.getJobExecution().getExecutionContext().get(key);
	}

	/**
	 * STEP参数取得
	 * 
	 * @param  
	 */
	public String getStringStepParameter(String key) {
		return this.stepExecution.getJobExecution().getExecutionContext().getString(key);
	}

	/**
	 * STEP参数取得
	 * 
	 * @param  
	 */
	public long getLongStepParameter(String key) {
		return this.stepExecution.getJobExecution().getExecutionContext().getLong(key, 0);
	}

	/**
	 * STEP参数取得
	 * 
	 * @param  
	 */
	public int getIntStepParameter(String key) {
		return this.stepExecution.getJobExecution().getExecutionContext().getInt(key, 0);
	}

	/**
	 * STEP参数递增(+1)
	 *
	 * @param key
	 */
	public void addLongStepParameter(String key) {
		this.addLongStepParameter(key, 1);
	}

	/**
	 * STEP参数递增
	 *
	 * @param key
	 * @param cnt
	 */
	public void addLongStepParameter(String key, long cnt) {
		long value = this.getLongStepParameter(key);
		value = value + cnt;
		this.saveStepParameter(key, value);
	}

	/**
	 * STEP参数递增(+1)
	 *
	 * @param key
	 */
	public void addIntStepParameter(String key) {
		this.addIntStepParameter(key, 1);
	}

	/**
	 * STEP参数递增
	 *
	 * @param key
	 * @param cnt
	 */
	public void addIntStepParameter(String key, int cnt) {
		int value = this.getIntStepParameter(key);
		value = value + cnt;
		this.saveStepParameter(key, value);
	}

}
