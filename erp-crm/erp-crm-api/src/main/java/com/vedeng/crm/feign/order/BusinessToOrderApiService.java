package com.vedeng.crm.feign.order;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.R;
import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.erp.broadcast.StatisticDataDto;
import com.vedeng.erp.saleorder.dto.QuoteLinkOrderDto;
import com.vedeng.erp.saleorder.dto.QuoteToOrderDto;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import feign.Headers;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * 商机转订单服务-调用ERP接口
 */
@FeignApi(serverName = "erpServer")
public interface BusinessToOrderApiService {

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /api/quoteToOrder/convert.do")
    R<Integer> convert(@RequestBody QuoteToOrderDto quoteToOrderDto);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /api/quoteToOrder/link.do")
    R<?> link(@RequestBody QuoteLinkOrderDto quoteLinkOrderDto);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /api/quoteToOrder/getSaleOrder.do")
    R<SaleorderInfoDto> getSaleOrder(@RequestBody QuoteLinkOrderDto quoteLinkOrderDto);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /api/quoteToOrder/isExistSaleOrder.do")
    R<Integer> isExistSaleOrder(@RequestBody QuoteLinkOrderDto quoteLinkOrderDto);
    
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /api/broadCast/getStatisticData.do")
    R<StatisticDataDto> getBraodCastStatisticData();
    
}