package com.vedeng.erp.kingdee.batch.writer;

import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeeInternalProcurementDto;
import com.vedeng.erp.kingdee.service.KingDeeInternalProcurementApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 内部采销
 * <AUTHOR>
 */
@Service
@Slf4j
public class BatchInternalProcurementWriter extends BaseWriter<KingDeeInternalProcurementDto> {

    @Autowired
    private KingDeeInternalProcurementApiService kingDeeInternalProcurementApiService;

    @Override
    public void doWrite(KingDeeInternalProcurementDto internalProcurementDto, JobParameters params, ExecutionContext stepContext) throws Exception {

        internalProcurementDto.setKingDeeBizEnums(KingDeeBizEnums.saveInternalProcurement);
        kingDeeInternalProcurementApiService.register(internalProcurementDto, true);

    }
}
