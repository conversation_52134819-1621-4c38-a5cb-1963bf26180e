<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.repository.mappers.KingDeeExpressCostMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.domain.entity.KingDeeExpressCostEntity">
    <!--@mbg.generated-->
    <!--@Table KING_DEE_EXPRESS_COST-->
    <id column="KING_DEE_EXPRESS_COST_ID" jdbcType="INTEGER" property="kingDeeExpressCostId" />
    <result column="FID" jdbcType="VARCHAR" property="fid" />
    <result column="F_BILL_NO" jdbcType="VARCHAR" property="fBillNo" />
    <result column="F_QZOK_ORG_ID" jdbcType="VARCHAR" property="fQzokOrgId" />
    <result column="F_QZOK_YSDDH" jdbcType="VARCHAR" property="fQzokYsddh" />
    <result column="F_QZOK_GSYWDH" jdbcType="VARCHAR" property="fQzokGsywdh" />
    <result column="F_QZOK_YWLX" jdbcType="VARCHAR" property="fQzokYwlx" />
    <result column="F_QZOK_CRKDH" jdbcType="VARCHAR" property="fQzokCrkdh" />
    <result column="F_QZOK_KDDH" jdbcType="VARCHAR" property="fQzokKddh" />
    <result column="F_QZOK_WLBM" jdbcType="VARCHAR" property="fQzokWlbm" />
    <result column="F_QZOK_XLH" jdbcType="VARCHAR" property="fQzokXlh" />
    <result column="F_QZOK_PCH" jdbcType="VARCHAR" property="fQzokPch" />
    <result column="F_QZOK_FHSL" jdbcType="VARCHAR" property="fQzokFhsl" />
    <result column="F_QZOK_CB" jdbcType="VARCHAR" property="fQzokCb" />
    <result column="F_QZOK_SJR" jdbcType="VARCHAR" property="fQzokSjr" />
    <result column="F_QZOK_DH" jdbcType="VARCHAR" property="fQzokDh" />
    <result column="F_QZOK_DZ" jdbcType="VARCHAR" property="fQzokDz" />
    <result column="F_QZOK_BDDJBH" jdbcType="VARCHAR" property="fQzokBddjbh" />
    <result column="F_QZOK_SFSC" jdbcType="VARCHAR" property="fQzokSfsc" />
    <result column="F_QZOK_SFJRCB" jdbcType="VARCHAR" property="fQzokSfjrcb" />
    <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    KING_DEE_EXPRESS_COST_ID, FID, F_BILL_NO, F_QZOK_ORG_ID, F_QZOK_YSDDH, F_QZOK_GSYWDH, 
    F_QZOK_YWLX, F_QZOK_CRKDH, F_QZOK_KDDH, F_QZOK_WLBM, F_QZOK_XLH, F_QZOK_PCH, F_QZOK_FHSL, 
    F_QZOK_CB, F_QZOK_SJR, F_QZOK_DH, F_QZOK_DZ, F_QZOK_BDDJBH, F_QZOK_SFSC, F_QZOK_SFJRCB, 
    IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, UPDATE_REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from KING_DEE_EXPRESS_COST
    where KING_DEE_EXPRESS_COST_ID = #{kingDeeExpressCostId,jdbcType=INTEGER}
  </select>

  <select id="selectByBddjbh" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from KING_DEE_EXPRESS_COST
    where IS_DELETE = 0 and F_QZOK_BDDJBH = #{fQzokBddjbh,jdbcType=VARCHAR}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from KING_DEE_EXPRESS_COST
    where KING_DEE_EXPRESS_COST_ID = #{kingDeeExpressCostId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="KING_DEE_EXPRESS_COST_ID" keyProperty="kingDeeExpressCostId" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeExpressCostEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_EXPRESS_COST (FID, F_BILL_NO, F_QZOK_ORG_ID, 
      F_QZOK_YSDDH, F_QZOK_GSYWDH, F_QZOK_YWLX, 
      F_QZOK_CRKDH, F_QZOK_KDDH, F_QZOK_WLBM, 
      F_QZOK_XLH, F_QZOK_PCH, F_QZOK_FHSL, 
      F_QZOK_CB, F_QZOK_SJR, F_QZOK_DH, 
      F_QZOK_DZ, F_QZOK_BDDJBH, F_QZOK_SFSC, 
      F_QZOK_SFJRCB, IS_DELETE, ADD_TIME, 
      MOD_TIME, CREATOR, UPDATER, 
      CREATOR_NAME, UPDATER_NAME, UPDATE_REMARK
      )
    values (#{fid,jdbcType=VARCHAR}, #{fBillNo,jdbcType=VARCHAR}, #{fQzokOrgId,jdbcType=VARCHAR}, 
      #{fQzokYsddh,jdbcType=VARCHAR}, #{fQzokGsywdh,jdbcType=VARCHAR}, #{fQzokYwlx,jdbcType=VARCHAR}, 
      #{fQzokCrkdh,jdbcType=VARCHAR}, #{fQzokKddh,jdbcType=VARCHAR}, #{fQzokWlbm,jdbcType=VARCHAR}, 
      #{fQzokXlh,jdbcType=VARCHAR}, #{fQzokPch,jdbcType=VARCHAR}, #{fQzokFhsl,jdbcType=VARCHAR}, 
      #{fQzokCb,jdbcType=VARCHAR}, #{fQzokSjr,jdbcType=VARCHAR}, #{fQzokDh,jdbcType=VARCHAR}, 
      #{fQzokDz,jdbcType=VARCHAR}, #{fQzokBddjbh,jdbcType=VARCHAR}, #{fQzokSfsc,jdbcType=VARCHAR}, 
      #{fQzokSfjrcb,jdbcType=VARCHAR}, #{isDelete,jdbcType=BOOLEAN}, #{addTime,jdbcType=TIMESTAMP}, 
      #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, 
      #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, #{updateRemark,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="KING_DEE_EXPRESS_COST_ID" keyProperty="kingDeeExpressCostId" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeExpressCostEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_EXPRESS_COST
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="fid != null">
        FID,
      </if>
      <if test="fBillNo != null">
        F_BILL_NO,
      </if>
      <if test="fQzokOrgId != null">
        F_QZOK_ORG_ID,
      </if>
      <if test="fQzokYsddh != null">
        F_QZOK_YSDDH,
      </if>
      <if test="fQzokGsywdh != null">
        F_QZOK_GSYWDH,
      </if>
      <if test="fQzokYwlx != null">
        F_QZOK_YWLX,
      </if>
      <if test="fQzokCrkdh != null">
        F_QZOK_CRKDH,
      </if>
      <if test="fQzokKddh != null">
        F_QZOK_KDDH,
      </if>
      <if test="fQzokWlbm != null">
        F_QZOK_WLBM,
      </if>
      <if test="fQzokXlh != null">
        F_QZOK_XLH,
      </if>
      <if test="fQzokPch != null">
        F_QZOK_PCH,
      </if>
      <if test="fQzokFhsl != null">
        F_QZOK_FHSL,
      </if>
      <if test="fQzokCb != null">
        F_QZOK_CB,
      </if>
      <if test="fQzokSjr != null">
        F_QZOK_SJR,
      </if>
      <if test="fQzokDh != null">
        F_QZOK_DH,
      </if>
      <if test="fQzokDz != null">
        F_QZOK_DZ,
      </if>
      <if test="fQzokBddjbh != null">
        F_QZOK_BDDJBH,
      </if>
      <if test="fQzokSfsc != null">
        F_QZOK_SFSC,
      </if>
      <if test="fQzokSfjrcb != null">
        F_QZOK_SFJRCB,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="fid != null">
        #{fid,jdbcType=VARCHAR},
      </if>
      <if test="fBillNo != null">
        #{fBillNo,jdbcType=VARCHAR},
      </if>
      <if test="fQzokOrgId != null">
        #{fQzokOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYsddh != null">
        #{fQzokYsddh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokGsywdh != null">
        #{fQzokGsywdh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYwlx != null">
        #{fQzokYwlx,jdbcType=VARCHAR},
      </if>
      <if test="fQzokCrkdh != null">
        #{fQzokCrkdh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokKddh != null">
        #{fQzokKddh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokWlbm != null">
        #{fQzokWlbm,jdbcType=VARCHAR},
      </if>
      <if test="fQzokXlh != null">
        #{fQzokXlh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokPch != null">
        #{fQzokPch,jdbcType=VARCHAR},
      </if>
      <if test="fQzokFhsl != null">
        #{fQzokFhsl,jdbcType=VARCHAR},
      </if>
      <if test="fQzokCb != null">
        #{fQzokCb,jdbcType=VARCHAR},
      </if>
      <if test="fQzokSjr != null">
        #{fQzokSjr,jdbcType=VARCHAR},
      </if>
      <if test="fQzokDh != null">
        #{fQzokDh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokDz != null">
        #{fQzokDz,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBddjbh != null">
        #{fQzokBddjbh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokSfsc != null">
        #{fQzokSfsc,jdbcType=VARCHAR},
      </if>
      <if test="fQzokSfjrcb != null">
        #{fQzokSfjrcb,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeExpressCostEntity">
    <!--@mbg.generated-->
    update KING_DEE_EXPRESS_COST
    <set>
      <if test="fid != null">
        FID = #{fid,jdbcType=VARCHAR},
      </if>
      <if test="fBillNo != null">
        F_BILL_NO = #{fBillNo,jdbcType=VARCHAR},
      </if>
      <if test="fQzokOrgId != null">
        F_QZOK_ORG_ID = #{fQzokOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYsddh != null">
        F_QZOK_YSDDH = #{fQzokYsddh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokGsywdh != null">
        F_QZOK_GSYWDH = #{fQzokGsywdh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYwlx != null">
        F_QZOK_YWLX = #{fQzokYwlx,jdbcType=VARCHAR},
      </if>
      <if test="fQzokCrkdh != null">
        F_QZOK_CRKDH = #{fQzokCrkdh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokKddh != null">
        F_QZOK_KDDH = #{fQzokKddh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokWlbm != null">
        F_QZOK_WLBM = #{fQzokWlbm,jdbcType=VARCHAR},
      </if>
      <if test="fQzokXlh != null">
        F_QZOK_XLH = #{fQzokXlh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokPch != null">
        F_QZOK_PCH = #{fQzokPch,jdbcType=VARCHAR},
      </if>
      <if test="fQzokFhsl != null">
        F_QZOK_FHSL = #{fQzokFhsl,jdbcType=VARCHAR},
      </if>
      <if test="fQzokCb != null">
        F_QZOK_CB = #{fQzokCb,jdbcType=VARCHAR},
      </if>
      <if test="fQzokSjr != null">
        F_QZOK_SJR = #{fQzokSjr,jdbcType=VARCHAR},
      </if>
      <if test="fQzokDh != null">
        F_QZOK_DH = #{fQzokDh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokDz != null">
        F_QZOK_DZ = #{fQzokDz,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBddjbh != null">
        F_QZOK_BDDJBH = #{fQzokBddjbh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokSfsc != null">
        F_QZOK_SFSC = #{fQzokSfsc,jdbcType=VARCHAR},
      </if>
      <if test="fQzokSfjrcb != null">
        F_QZOK_SFJRCB = #{fQzokSfjrcb,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where KING_DEE_EXPRESS_COST_ID = #{kingDeeExpressCostId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeExpressCostEntity">
    <!--@mbg.generated-->
    update KING_DEE_EXPRESS_COST
    set FID = #{fid,jdbcType=VARCHAR},
      F_BILL_NO = #{fBillNo,jdbcType=VARCHAR},
      F_QZOK_ORG_ID = #{fQzokOrgId,jdbcType=VARCHAR},
      F_QZOK_YSDDH = #{fQzokYsddh,jdbcType=VARCHAR},
      F_QZOK_GSYWDH = #{fQzokGsywdh,jdbcType=VARCHAR},
      F_QZOK_YWLX = #{fQzokYwlx,jdbcType=VARCHAR},
      F_QZOK_CRKDH = #{fQzokCrkdh,jdbcType=VARCHAR},
      F_QZOK_KDDH = #{fQzokKddh,jdbcType=VARCHAR},
      F_QZOK_WLBM = #{fQzokWlbm,jdbcType=VARCHAR},
      F_QZOK_XLH = #{fQzokXlh,jdbcType=VARCHAR},
      F_QZOK_PCH = #{fQzokPch,jdbcType=VARCHAR},
      F_QZOK_FHSL = #{fQzokFhsl,jdbcType=VARCHAR},
      F_QZOK_CB = #{fQzokCb,jdbcType=VARCHAR},
      F_QZOK_SJR = #{fQzokSjr,jdbcType=VARCHAR},
      F_QZOK_DH = #{fQzokDh,jdbcType=VARCHAR},
      F_QZOK_DZ = #{fQzokDz,jdbcType=VARCHAR},
      F_QZOK_BDDJBH = #{fQzokBddjbh,jdbcType=VARCHAR},
      F_QZOK_SFSC = #{fQzokSfsc,jdbcType=VARCHAR},
      F_QZOK_SFJRCB = #{fQzokSfjrcb,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where KING_DEE_EXPRESS_COST_ID = #{kingDeeExpressCostId,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="KING_DEE_EXPRESS_COST_ID" keyProperty="kingDeeExpressCostId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_EXPRESS_COST
    (FID, F_BILL_NO, F_QZOK_ORG_ID, F_QZOK_YSDDH, F_QZOK_GSYWDH, F_QZOK_YWLX, F_QZOK_CRKDH, 
      F_QZOK_KDDH, F_QZOK_WLBM, F_QZOK_XLH, F_QZOK_PCH, F_QZOK_FHSL, F_QZOK_CB, F_QZOK_SJR, 
      F_QZOK_DH, F_QZOK_DZ, F_QZOK_BDDJBH, F_QZOK_SFSC, F_QZOK_SFJRCB, IS_DELETE, ADD_TIME, 
      MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, UPDATE_REMARK)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.fid,jdbcType=VARCHAR}, #{item.fBillNo,jdbcType=VARCHAR}, #{item.fQzokOrgId,jdbcType=VARCHAR}, 
        #{item.fQzokYsddh,jdbcType=VARCHAR}, #{item.fQzokGsywdh,jdbcType=VARCHAR}, #{item.fQzokYwlx,jdbcType=VARCHAR}, 
        #{item.fQzokCrkdh,jdbcType=VARCHAR}, #{item.fQzokKddh,jdbcType=VARCHAR}, #{item.fQzokWlbm,jdbcType=VARCHAR}, 
        #{item.fQzokXlh,jdbcType=VARCHAR}, #{item.fQzokPch,jdbcType=VARCHAR}, #{item.fQzokFhsl,jdbcType=VARCHAR}, 
        #{item.fQzokCb,jdbcType=VARCHAR}, #{item.fQzokSjr,jdbcType=VARCHAR}, #{item.fQzokDh,jdbcType=VARCHAR}, 
        #{item.fQzokDz,jdbcType=VARCHAR}, #{item.fQzokBddjbh,jdbcType=VARCHAR}, #{item.fQzokSfsc,jdbcType=VARCHAR}, 
        #{item.fQzokSfjrcb,jdbcType=VARCHAR}, #{item.isDelete,jdbcType=BOOLEAN}, #{item.addTime,jdbcType=TIMESTAMP}, 
        #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, #{item.updater,jdbcType=INTEGER}, 
        #{item.creatorName,jdbcType=VARCHAR}, #{item.updaterName,jdbcType=VARCHAR}, #{item.updateRemark,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
</mapper>