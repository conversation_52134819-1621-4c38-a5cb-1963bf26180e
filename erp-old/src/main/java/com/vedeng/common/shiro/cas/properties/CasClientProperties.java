package com.vedeng.common.shiro.cas.properties;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Data
public class CasClientProperties {

    @Value("#{ @environment['cas.client.prefixUrl'] ?: null }")
    private String prefixUrl;

    @Value("#{ @environment['cas.client.casLoginUrl'] ?: null }")
    private String casLoginUrl;

    @Value("#{ @environment['cas.client.callbackUrl'] ?: null }")
    private String callbackUrl;

    @Value("#{ @environment['cas.client.serviceUrl'] ?: null }")
    private String serviceUrl;

    @Value("#{ @environment['cas.sessionIdCookie.path'] ?: null }")
    private String sessionIdPath;
    @Value("#{ @environment['cas.sessionIdCookie.maxAge'] ?: null }")
    private Integer sessionIdMaxAge;
    @Value("#{ @environment['cas.sessionIdCookie.name'] ?: null }")
    private String sessionIdName;
}
