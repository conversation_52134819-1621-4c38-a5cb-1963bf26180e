package com.vedeng.kpi.replica.dao;

import com.vedeng.finance.model.CapitalBill;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date created in 2020/6/15 16:50
 */
@Named("capitalBillReplicaMapper")
public interface CapitalBillReplicaMapper {

    CapitalBill getLatestCapitalBillByOrder(String orderNo);

    CapitalBill getFirstCapitalBillByOrder(String orderNo);

    CapitalBill getCapitalBillById(Integer capitalBillId);

    BigDecimal getRealReceivedAmountOfSaleorderBeforeExecuteTime(@Param("saleorderNo") String saleorderNo, @Param("executeTime") Long executeTime);

    /**
     * 获取订单及其售后单的相关流水
     * @param saleorderNo
     * @return
     */
    List<CapitalBill> getAllRealCapitalBillOfSaleorder(String saleorderNo);

    List<CapitalBill> getCapitalBillListOfSaleorder(@Param("saleorderNo") String saleorderNo);

    Integer getCapitalBillCountOfSaleorderByTraderSubjectBeforeExecuteTime(@Param("saleorderNo") String saleorderNo, @Param("executeTime") Long executeTime);

    List<CapitalBill> getCapitalBillOfAfterSalesBySaleorderNo(@Param("saleorderNo") String saleorderNo, @Param("executeTime") Long executeTime);
}
