package com.vedeng.common.orderstrategy;

import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 *订单金额算法策略
 * <AUTHOR>
 * @date $
 */
@Component
public class OrderAmountStrategy extends OrderStrategy{
    public static Logger logger = LoggerFactory.getLogger(OrderAmountStrategy.class);

    @Autowired
    private SaleorderMapper saleorderMapper  ;
    @Autowired
    private SaleorderGoodsMapper saleorderGoodsMapper  ;

    @Override
    public void execute(Object object) {
        Saleorder saleorder = (Saleorder) object;
        Integer orderId=saleorder.getSaleorderId();

        try {
        //获取订单实际入账金额(不含账期)
        BigDecimal  paymentAmount = saleorderMapper.getPaymentAmount(orderId);
        if(paymentAmount == null){
            paymentAmount = BigDecimal.ZERO;
        }
        //售后退货信息
        List<SaleorderGoods> afterReturnsInfo = saleorderGoodsMapper.getAfterReturnInfo(orderId);
        //订单售后实际退款金额(包括退货实退金额+售后退款实退)
        BigDecimal  returnAmount =  saleorderMapper.getReturnAmount(orderId);
        if(paymentAmount == null){
            paymentAmount = BigDecimal.ZERO;
        }
        if(returnAmount == null){
            returnAmount = BigDecimal.ZERO;
        }
        //实付金额
        saleorder.setPaymentAmount(paymentAmount.subtract(returnAmount));
        saleorder.setEndAmount(returnAmount);
        BigDecimal totalAmount = saleorder.getTotalAmount();
        for (SaleorderGoods saleorderGoods : afterReturnsInfo) {
            if(saleorderGoods.getSaleorderGoodsId().equals(0)){
                continue;
            }
            totalAmount = totalAmount.subtract(saleorderGoods.getAfterReturnAmount());
            saleorderGoodsMapper.updateSaleOrderAfterAmountInfo(saleorderGoods);
        }
        saleorder.setTotalAmount(totalAmount);
        //订单账期未还金额
        BigDecimal noPayBackAmount = saleorderMapper.getNoPayBackAmount(orderId);
        if(noPayBackAmount == null){
            noPayBackAmount = BigDecimal.ZERO;
        }
        saleorder.setAccountPayable(noPayBackAmount);
        saleorderMapper.updateAmountInfo(saleorder);
        }catch (Exception e){
            logger.error("OrderAmountStrategy error orderId:"+orderId,e);
        }
    }
}
