package com.vedeng.erp.kingdee.service.impl;

import com.vedeng.erp.kingdee.domain.command.KingDeeAliReceiveBillCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeReceiveBillEntity;
import com.vedeng.erp.kingdee.dto.KingDeeAliReceiveBillDto;
import com.vedeng.erp.kingdee.service.KingDeeAliReceiveBillApiService;
import com.vedeng.erp.kingdee.service.KingDeeAliReceiveBillService;
import com.vedeng.erp.kingdee.repository.KingDeeAliReceiveBillRepository;
import com.vedeng.erp.kingdee.mapstruct.KingDeeAliReceiveBillCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeAliReceiveBillConvertor;
import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class KingDeeAliReceiveBillServiceImpl  extends KingDeeBaseServiceImpl<
        KingDeeReceiveBillEntity,
        KingDeeAliReceiveBillDto,
        KingDeeAliReceiveBillCommand,
        KingDeeAliReceiveBillRepository,
        KingDeeAliReceiveBillConvertor,
        KingDeeAliReceiveBillCommandConvertor
        >
        implements KingDeeAliReceiveBillService, KingDeeAliReceiveBillApiService {
}
