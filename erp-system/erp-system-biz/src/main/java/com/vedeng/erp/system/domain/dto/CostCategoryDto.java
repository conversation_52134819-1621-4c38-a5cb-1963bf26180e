package com.vedeng.erp.system.domain.dto;

import com.vedeng.common.core.base.BaseDto;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.buyorder.dto 商品费用类别
 * @Date 2022/8/11 13:09
 */
@Setter
@Getter
public class CostCategoryDto extends BaseDto {

    /**
     * 主键ID
     */
    private Integer costCategoryId;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 旧分类名称
     */
    private String oldCategoryName;

    /**
     * 金蝶费用类别编号
     */
    private String unitKingDeeNo;

    /**
     * 原金蝶费用类别名称
     */
    private String oldUnitKingDeeName;

    /**
     * 是否删除
     */
    private Boolean isDel;

    /**
     * 是否需采购
     */
    private Integer purchase;


}
