package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.kingdee.batch.dto.BatchBankBillDto;
import com.vedeng.erp.kingdee.batch.dto.BatchCapitalBillDto;
import com.vedeng.erp.kingdee.batch.dto.BatchCustomerFinanceDto;
import com.vedeng.erp.kingdee.batch.dto.BatchSupplierFinanceDto;
import com.vedeng.erp.kingdee.batch.repository.BatchBankBillDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchCapitalBillDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchCustomerFinanceDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchSupplierFinanceDtoMapper;
import com.vedeng.erp.kingdee.domain.entity.KingDeeCustomerEntity;
import com.vedeng.erp.kingdee.domain.entity.KingDeePayVedengBankDto;
import com.vedeng.erp.kingdee.domain.entity.KingDeeSupplierEntity;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveBillDto;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveBillEntryDto;
import com.vedeng.erp.kingdee.enums.*;
import com.vedeng.erp.kingdee.repository.KingDeePayVedengBankRepository;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeCustomerMapper;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeSupplierMapper;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 银行已忽略流水推送金蝶
 */
@Service
@Slf4j
public class BankBillProcessor implements ItemProcessor<BatchBankBillDto, KingDeeReceiveBillDto> {

    @Autowired
    private BatchCapitalBillDtoMapper batchCapitalBillDtoMapper;

    @Resource
    private KingDeePayVedengBankRepository kingDeePayVedengBankRepository;

    @Autowired
    private KingDeeSupplierMapper kingDeeSupplierMapper;
    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;
    @Autowired
    private KingDeeCustomerMapper kingDeeCustomerMapper;
    @Autowired
    private BatchSupplierFinanceDtoMapper batchSupplierFinanceDtoMapper;
    @Autowired
    private BatchCustomerFinanceDtoMapper batchCustomerFinanceDtoMapper;
    @Autowired
    private BatchBankBillDtoMapper batchBankBillDtoMapper;
    /**
     * 微信
     */
    private final Integer WE_CHAT = 5;
    /**
     * 采购线
     */
    private final Integer BUYORDER_BUSSINESS_TYPE = 531;
    @Override
    public KingDeeReceiveBillDto process(BatchBankBillDto batchBankBillDto) throws Exception {
        log.info("金蝶处理银行流水信息{},", JSON.toJSONString(batchBankBillDto));
        //忽略银行流水处理
        KingDeeReceiveBillDto kingdeeReceiveBillDto = new KingDeeReceiveBillDto();
        //单据编号
        String tranFlow = batchBankBillDto.getTranFlow();
        if (WE_CHAT.equals(batchBankBillDto.getBankTag()) && tranFlow.length() > 30) {
            tranFlow = tranFlow.substring(0, 30);
        }
        kingdeeReceiveBillDto.setFBillNo(batchBankBillDto.getTranFlow());
        //单据日期
        kingdeeReceiveBillDto.setFDate(DateUtil.formatDate(batchBankBillDto.getTranDate()) + " " + DateUtil.formatTime(batchBankBillDto.getTranTime()));
        //流水号
        kingdeeReceiveBillDto.setFQzokLsh(batchBankBillDto.getTranFlow());
        //erp银行流水id
        kingdeeReceiveBillDto.setBankBillId(batchBankBillDto.getBankBillId());
        kingdeeReceiveBillDto.setFId(ErpConstant.ZERO.toString());

        if (ErpConstant.ZERO.equals(batchBankBillDto.getStatus()) && batchBankBillDto.getMatchedAmount().compareTo(new BigDecimal(0)) > 0) {

            List<BatchCapitalBillDto> capitalBillDtoList = batchCapitalBillDtoMapper.queryCapitalBillByBankBillId(batchBankBillDto.getBankBillId());
            if(CollUtil.isEmpty(capitalBillDtoList)){
                //根据bankBillId匹配不到CapitalBill数据，则用orderNo匹配
                capitalBillDtoList= batchCapitalBillDtoMapper.queryCapitalBillRelateOrderNo(batchBankBillDto.getBankBillId());
            }else{
                // 只推tradeType=1
                capitalBillDtoList = capitalBillDtoList.stream().filter(x -> ErpConstant.ONE.equals(x.getTraderType())).collect(Collectors.toList());
            }
            if(CollUtil.isEmpty(capitalBillDtoList)){
                log.error("资金流水推送失败!未查询到资金流水信息bankBillId:{}",batchBankBillDto.getBankBillId());
                return null;
            }
            //交易主体  1对公，2对私
            BatchCapitalBillDto capitalBillDto = CollUtil.getFirst(capitalBillDtoList);
            //未忽略，且已匹配金额>0
            //单据类型  单位类型编码
            if(BUYORDER_BUSSINESS_TYPE.equals(capitalBillDto.getBussinessType())){
                batchBankBillDto.setMatchedObject(4290);
            }
            String unitType = KingdeeIgnoreBillTypeEnums.matchUnitCodeById(batchBankBillDto.getMatchedObject());
            String billType=KingDeeFormConstant.BD_SUPPLIER.equals(unitType)?KingDeeReceiveBillTypeEnum.OTHER_RECEIVE.getCode():KingDeeReceiveBillTypeEnum.SALE_RECEIVE.getCode();
            kingdeeReceiveBillDto.setFBillTypeId(billType);
            //往来单位类型
            kingdeeReceiveBillDto.setFContactUnitType(unitType);
            //往来单位
            kingdeeReceiveBillDto.setFContactUnit(getContactUnitByTraderId(kingdeeReceiveBillDto.getFContactUnitType(), capitalBillDto.getTraderId()));
            if (StringUtils.isEmpty(kingdeeReceiveBillDto.getFContactUnit())) {
                log.error("资金流水推送失败！未查询到往来单位信息bankBillId:{}", batchBankBillDto.getBankBillId());
                return null;
            }
            //交易主体
            Integer traderSubject = capitalBillDto.getTraderSubject();
            //交易主体
            kingdeeReceiveBillDto.setFQzokJyzt(ErpConstant.ONE.equals(traderSubject) ? "对公" : "对私");
            //交易类型
            kingdeeReceiveBillDto.setFQzokJylx("收入");
            //交易方式
            //String fQzokJyfs = KingDeeTraderModeEnum.matchDescByCode(capitalBillDto.getTraderMode());
            String fQzokJyfs = KingDeeBankTagEnum.matchFQzokJyfsByCode(batchBankBillDto.getBankTag());

            kingdeeReceiveBillDto.setFQzokJyfs(fQzokJyfs);

            List<KingDeeReceiveBillEntryDto> kingdeeReceiveBillEntryDtos = new ArrayList<>();
            for(BatchCapitalBillDto batchCapitalBillDto : capitalBillDtoList){
                KingDeeReceiveBillEntryDto kingdeeReceiveBillEntryDto = new KingDeeReceiveBillEntryDto();
                //结算方式
                KingDeePayVedengBankDto kingDeePayVedengBankDto = queryBankInfo(batchBankBillDto.getBankTag());
                if(kingDeePayVedengBankDto == null){
                    log.error("资金流水推送失败!流水未查询到我方银行信息{},",batchBankBillDto.getBankTag());
                    return null;
                }
                //String fSettleTypeId = "";
                //if (KingDeeTraderModeEnum.BANK_PAY.getCode().equals(batchCapitalBillDto.getTraderMode())) {
                //    fSettleTypeId = KingDeePayBillSettleTypeEnum.TELEGRAPHIC_SETTLETYPE.getCode();
                //} else {
                //    String desc = KingDeeTraderModeEnum.matchDescByCode(batchCapitalBillDto.getTraderMode());
                //    for (KingDeePayBillSettleTypeEnum value : KingDeePayBillSettleTypeEnum.values()) {
                //        if (value.getName().equals(desc)) {
                //            fSettleTypeId = value.getCode();
                //        }
                //    }
                //}

                kingdeeReceiveBillEntryDto.setFSettleTypeId(KingDeeBankTagEnum.matchFSettleTypeIdByCode(batchBankBillDto.getBankTag()));
                kingdeeReceiveBillEntryDto.setFPurposeId(KingDeeFormConstant.BD_CUSTOMER.equals(unitType) ? "SFKYT01_SYS" : "SFKYT07_SYS");
                kingdeeReceiveBillEntryDto.setFAccountId(kingDeePayVedengBankDto.getPayBankNo());
                kingdeeReceiveBillEntryDto.setFRecTotalAmountFor(batchCapitalBillDto.getAmount().toString());
                kingdeeReceiveBillEntryDto.setFQzokBddjhid(batchCapitalBillDto.getCapitalBillId().toString());
                kingdeeReceiveBillEntryDto.setFQzokYsddh("");
                kingdeeReceiveBillEntryDto.setFQzokGsywdh(batchCapitalBillDto.getOrderNo());
                kingdeeReceiveBillEntryDto.setFQzokYwlx(KingDeeFormConstant.BD_CUSTOMER.equals(unitType) ? "销售订单收款" : "采购订单退款");
                // 微信和支付宝需要将手续费加上
                if (batchBankBillDto.getBankTag() == 4 || batchBankBillDto.getBankTag() == 5) {
                    // 查询关联的业务单号
                    batchBankBillDto.setOrderNo(batchCapitalBillDtoMapper.getOrderNoByBankBillId(batchBankBillDto.getBankBillId()));
                    kingdeeReceiveBillEntryDto.setFHandlingChargeFor(this.getFee(batchBankBillDto));
                }
                //未忽略部分全为销售收款
                kingdeeReceiveBillEntryDto.setFQzokSkyw(KingdeeIgnoreBillTypeEnums.matchCodeById(ErpConstant.ZERO));
                kingdeeReceiveBillEntryDtos.add(kingdeeReceiveBillEntryDto);
            }
            kingdeeReceiveBillDto.setFReceiveBillEntry(kingdeeReceiveBillEntryDtos);
            boolean old = kingDeeBaseApi.isExist(kingdeeReceiveBillDto);
            if (old){
                log.info("金蝶处理银行流水信息{},已经推送过金蝶", JSON.toJSONString(kingdeeReceiveBillDto));
                return null;
            }
            return kingdeeReceiveBillDto;
        }

        log.error("银行流水{},异常数据",JSON.toJSONString(batchBankBillDto));
        return null;
    }

    private String getFee(BatchBankBillDto batchBankBillDto) {
        BatchBankBillDto fee = null;
        if (batchBankBillDto.getBankTag() == 4 && org.apache.commons.lang3.StringUtils.isNotBlank(batchBankBillDto.getOrderNo())) {
            fee = batchBankBillDtoMapper.getAliFeeBankBillByOrderNo(batchBankBillDto.getOrderNo());
        }
        if (batchBankBillDto.getBankTag() == 5) {
            fee = batchBankBillDtoMapper.getWeChatFeeBankBillByTranFlow(batchBankBillDto.getTranFlow() + "_fee");
        }
        return Objects.isNull(fee) ? null : fee.getAmt().toString();
    }

    /**
     * 根据往来单位类型，返回往来单位编码
     * @param contactUnitType
     * @param contact
     * @return
     */
    private String getContactUnit(String contactUnitType,String contact){
        log.info("查询往来单位类型，参数{},{},",contactUnitType,contact);
        if(KingDeeFormConstant.BD_SUPPLIER.equals(contactUnitType)){
            //供应商
            KingDeeSupplierEntity kingDeeSupplierEntity = kingDeeSupplierMapper.queryInfoBySupplierName(contact);
            if(Objects.isNull(kingDeeSupplierEntity)){
                log.info("供应商未推送金蝶");
                return null;
            }
            return kingDeeSupplierEntity.getFNumber().toString();
        }else if(KingDeeFormConstant.BD_CUSTOMER.equals(contactUnitType)){
            //客户
            KingDeeCustomerEntity kingDeeCustomerEntity = kingDeeCustomerMapper.queryInfoByCustomerName(contact);
            if(Objects.isNull(kingDeeCustomerEntity)){
                log.info("客户未推送金蝶");
                return null;
            }
            return kingDeeCustomerEntity.getFNumber().toString();
        }else {
            //银行代收款
            KingDeePayVedengBankDto kingDeePayVedengBankDto = queryBankInfo(Integer.parseInt(contact));
            return kingDeePayVedengBankDto.getKingDeeBankCode();
        }
    }

    /**
     * 根据traderId查询erp的客户和供应商的主键id
     * @param contactUnitType
     * @return
     */
    private String getContactUnitByTraderId(String contactUnitType, Integer traderId) {
        if (KingDeeFormConstant.BD_SUPPLIER.equals(contactUnitType)) {
            //供应商
            BatchSupplierFinanceDto supplierDto = batchSupplierFinanceDtoMapper.getSupplierIdByTraderId(traderId);
            if (Objects.isNull(supplierDto)||Objects.isNull(supplierDto.getTraderSupplierId())) {
                log.info("供应商在erp系统不存在");
                return null;
            }
            return supplierDto.getTraderSupplierId().toString();
        } else{
            //客户
            BatchCustomerFinanceDto customerDto = batchCustomerFinanceDtoMapper.getCustomerIdByTraderId(traderId);
            if (Objects.isNull(customerDto)||Objects.isNull(customerDto.getTraderCustomerId())) {
                log.info("客户在erp系统不存在");
                return null;
            }
            return customerDto.getTraderCustomerId().toString();
        }
    }

    /**
     * 根据流水类型查询银行信息
     * @param bankType
     * @return
     */
    private KingDeePayVedengBankDto queryBankInfo(Integer bankType){
        KingDeePayVedengBankDto kingDeePayVedengBankDto = new KingDeePayVedengBankDto();
        switch (bankType){
            case 1:
                //建设银行
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.SIX);
                break;
            case 2:
                //南京银行
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.TWO);
                break;
            case 3:
                //中国银行
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.ONE);
                break;
            case 4:
                //支付宝
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.FOUR);
                break;
            case 5:
                //微信
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.FIVE);
                break;
            case 6:
                //交通银行
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.THREE);
                break;
            default:
                log.info("银行流水类型未查对应银行编码");
                return null;
        }
        return kingDeePayVedengBankDto;
    }
}
