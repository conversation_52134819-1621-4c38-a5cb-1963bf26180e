package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.annotation.BusinessID;
import com.vedeng.infrastructure.kingdee.annotation.KingDeeID;
import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * 应收单：费用应收单   https://www.yuque.com/manhuo/gf1570/zzbtxy
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date
 */
@Builder
@Getter
@Setter
@AllArgsConstructor
@Accessors(chain = true)
@WriteBackField(needBackField = {"FID","FEntityDetail.FEntryID", "FEntityDetail.F_QZOK_BDDJHID"})
public class KingDeeReceiveFeeDto extends KingDeeMqBaseDto {

    /**
     * 单据内码 0：表示新增，非0：云星空系统单据FID值，表示修改
     */
    @WriteBackField
    @KingDeeID
    private String fid;

    /**
     * 单据日期 格式yyyy-MM-dd
     */
    private String fdate;
    /**
     * 贝登单据头ID 贝登单据头ID号（预留）
     */
    @BusinessID("f_Qzok_Bddjtid")
    private String fQzokBddjtid;

    /**
     * 业务类型 默认FY
     */
    private String fbusinesstype;

    /**
     * 立账类型  默认填写1
     */
    private String fSetAccountType;
    /**
     * fEntityDetail
     */
    private List<KingDeeReceiveFeeDetailDto> fEntityDetail;



    /**
     * 单据类型 填单据类型编码，默认"YSD01_SYS"
     */
    private String fBillTypeID;

    /**
     * 客户 填写客户编码
     */
    private String fcustomerid;

    /**
     * 结算组织 填写组织编码
     */
    private String fsettleorgid;

    @Override
    public String getFormId() {
        return KingDeeFormConstant.RECEIVE_EXPENSES;
    }

    public KingDeeReceiveFeeDto(){
        this.fBillTypeID = "YSD02_SYS";
        this.fbusinesstype = "FY";
        this.fsettleorgid = KingDeeConstant.ORG_ID.toString();
        this.fSetAccountType = "1";
        this.fEntityDetail = new ArrayList<>();
    }
}
