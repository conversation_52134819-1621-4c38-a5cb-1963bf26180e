package com.vedeng.todolist.service.visitor;

import com.vedeng.authorization.model.User;
import com.vedeng.flash.dao.EarlyWarningTaskMapper;
import com.vedeng.todolist.dto.PrepareStockTaskToDoDto;
import com.vedeng.todolist.dto.SupplyChainStaffTodoDtoBuild;
import com.wms.service.context.ThreadLocalContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class PrepareStockTaskStaffVisitor implements TodoVisitor<SupplyChainStaffTodoDtoBuild> {

    @Autowired
    private EarlyWarningTaskMapper earlyWarningTaskMapper;

    @Override
    public void visitor(SupplyChainStaffTodoDtoBuild todoDtoBuild) {
        PrepareStockTaskToDoDto prepareStockTaskToDoDto = new PrepareStockTaskToDoDto();
        User user = ThreadLocalContext.get("userInfo");
        if (user != null) {
            List<Integer> userIdList = new ArrayList<>();
            userIdList.add(user.getUserId());
            prepareStockTaskToDoDto = earlyWarningTaskMapper.getPrepareStockTaskByUserId(userIdList);
            prepareStockTaskToDoDto.setUserId(user.getUserId());
        }
        todoDtoBuild.get().setPrepareStockTaskToDoDto(prepareStockTaskToDoDto);
    }
}
