package com.vedeng.erp.aftersale.dto;

import com.vedeng.erp.finance.dto.InvoiceDto;
import com.vedeng.infrastructure.file.domain.Attachment;
import lombok.Data;

import java.util.List;

/**
 * 售后api返回dto
 */
@Data
public class AfterSalesDto {
    /**
     * 售后id
     */
    private Integer afterSalesId;
    /**
     * 售后单号
     */
    private String afterSalesNo;
    /**
     * 售后主体类型（字典库:销售、采购、第三方）
     */
    private Integer subjectType;
    /**
     * 售后类型，字典表
     */
    private Integer type;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 订单id
     */
    private Integer orderId;
    /**
     * 是否生效0否1是
     */
    private Integer validStatus;
    /**
     * 售后状态0待确认1进行中2已完结3已关闭
     */
    private Integer atferSalesStatus;
    /**
     * 创建人
     */
    private Integer creator;
    /**
     * 创建人名称
     */
    private String creatorName;
    /**
     * 创建时间
     */
    private String addTime;
    /**
     * 生效时间
     */
    private String validTime;
    /**
     * 售后原因
     */
    private Integer reason;
    /**
     * 附加说明
     */
    private String comments;
    /**
     * 仅退票新蓝字发票号
     */
    private String newInvoiceNo;
    /**
     * 仅退票蓝字发票代码
     */
    private String newInvoiceCode;
    /**
     * 售后订单审核状态0待确认1审核中2审核通过3审核不通过
     */
    private Integer status;
    /**
     * 售后类型
     */
    private String typeName;
    /**
     * 退票状态
     */
    private Integer invoiceRefundStatus;
    /**
     * 新发票类型
     */
    private Integer invoiceType;
    /**
     * 收款状态: 0无收款状态 1未收款 2部分收款 3全部收款
     */
    private Integer amountCollectionStatus;
    /**
     * 开票状态 0未开票 1部分开票 2全部开票
     */
    private Integer invoiceStatus;

    /**
     * 付款状态：0无付款状态 1未付款 2部分付款 3全部付款
     */
    private Integer amountPayStatus;
    /**
     * 售后商品信息
     */
    private List<AfterBuyorderGoodsDto> afterBuyorderGoodsDtoList;
    /**
     * 售后退票信息
     */
    private List<ReturnBuyorderInvoiceDto> returnBuyorderInvoiceDto;
    /**
     * 售后发票信息
     */
    private List<InvoiceDto> invoiceDtoList;
    /**
     * 附件
     */
    private List<Attachment> attachmentList;
}
