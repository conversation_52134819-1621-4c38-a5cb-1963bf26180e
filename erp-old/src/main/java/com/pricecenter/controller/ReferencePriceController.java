package com.pricecenter.controller;

import com.pricecenter.dto.*;
import com.pricecenter.service.ReferencePriceService;
import com.pricecenter.service.validator.PriceValidatorForReference;
import com.pricecenter.service.validator.SkuNoValidatorForReference;
import com.pricecenter.service.validator.ValidatorChain;
import com.pricecenter.service.validator.ValidatorChainBuild;
import com.vedeng.aftersales.model.AfterSaleSupplyPolicy;
import com.vedeng.aftersales.model.dto.AfterSaleServiceStandardInfoDto;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.model.FileInfo;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.StringUtil;
import com.vedeng.goods.dao.CoreSkuGenerateMapper;
import com.vedeng.goods.dao.UnitMapper;
import com.vedeng.goods.model.CoreSkuGenerate;
import com.vedeng.goods.model.Unit;
import com.vedeng.goods.service.GoodsService;
import com.vedeng.system.service.UserService;
import org.activiti.engine.task.IdentityLink;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.FileInputStream;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 参考价列表
 */
@Controller
@RequestMapping("/price/referencePrice")
public class ReferencePriceController extends BaseController {

    //定义日志
    private static final Logger LOGGER = LoggerFactory.getLogger(ReferencePriceController.class);

    @Autowired
    private UserService userService;

    @Autowired
    private ReferencePriceService referencePriceService;

    @Resource
    private CoreSkuGenerateMapper coreSkuGenerateMapper;

    @Resource
    private UnitMapper unitMapper;

    @Resource
    private SkuNoValidatorForReference skuNoValidatorForReference;

    @Resource
    private PriceValidatorForReference priceValidatorForReference;

    @Autowired
    private GoodsService goodsService;

    /**
     * 参考价格维护列表页
     */
    @ResponseBody
    @RequestMapping(value = "/index",produces = "application/json;charset=UTF-8")
    public ModelAndView index(HttpServletRequest request, SkuReferencePricePageQueryDto queryDto,
                              @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                              @RequestParam(required = false) Integer pageSize) throws Exception {

        LOGGER.info("基础价格维护列表页");
        queryDto.setPageSize(pageSize);
        queryDto.setPageNo(pageNo);

        PageResultDto<SkuReferencePriceResponseDto> pageResultVo = this.referencePriceService.findByPage(queryDto);
        List<SkuReferencePriceResponseDto> SkuReferencePriceResponseDtoList = pageResultVo.getDatas();
        List<SkuReferencePriceResponseDto> SkuReferencePriceResponseDtoNewList = new ArrayList<SkuReferencePriceResponseDto>();
        SkuReferencePriceResponseDtoList.forEach(res->{
            String unitName = goodsService.getUnitNameBySkuNo(res.getSkuNo());
            SkuReferencePriceResponseDto skuReferencePriceResponseDto = new SkuReferencePriceResponseDto();
            BeanUtils.copyProperties(res, skuReferencePriceResponseDto);
            skuReferencePriceResponseDto.setUnitName(unitName);
            SkuReferencePriceResponseDtoNewList.add(skuReferencePriceResponseDto);
        });
        pageResultVo.setDatas(SkuReferencePriceResponseDtoNewList);

        Page page = super.getPageTag(request,pageNo,pageSize);
        page.setTotalRecord(pageResultVo.getTotalRecords());

        ModelAndView mv=new ModelAndView("/price/referencePrice/index");

        mv.addObject("referencePriceList",SkuReferencePriceResponseDtoNewList);
        mv.addObject("queryDto",queryDto);
        mv.addObject("page",page);

        List<User> manages = userService.selectAllAssignUser();
        //所属产品经理
        mv.addObject("managerUserList",manages);
        //所属产品助理
        mv.addObject("assUserList", manages);
        return mv;
    }

    @ResponseBody
    @RequestMapping(value = "/toEditValidator")
    public ResultInfo toEditValidator(HttpServletRequest request, @Param("skuNo") String skuNo){
        try{


            User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);

            UserInfo userInfo = this.coreSkuGenerateMapper.getAssignAndManageId(Long.valueOf(skuNo.substring(1)));

            //没有归属用户直接报错
            if(userInfo == null || (userInfo.getManagerId() == null && userInfo.getAssistantId() == null)){
                return new ResultInfo(-1,"当前SKU没有归属产品经理或助理");
            }

            if(!user.getUserId().equals(userInfo.getAssistantId())  &&  !user.getUserId().equals(userInfo.getManagerId())){
                return new ResultInfo(-1,"您没有操作权限，申请开通权限请联系研发部Aadi,只有归属产品经理或产品助理才可编辑");
            }
            return new ResultInfo(0,"成功","");

        }catch (Exception e){
            logger.error("toEditValidator",e);
            return new ResultInfo(-1,"失败");
        }
    }

    /**
     * 参考价编辑页
     */
    @RequestMapping(value = "/toEdit")
    public ModelAndView toEdit(@Param("skuNo") String skuNo,@Param("referencePriceId") String referencePriceId){


        SkuReferencePriceResponseDto referencePriceResponseDto = null;
        if(StringUtil.isNotEmpty(referencePriceId)){
            referencePriceResponseDto = this.referencePriceService.findReferencePriceById(Integer.valueOf(referencePriceId));
        }

        ModelAndView mv = new ModelAndView();
        mv.setViewName("price/referencePrice/edit");

        CoreSkuGenerate skuInfo = coreSkuGenerateMapper.selectBySkuNo(skuNo);
        mv.addObject("skuInfo",skuInfo);

        Unit unit = unitMapper.selectByPrimaryKey(skuInfo.getBaseUnitId());
        mv.addObject("unitName",unit.getUnitName());

        mv.addObject("firstEffectTime", Optional.ofNullable(referencePriceResponseDto).map(e->e.getAddTime()).orElse("-"));

        mv.addObject("referencePriceResponseDto",referencePriceResponseDto);
        return mv;
    }

    /**
     * 参考价编辑
     * @param editReferencePriceDto
     */
    @RequestMapping(value = "/editReferencePrice")
    public ModelAndView editReferencePrice(HttpServletRequest request,EditReferencePriceDto editReferencePriceDto){

        ModelAndView mv = new ModelAndView();

        try{

            User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);

            editReferencePriceDto.setOperator(user.getUsername());

            //通知价格中心,这次变动的数据
            referencePriceService.editReferencePrice(editReferencePriceDto);

            mv.addObject("url", "./index.do");

            return success(mv);

        }catch(Exception e){
            LOGGER.error("editBasePrice error",e);
            mv.addObject("message",e.getMessage());
            return fail(mv);
        }

    }

    /**
     * 详情页
     */
    @RequestMapping(value = "/detail")
    public ModelAndView detail(HttpServletRequest request,@Param("skuNo") String skuNo,@Param("referencePriceId") String referencePriceId) {

        SkuReferencePriceResponseDto referencePriceResponseDto = null;

        if(StringUtil.isNotEmpty(referencePriceId)){
            referencePriceResponseDto = this.referencePriceService.findReferencePriceById(Integer.valueOf(referencePriceId));
        }

        ModelAndView mv = new ModelAndView();
        mv.setViewName("price/referencePrice/detail");

        CoreSkuGenerate skuInfo = coreSkuGenerateMapper.selectBySkuNo(skuNo);
        mv.addObject("skuInfo",skuInfo);

        mv.addObject("referencePriceResponseDto",referencePriceResponseDto);
        return mv;
    }

    /**
     * 批量上传页面
     */
    @RequestMapping(value = "/batchUploadPage")
    public ModelAndView batchUploadPage(HttpServletRequest request) {
        ModelAndView mv = new ModelAndView();
        mv.setViewName("price/referencePrice/batchUpload");
        return mv;
    }

    /**
     * 批量上传
     * @param request
     * @param lwfile
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/batchUpload")
    public ResultInfo<?> batchUpload(HttpServletRequest request,
                                     @RequestParam("lwfile") MultipartFile lwfile) {
        ResultInfo<?> resultInfo = new ResultInfo<>();
        try {

            //处理文件
            List<ReferencePriceUploadDto> referencePriceDtoList = dealwithFile(request,lwfile);

            //批量上传
            referencePriceService.batchUploadReferencePrice(referencePriceDtoList);

            resultInfo.setCode(0);
            resultInfo.setMessage("批量导入成功");

        } catch (Exception e) {
            logger.error("batchUpload price file error", e);
            resultInfo.setMessage(e.getMessage());
        }
        return resultInfo;
    }

    /**
     * 处理文件
     * @param request
     * @return
     * @throws Exception
     */
    private List<ReferencePriceUploadDto> dealwithFile(HttpServletRequest request,MultipartFile lwfile) throws Exception {


        List<Row> rows = getRows(request,lwfile);

        if(CollectionUtils.isEmpty(rows)){
            throw new Exception("上传文件不能为空");
        }

        PriceInfoUploadValidatorDto priceInfoUploadValidatorDto = new PriceInfoUploadValidatorDto();
        priceInfoUploadValidatorDto.setUser((User) request.getSession().getAttribute(ErpConst.CURR_USER));
        priceInfoUploadValidatorDto.setRows(rows);

        ValidatorChain validatorChain = ValidatorChainBuild.newBuild()
                .setValidator(skuNoValidatorForReference)
                .setValidator(priceValidatorForReference)
                .create();

        ValidatorResult validatorResult = validatorChain.validator(priceInfoUploadValidatorDto);

        //检验不通过返回结果
        if(!validatorResult.getResult()){
            throw new Exception(validatorResult.getMessage());
        }

        return processRow(rows,priceInfoUploadValidatorDto);
    }

    /**
     * 获取行结果
     * @param rows
     * @return
     */
    private List<ReferencePriceUploadDto> processRow(List<Row> rows,PriceInfoUploadValidatorDto priceInfoUploadValidatorDto) {


        List<ReferencePriceUploadDto> priceInfoUploadDtoList = new ArrayList<>();

        rows.stream().forEach(row -> {

            ReferencePriceUploadDto referencePriceUploadDto = new ReferencePriceUploadDto();
            priceInfoUploadDtoList.add(referencePriceUploadDto);

            referencePriceUploadDto.setSkuNo(row.getCell(0).getStringCellValue());

            if(row.getCell(1) !=  null && row.getCell(1).getCellType() !=  CellType.BLANK){
                referencePriceUploadDto.setReferenceTerminalPrice(new BigDecimal(row.getCell(1).getStringCellValue()));
            }

            if(row.getCell(2) !=  null && row.getCell(2).getCellType() !=  CellType.BLANK){
                referencePriceUploadDto.setReferenceDistributionPrice(new BigDecimal(row.getCell(2).getStringCellValue()));
            }

            if(row.getCell(3) !=  null && row.getCell(3).getCellType() !=  CellType.BLANK){
                referencePriceUploadDto.setReferenceGroupPrice(new BigDecimal(row.getCell(3).getStringCellValue()));
            }

        });

        return priceInfoUploadDtoList;
    }

    /**
     * 解析行
     * @param request
     * @param lwfile
     * @return
     * @throws Exception
     */
    private List<Row> getRows(HttpServletRequest request,MultipartFile lwfile) throws Exception{

        // 临时文件存放地址
        String path = request.getSession().getServletContext().getRealPath("/upload/saleorder");
        FileInfo fileInfo = ftpUtilService.fileUploadServe(path, lwfile);
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);

        if(fileInfo.getCode() != 0){
            throw new Exception("文件上传失败");
        }
        List<Row> rows = new ArrayList<>();
        Workbook workbook = null;
        FileInputStream fileInputStream = new FileInputStream(new File(fileInfo.getFilePath()));
        try {
            // 获取excel路径
             workbook = WorkbookFactory.create(fileInputStream);
            // 获取第一面sheet
            Sheet sheet = workbook.getSheetAt(0);
            // 起始行
            int startRowNum = sheet.getFirstRowNum() + 1;// 第一行标题
            int endRowNum = sheet.getLastRowNum();// 结束行

            for (int rowNum = startRowNum; rowNum <= endRowNum; rowNum++) {
                rows.add(sheet.getRow(rowNum));
            }
        } catch (Exception e) {
            log.error("", e);
        } finally {
            if (fileInfo != null) {
                try {
                    fileInputStream.close();
                } catch (Exception e2) {
                }
            }
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (Exception e2) {
                }

            }

        }
        return rows;
    }

}
