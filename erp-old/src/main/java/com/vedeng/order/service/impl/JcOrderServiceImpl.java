package com.vedeng.order.service.impl;

import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.vedeng.aftersales.service.WebAccountService;
import com.vedeng.authorization.dao.RegionMapper;
import com.vedeng.authorization.model.Region;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.*;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.common.page.Page;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.common.util.NumberUtil;
import com.vedeng.customerbillperiod.constant.CustomerBillPeriodSettlementTypeEnum;
import com.vedeng.jc.api.dto.AccountResponse;
import com.vedeng.jc.api.enums.PlatformRoleEnum;
import com.vedeng.jc.api.enums.ProductLineEnum;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.enums.SaleOrderStatusEnum;
import com.vedeng.order.enums.SaleOrderTypeEnum;
import com.vedeng.order.model.ComponentRelation;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.model.dto.JcTraderContactDto;
import com.vedeng.order.service.JcOrderService;
import com.vedeng.order.service.OrderCommonService;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.system.service.RegionService;
import com.vedeng.trader.dao.*;
import com.vedeng.trader.model.*;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import com.vedeng.trader.service.TraderContactService;
import com.vedeng.trader.service.TraderCustomerService;
import com.wms.service.other.LogicalSaleorderChooseServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service("jcOrderService")
public class JcOrderServiceImpl extends BaseServiceimpl implements JcOrderService {

    private final static Logger LOGGER = LoggerFactory.getLogger(JcOrderServiceImpl.class);

    @Autowired
    @Qualifier("saleorderMapper")
    private SaleorderMapper saleorderMapper;
    @Resource
    private WebAccountService webAccountService;
    @Resource
    private TraderContactService traderContactService;
    @Resource
    private TraderMapper traderMapper;
    @Resource
    private TraderCustomerMapper traderCustomerMapper;
    @Resource
    protected SaleorderService saleorderService;
    @Resource
    private WebAccountMapper webAccountMapper;
    @Autowired
    private WarehouseStockService warehouseStockService;
    @Autowired
    private LogicalSaleorderChooseServiceImpl logicalSaleorderChooseServiceImpl;
    @Resource
    private TraderAddressMapper traderAddressMapper;
    @Resource
    private RegionMapper regionMapper;
    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;
    @Resource
    protected TraderCustomerService traderCustomerService;
    @Resource
    protected RegionService regionService;
    @Resource
    protected TraderContactGenerateMapper traderContactGenerateMapper;
    @Resource
    private OrderCommonService orderCommonService;

    /**
     * 集采客户级别：1.总部, 2.分院（目前只有两级层级）
     */
    private static final Integer GROUP_HEAD_NO = 1;
    private static final Integer GROUP_MEMBER_NO = 2;

    @Override
    public Map<String, Object> getJcSaleOrderListPage(HttpServletRequest request, Saleorder saleorder, Page page) {
        Map<String, Object> map = new HashMap<String, Object>();

        map.put("saleorder", saleorder);

        // 如果使用账期未还条件查询；1、先查询出全部-账期未还-的销售订单
        if (saleorder.getAccountPeriod() != null && saleorder.getAccountPeriod().intValue() >= 0) {
            List<Integer> saleOrderIdList = saleorderMapper.getLackAccountPeriodOrderSaleOrderId();
            saleorder.setKeyIds(saleOrderIdList);
        }


        BigDecimal totalAmount = null;
        if (saleorder != null && saleorder.getIsSearchCount() == 1) {
            totalAmount = saleorderMapper.getSaleorderListSum(map);
            saleorder.setAllTotalAmount(totalAmount);
            map.put("saleorder", saleorder);
        }
        /*********************** Cooper---合同回传和送货单回传查询条件 *************************************/
        List<Integer> saleorderIdsList = new ArrayList<Integer>();// 最终订单ID集合
        List<Integer> saleorderContractReturnList = null;// 合同回传的ID集合
        List<Integer> saleorderDeliveryReturnList = null;// 送货单回传的ID集合
        if (saleorder.getIsContractReturn() != null || saleorder.getIsDeliveryOrderReturn() != null) {
            if (saleorder.getIsContractReturn() != -1 || saleorder.getIsDeliveryOrderReturn() != -1) {// 两个回传只要有一个不是全部条件的都进来
                Map<String, Object> tempMap = new HashMap<String, Object>();
                tempMap.put("saleorder", saleorder);
                /*
                 * tempMap.put("type", 0);
                 * saleorderIdsList=saleorderMapper.getSaleorderReturnOrNotList(
                 * tempMap);
                 */
                if (saleorder.getIsContractReturn() == 1 && saleorder.getIsDeliveryOrderReturn() == 1) {// 合同已回传+送货单已回传
                    // 合同已回传的订单Id集合
                    tempMap.put("type", 1);
                    saleorderContractReturnList = saleorderMapper.getSaleorderReturnOrNotList(tempMap);
                    // 送货单已回传的订单Id集合
                    tempMap.put("type", 2);
                    saleorderDeliveryReturnList = saleorderMapper.getSaleorderReturnOrNotList(tempMap);

                    saleorderIdsList.addAll(saleorderContractReturnList);
                    // 求交集，形成合同已回传+送货单已回传的订单Id集合
                    saleorderIdsList.retainAll(saleorderDeliveryReturnList);
                } else if (saleorder.getIsContractReturn() == 0 && saleorder.getIsDeliveryOrderReturn() == 1) {// 合同未回传+送货单已回传
                    // 所有订单Id集合
                    tempMap.put("type", 0);
                    saleorderIdsList = saleorderMapper.getSaleorderReturnOrNotList(tempMap);
                    // 合同已回传的订单Id集合
                    tempMap.put("type", 1);
                    saleorderContractReturnList = saleorderMapper.getSaleorderReturnOrNotList(tempMap);
                    // 送货单已回传的订单Id集合
                    tempMap.put("type", 2);
                    saleorderDeliveryReturnList = saleorderMapper.getSaleorderReturnOrNotList(tempMap);
                    // 将所有订单Id中排除合同已回传的订单Id集合
                    saleorderIdsList.removeAll(saleorderContractReturnList);
                    // 求交集，形成合同未回传+送货单已回传的订单Id集合
                    saleorderIdsList.retainAll(saleorderDeliveryReturnList);
                } else if (saleorder.getIsContractReturn() == 1 && saleorder.getIsDeliveryOrderReturn() == 0) {// 合同已回传+送货单未回传
                    // 所有订单Id集合
                    tempMap.put("type", 0);
                    saleorderIdsList = saleorderMapper.getSaleorderReturnOrNotList(tempMap);
                    // 合同已回传的订单Id集合
                    tempMap.put("type", 1);
                    saleorderContractReturnList = saleorderMapper.getSaleorderReturnOrNotList(tempMap);
                    // 送货单已回传的订单Id集合
                    tempMap.put("type", 2);
                    saleorderDeliveryReturnList = saleorderMapper.getSaleorderReturnOrNotList(tempMap);
                    // 将所有订单Id中排除送货单已回传的订单Id集合
                    saleorderIdsList.removeAll(saleorderDeliveryReturnList);
                    // 求交集，形成合同已回传+送货单未回传的订单Id集合
                    saleorderIdsList.retainAll(saleorderContractReturnList);
                } else if (saleorder.getIsContractReturn() == 0 && saleorder.getIsDeliveryOrderReturn() == 0) {// 合同未回传+送货单未回传
                    // 所有订单Id集合
                    tempMap.put("type", 0);
                    saleorderIdsList = saleorderMapper.getSaleorderReturnOrNotList(tempMap);
                    // 复制列表
                    List<Integer> saleorderIdsListOther = new ArrayList<Integer>();
                    saleorderIdsListOther.addAll(saleorderIdsList);
                    // tempMap.put("type", 0);
                    // List<Integer>
                    // saleorderIdsList1=saleorderMapper.getSaleorderReturnOrNotList(tempMap);
                    // 合同已回传的订单Id集合
                    tempMap.put("type", 1);
                    saleorderContractReturnList = saleorderMapper.getSaleorderReturnOrNotList(tempMap);
                    // 送货单已回传的订单Id集合
                    tempMap.put("type", 2);
                    saleorderDeliveryReturnList = saleorderMapper.getSaleorderReturnOrNotList(tempMap);
                    // 将所有订单Id中排除合同已回传的订单Id集合
                    saleorderIdsList.removeAll(saleorderContractReturnList);
                    // 将所有订单Id中排除送货单已回传的订单Id集合
                    saleorderIdsListOther.removeAll(saleorderDeliveryReturnList);
                    // 求交集，形成合同未回传+送货单未回传的订单Id集合
                    saleorderIdsList.retainAll(saleorderIdsListOther);
                } else if (saleorder.getIsContractReturn() == -1 && saleorder.getIsDeliveryOrderReturn() == 1) {// 合同回传全部+送货单已回传
                    // 送货单已回传的订单Id集合
                    tempMap.put("type", 2);
                    saleorderDeliveryReturnList = saleorderMapper.getSaleorderReturnOrNotList(tempMap);
                    saleorderIdsList.addAll(saleorderDeliveryReturnList);
                } else if (saleorder.getIsContractReturn() == -1 && saleorder.getIsDeliveryOrderReturn() == 0) {// 合同回传全部+送货单未回传
                    // 所有订单Id集合
                    tempMap.put("type", 0);
                    saleorderIdsList = saleorderMapper.getSaleorderReturnOrNotList(tempMap);
                    // 送货单已回传的订单Id集合
                    tempMap.put("type", 2);
                    saleorderDeliveryReturnList = saleorderMapper.getSaleorderReturnOrNotList(tempMap);
                    // 将所有订单Id中排除送货单已回传的订单Id集合
                    saleorderIdsList.removeAll(saleorderDeliveryReturnList);
                } else if (saleorder.getIsContractReturn() == 1 && saleorder.getIsDeliveryOrderReturn() == -1) {// 合同已回传+送货单全部
                    // 合同已回传的订单Id集合
                    tempMap.put("type", 1);
                    saleorderContractReturnList = saleorderMapper.getSaleorderReturnOrNotList(tempMap);
                    saleorderIdsList.addAll(saleorderContractReturnList);
                } else if (saleorder.getIsContractReturn() == 0 && saleorder.getIsDeliveryOrderReturn() == -1) {// 合同未回传+送货单全部
                    // 所有订单Id集合
                    tempMap.put("type", 0);
                    saleorderIdsList = saleorderMapper.getSaleorderReturnOrNotList(tempMap);
                    // 合同已回传的订单Id集合
                    tempMap.put("type", 1);
                    saleorderContractReturnList = saleorderMapper.getSaleorderReturnOrNotList(tempMap);
                    // 将所有订单Id中排除合同已回传的订单Id集合
                    saleorderIdsList.removeAll(saleorderContractReturnList);
                }

            }
            map.put("saleorderIdsList", saleorderIdsList);
        }

        /**
         * 添加产品归属人条件搜索
         */
        if(saleorder.getProductBelongUserId() != null && saleorder.getProductBelongUserId() !=  -1) {
            List<Integer> saleOrderIds = saleorderMapper.getSaleorderIdListBelongtoProductRole(saleorder.getProductBelongUserId());
            map.put("productSaleIds",saleOrderIds);
        }

//        map.put("page", page);
//        page.setTotalRecord(saleorderMapper.getSaleorderListCount(map));
//        List<Saleorder> list = saleorderMapper.getJcOrderListPage(map);
//        map.put("saleorderList", list);

        /******************************************* 完 ***************************************************/
        Map<String, Object> saleorderINfo = null;
        saleorderINfo = saleorderMapper.getSaleorderListCount(map);

        map.put("page", page);
        map.put("total_amount", new BigDecimal(0));
        page.setTotalRecord(Integer.valueOf(saleorderINfo.get("total_count").toString()));

        if(null != saleorderINfo){
            map.put("total_amount", new BigDecimal(saleorderINfo.get("total_amount").toString()));
            page.setTotalRecord(Integer.valueOf(saleorderINfo.get("total_count").toString()));
        }
        List<Saleorder> saleorderList = null;
        saleorderList = saleorderMapper.getJcOrderListPage(map);
		/*if (!(org.apache.commons.lang3.StringUtils.isNotBlank(saleorder.getOptType()) && saleorder.getOptType().equals("orderIndex"))) {
			getPayAmount(saleorderList);
		}*/
        map.put("saleorderList", saleorderList);

        return map;
    }


     @Override
    public JcTraderContactDto getJcAccountInfo(Integer contactId) {
        if (Objects.isNull(contactId)) {
            return null;
        }

        TraderContactGenerate contactQuery = traderContactService.selectByPrimaryKey(contactId);
        if (contactQuery == null || !CommonConstants.ON.equals(contactQuery.getIsEnable())) {
            throw new IllegalStateException(String.format("查询联系人[%d :: s% ]信息失败或记录已被禁用", contactId,
                    contactQuery==null?"":contactQuery.getName()));
        }

        String mobileNoToUse = contactQuery.getMobile();


        Trader traderQuery = traderMapper.getTraderByTraderId(contactQuery.getTraderId());
        if (traderQuery == null) {
            LOGGER.error("获取集采客户联系人信息时查询主表表信息失败 - contactId: {}, traderId:{}", contactId , contactQuery.getTraderId());
            throw new IllegalStateException("查询交易联系人主表表信息失败");
        }

        JcTraderContactDto jcTraderContactDto = new JcTraderContactDto();
        //1 总院 2 分院
        jcTraderContactDto.setGroupLevel(NumberUtil.isZeroOrNull(traderQuery.getParentId())? GROUP_HEAD_NO : GROUP_MEMBER_NO);
        jcTraderContactDto.setContactName(contactQuery.getName());

        AccountResponse jcAccountInfo = webAccountService.getJcAccountInfo(mobileNoToUse);
        if (jcAccountInfo == null) {
            LOGGER.warn("调用集采客户信息失败 - traderId: {}, mobileNo: {}", contactQuery.getTraderId(), mobileNoToUse);
        }

        if (jcAccountInfo != null) {
            if (CollectionUtils.isNotEmpty(jcAccountInfo.getProductLine())) {
                String allowedGoodsTypes = jcAccountInfo.getProductLine().stream().map(item -> {
                    for (ProductLineEnum productLineEnum : ProductLineEnum.values()) {
                        if (item != null && item == productLineEnum.getCode()) {
                            return productLineEnum.getName();
                        }
                    }
                    return null;
                }).filter(Objects::nonNull).collect(Collectors.joining(ErpConst.Symbol.SLASH));

                jcTraderContactDto.setAllowedGoodsTypes(allowedGoodsTypes);
            }

            if(CollectionUtils.isNotEmpty(jcAccountInfo.getRole())){
                StringJoiner joiner = new StringJoiner(ErpConst.Symbol.COMMA);
                List<Integer> roleNoList = jcAccountInfo.getRole();
                for (PlatformRoleEnum platformRole : PlatformRoleEnum.values()) {
                    if (roleNoList.contains(platformRole.getCode())) {
                        joiner.add(platformRole.getName());
                    }
                }
                jcTraderContactDto.setPositions(joiner.toString());
            }
        }
        return jcTraderContactDto;
    }


     @Override
    public List<TraderCustomerVo> listAllGroupCustomer(Integer traderId) {
        Integer groupHeadId = getGroupHeadId(traderId);
        if (groupHeadId == null) {
            return Collections.emptyList();
        }

        List<TraderCustomerVo> returnList = new ArrayList<>();
        returnList.addAll(traderCustomerMapper.listGroupCustomer(groupHeadId, GROUP_HEAD_NO, BelongPlatformEnum.JC.getBelong()));
        returnList.addAll(traderCustomerMapper.listGroupCustomer(groupHeadId, GROUP_MEMBER_NO, BelongPlatformEnum.JC.getBelong()));

        return returnList;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer saveJcfOrderDetail(Saleorder saleorder, HttpServletRequest request, HttpSession session) {
        Objects.requireNonNull(saleorder, "提交的订单信息不能为空");

        //订单结算主体为收票客户
        Integer invoiceTraderId = saleorder.getInvoiceTraderId();

        //校验收票客户信息是否存在且未被禁用
        if (invoiceTraderId == null || invoiceTraderId== 0) {
            throw new IllegalArgumentException("集采订单结算主体为收票客户，保存订单信息时售票客户id为空");
        }
        TraderCustomerVo invoiceCustomerQuery = traderCustomerMapper.getCustomerInfo(invoiceTraderId);
        if (invoiceCustomerQuery == null || CommonConstants.DISABLE.equals(invoiceCustomerQuery.getIsEnable())) {
            throw new IllegalStateException("保存集采订单时未查询到收票客户");
        }

        // 保存客户名称（老流程太坑了）
        saleorder.setTraderName(invoiceCustomerQuery.getName());

        //检验并替换注册用户信息为联系人信息
        checkAndReplaceSubmittedAccountInfo(saleorder, false);

        //集采订单客户结算主体是"收票客户"
        saleorder.setTraderId(saleorder.getInvoiceTraderId());

        //设置默认支付信息 - 100%预付
        setDefaultPaymentInfo(saleorder);

        //更新订单的联系人职位
        updateContactPositionIfExist(saleorder.getTraderContactId(), saleorder.getGroupContactPositions());

        //线下集采订单保存时将订单状态置为"待用户确认"
        if (SaleOrderTypeEnum.JCF.getType().equals(saleorder.getOrderType())) {
            saleorder.setStatus(SaleOrderStatusEnum.CUSTOMER_CONFIRMING.getStatus());
        } else {
            saleorder.setStatus(SaleOrderStatusEnum.CONFIRMING.getStatus());
        }

        // 是否含有账期支付
        saleorder.setHaveAccountPeriod(BigDecimal.ZERO.compareTo(saleorder.getAccountPeriodAmount()) != 0 ? 1 : 0);
        /**   VDERP-7741jc订单修改收货地址后导致下发wms地址不一致   **/
        TraderAddress traderAddress = traderAddressMapper.getAddressInfoById(saleorder.getTakeTraderAddressId(),null);
        saleorder.setTakeTraderAreaId(traderAddress.getAreaId());
        /**   VDERP-7741jc订单修改收货地址后导致下发wms地址不一致   **/
        Saleorder result = saleorderService.saveEditSaleorderInfo(saleorder, request, session);
        return result != null? result.getSaleorderId():null;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveJcoOrder(Saleorder order) {
        Objects.requireNonNull(order, "提交的订单信息不能为空");
        Preconditions.checkArgument(StringUtils.isNotEmpty(order.getSaleorderNo()), "集采订单线上下单时订单号为空");

        Saleorder orderQuery = saleorderMapper.getSaleorderByOrderNo(order.getSaleorderNo());
        if (orderQuery != null) {
            logger.info("保存线上集采订单时订单信息已经存在 - orderNo: {}", orderQuery.getSaleorderNo());
            throw new IllegalStateException("订单信息已存在重复下单");
        }

        //校验收票客户信息是否存在且未被禁用
        checkInvoiceSubjectIfExist(order.getInvoiceTraderId());

        //===============================================================================================收货客户信息 Begin

        // 收货客户编号
        Integer deliveryTraderId = order.getTakeTraderId();
        //收货人姓名
        String deliveryUserName = order.getTakeTraderContactName();
        //收货人手机号
        String deliveryUserMobile = order.getTakeTraderContactMobile();

        //获取联系人信息
        TraderContactGenerate traderContactGenerate = getOrSaveContactInfoIfNotExist(deliveryTraderId, deliveryUserMobile, deliveryUserName);
        order.setTakeTraderContactId(traderContactGenerate.getTraderContactId());

        // 收货地区最小级ID
        Integer minDeliveryAddressId = order.getTakeTraderAreaId();
        //收货人详细地址
        String deliveryUserDetailAddress = order.getTakeTraderAddress();

        //获取联系地址信息
        TraderAddress addressQuery = getOrSaveAddressInfoIfNotExist(deliveryTraderId, minDeliveryAddressId, deliveryUserDetailAddress);
        order.setTakeTraderAddressId(addressQuery.getTraderAddressId());
        // 收货省市区
        order.setTakeTraderArea(regionMapper.getRegionNameStringByMinRegionId(minDeliveryAddressId));


        //===============================================================================================收货客户信息 End

        //检验并替换注册用户信息为联系人信息
        checkAndReplaceSubmittedAccountInfo(order, true);

        //前台下单区下单单联系人编号
        order.setCreator(order.getTraderContactId());

        //设置终客户信息
        populateTerminalCustomerInfo(order);

        //===============================================================================================收票信息 Begin

        //收票人名称
        String invoiceUserName = order.getInvoiceTraderContactName();
        // 默认为客户手机号
        String invoiceUserMobile =  order.getInvoiceTraderContactMobile();
        //获取联系人信息
        TraderContactGenerate invoiceContactQuery = getOrSaveContactInfoIfNotExist(order.getInvoiceTraderId(), invoiceUserMobile, invoiceUserName);
        order.setInvoiceTraderContactId(invoiceContactQuery.getTraderContactId());
        // 收票地址 最小地址Id
        Integer minInvoiceAddressId = order.getInvoiceTraderAreaId();
        // 收票人详细地址
        String invoiceDetailAddress = order.getInvoiceTraderAddress();

        //获取联系地址信息
        TraderAddress invoiceAddressQuery = getOrSaveAddressInfoIfNotExist(order.getInvoiceTraderId(), minInvoiceAddressId, invoiceDetailAddress);
        order.setInvoiceTraderAddressId(invoiceAddressQuery.getTraderAddressId());
        // 收票人省市区
        order.setInvoiceTraderArea(regionMapper.getRegionNameStringByMinRegionId(minInvoiceAddressId));

        //=============================================================================================== 收票信息 End

        // 根据客户ID查询客户信息(集采订单由于订单结算主体为"收票人"，为了不影响老代码，其用新字段保存客户编号)
        final Integer traderIdToUse = SaleOrderTypeEnum.isJcOrder(order.getOrderType()) ? order.getGroupCustomerId() : order.getTraderId();
        TraderCustomerVo customer = traderCustomerService.getCustomerInfo(traderIdToUse);
        if (order.getTotalAmount().compareTo(customer.getAccountPeriodLeft()) < 1) {
            order.setPeriodDay(customer.getPeriodDay());
            order.setPaymentType(OrderConstant.PREPAY_0_PERCENT);
            order.setHaveAccountPeriod(1);
            order.setPrepaidAmount(new BigDecimal(0));
            order.setAccountPeriodAmount(order.getTotalAmount());
        }
        //设置默认支付信息
        setDefaultPaymentInfo(order);
/*
VDERP-6869【集采订单】JCO、JCF支持票货同行

        //如果是自动自动电子发票 则票货地址一致
        if(order.getInvoiceMethod() != null && order.getInvoiceMethod() == 3){
            order.setIsSameAddress(CommonConstants.ON);
        }
*/

        // 订单来源 - 前台下单
        order.setSource(OrderConstant.ORDER_SOURCE_WEB);
        order.setCompanyId(ErpConst.NJ_COMPANY_ID);
        // 设置订单类型 - 集采线上订单
        order.setOrderType(SaleOrderTypeEnum.JCO.getType());
        order.setStatus(SaleOrderStatusEnum.CONFIRMING.getStatus());

        // 插入订单
        order.setIsSendInvoice(order.getIsApplyInvoice());

        // 设置订单归属人默认为客户对应的归属销售
        User customerOwner = saleorderMapper.getUserInfoByTraderId(order.getTraderId());
        if (customerOwner != null) {
            order.setUserId(customerOwner.getUserId());
            order.setOrgId(customerOwner.getOrgId());
            order.setOrgName(customerOwner.getOrgName());
        }
        //VDERP-7306, 新增默认为2 产品开票
        //Integer billPeriodSettlementType = order.getBillPeriodSettlementType()==null || order.getBillPeriodSettlementType() ==0 ?2:order.getBillPeriodSettlementType();
        order.setBillPeriodSettlementType(CustomerBillPeriodSettlementTypeEnum.ORDER_INVOICE.getCode());

        //设置订单确认时间 确认状态
        order.setConfirmTime(new Date());
        order.setConfirmStatus(ErpConst.STATUS_STATE.SOME);
        boolean success = saleorderMapper.insertSelective(order) > 0;
        if (!success) {
            logger.info("保存集采订单失败,插入数据库失败,订单号:{}", order.getSaleorderNo());
            throw new IllegalStateException(String.format("保存集采订单[orderNo:%s]至数据库信息失败", order.getSaleorderNo()));
        }
       //发送站内消息
        if(order.getTraderId() != null && order.getSaleorderNo() != null && order.getSaleorderId() != null) {
            //根据客户Id查询客户负责人
            List<Integer> userIdList = userMapper.getUserIdListByTraderId(order.getTraderId(), ErpConst.ONE);
            Map<String, String> map = new HashMap<>();
            map.put("saleorderNo", order.getSaleorderNo());
            MessageUtil.sendMessage(176, userIdList, map, "./order/jc/view.do?saleorderId=" + order.getSaleorderId());
        }
        //保存商品信息
        if (CollectionUtils.isNotEmpty(order.getGoodsList())) {
            for (SaleorderGoods currentGoods : order.getGoodsList()) {
                currentGoods.setSaleorderId(order.getSaleorderId());
                currentGoods.setDeliveryCycle("14天");
                currentGoods.setAddTime(System.currentTimeMillis());
                // 插入商品
                int j = saleorderGoodsMapper.insertSelective(currentGoods);

                //更新产品快照信息
                saleorderGoodsMapper.updateSaleorderGoodsSnapshotInfo(currentGoods);

                {
                    // 添加内部备注默认值
                    ComponentRelation componentRelation = ComponentRelation
                            .builder()
                            .scene(Integer.valueOf(0))
                            .relationId(order.getSaleorderId())
                            .skuNo(currentGoods.getSku())
                            .skuName(currentGoods.getGoodsName())
                            .time(0L)
                            .build();
                    logger.info("集采添加内部备注默认值 - ComponentRelation:{}", componentRelation);
                    saleorderGoodsMapper.insertInnerInsideDefault(componentRelation);
                }
                if (j <= 0) {
                    logger.info("保存线上集采订单商品信息发生错误 - 订单号:{}", order.getSaleorderNo());
                    throw new IllegalStateException("保存订单商品信息发生错误");
                }
            }
        }

        order.setOperateType(StockOperateTypeConst.START_ORDER);
        int i1 = warehouseStockService.updateOccupyStockService(order,0);
        try {
            logicalSaleorderChooseServiceImpl.chooseLogicalSaleorder(order,customerOwner);
        } catch (Exception e) {
            logger.error("并保存线上集采订单时销售单指定逻辑仓发生错误 - orderNo: {}", order.getSaleorderNo());
            throw new IllegalStateException("保存订单商品信息发生错误", e);
        }
        //VDERP-2263   订单售后采购改动通知
        orderCommonService.updateSaleOrderDataUpdateTime(order.getSaleorderId(),null, OrderDataUpdateConstant.SALE_ORDER_GENERATE);
        return i1;
    }


    private void setDefaultPaymentInfo(Saleorder saleorder) {
        // 100%预付--其他付款计划：均设置默认值
        if (OrderConstant.PREPAY_100_PERCENT.equals(saleorder.getPaymentType())) {
            BigDecimal defaultAmount = new BigDecimal(0.00);
            // 账期支付金额
            saleorder.setAccountPeriodAmount(defaultAmount);
            // 账期天数
            saleorder.setPeriodDay(0);
            // 物流代收0否 1是
            saleorder.setRetainageAmount(defaultAmount);// 尾款
            saleorder.setLogisticsCollection(0);
            // 尾款期限(月)
            saleorder.setRetainageAmountMonth(0);
        }
        if(saleorder.getBillPeriodSettlementType() == null || saleorder.getBillPeriodSettlementType() == 0){
            // 默认结算方式
            saleorder.setBillPeriodSettlementType(CustomerBillPeriodSettlementTypeEnum.ORDER_INVOICE.getCode());
        }
    }

     @Override
    public List<TraderContactGenerate> listContactWithGroupCustomer(Integer traderId) {
        if (traderId == null) {
            return Collections.emptyList();
        }

        return traderContactGenerateMapper.getTraderContactListInWebAccountByTraderId(traderId);
    }


    /**
     * 返回集团客户总部客户编号（目前只有二级层级结构）
     *
     * @param traderId
     * @return
     */
    private Integer getGroupHeadId(Integer traderId) {
        if (traderId == null) {
            return null;
        }
        Integer targetId = null;
        Trader traderQuery = traderMapper.getTraderByTraderId(traderId);
        if (traderQuery != null && Objects.equals(traderQuery.getBelongPlatform(), BelongPlatformEnum.JC.getBelong())) {
            boolean isHead = traderQuery.getParentId() == null || traderQuery.getParentId() == 0;
            targetId = isHead?traderQuery.getTraderId():traderQuery.getParentId();
        }
        return targetId;
    }

    @Override
    public String getRoleValueOfJcAccount(String mobile){
        AccountResponse response = webAccountService.getJcAccountInfo(mobile);
        if (response != null && response.getRole() != null) {
            return response.getRole()
                    .stream()
                    .map(role -> {
                        for (PlatformRoleEnum var : PlatformRoleEnum.values()){
                            if (var.getCode() == role){
                                return var.getName();
                            }
                        }
                        return null;
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining("/"));
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Saleorder saveJcfOrderAfterSelectingCustomer(Saleorder saleorder, HttpServletRequest request, HttpSession session) {
        User currentUser = (User) session.getAttribute(ErpConst.CURR_USER);
        if (currentUser == null) {
            throw new IllegalStateException("保存集采线下订单时，登录用户信息失效");
        }

        // 订单结算主体为收票客户
        saleorder.setGroupCustomerId(saleorder.getTraderId());

        populateTerminalCustomerInfo(saleorder);

        //获取联系人信息
        TraderContactGenerate invoiceContactQuery = getOrSaveContactInfoIfNotExist(saleorder.getTraderId(), saleorder.getTraderContactMobile(),
                saleorder.getTraderContactName());
        saleorder.setTraderContactId(invoiceContactQuery.getTraderContactId());
        //Set 发票类型- 13%增值税专用发票
        saleorder.setInvoiceType(OrderConstant.INVOICE_TYPE_13_PERCENT_SPECIAL);
        saleorder.setAddTime(System.currentTimeMillis());
        saleorder.setCreator(currentUser.getUserId());
        saleorder.setCreatorOrgId(currentUser.getOrgId());
        saleorder.setCreatorOrgName(currentUser.getOrgName());
        saleorder.setCompanyId(currentUser.getCompanyId());
        saleorder.setOrderType(SaleOrderTypeEnum.JCF.getType());
        saleorder.setStatus(SaleOrderStatusEnum.CUSTOMER_CONFIRMING.getStatus());
        return saleorderService.saveAddSaleorderInfo(saleorder, request, session);
    }


    private void populateTerminalCustomerInfo(Saleorder orderInfo) {
        TraderCustomer traderCustomer = new TraderCustomer();
        traderCustomer.setTraderId(orderInfo.getGroupCustomerId());
        TraderCustomerVo customerQuery = traderCustomerService.getTraderCustomerBaseInfo(traderCustomer);
        if (customerQuery != null && SysOptionConstant.CUSTOMER_NATURE_TERMINAL.equals(orderInfo.getCustomerNature())) {
            // 终端类型
            orderInfo.setTerminalTraderType(customerQuery.getCustomerType());
            // 终端名称
            orderInfo.setTerminalTraderName(customerQuery.getTrader().getTraderName());
            // 销售区域
            orderInfo.setSalesArea((String) regionService.getRegion(customerQuery.getAreaId(), 2));
            Region region1 = regionService.getRegionByRegionId(customerQuery.getAreaId());
            if(region1 == null || region1.getRegionCode() == null){
                logger.info("订单创建查询销售区域id查询为空，客户id:{}",orderInfo.getTraderId());
            }else {
                orderInfo.setSalesAreaId(Integer.parseInt(region1.getRegionCode()));
            }
        }
    }


    private void updateContactPositionIfExist(Integer webAccountId, String position) {
        if (webAccountId != null && !Strings.isNullOrEmpty(position)) {
            WebAccount webAccountQuery = webAccountMapper.getWebAccountInfo(webAccountId);
            if(webAccountQuery == null || webAccountQuery.getTraderId() == null) {
                return;
            }

            TraderContactGenerate contactQuery = traderContactService.getByTraderIdAndMobileNo(webAccountQuery.getTraderId(), webAccountQuery.getMobile());
            if (contactQuery==null) {
                return;
            }

            TraderContactGenerate traderContactGenerate = new TraderContactGenerate();
            traderContactGenerate.setTraderContactId(contactQuery.getTraderContactId());
            traderContactGenerate.setPosition(position);
            traderContactService.updateByPrimaryKeySelective(traderContactGenerate);
        }

    }


    private void checkAndReplaceSubmittedAccountInfo(Saleorder orderInfo, boolean replaceAddressIfNecessary) {
        //创建订单用户手机号
        String creatorMobileNo = orderInfo.getTraderContactMobile();

        WebAccount webAccountQuery = webAccountService.getByMobileNo(creatorMobileNo);
        if (webAccountQuery == null || CommonConstants.DISABLE.equals(webAccountQuery.getIsEnable())) {
            throw new IllegalArgumentException(String.format("保存集采订单时未查询下单人[mobileNo: %s]信息或已被禁用", creatorMobileNo));
        }

        if (webAccountQuery.getTraderId() == null || webAccountQuery.getTraderId() == 0) {
            LOGGER.error("保存集采订单时注册用户为关联客户 - erpAccountId: {}, traderId: {}", webAccountQuery.getErpAccountId() ,webAccountQuery.getTraderId());
            throw new IllegalArgumentException("保存集采订单时注册用户关联的客户为空");
        }

        // 根据traderId查询所属客户
        TraderCustomerVo traderCustomer = traderCustomerMapper.getCustomerInfo(webAccountQuery.getTraderId());
        if (traderCustomer == null || CommonConstants.DISABLE.equals(traderCustomer.getIsEnable())) {
            logger.error("保存集采订单时属未查询穿创建订单客户或改客户已被警用 - orderNo:{}, traderId:{}",orderInfo.getSaleorderNo(), webAccountQuery.getTraderId());
            throw new IllegalArgumentException("集采订单结算主体为收票客户，保存订单信息时售票客户id为空");
        }

        //保存创建客户ID
        orderInfo.setGroupCustomerId(webAccountQuery.getTraderId());
        // 下单人客户人类型
        orderInfo.setCustomerType(traderCustomer.getCustomerType());
        // 下单人客户人性质
        orderInfo.setCustomerNature(traderCustomer.getCustomerNature());

        final String accountName = StringUtils.defaultIfBlank(orderInfo.getTraderContactName(), webAccountQuery.getName());
        TraderContactGenerate traderContactQuery = getOrSaveContactInfoIfNotExist(webAccountQuery.getTraderId(), creatorMobileNo, accountName);
        orderInfo.setTraderContactId(traderContactQuery.getTraderContactId());
        orderInfo.setTraderContactName(traderContactQuery.getName());
        orderInfo.setTraderContactMobile(traderContactQuery.getMobile());
        //保存创建订单人手机号
        orderInfo.setCreateMobile(traderContactQuery.getMobile());

        //如果用收货联系地址信息替换客户信息
        if (replaceAddressIfNecessary) {
            orderInfo.setTraderAreaId(orderInfo.getTakeTraderAreaId());
            orderInfo.setTraderArea(orderInfo.getTakeTraderArea());
            orderInfo.setTraderAddressId(orderInfo.getTakeTraderAddressId());
            orderInfo.setTraderAddress(orderInfo.getTakeTraderAddress());
        }
    }


    private void checkInvoiceSubjectIfExist(Integer invoiceTraderId) {
        //订单结算主体为收票客户
        if (invoiceTraderId == null || invoiceTraderId== 0) {
            throw new IllegalArgumentException("集采订单结算主体为收票客户，保存订单信息时售票客户id为空");
        }

        TraderCustomerVo invoiceCustomerQuery = traderCustomerMapper.getCustomerInfo(invoiceTraderId);
        if (invoiceCustomerQuery == null || CommonConstants.DISABLE.equals(invoiceCustomerQuery.getIsEnable())) {
            throw new IllegalStateException("保存集采订单时未查询到收票客户");
        }
    }


    private TraderContactGenerate getOrSaveContactInfoIfNotExist(Integer traderId, String mobile, String name) {
        if(traderId==null || StringUtils.isEmpty(mobile) || StringUtils.isEmpty(name)) {
            throw new IllegalArgumentException("参数不合法");
        }

        TraderContactGenerate contactQuery = traderContactService.getByTraderIdAndMobileNo(traderId, mobile);
        if (contactQuery != null && CommonConstants.ENABLE.equals(contactQuery.getIsEnable())) {
            return contactQuery;
        }
        TraderContactGenerate contactToSave = new TraderContactGenerate();
        contactToSave.setTraderId(traderId);
        contactToSave.setTraderType(CommonConstants.TRADER_TYPE_1);
        contactToSave.setName(name);
        contactToSave.setMobile(mobile);
        contactToSave.setIsEnable(CommonConstants.ENABLE);
        contactToSave.setAddTime(System.currentTimeMillis());
        traderContactService.insertSelective(contactToSave);
        return contactToSave;
    }


    private TraderAddress getOrSaveAddressInfoIfNotExist(Integer traderId, Integer areaId, String addressDetails) {
        if(traderId==null || areaId ==null || StringUtils.isEmpty(addressDetails)) {
            throw new IllegalArgumentException("参数不合法");
        }

        TraderAddress  addressQuery = traderAddressMapper.getAddressInfoByTraderIdAndAreaId(traderId, areaId,StringUtils.trim(addressDetails));
        if (addressQuery != null  ) {
            return addressQuery;
        }
        TraderAddress addressToSave = new TraderAddress();
        addressToSave.setTraderId(traderId);
        addressToSave.setTraderType(CommonConstants.TRADER_TYPE_1);
        addressToSave.setAreaId(areaId);
        addressToSave.setAddress(StringUtils.trim(addressDetails));
        addressToSave.setAreaIds(regionMapper.getRegionIdStringByMinRegionId(areaId));
        addressToSave.setIsEnable(CommonConstants.ENABLE);
        addressToSave.setAddTime(System.currentTimeMillis());
        traderAddressMapper.insertSelective(addressToSave);
        return addressToSave;
    }
}
