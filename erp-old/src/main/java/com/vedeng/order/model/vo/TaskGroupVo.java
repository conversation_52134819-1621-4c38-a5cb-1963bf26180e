package com.vedeng.order.model.vo;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2025/2/18
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class TaskGroupVo {

    /**
     * 日期，格式为 2025-02-18
     */
    private String dateStr;

    /**
     * 已完成的任务数量
     */
    private Integer doneCount;
    /**
     * 未完成的任务数量
     */
    private Integer undoneCount;

    /**
     * 关闭的任务数量
     */
    private Integer closeCount;
}
