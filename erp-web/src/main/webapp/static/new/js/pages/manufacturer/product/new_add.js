$(function () {
    // 生产企业生产许可证号或备案凭证编号
    jQuery.validator.addMethod("productCompanyLicence", function(value, element) {
        $(element).parent()
        return this.optional(element) || /^[\u4e00-\u9fa5a-zA-Z0-9]+$/.test(value);
    }, "只可包含中文、英文、数字");


    //日期空间初始化

    var Pikadays = [];
    $('.J-date').each(function (i) {
        var $this = $(this);
        Pikadays[i] = new Pikaday({
            format: 'yyyy-mm-dd',
            field: $(this)[0],
            firstDay: 1,
            yearRange: [1900, 2099],
            onSelect: function (date) {
                var date1 = new Date(date).valueOf();
                var validDate1 = new Date(date);
                var minValidDate = new Date(validDate1.setDate(validDate1.getDate()+1));
                if (i == 1){
                    Pikadays[2].setMinDate(date1);
                }
                if(i == 2){
                    Pikadays[1].setMaxDate(date1);
                }
                /*if (i == 0) {
                    Pikadays[1].setMinDate(minValidDate);
                }
                if(i == 1){
                    Pikadays[2].setMinDate(date1);
                    Pikadays[0].setMaxDate(date1);
                    Pikadays[0].setMinDate(date1);
                }
                if(i == 2){
                    Pikadays[1].setMaxDate(date1);
                }*/


                if(i>2){
                    var j=i%2;
                    if(j==1){
                        Pikadays[i+1].setMinDate(date1);
                    }else if(j==0){
                        Pikadays[i-1].setMaxDate(date1)
                    }
                }
            }
        });
    })
    var checkDateRange = function () {
        var val1 = $('.J-date').eq(0).val();
        var val2 = $('.J-date').eq(1).val();

        if (val1) {
            Pikadays[1].setMinDate(new Date($.trim(val1)));
        }

        if (val2) {
            Pikadays[0].setMaxDate(new Date($.trim(val2)));
        }
    };

    //checkDateRange();


    //上传组件初始化 上传营业执照  生产企业生产许可证或备案凭证 生产企业生产产品登记表
    timeout = null;
    var attachmentFunction = [1307,1306,1308,1305]; //营业执照（新） 生产企业生产许可证或备案凭证（新）  生产企业生产产品登记表(即登记表附近)（新）
    var attachmentsName = ['yzAttachments','scAttachments','rcAttachments','djbAttachments'];
    $('.J-upload').each(function (i) {
        var _this = this;
        new Upload({
            limit: 50,
            url: GLOBAL.IMGUPLOADURL,
            wrapper: $(this),
            uploadName: 'upload' + i,

            list: JSON.parse($(this).siblings('.J-upload-data').val() || '[]'),
            onchange: function () {
                $(_this).find('.J-upload-item').each(function (ii) {
                    var data = $(this).data('item');
                    $(this).find('.J-item-name').remove();
                    $(this).append('<input type="hidden" class="J-item-name" name="' + attachmentsName[i] + '[' + ii + '].attachmentFunction" value="' + attachmentFunction[i] + '">');
                    $(this).append('<input type="hidden" class="J-item-name" name="' + attachmentsName[i] + '[' + ii + '].uri" value="' + data.filePath +'">');
                    $(this).append('<input type="hidden" class="J-item-name" name="' + attachmentsName[i] + '[' + ii + '].domain" value="' + data.domain + '">');
                })
                if (i == 0 || i == 1 || i == 2) {
                    $(_this).find('[name^=upload]').valid();
                }
            },
            filters: {
                mime_types: [
                    { title: "Image files", extensions: "jpg,jpeg,png" }
                ],
                max_file_size: '5MB'
            },
            onError: function (error) {
                var errorMsg = {
                    TYPE: '上传图片格式为：JPG、PNG、JEPG格式',
                    SIZE: '图片大小不超过5M'
                }
                if (error) {
                    var $error = $(_this).siblings('.J-upload-error');
                    $error.show().find('label').html(errorMsg[error]).show();
                    timeout && clearTimeout(timeout);
                    timeout = setTimeout(function () {
                        $error.hide();
                    }, 3000)
                }
            }
        });
    })

    window.localStorage.removeItem('addsuccess');

    //增加关闭提示
    GLOBAL.addtip();

    var loadDataBySupplier = function (relateId, name) {
        $.ajax({
            url: page_url + '/goods/manufacturer/addProduct.do?manufacturerName=' + name,
            dataType: 'html',
            success: function (res) {
                $('#form_submit').html(res)

                window.localStorage.setItem('needShowTip', 'true');
                //操作提示
                GLOBAL.showGlobalTip('供应商列表中已存在该生产企业，部分资质信息已同步！', null, 'needShowTip');
            }
        })
    }

    var manufacturerId = $('#manufacturerId').val()
    if (manufacturerId === undefined || manufacturerId === "" || manufacturerId === null) {
        // 新增
        $('#manufacturerName').blur(function () {
            var _this = this;
            $.ajax({
                url: page_url + '/goods/manufacturer/checkManufacturerName.do?manufacturerName=' + $(_this).val(),
                dataType: 'json',
                success: function (res) {
                    if (res && res.code === 0) {
                        loadDataBySupplier(res.data, $(_this).val())
                    }
                }
            })
        })
    }


})
//点击取消
function cancel() {
    var _self=self;
    var dialog = artDialog.confirm('取消后，所填写的信息，系统将不做保存', '', {
        fn: function () {
            // $(window.parent.document).find('[role=presentation].active .glyphicon.small').click();
            window.parent.closableTab.close($(window.parent.document).find('[role=presentation].active .glyphicon.small'),window.parent.closableTab.resizeMove);
        }, text: '确定'
    }, {
        fn: function () {
            dialog.close();
        }, text: '取消'
    });

}



layui.use('laydate', function() {
    var laydate = layui.laydate;

    laydate.render({
        elem: '#bcIssueDate',
        format: 'yyyy-MM-dd',
        trigger: 'click'
    });


    var startTime1 = laydate.render({ //开始的日历
        elem: '#bcStartTime',
        format: 'yyyy-MM-dd',
        trigger: 'click',
        done: function (value, date) { //回调
            if (value !== '') { //设置开始日历的值为结束日历可选的最小值
                endTime1.config.min = {
                    year: date.year,
                    month: date.month - 1,
                    date: date.date
                }
            } else {
                endTime1.config.min = {
                    year: 1900,
                    month: 0, //关键
                    date: 1
                }
            }
        }
    });

    var endTime1 = laydate.render({ //结束的日历
        elem: '#bcEndTime',
        format: 'yyyy-MM-dd',
        trigger: 'click',
        done: function (value, date) { //回调
            if (value !== '') { //设置结束日历的值为开始日历可选的最大值
                startTime1.config.max = {
                    year: date.year,
                    month: date.month - 1,
                    date: date.date
                }

            } else {
                startTime1.config.max = {
                    year: 2099,
                    month: 0, //关键
                    date: 1
                }
            }
        }
    });

    var startTime2 = laydate.render({ //开始的日历
        elem: '#peStartTime',
        format: 'yyyy-MM-dd',
        trigger: 'click',
        done: function (value, date) { //回调
            if (value !== '') { //设置开始日历的值为结束日历可选的最小值
                endTime2.config.min = {
                    year: date.year,
                    month: date.month - 1,
                    date: date.date
                }
            } else {
                endTime2.config.min = {
                    year: 1900,
                    month: 0, //关键
                    date: 1
                }
            }
        }
    });

    var endTime2 = laydate.render({ //结束的日历
        elem: '#peEndTime',
        format: 'yyyy-MM-dd',
        trigger: 'click',
        done: function (value, date) { //回调
            if (value !== '') { //设置结束日历的值为开始日历可选的最大值
                startTime2.config.max = {
                    year: date.year,
                    month: date.month - 1,
                    date: date.date
                }

            } else {
                startTime2.config.max = {
                    year: 2099,
                    month: 0, //关键
                    date: 1
                }
            }
        }
    });


    var startTime3 = laydate.render({ //开始的日历
        elem: '#rcStartTime',
        format: 'yyyy-MM-dd',
        trigger: 'click',
        done: function (value, date) { //回调
            if (value !== '') { //设置开始日历的值为结束日历可选的最小值
                endTime3.config.min = {
                    year: date.year,
                    month: date.month - 1,
                    date: date.date
                }
            } else {
                endTime3.config.min = {
                    year: 1900,
                    month: 0, //关键
                    date: 1
                }
            }
        }
    });

    var endTime3 = laydate.render({ //结束的日历
        elem: '#rcEndTime',
        format: 'yyyy-MM-dd',
        trigger: 'click',
        done: function (value, date) { //回调
            if (value !== '') { //设置结束日历的值为开始日历可选的最大值
                startTime3.config.max = {
                    year: date.year,
                    month: date.month - 1,
                    date: date.date
                }

            } else {
                startTime3.config.max = {
                    year: 2099,
                    month: 0, //关键
                    date: 1
                }
            }
        }
    });

    var startTime4 = laydate.render({ //开始的日历
        elem: '#pepStartTime',
        format: 'yyyy-MM-dd',
        trigger: 'click',
        done: function (value, date) { //回调
            if (value !== '') { //设置开始日历的值为结束日历可选的最小值
                endTime4.config.min = {
                    year: date.year,
                    month: date.month - 1,
                    date: date.date
                }
            } else {
                endTime4.config.min = {
                    year: 1900,
                    month: 0, //关键
                    date: 1
                }
            }
        }
    });

    var endTime4 = laydate.render({ //结束的日历
        elem: '#pepEndTime',
        format: 'yyyy-MM-dd',
        trigger: 'click',
        done: function (value, date) { //回调
            if (value !== '') { //设置结束日历的值为开始日历可选的最大值
                startTime4.config.max = {
                    year: date.year,
                    month: date.month - 1,
                    date: date.date
                }

            } else {
                startTime4.config.max = {
                    year: 2099,
                    month: 0, //关键
                    date: 1
                }
            }
        }
    });


})




