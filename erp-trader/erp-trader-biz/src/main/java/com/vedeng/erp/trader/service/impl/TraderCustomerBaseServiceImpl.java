package com.vedeng.erp.trader.service.impl;

import static com.vedeng.order.model.dto.SaleorderUserInfoDto.B2B_ORGID;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import javax.annotation.Resource;

import com.vedeng.erp.system.dto.*;
import com.vedeng.erp.trader.dto.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.core.base.BaseResponseCode;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.common.redis.utils.RedisUtil;
import com.vedeng.common.trace.enums.ArchiveCategory;
import com.vedeng.common.trace.enums.EventTrackingEnum;
import com.vedeng.common.util.StringUtil;
import com.vedeng.erp.kingdee.dto.KingDeeCustomerDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeCustomerDto;
import com.vedeng.erp.kingdee.service.KingDeeCustomerApiService;
import com.vedeng.erp.saleorder.dao.SaleOrderMapper;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.erp.system.service.OrganizationApiService;
import com.vedeng.erp.system.service.RegionApiService;
import com.vedeng.erp.system.service.SysOptionDefinitionApiService;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.trader.common.enums.TraderEnum;
import com.vedeng.erp.trader.constant.TraderConstant;
import com.vedeng.erp.trader.domain.PublicCustomerRecord;
import com.vedeng.erp.trader.domain.dto.DistributionLinkDto;
import com.vedeng.erp.trader.domain.dto.DistributionVisitPlanDto;
import com.vedeng.erp.trader.domain.dto.RegionRulesQueryDto;
import com.vedeng.erp.trader.domain.dto.SubmitVisitPlanDto;
import com.vedeng.erp.trader.domain.dto.TerminalDistributionLinkRequestDto;
import com.vedeng.erp.trader.domain.dto.TerminalDistributionLinkResponseDto;
import com.vedeng.erp.trader.domain.dto.TerminalRequestDto;
import com.vedeng.erp.trader.domain.dto.TerminalResponseDto;
import com.vedeng.erp.trader.domain.dto.TraderCategoryBrandDto;
import com.vedeng.erp.trader.domain.dto.TraderCustomerActionDto;
import com.vedeng.erp.trader.domain.dto.TraderCustomerInfoDto;
import com.vedeng.erp.trader.domain.dto.TraderCustomerPortraitDto;
import com.vedeng.erp.trader.domain.dto.TraderCustomerRelationQueryDto;
import com.vedeng.erp.trader.domain.dto.TraderCustomerTerminalPortraitDto;
import com.vedeng.erp.trader.domain.dto.TraderPerfectTagDto;
import com.vedeng.erp.trader.domain.dto.VisitBatchDistributeDto;
import com.vedeng.erp.trader.domain.entity.AuthorizationApiApply;
import com.vedeng.erp.trader.domain.entity.CommunicateSummaryEntity;
import com.vedeng.erp.trader.domain.entity.QuoteorderEntity;
import com.vedeng.erp.trader.domain.entity.TraderCustomerCategoryEntity;
import com.vedeng.erp.trader.domain.entity.WebAccountEntity;
import com.vedeng.erp.trader.domain.vo.PublicCustomerRegionRulesVo;
import com.vedeng.erp.trader.domain.vo.TraderCustomerInfoVo;
import com.vedeng.erp.trader.feign.OneDataDistributorTerminalLinkApi;
import com.vedeng.erp.trader.feign.OneDataTerminalInfoServiceApi;
import com.vedeng.erp.trader.feign.OneDataTraderEventApi;
import com.vedeng.erp.trader.feign.OneDataTraderServiceApi;
import com.vedeng.erp.trader.feign.OneDataTraderTagApi;
import com.vedeng.erp.trader.feign.OneDataUserBehaviorApi;
import com.vedeng.erp.trader.mapper.TraderCommunicateSummaryMapper;
import com.vedeng.erp.trader.mapper.PublicCustomerRecordMapper;
import com.vedeng.erp.trader.mapper.PublicCustomerRegionRulesMapper;
import com.vedeng.erp.trader.mapper.QuoteorderMapper;
import com.vedeng.erp.trader.mapper.TraderCustomerAttributeMapper;
import com.vedeng.erp.trader.mapper.TraderCustomerBaseMapper;
import com.vedeng.erp.trader.mapper.TraderCustomerCategoryMapper;
import com.vedeng.erp.trader.mapper.TraderMapper;
import com.vedeng.erp.trader.mapper.WebAccountMapper;
import com.vedeng.erp.trader.service.CommunicateRecordService;
import com.vedeng.erp.trader.service.TraderAddressApiService;
import com.vedeng.erp.trader.service.TraderContactService;
import com.vedeng.erp.trader.service.TraderCustomerApiService;
import com.vedeng.erp.trader.service.TraderCustomerBaseService;
import com.vedeng.erp.trader.service.TraderCustomerMarketingApiService;
import com.vedeng.erp.trader.service.TraderCustomerMarketingPrincipalApiService;
import com.vedeng.erp.trader.service.TraderCustomerTerminalApiService;
import com.vedeng.erp.trader.service.VisitRecordApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import com.vedeng.infrastructure.tyc.domain.dto.TycResultDto;
import com.vedeng.infrastructure.tyc.service.TycSearchService;
import com.vedeng.onedataapi.api.archived.req.TraderArchivedReqDto;
import com.vedeng.onedataapi.api.archived.res.TraderArchivedResDto;
import com.vedeng.onedataapi.api.archived.res.TraderArchivedResDto.TraderArchivedDto;
import com.vedeng.onedataapi.api.link.req.DistributorLinkReqV2Dto;
import com.vedeng.onedataapi.api.link.req.TerminalLibLinkReqDto;
import com.vedeng.onedataapi.api.link.req.TerminalLinkReqV2Dto;
import com.vedeng.onedataapi.api.link.res.DistributorLinkResV2;
import com.vedeng.onedataapi.api.link.res.TerminalLinkResV2;
import com.vedeng.onedataapi.api.link.res.TraderInfoV2;
import com.vedeng.onedataapi.api.terminal.req.TerminalListReqDto;
import com.vedeng.onedataapi.api.terminal.req.TerminalReqDto;
import com.vedeng.onedataapi.api.terminal.req.TerminalTypeSearchDto;
import com.vedeng.onedataapi.api.terminal.res.TerminalDataRes;
import com.vedeng.onedataapi.api.terminal.res.TerminalPageItemRes;
import com.vedeng.onedataapi.api.terminal.res.TerminalPageRes;
import com.vedeng.onedataapi.api.terminal.res.TerminalRes;
import com.vedeng.onedataapi.api.trader.req.TraderListReqDto;
import com.vedeng.onedataapi.api.trader.res.TraderPageItemRes;
import com.vedeng.onedataapi.api.trader.res.TraderPageRes;
import com.vedeng.onedataapi.api.tradertag.req.TraderTagApiReq;
import com.vedeng.onedataapi.api.tradertag.res.TraderDecisionTag;
import com.vedeng.onedataapi.api.tradertag.res.TraderInquiryTag;
import com.vedeng.onedataapi.api.tradertag.res.TraderPerfectTag;
import com.vedeng.onedataapi.api.tradertag.res.TraderSaleActionTag;
import com.vedeng.onedataapi.api.tradertag.res.TraderTransactionTag;
import com.vedeng.onedataapi.api.usertrace.req.UserBehaviorDto;
import com.vedeng.onedataapi.api.usertrace.res.UserBehaviorResDto;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 */
@Service
public class TraderCustomerBaseServiceImpl implements TraderCustomerBaseService, TraderCustomerApiService {

    private static final Logger logger = LoggerFactory.getLogger(TraderCustomerBaseServiceImpl.class);

    @Resource
    TraderCustomerBaseMapper traderCustomerBaseMapper;

    @Resource
    TraderCustomerCategoryMapper newTraderCustomerCategoryMapper;

    @Resource
    private PublicCustomerRecordMapper publicCustomerRecordMapper;

    @Autowired
    private RegionApiService regionApi;

    @Autowired
    private UserApiService userApiService;

    @Autowired(required = false)
    private KingDeeCustomerApiService kingDeeCustomerApiService;

    @Autowired
    private RegionApiService regionApiService;

    @Autowired
    @Qualifier("newTraderCustomerAttributeMapper")
    private TraderCustomerAttributeMapper traderCustomerAttributeMapper;

    @Autowired
    private OneDataTraderTagApi oneDataTraderTagApi;

    @Autowired
    private OneDataTerminalInfoServiceApi oneDataTerminalInfoServiceApi;

    @Autowired
    private OneDataTraderEventApi oneDataTraderEventApi;

    @Autowired
    private SysOptionDefinitionApiService sysOptionDefinitionApiService;

    @Autowired(required = false)
    private CommunicateRecordService communicateRecordService;

    @Autowired
    private TraderContactService traderContactService;

    @Autowired
    private TraderAddressApiService traderAddressApiService;

    @Autowired
    private TraderCustomerMarketingApiService traderCustomerMarketingApiService;

    @Autowired
    @Qualifier("newTraderMapper")
    private TraderMapper traderMapper;

    @Autowired
    private OrganizationApiService organizationApiService;

    @Autowired
    private OneDataUserBehaviorApi oneDataUserBehaviorApi;

    @Autowired(required = false)
    private TraderCustomerTerminalApiService traderCustomerTerminalApiService;

    @Autowired
    @Qualifier("newWebAccountMapper")
    WebAccountMapper webAccountMapper;

    @Autowired
    private OneDataDistributorTerminalLinkApi oneDataDistributorTerminalLinkApi;

    @Autowired
    private TycSearchService tycSearchService;

    @Autowired
    private TraderCustomerMarketingPrincipalApiService traderCustomerMarketingPrincipalApiService;

    @Autowired
    @Qualifier("traderCommunicateSummaryMapper")
    private TraderCommunicateSummaryMapper traderCommunicateSummaryMapper;

    @Resource
    private PublicCustomerRegionRulesMapper publicCustomerRegionRulesMapper;

    @Resource
    private OneDataTraderServiceApi oneDataTraderServiceApi;

    @Autowired
    private VisitRecordApiService visitRecordApiService;
    
    @Autowired
    private QuoteorderMapper quoteorderMapper;
    
    @Autowired
    private SaleOrderMapper saleOrderMapper;
    

    /**
     * 客户性质为466则为终端
     */
    private static final Integer TERMINAL = 466;

    private static final String SUMMARY_REDIS_KEY = "trader:summary:";

    @Override
    public List<TraderCustomerInfoDto> getTraderRelationInfo(TraderCustomerRelationQueryDto traderQueryDto) {
        List<TraderCustomerInfoDto> relatedTraderList = new ArrayList<>();
        TraderCustomerInfoDto traderCustomerInfoDto = traderCustomerBaseMapper.getTraderCutomerInfoById(traderQueryDto.getTraderCustomerId());
        if (Objects.isNull(traderCustomerInfoDto) || Objects.isNull(traderCustomerInfoDto.getAssociatedCustomerGroup()) || traderCustomerInfoDto.getAssociatedCustomerGroup().equals(0L)) {
            return relatedTraderList;
        }
        relatedTraderList = traderCustomerBaseMapper.getRelatedTraderByGroupId(traderCustomerInfoDto.getAssociatedCustomerGroup());
        return relatedTraderList.stream().filter(e -> !e.getTraderId().equals(traderQueryDto.getTraderId())).collect(Collectors.toList());
    }

    @Override
    public String associateTraderCustomer(Integer traderCustomerId, Integer associatedTraderCustomerId) {
        TraderCustomerInfoDto traderCustomer = traderCustomerBaseMapper.getTraderCutomerInfoById(traderCustomerId);
        TraderCustomerInfoDto associatedTraderCustomer = traderCustomerBaseMapper.getTraderCutomerInfoById(associatedTraderCustomerId);
        if (associatedTraderCustomer.getAssociatedCustomerGroup() > 0) {
            return "该客户已被关联";
        }
        //判断这两个客户是否处于锁定并且未解锁成功的状态
        List<Integer> traderCustomerIdList = Arrays.asList(traderCustomerId, associatedTraderCustomerId);
        if (this.checkTraderCustomerIsPrivatizedAndNotUnlocked(traderCustomerIdList)) {
            return "该客户尚未解锁，请解锁后再关联客户";
        }
        Long associatedCustomerGroup = traderCustomer.getAssociatedCustomerGroup() == 0 ? System.currentTimeMillis() :
                traderCustomer.getAssociatedCustomerGroup();
        traderCustomerBaseMapper.updateAssociatedCustomerGroupOfCustomer(Arrays.asList(traderCustomerId, associatedTraderCustomerId), associatedCustomerGroup);
        logger.info("客户：{}关联了客户：{}", traderCustomerId, associatedTraderCustomerId);
        return null;
    }

    @Override
    public Boolean checkTraderCustomerIsPrivatizedAndNotUnlocked(List<Integer> traderCustomerIdList) {
        for (Integer item : traderCustomerIdList) {
            List<PublicCustomerRecord> publicCustomerRecordList = publicCustomerRecordMapper.getPublicCustomerRecordListByTraderCustomerId(item);
            if (CollectionUtils.isNotEmpty(publicCustomerRecordList)) {
                PublicCustomerRecord lastPublicCustomerRecord = publicCustomerRecordList.get(publicCustomerRecordList.size() - 1);
                //如果客户当前公海记录处于锁定未解锁成功状态，则不能进行客户关联操作
                if (lastPublicCustomerRecord.getIsPrivatized() == 1 && lastPublicCustomerRecord.getIsUnlock() == 0) {
                    return true;
                }
            }
        }
        return false;
    }


    @Override
    public String cancelAssociateTraderCustomer(Integer traderCustomerId) {
        TraderCustomerInfoDto traderCustomer = traderCustomerBaseMapper.getTraderCutomerInfoById(traderCustomerId);
        if (traderCustomer.getAssociatedCustomerGroup() == 0) {
            return "该客户已被取消关联";
        }
        List<Integer> toUpdateTraderCustomerList = new ArrayList<>();
        //A取消关联B，如果同一组下只剩下A和B，则同时更新associatedCustomerGroup为0；否则只更新B的associatedCustomerGroup为0
        List<Integer> allTraderCustomerOfAssociatedCustomerGroup =
                traderCustomerBaseMapper.getTraderCustomerListByAssociatedCustomerGroup(traderCustomer.getAssociatedCustomerGroup());
        if (allTraderCustomerOfAssociatedCustomerGroup.size() <= 2) {
            toUpdateTraderCustomerList = allTraderCustomerOfAssociatedCustomerGroup;
        } else {
            toUpdateTraderCustomerList.add(traderCustomerId);
        }
        traderCustomerBaseMapper.updateAssociatedCustomerGroupOfCustomer(toUpdateTraderCustomerList, 0L);
        logger.info("客户：{}被取消关联", traderCustomerId);
        return null;
    }

    @Override
    public TraderCustomerInfoVo getTraderCustomerInfoVo(Integer traderId) {

        if (Objects.isNull(traderId)) {
            return null;
        }
        TraderCustomerInfoVo traderCustomerInfoVo = traderCustomerBaseMapper.getTraderCustomerInfoVo(traderId);
        addressAndType(traderCustomerInfoVo);
        return traderCustomerInfoVo;
    }

    @Override
    public TraderCustomerInfoVo getTraderCustomerInfo(Integer traderId) {
        TraderCustomerInfoVo traderCustomerInfo = traderCustomerBaseMapper.getTraderCustomerInfo(traderId);
        return traderCustomerInfo;
    }
    /**
     * 设置地址 以及类型
     *
     * @param traderCustomerInfoVo 对象
     */
    private void addressAndType(TraderCustomerInfoVo traderCustomerInfoVo) {
        if (traderCustomerInfoVo != null) {
            traderCustomerInfoVo.setCustomerCategories(getCustomerCategory(traderCustomerInfoVo.getTraderCustomerCategoryId()));

            List<TraderCustomerCategoryEntity> customerCategories = traderCustomerInfoVo.getCustomerCategories();
            StringBuilder sb = new StringBuilder();
            if (!org.springframework.util.CollectionUtils.isEmpty(customerCategories)) {
                for (TraderCustomerCategoryEntity customerCategory : customerCategories) {
                    sb = sb.append(customerCategory.getCustomerCategoryName()).append(" ");
                }
            } else {
                sb.append(traderCustomerInfoVo.getCustomerTypeStr()).append(" ").append(traderCustomerInfoVo.getCustomerNatureStr());
            }
            traderCustomerInfoVo.setCustomerTypeStr(sb.toString());
            // 处理地址 最小地址 想上 查到最大级  中国 江苏 南京 秦淮
            if (Objects.nonNull(traderCustomerInfoVo.getAreaId())) {
                String thisRegionToParentRegion = regionApi.getThisRegionToParentRegion(traderCustomerInfoVo.getAreaId());
                traderCustomerInfoVo.setAddress(thisRegionToParentRegion);
            }
        }
    }



    @Override
    public List<TraderCustomerInfoVo> queryTraderCustomerInfoByNameAndUserIdForCrm(Integer limit, String name,Integer userId,boolean belong) {
        List<TraderCustomerInfoVo> traderCustomerInfoVoByName = traderCustomerBaseMapper.getTraderCustomerInfoVoByNameAndUserIdForCRM(7, name,userId,belong);
        return traderCustomerInfoVoByName;
    }

    @Override
    public List<TraderCustomerInfoVo> queryTraderCustomerInfoByNameAndUserIdForCrmShared(Integer limit, String name,Integer userId,List<Integer> notIncludeTraderIds,boolean share){
        List<TraderCustomerInfoVo> traderCustomerInfoVoByName = traderCustomerBaseMapper.getTraderCustomerInfoVoByNameAndUserIdForCRMShared(limit, name,userId,notIncludeTraderIds,share);
        return traderCustomerInfoVoByName;
    }



    @Override
    public List<TraderCustomerInfoVo> queryTraderCustomerInfoByNameForCrm(Integer limit, String name) {

        CurrentUser currentUser = CurrentUser.getCurrentUser();

        List<TraderCustomerInfoVo> traderCustomerInfoVoByName = traderCustomerBaseMapper.getTraderCustomerInfoVoByNameForCRM(7, name);
        return traderCustomerInfoVoByName;
    }

    @Override
    public List<TraderCustomerInfoVo> queryTraderCustomerInfoByName(Integer limit, String name) {

        CurrentUser currentUser = CurrentUser.getCurrentUser();

        List<TraderCustomerInfoVo> traderCustomerInfoVoByName = traderCustomerBaseMapper.getTraderCustomerInfoVoByName(7, name, currentUser.getId(), true);
        if (CollectionUtils.isNotEmpty(traderCustomerInfoVoByName)) {
            traderCustomerInfoVoByName.forEach(this::accept);

        }

        if (CollectionUtils.isEmpty(traderCustomerInfoVoByName) || traderCustomerInfoVoByName.size() < limit) {
            int num = 0;
            if (CollectionUtils.isNotEmpty(traderCustomerInfoVoByName)) {
                num = traderCustomerInfoVoByName.size();
            }
            List<TraderCustomerInfoVo> other = traderCustomerBaseMapper.getTraderCustomerInfoVoByName(limit - num, name, currentUser.getId(), false);
            if (CollectionUtils.isNotEmpty(other)) {
                other.forEach(this::accept);
                traderCustomerInfoVoByName.addAll(other);
            }
        }

        traderCustomerInfoVoByName = traderCustomerInfoVoByName.stream().sorted(Comparator.comparing(TraderCustomerInfoVo::isBelong).reversed()).collect(Collectors.toList());


        return traderCustomerInfoVoByName;
    }

    /**
     * 设置地址以及判断销售归属 排序
     *
     * @param c 对象
     */
    private void accept(TraderCustomerInfoVo c) {

        addressAndType(c);
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        UserDto userDto = UserDto.builder().isAdmin(currentUser.getIsAdmin())
                .username(currentUser.getUsername())
                .companyId(currentUser.getCompanyId())
                .positType(currentUser.getPositType())
                .userId(currentUser.getId())
                .build();
        List<UserDto> allSubUserList = userApiService.getAllSubUserList(userDto, Collections.singletonList(310), true);
        if (CollectionUtils.isNotEmpty(allSubUserList)) {
            List<Integer> userIds = allSubUserList.stream().map(UserDto::getUserId).collect(Collectors.toList());
            c.setBelong(Objects.nonNull(currentUser.getId()) && userIds.contains(c.getSaleId()));
        }
    }

    @Override
    public List<TraderCustomerCategoryEntity> getCustomerCategory(Integer traderCustomerCategoryId) {

        List<TraderCustomerCategoryEntity> categoryList = new ArrayList<>();
        if (traderCustomerCategoryId == null) {

        }
        TraderCustomerCategoryEntity customerCategory = newTraderCustomerCategoryMapper.getTraderCustomerCategoryById(traderCustomerCategoryId);

        // 健壮性
        if (customerCategory != null) {
            Integer parentId = customerCategory.getParentId();
            categoryList.add(customerCategory);
            do {
                TraderCustomerCategoryEntity customerCate = newTraderCustomerCategoryMapper.getTraderCustomerCategoryById(parentId);
                if (customerCate != null) {
                    parentId = customerCate.getParentId();
                    categoryList.add(customerCate);
                } else {
                    // 避免死循环
                    parentId = null;
                }

            } while (parentId != null && parentId > 0);

            Collections.reverse(categoryList);
        }

        return categoryList;
    }

    @Override
    public Integer getTraderCustomerUserIdByTraderId(Integer traderId) {
        if (Objects.isNull(traderId)) {
            return null;
        }
        return traderCustomerBaseMapper.getTraderCustomerUserIdByTraderId(traderId);
    }

    @Override
    public List<TraderCustomerDto> selectPushKingDeeTraderCustomerData(Long begin, Long end, Integer limit) {
        return traderCustomerBaseMapper.selectPushKingDeeTraderCustomerData(begin, end, limit);
    }

    @Override
    public List<TraderCustomerDto> selectPushKingDeeTraderCustomerlicenceData(Long begin, Long end, Integer limit) {
        return traderCustomerBaseMapper.selectPushKingDeeTraderCustomerlicenceData(begin, end, limit);
    }

    @Override
    public List<TraderCustomerDto> queryHaveCapitalTraderCustomer() {
        return traderCustomerBaseMapper.queryHaveCapitalTraderCustomer();
    }

    @Override
    public List<TraderCustomerDto> selectPushKingDeeTraderCustomerDataByIds(List<Integer> ids) {
        return traderCustomerBaseMapper.selectPushKingDeeTraderCustomerDataByIds(ids);
    }

    @Override
    public KingDeeCustomerDto getKingDeeCustomerInfo(Integer traderCustomerId) {
        TraderCustomerDto traderInfo = traderCustomerBaseMapper.getTraderInfoForKingDee(traderCustomerId);
        if (traderInfo == null) {
            return null;
        }
        KingDeeCustomerDto kingDeeCustomerDto = new KingDeeCustomerDto();
        kingDeeCustomerDto.setFNumber(traderInfo.getTraderCustomerId());
        kingDeeCustomerDto.setFName(traderInfo.getTraderName());
        kingDeeCustomerApiService.query(kingDeeCustomerDto);

        if (ObjectUtil.isNull(kingDeeCustomerDto.getId())) {
            kingDeeCustomerDto.setKingDeeBizEnums(KingDeeBizEnums.saveCustomer);
            kingDeeCustomerDto.setFCustId("0");
        } else {
            kingDeeCustomerDto.setKingDeeBizEnums(KingDeeBizEnums.updateCustomer);
        }
        kingDeeCustomerDto.setFQzokSsjt(traderInfo.getParentTraderName());

        // 银行账户信息
        KingDeeCustomerDetailDto detailDto = new KingDeeCustomerDetailDto();
        detailDto.setFaccountname(traderInfo.getTraderName());
        if (traderInfo.getTraderFinanceDto() != null) {
            detailDto.setFopenbankname(traderInfo.getTraderFinanceDto().getBank());
            detailDto.setFcnaps(traderInfo.getTraderFinanceDto().getBankCode());
            detailDto.setFbankcode(traderInfo.getTraderFinanceDto().getBankAccount());
        }
        kingDeeCustomerDto.setFT_BD_CUSTBANK(Collections.singletonList(detailDto));
        return kingDeeCustomerDto;
    }

    @Override
    public TraderCustomerDto getTraderCustomerInfoByTraderId(Integer traderId) {
        return traderCustomerBaseMapper.getTraderCustomerInfoByTraderId(traderId);
    }

    @Override
    public List<TraderCustomerDto> getTraderCustomerInfoByTraderIds(List<Integer> traderIds) {
        return traderCustomerBaseMapper.getTraderCustomerInfoByTraderIds(traderIds);
    }

    @Override
    public TraderCustomerDto getTraderCustomerAptitudeInfoByTraderId(Integer traderId) {
        return traderCustomerBaseMapper.getTraderCustomerAptitudeInfoByTraderId(traderId);
    }

    @Override
    public TraderCustomerDto getTraderByPayApply(Integer payApplyId) {
        return traderCustomerBaseMapper.getTraderByPayApply(payApplyId);
    }

    @Override
    public void updateTraderAmount(Integer traderId, BigDecimal multiply) {
        traderCustomerBaseMapper.updateTraderAmount(traderId, multiply);
    }

    @Override
    public List<LikeTraderDto> getTrader( String keywords) {
        return traderCustomerBaseMapper.getTrader(keywords);
    }

    @Override
    public TraderCustomerPortraitDto getTraderCustomerPortrait(Integer traderId) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        // 基本信息
        TraderCustomerPortraitDto portraitDto = traderCustomerBaseMapper.getTraderCustomerPortrait(traderId);
       
        boolean isDealer = Objects.nonNull(portraitDto.getTraderCustomerCategoryId()) && "5".equals(portraitDto.getTraderCustomerCategoryId().toString());
        if (isDealer) {
            TraderDealerFrontDto.Principal principal = traderCustomerMarketingPrincipalApiService.stealthFrontData(portraitDto.getTraderCustomerId());
            if (Objects.nonNull(principal)) {
                portraitDto.setEffectiveness(principal.getEffectiveness());
            } else {
                portraitDto.setEffectiveness(1);
            }
        }
        portraitDto.setRegistrationAddress(regionApiService.getThisRegionToParentRegion(portraitDto.getAreaId()));
        TraderCustomerMarketingPrincipalDto marketingPrincipal = traderCustomerMarketingPrincipalApiService.getByTraderId(traderId);
        List<TraderCustomerMarketingTerminalDto> customerMarketing = traderCustomerMarketingApiService.getTraderCustomerMarketing(portraitDto.getTraderCustomerId());
        portraitDto.setTraderCustomerMarketingPrincipalDto(marketingPrincipal);
        portraitDto.setMarketingTerminalDtoList(customerMarketing);
        if (CollectionUtils.isNotEmpty(customerMarketing)) {
            //临床终端 一对一，因此直接取第一个元素
            TraderCustomerMarketingTerminalDto marketingDto = CollUtil.getFirst(customerMarketing);
            portraitDto.setInstitutionNatureName(marketingDto.getInstitutionNatureName());
            portraitDto.setInstitutionLevelName(marketingDto.getInstitutionLevelName());
            portraitDto.setInstitutionTypeName(marketingDto.getInstitutionTypeName());
            portraitDto.setManagementForms(marketingDto.getManagementForms());
            portraitDto.setTraderCustomerMarketingTypeName(marketingDto.getTraderCustomerMarketingTypeName());
        }

        // 客户类型处理
        if (TraderConstant.CLINICAL_MEDICINE.equals(portraitDto.getCustomerType()) && TraderConstant.TERMINAL.equals(portraitDto.getCustomerNature())) {
            String customerType = TraderEnum.getDefinitionNameById(portraitDto.getCustomerType()) + " " + TraderEnum.getDefinitionNameById(portraitDto.getCustomerNature());
            TraderCustomerMarketingTerminalDto first = Optional.ofNullable(CollUtil.getFirst(customerMarketing)).orElse(new TraderCustomerMarketingTerminalDto());
            portraitDto.setTraderCustomerCategoryName(customerType + " " + Convert.toStr(first.getTraderCustomerMarketingTypeName(), ""));
        } else {
            if (!Objects.isNull(portraitDto.getTraderCustomerCategoryId())) {
                List<TraderCustomerCategoryEntity> customerCategory = this.getCustomerCategory(portraitDto.getTraderCustomerCategoryId());
                if (CollectionUtils.isNotEmpty(customerCategory)) {
                    portraitDto.setTraderCustomerCategoryName(customerCategory.stream().map(TraderCustomerCategoryEntity::getCustomerCategoryName).collect(Collectors.joining(" ")));
                } else {
                    portraitDto.setTraderCustomerCategoryName(this.getCustomerCategoryName(portraitDto));
                }
            } else {
                portraitDto.setTraderCustomerCategoryName(this.getCustomerCategoryName(portraitDto));
            }
        }
        List<TraderCustomerAttributeDto> attributeMap = traderCustomerAttributeMapper.getCustomerAttributeByCustomerId(portraitDto.getTraderCustomerId());
        portraitDto.setBusinessCustomerType(attributeMap.stream().filter(map -> TraderConstant.BUSINESS_CUSTOMER_TYPE.equals(map.getAttributeName()))
                .map(TraderCustomerAttributeDto::getAttributeValue).findFirst().orElse(""));
        portraitDto.setBusinessProductsType(attributeMap.stream().filter(map -> TraderConstant.BUSINESS_PRODUCTS_TYPE.equals(map.getAttributeName()))
                .map(TraderCustomerAttributeDto::getAttributeValue).findFirst().orElse(""));
        // 统一处理字典项值展示
        List<SysOptionDefinitionDto> definitionDtoList = sysOptionDefinitionApiService.getByIds(Arrays.asList(portraitDto.getEmployees(), portraitDto.getAnnualSales()));
        Map<Integer, String> sysMap = Optional.of(definitionDtoList.stream().collect(Collectors.toMap(SysOptionDefinitionDto::getSysOptionDefinitionId, SysOptionDefinitionDto::getTitle))).orElse(new HashMap<>());
        portraitDto.setEmployeesStr(sysMap.get(portraitDto.getEmployees()));
        portraitDto.setAnnualSalesStr(sysMap.get(portraitDto.getAnnualSales()));
        // 联系人
        this.getTraderContactList(portraitDto, traderId);
        // 联系地址
        this.getTraderAddressList(portraitDto, traderId);

        // 判断 关联公司（仅临床医疗）模块是否展示
        if (TraderConstant.CLINICAL_MEDICINE.equals(portraitDto.getCustomerType())) {
            TraderCustomerRelationQueryDto queryDto = new TraderCustomerRelationQueryDto();
            queryDto.setTraderId(traderId);
            queryDto.setTraderCustomerId(portraitDto.getTraderCustomerId());
            List<TraderCustomerInfoDto> relationInfo = this.getTraderRelationInfo(queryDto);
            if (CollectionUtils.isNotEmpty(relationInfo)) {
                portraitDto.setHasRelationCustomer(true);
            }
        }
        // 决策标签
        TraderTagApiReq traderTagApiReq = new TraderTagApiReq();
        traderTagApiReq.setTraderId(traderId);
        try {
            RestfulResult<TraderDecisionTag> traderDecisionTag = oneDataTraderTagApi.getTraderDecisionTag(traderTagApiReq);
            TraderDecisionTag data = Optional.ofNullable(traderDecisionTag.getData()).orElse(new TraderDecisionTag());
            portraitDto.setLifeCycle(data.getLifeCycle());
            // CustomerLevel CustomerLevelDesc CustomerLevelFeature 这三个要么全有 要么全没有
            if (StrUtil.isNotBlank(data.getCustomerLevel())) {
                portraitDto.setCustomerGrade(data.getCustomerLevel() + data.getCustomerLevelDesc() + "(" + data.getCustomerLevelFeature() + ")");
            }
        } catch (Exception e) {
            logger.error("大数据接口查询客户决策标签异常,traderId:{}", traderId);
        }
        TraderTransactionTag traderTransactionTag = this.getTraderTransactionTagApi(traderTagApiReq);
        if (traderTransactionTag != null) {
            // 绑定消费类标签
            this.convertTraderTransactionTagToTraderCustomerPortraitDto(traderTransactionTag, portraitDto);
        }
        // 沟通记录
        PageParam<CommunicateRecordDto> recordDtoPageParam = new PageParam<>();
        CommunicateRecordDto query = new CommunicateRecordDto();
        recordDtoPageParam.setPageSize(3);
        recordDtoPageParam.setOrderBy("ADD_TIME DESC");
        query.setTraderId(traderId);
        recordDtoPageParam.setParam(query);
        if (communicateRecordService != null) {
            portraitDto.setCommunicateRecordDtoList(communicateRecordService.page(recordDtoPageParam).getList());
        }
        // 线上会员类标签
        // 客户行为（仅B2B客户展示）

        this.handleAiSummaryNew(portraitDto, traderId, currentUser.getId());
        String s = this.setShowInvalidReason(portraitDto.getInvalidReason(), portraitDto.getOtherReason());
        portraitDto.setShowInvalidReason(s);
        return portraitDto;
    }
    
    
    @Override
    public String setShowInvalidReason( String invalidReason , String otherReason) {
       
        if (StringUtils.isBlank(invalidReason)){
            return "";
        }
        
        // 将 invalidReason 按逗号分割成数组
        String[] reasons = invalidReason.split(",");

        // 使用 StringBuilder 来构建最终的字符串
        StringBuilder result = new StringBuilder("（");

        // 判断是否包含"其他"
        boolean hasOther = false;
        for (String reason : reasons) {
            if (reason.equals("其他")) {
                hasOther = true; // 标记包含"其他"
            } else {
                result.append(reason).append("&"); // 拼接非"其他"的原因
            }
        }

        // 如果包含"其他"，则拼接"其他"
        if (hasOther) {
            result.append("其他");
            // 如果 otherReason 不为空，则拼接其他原因
            if (otherReason != null && !otherReason.isEmpty()) {
                result.append("-").append(otherReason);
            }
        }

        // 如果最后一个字符是 &，则删除它
        if (result.length() > 1 && result.charAt(result.length() - 1) == '&') {
            result.deleteCharAt(result.length() - 1);
        }

        result.append("）");

        String show = result.toString();
        return show;
    }

    @Override
    public List<TraderBelongDto> getTraderBelongInfo(List<String> traderNameList) {
        return traderCustomerBaseMapper.getTraderBelongList(traderNameList);
    }

    @Override
    public List<TraderBelongDto> getTraderBelongInfoById(Integer traderId) {
        return traderCustomerBaseMapper.getTraderBelong(traderId);
    }

    /**
     * 处理AI客户助理图标显示
     *
     * @param portraitDto   TraderCustomerPortraitDto
     * @param traderId      traderId
     * @param currentUserId currentUserId
     */
    private void handleAiSummaryNew(TraderCustomerPortraitDto portraitDto, Integer traderId, Integer currentUserId) {
        try {
            // 只有当前客户的归属销售才会展示NEW
            CommunicateSummaryEntity latestSummary = traderCommunicateSummaryMapper.getLatestSummaryByTraderId(traderId);
            TraderUserDto traderUserDto = Optional.ofNullable(traderMapper.getTraderUserByTraderId(traderId)).orElse(new TraderUserDto());
            if (Objects.nonNull(latestSummary) && currentUserId.equals(traderUserDto.getUserId())) {
                boolean hasKey = RedisUtil.KeyOps.hasKey(SUMMARY_REDIS_KEY + traderId);
                if (hasKey) {
                    String lastViewTimeStr = RedisUtil.StringOps.get(SUMMARY_REDIS_KEY + traderId);
                    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date lastViewTime = formatter.parse(lastViewTimeStr);
                    if (latestSummary.getAddTime().after(lastViewTime)) {
                        // 如果最新一条数据的转化时间晚于 最近一次查看的时间，则展示"NEW"
                        portraitDto.setShowNewSummary(true);
                    }
                } else {
                    portraitDto.setShowNewSummary(true);
                }
            }
        } catch (Exception e) {
            logger.error("获取最近一次AI助理查看时间异常，traderId: {}", traderId);
        }
    }

    /**
     * 查询不到客户分类标签时，"客户类型"字段处理
     *
     * @param portraitDto 客户信息
     * @return 客户类型 展示字符串
     */
    private String getCustomerCategoryName(TraderCustomerPortraitDto portraitDto) {
        String parent = !Objects.isNull(portraitDto.getParentId()) && portraitDto.getParentId() > 0 ? TraderConstant.BRANCH_OFFICE : TraderConstant.HEAD_OFFICE;
        return TraderEnum.getDefinitionNameById(portraitDto.getCustomerType()) + TraderEnum.getDefinitionNameById(portraitDto.getCustomerNature()) + parent;
    }


    /**
     * 查询客户联系人信息
     *
     * @param portraitDto TraderCustomerPortraitDto
     * @param traderId    traderId
     */
    private void getTraderContactList(TraderCustomerPortraitDto portraitDto, Integer traderId) {
        List<TraderContactDto> communicateContact = traderContactService.getLatestCommunicateContact(traderId);
        List<TraderContactDto> contactListBySort = traderContactService.getTraderContactListBySort(traderId);
        if (CollectionUtils.isEmpty(communicateContact)) {
            // 取不到有沟通记录的联系人，直接按照职位优先级取前三位
            portraitDto.setTraderContactDtoList(contactListBySort);
        } else {
            List<TraderContactDto> temp = contactListBySort.stream().filter(item -> !item.getTraderContactId().equals(communicateContact.get(0).getTraderContactId())).limit(2).collect(Collectors.toList());
            communicateContact.addAll(temp);
            portraitDto.setTraderContactDtoList(communicateContact);
        }
    }

    /**
     * 查询客户地址信息
     *
     * @param portraitDto TraderCustomerPortraitDto
     * @param traderId    traderId
     */
    private void getTraderAddressList(TraderCustomerPortraitDto portraitDto, Integer traderId) {
        List<TraderAddressDto> latestTransactionAddress = traderAddressApiService.getLatestTransactionAddress(traderId);
        portraitDto.setTraderAddressDtoList(latestTransactionAddress);
    }

    @Override
    public PageInfo<TraderCustomerTerminal> getTerminalDetail(String name, Integer traderId, Integer pageSize, Integer pageNum) {

        PageInfo<TraderCustomerTerminal> result = new PageInfo<>();
        result.setPageNum(pageNum);
        result.setPageSize(pageSize);
        result.setList(Collections.emptyList());
        if (StrUtil.isEmpty(name)) {
            return result;
        }
        TerminalReqDto query = new TerminalReqDto();
        query.setSearchName(name);
        query.setPageNo(pageNum);
        query.setPageSize(pageSize);
        // 调用大数据接口查询
        try {
            RestfulResult<TerminalDataRes> terminalDetail = oneDataTerminalInfoServiceApi.getTerminalSearchDetail(query);
            logger.info("大数据接口getTerminalDetail，返回结果:{}", JSON.toJSONString(terminalDetail));
            if (terminalDetail.isSuccess()) {
                TerminalDataRes data = terminalDetail.getData();
                List<TerminalRes> list = data.getList();
                if (CollUtil.isEmpty(list)) {
                    return result;
                }
                Map<String, TraderCustomerTerminalDto> traderCustomerTerminal2Map = new HashMap<>();
                if (Objects.nonNull(traderId) && !Integer.valueOf("-1").equals(traderId)) {
                    List<TraderCustomerTerminalDto> traderCustomerTerminalDtos = traderCustomerTerminalApiService.queryByTraderId(traderId);
                    if (CollUtil.isNotEmpty(traderCustomerTerminalDtos)) {
                        traderCustomerTerminal2Map = traderCustomerTerminalDtos.stream().collect(Collectors.toMap(TraderCustomerTerminalDto::getDwhTerminalId, x -> x, (k1, k2) -> k1));
                    }
                }
                // 映射erp对象
                Map<String, TraderCustomerTerminalDto> finalTraderCustomerTerminal2Map = traderCustomerTerminal2Map;

                List<TraderCustomerTerminal> collect = list.stream().map(x -> {
                    TraderCustomerTerminal traderCustomerTerminal = new TraderCustomerTerminal();
                    traderCustomerTerminal.setHosName(x.getHosName());
                    traderCustomerTerminal.setTraderCustomerMarketingType(x.getHosTerminalType());
                    traderCustomerTerminal.setInstitutionLevel(x.getHosLevel());
                    traderCustomerTerminal.setInstitutionNature(x.getHosModel());
                    traderCustomerTerminal.setInstitutionType(x.getHosType());
                    traderCustomerTerminal.setInstitutionTypeChild(x.getHosSmallType());
                    traderCustomerTerminal.setBedNumber(x.getBedCount());
                    traderCustomerTerminal.setManagementForms(x.getBusinessStatus());
                    traderCustomerTerminal.setLegalRepresentative(x.getLegalRepresentative());
                    traderCustomerTerminal.setHospitalDepartment(x.getHosDepartmentList());
                    traderCustomerTerminal.setUniqueId(x.getUniqueId());
                    traderCustomerTerminal.setArea(x.getArea());
                    traderCustomerTerminal.setHosArea(x.getHosArea());
                    traderCustomerTerminal.setProvince(x.getProvince());
                    traderCustomerTerminal.setCity(x.getCity());
                    traderCustomerTerminal.setUnifiedSocialCreditCode(x.getUnifiedSocialCreditCode());
                    traderCustomerTerminal.setChecked(Objects.nonNull(finalTraderCustomerTerminal2Map.get(x.getUniqueId())));
                    return traderCustomerTerminal;
                }).collect(Collectors.toList());
                result.setList(collect);
                result.setTotal(data.getTotalRecord());
                result.setPages(data.getTotalPage());

                return result;
            }
        } catch (Exception e) {
            logger.info("大数据接口:getTerminalSearchDetail,异常", e);
        }
        return result;
    }

    @Override
    public PageInfo<TraderCustomerTerminal> getTycTerminalInfo(String name, Integer traderId, Integer pageSize, Integer pageNum) {
        PageInfo<TraderCustomerTerminal> pageResult = new PageInfo<>();
        pageResult.setPageSize(pageSize);
        pageResult.setPageNum(pageNum);
        PageInfo<TycResultDto> pageInfo = tycSearchService.searchByTerminalName(name);
        List<TycResultDto> tycResultDtoList = Optional.ofNullable(pageInfo.getList()).orElse(new ArrayList<>());

        Map<String, TraderCustomerTerminalDto> traderCustomerTerminal2Map = new HashMap<>();
        if (Objects.nonNull(traderId) && !Integer.valueOf("-1").equals(traderId)) {
            List<TraderCustomerTerminalDto> traderCustomerTerminalDtos = traderCustomerTerminalApiService.queryByTraderId(traderId);
            if (CollUtil.isNotEmpty(traderCustomerTerminalDtos)) {
                traderCustomerTerminal2Map = traderCustomerTerminalDtos.stream().filter(x -> StrUtil.isEmpty(x.getDwhTerminalId())).collect(Collectors.toMap(TraderCustomerTerminalDto::getTerminalName, x -> x, (k1, k2) -> k1));
            }
        }
        // 映射erp对象
        Map<String, TraderCustomerTerminalDto> finalTraderCustomerTerminal2Map = traderCustomerTerminal2Map;
        List<TraderCustomerTerminal> orderTerminalDtoList = tycResultDtoList.stream().map(item -> {
            TraderCustomerTerminal a = new TraderCustomerTerminal();
            a.setHosName(item.getName());
            a.setHosArea(item.getBase());
            a.setChecked(Objects.nonNull(finalTraderCustomerTerminal2Map.get(a.getHosName())));
            a.setUnifiedSocialCreditCode(item.getCreditCode());
            return a;
        }).collect(Collectors.toList());
        pageResult.setTotal(pageInfo.getTotal());
        pageResult.setList(orderTerminalDtoList);
        return pageResult;
    }

    @Override
    public Map<String, List<Map<String, Object>>> handleRelatedCustomerInfo(Integer traderId) {
        TraderCustomerPortraitDto portraitDto = traderCustomerBaseMapper.getTraderCustomerPortrait(traderId);
        TraderCustomerRelationQueryDto queryDto = new TraderCustomerRelationQueryDto();
        queryDto.setTraderId(traderId);
        queryDto.setTraderCustomerId(portraitDto.getTraderCustomerId());
        List<TraderCustomerInfoDto> relationInfo = this.getTraderRelationInfo(queryDto);

        // 先将当前客户作为根节点插入List
        Map<String, Object> currentCustomer = new HashMap<>();
        currentCustomer.put("name", portraitDto.getTraderName());
        currentCustomer.put("image", "lingsha.png");
        // 处理nodes
        List<Map<String, Object>> nodeList = relationInfo.stream().map(relation -> {
            Map<String, Object> tempNode = new HashMap<>();
            tempNode.put("name", relation.getTraderName());
            tempNode.put("image", "lingsha.png");
            return tempNode;
        }).collect(Collectors.toList());
        nodeList.add(0, currentCustomer);
        // 处理edges
        List<Map<String, Object>> edgeList = IntStream.range(0, relationInfo.size()).mapToObj(i -> {
            Map<String, Object> tempEdge = new HashMap<>();
            tempEdge.put("source", 0);
            tempEdge.put("target", i + 1);
            tempEdge.put("relation", "");
            return tempEdge;
        }).collect(Collectors.toList());

        Map<String, List<Map<String, Object>>> result = new HashMap<>();
        result.put("nodes", nodeList);
        result.put("edges", edgeList);
        return result;
    }

    @Override
    public Map<String, Object> getCustomerBehaviorTrace(Integer traderId) {
        Map<String, Object> behaviorTraceMap = new HashMap<>(2);
        //if (this.judgeIsB2bTrader(traderId)) {
            List<WebAccountEntity> webAccountList = this.getWebAccountListByTraderId(traderId);
            if (CollectionUtils.isNotEmpty(webAccountList)) {
                UserBehaviorResDto behaviorByMobile = this.getBehaviorByMobile(webAccountList.get(0).getMobile(), 1, 10);
                behaviorTraceMap.put("webAccountList", webAccountList);
                behaviorTraceMap.put("behaviorTrace", behaviorByMobile);
            }
        //}
        return behaviorTraceMap;
    }

    @Override
    public Map<String, Object> getCustomerArchiveTrace(Integer traderId, String archiveCursor, List<Long> archivedIdList) {
        Map<String, Object> archiveTraceMap = new HashMap<>(16);
        //if (this.judgeIsB2bTrader(traderId)) {
            List<ArchiveCategory> archiveCategoryList = EventTrackingEnum.getCategoryNameList();
            if(CollectionUtils.isEmpty(archivedIdList)){
                archivedIdList = EventTrackingEnum.getShowArchivedIdList(archiveCategoryList.get(0).getCategoryId());
            }
            TraderArchivedResDto traderArchivedResDto = this.getCustomerArchiveTraceByArchiveIds(traderId,null,null,archivedIdList,archiveCursor,1,10);
            archiveTraceMap.put("archiveCategoryList", archiveCategoryList);
            archiveTraceMap.put("traderArchivedResDto", traderArchivedResDto);
        //}
        return archiveTraceMap;
    }


    /**
     * 判断当前客户是否是b2b
     *
     * @param traderId traderId
     * @return true/false
     */
    private boolean judgeIsB2bTrader(Integer traderId) {
        List<Integer> orgIdList = traderMapper.getTraderBelongOrgIdList(traderId);
        if (CollectionUtils.isEmpty(orgIdList)) {
            return false;
        }
        return orgIdList.stream()
                .map(organizationApiService::getTopOrgIdByOrgId)
                .anyMatch(id -> id.equals(B2B_ORGID) || orgIdList.contains(B2B_ORGID));
    }


    @Override
    public UserBehaviorResDto getBehaviorByMobile(String mobile, Integer pageNo, Integer pageSize) {
        UserBehaviorDto userTrajectoryDto = new UserBehaviorDto();
        userTrajectoryDto.setMobile(mobile);
        userTrajectoryDto.setPageNo(pageNo);
        userTrajectoryDto.setPageSize(pageSize);
        RestfulResult<UserBehaviorResDto> userTrajectory = oneDataUserBehaviorApi.getUserBehaviorList(userTrajectoryDto);
        return userTrajectory.getData();
    }

	@Override
    public TraderArchivedResDto getCustomerArchiveTraceByArchiveIds(Integer traderId,String startTime,String endTime, List<Long> archiveIdList, String archiveCursor, Integer pageNo, Integer pageSize) {
        TraderArchivedReqDto traderArchivedReqDto = new TraderArchivedReqDto();
        traderArchivedReqDto.setArchivedIds(archiveIdList);
        //开始时间、结束时间
        try {
    		if(StringUtils.isNotEmpty(startTime)) {
    			traderArchivedReqDto.setStartTime(DateUtil.format(DateUtil.beginOfDay(DateUtils.parseDate(startTime)), DateUtils.DATE_FORMAT_19));
    		}
    		if(StringUtils.isNotEmpty(endTime)) {
    			traderArchivedReqDto.setEndTime(DateUtil.format(DateUtil.endOfDay(DateUtils.parseDate(endTime)), DateUtils.DATE_FORMAT_19));
    		}
    	}catch(ParseException e) {
    		logger.error("客户档案查询，开始结束时间转化异常",e);
    	}
        traderArchivedReqDto.setPageNo(pageNo);
        traderArchivedReqDto.setCursor(archiveCursor);
        traderArchivedReqDto.setPageSize(pageSize);
        traderArchivedReqDto.setTraderId(traderId);
        RestfulResult<TraderArchivedResDto> traderArchivedResDtoResult = null;
        try {
        	
        	traderArchivedResDtoResult = oneDataTraderEventApi.traderArchived(traderArchivedReqDto);
        	if(!traderArchivedResDtoResult.isSuccess()) {
        		logger.error("查询大数据客户档案失败，原因：{}，入参：{}",traderArchivedResDtoResult.getMessage(),JSON.toJSONString(traderArchivedReqDto));
        		return null;
        	}
        	//对大数据返回的 archivedFormat 客户报价、授权书申请、创建订单文案 增加状态的展示
        	convertArchivedFormat(traderArchivedResDtoResult);
        }catch(Exception e) {
        	logger.error("查询大数据客户档案异常",e);
        	return null;
        }
        return traderArchivedResDtoResult.getData();
    }

    @Override
    public Integer getTraderScoreByTraderId(Integer traderId) {
        return traderCustomerBaseMapper.getTraderScoreByTraderId(traderId);
    }

    /**
	 * 对大数据返回的 archivedFormat 客户报价、授权书申请、创建订单 增加状态的展示
	 * @param traderArchivedResDtoResult
	 */
	private void convertArchivedFormat(RestfulResult<TraderArchivedResDto> traderArchivedResDtoResult) {
		if(Objects.isNull(traderArchivedResDtoResult.getData())) {
			return;
		}
		TraderArchivedResDto traderArchivedResDto = traderArchivedResDtoResult.getData();
		List<TraderArchivedDto> traderArchivedDtoList = traderArchivedResDto.getList();
		if(CollectionUtils.isEmpty(traderArchivedDtoList)) {
			return;
		}
		for (TraderArchivedDto traderArchivedDto : traderArchivedDtoList) {
			//客户报价
			if(EventTrackingEnum.SALE_NEW_QUOTATION.getArchivedId().equals(traderArchivedDto.getArchivedId())) {
				//获取报价单号
				JSONObject  jsonObject  = traderArchivedDto.getArchivedContext();
				String quotationNo = (String) jsonObject.get("quotationNo");
				//查询报价是否生效
				QuoteorderEntity quoteorderEntity = quoteorderMapper.getQuoteorderEntityByQuotationNo(quotationNo);
				if(Objects.isNull(quoteorderEntity)) {
					continue;
				}
				if(quoteorderEntity.getValidStatus()) {
					traderArchivedDto.setArchivedFormat(traderArchivedDto.getArchivedFormat()+"(已生效)");
				}else {
					traderArchivedDto.setArchivedFormat(traderArchivedDto.getArchivedFormat()+"(未生效)");
				}
			}
			//授权书申请
			if(EventTrackingEnum.SALE_APPLY_AUTHORIZATION.getArchivedId().equals(traderArchivedDto.getArchivedId())) {
				//获取授权书编号
				JSONObject  jsonObject  = traderArchivedDto.getArchivedContext();
				String authorizationNo = (String) jsonObject.get("authorizationNo");
				AuthorizationApiApply authorizationApply =  quoteorderMapper.getAuthorizationApplyByNum(authorizationNo);
				if(Objects.isNull(authorizationApply)) {
					continue;
				}
				//apply_status 1：申请中、2：驳回、3：未过授权有效期结束日期（通过），超过授权有效期结束日期（过期）、4：取消、5：废弃
				Integer applyStatus = authorizationApply.getApplyStatus();
				String endTime = authorizationApply.getEndTime();

				if(applyStatus==1){
					traderArchivedDto.setArchivedFormat(traderArchivedDto.getArchivedFormat()+"(审核中)");
				}else if(applyStatus.equals(ErpConst.TWO)) {
					traderArchivedDto.setArchivedFormat(traderArchivedDto.getArchivedFormat()+"(审核不通过)");
				}else if(applyStatus.equals(ErpConst.FOUR)) {
					traderArchivedDto.setArchivedFormat(traderArchivedDto.getArchivedFormat()+"(取消)");
				}else if(applyStatus.equals(ErpConst.FIVE)) {
					traderArchivedDto.setArchivedFormat(traderArchivedDto.getArchivedFormat()+"(作废)");
				}else if(applyStatus.equals(ErpConst.THREE)) {
                    Date endTimeDay = null;
                    try {
                        endTimeDay = DateUtils.parseDate(endTime,DateUtils.DATE_FORMAT_10);
                    } catch (ParseException e) {
                        logger.warn("授权书有效期结束日期格式错误...",e);
                    }
					long nowDay = com.vedeng.common.util.DateUtil.getDayStartTime(new Date()).getTime();
					if(Objects.isNull(endTimeDay) || endTimeDay.getTime()<nowDay) {
						traderArchivedDto.setArchivedFormat(traderArchivedDto.getArchivedFormat()+"(过期)");
					}else {
						traderArchivedDto.setArchivedFormat(traderArchivedDto.getArchivedFormat()+"(审核通过)");
					}
				}
			}
			//创建订单
			if(EventTrackingEnum.SALE_CREATE_ORDER_BACK.getArchivedId().equals(traderArchivedDto.getArchivedId())||
			   EventTrackingEnum.SALE_CREATE_ORDER_FRONT.getArchivedId().equals(traderArchivedDto.getArchivedId())||
			   EventTrackingEnum.SALE_CREATE_ORDER_HISTORY_SYNC.getArchivedId().equals(traderArchivedDto.getArchivedId())) {
				//获取订单号
				JSONObject  jsonObject  = traderArchivedDto.getArchivedContext();
				String orderNo = (String) jsonObject.get("orderNo");
				SaleorderInfoDto saleorder = saleOrderMapper.getByAfterSaleNo(orderNo);
				if(Objects.isNull(saleorder)) {
					continue;
				}
				if(saleorder.getValidStatus()==1) {
					traderArchivedDto.setArchivedFormat(traderArchivedDto.getArchivedFormat()+"(已生效)");
				}else {
					traderArchivedDto.setArchivedFormat(traderArchivedDto.getArchivedFormat()+"(未生效)");
				}
			}
		}
	}


	
    @Override
    public List<WebAccountEntity> getWebAccountListByTraderId(Integer traderId) {
        WebAccountEntity webAccount = new WebAccountEntity();
        webAccount.setTraderId(traderId);
        List<WebAccountEntity> webAccountListByParam = webAccountMapper.getWebAccountListBytraderId(webAccount);
        TraderCustomerActionDto traderCustomerAction = traderCustomerBaseMapper.getTraderCustomerAction(traderId);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        webAccountListByParam.forEach(webnomrmal -> {
            String position = webnomrmal.getPosition();
            String name = webnomrmal.getName();
            if (traderCustomerAction.getCustomerNature().equals(TERMINAL) || Objects.isNull(position)) {
                position = "";
            }
            Long addTime = webnomrmal.getAddTime();
            Date vedengMemberTime = webnomrmal.getVedengMemberTime();
            String mobile = webnomrmal.getMobile();
            Integer isOnJob = webnomrmal.getIsOnJob();
            String formattedAddTime = dateFormat.format(new Date(addTime));

            // Format vedengMemberTime, handling possible null value
            String formattedVedengMemberTime = "";
            if (vedengMemberTime != null) {
                formattedVedengMemberTime = dateFormat.format(vedengMemberTime);
            }

            // Construct the formatted card string
            String cardString = String.format(
                    "%s %s %s<br>认证时间: %s <br> 注册时间: %s <br> 在职状态: %s",
                    mobile, StringUtils.isNotBlank(name) ? name : "", StringUtils.isNotBlank(position) ? ("(" + position + ")") : "", formattedVedengMemberTime, formattedAddTime,
                    (isOnJob == null) ? "无" : ((isOnJob == 1) ? "在职" : "离职"));
            webnomrmal.setAccount(cardString);
        });
        return webAccountListByParam;
    }

    @Override
    public TraderCustomerActionDto getTraderCustomerActionInfo(Integer traderId) {
        TraderCustomerActionDto traderCustomerAction = traderCustomerBaseMapper.getTraderCustomerAction(traderId);
        Integer isEnable = traderCustomerAction.getIsEnable();
        //根据isEnable的值决定traderCustomerIsEnable的显示
        traderCustomerAction.setTraderCustomerIsEnable(isEnable == 0 ? "否" : "是");
        // 决策标签
        TraderTagApiReq traderTagApiReq = new TraderTagApiReq();
        traderTagApiReq.setTraderId(traderId);
        try {
            RestfulResult<TraderDecisionTag> traderDecisionTag = oneDataTraderTagApi.getTraderDecisionTag(traderTagApiReq);
            TraderDecisionTag data = Optional.ofNullable(traderDecisionTag.getData()).orElse(new TraderDecisionTag());
            traderCustomerAction.setLifeCycle(data.getLifeCycle());
            String customerLevel = data.getCustomerLevel();
            String customerLevelDesc = data.getCustomerLevelDesc();
            String customerLevelFeature = data.getCustomerLevelFeature();
            String customerGrade = customerLevel + customerLevelDesc +
                    (StringUtil.isBlank(customerLevelFeature) ? "" : "(" + customerLevelFeature + ")");
            traderCustomerAction.setCustomerGrade(customerGrade);
        } catch (Exception e) {
            logger.error("获取大数据客户决策标签异常，traderId:{}", traderTagApiReq.getTraderId());
        }
        // 消费类标签
        TraderTransactionTag traderTransactionTag = this.getTraderTransactionTagApi(traderTagApiReq);
        if (traderTransactionTag != null) {
            this.convertTraderTransactionTagToTraderCustomerActionDto(traderTransactionTag, traderCustomerAction);
        }

        //询价类标签
        try {
            RestfulResult<TraderInquiryTag> traderInquiryTag = oneDataTraderTagApi.getTraderInquiryTag(traderTagApiReq);
            if (Objects.nonNull(traderInquiryTag) && Objects.nonNull(traderInquiryTag.getData())) {
                traderCustomerAction.setLatestInquiryContent(traderInquiryTag.getData().getLatestInquiryContent());
                traderCustomerAction.setLatestInquiryTime(traderInquiryTag.getData().getLatestInquiryTime());
            }
        } catch (Exception e) {
            logger.error("获取大数据客户询价类标签异常，traderId:{}", traderTagApiReq.getTraderId());
        }
        try {
            //销售行为类标签
            RestfulResult<TraderSaleActionTag> traderSaleActionTag = oneDataTraderTagApi.getTraderSaleActionTag(traderTagApiReq);
            if (Objects.nonNull(traderSaleActionTag) && Objects.nonNull(traderSaleActionTag.getData())) {
                traderCustomerAction.setCommunicationRecord(traderSaleActionTag.getData().getCommunicationRecord());
                traderCustomerAction.setHistoryCommunicationNum(traderSaleActionTag.getData().getHistoryCommunicationNum());
            }
        } catch (Exception e) {
            logger.error("获取大数据客户销售行为类标签异常，traderId:{}", traderTagApiReq.getTraderId());
        }
        return traderCustomerAction;
    }

    @Override
    public TraderCustomerActionDto getTraderCustomerAction(Integer traderId) {
        return traderCustomerBaseMapper.getTraderCustomerAction(traderId);
    }

    @Override
    public TraderCustomerInfoDto getTraderCutomerInfoById(Integer traderCustomerId) {
        return traderCustomerBaseMapper.getTraderCutomerInfoById(traderCustomerId);
    }

    /**
     * 调用大数据接口获取消费类标签
     *
     * @param traderTagApiReq 查询参数
     * @return TraderTransactionTag
     */
    private TraderTransactionTag getTraderTransactionTagApi(TraderTagApiReq traderTagApiReq) {
        try {
            RestfulResult<TraderTransactionTag> traderTransactionTag = oneDataTraderTagApi.getTraderTransactionTag(traderTagApiReq);
            return Optional.ofNullable(traderTransactionTag.getData()).orElse(new TraderTransactionTag());
        } catch (Exception e) {
            logger.error("获取大数据客户交易标签异常，traderId:{}", traderTagApiReq.getTraderId());
        }
        return null;
    }


    /**
     * 转换大数据消费类标签到客户行为
     *
     * @param transactionTag
     * @param traderCustomerAction
     */
    private void convertTraderTransactionTagToTraderCustomerActionDto(TraderTransactionTag transactionTag, TraderCustomerActionDto traderCustomerAction) {
        traderCustomerAction.setHistoryTransactionNum(transactionTag.getHistoryTransactionNum());
        traderCustomerAction.setHistoryTransactionAmount(transactionTag.getHistoryTransactionAmount());
        traderCustomerAction.setFirstOrderTime(transactionTag.getFirstOrderTime());
        traderCustomerAction.setLastOrderTime(transactionTag.getLastOrderTime());
        traderCustomerAction.setLastYearAveragePurchasePeriod(transactionTag.getLastYearAveragePurchasePeriod());
        traderCustomerAction.setDepartmentCover(transactionTag.getDepartmentCover());
        traderCustomerAction.setOrderCoverArea(transactionTag.getOrderCoverArea());
        traderCustomerAction.setInstalledArea(transactionTag.getInstalledArea());
        traderCustomerAction.setHistoryCumulativeUnitPrice(transactionTag.getHistoryCumulativeUnitPrice());
        // 7 30 60 180 365 730 交易分类 品牌处理
        traderCustomerAction.setTraderTimeTagsMap(this.getTraderCategoryBrandDto(transactionTag));
    }

    /**
     * 转换大数据消费类标签到客户画像
     *
     * @param transactionTag 查询参数
     * @param portraitDto    封装客户信息
     */
    private void convertTraderTransactionTagToTraderCustomerPortraitDto(TraderTransactionTag transactionTag, TraderCustomerPortraitDto portraitDto) {
        portraitDto.setHistoryTransactionNum(transactionTag.getHistoryTransactionNum());
        portraitDto.setHistoryTransactionAmount(transactionTag.getHistoryTransactionAmount());
        portraitDto.setFirstOrderTime(transactionTag.getFirstOrderTime());
        portraitDto.setLastOrderTime(transactionTag.getLastOrderTime());
        portraitDto.setLastYearAveragePurchasePeriod(transactionTag.getLastYearAveragePurchasePeriod());
        portraitDto.setDepartmentCover(transactionTag.getDepartmentCover());
        portraitDto.setOrderCoverArea(transactionTag.getOrderCoverArea());
        portraitDto.setInstalledArea(transactionTag.getInstalledArea());
        portraitDto.setHistoryCumulativeUnitPrice(transactionTag.getHistoryCumulativeUnitPrice());
        // 7 30 60 180 365 730 交易分类 品牌处理
        portraitDto.setTraderTimeTagsMap(this.getTraderCategoryBrandDto(transactionTag));
    }

    /**
     * 交易分类 品牌处理
     *
     * @param transactionTag
     * @return TraderCategoryBrandDto
     */
    private TraderCategoryBrandDto getTraderCategoryBrandDto(TraderTransactionTag transactionTag) {
        TraderCategoryBrandDto traderTimeTagsMap = new TraderCategoryBrandDto();
        traderTimeTagsMap.setSevenCategory(transactionTag.getTraderTransactionLast7Days().getCategoryList().stream().map(TraderTransactionTag.Category::getCategoryName).collect(Collectors.toList()));
        traderTimeTagsMap.setSevenBrand(transactionTag.getTraderTransactionLast7Days().getBrandList().stream().map(TraderTransactionTag.Brand::getBrandName).collect(Collectors.toList()));
        traderTimeTagsMap.setThirtyCategory(transactionTag.getTraderTransactionLast30Days().getCategoryList().stream().map(TraderTransactionTag.Category::getCategoryName).collect(Collectors.toList()));
        traderTimeTagsMap.setThirtyBrand(transactionTag.getTraderTransactionLast30Days().getBrandList().stream().map(TraderTransactionTag.Brand::getBrandName).collect(Collectors.toList()));
        traderTimeTagsMap.setSixtyCategory(transactionTag.getTraderTransactionLast60Days().getCategoryList().stream().map(TraderTransactionTag.Category::getCategoryName).collect(Collectors.toList()));
        traderTimeTagsMap.setSixtyBrand(transactionTag.getTraderTransactionLast60Days().getBrandList().stream().map(TraderTransactionTag.Brand::getBrandName).collect(Collectors.toList()));
        traderTimeTagsMap.setNinetyCategory(transactionTag.getTraderTransactionLast90Days().getCategoryList().stream().map(TraderTransactionTag.Category::getCategoryName).collect(Collectors.toList()));
        traderTimeTagsMap.setNinetyBrand(transactionTag.getTraderTransactionLast90Days().getBrandList().stream().map(TraderTransactionTag.Brand::getBrandName).collect(Collectors.toList()));
        traderTimeTagsMap.setHalfYearCategory(transactionTag.getTraderTransactionLast180Days().getCategoryList().stream().map(TraderTransactionTag.Category::getCategoryName).collect(Collectors.toList()));
        traderTimeTagsMap.setHalfYearBrand(transactionTag.getTraderTransactionLast180Days().getBrandList().stream().map(TraderTransactionTag.Brand::getBrandName).collect(Collectors.toList()));
        traderTimeTagsMap.setOneYearCategory(transactionTag.getTraderTransactionLast365Days().getCategoryList().stream().map(TraderTransactionTag.Category::getCategoryName).collect(Collectors.toList()));
        traderTimeTagsMap.setOneYearBrand(transactionTag.getTraderTransactionLast365Days().getBrandList().stream().map(TraderTransactionTag.Brand::getBrandName).collect(Collectors.toList()));
        traderTimeTagsMap.setTwoYearCategory(transactionTag.getTraderTransactionLast730Days().getCategoryList().stream().map(TraderTransactionTag.Category::getCategoryName).collect(Collectors.toList()));
        traderTimeTagsMap.setTwoYearBrand(transactionTag.getTraderTransactionLast730Days().getBrandList().stream().map(TraderTransactionTag.Brand::getBrandName).collect(Collectors.toList()));
        traderTimeTagsMap.setSpecialCategory(transactionTag.getHasTransactionInLastYearButNoneInLastMonthCategory().stream().map(TraderTransactionTag.Category::getCategoryName).collect(Collectors.toList()));
        return traderTimeTagsMap;
    }

    @Override
    public PageInfo<DistributionLinkDto> searchDistributionLinkPage(PageParam<DistributionLinkDto> pageParam) {
        PageInfo<DistributionLinkDto> result = new PageInfo<>();
        result.setPageSize(pageParam.getPageSize());
        result.setPageNum(pageParam.getPageNum());
        List<DistributionLinkDto> distributionLinkDtoList = new ArrayList<>();
        // 根据客户类型分别调用大数据接口查询
        DistributionLinkDto query = pageParam.getParam();
        List<TraderInfoV2> traderInfoV2List;
        try {
            if (TERMINAL.equals(query.getCustomerNature())) {
                // 终端
                RestfulResult<TerminalLinkResV2> queryTerminalLink = this.getTerminalLinkRes(query, pageParam.getPageSize(), pageParam.getPageNum());
                traderInfoV2List = Optional.ofNullable(queryTerminalLink.getData().getDistributorTraderInfoList()).orElse(new ArrayList<>());
                result.setTotal(queryTerminalLink.getData().getTotalRecord());
            } else {
                // 经销商
                RestfulResult<DistributorLinkResV2> queryDistributorLink = this.getDistributorLinkRes(query, pageParam.getPageSize(), pageParam.getPageNum());
                traderInfoV2List = Optional.ofNullable(queryDistributorLink.getData().getTerminalTraderInfoList()).orElse(new ArrayList<>());
                result.setTotal(queryDistributorLink.getData().getTotalRecord());
            }
            distributionLinkDtoList = traderInfoV2List.stream().map(item -> {
                DistributionLinkDto temp = new DistributionLinkDto();
                BeanUtil.copyProperties(item, temp);
                return temp;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("查询经销链路异常，traderId:{}", query.getTraderId(), e);
        }
        result.setList(distributionLinkDtoList);
        return result;
    }

    /**
     * 分页查询终端
     * @param pageParam
     * @param userId
     * @return
     */
    @Override
    public PageInfo<TerminalResponseDto> searchTerminalPage(PageParam<TerminalRequestDto> pageParam,Integer userId) {
        logger.info("当前登录人：{}",userId);
        try {
            // 获取该用户下的所有归属销售userId
            List<Integer> userIds = userApiService.getAllSubordinateByUserId(userId);
            // 判断是否是主管
            Integer areaSearchType = isManager(userId);

            TerminalRequestDto param = pageParam.getParam();
            List<String> terminalCategories = param.getTerminalCategories();

            // 处理终端客户类型，树形结构，如1-2-3 三级 或 4-5 两级
            List<TerminalTypeSearchDto> terminalTypeSearchList = terminalCategories.stream().map(e -> {
                TerminalTypeSearchDto typeSearchDto = new TerminalTypeSearchDto();
                String[] split = e.split("-");
                if (split.length > 0) {
                    typeSearchDto.setHosTerminalType(Optional.ofNullable(split[0]).map(Integer::parseInt).orElse(null));
                }
                if (split.length > 1) {
                    typeSearchDto.setHosType(Optional.ofNullable(split[1]).map(Integer::parseInt).orElse(null));
                }
                if (split.length > 2) {
                    typeSearchDto.setHosSmallType(Optional.ofNullable(split[2]).map(Integer::parseInt).orElse(null));
                }
                return typeSearchDto;
            }).collect(Collectors.toList());

            TerminalListReqDto reqDto = new TerminalListReqDto();
            // 终端名称
            reqDto.setTerminalName(param.getTerminalName());
            // 终端客户类型
            reqDto.setTerminalTypeSearchList(terminalTypeSearchList);
            // 机构评级
            reqDto.setHosLevelList(param.getInstitutionLevel());
            // 机构性质
            reqDto.setHosModel(param.getInstitutionNature());
            // 科室
            reqDto.setDepartment(param.getDepartment());
            // 覆盖情况
            reqDto.setCoverStatus(param.getCoverStatus());
            // 合作情况
            reqDto.setCooperationStatus(param.getCooperationStatus());
            // 是否是主管
            reqDto.setAreaSearchType(areaSearchType);
            reqDto.setTerminalAreaSearchList(param.getTerminalAreaSearchList());
            reqDto.setPageNo(pageParam.getPageNum());
            reqDto.setPageSize(pageParam.getPageSize());
            // 排序字段
            reqDto.setSortColumn(param.getSortColumn());
            // 排序类型 0-asc 和 1-desc
            reqDto.setSortType(param.getSortType());
            logger.info("调用listTerminal入参：{}", JSON.toJSONString(reqDto));
            RestfulResult<TerminalPageRes> terminalDetailInfo = oneDataTerminalInfoServiceApi.listTerminal(reqDto);
            logger.info("调用listTerminalr返回：{}", JSON.toJSONString(terminalDetailInfo));
            if (Objects.isNull(terminalDetailInfo)) {
                return new PageInfo<>();
            }
            if (Objects.isNull(terminalDetailInfo.getData())) {
                return new PageInfo<>();
            }
            if (Objects.isNull(terminalDetailInfo.getData().getList())) {
                return new PageInfo<>();
            }
            List<TerminalPageItemRes> terminalPageItemResList = terminalDetailInfo.getData().getList();

            List<TerminalResponseDto> terminalResponseDtoList = terminalPageItemResList.stream().map(e -> {
                TerminalResponseDto dto = new TerminalResponseDto();
                dto.setTerminalTraderId(e.getTerminalTraderId());
                dto.setProvinceCode(e.getProvinceRegionId());
                dto.setCityCode(e.getCityRegionId());
                dto.setProvinceName(e.getProvince());
                dto.setCityName(e.getCity());
                dto.setZoneId(e.getCountyRegionId());
                dto.setTerminalName(e.getTerminalName());
                dto.setCoverStatus(e.getCoverStatus());
                dto.setCooperationStatus(e.getCooperationStatus());
                dto.setBelongArea(e.getProvince() + e.getCity() + e.getCounty());
                dto.setTerminalCategories(e.getHosTerminalType());
                dto.setInstitutionLevel(e.getHosLevel());
                dto.setInstitutionNature(e.getHosModel());
                dto.setDepartment(e.getDepartment());
                dto.setDistributionLinkCount(e.getCooperationTraderCount());
                dto.setBiddingCount(e.getBiddingCount());
                dto.setIsBelongCurrentSales(0);
                // 设置是否属于当前销售
                if (Objects.nonNull(e.getTerminalTraderId())) {
                    UserDto userDto = userApiService.getUserByTraderId(e.getTerminalTraderId());
                    if (Objects.nonNull(userDto) && userIds.contains(userDto.getUserId())) {
                        dto.setIsBelongCurrentSales(1);
                    }
                }
                return dto;

            }).collect(Collectors.toList());
            PageInfo<TerminalResponseDto> result = new PageInfo<>();
            result.setPageSize(pageParam.getPageSize());
            result.setPageNum(pageParam.getPageNum());
            result.setTotal(terminalDetailInfo.getData().getTotalRecord());
            result.setList(terminalResponseDtoList);

            return result;
        }catch (Exception e){
            logger.error("【大数据接口】分页查询终端异常",e);
            return null;
        }
    }

    /**
     * 终端-分销商-分销链路
     * @param pageParam
     * @return
     */
    @Override
    public PageInfo<TerminalDistributionLinkResponseDto> searchTerminaCooperationDistributionLinkPage(PageParam<TerminalDistributionLinkRequestDto> pageParam,Integer userId) {
        logger.info("searchTerminaCooperationDistributionLinkPage入参：{}",JSON.toJSONString(pageParam));

        //获取该用户下的所有归属销售userId
        List<Integer> userIds = userApiService.getAllSubordinateByUserId(userId);
           Integer areaSearchType = isManager(userId);
           TerminalDistributionLinkRequestDto query = pageParam.getParam();
           TraderListReqDto traderListReqDto = new TraderListReqDto();
           // 合作时间 1:近一年 ; 2:近两年; 3:近三年 默认近三年
           traderListReqDto.setCooperationTimeFrame(query.getCooperationTime());
           // 终端名称
           traderListReqDto.setTerminalName(query.getTerminalName());
           // 注册地区
           traderListReqDto.setTraderAreaSearchList(query.getTraderAreaSearchList());
           // 是否是主管
           traderListReqDto.setAreaSearchType(areaSearchType);
           // 中标时间
           traderListReqDto.setLastBiddingTimeStart(query.getLastBiddingTimeStart());
           traderListReqDto.setLastBiddingTimeEnd(query.getLastBiddingTimeEnd());
           // 交易时间
           traderListReqDto.setLastSaleTimeStart(query.getLastSaleTimeStart());
           traderListReqDto.setLastSaleTimeEnd(query.getLastSaleTimeEnd());
           // 合作情况
           traderListReqDto.setCooperationStatus(query.getCooperationStatus());
           traderListReqDto.setPageSize(pageParam.getPageSize());
           traderListReqDto.setPageNo(pageParam.getPageNum());
           // 排序字段
           traderListReqDto.setSortColumn(query.getSortColumn());
           // 排序类型
           traderListReqDto.setSortType(query.getSortType());
           RestfulResult<TraderPageRes> traderPageResRestfulResult;
        try{
           logger.info("调用listDistributorTrader入参：{}",JSON.toJSONString(traderListReqDto));
           traderPageResRestfulResult = oneDataTraderServiceApi.listDistributorTrader(traderListReqDto);
           logger.info("调用listDistributorTrader返回：{}",JSON.toJSONString(traderPageResRestfulResult));
        }catch (Exception e){
            logger.error("【大数据接口】分页查询分销链路异常",e);
            return new PageInfo<>();
        }
           if (Objects.isNull(traderPageResRestfulResult)){
               return new PageInfo<>();
           }
           if (Objects.isNull(traderPageResRestfulResult.getData())){
               return new PageInfo<>();
           }
           // 终端查询是否不准确 0准确 1不准确
            if (Objects.equals(traderPageResRestfulResult.getData().getTerminalInaccuracy(),1)){
                logger.info("终端查询是否不准确");
                throw new ServiceException(BaseResponseCode.TERMINAL_INACCURACY);
            }
           if (Objects.isNull(traderPageResRestfulResult.getData().getList())){
               return new PageInfo<>();
           }
           List<TraderPageItemRes> traderPageItemResList = traderPageResRestfulResult.getData().getList();

           List<TerminalDistributionLinkResponseDto> distributionLinkDtoList = traderPageItemResList.stream().map(e -> {
               TerminalDistributionLinkResponseDto dto = new TerminalDistributionLinkResponseDto();
               dto.setTraderName(e.getTraderName());
               dto.setTraderId(e.getTraderId());
               dto.setProvinceCode(e.getProvinceRegionId());
               dto.setProvinceName(e.getProvince());
               dto.setCityCode(e.getCityRegionId());
               dto.setCityName(e.getCity());
               dto.setCountyCode(e.getCountyRegionId());
               dto.setCountyName(e.getCounty());
               dto.setDistributionLinkName(e.getTraderName());
               dto.setCooperationStatus(e.getCooperationStatus());
               dto.setRegisterArea(e.getProvince()+e.getCity()+e.getCounty());
               dto.setCooperationTerminal(e.getCooperationTerminal());
               dto.setLastBiddingTime(e.getLastBiddingTime());
               dto.setLastSaleTime(e.getLastSaleTime());

               dto.setSaleUser(StringUtils.isNotBlank(e.getSaleUser()) ? e.getSaleUser() : "-");
               dto.setDirectSale(e.getDirectSale());
               dto.setIsBelongCurrentSales(0);

               if (Objects.nonNull(e.getTraderId())) {
                   UserDto userDto = userApiService.getUserByTraderId(e.getTraderId());
                   // 设置是否归属当前销售 1是0否
                   if (Objects.nonNull(userDto) && userIds.contains(userDto.getUserId())) {
                       dto.setIsBelongCurrentSales(1);
                   }
               }
               return dto;

           }).collect(Collectors.toList());
           PageInfo<TerminalDistributionLinkResponseDto> result = new PageInfo<>();
           result.setPageSize(pageParam.getPageSize());
           result.setPageNum(pageParam.getPageNum());
           result.setTotal(traderPageResRestfulResult.getData().getTotalRecord());
           result.setList(distributionLinkDtoList);
           return result;

    }

    /**
     * 判断是否是主管
     *
     * @param userId userId
     * @return Integer
     */
    private Integer isManager(Integer userId){
        //  0销售； 1主管
        List<Integer> allSubordinateUserIds = userApiService.getAllSubordinateByUserId(userId);
        if (Objects.isNull(allSubordinateUserIds)){
            return 0;
        }
        if (allSubordinateUserIds.size() > 1){
            return 1;
        }
        return 0;
    }

    /**
     * 查询终端链路信息
     *
     * @param query    DistributionLinkDto
     * @param pageSize pageSize
     * @param pageNum  pageNum
     * @return RestfulResult<TerminalLinkRes>
     */
    private RestfulResult<TerminalLinkResV2> getTerminalLinkRes(DistributionLinkDto query, Integer pageSize, Integer pageNum) {
        TerminalLinkReqV2Dto terminalLinkReqDto = new TerminalLinkReqV2Dto();
        terminalLinkReqDto.setCooperationTimeFrame(query.getCooperationTimeFrame());
        terminalLinkReqDto.setLastBiddingTimeStart(query.getLastBiddingTimeStart());
        terminalLinkReqDto.setLastBiddingTimeEnd(query.getLastBiddingTimeEnd());
        terminalLinkReqDto.setSortColumn(query.getSortColumn());
        terminalLinkReqDto.setSortType(query.getSortType());
        terminalLinkReqDto.setLastSaleTimeStart(query.getLastSaleTimeStart());
        terminalLinkReqDto.setLastSaleTimeEnd(query.getLastSaleTimeEnd());
        terminalLinkReqDto.setTerminalTraderId(query.getTraderId());
        terminalLinkReqDto.setDistributorTraderName(query.getTraderName());
        terminalLinkReqDto.setLinkSourceType(query.getLinkSourceType());
        terminalLinkReqDto.setPageSize(pageSize);
        terminalLinkReqDto.setPageNo(pageNum);
        return oneDataDistributorTerminalLinkApi.queryTerminalLink(terminalLinkReqDto);
    }

    /**
     * 查询分销链路信息
     *
     * @param query    DistributionLinkDto 查询参数
     * @param pageSize 页大小
     * @param pageNum  当前页码
     * @return RestfulResult<DistributorLinkRes>
     */
    private RestfulResult<DistributorLinkResV2> getDistributorLinkRes(DistributionLinkDto query, Integer pageSize, Integer pageNum) {
        DistributorLinkReqV2Dto distributorLinkReqDto = new DistributorLinkReqV2Dto();
        distributorLinkReqDto.setPageSize(pageSize);
        distributorLinkReqDto.setPageNo(pageNum);
        distributorLinkReqDto.setLinkSourceType(query.getLinkSourceType());
        distributorLinkReqDto.setCooperationTimeFrame(query.getCooperationTimeFrame());
        distributorLinkReqDto.setDistributorTraderId(query.getTraderId());
        distributorLinkReqDto.setTerminalTraderName(query.getTraderName());
        distributorLinkReqDto.setSortColumn(query.getSortColumn());
        distributorLinkReqDto.setSortType(query.getSortType());
        distributorLinkReqDto.setLastBiddingTimeStart(query.getLastBiddingTimeStart());
        distributorLinkReqDto.setLastBiddingTimeEnd(query.getLastBiddingTimeEnd());
        distributorLinkReqDto.setLastSaleTimeStart(query.getLastSaleTimeStart());
        distributorLinkReqDto.setLastSaleTimeEnd(query.getLastSaleTimeEnd());
        return oneDataDistributorTerminalLinkApi.queryDistributorLink(distributorLinkReqDto);
    }

    @Override
    public D3ResultDto searchDistributionLinkD3(PageParam<DistributionLinkDto> pageParam) {
        D3ResultDto d3ResultDto = new D3ResultDto();
        DistributionLinkDto query = pageParam.getParam();
        // 根据linkSourceType 设置不同的排序规则（只有关系图这边需要这样处理，列表页是从前端传入的排序参数）
        if (query.getLinkSourceType() == 0 || query.getLinkSourceType() == 2) {
            query.setSortColumn(2);
        } else {
            query.setSortColumn(query.getLinkSourceType());
        }
        query.setSortType(1);

        TraderCustomerActionDto traderCustomerInfo = traderCustomerBaseMapper.getTraderCustomerAction(query.getTraderId());
        List<TraderInfoV2> traderInfoList = new ArrayList<>();
        try {
            if (TERMINAL.equals(traderCustomerInfo.getCustomerNature())) {
                // 终端
                RestfulResult<TerminalLinkResV2> queryTerminalLink = this.getTerminalLinkRes(query, pageParam.getPageSize(), pageParam.getPageNum());
                traderInfoList = Optional.ofNullable(queryTerminalLink.getData().getDistributorTraderInfoList()).orElse(new ArrayList<>());
            } else {
                // 经销商
                RestfulResult<DistributorLinkResV2> queryDistributorLink = this.getDistributorLinkRes(query, pageParam.getPageSize(), pageParam.getPageNum());
                traderInfoList = Optional.ofNullable(queryDistributorLink.getData().getTerminalTraderInfoList()).orElse(new ArrayList<>());
            }
        } catch (Exception e) {
            logger.error("查询经销链路异常，traderId:{}", query.getTraderId(), e);
        }

        // 组装数据
        List<D3NodeDto> d3NodeDtoList = traderInfoList.stream().map(traderInfo -> {
            D3NodeDto temp = new D3NodeDto();
            temp.setName(traderInfo.getTraderName());
            temp.setImage("");
            // 根据linkSourceType判断
            temp.setRelation(this.getCountByLinkSourceType(traderInfo, query.getLinkSourceType()));
            return temp;
        }).collect(Collectors.toList());
        // 将当前客户作为根节点传入
        D3NodeDto root = new D3NodeDto();
        root.setName(traderCustomerInfo.getTraderName());
        root.setImage("");
        d3NodeDtoList.add(0, root);

        // 组装edge
        List<TraderInfoV2> finalTraderInfoList = traderInfoList;
        List<D3EdgeDto> d3EdgeDtoList = traderInfoList.stream()
                .map(traderInfo -> {
                    D3EdgeDto temp = new D3EdgeDto();
                    temp.setSource(0);
                    temp.setTarget(finalTraderInfoList.indexOf(traderInfo) + 1);
                    temp.setRelation(this.getCountByLinkSourceType(traderInfo, query.getLinkSourceType()));
                    return temp;
                })
                .collect(Collectors.toList());

        d3ResultDto.setNodes(d3NodeDtoList);
        d3ResultDto.setEdges(d3EdgeDtoList);
        return d3ResultDto;
    }

    /**
     * 根据linkSourceType判断取哪个次数
     *
     * @param traderInfo     TraderInfoV2
     * @param linkSourceType linkSourceType
     * @return 中标/交易/商机/报价次数
     */
    private Integer getCountByLinkSourceType(TraderInfoV2 traderInfo, Integer linkSourceType) {
        switch (linkSourceType) {
            case 1:
                return traderInfo.getBiddingCount();
            case 2:
                return traderInfo.getSaleCount();
            case 3:
                return traderInfo.getBusinessChanceCount();
            case 4:
                return traderInfo.getQuoteCount();
            default:
                return 0;
        }
    }

    /**
     * CPM计算标签
     *
     * @param traderId traderId
     * @return TraderPerfectTagDto
     */
    @Override
    public TraderPerfectTagDto getTraderCpmTag(Integer traderId) {
        TraderTagApiReq param = new TraderTagApiReq();
        param.setTraderId(traderId);
        TraderPerfectTagDto result = new TraderPerfectTagDto();
        try {
            RestfulResult<TraderPerfectTag> perfectTag = oneDataTraderTagApi.getTraderCustomerPerfectTag(param);
            TraderPerfectTag tagData = perfectTag.getData();
            BeanUtil.copyProperties(tagData, result);
        } catch (Exception e) {
            logger.error("调用大数据cpm接口异常，traderId：{}", traderId, e);
        }
        return result;
    }

    @Override
    public PageInfo<TraderCustomerDto> getTraderCustomerPage(PageParam<TraderCustomerDto> pageParam) {
        PageInfo<TraderCustomerDto> selectPageInfo = PageHelper.startPage(pageParam).doSelectPageInfo(() -> traderCustomerBaseMapper.findByAll(pageParam.getParam()));
        // 获取所有的traderId
        List<Integer> traderIdList = selectPageInfo.getList().stream().map(TraderCustomerDto::getTraderId).collect(Collectors.toList());
        List<UserDto> userDtoList = null;
        if (CollUtil.isNotEmpty(traderIdList)) {
            userDtoList = userApiService.getUserByTraderIdList(traderIdList);
        }
        for (TraderCustomerDto traderCustomerDto : selectPageInfo.getList()) {
            this.handleCustomerInfo(traderCustomerDto, userDtoList);
        }
        return selectPageInfo;
    }

    @Override
    public R<PageInfo<TraderForSmartQuoteDto>> getTraderListForSmartQuote(String traderName, Integer userId, PageParam<TraderForSmartQuoteDto> pageParam) {
        PageInfo<TraderForSmartQuoteDto> pageInfo = PageHelper.startPage(pageParam).doSelectPageInfo(() -> traderCustomerBaseMapper.getTraderListForSmartQuote(traderName, userId));
        List<TraderForSmartQuoteDto> list = pageInfo.getList();
        if (CollUtil.isEmpty(list)) {
            return R.error("当前用户名下未查询到相关客户,userId:" + userId + ",名称入参:" + traderName + ",页数:" + pageParam.getPageNum());
        }
        list.forEach(s -> {
            //分销
            if (ErpConst.CUSTOME_RNATURE.equals(s.getCustomerNature())) {
                s.setTraderType(ErpConst.ONE);
            }
            //科研
            if (ErpConst.CUSTOME_INTERMIAL.equals(s.getCustomerNature()) && ErpConst.CUSTOME_TYPE_RESEARCH_MEDICAL.equals(s.getCustomerType())) {
                s.setTraderType(ErpConst.TWO);
            }
            //终端
            if (ErpConst.CUSTOME_INTERMIAL.equals(s.getCustomerNature()) && !ErpConst.CUSTOME_TYPE_RESEARCH_MEDICAL.equals(s.getCustomerType())) {
                s.setTraderType(ErpConst.ZERO);
            }
            List<TraderContactDto> traderContactList = traderContactService.getTraderContactListBySort(s.getTraderId());
            if (CollUtil.isNotEmpty(traderContactList)) {
                s.setHasContacts(Boolean.TRUE);
            }
        });
        return R.success(pageInfo);
    }

    /**
     * 处理客户归属销售、地区信息
     *
     * @param customerDto TraderCustomerDto
     * @param userDtoList 对应的归属销售信息集合
     */
    private void handleCustomerInfo(TraderCustomerDto customerDto, List<UserDto> userDtoList) {
        String regionName = regionApiService.getThisRegionToParentRegion(customerDto.getAreaId());
        customerDto.setAddress(regionName);
        if(CollUtil.isNotEmpty(userDtoList)){
            UserDto userDto = userDtoList.stream().filter(item -> item.getTraderId().equals(customerDto.getTraderId())).findFirst().orElse(new UserDto());
            customerDto.setUserName(userDto.getUsername());
        }
    }

    @Override
    public TraderUserDto getTraderUserByTraderId(Integer traderId) {
        return traderMapper.getTraderUserByTraderId(traderId);
    }

    /**
     * 查询注册地区
     * @param userId
     * @return
     */
    @Override
    public Object[] terminalAreaSearchList(Integer userId) {
        // 查下级客户
        List<Integer> userIds = userApiService.getAllSubordinateByUserId(userId);
        if(Objects.isNull(userIds)){
            return null;
        }
        logger.info("userId={}查询terminalAreaSearchList:{}",userId,JSON.toJSONString(userIds));
        // 查询 T_CUSTOMER_REGION_SALE表 userIds是线下销售
        List<PublicCustomerRegionRulesVo> publicCustomerRegionRulesVos = publicCustomerRegionRulesMapper.queryCustomerRegionRulesList(null,userIds);

        Map<Integer, JSONObject> provinceMap = new LinkedHashMap<>();
        Map<Integer, JSONObject> cityMap = new LinkedHashMap<>();

        for (PublicCustomerRegionRulesVo regionRulesVo : publicCustomerRegionRulesVos) {
            String provinceName = regionRulesVo.getProvinceName();
            Integer provinceId = regionRulesVo.getProvinceId();
            String cityName = regionRulesVo.getCityName();
            Integer cityId = regionRulesVo.getCityId();
            String zoneName = regionRulesVo.getZoneName();
            Integer zoneId = regionRulesVo.getZoneId();

            // 省处理
            JSONObject province = provinceMap.computeIfAbsent(provinceId, k -> new JSONObject()
                    .fluentPut("value", provinceName)
                    .fluentPut("label", provinceName)
                    .fluentPut("children", new JSONArray()));

            // 市处理
            JSONObject city = cityMap.computeIfAbsent(cityId, k -> new JSONObject()
                    .fluentPut("value", cityName)
                    .fluentPut("label", cityName)
                    .fluentPut("children", new JSONArray()));

            // 区处理
            city.getJSONArray("children").add(new JSONObject()
                    .fluentPut("value", zoneName)
                    .fluentPut("label", zoneName));

            // 将市添加到省的children数组中
            if (!province.getJSONArray("children").toString().contains(city.toString())) {
                province.getJSONArray("children").add(city);
            }
        }

        if(MapUtils.isEmpty(provinceMap)){
            return null;
        }
        Object[] areaArray = provinceMap.values().toArray();
//        logger.info("terminalAreaSearchList输出：{}",JSON.toJSON(areaArray));
        return areaArray;
    }

    @Override
    public TraderCustomerTerminalPortraitDto getTraderCustomerTerminalPortrait(String searchName) {
        TraderCustomerTerminalPortraitDto result = new TraderCustomerTerminalPortraitDto();
        try {
            TerminalReqDto query = new TerminalReqDto();
            query.setSearchName(searchName);
            logger.info("大数据接口getTerminalPortraitInfo入参:{}", JSON.toJSONString(query));
            RestfulResult<TerminalRes> terminalDetailInfo = oneDataTerminalInfoServiceApi.getTerminalDetailInfo(query);
            logger.info("大数据接口getTerminalPortraitInfo出参:{}", JSON.toJSONString(terminalDetailInfo));
            TerminalRes terminalRes = Optional.ofNullable(terminalDetailInfo.getData()).orElse(new TerminalRes());
            result.setHosName(terminalRes.getHosName());
            result.setAliasName(terminalRes.getAliasName());
            result.setHosTerminalType(terminalRes.getHosTerminalType());
            result.setHosModel(terminalRes.getHosModel());
            result.setHosLevel(terminalRes.getHosLevel());
            result.setHosType(terminalRes.getHosType());
            result.setHosSmallType(terminalRes.getHosSmallType());
            result.setBusinessStatus(terminalRes.getBusinessStatus());
            result.setLegalRepresentative(terminalRes.getLegalRepresentative());
            result.setBedCount(terminalRes.getBedCount());
            result.setHosDepartmentList(terminalRes.getHosDepartmentList());
            result.setProvince(terminalRes.getProvince());
            result.setCity(terminalRes.getCity());
            result.setArea(terminalRes.getArea());
            result.setUnifiedSocialCreditCode(terminalRes.getUnifiedSocialCreditCode());
            result.setBusinessScope(terminalRes.getBusinessScope());
            result.setFunding(terminalRes.getFunding());
            result.setRegAddr(terminalRes.getRegAddr());
            result.setTel(terminalRes.getTel());
            result.setMobile(terminalRes.getMobile());
            result.setFirstRegDate(terminalRes.getFirstRegDate());
        } catch (Exception e) {
            logger.error("大数据接口getTerminalPortraitInfo异常，searchName:{}", searchName, e);
        }
        return result;
    }

    @Override
    public D3ResultDto searchTerminalDistributionLinkD3(PageParam<DistributionLinkDto> pageParam) {
        D3ResultDto d3ResultDto = new D3ResultDto();
        DistributionLinkDto query = pageParam.getParam();
        if (query.getLinkSourceType() == 0) {
            query.setSortColumn(6);
        } else {
            query.setSortColumn(query.getLinkSourceType());
        }
        query.setSortType(1);

        List<TraderInfoV2> traderInfoList = new ArrayList<>();
        try {
            RestfulResult<TerminalLinkResV2> queryTerminalLink = this.getTerminalDistributionLinkRes(query, pageParam.getPageSize(), pageParam.getPageNum());
            traderInfoList = Optional.ofNullable(queryTerminalLink.getData().getDistributorTraderInfoList()).orElse(new ArrayList<>());
        } catch (Exception e) {
            logger.error("查询终端360分销链路异常，query:{}", JSON.toJSONString(query), e);
        }
        // 组装数据
        List<D3NodeDto> d3NodeDtoList = traderInfoList.stream().map(traderInfo -> {
            D3NodeDto temp = new D3NodeDto();
            temp.setName(traderInfo.getTraderName());
            temp.setImage("");
            // 根据linkSourceType判断
            temp.setRelation(this.getCountByLinkSourceType(traderInfo, query.getLinkSourceType()));
            return temp;
        }).collect(Collectors.toList());
        // 将当前客户作为根节点传入
        D3NodeDto root = new D3NodeDto();
        root.setName(query.getSearchName());
        root.setImage("");
        d3NodeDtoList.add(0, root);

        // 组装edge
        List<TraderInfoV2> finalTraderInfoList = traderInfoList;
        List<D3EdgeDto> d3EdgeDtoList = traderInfoList.stream()
                .map(traderInfo -> {
                    D3EdgeDto temp = new D3EdgeDto();
                    temp.setSource(0);
                    temp.setTarget(finalTraderInfoList.indexOf(traderInfo) + 1);
                    temp.setRelation(this.getCountByLinkSourceType(traderInfo, query.getLinkSourceType()));
                    return temp;
                })
                .collect(Collectors.toList());

        d3ResultDto.setNodes(d3NodeDtoList);
        d3ResultDto.setEdges(d3EdgeDtoList);
        return d3ResultDto;
    }

    @Override
    public PageInfo<DistributionLinkDto> searchTerminalDistributionLinkPage(PageParam<DistributionLinkDto> pageParam) {
        PageInfo<DistributionLinkDto> result = new PageInfo<>();
        result.setPageSize(pageParam.getPageSize());
        result.setPageNum(pageParam.getPageNum());
        List<DistributionLinkDto> distributionLinkDtoList = new ArrayList<>();
        // 根据客户类型分别调用大数据接口查询
        DistributionLinkDto query = pageParam.getParam();
        List<TraderInfoV2> traderInfoV2List;
        try {
            RestfulResult<TerminalLinkResV2> queryTerminalLink = this.getTerminalDistributionLinkRes(query, pageParam.getPageSize(), pageParam.getPageNum());
            traderInfoV2List = Optional.ofNullable(queryTerminalLink.getData().getDistributorTraderInfoList()).orElse(new ArrayList<>());
            result.setTotal(queryTerminalLink.getData().getTotalRecord());
            distributionLinkDtoList = traderInfoV2List.stream().map(item -> {
                DistributionLinkDto temp = new DistributionLinkDto();
                BeanUtil.copyProperties(item, temp);
                return temp;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("查询终端360分销链路异常，query:{}", JSON.toJSONString(query), e);
        }
        result.setList(distributionLinkDtoList);
        return result;
    }

    @Override
    public DistributionVisitPlanDto getDistributeVisitPlanList(List<TerminalResponseDto> terminalResponseDtoList,Integer userId) {
        List<Integer> regionIds = terminalResponseDtoList.stream().map(TerminalResponseDto::getZoneId).collect(Collectors.toList());
        // 通过最小区ID查询归属销售
        RegionRulesQueryDto regionRulesQueryDto = new RegionRulesQueryDto();
        List<PublicCustomerRegionRulesVo> publicCustomerRegionRulesVos = publicCustomerRegionRulesMapper.queryCustomerRegionRulesList(regionIds,null);
        Map<Integer,PublicCustomerRegionRulesVo> map = publicCustomerRegionRulesVos.stream().collect(Collectors.toMap(PublicCustomerRegionRulesVo::getZoneId, Function.identity(),(key1, key2)->key1));

        terminalResponseDtoList.forEach(e->{
            PublicCustomerRegionRulesVo publicCustomerRegionRulesVo = map.get(e.getZoneId());
            if (Objects.nonNull(publicCustomerRegionRulesVo)){
                e.setVisitorId(publicCustomerRegionRulesVo.getUserId());
                e.setVisitorName(publicCustomerRegionRulesVo.getUsername());
            }
        });
        List<Integer> userIds = userApiService.getAllSubordinateByUserId(userId);
        List<UserDto> userDtoList = userApiService.getUserInfoByUserIds(userIds);

        DistributionVisitPlanDto distributionVisitPlanDto = new DistributionVisitPlanDto();

        distributionVisitPlanDto.setVisitorList(userDtoList);
        distributionVisitPlanDto.setTerminalResponseDtoList(terminalResponseDtoList);

        return distributionVisitPlanDto;
    }

    /**
     * 批量派发拜访计划
     * @param submitVisitPlanDto
     * @param userId
     */
    @Override
    public void submitVisitPlan(SubmitVisitPlanDto submitVisitPlanDto,Integer userId) {
        logger.info("提交拜访计划入参：{}", JSON.toJSONString(submitVisitPlanDto));
        List<VisitInputTraderDto> visitList = new ArrayList<>();
        // 组装客户数据
        List<VisitBatchDistributeDto> visitBatchDistributeDtos = submitVisitPlanDto.getVisitBatchDistributeDtoList();
        List<Integer> traderIds = visitBatchDistributeDtos.stream().map(VisitBatchDistributeDto::getTraderId).collect(Collectors.toList());
        Map<Integer,TraderCustomerDto> map = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(traderIds)){
            List<TraderCustomerDto> traderCustomerListByTraderIds = this.getTraderCustomerListByTraderIds(traderIds);
            map = traderCustomerListByTraderIds.stream().collect(Collectors.toMap(TraderCustomerDto::getTraderId, Function.identity(),(key1, key2)->key1));
        }

        for (VisitBatchDistributeDto visitBatchDistributeDto : visitBatchDistributeDtos) {
            Integer traderId = visitBatchDistributeDto.getTraderId();
            TraderCustomerDto traderCustomerDto = map.get(traderId);
            Integer traderCustomerId = Optional.ofNullable(traderCustomerDto).map(TraderCustomerDto::getTraderCustomerId).orElse(null);
            VisitInputTraderDto visitInputTraderDto = new VisitInputTraderDto();
            // 客户来源方式 (1erp 2终端库 3天眼查)
            visitInputTraderDto.setCustomerFrom(Objects.isNull(traderCustomerDto) ? 2 : 1);
            visitInputTraderDto.setCustomerName(visitBatchDistributeDto.getCustomerName());
            visitInputTraderDto.setTraderId(visitBatchDistributeDto.getTraderId());
            visitInputTraderDto.setTraderCustomerId(traderCustomerId);
            visitInputTraderDto.setProvinceCode(visitBatchDistributeDto.getProvinceCode());
            visitInputTraderDto.setProvinceName(visitBatchDistributeDto.getProvinceName());
            visitInputTraderDto.setCityCode(visitBatchDistributeDto.getCityCode());
            visitInputTraderDto.setCityName(visitBatchDistributeDto.getCityName());
            visitInputTraderDto.setVisitorId(visitBatchDistributeDto.getVisitorId());
            visitInputTraderDto.setCustomerNature(visitBatchDistributeDto.getCustomerNature());
            visitList.add(visitInputTraderDto);
        }

        VisitBatchInputApiDto visitBatchInputApiDto = new VisitBatchInputApiDto();
        visitBatchInputApiDto.setUserId(userId);
        visitBatchInputApiDto.setPlanVisitDate(submitVisitPlanDto.getVisitTime());
        visitBatchInputApiDto.setVisitTarget(String.join(",", submitVisitPlanDto.getVisitTarget()));
        visitBatchInputApiDto.setTraderList(visitList);
        int result = visitRecordApiService.saveBatchVisitRecord(visitBatchInputApiDto);
        logger.info("共创建成功拜访计划"+result+"条");
    }

    @Override
    public List<TraderCustomerDto> getTraderCustomerListByTraderIds(List<Integer> traderIds) {
        return traderCustomerBaseMapper.getTraderCustomerListByTraderIds(traderIds);
    }



    /**
     * 查询终端360经销链路信息
     *
     * @param query    DistributionLinkDto
     * @param pageSize pageSize
     * @param pageNum  pageNum
     * @return RestfulResult<TerminalLinkRes>
     */
    private RestfulResult<TerminalLinkResV2> getTerminalDistributionLinkRes(DistributionLinkDto query, Integer pageSize, Integer pageNum) {
        TerminalLibLinkReqDto terminalLibLinkReqDto = new TerminalLibLinkReqDto();
        terminalLibLinkReqDto.setLinkSourceType(query.getLinkSourceType());
        terminalLibLinkReqDto.setCooperationTimeFrame(query.getCooperationTimeFrame());
        terminalLibLinkReqDto.setTerminalName(query.getSearchName());
        terminalLibLinkReqDto.setDistributorTraderName(query.getTraderName());
        terminalLibLinkReqDto.setLastBiddingTimeStart(DateUtil.format(query.getLastBiddingTimeStart(), "yyyy-MM-dd HH:mm:ss"));
        terminalLibLinkReqDto.setLastBiddingTimeEnd(DateUtil.format(query.getLastBiddingTimeEnd(), "yyyy-MM-dd HH:mm:ss"));
        terminalLibLinkReqDto.setLastSaleTimeStart(DateUtil.format(query.getLastSaleTimeStart(), "yyyy-MM-dd HH:mm:ss"));
        terminalLibLinkReqDto.setLastSaleTimeEnd(DateUtil.format(query.getLastSaleTimeEnd(), "yyyy-MM-dd HH:mm:ss"));
        terminalLibLinkReqDto.setSortColumn(query.getSortColumn());
        terminalLibLinkReqDto.setSortType(query.getSortType());
        terminalLibLinkReqDto.setPageSize(pageSize);
        terminalLibLinkReqDto.setPageNo(pageNum);
        return oneDataDistributorTerminalLinkApi.queryTerminalLibLink(terminalLibLinkReqDto);
    }
    /**
     *  根据名称获取审核通过的客户列表
     */
    @Override
    public List<TraderCustomerInfoVo> getTraderCustomerByTraderName(String keyword, int start, int pageSize) {
        if(StringUtils.isBlank(keyword)){
            return Collections.emptyList();
        }
        List<TraderCustomerInfoVo> other = traderCustomerBaseMapper.getTraderCustomerByTraderName(keyword, start, pageSize);
        return other;
    }
    @Override
    public List<TraderCustomerInfoVo> getTraderCustomerByTraderNameDefault(String keyword, int start, int pageSize) {

        List<TraderCustomerInfoVo> other = traderCustomerBaseMapper.getTraderCustomerByTraderName(keyword, start, pageSize);
        return other;
    }

    @Override
    public long countTraderCustomerByTraderName(String keyword) {
        return traderCustomerBaseMapper.countTraderCustomerByTraderName(keyword );
    }

    @Override
    public List<TraderCustomerInfoVo> queryTraderCustomerInfoByNameAndSubUserId(int limit, String name, List<Integer> allSubUserIdsList, List<Integer> notIncludeTraderIds) {
        if(CollectionUtils.isEmpty(allSubUserIdsList)){
            return Collections.emptyList();
        }
        List<TraderCustomerInfoVo> traderCustomerInfoVoByName = traderCustomerBaseMapper.queryTraderCustomerInfoByNameAndSubUserId(limit, name,allSubUserIdsList,notIncludeTraderIds);
        return traderCustomerInfoVoByName;
    }

}
