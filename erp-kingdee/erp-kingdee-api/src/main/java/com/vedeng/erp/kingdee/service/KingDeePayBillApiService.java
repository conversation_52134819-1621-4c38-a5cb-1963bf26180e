package com.vedeng.erp.kingdee.service;

import com.vedeng.erp.kingdee.dto.KingDeePayBillDto;
import com.vedeng.infrastructure.kingdee.service.KingDeeMqBaseService;

import java.util.List;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.kingdee.service
 * @Date 2022/9/8 15:56
 */
public interface KingDeePayBillApiService extends KingDeeMqBaseService<KingDeePayBillDto> {

    /**
     * 查询金蝶付款单对象 没有回调地址的
     * @return List<KingDeePayBillDto>
     */
    List<KingDeePayBillDto> queryKingDeePayBillNoReturnUrl();

    /**
     * 查询金蝶收款退款单对象 没有回调地址的
     * @return List<KingDeePayBillDto> 借用付款对象类型
     */
    List<KingDeePayBillDto> queryKingDeeReceiveRefundBillNoReturnUrl();

    /**
     * 查询当前付款单信息，并赋值回 流水号和回单号
     * @param data
     */
    boolean queryKingDeeReturnUrlAndTranFlowAndBank(KingDeePayBillDto data);

    /**
     * 从金蝶获取付款回单地址
     * @param tranFlow
     * @return
     */
    String queryKingDeeReturnUrl(String tranFlow);

    /**
     * 更新流水 和金蝶回推的回单地址
     * @param data
     */
    void updateKingDeePayBillReturnUrlAndTranFlow(KingDeePayBillDto data);

    /**
     * 推送金蝶流水集合
     * @param kingDeePayBillIds 流水id
     * @return List<KingDeePayBillDto>
     */
    List<KingDeePayBillDto> queryKingDeePayBillNoReturnUrlByKingDeePayBillIds(List<Integer> kingDeePayBillIds);

    /**
     * 下载流水回单
     * @param file 金蝶的回单文件BASE64
     * @param fileName 文件名
     * @param suffix 上传sso 后缀名
     * @return String erp Sso 地址
     */
    String downloadFile(String file, String fileName, String suffix);

    /**
     * <AUTHOR>
     * @desc 根据付款申请id作废金蝶付款单信息记录
     * @param fBillNo
     */
    void deleteKingDeeInfoByFBillNo(String fBillNo);

    List<KingDeePayBillDto> getPayBillInfoBybankBillId(Integer erpBankBillId,Integer fileIsPush);

    /**
     * 根据付款申请类型判断对接金蝶'付款单'还是'收款退款单'
     * 规则wiki http://wiki.ivedeng.com/pages/viewpage.action?pageId=*********
     *
     * @param payType   付款申请类型
     * @param relatedId
     * @return BD_Supplier 付款单
     * BD_Customer 收款退款单
     */
    String choosePayObject(Integer payType, Integer relatedId);
}
