<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="授权书列表" scope="application" />



<%@ include file="../../common/common.jsp"%>
<style>
	.infor_name {
		width: 105px !important;
	}

</style>
<div class="content">
	<div class="searchfunc">
		<form method="post" id="search" action="<%=basePath%>order/quote/authorizationIndex.do">
			<input type="hidden" id="companyInfoListWithSeq" name="companyInfoListWithSeq" value='${companyInfoListWithSeq}'/>

			<div class="search-form-row">
				<!-- 第一行 -->
				<ul class="search-form-list">
					<!-- 授权书编号 -->
					<li>
						<label class="infor_name">授权书编号</label>
						<input type="text" class="input-middle" name="authorizationApplyNum" id="brandName" value="${authorizationApplyVo.authorizationApplyNum}">
					</li>

					<!-- 申请人 -->
					<li>
						<label class="infor_name">申请人</label>
						<select name="applyPerson" class="input-middle">
							<option value="">全部</option>
							<c:forEach var="applyPersonI" items="${applyPersonList}">
								<option value="${applyPersonI}" <c:if test="${authorizationApplyVo.applyPerson eq applyPersonI}">selected="selected"</c:if>>${applyPersonI}</option>
							</c:forEach>
						</select>
					</li>

					<!-- 申请时间 -->
					<li>
						<label class="infor_name">申请时间</label>
						<input class="Wdate input-smaller96" type="text" placeholder="开始日期" autocomplete="off"
							   onClick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endTime\')}'})"
							   name="beginTime" id="beginTime" autocomplete="off" value="${authorizationApplyVo.beginTime}">
						<span>-</span>
						<input class="Wdate input-smaller96" type="text" placeholder="结束日期" autocomplete="off"
							   onClick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'beginTime\')}'})"
							   name="endTime" id="endTime" autocomplete="off" value="${authorizationApplyVo.endTime}">
					</li>

					<!-- 状态 -->
					<li>
						<label class="infor_name">状态</label>
						<select name="applyStatus" class="input-middle">
							<option value="">全部</option>
							<option value="1" <c:if test="${authorizationApplyVo.applyStatus eq 1}">selected="selected"</c:if>>审核中</option>
							<option value="2" <c:if test="${authorizationApplyVo.applyStatus eq 2}">selected="selected"</c:if>>驳回</option>
							<option value="3" <c:if test="${authorizationApplyVo.applyStatus eq 3}">selected="selected"</c:if>>审核通过</option>
							<option value="4" <c:if test="${authorizationApplyVo.applyStatus eq 4}">selected="selected"</c:if>>已取消</option>
							<option value="5" <c:if test="${authorizationApplyVo.applyStatus eq 5}">selected="selected"</c:if>>已作废</option>
						</select>
					</li>

					<!-- 采购单位/招标公司 -->
					<li>
						<label class="infor_name" >采购单位/招标公司</label>
						<input type="text" class="input-middle" name="company" value="${authorizationApplyVo.company}">
					</li>

					<!-- 采购项目名称 -->
					<li>
						<label class="infor_name">采购项目名称</label>
						<input type="text" class="input-middle" name="applyName" value="${authorizationApplyVo.applyName}">
					</li>

					<!-- 采购项目编号 -->
					<li>
						<label class="infor_name">采购项目编号</label>
						<input type="text" class="input-middle" name="applyNum" value="${authorizationApplyVo.applyNum}">
					</li>

					<!-- 授权公司 -->
					<li>
						<label class="infor_name" >授权公司</label>
						<input type="text" class="input-middle" name="authorizationCompany" value="${authorizationApplyVo.authorizationCompany}">
					</li>

					<!-- 是否本人处理 -->
					<li>
						<label class="infor_name"  >是否本人处理</label>
						<select name="isSelf" class="input-middle">
							<option value="">全部</option>
							<option value="0" <c:if test="${authorizationApplyVo.isSelf eq 0}">selected="selected"</c:if>>否</option>
							<option value="1" <c:if test="${authorizationApplyVo.isSelf eq 1}">selected="selected"</c:if>>是</option>
						</select>
					</li>

					<!-- 授权类型 -->
					<li>
						<label class="infor_name" >授权类型</label>
						<select name="authType" class="input-middle">
							<option value="">全部</option>
							<option value="0" <c:if test="${authorizationApplyVo.authType eq 0}">selected="selected"</c:if>>项目授权</option>
							<option value="1" <c:if test="${authorizationApplyVo.authType eq 1}">selected="selected"</c:if>>经销授权</option>
						</select>
					</li>

					<!-- 公章类型 -->
					<li>
						<label class="infor_name" >公章类型</label>
						<select name="sealType" class="input-middle">
							<option value="">全部</option>
							<c:forEach items="${companyInfoList}" var="company">
								<option value="${company.frontEndSeq}" <c:if test="${not empty authorizationApplyVo.sealType and authorizationApplyVo.sealType eq company.frontEndSeq}">selected="selected"</c:if>>${company.companyName}</option>
							</c:forEach>
						</select>
					</li>
				</ul>
			</div>

			<!-- 操作按钮 -->
			<div class="tcenter mt10">
				<span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();" id="searchSpan">搜索</span>
				<span class="bt-small bg-light-blue bt-bg-style ml10" onclick="reset();">重置</span>
			</div>
		</form>
	</div>
	<div class='normal-list-page list-page'>
		<table
				class="table table-bordered table-striped table-condensed table-centered">
			<thead>
			<tr class="sort">
				<th>序号</th>
				<th>授权申请编号</th>
				<th>公章类型</th>
				<th>申请人</th>
				<th>申请时间</th>
				<th>采购单位/招标公司</th>
				<th>产品名称</th>
				<th>授权公司</th>
				<!-- <th>创建人</th> -->
				<th>采购项目全称</th>
				<th>采购项目编号</th>
				<th>授权开始日期</th>
				<th>授权结束日期</th>
				<th>授权类型</th>
				<th>授权天数</th>
				<th>商机编号</th>

				<th>订单号</th>
				<th>当前处理人</th>
				<th>状态</th>
				<th>是否为标准模板</th>
				<th>操作栏</th>
			</tr>
			</thead>
			<tbody class="brand">
			<c:forEach var="authorizationApply" items="${authorizationApplyList}" varStatus="s">
				<tr>
					<td>${s.count}</td>
					<td><span style="color: #1E9FFF;cursor:pointer"  onclick="tiaozhuan(${authorizationApply.authorizationApplyId},${authorizationApply.quoteorderId},${authorizationApply.applyStatus})">${authorizationApply.authorizationApplyNum}</span></td>
					<td>
						<c:forEach items="${companyInfoList}" var="company">
							<c:if test ="${not empty authorizationApply.sealType }">
								<c:if test="${company.frontEndSeq eq authorizationApply.sealType}">
									${company.companyName}
								</c:if>
							</c:if>
						</c:forEach>

					</td>
					<td>${authorizationApply.applyPerson}</td>
					<td>${authorizationApply.addTimeFormatted}</td>
					<td>${authorizationApply.purchaseOrBidding}</td>
					<td>${authorizationApply.skuName}</td>
					<td>${authorizationApply.authorizedCompany}</td>
					<td>${authorizationApply.purchaseProjectName}</td>
					<td>${authorizationApply.purchaseProjectNum}</td>
					<td>${authorizationApply.beginTime}</td>
					<td>${authorizationApply.endTime}</td>
					<td>
						<c:if test ="${authorizationApply.authType eq '0'}">项目授权</c:if>
						<c:if test ="${authorizationApply.authType eq '1'}">经销授权</c:if>
					</td>
					<td>${authorizationApply.authorizationDay}</td>
					<td>${authorizationApply.sjNum}</td>

					<td>${authorizationApply.ddNum}</td>
					<td>
						<c:if test ="${authorizationApply.applyStatus eq '1'}">${authorizationApply.reviewer}</c:if>
						<c:if test ="${authorizationApply.applyStatus eq '2'}">${authorizationApply.applyPerson}</c:if>
						<c:if test ="${authorizationApply.applyStatus eq '3'}">-</c:if>
						<c:if test ="${authorizationApply.applyStatus eq '4'}">-</c:if>
						<c:if test ="${authorizationApply.applyStatus eq '5'}">-</c:if>
					</td>
					<td>
						<c:if test ="${authorizationApply.applyStatus eq '1'}">审核中</c:if>
						<c:if test ="${authorizationApply.applyStatus eq '2'}">驳回</c:if>
						<c:if test ="${authorizationApply.applyStatus eq '3'}">审核通过</c:if>
						<c:if test ="${authorizationApply.applyStatus eq '4'}">已取消</c:if>
						<c:if test ="${authorizationApply.applyStatus eq '5'}">已作废</c:if>
					</td>
					<td>
						<c:if test ="${authorizationApply.standardTemplate eq '0'}">是</c:if>
						<c:if test ="${authorizationApply.standardTemplate eq '1'}">否</c:if></td>
					<td>
						<c:if test="${authorizationApply.applyStatus eq 1}">
							<span style="color: #1E9FFF;cursor:pointer" onclick="pageShenhe(${authorizationApply.authorizationApplyId},'${formToken}','${authorizationApply.reviewer}',<c:if test="${authorizationApply.isCurrentUserCanCheck}">1</c:if><c:if test="${!authorizationApply.isCurrentUserCanCheck}">0</c:if>)">审核</span>
							&nbsp;
							<span class="mr10 pop-new-data" style="color: #1E9FFF" layerParams='{"width":"500px","height":"210px","title":"操作确认","link":"./zzApply.do?authorizationApplyId=${authorizationApply.authorizationApplyId}"}'>终止申请</span>
						</c:if>
						<c:if test="${authorizationApply.applyStatus eq 2}">
							<span class="mr10 pop-new-data" style="color: #1E9FFF" layerParams='{"width":"500px","height":"210px","title":"操作确认","link":"./zzApply.do?authorizationApplyId=${authorizationApply.authorizationApplyId}"}'>终止申请</span>
						</c:if>
						<c:if test="${authorizationApply.applyStatus eq 3}">
							<span class="mr10 pop-new-data" style="color: #1E9FFF" layerParams='{"width":"500px","height":"210px","title":"操作确认","link":"./zfApply.do?authorizationApplyId=${authorizationApply.authorizationApplyId}"}'>作废</span>
							<!-- add by Tomcat.Hui 2020/12/30 下午1:59 .Desc: VDERP-4491【财务管理】授权书列表增加财务权限. start -->
							<span class="mr10" style="color: #1E9FFF;cursor:pointer"  onclick="tiaozhuan(${authorizationApply.authorizationApplyId},${authorizationApply.quoteorderId},${authorizationApply.applyStatus})">打印</span>
							<!-- add by Tomcat.Hui 2020/12/30 下午1:59 .Desc: VDERP-4491【财务管理】授权书列表增加财务权限. end -->
						</c:if>
						<c:if test="${authorizationApply.applyStatus eq 4}">-</c:if>
						<c:if test="${authorizationApply.applyStatus eq 5}">-</c:if>
					</td>
				</tr>
			</c:forEach>
			<span style="display:none;"><div class="title-click nobor addtitle2" id="authorizationIndex"></div></span>
			</tbody>
		</table>
		<c:if test="${empty authorizationApplyList}">
			<!-- 查询无结果弹出 -->
			<div class="noresult">查询无结果！请尝试使用其他搜索条件。</div>
		</c:if>
		<tags:page page="${page}" />
	</div>
</div>
<script type="text/javascript"
		src="${pageContext.request.contextPath}/static/js/order/quote/authorization_index.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../../common/footer.jsp"%>
