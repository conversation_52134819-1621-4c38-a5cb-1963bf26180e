<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.repository.mappers.KingDeeOutPutFeePlainInvoiceMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.domain.entity.KingDeeOutPutFeePlainInvoiceEntity">
    <!--@mbg.generated-->
    <!--@Table KING_DEE_OUT_PUT_FEE_PLAIN_INVOICE-->
    <id column="PLAIN_INVOIC_ID" jdbcType="INTEGER" property="plainInvoicId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="FID" jdbcType="VARCHAR" property="fid" />
    <result column="F_QZOK_BDDJTID" jdbcType="VARCHAR" property="fQzokBddjtid" />
    <result column="FINVOICENO" jdbcType="VARCHAR" property="finvoiceno" />
    <result column="F_QZOK_FPDM" jdbcType="VARCHAR" property="fQzokFpdm" />
    <result column="FINVOICEDATE" jdbcType="VARCHAR" property="finvoicedate" />
    <result column="FDATE" jdbcType="VARCHAR" property="fdate" />
    <result column="FCONTACTUNITTYPE" jdbcType="VARCHAR" property="fcontactunittype" />
    <result column="FCONTACTUNIT" jdbcType="VARCHAR" property="fcontactunit" />
    <result column="FSALEORGID" jdbcType="VARCHAR" property="fsaleorgid" />
    <result column="FDOCUMENTSTATUS" jdbcType="VARCHAR" property="fdocumentstatus" />
    <result column="F_RED_BLUE" jdbcType="VARCHAR" property="fRedBlue" />
    <result column="FISPRICEEXCLUDETAX" jdbcType="INTEGER" property="fIsPriceExcludeTax" />
    <result column="FSALEEXINVENTRY" jdbcType="VARCHAR" property="fsaleexinventry" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    PLAIN_INVOIC_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, 
    FID, F_QZOK_BDDJTID, FINVOICENO, F_QZOK_FPDM, FINVOICEDATE, FDATE, FCONTACTUNITTYPE, 
    FCONTACTUNIT, FSALEORGID, FDOCUMENTSTATUS, F_RED_BLUE, FISPRICEEXCLUDETAX, FSALEEXINVENTRY
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from KING_DEE_OUT_PUT_FEE_PLAIN_INVOICE
    where PLAIN_INVOIC_ID = #{plainInvoicId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from KING_DEE_OUT_PUT_FEE_PLAIN_INVOICE
    where PLAIN_INVOIC_ID = #{plainInvoicId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="PLAIN_INVOIC_ID" keyProperty="plainInvoicId" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeOutPutFeePlainInvoiceEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_OUT_PUT_FEE_PLAIN_INVOICE (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      FID, F_QZOK_BDDJTID, FINVOICENO, 
      F_QZOK_FPDM, FINVOICEDATE, FDATE, 
      FCONTACTUNITTYPE, FCONTACTUNIT, FSALEORGID, 
      FDOCUMENTSTATUS, F_RED_BLUE, FISPRICEEXCLUDETAX, FSALEEXINVENTRY
      )
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{fid,jdbcType=VARCHAR}, #{fQzokBddjtid,jdbcType=VARCHAR}, #{finvoiceno,jdbcType=VARCHAR}, 
      #{fQzokFpdm,jdbcType=VARCHAR}, #{finvoicedate,jdbcType=VARCHAR}, #{fdate,jdbcType=VARCHAR}, 
      #{fcontactunittype,jdbcType=VARCHAR}, #{fcontactunit,jdbcType=VARCHAR}, #{fsaleorgid,jdbcType=VARCHAR}, 
      #{fdocumentstatus,jdbcType=VARCHAR}, #{fRedBlue,jdbcType=VARCHAR}, #{fIsPriceExcludeTax,jdbcType=INTEGER}, #{fsaleexinventry,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="PLAIN_INVOIC_ID" keyProperty="plainInvoicId" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeOutPutFeePlainInvoiceEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_OUT_PUT_FEE_PLAIN_INVOICE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="fid != null">
        FID,
      </if>
      <if test="fQzokBddjtid != null">
        F_QZOK_BDDJTID,
      </if>
      <if test="finvoiceno != null">
        FINVOICENO,
      </if>
      <if test="fQzokFpdm != null">
        F_QZOK_FPDM,
      </if>
      <if test="finvoicedate != null">
        FINVOICEDATE,
      </if>
      <if test="fdate != null">
        FDATE,
      </if>
      <if test="fcontactunittype != null">
        FCONTACTUNITTYPE,
      </if>
      <if test="fcontactunit != null">
        FCONTACTUNIT,
      </if>
      <if test="fsaleorgid != null">
        FSALEORGID,
      </if>
      <if test="fdocumentstatus != null">
        FDOCUMENTSTATUS,
      </if>
      <if test="fRedBlue != null">
        F_RED_BLUE,
      </if>
      <if test="fIsPriceExcludeTax != null">
        FISPRICEEXCLUDETAX,
      </if>
      <if test="fsaleexinventry != null">
        FSALEEXINVENTRY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fid != null">
        #{fid,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBddjtid != null">
        #{fQzokBddjtid,jdbcType=VARCHAR},
      </if>
      <if test="finvoiceno != null">
        #{finvoiceno,jdbcType=VARCHAR},
      </if>
      <if test="fQzokFpdm != null">
        #{fQzokFpdm,jdbcType=VARCHAR},
      </if>
      <if test="finvoicedate != null">
        #{finvoicedate,jdbcType=VARCHAR},
      </if>
      <if test="fdate != null">
        #{fdate,jdbcType=VARCHAR},
      </if>
      <if test="fcontactunittype != null">
        #{fcontactunittype,jdbcType=VARCHAR},
      </if>
      <if test="fcontactunit != null">
        #{fcontactunit,jdbcType=VARCHAR},
      </if>
      <if test="fsaleorgid != null">
        #{fsaleorgid,jdbcType=VARCHAR},
      </if>
      <if test="fdocumentstatus != null">
        #{fdocumentstatus,jdbcType=VARCHAR},
      </if>
      <if test="fRedBlue != null">
        #{fRedBlue,jdbcType=VARCHAR},
      </if>
      <if test="fIsPriceExcludeTax != null">
        #{fIsPriceExcludeTax,jdbcType=INTEGER},
      </if>
      <if test="fsaleexinventry != null">
        #{fsaleexinventry,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeOutPutFeePlainInvoiceEntity">
    <!--@mbg.generated-->
    update KING_DEE_OUT_PUT_FEE_PLAIN_INVOICE
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fid != null">
        FID = #{fid,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBddjtid != null">
        F_QZOK_BDDJTID = #{fQzokBddjtid,jdbcType=VARCHAR},
      </if>
      <if test="finvoiceno != null">
        FINVOICENO = #{finvoiceno,jdbcType=VARCHAR},
      </if>
      <if test="fQzokFpdm != null">
        F_QZOK_FPDM = #{fQzokFpdm,jdbcType=VARCHAR},
      </if>
      <if test="finvoicedate != null">
        FINVOICEDATE = #{finvoicedate,jdbcType=VARCHAR},
      </if>
      <if test="fdate != null">
        FDATE = #{fdate,jdbcType=VARCHAR},
      </if>
      <if test="fcontactunittype != null">
        FCONTACTUNITTYPE = #{fcontactunittype,jdbcType=VARCHAR},
      </if>
      <if test="fcontactunit != null">
        FCONTACTUNIT = #{fcontactunit,jdbcType=VARCHAR},
      </if>
      <if test="fsaleorgid != null">
        FSALEORGID = #{fsaleorgid,jdbcType=VARCHAR},
      </if>
      <if test="fdocumentstatus != null">
        FDOCUMENTSTATUS = #{fdocumentstatus,jdbcType=VARCHAR},
      </if>
      <if test="fRedBlue != null">
        F_RED_BLUE = #{fRedBlue,jdbcType=VARCHAR},
      </if>
      <if test="fIsPriceExcludeTax != null">
        FISPRICEEXCLUDETAX = #{fIsPriceExcludeTax,jdbcType=INTEGER},
      </if>
      <if test="fsaleexinventry != null">
        FSALEEXINVENTRY = #{fsaleexinventry,jdbcType=VARCHAR},
      </if>
    </set>
    where PLAIN_INVOIC_ID = #{plainInvoicId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeOutPutFeePlainInvoiceEntity">
    <!--@mbg.generated-->
    update KING_DEE_OUT_PUT_FEE_PLAIN_INVOICE
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      FID = #{fid,jdbcType=VARCHAR},
      F_QZOK_BDDJTID = #{fQzokBddjtid,jdbcType=VARCHAR},
      FINVOICENO = #{finvoiceno,jdbcType=VARCHAR},
      F_QZOK_FPDM = #{fQzokFpdm,jdbcType=VARCHAR},
      FINVOICEDATE = #{finvoicedate,jdbcType=VARCHAR},
      FDATE = #{fdate,jdbcType=VARCHAR},
      FCONTACTUNITTYPE = #{fcontactunittype,jdbcType=VARCHAR},
      FCONTACTUNIT = #{fcontactunit,jdbcType=VARCHAR},
      FSALEORGID = #{fsaleorgid,jdbcType=VARCHAR},
      FDOCUMENTSTATUS = #{fdocumentstatus,jdbcType=VARCHAR},
      F_RED_BLUE = #{fRedBlue,jdbcType=VARCHAR},
      FISPRICEEXCLUDETAX = #{fIsPriceExcludeTax,jdbcType=INTEGER},
      FSALEEXINVENTRY = #{fsaleexinventry,jdbcType=VARCHAR}
    where PLAIN_INVOIC_ID = #{plainInvoicId,jdbcType=INTEGER}
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update KING_DEE_OUT_PUT_FEE_PLAIN_INVOICE
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when PLAIN_INVOIC_ID = #{item.plainInvoicId,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when PLAIN_INVOIC_ID = #{item.plainInvoicId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when PLAIN_INVOIC_ID = #{item.plainInvoicId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when PLAIN_INVOIC_ID = #{item.plainInvoicId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when PLAIN_INVOIC_ID = #{item.plainInvoicId,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when PLAIN_INVOIC_ID = #{item.plainInvoicId,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="FID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fid != null">
            when PLAIN_INVOIC_ID = #{item.plainInvoicId,jdbcType=INTEGER} then #{item.fid,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_BDDJTID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokBddjtid != null">
            when PLAIN_INVOIC_ID = #{item.plainInvoicId,jdbcType=INTEGER} then #{item.fQzokBddjtid,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="FINVOICENO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.finvoiceno != null">
            when PLAIN_INVOIC_ID = #{item.plainInvoicId,jdbcType=INTEGER} then #{item.finvoiceno,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_FPDM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokFpdm != null">
            when PLAIN_INVOIC_ID = #{item.plainInvoicId,jdbcType=INTEGER} then #{item.fQzokFpdm,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="FINVOICEDATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.finvoicedate != null">
            when PLAIN_INVOIC_ID = #{item.plainInvoicId,jdbcType=INTEGER} then #{item.finvoicedate,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="FDATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fdate != null">
            when PLAIN_INVOIC_ID = #{item.plainInvoicId,jdbcType=INTEGER} then #{item.fdate,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="FCONTACTUNITTYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fcontactunittype != null">
            when PLAIN_INVOIC_ID = #{item.plainInvoicId,jdbcType=INTEGER} then #{item.fcontactunittype,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="FCONTACTUNIT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fcontactunit != null">
            when PLAIN_INVOIC_ID = #{item.plainInvoicId,jdbcType=INTEGER} then #{item.fcontactunit,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="FSALEORGID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fsaleorgid != null">
            when PLAIN_INVOIC_ID = #{item.plainInvoicId,jdbcType=INTEGER} then #{item.fsaleorgid,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="FDOCUMENTSTATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fdocumentstatus != null">
            when PLAIN_INVOIC_ID = #{item.plainInvoicId,jdbcType=INTEGER} then #{item.fdocumentstatus,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_RED_BLUE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fRedBlue != null">
            when PLAIN_INVOIC_ID = #{item.plainInvoicId,jdbcType=INTEGER} then #{item.fRedBlue,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="FISPRICEEXCLUDETAX = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fIsPriceExcludeTax != null">
            when PLAIN_INVOIC_ID = #{item.plainInvoicId,jdbcType=INTEGER} then #{item.fIsPriceExcludeTax,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="FSALEEXINVENTRY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fsaleexinventry != null">
            when PLAIN_INVOIC_ID = #{item.plainInvoicId,jdbcType=INTEGER} then #{item.fsaleexinventry,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where PLAIN_INVOIC_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.plainInvoicId,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="PLAIN_INVOIC_ID" keyProperty="plainInvoicId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_OUT_PUT_FEE_PLAIN_INVOICE
    (ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, FID, F_QZOK_BDDJTID, 
      FINVOICENO, F_QZOK_FPDM, FINVOICEDATE, FDATE, FCONTACTUNITTYPE, FCONTACTUNIT, FSALEORGID, 
      FDOCUMENTSTATUS, F_RED_BLUE, FISPRICEEXCLUDETAX, FSALEEXINVENTRY)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, 
        #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, #{item.updaterName,jdbcType=VARCHAR}, 
        #{item.fid,jdbcType=VARCHAR}, #{item.fQzokBddjtid,jdbcType=VARCHAR}, #{item.finvoiceno,jdbcType=VARCHAR}, 
        #{item.fQzokFpdm,jdbcType=VARCHAR}, #{item.finvoicedate,jdbcType=VARCHAR}, #{item.fdate,jdbcType=VARCHAR}, 
        #{item.fcontactunittype,jdbcType=VARCHAR}, #{item.fcontactunit,jdbcType=VARCHAR}, 
        #{item.fsaleorgid,jdbcType=VARCHAR}, #{item.fdocumentstatus,jdbcType=VARCHAR}, 
        #{item.fRedBlue,jdbcType=VARCHAR}, #{item.fIsPriceExcludeTax,jdbcType=INTEGER}, #{item.fsaleexinventry,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>