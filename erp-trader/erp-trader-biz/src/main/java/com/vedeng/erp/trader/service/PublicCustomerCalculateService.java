package com.vedeng.erp.trader.service;

import java.util.BitSet;
import java.util.List;

/**
 * @Author: daniel
 * @Date: 2022/2/22 14 47
 * @Description:
 */
public interface PublicCustomerCalculateService {

    /**
     * 计算公海客户
     * @param bitSetOfBasePublicCustomer 基础客户集合
     * @param scenarios 计算场景，1：公海预警计算，2：纳入公海计算
     * @return 客户集合
     */
    BitSet calculatePublicCustomer(BitSet bitSetOfBasePublicCustomer, Integer scenarios);

}
