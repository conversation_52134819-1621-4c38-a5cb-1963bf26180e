package com.wms.service.chain.step;

import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.flash.enums.WmsSendOrderTypeEnum;
import com.wms.constant.LogicalEnum;
import com.wms.constant.WMSContant;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.dao.WmsSendOrderMapper;
import com.wms.dto.PutSaleOrderDto;
import com.wms.dto.PutSaleOrderGoodsDto;
import com.wms.dto.WmsResponse;
import com.wms.model.po.WmsLogicalOrdergoods;
import com.wms.model.po.WmsSendOrder;
import com.wms.service.WMSInterfaceFactory;
import com.wms.service.WmsInterface;
import com.wms.service.chain.AbstractHandlerStep;
import com.wms.service.chain.HandlerStepContext;
import com.wms.unitconversionorder.dao.WmsUnitConversionOrderMapper;
import com.wms.unitconversionorder.model.dto.WmsUnitConversionOrderDto;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 单位转换单出库 下午发wms
 * <AUTHOR>
 */
@Service
@Slf4j
public class UnitConversionOrderOutWmsRequestHandler extends AbstractHandlerStep {

    // 盘库出库默认的出库人地址等信息
    public static final String CONSIGNEE_ID = "VEDENG";
    public static final String CONSIGNEE_NAME = "NJADMIN";
    public static final String PROVINCE = "江苏省";
    public static final String CITY = "南京市";
    public static final String DISTRICT = "栖霞区";
    public static final String CONSIGNEE_ADDRESS_1 = "经天路6号中电熊猫物流园区4#库二层";
    public static final String CONSIGNEE_CONTACT = "erp";
    public static final String CONSIGNEE_TEL_1 = "";
    @Resource
    private WMSInterfaceFactory wmsInterfaceFactory;


    @Autowired
    private WmsUnitConversionOrderMapper wmsUnitConversionOrderMapper;


    @Autowired
    private WmsSendOrderMapper wmsSendOrderMapper;



    @Override
    protected void doDealWith(HandlerStepContext context) throws Exception {

        Integer wmsUnitConversionOrderId = context.get("wmsUnitConversionOrderId");
        List<WmsLogicalOrdergoods> wmsLogicalOrdergoodsList = context.get("wmsLogicalOrdergoodsList");
        WmsUnitConversionOrderDto wmsUnitConversionOrderDto = wmsUnitConversionOrderMapper.selectByWmsUnitConversionOrderId(wmsUnitConversionOrderId);
        UnitConversionOrderInWmsRequestHandler bean = SpringUtil.getBean(UnitConversionOrderInWmsRequestHandler.class);
        Integer wmsSendOrderId = bean.saveWmsSendOrder(wmsUnitConversionOrderDto);
        PutSaleOrderDto putSaleOrderDto = new PutSaleOrderDto();
        putSaleOrderDto.setDocNo(wmsUnitConversionOrderDto.getWmsUnitConversionOrderNo());
        //   todo 待产品确定
        putSaleOrderDto.setOrderType(WmsInterfaceOrderType.UNIT_CONVERSION_OUT);

        putSaleOrderDto.setOrderTime(DateUtil.formatDateTime(wmsUnitConversionOrderDto.getAddTime()));
        putSaleOrderDto.setExpectedShipmentTime1(DateUtil.formatDateTime(wmsUnitConversionOrderDto.getAddTime()));
        putSaleOrderDto.setSoReferenceA(wmsUnitConversionOrderDto.getOrgName());
        putSaleOrderDto.setSoReferenceB(wmsUnitConversionOrderDto.getCreatorName());
        putSaleOrderDto.setHedi07("A");
        putSaleOrderDto.setNotes(wmsUnitConversionOrderDto.getComments());
        // todo 待产品确定
        putSaleOrderDto.setConsigneeId(CONSIGNEE_ID);
        putSaleOrderDto.setConsigneeName(CONSIGNEE_NAME);
        putSaleOrderDto.setConsigneeProvince(PROVINCE);
        putSaleOrderDto.setConsigneeCity(CITY);
        putSaleOrderDto.setConsigneeDistrict(DISTRICT);

        putSaleOrderDto.setConsigneeAddress1(CONSIGNEE_ADDRESS_1);
        putSaleOrderDto.setConsigneeContact(CONSIGNEE_CONTACT);
        putSaleOrderDto.setConsigneeTel1(CONSIGNEE_TEL_1);

        List<PutSaleOrderGoodsDto> details = new ArrayList<>();
        for (WmsLogicalOrdergoods wmsLogicalOrdergoods : wmsLogicalOrdergoodsList) {
            PutSaleOrderGoodsDto putSaleOrderGoodsDto = new PutSaleOrderGoodsDto();
            putSaleOrderGoodsDto.setSku(wmsLogicalOrdergoods.getSku());
            putSaleOrderGoodsDto.setQtyOrdered(wmsLogicalOrdergoods.getOccupyNum());
            putSaleOrderGoodsDto.setLotAtt08(LogicalEnum.getLogicalWarehouseCode(wmsLogicalOrdergoods.getLogicalWarehouseId()));
            putSaleOrderGoodsDto.setDedi07(wmsLogicalOrdergoods.getRelatedId().toString());
            details.add(putSaleOrderGoodsDto);
        }
        putSaleOrderDto.setDetails(details);

        try {
            log.info("WMS单位转换单出库下传的 单号:{},请求:{}",wmsUnitConversionOrderDto.getWmsUnitConversionOrderNo(), JSON.toJSONString(putSaleOrderDto));
            //XxlJobLogger.log("WMS单位转换单出库下传的 单号:{},请求:{}",wmsUnitConversionOrderDto.getWmsUnitConversionOrderNo(), JSON.toJSONString(putSaleOrderDto));
            WmsInterface putSaleOrderOutputInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_ORIGINAL_SALESORDER);

            WmsResponse response = putSaleOrderOutputInterface.request(putSaleOrderDto);
            //请求失败 补偿
            if("1".equals(response.getReturnFlag())){
                WmsSendOrder updateWmsSendOrder = new WmsSendOrder();
                updateWmsSendOrder.setWmsSendOrderId(wmsSendOrderId);
                updateWmsSendOrder.setSendStatus(ErpConst.ONE);
                wmsSendOrderMapper.updateByPrimaryKeySelective(updateWmsSendOrder);
            }
            log.info("WMS单位转换单出库下传的 单号:{},响应:{}",wmsUnitConversionOrderDto.getWmsUnitConversionOrderNo(),JSON.toJSONString(response));
            //XxlJobLogger.log("WMS单位转换单出库下传的 单号:{},响应:{}",wmsUnitConversionOrderDto.getWmsUnitConversionOrderNo(),JSON.toJSONString(response));
        } catch (Exception e) {
            log.error("下发单位转换单出库失败error 单号:"+wmsUnitConversionOrderDto.getWmsUnitConversionOrderNo(),e);
            //XxlJobLogger.log("下发单位转换单出库失败error 单号:"+wmsUnitConversionOrderDto.getWmsUnitConversionOrderNo(),e);
        }


    }

    @Transactional
    public Integer saveWmsSendOrder(WmsUnitConversionOrderDto wmsUnitConversionOrderDto) {
        WmsSendOrder wmsSendOrder = new WmsSendOrder();
        try {
            wmsSendOrder.setOrderType(WmsSendOrderTypeEnum.UNIT_CONVERSION_OUT.getCode());
            Integer id = wmsUnitConversionOrderDto.getWmsUnitConversionOrderId();
            wmsSendOrder.setOrderId(id);
            wmsSendOrder.setOrderNo(wmsUnitConversionOrderDto.getWmsUnitConversionOrderNo());
            WmsSendOrder oldInfo = wmsSendOrderMapper.getWmsSendOrderInfo(wmsSendOrder);
            if(oldInfo != null){
                WmsSendOrder update = new WmsSendOrder();
                update.setSendStatus(0);
                update.setWmsSendOrderId(oldInfo.getWmsSendOrderId());
                wmsSendOrderMapper.updateByPrimaryKeySelective(update);
                return oldInfo.getWmsSendOrderId();
            }
            Integer userId = CurrentUser.getCurrentUser().getId();
            wmsSendOrder.setCreator(userId);
            wmsSendOrder.setUpdater(userId);
            wmsSendOrder.setSendStatus(0);
            wmsSendOrderMapper.insertSelective(wmsSendOrder);
        } catch (Exception e) {
            log.error("saveWmsSendOrder error，下发库存转换出库单：{}",wmsUnitConversionOrderDto.getWmsUnitConversionOrderNo(),e);
            //XxlJobLogger.log("saveWmsSendOrder error，下发库存转换出库单：{}",wmsUnitConversionOrderDto.getWmsUnitConversionOrderNo(),e);
        }
        return wmsSendOrder.getWmsSendOrderId();
    }

}

