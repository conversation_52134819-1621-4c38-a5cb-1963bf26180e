<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>
<c:set var="title" value="详情" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript">
    function foldAndRetract(id) {
        $("#"+id).toggle();
    }
</script>
<div class="content">
    <div class="formtitle"></div>
    <div  id="desc_div">
        <form method="post" id="addForm" action="${pageContext.request.contextPath}/price/basePriceMaintain/detail.do">
            <input type="hidden" name="skuPriceChangeApplyId" id="skuPriceChangeApplyId" value="${skuPriceChangeApplyDto.id}"/>
            <ul class="payplan">

                <li>
                    <div class="parts">
                        <div class="title-container">
                            <div class="table-title nobor">基本信息</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                                <tr>
                                    <th style="width:50px">客户名称</th>
                                    <th style="width:80px">归属平台</th>
                                    <th style="width:80px">归属销售</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>${trader.traderName}</td>
                                    <td>
                                        <c:choose>
                                            <c:when test="${trader.belongPlatform == 1}">
                                                贝登医疗
                                            </c:when>
                                            <c:when test="${trader.belongPlatform == 2}">
                                                医械购
                                            </c:when>
                                            <c:when test="${trader.belongPlatform == 3}">
                                                科研购
                                            </c:when>
                                            <c:when test="${trader.belongPlatform == 4}">
                                                集团业务部
                                            </c:when>
                                            <c:otherwise>
                                                其他
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                    <td>${belongToSaleName}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </li>

                <li>
                    <div class="parts">
                        <div class="title-container">
                            <div class="table-title nobor">有效优惠券(${myCouponDto.availableCouponsList.size()})</div>
                            <a class="title-click" href="javascript:void(0);"  onclick="foldAndRetract('availableCouponDiv')">收起</a>
                        </div>

                        <div id="availableCouponDiv">
                            <table class="table  table-bordered table-striped table-condensed table-centered">
                                <tbody>
                                    <c:forEach var="availableCoupon" items="${myCouponDto.availableCouponsList}" varStatus="num">
                                        <tr>
                                            <td>
                                                ￥${availableCoupon.denomination}  满${availableCoupon.useThreshold}可用  ${couponIdAndLimitDescMap[availableCoupon.couponCode]}
                                                &nbsp;
                                                <fmt:formatDate value="${availableCoupon.effectiveStartTime}" type="date" pattern="yyyy-MM-dd HH:mm:ss" />
                                                -
                                                <fmt:formatDate value="${availableCoupon.effectiveEndTime}" type="date" pattern="yyyy-MM-dd HH:mm:ss" />
                                            </td>
                                        </tr>
                                    </c:forEach>
                                </tbody>
                            </table>
                        </div>

                    </div>
                </li>

                <li>
                    <div class="parts">
                        <div class="title-container">
                            <div class="table-title nobor">已使用(${myCouponDto.usedCouponsList.size()})</div>
                            <a class="title-click" href="javascript:void(0);"  onclick="foldAndRetract('usedCouponsDiv')">收起</a>
                        </div>

                        <div id="usedCouponsDiv">
                            <table class="table  table-bordered table-striped table-condensed table-centered">
                                <tbody>
                                    <c:forEach var="usedCoupon" items="${myCouponDto.usedCouponsList}" varStatus="num">
                                        <tr>
                                            <td>
                                                订单号:
                                                <a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewsaleorder${couponCodeAndOrderNoMap[usedCoupon.couponCode]['saleorderId']}","link":"./order/saleorder/view.do?saleorderId=${couponCodeAndOrderNoMap[usedCoupon.couponCode]['saleorderId']}","title":"订单信息"}'>
                                                        ${couponCodeAndOrderNoMap[usedCoupon.couponCode]["saleorderNo"]}
                                                </a>
                                                ￥${usedCoupon.denomination}  满${usedCoupon.useThreshold}可用  ${couponIdAndLimitDescMap[usedCoupon.couponCode]}
                                                &nbsp;
                                                <fmt:formatDate value="${usedCoupon.effectiveStartTime}" type="date" pattern="yyyy-MM-dd HH:mm:ss" />
                                                -
                                                <fmt:formatDate value="${usedCoupon.effectiveEndTime}" type="date" pattern="yyyy-MM-dd HH:mm:ss" />
                                            </td>
                                        </tr>
                                    </c:forEach>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </li>

                <li>
                    <div class="parts">
                        <div class="title-container">
                            <div class="table-title nobor">已过期(${myCouponDto.expiredCouponsList.size()})</div>
                            <a class="title-click" href="javascript:void(0);"  onclick="foldAndRetract('expiredCouponDiv')">收起</a>
                        </div>

                        <div id="expiredCouponDiv">
                            <table class="table  table-bordered table-striped table-condensed table-centered">
                                <tbody>
                                    <c:forEach var="expiredCoupon" items="${myCouponDto.expiredCouponsList}" varStatus="num">
                                        <tr>
                                            <td>
                                                ￥${expiredCoupon.denomination}  满${expiredCoupon.useThreshold}可用  ${couponIdAndLimitDescMap[expiredCoupon.couponCode]}
                                                &nbsp;
                                                <fmt:formatDate value="${expiredCoupon.effectiveStartTime}" type="date" pattern="yyyy-MM-dd HH:mm:ss" />
                                                -
                                                <fmt:formatDate value="${expiredCoupon.effectiveEndTime}" type="date" pattern="yyyy-MM-dd HH:mm:ss" />
                                            </td>
                                        </tr>
                                    </c:forEach>
                                </tbody>
                            </table>
                        </div>

                    </div>
                </li>

            </ul>
        </form>
    </div>
    <div class="add-tijiao  tcenter">
<%--    <button id="close-layer" type="button" class="dele" >关闭</button>--%>
    </div>
</div>
<%@ include file="../../common/footer.jsp"%>
