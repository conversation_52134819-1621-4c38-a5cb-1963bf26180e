package com.vedeng.erp.mobile.mapper;

import com.vedeng.erp.mobile.domain.Invoice;
import java.util.List;

import com.vedeng.erp.mobile.dto.SaleOrderInvoiceInfoDto;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;

@Named("mobileInvoiceMapper")
public interface InvoiceMapper {
    int deleteByPrimaryKey(Integer invoiceId);

    int insert(Invoice record);

    int insertOrUpdate(Invoice record);

    int insertOrUpdateSelective(Invoice record);

    int insertSelective(Invoice record);

    Invoice selectByPrimaryKey(Integer invoiceId);

    int updateByPrimaryKeySelective(Invoice record);

    int updateByPrimaryKey(Invoice record);

    int updateBatch(List<Invoice> list);

    int updateBatchSelective(List<Invoice> list);

    int batchInsert(@Param("list") List<Invoice> list);

    List<SaleOrderInvoiceInfoDto> getByRelatedIdAndType(@Param("relatedIds") List<Integer> relatedIds, @Param("type") Integer type);

}