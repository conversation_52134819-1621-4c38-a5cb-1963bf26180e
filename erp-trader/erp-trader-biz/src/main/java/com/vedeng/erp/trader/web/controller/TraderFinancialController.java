package com.vedeng.erp.trader.web.controller;

import com.vedeng.authorization.model.User;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.erp.trader.domain.dto.TraderFinanceDetail;
import com.vedeng.erp.trader.domain.dto.TraderSupplierFinanceDetail;
import com.vedeng.erp.trader.domain.entity.TraderCustomerFinance;
import com.vedeng.erp.trader.domain.entity.TraderSupplierFinance;
import com.vedeng.erp.trader.service.TraderFinancialService;
import com.vedeng.erp.trader.service.TraderSupplierFinanceService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 财务  客户-供应商
 * @date 2022/11/28 17:01
 */
@Controller
@RequestMapping("/traderFinancia")
@Slf4j
public class TraderFinancialController {

    Logger logger = LoggerFactory.getLogger(TraderFinancialController.class);

    @Resource
    private TraderFinancialService traderFinancialService;

    @Resource
    private TraderSupplierFinanceService traderSupplierFinanceService;
    /**
     * 去客户信息编辑页面
     * @param traderCustomerFinanceId
     * @return
     */
    @RequestMapping(value = "/edit")
    public ModelAndView edit(@RequestParam(required = false) Integer traderCustomerFinanceId) {
        if(Objects.isNull(traderCustomerFinanceId)){
            return new ModelAndView("common/fail");
        }
        ModelAndView mv = new ModelAndView("trader/financia/edit_trader_financia");
        //根据traderCustomerFinanceId 查客户回显使用
        TraderFinanceDetail traderFinance = traderFinancialService.getTraderFinancialById(traderCustomerFinanceId);
        mv.addObject("traderCustomerFinanceId", traderCustomerFinanceId);
        mv.addObject("trader",traderFinance);
        return mv;
    }

    /**
     * 去供应商信息编辑页面
     * @param id
     * @return
     */
    @RequestMapping(value = "/editSupplierTrader")
    public ModelAndView editSupplierTrader(@RequestParam(required = false) Integer id) {
        if(Objects.isNull(id)){
            return new ModelAndView("common/fail");
        }
        ModelAndView mv = new ModelAndView("trader/financia/edit_trader_supplier_financia");
        //根据traderCustomerFinanceId 查客户回显使用
        TraderSupplierFinanceDetail traderSupplierFinancial = traderSupplierFinanceService.getTraderSupplierFinancialById(id);
        mv.addObject("traderSupplierFinanceId", id);
        mv.addObject("trader",traderSupplierFinancial);
        return mv;
    }


    /**
     * 保存客户信息财务专用
     * @param request
     * @param traderFinance
     * @return
     */
    @RequestMapping(value = "/save")
    @ResponseBody
    public ResultInfo<?> save(HttpServletRequest request, TraderCustomerFinance traderFinance) {
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        try {
           //修改
            traderFinancialService.updateTraderCustomerFinance(traderFinance,user);
        }catch (Exception e){
            logger.error("updateTraderCustomerFinance  error",e);
            return ResultInfo.error();
        }
        return ResultInfo.success();
    }

    /**
     * 保存供应商信息财务专用
     * @param request
     * @param supplierFinance
     * @return
     */
    @RequestMapping(value = "/saveSupplier")
    @ResponseBody
    public ResultInfo<?> saveSupplier(HttpServletRequest request, TraderSupplierFinance supplierFinance) {
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        try {
            //修改
            traderSupplierFinanceService.updateTraderSupplierFinance(supplierFinance,user);
        }catch (Exception e){
            logger.error("updateTraderSupplierFinance  error",e);
            return ResultInfo.error();
        }
        return ResultInfo.success();
    }


    /**
     *
     * excel上传页面-客户
     * @param request
     * @return
     */
    @RequestMapping(value = "/toUploadExcelPage")
    @NoNeedAccessAuthorization
    public ModelAndView toUploadExcelPage(HttpServletRequest request) {
        ModelAndView mav = new ModelAndView("trader/financia/trader_financia_upload_excel");
        return mav;
    }


    /**
     *
     * excel上传页面-供应商
     * @param request
     * @return
     */
    @RequestMapping(value = "/toUploadExcelPageSupplier")
    @NoNeedAccessAuthorization
    public ModelAndView toUploadExcelPageSupplier(HttpServletRequest request) {
        ModelAndView mav = new ModelAndView("trader/financia/trader_financia_upload_excel_supplier");
        return mav;
    }

    /**
     * 批量导入 客户信息（财务专用）
     * @param file
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/import", method = RequestMethod.POST)
    @ResponseBody
    @NoNeedAccessAuthorization
    public ResultInfo importExcel(HttpServletRequest request,@RequestPart("file") MultipartFile file) throws Exception {
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        try {
           //校验excel  excel 存入 数据库
            traderFinancialService.importTraderCustomerFinancial(file,user);
        } catch (Exception e) {
            logger.error("importTraderCustomerFinancial",e);
            return ResultInfo.error(e.getMessage());
        }
        return ResultInfo.success();
    }

    /**
     * 批量导入 供应商信息（财务专用）
     * @param file
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/importSupplier", method = RequestMethod.POST)
    @ResponseBody
    @NoNeedAccessAuthorization
    public ResultInfo importSupplier(HttpServletRequest request,@RequestPart("file") MultipartFile file) throws Exception {
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        try {
            //校验excel  excel 存入 数据库
            traderSupplierFinanceService.importTraderSupplierFinancial(file,user);
        } catch (Exception e) {
            logger.error("importTraderSupplierFinancial",e);
            return ResultInfo.error(e.getMessage());
        }
        return ResultInfo.success();
    }
}
