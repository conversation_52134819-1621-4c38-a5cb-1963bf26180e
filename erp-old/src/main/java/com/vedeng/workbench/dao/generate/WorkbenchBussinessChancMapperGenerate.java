package com.vedeng.workbench.dao.generate;

import com.vedeng.workbench.model.dto.generate.WorkbenchBussinessChanceDo;

public interface WorkbenchBussinessChancMapperGenerate {
    int deleteByPrimaryKey(Integer bussinessChanceId);

    int insert(WorkbenchBussinessChanceDo record);

    int insertSelective(WorkbenchBussinessChanceDo record);

    WorkbenchBussinessChanceDo selectByPrimaryKey(Integer bussinessChanceId);

    int updateByPrimaryKeySelective(WorkbenchBussinessChanceDo record);

    int updateByPrimaryKey(WorkbenchBussinessChanceDo record);
}