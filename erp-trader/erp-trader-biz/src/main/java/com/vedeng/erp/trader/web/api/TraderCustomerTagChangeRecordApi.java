package com.vedeng.erp.trader.web.api;

import cn.hutool.core.lang.Assert;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.erp.trader.dto.TraderCustomerMarketingNodeDto;
import com.vedeng.erp.trader.dto.TraderCustomerTagChangeRecordDto;
import com.vedeng.erp.trader.service.TraderCustomerMarketingNodeApiService;
import com.vedeng.erp.trader.service.TraderCustomerTagChangeRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/9 13:16
 **/
@ExceptionController
@RestController
@RequestMapping("/traderCustomerTagChangeRecordApi")
@Slf4j
public class TraderCustomerTagChangeRecordApi {

    @Autowired
    private TraderCustomerTagChangeRecordService traderCustomerTagChangeRecordService;

    /**
     * 获取分类
     * @param traderCustomerId
     * @return
     */
    @RequestMapping(value = "/queryByTraderCustomerId")
    @NoNeedAccessAuthorization
    public R<List<List<TraderCustomerTagChangeRecordDto>>> queryByTraderCustomerId(Integer traderCustomerId) {

        return R.success(traderCustomerTagChangeRecordService.queryByTraderCustomerId(traderCustomerId));
    }




}
