package com.vedeng.erp.settlement.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.utils.numgenerator.BillNumGenerator;
import com.vedeng.common.core.utils.numgenerator.bean.BillGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.bean.NoGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.enums.BillType;
import com.vedeng.erp.settlement.domain.entity.CapitalBillEntity;
import com.vedeng.erp.finance.dto.CapitalBillDetailDto;
import com.vedeng.erp.finance.dto.CapitalBillDto;
import com.vedeng.erp.settlement.mapper.CapitalBillDetailMapper;
import com.vedeng.erp.settlement.mapper.CapitalBillMapper;
import com.vedeng.erp.settlement.mapstruct.CapitalBillConvertor;
import com.vedeng.erp.settlement.mapstruct.CapitalBillDetailConvertor;
import com.vedeng.erp.settlement.service.CapitalBillService;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.UserApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 交易流水
 * @date 2023/11/28 11:05
 */
@Service
@Slf4j
public class CapitalBillServiceImpl implements CapitalBillService {


    @Autowired
    CapitalBillMapper capitalBillMapper;
    @Autowired
    CapitalBillDetailMapper capitalBillDetailMapper;
    @Autowired
    CapitalBillConvertor capitalBillConvertor;
    @Autowired
    CapitalBillDetailConvertor capitalBillDetailConvertor;
    @Autowired
    UserApiService userApiService;

    @Override
    public void create(CapitalBillDto capitalBillDto) {
        log.info("资金流水：{}", JSON.toJSONString(capitalBillDto));
        CapitalBillEntity capitalBillEntity = capitalBillConvertor.toEntity(capitalBillDto);
        capitalBillMapper.insertSelective(capitalBillEntity);
        //流水编号
        BillGeneratorBean billGeneratorBean = new BillGeneratorBean(BillType.CAPITAL_NO,
                NoGeneratorBean.builder().dateFormat(ErpConstant.YYYYMMDDHHMMSS).id(capitalBillEntity.getCapitalBillId()).numberOfDigits(9).build());
        String capitalBillNo = new BillNumGenerator().distribution(billGeneratorBean);
        capitalBillEntity.setCapitalBillNo(capitalBillNo);
        capitalBillMapper.updateByPrimaryKeySelective(capitalBillEntity);

        capitalBillDto.setCapitalBillNo(capitalBillNo);
        capitalBillDto.setCapitalBillId(capitalBillEntity.getCapitalBillId());
        CapitalBillDetailDto capitalBillDetailDto = capitalBillDto.getCapitalBillDetailDto();
        UserDto userDto = userApiService.getUserById(capitalBillDto.getCreator());
        capitalBillDetailDto.setUserId(userDto.getUserId());
        capitalBillDetailDto.setOrgId(userDto.getOrgId());
        capitalBillDetailDto.setOrgName(userDto.getOrgName());
        capitalBillDetailDto.setCapitalBillId(capitalBillEntity.getCapitalBillId());
        capitalBillDetailMapper.insertSelective(capitalBillDetailConvertor.toEntity(capitalBillDetailDto));
    }

    @Override
    public List<CapitalBillDto> findAcceptanceBill(String acceptanceBillNo) {
        return capitalBillMapper.findAcceptanceBill(acceptanceBillNo);
    }

    @Override
    public void update(CapitalBillDto capitalBillDto) {
        log.info("资金流水：{}", JSON.toJSONString(capitalBillDto));
        CapitalBillEntity capitalBillEntity = capitalBillConvertor.toEntity(capitalBillDto);
        capitalBillMapper.updateByPrimaryKeySelective(capitalBillEntity);
    }


}
