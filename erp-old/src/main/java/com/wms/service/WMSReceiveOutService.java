package com.wms.service;


import com.vedeng.authorization.model.User;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.wms.model.dto.AddReceiveOutDto;
import com.wms.model.dto.WmsOutputOrderGoodsDto;
import com.wms.model.po.WmsOutputOrder;

import java.util.List;

/**
 * 领用出库单
 */
public interface WMSReceiveOutService {

    WmsOutputOrder findReceiveOutById(Long receiveOutId);

    List<WmsOutputOrderGoodsDto> queryOutputGoodsByReceiveOutId(Long scrappedOutOrderId);

    List<WarehouseGoodsOperateLog> getWlogList(Integer wmsOutputOrderId, Integer logOperateType);

    void updateReceiveOutAuditStatus(Long receiveOutId, int value);


    /**
     * 新增领用出库单
     * @param addReceiveOutDto 领用出库单
     * @param user 操作人
     * @return 生成的自增主键 ReceiveOutOrderId
     * @throws Exception
     */
    public Long addReceiveOutOrder(AddReceiveOutDto addReceiveOutDto, User user) throws Exception;

    /**
     * 根据 userId 查询用户信息
     * @param userId 用户ID
     * @return 用户信息
     */
    public User getUserInfoByUserId(Integer userId);
}
