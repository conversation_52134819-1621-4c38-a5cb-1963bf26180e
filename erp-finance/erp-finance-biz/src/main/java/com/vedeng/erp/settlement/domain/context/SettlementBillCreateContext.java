package com.vedeng.erp.settlement.domain.context;

import com.vedeng.erp.settlement.domain.dto.SettlementBillDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 结算单 生成参数上下文类
 * @date 2023/12/8 10:05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SettlementBillCreateContext<T> {


    private SettlementBillDto settlementBillDto;


    private T t;

}
