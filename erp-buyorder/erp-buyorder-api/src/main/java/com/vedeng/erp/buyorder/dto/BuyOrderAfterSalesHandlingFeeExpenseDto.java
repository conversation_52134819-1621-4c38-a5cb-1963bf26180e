package com.vedeng.erp.buyorder.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 保存采购售后生成的费用单对象
 * @date 2022/11/18 10:06
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BuyOrderAfterSalesHandlingFeeExpenseDto {

    /**
     * 售后单id
     */
    private Integer afterSalesId;

    /**
     * 售后单号
     */
    private String afterSalesNo;

    /**
     * 采购单号
     */
    private String buyorderNo;

    /**
     * 采购单id
     */
    private Integer buyorderId;

    /**
     * 商品ID
     */
    private Integer goodsId;

    /**
     * 数量
     */
    private Integer num;


    /**
     *  价格
     */
    private BigDecimal price;

    /**
     *  sku
     */
    private String sku;

    /**
     * 票种
     */
    private Integer invoiceType;


}
