package com.newtask;

import com.newtask.model.SkuInfoImageDto;
import com.newtask.service.PriceCenterSkuInfoImageService;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.http.NewHttpClientUtils;
import com.vedeng.common.util.JsonUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@JobHandler(value = "priceCenterSkuInfoImageTask")
@Component
public class PriceCenterSkuInfoImageTask extends AbstractJobHandler {

    @Value("${price.url}")
    private String priceUrl;


    private static String SKU_INFO_IMAGE = "contract_price/skuInfoImage";

    private static String SKU_INFO_IMAGE_ALL = "contract_price/skuInfoImageAll";

    Logger logger = LoggerFactory.getLogger(PriceCenterSkuInfoImageTask.class);

    @Autowired
    private PriceCenterSkuInfoImageService priceCenterSkuInfoImageService;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        XxlJobLogger.log("价格中心同步sku基本信息start-----------");
        logger.info("价格中心同步sku基本信息start-----------");
        List<SkuInfoImageDto> list = priceCenterSkuInfoImageService.getSkuInfoImage();
        if (s.equals("first")) {
            List<SkuInfoImageDto> listAll = priceCenterSkuInfoImageService.getSkuInfoImageAll();
            String requestJson = JsonUtils.translateToJson(listAll);
            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + SKU_INFO_IMAGE, requestJson);
        } else {
            String requestJson = JsonUtils.translateToJson(list);
            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + SKU_INFO_IMAGE, requestJson);
        }
        logger.info("价格中心同步sku基本信息end-----------");
        XxlJobLogger.log("价格中心同步sku基本信息end-----------");
        return SUCCESS;
    }
}
