// 操作记录列表--弹层
Vue.component('operation-log-dialog', {
    template: `
        <ui-dialog
            :visible.sync="isShow"
            title="操作记录"
            width="960px"
            align="center"
        >
            <div class="records-panel">
                <div class="panel-wrap" style="max-height: 560px;" id="operation-dialog">
                    <div class="operation-records">
                        <div class="list" v-if="list.length">
                            <div class="item" v-for="(item, index) in list" :key="index">
                                <div class="row">
                                    <div class="creator">
                                        <img :src="item.aliasHeadPicture || GLOBAL.defaultAvatar" class="icon"/>
                                        <div class="name">{{ item.creatorName }}</div>
                                    </div>
                                    <div class="time">{{ item.operationTime }}</div>
                                </div>
                                <div class="text-line-7">{{ item.logContent }}</div>
                            </div>
                        </div>
                        <div class="panel-null-data" v-else>
                            <i class="vd-ui_icon icon-info1 icon"></i>
                            <p class="font">历史数据暂无操作记录</p>
                        </div>
                    </div>
                </div>
            </div>
        </ui-dialog>
    `,

    props: {
        pageSize: {
            type: Number,
            default: 10
        },
        pageNum: {
            type: Number,
            default: 1
        }
    },
    data () {
        return {
            bizTypeEnum: '', // 业务类型 01线索池 02商机库 03 报价单
            relatedId: '', // 业务id
            quoteorderId: '', // 报价单id
            isShow: false,
            list: [],
            isEnd: false,
            isLoading: false,
            url: ''
        }
    },
    mounted () {
        let scrollEle = document.getElementById('operation-dialog');
        scrollEle && scrollEle.addEventListener('scroll', this.scrollFun);
    },
    methods: {
        close () {
            this.isShow = false;
        },
        open (query) {
            this.bizTypeEnum = query.bizTypeEnum;
            this.relatedId = query.relatedId;
            this.quoteorderId = query.quoteorderId;
            this.url = query.url;
            this.pageNum = 1;
            this.initData();
        },
        initData () {
            GLOBAL.showGlobalLoading();

            let url = '/crm/operationLog/public/page';

            let reqData = {
                "param": {
                    "bizList": [{
                        bizTypeEnum: this.bizTypeEnum,
                        bizId: this.relatedId
                    }]
                },
                "pageNum": 1,
                "pageSize": this.pageSize
            };
            if (this.quoteorderId) {
                reqData.param.bizList.push({
                    bizTypeEnum: '03',
                    bizId: this.quoteorderId
                });
            }

            if(this.url) {
                url = this.url;
                reqData.param = this.relatedId;
            }

            this.$axios.post(url, reqData).then(({data}) => {
                if (data.success) {
                    let total = data.data.total || 0;
                    let list = data.data.list || [];
                    this.list = list;
                    this.isEnd = (this.list.length >= total) || (list.length < this.pageSize);

                    this.isShow = true;
                } else {
                    this.$message.error(data.message);
                }
                this.isLoading = false;
                GLOBAL.hideGlobalLoading();
            });
        },
        async scrollFun () {
            let scrollEle = document.getElementById('operation-dialog');
            let scrollTop = scrollEle.scrollTop; // 滑出屏幕之外的高度
            let scrollH = scrollEle.scrollHeight; // 文档总高度
            let screenH = scrollEle.clientHeight; // 屏幕可视高度

            if (scrollH - scrollTop - screenH < 100 && !this.isLoading && !this.isEnd) {
                this.isLoading = true;

                let url = '/crm/operationLog/public/page';

                let reqData = {
                    "param": {
                        "bizList": [{
                            bizTypeEnum: this.bizTypeEnum,
                            bizId: this.relatedId
                        }]
                    },
                    "pageNum": ++this.pageNum,
                    "pageSize": this.pageSize
                };
                if (this.quoteorderId) {
                    reqData.param.bizList.push({
                        bizTypeEnum: '03',
                        bizId: this.quoteorderId
                    });
                }

                if(this.url) {
                    url = this.url;
                    reqData.param = this.relatedId;
                }

                let { data } = await this.$axios.post(url, reqData);
                if (data.success) {
                    let total = data.data.total || 0;
                    let list = data.data.list || [];
                    this.list = this.list.concat(list);
                    this.isEnd = (this.list.length >= total) || (list.length < this.pageSize);
                }
                this.isLoading = false;
            }
        }
    }
})