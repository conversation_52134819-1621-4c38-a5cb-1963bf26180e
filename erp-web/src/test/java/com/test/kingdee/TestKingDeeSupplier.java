package com.test.kingdee;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.erp.kingdee.domain.command.KingDeeSupplierCommand;
import com.vedeng.erp.kingdee.dto.KingDeeSupplierBankDto;
import com.vedeng.erp.kingdee.dto.KingDeeSupplierDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeSupplierCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeSupplierConvertor;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeSupplierMapper;
import com.vedeng.erp.kingdee.service.KingDeeSupplierService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶测试
 * @date 2022/8/29 16:59
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring.xml", "classpath:spring-mybatis.xml"})
public class TestKingDeeSupplier {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private KingDeeSupplierCommandConvertor commandConvertor;

    @Autowired
    private KingDeeSupplierConvertor kingDeeSupplierConvertor;

    @Autowired
    private KingDeeSupplierMapper kingDeeSupplierMapper;
    @Autowired
    private KingDeeSupplierService kingDeeSupplierService;


    @Test
    public void kingDeeSaveSupplier() {
        KingDeeSupplierDto dto = new KingDeeSupplierDto();
        dto.setKingDeeBizEnums(KingDeeBizEnums.saveSupplier);
        dto.setFSupplierId("0");
        dto.setFCreateOrgId(101);
        dto.setFNumber(3232221);
        dto.setFUseOrgId(101);
        dto.setFName("这是测试供应商");
        dto.setFBaseInfo("CG");
        dto.setFSupplierClassify("01");

        List<KingDeeSupplierBankDto> fBankInfo = new ArrayList<>();
        KingDeeSupplierBankDto sonDto = new KingDeeSupplierBankDto();
        sonDto.setFBankCode("**********");
        sonDto.setFBankHolder("供应商测试1");
        sonDto.setFOpenBankName("中国银行");
        sonDto.setFCNAPS("123456");
        fBankInfo.add(sonDto);

        dto.setFBankInfo(fBankInfo);

        kingDeeSupplierService.save(dto);

        KingDeeSupplierCommand command = commandConvertor.toCommand(dto);
        kingDeeBaseApi.save(new SaveExtCommand<>(command, dto.getFormId()));
    }

    //调用记录 - 1 22.11.11 11.13
    //入参   [{"parameters":["BD_Supplier","{\"Creator\":0,\"NeedReturnFields\":[],\"NeedUpDateFields\":[],\"isAutoSubmitAndAudit\":true,\"isAutoAdjustField\":true,\"Model\":{\"FSupplierId\":\"0\",\"FCreateOrgId\":{\"FNumber\":\"101\"},\"FNumber\":\"998877\",\"FUseOrgId\":{\"FNumber\":\"101\"},\"FName\":\"这是测试供应商\",\"FBaseInfo\":{\"FSupplyClassify\":\"CG\"},\"FSupplierClassify\":{\"FNumber\":\"01\"},\"FBankInfo\":[{\"FBankCode\":\"**********\",\"FBankHolder\":\"供应商测试1\",\"FOpenBankName\":\"中国银行\",\"FCNAPS\":\"123456\"}]},\"formId\":\"BD_Supplier\"}"]}]

    //调用金蝶接口，返回值[{"Result":{"ResponseStatus":{"IsSuccess":true,"Errors":[],"SuccessEntitys":[{"Id":322322,"Number":"998877","DIndex":0}],"SuccessMessages":[],"MsgCode":0},"Id":322322,"Number":"998877","NeedReturnData":[{}]}}]

}
