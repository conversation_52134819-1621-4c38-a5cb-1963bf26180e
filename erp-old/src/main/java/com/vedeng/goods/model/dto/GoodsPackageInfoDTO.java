package com.vedeng.goods.model.dto;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @description: 包装信息DTO
 * @author: Laden.chu[<EMAIL>]
 * @createDate: 2021-06-10 16:11
 * @version: 1.0
 */
@Getter
@Setter
@NoArgsConstructor
public class GoodsPackageInfoDTO {

    //商品Id
    private Integer skuId;

    private Integer spuId;

    //是否启用多级包装
    private Integer isEnableMultistagePackage;

    //中包装数量
    private Integer midPackageNum;

    //箱包装数量
    private Integer boxPackageNum;
}
