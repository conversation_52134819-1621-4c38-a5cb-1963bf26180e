//存储条件温度校验规则
jQuery.validator.addMethod("ltTem", function(value, element) {
    return compare('lessTemperature','greatTemperature');
}, '温度范围有误，请重新填写');
jQuery.validator.addMethod("gtTem", function(value, element) {
    return compare('lessTemperature','greatTemperature');
}, '温度范围有误，请重新填写');

//存储条件湿度校验规则
jQuery.validator.addMethod("ltHum", function(value, element) {
    var lessValue = parseInt($("input[name=storageConditionHumidityLowerValue]").val());
    var greatValue = parseInt($("input[name=storageConditionHumidityUpperValue]").val());
    return lessValue < greatValue;
}, '湿度度范围有误，请重新填写');

jQuery.validator.addMethod("gtHum", function(value, element) {
    var lessValue = parseInt($("input[name=storageConditionHumidityLowerValue]").val());
    var greatValue = parseInt($("input[name=storageConditionHumidityUpperValue]").val());
    return lessValue < greatValue;
}, '湿度度范围有误，请重新填写');



function compare(lessOneId, largeOneId) {
    var returnVal = true;
    var lessValue = parseInt($("#"+lessOneId).val());
    var greatValue = parseInt($("#"+largeOneId).val());
    if (lessValue >= greatValue) {
        returnVal = false;
    }
    return returnVal;
}


$('input[name=storageConditionTemperature]').change(function () {
    if(this.value==='4'&& this.checked){
        $('#lessTemperature').attr('disabled', false)
        $('#greatTemperature').attr('disabled', false)
    }else {
        $('#lessTemperature').attr('disabled', true)
        $('#greatTemperature').attr('disabled', true)
    }
});