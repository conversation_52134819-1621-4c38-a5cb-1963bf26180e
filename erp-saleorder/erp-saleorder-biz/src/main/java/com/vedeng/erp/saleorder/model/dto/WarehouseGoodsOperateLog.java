package com.vedeng.erp.saleorder.model.dto;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class WarehouseGoodsOperateLog implements Serializable
{
	/**
	 * serialVersionUID
	 */
	private static final long serialVersionUID = 2822391713822945210L;

	private Integer warehouseGoodsOperateLogId;

	private Integer barcodeId;

	private Integer companyId;

	private Integer operateType;

	private Integer relatedId;

	private Integer goodsId;

	private String barcodeFactory;

	private Integer num;

	private Integer warehouseId;

	private String batchNumber;

	private Long productDate;

	private Long expirationDate;

	private String comments;

	private Long addTime;

	private Integer creator;

	private Long modTime;

	private Integer updater;

	private Integer isEnable;

	private String goodsName;//产品名称

	private String model;//型号

	private String spec;//规格

	private String supplyModel;//供应商型号

	private String brandName;//品牌

	private String materialCode;//物料编码

	private String unitName;//单位

	private String sku;//订货号

	private String buyorderNo;//采购单号

	private Integer buyorderId;//采购ID

	private String saleorderNo;//销售单号

	private Integer saleorderId;//销售ID

	private Integer afterSalesId;//售后id

	private String afterSalesNo;//售后单号

	private BigDecimal xsprice;//销售单价

	private BigDecimal cgprice;//采购单价

	private Long beginTime; //开始时间

	private Long endTime; //开始时间

	private String expirationDateStr;//效期

	private String traderName;//供应商

	private Integer orderId;//业务订单id

	private String orderNo;//业务订单No

	private String expiration_date_str;//效期

	private String productDateStr;//生产日期

	private String problemRemark;  //问题原因

	private Integer logicalWarehouseId; //逻辑仓id

	private String addTimeStr;

	private BigDecimal amount;//总价

	private BigDecimal costPrice;//成本价

	private BigDecimal newCostPrice;
	/**
	 * 贝登批次码
	 */
	private String vedengBatchNumer;

	/**
	 * 剩余库存数量'
	 */
	private Integer lastStockNum;

	/**
	 * 灭菌编号
	 */
	private String sterilizationBatchNo;

	/**
	 * 入库条码是否使用  0未使用 1使用'
	 */
	private Integer isUse;
	/**
	 * 日志类型  0入库   1出库
	 */
	private Integer logType;

	private Integer isExpress;

	private Integer searchoperateType;

	private String operateTypeStr;

	/**
	 * 来源信息
	 */
	private String tagSources;

}
