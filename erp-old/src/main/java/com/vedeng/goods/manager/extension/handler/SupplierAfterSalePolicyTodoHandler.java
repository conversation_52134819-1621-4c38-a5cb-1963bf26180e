package com.vedeng.goods.manager.extension.handler;

import com.vedeng.aftersales.dao.AfterSaleServiceStandardApplyMapper;
import com.vedeng.aftersales.model.AfterSaleServiceStandardApply;
import com.vedeng.goods.manager.constants.GoodsValidConstants;
import com.vedeng.todolist.service.ITodoInstance;
import com.vedeng.todolist.service.impl.MaintainDataSupplyAfterSalePolicy;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Component
public class SupplierAfterSalePolicyTodoHandler extends BaseGoodsTodoHandler {

    @Resource
    private AfterSaleServiceStandardApplyMapper afterSaleServiceStandardApplyMapper;

    @Resource
    private MaintainDataSupplyAfterSalePolicy maintainDataSupplyAfterSalePolicy;

    @Override
    public int getServiceId() {
        return SUPPLIER_AFTER_SALES_SERVICE_ID;
    }

    @Override
    public ITodoInstance getITodoInstance() {
        return maintainDataSupplyAfterSalePolicy;
    }

    @Override
    public boolean isDone() {
        AfterSaleServiceStandardApply afterSaleServiceStandardApply = afterSaleServiceStandardApplyMapper.selectAfterSaleServiceStandardBySkuNo(getSkuNo());
        return afterSaleServiceStandardApply != null && Objects.equals(afterSaleServiceStandardApply.getSupplyAfterSaleIsMaintain(), GoodsValidConstants.ENABLE);
    }

    @Override
    public void onHandler() {
        if (!isDone()) {
            addGoodsTodoRecord(getGoodId(), getSkuNo());
        }
    }

    @Override
    public void finish(Object param) {
        if (param instanceof String) {
            String skuNo = (String) param;
            if (StringUtils.isNotBlank(skuNo)) {
                Integer skuId;
                try {
                    skuId = Integer.valueOf(skuNo.trim().substring(1));
                } catch (Exception e) {
                    logger.error("处理供应商售后政策待办时解析skuId失败", e);
                    return;
                }
                maintainDataSupplyAfterSalePolicy.finish(skuId);
            }
        }
    }
}
