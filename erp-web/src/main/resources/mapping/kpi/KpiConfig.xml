<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.kpi.dao.KpiConfigMapper">

    <resultMap id="groupResultMap" type="com.vedeng.kpi.model.DTO.KpiGroupConfigDto">
        <result column="GROUP_ID" jdbcType="INTEGER" property="groupId" />
        <result column="GROUP_NAME" jdbcType="INTEGER" property="groupName" />
        <collection property="groupManagerId" ofType="Integer">
            <result column="GROUP_MANAGER_ID" />
        </collection>
        <collection property="configItems" ofType="com.vedeng.kpi.model.DTO.KpiConfigItemDto">
            <result column="CONFIG_ID" jdbcType="INTEGER" property="configId" />
            <result column="ITEM" jdbcType="VARCHAR" property="item" />
            <result column="GROUP_CONFIG_ID" jdbcType="INTEGER" property="groupConfigId" />
            <result column="WEIGHT" jdbcType="INTEGER" property="weight" />
        </collection>
        <collection property="teams" ofType="com.vedeng.kpi.model.DTO.KpiTeamDto">
            <result column="TEAM_ID" jdbcType="INTEGER" property="teamId" />
            <result column="TEAM_NAME" jdbcType="VARCHAR" property="teamName" />
            <result column="TEAM_MANAGER_ID" jdbcType="INTEGER" property="teamManagerId" />
            <collection property="users" ofType="com.vedeng.kpi.model.DTO.KpiUserConfigDto">
                <result column="USER_ID" jdbcType="INTEGER" property="userId" />
                <result column="USER_GROUP_ID" jdbcType="INTEGER" property="groupId" />
                <result column="USER_GROUP_NAME" jdbcType="VARCHAR" property="groupName" />
                <result column="USER_TEAM_ID" jdbcType="INTEGER" property="teamId" />
                <result column="USER_TEAM_NAME" jdbcType="VARCHAR" property="teamName" />
            </collection>
        </collection>

    </resultMap>

    <resultMap id="userConfigResultMap" type="com.vedeng.kpi.model.DTO.KpiUserConfigDto">
        <result column="USER_ID" jdbcType="INTEGER" property="userId" />
        <result column="USER_NAME" jdbcType="VARCHAR" property="userName" />
        <result column="GROUP_ID" jdbcType="INTEGER" property="groupId" />
        <result column="GROUP_NAME" jdbcType="VARCHAR" property="groupName" />
        <result column="TEAM_ID" jdbcType="INTEGER" property="teamId" />
        <result column="TEAM_NAME" jdbcType="VARCHAR" property="teamName" />
        <result column="GOAL_MONTH_AMOUNT" jdbcType="INTEGER" property="monthAmountGoal" />
        <result column="CONFIG_DATE" jdbcType="DATE" property="configDate" />
        <result column="MONTH_STR" jdbcType="VARCHAR" property="monthStr" />
    </resultMap>

    <select id="getGroupConfig" parameterType="com.vedeng.kpi.model.DTO.KpiConfigQueryDto" resultMap="groupResultMap">
        SELECT
            tg.SALES_PERFORMANCE_GROUP_ID   as GROUP_ID,
            tg.GROUP_NAME,
            tgm.USER_ID                     as GROUP_MANAGER_ID,
            td.SALES_PERFORMANCE_DEPT_ID    as TEAM_ID,
            td.DEPT_NAME                    as TEAM_NAME,
            tdm.USER_ID                     as TEAM_MANAGER_ID,
            tc.ITEM,
            tc.SALES_PERFORMANCE_CONFIG_ID  as CONFIG_ID,
            tdu.USER_ID as USER_ID,
            tg.GROUP_NAME as USER_GROUP_NAME,
            tg.SALES_PERFORMANCE_GROUP_ID as USER_GROUP_ID,
            td.SALES_PERFORMANCE_DEPT_ID    as USER_TEAM_ID,
            td.DEPT_NAME                    as USER_TEAM_NAME,
            ifnull(tjc.WEIGHT,
                   0)           as WEIGHT,
            tjc.SALES_PERFORMANCE_CONFIG_ID as GROUP_CONFIG_ID
        FROM
            T_SALES_PERFORMANCE_GROUP tg
                LEFT JOIN
            T_SALES_PERFORMANCE_GROUP_MANAGER tgm
            ON tgm.SALES_PERFORMANCE_GROUP_ID = tg.SALES_PERFORMANCE_GROUP_ID
                LEFT JOIN
            T_SALES_PERFORMANCE_DEPT td
            ON td.SALES_PERFORMANCE_GROUP_ID = tg.SALES_PERFORMANCE_GROUP_ID
                LEFT JOIN
            T_SALES_PERFORMANCE_DEPT_MANAGER tdm
            ON tdm.SALES_PERFORMANCE_DEPT_ID = td.SALES_PERFORMANCE_DEPT_ID
                LEFT JOIN T_SALES_PERFORMANCE_DEPT_USER tdu
                          ON tdu.SALES_PERFORMANCE_DEPT_ID = td.SALES_PERFORMANCE_DEPT_ID
                LEFT JOIN
            T_R_SALES_PERFORMANCE_GROUP_J_CONFIG tjc
            ON tjc.SALES_PERFORMANCE_GROUP_ID = tg.SALES_PERFORMANCE_GROUP_ID
                LEFT JOIN
            T_SALES_PERFORMANCE_CONFIG tc
            ON tc.SALES_PERFORMANCE_CONFIG_ID = tjc.SALES_PERFORMANCE_CONFIG_ID
        WHERE tg.IS_DELETE = 0
          AND tg.COMPANY_ID =  #{companyId,jdbcType=INTEGER}
          AND tc.COMPANY_ID = #{companyId,jdbcType=INTEGER}
          AND tg.SALES_PERFORMANCE_GROUP_ID = #{groupId,jdbcType=INTEGER}
    </select>

    <select id="getBatchGroupConfig" parameterType="com.vedeng.kpi.model.DTO.KpiConfigQueryDto" resultMap="groupResultMap">
        SELECT
        tg.SALES_PERFORMANCE_GROUP_ID   as GROUP_ID,
        tg.GROUP_NAME,
        tgm.USER_ID                     as GROUP_MANAGER_ID,
        td.SALES_PERFORMANCE_DEPT_ID    as TEAM_ID,
        td.DEPT_NAME                    as TEAM_NAME,
        tdm.USER_ID                     as TEAM_MANAGER_ID,
        tc.ITEM,
        tc.SALES_PERFORMANCE_CONFIG_ID  as CONFIG_ID,
        tdu.USER_ID as USER_ID,
        tg.GROUP_NAME as USER_GROUP_NAME,
        tg.SALES_PERFORMANCE_GROUP_ID as USER_GROUP_ID,
        td.SALES_PERFORMANCE_DEPT_ID    as USER_TEAM_ID,
        td.DEPT_NAME                    as USER_TEAM_NAME,
        ifnull(tjc.WEIGHT,
        0)           as WEIGHT,
        tjc.SALES_PERFORMANCE_CONFIG_ID as GROUP_CONFIG_ID
        FROM
        T_SALES_PERFORMANCE_GROUP tg
        LEFT JOIN
        T_SALES_PERFORMANCE_GROUP_MANAGER tgm
        ON tgm.SALES_PERFORMANCE_GROUP_ID = tg.SALES_PERFORMANCE_GROUP_ID
        LEFT JOIN
        T_SALES_PERFORMANCE_DEPT td
        on td.SALES_PERFORMANCE_GROUP_ID = tg.SALES_PERFORMANCE_GROUP_ID
        LEFT JOIN
        T_SALES_PERFORMANCE_DEPT_MANAGER tdm
        ON tdm.SALES_PERFORMANCE_DEPT_ID = td.SALES_PERFORMANCE_DEPT_ID
        LEFT JOIN T_SALES_PERFORMANCE_DEPT_USER tdu
        ON tdu.SALES_PERFORMANCE_DEPT_ID = td.SALES_PERFORMANCE_DEPT_ID
        LEFT JOIN
        T_R_SALES_PERFORMANCE_GROUP_J_CONFIG tjc
        ON tjc.SALES_PERFORMANCE_GROUP_ID = tg.SALES_PERFORMANCE_GROUP_ID
        LEFT JOIN
        T_SALES_PERFORMANCE_CONFIG tc
        ON tc.SALES_PERFORMANCE_CONFIG_ID = tjc.SALES_PERFORMANCE_CONFIG_ID
        WHERE tg.IS_DELETE = 0
        and td.IS_DELETE = 0
        AND tg.COMPANY_ID =  #{companyId,jdbcType=INTEGER}
        AND tc.COMPANY_ID = #{companyId,jdbcType=INTEGER}
        <if test="groupIds != null">
            AND tg.SALES_PERFORMANCE_GROUP_ID in
            <foreach collection="groupIds" item="groupIds" open="(" close=")" index="index" separator=",">
                #{groupIds,jdbcType=INTEGER}
            </foreach>
        </if>

    </select>

    <select id="getBatchUserConfig" parameterType="com.vedeng.kpi.model.DTO.KpiConfigQueryDto" resultMap="userConfigResultMap">
        SELECT a.USER_ID,
               a.GOAL                       as GOAL_MONTH_AMOUNT,
               b.SALES_PERFORMANCE_GROUP_ID AS GROUP_ID,
               b.SALES_PERFORMANCE_DEPT_ID  AS TEAM_ID,
               b.DEPT_NAME                  as TEAM_NAME,
               d.GROUP_NAME                 as GROUP_NAME,
               e.USERNAME                   as USER_NAME
        FROM T_SALES_PERFORMANCE_DEPT_USER a
        LEFT JOIN T_SALES_PERFORMANCE_DEPT b on b.SALES_PERFORMANCE_DEPT_ID = a.SALES_PERFORMANCE_DEPT_ID
        LEFT JOIN T_SALES_PERFORMANCE_GROUP d on d.SALES_PERFORMANCE_GROUP_ID = b.SALES_PERFORMANCE_GROUP_ID
        LEFT JOIN T_USER e on e.USER_ID = a.USER_ID
        WHERE a.USER_ID in
        <foreach collection="userIds" item="userIds" open="(" close=")" index="index" separator=",">
            #{userIds,jdbcType=INTEGER}
        </foreach>
    </select>

    <select id="getUserConfig" parameterType="com.vedeng.kpi.model.DTO.KpiConfigQueryDto" resultMap="userConfigResultMap">
        SELECT a.USER_ID,
               ifnull(a.GOAL ,0)            as GOAL_MONTH_AMOUNT,
               b.SALES_PERFORMANCE_GROUP_ID AS GROUP_ID,
               b.SALES_PERFORMANCE_DEPT_ID  AS TEAM_ID,
               b.DEPT_NAME                  as TEAM_NAME,
               d.GROUP_NAME                 as GROUP_NAME,
               e.USERNAME                   as USER_NAME
        FROM T_SALES_PERFORMANCE_DEPT_USER a
        LEFT JOIN T_SALES_PERFORMANCE_DEPT b on b.SALES_PERFORMANCE_DEPT_ID = a.SALES_PERFORMANCE_DEPT_ID
        LEFT JOIN T_SALES_PERFORMANCE_GROUP d on d.SALES_PERFORMANCE_GROUP_ID = b.SALES_PERFORMANCE_GROUP_ID
        LEFT JOIN T_USER e on e.USER_ID = a.USER_ID
        WHERE a.USER_ID = #{userId}
    </select>


    <select id="getUserIds" resultType="java.lang.Integer">
        select USER_ID from T_SALES_PERFORMANCE_DEPT_USER group by USER_ID
    </select>

    <select id="getGroupIds" resultType="java.lang.Integer">
        select SALES_PERFORMANCE_GROUP_ID
        from T_SALES_PERFORMANCE_GROUP
        where IS_DELETE = 0 and COMPANY_ID = 1
        group by SALES_PERFORMANCE_GROUP_ID
    </select>

    <select id="getHisMonthTarget"  resultMap="userConfigResultMap">
        SELECT COALESCE(b.GOAL, a.GOAL, 0)*10000 AS GOAL_MONTH_AMOUNT,
               b.`YEAR_MONTH` as MONTH_STR,
               a.USER_ID                   AS USER_ID,
               c.USERNAME                  AS USER_NAME
        FROM T_SALES_PERFORMANCE_DEPT_USER a
                 JOIN T_USER c ON a.USER_ID = c.USER_ID
                 LEFT JOIN T_SALES_PERFORMANCE_DEPT_USER_GOAL b
                           ON a.SALES_PERFORMANCE_DEPT_USER_ID = b.SALES_PERFORMANCE_DEPT_USER_ID
        where c.IS_DISABLED = 0
          AND c.COMPANY_ID = 1
    </select>

    <select id="getUserOrganization" resultMap="userConfigResultMap">
        select a.USER_ID, b.SALES_PERFORMANCE_GROUP_ID as GROUP_ID, b.SALES_PERFORMANCE_DEPT_ID as TEAM_ID
        from T_SALES_PERFORMANCE_DEPT_USER a
                 LEFT JOIN T_SALES_PERFORMANCE_DEPT b on b.SALES_PERFORMANCE_DEPT_ID = a.SALES_PERFORMANCE_DEPT_ID
    </select>

</mapper>