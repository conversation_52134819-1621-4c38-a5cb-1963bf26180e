package com.wms.service.chain.step;

import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.wms.service.CancelTypeService;
import com.wms.service.WMSInterfaceFactory;
import com.wms.service.chain.AbstractHandlerStep;
import com.wms.service.chain.HandlerStepContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 采购退货出库 反向处理
 */
@Service
public class PurchaseReturnOutWmsRequestReverseHandler extends AbstractHandlerStep {

    private static final Logger LOGGER = LoggerFactory.getLogger(PurchaseReturnOutWmsRequestReverseHandler.class);

    @Resource
    private WMSInterfaceFactory wmsInterfaceFactory;

    @Autowired
    private CancelTypeService cancelTypeService;

    @Override
    protected void doDealWith(HandlerStepContext handlerStepContext) throws Exception {

        AfterSalesVo afterSalesVo = handlerStepContext.get("afterSalesInfo");



        LOGGER.info("采购退货出库，然后关闭，取消出库库,start=====================");
        if(!cancelTypeService.cancelOutPurchaseReturnMethod(afterSalesVo.getAfterSalesNo(),"售后单关闭,请求取消")){
            throw new Exception("出库单取消失败");
        }
    }

}

