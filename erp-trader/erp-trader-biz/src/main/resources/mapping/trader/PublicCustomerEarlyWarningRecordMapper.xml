<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.erp.trader.mapper.PublicCustomerEarlyWarningRecordMapper">

    <insert id="insertRecord" parameterType="com.vedeng.erp.trader.domain.PublicCustomerEarlyWarningRecord"
            useGeneratedKeys="true" keyProperty="publicCustomerEarlyWarningRecordId">
        insert into T_PUBLIC_CUSTOMER_EARLY_WARNING_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="publicCustomerEarlyWarningRecordId != null">
                PUBLIC_CUSTOMER_EARLY_WARNING_RECORD_ID,
            </if>
            <if test="traderCustomerId != null">
                TRADER_CUSTOMER_ID ,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="publicCustomerEarlyWarningRecordId != null">
                #{publicCustomerEarlyWarningRecordId,jdbcType=BIGINT},
            </if>
            <if test="traderCustomerId != null">
                #{traderCustomerId,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <insert id="batchAddRecord">
        INSERT INTO T_PUBLIC_CUSTOMER_EARLY_WARNING_RECORD (`TRADER_CUSTOMER_ID`,`ADD_TIME`) VALUES
        <foreach collection="traderCustomerIds" separator="," item="traderCustomerId">
            (#{traderCustomerId,jdbcType=INTEGER},#{currentTime})
        </foreach>
    </insert>
</mapper>