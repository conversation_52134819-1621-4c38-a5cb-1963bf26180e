package com.vedeng.workbench.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableList;
import com.vedeng.common.util.DateUtil;
import com.vedeng.dwh.constant.DwhConstants;
import com.vedeng.dwh.model.dto.DwhErpSubDeptDto;
import com.vedeng.dwh.model.dto.DwhErpUserDto;
import com.vedeng.dwh.model.dto.DwhWorkbenchDto;
import com.vedeng.dwh.model.dto.DwhWorkbenchOverviewDto;
import com.vedeng.dwh.model.generate.DwmNjUserOrgDfDo;
import com.vedeng.dwh.service.DwhBussinessChanceService;
import com.vedeng.dwh.service.DwhThreadLocalService;
import com.vedeng.workbench.dao.WorkbenchBussinessChanceExtMapper;
import com.vedeng.workbench.model.HeaddataSummaryDto;
import com.vedeng.workbench.model.dto.*;
import com.vedeng.workbench.model.dto.emptyDto.*;
import com.vedeng.workbench.model.dto.generate.WorkbenchBussinessChanceDo;
import com.vedeng.workbench.service.WorkbenchBussinessChanceService;
import com.vedeng.workbench.utils.WorkbenchUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

@SuppressWarnings("Duplicates")


@Slf4j
@Service
public class WorkbenchBussinessChanceServiceImpl implements WorkbenchBussinessChanceService {

    @Autowired
    private WorkbenchBussinessChanceExtMapper bussinessChanceMapper;

    @Autowired
    private DwhThreadLocalService dwhThreadLocalService;

    @Autowired
    private DwhBussinessChanceService dwhBussinessChanceService;


    @Override
    public List<WorkbenchBussinessChanceExtDto> getThisWeekExpectOrderChanceListById(WorkbenchDataQueryDto dataQueryDto) {

        //设置查询时间条件

        //获取本周的开始时间和结束时间
        Date thisWeekStart = WorkbenchUtil.getThisWeekStart();
        Long startTime = DateUtil.DateToLong(thisWeekStart);

        Date thisWeekEnd = WorkbenchUtil.getThisWeekEnd();
        Long endTime = DateUtil.DateToLong(thisWeekEnd);

        dataQueryDto.setStartTime(startTime);
        dataQueryDto.setEndTime(endTime);


        List<WorkbenchBussinessChanceExtDto> thisWeekExpectOrderChanceList = bussinessChanceMapper.getThisWeekExpectOrderChanceListById(dataQueryDto);

        thisWeekExpectOrderChanceList = thisWeekExpectOrderChanceList.stream().sorted(Comparator.comparingLong(WorkbenchBussinessChanceDo::getOrderTime)).collect(Collectors.toList());


        return thisWeekExpectOrderChanceList;
    }


    @Override
    public List<WorkbenchBussinessChanceExtDto> getTodayToCommunicateBussinessChanceById(WorkbenchDataQueryDto dataQueryDto) {

        Date nowDate = new Date();
        //设置查询时间为今天
        dataQueryDto.setNextContactDate(new Date());
        List<WorkbenchBussinessChanceExtDto> todayToCommunicateBussinessChanceList = bussinessChanceMapper.getTodayToCommunicateBussinessChanceById(dataQueryDto);

        //处理得到待交流商机的最近一次商机
        todayToCommunicateBussinessChanceList.stream().forEach(bussinessChance -> {
            List<BussinessCommunicateRecordDto> bussinessCommunicateRecordDtoList = bussinessChance.getBussinessCommunicateRecordDtoList();
            //stream流表达式排序
            bussinessChance.setContactType(-1);
            if (!CollectionUtils.isEmpty(bussinessCommunicateRecordDtoList)) {


                List<BussinessCommunicateRecordDto> collect = bussinessCommunicateRecordDtoList.stream().sorted((x, y) -> {
                    if (x.getEndTime() >= y.getEndTime()) {
                        return -1;
                    } else {
                        return 1;
                    }
                }).collect(Collectors.toList());
                bussinessChance.setLastCommunicateRecord(collect.get(0));
                bussinessChance.getLastCommunicateRecord().setEndDate(new Date(bussinessChance.getLastCommunicateRecord().getEndTime()));

                if (WorkbenchUtil.sameDate(bussinessChance.getLastCommunicateRecord().getEndDate(), nowDate)) {
                    bussinessChance.setContactType(1);
                }
            }
        });
        //排序
        todayToCommunicateBussinessChanceList = todayToCommunicateBussinessChanceList.stream().sorted(Comparator.comparing(WorkbenchBussinessChanceExtDto::getContactType, Comparator.comparingInt(x -> x))
                .thenComparing(WorkbenchBussinessChanceExtDto::getBussinessAmount, (x, y) -> x.compareTo(y) * (-1))).collect(Collectors.toList());

        return todayToCommunicateBussinessChanceList;
    }

    @Override
    public List<WorkbenchBussinessChanceExtDto> getTodayNewAddBussinessChnaceListById(WorkbenchDataQueryDto dataQueryDto) {
        //设置查询时间
        Long startTime = DateUtil.getDayStartTime(new Date()).getTime();
        Long endTime = DateUtil.getDayEndTime(new Date()).getTime();

        dataQueryDto.setStartTime(startTime);
        dataQueryDto.setEndTime(endTime);

        List<WorkbenchBussinessChanceExtDto> todayNewAddBussinessChanceList = bussinessChanceMapper.getTodayNewAddBussinessChnaceListById(dataQueryDto);

        //排序规则
        List<Integer> sortList = Arrays.asList(0, 6, 1, 2, 3, 7, 4);
        todayNewAddBussinessChanceList = todayNewAddBussinessChanceList.stream()
                .sorted(Comparator.comparing(WorkbenchBussinessChanceExtDto::getStatus, (x, y) -> {
                    //按照读取的list顺序排序
                    for (Integer no : sortList) {
                        if (no.equals(x) || no.equals(y)) {
                            if (x.equals(y)) {
                                return 0;
                            } else if (no.equals(x)) {
                                return -1;
                            } else {
                                return 1;
                            }
                        }
                    }
                    return 0;
                }).thenComparing(WorkbenchBussinessChanceExtDto::getAddTime)).collect(Collectors.toList());

        todayNewAddBussinessChanceList.stream().forEach(x -> {
            if (x != null && !StringUtils.isEmpty(x.getGoodsName())) {
                if (x.getGoodsName().indexOf(",") > 0) {
                    x.setGoodsName(x.getGoodsName().substring(0, x.getGoodsName().indexOf(",")));
                }
                if (x.getGoodsName().length() > 60) {
                    x.setGoodsName(x.getGoodsName().substring(0, 60) + "...");
                }
                if (x.getContent().indexOf(",") > 0) {
                    x.setContent(x.getContent().substring(0, x.getContent().indexOf(",")));
                }
                if (x.getGoodsName().length() > 60) {
                    x.setContent(x.getContent().substring(0, 60) + "...");
                }
            }
        });

        return todayNewAddBussinessChanceList;
    }


    @Override
    public List<ExpectThisWeekOrderSummaryDto> getExpectThisWeekOrderSummaryDtoListByIds(WorkbenchDataQueryDto dataQueryDto) {


        List<ExpectThisWeekOrderSummaryDto> expectThisWeekOrderSummaryDtoList = new ArrayList<>();

        //设置总计数值
        Integer allLaskWeekTotalNum = 0;
        Integer allLaskWeekfailNum = 0;
        Integer allThisWeekTotalNum = 0;
        BigDecimal allHitRate = new BigDecimal(0);
        BigDecimal allThisWeekTotalAmount = new BigDecimal(0);

        //查询每个销售在上周一00：00至上周日24：00的预计商机
        Long preWeekStartTime = WorkbenchUtil.getPreWeekStart().getTime();
        Long preWeekEndTime = WorkbenchUtil.getPreWeekEnd().getTime();

        Long thisWeekStartTime = WorkbenchUtil.getThisWeekStart().getTime();
        Long thisWeekEndTIME = WorkbenchUtil.getThisWeekEnd().getTime();


        //主管级别获取下属销售列表
        Map<DwhErpSubDeptDto, List<DwhErpSubDeptDto>> subUserOrDept = this.getSubUserOrDept(dataQueryDto);

        //获取下属部门
        List<DwhErpSubDeptDto> deptDtos = subUserOrDept.entrySet().stream()
                .flatMap(e -> e.getValue().stream()).collect(Collectors.toList());
        //获得所有部门对应的销售
        List<DwhErpUserDto> users = deptDtos.stream()
                .flatMap(s -> s.getSubUsers().stream()).collect(Collectors.toList());

        //获取所有销售的ids
        List<Integer> ids = users.stream().map(DwmNjUserOrgDfDo::getUserId).collect(Collectors.toList());

        //非空校验
        if (CollectionUtils.isEmpty(ids)) {
            return expectThisWeekOrderSummaryDtoList;
        }
//            expectThisWeekOrderSummaryDto.setName(subDeptsAndUser.getDeptName());
        //获取上周预计成单相关数据
        dataQueryDto.setStartTime(preWeekStartTime);
        dataQueryDto.setEndTime(preWeekEndTime);
        dataQueryDto.setUserIds(ids);

        List<WorkbenchBussinessChanceExtDto> lashWeekExpectOrderBussniness = bussinessChanceMapper.getExpectOrderBussinessChanceListByIds(dataQueryDto);

        //对上周所有销售的商机分组
        Map<Integer, List<WorkbenchBussinessChanceExtDto>> lastWeekChanceMap = lashWeekExpectOrderBussniness.stream().collect(Collectors.groupingBy(WorkbenchBussinessChanceExtDto::getUserId));

        //获取本周成单相关数据
        dataQueryDto.setStartTime(thisWeekStartTime);
        dataQueryDto.setEndTime(thisWeekEndTIME);

        List<WorkbenchBussinessChanceExtDto> thisWeekExpectOrderBussniness = bussinessChanceMapper.getExpectOrderBussinessChanceListByIds(dataQueryDto);
        //按销售分组
        Map<Integer, List<WorkbenchBussinessChanceExtDto>> ThisWeekChanceMap = thisWeekExpectOrderBussniness.stream().collect(Collectors.groupingBy(WorkbenchBussinessChanceExtDto::getUserId));

        for (Integer id : ids) {
            List<WorkbenchBussinessChanceExtDto> lasWeekChanceExtDtoList = lastWeekChanceMap.get(id);
            List<WorkbenchBussinessChanceExtDto> thisWeekChanceExtDtoList = ThisWeekChanceMap.get(id);

            ExpectThisWeekOrderSummaryDto expectThisWeekOrderSummaryDto = new ExpectThisWeekOrderSummaryDto();
            DwhErpUserDto userDeptInfo = dwhThreadLocalService.getUserInfo(id);
            expectThisWeekOrderSummaryDto.setId(id);
            expectThisWeekOrderSummaryDto.setName(userDeptInfo.getUsername());
            long laskWeekTotalNum = 0;
            long laskWeekFailNum = 0;
            BigDecimal hitRate = BigDecimal.ZERO;
            if (!CollectionUtils.isEmpty(lasWeekChanceExtDtoList)) {
                laskWeekTotalNum = lasWeekChanceExtDtoList.stream().count();
                laskWeekFailNum = lasWeekChanceExtDtoList.stream().filter(x -> x.getStatus() != 7).count();
            }

            if (laskWeekTotalNum != 0) {
                double rate = (laskWeekTotalNum - laskWeekFailNum) / (double) laskWeekTotalNum *100;
                hitRate = new BigDecimal(rate).setScale(2, BigDecimal.ROUND_HALF_UP);
            } else {
                hitRate = new BigDecimal(0);
            }
            //设置上周相关属性int
            expectThisWeekOrderSummaryDto.setLastWeekSuccessNum((int) laskWeekTotalNum);
            expectThisWeekOrderSummaryDto.setLastWeekFailNum((int) laskWeekFailNum);
            expectThisWeekOrderSummaryDto.setHitRate(hitRate);

            //更新总和值
            allLaskWeekTotalNum += (int) laskWeekTotalNum;
            allLaskWeekfailNum += (int) laskWeekFailNum;


            long thisWeekTotalNum = 0;
            BigDecimal thisWeekTotalAmount = new BigDecimal(0);
            if (!CollectionUtils.isEmpty(thisWeekChanceExtDtoList)) {
                thisWeekTotalNum = thisWeekChanceExtDtoList.stream().count();
                for (WorkbenchBussinessChanceExtDto workbenchBussinessChanceExtDto : thisWeekChanceExtDtoList) {
                    thisWeekTotalAmount = thisWeekTotalAmount.add(workbenchBussinessChanceExtDto.getBussinessAmount());
                }
            }
            expectThisWeekOrderSummaryDto.setThisWeekSuccessNum((int) thisWeekTotalNum);
            expectThisWeekOrderSummaryDto.setThisWeekAmount(thisWeekTotalAmount);

            //更新总和值
            allThisWeekTotalNum += (int) thisWeekTotalNum;
            allThisWeekTotalAmount = allThisWeekTotalAmount.add(thisWeekTotalAmount);

            //添加至list中
            expectThisWeekOrderSummaryDtoList.add(expectThisWeekOrderSummaryDto);

        }
        //排序
        if (!CollectionUtils.isEmpty(expectThisWeekOrderSummaryDtoList)) {
            expectThisWeekOrderSummaryDtoList = expectThisWeekOrderSummaryDtoList.stream()
                    .sorted(Comparator.comparing(o -> o.getThisWeekSuccessNum(), Comparator.nullsFirst(Integer::compareTo).reversed())
                    ).collect(Collectors.toList());
        }


        //设置总和数据
        ExpectThisWeekOrderSummaryDto expectThisWeekOrderSummaryDto = new ExpectThisWeekOrderSummaryDto();
        expectThisWeekOrderSummaryDto.setName("总计");
        expectThisWeekOrderSummaryDto.setLastWeekSuccessNum(allLaskWeekTotalNum);
        expectThisWeekOrderSummaryDto.setLastWeekFailNum(allLaskWeekfailNum);
        expectThisWeekOrderSummaryDto.setThisWeekSuccessNum(allThisWeekTotalNum);
        expectThisWeekOrderSummaryDto.setThisWeekAmount(allThisWeekTotalAmount);

        if (allLaskWeekTotalNum != 0) {
            allHitRate = new
                    BigDecimal((allLaskWeekTotalNum - allLaskWeekfailNum) / (double) allLaskWeekTotalNum *100).setScale(2, BigDecimal.ROUND_HALF_UP);
        }

        expectThisWeekOrderSummaryDto.setHitRate(allHitRate);
        expectThisWeekOrderSummaryDtoList.add(expectThisWeekOrderSummaryDto);
        return expectThisWeekOrderSummaryDtoList;
    }

    @Override
    public List<WorkbenchBussinessChanceExtDto> getThisWeekNewAddImportChanceListByIds(WorkbenchDataQueryDto

                                                                                               dataQueryDto) {
        List<WorkbenchBussinessChanceExtDto> thisWeekNewAddImportChanceList = new ArrayList<>();


        Long thisWeekStartTime = WorkbenchUtil.getThisWeekStart().getTime();
        Long thisWeekEndTIME = WorkbenchUtil.getThisWeekEnd().getTime();
        dataQueryDto.setStartTime(thisWeekStartTime);
        dataQueryDto.setEndTime(thisWeekEndTIME);

        //主管级别获取下属销售列表
        Map<DwhErpSubDeptDto, List<DwhErpSubDeptDto>> subUserOrDept = this.getSubUserOrDept(dataQueryDto);


        List<DwhErpSubDeptDto> deptDtos = subUserOrDept.entrySet().stream()
                .flatMap(e -> e.getValue().stream()).collect(Collectors.toList());

        List<DwhErpUserDto> users = deptDtos.stream()
                .flatMap(s -> s.getSubUsers().stream()).collect(Collectors.toList());

        List<Integer> ids = users.stream().map(DwmNjUserOrgDfDo::getUserId).collect(Collectors.toList());

        //非空校验
        if (CollectionUtils.isEmpty(ids)) {
            return thisWeekNewAddImportChanceList;
        }

        dataQueryDto.setUserIds(ids);
        List<WorkbenchBussinessChanceExtDto> chanceExtDtoList = bussinessChanceMapper.getThisWeekNewAddImportChanceListByIds(dataQueryDto);

        if (!CollectionUtils.isEmpty(chanceExtDtoList)) {
            //设置商机归属销售名字
            chanceExtDtoList.stream().forEach(x -> {

                DwhErpUserDto userDeptInfo = dwhThreadLocalService.getUserInfo(x.getUserId());
                x.setUserName(userDeptInfo.getUsername());
            });
            thisWeekNewAddImportChanceList.addAll(chanceExtDtoList);
            //按金额大->小排序
            thisWeekNewAddImportChanceList = thisWeekNewAddImportChanceList.stream().sorted((x, y) -> {
                if (x.getBussinessAmount().compareTo(y.getBussinessAmount()) >= 0) {
                    return -1;
                } else {
                    return 1;
                }
            }).collect(Collectors.toList());
        }
        return thisWeekNewAddImportChanceList;
    }

    @Override
    public List<WorkbenchBussinessChanceExtDto> getTwoDayUncontactChanceListByIds(WorkbenchDataQueryDto
                                                                                          dataQueryDto) {


        List<WorkbenchBussinessChanceExtDto> getTwoDayUncontactChanceList = new ArrayList<>();


        Date workDateBefore2DaysForDate = null;
        try {
            workDateBefore2DaysForDate = WorkbenchUtil.getWorkDateBefore(new Date(), 2);
        } catch (ParseException e) {
            log.error("【getTwoDayUncontactChanceListByIds】处理异常",e);
        }
        Long startTime = DateUtil.getDayStartTime(workDateBefore2DaysForDate).getTime();
        Long endTime = DateUtil.getDayEndTime(workDateBefore2DaysForDate).getTime();
        dataQueryDto.setTwoDaysBeforeDate(workDateBefore2DaysForDate);
        dataQueryDto.setStartTime(startTime);
        dataQueryDto.setEndTime(endTime);

        //主管级别获取下属销售列表
        Map<DwhErpSubDeptDto, List<DwhErpSubDeptDto>> subUserOrDept = this.getSubUserOrDept(dataQueryDto);

        List<DwhErpSubDeptDto> deptDtos = subUserOrDept.entrySet().stream()
                .flatMap(e -> e.getValue().stream()).collect(Collectors.toList());

        List<DwhErpUserDto> users = deptDtos.stream()
                .flatMap(s -> s.getSubUsers().stream()).collect(Collectors.toList());

        List<Integer> ids = users.stream().map(DwmNjUserOrgDfDo::getUserId).collect(Collectors.toList());

        //非空校验
        if (CollectionUtils.isEmpty(ids)) {
            return getTwoDayUncontactChanceList;
        }


        dataQueryDto.setUserIds(ids);


        //获得初步未通过通话记录过滤的所有销售商机列表
        List<WorkbenchBussinessChanceExtDto> twoDayUncontactChanceList = bussinessChanceMapper.getTwoDayUncontactChanceListByIds(dataQueryDto);

        if (!CollectionUtils.isEmpty(twoDayUncontactChanceList)) {

            twoDayUncontactChanceList.stream().forEach(chanceExtDto -> {

                DwhErpUserDto userDeptInfo = dwhThreadLocalService.getUserInfo(chanceExtDto.getUserId());
                chanceExtDto.setUserName(userDeptInfo.getUsername());


                //商机列表的附属通话记录排序，获得最近一次的童话记录
                List<BussinessCommunicateRecordDto> BussinessCommunicateRecordDtoList = chanceExtDto.getBussinessCommunicateRecordDtoList();
                if (!CollectionUtils.isEmpty(BussinessCommunicateRecordDtoList)) {
                    List<BussinessCommunicateRecordDto> collect = BussinessCommunicateRecordDtoList.stream().sorted((x, y) -> {
                        if (x.getEndTime() >= y.getEndTime()) {
                            return -1;
                        } else {
                            return 1;
                        }
                    }).collect(Collectors.toList());
                    chanceExtDto.setLastCommunicateRecord(collect.get(0));
                }
            });
            //过滤没有通话记录和通话记录在2天谴的
            twoDayUncontactChanceList = twoDayUncontactChanceList.stream().filter(x -> {
                if (CollectionUtils.isEmpty(x.getBussinessCommunicateRecordDtoList()) || x.getLastCommunicateRecord().getEndTime() < startTime) {
                    return true;
                }
                return false;
            }).collect(Collectors.toList());
            getTwoDayUncontactChanceList.addAll(twoDayUncontactChanceList);

            //转stream流
//                getTwoDayUncontactChanceList = getTwoDayUncontactChanceList.stream().sorted((x, y) -> {
//                            if (x.getBussinessAmount().compareTo(y.getBussinessAmount()) > 0) {
//                                return -1;
//                            } else {
//                                return 1;
//                            }
//                        }
//                ).collect(Collectors.toList());


            if (!CollectionUtils.isEmpty(getTwoDayUncontactChanceList)) {
                getTwoDayUncontactChanceList = getTwoDayUncontactChanceList.stream()
                        .sorted(Comparator.comparing(WorkbenchBussinessChanceExtDto::getBussinessAmount, Comparator.reverseOrder())
                        ).collect(Collectors.toList());
                getTwoDayUncontactChanceList.stream().forEach(x -> {
                    long addTime = x.getAddTime();
                    Date addDate = new Date(addTime);
                    x.setAddDate(addDate);
                    if (x.getLastCommunicateRecord() != null) {
                        long endCommuicatieTime = x.getLastCommunicateRecord().getEndTime();
                        Date endCommuicatieDate = new Date(endCommuicatieTime);
                        x.getLastCommunicateRecord().setEndDate(endCommuicatieDate);
                    }
                });
            }
        }
        //设置时间参数


        return getTwoDayUncontactChanceList;

    }

    @Override
    public List<WorkbenchBussinessChanceExtDto> getThisWeekCoreGoodfailChanceListByIds(WorkbenchDataQueryDto
                                                                                               dataQueryDto) {
        List<WorkbenchBussinessChanceExtDto> chanceExtDtoList = new ArrayList<>();

        Long thisWeekStartTime = WorkbenchUtil.getThisWeekStart().getTime();
        Long thisWeekEndTime = WorkbenchUtil.getThisWeekEnd().getTime();
        dataQueryDto.setStartTime(thisWeekStartTime);
        dataQueryDto.setEndTime(thisWeekEndTime);


        //主管级别获取下属销售列表
        Map<DwhErpSubDeptDto, List<DwhErpSubDeptDto>> subUserOrDept = this.getSubUserOrDept(dataQueryDto);

        //获取下属部门
        List<DwhErpSubDeptDto> deptDtos = subUserOrDept.entrySet().stream()
                .flatMap(e -> e.getValue().stream()).collect(Collectors.toList());
        //获得所有部门对应的销售
        List<DwhErpUserDto> users = deptDtos.stream()
                .flatMap(s -> s.getSubUsers().stream()).collect(Collectors.toList());

        //获取所有销售的ids
        List<Integer> ids = users.stream().map(DwmNjUserOrgDfDo::getUserId).collect(Collectors.toList());

        //非空校验
        if (CollectionUtils.isEmpty(ids)) {
            return chanceExtDtoList;
        }

        dataQueryDto.setUserIds(ids);

        chanceExtDtoList = bussinessChanceMapper.getThisWeekCoreFailChanceListByIds(dataQueryDto);

        if (!CollectionUtils.isEmpty(chanceExtDtoList)) {
            chanceExtDtoList= chanceExtDtoList.stream().sorted(Comparator.comparing(WorkbenchBussinessChanceDo::getModTime, Comparator.nullsFirst(Long::compareTo).reversed())).collect(Collectors.toList());
            chanceExtDtoList.forEach(chanceExtDto -> {
                DwhErpUserDto userDeptInfo = dwhThreadLocalService.getUserInfo(chanceExtDto.getUserId());
                chanceExtDto.setUserName(userDeptInfo.getUsername());
                Long modTime = chanceExtDto.getModTime();
                Date closeDate = new Date(modTime);
                chanceExtDto.setCloseDate(closeDate);
            });
        }
        return chanceExtDtoList;
    }


    @Override
    public List<WorkbenchBussinessChanceExtDto> getThisWeekExpectFailChanceListByIds(WorkbenchDataQueryDto
                                                                                             dataQueryDto) {
        List<WorkbenchBussinessChanceExtDto> chanceExtDtoList = new ArrayList<>();

        Long thisWeekStartTime = WorkbenchUtil.getThisWeekStart().getTime();
        Long todayStartTime = DateUtil.getDayStartTime(new Date()).getTime();
        dataQueryDto.setStartTime(thisWeekStartTime);
        dataQueryDto.setEndTime(todayStartTime);

        //主管级别获取下属销售列表
        Map<DwhErpSubDeptDto, List<DwhErpSubDeptDto>> subUserOrDept = this.getSubUserOrDept(dataQueryDto);

        //获取下属部门
        List<DwhErpSubDeptDto> deptDtos = subUserOrDept.entrySet().stream()
                .flatMap(e -> e.getValue().stream()).collect(Collectors.toList());
        //获得所有部门对应的销售
        List<DwhErpUserDto> users = deptDtos.stream()
                .flatMap(s -> s.getSubUsers().stream()).collect(Collectors.toList());

        //获取所有销售的ids
        List<Integer> ids = users.stream().map(DwmNjUserOrgDfDo::getUserId).collect(Collectors.toList());

        //非空校验
        if (CollectionUtils.isEmpty(ids)) {
            return chanceExtDtoList;
        }


        chanceExtDtoList = bussinessChanceMapper.getThisWeekExpectFailChanceListByIds(dataQueryDto);


        chanceExtDtoList = chanceExtDtoList.stream().sorted(Comparator.comparing(WorkbenchBussinessChanceDo::getOrderTime, Comparator.nullsFirst(Long::compareTo))).collect(Collectors.toList());
        chanceExtDtoList.forEach(chanceExtDto -> {
                    if (chanceExtDto != null) {
                        DwhErpUserDto userDeptInfo = dwhThreadLocalService.getUserInfo(chanceExtDto.getUserId());
                        chanceExtDto.setUserName(userDeptInfo.getUsername());
                        Long orderTime = chanceExtDto.getOrderTime();
                        Date expectOrderTime = new Date(orderTime);
                        chanceExtDto.setExpectOrderTime(expectOrderTime);
                    }
                });
//        chanceExtDtoList.stream().sorted((x, y) -> (x.getModTime() >= y.getModTime()) ? -1 : 1).forEach(chanceExtDto -> {
//            if(chanceExtDto !=null) {
//                DwhErpUserDto userDeptInfo = dwhThreadLocalService.getUserInfo(chanceExtDto.getUserId());
//                chanceExtDto.setUserName(userDeptInfo.getUsername());
//                Long orderTime = chanceExtDto.getOrderTime();
//                Date expectOrderTime = new Date(orderTime);
//                chanceExtDto.setExpectOrderTime(expectOrderTime);
//            }
//        });
//
        return chanceExtDtoList;
    }

    @Override
    public List<ExpectThisWeekOrderSummaryDto> getDeptExpectThisWeekOrderSummaryDtoListByIds(WorkbenchDataQueryDto dataQueryDto) {

        List<ExpectThisWeekOrderSummaryDto> expectThisWeekOrderSummaryDtoList = new ArrayList<>();

        //总计数值
        Integer allLaskWeekTotalNum = 0;
        Integer allLaskWeekfailNum = 0;
        Integer allThisWeekTotalNum = 0;
        BigDecimal allHitRate = new BigDecimal(0);
        BigDecimal allThisWeekTotalAmount = new BigDecimal(0);

        //时间参数
        Long preWeekStartTime = WorkbenchUtil.getPreWeekStart().getTime();
        Long preWeekEndTime = WorkbenchUtil.getPreWeekEnd().getTime();
        Long thisWeekStartTime = WorkbenchUtil.getThisWeekStart().getTime();
        Long thisWeekEndTIME = WorkbenchUtil.getThisWeekEnd().getTime();


        Map<DwhErpSubDeptDto, List<DwhErpSubDeptDto>> subUserOrDept = getSubUserOrDept(dataQueryDto);
        if (DwhConstants.XSZJ.equals(dataQueryDto.getPositionName())) {
            for (Map.Entry<DwhErpSubDeptDto, List<DwhErpSubDeptDto>> dwhErpSubDeptDtoListEntry : subUserOrDept.entrySet()) {
                ExpectThisWeekOrderSummaryDto expectThisWeekOrderSummaryDto = new NullExpectThisWeekOrderSummaryDto();
                //设置展示名称
                expectThisWeekOrderSummaryDto.setName(dwhErpSubDeptDtoListEntry.getKey().getDepartName());
                expectThisWeekOrderSummaryDto.setId(dwhErpSubDeptDtoListEntry.getKey().getDepartId());
                List<Integer> ids = dwhErpSubDeptDtoListEntry.getValue().stream().flatMap(x -> x.getSubUsers().stream())
                        .map(DwmNjUserOrgDfDo::getUserId).collect(Collectors.toList());

                //非空校验
                if (CollectionUtils.isEmpty(ids)) {

                    expectThisWeekOrderSummaryDtoList.add(expectThisWeekOrderSummaryDto);

                    continue;
                }

                dataQueryDto.setUserIds(ids);
                dataQueryDto.setStartTime(preWeekStartTime);
                dataQueryDto.setEndTime(preWeekEndTime);
                //查询上周
                List<WorkbenchBussinessChanceExtDto> lasWeekChanceExtDtoList = bussinessChanceMapper.getExpectOrderBussinessChanceListByIds(dataQueryDto);

                //查询这周
                dataQueryDto.setStartTime(thisWeekStartTime);
                dataQueryDto.setEndTime(thisWeekEndTIME);
                List<WorkbenchBussinessChanceExtDto> thisWeekChanceExtDtoList = bussinessChanceMapper.getExpectOrderBussinessChanceListByIds(dataQueryDto);
                if (!CollectionUtils.isEmpty(lasWeekChanceExtDtoList)) {
                    long laskWeekTotalNum = lasWeekChanceExtDtoList.stream().count();
                    long laskWeekFailNum = lasWeekChanceExtDtoList.stream().filter(x -> x.getStatus() != 7).count();
                    BigDecimal hitRate = null;

                    if (laskWeekTotalNum != 0) {
                        double rate = (laskWeekTotalNum - laskWeekFailNum) / (double) laskWeekTotalNum *100;
                        hitRate = new BigDecimal(rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                    } else {
                        hitRate = new BigDecimal(0);
                    }
//                设置上周相关属性int
                    expectThisWeekOrderSummaryDto.setLastWeekSuccessNum((int) laskWeekTotalNum);
                    expectThisWeekOrderSummaryDto.setLastWeekFailNum((int) laskWeekFailNum);
                    expectThisWeekOrderSummaryDto.setHitRate(hitRate);

                    //更新总和值
                    allLaskWeekTotalNum += (int) laskWeekTotalNum;
                    allLaskWeekfailNum += (int) laskWeekFailNum;

                }
                if (!CollectionUtils.isEmpty(thisWeekChanceExtDtoList)) {
                    long thisWeekTotalNum = thisWeekChanceExtDtoList.stream().count();
                    BigDecimal thisWeekTotalAmount = new BigDecimal(0);

                    for (WorkbenchBussinessChanceExtDto workbenchBussinessChanceExtDto : thisWeekChanceExtDtoList) {
                        thisWeekTotalAmount = thisWeekTotalAmount.add(workbenchBussinessChanceExtDto.getBussinessAmount());
                    }


                    expectThisWeekOrderSummaryDto.setThisWeekSuccessNum((int) thisWeekTotalNum);
                    expectThisWeekOrderSummaryDto.setThisWeekAmount(thisWeekTotalAmount);

                    //更新总和值
                    allThisWeekTotalNum += (int) thisWeekTotalNum;
                    allThisWeekTotalAmount = allThisWeekTotalAmount.add(thisWeekTotalAmount);
                }

                //添加至list中
                expectThisWeekOrderSummaryDtoList.add(expectThisWeekOrderSummaryDto);
            }
        } else {

            List<DwhErpSubDeptDto> subDeptDtoList = subUserOrDept.entrySet().stream().flatMap(x -> x.getValue().stream()).collect(Collectors.toList());
            for (DwhErpSubDeptDto dwhErpSubDeptDto : subDeptDtoList) {

                ExpectThisWeekOrderSummaryDto expectThisWeekOrderSummaryDto = new NullExpectThisWeekOrderSummaryDto();
                //设置展示名称
                expectThisWeekOrderSummaryDto.setName(dwhErpSubDeptDto.getDepartName());
                expectThisWeekOrderSummaryDto.setId(dwhErpSubDeptDto.getDepartId());
                List<Integer> ids = dwhErpSubDeptDto.getSubUsers().stream()
                        .map(DwmNjUserOrgDfDo::getUserId).collect(Collectors.toList());

                //非空校验
                if (CollectionUtils.isEmpty(ids)) {

                    expectThisWeekOrderSummaryDtoList.add(expectThisWeekOrderSummaryDto);

                    continue;
                }

                dataQueryDto.setUserIds(ids);
                dataQueryDto.setStartTime(preWeekStartTime);
                dataQueryDto.setEndTime(preWeekEndTime);
                //查询上周
                List<WorkbenchBussinessChanceExtDto> lasWeekChanceExtDtoList = bussinessChanceMapper.getExpectOrderBussinessChanceListByIds(dataQueryDto);

                //查询这周
                dataQueryDto.setStartTime(thisWeekStartTime);
                dataQueryDto.setEndTime(thisWeekEndTIME);
                List<WorkbenchBussinessChanceExtDto> thisWeekChanceExtDtoList = bussinessChanceMapper.getExpectOrderBussinessChanceListByIds(dataQueryDto);
                if (!CollectionUtils.isEmpty(lasWeekChanceExtDtoList)) {
                    long laskWeekTotalNum = lasWeekChanceExtDtoList.stream().count();
                    long laskWeekFailNum = lasWeekChanceExtDtoList.stream().filter(x -> x.getStatus() != 7).count();
                    BigDecimal hitRate = new BigDecimal(0);

                    if (laskWeekTotalNum != 0) {
                        double rate = (laskWeekTotalNum - laskWeekFailNum) / (double) laskWeekTotalNum *100;
                        hitRate = new BigDecimal(rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                    } else {
                        hitRate = new BigDecimal(0);
                    }
//                设置上周相关属性int
                    expectThisWeekOrderSummaryDto.setLastWeekSuccessNum((int) laskWeekTotalNum);
                    expectThisWeekOrderSummaryDto.setLastWeekFailNum((int) laskWeekFailNum);
                    expectThisWeekOrderSummaryDto.setHitRate(hitRate);

                    //更新总和值
                    allLaskWeekTotalNum += (int) laskWeekTotalNum;
                    allLaskWeekfailNum += (int) laskWeekFailNum;

                }
                if (!CollectionUtils.isEmpty(thisWeekChanceExtDtoList)) {
                    long thisWeekTotalNum = thisWeekChanceExtDtoList.stream().count();
                    BigDecimal thisWeekTotalAmount = new BigDecimal(0);

                    for (WorkbenchBussinessChanceExtDto workbenchBussinessChanceExtDto : thisWeekChanceExtDtoList) {
                        thisWeekTotalAmount = thisWeekTotalAmount.add(workbenchBussinessChanceExtDto.getBussinessAmount());
                    }


                    expectThisWeekOrderSummaryDto.setThisWeekSuccessNum((int) thisWeekTotalNum);
                    expectThisWeekOrderSummaryDto.setThisWeekAmount(thisWeekTotalAmount);

                    //更新总和值
                    allThisWeekTotalNum += (int) thisWeekTotalNum;
                    allThisWeekTotalAmount = allThisWeekTotalAmount.add(thisWeekTotalAmount);
                }

                //添加至list中
                expectThisWeekOrderSummaryDtoList.add(expectThisWeekOrderSummaryDto);
            }
        }
        if (!CollectionUtils.isEmpty(expectThisWeekOrderSummaryDtoList)) {
            expectThisWeekOrderSummaryDtoList = expectThisWeekOrderSummaryDtoList.stream()
                    .sorted(Comparator.comparing(ExpectThisWeekOrderSummaryDto::getThisWeekSuccessNum, Comparator.reverseOrder()))
                    .collect(Collectors.toList());
        }
        //设置总和数据
        ExpectThisWeekOrderSummaryDto expectThisWeekOrderSummaryDto = new ExpectThisWeekOrderSummaryDto();
        expectThisWeekOrderSummaryDto.setName("总计");
        expectThisWeekOrderSummaryDto.setLastWeekSuccessNum(allLaskWeekTotalNum);
        expectThisWeekOrderSummaryDto.setLastWeekFailNum(allLaskWeekfailNum);
        expectThisWeekOrderSummaryDto.setThisWeekSuccessNum(allThisWeekTotalNum);
        expectThisWeekOrderSummaryDto.setThisWeekAmount(allThisWeekTotalAmount);

        if (allLaskWeekTotalNum != 0) {
            allHitRate = new
                    BigDecimal((allLaskWeekTotalNum - allLaskWeekfailNum) / (double) allLaskWeekTotalNum * 100)
                    .setScale(2, BigDecimal.ROUND_HALF_UP);
        }

        expectThisWeekOrderSummaryDto.setHitRate(allHitRate);
        expectThisWeekOrderSummaryDtoList.add(expectThisWeekOrderSummaryDto);
        return expectThisWeekOrderSummaryDtoList;

    }

    @Override
    public List<WarningBussinessChanceSummaryDto> getWarningBussinessChanceSummaryDtoList(WorkbenchDataQueryDto dataQueryDto) {

        List<WarningBussinessChanceSummaryDto> warningBussinessChanceSummaryDtoList = new ArrayList<>();

        Map<DwhErpSubDeptDto, List<DwhErpSubDeptDto>> subUserOrDept = getSubUserOrDept(dataQueryDto);

        if (DwhConstants.XSZJ.equals(dataQueryDto.getPositionName())) {
            //总监获取
            for (Map.Entry<DwhErpSubDeptDto, List<DwhErpSubDeptDto>> subDeptDtoListEntry : subUserOrDept.entrySet()) {

                WarningBussinessChanceSummaryDto chanceSummaryDto = new NullWarningBussinessChanceSummaryDto();

                chanceSummaryDto.setName(subDeptDtoListEntry.getKey().getDepartName());
                List<Integer> ids = subDeptDtoListEntry.getValue().stream().flatMap(x -> x.getSubUsers().stream())
                        .map(DwmNjUserOrgDfDo::getUserId).collect(Collectors.toList());

                //非空校验
                if (CollectionUtils.isEmpty(ids)) {

                    warningBussinessChanceSummaryDtoList.add(chanceSummaryDto);

                    continue;
                }
                dataQueryDto.setUserIds(ids);


                //本周新增重大商机
                Long thisWeekStartTime = WorkbenchUtil.getThisWeekStart().getTime();
                Long thisWeekEndTIME = WorkbenchUtil.getThisWeekEnd().getTime();
                dataQueryDto.setStartTime(thisWeekStartTime);
                dataQueryDto.setEndTime(thisWeekEndTIME);
                List<WorkbenchBussinessChanceExtDto> thisWeekNewAddImportChanceList = bussinessChanceMapper.getThisWeekNewAddImportChanceListByIds(dataQueryDto);

                if (!CollectionUtils.isEmpty(thisWeekNewAddImportChanceList)) {
                    long ThisWeekNewBigProjectNum = thisWeekNewAddImportChanceList.stream().count();
                    chanceSummaryDto.setThisWeekNewBigProjectNum((int) ThisWeekNewBigProjectNum);
                } else {
                    chanceSummaryDto.setThisWeekNewBigProjectNum(0);
                }


                //2日未联系商机
                Date workDateBefore2DaysForDate = null;
                try {
                    workDateBefore2DaysForDate = WorkbenchUtil.getWorkDateBefore(new Date(), 2);
                } catch (ParseException e) {
                    log.info("工作日时间获取失败");
                }
                Long startTime = DateUtil.getDayStartTime(workDateBefore2DaysForDate).getTime();
                Long endTime = DateUtil.getDayEndTime(workDateBefore2DaysForDate).getTime();
                dataQueryDto.setTwoDaysBeforeDate(workDateBefore2DaysForDate);
                dataQueryDto.setStartTime(startTime);
                dataQueryDto.setEndTime(endTime);
                //获得初步未通过通话记录过滤的所有销售商机列表
                List<WorkbenchBussinessChanceExtDto> twoDayUncontactChanceList = bussinessChanceMapper.getTwoDayUncontactChanceListByIds(dataQueryDto);
                if (!CollectionUtils.isEmpty(twoDayUncontactChanceList)) {
                    twoDayUncontactChanceList.stream().forEach(chanceExtDto -> {
                        //商机列表的附属通话记录排序，获得最近一次的童话记录
                        List<BussinessCommunicateRecordDto> BussinessCommunicateRecordDtoList = chanceExtDto.getBussinessCommunicateRecordDtoList();
                        if (!CollectionUtils.isEmpty(BussinessCommunicateRecordDtoList)) {
                            List<BussinessCommunicateRecordDto> collect = BussinessCommunicateRecordDtoList.stream().sorted((x, y) -> {
                                if (x.getEndTime() >= y.getEndTime()) {
                                    return -1;
                                } else {
                                    return 1;
                                }
                            }).collect(Collectors.toList());
                            chanceExtDto.setLastCommunicateRecord(collect.get(0));
                        }
                    });
                    //获得没有通话记录和通话记录在2天前的商机
                    twoDayUncontactChanceList = twoDayUncontactChanceList.stream().filter(x -> {
                        if (CollectionUtils.isEmpty(x.getBussinessCommunicateRecordDtoList()) || x.getLastCommunicateRecord().getEndTime() < startTime) {
                            return true;
                        }
                        return false;
                    }).collect(Collectors.toList());
                }
                if (!CollectionUtils.isEmpty(twoDayUncontactChanceList)) {
                    long twoDayUncontactNum = twoDayUncontactChanceList.stream().count();
                    chanceSummaryDto.setUnCommunicateNum((int) twoDayUncontactNum);
                } else {
                    chanceSummaryDto.setUnCommunicateNum(0);
                }


                //本周核心商品商机丢单

                dataQueryDto.setStartTime(thisWeekStartTime);
                dataQueryDto.setEndTime(thisWeekEndTIME);


                List<WorkbenchBussinessChanceExtDto> thisWeekCoreFailchanceExtDtoList = bussinessChanceMapper.getThisWeekCoreFailChanceListByIds(dataQueryDto);
                if (!CollectionUtils.isEmpty(thisWeekCoreFailchanceExtDtoList)) {
                    long thisWeekCoreFailNum = thisWeekCoreFailchanceExtDtoList.stream().count();
                    chanceSummaryDto.setThisWeekLostCoreNum((int) thisWeekCoreFailNum);
                } else {
                    chanceSummaryDto.setThisWeekArrearsNum(0);
                }


                //本周预计商机到款未到
                Long todayStartTime = DateUtil.getDayStartTime(new Date()).getTime();
                dataQueryDto.setStartTime(thisWeekStartTime);
                dataQueryDto.setEndTime(todayStartTime);

                List<WorkbenchBussinessChanceExtDto> thisWeekExpectFailChanceExtDtoList = bussinessChanceMapper.getThisWeekExpectFailChanceListByIds(dataQueryDto);
                if (!CollectionUtils.isEmpty(thisWeekExpectFailChanceExtDtoList)) {
                    long thisWeekExpectFailNum = thisWeekExpectFailChanceExtDtoList.stream().count();
                    chanceSummaryDto.setThisWeekArrearsNum((int) thisWeekExpectFailNum);
                } else {
                    chanceSummaryDto.setThisWeekArrearsNum(0);
                }

                warningBussinessChanceSummaryDtoList.add(chanceSummaryDto);
            }
        } else {
            List<DwhErpSubDeptDto> subDeptDtoList = subUserOrDept.entrySet().stream().flatMap(x -> x.getValue().stream()).collect(Collectors.toList());
            for (DwhErpSubDeptDto dwhErpSubDeptDto : subDeptDtoList) {


                WarningBussinessChanceSummaryDto chanceSummaryDto = new NullWarningBussinessChanceSummaryDto();

                chanceSummaryDto.setName(dwhErpSubDeptDto.getDepartName());
                List<Integer> ids = dwhErpSubDeptDto.getSubUsers().stream()
                        .map(DwmNjUserOrgDfDo::getUserId).collect(Collectors.toList());

                //非空校验
                if (CollectionUtils.isEmpty(ids)) {

                    warningBussinessChanceSummaryDtoList.add(chanceSummaryDto);

                    continue;
                }
                dataQueryDto.setUserIds(ids);

                //本周新增重大商机
                Long thisWeekStartTime = WorkbenchUtil.getThisWeekStart().getTime();
                Long thisWeekEndTIME = WorkbenchUtil.getThisWeekEnd().getTime();
                dataQueryDto.setStartTime(thisWeekStartTime);
                dataQueryDto.setEndTime(thisWeekEndTIME);
                List<WorkbenchBussinessChanceExtDto> thisWeekNewAddImportChanceList = bussinessChanceMapper.getThisWeekNewAddImportChanceListByIds(dataQueryDto);

                if (!CollectionUtils.isEmpty(thisWeekNewAddImportChanceList)) {
                    long ThisWeekNewBigProjectNum = thisWeekNewAddImportChanceList.stream().count();
                    chanceSummaryDto.setThisWeekNewBigProjectNum((int) ThisWeekNewBigProjectNum);
                } else {
                    chanceSummaryDto.setThisWeekNewBigProjectNum(0);
                }

                //2日未联系商机
                Date workDateBefore2DaysForDate = null;
                try {
                    workDateBefore2DaysForDate = WorkbenchUtil.getWorkDateBefore(new Date(), 2);
                } catch (ParseException e) {
                    log.info("工作日时间获取失败");
                }
                Long startTime = DateUtil.getDayStartTime(workDateBefore2DaysForDate).getTime();
                Long endTime = DateUtil.getDayEndTime(workDateBefore2DaysForDate).getTime();
                dataQueryDto.setTwoDaysBeforeDate(workDateBefore2DaysForDate);
                dataQueryDto.setStartTime(startTime);
                dataQueryDto.setEndTime(endTime);
                //获得初步未通过通话记录过滤的所有销售商机列表
                List<WorkbenchBussinessChanceExtDto> twoDayUncontactChanceList = bussinessChanceMapper.getTwoDayUncontactChanceListByIds(dataQueryDto);

                if (!CollectionUtils.isEmpty(twoDayUncontactChanceList)) {
                    twoDayUncontactChanceList.stream().forEach(chanceExtDto -> {
                        //商机列表的附属通话记录排序，获得最近一次的童话记录
                        List<BussinessCommunicateRecordDto> BussinessCommunicateRecordDtoList = chanceExtDto.getBussinessCommunicateRecordDtoList();
                        if (!CollectionUtils.isEmpty(BussinessCommunicateRecordDtoList)) {
                            List<BussinessCommunicateRecordDto> collect = BussinessCommunicateRecordDtoList.stream().sorted((x, y) -> {
                                if (x.getEndTime() >= y.getEndTime()) {
                                    return -1;
                                } else {
                                    return 1;
                                }
                            }).collect(Collectors.toList());
                            chanceExtDto.setLastCommunicateRecord(collect.get(0));
                        }
                    });
                    //获得没有通话记录和通话记录在2天前的商机
                    twoDayUncontactChanceList = twoDayUncontactChanceList.stream().filter(x -> {
                        if (CollectionUtils.isEmpty(x.getBussinessCommunicateRecordDtoList()) || x.getLastCommunicateRecord().getEndTime() < startTime) {
                            return true;
                        }
                        return false;
                    }).collect(Collectors.toList());
                }
                if (!CollectionUtils.isEmpty(twoDayUncontactChanceList)) {
                    long twoDayUncontactNum = twoDayUncontactChanceList.stream().count();
                    chanceSummaryDto.setUnCommunicateNum((int) twoDayUncontactNum);
                } else {
                    chanceSummaryDto.setUnCommunicateNum(0);
                }


                //本周核心商品商机丢单

                dataQueryDto.setStartTime(thisWeekStartTime);
                dataQueryDto.setEndTime(thisWeekEndTIME);


                List<WorkbenchBussinessChanceExtDto> thisWeekCoreFailchanceExtDtoList = bussinessChanceMapper.getThisWeekCoreFailChanceListByIds(dataQueryDto);
                if (!CollectionUtils.isEmpty(thisWeekCoreFailchanceExtDtoList)) {
                    long thisWeekCoreFailNum = thisWeekCoreFailchanceExtDtoList.stream().count();
                    chanceSummaryDto.setThisWeekLostCoreNum((int) thisWeekCoreFailNum);
                } else {
                    chanceSummaryDto.setThisWeekLostCoreNum(0);
                }


                //本周预计商机到款未到
                Long todayStartTime = DateUtil.getDayStartTime(new Date()).getTime();
                dataQueryDto.setStartTime(thisWeekStartTime);
                dataQueryDto.setEndTime(todayStartTime);

                List<WorkbenchBussinessChanceExtDto> thisWeekExpectFailChanceExtDtoList = bussinessChanceMapper.getThisWeekExpectFailChanceListByIds(dataQueryDto);

                if (!CollectionUtils.isEmpty(thisWeekExpectFailChanceExtDtoList)) {
                    long thisWeekExpectFailNum = thisWeekExpectFailChanceExtDtoList.stream().count();
                    chanceSummaryDto.setThisWeekArrearsNum((int) thisWeekExpectFailNum);
                } else {
                    chanceSummaryDto.setThisWeekArrearsNum(0);
                }

                warningBussinessChanceSummaryDtoList.add(chanceSummaryDto);
            }
        }


        return warningBussinessChanceSummaryDtoList;
    }

    @Override
    public List<CommunicateSummaryDto> getCommunicateBussinessChanceByIds(WorkbenchDataQueryDto dataQueryDto) {
        List<CommunicateSummaryDto> communicateSummaryDtoList = new ArrayList<>();

        //设置时间参数
        Date yesterday = DateUtil.getPreviousDayByDateTime(new Date());
        long yesterdayStartTime = DateUtil.getDayStartTime(yesterday).getTime();
        long yesterdayEndTime = DateUtil.getDayEndTime(yesterday).getTime();
        Date today = new Date();

        //主管级别获取下属销售列表
        log.info("主管获取待沟通商机对象入参: {}", JSONObject.toJSONString(dataQueryDto));
        Map<DwhErpSubDeptDto, List<DwhErpSubDeptDto>> subUserOrDept = this.getSubUserOrDept(dataQueryDto);
        log.info("主管获取待沟通商机对象出参: {}", JSONObject.toJSONString(subUserOrDept));

        List<DwhErpSubDeptDto> deptDtos = subUserOrDept.entrySet().stream()
                .flatMap(e -> e.getValue().stream()).collect(Collectors.toList());

        List<DwhErpUserDto> users = deptDtos.stream()
                .flatMap(s -> s.getSubUsers().stream()).collect(Collectors.toList());

        List<Integer> ids = users.stream().map(DwmNjUserOrgDfDo::getUserId).collect(Collectors.toList());

        //非空校验
        if (CollectionUtils.isEmpty(ids)) {

            return communicateSummaryDtoList;
        }

        //获取昨天的计划沟通商机数
        dataQueryDto.setUserIds(ids);
        dataQueryDto.setStartTime(yesterdayStartTime);
        dataQueryDto.setEndTime(yesterdayEndTime);
        dataQueryDto.setNextContactDate(yesterday);
        List<WorkbenchBussinessChanceExtDto> yesterdayPlanCommunicateChanceList = bussinessChanceMapper.getPlanCommunicateChanceListByIds(dataQueryDto);
        //分组
        Map<Integer, List<WorkbenchBussinessChanceExtDto>> yesterdayPlanCommuicateMap = yesterdayPlanCommunicateChanceList.stream().collect(Collectors.groupingBy(WorkbenchBussinessChanceExtDto::getUserId));

        //获取计划外沟通商机数
        //获取下次沟通时间不为昨天的商机list
        List<WorkbenchBussinessChanceExtDto> yesterdayCommunicateChanceList = bussinessChanceMapper.getYesterdayContactChanceListByIds(dataQueryDto);
        Map<Integer, List<WorkbenchBussinessChanceExtDto>> yesterdayPlanOutCommunicateChanceList = yesterdayCommunicateChanceList.stream().filter(x -> {
            BussinessCommunicateRecordDto bussinessCommunicateRecordDto = x.getBussinessCommunicateRecordDtoList().stream().filter(e -> e.getEndTime() >= yesterdayStartTime && e.getEndTime() < yesterdayEndTime).findAny().orElse(null);
            if (bussinessCommunicateRecordDto == null) {
                return false;
            }
            return true;
        }).collect(Collectors.groupingBy(WorkbenchBussinessChanceExtDto::getUserId));


        //获取今日
        dataQueryDto.setNextContactDate(today);
        List<WorkbenchBussinessChanceExtDto> todayPlanCommunicateChanceList = bussinessChanceMapper.getPlanCommunicateChanceListByIds(dataQueryDto);
        //分组
        Map<Integer, List<WorkbenchBussinessChanceExtDto>> todayPlanCommuicateMap = todayPlanCommunicateChanceList.stream().collect(Collectors.groupingBy(WorkbenchBussinessChanceExtDto::getUserId));

        //遍历设置list属性
        for (Integer id : ids) {

            CommunicateSummaryDto summaryDto = new CommunicateSummaryDto();
            List<WorkbenchBussinessChanceExtDto> yesterDayPlanChanceExtDtoList = yesterdayPlanCommuicateMap.get(id);
            List<WorkbenchBussinessChanceExtDto> yesterdayPlanOutChanceExtDtoList = yesterdayPlanOutCommunicateChanceList.get(id);
            List<WorkbenchBussinessChanceExtDto> toDayPlanChanceExtDtoList = todayPlanCommuicateMap.get(id);

            DwhErpUserDto userInfo = dwhThreadLocalService.getUserInfo(id);
            summaryDto.setName(userInfo.getUsername());
            summaryDto.setUserId(userInfo.getUserId());

            //设置昨日参数
            long planCommunicateNum = 0;
            long realCommunicateNum = 0;
            if (!CollectionUtils.isEmpty(yesterDayPlanChanceExtDtoList)) {

                planCommunicateNum = yesterDayPlanChanceExtDtoList.stream().count();


                realCommunicateNum = yesterDayPlanChanceExtDtoList.stream().filter(e -> {
                    BussinessCommunicateRecordDto bussinessCommunicateRecordDto = e.getBussinessCommunicateRecordDtoList().stream().filter(x -> {
                        if (x.getEndTime() >= yesterdayStartTime && x.getEndTime() <= yesterdayEndTime) {
                            return true;
                        }
                        return false;
                    }).findAny().orElse(null);
                    if (bussinessCommunicateRecordDto != null) {
                        return true;
                    }
                    return false;
                }).count();


            }
            summaryDto.setPlaningCommunicateNum((int) planCommunicateNum);
            summaryDto.setInPlaningNum((int) realCommunicateNum);


            if (planCommunicateNum != 0) {
                double rate = 0;
                rate = realCommunicateNum / (double) planCommunicateNum * 100;

                summaryDto.setCompletionRate(new BigDecimal(rate).setScale(2, BigDecimal.ROUND_HALF_UP));
            } else {
                summaryDto.setCompletionRate(new BigDecimal(0));
            }
            //设置计划外
            long planOutNum = 0;
            if (!CollectionUtils.isEmpty(yesterdayPlanOutChanceExtDtoList)) {
                planOutNum = yesterdayPlanOutChanceExtDtoList.stream().count();
            }
            summaryDto.setOutPlaningNum((int) planOutNum);

            //设置今日
            long todayNum = 0;
            if (!CollectionUtils.isEmpty(toDayPlanChanceExtDtoList)) {
                todayNum = toDayPlanChanceExtDtoList.stream().count();
            }
            summaryDto.setTodayToCommunicateNum((int) todayNum);
            communicateSummaryDtoList.add(summaryDto);
        }


        return communicateSummaryDtoList;

    }

    @Override
    public List<CommunicateSummaryDto> getDeptCommunicateBussinessChanceByIds(WorkbenchDataQueryDto dataQueryDto) {

        List<CommunicateSummaryDto> communicateChanceSummaryDtoList = new ArrayList<>();


        //设置时间参数
        Date yesterday = DateUtil.getPreviousDayByDateTime(new Date());
        long yesterdayStartTime = DateUtil.getDayStartTime(yesterday).getTime();
        long yesterdayEndTime = DateUtil.getDayEndTime(yesterday).getTime();
        Date today = new Date();


        //获取下属分组信息
        Map<DwhErpSubDeptDto, List<DwhErpSubDeptDto>> subUserOrDept = this.getSubUserOrDept(dataQueryDto);

        if (DwhConstants.XSZJ.equals(dataQueryDto.getPositionName())) {
            for (Map.Entry<DwhErpSubDeptDto, List<DwhErpSubDeptDto>> subDeptDtoListEntry : subUserOrDept.entrySet()) {
                CommunicateSummaryDto summaryDto = new NullCommunicateSummaryDto();
                summaryDto.setName(subDeptDtoListEntry.getKey().getDepartName());

                List<Integer> ids = subDeptDtoListEntry.getValue().stream().flatMap(x -> x.getSubUsers().stream())
                        .map(DwmNjUserOrgDfDo::getUserId).collect(Collectors.toList());

                //非空校验
                if (CollectionUtils.isEmpty(ids)) {
                    communicateChanceSummaryDtoList.add(summaryDto);
                    continue;
                }

                //昨天计划沟通
                dataQueryDto.setUserIds(ids);
                dataQueryDto.setNextContactDate(yesterday);
                dataQueryDto.setStartTime(yesterdayStartTime);
                dataQueryDto.setEndTime(yesterdayEndTime);
                List<WorkbenchBussinessChanceExtDto> yesterdayPlanCommunicateChanceListByIds = bussinessChanceMapper.getPlanCommunicateChanceListByIds(dataQueryDto);
                long planCommunicateNum = yesterdayPlanCommunicateChanceListByIds.stream().count();
                long realCommunicateNum = yesterdayPlanCommunicateChanceListByIds.stream().filter(e -> {
                    BussinessCommunicateRecordDto bussinessCommunicateRecordDto = e.getBussinessCommunicateRecordDtoList().stream().filter(x -> {
                        if (x.getEndTime() >= yesterdayStartTime && x.getEndTime() <= yesterdayEndTime) {
                            return true;
                        }
                        return false;
                    }).findAny().orElse(null);
                    if (bussinessCommunicateRecordDto != null) {
                        return true;
                    }
                    return false;
                }).count();
                summaryDto.setPlaningCommunicateNum((int) planCommunicateNum);
                summaryDto.setInPlaningNum((int) realCommunicateNum);
                if (planCommunicateNum != 0) {
                    double rate = realCommunicateNum / (double) planCommunicateNum * 100;
                    summaryDto.setCompletionRate(new BigDecimal(rate).setScale(2, BigDecimal.ROUND_HALF_UP));
                } else {
                    summaryDto.setCompletionRate(new BigDecimal(0));
                }


                //计划外
                List<WorkbenchBussinessChanceExtDto> yesterdayCommunicateChanceList = bussinessChanceMapper.getYesterdayContactChanceListByIds(dataQueryDto);
                long planOutNum = yesterdayCommunicateChanceList.stream().filter(x -> {
                    BussinessCommunicateRecordDto bussinessCommunicateRecordDto = x.getBussinessCommunicateRecordDtoList().stream().filter(e -> e.getEndTime() >= yesterdayStartTime && e.getEndTime() < yesterdayEndTime).findAny().orElse(null);
                    if (bussinessCommunicateRecordDto == null) {
                        return false;
                    }
                    return true;
                }).count();
                summaryDto.setOutPlaningNum((int) planOutNum);

                //获取今日
                dataQueryDto.setNextContactDate(today);
                List<WorkbenchBussinessChanceExtDto> todayPlanCommunicateChanceList = bussinessChanceMapper.getPlanCommunicateChanceListByIds(dataQueryDto);
                long todayNum = todayPlanCommunicateChanceList.stream().count();
                summaryDto.setTodayToCommunicateNum((int) todayNum);
                communicateChanceSummaryDtoList.add(summaryDto);
            }

        } else {
            List<DwhErpSubDeptDto> subDeptDtos = subUserOrDept.entrySet().stream().flatMap(x -> x.getValue().stream()).collect(Collectors.toList());
            for (DwhErpSubDeptDto subDeptDto : subDeptDtos) {

                CommunicateSummaryDto summaryDto = new NullCommunicateSummaryDto();
                summaryDto.setName(subDeptDto.getDepartName());

                List<Integer> ids = subDeptDto.getSubUsers().stream()
                        .map(DwmNjUserOrgDfDo::getUserId).collect(Collectors.toList());

                //非空校验
                if (CollectionUtils.isEmpty(ids)) {
                    communicateChanceSummaryDtoList.add(summaryDto);
                    continue;
                }

                //昨天计划沟通
                dataQueryDto.setUserIds(ids);
                dataQueryDto.setNextContactDate(yesterday);
                dataQueryDto.setStartTime(yesterdayStartTime);
                dataQueryDto.setEndTime(yesterdayEndTime);
                List<WorkbenchBussinessChanceExtDto> yesterdayPlanCommunicateChanceListByIds = bussinessChanceMapper.getPlanCommunicateChanceListByIds(dataQueryDto);
                long planCommunicateNum = yesterdayPlanCommunicateChanceListByIds.stream().count();
                long realCommunicateNum = yesterdayPlanCommunicateChanceListByIds.stream().filter(e -> {
                    BussinessCommunicateRecordDto bussinessCommunicateRecordDto = e.getBussinessCommunicateRecordDtoList().stream().filter(x -> {
                        if (x.getEndTime() >= yesterdayStartTime && x.getEndTime() <= yesterdayEndTime) {
                            return true;
                        }
                        return false;
                    }).findAny().orElse(null);
                    if (bussinessCommunicateRecordDto != null) {
                        return true;
                    }
                    return false;
                }).count();
                summaryDto.setPlaningCommunicateNum((int) planCommunicateNum);
                summaryDto.setInPlaningNum((int) realCommunicateNum);
                if (planCommunicateNum != 0) {
                    double rate = realCommunicateNum / (double) planCommunicateNum * 100;
                    summaryDto.setCompletionRate(new BigDecimal(rate).setScale(2, BigDecimal.ROUND_HALF_UP));
                } else {
                    summaryDto.setCompletionRate(new BigDecimal(0));
                }


                //计划外
                List<WorkbenchBussinessChanceExtDto> yesterdayCommunicateChanceList = bussinessChanceMapper.getYesterdayContactChanceListByIds(dataQueryDto);
                long planOutNum = yesterdayCommunicateChanceList.stream().filter(x -> {
                    BussinessCommunicateRecordDto bussinessCommunicateRecordDto = x.getBussinessCommunicateRecordDtoList().stream().filter(e -> e.getEndTime() >= yesterdayStartTime && e.getEndTime() < yesterdayEndTime).findAny().orElse(null);
                    if (bussinessCommunicateRecordDto == null) {
                        return false;
                    }
                    return true;
                }).count();
                summaryDto.setOutPlaningNum((int) planOutNum);

                //获取今日
                dataQueryDto.setNextContactDate(today);
                List<WorkbenchBussinessChanceExtDto> todayPlanCommunicateChanceList = bussinessChanceMapper.getPlanCommunicateChanceListByIds(dataQueryDto);
                long todayNum = todayPlanCommunicateChanceList.stream().count();
                summaryDto.setTodayToCommunicateNum((int) todayNum);
                communicateChanceSummaryDtoList.add(summaryDto);
            }
        }
        return communicateChanceSummaryDtoList;
    }

    @Override
    public List<DataOverviewSummaryDto> getDataOverviewSummaryDtoListByIds(WorkbenchDataQueryDto dataQueryDto) {

        List<DataOverviewSummaryDto> DataOverviewSummaryDtoList = new ArrayList<>();

        //设置时间参数
        Date yesterday = DateUtil.getPreviousDayByDateTime(new Date());
        long yesterdayStartTime = DateUtil.getDayStartTime(yesterday).getTime();
        long yesterdayEndTime = DateUtil.getDayEndTime(yesterday).getTime();
        long thisWeekStartTime = WorkbenchUtil.getThisWeekStart().getTime();
        long thisWeekEndTIME = WorkbenchUtil.getThisWeekEnd().getTime();
        long hisStartTime = DateUtil.convertLong("2019-11-1", DateUtil.DATE_FORMAT);

        //主管级别获取下属销售列表
        Map<DwhErpSubDeptDto, List<DwhErpSubDeptDto>> subUserOrDept = this.getSubUserOrDept(dataQueryDto);


        List<DwhErpSubDeptDto> deptDtos = subUserOrDept.entrySet().stream()
                .flatMap(e -> e.getValue().stream()).collect(Collectors.toList());

        List<DwhErpUserDto> users = deptDtos.stream()
                .flatMap(s -> s.getSubUsers().stream()).collect(Collectors.toList());

        List<Integer> ids = users.stream().map(DwmNjUserOrgDfDo::getUserId).collect(Collectors.toList());

        //非空校验
        if (CollectionUtils.isEmpty(ids)) {

            return DataOverviewSummaryDtoList;
        }

        //查询参数这周
        dataQueryDto.setUserIds(ids);
        dataQueryDto.setStartTime(thisWeekStartTime);
        dataQueryDto.setEndTime(thisWeekEndTIME);
        List<WorkbenchBussinessChanceExtDto> thisWeekNewAddImportChanceListByIds = bussinessChanceMapper.getThisWeekNewAddChanceListByIds(dataQueryDto);
        //分组
        Map<Integer, List<WorkbenchBussinessChanceExtDto>> thisWeekNewAddMap = thisWeekNewAddImportChanceListByIds.stream().collect(Collectors.groupingBy(WorkbenchBussinessChanceExtDto::getUserId));

        //遍历
        for (Integer id : ids) {
            DataOverviewSummaryDto summaryDto = new DataOverviewSummaryDto();
            DwhErpUserDto userInfo = dwhThreadLocalService.getUserInfo(id);

            //这周商机参数
            List<WorkbenchBussinessChanceExtDto> chanceExtDtoList = thisWeekNewAddMap.get(id);
            long thisWeekNewAddChanceNum = 0;
            BigDecimal thisWeekNewAddChanceAmount = new BigDecimal(0);

            if (!CollectionUtils.isEmpty(chanceExtDtoList)) {
                thisWeekNewAddChanceNum = chanceExtDtoList.stream().count();


                for (WorkbenchBussinessChanceExtDto workbenchBussinessChanceExtDto : chanceExtDtoList) {
                    if (workbenchBussinessChanceExtDto.getBussinessAmount() != null) {
                        thisWeekNewAddChanceAmount = thisWeekNewAddChanceAmount.add(workbenchBussinessChanceExtDto.getBussinessAmount());

                    }
                }
                thisWeekNewAddChanceAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
            }


            //历史总商机参数
            DwhWorkbenchOverviewDto hisBussinessChanceOverviewByUser = dwhBussinessChanceService.getHisBussinessChanceOverviewByUser(id, hisStartTime, yesterdayEndTime);

            BigDecimal hisTotalAmount = BigDecimal.ZERO;
            hisTotalAmount = hisBussinessChanceOverviewByUser.getTotalBussinessChanceAmount();
            if (hisTotalAmount != null) {
                hisTotalAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
            }
            Integer histTotalNum = 0;
            histTotalNum = hisBussinessChanceOverviewByUser.getTotalBussinessChanceNum();
            BigDecimal avrAmount = new BigDecimal(0);
            if (histTotalNum != null && histTotalNum != 0) {
                avrAmount = hisTotalAmount.divide(new BigDecimal(histTotalNum), 2, BigDecimal.ROUND_HALF_UP);
            }

            //昨日商机参数
            DwhWorkbenchOverviewDto yesterdayBussinessChanceOverviewByUser = dwhBussinessChanceService.getYesterdayBussinessChanceOverviewByUser(id, yesterdayStartTime, yesterdayEndTime);
            BigDecimal yesterdayTotalBussinessChanceAmount = BigDecimal.ZERO;
            if (yesterdayBussinessChanceOverviewByUser.getTotalBussinessChanceAmount() != null) {
                yesterdayTotalBussinessChanceAmount = yesterdayBussinessChanceOverviewByUser.getTotalBussinessChanceAmount();
                yesterdayTotalBussinessChanceAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
            }
            Integer yesterdayTotalBussinessChanceNum = 0;
            yesterdayTotalBussinessChanceNum = yesterdayBussinessChanceOverviewByUser.getTotalBussinessChanceNum();

            //设置属性
            summaryDto.setUserId(userInfo.getUserId());
            summaryDto.setName(userInfo.getUsername());
            summaryDto.setTotalBussinessAmount(hisTotalAmount);
            summaryDto.setTotalBussinessNum(histTotalNum);
            summaryDto.setAvrBussinessAmount(avrAmount);
            summaryDto.setYesterdayBussinessAmount(yesterdayTotalBussinessChanceAmount);
            summaryDto.setYesterdayBussinessNum(yesterdayTotalBussinessChanceNum);
            summaryDto.setThisWeekBussinessAmount(thisWeekNewAddChanceAmount);
            summaryDto.setThisWeekBussinessNum((int) thisWeekNewAddChanceNum);

            DataOverviewSummaryDtoList.add(summaryDto);
        }
        return DataOverviewSummaryDtoList;
    }

    @Override
    public List<DataOverviewSummaryDto> getDeptDataOverviewSummaryDtoListByIds(WorkbenchDataQueryDto dataQueryDto) {

        List<DataOverviewSummaryDto> dataOverviewSummaryDtoList = new ArrayList<>();

        //设置时间参数
        Date yesterday = DateUtil.getPreviousDayByDateTime(new Date());
        long yesterdayStartTime = DateUtil.getDayStartTime(yesterday).getTime();
        long yesterdayEndTime = DateUtil.getDayEndTime(yesterday).getTime();
        long thisWeekStartTime = WorkbenchUtil.getThisWeekStart().getTime();
        long thisWeekEndTIME = WorkbenchUtil.getThisWeekEnd().getTime();
        long hisStartTime = DateUtil.convertLong("2019-11-1", DateUtil.DATE_FORMAT);
        DwhErpUserDto userInfo = dwhThreadLocalService.getUserInfo(dataQueryDto.getUserId());

        //获取下属
        Map<DwhErpSubDeptDto, List<DwhErpSubDeptDto>> subUserOrDept = this.getSubUserOrDept(dataQueryDto);
        if (DwhConstants.XSZJ.equals(userInfo.getPositionName())) {
            for (Map.Entry<DwhErpSubDeptDto, List<DwhErpSubDeptDto>> subDeptDtoListEntry : subUserOrDept.entrySet()) {
                DataOverviewSummaryDto summaryDto = new NullDataOverviewSummaryDto();
                summaryDto.setName(subDeptDtoListEntry.getKey().getDepartName());


                List<Integer> ids = subDeptDtoListEntry.getValue().stream().flatMap(x -> x.getSubUsers().stream())
                        .map(DwmNjUserOrgDfDo::getUserId).collect(Collectors.toList());

                //非空校验
                if (CollectionUtils.isEmpty(ids)) {
                    dataOverviewSummaryDtoList.add(summaryDto);
                    continue;
                }


                //查询这周
                dataQueryDto.setUserIds(ids);
                dataQueryDto.setStartTime(thisWeekStartTime);
                dataQueryDto.setEndTime(thisWeekEndTIME);

                List<WorkbenchBussinessChanceExtDto> thisWeekNewAddChanceList = bussinessChanceMapper.getThisWeekNewAddChanceListByIds(dataQueryDto);

                BigDecimal thisWeekNewAddChanceAmount = new BigDecimal(0);
                long thisWeekNewAddChanceNum = thisWeekNewAddChanceList.stream().count();
                for (WorkbenchBussinessChanceExtDto workbenchBussinessChanceExtDto : thisWeekNewAddChanceList) {
                    thisWeekNewAddChanceAmount = thisWeekNewAddChanceAmount.add(workbenchBussinessChanceExtDto.getBussinessAmount());
                }


                List<DwhWorkbenchOverviewDto> hisBussinessChanceOverviewByDept = new ArrayList<>();
                List<DwhWorkbenchOverviewDto> yesBussinessChanceOverviewByDept = new ArrayList<>();


                //行政总监
                //获得当前的二级部门的orgIds
                List<Integer> orgIds = new ArrayList<>();
                orgIds.add(subDeptDtoListEntry.getKey().getDepartId());

                //历史总商机参数

                hisBussinessChanceOverviewByDept = dwhBussinessChanceService.getHisBussinessChanceOverviewByDept(orgIds, 2, hisStartTime, yesterdayEndTime);
                yesBussinessChanceOverviewByDept = dwhBussinessChanceService.getYesterdayBussinessChanceOverviewByDept(orgIds, 2, yesterdayStartTime, yesterdayEndTime);


                DwhWorkbenchOverviewDto hisDwhWorkbenchOverviewDto = hisBussinessChanceOverviewByDept.stream().findFirst().orElse(new NullDwhWorkbenchOverviewDto());


                BigDecimal hisTotalAmount = hisDwhWorkbenchOverviewDto.getTotalBussinessChanceAmount();
                Integer hisTotalNum = hisDwhWorkbenchOverviewDto.getTotalBussinessChanceNum();
                BigDecimal avrNum = new BigDecimal(0);
                if (hisTotalNum != null && hisTotalNum != 0) {
                    avrNum = hisTotalAmount.divide(new BigDecimal(hisTotalNum), 2, BigDecimal.ROUND_HALF_UP);
                }

                //昨天商机
                DwhWorkbenchOverviewDto yesterDayDwhWorkbenchOverviewDto = yesBussinessChanceOverviewByDept.stream().findFirst().orElse(new NullDwhWorkbenchOverviewDto());


                BigDecimal yesterDayTotalAmount = yesterDayDwhWorkbenchOverviewDto.getTotalBussinessChanceAmount();

                Integer yesterDayTotalNum = yesterDayDwhWorkbenchOverviewDto.getTotalBussinessChanceNum();


                //设置属性
                summaryDto.setTotalBussinessAmount(hisTotalAmount);
                summaryDto.setTotalBussinessNum(hisTotalNum);
                summaryDto.setAvrBussinessAmount(avrNum);
                summaryDto.setYesterdayBussinessAmount(yesterDayTotalAmount);
                summaryDto.setYesterdayBussinessNum(yesterDayTotalNum);
                summaryDto.setThisWeekBussinessAmount(thisWeekNewAddChanceAmount);
                summaryDto.setThisWeekBussinessNum((int) thisWeekNewAddChanceNum);

                dataOverviewSummaryDtoList.add(summaryDto);

            }
        } else {
            List<DwhErpSubDeptDto> subDeptDtoList = subUserOrDept.entrySet().stream().flatMap(e -> e.getValue().stream()).collect(Collectors.toList());
            for (DwhErpSubDeptDto dwhErpSubDeptDto : subDeptDtoList) {


                DataOverviewSummaryDto summaryDto = new NullDataOverviewSummaryDto();
                summaryDto.setName(dwhErpSubDeptDto.getDepartName());
//
//
                List<Integer> ids = dwhErpSubDeptDto.getSubUsers().stream()
                        .map(DwmNjUserOrgDfDo::getUserId).collect(Collectors.toList());

                //非空校验
                if (CollectionUtils.isEmpty(ids)) {
                    dataOverviewSummaryDtoList.add(summaryDto);
                    continue;
                }


                //查询这周
                dataQueryDto.setUserIds(ids);
                dataQueryDto.setStartTime(thisWeekStartTime);
                dataQueryDto.setEndTime(thisWeekEndTIME);

                List<WorkbenchBussinessChanceExtDto> thisWeekNewAddChanceList = bussinessChanceMapper.getThisWeekNewAddChanceListByIds(dataQueryDto);

                BigDecimal thisWeekNewAddChanceAmount = new BigDecimal(0);
                long thisWeekNewAddChanceNum = thisWeekNewAddChanceList.stream().count();
                for (WorkbenchBussinessChanceExtDto workbenchBussinessChanceExtDto : thisWeekNewAddChanceList) {
                    thisWeekNewAddChanceAmount = thisWeekNewAddChanceAmount.add(workbenchBussinessChanceExtDto.getBussinessAmount());
                }


                List<DwhWorkbenchOverviewDto> hisBussinessChanceOverviewByDept = new ArrayList<>();
                List<DwhWorkbenchOverviewDto> yesBussinessChanceOverviewByDept = new ArrayList<>();


                //经理
                //获得当前的三级级部门的orgIds
                List<Integer> orgIds = new ArrayList<>();
                orgIds.add(dwhErpSubDeptDto.getDepartId());

                //历史总商机参数

                hisBussinessChanceOverviewByDept = dwhBussinessChanceService.getHisBussinessChanceOverviewByDept(orgIds, 3, hisStartTime, yesterdayEndTime);

                yesBussinessChanceOverviewByDept = dwhBussinessChanceService.getYesterdayBussinessChanceOverviewByDept(orgIds, 3, yesterdayStartTime, yesterdayEndTime);


                DwhWorkbenchOverviewDto hisDwhWorkbenchOverviewDto = hisBussinessChanceOverviewByDept.stream().findFirst().orElse(new NullDwhWorkbenchOverviewDto());


                BigDecimal hisTotalAmount = hisDwhWorkbenchOverviewDto.getTotalBussinessChanceAmount();
                Integer hisTotalNum = hisDwhWorkbenchOverviewDto.getTotalBussinessChanceNum();
                BigDecimal avrNum = new BigDecimal(0);
                if (hisTotalNum != null && hisTotalNum != 0) {
                    avrNum = hisTotalAmount.divide(new BigDecimal(hisTotalNum), 2, BigDecimal.ROUND_HALF_UP);
                }

                //昨天商机
                DwhWorkbenchOverviewDto yesterDayDwhWorkbenchOverviewDto = yesBussinessChanceOverviewByDept.stream().findFirst().orElse(new NullDwhWorkbenchOverviewDto());


                BigDecimal yesterDayTotalAmount = yesterDayDwhWorkbenchOverviewDto.getTotalBussinessChanceAmount();

                Integer yesterDayTotalNum = yesterDayDwhWorkbenchOverviewDto.getTotalBussinessChanceNum();


                //设置属性
                summaryDto.setTotalBussinessAmount(hisTotalAmount);
                summaryDto.setTotalBussinessNum(hisTotalNum);
                summaryDto.setAvrBussinessAmount(avrNum);
                summaryDto.setYesterdayBussinessAmount(yesterDayTotalAmount);
                summaryDto.setYesterdayBussinessNum(yesterDayTotalNum);
                summaryDto.setThisWeekBussinessAmount(thisWeekNewAddChanceAmount);
                summaryDto.setThisWeekBussinessNum((int) thisWeekNewAddChanceNum);

                dataOverviewSummaryDtoList.add(summaryDto);

            }
        }

        return dataOverviewSummaryDtoList;
    }


    @Override
    public HeaddataSummaryDto getHeaddataSummaryDto(WorkbenchDataQueryDto dataQueryDto) {

        Integer userId = dataQueryDto.getUserId();
        //时间参数
        Date yesterday = DateUtil.getPreviousDayByDateTime(new Date());
        long yesterdayStartTime = DateUtil.getDayStartTime(yesterday).getTime();
        long yesterdayEndTime = DateUtil.getDayEndTime(yesterday).getTime();
        long thisWeekStartTime = WorkbenchUtil.getThisWeekStart().getTime();
        long thisWeekEndTIME = WorkbenchUtil.getThisWeekEnd().getTime();
        long hisStartTime = DateUtil.convertLong("2019-11-1", DateUtil.DATE_FORMAT);
        long thisMonthStartTime = DateUtil.getBeginDayOfMonth().getTime();
        long thisMonthEndTime = DateUtil.getEndDayOfMonth().getTime();

        HeaddataSummaryDto headdataSummaryDto = new HeaddataSummaryDto();
        //判断是销售还是主管及以上
        if ("销售工程师".equals(dataQueryDto.getPositionName()) || "销售专员".equals(dataQueryDto.getPositionName())) {

            //总计
            DwhWorkbenchOverviewDto hisBussinessChanceOverviewDto = dwhBussinessChanceService.getHisBussinessChanceOverviewByUser(userId, hisStartTime, yesterdayEndTime);
            Integer hisTotalNum = hisBussinessChanceOverviewDto.getTotalBussinessChanceNum();
            if (hisBussinessChanceOverviewDto.getTotalBussinessChanceAmount() == null) {
                hisBussinessChanceOverviewDto.setTotalBussinessChanceAmount(BigDecimal.ZERO);
            }
            BigDecimal hisTotalAmount = hisBussinessChanceOverviewDto.getTotalBussinessChanceAmount().setScale(0,BigDecimal.ROUND_HALF_UP);

            //昨日
            DwhWorkbenchOverviewDto yesterdayBussinessChanceOverviewDto = dwhBussinessChanceService.getYesterdayBussinessChanceOverviewByUser(userId, yesterdayStartTime, yesterdayEndTime);
            if (yesterdayBussinessChanceOverviewDto.getTotalBussinessChanceAmount() == null) {
                yesterdayBussinessChanceOverviewDto.setTotalBussinessChanceAmount(BigDecimal.ZERO);
            }
            BigDecimal yesterdayTotalAmount = yesterdayBussinessChanceOverviewDto.getTotalBussinessChanceAmount().setScale(0,BigDecimal.ROUND_HALF_UP);
            Integer yesterdayTotalNum = yesterdayBussinessChanceOverviewDto.getTotalBussinessChanceNum();

            //本月
            DwhWorkbenchOverviewDto expectBussinessChanceOverDto = dwhBussinessChanceService.getExpectBussinessChanceOverviewByUser(userId, thisMonthStartTime, thisMonthEndTime);
            if (expectBussinessChanceOverDto.getTotalBussinessChanceAmount() == null) {
                expectBussinessChanceOverDto.setTotalBussinessChanceAmount(BigDecimal.ZERO);
            }
            BigDecimal thisMonthTotalAmount = expectBussinessChanceOverDto.getTotalBussinessChanceAmount().setScale(0,BigDecimal.ROUND_HALF_UP);
            Integer thisMonthTotalNum = expectBussinessChanceOverDto.getTotalBussinessChanceNum();

            headdataSummaryDto.setTotalBussinessAmount(hisTotalAmount);
            headdataSummaryDto.setTotalBussinessNum(hisTotalNum);

            headdataSummaryDto.setYesterdayTotalBussinessAmount(yesterdayTotalAmount);
            headdataSummaryDto.setYesterdayTotalBussinessNum(yesterdayTotalNum);

            headdataSummaryDto.setThisMouthTotalAmount(thisMonthTotalAmount);
            headdataSummaryDto.setThisMouthBussinessNum(thisMonthTotalNum);
        } else {

            Map<DwhErpSubDeptDto, List<DwhErpSubDeptDto>> subUserOrDept = getSubUserOrDept(dataQueryDto);

            List<DwhWorkbenchOverviewDto> hisBussinessChanceOverviewByDept = new ArrayList<>();
            List<DwhWorkbenchOverviewDto> yesBussinessChanceOverviewByDept = new ArrayList<>();
            List<DwhWorkbenchOverviewDto> expectBussinessChanceOverviewByDept = new ArrayList<>();

            if (DwhConstants.XSZJ.equals(dataQueryDto.getPositionName())) {
                //获取下属id
                List<Integer> ids = subUserOrDept.entrySet().stream().map(x -> x.getKey().getDepartId()).collect(Collectors.toList());
                //非空校验
                if (CollectionUtils.isEmpty(ids)) {
                    return headdataSummaryDto;
                }
                //如果是销售总监
                hisBussinessChanceOverviewByDept = dwhBussinessChanceService.getHisBussinessChanceOverviewByDept(ids, 2, hisStartTime, yesterdayEndTime);
                yesBussinessChanceOverviewByDept = dwhBussinessChanceService.getYesterdayBussinessChanceOverviewByDept(ids, 2, yesterdayStartTime, yesterdayEndTime);
                expectBussinessChanceOverviewByDept = dwhBussinessChanceService.getExpectBussinessChanceOverviewByDept(ids, 2, thisMonthStartTime, thisMonthEndTime);

            } else if (DwhConstants.ZG.equals(dataQueryDto.getPositionName())) {
                DwhErpUserDto userInfo = dwhThreadLocalService.getUserInfo(dataQueryDto.getUserId());
                //获取下属id
                List<Integer> ids = subUserOrDept.entrySet().stream().map(x -> x.getKey().getDepartId()).collect(Collectors.toList());
                //非空校验
                if (CollectionUtils.isEmpty(ids)) {
                    return headdataSummaryDto;
                }
                //主管
                if (userInfo.getOrgId().equals(userInfo.getL3Id())) {
                    hisBussinessChanceOverviewByDept = dwhBussinessChanceService.getHisBussinessChanceOverviewByDept(ids, 3, hisStartTime, yesterdayEndTime);
                    yesBussinessChanceOverviewByDept = dwhBussinessChanceService.getYesterdayBussinessChanceOverviewByDept(ids, 3, yesterdayStartTime, yesterdayEndTime);
                    expectBussinessChanceOverviewByDept = dwhBussinessChanceService.getExpectBussinessChanceOverviewByDept(ids, 3, thisMonthStartTime, thisMonthEndTime);
                }else if (userInfo.getOrgId().equals(userInfo.getL2Id())){
                    hisBussinessChanceOverviewByDept = dwhBussinessChanceService.getHisBussinessChanceOverviewByDept(ids, 2, hisStartTime, yesterdayEndTime);
                    yesBussinessChanceOverviewByDept = dwhBussinessChanceService.getYesterdayBussinessChanceOverviewByDept(ids, 2, yesterdayStartTime, yesterdayEndTime);
                    expectBussinessChanceOverviewByDept = dwhBussinessChanceService.getExpectBussinessChanceOverviewByDept(ids, 2, thisMonthStartTime, thisMonthEndTime);
                }else if(userInfo.getOrgId().equals(userInfo.getL1Id())){
                    hisBussinessChanceOverviewByDept = dwhBussinessChanceService.getHisBussinessChanceOverviewByDept(ids, 1, hisStartTime, yesterdayEndTime);
                    yesBussinessChanceOverviewByDept = dwhBussinessChanceService.getYesterdayBussinessChanceOverviewByDept(ids, 1, yesterdayStartTime, yesterdayEndTime);
                    expectBussinessChanceOverviewByDept = dwhBussinessChanceService.getExpectBussinessChanceOverviewByDept(ids, 1, thisMonthStartTime, thisMonthEndTime);
                }
            } else {
                //获取下属id
                List<Integer> ids = subUserOrDept.entrySet().stream().flatMap(e -> e.getValue().stream()).map(DwhErpSubDeptDto::getDepartId).collect(Collectors.toList());
                //非空校验
                if (CollectionUtils.isEmpty(ids)) {
                    return headdataSummaryDto;
                }
                //经理
                hisBussinessChanceOverviewByDept = dwhBussinessChanceService.getHisBussinessChanceOverviewByDept(ids, 3, hisStartTime, yesterdayEndTime);
                yesBussinessChanceOverviewByDept = dwhBussinessChanceService.getYesterdayBussinessChanceOverviewByDept(ids, 3, yesterdayStartTime, yesterdayEndTime);
                expectBussinessChanceOverviewByDept = dwhBussinessChanceService.getExpectBussinessChanceOverviewByDept(ids, 3, thisMonthStartTime, thisMonthEndTime);
            }

            Integer hisTotalNum = 0;
            BigDecimal hisTotalAmount = new BigDecimal(0);
            Integer yesterdayTotalNum = 0;
            BigDecimal yesterdayTotalAmount = new BigDecimal(0);
            Integer thisMonthTotalNum = 0;
            BigDecimal thisMonthTotalAmount = new BigDecimal(0);

            //历史总计
            for (DwhWorkbenchOverviewDto dwhWorkbenchOverviewDto : hisBussinessChanceOverviewByDept) {
                hisTotalAmount = hisTotalAmount.add(dwhWorkbenchOverviewDto.getTotalBussinessChanceAmount()).setScale(0,BigDecimal.ROUND_HALF_UP);
                hisTotalNum += dwhWorkbenchOverviewDto.getTotalBussinessChanceNum();
            }

            //昨日总计
            for (DwhWorkbenchOverviewDto dwhWorkbenchOverviewDto : yesBussinessChanceOverviewByDept) {
                yesterdayTotalAmount = yesterdayTotalAmount.add(dwhWorkbenchOverviewDto.getTotalBussinessChanceAmount()).setScale(0,BigDecimal.ROUND_HALF_UP);
                yesterdayTotalNum += dwhWorkbenchOverviewDto.getTotalBussinessChanceNum();
            }

            //本月总计
            for (DwhWorkbenchOverviewDto dwhWorkbenchOverviewDto : expectBussinessChanceOverviewByDept) {
                thisMonthTotalAmount = thisMonthTotalAmount.add(dwhWorkbenchOverviewDto.getTotalBussinessChanceAmount()).setScale(0,BigDecimal.ROUND_HALF_UP);
                thisMonthTotalNum += dwhWorkbenchOverviewDto.getTotalBussinessChanceNum();
            }


            headdataSummaryDto.setTotalBussinessAmount(hisTotalAmount);
            headdataSummaryDto.setTotalBussinessNum(hisTotalNum);

            headdataSummaryDto.setYesterdayTotalBussinessAmount(yesterdayTotalAmount);
            headdataSummaryDto.setYesterdayTotalBussinessNum(yesterdayTotalNum);

            headdataSummaryDto.setThisMouthTotalAmount(thisMonthTotalAmount);
            headdataSummaryDto.setThisMouthBussinessNum(thisMonthTotalNum);

        }

        return headdataSummaryDto;
    }

    @Override
    public List<BussinessStatusEchartsDto> getDeptBussinessChanceStatusDtoList(WorkbenchDataQueryDto dataQueryDto) {

        List<BussinessStatusEchartsDto> bussinessStatusEchartsDtoList = new ArrayList<>();

        //时间参数
        Date yesterday = DateUtil.getPreviousDayByDateTime(new Date());
        long yesterdayStartTime = DateUtil.getDayStartTime(yesterday).getTime();
        long yesterdayEndTime = DateUtil.getDayEndTime(yesterday).getTime();
        long thisWeekStartTime = WorkbenchUtil.getThisWeekStart().getTime();
        long thisWeekEndTIME = WorkbenchUtil.getThisWeekEnd().getTime();
        long hisStartTime = DateUtil.convertLong("2019-11-1", DateUtil.DATE_FORMAT);
        long thisMonthStartTime = DateUtil.getBeginDayOfMonth().getTime();
        long thisMonthEndTime = DateUtil.getEndDayOfMonth().getTime();

        List<DwhWorkbenchOverviewDto> DeptBussinessChanceStatusDtoList = new ArrayList<>();
        Map<DwhErpSubDeptDto, List<DwhErpSubDeptDto>> subUserOrDept = this.getSubUserOrDept(dataQueryDto);

//        //分管部门总数id
//        List<Integer> orgIds = subUserOrDept.entrySet().stream().map(x -> x.getKey().getDepartId()).collect(Collectors.toList());
        Integer showOrgId = 0;
        //非空校验
//        if (CollectionUtils.isEmpty(orgIds)) {
//            return bussinessStatusEchartsDtoList;
//        }


        if (DwhConstants.ZG.equals(dataQueryDto.getPositionName())) {
            showOrgId = -1;
            //返回自己结果;
            //DeptBussinessChanceStatusDtoList = dwhBussinessChanceService.getBussinessChanceStatusOverviewByUser(firstId, hisStartTime, yesterdayEndTime);
            //返回整个部门的数据统计
            DwhErpUserDto curUser = dwhThreadLocalService.getUserInfo(dataQueryDto.getUserId());
            if(null != curUser.getOrgId()){
                List<Integer> orgIds = Arrays.asList(curUser.getOrgId());
                if(curUser.getOrgId().equals(curUser.getL3Id())){
                    DeptBussinessChanceStatusDtoList = dwhBussinessChanceService.getBussinessChanceStatusOverviewByDept(orgIds,3, hisStartTime, yesterdayEndTime);
                }else if (curUser.getOrgId().equals(curUser.getL2Id())){
                    DeptBussinessChanceStatusDtoList = dwhBussinessChanceService.getBussinessChanceStatusOverviewByDept(orgIds,2, hisStartTime, yesterdayEndTime);
                }else if (curUser.getOrgId().equals(curUser.getL1Id())){
                    DeptBussinessChanceStatusDtoList = dwhBussinessChanceService.getBussinessChanceStatusOverviewByDept(orgIds,1, hisStartTime, yesterdayEndTime);
                }
            }


        } else if (DwhConstants.XSZJ.equals(dataQueryDto.getPositionName())) {

            //总监
            //分管部门总数id
            List<Integer> orgIds = subUserOrDept.entrySet().stream().map(x -> x.getKey().getDepartId()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(orgIds)) {
                return bussinessStatusEchartsDtoList;
            }


            DeptBussinessChanceStatusDtoList = dwhBussinessChanceService.getBussinessChanceStatusOverviewByDept(orgIds, 2, hisStartTime, yesterdayEndTime);

            showOrgId = 69;

            DeptBussinessChanceStatusDtoList = DeptBussinessChanceStatusDtoList.stream().filter(x -> {
                return x.getOrgId() == 69;
            }).collect(Collectors.toList());


        } else if (DwhConstants.JL.equals(dataQueryDto.getPositionName())) {
            //经理
            //分管部门总数id
            List<Integer> orgIds = subUserOrDept.entrySet().stream().flatMap(e -> e.getValue().stream()).map(x -> x.getDepartId()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(orgIds)) {
                return bussinessStatusEchartsDtoList;
            }

            Integer showId = orgIds.stream().findFirst().orElse(0);
            showOrgId = showId;
            DeptBussinessChanceStatusDtoList = dwhBussinessChanceService.getBussinessChanceStatusOverviewByDept(orgIds, 3, hisStartTime, yesterdayEndTime);

            DeptBussinessChanceStatusDtoList = DeptBussinessChanceStatusDtoList.stream().filter(x -> {
                return x.getOrgId().equals(showId);
            }).collect(Collectors.toList());
        }


        //
        Integer totalNum = 0;


        //除去已关闭4，已成单7
        List<DwhWorkbenchOverviewDto> totalBussinessChanceList = DeptBussinessChanceStatusDtoList.stream().filter(x -> x.getStatus() != 4 && x.getStatus() != 7).collect(Collectors.toList());
        for (DwhWorkbenchOverviewDto e : totalBussinessChanceList) {
            totalNum += e.getTotalBussinessChanceNum();
        }


        //获得生成的orgid
        List<Integer> statusList = Arrays.asList(0, 1, 2, 3, 6);

        //遍历获值

        for (Integer statusId : statusList) {
            //获得对应status的信息
            DwhWorkbenchOverviewDto dwhWorkbenchOverviewDto = DeptBussinessChanceStatusDtoList.stream().filter(x -> x.getStatus().equals(statusId)).findFirst().orElse(new NullDwhWorkbenchOverviewDto());


            BussinessStatusEchartsDto statusEchartsDto = new BussinessStatusEchartsDto();

            statusEchartsDto.setOrgId(showOrgId);
            statusEchartsDto.setStatusId(statusId);
            statusEchartsDto.setStatusAmount(dwhWorkbenchOverviewDto.getTotalBussinessChanceAmount());
            statusEchartsDto.setStatusNum(dwhWorkbenchOverviewDto.getTotalBussinessChanceNum());
            ConvertStatusToName(statusEchartsDto);

            BigDecimal rate = new BigDecimal(0);
            if (totalNum != 0) {
                rate = BigDecimal.valueOf(dwhWorkbenchOverviewDto.getTotalBussinessChanceNum().doubleValue() / totalNum).setScale(4, BigDecimal.ROUND_HALF_UP);
            }
            NumberFormat percent = NumberFormat.getPercentInstance();
            percent.setMaximumFractionDigits(2);
            String rateformat = percent.format(rate);
            statusEchartsDto.setRate(rateformat);

            bussinessStatusEchartsDtoList.add(statusEchartsDto);
        }


        return bussinessStatusEchartsDtoList;

    }

    @Override
    public List<SalersDwhWorkbenchOverviewExtDto> getSalersImportantBussinessChances(WorkbenchDataQueryDto dataQueryDto) {

        List<SalersDwhWorkbenchOverviewExtDto> salersDwhWorkbenchOverviewExtDtoList = new ArrayList<>();

        //时间参数
        Date yesterday = DateUtil.getPreviousDayByDateTime(new Date());
        long yesterdayStartTime = DateUtil.getDayStartTime(yesterday).getTime();
        long yesterdayEndTime = DateUtil.getDayEndTime(yesterday).getTime();
        long thisWeekStartTime = WorkbenchUtil.getThisWeekStart().getTime();
        long thisWeekEndTIME = WorkbenchUtil.getThisWeekEnd().getTime();
        long hisStartTime = DateUtil.convertLong("2019-11-1", DateUtil.DATE_FORMAT);
        long thisMonthStartTime = DateUtil.getBeginDayOfMonth().getTime();
        long thisMonthEndTime = DateUtil.getEndDayOfMonth().getTime();

        Map<DwhErpSubDeptDto, List<DwhErpSubDeptDto>> subUserOrDept = this.getSubUserOrDept(dataQueryDto);
        //获取下属部门
        List<DwhErpSubDeptDto> deptDtos = subUserOrDept.entrySet().stream()
                .flatMap(e -> e.getValue().stream()).collect(Collectors.toList());
        //获得所有部门对应的销售
        List<DwhErpUserDto> users = deptDtos.stream()
                .flatMap(s -> s.getSubUsers().stream()).collect(Collectors.toList());
        //获取所有销售的ids
        List<Integer> ids = users.stream().map(DwmNjUserOrgDfDo::getUserId).collect(Collectors.toList());


        //非空校验
        if (CollectionUtils.isEmpty(ids)) {
            return salersDwhWorkbenchOverviewExtDtoList;
        }

        //设置总量
        Integer sAllTotalNum = 0;
        Integer aAllTotalNum = 0;
        BigDecimal sAllTotalAmount = new BigDecimal(0);
        BigDecimal aAllTotalAmount = new BigDecimal(0);
        for (Integer id : ids) {
            SalersDwhWorkbenchOverviewExtDto salersDwhWorkbenchOverviewExtDto = new SalersDwhWorkbenchOverviewExtDto();
            DwhErpUserDto userInfo = dwhThreadLocalService.getUserInfo(id);
            salersDwhWorkbenchOverviewExtDto.setSalesId(id);
            salersDwhWorkbenchOverviewExtDto.setSalesName(userInfo.getUsername());

            //设置初始参数
            Integer sTotalNum = 0;
            Integer aTotalNum = 0;
            BigDecimal sTotalAmount = new BigDecimal(0);
            BigDecimal aTotalAmount = new BigDecimal(0);

            List<DwhWorkbenchDto> importantBussinessChancesByUser = dwhBussinessChanceService.getImportantBussinessChancesByUser(id, hisStartTime, yesterdayEndTime);

            for (DwhWorkbenchDto dwhWorkbenchDto : importantBussinessChancesByUser) {
                if (dwhWorkbenchDto.getBussinessLevel() == 939) {
                    //s商机
                    sTotalNum++;
                    sAllTotalNum++;
                    sTotalAmount = sTotalAmount.add(dwhWorkbenchDto.getBussinessAmount());
                    sAllTotalAmount = sAllTotalAmount.add(dwhWorkbenchDto.getBussinessAmount());
                } else if (dwhWorkbenchDto.getBussinessLevel() == 940) {
                    aTotalNum++;
                    aAllTotalNum++;
                    aTotalAmount = aTotalAmount.add(dwhWorkbenchDto.getBussinessAmount());
                    aAllTotalAmount = aAllTotalAmount.add(dwhWorkbenchDto.getBussinessAmount());
                }
            }
            sTotalAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
            aTotalAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
            salersDwhWorkbenchOverviewExtDto.setSsTotalNum(sTotalNum);
            salersDwhWorkbenchOverviewExtDto.setSsTotalAmount(sTotalAmount);
            salersDwhWorkbenchOverviewExtDto.setAaTotalNum(aTotalNum);
            salersDwhWorkbenchOverviewExtDto.setAaTotalAmount(aTotalAmount);
            salersDwhWorkbenchOverviewExtDtoList.add(salersDwhWorkbenchOverviewExtDto);
        }

        //排序
        salersDwhWorkbenchOverviewExtDtoList = salersDwhWorkbenchOverviewExtDtoList.stream().sorted((x, y) -> x.getSsTotalAmount().compareTo(y.getSsTotalAmount()) * (-1)).collect(Collectors.toList());
        //总计
        SalersDwhWorkbenchOverviewExtDto overviewExtDto = new SalersDwhWorkbenchOverviewExtDto();

        overviewExtDto.setSalesName("总计");
        overviewExtDto.setSsTotalNum(sAllTotalNum);
        overviewExtDto.setSsTotalAmount(sAllTotalAmount);
        overviewExtDto.setAaTotalNum(aAllTotalNum);
        overviewExtDto.setAaTotalAmount(aAllTotalAmount);

        salersDwhWorkbenchOverviewExtDtoList.add(overviewExtDto);

        return salersDwhWorkbenchOverviewExtDtoList;
    }

    @Override
    public List<DwhWorkbenchOverviewDto> getDeptImportantBussinessChances(WorkbenchDataQueryDto dataQueryDto) {
        List<DwhWorkbenchOverviewDto> importantBussinessChancesByDept = new ArrayList<>();
        //时间参数
        Date yesterday = DateUtil.getPreviousDayByDateTime(new Date());
        long yesterdayStartTime = DateUtil.getDayStartTime(yesterday).getTime();
        long yesterdayEndTime = DateUtil.getDayEndTime(yesterday).getTime();
        long thisWeekStartTime = WorkbenchUtil.getThisWeekStart().getTime();
        long thisWeekEndTIME = WorkbenchUtil.getThisWeekEnd().getTime();
        long hisStartTime = DateUtil.convertLong("2019-11-1", DateUtil.DATE_FORMAT);
        long thisMonthStartTime = DateUtil.getBeginDayOfMonth().getTime();
        long thisMonthEndTime = DateUtil.getEndDayOfMonth().getTime();

        //设置总量
        Integer sAllTotalNum = 0;
        Integer aAllTotalNum = 0;
        BigDecimal sAllTotalAmount = new BigDecimal(0);
        BigDecimal aAllTotalAmount = new BigDecimal(0);
        Map<DwhErpSubDeptDto, List<DwhErpSubDeptDto>> subUserOrDept = this.getSubUserOrDept(dataQueryDto);

        List<Integer> orgIds = new ArrayList<>();

        //判断级别并获得下属组织orgIds
        if (DwhConstants.XSZJ.equals(dataQueryDto.getPositionName())) {
            orgIds = subUserOrDept.entrySet().stream().map(x -> x.getKey().getDepartId()).collect(Collectors.toList());
        } else {
            orgIds = subUserOrDept.entrySet().stream().flatMap(e -> e.getValue().stream()).map(x -> x.getDepartId()).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(orgIds)) {
            return importantBussinessChancesByDept;
        }


//
//        if (DwhConstants.XSZJ.equals(dataQueryDto.getPositionName())) {
//
//            importantBussinessChancesByDept = dwhBussinessChanceService.getImportantBussinessChancesByDept(orgIds, 2, hisStartTime, yesterdayEndTime);
//        } else {
//            importantBussinessChancesByDept = dwhBussinessChanceService.getImportantBussinessChancesByDept(orgIds, 3, hisStartTime, yesterdayEndTime);
//        }
//        importantBussinessChancesByDept.stream().sorted((x, y) -> x.getSsTotalAmount().compareTo(y.getSsTotalAmount()) * (-1)).collect(Collectors.toList());

        importantBussinessChancesByDept = dwhBussinessChanceService
                .getImportantBussinessChancesByDept(orgIds,
                        DwhConstants.XSZJ.equals(dataQueryDto.getPositionName()) ? 2 : 3,
                        hisStartTime,
                        yesterdayEndTime)
                .stream()
                .sorted(Comparator.comparing(DwhWorkbenchOverviewDto::getSsTotalAmount, Comparator.reverseOrder()))
                .collect(Collectors.toList());

        for (DwhWorkbenchOverviewDto dwhWorkbenchOverviewDto : importantBussinessChancesByDept) {
            sAllTotalNum += dwhWorkbenchOverviewDto.getSsTotalNum();
            aAllTotalNum += dwhWorkbenchOverviewDto.getAaTotalNum();
            sAllTotalAmount = sAllTotalAmount.add(dwhWorkbenchOverviewDto.getSsTotalAmount());
            aAllTotalAmount = aAllTotalAmount.add(dwhWorkbenchOverviewDto.getAaTotalAmount());
        }

        //设计总和
        DwhWorkbenchOverviewDto importantSummaryDto = new DwhWorkbenchOverviewDto();
        importantSummaryDto.setOrgName("总计");
        importantSummaryDto.setSsTotalNum(sAllTotalNum);
        importantSummaryDto.setSsTotalAmount(sAllTotalAmount.stripTrailingZeros());
        importantSummaryDto.setAaTotalNum(aAllTotalNum);
        importantSummaryDto.setAaTotalAmount(aAllTotalAmount.stripTrailingZeros());

        importantBussinessChancesByDept.add(importantSummaryDto);


        return importantBussinessChancesByDept;
    }

    @Override
    public List<SubObjectDo> getSubObject(WorkbenchDataQueryDto dataQueryDto) {
        List<SubObjectDo> subObjectDoList = new ArrayList<>();

        if ("销售工程师".equals(dataQueryDto.getPositionName()) || "销售专员".equals(dataQueryDto.getPositionName())) {
            return subObjectDoList;
        } else {
            Map<DwhErpSubDeptDto, List<DwhErpSubDeptDto>> subUserOrDept = this.getSubUserOrDept(dataQueryDto);
            if (CollectionUtils.isEmpty(subUserOrDept)) {
                return subObjectDoList;
            }
            if (DwhConstants.ZG.equals(dataQueryDto.getPositionName())) {
                List<DwhErpSubDeptDto> subObjectList = subUserOrDept.entrySet().stream().map(Map.Entry::getValue).findFirst().orElse(null);
                if (CollectionUtils.isEmpty(subObjectList)) {
                    return subObjectDoList;
                }
                subObjectDoList = subObjectList.stream().flatMap(o -> o.getSubUsers().stream()).map(e -> {
                    SubObjectDo sub = new SubObjectDo();
                    sub.setSubObjectId(e.getUserId());
                    sub.setSubObjectName(e.getUsername());
                    return sub;
                }).collect(Collectors.toList());
                return subObjectDoList;
            } else if (DwhConstants.JL.equals(dataQueryDto.getPositionName())) {
                subObjectDoList = subUserOrDept.entrySet().stream().flatMap(e -> e.getValue().stream()).map(x -> {
                    SubObjectDo sub = new SubObjectDo();
                    sub.setSubObjectId(x.getDepartId());
                    sub.setSubObjectName(x.getDepartName());
                    return sub;
                }).collect(Collectors.toList());
                return subObjectDoList;
            } else {

                subObjectDoList = subUserOrDept.entrySet().stream().map(x -> {
                    SubObjectDo sub = new SubObjectDo();
                    sub.setSubObjectId(x.getKey().getDepartId());
                    sub.setSubObjectName(x.getKey().getDepartName());
                    return sub;
                }).collect(Collectors.toList());
                return subObjectDoList;
            }
        }
    }

    @Override
    public List<BussinessStatusEchartsDto> getBussinessChanceStatus(WorkbenchDataQueryDto dataQueryDto) {

        List<BussinessStatusEchartsDto> bussinessStatusEchartsDtoList = new ArrayList<>();

        //时间参数
        Date yesterday = DateUtil.getPreviousDayByDateTime(new Date());
        long yesterdayStartTime = DateUtil.getDayStartTime(yesterday).getTime();
        long yesterdayEndTime = DateUtil.getDayEndTime(yesterday).getTime();
        long thisWeekStartTime = WorkbenchUtil.getThisWeekStart().getTime();
        long thisWeekEndTIME = WorkbenchUtil.getThisWeekEnd().getTime();
        long hisStartTime = DateUtil.convertLong("2019-11-1", DateUtil.DATE_FORMAT);
        long thisMonthStartTime = DateUtil.getBeginDayOfMonth().getTime();
        long thisMonthEndTime = DateUtil.getEndDayOfMonth().getTime();

        //商机状态0未处理、1报价中、2已报价、3已订单、4已关闭、5未分配、6处理中、7已成单
        List<DwhWorkbenchOverviewDto> bussinessChanceStatusOverview = new ArrayList<>();

        if ("销售工程师".equals(dataQueryDto.getPositionName()) || "销售专员".equals(dataQueryDto.getPositionName()) ) {
            //如果要查的是销售工程师
            bussinessChanceStatusOverview = dwhBussinessChanceService.getBussinessChanceStatusOverviewByUser(dataQueryDto.getUserId(), hisStartTime, yesterdayEndTime);
        } else if (DwhConstants.ZG.equals(dataQueryDto.getPositionName())){
            if (dataQueryDto.getQueryUserId() == -1){
                DwhErpUserDto userInfo = dwhThreadLocalService.getUserInfo(dataQueryDto.getUserId());
                List<Integer> ids  = Arrays.asList(userInfo.getOrgId());
                if (userInfo.getOrgId().equals(userInfo.getL3Id())){
                    bussinessChanceStatusOverview = dwhBussinessChanceService.getBussinessChanceStatusOverviewByDept(ids, 3, hisStartTime, yesterdayEndTime);
                } else if (userInfo.getOrgId().equals(userInfo.getL2Id())){
                    bussinessChanceStatusOverview = dwhBussinessChanceService.getBussinessChanceStatusOverviewByDept(ids, 2, hisStartTime, yesterdayEndTime);
                } else if (userInfo.getOrgId().equals(userInfo.getL1Id())){
                    bussinessChanceStatusOverview = dwhBussinessChanceService.getBussinessChanceStatusOverviewByDept(ids, 1, hisStartTime, yesterdayEndTime);
                }
            } else {
                bussinessChanceStatusOverview = dwhBussinessChanceService.getBussinessChanceStatusOverviewByUser(dataQueryDto.getQueryUserId(), hisStartTime, yesterdayEndTime);
            }

        } else {
            List<Integer> ids = Arrays.asList(dataQueryDto.getQueryOrgId());
            if (DwhConstants.XSZJ.equals(dataQueryDto.getPositionName())) {
                bussinessChanceStatusOverview = dwhBussinessChanceService.getBussinessChanceStatusOverviewByDept(ids, 2, hisStartTime, yesterdayEndTime);
            } else {
                bussinessChanceStatusOverview = dwhBussinessChanceService.getBussinessChanceStatusOverviewByDept(ids, 3, hisStartTime, yesterdayEndTime);
            }


        }


        //
        Integer totalNum = 0;

        //除去已成单7,已关闭4
        List<DwhWorkbenchOverviewDto> totalBussinessChanceList = bussinessChanceStatusOverview.stream().filter(x -> x.getStatus() != 4&&x.getStatus()!=7).collect(Collectors.toList());
        for (DwhWorkbenchOverviewDto e : totalBussinessChanceList) {
            totalNum += e.getTotalBussinessChanceNum();
        }


        List<Integer> statusList = Arrays.asList(0, 1, 2, 3, 6);

        //遍历获值

        for (Integer statusId : statusList) {
            //获得对应status的信息
            DwhWorkbenchOverviewDto dwhWorkbenchOverviewDto = bussinessChanceStatusOverview.stream().filter(x -> x.getStatus() == statusId).findFirst().orElse(new NullDwhWorkbenchOverviewDto());


            BussinessStatusEchartsDto statusEchartsDto = new BussinessStatusEchartsDto();

            statusEchartsDto.setStatusId(statusId);
            statusEchartsDto.setStatusAmount(dwhWorkbenchOverviewDto.getTotalBussinessChanceAmount());
            statusEchartsDto.setStatusNum(dwhWorkbenchOverviewDto.getTotalBussinessChanceNum());
            ConvertStatusToName(statusEchartsDto);

            BigDecimal rate = new BigDecimal(0);
            if (totalNum != 0) {
                rate = BigDecimal.valueOf(dwhWorkbenchOverviewDto.getTotalBussinessChanceNum().doubleValue() / totalNum).setScale(4, BigDecimal.ROUND_HALF_UP);
            }
            NumberFormat percent = NumberFormat.getPercentInstance();
            percent.setMaximumFractionDigits(2);
            String rateformat = percent.format(rate);
            statusEchartsDto.setRate(rateformat);

            bussinessStatusEchartsDtoList.add(statusEchartsDto);
        }

        log.info(bussinessStatusEchartsDtoList.toString());
        return bussinessStatusEchartsDtoList;
    }

    /**
     * .
     *
     * @param statusEchartsDto
     * @jira: 转换状态
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/8 9:56.
     * @author: Randy.Xu.
     * @return: void.
     * @throws: .
     */
    private void ConvertStatusToName(BussinessStatusEchartsDto statusEchartsDto) {
        if (statusEchartsDto.getStatusId() == 0) {
            statusEchartsDto.setStatusName("未处理");
        } else if (statusEchartsDto.getStatusId() == 1) {
            statusEchartsDto.setStatusName("报价中");
        } else if (statusEchartsDto.getStatusId() == 2) {
            statusEchartsDto.setStatusName("已报价");
        } else if (statusEchartsDto.getStatusId() == 3) {
            statusEchartsDto.setStatusName("已订单");
        } else if (statusEchartsDto.getStatusId() == 4) {
            statusEchartsDto.setStatusName("已关闭");
        } else if (statusEchartsDto.getStatusId() == 5) {
            statusEchartsDto.setStatusName("未分配");
        } else if (statusEchartsDto.getStatusId() == 6) {
            statusEchartsDto.setStatusName("处理中");
        } else if (statusEchartsDto.getStatusId() == 7) {
            statusEchartsDto.setStatusName("已成单");
        } else {
            statusEchartsDto.setStatusName("状态异常");
        }
    }

    /**
     * /**
     * .
     *
     * @param dataQueryDto
     * @jira: .
     * @notes: 根据Id获取下辖地区或人员信息
     * @version: 1.0.
     * @date: 2020/11/3 17:41.
     * @author: Randy.Xu.
     * @return: java.util.List<com.vedeng.dwh.model.dto.DwhErpSubDept>.
     * @throws: .
     */
    private Map<DwhErpSubDeptDto, List<DwhErpSubDeptDto>> getSubUserOrDept(WorkbenchDataQueryDto dataQueryDto) {


        //获取下属地区
        Integer userId = dataQueryDto.getUserId();
        Map<DwhErpSubDeptDto, List<DwhErpSubDeptDto>> subUserOrDept = dwhThreadLocalService.getUserOrganization(userId);


        return subUserOrDept;
    }
}
