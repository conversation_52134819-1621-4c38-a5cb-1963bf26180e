<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.aftersales.dao.TraderAssociatedLogMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.aftersales.model.TraderAssociatedLogDo">
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="ERP_ACCOUNT_ID" jdbcType="INTEGER" property="erpAccountId" />
    <result column="REGISTERED_ACCOUNT_PLATFORM" jdbcType="BIT" property="registeredAccountPlatform" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="REASON" jdbcType="INTEGER" property="reason" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="OPERATION_TYPE" jdbcType="BIT" property="operationType" />
    <result column="OPERATION_DESCRIPTION" jdbcType="VARCHAR" property="operationDescription" />
    <result column="OPERATOR_ID" jdbcType="INTEGER" property="operatorId" />
    <result column="OPERATOR_NAME" jdbcType="VARCHAR" property="operatorName" />
    <result column="OPERATOR_ORGANIZATION" jdbcType="VARCHAR" property="operatorOrganization" />
    <result column="OPERATE_TIME" jdbcType="TIMESTAMP" property="operateTime" />
  </resultMap>

  <sql id="BASE_SQL_COLUMNS">
    ID, ERP_ACCOUNT_ID,REGISTERED_ACCOUNT_PLATFORM, OPERATION_TYPE, OPERATION_DESCRIPTION, REASON, REMARK, OPERATOR_ID, OPERATOR_NAME, OPERATOR_ORGANIZATION,OPERATE_TIME
  </sql>

  <insert id="insertSelective" parameterType="com.vedeng.aftersales.model.TraderAssociatedLogDo">
    insert into T_TRADER_ASSOCIATED_LOG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="erpAccountId != null">
        ERP_ACCOUNT_ID,
      </if>
      <if test="registeredAccountPlatform != null">
        REGISTERED_ACCOUNT_PLATFORM,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="operationType != null">
        OPERATION_TYPE,
      </if>
      <if test="operationDescription != null">
        OPERATION_DESCRIPTION,
      </if>
      <if test="reason != null">
        REASON,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="operatorId != null">
        OPERATOR_ID,
      </if>
      <if test="operatorName != null">
        OPERATOR_NAME,
      </if>
      <if test="operatorOrganization != null">
        OPERATOR_ORGANIZATION,
      </if>
      <if test="operateTime != null">
        OPERATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="erpAccountId != null">
        #{erpAccountId,jdbcType=INTEGER},
      </if>
      <if test="registeredAccountPlatform != null">
        #{registeredAccountPlatform,jdbcType=BIT},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="operationType != null">
        #{operationType,jdbcType=BIT},
      </if>
      <if test="operationDescription != null">
        #{operationDescription,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="operatorName != null">
        #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="operatorOrganization != null">
        #{operatorOrganization,jdbcType=VARCHAR},
      </if>
      <if test="operateTime != null">
        #{operateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="BASE_SQL_COLUMNS"/>
    from T_TRADER_ASSOCIATED_LOG
    where ID = #{id,jdbcType=BIGINT}
  </select>
  <select id="listByWebAccountId" resultMap="BaseResultMap">
    select
    <include refid="BASE_SQL_COLUMNS"/>
    from T_TRADER_ASSOCIATED_LOG
    where ERP_ACCOUNT_ID = #{webAccountId,jdbcType=BIGINT}
  </select>
</mapper>