package com.vedeng.api.standard.adapter.buyorder.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 采购单更新请求
 * 专门用于更新采购单操作，包含所有可更新的业务字段
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BuyOrderUpdateRequest extends BaseBuyOrderRequest {

    private static final long serialVersionUID = 1L;

    // ========== 基础控制字段 ==========
    /**
     * 是否新订单（0-否，1-是）
     */
    private Integer isNew;

    /**
     * 订单状态
     */
    private Integer status;

    /**
     * 是否备货订单
     */
    private Boolean isBhOrder = false;

    /**
     * 搜索供应商名称
     */
    private String searchTraderName;

    /**
     * 供应商供应商ID
     */
    private Integer traderSupplierId;

    /**
     * 供应商名称
     */
    private String traderName;

    /**
     * 期间余额
     */
    private BigDecimal periodBalance;

    /**
     * 有效返利费用
     */
    private BigDecimal validRebateCharge;

    // ========== 供应商信息字段 ==========
    /**
     * 供应商ID
     */
    private Integer traderId;

    /**
     * 联系人信息（格式：contactId|name|mobile|telephone）
     */
    private String traderContactStr;

    /**
     * 地址信息（格式：addressId|area|address）
     */
    private String traderAddressStr;

    /**
     * 供应商备注
     */
    private String traderComments;

    /**
     * 发货方式
     */
    private Integer deliveryDirect;

    // ========== 付款信息字段 ==========
    /**
     * 付款方式
     */
    private Integer paymentType;

    /**
     * 预付金额
     */
    private BigDecimal prepaidAmount;

    /**
     * 账期金额
     */
    private BigDecimal accountPeriodAmount;

    /**
     * 尾款金额
     */
    private BigDecimal retainageAmount;

    /**
     * 尾款期限（月）
     */
    private Integer retainageAmountMonth;

    /**
     * 付款备注
     */
    private String paymentComments;

    /**
     * 是否有账期（0-否，1-是）
     */
    private Integer haveAccountPeriod;

    /**
     * 银行承兑汇票（0-否，1-是）
     */
    private Integer bankAcceptance;

    // ========== 收票信息字段 ==========
    /**
     * 收票种类
     */
    private Integer invoiceType;

    /**
     * 收票备注
     */
    private String invoiceComments;

    // ========== 收货信息字段 ==========
    /**
     * 物流公司ID
     */
    private Integer logisticsId;

    /**
     * 收货地址（格式：addressId|companyName|areas）
     */
    private String takeAddressId;

    /**
     * 运费说明
     */
    private Integer freightDescription;

    /**
     * 物流备注
     */
    private String logisticsComments;

    // ========== 商品相关字段 ==========
    /**
     * 商品ID集合（逗号分隔）
     */
    private String goodsIds = "";

    /**
     * 销售商品ID集合（逗号分隔）
     */
    private String saleorderGoodsIds = "";

    /**
     * 当前操作的采购商品ID
     */
    private Integer buyorderGoodsId;

    /**
     * 销售商品数量
     */
    private Integer saleorderGoodsNum;

    /**
     * 采购费用ID
     */
    private String buyorderExpenseId = "";

    /**
     * 商品列表（用于更新商品信息）
     */
    private List<BuyOrderGoodsRequest> goodsList;

    /**
     * 删除的采购商品ID集合（逗号分隔）
     */
    private String delBuyGoodsIds = "";

    // ========== 商品业务字段（格式：商品ID|值） ==========
    /**
     * 采购汇总（格式：商品ID|数量）
     */
    private String buySum;

    /**
     * 是否赠品商品（格式：商品ID|0或1）
     */
    private String isGiftGoods;

    /**
     * 商品价格（格式：商品ID|价格）
     */
    private String price;

    /**
     * 发货时间字符串（格式：商品ID|日期）
     */
    private String sendGoodsTimeStr;

    /**
     * 收货时间字符串（格式：商品ID|日期）
     */
    private String receiveGoodsTimeStr;

    /**
     * 交货周期（格式：商品ID|天数）
     */
    private String deliveryCycle;

    /**
     * 是否有授权（格式：商品ID|0或1）
     */
    private String isHaveAuth;

    /**
     * 商品备注（格式：商品ID|备注）
     */
    private String goodsComments;

    /**
     * 安装信息（格式：商品ID|安装信息）
     */
    private String installation;

    /**
     * 内部备注（格式：商品ID|备注）
     */
    private String insideComments;

    /**
     * 数据库采购数量（格式：商品ID|销售商品ID|数量）
     */
    private String dbBuyNum;

    @Override
    public String getOperationType() {
        return "update";
    }

    @Override
    public void validate() {
        super.validate();
        
        if (getBuyOrderId() == null) {
            throw new IllegalArgumentException("采购单ID不能为空");
        }
    }

    /**
     * 创建用于更新操作的请求对象
     */
    public static BuyOrderUpdateRequest forUpdate(Integer buyOrderId) {
        BuyOrderUpdateRequest request = new BuyOrderUpdateRequest();
        request.setBuyOrderId(buyOrderId);
        return request;
    }
}
