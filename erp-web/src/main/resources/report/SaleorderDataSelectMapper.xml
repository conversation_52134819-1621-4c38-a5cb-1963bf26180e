<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.report.dao.SaleorderDataSelectMapper">
    <select id="getSaleorderLeftAmountPeriodIdsList" resultType="java.lang.Integer">
        SELECT DISTINCT s.SALEORDER_ID
        FROM T_SALEORDER s
        LEFT JOIN (
        SELECT COALESCE
        (
        sum( ABS( a.AMOUNT ) ) - IFNULL( c.hk_amount, 0 ) - ifnull( d.hk_amount, 0 ),
        0
        ) AS relatedDecimal,
        b.RELATED_ID
        FROM
        T_CAPITAL_BILL a
        LEFT JOIN T_CAPITAL_BILL_DETAIL b ON a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
        LEFT JOIN (
        SELECT COALESCE
        ( sum( ABS( b1.AMOUNT ) ), 0 ) AS hk_amount,
        b1.RELATED_ID
        FROM
        T_CAPITAL_BILL a1
        LEFT JOIN T_CAPITAL_BILL_DETAIL b1 ON a1.CAPITAL_BILL_ID = b1.CAPITAL_BILL_ID
        WHERE
        a1.TRADER_TYPE IN ( 1, 4 )
        AND b1.ORDER_TYPE = 1
        AND b1.BUSSINESS_TYPE = 533
        GROUP BY
        b1.RELATED_ID
        ) AS c ON b.RELATED_ID = c.RELATED_ID
        LEFT JOIN (
        SELECT COALESCE
        ( sum( ABS( b1.AMOUNT ) ), 0 ) AS hk_amount,
        c1.ORDER_ID
        FROM
        T_CAPITAL_BILL a1
        LEFT JOIN T_CAPITAL_BILL_DETAIL b1 ON a1.CAPITAL_BILL_ID = b1.CAPITAL_BILL_ID
        LEFT JOIN T_AFTER_SALES c1 ON c1.AFTER_SALES_ID = b1.RELATED_ID
        AND c1.SUBJECT_TYPE = 535
        WHERE
        a1.TRADER_TYPE = 3
        AND b1.ORDER_TYPE = 3
        AND b1.BUSSINESS_TYPE = 533
        GROUP BY
        c1.ORDER_ID
        ) AS d ON d.ORDER_ID = b.RELATED_ID
        WHERE
        a.TRADER_TYPE = 3
        AND a.TRADER_MODE = 527
        AND b.ORDER_TYPE = 1
        GROUP BY
        b.RELATED_ID
        ) shenyuzhangqi ON shenyuzhangqi.RELATED_ID = s.SALEORDER_ID
        WHERE
        s.ORDER_TYPE != 2
        <if test="startTime != 0 and startTime != null  and endTime != 0 and endTime != null">
            AND s.SATISFY_DELIVERY_TIME >= #{startTime}
            AND s.SATISFY_DELIVERY_TIME &lt;= #{endTime}
        </if>
        GROUP BY
        s.SALEORDER_ID

    </select>
    <select id="findLeftAmountPeriodBySaleorderId" resultType="java.util.Map">
        SELECT DISTINCT s.SALEORDER_ID saleorderId, ifnull(shenyuzhangqi.relatedDecimal,0) leftAmountPeriod
        FROM T_SALEORDER s
        LEFT JOIN (
        SELECT COALESCE
        (
        sum( ABS( a.AMOUNT ) ) - IFNULL( c.hk_amount, 0 ) - ifnull( d.hk_amount, 0 ),
        0
        ) AS relatedDecimal,
        b.RELATED_ID
        FROM
        T_CAPITAL_BILL a
        LEFT JOIN T_CAPITAL_BILL_DETAIL b ON a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
        LEFT JOIN (
        SELECT COALESCE
        ( sum( ABS( b1.AMOUNT ) ), 0 ) AS hk_amount,
        b1.RELATED_ID
        FROM
        T_CAPITAL_BILL a1
        LEFT JOIN T_CAPITAL_BILL_DETAIL b1 ON a1.CAPITAL_BILL_ID = b1.CAPITAL_BILL_ID
        WHERE
        a1.TRADER_TYPE IN ( 1, 4 )
        AND b1.ORDER_TYPE = 1
        AND b1.BUSSINESS_TYPE = 533
        <if test="bizIds != null and bizIds.size() > 0">
            and b1.RELATED_ID in
            <foreach collection="bizIds" item="bizId" index="index" close=")" open="(" separator=",">
                #{bizId}
            </foreach>
        </if>
        GROUP BY
        b1.RELATED_ID
        ) AS c ON b.RELATED_ID = c.RELATED_ID
        LEFT JOIN (
        SELECT COALESCE
        ( sum( ABS( b1.AMOUNT ) ), 0 ) AS hk_amount,
        c1.ORDER_ID
        FROM
        T_CAPITAL_BILL a1
        LEFT JOIN T_CAPITAL_BILL_DETAIL b1 ON a1.CAPITAL_BILL_ID = b1.CAPITAL_BILL_ID
        LEFT JOIN T_AFTER_SALES c1 ON c1.AFTER_SALES_ID = b1.RELATED_ID
        AND c1.SUBJECT_TYPE = 535
        WHERE
        a1.TRADER_TYPE = 3
        AND b1.ORDER_TYPE = 3
        AND b1.BUSSINESS_TYPE = 533
        <if test="bizIds != null and bizIds.size() > 0">
            and c1.ORDER_ID in
            <foreach collection="bizIds" item="bizId" index="index" close=")" open="(" separator=",">
                #{bizId}
            </foreach>
        </if>
        GROUP BY
        c1.ORDER_ID
        ) AS d ON d.ORDER_ID = b.RELATED_ID
        WHERE
        a.TRADER_TYPE = 3
        AND a.TRADER_MODE = 527
        AND b.ORDER_TYPE = 1
        <if test="bizIds != null and bizIds.size() > 0">
            AND b.RELATED_ID in
            <foreach collection="bizIds" item="bizId" index="index" close=")" open="(" separator=",">
                #{bizId}
            </foreach>
        </if>
        GROUP BY
        b.RELATED_ID
        ) shenyuzhangqi ON shenyuzhangqi.RELATED_ID = s.SALEORDER_ID
        WHERE
        s.ORDER_TYPE != 2
        <if test="bizIds != null and bizIds.size() > 0">
            AND s.SALEORDER_ID in
            <foreach collection="bizIds" item="bizId" index="index" close=")" open="(" separator=",">
                #{bizId}
            </foreach>
        </if>
        GROUP BY
        s.SALEORDER_ID

    </select>
    <select id="getSaleorderIdsPreTime" resultType="java.lang.Integer">
        SELECT DISTINCT a.SALEORDER_ID
        FROM T_SALEORDER a
        WHERE
        a.ORDER_TYPE != 2
             AND ((a.MOD_TIME >= #{startTime}
            AND a.MOD_TIME &lt;= #{endTime} )
            OR (a.UPDATE_DATA_TIME >= #{startTime}
            AND a.UPDATE_DATA_TIME &lt;= #{endTime})
            OR (a.SATISFY_DELIVERY_TIME >= #{startTime}
            AND a.SATISFY_DELIVERY_TIME &lt;= #{endTime} ))
    </select>
    <select id="findAutoCheckBySaleorderId" resultType="java.util.Map">

        SELECT SALEORDER_ID saleorderId,
        case
        when KSSH = 0 AND SHWC = 0 then 0
        when ZZZDSH > 0 and YYBSH = 0 and SHWC > 0 then 1
        else 0 end as autoCheck
        FROM (
        SELECT A.SALEORDER_ID,
        sum(if(TA.ACT_NAME_ = '资质自动审核' and TA.ASSIGNEE_ = 'njadmin', 1, 0)) as ZZZDSH,
        sum(if(TA.ACT_NAME_ IN ('质量管理部审核','运营部审核'), 1, 0))                               as YYBSH,
        sum(if(TA.ACT_NAME_ = '审核完成', 1, 0))                                as SHWC,
        sum(if(TA.ACT_NAME_ = '开始审核', 1, 0))                                as KSSH
        FROM T_SALEORDER A
        LEFT JOIN ACT_HI_PROCINST RES ON SUBSTRING_INDEX( RES.BUSINESS_KEY_, '_',- 1) = A.SALEORDER_ID
        AND (RES.BUSINESS_KEY_ LIKE 'saleorderVerify%'
        OR RES.BUSINESS_KEY_ LIKE 'hc_order_auto_verify%'
        OR RES.BUSINESS_KEY_ LIKE 'bd_order_auto_verify%')
        LEFT JOIN ACT_HI_ACTINST TA ON RES.PROC_INST_ID_ = TA.PROC_INST_ID_
        AND TA.ACT_NAME_ IN ('资质自动审核','质量管理部审核','运营部审核','审核完成','开始审核')
        WHERE
              A.SALEORDER_ID in
            <foreach collection="bizIds" item="bizId" index="index" close=")" open="(" separator=",">
                #{bizId}
            </foreach>
        and(
        RES.BUSINESS_KEY_ LIKE 'saleorderVerify%'
        OR RES.BUSINESS_KEY_ LIKE 'hc_order_auto_verify%'
        OR RES.BUSINESS_KEY_ LIKE 'bd_order_auto_verify%') GROUP BY A.SALEORDER_ID
        ) x
    </select>

    <select id="SaleorderCommunicateNumIdsList" resultType="java.lang.Integer">
        select a.SALEORDER_ID
        from T_SALEORDER a
        left join T_COMMUNICATE_RECORD b ON a.SALEORDER_ID = b.RELATED_ID
        where 1 = 1 AND b.COMMUNICATE_TYPE = 246
        <if test="startTime != 0 and startTime != null  and endTime != 0 and endTime != null">
            AND (b.ADD_TIME >= #{startTime}
            AND b.ADD_TIME &lt;= #{endTime} )
        </if>
        GROUP BY
        a.SALEORDER_ID

    </select>
    <select id="findCommunicateNumBySaleorderId" resultType="java.util.Map">
        select a.SALEORDER_ID saleorderId,count(b. COMMUNICATE_RECORD_ID) communicateNum
        from T_SALEORDER a
        left join T_COMMUNICATE_RECORD b ON a.SALEORDER_ID = b.RELATED_ID AND b.COMMUNICATE_TYPE = 246
        where
        <if test="bizIds != null and bizIds.size() > 0">
            a.SALEORDER_ID in
            <foreach collection="bizIds" item="bizId" index="index" close=")" open="(" separator=",">
                #{bizId}
            </foreach>
        </if>
        GROUP BY
        a.SALEORDER_ID

    </select>

    <select id="getWarehouseGoodsOperateChangedData" resultType="com.newtask.data.dto.WarehouseOperateDataDto">
        SELECT
            WAREHOUSE_GOODS_OPERATE_LOG_ID,ADD_TIME
        FROM
            T_WAREHOUSE_GOODS_OPERATE_LOG
        ORDER BY WAREHOUSE_GOODS_OPERATE_LOG_ID desc
        limit #{num}
    </select>
    <select id="getBuyStatusChangedGoodsSecondQuery" resultType="java.lang.Integer">
        SELECT
        RELATED_ID
        FROM
        T_WAREHOUSE_GOODS_OPERATE_LOG
        WHERE
        OPERATE_TYPE = 2
        AND IS_ENABLE = 1
        AND WAREHOUSE_GOODS_OPERATE_LOG_ID in
        <foreach collection="warehouseGoodsOperateChangedData" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        UNION
        SELECT
        e.SALEORDER_GOODS_ID
        FROM
        T_WAREHOUSE_GOODS_OPERATE_LOG b
        LEFT JOIN T_R_BUYORDER_J_SALEORDER d ON b.RELATED_ID = d.BUYORDER_GOODS_ID
        LEFT JOIN T_SALEORDER_GOODS e ON e.SALEORDER_GOODS_ID = d.SALEORDER_GOODS_ID
        WHERE
        e.DELIVERY_DIRECT = 0
        AND b.OPERATE_TYPE = 1
        AND b.IS_ENABLE = 1
        AND b.WAREHOUSE_GOODS_OPERATE_LOG_ID in
        <foreach collection="warehouseGoodsOperateChangedData" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>