package com.newtask.saleorder;

import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.aftersale.domain.dto.AfterSalesToYxbDto;
import com.vedeng.erp.aftersale.service.AfterSalesCommonService;
import com.vedeng.erp.aftersale.service.AfterSalesToYxbApiService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 医修帮关闭和完结工单定时任务
 */
@Component
@JobHandler(value = "TicketCompletionTask")
@Slf4j
public class TicketCompletionTask extends AbstractJobHandler {
    @Autowired
    private AfterSalesToYxbApiService afterSalesToYxbApiService;
    @Autowired
    private AfterSalesCommonService afterSalesCommonService;
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        log.info("==================TicketCompletionTask开始====================");
        if (StrUtil.isEmpty(param)) {
            int pageSize = 100;
            int pageNum = 1;
            Page<Object> page = new Page<>(pageNum, pageSize);
            PageInfo<AfterSalesToYxbDto> afterSalesToYxbDtoPageInfo = afterSalesToYxbApiService.selectByForDownTask(page);
            pageNum = afterSalesToYxbDtoPageInfo.getPages();
            while (true){
                log.info("分页执行进度 {},{}", pageSize, pageNum);
                Page<Object> pageDes = new Page<>(pageNum, pageSize);
                PageInfo<AfterSalesToYxbDto> salesToYxbDtoPageInfo = afterSalesToYxbApiService.selectByForDownTask(pageDes);
                List<AfterSalesToYxbDto> afterSalesToYxbDtos = salesToYxbDtoPageInfo.getList();
                if (afterSalesToYxbDtos.isEmpty()){
                    break;
                }
                afterSalesToYxbDtos.forEach(x->{
                    afterSalesCommonService.closeOrCompletedYxb(x);
                });
                if (!salesToYxbDtoPageInfo.isHasPreviousPage()) {
                    break;
                }
                pageNum = salesToYxbDtoPageInfo.getPrePage();
                log.info("分页查询进度 {},{}", pageSize, pageNum);
            }
        }
        log.info("==================TicketCompletionTask结束====================");
        return SUCCESS;
    }
}
