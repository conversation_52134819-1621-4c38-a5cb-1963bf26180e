<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="新增样品申请单" scope="application"/>
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>
<%@ include file="../../common/common.jsp" %>
<link rel="stylesheet" href="<%= basePath %>static/css/select2.css?rnd=${resourceVersionKey}"/>
<script type="text/javascript"
        src="<%= basePath %>static/js/jquery/ajaxfileupload.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src='<%= basePath %>static/js/select2.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript"
        src='<%= basePath %>static/js/select2_locale_zh-CN.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript">

    function deleteRow(row) {
        index = layer.confirm("您是否确认该操作？", {
            btn: ['确定', '取消'] //按钮
        }, function () {
            $(row).parent().parent().remove();
            layer.close(index);
        }, function () {
        });
    }

    function addSubmit() {
        var canSubmit = true;

        checkLogin();
        $("#too_long_reveice_reason").hide()
        $("#no_receive_user").hide()
        $("#no_logistic_comment").hide()
        $("#too_long_logistic_comment").hide()
        $("#no_sku").hide()

        if ($("#applyType").val() == 0) {
            canSubmit = false;
            layer.alert("请填写申请类型！");
            return false;
        }

        if ($('#applyType').val() == 4223) {
            if ($("#activityCode").val() == '') {
                canSubmit = false;
                layer.alert("请填写备案的活动编码");
                return false;
            }
            if ($("#activityCode").val().length > 255) {
                canSubmit = false;
                layer.alert("活动编码不允许超过255个字！");
                return false;
            }

            if ($("#activityName").val() == '') {
                canSubmit = false;
                layer.alert("请填写备案的活动名称");
                return false;
            }
            if ($("#activityName").val().length > 255) {
                layer.alert("活动名称不允许超过255个字！");
                return false;
            }
        }

        if ($("#borrowTraderName").val() == '') {
            canSubmit = false;
            layer.alert("请填写样品申请企业！");
            return false;
        }


        if ($("#traderContact").val() == '0') {
            canSubmit = false;
            layer.alert("请选择收货联系人！");
            return false;
        }

        if ($("#traderAddress").val() == '0') {
            canSubmit = false;
            layer.alert("请选择收货地址！");
            return false;
        }

        if ($("#logisticCommnet").val() == '') {
            canSubmit = false;
            layer.alert("请填写物流备注！");
            return false;
        }


        if ($("#logisticCommnet").val().length > 500) {
            canSubmit = false;
            layer.alert("物流备注不允许超过500个字！");
            return false;
        }


        if ($("#borrowReason").val() == '') {
            canSubmit = false;
            layer.alert("请填写样品申请原因！");
            return false;
        }

        if ($("#borrowReason").val().length > 1000) {
            canSubmit = false;
            layer.alert("样品申请原因不允许超过1000个字！");
            return false;
        }


        var trs = $('.sampleOrderGoodsClass');

        if (trs.length == 0) {
            canSubmit = false;
            layer.alert("请选择产品！");
            return;
        }

        var re = /^[1-9]\d*$/;
        trs.each(function () {
            var skuNo = $(this).find('td').eq(0).find('input').eq(0).val();
            var receiveOutNum = $(this).find('td').eq(4).find('input').eq(0).val();

            if (receiveOutNum == undefined || receiveOutNum == ''){
                canSubmit = false;
                layer.alert("请填写数量！");
                return false;
            }

            if (!re.test(receiveOutNum)) {
                canSubmit = false;
                layer.alert("数量必须为大于0的整数");
                return false;
            }
        });

        $('#traderContactValue').val($("#traderContact").find("option:selected").text());
        $('#traderAddressValue').val($("#traderAddress").find("option:selected").text());


        if ($('#alreadySubmit').val() == 0 && canSubmit){
            $('#alreadySubmit').val(1);
            $("#addForm").submit();
        }



        // $.ajax({
        //     url: page_url + '/wms/sampleOut/saveSampleOutOrder.do',
        //     data: $('#addForm').serialize(),
        //     type: "POST",
        //     dataType: "json",
        //     async: false,
        //     success: function (data) {
        //
        //     },
        //     error: function (data) {
        //         if (data.status == 1001) {
        //             layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
        //         }
        //     }
        // });

    }

    function setGoodData(sku, goodsName, brandName, model, unitName, stockNum, availableStockNum, price, purchasePrice) {
        var isSaleFlag = $('#isSaleFlag').val();
        var purchasePriceStr = '';
        if (isSaleFlag == 0){
            purchasePriceStr = "     <td>" + purchasePrice + "</td>\n";
        }

        var canAddGoods = true;
        $('.mySkuFlag').each(function () {
            if ($(this).val() == sku) {
                canAddGoods = false;
                layer.alert('该商品已在列表，不允许重复添加！');
                return;
            }
        });
        debugger;
        var tbody = $("#thisTimeUpdateTbody");
        var tr = $("<tr  class=\"sampleOrderGoodsClass\">\n" +
            "     <td>\n" +
            "     " + sku + "\n" +
            "       <input type=\"hidden\" name=\"skuNo\" class=\"mySkuFlag\" value=\"" + sku + "\">\n" +
            "     </td>\n" +
            "     <td>" + goodsName + "</td>\n" +
            "     <td>" + brandName + "</td>\n" +
            "     <td>" + model + "</td>\n" +
            "     <td><input type=\"number\" class=\"input-middle\" style=\"width:100%;\" onblur=\"changeOutputNum(this)\" name=\"outputNum\"></td>\n" +
            "<input type=\"hidden\" name=\"unit\" value=\" " + unitName + "\">" +
            "     <td>" + unitName + "</td>\n" +
            "     <td>" + price + "</td>\n" +
            "       <input type=\"hidden\" name=\"price\" class=\"myPriceFlag\" value=\"" + price + "\">\n" +
            "       <input type=\"hidden\" name=\"purchasePrice\" class=\"myPurchasePriceFlag\" value=\"" + purchasePrice + "\">\n" +
            "     <td class=\"totalPriceFlag\">0</td>\n" +
            purchasePriceStr +
            "     <td>普发</td>\n" +
            "     <td>" + availableStockNum + "/" + stockNum + "</td>\n" +
            "     <td><a href=\"#\" onclick=\"deleteRow(this)\">删除</a></td>\n" +
            "     </tr>");

        if (canAddGoods) {
            tbody.append(tr);
        }
    }

    function searchTerminal() {
        checkLogin();
        delWarnTips("errorTxtMsg");

        var canSearchTerminal = true;
        if ($('.mySkuFlag').length > 0) {
            canSearchTerminal = false;
            layer.alert('请先删除商品行数据，再修改客户！')
        }

        var searchTraderName = $("#borrowTraderName").val();

        var searchURL = '/trader/customer/searchCustomerList.do';

        if (canSearchTerminal) {
            $("#traderInfo").find("#terminalDiv").attr('layerParams', '{"width":"800px","height":"300px","title":"客户信息","link":"' + page_url + searchURL + '?traderType=' + $("#traderType").val() + '&callbackFuntion=setTraderInfo&lendOut=2&searchTraderName=' + encodeURI(searchTraderName) + '&indexId=1"}');
            $("#traderInfo").find("#terminalDiv").click();
        }
    }


    function setTraderInfo(traderId, traderName, traderType) {

        //根据客户ID获取联系人列表&地址列表
        $.ajax({
            async: true,
            url: page_url + '/order/saleorder/getCustomerContactAndAddress.do',
            data: {"traderId": traderId, "traderType": traderType},
            type: "POST",
            dataType: "json",
            success: function (data) {

                var addGoodsUrl = '{"width":"700px","height":"480px","title":"添加产品","link":"<%=path%>/wms/sampleOut/addSampleOutGoods.do?callbackFunction=setGoodData&traderId=' + traderId + '"}';
                $('#myGoodsAddDiv').attr('layerParams', addGoodsUrl);


                $("#borrowTraderId").val(traderId);
                $("#borrowTraderName").val(traderName);
                $("#traderType").val(traderType);

                var contactStr = '<option value="0">请选择</option>';
                for (var i = 0; i < data.param.contactList.length; i++) {
                    var isSelected = data.param.contactList[i].isDefault == 1 ? 'selected = "selected"' : '';
                    contactStr += '<option value = "' + data.param.contactList[i].traderContactId + '" valueFlag="' + data.param.contactList[i].name + '/' + data.param.contactList[i].telephone + '/' + data.param.contactList[i].mobile + '" ' + isSelected + '>' + data.param.contactList[i].name + '/' + data.param.contactList[i].telephone + '/' + data.param.contactList[i].mobile + '</option>';
                }
                $("#traderContact").html(contactStr);

                var addressStr = '<option value="0">请选择</option>';
                for (var i = 0; i < data.param.addressList.length; i++) {
                    var isSelected = data.param.addressList[i].traderAddress.isDefault == 1 ? 'selected = "selected"' : '';
                    addressStr += '<option value = "' + data.param.addressList[i].traderAddress.traderAddressId + '" valueFlag="' + data.param.addressList[i].area + '/' + data.param.addressList[i].traderAddress.address + '" ' + isSelected + '>' + data.param.addressList[i].area + '/' + data.param.addressList[i].traderAddress.address + '</option>';
                }
                $("#traderAddress").html(addressStr);
            },
            error: function (data) {
                if (data.status == 1001) {
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    }

    function toAddSampleGoods() {
        checkLogin();
        delWarnTips("errorTxtMsg");

        var canAddSampleGoods = true;
        if ($("#borrowTraderId").val() == '') {
            canAddSampleGoods = false;
            layer.alert('请先选择申请客户，再添加商品！')
        }

        if (canAddSampleGoods) {
            $("#myGoodsAddDiv").click();
        }
    }

    function applyTypeChange() {
        debugger
        if ($('#applyType').val() == 4223) {
            $('.activeLi').css("display", "block");
        } else {
            $('.activeLi').css("display", "none");
        }
    }

    function uploadFile(obj, num) {
        checkLogin();
        var imgPath = $(obj).val();
        if (imgPath == '' || imgPath == undefined) {
            return false;
        }
        var oldName = imgPath.substr(imgPath.lastIndexOf('\\') + 1);
        var domain = $("#domain").val();
        //判断上传文件的后缀名
        var strExtension = imgPath.substr(imgPath.lastIndexOf('.') + 1);
        if (strExtension != 'jpg' && strExtension != 'gif' && strExtension != 'png' && strExtension != 'bmp'
            && strExtension != 'pdf' && strExtension != 'doc' && strExtension != 'docx') {
            layer.alert("文件格式不正确");
            return;
        }
        var fileSize = 0;
        var isIE = /msie/i.test(navigator.userAgent) && !window.opera;
        if (isIE && !obj.files) {
            var filePath = obj.value;
            var fileSystem = new ActiveXObject("Scripting.FileSystemObject");
            var file = fileSystem.GetFile(filePath);
            fileSize = file.Size;
        } else {
            fileSize = obj.files[0].size;
        }
        fileSize = Math.round(fileSize / 1024 * 100) / 100; //单位为KB
        if (fileSize > 5120 * 2) {
            layer.alert("上传附件不得超过10M");
            return false;
        }
        $.ajaxFileUpload({
            url: page_url + '/fileUpload/ajaxFileUpload.do', //用于文件上传的服务器端请求地址
            secureuri: false, //一般设置为false
            fileElementId: $(obj).attr("id"), //文件上传控件的id属性  <input type="file" id="file" name="file" /> 注意，这里一定要有name值   //$("form").serialize(),表单序列化。指把所有元素的ID，NAME 等全部发过去
            dataType: 'json',//返回值类型 一般设置为json
            complete: function () {//只要完成即执行，最后执行
            },
            //服务器成功响应处理函数
            success: function (data) {
                if (data.code == 0) {
                    $("#name_" + num).val(oldName);
                    $("#uri_" + num).val(data.filePath);
                    $("#img_icon_" + num).attr("class", "iconsuccesss ml7").show();
                    $("#img_view_" + num).attr("href", "http://" + data.httpUrl + data.filePath).show();
                    $("#img_del_" + num).show();
                } else {
                    layer.alert(data.message);
                }
            },
            //服务器响应失败处理函数
            error: function (data, status, e) {
                if (data.status == 1001) {
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                } else {
                    layer.alert(data.responseText);
                }

            }
        });
    }


    function changeOutputNum(param){
        var myPriceFlag = Number($(param).parent().parent().find(".myPriceFlag").val()).toFixed(2);
        var outputNum = Number($(param).val()).toFixed(2);
        $(param).parent().parent().find(".totalPriceFlag").html(Number(myPriceFlag * outputNum).toFixed(2));
    }

    function delImg(num) {
        checkLogin();
        index = layer.confirm("您是否确认该操作？", {
            btn: ['确定','取消'] //按钮
        }, function(){
            $("#img_icon_" + num).hide();
            $("#img_view_" + num).hide();
            $("#img_del_" + num).hide();
            $("#uri_" + num).val('');
            $("#domain_" + num).val('');
            $('#name_1').val('');
            layer.close(index);
        }, function(){
        });
    }
    $(function(){
        //联系人下拉搜索初始化
        $(".contact").select2();
        // 联系地址下拉搜索初始化
        $(".address").select2();
    });

</script>

<div class="content">
    <div class="formtitle"></div>
    <div id="desc_div">
        <form method="post" id="addForm"
              action="${pageContext.request.contextPath}/wms/sampleOut/saveSampleOutOrder.do">
            <ul class="payplan">
                <div>
                    <div class="title-container title-container-blue">
                        <div class="table-title nobor">样品申请企业信息</div>
                    </div>


                    <li>
                        <div class="infor_name">
                            <span class="font-red">*</span><label>申请类型:</label>
                        </div>
                        <div class="f_left">
                            <select class="input-middle f_left J-select" name="applyType" id="applyType"
                                    onchange="applyTypeChange();">
                                <option selected="selected" value="0">请选择</option>
                                <c:forEach items="${applyTypeList}" var="applyType">
                                    <option value="${applyType.sysOptionDefinitionId }">${applyType.title}</option>
                                </c:forEach>
                            </select>
                        </div>
                        <div class="f_left">
                            &nbsp;<label id="receiveOutUserOrg" name="receiveOutUserOrg"></label>
                        </div>
                    </li>

                    <li class="activeLi" hidden>
                        <div class="infor_name">
                            <label class="font-red">活动编码:</label>
                        </div>
                        <div class="f_left">
                            <input type="text" placeholder="请填写备案的活动编码" class="input-middle" id="activityCode"
                                   name="activityCode" value=""/>
                        </div>

                        <div class="infor_name">
                            <label class="font-red">活动名称:</label>
                        </div>
                        <div class="f_left">
                            <input type="text" placeholder="请填写备案的活动名称" class="input-middle" id="activityName"
                                   name="activityName" value=""/>
                        </div>
                    </li>


                    <li id="traderInfo">
                        <div class="infor_name">
                            <span class="font-red">*</span><label>样品申请企业:</label>
                        </div>
                        <div class="f_left">

                            <input type="text" placeholder="请填写样品申请企业" class="input-middle"
                                   id="borrowTraderName"
                                   name="borrowTraderName" value=""/>
                            <input type="hidden" id="borrowTraderId" name="borrowTraderId"/>
                            <input type="hidden" id="traderType" name="traderType" value="1"/>

                            <label class="bt-bg-style bg-light-blue bt-small" onclick="searchTerminal();" id="errorMes">搜索</label>

                            <!-- 弹框 -->
                            <span style="display: none;">
                                <div class="title-click nobor  pop-new-data" id="terminalDiv"></div>
                            </span>
                        </div>
                    </li>

                    <li>
                        <div class="infor_name">
                            <span class="font-red">*</span><label>收货联系人:</label>
                        </div>
                        <div class="f_left">
                            <select class="input-xx contact" id="traderContact" name="traderContactId">
                                <option value="0">请选择</option>
                            </select>
                        </div>
                        <input type="hidden" id="traderContactValue" name="traderContact">
                    </li>

                    <Li>
                        <div class="infor_name">
                            <span class="font-red">*</span><label>收货地址:</label>
                        </div>
                        <div class="f_left">
                            <select class="input-xx address" id="traderAddress" name="traderAddressId">
                                <option value="0">请选择</option>
                            </select>
                        </div>
                        <input type="hidden" id="traderAddressValue" name="traderAddress">
                    </Li>


                    <li>
                        <div class="infor_name">
                            <span class="font-red">*</span><label>物流备注:</label>
                        </div>
                        <div class="f_left">
                            <input type="text" placeholder="请填写样品申请的物流需求" class="input-middle"
                                   id="logisticCommnet" name="logisticCommnet" value=""/>
                        </div>
                    </li>

                    <li>
                        <div class="infor_name">
                            <span class="font-red">*</span><label>样品申请原因:</label>
                        </div>
                        <div class="f_left">
                            <textarea name="borrowReason" id="borrowReason"
                                      placeholder="请填写样品申请原因及其他备注事项"
                                      rows="5" class="wid50"></textarea>
                        </div>
                    </li>

                    <li>
                        <div class="form-tips">
                            <lable>附件上传</lable>
                        </div>
                        <input type="hidden" id="domain" name="domain" value="${domain}">
                        <input type="hidden" id="isSaleFlag" value="${isSales}">
                        <div class="f_left ">
                            <div class="form-blanks">
                                <div class="pos_rel f_left">
                                    <input type="file" class="uploadErp" id='file_1' name="lwfile"
                                           onchange="uploadFile(this,1);">
                                    <input type="text" class="input-largest" id="name_1" readonly="readonly"
                                           placeholder="请上传附件" name="fileName" onclick="file_1.click();">
                                    <input type="hidden" id="uri_1" name="fileUri">
                                </div>
                                <label class="bt-bg-style bt-small bg-light-blue" type="file" id="busUpload"
                                       onclick="return $('#file_1').click();">浏览</label>
                                <!-- 上传成功出现 -->
                                <div class="f_left">
                                    <i class="iconsuccesss mt3 none" id="img_icon_1"></i>
                                    <a href="" target="_blank" class="font-blue cursor-pointer  mt3 none"
                                       id="img_view_1">查看</a>
                                    <span class="font-red cursor-pointer  mt3 none" onclick="delImg(1)"
                                          id="img_del_1">删除</span>
                                </div>
                                <div class="font-grey9  mb4">
                                    1、上传文件格式可以是jpg、png、pdf等格式；
                                    <br/>
                                    2、上传文件不要超过10MB；
                                    <div class='clear'></div>
                                </div>
                            </div>
                        </div>
                    </li>


                    <li id="no_receive_user" hidden="hidden">
                        <label><span class="font-red">未选择领用人：请选择领用人</span></label>
                    </li>

                    <li id="no_logistic_comment" hidden="hidden">
                        <label><span class="font-red">未填写物流备注：请填写物流备注！</span></label>
                    </li>
                    <li id="too_long_logistic_comment" hidden="hidden">
                        <label><span class="font-red">长度超过限制：物流备注不允许超过500个字！</span></label>
                    </li>
                </div>

                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">产品信息</div>
                            <div class="title-click nobor " onclick="toAddSampleGoods()">
                                新增
                            </div>
                        </div>

                        <!-- 弹框 -->
                        <span style="display: none;">
                                <div class="title-click nobor  pop-new-data" id="myGoodsAddDiv"></div>
                            </span>

                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                            <tr>
                                <th style="width:80px">订货号</th>
                                <th style="width:80px">产品名称</th>
                                <th style="width:80px">品牌</th>
                                <th style="width:80px">型号</th>
                                <th style="width:80px">数量</th>
                                <th style="width:80px">单位</th>
                                <th style="width:80px">销售价</th>
                                <th style="width:80px">申请总价</th>
                                <c:if test="${isSales eq 0}">
                                    <th style="width:80px">参考成本价</th>
                                </c:if>
                                <th style="width:80px">发货方式</th>
                                <th style="width:80px">可用/库存</th>
                                <th style="width:80px">操作</th>
                            </tr>
                            </thead>
                            <tbody id="thisTimeUpdateTbody">
                            </tbody>
                        </table>
                    </div>
                </li>
                <li id="no_sku" hidden="hidden">
                    <label><span class="font-red">未选择产品：请选择领用产品！</span></label>
                </li>
            </ul>
            <div class="add-tijiao tcenter mt10">
                <button type="button" class="bt-bg-style bg-deep-green" onclick="addSubmit();">提交并审核</button>
                <input type="hidden" id="alreadySubmit" value="0">
            </div>
        </form>
    </div>
</div>
<%@ include file="../../common/footer.jsp" %>
