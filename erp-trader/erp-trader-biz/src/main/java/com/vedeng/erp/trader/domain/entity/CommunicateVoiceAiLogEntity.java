package com.vedeng.erp.trader.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 语音请求日志表
 */
@Getter
@Setter
public class CommunicateVoiceAiLogEntity extends BaseEntity {
    /**
     * 主键
     */
    private Long id;

    /**
     * T_COMMUNICATE_RECORD表的ID
     */
    private Integer communicateRecordId;

    /**
     * 场景
     */
    private String senceCode;

    /**
     * 分组名称
     */
    private String groupCode;

    /**
     * GPT版本
     */
    private String gptVersion;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 请求内容
     */
    private String requestText;

    /**
     * 返回内容
     */
    private String responseText;
}