package com.vedeng.trader.group.buy;

import com.vedeng.trader.group.GoodsBehavior;

import java.util.BitSet;

/**
 * <b>Description:</b>未购买商品<br>
 * <b>Author:calvin</b>
 * <br><b>Date:</b> 2020/5/25
 */
public class UnBuyGoodsBehavior extends BuyBaseBehavior {
    @Override
    public BitSet calculate() {
        BitSet res=getBaseTraderSet();
        BitSet buyset=getBuyGoods();
        res.andNot(buyset);
        return res;
    }
}
