package com.vedeng.erp.trader.dto;

import com.vedeng.common.core.base.BaseDto;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 客户终端审核记录表
 * @date 2023/8/7 10:17
 */
@Getter
@Setter
public class TraderCustomerTerminalAuditRecordDto extends BaseDto {

    /**
     * 主键
     */
    private Integer traderCustomerTerminalAuditRecordId;

    /**
     * 交易者id
     */
    private Integer traderId;

    /**
     * 客户id
     */
    private Integer traderCustomerId;

    /**
     * 终端id
     */
    private Integer traderCustomerTerminalId;

    /**
     * 审核状态
     * 0=审核中
     * 1=审核通过
     * 2=审核不通过
     */
    private Integer auditStatus;

    /**
     * 审核备注
     */
    private String comments;

    /**
     * 审核人id
     */
    private Integer auditUserId;

    /**
     * 审核人
     */
    private String auditUsername;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 是否删除 （0否1是）
     */
    private Integer isDeleted;
}