<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.trader.dao.TraderCustomerMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.trader.model.TraderCustomer">
        <id column="TRADER_CUSTOMER_ID" property="traderCustomerId" jdbcType="INTEGER"/>
        <result column="TRADER_ID" property="traderId" jdbcType="INTEGER"/>
        <result column="TRADER_CUSTOMER_CATEGORY_ID" property="traderCustomerCategoryId" jdbcType="INTEGER"/>
        <result column="CUSTOMER_TYPE" property="customerType" jdbcType="INTEGER"/>
        <result column="CUSTOMER_NATURE" property="customerNature" jdbcType="INTEGER"/>
        <result column="IS_ENABLE" property="isEnable" jdbcType="BIT"/>
        <result column="IS_TOP" property="isTop" jdbcType="BIT"/>
        <result column="AMOUNT" property="amount" jdbcType="DECIMAL"/>
        <result column="PERIOD_AMOUNT" property="periodAmount" jdbcType="DECIMAL"/>
        <result column="PERIOD_DAY" property="periodDay" jdbcType="INTEGER"/>
        <result column="DISABLE_TIME" property="disableTime" jdbcType="BIGINT"/>
        <result column="CUSTOMER_FROM" property="customerFrom" jdbcType="INTEGER"/>
        <result column="FIRST_ENQUIRY_TYPE" property="firstEnquiryType" jdbcType="INTEGER"/>
        <result column="CUSTOMER_LEVEL" property="customerLevel" jdbcType="INTEGER"/>
        <result column="USER_EVALUATE" property="userEvaluate" jdbcType="INTEGER"/>
        <result column="CUSTOMER_SCORE" property="customerScore" jdbcType="INTEGER"/>
        <result column="BASIC_MEDICAL_DEALER" property="basicMedicalDealer" jdbcType="BIT"/>
        <result column="IS_VIP" property="isVip" jdbcType="BIT"/>
        <result column="OWNERSHIP" property="ownership" jdbcType="INTEGER"/>
        <result column="MEDICAL_TYPE" property="medicalType" jdbcType="INTEGER"/>
        <result column="HOSPITAL_LEVEL" property="hospitalLevel" jdbcType="INTEGER"/>
        <result column="EMPLOYEES" property="employees" jdbcType="INTEGER"/>
        <result column="ANNUAL_SALES" property="annualSales" jdbcType="INTEGER"/>
        <result column="SALES_MODEL" property="salesModel" jdbcType="INTEGER"/>
        <result column="REGISTERED_CAPITAL" property="registeredCapital" jdbcType="DECIMAL"/>
        <result column="REGISTERED_DATE" property="registeredDate" jdbcType="DATE"/>
        <result column="DIRECT_SELLING" property="directSelling" jdbcType="DECIMAL"/>
        <result column="WHOLESALE" property="wholesale" jdbcType="DECIMAL"/>
        <result column="DISABLE_REASON" property="disableReason" jdbcType="VARCHAR"/>
        <result column="COMMENTS" property="comments" jdbcType="VARCHAR"/>
        <result column="FINANCE_COMMENTS" property="financeComments" jdbcType="VARCHAR"/>
        <result column="LOGISTICS_COMMENTS" property="logisticsComments" jdbcType="VARCHAR"/>
        <result column="BRIEF" property="brief" jdbcType="VARCHAR"/>
        <result column="HISTORY" property="history" jdbcType="VARCHAR"/>
        <result column="BUSINESS_CONDITION" property="businessCondition" jdbcType="VARCHAR"/>
        <result column="BUSINESS_PLAN" property="businessPlan" jdbcType="VARCHAR"/>
        <result column="ADVANTAGE" property="advantage" jdbcType="VARCHAR"/>
        <result column="PRIMAL_PROBLEM" property="primalProblem" jdbcType="VARCHAR"/>
        <result column="ORGANIZATION_CODE" property="orgNumber" jdbcType="VARCHAR"/>
        <result column="UNIFIED_SOCIAL_CREDIT_IDENTIFIER" property="creditCode" jdbcType="VARCHAR"/>
        <result column="BUSINESS_SCOPE" property="businessScope" jdbcType="VARCHAR"/>
        <result column="HISTORY_NAME" property="historyNames" jdbcType="VARCHAR"/>
        <result column="ADD_TIME" property="addTime" jdbcType="BIGINT"/>
        <result column="CREATOR" property="creator" jdbcType="INTEGER"/>
        <result column="IS_PROFIT" property="isProfit" jdbcType="INTEGER"/>
        <result column="IS_LIMITPRICE" property="isLimitprice" jdbcType="INTEGER"/>
        <result column="MOD_TIME" property="modTime" jdbcType="BIGINT"/>
        <result column="UPDATER" property="updater" jdbcType="INTEGER"/>
        <result column="MN_CODE" property="meiNianCode" jdbcType="VARCHAR"/>
        <result column="TRADER_LEVEL" property="traderLevel" jdbcType="INTEGER"/>
        <result column="SOURCE" property="source" jdbcType="INTEGER"/>
        <result column="BELONG_PLATFORM" property="belongPlatform" jdbcType="INTEGER"/>

    </resultMap>

    <resultMap type="com.vedeng.trader.model.vo.TraderCustomerVo" id="VoBaseResultMap" extends="BaseResultMap">
        <result column="TRADER_NAME" property="name" jdbcType="VARCHAR" />
        <result column="CONTACT_NAME" property="contactName" jdbcType="VARCHAR" />
        <result column="STATUS" property="customerStatus" jdbcType="INTEGER" />
        <result column="EFFECTIVENESS" property="effectiveness" jdbcType="INTEGER" />
        <result column="APTITUDE_STATUS" property="aptitudeStatus" jdbcType="INTEGER" />
        <result column="CUSTOMER_BUY_COUNT" property="buyCount" jdbcType="INTEGER" />
        <result column="CUSTOMER_QUOTE_COUNT" property="quoteCount" jdbcType="INTEGER" />
        <result column="CUSTOMER_BUY_MONEY" property="buyMoney" jdbcType="DECIMAL" />
        <result column="AREA_ID" property="areaId" jdbcType="INTEGER"/>
        <result column="AREA_IDS" property="areaIds" jdbcType="VARCHAR"/>
        <result column="QUOTE_COUNT" property="quoteCount" jdbcType="INTEGER" />
        <result column="COMMUNCATE_COUNT" property="communcateCount" jdbcType="INTEGER" />
        <result column="BUSSINESS_CHANCE_COUNT" property="bussinessChanceCount" jdbcType="INTEGER" />
        <result column="IS_PROFIT" property="isProfit" jdbcType="INTEGER" />
        <result column="LAST_BUSSINESS_TIME" property="lastBussinessTime" jdbcType="BIGINT" />
        <result column="FIRST_BUSSINESS_TIME" property="firstBussinessTime" jdbcType="BIGINT" />

        <result column="USED_PERIOD_AMOUNT" property="usedPeriodAmount" jdbcType="DECIMAL" />
        <result column="USED_TIMES" property="usedTimes" jdbcType="INTEGER"/>
        <result column="CAPITAL_BILL_ID" property="capitalBillId" jdbcType="INTEGER" />
        <result column="TRADER_MODE" property="traderMode" jdbcType="INTEGER" />
        <result column="ORDER_PERIOD_AMOUNT" property="orderPeriodAmount" jdbcType="DECIMAL" />
        <result column="ORDER_PAYMENT_STATUS" property="orderPaymentStatus" jdbcType="INTEGER" />
        <result column="SALEORDER_ID" property="saleorderId" jdbcType="INTEGER" />

        <result column="TRADER_CONTACT_NAME" property="traderContactName" jdbcType="VARCHAR" />
        <result column="TRADER_CONTACT_MOBILE" property="traderContactMobile" jdbcType="VARCHAR" />
        <result column="LAST_VERIFY_USERNAME" property="lastVerifyUsermae" jdbcType="VARCHAR" />
        <result column="VERIFY_USERNAME" property="verifyUsername" jdbcType="VARCHAR" />
        <result column="VERIFY_STATUS" property="verifyStatus" jdbcType="INTEGER" />

        <result column="CUSTOMER_TYPE_STR" property="customerTypeStr" jdbcType="VARCHAR" />
        <result column="INVALID_REASON" property="invalidReason" jdbcType="VARCHAR" />
        <result column="CUSTOMER_NATURE_STR" property="customerNatureStr" jdbcType="VARCHAR" />
        <result column="CUSTOMER_CATEGORY" property="customerCategory" jdbcType="INTEGER" />
        <result column="USER_ID" property="userId" jdbcType="INTEGER" />
        <result column="SOURCE" property="source" jdbcType="INTEGER" />
        <result column="ASSOCIATED_CUSTOMER_GROUP" property="associatedCustomerGroup" jdbcType="BIGINT" />
        <result column="IS_PRIVATIZED" property="isPrivatized" jdbcType="INTEGER" />
        <result column="PUBLIC_CUSTOMER_RECORD_ID" property="publicCustomerRecordId" jdbcType="INTEGER" />
        <result column="LAST_COMMUNICATE_TIME" property="lastCommuncateTime" jdbcType="BIGINT" />
        <result column="ORIGIN_USER_ID" property="originUserId" jdbcType="INTEGER" />
        <result column="LOCK_USER_NAME" property="lockUserName" jdbcType="VARCHAR" />
    </resultMap>
    <sql id="Base_Column_List">
        TRADER_CUSTOMER_ID, TRADER_ID, TRADER_CUSTOMER_CATEGORY_ID, CUSTOMER_TYPE, CUSTOMER_NATURE, IS_ENABLE, IS_TOP, AMOUNT,
        PERIOD_AMOUNT, PERIOD_DAY, DISABLE_TIME, CUSTOMER_FROM, FIRST_ENQUIRY_TYPE, CUSTOMER_LEVEL,
        USER_EVALUATE, CUSTOMER_SCORE, BASIC_MEDICAL_DEALER, IS_VIP, OWNERSHIP, MEDICAL_TYPE,
        HOSPITAL_LEVEL, EMPLOYEES, ANNUAL_SALES, SALES_MODEL, REGISTERED_CAPITAL, REGISTERED_DATE,
        DIRECT_SELLING, WHOLESALE, DISABLE_REASON, COMMENTS, FINANCE_COMMENTS, LOGISTICS_COMMENTS,
        BRIEF, HISTORY, BUSINESS_CONDITION, BUSINESS_PLAN, ADVANTAGE, PRIMAL_PROBLEM, ADD_TIME, HISTORY_NAME,BUSINESS_SCOPE,
        CREATOR, MOD_TIME, UPDATER,ASSOCIATED_CUSTOMER_GROUP
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        T_TRADER_CUSTOMER
        WHERE
        TRADER_CUSTOMER_ID = #{traderId,jdbcType=INTEGER}
    </select>

    <update id="updateCustomerIsProfit">
        update T_TRADER_CUSTOMER SET IS_PROFIT=#{isProfit}
        WHERE TRADER_ID=#{traderId}
    </update>

    <select id="getTraderCustomerIdsListPage" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT t.TRADER_ID,t.TRADER_CUSTOMER_ID FROM T_TRADER_CUSTOMER t LEFT JOIN T_VERIFIES_INFO v ON v.RELATE_TABLE_KEY=t.TRADER_CUSTOMER_ID AND v.relate_table='T_CUSTOMER_APTITUDE'
        WHERE 1=1
        <if test="trader.customerNature!=null">
            AND t.CUSTOMER_NATURE=#{trader.customerNature}
        </if>
        AND v.STATUS IS NULL
    </select>

    <select id="getTraderCustomerListPage" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT t.TRADER_ID,t.TRADER_CUSTOMER_ID,tr.SOURCE FROM T_TRADER_CUSTOMER t LEFT JOIN T_TRADER tr on tr.TRADER_ID=t.TRADER_ID
        WHERE 1=1 and t.IS_ENABLE=1
    </select>


    <select id="getTraderCustomerByOrganizationsListPage" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT t.TRADER_ID,t.TRADER_CUSTOMER_ID,tr.SOURCE
        FROM T_TRADER_CUSTOMER t
        LEFT JOIN T_TRADER tr ON tr.TRADER_ID = t.TRADER_ID
        LEFT JOIN T_R_TRADER_J_USER B ON tr.TRADER_ID = B.TRADER_ID
        LEFT JOIN T_USER C ON B.USER_ID = C.USER_ID
        LEFT JOIN T_R_USER_POSIT D ON C.USER_ID = D.USER_ID
        LEFT JOIN T_POSITION E ON D.POSITION_ID = E.POSITION_ID
        LEFT JOIN T_ORGANIZATION F ON E.ORG_ID = F.ORG_ID
        WHERE
        1 = 1
        AND t.IS_ENABLE = 1
        AND F.ORG_ID IN
        <foreach collection="organizations" separator="," open="(" close=")" item="i">
            #{i.orgId}
        </foreach>
    </select>

    <select id="getYXGCustomerListPage" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT t.TRADER_ID,t.TRADER_CUSTOMER_ID,tr.SOURCE FROM T_TRADER_CUSTOMER t LEFT JOIN T_TRADER tr on tr.TRADER_ID=t.TRADER_ID
        WHERE 1=1 and t.IS_ENABLE=1 AND tr.BELONG_PLATFORM=2
    </select>

    <select id="getTraderCustomerByTraderId" resultMap="BaseResultMap">
        SELECT t.TRADER_ID,t.TRADER_CUSTOMER_ID,tr.SOURCE FROM T_TRADER_CUSTOMER t LEFT JOIN T_TRADER tr on tr.TRADER_ID=t.TRADER_ID
        WHERE 1=1 and  t.TRADER_ID=#{traderId}
    </select>

    <select id="getBaseCustomerByTraderId" resultMap="BaseResultMap">
        SELECT t.TRADER_ID,t.TRADER_CUSTOMER_ID,t.CUSTOMER_NATURE,t.IS_PROFIT, t.ASSOCIATED_CUSTOMER_GROUP FROM T_TRADER_CUSTOMER t
        WHERE 1=1 and  t.TRADER_ID=#{traderId}
    </select>

    <update id="updateCustomerCategoryById" parameterType="com.vedeng.trader.model.TraderCustomer">
        update T_TRADER_CUSTOMER SET CUSTOMER_CATEGORY=#{customerCategory} WHERE TRADER_CUSTOMER_ID=#{traderCustomerId}
    </update>

    <update id="updateCustomerLevelById" parameterType="com.vedeng.trader.model.TraderCustomer">
        update T_TRADER_CUSTOMER SET CUSTOMER_LEVEL=#{customerLevel} WHERE TRADER_CUSTOMER_ID=#{traderCustomerId}
    </update>
    <update id="updateTraderCustomerAmount">
        UPDATE T_TRADER_CUSTOMER A
        SET A.AMOUNT = A.AMOUNT + #{amount,jdbcType=DECIMAL},
            A.UPDATER = #{updater,jdbcType=INTEGER},
            A.MOD_TIME = #{modTime,jdbcType=BIGINT}
        WHERE A.TRADER_ID = #{traderId,jdbcType=INTEGER}
    </update>

    <select id="getCustomerInfo" resultMap="VoBaseResultMap" parameterType="java.lang.Integer">
	SELECT B.TRADER_NAME,
	       B.AREA_ID,
           B.BELONG_PLATFORM,
	       A.TRADER_CUSTOMER_CATEGORY_ID,
	       A.CUSTOMER_TYPE,
	       A.CUSTOMER_NATURE,
	       A.TRADER_CUSTOMER_ID,
	       A.TRADER_ID,
	       A.COMMENTS,
	       B.AREA_IDS,
	       A.IS_ENABLE,
	       A.CUSTOMER_LEVEL,
	       A.PERIOD_DAY,
	       A.AMOUNT,
	       A.OWNERSHIP
	FROM T_TRADER_CUSTOMER A LEFT JOIN T_TRADER B ON A.TRADER_ID = B.TRADER_ID
  	WHERE
  		A.TRADER_ID = #{traderId,jdbcType=INTEGER}
  </select>


    <select id="getCustomerByTraderName" resultMap="VoBaseResultMap" parameterType="java.lang.Integer">
        SELECT B.TRADER_NAME,
               B.AREA_ID,
               A.TRADER_CUSTOMER_CATEGORY_ID,
               A.CUSTOMER_TYPE,
               A.CUSTOMER_NATURE,
               A.TRADER_CUSTOMER_ID,
               A.TRADER_ID,
               A.COMMENTS,
               B.AREA_IDS,
               A.IS_ENABLE,
               A.CUSTOMER_LEVEL,
               A.PERIOD_DAY,
               A.AMOUNT,
               A.OWNERSHIP
        FROM T_TRADER_CUSTOMER A LEFT JOIN T_TRADER B ON A.TRADER_ID = B.TRADER_ID
        WHERE
            B.TRADER_NAME = #{traderName,jdbcType=INTEGER}
    </select>
    
    <select id="getTraderCustomerById" resultMap="BaseResultMap">
        select * from T_TRADER_CUSTOMER where TRADER_ID=#{traderId}
    </select>
    <select id="getTraderCustomerByPayApply" resultType="com.vedeng.trader.model.TraderCustomer">
        SELECT
            TC.*
        FROM
            T_PAY_APPLY PA
                LEFT JOIN T_AFTER_SALES A ON PA.RELATED_ID = A.AFTER_SALES_ID
                LEFT JOIN T_SALEORDER S ON A.ORDER_ID = S.SALEORDER_ID
                LEFT JOIN T_TRADER_CUSTOMER TC ON S.TRADER_ID = TC.TRADER_ID
        WHERE
            PA.PAY_APPLY_ID = #{payApplyId,jdbcType=INTEGER};
    </select>


    <select id="getTraderCustomerIsMember" resultType="integer">
        SELECT
            A.TRADER_CUSTOMER_ID,B.TRADER_NAME
        FROM
            T_TRADER_CUSTOMER A
                JOIN T_TRADER B ON A.TRADER_ID = B.TRADER_ID
                JOIN T_VERIFIES_INFO C ON A.TRADER_CUSTOMER_ID = C.RELATE_TABLE_KEY
                LEFT JOIN T_WEB_ACCOUNT D ON A.TRADER_ID=D.TRADER_ID
        WHERE
            A. CUSTOMER_NATURE = 465
          AND C.RELATE_TABLE='T_CUSTOMER_APTITUDE'
          AND C.`STATUS` in(0,1,5)
          AND B.BELONG_PLATFORM=1
          AND A.IS_VEDENG_MEMBER!=1 AND D.WEB_ACCOUNT_ID is not null
    </select>

    <select id="getTraderCustomerIsMemberForAssign" resultType="integer">
        SELECT
            A.TRADER_CUSTOMER_ID,B.TRADER_NAME
        FROM
            T_TRADER_CUSTOMER A
                JOIN T_TRADER B ON A.TRADER_ID = B.TRADER_ID
                JOIN T_VERIFIES_INFO C ON A.TRADER_CUSTOMER_ID = C.RELATE_TABLE_KEY
                LEFT JOIN T_WEB_ACCOUNT D ON A.TRADER_ID=D.TRADER_ID
        WHERE
            A. CUSTOMER_NATURE = 465
          AND C.RELATE_TABLE='T_CUSTOMER_APTITUDE'
          AND C.`STATUS` in(0,1,5)
          AND B.BELONG_PLATFORM=1
          AND A.IS_VEDENG_MEMBER!=1 AND D.WEB_ACCOUNT_ID is not null
            and   B.TRADER_ID IN
            <foreach collection="traderIds" item="traderId" index="index" open="(" separator="," close=")">
                #{traderId}
            </foreach>
    </select>

    <update id="updateIsVedengMember">
        UPDATE T_TRADER_CUSTOMER
        SET IS_VEDENG_MEMBER = #{status}
        WHERE
        TRADER_CUSTOMER_ID IN
        <foreach collection="list" index="index" open="(" close=")" separator="," item="id">
            #{id,jdbcType=INTEGER}
        </foreach>
    </update>

    <select id="getTraderByTraderName" resultType="com.vedeng.trader.model.vo.TraderCustomerVo">
        SELECT A.TRADER_ID,
        B.TRADER_NAME,
        A.IS_ENABLE
        FROM T_TRADER_CUSTOMER A
        INNER JOIN T_TRADER B ON A.TRADER_ID = B.TRADER_ID
        WHERE     B.TRADER_NAME IN
        <foreach collection="traderList" item="list" separator="," open="(" close=")">
            #{list.traderName,jdbcType=VARCHAR}
        </foreach>
        AND B.COMPANY_ID = #{companyId,jdbcType=INTEGER}
    </select>

    <update id="updatePriceLimtList" parameterType="java.util.ArrayList">
        UPDATE T_TRADER_CUSTOMER A
        SET A.IS_LIMITPRICE = 1,MOD_TIME = NOW()
        WHERE A.TRADER_ID IN
        <foreach collection="traderCustomerList" item="traderCustomer" open="(" close=")" separator=",">
            #{traderCustomer.traderId,jdbcType=INTEGER}
        </foreach>
    </update>

    <update id="delPriceLimitTrader" parameterType="com.vedeng.trader.model.vo.TraderCustomerVo">
        UPDATE T_TRADER_CUSTOMER A
        SET A.IS_LIMITPRICE = 0,
        A.UPDATER = #{traderCustomer.updater,jdbcType=INTEGER},
        A.MOD_TIME = #{traderCustomer.modTime,jdbcType=BIGINT}
        WHERE A.TRADER_CUSTOMER_ID IN
        <foreach collection="traderCustomer.traderList" separator="," open="(" item="traderCustomerId" close=")">
            #{traderCustomerId,jdbcType=INTEGER}
        </foreach>
    </update>

    <select id="getTraderCustomerByTraderName" resultMap="VoBaseResultMap" parameterType="java.lang.String">
        SELECT B.TRADER_NAME,
               B.AREA_ID,
               A.TRADER_CUSTOMER_CATEGORY_ID,
               A.CUSTOMER_TYPE,
               A.CUSTOMER_NATURE,
               A.TRADER_CUSTOMER_ID,
               A.TRADER_ID,
               A.COMMENTS,
               B.AREA_IDS,
               A.IS_ENABLE,
               A.CUSTOMER_LEVEL,
               A.PERIOD_DAY,
               A.AMOUNT,
               A.OWNERSHIP,
               B.BELONG_PLATFORM
        FROM T_TRADER_CUSTOMER A LEFT JOIN T_TRADER B ON A.TRADER_ID = B.TRADER_ID
        WHERE
            B.TRADER_NAME = #{traderName,jdbcType=VARCHAR} AND B.COMPANY_ID = 1
	    ORDER BY B.TRADER_ID DESC
	    LIMIT 1
  </select>


    <select id="searchCustomerListPage" parameterType="java.util.Map" resultType="com.vedeng.trader.model.vo.TraderCustomerVo">
        SELECT
            A.*,
            B.AREA_ID,
            B.TRADER_NAME,
            B.AREA_IDS,
            C.TELEPHONE TRADER_CONTACT_TELEPHONE,
            C.MOBILE TRADER_CONTACTMOBILE,
            C.MOBILE2 TRADER_CONTACT_MOBILE2,
            C.NAME
        FROM T_TRADER_CUSTOMER A
        LEFT JOIN T_TRADER B ON A.TRADER_ID = B.TRADER_ID
        LEFT JOIN T_TRADER_CONTACT C ON  B.TRADER_ID = C.TRADER_ID
        AND C.IS_ENABLE = 1 AND  C.IS_DEFAULT = 1
        WHERE 1
        <if test="traderCustomerVo.searchTraderName!=null and traderCustomerVo.searchTraderName!=''">
            AND B.TRADER_NAME LIKE CONCAT('%',#{traderCustomerVo.searchTraderName},'%' )
        </if>

            AND B.COMPANY_ID =1

        <if test="traderCustomerVo.traderId != null and traderCustomerVo.traderId != 0">
            AND B.TRADER_ID = #{traderCustomerVo.traderId}
        </if>
    </select>

    <select id="getTraderIdsByBaseAttributesListPage" parameterType="map" resultType="java.lang.Integer">
        SELECT DISTINCT T.TRADER_ID FROM T_TRADER T LEFT JOIN T_TRADER_CUSTOMER C ON T.TRADER_ID=C.TRADER_ID
        <if test="q.brands!=null">
            LEFT JOIN T_TRADER_CUSTOMER_BUSSINESS_BRAND B ON C.TRADER_CUSTOMER_ID=B.TRADER_CUSTOMER_ID
        </if>
        <if test="q.departments!=null">
            LEFT JOIN T_TRADER_CUSTOMER_ATTRIBUTE A1 ON A1.TRADER_CUSTOMER_ID=C.TRADER_CUSTOMER_ID AND A1.ATTRIBUTE_CATEGORY_ID=17
        </if>
         <if test="q.products!=null">
            LEFT JOIN T_TRADER_CUSTOMER_ATTRIBUTE A2 ON A2.TRADER_CUSTOMER_ID=C.TRADER_CUSTOMER_ID AND A2.ATTRIBUTE_CATEGORY_ID=18
        </if>
        <if test="q.productTypes!=null">
            LEFT JOIN T_TRADER_CUSTOMER_ATTRIBUTE A3 ON A3.TRADER_CUSTOMER_ID=C.TRADER_CUSTOMER_ID AND A3.ATTRIBUTE_CATEGORY_ID in (6,7)
        </if>
        <if test="q.customerTypes!=null">
            LEFT JOIN T_TRADER_CUSTOMER_ATTRIBUTE A4 ON A4.TRADER_CUSTOMER_ID=C.TRADER_CUSTOMER_ID AND A4.ATTRIBUTE_CATEGORY_ID in (4,5)
        </if>
        <if test="(q.customerGradeIds != null and q.customerGradeIds.size() > 0) or (q.customerTripIds != null and q.customerTripIds.size() > 0)">
            LEFT JOIN DWH_TRADER_FEATURE_DF DWH ON DWH.TRADER_ID = T.TRADER_ID
        </if>
        where C.IS_ENABLE=1 AND T.BELONG_PLATFORM = #{q.belongPlatform}
        <if test="q.area !=null and q.area!=''">
            AND find_in_set(#{q.area},T.AREA_IDS)
        </if>
        <if test="q.brands!=null">
            AND B.BRAND_ID IN
            <foreach collection="q.brands" separator="," open="(" close=")" item="id">
            #{id}
            </foreach>
        </if>
         <if test="q.departments!=null">
            AND A1.ATTRIBUTE_ID IN
            <foreach collection="q.departments" separator="," open="(" close=")" item="id">
            #{id}
            </foreach>
        </if>
         <if test="q.products!=null">
            AND A2.ATTRIBUTE_ID IN
            <foreach collection="q.products" separator="," open="(" close=")" item="id">
            #{id}
            </foreach>
        </if>
        <if test="q.productTypes!=null">
            AND A3.ATTRIBUTE_ID IN
            <foreach collection="q.productTypes" separator="," open="(" close=")" item="id">
            #{id}
            </foreach>
        </if>
        <if test="q.customerTypes!=null">
            AND A4.ATTRIBUTE_ID IN
            <foreach collection="q.customerTypes" separator="," open="(" close=")" item="id">
            #{id}
            </foreach>
        </if>
        <if test="q.customerGradeIds != null and q.customerGradeIds.size() > 0">
            AND DWH.TRADER_LEVEL_STATUS IN
            <foreach collection="q.customerGradeIds" separator="," open="(" close=")" item = "id">
                #{id}
            </foreach>
        </if>

        <if test="q.customerTripIds != null and q.customerTripIds.size() > 0">
            AND DWH.TRADER_JOURNEY_STATUS IN
            <foreach collection="q.customerTripIds" separator="," open="(" close=")" item = "id">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="getTraderByTraderCustomerId" resultType="com.vedeng.trader.model.Trader">
        select T.* from T_TRADER_CUSTOMER C LEFT JOIN T_TRADER T ON C.TRADER_ID=T.TRADER_ID
        WHERE C.TRADER_CUSTOMER_ID=#{traderCustomerId}
    </select>

    <update id="updateCategoryIdAndVipPrice" parameterType="com.vedeng.trader.model.TraderCustomer">
        update T_TRADER_CUSTOMER SET TRADER_CUSTOMER_CATEGORY_ID=#{traderCustomerCategoryId},IS_SHOW_VIP_PRICE=#{isShowVipPrice}
        where TRADER_CUSTOMER_ID=#{traderCustomerId}
    </update>
    <update id="update" parameterType="com.vedeng.trader.model.TraderCustomer">
        update T_TRADER_CUSTOMER
        <set >
            <if test="traderCustomerCategoryId != null" >
                TRADER_CUSTOMER_CATEGORY_ID = #{traderCustomerCategoryId,jdbcType=INTEGER},
            </if>
            <if test="customerType != null" >
                CUSTOMER_TYPE = #{customerType,jdbcType=INTEGER},
            </if>
            <if test="customerNature != null" >
                CUSTOMER_NATURE = #{customerNature,jdbcType=INTEGER},
            </if>
            <if test="isEnable != null" >
                IS_ENABLE = #{isEnable,jdbcType=BIT},
            </if>
            <if test="isTop != null" >
                IS_TOP = #{isTop,jdbcType=BIT},
            </if>
            <if test="amount != null" >
                AMOUNT = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="periodAmount != null" >
                PERIOD_AMOUNT = #{periodAmount,jdbcType=DECIMAL},
            </if>
            <if test="periodDay != null" >
                PERIOD_DAY = #{periodDay,jdbcType=INTEGER},
            </if>
            <if test="disableTime != null" >
                DISABLE_TIME = #{disableTime,jdbcType=BIGINT},
            </if>
            <if test="customerFrom != null" >
                CUSTOMER_FROM = #{customerFrom,jdbcType=INTEGER},
            </if>
            <if test="firstEnquiryType != null" >
                FIRST_ENQUIRY_TYPE = #{firstEnquiryType,jdbcType=INTEGER},
            </if>
            <if test="customerLevel != null" >
                CUSTOMER_LEVEL = #{customerLevel,jdbcType=INTEGER},
            </if>
            <if test="userEvaluate != null" >
                USER_EVALUATE = #{userEvaluate,jdbcType=INTEGER},
            </if>
            <if test="customerScore != null" >
                CUSTOMER_SCORE = #{customerScore,jdbcType=INTEGER},
            </if>
            <if test="basicMedicalDealer != null" >
                BASIC_MEDICAL_DEALER = #{basicMedicalDealer,jdbcType=BIT},
            </if>
            <if test="isVip != null" >
                IS_VIP = #{isVip,jdbcType=BIT},
            </if>
            <if test="ownership != null" >
                OWNERSHIP = #{ownership,jdbcType=INTEGER},
            </if>
            <if test="medicalType != null" >
                MEDICAL_TYPE = #{medicalType,jdbcType=INTEGER},
            </if>
            <if test="hospitalLevel != null" >
                HOSPITAL_LEVEL = #{hospitalLevel,jdbcType=INTEGER},
            </if>
            <if test="employees != null" >
                EMPLOYEES = #{employees,jdbcType=INTEGER},
            </if>
            <if test="annualSales != null" >
                ANNUAL_SALES = #{annualSales,jdbcType=INTEGER},
            </if>
            <if test="salesModel != null" >
                SALES_MODEL = #{salesModel,jdbcType=INTEGER},
            </if>
            <if test="registeredCapital != null" >
                REGISTERED_CAPITAL = #{registeredCapital,jdbcType=DECIMAL},
            </if>

            <if test="registeredDate != null" >
                REGISTERED_DATE = #{registeredDate,jdbcType=DATE},
            </if>
            <if test="directSelling != null" >
                DIRECT_SELLING = #{directSelling,jdbcType=DECIMAL},
            </if>
            <if test="wholesale != null" >
                WHOLESALE = #{wholesale,jdbcType=DECIMAL},
            </if>
            <if test="disableReason != null" >
                DISABLE_REASON = #{disableReason,jdbcType=VARCHAR},
            </if>
            <if test="comments != null" >
                COMMENTS = #{comments,jdbcType=VARCHAR},
            </if>
            <if test="financeComments != null" >
                FINANCE_COMMENTS = #{financeComments,jdbcType=VARCHAR},
            </if>
            <if test="logisticsComments != null" >
                LOGISTICS_COMMENTS = #{logisticsComments,jdbcType=VARCHAR},
            </if>
            <if test="brief != null" >
                BRIEF = #{brief,jdbcType=VARCHAR},
            </if>
            <if test="history != null" >
                HISTORY = #{history,jdbcType=VARCHAR},
            </if>
            <if test="businessCondition != null" >
                BUSINESS_CONDITION = #{businessCondition,jdbcType=VARCHAR},
            </if>
            <if test="businessPlan != null" >
                BUSINESS_PLAN = #{businessPlan,jdbcType=VARCHAR},
            </if>
            <if test="advantage != null" >
                ADVANTAGE = #{advantage,jdbcType=VARCHAR},
            </if>
            <if test="primalProblem != null" >
                PRIMAL_PROBLEM = #{primalProblem,jdbcType=VARCHAR},
            </if>
            <if test="modTime != null" >
                MOD_TIME = #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null" >
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="orgNumber != null" >
                ORGANIZATION_CODE = #{orgNumber,jdbcType=VARCHAR},
            </if>
            <if test="creditCode != null" >
                UNIFIED_SOCIAL_CREDIT_IDENTIFIER = #{creditCode,jdbcType=VARCHAR},
            </if>
            <if test="businessScope != null" >
                BUSINESS_SCOPE = #{businessScope,jdbcType=VARCHAR},
            </if>
            <if test="historyNames != null" >
                HISTORY_NAME = #{historyNames,jdbcType=VARCHAR},
            </if>
            <if test="isProfit != null" >
                IS_PROFIT = #{isProfit,jdbcType=INTEGER},
            </if>
        </set>
        where
        1=1

            and TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER}

        <if test="traderId != null and traderId > 0">
            and TRADER_ID = #{traderId,jdbcType=INTEGER}
        </if>

    </update>
    <update id="updateHasQuoted">
        update T_TRADER_CUSTOMER c inner join T_QUOTEORDER q  on c.TRADER_ID = q.TRADER_ID
        set c.HAS_QUOTED = 1
        where c.HAS_QUOTED = 0

            and c.TRADER_ID in

            <foreach collection="countToUpdate" open="(" close=")" index="index" separator="," item="traderId">
                #{traderId,jdbcType=INTEGER}
            </foreach>


    </update>
    <update id="updateIsCooperated">
        update T_TRADER_CUSTOMER c inner join (select distinct a.TRADER_ID from T_SALEORDER a
         join T_TRADER t on t.TRADER_ID = a.TRADER_ID
         where a.VALID_STATUS = 1 and a.STATUS != 3 and t.COMPANY_ID = 1) b on c.TRADER_ID = b.TRADER_ID
         set c.IS_COOPERATED=1
         where c.IS_COOPERATED = 0

            and c.TRADER_ID in
            <foreach collection="countToUpdate" open="(" close=")" separator="," item="traderId">
                #{traderId,jdbcType=INTEGER}
            </foreach>


    </update>
    <select id="getCustomerNatureBySaleorderId" resultType="java.lang.Integer">
        SELECT TC.CUSTOMER_NATURE FROM T_TRADER_CUSTOMER TC JOIN T_SALEORDER S ON TC.TRADER_ID = S.TRADER_ID
        WHERE S.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
    </select>
    <select id="getTraderCertificateCheckStatusByTraderId" resultType="java.lang.Integer">
        SELECT IFNULL(V.STATUS,3) STATUS
        FROM T_TRADER_CUSTOMER C LEFT JOIN T_VERIFIES_INFO V
            ON V.RELATE_TABLE = 'T_CUSTOMER_APTITUDE' AND C.TRADER_CUSTOMER_ID = V.RELATE_TABLE_KEY
        WHERE C.TRADER_ID = #{traderId,jdbcType=INTEGER}
    </select>
    <select id="listGroupCustomer" parameterType="map" resultType="com.vedeng.trader.model.vo.TraderCustomerVo">
        SELECT
            A.*,
            B.AREA_ID,
            B.TRADER_NAME,
            B.AREA_IDS
        FROM T_TRADER_CUSTOMER A
        INNER JOIN T_TRADER B ON A.TRADER_ID = B.TRADER_ID
        WHERE
           A.IS_ENABLE=1
        <choose>
            <when test="selfOrChild == 1">
                and B.TRADER_ID = #{traderId,jdbcType=INTEGER}
            </when>
            <otherwise>
                and B.PARENT_ID = #{traderId,jdbcType=INTEGER}
            </otherwise>
        </choose>
limit 1000
    </select>

    <select id="filterTraderIdsByCoverData" resultType="java.lang.Integer">
        SELECT
            DISTINCT T.TRADER_ID
        FROM
            T_TRADER T
        LEFT JOIN T_SALEORDER S ON S.TRADER_ID = T.TRADER_ID
        LEFT JOIN T_SALEORDER_GOODS SG ON S.SALEORDER_ID = SG.SALEORDER_ID
        <if test="categoryIds != null and categoryIds.size() > 0">
            LEFT JOIN T_GOODS G1 ON G1.GOODS_ID = SG.GOODS_ID
            LEFT JOIN T_BUYORDER B ON B.TRADER_ID = T.TRADER_ID
            LEFT JOIN T_BUYORDER_GOODS BG ON BG.BUYORDER_ID = B.BUYORDER_ID
            LEFT JOIN T_GOODS G2 ON G2.GOODS_ID = BG.GOODS_ID
        </if>
        <if test="departments != null and departments.size() > 0">
            LEFT JOIN V_CORE_SKU sku ON sku.SKU_NO = SG.SKU
            LEFT JOIN V_CORE_SPU spu on sku.SPU_ID=spu.SPU_ID
            LEFT JOIN V_SPU_DEPARTMENT_MAPPING dm ON dm.SPU_ID=spu.SPU_ID
        </if>
        WHERE
        1 = 1
        AND	S.VALID_STATUS = 1
        AND S.`STATUS` != 3
        AND SG.IS_DELETE = 0
        AND T.TRADER_ID IN
        <foreach collection="traderIds" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>

        <if test="categoryIds != null and categoryIds.size() > 0">
            AND (
            G1.CATEGORY_ID IN
            <foreach collection="categoryIds" separator="," open="(" close=")" item="id">
                #{id}
            </foreach>
            OR
            G2.CATEGORY_ID IN
            <foreach collection="categoryIds" separator="," open="(" close=")" item="id">
                #{id}
            </foreach>
            )
        </if>

        <if test="departments != null and departments.size() > 0">
            AND dm.DEPARTMENT_ID IN
            <foreach collection="departments" separator="," open="(" close=")" item="id">
                #{id}
            </foreach>
        </if>


    </select>

    <select id="getTraderCustomerAccountPeriodInfo" parameterType="java.lang.Integer" resultMap="VoBaseResultMap" >
		select
			a.PERIOD_AMOUNT,a.PERIOD_DAY,a.TRADER_ID,
			b.SALEORDER_ID
		from
			T_TRADER_CUSTOMER a
		left join
			T_SALEORDER b
		on
			a.TRADER_ID = b.TRADER_ID
		and
			b.HAVE_ACCOUNT_PERIOD = 1
		and
			b.STATUS != 3
		left join
			T_CAPITAL_BILL_DETAIL c
		on
			c.RELATED_ID = b.SALEORDER_ID
		and
			c.ORDER_TYPE = 1
		left join
			T_CAPITAL_BILL d
		on
			c.CAPITAL_BILL_ID = d.CAPITAL_BILL_ID
		and
			d.TRADER_MODE = 533
		where
			a.TRADER_ID = #{traderId,jdbcType=INTEGER}
		GROUP BY
			b.SALEORDER_ID
	</select>

    <select id="getCustomerSaleorderInfo" resultMap="VoBaseResultMap" parameterType="java.lang.Integer">
        select
        count(*) as CUSTOMER_BUY_COUNT,max(VALID_TIME) as LAST_BUSSINESS_TIME,min(VALID_TIME) as FIRST_BUSSINESS_TIME,sum(TOTAL_AMOUNT) as CUSTOMER_BUY_MONEY
        from
        T_SALEORDER a
        where
        a.VALID_STATUS = 1	<!-- 是否生效 0否 1是 -->
        and a.PAYMENT_STATUS = 2	<!-- 付款状态 0未付款 1部分付款 2全部付款 -->
        and
        a.TRADER_ID = #{traderId,jdbcType=INTEGER}
    </select>

    <select id="getTraderCustomerZYAccountPeriodAmount" parameterType="java.lang.Integer" resultType="java.math.BigDecimal">
		select
			COALESCE(sum(ABS(ACCOUNT_PERIOD_AMOUNT)),0)
		from
			T_SALEORDER
		where
			TRADER_ID = #{traderId,jdbcType=INTEGER}
		and
			HAVE_ACCOUNT_PERIOD = 1
		and
			PAYMENT_STATUS in (0,1)
		and
			STATUS != 3
	</select>

    <select id="getTraderCustomerAccountPeriodTimes" parameterType="java.lang.Integer" resultType="java.lang.Integer">
		select
	  		count(*)
	  	from
	  		T_CAPITAL_BILL a
	  	left join
			T_CAPITAL_BILL_DETAIL b
		on
			a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
		left join
			T_SALEORDER c
		on
			c.SALEORDER_ID = b.RELATED_ID
	  	where
	  		a.TRADER_TYPE = 3
	  	and
	  		a.TRADER_MODE = 527
	  	and
	  		b.ORDER_TYPE = 1
	  	and
	  		c.TRADER_ID = #{traderId,jdbcType=INTEGER}
	</select>

    <select id="getCustomerByTraderId" resultMap="VoBaseResultMap" parameterType="java.lang.Integer">
  	select
    	a.*,
  		j.LAST_VERIFY_USERNAME,
  		j.VERIFY_USERNAME,
  		ifnull(j.STATUS,-1) as VERIFY_STATUS
    from T_TRADER_CUSTOMER a
    left join
  		T_VERIFIES_INFO j on j.RELATE_TABLE_KEY = a.TRADER_CUSTOMER_ID and j.RELATE_TABLE = "T_TRADER_CUSTOMER"
    where a.TRADER_ID = #{traderId,jdbcType=INTEGER}
  </select>

    <select id="getCustomerQuoteorderInfo" resultMap="VoBaseResultMap" parameterType="java.lang.Integer">
  	select
  		count(*) as CUSTOMER_QUOTE_COUNT
  	from
  		T_QUOTEORDER a
  	where
  		a.VALID_STATUS = 1
  		and
  		a.TRADER_ID = #{traderId,jdbcType=INTEGER}

  </select>

    <select id="getTraderAccountPeriodApplyList" parameterType="java.lang.Integer" resultType="com.vedeng.finance.model.TraderAccountPeriodApply">
		SELECT A.TRADER_ACCOUNT_PERIOD_APPLY_ID,
		       A.TRADER_ID,
		       A.TRADER_TYPE,
		       A.ACCOUNT_PERIOD_NOW,
		       A.ACCOUNT_PERIOD_APPLY,
		       A.ACCOUNT_PERIOD_LEFT,
		       A.ACCOUNT_PERIOD_DAYS_NOW,
		       A.ACCOUNT_PERIOD_DAYS_APPLY,
		       A.USED_TIMES,
		       A.OVERDUE_TIMES,
		       A.OVERDUE_AMOUNT,
		       A.SALEORDER_ID,
		       A.PREDICT_PROFIT,
		       A.STATUS,
		       A.COMMENTS,
		       FROM_UNIXTIME(A.ADD_TIME / 1000, '%Y-%m-%d %H:%i:%s') AS ADD_TIME_STR,
		       A.ADD_TIME,
		       A.CREATOR,
		       A.MOD_TIME,
		       A.UPDATER
		FROM T_TRADER_ACCOUNT_PERIOD_APPLY A
		WHERE A.TRADER_ID = #{traderId,jdbcType=INTEGER} AND A.TRADER_TYPE = #{traderType,jdbcType=INTEGER}
	</select>

    <resultMap id="TraderFinanceBaseResultMap" type="com.vedeng.trader.model.TraderFinance" >
        <id column="TRADER_FINANCE_ID" property="traderFinanceId" jdbcType="INTEGER" />
        <result column="TRADER_ID" property="traderId" jdbcType="INTEGER" />
        <result column="TRADER_TYPE" property="traderType" jdbcType="BIT" />
        <result column="CREDIT_RATING" property="creditRating" jdbcType="INTEGER" />
        <result column="REG_ADDRESS" property="regAddress" jdbcType="VARCHAR" />
        <result column="REG_TEL" property="regTel" jdbcType="VARCHAR" />
        <result column="TAX_NUM" property="taxNum" jdbcType="VARCHAR" />
        <result column="AVERAGE_TAXPAYER_DOMAIN" property="averageTaxpayerDomain" jdbcType="VARCHAR" />
        <result column="AVERAGE_TAXPAYER_URI" property="averageTaxpayerUri" jdbcType="VARCHAR" />
        <result column="BANK" property="bank" jdbcType="VARCHAR" />
        <result column="BANK_CODE" property="bankCode" jdbcType="VARCHAR" />
        <result column="BANK_ACCOUNT" property="bankAccount" jdbcType="VARCHAR" />
        <result column="COMMENTS" property="comments" jdbcType="VARCHAR" />
        <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
        <result column="CREATOR" property="creator" jdbcType="INTEGER" />
        <result column="CHECK_STATUS" property="checkStatus" jdbcType="INTEGER" />
        <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
        <result column="UPDATER" property="updater" jdbcType="INTEGER" />
    </resultMap>

    <resultMap type="com.vedeng.trader.model.vo.TraderFinanceVo" id="TraderFinanceVoBaseResultMap" extends="TraderFinanceBaseResultMap">
        <result column="AMOUNT" property="amount" jdbcType="DECIMAL" />
        <result column="PERIOD_AMOUNT" property="periodAmount" jdbcType="DECIMAL" />
        <result column="PERIOD_DAY" property="periodDay" jdbcType="INTEGER" />
    </resultMap>

    <select id="getTraderCustomerFinanceList" resultMap="TraderFinanceBaseResultMap" parameterType="com.vedeng.trader.model.TraderFinance" >
        select
        tf.TRADER_FINANCE_ID, tf.TRADER_ID, tf.TRADER_TYPE, tf.CREDIT_RATING, tf.REG_ADDRESS, tf.REG_TEL, tf.TAX_NUM,
        tf.AVERAGE_TAXPAYER_DOMAIN, tf.AVERAGE_TAXPAYER_URI, tf.BANK, tf.BANK_CODE, tf.BANK_ACCOUNT,tf.COMMENTS,tf.ADD_TIME,
        tf.CREATOR, tf.MOD_TIME, tf.UPDATER
        from T_TRADER_FINANCE tf
        where 1=1

            and tf.TRADER_ID = #{traderId,jdbcType=INTEGER}

        <if test="traderType !=null ">
            and tf.TRADER_TYPE = #{traderType,jdbcType=BIT}
        </if>
        order by tf.ADD_TIME desc
    </select>

    <!-- getTraderFinance -->
    <select id="getTraderCustomerFinance" resultMap="TraderFinanceVoBaseResultMap" parameterType="com.vedeng.trader.model.vo.TraderFinanceVo" >
        select
        tf.TRADER_FINANCE_ID, tf.TRADER_ID, tf.TRADER_TYPE, tf.CREDIT_RATING, tf.REG_ADDRESS, tf.REG_TEL, tf.TAX_NUM,
        tf.AVERAGE_TAXPAYER_DOMAIN, tf.AVERAGE_TAXPAYER_URI, tf.BANK, tf.BANK_CODE, tf.BANK_ACCOUNT,tf.COMMENTS,tf.ADD_TIME,
        tf.CREATOR, tf.MOD_TIME, tf.UPDATER, t.AMOUNT,v.STATUS as CHECK_STATUS
        from T_TRADER_FINANCE tf
        left join T_TRADER_CUSTOMER t on tf.TRADER_ID = t.TRADER_ID
        left join T_VERIFIES_INFO v ON v.RELATE_TABLE_KEY=tf.TRADER_FINANCE_ID AND v.relate_table='T_TRADER_FINANCE'
        where 1=1
        <if test="traderFinanceId != null" >
            and tf.TRADER_FINANCE_ID = #{traderFinanceId,jdbcType=INTEGER}
        </if>
        <if test="traderId!=null and traderId!=0">
            and tf.TRADER_ID = #{traderId,jdbcType=INTEGER}
        </if>
        <if test="traderType !=null ">
            and tf.TRADER_TYPE = #{traderType,jdbcType=BIT}
        </if>
        order by tf.ADD_TIME desc limit 1
    </select>


    <select id="getTraderCustomerUsedAccountPeriodAmount" parameterType="java.lang.Integer" resultType="java.math.BigDecimal">
		SELECT
			COALESCE (
				sum(ABS(a.AMOUNT)) - IFNULL(c.hk_amount, 0) - IFNULL(d.txy_amount, 0),
				0
			)
		FROM
			T_CAPITAL_BILL a
		LEFT JOIN T_CAPITAL_BILL_DETAIL b ON a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
		LEFT JOIN (
			SELECT
				COALESCE (sum(ABS(b1.AMOUNT)), 0) AS hk_amount,
				b1.TRADER_ID
			FROM
				T_CAPITAL_BILL a1
			LEFT JOIN T_CAPITAL_BILL_DETAIL b1 ON a1.CAPITAL_BILL_ID = b1.CAPITAL_BILL_ID
			WHERE
				a1.TRADER_TYPE IN (1, 4)
			AND b1.ORDER_TYPE = 1
			AND b1.BUSSINESS_TYPE = 533
			AND b1.TRADER_TYPE = 1
			GROUP BY
				b1.TRADER_ID
		) AS c ON b.TRADER_ID = c.TRADER_ID
		LEFT JOIN (
			SELECT
				COALESCE (sum(ABS(b1.AMOUNT)), 0) AS txy_amount,
				b1.TRADER_ID
			FROM
				T_CAPITAL_BILL a1
			LEFT JOIN T_CAPITAL_BILL_DETAIL b1 ON a1.CAPITAL_BILL_ID = b1.CAPITAL_BILL_ID
			WHERE
				a1.TRADER_TYPE = 3
			AND b1.ORDER_TYPE = 3
			AND b1.BUSSINESS_TYPE = 533
			AND b1.TRADER_TYPE = 1
			GROUP BY
				b1.TRADER_ID
		) AS d ON d.TRADER_ID = b.TRADER_ID
		WHERE
			a.TRADER_TYPE = 3
		AND a.TRADER_MODE = 527
		AND b.ORDER_TYPE = 1
		AND b.TRADER_TYPE = 1
		AND b.TRADER_ID  = #{traderId,jdbcType=INTEGER}
	</select>
    <select id="getCustomerByInfo" resultType="com.vedeng.trader.model.TraderCustomer">
        SELECT
            B.TRADER_ID,B.TRADER_CUSTOMER_ID
        FROM T_TRADER A
        LEFT JOIN T_R_TRADER_J_USER G ON A.TRADER_ID = G.TRADER_ID
        left JOIN T_TRADER_CUSTOMER B on A.TRADER_ID = B.TRADER_ID
        <if test=" departmentInfo != null || departmentIdList != null">
            LEFT JOIN T_R_USER_POSIT C ON G.USER_ID = C.USER_ID
            LEFT JOIN T_POSITION D ON C.POSITION_ID = D.POSITION_ID
            LEFT JOIN T_ORGANIZATION E ON D.ORG_ID = E.ORG_ID
        </if>
        <where>
            1 = 1
            AND B.TRADER_ID IS NOT NULL
            AND A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
            <if test="creatorList != null">
                AND G.USER_ID in
                <foreach collection="creatorList" item="creatList" open="(" close=")" separator=",">
                  #{creatList,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="departmentIdList != null">
                AND E.ORG_ID in
                <foreach collection="departmentIdList" item="departList" open="(" close=")" separator=",">
                    #{departList,jdbcType=INTEGER}
                </foreach>
            </if>
            <!-- 归属销售 -->
            <if test="creator != null and creator != ''">
                AND G.USER_ID = #{creator,jdbcType=INTEGER}
            </if>
            <!-- 客户名称 -->
            <if test="traderName != null and traderName != ''">
                AND A.TRADER_NAME like CONCAT('%',#{traderName,jdbcType=VARCHAR},'%' )
            </if>
            <!-- 部门 -->
            <if test="departmentCustomerInfo != null">
                <trim prefix="AND ( " suffix=" )" prefixOverrides="or">
                    <foreach item="item" collection="departmentCustomerInfo" index="">
                        or( E.ORG_ID = #{item, jdbcType=INTEGER} )
                    </foreach>
                </trim>
            </if>
        </where>
    </select>
    <select id="getCustomerInfoByCustomerId"
            resultType="com.vedeng.trader.model.vo.CustomerBillPeriodApplyItemVo">
         SELECT
            a.TRADER_ID,
           	b.TRADER_NAME as CUSTOMER_NAME,
            d.TITLE as CUSTOMER_NATURE,
            f.TITLE as CUSTOMER_TYPE,
            c.TRADER_TYPE
        FROM
            T_TRADER_CUSTOMER a
            LEFT JOIN T_TRADER b ON a.TRADER_ID = b.TRADER_ID
            LEFT JOIN T_TRADER_ACCOUNT_PERIOD_APPLY c ON b.TRADER_ID = c.TRADER_ID
            LEFT JOIN T_SYS_OPTION_DEFINITION d ON a.CUSTOMER_NATURE = d.SYS_OPTION_DEFINITION_ID
            LEFT JOIN T_SYS_OPTION_DEFINITION f ON a.CUSTOMER_TYPE = f.SYS_OPTION_DEFINITION_ID
        WHERE
            a.TRADER_CUSTOMER_ID = #{customerId,jdbcType=INTEGER}
            LIMIT 1
    </select>
    <select id="getBillTypeByOrderId" resultType="java.lang.Integer">
        SELECT
            T2.BILL_PERIOD_TYPE
        FROM
            T_CUSTOMER_BILL_PERIOD_USE_DETAIL T3
            LEFT JOIN T_CUSTOMER_BILL_PERIOD T2 ON T3.BILL_PERIOD_ID = T2.BILL_PERIOD_ID
        WHERE
            T3.USE_TYPE in (1,2) AND
            T3.RELATED_ID = #{relatedId,jdbcType=INTEGER}
    </select>
    <select id="getTraderIdByTraderGroupId" resultType="java.lang.Integer">
        SELECT
        A.TRADER_ID
        FROM
        T_R_TRADER_GROUP_J_TRADER A
        WHERE
        A.TRADER_GROUP_ID= #{ traderGroupId , jdbcType=INTEGER }
    </select>

    <select id="getCustomerVolistpage" parameterType="Map" resultMap="VoBaseResultMap">
        SELECT
        t.TRADER_ID,
        t.TRADER_NAME,
        t.AREA_ID,
        t.COMPANY_ID,
        t.BELONG_PLATFORM,
        t.LAST_COMMUNICATE_TIME,
        ttc.TRADER_CUSTOMER_ID,
        ttc.CUSTOMER_CATEGORY,
        ttc.IS_ENABLE,
        ttc.IS_TOP,
        ttc.CUSTOMER_LEVEL,
        tlfe.TRADER_LEVEL as traderCustomerLevelGrade,
        tlfe.HISTORY_COMMUNICATION_NUM as historyCommunicationNum,
        ttc.ADD_TIME,
        ttc.MOD_TIME,
        ttc.CUSTOMER_SCORE,
        ttc.TRADER_CUSTOMER_CATEGORY_ID,
        ttc.BASIC_MEDICAL_DEALER,
        ttc.CUSTOMER_TYPE,
        ttc.CUSTOMER_NATURE,
        ttc.IS_VEDENG_MEMBER
        <if test="listCustomerQuery.aptitudeStatus != null">
            ,v.STATUS as APTITUDE_STATUS
        </if>
        <if test="listCustomerQuery.customerStatus !=null">
            ,a.VERIFY_USERNAME, a.STATUS as VERIFY_STATUS
        </if>
        FROM T_TRADER_CUSTOMER ttc
            JOIN T_TRADER t ON t.TRADER_ID = ttc.TRADER_ID
            LEFT JOIN T_TRADER_CUSTOMER_MARKETING_PRINCIPAL tcmp ON tcmp.TRADER_CUSTOMER_ID=ttc.TRADER_CUSTOMER_ID
            LEFT JOIN DWH_TRADER_LIST_FILTER_ERP tlfe ON tlfe.TRADER_CUSTOMER_ID=ttc.TRADER_CUSTOMER_ID
        <if test="listCustomerQuery.userIdList !=null  ">
            JOIN T_R_TRADER_J_USER tu on  t.TRADER_ID =tu.TRADER_ID and tu.TRADER_TYPE = 1
        </if>
        <if test="listCustomerQuery.sharedSaleId != null" >
            LEFT JOIN T_R_SALES_J_TRADER TRSJT ON ttc.TRADER_ID = TRSJT.TRADER_ID AND TRSJT.IS_DELETED = 0
        </if>
        <if test="listCustomerQuery.traderCustomerIsSea !=null  ">
            JOIN T_R_TRADER_J_USER tue on  t.TRADER_ID =tue.TRADER_ID and tue.TRADER_TYPE = 1
            JOIN T_USER tuse on tue.USER_ID = tuse.USER_ID
        </if>
        <if test="listCustomerQuery.areaType == 3 and listCustomerQuery.areaId != null ">
                join (
                select distinct TRADER_CUSTOMER_ID
                from T_TRADER_CUSTOMER_BUSSINESS_AREA
                where
                FIND_IN_SET(#{listCustomerQuery.areaId}, AREA_IDS)
                ) TCBA on TCBA.TRADER_CUSTOMER_ID = ttc.TRADER_CUSTOMER_ID
        </if>
        <if test="listCustomerQuery.customerStatus !=null">
            LEFT JOIN  T_VERIFIES_INFO a ON a.RELATE_TABLE_KEY = ttc.TRADER_CUSTOMER_ID AND a.RELATE_TABLE = 'T_TRADER_CUSTOMER' and a.VERIFIES_TYPE = 617
        </if>
        <if test="listCustomerQuery.aptitudeStatus != null">
            LEFT JOIN T_VERIFIES_INFO v ON v.RELATE_TABLE_KEY=ttc.TRADER_CUSTOMER_ID AND v.relate_table='T_CUSTOMER_APTITUDE'
        </if>
        <if test="listCustomerQuery.financeCheckStatus != null">
            LEFT JOIN T_TRADER_FINANCE f on ttc.TRADER_ID = f.TRADER_ID
            LEFT JOIN T_VERIFIES_INFO fv on fv.RELATE_TABLE_KEY=f.TRADER_FINANCE_ID AND fv.RELATE_TABLE='T_TRADER_FINANCE'
        </if>
        <if test="listCustomerQuery.customerAlert != null">
            <choose>
                <when test="listCustomerQuery.customerAlert == 1">
                   join (select distinct TRADER_CUSTOMER_ID from T_PUBLIC_CUSTOMER_EARLY_WARNING_RECORD where ADD_TIME &gt;
                    unix_timestamp(current_date())
                    * 1000)
                    wr on ttc.TRADER_CUSTOMER_ID = wr.TRADER_CUSTOMER_ID
                </when>
                <when test="listCustomerQuery.customerAlert == 2">
                    join (
                    select distinct TRADER_CUSTOMER_ID
                    from T_PUBLIC_CUSTOMER_RECORD
                    where IS_PRIVATIZED = 1
                    and (unix_timestamp(current_timestamp()) * 1000 - PRIVATIZED_TIME) &lt; 24 * 3600 * 1000
                    ) pcr on ttc.TRADER_CUSTOMER_ID = pcr.TRADER_CUSTOMER_ID
                </when>
                <when test="listCustomerQuery.customerAlert == 3">
                    left join
                    (
                        select distinct TRADER_CUSTOMER_ID
                    from (
                        select distinct TRADER_CUSTOMER_ID
                        from T_PUBLIC_CUSTOMER_RECORD
                        where IS_PRIVATIZED = 1
                        and (unix_timestamp(current_timestamp()) * 1000 - PRIVATIZED_TIME) &lt; 24 * 3600 * 1000
                        union all
                        select distinct TRADER_CUSTOMER_ID
                        from T_PUBLIC_CUSTOMER_EARLY_WARNING_RECORD
                        where ADD_TIME &gt; unix_timestamp(current_date()) * 1000
                        ) wr_pcr
                    ) wr_pcr_dis
                    on ttc.TRADER_CUSTOMER_ID = wr_pcr_dis.TRADER_CUSTOMER_ID
                </when>
            </choose>
        </if>
        <!-- 终端机构性质 -->
        <if test="listCustomerQuery.institutionNature != null ">
            join (
            select distinct TRADER_CUSTOMER_ID
            from T_TRADER_CUSTOMER_MARKETING_TERMINAL
            where
            FIND_IN_SET(#{listCustomerQuery.institutionNature, jdbcType=VARCHAR}, INSTITUTION_NATURE)
            ) TTCMTA on TTCMTA.TRADER_CUSTOMER_ID = ttc.TRADER_CUSTOMER_ID
        </if>
        <!-- 终端机构评级 -->
        <if test="listCustomerQuery.institutionLevel !=null and listCustomerQuery.institutionLevel.size() > 0 and !listCustomerQuery.institutionLevel.contains(-1) ">
            join (
            select distinct TRADER_CUSTOMER_ID
            from T_TRADER_CUSTOMER_MARKETING_TERMINAL
            where 1=1
            and(
            <foreach collection="listCustomerQuery.institutionLevel" item="institutionLevelId" separator="OR"
                     index="index" open="(" close=")">
                FIND_IN_SET(#{institutionLevelId}, INSTITUTION_LEVEL)
            </foreach>
            )
            ) TTCMTB on TTCMTB.TRADER_CUSTOMER_ID = ttc.TRADER_CUSTOMER_ID
        </if>
        <if test="listCustomerQuery.traderCustomerMarketingTypeList != null and listCustomerQuery.traderCustomerMarketingTypeList.size() > 0">
            join (
            select distinct TRADER_CUSTOMER_ID
            from T_TRADER_CUSTOMER_MARKETING_TERMINAL
            where TRADER_CUSTOMER_MARKETING_TYPE in

            <foreach collection="listCustomerQuery.traderCustomerMarketingTypeList" item="traderCustomerMarketingType"
                     index="index"
                     open="(" close=")" separator=",">
                #{traderCustomerMarketingType}
            </foreach>

            <if test="listCustomerQuery.institutionTypeList != null and listCustomerQuery.institutionTypeList.size() > 0">
                and (
                <foreach collection="listCustomerQuery.institutionTypeList" item="institutionType" separator="OR"
                         index="index" open="(" close=")">
                    FIND_IN_SET(#{institutionType}, INSTITUTION_TYPE)
                </foreach>
                )
            </if>

            <if test="listCustomerQuery.institutionTypeChildList != null and listCustomerQuery.institutionTypeChildList.size() > 0">
                and (
                <foreach collection="listCustomerQuery.institutionTypeChildList" item="institutionTypeChild"
                         separator="OR" index="index" open="(" close=")">
                    FIND_IN_SET(#{institutionTypeChild}, INSTITUTION_TYPE_CHILD)
                </foreach>
                )
            </if>

            ) TTCMT on TTCMT.TRADER_CUSTOMER_ID = ttc.TRADER_CUSTOMER_ID
        </if>

        <where>
            <if test="listCustomerQuery.searchMsg != null and listCustomerQuery.searchMsg != ''">
                AND (ttc.TRADER_ID IN(
                select
                cm.TRADER_ID
                from
                T_TRADER_TAG a
                left join
                T_TAG b on a.TAG_ID=b.TAG_ID
                left join T_COMMUNICATE_RECORD cm ON a.TRADER_ID  = cm.COMMUNICATE_RECORD_ID
                where 1=1
                and a.TRADER_TYPE = 3
                and b.TAG_NAME like concat ('%',#{listCustomerQuery.searchMsg},'%')
                ) or ttc.TRADER_ID IN (
                select
                TRADER_ID
                from
                T_COMMUNICATE_RECORD
                where
                  TRADER_ID >0 and
                    (NEXT_CONTACT_CONTENT like concat ('%',#{listCustomerQuery.searchMsg},'%')
                    OR CONTENT_SUFFIX like concat ('%',#{listCustomerQuery.searchMsg},'%')
                    )
                )
                )
            </if>


            <!-- 客户来源 0 erp 1 耗材商城 -->
            <if test="listCustomerQuery.source != null ">
                AND t.SOURCE = #{listCustomerQuery.source, jdbcType=INTEGER}
            </if>
            <!--人工维护-->
            <if test="listCustomerQuery.manualUpdate!=null">
                AND tlfe.MANUAL_UPDATE=#{listCustomerQuery.manualUpdate}
            </if>

            <!--标签完善度-->
            <if test="listCustomerQuery.selectLabelPerfectionValue!=null and listCustomerQuery.selectLabelPerfectionValue==0 and listCustomerQuery.selectLabelPerfectionMin!=null and listCustomerQuery.selectLabelPerfectionMax!=null">
                AND tlfe.ALL_SCORE <![CDATA[>=]]> #{listCustomerQuery.selectLabelPerfectionMin}  and tlfe.ALL_SCORE <![CDATA[<=]]> #{listCustomerQuery.selectLabelPerfectionMax}
            </if>

            <if test="listCustomerQuery.selectLabelPerfectionValue!=null and listCustomerQuery.selectLabelPerfectionValue==1 and listCustomerQuery.selectLabelPerfectionChildValueOne!=null ">
                AND tlfe.C_SCORE = #{listCustomerQuery.selectLabelPerfectionChildValueOne}
            </if>
            <if test="listCustomerQuery.selectLabelPerfectionValue!=null and listCustomerQuery.selectLabelPerfectionValue==2 and listCustomerQuery.selectLabelPerfectionChildValueOne!=null ">
                AND tlfe.P_SKU_TYPE_SCORE = #{listCustomerQuery.selectLabelPerfectionChildValueOne}
            </if>
            <if test="listCustomerQuery.selectLabelPerfectionValue!=null and listCustomerQuery.selectLabelPerfectionValue==3 and listCustomerQuery.selectLabelPerfectionChildValueOne!=null ">
                AND tlfe.P_SKU_SCOPE_SCORE =  #{listCustomerQuery.selectLabelPerfectionChildValueOne}
            </if>
            <if test="listCustomerQuery.selectLabelPerfectionValue!=null and listCustomerQuery.selectLabelPerfectionValue==4 and listCustomerQuery.selectLabelPerfectionChildValueOne!=null ">
                AND tlfe.P_SALE_TYPE_SCORE =  #{listCustomerQuery.selectLabelPerfectionChildValueOne}
            </if>
            <if test="listCustomerQuery.selectLabelPerfectionValue!=null and listCustomerQuery.selectLabelPerfectionValue==5 and listCustomerQuery.selectLabelPerfectionChildValueOne!=null ">
                AND tlfe.P_AREA_SCORE = #{listCustomerQuery.selectLabelPerfectionChildValueOne}
            </if>
            <!--标签完善度-->
            <!-- 经销商有效性 -->
            <if test="listCustomerQuery.effectiveness != null ">
                AND tcmp.EFFECTIVENESS = #{listCustomerQuery.effectiveness, jdbcType=INTEGER}
            </if>
            <!-- 主营商品范畴类别 -->
            <if test="listCustomerQuery.traderCustomerMainCategoryType != null ">
                AND tcmp.SKU_SCOPE = #{listCustomerQuery.traderCustomerMainCategoryType, jdbcType=VARCHAR}
            </if>
            <!-- 主营商品范畴 -->
            <if test="listCustomerQuery.traderCustomerMainCategory !=null and listCustomerQuery.traderCustomerMainCategory.size() > 0 ">
                AND (
                <foreach collection="listCustomerQuery.traderCustomerMainCategory" item="item" separator=" OR ">
                    <if test="item != null">
                        FIND_IN_SET(#{item}, tcmp.SKU_CATEGORY) > 0
                    </if>
                </foreach>
                )
            </if>
            <!-- 主营商品类型 -->
            <if test="listCustomerQuery.skuType != null ">
                AND FIND_IN_SET(#{listCustomerQuery.skuType}, tcmp.SKU_TYPE)
            </if>
            <!-- 销售类别 -->
            <if test="listCustomerQuery.traderCustomerOwnership != null ">
                AND tcmp.SALES_TYPE = #{listCustomerQuery.traderCustomerOwnership, jdbcType=VARCHAR}
            </if>
            <!-- 核心资源-产品型渠道商 -->
            <if test="listCustomerQuery.traderCustomerDevelopLevel == 1 ">
                AND  tcmp.IS_PRODUCT_CUSTOMER = 1
            </if>
            <!-- 核心资源-政府关系 -->
            <if test="listCustomerQuery.traderCustomerDevelopLevel == 2 ">
                AND tcmp.IS_CHANNEL_CUSTOMER = 1
            </if>
            <!-- 客户等级 -->
            <if test="listCustomerQuery.traderCustomerLevelGrade != null ">
                AND tlfe.TRADER_LEVEL = #{listCustomerQuery.traderCustomerLevelGrade, jdbcType=VARCHAR}
            </if>
            <!-- 生命周期 -->
            <if test="listCustomerQuery.traderCustomerLifeCycle != null ">
                AND tlfe.LIFE_CYCLE = #{listCustomerQuery.traderCustomerLifeCycle, jdbcType=VARCHAR}
            </if>
            <!-- 交易分类 -->
            <!-- 近30天 -->
            <if test="listCustomerQuery.traderCustomerTradeType == 0 and listCustomerQuery.traderCustomerTrade != null">
                AND (
                <foreach collection="listCustomerQuery.traderCustomerTrade" item="item" separator=" OR ">
                    <if test="item != null">
                        FIND_IN_SET(#{item}, tlfe.CATEGORY_30_IDS) > 0
                    </if>
                </foreach>
                )
            </if>
            <!-- 近60天 -->
            <if test="listCustomerQuery.traderCustomerTradeType == 1 and listCustomerQuery.traderCustomerTrade != null">
                AND (
                <foreach collection="listCustomerQuery.traderCustomerTrade" item="item" separator=" OR ">
                    <if test="item != null">
                        FIND_IN_SET(#{item}, tlfe.CATEGORY_60_IDS) > 0
                    </if>
                </foreach>
                )
            </if>
            <!-- 近90天 -->
            <if test="listCustomerQuery.traderCustomerTradeType == 2 and listCustomerQuery.traderCustomerTrade != null">
                AND (
                <foreach collection="listCustomerQuery.traderCustomerTrade" item="item" separator=" OR ">
                    <if test="item != null">
                        FIND_IN_SET(#{item}, tlfe.CATEGORY_90_IDS) > 0
                    </if>
                </foreach>
                )
            </if>
            <!-- 近180天 -->
            <if test="listCustomerQuery.traderCustomerTradeType == 3 and listCustomerQuery.traderCustomerTrade != null">
                AND (
                <foreach collection="listCustomerQuery.traderCustomerTrade" item="item" separator=" OR ">
                    <if test="item != null">
                        FIND_IN_SET(#{item}, tlfe.CATEGORY_180_IDS) > 0
                    </if>
                </foreach>
                )
            </if>
            <!-- 近365天 -->
            <if test="listCustomerQuery.traderCustomerTradeType == 4 and listCustomerQuery.traderCustomerTrade != null">
                AND (
                <foreach collection="listCustomerQuery.traderCustomerTrade" item="item" separator=" OR ">
                    <if test="item != null">
                        FIND_IN_SET(#{item}, tlfe.CATEGORY_365_IDS) > 0
                    </if>
                </foreach>
                )
            </if>
            <!-- 近730天 -->
            <if test="listCustomerQuery.traderCustomerTradeType == 5 and listCustomerQuery.traderCustomerTrade != null">
                AND (
                <foreach collection="listCustomerQuery.traderCustomerTrade" item="item" separator=" OR ">
                    <if test="item != null">
                        FIND_IN_SET(#{item}, tlfe.CATEGORY_730_IDS) > 0
                    </if>
                </foreach>
                )
            </if>
            <!-- 交易品牌 -->
            <!-- 近30天 -->
            <if test="listCustomerQuery.traderCustomerBrandType == 0 and listCustomerQuery.traderCustomerBrand != null">
                AND (
                <foreach collection="listCustomerQuery.traderCustomerBrand" item="item" separator=" OR ">
                    <if test="item != null">
                        FIND_IN_SET(#{item}, tlfe.BRAND_30_IDS) > 0
                    </if>
                </foreach>
                )
            </if>
            <!-- 近60天 -->
            <if test="listCustomerQuery.traderCustomerBrandType == 1 and listCustomerQuery.traderCustomerBrand != null">
                AND (
                <foreach collection="listCustomerQuery.traderCustomerBrand" item="item" separator=" OR ">
                    <if test="item != null">
                        FIND_IN_SET(#{item}, tlfe.BRAND_60_IDS) > 0
                    </if>
                </foreach>
                )
            </if>
            <!-- 近90天 -->
            <if test="listCustomerQuery.traderCustomerBrandType == 2 and listCustomerQuery.traderCustomerBrand != null">
                AND (
                <foreach collection="listCustomerQuery.traderCustomerBrand" item="item" separator=" OR ">
                    <if test="item != null">
                        FIND_IN_SET(#{item}, tlfe.BRAND_90_IDS) > 0
                    </if>
                </foreach>
                )
            </if>
            <!-- 近180天 -->
            <if test="listCustomerQuery.traderCustomerBrandType == 3 and listCustomerQuery.traderCustomerBrand != null">
                AND (
                <foreach collection="listCustomerQuery.traderCustomerBrand" item="item" separator=" OR ">
                    <if test="item != null">
                        FIND_IN_SET(#{item}, tlfe.BRAND_180_IDS) > 0
                    </if>
                </foreach>
                )
            </if>
            <!-- 近365天 -->
            <if test="listCustomerQuery.traderCustomerBrandType == 4 and listCustomerQuery.traderCustomerBrand != null">
                AND (
                <foreach collection="listCustomerQuery.traderCustomerBrand" item="item" separator=" OR ">
                    <if test="item != null">
                        FIND_IN_SET(#{item}, tlfe.BRAND_365_IDS) > 0
                    </if>
                </foreach>
                )
            </if>
            <!-- 近730天 -->
            <if test="listCustomerQuery.traderCustomerBrandType == 5 and listCustomerQuery.traderCustomerBrand != null">
                AND (
                <foreach collection="listCustomerQuery.traderCustomerBrand" item="item" separator=" OR ">
                    <if test="item != null">
                        FIND_IN_SET(#{item}, tlfe.BRAND_730_IDS) > 0
                    </if>
                </foreach>
                )
            </if>
            <!-- 沟通记录 -->
            <!-- 30天无沟通 -->
            <if test="listCustomerQuery.traderCustomerCommunicate == 1 ">
                AND tlfe.NO_COMMUNICATION_30 IS NOT NULL AND tlfe.NO_COMMUNICATION_30 <![CDATA[<> '']]>
            </if>
            <!-- 60天无沟通 -->
            <if test="listCustomerQuery.traderCustomerCommunicate == 2 ">
                AND tlfe.NO_COMMUNICATION_60 IS NOT NULL AND tlfe.NO_COMMUNICATION_60 <![CDATA[<> '']]>
            </if>
            <!-- 90天无沟通 -->
            <if test="listCustomerQuery.traderCustomerCommunicate == 3 ">
                AND tlfe.NO_COMMUNICATION_90 IS NOT NULL AND tlfe.NO_COMMUNICATION_90 <![CDATA[<> '']]>
            </if>
            <!-- 180天无沟通 -->
            <if test="listCustomerQuery.traderCustomerCommunicate == 4 ">
                AND tlfe.NO_COMMUNICATION_180 IS NOT NULL AND tlfe.NO_COMMUNICATION_180 <![CDATA[<> '']]>
            </if>
            <!-- 365天无沟通 -->
            <if test="listCustomerQuery.traderCustomerCommunicate == 5 ">
                AND tlfe.NO_COMMUNICATION_365 IS NOT NULL AND tlfe.NO_COMMUNICATION_365 <![CDATA[<> '']]>
            </if>
            <!-- 730天无沟通 -->
            <if test="listCustomerQuery.traderCustomerCommunicate == 6 ">
                AND tlfe.NO_COMMUNICATION_730 IS NOT NULL AND tlfe.NO_COMMUNICATION_730 <![CDATA[<> '']]>
            </if>
            <!-- 历史沟通次数 -->
            <!-- 0次 -->
            <if test="listCustomerQuery.traderCustomerCommunicateTimes == 1 ">
                AND CAST(tlfe.HISTORY_COMMUNICATION_NUM  AS UNSIGNED) = 0
            </if>
            <!-- 1次 -->
            <if test="listCustomerQuery.traderCustomerCommunicateTimes == 2 ">
                AND CAST(tlfe.HISTORY_COMMUNICATION_NUM  AS UNSIGNED) = 1
            </if>
            <!-- 2次到5次 -->
            <if test="listCustomerQuery.traderCustomerCommunicateTimes == 3 ">
                AND CAST(tlfe.HISTORY_COMMUNICATION_NUM  AS UNSIGNED) &gt; 1 and CAST(tlfe.HISTORY_COMMUNICATION_NUM  AS UNSIGNED) &lt; 6
            </if>
            <!-- 五次以上 -->
            <if test="listCustomerQuery.traderCustomerCommunicateTimes == 4 ">
                AND CAST(tlfe.HISTORY_COMMUNICATION_NUM  AS UNSIGNED) &gt; 5
            </if>
            <!-- 有无手机 -->
            <if test="listCustomerQuery.traderCustomerIsMobile == 1 ">
                AND EXISTS (SELECT 1 FROM T_TRADER_CONTACT a WHERE a.TRADER_ID = t.TRADER_ID AND ((a.MOBILE IS NOT NULL OR a.MOBILE2 IS NOT NULL) AND (a.MOBILE <![CDATA[<> '']]> OR a.MOBILE2 <![CDATA[<> '']]>)))
            </if>
            <if test="listCustomerQuery.traderCustomerIsMobile == 2 ">
                AND NOT EXISTS (SELECT 1 FROM T_TRADER_CONTACT a WHERE a.TRADER_ID = t.TRADER_ID AND ((a.MOBILE IS NOT NULL OR a.MOBILE2 IS NOT NULL) AND (a.MOBILE <![CDATA[<> '']]> OR a.MOBILE2 <![CDATA[<> '']]>)))
            </if>
            <!-- 是否公海 客户归属销售是【bei】的就是公海客户，否则即为非公海客户； -->
            <if test="listCustomerQuery.traderCustomerIsSea == 1 ">
                AND  tuse.USERNAME  = 'Bei'
            </if>
            <if test="listCustomerQuery.traderCustomerIsSea == 2 ">
                AND  tuse.USERNAME <![CDATA[<> 'Bei']]>
            </if>
            <!-- 贝登会员 -->
            <if test="listCustomerQuery.traderCustomerIsMember != null ">
                AND ttc.IS_VEDENG_MEMBER = #{listCustomerQuery.traderCustomerIsMember, jdbcType=INTEGER}
            </if>
            <if test="listCustomerQuery.aptitudeStatus != null and listCustomerQuery.aptitudeStatus != 3">
                and v.STATUS=#{listCustomerQuery.aptitudeStatus}
            </if>
            <if test=" listCustomerQuery.aptitudeStatus == 3">
                and v.STATUS IS NULL
            </if>
            <if test="listCustomerQuery.userIdList !=null">
                and tu.USER_ID in
                <foreach collection="listCustomerQuery.userIdList" item="userId" index="index" open="(" close=")" separator=",">
                    #{userId}
                </foreach>
            </if>
            <if test="listCustomerQuery.isLimitedPrice != null">
                and ttc.IS_LIMITPRICE=#{listCustomerQuery.isLimitedPrice}
            </if>
            <if test="listCustomerQuery.financeCheckStatus != null">
                and fv.STATUS=#{listCustomerQuery.financeCheckStatus}
            </if>
            <if test="listCustomerQuery.customerName !=null and listCustomerQuery.customerName !='' ">
                and t.TRADER_NAME like CONCAT('%',#{listCustomerQuery.customerName},'%' )
            </if>
            <if test="listCustomerQuery.customerType !=null and listCustomerQuery.customerType > 0">
                and ttc.CUSTOMER_TYPE = #{listCustomerQuery.customerType}
            </if>
            <if test="listCustomerQuery.customerNature !=null and listCustomerQuery.customerNature > 0">
                and ttc.CUSTOMER_NATURE = #{listCustomerQuery.customerNature}
            </if>

            <if test="listCustomerQuery.customerCategory != null">
                and ttc.CUSTOMER_CATEGORY = #{listCustomerQuery.customerCategory}
            </if>
            <if test="listCustomerQuery.areaType == 1 and listCustomerQuery.areaId != null">
                AND FIND_IN_SET(#{listCustomerQuery.areaId}, t.AREA_IDS)
            </if>
            <if test="listCustomerQuery.areaType == 2 and listCustomerQuery.areaId != null">
                AND FIND_IN_SET(#{listCustomerQuery.areaId}, t.WAREHOUSE_AREA_IDS)
            </if>
            <if test="listCustomerQuery.customerStatus !=null and listCustomerQuery.customerStatus ==0">
                and a.STATUS = 0
            </if>
            <if test="listCustomerQuery.customerStatus !=null and listCustomerQuery.customerStatus ==1">
                and a.STATUS = 1
            </if>
            <if test="listCustomerQuery.customerStatus !=null and listCustomerQuery.customerStatus ==2">
                and a.STATUS = 2
            </if>
            <if test="listCustomerQuery.customerStatus !=null and listCustomerQuery.customerStatus ==3">
                and (ISNULL(a.STATUS) or a.STATUS = 3)
            </if>
            <if test="listCustomerQuery.hasQuoted !=null">
                and ttc.HAS_QUOTED= #{listCustomerQuery.hasQuoted, jdbcType=BIT}
            </if>
            <if test="listCustomerQuery.isCooperated !=null">
                and ttc.IS_COOPERATED= #{listCustomerQuery.isCooperated, jdbcType=BIT}
            </if>
            <if test="listCustomerQuery.wxTraderIdList !=null ">
                and ttc.TRADER_ID in
                <foreach collection="listCustomerQuery.wxTraderIdList" item="traderId" index="index"
                         open="(" close=")" separator=",">
                    #{traderId}
                </foreach>
            </if>
            <if test="listCustomerQuery.parenterTraderIdList !=null ">
                and ttc.TRADER_ID in
                <foreach collection="listCustomerQuery.parenterTraderIdList" item="traderId" index="index"
                         open="(" close=")" separator=",">
                    #{traderId}
                </foreach>
            </if>
            <if test="listCustomerQuery.searchTraderGroupList !=null ">
                and ttc.TRADER_ID in
                <foreach collection="listCustomerQuery.searchTraderGroupList" item="traderId" index="index"
                         open="(" close=")" separator=",">
                    #{traderId}
                </foreach>
            </if>
            <if test="listCustomerQuery.traderIdList !=null and listCustomerQuery.traderIdList.size() > 0">
                and ttc.TRADER_ID in
                <foreach collection="listCustomerQuery.traderIdList" item="traderId" index="index" open="(" close=")" separator=",">
                    #{traderId}
                </foreach>
            </if>
            <if test="listCustomerQuery.categoryList !=null">
                and ttc.TRADER_CUSTOMER_CATEGORY_ID in
                <foreach collection="listCustomerQuery.categoryList" item="categoryId" index="index" open="(" close=")" separator=",">
                    #{categoryId}
                </foreach>
            </if>
            <if test="listCustomerQuery.smallScore !=null">
                and ttc.CUSTOMER_SCORE <![CDATA[>=]]> #{listCustomerQuery.smallScore}
            </if>
            <if test="listCustomerQuery.bigScore !=null">
                and ttc.CUSTOMER_SCORE <![CDATA[<=]]> #{listCustomerQuery.bigScore}
            </if>
            <if test="listCustomerQuery.customerLevel !=null and listCustomerQuery.customerLevel !=0">
                and ttc.CUSTOMER_LEVEL = #{listCustomerQuery.customerLevel}
            </if>
            <if test="listCustomerQuery.userEvaluate !=null and listCustomerQuery.userEvaluate !=0">
                and ttc.USER_EVALUATE = #{listCustomerQuery.userEvaluate}
            </if>
            <if test="listCustomerQuery.basicMedicalDealer !=null and listCustomerQuery.basicMedicalDealer !=-1">
                and ttc.BASIC_MEDICAL_DEALER = #{listCustomerQuery.basicMedicalDealer}
            </if>
            <if test="listCustomerQuery.timeType ==1">
                <if test="listCustomerQuery.startTime != null">
                    and ttc.ADD_TIME <![CDATA[>=]]> #{listCustomerQuery.startTime}
                </if>
                <if test="listCustomerQuery.endTime != null">
                    and ttc.ADD_TIME <![CDATA[<=]]> #{listCustomerQuery.endTime}
                </if>
            </if>
            <if test="listCustomerQuery.timeType ==2 and listCustomerQuery.capitalBillTraderIdList != null">
                and ttc.TRADER_ID in
                <foreach collection="listCustomerQuery.capitalBillTraderIdList" item="traderId" index="index"
                         open="(" close=")" separator=",">
                    #{traderId}
                </foreach>
            </if>
            <if test="listCustomerQuery.timeType ==3">
                <if test="listCustomerQuery.startTime != null">
                    and ttc.MOD_TIME <![CDATA[>=]]> #{listCustomerQuery.startTime}
                </if>
                <if test="listCustomerQuery.endTime != null">
                    and ttc.MOD_TIME <![CDATA[<=]]> #{listCustomerQuery.endTime}
                </if>
            </if>
            <if test="listCustomerQuery.timeType ==4">
                <if test="listCustomerQuery.startTime != null">
                    and t.LAST_COMMUNICATE_TIME <![CDATA[>=]]> #{listCustomerQuery.startTime}
                </if>
                <if test="listCustomerQuery.endTime != null">
                    and t.LAST_COMMUNICATE_TIME <![CDATA[<=]]> #{listCustomerQuery.endTime}
                </if>
            </if>
            <if test="listCustomerQuery.platformNo != null">
                and t.BELONG_PLATFORM=#{listCustomerQuery.platformNo}
            </if>
            <if test="listCustomerQuery.customerAlert != null">
                <choose>
                    <when test="listCustomerQuery.customerAlert == 3">
                        and wr_pcr_dis.TRADER_CUSTOMER_ID is null
                    </when>
                </choose>
            </if>

            <if test="listCustomerQuery.sharedSaleId != null" >
                AND TRSJT.SALE_USER_ID = #{listCustomerQuery.sharedSaleId, jdbcType=INTEGER}
            </if>

            <if test="listCustomerQuery.agencyBrandIdList != null and listCustomerQuery.otherAgencyBrand != null">
                AND (
                <foreach collection="listCustomerQuery.agencyBrandIdList" item="item" separator=" OR ">
                    <if test="item != null">
                        FIND_IN_SET(#{item}, tcmp.AGENCY_BRAND)
                    </if>
                </foreach>
                or tcmp.OTHER_AGENCY_BRAND like CONCAT('%',#{listCustomerQuery.otherAgencyBrand},'%')
                )
            </if>

            <if test="listCustomerQuery.agencyBrandIdList != null and listCustomerQuery.otherAgencyBrand == null">
                AND (
                <foreach collection="listCustomerQuery.agencyBrandIdList" item="item" separator=" OR ">
                    <if test="item != null">
                        FIND_IN_SET(#{item}, tcmp.AGENCY_BRAND)
                    </if>
                </foreach>
                )
            </if>

            <if test="listCustomerQuery.agencyBrandIdList == null and listCustomerQuery.otherAgencyBrand != null">
                AND tcmp.OTHER_AGENCY_BRAND like CONCAT('%',#{listCustomerQuery.otherAgencyBrand},'%')
            </if>

            <if test="listCustomerQuery.agencySkuList != null and listCustomerQuery.otherAgencySku != null">
                AND (
                <foreach collection="listCustomerQuery.agencySkuList" item="item" separator=" OR ">
                    <if test="item != null">
                        FIND_IN_SET(#{item}, tcmp.AGENCY_SKU)
                    </if>
                </foreach>
                or tcmp.OTHER_AGENCY_SKU like CONCAT('%',#{listCustomerQuery.otherAgencySku},'%')
                )
            </if>

            <if test="listCustomerQuery.agencySkuList != null and listCustomerQuery.otherAgencySku == null">
                AND (
                <foreach collection="listCustomerQuery.agencySkuList" item="item" separator=" OR ">
                    <if test="item != null">
                        FIND_IN_SET(#{item}, tcmp.AGENCY_SKU)
                    </if>
                </foreach>
                )
            </if>

            <if test="listCustomerQuery.agencySkuList == null and listCustomerQuery.otherAgencySku != null">
                AND tcmp.OTHER_AGENCY_SKU like CONCAT('%',#{listCustomerQuery.otherAgencySku},'%')
            </if>

            <if test="listCustomerQuery.governmentRelation != null and listCustomerQuery.governmentRelation != ''">
                AND FIND_IN_SET(#{listCustomerQuery.governmentRelation}, tcmp.GOVERNMENT_RELATION)
                <if test="listCustomerQuery.otherGovernmentRelation != null and listCustomerQuery.otherGovernmentRelation != ''">
                    AND tcmp.OTHER_GOVERNMENT_RELATION like CONCAT('%',#{listCustomerQuery.otherGovernmentRelation},'%')
                </if>
            </if>

        </where>

        <choose>
            <when test="listCustomerQuery.customerSearchOrderBy == null or listCustomerQuery.customerSearchOrderBy.equals('Y')">
                <if test="listCustomerQuery.userIdList != null and listCustomerQuery.userIdList.size() lte 1">
                    ORDER BY ttc.IS_TOP DESC, ttc.IS_ENABLE DESC ,ttc.MOD_TIME DESC
                </if>
                <if test="listCustomerQuery.userIdList != null and listCustomerQuery.userIdList.size() gt 1">
                    ORDER BY ttc.IS_TOP DESC, ttc.IS_ENABLE DESC ,ttc.TRADER_CUSTOMER_ID DESC
                </if>
            </when>
            <otherwise>
                ORDER BY ttc.IS_TOP DESC, ttc.IS_ENABLE DESC ,ttc.TRADER_CUSTOMER_ID DESC
            </otherwise>
        </choose>


    </select>

    <select id="getCustomerQuoteorderInfoByTraderCustomerVo" resultMap="VoBaseResultMap" parameterType="com.vedeng.trader.model.vo.TraderCustomerVo">
        select
        count(*) as CUSTOMER_QUOTE_COUNT,a.TRADER_ID
        from
        T_QUOTEORDER a
        where
        a.VALID_STATUS = 1
        and
        a.TRADER_ID in
        <foreach collection="traderIdList" index="index" item="traderId" open="(" close=")" separator=",">
            #{traderId,jdbcType=INTEGER}
        </foreach>
        group by a.TRADER_ID
    </select>
    <select id="getCustomerSaleorderInfoList" resultMap="VoBaseResultMap" parameterType="list">
        select
        count(*) as CUSTOMER_BUY_COUNT,max(VALID_TIME) as LAST_BUSSINESS_TIME,
        min(VALID_TIME) as FIRST_BUSSINESS_TIME,sum(TOTAL_AMOUNT) as CUSTOMER_BUY_MONEY, a.TRADER_ID
        from
        T_SALEORDER a
        where
        a.VALID_STATUS = 1	<!-- 是否生效 0否 1是 -->
        and a.PAYMENT_STATUS = 2	<!-- 付款状态 0未付款 1部分付款 2全部付款 -->
        and a.TRADER_ID in
        <foreach collection="traderIdList" index="index" item="traderId" open="(" close=")" separator=",">
            #{traderId,jdbcType=INTEGER}
        </foreach>
        group by a.TRADER_ID
    </select>
    <select id="getTraderGroupIdListByTraderId" resultType="com.vedeng.trader.model.vo.TraderCustomerVo">
        SELECT
        A.TRADER_ID ,
        IFNULL(GROUP_CONCAT(A.TRADER_GROUP_ID),'') AS traderGroupIdStr
        FROM
        T_R_TRADER_GROUP_J_TRADER A
        WHERE
        A.TRADER_ID IN
        <foreach collection="traderIdList" item="traderId" index="index"
                 open="(" close=")" separator=",">
            #{traderId}
        </foreach>
        GROUP BY A.TRADER_ID
    </select>

    <select id="searchTraderCustomerListPage" parameterType="java.util.Map" resultType="com.vedeng.trader.model.vo.TraderCustomerVo">
        SELECT A.*, B.AREA_ID, B.TRADER_NAME, B.AREA_IDS, B.BELONG_PLATFORM,B.TRADER_TYPE
        FROM T_TRADER_CUSTOMER A
        LEFT JOIN T_TRADER B ON A.TRADER_ID = B.TRADER_ID
        WHERE 1=1
        <if test="traderCustomerVo.searchTraderName!=null and traderCustomerVo.searchTraderName!=''">
            AND B.TRADER_NAME LIKE CONCAT('%',#{traderCustomerVo.searchTraderName},'%' )
        </if>
        <if test="traderCustomerVo.companyId!=null and traderCustomerVo.companyId!=0">
            AND B.COMPANY_ID = #{traderCustomerVo.companyId}
        </if>
        <if test="traderCustomerVo.isEnable != null" >
            AND A.IS_ENABLE = #{traderCustomerVo.isEnable,jdbcType=BIT}
        </if>
        <if test="traderCustomerVo.userIdList != null and traderCustomerVo.userIdList.size != 0">
            AND A.TRADER_ID in (
            select TRADER_ID from T_R_TRADER_J_USER  where TRADER_TYPE =1
            and USER_ID in
            <foreach collection="traderCustomerVo.userIdList" item="userId" index="index"
                     open="(" close=")" separator=",">
                #{userId}
            </foreach>
            )
        </if>
    </select>
    <select id="getTraderCustomer" resultType="com.vedeng.trader.model.TraderCustomer">
        select
        t.*, a.TRADER_NAME,
        j.LAST_VERIFY_USERNAME,
        j.VERIFY_USERNAME,
        ifnull(j.STATUS,-1) as VERIFY_STATUS
        from
        T_TRADER_CUSTOMER t
        left join
        T_TRADER a on t.TRADER_ID = a.TRADER_ID
        left join
        T_VERIFIES_INFO j on j.RELATE_TABLE_KEY = t.TRADER_CUSTOMER_ID and j.RELATE_TABLE = "T_TRADER_CUSTOMER"
        where 1=1
        <if test="traderId != null and traderId != 0">
            and t.TRADER_ID = #{traderId,jdbcType=INTEGER}
        </if>
        <if test="traderCustomerId != null and traderCustomerId != 0">
            and t.TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER}
        </if>
        limit 100
    </select>
    <select id="getTraderIdByTime" resultType="java.lang.Integer">
        SELECT DISTINCT c.TRADER_ID FROM T_TRADER_CUSTOMER c JOIN T_QUOTEORDER q ON c.TRADER_ID = q.TRADER_ID
         WHERE (
          q.MOD_TIME <![CDATA[ >= ]]> #{startTime}
         AND q.MOD_TIME <![CDATA[ <= ]]>#{endTime}
        )
    </select>
    <select id="getTraderIdByTimeAndValidStatus" resultType="java.lang.Integer">
        select distinct a.TRADER_ID from T_SALEORDER a join T_TRADER_CUSTOMER c on c.TRADER_ID = a.TRADER_ID
        where a.VALID_STATUS = 1 and a.STATUS != 3
        and (
          a.MOD_TIME <![CDATA[ >= ]]> #{startTime}
         AND a.MOD_TIME <![CDATA[ <= ]]>#{endTime}
        )
    </select>


    <select id="getTraderCustomerInfo" resultType="com.vedeng.trader.model.vo.TraderCustomerVo">
        SELECT
            D.AREA_ID,
            D.AREA_IDS,
            D.TRADER_NAME traderName,
            D.BELONG_PLATFORM belongPlatform,
            B.TRADER_CONTACT_ID traderContactId,
            B.`NAME` traderContactName,
            B.TELEPHONE traderContactTelephone,
            B.MOBILE traderContactMobile,
            C.TRADER_ADDRESS_ID traderAddressId,
            C.AREA_ID traderAreaId,
            C.ADDRESS traderAddress,
            A.CUSTOMER_TYPE customerType,
            A.CUSTOMER_NATURE customerNature
        FROM
            T_TRADER_CUSTOMER A
                LEFT JOIN T_TRADER_CONTACT B ON A.TRADER_ID = B.TRADER_ID AND B.IS_ENABLE=1 AND B.IS_DEFAULT=1
                LEFT JOIN T_TRADER_ADDRESS C ON A.TRADER_ID = C.TRADER_ID AND C.IS_ENABLE = 1 AND C.IS_DEFAULT = 1
                LEFT JOIN T_TRADER D ON A.TRADER_ID = D.TRADER_ID
        WHERE A.TRADER_ID = #{traderId,jdbcType=INTEGER}
            LIMIT 1
    </select>
    <select id="getPublicCustomerVolistpage" parameterType="Map" resultMap="VoBaseResultMap">
        SELECT
            t.TRADER_ID,
            t.TRADER_NAME,
            t.AREA_ID,
            t.COMPANY_ID,
            t.BELONG_PLATFORM,
            t.LAST_COMMUNICATE_TIME,
            ttc.TRADER_CUSTOMER_ID,
            ttc.CUSTOMER_CATEGORY,
            ttc.IS_ENABLE,
            ttc.IS_TOP,
            ttc.CUSTOMER_LEVEL,
            tlfe.TRADER_LEVEL as traderCustomerLevelGrade,
            tlfe.GONGHAI_LEVEL AS gongHaiLevel,
            tcmp.EFFECTIVENESS,
            ttc.ADD_TIME,
            ttc.MOD_TIME,
            ttc.CUSTOMER_SCORE,
            ttc.TRADER_CUSTOMER_CATEGORY_ID,
            ttc.BASIC_MEDICAL_DEALER,
            ttc.CUSTOMER_TYPE,
            ttc.CUSTOMER_NATURE,
            ttc.ASSOCIATED_CUSTOMER_GROUP,
            pcr.PUBLIC_CUSTOMER_RECORD_ID,
            pcr.IS_PRIVATIZED,
            pcr.ORIGIN_USER_ID,
            tlfe.INVALID_REASON,
            ttc.IS_VEDENG_MEMBER,
            pcr.LOCK_USER_NAME
            <if test="listCustomerQuery.aptitudeStatus != null">
                ,v.STATUS as APTITUDE_STATUS
            </if>
            <if test="listCustomerQuery.customerStatus !=null">
                ,a.VERIFY_USERNAME, a.STATUS as VERIFY_STATUS
            </if>
        FROM T_TRADER_CUSTOMER ttc
        JOIN T_TRADER t ON t.TRADER_ID = ttc.TRADER_ID
        JOIN T_PUBLIC_CUSTOMER_RECORD pcr on ttc.TRADER_CUSTOMER_ID = pcr.TRADER_CUSTOMER_ID
         INNER JOIN (
            SELECT TRADER_CUSTOMER_ID, MAX(ADD_TIME) AS max_add_time
            FROM T_PUBLIC_CUSTOMER_RECORD
            GROUP BY TRADER_CUSTOMER_ID
         ) latest ON pcr.TRADER_CUSTOMER_ID = latest.TRADER_CUSTOMER_ID AND pcr.ADD_TIME = latest.max_add_time
        LEFT JOIN T_TRADER_CUSTOMER_MARKETING_PRINCIPAL tcmp ON tcmp.TRADER_CUSTOMER_ID=ttc.TRADER_CUSTOMER_ID
        LEFT JOIN DWH_TRADER_LIST_FILTER_ERP tlfe ON tlfe.TRADER_CUSTOMER_ID=ttc.TRADER_CUSTOMER_ID
            <if test="listCustomerQuery.userIdList !=null ">
                JOIN T_R_TRADER_J_USER tu on  t.TRADER_ID =tu.TRADER_ID and tu.TRADER_TYPE = 1
            </if>
            <if test="listCustomerQuery.customerStatus !=null">
                LEFT JOIN  T_VERIFIES_INFO a ON a.RELATE_TABLE_KEY = ttc.TRADER_CUSTOMER_ID AND a.RELATE_TABLE = 'T_TRADER_CUSTOMER' and a.VERIFIES_TYPE = 617
            </if>
            <if test="listCustomerQuery.aptitudeStatus != null">
                LEFT JOIN T_VERIFIES_INFO v ON v.RELATE_TABLE_KEY=ttc.TRADER_CUSTOMER_ID AND v.relate_table='T_CUSTOMER_APTITUDE'
            </if>
            <if test="listCustomerQuery.financeCheckStatus != null">
                LEFT JOIN T_TRADER_FINANCE f on ttc.TRADER_ID = f.TRADER_ID
                LEFT JOIN T_VERIFIES_INFO fv on fv.RELATE_TABLE_KEY=f.TRADER_FINANCE_ID AND fv.RELATE_TABLE='T_TRADER_FINANCE'
            </if>
            <!--联系人-->
            <if test="listCustomerQuery.conditionType != null and listCustomerQuery.conditionType==1">
                JOIN T_TRADER_CONTACT tc on t.TRADER_ID = tc.TRADER_ID
            </if>
            <!--注册用户-->
            <if test="listCustomerQuery.conditionType != null and listCustomerQuery.conditionType==2">
                JOIN T_WEB_ACCOUNT wa on t.TRADER_ID = wa.TRADER_ID
            </if>
            <!--线索-->
            <if test="listCustomerQuery.conditionType != null and listCustomerQuery.conditionType==3">
                JOIN T_BUSINESS_CLUES bc on t.TRADER_ID = bc.TRADER_ID
            </if>
            <!--商机-->
            <if test="listCustomerQuery.conditionType != null and listCustomerQuery.conditionType==4">
                JOIN T_BUSSINESS_CHANCE buc on t.TRADER_ID = buc.TRADER_ID
            </if>
            <!--报价次数-->
            <if test="listCustomerQuery.conditionType != null and listCustomerQuery.conditionType==5">
                JOIN T_QUOTEORDER quo on t.TRADER_ID = quo.TRADER_ID
            </if>
            <!--交易次数-->
            <if test="listCustomerQuery.conditionType != null and listCustomerQuery.conditionType==6">
                JOIN T_SALEORDER sa on t.TRADER_ID = sa.TRADER_ID
            </if>
        <where>
            <if test="listCustomerQuery.searchMsg != null and listCustomerQuery.searchMsg != ''">
                AND (ttc.TRADER_ID IN (
                select
                distinct TRADER_ID
                from
                T_BUSSINESS_CHANCE
                where CONTENT like CONCAT('%',#{listCustomerQuery.searchMsg},'%' ) AND TRADER_ID >0
                )
                or ttc.TRADER_ID IN(
                select
                a.TRADER_ID
                from
                T_TRADER_TAG a
                left join
                T_TAG b on a.TAG_ID=b.TAG_ID
                where 1=1
                and a.TRADER_TYPE = 3
                and b.TAG_NAME like concat ('%',#{listCustomerQuery.searchMsg},'%')
                ) or ttc.TRADER_ID IN (
                select
                TRADER_ID
                from
                T_COMMUNICATE_RECORD
                where
                TRADER_ID >0 and
                (CONTACT_CONTENT like concat ('%',#{listCustomerQuery.searchMsg},'%')
                OR NEXT_CONTACT_CONTENT like concat ('%',#{listCustomerQuery.searchMsg},'%')
                OR CONTENT_SUFFIX like concat ('%',#{listCustomerQuery.searchMsg},'%')
                )
                )
                )
            </if>

            <if test="listCustomerQuery.originUserId !=null">
                AND pcr.ORIGIN_USER_ID = #{listCustomerQuery.originUserId}
            </if>
            <if test="listCustomerQuery.effectiveness != null ">
                AND tcmp.EFFECTIVENESS = #{listCustomerQuery.effectiveness, jdbcType=INTEGER}
            </if>
            <!-- 客户等级 -->
            <if test="listCustomerQuery.traderCustomerLevelGrade != null ">
                AND tlfe.TRADER_LEVEL = #{listCustomerQuery.traderCustomerLevelGrade, jdbcType=VARCHAR}
            </if>
            <if test="listCustomerQuery.queryGongHaiLevelList != null and listCustomerQuery.queryGongHaiLevelList.size() > 0">
                and tlfe.GONGHAI_LEVEL in
                <foreach collection="listCustomerQuery.queryGongHaiLevelList" item="gongHaiLevel" index="index" open="(" close=")" separator=",">
                    #{gongHaiLevel}
                </foreach>
            </if>
            <if test="listCustomerQuery.queryInvalidReasonList != null and listCustomerQuery.queryInvalidReasonList.size() > 0">
                AND (
                <foreach collection="listCustomerQuery.queryInvalidReasonList" item="invalidReason" separator=" OR ">
                    FIND_IN_SET(#{invalidReason}, tlfe.INVALID_REASON)
                </foreach>
                )
            </if>
            <!-- 客户来源 0 erp 1 耗材商城 -->
            <if test="listCustomerQuery.source != null ">
                AND t.SOURCE = #{listCustomerQuery.source, jdbcType=INTEGER}
            </if>
            <if test="listCustomerQuery.aptitudeStatus != null and listCustomerQuery.aptitudeStatus != 3">
                and v.STATUS=#{listCustomerQuery.aptitudeStatus}
            </if>
            <if test=" listCustomerQuery.aptitudeStatus == 3">
                and (v.STATUS IS NULL or v.STATUS = 3)
            </if>
            <if test="listCustomerQuery.userIdList !=null ">
                and tu.USER_ID in
                <foreach collection="listCustomerQuery.userIdList" item="userId" index="index" open="(" close=")" separator=",">
                    #{userId}
                </foreach>
            </if>
            <if test="listCustomerQuery.isLimitedPrice != null">
                and ttc.IS_LIMITPRICE=#{listCustomerQuery.isLimitedPrice}
            </if>
            <if test="listCustomerQuery.financeCheckStatus != null">
                and fv.STATUS=#{listCustomerQuery.financeCheckStatus}
            </if>
            <if test="listCustomerQuery.customerName !=null and listCustomerQuery.customerName !='' ">
                and t.TRADER_NAME like CONCAT('%',#{listCustomerQuery.customerName},'%' )
            </if>
            <if test="listCustomerQuery.customerType !=null and listCustomerQuery.customerType > 0">
                and ttc.CUSTOMER_TYPE = #{listCustomerQuery.customerType}
            </if>
            <if test="listCustomerQuery.customerNature !=null and listCustomerQuery.customerNature > 0">
                and ttc.CUSTOMER_NATURE = #{listCustomerQuery.customerNature}
            </if>
            <if test="listCustomerQuery.customerCategory != null">
                and ttc.CUSTOMER_CATEGORY = #{listCustomerQuery.customerCategory}
            </if>
            <if test="listCustomerQuery.areaId != null">
                and FIND_IN_SET(#{listCustomerQuery.areaId},t.AREA_IDS)
            </if>
            <if test="listCustomerQuery.customerStatus !=null and listCustomerQuery.customerStatus ==0">
                and a.STATUS = 0
            </if>
            <if test="listCustomerQuery.customerStatus !=null and listCustomerQuery.customerStatus ==1">
                and a.STATUS = 1
            </if>
            <if test="listCustomerQuery.customerStatus !=null and listCustomerQuery.customerStatus ==2">
                and a.STATUS = 2
            </if>
            <if test="listCustomerQuery.customerStatus !=null and listCustomerQuery.customerStatus ==3">
                and (ISNULL(a.STATUS) or a.STATUS = 3)
            </if>
            <if test="listCustomerQuery.hasQuoted !=null">
                and ttc.HAS_QUOTED= #{listCustomerQuery.hasQuoted, jdbcType=BIT}
            </if>
            <if test="listCustomerQuery.isCooperated !=null">
                and ttc.IS_COOPERATED= #{listCustomerQuery.isCooperated, jdbcType=BIT}
            </if>
            <if test="listCustomerQuery.wxTraderIdList !=null ">
                and ttc.TRADER_ID in
                <foreach collection="listCustomerQuery.wxTraderIdList" item="traderId" index="index"
                         open="(" close=")" separator=",">
                    #{traderId}
                </foreach>
            </if>
            <if test="listCustomerQuery.parenterTraderIdList !=null ">
                and ttc.TRADER_ID in
                <foreach collection="listCustomerQuery.parenterTraderIdList" item="traderId" index="index"
                         open="(" close=")" separator=",">
                    #{traderId}
                </foreach>
            </if>
            <if test="listCustomerQuery.searchTraderGroupList !=null ">
                and ttc.TRADER_ID in
                <foreach collection="listCustomerQuery.searchTraderGroupList" item="traderId" index="index"
                         open="(" close=")" separator=",">
                    #{traderId}
                </foreach>
            </if>
            <if test="listCustomerQuery.traderIdList !=null and listCustomerQuery.traderIdList.size() > 0">
                and ttc.TRADER_ID in
                <foreach collection="listCustomerQuery.traderIdList" item="traderId" index="index" open="(" close=")" separator=",">
                    #{traderId}
                </foreach>
            </if>
            <if test="listCustomerQuery.categoryList !=null">
                and ttc.TRADER_CUSTOMER_CATEGORY_ID in
                <foreach collection="listCustomerQuery.categoryList" item="categoryId" index="index" open="(" close=")" separator=",">
                    #{categoryId}
                </foreach>
            </if>
            <if test="listCustomerQuery.originUserList !=null and listCustomerQuery.originUserList.size() > 0">
                and pcr.ORIGIN_USER_ID in
                <foreach collection="listCustomerQuery.originUserList" item="originUserId" index="index" open="(" close=")" separator=",">
                    #{originUserId,jdbcType=INTEGER}
                </foreach>
            </if>
             <if test="listCustomerQuery.lockStatus != null">
                 and pcr.IS_PRIVATIZED = #{listCustomerQuery.lockStatus}
            </if>
             <if test="listCustomerQuery.lockStatus == null">
                 and pcr.IS_PRIVATIZED in (0,1)
            </if>
            and pcr.IS_UNLOCK = 0
            <if test="listCustomerQuery.smallScore !=null">
                and ttc.CUSTOMER_SCORE <![CDATA[>=]]> #{listCustomerQuery.smallScore}
            </if>
            <if test="listCustomerQuery.bigScore !=null">
                and ttc.CUSTOMER_SCORE <![CDATA[<=]]> #{listCustomerQuery.bigScore}
            </if>
            <if test="listCustomerQuery.customerLevel !=null and listCustomerQuery.customerLevel !=0">
                and ttc.CUSTOMER_LEVEL = #{listCustomerQuery.customerLevel}
            </if>
            <if test="listCustomerQuery.userEvaluate !=null and listCustomerQuery.userEvaluate !=0">
                and ttc.USER_EVALUATE = #{listCustomerQuery.userEvaluate}
            </if>
            <if test="listCustomerQuery.basicMedicalDealer !=null and listCustomerQuery.basicMedicalDealer !=-1">
                and ttc.BASIC_MEDICAL_DEALER = #{listCustomerQuery.basicMedicalDealer}
            </if>
            <if test="listCustomerQuery.timeType ==1">
                <if test="listCustomerQuery.startTime != null">
                    and ttc.ADD_TIME <![CDATA[>=]]> #{listCustomerQuery.startTime}
                </if>
                <if test="listCustomerQuery.endTime != null">
                    and ttc.ADD_TIME <![CDATA[<=]]> #{listCustomerQuery.endTime}
                </if>
            </if>
            <if test="listCustomerQuery.timeType ==2">
                and ttc.TRADER_ID in
                <foreach collection="listCustomerQuery.capitalBillTraderIdList" item="traderId" index="index"
                         open="(" close=")" separator=",">
                    #{traderId}
                </foreach>
            </if>
            <if test="listCustomerQuery.timeType ==3">
                <if test="listCustomerQuery.startTime != null">
                    and ttc.MOD_TIME <![CDATA[>=]]> #{listCustomerQuery.startTime}
                </if>
                <if test="listCustomerQuery.endTime != null">
                    and ttc.MOD_TIME <![CDATA[<=]]> #{listCustomerQuery.endTime}
                </if>
            </if>
            <if test="listCustomerQuery.timeType ==4">
                <if test="listCustomerQuery.startTime != null">
                    and t.LAST_COMMUNICATE_TIME <![CDATA[>=]]> #{listCustomerQuery.startTime}
                </if>
                <if test="listCustomerQuery.endTime != null">
                    and t.LAST_COMMUNICATE_TIME <![CDATA[<=]]> #{listCustomerQuery.endTime}
                </if>
            </if>
            <if test="listCustomerQuery.platformNo != null">
                and t.BELONG_PLATFORM=#{listCustomerQuery.platformNo}
            </if>
            <if test="listCustomerQuery.conditionType != null and listCustomerQuery.conditionType==1">
                AND (tc.MOBILE is not null  or tc.MOBILE2 is not null or tc.TELEPHONE is not null)
                AND (tc.MOBILE != ''  or tc.MOBILE2 != '' or tc.TELEPHONE != '')
            </if>
            <if test="listCustomerQuery.conditionType != null and listCustomerQuery.conditionType==2">
                AND wa.ERP_ACCOUNT_ID is not null
                and wa.IS_ENABLE = 1
            </if>
            <if test="listCustomerQuery.conditionType != null and listCustomerQuery.conditionType==3">
                AND bc.BUSINESS_CLUES_ID is not null
            </if>
            <if test="listCustomerQuery.conditionType != null and listCustomerQuery.conditionType==4">
                AND buc.BUSSINESS_CHANCE_ID is not null
                AND buc.MERGE_STATUS IN (0,2)
            </if>
            <if test="listCustomerQuery.conditionType != null and listCustomerQuery.conditionType==5">
                AND quo.VALID_STATUS = 1
            </if>
            <if test="listCustomerQuery.conditionType != null and listCustomerQuery.conditionType==6">
                AND sa.VALID_STATUS =1
                AND sa.PAYMENT_STATUS = 2
            </if>
        </where>
        <if test="listCustomerQuery.conditionType != null and (listCustomerQuery.conditionType==1 or listCustomerQuery.conditionType==2 or listCustomerQuery.conditionType==3 or listCustomerQuery.conditionType==4 )">
            GROUP BY pcr.PUBLIC_CUSTOMER_RECORD_ID
        </if>
        <if test="listCustomerQuery.conditionType != null and listCustomerQuery.conditionType==5">
            GROUP BY pcr.PUBLIC_CUSTOMER_RECORD_ID  HAVING count(quo.QUOTEORDER_ID)>0
        </if>
        <if test="listCustomerQuery.conditionType != null and listCustomerQuery.conditionType==6">
            GROUP BY pcr.PUBLIC_CUSTOMER_RECORD_ID  HAVING count(sa.SALEORDER_ID)>0
        </if>
        ORDER BY pcr.ADD_TIME DESC,pcr.MOD_TIME DESC
    </select>
    <select id="selectRelatedIdByAssociatedCustomerGroup" resultType="java.lang.Integer">
        select TRADER_CUSTOMER_ID
        from T_TRADER_CUSTOMER
        where ASSOCIATED_CUSTOMER_GROUP = #{associatedCustomerGroup,jdbcType=INTEGER}
    </select>
    <update id="updatePublicCustomerEarlyWarningCount">
        UPDATE T_TRADER_CUSTOMER
        SET PUBLIC_CUSTOMER_EARLY_WARNING_COUNT = PUBLIC_CUSTOMER_EARLY_WARNING_COUNT + 1
        WHERE
            TRADER_CUSTOMER_ID IN
        <foreach collection="traderCustomerIds" separator="," open="(" close=")" item="traderCustomerId">
            #{traderCustomerId,jdbcType=INTEGER}
        </foreach>
    </update>

    <update id="clearRelationBetweenTraderCustomer">
        UPDATE T_TRADER_CUSTOMER SET ASSOCIATED_CUSTOMER_GROUP = 0 WHERE TRADER_ID = #{traderId,jdbcType=INTEGER}
    </update>



    <update id="resetCustomerEarlyWarningCount">
        UPDATE T_TRADER_CUSTOMER
        SET PUBLIC_CUSTOMER_EARLY_WARNING_COUNT = 0
        WHERE
            TRADER_CUSTOMER_ID IN
        <foreach collection="customerIds" separator="," open="(" close=")" item="customerId">
            #{customerId,jdbcType=INTEGER}
        </foreach>
    </update>


    <select id="getTraderCountByAssociatedCustomerGroup"
            resultType="com.vedeng.order.model.dto.AssociatedCustomerGroupCountDto">
        SELECT
            ASSOCIATED_CUSTOMER_GROUP,
            COUNT( 1 ) MYCOUNT
        FROM
            T_TRADER_CUSTOMER
        WHERE
            ASSOCIATED_CUSTOMER_GROUP IN
        <foreach collection="associatedCustomerGroupList" separator="," open="(" close=")" item="associatedCustomerGroup">
            #{associatedCustomerGroup,jdbcType=BIGINT}
        </foreach>
        GROUP BY
            ASSOCIATED_CUSTOMER_GROUP
    </select>
    <select id="getTraderCustomerListByAssociatedCustomerGroup" resultType="com.vedeng.trader.model.TraderCustomer">
        select *
        from T_TRADER_CUSTOMER where ASSOCIATED_CUSTOMER_GROUP = #{associatedCustomerGroup}
    </select>
    <select id="getTraderBelongByCustomerId" resultType="java.lang.Integer">
        SELECT
            T.BELONG_PLATFORM
        FROM
            T_TRADER T
            LEFT JOIN T_TRADER_CUSTOMER TC ON TC.TRADER_ID = T.TRADER_ID
        WHERE
            TC.TRADER_CUSTOMER_ID = #{customerId,jdbcType=INTEGER}
            LIMIT 1

    </select>

    <update id="resetAllWarningCount">
        UPDATE T_TRADER_CUSTOMER
        SET PUBLIC_CUSTOMER_EARLY_WARNING_COUNT = 0
        WHERE
            PUBLIC_CUSTOMER_EARLY_WARNING_COUNT > 0
    </update>

    <select id="getPublicCustomerRecord" resultType="com.vedeng.authorization.model.User">
        select distinct b.USER_ID,b.USERNAME
        from T_PUBLIC_CUSTOMER_RECORD a left  join T_USER b on a.ORIGIN_USER_ID = b.USER_ID
    </select>
    <select id="getTraderBaseInfo" resultType="com.vedeng.trader.model.dto.TraderBaseInfoDto">
        SELECT
            T.TRADER_NAME,
            U.USERNAME AS salesNameStr
        FROM
            T_TRADER T
            LEFT JOIN T_R_TRADER_J_USER TU ON T.TRADER_ID = TU.TRADER_ID
            LEFT JOIN T_USER U ON U.USER_ID = TU.USER_ID
        WHERE
            T.TRADER_ID = #{traderId,jdbcType=INTEGER}
            LIMIT 1
    </select>

    <select id="selectTraderCustomer" resultMap="BaseResultMap">
        select
            E.TRADER_ID
        from
            T_TRADER_CUSTOMER  E
        left join T_TRADER_CUSTOMER_FINANCE A  on A.TRADER_ID = E.TRADER_ID
        where
         A.TRADER_ID = E.TRADER_ID
         and  E.TRADER_ID = #{traderId}
    </select>
    
    <select id="queryAssignInfo" resultType="com.vedeng.trader.model.dto.AssignCustomerInfo">
        select a.TRADER_ID,
               b.TRADER_NAME,
               a.TRADER_CUSTOMER_ID,
               a.CUSTOMER_LEVEL,
               cl.TITLE              as customerLevelStr,
               b.AREA_ID,
               IFNULL(c.buyCount, 0) as buyCount,
               u.USERNAME            as belongUser
        from T_TRADER_CUSTOMER a
                 left join T_TRADER b on a.TRADER_ID = b.TRADER_ID
                 left join T_R_TRADER_J_USER tu on tu.TRADER_ID = b.TRADER_ID
                 left join T_USER u on u.USER_ID = tu.USER_ID
                 left join T_SYS_OPTION_DEFINITION cl on a.CUSTOMER_LEVEL = cl.SYS_OPTION_DEFINITION_ID
                 left join (select TRADER_ID, count(*) buyCount
                            from T_SALEORDER
                            where VALID_STATUS = 1
                              and PAYMENT_STATUS = 2
                              and TRADER_ID = #{traderId}) c on c.TRADER_ID = a.TRADER_ID
        where b.TRADER_ID = #{traderId}
    </select>
</mapper>
