package com.wms.service.processor.output;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.common.util.DateUtil;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.constant.WmsLogicalOperateTypeEnum;
import com.wms.dao.WmsOutputOrderGoodsMapper;
import com.wms.dao.WmsOutputOrderMapper;
import com.wms.dto.OutputDto;
import com.wms.dto.OutputGoodDto;
import com.wms.dto.StockCalculateDto;
import com.wms.model.dto.WmsOutputOrderGoodsDto;
import com.wms.model.po.WmsOutputOrder;
import com.wms.model.po.WmsOutputOrderGoods;
import com.wms.service.WmsInventoryOutService;
import com.wms.service.context.ThreadLocalContext;
import com.wms.service.processor.AbstractOutputOrderProcessor;
import com.wms.service.stockcalculate.SaleorderOutCaculateImpl;
import com.wms.service.util.WmsCommonUtil;
import com.wms.unitconversionorder.dao.WmsUnitConversionOrderItemMapper;
import com.wms.unitconversionorder.dao.WmsUnitConversionOrderMapper;
import com.wms.unitconversionorder.model.dto.WmsUnitConversionOrderDto;
import com.wms.unitconversionorder.model.dto.WmsUnitConversionOrderItemDto;
import com.wms.unitconversionorder.model.entity.WmsUnitConversionOrder;
import com.wms.unitconversionorder.model.entity.WmsUnitConversionOrderItem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 单位转换单出库wms回传
 */
@Service
@Slf4j
public class WmsUnitConversionOutputProcessor extends AbstractOutputOrderProcessor {

    @Autowired
    @Qualifier("saleorderOutCaculateImpl")
    private SaleorderOutCaculateImpl stockinfoCaculateInterface;

    @Autowired
    private WmsUnitConversionOrderMapper wmsUnitConversionOrderMapper;

    @Autowired
    private WmsUnitConversionOrderItemMapper wmsUnitConversionOrderItemMapper;



    @Override
    protected void commonValidator(OutputDto requestBean) throws Exception {
        //  hollis wms 接口标识 todo
        if(WmsInterfaceOrderType.INVENTORY_OUT.equals(requestBean.getOrderType())){
            ThreadLocalContext.put("operateType",StockOperateTypeConst.UNIT_CONVERSION_OUT);
        }
    }

    @Override
    protected void updateOrderData(OutputDto requestBean) throws Exception {

        String orderNo = WmsCommonUtil.getOriginalOrderNo(requestBean.getSOReference1());
        WmsUnitConversionOrder wmsUnitConversionOrder = wmsUnitConversionOrderMapper.selectByWmsUnitConversionOrderNo(orderNo);
        Assert.notNull(wmsUnitConversionOrder, "wms单位转换出库单回传未查此单：" + orderNo);

        Map<Integer, WmsUnitConversionOrderItemDto> wmsOutputOrderGoodsMap = new HashMap<>();
        List<WmsUnitConversionOrderItemDto> wmsUnitConversionOrderItemDtoList = wmsUnitConversionOrderMapper.selectByWmsUnitConversionOrderId(wmsUnitConversionOrder.getWmsUnitConversionOrderId()).getWmsUnitConversionOrderItemDtoList();

        for (WmsUnitConversionOrderItemDto wmsOutputOrderGood : wmsUnitConversionOrderItemDtoList) {
            wmsOutputOrderGoodsMap.put(wmsOutputOrderGood.getWmsUnitConversionOrderItemId(),wmsOutputOrderGood);
        }

        List<OutputGoodDto> details = requestBean.getDetails();
        Map<Integer,Integer> outNumMap = new HashMap<>(16);
        for (OutputGoodDto detail : details) {
            int detailId = getRelateId(detail);
            Integer outNum = outNumMap.get(detailId) == null ? 0 : outNumMap.get(detailId);
            outNumMap.put(detailId,outNum + detail.getQtyShipped().intValue());
        }

        // 匹配 数据，更新出库数据量，出库状态，累加
        for (Integer detailId : outNumMap.keySet()) {
            WmsUnitConversionOrderItemDto wmsOutputOrderGoods = wmsOutputOrderGoodsMap.get(detailId);
            Assert.notNull(wmsOutputOrderGoods, "wms单位转换出库单:" + orderNo + "回传未查到此商品明细:" + detailId);
            Integer outNum = outNumMap.get(detailId);
            WmsUnitConversionOrderItem update = new WmsUnitConversionOrderItem();
            update.setWmsUnitConversionOrderItemId(wmsOutputOrderGoods.getWmsUnitConversionOrderItemId());
            update.setWmsRealOutNum(wmsOutputOrderGoods.getWmsRealOutNum().add(BigDecimal.valueOf(outNum)));
            update.setOutStatus(wmsOutputOrderGoods.getSourceNum().compareTo(update.getWmsRealOutNum())<=0 ? 2 : 1);
            log.info("单位转换出库单：{}明细更新：id:{},data:{}",orderNo,wmsOutputOrderGoods.getWmsUnitConversionOrderItemId(), JSON.toJSONString(update));
            wmsUnitConversionOrderItemMapper.updateByPrimaryKeySelective(update);
        }

    }

    @Override
    protected int getOperateType(OutputDto requestBean) {
        return StockOperateTypeConst.UNIT_CONVERSION_OUT;
    }

    @Override
    protected int getWmsLogicalOperateType() {
        return WmsLogicalOperateTypeEnum.UNIT_CONVERSION_OUT.getOperateTypeCode();
    }

    /**
     *
     * @param outputGoodDto data
     * @return int
     */
    @Override
    protected int getRelateId(OutputGoodDto outputGoodDto) {
        return Integer.parseInt(outputGoodDto.getUserDefine1());
    }

    @Override
    protected List<WarehouseDto> getWarehouseStockList(List<StockCalculateDto> stockCalculateList) {
        return stockinfoCaculateInterface.calculateStockInfo(stockCalculateList);
    }
}
