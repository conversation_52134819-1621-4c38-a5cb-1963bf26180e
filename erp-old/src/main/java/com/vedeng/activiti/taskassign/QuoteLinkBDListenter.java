package com.vedeng.activiti.taskassign;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.constant.BCConstants;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.OrderConstant;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.order.dao.*;
import com.vedeng.order.model.*;
import com.vedeng.order.service.QuoteService;
import com.vedeng.system.service.UserService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <b>Description:</b>报价关联bd订单审核节点通知<br>
 * <b>@Author:calvin</b>
 * <br><b>@Date:</b> 2020/10/14
 */
public class QuoteLinkBDListenter implements ExecutionListener {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private static final Logger logger = LoggerFactory.getLogger(QuoteLinkBDListenter.class);

    // 运行时注入service
    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
    private SaleorderMapper saleorderMapper = (SaleorderMapper) context.getBean("saleorderMapper");
    private SaleorderGoodsMapper saleorderGoodsMapper = (SaleorderGoodsMapper) context.getBean("saleorderGoodsMapper");
    private QuoteorderMapper quoteorderMapper = (QuoteorderMapper) context.getBean("quoteorderMapper");
    private BussinessChanceMapper bussinessChanceMapper=(BussinessChanceMapper)context.getBean("bussinessChanceMapper");
    private QuoteLinkBdLogMapper quoteLinkBdLogMapper=(QuoteLinkBdLogMapper)context.getBean("quoteLinkBdLogMapper");
    private QuoteService quoteService=(QuoteService)context.getBean("quoteService");
    private UserService userService = (UserService) context.getBean("userService");

    private String quoteDetailUrl="./order/quote/getQuoteDetail.do?viewType=3&quoteorderId=";
    @Override
    public void notify(DelegateExecution execution) {
        Integer quoteId=(Integer) execution.getVariable("quoteId");
        Integer logId=(Integer)execution.getVariable("logId");
        Integer orderId=(Integer)execution.getVariable("bdOrderId");
        Integer userId=(Integer)execution.getVariable("creator");

        logger.info("QuoteLinkBDListenter quoteId:{},logId:{},orderId:{},userId:{}", quoteId, logId, orderId, userId);

        if(execution.getCurrentActivityName().equals("审核完成")){
            Saleorder saleorder=new Saleorder();
            saleorder.setSaleorderId(orderId);
            saleorder.setQuoteorderId(quoteId);
            saleorderMapper.updateByPrimaryKeySelective(saleorder);
            Quoteorder quoteorder=new Quoteorder();
            quoteorder.setQuoteorderId(quoteId);
            quoteorder.setLinkBdStatus(OrderConstant.QUOTE_LINK_BD_PASS_STATUS);
            quoteorder.setFollowOrderStatus(ErpConst.ONE);
            quoteorder.setFollowOrderTime(System.currentTimeMillis());
            quoteorderMapper.updateQuote(quoteorder);
            Quoteorder quoteOrderInfo = quoteorderMapper.getQuoteorderById(quoteId);

            logger.info("报价单关联订单审核通过报价单基础信息 quoteOrderInfo:{}", JSON.toJSONString(quoteOrderInfo));
            if(quoteOrderInfo != null){
                BussinessChance businessChanceUpdate = new BussinessChance();
                businessChanceUpdate.setBussinessChanceId(quoteOrderInfo.getBussinessChanceId());
                businessChanceUpdate.setIsLinkBd(ErpConst.ONE);
                businessChanceUpdate.setStatus(BCConstants.BC_ORDERING);
                bussinessChanceMapper.updateByPrimaryKeySelective(businessChanceUpdate);
            }
            saveQuoteorderGoods(quoteId,orderId);
            sendMessage(141,quoteId,orderId,userId);
        }else{
            Quoteorder quoteorder=new Quoteorder();
            quoteorder.setQuoteorderId(quoteId);
            quoteorder.setLinkBdStatus(OrderConstant.QUOTE_LINK_BD_REJECT_STATUS);
            quoteorderMapper.updateQuote(quoteorder);
            QuoteLinkBdLog log=new QuoteLinkBdLog();
            log.setIsEnable(ErpConst.ZERO);
            log.setUpdateTime(System.currentTimeMillis());
            log.setQuoteLinkBdLogId(logId);
            quoteLinkBdLogMapper.updateByPrimaryKeySelective(log);
            sendMessage(142,quoteId,orderId,userId);
        }
    }

    /**
     * <b>Description:</b>发送消息给用户<br>
     * @param
     * @return
     * @Note
     * <b>@Author:calvin</b>
     * <br><b>@Date:</b> 2020/10/16
     */
    private void sendMessage(Integer msgId,Integer quoteId,Integer orderId,Integer userId){
        List<Integer> userIds=new ArrayList<>();
        userIds.add(userId);
        String url=quoteDetailUrl+quoteId;
        Saleorder msgOrder=saleorderMapper.getSaleOrderById(orderId);
        Map<String,String> param=new HashMap<>();
        param.put("orderNo",msgOrder==null?"":msgOrder.getSaleorderNo());
        MessageUtil.sendMessage(msgId,userIds,param,url,null);
    }
    /**
     * <b>Description:</b>同步bd订单商品到报价单商品中<br>
     * @param
     * @return
     * @Note
     * <b>@Author:calvin</b>
     * <br><b>@Date:</b> 2020/10/16
     */
    private void saveQuoteorderGoods(Integer quoteId,Integer orderId){
//        quoteorderMapper.deleteGoodsByQuoteId(quoteId);
        Saleorder queryOrder=new Saleorder();
        queryOrder.setSaleorderId(orderId);
        List<SaleorderGoods> orderGoodsList=saleorderGoodsMapper.getSaleordergoodsList(queryOrder);
        List<QuoteorderGoods> quoteGoodsList=quoteorderMapper.getQuoteorderGoodsByIdsample(quoteId);
        boolean isExist;
        for(SaleorderGoods sg:orderGoodsList){
            isExist=false;
            for(QuoteorderGoods qg:quoteGoodsList){
                if(sg.getSku().equals(qg.getSku())){
                    isExist=true;
                    BeanUtils.copyProperties(sg,qg);
                    quoteService.updateQuoteGoodsReal(qg);
                }
            }
            if(!isExist){
                QuoteorderGoods qog=new QuoteorderGoods();
                BeanUtils.copyProperties(sg,qog);
                qog.setQuoteorderId(quoteId);
                quoteService.saveQuoteGoodsReal(qog);
            }
        }
    }
}
