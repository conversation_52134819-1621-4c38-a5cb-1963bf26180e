$(function(){

    $("#showFollowUp").click(function(){
        checkLogin();
        var earlyWarningTaskIds = "";
        var totalPrice=0
        $.each($("input[name='checkOne']"),function(i,n){
            if($(this).prop("checked")){
                earlyWarningTaskIds += $(this).siblings("input[name='earlyWarningTaskId']").val()+",";
                totalPrice+=Number((parseFloat($(this).siblings("input[name='urgingTicketAmount']").val())).toFixed(2));
            }
        });
        if(earlyWarningTaskIds==""||totalPrice==0){
            layer.alert("未选择任何信息");
            return false;
        }
        earlyWarningTaskIds = earlyWarningTaskIds.substring(0,earlyWarningTaskIds.length-1);
        var url = page_url+"/flash/earlyWarningTicksTask/showFollowUp.do?earlyWarningTaskIds="+earlyWarningTaskIds+"&totalPrice="+totalPrice;
        $("#followUoView").attr('layerParams','{"width":"1200px","height":"500px","title":"跟进","link":"'+url+'"}');
        $("#followUoView").click();
        layer.close(index);
    })

});

function selectAll(obj) {
    if($(obj).is(":checked")){
        $("input[type='checkbox']").not(':disabled').prop("checked",true);
    }else{
        $("input[type='checkbox']").not(':disabled').prop('checked',false);
    }
}
function changeTab(index) {
    checkLogin();
    let userIds = $('#userIds').val();
    window.location = "/flash/earlyWarningTicksTask/earlyWarningTicksTask.do?userIds="+escape(userIds)+'&tabFlag=' + index;
}
