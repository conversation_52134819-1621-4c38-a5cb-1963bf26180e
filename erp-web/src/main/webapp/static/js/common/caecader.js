void function () {
    var defaults = {
        el: '.J-caecader',
        value: null,
        onchange: function () {},
        async: false,
        url: '',
        data: [],
        input: '.J-select-address',
        placeholder: '',
        filterable: false, // 可搜索
        level: 3, // 选项层级
    };

    var Caecader = function (config) {
        this.config = $.extend({}, defaults, config);
        this.__init();
        return this;
    }

    Caecader.prototype = {
        constructor: 'Caecader',

        // mounted
        __init: function () {
            this.$wrapper = $(this.config.el);
            this.$input = $(this.config.input);
            this.onCompressing = false; // 中文输入

            // 已选项
            this.lv1Data = {};
            this.lv2Data = {};
            this.lv3Data = {};

            var _this = this;
            $.ajax({
                url: this.config.url,
                dataType: 'json',
                success: function (res) {
                    if (res.code == 0) {
                        if (_this.config.parseData) {
                            _this.data = _this.config.parseData(res);
                        } else {
                            _this.data = res.data;
                        }

                        // 不足三个层级-数据处理
                        if (_this.config.level < 3) {
                            _this.data.forEach((lv1) => {
                                if (_this.config.level == 1) {
                                    delete lv1.children;
                                } else if (_this.config.level == 2) {
                                    if (lv1.children && lv1.children.length) {
                                        lv1.children.forEach(lv2 => {
                                            delete lv2.children;
                                        })
                                    }
                                }
                            })
                        }

                        _this.__initTmpl();
                        _this.__bindEvent();
                    }
                }
            })
        },

        // initHtml
        __initTmpl: function () {

            // 选择面板
            var Li_s = [];
            this.data.forEach((item=> {
                Li_s.push(`
                    <li class="select-opt J-select-lv1" data-id="${item.value}">
                        <p>${item.label}</p>
                        <div class="J-opt-icon">
                            ${ item.children && item.children.length ? `<i class="vd-icon icon-right"></i>` : '<i class="vd-icon icon-selected2"></i>' }
                        </div>
                    </li>
                `);
            }))

            var panel = `
                <div class="vd-ui-cascader-modal J-modal">
                    <div class="suggestion-list J-filter-wrap" v-if="filtering">
                        <!-- <template v-if="loading">
                            <div class="loading-li" :style="{'width':width}">
                                <p>
                                    <i class="vd-icon icon-loading" ref="loading"></i>
                                    <span>加载中...</span>
                                </p>
                            </div>
                        </template>
                        <div class="filter-list scrollbar">
                            <div class="filter-item" @click.stop="handleSuggestionClick(item)">
                                <div v-html="suggestShow(item)"></div>
                            </div>
                        </div>
                        <div class="no-filter" v-else>无匹配数据</div> -->
                    </div>
                    <div class="select-wrap J-select-list">
                        <ul class="vd-ui-cascader-node scrollbar J-lv1-wrap">
                            ${Li_s.join('')}
                        </ul>
                    </div>
                </div>
            `

            var wrap = `
                <div class="vd-ui-cascader J-ui-cascader" @mouseenter="handleEnter" @mouseleave="handleLeave" >
                    <div class="vd-ui-cascader-wrapper J-wrapper" @click="handleClick">
                        <div class="vd-ui-input">
                            <input 
                                class="J-input test"
                                placeholder="${this.config.placeholder}" 
                                autocomplete="off"
                            />
                            <div class="icon J-icon">
                                ${
                                    this.config.filterable ? 
                                    `<i slot="suffix" class="vd-icon icon-search"></i>` : 
                                    `<i slot="suffix" class="vd-icon icon-down"></i>`
                                }
                            </div>
                        </div>
                    </div>
                    ${panel}
                </div>`;

            this.$wrapper.append(wrap);
            this.$value = this.$wrapper.find('.J-input');
            if (this.$input.val().trim()) {
                this.__initValue();
            }
        },

        // methods
        __bindEvent: function () {
            var _this = this;

            this.$wrapper.on('click', '.J-wrapper', function () {
                if (_this.$wrapper.find('.J-ui-cascader .J-input').hasClass('focus')) {
                    return;
                } else {
                    $(document).click();
                    _this.__showList();
                }
            })

            this.$wrapper.on('click', '.J-select-lv1', function (event) {
                _this.$wrapper.find('.J-lv2-wrap, .J-lv3-wrap').remove();
                _this.lv2Data = {};
                _this.lv3Data = {};
                _this.__getData($(this).data('id'), 1);
                event.stopPropagation();
            })
            this.$wrapper.on('click', '.J-select-lv2', function (event) {
                _this.$wrapper.find('.J-lv3-wrap').remove();
                _this.lv3Data = {};
                _this.__getData($(this).data('id'), 2);
                event.stopPropagation();
            })
            this.$wrapper.on('click', '.J-select-lv3', function (event) {
                _this.__getData($(this).data('id'), 3);
                _this.__setValue();
                event.stopPropagation();
            });

            this.$wrapper.on('click', '.J-lv-clear', function (event) {
                _this.__clear();
                event.stopPropagation();
            });

            // 外面值异步改变 手动触发change事件
            this.$input[0].triggerChange = function(){
                _this.lv1Data = {};
                _this.lv2Data = {};
                _this.lv3Data = {};
                _this.$wrapper.find('.selected').removeClass('selected');
                _this.$wrapper.find('.J-lv2-wrap, .J-lv3-wrap').remove();
                _this.__initValue();
            }

            $(document).click(function () {
                _this.__hideList();
            })

            this.$wrapper.click(function (e) {
                e.stopPropagation();
            })

            // 移入cascader展示clear图标 
            this.$wrapper.on('mouseenter', '.J-ui-cascader', function () {
                if (_this.$value.val()) {
                    // 展示clear
                    _this.$wrapper.find('.J-icon').html(`<i slot="suffix" class="vd-icon icon-error2 J-lv-clear"></i>`)
                }
            })
            this.$wrapper.on('mouseleave', '.J-ui-cascader', function () {
                if (_this.config.filterable) {
                    _this.$wrapper.find('.J-icon').html(`<i slot="suffix" class="vd-icon icon-search"></i>`);
                } else {
                    _this.$wrapper.find('.J-icon').html(`<i slot="suffix" class="vd-icon icon-down"></i>`);
                }
            })


            /** 搜索相关 **/
            this.$wrapper.on('blur', '.J-ui-cascader .J-input', function (e) {
                setTimeout(()=> {
                    // 搜索面板blur后恢复原值 setTime等待点击事件修改完值
                    if (_this.$wrapper.find('.J-filter-wrap').css('display') !== 'none' || ((_this.$value.val() == '' && _this.lv1Data.label && _this.lv2Data.label && _this.lv3Data.label))) {
                        _this.__hideFilter();
                        _this.__setValue(); // blur后恢复原值
                    }
                }, 300);
            })
            this.$wrapper.on('keydown', '.J-ui-cascader .J-input', function (e) {
                if (e.key === "Tab" || e.keyCode === 9) {
                    _this.__hideFilter();
                    _this.__hideList();
                    // 阻止默认的 Tab 行为（如焦点切换）
                    e.preventDefault();
                }
            })
            this.$wrapper.on('input', '.J-ui-cascader .J-input', function (e) {
                if (_this.onCompressing) {
                    return;
                }

                let val = e.target.value;
                _this.__handlerInput(val.trim());
            })
            this.$wrapper.on('compositionstart', '.J-ui-cascader .J-input', function (e) {
                _this.onCompressing = true;
            })
            this.$wrapper.on('compositionend', '.J-ui-cascader .J-input', function (e) {
                _this.onCompressing = false;
                if (e.target.value.trim()) {
                    _this.__handlerInput(e.target.value.trim());
                }
            })
            this.$wrapper.on('click', '.J-filter-item', function (event) {
                let index = $(this).data('index');
                let filterItem = _this.filterResult[index] || {};
                _this.lv1Data = filterItem.l1 || {};
                let lv2 = filterItem.l2 || {};
                let lv3 = filterItem.l3 || {};

                if (_this.lv1Data.label && _this.lv1Data.value) {
                    _this.$wrapper.find('.J-lv2-wrap, .J-lv3-wrap').remove();
                    _this.lv2Data = {};
                    _this.lv3Data = {};
                    _this.__getData(_this.lv1Data.value, 1);
                }
                if (lv2.label && lv2.value) {
                    _this.$wrapper.find('.J-lv3-wrap').remove();
                    _this.lv3Data = {};
                    _this.__getData(lv2.value, 2);
                }
                if (lv3.label && lv3.value) {
                    _this.__getData(lv3.value, 3);
                    _this.__setValue();
                }

                _this.__hideFilter();
                event.stopPropagation();
            })
        },

        __showList: function () {
            //右侧位置不够，向左延伸
            this.$wrapper.find('.J-ui-cascader .J-input').addClass('focus');
            this.$wrapper.find('.J-modal').slideDown(220).css('display', 'inline-flex');
            this.__scroll();
        },
        __hideList: function () {
            this.$wrapper.find('.J-ui-cascader .J-input').removeClass('focus');
            this.$wrapper.find('.J-modal').slideUp(190);
        },
        __getData: function (id, lv) {
            var _this = this;
            var data = [this.data, this.lv1Data.children, this.lv2Data.children][lv - 1];

            $.each(data, function (i, obj) {
                if (id == obj.value) {
                    _this['lv' + lv + 'Data'] = obj;
                }
            })

            var childData = _this['lv' + lv + 'Data'].children;

            if (lv < 3) {
                if (childData && childData.length) {
                    let childLi = [];
                    childData.forEach((item) => {
                        childLi.push(`
                            <li class="select-opt J-select-lv${lv + 1}" data-id="${item.value}">
                                <p>${item.label}</p>
                                <div class="J-opt-icon">
                                    ${ item.children && item.children.length ? `<i class="vd-icon icon-right"></i>` : '<i class="vd-icon icon-selected2"></i>' }
                                </div>
                            </li>
                        `)
                    })

                    let childWrap = `<ul class="vd-ui-cascader-node scrollbar J-lv${lv + 1}-wrap">
                                    ${childLi.join('')}
                                </ul>`;

                    $('.J-select-list', _this.$wrapper).append(childWrap);
                } else {
                    _this.__setValue();
                }
            }

            this.__resetDom(id, lv);
        },
        // 重置选中状态
        __resetDom: function (id, lv) {
            var $selector = $('.J-select-lv' + lv, this.$wrapper);

            $selector.each(function () {
                $(this).removeClass('selected');
                if ($(this).data('id') == id) {
                    $(this).addClass('selected');
                    $('.J-aa').find('.vd-icon').length;
                }
            });
        },
        // 有初始值
        __initValue: function () {
            var _this = this;
            var value = this.$input.val();

            if(!value){
                this.__clear();
                return;
            }

            var initVal = [null, null, null];
            $.each(this.data, function (i, lv1) {
                if (lv1.value == value) {
                    initVal[0] = lv1.value;
                    initVal[1] = "";
                }
                $.each(lv1.children, function (ii, lv2) {
                    if (lv2.value == value) {
                        initVal[0] = lv1.value;
                        initVal[1] = lv2.value;
                        initVal[2] = "";
                    }

                    $.each(lv2.children, function (iii, lv3) {
                        if (lv3.value == value) {
                            initVal[0] = lv1.value;
                            initVal[1] = lv2.value;
                            initVal[2] = lv3.value;
                        }
                    })
                })
            })

            if (!initVal.join('')) {
                this.__clear();
                return;
            }

            $.each(initVal, function (i, item) {
                if (item !== null) {
                    _this.__getData(item, i + 1);
                }
            })

            this.__setValue();
        },

        __scroll: function () {
            this.$wrapper.find('.vd-ui-cascader-node').each(function () {
                var $selected = $(this).find('.selected');

                if ($selected && $selected.length) {
                    $(this).scrollTop($selected[0].offsetTop);
                }
            })
        },
        __setValue: function () {
            var label = (this.lv1Data.label || '') + (this.lv2Data.label ? ' / ' + this.lv2Data.label : '') + (this.lv3Data.label ? ' / ' + this.lv3Data.label : '');
            var value = this.lv3Data.value || this.lv2Data.value || this.lv1Data.value;
            this.$value.val(label || '');
            this.$input.val(value || '');
            this.config.onchange([this.lv1Data.label, this.lv2Data.label, this.lv3Data.label], [this.lv1Data.value, this.lv2Data.value, this.lv3Data.value]);
            this.__hideList();
        },
        __clear: function () {
            this.$value.val('');
            this.lv1Data = {};
            this.lv2Data = {};
            this.lv3Data = {};
            this.config.onchange([], []);
            this.$wrapper.find('.selected').removeClass('selected');
            this.$wrapper.find('.J-lv2-wrap, .J-lv3-wrap').remove();
            this.__hideList();
        },


        /** 搜索相关 */
        __handlerInput: function (val) {
            if (!val.trim()) {
                this.__hideFilter();
                return;
            };

            this.__drawFilter(val);
        },
        __hideFilter: function () {
            this.$wrapper.find('.J-filter-wrap').fadeOut(190).html('');
            this.$wrapper.find('.J-select-list').fadeIn(220);
            this.$wrapper.find('.J-modal').css('width', 'auto');
        },
        // 搜索面板
        __drawFilter: function (val) {
            this.filterResult = this.__getSuggestions(val);

            this.$wrapper.find('.J-filter-wrap').fadeIn(220).html('');
            this.$wrapper.find('.J-select-list').fadeOut(190);
            this.$wrapper.find('.J-modal').css('width', '100%');

            if (this.filterResult.length) {
                list = [];
                this.filterResult.forEach((item, index)=> {
                    list.push(`
                        <div class="filter-item J-filter-item" data-index="${index}">
                            <div>${this.__suggestShow(item)}</div>
                        </div>
                    `);
                })

                this.$wrapper.find('.J-filter-wrap').html(`
                    <div class="filter-list scrollbar">
                        ${list.join('')}
                    </div>
                `);
            } else {
                this.$wrapper.find('.J-filter-wrap').html('<div class="no-filter">无匹配数据</div>');
            }
        },
        __suggestShow(item) {
            let str = '';
            if (item.l1) {
                str += item.l1.label;
            }
            if (item.l2) {
                str += ` / ${item.l2.label}`;
            }
            if (item.l3) {
                str += ` / ${item.l3.label}`;
            }

            let val = this.$wrapper.find('.J-ui-cascader .J-input').val();
            const keywords = this.__getSuggestionWord(val.trim());
            keywords.sort((a, b) => b.length - a.length);
            for (const keyword of keywords) {
                const regExp = new RegExp(keyword, 'g');
                str = str.replace(regExp, `<font color='#FF6600'">${keyword}</font>`);
            }
            return str;
        },
        // 搜索匹配结果
        __getSuggestions: function (val) {
            let words = this.__getSuggestionWord(val);
            let searchRes = []; // 匹配结果

            if (words.length > 1) {
                this.data.forEach((L1, i1) => {
                    // 匹配到第一级， 注: words第一级 不一定是 list第一级别
                    let level = words.length;
                    if (L1.label.includes(words[0])) {
                        // 继续匹配下一级
                        level--;
                        if (level && L1.children && L1.children.length) {
                            L1.children.forEach((L2, i2) => {
                                if (L2.label.includes(words[words.length - level])) {
                                    level--;
                                    if (L2.children && L2.children.length) {
                                        if (level) {
                                            L2.children.forEach((L3, i3)=> {
                                                if (L3.label.includes(words[words.length - level])) {
                                                    searchRes.push({
                                                        l1: L1,
                                                        l2: L2,
                                                        l3: L3
                                                    })
                                                }
                                            })
                                        } else {
                                            L2.children.forEach(L3=> {
                                                searchRes.push({
                                                    l1: L1,
                                                    l2: L2,
                                                    l3: L3
                                                })
                                            })
                                        }
                                    } else {
                                        searchRes.push({
                                            l1: L1,
                                            l2: L2,
                                        })
                                    }
                                }
                            })
                        }
                    } else {
                        // 一级没匹配到, 继续从第二级比较
                        L1.children && L1.children.length && L1.children.forEach(L2 => {
                            if (L2.label.includes(words[0])) {
                                level--;
                                if (level && L2.children && L2.children.length) {
                                    L2.children.forEach(L3 => {
                                        if (L3.label.includes(words[words.length - level])) {
                                            searchRes.push({
                                                l1: L1,
                                                l2: L2,
                                                l3: L3
                                            })
                                        }
                                    })
                                }
                            }
                        })
                    }
                })
            } else if (words.length == 1) {
                let word = words[0].trim();

                this.data.forEach(L1 => {
                    // 一级匹配, 则匹配结果包含所有子集
                    if (L1.label.includes(word)) {
                        if (L1.children) {
                            if (L1.children && L1.children.length) {
                                L1.children.forEach(L2 => {
                                    if (L2.children && L2.children.length) {
                                        L2.children.forEach(L3 => {
                                            searchRes.push({
                                                l1: L1,
                                                l2: L2,
                                                l3: L3
                                            })
                                        })
                                    } else {
                                        searchRes.push({
                                            l1: L1,
                                            l2: L2
                                        })
                                    }
                                })
                            } else {
                                searchRes.push({
                                    l1: L1,
                                })
                            }
                        }
                    }
                    // 一级不匹配, 继续轮循下面二级
                    else {
                        L1.children && L1.children.length && L1.children.forEach(L2 => {
                            if (L2.label.includes(word)) {
                                if (L2.children && L2.children.length) {
                                    L2.children.forEach(L3 => {
                                        searchRes.push({
                                            l1: L1,
                                            l2: L2,
                                            l3: L3
                                        })
                                    })
                                } else {
                                    searchRes.push({
                                        l1: L1,
                                        l2: L2
                                    })
                                }
                            } 
                            // 二级不匹配, 继续轮循下面三级
                            else {
                                L2.children && L2.children.length && L2.children.forEach(L3 => {
                                    if (L3.label.includes(word)) {
                                        searchRes.push({
                                            l1: L1,
                                            l2: L2,
                                            l3: L3
                                        })
                                    }
                                })
                            }
                        })
                    }
                })
            }

            if (searchRes.length > 100) {
                searchRes = searchRes.slice(0, 100);
            }
            return searchRes;
        },
        __getSuggestionWord: function (val) {
            let splitArr = val.split('/');
            let words = [];
            if (splitArr.length > 1) {
                splitArr.forEach(item=> {
                    if (item.trim()) {
                        words.push(item.trim());
                    }
                })
            } else {
                words.push(splitArr[0].trim());
            }
            return words;
        },
    }

    window.Caecader = Caecader;

}.call(this);