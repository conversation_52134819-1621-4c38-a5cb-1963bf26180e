<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
            + path + "/";
%>
<c:set var="path" value="<%=basePath%>" scope="application" />
<link rel="stylesheet" href="<%=basePath%>static/css/general.css?rnd=${resourceVersionKey}" />
<link rel="stylesheet" href="<%=basePath%>static/css/manage.css" />

<style>
    .radio-item {
        display: block;
        margin-bottom: 15px;
        margin-inline: 25px;
        margin-top: 15px;
    }
</style>
<div id="app">
        <el-card class="box-card">
            <div slot="header" class="clearfix">
                <span>{{name}}</span>
            </div>
            <el-table
                    :data="auditRecordList"
                    border
                    style="width: 100%">
                <el-table-column
                        prop="auditUsername"
                        label="操作人"
                >
                </el-table-column>
                <el-table-column
                        label="操作时间"
                >
                    <template slot-scope="scope">
                        <span>{{parseTime(scope.row.addTime)}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="操作事项"
                        width="180">
                    <template slot-scope="scope">
                    <span >
                            {{scope.row.auditStatus==0?'提交审核':scope.row.auditStatus==1?'审核通过':scope.row.auditStatus==2?'审核不通过':''}}
                    </span>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="comments"
                        label="备注">
                </el-table-column>
            </el-table>
        </el-card>

</div>

<script src="${pageContext.request.contextPath}/static/api/trader/traderCustomerTerminal.js?rnd=${resourceVersionKey}"></script>

<script type="text/javascript">
    const queryData = '${traderCustomerTerminalId}';
    const viewName = '${name}';
    const sendThis = (_this) => {
        vm = _this;
    }
    new Vue({
        el: '#app',
        data() {
            return {
                auditRecordList: [],
                name:''
            }
        },

        mounted() {
            loadingApp()
        },

        created() {
            sendThis(this);

            this.name = viewName;
            getAuditRecordList({"traderCustomerTerminalId": queryData}).then(res => {
                this.auditRecordList = res.data.data;
            })
        },

        methods: {
            scrollTo(ref){
                let top = this.$refs[ref].$el.offsetTop;
                window.scrollTo(0, top);
            }
        }
    })
</script>
