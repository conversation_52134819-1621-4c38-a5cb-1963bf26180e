package com.wms.model.ddi.generate;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * T_WAREHOUSE_GOODS_OPERATE_LOG
 * <AUTHOR>
@Data
public class VWarehouseGoodsOperateLog implements Serializable {
    private Integer warehouseGoodsOperateLogId;

    /**
     * 产品条码ID
     */
    private Integer barcodeId;

    /**
     * 公司ID
     */
    private Integer companyId;

    /**
     * 操作类型1入库 2出库3销售换货入库4销售换货出库5销售退货入库6采购退货出库7采购换货出库8采购换货入库 9外借入库 10外借出库 11 调整盘盈入库  12盘盈入库
     */
    private Byte operateType;

    /**
     * 关联采购、销售、售后产品ID
     */
    private Integer relatedId;

    /**
     * 出库时对应拣货单详细ID
     */
    private Integer warehousePickingDetailId;

    /**
     * 商品ID
     */
    private Integer goodsId;

    /**
     * 厂商条码
     */
    private String barcodeFactory;

    /**
     * 产品数量
     */
    private Integer num;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 库房ID
     */
    private Integer storageRoomId;

    /**
     * 货区ID
     */
    private Integer storageAreaId;

    /**
     * 库位ID
     */
    private Integer storageLocationId;

    /**
     * 货架ID
     */
    private Integer storageRackId;

    /**
     * 批次号
     */
    private String batchNumber;

    /**
     * 效期
     */
    private Long expirationDate;

    /**
     * 入库验收0未验收1验收通过2不通过
     */
    private Boolean checkStatus;

    /**
     * 入库验收人
     */
    private Integer checkStatusUser;

    /**
     * 入库时间
     */
    private Long checkStatusTime;

    /**
     * 出库复核0未复核1通过2不通过
     */
    private Boolean recheckStatus;

    /**
     * 出库复核人
     */
    private Integer recheckStatusUser;

    /**
     * 出库复核时间
     */
    private Long recheckStatusTime;

    /**
     * 备注
     */
    private String comments;

    /**
     * 是否有效 0否 1是
     */
    private Boolean isEnable;

    /**
     * 是否已绑定快递0否 1是
     */
    private Boolean isExpress;

    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private Long modTime;

    /**
     * 更新人
     */
    private Integer updater;

    /**
     * 0 否  1 是 
     */
    private Boolean isProblem;

    /**
     * 问题备注
     */
    private String problemRemark;

    /**
     * 生产日期
     */
    private Long productDate;

    /**
     * 成本价
     */
    private BigDecimal costPrice;

    /**
     * 入库条码是否使用  0未使用 1使用
     */
    private Boolean isUse;

    /**
     * 逻辑仓id 
     */
    private Integer logicalWarehouseId;

    /**
     * 贝登批次码
     */
    private String vedengBatchNumer;

    /**
     * 剩余库存数量
     */
    private Integer lastStockNum;

    /**
     * 灭菌批号
     */
    private String sterilzationBatchNumber;

    /**
     * 日志类型  0入库  1出库
     */
    private Boolean logType;

    private static final long serialVersionUID = 1L;
}