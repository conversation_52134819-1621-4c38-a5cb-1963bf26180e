<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.trader.dao.WebAccountInvitationLogMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.trader.model.WebAccountInvitationLog">
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="INVITER_MOBILE" jdbcType="CHAR" property="inviterMobile" />
    <result column="INVITEE_TRADER_ID" jdbcType="INTEGER" property="inviteeTraderId" />
    <result column="RERIGTER_PLATFORM" jdbcType="INTEGER" property="rerigterPlatform" />
    <result column="REGISTER_MOBILE" jdbcType="CHAR" property="registerMobile" />
    <result column="CREATE_TIME" jdbcType="BIGINT" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="BIGINT" property="updateTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, INVITER_MOBILE, INVITEE_TRADER_ID, RERIGTER_PLATFORM, REGISTER_MOBILE, CREATE_TIME, 
    UPDATE_TIME, CREATOR, UPDATER, IS_DELETE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_WEB_ACCOUNT_INVITATION_LOG
    where ID = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from T_WEB_ACCOUNT_INVITATION_LOG
    where ID = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.trader.model.WebAccountInvitationLog" useGeneratedKeys="true">
    insert into T_WEB_ACCOUNT_INVITATION_LOG (INVITER_MOBILE, INVITEE_TRADER_ID, RERIGTER_PLATFORM, 
      REGISTER_MOBILE, CREATE_TIME, UPDATE_TIME, 
      CREATOR, UPDATER, IS_DELETE
      )
    values (#{inviterMobile,jdbcType=CHAR}, #{inviteeTraderId,jdbcType=INTEGER}, #{rerigterPlatform,jdbcType=INTEGER}, 
      #{registerMobile,jdbcType=CHAR}, #{createTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}, 
      #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, #{isDelete,jdbcType=BOOLEAN}
      )
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.trader.model.WebAccountInvitationLog" useGeneratedKeys="true">
    insert into T_WEB_ACCOUNT_INVITATION_LOG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="inviterMobile != null">
        INVITER_MOBILE,
      </if>
      <if test="inviteeTraderId != null">
        INVITEE_TRADER_ID,
      </if>
      <if test="rerigterPlatform != null">
        RERIGTER_PLATFORM,
      </if>
      <if test="registerMobile != null">
        REGISTER_MOBILE,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="inviterMobile != null">
        #{inviterMobile,jdbcType=CHAR},
      </if>
      <if test="inviteeTraderId != null">
        #{inviteeTraderId,jdbcType=INTEGER},
      </if>
      <if test="rerigterPlatform != null">
        #{rerigterPlatform,jdbcType=INTEGER},
      </if>
      <if test="registerMobile != null">
        #{registerMobile,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.trader.model.WebAccountInvitationLog">
    update T_WEB_ACCOUNT_INVITATION_LOG
    <set>
      <if test="inviterMobile != null">
        INVITER_MOBILE = #{inviterMobile,jdbcType=CHAR},
      </if>
      <if test="inviteeTraderId != null">
        INVITEE_TRADER_ID = #{inviteeTraderId,jdbcType=INTEGER},
      </if>
      <if test="rerigterPlatform != null">
        RERIGTER_PLATFORM = #{rerigterPlatform,jdbcType=INTEGER},
      </if>
      <if test="registerMobile != null">
        REGISTER_MOBILE = #{registerMobile,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
    </set>
    where ID = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.trader.model.WebAccountInvitationLog">
    update T_WEB_ACCOUNT_INVITATION_LOG
    set INVITER_MOBILE = #{inviterMobile,jdbcType=CHAR},
      INVITEE_TRADER_ID = #{inviteeTraderId,jdbcType=INTEGER},
      RERIGTER_PLATFORM = #{rerigterPlatform,jdbcType=INTEGER},
      REGISTER_MOBILE = #{registerMobile,jdbcType=CHAR},
      CREATE_TIME = #{createTime,jdbcType=BIGINT},
      UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN}
    where ID = #{id,jdbcType=BIGINT}
  </update>
  <select id="countInvited" resultType="java.lang.Integer">
    SELECT COUNT(0) FROM T_WEB_ACCOUNT_INVITATION_LOG WHERE REGISTER_MOBILE =#{registerMobile}
  </select>
</mapper>