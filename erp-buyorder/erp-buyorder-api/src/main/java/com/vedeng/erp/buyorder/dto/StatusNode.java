package com.vedeng.erp.buyorder.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class StatusNode {
    //{"label":"待审核","jump":"#J-test","tip":"审核者：<PERSON><PERSON><PERSON>(李白)","lock":"订单审核中","status":2}
    private String label;

    private Integer status;

    private String jump;

    private String lock;

    private String tip;

    private Integer sort; // 增加节点排序，方便定位修改当前节点前后节点的status

    private Integer fail = 0;

    public StatusNode(String label, Integer status, Integer sort) {
        this.label = label;
        this.status = status;
        this.sort = sort;
    }

    public StatusNode(String label, Integer status) {
        this.label = label;
        this.status = status;
    }

    public  static void changeNodeStatus(List<StatusNode> nodeList, Integer currentStatus) {
        for (StatusNode statusNode : nodeList) {
            if (statusNode != null && statusNode.getSort() != null) {
                if (statusNode.getSort() < currentStatus) {
                    statusNode.setStatus(1);
                } else if (statusNode.getSort().equals(currentStatus)) {
                    statusNode.setStatus(2);
                }
            }
            // 大于时置为0 未进行，默认值是0，因此不需要处理
        }
    }
    /**
     * 根据节点的sort范围值修改对应节点的status为指定值
     */
    public static void changeNodeStatusBySort(List<StatusNode> nodeList, Integer startSort, Integer endSort, Integer status) {
        for (StatusNode statusNode : nodeList) {
            if (statusNode != null && statusNode.getSort() != null) {
                if (startSort <= statusNode.getSort() && statusNode.getSort() <= endSort) {
                    statusNode.setStatus(status);
                }
            }
        }
    }

}
