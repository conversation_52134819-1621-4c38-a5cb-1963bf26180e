package com.vedeng.finance.util;

import com.alibaba.fastjson.JSONObject;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.finance.constant.HxInvoiceConstant;
import com.vedeng.finance.dto.HxInvoiceResponse;
import org.apache.http.Consts;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.http.HttpStatus;
import sun.misc.BASE64Decoder;
import java.io.IOException;
import java.util.Base64;

/**
 * <AUTHOR>
 * @date 2020/08/06
 **/
public class HxHttpRequestUtil {

    public static HxInvoiceResponse hxHttpClientRequest(String url, Object requestDTO) throws IOException {
        HxInvoiceResponse hxInvoiceResponse = new HxInvoiceResponse();
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        String json = JSONObject.toJSONString(requestDTO);
        httpPost.setEntity(new StringEntity(json));
        httpPost.setHeader("Accept","application/json");
        httpPost.setHeader("Content-type","application/json");
        CloseableHttpResponse httpResponse = httpClient.execute(httpPost);
        String responseContent = EntityUtils.toString(httpResponse.getEntity(), Consts.UTF_8);
        if (httpResponse.getStatusLine().getStatusCode() != HttpStatus.OK.value()){
            HxInvoiceResponse.ReturnInfo returnInfo = new HxInvoiceResponse.ReturnInfo();
            returnInfo.setReturnCode(HxInvoiceConstant.HX_RESPONSE_FAIL);
            returnInfo.setReturnMessage(responseContent);
            hxInvoiceResponse.setReturnInfo(returnInfo);
        } else {
            try {
                hxInvoiceResponse = JsonUtils.readValue(responseContent, HxInvoiceResponse.class);
            }catch (Exception e){
                HxInvoiceResponse.ReturnInfo returnInfo = new HxInvoiceResponse.ReturnInfo();
                returnInfo.setReturnCode(HxInvoiceConstant.HX_RESPONSE_FAIL);
                returnInfo.setReturnMessage(responseContent);
                hxInvoiceResponse.setReturnInfo(returnInfo);
            }
        }
        return hxInvoiceResponse;
    }


    public static String base64Decode2String(String base64String){
        byte[] bytes = Base64.getDecoder().decode(base64String.replace("\r\n",""));
        return new String(bytes);
    }


    public static byte[] base64Decode2Img(String base64String) throws IOException {
        BASE64Decoder decoder = new BASE64Decoder();
        byte[] b = decoder.decodeBuffer(base64String.replace("\r\n",""));
        for (int i = 0; i < b.length; i++) {
            if (b[i] < 0){
                b[i] += 256;
            }
        }
        return b;
    }

}
