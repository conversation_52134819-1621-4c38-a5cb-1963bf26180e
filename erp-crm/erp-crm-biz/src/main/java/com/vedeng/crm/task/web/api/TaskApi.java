package com.vedeng.crm.task.web.api;

import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.DataDictionaryDto;
import com.vedeng.common.core.utils.validator.ValidatorUtils;
import com.vedeng.common.core.utils.validator.group.AddGroup;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.crm.business.business.service.CrmBusinessChanceService;
import com.vedeng.crm.business.quote.facade.QuoteFacade;
import com.vedeng.crm.business.web.api.BusinessChanceApi;
import com.vedeng.crm.common.domain.DeadlineRole;
import com.vedeng.crm.task.domain.dto.TaskHandleDto;
import com.vedeng.crm.task.domain.dto.TaskQueryDto;
import com.vedeng.crm.task.domain.dto.TaskUserDto;
import com.vedeng.crm.task.domain.dto.TaskDto;
import com.vedeng.crm.task.domain.vo.MyTaskVo;
import com.vedeng.crm.task.service.TaskService;
import com.vedeng.crm.visitrecord.api.CrmVisitApiService;
import com.vedeng.erp.business.domain.dto.BusinessChanceDto;
import com.vedeng.erp.business.service.BusinessChanceService;
import com.vedeng.erp.trader.service.BusinessChanceApiService;
import com.vedeng.erp.trader.service.QuoteOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @menu crm任务
 * @date 2024/7/30 9:50
 */
@ExceptionController
@RestController
@RequestMapping("/crm/task/profile")
@Slf4j
public class TaskApi {

    @Autowired
    TaskService taskService;

    @Autowired
    QuoteFacade quoteFacade;

    @Autowired
    private CrmVisitApiService crmVisitApiService;

    @Autowired
    private  BusinessChanceService businessChanceService;

    /**
     * 添加任务
     */
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<Void> save(@RequestBody TaskDto taskDto) {
        ValidatorUtils.validate(taskDto, AddGroup.class);
        taskService.save(taskDto);
        return R.success();
    }

    /**
     * 添加任务 - 根据商机获取任务类型
     */
    @RequestMapping(value = "/getTaskType")
    public R<List<DataDictionaryDto>> getTaskType(@RequestParam Integer businessId,@RequestParam Integer type) {
        return R.success(taskService.getTaskType(businessId, type));
    }


    /**
     * 添加任务 - 获取任务候选人员
     */
    @RequestMapping(value = "/getCandidateUser", method = RequestMethod.POST)
    public R<List<DeadlineRole.Department>> getCandidateUser() {
        List<DeadlineRole.Department> user = taskService.getCandidateUser();
        return R.success(user);
    }


    /**
     * 添加任务 - 根据商机阶段获取督导类型
     */
    @RequestMapping(value = "/getSupervisionType", method = RequestMethod.POST)
    public R<List<DataDictionaryDto>> getSupervisionType(@RequestParam Integer businessId) {
        return R.success(taskService.getSupervisionType(businessId));
    }

    /**
     * 列表搜索条件 - 获取发起人,获取待办人
     */
    @RequestMapping(value = "/getCreator", method = RequestMethod.POST)
    public R<List<TaskUserDto>> getCreator(@RequestParam(required = false) String username){
        List<TaskUserDto> user = taskService.getCreator();
        if (StrUtil.isNotEmpty(username)) {
            String lowerCaseUsername = username.toLowerCase();
            user = user.stream().filter(t -> t.getUserName().toLowerCase().contains(lowerCaseUsername)).collect(Collectors.toList());
        }
        return R.success(user);
    }

    /**
     * 列表搜索条件 - 级联获取任务类型
     */
    @RequestMapping(value = "/cascadeGetTaskType", method = RequestMethod.POST)
    public R<List<DataDictionaryDto>> cascadeGetTaskType() {
        return R.success(taskService.cascadeGetTaskType());
    }

    /**
     * 任务列表
     */
    @RequestMapping(value = "/taskPage", method = RequestMethod.POST)
    public R<PageInfo<MyTaskVo>> taskPage(@RequestBody PageParam<TaskQueryDto> taskQueryDto) {
        return R.success(taskService.taskPage(taskQueryDto));
    }

    /**
     * 任务详情
     *
     * @param taskId     我发起的任务id
     * @param taskItemId 我的任务id
     */
    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    public R<MyTaskVo> detail(@RequestBody TaskQueryDto taskQueryDto) {
        MyTaskVo taskVo = taskService.detail(taskQueryDto.getTaskId(), taskQueryDto.getTaskItemId());
        if(taskVo != null && taskVo.getBizType().equals(3)){
            Integer quoteOrderId =  quoteFacade.selectQuoteorderIdByBusinessChanceId(taskVo.getBizId());
            if(quoteOrderId != null){
                taskVo.setBizId(quoteOrderId);
            }
        }
        return R.success(taskVo);
    }

    /**
     * 处理
     */
    @RequestMapping(value = "/handle", method = RequestMethod.POST)
    public R<Void> handle(@RequestBody TaskHandleDto taskHandleDto) {
        ValidatorUtils.validate(taskHandleDto, AddGroup.class);
        taskService.handle(taskHandleDto);
        return R.success();
    }

    /**
     * 处理之前的待办事项
     */
    @RequestMapping(value = "/autoHandle", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<Void> autoHandle(@RequestBody TaskDto taskDto) {
        taskService.handlePreviousTodoTasks(taskDto);
        return R.success();
    }

    /**
     * 处理之前的待办事项
     */
    @RequestMapping(value = "/closeTaskForBusinessChance", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<Void> closeTaskForBusinessChance(@RequestBody TaskDto taskDto) {
        taskService.handleTaskCompleteForBusincessChance(taskDto);
        BusinessChanceDto businessChanceDto = businessChanceService.selectOne(BusinessChanceDto.builder().bussinessChanceId(taskDto.getBizId()).build());
        crmVisitApiService.SendMessageForCheckBusinessChance(businessChanceDto.getBussinessChanceNo(),taskDto.getApiUserName(),"联动关闭-" + businessChanceDto.getClosedComments());
        return R.success();
    }

    /**
     * 获取当前用户待办任务总数
     */
    @RequestMapping(value = "/getCurrentUserTodoCount", method = RequestMethod.POST)
    public R<Integer> getCurrentUserTodoCount(@RequestBody TaskQueryDto taskQueryDto) {
        // 处理状态：0处理中
        taskQueryDto.setDoneStatus(0);
        // 列表类型：1.我的任务列表
        taskQueryDto.setListType(1);
        PageParam<TaskQueryDto> pageParam = new PageParam<>();
        pageParam.setPageNum(1);
        pageParam.setPageSize(1);
        pageParam.setParam(taskQueryDto);
        PageInfo<MyTaskVo> pageInfo = taskService.taskPage(pageParam);
        return R.success(Math.toIntExact(pageInfo.getTotal()));
    }
}
