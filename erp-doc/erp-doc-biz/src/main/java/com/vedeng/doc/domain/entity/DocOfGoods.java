package com.vedeng.doc.domain.entity;


import lombok.Data;

import java.io.Serializable;

/**
 * 资料库商品资料表
 * <AUTHOR>
 * @TableName T_DOC_OF_GOODS
 */
@Data
public class DocOfGoods implements Serializable {
    /**
     * 
     */
    private Integer id;

    /**
     * 商品的SPU ID
     */
    private Integer spuId;

    /**
     * 商品的SKU ID
     */
    private Integer skuId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品型号
     */
    private String model;

    /**
     * 商品资料的标题
     */
    private String docTitle;

    /**
     * 商品归属的三级分类ID
     */
    private Integer thirdCategory;

    /**
     * 商品的品牌ID
     */
    private Integer brandId;

    /**
     * 生产厂商资料ID
     */
    private Integer docSupplierId;

    /**
     * 商品资料的正文
     */
    private String docText;

    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 创建者
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private Long modTime;

    /**
     * 更新人
     */
    private Integer updater;

    /**
     * 是否禁用
     */
    private Integer disable;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 排序规则
     */
    private Integer sortRules;

    private static final long serialVersionUID = 1L;
}