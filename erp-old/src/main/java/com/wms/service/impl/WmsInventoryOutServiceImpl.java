package com.wms.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.authorization.model.Organization;
import com.vedeng.authorization.model.User;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.utils.numgenerator.BillNumGenerator;
import com.vedeng.common.core.utils.numgenerator.bean.BillGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.enums.BillType;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.DateUtil;
import com.vedeng.flash.enums.WmsSendOrderTypeEnum;
import com.vedeng.goods.model.vo.GoodsVo;
import com.vedeng.logistics.model.WarehouseStock;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.system.service.OrgService;
import com.vedeng.system.service.UserService;
import com.wms.constant.LogicalEnum;
import com.wms.constant.WmsOutputOrderTypeConstant;
import com.wms.dao.WmsOutputOrderGoodsMapper;
import com.wms.dao.WmsOutputOrderMapper;
import com.wms.dao.WmsSendOrderMapper;
import com.wms.model.dto.WmsOutputOrderGoodsDto;
import com.wms.model.po.WmsOutputOrder;
import com.wms.model.po.WmsOutputOrderGoods;
import com.wms.model.po.WmsSendOrder;
import com.wms.service.WmsInventoryOutService;
import com.wms.service.chain.Build.StepBuildFactory;
import com.wms.service.chain.HandlerStep;
import com.wms.service.chain.HandlerStepContext;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 盘亏出库实现类
 * @date 2022/9/26 10:29
 **/
@Service
@Slf4j
public class WmsInventoryOutServiceImpl implements WmsInventoryOutService {

    @Autowired
    private OrgService orgService;

    @Autowired
    private UserService userService;

    @Resource
    private WmsOutputOrderMapper wmsOutputOrderMapper;

    @Resource
    private WmsOutputOrderGoodsMapper wmsOutputOrderGoodsMapper;

    @Autowired
    private WarehouseStockService warehouseStockService;


    @Autowired
    private StepBuildFactory stepBuildFactory;

    @Autowired
    private WmsSendOrderMapper wmsSendOrderMapper;


    @Override
    @Transactional(rollbackFor = Throwable.class)
    public WmsOutputOrder saveInventoryOutOrder(WmsOutputOrder wmsOutputOrder) {


        checkData(wmsOutputOrder.getWmsOutputOrderGoods());
        Organization organization = orgService.getOrgByOrgId(wmsOutputOrder.getApplyerDepartmentId());
        wmsOutputOrder.setApplyerDepartment(organization.getOrgName());
        User user = userService.getUserById(wmsOutputOrder.getApplyerId());
        wmsOutputOrder.setApplyer(user.getUsername());
        wmsOutputOrder.setType(WmsOutputOrderTypeConstant.INVENTORY_OUT);
        wmsOutputOrder.setVerifyStatus(1);
        wmsOutputOrder.setOutStatus(0);
        String addTime = DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss");
        wmsOutputOrder.setAddTime(addTime);
        wmsOutputOrder.setUpdateTime(addTime);
        int i = wmsOutputOrderMapper.insertSelective(wmsOutputOrder);

        if(i > 0){
            BillGeneratorBean billGeneratorBean = new BillGeneratorBean(BillType.INVENTORY_OUT_ORDER);
            String orderNo = new BillNumGenerator().distribution(billGeneratorBean);
            WmsOutputOrder updateorder = new WmsOutputOrder();
            wmsOutputOrder.setOrderNo(orderNo);
            updateorder.setOrderNo(orderNo);
            updateorder.setId(wmsOutputOrder.getId());
            wmsOutputOrderMapper.updateByPrimaryKeySelective(updateorder);

            List<WmsOutputOrderGoods> wmsOutputOrderGoods = wmsOutputOrder.getWmsOutputOrderGoods();
            for (WmsOutputOrderGoods wmsOutputOrderGood : wmsOutputOrderGoods) {
                wmsOutputOrderGood.setWmsOutputOrderId(wmsOutputOrder.getId());
                Integer logicalWarehouseId = LogicalEnum.getLogicalWarehouseIdByName(wmsOutputOrderGood.getLogicalName());

                if (logicalWarehouseId == 0) {
                    log.error("未查到逻辑仓id,name:{}",wmsOutputOrderGood.getLogicalName());
                    throw new ServiceException("未查到逻辑仓id");
                }
                wmsOutputOrderGood.setLogicalWarehouseId(logicalWarehouseId);
                wmsOutputOrderGoodsMapper.insertSelective(wmsOutputOrderGood);
            }

        }
        return wmsOutputOrder;
    }

    @Override
    public WmsOutputOrder getInventoryOutById(Long wmsOutPutOrderId) {
        return wmsOutputOrderMapper.selectByPrimaryKey(wmsOutPutOrderId);
    }

    @Override
    public WmsOutputOrder getInventoryOutByOrderNo(String orderNo) {
        return wmsOutputOrderMapper.getInventoryOutByOrderNo(orderNo);
    }

    @Override
    public void updateInventoryOutOrderAuditStatus(Long inventoryOutOrderId, Integer value) {
        WmsOutputOrder update = new WmsOutputOrder();
        update.setId(inventoryOutOrderId);
        update.setVerifyStatus(value);
        wmsOutputOrderMapper.updateByPrimaryKeySelective(update);
    }

    @Override
    public void synchStockAndPutWmsInventoryOutOrder(Long inventoryOutOrderId) throws Exception {
        HandlerStepContext handlerStepContext = new HandlerStepContext();
        handlerStepContext.put("inventoryOutOrderId",inventoryOutOrderId);
        HandlerStep handlerStep = stepBuildFactory.buildInventoryOutOrder();
        handlerStep.dealWith(handlerStepContext);
    }

    @Override
    public List<WmsOutputOrderGoodsDto> getWmsInputOrderGoodsDto(Long inventoryOutOrderId) {
        if (Objects.isNull(inventoryOutOrderId)) {
            return Collections.emptyList();
        }
        return wmsOutputOrderGoodsMapper.queryOutputGoodsByWmsOutPutOrderId(inventoryOutOrderId);
    }

    @Override
    public void checkData(List<WmsOutputOrderGoods> check) {
        Map<String, WarehouseStock> logicalStockMapInfo = warehouseStockService.getLogicalStockMapInfo(check.stream().map(WmsOutputOrderGoods::getSkuNo).distinct().collect(Collectors.toList()));
        for (WmsOutputOrderGoods goods : check) {
            WarehouseStock warehouseStock = logicalStockMapInfo.get(goods.getSkuNo() + LogicalEnum.getLogicalWarehouseIdByName(goods.getLogicalName()));
            if (Objects.nonNull(warehouseStock)) {
                if (warehouseStock.getAvailableStockNum().equals(0)) {
                    log.error("盘库出库单:{}, 当前商品库存不足：{}",JSON.toJSONString(goods), JSON.toJSONString(warehouseStock));
                    throw new ServiceException("盘库出库单 当前商品库存不足");
                }
                if (Objects.nonNull(goods.getOutputNum())) {
                    if (goods.getOutputNum() > warehouseStock.getAvailableStockNum()) {
                        log.error("盘库出库单:{}, 当前商品库存不足：{}",JSON.toJSONString(goods), JSON.toJSONString(warehouseStock));
                        throw new ServiceException("盘库出库单 当前商品库存不足");
                    }
                }
            }
        }
    }
}
