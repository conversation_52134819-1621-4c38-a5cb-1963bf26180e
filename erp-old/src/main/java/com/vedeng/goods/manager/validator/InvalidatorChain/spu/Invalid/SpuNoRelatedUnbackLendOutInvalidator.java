package com.vedeng.goods.manager.validator.InvalidatorChain.spu.Invalid;

import com.google.common.base.CharMatcher;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.goods.exception.InvalidatorChainException;
import com.vedeng.goods.manager.validator.GoodsInvalidatorChain;
import com.wms.dao.WmsOutputOrderMapper;
import com.wms.model.po.WmsOutputOrder;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.vedeng.goods.manager.validator.enums.GoodsInvalidatorErrorEnum.*;

@Service
public class SpuNoRelatedUnbackLendOutInvalidator extends GoodsInvalidatorChain {

    @Resource
    WmsOutputOrderMapper wmsOutputOrderMapper;

    @Override
    public void doInvalid() {
        String spuNo = getSpuNo();
        List<String> skuNoList = getSkuNoList();
        if(CollectionUtils.isEmpty(skuNoList)){
            return ;
        }
        StringBuffer skus = new StringBuffer();
        StringBuffer orders = new StringBuffer();
        for (String skuNo : skuNoList) {
            List<WmsOutputOrder> wmsOutputOrderList = wmsOutputOrderMapper.getUnbackLendOutBySkuNo(skuNo);
            if(CollectionUtils.isNotEmpty(wmsOutputOrderList)) {
                skus.append(skuNo);
                skus.append(",");
                String collect = wmsOutputOrderList.stream().map(e -> e.getOrderNo()).collect(Collectors.joining(","));
                orders.append(collect);
                orders.append(",");
            }
        }
        if(skus.length()>0){
            String tempSkusList = SpuNoRelatedUnbackLendOutInvalidatorError.getMessage().replace("skus", skus.substring(0,skus.length()-1));
            String skusList = tempSkusList.replace("orderNo", orders.substring(0,orders.length()-1));
            throw new InvalidatorChainException(SpuNoRelatedUnbackLendOutInvalidatorError.getCode(), skusList);
        }
    }
}
