package com.vedeng.erp.kingdee.mapstruct;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.kingdee.domain.entity.KingDeeProfitLossEntity;
import com.vedeng.erp.kingdee.dto.KingDeeProfitLossDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeProfitLossDto;
import org.mapstruct.*;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 盈亏单  贝登dto 转 金蝶 command
 * @date
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface KingDeeProfitLossConvertor extends BaseMapStruct<KingDeeProfitLossEntity, KingDeeProfitLossDto> {
	
	/**
     * DTO转Entity
     *
     * @param dto
     * @return
     */
    @Mapping(target = "FBillEntry", source = "FBillEntry", qualifiedByName = "expensesToJsonArray")
    @Override
    KingDeeProfitLossEntity toEntity(KingDeeProfitLossDto dto);

    /**
     * Entity转DTO
     *
     * @param entity
     * @return
     */
    @Mapping(target = "FBillEntry", source = "FBillEntry", qualifiedByName = "expensesJsonArrayToList")
    @Override
    KingDeeProfitLossDto toDto(KingDeeProfitLossEntity entity);
	
    
    /**
     * entity 中JSONArray 转 原对象
     *
     * @param array JSONArray
     * @return List<KingDeePayBillDto> dto中的对象
     */
    @Named("expensesJsonArrayToList")
    default List<KingDeeProfitLossDetailDto> entryJsonArrayToList(JSONArray array) {
        if (CollUtil.isEmpty(array)) {
            return Collections.emptyList();
        }
        return array.toJavaList(KingDeeProfitLossDetailDto.class);
    }

    /**
     * dto 原对象中 转 JSONArray
     *
     * @param source 对象
     * @return JSONArray JSONArray
     */
    @Named("expensesToJsonArray")
    default JSONArray entryListToJsonArray(List<KingDeeProfitLossDetailDto> source) {
        if (CollUtil.isEmpty(source)) {
            return null;
        }
        return JSONArray.parseArray(JSON.toJSONString(source));
    }
    
}
