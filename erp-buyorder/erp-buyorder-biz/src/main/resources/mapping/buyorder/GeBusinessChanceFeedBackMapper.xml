<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.erp.buyorder.mapper.GeBusinessChanceFeedBackMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.erp.buyorder.domain.entity.GeBusinessChanceFeedBack" >
    <!--          -->
    <id column="GE_BUSSINESS_CHANCE_FEEDBACK_ID" property="geBussinessChanceFeedbackId" jdbcType="INTEGER" />
    <result column="GE_BUSSINESS_CHANCE_ID" property="geBussinessChanceId" jdbcType="INTEGER" />
    <result column="STATUS" property="status" jdbcType="BIT" />
    <result column="CONTENT" property="content" jdbcType="VARCHAR" />
    <result column="IS_HAVING_ACCOUNT" property="isHavingAccount" jdbcType="BIT" />
    <result column="ACCOUNT_NAME" property="accountName" jdbcType="VARCHAR" />
    <result column="ACCOUNT_AREA_ID" property="accountAreaId" jdbcType="INTEGER" />
    <result column="ACCOUNT_AREA" property="accountArea" jdbcType="VARCHAR" />
    <result column="ACCOUNT_ADDRESS" property="accountAddress" jdbcType="VARCHAR" />
    <result column="IS_HAVING_MPC" property="isHavingMpc" jdbcType="BIT" />
    <result column="MPC_DETAIL" property="mpcDetail" jdbcType="VARCHAR" />
    <result column="ADD_TIME" property="addTime" jdbcType="TIMESTAMP" />
    <result column="MOD_TIME" property="modTime" jdbcType="TIMESTAMP" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
    <result column="CREATOR_NAME" property="creatorName" jdbcType="VARCHAR" />
    <result column="UPDATER_NAME" property="updaterName" jdbcType="VARCHAR" />
    <result column="IS_DELETE" property="isDelete" jdbcType="BIT" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--          -->
    GE_BUSSINESS_CHANCE_FEEDBACK_ID, GE_BUSSINESS_CHANCE_ID, STATUS, CONTENT, IS_HAVING_ACCOUNT, 
    ACCOUNT_NAME, ACCOUNT_AREA_ID, ACCOUNT_AREA, ACCOUNT_ADDRESS, IS_HAVING_MPC, MPC_DETAIL, 
    ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, IS_DELETE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--          -->
    select 
    <include refid="Base_Column_List" />
    from T_GE_BUSINESS_CHANCE_FEEDBACK
    where GE_BUSSINESS_CHANCE_FEEDBACK_ID = #{geBussinessChanceFeedbackId,jdbcType=INTEGER}
  </select>
    <select id="queryFednBackInfo" resultType="com.vedeng.erp.buyorder.dto.GeExamineFeedBackDto">
      select
      a.GE_BUSSINESS_CHANCE_ID,
        a.STATUS,
        a.CONTENT,
        IS_HAVING_ACCOUNT,
        ACCOUNT_NAME,
        ACCOUNT_AREA_ID,
        ACCOUNT_ADDRESS,
        IS_HAVING_MPC,
        MPC_DETAIL
        from T_GE_BUSINESS_CHANCE_FEEDBACK a
        where IS_DELETE =0
        and a.GE_BUSSINESS_CHANCE_ID=#{geBussinessChanceId}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--          -->
    delete from T_GE_BUSINESS_CHANCE_FEEDBACK
    where GE_BUSSINESS_CHANCE_FEEDBACK_ID = #{geBussinessChanceFeedbackId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.erp.buyorder.domain.entity.GeBusinessChanceFeedBack" >
    <!--          -->
    insert into T_GE_BUSINESS_CHANCE_FEEDBACK (GE_BUSSINESS_CHANCE_FEEDBACK_ID, GE_BUSSINESS_CHANCE_ID, 
      STATUS, CONTENT, IS_HAVING_ACCOUNT, 
      ACCOUNT_NAME, ACCOUNT_AREA_ID, ACCOUNT_AREA, 
      ACCOUNT_ADDRESS, IS_HAVING_MPC, MPC_DETAIL, 
      ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      IS_DELETE)
    values (#{geBussinessChanceFeedbackId,jdbcType=INTEGER}, #{geBussinessChanceId,jdbcType=INTEGER}, 
      #{status,jdbcType=BIT}, #{content,jdbcType=VARCHAR}, #{isHavingAccount,jdbcType=BIT}, 
      #{accountName,jdbcType=VARCHAR}, #{accountAreaId,jdbcType=INTEGER}, #{accountArea,jdbcType=VARCHAR}, 
      #{accountAddress,jdbcType=VARCHAR}, #{isHavingMpc,jdbcType=BIT}, #{mpcDetail,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{isDelete,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.erp.buyorder.domain.entity.GeBusinessChanceFeedBack" >
    <!--          -->
    insert into T_GE_BUSINESS_CHANCE_FEEDBACK
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="geBussinessChanceFeedbackId != null" >
        GE_BUSSINESS_CHANCE_FEEDBACK_ID,
      </if>
      <if test="geBussinessChanceId != null" >
        GE_BUSSINESS_CHANCE_ID,
      </if>
      <if test="status != null" >
        STATUS,
      </if>
      <if test="content != null" >
        CONTENT,
      </if>
      <if test="isHavingAccount != null" >
        IS_HAVING_ACCOUNT,
      </if>
      <if test="accountName != null" >
        ACCOUNT_NAME,
      </if>
      <if test="accountAreaId != null" >
        ACCOUNT_AREA_ID,
      </if>
      <if test="accountArea != null" >
        ACCOUNT_AREA,
      </if>
      <if test="accountAddress != null" >
        ACCOUNT_ADDRESS,
      </if>
      <if test="isHavingMpc != null" >
        IS_HAVING_MPC,
      </if>
      <if test="mpcDetail != null" >
        MPC_DETAIL,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
      <if test="creatorName != null" >
        CREATOR_NAME,
      </if>
      <if test="updaterName != null" >
        UPDATER_NAME,
      </if>
      <if test="isDelete != null" >
        IS_DELETE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="geBussinessChanceFeedbackId != null" >
        #{geBussinessChanceFeedbackId,jdbcType=INTEGER},
      </if>
      <if test="geBussinessChanceId != null" >
        #{geBussinessChanceId,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        #{status,jdbcType=BIT},
      </if>
      <if test="content != null" >
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="isHavingAccount != null" >
        #{isHavingAccount,jdbcType=BIT},
      </if>
      <if test="accountName != null" >
        #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="accountAreaId != null" >
        #{accountAreaId,jdbcType=INTEGER},
      </if>
      <if test="accountArea != null" >
        #{accountArea,jdbcType=VARCHAR},
      </if>
      <if test="accountAddress != null" >
        #{accountAddress,jdbcType=VARCHAR},
      </if>
      <if test="isHavingMpc != null" >
        #{isHavingMpc,jdbcType=BIT},
      </if>
      <if test="mpcDetail != null" >
        #{mpcDetail,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null" >
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null" >
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null" >
        #{isDelete,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.buyorder.domain.entity.GeBusinessChanceFeedBack" >
    <!--          -->
    update T_GE_BUSINESS_CHANCE_FEEDBACK
    <set >
      <if test="geBussinessChanceId != null" >
        GE_BUSSINESS_CHANCE_ID = #{geBussinessChanceId,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        STATUS = #{status,jdbcType=BIT},
      </if>
      <if test="content != null" >
        CONTENT = #{content,jdbcType=VARCHAR},
      </if>
      <if test="isHavingAccount != null" >
        IS_HAVING_ACCOUNT = #{isHavingAccount,jdbcType=BIT},
      </if>
      <if test="accountName != null" >
        ACCOUNT_NAME = #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="accountAreaId != null" >
        ACCOUNT_AREA_ID = #{accountAreaId,jdbcType=INTEGER},
      </if>
      <if test="accountArea != null" >
        ACCOUNT_AREA = #{accountArea,jdbcType=VARCHAR},
      </if>
      <if test="accountAddress != null" >
        ACCOUNT_ADDRESS = #{accountAddress,jdbcType=VARCHAR},
      </if>
      <if test="isHavingMpc != null" >
        IS_HAVING_MPC = #{isHavingMpc,jdbcType=BIT},
      </if>
      <if test="mpcDetail != null" >
        MPC_DETAIL = #{mpcDetail,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null" >
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null" >
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null" >
        IS_DELETE = #{isDelete,jdbcType=BIT},
      </if>
    </set>
    where GE_BUSSINESS_CHANCE_FEEDBACK_ID = #{geBussinessChanceFeedbackId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.buyorder.domain.entity.GeBusinessChanceFeedBack" >
    <!--          -->
    update T_GE_BUSINESS_CHANCE_FEEDBACK
    set GE_BUSSINESS_CHANCE_ID = #{geBussinessChanceId,jdbcType=INTEGER},
      STATUS = #{status,jdbcType=BIT},
      CONTENT = #{content,jdbcType=VARCHAR},
      IS_HAVING_ACCOUNT = #{isHavingAccount,jdbcType=BIT},
      ACCOUNT_NAME = #{accountName,jdbcType=VARCHAR},
      ACCOUNT_AREA_ID = #{accountAreaId,jdbcType=INTEGER},
      ACCOUNT_AREA = #{accountArea,jdbcType=VARCHAR},
      ACCOUNT_ADDRESS = #{accountAddress,jdbcType=VARCHAR},
      IS_HAVING_MPC = #{isHavingMpc,jdbcType=BIT},
      MPC_DETAIL = #{mpcDetail,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=BIT}
    where GE_BUSSINESS_CHANCE_FEEDBACK_ID = #{geBussinessChanceFeedbackId,jdbcType=INTEGER}
  </update>
  <update id="updateByGeBussinessChanceId">
    <!--          -->
    update T_GE_BUSINESS_CHANCE_FEEDBACK
    <set >
      <if test="geBussinessChanceId != null" >
        GE_BUSSINESS_CHANCE_ID = #{geBussinessChanceId,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        STATUS = #{status,jdbcType=BIT},
      </if>
      <if test="content != null" >
        CONTENT = #{content,jdbcType=VARCHAR},
      </if>
      <if test="isHavingAccount != null" >
        IS_HAVING_ACCOUNT = #{isHavingAccount,jdbcType=BIT},
      </if>
      <if test="accountName != null" >
        ACCOUNT_NAME = #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="accountAreaId != null" >
        ACCOUNT_AREA_ID = #{accountAreaId,jdbcType=INTEGER},
      </if>
      <if test="accountArea != null" >
        ACCOUNT_AREA = #{accountArea,jdbcType=VARCHAR},
      </if>
      <if test="accountAddress != null" >
        ACCOUNT_ADDRESS = #{accountAddress,jdbcType=VARCHAR},
      </if>
      <if test="isHavingMpc != null" >
        IS_HAVING_MPC = #{isHavingMpc,jdbcType=BIT},
      </if>
      <if test="mpcDetail != null" >
        MPC_DETAIL = #{mpcDetail,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null" >
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null" >
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null" >
        IS_DELETE = #{isDelete,jdbcType=BIT},
      </if>
    </set>
    where GE_BUSSINESS_CHANCE_ID = #{geBussinessChanceId,jdbcType=INTEGER} and IS_DELETE=0
  </update>

  <select id="queryByGeBussinessChanceId" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    SELECT
    <include refid="Base_Column_List" />
    FROM T_GE_BUSINESS_CHANCE_FEEDBACK
    WHERE GE_BUSSINESS_CHANCE_ID = #{geBussinessChanceId,jdbcType=INTEGER}
  </select>
</mapper>