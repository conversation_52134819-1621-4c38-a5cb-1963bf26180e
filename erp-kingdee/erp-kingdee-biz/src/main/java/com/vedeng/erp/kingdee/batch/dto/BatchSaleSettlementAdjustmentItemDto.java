package com.vedeng.erp.kingdee.batch.dto;

import java.math.BigDecimal;
import java.util.Date;

import com.vedeng.common.core.base.BaseDto;
import lombok.*;

/**
 * 销售售后调整明细单
 */
@Getter
@Setter
public class BatchSaleSettlementAdjustmentItemDto extends BaseDto {

    /**
     * 销售售后调整单明细id
     */
    private Integer saleSettlementAdjustmentItemId;

    /**
     * 销售售后调整单id
     */
    private Integer saleSettlementAdjustmentId;

    /**
     * 分摊价格
     */
    private BigDecimal apportionAmount;

    /**
     * 调整金额
     */
    private BigDecimal adjustmentAmount;

    /**
     * 原始售价
     */
    private BigDecimal differenceAmount;

    /**
     * 实际售价
     */
    private BigDecimal actualAmount;

    /**
     * skuId
     */
    private Integer skuId;

    /**
     * sku
     */
    private String sku;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * 实际销售数量
     */
    private Integer num;

    /**
     * SN码
     */
    private String barcodeFactory;

    /**
     * 批次号
     */
    private String batchNumber;

    /**
     * 销售商品id
     */
    private Integer saleorderGoodsId;
}