package com.vedeng.common.page;

import com.ctrip.framework.apollo.ConfigService;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.shiro.JedisUtils;
import com.vedeng.common.util.StringUtil;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.binding.BindingException;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.scripting.defaults.DefaultParameterHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.BindException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.Properties;

/**
 * 为了文件上传做兼容 需要对某些表的insert 做兼容处理
 */
@Intercepts({@Signature(type = Executor.class,method = "update",args =  {MappedStatement.class, Object.class})})
public class FileUploadInterceptor implements Interceptor {

    public static Logger logger = LoggerFactory.getLogger(FileUploadInterceptor.class);

    public static final String TRADER_CERTIFICATE  = "T_TRADER_CERTIFICATE";

    public static final String ATTACHMENT  = "T_ATTACHMENT";

    public static final String GOODS_ATTACHMENT  = "T_GOODS_ATTACHMENT";

    public static final String TRADER_FINANCE  = "T_TRADER_FINANCE";

    public static final String BRAND  = "T_BRAND";

    @Override
    public Object intercept(Invocation invocation) throws Throwable {

        Object[] args = invocation.getArgs();

        MappedStatement mappedStatement = (MappedStatement) args[0];
        Object parameter = invocation.getArgs()[1];
        BoundSql boundSql = mappedStatement.getBoundSql(parameter);
        String originalSql = boundSql.getSql().trim();

        if (!needInterceptor(mappedStatement,originalSql) ) {
            return invocation.proceed();
        }

        try {
            if (mappedStatement.getSqlCommandType() == SqlCommandType.INSERT) {
                dealWithParameterValue(originalSql,parameter);
            } else if (mappedStatement.getSqlCommandType() == SqlCommandType.UPDATE) {
                dealWithSuffixRedis(mappedStatement, parameter, originalSql);
            }
        }catch (Exception e){
            if(logger.isDebugEnabled()){
                logger.error("文件上传拦截器错误:"+originalSql+parameter ,e);
            }
        }

        return invocation.proceed();
    }

    /**
     * 删除文件，将删除的文件suffix，暂时保存到缓存中（全删全插，无法确定数据是删除还是不删除）
     * @param mappedStatement
     * @param parameter
     * @param originalSql
     */
    private void dealWithSuffixRedis(MappedStatement mappedStatement, Object parameter, String originalSql) throws Exception {
        if(!(parameter instanceof Map)){
            return;
        }
        Map<String, Object> param = (Map<String, Object>) parameter;

        // 这边的拦截器直接限制死了入参的名字，完全不考虑不同业务场景下的参数名字，比如registrationNumberId对应的是Attachment表的RELATED_ID字段，
        // 并不是所有的存储附件的场景都适用这个拦截器。写这段代码的人完全属于战犯级。
        if (!param.containsKey("attachmentFunction")) {
            return;
        }

        if (!param.containsKey("registrationNumberId")) {
            return;
        }

        String sql = "";
        if (originalSql.indexOf(TRADER_CERTIFICATE) >= 0) {
            Integer traderId = (Integer) param.get("traderId");
            sql = "SELECT * FROM T_TRADER_CERTIFICATE WHERE TRADER_ID = " + traderId;
        } else if (originalSql.indexOf(ATTACHMENT) >= 0) {
            List<Integer> attachmentFunctions = (List<Integer>) param.get("attachmentFunction");
            String attachmentFunction = "";
            if (CollectionUtils.isNotEmpty(attachmentFunctions)) {
                for (Integer attId : attachmentFunctions) {
                    if (StringUtil.isEmpty(attachmentFunction)) {
                        attachmentFunction += attId;
                    } else {
                        attachmentFunction += "," + attId;
                    }
                }
            }
            sql = "SELECT * FROM T_ATTACHMENT WHERE ATTACHMENT_TYPE = " + BeanUtils.getProperty(parameter, "attachmentType") +
                    " AND RELATED_ID = "  + BeanUtils.getProperty(parameter, "registrationNumberId") +
                    " AND ATTACHMENT_FUNCTION IN (" + attachmentFunction + ")" + " AND IS_DELETED = 0;" ;
        }

        Connection connection= null;
        PreparedStatement countStmt=null;
        ResultSet rs=null;
        try {connection=mappedStatement.getConfiguration().getEnvironment().getDataSource().getConnection();
              countStmt = connection.prepareStatement(sql);
              rs = countStmt.executeQuery();
            //遍历结果集
            while (rs.next()) {
                String uri = rs.getString("URI");
                String suffix = rs.getString("SUFFIX");
                //老图片路径
                if (uri.indexOf("resourceId") != -1) {
                    String resourceId = uri.substring(uri.indexOf("=") + 1);
                    if (!JedisUtils.exists(ErpConst.KEY_PREFIX_ATTACHMENT + resourceId)) {
                        JedisUtils.set(ErpConst.KEY_PREFIX_ATTACHMENT + resourceId, suffix, 86400);
                    }
                }
            }
        }catch(Exception e){
            logger.error("{}",originalSql,e);
        }finally {
            try{
                rs.close();
            }catch (Exception e){
                logger.error("【dealWithSuffixRedis】处理异常",e);
            }
            try{
                countStmt.close();
            }catch (Exception e){
                logger.error("【dealWithSuffixRedis】处理异常",e);
            }
            try{
                connection.close();
            }catch (Exception e){
                logger.error("【dealWithSuffixRedis】处理异常",e);
            }
        }

    }

    /**
     * 处理参数值
     * @param parameter
     */
    private void dealWithParameterValue(String originalSql,Object parameter) throws Exception {

        //如果是客户资质
        String uri = "";
        String targetProValue = "";
        String suffix = "";
        if (originalSql.indexOf(TRADER_CERTIFICATE) >= 0 || originalSql.indexOf(ATTACHMENT) >= 0 || originalSql.indexOf(GOODS_ATTACHMENT) >= 0) {

            uri = BeanUtils.getProperty(parameter, "uri");
            targetProValue = "domain";

        } else if (originalSql.indexOf(TRADER_FINANCE) >= 0) {

            uri = BeanUtils.getProperty(parameter, "averageTaxpayerUri");
            targetProValue = "averageTaxpayerDomain";

        } else if (originalSql.indexOf(BRAND) >= 0) {

            uri = BeanUtils.getProperty(parameter, "logoUri");
            targetProValue = "logoDomain";
        }

        if(StringUtils.isEmpty(uri)){
            return;
        }
        //老图片路径
        if (uri.indexOf("resourceId") == -1) {
            BeanUtils.setProperty(parameter,targetProValue,ConfigService.getAppConfig().getProperty("file_url","file1.vedeng.com"));
        }else{
            // VDERP-7467 从缓存中获取suffix BEGIN
            String resourceId = uri.substring(uri.indexOf("=")+1);
            suffix = JedisUtils.get(ErpConst.KEY_PREFIX_ATTACHMENT + resourceId);
            if (StringUtil.isNotEmpty(suffix)) {
                BeanUtils.setProperty(parameter,"suffix", suffix);
            }
//            String size=JedisUtils.get(ErpConst.KEY_PREFIX_ATTACHMENT_SIZE + resourceId);
//            if(StringUtils.isNotBlank(size)){
//                BeanUtils.setProperty(parameter,"file_size", size);
//            }

            // VDERP-7467 从缓存中获取suffix END
            BeanUtils.setProperty(parameter,targetProValue,ConfigService.getAppConfig().getProperty("oss_url_show","file.vedeng.com"));
        }
    }

    /**
     * 判断当前的sql是否需要拦截
     * @param mappedStatement
     * @return
     */
    private boolean needInterceptor(MappedStatement mappedStatement,String originalSql) {

        if(mappedStatement.getSqlCommandType() != SqlCommandType.INSERT && mappedStatement.getSqlCommandType() != SqlCommandType.UPDATE){
            return false;
        }

        if (originalSql.indexOf(TRADER_CERTIFICATE) >= 0 ||
                originalSql.indexOf(ATTACHMENT) >= 0 ||
                originalSql.indexOf(GOODS_ATTACHMENT) >= 0 ||
                originalSql.indexOf(TRADER_FINANCE) >= 0 ||
                originalSql.indexOf(BRAND) >= 0)
        {
            return true;
        }

        return false;
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {

    }
}
