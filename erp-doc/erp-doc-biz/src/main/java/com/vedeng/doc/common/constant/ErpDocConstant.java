package com.vedeng.doc.common.constant;

/**
 * <AUTHOR>
 * @create 2022/1/11 9:36
 */
public class ErpDocConstant {


    /**
     * 首营附件信息模块
     */
    public static final Integer ATTACHMENT_TYPE_974 = 974;

    /**
     * 注册证附件/备案凭证附件
     */
    public static final Integer ATTACHMENT_FUNCTION_975 = 975;

    /**
     * 注册证id
     */
    public static final String REGISTRATION_ID_STR = "344";

    public static class Number {

        public static final Integer ZERO = 0;
        public static final Integer ONE = 1;
        public static final Integer TWO = 2;

        /**
         * 一般用于省市区 顶级id（中国）
         */
        public static final Integer ONE_HUNDRED_THOUSAND = 100000;
    }

    /**
     * 含章 不含章
     */
    public static class HasOrNotStamp {

        public static final String HAS = " - 含章";
        public static final String HAS_ONE = "_含章_";
        public static final String NOT_HAS = " - 不含章";
        public static final String NOT_HAS_ONE = " _不含章_";
    }

    public static class Date {

        public static String TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

        public static String TIME_FORMAT_LONG = "yyyyMMddHHmmss";

        public static String TIME_FORMAT_T = "HH:mm:ss";
    }
}
