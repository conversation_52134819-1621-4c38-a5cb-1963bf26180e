package com.vedeng.erp.system.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.system.domain.dto.CostCategoryDto;
import com.vedeng.erp.system.domain.entity.CostCategoryEntity;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.system.mapstruct
 * @Date 2022/8/15 13:36
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface CostCategoryConvertor extends BaseMapStruct<CostCategoryEntity, CostCategoryDto> {
}
