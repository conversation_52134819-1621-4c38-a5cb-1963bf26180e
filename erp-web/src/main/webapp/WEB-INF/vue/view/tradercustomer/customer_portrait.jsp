<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
            + path + "/";
%>
<c:set var="path" value="<%=basePath%>" scope="application" />
<link rel="stylesheet" href="<%=basePath%>static/css/general.css?rnd=${resourceVersionKey}" />
<script src="${pageContext.request.contextPath}/static/js/echarts.min.js"></script>
<link rel="stylesheet" href="<%=basePath%>static/css/manage.css" />
<link rel="stylesheet" href="<%=basePath%>static/new/css/common/font/font.css" />
<link rel="stylesheet" href="<%=basePath%>static/css/trader/iconfont.css" />

<style type="text/css">
    .el-descriptions-item__container{
        padding-left: 15px;
    }
    .el-descriptions__body{
        padding-top: 10px;
    }
    .el-tag--small {
        margin-left: 10px;
    }
    .el-table .el-table__cell {
        padding: 6px 0;
    }

    .parts{
        padding-top: 10px;
    }
    .vd-tip.detail-block-tip {
        border: 0;
        border-radius: 0;
        padding: 9px 20px 9px 46px;
        margin-bottom: 10px; }
    .vd-tip.detail-block-tip .vd-tip-icon {
        left: 20px;
        top: 7px; }

    .detail-block-option {
        padding: 0 20px;
        margin-bottom: 10px; }
    .vd-icon {
        /*font-family: 'HC' !important;*/
         speak: none;
         font-style: normal;
        font-weight: normal;
        font-variant: normal;
        text-transform: none;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 16px;
    }
    .icon-info2:before {
        content: "\e91e";
    }
    .container-agency {
        display: flex;
        flex-direction: column;
        width:500px;
    }

	.main-container {
    	padding: 20px;
	}

	.parts-time {
	    display: flex; /* 使用 Flex 布局 */
	    align-items: center; /* 垂直居中 */
	}

	.date-container {
	    display: flex; /* 使用 Flex 布局 */
	    align-items: center; /* 垂直居中 */
	}

	.query-date {
	    margin-right: 10px; /* 调整查询日期文本和输入框之间的间距 */
	}

	.input-smaller96 {
	    width: 120px; /* 调整输入框的宽度，确保它们显示合适 */
	}

	.gang {
	    margin: 0 10px; /* 调整两个日期输入框之间的间距 */
	}

	i{
		background: none !important;
	}
	.el-date-editor .el-range-separator {
		width:8%
	}


</style>
<%@ include file="../../../jsp/trader/customer/customer_tag.jsp"%>


<div id="app" style="display: none;">
    <el-row style="text-align: right; margin-right: 3%; font-size: 18px; font-weight: 600" v-if="customerClass == 4">CPM标签完善度 {{this.cpm.allScore}} %
        <el-button type="text" size="mini" @click="viewCpm()" style="margin-left: 10px">查看</el-button>
    </el-row>

    <el-dialog
            title="CPM标签完善度雷达图"
            v-show="false"
            width="670px"
            append-to-body
            center="true"
            :visible.sync="dialogVisible">
        <template #default>
            <canvas id="main" width="640" height="600"></canvas>
        </template>
    </el-dialog>

    <el-card class="box-card J-block" data-ref="jbxx" ref="jbxx">
        <el-descriptions title="基本信息" :column="2">
            <template slot="extra">
                <el-link style="margin-right: 20px" type="primary" href="/trader/customer/baseinfo.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}">查看更多</el-link>
            </template>
            <el-descriptions-item label="客户名称">{{traderCustomerPortraitDto.traderName}}</el-descriptions-item>
            <el-descriptions-item label="注册地址">{{traderCustomerPortraitDto.registrationAddress}}</el-descriptions-item>
            <el-descriptions-item label="客户类型" :span="customerTypeSpan">{{traderCustomerPortraitDto.traderCustomerCategoryName}}</el-descriptions-item>
            <el-descriptions-item label="客户有效性" v-if="customerClass == 4">
                <span style="width: 40px" v-if="traderCustomerPortraitDto.effectiveness == 0">无效</span>
                <span v-if="traderCustomerPortraitDto.effectiveness == 1">有效</span>
                <span v-if="traderCustomerPortraitDto.effectiveness == 0 && traderCustomerPortraitDto.showInvalidReason != ''">{{traderCustomerPortraitDto.showInvalidReason}}</span>
            </el-descriptions-item>
            <el-descriptions-item label="终端大类" v-if="customerClass == 3">{{traderCustomerPortraitDto.traderCustomerMarketingTypeName}}</el-descriptions-item>
            <el-descriptions-item label="机构性质" v-if="customerClass == 3">{{traderCustomerPortraitDto.institutionNatureName}}</el-descriptions-item>
            <el-descriptions-item label="机构等级" v-if="customerClass == 3">{{traderCustomerPortraitDto.institutionLevelName}}</el-descriptions-item>
            <el-descriptions-item label="机构类型" v-if="customerClass == 3">{{traderCustomerPortraitDto.institutionTypeName}}</el-descriptions-item>
            <el-descriptions-item label="经营状态" v-if="customerClass == 3 || customerClass ==1">{{traderCustomerPortraitDto.managementForms}}</el-descriptions-item>
            <el-descriptions-item label="成立时间" v-if="customerClass == 3 || customerClass ==1">{{parseTime(traderCustomerPortraitDto.registeredDate, '{y}-{m}-{d}')}}</el-descriptions-item>
            <el-descriptions-item label="注册资本" v-if="customerClass == 3 || customerClass ==1">{{traderCustomerPortraitDto.registeredCapital}}</el-descriptions-item>
            <el-descriptions-item label="经营范围" v-if="customerClass == 3 || customerClass ==1" :span="2">{{traderCustomerPortraitDto.businessScope}}</el-descriptions-item>

            <el-descriptions-item label="经营终端类型" :span="2" v-if="customerClass == 4" >
                <div>
                    <el-row>
                        <el-col :span="24">
                            <el-tag size="small" v-for="item in traderCustomerPortraitDto.marketingTerminalDtoList">{{item.traderCustomerMarketingTypeName}}</el-tag>
                        </el-col>
                    </el-row>

                    <el-row style="margin-top:10px;">
                        <el-col :span="24">
                            <el-table
                                    :data="traderCustomerPortraitDto.marketingTerminalDtoList"
                                    border
                                    style="width: 100%; "
                                    key="marketingTerminalDto"
                                    :cell-style="{'text-align':'center'}"
                                    :header-cell-style="{'text-align':'center'}">
                                <el-table-column
                                        prop="traderCustomerMarketingTypeName"
                                        label="大类"
                                        min-width="20%"
                                        width="140px"
                                    >
                                </el-table-column>
                                <el-table-column
                                        prop="institutionNatureName"
                                        label="性质"
                                        min-width="20%"
                                        width="140px"
                                >
                                </el-table-column>
                                <el-table-column
                                        prop="institutionLevelName"
                                        label="评级"
                                        min-width="20%"
                                        width="200px"
                                    >
                                </el-table-column>
                                <el-table-column
                                        label="类型"
                                        prop="model"
                                        min-width="40%"
                                        width="300px"
                                >
                                        <template slot-scope="scope">
                                            <el-row>{{scope.row.institutionTypeName}}</el-row>

                                        </template>
                                </el-table-column>
                            </el-table>
                        </el-col>

                    </el-row>
                </div>
            </el-descriptions-item>
            <el-descriptions-item label="经营产品类型" v-if="customerClass == 2">
                {{traderCustomerPortraitDto.businessProductsType}}
            </el-descriptions-item>
            <el-descriptions-item label="经营客户类型" v-if="customerClass == 2">
                {{traderCustomerPortraitDto.businessCustomerType}}
            </el-descriptions-item>
            <el-descriptions-item label="员工人数" v-if="customerClass == 2">
                {{traderCustomerPortraitDto.employeesStr}}
            </el-descriptions-item>
            <el-descriptions-item label="年销售额" :span="2" v-if="customerClass == 2">
                {{traderCustomerPortraitDto.annualSalesStr}}
            </el-descriptions-item>

            <el-descriptions-item label="商品类型" :span="2" v-if="customerClass == 4">{{traderCustomerPortraitDto.traderCustomerMarketingPrincipalDto.skuTypeName}}</el-descriptions-item>
            <el-descriptions-item label="商品范畴" :span="2" v-if="customerClass == 4">
                <div style="flex-wrap: wrap;">
                    <el-tag size="small" v-if="traderCustomerPortraitDto.traderCustomerMarketingPrincipalDto.skuCategoryNameList.length > 0"
                            v-for="item in traderCustomerPortraitDto.traderCustomerMarketingPrincipalDto.skuCategoryNameList">{{item}}</el-tag>
                </div>
                <el-row v-if="traderCustomerPortraitDto.traderCustomerMarketingPrincipalDto.skuCategoryNameList.length == 0">
                    <el-col :span="24">
                        {{traderCustomerPortraitDto.traderCustomerMarketingPrincipalDto.skuScopeName}}
                    </el-col>
                </el-row>
            </el-descriptions-item>
            <el-descriptions-item label="销售类别" :span="2" v-if="customerClass == 4">{{traderCustomerPortraitDto.traderCustomerMarketingPrincipalDto.salesTypeName}}</el-descriptions-item>
            <el-descriptions-item label="核心资源" :span="2" v-if="customerClass == 4">
                <div>
                    <div class="container-agency">
                        <el-row>
                            <el-col :span="24">
                                <span>
                                    产品型渠道商
                                    <span style="margin-left: 14px" v-if="showCustomerAgency==1">无代理商品或品牌</span>
                                    <span style="margin-left: 14px" v-if="showCustomerAgency==2">有代理商品或品牌</span>
                                    <span style="margin-left: 14px" v-if="showCustomerAgency==3"></span>
                                </span>
                            </el-col>
                        </el-row>
                        <el-row v-if="showCustomerAgency==2">
                            <el-col :span="4" offset="2">
                                     代理品牌：
                            </el-col>
                            <el-col :span="18">
                                     {{traderCustomerPortraitDto.traderCustomerMarketingPrincipalDto.agencyBrandName}}
                            </el-col>
                        </el-row>
                        <el-row v-if="showCustomerAgency==2">
                            <el-col :span="4" :offset="2" >
                                    代理商品：
                            </el-col>
                            <el-col :span="18" >
                                {{traderCustomerPortraitDto.traderCustomerMarketingPrincipalDto.agencySkuName}}
                            </el-col>
                        </el-row>
                    </div>
                    <div class="container-agency">
                        <el-row :span="24">
                            <el-col :span="24">
                                <span>
                                    客户型渠道商
                                </span>
                            </el-col>
                        </el-row>
                        <el-row >
                            <el-col :span="22" offset="2">
                                <span>
                                      {{traderCustomerPortraitDto.traderCustomerMarketingPrincipalDto.governmentRelationName}}
                                </span>
                            </el-col>
                        </el-row>
                    </div>
                </div>
            </el-descriptions-item>
        </el-descriptions>
    </el-card>

    <el-card class="box-card J-block" data-ref="lxr" ref="lxr" v-if="this.traderCustomerPortraitDto.traderContactDtoList.length > 0">
        <div slot="header" class="clearfix">
            <span>联系人</span>
            <el-link style="float: right; padding: 3px 0" type="primary" href="/ezadmin/list/list-traderContractList?perPageInt=50&TRADER_ID=${traderCustomer.traderId}&TRADER_CUSTOMERID=${traderCustomer.traderCustomerId}&customerNature=${traderCustomer.customerNature}">查看更多</el-link>
        </div>
        <template>
            <el-table
                    :data="traderCustomerPortraitDto.traderContactDtoList"
                    border
                    style="width: 100%; "
                    key="traderContact">
                <el-table-column
                        align="center"
                        text-align="center"
                        type="index"
                        label="序号"
                        min-width="12%"
                        width="80px">
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        prop="name"
                        label="姓名"
                        min-width="8%"
                        width="120px"
                >
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        label="性别"
                        min-width="6%"
                        width="60px"
                >
                    <template slot-scope="scope">
                        <span v-if="scope.row.sex == 0">女</span>
                        <span v-else-if="scope.row.sex == 1">男</span>
                        <span v-else>保密</span>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        prop="position"
                        label="职位"
                        min-width="6%"
                        width="60px"
                >
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        label="电话"
                        prop="telephone"
                        min-width="8%"
                        width="140px">
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        prop="mobile"
                        label="手机1"
                        min-width="8%"
                        width="140px"
                >
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        prop="mobile2"
                        label="手机2"
                        min-width="8%"
                        width="140px"
                    >
                </el-table-column>


                <el-table-column
                        align="center"
                        text-align="center"
                        label="在职状态"
                        min-width="6%"
                        width="80px">
                        <template slot-scope="scope">
                            <span v-if="scope.row.isOnJob == 1">在职</span>
                            <span v-else>离职</span>
                        </template>
                </el-table-column>

                <el-table-column
                        align="center"
                        text-align="center"
                        prop="memberFlag"
                        label="注册信息"
                        min-width="10%"
                        width="200px">
                </el-table-column>
                <el-table-column
                        align="left"
                        text-align="left"
                        prop="comments"
                        label="备注"
                        min-width="30%" >
                </el-table-column>
            </el-table>
        </template>
    </el-card>

    <el-card class="box-card J-block" data-ref="lxdz" ref="lxdz" v-if="this.traderCustomerPortraitDto.traderAddressDtoList.length > 0">
        <div slot="header" class="clearfix">
            <span>联系地址</span>
            <el-link style="float: right; padding: 3px 0" type="primary" href="/ezadmin/list/list-addressInformationList?TRADER_ID=${traderCustomer.traderId}&TRADER_CUSTOMERID=${traderCustomer.traderCustomerId}&customerNature=${traderCustomer.customerNature}">查看更多</el-link>
        </div>
        <template>
            <el-table
                    :data="traderCustomerPortraitDto.traderAddressDtoList"
                    border
                    style="width: 100%; "
                    key="traderAddress">
                <el-table-column
                        align="center"
                        text-align="center"
                        type="index"
                        label="序号"
                        min-width="50"
                        width="80px">
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        label="默认地址"
                        min-width="10%"
                        width="80px">
                    <template slot-scope="scope">
                        <span v-if="scope.row.isDefault == 1">默认</span>
                        <span v-else>非默认</span>
                    </template>
                </el-table-column>

                <el-table-column
                        align="center"
                        text-align="center"
                        label="启用或禁用"
                        min-width="10%"
                        width="100px">
                    <template slot-scope="scope">
                        <span v-if="scope.row.isEnable == 1">启用</span>
                        <span v-else>禁用</span>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        prop="area"
                        label="地区"
                        min-width="30%"
                        width="300px">
                </el-table-column>
                <el-table-column
                        align="left"
                        text-align="left"
                        prop="address"
                        align="left"
                        header-align="left"
                        label="详细地址"
                        min-width="10%" >
                </el-table-column>


            </el-table>
        </template>
    </el-card>

    <el-card class="box-card J-block" data-ref="glgs" ref="glgs"  v-if="traderCustomerPortraitDto.hasRelationCustomer && customerClass != 2">
        <div slot="header" class="clearfix">
            <span>关联公司</span>
            <el-link style="float: right; padding: 3px 0" type="primary" href="/trader/relation/info.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}">查看更多</el-link>
        </div>
        <div id="relationCompany" style="">

        </div>
    </el-card>

    <el-card class="box-card J-block" data-ref="jxll" ref="jxll" v-if="showDistributionLink">
        <div slot="header" class="clearfix">
            <span>经销链路</span>
            <el-link style="float: right; padding: 3px 0" type="primary" href="/trader/customer/new/distribution/link.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}">查看明细</el-link>
        </div>
        <div>
            <div style="margin-top: 10px;margin-bottom: 10px;">
                <span>来源：</span>
                <select id="linkSourceType" name="linkSourceType" onchange="javascript:vm.linkSourceType=this.value;refreshRela2();">
                    <option value="0">全部</option>
                    <option value="1">中标数据</option>
                    <option value="2">贝登交易</option>
                    <option value="3">商机</option>
                    <option value="4">报价</option>
                </select>
                <span>时间：</span>
                <select id="cooperationTimeFrame" name="cooperationTimeFrame" onchange="javascript:vm.cooperationTimeFrame=this.value;refreshRela2();">
                    <option value="3">近三年</option>
                    <option value="2">近二年</option>
                    <option value="1">近一年</option>
                </select>
                &nbsp;&nbsp;&nbsp;&nbsp;
                <span><i class="vd-tip-icon vd-icon icon-info2 goods-required-item" style="color: #efc859; background: none"></i>全部包含中标数据、贝登交易数据、商机、报价等。展示部分信息，完整链路请点击查看明细</span>

            </div>
            <div id="distributionLink" style="">

            </div>
        </div>
    </el-card>

    <el-card class="box-card J-block" data-ref="jcbq" ref="jcbq" v-if="traderCustomerPortraitDto.lifeCycle || traderCustomerPortraitDto.customerGrade">
        <el-descriptions title="决策标签" :column="2">
            <template slot="extra">
                <el-link style="margin-right: 20px;" type="primary" href="/trader/customer/new/customeDetail.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}">查看更多</el-link>
            </template>
            <el-descriptions-item label="生命周期	">{{traderCustomerPortraitDto.lifeCycle}}</el-descriptions-item>
            <el-descriptions-item label="客户等级	">{{traderCustomerPortraitDto.customerGrade}}</el-descriptions-item>
        </el-descriptions>
    </el-card>

    <el-card class="box-card J-block" data-ref="jylbq" ref="jylbq" v-if="traderCustomerPortraitDto.historyTransactionNum">
        <el-descriptions title="交易类标签" :column="2">
            <template slot="extra">
                <el-link style="margin-right: 20px;" type="primary" href="/trader/customer/new/customeDetail.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}">查看更多</el-link>
            </template>
            <el-descriptions-item label="累计下单">{{traderCustomerPortraitDto.historyTransactionNum}}</el-descriptions-item>
            <el-descriptions-item label="历史交易总额">{{traderCustomerPortraitDto.historyTransactionAmount}}</el-descriptions-item>
            <el-descriptions-item label="首次下单时间">{{traderCustomerPortraitDto.firstOrderTime}}</el-descriptions-item>
            <el-descriptions-item label="最近下单时间">{{traderCustomerPortraitDto.lastOrderTime}}</el-descriptions-item>
            <el-descriptions-item label="近一年平均购买周期">{{traderCustomerPortraitDto.lastYearAveragePurchasePeriod}}</el-descriptions-item>
            <el-descriptions-item label="历史累计客单价">{{traderCustomerPortraitDto.historyCumulativeUnitPrice}}</el-descriptions-item>
            <el-descriptions-item label="订单覆盖科室" :span="2">{{traderCustomerPortraitDto.departmentCover}}</el-descriptions-item>
            <el-descriptions-item label="订单收货地区" :span="2">{{traderCustomerPortraitDto.orderCoverArea}}</el-descriptions-item>
            <el-descriptions-item label="订单装机地区" :span="2">{{traderCustomerPortraitDto.installedArea}}</el-descriptions-item>
            <el-descriptions-item label="交易时间" :span="2">
                <el-select v-model = "traderTimeValue" placeholder="请选择" size="mini" @change="handleChange">
                    <el-option
                            v-for="item in traderTimeOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                    </el-option>
                </el-select>
            </el-descriptions-item>
            <el-descriptions-item label="交易时间内品牌" :span="2">
                <div style="flex-wrap: wrap;">
                    <el-tag :v-model="brandTagList" v-for="item in brandTagList" size="small">{{item}}</el-tag>
                </div>
            </el-descriptions-item>
            <el-descriptions-item label="交易时间内分类" :span="2">
                <div style="flex-wrap: wrap;">
                    <el-tag :v-model="categoryTagList" v-for="item in categoryTagList" size="small">{{item}}</el-tag>
                </div>
            </el-descriptions-item>
            <el-descriptions-item label="近一年交易但近一月无交易分类" :span="2">
                <div style="flex-wrap: wrap;">
                    <el-tag v-for="item in specialCategoryTagList" size="small">{{item}}</el-tag>
                </div>
            </el-descriptions-item>

        </el-descriptions>
    </el-card>

    <el-card class="box-card J-block" data-ref="gtjl" ref="gtjl" v-if="this.traderCustomerPortraitDto.communicateRecordDtoList.length > 0">
        <div slot="header" class="clearfix">
            <span>沟通记录</span>
            <el-link style="float: right; padding: 3px 0" type="primary" href="/trader/customer/communicaterecord.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}&customerNature=${traderCustomer.customerNature}">查看更多</el-link>
        </div>
        <template>
            <el-table
                    :data="traderCustomerPortraitDto.communicateRecordDtoList"
                    border
                    style="width: 100%; "
                    key="communicateRecord" >
                <el-table-column
                        align="center"
                        text-align="center"
                        label="时间"
                        min-width="10%"
                        width="100px"    >
                    <template slot-scope="scope">
                        <span>{{parseTime(scope.row.begintime, '{y}-{m}-{d}')}}</span>
                    </template>
                </el-table-column>

                <el-table-column
                        align="center"
                        text-align="center"
                        label="录音内容"
                        min-width="10%"
                        width="80px" >
                    <template slot-scope="scope">
                        <span v-if="scope.row.coidUri != null && scope.row.coidUri != ''">
                            <el-button type="text" style="padding: 0;" @click="playrecord(scope.row.coidUri)">播放</el-button>
                        </span>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        prop="contactName"
                        label="联系人"
                        min-width="15%"
                        width="120px" >
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        prop="phone"
                        label="联系方式"
                        min-width="15%"
                        width="120px">
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        label="沟通方式"
                        min-width="10%"
                        width="80px">
                    <template slot-scope="scope">
                        <span v-if="scope.row.coidType == 2">呼出</span>
                        <span v-else>呼入</span>
                    </template>
                </el-table-column>

                <el-table-column
                        align="center"
                        text-align="center"
                        prop="creatorName"
                        label="操作人"
                        min-width="10%"
                        width="120px">
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        label="下次联系日期"
                        min-width="20%"
                        width="120px">
                    <template slot-scope="scope">
                        <span>{{parseTime(scope.row.nextContactDate)}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        align="left"
                        text-align="left"
                        label="沟通内容"
                        min-width="10%"
                >
                    <template slot-scope="scope">
                        <div style="flex-wrap: wrap;">
                            <el-tag v-for="item in scope.row.allTag" size="small">{{item.tagName}}</el-tag>
                        </div>
                        <span>{{scope.row.contentSuffix}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        align="left"
                        text-align="left"
                        prop="nextContactContent"
                        label="下次沟通内容"
                        min-width="25%"
                width="200px">
                </el-table-column>
            </el-table>
        </template>
    </el-card>
    <el-card class="box-card J-block" data-ref="yhfl" ref="yhfl" v-if="customerClass != 2 && this.archiveCards.length > 0 ">
        <div class="parts">
            <div class="title-container title-container-blue">
                <div class="table-title nobor" style="font-size: 16px;">
                    客户档案&用户行为
                </div>
            </div>
            <el-tabs v-model="activeName" type="card" @tab-click="handleClick" >
            	<el-link style="float: right; padding: 3px 0; margin-right: 20px" type="primary" href="/trader/customer/new/customeDetail.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}">查看更多</el-link>
           		<el-tab-pane label="客户档案" name="first">
           			<div class="main-container">
                		<div class="parts-time">
                			<div class="date-container">
	                			<div class="query-date">查询日期：</div>

                                <el-date-picker
								    v-model="dateRange"
								    type="daterange"
								    range-separator="至"
								    start-placeholder="开始日期"
								    end-placeholder="结束日期"
								    value-format="yyyy-MM-dd"
								    @change="handleDateChange"
								    @blur="queryArchive">
								 </el-date-picker>


	                        </div>
                		</div>
                        <div class="parts-time">
                            <div class="activities-wrap" style="display: flex;">
                                <div id="card" class="cards-container" style="">
                                    <div
                                            class="card"
                                            v-for="(card, index) in archiveCards"
                                            :key="index"
                                            :class="{ 'selected': card.selected }"
                                            @click="toggleArchiveCardSelection(card)"
                                    >
                                        <div v-html="card.categoryName"></div>
                                    </div>
                                </div>
                                <%--    提示文案! 用户线上行为数据有1天延迟，今日访问数据正在解析中。--%>

                                <div style="display: flex; flex-direction: column; align-items: flex-start; justify-content: flex-start; width: 80%;   position: relative; padding: 40px 0 0 240px">


                                    <div class="timeline-container">
                                        <el-timeline :reverse="reverse">
                                            <el-timeline-item
                                                    v-for="(activity, index) in archiveActivities"
                                                    :key="index"
                                                    :timestamp="activity.eventTime"
                                            >
                                                <div v-html="activity.archivedFormat"></div>
                                            </el-timeline-item>
                                        </el-timeline>
                                        <div style="font-size: 14px; color: #09f; cursor: pointer;padding-bottom: 40px;" @click="getArchiveTimeLineMore" v-show="isShowTmeArchiveMore"  >更多</div>
                                        <div style="font-size: 14px; color: #8492a6; cursor:text;padding-bottom: 40px;"  v-show="!isShowTmeArchiveMore"  >没有了</div>

                                    </div>
                                </div>
                            </div>
                        </div>
                      </div>
                </el-tab-pane>

                <el-tab-pane label="用户行为" name="second">
                        <div class="parts">
                            <div class="parts">
                                &nbsp; &nbsp;! 用户线上行为数据有1天延迟，今日访问数据正在解析中。
                            </div>
                            <div class="activities-wrap" style="display: flex;">
                                <div id="card" class="cards-container" style="">
                                    <div
                                            class="card"
                                            v-for="(card, index) in cards"
                                            :key="index"
                                            :class="{ 'selected': card.selected }"
                                            @click="toggleCardSelection(card)"
                                    >
                                        <div v-html="card.account"></div>
                                    </div>
                                </div>
                                <%--    提示文案! 用户线上行为数据有1天延迟，今日访问数据正在解析中。--%>

                                <div style="display: flex; flex-direction: column; align-items: flex-start; justify-content: flex-start; width: 80%;   position: relative; padding: 40px 0 0 240px">


                                    <div class="timeline-container">
                                        <el-timeline :reverse="reverse">
                                            <el-timeline-item
                                                    v-for="(activity, index) in activities"
                                                    :key="index"
                                                    :timestamp="activity.userActionTraceTime"
                                            >
                                                <div v-html="activity.userActionTraceDesc"></div>
                                                <a target="_blank" v-if="activity.userActionTracePcLink" :href="activity.userActionTracePcLink">{{activity.userActionTracePcLink}}</a>
                                            </el-timeline-item>
                                        </el-timeline>
                                        <div style="font-size: 14px; color: #09f; cursor: pointer;padding-bottom: 40px;" @click="getTimeLineMore" v-show="isShowTmeMore"  >更多</div>
                                        <div style="font-size: 14px; color: #8492a6; cursor:text;padding-bottom: 40px;"  v-show="!isShowTmeMore"  >没有了</div>

                                    </div>
                                </div>
                            </div>
                        </div>
                </el-tab-pane>

            </el-tabs>

        </div>
    </el-card>

    <div id="category-wrap-div" class="category-wrap" :class="{hide: !isShowCategory}">
        <div class="slide-btn" @click="toggleShowCategory">
            <i class="el-icon-arrow-right" v-if="isShowCategory"></i>
            <i class="el-icon-arrow-left" v-else></i>

        </div>
        <div class="category-item"  :class="{active: item.ref === categoryIndex}" v-for="(item, index) in categoryList" @click="scrollTo(item.ref, index)">{{item.label}}</div>
    </div>

    <div id="backtop-container" class="el-backtop" style="right: 40px; bottom: 40px; display: none">
        <el-row v-if="traderCustomerPortraitDto.showNewSummary" style="position: absolute; top: -9px; right: 0; color: red">
            NEW
        </el-row>
        <el-row>AI客户助理</el-row>
    </div>

    <el-dialog
            title="AI客户助理"
            v-show="false"
            width="670px"
            append-to-body
            center="true"
            @open="searchRecord()"
            :visible.sync="aiDialogVisible">

        <template>
            <div class="container">
                <div class="circle">
                    <span class="text">助理</span>
                </div>
                <el-row class="row">根据近期沟通记录分析事项如下：</el-row>
            </div>
        </template>

        <el-card class="box-card my-card" style="margin-top: 20px;">
            <div slot="header" class="clearfix" style="font-size: 12px">
                <span>沟通内容分析</span>
                <el-button style="float: right; padding: 3px 0" type="text" v-if="this.communicateAiSummaryDto.communicateSummaryId" @click="modifySummary()">去修正</el-button>
            </div>
            <el-row style="padding-left: 15px; padding-bottom: 15px" v-if="this.communicateAiSummaryDto.communicateSummaryId">
                <span>客户的意图：{{this.communicateAiSummaryDto.customerIntentions}}</span><br>
                <span></span><br>
                <span>意向商品：{{this.communicateAiSummaryDto.intentionGoods}}</span><br>
                <span>品牌：{{this.communicateAiSummaryDto.brands}}</span><br>
                <span>型号：{{this.communicateAiSummaryDto.models}}</span><br>
                <span>客户类型：{{this.communicateAiSummaryDto.customerTypes}}</span><br>
                <span>是否有意向：{{this.communicateAiSummaryDto.isIntention}}</span><br>
                <span>是否加微信：{{this.communicateAiSummaryDto.isAddWechat}}</span><br>
                <span>沟通是否有效：{{this.communicateAiSummaryDto.isEffectiveCommunication}}</span><br>
            </el-row>
            <el-row v-else style="height: 100px; display: flex; align-items: center; justify-content: center; text-align: center;">
                暂无最新沟通记录解析
            </el-row>
        </el-card>

        <el-card class="box-card my-card" style="margin-top: 20px;">
            <div slot="header" class="clearfix" style="font-size: 12px">
                <span>客户标签分析</span>
            </div>

            <el-row style="height: 100px; display: flex; align-items: center; justify-content: center; text-align: center;">
                敬请期待......
            </el-row>
        </el-card>

        <el-card class="box-card my-card" style="margin-top: 20px">
            <div slot="header" class="clearfix" style="font-size: 12px">
                <span>待办事项</span>
            </div>
            <el-row style="height: 100px; display: flex; align-items: center; justify-content: center; text-align: center;">
                敬请期待......
            </el-row>
        </el-card>
    </el-dialog>
</div>

<script src="${pageContext.request.contextPath}/static/api/trader/traderCustomer.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/js/d3.js" charset="utf-8"></script>

<script type="text/javascript">
    const traderId = '${traderId}';
    let vm = new Vue({
        el: '#app',
        data() {
            return {
                timer:null,//定时器，进行圆环关系处理
                cards: [],
                archiveCards: [],
                activities: [],
                archiveActivities: [],
                isShowTmeMore: false,
                isShowTmeArchiveMore: false,
                reverse: false,
                traderCustomerPortraitDto: {
                    communicateRecordDtoList:[],
                    traderAddressDtoList:[],
                    traderContactDtoList:[],
                    traderCustomerMarketingPrincipalDto:{
                        agencySkuName:'',
                        agencyBrandName:''
                    }
                },
                categoryList: [{
                    label: '基本信息',
                    ref: 'jbxx'
                }
                ],
                traderTimeValue: "seven",
                traderTimeCategory: "sevenCategory",
                traderTimeBrand: "sevenBrand",
                traderTimeOptions:[
                    {
                        label: '近7天',
                        value: "seven"
                    },
                    {
                        label: '近30天',
                        value: "thirty"
                    },
                    {
                        label: '近60天',
                        value: "sixty"
                    },
                    {
                        label: '近90天',
                        value: "ninety"
                    },
                    {
                        label: '近180天',
                        value: "halfYear"
                    },
                    {
                        label: '近365天',
                        value: "oneYear"
                    },
                    {
                        label: '近730天',
                        value: "twoYear"
                    }
                ],
                categoryTagList: [],
                brandTagList: [],
                specialCategoryTagList: [],
                // 根据客户类型 和 客户性质分类。1： 科研终端；2：科研分销；3：临床终端；4：临床分销
                customerClass: 0,
                customerTypeSpan: 2,
                timeLinePage: 1,
                timeLineArchivePage: 1,
                isShowCategory: true,
                categoryIndex: 0,

                // 经销链路
                linkSourceType: 0,
                cooperationTimeFrame: 3,
                showDistributionLink: false,
                dialogVisible: false,
                aiDialogVisible: false,
                cpm: {
                    allScore: 0,
                    cScore: 0,
                    pareaScore: 0,
                    psaleTypeScore: 0,
                    pskuScopeScore: 0,
                    pskuTypeScore: 0
                },
                communicateAiSummaryDto: {
                    customerIntentions: '',
                    intentionGoods: '',
                    brands: '',
                    models: '',
                    customerTypes: '',
                    isIntention: '',
                    isAddWechat: '',
                    isEffectiveCommunication: ''
                },
                showCustomerAgency:true,
                activeName: 'first',
                dateRange: [],
                archiveCursor:'',
                oldCategory:0,
                oldStartTime:null,
                oldEndTime:null,
                activeArchiveCard:null,
                showCustomerAgency:1
            }
        },
        computed: {
     	 	  startTime() {
     		    if (this.dateRange && this.dateRange.length > 0) {
     		      return this.dateRange[0];
     		    } else {
     		      return null; // 或者设置成默认值
     		    }
     		  },
     		  endTime() {
     		    if (this.dateRange && this.dateRange.length > 1) {
     		      return this.dateRange[1];
     		    } else {
     		      return null; // 或者设置成默认值
     		    }
     		  }
          },
        mounted() {
            loadingApp();
            this.$nextTick(() => {
                this.checkCategoryIndex();
        })
            let _this = this;
            $(window).on('scroll', function () {
                _this.checkCategoryIndex();
            })
        },

        created() {
            this.initPageData();
        },

        methods: {
       	 	handleDateChange(value) {

       	    },
            handleClick(tab, event) {

            },
            queryArchive(){
            	this.onAchiveCardSelected(vm.activeArchiveCard);
            },
            async initPageData() {
                await getTraderCustomerPortrait({"traderId": traderId}).then(res => {
                    this.traderCustomerPortraitDto = res.data.data;
                    debugger;
                    if(this.traderCustomerPortraitDto.traderCustomerMarketingPrincipalDto.isProductCustomer==1){
                        if(_.isEmpty(this.traderCustomerPortraitDto.traderCustomerMarketingPrincipalDto.agencySkuName) && _.isEmpty(this.traderCustomerPortraitDto.traderCustomerMarketingPrincipalDto.agencyBrandName)){
                            this.showCustomerAgency = 1;
                        }else{
                            this.showCustomerAgency = 2;
                        }
                    }else{
                        this.showCustomerAgency = 3;
                    }

                    // 处理菜单元素
                    if ( this.traderCustomerPortraitDto.traderContactDtoList.length > 0) {
                        this.categoryList.splice(1,0,{
                            label: '联系人',
                            ref: 'lxr'
                        })
                    }
                    if (this.traderCustomerPortraitDto.traderAddressDtoList.length > 0) {
                        this.categoryList.splice(2,0,{
                            label: '联系地址',
                            ref: 'lxdz'
                        })
                    }
                    if (this.traderCustomerPortraitDto.hasRelationCustomer && !(this.traderCustomerPortraitDto.customerType == 426 && this.traderCustomerPortraitDto.customerNature == 465)) {
                        this.categoryList.splice(3,0,{
                            label: '关联公司',
                            ref: 'glgs'
                        })
                    }

                    if (this.traderCustomerPortraitDto.lifeCycle || this.traderCustomerPortraitDto.customerGrade) {
                        this.categoryList.splice(5,0,{
                            label: '决策标签',
                            ref: 'jcbq'
                        })
                    }
                    if (this.traderCustomerPortraitDto.historyTransactionNum) {
                        this.categoryList.splice(6,0,{
                            label: '交易类标签',
                            ref: 'jylbq'
                        })
                    }
                    if (this.traderCustomerPortraitDto.communicateRecordDtoList.length > 0) {
                        this.categoryList.splice(7,0,{
                            label: '沟通记录',
                            ref: 'gtjl'
                        })
                    }
                    if (this.traderCustomerPortraitDto.customerType == 426 && this.traderCustomerPortraitDto.customerNature == 465) {
                        var lastCategory = this.categoryList[this.categoryList.length - 1];
                        if (lastCategory.ref == 'yhfl') {
                            this.categoryList.pop();
                        }
                    }

                    this.categoryTagList = this.traderCustomerPortraitDto.traderTimeTagsMap[this.traderTimeCategory];
                    this.brandTagList = this.traderCustomerPortraitDto.traderTimeTagsMap[this.traderTimeBrand];

                    this.specialCategoryTagList = this.traderCustomerPortraitDto.traderTimeTagsMap["specialCategory"];
                    if (this.traderCustomerPortraitDto.customerType == 426) {
                        if (this.traderCustomerPortraitDto.customerNature == 465) {
                            this.customerClass = 2;
                            this.customerTypeSpan = 1;
                        }
                        if (this.traderCustomerPortraitDto.customerNature == 466) {
                            this.customerClass = 1;
                            this.customerTypeSpan = 1;
                        }
                    }
                    if (this.traderCustomerPortraitDto.customerType == 427) {
                        if (this.traderCustomerPortraitDto.customerNature == 465) {
                            this.customerClass = 4;
                            this.customerTypeSpan = 1;
                        }
                        if (this.traderCustomerPortraitDto.customerNature == 466) {
                            this.customerClass = 3;
                            this.customerTypeSpan = 1;
                        }
                    }
                });

        await getCustomerBehaviorTrace({"traderId": traderId}).then(res=> {
            if(Object.keys(res.data.data).length !== 0) {

	            var webAccountListByParam = res.data.data["webAccountList"];
	            this.activities = res.data.data["behaviorTrace"].list;
	            this.cards = webAccountListByParam.map(item => {
	                return {...item, selected: false};
	            });
	            this.cards = webAccountListByParam.map((item, index) => {
	                if(index===0){
	                this.onCardSelected(item);
	            	}
	            	return { ...item, selected: index === 0 }; // 将第一个卡片设置为选中状态
            	});
        	}
    	});

        //客户档案，初始化
        await getCustomerArchiveTrace({"traderId": traderId,"archiveCursor":vm.archiveCursor}).then(res=> {
            if(Object.keys(res.data.data).length !== 0) {

            	var item = {
                        label: '用户分类&用户行为',
                        ref: 'yhfl'
                    };
                    this.categoryList.splice(8,0,item);

	            var archiveCategoryList = res.data.data["archiveCategoryList"];
	            this.archiveActivities = res.data.data["traderArchivedResDto"].list;

	            //记录历史查询条件
	            this.oldCategory = 0,
                this.oldStartTime = null,
                this.oldEndTime = null

	            this.archiveCards = archiveCategoryList.map(item => {
	                return {...item, selected: false};
	            });
	            this.archiveCards = archiveCategoryList.map((item, index) => {
	                if(index===0){
	                this.onAchiveCardSelected(item);
	            	}
	            	return { ...item, selected: index === 0 }; // 将第一个卡片设置为选中状态
            	});
        	}
    	});

        await getDistributionLinkD3(traderId,  0, 3).then(res => {
            if(res.data.data.edges.length > 0) {
                this.showDistributionLink = true;
            this.categoryList.splice(4,0,{
                label: '经销链路',
                ref: 'jxll'
            });
            }
        });

        await  getCpm({"traderId": traderId}).then(res => {
            var score = res.data.data;
            this.cpm = score;
        });

        const that = this;
        // 关联公司
        that.timer = setInterval(function () {
            if (document.readyState === 'complete') {
                window.clearInterval(that.timer);
                refreshRela();
                refreshRela2();
            }
        }, 1000);
            },

            scrollTo(ref, index){
                if(!this.$refs[ref]){
                    return;
                }
                let top = this.$refs[ref].$el.offsetTop;
                window.scrollTo(0, top);

                setTimeout(() => {
                    this.categoryIndex = ref;
                }, 200)
            },

            handleChange(value) {
                traderTimeCategory = value + 'Category';
                traderTimeBrand = value + 'Brand';
                this.categoryTagList = this.traderCustomerPortraitDto.traderTimeTagsMap[traderTimeCategory];
                this.brandTagList = this.traderCustomerPortraitDto.traderTimeTagsMap[traderTimeBrand];
            },

            toggleCardSelection(selectedCard) {
                this.cards.forEach(card => {
                    if (card !== selectedCard) {
                    card.selected = false; // 将其他卡片置为非选中状态
                    }
                });
                selectedCard.selected = !selectedCard.selected;
                if (selectedCard.selected) {
                    this.timeLinePage = 1;
                    this.onCardSelected(selectedCard);
                }
            },

            onCardSelected(card) {
                // 在卡片选中时触发的操作
                //发送aiax请求给后台接口，参数是card的mobile,获取后更换 this.activities的值
                if(card){
                    this.timeLineCard = card;
                }

                $.ajax({
                    url: page_url+"/traderCustomerAction/get/behaviorByMobile.do",
                    type: "POST",
                    data: {
                        mobile: this.timeLineCard.mobile,
                        pageNo: this.timeLinePage || 1
                    },
                    dataType: "json",
                    success: function (data) {
                        if(vm.timeLinePage != 1){
                            var currentDate = vm.lastDate;
                            data.data.list.forEach(timeLineObj => {
                                if (timeLineObj.userActionTraceDay == currentDate && currentDate !="") {
                                timeLineObj.userActionTraceTime = currentDate==''?(timeLineObj.userActionTraceTime =timeLineObj.userActionTraceTime) :(timeLineObj.userActionTraceTime.substring(11,20));//把日期开头进行一次处理。

                            }else{
                                currentDate = timeLineObj.userActionTraceDay;
                            }
                        });
                            vm.lastDate = currentDate;
                            vm.activities = vm.activities.concat(data.data.list);
                        }else {
                            var currentDate = "";
                            data.data.list.forEach(timeLineObj => {
                                if (timeLineObj.userActionTraceDay == currentDate && currentDate !="") {
                                timeLineObj.userActionTraceTime = currentDate==''?(timeLineObj.userActionTraceTime =timeLineObj.userActionTraceTime) :(timeLineObj.userActionTraceTime.substring(11,20));//把日期开头进行一次处理。
                            }else{
                                currentDate = timeLineObj.userActionTraceDay;
                            }
                        });
                            vm.activities = data.data.list;
                            vm.lastDate = currentDate;
                        }

                        if(data.data.totalPage  > vm.timeLinePage){
                            vm.isShowTmeMore = true;
                        }else {
                            vm.isShowTmeMore = false;
                        }
                    }
                });
            },

            getTimeLineMore(){
                this.timeLinePage++;
                this.onCardSelected();
            },

            //客户档案卡片切换
            toggleArchiveCardSelection(selectedCard) {
            	vm.activeArchiveCard = selectedCard;
            	if(selectedCard.selected == true){
            		return;
            	}
                this.archiveCards.forEach(card => {
                    if (card !== selectedCard) {
                    card.selected = false; // 将其他卡片置为非选中状态
                    }
                });
                selectedCard.selected = !selectedCard.selected;
                if (selectedCard.selected) {
                    this.timeLineArchivePage = 1;
                    this.onAchiveCardSelected(selectedCard);
                    vm.activeArchiveCard = selectedCard;
                }
            },

            //客户档案卡片点击查询
            onAchiveCardSelected(archiveCard){
            	// 在卡片选中时触发的操作
                //发送aiax请求给后台接口，参数是card的categoryId,获取后更换 this.archiveActivities的值
                if(archiveCard){
                    this.timeLineArchiveCard = archiveCard;
                }
                debugger;
            	//判断查询条件是否变更,如果变更,archiveCursor 清空,并且更新old条件为最新
            	if(this.oldCategory != this.timeLineArchiveCard.categoryId || this.oldStartTime != this.startTime  || this.oldEndTime != this.endTime){
            		vm.archiveCursor = '';
            		this.oldCategory = this.timeLineArchiveCard.categoryId;
            		this.oldStartTime = this.startTime;
            		this.oldEndTime = this.endTime;
            		vm.archiveActivities = [];
            		vm.timeLineArchivePage = 1;
            	}

                $.ajax({
                    url: page_url+"/traderCustomerAction/get/archiveByTraderId.do",
                    type: "POST",
                    data: {
                    	traderId:traderId,
                        categoryId: this.timeLineArchiveCard.categoryId,
                        pageNo: this.timeLinePage || 1,
                        startTime:this.startTime,
                        endTime:this.endTime,
                        archiveCursor:vm.archiveCursor
                    },
                    dataType: "json",
                    success: function (data) {
                    	debugger;
                        if(vm.timeLineArchivePage != 1){
                            var currentDate = vm.archiveLastDate;
                            data.data.list.forEach(timeLineObj => {
	                           	var eventDay = timeLineObj.eventTime.substring(0,10);
	                            if (eventDay == currentDate && currentDate !="") {
	                               	timeLineObj.eventTime = currentDate==''?(timeLineObj.eventTime = timeLineObj.eventTime) :(timeLineObj.eventTime.substring(11,20));//把日期开头进行一次处理。
	                            }else{
	                                currentDate = eventDay;
	                            }
                        });
                            vm.archiveLastDate = currentDate;
                            vm.archiveActivities = vm.archiveActivities.concat(data.data.list);
                        }else {
                            var currentDate = "";
                            data.data.list.forEach(timeLineObj => {
                            	var eventDay = timeLineObj.eventTime.substring(0,10);
                                if (eventDay == currentDate && currentDate !="") {
                                	timeLineObj.eventTime = currentDate==''?(timeLineObj.eventTime =timeLineObj.eventTime) :(timeLineObj.eventTime.substring(11,20));//把日期开头进行一次处理。
	                            }else{
	                                currentDate = eventDay;
	                            }
                        });
                            vm.archiveActivities = data.data.list;
                            vm.archiveLastDate = currentDate;
                        }
                        vm.archiveCursor = data.data.cursor;
                        console.log("============"+this.archiveCursor);
                        if(vm.archiveActivities!=null && vm.archiveCursor!=null){
                            vm.isShowTmeArchiveMore = true;
                        }else {
                            vm.isShowTmeArchiveMore = false;
                        }
                    }
                });
            },

            getArchiveTimeLineMore(){
                this.timeLineArchivePage++;
                this.onAchiveCardSelected();
            },

            playrecord(url) {
                if (url != '') {
                    layer.open({
                        type: 2,
                        shadeClose: false, //点击遮罩关闭
                        area: ['360px', '80px'],
                        title: false,
                        content: ['/system/call/getrecordpaly.do?url=' + url],
                        success: function (layero, index) {
                            //layer.iframeAuto(index);
                        },
                        error: function (data) {
                            if (data.status == 1001) {
                                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                            }
                        }
                    });
                }
            },

            viewBusinessCards(traderContactId) {
                layer.open({
                    type: 2,
                    shadeClose: false, //点击遮罩关闭
                    area: ['500px', '400px'],
                    title: false,
                    content: ['/trader/customer/viewBusinessCards.do?traderContactId=' + traderContactId],
                    success: function (layero, index) {
                    },
                    error: function (data) {
                        if (data.status == 1001) {
                            layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                        }
                    }
                });
            },
            toggleShowCategory(){
                this.isShowCategory = !this.isShowCategory;
            },
            checkCategoryIndex(){
                let top = $(window).scrollTop();
                let ref = "";

                $('.J-block').each(function(i, item){
                    if($(this).offset().top - 40 < top){
                        ref = $(this).data('ref');
                    }
                });

                this.categoryIndex = ref;
            },

            // 查看cpm雷达图
            viewCpm() {
                this.dialogVisible = true;
                setTimeout(()=>{
                    this.show1()
                },20)
            },

            // 查看ai助理信息
            searchRecord() {
                getLatestSummary({"traderId" :traderId}).then(res => {
                    this.communicateAiSummaryDto = res.data.data;
                })
            },

            // 修正沟通记录弹窗
            modifySummary() {
                layer.open({
                    type: 2,
                    content: '/communicateSummary/modify/dialog.do?communicateSummaryId=' + this.communicateAiSummaryDto.communicateSummaryId,
                    area: ['80%', '80%'],
                    title :'沟通内容摘要'
                });
            },

            show1() {
                var chartDom = document.getElementById('main');
                var myChart = echarts.init(chartDom);
                var option;
                option = {
                    radar: {
                        splitNumber:4,              //指示器轴的分割段数
                        indicator: [
                            { name: '主营客户(D)', max: 20 },
                            { name: '主营商品类型(P)', max: 20 },
                            { name: '主营商品范畴(P)', max: 20 },
                            { name: '经营模式(M)', max: 20 },
                            { name: '经营区域(D)', max: 20 }
                        ],
                        axisLabel:{                 //坐标轴刻度标签的相关设置
                            show:true,
                            formatter: function (value, index) { //使用函数模板，函数参数分别为刻度数值（类目），刻度的索引
                                return value+"%";
                            },
                            showMinLabel:false
                        }
                    },
                    series: [
                        {
                            name: 'Budget vs spending',
                            type: 'radar',
                            data: [
                                {
                                    value: [this.cpm.cscore, this.cpm.pskuTypeScore, this.cpm.pskuScopeScore, this.cpm.psaleTypeScore, this.cpm.pareaScore],
                                    name: 'Allocated Budget'
                                }
                            ]
                        }
                    ]
                };
                option && myChart.setOption(option);
            } ,

            handleClose() {
                this.dialogVisible = false;
            }
        }
    })
</script>

<script>
    var container = document.getElementById('backtop-container');
    container.addEventListener('click', function() {
        // 这里是点击事件的处理代码
        vm.aiDialogVisible = true;
    });
</script>

<script>
    var colorArrayAll = ["#ec7c7d","#ef6191","#b969ca","#9d7ced","#7777db","#5d81a5","#4cb7ff","#58c9c9","#5ec9a6","#59d25a","#b3dd62","#f5d44d","#fe934d","#a47d70","#E1E5E8"];//定义15个不同的颜色值，不变
    var colorArray = colorArrayAll;   //随机数取颜色值，取一个移除一个
    function getColor() {
        var index = Math.floor(Math.random()*colorArray.length);
        var current = colorArray[index];
        colorArray = colorArray.filter(function(item) {
            return item !== current;
        });
        if(colorArray.length == 0){
            colorArray =colorArrayAll;
        }
        return current;
    }

    var width = 600;
    var height = 400;
    var img_w = 60;
    var img_h = 60;
    var radius = 30;    //圆形半径



    // 查询组装关联信息
    var currentTraderId = '${traderId}';
    $(document).ready(function() {

    });

    function refreshRela() {
        var url = '/traderCustomerBase/get/customer/relation.do?traderId='+currentTraderId;
        var svg = d3.select("#relationCompany").append("svg")
            .attr("width",width)
            .attr("height",height);
        d3.json(url,function(error,root){
            root = root.data;
            //D3力导向布局
            var force = d3.layout.force()
                .nodes(root.nodes)
                .links(root.edges)
                .size([width,height])
                .linkDistance(200)
                .charge(-1500)
                .start();

            //边
            var edges_line = svg.selectAll("line")
                .data(root.edges)
                .enter()
                .append("line")
                .style("stroke","#ccc")
                .style("stroke-width",1);

            //边上的文字（人物之间的关系）
            var edges_text = svg.selectAll(".linetext")
                .data(root.edges)
                .enter()
                .append("text")
                .attr("class","linetext")
                .text(function(d){
                    return d.relation;
                });

            // 圆形图片节点（人物头像）
            var nodes_img = svg.selectAll("image")
                .data(root.nodes)
                .enter()
                .append("circle")
                .attr("class", "circleImg")
                .attr("r", radius)
                .attr("fill", function(d, i){
                    //创建圆形图片
                    var defs = svg.append("defs").attr("id", "imgdefs")
                    var catpattern = defs.append("pattern")
                        .attr("id", "catpattern" + i)
                        .attr("height", 1)
                        .attr("width", 1)
                    catpattern.append("image")
                        .attr("x", - (img_w / 2 - radius))
                        .attr("y", - (img_h / 2 - radius))
                        .attr("width", img_w)
                        .attr("height", img_h)
                    return getColor();
                }).call(force.drag);


            var text_dx = -20;
            var text_dy = 20;

            var nodes_text = svg.selectAll(".nodetext")
                .data(root.nodes)
                .enter()
                .append("text")
                .attr("class","nodetext")
                .attr("dx",text_dx)
                .attr("dy",text_dy)
                .text(function(d){
                    return d.name;
                });


            force.on("tick", function(){

                //限制结点的边界
                root.nodes.forEach(function(d,i){
                    d.x = d.x - img_w/2 < 0     ? img_w/2 : d.x ;
                    d.x = d.x + img_w/2 > width ? width - img_w/2 : d.x ;
                    d.y = d.y - img_h/2 < 0      ? img_h/2 : d.y ;
                    d.y = d.y + img_h/2 + text_dy > height ? height - img_h/2 - text_dy : d.y ;
                });

                //更新连接线的位置
                edges_line.attr("x1",function(d){ return d.source.x; });
                edges_line.attr("y1",function(d){ return d.source.y; });
                edges_line.attr("x2",function(d){ return d.target.x; });
                edges_line.attr("y2",function(d){ return d.target.y; });

                //更新连接线上文字的位置
                edges_text.attr("x",function(d){ return (d.source.x + d.target.x) / 2 ; });
                edges_text.attr("y",function(d){ return (d.source.y + d.target.y) / 2 ; });


                //更新结点图片和文字
                nodes_img.attr("cx",function(d){ return d.x });
                nodes_img.attr("cy",function(d){ return d.y });

                nodes_text.attr("x",function(d){ return d.x });
                nodes_text.attr("y",function(d){ return d.y + img_w/2; });
            });
        });
    }

</script>

<script>
    var colorArrayAll = ["#ec7c7d","#ef6191","#b969ca","#9d7ced","#7777db","#5d81a5","#4cb7ff","#58c9c9","#5ec9a6","#59d25a","#b3dd62","#f5d44d","#fe934d","#a47d70","#E1E5E8"];//定义15个不同的颜色值，不变
    var colorArray = colorArrayAll;   //随机数取颜色值，取一个移除一个
    function getColor() {
        var index = Math.floor(Math.random()*colorArray.length);
        var current = colorArray[index];
        colorArray = colorArray.filter(function(item) {
            return item !== current;
        });
        if(colorArray.length == 0){
            colorArray =colorArrayAll;
        }
        return current;
    }

    var width = 800;
    var height = 600;
    var img_w = 60;
    var img_h = 60;
    var radius = 20;    //圆形半径调整为20



    // 查询组装关联信息
    var currentTraderId = '${traderId}';
    $(document).ready(function() {

    });

    function refreshRela2() {
        d3.select("#distributionLink").html('');
        var linkSourceType = vm.linkSourceType;
        var cooperationTimeFrame = vm.cooperationTimeFrame;
        var url = '/traderCustomerBase/distributionLink/d3.do?traderId='+currentTraderId + '&linkSourceType=' + linkSourceType + '&cooperationTimeFrame=' + cooperationTimeFrame;
        var svg = d3.select("#distributionLink").append("svg")
            .attr("width",width)
            .attr("height",height);
        d3.json(url,function(error,root){
            root = root.data;
            if(root.nodes.length <=1){
                d3.select("#distributionLink").html("<div style='justify-content: center;display: flex;align-items: center;text-align:center;width:"+width+"px;height:"+height+"px;'>暂无数据</div>");
                return ;
            }
//D3力导向布局
            var force = d3.layout.force()
                .nodes(root.nodes)
                .links(root.edges)
                .size([width,height])
                .linkDistance(200)
                .charge(-1500)
                .start();

//边
            var edges_line = svg.selectAll("line")
                .data(root.edges)
                .enter()
                .append("line")
                .style("stroke","#ccc")
                .style("stroke-width",1);

//边上的文字（人物之间的关系）
            var edges_text = svg.selectAll(".linetext")
                .data(root.edges)
                .enter()
                .append("text")
                .attr("class","linetext")
                .text(function(d){
                    switch (linkSourceType) {
                        case "0":
                            return "";
                        case "1":
                            return (d.relation==undefined || d.relation == ""|| d.relation == null)?"中标0次":("中标"+d.relation+"次");
                        case "2":
                            return (d.relation==undefined || d.relation == ""|| d.relation == null)?"交易0次":("交易"+d.relation+"次");
                        case "3":
                            return (d.relation==undefined || d.relation == ""|| d.relation == null)?"商机0次":("商机"+d.relation+"次");
                        case "4":
                            return (d.relation==undefined || d.relation == ""|| d.relation == null)?"报价0次":("报价"+d.relation+"次");
                        default:
                            return "";
                    }
                });

            let maxRelation = 10; // 初始值设置10

            root.nodes.forEach(node => {
                    if (node.relation !== null && node.relation > maxRelation) {
                    maxRelation = node.relation;
                }
            });

            // 圆形图片节点（人物头像）
            var nodes_img = svg.selectAll("image")
                .data(root.nodes)
                .enter()
                .append("circle")
                .attr("class", "circleImg")
                .attr("r", function (d,i) {
                    if(d.relation==undefined || d.relation == ""|| d.relation == null){
                        return radius;
                    }else{
                        return radius +((d.relation>100)?10:d.relation/(maxRelation>10?10:1));

                    }
                })
                .attr("fill", function(d, i){
//创建圆形图片
                    var defs = svg.append("defs").attr("id", "imgdefs")
                    var catpattern = defs.append("pattern")
                        .attr("id", "catpattern" + i)
                        .attr("height", 1)
                        .attr("width", 1)
                    catpattern.append("image")
                        .attr("x", - (img_w / 2 - radius))
                        .attr("y", - (img_h / 2 - radius))
                        .attr("width", img_w)
                        .attr("height", img_h)
                    return getColor();
                }).call(force.drag);


            var text_dx = -20;
            var text_dy = 20;

            var nodes_text = svg.selectAll(".nodetext")
                .data(root.nodes)
                .enter()
                .append("text")
                .attr("class","nodetext")
                .attr("dx",text_dx)
                .attr("dy",text_dy)
                .text(function(d){
                    return d.name;
                });


            force.on("tick", function(){

//限制结点的边界
                root.nodes.forEach(function(d,i){
                    d.x = d.x - img_w/2 < 0     ? img_w/2 : d.x ;
                    d.x = d.x + img_w/2 > width ? width - img_w/2 : d.x ;
                    d.y = d.y - img_h/2 < 0      ? img_h/2 : d.y ;
                    d.y = d.y + img_h/2 + text_dy > height ? height - img_h/2 - text_dy : d.y ;
                });

//更新连接线的位置
                edges_line.attr("x1",function(d){ return d.source.x; });
                edges_line.attr("y1",function(d){ return d.source.y; });
                edges_line.attr("x2",function(d){ return d.target.x; });
                edges_line.attr("y2",function(d){ return d.target.y; });

//更新连接线上文字的位置
                edges_text.attr("x",function(d){ return (d.source.x + d.target.x) / 2 ; });
                edges_text.attr("y",function(d){ return (d.source.y + d.target.y) / 2 ; });


//更新结点图片和文字
                nodes_img.attr("cx",function(d){ return d.x });
                nodes_img.attr("cy",function(d){ return d.y });

                nodes_text.attr("x",function(d){ return d.x });
                nodes_text.attr("y",function(d){ return d.y + img_w/2; });
            });
        });
    }
</script>

<style>
    .el-descriptions__title {
        margin-left: 15px;
    }

    .el-descriptions__header {
        margin-bottom: 9px;
        padding-top: 9px;
    }

    .el-descriptions {
        background-color: #c5ddfb;
    }

    .el-descriptions-item__cell.el-descriptions-item__content {
        text-align: center !important;
    }

    .el-descriptions-item__cell.el-descriptions-item__label.is-bordered-label {
        text-align: center;
        width: 10%;
    }

    .el-input--mini .el-input__icon {
        line-height: 0px;
    }

    .el-card__header {
        height: 34px;
        background-color: #c5ddfb;
        box-sizing: border-box;
        padding-left: 15px;
        font-weight: 700;
        padding-top: 9px;
        padding-bottom: 30px;
        color: #303133;
    }

    .el-card__body {
        padding: 0;
    }

    .el-dialog__header {
        background-color: #f3f3f3;
        border-bottom: 1px solid #ddd;
        border-radius: 2px 2px 0 0;
        height: 35px;
        padding: 7px 15px 0;
    }

    .el-dialog__title {
        font-size: 14px;
        margin-left: 0;
    }

    .el-dialog__headerbtn {
        margin-top: -5px;
    }

    .el-dialog__body {
        padding: 20px 20px;
    }

    .my-content {
        width: 40%;
    }

    .category-wrap {
        position: fixed;
        width: 150px;
        background: #fff;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        z-index: 11;
        border: 1px solid #ced3d9;
        padding: 0;
        transition: right .22s ease;
    }

    .category-wrap.hide {
         right: -150px;
     }

    .category-wrap .category-item {
        padding: 0 20px;
        line-height: 40px;
        height: 40px;
        cursor: pointer;
    }

    .category-wrap .category-item:hover {
        background: #eee;
    }

    .category-wrap .category-item.active {
        background: #c5ddfb;
    }

    .category-wrap .slide-btn {
        position: absolute;
        left: -30px;
        top: calc(50% - 25px);
        width: 30px;
        background: #fff;
        border: 1px solid #ced3d9;
        border-radius: 3px 0 0 3px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }

    .category-wrap .slide-btn:hover {
         color: #09f;
     }

    .nodetext {
        font-size: 12px ;
        font-family: SimSun;
        fill:#000000;
    }

    .linetext {
        font-size: 12px ;
        font-family: SimSun;
        fill:#1f77b4;
        fill-opacity:1.0;
    }

    .circleImg {
        stroke-width: 1.5px;
    }

    .card {
        width: 200px;
        height: 100px;
        background-color: #a6a6a6;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        margin: 10px;
        user-select: none;
    }

    .selected {
        background-color: #1e80ff;
        color: white;
    }

    .el-timeline-item__node {
        position: absolute;
        background-color: #1e80ff;
        border-radius: 50%;
        display: flex;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
    }

    .el-card__header .clearfix{
        font-size: 16px;
        font-weight: 700;
    }
    .el-timeline-item__timestamp {
        width: 130px;
        text-align: right;
    }
    .el-timeline-item__timestamp {
        position: absolute;
        left: -140px;
        top: 0;
        transform: translateY(-50%);
    }

    .el-dialog__headerbtn {
        font-size: 13px;
    }

    .el-backtop i {
        background: none !important;
    }

    .el-backtop {
        width: 65px;
        height: 65px;
        font-size: 12px;
        border: 2px solid;
        color: #7c8083;
    }

    .my-card .el-card__header {
        background-color: #d5d9dd;
    }

    .container {
        display: flex;
        align-items: center;
    }

    .circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #D5D9DD;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .text {
        color: rgba(0, 0, 0, 0.97);
        font-size: 12px;
    }

    .row {
        margin-left: 10px;
    }
</style>
