package com.vedeng.goods.model.entity;

import java.io.Serializable;

/**
 * SKU授权内容（类型）
 *
 * <AUTHOR>
 * @Date 2020/9/24
 */
public class SkuAuthorizationTerminalType implements Serializable {
    /**
     * 授权类型ID
     */
    private Integer skuAuthorizationTerminaltypeId;

    /**
     * SKU
     */
    private Integer skuId;

    /**
     * 授权类型ID
     */
    private Integer terminalTypeId;

    /**
     * 雪花ID
     */
    private Long snowFlakeId;

    /**
     * 是否启用
     */
    private Integer isEnable;

    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 修改时间
     */
    private Long modTime;

    /**
     * 最后一次更新者
     */
    private Integer updater;

    public SkuAuthorizationTerminalType() {
    }

    public SkuAuthorizationTerminalType(Integer skuId, Integer terminalTypeId, Long snowFlakeId,  Long addTime, Integer creator) {
        this.skuId = skuId;
        this.terminalTypeId = terminalTypeId;
        this.snowFlakeId = snowFlakeId;
        this.addTime = addTime;
        this.creator = creator;
    }

    public Integer getSkuAuthorizationTerminaltypeId() {
        return skuAuthorizationTerminaltypeId;
    }

    public void setSkuAuthorizationTerminaltypeId(Integer skuAuthorizationTerminaltypeId) {
        this.skuAuthorizationTerminaltypeId = skuAuthorizationTerminaltypeId;
    }

    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    public Integer getTerminalTypeId() {
        return terminalTypeId;
    }

    public void setTerminalTypeId(Integer terminalTypeId) {
        this.terminalTypeId = terminalTypeId;
    }

    public Long getSnowFlakeId() {
        return snowFlakeId;
    }

    public void setSnowFlakeId(Long snowFlakeId) {
        this.snowFlakeId = snowFlakeId;
    }

    public Integer getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(Integer isEnable) {
        this.isEnable = isEnable;
    }

    public Long getAddTime() {
        return addTime;
    }

    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Long getModTime() {
        return modTime;
    }

    public void setModTime(Long modTime) {
        this.modTime = modTime;
    }

    public Integer getUpdater() {
        return updater;
    }

    public void setUpdater(Integer updater) {
        this.updater = updater;
    }
}
