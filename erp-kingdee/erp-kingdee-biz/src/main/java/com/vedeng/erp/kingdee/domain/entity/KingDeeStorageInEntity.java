package com.vedeng.erp.kingdee.domain.entity;

import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseEntity;
import java.util.Date;

import com.vedeng.common.mybatis.jbatis.annotation.Column;
import com.vedeng.common.mybatis.jbatis.annotation.GeneratedValue;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.common.mybatis.jbatis.annotation.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
    * 其他入库
    */
@Getter
@Setter
@ToString
@Table(name = "KING_DEE_STORAGE_IN")
public class KingDeeStorageInEntity extends BaseEntity {
    /**
    * id
    */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
    * 单据内码  0：表示新增* 非0：云星空系统单据FID值，表示修改
    */
    @Column(name = "FID")
    private String fId;

    /**
    * 单据编号 填写单据编号，若为空则调用系统的编码规则生成
    */
    private String fBillNo;

    /**
    * 贝登单据头ID 贝登单据头ID号（预留）
    */
    private String fQzokBddjtId;

    /**
    * 库存方向 如果是普通入库，默认值 ：GENERAL* 如果是退货，则默认值 ： RETURN
    */
    private String fStockDirect;

    /**
    * 单据日期 格式yyyy-MM-dd
    */
    private String fDate;

    /**
    * 单据类型 填单据类型编码，默认QTRKD01_SYS
    */
    private String fBillTypeId;

    /**
    * 库存组织 填写组织编码
    */
    private String fStockOrgId;

    /**
    * 供应商 填写供应商编码
    */
    private String fSupplierId;

    /**
     * 客户编码
     */
    private String fQzokKh;

    /**
    * 部门 填写部门编码，默认值 ：BM9999
    */
    private String fDeptId;

    /**
    * 明细
    */
    @Column(jdbcType = "VARCHAR")
    private JSONArray fEntity;
}