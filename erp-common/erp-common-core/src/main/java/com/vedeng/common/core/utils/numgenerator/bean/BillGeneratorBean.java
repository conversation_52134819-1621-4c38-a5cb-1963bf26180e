package com.vedeng.common.core.utils.numgenerator.bean;

import com.vedeng.common.core.utils.numgenerator.enums.BillType;
import lombok.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 单据编号生成参数类
 * @date 2022/7/13 11:06
 */
@Getter
@Setter
@Builder
public class BillGeneratorBean   {


    /**
     * 生成编号类型枚举
     */
    private BillType billType;

    /**
     * 订单生成参数
     */
    private NoGeneratorBean noGeneratorBean;


    public BillGeneratorBean() {
    }

    public BillGeneratorBean(BillType billType) {
        this.billType = billType;
        this.noGeneratorBean = billType.getNoGeneratorBean();
    }

    public BillGeneratorBean(BillType billType, NoGeneratorBean noGeneratorBean) {
        this.billType = billType;
        this.noGeneratorBean = noGeneratorBean;
    }
}
