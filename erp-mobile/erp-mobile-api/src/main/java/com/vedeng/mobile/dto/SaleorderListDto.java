package com.vedeng.mobile.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 销售订单列表查询返回结果类
 */
@Data
public class SaleorderListDto {

    /**
     * 销售订单id
     */
    private Integer saleOrderId;

    /**
     * 销售订单号
     */
    private String saleOrderNo;

    /**
     * 销售订单状态
     */
    private Integer orderStatus;

    /**
     * 客户名称
     */
    private String traderName;

    /**
     * 归属销售Id
     */
    private Integer currentUserId;

    /**
     * 归属销售名称
     */
    private String currentUserName;

    /**
     * 是否有售后信息
     */
    private Boolean haveAfterSalesInfo;

    /**
     * 是否有物流信息
     */
    private Boolean haveExpressInfo;

    /**
     * 是否有发票信息
     */
    private Boolean haveInvoiceInfo;

    /**
     * 是否有合同信息
     */
    private Boolean haveContractInfo;

    /**
     * 商品总数
     */
    private Integer goodsNum;

    /**
     * 开票方式:1手动纸质开票、2自动纸质开票、3自动电子发票
     */
    private Integer invoiceMethod;

    /**
     * 是否寄送发票 0否 1是
     */
    private Integer isSendInvoice;

    /**
     * 订单总价
     */
    private BigDecimal totalAmount;

    /**
     * 商品明细信息
     */
    private List<SaleorderListOfGoodsDto> saleOrderGoodsList;

}
