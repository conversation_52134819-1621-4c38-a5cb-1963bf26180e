package com.vedeng.finance.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.vedeng.finance.model.CapitalBillDetail;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.context.annotation.Bean;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * easy excel 导出类
 * <AUTHOR>
 * @date 2022/3/2 15:07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@HeadFontStyle(fontHeightInPoints = 20)
@ContentFontStyle(fontHeightInPoints = 11,fontName = "宋体")
//@ColumnWidth(200)
public class BankBillVo{

    @ExcelProperty(index = 0,value = "流水号")
//    @ColumnWidth(260)
    private String tranFlow;

    @ExcelProperty(index = 4,value = "交易时间")
//    @ColumnWidth(160)
    private String realTrandatetime;

    @ExcelProperty(index = 5,value = "摘要")
//    @ColumnWidth(140)
    private String message;

    @ExcelProperty(index = 6,value = "收款金额")
//    @ColumnWidth(100)
    private BigDecimal inAmt;

    @ExcelProperty(index = 7,value = "付款金额")
//    @ColumnWidth(100)
    private BigDecimal outAmt;

    @ExcelProperty(index = 8,value = "银行名称")
    private String bankName;

    @ExcelProperty(index = 9,value = "银行余额")
//    @ColumnWidth(100)
    private String amt1;

    @ExcelProperty(index = 3,value = "对方账号")
//    @ColumnWidth(180)
    private String accno2;

    @ExcelProperty(index = 1,value = "对方名称")
//    @ColumnWidth(180)
    private String accName1;

    @ExcelProperty(index = 10,value = "备注")
//    @ColumnWidth(180)
    private String det;

    @ExcelProperty(index = 11,value = "剩余结款金额")
//    @ColumnWidth(100)
    private BigDecimal balance;

    @ExcelProperty(index = 2,value = "对方开户机构")
//    @ColumnWidth(260)
    private String cadbankNm;

    @ExcelProperty(index = 14,value = "创建时间")
//    @ColumnWidth(120)
    private String addTimeStr;

    @ExcelProperty(index = 15,value = "生效时间")
//    @ColumnWidth(120)
    private String validTimeStr;

    @ExcelProperty(index = 16,value = "合同金额")
//    @ColumnWidth(100)
    private BigDecimal totalAmount;

    @ExcelProperty(index = 17,value = "已结款金额")
//    @ColumnWidth(100)
    private BigDecimal receivedAmount;

    @ExcelProperty(index = 18,value = "本次到款")
//    @ColumnWidth(100)
    private BigDecimal thisAmount;

    @ExcelProperty(index = 19,value = "收款名称")
//    @ColumnWidth(180)
    private String payee;

    @ExcelProperty(index = 20,value = "备注")
//    @ColumnWidth(200)
    private String comments;//备注


    @ExcelProperty(index = 12,value = "订单号")
//    @ColumnWidth(240)
    private String saleorderNo;//订单号

    @ExcelProperty(index = 13,value = "合同名称")
//    @ColumnWidth(200)
    private String traderName;//客户名称

    @ExcelProperty(index = 21,value = "金蝶凭证号")
//    @ColumnWidth(200)
    private String financeVoucherNo;//金蝶凭证号

    @ExcelProperty(index = 22,value = "发送结果")
//    @ColumnWidth(200)
    private String isSend;

    @ExcelProperty(index = 24,value = "匹配项目")
//    @ColumnWidth(200)
    private String matchedObjectName;//匹配项目名称

    @ExcelProperty(index = 23,value = "商户订单号")
    private String orderNo;

}