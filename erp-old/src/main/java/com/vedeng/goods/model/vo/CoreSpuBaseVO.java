package com.vedeng.goods.model.vo;

import com.beust.jcommander.internal.Lists;
import com.vedeng.common.constant.goods.GoodsCheckStatusEnum;
import com.vedeng.common.constant.goods.SpuLevelEnum;
import com.vedeng.common.util.DateUtil;
import com.vedeng.goods.model.dto.CoreSpuBaseDTO;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.List;

public class CoreSpuBaseVO extends CoreSpuBaseDTO {

	private String registrationNumber;
	private String spuTypeShow;
	@Deprecated
	private String spuLevelShow;// 0:其他产品,1:核心产品、2:临时产品、

	private String checkStatusShow;
	private String modTimeShow;
	private String operateInfoIdShow;
	private Integer skuTotalSize = 0;
	private String skuIdsInSpu;
	private List<CoreSkuBaseVO> coreSkuBaseVOList = Lists.newArrayList();

	public List<CoreSkuBaseVO> getCoreSkuBaseVOList() {
		return coreSkuBaseVOList;
	}

	/**
	 * 归属经理名称
	 *
	 * @since ERP_LV_2020_69
	 */
	private String productMgrName;

	/**
	 * 归属产品助理
	 *
	 * @since ERP_LV_2020_105
	 */
	private String productAssistantName;

	/**
	 * 商品级别
	 *
	 * @since ERP_LV_2020_105
	 */
	private String goodsLevelName;

	/**
	 * 商品档位
	 *
	 * @since  ERP_LV_2020_105
	 */
	private String goodsPositionName;

	/**
	 * 税收分类编码
	 */
	private String taxClassificationCode;

	public String getTaxClassificationCode() {
		return taxClassificationCode;
	}

	public void setTaxClassificationCode(String taxClassificationCode) {
		this.taxClassificationCode = taxClassificationCode;
	}

	public void setCoreSkuBaseVOList(List<CoreSkuBaseVO> coreSkuBaseVOList) {
		this.coreSkuBaseVOList = coreSkuBaseVOList;
	}

	public String getSpuTypeShow() {
		return spuTypeShow;
	}

	public void setSpuTypeShow(String spuTypeShow) {


		this.spuTypeShow = spuTypeShow;
	}
	public String getRegistrationNumber() {
		return registrationNumber;
	}

	public void setRegistrationNumber(String registrationNumber) {
		this.registrationNumber = registrationNumber;
	}
	public String getSpuLevelShow() {
		spuLevelShow = SpuLevelEnum.levelName(getSpuLevel());
		return spuLevelShow;
	}

	public void setSpuLevelShow(String spuLevelShow) {
		this.spuLevelShow = spuLevelShow;
	}

	public String getCheckStatusShow() {
		checkStatusShow = GoodsCheckStatusEnum.statusName(getCheckStatus());
		return checkStatusShow;
	}

	public void setCheckStatusShow(String checkStatusShow) {
		this.checkStatusShow = checkStatusShow;
	}

	public String getModTimeShow() {
		if (getModTime() != null) {
			modTimeShow = DateFormatUtils.format(getModTime(), DateUtil.TIME_FORMAT);
		}
		return modTimeShow;
	}

	public void setModTimeShow(String modTimeShow) {
		this.modTimeShow = modTimeShow;
	}

	public String getOperateInfoIdShow() {
		operateInfoIdShow = NumberUtils.toInt(getOperateInfoId() + "") > 0 ? "已添加" : "未添加";
		return operateInfoIdShow;
	}

	public void setOperateInfoIdShow(String operateInfoIdShow) {
		this.operateInfoIdShow = operateInfoIdShow;
	}

	public Integer getSkuTotalSize() {
		return skuTotalSize;
	}

	public void setSkuTotalSize(Integer skuTotalSize) {
		this.skuTotalSize = skuTotalSize;
	}

	public String getSkuIdsInSpu() {
		return skuIdsInSpu;
	}

	public void setSkuIdsInSpu(String skuIdsInSpu) {
		this.skuIdsInSpu = skuIdsInSpu;
	}

	@Override
	public String getProductMgrName() {
		return productMgrName;
	}

	@Override
	public void setProductMgrName(String productMgrName) {
		this.productMgrName = productMgrName;
	}

	public String getProductAssistantName() {
		return productAssistantName;
	}

	public void setProductAssistantName(String productAssistantName) {
		this.productAssistantName = productAssistantName;
	}

	public String getGoodsLevelName() {
		return goodsLevelName;
	}

	public void setGoodsLevelName(String goodsLevelName) {
		this.goodsLevelName = goodsLevelName;
	}

	public String getGoodsPositionName() {
		return goodsPositionName;
	}

	public void setGoodsPositionName(String goodsPositionName) {
		this.goodsPositionName = goodsPositionName;
	}
}
