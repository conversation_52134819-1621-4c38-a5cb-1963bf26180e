package com.vedeng.erp.common.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.vedeng.common.core.base.BaseDto;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 销售人员和业务数据关系表
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class RSalesJBusinessOrderDto extends BaseDto {

    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date addTime;

    /**
     * 修改时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date modTime;
    /**
     * 主键
     */
    private Integer id;

    /**
     * 业务类型
     * 1.商机
     * 2.报价
     * 3.订单
     * 4.售后
     * 5.线索
     */
    private Integer businessType;

    /**
     * 业务ID
     */
    private Integer businessId;

    /**
     * 业务单号
     */
    private String businessNo;

    /**
     * 销售人员ID
     */
    private Integer saleUserId;

    /**
     * 销售人员名称
     */
    private String saleUserName;

    /**
     * 销售人员中文名称
     */
    private String saleUserCnName;

    /**
     * 操作人中文名称
     */
    private String creatorCnName;


    //ALIAS_HEAD_PICTURE
    private String saleAliasHeadPicture;//用户头像

    /**
     * 协作人标签
     * 1: 线下销售
     * 2: 产线负责人
     * 3: 手动添加
     */
    private Integer shareTag;

    /**
     * 所有协作人标签，按优先级排序的列表
     */
    private List<Integer> shareTags;
}