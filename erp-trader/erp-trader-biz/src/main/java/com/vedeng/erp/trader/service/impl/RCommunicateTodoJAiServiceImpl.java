package com.vedeng.erp.trader.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.trader.domain.entity.CommunicateRecordEntity;
import com.vedeng.erp.trader.domain.entity.TraderContactEntity;
import com.vedeng.erp.trader.domain.entity.VoiceFieldResultEntity;
import com.vedeng.erp.trader.dto.RCommunicateTodoJAiDto;
import com.vedeng.erp.trader.mapper.CommunicateRecordMapper;
import com.vedeng.erp.trader.mapper.TraderContactMapper;
import com.vedeng.erp.trader.mapper.TraderVoiceFieldResultMapper;
import com.vedeng.erp.trader.mapstruct.RCommunicateTodoJAiConvertor;
import com.vedeng.erp.trader.service.RCommunicateTodoJAiApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import org.springframework.beans.factory.annotation.Autowired;

import com.vedeng.erp.trader.mapper.RCommunicateTodoJAiMapper;
import com.vedeng.erp.trader.domain.entity.RCommunicateTodoJAiEntity;
import com.vedeng.erp.trader.service.RCommunicateTodoJAiService;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class RCommunicateTodoJAiServiceImpl implements RCommunicateTodoJAiService, RCommunicateTodoJAiApiService {

    @Autowired
    private RCommunicateTodoJAiMapper rCommunicateTodoJAiMapper;
    @Autowired
    private RCommunicateTodoJAiConvertor rCommunicateTodoJAiConvertor;
    @Autowired
    private CommunicateRecordMapper communicateRecordMapper;
    @Autowired
    private TraderVoiceFieldResultMapper traderVoiceFieldResultMapper;
    @Autowired
    private TraderContactMapper traderContactMapper;


    @Override
    public int deleteByPrimaryKey(Integer communicateInfoId) {
        return rCommunicateTodoJAiMapper.deleteByPrimaryKey(communicateInfoId);
    }

    @Override
    public int insert(RCommunicateTodoJAiEntity record) {
        return rCommunicateTodoJAiMapper.insert(record);
    }

    @Override
    public int insertSelective(RCommunicateTodoJAiEntity record) {
        return rCommunicateTodoJAiMapper.insertSelective(record);
    }

    @Override
    public RCommunicateTodoJAiEntity selectByPrimaryKey(Integer communicateInfoId) {
        return rCommunicateTodoJAiMapper.selectByPrimaryKey(communicateInfoId);
    }

    @Override
    public int updateByPrimaryKeySelective(RCommunicateTodoJAiEntity record) {
        return rCommunicateTodoJAiMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(RCommunicateTodoJAiEntity record) {
        return rCommunicateTodoJAiMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void createBusinessChange(RCommunicateTodoJAiDto record) {
        log.info("ai分析处理结果：生成商机{}", JSON.toJSONString(record));
        RCommunicateTodoJAiEntity entity = this.save(record);
        entity.setCreateBusinessChange(ErpConstant.T);
        entity.setBusinessNo(record.getBusinessNo());
        entity.setBusinessId(record.getBusinessId());
        this.updateByPrimaryKeySelective(entity);
    }


    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void noCreateBusinessChange(RCommunicateTodoJAiDto record) {
        log.info("ai分析处理结果：无需生成商机{}", JSON.toJSONString(record));
        RCommunicateTodoJAiEntity entity = this.save(record);
        entity.setCreateBusinessChange(ErpConstant.F);
        this.updateByPrimaryKeySelective(entity);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void updateTraderSign(RCommunicateTodoJAiDto record) {
        RCommunicateTodoJAiEntity entity = this.save(record);
        entity.setTraderId(record.getTraderId());
        entity.setUpdateTraderSign(ErpConstant.T);
        this.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void noUpdateTraderSign(RCommunicateTodoJAiDto record) {
        RCommunicateTodoJAiEntity entity = this.save(record);
        entity.setUpdateTraderSign(ErpConstant.F);
        this.updateByPrimaryKeySelective(entity);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void syncContactPosition(RCommunicateTodoJAiDto record) {
        log.info("ai分析处理结果：同步联系人职位{}", JSON.toJSONString(record));
        RCommunicateTodoJAiEntity entity = this.save(record);
        entity.setSyncContactPosition(ErpConstant.T);
        this.updateByPrimaryKeySelective(entity);

        // 获取ai解析的摘要下的联系人职位信息 ，一条录音仅有一种场景
        List<VoiceFieldResultEntity> byCommunicateRecordId = traderVoiceFieldResultMapper.selectByGroupCodeAndFieldCodeAndCommunicateRecordId(
                "GROUP_SUMMARY",
                "contactPosition",
                entity.getCommunicateRecordId());
        if (CollUtil.isNotEmpty(byCommunicateRecordId)) {
            TraderContactEntity traderContactEntity = traderContactMapper.selectByTraderContactId(entity.getTraderId());
            String fieldResult = CollUtil.getFirst(byCommunicateRecordId).getFieldResult();
            traderContactEntity.setPosition(fieldResult);
            traderContactMapper.updateByPrimaryKeySelective(traderContactEntity);

            entity.setCustomerContactId(traderContactEntity.getTraderContactId());
            entity.setCustomerContactPosition(traderContactEntity.getPosition());
            this.updateByPrimaryKeySelective(entity);
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void addCommunicateRecord(RCommunicateTodoJAiDto rCommunicateTodoJAiDto) {

        // 将选中的摘要，更新处理方式
        rCommunicateTodoJAiDto.getCheckDigestId().forEach(id -> {
            VoiceFieldResultEntity voiceFieldResultEntity = traderVoiceFieldResultMapper.selectByPrimaryKey(id);
            voiceFieldResultEntity.setDoFlag("Y");
            traderVoiceFieldResultMapper.updateByPrimaryKeySelective(voiceFieldResultEntity);
        });


        CommunicateRecordEntity communicateRecordEntity = communicateRecordMapper.findByCommunicateRecordId(rCommunicateTodoJAiDto.getCommunicateRecordId());
        String join = String.join("\n", rCommunicateTodoJAiDto.getCheckDigest());
        communicateRecordEntity.setContentSuffix(Convert.toStr(communicateRecordEntity.getContentSuffix(), "") + "\n" + join);

        communicateRecordMapper.updateByPrimaryKeySelective(communicateRecordEntity);
    }


    private RCommunicateTodoJAiEntity save(RCommunicateTodoJAiDto record) {
        RCommunicateTodoJAiEntity rCommunicateTodoJAiEntity = rCommunicateTodoJAiMapper.findByCommunicateRecordId(record.getCommunicateRecordId());
        if (rCommunicateTodoJAiEntity == null) {
            rCommunicateTodoJAiEntity = rCommunicateTodoJAiConvertor.toEntity(record);
            this.insertSelective(rCommunicateTodoJAiEntity);
        }
        return rCommunicateTodoJAiEntity;
    }

}
