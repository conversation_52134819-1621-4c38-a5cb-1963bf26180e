package com.wms.inventorytransfer.model.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 库存转移单请求对象
 *
 * <AUTHOR>
 * @date 2020/7/20 11:09:20
 */
public class InventoryTransferRequest implements Serializable {

    /**
     * 活动ID
     */
    private Integer actionId;

    /**
     * 转移单详情
     */
    private List<InventoryTransferDetailDto> inventoryTransferDetailDtos;

    public InventoryTransferRequest() {
    }

    public InventoryTransferRequest(Integer actionId, List<InventoryTransferDetailDto> inventoryTransferDetailDtos) {
        this.actionId = actionId;
        this.inventoryTransferDetailDtos = inventoryTransferDetailDtos;
    }

    public Integer getActionId() {
        return actionId;
    }

    public void setActionId(Integer actionId) {
        this.actionId = actionId;
    }

    public List<InventoryTransferDetailDto> getInventoryTransferDetailDtos() {
        return inventoryTransferDetailDtos;
    }

    public void setInventoryTransferDetailDtos(List<InventoryTransferDetailDto> inventoryTransferDetailDtos) {
        this.inventoryTransferDetailDtos = inventoryTransferDetailDtos;
    }
}
