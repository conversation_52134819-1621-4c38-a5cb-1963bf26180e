
void function () {
    new Vue({
        el: '#page-container',
        data: {
            pageLoading: true, // 页面加载状态
            businessChanceId: '', // 商机id
            isEdit: false, // true:编辑状态  false:新建
            fixedTop: layout_hidden_value ? 0 : 50, //表头悬浮高度，根据有没有头部判断
            asideList: [
                { name: '跟进记录', icon: 'msg', id: 1 },
                { name: '任务记录', icon: 'record', id: 3 },
                { name: '操作记录', icon: 'time', id: 2 },
                { name: '协作人', icon: 'user', id: 4 },
            ],
            asideIndex: 1,

            detail: {},
            // 报价信息
            quoteorderId: '', // 报价单id， 有id调接口展示报价信息模块
            quoteorderInfo: {},

            // 关联单据
            relatedOrderDtoList: [],
            relatedOrderHeaders: [
                { key: "type", label: "单据类型", width: "130px" },
                { key: "relatedNo", label: "单据号", width: "380px" },
                { key: "belongUserName", label: "归属销售", width: "430px", avatar: 'belongUserPic' },
                { key: "addTimeDate", label: "创建时间", width: "300px" },
            ],

            // 转订单
            isShowToOrder: false,
            canSubmit: true,
            ToOrderTypes: [
                { label: '新建订单', tip: '（将当前商机转成新的订单）', value: '1' },
                { label: '关联订单', tip: '（将当前商机与已有订单进行关联）', value: '2' }
            ],
            orderType: '',
            saleOrderNo: '', // 关联订单号
            saleOrderNoError: '',

            // 关闭商机
            isShowCloseDialog: false,
            CloseReasonTypes: [], // 字典
            closeReasonType: '', // 关闭类型
            closeRemark: '', // 关闭原因

            taskItemId: '', // 任务id （默认打开处理/查看）
            cardLineNum: '', //一行展示的数量
            defaultAiData: {},
            taskNum: ''
        },
        filters: {
            // 非空-排除0
            checkValNull (val) {
                if (val == 0 || val) {
                    return val;
                } else {
                    return '-';
                }
            },
        },
        computed: {
            area () {
                let arr = [];
                this.detail.province && arr.push(this.detail.province);
                this.detail.city && arr.push(this.detail.city);
                this.detail.county && arr.push(this.detail.county);
                return arr.join(' / ');
            },
            // 商机节点状态
            stepClass () {
                return (timeKey) => {
                    let name = '';
                    if (this.detail[timeKey]) {
                        if (this.detail.stage == 6) {
                            name = 'close';
                        } else if (this.detail.stage == 5) {
                            name = 'success';
                        } else {
                            name = 'active';
                        }
                    } else {
                        name = '';
                    }
                    return name;
                }
            },
            // 商机标签
            tagsShow () {
                let arr = [];
                if (this.detail.tags && this.detail.tags.length) {
                    this.detail.tags.forEach(item => {
                        if (item.name) {
                            arr.push(item.name);
                        }
                    })
                }
                return arr.join(' / ');
            },
            // 已赢单/已关闭
            isOver () {
                return this.detail.stage == 5 || this.detail.stage == 6
            }
        },
        created () {
            this.businessChanceId = document.getElementById('businesschance-id').value || '';
            this.taskItemId = document.getElementById('task-id').value || '';
            this.taskItemId && (this.initTaskDetail());

            GLOBAL.showGlobalLoading();
            this.initData();
        },
        mounted() {
            this.$nextTick(() => {
                let isdelete = GLOBAL.getQuery('isdelete') == 1;

                if(isdelete) {
                    this.showCloseLeadsDialog();
                    window.history.replaceState(null, null, '/crm/businessChance/profile/detail?id=' + this.businessChanceId);
                }
            })

            if(window.parent) {
                try {
                    $(window.parent.document).find('.active').eq(0).find('span').attr("title","查看商机").text("查看商机");
                } catch (error) {
                }
            }

            this.getAiData();
            this.getTaskNum();
        },
        methods: {
            async initData () {
                // 详情
                let { data } = await this.$axios.get(`/crm/presales/profile/detail?dataId=${this.businessChanceId}&type=2`)
                if (data.success) {
                    this.detail = data.data || {};
                    this.relatedOrderDtoList = this.detail.relatedOrderDtoList || []; // 关联单据
                    console.log('关联单据relatedOrderDtoList:', this.relatedOrderDtoList);
                    this.quoteorderId = this.detail.quoteorderDto && this.detail.quoteorderDto.quoteorderId || ''; // 报价单id
                    if (this.quoteorderId) {
                        this.getQuoteorder();
                    }
                } else {
                    this.$message.error(data.message);
                }

                if (!layout_hidden_value) {
                    document.title = (this.detail.traderName || '') + '商机查看';
                }
                this.pageLoading = false;
                GLOBAL.hideGlobalLoading();
            },
            getAiData() {
                this.$axios.post('/crm/category/getByBusiness?businessId=' + this.businessChanceId + '&businessType=1').then(({ data }) => {
                    if(data.success) {
                        if(data.data.matchedCategories && data.data.matchedCategories.length) {
                            // this.aiValue.keywords = data.data.keywords ? data.data.keywords.split(',') : [];
                            // this.aiValue.categoryIds = data.data.matchedCategories.split(',');

                            this.defaultAiData = {
                                keywords: data.data.keywords || [],
                                list: data.data.matchedCategories,
                                word: this.productCommentsSale
                            }
                        }
                    }
                })
            },
            initTaskDetail() {
                this.$axios.post('/crm/task/profile/detail', {
                    taskItemId: this.taskItemId
                }).then(({data})=> {
                    if (data.success) {
                        let taskDetail = data.data || {};
                        if (taskDetail.canHandle) {
                            this.$refs.renwuHandle.open(taskDetail);
                        } else {
                            this.$refs.renwuReview.open(taskDetail);
                        }
                    }
                })
            },
            getTaskNum() {
                this.$axios.post('/crm/task/profile/getCurrentUserTodoCount', {
                    bizList: [{
                        bizType: 1,
                        bizId: this.businessChanceId
                    }, {
                        bizType: 3,
                        bizId: this.businessChanceId
                    }],
                    listType: 1
                }).then(({data}) => {
                    if(data.success) {
                        let taskNum = data.data || 0;
                        this.taskNum = taskNum > 99 ? '99+' : taskNum;
                    }
                })
            },
            // 报价信息
            getQuoteorder () {
                this.$axios.post(`/crm/quote/profile/summaryInfo?quoteorderId=${this.quoteorderId}`).then(({data}) => {
                    console.log('报价信息 ===>', data.data);
                    if (data.success) {
                        this.quoteorderInfo = data.data || {};
                    } else {
                        this.$message.error(data.message);
                    }
                })
            },
            /* 页头按钮操作 */
            // 编辑商机 权限C0202
            handlerEdit () {
                if (GLOBAL.auth('C0202')) {
                    window.location.href = `/crm/businessChance/profile/edit?id=${this.businessChanceId}&type=2`;
                } else {
                    GLOBAL.showNoAuth();
                }
            },
            // 转订单 权限C0208
            handlerToOrder (confirm) {
                if (!GLOBAL.auth('C0208')) {
                    GLOBAL.showNoAuth();
                    return;
                }

                let _this = this;
                let confirmParam = confirm === 'Y' ? '&confirm=Y' : '';
                
                GLOBAL.checkNetwork(() => {
                    _this.$axios.get(`/crm/businessChance/profile/toOrderVerify?businessChanceId=${ _this.businessChanceId }${confirmParam}&systemCode=${GLOBAL.checkCrmType().type}`)
                        .then(({ data }) => {
                            if (data.code === 0) {
                                _this.orderType = '';
                                _this.saleOrderNo = '';
                                _this.isShowToOrder = true;

                                _this.ToOrderTypes = [
                                    { label: '新建订单', tip: `（将商机转成<span class="red">${GLOBAL.checkCrmType().label}</span>销售订单）`, value: '1' },
                                    { label: '关联订单', tip: `（将商机与<span class="red">${GLOBAL.checkCrmType().label}</span>销售订单关联）`, value: '2' }
                                ];
    
                                _this.$form.rules({
                                    orderType: {
                                        required: '请选择转订单的方式'
                                    },
                                }, 'ToOrderValid', _this);
                            } else if (data.code === 1) {
                                _this.$popup.warn({
                                    message: '报价单中存在重复产品，转订单后将进行数量合并，并取最低价格和货期，是否确认？',
                                    buttons: [{
                                        txt: '确认',
                                        btnClass: 'confirm',
                                        callback() {
                                            _this.handlerToOrder('Y');
                                        }
                                    }]
                                })
                            } else if (data.code === 500001){
                                _this.$popup.warn({
                                    message: data.message,
                                    buttons: [{
                                        txt: '添加报价',
                                        btnClass: 'confirm',
                                        callback() {
                                            _this.handlerQuoteorder();
                                        }
                                    }, {
                                        txt: '取消',
                                    }]
                                })
                            } else {
                                _this.$popup.warn({
                                    message: data.message,
                                    buttons: [{
                                        txt: '我知道了',
                                        btnClass: 'confirm',
                                    }]
                                })
                            }
                        })
                })

            },
            // 订单号失焦校验
            chackOrderNo () {
                if (this.orderType == '2' && !this.saleOrderNo.trim()) {
                    this.saleOrderNoError = "请输入关联的ERP销售单号";
                } else {
                    this.saleOrderNoError = "";
                }
            },
            // 验证转商机表单
            validToOrderForm () {
                let error = 0;

                if (!this.$form.validForm('ToOrderValid')) {
                    error++;
                }

                if (this.orderType == '2' && !this.saleOrderNo.trim()) {
                    this.saleOrderNoError = "请输入关联的ERP销售单号";
                    error++;
                }

                if (error) {
                    return false;
                }
                return true;
            },
            validSubmit() {
                if (!this.validToOrderForm()) return;
                if (!this.canSubmit) return;

                const _this = this;
                this.$popup.warn({
                    message: '关联/新建订单之后将无法修改，您是否确定继续？',
                    buttons: [{
                        txt: '确认',
                        btnClass: 'confirm',
                        callback() {
                            _this.submitToOrder();
                        }
                    }]
                })
            },
            submitToOrder () {
                this.canSubmit = false;
                GLOBAL.showGlobalLoading();
                // 新建订单
                if (this.orderType == 1) {
                    this.$axios.get(`/crm/businessChance/profile/toOrder?businessChanceId=${ this.businessChanceId }&systemCode=${GLOBAL.checkCrmType().type}`)
                        .then(({data})=> {
                            GLOBAL.hideGlobalLoading();
                            if (data.success) {
                                this.isShowToOrder = false;
                                this.$message.success('转订单成功');
                                setTimeout(()=> {
                                    GLOBAL.link({name:'商机转订单', url: data.data.jumpErpInnerUrl, link: data.data.jumpErpUrl, nohost: true});
                                }, 2000);
                            } else {
                                this.canSubmit = true;
                                this.$message.error(data.message);
                            }
                        })
                }
                // 关联订单
                else {
                    this.$axios.post(`/crm/businessChance/profile/linkOrder`, {
                        businessChanceId: this.businessChanceId,
                        saleOrderNo: this.saleOrderNo.trim(),
                        systemCode: GLOBAL.checkCrmType().type
                    }).then(({data})=> {
                        GLOBAL.hideGlobalLoading();
                        if (data.success) {
                            this.isShowToOrder = false;
                            this.$message.success('转订单成功');
                            setTimeout(()=> {
                                window.location.reload();
                            }, 2000);
                        } else {
                            this.canSubmit = true;
                            this.$message.error(data.message);
                        }
                    })
                }
            },
            // 新建报价/编辑（有报价单）
            handlerQuoteorder () {
                if (this.quoteorderId) {
                    this.editQuoteorder();
                } else {
                    this.createQuoteorder();
                }
            },
            // 关注
            handlerAttention () {
                // 权限 关注C0203 取消关注C0204
                if (this.detail.attentionState && GLOBAL.auth('C0204')) { // 取消关注
                    this.$axios.post(`/crm/businessChance/profile/cancelAttention?id=${ this.businessChanceId }`).then(({data})=> {
                        if (data.success) {
                            this.detail.attentionState = 0;
                            this.$message.success('已取消关注');
                        } else {
                            this.$message.error(data.message);
                        }
                    })
                } else if (!this.detail.attentionState && GLOBAL.auth('C0203')) { // 关注
                    this.$axios.post(`/crm/businessChance/profile/attention`, [Number(this.businessChanceId)]).then(({data})=> {
                        if (data.success) {
                            this.detail.attentionState = 1;
                            this.$message.success('已关注');
                        } else {
                            this.$message.error(data.message);
                        }
                    })
                } else {
                    GLOBAL.showNoAuth();
                }
            },
            /* 关闭商机 ↓↓↓ */
            showCloseLeadsDialog() {
                if (!GLOBAL.auth('C0109')) {
                    GLOBAL.showNoAuth();
                    return;
                }

                this.closeReasonType = '';
                this.closeRemark = '';

                this.$axios.get('/crm/sysOption/public/getByParentCode?parentCode=BUSINESS_CLOSE_REASON').then(({data})=> {
                    if (data.success) {
                        let resArr = data.data || [];
                        this.CloseReasonTypes = resArr.map((item => {
                            return {
                                label: item.title,
                                value: item.sysOptionDefinitionId
                            }
                        }))
                        this.isShowCloseDialog = true;

                        this.$form.rules({
                            closeReasonType: {
                                required: '请选择关闭原因'
                            },
                        }, 'CloseLeads', this);
                    }
                })
            },
            closeTheLeads () {
                if (this.$form.validForm('CloseLeads')) {
                    GLOBAL.showGlobalLoading();

                    let comments = '';
                    this.CloseReasonTypes.forEach((item)=> {
                        if (item.value == this.closeReasonType) {
                            comments = item.label
                        }
                    })

                    this.$axios.post('/crm/businessChance/profile/close', {
                        businessChanceId: this.businessChanceId,
                        statusComments: this.closeReasonType,
                        closeComments: this.closeRemark,
                        comments
                    }).then(({ data }) => {
                        this.isShowCloseDialog = false;
                        GLOBAL.hideGlobalLoading();
                        if (data.success) {
                            this.$message.success('关闭成功');
                            setTimeout(()=> {
                                window.location.reload();
                            }, 2000)
                        } else {
                            this.$message.warn(data.message || '网络异常');
                        }
                    })
                }
            },




            // 跟进记录-操作记录-协作人列表--弹层
            openDialog (id) {
                switch(id) {
                    case 1: 
                        this.$refs.followRecordListDialog.open({
                            relatedId: this.businessChanceId, // 业务id
                            traderId: this.detail.traderId,   // 已建档客户id
                            traderName: this.detail.traderName, // 客户名称
                            traderNameLink: this.detail.traderNameLink,
                            traderNameInnerLink: this.detail.traderNameInnerLink,
                            tycFlag: this.detail.tycFlag,  // 是否天眼查

                            belongerId: this.detail.belongerId, // 归属销售id
                            belonger: this.detail.belonger, // 归属销售
                            belongerPic: this.detail.belongPic,  // 归属销售头像
                            contact: this.detail.contact, // 联系人
                            traderContactId: this.detail.traderContactId, // 联系人id
                            phone: this.detail.phone, // 手机
                            telePhone: this.detail.telephone, // 固话
                            communicateType: 244
                        });
                        break;
                    case 2:
                        this.$refs.operationLogDialog.open({
                            bizTypeEnum: '02', // 业务类型 01线索池 02商机库 03 报价单
                            relatedId: this.businessChanceId, // 业务id
                            quoteorderId: this.quoteorderId, // 报价单id
                        });
                        break;
                    case 3:
                        this.$refs.renwuDialog.open({
                            bizType: '1', //  1:商机 2:线索 3:报价
                            relatedId: this.businessChanceId, // 业务id
                            quoteorderId: this.quoteorderId, // 报价单id
                            disabled: this.detail.stage === 6
                        });
                        break;
                    case 4:
                        this.$refs.partnerListDialog.open({
                            relatedId: this.businessChanceId, // 业务id
                            businessType: 1, // 1.商机 2.报价 3.订单 4.售后 5.线索
                            belongerId: this.detail.belongerId, // 归属销售id
                            businessNo: this.detail.bussinessChanceNo, // 业务编号
                        });
                        break;
                }
            },
            /* dialog内更新数据，panel同步更新 */
            refreshFollowPanel () {
                if (this.$refs.followRecordList) {
                    this.$refs.followRecordList.initData();
                }
            },
            refreshRenwuPanel () {
                if (this.$refs.renwuList) {
                    this.$refs.renwuList.initData();
                } else {
                    this.getTaskNum();
                }
            },
            // 协同人panel更新
            refreshPartnerList () {
                if (this.$refs.partnerList) {
                    this.$refs.partnerList.initData();
                }
                if (this.$refs.operationLog) {
                    this.$refs.operationLog.initData();
                }
            },


            // 天眼查详情
            openTyc () {
                this.$refs.tycDetail.open(this.detail.traderName);
            },


            // 添加报价 权限C0225
            createQuoteorder () {
                if (!GLOBAL.auth('C0225')) {
                    GLOBAL.showNoAuth();
                    return;
                }
                this.$axios.get(`/crm/businessChance/profile/addQuote?businessChanceId=${ this.businessChanceId }`).then(({data})=> {
                    if (data.success) {
                        GLOBAL.link({ name: '报价单', url: `/crm/quoter/profile/index?id=${ data.data }`})
                        // window.open(`/crm/quoter/profile/index?id=${ data.data }`);
                    } else {
                        this.$message.warn(data.message || '网络异常');
                    }
                })
            },
            // 编辑报价 权限C0224
            editQuoteorder () {
                if (GLOBAL.auth('C0224')) {
                    GLOBAL.link({ name: '报价单', url: `/crm/quoter/profile/index?id=${ this.quoteorderId }`})
                } else {
                    GLOBAL.showNoAuth();
                }
            },
            // 导出报价 权限C0222
            exportQuoteorder () {
                if (GLOBAL.auth('C0222')) {
                    window.open('/crm/quote/profile/export?quoteorderId=' + this.quoteorderId);
                } else {
                    GLOBAL.showNoAuth();
                }
            },
            // 申请授权书 权限C0214
            applyQuoteorder () {
                if (GLOBAL.auth('C0214')) {
                    GLOBAL.showGlobalLoading();
                    this.$axios.post('/crm/quote/profile/checkQuoteApply?quoteorderId=' + this.quoteorderId).then(({ data }) => {
                        GLOBAL.hideGlobalLoading();
                        if (data.success) {
                            let totalNum = data.data.num;
                            let applyNum = data.data.appliedNum;;

                            if (applyNum == 0) {
                                GLOBAL.link({ name: '申请授权书', url: data.data.submitInnerLink, link: data.data.submitLink, nohost: true, crmType: 1 })
                            } else if (totalNum > applyNum) {
                                this.$popup.warn({
                                    message: `您已提交了${applyNum}个授权申请，是否继续申请？`,
                                    buttons: [{
                                        txt: '查看授权',
                                        btnClass: 'confirm',
                                        callback() {
                                            GLOBAL.link({ name: '查看授权书', url: data.data.checkInnerLink, link: data.data.checkLink, nohost: true, crmType: 1 })
                                        },
                                    }, {
                                        txt: '继续申请',
                                        callback() {
                                            GLOBAL.link({ name: '申请授权书', url: data.data.submitInnerLink, link: data.data.submitLink, nohost: true, crmType: 1 })
                                        }
                                    }]
                                });
                            } else if (totalNum == applyNum) {
                                this.$popup.warn({
                                    message: `您已提交了${applyNum}个授权申请`,
                                    buttons: [{
                                        txt: '查看授权',
                                        btnClass: 'confirm',
                                        callback() {
                                            GLOBAL.link({name:'查看授权书', url: data.data.checkInnerLink, link: data.data.checkLink, nohost: true, crmType: 1})
                                        },
                                    }]
                                });
                            }
                        } else {
                            const quoteApplyLink = this.quoteorderInfo.quoteApplyDto && this.quoteorderInfo.quoteApplyDto.quoteApplyLink || '';
                            const quoteApplyInnerLink = this.quoteorderInfo.quoteApplyDto && this.quoteorderInfo.quoteApplyDto.quoteApplyInnerLink || '';
                            GLOBAL.link({ name: '申请授权书', url: quoteApplyInnerLink, link: quoteApplyLink, nohost: true, crmType: 1 })
                        }
                    })
                } else {
                    GLOBAL.showNoAuth();
                }
            },
            // 查看报价
            reviewQuoteorder () {
                GLOBAL.link({ name: '报价单', url: `/crm/quoter/profile/index?id=${ this.quoteorderId }`})
                // window.open(`/crm/quoter/profile/index?id=${ this.quoteorderId }`);
            },
            // 拨打电话
            callNumber_ (num) {
                GLOBAL.callNumber({
                    phone: num || '',
                    traderId: this.detail.traderId || 0,
                    traderType: 1,
                    callType: 1, //  7线索1商机2销售订单3报价4售后5采购订单//没有就传 0
                    orderId: this.businessChanceId || 0,
                    traderContactId: this.detail.traderContactId || 0
                })
            },
            handlerRecordAdd (data) {
                console.log(data)
                if(data.noneNextDate === 1) {
                    let _this = this;

                    this.$popup.warn({
                        message: '无下次跟进时间，是否需要关闭该条商机？',
                        buttons: [{
                            txt: '关闭商机',
                            btnClass: 'delete',
                            callback() {
                               _this.showCloseLeadsDialog();
                            }
                        },
                        {
                            txt: '无需关闭',
                        }]
                    })
                }
            },
            addVisitPlan() {
                if (GLOBAL.auth('C0401')) {
                    GLOBAL.link({ name: '新建拜访计划', url: '/crm/visitRecord/profile/add?bizid=' + this.businessChanceId + '&type=2', multi: true })
                } else {
                    GLOBAL.showNoAuth();
                }
            }
        }
    })
}.call(this);