$(function(){
	$("#sub").click(function(){
		checkLogin();
		var flag1 = false;
		var searchNo = $("#searchNo").val()==undefined?"":$("#searchNo").val();
		if(searchNo==""){
			warnErrorTips("searchNo","searchNoError","采购单号不允许为空");//文本框ID和提示用语
			return false;
		}

		/*赠品参考价校验*/
		var priceReg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
		var flagIsgift1 = true;
		var flagIsgift2 = true;
		var flagIsgift3 = true;
		$.each($("input[name='xreferPrice']"), function (i, n) {
			var price = $(this).val();
			var goodsId = $(this).attr("alt3");
			var isGift = $(this).attr("altIsGift");
			if (price === '' && isGift == 1) {
				$("input[alt3='" + goodsId + "']").addClass("errorbor");
				flagIsgift1 = false;
				return false;
			}
			if (price == 0.00 && isGift == 1){
				$("input[alt3='" + goodsId + "']").addClass("errorbor");
				flagIsgift2 = false;
				return false;
			}
			if (price == 0 && isGift == 1) {
				$("input[alt3='" + goodsId + "']").addClass("errorbor");
				flagIsgift2 = false;
				return false;
			}
			var a = /(\d{11})\d*/;
			var b =/(\.\d{3})\d*/;
			if (!(price != '' && (priceReg.test(price) && !a.test(price) && !b.test(price)))){
				$("input[alt3='" + goodsId + "']").addClass("errorbor");
				flagIsgift3 = false;
				return false;
			}

			if (!priceReg.test(price)) {
				$("input[alt3='" + goodsId + "']").addClass("errorbor");
				flagIsgift3 = false;
				return false;
			}

		});
		if(!flagIsgift1){
			layer.alert("请填写赠品参考价");
			return false;
		}
		if(!flagIsgift2){
			layer.alert("赠品参考价不可为0");
			return false;
		}
		if(!flagIsgift3){
			layer.alert("赠品参考价输入错误！仅允许使用数字，最多精确到小数点后两位");
			return false;
		}

		/*【采购预计发货日】和【采购预计收货日】校验*/
		let sendGoodsTimeFlag = true;
		let receiveGoodsTimeFlag = true;
		let dateCompareFlag = true;
		$.each($("input[name='xSendGoodsTime']"),function(i,n){
			let sendTimeStr = $(this).val();
			if(sendTimeStr ==''||sendTimeStr == undefined){
				$(this).addClass("errorbor");
				sendGoodsTimeFlag = false;
				return false;
			}
			let receviceDateStr = $(this).parent().next().children().val();
			if(receviceDateStr != '' && receviceDateStr < sendTimeStr){
				$(this).parent().next().children().addClass("errorbor");
				dateCompareFlag = false;
				return false;
			}
		});
		/*$.each($("input[name='xReceiveGoodsTime']"),function(i,n){
			let sendTimeStr = $(this).val();
			if(sendTimeStr ==''||sendTimeStr == undefined){
				$(this).addClass("errorbor");
				receiveGoodsTimeFlag = false;
				return false;
			}
		});*/
		if(!sendGoodsTimeFlag){
			layer.alert("请填写采购预计发货日");
			return false;
		}
		/*if(!receiveGoodsTimeFlag){
			layer.alert("请填写采购预计到货日");
			return false;
		}*/
		if(!dateCompareFlag){
			layer.alert("收货时间应该大于或等于发货时间");
			return false;
		}
		$.ajax({
			type: "POST",
			url: page_url+"/order/buyorder/getBuyorderByBuyorderNo.do",
			data: {'buyorderNo':searchNo},
			dataType:'json',
			async: false,
			success: function(data){
				
				if(data.code==0){
					$("#validStatus").val(data.data.validStatus);
					$("#status").val(data.data.status);
					$("#lockedStatus").val(data.data.lockedStatus);
					$("#verifyStatus").val(data.data.verifyStatus);
					$("#deliveryDirect").val(data.data.deliveryDirect);
					$("#saleorderNoType").val(data.data.saleorderNo);
					$("#newSaleorderNoType").val(data.data.saleorderType);
					$("#buyorderId").val(data.data.buyorderId);
					$("input[name='beforeParams']").val(data.message);
					$("#isNew").val(data.data.isNew);
					$("#buyorderType").val(data.data.orderType);
					$("#orderIsGift").val(data.data.isGift);
				}else{
					flag1= true;
				}
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
		console.log("是否为赠品商品[0]:"+$("#gv0IsGift").val())
		console.log("是否为赠品采购单:"+$("#orderIsGift").val())

		searchNo = searchNo.replace(/^\s*/,"");
		if($("#isHaveVirtureSku").val() != 2 && searchNo.substr(0,4)=='CGFY'){
			warnErrorTips("searchNo","searchNoError","实物商品不允许添加到采购费用订单");
			return false;

		}

		if ($("#gv0IsGift").val() == 1 && $("#orderIsGift").val() != 1){
			warnErrorTips("searchNo","searchNoError","不允许将赠品商品添加到非采购赠品订单中");
			return false;
		}
		if ($("#gv0IsGift").val() != 1 && $("#orderIsGift").val() == 1){
			warnErrorTips("searchNo","searchNoError","不允许将非赠品商品添加到采购赠品订单中");
			return false;
		}

		if(flag1){
			warnErrorTips("searchNo","searchNoError","采购单号不存在，请检验");
			return false;
		}
		if($("#validStatus").val()==1 && $("#status").val()!=2 && $("#status").val()!=3){
			warnErrorTips("searchNo","searchNoError","不允许添加到已生效订单");
			return false;
		}
		if( $("#status").val()==2 ){
			warnErrorTips("searchNo","searchNoError","不允许添加到已完结订单");
			return false;
		}
		if( $("#status").val()==3 ){
			warnErrorTips("searchNo","searchNoError","不允许添加到已关闭订单");
			return false;
		}
		if($("#lockedStatus").val()==1 ){
			warnErrorTips("searchNo","searchNoError","不允许添加到已锁定订单");
			return false;
		}
		if($("#verifyStatus").val()==0 ){
			warnErrorTips("searchNo","searchNoError","不允许添加到审核中订单");
			return false;
		}
		var flag2 = false;
		var flag3 = false;
		var numReg = /^([1]?\d{1,10})$/;
		var sum = Number(0);
		
		$.each($("input[name='saleorderGoodsNum']"),function(i,n){
			var num = $(this).val();
			if(num ==''||num == undefined){
				$(this).val(0);
				flag2 = true;
				return false;
			}
			if(!numReg.test(num)){
				$(this).val(0);
				flag3 = true;
				return false;
			}
			sum += Number(num);
		});
		if(flag2){
			layer.alert("数量不允许为空");
			return false;
		}
		if(flag3){
			layer.alert("数量必须为大于等于1的正整数");
			return false;
		}
		if(sum==Number(0)){
			layer.alert("订单中采购商品数量必须大于等于1");
			return false;
		}
//		if(flag && $("#validStatus").val()==1){
//			layer.alert("订单已生效，禁止添加");
//			flag = false;
//		}
//		if(flag && $("#status").val()==2){
//			layer.alert("订单已完结，禁止添加");
//			flag = false;
//		}
//		if(flag && $("#status").val()==3){
//			layer.alert("订单已关闭，禁止添加");
//			flag = false;
//		}
		var saleorderNoType = $("#saleorderNoType").val();
		var saleorderNos = $("input[name='saleorderNo']").val();

		// 采购单 orderType
		let buyorderType = $("input[name='buyorderType']").val();

		// 销售单 orderType集合
		let saleorderType = [];

		$.each($("input[name='saleorderType']"),function(){
			let orderType = $(this).val();
			saleorderType.push(orderType);
		});

		let uniqeSaleorderType = Array.from(new Set(saleorderType));

		if ((buyorderType == 0 && uniqeSaleorderType.indexOf("2") >= 0) || (buyorderType == 1 && (uniqeSaleorderType.length > 1 || uniqeSaleorderType[0] !== '2'))) {
			warnErrorTips("searchNo","searchNoError","不允许将备货单、销售订单的商品添加到同一采购单");
			return false;
		}
		var searchdeliveryDirect = $("#deliveryDirect").val();
		var deliveryDirects = $("input[name='deliveryDirect']").val();
		
		if( (searchdeliveryDirect==0 && deliveryDirects.indexOf("1")>=0) || (searchdeliveryDirect==1 && deliveryDirects.indexOf("0")>=0)){
			warnErrorTips("searchNo","searchNoError","不允许将直发与普发的商品添加到同一采购单");
			return false;
		}
		
		if(searchdeliveryDirect==1 && deliveryDirects.indexOf("1")>=0 && saleorderNos !=saleorderNoType){
			warnErrorTips("searchNo","searchNoError","不允许将非同一销售订单的直发商品添加到同一采购单");
			return false;
		}

		<!--VDERP-12903 虚拟商品不允许添加到采购订单中-->
		if ($("#isHaveVirtureSku").val() == 2){
			warnErrorTips("searchNo","searchNoError","虚拟商品不允许添加到采购订单中");
			return false;
		}


		
		$("#myform").attr("action", page_url + "/order/buyorder/saveAddHavedBuyorder.do");
		$("#myform").submit();
		
	})
	
})

function expensePrice2(obj,sku,goodId) {
	var inputPrice = $(obj).val();
	var goodsId = $(obj).attr("alt3");
	var isGift = $(obj).attr("altIsGift");
	var priceReg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
	if (inputPrice === '') {
		$("input[alt3='" + goodsId + "']").addClass("errorbor");
		layer.alert("请填写赠品参考价");
		return false;
	}

	if (inputPrice == 0 && isGift == 1) {
		$("input[alt3='" + goodsId + "']").addClass("errorbor");
		layer.alert("赠品参考价不可为0");
		return false;
	}

	var a = /(\d{11})\d*/;
	var b =/(\.\d{3})\d*/;
	if (!(inputPrice != '' && (priceReg.test(inputPrice) && !a.test(inputPrice) && !b.test(inputPrice)))){
		$("input[alt3='" + goodsId + "']").addClass("errorbor");
		layer.alert("赠品参考价输入错误！仅允许使用数字，最多精确到小数点后两位");
		return false;
	}

	// if (!priceReg.test(inputPrice)) {
	//     $(obj).val(0.00);
	//     return false;
	// }
	$("#priceValue"+goodId).val(sku+'|'+goodId+'|'+$(obj).val());

	$(obj).siblings().val(buyorderGoodsId + "|" + inputPrice);
}

function changeSendGoodsTimeStr0(obj,sku,goodId){
	$("#sendGoodsTimeValue"+goodId).val(sku+'|'+goodId+'|'+$(obj).val());
}

function changeReceiveGoodsTimeStr0(obj,sku,goodId){
	$("#receiveGoodsTimeValue"+goodId).val(sku+'|'+goodId+'|'+$(obj).val());
}


function addNum(obj,num,saleorderGoodsId,sku,saleorderNo){
	checkLogin();
	var srnum = $(obj).val();
	var goodsId = $(obj).attr("alt1");	
	var numReg = /^([1]?\d{1,10})$/;
	if(srnum==''){
		layer.alert("数量不允许为空");
		$(obj).val(1);
		$("span[alt='"+goodsId+"']").html(1);
		var zSum = Number(0);
		$.each($(".buyNum"),function(i,n){
			zSum += Number($(this).html());
		});
		$("#zSum").html(zSum);
		return false;
	}
	if(srnum !='' && !numReg.test(srnum)){
		layer.alert("数量必须为正整数且小于11位数字");
		$("span[alt='"+goodsId+"']").html(1);
		$(obj).val(1);
		var zSum = Number(0);
		$.each($(".buyNum"),function(i,n){
			zSum += Number($(this).html());
		});
		$("#zSum").html(zSum);
		return false;
	}
	if(Number(srnum)<1 || Number(srnum) > Number(num)){
		layer.alert("数量必须大于等于1且小于等于"+num);
		$("span[alt='"+goodsId+"']").html(1);
		$(obj).val(1);
		var zSum = Number(0);
		$.each($(".buyNum"),function(i,n){
			zSum += Number($(this).html());
		});
		$("#zSum").html(zSum);
		return false;
	}
	$(obj).siblings("input").val(sku+"|"+saleorderGoodsId+"|"+srnum+"|"+saleorderNo);
	var sum =Number(0);
	
	$.each($("input[alt1='"+goodsId+"']"),function(i,n){
		sum += Number($(this).val());
	});

	$("span[alt='"+goodsId+"']").html(sum);
	$("span[alt='"+goodsId+"']").siblings("input").val(sku+"|"+goodsId+"|"+sum);
	//计算总件数
	var zSum = Number(0);
	$.each($(".buyNum"),function(i,n){
		zSum += Number($(this).html());
	});
	$("#zSum").html(zSum);
	
}


