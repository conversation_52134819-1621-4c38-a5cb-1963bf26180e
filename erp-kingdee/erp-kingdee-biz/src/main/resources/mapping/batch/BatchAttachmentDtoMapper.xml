<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchAttachmentDtoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchAttachmentDto">
    <!--@mbg.generated-->
    <!--@Table T_ATTACHMENT-->
    <id column="ATTACHMENT_ID" jdbcType="INTEGER" property="attachmentId" />
    <result column="ATTACHMENT_TYPE" jdbcType="INTEGER" property="attachmentType" />
    <result column="ATTACHMENT_FUNCTION" jdbcType="INTEGER" property="attachmentFunction" />
    <result column="RELATED_ID" jdbcType="INTEGER" property="relatedId" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="DOMAIN" jdbcType="VARCHAR" property="domain" />
    <result column="URI" jdbcType="VARCHAR" property="uri" />
    <result column="ALT" jdbcType="VARCHAR" property="alt" />
    <result column="SORT" jdbcType="INTEGER" property="sort" />
    <result column="IS_DEFAULT" jdbcType="INTEGER" property="isDefault" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="OSS_RESOURCE_ID" jdbcType="VARCHAR" property="ossResourceId" />
    <result column="ORIGINAL_FILEPATH" jdbcType="VARCHAR" property="originalFilepath" />
    <result column="SYN_SUCCESS" jdbcType="INTEGER" property="synSuccess" />
    <result column="COST_TIME" jdbcType="BIGINT" property="costTime" />
    <result column="IS_DELETED" jdbcType="INTEGER" property="isDeleted" />
    <result column="SUFFIX" jdbcType="VARCHAR" property="suffix" />
    <result column="FILE_SIZE" jdbcType="DECIMAL" property="fileSize" />
    <result column="FILE_HEIGHT" jdbcType="DECIMAL" property="fileHeight" />
    <result column="FILE_WIDTH" jdbcType="DECIMAL" property="fileWidth" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ATTACHMENT_ID, ATTACHMENT_TYPE, ATTACHMENT_FUNCTION, RELATED_ID, `NAME`, `DOMAIN`, 
    URI, ALT, SORT, IS_DEFAULT, ADD_TIME, CREATOR, OSS_RESOURCE_ID, ORIGINAL_FILEPATH, 
    SYN_SUCCESS, COST_TIME, IS_DELETED, SUFFIX, FILE_SIZE, FILE_HEIGHT, FILE_WIDTH
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_ATTACHMENT
    where ATTACHMENT_ID = #{attachmentId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_ATTACHMENT
    where ATTACHMENT_ID = #{attachmentId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="ATTACHMENT_ID" keyProperty="attachmentId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchAttachmentDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_ATTACHMENT (ATTACHMENT_TYPE, ATTACHMENT_FUNCTION, 
      RELATED_ID, `NAME`, `DOMAIN`, 
      URI, ALT, SORT, IS_DEFAULT, 
      ADD_TIME, CREATOR, OSS_RESOURCE_ID, 
      ORIGINAL_FILEPATH, SYN_SUCCESS, COST_TIME, 
      IS_DELETED, SUFFIX, FILE_SIZE, 
      FILE_HEIGHT, FILE_WIDTH)
    values (#{attachmentType,jdbcType=INTEGER}, #{attachmentFunction,jdbcType=INTEGER}, 
      #{relatedId,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, #{domain,jdbcType=VARCHAR}, 
      #{uri,jdbcType=VARCHAR}, #{alt,jdbcType=VARCHAR}, #{sort,jdbcType=INTEGER}, #{isDefault,jdbcType=INTEGER}, 
      #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, #{ossResourceId,jdbcType=VARCHAR}, 
      #{originalFilepath,jdbcType=VARCHAR}, #{synSuccess,jdbcType=INTEGER}, #{costTime,jdbcType=BIGINT}, 
      #{isDeleted,jdbcType=INTEGER}, #{suffix,jdbcType=VARCHAR}, #{fileSize,jdbcType=DECIMAL}, 
      #{fileHeight,jdbcType=DECIMAL}, #{fileWidth,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" keyColumn="ATTACHMENT_ID" keyProperty="attachmentId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchAttachmentDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_ATTACHMENT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="attachmentType != null">
        ATTACHMENT_TYPE,
      </if>
      <if test="attachmentFunction != null">
        ATTACHMENT_FUNCTION,
      </if>
      <if test="relatedId != null">
        RELATED_ID,
      </if>
      <if test="name != null">
        `NAME`,
      </if>
      <if test="domain != null">
        `DOMAIN`,
      </if>
      <if test="uri != null">
        URI,
      </if>
      <if test="alt != null">
        ALT,
      </if>
      <if test="sort != null">
        SORT,
      </if>
      <if test="isDefault != null">
        IS_DEFAULT,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="ossResourceId != null">
        OSS_RESOURCE_ID,
      </if>
      <if test="originalFilepath != null">
        ORIGINAL_FILEPATH,
      </if>
      <if test="synSuccess != null">
        SYN_SUCCESS,
      </if>
      <if test="costTime != null">
        COST_TIME,
      </if>
      <if test="isDeleted != null">
        IS_DELETED,
      </if>
      <if test="suffix != null">
        SUFFIX,
      </if>
      <if test="fileSize != null">
        FILE_SIZE,
      </if>
      <if test="fileHeight != null">
        FILE_HEIGHT,
      </if>
      <if test="fileWidth != null">
        FILE_WIDTH,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="attachmentType != null">
        #{attachmentType,jdbcType=INTEGER},
      </if>
      <if test="attachmentFunction != null">
        #{attachmentFunction,jdbcType=INTEGER},
      </if>
      <if test="relatedId != null">
        #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="domain != null">
        #{domain,jdbcType=VARCHAR},
      </if>
      <if test="uri != null">
        #{uri,jdbcType=VARCHAR},
      </if>
      <if test="alt != null">
        #{alt,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="isDefault != null">
        #{isDefault,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="ossResourceId != null">
        #{ossResourceId,jdbcType=VARCHAR},
      </if>
      <if test="originalFilepath != null">
        #{originalFilepath,jdbcType=VARCHAR},
      </if>
      <if test="synSuccess != null">
        #{synSuccess,jdbcType=INTEGER},
      </if>
      <if test="costTime != null">
        #{costTime,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="suffix != null">
        #{suffix,jdbcType=VARCHAR},
      </if>
      <if test="fileSize != null">
        #{fileSize,jdbcType=DECIMAL},
      </if>
      <if test="fileHeight != null">
        #{fileHeight,jdbcType=DECIMAL},
      </if>
      <if test="fileWidth != null">
        #{fileWidth,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchAttachmentDto">
    <!--@mbg.generated-->
    update T_ATTACHMENT
    <set>
      <if test="attachmentType != null">
        ATTACHMENT_TYPE = #{attachmentType,jdbcType=INTEGER},
      </if>
      <if test="attachmentFunction != null">
        ATTACHMENT_FUNCTION = #{attachmentFunction,jdbcType=INTEGER},
      </if>
      <if test="relatedId != null">
        RELATED_ID = #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        `NAME` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="domain != null">
        `DOMAIN` = #{domain,jdbcType=VARCHAR},
      </if>
      <if test="uri != null">
        URI = #{uri,jdbcType=VARCHAR},
      </if>
      <if test="alt != null">
        ALT = #{alt,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        SORT = #{sort,jdbcType=INTEGER},
      </if>
      <if test="isDefault != null">
        IS_DEFAULT = #{isDefault,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="ossResourceId != null">
        OSS_RESOURCE_ID = #{ossResourceId,jdbcType=VARCHAR},
      </if>
      <if test="originalFilepath != null">
        ORIGINAL_FILEPATH = #{originalFilepath,jdbcType=VARCHAR},
      </if>
      <if test="synSuccess != null">
        SYN_SUCCESS = #{synSuccess,jdbcType=INTEGER},
      </if>
      <if test="costTime != null">
        COST_TIME = #{costTime,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        IS_DELETED = #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="suffix != null">
        SUFFIX = #{suffix,jdbcType=VARCHAR},
      </if>
      <if test="fileSize != null">
        FILE_SIZE = #{fileSize,jdbcType=DECIMAL},
      </if>
      <if test="fileHeight != null">
        FILE_HEIGHT = #{fileHeight,jdbcType=DECIMAL},
      </if>
      <if test="fileWidth != null">
        FILE_WIDTH = #{fileWidth,jdbcType=DECIMAL},
      </if>
    </set>
    where ATTACHMENT_ID = #{attachmentId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchAttachmentDto">
    <!--@mbg.generated-->
    update T_ATTACHMENT
    set ATTACHMENT_TYPE = #{attachmentType,jdbcType=INTEGER},
      ATTACHMENT_FUNCTION = #{attachmentFunction,jdbcType=INTEGER},
      RELATED_ID = #{relatedId,jdbcType=INTEGER},
      `NAME` = #{name,jdbcType=VARCHAR},
      `DOMAIN` = #{domain,jdbcType=VARCHAR},
      URI = #{uri,jdbcType=VARCHAR},
      ALT = #{alt,jdbcType=VARCHAR},
      SORT = #{sort,jdbcType=INTEGER},
      IS_DEFAULT = #{isDefault,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      OSS_RESOURCE_ID = #{ossResourceId,jdbcType=VARCHAR},
      ORIGINAL_FILEPATH = #{originalFilepath,jdbcType=VARCHAR},
      SYN_SUCCESS = #{synSuccess,jdbcType=INTEGER},
      COST_TIME = #{costTime,jdbcType=BIGINT},
      IS_DELETED = #{isDeleted,jdbcType=INTEGER},
      SUFFIX = #{suffix,jdbcType=VARCHAR},
      FILE_SIZE = #{fileSize,jdbcType=DECIMAL},
      FILE_HEIGHT = #{fileHeight,jdbcType=DECIMAL},
      FILE_WIDTH = #{fileWidth,jdbcType=DECIMAL}
    where ATTACHMENT_ID = #{attachmentId,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2022-11-28-->
  <select id="findByAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_ATTACHMENT
    <where>
        and RELATED_ID=#{relatedId,jdbcType=INTEGER}
      <if test="attachmentId != null">
        and ATTACHMENT_ID=#{attachmentId,jdbcType=INTEGER}
      </if>
      <if test="attachmentType != null">
        and ATTACHMENT_TYPE=#{attachmentType,jdbcType=INTEGER}
      </if>
      <if test="attachmentFunction != null">
        and ATTACHMENT_FUNCTION=#{attachmentFunction,jdbcType=INTEGER}
      </if>
      <if test="name != null">
        and `NAME`=#{name,jdbcType=VARCHAR}
      </if>
      <if test="domain != null">
        and `DOMAIN`=#{domain,jdbcType=VARCHAR}
      </if>
      <if test="uri != null">
        and URI=#{uri,jdbcType=VARCHAR}
      </if>
      <if test="alt != null">
        and ALT=#{alt,jdbcType=VARCHAR}
      </if>
      <if test="sort != null">
        and SORT=#{sort,jdbcType=INTEGER}
      </if>
      <if test="isDefault != null">
        and IS_DEFAULT=#{isDefault,jdbcType=INTEGER}
      </if>
      <if test="addTime != null">
        and ADD_TIME=#{addTime,jdbcType=BIGINT}
      </if>
      <if test="creator != null">
        and CREATOR=#{creator,jdbcType=INTEGER}
      </if>
      <if test="ossResourceId != null">
        and OSS_RESOURCE_ID=#{ossResourceId,jdbcType=VARCHAR}
      </if>
      <if test="originalFilepath != null">
        and ORIGINAL_FILEPATH=#{originalFilepath,jdbcType=VARCHAR}
      </if>
      <if test="synSuccess != null">
        and SYN_SUCCESS=#{synSuccess,jdbcType=INTEGER}
      </if>
      <if test="costTime != null">
        and COST_TIME=#{costTime,jdbcType=BIGINT}
      </if>
      <if test="isDeleted != null">
        and IS_DELETED=#{isDeleted,jdbcType=INTEGER}
      </if>
      <if test="suffix != null">
        and SUFFIX=#{suffix,jdbcType=VARCHAR}
      </if>
      <if test="fileSize != null">
        and FILE_SIZE=#{fileSize,jdbcType=DECIMAL}
      </if>
      <if test="fileHeight != null">
        and FILE_HEIGHT=#{fileHeight,jdbcType=DECIMAL}
      </if>
      <if test="fileWidth != null">
        and FILE_WIDTH=#{fileWidth,jdbcType=DECIMAL}
      </if>
    </where>
  </select>

  <select id="purchaseInfindByQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_ATTACHMENT
    <where>
      and RELATED_ID=#{relatedId,jdbcType=INTEGER}
        and ATTACHMENT_TYPE=#{attachmentType,jdbcType=INTEGER}
        and ATTACHMENT_FUNCTION=#{attachmentFunction,jdbcType=INTEGER}
    </where>
    order by ATTACHMENT_ID DESC LIMIT 1
  </select>
</mapper>