package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchExpressDetailDto;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BatchExpressDetailDtoMapper {
    int deleteByPrimaryKey(Integer expressDetailId);

    int insert(BatchExpressDetailDto record);

    int insertOrUpdate(BatchExpressDetailDto record);

    int insertOrUpdateSelective(BatchExpressDetailDto record);

    int insertSelective(BatchExpressDetailDto record);

    BatchExpressDetailDto selectByPrimaryKey(Integer expressDetailId);

    int updateByPrimaryKeySelective(BatchExpressDetailDto record);

    int updateByPrimaryKey(BatchExpressDetailDto record);

    int updateBatch(List<BatchExpressDetailDto> list);

    int updateBatchSelective(List<BatchExpressDetailDto> list);

    int batchInsert(@Param("list") List<BatchExpressDetailDto> list);

    List<BatchExpressDetailDto> findByExpressIdAndBusinessType(@Param("expressId")Integer expressId,@Param("businessType")Integer businessType);


}