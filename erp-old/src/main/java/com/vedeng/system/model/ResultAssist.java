package com.vedeng.system.model;

import java.math.BigDecimal;

public class ResultAssist {

	private Integer relatedId;
	
	private Integer relatedInt;
	
	private String relatedStr;
	
	private BigDecimal relatedDecimal;
	
	private Long relatedLong;

	private Object relatedObj;
	
	
	public Object getRelatedObj() {
		return relatedObj;
	}

	public void setRelatedObj(Object relatedObj) {
		this.relatedObj = relatedObj;
	}

	public Long getRelatedLong() {
		return relatedLong;
	}

	public void setRelatedLong(Long relatedLong) {
		this.relatedLong = relatedLong;
	}

	public Integer getRelatedId() {
		return relatedId;
	}

	public void setRelatedId(Integer relatedId) {
		this.relatedId = relatedId;
	}

	public Integer getRelatedInt() {
		return relatedInt;
	}

	public void setRelatedInt(Integer relatedInt) {
		this.relatedInt = relatedInt;
	}

	public String getRelatedStr() {
		return relatedStr;
	}

	public void setRelatedStr(String relatedStr) {
		this.relatedStr = relatedStr;
	}

	public BigDecimal getRelatedDecimal() {
		return relatedDecimal;
	}

	public void setRelatedDecimal(BigDecimal relatedDecimal) {
		this.relatedDecimal = relatedDecimal;
	}
	
}
