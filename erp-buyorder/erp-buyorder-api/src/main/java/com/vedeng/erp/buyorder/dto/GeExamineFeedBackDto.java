package com.vedeng.erp.buyorder.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/2/16 13:05
 * @describe GE审核页反馈
 */
@Data
public class GeExamineFeedBackDto {

    private List<String> errors;

    private Integer geBussinessChanceId;

    // 报单状态 1可跟进 2不可跟进
    private Integer status;

    //跟进状态备注
    private String content;

    // 是否有account 0无 1有
    private Integer isHavingAccount;

    //account名
    private String accountName;

    // account销售区域ID
    private Integer accountAreaId;

    //account详细地址
    private String accountAddress;

    // 是否有MPC 0无 1有
    private Integer isHavingMpc;

    // MPC详情
    private String mpcDetail;

    //account销售区域
    private String accountArea;





}
