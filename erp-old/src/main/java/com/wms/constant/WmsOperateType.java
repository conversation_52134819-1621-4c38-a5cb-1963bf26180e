package com.wms.constant;

/**
 * <AUTHOR>
 * @description:
 * @date 2020/11/1713:50
 */
public class WmsOperateType {

    /**
     *  0销售出库
     */
    public final static Integer WAREHOUSE_OUT = 0;

    /**
     * 1销售售后出库
     *
     */
    public final static Integer ORDER_WAREHOUSE_OUT = 1;

    /**
     *  2采购售后出库
     */
    public final static Integer BUYORDER_WAREHOUSE_OUT = 2;

    /**
     *  3外借出库
     */
    public final static Integer LENDOUT_WAREHOUSE_OUT = 3;

    /**
     *  4报废出库
     */
    public final static Integer CRASH_OUT = 4;

    /**
     *  5调拔出库
     */
    public final static Integer TRANSFER_OUT = 5;

    /**
     *  6样品出库
     */
    public final static Integer SAMPLE_OUT = 6;

    /**
     * 7领用出库
     */
    public static final Integer RECEIVER_OUT = 7;

    /**
     *  8直发销售出库
     */
    public final static Integer DELIVERY_DIRECT_WAREHOUSE_OUT = 8;

    /**
     *  9盘亏出库
     */
    public final static Integer INVENTORY_OUT = 9;

    /**
     * 10 单位转换出库
     */
    public final static Integer UNIT_CONVERSION_OUT = 10;

    /**
     * 样品出库
     */
    public final static Integer SAMPLE_ORDER_OUT = 11;


    /**
     *  0采购入库
     */
    public final static Integer WAREHOUSE_IN = 0;

    /**
     * 1销售售后入库
     *
     */
    public final static Integer ORDER_WAREHOUSE_IN = 1;

    /**
     *  2采购售后入库
     */
    public final static Integer BUYORDER_WAREHOUSE_IN = 2;

    /**
     *  3盘盈入库
     */
    public final static Integer SURPLUS_WAREHOUSE_IN = 3;

    /**
     *  4借货归还
     */
    public final static Integer LENDOUT_WAREHOUSE_IN = 4;

    /**
     *  5库存初始化入库单
     */
    public final static Integer INPUT_CS_INIT = 5;

    /**
     *  6赠品入库
     */
    public final static Integer INPUT_GIFT = 6;

    /**
     *  7直发采购入库
     */
    public final static Integer DELIVERY_DIRECT_WAREHOUSE_IN = 7;

    /**
     * 8 单位转换入库
     */
    public final static Integer UNIT_CONVERSION_IN = 8;


}
