package com.vedeng.erp.kingdee.batch.dto;

import com.vedeng.erp.kingdee.batch.common.bean.BatchBaseDto;
import lombok.*;

import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BatchFlowNodeDto extends BatchBaseDto {

    /**
     * 主键
     */
    private Long flowNodeId;

    /**
     * 业务流转单ID
     */
    private Long flowOrderId;

    /**
     * 是否开票，0:否, 1:是
     */
    private Integer openInvoice;

    /**
     * 节点级别，从1开始依次递增
     */
    private Integer nodeLevel;

    /**
     * 付款方式，0:先款后货, 1:后款先货
     */
    private Integer paymentMethod;

    /**
     * 发票类型 字典表
     */
    private Integer invoiceType;

    /**
     * 发票税率
     */
    private BigDecimal ratio;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 账期支付金额
     */
    private BigDecimal creditPayment;

    /**
     * 尾款金额
     */
    private BigDecimal balance;

    /**
     * 尾款期限（月）
     */
    private Integer balanceDueDate;


    /**
     * 交易者id
     */
    private Integer traderId;

    /**
     * 交易者名称
     */
    private String traderName;

    /**
     * 联系人ID
     */
    private Integer traderContactId;

    /**
     * 联系人姓名
     */
    private String traderContactName;

    /**
     * 联系人手机号
     */
    private String traderContactPhone;


    /**
     * 联系地址id
     */
    private Integer traderAddressId;

    /**
     * 联系地址
     */
    private String traderContactAddress;

    /**
     * 收货联系人ID
     */
    private Integer receiverTraderContactId;

    /**
     * 收货人姓名
     */
    private String receiverName;

    /**
     * 收货人手机号
     */
    private String receiverPhone;

    /**
     * 收货地址ID
     */
    private Integer receiverAddressId;

    /**
     * 收货地址
     */
    private String receiverAddress;

    /**
     * 收票联系人ID
     */
    private Integer invoiceReceiverTraderContactId;

    /**
     * 收票人姓名
     */
    private String invoiceReceiverName;

    /**
     * 收票人手机号
     */
    private String invoiceReceiverPhone;

    /**
     * 收票地址ID
     */
    private Integer invoiceReceiverAddressId;

    /**
     * 收票地址
     */
    private String invoiceReceiverAddress;
}