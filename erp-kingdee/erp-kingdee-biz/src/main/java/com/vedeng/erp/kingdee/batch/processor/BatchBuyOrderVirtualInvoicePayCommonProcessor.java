package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.finance.enums.InvoiceTaxTypeEnum;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.*;
import com.vedeng.erp.kingdee.domain.entity.KingDeePurchaseReceiptEntity;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.erp.kingdee.dto.result.KingDeePurchaseInitReceiptQueryResultDto;
import com.vedeng.erp.kingdee.dto.result.KingDeePurchaseReceiptQueryResultDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeePurchaseReceiptConvertor;
import com.vedeng.erp.kingdee.repository.mappers.KingDeePurchaseReceiptMapper;
import com.vedeng.erp.kingdee.service.KingDeePayCommonService;
import com.vedeng.erp.kingdee.service.KingDeePurchaseInitInStockService;
import com.vedeng.erp.kingdee.service.KingDeePurchaseReceiptApiService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 采购票的应付单
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchBuyOrderVirtualInvoicePayCommonProcessor implements ItemProcessor<BatchVirtualInvoiceDto, KingDeePayCommonDto> {

    @Autowired
    private KingDeePurchaseReceiptMapper kingDeePurchaseReceiptMapper;

    @Autowired
    private KingDeePurchaseReceiptConvertor kingDeePurchaseReceiptConvertor;

    @Autowired
    private BatchInvoiceDtoMapper batchInvoiceDtoMapper;

    @Autowired
    protected KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private KingDeePayCommonService kingDeePayCommonApiService;

    @Autowired
    private BatchRInvoiceDetailJOperateLogDtoMapper batchRInvoiceDetailJOperateLogDtoMapper;

    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;

    @Autowired
    private BatchWarehouseGoodsOutInDtoMapper batchWarehouseGoodsOutInDtoMapper;

    @Autowired
    private KingDeePurchaseInitInStockService kingDeePurchaseInitInStockService;

    @Autowired
    private KingDeePurchaseReceiptApiService kingDeePurchaseReceiptApiService;


    @Autowired
    private BatchVirtualInvoiceItemDtoMapper batchVirtualInvoiceItemDtoMapper;

    @Autowired
    private BatchRVirtualInvoiceJWarehouseDtoMapper batchRVirtualInvoiceJWarehouseDtoMapper;


    @Override
    public KingDeePayCommonDto process(BatchVirtualInvoiceDto batchInvoiceDto) throws Exception {

        log.info("处理采购单虚拟蓝字有效票{}" , JSON.toJSONString(batchInvoiceDto));

        if (isBlueDisEnable(batchInvoiceDto)) return null;


        List<BatchRVirtualInvoiceJWarehouseDto> rVirtualInvoiceJWarehouseDtoList = batchRVirtualInvoiceJWarehouseDtoMapper.selectByVirtualInvoiceId(batchInvoiceDto.getVirtualInvoiceId());

        // 根据 商品维度聚合分组
        if (CollUtil.isEmpty(rVirtualInvoiceJWarehouseDtoList)) {
            log.error("当前采购单虚拟蓝字有效票id:{},未关联到入库单信息",JSON.toJSONString(batchInvoiceDto.getVirtualInvoiceId()));
            return null;
        }
        //一级dto

        KingDeePayCommonDto kingDeePayCommonDto = new KingDeePayCommonDto();
        kingDeePayCommonDto.setFQzokBddjtId(batchInvoiceDto.getUuid());
        // 限制查采购票
        kingDeePayCommonDto.setFBusinessType("CG");
        Boolean exist = kingDeePayCommonApiService.kingDeeIsExist(kingDeePayCommonDto);
        if (exist) {
            return null;
        }


        processNum(rVirtualInvoiceJWarehouseDtoList);

        // 票明细id 去聚合
        Map<Integer, List<BatchRVirtualInvoiceJWarehouseDto>> groupByInvoiceDetailId = rVirtualInvoiceJWarehouseDtoList
                .stream().collect(Collectors.groupingBy(BatchRVirtualInvoiceJWarehouseDto::getVirtualInvoiceItemId));

        List<Integer> collect = rVirtualInvoiceJWarehouseDtoList.stream().map(BatchRVirtualInvoiceJWarehouseDto::getWarehouseGoodsOutInItemId).collect(Collectors.toList());
        List<BatchWarehouseGoodsOutInDto> batchWarehouseGoodsOutInDtos = batchWarehouseGoodsOutInDtoMapper.selectWarehouseOutInOrder(collect);

        updateInvoiceOpenTime(batchInvoiceDto, batchWarehouseGoodsOutInDtos);

        List<String> outInNos = batchWarehouseGoodsOutInDtos.stream().map(BatchWarehouseGoodsOutInDto::getOutInNo).distinct().collect(Collectors.toList());
        List<KingDeePurchaseReceiptDto> all2Dto = new ArrayList<>();
        for (String no : outInNos) {
            processOutInNo(all2Dto, no);
        }

        if (CollUtil.isEmpty(all2Dto)) {
            log.error("当前采购单虚拟蓝字:{},有效票未关联到推送金蝶入库单信息",JSON.toJSONString(batchInvoiceDto.getVirtualInvoiceId()));
            throw new KingDeeException("当前采购单虚拟蓝字:" + JSON.toJSONString(batchInvoiceDto.getVirtualInvoiceId()) + ",有效票未关联到推送金蝶入库单信息");
        }

        Map<String, KingDeePurchaseReceiptDto> kingDeeData = all2Dto
                .stream().collect(Collectors.toMap(KingDeePurchaseReceiptDto::getFBillNo,c->c,(k1,k2)->k1));

        KingDeePayCommonAndInvoiceDto kingDeePayCommonAndInvoiceDto = prepareKingDeePayCommonAndInvoiceDto(batchInvoiceDto);

        List<BatchVirtualInvoiceItemDto> virtualInvoiceItemDtos = batchVirtualInvoiceItemDtoMapper.selectByVirtualInvoiceId(batchInvoiceDto.getVirtualInvoiceId());
        Map<Integer, BatchVirtualInvoiceItemDto> invoiceDetail2Map = virtualInvoiceItemDtos.stream().collect(Collectors.toMap(BatchVirtualInvoiceItemDto::getVirtualInvoiceItemId, c -> c, (k1, k2) -> k1));

        DecimalFormat decimalFormat = new DecimalFormat("0.00#");
        String taxRate = Objects.isNull(batchInvoiceDto.getRatio()) ? "0.00" :kingDeePayCommonAndInvoiceDto.getSpecial()? decimalFormat.format(batchInvoiceDto.getRatio().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP)):"0.00";
        BigDecimal taxRateNum = Objects.isNull(batchInvoiceDto.getRatio()) ? BigDecimal.ZERO : kingDeePayCommonAndInvoiceDto.getSpecial()?batchInvoiceDto.getRatio():BigDecimal.ZERO;

        kingDeePayCommonAndInvoiceDto.setKingDeePayCommonDto(kingDeePayCommonDto);
        kingDeePayCommonDto.setFDate(DateUtil.formatDate(batchInvoiceDto.getOpenInvoiceTime()));
        kingDeePayCommonDto.setFSupplierId(batchInvoiceDto.getTraderSupplierId().toString());
        // 构建应付单
        List<KingDeePayCommonDetailDto> kingDeePayCommonDetailDtoList = new ArrayList<>();
        kingDeePayCommonDto.setFEntityDetail(kingDeePayCommonDetailDtoList);
        groupByInvoiceDetailId.forEach((k, v) -> {

            // 匹配总数
            BigDecimal totalNum = v.stream().map(BatchRVirtualInvoiceJWarehouseDto::getNum).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            if (totalNum.compareTo(BigDecimal.ZERO) == 0) {
                log.info("匹配数量为0：{}",JSON.toJSONString(v));
                return;
            }

            List<KingDeePayCommonDetailDto> calcNum = new ArrayList<>();

            // 票明细总额
            BatchVirtualInvoiceItemDto data = invoiceDetail2Map.get(k);
            BigDecimal totalAmount = Objects.isNull(data.getTotalAmount()) ? BigDecimal.ZERO : data.getTotalAmount().abs();
            // 平均价格
            BigDecimal priceAverage = totalAmount.divide(totalNum,6, RoundingMode.HALF_UP);

            for (int i = 0; i < v.size(); i++) {
                BatchRVirtualInvoiceJWarehouseDto batchRVirtualInvoiceJWarehouseDto = v.get(i);
                BigDecimal thisAllNum = batchRVirtualInvoiceJWarehouseDto.getNum();
                BigDecimal thisTotalAmount = thisAllNum.multiply(priceAverage).setScale(2, RoundingMode.HALF_UP);
                //二级dto
                KingDeePayCommonDetailDto kingDeePayCommonDetailDto = new KingDeePayCommonDetailDto();
                kingDeePayCommonDetailDtoList.add(kingDeePayCommonDetailDto);
                calcNum.add(kingDeePayCommonDetailDto);
                kingDeePayCommonDetailDto.setFMaterialId(batchRVirtualInvoiceJWarehouseDto.getSku());
                // 商品在当前发票行的对应开票总数
                kingDeePayCommonDetailDto.setFPriceQty(thisAllNum.toString());
                // 数组中商品的录票单价平均值
                kingDeePayCommonDetailDto.setFTaxPrice(priceAverage.toString());
                kingDeePayCommonDetailDto.setFEntryTaxRate(taxRate);
                // 票的明细行id
                KingDeePurchaseReceiptDto kingDeeInData = kingDeeData.get(batchRVirtualInvoiceJWarehouseDto.getOutInNo());
                if (Objects.isNull(kingDeeInData)) {
                    log.error("未查到入库单信息{}", k);
                    throw new KingDeeException("未查到入库单信息");
                }
                kingDeePayCommonDetailDto.setF_QZOK_BDDJHID(k.toString()+"-"+batchRVirtualInvoiceJWarehouseDto.getWarehouseGoodsOutInItemId());
                kingDeePayCommonDetailDto.setFSourceType(KingDeeFormConstant.STK_INSTOCK);
                // 价税合计 发票sku商品录入的总额
                kingDeePayCommonDetailDto.setFAllAmountFor_D(thisTotalAmount.toString());
                // 税额  [价税合计/(1+ 税率)]*税率
                BigDecimal taxAmouontFor = thisTotalAmount.multiply(taxRateNum).divide(BigDecimal.ONE.add(taxRateNum),2,RoundingMode.HALF_UP);
                kingDeePayCommonDetailDto.setFTaxAmountFor_D(taxAmouontFor.toString());
                // 发票不含税的金额 价税合计-税额
                kingDeePayCommonDetailDto.setFNoTaxAmountFor_D(thisTotalAmount.subtract(taxAmouontFor).toString());

                //三级dto
                List<KingDeePayCommonDetailLinkDto> kingDeePayCommonDetailLinkDtoList = new ArrayList<>();
                kingDeePayCommonDetailDto.setFEntityDetail_Link(kingDeePayCommonDetailLinkDtoList);

                Boolean hasInitInStock = kingDeeInData.getHasInitInStock();
                if (hasInitInStock) {
                    kingDeePayCommonDetailDto.setFSourceType(KingDeeFormConstant.STK_INIT_IN_STOCK);
                }
                // 入库单
                List<KingDeePurchaseReceiptDetailDto> fInStockEntry = kingDeeInData.getFInStockEntry();
                // 入库单明细
                Map<String, KingDeePurchaseReceiptDetailDto> fInStockEntry2Map = fInStockEntry.stream().collect(Collectors.toMap(KingDeePurchaseReceiptDetailDto::getFQzokBddjhId, a -> a, (k1, k2) -> k1));

                // 此条入库单明细
                KingDeePurchaseReceiptDetailDto kingDeePurchaseReceiptDetail = fInStockEntry2Map.get(batchRVirtualInvoiceJWarehouseDto.getWarehouseGoodsOutInItemId().toString());
                if (Objects.isNull(kingDeePurchaseReceiptDetail)) {
                    log.error("未能查询到此入库单明细对应的金蝶入库单明细{}", JSON.toJSONString(batchRVirtualInvoiceJWarehouseDto));
                    throw new KingDeeException("未查到入库单信息");
                }
                BatchVirtualInvoiceItemDto batchVirtualInvoiceItemDto = invoiceDetail2Map.get(batchRVirtualInvoiceJWarehouseDto.getVirtualInvoiceItemId());
                if (Objects.isNull(batchVirtualInvoiceItemDto)) {
                    log.error("未能查到票的明细信息{}", JSON.toJSONString(batchRVirtualInvoiceJWarehouseDto));
                    throw new KingDeeException("未能查到票的明细信息");
                }

                KingDeePayCommonDetailLinkDto kingDeePayCommonDetailLinkDto = new KingDeePayCommonDetailLinkDto();
                kingDeePayCommonDetailLinkDto.setFLinkId("0");
                kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FRuleId("AP_InStockToPayableMap");
                kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FFlowLineId("0");
                kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FSTableId("0");
                kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FSTableName("T_STK_INSTOCKENTRY");

                if (hasInitInStock) {
                    kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FRuleId("InitInStock_To_ApPayableMap");
                    kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FSTableName("T_STK_INITINSTOCKENTRY");
                }
                // 金蝶入库单id
                kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FSBillId(kingDeeInData.getFId());
                // 金蝶入库行id
                kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FSId(kingDeePurchaseReceiptDetail.getFEntryId());
                kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FBASICUNITQTYOld(batchRVirtualInvoiceJWarehouseDto.getNum().toString());
                kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FBASICUNITQTY(batchRVirtualInvoiceJWarehouseDto.getNum().toString());
                kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FStockBaseQty(batchRVirtualInvoiceJWarehouseDto.getNum().toString());
                kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FStockBaseQtyOld(batchRVirtualInvoiceJWarehouseDto.getNum().toString());
                if (i == v.size() - 1) {

                    // 减去最后一行 获取前面的已录入
                    BigDecimal previousAmount = priceAverage.multiply(totalNum.subtract(batchRVirtualInvoiceJWarehouseDto.getNum())).setScale(2, RoundingMode.HALF_UP);
                    // 避免尾差
                    BigDecimal thisLine = totalAmount.subtract(previousAmount);
                    log.info("采购蓝票invoiceId:{},sku:{},已录入:{},最后一行:{}", batchInvoiceDto.getVirtualInvoiceId(), kingDeePayCommonDetailDto.getFMaterialId(), previousAmount, thisLine);
                    kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FALLAMOUNTFOR_DOld(thisLine.toString());
                    kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FALLAMOUNTFOR_D(thisLine.toString());
                } else {
                    // 均价*录入关系的数量
                    BigDecimal thisLine = priceAverage.multiply(batchRVirtualInvoiceJWarehouseDto.getNum()).setScale(2, RoundingMode.HALF_UP);
                    log.info("采购虚拟蓝票invoiceId:{},sku:{},前几行金额:{}", batchInvoiceDto.getVirtualInvoiceId(), kingDeePayCommonDetailDto.getFMaterialId(), thisLine);
                    kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FALLAMOUNTFOR_DOld(thisLine.toString());
                    kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FALLAMOUNTFOR_D(thisLine.toString());
                }

                kingDeePayCommonDetailLinkDtoList.add(kingDeePayCommonDetailLinkDto);
            }

            if (CollUtil.isEmpty(calcNum)) {
                return;
            }
            processTailDifferent(taxRateNum, calcNum, totalAmount);


        });

        return kingDeePayCommonDto;
    }

    private void processTailDifferent(BigDecimal taxRateNum, List<KingDeePayCommonDetailDto> calcNum, BigDecimal totalAmount) {
        BigDecimal logTotalAmount = calcNum.stream().map(x -> new BigDecimal(x.getFAllAmountFor_D())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        // 满足查额在0.01以内的
        if (totalAmount.subtract(logTotalAmount.abs()).abs().compareTo(new BigDecimal("0.01")) <= 0) {
            KingDeePayCommonDetailDto kingDeePayCommonDetailDto = calcNum.get(calcNum.size() - 1);

            BigDecimal subtract = totalAmount.subtract(logTotalAmount);
            BigDecimal bigDecimal = new BigDecimal(kingDeePayCommonDetailDto.getFAllAmountFor_D());
            BigDecimal total = bigDecimal.add(subtract);
            kingDeePayCommonDetailDto.setFAllAmountFor_D(total.toString());
            // 税额  [价税合计/(1+ 税率)]*税率
            BigDecimal taxAmouontFor = total.multiply(taxRateNum).divide(BigDecimal.ONE.add(taxRateNum),2,RoundingMode.HALF_UP);
            kingDeePayCommonDetailDto.setFTaxAmountFor_D(taxAmouontFor.toString());
            // 发票不含税的金额 价税合计-税额
            kingDeePayCommonDetailDto.setFNoTaxAmountFor_D(total.subtract(taxAmouontFor).toString());
        }
    }

    private KingDeePayCommonAndInvoiceDto prepareKingDeePayCommonAndInvoiceDto(BatchVirtualInvoiceDto batchInvoiceDto) {
        // 发票类型 1电票 2 纸票
        KingDeePayCommonAndInvoiceDto kingDeePayCommonAndInvoiceDto = new KingDeePayCommonAndInvoiceDto();
        boolean isSpecialInvoice = InvoiceTaxTypeEnum.getIsSpecialInvoice(batchInvoiceDto.getInvoiceType());
        kingDeePayCommonAndInvoiceDto.setSpecial(isSpecialInvoice);
        return kingDeePayCommonAndInvoiceDto;
    }

    private void processOutInNo(List<KingDeePurchaseReceiptDto> all2Dto, String no) {
        List<KingDeePurchaseReceiptEntity> nodata = kingDeePurchaseReceiptMapper.findAll(KingDeePurchaseReceiptQueryDto.builder().fBillNo(no).build());

        if (CollUtil.isNotEmpty(nodata)) {
            List<KingDeePurchaseReceiptDto> kingDeePurchaseReceiptDtos = kingDeePurchaseReceiptConvertor.toDto(nodata);
            all2Dto.addAll(kingDeePurchaseReceiptDtos);
        } else {

             // 先查金蝶 查不到查期初
            List<KingDeePurchaseReceiptQueryResultDto> data = kingDeePurchaseReceiptApiService.queryByOutInNo(no);
            if (CollUtil.isEmpty(data)) {
                List<KingDeePurchaseInitReceiptQueryResultDto> kingDeePurchaseInitInStock = kingDeePurchaseInitInStockService.getKingDeePurchaseInitInStock(no);
                if (CollUtil.isNotEmpty(kingDeePurchaseInitInStock)) {
                    KingDeePurchaseReceiptDto kingDeePurchaseReceiptDto = new KingDeePurchaseReceiptDto();
                    kingDeePurchaseReceiptDto.setHasInitInStock(Boolean.TRUE);
                    kingDeePurchaseReceiptDto.setFId(kingDeePurchaseInitInStock.get(0).getFID());
                    kingDeePurchaseReceiptDto.setFId(kingDeePurchaseInitInStock.get(0).getFID());
                    kingDeePurchaseReceiptDto.setFBillNo(kingDeePurchaseInitInStock.get(0).getFBillNo());
                    List<KingDeePurchaseReceiptDetailDto> entities = kingDeePurchaseInitInStock.stream().map(x -> {
                        KingDeePurchaseReceiptDetailDto result = new KingDeePurchaseReceiptDetailDto();
                        result.setFEntryId(x.getFInitInStockEntry_FEntryId());
                        result.setFQzokBddjhId(x.getF_QZOK_BDDJHID());
                        return result;
                    }).collect(Collectors.toList());
                    kingDeePurchaseReceiptDto.setFInStockEntry(entities);
                    all2Dto.add(kingDeePurchaseReceiptDto);
                }
            }
            if (CollUtil.isNotEmpty(data)) {
                KingDeePurchaseReceiptDto kingDeePurchaseReceiptDto = new KingDeePurchaseReceiptDto();
                kingDeePurchaseReceiptDto.setFId(data.get(0).getFID());
                kingDeePurchaseReceiptDto.setFId(data.get(0).getFID());
                kingDeePurchaseReceiptDto.setFBillNo(data.get(0).getFBillNo());
                List<KingDeePurchaseReceiptDetailDto> entities = data.stream().map(x -> {
                    KingDeePurchaseReceiptDetailDto result = new KingDeePurchaseReceiptDetailDto();
                    result.setFEntryId(x.getFInStockEntry_FEntryId());
                    result.setFQzokBddjhId(x.getF_QZOK_BDDJHID());
                    return result;
                }).collect(Collectors.toList());
                kingDeePurchaseReceiptDto.setFInStockEntry(entities);
                all2Dto.add(kingDeePurchaseReceiptDto);
            }
        }
    }

    private void updateInvoiceOpenTime(BatchVirtualInvoiceDto batchInvoiceDto, List<BatchWarehouseGoodsOutInDto> batchWarehouseGoodsOutInDtos) {
        Optional<Date> max = batchWarehouseGoodsOutInDtos.stream().max(Comparator.comparing(BatchWarehouseGoodsOutInDto::getOutInTime)).map(BatchWarehouseGoodsOutInDto::getOutInTime);
        if (max.isPresent() && max.get().getTime() > batchInvoiceDto.getOpenInvoiceTime().getTime()) {
            batchInvoiceDto.setOpenInvoiceTime(max.get());
        }
    }

    private void processNum(List<BatchRVirtualInvoiceJWarehouseDto> rVirtualInvoiceJWarehouseDtoList) {
        // 处理num
        for (BatchRVirtualInvoiceJWarehouseDto dto : rVirtualInvoiceJWarehouseDtoList) {
            // 统一保留2位小数
            // 判断是否为入库单明细的最后一个 sql 倒排
            List<BatchRVirtualInvoiceJWarehouseDto> byOperateLogId = batchRVirtualInvoiceJWarehouseDtoMapper.findByWarehouseGoodsOutInItemId(dto.getWarehouseGoodsOutInItemId());

            List<BatchRInvoiceDetailJOperateLogDto> jOperateLogDtos = batchRInvoiceDetailJOperateLogDtoMapper.findByOperateLogId(dto.getWarehouseGoodsOutInItemId());
            boolean isLastInvoice = false;
            // 是否为最后一张票
            if (CollUtil.isNotEmpty(jOperateLogDtos)) {
                BatchRInvoiceDetailJOperateLogDto data = jOperateLogDtos.get(0);
                if (dto.getVirtualInvoiceItemId().equals(byOperateLogId.get(0).getVirtualInvoiceItemId()) ) {
                    if (data.getAddTime().getTime() > byOperateLogId.get(0).getAddTime().getTime()) {
                        isLastInvoice = true;
                    } else {
                        isLastInvoice = false;
                    }

                }
            } else {
                isLastInvoice=  dto.getVirtualInvoiceItemId().equals(byOperateLogId.get(0).getVirtualInvoiceItemId());
            }

            // 入库单明细数量是否已经用尽
            BatchWarehouseGoodsOutInItemDto batchWarehouseGoodsOutInItemDto = batchWarehouseGoodsOutInItemDtoMapper.selectByPrimaryKey(Long.valueOf(dto.getWarehouseGoodsOutInItemId()));
            BigDecimal originalNum = batchWarehouseGoodsOutInItemDto.getNum().abs();

            BigDecimal allUseNum = byOperateLogId.stream().map(x -> x.getNum().abs()).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            BigDecimal used = jOperateLogDtos.stream().filter(Objects::nonNull).map(x -> x.getNum().abs()).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            allUseNum = allUseNum.add(used);
            // 用完 且当前明细为最后一个明细
            if (isLastInvoice && originalNum.compareTo(allUseNum) <= 0) {
                // 前面的四舍五入保留2位
                BigDecimal beforeThis = byOperateLogId.stream()
                        .filter(x -> !x.getVirtualInvoiceWarehouseId().equals(dto.getVirtualInvoiceWarehouseId()))
                        .map(x -> {
                            BigDecimal abs = x.getNum().abs();
                            if (abs.compareTo(new BigDecimal("0.01")) <= 0) {
                                return new BigDecimal("0.01");
                            } else {
                                return x.getNum().abs().setScale(2, RoundingMode.DOWN);
                            }
                        })
                        .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                BigDecimal before = jOperateLogDtos.stream().filter(Objects::nonNull).map(x -> {
                    BigDecimal abs = x.getNum().abs();
                    if (abs.compareTo(new BigDecimal("0.01")) <= 0) {
                        return new BigDecimal("0.01");
                    } else {
                        return x.getNum().abs().setScale(2, RoundingMode.DOWN);
                    }
                }).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                beforeThis = beforeThis.add(before);
                dto.setNum(originalNum.subtract(beforeThis));
            } else {

                if (dto.getNum().abs().compareTo(new BigDecimal("0.01")) <= 0) {
                    dto.setNum(new BigDecimal("0.01"));
                } else {
                    dto.setNum(dto.getNum().abs().setScale(2, RoundingMode.DOWN));
                }

            }

        }
    }

    private boolean isBlueDisEnable(BatchVirtualInvoiceDto batchInvoiceDto) {
        if (Objects.nonNull(batchInvoiceDto.getSourceInvoiceId()) && batchInvoiceDto.getSourceInvoiceId() > 0) {
            BatchInvoiceDto byInvoiceId = batchInvoiceDtoMapper.findByInvoiceId(batchInvoiceDto.getSourceInvoiceId());
            if (Objects.nonNull(byInvoiceId) ) {
                // 排除采购蓝字作废
                List<BatchInvoiceDto> blueDisEnableByInvoiceCodeAndInvoiceNo = batchInvoiceDtoMapper.findBlueDisEnableByInvoiceCodeAndInvoiceNo(byInvoiceId.getInvoiceCode(), byInvoiceId.getInvoiceNo(),503);
                if (CollUtil.isNotEmpty(blueDisEnableByInvoiceCodeAndInvoiceNo)) {
                    log.info("当前采购虚拟蓝票关联原始蓝票存在蓝字作废：{}", JSON.toJSONString(blueDisEnableByInvoiceCodeAndInvoiceNo));
                    return true;
                }
            }
        }
        return false;
    }

}
