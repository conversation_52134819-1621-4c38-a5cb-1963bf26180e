package com.vedeng.temporal.workflow.activity.core;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vedeng.common.core.enums.SystemSourceEnum;
import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import com.vedeng.temporal.exception.BusinessProcessException;
import com.vedeng.temporal.exception.ExceptionHandler;
import com.vedeng.temporal.util.SystemApiClient;
import com.vedeng.common.core.http.ISystemApiClient;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * 通用业务操作模板 - 重构版
 * <p>
 * 重构变更：
 * - 移除 FlowContext 依赖，直接使用 CompanyBusinessRequest
 * - 自动处理固定参数（businessId、workflowExecutionId、companyCode等）
 * - 简化配置对象，使用函数式接口
 * - 集成 SystemApiClient 进行 API 调用
 * - 职责分离：异常处理委托给 ExceptionHandler（新增）
 * <p>
 * 设计理念：
 * - 统一处理所有 HTTP 接口的固定参数
 * - 避免在每个 Activity 中重复处理通用参数
 * - 提供标准化的业务操作流程
 * - 支持灵活的业务数据准备和结果提取
 * - 单一职责：专注于业务模板执行，异常处理委托给专门的处理器
 * <p>
 * 通用流程：
 * 1. 自动添加固定参数（businessId、workflowExecutionId等）
 * 2. 业务数据准备（通过 dataPreparer 函数）
 * 3. 合并参数并调用 API
 * 4. 结果提取（通过 resultExtractor 函数）
 * 5. 异常处理（委托给 ExceptionHandler）
 *
 * <AUTHOR> 4.0 sonnet
 * @version 3.0 (职责分离，异常处理委托给 ExceptionHandler)
 * @since 2025-01-18
 */
@Component
@Slf4j
public class UniversalActivityTemplate {

    @Autowired
    private SystemApiClient systemApiClient;

    @Autowired
    private ExceptionHandler exceptionHandler;

    private final ObjectMapper objectMapper = new ObjectMapper();


    /**
     * 执行业务操作（异常优先模式）
     * 成功时返回结果，失败时抛出BusinessProcessException
     * <p>
     * 设计理念：
     * - 统一异常处理架构的核心方法
     * - 让异常向上传播，由上层统一处理
     * - 消除Response和Exception模式的架构分歧
     *
     * @param request 业务请求
     * @param config  操作配置
     * @return 成功时的业务响应
     * @throws BusinessProcessException 业务执行失败时抛出
     */
    public CompanyBusinessResponse execute(CompanyBusinessRequest request, BusinessOperationConfig config)
            throws BusinessProcessException {
        long startTime = System.currentTimeMillis();

        try {
            log.info("开始执行业务操作: {}, 业务ID: {}", config.getOperationName(), request.getBusinessId());

            // 1. 构建幂等性请求头
            Map<String, String> idempotencyHeaders = buildIdempotencyHeaders(request);

            // 2. 业务数据准备
            Object businessData = null;
            if (config.getDataPreparer() != null) {
                businessData = config.getDataPreparer().apply(request);
            }

            // 3. 合并请求头（业务请求头 + 幂等性请求头）
            Map<String, String> finalHeaders = mergeHeaders(request.getHeaders(), idempotencyHeaders);

            // 4. 调用业务接口（动态用户ID支持）
            ISystemApiClient apiClient = systemApiClient.withCompany(request.getTargetCompanyCode());

            // 添加用户信息（如果有）
            if (request.getUserId() != null) {
                apiClient = apiClient.withUser(request.getUserId());
            } else if (request.getUserName() != null) {
                apiClient = apiClient.withUser(request.getUserName());
            }

            String responseJson = apiClient.postToSystemApi(config.getApiPath(), businessData, finalHeaders, SystemSourceEnum.TEMPORAL.name());

            // 5. 解析响应为Map对象
            Map<String, Object> apiResponseMap = objectMapper.readValue(responseJson, Map.class);

            // 6. 检查API调用是否成功 (基于ApiResponse标准格式：code=0表示成功)
            Integer code = (Integer) apiResponseMap.get("code");
            String message = (String) apiResponseMap.get("message");
            Object data = apiResponseMap.get("data");

            if (code == null || !code.equals(0)) {
                long duration = System.currentTimeMillis() - startTime;
                String errorMsg = String.format("%s失败: %s (错误码: %d)",
                        config.getOperationName(), message, code != null ? code : -1);
                log.error("{}, 业务ID: {}, 耗时: {}ms", errorMsg, request.getBusinessId(), duration);

                // 返回失败的Response，让Activity层决定如何处理
                return CompanyBusinessResponse.builder()
                        .success(false)
                        .message(errorMsg)
                        .errorCode(code != null ? "API_ERROR_" + code : "API_ERROR_UNKNOWN")
                        .processTimestamp(System.currentTimeMillis())
                        .build();
            }

            // 7. 成功时进行结果提取
            String extractedResult = null;
            if (config.getResultExtractor() != null) {
                // 传递ApiResponse的data字段
                Map<String, Object> responseData = data != null ?
                        objectMapper.convertValue(data, Map.class) : new HashMap<>();
                extractedResult = config.getResultExtractor().apply(responseData);
            }


            long duration = System.currentTimeMillis() - startTime;
            log.info("{} 执行成功, 业务ID: {}, 结果: {}, 耗时: {}ms",
                    config.getOperationName(), request.getBusinessId(), extractedResult, duration);

            return CompanyBusinessResponse.builder()
                    .success(true)
                    .message(config.getOperationName() + "成功")
                    .generatedDocumentId(extractedResult)
                    .resultData(data)
                    .processTimestamp(System.currentTimeMillis())
                    .build();

        } catch (BusinessProcessException e) {
            // 业务流程异常直接重新抛出
            throw e;
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("{} 执行异常, 业务ID: {}, 耗时: {}ms",
                    config.getOperationName(), request.getBusinessId(), duration, e);

            // 使用简化的异常处理，直接抛出分类后的异常
            exceptionHandler.handleAndThrowActivityException(e, config.getOperationName(),
                    request.getBusinessId(), request.getTargetCompanyCode());
        }
        return CompanyBusinessResponse.success(config.getOperationName() + "成功", "");
    }


    // ========== 核心辅助方法 ==========

    /**
     * 构建幂等性请求头
     * 将业务相关的元数据构建为HTTP请求头，用于幂等性控制
     */
    private Map<String, String> buildIdempotencyHeaders(CompanyBusinessRequest request) {
        Map<String, String> headers = new HashMap<>();

        // 业务相关头部 - 流程订单ID（核心幂等性字段）
        if (request.getBusinessId() != null) {
            headers.put("X-Idempotency-Flow-Order-Id", request.getBusinessId());
        }

        // 公司相关头部 - 用于幂等性范围控制
        if (request.getTargetCompanyCode() != null) {
            headers.put("X-Idempotency-Company-Code", request.getTargetCompanyCode());
        }

        // 业务类型头部 - 用于区分不同业务类型的幂等性
        if (request.getBusinessType() != null) {
            headers.put("X-Idempotency-Business-Type", request.getBusinessType());
        }

        // 用户相关头部 - 用于用户级别的幂等性控制
        if (request.getUserId() != null) {
            headers.put("X-Idempotency-User-Id", request.getUserId().toString());
        }

        log.debug("构建幂等性请求头完成, 业务ID: {}, 头部数量: {}",
                request.getBusinessId(), headers.size());

        return headers;
    }

    /**
     * 合并请求头
     * 将业务请求头和幂等性请求头合并，幂等性请求头优先级更高
     */
    private Map<String, String> mergeHeaders(Map<String, String> businessHeaders, Map<String, String> idempotencyHeaders) {
        Map<String, String> finalHeaders = new HashMap<>();

        // 先添加业务请求头
        if (businessHeaders != null) {
            finalHeaders.putAll(businessHeaders);
        }

        // 再添加幂等性请求头（会覆盖同名的业务请求头）
        if (idempotencyHeaders != null) {
            finalHeaders.putAll(idempotencyHeaders);
        }

        return finalHeaders;
    }

    /**
     * 业务操作配置类 - 简化版
     * 移除 FlowContext 依赖，使用函数式接口
     */
    @Data
    public static class BusinessOperationConfig {

        /**
         * 操作名称（用于日志）
         */
        private String operationName;

        /**
         * API路径
         */
        private String apiPath;

        /**
         * 数据准备器（从 CompanyBusinessRequest 准备业务数据）
         */
        private Function<CompanyBusinessRequest, Object> dataPreparer;

        /**
         * 结果提取器（从 API 响应中提取需要的结果）
         */
        private Function<Map<String, Object>, String> resultExtractor;

        /**
         * 创建配置构建器
         */
        public static BusinessOperationConfig create() {
            return new BusinessOperationConfig();
        }

        /**
         * 设置操作名称
         */
        public BusinessOperationConfig operationName(String operationName) {
            this.operationName = operationName;
            return this;
        }

        /**
         * 设置API路径
         */
        public BusinessOperationConfig apiPath(String apiPath) {
            this.apiPath = apiPath;
            return this;
        }

        /**
         * 设置数据准备器
         */
        public BusinessOperationConfig dataPreparer(Function<CompanyBusinessRequest, Object> dataPreparer) {
            this.dataPreparer = dataPreparer;
            return this;
        }

        /**
         * 设置结果提取器
         */
        public BusinessOperationConfig resultExtractor(Function<Map<String, Object>, String> resultExtractor) {
            this.resultExtractor = resultExtractor;
            return this;
        }
    }
}
