package com.vedeng.erp.kingdee.batch.dto;

import com.vedeng.erp.kingdee.batch.common.bean.BatchBaseDto;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * wms入库单商品表
 * <AUTHOR>
 */
@Getter
@Setter
public class BatchWmsInputOrderGoodsDto extends BatchBaseDto {


    private Integer wmsInputOrderGoodsId;

    /**
     * 入库单的id
     */
    private Integer wmsInputOrderId;

    /**
     * sku编号
     */
    private String skuNo;

    /**
     * 商品id
     */
    private Integer goodsId;

    /**
     * 需入库数量
     */
    private Integer inputNum;

    /**
     * 入库数量
     */
    private Integer arrivalNum;

    /**
     * 入库状态:0-未入库 1-部分入库 2 全部入库
     */
    private Integer arrivalStatus;

    /**
     * 是否删除:0-未删除 1-已删除
     */
    private Integer isDeltet;

    /**
     * 修改时间
     */
    private Date modeTime;
}