<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="客户信息(财务专用)修改" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src="<%= basePath %>static/js/jquery/ajaxfileupload.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/trader/edit_trader_finance.js?rnd=${resourceVersionKey}"></script>
<style type="text/css">
	li{
		margin-top: 5px;
	}

</style>

<div class="addElement">
    <div class="add-main adddepart">
        <form id="traderFinanciaForm" method="post" enctype="multipart/form-data">
            <ul>
                <li>
                    <div class="form-tips">
                        <lable>客户ID</lable>
                    </div>
                    <div class="f_left ">
                        <div class="form-blanks">
                           ${trader.traderId}
                        </div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <lable>客户名称</lable>
                    </div>
                    <div class="f_left ">
                        <div class="form-blanks">
                            ${trader.traderName}
                        </div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <lable>客户类型</lable>
                    </div>
                    <div class="f_left ">
                        <div class="form-blanks">
                            <c:if test="${trader.customerType eq 465}">分销</c:if>
                            <c:if test="${trader.customerType eq 466}">终端</c:if>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <lable>客户名称(新)</lable>
                    </div>
                    <div class="f_left ">
                        <div class="form-blanks">
                            <input type="text" class="input-largest" maxlength="50" name="customerNameNew" id="customerNameNew" value="${trader.customerNameNew}" />
                        </div>
                    </div>
                </li>
                <li id="customerNatureLi">
                    <div class="form-tips">
                        <lable><font color="red">*</font>客户性质</lable>
                    </div>
                    <select class="input-middle" name="customerNature" id="customerNature" onchange="customerNatureSelect()">
                        <option value="0">选择</option>
                        <option value="1" <c:if test="${trader.customerNature eq 1}">selected="selected"</c:if>>直接客户</option>
                        <option value="2" <c:if test="${trader.customerNature eq 2}">selected="selected"</c:if>>间接客户</option>
                    </select>
                </li>

                <c:if test="${trader.customerClass==0}">
                <li id="customerClassLi" hidden>
                    <div class="form-tips">
                        <lable><font color="red">*</font>终端客户分类</lable>
                    </div>
                    <select class="input-middle" name="customerClass" id="customerClass" onchange="customerClassSelect()">
                        <option value="0">选择</option>
                        <option value="1" <c:if test="${trader.customerClass eq 1}">selected="selected"</c:if>>公立</option>
                        <option value="2" <c:if test="${trader.customerClass eq 2}">selected="selected"</c:if>>民营个体</option>
                        <option value="3" <c:if test="${trader.customerClass eq 3}">selected="selected"</c:if>>民营集团</option>
                    </select>
                </li>
                </c:if>
                <c:if test="${trader.customerClass!=0}">
                    <li id="customerClassLi">
                        <div class="form-tips">
                            <lable><font color="red">*</font>终端客户分类</lable>
                        </div>
                        <select class="input-middle" name="customerClass" id="customerClass" onchange="customerClassSelect()">
                            <option value="0">选择</option>
                            <option value="1" <c:if test="${trader.customerClass eq 1}">selected="selected"</c:if>>公立</option>
                            <option value="2" <c:if test="${trader.customerClass eq 2}">selected="selected"</c:if>>民营个体</option>
                            <option value="3" <c:if test="${trader.customerClass eq 3}">selected="selected"</c:if>>民营集团</option>
                        </select>
                    </li>
                </c:if>
                <c:if test="${not empty trader.groupName}">
                <li id="groupNameLi">
                    <div class="form-tips">
                        <lable>所属集团</lable>
                    </div>
                    <div class="f_left ">
                        <div class="form-blanks">
                            <input type="text" class="input-largest"  maxlength="50" name="groupName" id="groupName" value="${trader.groupName}" />
                        </div>
                    </div>
                </li>
                </c:if>
                <c:if test="${empty trader.groupName}">
                    <li id="groupNameLi" hidden>
                        <div class="form-tips">
                            <lable>所属集团</lable>
                        </div>
                        <div class="f_left ">
                            <div class="form-blanks">
                                <input type="text" class="input-largest" maxlength="50" name="groupName" id="groupName" value="${trader.groupName}" />
                            </div>
                        </div>
                    </li>
                </c:if>

                <c:if test="${trader.customerSecondType==0}">
                <li id="customerSecondTypeLi" hidden>
                    <div class="form-tips">
                        <lable><font color="red">*</font>客户细分类</lable>
                    </div>
                    <select class="input-middle" name="customerSecondType" id="customerSecondType" onchange="customerSecondTypeSelect()">
                        <option value="0">选择</option>
                        <option value="1" <c:if test="${trader.customerSecondType eq 1}">selected="selected"</c:if>>医疗卫生机构</option>
                        <option value="2" <c:if test="${trader.customerSecondType eq 2}">selected="selected"</c:if>>非医疗卫生机构</option>
                        <option value="3" <c:if test="${trader.customerSecondType eq 3}">selected="selected"</c:if>>分销商</option>
                    </select>
                </li>
                </c:if>
                <c:if test="${trader.customerSecondType!=0}">
                    <li id="customerSecondTypeLi">
                        <div class="form-tips">
                            <lable><font color="red">*</font>客户细分类</lable>
                        </div>
                        <select class="input-middle" name="customerSecondType" id="customerSecondType" onchange="customerSecondTypeSelect()">
                            <option value="0">选择</option>
                            <option value="1" <c:if test="${trader.customerSecondType eq 1}">selected="selected"</c:if>>医疗卫生机构</option>
                            <option value="2" <c:if test="${trader.customerSecondType eq 2}">selected="selected"</c:if>>非医疗卫生机构</option>
                            <option value="3" <c:if test="${trader.customerSecondType eq 3}">selected="selected"</c:if>>分销商</option>
                        </select>
                    </li>
                </c:if>
                <c:if test="${trader.customerThirdType!=0}">
                <li id="customerThirdTypeLi">
                    <div class="form-tips">
                        <lable><font color="red">*</font>医疗机构分类</lable>
                    </div>
                    <select class="input-middle" name="customerThirdType" id="customerThirdType" onchange="customerThirdTypeSelect()">
                        <option value="0">选择</option>
                        <option value="1" <c:if test="${trader.customerThirdType eq 1}">selected="selected"</c:if>>医院</option>
                        <option value="2" <c:if test="${trader.customerThirdType eq 2}">selected="selected"</c:if>>基层医疗卫生机构</option>
                        <option value="3" <c:if test="${trader.customerThirdType eq 3}">selected="selected"</c:if>>专业医疗卫生机构</option>
                        <option value="4" <c:if test="${trader.customerThirdType eq 4}">selected="selected"</c:if>>其他医疗卫生机构</option>
                    </select>
                </li>
                </c:if>
                <c:if test="${trader.customerThirdType==0}">
                    <li id="customerThirdTypeLi" hidden>
                        <div class="form-tips">
                            <lable><font color="red">*</font>医疗机构分类</lable>
                        </div>
                        <select class="input-middle" name="customerThirdType" id="customerThirdType" onchange="customerThirdTypeSelect()">
                            <option value="0">选择</option>
                            <option value="1" <c:if test="${trader.customerThirdType eq 1}">selected="selected"</c:if>>医院</option>
                            <option value="2" <c:if test="${trader.customerThirdType eq 2}">selected="selected"</c:if>>基层医疗卫生机构</option>
                            <option value="3" <c:if test="${trader.customerThirdType eq 3}">selected="selected"</c:if>>专业医疗卫生机构</option>
                            <option value="4" <c:if test="${trader.customerThirdType eq 4}">selected="selected"</c:if>>其他医疗卫生机构</option>
                        </select>
                    </li>
                </c:if>
                <c:if test="${trader.hospitalLever==0}">
                <li id="hospitalLeverLi"  hidden>
                    <div class="form-tips">
                        <lable><font color="red">*</font>医院等级</lable>
                    </div>
                    <select class="input-middle" name="hospitalLever" id="hospitalLever">
                        <option value="0">选择</option>
                        <option value="1" <c:if test="${trader.hospitalLever eq 1}">selected="selected"</c:if>>一级</option>
                        <option value="2" <c:if test="${trader.hospitalLever eq 2}">selected="selected"</c:if>>二级</option>
                        <option value="3" <c:if test="${trader.hospitalLever eq 3}">selected="selected"</c:if>>三级</option>
                        <option value="4" <c:if test="${trader.hospitalLever eq 4}">selected="selected"</c:if>>未分级</option>
                    </select>
                </li>
                </c:if>
                <c:if test="${trader.hospitalLever!=0}">
                    <li id="hospitalLeverLi">
                        <div class="form-tips">
                            <lable><font color="red">*</font>医院等级</lable>
                        </div>
                        <select class="input-middle" name="hospitalLever" id="hospitalLever">
                            <option value="0">选择</option>
                            <option value="1" <c:if test="${trader.hospitalLever eq 1}">selected="selected"</c:if>>一级</option>
                            <option value="2" <c:if test="${trader.hospitalLever eq 2}">selected="selected"</c:if>>二级</option>
                            <option value="3" <c:if test="${trader.hospitalLever eq 3}">selected="selected"</c:if>>三级</option>
                            <option value="4" <c:if test="${trader.hospitalLever eq 4}">selected="selected"</c:if>>未定级</option>
                        </select>
                    </li>
                </c:if>
                <c:if test="${not empty trader.hospitalName}">
                <li id="hospitalNameLi">
                    <div class="form-tips">
                        <lable>所属医院共体</lable>
                    </div>
                    <div class="f_left ">
                        <div class="form-blanks">
                            <input type="text" class="input-largest" maxlength="50" name="hospitalName" id="hospitalName" value="${trader.hospitalName}" />
                        </div>
                    </div>
                </li>
                </c:if>
                <c:if test="${empty trader.hospitalName}">
                    <li id="hospitalNameLi" hidden>
                        <div class="form-tips">
                            <lable>所属医院共体</lable>
                        </div>
                        <div class="f_left ">
                            <div class="form-blanks">
                                <input type="text" class="input-largest" maxlength="50" name="hospitalName" id="hospitalName" value="${trader.hospitalName}" />
                            </div>
                        </div>
                    </li>
                </c:if>
            </ul>
            <div class="add-tijiao tcenter" style="margin-top:15px">
                <input type="hidden" value="${trader.traderCustomerFinanceId}" name="traderCustomerFinanceId"
                       id="traderCustomerFinanceId">
                <button type="button" class="bt-bg-style bg-deep-green" id="outIn_attach_return_submit"
                        onclick="deliveryOrderReturnSubmit()">提交
                </button>
                <button class="dele" type="button" id="close-layer">取消</button>
            </div>
        </form>
    </div>
</div>
<%@ include file="../../common/footer.jsp"%>