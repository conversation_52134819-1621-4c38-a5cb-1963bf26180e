<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.mobile.system.mapper.TraderCustomerForMobileMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.mobile.system.dto.TraderCustomerSearchResultDto">
        <id column="TRADER_CUSTOMER_ID" property="traderCustomerId" jdbcType="INTEGER"/>
        <result column="TRADER_ID" property="traderId" jdbcType="INTEGER"/>
        <result column="TRADER_NAME" property="traderName" jdbcType="VARCHAR"/>
        <result column="USER_ID" property="userId" jdbcType="INTEGER"/>
        <result column="CUSTOMER_NATURE" property="customerNature" jdbcType="INTEGER"/>
        <result column="TZ_CUSTOMER" property="tzCustomer" jdbcType="INTEGER"/>
        <result column="TZ_CUSTOMER_LEVEL" property="tzCustomerLevel" jdbcType="INTEGER"/>
        <result column="AREA_ID" property="areaId" jdbcType="INTEGER"/>

    </resultMap>

    <select id="searchTraderCustomerByName"  resultType="com.vedeng.mobile.system.dto.TraderCustomerSearchResultDto">
        SELECT A.TRADER_CUSTOMER_ID, A.TRADER_ID,A.CUSTOMER_NATURE, B.TRADER_NAME,B.AREA_ID,
        C.USER_ID,D.TZ_CUSTOMER,D.TZ_CUSTOMER_LEVEL
        FROM T_TRADER_CUSTOMER A
        LEFT JOIN T_TRADER B ON A.TRADER_ID = B.TRADER_ID
        LEFT JOIN T_R_TRADER_J_USER C ON C.TRADER_ID = A.TRADER_ID
        LEFT JOIN DWH_TRADER_LIST_FILTER_ERP D ON D.TRADER_ID = A.TRADER_ID AND A.TRADER_CUSTOMER_ID = D.TRADER_CUSTOMER_ID
        WHERE  B.TRADER_NAME = #{customerName,jdbcType=VARCHAR}
        AND B.COMPANY_ID = 1
        AND A.IS_ENABLE = 1
        LIMIT 10
    </select>

    <select id="searchTraderCustomerListPage"  resultType="com.vedeng.mobile.system.dto.TraderCustomerSearchResultDto">
        SELECT A.TRADER_CUSTOMER_ID, A.TRADER_ID,A.CUSTOMER_NATURE, B.TRADER_NAME,W.AREA_ID,
        C.USER_ID,D.TZ_CUSTOMER,D.TZ_CUSTOMER_LEVEL
        FROM T_TRADER_CUSTOMER A
        LEFT JOIN T_TRADER B ON A.TRADER_ID = B.TRADER_ID
        LEFT JOIN T_R_TRADER_J_USER C ON C.TRADER_ID = A.TRADER_ID
        LEFT JOIN DWH_TRADER_LIST_FILTER_ERP D ON D.TRADER_ID = A.TRADER_ID AND A.TRADER_CUSTOMER_ID = D.TRADER_CUSTOMER_ID
        LEFT JOIN T_TRADER_WORKAREA W ON W.TRADER_ID = B.TRADER_ID
        WHERE  B.TRADER_NAME LIKE CONCAT('%',#{traderCustomerVo.searchTraderName},'%' )
            AND B.COMPANY_ID = #{traderCustomerVo.companyId}
        <if test="traderCustomerVo.isEnable != null" >
            AND A.IS_ENABLE = #{traderCustomerVo.isEnable,jdbcType=BIT}
        </if>
        <if test="traderCustomerVo.userIdList != null and traderCustomerVo.userIdList.size != 0">
            AND (
                A.TRADER_ID in (
                    select TRADER_ID from T_R_TRADER_J_USER where TRADER_TYPE =1
                    and USER_ID in
                    <foreach collection="traderCustomerVo.userIdList" item="userId" index="index"
                             open="(" close=")" separator=",">
                        #{userId}
                    </foreach>
                )
                OR
                A.TRADER_ID in (
                select TRADER_ID from T_R_SALES_J_TRADER where
                    SALE_USER_ID in
                    <foreach collection="traderCustomerVo.userIdList" item="userId" index="index"
                             open="(" close=")" separator=",">
                        #{userId}
                    </foreach>
                )
                OR A.TRADER_ID IN (
                    select DISTINCT TW.TRADER_ID
                    FROM T_CUSTOMER_REGION_SALE TS
                    JOIN T_TRADER_WORKAREA TW ON TS.REGION_ID = TW.AREA_ID
                    JOIN T_R_TRADER_J_USER TRJ
                    ON TRJ.TRADER_ID = TW.TRADER_ID AND TRJ.USER_ID = TS.USER_ID and TRJ.TRADER_TYPE = 1
                    WHERE TS.USER_ID_DOWN IN
                    <foreach collection="traderCustomerVo.userIdList" item="userId" index="index"
                             open="(" close=")" separator=",">
                        #{userId}
                    </foreach>
                )
                OR A.TRADER_ID IN (
                    select DISTINCT TW.TRADER_ID FROM T_CUSTOMER_REGION_SALE TS
                    JOIN T_TRADER_WORKAREA TW ON TS.REGION_ID  = TW.AREA_ID
                    JOIN T_R_TRADER_J_USER TRJ ON TRJ.TRADER_ID  = TW.TRADER_ID  AND TRJ.USER_ID = TS.USER_ID_DOWN  and TRJ.TRADER_TYPE =1
                    WHERE TS.USER_ID  IN
                    <foreach collection="traderCustomerVo.userIdList" item="userId" index="index"
                             open="(" close=")" separator=",">
                        #{userId}
                    </foreach>
                    )
                )
        </if>
    </select>

    <select id="selectTraderCustomerId" resultType="java.lang.Integer">
        select DISTINCT USER_ID
        FROM (
                 select USER_ID AS USER_ID
                 FROM T_R_TRADER_J_USER
                 WHERE TRADER_ID = #{traderId,jdbcType=INTEGER}
                 UNION ALL
                 select SALE_USER_ID AS USER_ID
                 from T_R_SALES_J_TRADER
                 where TRADER_ID = #{traderId,jdbcType=INTEGER} AND IS_DELETED = 0
             ) TEMP
    </select>

    <select id="selectSaleDownArea" resultType="java.lang.Integer">
        select REGION_ID from T_CUSTOMER_REGION_SALE where USER_ID_DOWN IN
        <foreach collection="userIds" item="userId" index="index"
                 open="(" close=")" separator=",">
            #{userId}
        </foreach>
        AND REGION_ID = #{areaCode,jdbcType=INTEGER}
    </select>

    <select id="selectSaleDownBelongA2" resultType="java.lang.Integer">
        SELECT  TW.TRADER_ID FROM T_CUSTOMER_REGION_SALE TS
        JOIN T_TRADER_WORKAREA TW ON TS.REGION_ID  = TW.AREA_ID
        JOIN T_R_TRADER_J_USER TRJ ON TRJ.TRADER_ID  = TW.TRADER_ID  AND TRJ.USER_ID = TS.USER_ID_DOWN  and TRJ.TRADER_TYPE =1
        WHERE TW.TRADER_ID=#{traderId,jdbcType=INTEGER} and TS.USER_ID IN
        <foreach collection="userIds" item="userId" index="index"
                 open="(" close=")" separator=",">
            #{userId}
        </foreach>
    </select>

    <select id="selectBelongTraderByAll" resultType="java.lang.Integer">
        SELECT  TRJ.TRADER_ID
        FROM T_R_TRADER_J_USER TRJ
        WHERE TRJ.TRADER_ID=#{traderId,jdbcType=INTEGER} and TRJ.USER_ID IN
        <foreach collection="userIds" item="userId" index="index"
                 open="(" close=")" separator=",">
            #{userId}
        </foreach>
    </select>




    <select id="getTraderWorkAreaByCustomerName" resultType="java.lang.Integer">
        SELECT ttw .AREA_ID FROM T_TRADER tt
                                     JOIN T_TRADER_CUSTOMER ttc  ON tt.TRADER_ID =ttc .TRADER_ID
                                     JOIN T_TRADER_WORKAREA ttw  ON tt .TRADER_ID = ttw .TRADER_ID
        WHERE
            tt.TRADER_NAME=#{customerName,jdbcType=VARCHAR} AND
            ttw .AREA_ID  IS NOT NULL
        limit 1
    </select>


    <select id="getCustomerRegionSale" resultType="java.lang.String">


        SELECT DATE_FORMAT(FROM_UNIXTIME(MOD_TIME/1000), '%Y-%m-%d %H:%i:%s') MOD_TIME  FROM T_CUSTOMER_REGION_SALE WHERE REGION_ID = #{regionId,jdbcType=INTEGER}
               AND (
                USER_ID = #{userId,jdbcType=INTEGER}
                OR
                USER_ID_DOWN = #{userId,jdbcType=INTEGER}

            )

    </select>






    <select id="searchTraderCustomerListPageForYingji"  resultType="com.vedeng.mobile.system.dto.TraderCustomerSearchResultDto">
        SELECT A.TRADER_CUSTOMER_ID, A.TRADER_ID,A.CUSTOMER_NATURE, B.TRADER_NAME,W.AREA_ID,
        C.USER_ID,D.TZ_CUSTOMER,D.TZ_CUSTOMER_LEVEL
        FROM T_TRADER_CUSTOMER A
        LEFT JOIN T_TRADER B ON A.TRADER_ID = B.TRADER_ID
        LEFT JOIN T_R_TRADER_J_USER C ON C.TRADER_ID = A.TRADER_ID
        LEFT JOIN DWH_TRADER_LIST_FILTER_ERP D ON D.TRADER_ID = A.TRADER_ID AND A.TRADER_CUSTOMER_ID = D.TRADER_CUSTOMER_ID
        LEFT JOIN T_TRADER_WORKAREA W ON W.TRADER_ID = B.TRADER_ID
        WHERE  B.TRADER_NAME LIKE CONCAT('%',#{traderCustomerVo.searchTraderName},'%' )
        AND B.COMPANY_ID = #{traderCustomerVo.companyId}
        <if test="traderCustomerVo.isEnable != null" >
            AND A.IS_ENABLE = #{traderCustomerVo.isEnable,jdbcType=BIT}
        </if>
        <if test="traderCustomerVo.userIdList != null and traderCustomerVo.userIdList.size != 0">
            AND
            (
                A.TRADER_ID in (
                select TRADER_ID from T_R_TRADER_J_USER where TRADER_TYPE =1
                and USER_ID in
                <foreach collection="traderCustomerVo.userIdList" item="userId" index="index"
                         open="(" close=")" separator=",">
                    #{userId}
                </foreach>
                )
            OR  A.TRADER_ID in (
                select TRADER_ID from T_R_SALES_J_TRADER where
                SALE_USER_ID in
                <foreach collection="traderCustomerVo.userIdList" item="userId" index="index"
                         open="(" close=")" separator=",">
                    #{userId}
                </foreach>
                )
            )
        </if>
    </select>

    <select id="searchTraderCustomerListPageForFeigong"  resultType="com.vedeng.mobile.system.dto.TraderCustomerSearchResultDto">
        SELECT A.TRADER_CUSTOMER_ID, A.TRADER_ID,A.CUSTOMER_NATURE, B.TRADER_NAME,W.AREA_ID,
        C.USER_ID,D.TZ_CUSTOMER,D.TZ_CUSTOMER_LEVEL
        FROM T_TRADER_CUSTOMER A
        LEFT JOIN T_TRADER B ON A.TRADER_ID = B.TRADER_ID
        LEFT JOIN T_R_TRADER_J_USER C ON C.TRADER_ID = A.TRADER_ID
        LEFT JOIN DWH_TRADER_LIST_FILTER_ERP D ON D.TRADER_ID = A.TRADER_ID AND A.TRADER_CUSTOMER_ID = D.TRADER_CUSTOMER_ID
        LEFT JOIN T_TRADER_WORKAREA W ON W.TRADER_ID = B.TRADER_ID
        WHERE  B.TRADER_NAME LIKE CONCAT('%',#{traderCustomerVo.searchTraderName},'%' )
        AND B.COMPANY_ID = #{traderCustomerVo.companyId}
        <if test="traderCustomerVo.isEnable != null" >
            AND A.IS_ENABLE = #{traderCustomerVo.isEnable,jdbcType=BIT}
        </if>
    </select>


    <select id="getTraderWorkAreaByTraderId" resultType="java.lang.Integer">
        SELECT AREA_ID  FROM T_TRADER_WORKAREA WHERE TRADER_ID = #{traderId,jdbcType=INTEGER}
        limit 1
    </select>
    <update id="updateTraderWorkAreaByTraderId" >
        update T_TRADER_WORKAREA
            set AREA_ID = #{areaId,jdbcType=INTEGER},AREA_NAME_ALL=#{areaNameFull,jdbcType=VARCHAR},MOD_TIME = NOW()
        WHERE TRADER_ID = #{traderId,jdbcType=INTEGER}
    </update>

    <update id="insertTraderWorkAreaByTraderId" >
        INSERT  INTO T_TRADER_WORKAREA(TRADER_ID,AREA_ID,AREA_NAME_ALL,ADD_TIME,MOD_TIME)
            VALUES (#{traderId,jdbcType=INTEGER},#{areaId,jdbcType=INTEGER},#{areaNameFull,jdbcType=VARCHAR},NOW(),NOW())
    </update>

</mapper>