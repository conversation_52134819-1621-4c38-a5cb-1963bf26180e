<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>

<link rel="stylesheet" href="${pageContext.request.contextPath}/static/vue/tailwind/tailwind.min.css">

<div id="app" class="p-4">
    <el-form :model="form" :rules="rules" ref="form" label-width="120px" class="space-y-4">
        <el-row :gutter="20">
            <el-col :span="12">
                <el-form-item label="交易主体:" prop="traderSubject" class="mb-0">
                    <el-radio-group v-model="form.traderSubject" @change="handleTransactionChange"
                                    class="space-x-4">
                        <el-radio :label="1">对公</el-radio>
                        <el-radio :label="2">对私</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="交易方式:" prop="traderMode" class="mb-0">
                    <el-radio-group v-model="form.traderMode" @change="handleTransactionMethodChange"
                                    class="space-x-4" :disabled="isAlipayDisabled">
                        <el-radio :label="521">银行</el-radio>
                        <el-radio :label="520">支付宝</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-col>
        </el-row>

        <el-row :gutter="20">
            <el-col :span="12">
                <el-form-item label="申请金额:" prop="payAmount" class="mb-0">
                    <div class="flex items-center space-x-4">
                        <el-input
                                v-model="form.payAmount"
                                class="w-32"
                                :maxlength="13"
                                :value-format="'decimal'"
                        ></el-input>
                        <div class="flex items-center space-x-2 text-gray-400">
                            <span class="font-semibold">退款金额:</span>
                            <span>{{ refundAmount }}</span>
                        </div>
                        <div class="flex items-center space-x-2 text-gray-400">
                            <span class="font-semibold">已付金额:</span>
                            <span>{{ paidAmount }}</span>
                        </div>
                    </div>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="内部备注:" class="mb-0">
                    <el-input v-model="form.comment" style="width: 300px;" maxlength="100"></el-input>
                </el-form-item>
            </el-col>
        </el-row>


        <el-form-item label="账户信息:" prop="accountSelection" class="mb-0" v-if="!isAlipayDisabled">
            <el-col :span="21">
                <el-radio-group v-model="form.accountSelection" @change="handleAccountSelectionChange"
                                class="space-x-4">
                    <el-radio label="select">选择账号</el-radio>
                    <el-radio label="fill">填写账号</el-radio>
                </el-radio-group>
            </el-col>
            <el-col :span="2" class="ml-16">
                <el-button type="primary" plain size="mini" @click="refreshAccount">重新计算银行</el-button>
            </el-col>
        </el-form-item>

        <div class="w-full mb-0" v-if="!isAlipayDisabled">
            <el-table v-show="form.accountSelection === 'select'" :data="accounts" style="width: 95%"
                      highlight-current-row border
                      class="ml-10 mt-2"
                      size="mini"
            >

                <el-table-column label="选择" width="55">
                    <template slot-scope="scope">
                        <el-radio :value="selectedAccountIndex" :label="scope.$index"
                                  size="medium"
                                  @change="handleRadioChange(scope.row)">&nbsp;
                        </el-radio>
                    </template>
                </el-table-column>

                <el-table-column prop="tag" label="标签" width="120">
                    <template slot="header" slot-scope="scope">
                        <span>
                            标签
                            <el-tooltip placement="top">
                                <div slot="content">
                                    直接收款账号：关联销售订单客户使用的付款账号
                                    <br>
                                    历史收款账号：历史该客户付款使用过的账号
                                </div>
                                <i class="el-icon-question" style="margin-left: 5px;"></i>
                            </el-tooltip>
                        </span>
                    </template>
                    <template slot-scope="scope">
                        <el-tag v-if="scope.row.tag === 0" type="success">直接收款账户</el-tag>
                        <el-tag v-else-if="scope.row.tag === 1" type="info">历史收款账户</el-tag>
                    </template>
                </el-table-column>
                <el-table-column :label="accountNoLabel" prop="accountNo" width="200"></el-table-column>
                <el-table-column prop="accountName" label="收款名称"></el-table-column>
                <el-table-column prop="receiptBankName" label="回单银行名称">
                    <template slot="header" slot-scope="scope">
                        <span>
                            回单银行名称
                            <el-tooltip placement="top">
                                <div slot="content">
                                    收款当日退款无回单文件
                                </div>
                                <i class="el-icon-question" style="margin-left: 5px;"></i>
                            </el-tooltip>
                        </span>
                    </template>
                </el-table-column>


                <!-- 根据交易方式条件渲染开户银行列 -->
                <el-table-column v-if="form.traderMode !== 520" label="开户银行">
                    <template slot="header">
                        <span><font color="red">* </font>开户银行</span>
                    </template>
                    <template slot-scope="scope">
                        <el-form-item prop="openingBank"
                                      :rules="rules['selectOpeningBank'+scope.$index]"
                                      label-width="0px">
                            <el-select v-model="scope.row.openingBank" filterable remote clearable
                                       :remote-method="fetchBanks"
                                       :loading="loadingBanks"
                                       placeholder="选择开户银行"
                                       :disabled="!scope.row.bankSelectionEnabled"
                                       class="w-full"
                                       @change="() => fetchBankCode(scope.row)"
                                       @clear="() => clearBankCode(scope.row)">
                                <el-option
                                        v-for="item in bankOptions"
                                        :key="item.bankId"
                                        :label="item.bankName"
                                        :value="item.bankId">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </template>
                </el-table-column>

                <!-- 当交易方式为支付宝时，显示空白元素 -->
                <el-table-column v-else label="开户银行">
                    <template slot-scope="scope">
                        <div></div>
                    </template>
                </el-table-column>

                <el-table-column label="联行号" width="160">
                    <template slot-scope="scope">
                        <div>
                            {{ scope.row.bankCode }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="isVerify" label="验证状态" width="80">
                    <template slot-scope="scope">
                        {{ scope.row.isVerify === 0 ? '未验证' : '已验证' }}
                    </template>
                </el-table-column>
            </el-table>

            <el-table v-show="form.accountSelection === 'fill'" :data="form.manualAccount" ref="fillTable"
                      style="width: 95%"
                      border
                      size="mini"
                      class="ml-10 mt-2">
                <el-table-column :label="accountNoLabel">
                    <template slot="header" slot-scope="scope">
                        <span><font color="red">* </font> {{accountNoLabel}}</span>
                    </template>
                    <template slot-scope="scope">
                        <el-form-item :prop="'manualAccount.' + scope.$index + '.accountNumber'"
                                      :rules="rules.accountNumber" label-width="0px">
                            <el-input
                                    v-model="scope.row.accountNumber"
                                    class="w-full"
                                    :maxlength="100"
                                    @blur="validateBankAccount"
                            ></el-input>
                        </el-form-item>
                    </template>
                </el-table-column>
                <el-table-column label="收款名称">
                    <template slot="header">
                        <span><font color="red">* </font>收款名称</span>
                    </template>
                    <template slot-scope="scope">
                        <el-form-item :prop="'manualAccount.' + scope.$index + '.recipientName'"
                                      :rules="rules.recipientName" label-width="0px">
                            <el-input v-model="scope.row.recipientName" class="w-full" maxlength="100"></el-input>
                        </el-form-item>
                    </template>
                </el-table-column>

                <!-- 根据交易方式条件渲染开户银行列 -->
                <el-table-column v-if="form.traderMode !== 520" label="开户银行">
                    <template slot="header">
                        <span><font color="red">* </font>开户银行</span>
                    </template>
                    <template slot-scope="scope">
                        <el-form-item :prop="'manualAccount.' + scope.$index + '.openingBank'"
                                      :rules="rules.fillOpeningBank" label-width="0px">
                            <el-select v-model="scope.row.openingBank" filterable remote clearable
                                       :remote-method="fetchBanks"
                                       :loading="loadingBanks"
                                       placeholder="选择开户银行"
                                       :disabled="!scope.row.bankSelectionEnabled"
                                       class="w-full"
                                       @change="() => fetchBankCode(scope.row)"
                                       @clear="() => clearBankCode(scope.row)">

                                <el-option
                                        v-for="item in bankOptions"
                                        :key="item.bankId"
                                        :label="item.bankName"
                                        :value="item.bankId">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </template>
                </el-table-column>

                <!-- 当交易方式为支付宝时，显示空白元素 -->
                <el-table-column v-else label="开户银行">
                    <template slot-scope="scope">
                        <div></div>
                    </template>
                </el-table-column>

                <el-table-column label="联行号">
                    <template slot-scope="scope">
                        <div>
                            {{ scope.row.bankCode }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="isVerify" label="验证状态" width="80">
                    <template slot-scope="scope">
                        {{ scope.row.isVerify === 0 ? '未验证' : '已验证' }}
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <div class="flex justify-center mt-2 text-xm p-24" v-if="isAlipayDisabled">
            满足第三方支付（微信，支付宝）自动退款，款项将原路退回，无需选择账号
        </div>

    </el-form>

    <div class="flex justify-center mt-2">
        <el-button type="primary" @click="submitForm" :loading="loading">提交</el-button>
        <el-button @click="closeForm">取消</el-button>
    </div>
</div>

<script src="${pageContext.request.contextPath}/static/api/afterSalesOrder/afterSalesOrder.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/api/system/bank.js?rnd=${resourceVersionKey}"></script>

<script>
    let afterSalesId = ${afterSalesId};

    new Vue({
        el: '#app',
        data() {
            return {
                loading: false, // 添加 loading 状态
                form: {
                    traderSubject: 1, // 默认设置为对公
                    traderMode: 521, // 默认设置为银行
                    payAmount: 0, // 初始申请金额
                    comment: '',
                    accountSelection: 'select',
                    selectedAccount: null,
                    manualAccount: [{
                        accountNumber: '',
                        recipientName: '',
                        openingBank: '',
                        bankCode: '',
                        bank: '',
                        isVerify: 0
                    }]
                },
                refundAmount: 0,
                paidAmount: 0,
                accounts: [],
                bankOptions: [],
                loadingBanks: false,
                accountNoLabel: '银行账号',
                selectedAccountIndex: null,
                isAlipayDisabled: false, // 添加 isAlipayDisabled 属性
                rules: {
                    traderSubject: [
                        {required: true, message: '请选择交易主体', trigger: 'change'}
                    ],
                    traderMode: [
                        {required: true, message: '请选择交易方式', trigger: 'change'}
                    ],
                    payAmount: [
                        {required: true, message: '请输入申请金额', trigger: 'blur'},
                        {validator: this.validatePayAmount, trigger: 'blur'}
                    ],
                    accountSelection: [
                        {required: true, message: '请选择账户信息', trigger: 'change'}
                    ],
                }
            }
        },
        created() {
            this.fetchTransactionData();
            if (this.form.accountSelection === 'select') {
                this.fetchCustomerBankAccounts(this.form.traderMode, 'select');
            }
            this.calculateRefundableAmount(); // 计算可退金额
            this.updateValidationRules();
            this.refundableToOrigin();
        },
        methods: {

            refundableToOrigin() {
                isRefundableToOrigin({afterSalesId: afterSalesId}).then(response => {
                    if (response.data.data) {
                        // 满足条件，禁用支付宝选项并显示特定文本
                        this.isAlipayDisabled = true; // 禁用支付宝选项
                        this.form.traderMode = 520; // 设置交易方式为支付宝
                        this.form.accountSelection = 'select'; // 设置账户选择为选择账号
                        this.form.selectedAccount = {
                            accountName: '支付宝',
                            accountNo: '***********'
                        };
                        this.form.manualAccount = []; // 清空手动填写账号
                        this.accounts = []; // 清空账户列表
                        this.accountNoLabel = '支付宝账号'; // 更新账号标签
                        this.updateValidationRules(); // 更新验证规则
                    }
                }).catch(error => {
                    console.error('判断是否可以原路退回失败:', error);
                })
            },

            fetchAllBankOptions() {
                this.accounts.forEach(account => {
                    if (account.accountType === 1) {
                        // 检查 bankOptions 中是否已经存在相同的 bankId
                        const existingBank = this.bankOptions.find(option => option.bankId === account.bankId);
                        if (!existingBank) {
                            // 如果不存在，则 push 新的银行选项
                            this.bankOptions.push({
                                bankId: account.bankId,
                                bankName: account.bankName,
                                bankNo: account.bankCode
                            });
                        }
                    }
                });
                console.log(this.bankOptions);
            },
            calculateRefundableAmount() {
                this.form.payAmount = parseFloat(this.refundAmount - this.paidAmount).toFixed(2);
            },
            fetchTransactionData() {
                afterSaleRefundInfo({afterSalesId: afterSalesId})
                    .then(response => {
                        const refundDetails = response.data.data.refundDetailDtoList;
                        const entityType = this.form.traderSubject;
                        const relevantDetails = refundDetails.find(detail => detail.traderSubject === entityType);
                        if (relevantDetails) {
                            this.refundAmount = parseFloat(relevantDetails.refundAmount).toFixed(2);
                            this.paidAmount = parseFloat(relevantDetails.paidAmount).toFixed(2);
                            this.calculateRefundableAmount(); // 设置初始值
                        }
                    })
                    .catch(error => {
                        console.error('获取交易数据失败:', error);
                    });
            },
            fetchCustomerBankAccounts(accountType, type) {
                console.log(accountType, type)
                if (accountType == 521) {
                    accountType = 1
                }
                if (accountType == 520) {
                    accountType = 3
                }
                getCustomerBankAccount({afterSalesId: afterSalesId, accountType: accountType})
                    .then(response => {
                        this.accounts = response.data.data;
                        if (type === 'select') {
                            this.enableBankSelectionForSelectedRow();
                        }
                        if (this.form.accountSelection === 'select') {
                            this.fetchAllBankOptions();
                        } else {
                            this.bankOptions = [];
                        }
                    })
                    .catch(error => {
                        console.error('获取账户信息失败:', error);
                    });
            },

            fetchBanks(query) {
                if (query !== '') {
                    this.loadingBanks = true;
                    getBankByKeywords({keywords: query})
                        .then(response => {
                            this.bankOptions = response.data.data;
                            this.loadingBanks = false;
                        })
                        .catch(error => {
                            console.error('获取银行信息失败:', error);
                            this.loadingBanks = false;
                        });
                } else {
                    this.bankOptions = [];
                }
            },

            fetchBankCode(row) {
                console.log(row)
                const selectedBank = this.bankOptions.find(bank => bank.bankId === row.openingBank);
                console.log(selectedBank)
                if (selectedBank) {
                    this.$set(row, 'bankCode', selectedBank.bankNo);
                    this.$set(row, 'bankId', selectedBank.bankId);
                    this.$set(row, 'bank', selectedBank.bankName);
                    this.$set(row, 'bankName', selectedBank.bankName);
                } else {
                    this.$set(row, 'bankCode', '');
                    this.$set(row, 'bankId', '');
                    this.$set(row, 'bank', '');
                    this.$set(row, 'bankName', '');
                }
            },

            clearBankCode(row) {
                this.$set(row, 'openingBank', '');
                this.$set(row, 'bankCode', '');
                this.$set(row, 'bank', '');
            },

            handleTransactionChange() {
                this.fetchTransactionData();
                this.$refs.form.clearValidate('payAmount');
            },

            handleTransactionMethodChange() {
                if (this.isAlipayDisabled) {
                    return; // 如果支付宝被禁用，则不执行后续操作
                }

                this.updateValidationRules();

                this.selectedAccountIndex = null
                if (this.form.traderMode === 521) {
                    this.accountNoLabel = '银行账号';
                    this.fetchCustomerBankAccounts(1);
                    this.form.manualAccount.forEach(row => {
                        row.openingBank = ''
                        row.bankCode = ''
                        row.bankSelectionEnabled = true
                    });
                    this.accounts.forEach(account => {
                        account.openingBank = '';
                        account.bankCode = ''
                    });
                    this.form.selectedAccount = null
                }

                if (this.form.traderMode === 520) {
                    this.accountNoLabel = '支付宝账号';
                    this.fetchCustomerBankAccounts(3);
                    this.form.manualAccount.forEach(row => {
                        row.openingBank = '';
                        row.bankCode = ''
                    });
                    this.accounts.forEach(account => {
                        account.openingBank = '';
                        account.bankCode = ''
                    });
                    this.form.selectedAccount = null
                }

            },

            handleAccountSelectionChange(selection) {
                this.updateValidationRules();

                if (selection === 'select') {
                    this.fetchCustomerBankAccounts(this.form.traderMode, 'select');
                }
                this.handleTransactionMethodChange();


            },

            handleRadioChange(row, index) {
                this.rules['selectOpeningBank' + this.selectedAccountIndex] = [];
                this.selectedAccountIndex = this.accounts.indexOf(row);
                this.updateValidationRules();
                this.form.selectedAccount = row;
                this.enableBankSelectionForSelectedRow();
            },


            validateBankAccount() {
                const accountNo = this.form.manualAccount[0].accountNumber;
                if (accountNo) {
                    let accountType;
                    if (this.form.traderMode == 521) {
                        accountType = 1
                    }
                    if (this.form.traderMode == 520) {
                        accountType = 3
                    }

                    getCustomerBankAccountByBankAccount({
                        accountNo: accountNo,
                        accountType: accountType,
                        afterSalesId: afterSalesId
                    }).then(response => {
                        const accountInfo = response.data.data;
                        if (accountInfo && accountInfo.length > 0) {
                            // Update last use time
                            updateLastUseTime({customerBankAccountId: accountInfo[0].customerBankAccountId})
                                .then(() => {
                                    this.$message.error('该账号存在于客户账户中，请至 "选择账户" 中选择');
                                    this.form.manualAccount[0].accountNumber = '';
                                    this.loading = false;
                                })
                                .catch(error => {
                                    console.error('更新最近使用时间失败:', error);
                                    this.loading = false;
                                });
                        }
                    })
                        .catch(error => {
                            console.error('查询客户账户信息失败:', error);
                            this.loading = false;
                        });
                }
            },

            validatePayAmount(rule, value, callback) {
                const maxAmount = this.refundAmount - this.paidAmount;
                if (parseFloat(value) <= 0) {
                    callback(new Error('退款金额必须大于0'));
                } else if (parseFloat(value) > maxAmount) {
                    callback(new Error(`退款金额不能大于` + maxAmount.toFixed(2)));
                }
                const regex = /^\d{1,10}(\.\d{0,2})?$/;
                if (!regex.test(value)) {
                    callback(new Error(`退款金额最多10位整数和最多2位小数`));
                }
                callback();
            },


            enableBankSelectionForSelectedRow() {
                // 禁用所有行的开户银行选择
                this.accounts.forEach(account => {
                    account.bankSelectionEnabled = false;
                });

                // 启用当前选中行的开户银行选择
                if (this.selectedAccountIndex !== null && this.accounts[this.selectedAccountIndex].isVerify == 0) {
                    this.accounts[this.selectedAccountIndex].bankSelectionEnabled = true;
                    this.$forceUpdate();
                }
            },


            updateValidationRules() {
                // this.$refs.form.clearValidate('openingBank');

                if (this.form.accountSelection === 'select') {
                    this.rules.accountNumber = [];
                    this.rules.recipientName = [];
                    this.rules.fillOpeningBank = [];
                    if (this.selectedAccountIndex || this.selectedAccountIndex === 0) {
                        this.rules['selectOpeningBank' + this.selectedAccountIndex] = [
                            {
                                validator: (rule, value, callback) => {
                                    if (!this.form.selectedAccount) {
                                        return
                                    }
                                    let openingBank = this.form.selectedAccount.openingBank
                                    if (this.selectedAccountIndex !== null && !openingBank) {
                                        callback(new Error('请选择开户银行'));
                                    } else {
                                        callback();
                                    }
                                },
                                trigger: 'change'
                            }
                        ];
                    }
                }

                if (this.form.accountSelection === 'fill') {

                    this.rules['selectOpeningBank' + this.selectedAccountIndex] = [];

                    this.rules.accountNumber = [
                        {
                            validator: function (rule, value, callback) {
                                if (!value || value.trim() === '') {
                                    callback(new Error("请填写账号"));
                                } else {
                                    callback();
                                }
                            },
                            trigger: 'blur'
                        }
                    ];
                    this.rules.recipientName = [
                        {
                            validator: function (rule, value, callback) {
                                if (!value || value.trim() === '') {
                                    callback(new Error("请填写收款名称"));
                                } else {
                                    callback();
                                }
                            },
                            trigger: 'blur'
                        }
                    ];

                    this.rules.fillOpeningBank = [
                        {
                            validator: function (rule, value, callback) {
                                if (!value) {
                                    callback(new Error("请选择开户银行"));
                                } else {
                                    callback();
                                }
                            },
                            trigger: 'change'
                        }
                    ];
                }
            },

            refreshAccount() {
                axios({
                    url: '/customer/account/refresh.do',
                    method: 'post',
                    params: {
                        afterSalesId: afterSalesId,
                    }
                })
                    .then(response => {
                        if (response.data.code === 0) {
                            this.fetchCustomerBankAccounts(this.form.traderMode, 'select');
                        }
                    })
                    .catch(error => {
                        console.error('刷新账户信息失败:', error);
                    });
            },


            closeForm() {
                this.closeThis()
            },

            closeThis() {
                parent.layer.closeAll();
                parent.window.location.reload();
            },

            submitForm() {
                this.loading = true;
                const validateForm = (callback) => {
                    this.$refs.form.validate((valid) => {
                        if (valid) {
                            callback();
                        } else {
                            this.loading = false;
                        }
                    });
                };

                const handleResponse = (response) => {
                    this.loading = false;
                    if (response.data.code === 0) {
                        this.$message({
                            message: '新增付款成功',
                            type: 'success',
                            duration: 1000,
                            onClose: () => {
                                this.closeThis();
                                this.loading = false;
                            }
                        });
                    } else {
                        this.$message({
                            message: response.data.message,
                            type: 'error',
                            onClose: () => {
                                this.loading = false;
                            }
                        });
                    }
                };

                const handleError = (error) => {
                    this.loading = false;
                    console.error('提交表单失败:', error);
                };

                const createRequest = (account) => ({
                    traderMode: this.form.traderMode,
                    traderSubject: this.form.traderSubject,
                    afterSalesId: afterSalesId,
                    payAmount: this.form.payAmount,
                    traderName: account.traderName,
                    bankAccount: account.bankAccount,
                    bankCode: account.bankCode,
                    bank: account.bank,
                    bankId: account.bankId,
                    bankReceiptAliasId: account.bankReceiptAliasId,
                    comment: this.form.comment
                });

                if (this.form.accountSelection === 'select') {
                    const selectedAccount = this.form.selectedAccount;
                    if (!selectedAccount) {
                        this.$message({
                            message: '请选择一个账户',
                            type: 'warning'
                        });
                        this.loading = false;
                        return;
                    }

                    validateForm(() => {
                        const request = createRequest({
                            traderName: selectedAccount.accountName,
                            bankAccount: selectedAccount.accountNo,
                            bankCode: selectedAccount.bankCode,
                            bank: selectedAccount.bankName,
                            bankId: selectedAccount.bankId,
                            bankReceiptAliasId: selectedAccount.bankReceiptAliasId
                        });

                        createRefundApply(request)
                            .then(handleResponse)
                            .catch(handleError)
                            .finally(() => {
                                console.log('Request completed, setting loading to false');
                                this.loading = false;
                            });
                    });
                }

                if (this.form.accountSelection === 'fill') {
                    validateForm(() => {
                        const manualAccount = this.form.manualAccount[0];
                        const request = createRequest({
                            traderName: manualAccount.recipientName,
                            bankAccount: manualAccount.accountNumber,
                            bankCode: manualAccount.bankCode,
                            bank: manualAccount.bank,
                            bankId: manualAccount.bankId
                        });

                        createRefundApply(request)
                            .then(handleResponse)
                            .catch(handleError)
                            .finally(() => {
                                console.log('Request completed, setting loading to false');
                                this.loading = false;
                            });
                    });
                }
            }
        }
    })
    ;
</script>

<style>
    .el-table__cell .cell .el-form-item {
        margin-bottom: 15px;
    }

    start {
        content: "*";
        color: #F56C6C;
        margin-right: 4px;
    }
</style>