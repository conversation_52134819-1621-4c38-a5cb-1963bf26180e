<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.CustomerRegionSaleRulesMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.CustomerRegionSaleRules">
    <id column="PUBLIC_CUSTOMER_REGION_RULES_ID" jdbcType="INTEGER" property="publicCustomerRegionRulesId" />
    <result column="REGION_ID" jdbcType="INTEGER" property="regionId" />
    <result column="USER_ID" jdbcType="INTEGER" property="userId" />
    <result column="USER_ID_DOWN" jdbcType="INTEGER" property="userIdDown" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
  </resultMap>


  <sql id="Base_Column_List">
    PUBLIC_CUSTOMER_REGION_RULES_ID, REGION_ID, USER_ID, USER_ID_DOWN, ADD_TIME, CREATOR, MOD_TIME,
    UPDATER
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_CUSTOMER_REGION_SALE
    where PUBLIC_CUSTOMER_REGION_RULES_ID = #{publicCustomerRegionRulesId,jdbcType=INTEGER}
  </select>
  <select id="selectByRegionId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_CUSTOMER_REGION_SALE
    where REGION_ID = #{regionId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_CUSTOMER_REGION_SALE
    where PUBLIC_CUSTOMER_REGION_RULES_ID = #{publicCustomerRegionRulesId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="PUBLIC_CUSTOMER_REGION_RULES_ID" keyProperty="publicCustomerRegionRulesId" parameterType="com.vedeng.erp.trader.domain.PublicCustomerRegionRules" useGeneratedKeys="true">
    insert into T_CUSTOMER_REGION_SALE (REGION_ID, USER_ID,USER_ID_DOWN, ADD_TIME,
      CREATOR, MOD_TIME, UPDATER
      )
    values (#{regionId,jdbcType=INTEGER}, #{userId,jdbcType=INTEGER}, #{userIdDown,jdbcType=INTEGER}, #{addTime,jdbcType=BIGINT},
      #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}
      )
  </insert>


  <select id="selectRegionIdExist" parameterType="com.vedeng.erp.trader.dto.CustomerRegionSaleRulesApiDto" resultType="java.lang.Integer">
    select
    REGION_ID
    from T_CUSTOMER_REGION_SALE
    where REGION_ID IN
      <foreach collection="areaIds" item="it" separator="," open="(" close=")">
      #{it}
      </foreach>
  </select>

  <update id="updateByRegionIds" parameterType="com.vedeng.erp.trader.dto.CustomerRegionSaleRulesApiDto" >
    UPDATE T_CUSTOMER_REGION_SALE
    SET
    USER_ID= #{userIdOn,jdbcType=INTEGER},
    USER_ID_DOWN=#{userIdDown,jdbcType=INTEGER},
    MOD_TIME =   UNIX_TIMESTAMP(NOW(3)) * 1000 ,
    UPDATER = #{userId,jdbcType=INTEGER}
    where REGION_ID IN
    <foreach collection="areaIds" item="it" separator="," open="(" close=")">
      #{it}
    </foreach>
  </update>




  <insert id="insertSelective" keyColumn="PUBLIC_CUSTOMER_REGION_RULES_ID" keyProperty="publicCustomerRegionRulesId" parameterType="com.vedeng.erp.trader.domain.PublicCustomerRegionRules" useGeneratedKeys="true">
    insert into T_CUSTOMER_REGION_SALE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="regionId != null">
        REGION_ID,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="userIdDown != null">
        USER_ID_DOWN,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="regionId != null">
        #{regionId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="userIdDown != null">
        #{userIdDown,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <insert id="batchAddPublicCustomerCalculateRules" parameterType="com.vedeng.erp.trader.domain.PublicCustomerRegionRules" >
    insert into T_CUSTOMER_REGION_SALE (REGION_ID,USER_ID,USER_ID_DOWN,ADD_TIME,CREATOR,MOD_TIME,UPDATER) values
    <foreach collection="list" item="i" separator=",">
      (#{i.regionId},#{i.userId},#{i.userIdDown},#{i.addTime},#{i.creator},#{i.modTime},#{i.updater})
    </foreach>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.PublicCustomerRegionRules">
    update T_CUSTOMER_REGION_SALE
    <set>
      <if test="regionId != null">
        REGION_ID = #{regionId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        USER_ID = #{userId,jdbcType=INTEGER},
      </if>
      <if test="userIdDown != null">
        USER_ID = #{userIdDown,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where PUBLIC_CUSTOMER_REGION_RULES_ID = #{publicCustomerRegionRulesId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.PublicCustomerRegionRules">
    update T_CUSTOMER_REGION_SALE
    set REGION_ID = #{regionId,jdbcType=INTEGER},
      USER_ID = #{userId,jdbcType=INTEGER},
      USER_ID_DOWN = #{userIdDown,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER}
    where PUBLIC_CUSTOMER_REGION_RULES_ID = #{publicCustomerRegionRulesId,jdbcType=INTEGER}
  </update>

  <delete id="batchDeleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_CUSTOMER_REGION_SALE
    where PUBLIC_CUSTOMER_REGION_RULES_ID in
    <foreach collection="list" item="it" separator="," open="(" close=")">
      #{it}
    </foreach>
  </delete>

</mapper>