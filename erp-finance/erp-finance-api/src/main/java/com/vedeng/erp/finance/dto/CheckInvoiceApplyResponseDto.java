package com.vedeng.erp.finance.dto;

import com.vedeng.erp.trader.dto.TraderFinanceDto;
import lombok.Data;

@Data
public class CheckInvoiceApplyResponseDto {

    /**
     * 财务信息
     */
    private TraderFinanceDto traderFinanceDto;

    /**
     * 是否专票
     */
    private boolean isSpecialInvoice;

    /**
     * 是否需要二次确认：资质校验是否通过
     */
    private boolean isPassCredential;

    /**
     * 是否需要二次确认：符合票货同行
     */
    private boolean isPassPeerCheck;

    /**
     * 是否需要二次确认：申请检查是否通过
     */
    private boolean isPassRuleCheck;

}
