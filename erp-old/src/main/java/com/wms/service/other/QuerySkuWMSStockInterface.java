package com.wms.service.other;

import com.google.gson.Gson;
import com.wms.constant.WMSContant;
import com.wms.dto.WmsRequest;
import com.wms.dto.WmsStockRequest;
import com.wms.dto.WmsStockResponse;
import com.wms.service.AbstractWmsInterface;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName QuerySkuWMSStockInterface.java
 * @Description TODO 查询WMS库存接口
 * @createTime 2020年08月11日 08:58:00
 */
@Service
public class QuerySkuWMSStockInterface  extends AbstractWmsInterface {
    @Override
    protected void setMethodAndMessageId(WmsRequest wmsRequest) {
        wmsRequest.setMethod(WMSContant.QUERT_WMS_SKUSTOCK);
        wmsRequest.setMessageId(WMSContant.QUERT_WMS_SKUSTOCK);
    }

    @Override
    protected JSONObject getXmlDate(Object... param) {
        if(param == null ||param.length == 0){
            return null;
        }
        WmsStockRequest wmsStockRequest = ( WmsStockRequest) param[0];

        JSONObject headItem = new JSONObject();

        headItem.put("sku",wmsStockRequest.getSku());
        headItem.put("lotAtt08",wmsStockRequest.getLotAtt08());
        headItem.put("pageSize",wmsStockRequest.getPageSize());
        headItem.put("pageNo",wmsStockRequest.getPageNo());

        JSONObject head = new JSONObject();
        head.put("header",headItem);

        JSONObject headObject = new JSONObject();
        headObject.put("data",head);

        return headObject;
    }

    @Override
    protected List<WmsStockResponse> parseResponseDate(JSONObject returnObject) {
        JSONObject items = (JSONObject) returnObject.get("items");
        if(items == null){return null;}
        JSONArray item = (JSONArray) items.get("item");
        if(item == null){return null;}
        List<WmsStockResponse> result = new ArrayList<>();
        Gson gson = new Gson();
        item.stream().forEach(jsonObj -> {
            result.add(gson.fromJson(jsonObj.toString(),WmsStockResponse.class));
        });
        return result;
    }
}
