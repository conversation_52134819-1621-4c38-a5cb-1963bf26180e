package com.vedeng.erp.trader.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
    * 特麦帮标记Dto
    */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SpecialSalesDto {
    /**
    * 主键ID
    */
    private Integer specialSalesId;

    /**
    * 关联表主键ID
    */
    private Integer relateId;

    /**
    * 关联表单号
    */
    private String relateNo;

    /**
    * 关联类型（1-商机 2-报价 3-销售订单 4-采购订单 5-采购费用订单）
    */
    private Integer relateType;

    /**
    * 是否删除（0未删除 1已删除）
    */
    private Integer isDelete;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 修改人
     */
    private Integer updater;

    /**
     * 添加人名称
     */
    private String creatorName;

    /**
     * 更新人名称
     */
    private String updaterName;

}