<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.kpi.dao.KpiOrderLogMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.kpi.model.DO.KpiOrderLogDo">
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="USER_ID" jdbcType="INTEGER" property="userId" />
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
    <result column="TEAM_ID" jdbcType="INTEGER" property="teamId" />
    <result column="GROUP_ID" jdbcType="INTEGER" property="groupId" />
    <result column="OPERATION" jdbcType="INTEGER" property="operation" />
    <result column="KPI_DATE" jdbcType="DATE" property="kpiDate" />
    <result column="ORDER_ID" jdbcType="INTEGER" property="orderId" />
    <result column="ORDER_NO" jdbcType="VARCHAR" property="orderNo" />
    <result column="ORDER_TYPE" jdbcType="INTEGER" property="orderType" />
    <result column="PAYMENT_TYPE" jdbcType="INTEGER" property="paymentType" />
    <result column="PAYMENT_COMMENTS" jdbcType="VARCHAR" property="paymentComments" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName" />
    <result column="TRADER_TYPE" jdbcType="INTEGER" property="traderType" />
    <result column="TRADER_SUBJECT" jdbcType="INTEGER" property="traderSubject" />
    <result column="ROSS_MARGIN" jdbcType="DECIMAL" property="rossMargin" />
    <result column="IS_SPEC_ORDER" jdbcType="INTEGER" property="isSpecOrder" />
    <result column="AFTER_SALE_ID" jdbcType="INTEGER" property="afterSaleId" />
    <result column="AFTER_SALE_NO" jdbcType="VARCHAR" property="afterSaleNo" />
    <result column="AFTER_SALE_COMMENT" jdbcType="VARCHAR" property="afterSaleComment" />
    <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
    <result column="ORDER_REAL_AMOUNT" jdbcType="DECIMAL" property="orderRealAmount" />
    <result column="ORDER_SUM_AMOUNT" jdbcType="DECIMAL" property="orderSumAmount" />
    <result column="KPI_AMOUNT" jdbcType="DECIMAL" property="kpiAmount" />
    <result column="CHANCE_NO" jdbcType="VARCHAR" property="chanceNo" />
    <result column="CHANCE_ID" jdbcType="INTEGER" property="chanceId" />
    <result column="CHANCE_TYPE" jdbcType="INTEGER" property="chanceType" />
    <result column="CHANCE_SOURCE" jdbcType="INTEGER" property="chanceSource" />
    <result column="CHANCE_TRANS_FLAG" jdbcType="INTEGER" property="chanceTransFlag" />
    <result column="CHANCE_TRANS_DAYS" jdbcType="INTEGER" property="chanceTransDays" />
    <result column="COMMENT" jdbcType="VARCHAR" property="comment" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="IS_USED" jdbcType="INTEGER" property="isUsed" />
  </resultMap>
  <resultMap id="QueryResultMap" type="com.vedeng.kpi.model.DTO.KpiDataQueryDto">
    <result column="USER_ID" jdbcType="INTEGER" property="userId" />
    <result column="KPI_DATE_START" jdbcType="DATE" property="kpiDateStart" />
    <result column="KPI_DATE_END" jdbcType="DATE" property="kpiDateEnd" />
    <result column="CO_CUSTOMERS" jdbcType="INTEGER" property="coCustomers" />
    <result column="LOST_CUSTOMERS" jdbcType="INTEGER" property="lostCustomers" />
    <result column="CO_ORDER_NUM" jdbcType="INTEGER" property="coOrderNum" />
    <result column="IS_ALL_RETURN" jdbcType="INTEGER" property="isAllReturn" />
    <result column="RET_FLAG" jdbcType="INTEGER" property="retFlag" />
    <result column="RET_DATE" jdbcType="DATE" property="retDate" />
    <result column="CUST_NUM" jdbcType="INTEGER" property="custNum" />
    <result column="LAST_ORDER_TIME" jdbcType="INTEGER" property="lastOrderTime" />
    <result column="LAST_CHAT_TIME" jdbcType="INTEGER" property="lastChatTime" />
    <result column="VALID_TIME" jdbcType="INTEGER" property="validTime" />
    <result column="ORDER_PAID_AMOUNT" jdbcType="DECIMAL" property="orderPaidAmount" />
    <result column="SH_VALID_TIME" jdbcType="INTEGER" property="shValidTime" />
    <result column="TRANS_ADD_TIME" jdbcType="INTEGER" property="chanceAddTime" />
    <result column="ASSIGN_TIME" jdbcType="BIGINT" property="assignTime"/>
  </resultMap>
  <sql id="Base_Column_List">
    ID, USER_ID, COMPANY_ID, TEAM_ID, GROUP_ID, `OPERATION`, KPI_DATE, ORDER_ID, ORDER_NO, 
    ORDER_TYPE, PAYMENT_TYPE, PAYMENT_COMMENTS, TRADER_ID, TRADER_NAME, TRADER_TYPE, 
    TRADER_SUBJECT, ROSS_MARGIN, IS_SPEC_ORDER, AFTER_SALE_ID, AFTER_SALE_NO, AFTER_SALE_COMMENT, 
    AMOUNT, ORDER_REAL_AMOUNT, ORDER_SUM_AMOUNT, KPI_AMOUNT, CHANCE_NO, CHANCE_ID, CHANCE_TYPE, 
    CHANCE_SOURCE, CHANCE_TRANS_FLAG, CHANCE_TRANS_DAYS, `COMMENT`, ADD_TIME, UPDATE_TIME, 
    IS_USED
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_KPI_ORDER_LOG
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_KPI_ORDER_LOG
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.kpi.model.DO.KpiOrderLogDo" useGeneratedKeys="true">
    insert into T_KPI_ORDER_LOG (USER_ID, COMPANY_ID, TEAM_ID, 
      GROUP_ID, `OPERATION`, KPI_DATE, 
      ORDER_ID, ORDER_NO, ORDER_TYPE, 
      PAYMENT_TYPE, PAYMENT_COMMENTS, TRADER_ID, 
      TRADER_NAME, TRADER_TYPE, TRADER_SUBJECT, 
      ROSS_MARGIN, IS_SPEC_ORDER, AFTER_SALE_ID, 
      AFTER_SALE_NO, AFTER_SALE_COMMENT, AMOUNT, 
      ORDER_REAL_AMOUNT, ORDER_SUM_AMOUNT, KPI_AMOUNT, 
      CHANCE_NO, CHANCE_ID, CHANCE_TYPE, 
      CHANCE_SOURCE, CHANCE_TRANS_FLAG, CHANCE_TRANS_DAYS, 
      `COMMENT`, ADD_TIME, UPDATE_TIME, 
      IS_USED)
    values (#{userId,jdbcType=INTEGER}, #{companyId,jdbcType=INTEGER}, #{teamId,jdbcType=INTEGER}, 
      #{groupId,jdbcType=INTEGER}, #{operation,jdbcType=INTEGER}, #{kpiDate,jdbcType=DATE}, 
      #{orderId,jdbcType=INTEGER}, #{orderNo,jdbcType=VARCHAR}, #{orderType,jdbcType=INTEGER}, 
      #{paymentType,jdbcType=INTEGER}, #{paymentComments,jdbcType=VARCHAR}, #{traderId,jdbcType=INTEGER}, 
      #{traderName,jdbcType=VARCHAR}, #{traderType,jdbcType=INTEGER}, #{traderSubject,jdbcType=INTEGER}, 
      #{rossMargin,jdbcType=DECIMAL}, #{isSpecOrder,jdbcType=INTEGER}, #{afterSaleId,jdbcType=INTEGER}, 
      #{afterSaleNo,jdbcType=VARCHAR}, #{afterSaleComment,jdbcType=VARCHAR}, #{amount,jdbcType=DECIMAL}, 
      #{orderRealAmount,jdbcType=DECIMAL}, #{orderSumAmount,jdbcType=DECIMAL}, #{kpiAmount,jdbcType=DECIMAL}, 
      #{chanceNo,jdbcType=VARCHAR}, #{chanceId,jdbcType=INTEGER}, #{chanceType,jdbcType=INTEGER}, 
      #{chanceSource,jdbcType=INTEGER}, #{chanceTransFlag,jdbcType=INTEGER}, #{chanceTransDays,jdbcType=INTEGER}, 
      #{comment,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{isUsed,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.kpi.model.DO.KpiOrderLogDo" useGeneratedKeys="true">
    insert into T_KPI_ORDER_LOG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="teamId != null">
        TEAM_ID,
      </if>
      <if test="groupId != null">
        GROUP_ID,
      </if>
      <if test="operation != null">
        `OPERATION`,
      </if>
      <if test="kpiDate != null">
        KPI_DATE,
      </if>
      <if test="orderId != null">
        ORDER_ID,
      </if>
      <if test="orderNo != null">
        ORDER_NO,
      </if>
      <if test="orderType != null">
        ORDER_TYPE,
      </if>
      <if test="paymentType != null">
        PAYMENT_TYPE,
      </if>
      <if test="paymentComments != null">
        PAYMENT_COMMENTS,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderName != null">
        TRADER_NAME,
      </if>
      <if test="traderType != null">
        TRADER_TYPE,
      </if>
      <if test="traderSubject != null">
        TRADER_SUBJECT,
      </if>
      <if test="rossMargin != null">
        ROSS_MARGIN,
      </if>
      <if test="isSpecOrder != null">
        IS_SPEC_ORDER,
      </if>
      <if test="afterSaleId != null">
        AFTER_SALE_ID,
      </if>
      <if test="afterSaleNo != null">
        AFTER_SALE_NO,
      </if>
      <if test="afterSaleComment != null">
        AFTER_SALE_COMMENT,
      </if>
      <if test="amount != null">
        AMOUNT,
      </if>
      <if test="orderRealAmount != null">
        ORDER_REAL_AMOUNT,
      </if>
      <if test="orderSumAmount != null">
        ORDER_SUM_AMOUNT,
      </if>
      <if test="kpiAmount != null">
        KPI_AMOUNT,
      </if>
      <if test="chanceNo != null">
        CHANCE_NO,
      </if>
      <if test="chanceId != null">
        CHANCE_ID,
      </if>
      <if test="chanceType != null">
        CHANCE_TYPE,
      </if>
      <if test="chanceSource != null">
        CHANCE_SOURCE,
      </if>
      <if test="chanceTransFlag != null">
        CHANCE_TRANS_FLAG,
      </if>
      <if test="chanceTransDays != null">
        CHANCE_TRANS_DAYS,
      </if>
      <if test="comment != null">
        `COMMENT`,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="isUsed != null">
        IS_USED,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="teamId != null">
        #{teamId,jdbcType=INTEGER},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=INTEGER},
      </if>
      <if test="operation != null">
        #{operation,jdbcType=INTEGER},
      </if>
      <if test="kpiDate != null">
        #{kpiDate,jdbcType=DATE},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="paymentType != null">
        #{paymentType,jdbcType=INTEGER},
      </if>
      <if test="paymentComments != null">
        #{paymentComments,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderName != null">
        #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="traderType != null">
        #{traderType,jdbcType=INTEGER},
      </if>
      <if test="traderSubject != null">
        #{traderSubject,jdbcType=INTEGER},
      </if>
      <if test="rossMargin != null">
        #{rossMargin,jdbcType=DECIMAL},
      </if>
      <if test="isSpecOrder != null">
        #{isSpecOrder,jdbcType=INTEGER},
      </if>
      <if test="afterSaleId != null">
        #{afterSaleId,jdbcType=INTEGER},
      </if>
      <if test="afterSaleNo != null">
        #{afterSaleNo,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleComment != null">
        #{afterSaleComment,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="orderRealAmount != null">
        #{orderRealAmount,jdbcType=DECIMAL},
      </if>
      <if test="orderSumAmount != null">
        #{orderSumAmount,jdbcType=DECIMAL},
      </if>
      <if test="kpiAmount != null">
        #{kpiAmount,jdbcType=DECIMAL},
      </if>
      <if test="chanceNo != null">
        #{chanceNo,jdbcType=VARCHAR},
      </if>
      <if test="chanceId != null">
        #{chanceId,jdbcType=INTEGER},
      </if>
      <if test="chanceType != null">
        #{chanceType,jdbcType=INTEGER},
      </if>
      <if test="chanceSource != null">
        #{chanceSource,jdbcType=INTEGER},
      </if>
      <if test="chanceTransFlag != null">
        #{chanceTransFlag,jdbcType=INTEGER},
      </if>
      <if test="chanceTransDays != null">
        #{chanceTransDays,jdbcType=INTEGER},
      </if>
      <if test="comment != null">
        #{comment,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isUsed != null">
        #{isUsed,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.kpi.model.DO.KpiOrderLogDo">
    update T_KPI_ORDER_LOG
    <set>
      <if test="userId != null">
        USER_ID = #{userId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="teamId != null">
        TEAM_ID = #{teamId,jdbcType=INTEGER},
      </if>
      <if test="groupId != null">
        GROUP_ID = #{groupId,jdbcType=INTEGER},
      </if>
      <if test="operation != null">
        `OPERATION` = #{operation,jdbcType=INTEGER},
      </if>
      <if test="kpiDate != null">
        KPI_DATE = #{kpiDate,jdbcType=DATE},
      </if>
      <if test="orderId != null">
        ORDER_ID = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        ORDER_TYPE = #{orderType,jdbcType=INTEGER},
      </if>
      <if test="paymentType != null">
        PAYMENT_TYPE = #{paymentType,jdbcType=INTEGER},
      </if>
      <if test="paymentComments != null">
        PAYMENT_COMMENTS = #{paymentComments,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderName != null">
        TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="traderType != null">
        TRADER_TYPE = #{traderType,jdbcType=INTEGER},
      </if>
      <if test="traderSubject != null">
        TRADER_SUBJECT = #{traderSubject,jdbcType=INTEGER},
      </if>
      <if test="rossMargin != null">
        ROSS_MARGIN = #{rossMargin,jdbcType=DECIMAL},
      </if>
      <if test="isSpecOrder != null">
        IS_SPEC_ORDER = #{isSpecOrder,jdbcType=INTEGER},
      </if>
      <if test="afterSaleId != null">
        AFTER_SALE_ID = #{afterSaleId,jdbcType=INTEGER},
      </if>
      <if test="afterSaleNo != null">
        AFTER_SALE_NO = #{afterSaleNo,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleComment != null">
        AFTER_SALE_COMMENT = #{afterSaleComment,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="orderRealAmount != null">
        ORDER_REAL_AMOUNT = #{orderRealAmount,jdbcType=DECIMAL},
      </if>
      <if test="orderSumAmount != null">
        ORDER_SUM_AMOUNT = #{orderSumAmount,jdbcType=DECIMAL},
      </if>
      <if test="kpiAmount != null">
        KPI_AMOUNT = #{kpiAmount,jdbcType=DECIMAL},
      </if>
      <if test="chanceNo != null">
        CHANCE_NO = #{chanceNo,jdbcType=VARCHAR},
      </if>
      <if test="chanceId != null">
        CHANCE_ID = #{chanceId,jdbcType=INTEGER},
      </if>
      <if test="chanceType != null">
        CHANCE_TYPE = #{chanceType,jdbcType=INTEGER},
      </if>
      <if test="chanceSource != null">
        CHANCE_SOURCE = #{chanceSource,jdbcType=INTEGER},
      </if>
      <if test="chanceTransFlag != null">
        CHANCE_TRANS_FLAG = #{chanceTransFlag,jdbcType=INTEGER},
      </if>
      <if test="chanceTransDays != null">
        CHANCE_TRANS_DAYS = #{chanceTransDays,jdbcType=INTEGER},
      </if>
      <if test="comment != null">
        `COMMENT` = #{comment,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isUsed != null">
        IS_USED = #{isUsed,jdbcType=INTEGER},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.kpi.model.DO.KpiOrderLogDo">
    update T_KPI_ORDER_LOG
    set USER_ID = #{userId,jdbcType=INTEGER},
      COMPANY_ID = #{companyId,jdbcType=INTEGER},
      TEAM_ID = #{teamId,jdbcType=INTEGER},
      GROUP_ID = #{groupId,jdbcType=INTEGER},
      `OPERATION` = #{operation,jdbcType=INTEGER},
      KPI_DATE = #{kpiDate,jdbcType=DATE},
      ORDER_ID = #{orderId,jdbcType=INTEGER},
      ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      ORDER_TYPE = #{orderType,jdbcType=INTEGER},
      PAYMENT_TYPE = #{paymentType,jdbcType=INTEGER},
      PAYMENT_COMMENTS = #{paymentComments,jdbcType=VARCHAR},
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      TRADER_TYPE = #{traderType,jdbcType=INTEGER},
      TRADER_SUBJECT = #{traderSubject,jdbcType=INTEGER},
      ROSS_MARGIN = #{rossMargin,jdbcType=DECIMAL},
      IS_SPEC_ORDER = #{isSpecOrder,jdbcType=INTEGER},
      AFTER_SALE_ID = #{afterSaleId,jdbcType=INTEGER},
      AFTER_SALE_NO = #{afterSaleNo,jdbcType=VARCHAR},
      AFTER_SALE_COMMENT = #{afterSaleComment,jdbcType=VARCHAR},
      AMOUNT = #{amount,jdbcType=DECIMAL},
      ORDER_REAL_AMOUNT = #{orderRealAmount,jdbcType=DECIMAL},
      ORDER_SUM_AMOUNT = #{orderSumAmount,jdbcType=DECIMAL},
      KPI_AMOUNT = #{kpiAmount,jdbcType=DECIMAL},
      CHANCE_NO = #{chanceNo,jdbcType=VARCHAR},
      CHANCE_ID = #{chanceId,jdbcType=INTEGER},
      CHANCE_TYPE = #{chanceType,jdbcType=INTEGER},
      CHANCE_SOURCE = #{chanceSource,jdbcType=INTEGER},
      CHANCE_TRANS_FLAG = #{chanceTransFlag,jdbcType=INTEGER},
      CHANCE_TRANS_DAYS = #{chanceTransDays,jdbcType=INTEGER},
      `COMMENT` = #{comment,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      IS_USED = #{isUsed,jdbcType=INTEGER}
    where ID = #{id,jdbcType=INTEGER}
  </update>

  <!-- add by Tomcat.Hui 2020/6/1 3:02 下午 .Desc: VDERP-2376 【五行剑法】规则修改. start -->
  <!-- 查询未被消费的记录 -->
  <select id="selectUnUsedLogs" parameterType="com.vedeng.kpi.model.DTO.KpiDataQueryDto" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_KPI_ORDER_LOG
    where IS_USED = 0
    and KPI_DATE between #{kpiDateStart} and #{kpiDateEnd}
    and OPERATION in (1,2,3)
  </select>

  <!-- 查询团队kpi金额 -->
  <select id="getGroupOrderAmountMonth" parameterType="com.vedeng.kpi.model.DTO.KpiDataQueryDto" resultMap="QueryResultMap">
    select sum(KPI_AMOUNT) as KPI_AMOUNT,USER_ID
    from T_KPI_ORDER_LOG
    where USER_ID in
    <foreach collection="userIds" item="item" open="(" close=")" index="index" separator=",">
      #{item,jdbcType=INTEGER}
    </foreach>
    and KPI_DATE between #{kpiDateStart,jdbcType=DATE} and #{kpiDateEnd,jdbcType=DATE}
    and OPERATION in (1, 2, 3)
    group by USER_ID
  </select>

  <!-- 查询团队合作客户是否丢失（近90天） -->
  <select id="getGroupCoCustomers" parameterType="com.vedeng.kpi.model.DTO.KpiDataQueryDto" resultMap="QueryResultMap">
    select sum(t.IS_RETURN) as CO_ORDER_NUM, USER_ID, TRADER_ID
    from (
    select sum(case when OPERATION = 1 then 1 when OPERATION = 3 then -1 end) as IS_RETURN,
    USER_ID,
    ORDER_NO,
    TRADER_ID
    from T_KPI_ORDER_LOG
    where USER_ID in
    <foreach collection="userIds" item="userIds" open="(" close=")" index="index" separator=",">
      #{userIds,jdbcType=INTEGER}
    </foreach>
    and datediff(#{kpiDateEnd,jdbcType=DATE}, KPI_DATE) &lt;= #{days}
    and OPERATION in (1,3)
    group by USER_ID, ORDER_NO, TRADER_ID) t
    group by t.USER_ID, t.TRADER_ID
  </select>

  <!-- 查询团队当月新增BD客户数 -->
  <select id="getGroupBdNewCustomers" parameterType="com.vedeng.kpi.model.DTO.KpiDataQueryDto" resultMap="QueryResultMap">
    select count(distinct (TRADER_ID)) as CO_CUSTOMERS,USER_ID
    from T_KPI_ORDER_LOG
    where USER_ID in
    <foreach collection="userIds" item="userIds" open="(" close=")" index="index" separator=",">
      #{userIds,jdbcType=INTEGER}
    </foreach>
      and KPI_DATE between #{kpiDateStart,jdbcType=DATE} and #{kpiDateEnd,jdbcType=DATE}
      and OPERATION = 1
      and ORDER_TYPE = 1
    group by USER_ID
  </select>

  <!-- 查询团队当月BD客户是否失效 -->
  <select id="getGroupBdTraderMonth" parameterType="com.vedeng.kpi.model.DTO.KpiDataQueryDto" resultMap="QueryResultMap">
    select t.USER_ID,t.KPI_DATE,t.TRADER_ID, IF((SUM(ORDER_FLAG) - sum(t.RET_FLAG)) = 0, 1, 0) as IS_ALL_RETURN
    from (select a.*, if(b.ID, 1, 0) as RET_FLAG, if(a.ORDER_ID, 1, 0) as ORDER_FLAG
    from (select USER_ID, date_format(KPI_DATE, '%Y%m') as KPI_DATE, TRADER_ID, ORDER_ID
    from T_KPI_ORDER_LOG
    where
    OPERATION = 1   and ORDER_TYPE = 1 and USER_ID in
    <foreach collection="userIds" item="userIds" open="(" close=")" index="index" separator=",">
      #{userIds,jdbcType=INTEGER}
    </foreach>
    and KPI_DATE between #{kpiDateStart,jdbcType=DATE} and #{kpiDateEnd,jdbcType=DATE}
    group by USER_ID, date_format(KPI_DATE, '%Y%m'), TRADER_ID, ORDER_ID) a
    left join T_KPI_ORDER_LOG b
    on a.ORDER_ID = b.ORDER_ID and b.OPERATION = 3 and b.KPI_DATE &lt; #{kpiDateEnd,jdbcType=DATE}) t
    group by t.USER_ID,t.KPI_DATE, t.TRADER_ID
  </select>

  <!-- 查询团队当月成交商机(需要在代码里再次过滤,如果不是该客户首次转化,则不计入) -->
  <select id="getGroupChanceTrans" parameterType="com.vedeng.kpi.model.DTO.KpiDataQueryDto" resultMap="QueryResultMap">
    select l.USER_ID,
    l.CHANCE_ID,
    l.TRADER_ID,
    l.ORDER_ID,
    if(l.CHANCE_TRANS_FLAG = 0,
    -1,
    1)      as CHANCE_TRANS_FLAG,
    c.ADD_TIME as TRANS_ADD_TIME
    from T_KPI_ORDER_LOG l
    inner join T_BUSSINESS_CHANCE c on c.BUSSINESS_CHANCE_ID = l.CHANCE_ID
    where l.USER_ID in
    <foreach collection="userIds" item="userIds" open="(" close=")" index="index" separator=",">
      #{userIds,jdbcType=INTEGER}
    </foreach>
    and l.KPI_DATE between #{kpiDateStart,jdbcType=DATE} and #{kpiDateEnd,jdbcType=DATE}
    and l.CHANCE_TRANS_FLAG in (0,1)
    and l.CHANCE_ID is not null
  </select>

  <select id="getKpiLogByOrderNoAndOperation" resultType="com.vedeng.kpi.model.DO.KpiOrderLogDo">
    SELECT * FROM T_KPI_ORDER_LOG WHERE ORDER_NO = #{orderNo} AND OPERATION = #{operation}
  </select>

  <select id="getKpiDetail" parameterType="com.vedeng.kpi.model.DTO.KpiDataQueryDto" resultMap="QueryResultMap">
    select l.ID,
           l.USER_ID,
           l.COMPANY_ID,
           l.TEAM_ID,
           l.GROUP_ID,
           l.`OPERATION`,
           l.KPI_DATE,
           l.ORDER_ID,
           l.ORDER_NO,
           l.ORDER_TYPE,
           l.PAYMENT_TYPE,
           l.PAYMENT_COMMENTS,
           l.TRADER_ID,
           l.TRADER_NAME,
           l.TRADER_TYPE,
           l.TRADER_SUBJECT,
           l.ROSS_MARGIN,
           l.IS_SPEC_ORDER,
           l.AFTER_SALE_ID,
           l.AFTER_SALE_NO,
           l.AFTER_SALE_COMMENT,
           l.AMOUNT,
           l.ORDER_REAL_AMOUNT,
           l.ORDER_SUM_AMOUNT,
           l.KPI_AMOUNT,
           l.CHANCE_NO,
           l.CHANCE_ID,
           l.CHANCE_TYPE,
           l.CHANCE_SOURCE,
           l.CHANCE_TRANS_FLAG,
           l.CHANCE_TRANS_DAYS,
           l.`COMMENT`,
           l.ADD_TIME,
           l.UPDATE_TIME,
           l.IS_USED,
           s.VALID_TIME,
           bc.ASSIGN_TIME,
           sh.VALID_TIME as SH_VALID_TIME
    from T_KPI_ORDER_LOG l
           left join T_SALEORDER s on l.ORDER_ID = s.SALEORDER_ID
           left join T_AFTER_SALES sh on sh.AFTER_SALES_ID = l.AFTER_SALE_ID
           LEFT JOIN T_BUSSINESS_CHANCE bc ON l.CHANCE_ID = bc.BUSSINESS_CHANCE_ID
    where l.USER_ID = #{userId}
      and s.VALID_STATUS = 1
      and OPERATION in (1, 2, 3)
    and KPI_DATE between #{kpiDateStart} and #{kpiDateEnd}
  </select>

  <select id="getKpiLogByAfterSalesNo" resultType="com.vedeng.kpi.model.DO.KpiOrderLogDo">
    SELECT * FROM T_KPI_ORDER_LOG WHERE AFTER_SALE_NO = #{afterSalesNo}
  </select>

  <!-- 查询用户下本月新BD客户详情(页面展示明细列表使用) -->
  <select id="getKpiBdCoDetail" parameterType="com.vedeng.kpi.model.DTO.KpiDataQueryDto" resultMap="QueryResultMap">
    select
      l.ORDER_ID,l.TRADER_ID,l.TRADER_NAME,l.ORDER_NO,l.ORDER_REAL_AMOUNT,l.KPI_DATE,s.VALID_TIME
    from T_KPI_ORDER_LOG l
           inner join T_SALEORDER s on l.ORDER_ID = s.SALEORDER_ID
    where l.USER_ID = #{userId}
      and l.OPERATION = 1
      <if test="orderType != null">
        and l.ORDER_TYPE = #{orderType}
      </if>
      and l.KPI_DATE between #{kpiDateStart} and #{kpiDateEnd}
  </select>

  <!-- 查询用户下本月流失BD客户(页面展示明细列表使用) -->
  <select id="getKpiBdLostDetail" parameterType="com.vedeng.kpi.model.DTO.KpiDataQueryDto" resultMap="QueryResultMap">
    select a.TRADER_ID,b.TRADER_NAME,
    a.ORDER_ID,
    a.ORDER_NO,
    a.ORDER_REAL_AMOUNT,s.VALID_TIME, if(b.ID, 1, 0) as RET_FLAG,b.KPI_DATE as KPI_DATE
    from (select date_format(KPI_DATE, '%Y%m') as KPI_DATE, TRADER_ID, ORDER_ID,ORDER_NO,ORDER_REAL_AMOUNT
    from T_KPI_ORDER_LOG
    where ORDER_ID in (select ORDER_ID
    from T_KPI_ORDER_LOG
    where USER_ID = #{userId,jdbcType=INTEGER}
    and KPI_DATE between #{kpiDateStart,jdbcType=DATE} and #{kpiDateEnd,jdbcType=DATE}
    and OPERATION in (2, 3)
    and ORDER_TYPE = 1
    group by ORDER_ID)
    and OPERATION = 1
    <!-- group by date_format(KPI_DATE, '%Y%m'), TRADER_ID, ORDER_ID -->) a
    left join T_KPI_ORDER_LOG b
    on a.ORDER_ID = b.ORDER_ID and b.OPERATION = 3 and b.KPI_DATE &lt;= #{kpiDateEnd,jdbcType=DATE}
    inner join T_SALEORDER s on b.ORDER_ID = s.SALEORDER_ID
  </select>

  <!-- 查询销售名下合作客户是否丢失(页面展示明细列表使用) -->
  <select id="getUserCoCustomers" parameterType="com.vedeng.kpi.model.DTO.KpiDataQueryDto" resultMap="QueryResultMap">
    select sum(t.IS_RETURN) as CO_ORDER_NUM,TRADER_ID
    from (
           select sum(case when OPERATION = 1 then 1 when OPERATION = 3 then -1 else 0 end) as IS_RETURN,
                  ORDER_NO,
                  TRADER_ID
           from T_KPI_ORDER_LOG
           where USER_ID = #{userId,jdbcType=INTEGER}
             and datediff(#{kpiDateEnd,jdbcType=DATE}, KPI_DATE) &lt;= #{days}
           group by ORDER_NO, TRADER_ID) t
    group by t.TRADER_ID
  </select>

  <!-- 查询销售名下客户log详情(页面展示明细列表使用) -->
  <select id="getUserCoCustomersDetail" parameterType="com.vedeng.kpi.model.DTO.KpiDataQueryDto" resultMap="QueryResultMap">
    select
    TRADER_ID,TRADER_NAME,count(TRADER_ID) as CUST_NUM,sum(KPI_AMOUNT) as KPI_AMOUNT
    from T_KPI_ORDER_LOG
    where TRADER_ID in
    <foreach collection="traderIds" item="traderIds" open="(" close=")" index="index" separator=",">
      #{traderIds,jdbcType=INTEGER}
    </foreach>
    and USER_ID = #{userId,jdbcType=INTEGER}
    and datediff(#{kpiDateEnd,jdbcType=DATE}, KPI_DATE) &lt;= #{days}
    and OPERATION = #{operation,jdbcType=INTEGER}
    group by TRADER_ID,TRADER_NAME
  </select>

  <!-- 查询销售名下客户log详情(页面展示明细列表使用) -->
  <select id="getTraderLastTimes" parameterType="com.vedeng.kpi.model.DTO.KpiDataQueryDto" resultMap="QueryResultMap">
    select sale.LAST_ORDER_TIME,
    chat.LAST_CHAT_TIME,
    COALESCE(sale.TRADER_ID, chat.TRADER_ID) as TRADER_ID
    from (
    select max(s.ADD_TIME) as LAST_ORDER_TIME, s.TRADER_ID
    from T_SALEORDER s
    where s.VALID_STATUS = 1
    and TRADER_ID in
    <foreach collection="traderIds" item="item" open="(" close=")" index="index" separator=",">
      #{item,jdbcType=INTEGER}
    </foreach>
    group by s.TRADER_ID) sale
    left join
    (select max(a.BEGINTIME) as LAST_CHAT_TIME, a.TRADER_ID
    from T_COMMUNICATE_RECORD a
    where a.COMPANY_ID = 1
    and a.TRADER_ID in
    <foreach collection="traderIds" item="item" open="(" close=")" index="index" separator=",">
      #{item,jdbcType=INTEGER}
    </foreach>
    and a.TRADER_TYPE = 1
    group by a.TRADER_ID) chat
    on sale.TRADER_ID = chat.TRADER_ID
  </select>

  <!-- 已计入业绩&未到全款 -->
  <select id="getSendNotPaid" parameterType="com.vedeng.kpi.model.DTO.KpiDataQueryDto" resultMap="QueryResultMap">
    select l.ORDER_ID,l.ORDER_NO,l.TRADER_NAME,l.TRADER_ID,s.VALID_TIME,
           s.REAL_TOTAL_AMOUNT as ORDER_REAL_AMOUNT,
           s.REAL_PAY_AMOUNT as ORDER_PAID_AMOUNT
    from T_KPI_ORDER_LOG l
           left join T_SALEORDER s on s.SALEORDER_ID = l.ORDER_ID
    where l.USER_ID = #{userId}
      and l.OPERATION = 1
      and s.DELIVERY_STATUS = 2
      and s.PAYMENT_STATUS in (0, 1)
    order by l.KPI_DATE desc
  </select>

  <!-- 可发货&未计入业绩 -->
  <select id="getPaidNotKpi" parameterType="com.vedeng.kpi.model.DTO.KpiDataQueryDto" resultMap="QueryResultMap">
    select s.SALEORDER_ID as ORDER_ID,
           s.SALEORDER_NO as ORDER_NO,
           s.TRADER_ID,
           s.TRADER_NAME,
           s.USER_ID,
           s.VALID_TIME,
           s.REAL_TOTAL_AMOUNT as ORDER_REAL_AMOUNT,
           s.REAL_PAY_AMOUNT as ORDER_PAID_AMOUNT
    from T_SALEORDER s
    where s.USER_ID = #{userId}
      and s.PAYMENT_STATUS = 2
      and s.LOCKED_STATUS = 0
      and s.DELIVERY_STATUS = 0
      <!-- 金额大于0 且 19年之后  -->
      and s.REAL_TOTAL_AMOUNT &gt; 0
      and s.IS_SALES_PERFORMANCE = 0
  </select>


  <!-- 更新log为已消费 -->
  <update id="updateLogUsed" parameterType="com.vedeng.kpi.model.DTO.KpiDataQueryDto">
  update T_KPI_ORDER_LOG set IS_USED = 1
  where ID between #{startId} and #{endId}
  </update>

  <update id="updateChanceTransferFlag2Null">
    UPDATE T_KPI_ORDER_LOG SET CHANCE_TRANS_FLAG = NULL where ORDER_NO = #{orderNo} AND OPERATION IN (1,3)
  </update>
  <update id="updateChanceTransferFlagByOrderNo">
    UPDATE T_KPI_ORDER_LOG SET CHANCE_TRANS_FLAG = #{flag} WHERE ORDER_NO = #{saleorderNo} AND OPERATION = #{operation}
  </update>
    <update id="updateKpiDateByAfterSalesNo">
      UPDATE T_KPI_ORDER_LOG SET KPI_DATE = #{kpiDate} WHERE AFTER_SALE_NO = #{afterSalesNo}
    </update>

    <!-- 查询商机的最早计入业绩时间 -->
  <select id="getFirstChanceLog" parameterType="com.vedeng.kpi.model.DTO.KpiDataQueryDto" resultMap="QueryResultMap">
    select min(KPI_DATE) as KPI_DATE,CHANCE_ID from T_KPI_ORDER_LOG where CHANCE_ID in
    <foreach collection="chanceIds" item="item" open="(" close=")" index="index" separator=",">
      #{item,jdbcType=INTEGER}
    </foreach>
    and OPERATION = 1 group by CHANCE_ID;
  </select>
    <select id="getKpiLogByPage" resultType="com.vedeng.kpi.model.DO.KpiOrderLogDo">
      SELECT * FROM T_KPI_ORDER_LOG LIMIT #{offset},#{limit}
    </select>
    <select id="getKpiOrderOfSaleorderNo" resultType="com.vedeng.kpi.model.DO.KpiOrderLogDo">
      SELECT * FROM T_KPI_ORDER_LOG WHERE ORDER_NO = #{saleorderNo}
    </select>
  <select id="getKpiLogOfAfterSalesByPage" resultType="com.vedeng.kpi.model.DO.KpiOrderLogDo">
    SELECT * FROM T_KPI_ORDER_LOG WHERE OPERATION IN (2,3) LIMIT #{offset},#{limit}
  </select>

  <select id="getKpiOrderLogNum" resultType="java.lang.Integer">
    SELECT COUNT(1) FROM T_KPI_ORDER_LOG where KPI_DATE = #{kpiDate,jdbcType=DATE}
  </select>

  <select id="getKpiDailyCount" resultType="java.lang.Integer">
    SELECT COUNT(1) FROM T_KPI_DAILY_COUNT where KPI_DATE = #{kpiDate,jdbcType=DATE}
  </select>

  <!-- add by Tomcat.Hui 2020/6/1 3:02 下午 .Desc: VDERP-2376 【五行剑法】规则修改. end -->
</mapper>