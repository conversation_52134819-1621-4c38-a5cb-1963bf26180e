package com.vedeng.goods.model;

import java.util.Date;

public class BaseAttributeValue {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_BASE_ATTRIBUTE_VALUE.BASE_ATTRIBUTE_VALUE_ID
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    private Integer baseAttributeValueId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_BASE_ATTRIBUTE_VALUE.BASE_ATTRIBUTE_ID
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    private Integer baseAttributeId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_BASE_ATTRIBUTE_VALUE.SORT
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    private Integer sort;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_BASE_ATTRIBUTE_VALUE.ATTR_VALUE
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    private String attrValue;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_BASE_ATTRIBUTE_VALUE.UNIT_ID
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    private Integer unitId;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_BASE_ATTRIBUTE_VALUE.IS_DELETED
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    private Integer isDeleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_BASE_ATTRIBUTE_VALUE.ADD_TIME
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    private Date addTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_BASE_ATTRIBUTE_VALUE.CREATOR
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    private Integer creator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_BASE_ATTRIBUTE_VALUE.MOD_TIME
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    private Date modTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_BASE_ATTRIBUTE_VALUE.UPDATER
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    private Integer updater;

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_BASE_ATTRIBUTE_VALUE.BASE_ATTRIBUTE_VALUE_ID
     *
     * @return the value of V_BASE_ATTRIBUTE_VALUE.BASE_ATTRIBUTE_VALUE_ID
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    public Integer getBaseAttributeValueId() {
        return baseAttributeValueId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_BASE_ATTRIBUTE_VALUE.BASE_ATTRIBUTE_VALUE_ID
     *
     * @param baseAttributeValueId the value for V_BASE_ATTRIBUTE_VALUE.BASE_ATTRIBUTE_VALUE_ID
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    public void setBaseAttributeValueId(Integer baseAttributeValueId) {
        this.baseAttributeValueId = baseAttributeValueId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_BASE_ATTRIBUTE_VALUE.BASE_ATTRIBUTE_ID
     *
     * @return the value of V_BASE_ATTRIBUTE_VALUE.BASE_ATTRIBUTE_ID
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    public Integer getBaseAttributeId() {
        return baseAttributeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_BASE_ATTRIBUTE_VALUE.BASE_ATTRIBUTE_ID
     *
     * @param baseAttributeId the value for V_BASE_ATTRIBUTE_VALUE.BASE_ATTRIBUTE_ID
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    public void setBaseAttributeId(Integer baseAttributeId) {
        this.baseAttributeId = baseAttributeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_BASE_ATTRIBUTE_VALUE.SORT
     *
     * @return the value of V_BASE_ATTRIBUTE_VALUE.SORT
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    public Integer getSort() {
        return sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_BASE_ATTRIBUTE_VALUE.SORT
     *
     * @param sort the value for V_BASE_ATTRIBUTE_VALUE.SORT
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    public void setSort(Integer sort) {
        this.sort = sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_BASE_ATTRIBUTE_VALUE.ATTR_VALUE
     *
     * @return the value of V_BASE_ATTRIBUTE_VALUE.ATTR_VALUE
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    public String getAttrValue() {
        return attrValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_BASE_ATTRIBUTE_VALUE.ATTR_VALUE
     *
     * @param attrValue the value for V_BASE_ATTRIBUTE_VALUE.ATTR_VALUE
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    public void setAttrValue(String attrValue) {
        this.attrValue = attrValue == null ? null : attrValue.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_BASE_ATTRIBUTE_VALUE.UNIT_ID
     *
     * @return the value of V_BASE_ATTRIBUTE_VALUE.UNIT_ID
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    public Integer getUnitId() {
        return unitId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_BASE_ATTRIBUTE_VALUE.UNIT_ID
     *
     * @param unitId the value for V_BASE_ATTRIBUTE_VALUE.UNIT_ID
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_BASE_ATTRIBUTE_VALUE.IS_DELETED
     *
     * @return the value of V_BASE_ATTRIBUTE_VALUE.IS_DELETED
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    public Integer getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_BASE_ATTRIBUTE_VALUE.IS_DELETED
     *
     * @param isDeleted the value for V_BASE_ATTRIBUTE_VALUE.IS_DELETED
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_BASE_ATTRIBUTE_VALUE.ADD_TIME
     *
     * @return the value of V_BASE_ATTRIBUTE_VALUE.ADD_TIME
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    public Date getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_BASE_ATTRIBUTE_VALUE.ADD_TIME
     *
     * @param addTime the value for V_BASE_ATTRIBUTE_VALUE.ADD_TIME
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_BASE_ATTRIBUTE_VALUE.CREATOR
     *
     * @return the value of V_BASE_ATTRIBUTE_VALUE.CREATOR
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    public Integer getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_BASE_ATTRIBUTE_VALUE.CREATOR
     *
     * @param creator the value for V_BASE_ATTRIBUTE_VALUE.CREATOR
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_BASE_ATTRIBUTE_VALUE.MOD_TIME
     *
     * @return the value of V_BASE_ATTRIBUTE_VALUE.MOD_TIME
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    public Date getModTime() {
        return modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_BASE_ATTRIBUTE_VALUE.MOD_TIME
     *
     * @param modTime the value for V_BASE_ATTRIBUTE_VALUE.MOD_TIME
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    public void setModTime(Date modTime) {
        this.modTime = modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_BASE_ATTRIBUTE_VALUE.UPDATER
     *
     * @return the value of V_BASE_ATTRIBUTE_VALUE.UPDATER
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    public Integer getUpdater() {
        return updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_BASE_ATTRIBUTE_VALUE.UPDATER
     *
     * @param updater the value for V_BASE_ATTRIBUTE_VALUE.UPDATER
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }



    @Override
    public int hashCode() {
        return getBaseAttributeValueId() == null ? 0: this.getBaseAttributeValueId().hashCode();
    }

    @Override
    public boolean equals(Object obj) {
        if(obj==null){
            return false;
        }

        if (!(obj instanceof BaseAttributeValue)){
            return false;
        }
        BaseAttributeValue input = (BaseAttributeValue) obj;
        return input==this|| input.getBaseAttributeValueId().equals(this.getBaseAttributeValueId());
    }
}