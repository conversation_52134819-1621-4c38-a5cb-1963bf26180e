<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.crm.visitrecord.mapper.CrmCommunicateRecordMapper">

    <select id="selectCommunicateByRecordId" resultType="com.vedeng.crm.visitrecord.domain.vo.CommunicateRecordVo">
        SELECT 
            c.COMMUNICATE_RECORD_ID as communicateRecordId,
            c.RELATED_ID as relatedId,
            c.<PERSON><PERSON><PERSON><PERSON> as creator,
            u.USERNAME as userName,
            DETAIL.ALIAS_HEAD_PICTURE AS aliasHeadPicture,
            FROM_UNIXTIME(c.ADD_TIME/1000) as addTime,
            c.CONTENT_SUFFIX as contentSuffix
        FROM 
            T_COMMUNICATE_RECORD c
            LEFT JOIN T_USER u ON c.CREATOR = u.USER_ID
            LEFT JOIN T_USER_DETAIL DETAIL ON DETAIL.USER_ID = c.CREATOR
        WHERE 
            c.RELATED_ID = #{recordId}
            AND c.COMMUNICATE_TYPE = 5503
        ORDER BY 
            c.ADD_TIME DESC
    </select>

</mapper> 