<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.system.dao.AttachmentMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.system.model.Attachment" >
    <id column="ATTACHMENT_ID" property="attachmentId" jdbcType="INTEGER" />
    <result column="ATTACHMENT_TYPE" property="attachmentType" jdbcType="INTEGER" />
    <result column="ATTACHMENT_FUNCTION" property="attachmentFunction" jdbcType="INTEGER" />
    <result column="RELATED_ID" property="relatedId" jdbcType="INTEGER" />
    <result column="NAME" property="name" jdbcType="VARCHAR" />
    <result column="DOMAIN" property="domain" jdbcType="VARCHAR" />
    <result column="URI" property="uri" jdbcType="VARCHAR" />
    <result column="ALT" property="alt" jdbcType="VARCHAR" />
    <result column="SORT" property="sort" jdbcType="INTEGER" />
    <result column="IS_DEFAULT" property="isDefault" jdbcType="BIT" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="IS_DELETED" property="isDeleted" jdbcType="INTEGER" />
    <result column="SUFFIX" property="suffix" jdbcType="VARCHAR" />
  </resultMap>

  <resultMap id="wmsQlaResultMap" type="com.wms.model.po.WmsQLA" extends="BaseResultMap">
    <result column="SKU_NO" property="skuNO" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ATTACHMENT_ID, ATTACHMENT_TYPE, ATTACHMENT_FUNCTION, RELATED_ID, NAME, DOMAIN, URI,
    ALT, SORT, IS_DEFAULT, ADD_TIME, CREATOR, IS_DELETED, SUFFIX, SYN_SUCCESS
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from T_ATTACHMENT
    where ATTACHMENT_ID = #{attachmentId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from T_ATTACHMENT
    where ATTACHMENT_ID = #{attachmentId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.system.model.Attachment" >
    insert into T_ATTACHMENT (ATTACHMENT_ID, ATTACHMENT_TYPE, ATTACHMENT_FUNCTION,
      RELATED_ID, NAME, DOMAIN,
      URI, ALT, SORT, IS_DEFAULT,
      ADD_TIME, CREATOR, SUFFIX)
    values (#{attachmentId,jdbcType=INTEGER}, #{attachmentType,jdbcType=INTEGER}, #{attachmentFunction,jdbcType=INTEGER},
      #{relatedId,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, #{domain,jdbcType=VARCHAR},
      #{uri,jdbcType=VARCHAR}, #{alt,jdbcType=VARCHAR}, #{sort,jdbcType=INTEGER}, #{isDefault,jdbcType=BIT},
      #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, #{suffix,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.system.model.Attachment" useGeneratedKeys="true" keyProperty="attachmentId">
    insert into T_ATTACHMENT
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="attachmentId != null" >
        ATTACHMENT_ID,
      </if>
      <if test="attachmentType != null" >
        ATTACHMENT_TYPE,
      </if>
      <if test="attachmentFunction != null" >
        ATTACHMENT_FUNCTION,
      </if>
      <if test="relatedId != null" >
        RELATED_ID,
      </if>
      <if test="name != null" >
        NAME,
      </if>
      <if test="domain != null" >
        DOMAIN,
      </if>
      <if test="uri != null" >
        URI,
      </if>
      <if test="alt != null" >
        ALT,
      </if>
      <if test="sort != null" >
        SORT,
      </if>
      <if test="isDefault != null" >
        IS_DEFAULT,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="ossResourceId != null" >
        OSS_RESOURCE_ID,
      </if>
      <if test="originalFilepath != null" >
        ORIGINAL_FILEPATH,
      </if>
      <if test="isDeleted != null" >
        IS_DELETED,
      </if>

      <if test="suffix != null" >
        SUFFIX,
      </if>

    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="attachmentId != null" >
        #{attachmentId,jdbcType=INTEGER},
      </if>
      <if test="attachmentType != null" >
        #{attachmentType,jdbcType=INTEGER},
      </if>
      <if test="attachmentFunction != null" >
        #{attachmentFunction,jdbcType=INTEGER},
      </if>
      <if test="relatedId != null" >
        #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="domain != null" >
        #{domain,jdbcType=VARCHAR},
      </if>
      <if test="uri != null" >
        #{uri,jdbcType=VARCHAR},
      </if>
      <if test="alt != null" >
        #{alt,jdbcType=VARCHAR},
      </if>
      <if test="sort != null" >
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="isDefault != null" >
        #{isDefault,jdbcType=BIT},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="ossResourceId != null" >
        #{ossResourceId,jdbcType=VARCHAR},
      </if>
      <if test="originalFilepath != null" >
        #{originalFilepath,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null" >
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="suffix != null" >
        #{suffix,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.system.model.Attachment" >
    update T_ATTACHMENT
    <set >
      <if test="attachmentType != null" >
        ATTACHMENT_TYPE = #{attachmentType,jdbcType=INTEGER},
      </if>
      <if test="attachmentFunction != null" >
        ATTACHMENT_FUNCTION = #{attachmentFunction,jdbcType=INTEGER},
      </if>
      <if test="relatedId != null" >
        RELATED_ID = #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="name != null" >
        NAME = #{name,jdbcType=VARCHAR},
      </if>
      <if test="domain != null" >
        DOMAIN = #{domain,jdbcType=VARCHAR},
      </if>
      <if test="uri != null" >
        URI = #{uri,jdbcType=VARCHAR},
      </if>
      <if test="alt != null" >
        ALT = #{alt,jdbcType=VARCHAR},
      </if>
      <if test="sort != null" >
        SORT = #{sort,jdbcType=INTEGER},
      </if>
      <if test="isDefault != null" >
        IS_DEFAULT = #{isDefault,jdbcType=BIT},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="ossResourceId != null" >
        OSS_RESOURCE_ID = #{ossResourceId,jdbcType=VARCHAR},
      </if>
      <if test="originalFilepath != null" >
        ORIGINAL_FILEPATH = #{originalFilepath,jdbcType=VARCHAR},
      </if>
      <if test="suffix != null" >
        SUFFIX = #{suffix,jdbcType=VARCHAR},
      </if>
      <if test="synSuccess != null" >
        SYN_SUCCESS = #{synSuccess,jdbcType=INTEGER},
      </if>
    </set>
    where ATTACHMENT_ID = #{attachmentId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.system.model.Attachment" >
    update T_ATTACHMENT
    set ATTACHMENT_TYPE = #{attachmentType,jdbcType=INTEGER},
      ATTACHMENT_FUNCTION = #{attachmentFunction,jdbcType=INTEGER},
      RELATED_ID = #{relatedId,jdbcType=INTEGER},
      NAME = #{name,jdbcType=VARCHAR},
      DOMAIN = #{domain,jdbcType=VARCHAR},
      URI = #{uri,jdbcType=VARCHAR},
      ALT = #{alt,jdbcType=VARCHAR},
      SORT = #{sort,jdbcType=INTEGER},
      IS_DEFAULT = #{isDefault,jdbcType=BIT},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      SUFFIX = #{suffix,jdbcType=INTEGER}
    where ATTACHMENT_ID = #{attachmentId,jdbcType=INTEGER}
  </update>


  <select id="getAttachmentsList" parameterType="com.vedeng.system.model.Attachment" resultMap="BaseResultMap" >
  	select
    <include refid="Base_Column_List" />
    from T_ATTACHMENT
    where 1=1

    AND ATTACHMENT_TYPE = #{attachmentType,jdbcType=INTEGER}
    <if test="registrationNumberId != null and registrationNumberId != ''">
      and RELATED_ID = #{registrationNumberId,jdbcType=INTEGER}
    </if>
    and ATTACHMENT_FUNCTION IN
    <foreach collection="attachmentFunction" item="attachment" open="(" close=")" separator=",">
    	#{attachment,jdbcType=INTEGER}
    </foreach>
    <if test="addTime != null and addTime != ''">
        and ADD_TIME >= #{addTime,jdbcType=BIGINT}
    </if>
    <if test="domain != null and domain != ''">
      and DOMAIN = #{domain,jdbcType=VARCHAR}
    </if>
    and IS_DELETED =0
    order by SORT desc
  </select>

  <select id="getAttachmentsByProductCompanyId" resultMap="BaseResultMap">
    select
    DOMAIN, URI
    from T_ATTACHMENT a
    join
      T_REGISTRATION_NUMBER b
    on a.RELATED_ID = b.REGISTRATION_NUMBER_ID
    where b.PRODUCT_COMPANY_ID = #{productCompanyId,jdbcType=INTEGER}
    and a.ATTACHMENT_FUNCTION IN
    <foreach collection="attachmentFunction" item="attachment" open="(" close=")" separator=",">
      #{attachment,jdbcType=INTEGER}
    </foreach>

  </select>

  <delete id="deleteByParam" parameterType="java.util.Map" >
    delete from T_ATTACHMENT
    where
    	ATTACHMENT_TYPE = #{attachmentType, jdbcType=INTEGER}
    AND RELATED_ID = #{registrationNumberId, jdbcType=INTEGER}
  </delete>

  <update id="updataByParamNew" parameterType="java.util.Map" >
    update T_ATTACHMENT set IS_DELETED =1
    where
    	ATTACHMENT_TYPE = #{attachmentType, jdbcType=INTEGER}
    AND RELATED_ID = #{manufacturerId, jdbcType=INTEGER}
    AND ATTACHMENT_FUNCTION  IN
    <foreach collection="attachmentFunction" item="attachment" open="(" close=")" separator=",">
      #{attachment,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateByRelation">
    update T_ATTACHMENT set IS_DELETED = 1 where RELATED_ID = #{relatedId,jdbcType=INTEGER} and ATTACHMENT_FUNCTION = #{attachmentFunction,jdbcType=INTEGER}
  </update>

  <delete id="deleteJGBDGZB" parameterType="java.util.Map" >
    delete from T_ATTACHMENT
    where
    	ATTACHMENT_TYPE = #{attachmentType, jdbcType=INTEGER}
    AND RELATED_ID = #{registrationNumberId, jdbcType=INTEGER}
    AND  ATTACHMENT_FUNCTION = 1001
  </delete>

  <insert id="insertAttachmentList" parameterType="java.util.Map">
  	insert into T_ATTACHMENT
  		(ATTACHMENT_TYPE, ATTACHMENT_FUNCTION, RELATED_ID, DOMAIN, URI, ADD_TIME, CREATOR, SUFFIX)
    values
    	<foreach collection="attachmentsList" item="attachments" separator=",">
    		<trim prefix="(" suffix=")" suffixOverrides=",">
	    		#{attachmentType,jdbcType=INTEGER}, #{attachments.attachmentFunction,jdbcType=INTEGER}, #{registrationNumberId,jdbcType=INTEGER},
	    		#{attachments.domain,jdbcType=VARCHAR}, #{attachments.uri,jdbcType=VARCHAR}, unix_timestamp(now())*1000, #{userId,jdbcType=INTEGER},
                #{attachments.suffix,jdbcType=VARCHAR}
    		</trim>
    	</foreach>
  </insert>
  <select id="getSkuBarcodeByGoodsId" resultMap="BaseResultMap">
  SELECT
    <include refid="Base_Column_List" />
  FROM
	T_ATTACHMENT A
  WHERE
	A.ATTACHMENT_FUNCTION = 1400
	AND A.ATTACHMENT_TYPE = 500
	AND A.RELATED_ID= #{goodsId,jdbcType=INTEGER}
  </select>

  <delete id="delByKeyIdAndRelatedTableId">
    delete from T_ATTACHMENT where RELATED_ID = #{realtedId} and ATTACHMENT_FUNCTION = #{realtedTableId}
  </delete>

  <delete id="delAttachment" parameterType="com.vedeng.system.model.Attachment">
    delete from T_ATTACHMENT
    where 1=1
    <if test="attachmentId != null and attachmentId != 0">
      and ATTACHMENT_ID = #{attachmentId,jdbcType=INTEGER}
    </if>
    <if test="attachmentFunction != null">
      and ATTACHMENT_FUNCTION = #{attachmentFunction,jdbcType=INTEGER}
    </if>
    <if test="attachmentType != null">
      and ATTACHMENT_TYPE = #{attachmentType,jdbcType=INTEGER}
    </if>
    <if test="relatedId != null">
      and RELATED_ID = #{relatedId,jdbcType=INTEGER}
    </if>
  </delete>
  <delete id="delVerifiesInfo" parameterType="com.vedeng.system.model.Attachment">
    delete from T_VERIFIES_INFO
    where 1=1
      and RELATE_TABLE_KEY =#{relatedId}
    and VERIFIES_TYPE=868
  </delete>

    <select id="getAttachmentInfoByRelatedIdAndFunctionId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from T_ATTACHMENT where RELATED_ID = #{realtedId, jdbcType=INTEGER} and ATTACHMENT_FUNCTION = #{functionId, jdbcType=INTEGER}
  </select>
  <select id="getWmsQlaListBySkuId" resultMap="wmsQlaResultMap">
    SELECT
      SKU.SKU_NO,
      FILE.*
    FROM
      V_CORE_SKU SKU
        LEFT JOIN V_CORE_SPU SPU ON SKU.SPU_ID = SPU.SPU_ID
        LEFT JOIN T_FIRST_ENGAGE FE ON FE.FIRST_ENGAGE_ID = SPU.FIRST_ENGAGE_ID
        LEFT JOIN T_REGISTRATION_NUMBER RN ON RN.REGISTRATION_NUMBER_ID = FE.REGISTRATION_NUMBER_ID
        LEFT JOIN T_ATTACHMENT FILE ON FILE.RELATED_ID = RN.REGISTRATION_NUMBER_ID
        AND FILE.ATTACHMENT_TYPE = 974
        AND FILE.ATTACHMENT_FUNCTION IN ( 975 ,1301 )
        AND FILE.IS_DELETED=0
    WHERE
      FILE.ATTACHMENT_ID IS NOT NULL AND  SKU.SKU_ID=#{skuId,jdbcType=INTEGER}
    UNION ALL
    SELECT
      SKU.SKU_NO,
      TC.*
    FROM
      V_CORE_SKU SKU
        LEFT JOIN V_CORE_SPU SPU ON SKU.SPU_ID = SPU.SPU_ID
        LEFT JOIN T_FIRST_ENGAGE FE ON FE.FIRST_ENGAGE_ID = SPU.FIRST_ENGAGE_ID
        LEFT JOIN T_REGISTRATION_NUMBER RN ON RN.REGISTRATION_NUMBER_ID = FE.REGISTRATION_NUMBER_ID
        LEFT JOIN T_ATTACHMENT TC ON TC.RELATED_ID = RN.MANUFACTURER_ID
        AND TC.ATTACHMENT_TYPE = 974
        AND TC.ATTACHMENT_FUNCTION IN (1302,1303,1304,1305,1306,1307)
        AND TC.IS_DELETED=0
    WHERE
      TC.ATTACHMENT_ID IS NOT NULL AND  SKU.SKU_ID=#{skuId,jdbcType=INTEGER}
  </select>
  <select id="getWmsQlaList" resultMap="wmsQlaResultMap">
    SELECT
      SKU.SKU_NO,
      FILE.*
    FROM
      V_CORE_SKU SKU
        LEFT JOIN V_CORE_SPU SPU ON SKU.SPU_ID = SPU.SPU_ID
        LEFT JOIN T_FIRST_ENGAGE FE ON FE.FIRST_ENGAGE_ID = SPU.FIRST_ENGAGE_ID
        LEFT JOIN T_REGISTRATION_NUMBER RN ON RN.REGISTRATION_NUMBER_ID = FE.REGISTRATION_NUMBER_ID
        LEFT JOIN T_ATTACHMENT FILE ON FILE.RELATED_ID = RN.REGISTRATION_NUMBER_ID
        AND FILE.ATTACHMENT_TYPE = 974
        AND FILE.ATTACHMENT_FUNCTION IN ( 975 ,1301 )
        AND FILE.IS_DELETED=0
    WHERE
      FILE.ATTACHMENT_ID IS NOT NULL
    UNION ALL
    SELECT
      SKU.SKU_NO,
      TC.*
    FROM
      V_CORE_SKU SKU
        LEFT JOIN V_CORE_SPU SPU ON SKU.SPU_ID = SPU.SPU_ID
        LEFT JOIN T_FIRST_ENGAGE FE ON FE.FIRST_ENGAGE_ID = SPU.FIRST_ENGAGE_ID
        LEFT JOIN T_REGISTRATION_NUMBER RN ON RN.REGISTRATION_NUMBER_ID = FE.REGISTRATION_NUMBER_ID
        LEFT JOIN T_ATTACHMENT TC ON TC.RELATED_ID = RN.MANUFACTURER_ID
        AND TC.ATTACHMENT_TYPE = 974
        AND TC.ATTACHMENT_FUNCTION IN (1302,1303,1304,1305,1306,1307)
        AND TC.IS_DELETED=0
    WHERE
      TC.ATTACHMENT_ID IS NOT NULL
  </select>

  <select id="getWmsQlaListInSkuNoStr" resultMap="wmsQlaResultMap">
    SELECT
      SKU.SKU_NO,
      FILE.*
    FROM
      V_CORE_SKU SKU
        LEFT JOIN V_CORE_SPU SPU ON SKU.SPU_ID = SPU.SPU_ID
        LEFT JOIN T_FIRST_ENGAGE FE ON FE.FIRST_ENGAGE_ID = SPU.FIRST_ENGAGE_ID
        LEFT JOIN T_REGISTRATION_NUMBER RN ON RN.REGISTRATION_NUMBER_ID = FE.REGISTRATION_NUMBER_ID
        LEFT JOIN T_ATTACHMENT FILE ON FILE.RELATED_ID = RN.REGISTRATION_NUMBER_ID
        AND FILE.ATTACHMENT_TYPE = 974
        AND FILE.ATTACHMENT_FUNCTION IN ( 975 ,1301 )
        AND FILE.IS_DELETED=0
    WHERE
      FILE.ATTACHMENT_ID IS NOT NULL AND  SKU.SKU_NO in (${skuNoStr})
    UNION ALL
    SELECT
      SKU.SKU_NO,
      TC.*
    FROM
      V_CORE_SKU SKU
        LEFT JOIN V_CORE_SPU SPU ON SKU.SPU_ID = SPU.SPU_ID
        LEFT JOIN T_FIRST_ENGAGE FE ON FE.FIRST_ENGAGE_ID = SPU.FIRST_ENGAGE_ID
        LEFT JOIN T_REGISTRATION_NUMBER RN ON RN.REGISTRATION_NUMBER_ID = FE.REGISTRATION_NUMBER_ID
        LEFT JOIN T_ATTACHMENT TC ON TC.RELATED_ID = RN.MANUFACTURER_ID
        AND TC.ATTACHMENT_TYPE = 974
        AND TC.ATTACHMENT_FUNCTION IN (1302,1303,1304,1305,1306,1307)
        AND TC.IS_DELETED=0
    WHERE
      TC.ATTACHMENT_ID IS NOT NULL AND  SKU.SKU_NO in (${skuNoStr})
  </select>


  <select id="getWmsQlaListByRegisterNumberId" resultMap="wmsQlaResultMap">
	SELECT
	  file.*
	FROM T_ATTACHMENT file
	where file.RELATED_ID = #{registrationNumberId,jdbcType=INTEGER} and file.attachment_function in (1000,975,978)
  </select>
  <select id="getAttachmentsByOriginalFilepath" resultType="com.vedeng.system.model.Attachment">
    SELECT
      A.*
    FROM
      T_ATTACHMENT A
    WHERE
      A.ATTACHMENT_TYPE = #{attachmentType,jdbcType=INTEGER}
      AND A.ORIGINAL_FILEPATH = #{originalFilepath,jdbcType=VARCHAR}
    LIMIT 1
  </select>
  <select id="getWmsQlaListHistory" resultType="com.vedeng.system.model.Attachment">
    SELECT
      file.*,sku.SKU_NO
    FROM V_CORE_SKU sku
    INNER JOIN V_CORE_SPU spu on sku.SPU_ID=spu.SPU_ID
    INNER JOIN T_FIRST_ENGAGE fe on fe.FIRST_ENGAGE_ID=spu.FIRST_ENGAGE_ID
    INNER JOIN T_REGISTRATION_NUMBER rn on rn.REGISTRATION_NUMBER_ID=fe.REGISTRATION_NUMBER_ID
    INNER JOIN T_ATTACHMENT file on file.RELATED_ID=rn.REGISTRATION_NUMBER_ID
    WHERE file.attachment_function IN (1000,975,978)
      AND  ATTACHMENT_ID &gt; #{start}
      AND DOMAIN = 'file1.vedeng.com'
    GROUP BY file.ATTACHMENT_ID
      limit #{limit}
  </select>

  <select id="getUndeletedAttachmentsList" parameterType="com.vedeng.system.model.Attachment" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from T_ATTACHMENT
    where ATTACHMENT_TYPE = #{attachmentType,jdbcType=INTEGER}
    <if test="registrationNumberId != null and registrationNumberId != ''">
      and RELATED_ID = #{registrationNumberId,jdbcType=INTEGER}
    </if>
    and ATTACHMENT_FUNCTION IN
    <foreach collection="attachmentFunction" item="attachment" open="(" close=")" separator=",">
      #{attachment,jdbcType=INTEGER}
    </foreach>
    <if test="addTime != null and addTime != ''">
      and ADD_TIME >= #{addTime,jdbcType=BIGINT}
    </if>
    <if test="domain != null and domain != ''">
      and DOMAIN = #{domain,jdbcType=VARCHAR}
    </if>
    and IS_DELETED =0
    order by SORT desc
  </select>
  <select id="selectByPropertiesSelective" resultType="com.vedeng.system.model.Attachment">
    select
    <include refid="Base_Column_List" />
    from T_ATTACHMENT
    where ATTACHMENT_TYPE = #{attachmentType,jdbcType=INTEGER}
    <if test="relatedId != null and relatedId != ''">
      and RELATED_ID = #{relatedId,jdbcType=INTEGER}
    </if>
    <if test="attachmentFunction != null and attachmentFunction !=''">
      and ATTACHMENT_FUNCTION = #{attachmentFunction,jdbcType=INTEGER}
    </if>
    <if test="domain != null and domain !=''">
      and DOMAIN = #{domain,jdbcType=VARCHAR}
    </if>
    <if test="uri != null and uri !=''">
      and URI = #{uri,jdbcType=VARCHAR}
    </if>
    and IS_DELETED =0
  </select>

  <select id="getAttachmentList" resultMap="BaseResultMap" parameterType="com.vedeng.goods.model.vo.Attachment">
    select <include refid="Base_Column_List" />
    from T_ATTACHMENT
    where IS_DELETED = 0
    <if test="attachmentId != null and attachmentId != ''">
      AND ATTACHMENT_ID = #{attachmentId,jdbcType=INTEGER}
    </if>
    <if test="relatedId != null and relatedId != ''">
      AND RELATED_ID = #{relatedId,jdbcType=INTEGER}
    </if>
    <if test="attachmentType != null and attachmentType != ''">
      AND ATTACHMENT_TYPE = #{attachmentType,jdbcType=INTEGER}
    </if>
    <if test="attachmentFunction != null and attachmentFunction != ''">
      AND ATTACHMENT_FUNCTION = #{attachmentFunction,jdbcType=INTEGER}
    </if>
  </select>


  <select id="getAttachmentListByAlt" resultMap="BaseResultMap" parameterType="com.vedeng.goods.model.vo.Attachment">
    select <include refid="Base_Column_List" />
    from T_ATTACHMENT
    where 1 = 1
    <if test="attachmentId != null and attachmentId != ''">
      AND ATTACHMENT_ID = #{attachmentId,jdbcType=INTEGER}
    </if>
    <if test="relatedId != null and relatedId != ''">
      AND RELATED_ID = #{relatedId,jdbcType=INTEGER}
    </if>
    <if test="attachmentType != null and attachmentType != ''">
      AND ATTACHMENT_TYPE = #{attachmentType,jdbcType=INTEGER}
    </if>
    <if test="attachmentFunction != null and attachmentFunction != ''">
      AND ATTACHMENT_FUNCTION = #{attachmentFunction,jdbcType=INTEGER}
    </if>
    <if test="alt != null and alt != ''">
      AND ALT = #{alt,jdbcType=VARCHAR}
    </if>
  </select>
  <select id="getSaleorderAttachmentById" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		SELECT <include refid="Base_Column_List" />
			FROM T_ATTACHMENT
			WHERE ATTACHMENT_FUNCTION in (492,493,1201) AND RELATED_ID = #{saleorderId,jdbcType=INTEGER} AND IS_DELETED = 0
        ORDER BY
        ADD_TIME DESC
   </select>

  <select id="getSaleorderAttachmentByIdAndType" resultMap="BaseResultMap" >
    SELECT <include refid="Base_Column_List" />
    FROM T_ATTACHMENT
    WHERE ATTACHMENT_FUNCTION in (#{type,jdbcType=INTEGER}) AND RELATED_ID = #{saleorderId,jdbcType=INTEGER} AND IS_DELETED = 0
    ORDER BY
    ADD_TIME DESC
  </select>

  <select id="getSyncAttachmentsList" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from T_ATTACHMENT
    where 1=1
    and IS_DELETED =0
    AND SUFFIX IS NULL
    AND ( URI IS NOT NULL AND URI != '' )
    AND ( DOMAIN IS NOT NULL AND DOMAIN != '' )
    <if test="page != null and count != null">
      LIMIT  #{page, jdbcType=INTEGER}, #{count, jdbcType=INTEGER}
    </if>
  </select>
  <select id="getAfterSaleAttachmentList" resultType="com.vedeng.system.model.Attachment">
    select <include refid="Base_Column_List" />
    from T_ATTACHMENT
    where 1 = 1
    <if test="attachmentId != null and attachmentId != ''">
      AND ATTACHMENT_ID = #{attachmentId,jdbcType=INTEGER}
    </if>
    <if test="relatedId != null and relatedId != ''">
      AND RELATED_ID = #{relatedId,jdbcType=INTEGER}
    </if>
    <if test="attachmentType != null and attachmentType != ''">
      AND ATTACHMENT_TYPE = #{attachmentType,jdbcType=INTEGER}
    </if>
    <if test="attachmentFunction != null and attachmentFunction != ''">
      AND ATTACHMENT_FUNCTION = #{attachmentFunction,jdbcType=INTEGER}
    </if>
    AND IS_DELETED != 1
  </select>
  <select id="queryRelatedIdByAttachementId" resultType="com.vedeng.system.model.Attachment">
    select * from T_ATTACHMENT where ATTACHMENT_ID =#{attachmentId}
  </select>
  <select id="getAttachment" resultType="com.vedeng.system.model.Attachment">
    select <include refid="Base_Column_List" />
    from T_ATTACHMENT
    where 1 = 1
    <if test="attachmentId != null and attachmentId != ''">
      AND ATTACHMENT_ID = #{attachmentId,jdbcType=INTEGER}
    </if>
    <if test="relatedId != null and relatedId != ''">
      AND RELATED_ID = #{relatedId,jdbcType=INTEGER}
    </if>
    <if test="attachmentType != null and attachmentType != ''">
      AND ATTACHMENT_TYPE = #{attachmentType,jdbcType=INTEGER}
    </if>
    <if test="attachmentFunction != null and attachmentFunction != ''">
      AND ATTACHMENT_FUNCTION = #{attachmentFunction,jdbcType=INTEGER}
    </if>
    limit 1
  </select>
  <select id="findSaleorderId" resultType="com.vedeng.system.model.Attachment">
    select RELATED_ID  from T_ATTACHMENT
    where 1=1
    <if test="attachmentId != null and attachmentId != 0">
      and ATTACHMENT_ID = #{attachmentId,jdbcType=INTEGER}
    </if>
    <if test="attachmentFunction != null">
      and ATTACHMENT_FUNCTION = #{attachmentFunction,jdbcType=INTEGER}
    </if>
    <if test="attachmentType != null">
      and ATTACHMENT_TYPE = #{attachmentType,jdbcType=INTEGER}
    </if>
    <if test="relatedId != null">
      and RELATED_ID = #{relatedId,jdbcType=INTEGER}
    </if>
  </select>

  <update id="batchUpdateAttachment" parameterType="java.util.List">
    <foreach collection="list" item="item" index="index" separator=";">
      UPDATE T_ATTACHMENT
      SET SUFFIX = #{item.suffix,jdbcType=INTEGER}
      where ATTACHMENT_ID = #{item.attachmentId,jdbcType=INTEGER}
    </foreach>
  </update>

  <update id="updateUpdateAttachmentSuffix">
      UPDATE T_ATTACHMENT SET
      SUFFIX = #{attachment.suffix,jdbcType=INTEGER}
      where ATTACHMENT_ID = #{attachment.attachmentId,jdbcType=INTEGER}
  </update>

  <update id="batchDeleteByPrimaryKey" parameterType="java.util.List">
    UPDATE T_ATTACHMENT SET
      IS_DELETED = 1
    where ATTACHMENT_ID in (
      <foreach collection="record" item="item" index="index" separator=",">
          #{item,jdbcType=INTEGER}
      </foreach>
    )
  </update>
  <update id="updateAttachmentById" parameterType="com.vedeng.system.model.Attachment">
    UPDATE T_ATTACHMENT SET
    IS_DELETED = 1
    where ATTACHMENT_FUNCTION in (492) AND RELATED_ID = #{relatedId,jdbcType=INTEGER}
  </update>
  <update id="updateByAttachment" parameterType="com.vedeng.system.model.Attachment">
    update T_ATTACHMENT
    set IS_DELETED = 1
    where 1=1
    <if test="attachmentId != null and attachmentId != 0">
      and ATTACHMENT_ID = #{attachmentId,jdbcType=INTEGER}
    </if>
    <if test="attachmentFunction != null">
      and ATTACHMENT_FUNCTION = #{attachmentFunction,jdbcType=INTEGER}
    </if>
    <if test="attachmentType != null">
      and ATTACHMENT_TYPE = #{attachmentType,jdbcType=INTEGER}
    </if>
    <if test="relatedId != null">
      and RELATED_ID = #{relatedId,jdbcType=INTEGER}
    </if>
  </update>
  <update id="updateVerifiesInfo" parameterType="com.vedeng.system.model.Attachment">
    update T_VERIFIES_INFO
    set IS_DELETED = 1
    where 1=1
    <if test="relatedId !=null">
      and RELATE_TABLE_KEY =#{relatedId}
    </if>
    and VERIFIES_TYPE=868
  </update>

   <select id="getWMSImageList" resultType="com.vedeng.system.model.Attachment">

    SELECT
      A.*
    FROM
      T_ATTACHMENT A
    WHERE
      A.ATTACHMENT_TYPE = 2000
      AND A.ATTACHMENT_FUNCTION IN (2001,2002)
      AND A.SYN_SUCCESS =0
    GROUP BY A.ORIGINAL_FILEPATH
  </select>
  <select id="getAttachmentsByParams" resultType="com.vedeng.system.model.Attachment">
    SELECT
      A.*
    FROM
      T_ATTACHMENT A
    WHERE
      A.ATTACHMENT_TYPE = #{attachmentType}
      <if test="attachmentFunction != null and attachmentFunction != ''">
        AND A.ATTACHMENT_FUNCTION = #{attachmentFunction}
      </if>
      <if test="originalFilepath != null" >
        AND ORIGINAL_FILEPATH = #{originalFilepath,jdbcType=VARCHAR}
      </if>
  </select>
  <insert id="batchInsertSelective" keyColumn="ATTACHMENT_ID" keyProperty="attachmentId" parameterType="com.vedeng.system.model.Attachment" useGeneratedKeys="true">
    insert into T_ATTACHMENT (ATTACHMENT_TYPE, ATTACHMENT_FUNCTION,
    RELATED_ID,  `DOMAIN`,
    URI,ADD_TIME, CREATOR)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix=" (" suffix=")" suffixOverrides=",">
        #{item.attachmentType,jdbcType=INTEGER}, #{item.attachmentFunction,jdbcType=INTEGER},
        #{item.relatedId,jdbcType=INTEGER}, #{item.domain,jdbcType=VARCHAR},
        #{item.uri,jdbcType=VARCHAR},
        #{item.addTime,jdbcType=BIGINT}, #{item.creator,jdbcType=INTEGER}
      </trim>
    </foreach>

  </insert>

  <delete id="delAttachmentId">
    delete from T_ATTACHMENT
    where 1=1
    <if test="attachmentId != null and attachmentId != 0">
      AND ATTACHMENT_ID = #{attachmentId,jdbcType=INTEGER}
    </if>
    <if test="attachmentType != null and attachmentType != ''">
      AND ATTACHMENT_TYPE = #{attachmentType,jdbcType=INTEGER}
    </if>
    <if test="attachmentFunction != null and attachmentFunction != ''">
      AND ATTACHMENT_FUNCTION = #{attachmentFunction,jdbcType=INTEGER}
    </if>
    <if test="relatedId != null and relatedId != ''">
      AND RELATED_ID = #{relatedId,jdbcType=INTEGER}
    </if>
  </delete>

  <select id="queryOutInAttachmentList" resultMap="BaseResultMap" parameterType="com.vedeng.system.model.Attachment">
    select
    ATTACHMENT_ID,
    NAME,
    DOMAIN,
    URI,
    FROM_UNIXTIME(ADD_TIME/1000,'%Y-%m-%d %H:%i:%s') addTimeStr,
    CREATOR
    from T_ATTACHMENT
    where IS_DELETED = 0
    <if test="attachmentId != null and attachmentId != ''">
      AND ATTACHMENT_ID = #{attachmentId,jdbcType=INTEGER}
    </if>
    <if test="relatedId != null and relatedId != ''">
      AND RELATED_ID = #{relatedId,jdbcType=INTEGER}
    </if>
    <if test="attachmentType != null and attachmentType != ''">
      AND ATTACHMENT_TYPE = #{attachmentType,jdbcType=INTEGER}
    </if>
    <if test="attachmentFunction != null and attachmentFunction != ''">
      AND ATTACHMENT_FUNCTION = #{attachmentFunction,jdbcType=INTEGER}
    </if>
  </select>

  <update id="delWarehouseOutAttachment">
    update  T_ATTACHMENT SET IS_DELETED = 1
    where 1=1
    <if test="attachmentId != null and attachmentId != 0">
      and ATTACHMENT_ID = #{attachmentId,jdbcType=INTEGER}
    </if>
  </update>
</mapper>
