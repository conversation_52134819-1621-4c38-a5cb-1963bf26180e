package com.newtask.celery.dao;

import com.newtask.celery.model.TaskJob;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Author: Cherny.chen
 * @Create: 2021/7/12 9:59
 */
public interface TaskJobMapper {

    /**
     * 保存
     *
     * @param taskJob 任务
     */
    void save(TaskJob taskJob);

    /**
     * 更新
     *
     * @param taskJob 任务
     */
    void update(TaskJob taskJob);

    /**
     * 获取待执行队列
     *
     * @return
     */
    List<Integer> listActiveQueue();

    /**
     * 获取待执行队列
     *
     * @param queueKey
     * @return
     */
    List<TaskJob> listTaskByQueue(@Param("queue") Integer queueKey, @Param("lastId") Long lastId);

    /**
     * 根据uuid判断执行成功
     *
     * @param uuid
     * @return
     */
    @Select(" SELECT COUNT(1) FROM T_ASYNC_TASK_JOB WHERE STATUS = 5 AND UUID = #{uuid}")
    int countRunSuccess(@Param("uuid") String uuid);

}
