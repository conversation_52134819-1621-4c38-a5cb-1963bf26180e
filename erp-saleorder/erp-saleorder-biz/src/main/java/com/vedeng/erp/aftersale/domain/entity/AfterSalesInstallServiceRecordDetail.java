package com.vedeng.erp.aftersale.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class AfterSalesInstallServiceRecordDetail extends BaseEntity {
    /**
     * AFTER_SALES_SERVICE_DETAIL_ID
     **/
    private Integer afterSalesServiceDetailId;

    /**
     * 售后安调服务记录主档ID  AFTER_SALES_SERVICE_ID
     **/
    private Integer afterSalesServiceId;

    /**
     * sku  SKU
     **/
    private String sku;

    /**
     * 产品名称  SKU_NAME
     **/
    private String skuName;

    /**
     * 品牌  BAND
     **/
    private String brand;

    /**
     * 型号  MODEL
     **/
    private String model;

    /**
     * 本次服务数量  NUM
     **/
    private Integer num;

    /**
     * SN码  SERIAL_NUMBER
     **/
    private String serialNumber;

    /**
     * 是否删除 0否 1是  IS_DELETE
     **/
    private Integer isDelete;

    /**
     * 售后单商品id  AFTER_SALES_GOODS_ID
     **/
    private Integer afterSalesGoodsId;

    /**
     * 对应的商品的发货时间
     */
    private Long deliveryTime;

    /**
     * 对应的商品的签收时间
     */
    private Long arrivalTime;

    /**
     * 补充码
     * VDERP-14089 新添加字段
     */
    private String supplCode;

}
