package com.vedeng.erp.kingdee.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 销售售后调整单
 * 售后调整单 是对 销售出库单 进行调整
 * @date 2023/2/16 12:53
 */
@Getter
@Setter
public class SaleSettlementAdjustmentEntity extends BaseEntity {

    /**
     * 销售售后调整单id
     */
    private Integer saleSettlementAdjustmentId;

    /**
     * 订单id
     */
    private Integer saleorderId;

    /**
     * 订单编号
     */
    private String saleorderNo;

    /**
     * 售后单id
     */
    private Integer afterSaleId;

    /**
     * 售后单编号
     */
    private String afterSaleNo;

    /**
     * 出库单id
     */
    private Long warehouseOutId;

    /**
     * 出库单号
     */
    private String warehouseOutNo;

    /**
     * 应收总价
     */
    private BigDecimal totalAmount;

    /**
     * 实收总价
     */
    private BigDecimal receivedAmount;

    /**
     * 尾差金额
     */
    private BigDecimal differenceAmount;

    /**
     * 调整单类型 1=销售单调整单 2=售后单调整单
     * 在一个销售订单生命周期中
     * 仅会出现一个销售单调整单，会出现多个售后调整单
     */
    private Integer adjustmentType;

    /**
     * 是否推送过金蝶
     */
    private Boolean isPushed;
}
