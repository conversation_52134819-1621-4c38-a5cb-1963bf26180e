package com.vedeng.op.service.impl;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.pricecenter.dto.PurchaseHistoryDto;
import com.pricecenter.service.imp.BasePriceMaintainServiceImpl;
import com.vedeng.common.http.NewHttpClientUtils;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.op.api.dto.contract.ContractRequest;
import com.vedeng.op.api.dto.contract.ContractSku;
import com.vedeng.op.service.OpService;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class OpServiceImpl implements OpService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BasePriceMaintainServiceImpl.class);

    private static String SUCCESS_CODE = "success";

    @Value("${op.url}")
    private String opUrl;

    private static String FIND_SKU_CONTRACTINFO = "op/getSkuNoAndContractLabel";

    @Override
    public List<ContractSku> findSkuContractInfo(ContractRequest contractRequest) {

        List<ContractSku> contractSkuInfos = encapsulatDefaultPrice(contractRequest);

        try {
            //封装请求参数
            String requestJson = JsonUtils.translateToJson(contractRequest);

            LOGGER.info("OpServiceImpl->findSkuContractInfo 请求参数:" + requestJson);

            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(opUrl + FIND_SKU_CONTRACTINFO,requestJson);

            LOGGER.info("OpServiceImpl->findSkuContractInfo 响应:" + resultJsonObj.toString());

            if(!SUCCESS_CODE.equals(resultJsonObj.get("code")) ){
                return contractSkuInfos;
            }

            Gson gson = new Gson();

            contractSkuInfos = gson.fromJson(resultJsonObj.getJSONArray("data").toString(),
                    new TypeToken<List<ContractSku>>(){}.getType());

        } catch (Exception e) {
            LOGGER.error("查询SKU的合约信息错误",e);
        }

        return contractSkuInfos;
    }

    private List<ContractSku> encapsulatDefaultPrice(ContractRequest contractRequest) {

        List<ContractSku> contractSkus = new ArrayList<>();

        contractRequest.getSkuNos().stream().forEach(skuNo -> {
            ContractSku contractSku  = new ContractSku();
            contractSku.setIsContract(0);
            contractSku.setSkuNo(skuNo);
            contractSkus.add(contractSku);
        });

        return contractSkus;
    }
}
