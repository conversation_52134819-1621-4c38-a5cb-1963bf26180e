package com.vedeng.order.model.adk;

import java.util.Date;

public class TAdkSupplier {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_ADK_SUPPLIER.adk_supplier_id
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	private Integer adkSupplierId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_ADK_SUPPLIER.adk_supplier_code
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	private String adkSupplierCode;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_ADK_SUPPLIER.adk_supplier_name
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	private String adkSupplierName;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_ADK_SUPPLIER.add_no
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	private String addNo;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_ADK_SUPPLIER.add_name
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	private String addName;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_ADK_SUPPLIER.add_time
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	private Date addTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_ADK_SUPPLIER.update_no
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	private String updateNo;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_ADK_SUPPLIER.update_name
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	private String updateName;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_ADK_SUPPLIER.update_time
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	private Date updateTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_ADK_SUPPLIER.status
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	private String status;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_ADK_SUPPLIER.adk_supplier_id
	 * @return  the value of T_ADK_SUPPLIER.adk_supplier_id
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	public Integer getAdkSupplierId() {
		return adkSupplierId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_ADK_SUPPLIER.adk_supplier_id
	 * @param adkSupplierId  the value for T_ADK_SUPPLIER.adk_supplier_id
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	public void setAdkSupplierId(Integer adkSupplierId) {
		this.adkSupplierId = adkSupplierId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_ADK_SUPPLIER.adk_supplier_code
	 * @return  the value of T_ADK_SUPPLIER.adk_supplier_code
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	public String getAdkSupplierCode() {
		return adkSupplierCode;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_ADK_SUPPLIER.adk_supplier_code
	 * @param adkSupplierCode  the value for T_ADK_SUPPLIER.adk_supplier_code
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	public void setAdkSupplierCode(String adkSupplierCode) {
		this.adkSupplierCode = adkSupplierCode;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_ADK_SUPPLIER.adk_supplier_name
	 * @return  the value of T_ADK_SUPPLIER.adk_supplier_name
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	public String getAdkSupplierName() {
		return adkSupplierName;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_ADK_SUPPLIER.adk_supplier_name
	 * @param adkSupplierName  the value for T_ADK_SUPPLIER.adk_supplier_name
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	public void setAdkSupplierName(String adkSupplierName) {
		this.adkSupplierName = adkSupplierName;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_ADK_SUPPLIER.add_no
	 * @return  the value of T_ADK_SUPPLIER.add_no
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	public String getAddNo() {
		return addNo;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_ADK_SUPPLIER.add_no
	 * @param addNo  the value for T_ADK_SUPPLIER.add_no
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	public void setAddNo(String addNo) {
		this.addNo = addNo;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_ADK_SUPPLIER.add_name
	 * @return  the value of T_ADK_SUPPLIER.add_name
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	public String getAddName() {
		return addName;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_ADK_SUPPLIER.add_name
	 * @param addName  the value for T_ADK_SUPPLIER.add_name
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	public void setAddName(String addName) {
		this.addName = addName;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_ADK_SUPPLIER.add_time
	 * @return  the value of T_ADK_SUPPLIER.add_time
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	public Date getAddTime() {
		return addTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_ADK_SUPPLIER.add_time
	 * @param addTime  the value for T_ADK_SUPPLIER.add_time
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	public void setAddTime(Date addTime) {
		this.addTime = addTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_ADK_SUPPLIER.update_no
	 * @return  the value of T_ADK_SUPPLIER.update_no
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	public String getUpdateNo() {
		return updateNo;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_ADK_SUPPLIER.update_no
	 * @param updateNo  the value for T_ADK_SUPPLIER.update_no
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	public void setUpdateNo(String updateNo) {
		this.updateNo = updateNo;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_ADK_SUPPLIER.update_name
	 * @return  the value of T_ADK_SUPPLIER.update_name
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	public String getUpdateName() {
		return updateName;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_ADK_SUPPLIER.update_name
	 * @param updateName  the value for T_ADK_SUPPLIER.update_name
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_ADK_SUPPLIER.update_time
	 * @return  the value of T_ADK_SUPPLIER.update_time
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	public Date getUpdateTime() {
		return updateTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_ADK_SUPPLIER.update_time
	 * @param updateTime  the value for T_ADK_SUPPLIER.update_time
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_ADK_SUPPLIER.status
	 * @return  the value of T_ADK_SUPPLIER.status
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	public String getStatus() {
		return status;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_ADK_SUPPLIER.status
	 * @param status  the value for T_ADK_SUPPLIER.status
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	public void setStatus(String status) {
		this.status = status;
	}
}