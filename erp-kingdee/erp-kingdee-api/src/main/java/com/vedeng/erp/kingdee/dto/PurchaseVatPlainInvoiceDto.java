package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.annotation.BusinessID;
import com.vedeng.infrastructure.kingdee.annotation.KingDeeID;
import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶  采购增值税 普票
 * @date 2022/11/10 09:13
 */
@NoArgsConstructor
@Data
@WriteBackField(needBackField = {"FID","FPURCHASEICENTRY.FEntryID","FPURCHASEICENTRY.F_QZOK_BDDJHID"})
public class PurchaseVatPlainInvoiceDto extends KingDeeMqBaseDto {

    private Integer purchaseVatPlainInvoiceId;

    /**
     * 单据内码
     */
    @WriteBackField
    @KingDeeID
    private String fid;
    /**
     * 业务日期  录票时间
     */
    private String fdate;
    /**
     * 贝登erp对应的单据头ID
     */
    @BusinessID("F_QZOK_BDDJTID")
    private String fQzokBddjtid;
    /**
     * 发票号23456
     */
    private String finvoiceno;
    /**
     *  发票日期 2022-09-22 00:00:00
     */
    private String finvoicedate;
    /**
     * 供应商
     */
    private String fsupplierid;
    /**
     * 单据状态 默认:Z
     */
    private String fdocumentstatus;
    /**
     * 单据类型 CGPTFP01_SYS            采购增值税普通发票
     */
    private String fBillTypeID;
    /**
     * 结算组织 默认101,配置化
     */
    private String fsettleorgid;
    /**
     * 采购组织 默认101,配置化
     */
    private String fpurchaseorgid;
    /**
     * 作废状态 A （正常）
     */
    private String fCancelStatus;
    /**
     * 红蓝字标识 0 蓝字  1 红字
     */
    private String fRedBlue;
    /**
     * 发票代码12345
     */
    private String fQzokFpdm;
    /**
     * 发票类型 1电票 2 纸票
     */
    private String fQzokFplx;
    /**
     * fpurchaseicentry
     */
    private List<PurchaseVatPlainInvoiceDetailDto> FPURCHASEICENTRY;

    /**
     * @组合对象@ 标识是否有费用
     */
    private Boolean hasExpense = Boolean.FALSE;

    public PurchaseVatPlainInvoiceDto(KingDeeBizEnums kingDeeBizEnums) {
        super(kingDeeBizEnums);
    }

    @Override
    public String getFormId() {
        return KingDeeFormConstant.PURCHASE_VAT_PLAIN_INVOICE;
    }
}
