
function exportPayApplyList(){
	checkLogin();
	location.href = page_url + '/report/finance/exportPayApplyList.do?' + $("#search").serialize();
}

function matchBankBill(payApplyId,bankBillId,tranFlow,taskId,bankTag,payType){
	layer.confirm('确认匹配?', {title:'操作确认'},function(index){
			var paymentType = 0;
			if(bankTag == 2){
				//南京银行
				paymentType = 641;
			}else if(bankTag == 3){
				//中国银行
				paymentType = 716;
			}
			var formToken = $("input[name='formToken']").val();
			if(payType == 517){
				var url = page_url + '/finance/buyorder/payApplyPassByBankBill.do';
			}else{
				var url = page_url + '/finance/capitalbill/saveAddAfterCapitalBillForBank.do';
			}
			$.ajax({
				async : false,
				url : url,
				data : {
					"payApplyId" : payApplyId,
					"bankBillId" : bankBillId,
					"tranFlow" : tranFlow,
					"taskId":taskId,
					"paymentType":paymentType,
					"formToken":formToken
				},
				type : "POST",
				dataType : "json",
				success : function(data) {
					if(data.code == 0){
							layer.alert(data.message, { icon : 1});
							window.location.reload();
					}else{
						layer.alert(data.message, { icon : 2});
						window.location.reload();
					}
				},error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			})
		  layer.close(index);
		}); 
}

// add by Tomcat.Hui 2019/9/11 13:05 .Desc: VDERP-1215 付款申请增加批量操作功能. start
//全选
function checkall(obj,name){
	checkLogin();
	if($(obj).is(":checked")){
		$("input[name='"+$(obj).val()+"']").not(':disabled').prop("checked",true);
	}else{
		$("input[name='"+$(obj).val()+"']").not(':disabled').prop('checked',false);
	}
}

//批量确认前置页面
function batchConfirmView(){

	var checkedObj = $("input[name='checkbox_one']:checked");
	var ids = new Array();
	ids.push(-100)
	if(checkedObj && checkedObj.length > 0){
		for (var i = 0; i < checkedObj.length; i++) {
			// 找到被选中的单选框
			if($(checkedObj[i]).is(":checked")){
				var id = $(checkedObj[i]).val();
				ids.push(id);
			}
		}
	}



	layer.open({
		type: 2,
		shadeClose: false,
		area: ['30%', '30%'],
		title: '选择付款银行',
		content: '/order/buyorder/batchConfirmView.do?ids='+ids
	});
}


// 批量确认
function batchConfirm(){
	// 获取所有选中的单选框
	var checkedObj = $("input[name='checkbox_one']:checked");
	if(checkedObj && checkedObj.length > 0){
		// 再次确认是否批量操作
		layer.confirm('确定同意所选数据吗?', {title:'操作确认'},function(index){
			layer.close(index);
			// 循环
			var formToken = $("input[name='formToken']").val();

			var ids = new Array();
			for (var i = 0; i < checkedObj.length; i++) {
				// 找到被选中的单选框
				if($(checkedObj[i]).is(":checked")){
					var id = $(checkedObj[i]).val();
					ids.push(id);
				}
			}

			let validComments = $("#commentParent").val();
			let payVedengBankId = $("#payVedengBankIdParent").val();

			console.log(ids);
			var formToken = $("input[name='formToken']").val();
			$.ajax({
				async : false,
				url :page_url + "/order/buyorder/batchComplementTask.do",
				data : {
					"ids" : ids,
					"formToken" : formToken,
					"validComments": validComments,
					"payVedengBankId": payVedengBankId,
				},
				type : "POST",
				dataType : "json",
				//ajax传递数组使用
				traditional: true,
				success : function(data) {
					if(data.code == 0){
						layer.alert(data.message, { icon : 1});
						sleep(500)
						window.location.href=page_url+"/finance/payapply/getPayApplyListPage.do";
						/*window.location.reload();*/
					}else{
						layer.alert(data.message, { icon : 2});
						window.location.reload();
					}
				},error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			})
			layer.close(index);
			// 页面重新加载
			window.location.reload();
		});
	}
	// 如果没有选中的单选框
	else{
		layer.alert("当前无选中的按钮");
	}
	// add by Tomcat.Hui 2019/9/11 13:05 .Desc: VDERP-1215 付款申请增加批量操作功能. end


}

function sleep(numberMillis) {
	var now = new Date();
	var exitTime = now.getTime() + numberMillis;
	while (true) {
		now = new Date();
		if (now.getTime() > exitTime)
			return;
	}
}


//支付宝结款确认更新资金流水
function  updateCapitalBill(bankBillId,capitalBillId) {
	checkLogin();

	layer.confirm('确认结款?', {title:'操作确认'},function(index){
		$.ajax({
			async:false,
			url:page_url + '/finance/capitalbill/updateCapitalBill.do',
			data:{
				bankBillId:bankBillId,
				capitalBillId:capitalBillId
			},
			type : "POST",
			dataType : "json",
			success:function (result) {
				if(result.code == 0){
					layer.alert(result.message, { icon : 1})
				}else{
					layer.alert(result.message, { icon : 2})
				}
				var currentUrl = $("#currentUrl").val();
				console.log(currentUrl);
				window.location.href=currentUrl;
				layer.close(index);

			},
			error:function(result){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		})
	});
}


//支付宝批量确认
function updateBatchCapitalBill(obj,bankBillId) {
	checkLogin();
	var selectedCapitalBillIds = "";
	$(obj).parent().parent().parent().find('input[name="checkOne"]:checked').each(function () {
		selectedCapitalBillIds += $(this).val()+",";
	});
	console.log(selectedCapitalBillIds);
	if(selectedCapitalBillIds ==""){
		layer.alert("请先选择数据");
		return;
	}

	index=layer.confirm("确定批量匹配流水吗？", {
		btn: ['确定','取消'] //按钮
	}, function(){
		$.ajax({
			type: "POST",
			url: page_url + '/finance/capitalbill/batchUpdateCapitalBill.do',
			data: {"capitalBillIds":selectedCapitalBillIds,"bankBillId":bankBillId},
			dataType:'json',
			success: function(result){
				if(result.code == 0){
					layer.alert(result.message, { icon : 1})
				}else{
					layer.alert(result.message, { icon : 2})
				}
				var currentUrl = $("#currentUrl").val();
				console.log(currentUrl);
				window.location.href=currentUrl;
				layer.close(index);
			},
			error:function(result){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		});
	});
}

