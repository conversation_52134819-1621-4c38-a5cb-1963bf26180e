package com.vedeng.goods.dao;



import com.vedeng.common.model.ResultInfo;
import com.vedeng.goods.model.Unit;

import java.util.List;
import java.util.Map;

/**
 * <b>Description:</b><br> 产品单位管理mapper
 * <AUTHOR>
 * @Note
 * <b>ProjectName:</b> dbcenter
 * <br><b>PackageName:</b> com.vedeng.dao.goods
 * <br><b>ClassName:</b> UnitMapper
 * <br><b>Date:</b> 2017年7月3日 下午3:48:19
 */
public interface UnitMapper {
	
    int deleteByPrimaryKey(Integer unitId);

    int insert(Unit record);

    int insertSelective(Unit record);

    Unit selectByPrimaryKey(Integer unitId);

    int updateByPrimaryKeySelective(Unit record);

    int updateByPrimaryKey(Unit record);
    
    /**
     * <b>Description:</b><br> 保存添加的单位
     * @param unit
     * @return
     * @Note
     * <b>Author:</b> leo.yang
     * <br><b>Date:</b> 2017年7月3日 下午3:53:12
     */
	ResultInfo addUnit(Unit unit);

	/**
	 * <b>Description:</b><br> 获取单位列表（分页）
	 * @param map
	 * @return
	 * @Note
	 * <b>Author:</b> leo.yang
	 * <br><b>Date:</b> 2017年7月3日 下午3:53:30
	 */
	List<Unit> getUnitlistpage(Map<String, Object> map);

	/**
	 * <b>Description:</b><br> 获取全部单位列表（不分页）
	 * @param unit
	 * @return
	 * @Note
	 * <b>Author:</b> leo.yang
	 * <br><b>Date:</b> 2017年7月3日 下午3:53:49
	 */
	List<Unit> getAllUnitList(Unit unit);

	/**
	 * <b>Description:</b><br> 验证单位名称是否重复
	 * @param unit
	 * @return
	 * @Note
	 * <b>Author:</b> leo.yang
	 * <br><b>Date:</b> 2017年7月3日 下午3:54:10
	 */
	Integer vailUnitName(Unit unit);

	/**
	 * <b>Description:</b><br> 验证单位是否被产品使用
	 * @param unit
	 * @return
	 * @Note
	 * <b>Author:</b> leo.yang
	 * <br><b>Date:</b> 2017年7月3日 下午3:54:34
	 */
	Integer vailUnitNameIsUsed(Unit unit);
	
	/**
	 * 
	 * <b>Description: 根据公司ID查询单位list</b><br> 
	 * @param companyId [必填]
	 * @return
	 * <b>Author: Franlin</b>  
	 * <br><b>Date: 2018年10月10日 下午6:51:06 </b>
	 */
	List<Unit> selectUnitListByCompanyId(Integer companyId);
}