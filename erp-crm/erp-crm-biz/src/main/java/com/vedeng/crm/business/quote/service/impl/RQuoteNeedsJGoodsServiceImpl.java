package com.vedeng.crm.business.quote.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.crm.business.quote.domain.entity.QuoteorderNeedsEntity;
import com.vedeng.crm.business.quote.domain.entity.RQuoteorderNeedsJGoodsEntity;
import com.vedeng.crm.business.quote.mapper.RQuoteorderNeedsJGoodsMapper;
import com.vedeng.crm.business.quote.service.RQuoteNeedsJGoodsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class RQuoteNeedsJGoodsServiceImpl implements RQuoteNeedsJGoodsService {

    @Autowired
    private RQuoteorderNeedsJGoodsMapper rQuoteorderNeedsJGoodsMapper;

    @Override
    public void deleteByQuoteGoodsId(Integer quoteGoodsId, CurrentUser currentUser) {
        RQuoteorderNeedsJGoodsEntity deleteDto = new RQuoteorderNeedsJGoodsEntity();
        deleteDto.setIsDelete(1);
        deleteDto.setQuoteorderGoodsId(quoteGoodsId);
        deleteDto.setUpdater(currentUser.getId());
        deleteDto.setModTime(new Date());
        rQuoteorderNeedsJGoodsMapper.updateByQuoteGoodsId(deleteDto);
    }

    @Override
    public int insertSelective( RQuoteorderNeedsJGoodsEntity rQuoteorderNeedsJGoodsEntity ,CurrentUser currentUser) {
        rQuoteorderNeedsJGoodsEntity.setCreator(currentUser.getId());
        rQuoteorderNeedsJGoodsEntity.setAddTime(new Date());
        rQuoteorderNeedsJGoodsEntity.setUpdater(currentUser.getId());
        rQuoteorderNeedsJGoodsEntity.setModTime(rQuoteorderNeedsJGoodsEntity.getAddTime());
        log.info("插入关系表数据：{}", rQuoteorderNeedsJGoodsEntity);
        int i = rQuoteorderNeedsJGoodsMapper.insertSelective(rQuoteorderNeedsJGoodsEntity);
        return i;
    }

    @Override
    public int updateByPrimaryKeySelective(Long id, Long needsId, CurrentUser currentUser) {
        RQuoteorderNeedsJGoodsEntity updateDto = new RQuoteorderNeedsJGoodsEntity();
        updateDto.setRQuoteorderNeedsJGoodsId(id);
        updateDto.setQuoteorderNeedsId(needsId);
        updateDto.setUpdater(currentUser.getId());
        updateDto.setModTime(new Date());
        int i = rQuoteorderNeedsJGoodsMapper.updateByPrimaryKeySelective(updateDto);
        return i;
    }

    @Override
    public RQuoteorderNeedsJGoodsEntity selectByQuoteGoodsId(Integer quoteGoodsId) {
        RQuoteorderNeedsJGoodsEntity rQuoteorderNeedsJGoodsEntity = rQuoteorderNeedsJGoodsMapper.selectByQuoteorderGoodsId(quoteGoodsId);
        return rQuoteorderNeedsJGoodsEntity;
    }

    @Override
    public List<RQuoteorderNeedsJGoodsEntity> selectByQuoteorderId(Integer quoteorderId) {
        List<RQuoteorderNeedsJGoodsEntity> rQuoteorderNeedsJGoodsEntities = rQuoteorderNeedsJGoodsMapper.selectByQuoteorderId(quoteorderId);
        return rQuoteorderNeedsJGoodsEntities;
    }

    @Override
    public void batchDeleteByQuoteorderNeedsId(List<QuoteorderNeedsEntity> extraOldList) {
        rQuoteorderNeedsJGoodsMapper.batchDeleteByQuoteorderNeedsId(extraOldList);
    }
}
