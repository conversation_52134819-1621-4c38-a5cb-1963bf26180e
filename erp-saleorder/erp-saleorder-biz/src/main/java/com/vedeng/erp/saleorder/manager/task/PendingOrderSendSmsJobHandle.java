package com.vedeng.erp.saleorder.manager.task;


import com.vedeng.authorization.model.User;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.DateUtil;
import com.vedeng.erp.saleorder.service.PendingOrderService;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.vo.SaleorderVo;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 待签收列表发送短信
 * <AUTHOR>
 */
@Component
@JobHandler(value = "PendingOrderSendSmsJobHandle")
public class PendingOrderSendSmsJobHandle extends AbstractJobHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(PendingOrderSendSmsJobHandle.class);

    @Autowired
    private PendingOrderService pendingOrderService;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        XxlJobLogger.log("PendingOrderSendSmsJobHandle 系统发送待签收短信 | begin ...............");
        LOGGER.info("PendingOrderSendSmsJobHandle 系统发送待签收短信 | begin ............... 入参：{}", param);
        Saleorder saleorder = new Saleorder();
        Long validTimeStart = null;
        Long validTimeEnd = DateUtil.getNowDateEarlyMorning();
        String[] split = param.split("-");
        if (split != null && split.length == 2 ) {
            validTimeStart = Long.parseLong(split[0]);
            validTimeEnd = Long.parseLong(split[1]);
        } else if (split != null && split.length == 1 ) {
            saleorder.setSaleorderNo(split[0]);
            validTimeEnd = null;
        }

        // saleorder.setValidTime(DateUtil.StringToLong("2021-12-20 00:00:00"));
        // 获取待签收信息: 每日10点，针对功能上线后第一日0：00至发短信前一日24：00的待客户签收信息
        List<SaleorderVo> pendingOrderList = pendingOrderService.getPendingOrderListBySaleorder(saleorder, validTimeStart, validTimeEnd);

        User user = new User();
        user.setUserId(0);
        // TODO: 2022/10/14 VDERP-12290【审计】订单确认、签回单功能修改（隐藏和取消）
        //pendingOrderService.sendSms(pendingOrderList,user, ConfirmRecordSendTypeEnum.SYSTEM_SEND.getCode());

        XxlJobLogger.log("PendingOrderSendSmsJobHandle 系统发送待签收短信 | end ...............");
        LOGGER.info("PendingOrderSendSmsJobHandle 系统发送待签收短信 | end ...............");
        return SUCCESS;
    }
}
