<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>


<div id="app" style="display: none;">
    <el-form :rules="rules" ref="form" label-width="120px" :model="invoiceDto">
        <el-form-item label="发票代码:" prop="invoiceCodeRed">
            <el-input v-model="invoiceDto.invoiceCodeRed"
                      type="text"
                      maxlength="15"
                      style="width: 200px"
                      :disabled="checked">
            </el-input>

            <el-checkbox v-model="checked" @change="fullElectronicInvoiceChange()">数电发票</el-checkbox>
        </el-form-item>
        <el-form-item label="发票号码:" prop="invoiceNoRed">
            <el-input
                    v-model="invoiceDto.invoiceNoRed"
                    type="text"
                    :maxlength="invoiceNoMaxLength"
                    style="width: 200px"></el-input>
        </el-form-item>
        <el-form-item label="发票金额:">
            <span>{{returnInvoiceDto.amount}}</span>
        </el-form-item>
        <el-form-item label="票种:">
            <span>{{returnInvoiceDto.invoiceTypeStr}}</span>
        </el-form-item>
        <el-form-item label="红蓝字:">
            <span style="color: red">红字有效</span>
        </el-form-item>
        <el-form-item label="退票范围:">
            <template v-if="returnInvoiceDto.expenseAfterSalesType==4121">
                <el-radio v-model="radio" :label="1">仅退货商品部分退票</el-radio>
            </template>
            <template v-else>
                <el-radio v-model="radio" :label="1">仅退票商品</el-radio>
            </template>
        </el-form-item>


        <div style="padding-left: 115px">
            <template v-if="returnInvoiceDto.expenseAfterSalesType==4121">
                <el-table
                        border style="width: 100%"
                        :data="invoiceDto.returnInvoiceGoodsDtos"
                <%--:key="true"--%>
                <%--  :summary-method="getSummaries" :key="renderTableAgain"--%>

                >
                    <el-table-column
                            label="序号"
                            type="index"
                            width="50"
                            align="center"
                    >
                    </el-table-column>
                    <el-table-column
                            label="产品名称"
                    >
                        <template slot-scope="scope">
                            <el-link type="primary" @click="viewSku(scope.row.goodsId)">
                                <span>{{ scope.row.goodsName }}</span>
                            </el-link>
                            <br>
                            <span>{{ scope.row.sku }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="品牌"
                            align="center"
                            width="200"
                    >
                        <template slot-scope="scope">
                            <span>{{ scope.row.brandName }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="单价"
                            align="center"
                            width="150"
                    >
                        <template slot-scope="scope">
                            <span>{{ scope.row.price }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="订单数量"
                            align="center"
                            width="150"
                    >
                        <template slot-scope="scope">
                            <span>{{ scope.row.num }}</span>
                        </template>
                    </el-table-column>

                    <el-table-column
                            label="单位"
                            align="center"
                            width="150"
                    >
                        <template slot-scope="scope">
                            <span>{{ scope.row.unit }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="退货数量"
                            align="center"
                            width="150"
                    >
                        <template slot-scope="scope">
                            <span>{{ scope.row.afterReturnNum }}</span>
                        </template>
                    </el-table-column>

                    <el-table-column
                            label="退票数量"
                            align="center"
                            width="150"
                    >
                        <template slot-scope="scope">
                            <span>{{ scope.row.returnNum }}</span>
                        </template>
                    </el-table-column>

                    <el-table-column
                            label="退票金额"
                            align="center"
                            width="250"
                    >
                        <template slot-scope="scope">
                            <el-form-item label-width="0"
                                          :prop="'returnInvoiceGoodsDtos.' + scope.$index "
                                          :rules="rules.amount"
                            >
                                <el-input v-model='scope.row.amount'
                                          @input="handleInputAmountValue(scope.row)"
                                          @change="invoiceAmountChanged(scope.row)"
                                ></el-input>
                            </el-form-item>

                        </template>
                    </el-table-column>
                </el-table>
            </template>
            <template v-else>
                <el-table
                        border style="width: 100%"
                        :data="invoiceDto.returnInvoiceGoodsDtos"
                <%--:key="true"--%>
                <%--  :summary-method="getSummaries" :key="renderTableAgain"--%>

                >
                    <el-table-column
                            label="序号"
                            type="index"
                            width="50"
                            align="center"
                    >
                    </el-table-column>
                    <el-table-column
                            label="产品名称"
                    >
                        <template slot-scope="scope">
                            <el-link type="primary" @click="viewSku(scope.row.goodsId)">
                                <span>{{ scope.row.goodsName }}</span>
                            </el-link>
                            <br>
                            <span>{{ scope.row.sku }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="品牌"
                            align="center"
                            width="200"
                    >
                        <template slot-scope="scope">
                            <span>{{ scope.row.brandName }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="型号"
                            align="center"
                            width="200"
                    >
                        <template slot-scope="scope">
                            <span>{{ scope.row.model }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="单价"
                            align="center"
                            width="150"
                    >
                        <template slot-scope="scope">
                            <span>{{ scope.row.price }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="录票单价"
                            align="center"
                            width="150"
                    >
                        <template slot-scope="scope">
                            <span>{{ scope.row.invoicePrice }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="订单数量"
                            align="center"
                            width="150"
                    >
                        <template slot-scope="scope">
                            <span>{{ scope.row.num }}</span>
                        </template>
                    </el-table-column>

                    <template v-if="returnInvoiceDto.expenseAfterSalesType==4121">
                        <el-table-column
                                label="单位"
                                align="center"
                                width="150"
                        >
                            <template slot-scope="scope">
                                <span>{{ scope.row.unit }}</span>
                            </template>
                        </el-table-column>
                    </template>

                    <el-table-column
                            label="退票数量"
                            align="center"
                            width="150"
                    >
                        <template slot-scope="scope">
                            <span>{{ scope.row.returnNum }}</span>
                        </template>
                    </el-table-column>

                    <el-table-column
                            label="退票总额"
                            align="center"
                            width="250"
                    >
                        <template slot-scope="scope">
                            <el-form-item label-width="0"
                                          :prop="'returnInvoiceGoodsDtos.' + scope.$index "
                                          :rules="rules.amount"
                            >
                                <el-input v-model='scope.row.amount'
                                          @input="handleInputAmountValue(scope.row)"
                                          @change="invoiceAmountChanged(scope.row)"
                                ></el-input>
                            </el-form-item>

                        </template>
                    </el-table-column>
                </el-table>
                <template  v-if="returnInvoiceDto.expenseAfterSalesType==4122">
                    <br>
                    <el-alert
                            title="红票录入后，系统在自动生成红票记录同时，将自动使用录票单价、数量及售后提交人员所提供的蓝字发票，重新录入至原采购订单，请知悉！"
                            style="width: 1000px"
                            type="warning"
                            show-icon>
                    </el-alert>
                </template>
            </template>
        </div>
        <br>


        <el-form-item>
            <el-button type="primary" @click="onSubmit('form')" :disabled="formDisabled">提交</el-button>
        </el-form-item>
    </el-form>
</div>
<script src="${pageContext.request.contextPath}/static/api/buyorder/returnInvoice.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript">

    const viewInfo = {
        invoiceNo: '${viewInfo.invoiceNo}',
        invoiceCode: '${viewInfo.invoiceCode}',
        expenseAfterSalesId:'${viewInfo.expenseAfterSalesId}'
    }

    const sendThis = (_this) => {
        vm = _this;
    }

    new Vue({
        el: '#app',
        data() {
            var checkPrice = (rule, value, callback) => {
                if (value.amount === undefined || value.amount === '' || value.amount === null) {
                    callback(new Error("退票金额不可为空"));
                }
                if (Number(value.totalAmount) === 0) {
                    callback(new Error("单价不可为0"));
                }
                if (Number(value.amount) - Number(value.totalAmount)>1||Number(value.amount) - Number(value.totalAmount)<-1) {
                    callback(new Error("金额输入有误"));
                }

                callback();
            }
            return {
                formDisabled: false,
                radio:1,
                //表单校验
                invoiceNoMaxLength: 8,
                rules: {
                    invoiceNoRed: [
                        {max: 8, min: 8, message: '发票号码应为8个字符', trigger: 'blur'},
                        {required:true, message: '发票号不可为空', trigger: 'blur'},
                        {
                            pattern: /^[0-9]*$/,
                            message: '只能输入数字',
                            trigger: 'blur'
                        }
                    ],
                    invoiceCodeRed: [
                        {max: 15, message: '最大支持输入15个字符', trigger: 'blur'},
                        {required:true, message: '发票代码不可为空', trigger: 'blur'},
                        {
                            pattern: /^[0-9]*$/,
                            message: '只能输入数字',
                            trigger: 'blur'
                        }
                    ],
                    amount: [
                        // {required: true, message: "单价不可为空", trigger: 'blur'},
                        {validator: checkPrice, trigger: 'blur'}
                    ],
                },
                invoiceDto: {
                    invoiceNo: '',
                    invoiceCode: '',
                    expenseAfterSalesId: '',
                    returnInvoiceGoodsDtos: [],
                    invoiceProperty: 1
                },
                checked: false,

                returnInvoiceGoods: [],
                returnInvoiceDto: {
                    returnInvoiceGoodsDtos: [],
                    invoiceCode: '',
                    invoiceNo: '',
                    expenseAfterSalesId: ''
                }

            }
        },

        mounted() {
            loadingApp();
            sendThis(this);
        },
        created() {
            this.initData();
        },
        methods: {
            initData: async function () {
                if (viewInfo.invoiceCode != null && viewInfo.invoiceCode != "" && viewInfo.invoiceNo != null && viewInfo.invoiceNo != ""
                    && viewInfo.expenseAfterSalesId != null && viewInfo.expenseAfterSalesId != "") {
                    await getInvoiceGoodsData(viewInfo).then(res => {
                        debugger
                        // this.returnInvoiceDto = JSON.parse(JSON.stringify(res.data.data));
                        this.returnInvoiceDto = res.data.data;
                        if (this.returnInvoiceDto.expenseAfterSalesId != viewInfo.expenseAfterSalesId) {
                            this.$message({
                                message: '当前待退票记录以完成请刷新售后详情页',
                                type: 'error',
                            });
                            this.formDisabled = true;
                        }

                        this.returnInvoiceDto.returnInvoiceGoodsDtos.forEach(item => {
                            item.amount = item.totalAmount;
                            item.amount = parseFloat(item.amount).toFixed(2);
                            if (this.returnInvoiceDto.expenseAfterSalesType == 4122) {
                                item.invoicePrice = parseFloat(Number(item.amount) / Number(item.returnNum)).toFixed(8);
                            }
                        });
                        this.invoiceDto = JSON.parse(JSON.stringify(this.returnInvoiceDto));
                        if (this.returnInvoiceDto.expenseAfterSalesType == 4121) {
                            this.invoiceDto.invoiceNoRed = '';
                            this.invoiceDto.invoiceCodeRed = '';
                        }

                    });

                }
            },
            // 是否数电发票变更事件监听
            fullElectronicInvoiceChange() {
                if (this.checked) {
                    // 是数电
                    this.invoiceDto.invoiceProperty = 3;
                    this.invoiceDto.invoiceCodeRed = '0000000000';
                    this.rules.invoiceNoRed[0].max = 20;
                    this.rules.invoiceNoRed[0].min = 20;
                    this.rules.invoiceNoRed[0].message = '发票号码应为20个字符';
                    this.invoiceNoMaxLength = 20;
                } else {
                    // 非数电
                    this.invoiceDto.invoiceProperty = 1;
                    this.invoiceDto.invoiceCodeRed = '';
                    this.rules.invoiceNoRed[0].max = 8;
                    this.rules.invoiceNoRed[0].min = 8;
                    this.rules.invoiceNoRed[0].message = '发票号码应为8个字符';
                    this.invoiceNoMaxLength = 8;
                }
            },

            //处理商品金额输入
            handleInputAmountValue(row) {
                row.amount = this.limitInputNumberDecimal(row.amount);
            },
            //限制数字和小数
            limitInputNumberDecimal(value) {
                // debugger
                if (value != undefined) {
                    value = value.replace(/[^\d.]/g, '') // 只能输入数字和.
                    value = value.replace(/^\./g, '')  //第一个字符不能是.
                    value = value.replace(/\.{2,}/g, '.') // 不能连续输入.
                    value = value.replace(/(\.\d+)\./g, '$1') // .后面不能再输入.
                    value = value.replace(/^0+(\d)/, '$1') // 第一位0开头，0后面为数字，则过滤掉，取后面的数字
                    value = value.replace(/(\d{10})\d*/, '$1') // 最多保留15位整数
                    value = value.replace(/(\.\d{2})\d*/, '$1')// 最多保留2位小数
                }
                return value;
            },
            invoiceAmountChanged(row) {
                debugger
                if (row.amount) {
                    row.amount = Number(row.amount);
                    row.amount = parseFloat(row.amount).toFixed(2);
                    row.invoicePrice = parseFloat(Number(row.amount) / Number(row.returnNum)).toFixed(8);
                }
            },
            viewSku(skuid) {
                openTab("sku信息", '/goods/vgoods/viewSku.do?skuId=' + skuid);
            },
            closeThis() {
                window.parent.closableTab.close($(window.parent.document).find('[role=presentation].active .glyphicon.small'), window.parent.closableTab.resizeMove);
            },
            onSubmit(form) {
                this.$refs[form].validate((valid) => {
                    if (valid) {
                        debugger
                        this.formDisabled = true;
                        add(this.invoiceDto).then(res => {
                            if (res.data.code == 0) {
                             debugger

                                this.$message({
                                    message: '录票成功',
                                    type: 'success',
                                    onClose: () => {
                                        // openTab("报价单详情", '/order/quote/getQuoteDetail.do?quoteorderId=' + res.data.data.quoteorderDto.quoteorderId + '&viewType=2')
                                        this.closeThis()
                                    }
                                });

                            } else {
                                this.$message({
                                    message: res.data.message,
                                    type: 'error',
                                });
                                this.formDisabled = false;
                            }
                        });


                    }
                })
            }

        },
        computed: {}


    })
</script>
