package com.wms.model.po;

import java.util.Date;

public class WmsSendOrder {
    /**   WMS_SEND_ORDER_ID **/
    private Integer wmsSendOrderId;

    /** 订单类型  0销售单  1 采购单    ORDER_TYPE **/
    private Integer orderType;

    /** 订单号  ORDER_NO **/
    private String orderNo;

    /** 订单id  ORDER_ID **/
    private Integer orderId;

    /** 下发状态  0未下发  1以下发  SEND_STATUS **/
    private Integer sendStatus;

    /** 创建时间  ADD_TIME **/
    private Date addTime;

    /** 更新时间  MODE_TIME **/
    private Date modeTime;

    /** 0 未删除  1删除  IS_DELETE **/
    private Integer isDelete;

    /** 创建人  CREATOR **/
    private Integer creator;

    /** 更新人  UPDATER **/
    private Integer updater;

    /**     WMS_SEND_ORDER_ID   **/
    public Integer getWmsSendOrderId() {
        return wmsSendOrderId;
    }

    /**     WMS_SEND_ORDER_ID   **/
    public void setWmsSendOrderId(Integer wmsSendOrderId) {
        this.wmsSendOrderId = wmsSendOrderId;
    }

    /**   订单类型  0销售单  1 采购单    ORDER_TYPE   **/
    public Integer getOrderType() {
        return orderType;
    }

    /**   订单类型  0销售单  1 采购单    ORDER_TYPE   **/
    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    /**   订单号  ORDER_NO   **/
    public String getOrderNo() {
        return orderNo;
    }

    /**   订单号  ORDER_NO   **/
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    /**   订单id  ORDER_ID   **/
    public Integer getOrderId() {
        return orderId;
    }

    /**   订单id  ORDER_ID   **/
    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    /**   下发状态  0未下发  1以下发  SEND_STATUS   **/
    public Integer getSendStatus() {
        return sendStatus;
    }

    /**   下发状态  0未下发  1以下发  SEND_STATUS   **/
    public void setSendStatus(Integer sendStatus) {
        this.sendStatus = sendStatus;
    }

    /**   创建时间  ADD_TIME   **/
    public Date getAddTime() {
        return addTime;
    }

    /**   创建时间  ADD_TIME   **/
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**   更新时间  MODE_TIME   **/
    public Date getModeTime() {
        return modeTime;
    }

    /**   更新时间  MODE_TIME   **/
    public void setModeTime(Date modeTime) {
        this.modeTime = modeTime;
    }

    /**   0 未删除  1删除  IS_DELETE   **/
    public Integer getIsDelete() {
        return isDelete;
    }

    /**   0 未删除  1删除  IS_DELETE   **/
    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    /**   创建人  CREATOR   **/
    public Integer getCreator() {
        return creator;
    }

    /**   创建人  CREATOR   **/
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**   更新人  UPDATER   **/
    public Integer getUpdater() {
        return updater;
    }

    /**   更新人  UPDATER   **/
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }
}