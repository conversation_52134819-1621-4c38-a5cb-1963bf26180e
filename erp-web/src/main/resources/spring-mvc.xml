<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:mvc="http://www.springframework.org/schema/mvc" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:p="http://www.springframework.org/schema/p" xmlns:context="http://www.springframework.org/schema/context"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:apollo="http://www.ctrip.com/schema/apollo"
	xsi:schemaLocation="http://www.springframework.org/schema/beans 
		http://www.springframework.org/schema/beans/spring-beans.xsd 
		http://www.springframework.org/schema/context 
		http://www.springframework.org/schema/context/spring-context.xsd 
		http://www.springframework.org/schema/mvc 
		http://www.springframework.org/schema/mvc/spring-mvc.xsd
		http://www.springframework.org/schema/aop
		http://www.springframework.org/schema/aop/spring-aop.xsd
		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
		http://www.ctrip.com/schema/apollo http://www.ctrip.com/schema/apollo.xsd">

	<apollo:config/>

	<!-- aop扫描器 -->
	<context:component-scan base-package="com.vedeng.erp" use-default-filters="false">
		<context:include-filter type="annotation" expression="org.springframework.stereotype.Controller" />
		<context:include-filter type="annotation" expression="org.springframework.web.bind.annotation.RestController" />
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Component" />
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Repository" />
		<context:exclude-filter type="annotation" expression="javax.annotation.Resource" />
	</context:component-scan>

	<!-- 注解Controller扫描器 -->
	<context:component-scan base-package="com.**.controller,com.**.api" use-default-filters="false">
		<context:include-filter type="annotation" expression="org.springframework.stereotype.Controller" />
		<context:include-filter type="annotation" expression="org.springframework.web.bind.annotation.RestController" />
	</context:component-scan>

	<!-- aop注解-->
	<aop:aspectj-autoproxy proxy-target-class="true"/>




	<mvc:annotation-driven>
		<mvc:message-converters register-defaults="true">
			<!-- 配置Fastjson支持 -->
			<bean class="org.springframework.http.converter.json.MappingJackson2HttpMessageConverter">
				<property name="supportedMediaTypes">
					<list>
						<value>text/html;charset=UTF-8</value>
						<value>application/json</value>
					</list>
				</property>
			</bean>
		</mvc:message-converters>
	</mvc:annotation-driven>


	<!-- vue编写的页面跳转的试图解析器-->
	<bean class="org.springframework.web.servlet.view.InternalResourceViewResolver">
		<property name="viewClass" value="org.springframework.web.servlet.view.JstlView" />
		<!--
            支持通配符，如果返回的视图名的开头是vue，则使用该视图解析器
            也可以定义为 *vue*：表示包含vue的
         -->
		<property name="viewNames" value="vue*"/>
		<property name="prefix" value="/WEB-INF/"/>
		<property name="suffix" value=".jsp"/>
	</bean>


	<!-- 前后缀配置 -->
	<bean class="org.springframework.web.servlet.view.InternalResourceViewResolver" p:prefix="/WEB-INF/jsp/" p:suffix=".jsp"/>


	<!-- 注册XmlViewResolver，用于iReport & JasperReports报表生成 -->
	<bean id="jasperReportResolver" class="org.springframework.web.servlet.view.XmlViewResolver">
		<property name="order">
			<value>0</value>
		</property>
		<property name="location">
			<value>WEB-INF/ireport/jasper-defs.xml</value>
		</property>
	</bean>

	<!-- 上传 -->
	<bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
		<property name="defaultEncoding" value="utf-8"></property>
		<property name="maxUploadSize" value="10485760000"></property>
		<property name="maxInMemorySize" value="40960"></property>
		<property name="resolveLazily" value="true"></property>
	</bean>
	<!-- 权限拦截器 -->
	<bean id="securityInterceptor" class="com.vedeng.common.shiro.SecurityInterceptor"/>

	<!-- static 缓存10天 每次发布会自动更新常用的 静态文件-->
	<mvc:resources mapping="/static/**" location="/static/,classpath:/static/ezadmin/" cache-period="864000" />
	<mvc:resources mapping="/webjars/**" location="classpath:/META-INF/resources/webjars/" cache-period="864000" />


	<!-- 引入 activiti 流程模块配置文件 -->
	<import resource="classpath:activiti.cfg.xml" />

	<!-- 引入 API标准化模块配置文件 -->
	<import resource="classpath:META-INF/spring/api-standard-context.xml" />

	<!-- 拦截器，拦截*.do请求 -->
	<mvc:interceptors>
		<mvc:interceptor>
			<mvc:mapping path="/**"/>
			<mvc:exclude-mapping path="/system/**" />
			<mvc:exclude-mapping path="/static/**" />
			<mvc:exclude-mapping path="/webjars/**" />
			<mvc:exclude-mapping path="/checkSession.do" />
			<mvc:exclude-mapping path="/pay/**" />
			<mvc:exclude-mapping path="/mjx/**" />
			<mvc:exclude-mapping path="/yxg/**" />
			<mvc:exclude-mapping path="/order/saleorder/checkAptitudeForYxg.do" />
			<mvc:exclude-mapping path="/el/**" />
			<mvc:exclude-mapping path="/wms/**" />
			<mvc:exclude-mapping path="/aftersale/order/**" />
			<mvc:exclude-mapping path="/userCenter/**" />
			<mvc:exclude-mapping path="/checkpreload.html" />
			<mvc:exclude-mapping path="/checkInnerWeb.html"/>
			<mvc:exclude-mapping path="/batSyncToPhp.html" />
			<mvc:exclude-mapping path="/nopower.do" />
			<mvc:exclude-mapping path="/finance/invoice/hx_invoice/**"/>
			<mvc:exclude-mapping path="/system/call/pushVoiceWxMp3.do"/>
			<mvc:exclude-mapping path="/order/saleorder/saveBDAddSaleorder.do"/>
			<mvc:exclude-mapping path="/order/saleorder/saveYgyxBuyorderToErpSaleOrder.do"/>
			<mvc:exclude-mapping path="/trader/customer/saveMjxContactAdders.do"/>
			<bean id="currentLimitInterceptor" class="com.vedeng.common.shiro.CurrentLimitInterceptor"></bean>
		</mvc:interceptor>

		<!-- 权限拦截器   ：暂时注释掉，不能删-->
		<mvc:interceptor>
			<mvc:mapping path="/**" />
			<mvc:exclude-mapping path="/dologin.do" />
			<mvc:exclude-mapping path="/login.do" />
            <mvc:exclude-mapping path="/logout.do" />
            <mvc:exclude-mapping path="/code.do" />
            <mvc:exclude-mapping path="/checkSession.do" />
            <mvc:exclude-mapping path="/index.do" />
            <mvc:exclude-mapping path="/menu.do" />
            <mvc:exclude-mapping path="/welcome.do" />
            <mvc:exclude-mapping path="/selectorg.do" />
			<mvc:exclude-mapping path="/notAssignPosition.do" />
            <mvc:exclude-mapping path="/changeorg.do" />
            <mvc:exclude-mapping path="/savechangeorg.do" />
            <mvc:exclude-mapping path="/saveselectorg.do" />
            <mvc:exclude-mapping path="/fileUpload/ajaxFileUpload.do" />
            <mvc:exclude-mapping path="/fileUpload/uploadFile2Oss.do" />
            <mvc:exclude-mapping path="/fileUpload/ueditFileUpload.do" />

			<mvc:exclude-mapping path="/system/call/pushVoiceWxMp3.do"/>

			<mvc:exclude-mapping path="/system/message/getAllMessageNoread.do" />
			<mvc:exclude-mapping path="/system/message/queryNoReadMsgNum.do" />
			<mvc:exclude-mapping path="/phoneticTranscription/phonetic/viewContent.do" />
			<mvc:exclude-mapping path="/phoneticTranscription/phonetic/addComments.do" />
			<mvc:exclude-mapping path="/tradermsg/sendMsg/sendTraderMsg.do" />
			<mvc:exclude-mapping path="/tradermsg/sendMsg/sendWebaccountCertificateMsg.do" />
			<mvc:exclude-mapping path="/order/saleorder/saveBDAddSaleorder.do" />
			<mvc:exclude-mapping path="/order/saleorder/saveYgyxBuyorderToErpSaleOrder.do" />
			<mvc:exclude-mapping path="/order/saleorder/deleteBdOrder.do" />
			<mvc:exclude-mapping path="/order/saleorder/updateBDSaleStatus.do" />
			<mvc:exclude-mapping path="/goods/vgoods/viewSku.do" />
			<mvc:exclude-mapping path="/goods/vgoods/viewSkuBySkuId.do" />

			<mvc:exclude-mapping path="/goods/vgoods/viewSpu.do" />
			<mvc:exclude-mapping path="/goods/vgoods/static/getCostPrice.do" />
			<mvc:exclude-mapping path="/vgoods/operate/handleOldData.do" />
			<mvc:exclude-mapping path="/trader/customer/saveMjxContactAdders.do" />
			<mvc:exclude-mapping path="/trader/customer/tyc/check.do" />
			<mvc:exclude-mapping path="/order/saleorder/searchOrderInfo.do" />
			<mvc:exclude-mapping path="/order/saleorder/updateOrderDeliveryStatus.do" />
			<mvc:exclude-mapping path="/warehouse/warehousesout/updateWarehouseProblem.do" />
			<mvc:exclude-mapping path="/warehouse/warehouses/insertNewStock.do" />
			<mvc:exclude-mapping path="/warehouse/warehouses/updateSaleorderOccupyNum.do" />
			<mvc:exclude-mapping path="/order/saleorder/queryOutBoundQuantity.do" />
			<mvc:exclude-mapping path="/order/hc/updateorderstatus.do" />
			<mvc:exclude-mapping path="/aftersales/webaccount/certificate/update.do" />
			<mvc:exclude-mapping path="/aftersales/webaccount/certificate/add.do" />
			<mvc:exclude-mapping path="/trader/customer/aptitude/status.do" />
			<mvc:exclude-mapping path="/trader/customer/yxg/status/sync.do" />
			<mvc:exclude-mapping path="/trader/customer/addYxgTrader.do" />
			<mvc:exclude-mapping path="/trader/customer/yxgStartFinanceCheck.do" />


			<mvc:exclude-mapping path="/producter/productering.do" />
			<mvc:exclude-mapping path="/warehouse/warehouses/getErrorStockGoodsList.do" />
			<mvc:exclude-mapping path="/warehouse/warehouses/updateWarehouseLogIsUse.do" />
			<mvc:exclude-mapping path="/order/bussinesschance/getBussinessChanceAndQuoteInfo.do" />
			<mvc:exclude-mapping path="/order/saleorder/getCouponOrderInfoByCouponId.do" />
			<mvc:exclude-mapping path="/order/saleorder/getCouponOrderDetailByCouponcode.do" />
			<mvc:exclude-mapping path="/warehouse/warehouses/initOrderOccupy.do" />
			<mvc:exclude-mapping path="/warehouse/warehouses/getLogicalStockInfo.do" />
			<mvc:exclude-mapping path="/warehouse/warehousesout/uplodeOutOrder.do" />
			<mvc:exclude-mapping path="/warehouse/warehousesout/printOutOrder.do" />

			<mvc:exclude-mapping path="/firstengage/baseinfo/doRegistrationImg.do" />
			<mvc:exclude-mapping path="/firstengage/baseinfo/doRegistnumpdfImg.do" />

			<!--对外提供接口服务-->
			<mvc:exclude-mapping path="/mjx/**" />
			<mvc:exclude-mapping path="/yxg/**" />
			<mvc:exclude-mapping path="/order/saleorder/checkAptitudeForYxg.do" />
			<mvc:exclude-mapping path="/orderstream/saleorder/contractReturnSaveForOtherErp.do" />

			<mvc:exclude-mapping path="/el/**" />
			<mvc:exclude-mapping path="/wms/**" />
			<mvc:exclude-mapping path="/aftersale/order/**" />
			<mvc:exclude-mapping path="/userCenter/**" />
			<mvc:exclude-mapping path="/checkpreload.html" />
			<mvc:exclude-mapping path="/checkInnerWeb.html"/>
			<mvc:exclude-mapping path="/batSyncToPhp.html" />
			<mvc:exclude-mapping path="/nopower.do" />
			<mvc:exclude-mapping path="/order/saleorder/queryOutBoundQuantity.do" />
			<mvc:exclude-mapping path="/aftersales/order/invoiceGoodList.do"/>
			<mvc:exclude-mapping path="/system/user/resetPassword.do"/>
			<mvc:exclude-mapping path="/warehouse/warehousesout/getPurchaseTime.do"/>
			<mvc:exclude-mapping path="/warehouse/warehousesout/getDeliverTime.do"/>

			<mvc:exclude-mapping path="/warehouse/warehousesout/getInLibraryBarcode.do" />
			<mvc:exclude-mapping path="/goods/vgoods/uplodeSkuDeliveryRange.do" />

			<mvc:exclude-mapping path="/goods/vgoods/saveUplodeSkuDeliveryRange.do" />
			<mvc:exclude-mapping path="/system/list/**"/>
			<mvc:exclude-mapping path="/order/quote/consult/**"/>

			<mvc:exclude-mapping path="/fileUpload/ajaxFileUploadAuthorization.do" />
			<mvc:exclude-mapping path="/order/quote/canApply.do" />
			<mvc:exclude-mapping path="/order/quote/next.do" />
			<mvc:exclude-mapping path="/order/quote/authorizationExamine.do"/>
			<mvc:exclude-mapping path="/order/quote/authorizationPreview.do" />
			<mvc:exclude-mapping path="/pay/**" />



			<mvc:exclude-mapping path="/category/base/getSecondCategoryList.do" />

			<mvc:exclude-mapping path="/category/base/commitCategory.do" />


			<mvc:exclude-mapping path="/category/base/choiceCategory.do" />

			<mvc:exclude-mapping path="/category/base/getAllLevelCategoryByIdList.do" />
			<mvc:exclude-mapping path="/warehouse/warehouses/getWmsStock.do" />


			<mvc:exclude-mapping path="/order/saleorder/deliveryNoticeAddPage.do"/>
			<mvc:exclude-mapping path="/order/saleorder/saveDeliveryNotice.do"/>
			<mvc:exclude-mapping path="/order/saleorder/changeStatusDeliveryNotice.do"/>
			<mvc:exclude-mapping path="/order/saleorder/deliveryNoticeDetailPage.do"/>
			<mvc:exclude-mapping path="/order/saleorder/deliveryNoticeEditPage.do"/>
			<mvc:exclude-mapping path="/finance/invoice/hx_invoice/**"/>
			<mvc:exclude-mapping path="/order/quote/authorizationExamine.do"/>

			<mvc:exclude-mapping path="/order/saleorder/printOrder.do"/>
			<mvc:exclude-mapping path="/order/saleorder/editApplyValidSaleorder.do"/>
			<mvc:exclude-mapping path="/order/buyorder/editApplyValidBuyorder.do"/>
			<mvc:exclude-mapping path="/trader/supplier/syncDocSupplierToFtp.do"/>
			<mvc:exclude-mapping path="/order/buyorder/indexPendingPurchaseExport.do"/>

			<mvc:exclude-mapping path="/order/aftersales/getEffectAfterSalePolicy.do"/>
			<mvc:exclude-mapping path="/buyorder/listGoodsOnWayInfo.do"/>
			<mvc:exclude-mapping path="/trader/supplier/addTraderSupplier.do" />
			<mvc:exclude-mapping path="/api/tyc/detail.do"/>
			<mvc:exclude-mapping path="/api/tyc/list.do"/>

			<ref bean="securityInterceptor"/>
		</mvc:interceptor>

		<mvc:interceptor>
			<mvc:mapping path="/**" />
			<bean class="com.vedeng.common.validator.FormTokenInterceptor" />
		</mvc:interceptor>

		<mvc:interceptor>
			<!-- 设置拦截所有请求 -->
			<mvc:mapping path="/**/*.do"/>
			<!-- 设置不拦截静态文件 -->
			<mvc:exclude-mapping path="/**/static/**/*.*" />
			<bean class="com.vedeng.core.trace.interceptor.MdcInterceptor" />
		</mvc:interceptor>

	</mvc:interceptors>


</beans>