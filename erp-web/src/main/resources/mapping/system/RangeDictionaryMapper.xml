<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.system.dao.RangeDictionaryMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.system.model.RangeDictionary">
        <id column="ID" jdbcType="INTEGER" property="id" />
        <result column="NAME" jdbcType="VARCHAR" property="name" />
        <result column="VALUE" jdbcType="VARCHAR" property="value" />
        <result column="LEFT_CLOSURE" jdbcType="INTEGER" property="leftClosure" />
        <result column="RIGHT_CLOSURE" jdbcType="INTEGER" property="rightClosure" />
        <result column="MIN" jdbcType="INTEGER" property="min" />
        <result column="MAX" jdbcType="INTEGER" property="max" />
    </resultMap>


    <select id="getAllDict" resultMap="BaseResultMap">
        SELECT
            *
        FROM T_RANGE_DICTIONARY T
    </select>

</mapper>