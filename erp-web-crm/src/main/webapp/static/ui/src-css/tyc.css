.vd-ui-tianyancha-wrap .tyc-trader-tip {
  display: flex;
  align-items: center;
  margin-top: 5px;
}
.vd-ui-tianyancha-wrap .tyc-trader-tip .vd-ui_icon {
  line-height: 1;
  margin-right: 5px;
  font-size: 16px;
}
.vd-ui-tianyancha-wrap .tyc-trader-tip.tip-success {
  color: #13BF13;
}
.vd-ui-tianyancha-wrap .tyc-trader-tip.tip-warn {
  color: #FF6600;
}
.vd-ui-tianyancha {
  position: relative;
  display: flex;
}
.vd-ui-tianyancha .vd-ui-tyc-input {
  width: 300px;
  position: relative;
}
.vd-ui-tianyancha .vd-ui-tyc-salename {
  position: absolute;
  right: 1px;
  color: #999;
  line-height: 28px;
  top: 1px;
  background: #fff;
  padding: 0 10px;
  border-radius: 3px;
}
.vd-ui-tianyancha .vd-ui-tyc-right {
  height: 30px;
  display: flex;
  align-items: center;
}
.vd-ui-tianyancha .vd-ui-tyc-right .vd-ui-tyc-search-icon {
  display: flex;
  align-items: center;
  margin-left: 10px;
  cursor: pointer;
  color: #09F;
}
.vd-ui-tianyancha .vd-ui-tyc-right .vd-ui-tyc-search-icon .icon-search {
  font-size: 16px;
}
.vd-ui-tianyancha .vd-ui-tyc-right .vd-ui-tyc-search-icon > span {
  margin-left: 5px;
}
.vd-ui-tianyancha .vd-ui-tyc-right .vd-ui-tyc-search-icon:hover {
  color: #f60;
}
.vd-ui-tianyancha .vd-ui-tyc-right .vd-ui-tyc-warn-icon {
  position: relative;
}
.vd-ui-tianyancha .vd-ui-tyc-right .vd-ui-tyc-warn-icon .icon-caution1 {
  font-size: 16px;
  color: #F60;
  margin-left: 10px;
}
.vd-ui-tianyancha .vd-ui-tyc-right .vd-ui-tyc-warn-icon .icon-caution1:hover + .vd-ui-tyc-icon-tip {
  display: block;
}
.vd-ui-tianyancha .vd-ui-tyc-right .vd-ui-tyc-warn-icon .vd-ui-tyc-icon-tip {
  display: none;
  position: absolute;
  transition: display 0.3s ease;
  top: -50px;
  left: -3px;
  max-width: 600px;
  width: 100vw;
}
.vd-ui-tianyancha .vd-ui-tyc-right .vd-ui-tyc-warn-icon .vd-ui-tyc-icon-tip .tip-txt {
  position: relative;
  max-width: 600px;
  max-width: max-content;
  max-height: 110px;
  padding: 10px 15px;
  background-color: #fff;
  font-size: 12px;
  border-radius: 3px;
  box-shadow: rgba(0, 0, 0, 0.12) 0px 3px 6px;
  box-sizing: border-box;
}
.vd-ui-tianyancha .vd-ui-tyc-right .vd-ui-tyc-warn-icon .vd-ui-tyc-icon-tip .tip-txt::after {
  content: "";
  display: block;
  width: 0;
  height: 0;
  border-right: 5px solid transparent;
  border-left: 5px solid transparent;
  border-top: 5px solid #fff;
  border-bottom: 5px solid transparent;
  position: absolute;
  top: 38px;
  left: 16px;
}
.vd-ui-tianyancha .vd-ui-tyc-right .icon-tyc {
  font-size: 16px;
  width: 16px;
  height: 16px;
  display: inline-block;
  background: url(../image/tyc.png) no-repeat;
  background-size: 16px;
  margin-left: 10px;
  cursor: pointer;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related {
  max-height: 370px;
  overflow-y: auto;
  border-radius: 3px;
  background: #fff;
  border: solid 1px #BABFC2;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
  position: absolute;
  top: 30px;
  z-index: 9;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related .loading {
  color: #666;
  height: 39px;
  line-height: 37px;
  padding: 0px 10px;
  overflow: hidden;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related .loading i {
  animation: loading 1.8s linear infinite;
  display: inline-block;
  position: relative;
  top: 1px;
  font-size: 16px;
  margin-right: 5px;
  color: #09F;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related .failed-li {
  height: 39px;
  line-height: 39px;
  padding: 0px 10px;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related .failed-li i {
  position: relative;
  top: 2px;
  font-size: 16px;
  color: #E64545;
  margin-right: 5px;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related .failed-li .reload {
  color: #09f;
  cursor: pointer;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related .empty-li {
  height: 39px;
  line-height: 39px;
  padding: 0px 10px;
  text-align: center;
  color: #999;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related .search-related-list {
  padding: 5px 10px;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related .search-related-list .local-data {
  font-size: 12px;
  color: #999;
  line-height: 30px;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related .search-related-list .sr-item {
  padding: 6px 0;
  display: flex;
  justify-content: space-between;
  cursor: pointer;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related .search-related-list .sr-item .sr-item-left {
  flex: 1;
  min-width: 0;
  margin-right: 10px;
  display: flex;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related .search-related-list .sr-item .sr-item-left .icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  margin-right: 5px;
  margin-top: 1px;
  background: url('../image/tyc.png') no-repeat;
  background-size: 16px;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related .search-related-list .sr-item .sr-item-left > p {
  flex: 1;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related .search-related-list .sr-item .sr-item-right {
  width: 150px;
  text-align: right;
  color: #999;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related .search-related-list .sr-item:hover .sr-item-left {
  color: #f60;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related .search-related-list .sr-item.disabled {
  color: #999;
  pointer-events: none;
}
.tyc-table-wrap.vd-ui-table-wrap {
  width: 100%;
}
.tyc-table-wrap.vd-ui-table-wrap.with-border {
  border-left: 1px solid #E1E5E8;
  border-right: 1px solid #E1E5E8;
}
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-wrap-widthscroll .vd-ui-th,
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-wrap-widthscroll .vd-ui-td {
  transition: box-shadow 0.3s;
}
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-wrap-widthscroll .vd-ui-th.sticky-left,
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-wrap-widthscroll .vd-ui-td.sticky-left {
  position: sticky;
  left: 0;
  z-index: 3;
}
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-wrap-widthscroll .vd-ui-th.sticky-right,
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-wrap-widthscroll .vd-ui-td.sticky-right {
  position: sticky;
  right: 0;
  z-index: 3;
}
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-wrap-widthscroll .vd-ui-th.sticky-left:before {
  content: "";
  width: 1px;
  height: 100%;
  background: #E1E5E8;
  right: -1px;
  position: absolute;
  top: 0;
}
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-wrap-widthscroll.is-left-fixed .vd-ui-th.sticky-left-last:after,
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-wrap-widthscroll.is-left-fixed .vd-ui-td.sticky-left-last:after {
  position: absolute;
  top: -1px;
  left: -1px;
  width: 100%;
  height: 100%;
  content: "";
  pointer-events: none;
}
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-wrap-widthscroll.is-left-fixed .vd-ui-th.sticky-left-last:before,
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-wrap-widthscroll.is-left-fixed .vd-ui-td.sticky-left-last:before {
  background: transparent;
  position: absolute;
  top: 0;
  right: 0;
  bottom: -1px;
  width: 100%;
  transform: translate(100%);
  transition: box-shadow 0.3s;
  content: "";
  pointer-events: none;
  box-shadow: inset 10px 0 8px -8px #00000026;
}
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-wrap-widthscroll.is-left-fixed .vd-ui-th.sticky-left-last {
  border-right: 0;
}
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-wrap-widthscroll.is-left-fixed .vd-ui-th.sticky-left-last + td {
  border-left: 0;
}
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-wrap-widthscroll.is-right-fixed .vd-ui-th.sticky-right-first:after,
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-wrap-widthscroll.is-right-fixed .vd-ui-td.sticky-right-first:after {
  position: absolute;
  top: -1px;
  right: -1px;
  width: calc(100% - 1px);
  height: 100%;
  content: "";
  pointer-events: none;
}
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-wrap-widthscroll.is-right-fixed .vd-ui-th.sticky-right-first:before,
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-wrap-widthscroll.is-right-fixed .vd-ui-td.sticky-right-first:before {
  position: absolute;
  top: 0;
  right: 0;
  bottom: -1px;
  width: 100%;
  transform: translate(-100%);
  transition: box-shadow 0.3s;
  content: "";
  pointer-events: none;
  box-shadow: inset -10px 0 8px -8px #00000026;
}
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-wrap-widthscroll.is-right-fixed .vd-ui-th.sticky-right-first {
  border-left: 0;
}
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-table-body {
  overflow: auto;
  position: relative;
}
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-table-body.vd-ui-wrap-scroll {
  overflow-y: auto;
}
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-table-body::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-table-body::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-table-body::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-table-body::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-table-body::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-table-container {
  width: 100%;
  overflow: auto;
}
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  text-align: left;
}
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-table .td-text {
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-table .td-text2 {
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-table .td-text .td-link {
  color: #0066CC;
  cursor: pointer;
}
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-table .td-text .td-link:hover {
  color: #f60;
}
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-table .vd-ui-th {
  padding: 8px 10px;
  background: #FAFBFC;
  font-weight: normal;
  border: solid 1px #E1E5E8;
  color: #999;
  position: relative;
  z-index: 2;
}
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-table .vd-ui-th:first-child {
  border-left: 0;
}
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-table .vd-ui-th:last-child {
  border-right: 0;
}
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-table .vd-ui-th.vd-ui-th-bar {
  border-left-color: #F5F7FA;
  z-index: 1;
}
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-table .vd-ui-th.vd-ui-th-bar-prev {
  border-right-color: #F5F7FA;
}
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-table .vd-ui-td {
  padding: 8px 10px;
  border-bottom: solid 1px #EBEFF2;
  background: #fff;
  vertical-align: top;
  word-break: break-all;
}
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-table .vd-ui-tr:hover .vd-ui-td {
  background: #EBEFF2;
}
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-table .vd-ui-tr.tr-checked .vd-ui-td {
  background: #E6F7FF;
}
.tyc-table-wrap.vd-ui-table-wrap .vd-ui-table .vd-ui-tr.tr-checked:hover .vd-ui-td {
  background: #D1EEFF;
}
.tyc-table-wrap.vd-ui-table-wrap .option-item {
  color: #0066CC;
  cursor: pointer;
  margin-right: 20px;
  position: relative;
}
.tyc-table-wrap.vd-ui-table-wrap .option-item::before {
  content: "";
  width: 1px;
  position: absolute;
  right: -10px;
  height: 15px;
  background: #e1e5e8;
  top: 3px;
}
.tyc-table-wrap.vd-ui-table-wrap .option-item:last-child {
  margin-right: 0;
}
.tyc-table-wrap.vd-ui-table-wrap .option-item:last-child::before {
  display: none;
}
.tyc-table-wrap.vd-ui-table-wrap .option-item.item-red {
  color: #E64545;
}
.tyc-table-wrap.vd-ui-table-wrap .option-item:hover {
  color: #f60;
}
.tyc-table-wrap.vd-ui-table-wrap .tyc-choose {
  color: #09f;
  cursor: pointer;
}
.tyc-table-wrap.vd-ui-table-wrap .tyc-choose:hover {
  color: #f60;
}
.tyc-empty {
  padding: 130px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.tyc-empty .empty-img {
  width: 110px;
  height: 95px;
  background: url('../image/common/no-data.svg') no-repeat;
  background-size: 110px 95px;
  background-position: center;
  margin-bottom: 40px;
}
@keyframes zhuan {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
.tyc-info-wrap .tyc-info-loading {
  padding: 100px 0;
  text-align: center;
}
.tyc-info-wrap .tyc-info-loading .icon-loading {
  display: inline-block;
  font-size: 32px;
  color: #06c;
  animation: zhuan 2s linear infinite;
}
.tyc-info-wrap .tyc-info-title {
  margin-bottom: 20px;
}
.tyc-info-wrap .tyc-info-title .tyc-info-title-txt {
  font-size: 20px;
  font-weight: 700;
}
.tyc-info-wrap .tyc-info-title .tyc-info-tags {
  display: flex;
  align-items: center;
  margin-top: 10px;
  margin-bottom: -5px;
  flex-wrap: wrap;
}
.tyc-info-wrap .tyc-info-title .tyc-info-tags .tag-item {
  background: #E6F7FF;
  font-size: 12px;
  height: 22px;
  line-height: 22px;
  padding: 0 5px;
  margin-right: 5px;
  margin-bottom: 5px;
}
.tyc-info-wrap .tyc-info-title .tyc-info-tags .tag-item:last-child {
  margin-right: 0;
}
.tyc-info-wrap .tyc-info-list::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.tyc-info-wrap .tyc-info-list::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.tyc-info-wrap .tyc-info-list::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.tyc-info-wrap .tyc-info-list::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.tyc-info-wrap .tyc-info-list::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.tyc-info-wrap .tyc-info-list .tyc-info-item {
  display: flex;
  border-bottom: solid 1px #E1E5E8;
}
.tyc-info-wrap .tyc-info-list .tyc-info-item .tyc-info-label {
  width: 160px;
  padding: 7px 10px;
  border-left: solid 1px #E1E5E8;
  background: #F5F7FA;
}
.tyc-info-wrap .tyc-info-list .tyc-info-item .tyc-info-txt {
  flex: 1;
  padding: 7px 10px;
  border-left: solid 1px #E1E5E8;
  border-right: solid 1px #E1E5E8;
}
.tyc-info-wrap .tyc-info-list .tyc-info-item:first-child {
  border-top: solid 1px #E1E5E8;
}
