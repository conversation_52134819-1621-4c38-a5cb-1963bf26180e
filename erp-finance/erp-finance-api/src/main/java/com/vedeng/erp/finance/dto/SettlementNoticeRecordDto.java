package com.vedeng.erp.finance.dto;

import lombok.Data;

import java.util.Date;

/**
 * 财务结款通知记录DTO
 */
@Data
public class SettlementNoticeRecordDto {
    /**
     * 主键
     */
    private Long settlementNoticeRecordId;

    /**
     * 银行ID
     */
    private Integer bankBillId;

    /**
     * 通知类型
     */
    private Integer noticeType;

    /**
     * 通知次数
     */
    private Integer noticeCount;

    /**
     * 最近一次通知时间
     */
    private Date lastNoticeTime;

    /**
     * 是否删除 0否 1是
     */
    private Integer isDelete;

    /**
     * 更新备注
     */
    private String updateRemark;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 修改时间
     */
    private Date modTime;

    /**
     * 添加人ID
     */
    private Integer creator;

    /**
     * 添加人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Integer updater;

    /**
     * 更新人名称
     */
    private String updaterName;
}
