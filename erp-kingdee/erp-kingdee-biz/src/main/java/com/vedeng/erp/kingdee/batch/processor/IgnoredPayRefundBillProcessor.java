package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.erp.finance.dto.BankBillIgnoreRecordDto;
import com.vedeng.erp.finance.dto.PayVedengBankDto;
import com.vedeng.erp.finance.service.BankBillIgnoreRecordApiService;
import com.vedeng.erp.finance.service.PayVedengBankApiService;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchBankBillDto;
import com.vedeng.erp.kingdee.batch.repository.BatchBankBillDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeePayRefundBillDto;
import com.vedeng.erp.kingdee.dto.KingDeePayRefundEntryDto;
import com.vedeng.erp.kingdee.enums.KingDeePayBillUseTypeEnums;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Objects;
import java.util.Optional;

/**
 * 已忽略 付款退款单 构造器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/24 16:59
 */
@Service
@Slf4j
public class IgnoredPayRefundBillProcessor extends BaseProcessor<BatchBankBillDto, KingDeePayRefundBillDto> {

    @Autowired
    private BankBillIgnoreRecordApiService bankBillIgnoreRecordApiService;

    @Autowired
    private PayVedengBankApiService payVedengBankApiService;

    @Autowired
    private BatchBankBillDtoMapper batchBankBillDtoMapper;

    @Override
    public KingDeePayRefundBillDto doProcess(BatchBankBillDto input, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("开始处理已忽略付款退款单流水信息：{}", JSONObject.toJSONString(input));
        // 查询忽略时的信息
        BankBillIgnoreRecordDto bankBillIgnoreRecordDto = bankBillIgnoreRecordApiService.selectByBankBillId(input.getBankBillId());
        if (Objects.isNull(bankBillIgnoreRecordDto) || !KingDeeConstant.BD_SUPPLIER.equals(bankBillIgnoreRecordDto.getContactUnitType())) {
            log.info("当前忽略流水不符合条件：{}", JSONObject.toJSONString(input));
            return null;
        }
        KingDeePayRefundBillDto result = new KingDeePayRefundBillDto();
        result.setFId("0");
        result.setFBillNo(KingDeeConstant.IGNORE + input.getBankBillId().toString());
        result.setFContactUnitType(KingDeeConstant.BD_SUPPLIER);
        result.setFPayUnitType(KingDeeConstant.BD_SUPPLIER);
//        result.setFDate(DateUtil.formatDate(input.getTranDate()));
        result.setFDate(DateUtil.formatDateTime(input.getRealTrandatetime()));//VDERP-16088  款对接金蝶业务日期规则调整-精确到时分秒
        result.setFQzokPzgsywdh(input.getOrderNo());
        result.setFContactUnit(bankBillIgnoreRecordDto.getContactUnitNo());
        result.setFQzokLsh(input.getTranFlow());

        KingDeePayRefundEntryDto entryDto = new KingDeePayRefundEntryDto();
        entryDto.setFpurposeid(KingDeePayBillUseTypeEnums.BUY_PAY.getCode());
        entryDto.setFrefundamountfor(input.getAmt().subtract(input.getMatchedAmount()));
        Optional<PayVedengBankDto> payVeDengBankDto = Optional.ofNullable(payVedengBankApiService.queryBankInfo(input.getBankTag()));
        if (!payVeDengBankDto.isPresent()) {
            log.info("未查询到忽略流水的银行信息：{}", JSONObject.toJSONString(input));
            return null;
        }
        entryDto.setFaccountid(payVeDengBankDto.get().getPayBankNo());
        entryDto.setFQzokYwlx(KingDeeConstant.OTHER_BUSINESS_RECEIPTS);

        // 处理手续费
        this.handleFeeAndSettleType(input, entryDto);
        result.setFrefundbillentry(Collections.singletonList(entryDto));
        log.info("已忽略付款退款单构造完成：{}", JSONObject.toJSONString(result));
        return result;
    }

    /**
     * 根据BankTag处理手续费和付款类型
     *
     * @param input    BatchBankBillDto
     * @param entryDto KingDeePayRefundEntryDto
     */
    private void handleFeeAndSettleType(BatchBankBillDto input, KingDeePayRefundEntryDto entryDto) {
        switch (input.getBankTag()) {
            case 4:
                entryDto.setFsettletypeid(KingDeePayBillUseTypeEnums.ALI_PAY.getCode());
                entryDto.setFhandlingchargefor(BigDecimal.ZERO);
                break;
            case 5:
                entryDto.setFsettletypeid(KingDeePayBillUseTypeEnums.WE_CHAT_PAY.getCode());
                entryDto.setFhandlingchargefor(BigDecimal.ZERO);
                break;
            default:
                entryDto.setFsettletypeid(KingDeePayBillUseTypeEnums.WIRE_TRANSFER.getCode());
                break;
        }
    }
}