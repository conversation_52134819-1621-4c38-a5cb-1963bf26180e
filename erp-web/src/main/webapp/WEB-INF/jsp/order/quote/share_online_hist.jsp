<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="客户提交报价单记录" scope="application" />
<%@ include file="../../common/common.jsp"%>
<div class="main-container">
    <div>
        <c:choose>
            <c:when test="${orderChangce eq 1}">
                客户修改了商品信息，提交订单，具体信息如下：
            </c:when>
            <c:otherwise>
                客户未修改商品信息，提交订单，具体信息如下：
            </c:otherwise>
        </c:choose>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <th class="wid16">产品名称</th>
                <th class="wid8">报价</th>
                <th class="wid6">提交订单前，报价单数量</th>
                <th class="wid6">客户提交订单数量</th>
            </tr>
            </thead>
        <tbody>
        <c:forEach var="list" items="${list}" varStatus="staut">
            <tr>
                <td>${list.skuName} <br></br> ${list.sku}</td>
                <td><fmt:formatNumber type="number" value="${list.price}" pattern="0.00" maxFractionDigits="2" /></td>
                <td>${list.quNum}</td>
                <td>
                    <c:choose>
                        <c:when test="${list.isChangce eq 1}">
                            <span style="color: orangered"> ${list.saleNum}  </span>
                        </c:when>
                        <c:otherwise>
                            ${list.saleNum}
                        </c:otherwise>
                    </c:choose>
                </td>
            </tr>
        </c:forEach>
        </tbody>
        </table>
    </div>
</div>
<div style="text-align: center"><button class="bt-bg-style bt-small bg-light-blue"  id="close-layer" type="button">关闭</button></div>
<%@ include file="../../common/footer.jsp"%>
