<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>客户账期使用记录</title>
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="caiwu-zhangqi" datasource="erp-reportdatasource" fixednumber="2" append_column_url="" append_row_url="" empty_show="-" firstcol="numbers">
  <div id="appendHead"></div> 
  <div class="layui-fluid"> 
   <div class="layui-tab"> 
    <ul class="layui-tab-title" id="tab"> 
    </ul> 
   </div> 
   <form class="layui-form" id="search">
       <div class=" layui-inline ">
           <label class="layui-form-label">客户名称</label>
           <div class="layui-input-inline"><input class=" layui-input list-search-item " type="text" name="TRADER_NAME" placeholder="" style="" alias="t" jdbctype="" data="" datatype="" oper="">
           </div>
       </div>
       <div class=" layui-inline ">
           <label class="layui-form-label">归属销售</label>
           <div class="layui-input-inline"><select class=" layui-input list-search-item " type="search" name="USER_ID" placeholder="" style="" alias="t" jdbctype="" data="select user_id K,username V from T_USER ORDER BY USERNAME" datatype="KVSQLCACHE" oper=""></select>
           </div>
       </div>
    <div class=" layui-inline "><label class="layui-form-label">逾期状态</label>
     <div class="layui-input-inline">
      <select class=" layui-input list-search-item " type="search" name="OVER_STATUS" placeholder="" style=""   jdbctype="" data='[{"V":"未逾期","K":"未逾期"},{"V":"部分逾期","K":"部分逾期"},{"V":"全部逾期","K":"全部逾期"},{"V":"未开始","K":"未开始"}]' datatype="JSON" oper=""></select>
     </div>
    </div>
       <div class=" layui-inline "><label class="layui-form-label">订单号</label>
           <div class="layui-input-inline">
               <object class=" layui-input list-search-item " type="19" name="SALEORDER_NO" placeholder="" style="" alias="t" jdbctype="" data="" datatype="" oper=""></object>
           </div>
       </div>
       <div class=" layui-inline "><label class="layui-form-label">订单生效日期</label>
           <div class="layui-input-inline">
               <object class=" layui-input list-search-item " type="daterange" name="VALID_TIME" placeholder="" style="" alias="" jdbctype="" data="" datatype="" oper=""></object>
           </div>
       </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">账期类型</label>
     <div class="layui-input-inline"><select class=" layui-input list-search-item " type="search" name="BILL_PERIOD_TYPE" placeholder="" style="" alias="t" jdbctype="" data='[{"V":"正式账期","K":"1"},{"V":"临时账期","K":"2"},{"V":"订单账期","K":"3"}]' datatype="JSON" oper=""></select>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">账期归还</label>
     <div class="layui-input-inline"><select class=" layui-input list-search-item " type="search" name="UNRETURNED_AMOUNT" placeholder="" style="" alias="t" jdbctype="BODY" data='[{"V":"是","K":"1"},{"V":"否","K":"2"}]' datatype="JSON" oper=""></select>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">归属部门</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="search-org" name="ORG_ID" placeholder="" style="" alias="t" jdbctype="" data="" datatype="" oper="IN"></object>
     </div>
    </div>
   </form>
   <hr class="layui-border-blue"> 
   <div class="btn-group   bd-highlight" id="tableButton">
       <button item_name="导出" name="导出" url="/ezadmin/list/export-caiwu-zhangqi" opentype="_BLANK_PARAM_COLUMN" windowname="" style type="table">导出</button>
   </div>
   <table id="table" class="layui-table" style=" width:100%"> 
    <thead> 
     <tr id="column">
      <th type="rowbutton" id="rowbutton"></th>
      <th item_name="SALEORDER_NO" name="SALEORDER_NO" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data=""  style="" head="18">订单号</th>
      <th item_name="TRADER_ID" name="TRADER_ID" body="td-text" order="0" url="" opentype="MODEL" windowname="OPEN" datatype="" data="" style="" head="18">客户ID</th>
      <th item_name="TRADER_NAME" name="TRADER_NAME" body="td-link" order="0" url="/trader/customer/baseinfo.do?traderId=${TRADER_ID}" opentype="PARENT"  windowname="" datatype="" data="" style="" head="18">客户名称</th>
      <th item_name="VALID_TIME" name="VALID_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">订单生效日期</th>
      <th item_name="VU_USERNAME" name="VU_USERNAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">生效时归属销售</th>
      <th item_name="NOW_USERNAME" name="NOW_USERNAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">当前归属销售</th>
      <th item_name="VA_ORG_NAME" name="VA_ORG_NAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">生效时归属部门</th>
      <th item_name="NOW_ORG_NAME" name="NOW_ORG_NAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">当前归属部门</th>
      <th item_name="BILL_PERIOD_TYPE" name="BILL_PERIOD_TYPE" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">账期类型</th>
      <th item_name="CREDIT_TOTAL_AMOUNT" name="CREDIT_TOTAL_AMOUNT" body="td-text" order="0" url="" opentype="MODEL" windowname="OPEN" datatype="" data="" style="" head="18" jdbctype="NUMBER">账期额度</th>
      <th item_name="CREDIT_USABLE_AMOUNT" name="CREDIT_USABLE_AMOUNT" body="td-text" order="0" url="" opentype="MODEL" windowname="OPEN" datatype="" data="" style="" head="18" jdbctype="NUMBER">账期可用额度</th>
      <th item_name="SETTLEMENT_PERIOD" name="SETTLEMENT_PERIOD" body="td-text" order="0" url="" opentype="MODEL" windowname="OPEN" datatype="" data="" style="" head="18" jdbctype="NUMBER">账期天数</th>
      <th item_name="BILL_PERIOD_END" name="BILL_PERIOD_END" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">账期有效期</th>
      <th item_name="TOTAL_AMOUNT" name="TOTAL_AMOUNT" body="td-text" order="0" url="" opentype="MODEL" windowname="OPEN" datatype="" data="" style="" head="18" jdbctype="NUMBER">订单原金额</th>
      <th item_name="REAL_TOTAL_AMOUNT" name="REAL_TOTAL_AMOUNT" body="td-text" order="0" url="" opentype="MODEL" windowname="OPEN" datatype="" data="" style="" head="18" jdbctype="NUMBER">订单实际金额</th>
      <th item_name="CREDIT_USE_AMOUNT" name="CREDIT_USE_AMOUNT" body="td-text" order="0" url="" opentype="MODEL" windowname="OPEN" datatype="" data="" style="" head="18" jdbctype="NUMBER">账期使用额</th>
      <th item_name="OCCUPY_AMOUNT" name="OCCUPY_AMOUNT" body="td-text" order="0" url="" opentype="MODEL" windowname="OPEN" datatype="" data="" style="" head="18" jdbctype="NUMBER">账期占用额</th>
      <th item_name="UNRETURNED_AMOUNT" name="UNRETURNED_AMOUNT" body="td-text" order="0" url="" opentype="MODEL" windowname="OPEN" datatype="" data="" style="" head="18" jdbctype="NUMBER">未归还账期额</th>
      <th item_name="DELIVERY_AMOUNT" name="DELIVERY_AMOUNT" body="td-text" order="0" url="" opentype="MODEL" windowname="OPEN" datatype="" data="" style="" head="18" jdbctype="NUMBER">订单已发货金额</th>
      <th item_name="RETENTION_MONEY" name="RETENTION_MONEY" body="td-text" order="0" url="" opentype="MODEL" windowname="OPEN" datatype="" data="" style="" head="18" jdbctype="NUMBER">质保金</th>
      <th item_name="BILL_PERIOD_SETTLEMENT_TYPE" name="BILL_PERIOD_SETTLEMENT_TYPE" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">结算标准</th>
      <th item_name="FREEZE_AMOUNT" name="FREEZE_AMOUNT" body="td-text" order="0" url="" opentype="MODEL" windowname="OPEN" datatype="" data="" style="" head="18" jdbctype="NUMBER">账期冻结额</th>
      <th item_name="OVERDUE_AMOUNT" name="OVERDUE_AMOUNT" body="td-text" order="0" url="" opentype="MODEL" windowname="OPEN" datatype="" data="" style="" head="18" jdbctype="NUMBER">逾期未还金额</th>
      <th item_name="COUNT_OF_OVERDUE" name="COUNT_OF_OVERDUE" body="td-text" order="0" url="" opentype="MODEL" windowname="OPEN" datatype="" data="" style="" head="18" jdbctype="NUMBER">逾期次数</th>
      <th item_name="DAYS_OF_OVERDUE" name="DAYS_OF_OVERDUE" body="td-text" order="0" url="" opentype="MODEL" windowname="OPEN" datatype="" data="" style="" head="18" jdbctype="NUMBER">逾期天数</th>
      <th item_name="RETURNED_AMOUNT" name="RETURNED_AMOUNT" body="td-text" order="0" url="" opentype="MODEL" windowname="OPEN" datatype="" data="" style="" head="18" jdbctype="NUMBER">已归还账期额</th>
      <th item_name="OVER_STATUS" name="OVER_STATUS" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">逾期状态</th>
     </tr> 
    </thead> 
    <tbody> 
     <tr> 
      <td colspan="100"> express: <pre id="express" class="layui-code" orderby="" groupby=" ">
          StringBuilder sql=new StringBuilder();
          sql.append("
          select * from (
          SELECT A.TRADER_ID,
       A.SALEORDER_ID,
       t.TRADER_NAME,
       B.USERNAME,B.USER_ID,
       a.ORG_NAME,
      va.ORG_NAME AS VA_ORG_NAME,
      do.ORG_NAME AS NOW_ORG_NAME,
      vu.USERNAME AS VU_USERNAME,
       B.USERNAME AS NOW_USERNAME,
       CASE

           WHEN a.`LEVEL` &gt; 1
               AND a.`LEVEL` &lt; 4 THEN
               concat_ws('/', c.ORG_NAME, b.ORG_NAME, a.ORG_NAME)
           WHEN a.`LEVEL` = 4 THEN
               concat_ws('/', d.ORG_NAME, c.ORG_NAME, b.ORG_NAME)
           WHEN a.`LEVEL` &gt; 4 THEN
               concat_ws('/', p.ORG_NAME, f.ORG_NAME, d.ORG_NAME, c.ORG_NAME, b.ORG_NAME)
           ELSE a.ORG_NAME
           END                                                                                ORG_NAMES,
       CASE

           WHEN T2.BILL_PERIOD_TYPE = 1 THEN
               '正式账期'
           WHEN T2.BILL_PERIOD_TYPE = 2 THEN
               '临时账期'
           WHEN T2.BILL_PERIOD_TYPE = 3 THEN
               '订单账期'
           END                                                                                BILL_PERIOD_TYPE,
       T2.APPLY_AMOUNT                                                                     AS CREDIT_TOTAL_AMOUNT,
       TC.CREDIT_USABLE_AMOUNT,
       CASE

           WHEN T2.BILL_PERIOD_TYPE = 3 THEN
               '-'
           ELSE FROM_UNIXTIME(IF(T2.BILL_PERIOD_END = 0, NULL, T2.BILL_PERIOD_END) / 1000, '%Y-%m-%d %H:%i:%s')
           END                                                                                BILL_PERIOD_END,
       A.SALEORDER_NO,
       FROM_UNIXTIME(IF(A.VALID_TIME = 0, NULL, A.VALID_TIME) / 1000, '%Y-%m-%d %H:%i:%s') AS VALID_TIME,
       A.TOTAL_AMOUNT,
       A.REAL_TOTAL_AMOUNT,
       T1.AMOUNT                                                                              CREDIT_USE_AMOUNT,
       CASE
           T1.OCCUPANCY
           WHEN 1 THEN
               T1.UNRETURNED_AMOUNT
           ELSE 0
           END                                                                                OCCUPY_AMOUNT,
       CASE
           T1.OCCUPANCY
           WHEN 0 THEN
               T1.UNRETURNED_AMOUNT
           ELSE 0
           END                                                                                FREEZE_AMOUNT,
       CASE

           WHEN T2.BILL_PERIOD_TYPE = 2 &amp;&amp; Z1.BILL_PERIOD_TYPE = 1 THEN
               '-'
           WHEN T2.BILL_PERIOD_TYPE = 3 &amp;&amp; Z1.BILL_PERIOD_TYPE = 1 THEN
               '-'
           WHEN T2.BILL_PERIOD_TYPE = 3 &amp;&amp; Z2.BILL_PERIOD_TYPE = 2 THEN
               '-'
           ELSE A.RETENTION_MONEY
           END                                                                                RETENTION_MONEY,
       CASE

           WHEN A.BILL_PERIOD_SETTLEMENT_TYPE = 1 THEN
               '产品发货'
           ELSE '产品开票'
           END                                                                                BILL_PERIOD_SETTLEMENT_TYPE,
       T1.UNRETURNED_AMOUNT,

       SUM(case when IFNULL(T3.OVERDUE_DAYS, 0) = 0 then IFNULL(T3.OVERDUE_AMOUNT, 0) else IFNULL(T3.UNRETURNED_AMOUNT, 0) end)                                                   OVERDUE_AMOUNT,
       SUM(IF(T3.OVERDUE_DAYS &gt; 0, 1, 0))                                                     COUNT_OF_OVERDUE,
       SUM(IFNULL(T3.OVERDUE_DAYS, 0))                                                        DAYS_OF_OVERDUE,
       T1.AMOUNT - T1.UNRETURNED_AMOUNT                                                       RETURNED_AMOUNT,
       CASE

           WHEN TN.UN_SUPERVISE_AMOUNT IS NULL THEN
               0.00
           ELSE TN.UN_SUPERVISE_AMOUNT
           END                                                                                UN_SUPERVISE_AMOUNT,
       CASE

           WHEN SUM(T3.OVERDUE_AMOUNT) = 0 THEN
               '未逾期'
           WHEN SUM(T3.OVERDUE_AMOUNT) &gt; 0
               AND SUM(T3.OVERDUE_AMOUNT) &lt; T1.AMOUNT THEN
               '部分逾期'
           WHEN SUM(T3.OVERDUE_AMOUNT) = T1.AMOUNT THEN
               '全部逾期'
           ELSE '未开始'
           END                                                                                OVER_STATUS,
       T1.BILL_PERIOD_ID,
       T1.BILL_PERIOD_USE_DETAIL_ID,
       T1.CUSTOMER_ID,
       T1.RELATED_ID,
       T2.SETTLEMENT_PERIOD,
       SUM(T3.OVERDUE_AMOUNT)                                                                 OVERDUE_AMOUNT_FLAG,
       SGS.DELIVERY_AMOUNT
FROM T_CUSTOMER_BILL_PERIOD_USE_DETAIL T1
         LEFT JOIN T_SALEORDER A ON T1.RELATED_ID = A.SALEORDER_ID
         LEFT JOIN (SELECT DISTINCT SG.SALEORDER_ID, SUM((CASE WHEN SG.DELIVERY_NUM = 0 then 0 ELSE (SG.DELIVERY_NUM - ifnull(SG.AFTER_RETURN_NUM,0)) END) * SG.REAL_PRICE) AS DELIVERY_AMOUNT
FROM T_SALEORDER_GOODS SG
WHERE SG.IS_DELETE = 0 GROUP BY SALEORDER_ID
)SGS ON A.SALEORDER_ID = SGS.SALEORDER_ID
         LEFT JOIN T_TRADER t ON A.TRADER_ID = t.TRADER_ID
         LEFT JOIN T_USER vu ON A.VALID_USER_ID = vu.USER_ID
         LEFT JOIN T_ORGANIZATION va ON A.VALID_ORG_ID = va.ORG_ID
         LEFT JOIN T_ORGANIZATION a ON A.ORG_ID = a.ORG_ID
         LEFT JOIN T_ORGANIZATION b ON a.PARENT_ID = b.ORG_ID
         LEFT JOIN T_ORGANIZATION c ON b.PARENT_ID = c.ORG_ID
    AND c.`LEVEL` &gt; 0
         LEFT JOIN T_ORGANIZATION d ON c.PARENT_ID = d.ORG_ID
    AND d.`LEVEL` &gt; 0
         LEFT JOIN T_ORGANIZATION f ON d.PARENT_ID = f.ORG_ID
    AND f.`LEVEL` &gt; 0
         LEFT JOIN T_ORGANIZATION p ON f.PARENT_ID = p.ORG_ID
    AND p.`LEVEL` &gt; 0
         INNER JOIN T_R_TRADER_J_USER TR ON A.TRADER_ID = TR.TRADER_ID
         LEFT JOIN T_USER B ON TR.USER_ID = B.USER_ID
         LEFT JOIN T_R_USER_POSIT bt ON B.USER_ID = bt.USER_ID
         LEFT JOIN T_POSITION dt ON dt.POSITION_ID = bt.POSITION_ID
         LEFT JOIN T_ORGANIZATION do ON dt.ORG_ID = do.ORG_ID
         LEFT JOIN T_CUSTOMER_BILL_PERIOD T2 ON T1.BILL_PERIOD_ID = T2.BILL_PERIOD_ID
         LEFT JOIN (
    SELECT ZT1.RELATED_ID,
           ZT0.BILL_PERIOD_TYPE
    FROM T_CUSTOMER_BILL_PERIOD_USE_DETAIL ZT1
             LEFT JOIN T_CUSTOMER_BILL_PERIOD ZT0 ON ZT1.BILL_PERIOD_ID = ZT0.BILL_PERIOD_ID
    WHERE ZT1.USE_TYPE IN (1, 2)
      AND ZT0.BILL_PERIOD_TYPE = 1
) Z1 ON T1.RELATED_ID = Z1.RELATED_ID
         LEFT JOIN (
    SELECT ZT3.RELATED_ID,
           ZT2.BILL_PERIOD_TYPE
    FROM T_CUSTOMER_BILL_PERIOD_USE_DETAIL ZT3
             LEFT JOIN T_CUSTOMER_BILL_PERIOD ZT2 ON ZT3.BILL_PERIOD_ID = ZT2.BILL_PERIOD_ID
    WHERE ZT3.USE_TYPE IN (1, 2)
      AND ZT2.BILL_PERIOD_TYPE = 2
) Z2 ON T1.RELATED_ID = Z2.RELATED_ID
         LEFT JOIN (select BILL_PERIOD_USE_DETAIL_ID,
                           sum(ifnull(OVERDUE_DAYS, 0))      OVERDUE_DAYS,
                           sum(ifnull(OVERDUE_AMOUNT, 0))    OVERDUE_AMOUNT,
                           sum(ifnull(UNRETURNED_AMOUNT, 0)) UNRETURNED_AMOUNT
                    from T_CUSTOMER_BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL
                    group by BILL_PERIOD_USE_DETAIL_ID) T3
                   ON T1.BILL_PERIOD_USE_DETAIL_ID = T3.BILL_PERIOD_USE_DETAIL_ID
         LEFT JOIN (
    SELECT TT1.BILL_PERIOD_ID,
           (TT1.APPLY_AMOUNT - IFNULL(sum(TT2.AMOUNT), 0)) CREDIT_USABLE_AMOUNT
    FROM T_CUSTOMER_BILL_PERIOD TT1
             LEFT JOIN T_CUSTOMER_BILL_PERIOD_USE_DETAIL TT2 ON TT1.BILL_PERIOD_ID = TT2.BILL_PERIOD_ID
    GROUP BY TT1.BILL_PERIOD_ID
) TC ON T1.BILL_PERIOD_ID = TC.BILL_PERIOD_ID
         LEFT JOIN (
    SELECT TT3.BILL_PERIOD_USE_DETAIL_ID,
           IFNULL(TT4.AMOUNT, 0) - SUM(IFNULL(TT3.AMOUNT, 0)) UN_SUPERVISE_AMOUNT
    FROM T_CUSTOMER_BILL_PERIOD_RISK_MANAGEMENT_DETAIL TT4
             LEFT JOIN T_CUSTOMER_BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL TT3
                       ON TT4.BILL_PERIOD_USE_DETAIL_ID = TT3.BILL_PERIOD_USE_DETAIL_ID
    GROUP BY TT4.BILL_PERIOD_USE_DETAIL_ID
) TN ON T3.BILL_PERIOD_USE_DETAIL_ID = TN.BILL_PERIOD_USE_DETAIL_ID
WHERE T1.USE_TYPE = 1
  AND HAVE_ACCOUNT_PERIOD = 1
  AND TR.TRADER_TYPE = 1
  AND dt.TYPE = 310

          ");
if(isNotBlank("UNRETURNED_AMOUNT")){

            if($("UNRETURNED_AMOUNT").equals("1")){
                sql.append(" AND T1.UNRETURNED_AMOUNT = 0 ");
            }
            if($("UNRETURNED_AMOUNT").equals("2")){
               sql.append("  AND T1.UNRETURNED_AMOUNT > 0  ");
            }
}

          sql.append("
GROUP BY
            A.SALEORDER_ID,
            T2.BILL_PERIOD_TYPE,
            T1.RELATED_ID ORDER BY T2.BILL_PERIOD_TYPE, A.SALEORDER_ID
          ) t where 1=1 ");
          list=search(sql);
          return list;
</pre> count: <pre id="count" class="layui-code"></pre></td>
     </tr> 
    </tbody> 
   </table> 
  </div> 
  <div id="appendFoot"></div> 
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script> 
  <script>
    layui.use(function () {

    })
</script> 
 </body>
</html>