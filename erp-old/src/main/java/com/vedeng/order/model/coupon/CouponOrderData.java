package com.vedeng.order.model.coupon;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 *  优惠券订单数据统计
 * <AUTHOR>
 * @date $
 */
public class CouponOrderData implements Serializable {
    private static final long serialVersionUID = 1L;
    //累计成单量
    private Integer orderNum;
    //成交客户数
    private Integer traderNum;
    //累计成交金额
    private BigDecimal allTotalAmount;
    //累计优惠金额
    private BigDecimal totalCouponAmount;
    //销售总额
    private BigDecimal saleAmount;
    //订单明细数据
    private List<CouponOrderDetailData>couponOrderDetailDataList;

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public Integer getTraderNum() {
        return traderNum;
    }

    public void setTraderNum(Integer traderNum) {
        this.traderNum = traderNum;
    }

    public BigDecimal getAllTotalAmount() {
        return allTotalAmount;
    }

    public void setAllTotalAmount(BigDecimal allTotalAmount) {
        this.allTotalAmount = allTotalAmount;
    }

    public BigDecimal getTotalCouponAmount() {
        return totalCouponAmount;
    }

    public void setTotalCouponAmount(BigDecimal totalCouponAmount) {
        this.totalCouponAmount = totalCouponAmount;
    }

    public BigDecimal getSaleAmount() {
        return saleAmount;
    }

    public void setSaleAmount(BigDecimal saleAmount) {
        this.saleAmount = saleAmount;
    }

    public List<CouponOrderDetailData> getCouponOrderDetailDataList() {
        return couponOrderDetailDataList;
    }

    public void setCouponOrderDetailDataList(List<CouponOrderDetailData> couponOrderDetailDataList) {
        this.couponOrderDetailDataList = couponOrderDetailDataList;
    }

    @Override
    public String toString() {
        return "CouponOrderData{" +
                "orderNum=" + orderNum +
                ", traderNum=" + traderNum +
                ", allTotalAmount=" + allTotalAmount +
                ", totalCouponAmount=" + totalCouponAmount +
                ", saleAmount=" + saleAmount +
                ", couponOrderDetailDataList=" + couponOrderDetailDataList +
                '}';
    }
}
