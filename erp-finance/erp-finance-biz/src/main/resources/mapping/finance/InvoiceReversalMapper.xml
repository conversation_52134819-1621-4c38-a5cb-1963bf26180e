<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.erp.finance.mapper.InvoiceReversalMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.erp.finance.domain.entity.InvoiceReversalEntity" >
    <!--          -->
    <id column="INVOICE_REVERSAL_ID" property="invoiceReversalId" jdbcType="INTEGER" />
    <result column="INVOICE_ID" property="invoiceId" jdbcType="INTEGER" />
    <result column="INVOICE_CODE" property="invoiceCode" jdbcType="VARCHAR" />
    <result column="INVOICE_NO" property="invoiceNo" jdbcType="VARCHAR" />
    <result column="SALE_NAME" property="saleName" jdbcType="VARCHAR" />
    <result column="REVERSAL_BILL_TYPE" property="reversalBillType" jdbcType="BIT" />
    <result column="REVERSAL_BILL_NO" property="reversalBillNo" jdbcType="VARCHAR" />
    <result column="INVOICE_TYPE" property="invoiceType" jdbcType="INTEGER" />
    <result column="REVERSAL_AUDIT_USER" property="reversalAuditUser" jdbcType="INTEGER" />
    <result column="REVERSAL_AUDIT_STATUS" property="reversalAuditStatus" jdbcType="BIT" />
    <result column="AUDIT_APPLY_TIME" property="auditApplyTime" jdbcType="TIMESTAMP" />
    <result column="AUDIT_COMMENTS" property="auditComments" jdbcType="VARCHAR" />
    <result column="IS_DELETE" property="isDelete" jdbcType="BIT" />
    <result column="ADD_TIME" property="addTime" jdbcType="TIMESTAMP" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="CREATOR_NAME" property="creatorName" jdbcType="VARCHAR" />
    <result column="MOD_TIME" property="modTime" jdbcType="TIMESTAMP" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
    <result column="UPDATER_NAME" property="updaterName" jdbcType="VARCHAR" />
    <result column="UPDATE_REMARK" property="updateRemark" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--          -->
    INVOICE_REVERSAL_ID, INVOICE_ID, INVOICE_CODE, INVOICE_NO, SALE_NAME, REVERSAL_BILL_TYPE, 
    REVERSAL_BILL_NO, INVOICE_TYPE, REVERSAL_AUDIT_USER, REVERSAL_AUDIT_STATUS, AUDIT_APPLY_TIME, 
    AUDIT_COMMENTS, IS_DELETE, ADD_TIME, CREATOR, CREATOR_NAME, MOD_TIME, UPDATER, UPDATER_NAME, 
    UPDATE_REMARK
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--          -->
    select 
    <include refid="Base_Column_List" />
    from T_INVOICE_REVERSAL
    where INVOICE_REVERSAL_ID = #{invoiceReversalId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--          -->
    delete from T_INVOICE_REVERSAL
    where INVOICE_REVERSAL_ID = #{invoiceReversalId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceReversalEntity" >
    <!--          -->
    insert into T_INVOICE_REVERSAL (INVOICE_REVERSAL_ID, INVOICE_ID, INVOICE_CODE, 
      INVOICE_NO, SALE_NAME, REVERSAL_BILL_TYPE, 
      REVERSAL_BILL_NO, INVOICE_TYPE, REVERSAL_AUDIT_USER, 
      REVERSAL_AUDIT_STATUS, AUDIT_APPLY_TIME, AUDIT_COMMENTS, 
      IS_DELETE, ADD_TIME, CREATOR, 
      CREATOR_NAME, MOD_TIME, UPDATER, 
      UPDATER_NAME, UPDATE_REMARK)
    values (#{invoiceReversalId,jdbcType=INTEGER}, #{invoiceId,jdbcType=INTEGER}, #{invoiceCode,jdbcType=VARCHAR}, 
      #{invoiceNo,jdbcType=VARCHAR}, #{saleName,jdbcType=VARCHAR}, #{reversalBillType,jdbcType=BIT}, 
      #{reversalBillNo,jdbcType=VARCHAR}, #{invoiceType,jdbcType=INTEGER}, #{reversalAuditUser,jdbcType=INTEGER}, 
      #{reversalAuditStatus,jdbcType=BIT}, #{auditApplyTime,jdbcType=TIMESTAMP}, #{auditComments,jdbcType=VARCHAR}, 
      #{isDelete,jdbcType=BIT}, #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{creatorName,jdbcType=VARCHAR}, #{modTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER}, 
      #{updaterName,jdbcType=VARCHAR}, #{updateRemark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceReversalEntity"
          keyColumn="INVOICE_REVERSAL_ID" keyProperty="invoiceReversalId" useGeneratedKeys="true">
    <!--          -->
    insert into T_INVOICE_REVERSAL
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="invoiceReversalId != null" >
        INVOICE_REVERSAL_ID,
      </if>
      <if test="invoiceId != null" >
        INVOICE_ID,
      </if>
      <if test="invoiceCode != null" >
        INVOICE_CODE,
      </if>
      <if test="invoiceNo != null" >
        INVOICE_NO,
      </if>
      <if test="saleName != null" >
        SALE_NAME,
      </if>
      <if test="reversalBillType != null" >
        REVERSAL_BILL_TYPE,
      </if>
      <if test="reversalBillNo != null" >
        REVERSAL_BILL_NO,
      </if>
      <if test="invoiceType != null" >
        INVOICE_TYPE,
      </if>
      <if test="reversalAuditUser != null" >
        REVERSAL_AUDIT_USER,
      </if>
      <if test="reversalAuditStatus != null" >
        REVERSAL_AUDIT_STATUS,
      </if>
      <if test="auditApplyTime != null" >
        AUDIT_APPLY_TIME,
      </if>
      <if test="auditComments != null" >
        AUDIT_COMMENTS,
      </if>
      <if test="isDelete != null" >
        IS_DELETE,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="creatorName != null" >
        CREATOR_NAME,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
      <if test="updaterName != null" >
        UPDATER_NAME,
      </if>
      <if test="updateRemark != null" >
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="invoiceReversalId != null" >
        #{invoiceReversalId,jdbcType=INTEGER},
      </if>
      <if test="invoiceId != null" >
        #{invoiceId,jdbcType=INTEGER},
      </if>
      <if test="invoiceCode != null" >
        #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNo != null" >
        #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="saleName != null" >
        #{saleName,jdbcType=VARCHAR},
      </if>
      <if test="reversalBillType != null" >
        #{reversalBillType,jdbcType=BIT},
      </if>
      <if test="reversalBillNo != null" >
        #{reversalBillNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceType != null" >
        #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="reversalAuditUser != null" >
        #{reversalAuditUser,jdbcType=INTEGER},
      </if>
      <if test="reversalAuditStatus != null" >
        #{reversalAuditStatus,jdbcType=BIT},
      </if>
      <if test="auditApplyTime != null" >
        #{auditApplyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditComments != null" >
        #{auditComments,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null" >
        #{isDelete,jdbcType=BIT},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null" >
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null" >
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null" >
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceReversalEntity" >
    <!--          -->
    update T_INVOICE_REVERSAL
    <set >
      <if test="invoiceId != null" >
        INVOICE_ID = #{invoiceId,jdbcType=INTEGER},
      </if>
      <if test="invoiceCode != null" >
        INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNo != null" >
        INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="saleName != null" >
        SALE_NAME = #{saleName,jdbcType=VARCHAR},
      </if>
      <if test="reversalBillType != null" >
        REVERSAL_BILL_TYPE = #{reversalBillType,jdbcType=BIT},
      </if>
      <if test="reversalBillNo != null" >
        REVERSAL_BILL_NO = #{reversalBillNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceType != null" >
        INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="reversalAuditUser != null" >
        REVERSAL_AUDIT_USER = #{reversalAuditUser,jdbcType=INTEGER},
      </if>
      <if test="reversalAuditStatus != null" >
        REVERSAL_AUDIT_STATUS = #{reversalAuditStatus,jdbcType=BIT},
      </if>
      <if test="auditApplyTime != null" >
        AUDIT_APPLY_TIME = #{auditApplyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditComments != null" >
        AUDIT_COMMENTS = #{auditComments,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null" >
        IS_DELETE = #{isDelete,jdbcType=BIT},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null" >
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null" >
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null" >
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where INVOICE_REVERSAL_ID = #{invoiceReversalId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceReversalEntity" >
    <!--          -->
    update T_INVOICE_REVERSAL
    set INVOICE_ID = #{invoiceId,jdbcType=INTEGER},
      INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR},
      INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR},
      SALE_NAME = #{saleName,jdbcType=VARCHAR},
      REVERSAL_BILL_TYPE = #{reversalBillType,jdbcType=BIT},
      REVERSAL_BILL_NO = #{reversalBillNo,jdbcType=VARCHAR},
      INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
      REVERSAL_AUDIT_USER = #{reversalAuditUser,jdbcType=INTEGER},
      REVERSAL_AUDIT_STATUS = #{reversalAuditStatus,jdbcType=BIT},
      AUDIT_APPLY_TIME = #{auditApplyTime,jdbcType=TIMESTAMP},
      AUDIT_COMMENTS = #{auditComments,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=BIT},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where INVOICE_REVERSAL_ID = #{invoiceReversalId,jdbcType=INTEGER}
  </update>
  <select id="queryByInvoiceAndRelated" resultMap="BaseResultMap">
    SELECT
        <include refid="Base_Column_List" />
    FROM
    T_INVOICE_REVERSAL
    WHERE INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
    AND REVERSAL_BILL_TYPE = #{reversalBillType,jdbcType=INTEGER}
    AND REVERSAL_BILL_NO = #{reversalBillNo,jdbcType=VARCHAR}
    AND REVERSAL_AUDIT_STATUS = 0
  </select>
</mapper>