package com.vedeng.aftersales.dao;

import com.vedeng.aftersales.component.dto.AfterSaleServiceLabelDto;

import java.util.List;

public interface AfterSaleServiceLabelMapper {
    int deleteByPrimaryKey(Integer afterSaleServiceLabelId);

    int insert(AfterSaleServiceLabelDto record);

    int insertSelective(AfterSaleServiceLabelDto record);

    AfterSaleServiceLabelDto selectByPrimaryKey(Integer afterSaleServiceLabelId);

    int updateByPrimaryKeySelective(AfterSaleServiceLabelDto record);

    int updateByPrimaryKey(AfterSaleServiceLabelDto record);

    List<AfterSaleServiceLabelDto> getAllLabels();
}