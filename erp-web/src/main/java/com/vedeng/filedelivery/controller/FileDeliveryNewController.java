package com.vedeng.filedelivery.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import com.vedeng.system.service.impl.UserServiceImpl;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.TaskService;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.impl.persistence.entity.HistoricActivityInstanceEntity;
import org.activiti.engine.impl.persistence.entity.IdentityLinkEntity;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSON;
import com.ezadmin.common.utils.EzPage;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.authorization.model.Organization;
import com.vedeng.authorization.model.User;
import com.vedeng.common.annotation.SystemControllerLog;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.core.annotation.ExcludeAuthorization;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.R;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.OrderNoDict;
import com.vedeng.common.util.StringUtil;
import com.vedeng.common.validator.FormToken;
import com.vedeng.erp.trader.domain.vo.TraderCustomerInfoVo;
import com.vedeng.erp.trader.dto.TraderSupplierDto;
import com.vedeng.erp.trader.service.TraderCustomerBaseService;
import com.vedeng.erp.trader.service.TraderSupplierApiService;
import com.vedeng.filedelivery.model.FileDeliveryApolloCommon;
import com.vedeng.filedelivery.service.LogisticsOrderService;
import com.vedeng.oa.service.filedelivery.api.FileDeliveryAddressApiService;
import com.vedeng.oa.service.filedelivery.api.FileDeliveryNewApiService;
import com.vedeng.oa.service.filedelivery.vo.FileDeliveryAddressVO;
import com.vedeng.oa.service.filedelivery.vo.FileDeliveryButtonVO;
import com.vedeng.oa.service.filedelivery.vo.FileDeliveryFlowVO;
import com.vedeng.oa.service.filedelivery.vo.FileDeliveryUploadVO;
import com.vedeng.oa.service.filedelivery.vo.FileDeliveryVO;
import com.vedeng.system.service.OrgService;
import com.vedeng.system.service.VerifiesRecordService;
import com.vedeng.trader.dao.TraderAddressMapper;
import com.vedeng.trader.dao.TraderContactMapper;
import com.vedeng.trader.model.TraderAddress;
import com.vedeng.trader.model.vo.TraderContactVo;

import top.ezadmin.common.utils.StringUtils;
import top.ezadmin.web.EzResult;

/**
 * 文件寄送（新）
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping("/logistics/filedeliveryNew")
public class FileDeliveryNewController extends BaseController {
	public static Logger logger = LoggerFactory.getLogger(FileDeliveryNewController.class);

    @Autowired // 自动装载
	private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
	
	@Resource
	private OrderNoDict orderNoDict;
	
	@Autowired
	private FileDeliveryNewApiService fileDeliveryNewApiService;
	
	@Autowired
	private FileDeliveryAddressApiService fileDeliveryAddressApiService;
	
	@Autowired
	@Qualifier("actionProcdefService")
	private ActionProcdefService actionProcdefService;
	
	@Autowired
	@Qualifier("verifiesRecordService")
	private VerifiesRecordService verifiesRecordService;
	
	@Resource
    private LogisticsOrderService logisticsOrderService;
	
	@Autowired
	private FileDeliveryApolloCommon fileDeliveryApolloCommon;
	
	@Resource
	private RedissonClient redissonClient;
	
	@Value("${redis_dbtype}")
	private String redisDbtype;
    @Autowired
    private UserServiceImpl userService;

	/**
	 * 文件寄送-新增跳转
	 * @param session
	 * @return
	 */
	@SuppressWarnings("deprecation")
	@FormToken(save=true)
	@RequestMapping(value = "/addNew")
	@NoNeedAccessAuthorization
	public ModelAndView addFileDelivery(HttpSession session,Integer fileDeliveryId ) {
		logger.info("进入方法");
		ModelAndView mv = new ModelAndView();
		// 获取session中user信息
		User session_user = (User) session.getAttribute(ErpConst.CURR_USER);
		if (session_user == null) {
			return fail(mv);
		}
		try {
			//获取文件寄送主键ID，创建一个文件寄送单号
			FileDeliveryVO fileDeliveryVO = new FileDeliveryVO();
			if(fileDeliveryId==null||fileDeliveryId==0){
				fileDeliveryVO.setCreator(session_user.getUserId());
				fileDeliveryVO.setAddTime(new Date());
				fileDeliveryVO.setIsDeleted(CommonConstants.ONE);
				fileDeliveryVO = fileDeliveryNewApiService.saveFileDeliveryNew(fileDeliveryVO);
				String fileDeliveryNo = orderNoDict.getOrderNum(fileDeliveryVO.getFileDeliveryId(),11);
				fileDeliveryVO.setFileDeliveryNo(fileDeliveryNo);
				mv.addObject("deliveryVO",fileDeliveryVO);
			}else{
				fileDeliveryVO = fileDeliveryNewApiService.getFileDeliveryNew(fileDeliveryId);
				mv.addObject("deliveryVO",fileDeliveryVO);
			}
			mv.setViewName("logistics/filedelivery/newedit");
			mv.addObject("config",fileDeliveryNewApiService.getConfig());
			//文件地址
			mv.setViewName("logistics/filedelivery/newedit");
			return mv;
		}catch(Exception e) {
			logger.error("跳转文件寄送失败",e);
			return fail(mv);
		}
	}

	
	/**
	 * 保存文件寄送,form表单提交
	 * @param request
	 * @param fileDelivery
	 * @return
	 */
	@ResponseBody
	@FormToken(remove=true)
	@RequestMapping(value = "/saveFileDelivery")
	@SystemControllerLog(operationType = "add",desc = "保存文件寄送")
	@NoNeedAccessAuthorization
	public R saveFileDelivery(HttpServletRequest request, FileDeliveryVO fileDeliveryVO) {
		logger.info("保存文件入参：{}",JSON.toJSONString(fileDeliveryVO));
		User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
		if (user == null) {
			return R.error("登录失效，请重新登录");
		}
		String applyOrgName = user.getOrgName();
		//applyOrgName = organization1.getOrgName() + "/" +organization2.getOrgName();
		int success = fileDeliveryNewApiService.updateFileDeliveryNew(fileDeliveryVO,user.getUserId(),applyOrgName);
		if(success<1) {
			//文件保存失败
			return R.error("文件寄送保存失败");
		}
		Map<String, Object> variableMap = new HashMap<String, Object>();
		String taskId = "";
		//开始生成流程(如果没有taskId表示新流程需要生成)
	    variableMap.put("fileDeliveryNew", fileDeliveryVO);
	    variableMap.put("currentAssinee", user.getUsername());
	    variableMap.put("processDefinitionKey","fileDelivery");
	    variableMap.put("businessKey","fileDeliveryNew_" + fileDeliveryVO.getFileDeliveryId());
	    variableMap.put("relateTableKey",fileDeliveryVO.getFileDeliveryId());
	    variableMap.put("relateTable", "T_FILE_DELIVERY_NEW");
	    try {
	    	ResultInfo<?> resultInfo = actionProcdefService.createProcessInstance(request,"fileDeliveryNew","fileDeliveryNew_" + fileDeliveryVO.getFileDeliveryId(),variableMap);
	    	if(Objects.nonNull(resultInfo) && resultInfo.getCode()!=0) {
	    		logger.error("文件寄送流程发起失败,需重新提交");
				return R.error("文件寄送流程发起失败,需重新提交");
	    	}
	    }catch(Exception e) {
	    	logger.error("文件寄送流程发起失败，原因：{}",e);
			return R.error("文件寄送流程发起失败,需重新提交");
	    }
	    try {
	    	//默认申请人通过
	    	//根据BusinessKey获取生成的审核实例
	    	Map<String, Object> historicInfo=actionProcdefService.getHistoric(processEngine, "fileDeliveryNew_"+ fileDeliveryVO.getFileDeliveryId());
	    	if(MapUtils.isEmpty(historicInfo)) {
	    		logger.error("文件寄送流程历史，查询失败");
				return R.error("文件寄送流程历史，查询失败");
	    	}
	    	if(historicInfo.get("endStatus") != "审核完成"){
	    		Task taskInfo = (Task) historicInfo.get("taskInfo");
	    		taskId = taskInfo.getId();
	    		Authentication.setAuthenticatedUserId(user.getUsername());
	    		Map<String, Object> variables = new HashMap<String, Object>();
	    		//设置审核完成监听器回写参数
	    		variables.put("tableName", "T_FILE_DELIVERY_NEW");
	    		variables.put("id", "FILE_DELIVERY_ID");
	    		variables.put("idValue", fileDeliveryVO.getFileDeliveryId());
	    		variables.put("key", "VERIFY_STATUS");
	    		variables.put("value", 2);
	    		//回写数据的表在erp中
	    		variables.put("db", 1);
	    		//默认审批通过
	    		ResultInfo<?> complementStatus = actionProcdefService.complementTask(request,taskId,"",user.getUsername(),variables);
	    		//如果未结束添加审核对应主表的审核状态
//	    		if(!complementStatus.getData().equals("endEvent")){
//	    			verifiesRecordService.saveVerifiesInfo(taskId,0);
//	    		}
	    		//修改主表审核中-置为审批通过
	    		fileDeliveryNewApiService.updateFileDeliveryVerifyStatus(2,user.getUserId(),fileDeliveryVO.getFileDeliveryId());
	    	}
	    	//审核通过，选择的部门为申请人所在部门时，进行面单下单操作，并存储到redis、发送企微通知到指定申请人
			if(fileDeliveryVO.getDeliveryDept()==3 ) {//申请人通过，无需其他人审批
				//测试要收费，注意调用
				try {
					logger.info("==fileDelivery== 发起面单下单操作，寄送表主键：{},用户ID：{},用户名称：{}",fileDeliveryVO.getFileDeliveryId(),user.getUserId(),user.getUsername());
					logisticsOrderService.submit(fileDeliveryVO.getFileDeliveryId(),user.getUserId(),null,"erp",1);
				}catch(Exception e) {
					logger.error("发起面单下单操作异常",e);
				}

			}
	    }catch(Exception e) {
	    	logger.error("发起人自动审核失败，原因：{}",e.getMessage());
	    	return R.error(e.getMessage());
	    }
		return R.success(fileDeliveryVO);
	}
	
	/**
	 * 新文件寄送详情
	 * @param session
	 * @return
	 */
	@FormToken(save=true)
	@RequestMapping(value = {"/getFileDeliveryDetail", "/viewNew"})
	@NoNeedAccessAuthorization
	public ModelAndView getFileDeliveryDetail(HttpSession session, Integer fileDeliveryId) {
		ModelAndView mv = new ModelAndView();
		// 获取session中user信息
		User session_user = (User) session.getAttribute(ErpConst.CURR_USER);
		if (session_user == null) {
			return fail(mv);
		}
		//获取文件寄送主键ID，创建一个文件寄送单号
		FileDeliveryVO fileDeliveryVO = fileDeliveryNewApiService.getFileDeliveryNew(fileDeliveryId);
		List<FileDeliveryAddressVO> fileDeliveryAddressVOList = fileDeliveryAddressApiService.getFileDeliveryAddress(fileDeliveryId);
		fileDeliveryAddressVOList.forEach(item->{
			if(item.getTraderType()!=null&&item.getTraderType()!=3&&item.getContactType()!=null&&item.getContactType()==1){
				item.setTraderContactMobileHidden(StringUtil.hideMiddleFourDigits(item.getTraderContactMobile()));
			}else{
				item.setTraderContactMobileHidden(item.getTraderContactMobile());
			}
		});
		mv.addObject("deliveryVO", fileDeliveryVO);
		mv.addObject("deliveryAddressVO", fileDeliveryAddressVOList);
		// 获取session中user信息
		mv.addObject("curr_user", session_user);
		try {
			Map<String, Object> historicInfo=actionProcdefService.getHistoric(processEngine, "fileDeliveryNew_"+ fileDeliveryId);
			Task taskInfo = (Task) historicInfo.get("taskInfo");
			mv.addObject("taskInfo", historicInfo.get("taskInfo"));
			mv.addObject("commentMap", historicInfo.get("commentMap"));
			String verifyUsers = null;
	    	if(null!=taskInfo){
	    	    Map<String, Object> taskInfoVariables= actionProcdefService.getVariablesMap(taskInfo);
	    	    verifyUsers = (String) taskInfoVariables.get("verifyUsers");
	    	}
	    	mv.addObject("verifyUsers", verifyUsers);
	    	//富文本说明
	    	mv.addObject("config",fileDeliveryNewApiService.getConfig());
			mv.setViewName("logistics/filedelivery/newview");
			Integer positionType=0;
			if(session_user!=null && CollectionUtils.isNotEmpty(session_user.getPositions()) ) {
				positionType = session_user.getPositions().get(0).getType();
			}
			//用户按钮权限展示
			FileDeliveryButtonVO fileDeliveryButtonVO = getFileDeliveryButtonVO(fileDeliveryVO,session_user,historicInfo,positionType);
			List<FileDeliveryFlowVO> fileDeliveryFlowVOList = getFileDeliveryFlowVOList(fileDeliveryVO,session_user,historicInfo);
			mv.addObject("fileDeliveryButtonVO",fileDeliveryButtonVO);
			mv.addObject("fileDeliveryFlowVOList",fileDeliveryFlowVOList);
			//如果财务，只返回财务
			//打印按钮（已审核、未寄送/已寄送、未关闭）
			if(positionType == 314) {
				mv.addObject("emsprintname",fileDeliveryApolloCommon.emsprintname_cw);
				mv.addObject("sfprintname",fileDeliveryApolloCommon.sfprintname_cw);
			}else {
				mv.addObject("emsprintname",fileDeliveryApolloCommon.emsprintname);
				mv.addObject("sfprintname",fileDeliveryApolloCommon.sfprintname);
			}
			mv.addObject("ztprintname",fileDeliveryApolloCommon.ztprintname);
			
		} catch (Exception e) {
			logger.error("getFileDeliveryDetail:", e);
			return fail(mv);
		}
		return mv;
	}
	
	@SuppressWarnings("unchecked")
	private List<FileDeliveryFlowVO> getFileDeliveryFlowVOList(FileDeliveryVO fileDeliveryVO, User session_user,
			Map<String, Object> historicInfo) {
		//logger.info("getFileDeliveryFlowVOList 流程信息：{}",JSON.toJSONString(historicInfo));
		List<FileDeliveryFlowVO> fileDeliveryFlowVOList = new ArrayList<>();
		//当前待审核人
		Map<String,Object> candidateUserMap = (Map<String, Object>) historicInfo.get("candidateUserMap");
		//流程进度
		List<HistoricActivityInstanceEntity> entityList =  (List<HistoricActivityInstanceEntity>) historicInfo.get("historicActivityInstance");
		Map<String,Object> commentMap = (Map<String, Object>) historicInfo.get("commentMap");
		for (HistoricActivityInstanceEntity entity : entityList) {
			if(entity.getActivityType().equals("exclusiveGateway")) {
				continue;
			}
			FileDeliveryFlowVO fileDeliveryFlowVO = new FileDeliveryFlowVO();
			fileDeliveryFlowVO.setFlowName(entity.getActivityName().equals("审核完成")?"结束":entity.getActivityName());
			if(entity.getActivityType().equals("startEvent")) {
				fileDeliveryFlowVO.setOperatorName((String)historicInfo.get("startUser"));
			}else if(entity.getActivityType().equals("userTask") && StringUtils.isBlank(entity.getAssignee())) {
				String operatorName = "";
				List<IdentityLinkEntity> taskList = (List<IdentityLinkEntity>) candidateUserMap.get(entity.getTaskId());
				if(CollectionUtils.isNotEmpty(taskList)) {
					List<String> operatorNames = taskList.stream().map(IdentityLinkEntity::getUserId).collect(Collectors.toList());
					operatorName = String.join(",", operatorNames);
				}
				fileDeliveryFlowVO.setOperatorName(operatorName);
			}else {
				fileDeliveryFlowVO.setOperatorName(entity.getAssignee());
			}
			String taskId = entity.getTaskId();
			String remark = (String)commentMap.get(taskId);
			if(StringUtils.isNotBlank(remark)) {
				fileDeliveryFlowVO.setRemark(remark);
			}else {
				fileDeliveryFlowVO.setRemark("");
			}
			String startTimeStr = DateUtil.DateToString(entity.getStartTime(),"yyyy-MM-dd HH:mm:ss") ;
			fileDeliveryFlowVO.setOperatorTime(startTimeStr);
			fileDeliveryFlowVOList.add(fileDeliveryFlowVO);
		}
		return fileDeliveryFlowVOList;
	}


	@SuppressWarnings("unchecked")
	private FileDeliveryButtonVO getFileDeliveryButtonVO(FileDeliveryVO fileDeliveryVO, User session_user,
			Map<String, Object> historicInfo, Integer positionType) {
		//审核状态【0未审核1审核中2审核通过3审核不通过】
		Integer verifyStatus = fileDeliveryVO.getVerifyStatus();
		//是否已关闭【1：已关闭；2：未关闭】
		Integer close = fileDeliveryVO.getIsClosed();
		//寄送状态【0未寄送1已寄送】
		Integer deliveryStatus = fileDeliveryVO.getDeliveryStatus();
		Task taskInfo = (Task) historicInfo.get("taskInfo");
		Map<String,Object> candidateUserMap = (Map<String, Object>) historicInfo.get("candidateUserMap");
		boolean belong = (boolean) candidateUserMap.get("belong");
		//通过按钮（审核中、未寄送、未关闭）
		Integer passButton = ((verifyStatus==1 && deliveryStatus==0 && close==0 && taskInfo!=null && taskInfo.getAssignee() == session_user.getUsername()) || belong)?1:0;
		//驳回按钮（审核中、未寄送、未关闭）
		Integer noPassButton = ((verifyStatus==1 && deliveryStatus==0 && close==0 && taskInfo!=null && taskInfo.getAssignee() == session_user.getUsername()) || belong)?1:0;
		FileDeliveryButtonVO fileDeliveryButtonVO = new FileDeliveryButtonVO();
		//打印按钮，审核通过、未关闭、有权限
		Integer printButton = 0;
		if(userService.judgeHasPermission("/logistics/filedeliveryNew/print.do") && verifyStatus==2 && close==0 ){
			printButton=1;
		}
		//关闭按钮（已审核、未寄送、未关闭、有权限）
		Integer closeButton = 0;
		if(userService.judgeHasPermission("/logistics/filedeliveryNew/closeFileDelivery.do") && verifyStatus==2 && deliveryStatus==0 && close==0){
			closeButton=1;
		}
		fileDeliveryButtonVO.setPrintButton(printButton);
		fileDeliveryButtonVO.setPassButton(passButton);
		fileDeliveryButtonVO.setNoPassButton(noPassButton);
		fileDeliveryButtonVO.setCloseButton(closeButton);
		return fileDeliveryButtonVO;
	}

	/**
	 * 点击审核通过、驳回跳转弹框
	 * @param session
	 * @param fileDeliveryId
	 * @param taskId
	 * @param pass
	 * @return
	 */
	@FormToken(save=true)
	@ResponseBody
	@RequestMapping(value = "/complement")
	@NoNeedAccessAuthorization
	public ModelAndView complement(HttpSession session,Integer fileDeliveryId, String taskId, Boolean pass) {
		ModelAndView mv = new ModelAndView();
		mv.addObject("taskId", taskId);
		mv.addObject("pass", pass);
		mv.addObject("fileDeliveryId", fileDeliveryId);
		mv.setViewName("logistics/filedelivery/complement");
		return mv;
	}
	
	/**
	 * 文件寄送审核操作
	 * @param request
	 * @param fileDeliveryId
	 * @param taskId
	 * @param comment
	 * @param pass
	 * @param session
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/complementTask")
	@NoNeedAccessAuthorization
	public R complementTask(HttpServletRequest request,Integer fileDeliveryId, String taskId, String comment, Boolean pass,
			HttpSession session) {
		User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
		if (user == null) {
			return R.error("任务完成操作失败");
		}
		
		FileDeliveryVO fileDeliveryVO = fileDeliveryNewApiService.getFileDeliveryNew(fileDeliveryId);
		//已审核通过
		if(fileDeliveryVO.getVerifyStatus()==2) {
			return R.error("审核状态为已审核，不可执行该操作，请稍后再试！");
		}
		if(fileDeliveryVO.getVerifyStatus()==3) {
			return R.error("审核状态为已驳回，不可执行该操作，请稍后再试！");
		}
		
		Map<String, Object> historicInfo=actionProcdefService.getHistoric(processEngine, "fileDeliveryNew_"+ fileDeliveryId);
		Map<String,Object> candidateUserMap = (Map<String, Object>) historicInfo.get("candidateUserMap");
		boolean belong = (boolean) candidateUserMap.get("belong");
		if(!belong) {
			return R.error("您非当前审批人");
		}
		
		Map<String, Object> variables = new HashMap<String, Object>();
		variables.put("pass", pass);
		variables.put("updater",user.getUserId());
		try {
		    Integer status = 0;
			if(pass){
			    //如果审核通过
			     status = 0;
			}else{
			    //如果审核不通过
			    status = 2;
			    //如果不通过审核
			    //获取任务的Service，设置和获取流程变量  
	    	    TaskService taskService = processEngine.getTaskService();
	    	    String tableName= (String) taskService.getVariable(taskId, "tableName");
	    	    String id=(String) taskService.getVariable(taskId, "id");
	    	    Integer idValue=(Integer) taskService.getVariable(taskId, "idValue");
	    	    String key= (String) taskService.getVariable(taskId, "key");
	    	    if(tableName != null && id != null && idValue != null && key != null){
	    		actionProcdefService.updateInfo(tableName, id, idValue, key, 3, 1);
	    	    }
	    	    verifiesRecordService.saveVerifiesInfo(taskId,status);	
			}
		    ResultInfo<?> complementStatus = actionProcdefService.complementTask(request,taskId,comment,user.getUsername(),variables);
		    if(complementStatus.getCode()==0) {
		    	//审核通过，选择的部门为申请人所在部门时，进行面单下单操作，并存储到redis、发送企微通知到指定申请人
		    	if(pass && fileDeliveryVO.getDeliveryDept()==3 ) {
		    		//测试要收费，注意调用
		    		try {
		    			logger.info("==fileDelivery== 发起面单下单操作，寄送表主键：{},用户ID：{},用户名称：{}",fileDeliveryId,user.getUserId(),user.getUsername());
			    		logisticsOrderService.submit(fileDeliveryId,user.getUserId(),null,"erp",1);
		    		}catch(Exception e) {
		    			logger.error("发起面单下单操作异常",e);
		    		}
		    		
		    	}
		    }
			return R.success();
		} catch (Exception e) {
			logger.error("file delivery complementTask", e);
			return R.error("审核失败！");
		}
	}
	
	
	/**
	 * erp生成打印码，可能存在失败的情况，express应用使用打印码进行打印时，需要判断打印条数是否都满足，如果不满足需要重新生成打印链接，并更新表数据
	 * @param session
	 * @param fileDeliveryId
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/queryPrintUrlByPrintCode")
	@NoNeedAccessAuthorization
	public R queryPrintUrlByPrintCode(HttpSession session,Integer fileDeliveryId) {
		try {
			logger.info("==fileDelivery== 发起面单下单操作，寄送表主键：{},用户ID：{}",fileDeliveryId,"系统补偿");
    		List<String> printUrlList = logisticsOrderService.submit(fileDeliveryId,1,null,"express",3);
			return R.success(printUrlList);
		}catch(Exception e) {
			logger.error("获取面单下载地址链接失败",e);
			return R.error("获取面单下载地址链接失败");
		}
	}
	
	

	/**
	 * 新增收件单位
	 * @param session
	 * @return
	 */
	@ResponseBody
	@FormToken(save=true)
	@RequestMapping(value = "/saveFileDeliveryAddress")
	@NoNeedAccessAuthorization
	public ModelAndView saveFileDeliveryAddress(HttpSession session,FileDeliveryAddressVO fileDeliveryAddressVO) {
		ModelAndView mav = new ModelAndView();
		User user = (User) session.getAttribute(Consts.SESSION_USER);
		if (user == null) {
			return fail(mav);
		}
		try {
			Integer userId = user.getUserId();
			FileDeliveryAddressVO deliveryAddressVO = fileDeliveryAddressApiService.saveFileDeliveryAddress(fileDeliveryAddressVO,userId);
			mav.addObject("deliveryAddressVO", deliveryAddressVO);
			return success(mav);
		}catch(Exception e) {
			logger.error("新增收件单位失败",e);
		}
		return success(mav);
	}
	
	
	/**
	 * 删除收件单位(ajax请求)
	 * @param session
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/deleteFileDeliveryAddress")
	@NoNeedAccessAuthorization
	public R deleteFileDeliveryAddress(HttpSession session,Integer id) {
		ModelAndView mav = new ModelAndView();
		User user = (User) session.getAttribute(Consts.SESSION_USER);
		if (user == null) {
			return R.error("登录过期");
		}
		int succsee = fileDeliveryAddressApiService.deleteFileDeliveryAddressById(id,user.getUserId());
		logger.info("删除收件单位",(succsee>0?"成功":"失败"));
		return R.success();
	}

	/**
	 * 关闭
	 * todo  收回打印码，判断权限：未关闭未寄送 ，角色是313或314且非审核不通过       或 是申请人且审核不通过
	 *
	 * @param session
	 * @param id
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/closeFileDelivery")
	@NoNeedAccessAuthorization
	public R closeFileDelivery(HttpSession session,Integer fileDeliveryId,String comments) {
		User user = (User) session.getAttribute(Consts.SESSION_USER);
		if (user == null) {
			return R.error("未登录");
		}
		//关闭按钮（已审核、未寄送、未关闭）
		FileDeliveryVO fileDeliveryVO = fileDeliveryNewApiService.getFileDeliveryNew(fileDeliveryId);
		if(fileDeliveryVO.getIsClosed()==1) {
			return R.error("关闭状态为已关闭，不可执行该操作，请稍后再试");
		}
		if(Objects.nonNull(fileDeliveryVO.getDeliveryTime())) {
			return R.error("寄送状态为已寄送，不可执行该操作，请稍后再试");
		}
		if(fileDeliveryVO.getVerifyStatus()==3) {
			return R.error("审核状态为已驳回，不可执行该操作，请刷新后再试");
		}
		fileDeliveryNewApiService.updateFileDeliveryColseStatus(1,comments, user.getUserId(),fileDeliveryId);
		//清除打印码并回收
		try {
			RBucket<String> fileIdAndCodeRBucket = redissonClient.getBucket(redisDbtype+"file_delivery:printCodeAndId:"+fileDeliveryId);
			String printCode = fileIdAndCodeRBucket.get();
			if(StringUtils.isNotBlank(printCode)) {
				RBucket<String> rBucket = redissonClient.getBucket(redisDbtype+"file_delivery:printCode:"+printCode);
				rBucket.delete();
				//code的集合也要设置相同的过期时间，以便进行回收
				RMapCache<String, String> usedCodesMap = redissonClient.getMapCache(redisDbtype+"usedCodes");
				//usedCodesMap 回收打印码
				usedCodesMap.remove(printCode);
			}

			
		}catch(Exception e) {
			logger.error("打印码回收失败",e);
		}
		return R.success();
	}
	
	/**
	 * 批量导入收件单位（类型其他）
	 * @param file
	 * @param fileDeliveryId
	 * @return
	 */
	@RequestMapping("/upload")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<?> upload(HttpSession session,@RequestPart("file") MultipartFile file,@RequestParam(value = "fileDeliveryId", required = true) Integer fileDeliveryId) {
		User user = (User) session.getAttribute(Consts.SESSION_USER);
		if (user == null) {
			return R.error("未登录");
		}
		FileDeliveryUploadVO fileDeliveryUploadVO = null;
        try {
        	fileDeliveryUploadVO = fileDeliveryNewApiService.upload(file,fileDeliveryId,user.getUserId());
        } catch (Exception e) {
            log.error("批量导入收件单位（类型其他）上传失败",e);
            return R.error(e.getMessage());
        }
        return R.success(fileDeliveryUploadVO);
    }


	@Autowired
	TraderCustomerBaseService traderCustomerBaseService;
	@Autowired
	TraderSupplierApiService traderSupplierApiService;

	@RequestMapping(value = "/queryTrader")
	@NoNeedAccessAuthorization
	@ResponseBody
	public EzResult queryTraderCustomerInfoByName(@RequestParam(value = "pageIndex", required = false,defaultValue = "1") Integer pageIndex,
												  String keyword, String traderType) {
		EzResult a=EzResult.instance() ;
		a.data(new ArrayList<>());
		if(org.apache.commons.lang3.StringUtils.isBlank(keyword)){
			a.count(0);
			return a;
		}
		//客户
		int limitC=10;
		EzPage page=new EzPage(pageIndex,limitC,"");
		if(StringUtils.isBlank(traderType)||StringUtils.equals(traderType,"1")){
			List<TraderCustomerInfoVo> list=traderCustomerBaseService.getTraderCustomerByTraderName(keyword,page.getStartRecord(),limitC);
			if(!CollectionUtils.isEmpty(list)){
				list.forEach(item->{
					a.dataKVList(item.getTraderId()+"",item.getTraderName());
				});
				long count=traderCustomerBaseService.countTraderCustomerByTraderName(keyword);
				page.setTotalRecord(count);
				 a.count(page.getTotalPage());

			}else{
				a.count(0);
			}
		}else if(StringUtils.equals(traderType,"2")){
			List<TraderSupplierDto> list= traderSupplierApiService.getTraderSupplierByTraderName(keyword, page.getStartRecord(),limitC);
			if(!CollectionUtils.isEmpty(list)){
				list.forEach(item->{
					a.dataKVList(item.getTraderId()+"",item.getTraderName());
				});
				long count=traderSupplierApiService.countTraderSupplierByTraderName(keyword);
				page.setTotalRecord(count);
				a.count(page.getTotalPage());
			}else{
				a.count(0);
			}
		}
		return a;
 	}

	
	
	@Autowired
	private TraderContactMapper traderContactMapper;
	@Autowired
	@Qualifier("traderAddressMapper")
	private TraderAddressMapper traderAddressMapper;
	/**
	 * 客户、收货客户、收票客户联系人的相关信息
	 * @param
	 * @return R
	 */
	@RequestMapping(value = "/getTraderContactInfo", method = RequestMethod.POST)
	@ResponseBody
	@ExcludeAuthorization
	@NoNeedAccessAuthorization
	public R<?> getTraderContactInfo(String name, String traderIdToUse, Integer traderType){
		TraderContactVo traderContactVo = new TraderContactVo();
		traderContactVo.setTraderId(Integer.valueOf(traderIdToUse));
		traderContactVo.setIsEnable(1);
		traderContactVo.setName(name);
		traderContactVo.setTraderType(traderType);
		List<TraderContactVo> tcvList = traderContactMapper.getTraderContactVoListForOrderSearch(traderContactVo);
		List<TraderContactVo> result=new ArrayList<>();
		if(!CollectionUtils.isEmpty(tcvList)){
			tcvList.forEach(item->{
				item.setMobile(StringUtil.hideMiddleFourDigits(item.getMobile()));
				if(StringUtils.isNotBlank(item.getName())){
					item.setName(item.getName()+" "+item.getMobile());
					result.add(item);
				}
			});
		}
		return R.success(result);
	}

	/**
	 * 客户、收货客户、收票客户地址的相关信息
	 * @param
	 * @return R
	 */
	@RequestMapping(value = "/getTraderDressInfo", method = RequestMethod.POST)
	@ResponseBody
	@ExcludeAuthorization
	@NoNeedAccessAuthorization
	public R<?> getTraderDressInfo(String name,String traderIdToUse, Integer traderType){
		TraderAddress ta = new TraderAddress();
		ta.setTraderId(Integer.valueOf(traderIdToUse));
		ta.setIsEnable(1);
		ta.setTraderType(traderType);
		ta.setAreaQuery(name);
		List<TraderAddress> taList = traderAddressMapper.getTraderAddressInfoList(ta);
		if (CollectionUtils.isNotEmpty(taList)) {
			taList.forEach(item -> {
				String address = item.getAddress();
				item.setAddress(org.apache.commons.lang3.StringUtils.isNotBlank(address) ? address.replaceAll("(\r\n|\r|\n|\n\r)", "") : "");
			});
		}
		return R.success(taList);
	}
	@ResponseBody
	@RequestMapping(value = "/updateLogisticNos")
	@NoNeedAccessAuthorization
	public R updateLogisticNos(HttpSession session,Integer addressId,String logisticNos) {
		ModelAndView mav = new ModelAndView();
		User user = (User) session.getAttribute(Consts.SESSION_USER);
		if (user == null) {
			return R.error("未登录");
		}
		try {
			//更新地址表快递信息，同时更新主表快递号信息
			fileDeliveryNewApiService.updateLogisticNos(addressId,logisticNos,user.getUserId());
			return R.success();
		}catch(Exception e) {
			logger.error("更新快递单号失败",e);
		}
		return R.error("更新快递单号失败");
		
	}
	@ResponseBody
	@RequestMapping(value = "/print")
	public R print(HttpSession session,Integer fileDeliveryId ,Integer printType) {
		ModelAndView mav = new ModelAndView();
		User user = (User) session.getAttribute(Consts.SESSION_USER);
		if (user == null) {
			return R.error("未登录");
		}
		List<String> list=new ArrayList<>();
		FileDeliveryVO fileDeliveryVO=fileDeliveryNewApiService.getDeliveryById(fileDeliveryId);
		if(fileDeliveryVO.getVerifyStatus()!=2||fileDeliveryVO.getIsClosed()!=0){
			return R.error("关闭状态为已关闭，不可执行该操作，请稍后再试");
		}

		if(fileDeliveryVO.getLogisticsName()==null){
			fileDeliveryVO.setLogisticsName(printType);
			//update
			fileDeliveryNewApiService.setFileDeliveryLoginsticsName(fileDeliveryId,printType);
		}else{
			printType=fileDeliveryVO.getLogisticsName();
		}
		//传递printType参数
		//对所有未生成面单的地址 生成面单
		logisticsOrderService.submit(fileDeliveryId,user.getUserId(),printType,"erp",2);

		List<FileDeliveryAddressVO> addressVOList=fileDeliveryNewApiService.getDeliveryAdressList(fileDeliveryId);
		if(CollectionUtils.isEmpty(addressVOList)){
			return R.error("没有可用的地址，请先添加地址。");
		}
		addressVOList.forEach(item->{
            try {
				if(StringUtils.isNotBlank(item.getExpressLabelurl())){
					List<String> urls = Arrays.asList(item.getExpressLabelurl().split(","));
					list.addAll(urls);
				}
            } catch (Exception e) {
                logger.error(""+item.getId(),e);
            }
        });
		if(CollectionUtils.isEmpty(list) && printType!=4){
			return R.error("面单获取失败。");
		}
		//返回面单地址
		return R.success(list);
	}
	


}
