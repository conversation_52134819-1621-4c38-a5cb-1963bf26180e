package com.vedeng.order.service;

import com.vedeng.common.model.ResultAssist;
import com.vedeng.finance.model.BuyorderData;
import com.vedeng.order.model.vo.BuyorderGoodsVo;

import java.math.BigDecimal;
import java.util.List;

/**
 * <b>Description:</b><br> 采购订单数据
 *
 * <AUTHOR>
 * @Note <b>ProjectName:</b> dbcenter
 * <br><b>PackageName:</b> com.vedeng.service.common
 * <br><b>ClassName:</b> BuyorderDataService
 * <br><b>Date:</b> 2017年10月17日 上午10:55:15
 */
public interface BuyorderDataService {
    /**
     * <b>Description:</b><br> 已付款金额(不含账期) --- 业务类型(交易类型2支出5转出)减去（交易类型1收入4转入）
     *
     * @param buyorderId
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年10月17日 上午10:56:41
     */
    BigDecimal getPaymentAmount(Integer buyorderId);

    /**
     * <b>Description:</b><br> 已付款金额east用
     *
     * @param buyorderId
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2018年5月29日 下午5:12:28
     */
    BigDecimal getPaymentAndPeriodAmount(Integer buyorderId);

    /**
     * <b>Description:</b><br> 应付账期额 --- 已付款账期金额减去退还账期金额减去信用还款
     *
     * @param buyorderId
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年10月17日 上午10:56:43
     */
    BigDecimal getLackAccountPeriodAmount(Integer buyorderId);

    /**
     * <b>Description:</b><br> 已收票总额 --- 已收票总额减去退票总额
     *
     * @param buyorderId
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年10月17日 上午10:56:45
     */
    BigDecimal getInvoiceAmount(Integer buyorderId);

    /**
     * <b>Description:</b><br>
     *
     * @param buyorderId
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年10月17日 上午10:56:48
     */
    BuyorderData getBuyorderData(Integer buyorderId);

    /**
     * <b>Description:</b><br> 订单产品发货数量 --- 根据快递单查询发货数量
     *
     * @param buyorderGoodsId
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年10月19日 下午3:28:39
     */
    Integer getDeliveryNum(Integer buyorderGoodsId);

    /**
     * <b>Description:</b><br> 获取采购订单相关票款信息
     *
     * @param buyorderIdList
     * @return
     * @Note <b>Author:</b> leo.yang
     * <br><b>Date:</b> 2017年10月23日 下午4:12:04
     */
    List<BuyorderData> getBuyorderDatas(List<Integer> buyorderIdList);

    /**
     * <b>Description:</b><br> 采购付款金额（订单总额-退款金额）--- 订单产品数量-退货产品数量-仅退款售后的金额，计算出订单金额
     *
     * @param buyorderId
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年10月24日 上午9:13:28
     */
    BigDecimal getRealAmount(Integer buyorderId);

    /**
     * <b>Description:</b><br> 采购订单实际金额（订单总额-退款金额）
     *
     * @param buyOrderIdList
     * @param companyId
     * @return
     * @Note <b>Author:</b> duke
     * <br><b>Date:</b> 2018年9月14日 下午2:26:15
     */
    List<ResultAssist> getNewRealAmount(List<Integer> buyOrderIdList, Integer companyId);

    /**
     * <b>Description:</b><br> 订单账期金额 --- 已付款账期金额减去退还账期金额
     *
     * @param buyorderId
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年10月24日 上午9:14:42
     */
    BigDecimal getPeriodAmount(Integer buyorderId);

    /**
     * <b>Description:</b><br> 获取采购产品已收票数量 --- 收票数量减去退票数量（发票单价加，发票单价负减）
     *
     * @param buyorderGoodsId
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年11月15日 下午5:53:30
     */
    BigDecimal getHaveInvoiceNums(Integer buyorderGoodsId);

    /**
     * 获取采购产品已收票数量 --- 收票数量减去退票数量（发票单价加，发票单价负减） 批量
     * <b>Description:</b><br>
     *
     * @param bgvList
     * @return
     * @Note <b>Author:</b> Cooper
     * <br><b>Date:</b> 2018年7月2日 下午2:14:03
     */
    List<BuyorderGoodsVo> getHaveInvoiceNumsByList(List<BuyorderGoodsVo> bgvList);

    /**
     * <b>Description:</b><br> 订单预付款金额（订单预付款金额-退款金额）-- 订单产品数量减去退货产品数量后，计算出订单预付款金额金额
     *
     * @param buyorderId
     * @return
     * @Note <b>Author:</b> leo.yang
     * <br><b>Date:</b> 2018年2月1日 下午8:17:58
     */
    BigDecimal getRealPreAmount(Integer buyorderId);

    /**
     * <b>Description:</b><br> 已付款金额(不含账期) --- 业务类型(交易类型2支出5转出)减去（交易类型1收入4转入）
     * @param buyorderId
     * @return
     * @Note
     * <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年10月17日 上午10:56:41
     */
    public BigDecimal getPaymentAmountDB(Integer buyorderId);

    BigDecimal getAfterSaleServiceAmount(Integer orderId);
}
