<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.finance.mapper.RInvoiceDetailJOperateLogMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.finance.domain.entity.RInvoiceDetailJOperateLogEntity">
    <!--@mbg.generated-->
    <!--@Table T_R_INVOICE_DETAIL_J_OPERATE_LOG-->
    <id column="R_INVOICE_DETAIL_J_OPERATE_LOG_ID" jdbcType="INTEGER" property="RInvoiceDetailJOperateLogId" />
    <result column="INVOICE_DETAIL_ID" jdbcType="INTEGER" property="invoiceDetailId" />
    <result column="OPERATE_LOG_ID" jdbcType="INTEGER" property="operateLogId" />
    <result column="INVOICE_ID" jdbcType="INTEGER" property="invoiceId" />
    <result column="DETAILGOODS_ID" jdbcType="INTEGER" property="detailGoodsId" />
    <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId" />
    <result column="SKU" jdbcType="VARCHAR" property="sku" />
    <result column="NUM" jdbcType="DECIMAL" property="num" />
    <result column="OPERATE_TYPE" jdbcType="INTEGER" property="operateType" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    R_INVOICE_DETAIL_J_OPERATE_LOG_ID, INVOICE_DETAIL_ID, OPERATE_LOG_ID, INVOICE_ID, 
    DETAILGOODS_ID, GOODS_ID, SKU, NUM, OPERATE_TYPE, IS_DELETE, ADD_TIME, CREATOR,
    MOD_TIME, UPDATER
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_R_INVOICE_DETAIL_J_OPERATE_LOG
    where R_INVOICE_DETAIL_J_OPERATE_LOG_ID = #{RInvoiceDetailJOperateLogEntityId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_R_INVOICE_DETAIL_J_OPERATE_LOG
    where R_INVOICE_DETAIL_J_OPERATE_LOG_ID = #{RInvoiceDetailJOperateLogEntityId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="R_INVOICE_DETAIL_J_OPERATE_LOG_ID" keyProperty="RInvoiceDetailJOperateLogEntityId" parameterType="com.vedeng.erp.finance.domain.entity.RInvoiceDetailJOperateLogEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_INVOICE_DETAIL_J_OPERATE_LOG (INVOICE_DETAIL_ID, OPERATE_LOG_ID, INVOICE_ID, 
      DETAILGOODS_ID, GOODS_ID, SKU, 
      NUM,  OPERATE_TYPE,
      IS_DELETE, ADD_TIME, CREATOR, 
      MOD_TIME, UPDATER)
    values (#{invoiceDetailId,jdbcType=INTEGER}, #{operateLogId,jdbcType=INTEGER}, #{invoiceId,jdbcType=INTEGER}, 
      #{detailGoodsId,jdbcType=INTEGER}, #{goodsId,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR}, 
      #{num,jdbcType=DECIMAL}, #{operateType,jdbcType=INTEGER},
      #{isDelete,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{modTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="R_INVOICE_DETAIL_J_OPERATE_LOG_ID" keyProperty="RInvoiceDetailJOperateLogEntityId" parameterType="com.vedeng.erp.finance.domain.entity.RInvoiceDetailJOperateLogEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_INVOICE_DETAIL_J_OPERATE_LOG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="invoiceDetailId != null">
        INVOICE_DETAIL_ID,
      </if>
      <if test="operateLogId != null">
        OPERATE_LOG_ID,
      </if>
      <if test="invoiceId != null">
        INVOICE_ID,
      </if>
      <if test="detailGoodsId != null">
        DETAILGOODS_ID,
      </if>
      <if test="goodsId != null">
        GOODS_ID,
      </if>
      <if test="sku != null">
        SKU,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="operateType != null">
        OPERATE_TYPE,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="invoiceDetailId != null">
        #{invoiceDetailId,jdbcType=INTEGER},
      </if>
      <if test="operateLogId != null">
        #{operateLogId,jdbcType=INTEGER},
      </if>
      <if test="invoiceId != null">
        #{invoiceId,jdbcType=INTEGER},
      </if>
      <if test="detailGoodsId != null">
        #{detailGoodsId,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null">
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        #{num,jdbcType=DECIMAL},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.finance.domain.entity.RInvoiceDetailJOperateLogEntity">
    <!--@mbg.generated-->
    update T_R_INVOICE_DETAIL_J_OPERATE_LOG
    <set>
      <if test="invoiceDetailId != null">
        INVOICE_DETAIL_ID = #{invoiceDetailId,jdbcType=INTEGER},
      </if>
      <if test="operateLogId != null">
        OPERATE_LOG_ID = #{operateLogId,jdbcType=INTEGER},
      </if>
      <if test="invoiceId != null">
        INVOICE_ID = #{invoiceId,jdbcType=INTEGER},
      </if>
      <if test="detailGoodsId != null">
        DETAILGOODS_ID = #{detailGoodsId,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null">
        GOODS_ID = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=DECIMAL},
      </if>
      <if test="operateType != null">
        OPERATE_TYPE = #{operateType,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where R_INVOICE_DETAIL_J_OPERATE_LOG_ID = #{RInvoiceDetailJOperateLogId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.finance.domain.entity.RInvoiceDetailJOperateLogEntity">
    <!--@mbg.generated-->
    update T_R_INVOICE_DETAIL_J_OPERATE_LOG
    set INVOICE_DETAIL_ID = #{invoiceDetailId,jdbcType=INTEGER},
      OPERATE_LOG_ID = #{operateLogId,jdbcType=INTEGER},
      INVOICE_ID = #{invoiceId,jdbcType=INTEGER},
      DETAILGOODS_ID = #{detailGoodsId,jdbcType=INTEGER},
      GOODS_ID = #{goodsId,jdbcType=INTEGER},
      SKU = #{sku,jdbcType=VARCHAR},
      NUM = #{num,jdbcType=DECIMAL},
      OPERATE_TYPE = #{operateType,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER}
    where R_INVOICE_DETAIL_J_OPERATE_LOG_ID = #{RInvoiceDetailJOperateLogId,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2022-11-28-->
  <select id="findAllByInvoiceId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_R_INVOICE_DETAIL_J_OPERATE_LOG
    where INVOICE_ID=#{invoiceId,jdbcType=INTEGER}
    and IS_DELETE = 0
  </select>

  <select id="getValidWarehousingLogByRelatedIds" resultType="com.vedeng.erp.finance.dto.InvoiceRelationOperateLogDto">
    SELECT
      T1.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID LOG_ID,
      T1.RELATED_ID,
      T1.GOODS_ID,
      ABS(T1.NUM) - SUM(
              IFNULL( T2.NUM, 0 )) CAN_RELATION_NUM
    FROM
      T_WAREHOUSE_GOODS_OUT_IN_ITEM T1
        LEFT JOIN T_R_INVOICE_DETAIL_J_OPERATE_LOG T2 ON T1.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID = T2.OPERATE_LOG_ID
      AND T2.OPERATE_TYPE = #{operateType,jdbcType=INTEGER}
      AND T2.IS_DELETE = 0
    WHERE
      T1.OPERATE_TYPE = #{operateType,jdbcType=INTEGER}
      AND T1.IS_DELETE = 0
      AND T1.RELATED_ID IN
    <foreach collection="detailGoodsIdList" item="detailGoodsId" index="index" open="(" close=")" separator=",">
      #{detailGoodsId,jdbcType=INTEGER}
    </foreach>
    GROUP BY
      T1.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID
    HAVING
        CAN_RELATION_NUM > 0
  </select>

  <select id="sumWarehouseGoodsOutInItem" resultType="bigDecimal">
    SELECT
    COALESCE(SUM(NUM),0)
    FROM
      T_WAREHOUSE_GOODS_OUT_IN_ITEM
    WHERE
      OPERATE_TYPE in (1,17)
      AND RELATED_ID =  #{detailGoodsId,jdbcType=INTEGER}
  </select>
</mapper>