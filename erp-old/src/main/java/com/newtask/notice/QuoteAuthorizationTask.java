package com.newtask.notice;

import com.vedeng.common.activiti.entity.ActivitiTaskUnDoEntity;
import com.vedeng.common.activiti.service.ActivitiNoticeService;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.order.service.QuoteService;
import com.wms.service.WmsSampleOutService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/12/14
 */
@Component
@JobHandler(value = "QuoteAuthorizationTask")
public class QuoteAuthorizationTask extends AbstractJobHandler {
    @Autowired
    private QuoteService quoteService;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        List<ActivitiTaskUnDoEntity> taskUnDoEntities =  quoteService.selectTimeoutTaskForNotice();
        for(ActivitiTaskUnDoEntity taskUnDoEntity : taskUnDoEntities){
            String  x = taskUnDoEntity.getBusinessKey();
            Long authorizationApplyId =Long.parseLong(x.split("_")[1]);

            quoteService.sendWeixinNoticeForActiviti(authorizationApplyId,x,taskUnDoEntity.getTaskId(),taskUnDoEntity.getUserName());

            XxlJobLogger.log("处理中"+taskUnDoEntity.getBusinessKey());
        }
        return ReturnT.SUCCESS;
    }

}
