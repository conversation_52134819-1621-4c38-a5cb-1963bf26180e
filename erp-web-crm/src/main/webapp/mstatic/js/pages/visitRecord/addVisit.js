// 添加拜访记录
void function () {
    new Vue({
        el: '#page-container',
        data: {
            canAjax: true,
            pageLoading: true, // 页面加载状态

            /* Card1 */
            // 客户名称
            traderId: '',
            traderName: '',
            visitCustomerVo: {},
            // 联系方式
            visitFormMobile: '', // 手机
            visitFormTelephone: '', // 固话
            // 其他联系方式
            otherContact: [],
            visitFormOtherContact: '', // 接口入参
            // 联系人
            visitFormContact: '',
            isFromPhone: false, // 是否通过手机号选择的
            // 职位
            visitFormDepart: '',
            positionList: [],
            // 未获得联系人信息
            noContract: false,

            /* Card2 */
            visitFormPPT: '', // 讲解PPT
            visitFormConnect: '', // 沟通记录

            // 提交
            cansubmit: true,
        },
        async created() {
            this.visitRecordId = document.getElementById('visitRecordId').value || ''; // 拜访计划id
            this.initData();
        },
        mounted () {
        },
        methods: {
            // 默认带入客户名称
            initData () {
                this.$axios.get(`/crm/visitrecord/m/detail?id=${this.visitRecordId}`).then(({data}) => {
                    if (data.success) {
                        let detail = data.data;
                        console.log('detail', detail);
                        this.traderId = detail.traderId || '';
                        this.traderName = detail.customerName || '';
                        this.visitCustomerVo = detail.visitCustomerVo || {};
                        this.traderTypeChange(detail.customerNature || '');

                        this.noContract = detail.noContract === 'Y' ? true : false; 

                        if (!this.noContract) {
                            this.visitFormContact = detail.contactName || '';
                            this.visitFormMobile = detail.contactMobile || '';
                            this.visitFormTelephone = detail.contactTele || '';
                            if (detail.otherContact) {
                                this.visitFormOtherContact = detail.otherContact;
                                this.initOtherContact(detail.otherContact);
                            }
                            this.visitFormDepart = detail.contactPosition || '';
                        }

                    }

                    this.pageLoading = false;
                })
            },

            // 初始化其他联系方式
            initOtherContact (otherStr) {
                let items = otherStr.split('##');
                let arr = [];
                items.forEach((item)=> {
                    let key = item.split(':')[0];
                    let val = item.split(':')[1];
                    if (key && val) {
                        arr.push({
                            label: key,
                            value: val,
                            id: Math.random()
                        })
                    }
                })
                this.otherContact = arr;
            },

            traderTypeChange (type) {
                this.positionList = [];
                this.contactPosition = "";

                setTimeout(() => {
                    let list = [];
    
                    let labels = {
                        465: ['老板', '销售负责人', '销售经理', '采购负责人', '采购经理', '商务人员', '财务人员', '物流人员', '售后人员', '临床工程师', '其他'],
                        466: ['院长', '设备科人员', '临床医生', '采购负责人', '采购经理', '运营人员', '财务人员', '医院库管', '其他']
                    };

                    labels[type].forEach(item => {
                        list.push({
                            label: item,
                            value: item
                        })
                    })

                    this.positionList = list;
                }, 100);
            },

            /* 手机 */
            handlerPhone (val) {
                console.log('Handler Phone', val);
                if (this.traderId) {
                    if (this.isFromPhone) { // 当前联系人是选择的
                        this.visitFormContact = val.traderContactName || '';
                        this.traderContactId = val.traderContactId || '';
                    } else if (val.choosed) {
                        this.isFromPhone = true;
                        this.visitFormContact = val.traderContactName || '';
                        this.traderContactId = val.traderContactId || '';
                    }
                }
            },

            /* 其他联系方式 */
            addContact () {
                if (this.otherContact.length < 5) {
                    this.$refs.otherContactDialog.show();
                }
            },
            handlerAddContact (val) {
                console.log('handlerAddContact:::::', val);
                this.otherContact.push({
                    label: val,
                    value: '',
                    id: Math.random()
                })
            },
            handlerAddOnInput () {
                let arr = [];
                this.otherContact.forEach(item => {
                    if (item.value.trim()) {
                        arr.push(`${item.label}:${item.value.trim()}`);
                    }
                });
                let str = arr.join('##');
                this.visitFormOtherContact = str;
            },
            handlerAddDelete (index) {
                this.otherContact.splice(index, 1);
                this.handlerAddOnInput();
            },

            /* 联系人 */
            handlerContact() {
                this.isFromPhone = false;
                this.traderContactId = '';
            },
            // PPT
            visitFormPPTChange () {
                console.log('Handler Visit', this.visitType);
            },
            


            errorMsg (txt) {
                this.$message({ type: 'error', message: txt });
            },
            // 验证表单
            formBindValid () {
                if (!this.noContract && !this.visitFormMobile.trim() && !this.visitFormTelephone.trim() && !this.visitFormOtherContact.trim()) {
                    this.errorMsg('手机、固话和其他联系方式至少填写一项');
                    return false;
                }
                else if (!this.noContract && this.visitFormMobile && this.visitFormMobile.length !== 11) {
                    this.errorMsg('请输入11位手机号码');
                    return false;
                }
                else if (!this.noContract && !this.visitFormContact) {
                    this.errorMsg('请输入联系人');
                    return false;
                }
                else if (!this.noContract && !this.visitFormDepart) {
                    this.errorMsg('请选择联系人职位');
                    return false;
                }
                else if (!this.visitFormConnect) {
                    this.errorMsg('请填写沟通记录');
                    return false;
                }
                return true;
            },

            async submit () {
                console.log(' -- check -- ', this.cansubmit, this.formBindValid());
                if (!this.cansubmit || !this.formBindValid()) {
                    return;
                }

                let _this = this;
                this.$dialog({
                    type: 'warn',
                    message: '提交后将完成拜访，拜访计划和记录将不可修改，确认继续提交？',
                    buttons: [ {
                        txt: '取消'
                    }, {
                        txt: '提交',
                        btnClass: 'confirm',
                        callback: function () {

                            let reqData = {
                                id: _this.visitRecordId,
                                showPpt: _this.visitFormPPT,
                                customerRequires: _this.visitFormConnect,
                                noContract: 'Y'
                            };
            
                            if (!_this.noContract) {
                                reqData.recordContactMobile = _this.visitFormMobile;
                                reqData.recordContactName = _this.visitFormContact;
                                reqData.recordContactTele = _this.visitFormTelephone;
                                reqData.recordContactPosition = _this.visitFormDepart;
                                reqData.recordOtherContact = _this.visitFormOtherContact;
                                reqData.noContract = 'N';
                            }
            
                            _this.cansubmit = false;
                            _this.$axios.post('/crm/visitrecord/m/update', reqData).then(({data}) => {
                                if (data.success) {
                                    window.location.replace(`/crm/visitRecord/m/detail?id=${_this.visitRecordId}`);
                                } else {
                                    _this.cansubmit = true;
                                    _this.$message({
                                        message: data.message
                                    })
                                }
                            })

                        }
                    }]
                })
            }
        }
    })
}.call(this);