package com.newtask.data.saleorderdata;

import com.newtask.data.AbstractDataSync;
import com.newtask.data.dao.SaleorderDataMapper;
import com.newtask.data.dto.SaleoderDataDto;
import com.vedeng.erp.saleorder.enums.SubStatusEnum;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/11 11:16
 * @describe
 */
@Component
public class SaleorderSubStatusSync extends AbstractDataSync {

    @Resource
    SaleorderDataMapper saleorderDataMapper;


    @Override
    public List<Integer> loadBizId(Long startTime, Long endTime) {
        List<Integer> saleorderIdsList = saleorderDataMapper.getSaleorderSubStatusIdsList(startTime, endTime);

        return saleorderIdsList;
    }

    @Override
    public List<Map<String, Object>> loadBizData(List<Integer> bizIds) {
        List<Map<String, Object>> subStatus = new ArrayList<>();

        List<Saleorder> saleorderList = null;
        if (CollectionUtils.isNotEmpty(bizIds)) {
            saleorderList = saleorderDataMapper.findSubStatusBySaleorderId(bizIds);
            if (!saleorderList.isEmpty()) {
                Map<Integer, Saleorder> saleorderMap = saleorderList.stream().collect(Collectors.toMap(Saleorder::getSaleorderId, Saleorder -> Saleorder));
                saleorderMap.forEach((key, value) -> {
                    Map<String, Object> map = new HashMap<>();
                    SubStatusEnum tem=calSubStatusById(value);
                    map.put("subStatus", tem.getCode());
                    map.put("saleorderId",key);
                    subStatus.add(map);
                });
            }

        }

        return subStatus;
    }


    @Override
    public void updateData(List<Map<String, Object>> dataList) {
        if (CollectionUtils.isNotEmpty(dataList)) {
            long timeMillis = System.currentTimeMillis();
            List<SaleoderDataDto> saleoderDataDtos = new ArrayList<>();
            dataList.forEach(item -> {
                Integer saleorderId = NumberUtils.toInt(item.get("saleorderId").toString());

                SaleoderDataDto saleoderDataDto = new SaleoderDataDto();
                saleoderDataDto.setSaleorderId(saleorderId);
                saleoderDataDto.setSubStatus(NumberUtils.toLong(item.get("subStatus")+""));
                saleoderDataDtos.add(saleoderDataDto);
                // 判断saleorderId是否存在
                makeExist(saleorderId, timeMillis);
            });
            // 根据id修改OrderStreamStatus
            saleorderDataMapper.updateSaleorderSubStatus(saleoderDataDtos, timeMillis);
        }
    }

    @Override
    public void makeExist(Integer id, Long nowTime) {
        Long count = saleorderDataMapper.getSaleorderById(id);
        if (count <= 0) {
            saleorderDataMapper.insertSaleOrder(id, nowTime);
        }
    }

    // status: 0待确认（默认）、1进行中、2已完结、3已关闭
    // PAYMENT_STATUS:  0未付款 1部分付款 2全部付款
    // DELIVERY_STATUS: 0未发货 1部分发货 2全部发货
    // ARRIVAL_STATUS: 0未收货 1部分收货 2全部收货
    // INVOICE_STATUS: 0未开票 1部分开票 2全部开票
    // VERIFY_STATUS: 3待提交、0审核中、1审核通过、2审核不通过
    /**
     * 0待确认：新增、报价转订单且未提交审核的订单；
     * 1待审核：提交审核的订单；子节点：审核中、审核通过、审核不通过
     * 2待收款：审核通过已生效的订单；子节点：未收款、部分收款、全部收款
     * 3待发货：全部收款的订单；子节点：未发货、部分发货、全部发货
     * 4待收货：全部发货的订单，即有物流信息但快递状态“未收货”“部分收货”的订单；子节点：未收货、部分收货、全部收货
     * 5待开票：全部收货或提前开票（通过）的订单；子节点：未开票、部分开票、全部开票
     * 6已完结：票：全部开票（*寄送）+货：全部收货+款：全部收款（未还账期=0），订单完结
     * 7已关闭：已关闭订单
     * 8待客户确认：jcf订单待客户确认
     */
    public SubStatusEnum calSubStatusById(Saleorder saleorder){
//        Saleorder saleorder= saleorderMapper.getSaleOrderBaseInfo(saleOrderId);
        SubStatusEnum resultSubStatus=SubStatusEnum.TO_BE_CONFIRMED;
        if ("0".equals(saleorder.getStatus()+"")  && "3".equals(saleorder.getVerifyStatus()+"")) {
            resultSubStatus=SubStatusEnum.TO_BE_CONFIRMED;
        }
        if ("0".equals(saleorder.getVerifyStatus()+"")) {
            resultSubStatus=SubStatusEnum.TO_AUDIT;
        }
        if ("1".equals(saleorder.getVerifyStatus()+"") && "1".equals(saleorder.getValidStatus()+"")) {
            resultSubStatus=SubStatusEnum.TO_COLLECTION;
        }
        if ("1".equals(saleorder.getStatus()+"") && "2".equals(saleorder.getPaymentStatus()+"")) {
            resultSubStatus=SubStatusEnum.TO_SEND_GOODS;
        }
        if ("1".equals(saleorder.getStatus()+"") && "2".equals(saleorder.getDeliveryStatus()+"") && !"2".equals(saleorder.getArrivalStatus()+"")) {
            resultSubStatus=SubStatusEnum.TO_THE_GOODS;
        }
        if ("1".equals(saleorder.getStatus()+"") && "2".equals(saleorder.getArrivalStatus()+"")){
            resultSubStatus=SubStatusEnum.TO_MAKE_OUT_INVOICE;
        }
        if ("2".equals(saleorder.getStatus()+"")){
            resultSubStatus=SubStatusEnum.FINISHED;
        }
        if ("4".equals(saleorder.getStatus()+"")) {
            resultSubStatus=SubStatusEnum.WAIT_CONFIRM;
        }
        if ("3".equals(saleorder.getStatus()+"")) {
            resultSubStatus=SubStatusEnum.CLOSE;
        }
        return resultSubStatus;
    }
}
