package com.vedeng.erp.aftersale.facade;

import com.vedeng.aftersales.component.dto.AfterSaleOrderAddDto;

import javax.servlet.http.HttpServletRequest;

public interface AfterSalesCreateOnlineFacade {

    /**
     * 创建售后订单
     * @param afterSaleOrderAddDto
     * @throws Exception
     */
    void createAfterSalesOrder(AfterSaleOrderAddDto afterSaleOrderAddDto) throws Exception;
    /**
     * 售后自动审核
     * @param request  null时取njadmin
     * @throws Exception
     */
    void startAuditProcess(HttpServletRequest request,Boolean isAuto) throws Exception;


}
