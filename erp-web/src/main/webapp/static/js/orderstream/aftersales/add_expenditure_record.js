$(function() {
    $("#submit").click(function(){
        debugger;
        checkLogin();
        $(".warning").remove();
        $("input").removeClass("errorbor");

        if ($("#payer").val().length > 100){
            warnTips("payerError"," 支付方最多输入200字符，请检查后提交");
            return false;
        }

        var remark = $("#remark").val();
        if(remark.length > 200){
            warnTips("remarkError"," 备注最多输入200字符，请检查后提交");
            return false;
        }

        var amount = $("#amount").val();
        if (amount.length > 14){
            warnTips("amountError","金额最多输入14位字符，请检查后提交");
            return false;
        }
        var regPos = /^\d+(\.\d+)?$/; //非负浮点数
        var regNeg = /^(-(([0-9]+\.[0-9]*[1-9][0-9]*)|([0-9]*[1-9][0-9]*\.[0-9]+)|([0-9]*[1-9][0-9]*)))$/; //负浮点数

        if(!regPos.test(amount) && !regNeg.test(amount)) {
            warnTips("amountError"," 请输入数字字符，保留小数点后两位");
            return false;
        }

        /*根据数据库,限制14, 12整数,2小数*/
        var reg = /^[1-9]\d{0,11}(\.\d{1,2})?$|^0(\.\d{1,2})?$/;
        if (!reg.test(amount)){
            warnTips("amountError"," 请输入数字字符，保留小数点后两位,最多12位整数");
            return false;
        }
        /*var reg = /^[0-9]+.?[0-9]*$/;
        if(!reg.test(amount)){
            warnTips("amountError"," 请输入输入数字字符，保留小数点后两位");
            return false;
        }*/

        $.ajax({
            url:page_url+'/order/afterSalesCommon/saveExpenditureRecord.do',
            data:$('#addExpenditureRecord').serialize(),
            type:"POST",
            dataType : "json",
            async: false,
            success:function(data){
                if (null!=data && data.code != 0) {
                    layer.alert(data.message);
                }else {
                    $('#cancle').click();
                    parent.location.reload();
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
        return false;
    });
});

/**
 * 控制只能输入小数点后2位
 * @param obj
 */
function clearNoNum(obj) {
    obj.value = obj.value.replace(/[^\d.]/g, "");
    obj.value = obj.value.replace(/\.{2,}/g, ".");
    obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
    if (obj.value.indexOf(".") < 0 && obj.value != "") {
        obj.value = parseFloat(obj.value);
    }
}
