package com.vedeng.order.model.dto;

import com.vedeng.authorization.model.User;
import lombok.Data;

import java.util.List;

@Data
public class SaleorderUserInfoDto {

    private Integer positionLevel;

    private String positionName;

    private Integer positionType;

    private String userName;

    private Integer userId;

    /**
     * 职位等级 1：员工；2：主管，3：经理，4：总监
     */
    private Integer levelType;


    private List<Integer> subUserIdList;

    /**
     * 是否是B2B 0是，1否
     */
    private Integer isB2B;

    public static Integer STAFF_LEVEL = 1;
    public static String STAFF_NAME = "销售工程师";
    public static Integer SUPERVISOR_LEVEL = 2;
    public static String SUPERVISOR_NAME = "销售主管";
    public static Integer MANAGE_LEVEL = 3;
    public static String MANAGE_NAME = "销售经理";
    public static Integer CHIEF_LEVEL = 4;
    public static String CHIEF_NAME = "销售总监";

    public static Integer B2B_ORGID = 38;
    public static Integer AED_ORGID = 199;

}
