package com.vedeng.erp.saleorder.task;

import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.system.service.VerifiesRecordService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.TaskService;
import org.activiti.engine.task.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description VDERP-7934 已关闭的备货单 执行为审核不通过
 * @Date 2022/1/10 11:03
 */
@Component
@JobHandler(value = "BHSaleOrderVerifyStatusSyncTask")
public class BHSaleOrderVerifyStatusSyncTask extends AbstractJobHandler {

    @Autowired
    private ActionProcdefService actionProcdefService;

    @Autowired
    private VerifiesRecordService verifiesRecordService;

    @Autowired // 自动装载
    protected ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

    @Resource
    private SaleorderMapper saleorderMapper;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {

        TaskService taskService = processEngine.getTaskService();
        String comment = "已关闭备货单统一驳回";
        Map<String, Object> variables = new HashMap<>();
        variables.put("pass", false);

        // 获取所有已关闭 审核中的备货单
        List<Integer> closeAndVerifyBh = saleorderMapper.getCloseAndVerifyBh();
        for (Integer saleOrderId : closeAndVerifyBh) {
            // 获取备货单的当前审核信息
            Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
                    "bhSaleorderVerify_" + saleOrderId);

            Task taskInfo = (Task) historicInfo.get("taskInfo");
            String taskId = taskInfo.getId();
            String tableName = (String) taskService.getVariable(taskId, "tableName");
            String id = (String) taskService.getVariable(taskId, "id");
            Integer idValue = (Integer) taskService.getVariable(taskId, "idValue");
            String key = (String) taskService.getVariable(taskId, "key");
            if (tableName != null && id != null && idValue != null && key != null) {
                actionProcdefService.updateInfo(tableName, id, idValue, key, 3, 2);
            }
            verifiesRecordService.saveVerifiesInfo(taskId, 2);
            actionProcdefService.complementTask(null, taskId, comment, "njadmin", variables);
        }
        XxlJobLogger.log("本次更新的BH单ID为：" + closeAndVerifyBh);
        return SUCCESS;
    }
}
