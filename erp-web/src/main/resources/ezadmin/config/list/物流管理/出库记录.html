<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>出库记录</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="THjcDN81Dpw" datasource="erp-reportdatasource" fixednumber="0" fixednumberright="0" append_column_url="" append_row_url="" empty_show="/" firstcol="">
<div id="appendHead"></div>
<div class="layui-fluid">
    <div class="layui-tab">
        <ul class="layui-tab-title" id="tab">
        </ul>
    </div>
    <form class="layui-form" id="search">
		<div class=" layui-inline ">
			<label class="layui-form-label">关键字</label>
			<div class="layui-input-inline"><object class=" layui-input list-search-item " type="union" name="SA.SALEORDER_NO,ASS.AFTER_SALES_NO,L.LEND_OUT_NO,OO.ORDER_NO,IA.INVENTORY_ADJUSTMENT_NO,WUCO.WMS_UNIT_CONVERSION_ORDER_NO" placeholder style="" alias="" jdbctype="VARCHAR" data="" datatype="" oper="" validate_rules="" validate_messages=""></object>
			</div>
		</div>
        <div class=" layui-inline "><label class="layui-form-label">产品名称</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="19" name="SKU_NAME" placeholder="" style="" alias="AB" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" ></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">品牌</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="BRAND_NAME" placeholder="" style="" alias="B" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" ></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">型号</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="19" name="MODEL" placeholder="" style="" alias="AB" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" ></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">订货号</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="SKU_NO" placeholder="" style="" alias="AB" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" ></object>
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">收货方</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="TAKE_TRADER_NAME" placeholder="" style="" alias="SA" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" ></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">出库种类</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="OPERATE_TYPE" placeholder="" style="" alias="A" jdbctype="NUMBER" data="[{&quot;V&quot;:&quot;销售出库&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;销售换货出库&quot;,&quot;K&quot;:&quot;4&quot;},{&quot;V&quot;:&quot;采购退货出库&quot;,&quot;K&quot;:&quot;6&quot;},{&quot;V&quot;:&quot;采购换货出库&quot;,&quot;K&quot;:&quot;7&quot;},{&quot;V&quot;:&quot;外借出库&quot;,&quot;K&quot;:&quot;10&quot;},{&quot;V&quot;:&quot;报废出库&quot;,&quot;K&quot;:&quot;13&quot;},{&quot;V&quot;:&quot;领用出库&quot;,&quot;K&quot;:&quot;14&quot;},{&quot;V&quot;:&quot;盘亏出库&quot;,&quot;K&quot;:&quot;15&quot;},{&quot;V&quot;:&quot;单位转换出库&quot;,&quot;K&quot;:&quot;19&quot;},{&quot;V&quot;:&quot;样品出库&quot;,&quot;K&quot;:&quot;18&quot;}]" datatype="JSON" oper="EQ" ></select>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">出库时间</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="daterange" name="ADD_TIME" placeholder="" style="" alias="A" jdbctype="DATE_TO_NUMBER" data="" datatype="" oper="BETWEEN" ></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">销售单号</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="30" name="SALEORDER_NO" placeholder="" style="" alias="SA" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" ></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">售后单号</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="30" name="AFTER_SALES_NO" placeholder="" style="" alias="ASS" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" ></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">外借单号</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="30" name="LEND_OUT_NO" placeholder="" style="" alias="L" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" ></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">wms出库单号</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="30" name="ORDER_NO" placeholder="" style="" alias="OO" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" ></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">库存调整单单号</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="30" name="INVENTORY_ADJUSTMENT_NO" placeholder="" style="" alias="IA" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" ></object>
            </div>
        </div>
		<div class=" layui-inline "><label class="layui-form-label">单位转换单号</label>
			<div class="layui-input-inline">
				<object class=" layui-input list-search-item " type="30" name="WMS_UNIT_CONVERSION_ORDER_NO" placeholder="" style="" alias="WUCO" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" ></object>
			</div>
		</div>
    </form>
    <hr class="layui-border-blue">
    <div class="btn-group   bd-highlight" id="tableButton">
    </div>
    <table id="table" class="layui-table" style=" width:100%">
        <thead>
        <tr id="column">
            <th type="rowbutton" id="rowbutton"></th>
            <th item_name="EX_WAREHOUSE_TIME" name="EX_WAREHOUSE_TIME" body="td-text" order="0" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">出库时间</th>
            <th item_name="ORDER_NO" name="ORDER_NO" body="td-link" order="0" url="/logistics/warehousein/viewWarehouseDetail.do?operateType=${OPERATE_TYPE}&amp;orderId=${ORDER_ID}&amp;orderNo=${ORDER_NO}" opentype="PARENT" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">出库单据</th>
            <th item_name="SKU_NO" name="SKU_NO" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">订货号</th>
            <th item_name="SKU_NAME" name="SKU_NAME" body="td-link" order="0" url="/goods/goods/viewbaseinfo.do?goodsId=${SKU_ID}" opentype="PARENT" windowname="${SKU_NAME}" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">产品名称</th>
            <th item_name="MATERIAL_CODE" name="MATERIAL_CODE" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">物料编码</th>
            <th item_name="BRAND_NAME" name="BRAND_NAME" body="td-text" order="0" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">品牌</th>
            <th item_name="MODEL" name="MODEL" body="td-text" order="0" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">型号</th>
            <th item_name="NUM" name="NUM" body="td-text" order="0" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">数量</th>
            <th item_name="UNIT_NAME" name="UNIT_NAME" body="td-text" order="0" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">单位</th>
            <th item_name="GOODS_PRICE" name="GOODS_PRICE" body="td-text" order="0" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">单价</th>
            <th item_name="TOTAL_PRICE" name="TOTAL_PRICE" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">总价</th>
            <th item_name="SALE_TRADER_NAME" name="SALE_TRADER_NAME" body="td-text" order="0" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">收货方</th>
            <th item_name="OPERATE_TYPE" name="OPERATE_TYPE" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;采购入库 &quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;销售出库&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;销售换货入库&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;销售换货出库&quot;,&quot;K&quot;:&quot;4&quot;},{&quot;V&quot;:&quot;销售退货入库&quot;,&quot;K&quot;:&quot;5&quot;},{&quot;V&quot;:&quot;采购退货出库&quot;,&quot;K&quot;:&quot;6&quot;},{&quot;V&quot;:&quot;采购换货出库&quot;,&quot;K&quot;:&quot;7&quot;},{&quot;V&quot;:&quot;采购换货入库&quot;,&quot;K&quot;:&quot;8&quot;},{&quot;V&quot;:&quot;外借入库&quot;,&quot;K&quot;:&quot;9&quot;},{&quot;V&quot;:&quot;外借出库&quot;,&quot;K&quot;:&quot;10&quot;},{&quot;V&quot;:&quot;调整盘盈入库&quot;,&quot;K&quot;:&quot;11&quot;},{&quot;V&quot;:&quot;盘盈入库&quot;,&quot;K&quot;:&quot;12&quot;},{&quot;V&quot;:&quot;报废出库&quot;,&quot;K&quot;:&quot;13&quot;},{&quot;V&quot;:&quot;领用出库&quot;,&quot;K&quot;:&quot;14&quot;},{&quot;V&quot;:&quot;盘亏出库&quot;,&quot;K&quot;:&quot;15&quot;},{&quot;V&quot;:&quot;单位转换出库&quot;,&quot;K&quot;:&quot;19&quot;},{&quot;V&quot;:&quot;样品出库&quot;,&quot;K&quot;:&quot;18&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">出库种类</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td> express:</td>
            <td colspan="100"><pre id="express" class="layui-code" orderby="ORDER BY EX_WAREHOUSE_TIME DESC" groupby="">StringBuilder sql1=new StringBuilder("SELECT
	A.WAREHOUSE_GOODS_OPERATE_LOG_ID AS LOG_ID,
	FROM_UNIXTIME( A.ADD_TIME / 1000, '%Y-%m-%d %H:%i:%S' ) AS EX_WAREHOUSE_TIME,
	(
	CASE
			A.OPERATE_TYPE
			WHEN 2 THEN
			SA.SALEORDER_NO
			WHEN 4 THEN
			ASS.AFTER_SALES_NO
			WHEN 6 THEN
			ASS.AFTER_SALES_NO
			WHEN 7 THEN
			ASS.AFTER_SALES_NO
			WHEN 10 THEN
		IF
			( L.LEND_OUT_ID IS NOT NULL, L.LEND_OUT_NO, OO.order_no )
			WHEN 13 THEN
			OO.order_no
			WHEN 14 THEN
			OO.order_no
			WHEN 15 THEN
			IA.INVENTORY_ADJUSTMENT_NO
			WHEN 19 THEN
			WUCO.WMS_UNIT_CONVERSION_ORDER_NO
			WHEN 18 THEN
			OO.order_no
			ELSE ''
		END
		) AS ORDER_NO,
		(
		CASE
				A.OPERATE_TYPE
				WHEN 2 THEN
				SA.SALEORDER_ID
				WHEN 4 THEN
				ASS.AFTER_SALES_ID
				WHEN 6 THEN
				ASS.AFTER_SALES_ID
				WHEN 7 THEN
				ASS.AFTER_SALES_ID
				WHEN 10 THEN
			IF
				( L.LEND_OUT_ID IS NOT NULL, L.LEND_OUT_ID, OO.id )
				WHEN 13 THEN
				OO.id
				WHEN 14 THEN
				OO.id
				WHEN 15 THEN
				IA.INVENTORY_ADJUSTMENT_ID
				WHEN 19 THEN
				WUCO.WMS_UNIT_CONVERSION_ORDER_ID
				WHEN 18 THEN
				OO.id
				ELSE ''
			END
			) AS ORDER_ID,
			A.OPERATE_TYPE,
			AB.SKU_NO,
			AB.SKU_NAME,
			AB.MATERIAL_CODE,
			AB.SKU_ID,
			B.BRAND_NAME,
			AB.MODEL,
			ABS( A.NUM ) NUM,
			U.UNIT_NAME,
			(
			CASE
					A.OPERATE_TYPE
					WHEN 2 THEN
					SG.PRICE
					WHEN 4 THEN
					ASG.PRICE
					WHEN 6 THEN
					ASG.PRICE
					WHEN 7 THEN
					ASG.PRICE ELSE 0
				END
				) AS GOODS_PRICE,
				(
				CASE
						A.OPERATE_TYPE
						WHEN 2 THEN
						SG.PRICE * ABS( A.NUM )
						WHEN 4 THEN
						ASG.PRICE * ABS( A.NUM )
						WHEN 6 THEN
						ASG.PRICE * ABS( A.NUM )
						WHEN 7 THEN
						ASG.PRICE * ABS( A.NUM ) ELSE 0
					END
					) AS TOTAL_PRICE,
					( CASE A.OPERATE_TYPE WHEN 2 THEN SA.TAKE_TRADER_NAME END ) AS SALE_TRADER_NAME
				FROM
					T_WAREHOUSE_GOODS_OPERATE_LOG A
					LEFT JOIN V_CORE_SKU AB ON AB.SKU_ID = A.GOODS_ID
					LEFT JOIN V_CORE_SPU BB ON AB.SPU_ID = BB.SPU_ID
					LEFT JOIN T_BRAND B ON B.BRAND_ID = BB.BRAND_ID
					LEFT JOIN T_UNIT U ON U.UNIT_ID = AB.BASE_UNIT_ID
					LEFT JOIN T_SALEORDER_GOODS SG ON SG.SALEORDER_GOODS_ID = A.RELATED_ID
					AND A.OPERATE_TYPE = 2
					LEFT JOIN T_SALEORDER SA ON SG.SALEORDER_ID = SA.SALEORDER_ID
					LEFT JOIN T_AFTER_SALES_GOODS ASG ON ASG.AFTER_SALES_GOODS_ID = A.RELATED_ID
					AND A.OPERATE_TYPE IN ( 4, 6, 7 )
					LEFT JOIN T_AFTER_SALES ASS ON ASG.AFTER_SALES_ID = ASS.AFTER_SALES_ID
					LEFT JOIN T_AFTER_SALES_DETAIL ASD ON ASG.AFTER_SALES_ID = ASD.AFTER_SALES_ID
					LEFT JOIN T_WMS_OUTPUT_ORDER_GOODS OOG ON OOG.id = A.RELATED_ID
					AND A.OPERATE_TYPE IN ( 10, 13, 14 ,18)
					AND OOG.SKU_NO = AB.SKU_NO
					LEFT JOIN T_WMS_OUTPUT_ORDER OO ON OO.id = OOG.wms_output_order_id
					LEFT JOIN T_LEND_OUT L ON A.RELATED_ID = L.LEND_OUT_ID
					AND A.OPERATE_TYPE = 10
					AND A.GOODS_ID = L.GOODS_ID
					LEFT JOIN T_WMS_INVENTORY_ADJUSTMENT_DETAIL IAD ON IAD.INVENTORY_ADJUSTMENT_DETAIL_ID = A.RELATED_ID
					AND A.OPERATE_TYPE = 15
					LEFT JOIN T_WMS_INVENTORY_ADJUSTMENT IA ON IA.INVENTORY_ADJUSTMENT_ID = IAD.INVENTORY_ADJUSTMENT_ID
					LEFT JOIN T_WMS_UNIT_CONVERSION_ORDER_ITEM WUCOI ON WUCOI.WMS_UNIT_CONVERSION_ORDER_ITEM_ID = A.RELATED_ID
					AND A.OPERATE_TYPE = 19
					LEFT JOIN T_WMS_UNIT_CONVERSION_ORDER WUCO ON WUCO.WMS_UNIT_CONVERSION_ORDER_ID = WUCOI.WMS_UNIT_CONVERSION_ORDER_ID
					LEFT JOIN T_BARCODE BR ON BR.BARCODE_ID = A.BARCODE_ID
				WHERE
					A.LOG_TYPE = 1
					AND A.IS_ENABLE = 1
					AND A.NUM &lt; 0 ");

String group1="GROUP BY A.WAREHOUSE_GOODS_OPERATE_LOG_ID";

StringBuilder sql2=new StringBuilder("SELECT
					A.WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL_ID AS LOG_ID,
					FROM_UNIXTIME( A.ADD_TIME / 1000, '%Y-%m-%d %H:%i:%S' ) AS EX_WAREHOUSE_TIME,
					(
					CASE
							A.OPERATE_TYPE
							WHEN 2 THEN
							SA.SALEORDER_NO
							WHEN 4 THEN
							ASS.AFTER_SALES_NO
							WHEN 6 THEN
							ASS.AFTER_SALES_NO
							WHEN 7 THEN
							ASS.AFTER_SALES_NO
							WHEN 10 THEN
						IF
							( L.LEND_OUT_ID IS NOT NULL, L.LEND_OUT_NO, OO.order_no )
							WHEN 13 THEN
							OO.order_no
							WHEN 14 THEN
							OO.order_no
							WHEN 15 THEN
							IA.INVENTORY_ADJUSTMENT_NO
							WHEN 19 THEN
							WUCO.WMS_UNIT_CONVERSION_ORDER_NO
							ELSE ''
						END
						) AS ORDER_NO,
						(
						CASE
								A.OPERATE_TYPE
								WHEN 2 THEN
								SA.SALEORDER_ID
								WHEN 4 THEN
								ASS.AFTER_SALES_ID
								WHEN 6 THEN
								ASS.AFTER_SALES_ID
								WHEN 7 THEN
								ASS.AFTER_SALES_ID
								WHEN 10 THEN
							IF
								( L.LEND_OUT_ID IS NOT NULL, L.LEND_OUT_ID, OO.id )
								WHEN 13 THEN
								OO.id
								WHEN 14 THEN
								OO.id
								WHEN 15 THEN
								IA.INVENTORY_ADJUSTMENT_ID
								WHEN 19 THEN
								WUCO.WMS_UNIT_CONVERSION_ORDER_ID
							ELSE ''
							END
							) AS ORDER_ID,
							A.OPERATE_TYPE,
							AB.SKU_NO,
							AB.SKU_NAME,
							AB.MATERIAL_CODE,
							AB.SKU_ID,
							B.BRAND_NAME,
							AB.MODEL,
							ABS( A.NUM ) NUM,
							U.UNIT_NAME,
							(
							CASE
									A.OPERATE_TYPE
									WHEN 2 THEN
									SG.PRICE
									WHEN 4 THEN
									ASG.PRICE
									WHEN 6 THEN
									ASG.PRICE
									WHEN 7 THEN
									ASG.PRICE ELSE 0
								END
								) AS GOODS_PRICE,
								(
								CASE
										A.OPERATE_TYPE
										WHEN 2 THEN
										SG.PRICE * ABS( A.NUM )
										WHEN 4 THEN
										ASG.PRICE * ABS( A.NUM )
										WHEN 6 THEN
										ASG.PRICE * ABS( A.NUM )
										WHEN 7 THEN
										ASG.PRICE * ABS( A.NUM ) ELSE 0
									END
									) AS TOTAL_PRICE,
									( CASE A.OPERATE_TYPE WHEN 2 THEN SA.TAKE_TRADER_NAME END ) AS SALE_TRADER_NAME
								FROM
									T_WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL A
									LEFT JOIN V_CORE_SKU AB ON AB.SKU_ID = A.GOODS_ID
									LEFT JOIN V_CORE_SPU BB ON AB.SPU_ID = BB.SPU_ID
									LEFT JOIN T_BRAND B ON B.BRAND_ID = BB.BRAND_ID
									LEFT JOIN T_UNIT U ON U.UNIT_ID = AB.BASE_UNIT_ID
									LEFT JOIN T_SALEORDER_GOODS SG ON SG.SALEORDER_GOODS_ID = A.RELATED_ID
									AND A.OPERATE_TYPE = 2
									LEFT JOIN T_SALEORDER SA ON SG.SALEORDER_ID = SA.SALEORDER_ID
									LEFT JOIN T_AFTER_SALES_GOODS ASG ON ASG.AFTER_SALES_GOODS_ID = A.RELATED_ID
									AND A.OPERATE_TYPE IN ( 4, 6, 7 )
									LEFT JOIN T_AFTER_SALES ASS ON ASG.AFTER_SALES_ID = ASS.AFTER_SALES_ID
									LEFT JOIN T_AFTER_SALES_DETAIL ASD ON ASG.AFTER_SALES_ID = ASD.AFTER_SALES_ID
									LEFT JOIN T_WMS_OUTPUT_ORDER_GOODS OOG ON OOG.id = A.RELATED_ID
									AND A.OPERATE_TYPE IN ( 10, 13, 14 )
									AND OOG.SKU_NO = AB.SKU_NO
									LEFT JOIN T_WMS_OUTPUT_ORDER OO ON OO.id = OOG.wms_output_order_id
									LEFT JOIN T_LEND_OUT L ON A.RELATED_ID = L.LEND_OUT_ID
									AND A.OPERATE_TYPE = 10
									AND A.GOODS_ID = L.GOODS_ID
									LEFT JOIN T_WMS_INVENTORY_ADJUSTMENT_DETAIL IAD ON IAD.INVENTORY_ADJUSTMENT_DETAIL_ID = A.RELATED_ID
									AND A.OPERATE_TYPE = 15
									LEFT JOIN T_WMS_INVENTORY_ADJUSTMENT IA ON IA.INVENTORY_ADJUSTMENT_ID = IAD.INVENTORY_ADJUSTMENT_ID
									LEFT JOIN T_WMS_UNIT_CONVERSION_ORDER_ITEM WUCOI ON WUCOI.WMS_UNIT_CONVERSION_ORDER_ITEM_ID = A.RELATED_ID
									AND A.OPERATE_TYPE = 19
									LEFT JOIN T_WMS_UNIT_CONVERSION_ORDER WUCO ON WUCO.WMS_UNIT_CONVERSION_ORDER_ID = WUCOI.WMS_UNIT_CONVERSION_ORDER_ID
									LEFT JOIN T_BARCODE BR ON BR.BARCODE_ID = A.BARCODE_ID
								WHERE
									A.LOG_TYPE = 1
									AND A.IS_ENABLE = 1
									");

String group2="GROUP BY A.WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL_ID";

result=unionall(sql1,group1,"",sql2,group2,"");
return result;</pre></td>
        </tr>
        <tr>
            <td> count:</td>
            <td colspan="100"><pre id="count" class="layui-code"></pre></td>
        </tr>
        </tbody>
    </table>
</div>
<div id="appendFoot">
    <script>
        function submitHandle(){
            if($("#ADD_TIME_START_ID").val()==""){
                layer.alert("出库开始时间不能为空")
                return false;
            }
            return true;
        }

    </script>
</div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    })
</script>
</body>
</html>