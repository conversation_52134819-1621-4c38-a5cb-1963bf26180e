package com.vedeng.system.controller;

import com.vedeng.authorization.model.User;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.system.service.UserService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/25 9:41
 * @describe 用户管理
 */
@Controller
@RequestMapping("/system/userposit")
public class UserPositController {

    @Autowired
    private UserService userService;



    /**
     * type 为 310 ，则查询所有销售部门，否则根据parentId查询树形结构的部门信息
     * @param request
     * @param parentId
     * @param level
     * @param type
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/static/getAllUser")
    public ResultInfo<User> getAllOriganization(HttpServletRequest request, Integer parentId, Integer level, Integer type) {
        User s_u = new User();
        s_u.setCompanyId(1);
        s_u.setIsDisabled(0);
        ResultInfo<User> resultInfo = new ResultInfo<User>();
        if (type != null && type == 310) {
            s_u.setPositType(310);
        }
        if (parentId != null) {
            s_u.setParentId(parentId);
        }
        List<User> userByPositTypes = userService.getUserInfo(s_u);

        fillUser(userByPositTypes, level);
        if (userByPositTypes != null) {
            resultInfo.setCode(0);
            resultInfo.setMessage("操作成功");
            resultInfo.setListData(userByPositTypes);
        }
        return resultInfo;
    }

    private void fillUser(List<User> userByPositTypes, Integer level) {
        if (level > 1 && CollectionUtils.isNotEmpty(userByPositTypes)) {
            for (User user : userByPositTypes) {
                fill(user);
                Integer a = level - 1;
                fillUser(user.getUsers(), a);
            }
        }
    }

    private void fill(User item) {
        List<User> userByPositTypes = userService.getUserByParentId(item.getUserId());
        if (CollectionUtils.isNotEmpty(userByPositTypes)) {
            item.setUsers(userByPositTypes);
        }
    }

}
