package com.wms.model.generate;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * V_WMS_IN_SNCODE_ORDER
 * <AUTHOR>

public class VWmsInSncodeOrder implements Serializable {
    /**
     * 入库SN码追溯表id
     */
    private Integer inSncodeOrderId;

    /**
     * 关联订单id
     */
    private Integer orderId;

    /**
     * 关联订单商品id
     */
    private Integer orderGoodsId;

    /**
     * 业务类型 0采购入库 1销售售后入库 2采购售后入库 3盘盈入库 4借货归还 5库存初始化入库单 6赠品入库
     */
    private Integer operateType;

    /**
     * WMS上架交易号
     */
    private String wmsOrderNo;

    /**
     * SKU
     */
    private String sku;

    /**
     * 商品id
     */
    private Integer skuId;

    /**
     * 厂家SN码
     */
    private String snCode;

    /**
     * 贝登追溯码
     */
    private String serialNo;

    /**
     * 入库时间
     */
    private Date inTime;

    /**
     * 生产日期
     */
    private Date productDate;

    /**
     * 有效期至
     */
    private Date expirationDate;

    /**
     * 厂家批号
     */
    private String batchNumber;

    /**
     * 贝登批次码
     */
    private String vedengBatchNumer;

    /**
     * 备注
     */
    private String comments;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 更新时间
     */
    private Date modeTime;

    /**
     * 是否删除  0否  1是
     */
    private Boolean isDelete;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 更新人
     */
    private Integer updater;

    private static final long serialVersionUID = 1L;

    public Integer getInSncodeOrderId() {
        return inSncodeOrderId;
    }

    public void setInSncodeOrderId(Integer inSncodeOrderId) {
        this.inSncodeOrderId = inSncodeOrderId;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Integer getOrderGoodsId() {
        return orderGoodsId;
    }

    public void setOrderGoodsId(Integer orderGoodsId) {
        this.orderGoodsId = orderGoodsId;
    }

    public Integer getOperateType() {
        return operateType;
    }

    public void setOperateType(Integer operateType) {
        this.operateType = operateType;
    }

    public String getWmsOrderNo() {
        return wmsOrderNo;
    }

    public void setWmsOrderNo(String wmsOrderNo) {
        this.wmsOrderNo = wmsOrderNo;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    public String getSnCode() {
        return snCode;
    }

    public void setSnCode(String snCode) {
        this.snCode = snCode;
    }

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    public Date getInTime() {
        return inTime;
    }

    public void setInTime(Date inTime) {
        this.inTime = inTime;
    }

    public Date getProductDate() {
        return productDate;
    }

    public void setProductDate(Date productDate) {
        this.productDate = productDate;
    }

    public Date getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(Date expirationDate) {
        this.expirationDate = expirationDate;
    }

    public String getBatchNumber() {
        return batchNumber;
    }

    public void setBatchNumber(String batchNumber) {
        this.batchNumber = batchNumber;
    }

    public String getVedengBatchNumer() {
        return vedengBatchNumer;
    }

    public void setVedengBatchNumer(String vedengBatchNumer) {
        this.vedengBatchNumer = vedengBatchNumer;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Date getModeTime() {
        return modeTime;
    }

    public void setModeTime(Date modeTime) {
        this.modeTime = modeTime;
    }

    public Boolean getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Boolean delete) {
        isDelete = delete;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Integer getUpdater() {
        return updater;
    }

    public void setUpdater(Integer updater) {
        this.updater = updater;
    }
}