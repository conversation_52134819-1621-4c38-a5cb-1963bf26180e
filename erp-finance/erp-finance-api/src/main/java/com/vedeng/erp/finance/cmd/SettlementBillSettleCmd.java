package com.vedeng.erp.finance.cmd;

import com.vedeng.common.core.enums.BusinessSourceTypeEnum;
import com.vedeng.common.core.enums.SettlementTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 财务结算单结算命令
 * @date 2023/11/16 10:41
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
public class SettlementBillSettleCmd {

    /**
     * 结算单id
     */
    private Integer settlementBillId;

    /**
     * 结算来源类型
     * saleOrder.销售单
     * buyOrder.采购单
     * buyOrderExpense.采购费用单
     * buyOrderAfterSale.采购售后单
     * buyOrderExpenseAfterSale.采购费用售后单
     * buyOrderRebateChargeApply.采购返利结算收款申请
     */
    private BusinessSourceTypeEnum sourceTypeEnum;

    /**
     * 结算类型
     */
    private List<SettlementTypeEnum> settlementTypeEnum;


}
