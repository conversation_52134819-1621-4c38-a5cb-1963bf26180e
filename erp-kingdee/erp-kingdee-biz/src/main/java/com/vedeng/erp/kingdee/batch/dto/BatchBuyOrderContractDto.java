package com.vedeng.erp.kingdee.batch.dto;

import com.vedeng.erp.kingdee.batch.common.bean.BatchBaseDto;
import lombok.*;

import java.math.BigDecimal;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchBuyOrderContractDto extends BatchBaseDto {
    private Integer buyOrderId;
    private String buyOrderNo;
    private BigDecimal totalAmount;
    private BigDecimal rate;
    //附件名称
    private String name;
    private String url;
    private Long validTimeBegin;
    private Long validTimeEnd;
    private Long validTime;
    private Integer invoiceType;
    //附件回写表id
    private Integer dataId;
    //oss http协议前缀
    private String ossPre;
    //附件domain
    private String domain;
    //附件uri
    private String uri;
    //附件id
    private Integer attachmentId;

    private Long attachmentAddTime;

}
