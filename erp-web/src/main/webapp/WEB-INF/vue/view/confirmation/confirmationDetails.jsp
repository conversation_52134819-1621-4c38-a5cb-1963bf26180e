<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<!-- 引入样式 -->
<%--<link rel="stylesheet" href="${pageContext.request.contextPath}/static/vue/element-ui/css/index.css">--%>
<!-- 引入脚本 -->
<%--<script src="${pageContext.request.contextPath}/static/vue/element-ui/js/vue.js"></script>--%>
<%--<script src="${pageContext.request.contextPath}/static/vue/element-ui/js/ele-index.js"></script>--%>
<style>
    .confirmName {
        color: #00a2d4;
    }
    .blue {
        color: blue;
    }
    .baseModel {
        padding-top: 20px;
        padding-left: 10px;
        font-size: 13px;
    }
</style>
<div id="app">
    <el-row :gutter="20" class="baseModel">
        <el-col :span="4">
            <div class="grid-content bg-purple">
                确认单回传
            </div>
        </el-col>
        <el-col :span="20">
            <div class="grid-content bg-purple confirmName">
                {{confirmationName}}
            </div>
        </el-col>
    </el-row>
    <el-row :gutter="20" class="baseModel">
        <el-col :span="4">
            <div class="grid-content bg-purple">
                附件列表:
            </div>
        </el-col>
        <el-col :span="20">
            <div class="grid-content bg-purple ">
                <div v-for = "att in attachments">
                    {{att.name}}&nbsp;
                    <a href="javascript:void(0)" @click="showAtt(att)">预览</a>
                </div>
            </div>
        </el-col>
    </el-row>
    <%--批次列表--%>
    <el-row :gutter="20" class="baseModel">
        <el-col :span="4">
            <div class="grid-content bg-purple">
                请选择确认收货商品
            </div>

        </el-col>
        <el-col :span="20">
            <div class="grid-content bg-purple-light" style="margin-right: 40px">
                    <div v-for="b in batchesRecodes">
                        <div style="padding-bottom: 10px">{{b.batchTime}}</div>
                        <el-table border stripe :data="b.batchExpressVos">
                            <el-table-column width="60" type="index" label="序号"></el-table-column>
                            <el-table-column width="85" prop="sku" label="订货号"></el-table-column>
                            <el-table-column width="400" prop="goodsName" label="产品名称" style=": blue !important;"></el-table-column>
                            <el-table-column width="120" prop="brand" label="品牌"></el-table-column>
                            <el-table-column width="90" prop="model" label="型号"></el-table-column>
                            <el-table-column width="85" prop="num" label="实际签收数量"></el-table-column>
                            <el-table-column  prop="logisticsOrderNo" label="物流单号"></el-table-column>
                        </el-table>
                    </div>
            </div>
        </el-col>
    </el-row>

    <%--按钮--%>
    <span style="display: block;text-align: center;" class="baseModel">
            <el-button type="primary" plain @click="reInit">关闭</el-button>
    </span>
</div>

<script type="text/javascript">
    let confirmationFormRecord = ${confirmationFormRecode};
    const vm = new Vue({
        el: '#app',
        data() {
            return {
                batchesRecodes: [{
                    batchId: null,
                    batchNo: '',
                    batchTime:'',
                    batchExpressVos: []
                }],
                attachments: [],
                confirmationFormRecord,
                popupIndex: -1,
                confirmationName: '',
            }
        },
        created() {
            this.attachments = confirmationFormRecord.attachmentsBack;
            this.batchesRecodes = confirmationFormRecord.batchesRecodes;
            this.popupIndex = 1;
            this.confirmationName = confirmationFormRecord.confirmationName
        },
        // watch: {
        //     // 监听popupIndex 禁止或者释放滚动
        //     popupIndex: function (newVal,oldVal) {
        //         if (newVal !== -1){
        //             let mo=function(e){e.preventDefault();};
        //             parent.document.body.style.overflow='hidden';
        //             parent.document.addEventListener("touchmove",mo,false);//禁止页面滑动
        //         } else {
        //             let mo=function(e){e.preventDefault();};
        //             parent.document.body.style.overflow='';//出现滚动条
        //             parent.document.removeEventListener("touchmove",mo,false);
        //         }
        //     }
        // },
        methods: {
            /*提交*/
            submit() {
                //发送请求
                axios({
                    url: '/orderstream/saleorder/uploadConfirmationForm.do',
                    method: 'post',
                    data: data
                });
                this.popupIndex = -1;
                this.reInit();
                parent.location.reload();
            },
            /*取消*/
            reInit() {
                this.popupIndex = -1;
                layer.closeAll();
                parent.layer.closeAll();
            },
            showAtt(att){
                window.open(att.uri)
            }
        }
    });
</script>
