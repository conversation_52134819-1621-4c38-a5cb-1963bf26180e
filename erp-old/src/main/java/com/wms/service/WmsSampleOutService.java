package com.wms.service;


import com.vedeng.authorization.model.User;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.order.model.Saleorder;
import com.vedeng.system.model.Attachment;
import com.wms.model.dto.AddSampleOutDto;
import com.wms.model.dto.WmsOutputOrderGoodsDto;
import com.wms.model.po.WmsOutputOrder;

import java.util.Collection;
import java.util.List;

public interface WmsSampleOutService {


    Long saveSampleOutOrder(AddSampleOutDto addSampleOutDto, User user);

    WmsOutputOrder getSampleOutOrderDetail(Long sampleOrderId);

    List<WmsOutputOrderGoodsDto> queryOutputGoodsBySampleOrderId(Long sampleOrderId);

    Attachment getAttachmentBySampleOrderId(Long sampleOrderId);

    Long saveEditSampleOutOrder(AddSampleOutDto addSampleOutDto, User user);

    void updateSampleOutAuditStatus(Long sampleOutOrderId, int value);

    String getAuditForSameOut(Long sampleOutOrderId);

    Integer getAuditLine(WmsOutputOrder wmsOutputOrder);

    Integer isKaOrg(User user);

    void updateLendOutAuditStatus(Long sampleOutOrderId, int value);

    void sampleOutOrderAuditPass(Long sampleOutOrderId) throws Exception;

    List<WarehouseGoodsOperateLog> getWLById(WarehouseGoodsOperateLog w, Integer isDirect);

    List<Integer> getAllMessageUserIdList(Long sampleOrderId);

    List<Integer> getPrintOutIdListByType(Saleorder saleorder, Integer isDirect, Integer expressType);

    /**
     * 校验样品申请单是否满足自动审核条件，并返回不满足的原因(此处迁移了销售订单审核流程中的校验逻辑)
     *
     * @param sampleOutId 样品申请单id
     * @return ResultInfo
     */
    ResultInfo checkWmsSampleOutAutoValid(Integer sampleOutId);

    String getCommentsOfAutoCheck(Integer sampleOutId);

    /**
     * 发送抄送消息-微信
     * @param sampleOutId
     * @param userNameList
     */
    void sendWeixinNotice(Long sampleOutId, Collection<String> userNameList);

    /**
     * 审批抄送提醒
     * @param sampleOutId
     * @param businessKey
     * @param taskId
     * @param userName
     */
    void sendWeixinNoticeForActiviti(Long sampleOutId, String businessKey, String taskId,String userName);
}
