package com.vedeng.erp.saleorder.service;

import com.vedeng.authorization.model.User;
import com.vedeng.order.model.Saleorder;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/9 11:08
 * @describe 电子签章
 */
public interface ElectronicSignatureService {


    /**
     * 发起半程电子签章
     * @param saleorderId
     * @return
     */
    void signature(User user, Integer saleorderId);

    /**
     *  获取电子签章记录
     * @param request request
     * @param saleOrder 订单Id
     * @param mv    视图
     */
    void electronicSignature(HttpServletRequest request,Saleorder saleOrder, ModelAndView mv);

    /**
     * 新增电子签章
     * @param saleOrder 订单
     * @param mv mv
     */
    void addElectronicSignature(Saleorder saleOrder, ModelAndView mv);

    /**
     * 保存电子签章
     * @param request request
     * @param saleorder 订单
     * @return Integer
     */
    Integer saveElectronicSignature(HttpServletRequest request, Saleorder saleorder);
}
