<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/base.css">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/general.css?rnd=${resourceVersionKey}">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/manage.css">
<script type="text/javascript">
function closewin(){
	var index = parent.layer.getFrameIndex(window.name); //获取当前窗体索引
    parent.layer.close(index);
}
</script>
<div class="tips-frame">
   <div class="tips-content pt20">
       <i class="icongantanhao" style="margin-bottom:-9px;"></i>
       <span>您没有操作权限。您暂时还未开通此权限，请联系研发部${noPowerContract}开通权限。</span>
   </div>
    <span id="locUrl" style="font-size:14px;margin-top:10px;padding-left: 180px;">
    </span>
    <script>
        var currentPagePath = window.location.pathname;
        var locUrlSpan = document.getElementById("locUrl");
        locUrlSpan.textContent = "访问地址为："+currentPagePath;
    </script>
   <div class="tips-confirm pt25">
       <button class="bt-small bt-bg-style  bg-light-green" type="button" onclick="closewin();" id='close-layer'>好的</button>
   </div>
</div>