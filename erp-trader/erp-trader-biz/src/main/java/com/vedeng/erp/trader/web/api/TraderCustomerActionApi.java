package com.vedeng.erp.trader.web.api;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.trace.enums.EventTrackingEnum;
import com.vedeng.common.util.StringUtil;
import com.vedeng.erp.trader.domain.dto.TraderCustomerActionDto;
import com.vedeng.erp.trader.service.TraderCustomerBaseService;
import com.vedeng.onedataapi.api.archived.res.TraderArchivedResDto;
import com.vedeng.onedataapi.api.usertrace.res.UserBehaviorResDto;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 客户行为 表控制层
 * <AUTHOR>
 * @Date 2023/8/15 9:50
 **/
@ExceptionController
@RestController
@RequestMapping("/traderCustomerAction")
@Slf4j
public class TraderCustomerActionApi {
    @Autowired
    private TraderCustomerBaseService traderCustomerBaseService;

    /**
     * 根据手机号码获取行为轨迹
     */
    @RequestMapping(value = "/get/behaviorByMobile",method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    @ResponseBody
    public R<UserBehaviorResDto> getBehaviorByMobile(String mobile, @RequestParam(value="pageNo",defaultValue="1",required = false)Integer pageNo , @RequestParam(value="pageSize",defaultValue="10",required = false)Integer pageSize) {
        if (StringUtil.isBlank(mobile)) {
            return  R.error(400, "手机号码不能为空");
        }
        return  R.success(traderCustomerBaseService.getBehaviorByMobile(mobile,pageNo,pageSize));
    }
    
    /**
     * 根据客户ID、档案ID列表查询客户档案
     * @param traderId 客户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param categoryId 分类ID(基础信息、售前、沟通、售中、售后)
     * @param pageNo 页码
     * @param pageSize 页条数
     * @return 返回客户档案
     */
    @RequestMapping(value = "/get/archiveByTraderId",method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    @ResponseBody
    public R<TraderArchivedResDto> archiveByTraderId(
    		@RequestParam(value="traderId",required = true)Integer traderId, 
    		@RequestParam(value="startTime",required = false)String startTime,
    		@RequestParam(value="endTime",required = false)String endTime,
    		@RequestParam(value="categoryId",required = false)Integer categoryId,
    		@RequestParam(value="archiveCursor",defaultValue="",required = false)String archiveCursor,
    		@RequestParam(value="pageNo",defaultValue="1",required = false)Integer pageNo , 
    		@RequestParam(value="pageSize",defaultValue="10",required = false)Integer pageSize) {
       
        return  R.success(traderCustomerBaseService.getCustomerArchiveTraceByArchiveIds(traderId, startTime,endTime,EventTrackingEnum.getShowArchivedIdList(categoryId), archiveCursor,pageNo, pageSize));
    }

    /**
     * 获取客户行为信息信息
     *
     * @param traderId 客户id
     * @return TraderCustomerPortraitDto
     */
    @RequestMapping(value = "/get/actionInfo", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<TraderCustomerActionDto> getTraderCustomerActionInfo(@RequestParam Integer traderId) {
        return R.success(traderCustomerBaseService.getTraderCustomerActionInfo(traderId));
    }


}
