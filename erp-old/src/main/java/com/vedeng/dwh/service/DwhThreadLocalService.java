package com.vedeng.dwh.service;

import com.alibaba.fastjson.JSONObject;
import com.vedeng.dwh.constant.DwhConstants;
import com.vedeng.dwh.model.dto.DwhErpOrganizationDto;
import com.vedeng.dwh.model.dto.DwhErpSubDeptDto;
import com.vedeng.dwh.model.dto.DwhErpUserDto;
import com.vedeng.kpi.share.KpiUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DwhThreadLocalService {

    @Autowired
    private DwhQueryService dwhQueryService;

    private static volatile ThreadLocal<Map<DwhErpSubDeptDto, Map<DwhErpSubDeptDto, List<DwhErpSubDeptDto>>>> organization = new ThreadLocal<>();

    private static volatile ThreadLocal<List<DwhErpOrganizationDto>> depts = new ThreadLocal<>();

    private static volatile ThreadLocal<List<DwhErpUserDto>> users = new ThreadLocal<>();


    /**
     * 加载全量组织信息.
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/3 8:57 下午.
     * @author: Tomcat.Hui.
     * @return: java.util.Map<com.vedeng.dwh.model.dto.DwhErpSubDeptDto,java.util.Map<com.vedeng.dwh.model.dto.DwhErpSubDeptDto,java.util.List<com.vedeng.dwh.model.dto.DwhErpSubDeptDto>>>.
     * @throw: .
     */
    public Map<DwhErpSubDeptDto, Map<DwhErpSubDeptDto, List<DwhErpSubDeptDto>>> getCurrentAllOrganization(){
        log.info("线程内部门信息organization值：{}", JSONObject.toJSONString(organization));
        if (null == organization.get() || organization.get().keySet().size() == 0) {
            Date today = KpiUtils.getDateStart(new Date());
            Timestamp startTime = new Timestamp(today.getTime());
            log.info("getFoldDeptsUsers获取全量部门,入参：{}", JSONObject.toJSONString(startTime));
            organization.set(dwhQueryService.getFoldDeptsUsers(startTime,startTime));
        }
        return organization.get();
    }

    /**
     * 获取用户下属部门&人员.
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/3 8:56 下午.
     * @author: Tomcat.Hui.
     * @param userId: .
     * @return: java.util.List<com.vedeng.dwh.model.dto.DwhErpSubDeptDto>.
     * @throw: .
     */
    public Map<DwhErpSubDeptDto, List<DwhErpSubDeptDto>> getUserOrganization(Integer userId){
        DwhErpUserDto user = getUserInfo(userId);
        Map<DwhErpSubDeptDto, List<DwhErpSubDeptDto>> result = new HashMap<>();
        if (user.getPositionName().equals(DwhConstants.XSZJ)) {
            result = getCurrentAllOrganization().entrySet().stream()
                    .filter(o -> o.getKey().getDepartId().equals(user.getOrgId())).findFirst()
//                    .filter(q -> !q.getValue().entrySet().stream().map(r -> r.getKey().getDepartId()).equals(136))
                    .map(e -> e.getValue())
                    .orElse(null);

            // changed by Randy.Xu   .Desc: begin 排除运营部
            result = result.entrySet().stream().filter(x -> x.getKey().getDepartId() != 136)
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            // changed by Randy.Xu   .Desc: end


        } else if (user.getPositionName().equals(DwhConstants.JL)){
            result = getCurrentAllOrganization().entrySet().stream().flatMap(e -> e.getValue().entrySet().stream())
                    .filter(o -> o.getKey().getDepartId().equals(user.getOrgId()))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,(k,v) -> k,HashMap::new));
            for (Map.Entry<DwhErpSubDeptDto, List<DwhErpSubDeptDto>> e : result.entrySet()) {
                e.setValue(e.getValue().stream().filter(x -> !x.getDepartId().equals(user.getOrgId())).collect(Collectors.toList()));
            }
        } else if (user.getPositionName().equals(DwhConstants.ZG)) {
            result = getCurrentAllOrganization().entrySet().stream().flatMap(e -> e.getValue().entrySet().stream())
                    .filter(o -> o.getValue().stream().anyMatch(l -> l.getDepartId().equals(user.getOrgId())))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,(k,v) -> k,HashMap::new));
            List<DwhErpSubDeptDto> collect = result.entrySet().stream().flatMap(e -> e.getValue().stream()).filter(o -> o.getDepartId().equals(user.getOrgId())).collect(Collectors.toList());
            DwhErpSubDeptDto dwhErpSubDeptDto = collect.get(0);
            HashMap<DwhErpSubDeptDto, List<DwhErpSubDeptDto>> newResult = new HashMap<>();
            newResult.put(dwhErpSubDeptDto,collect);
            result = newResult;
        }
        return result;
    }

    /**
     * 获取当前指定部门配置.
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/4 4:59 下午.
     * @author: Tomcat.Hui.
     * @param orgId: .
     * @return: com.vedeng.dwh.model.dto.DwhErpOrganizationDto.
     * @throw: .
     */
    public DwhErpOrganizationDto getDepartInfo(Integer orgId){
        Date today = KpiUtils.getDateStart(new Date());
        Timestamp startTime = new Timestamp(today.getTime());
        if (null == depts.get() || depts.get().size() == 0) {
            depts.set(dwhQueryService.getBaseOrganizationInfo(startTime,startTime));
        }
        return depts.get().stream().filter(d -> d.getOrgId().equals(orgId)).findFirst().orElse(null);
    }

    /**
     * 获取当前指定用户信息.
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/4 5:10 下午.
     * @author: Tomcat.Hui.
     * @param userId: .
     * @return: com.vedeng.dwh.model.dto.DwhErpUserDto.
     * @throw: .
     */
    public DwhErpUserDto getUserInfo(Integer userId){
        Date today = KpiUtils.getDateStart(new Date());
        Timestamp startTime = new Timestamp(today.getTime());
        if (null == users.get() || users.get().size() == 0) {
            users.set(dwhQueryService.getBaseUserInfo(startTime,startTime));
        }
        return users.get().stream().filter(u -> u.getUserId().equals(userId)).findFirst().orElse(null);
    }
}
