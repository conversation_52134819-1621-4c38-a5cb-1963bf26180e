var wsSoftPhoneLocale = {
    SPFunc_0 : " ",
    SPFunc_1 : "Log On",
    SPFunc_2 : "Log Off",
    SPFunc_3 : "Out Bound",
    SPFunc_4 : "Inner Call",
    SPFunc_5 : "Monitor",
    SPFunc_6 : "Hold",
    SPFunc_7 : "Consult",
    SPFunc_8 : "Outline Consult",
    SPFunc_9 : "Transfer",
    SPFunc_10 : "Drop Call",
    SPFunc_11 : "Fetch",
    SPFunc_12 : "Cancel OutBound",
    SPFunc_13 : "Cancel Consult",
    SPFunc_14 : "Finish Consult",
    SPFunc_15 : "Transfer",
    SPFunc_16 : "Swap Talk",
    SPFunc_17 : "Conf",
    SPFunc_18 : "Swap Talk",
    SPFunc_19 : "Cancel",
    SPFunc_20 : "Swap Talk",
    SPFunc_21 : "Transfer",
    SPFunc_22 : "Finish Consult",
    SPFunc_23 : "Swap Talk",
    SPFunc_24 : "Cancel Monitor",
    SPFunc_25 : "Force Drop",
    SPFunc_26 : "Intercept",
    SPFunc_27 : "Intervene",
    SPFunc_28 : "Finish Monitor",
    SPFunc_29 : "Drop Customer",
    SPFunc_30 : "Drop Agent",
    SPFunc_31 : "Finish Wrapup",
    SPFunc_32 : "Drop OutLine",
    SPFunc_33 : "Leave",
    SPFunc_34 : "Resume",
    SPFunc_35 : "Cancel Inner",
    SPFunc_36 : "WorkMode",
    SPFunc_37 : "Agent Msg",
    SPFunc_38 : "Cancel Prepare",
    SPFunc_39 : "Cancel Prepare",
    SPFunc_40 : "Cancel Prepare",
    SPFunc_41 : "Conf",
    SPFunc_42 : "OffHook",
    SPFunc_43 : "Cancel Preview",
    SPFunc_44 : "Preview OutBound",
    SPFunc_45 : "Delete Task",
    SPFunc_46 : "Cancel Task",
    SPFunc_47 : "Finish",
    SPFunc_48 : "Finish Conf",
    SPFunc_49 : "Quit Conf",
    SPFunc_50 : "Drop Party",
    SPFunc_51 : "Agent Msg",
    SPFunc_52 : "Cancel Waiting",
    SPFunc_53 : "Alert Transfer",
    SPFunc_54 : "Preset Leave",
    SPFunc_55 : "Cancel Leave",
    SPFunc_56 : "Consult",
    SPFunc_57 : "Transfer IVR",
    SPFunc_58 : "Transfer OutLine",
    SPFunc_59 : "Transfer Agent",
    SPFunc_60 : "Mute Mic",
    SPFunc_61 : "UnmuteMic",
    SPFunc_62 : "DialPad",
    SPFunc_63 : "Volume",
    SPFunc_64 : "Config",
    SPFunc_65 : "TransferToIVR",
    SPFunc_66 : "Answer Video",
    SPFunc_67 : "Finish Video",
    SPFunc_68 : "MonitorControl",

    AgentState_0:"Null",
    AgentState_1:"Init",
    AgentState_2:"Init Phone",
    AgentState_3:"Idle",
    AgentState_4:"Play AgentID",
    AgentState_5:"Ring",
    AgentState_6:"Talk",
    AgentState_7:"Hold",
    AgentState_8:"Inputting Number",
    AgentState_9:"OutBounding",
    AgentState_10:"Consult Alerting",
    AgentState_11:"Consult",
    AgentState_12:"Consult Hold",
    AgentState_13:"Hold Calling",
    AgentState_14:"OutLine Talk",
    AgentState_15:"OutLine Talk",
    AgentState_16:"Monitor Select",
    AgentState_17:"Monitor",
    AgentState_18:"Conf",
    AgentState_19:"Wrapup",
    AgentState_20:"Consulted",
    AgentState_21:"Conf",
    AgentState_22:"Conf",
    AgentState_23:"Leave",
    AgentState_24:"Inner Call Select",
    AgentState_25:"Inner Call Alerting",
    AgentState_26:"Inner Talk",
    AgentState_27:"Waiting Ring",
    AgentState_28:"Preview Task",
    AgentState_29:"Special Service",
    AgentState_30:"Simple Conf",
    AgentState_31:"IVR Verifying",
    AgentState_32:"Transferring",
    AgentState_33:"Consult",
    AgentState_34:"OnLine",
    AgentState_35:"Conf",
    AgentState_36:"Swapping",
    AgentState_37:"Swapping",
    AgentState_38:"Held",
    AgentState_39:"Swapping",
    AgentState_40:"Swapping",
    AgentState_41:"Inner Hold",
    AgentState_42:"Inner Held",
    AgentState_43:"Transferred OutBounding",
    AgentState_44:"Alert Transferring",
    AgentState_45:"ADC OutBounding",
    AgentState_46:"Waiting Alert",
    AgentState_47:"Waiting Alert",
    AgentState_48:"Alerting",
    AgentState_49:"Alerting",
    AgentState_50:"ConsultQueue",
    AgentState_51:"VideoConfQueue",
    AgentState_52:"VideoConnecting",
    AgentState_53:"VideoConf",
    AgentState_54:"MonitorAlerting",

    AgentWorkMode_0 : "IdleMode",
    AgentWorkMode_1 : "LastCallMode",
    AgentWorkMode_2 : "NRTMode",
    AgentWorkMode_3 : "BusyMode",
    AgentWorkMode_4 : "MonitorMode",
    AgentWorkMode_5 : "PreviewMode",
    AgentWorkMode_6 : "LeaveMode",
    AgentWorkMode_7 : "BlockMode",
    AgentWorkMode_8 : "InboundMode",


    DeviceCallState_0:"Null",
    DeviceCallState_2:"CallInner",
    DeviceCallState_4:"Connected",
    DeviceCallState_5:"Initiated",
    DeviceCallState_6:"BusyTone",
    DeviceCallState_8:"Alerting",
    DeviceCallState_10:"Hurry_HookOn",
    DeviceCallState_11:"Playing",
    DeviceCallState_12:"Swapping",
    DeviceCallState_13:"SwapBack",
    DeviceCallState_14:"ConsultHold",
    DeviceCallState_16:"DialOut",
    DeviceCallState_32:"Idle",
    DeviceCallState_64:"Deflect",
    DeviceCallState_128:"Transfer",
    DeviceCallState_256:"Hold",
    DeviceCallState_512:"Conf",
    DeviceCallState_1024:"MonitorLine",
    DeviceCallState_2048:"Consult",
    DeviceCallState_4096:"DialTone",
    DeviceCallState_8192:"Failed",
    DeviceCallState_16384:"Authenticating",
    DeviceCallState_32768:"NetConnecting",

    Transfer_IVR:"IVR",
    Transfer_Agent:"Agent",
    Transfer_OutLine:"OutLine",

    Caption_SkillCode:"SkillCode",
    Caption_SkillName:"SkillName",
    Caption_Length:"Length",
    Caption_AllQueue:"AllQueue",
    Caption_MyQueue:"MyQueue",
    Caption_AutoAnswer:"AutoAnswer",

    ShowLog:"Show Log",
    ClearLog:"Clear Log",
    IVRPanelCaption:"IVR ShortCut",
    ClearLogSucc:"Clear Succ",
};

/*
 * 描述：动态取得本地资源文件内容
 *
 * 参数： key 对应的资源的key params 对应资源中的参数列表
 *
 * 返回：对应的资源内容
 *
 * 用法： getLocale("helloParam",{first:value1,second:value2});
 */
function getWSSoftPhoneText(key, params) {
    var result = ""; // 对应的资源的内容

    if (typeof (key) != 'undefined'
        && typeof (wsSoftPhoneLocale) != 'undefined') {
        // 根据key取得对应的资源内容，如果没有找到则返回key值
        if (wsSoftPhoneLocale[key] != undefined) {
            result = wsSoftPhoneLocale[key];
        } else {
            result = key;
        }

        if (typeof (params) != 'undefined') {
            // 替换对应参数为value的值
            var regExp = new RegExp(); // 替换资源中参数的正则
            for (var i = 0; i < params.length; i++) {
                regExp = eval("/{[" + i + "]}/g");
                result = result.replace(regExp, params[i]);
            }
        }
        // 如果没有找到对应的资源则返回 "No Value"
        if (/{[0-9]+}/.test(result)) {
            result = result.replace(/{[0-9]+}/g, "No Value");
        }
    }
    return result;
}

function getStateName(st) {
    return getWSSoftPhoneText("AgentState_" + st);
}

function getWorkModeName(wkmd) {
    return getWSSoftPhoneText("AgentWorkMode_" + wkmd);
}
function getWorkModeStateName(wkmd, st) {
    return getWSSoftPhoneText("AgentWorkMode_" + wkmd) + "-"
        + getWSSoftPhoneText("AgentState_" + st);
}

function getDeviceCallStateName(st){
    return getWSSoftPhoneText("DeviceCallState_" + st);
}

function getTransferName(item){
    return getWSSoftPhoneText("Transfer_" + item);
}

function getSoftPhoneCaption(key){
    return getWSSoftPhoneText("Caption_" + key);
}
