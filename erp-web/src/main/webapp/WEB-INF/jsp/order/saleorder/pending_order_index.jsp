<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="待客户签收列表" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='<%= basePath %>static/js/order/saleorder/pending_order_index.js?rnd=${resourceVersionKey}'></script>

<div class="searchfunc">
    <form method="post" id="search" action="<%= basePath %>orderstream/saleorder/pendingOrderIndex.do">
        <ul class="search-wrap">
            <li>
                <label class="infor_name">订单号</label>
                <input type="text" class="input-middle" name="saleorderNo" id="saleorderNo" value="${saleorder.saleorderNo}"/>
            </li>
            <li>
                <label class="infor_name">客户名称</label>
                <input type="text" class="input-middle" name="traderName" id="traderName" value="${saleorder.traderName}"/>
            </li>
            <li>
                <label class="infor_name">归属销售</label>
                <select class="input-middle" name="optUserId" id="optUserId">
                    <option value="-1">全部</option>
                    <c:forEach items="${userList}" var="list">
                        <option value="${list.userId}" <c:if test="${saleorder.optUserId eq list.userId}">selected="selected"</c:if>>${list.username}</option>
                    </c:forEach>
                </select>
            </li>
            <li>
                <label class="infor_name">销售部门</label>
                <select class="input-middle" name="orgId" id="orgId">
                    <option value="-1">全部</option>
                    <c:forEach items="${orgList}" var="org">
                        <option value="${org.orgId}" <c:if test="${saleorder.orgId eq org.orgId}">selected="selected"</c:if>>${org.orgName}</option>
                    </c:forEach>
                </select>
            </li>
            <li>
                <label class="infor_name">订单联系人</label>
                <input type="text" class="input-middle" name="traderContactName" id="traderContactName" value="${saleorder.traderContactName}"/>
            </li>
            <li>
                <label class="infor_name">SKU</label>
                <input type="text" class="input-middle" name="sku" id="sku" value="${saleorder.sku}"/>
            </li>
            <li>
                <label class="infor_name">通知已发送</label>
                <select name="isSendSms" class="input-middle">
                    <option value="-1">全部</option>
                    <option value="1" <c:if test="${saleorder.isSendSms == 1}"> selected </c:if>>是</option>
                    <option value="0" <c:if test="${saleorder.isSendSms == 0}"> selected </c:if>>否</option>
                </select>
            </li>
            <li>
                <div class="infor_name specialinfor">
                    <select name="searchDateType" id="searchDateType" style="width:85px;">
                        <option value="2" <c:if test="${saleorder.searchDateType == 2}">selected="selected"</c:if> >生效时间</option>
                        <option value="7" <c:if test="${saleorder.searchDateType == 7}">selected="selected"</c:if> >最近通知时间</option>
                    </select>
                </div>
                <input class="Wdate f_left input-smaller96 mr5" type="text" placeholder="请选择日期" autocomplete="off" onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'searchEndtimeStr\')}'})" name="searchBegintimeStr" id="searchBegintimeStr" value='<date:date value ="${saleorder.searchBegintime}" format="yyyy-MM-dd"/>'>
                <div class="gang">-</div>
                <input class="Wdate f_left input-smaller96" type="text" placeholder="请选择日期" autocomplete="off" onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'searchBegintimeStr\')}'})" name="searchEndtimeStr" id="searchEndtimeStr" value='<date:date value ="${saleorder.searchEndtime}" format="yyyy-MM-dd"/>'>
            </li>

        </ul>
        <div class="tcenter">
            <span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();" id="searchSpan">搜索</span>
            <span class="bt-small bg-light-blue bt-bg-style mr20" onclick="reset();search();">重置</span>
        </div>
    </form>
</div>
<div class="content">
    <div class="">
        <div class="normal-list-page">
            <div class="option-wrap J-fix-wrap" style="background-color: #B2B2B2">
                <div class="option-fix-wrap cf J-fix-cnt">
                    <input type="hidden" name="saleorderReq" value='${saleorderReq}'/>
                    <c:choose>
                        <c:when test="${smsButton}">
                            <input type="button" class="btn btn-small bt-bg-style bg-light-blue" onclick=sendSms(); value="发送签收通知"/>
                        </c:when>
                    </c:choose>
                    <c:choose>
                        <c:when test="${copyButton == 1}">
                            <input type="text" style="display: none" id="copyUrl" value="${copyUrl}" />
                            <input type="button" class="btn btn-small bt-bg-style bg-light-blue copyButton" name="copyButton" id="copyButton" value="复制链接"
                                   style="margin-right: 0px;" onclick="copyOrderIds('${traderId}','${traderContactMobile}');"/>
                            <i class="vd-tip-icon vd-icon icon-problem1" style="color: #2E8AE6;font-size: 12px"></i>
                        </c:when>
                        <c:when test="${copyButton ==2}">

                        </c:when>
                        <%--                        <c:otherwise>--%>
                        <%--                            <input type="button" class="btn btn-small bt-bg-style bg-light-grey copyButton" name="copyButton" id="copyButton" value="复制链接"--%>
                        <%--                                   style="margin-right: 0px;" disabled="true"/>--%>
                        <%--                            <i class="vd-tip-icon vd-icon icon-problem1" style="color: #2E8AE6;font-size: 12px"></i>--%>
                        <%--                        </c:otherwise>--%>
                    </c:choose>


                </div>
            </div>
            <table
                    class="table table-bordered table-striped table-condensed table-centered">
                <thead>
                <tr>
                    <th class="wid6"><input type="checkbox" name="all_checknox" onclick="selectall(this,'checkbox');" value="checknox"/> <span>全选</span></th>
                    <th class="wid6">序号</th>
                    <th class="wid10">订单号</th>
                    <th class="wid10">客户名称</th>
                    <th class="wid15">归属销售</th>
                    <th class="wid10">销售部门</th>
                    <th class="wid10">订单联系人</th>
                    <th class="wid10">产品名称</th>
                    <th class="wid10">待客户签收数量</th>
                    <th class="wid10">订单生效时间</th>
                    <th class="wid10">通知发送</th>
                    <th class="wid10">最近通知时间</th>
                </tr>
                </thead>
                <tbody>
                <c:forEach var="list" items="${saleorderList}" varStatus="num">
                    <tr>
                        <td>
                            <input type="checkbox" class="J-check-item" name="checknox"
                                   data-saleorderGoodsId="${list.saleorderGoodsId}"
                                   data-traderName="${list.traderName}"
                                   data-traderId="${list.traderId}"
                                   data-goodsName="${list.goodsName}"
                                   data-traderContactMobile="${list.traderContactMobile}"
                                   data-traderContactName="${list.traderContactName}"
                                   value="${list.saleorderId}" onclick="checkInfo(this)">
                        </td>
                        <td>${num.count}</td>
                        <td>${list.saleorderNo}</td>
                        <td>${list.traderName}</td>
                        <td>${list.optUserName}</td>
                        <td>${list.salesDeptName}</td>
                        <td>${list.traderContactName}</td>
                        <td>
                                ${list.goodsName}
                            <br>
                                ${list.sku}</td>
                        <td>${list.deliveryNum - list.afterReturnNum - list.confirmNumber}</td>
                        <td><date:date value="${list.validTime}" /></td>
                        <td>
                            <c:choose>
                                <c:when test="${list.sendTime == null}">
                                    否
                                </c:when>
                                <c:otherwise>
                                    是
                                </c:otherwise>
                            </c:choose>
                        </td>
                        <td><fmt:formatDate value="${list.sendTime}" pattern="yyyy-MM-dd HH:mm:ss"/></td>
                    </tr>
                </c:forEach>
                </tbody>
            </table>
            <c:if test="${empty saleorderList}">
                <!-- 查询无结果弹出 -->
                <div class="noresult">查询无结果！请尝试使用其他搜索条件。</div>
            </c:if>
        </div>
    </div>
    <tags:page page="${page}"/>
</div>

<script type="text/javascript">
    $('.vd-tip-icon.vd-icon.icon-problem1').click(function () {
        layer.alert("当搜索结果的客户名称+订单联系人手机号均相同时，复制链接可用，否则禁用；可将链接私聊发给客户完成货物签收")
    });
</script>

<%@ include file="../../common/footer.jsp"%>