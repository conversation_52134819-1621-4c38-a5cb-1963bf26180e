package com.newtask.data.dto;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description com.newtask.data.dto
 * @Date 2021/10/19 18:38
 */
@Getter
@Setter
public class BuyorderDataDto {

    /**
     * 采购数据表ID
     */
    private Integer buyorderDataId;

    /**
     * 采购订单ID
     */
    private Integer buyorderId;

    /**
     * 订单主状态
     */
    private String subStatus;

    /**
     * 订单审核状态
     */
    private Integer verifyStatus;

    /**
     * 付款申请到财务
     */
    private Integer isFinanceAlreadyStatus;

    /**
     * 是否全部添加物流信息
     */
    private Integer isAllLogisticsStatus;

    /**
     * 录票状态
     */
    private Integer recordInvoiceApplyStatus;


    /**
     * 未还账期款
     */

    private BigDecimal lackAccountPeriodAmount;
}
