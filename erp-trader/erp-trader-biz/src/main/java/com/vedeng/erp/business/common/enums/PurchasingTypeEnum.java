package com.vedeng.erp.business.common.enums;

import lombok.Getter;

@Getter
public enum PurchasingTypeEnum {

    SMALL(405,"直接采购"),

    BIG(406,"招投标采购"),
    
    ;
    
    
    
    private final Integer code;


    private final String title;

    PurchasingTypeEnum(Integer code, String title) {
        this.code = code;
        this.title = title;
    }


    public static String getTitleByCode(Integer code) {
        for (PurchasingTypeEnum businessChanceLevelEnum : PurchasingTypeEnum.values()) {
            if (businessChanceLevelEnum.getCode().equals(code)) {
                return businessChanceLevelEnum.getTitle();
            }
        }
        return "";
    }

}
