package com.vedeng.goods.web.api;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.goods.domain.dto.GoodsFinanceInfoDto;
import com.vedeng.goods.service.GoodsFinanceInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

/**
 * @description:
 * @author: Zeno.zuo
 * @email: <EMAIL>
 * @date: 2022/11/29 14:20
 **/
@Controller
@RequestMapping("/goods/finance/api")
@Slf4j
@ExceptionController
public class GoodsFinanceApi {
    @Autowired
    private GoodsFinanceInfoService goodsFinanceInfoService;

    /**
     * 查询商品财务信息详情
     * @param goodsFinanceInfoDto
     * @return
     */
    @RequestMapping("/goodsFinanceInfoData")
    @ResponseBody
    public R<?> goodsFinanceInfoData(GoodsFinanceInfoDto goodsFinanceInfoDto) {
        GoodsFinanceInfoDto goodsFinanceInfo = goodsFinanceInfoService.getGoodsFinanceInfoDetailById(goodsFinanceInfoDto.getGoodsFinanceId());
        goodsFinanceInfoService.auditButton(goodsFinanceInfo);
        return R.success(goodsFinanceInfo);
    }

    /**
     * 保存商品财务信息
     * @param goodsFinanceInfoDto
     * @return
     */
    @RequestMapping(value = "/saveGoodsFinanceInfo", method = RequestMethod.PUT)
    @ResponseBody
    public R<?> saveGoodsFinanceInfo(GoodsFinanceInfoDto goodsFinanceInfoDto){
        return R.success(goodsFinanceInfoService.saveGoodsFinanceInfo(goodsFinanceInfoDto));
    }


    /**
     * 审核
     * @param goodsFinanceInfoDto
     * @return
     */
    @RequestMapping(value = "/audit", method = RequestMethod.POST)
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<?> audit(GoodsFinanceInfoDto goodsFinanceInfoDto){
        goodsFinanceInfoService.audit(goodsFinanceInfoDto);
        return R.success();
    }


    /**
     * 导入数据
     * @param file
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/import", method = RequestMethod.POST)
    @ResponseBody
    public R<?> importExcel(@RequestPart("file") MultipartFile file) throws Exception {
        try {
            goodsFinanceInfoService.importExcelFile(file);
        } catch (ServiceException e) {
            log.error("【importExcel】 处理异常",e);
            throw new ServiceException(e.getMessage());
        } catch (Exception e) {
            log.error("【importExcel】 处理异常",e);
            throw new ServiceException("文件错误");
        }
        return R.success();
    }
}
