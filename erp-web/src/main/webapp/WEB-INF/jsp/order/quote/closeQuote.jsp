<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="确认审核" scope="application" />
<%@ include file="../../common/common.jsp"%>
 <div class="formpublic">
            <form method="post" action="" id="complement">
                <ul>
                   <li>
                   	 <div class="infor_name">
						<span>*</span>
						<lable for='name'>关闭原因</lable>
					 </div>
                   	 <div class="f_left">
                        <select name="reason" id="firstreason">
                            <option value="">请选择关闭原因</option>
                            <c:forEach var="il" items="${firstCloseReason}">
                                <c:if test="${!(fn:length(il.optionType) > 0)}">
                                    <option  value="${il.sysOptionDefinitionId}">${il.title}</option>
                                </c:if>
                            </c:forEach>
                        </select>
                         <select class="J-select" name="closeReasonId" ></select>
                    </div>
                   </li>
                    <li id="closeReasonComment">
                        <div>
                            <textarea type="text" style='width: 100%; height: 50px' name="closeReasonComment" id="closeReasonComment_1" placeholder="详细原因"  value="" ></textarea>
                        </div>
                    </li>
                </ul>
                <div class="add-tijiao tcenter">
                	<input type="hidden" name="formToken" value="${formToken}"/>
                	<input type="hidden" value="${quoteorderId}" name="quoteorderId">
                	<input type="hidden" value="${saleCloseReason}" id="J-saleCloseReason">
                	<input type="hidden" value="${goodsCloseReason}" id="J-goodsCloseReason">
                	<input type="hidden" value="${traderCloseReason}" id="J-traderCloseReason">

                    <button type="button" class="bg-light-green" onclick="closeQuoteVerify()">提交</button>
                    <button class="dele" type="button" id="close-layer">取消</button>
                </div>
           </form>
</div>
<script type="text/javascript" src='<%= basePath %>static/js/order/quote/complete.js?rnd=${resourceVersionKey}'></script>
<%@ include file="../../common/footer.jsp"%>