package com.vedeng.erp.trader.domain.dto;


import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 客户行为
 */
@Data
public class TraderCustomerActionDto {
    /**
     * 客户id
     */
    private Integer traderCustomerId;

    /**
     * 客户启用状态显示
     */
    private String traderCustomerIsEnable;
    /**
     * 客户名称
     */
    private String traderName;
    /**
     * 封装交易类标签 各个时间段内的分类和品牌信息
     */
    TraderCategoryBrandDto traderTimeTagsMap = new TraderCategoryBrandDto();
    /**
     * 客户性质
     */
    private Integer customerNature;
    /**
     * 归属平台
     */
    private Integer belongPlatform;

    /**
     * 是否启用
     */
    private Integer isEnable;
    /**
     * 决策标签-生命周期
     */
    private String lifeCycle;

    /**
     * 决策标签-客户等级
     */
    private String customerGrade;

    /**
     * 交易类标签-累计下单
     */
    private String historyTransactionNum;

    /**
     * 交易类标签-历史交易总额
     */
    private String historyTransactionAmount;

    /**
     * 交易类标签-首次下单时间
     */
    private String firstOrderTime;

    /**
     * 交易类标签-最近下单时间
     */
    private String lastOrderTime;

    /**
     * 交易类标签-近一年平均购买周期
     */
    private String lastYearAveragePurchasePeriod;

    /**
     * 交易类标签-历史累计客单价
     */
    private String historyCumulativeUnitPrice;

    /**
     * 交易类标签-订单覆盖科室
     */
    private String departmentCover;

    /**
     * 交易类标签-订单收货地区
     */
    private String orderCoverArea;

    /**
     * 交易类标签-订单装机地区
     */
    private String installedArea;
    /**
     * 询价类标签-最近询价时间
     */
    private String latestInquiryTime;
    /**
     * 询价类标签-最近询价商品
     */
    private String latestInquiryContent;
    /**
     * 销售行为类标签-沟通记录
     */
    private String communicationRecord;
    /**
     * 询价类标签-历史沟通次数
     */
    private String historyCommunicationNum;
}
