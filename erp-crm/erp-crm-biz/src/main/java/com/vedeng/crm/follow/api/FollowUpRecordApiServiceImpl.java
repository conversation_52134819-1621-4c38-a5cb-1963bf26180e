package com.vedeng.crm.follow.api;

import com.vedeng.crm.api.FollowUpRecordApiService;
import com.vedeng.crm.follow.service.FollowUpRecordService;
import com.vedeng.erp.trader.dto.CommunicateRecordDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/12/9
 */
@Service
@Slf4j
public class FollowUpRecordApiServiceImpl implements FollowUpRecordApiService {

    @Autowired
    @Lazy
    private FollowUpRecordService followUpRecordService;


    @Override
    public void add(CommunicateRecordDto communicateRecordDto) {
        followUpRecordService.add(communicateRecordDto);
    }
}
