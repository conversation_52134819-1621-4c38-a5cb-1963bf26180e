package com.vedeng.finance.controller;

import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.finance.model.PayOrder;
import com.vedeng.finance.service.PayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @ClassName PayController.java
 * @Description TODO 支付信息推送
 * @createTime 2020年09月24日 17:49:00
 */
@RestController
@RequestMapping("/pay")
public class PayController {
    private static final Class PayOrder = null;
    @Autowired
    private PayService payService;

    /**
     * <b>Description:</b>付款成功，推送流水信息给ERP
     * @param request
     * @param response
     * @return ResultInfo
     * @Note
     * <b>Author：</b> lijie
     * <b>Date:</b> 2018年11月5日 下午1:46:05
     */
    @ResponseBody
    @RequestMapping(headers = "version=v1", value = "/orderpay", method = RequestMethod.POST)
    public ResultInfo orderPay(HttpServletRequest request, HttpServletResponse response) {
        try {
            ResultInfo resultInfo = null;
            String data = request.getParameter("data");
            PayOrder payOrder = JsonUtils.readValue(data, PayOrder.class);
            if(null == payOrder
                    || null == payOrder.getOrderNo()
                    || null == payOrder.getCapitalBillId()){
                return new ResultInfo(-1,"参数错误");
            }

            resultInfo = payService.orderPay(payOrder);
            return resultInfo;
        } catch (Exception e) {
            return new ResultInfo(-1,e.getMessage());
        }

    }

    /**
     * <b>Description:</b>退款成功，推送流水信息给ERP
     * @param request
     * @param response
     * @return ResultInfo
     * @Note
     * <b>Author：</b> lijie
     * <b>Date:</b> 2018年11月6日 上午8:55:02
     */
    @ResponseBody
    @RequestMapping(headers = "version=v1", value = "/orderrefund", method = RequestMethod.POST)
    public ResultInfo orderRefund(HttpServletRequest request, HttpServletResponse response) {
        try {
            ResultInfo resultInfo = null;
            String data = request.getParameter("data");
            PayOrder payOrder = JsonUtils.readValue(data, PayOrder.class);
            if(null == payOrder
                    || null == payOrder.getOrderNo()
                    || null == payOrder.getCapitalBillId()){
                return new ResultInfo(-1,"参数错误");
            }

            resultInfo = payService.orderRefund(payOrder);
            return resultInfo;
        } catch (Exception e) {
            return new ResultInfo(-1,e.getMessage());
        }

    }
}
