<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.saleorder.dao.VJdGoodsMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.saleorder.model.po.VJdGoods">
    <!--@mbg.generated-->
    <!--@Table V_JD_GOODS-->
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="JD_GOODS_ID" jdbcType="BIGINT" property="jdGoodsId" />
    <result column="VD_SKU_NO" jdbcType="VARCHAR" property="vdSkuNo" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="DELETE_FLAG" jdbcType="TINYINT" property="deleteFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, JD_GOODS_ID, VD_SKU_NO, ADD_TIME, MOD_TIME, CREATOR, UPDATER, DELETE_FLAG
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from V_JD_GOODS
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from V_JD_GOODS
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.saleorder.model.po.VJdGoods" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into V_JD_GOODS (JD_GOODS_ID, VD_SKU_NO, ADD_TIME, 
      MOD_TIME, CREATOR, UPDATER, 
      DELETE_FLAG)
    values (#{jdGoodsId,jdbcType=BIGINT}, #{vdSkuNo,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, 
      #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, 
      #{deleteFlag,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.saleorder.model.po.VJdGoods" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into V_JD_GOODS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="jdGoodsId != null">
        JD_GOODS_ID,
      </if>
      <if test="vdSkuNo != null">
        VD_SKU_NO,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="deleteFlag != null">
        DELETE_FLAG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="jdGoodsId != null">
        #{jdGoodsId,jdbcType=BIGINT},
      </if>
      <if test="vdSkuNo != null">
        #{vdSkuNo,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.saleorder.model.po.VJdGoods">
    <!--@mbg.generated-->
    update V_JD_GOODS
    <set>
      <if test="jdGoodsId != null">
        JD_GOODS_ID = #{jdGoodsId,jdbcType=BIGINT},
      </if>
      <if test="vdSkuNo != null">
        VD_SKU_NO = #{vdSkuNo,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="deleteFlag != null">
        DELETE_FLAG = #{deleteFlag,jdbcType=TINYINT},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.saleorder.model.po.VJdGoods">
    <!--@mbg.generated-->
    update V_JD_GOODS
    set JD_GOODS_ID = #{jdGoodsId,jdbcType=BIGINT},
      VD_SKU_NO = #{vdSkuNo,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      DELETE_FLAG = #{deleteFlag,jdbcType=TINYINT}
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update V_JD_GOODS
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="JD_GOODS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.jdGoodsId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="VD_SKU_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.vdSkuNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="DELETE_FLAG = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.deleteFlag,jdbcType=TINYINT}
        </foreach>
      </trim>
    </trim>
    where ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update V_JD_GOODS
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="JD_GOODS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.jdGoodsId != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.jdGoodsId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="VD_SKU_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.vdSkuNo != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.vdSkuNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="DELETE_FLAG = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deleteFlag != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.deleteFlag,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
    </trim>
    where ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="ID" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into V_JD_GOODS
    (JD_GOODS_ID, VD_SKU_NO, ADD_TIME, MOD_TIME, CREATOR, UPDATER, DELETE_FLAG)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.jdGoodsId,jdbcType=BIGINT}, #{item.vdSkuNo,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP}, 
        #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, #{item.updater,jdbcType=INTEGER}, 
        #{item.deleteFlag,jdbcType=TINYINT})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.saleorder.model.po.VJdGoods" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into V_JD_GOODS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      JD_GOODS_ID,
      VD_SKU_NO,
      ADD_TIME,
      MOD_TIME,
      CREATOR,
      UPDATER,
      DELETE_FLAG,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      #{jdGoodsId,jdbcType=BIGINT},
      #{vdSkuNo,jdbcType=VARCHAR},
      #{addTime,jdbcType=TIMESTAMP},
      #{modTime,jdbcType=TIMESTAMP},
      #{creator,jdbcType=INTEGER},
      #{updater,jdbcType=INTEGER},
      #{deleteFlag,jdbcType=TINYINT},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        ID = #{id,jdbcType=INTEGER},
      </if>
      JD_GOODS_ID = #{jdGoodsId,jdbcType=BIGINT},
      VD_SKU_NO = #{vdSkuNo,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      DELETE_FLAG = #{deleteFlag,jdbcType=TINYINT},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.saleorder.model.po.VJdGoods" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into V_JD_GOODS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="jdGoodsId != null">
        JD_GOODS_ID,
      </if>
      <if test="vdSkuNo != null">
        VD_SKU_NO,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="deleteFlag != null">
        DELETE_FLAG,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="jdGoodsId != null">
        #{jdGoodsId,jdbcType=BIGINT},
      </if>
      <if test="vdSkuNo != null">
        #{vdSkuNo,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=TINYINT},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        ID = #{id,jdbcType=INTEGER},
      </if>
      <if test="jdGoodsId != null">
        JD_GOODS_ID = #{jdGoodsId,jdbcType=BIGINT},
      </if>
      <if test="vdSkuNo != null">
        VD_SKU_NO = #{vdSkuNo,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="deleteFlag != null">
        DELETE_FLAG = #{deleteFlag,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>

<!--auto generated by MybatisCodeHelper on 2022-12-07-->
  <select id="findOneByJdGoodsIdAndDeleteFlag" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from V_JD_GOODS
        where JD_GOODS_ID=#{jdGoodsId,jdbcType=BIGINT} and DELETE_FLAG=#{deleteFlag,jdbcType=TINYINT}
    </select>

  <select id="getGoodsMappingByList" resultType="com.vedeng.erp.saleorder.model.po.VJdGoods">
    select JD_GOODS_ID jdGoodsId,VD_SKU_NO vdSkuNo from V_JD_GOODS where JD_GOODS_ID in
    <foreach collection="list" item="item" separator="," open="(" close=")">
      #{item.jdSkuNo}
    </foreach>
  </select>
</mapper>