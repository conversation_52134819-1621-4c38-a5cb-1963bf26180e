package com.vedeng.order.service;

import com.vedeng.authorization.model.User;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.order.model.Buyorder;

public interface GeInfoCollectionService {
    //保存GE主机和探头序列号
    ResultInfo saveSncode(String sku, String masterSlavejson, Integer buyorderGoodsId, Integer goodsId, User user);
    //更新GE供应商信息
    ResultInfo upadteGeTraderInfo(Buyorder buyorder, String traderContactStr, String traderAddressStr);
    //更新GE合同信息
    ResultInfo upadteGeConInfo(String geBuyorderGoodsIds, String geContractNo, String geSaleContractNo);
    //删除GE合同信息
    ResultInfo delGeInfoCollection(Integer buyorderGoodsId);
    //删除主机和探头
    void delMasterSlave(Integer buyorderGoodsId);
    //校验主机序列号是否已存在
    ResultInfo checkMasterSno(String masterSno,Integer buyorderGoodsId);
    //校验探头序列号是否已存在
    ResultInfo checkSlaveSno(String slaveSno,String masterSlavejson);
}
