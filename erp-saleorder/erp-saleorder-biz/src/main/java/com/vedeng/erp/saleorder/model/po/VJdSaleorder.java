package com.vedeng.erp.saleorder.model.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.vedeng.common.core.utils.validator.group.AddGroup;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 京东销售订单表
 */
@Data
public class VJdSaleorder implements Serializable {
    private static final long serialVersionUID = 6906719125697903013L;
    /**
     * 主键
     */
    @ExcelIgnore
    private Long saleorderId;

    //京东订单表增加客户ID字段-VDERP-17678	【应急业务】ERP京东制单支持多主体
    @ExcelIgnore
    private Integer traderId;

    @ExcelIgnore
    private Long vdSaleorderId;

    /**
     * 京东订单号
     */
    @ExcelProperty(value= "客户订单号")
    @NotNull(message = "客户订单号不可为空",groups = {AddGroup.class})
    private String jdSaleorderNo;

    /**
     * erp订单号
     */
    @ExcelIgnore
    private String saleorderNo;

    /**
     * 京东商品编码
     */
    @ExcelProperty(value= "商品编码")
    @NotNull(message = "商品编码不可为空",groups = {AddGroup.class})
    private String jdSkuNo;

    /**
     * sku编号
     */
    @ExcelIgnore
    private String skuNo;

    /**
     * 采购价
     */
    private BigDecimal purchasingPrice;

    @ExcelProperty(value= "采购价")
    @NotNull(message = "采购价不可为空",groups = {AddGroup.class})
    private String buyPrice;

    /**
     * 购买数量
     */
    private Integer num;
    @ExcelProperty(value= "购买数量")
    @NotNull(message = "购买数量不可为空",groups = {AddGroup.class})
    private String buyNum;

    /**
     * 收货人姓名
     */
    @ExcelProperty(value= "收货人姓名")
    @NotNull(message = "收货人姓名不可为空",groups = {AddGroup.class})
    private String arrivalUserName;

    /**
     * 收货人电话
     */
    @ExcelProperty(value= "收货人电话")
    @NotNull(message = "收货人电话不可为空",groups = {AddGroup.class})
    private String arrivalUserPhone;

    /**
     * 收货人地址
     */
    @ExcelProperty(value= "收货人地址")
    @NotNull(message = "收货人地址不可为空",groups = {AddGroup.class})
    private String consigneeAddress;

    /**
     * 省
     */
    @ExcelProperty(value= "省")
    private String province;

    /**
     * 市
     */
    @ExcelProperty(value= "市")
    private String city;

    /**
     * 县
     */
    @ExcelProperty(value= "县")
    private String county;


    /**
     * 订单状态
     */
    @ExcelIgnore
    @ExcelProperty(value= "订单状态")
    private String orderStatus;

    /**
     * 货期
     */
    @ExcelProperty(value= "货期")
    private String purchaseTime;


    /**
     * TERMINAL_TRADER_ID 终端客户ID
     */
    @ExcelProperty(value = "终端ID")
    private String terminalTraderId;

    /**
     * TERMINAL_TRADER_NAME 终端客户名称
     */
    @ExcelProperty(value = "终端名称")
    private String terminalTraderName;



    /**
     * erp订单生成状态，1:待生成，2：已生成，3：生成失败
     */
    @ExcelIgnore
    private Integer orderGenerateStatus;
    @ExcelIgnore
    private Integer regionId;

    /**
     * 错误原因
     */
    private String errorReason;

    /**
     * 添加人ID
     */
    @ExcelIgnore
    private Integer creator;

    /**
     * 添加人名称
     */
    @ExcelIgnore
    private String creatorName;

    /**
     * 是否删除 0否 1是
     */
    @ExcelIgnore
    private Boolean isDelete;

    /**
     * 更新人ID
     */
    @ExcelIgnore
    private Integer updater;

    /**
     * 创建时间
     */
    @ExcelIgnore
    private Date addTime;

    /**
     * 备注 -http://jira.ivedeng.com/browse/VDERP-17005
     * 应急订单导入SKU增加备注字段
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 更新备注
     */
    @ExcelIgnore
    private String updateRemark;

    /**
     * 更新人名称
     */
    @ExcelIgnore
    private String updaterName;

    /**
     * 修改时间
     */
    @ExcelIgnore
    private Date modTime;
}