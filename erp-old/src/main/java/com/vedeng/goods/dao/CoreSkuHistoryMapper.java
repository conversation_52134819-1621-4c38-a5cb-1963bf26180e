package com.vedeng.goods.dao;

import com.vedeng.goods.model.CoreSkuHistory;
import org.springframework.stereotype.Repository;

@Repository
public interface CoreSkuHistoryMapper {
    int deleteByPrimaryKey(Integer skuHistoryId);

    int insert(CoreSkuHistory record);

    int insertSelective(CoreSkuHistory record);

    CoreSkuHistory selectByPrimaryKey(Integer skuHistoryId);

    int updateByPrimaryKeySelective(CoreSkuHistory record);

    int updateByPrimaryKey(CoreSkuHistory record);

    CoreSkuHistory selectBySkuId(Integer SkuId);
}