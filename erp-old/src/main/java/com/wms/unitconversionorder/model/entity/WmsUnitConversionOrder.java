package com.wms.unitconversionorder.model.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description 单位转换表
 * @date 2023/2/14 12:26
 **/
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class WmsUnitConversionOrder extends BaseEntity {

    /**
     * 单位转换订单id
     */
    private Integer wmsUnitConversionOrderId;

    /**
     * 单位转换订单号
     */
    private String wmsUnitConversionOrderNo;

    /**
     * 申请类型 1单位转换、2主机配件、3组合产品
     */
    private Integer orderType;

    /**
     * 申请原因
     */
    private String reason;

    /**
     * 审核状态 0 待审核 1 审核中 2 审核通过 3 审核不通过
     */
    private Integer verifyStatus;

    /**
     * 部门id
     */
    private Integer orgId;

    /**
     * 部门
     */
    private String orgName;

    /**
     * 备注
     */
    private String comments;

    /**
     * 是否删除 0 否 1 是
     */
    private Integer isDelete;




}
