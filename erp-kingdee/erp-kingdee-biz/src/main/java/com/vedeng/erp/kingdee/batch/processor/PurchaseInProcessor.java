package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.BatchBuyorderDto;
import com.vedeng.erp.kingdee.batch.dto.BatchBuyorderGoodsDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInItemDto;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInItemDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchBuyorderDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchBuyorderGoodsDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchSkuSupplyAuthDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeePurchaseReceiptDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeePurchaseReceiptDto;
import com.vedeng.erp.kingdee.dto.result.KingDeePurchaseReceiptQueryResultDto;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryExtParam;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryFilterDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 采购入库 处理类
 * @date 2022/11/18 13:55
 */

@Service
@Slf4j
public class PurchaseInProcessor implements ItemProcessor<BatchWarehouseGoodsOutInDto, KingDeePurchaseReceiptDto> {

    @Autowired
    private BatchBuyorderDtoMapper buyorderDtoMapper;

    @Autowired
    private BatchBuyorderGoodsDtoMapper batchBuyorderGoodsDtoMapper;

    @Autowired
    private BatchSkuSupplyAuthDtoMapper batchSkuSupplyAuthDtoMapper;

    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;


    @Value("${kingDeeBuyorerInNotPush}")
    private String kingDeeBuyorerInNotPush;

    public static final String SPECIAL_INVOICE = "专用发票";


    @Override
    public KingDeePurchaseReceiptDto process(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto) throws Exception {



        log.info("PurchaseInProcessorService.process：" + JSON.toJSONString(batchWarehouseGoodsOutInDto));
        log.info("查询是否推送金蝶入库单");
        KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
        queryParam.setFormId(KingDeeFormConstant.STK_INSTOCK);
        List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
        queryFilterDtos.add(KingDeeQueryFilterDto.builder().fieldName("fBillNo").value(batchWarehouseGoodsOutInDto.getOutInNo()).build());
        queryParam.setFilterString(queryFilterDtos);
        List<KingDeePurchaseReceiptQueryResultDto> query = kingDeeBaseApi.query(queryParam, KingDeePurchaseReceiptQueryResultDto.class);
        log.info("采购入库单查询金蝶结果：{}",JSON.toJSONString(query));
        if (CollUtil.isNotEmpty(query)) {
            log.info("已推送金蝶数据，无需推送");
            return null;
        }
        // 查询供营商 信息 采购单
        BatchBuyorderDto batchBuyorderDto = buyorderDtoMapper.selectByBuyorderNo(batchWarehouseGoodsOutInDto.getRelateNo());

        boolean special;
        if (StrUtil.isNotEmpty(batchWarehouseGoodsOutInDto.getInvoiceTypeName()) && batchWarehouseGoodsOutInDto.getInvoiceTypeName().contains(SPECIAL_INVOICE)) {
            special = true;
            // 过滤未认证的专票
        } else {
            special = false;
        }
        if (Objects.isNull(batchBuyorderDto)) {
            throw new KingDeeException("未查到此采购单"+batchWarehouseGoodsOutInDto.getRelateNo()+"信息");
        }
        if (StrUtil.isNotEmpty(kingDeeBuyorerInNotPush)) {
            List<Integer> traderIds = JSON.parseArray(kingDeeBuyorerInNotPush, Integer.class);
            if (CollUtil.isNotEmpty(traderIds)) {
                boolean contains = traderIds.contains(batchBuyorderDto.getTraderId());
                if (contains) {
                    log.info("采购入库单：{}，符合不推送金蝶条件，{}",batchWarehouseGoodsOutInDto.getOutInNo(),kingDeeBuyorerInNotPush);
                    return null;
                }
            }
        }
        List<BatchBuyorderGoodsDto> batchBuyorderGoodsDtos = batchBuyorderGoodsDtoMapper.selectByBuyorderIdNotDelete(batchBuyorderDto.getBuyorderId());
        if (CollUtil.isEmpty(batchBuyorderGoodsDtos)) {
            throw new KingDeeException("采购单未查询到商品信息");
        }
        String isDirect  = batchBuyorderDto.getDeliveryDirect().equals(0)?"否":"是";

        DecimalFormat decimalFormat = new DecimalFormat("0.00#");
        String taxRate = StrUtil.isEmpty(batchWarehouseGoodsOutInDto.getRate()) ? "0.00" : special ? decimalFormat.format(new BigDecimal(batchWarehouseGoodsOutInDto.getRate()).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP)) : "0.00";

        Map<Integer, BatchBuyorderGoodsDto> batchBuyorderGoods2Map = batchBuyorderGoodsDtos.stream().collect(Collectors.toMap(BatchBuyorderGoodsDto::getBuyorderGoodsId, c -> c, (k1, k2) -> k1));
        KingDeePurchaseReceiptDto dto = new KingDeePurchaseReceiptDto();
        dto.setFId("0");
        dto.setFBillTypeId("RKD01_SYS");
        dto.setFBillNo(batchWarehouseGoodsOutInDto.getOutInNo());
        dto.setFQzokBddjtId(batchWarehouseGoodsOutInDto.getWarehouseGoodsOutInId().toString());
        dto.setFDate(DateUtil.formatDate(batchWarehouseGoodsOutInDto.getOutInTime()));
        dto.setFStockOrgId(KingDeeConstant.ORG_ID.toString());
        dto.setFSupplierId(batchBuyorderDto.getTraderSupplierId().toString());

        List<KingDeePurchaseReceiptDetailDto>  FEntityDetail = new ArrayList<>();
        dto.setFInStockEntry(FEntityDetail);

        List<BatchWarehouseGoodsOutInItemDto> byOutInNo = batchWarehouseGoodsOutInItemDtoMapper.findByOutInNo(batchWarehouseGoodsOutInDto.getOutInNo());
        if (CollUtil.isEmpty(byOutInNo)) {
            log.warn("未能查到入库单子单信息");
            return null;
        }
        batchWarehouseGoodsOutInDto.setBatchWarehouseGoodsOutInItemDtos(byOutInNo);
        batchWarehouseGoodsOutInDto.getBatchWarehouseGoodsOutInItemDtos().forEach(l->{
            KingDeePurchaseReceiptDetailDto detailDto = new KingDeePurchaseReceiptDetailDto();
            //物料
            detailDto.setFRealQty(l.getNum().toString());
            BatchBuyorderGoodsDto batchBuyorderGoodsDto = batchBuyorderGoods2Map.get(l.getRelatedId());
            if (Objects.nonNull(batchBuyorderGoodsDto)) {
                // 授权类型
                String authType = batchSkuSupplyAuthDtoMapper.findByTraderSupplyIdAndValidStartTimeBeforeAndValidEndTimeAfter(batchBuyorderDto.getTraderSupplierId(), batchBuyorderDto.getValidTime(), batchBuyorderGoodsDto.getSku());
                detailDto.setFQzokSqlx(StrUtil.isEmpty(authType)?"":authType);
                detailDto.setFTaxPrice(batchBuyorderGoodsDto.getPrice().toString());
                detailDto.setFMaterialId(batchBuyorderGoodsDto.getSku());
            } else {
                log.error("未查到当前入库原始商品信息:{}",JSON.toJSONString(l));
                throw new KingDeeException("未查到当前入库原始商品信息");
            }
            // 税率
            detailDto.setFEntryTaxRate(taxRate);
            // 默认
            detailDto.setFStockId("ck9999");

            // 原始订单号 入库单关联的采购单单号
            detailDto.setFQzokYsddh(batchBuyorderDto.getBuyorderNo());
            // 归属业务单号 入库单关联的采购单单号（预留字段，如果将来做了拆单，就传拆单的任务单号
            detailDto.setFQzokGsywdh(batchBuyorderDto.getBuyorderNo());
            detailDto.setFQzokYwlx("采购订单");
            detailDto.setFQzokPch(l.getBatchNumber());
            detailDto.setFQzokXlh(l.getBarcodeFactory());
            // 是否直发
            detailDto.setFQzokSfzf(isDirect);
            // 行id
            detailDto.setFQzokBddjhId(l.getWarehouseGoodsOutInDetailId().toString());
            FEntityDetail.add(detailDto);
        });



        return dto;
    }

}
