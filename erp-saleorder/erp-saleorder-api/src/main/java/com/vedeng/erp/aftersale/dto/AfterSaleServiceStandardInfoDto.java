package com.vedeng.erp.aftersale.dto;


import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description 售后政策DTO
 * @Date 2022/1/10 19:49
 */
@Data
public class AfterSaleServiceStandardInfoDto {

    /**
     * 是否提供上门安装服务 是否提供上门安装服务 安装类型:0-收费安装 1-免费安装 2-不可安装
     */
    private Integer installPolicyInstallType;

    /**
     * 是否保修 1-是 0-否
     */
    private Integer guaranteePolicyIsGuarantee;

    /**
     * 是否支持退货 1-是 0-否
     */
    private Integer returnPolicySupportReturn;

    /**
     * 是否免费远程指导装机 1-是 0-否
     */
    private Integer installPolicyFreeRemoteInstall;

    /**
     * 安装区域
     */
    private String  provinceCityJsonvalue;

    /**
     *贝登服务标准表ID
     */
    private Long serviceStandardInfoId;

    /**
     * 安装费
     */
    private BigDecimal installPolicyInstallFee;

}