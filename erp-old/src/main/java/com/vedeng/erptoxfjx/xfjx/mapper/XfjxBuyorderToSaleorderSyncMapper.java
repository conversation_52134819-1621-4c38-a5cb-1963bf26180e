package com.vedeng.erptoxfjx.xfjx.mapper;

import com.vedeng.erptoxfjx.xfjx.domain.XfjxBuyorderToSaleorderSync;
import com.vedeng.erptoxfjx.xfjx.domain.XfjxBuyorderToSaleorderSyncExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface XfjxBuyorderToSaleorderSyncMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_buyorder_to_saleorder_sync
     *
     * @mbg.generated
     */
    long countByExample(XfjxBuyorderToSaleorderSyncExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_buyorder_to_saleorder_sync
     *
     * @mbg.generated
     */
    int deleteByExample(XfjxBuyorderToSaleorderSyncExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_buyorder_to_saleorder_sync
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_buyorder_to_saleorder_sync
     *
     * @mbg.generated
     */
    int insert(XfjxBuyorderToSaleorderSync record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_buyorder_to_saleorder_sync
     *
     * @mbg.generated
     */
    int insertSelective(XfjxBuyorderToSaleorderSync record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_buyorder_to_saleorder_sync
     *
     * @mbg.generated
     */
    List<XfjxBuyorderToSaleorderSync> selectByExample(XfjxBuyorderToSaleorderSyncExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_buyorder_to_saleorder_sync
     *
     * @mbg.generated
     */
    XfjxBuyorderToSaleorderSync selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_buyorder_to_saleorder_sync
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") XfjxBuyorderToSaleorderSync record, @Param("example") XfjxBuyorderToSaleorderSyncExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_buyorder_to_saleorder_sync
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") XfjxBuyorderToSaleorderSync record, @Param("example") XfjxBuyorderToSaleorderSyncExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_buyorder_to_saleorder_sync
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(XfjxBuyorderToSaleorderSync record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_buyorder_to_saleorder_sync
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(XfjxBuyorderToSaleorderSync record);
}