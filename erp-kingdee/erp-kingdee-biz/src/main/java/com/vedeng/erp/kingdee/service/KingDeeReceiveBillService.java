package com.vedeng.erp.kingdee.service;


import com.vedeng.erp.kingdee.enums.KingDeeBankBillFormIdAndContactUnitTypeEnum;
import com.vedeng.infrastructure.kingdee.service.KingDeeBaseService;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveBillDto;

public interface KingDeeReceiveBillService extends KingDeeBaseService<KingDeeReceiveBillDto> {

    /**
     * 根据bankBillId查询对应的往来单位类型及金蝶FormId
     * @param bankBillId
     * @return
     */
    KingDeeBankBillFormIdAndContactUnitTypeEnum selectKingDeeBankBillEnumById(Integer bankBillId);
}
