<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchRollbackInvoiceDtoMapper">
    <select id="findByAll" resultType="com.vedeng.erp.kingdee.batch.dto.BatchRollbackInvoiceDto">
        SELECT T2.INVOICE_ID,
               T2.TYPE,
               T2.INVOICE_TYPE
        FROM T_HX_INVOICE T1
                 INNER JOIN T_INVOICE T2 ON T1.INVOICE_NUM = T2.INVOICE_NO AND T1.INVOICE_CODE = T2.INVOICE_CODE
                 LEFT JOIN T_INVOICE_VOUCHER T3 ON T2.INVOICE_ID = T3.INVOICE_ID
            AND T2.COLOR_TYPE = 2 AND T2.IS_ENABLE = 1
        WHERE T1.COLOR_TYPE = 3
          AND T2.TYPE IN (503, 4126)
          AND T2.VALID_STATUS = 1
          AND T2.COMPANY_ID = 1
          AND T3.INVOICE_VOUCHER_ID IS NOT NULL
          AND (T3.VOUCHER_NO IS NULL OR T3.VOUCHER_NO = '')
        <if test="beginTime != null">
            AND T1.ADD_TIME <![CDATA[>=]]> #{beginTime,jdbcType=BIGINT}
        </if>
        <if test="endTime != null">
            AND T1.ADD_TIME <![CDATA[<=]]> #{endTime,jdbcType=BIGINT}
        </if>
    </select>

    <select id="getExpensePlainInvoiceFid" resultType="java.lang.String">
        SELECT FID
        FROM KING_DEE_IN_PUT_FEE_PLAIN_INVOICE_ENTITY
        WHERE F_QZOK_BDDJTID = #{invoiceIdStr,jdbcType=VARCHAR}
        GROUP BY F_QZOK_BDDJTID
    </select>

    <select id="getExpenseSpecialInvoiceFid" resultType="java.lang.String">
        SELECT FID
        FROM KING_DEE_IN_PUT_FEE_SPECIAL_INVOICE_ENTITY
        WHERE F_QZOK_BDDJTID = #{invoiceIdStr,jdbcType=VARCHAR}
        GROUP BY F_QZOK_BDDJTID
    </select>


    <select id="getPayCommonIdByInvoiceFid" resultType="java.lang.String">
        SELECT F_ID
        FROM KING_DEE_PAY_COMMON
        WHERE F_QZOK_BDDJT_ID = #{invoiceIdStr,jdbcType=VARCHAR}
        GROUP BY F_QZOK_BDDJT_ID
    </select>

    <select id="getBuyOrderPlainInvoiceFid" resultType="java.lang.String">
        SELECT FID
        FROM KING_DEE_PURCHASE_VAT_PLAIN_INVOICE
        WHERE F_QZOK_BDDJTID = #{invoiceIdStr,jdbcType=VARCHAR}
        GROUP BY F_QZOK_BDDJTID
    </select>

    <select id="getBuyOrderSpecialInvoiceFid" resultType="java.lang.String">
        SELECT FID
        FROM KING_DEE_PURCHASE_VAT_SPECIAL_INVOICE
        WHERE F_QZOK_BDDJTID = #{invoiceIdStr,jdbcType=VARCHAR}
        GROUP BY F_QZOK_BDDJTID
    </select>

    <select id="getExpensePayCommonIdByInvoiceFid" resultType="java.lang.String">
        SELECT F_ID
        FROM KING_DEE_PAY_EXPENSES
        WHERE F_QZOK_BDDJT_ID = #{invoiceIdStr,jdbcType=VARCHAR}
        GROUP BY F_QZOK_BDDJT_ID
    </select>

    <select id="findAfterSaleDeprecateBlueInvoice"
            resultType="com.vedeng.erp.kingdee.batch.dto.BatchRollbackInvoiceDto">
        <!--T1是蓝字有效票，T2是蓝字作废票-->
        SELECT T1.INVOICE_ID, T1.INVOICE_TYPE, T1.TYPE
        FROM T_INVOICE T1
                 LEFT JOIN T_INVOICE T2 ON T1.INVOICE_NO = T2.INVOICE_NO AND T1.INVOICE_CODE = T2.INVOICE_CODE AND
                                           T1.IS_ENABLE = 1 AND T2.IS_ENABLE = 0
                 LEFT JOIN T_INVOICE_APPLY T3 ON T1.INVOICE_APPLY_ID = T3.INVOICE_APPLY_ID
                 LEFT JOIN T_INVOICE_VOUCHER T4 ON T4.INVOICE_ID = T2.INVOICE_ID
        WHERE T1.COLOR_TYPE = 2
          AND T2.COLOR_TYPE = 2
          AND T1.TYPE = 505
          AND T1.VALID_STATUS = 1
          AND T1.COMPANY_ID = 1
          AND T3.IS_ADVANCE = 0
          AND T4.VOUCHER_NO IS NULL
        <if test="beginTime != null">
            AND T2.ADD_TIME <![CDATA[>=]]> #{beginTime,jdbcType=BIGINT}
        </if>
        <if test="endTime != null">
            AND T2.ADD_TIME <![CDATA[<=]]> #{endTime,jdbcType=BIGINT}
        </if>
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="getSaleOrderSpecialInvoiceFid" resultType="java.lang.String">
        SELECT FID
        FROM  KING_DEE_SALES_VAT_SPECIAL_INVOICE
        WHERE F_QZOK_BDDJTID = #{invoiceIdStr,jdbcType=VARCHAR}
        GROUP BY F_QZOK_BDDJTID
    </select>

    <select id="getSaleOrderPlainInvoiceFid" resultType="java.lang.String">
        SELECT FID
        FROM  KING_DEE_SALES_VAT_PLAIN_INVOICE
        WHERE F_QZOK_BDDJTID = #{invoiceIdStr,jdbcType=VARCHAR}
        GROUP BY F_QZOK_BDDJTID
    </select>

    <select id="getReceiveCommonIdByInvoiceIdStr" resultType="java.lang.String">
        SELECT F_ID
        FROM KING_DEE_RECEIVE_COMMON
        WHERE F_QZOK_BDDJT_ID = #{invoiceIdStr,jdbcType=VARCHAR}
        GROUP BY F_QZOK_BDDJT_ID
    </select>

    <select id="getReceiveFeeIdByInvoiceIdStr" resultType="java.lang.String">
        SELECT FID
        FROM KING_DEE_RECEIVE_FEE
        WHERE F_QZOK_BDDJTID = #{invoiceIdStr,jdbcType=VARCHAR}
        GROUP BY F_QZOK_BDDJTID
    </select>

    <select id="getIsVirtualGoodSetByInvoiceId" resultType="java.lang.Integer">
        SELECT CS.IS_VIRTURE_SKU
        FROM T_INVOICE TI
                 LEFT JOIN T_INVOICE_DETAIL TID ON TI.INVOICE_ID = TID.INVOICE_ID
                 LEFT JOIN T_SALEORDER_GOODS SG ON TID.DETAILGOODS_ID = SG.SALEORDER_GOODS_ID
                 LEFT JOIN V_CORE_SKU CS ON CS.SKU_NO = SG.SKU
        WHERE TI.INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
    </select>

    <select id="getSaleOrderFeeSpecialInvoiceFid" resultType="java.lang.String">
        SELECT FID
        FROM KING_DEE_OUT_PUT_FEE_SPECIAL_INVOICE
        WHERE F_QZOK_BDDJTID = #{invoiceIdStr,jdbcType=VARCHAR}
        GROUP BY F_QZOK_BDDJTID
    </select>

    <select id="getSaleOrderFeePlainInvoiceFid" resultType="java.lang.String">
        SELECT FID
        FROM KING_DEE_OUT_PUT_FEE_PLAIN_INVOICE
        WHERE F_QZOK_BDDJTID = #{invoiceIdStr,jdbcType=VARCHAR}
        GROUP BY F_QZOK_BDDJTID
    </select>

    <select id="findVirtualInvoiceDisable" resultType="com.vedeng.erp.kingdee.batch.dto.BatchRollbackInvoiceDto">
        SELECT T4.VIRTUAL_INVOICE_ID,
        T4.INVOICE_TYPE,T4.UUID
        FROM T_HX_INVOICE T1
        INNER JOIN T_INVOICE T2 ON T1.INVOICE_NUM = T2.INVOICE_NO AND T1.INVOICE_CODE = T2.INVOICE_CODE
        left join T_VIRTUAL_INVOICE T4 on T2.INVOICE_ID = T4.SOURCE_INVOICE_ID
        LEFT JOIN T_INVOICE_VOUCHER T3 ON T4.UUID = T3.INVOICE_ID
        AND T2.COLOR_TYPE = 2 AND T2.IS_ENABLE = 1
        WHERE T1.COLOR_TYPE = 3
        AND T2.TYPE = 503
        AND T2.VALID_STATUS = 1
        AND T2.COMPANY_ID = 1
        AND T3.INVOICE_VOUCHER_ID IS NOT NULL
        AND (T3.VOUCHER_NO IS NULL OR T3.VOUCHER_NO = '')
        <if test="beginTime != null">
            AND T1.ADD_TIME <![CDATA[>=]]> #{beginTime,jdbcType=BIGINT}
        </if>
        <if test="endTime != null">
            AND T1.ADD_TIME <![CDATA[<=]]> #{endTime,jdbcType=BIGINT}
        </if>
    </select>
</mapper>