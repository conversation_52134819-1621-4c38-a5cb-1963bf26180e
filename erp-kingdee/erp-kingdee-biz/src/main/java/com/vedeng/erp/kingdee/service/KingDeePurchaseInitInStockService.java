package com.vedeng.erp.kingdee.service;

import com.vedeng.erp.kingdee.dto.result.KingDeePurchaseInitReceiptQueryResultDto;

import java.util.List;

/**
 * <AUTHOR>
 * @description 金蝶期初采购入库单
 * @date 2023/3/7 16:12
 **/
public interface KingDeePurchaseInitInStockService  {


    /**
     * 采购入库
     * @param outInNo
     * @return
     */
    List<KingDeePurchaseInitReceiptQueryResultDto> getKingDeePurchaseInitInStock(String outInNo);
}
