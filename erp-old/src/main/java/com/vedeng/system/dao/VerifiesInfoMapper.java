package com.vedeng.system.dao;

import java.math.BigInteger;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.inject.Named;

import com.vedeng.system.model.ActHiTaskInfoDo;
import io.swagger.models.auth.In;
import org.apache.ibatis.annotations.Param;

import com.vedeng.system.model.VerifiesInfo;


@Named("verifiesInfoMapper")
public interface VerifiesInfoMapper {
    int deleteByPrimaryKey(Integer verifiesInfoId);

    int insert(VerifiesInfo record);

    int insertSelective(VerifiesInfo record);

    VerifiesInfo selectByPrimaryKey(Integer verifiesInfoId);

    int updateByPrimaryKeySelective(VerifiesInfo record);

    int updateByPrimaryKey(VerifiesInfo record);
    
    List<VerifiesInfo> getVerifiesInfo(VerifiesInfo verifiesInfo);
    
    /**
     * <b>Description:</b><br> 查询付款记录的关联审核状态
     * @param verifiesInfo
     * @return
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2018年1月4日 下午3:34:57
     */
    VerifiesInfo getVerifiesInfoByParam(VerifiesInfo verifiesInfo);

    Integer updateInfo(@Param("tableName")String tableName,@Param("id") String id,@Param("idValue") Integer idValue, @Param("key")String key, @Param("value")Integer value);

    Integer updateInfoTime(@Param("tableName")String tableName,@Param("id") String id,@Param("idValue") Integer idValue, @Param("key")String key, @Param("value")Integer value,@Param("validTime")long validTime, @Param("modTime")long modTime);
    
    Integer updateVerifyInfo(@Param("tableName")String tableName,@Param("idValue") Integer idValue,@Param("value")Integer value);
    
    int deleteVerifiesInfo(@Param("relateTableKey")Integer relateTableKey,@Param("relateTable")String relateTable);


    List<Integer> getBuyorderListUnVerified(Map<String, Object> map);


    List<VerifiesInfo> batchSelectVerifiesInfoByRelatedTableAndKeyAndType(@Param("relatedIdList") List<Integer> relatedIdList,
                                                                          @Param("relatedTable") String relatedTable, @Param("relatedType") Integer relatedType);


    /**
     * 待用户审核的审核记录
     * @param relateTable 表
     * @param username 用户
     * @return 审核记录
     */
    List<VerifiesInfo> getVerifiesInfoWaitUserCheck(@Param("relateTable") String relateTable, @Param("username") String username,
                                                    @Param("isNeedUserCheck") Boolean isNeedUserCheck);

    /**
     * 获取历史供应商审核的资料
     * @return
     */
    List<VerifiesInfo> getHistorySupplierVerufyInfo(Integer type);

    Integer getLastHisActivityInfo(String businessKey);

    List<ActHiTaskInfoDo> getHisTaskInfo(Integer insId);

    List<VerifiesInfo> getHistoryCustomerVerifyInfoByTableName(String tableName);

    Integer getSaleorderVerifyInfo(Integer saleorderId);

    Integer getOrderVerifyInfo(@Param("relateTable") String relateTable, @Param("verifiesType") Integer verifiesType, @Param("relateTableKey") Integer relateTableKey);

    Integer getPurchaseContractVerify(@Param("buyorderId") Integer buyorderId,@Param("addTime") Long addTime);

    int updatePurchaseContractStatus(@Param("addTime") Long addTime, @Param("status") Integer status);

}