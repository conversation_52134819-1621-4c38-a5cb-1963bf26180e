package com.vedeng.erp.settlement.manager;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.enums.BusinessSourceTypeEnum;
import com.vedeng.common.core.enums.SettlementTypeEnum;
import com.vedeng.common.core.enums.SupplierAssetEnum;
import com.vedeng.common.core.enums.TraderTypeEnum;
import com.vedeng.erp.aftersale.dto.AfterSalesApiDto;
import com.vedeng.erp.aftersale.dto.AfterSalesGoodsDto;
import com.vedeng.erp.aftersale.service.BuyorderAfterSalesApiService;
import com.vedeng.erp.finance.cmd.SettlementBillCreateCmd;
import com.vedeng.erp.finance.cmd.SettlementBillQueryCmd;
import com.vedeng.erp.finance.dto.CapitalBillDetailDto;
import com.vedeng.erp.finance.dto.CapitalBillDto;
import com.vedeng.erp.settlement.domain.context.SettlementBillSettleContext;
import com.vedeng.erp.settlement.domain.dto.SettlementBillDto;
import com.vedeng.erp.settlement.domain.dto.SettlementBillItemDto;
import com.vedeng.erp.settlement.enums.CapitalBusinessTypeEnum;
import com.vedeng.erp.settlement.enums.CapitalOrderTypeEnum;
import com.vedeng.erp.settlement.enums.CapitalTraderModeEnum;
import com.vedeng.erp.settlement.service.SettlementBillService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购售后单
 * @date 2023/12/7 17:41
 */
@Component
@Slf4j
public class BuyOrderAfterSaleSettlementHandle extends AbstractSettlementHandle {

    @Autowired
    private BuyorderAfterSalesApiService buyorderAfterSalesApiService;
    @Autowired
    private SettlementBillService settlementBillService;



    @Override
    protected SettlementBillDto preCreate(SettlementBillCreateCmd settlementBillCreateCmd) {
        log.info("开始创建售后结算单：{}",JSON.toJSONString(settlementBillCreateCmd));
        Integer afterSaleId = settlementBillCreateCmd.getBusinessSourceId();
        AfterSalesApiDto afterSalesApiDto = buyorderAfterSalesApiService.getAfterSalesByAfterSalesId(afterSaleId);
        Integer buyOrderId = afterSalesApiDto.getOrderId();
        String afterSalesNo = afterSalesApiDto.getAfterSalesNo();
        // 获取采购单返利支付结算单
        SettlementBillQueryCmd cmd = new SettlementBillQueryCmd(buyOrderId, BusinessSourceTypeEnum.buyOrder);
        SettlementBillDto buyOrderSettlementBill = settlementBillService.getSettlementByBusiness(cmd);
        List<SettlementBillItemDto> buyOrderSettlementBillItem = buyOrderSettlementBill.getSettlementBillItemDtoList();
        // 采购单返利支付结算单明细
        Map<Integer, SettlementBillItemDto> buyOrderSettlementItemMap = buyOrderSettlementBillItem.stream()
                .filter(e -> SettlementTypeEnum.REBATE_PAY.getSupplierAssetEnum().getCode().equals(e.getSettlementType()))
                .collect(Collectors.toMap(SettlementBillItemDto::getBusinessItemId, Function.identity(), (key1, key2) -> key1));

        List<AfterSalesGoodsDto> afterSalesGoodsList = buyorderAfterSalesApiService.getAfterSalesGoodsByAfterSalesId(afterSaleId);
        List<SettlementBillItemDto> settlementBillItemDtoList = new ArrayList<>();
        for (AfterSalesGoodsDto afterSalesGoods : afterSalesGoodsList) {
            Integer orderDetailId = afterSalesGoods.getOrderDetailId();
            // 匹配返利支付结算单明细
            SettlementBillItemDto buyOrderSettlementItem = buyOrderSettlementItemMap.get(orderDetailId);
            if (Objects.isNull(buyOrderSettlementItem)){
                continue;
            }
            // 返利支付时结算单价
            BigDecimal price = buyOrderSettlementItem.getPrice();
            // 售后数量
            BigDecimal number = BigDecimal.valueOf(afterSalesGoods.getNum());

            SettlementBillItemDto settlementBillItemDto = new SettlementBillItemDto();
            settlementBillItemDto.setBusinessType(settlementBillCreateCmd.getSourceTypeEnum().getCode());
            settlementBillItemDto.setBusinessNo(afterSalesNo);
            settlementBillItemDto.setBusinessId(afterSaleId);
            settlementBillItemDto.setSettlementType(SupplierAssetEnum.rebate.getCode());
            settlementBillItemDto.setTraderDirection(TraderTypeEnum.rollIn.getType());
            settlementBillItemDto.setBusinessItemId(afterSalesGoods.getAfterSalesGoodsId());
            settlementBillItemDto.setProductName(afterSalesGoods.getGoodsName());
            settlementBillItemDto.setPrice(price);
            settlementBillItemDto.setNumber(number);
            settlementBillItemDto.setAmount(price.multiply(number));
            settlementBillItemDtoList.add(settlementBillItemDto);
        }
        // 计算售后返利结算总额
        BigDecimal afterSalesSettleAmount = settlementBillItemDtoList.stream().map(e-> e.getPrice().multiply(e.getNumber())).reduce(BigDecimal.ZERO, BigDecimal::add);
        log.info("计算售后返利结算总额:{}",afterSalesSettleAmount);
        // 组装售后结算明细
        SettlementBillDto settlementBillDto = new SettlementBillDto();
        settlementBillDto.setTraderId(buyOrderSettlementBill.getTraderId());
        settlementBillDto.setTraderSubjectType(ErpConstant.TWO);
        settlementBillDto.setTraderSubjectId(buyOrderSettlementBill.getTraderSubjectId());
        settlementBillDto.setSourceType(settlementBillCreateCmd.getSourceTypeEnum().getCode());
        settlementBillDto.setSettleAmount(afterSalesSettleAmount);
        settlementBillDto.setAlreadySettleAmount(BigDecimal.ZERO);
        settlementBillDto.setAccountPeriod(ErpConstant.ZERO);
        settlementBillDto.setAccountPeriodAmount(BigDecimal.ZERO);
        settlementBillDto.setAlreadyRepaidAmount(BigDecimal.ZERO);
        settlementBillDto.setBusinessSourceTypeId(afterSaleId);
        settlementBillDto.setBusinessSourceTypeNo(afterSalesNo);
        settlementBillDto.setSettlementBillItemDtoList(settlementBillItemDtoList);
        log.info("组装售后结算单：{}", JSON.toJSONString(settlementBillDto));
        return settlementBillDto;
    }

    @Override
    protected void postCreate(SettlementBillDto settlementBillDto) {

    }

    @Override
    protected BigDecimal buildPayAmount(SettlementBillSettleContext context) {
        return context.getSettlementBillDto().getSettleAmount();
    }

    @Override
    protected void postSettlement(SettlementBillSettleContext context) {

    }

    @Override
    protected void buildCapitalBill(SettlementBillSettleContext context) {
        CapitalBillDto capitalBillDto = context.getCapitalBillDto();
        capitalBillDto.setTraderMode(CapitalTraderModeEnum.REBATE.getCode());
        CapitalBillDetailDto capitalBillDetailDto = capitalBillDto.getCapitalBillDetailDto();
        capitalBillDetailDto.setBussinessType(CapitalBusinessTypeEnum.ORDER_REFUND.getCode());
        capitalBillDetailDto.setOrderType(CapitalOrderTypeEnum.AFTER_SALE_ORDER.getCode());
    }

}
