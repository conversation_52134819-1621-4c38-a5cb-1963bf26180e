var keyValue=1000
var newData;
var fileImg=[];
$(function() {
    $(".mybtn").click(function(){
        var id = $(this).attr('id');
        var buyOrderId = $("#buyOrderId").val();
        if(id == "sbu-mit"){
            var expressIds=[]
            $("input:checkbox[name=checkexpress]:checked").each(function () {
                expressIds.push($(this).val())
            });
            if (expressIds.length=== 0) {
                layer.alert('未勾选快递信息', {icon: 2});
                return;
            }
            var list = [];
            var b1 = false;
            for (i=0;i<expressIds.length;i++){
                // jq 转dom对象
                var $1 = $("div[lay-id='table-" + expressIds[i] + "']").find("tbody:last").get(0);
                var length = $1.childElementCount;
                for (let j = 0; j < length; j++) {
                    var childElementCount = $1.children[j].childElementCount;
                    var purchaseDetail = new PurchaseDetail($1.children[j]);
                    // 单独校验
                    b1 = checkPurchaseDetail(purchaseDetail);
                    if (!b1) {
                        return;
                    }
                    console.log(purchaseDetail);
                    list.push(purchaseDetail)
                }
            }
            if (!b1) {
                return;
            }
            console.log(list);
            var b = checkData(list);
            if (!b) {
                return;
            }
            // 开启保存
            savePurchaseData(list,buyOrderId);
        }
    });
})

function uploadImg(obj){

    layer.open({
        type:2
        ,title: '上传同行单'
        ,area: ["500px", "360px"]
        ,content: "/order/newBuyorderPeerList/uploadImgView.do"
        // ,btn: ['取消', '提交']
        /*,yes: function(index, layero){
            // fileImg = $(layero).find("iframe")[0].contentWindow.getCheckData();
            // console.log(fileImg);
            layer.close(index);
        }
        ,btn2:function (index,layero){
            fileImg = $(layero).find("iframe")[0].contentWindow.getCheckData();
            console.log(fileImg);
            layer.close(index);
        }*/
        ,cancel: function(index,layero){
            /*fileImg = $(layero).find("iframe")[0].contentWindow.getCheckData();
            console.log(fileImg);*/
            layer.close(index);
            /*if (spareCost.data != null && spareCost.data != undefined && spareCost.data != []) {
                // newData = spareCost
                // reloadData(obj)
            }*/

        }
    });

}
function setFileUrls(obj){
    fileImg=obj;
    console.log(fileImg)
}

function downloadExcel(expressId, buyorderId) {

}
function uploadExcel(obj,buyorderId){

    layer.open({
        type:2
        ,title: '导入数据'
        ,area: ["500px", "360px"]
        ,content: "/order/newBuyorderPeerList/uploadExcelView2.do?expressId="+obj+"&buyOrderId="+buyorderId
        ,cancel: function(index,layero){
            // var spareCost = $(layero).find("iframe")[0].contentWindow.getCheckData();
            // console.log(spareCost);
            layer.close(index);
            /*if (spareCost.data != null && spareCost.data != undefined && spareCost.data != []) {
                newData = spareCost
                reloadData(obj,newData.data)
            }*/

        }
    });

}
function reloadData(obj,datas) {
    var table = layui.table;
    console.log(datas)
    if (datas != null && datas != undefined && datas != []) {
        table.render({
            elem: '#table-'+obj //指定原始表格元素选择器（推荐id选择器）
            // ,method: 'post'
            , data: datas
            // ,where :{expressId:}
            ,limit:datas.length
            ,cols: [[
                {field:'goodsName',style:'font-size: 10px;' ,width:200,align:'center',title:'产品名',unresize:true},
                {field:'sku',align:'center',width:100,style:'font-size: 10px;',title:'订货号',unresize:true},
                {field:'model',align:'center',width:100,style:'font-size: 10px;',title:'型号/规格',unresize:true},
                {field:'unitName',style:'font-size: 10px;',align:'center',width:60,title:'单位',unresize:true},
                {field:'manufacturerName',align:'center',width:150,style:'font-size: 10px;',title:'品牌（生产企业）',unresize:true},
                {field:'productCompanyLicence',align:'center',style:'font-size: 10px;',title:'许可证号',unresize:true},
                {field:'registrationNumber',align:'center',style:'font-size: 10px;',title:'注册证号',unresize:true},
                {field:'',align:'center',width:180,templet:'#pcEdit',title:'生产批号/序列号',unresize:true},
                {field:'',align:'center',templet:'#dnumEditExcel',title:'收货数量/可收货数量',unresize:true},
                {field:'',align:'center',width:130,templet:'#productionTime',title:'生产日期',unresize:true},
                {field:'',align:'center',width:130,templet:'#productionTime',title:'失效日期',unresize:true},
                {field:'expressDetailId',align:'center',hide:true},
                {field:'spuType',align:'center',hide:true},
                {fixed: '', width:150, align:'center', toolbar: '#barDemoExcel',title:'操作',unresize:true}
            ]] //设置表头
            ,text:{none:'此物流信息下无还需维护同行单数据'}
            ,done: function (res,curr,count){
                if (count == 0) {
                    $("#btn-"+value).addClass("layui-btn-disabled").attr("disabled",true).removeAttr('onclick');
                }
            }
        });
    }

}
function savePurchaseData(list,buyOrderId){
    debugger
    $.ajax({
        async:false,
        url: '/order/newBuyorderPeerList/saveDetails.do',
        data:JSON.stringify({
            "list":list,
            "buyorderId":buyOrderId,
            "fileUrls":fileImg
        }),
        type:"POST",
        dataType : "json",
        contentType:'application/json',
        success:function(data){
            console.log(data);
            // layer.close(load);
            if (data.data.code == 200) {
                layer.msg('保存成功', {
                    icon: 1,
                    time: 2000 //2秒关闭（如果不配置，默认是3秒）
                }, function(){
                    //do something
                    window.parent.closableTab.close($(window.parent.document).find('[role=presentation].active .glyphicon.small'),window.parent.closableTab.resizeMove);
                });

            } else {
                if (data.code==-1) {
                    layer.alert(data.message);
                }
                if (data.data.code == 101) {
                    layer.alert("生产批次号/序列号存在空值");
                }  else
                if (data.data.code == 102) {
                    layer.alert("生产批次号/序列号存在重复："+data.data.msg);
                }else
                if (data.data.code == 1) {
                    layer.alert(data.data.msg);
                }else
                if (data.data.code == 2) {
                    layer.alert(data.data.msg+"的收货数量超出可填写的收货数量");
                }else
                if (data.data.code == 90) {
                    layer.alert(data.data.msg+"的生产批号/序列号不可为空");
                }else
                if (data.data.code == 91) {
                    layer.alert(data.data.msg+"的收货数量必填");
                }else if (data.data.code == 92) {
                    layer.alert(data.data.msg + "的生效日期、失效日期必填");
                } else {
                    layer.alert("服务器错误，请联系研发部");
                }
            }

            // window.parent.search();
        },
        error:function(data){
            if (data.code==-1) {
                layer.alert(data.message);
            }
            if(data.status == 1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
            }
            if(data.status == 400){
                layer.alert("操作失败");
            }
            if(data.status == 500){
                layer.alert("操作失败");
            }
        }
    });
}

function pcChange(obj){
    var pc = $(obj).val();
    var s = pc.trim();
    if (pc.length > s.length) {
        layer.alert("生产批次号/序列号不可带空格",{icon: 2})
    }
    if (s.length>50) {
        layer.alert("生产批次号/序列号 长度不可超过50",{icon: 2})
    }
}

function arrivalNum(obj){
    var re = /^\+?[1-9][0-9]*$/;
    var arrivalNum = $(obj).val();
    if (!re.test(arrivalNum)) {
        $(obj).get(0).value = "";
        layer.alert("输入内容必须是整数，且小于等于50个字符", {icon: 2});
    } else {
        if (arrivalNum.length > 50) {
            layer.alert("输入内容必须是整数，且小于等于50个字符", {icon: 2});
        }
        if (parseInt(arrivalNum)>parseInt($(obj).get(0).parentNode.children[0].value)) {
            $(obj).get(0).value = "";
            layer.alert("收货数量超出",{icon: 2})
        }
    }

}
function getNowDateTime(time){
    var now = new Date();
    var fullYear = now.getFullYear();
    var month = now.getMonth()+1<10?"0"+(now.getMonth()+1):now.getMonth()+1;
    var day = now.getDate()<10?"0"+now.getDate():now.getDate();

    var no = new Date(""+fullYear+" "+month+" "+day);
    var time = new Date(time);
    debugger
    if (no.getTime() <= time.getTime()) {
        return false;
    }
    return true;
}
function checkPurchaseDetail(obj){
    if (obj.batchNumber.length>50) {
        layer.alert(obj.sku+"的生产批次号/序列号 长度不可超过50",{icon: 2})
        return false;
    }
    if (obj.batchNumber==""||obj.batchNumber==undefined) {
        layer.alert(obj.sku+"的生产批次号/序列号 必填",{icon: 2})
        return false;
    }

    if (parseInt(obj.arrivalCount)>parseInt(obj.oldNum)) {
        layer.alert(obj.sku+"收货数量超出可收获数量",{icon: 2});
        return false;
    }

    if (parseInt(obj.arrivalCount)==0||obj.arrivalCount==undefined||obj.arrivalCount=="") {
        layer.alert(obj.sku+"收货数量必填",{icon: 2});
        return false;
    }

    if (obj.spuType==317||obj.spuType==318) {
        if (obj.manufactureDateTime==""||obj.manufactureDateTime==undefined||obj.invalidDateTime==""||obj.invalidDateTime==undefined) {
            layer.alert(obj.sku+"的生效日期、失效日期必填",{icon: 2});
            return false;
        }
    }
    if (obj.manufactureDateTime!=""&&obj.manufactureDateTime!=undefined) {
        if(!getNowDateTime(obj.manufactureDateTime)){
            layer.alert(obj.sku+"的生产日期只可小于今天",{icon: 2});
            return false;
        }
    }

    if (obj.manufactureDateTime!=""&&obj.manufactureDateTime!=undefined&&obj.invalidDateTime!=""&&obj.invalidDateTime!=undefined) {

        if (obj.manufactureDateTime>obj.invalidDateTime) {
            layer.alert(obj.sku+"的生产日期不可大于失效日期",{icon: 2});
            return false;
        }
    }

    return true;
}
function checkData(obj){
    var flag = false;
    $.ajax({
        async:false,
        url: '/order/newBuyorderPeerList/checkDetails.do',
        data:JSON.stringify(obj),
        type:"POST",
        dataType : "json",
        contentType:'application/json',
        success:function(data){
            console.log(data);
            // layer.close(load);
            if (data.data.code === 200) {
                flag = true;
            } else {
                if (data.data.code === 101) {
                    layer.alert("生产批次号/序列号存在空值");
                }
                if (data.data.code === 102) {
                    layer.alert("生产批次号/序列号存在重复："+data.data.msg);
                }

                if (data.data.code === 1) {
                    layer.alert(data.data.msg);
                }
                if (data.data.code === 2) {
                    layer.alert(data.data.msg+"的收货数量超出可填写的收货数量");
                }

                if (data.data.code === 90) {
                    layer.alert(data.data.msg+"的生产批号/序列号不可为空");
                }

                if (data.data.code === 91) {
                    layer.alert(data.data.msg+"的收货数量必填");
                }

                if (data.data.code === 92) {
                    layer.alert(data.data.msg+"的生效日期、失效日期必填");
                }

            }

            // window.parent.search();
        },
        error:function(data){
            if(data.status == 1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
            }
            if(data.status == 400){
                layer.alert("操作失败");
            }
            if(data.status == 500){
                layer.alert("操作失败");
            }
        }
    });

    return flag;
}
function PurchaseDetail(obj){
    // 物流明细id
    this.expressDetailId=obj.children[11].children[0].innerText;
    this.spuType=obj.children[12].children[0].innerText;
    debugger
    if (obj.children[12].children[0].innerText == 316) {
        this.unionSequence = 1;
    } else {
        this.unionSequence = 0;
    }

    // 商品sku
    this.sku=obj.children[1].children[0].innerText;
    // 商品名
    this.skuName=obj.children[0].children[0].innerText;
    // 型号
    this.model=obj.children[2].children[0].innerText;
    // 单位
    this.unit=obj.children[3].children[0].innerText;
    // 生产厂家
    this.productCompany=obj.children[4].children[0].innerText;
    // 生产许可证号
    this.productionLicence=obj.children[5].children[0].innerText;
    // 注册证号
    this.registerNumber=obj.children[6].children[0].innerText;
    // 生产批次号
    this.batchNumber=obj.children[7].children[0].children[0].value.trim();
    // 收货数量
    this.arrivalCount=obj.children[8].children[0].children[1].value;
    // 此物流单下还需要的
    this.oldNum=obj.children[8].children[0].children[0].value;
    // 生产日期
    this.manufactureDateTime=obj.children[9].children[0].children[0].value;
    // 失效日期
    debugger
    this.invalidDateTime=obj.children[10].children[0].children[0].value;
}
function copyFun(obj) {
    layer.confirm('是否复制当前行?', function(index){
        debugger
        var parentNode = obj.parentNode.parentNode.parentNode;
        console.log(parentNode)
        var tr = $(parentNode);
        var str = $(parentNode).clone();
        $(str).find(".takeDate").each(function (){
            keyValue =keyValue+1;
            $(this).attr("lay-key",keyValue)
            console.log(this)
        })
        tr.after(str);
        setTimeout(function(){
            $(str).find(".takeDate").each(function (){
                console.log(11221);
                layui.laydate.render({
                    elem: this
                    ,type: 'date'
                    ,closeStop: this
                    ,zIndex: 99999999
                });
            })
        },2000)
        layer.close(index);
    })



}
function delFun(obj) {
    layer.confirm('是否删除当前行?', function(index){
        debugger
        $(obj.parentNode.parentNode.parentNode).remove();
        layer.close(index);
    })

}





