<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:mvc="http://www.springframework.org/schema/mvc" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:apollo="http://www.ctrip.com/schema/apollo" xmlns:rabbit="http://www.springframework.org/schema/rabbit"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.ctrip.com/schema/apollo http://www.ctrip.com/schema/apollo.xsd
		http://www.springframework.org/schema/rabbit http://www.springframework.org/schema/rabbit/spring-rabbit.xsd">

	<!--rabbit连接-->
	<rabbit:connection-factory
			id="connectionFactory"
			addresses="${mjx.rabbitmq.host}"
			username="${mjx.rabbitmq.username}"
			password="${mjx.rabbitmq.password}"
			virtual-host="${mjx.rabbitmq.virtualHost}"
			channel-cache-size="100"
			publisher-returns="true"
			publisher-confirms="true" />

	<rabbit:connection-factory
			id="marketConnectionFactory"
			addresses="${market.rabbitmq.host}"
			username="${market.rabbitmq.username}"
			password="${market.rabbitmq.password}"
			virtual-host="${market.rabbitmq.virtualHost}"
			channel-cache-size="100"
			publisher-returns="true"
			publisher-confirms="true" />


	<!-- 定义mq管理 -->
	<rabbit:admin connection-factory="connectionFactory" />
    <!--用来将消息转换为Json，方便消息在生产者和消费者之间传输。-->

	<bean id="messageConverter"
		  class="org.springframework.amqp.support.converter.SimpleMessageConverter" />


	<rabbit:template id="rabbitTemplate"
					 connection-factory="connectionFactory" message-converter="messageConverter" />

	<rabbit:template id="marketRabbitTemplate"
					 connection-factory="marketConnectionFactory" message-converter="messageConverter" />

	<!--定义queue  说明：durable:是否持久化 exclusive: 仅创建者可以使用的私有队列，断开后自动删除 auto_delete: 当所有消费客户端连接断开后，是否自动删除队列-->
	<rabbit:queue name="MjxLogisticsQueue" durable="true" auto-delete="false" exclusive="false" />
	<rabbit:queue name="StockServiceOccupyQueue" durable="true" auto-delete="false" exclusive="false" />
	<rabbit:queue name="StockServiceStockNumQueue" durable="true" auto-delete="false" exclusive="false" />
	<rabbit:queue name="ActionChangeMsgQueue" durable="true" auto-delete="false" exclusive="false" />
	<rabbit:queue name="InvoiceOpenMsgQueue" durable="true" auto-delete="false" exclusive="false" />

	<!--  集采解绑 -->
	<rabbit:queue name="jcErpUnboundQueue" durable="true" auto-delete="false" exclusive="false" />

	<!--定义direct-exchange（一对一） -->
	<rabbit:direct-exchange name="MjxLogisticsExchange" durable="true" auto-delete="false">
		<rabbit:bindings>
			<rabbit:binding queue="MjxLogisticsQueue" key="MjxLogisticsRoutingkey" />
		</rabbit:bindings>
	</rabbit:direct-exchange>


	<rabbit:direct-exchange name="StockServiceExchange" durable="true" auto-delete="false">
		<rabbit:bindings>
			<rabbit:binding queue="StockServiceOccupyQueue" key="StockServiceOccupyRoutingkey" />
			<rabbit:binding queue="StockServiceStockNumQueue" key="StockServiceStockNumRoutingkey" />
			<rabbit:binding queue="ActionChangeMsgQueue" key="ActionChangeMsgRoutingkey" />
		</rabbit:bindings>
	</rabbit:direct-exchange>

	<!--  集采解绑 -->
	<rabbit:direct-exchange name="jcErpOrgExchange" durable="true" auto-delete="false">
	<rabbit:bindings>
		<rabbit:binding queue="jcErpUnboundQueue" key="jcErpUnboundRoutingkey" />
	</rabbit:bindings>
	</rabbit:direct-exchange>



	<rabbit:queue name="customerAptitudeStatusChangeQueue" durable="true" auto-delete="false" exclusive="false" />
	<rabbit:fanout-exchange name="customer_aptitude_status_exchange"  durable="true"  auto-delete="false">
		<rabbit:bindings>
			<rabbit:binding queue="customerAptitudeStatusChangeQueue"></rabbit:binding>
		</rabbit:bindings>
	</rabbit:fanout-exchange>

	<rabbit:direct-exchange name="CustomerBillPeriodExchange" durable="true" auto-delete="false">
		<rabbit:bindings>
			<rabbit:binding queue="InvoiceOpenMsgQueue" key="InvoiceOpenMsgRoutingkey" />
		</rabbit:bindings>
	</rabbit:direct-exchange>

	<rabbit:queue name="erpGoodsBusinessQueue" durable="true" auto-declare="false" exclusive="false" />
	<rabbit:listener-container connection-factory="connectionFactory" acknowledge="manual" failed-declaration-retry-interval="${failed-declaration-retry-interval}"
							   declaration-retries="${declaration-retries}">
		<rabbit:listener ref="businessChanceConsumer" queues="erpGoodsBusinessQueue"></rabbit:listener>
	</rabbit:listener-container>

	<rabbit:queue name="ActionChangeMsgQueue" durable="true" auto-declare="false" exclusive="false" />
	<rabbit:listener-container connection-factory="connectionFactory" acknowledge="manual" failed-declaration-retry-interval="${failed-declaration-retry-interval}"
							   declaration-retries="${declaration-retries}">
		<rabbit:listener ref="inventoryTransferConsumer" queues="ActionChangeMsgQueue"></rabbit:listener>
	</rabbit:listener-container>

	<rabbit:queue name="erpPriceStockChangeQueue" durable="true" auto-declare="false" exclusive="false" />
	<rabbit:listener-container connection-factory="connectionFactory" acknowledge="manual" failed-declaration-retry-interval="${failed-declaration-retry-interval}"
							   declaration-retries="${declaration-retries}" concurrency="1">
		<rabbit:listener ref="erpPriceStockChangeConsumer" queues="erpPriceStockChangeQueue"></rabbit:listener>
	</rabbit:listener-container>

	<rabbit:queue name="erpOfCanalQueue" durable="true" auto-declare="false" exclusive="false" />
	<rabbit:listener-container connection-factory="connectionFactory" acknowledge="manual" failed-declaration-retry-interval="${failed-declaration-retry-interval}"
							   declaration-retries="${declaration-retries}" concurrency="2" prefetch="50">
		<rabbit:listener ref="erpOfCanalQueueConsumer" queues="erpOfCanalQueue"></rabbit:listener>
	</rabbit:listener-container>

	<rabbit:queue name="contractQueue" durable="true" auto-declare="false" exclusive="false" />
	<rabbit:listener-container connection-factory="connectionFactory" acknowledge="manual" failed-declaration-retry-interval="${failed-declaration-retry-interval}"
							   declaration-retries="${declaration-retries}">
		<rabbit:listener ref="contractReturnQueueConsumer" queues="contractQueue"></rabbit:listener>
	</rabbit:listener-container>

	<rabbit:queue name="InvoiceOpenMsgQueue" durable="true" auto-declare="false" exclusive="false" />
	<rabbit:listener-container connection-factory="connectionFactory" acknowledge="manual" failed-declaration-retry-interval="${failed-declaration-retry-interval}"
							   declaration-retries="${declaration-retries}">
		<rabbit:listener ref="orderInvoiceOpenConsumer" queues="InvoiceOpenMsgQueue"></rabbit:listener>
	</rabbit:listener-container>


    <!--订单确认MQ-->
    <rabbit:queue name="confirmStatusQueue" auto-declare="true" durable="true"/>
    <rabbit:listener-container connection-factory="connectionFactory" acknowledge="manual"
                               failed-declaration-retry-interval="${failed-declaration-retry-interval}"
                               declaration-retries="${declaration-retries}">
        <rabbit:listener ref="confirmSaleOrderConsumer" queues="confirmStatusQueue"/>
    </rabbit:listener-container>

	<!--  签收单确认MQ-->
    <rabbit:queue name="singInFormToErpQueue" auto-declare="true" durable="true"/>
    <rabbit:listener-container connection-factory="connectionFactory" acknowledge="manual"
                               failed-declaration-retry-interval="${failed-declaration-retry-interval}"
                               declaration-retries="${declaration-retries}">
        <rabbit:listener ref="confirmSignOrdrConsumer" queues="singInFormToErpQueue"/>
    </rabbit:listener-container>

	<!--  疫情秒杀活动商机mq-->
	<rabbit:queue name="kygLimitQueue" auto-declare="true" durable="true"/>
	<rabbit:listener-container connection-factory="connectionFactory" acknowledge="manual"
							   failed-declaration-retry-interval="${failed-declaration-retry-interval}"
							   declaration-retries="${declaration-retries}">
		<rabbit:listener ref="activityPreOrderConsumer" queues="kygLimitQueue"/>
	</rabbit:listener-container>

	<!-- 售后单创建 -->
	<rabbit:queue name="bdPushAfterSalesOrderToErpQueue" auto-declare="true" durable="true"/>
	<rabbit:listener-container connection-factory="connectionFactory" acknowledge="manual"
							   failed-declaration-retry-interval="${failed-declaration-retry-interval}"
							   declaration-retries="${declaration-retries}">
		<rabbit:listener ref="bdAfterSalesOrderConsumer" queues="bdPushAfterSalesOrderToErpQueue"/>
	</rabbit:listener-container>

	<!-- 售后单创建结果推送前台-->
	<rabbit:queue name="erpConsumeAfterSalesOrderIsSuccessQueue" durable="true" auto-declare="true" exclusive="false" auto-delete="false"/>
	<rabbit:direct-exchange name="afterSalesOrderExchange" durable="true" auto-declare="true" auto-delete="false">
		<rabbit:bindings>
			<rabbit:binding queue="erpConsumeAfterSalesOrderIsSuccessQueue" key="erpConsumeAfterSalesOrderIsSuccessRoutingKey"/>
		</rabbit:bindings>
	</rabbit:direct-exchange>

	<!-- 用户端售后状态推送前台mq-->
	<rabbit:queue name="erpCreateAfterSalesOrderFlowToBdQueue" durable="true" auto-declare="true" exclusive="false" auto-delete="false"/>
	<rabbit:direct-exchange name="afterSalesOrderExchange" durable="true" auto-declare="true" auto-delete="false">
		<rabbit:bindings>
			<rabbit:binding queue="erpCreateAfterSalesOrderFlowToBdQueue" key="erpCreateAfterSalesOrderFlowToBdRoutingKey"/>
		</rabbit:bindings>
	</rabbit:direct-exchange>

	<!-- 售后单推送前台mq-->
	<rabbit:queue name="erpChangAfterSalesOrderToBdQueue" durable="true" auto-declare="true" exclusive="false" auto-delete="false"/>
	<rabbit:direct-exchange name="afterSalesOrderExchange" durable="true" auto-declare="true" auto-delete="false">
		<rabbit:bindings>
			<rabbit:binding queue="erpChangAfterSalesOrderToBdQueue" key="erpChangAfterSalesOrderToBdRoutingKey"/>
		</rabbit:bindings>
	</rabbit:direct-exchange>

	<!--  前台修改售后单联系人信息推送mq-->
	<rabbit:queue name="bdChangContactInfoQueue" auto-declare="true" durable="true"/>
	<rabbit:listener-container connection-factory="connectionFactory" acknowledge="manual" failed-declaration-retry-interval="${failed-declaration-retry-interval}" declaration-retries="${declaration-retries}">
		<rabbit:listener ref="modifyAfterSalesContactInfoConsumer" queues="bdChangContactInfoQueue"/>
	</rabbit:listener-container>

	<!--  售后评价前台回传mq-->
	<rabbit:queue name="bdEvaluateAfterSalesOrderToErpBdQueue" auto-declare="true" durable="true"/>
	<rabbit:listener-container connection-factory="connectionFactory" acknowledge="manual" failed-declaration-retry-interval="${failed-declaration-retry-interval}" declaration-retries="${declaration-retries}">
		<rabbit:listener ref="afterSalesEvaluationReturnConsumer" queues="bdEvaluateAfterSalesOrderToErpBdQueue"/>
	</rabbit:listener-container>
</beans>