<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="公海条件配置" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<script type="text/javascript"
        src="<%= basePath %>static/js/trader/public/save_calculate_rules.js?rnd=${resourceVersionKey}"></script>
<div class="content">


    <form action="" method="post" onsubmit="return false" id="saveCalculateRule">
        
        <div>
            <div class="item">
                <div>
                    <span style="color: red">*</span>
                    <span style="font-weight: bold;font-size: 14px">新客保护期：</span>
                </div>
                <div>
                    <div style="margin-left: 80px">
                        近<input class="input-number" type="text" name="customerCreatedDays" placeholder="1-999" value="${latestRule.customerCreatedDays}"/>天创建的新客，将不纳入公海
                    </div>
                    <div id="customerCreatedDaysError"></div>
                </div>
            </div>

            <div class="item">
                <span style="color: red">*</span>
                <span style="font-weight: bold;font-size: 14px">纳入公海条件：</span>
            </div>
            <div>
                <div style="margin-left: 50px">
                    不满足     近<input class="input-number" type="text" name="validOrderDays" placeholder="1-999" value="${latestRule.validOrderDays}"/>天，生效订单数大于
                    <input class="input-number" type="text" name="validOrderCount" placeholder="0-999" value="${latestRule.validOrderCount}"/>个
                </div>
                <div id="validOrderDaysError"></div>
                <div style="margin-left: 65px;margin-top: 10px">
                    且，近<input class="input-number" type="text" name="communicationDays" placeholder="1-999"
                                 value="${latestRule.communicationDays}"/>天，接通电话数大于
                    <input class="input-number" type="text" name="communicationCount" placeholder="0-999" value="${latestRule.communicationCount}"/>个，将纳入公海
                </div>
                <div id="communicationDaysError"></div>
            </div>


            <div class="item">
                <span style="color: red">*</span>
                <span style="font-weight: bold;font-size: 14px">解锁保护期：</span>
            </div>
            <div>
                <div style="margin-left: 80px">
                    解锁后<input class="input-number" type="text" name="lockProtectDays" placeholder="1-999" value="${latestRule.lockProtectDays}"/>天内，将不纳入公海
                </div>
                <div id="lockProtectDaysError"></div>
            </div>
        </div>

        <div style="padding: 50px 20px;color: green">
            <div>注：</div>
            <div>1.保护期内的客户与其关联客户合并保护；</div>
            <div>2.接通电话标准：通话时长≥2min；</div>
            <div>3.近n天：今天及今天前的n-1天。</div>
        </div>

        <div class="add-tijiao tcenter">
            <button type="submit" id="submit" style="background: dodgerblue; border: 1px solid dodgerblue;">确定</button>
            <button type="button" class="dele" id="cancle"
                    style="background: dodgerblue; border: 1px solid dodgerblue;">取消
            </button>
        </div>
    </form>

</div>
<style>
    .item{
        padding: 10px;
    }
    .input-number{
        width: 100px;
        margin: 0 3px;
    }
</style>
