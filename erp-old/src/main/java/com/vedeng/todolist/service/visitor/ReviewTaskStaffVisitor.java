package com.vedeng.todolist.service.visitor;

import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.goods.GoodsConstants;
import com.vedeng.common.constant.goods.LogTypeEnum;
import com.vedeng.flash.dto.ReviewQueryDto;
import com.vedeng.goods.dao.LogCheckGenerateMapper;
import com.vedeng.goods.model.LogCheckGenerate;
import com.vedeng.order.model.po.OrderReviewProcessPo;
import com.vedeng.order.service.OrderReviewProcessService;
import com.vedeng.todolist.dto.ReviewTaskToDoDto;
import com.vedeng.todolist.dto.SupplyChainStaffTodoDtoBuild;
import com.wms.service.context.ThreadLocalContext;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.*;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.runtime.ProcessInstanceQuery;
import org.activiti.engine.task.Task;
import org.activiti.engine.task.TaskQuery;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@SuppressWarnings("Duplicates")
@Service
public class ReviewTaskStaffVisitor implements TodoVisitor<SupplyChainStaffTodoDtoBuild> {
    @Autowired
    private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

    @Autowired
    private OrderReviewProcessService orderReviewProcessService;

    @Resource
    LogCheckGenerateMapper logCheckGenerateMapper;

    @Override
    public void visitor(SupplyChainStaffTodoDtoBuild todoDtoBuild) {
        User user = ThreadLocalContext.get("userInfo");
        ReviewTaskToDoDto reviewTaskToDoDto = new ReviewTaskToDoDto();

        List<String> businessKeys = orderReviewProcessService.getAllOrderReviewProcess().stream().map(OrderReviewProcessPo::getBusinessKey).collect(Collectors.toList());


        ProcessInstanceQuery processQueryForRemind = processEngine.getRuntimeService().createProcessInstanceQuery();
        ProcessInstanceQuery processQueryForSubmitUnfinish = processEngine.getRuntimeService().createProcessInstanceQuery();

        // 任务相关service
        // 创建历史实例查询
        HistoricActivityInstanceQuery historyAIQuery = processEngine.getHistoryService()
                .createHistoricActivityInstanceQuery();

        TaskService taskService = processEngine.getTaskService();
        TaskQuery taskInstanceQueryForRemind = taskService.createTaskQuery();
        TaskQuery taskInstanceQueryForSubmitUnfinish = taskService.createTaskQuery();

        //待审核
        //自有审核
        List<Integer> selfReviewTypeListForRemind = new ArrayList<>();
        if (user.hasRole(GoodsConstants.GOODS_EDIT_ROLE)) {
            selfReviewTypeListForRemind.add(LogTypeEnum.SPU.getLogType());
            selfReviewTypeListForRemind.add(LogTypeEnum.SKU.getLogType());
        }
        if (user.hasRole(GoodsConstants.GOODS_CHECK_ROLE)) {
            selfReviewTypeListForRemind.add(LogTypeEnum.FIRSTENGAGE.getLogType());
        }

        List<Integer> selfReviewTypeListForSubmit = new ArrayList<>();
        selfReviewTypeListForSubmit.add(LogTypeEnum.SPU.getLogType());
        selfReviewTypeListForSubmit.add(LogTypeEnum.SKU.getLogType());
        selfReviewTypeListForSubmit.add(LogTypeEnum.FIRSTENGAGE.getLogType());

        ReviewQueryDto reviewQueryDto = new ReviewQueryDto();
        List<LogCheckGenerate> UnfinishLogCheckGenerateListForRemind= new ArrayList<>();
        if (CollectionUtils.isNotEmpty(selfReviewTypeListForRemind)){
            UnfinishLogCheckGenerateListForRemind = logCheckGenerateMapper.getAllUnfinishProcess(selfReviewTypeListForRemind, reviewQueryDto);
        }
        //activity审核
        Set<String> processDefinitionKeyList = new HashSet(Arrays.asList("afterSalesVerify", "buyorderVerify", "buyorderVerify_HC", "earlyBuyorderVerify"
                , "bhSaleorderVerify", "traderCustomerVerify", "editSaleorderVerify", "editBuyorderVerify", "saleorderModifyAudit","buyorderExpenseVerify"));

        taskInstanceQueryForRemind.taskCandidateUser(user.getUsername());
        // 用户任务
        List<Task> historicTaskInstanceList = taskInstanceQueryForRemind.list();
        Set<String> processInstanceIds = new HashSet<>();
        for (Task historicTaskInstance : historicTaskInstanceList) {
            processInstanceIds.add(historicTaskInstance.getProcessInstanceId());
        }
        List<ProcessInstance> processInstanceList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(processInstanceIds)) {
            processQueryForRemind.processDefinitionKeys(processDefinitionKeyList);
            processQueryForRemind.processInstanceIds(processInstanceIds);

            processInstanceList = processQueryForRemind.list().stream().filter(item ->
                    CollectionUtils.isEmpty(businessKeys) || !businessKeys.contains(item.getBusinessKey()))
                    .collect(Collectors.toList());
        }
        reviewTaskToDoDto.setRemainReviewNum(processInstanceList.size() + UnfinishLogCheckGenerateListForRemind.size());


        //发起的在途
        List<LogCheckGenerate> UnfinishLogCheckGenerateListForSubmit = logCheckGenerateMapper.getAllUnfinishProcess(selfReviewTypeListForSubmit, reviewQueryDto);

        if (CollectionUtils.isNotEmpty(UnfinishLogCheckGenerateListForSubmit)) {
            UnfinishLogCheckGenerateListForSubmit = UnfinishLogCheckGenerateListForSubmit.stream().filter(e -> e.getCreator().equals(user.getUserId())).collect(Collectors.toList());
        }
        List<Task> list = taskInstanceQueryForSubmitUnfinish.list();
        Set<String> processInstanceIds2 = new HashSet<>();
        for (Task historicTaskInstance : list) {
            processInstanceIds2.add(historicTaskInstance.getProcessInstanceId());
        }
        List<ProcessInstance> list1= new ArrayList<>();
        if(CollectionUtils.isNotEmpty(processInstanceIds2)) {
            processQueryForSubmitUnfinish.processDefinitionKeys(processDefinitionKeyList);
            processQueryForSubmitUnfinish.processInstanceIds(processInstanceIds2);
            list1 = processQueryForSubmitUnfinish.list().stream().filter(item ->
                    CollectionUtils.isEmpty(businessKeys) || !businessKeys.contains(item.getBusinessKey()))
                    .collect(Collectors.toList());
        }

        //发起者过滤
        if(CollectionUtils.isNotEmpty(list1)) {
            list1 = list1.stream().filter(e -> filterStarter(user, e, historyAIQuery)).collect(Collectors.toList());
        }
        reviewTaskToDoDto.setSubmitUnfinishNum(UnfinishLogCheckGenerateListForSubmit.size()+list1.size());
        //结果设置
        todoDtoBuild.get().setReviewTaskToDoDto(reviewTaskToDoDto);
    }

    private boolean filterStarter(User user, ProcessInstance e, HistoricActivityInstanceQuery historyAIQuery) {
        String processDefinitionId = e.getProcessInstanceId();
        historyAIQuery.processInstanceId(processDefinitionId);
        historyAIQuery.activityName("申请人");
        List<HistoricActivityInstance> list = historyAIQuery.list();
        try {
            if(CollectionUtils.isNotEmpty(list)){
                if(list.stream().anyMatch(o->o.getAssignee().equals(user.getUsername()))){
                    return true;
                }
            }
        } catch (Exception e1) {
            return false;
        }
        return false;
    }
}
