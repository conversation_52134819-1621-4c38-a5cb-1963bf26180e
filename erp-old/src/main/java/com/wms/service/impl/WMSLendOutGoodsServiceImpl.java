package com.wms.service.impl;

import com.wms.dao.WmsOutputOrderGoodsMapper;
import com.wms.model.po.WmsOutputOrderGoods;
import com.wms.service.WMSLendOutGoodsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class WMSLendOutGoodsServiceImpl implements WMSLendOutGoodsService {

    @Autowired
    private WmsOutputOrderGoodsMapper outputOrderGoodsMapper;

    @Override
    public List<WmsOutputOrderGoods> queryOutputGoodsByLendOutId(Long lendOutOrderId) {
        return outputOrderGoodsMapper.queryOutputGoodsByLendOutId(lendOutOrderId);
    }
}
