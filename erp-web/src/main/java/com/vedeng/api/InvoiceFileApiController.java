package com.vedeng.api;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.model.ResultJSON;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.customerbillperiod.constant.CustomerBillPeriodOverdueManageDetailTypeEnum;
import com.vedeng.customerbillperiod.constant.CustomerBillPeriodUseTypeEnum;
import com.vedeng.customerbillperiod.dao.CustomerBillPeriodOverdueManagementDetailMapper;
import com.vedeng.customerbillperiod.dao.CustomerBillPeriodUseDetailMapper;
import com.vedeng.customerbillperiod.dto.CustomerBillPeriodUnfreezingDto;
import com.vedeng.customerbillperiod.dto.GenerateCustomerBillPeriodManagementDetailDto;
import com.vedeng.customerbillperiod.dto.RollbackCustomerBillPeriodManagementDetailDto;
import com.vedeng.customerbillperiod.model.CustomerBillPeriodOverdueManagementDetail;
import com.vedeng.customerbillperiod.model.CustomerBillPeriodUseDetail;
import com.vedeng.customerbillperiod.service.CustomerBillPeriodManagementCodeCommonService;
import com.vedeng.erp.finance.api.InvoiceFileApi;
import com.vedeng.erp.finance.dto.InvoiceFileDto;
import com.vedeng.erp.finance.dto.VoucherDto;
import com.vedeng.finance.dao.CapitalBillMapper;
import com.vedeng.finance.dao.InvoiceMapper;
import com.vedeng.finance.model.CapitalBill;
import com.vedeng.finance.model.Invoice;
import com.vedeng.infrastructure.oss.service.OssUtilsService;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.vo.OrderData;
import com.vedeng.order.service.OrderAccountPeriodService;
import com.vedeng.order.service.SaleorderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.stream.Collectors;

/**
 * OCR识别插件升级及发票文件上传
 */
@Slf4j
@Controller
@RequestMapping("/api/invoice")
public class InvoiceFileApiController extends BaseController {

    @Autowired
    private SaleorderService saleorderServiceOrder;

    @Autowired
    private OssUtilsService ossUtilsService;

    @Autowired
    private InvoiceFileApi invoiceFileApi;

    @Value(value = "${desktopAppUpgradeInfo:{}}")
    private String desktopAppUpgradeInfo;

    @NoNeedAccessAuthorization
    @RequestMapping(value = "/desktopAppUpgradeInfo")
    @ResponseBody
    public AppVersion desktopAppUpgradeInfo() {
        AppVersion appVersion = JSONObject.parseObject(desktopAppUpgradeInfo, AppVersion.class);
        return appVersion;
    }


    public static String getFileExtension(String fileName) {
        int dotIndex = fileName.lastIndexOf(".");
        if (dotIndex != -1 && dotIndex < fileName.length() - 1) {
            return fileName.substring(dotIndex + 1);
        } else {
            return "";
        }
    }

    @NoNeedAccessAuthorization
    @RequestMapping(value = "/uploadUpgradeFile")
    @ResponseBody
    public ResultInfo<?> uploadUpgradeFile(HttpServletRequest request, @RequestParam("file") MultipartFile file) {
        String fileName = file.getOriginalFilename();

        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            String extension = getFileExtension(fileName);
            String fileUrl = ossUtilsService.upload2OssForInputStream(extension, fileName, inputStream);
            if (StringUtils.isNotBlank(fileUrl)) {
                return ResultInfo.success(fileUrl);
            }
        } catch (Exception e) {
            log.error("上传升级包失败", e);
            return ResultInfo.error("上传升级包失败" + e.getMessage());
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (Exception e) {
                log.error("上传升级包失败，流关闭失败");
                return null;
            }
        }
        return ResultInfo.error("上传升级包失败");
    }

    @NoNeedAccessAuthorization
    @RequestMapping(value = "/querySaleOrderData")
    @ResponseBody
    public ResultInfo<?> uploadUpgradeFile(HttpServletRequest request, @RequestParam("saleOrderNo") String saleOrderNo) {
        try {
            OrderData order = saleorderServiceOrder.queryOrderNoForMjx(saleOrderNo);
            return ResultInfo.success(order);
        } catch (Exception e) {
            log.error("查询订单信息失败", e);
            return ResultInfo.error(e.getMessage());
        }
    }

    @NoNeedAccessAuthorization
    @RequestMapping(value = "/test")
    @ResponseBody
    public ResultInfo<?> test(HttpServletResponse response) throws Exception {
        if (true) {
            response.setHeader("permissionstatus", "nopermission");
            // 没有权限的状态码
            response.setStatus(1001);
            // 向http头添加登录的url
            response.getWriter().write(JsonUtils.convertObjectToJsonStr(ResultJSON.failed().message("nopermission")));
            response.flushBuffer();
            return null;
        }
        return new ResultInfo<>(0, "不弹窗");
    }


    @NoNeedAccessAuthorization
    @RequestMapping(value = "/handInvoiceFile")
    @ResponseBody
    public ResultInfo<?> handInvoiceFile(HttpServletRequest request, @RequestParam("file") MultipartFile file) {
        String invoiceNo = request.getParameter("invoiceNo");
        String invoiceCode = request.getParameter("invoiceCode");
        Integer tag = Integer.valueOf(request.getParameter("tag"));
        InvoiceFileDto invoiceFileDto = new InvoiceFileDto();
        invoiceFileDto.setInvoiceNo(invoiceNo);
        invoiceFileDto.setInvoiceCode(invoiceCode);
        invoiceFileDto.setTag(tag);
        if (StringUtils.isBlank(invoiceNo) || tag == null) {
            log.warn("上传发票文件参数为空失败");
            return ResultInfo.error("上传发票文件参数为空失败，文件为空或发票类别为空");
        }
        boolean checkInvoiceExist = invoiceFileApi.getInvoiceByInvoiceNoAndTag(invoiceNo, tag);
        if (!checkInvoiceExist) {
            log.warn(invoiceNo + "，发票号码不存在");
            return ResultInfo.error(invoiceNo + "，发票号码不存在");
        }

        String extension = getFileExtension(file.getOriginalFilename());

        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            String fileUrl = ossUtilsService.upload2OssForInputStream(extension, invoiceNo + "_" + invoiceCode + "." + extension, inputStream);
            invoiceFileDto.setFileUrl(fileUrl);
            boolean result = invoiceFileApi.handleInvoiceFile(invoiceFileDto);
            if (result) {
                return ResultInfo.success();
            }
        } catch (Exception e) {
            log.error("上传发票文件失败", e);
            return ResultInfo.error("上传发票文件失败" + e.getMessage());
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (Exception e) {
                log.error("上传发票文件失败，流关闭失败");
                return null;
            }
        }
        return ResultInfo.error("上传发票文件失败");
    }

    @NoNeedAccessAuthorization
    @RequestMapping(value = "/handleVoucherFile")
    @ResponseBody
    public ResultInfo<?> handleVoucherFile(HttpServletRequest request, @RequestParam("file") MultipartFile file) {
        String voucherDate = request.getParameter("voucherDate");
        String voucherNo = request.getParameter("voucherNo");
        String extension = getFileExtension(file.getOriginalFilename());
        VoucherDto voucherDto = invoiceFileApi.judgeTransactionOrInvoice(voucherDate, voucherNo);
        if (ObjectUtil.isNull(voucherDto)) {
            return ResultInfo.error("上传凭证附件失败,未获取到对应的记录,voucherDate:" + voucherDate + ",voucherNo:" + voucherNo);
        }
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            String fileUrl = ossUtilsService.upload2OssForInputStream(extension, voucherDate + voucherNo + "." + extension, inputStream);
            int updateRows = invoiceFileApi.updateVoucherUrl(voucherDto, fileUrl, voucherDate, voucherNo);
            if (updateRows > 0) {
                return ResultInfo.success();
            }
        } catch (Exception e) {
            log.error("上传凭证附件失败", e);
            return ResultInfo.error("上传凭证附件失败" + e.getMessage());
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (Exception e) {
                log.error("上传凭证附件失败，流关闭失败");
            }
        }
        return ResultInfo.error("上传凭证附件失败");
    }

    @Autowired
    InvoiceMapper invoiceMapper;
    @Autowired
    OrderAccountPeriodService orderAccountPeriodService;

    @NoNeedAccessAuthorization
    @RequestMapping(value = "/dealCustomerBillPeriodManagement")
    @ResponseBody
    public ResultInfo<?> dealCustomerBillPeriodManagement(String invoiceIds) {
        log.info("修复账期发票开始");
        log.info("需要修复的账期发票:{}", invoiceIds);
        String[] split = invoiceIds.split(",");
        log.info("需要修复的账期发票数量:{}", split.length);
        int index = 0;
        for (String s : split) {
            index++;
            log.info("正在修复第{}个账期发票", index);
            Integer invoiceId = Integer.valueOf(s);
            Invoice invoiceInfo = invoiceMapper.getInvoiceBaseInfoByInvoiceId(invoiceId);
            if (invoiceInfo == null) {
                log.info("开票生成账期逾期编码时发票信息不存在invoiceId:{}", invoiceId);
                continue;
            }
            orderAccountPeriodService.dealCustomerBillPeriodManagement(
                    invoiceInfo.getRelatedId(),
                    CustomerBillPeriodOverdueManageDetailTypeEnum.ORDER_INVOICE.getCode(),
                    invoiceId,
                    invoiceInfo.getAmount());
        }
        log.info("修复账期发票完成");
        return ResultInfo.success();
    }


    public static void main(String[] args) {
        long x = 1710491760463L; //2024-03-15 16:36:00
        long y = 1725499035000L;  //2023-12-26 15:22:08
        Instant instant1 = Instant.ofEpochMilli(x);//
        Instant instant2 = Instant.ofEpochMilli(y);
        // 计算两个时间戳之间的Duration
        Duration duration = Duration.between(instant2, instant1);
        // 将Duration转换为天数
        long overdueDate = duration.toDays();
        System.out.println(overdueDate);
    }

    @NoNeedAccessAuthorization
    @RequestMapping(value = "/dealBillPeriod")
    @ResponseBody
    public ResultInfo<?> dealBillPeriod(Integer invoiceId) {
        log.info("修复账期发票开始");
        log.info("需要修复的账期发票:{}", invoiceId);
        logger.info("task修复账期数据");
        // 补充正数的账期逾期管理明细
        Invoice invoiceInfo = invoiceMapper.getInvoiceBaseInfoByInvoiceId(invoiceId);
        if (invoiceInfo == null) {
            logger.info("开票生成账期逾期编码时发票信息不存在invoiceId:{}", invoiceId);
        }
        Integer saleOrderId = invoiceInfo.getRelatedId();
        Integer managementType = 2;
        Saleorder orderInfo = saleorderMapper.getSaleOrderById(saleOrderId);
        if (orderInfo == null) {
            logger.error("生成账期逾期编码订单信息异常 orderId:{}", saleOrderId);
        }


        GenerateCustomerBillPeriodManagementDetailDto managementDetailDto = new GenerateCustomerBillPeriodManagementDetailDto();
        managementDetailDto.setAmount(invoiceInfo.getAmount());
        managementDetailDto.setCompanyId(orderInfo.getCompanyId());
        managementDetailDto.setCustomerId(saleorderMapper.getCustomerIdByOrderId(saleOrderId));
        managementDetailDto.setOrderId(Long.valueOf(saleOrderId));
        managementDetailDto.setOrderNo(orderInfo.getSaleorderNo());
        managementDetailDto.setType(managementType);
        managementDetailDto.setAddTime(System.currentTimeMillis());
        managementDetailDto.setRelatedId(Long.valueOf(invoiceId));
        managementDetailDto.setAmount(invoiceInfo.getAmount());
        logger.info("生成客户账期管理明细编码 generateCustomerBillPeriodManagementDetailDto:{}", JSON.toJSONString(managementDetailDto));


        List<CustomerBillPeriodUseDetail> useDetailListOfUnOverdueManaged =
                customerBillPeriodUseDetailMapper.getCustomerBillPeriodUseDetailListOfUnOverdueManagedByOrderId(managementDetailDto.getCompanyId(),
                                managementDetailDto.getCustomerId(), managementDetailDto.getOrderId()).stream()
                        .filter(item -> item.getUnreturnedAmount().compareTo(BigDecimal.ZERO) > 0)
                        .collect(Collectors.toList());

        if (CollUtil.isEmpty(useDetailListOfUnOverdueManaged)) {
            logger.info("无需要偿还账期");
        }

        // 查询客户账期使用明细
        List<CustomerBillPeriodUseDetail> customerBillPeriodUseDetailLisOrderId = customerBillPeriodUseDetailMapper.getCustomerBillPeriodUseDetailLisOrderId(managementDetailDto.getCompanyId(),
                managementDetailDto.getCustomerId(), managementDetailDto.getOrderId());
        logger.info("查询客户账期使用明细{}", JSON.toJSONString(customerBillPeriodUseDetailLisOrderId));

        BigDecimal overdueAmount = managementDetailDto.getAmount();

        String billPeriodOverdueManagementCode =
                customerBillPeriodManagementCodeCommonService.customerBillPeriodManagementCodeGenerator(managementDetailDto.getCompanyId(), managementDetailDto.getCustomerId(),
                        managementDetailDto.getOrderNo(), managementDetailDto.getOrderId(), managementType);

        // 获取订单的还款数据
        List<CapitalBill> periodCapitalBill = capitalBillMapper.getPeriodCapitalBill(saleOrderId, 1);

        if (CollUtil.isNotEmpty(periodCapitalBill) && periodCapitalBill.size() > 1) {
            logger.info("多笔账期还款暂不处理{}", JSON.toJSONString(periodCapitalBill));
        }

        // 计算逾期天数
        // 根据订单的还款时间，减去账期生成时间，减去账期周期，得到逾期天数，更新逾期天数
        long overdueDate = 0;
        CapitalBill capitalBill = null;
        if (CollUtil.isNotEmpty(periodCapitalBill)) {
            capitalBill = CollUtil.getFirst(periodCapitalBill);
            // 将时间戳转换为Instant对象
            Instant instant1 = Instant.ofEpochMilli(capitalBill.getTraderTime());
            Instant instant2 = Instant.ofEpochMilli(invoiceInfo.getAddTime());
            // 计算两个时间戳之间的Duration
            Duration duration = Duration.between(instant2, instant1);
            // 将Duration转换为天数
            overdueDate = duration.toDays();
        }

        long l = 0;
        for (CustomerBillPeriodUseDetail item : customerBillPeriodUseDetailLisOrderId) {
            CustomerBillPeriodOverdueManagementDetail overdueManagementDetail = new CustomerBillPeriodOverdueManagementDetail();
            overdueManagementDetail.setBillPeriodOverdueManagementCode(billPeriodOverdueManagementCode);
            overdueManagementDetail.setCompanyId(item.getCompanyId());
            overdueManagementDetail.setCustomerId(item.getCustomerId());
            overdueManagementDetail.setBillPeriodUseDetailId(item.getBillPeriodUseDetailId());
            overdueManagementDetail.setType(item.getSettlementType());
            overdueManagementDetail.setRelatedId(managementDetailDto.getRelatedId());
            overdueManagementDetail.setParentManagementDetailId(0L);
            overdueManagementDetail.setSettlementPeriod(item.getSettlementPeriod());
            overdueManagementDetail.setOverdueAmount(BigDecimal.ZERO);
            // 根据订单的还款时间，减去账期生成时间，减去账期周期，得到逾期天数，更新逾期天数
            l = item.getSettlementPeriod() - overdueDate;
            // 判断是否小于0，如果小于表示逾期，则逾期天数取绝对值
            if (l < 0) {
                overdueManagementDetail.setOverdueDays(Math.abs((int) l));
            } else {
                overdueManagementDetail.setOverdueDays(0);
            }
            // 发票开票时间
            overdueManagementDetail.setAddTime(invoiceInfo.getAddTime());
            overdueManagementDetail.setModTime(invoiceInfo.getAddTime());

            overdueManagementDetail.setAmount(overdueAmount);
            overdueManagementDetail.setUnreturnedAmount(overdueAmount);
            overdueAmount = overdueAmount.subtract(overdueAmount);
            customerBillPeriodOverdueManagementDetailMapper.insertSelective(overdueManagementDetail);
            logger.info("生成账期的逾期管理编码记录：{}", overdueManagementDetail.toString());
            if (overdueAmount.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }
            billPeriodOverdueManagementCode = customerBillPeriodManagementCodeCommonService.billPeriodManagementCodeIncrease(billPeriodOverdueManagementCode);
        }

        CustomerBillPeriodUseDetail first = CollUtil.getFirst(customerBillPeriodUseDetailLisOrderId);
        CustomerBillPeriodUnfreezingDto customerBillPeriodUnfreezingDto = new CustomerBillPeriodUnfreezingDto();
        customerBillPeriodUnfreezingDto.setUnfreezingType(CustomerBillPeriodUseTypeEnum.REPAYMENT.getCode());
        customerBillPeriodUnfreezingDto.setCompanyId(ErpConst.ONE);
        customerBillPeriodUnfreezingDto.setCustomerId(first.getCustomerId());
        customerBillPeriodUnfreezingDto.setOrderId(Long.valueOf(saleOrderId));
        customerBillPeriodUnfreezingDto.setRelatedId(Long.valueOf(capitalBill.getCapitalBillId()));
        customerBillPeriodUnfreezingDto.setUnfreezingAmount(capitalBill.getAmount());
        customerBillPeriodUnfreezingDto.setAddTime(capitalBill.getTraderTime());
        logger.info("添加流水处理订单冻结和占用的账期金额释放 customerBillPeriodUnfreezingDto:{}", JSON.toJSONString(customerBillPeriodUnfreezingDto));

        RollbackCustomerBillPeriodManagementDetailDto rollbackCustomerBillPeriodManagementDetailDto = new RollbackCustomerBillPeriodManagementDetailDto();
        BeanUtils.copyProperties(customerBillPeriodUnfreezingDto, rollbackCustomerBillPeriodManagementDetailDto);
        rollbackCustomerBillPeriodManagementDetailDto.setType(CustomerBillPeriodOverdueManageDetailTypeEnum.REPAYMENT.getCode());
        rollbackCustomerBillPeriodManagementDetailDto.setAmount(capitalBill.getAmount());
        // 补充负数的账期逾期管理明细
        logger.info("生成负数的客户账期管理编码：{}", rollbackCustomerBillPeriodManagementDetailDto.toString());

        List<CustomerBillPeriodOverdueManagementDetail> overdueManagementDetailListUnreturned =
                customerBillPeriodOverdueManagementDetailMapper.getOverdueManagementDetailListUnreturnedByOrderId(
                        rollbackCustomerBillPeriodManagementDetailDto.getCompanyId(),
                        rollbackCustomerBillPeriodManagementDetailDto.getCustomerId(),
                        rollbackCustomerBillPeriodManagementDetailDto.getOrderId());
        if (overdueManagementDetailListUnreturned.size() > 0) {
            //待生成负数账期逾期管理编码的金额
            BigDecimal toRepaymentAmountForOverdue = rollbackCustomerBillPeriodManagementDetailDto.getAmount().abs();
            for (CustomerBillPeriodOverdueManagementDetail item : overdueManagementDetailListUnreturned) {
                CustomerBillPeriodOverdueManagementDetail overdueManagementDetail = new CustomerBillPeriodOverdueManagementDetail();
                overdueManagementDetail.setBillPeriodOverdueManagementCode(item.getBillPeriodOverdueManagementCode());
                overdueManagementDetail.setCompanyId(item.getCompanyId());
                overdueManagementDetail.setCustomerId(item.getCustomerId());
                overdueManagementDetail.setType(rollbackCustomerBillPeriodManagementDetailDto.getType());
                overdueManagementDetail.setSettlementPeriod(item.getSettlementPeriod());
                overdueManagementDetail.setRelatedId(rollbackCustomerBillPeriodManagementDetailDto.getRelatedId());
                overdueManagementDetail.setParentManagementDetailId(item.getBillPeriodOverdueManagementDetailId());
                overdueManagementDetail.setAddTime(rollbackCustomerBillPeriodManagementDetailDto.getAddTime());
                overdueManagementDetail.setBillPeriodUseDetailId(item.getBillPeriodUseDetailId());
                BigDecimal amount = item.getUnreturnedAmount().compareTo(toRepaymentAmountForOverdue) >= 0 ? toRepaymentAmountForOverdue : item.getUnreturnedAmount();
                overdueManagementDetail.setAmount(amount.multiply(new BigDecimal("-1")));
                //保存负数账期逾期编码
                customerBillPeriodOverdueManagementDetailMapper.insertSelective(overdueManagementDetail);
                logger.info("保存负数的客户账期逾期管理编码：{}", overdueManagementDetail.toString());

                //同步更新账期编码的未还金额
                CustomerBillPeriodOverdueManagementDetail toUpdate = new CustomerBillPeriodOverdueManagementDetail();
                toUpdate.setBillPeriodOverdueManagementDetailId(item.getBillPeriodOverdueManagementDetailId());
                toUpdate.setUnreturnedAmount(item.getUnreturnedAmount().subtract(amount));
                toUpdate.setModTime(customerBillPeriodUnfreezingDto.getAddTime());
                if (l < 0) {
                    toUpdate.setOverdueAmount(amount);
                }
                customerBillPeriodOverdueManagementDetailMapper.updateByPrimaryKeySelective(toUpdate);
                logger.info("更新账期的逾期管理编码的未还金额：{}", toUpdate);

                if (toRepaymentAmountForOverdue.compareTo(item.getUnreturnedAmount()) <= 0) {
                    break;
                }
                toRepaymentAmountForOverdue = toRepaymentAmountForOverdue.subtract(item.getUnreturnedAmount());
            }
        }
        log.info("修复账期发票完成");
        return ResultInfo.success();
    }


    @Autowired
    private SaleorderMapper saleorderMapper;
    @Autowired
    CustomerBillPeriodManagementCodeCommonService customerBillPeriodManagementCodeCommonService;
    @Autowired
    CustomerBillPeriodUseDetailMapper customerBillPeriodUseDetailMapper;
    @Autowired
    CustomerBillPeriodOverdueManagementDetailMapper customerBillPeriodOverdueManagementDetailMapper;
    @Autowired
    CapitalBillMapper capitalBillMapper;

}
