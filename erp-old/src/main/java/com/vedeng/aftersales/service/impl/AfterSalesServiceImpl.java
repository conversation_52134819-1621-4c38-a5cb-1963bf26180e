package com.vedeng.aftersales.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import com.alibaba.fastjson.JSON;
import com.common.constants.Contant;
import com.fasterxml.jackson.core.type.TypeReference;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.aftersales.component.AfterSalesOrderService;
import com.vedeng.aftersales.component.vo.MapExtendsVo;
import com.vedeng.aftersales.dao.*;
import com.vedeng.aftersales.model.*;
import com.vedeng.aftersales.model.dto.LongDistanceFreeSearchDto;
import com.vedeng.aftersales.model.entities.AfterSaleServiceStandardLongdistanceFeePo;
import com.vedeng.aftersales.model.vo.*;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.aftersales.util.SaleAfterServiceUtil;
import com.vedeng.authorization.dao.RegionMapper;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.Organization;
import com.vedeng.authorization.model.Region;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.*;
import com.vedeng.common.constant.goods.GoodsConstants;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.common.core.enums.BusinessSourceTypeEnum;
import com.vedeng.common.core.enums.SettlementTypeEnum;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.utils.ErpSpringBeanUtil;
import com.vedeng.common.exception.RunTimeServiceException;
import com.vedeng.common.http.HttpClientUtils;
import com.vedeng.common.model.FileInfo;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.trace.data.TrackParamsData;
import com.vedeng.common.trace.enums.EventTrackingEnum;
import com.vedeng.common.trace.track.TrackStrategy;
import com.vedeng.common.trace.track.factory.TrackStrategyFactory;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.*;
import com.vedeng.customerbillperiod.exception.CustomerBillPeriodException;
import com.vedeng.erp.aftersale.dto.*;
import com.vedeng.erp.aftersale.service.AfterSalesApiService;
import com.vedeng.erp.aftersale.service.BuyorderAfterSalesApiService;
import com.vedeng.erp.buyorder.dto.BuyOrderAfterSalesHandlingFeeExpenseDto;
import com.vedeng.erp.buyorder.service.BuyorderExpenseApiService;
import com.vedeng.erp.finance.cmd.SettlementBillCreateCmd;
import com.vedeng.erp.finance.cmd.SettlementBillQueryCmd;
import com.vedeng.erp.finance.dto.*;
import com.vedeng.erp.finance.enums.YNEnum;
import com.vedeng.erp.finance.service.CapitalBillApiService;
import com.vedeng.erp.finance.service.InvoiceApiService;
import com.vedeng.erp.finance.service.InvoiceReversalApiService;
import com.vedeng.erp.finance.service.SettlementBillApiService;
import com.vedeng.erp.finance.service.PayApplyApiService;
import com.vedeng.erp.saleorder.api.SaleorderSyncService;
import com.vedeng.erp.saleorder.enums.*;
import com.vedeng.erp.saleorder.enums.PCOrderStatusEnum;
import com.vedeng.erp.trader.service.TraderSupplierApiService;
import com.vedeng.finance.constant.CapitalBillBussinessTypeEnum;
import com.vedeng.finance.dao.*;
import com.vedeng.finance.model.*;
import com.vedeng.finance.model.vo.PayApplyVo;
import com.vedeng.finance.service.CapitalBillService;
import com.vedeng.finance.service.PayApplyService;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.dao.CoreSpuMapper;
import com.vedeng.goods.dao.RCategoryJUserMapper;
import com.vedeng.goods.dto.CoreSkuDto;
import com.vedeng.goods.model.CoreSku;
import com.vedeng.goods.model.CoreSkuGenerate;
import com.vedeng.goods.service.CoreSkuApiService;
import com.vedeng.goods.service.VgoodsService;
import com.vedeng.infrastructure.sms.service.SmsService;
import com.vedeng.logistics.dao.*;
import com.vedeng.logistics.model.*;
import com.vedeng.logistics.model.outIn.OutInDetail;
import com.vedeng.logistics.service.WarehouseGoodsInService;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.order.dao.*;
import com.vedeng.order.model.*;
import com.vedeng.order.model.vo.*;
import com.vedeng.order.service.*;
import com.vedeng.orderstream.aftersales.constant.AfterSalesProcessEnum;
import com.vedeng.orderstream.aftersales.constant.TraderModeEnum;
import com.vedeng.orderstream.aftersales.dao.RInstallstionJGoodsMapper;
import com.vedeng.system.dao.AttachmentMapper;
import com.vedeng.system.dao.ParamsConfigMapper;
import com.vedeng.system.dao.SysOptionDefinitionMapper;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.model.vo.ParamsConfigVo;
import com.vedeng.system.service.OrgService;
import com.vedeng.system.service.VerifiesRecordService;
import com.vedeng.trader.dao.*;
import com.vedeng.trader.model.*;
import com.vedeng.trader.model.vo.TraderAddressVo;
import com.vedeng.trader.model.vo.TraderFinanceVo;
import com.vedeng.trader.model.vo.TraderVo;
import com.vedeng.trader.service.TraderDataService;
import com.wms.service.LogicalAfterorderChooseService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.Transformer;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <b>Description:</b><br> 售后订单服务层
 * <AUTHOR>
 * @Note
 * <b>ProjectName:</b> erp
 * <br><b>PackageName:</b> com.vedeng.aftersales.service.impl
 * <br><b>ClassName:</b> AfterSalesServiceImpl
 * <br><b>Date:</b> 2017年10月9日 上午11:26:20
 */
@Service("afterSalesOrderService")
public class AfterSalesServiceImpl extends BaseServiceimpl implements AfterSalesService {
	public static Logger logger = LoggerFactory.getLogger(AfterSalesServiceImpl.class);

	// add by Tomcat.Hui 2020/3/6 11:41 上午 .Desc: VDERP-2057 BD订单流程优化-ERP部分
	@Value("${mjx_url}")
	private String mjxurl;

	@Autowired
	@Qualifier("webAccountMapper")
	private WebAccountMapper webAccountMapper;

	//add by Tomcat.Hui 2019/9/5 10:44 .Desc: VDERP-1053 add mapper. start
	@Autowired
	@Qualifier("afterSalesMapper")
	private AfterSalesMapper afterSalesMapper;
	//add by Tomcat.Hui 2019/9/5 10:44 .Desc: VDERP-1053 add mapper. end

	@Autowired
	@Qualifier("userMapper")
	private UserMapper userMapper;

	@Autowired
	@Qualifier("rCategoryJUserMapper")
	private RCategoryJUserMapper rCategoryJUserMapper;

	@Resource
	private OrgService orgService;

	@Resource
	private ParamsConfigMapper paramsConfigMapper;

	@Resource
	private RTraderJUserMapper rTraderJUserMapper;

	@Resource
	private OrderNoDict orderNoDict;

	@Autowired
	@Qualifier("afterSalesGoodsMapper")
	private AfterSalesGoodsMapper afterSalesGoodsMapper;

	@Autowired
	@Qualifier("warehouseGoodsOperateLogMapper")
	private WarehouseGoodsOperateLogMapper warehouseGoodsOperateLogMapper;

	@Autowired
	private WarehouseStockService warehouseStockService;

	@Autowired
	@Qualifier("saleorderMapper")
	private SaleorderMapper saleorderMapper;

	@Autowired
	@Qualifier("warehouseGoodsStatusMapper")
	private WarehouseGoodsStatusMapper warehouseGoodsStatusMapper;

	@Autowired
	private AfterSalesDetailMapper afterSalesDetailMapper;

	@Resource
	private AfterSalesLongDistanceFeeMapper afterSalesLongDistanceFeeMapper;

	@Resource
	private RegionMapper regionMapper;


	@Autowired
	private AttachmentMapper attachmentMapper;

	@Autowired
	private BuyorderMapper buyorderMapper;

	@Autowired
	private SaleorderGoodsMapper saleorderGoodsMapper;

	@Resource
	private BuyorderGoodsMapper buyorderGoodsMapper;

	@Resource
	private TraderAddressMapper traderAddressMapper;

	@Resource
	private CoreSpuMapper coreSpuMapper;

	@Resource
	private InvoiceMapper invoiceMapper;

	@Autowired
	private LogicalAfterorderChooseService logicalAfterorderChooseService;

	@Autowired
	private OrderAccountPeriodService orderAccountPeriodService;

	@Resource
	private SysOptionDefinitionMapper sysOptionDefinitionMapper;

	@Resource
	private AfterSalesInvoiceMapper afterSalesInvoiceMapper;

	@Resource
	private RInvoiceJInvoiceMapper rInvoiceJInvoiceMapper;

	@Autowired
	private SaleorderDataService saleorderDataService;

	@Resource
    private CapitalBillMapper capitalBillMapper;

	@Autowired
    private BuyorderDataService buyorderDataService;

	@Resource
    private TraderContactMapper traderContactMapper;
	@Resource
	private OrderCommonService orderCommonService;

	@Autowired
	private TraderDataService traderDataService;

	@Resource
	private AfterSalesTraderMapper afterSalesTraderMapper;

	@Resource
	private AfterSalesRecordMapper afterSalesRecordMapper;
	@Autowired
	private VgoodsService vGoodsService;
	@Resource
	private TraderFinanceMapper traderFinanceMapper;

	@Resource
	private TraderMapper traderMapper;

	@Resource
	private ExpressMapper expressMapper;

	@Resource
	private CapitalBillService capitalBillService;

	@Resource
	private InvoiceApplyMapper invoiceApplyMapper;

	@Resource
	private AfterSalesInstallstionMapper afterSalesInstallstionMapper;

	@Resource
	private PayApplyMapper payApplyMapper;

	@Resource
	private PayApplyDetailMapper payApplyDetailMapper;

	@Resource
	private AfterSaleBuyorderDirectOutLogMapper afterSaleBuyorderDirectOutLogMapper;

	@Resource
	private RInstallstionJGoodsMapper rInstallstionJGoodsMapper;

	@Resource
	private RBuyorderSaleorderMapper rBuyorderSaleorderMapper;

	@Resource
	private ExpressDetailMapper expressDetailMapper;

	@Autowired
	private SmsService smsService;
	@Resource
	private SaleorderSyncService saleorderSyncService;


	@Autowired
	private SaleorderService saleorderService;

	@Autowired
	private CapitalBillApiService capitalBillApiService;

	@Autowired
	private InvoiceApiService invoiceApiService;

	@Autowired
	private WarehouseGoodsOutInItemMapper warehouseGoodsOutInItemMapper;

	@Autowired
	private WarehouseGoodsInService warehouseGoodsInService;

	@Autowired
	private InvoiceReversalApiService invoiceReversalApiService;

	@Autowired
	private BuyorderExpenseApiService buyorderExpenseApiService;

	@Autowired
	private CoreSkuApiService coreSkuApiService;

	@Autowired
	private CoreSkuMapper coreSkuMapper;

	@Autowired
	private BuyorderAfterSalesApiService buyorderAfterSalesApiService;

	@Autowired
	private PayApplyApiService payApplyApiService;

	@Autowired
	private SettlementBillApiService settlementBillApiService;

	private final static String DEFAULT_FY = "V253620";

	@Autowired
	private TraderSupplierApiService traderSupplierApiService;

	@Autowired
	private AfterSalesApiService afterSalesApiService;

	@Autowired
	private TrackStrategyFactory trackStrategyFactory;

	/**
	 * 申请状态
	 */
	private static final List<Integer> APPLICATE_STATUS = Arrays.asList(SaleOrderStatusEnum.IN_PROGRESS.getCode(), SaleOrderStatusEnum.FINISHED.getCode());

	/**
	 * <b>Description:</b><br> 查询售后订单分页信息
	 * @param afterSalesVo
	 * @param page
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年10月9日 上午11:22:12
	 */
	@Override
	public Map<String, Object> getAfterSalesPage(AfterSalesVo afterSalesVo, Page page,List<User> userList) {
		String url = httpUrl + ErpConst.GET_AFTERSALES_PAGE;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<List<AfterSalesVo>>> TypeRef2 = new TypeReference<ResultInfo<List<AfterSalesVo>>>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, afterSalesVo, clientId, clientKey, TypeRef2,
					page);
			if(result == null || result.getCode() ==-1){
				return null;
			}
			@SuppressWarnings("unchecked")
			List<AfterSalesVo> asvList = (List<AfterSalesVo>) result.getData();
			page = result.getPage();
			if(asvList != null && asvList.size() > 0){
				for (AfterSalesVo asv : asvList) {
					if(asv.getCreator() != 0){
						asv.setCreatorName(getUserNameByUserId(asv.getCreator()));
						asv.setOrgName(getOrgaNameByUserId(asv.getCreator()));
					}
					if(asv.getServiceUserId() != 0){
						asv.setServiceUserName(getUserNameByUserId(asv.getServiceUserId()));
					}
//					for (User user : userList) {
//						if(asv.getServiceUserId().equals(user.getUserId())){
//							asv.setIsView(1);
//							break;
//						}
//					}
					//审核人
					if(null != asv.getVerifyUsername()){
					    List<String> verifyUsernameList = Arrays.asList(asv.getVerifyUsername().split(","));
					    asv.setVerifyUsernameList(verifyUsernameList);
					}

					// add by Randy.Xu 2021/4/15 9:19 .Desc: . begin
					//VDERP-3895 【商品标准、采购及交付体验提升（一期）】销售售后单列表、详情页供应链视角
					//对应售后单的入库单号
					Integer saleorderId = asv.getOrderId();
					List<SaleorderGoods> saleorderGoodsList = saleorderMapper.getSaleorderGoodsBySaleOrderId(saleorderId);
					List<Buyorder> inOrderList = new ArrayList<>();
					if(!CollectionUtils.isEmpty(saleorderGoodsList)){
						for (SaleorderGoods saleorderGoods : saleorderGoodsList) {
							//根据中间表去查
							List<Buyorder> buyorderList = buyorderMapper.getBuyorderListBySaleorderGoodsId(saleorderGoods.getSaleorderGoodsId());
							if(!CollectionUtils.isEmpty(buyorderList)){
								//去重
								List<Buyorder> newList = buyorderList.stream().collect(Collectors.collectingAndThen(
										Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(Buyorder::getBuyorderNo))), ArrayList::new));
                                inOrderList.addAll(newList);
							}else{
								//根据出库记录去查
								List<Buyorder> BuyorderList2 = buyorderMapper.getBuyorderOutListBySaleorderGoodsId(saleorderGoods.getSaleorderGoodsId());
								if(!CollectionUtils.isEmpty(BuyorderList2)){
									//去重
									List<Buyorder> newList = BuyorderList2.stream().collect(Collectors.collectingAndThen(
											Collectors.toCollection(()->new TreeSet<>(Comparator.comparing(Buyorder::getBuyorderNo))),ArrayList::new));
                                    inOrderList.addAll(newList);
								}
							}
						}
					}
					//去重
					if(!CollectionUtils.isEmpty(inOrderList)){
						inOrderList=inOrderList.stream().collect(Collectors.collectingAndThen(
								Collectors.toCollection(()->new TreeSet<>(Comparator.comparing(Buyorder::getBuyorderNo))),ArrayList::new));
					}
                    asv.setInOrderList(inOrderList);
                    //对应的售后商品产品经理
                    List<User> afterSalesProductManagerUserList = afterSalesMapper.getAfterSalesGoodsProductManagerUser(asv.getAfterSalesId());
					List<User> afterSalesProductAssistantUserList = afterSalesMapper.getAfterSalesGoodsProductAssistantUser(asv.getAfterSalesId());
                    List<User> allUserList = new ArrayList<>();
                    if(!CollectionUtils.isEmpty(afterSalesProductManagerUserList)) {
						allUserList.addAll(afterSalesProductManagerUserList);
					}
					if(!CollectionUtils.isEmpty(afterSalesProductAssistantUserList)) {
						allUserList.addAll(afterSalesProductAssistantUserList);
					}
					if(!CollectionUtils.isEmpty(allUserList)){
						//去重
						allUserList = allUserList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(()->new TreeSet<>(Comparator.comparing(User::getUserId))),ArrayList::new));
					}
                    asv.setBeLongProductUserList(allUserList);
					// add by Randy.Xu 2021/4/15 9:19 .Desc: . end
				}
			}
			Map<String, Object> map = new HashMap<>();
			map.put("list", asvList);
			map.put("page", page);
			return map;
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	public Map<String, Object> getbuyorderAfterSalesList(AfterSalesVo afterSalesVo, Page page) {
		String url = httpUrl + "aftersales/order/getbuyorderaftersaleslistpage.htm";
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<List<AfterSalesVo>>> TypeRef2 = new TypeReference<ResultInfo<List<AfterSalesVo>>>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, afterSalesVo, clientId, clientKey, TypeRef2,
					page);
			if(result == null || result.getCode() ==-1){
				return null;
			}
			Map<String, Object> map = new HashMap<>();
			List<AfterSalesVo> asvList = (List<AfterSalesVo>) result.getData();
			page = result.getPage();
			if(asvList.size() > 0){
				List<Integer> userIds = new ArrayList<>();
				for(AfterSalesVo vo : asvList){
					if(vo.getCreator() > 0){
						userIds.add(vo.getCreator());
					}
				}
				if (userIds.size() > 0) {
					List<User> userList = userMapper.getUserByUserIds(userIds);
					for(AfterSalesVo afterSales : asvList){
						if(afterSales.getCreator() > 0){
							for(User u : userList){
								if(u.getUserId().equals(afterSales.getCreator())){
									afterSales.setCreatorName(u.getUsername());
									break;
								}
							}
						}
					}
				}
			}

			map.put("list", asvList);
			map.put("page", page);
			return map;
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}
	/**
	 * <b>Description:</b><br> 根据订单id查询对应的售后订单列表
	 * @param afterSalesVo
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年10月10日 上午9:12:45
	 */
	@Override
	public List<AfterSalesVo> getAfterSalesVoListByOrderId(AfterSalesVo afterSalesVo) {
		String url = httpUrl + ErpConst.GET_AFTERSALESLIST_BYORDERID;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<List<AfterSalesVo>>> TypeRef2 = new TypeReference<ResultInfo<List<AfterSalesVo>>>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, afterSalesVo, clientId, clientKey, TypeRef2);
			if(result == null || result.getCode() ==-1){
				return null;
			}
			@SuppressWarnings("unchecked")
			List<AfterSalesVo> asvList = (List<AfterSalesVo>) result.getData();
			if(asvList != null && asvList.size() > 0){
				for (AfterSalesVo asv : asvList) {
					if(asv.getCreator() != 0){
						asv.setCreatorName(getUserNameByUserId(asv.getCreator()));
					}
				}
			}
			return asvList;
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}
	/**
	 * <b>Description:</b><br> 保存新增售后
	 * @param afterSalesVo
	 * @param user
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年10月11日 下午4:28:14
	 */
	@Override
	public ResultInfo<?> saveAddAfterSales(AfterSalesVo afterSalesVo, User user) {
		afterSalesVo.setCreator(user.getUserId());
		afterSalesVo.setAddTime(DateUtil.sysTimeMillis());
		afterSalesVo.setUpdater(user.getUserId());
		afterSalesVo.setModTime(DateUtil.sysTimeMillis());
		//根据当前的销售人员获取相应的售后人员
		if(afterSalesVo.getSubjectType() == 535){
			afterSalesVo.setServiceUserId(getAfterSalesServiceUser(user));
		}
		if(ObjectUtils.notEmpty(afterSalesVo.getZone())){
			afterSalesVo.setArea(getAddressByAreaId(afterSalesVo.getZone()));
		}else if(ObjectUtils.notEmpty(afterSalesVo.getCity()) && ObjectUtils.isEmpty(afterSalesVo.getZone())){
			afterSalesVo.setArea(getAddressByAreaId(afterSalesVo.getCity()));
		}else if(ObjectUtils.notEmpty(afterSalesVo.getProvince()) && ObjectUtils.isEmpty(afterSalesVo.getCity()) && ObjectUtils.isEmpty(afterSalesVo.getZone())){
			afterSalesVo.setArea(getAddressByAreaId(afterSalesVo.getProvince()));
		}


		logger.info("新增售后订单，开始... afterSalesVo:{}", JSON.toJSONString(afterSalesVo));
		// add by frnlin.wu for [耗材商城售后：提出代码] at 2018-12-18 begin
		// 订单ID
		Integer orderId = afterSalesVo.getOrderId();
		// 订单号
		String orderNo = afterSalesVo.getOrderNo();
		// 售后类型
		Integer type = afterSalesVo.getType();
		// 售后主体
		Integer subjectType = afterSalesVo.getSubjectType();
		// 公司ID
		Integer companyId = afterSalesVo.getCompanyId();
		// 售后来源
		Integer source = afterSalesVo.getSource();
		//VDERP-2469  配合前台和指南针优惠券功能，ERP相应的改动
		//增加售后关联销售单类型
		afterSalesVo.setOrderType(-1);
		logger.info("新增售后订单，订单ID:{}, 订单号:{}, 售后类型:{}, 售后主体:{}, 公司ID:{}, source:{}", orderId, orderNo, type, subjectType, companyId, source);

		// 统计销售订单sku商品数量 和 销售订单的sku的小计
		Map<Integer, MapExtendsVo<Integer, BigDecimal>> skuNumMap = new HashMap<Integer, MapExtendsVo<Integer, BigDecimal>>();
		// add by frnlin.wu for [耗材商城售后：提出代码] at 2018-12-18 end
		//验证当前的销售订单或采购订单最新一次售后的状态
		//采购
		if (subjectType == 536) {
			List<AfterSalesVo> list = getAfterSalesVoListByOrderId(afterSalesVo);
			if (list != null && list.size() > 0 && (list.get(0).getAtferSalesStatus() == 0 || list.get(0).getAtferSalesStatus() == 1)) {
				ResultInfo.error("");
			}
		} else if (subjectType == 535) {
			//销售
			Saleorder saleorder = saleorderMapper.getSaleorderInfoById(orderId);

			afterSalesVo.setOrderType(saleorder.getOrderType());
			List<SaleorderGoodsVo> sgvList = saleorderGoodsMapper.getSaleorderGoodsVoListBySaleorderId(saleorder);
			// modify by franlin.wu [统计销售订单sku商品数量] at 2018-12-18 begin
			// 查询特殊商品id的list
			List<Integer> specialGoodIdList = getSpecialGoodsId();

			if (!CollectionUtils.isEmpty(sgvList)) {
				for (int i = 0; i < sgvList.size(); i++) {
					// 当前sku的商品信息
					SaleorderGoodsVo sgv = sgvList.get(i);
					// null，则继续
					if (null == sgv || null == sgv.getGoodsId()) {
						continue;
					}
					// add by franlin.wu for [耗材订单售后] at 2018-12-18 begin
					// 商品ID
					Integer goodsId = sgv.getGoodsId();
					if (CommonConstants.ONE.equals(source) || afterSalesVo.getOrderType().equals(1)) {
						// 统计每个SaleorderGoodId的销售订单数量和sku的小计
						MapExtendsVo<Integer, BigDecimal> skuNumAndAmount = new MapExtendsVo<Integer, BigDecimal>();
						// 数量
						skuNumAndAmount.setExData(sgv.getNum());
						// sku的小计
						skuNumAndAmount.setValue(null == sgv.getMaxSkuRefundAmount() ? BigDecimal.ZERO : sgv.getMaxSkuRefundAmount());
						// 循环特殊商品ID
						for (Integer specialGoodsId : specialGoodIdList) {
							if (goodsId.equals(specialGoodsId)) {
								// sku的小计 ，特殊商品最大退款金额为当前单价
								skuNumAndAmount.setValue(null == sgv.getPrice() ? BigDecimal.ZERO : sgv.getPrice());
								break;
							}
						}
						// 统计
						skuNumMap.put(sgv.getSaleorderGoodsId(), skuNumAndAmount);
					}
					// add by franlin.wu for [耗材订单售后] at 2018-12-18 end
					if (sgv.getLockedStatus() == 1) {
						sgvList.remove(i);
						i--;
					}
				}
			}
			// modify by franlin.wu [统计销售订单sku商品数量] at 2018-12-18 end
			if (CollectionUtils.isEmpty(sgvList)) {
				logger.error("统计销售订单sku商品数量 warn afterSalesVo:{}", JSON.toJSONString(afterSalesVo));
				return ResultInfo.error("统计销售订单sku商品数量为空");
			}
		}

		//添加主表信息----开始
		AfterSales afterSales = new AfterSales();
		afterSales.setOrderId(orderId);
		afterSales.setOrderNo(orderNo);
		afterSales.setAddTime(afterSalesVo.getAddTime());
		afterSales.setAtferSalesStatus(afterSalesVo.getAtferSalesStatus());
		afterSales.setCreator(afterSalesVo.getCreator());
		afterSales.setModTime(afterSalesVo.getModTime());
		afterSales.setServiceUserId(afterSalesVo.getServiceUserId());
		afterSales.setStatus(afterSalesVo.getStatus());
		afterSales.setSubjectType(subjectType);
		afterSales.setType(type);
		afterSales.setUpdater(afterSalesVo.getUpdater());
		afterSales.setValidStatus(afterSalesVo.getValidStatus());
		afterSales.setCompanyId(companyId);
		// add by franlin.wu for[售后来源0ERP 1耗材] at 2018-12-15 begin
		afterSales.setSource(source);
		// add by franlin.wu for[售后来源0ERP 1耗材] at 2018-12-15 end
		//VDERP-6890直发的销售售后与关联的采购单售后强耦合---销售售后退换货单流程变更start
		if (afterSalesVo.getCreateType() != null) {
			afterSales.setCreateType(afterSalesVo.getCreateType());//设置订单创建方式
		}
		if(afterSalesVo.getType().equals(548)){
			afterSales.setInvoiceRefundStatus(ErpConst.ONE);
		}
		afterSales.setDeliveryDirectAfterSalesId(afterSalesVo.getDeliveryDirectAfterSalesId());
		//订单流升级--新售后订单增加标识--0否1是
		afterSales.setIsNew(ErpConst.ONE);
		//VDERP-6890直发的销售售后与关联的采购单售后强耦合---销售售后退换货单流程变更end
		Integer res = afterSalesMapper.insertSelective(afterSales);
		// add by franlin.wu for[提出代码，afterSales.getAfterSalesId()] at 2018-12-18 begin
		// 售后订单ID
		Integer afterSalesId = afterSales.getAfterSalesId();
		logger.info("新增售后订单，售后订单ID:{}", afterSalesId);
		// add by franlin.wu for[提出代码，afterSales.getAfterSalesId()] at 2018-12-18 end

		// 统计当前
		Map<Integer, Integer> nowReundSkuNumMap = new HashMap<Integer, Integer>();
		// operateType = 1  添加售后订单商品
		if (!addAfterSaleForGoods(afterSalesVo, afterSalesId, nowReundSkuNumMap, 1)) {
			logger.error("添加售后订单商品失败 afterSalesVo:{}", JSON.toJSONString(afterSalesVo));
			return ResultInfo.error("添加售后订单商品失败");
		}
		// 涉及耗材售后订单 退款金额
		BigDecimal hcReundAmount = BigDecimal.ZERO;
		try {
			hcReundAmount = subReundAmountBySourceAndType(afterSalesVo, afterSalesId, nowReundSkuNumMap, skuNumMap);
		} catch (Exception e) {
			logger.error("计算耗材的售后退款金额异常", e);
			return ResultInfo.error("计算耗材的售后退款金额异常");
		}
		//新增售后详情T_AFTER_SALES_DETAIL
		if (!addAfterSaleDetails(afterSalesVo, afterSalesId, source, hcReundAmount, 1)) {
			logger.error("添加售后详情异常 warn afterSalesVo:{}", JSON.toJSONString(afterSalesVo));
			return ResultInfo.error("添加售后详情异常");
		}
		afterSales.setAfterSalesNo(orderNoDict.getOrderNum(afterSalesId, 6));
		res = afterSalesMapper.updateByPrimaryKeySelective(afterSales);
		//添加主表信息----结束
		if (res < 1) {
			return ResultInfo.error("添加售后主表异常");
		}
		try {
			if (subjectType == 535 || subjectType == 537) {
				// 开始添加埋点信息
				Map<String, Object> trackParams = new HashMap<>();
				// 客户id
				trackParams.put("traderId", afterSalesVo.getTraderId());
				// 创建人信息
				trackParams.put("track_user", user);
				// 订单号
				trackParams.put("orderNo", afterSales.getOrderNo());
				// 售后类型
				SysOptionDefinition sysOptionDefinition = sysOptionDefinitionMapper.selectByPrimaryKey(type);
				trackParams.put("afterSaleTypeName", Objects.nonNull(sysOptionDefinition) ? sysOptionDefinition.getTitle() : "");
				TrackStrategy trackStrategy = trackStrategyFactory.getStrategyByType(EventTrackingEnum.AFTER_SALE_ORDER_CREATE_BACK);
				TrackParamsData trackParamsData = new TrackParamsData();
				trackParamsData.setTrackParams(trackParams);
				trackParamsData.setTrackTime(new Date());
				trackParamsData.setTrackResult(ResultInfo.success());
				trackParamsData.setEventTrackingEnum(EventTrackingEnum.AFTER_SALE_ORDER_CREATE_BACK);
				trackStrategy.track(trackParamsData);
			}
		} catch (Exception e) {
			logger.error("埋点：{}，失败，不影响正常业务",EventTrackingEnum.AFTER_SALE_ORDER_CREATE_BACK.getArchivedName(),e);
		}
		//添加附件表信息----开始
		String[] fileNames = afterSalesVo.getAttachName();
		String[] fileUri = afterSalesVo.getAttachUri();
		if (fileNames != null && fileUri != null) {
			for (int i = 0; i < fileNames.length && i < fileUri.length; i++) {
				String name = fileNames[i];
				String uri = fileUri[i];
				if (!"".equals(name) && !"".equals(uri)) {
					Attachment attachment = new Attachment();
					attachment.setName(name);
					attachment.setUri(uri);
					if (uri.contains("jpg") || uri.contains("png") || uri.contains("gif") || uri.contains("bmp")) {
						attachment.setAttachmentType(460);
					} else {
						attachment.setAttachmentType(461);
					}
					attachment.setAttachmentFunction(afterSales.getType());
					attachment.setRelatedId(afterSales.getAfterSalesId());
					attachment.setCreator(afterSalesVo.getCreator());
					attachment.setAddTime(afterSalesVo.getAddTime());
					attachment.setDomain(afterSalesVo.getDomain());
					int res3 = attachmentMapper.insert(attachment);
					if (res3 == 0) {
						return ResultInfo.error("添加附件信息异常");
					}
				}
			}
		}
		//添加附件表信息----结束

		//添加售后退票表信息----开始
		if(548 == afterSalesVo.getType()) {
			//采购售后仅退票保存退票信息
			AfterSalesInvoice afterSalesInvoice = new AfterSalesInvoice();
			afterSalesInvoice.setAfterSalesId(afterSales.getAfterSalesId());
			afterSalesInvoice.setIsRefundInvoice(1);
			Integer invoiceId = invoiceApiService.queryInvoiceIdByInfo(afterSalesVo.getOrderId(),afterSalesVo.getInvoiceNo());
			afterSalesInvoice.setInvoiceId(invoiceId);
			afterSalesInvoice.setInvoiceNo(afterSalesVo.getInvoiceNo());
			int res4 = afterSalesInvoiceMapper.insertSelective(afterSalesInvoice);
			if (res4 == 0) {
				return ResultInfo.error("添加售后退票表信息异常");
			}
			//保存新发票信息
			AfterBuyorderInvoiceDto afterBuyorderInvoiceDto = new AfterBuyorderInvoiceDto();
			afterBuyorderInvoiceDto.setAfterSalesId(afterSales.getAfterSalesId());
			afterBuyorderInvoiceDto.setInvoiceNo(afterSalesVo.getNewInvoiceNo());
			afterBuyorderInvoiceDto.setInvoiceCode(afterSalesVo.getNewInvoiceCode());
			afterBuyorderInvoiceDto.setCreator(afterSales.getCreator());
			afterBuyorderInvoiceDto.setInvoiceType(afterSalesVo.getInvoiceType());
			SysOptionDefinition sysOptionDefinition = sysOptionDefinitionMapper.selectByPrimaryKey(afterSalesVo.getInvoiceType());
			if(sysOptionDefinition != null) {
				afterBuyorderInvoiceDto.setRatio(new BigDecimal(sysOptionDefinition.getComments()));
			}
			buyorderAfterSalesApiService.saveAfterBuyorderInvoiceInfo(afterBuyorderInvoiceDto);
			//退票状态设置为未退票
			afterSales.setInvoiceRefundStatus(ErpConst.ONE);
			afterSalesMapper.updateByPrimaryKeySelective(afterSales);
		}else {
			String[] invoiceIds = afterSalesVo.getInvoiceIds();
			if (invoiceIds != null) {
				for (int i = 0; i < invoiceIds.length; i++) {
					if (!"".equals(invoiceIds[i])) {
						AfterSalesInvoice afterSalesInvoice = new AfterSalesInvoice();
						afterSalesInvoice.setInvoiceId(Integer.valueOf(invoiceIds[i]));
						afterSalesInvoice.setAfterSalesId(afterSales.getAfterSalesId());
						afterSalesInvoice.setIsRefundInvoice(1);
						int res4 = afterSalesInvoiceMapper.insertSelective(afterSalesInvoice);
						if (res4 == 0) {
							return ResultInfo.error("添加售后退票表信息异常");
						}
					}
				}
			}
		}
		//添加售后退票表信息----结束

		//新增售后锁定关联的订单
		logger.info("售后锁定销售单,{},",JSON.toJSONString(afterSales));
		if (afterSales.getSubjectType() == 535 && (afterSales.getType() == 539 || afterSales.getType() == 540 || afterSales.getType() == 543)) {
			logger.info("售后锁定销售单开始,afterSales{},",JSON.toJSONString(afterSales));
			//锁定销售
			Saleorder saleorder = new Saleorder();
			saleorder.setSaleorderId(afterSales.getOrderId());
			saleorder.setServiceStatus(1);//售后中
			saleorder.setLockedStatus(1);
			saleorder.setLockedReason("售后锁定");//锁定原因
			int re = saleorderMapper.updateByPrimaryKeySelective(saleorder);
			if (re == 0) {
				return ResultInfo.error("新增售后锁定关联的订单异常");
			}

			//更新锁定订单预警状态
			updateLockSaleorderWarning(afterSales.getOrderId());

			if (null != afterSalesVo.getSku() && !("".equals(afterSalesVo.getSku()))) {
				String[] skus = afterSalesVo.getSku().split(",");
				Object[] skuClear = oneClear(skus);
				String[] sku = Arrays.copyOf(skuClear, skuClear.length, String[].class);
				List<AfterSalesGoodsVo> list = new ArrayList<>();
				for (int i = 0; i < sku.length; i++) {
					AfterSalesGoodsVo afterSalesGoodsVo = new AfterSalesGoodsVo();
					if (null != sku[i].trim() && !("".equals(sku[i].trim()))) {
						afterSalesGoodsVo.setSku(sku[i]);
						afterSalesGoodsVo.setSaleorderId(afterSalesVo.getSaleorderId());
						list.add(afterSalesGoodsVo);
					}
				}
				if (list.size() > 0) {
					int reloc = saleorderGoodsMapper.updateGoodsLockStatusBySku(list);
					if (reloc == 0) {
						return ResultInfo.error("更新锁定订单预警状态异常");
					}
				}
			}
		} else if (afterSales.getSubjectType() == 536) {
			//锁定采购
			Buyorder buyorder = new Buyorder();
			buyorder.setBuyorderId(afterSales.getOrderId());
			buyorder.setServiceStatus(1);
			buyorder.setLockedStatus(1);
			int re = buyorderMapper.updateByPrimaryKeySelective(buyorder);
			if (re == 0) {
				return ResultInfo.error("锁定采购异常");
			}
		}

		orderCommonService.updateAfterOrderDataUpdateTime(afterSales.getAfterSalesId(), null, OrderDataUpdateConstant.AFTER_ORDER_GENERATE);

		logger.info("推送售后单到前台商城,新增售后单,afterSalesId:{}", afterSales.getAfterSalesId());
		afterSalesApiService.pushAfterSalesToFrontMall(afterSales.getAfterSalesId());
		return ResultInfo.success(afterSales.getAfterSalesId());
	}

	/**
	 * 查询特殊商品id的list
	 *
	 * @return
	 */
	private List<Integer> getSpecialGoodsId() {
		List<Integer> list = new LinkedList<>();
		try {
			// 根据id查询特殊商品
			List<SysOptionDefinition> sysOptList = sysOptionDefinitionMapper.getDictionaryByParentId(693);
			if (org.apache.commons.collections.CollectionUtils.isNotEmpty(sysOptList)) {
				for (SysOptionDefinition sysOpt : sysOptList) {
					if (null == sysOpt || StringUtil.isEmpty(sysOpt.getComments())) {
						continue;
					}
					list.add(Integer.parseInt(sysOpt.getComments().trim()));
				}
			}
		} catch (Exception e) {
			logger.error("根据id查询特殊商品发生异常", e);
		}
		return list;
	}

	/**
	 * 添加售后订单商品
	 *
	 * @param afterSalesVo
	 * @param afterSalesId
	 * @param skuNumMap
	 * @param operaterType
	 * @return
	 * @throws RuntimeException
	 */
	private boolean addAfterSaleForGoods(AfterSalesVo afterSalesVo, Integer afterSalesId,
										 Map<Integer, Integer> skuNumMap, int operaterType) throws RuntimeException {
		// 页面参数
		String[] afterSalesNums = afterSalesVo.getAfterSalesNum();
		// 售后类型
		Integer type = afterSalesVo.getType();
		// 公司ID
		Integer companyId = afterSalesVo.getCompanyId();
		// 订单ID
		Integer orderId = afterSalesVo.getOrderId();
		if(type == 548){
			//采购售后仅退票
			List<AfterSalesGoods> orderGoodsPriceList = afterSalesMapper.getOrderGoodsPriceByAfterId(afterSalesVo.getSubjectType(), afterSalesId);
			if (afterSalesNums != null) {
				for(int i = 0;i < afterSalesNums.length;i++){
					String afterBuyorderNum = afterSalesNums[i];
					logger.info("采购售后ID{},仅退票提交商品信息{},",afterSalesId,JSON.toJSONString(afterBuyorderNum));
					if(!"".equals(afterBuyorderNum)){
						AfterSalesGoods afterSalesGoods = new AfterSalesGoods();
						//buyorderGoodsId采购商品id
						Integer buyorderGoodsId = Integer.valueOf(afterBuyorderNum.split("\\|")[0]);
						//仅退票售后数量
						BigDecimal afterInvoiceNum = new BigDecimal(0);
						if (!afterBuyorderNum.split("\\|")[1].equals("undefined")) {
							// 退货数量
							afterInvoiceNum = new BigDecimal(afterBuyorderNum.split("\\|")[1]);
							afterSalesGoods.setAfterInvoiceNum(afterInvoiceNum);
						}
						//goodsId
						afterSalesGoods.setGoodsId(Integer.valueOf(afterBuyorderNum.split("\\|")[2]));
						afterSalesGoods.setAfterSalesId(afterSalesId);
						afterSalesGoods.setOrderDetailId(buyorderGoodsId);
						//保存其他产品信息
						if (EmptyUtils.isNotEmpty(orderGoodsPriceList)) {
							for (AfterSalesGoods buyorderGoodsInfo:orderGoodsPriceList) {
								if (buyorderGoodsId != null && buyorderGoodsId.equals(buyorderGoodsInfo.getGoodsId())) {
									afterSalesGoods.setPrice(buyorderGoodsInfo.getPrice());
									break;// 结束for循环
								}
							}
						}
						Integer res2 = afterSalesGoodsMapper.insertSelective(afterSalesGoods);
						if (res2 == 0) {
							throw new ServiceException("没有插入，抛出事务回滚");
						}
					}
				}
			}
			return true;
		}else {
			// 特殊商品ID
			Integer thGoodsId = afterSalesVo.getThGoodsId();
			//添加售后产品表信息----开始
			if (afterSalesNums != null) {
				List<Integer> orderGoodsIdList = new ArrayList<>();//退货产品的id
				List<Integer> thNumList = new ArrayList<>();//退货产品的数量
				List<AfterSalesGoods> afterSalesGoodsList = new ArrayList<>();//退货产品集合
				AfterSalesGoods afterSalesGoods = null;

				// 查询售后订单商品单价与小计
				List<AfterSalesGoods> orderGoodsPriceList = null;
				// 非第三方售后（第三方售后无对应订单）
				if (afterSalesVo.getSubjectType() != null && !afterSalesVo.getSubjectType().equals(537)) {
					orderGoodsPriceList = afterSalesMapper.getOrderGoodsPriceByAfterId(afterSalesVo.getSubjectType(), afterSalesId);
				}
				// 计算退款金额
//			BigDecimal refundAmount = new BigDecimal(0);
				for (int i = 0; i < afterSalesNums.length; i++) {
					String afterSalesNum = afterSalesNums[i];
					logger.info("新增售后订单，售后产品信息:{}", afterSalesNum);
					if (!"".equals(afterSalesNum)) {
						Integer saleorderGoodsId = Integer.valueOf(afterSalesNum.split("\\|")[0]);//此处的saleorderGoodsId有可能是buyorderGoodsId
						afterSalesGoods = new AfterSalesGoods();

						Integer num = 0;
						if (!afterSalesNum.split("\\|")[1].equals("undefined")) {
							// 退货数量
							num = Integer.valueOf(afterSalesNum.split("\\|")[1]);
							afterSalesGoods.setNum(num);
							thNumList.add(num);
						}
						afterSalesGoods.setAfterSalesId(afterSalesId);
						afterSalesGoods.setOrderDetailId(saleorderGoodsId);
						orderGoodsIdList.add(saleorderGoodsId);

						if (!afterSalesNum.split("\\|")[2].equals("undefined")) {
							Integer deliveryDirect = Integer.valueOf(afterSalesNum.split("\\|")[2]);
							afterSalesGoods.setDeliveryDirect(deliveryDirect);
						}

						// 商品ID
						Integer goodsId = null;

						if (afterSalesNum.split("\\|").length >= 4) {

							goodsId = Integer.valueOf(afterSalesNum.split("\\|")[3]);

							if (afterSalesVo.getSubjectType() != null && afterSalesVo.getSubjectType().equals(535)) {
								//是否是活动商品
								Integer isActionGoods = Integer.valueOf(afterSalesNum.split("\\|")[4]);
								//是否是活动商品
								afterSalesGoods.setIsActionGoods(isActionGoods);
							}

						} else {
							goodsId = Integer.valueOf(afterSalesNum.split("\\|")[0]);
						}

						// add by franlin.wu for [统计每个saleorderGoodsId商品退货数量] at 2018-12-18 begin
						if (null == skuNumMap.get(saleorderGoodsId)) {
							skuNumMap.put(saleorderGoodsId, num);
						} else {
							// 累加
							skuNumMap.put(saleorderGoodsId, num + skuNumMap.get(saleorderGoodsId));
						}
						// add by franlin.wu for [统计每个sku商品退货数量] at 2018-12-18 end

						// 计算退货金额
						if (EmptyUtils.isNotEmpty(orderGoodsPriceList)) {
							for (int x = 0; x < orderGoodsPriceList.size(); x++) {
								if (saleorderGoodsId != null && saleorderGoodsId.equals(orderGoodsPriceList.get(x).getGoodsId())) {
									afterSalesGoods.setPrice(orderGoodsPriceList.get(x).getPrice());
									afterSalesGoods.setSkuRefundAmount(orderGoodsPriceList.get(x).getPrice().multiply(new BigDecimal(num)));
									afterSalesGoods.setSkuOldRefundAmount(orderGoodsPriceList.get(x).getPrice().multiply(new BigDecimal(num)));
									break;// 结束for循环
								}
							}
						}
//					refundAmount.add(afterSalesGoods.getSkuRefundAmount());
						afterSalesGoods.setGoodsId(goodsId);
						//计算售后实际应退数量start
						setRealRknum(type, saleorderGoodsId, afterSalesGoods);
						//计算售后实际应退数量end
						afterSalesGoodsList.add(afterSalesGoods);

						Integer res2 = afterSalesGoodsMapper.insertSelective(afterSalesGoods);
						if (res2 == 0) {
							throw new RuntimeException("没有插入，抛出事务回滚");
						}
					}
				}
				// 退款金额统一使用SKU_REFUND_AMOUNT累计 2019-02-27
//			afterSalesVo.setRefundAmount(refundAmount);
				//退货产生的退票
				if (type == 539 || type == 546) {
					// 编辑时 删除
					if (2 == operaterType) {
						//先删除售后关联的InvoiceId
						afterSalesInvoiceMapper.delAfterSalesInvoiceByAfterSalesId(afterSalesId);
					}

					List<AfterSalesInvoiceVo> afterSalesInvoiceVoList = null;
					AfterSalesInvoiceVo afterSalesInvoiceVo = new AfterSalesInvoiceVo();
					afterSalesInvoiceVo.setCompanyId(companyId);
					afterSalesInvoiceVo.setRelatedId(orderId);

					if (type == 539) {
						//查询是否有电子票，如果有就全退，且不存在纸质票
						afterSalesInvoiceVo.setInvoiceProperty(2);
						afterSalesInvoiceVo.setType(505);
						afterSalesInvoiceVoList = afterSalesInvoiceMapper.getAfterSalesInvoiceVos(afterSalesInvoiceVo);
						if (afterSalesInvoiceVoList == null || afterSalesInvoiceVoList.size() == 0) {
							List<Integer> typeList = new ArrayList<>();
							typeList.add(504);
							typeList.add(505);
							afterSalesInvoiceVo.setTypeList(typeList);
							afterSalesInvoiceVo.setOrderGoodsIdList(orderGoodsIdList);
							afterSalesInvoiceVo.setInvoiceProperty(null);
							afterSalesInvoiceVo.setType(null);
							afterSalesInvoiceVoList = afterSalesInvoiceMapper.getAfterSalesInvoiceVos(afterSalesInvoiceVo);
						}
					} else {
						List<Integer> typeList = new ArrayList<>();
						typeList.add(503);
						typeList.add(504);
						afterSalesInvoiceVo.setTypeList(typeList);
						afterSalesInvoiceVo.setOrderGoodsIdList(orderGoodsIdList);
						afterSalesInvoiceVo.setType(null);
						afterSalesInvoiceVoList = afterSalesInvoiceMapper.getAfterSalesInvoiceVos(afterSalesInvoiceVo);
					}
					if (afterSalesInvoiceVoList != null && afterSalesInvoiceVoList.size() > 0) {
						//过滤红字有效的是否已关联蓝子有效,或者蓝字有效关联了红字有效
						for (AfterSalesInvoiceVo asi : afterSalesInvoiceVoList) {
							List<RInvoiceJInvoice> rInvoiceJInvoiceList = rInvoiceJInvoiceMapper.getRInvoiceJInvoiceList(asi.getInvoiceId());
							if (rInvoiceJInvoiceList != null && rInvoiceJInvoiceList.size() > 0) {
								BigDecimal amount = rInvoiceJInvoiceMapper.getAfterInvoiceTotalAmount(asi.getInvoiceId());
								if (amount.compareTo(new BigDecimal(0)) > 0) {
									AfterSalesInvoice as = new AfterSalesInvoice();
									as.setAfterSalesId(afterSalesId);
									as.setInvoiceId(asi.getInvoiceId());
									as.setIsRefundInvoice(1);
									int re = afterSalesInvoiceMapper.insertSelective(as);
									if (re == 0) {
										return false;
									}
								}
							} else {
								AfterSalesInvoice as = new AfterSalesInvoice();
								as.setAfterSalesId(afterSalesId);
								as.setInvoiceId(asi.getInvoiceId());
								as.setIsRefundInvoice(1);
								int re = afterSalesInvoiceMapper.insertSelective(as);
								if (re == 0) {
									return false;
								}
							}
						}
					}
				}

			}

			//退换货,安调，维修手续费
			if (type == 539 || type == 540 || type == 546 || type == 547 || type == 541 || type == 584 || type == 550 || type == 585 || type == 4090 || type == 4091) {
				AfterSalesGoods asg = new AfterSalesGoods();
				asg.setAfterSalesId(afterSalesId);
				asg.setGoodsType(1);
				asg.setNum(1);
				asg.setGoodsId(thGoodsId);//特殊商品的id
				// add by franlin.wu for [编辑,逻辑保存不变] at 2018-12-18 begin
				if (2 == operaterType) {
					asg.setPrice(afterSalesVo.getServiceAmount());
				}
				// add by franlin.wu for [编辑,逻辑保存不变] at 2018-12-18 end
				int res2 = afterSalesGoodsMapper.insertSelective(asg);
				if (res2 == 0) {
					return false;
				}
			}
			//添加售后产品表信息----结束

			return true;
		}
	}

	private int getHistoryArrivalNum(Integer saleOrderGoodsId) {
		return afterSalesGoodsMapper.getSaleorderAfterSaleArrivalGoods(saleOrderGoodsId);
	}

	private int getHistoryReturnNum(Integer saleorderGoodsId) {
		return   afterSalesGoodsMapper.getSaleorderAftersaleReturnGoods(saleorderGoodsId);
	}

	@Override
	public void setRealRknum(Integer type,Integer saleorderGoodsId,AfterSalesGoods afterSalesGoods){
		if(SysOptionConstant.ID_539.equals(type) && !GoodsConstants.VIRTUAL_GOODS.contains(afterSalesGoods.getGoodsId())){
			//判断是否供应链维护虚拟商品，自动退货
			CoreSku coreSku = coreSkuMapper.selectByPrimaryKey(afterSalesGoods.getGoodsId());
			if(ErpConst.ONE.equals(coreSku.getIsVirtureSku())){
				afterSalesGoods.setRknum(ErpConst.ZERO);
				return;
			}
			SaleorderGoods saleorderGoods = saleorderGoodsMapper.selectByPrimaryKey(saleorderGoodsId);
			//实际应退数量
			int historyReturnNum = getHistoryReturnNum(saleorderGoods.getSaleorderGoodsId());
			// 本次退货数量
			int nowReturnNum = afterSalesGoods.getNum();
			// 当前销售单剩余总数 = 销售单原始销售数量 - 历史退货单的退货数量
			int curTotal = saleorderGoods.getNum() - historyReturnNum;
			// 当前客户剩余数量 = 销售已出货数量 - 历史已经退回数量 - 当前售后单退回数量(新增时为0)
			int curNoReturnNum = saleorderGoods.getDeliveryNum() - getHistoryArrivalNum(saleorderGoods.getSaleorderGoodsId());
			// 本次退货数量 - （X- X中已出库的数量），只下传大于0的数量
			int resultNum = (curTotal - curNoReturnNum) - nowReturnNum;
			logger.info("销售退货售后商品{},销售数量{},已售后数量{},已发货数量{},",saleorderGoods.getSaleorderGoodsId()
					,saleorderGoods.getNum(),saleorderGoods.getAfterReturnNum(),saleorderGoods.getDeliveryNum());
			if(resultNum < 0){
				afterSalesGoods.setRknum(Math.abs(resultNum));
			}else {
				afterSalesGoods.setRknum(ErpConst.ZERO);
			}
		}else if(SysOptionConstant.ID_540.equals(type) && !GoodsConstants.VIRTUAL_GOODS.contains(afterSalesGoods.getGoodsId())){
			afterSalesGoods.setRknum(afterSalesGoods.getNum());
		}else {
			afterSalesGoods.setRknum(0);
		}
	}

	@Override
	public Boolean checkWhenEditAfterSales(Integer afterSalesId) {
		AfterSales afterSales = afterSalesMapper.getAfterSalesById(afterSalesId);
		if (afterSales == null) {
			return false;
		}
		//售后单只能在待确认状态并且待审核或者审核不通过情况下才能编辑
		if (afterSales.getAtferSalesStatus() == 0 && (afterSales.getStatus() == 0 || afterSales.getStatus() == 3)) {
			return true;
		}
		return false;
	}

	@Override
	public ResultInfo saveDirectAfterSaleExchangeOutLog(List<AfterSaleBuyorderDirectOutLog> afterSaleBuyorderDirectOutLogs) {
		if(CollectionUtils.isEmpty(afterSaleBuyorderDirectOutLogs)){
			return ResultInfo.success();
		}
		for (AfterSaleBuyorderDirectOutLog saleBuyorderDirectOutLog : afterSaleBuyorderDirectOutLogs) {
			if(StringUtils.isNotBlank(saleBuyorderDirectOutLog.getProduceTimeStr())){
				saleBuyorderDirectOutLog.setProduceTime(DateUtil.convertLong(saleBuyorderDirectOutLog.getProduceTimeStr(),DateUtil.TIME_FORMAT));
			}
			if(StringUtils.isNotBlank(saleBuyorderDirectOutLog.getOutTimeStr())){
				saleBuyorderDirectOutLog.setOutTime(DateUtil.convertLong(saleBuyorderDirectOutLog.getOutTimeStr(),DateUtil.TIME_FORMAT));
			}
			if(StringUtils.isNotBlank(saleBuyorderDirectOutLog.getValidTimeStr())){
				saleBuyorderDirectOutLog.setValidTime(DateUtil.convertLong(saleBuyorderDirectOutLog.getValidTimeStr(),DateUtil.TIME_FORMAT));
			}
				afterSaleBuyorderDirectOutLogMapper.insertSelective(saleBuyorderDirectOutLog);
		}
		return ResultInfo.success();
	}

	/**
	 * 计算耗材的售后退款金额
	 *
	 * @param afterSalesVo
	 * @param afterSalesId
	 * @param nowReundSkuNumMap
	 * @param skuNumMap
	 * @return
	 * @throws RuntimeException
	 */
	private BigDecimal subReundAmountBySourceAndType(AfterSalesVo afterSalesVo, Integer afterSalesId, Map<Integer, Integer> nowReundSkuNumMap, Map<Integer, MapExtendsVo<Integer, BigDecimal>> skuNumMap) throws RuntimeException {
		// 订单来源
		Integer source = afterSalesVo.getSource();
		// 售后类型
		Integer type = afterSalesVo.getType();
		// 订单ID
		Integer orderId = afterSalesVo.getOrderId();

		// 涉及耗材售后订单 退款金额
		BigDecimal hcReundAmount = BigDecimal.ZERO;
		// 耗材退货
		if ((CommonConstants.ONE.equals(source) || afterSalesVo.getOrderType().equals(1)) && 539 == type) {
			//  计算当前销售订单下每个SalesGoodsId已退货金额
			Map<Integer, MapExtendsVo<Integer, BigDecimal>> everySkuReundAmountMap = subSkuReundAmountByOrderId(orderId);

			Integer saleorderType = saleorderMapper.getSaleorderBySaleorderId(orderId).getOrderType();

			// 遍历 当前退货商品
			Iterator<Map.Entry<Integer, Integer>> iterator = nowReundSkuNumMap.entrySet().iterator();
			// 遍历
			while (iterator.hasNext()) {
				Map.Entry<Integer, Integer> entry = iterator.next();
				// 空，则继续
				if (null == entry || null == entry.getKey() || null == entry.getValue()) {
					continue;
				}
				// 获取sa
				Integer goodsId = entry.getKey();
				// 当前SalesGoodsId的退货数量
				Integer reundSkuNum = entry.getValue();
				// 获取销售订单的商品信息
				MapExtendsVo<Integer, BigDecimal> orderSku = skuNumMap.get(goodsId);
				// 校验不通过
				if (null == orderSku || null == orderSku.getExData() || orderSku.getExData() < reundSkuNum) {
					throw new RuntimeException("当前退货数量大于销售订单销售数量");
				}
				// 当前销售订单的sku的数量
				Integer skuNum = orderSku.getExData();
				// 当前SalesGoodsId的最大退款金额
				BigDecimal skuTotalAmount = orderSku.getValue();
				// 当前sku已退数量
				BigDecimal alReturnNum = BigDecimal.ZERO;
				// 退货金额
				BigDecimal alReturnAmount = BigDecimal.ZERO;
				// 已经退货数量和退货金额
				MapExtendsVo<Integer, BigDecimal> alReundNumAndAmount = (null == everySkuReundAmountMap ? null : everySkuReundAmountMap.get(goodsId));
				// 非空
				if (null != alReundNumAndAmount) {
					// 退货数量
					alReturnNum = null == alReundNumAndAmount.getExData() ? alReturnNum : new BigDecimal(alReundNumAndAmount.getExData());
					// 退货金额
					alReturnAmount = null == alReundNumAndAmount.getValue() ? alReturnAmount : alReundNumAndAmount.getValue();
				}

				SaleorderGoods saleorderGoods = saleorderGoodsMapper.getSaleorderGoodsByIdSample(goodsId);

				// 当前SalesGoodsId的退款小计
				BigDecimal nowSkuReundAmount = SaleAfterServiceUtil.subRefundAmount(new BigDecimal(reundSkuNum), new BigDecimal(skuNum),
						alReturnNum, skuTotalAmount, alReturnAmount,saleorderGoods);

				AfterSalesGoods record = new AfterSalesGoods();
				record.setOrderDetailId(goodsId);
				record.setAfterSalesId(afterSalesId);
				record.setSkuRefundAmount(nowSkuReundAmount);
				record.setSkuOldRefundAmount(nowSkuReundAmount);
				// 更新当前SalesGoodsId退货金额
				afterSalesGoodsMapper.updateByGoodsIdAndAfterSalesId(record);

				// 累计
				hcReundAmount = hcReundAmount.add(nowSkuReundAmount);
			}

		}
		return hcReundAmount;
	}

	/**
	 * 计算当前销售订单下每个商品id已退货金额
	 *
	 * @param orderId
	 * @return
	 */
	private Map<Integer, MapExtendsVo<Integer, BigDecimal>> subSkuReundAmountByOrderId(Integer orderId) {
		if (null != orderId) {
			AfterSalesVo afterSalesVo = new AfterSalesVo();
			afterSalesVo.setOrderId(orderId);
			// 审核通过
			afterSalesVo.setAtferSalesStatus(2);
			// 已完结
			afterSalesVo.setStatus(2);
			afterSalesVo.setType(539);
			// getAfterSalesVoListByOrderId
			List<AfterSalesVo> afterList = getAfterSalesVoListByOrderId(afterSalesVo);
			// 非空
			if (org.apache.commons.collections.CollectionUtils.isNotEmpty(afterList)) {
				// 统计各sku的
				Map<Integer, MapExtendsVo<Integer, BigDecimal>> skuMap = new HashMap<Integer, MapExtendsVo<Integer, BigDecimal>>();
				// 根据afterSalesId获取当前售后订单商品（不包含特殊商品）
				List<AfterSalesGoods> afterSalesGoodsList = afterSalesGoodsMapper.selectByAfterSalesId(afterList, 693);

				for (AfterSalesGoods asg : afterSalesGoodsList) {
					if (null == asg) {
						continue;
					}
					// 商品ID
					Integer goodsId = asg.getOrderDetailId();
					// 定义 当前sku的退款金额
					BigDecimal alSkuReundAmount = BigDecimal.ZERO;
					// 当前sku的退款金额 不是null则赋予
					if (null != asg.getSkuRefundAmount()) {
						alSkuReundAmount = asg.getSkuRefundAmount();
					}
					// 定义当前退货数量
					Integer num = 0;
					if (null != asg.getNum()) {
						num = asg.getNum();
					}
					// Integer 退货数量 BigDecimal 退货金额
					MapExtendsVo<Integer, BigDecimal> mapExVo = new MapExtendsVo<Integer, BigDecimal>();
					// 根据goodsId区分
					if (null == skuMap.get(goodsId)) {
						// 退货数量
						mapExVo.setExData(num);
						// 退款金额
						mapExVo.setValue(alSkuReundAmount);
					} else {
						// 退货数量
						mapExVo.setExData(num + skuMap.get(goodsId).getExData());
						// 退款金额
						mapExVo.setValue(alSkuReundAmount.add(skuMap.get(goodsId).getValue()));
					}

					skuMap.put(goodsId, mapExVo);
				}
				return skuMap;
			}
		}
		return null;
	}

	/**
	 * 添加售后详情
	 *
	 * @param afterSalesVo
	 * @param afterSalesId
	 * @param source
	 * @param hcReundAmount
	 * @param operateType
	 * @return
	 */
	private boolean addAfterSaleDetails(AfterSalesVo afterSalesVo, Integer afterSalesId, Integer source,
										BigDecimal hcReundAmount, int operateType) {
		logger.info("添加售后详情信息入参 afterSalesVo:{},afterSalesId:{},source:{},hcReundAmount:{}, operateType:{}",
				JSON.toJSONString(afterSalesVo), afterSalesId, source, hcReundAmount, operateType);
		// 订单ID
		Integer orderId = afterSalesVo.getOrderId();
		// 售后类型
		Integer type = afterSalesVo.getType();
		// 售后主体
		Integer subjectType = afterSalesVo.getSubjectType();

		//添加详情表信息----开始
		AfterSalesDetail afterSalesDetail = new AfterSalesDetail();

		//售后联系人名和电话
        afterSalesDetail.setAfterConnectUserName(afterSalesVo.getAfterConnectUserName());
        afterSalesDetail.setAfterConnectPhone(afterSalesVo.getAfterConnectPhone());
        afterSalesDetail.setFirstResponsibleDepartment(afterSalesVo.getFirstResponsibleDepartment());
		/****************计算实退金额开始************************/
		Integer afterSalesType = 543;
		if (CommonConstants.ONE.equals(source) || (afterSalesVo.getOrderType().equals(1) && (!afterSalesType.equals(type)))) {
			//退款金额
			afterSalesDetail.setRefundAmount(hcReundAmount);
		} else {
			afterSalesDetail.setRefundAmount(afterSalesVo.getRefundAmount());//退款金额
		}
		// add by for franlin.wu [耗材售后] at 2018-12-18 end
		afterSalesDetail.setTraderMode(afterSalesVo.getTraderMode());//交易方式（字典表查）
		// 销售订单退款 or 第三方退款 or 耗材 && 线上
		if (type == 543 || type == 551) {
			// 款项退还0无 1退到客户余额 2退给客户
			afterSalesDetail.setRefund(2);
		} else {
			afterSalesDetail.setRefund(afterSalesVo.getRefund());
		}
		// 退款状态0无退款1未退款 2部分退款 3已退款
		afterSalesDetail.setRefundAmountStatus(afterSalesVo.getRefundAmountStatus());
//		//之前售后订单的已退款总额
//		BigDecimal hasRefunded = afterSalesMapper.getSumTKAfterSalesBySaleorderid(afterSales.getOrderId());
		//销售---售后服务费>0
		if (subjectType == 535) {
			if (type == 539) {
				// 订单已付款金额，不含账期
				BigDecimal pa = saleorderDataService.getPaymentAndPeriodAmount(orderId);
				// 已付款金额（不含账期）
				afterSalesDetail.setPaymentAmount(pa);
				// 耗材售后
				if (CommonConstants.ONE.equals(source)) {
					// 根据订单ID查询销售订单信息
					Saleorder saleorder = saleorderMapper.selectByPrimaryKey(orderId);
					// 空
					if (null == saleorder) {
						return false;
					}
					// 支付方式：0线上、1线下  默认1
					Integer paymentMode = saleorder.getPaymentMode();
					// 支付方式：1支付宝、2微信、3银行
					Integer payType = saleorder.getPayType();
					// 默认 退给客户
					if (CommonConstants.ZERO.equals(paymentMode)) {
						afterSalesDetail.setRefund(2);
					}
					// 默认0
					Integer traderMode = Constants.ONE.equals(payType) ? 520 : Constants.TWO.equals(payType) ? 522 : Constants.THREE.equals(payType) ? 521 : 0;
					// 交易方式
					afterSalesDetail.setTraderMode(traderMode);
					//实退金额
					//查询当前订单的所有已完结退货总额
					BigDecimal tk = afterSalesMapper.getSumTKAfterSalesBySaleorderid(orderId);
					//账期金额
					//BigDecimal perid = saleorderDataService.getPeriodAmount(afterSales.getOrderId());
					///账期欠款金额
					BigDecimal lackperid = saleorderDataService.getLackAccountPeriodAmount(orderId);
					BigDecimal PreAnnealing = pa.add(lackperid).add(afterSalesDetail.getRefundAmount()).subtract(saleorder.getTotalAmount().subtract(tk));
					BigDecimal payPeriod = new BigDecimal(0);//偿还帐期金额
					if (PreAnnealing.compareTo(lackperid) <= 0 && lackperid.compareTo(new BigDecimal(0)) > 0) {
						payPeriod = PreAnnealing;
					} else if (lackperid.compareTo(BigDecimal.ZERO) > 0) {
						payPeriod = lackperid;
					} else {
						payPeriod = BigDecimal.ZERO;
					}
					afterSalesDetail.setPayPeriodAmount(payPeriod);
					logger.info("订单ID" + orderId + "售后单ID" + afterSalesId + "实退金额=" + PreAnnealing + "-" + payPeriod);
					if (PreAnnealing.compareTo(new BigDecimal(0)) > 0) {//预付款大于0
						afterSalesDetail.setRealRefundAmount(PreAnnealing.subtract(payPeriod));//实退金额
						if (afterSalesDetail.getRealRefundAmount().compareTo(new BigDecimal(0)) > 0) {
							afterSalesDetail.setRefundAmountStatus(1);
						}
					} else {
						afterSalesDetail.setRealRefundAmount(new BigDecimal(0));
					}
					// 实付金额大于0则 修改退款状态：未退款
				} else {
					//查询当前订单的所有已完结退货总额
					BigDecimal tk = afterSalesMapper.getSumTKAfterSalesBySaleorderid(orderId);
					//账期欠款金额
					BigDecimal lackperid = saleorderDataService.getLackAccountPeriodAmount(orderId);
					Saleorder saleorder = saleorderMapper.selectByPrimaryKey(orderId);

					//支付宝提现
					BigDecimal zhifubao = capitalBillMapper.getzhifubaoAmount(orderId);

					// 预退款金额=订单已付款金额+退货产品金额-订单金额，（此处订单已付款包含帐期支付）。如果预退款金额<=0时，按0处理，即不退款，偿还帐期金额=实际退款金额=0；
//					BigDecimal PreAnnealing = pa.add(lackperid).add(afterSalesDetail.getRefundAmount()).subtract(saleorder.getTotalAmount().subtract(tk).subtract(zhifubao));

					BigDecimal PreAnnealing = pa.add(lackperid).add(afterSalesDetail.getRefundAmount()).subtract(saleorder.getTotalAmount().subtract(tk));
					BigDecimal payPeriod = new BigDecimal(0);//偿还帐期金额
					if (PreAnnealing.compareTo(lackperid) <= 0 && lackperid.compareTo(new BigDecimal(0)) > 0) {
						payPeriod = PreAnnealing;
					} else if (lackperid.compareTo(BigDecimal.ZERO) > 0) {
						payPeriod = lackperid;
					} else {
						payPeriod = BigDecimal.ZERO;
					}
					afterSalesDetail.setPayPeriodAmount(payPeriod);
					logger.info("2订单ID" + orderId + "售后单ID" + afterSalesId + "实退金额=" + PreAnnealing + "-" + payPeriod);
					if (PreAnnealing.compareTo(BigDecimal.ZERO) > 0) {//预付款大于0
						afterSalesDetail.setRealRefundAmount(PreAnnealing.subtract(payPeriod));//实退金额
						if (afterSalesDetail.getRealRefundAmount().compareTo(BigDecimal.ZERO) > 0) {
							afterSalesDetail.setRefundAmountStatus(1);
						}
					} else {
						afterSalesDetail.setRealRefundAmount(BigDecimal.ZERO);
					}
				}
			} else if (type == 543) {
				//实退金额
				afterSalesDetail.setRealRefundAmount(afterSalesDetail.getRefundAmount());
			}
			//采购---售后服务费<0
		} else if (subjectType == 536 && type == 546) {
			//订单已付款金额，不含账期
			BigDecimal pa = buyorderDataService.getPaymentAndPeriodAmount(orderId).abs();
			//VDERP-8909 修复付款金额未扣除手续费
//			BigDecimal afterSaleServiceAmount = buyorderDataService.getAfterSaleServiceAmount(orderId);
//			pa = pa.subtract(afterSaleServiceAmount);
			afterSalesDetail.setPaymentAmount(pa);
			//账期金额
			//BigDecimal perid = buyorderDataService.getPeriodAmount(afterSales.getOrderId());
			//账期欠款金额
			BigDecimal lackperid = buyorderDataService.getLackAccountPeriodAmount(orderId);
			Buyorder buyorder = buyorderMapper.selectByPrimaryKey(orderId);
			//查询当前采购订单的所有已完结退货售后
			BigDecimal tk = afterSalesMapper.getSumTKAfterSalesByBuyorderid(orderId);


			// 预退款金额=订单已付款金额+退货产品金额-订单金额，（此处订单已付款包含帐期支付）。如果预退款金额<=0时，按0处理，即不退款，偿还帐期金额=实际退款金额=0；
			BigDecimal PreAnnealing = pa.add(lackperid).add(afterSalesDetail.getRefundAmount()).subtract(buyorder.getTotalAmount().subtract(tk));
			logger.info("{}订单已付款金额：{},本次退款金额：{}，订单金额:{},已退金额：{}",orderId,pa.add(lackperid),afterSalesDetail.getRefundAmount(),buyorder.getTotalAmount(),tk);
			//BigDecimal PreAnnealing = pa.add(buyorder.getAccountPeriodAmount()).add(afterSalesDetail.getRefundAmount()).subtract(buyorderDataService.getRealAmount(afterSales.getOrderId())).subtract(tk);
			//偿还帐期金额
			BigDecimal payPeriod = new BigDecimal(0);
			if (PreAnnealing.compareTo(lackperid) <= 0 && lackperid.compareTo(new BigDecimal(0)) > 0) {
				payPeriod = PreAnnealing;
			} else if (lackperid.compareTo(new BigDecimal(0)) > 0) {
				payPeriod = lackperid;
			} else {
				payPeriod = new BigDecimal(0);
			}
			afterSalesDetail.setPayPeriodAmount(payPeriod);
			//预付款大于0
			if (PreAnnealing.compareTo(new BigDecimal(0)) > 0) {
				//实退金额
				afterSalesDetail.setRealRefundAmount(PreAnnealing.subtract(payPeriod));
				afterSalesDetail.setRefundAmountStatus(-1);
			} else {
				afterSalesDetail.setRealRefundAmount(new BigDecimal(0));
				afterSalesDetail.setRefundAmountStatus(-1);
			}

		} else if (subjectType == 537 && type == 551) {
			//实退金额
			afterSalesDetail.setRealRefundAmount(afterSalesVo.getRefundAmount());
		}
		// add by franlin.wu for [兼容编辑] at 2018-12-18 begin
		else if (2 == operateType && subjectType == 537 && (550 == type || 585 == type)) {
			afterSalesDetail.setServiceAmount(afterSalesVo.getServiceAmount());
			afterSalesDetail.setInvoiceType(afterSalesVo.getInvoiceType());
			afterSalesDetail.setIsSendInvoice(afterSalesVo.getIsSendInvoice());
		}
		// add by franlin.wu for [兼容编辑] at 2018-12-18 end
//		if(realRefundAmount.compareTo(new BigDecimal(0)) > 0){
//			afterSalesDetail.setRefundAmountStatus(1);
//		}
		/****************计算实退金额结束************************/
		// add by franlin.wu for [兼容编辑] at 2018-12-18 begin
		if (2 == operateType && (type == 546 || type == 547)) {
			afterSalesDetail.setServiceAmount(afterSalesVo.getServiceAmount());
			afterSalesDetail.setInvoiceType(afterSalesVo.getInvoiceType());
			afterSalesDetail.setIsSendInvoice(afterSalesVo.getIsSendInvoice());
		}
		// add by franlin.wu for [兼容编辑] at 2018-12-18 end

		afterSalesDetail.setAfterSalesId(afterSalesId);
		afterSalesDetail.setComments(afterSalesVo.getComments());
		afterSalesDetail.setReason(afterSalesVo.getReason());

		afterSalesDetail.setTraderId(afterSalesVo.getTraderId());
		if(548 != afterSalesVo.getType()) {
			//采购售后仅退票不存在联系人信息，此逻辑无需进行
			TraderContact traderContact = null;
			if (afterSalesVo.getTraderContactId() != null && afterSalesVo.getTraderContactId() != 0) {
				traderContact = traderContactMapper.selectByPrimaryKey(afterSalesVo.getTraderContactId());
			} else if (afterSalesVo.getTraderId() != null && afterSalesVo.getTraderId() != 0) {
				traderContact = new TraderContact();
				traderContact.setTraderId(afterSalesVo.getTraderId());
				traderContact.setTraderType(1);
				traderContact.setIsDefault(1);
				//2018-2-7取第一条数据，兼容老数据
				traderContact = traderContactMapper.getTraderContactVo(traderContact);
			}
			if (traderContact != null) {
				afterSalesDetail.setTraderContactId(afterSalesVo.getTraderContactId());
				afterSalesDetail.setTraderContactMobile(traderContact.getMobile());
				afterSalesDetail.setTraderContactName(traderContact.getName());
				afterSalesDetail.setTraderContactTelephone(traderContact.getTelephone());
				afterSalesDetail.setTraderId(traderContact.getTraderId());
			} else {
				afterSalesDetail.setTraderContactName(afterSalesVo.getTraderContactName());
				afterSalesDetail.setTraderContactMobile(afterSalesVo.getTraderContactMobile());
			}
			if (afterSalesVo.getTakeMsg() != null && !"".equals(afterSalesVo.getTakeMsg()) && afterSalesVo.getTakeMsg().split("\\|").length == 4) {
				afterSalesDetail.setAddressId(Integer.valueOf(afterSalesVo.getTakeMsg().split("\\|")[0]));
				afterSalesDetail.setAreaId(Integer.valueOf(afterSalesVo.getTakeMsg().split("\\|")[1]));
				afterSalesDetail.setArea(afterSalesVo.getTakeMsg().split("\\|")[2]);
				afterSalesDetail.setAddress(afterSalesVo.getTakeMsg().split("\\|")[3]);
			}
			if (notEmpty(afterSalesVo.getProvince())) {
				if (notEmpty(afterSalesVo.getZone())) {
					afterSalesDetail.setAreaId(Integer.valueOf(afterSalesVo.getZone()));
				} else if (notEmpty(afterSalesVo.getCity()) && !notEmpty(afterSalesVo.getZone())) {
					afterSalesDetail.setAreaId(Integer.valueOf(afterSalesVo.getCity()));
				} else {
					afterSalesDetail.setAreaId(Integer.valueOf(afterSalesVo.getProvince()));
				}
				afterSalesDetail.setArea(afterSalesVo.getArea());
				afterSalesDetail.setAddress(afterSalesVo.getAddress());
			}
			if (afterSalesVo.getTraderSubject() != null && afterSalesVo.getTraderSubject() != 0) {
				afterSalesDetail.setTraderSubject(afterSalesVo.getTraderSubject());
				afterSalesDetail.setBank(afterSalesVo.getBank());
				afterSalesDetail.setBankAccount(afterSalesVo.getBankAccount());
				afterSalesDetail.setBankCode(afterSalesVo.getBankCode());

			}
			afterSalesDetail.setPayee(afterSalesVo.getPayee());

			// 由于不同的退款业务逻辑，refund退款路径会在上文再次变更，因此我们需要在此处最后来统一判断（页面上选择款项退还路径并不是最后的结果）
			setRefundComment(afterSalesVo, afterSalesDetail.getRefund());
			afterSalesDetail.setRefundComment(afterSalesVo.getRefundComment());
		}
		Integer afterSalesDetailId;
		// add by franlin.wu for [@1:兼容编辑| @2:避免执行退款运算时，当前的traderId为空，应该是之前查询traderId] at 2018-12-20/2019-04-22[modify@2] begin
		int res1 = 0;
		// 2 编辑
		if (CommonConstants.TWO.equals(operateType)) {
			// 编辑时页面没有传 afterSalesVo.getTraderId()
			afterSalesDetail.setAfterSalesDetailId(afterSalesVo.getAfterSalesDetailId());
			res1 = afterSalesDetailMapper.updateByPrimaryKeySelective(afterSalesDetail);
			afterSalesDetailId = afterSalesVo.getAfterSalesDetailId();
		} else {
			res1 = afterSalesDetailMapper.insertSelective(afterSalesDetail);
			afterSalesDetailId = afterSalesDetail.getAfterSalesDetailId();
		}
		if( type == 546){
			AfterSalesDetail detail = new AfterSalesDetail();
			detail.setAfterSalesDetailId(afterSalesDetailId);
			detail.setAfterSalesId(afterSalesDetail.getAfterSalesId());
			preExecute(detail);
		}

		// add by franlin.wu for [@1:兼容编辑| @2:避免执行退款运算时，当前的traderId为空，应该是之前查询traderId] at 2018-12-20/2019-04-22[modify@2] end
		return res1 != 0;
	}

	private void preExecute(AfterSalesDetail afterSalesDetail){
		try{
			// 上下文参数
			AfterSales queryAfterSales = new AfterSales();
			queryAfterSales.setAfterSalesId(afterSalesDetail.getAfterSalesId());
			AfterSalesVo afterSalesVo = afterSalesMapper.viewAfterSalesDetailBuyorder(queryAfterSales);

			ExecuteBuyorderReturnDto returnDto = new ExecuteBuyorderReturnDto();
			// 是否含返利结算
			Boolean isRebateSettlement = isRebateSettlement(afterSalesVo.getType(),afterSalesVo.getOrderId(),returnDto,afterSalesDetail.getAfterSalesId());
			// 预退金额（不含返利）
			BigDecimal refundNoRebateMoney = this.refundNoRebateMoney(afterSalesVo,returnDto);
			BigDecimal payPeriodAmount = getPeriodAmount(returnDto, refundNoRebateMoney, afterSalesVo);
			BigDecimal realRefundAmount = calRealRefundAmount(returnDto, refundNoRebateMoney, afterSalesVo);
			afterSalesDetail.setPayPeriodAmount(payPeriodAmount);
			afterSalesDetail.setRealRefundAmount(realRefundAmount);


			int updateByPrimaryKeySelective = afterSalesDetailMapper.updateByPrimaryKeySelective(afterSalesDetail);
			logger.info("添加售后，预执行退款运算，执行结果：{}:入参：{}",updateByPrimaryKeySelective,JSON.toJSONString(afterSalesDetail));
		}catch (Exception e){
			logger.error("添加售后，预执行退款运算，执行错误",e);
		}

	}

	//添加退款特别提醒
	private void setRefundComment(AfterSalesVo afterSalesVo, Integer realRefund) {
		// 先置为空，满足条件再提示，否则每个if后都需要加个else置空
		afterSalesVo.setRefundComment("");
		if (afterSalesVo.getType() == 539 && realRefund != null && realRefund == 2) {
			Saleorder saleorder = saleorderService.getSaleOrderById(afterSalesVo.getSaleorderId());
			if (saleorder.getPaymentMode() == 0) {
				//获取交易流水大于一年
				CapitalBillDetailDto CapitalBillDetailDto = new CapitalBillDetailDto();
				// 销售订单类型
				CapitalBillDetailDto.setOrderType(ErpConst.ONE);
				CapitalBillDetailDto.setOrderNo(saleorder.getSaleorderNo());
				CapitalBillDetailDto.setRelatedId(afterSalesVo.getSaleorderId());
				List<CapitalBillDto> finance_sale_detail = capitalBillApiService.getCapitalBillData(CapitalBillDetailDto, "finance_sale_detail");
				if (CollUtil.isNotEmpty(finance_sale_detail)) {
					CapitalBillDto capitalBillDto = finance_sale_detail.get(0);
					Long traderTime = capitalBillDto.getTraderTime();
					Date traderDate = cn.hutool.core.date.DateUtil.date(traderTime);
					Date newDate = cn.hutool.core.date.DateUtil.offset(traderDate, DateField.YEAR, 1);
					Date nowDate = cn.hutool.core.date.DateUtil.date();
					long betweenDay = cn.hutool.core.date.DateUtil.between(
							cn.hutool.core.date.DateUtil.endOfDay(nowDate),
							cn.hutool.core.date.DateUtil.endOfDay(newDate),
							cn.hutool.core.date.DateUnit.DAY, Boolean.FALSE);
					if (betweenDay < 0) {
						afterSalesVo.setRefundComment("关联交易流水已经超过一年，无法原路退款，请与客户确认退款路径");
					}
				}
			}
		}
	}

	@Override
	public ResultInfo updateLockSaleorderWarning(Integer saleorderId) {
		try {
			List<SaleorderGoods> saleorderGoodsLists = saleorderMapper.getSaleorderGoodsBySaleOrderId(saleorderId);
			if (CollectionUtils.isEmpty(saleorderGoodsLists)){
				return ResultInfo.success("锁定更新销售单预警成功");
			}
			saleorderGoodsLists.forEach(item -> {
				SaleorderGoodsVo saleorderGoodsVo = new SaleorderGoodsVo();
				saleorderGoodsVo.setSaleorderGoodsId(item.getSaleorderGoodsId());
				saleorderGoodsVo.setWarnLevel(null);
				saleorderGoodsMapper.updateWarnStatusSelective(saleorderGoodsVo);
			});
		} catch (Exception e) {
			logger.error("锁定销售单时，清除预警失败，销售单ID：{},错误原因：{}", saleorderId, e);
			return new ResultInfo(-1, "锁定更新销售单预警失败");
		}
		return new ResultInfo(0, "锁定更新销售单预警成功");
	}

	public static Object[] oneClear(Object[] arr) {
		Set set = new HashSet();

		Arrays.asList(arr).stream().forEach(x -> set.add(x));

		return set.toArray();
	}

	/**
	 * <b>Description:</b><br> 判断整型数据
	 *
	 * @param id
	 * @return
	 * @Note <b>Author:</b> east
	 * <br><b>Date:</b> 2017年10月13日 下午2:03:59
	 */
	private boolean notEmpty(Integer id) {
		if (id == null || id == 0) {
			return false;
		} else {
			return true;
		}
	}


	/**
	 * <b>Description:</b><br> 根据当前的销售人员获取相应的售后人员
	 * @param user
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年12月25日 下午3:38:32
	 */
	public Integer getAfterSalesServiceUser(User user){
		//获取当前销售人员的一级部门的orgid
		Integer userId = 0;
		Integer orgId = user.getOrgId();

//		List<Organization> orgList = orgService.getParentOrgList(user.getOrgId());//根据当前部门ID查询所有上级集合
//		for (Organization org : orgList) {
//			if(org.getLevel() == 5 || org.getLevel() == 4){
//				orgId = org.getOrgId();
//				break;
//			}
//		}

		// add by franlin.wu for [避免空指针异常] at 2018-12-15 begin
		if(null != orgId && orgId != 0)
		// add by franlin.wu for [避免空指针异常] at 2018-12-15 end
		{
//			ParamsConfigVo paramsConfigVo = new ParamsConfigVo();
//			paramsConfigVo.setCompanyId(user.getCompanyId());
//			paramsConfigVo.setParamsKey(109);
//			paramsConfigVo.setTitle(orgId.toString());
//			ParamsConfigVo aftersales = paramsConfigMapper.getParamsConfigVoByParamsKey(paramsConfigVo);
			ParamsConfigVo aftersales =getparamsConfig(orgId,user.getCompanyId());
			if(aftersales == null){
				ParamsConfigVo paramsConfigVo = new ParamsConfigVo();
				paramsConfigVo.setCompanyId(user.getCompanyId());
				paramsConfigVo.setTitle(null);
				paramsConfigVo.setParamsKey(108);
				aftersales = paramsConfigMapper.getParamsConfigVoByParamsKey(paramsConfigVo);
			}
			if(aftersales != null){
				User us = userMapper.getByUsernameEnable(aftersales.getParamsValue(), user.getCompanyId());
				userId = us == null?0:us.getUserId();
			}
		}
		return userId;
	}

	public ParamsConfigVo getparamsConfig(Integer orgId,Integer companyId){
		ParamsConfigVo paramsConfigVo = new ParamsConfigVo();
		paramsConfigVo.setCompanyId(companyId);
		paramsConfigVo.setParamsKey(109);
		paramsConfigVo.setTitle(orgId.toString());
		ParamsConfigVo aftersales = paramsConfigMapper.getParamsConfigVoByParamsKey(paramsConfigVo);
		if(aftersales != null){
			return aftersales;
		}
		List<Organization> orgList = orgService.getParentOrgList(orgId);
		//取orgList，从最后一条逐个循环
		for (int i = orgList.size()-1; i >= 0; i--) {
			paramsConfigVo.setTitle(orgList.get(i).getOrgId().toString());
			aftersales = paramsConfigMapper.getParamsConfigVoByParamsKey(paramsConfigVo);
			if(aftersales != null){
				return aftersales;
			}
		}
		return null;


	}

	@Override
	public void saveVerifiesNotPassReason(Integer idValue, String comment) {
		afterSalesMapper.saveVerifiesNotPassReason(idValue,comment);
	}

	/**
	 * <b>Description:</b><br> 保存编辑售后
	 * @param afterSalesVo
	 * @param user
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年10月11日 下午4:28:14
	 */
	@Override
	public ResultInfo<?> saveEditAfterSales(AfterSalesVo afterSalesVo, User user) {
		afterSalesVo.setUpdater(user.getUserId());
		afterSalesVo.setModTime(DateUtil.sysTimeMillis());
		if(ObjectUtils.notEmpty(afterSalesVo.getZone())){
			afterSalesVo.setArea(getAddressByAreaId(afterSalesVo.getZone()));
			afterSalesVo.setAreaId(afterSalesVo.getZone());
		}else if(ObjectUtils.notEmpty(afterSalesVo.getCity()) && ObjectUtils.isEmpty(afterSalesVo.getZone())){
			afterSalesVo.setArea(getAddressByAreaId(afterSalesVo.getCity()));
		}else if(ObjectUtils.notEmpty(afterSalesVo.getProvince()) && ObjectUtils.isEmpty(afterSalesVo.getCity()) && ObjectUtils.isEmpty(afterSalesVo.getZone())){
			afterSalesVo.setArea(getAddressByAreaId(afterSalesVo.getProvince()));
		}
		boolean pass = check(afterSalesVo);
		if(!pass){
			return ResultInfo.error("编辑提交失败");
		}

		String url = httpUrl + ErpConst.SAVE_EDIT_AFTERSALES;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, afterSalesVo, clientId, clientKey, TypeRef2);
			if(result != null && result.getCode().equals(0)){
			Integer afterId = (Integer) result.getData();
				//更新售后单updataTime
				orderCommonService.updateAfterOrderDataUpdateTime(afterId,null,OrderDataUpdateConstant.AFTER_ORDER_GENERATE);
			}
			if(afterSalesVo.getType().equals(548)){
				AfterBuyorderInvoiceDto afterBuyorderInvoiceDto = new AfterBuyorderInvoiceDto();
				afterBuyorderInvoiceDto.setAfterBuyorderInvoiceId(afterSalesVo.getAfterBuyorderInvoiceId());
				afterBuyorderInvoiceDto.setInvoiceNo(afterSalesVo.getNewInvoiceNo());
				afterBuyorderInvoiceDto.setInvoiceCode(afterSalesVo.getNewInvoiceCode());
				afterBuyorderInvoiceDto.setInvoiceType(afterSalesVo.getInvoiceType());
				SysOptionDefinition sysOptionDefinition = sysOptionDefinitionMapper.selectByPrimaryKey(afterSalesVo.getInvoiceType());
				if(sysOptionDefinition != null) {
					afterBuyorderInvoiceDto.setRatio(new BigDecimal(sysOptionDefinition.getComments()));
				}
				buyorderAfterSalesApiService.updateByPrimaryKeySelective(afterBuyorderInvoiceDto);
				//采购仅退票售后编辑后，订单置为待确认状态
				AfterSales afterSales = new AfterSales();
				afterSales.setAfterSalesId(afterSalesVo.getAfterSalesId());
				afterSales.setAtferSalesStatus(ErpConst.ZERO);
				afterSalesMapper.updateByPrimaryKeySelective(afterSales);
			}
			if( afterSalesVo.getType() == 546){
				AfterSalesDetail detail = new AfterSalesDetail();
				detail.setAfterSalesDetailId(afterSalesVo.getAfterSalesDetailId());
				detail.setAfterSalesId(afterSalesVo.getAfterSalesId());
				preExecute(detail);
			}
			return result;
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}

	private boolean check(AfterSalesVo afterSalesVo){
		AfterSalesDetailVo afterSalesInfo = afterSalesDetailMapper.getAfterSalesInfoByAfterSalesId(afterSalesVo.getAfterSalesId());
		if (afterSalesInfo.getAfterType().intValue() != 546){
			return Boolean.TRUE;
		}
		// 采购退货
		if(Objects.isNull(afterSalesVo.getTraderContactId()) || StringUtils.isBlank(afterSalesVo.getTakeMsg())){
			logger.info("{}保存编辑采购售后失败，联系人和收货地址不能为空：{}，{}",afterSalesVo.getAfterSalesNo(),afterSalesVo.getTraderContactId(),afterSalesVo.getTakeMsg());
		    return Boolean.FALSE;
		}
		if(!Objects.isNull(afterSalesInfo.getRefundAmountStatus())){
			logger.info("{}编辑2按钮校验，已执行退款运算，无法编辑提交，当前退款状态{}",afterSalesVo.getAfterSalesNo(),afterSalesInfo.getRefundAmountStatus());
			return Boolean.FALSE;
		}
		return Boolean.TRUE;
	}

	/**
	 * <b>Description:</b><br> 获取售后的订单详情
	 * @param afterSales
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年10月17日 下午2:01:58
	 */
	@Override
	public AfterSalesVo getAfterSalesVoDetail(AfterSalesVo afterSales) {
		try {
			AfterSalesVo afterSalesVo = null;
			AfterSalesDetail afterSalesDetail=new AfterSalesDetail();
//			setTraderType(afterSales);			

			if(afterSales != null && afterSales.getTraderType()!= null && afterSales.getTraderType()==1){
				//销售的售后详情
				afterSalesVo = afterSalesMapper.viewAfterSalesDetailSaleorder(afterSales);
				afterSalesVo.setTraderType(afterSales.getTraderType());
			}else if(afterSales != null && afterSales.getTraderType()!= null && afterSales.getTraderType()==2){
				//采购的售后详情
				afterSalesVo = afterSalesMapper.viewAfterSalesDetailBuyorder(afterSales);
				afterSalesVo.setTraderType(afterSales.getTraderType());
			}else {
				//第三方的售后详情
				afterSalesVo = afterSalesMapper.viewAfterSalesDetailThird(afterSales);
				afterSalesVo.setTraderType(afterSales.getTraderType());
			}
			if(afterSalesVo.getServiceUserId() != 0){
				afterSalesVo.setServiceUserName(getUserNameByUserId(afterSalesVo.getServiceUserId()));
			}
			switch (AfterSalesProcessEnum.getInstance(afterSalesVo.getType())){
				case AFTERSALES_TH :{
					BigDecimal pa = saleorderDataService.getPaymentAndPeriodAmount(afterSalesVo.getOrderId());
					afterSalesVo.setPayAmount(null == pa ? BigDecimal.ZERO : pa);
					afterSalesDetail.setPaymentAmount(null == pa ? BigDecimal.ZERO : pa);
					afterSalesDetail.setAfterSalesDetailId(afterSalesVo.getAfterSalesDetailId());
					afterSalesDetailMapper.updateByPrimaryKeySelective(afterSalesDetail);
					Saleorder saleorder = saleorderMapper.selectByPrimaryKey(afterSalesVo.getOrderId());
					//查询当前订单的所有已完结退货、退款、总额
					BigDecimal tk = afterSalesMapper.getSumTKAfterSalesBySaleorderid(afterSalesVo.getOrderId());

					//账期欠款金额
					BigDecimal lackperid = saleorderDataService.getLackAccountPeriodAmount(afterSalesVo.getOrderId());
					//支付宝提现
					BigDecimal zhifubao = capitalBillMapper.getzhifubaoAmount(afterSalesVo.getOrderId());
					// add by franlin.wu for [空指针] at 2018-12-19 begin
					BigDecimal refundAmount = null == afterSalesVo.getRefundAmount() ? BigDecimal.ZERO : afterSalesVo.getRefundAmount();
					// add by franlin.wu for [空指针] at 2018-12-19 end
					// 预退款金额=订单已付款金额+退货产品金额-订单金额，（此处订单已付款包含帐期支付）。如果预退款金额<=0时，按0处理，即不退款，偿还帐期金额=实际退款金额=0；
					BigDecimal preAnnealing = afterSalesVo.getPayAmount().add(lackperid).add(refundAmount).subtract(saleorder.getTotalAmount().subtract(tk).subtract(zhifubao));
					afterSalesVo.setPreAnnealing(preAnnealing);
					//已退金额 退余额
					if(afterSalesVo.getRefund() == 1){
						afterSalesVo.setHaveRefundAmount(afterSalesMapper.getHaveRefundAmount(afterSalesVo.getAfterSalesId(), 1));
					}else if(afterSalesVo.getRefund() == 2){
						afterSalesVo.setHaveRefundAmount(afterSalesMapper.getHaveRefundAmount(afterSalesVo.getAfterSalesId(), 2));
					}else{
						afterSalesVo.setHaveRefundAmount(new BigDecimal(0));
					}


					if(afterSalesVo.getStatus() == 1 && afterSalesVo.getRefund() == 0){
						TraderFinanceVo tf = new TraderFinanceVo();
						tf.setTraderId(afterSalesVo.getTraderId());
						tf.setTraderType(1);
						TraderFinanceVo traderFinanceVo = traderFinanceMapper.getTraderCustomerFinance(tf);
						if(traderFinanceVo != null){
							afterSalesVo.setBank(traderFinanceVo.getBank());
							afterSalesVo.setBankAccount(traderFinanceVo.getBankAccount());
							afterSalesVo.setBankCode(traderFinanceVo.getBankCode());
						}
						Trader trader = traderMapper.getTraderByTraderId(afterSalesVo.getTraderId());
						if(trader != null){
							afterSalesVo.setPayee(trader.getTraderName());
						}

					}
					break;
				}
				case AFTERSALES_TK :{
					if(afterSalesVo.getPaymentAmount().compareTo(new BigDecimal(0)) == 0){
						BigDecimal pa = saleorderDataService.getPaymentAndPeriodAmount(afterSalesVo.getOrderId());
						afterSalesVo.setPayAmount(pa);
					}else{
						afterSalesVo.setPayAmount(afterSalesVo.getPaymentAmount());
					}
					BigDecimal per = saleorderDataService.getPeriodAmount(afterSalesVo.getOrderId());
					afterSalesVo.setPayAmount(afterSalesVo.getPayAmount().add(per));

					break;
				}
				default:
			}

			if(afterSalesVo.getType() == 546){
				//订单已付款金额不含账期----2018-06-04x修改：新增已付款不含帐期字段，之前数据需查询，肯能存在不准确（算的是实际流水金额）
				if(afterSalesVo.getPaymentAmount().compareTo(new BigDecimal(0)) == 0){
					BigDecimal pa = buyorderDataService.getPaymentAndPeriodAmount(afterSalesVo.getOrderId());
					afterSalesVo.setPayAmount(pa);
				}else{
					afterSalesVo.setPayAmount(afterSalesVo.getPaymentAmount());
				}

				//已退金额
				if(afterSalesVo.getRefund() == 2){//退余额
					afterSalesVo.setHaveRefundAmount(afterSalesMapper.getHaveRefundAmount(afterSalesVo.getAfterSalesId(), 3));
				}else if(afterSalesVo.getRefund() == 1){
					afterSalesVo.setHaveRefundAmount(afterSalesMapper.getHaveRefundAmount(afterSalesVo.getAfterSalesId(), 4));
				}else{
					afterSalesVo.setHaveRefundAmount(new BigDecimal(0));
				}

			}
			if(afterSalesVo.getSubjectType() != 537){
				TraderBussinessData traderBussinessData = traderDataService.getTraderBussinessData(afterSalesVo.getTraderId(),afterSales.getTraderType());
				if(traderBussinessData != null){
					afterSalesVo.setOrderCount(traderBussinessData.getOrderTimes());
					afterSalesVo.setOrderTotalAmount(traderBussinessData.getOrderTotalAmount());
					afterSalesVo.setLastOrderTime(traderBussinessData.getLastOrderTime());

				}

			}else{
				TraderContact tc = new TraderContact();
				tc.setTraderId(afterSalesVo.getTraderId());
				tc.setTraderType(1);
				tc.setIsEnable(1);
				List<TraderContact> list = traderContactMapper.getTraderContact(tc);
				afterSalesVo.setTraderContactList(list);

			}
			if (AfterSalesProcessEnum.getInstance(afterSalesVo.getType()) != null &&
                    !AfterSalesProcessEnum.DEFAULT_CONSTANT.equals(AfterSalesProcessEnum.getInstance(afterSalesVo.getType()))){
                afterSalesVo.setAttachmentList(getAttachmentListByParam(afterSalesVo));
            }else if(afterSalesVo.getSubjectType().equals(536)){
				afterSalesVo.setAttachmentList(getAttachmentListByParam(afterSalesVo));
			}
			if(afterSalesVo.getType() == 539 || afterSalesVo.getType() == 540 || afterSalesVo.getType() == 541
					||  afterSalesVo.getType() == 4090 ||  afterSalesVo.getType() == 4091
			|| afterSalesVo.getType() == 584 || afterSalesVo.getType() == 548
					|| afterSalesVo.getType() == 546 || afterSalesVo.getType() == 547 || afterSalesVo.getType() == 544 || afterSalesVo.getType() == 545
					|| afterSalesVo.getType() == 3321 || afterSalesVo.getType() == 3322 || afterSalesVo.getType() == 3323 || afterSalesVo.getType() == 3324){
				//售后产品

				afterSalesVo.setCompanyId(afterSales.getCompanyId());
				if(afterSales.getTraderType()==1){
					afterSalesVo.setAfterSalesGoodsList(getAfterSalesGoodsVoListByParamNew(afterSalesVo));
				}else{
					afterSalesVo.setAfterSalesGoodsList(getBuyorderAfterSalesGoodsVoListByParamNew(afterSalesVo));
				}
			}

			if(afterSalesVo.getType() == 539 || afterSalesVo.getType() == 540 || afterSalesVo.getType() == 546 || afterSalesVo.getType() == 547){
				//退换货手续费
				afterSalesVo.setThhGoodsList(getTHHGoodsList(afterSalesVo));
				if(afterSalesVo.getStatus() != 2){
					//条码出库记录
					afterSalesVo.setWarehouseGoodsOperateLogList(getWarehouseOutListByParam(afterSalesVo));
				}
			}
			if(afterSalesVo.getType() == 539 || afterSalesVo.getType() == 546 ){//退货
				//退票信息
				afterSalesVo.setAfterSalesInvoiceVoList(getAfterSalesInvoiceVoListByParam(afterSalesVo));
			}
			if(afterSalesVo.getType() == 542 || afterSalesVo.getType() == 548 || afterSalesVo.getType()==1135){//退票 //丢票
				//退票信息
				afterSalesVo.setAfterSalesInvoiceVoList(getAfterSalesInvoiceVoListByParam(afterSalesVo));
			}
			if(afterSalesVo.getType() == 541 ||  afterSalesVo.getType() == 4090 ||  afterSalesVo.getType() == 4091 ||  afterSalesVo.getType() == 584 || afterSalesVo.getType() == 550 || afterSalesVo.getType() == 585){
				//工程师列表
				afterSalesVo.setAfterSalesInstallstionVoList(getAfterSalesInstallstionVo(afterSalesVo));

			}

			if (afterSalesVo.getType() == 584 || afterSalesVo.getType() == 4090 || afterSalesVo.getType() == 4091 || afterSalesVo.getType() == 541) {
				// VDERP-10877 将所有的售后商品作为安调商品
				afterSalesVo.setAtGoodsList(getAtGoodsInfo(afterSalesVo));
			}

			if(afterSalesVo.getStatus() == 2){//审核完成
				//退换货入库记录
				if(afterSalesVo.getType() == 539 || afterSalesVo.getType() == 540 || afterSalesVo.getType() == 547){
					afterSalesVo.setAfterReturnInstockList(getAfterReturnGoodsStorageList(afterSalesVo));
				}

				if(afterSalesVo.getType() == 540){
					afterSalesVo.setAfterReturnOutstockList(getAfterReturnOutStockList(afterSalesVo));
					afterSalesVo.setExpresseList(getExpressList(afterSalesVo));
				}
				if(afterSalesVo.getType() == 546 || afterSalesVo.getType() == 547){//采购退货出库记录
					afterSalesVo.setAfterReturnOutstockList(getAfterReturnOutStockList(afterSalesVo));
				}

				//开票记录
				if(afterSalesVo.getType() == 539 || afterSalesVo.getType() == 540 || afterSalesVo.getType() == 546 || afterSalesVo.getType() == 547
						|| afterSalesVo.getType() == 541 ||  afterSalesVo.getType() == 4090 ||  afterSalesVo.getType() == 4091 || afterSalesVo.getType() == 584 || afterSalesVo.getType() == 550 || afterSalesVo.getType() == 585){
					afterSalesVo.setAfterOpenInvoiceList(getAfterInvoiceList(afterSalesVo.getAfterSalesId(),SysOptionConstant.ID_504,1));
				}

				if(afterSalesVo.getType() != 542 && afterSalesVo.getType() != 544 && afterSalesVo.getType() != 545 && afterSalesVo.getType() != 548
						&& afterSalesVo.getType() != 552 && afterSalesVo.getType() != 553&&afterSalesVo.getType()!=1135){
					//交易记录
					afterSalesVo.setAfterCapitalBillList(getAfterCapitalBillList(afterSalesVo));
				}

				if(afterSalesVo.getType() != 543 && afterSalesVo.getType() != 544 && afterSalesVo.getType() != 545 && afterSalesVo.getType() != 549
						&& afterSalesVo.getType() != 552 && afterSalesVo.getType() != 553){
					//合同回传
					afterSalesVo.setAfterContractAttachmentList(getAfterAttachmentList(afterSalesVo.getAfterSalesId(),SysOptionConstant.ID_583));//583售后合同回传
				}

				if(AfterSalesProcessEnum.AFTERSALES_TH.getCode().equals(afterSalesVo.getType()) ||
						AfterSalesProcessEnum.AFTERSALES_TP.getCode().equals(afterSalesVo.getType()) ||
						AfterSalesProcessEnum.LOST_TICKET.getCode().equals(afterSalesVo.getType())){
					//退票材料
					afterSalesVo.setAfterInvoiceAttachmentList(getAfterAttachmentList(afterSalesVo.getAfterSalesId(),SysOptionConstant.ID_588));//588售后退票上传图片
				}

				if(afterSalesVo.getType() == 541 ||  afterSalesVo.getType() == 4090 ||  afterSalesVo.getType() == 4091 || afterSalesVo.getType() == 584 || afterSalesVo.getType() == 550 || afterSalesVo.getType() == 551 ||  afterSalesVo.getType() == 549 ||afterSalesVo.getType() == 543 ||
						afterSalesVo.getType() == 585 || afterSalesVo.getType() == 546 || afterSalesVo.getType() == 547 || afterSalesVo.getType() == 539){
					//申请付款列表
					afterSalesVo.setAfterPayApplyList(getAfterAtPaymentApply(afterSalesVo.getAfterSalesId()));
				}
				if(afterSalesVo.getType() == 541 ||  afterSalesVo.getType() == 4090 ||  afterSalesVo.getType() == 4091 || afterSalesVo.getType() == 584 || afterSalesVo.getType() == 550 || afterSalesVo.getType() == 585){
					//安调维修录票
					afterSalesVo.setAfterSalesInvoiceVoList(getAfterSalesAWInvoiceVoList(afterSalesVo.getAfterSalesId()));
				}
				//售后对象
				afterSalesVo.setAfterSalesTraderList(getAfterSalesTraderList(afterSalesVo.getAfterSalesId()));

			}
			//售后记录
			afterSalesVo.setAfterSalesRecordVoList(getAfterSalesRecordVoListByParam(afterSalesVo));
			//售后的关闭状态
			afterSalesVo.setCloseStatus(CollectionUtils.isEmpty(afterSalesVo.getAfterReturnInstockList()) &&
					CollectionUtils.isEmpty(afterSalesVo.getAfterReturnOutstockList()) &&
					CollectionUtils.isEmpty(afterSalesVo.getAfterOpenInvoiceList())
					&& CollectionUtils.isEmpty(afterSalesVo.getAfterCapitalBillList()) &&
					CollectionUtils.isEmpty(afterSalesVo.getAfterPayApplyList()) &&
					isReturnInvoice(afterSalesVo) ? 1 : 2);

			//第三方安调维修、销售售后退换货安调维修申请开票条件（售后服务费大于0，且已付款）
			if(((afterSalesVo.getSubjectType() == 535 && (afterSalesVo.getType() == 539 || afterSalesVo.getType() == 540 ||
					 afterSalesVo.getType() == 541 ||  afterSalesVo.getType() == 4090 ||  afterSalesVo.getType() == 4091 ||afterSalesVo.getType() == 584))
					|| ((afterSalesVo.getSubjectType() == 537 && (afterSalesVo.getType() == 550 || afterSalesVo.getType() == 585))))
					&& (afterSalesVo.getAtferSalesStatus() == 1 || afterSalesVo.getAtferSalesStatus() == 2) && afterSalesVo.getStatus() == 2 ){
				if(afterSalesVo.getServiceAmount() != null && afterSalesVo.getServiceAmount().compareTo(new BigDecimal(0)) > 0){
					CapitalBill cb = new CapitalBill();
					cb.setOrderNo(afterSalesVo.getAfterSalesNo());
					cb.setRelatedId(afterSalesVo.getAfterSalesId());
					BigDecimal bd = capitalBillMapper.getAftersaleServiceAmountBill(cb);
					//订单收款金额大于0不能修改售后服务费
					if(bd.compareTo(new BigDecimal(0)) > 0){
						afterSalesVo.setIsModifyServiceAmount(0);
					}else{
						afterSalesVo.setIsModifyServiceAmount(1);
					}
					//查询是否有已申请的开票（包含待审核和审核通过）
					Integer validStatus = afterSalesVo.getValidStatus();
					Integer atferSalesStatus = afterSalesVo.getAtferSalesStatus();
					AfterSalesDetailApiDto afterSalesDetailApiDto = afterSalesApiService.getAfterSalesDetailByAfterSalesId(afterSalesVo.getAfterSalesId());
					Integer invoiceStatus = afterSalesDetailApiDto.getInvoiceStatus();
					if (YNEnum.N.getCode().equals(validStatus) || !APPLICATE_STATUS.contains(atferSalesStatus) || SaleOrderInvoiceStatusEnum.ALL.getCode().equals(invoiceStatus)) {
						afterSalesVo.setIsCanApplyInvoice(0);
					} else {
						afterSalesVo.setIsCanApplyInvoice(1);
					}
				}else{
					afterSalesVo.setIsCanApplyInvoice(0);
					afterSalesVo.setIsModifyServiceAmount(1);
				}

			}

			//取出商品表里现在的退货金额
			if (null != afterSales.getTraderType() && afterSales.getTraderType() == 1 &&
					!CollectionUtils.isEmpty(afterSalesVo.getAfterSalesGoodsList())) {
				afterSalesVo.getAfterSalesGoodsList().forEach(afterSalesGoodsVo -> {
					afterSalesGoodsVo.setFag((afterSalesGoodsVo.getSkuRefundAmount().compareTo(afterSalesGoodsVo.getSkuOldRefundAmount()) != 0
							&& afterSalesGoodsVo.getSkuOldRefundAmount().compareTo(new BigDecimal(0)) > 0) ? 2 : 1);
				});
			}




			if(afterSalesVo != null){
				if(afterSalesVo.getSource()!=null&&afterSalesVo.getSource()==2){//来源前台
					afterSalesVo.setCreatorName(afterSalesVo.getCreateFrontEndUser()+"（前台登录客户）");
				}else{
					afterSalesVo.setCreatorName(getUserNameByUserId(afterSalesVo.getCreator()));
				}
				//查询关闭人名字
				afterSalesVo.setAfterSalesStatusUserName(getUserNameByUserId(afterSalesVo.getAfterSalesStatusUser()));
				afterSalesVo.setUserName(getUserNameByUserId(afterSalesVo.getUserId()));
				afterSalesVo.setOrgName(getOrgNameByOrgId(afterSalesVo.getOrgId()));
				if(afterSalesVo.getAfterSalesGoodsList() != null){
					for (AfterSalesGoodsVo asgv : afterSalesVo.getAfterSalesGoodsList()) {
						asgv.setUserList(rCategoryJUserMapper.getUserByCategory(asgv.getCategoryId(), afterSales.getCompanyId()));
					}
				}
				if(afterSalesVo.getAfterSalesRecordVoList() != null){
					for (AfterSalesRecordVo asrv : afterSalesVo.getAfterSalesRecordVoList()) {
						asrv.setOptName(getUserNameByUserId(asrv.getCreator()));
					}
				}
				if(afterSalesVo.getAfterSalesInvoiceVoList() != null){//退票
					for (AfterSalesInvoiceVo asi : afterSalesVo.getAfterSalesInvoiceVoList()) {
						if (null!=asi){
							asi.setCreatorName(getUserNameByUserId(asi.getCreator()));;
							asi.setValidUsername(getUserNameByUserId(asi.getValidUserId()));
						}
					}
				}
				if(afterSalesVo.getAfterContractAttachmentList() != null){
					for (Attachment aca : afterSalesVo.getAfterContractAttachmentList()) {
						aca.setUsername(getUserNameByUserId(aca.getCreator()));
					}
				}
				if(afterSalesVo.getAfterInvoiceAttachmentList() != null){
					for (Attachment aca : afterSalesVo.getAfterInvoiceAttachmentList()) {
						aca.setUsername(getUserNameByUserId(aca.getCreator()));
					}
				}
				if(afterSalesVo.getAfterReturnInstockList() != null){
					for (WarehouseGoodsOperateLog aca : afterSalesVo.getAfterReturnInstockList()) {
						aca.setOperator(getUserNameByUserId(aca.getCreator()));
					}
				}
				if(afterSalesVo.getAfterReturnOutstockList() != null){
					for (WarehouseGoodsOperateLog aca : afterSalesVo.getAfterReturnOutstockList()) {
						aca.setOperator(getUserNameByUserId(aca.getCreator()));
					}
				}
				if(afterSalesVo.getAfterPayApplyList() != null){
					for (PayApply app : afterSalesVo.getAfterPayApplyList()) {
						app.setCreatorName(getUserNameByUserId(app.getCreator()));
					}
				}
				if(afterSalesVo.getAfterOpenInvoiceList() != null){//退票
					for (AfterSalesInvoiceVo asi : afterSalesVo.getAfterOpenInvoiceList()) {
						asi.setCreatorName(getUserNameByUserId(asi.getCreator()));
						asi.setValidUsername(getUserNameByUserId(asi.getValidUserId()));
					}
				}
				if(afterSalesVo.getAfterCapitalBillList() != null){//交易信息
					for (CapitalBill asi : afterSalesVo.getAfterCapitalBillList()) {
						asi.setCreatorName(getUserNameByUserId(asi.getCreator()));
					}
				}
			}
			return afterSalesVo;
		} catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}

	private void setTraderType(AfterSalesVo afterSales) {
		try {
			if (Objects.nonNull(afterSales.getSubjectType())){
				logger.info("无需设置");
				return;
			}
			AfterSales afterSalesInfo = afterSalesMapper.selectByPrimaryKey(afterSales.getAfterSalesId());
			if (Objects.isNull(afterSalesInfo)){
				logger.info("未查到售后信息");
				return;
			}
			Integer subjectType = afterSalesInfo.getSubjectType();
			if (Objects.equals(subjectType,535)){
				afterSales.setTraderType(2);
			}else if (Objects.equals(subjectType,536)){
				afterSales.setTraderType(1);
			}
			logger.info("设置TraderType:{}",afterSales.getTraderType());
		}catch (Exception e){
			logger.error("设置售后类型异常",e);
		}
	}


	public List<AfterSalesGoodsVo> getAtGoodsInfoByAfterSalesId(Integer afterSalesId) {
		List<AfterSalesGoodsVo> atGoodsList = afterSalesGoodsMapper.getAtGoodsInfoList(afterSalesId);
		atGoodsList.forEach(item -> {
			List<Buyorder> buyorderList = saleorderMapper.getBuyOrderNoBySaleorderGoodsId(item.getOrderDetailId());
			if (buyorderList == null || buyorderList.size() == 0) {
				//通过出入库记录查询销售单商品对应的备货单单号集合
				buyorderList = saleorderMapper.getBhOrderNoBySaleorderGoodsId(item.getOrderDetailId());
			}
			item.setIsGift(saleorderGoodsMapper.getIsGiftBySaleorderGoodsId(item.getOrderDetailId()));
			item.setBuyorderNos(buyorderList);
			// 计算商品实际的发货数量
			item.setAfterSaleUpLimitNum(getAfterSaleUpLimitNum(item.getOrderDetailId()));
		});
		if (atGoodsList.size() > 0) {
			atGoodsList.forEach(x->{
				Map<String,Object> skuTipsMap = this.vGoodsService.skuTip(x.getGoodsId());
				x.setBrandName(skuTipsMap.get("BRAND_NAME").toString());
				x.setModel(skuTipsMap.get("MODEL").toString());
			});
		}
		return atGoodsList;
	}

	@Override
	public Boolean isMedicalHelpCompany(Integer afterSalesId) {
		//查询关联的工程师
		AfterSalesInstallstion afterSalesInstallstion = new AfterSalesInstallstion();
		afterSalesInstallstion.setAfterSalesId(afterSalesId);
		List<AfterSalesInstallstionVo> asivList = afterSalesInstallstionMapper.getAfterSalesInstallstionVoByParam(afterSalesInstallstion);
		return Optional.ofNullable(asivList)
				.orElse(Collections.emptyList())
				.stream()
				.anyMatch(x -> "南京医修帮医疗科技有限公司".equals(x.getName()));
	}

	/**
	 * <b>Description:</b><br> 查询售后关联的工程师
	 * @param afterSales
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年10月24日 上午11:00:43
	 */
	public List<AfterSalesInstallstionVo> getAfterSalesInstallstionVo(AfterSalesVo afterSales){
		AfterSalesInstallstion afterSalesInstallstion = new AfterSalesInstallstion();
		afterSalesInstallstion.setAfterSalesId(afterSales.getAfterSalesId());
		List<AfterSalesInstallstionVo> list = afterSalesInstallstionMapper.getAfterSalesInstallstionVoByParam(afterSalesInstallstion);

		if (CollectionUtils.isEmpty(list)){
			return list;
		}

		list.forEach(item -> {
			List<AfterSalesInstallstionVo> afterSalesInstallstionVo = afterSalesInstallstionMapper.getAfterSalesPayStatus(afterSales.getAfterSalesId(), item.getEngineerId());

			if(!CollectionUtils.isEmpty(afterSalesInstallstionVo)){
				afterSalesInstallstionVo.stream().filter(payApply -> payApply.getEngineerId().equals(item.getEngineerId()) &&
						payApply.getValidStatus().equals(1) || payApply.getValidStatus().equals(0))
						.forEach(payApply -> item.setValidStatus(payApply.getValidStatus()));
				}

			//服务次数
			item.setServiceTimes(afterSalesInstallstionMapper.getEngineerInstallstionCount(item.getEngineerId()));
			item.setAfterSalesGoodsVoList(afterSalesGoodsMapper.getAfterSalesGoodsVosListByAfterSalesInstallstionId(item.getAfterSalesInstallstionId()));
			for (AfterSalesGoodsVo afterSalesGoodsVo : item.getAfterSalesGoodsVoList()) {
				List<Buyorder> list1 = saleorderMapper.getBuyOrderNoBySaleorderGoodsId(afterSalesGoodsVo.getOrderDetailId());
				if(list1 == null || list1.size() == 0){
					//通过出入库记录查询销售单商品对应的备货单单号集合
					list1 = saleorderMapper.getBhOrderNoBySaleorderGoodsId(afterSalesGoodsVo.getOrderDetailId());
				}
				afterSalesGoodsVo.setBuyorderNos(list1);
			}
		});
		return list;
	}


	/**
	 * 查询安调商品信息
	 */
	private List<AfterSalesGoodsVo> getAtGoodsInfo(AfterSalesVo afterSales) {
		List<AfterSalesGoodsVo> atGoodsList = afterSalesGoodsMapper.getAtGoodsInfoList(afterSales.getAfterSalesId());
		atGoodsList.forEach(item -> {
			List<Buyorder> buyorderList = saleorderMapper.getBuyOrderNoBySaleorderGoodsId(item.getOrderDetailId());
			if (buyorderList == null || buyorderList.size() == 0) {
				//通过出入库记录查询销售单商品对应的备货单单号集合
				buyorderList = saleorderMapper.getBhOrderNoBySaleorderGoodsId(item.getOrderDetailId());
			}
			item.setIsGift(saleorderGoodsMapper.getIsGiftBySaleorderGoodsId(item.getOrderDetailId()));
			item.setIsDirectPurchase(saleorderGoodsMapper.getIsDirectPurchaseBySaleorderGoodsId(item.getOrderDetailId()));
			item.setBuyorderNos(buyorderList);
			// 计算商品实际的发货数量
			item.setAfterSaleUpLimitNum(getAfterSaleUpLimitNum(item.getOrderDetailId()));
		});
		return atGoodsList;
	}

	private int getAfterSaleUpLimitNum(Integer orderDetailId) {
		int afterSaleUpLimitNum = 0;
		SaleorderGoods saleorderGoods = saleorderGoodsMapper.selectByPrimaryKey(orderDetailId);
		if (Objects.nonNull(saleorderGoods)) {
			if (saleorderGoods.getNum().equals(saleorderGoods.getDeliveryNum())) {// 全部已发货
				afterSaleUpLimitNum = saleorderGoods.getNum() - (saleorderGoods.getAfterReturnNum() == null ? 0 : saleorderGoods.getAfterReturnNum());
			}
			if (saleorderGoods.getNum() > saleorderGoods.getDeliveryNum() && saleorderGoods.getDeliveryNum() > 0) {// 部分发货
				int sale = saleorderGoods.getNum() - (saleorderGoods.getAfterReturnNum() == null ? 0 : saleorderGoods.getAfterReturnNum());
				if (sale >= saleorderGoods.getDeliveryNum()) {// 减去退货后数量大于已发货
					afterSaleUpLimitNum = saleorderGoods.getDeliveryNum();
				} else {
					afterSaleUpLimitNum = sale;
				}
			}
		}
		return afterSaleUpLimitNum;
	}

	/**
	 * 查询售后的安调维修的录票
	 *
	 * @param afterSalesId
	 * @return
	 */
	public List<AfterSalesInvoiceVo> getAfterSalesAWInvoiceVoList(Integer afterSalesId){
		List<AfterSalesInvoiceVo> afterSalesInvoiceVoList = afterSalesInvoiceMapper.getAfterSalesAWInvoiceVoList(afterSalesId);
		return afterSalesInvoiceVoList;
	}

	/**
	 * <b>Description:</b><br> 根据售后订单获取售后附件信息列表
	 * @param afterSalesId
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年10月23日 下午5:16:04
	 */
	public List<Attachment> getAfterAttachmentList(Integer afterSalesId,Integer attachmentFunction){
		Attachment att = new Attachment();
		//588售后退票材料上传,583售后合同回传
		att.setAttachmentFunction(attachmentFunction);
		att.setRelatedId(afterSalesId);//售后表主键
		return attachmentMapper.getAttachmentList(att);
	}

	/**
	 * <b>Description:</b><br> 资金流水-交易记录
	 * @param afterSalesVo
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年10月23日 下午4:36:58
	 */
	public List<CapitalBill> getAfterCapitalBillList(AfterSalesVo afterSalesVo){
		//交易记录
		CapitalBill capitalBill = new CapitalBill();
		CapitalBillDetail capitalBillDetail = new CapitalBillDetail();
		capitalBillDetail.setOrderType(Constants.THREE);//售后订单类型
		capitalBillDetail.setOrderNo(afterSalesVo.getOrderNo());
		capitalBillDetail.setRelatedId(afterSalesVo.getAfterSalesId());
		capitalBill.setCapitalBillDetail(capitalBillDetail);
		return capitalBillService.getCapitalBillList(capitalBill);
	}

	/**
	 * <b>Description:</b><br> 获取换货的物流信息
	 * @param afterSalesVo
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年11月16日 下午2:14:05
	 */
	public List<Express> getExpressList(AfterSalesVo afterSalesVo){
		//快递单列表
		Express ex = new Express();
		ex.setBusinessType(582);//售后
		ex.setRelatedId(afterSalesVo.getAfterSalesId());
		List<Express> expressList = expressMapper.getExpressInfo(ex);
		return expressList;
	}

	/**
	 * <b>Description:</b><br> 查询售后的退票信息
	 * @param afterSales
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年10月20日 下午5:29:12
	 */
	public List<AfterSalesInvoiceVo> getAfterSalesInvoiceVoListByParam(AfterSalesVo afterSales){
		AfterSalesInvoice asi = new AfterSalesInvoice();
		asi.setAfterSalesId(afterSales.getAfterSalesId());
		asi.setType(afterSales.getType());
		return afterSalesInvoiceMapper.getAfterSalesInvoiceVoList(asi);
	}

	/**
	 * <b>Description:</b><br> 查询销售订单的售后产品信息
	 * @param afterSales
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年10月20日 下午5:04:27
	 */
	@Override
	public List<AfterSalesGoodsVo> getAfterSalesGoodsVoListByParamNew(AfterSalesVo afterSales){
		//退货，换货，安调,维修
		AfterSalesGoods afterSalesGoods = new AfterSalesGoods();
		afterSalesGoods.setAfterSalesId(afterSales.getAfterSalesId());
		//退货信息
		List<AfterSalesGoodsVo> list = afterSalesGoodsMapper.getAfterSalesGoodsVosList(afterSalesGoods);
		//往期退货数量
		List<AfterSalesGoodsVo> excludeCurrentGoodNumlist=afterSalesGoodsMapper.getExcludeCurrentGoodNum(afterSales);


		if(list != null && afterSales.getType() != null && (afterSales.getType() == 539
				|| afterSales.getType() == 584 || afterSales.getType() == 541 || afterSales.getType() == 540 || afterSales.getType() == 4090 || afterSales.getType() == 4091)){
			//------------------------------------查询销售单对应的采购单：参照数据导出方法-------------------
			List<Integer> saleOrderGoodsIdList = new ArrayList<>();
			List<BuyorderGoodsVo> buyOrderGoodsList = new ArrayList<>();
			if(afterSales.getType() == 539 || afterSales.getType() == 584 || afterSales.getType() == 541 || afterSales.getType() == 540 || afterSales.getType() == 4090 || afterSales.getType() == 4091){
				if(list != null && list.size() > 0){
					for(int i=0;i<list.size();i++){
						saleOrderGoodsIdList.add(list.get(i).getOrderDetailId());
					}
					//财务模块对应采购单
					buyOrderGoodsList = saleorderDataService.getBuyOrderInfoBySaleGoodsIdForAfterSale(saleOrderGoodsIdList);
				}
			}
			//---------------------------------------------------------------------------------------------
			List<Integer> afterGoodsIdList = new ArrayList<>();
			for (AfterSalesGoodsVo asgv : list) {
				if(excludeCurrentGoodNumlist!=null&&excludeCurrentGoodNumlist.size()>0){
					for(AfterSalesGoodsVo gn:excludeCurrentGoodNumlist){//遍历每个商品往期的退货数量
						if(asgv.getGoodsId().equals(gn.getGoodsId())){
							asgv.setExcludeGoodNum(gn.getNum());
						}
					}
				}
				if(asgv.getOrderType().equals(Constants.ONE)
						|| asgv.getOrderType().equals(Constants.FIVE)){
					asgv.setTotalAmount(asgv.getMaxSkuRefundAmount());
				}else{
					asgv.setTotalAmount(asgv.getSaleorderPrice().multiply(new BigDecimal(asgv.getSaleorderNum())));
				}

				SaleorderGoods saleOrderGoods = saleorderGoodsMapper.getSaleorderGoodsInfoById(asgv.getOrderDetailId());

				if((afterSales.getType() == 539|| afterSales.getType() == 540 || afterSales.getType() == 584 || afterSales.getType() == 541 || afterSales.getType() == 4090 || afterSales.getType() == 4091) && asgv.getGoodsType() == 0){
					/*List<Buyorder> list1 = saleorderMapper.getBuyOrderNoBySaleorderGoodsId(asgv.getOrderDetailId());
					if(list1 == null || list1.size() == 0){
						//通过出入库记录查询销售单商品对应的备货单单号集合
						list1 = saleorderMapper.getBhOrderNoBySaleorderGoodsId(asgv.getOrderDetailId());
					}
					asgv.setBuyorderNos(list1);*/

					for(BuyorderGoodsVo buyGoods:buyOrderGoodsList){
						if(buyGoods.getSaleorderGoodsId().equals(asgv.getOrderDetailId())){
//							sg.setCostPrice(buyGoods.getPrice());
							if(buyGoods.getBuyOrderNoStr() != null && buyGoods.getBuyOrderIdStr() != null){
								List<Buyorder> buyorderList = new ArrayList<>();
								String[] split = buyGoods.getBuyOrderNoStr().split(",");
								if(split != null && split.length > 0){
									for(int m=0;m<split.length;m++){
										Buyorder buyOrder = new Buyorder();
										buyOrder.setBuyorderNo(split[m]);
										String s = buyGoods.getBuyOrderIdStr().split(",")[m];
										buyOrder.setBuyorderId(Integer.valueOf("".equals(s)?"0":s));
//										buyOrder.setBuyorderId(Integer.valueOf(buyGoods.getBuyOrderIdStr().split(",")[m]));
										buyorderList.add(buyOrder);
									}
								}
								asgv.setBuyorderNos(buyorderList);
							}
						}
					}
				}

				Integer buynum = buyorderGoodsMapper.getBuyorderGoodsSum(asgv.getOrderDetailId());
				asgv.setBuyNum(buynum);
				Integer arrivalNum = saleorderDataService.getArrivalNum(asgv.getOrderDetailId());
				asgv.setReceiveNum(arrivalNum);
				afterGoodsIdList.add(asgv.getAfterSalesGoodsId());
				if(afterSales.getStatus() == 2){
					if(afterSales.getType() == 539){
						//计算商品的售后数量上限
						AfterSales as = new AfterSales();
						as.setOrderId(afterSales.getOrderId());
						as.setType(539);
						List<Integer> idlist= afterSalesMapper.getAfterSalesIdListByOrderId(afterSales);
						AfterSalesGoodsVo afterSalesGoodsVo = new AfterSalesGoodsVo();
						if(idlist != null && idlist.size() > 0){
							afterSalesGoodsVo.setAfterSalesIdList(idlist);
						}else{
							idlist = new ArrayList<>();
							idlist.add(-1);
							afterSalesGoodsVo.setAfterSalesIdList(idlist);
						}
						afterSalesGoodsVo.setOrderDetailId(asgv.getOrderDetailId());
						afterSalesGoodsVo.setType(539);//退货
						Integer num = afterSalesGoodsMapper.getAfterSalesNumByParam(afterSalesGoodsVo);//退货的数量
						asgv.setNowSalesNum(asgv.getSaleorderNum() - num + asgv.getNum());
						//asgv.setNowSalesNum(asgv.getSaleorderNum() - num );
					}else if(afterSales.getType() == 540){//换货
						WarehouseGoodsOperateLog wl = new WarehouseGoodsOperateLog();
						wl.setRelatedId(asgv.getAfterSalesGoodsId());
						wl.setOperateType(3);//销售换货入库
						asgv.setExchangeReturnNum(warehouseGoodsOperateLogMapper.getAftersalesGoodsSum(wl));
						wl.setOperateType(4);//销售换货出库
						asgv.setExchangeDeliverNum(warehouseGoodsOperateLogMapper.getAftersalesGoodsSum(wl));
					}
				}
//				//如果销售订单产品是直发的，切全部收货全部发货，则发货数量和收货数量为num
//				if(saleOrderGoods.getDeliveryDirect() == 1 && saleOrderGoods.getArrivalStatus() == 2 && saleOrderGoods.getDeliveryStatus() == 2){
//					asgv.setReceiveNum(saleOrderGoods.getNum());
//					asgv.setDeliveryNum(saleOrderGoods.getNum());
//				}
			}
			if(afterSales.getStatus() == 2){
				//获取售后产品入库记录
				Integer operateType = 0;//操作类型1入库 2出库3销售换货入库4销售换货出库5销售退货入库6采购退货出库7采购换货出库8采购换货入库
				switch (afterSales.getType().intValue()) {
					case 539://销售订单退货
						operateType = 5;
						break;
					case 540://销售订单换货
						operateType = 3;
						break;
					default:
						break;
				}
				List<AfterSalesGoods> salesGoodsList = afterSalesGoodsMapper.getAfterGoodsList(afterGoodsIdList,afterSales.getCompanyId(),operateType);
				for(int j=0;j<list.size();j++){
					Integer incnt = 0;
					for(int i=0;i<salesGoodsList.size();i++){
						if(salesGoodsList.get(i).getAfterSalesGoodsId().intValue() == list.get(j).getAfterSalesGoodsId().intValue()){
							incnt = incnt + salesGoodsList.get(i).getNum();
						}
					}
					list.get(j).setIncnt(incnt);
				}
			}
		}
		return list;
	}

	@Override
	public Map<String, Object> getStorageAftersalesPage(AfterSalesVo afterSalesVo, Page page) {
		String url = httpUrl + "aftersales/order/getstorageaftersalespage.htm";
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<List<AfterSalesVo>>> TypeRef2 = new TypeReference<ResultInfo<List<AfterSalesVo>>>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, afterSalesVo, clientId, clientKey, TypeRef2,
					page);
			if(result == null || result.getCode() ==-1){
				return null;
			}
			Map<String, Object> map = new HashMap<>();
			List<AfterSalesVo> asvList = (List<AfterSalesVo>) result.getData();
			page = result.getPage();
			map.put("list", asvList);
			map.put("page", page);
			return map;
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}

	@Override
	public List<AfterSalesGoodsVo> getAfterSalesGoodsVoList(AfterSalesVo asv,HttpSession hs) {
		String url = httpUrl + "aftersales/order/getaftersalesgoodsvolist.htm";
		User user = (User) hs.getAttribute(ErpConst.CURR_USER);
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<List<AfterSalesGoodsVo>>> TypeRef2 = new TypeReference<ResultInfo<List<AfterSalesGoodsVo>>>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, asv, clientId, clientKey, TypeRef2);
			if(result == null || result.getCode() ==-1){
				return null;
			}
			@SuppressWarnings("unchecked")
			List<AfterSalesGoodsVo> asvList = (List<AfterSalesGoodsVo>) result.getData();
			for (AfterSalesGoodsVo aVo : asvList) {
				aVo.setGoodsHeader(rCategoryJUserMapper.getUserByCategoryNm(aVo.getCategoryId(), user.getCompanyId()));
				/**
				 * 调用接口获取库存信息
				 */
				List<String> skus = Arrays.asList(aVo.getSku());
				Map<String, WarehouseStock> stockInfo = warehouseStockService.getStockInfo(skus);
				if(!CollectionUtils.isEmpty(stockInfo)) {
				    if(stockInfo.get(aVo.getSku()) == null){
                        aVo.setTotalNum(0);
                        aVo.setActionLockCount(0);
                    }else {
                        aVo.setTotalNum(stockInfo.get(aVo.getSku()).getStockNum());
                        aVo.setActionLockCount(stockInfo.get(aVo.getSku()).getActionLockNum());
                    }
				}else{
					aVo.setTotalNum(0);
					aVo.setActionLockCount(0);
				}
			}
			return asvList;
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}

	@Override
	public AfterSalesVo getAfterSalesVoListById(AfterSales afterSales) {
		String url = httpUrl + "aftersales/order/getaftersalesvolistbyid.htm";
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<AfterSalesVo>> TypeRef2 = new TypeReference<ResultInfo<AfterSalesVo>>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, afterSales, clientId, clientKey, TypeRef2);
			AfterSalesVo avs = (AfterSalesVo) result.getData();
			return avs;
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}

	@Override
	public AfterSalesGoodsVo getAfterSalesGoodsInfo(AfterSalesGoods afterSalesGoods) {
		String url = httpUrl + "aftersales/order/getaftersalesgoodsinfo.htm";
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<AfterSalesGoodsVo>> TypeRef2 = new TypeReference<ResultInfo<AfterSalesGoodsVo>>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, afterSalesGoods, clientId, clientKey, TypeRef2);
			AfterSalesGoodsVo avs = (AfterSalesGoodsVo) result.getData();
			return avs;
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}
	/**
	 * <b>Description:</b><br> 申请审核
	 * @param afterSales
	 * @param
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年10月19日 上午11:02:23
	 */
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public ResultInfo<?> editApplyAudit(AfterSalesVo afterSales) {
		long start=System.currentTimeMillis();
		String url = httpUrl + ErpConst.APPLY_AFTERSALES_AUDIT;

		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<AfterSalesDetailVo>> TypeRef2 = new TypeReference<ResultInfo<AfterSalesDetailVo>>() {};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, afterSales, clientId, clientKey, TypeRef2);
			if(result != null){
				if(result.getCode() == 0 && result.getData() != null){
					//审核通过--进行中
					if(afterSales.getStatus() == 2 && afterSales.getAtferSalesStatus() == 1){//审核通过，退余额，退账期
						//销售--退货及退款
						if(afterSales.getSubjectType() != null && afterSales.getSubjectType()==535 && (afterSales.getType() == 539 || afterSales.getType() == 543)){
							//发送消息--售后销售订单退货-退款审核通过（钱退到账户余额）
							AfterSalesDetailVo new_afterSales = (AfterSalesDetailVo)result.getData();
							if(new_afterSales != null && new_afterSales.getTraderId() != null){
								//根据客户Id查询客户负责人
								List<Integer> userIdList = userMapper.getUserIdListByTraderId(new_afterSales.getTraderId(),ErpConst.ONE);
								Map<String,String> map = new HashMap<>();
								map.put("afterorderNo", new_afterSales.getAfterSalesNo());
								MessageUtil.sendMessage(40, userIdList, map,
										"./order/saleorder/viewAfterSalesDetail.do?afterSalesId="+new_afterSales.getAfterSalesId());//
							}
						}
					}
				}
			}
			return result;
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}finally {
			long end=System.currentTimeMillis();
			if((end-start)>10000){
				logger.error("事务过长");
			}
		}
	}

	//从db迁移的代码
	private void editApplyAuditFromDb(AfterSalesVo afterSales) {
		try {
			editApplyAuditNew(afterSales);
			if(afterSales.getAtferSalesStatus() != null && afterSales.getAtferSalesStatus() == 2){
				if(afterSales.getType()==539){//销售退货刷销售单的票货款
					updateSaleorderAllStatus(afterSales.getAfterSalesId(), afterSales.getOrderId());
					saleorderDataService.getIsEnd(afterSales.getOrderId());
				}else if(afterSales.getType()==546){//采购退货刷采购单的票货款
					updateBuyorderAllStatus(afterSales.getAfterSalesId(), afterSales.getOrderId());
				}
			}
		} catch (Exception e) {
			logger.error("editApplyAudit error:{}",e);
		}
	}

	private void editApplyAuditNew(AfterSalesVo afterSales) {
		int res  = afterSalesMapper.updateByPrimaryKeySelective(afterSales);
		if(afterSales.getAtferSalesStatus() != null && afterSales.getAtferSalesStatus() == 2){//确认售后完成
			if(afterSales.getSubjectType() != null && afterSales.getSubjectType()==535 && (afterSales.getType()==540 || afterSales.getType()==543)){
				//销售
				Saleorder saleorder = new Saleorder();
				saleorder.setSaleorderId(afterSales.getOrderId());
				saleorder.setServiceStatus(2);//售后关闭
				if(afterSales.getSubjectType()==535 && afterSales.getType()==543){
					//查询当前销售订单下面是否还有退换货的售后单
					List<SaleorderGoodsVo> sgvList = saleorderGoodsMapper.getSaleorderGoodsVoListBySaleorderId(saleorder);
					if(null != sgvList && sgvList.size() > 0){
						for (int i = 0; i < sgvList.size(); i++) {
							if(sgvList.get(i).getLockedStatus() == 0){
								sgvList.remove(i);
								i--;
							}
						}
					}
					AfterSales afterSales1 = new AfterSales();
					afterSales1.setOrderId(afterSales.getOrderId());
					List<AfterSalesVo> afterSalesVoList = afterSalesMapper.getReturnBackMoneyByOrderId(afterSales1);
					//如果没有，则解锁销售订单
					if((null == sgvList || sgvList.size() == 0) && (afterSalesVoList.size() == 0 || afterSalesVoList == null)){
						saleorder.setLockedStatus(0);//0未锁定，1已锁定
					}
				}
				res = saleorderMapper.updateByPrimaryKeySelective(saleorder);
				if(saleorder.getLockedStatus() != null && saleorder.getLockedStatus()==0){
					updateUnlockSaleOrderWarning(saleorder.getSaleorderId());
				}

			}else if(afterSales.getSubjectType() != null && afterSales.getSubjectType()==536&& afterSales.getType()==547){
				int count=0;
				AfterSalesVo afterSalesVo=new AfterSalesVo();
				afterSalesVo.setOrderId(afterSales.getOrderId());
				List<AfterSalesVo> byOrderLists=getAfterSalesVoListByOrderId(afterSalesVo);
				if(byOrderLists!=null&&byOrderLists.size()>0){
					for(AfterSalesVo afterSalesVo1:byOrderLists){
						if(afterSalesVo1.getAtferSalesStatus().equals(0)|| afterSalesVo1.getAtferSalesStatus().equals(1)){
							count++;
						}
					}
				}
				if(count==0){
					//采购
					Buyorder buyorder = new Buyorder();
					buyorder.setBuyorderId(afterSales.getOrderId());
					buyorder.setServiceStatus(2);
					buyorder.setLockedStatus(0);
					res = buyorderMapper.updateByPrimaryKeySelective(buyorder);
				}
			}
		}
	}

	/**
	 * .
	 * @jira: .
	 * @notes: 解锁时,更新销售单预警状态
	 * @version: 1.0.
	 * @date: 2021/12/28 11:04.
	 * @author: Randy.Xu.
	 * @param saleorderId
	 * @return: void.
	 * @throws:  .
	 */
	private void updateUnlockSaleOrderWarning(Integer saleorderId) {
		try{
			long nowTime = DateUtil.gainNowDate();
			List<SaleorderGoods> saleorderGoodsList = saleorderMapper.getSaleorderGoodsBySaleOrderId(saleorderId);
			if(org.apache.commons.collections.CollectionUtils.isNotEmpty(saleorderGoodsList)){
				for (SaleorderGoods saleorderGoods : saleorderGoodsList) {
					// 时效监控开始时间不为空，才去更新，不然不需要重置
					if (saleorderGoods.getAgingTime() != null && saleorderGoods.getAgingTime() > 0) {
						SaleorderGoodsVo saleorderGoodsVo = new SaleorderGoodsVo();
						saleorderGoodsVo.setSaleorderGoodsId(saleorderGoods.getSaleorderGoodsId());
						saleorderGoodsVo.setWarnLevel(null);
						saleorderGoodsVo.setAging(0);
						saleorderGoodsVo.setAgingTime(nowTime);
						saleorderGoodsMapper.updateWarnStatusSelective(saleorderGoodsVo);
					}
				}
			}
		}catch (Exception e){
			logger.error("解锁时,更新销售单预警状态失败，销售单id:{},错误：{}",saleorderId,e);
		}
	}

	/**
	 * <b>Description:</b><br> 关闭售后订单
	 * @param afterSales
	 * @param user
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年10月23日 上午10:16:45
	 */
	@Override
	public ResultInfo<?> saveCloseAfterSales(AfterSalesVo afterSales, User user) {
		//订单关闭
		afterSales.setAtferSalesStatus(3);
		afterSales.setCompanyId(user.getCompanyId());
		afterSales.setModTime(DateUtil.sysTimeMillis());
		afterSales.setUpdater(user.getUserId());
		//设置关闭人Id
		afterSales.setAfterSalesStatusUser(user.getUserId());

		afterSalesMapper.updateByPrimaryKeySelective(afterSales);
		//更新售后updatatime
		orderCommonService.updateAfterOrderDataUpdateTime(afterSales.getAfterSalesId(),null,OrderDataUpdateConstant.AFTER_ORDER_CLOSE);

		//VDERP-6922 【库存管理】订单占用释放逻辑补充
		//关闭生效换货单 归还库存占用
		logicalAfterorderChooseService.closeExchangeAfterOrder(afterSales);
		if(afterSales.getSubjectType() != null && afterSales.getSubjectType()==536){
			//采购
			Buyorder buyorder = new Buyorder();
			buyorder.setBuyorderId(afterSales.getOrderId());
			buyorder.setServiceStatus(3);
			buyorder.setLockedStatus(0);
			buyorderMapper.updateByPrimaryKeySelective(buyorder);
		}
		try {
			if (Objects.nonNull(afterSales.getSubjectType()) && (afterSales.getSubjectType() == 535 || afterSales.getSubjectType() == 537)) {
				AfterSales after = afterSalesMapper.selectByPrimaryKey(afterSales.getAfterSalesId());
				AfterSalesDetailVo detail = afterSalesDetailMapper.getAfterSalesDetailByAfterSalesId(afterSales.getAfterSalesId());
				// 开始添加埋点信息
				Map<String, Object> trackParams = new HashMap<>();
				// 客户id
				trackParams.put("traderId", detail.getTraderId());
				// 创建人信息
				trackParams.put("track_user", user);
				// 订单号
				trackParams.put("orderNo", after.getOrderNo());
				// 售后类型
				SysOptionDefinition sysOptionDefinition = sysOptionDefinitionMapper.selectByPrimaryKey(detail.getAfterType());
				trackParams.put("afterSaleTypeName", Objects.nonNull(sysOptionDefinition) ? sysOptionDefinition.getTitle() : "");
				TrackStrategy trackStrategy = trackStrategyFactory.getStrategyByType(EventTrackingEnum.AFTER_SALE_ORDER_CLOSE_BACK);
				TrackParamsData trackParamsData = new TrackParamsData();
				trackParamsData.setTrackParams(trackParams);
				trackParamsData.setTrackTime(new Date());
				trackParamsData.setTrackResult(ResultInfo.success());
				trackParamsData.setEventTrackingEnum(EventTrackingEnum.AFTER_SALE_ORDER_CLOSE_BACK);
				trackStrategy.track(trackParamsData);
			}
		} catch (Exception e) {
			logger.error("埋点：{}，失败，不影响正常业务",EventTrackingEnum.AFTER_SALE_ORDER_CLOSE_BACK.getArchivedName(),e);
		}
		return ResultInfo.success();
		/**售后单关闭更新销售单状态，变为调用销售接口，不在请求db修改
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, afterSales, clientId, clientKey, TypeRef2);
			if(result != null && result.getCode().equals(0)){
				//更新售后updatatime
				orderCommonService.updateAfterOrderDataUpdateTime(afterSales.getAfterSalesId(),null,OrderDataUpdateConstant.AFTER_ORDER_CLOSE);

				//VDERP-6922 【库存管理】订单占用释放逻辑补充
				//关闭生效换货单 归还库存占用
				logicalAfterorderChooseService.closeExchangeAfterOrder(afterSales);

			}
			return result;
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}**/
	}
	/**
	 * <b>Description:</b><br> 保存售后的退换货手续费
	 * @param afterSales
	 * @param user
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年10月23日 上午11:38:35
	 */
	@Override
	public ResultInfo<?> saveEditRefundFee(AfterSalesVo afterSales, User user) {
		String url = httpUrl + ErpConst.SAVE_AFTERSALES_REFUNDFEE;
		afterSales.setModTime(DateUtil.sysTimeMillis());
		afterSales.setUpdater(user.getUserId());

		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, afterSales, clientId, clientKey, TypeRef2);
			return result;
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}
	/**
	 * <b>Description:</b><br> 保存编辑的退票信息
	 * @param
	 * @param
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年10月23日 上午11:38:35
	 */
	@Override
	public ResultInfo<?> saveEditRefundTicket(AfterSalesInvoice afterSalesInvoice) {
		String url = httpUrl + ErpConst.SAVE_AFTERSALES_REFUNDTICKET;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, afterSalesInvoice, clientId, clientKey, TypeRef2);
			return result;
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}

	/**
	 * <b>Description:</b><br> 保存新增或更新的售后过程
	 * @param
	 * @param user
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年10月23日 下午5:35:59
	 */
	@Override
	public ResultInfo<?> saveAfterSalesRecord(AfterSalesRecord afterSalesRecord, User user) {
		String url = httpUrl + ErpConst.SAVE_AFTERSALES_RECORD;
		if(ObjectUtils.isEmpty(afterSalesRecord.getAfterSalesRecordId())){
			afterSalesRecord.setAddTime(DateUtil.sysTimeMillis());
			afterSalesRecord.setCreator(user.getUserId());
		}
		afterSalesRecord.setModTime(DateUtil.sysTimeMillis());
		afterSalesRecord.setUpdater(user.getUserId());
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Integer>> TypeRef2 = new TypeReference<ResultInfo<Integer>>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, afterSalesRecord, clientId, clientKey, TypeRef2);
			return result;
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}
	/**
	 * <b>Description:</b><br> 查询新增产品与工程师页面所需数据
	 * @param
	 * @param
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年10月24日 下午3:23:15
	 */
	@Override
	public List<AfterSalesGoodsVo> getAfterSalesInstallstionVoByParam(AfterSalesVo afterSales) {
		String url = httpUrl + ErpConst.GET_ENIGNEER_INSTALLSTION;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, afterSales, clientId, clientKey, TypeRef2);
			if(result == null || result.getCode() ==-1){
				return null;
			}
			JSONArray json = JSONArray.fromObject(result.getData());
			@SuppressWarnings("unchecked")
			List<AfterSalesGoodsVo> list = (List<AfterSalesGoodsVo>) JSONArray.toCollection(json, AfterSalesGoodsVo.class);
			return list;
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}
	/**
	 * <b>Description:</b><br> 保存编辑的安调信息
	 * @param
	 * @param
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年10月23日 下午5:35:59
	 */
	@Override
	public ResultInfo<?> saveEditInstallstion(AfterSalesDetail afterSalesDetail) {
		String url = httpUrl + ErpConst.SAVE_AFTERSALES_INSTALLSTION;

		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, afterSalesDetail, clientId, clientKey, TypeRef2);
			return result;
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}
	/**
	 * <b>Description:</b><br> 查询工程师分页信息
	 * @param
	 * @param page
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年10月24日 下午3:23:15
	 */
	@Override
	public Map<String, Object> getEngineerPage(AfterSalesVo afterSales, Page page) {
		String url = httpUrl + ErpConst.GET_ENIGNEER_PAGE;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, afterSales, clientId, clientKey, TypeRef2,
					page);
			if(result == null || result.getCode() ==-1){
				return null;
			}
			JSONArray json = JSONArray.fromObject(result.getData());
			@SuppressWarnings("unchecked")
			List<EngineerVo> list = (List<EngineerVo>) JSONArray.toCollection(json, EngineerVo.class);
			if(list.size() > 0){
				for (EngineerVo ev : list) {
					ev.setAreaStr(getAddressByAreaId(ev.getAreaId()));
				}
			}
			page = result.getPage();
			Map<String, Object> map = new HashMap<>();
			map.put("list", list);
			map.put("page", page);
			return map;
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}
	/**
	 * <b>Description:</b><br> 保存新增或编辑的工程师与售后产品的关系
	 * @param afterSalesInstallstionVo
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年10月27日 下午5:57:28
	 */
	@Override
	public ResultInfo<?> saveAfterSalesEngineer(AfterSalesInstallstionVo afterSalesInstallstionVo,User user) {
		if(ObjectUtils.isEmpty(afterSalesInstallstionVo.getAfterSalesInstallstionId())){
			afterSalesInstallstionVo.setAddTime(DateUtil.sysTimeMillis());
			afterSalesInstallstionVo.setCreator(user.getUserId());
		}
		afterSalesInstallstionVo.setModTime(DateUtil.sysTimeMillis());
		afterSalesInstallstionVo.setUpdater(user.getUserId());
		String url = httpUrl + ErpConst.SAVE_AFTERSALES_ENGINEER;

		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, afterSalesInstallstionVo, clientId, clientKey, TypeRef2);
			return result;
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}
	/**
	 * <b>Description:</b><br> 获取编辑工程师与售后产品的信息
	 * @param afterSalesInstallstionVo
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年10月31日 下午1:01:03
	 */
	@Override
	public AfterSalesInstallstionVo getAfterSalesInstallstionVo(AfterSalesInstallstionVo afterSalesInstallstionVo) {
		String url = httpUrl + ErpConst.EDIT_ENIGNEER_INSTALLSTION;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, afterSalesInstallstionVo, clientId, clientKey, TypeRef2);
			if(result == null || result.getCode() ==-1){
				return null;
			}
			JSONObject json = JSONObject.fromObject(result.getData());
			//AfterSalesInstallstionVo asiv =  (AfterSalesInstallstionVo) JSONObject.toBean(json, AfterSalesInstallstionVo.class);
			AfterSalesInstallstionVo asiv = JsonUtils.readValue(json.toString(), AfterSalesInstallstionVo.class);
			if(asiv != null && asiv.getRiInstallstionJGoodList() != null){
				for (RInstallstionJGoods ri : asiv.getRiInstallstionJGoodList()) {

				}
			}

			return asiv;
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}
	/**
	 * <b>Description:</b><br> 查询客户的联系人和财务信息
	 * @param traderVo
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年11月3日 下午1:08:22
	 */
	@Override
	public Map<String, Object> getCustomerContactAndFinace(TraderVo traderVo) {
		String url = httpUrl + ErpConst.GET_CUSTOMER_CONTACTANDFINCAE;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, traderVo, clientId, clientKey, TypeRef2);
			if(result == null || result.getCode() ==-1){
				return null;
			}
			return (Map<String, Object>) result.getData();
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}
	/**
	 * <b>Description:</b><br> 获取售后安调维修申请付款页面的信息--工程师和安调维修费用
	 * @param afterSalesVo
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年11月7日 下午1:57:19
	 */
	@Override
	public AfterSalesVo getAfterSalesApplyPay(AfterSalesVo afterSalesVo) {
		String url = httpUrl + ErpConst.GET_AFTERSALES_APPLYPAY;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, afterSalesVo, clientId, clientKey, TypeRef2);
			if(result == null || result.getCode() ==-1){
				return null;
			}
			JSONObject json = JSONObject.fromObject(result.getData());
			AfterSalesVo as = JsonUtils.readValue(json.toString(), AfterSalesVo.class);
			return as;
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}
	/**
	 * <b>Description:</b><br> 保存申请付款
	 * @param
	 * @param user
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年10月11日 下午4:28:14
	 */
	@Override
	public ResultInfo<?> saveApplyPay(PayApplyVo payApplyVo, User user) {
		payApplyVo.setAddTime(DateUtil.sysTimeMillis());
		payApplyVo.setCreator(user.getUserId());
		payApplyVo.setModTime(DateUtil.sysTimeMillis());
		payApplyVo.setUpdater(user.getUserId());
		payApplyVo.setCompanyId(user.getCompanyId());
		payApplyVo.setPayType(518);//售后
		String url = httpUrl + ErpConst.SAVE_AFTERSALES_APPLYPAY;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, payApplyVo, clientId, clientKey, TypeRef2);
			return result;
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}
	/**
	 * <b>Description:</b><br> 获取售后过程
	 * @param
	 * @param
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年10月11日 下午4:28:14
	 */
	@Override
	public AfterSalesRecord getAfterSalesRecord(AfterSalesRecord afterSalesRecord) {
		String url = httpUrl + ErpConst.GET_AFTERSALES_RECORD;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, afterSalesRecord, clientId, clientKey, TypeRef2);
			if(result == null || result.getCode() == -1){
				return null;
			}
			JSONObject json = JSONObject.fromObject(result.getData());
			AfterSalesRecord as = JsonUtils.readValue(json.toString(), AfterSalesRecord.class);
			return as;
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}

	@Override
	public AfterSales selectById(AfterSales as) {
		String url = httpUrl + "aftersales/order/selectbyid.htm";
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, as, clientId, clientKey, TypeRef2);
			if(result == null || result.getCode() ==-1){
				return null;
			}
			JSONObject json = JSONObject.fromObject(result.getData());
			AfterSales sa = JsonUtils.readValue(json.toString(), AfterSales.class);
			return sa;
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}

	@Override
	public ResultInfo<?> executeRefundOperation(AfterSalesVo afterSalesVo, User user) {
		try {
			if (!Objects.equals(afterSalesVo.getType(),SysOptionConstant.ID_546)){
				ResultInfo<?> resultInfo = executeRefundOperationOld(afterSalesVo, user);
				return resultInfo;
			}
			if (Objects.equals(afterSalesVo.getType(),SysOptionConstant.ID_546)){
				ResultInfo<?> resultInfo = executeRefundOperationNewOnlyTh(afterSalesVo, user);
				return resultInfo;
			}
		}catch (Exception e){
			logger.info("执行退款运算错误",e);
			return ResultInfo.error("执行退款运算错误");
		}
		return ResultInfo.success();
	}

	@Override
	public ResultInfo<?> calRefund(AfterSalesVo afterSalesVo) {
		String url = httpUrl + ErpConst.CAL_REFUND;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, afterSalesVo, clientId, clientKey, TypeRef2);
			if(result != null && result.getCode().equals(0)){
				/**
				 * 执行退款运算处理账期信息
				 */
				dealOrderCustomerBillPeriodWithRefundOperation(afterSalesVo, result);
			}

			return result;
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}

	/**
	 * 旧版执行退款运算
	 * @param afterSalesVo
	 * @param user
	 * @return
	 * @Note
	 */
	private ResultInfo<?> executeRefundOperationOld(AfterSalesVo afterSalesVo, User user) {
		Boolean expenseCheck = createExpenseCheck(afterSalesVo);
		if (!expenseCheck){
			logger.error("{}采购退货执行退款运算，生成费用单前置校验失败",afterSalesVo.getAfterSalesNo());
			return ResultInfo.error("生成费用单前置校验失败");
		}
		if(afterSalesVo != null && ObjectUtils.notEmpty(afterSalesVo.getTraderId())){
			RTraderJUser rTraderJUser = new RTraderJUser();
			rTraderJUser.setTraderId(afterSalesVo.getTraderId());
			rTraderJUser.setTraderType(1);
			List<RTraderJUser> users = rTraderJUserMapper.getUserTrader(rTraderJUser);
			if(users != null && users.size() > 0){
				afterSalesVo.setUserId(users.get(0).getUserId());
				Organization org = orgService.getOrgNameByUserId(users.get(0).getUserId());
				if(org != null){
					afterSalesVo.setOrgId(org.getOrgId());
					afterSalesVo.setOrgName(org.getOrgName());
				}
			}
		}
		afterSalesVo.setUpdater(user.getUserId());
		afterSalesVo.setModTime(DateUtil.sysTimeMillis());
		String url = httpUrl + ErpConst.EXECUTE_REFUND_OPERATION;
		logger.info("售后执行退款开始 afterSalesVo:{}", JSON.toJSONString(afterSalesVo));
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, afterSalesVo, clientId, clientKey, TypeRef2);
			if(result != null && result.getCode().equals(0)){
				//更新售后updataTime
				orderCommonService.updateAfterOrderDataUpdateTime(afterSalesVo.getAfterSalesId(),null,OrderDataUpdateConstant.AFTER_ORDER_REFUND);

				/**
				 * 执行退款运算处理账期信息
				 */
				dealOrderCustomerBillPeriodWithRefundOperation(afterSalesVo, result);

				/**
				 * 刷新售后单最终应退金额
				 */
				refreshFinalRefundableAmount(afterSalesVo, result);
				if (result.getCode().equals(2)){
					logger.info("支付服务费失败，供应商余额小于服务费,{}",afterSalesVo.getAfterSalesNo());
					return new ResultInfo<>(-1,"支付服务费失败，供应商余额小于服务费");
				}
			}
			if(result != null &&  result.getCode().equals(2)){
				try{
					createExpense(afterSalesVo);
				}catch (Exception e){
					logger.error("{}创建采购售后手续费费用单异常,{}",afterSalesVo.getAfterSalesNo(),e);
				}finally {
					return ResultInfo.success();
				}

			}
			return result;
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}

	/**
	 * <b>Description:</b><br> 执行退款运算操作
	 * @param afterSalesVo
	 * @param user
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年10月11日 下午4:28:14
	 */
	public ResultInfo<?> executeRefundOperationNewOnlyTh(AfterSalesVo afterSalesVo, User user) throws Exception{
		logger.info("采购售后执行退款开始：{}",JSON.toJSONString(afterSalesVo));
		ExecuteBuyorderReturnDto returnDto = new ExecuteBuyorderReturnDto();
		// 是否含返利结算
		Boolean isRebateSettlement = isRebateSettlement(afterSalesVo.getType(),afterSalesVo.getOrderId(),returnDto,afterSalesVo.getAfterSalesId());
		if (isRebateSettlement){
			// 结算单前置校验
			Boolean settleCheck = createSettleCheck(afterSalesVo);
			if (!settleCheck){
				logger.error("{}采购退货执行退款运算，生成结算单前置校验失败",afterSalesVo.getAfterSalesNo());
				return ResultInfo.error("生成结算单前置校验失败");
			}
		}
		afterSalesVo.setUpdater(user.getUserId());
		afterSalesVo.setModTime(DateUtil.sysTimeMillis());
		// 采购售后退货
		ErpSpringBeanUtil.getBean(this.getClass()).executeBuyOrderReturnOperation(isRebateSettlement,afterSalesVo,returnDto);
		try{
			// 生成费用单
			createExpense(afterSalesVo);
		}catch (Exception e){
			logger.error("{}创建采购售后手续费费用单异常,{}",afterSalesVo.getAfterSalesNo(),e);
		}finally {
			return ResultInfo.success();
		}
	}

	/**
	 * <b>Description:</b><br> 执行退款运算操作
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年10月11日 下午4:28:14
	 */
	@Transactional(rollbackFor = Exception.class)
	public void executeBuyOrderReturnOperation(Boolean isRebateSettlement,AfterSalesVo afterSales,ExecuteBuyorderReturnDto returnDto) throws Exception{
		// 上下文参数
		AfterSalesVo afterSalesVo = getNewAfterSalesVo(afterSales);
		// 预退金额（不含返利）
		BigDecimal refundNoRebateMoney = this.refundNoRebateMoney(afterSalesVo,returnDto);

		// 1、优先退返利
		tryCreateFlSettlement(afterSalesVo,isRebateSettlement);
		// 2、多余的钱偿还账期
		periodBack(returnDto, refundNoRebateMoney, afterSalesVo);
		// 3、多余的钱支付服务费，尝试退还余额（返利无服务费）
		refundAmount(returnDto,refundNoRebateMoney,afterSalesVo);
		// 4、更新状态
		updatePayStatus(returnDto,afterSalesVo,refundNoRebateMoney,isRebateSettlement);
	}

	/**
	 * 匹配当前售后单结算金额
	 * @param afterSalesVo
	 * @return
	 */
	private BigDecimal getCurrentAfterSalesSettleAmount(AfterSalesVo afterSalesVo) {
		Integer buyOrderId = afterSalesVo.getOrderId();
		Integer afterSaleId = afterSalesVo.getAfterSalesId();
		// 获取采购单返利支付结算单
		SettlementBillQueryCmd cmd = new SettlementBillQueryCmd(buyOrderId, BusinessSourceTypeEnum.buyOrder);
		SettlementBillApiDto buyOrderSettlementBill = settlementBillApiService.getSettlementByBusiness(cmd);
		if (Objects.isNull(buyOrderSettlementBill)){
			logger.info("未查到对应采购结算单");
			return BigDecimal.ZERO;
		}
		List<SettlementBillItemApiDto> buyOrderSettlementBillItem = buyOrderSettlementBill.getSettlementBillItemDtoList();
		if (CollectionUtils.isEmpty(buyOrderSettlementBillItem)){
			logger.info("未查到对应采购结算单明细");
			return BigDecimal.ZERO;
		}
		// 采购单返利支付结算单明细
		Map<Integer,SettlementBillItemApiDto> buyOrderSettlementItemMap = buyOrderSettlementBillItem.stream()
				.filter(e-> SettlementTypeEnum.REBATE_PAY.getSupplierAssetEnum().getCode().equals(e.getSettlementType()))
				.collect(Collectors.toMap(SettlementBillItemApiDto::getBusinessItemId, Function.identity(),(key1, key2)->key1));
		if (MapUtils.isEmpty(buyOrderSettlementItemMap)){
			logger.info("未查到对应采购返利结算单明细");
			return BigDecimal.ZERO;
		}
		List<AfterSalesGoodsDto> afterSalesGoodsList = buyorderAfterSalesApiService.getAfterSalesGoodsByAfterSalesId(afterSaleId);

		List<SettlementBillItemApiDto> settlementBillItemDtoList = new ArrayList<>();
		for (AfterSalesGoodsDto afterSalesGoods : afterSalesGoodsList) {
			Integer orderDetailId = afterSalesGoods.getOrderDetailId();
			// 匹配返利支付结算单明细
			SettlementBillItemApiDto buyOrderSettlementItem = buyOrderSettlementItemMap.get(orderDetailId);
			if (Objects.isNull(buyOrderSettlementItem)){
				continue;
			}
			// 返利支付时结算单价
			BigDecimal price = buyOrderSettlementItem.getPrice();
			// 售后数量
			BigDecimal number = BigDecimal.valueOf(afterSalesGoods.getNum());

			SettlementBillItemApiDto settlementBillItemDto = new SettlementBillItemApiDto();
			settlementBillItemDto.setPrice(price);
			settlementBillItemDto.setNumber(number);
			settlementBillItemDtoList.add(settlementBillItemDto);
		}
		logger.info("售后匹配结算单结果：{}",JSON.toJSONString(settlementBillItemDtoList));
		// 计算售后返利结算总额
		BigDecimal currentAfterSalesSettleAmount = settlementBillItemDtoList.stream().map(e-> e.getPrice().multiply(e.getNumber())).reduce(BigDecimal.ZERO, BigDecimal::add);
		return currentAfterSalesSettleAmount;
	}

	private AfterSalesVo getNewAfterSalesVo(AfterSalesVo afterSalesVo) {
		AfterSalesVo afterSalesVo2 = afterSalesMapper.viewAfterSalesDetailBuyorder(afterSalesVo);

		if(afterSalesVo.getUpdater()!=null && afterSalesVo.getUpdater()>0){
			afterSalesVo2.setUpdater(afterSalesVo.getUpdater());
			afterSalesVo2.setModTime(afterSalesVo.getModTime());
		}
		return afterSalesVo2;
	}


	/**
	 * 更新订单退款状态和金额
	 * 退款状态 0无退款 1未退款 2部分退款 3已退款
	 */
	public void updatePayStatus(ExecuteBuyorderReturnDto returnDto,AfterSalesVo afterSalesVo,BigDecimal refundReadyMoney,Boolean isRebateSettlement) {
		logger.info("4、开始计算退款状态");
		// 账期总金额
		BigDecimal debtPeriodMoney = returnDto.getDebtPeriodMoney();
		AfterSalesDetail afterSalesDetail = new AfterSalesDetail();
		afterSalesDetail.setAfterSalesDetailId(afterSalesVo.getAfterSalesDetailId());

		//退到贝登在供应商的余额
		if (afterSalesVo.getRefund() == 2) {
			logger.info("退款到贝登在供应商的余额");
			// 预退款（不含返利）- 账期总金额
			BigDecimal withoutDebt = refundReadyMoney.subtract(debtPeriodMoney);
			// 有返利
			if (isRebateSettlement) {
				// 已退款
				afterSalesDetail.setRefundAmountStatus(3);
			}
			// 无返利
			if (!isRebateSettlement) {
				// 无退款 or 已退款
				afterSalesDetail.setRefundAmountStatus(withoutDebt.compareTo(BigDecimal.ZERO) <= 0 ? 0 : 3);
			}
		}

		// 退到公司账户
		if (afterSalesVo.getRefund() == 1) {
			logger.info("退款到公司账户");
			// 预退款（不含返利） - 服务费 - 账期总金额 (最终剩余的钱)
			BigDecimal subtract = refundReadyMoney.subtract(debtPeriodMoney.add(afterSalesVo.getServiceAmount()));
			// 有返利
			if (isRebateSettlement) {
				// 未退款 or 已退款
				afterSalesDetail.setRefundAmountStatus(subtract.compareTo(BigDecimal.ZERO) > 0 ? 1 : 3);
			}

			// 无返利
			if (!isRebateSettlement) {
				if (subtract.compareTo(BigDecimal.ZERO) < 0
						|| (subtract.compareTo(BigDecimal.ZERO) == 0 && afterSalesVo.getServiceAmount().compareTo(BigDecimal.ZERO) == 0)) {
					// 无退款
					afterSalesDetail.setRefundAmountStatus(0);
				} else if (subtract.compareTo(BigDecimal.ZERO) == 0) {
					// 已退款
					afterSalesDetail.setRefundAmountStatus(3);
				} else {
					// 未退款
					afterSalesDetail.setRefundAmountStatus(1);
				}
			}
		}
		// 计算账期金额和实退金额
		logger.info("更新订单付款状态，是否含返利：{}，入参:{}",isRebateSettlement,JSON.toJSONString(afterSalesDetail));
		int count = afterSalesDetailMapper.updateByPrimaryKeySelective(afterSalesDetail);
		logger.info("{}更新订单付款状态结果，count:{}", afterSalesVo.getAfterSalesNo(), count);
	}


	/**
	 * 退款金额
	 * @param returnDto
	 * @param refundReadyMoney
	 * @param afterSalesVo
	 * @return
	 * @throws Exception
	 */
	public void refundAmount(ExecuteBuyorderReturnDto returnDto,BigDecimal refundReadyMoney,AfterSalesVo afterSalesVo) throws Exception{
		logger.info("3、多余的钱支付服务费，尝试退还余额（返利无服务费）");
		BigDecimal debtPeriodMoney = returnDto.getDebtPeriodMoney();
		// 服务费
		BigDecimal serviceAmount = afterSalesVo.getServiceAmount();
		// 还了账期后，还剩的预退款
		BigDecimal refundWithoutDebt = refundReadyMoney.subtract(debtPeriodMoney);
		logger.info("除去账期后，预退款剩余：{}，服务费：{}",refundWithoutDebt,serviceAmount);
		if(serviceAmount.compareTo(BigDecimal.ZERO) > 0){
			logger.info("{}准备支付服务费，需要生成采购费用单",afterSalesVo.getAfterSalesNo());
			// 服务费退还(无需更新余额，需要生成费用单，费用单无需扣减)
			accountBalancesCapital(returnDto,serviceAmount,afterSalesVo);
		}

		if (afterSalesVo.getRefund() == 2){
			BigDecimal remain = calRealRefundAmount(returnDto,refundReadyMoney,afterSalesVo);
			if (remain.compareTo(BigDecimal.ZERO) <= 0){
				logger.info("{}预退款金额小于等于0，无需退到余额",afterSalesVo.getAfterSalesNo());
				return;
			}
			// 最后剩余的前退还余额
			logger.info("除去服务费后，预退款剩余：{}，全部退还到余额",remain);
			accountBalancesCapital(returnDto,remain,afterSalesVo);
			updateSupplier(afterSalesVo,refundWithoutDebt);
		}
	}

	private BigDecimal calRealRefundAmount(ExecuteBuyorderReturnDto returnDto,BigDecimal refundReadyMoney,AfterSalesVo afterSalesVo){
		logger.info("3、多余的钱支付服务费，尝试退还余额（返利无服务费）");
		BigDecimal debtPeriodMoney = returnDto.getDebtPeriodMoney();
		// 服务费
		BigDecimal serviceAmount = afterSalesVo.getServiceAmount();
		// 还了账期后，还剩的预退款
		BigDecimal refundWithoutDebt = refundReadyMoney.subtract(debtPeriodMoney);
		logger.info("除去账期后，预退款剩余：{}，服务费：{}",refundWithoutDebt,serviceAmount);
		BigDecimal remain = refundWithoutDebt.subtract(serviceAmount);
		if (remain.compareTo(BigDecimal.ZERO) <= 0){
			remain = BigDecimal.ZERO;
		}
		return remain;
	}

	public void updateSupplier(AfterSalesVo afterSalesVo,BigDecimal amount){
		Integer traderId = afterSalesVo.getTraderId();
		logger.info("{}更新供应商余额，TraderID:{}，金额：{}",afterSalesVo.getAfterSalesNo(),traderId,amount);
		traderSupplierApiService.updateSupplierAmount(traderId,amount);
	}

	/**
	 * 更新余额
	 * @param accountBalanceMoney
	 * @param afterSalesVo
	 * @throws Exception
	 */
	public void accountBalancesCapital(ExecuteBuyorderReturnDto returnDto,BigDecimal accountBalanceMoney,AfterSalesVo afterSalesVo) {
		if (accountBalanceMoney.compareTo(BigDecimal.ZERO) <= 0){
			logger.info("{}更新余额小于等于0无需生成流水{}",afterSalesVo.getAfterSalesNo(),accountBalanceMoney);
			return;
		}
		CapitalBill cb = new CapitalBill();
		CapitalBillDetail cbd = new CapitalBillDetail();
		cb.setAmount(accountBalanceMoney);
		//退还余额
		cb.setTraderMode(530);
		//转入
		cb.setTraderType(4);
		cb.setPayee(afterSalesVo.getPayee());
		cb.setPayer(returnDto.getTraderName());
		//退款
		cbd.setBussinessType(531);
		cbd.setUserId(afterSalesVo.getUserId());
		saveCapitalBill(afterSalesVo,cb,cbd,2);
	}

	/**
	 * 多余的钱偿还账期
	 * @param refundReadyMoney
	 * @param afterSalesVo
	 * @throws Exception
	 */
	public void periodBack(ExecuteBuyorderReturnDto returnDto,BigDecimal refundReadyMoney,AfterSalesVo afterSalesVo) throws Exception{
		logger.info("2、多余的钱偿还账期");
		BigDecimal periodAmount = getPeriodAmount(returnDto, refundReadyMoney, afterSalesVo);
		if(periodAmount.compareTo(BigDecimal.ZERO) == 0){
			return;
		}
		// 偿还账期
		periodBackCapital(periodAmount,returnDto,afterSalesVo);
	}

	/**
	 * 计算应退账期金额
	 * @param refundReadyMoney
	 * @param afterSalesVo
	 * @return
	 */
	private BigDecimal getPeriodAmount(ExecuteBuyorderReturnDto returnDto,BigDecimal refundReadyMoney,AfterSalesVo afterSalesVo){
		BigDecimal debtPeriodMoney = returnDto.getDebtPeriodMoney();
		logger.info("{}当前账期金额：{}",afterSalesVo.getAfterSalesNo(),debtPeriodMoney);

		if(debtPeriodMoney.compareTo(BigDecimal.ZERO) <= 0 ){
			logger.info("{}账期金额为：{}，无需归还账期",afterSalesVo.getAfterSalesNo(),debtPeriodMoney);
			return BigDecimal.ZERO;
		}
		if(refundReadyMoney.compareTo(BigDecimal.ZERO) <=  0 ){
			logger.info("{}预退金额为：{}，无需归还账期",afterSalesVo.getAfterSalesNo(),refundReadyMoney);
			return BigDecimal.ZERO;
		}
		// 计算应退账期金额
		BigDecimal periodAmount = refundReadyMoney.compareTo(debtPeriodMoney) > 0 ? debtPeriodMoney : refundReadyMoney;
		return periodAmount;
	}

	public void periodBackCapital(BigDecimal debtPeriodMoney,ExecuteBuyorderReturnDto returnDto,AfterSalesVo afterSalesVo) throws Exception{
		logger.info("{}偿还账期开始,账期金额{}",afterSalesVo.getAfterSalesNo(),debtPeriodMoney);
		CapitalBill cb = new CapitalBill();
		cb.setAmount(debtPeriodMoney);
		//退还信用
		cb.setTraderMode(529);
		//转移
		cb.setTraderType(3);
		cb.setPayee(returnDto.getTraderName());
		cb.setPayer(afterSalesVo.getPayee());
		CapitalBillDetail cbd = new CapitalBillDetail();
		//退款
		cbd.setBussinessType(531);
		cbd.setUserId(afterSalesVo.getUserId());
		saveCapitalBill(afterSalesVo,cb,cbd,2);
	}

	public void saveCapitalBill(AfterSalesVo afterSalesVo, CapitalBill cb, CapitalBillDetail cbd, Integer traderType) {
		String afterSalesNo = afterSalesVo.getAfterSalesNo();
		CapitalBillDto capitalBill = new CapitalBillDto();
		capitalBill.setCreator(afterSalesVo.getUpdater());
		capitalBill.setAddTime(DateUtil.sysTimeMillis());
		capitalBill.setCurrencyUnitId(1);
		capitalBill.setTraderTime(DateUtil.sysTimeMillis());
		capitalBill.setTraderType(cb.getTraderType());
		capitalBill.setTraderSubject(1);
		capitalBill.setTraderMode(cb.getTraderMode());
		capitalBill.setAmount(cb.getAmount());
		capitalBill.setPayee(cb.getPayee());//收款方
		capitalBill.setPayer(cb.getPayer());//付款方
		capitalBill.setCompanyId(afterSalesVo.getCompanyId());
		capitalBillApiService.insertCapitalBill(capitalBill);

		Integer capitalBillId = capitalBill.getCapitalBillId();

		List<CapitalBillDetailDto> capitalBillDetailso = new ArrayList<>();
		CapitalBillDetailDto capitalBillDetail2 = new CapitalBillDetailDto();
		capitalBillDetail2.setAmount(capitalBill.getAmount());
		capitalBillDetail2.setBussinessType(cbd.getBussinessType());//退款
		capitalBillDetail2.setOrderType(3);//售后
		capitalBillDetail2.setRelatedId(afterSalesVo.getAfterSalesId());
		capitalBillDetail2.setOrderNo(afterSalesNo);
		capitalBillDetail2.setUserId(cbd.getUserId());
		capitalBillDetail2.setOrgId(cbd.getOrgId());
		capitalBillDetail2.setOrgName(cbd.getOrgName());
		capitalBillDetail2.setTraderId(afterSalesVo.getTraderId());//交易者id
		capitalBillDetail2.setTraderType(traderType);//客户/供应商
		capitalBillDetail2.setCapitalBillId(capitalBillId);
		capitalBillDetailso.add(capitalBillDetail2);

		//生成资金流水号
		CapitalBillDto capitalBillExtra = new CapitalBillDto();
		capitalBillExtra.setCapitalBillId(capitalBillId);
		capitalBillExtra.setCapitalBillNo(orderNoDict.getOrderNum(capitalBillId,10));
		logger.info("生成资金流水号：{}",capitalBillExtra.getCapitalBillNo());
		capitalBillApiService.updateCapitalBillNo(capitalBillExtra);
		capitalBillApiService.insertCapitalBillDetail(capitalBillDetailso);
		logger.info("{}生成资金流水：{}",afterSalesNo,JSON.toJSONString(capitalBill));
		logger.info("{}生成资金流水明细：{}",afterSalesNo,JSON.toJSONString(capitalBillDetailso));
	}

	/**
	 * 计算预退款，不含返利
	 * @param afterSalesVo
	 * @param returnDto
	 * @return
	 * @throws Exception
	 */
	private BigDecimal refundNoRebateMoney(AfterSalesVo afterSalesVo,ExecuteBuyorderReturnDto returnDto) throws Exception{
		Integer buyOrderId = afterSalesVo.getOrderId();
		BigDecimal buyOrderSettleAmount = returnDto.getBuyOrderSettleAmount();
		BigDecimal afterSalesAmountFl = capitalBillApiService.getAfterSalesFlByBuyOrderId(buyOrderId);
		logger.info("{}当前已售后返利金额：{}",afterSalesVo.getAfterSalesNo(),afterSalesAmountFl);
		BigDecimal currentAfterSalesSettleAmount = returnDto.getCurrentAfterSalesSettleAmount();
		logger.info("订单结算金额：{},本次结算售后:{}",buyOrderSettleAmount,currentAfterSalesSettleAmount);

		returnDto.setCurrentAfterSalesSettleAmount(currentAfterSalesSettleAmount);
		// 订单已付款金额
		BigDecimal payWithFl = Optional.ofNullable(afterSalesVo).map(AfterSalesVo::getPaymentAmount).orElseThrow(() -> new RunTimeServiceException(CommonConstants.FAIL_CODE,"未查到采购单付款金额"));
		// 订单已付款金额(不含返利)
		BigDecimal afterSalesFl = buyOrderSettleAmount.subtract(afterSalesAmountFl);
		logger.info("售后已付款，去掉返利{}={}-{}",afterSalesFl,buyOrderSettleAmount,afterSalesAmountFl);
		BigDecimal pay = payWithFl.subtract(afterSalesFl);

		logger.info("订单已付款金额(不含返利){}={}-{}",pay,payWithFl,buyOrderSettleAmount);

		// 本次售后单退款金额
		BigDecimal refundTotalAmount = Optional.ofNullable(afterSalesVo).map(AfterSalesVo::getRefundAmount).orElseThrow(() -> new RunTimeServiceException(CommonConstants.FAIL_CODE,"未查到售后单退款金额"));
		// ==>本次售后单退款金额(不含返利)
		BigDecimal refundAmount = refundTotalAmount.subtract(currentAfterSalesSettleAmount);
		logger.info("本次售后单退款金额(不含返利){}={}-{}",refundAmount,refundTotalAmount,currentAfterSalesSettleAmount);

		// 采购单总金额(含返利)
		BigDecimal buyOrderTotalAmount = Optional.ofNullable(afterSalesVo).map(AfterSalesVo::getTotalAmount).orElseThrow(() -> new RunTimeServiceException(CommonConstants.FAIL_CODE,"未查到采购单总金额"));
		// 采购单总金额(不含返利)
		BigDecimal buyOrderTotalAmountNoFl = buyOrderTotalAmount.subtract(buyOrderSettleAmount);
		logger.info("采购单总金额(不含返利){}={}-{}",buyOrderTotalAmountNoFl,buyOrderTotalAmount,buyOrderSettleAmount);

		// 查询当前采购订单的所有已完结退货售后（含返利）  结果含本次售后单
		BigDecimal tkAmount = afterSalesMapper.getSumTKAfterSalesByBuyorderid(afterSalesVo.getOrderId());
		// 查询当前采购订单的所有已完结退货售后（仅返利）
		BigDecimal tkFlAmount = afterSalesMapper.getSumFlTKAfterSalesByBuyorderid(afterSalesVo.getOrderId());
		// 查询当前采购订单的所有已完结退货售后（不含返利）
		BigDecimal tkAmountNoFl = tkAmount.subtract(Optional.ofNullable(tkFlAmount).orElse(BigDecimal.ZERO));
		logger.info("查询当前采购订单的所有已完结退货售后（不含返利){}={}+{}",tkAmountNoFl,tkAmount,tkFlAmount);

		// ==>订单金额(不含返利)
		BigDecimal totalAmount = buyOrderTotalAmountNoFl.subtract(tkAmountNoFl);
		logger.info("订单金额(不含返利){}={}-{}",totalAmount,buyOrderTotalAmountNoFl,tkAmountNoFl);

		Integer orderId = Optional.ofNullable(afterSalesVo).map(AfterSalesVo::getOrderId).orElseThrow(() -> new RunTimeServiceException(CommonConstants.FAIL_CODE,"未查到采购订单ID"));
		// 账期总金额
		BigDecimal debtPeriodMoney = buyorderMapper.getLackAccountPeriodAmount(orderId);

		// 订单已付款金额(含账期、不含返利)
		BigDecimal payAmount = pay.add(debtPeriodMoney);
		logger.info("订单已付款金额(含账期、不含返利){}={}+{}",payAmount,pay,debtPeriodMoney);

		// ------- 预退款金额=订单已付款金额(含账期、不含返利)+本次售后单退款金额(不含返利)-订单金额(不含返利) -------
		BigDecimal refundReadyMoney = payAmount.add(refundAmount).subtract(totalAmount);
		logger.info("预退款金额{}=订单已付款金额(含账期、不含返利)：{}+本次售后单退款金额(不含返利)：{}-订单金额(不含返利)：{}",refundReadyMoney,payAmount,refundAmount,totalAmount);
		returnDto.setDebtPeriodMoney(debtPeriodMoney);
		returnDto.setTraderName(afterSalesVo.getTraderName());
		return refundReadyMoney;
	}

	/**
	 * 创建费用单
	 * @param afterSalesVo
	 */
	private void createExpense(AfterSalesVo afterSalesVo){
		logger.info("{}创建费用单:{}",afterSalesVo.getAfterSalesNo(),afterSalesVo.getServiceAmount());

		AfterSalesDetailVo info = afterSalesDetailMapper.getAfterSalesInfoByAfterSalesId(afterSalesVo.getAfterSalesId());
		if (info.getServiceAmount().compareTo(BigDecimal.ZERO) == 0){
			logger.info("{}无需创建费用单，售后服务金额为0",afterSalesVo.getAfterSalesNo());
			return;
		}
		BuyOrderAfterSalesHandlingFeeExpenseDto dto = new BuyOrderAfterSalesHandlingFeeExpenseDto();
		dto.setAfterSalesId(afterSalesVo.getAfterSalesId());
		dto.setAfterSalesNo(afterSalesVo.getAfterSalesNo());
		dto.setBuyorderId(info.getOrderId());
		dto.setBuyorderNo(info.getOrderNo());
		dto.setSku(DEFAULT_FY);
		dto.setNum(1);
		dto.setPrice(info.getServiceAmount());
		CoreSkuDto coreSkuDtoBySkuNo = coreSkuApiService.getCoreSkuDtoBySkuNo(DEFAULT_FY);
		if(Objects.isNull(coreSkuDtoBySkuNo)){
			logger.error("{}创建采购售后手续费费用单失败,原因：未查到V253620，请关注：{}",afterSalesVo.getAfterSalesNo(),JSON.toJSONString(afterSalesVo));
			return;
		}
		dto.setInvoiceType(info.getInvoiceType());
		dto.setGoodsId(coreSkuDtoBySkuNo.getSkuId());
		// 创建费用单
		buyorderExpenseApiService.addBuyOrderAfterSalesExpense(dto);
	}

	/**
	 * 创建费用单前置校验
	 * @param afterSalesVo
	 * @return
	 */
	private Boolean createExpenseCheck(AfterSalesVo afterSalesVo){
		AfterSalesDetailVo info = afterSalesDetailMapper.getAfterSalesInfoByAfterSalesId(afterSalesVo.getAfterSalesId());
		if(Objects.isNull(info)){
			logger.info("{}执行退款运算，未查到售后单明细，校验不通过",afterSalesVo.getAfterSalesNo());
			return Boolean.FALSE;
		}
		if(info.getAfterType().intValue() != 546){
			// 非采购退货
			return Boolean.TRUE;
		}
		if(Objects.isNull(info.getServiceAmount()) || info.getServiceAmount().compareTo(BigDecimal.ZERO) == 0 ){
			// 无手续费
			return Boolean.TRUE;
		}
		CoreSkuDto coreSkuDtoBySkuNo = coreSkuApiService.getCoreSkuDtoBySkuNo(DEFAULT_FY);
		if(Objects.isNull(coreSkuDtoBySkuNo)){
			logger.info("{}执行退款运算，未查到费用虚拟商品{}，校验不通过",afterSalesVo.getAfterSalesNo(),DEFAULT_FY);
			return Boolean.FALSE;
		}
		return Boolean.TRUE;
	}

	/**
	 * 创建结算单前置校验
	 * @param afterSalesVo
	 * @return
	 */
	private Boolean createSettleCheck(AfterSalesVo afterSalesVo){
		// 是否含返利支付
		Integer afterSalesId = afterSalesVo.getAfterSalesId();
		List<AfterSalesGoodsDto> afterSalesGoods = buyorderAfterSalesApiService.getAfterSalesGoodsByAfterSalesId(afterSalesId);
		if (CollectionUtils.isEmpty(afterSalesGoods)){
			logger.info("前置校验,执行退款运算执行结算时，未查到售后单商品，校验不通过",afterSalesVo.getAfterSalesNo());
			return Boolean.FALSE;
		}
		AfterSalesApiDto afterSalesApiDto = buyorderAfterSalesApiService.getAfterSalesByAfterSalesId(afterSalesId);
		if(Objects.isNull(afterSalesApiDto)){
			logger.info("前置校验,执行退款运算执行结算时，未查到售后单信息，校验不通过",afterSalesVo.getAfterSalesNo());
			return Boolean.FALSE;
		}
		// 默认通过
		return Boolean.TRUE;
	}

	/**
	 * 判断是否含返利结算(返利结算金额可能为0)
	 * @param type 售后类型
	 * @param orderId 订单id
	 * @return
	 */
	private Boolean isRebateSettlement(Integer type,Integer buyOrderId,ExecuteBuyorderReturnDto returnDto,Integer afterSalesId){
		if(!Objects.equals(type,SysOptionConstant.ID_546)){
			logger.info("非采购退货，无需创建结算单");
			return Boolean.FALSE;
		}
		// 采购单返利支付结算单
		SettlementBillQueryCmd cmd = new SettlementBillQueryCmd(buyOrderId,BusinessSourceTypeEnum.buyOrder);
		SettlementBillApiDto settlementBillApiDto = settlementBillApiService.getSettlementByBusiness( cmd);
		if (Objects.isNull(settlementBillApiDto)){
			logger.info("采购退货，查询到采购单支付结算时，未使用返利");
			return Boolean.FALSE;
		}
		// 采购单返利支付结算单明细（SKU维度）
		List<SettlementBillItemApiDto> buyOrderSettlementBillItem = settlementBillApiDto.getSettlementBillItemDtoList();

		List<SettlementBillItemApiDto> settlementBillItemApiDtos = buyOrderSettlementBillItem.stream()
				.filter(e -> SettlementTypeEnum.REBATE_PAY.getSupplierAssetEnum().getCode().equals(e.getSettlementType())).collect(Collectors.toList());

		// 返利单的结算金额
		BigDecimal settleAmount = settlementBillItemApiDtos.stream().map(SettlementBillItemApiDto::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
		returnDto.setBuyOrderSettleAmount(settleAmount);

		AfterSalesVo afterSalesVo = new AfterSalesVo();
		afterSalesVo.setAfterSalesId(afterSalesId);
		afterSalesVo.setOrderId(buyOrderId);
		BigDecimal currentAfterSalesSettleAmount = this.getCurrentAfterSalesSettleAmount(afterSalesVo);
		returnDto.setCurrentAfterSalesSettleAmount(currentAfterSalesSettleAmount);
		logger.info("采购退货，使用返利结算，金额：{}",settleAmount);
		if (currentAfterSalesSettleAmount.compareTo(BigDecimal.ZERO) > 0){
			return Boolean.TRUE;
		}
		logger.info("该售后单对应的采购单使用了返利，金额：{}，但该售后单中不含返利商品",settleAmount);
		return Boolean.FALSE;
	}

	/**
	 * 创建返利结算单
	 * @param afterSalesVo
	 */
	private void tryCreateFlSettlement(AfterSalesVo afterSalesVo, Boolean isRebateSettlement){
		logger.info("1、优先尝试退返利");
		if (!isRebateSettlement){
			logger.info("无需退还返利");
			return;
		}
		Integer afterSalesId = afterSalesVo.getAfterSalesId();
		SettlementBillCreateCmd settlementBillCreateCmd = new SettlementBillCreateCmd(BusinessSourceTypeEnum.buyOrderAfterSale,afterSalesId,Arrays.asList(SettlementTypeEnum.REBATE_REFUND));
		settlementBillApiService.createAlsoSettlement(settlementBillCreateCmd);
	}

	/**
	 * 刷新售后单最终应退金额
	 *
	 * @param afterSalesVo
	 * @param result
	 */
	private void refreshFinalRefundableAmount(AfterSalesVo afterSalesVo, ResultInfo<?> result){
		logger.info("refreshFinalRefundableAmount afterSalesVo:{}", JSON.toJSONString(afterSalesVo));
		AfterSalesVo afterSalesResult;
		switch (AfterSalesProcessEnum.getInstance(afterSalesVo.getType())){
			case AFTERSALES_TK: {
				logger.info("刷新售后单最终应退金额售后单为退款类型 string:{}", result.getData().toString());
				afterSalesResult =	JSON.parseObject(result.getData().toString(),AfterSalesVo.class);
				break;
			}
			default:{
				logger.info("该售后单类型执行退款运算暂无需要刷新的最终退款金额 afterSalesVo:{}", JSON.toJSONString(afterSalesVo));
				return;
			}
		}
		logger.info("刷新售后单最终应退金额执行退款运算返回信息 afterSalesResult:{}", JSON.toJSONString(afterSalesResult));
		afterSalesDetailMapper.updateFinalRefundableAmountByDetalId(afterSalesResult.getAfterSalesDetailId(),
				afterSalesResult.getFinalRefundableAmount());
	}

	/**
	 * 执行退款运算处理账期信息
	 *
	 * @param afterSalesVo
	 * @param result
	 */
	private void dealOrderCustomerBillPeriodWithRefundOperation(AfterSalesVo afterSalesVo, ResultInfo<?> result) {
		if (!afterSalesVo.getType().equals(539)){
			logger.info("执行退款运算的售后订单类型非退货类型 type:{}", afterSalesVo.getType());
			return;
		}
		logger.info("执行退款运算处理账期信息 string:{}", result.getData().toString());
		try {
			AfterSalesVo afterSalesResult =	JSON.parseObject(result.getData().toString(),AfterSalesVo.class);
			logger.info("执行退款运算返回信息 afterSalesResult:{}", JSON.toJSONString(afterSalesResult));
			orderAccountPeriodService.dealOrderCustomerBillPeriodWithRefundOperation(afterSalesResult);
		} catch (CustomerBillPeriodException e) {
					logger.error("执行退款运算处理订单冻结和占用的账期金额释放错误 message:{}"+result.getData().toString(), e.getMessage());
		}
		 catch (Exception e){
			logger.error("执行退款运算处理订单冻结和占用的账期金额释放错误", e);
		}
	}

	/**
	 * <b>Description:</b><br> 保存退货的申请付款
	 * @param payApplyVo
	 * @param user
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年10月11日 下午4:28:14
	 */
	@Override
	public ResultInfo<?> saveRefundApplyPay(PayApplyVo payApplyVo, User user, boolean isUpgrade) {
		ResultInfo resultInfo = new ResultInfo(0,"保存成功");
		payApplyVo.setAddTime(DateUtil.sysTimeMillis());
		payApplyVo.setCreator(user.getUserId());
		payApplyVo.setModTime(DateUtil.sysTimeMillis());
		payApplyVo.setUpdater(user.getUserId());
		payApplyVo.setCompanyId(user.getCompanyId());
		payApplyVo.setPayType(SysOptionConstant.ID_518);//售后

		AfterSales afterSales = new AfterSales();
		afterSales.setAfterSalesId(payApplyVo.getRelatedId());
		AfterSalesVo afterSalesVo = afterSalesMapper.viewAfterSalesDetailSaleorder(afterSales);

		payApplyVo.setTraderName(afterSalesVo.getPayee());
		payApplyVo.setBank(afterSalesVo.getBank());
		payApplyVo.setAmount(isUpgrade ? payApplyVo.getPayApplyTotalAmount() : afterSalesVo.getRealRefundAmount());
		payApplyVo.setTotalAmount(isUpgrade ? payApplyVo.getPayApplyTotalAmount() : afterSalesVo.getRealRefundAmount());
		payApplyVo.setBankAccount(afterSalesVo.getBankAccount());
		payApplyVo.setBankCode(afterSalesVo.getBankCode());
		payApplyVo.setTraderSubject(afterSalesVo.getTraderSubject());
		PayApply payApply = new PayApply(payApplyVo.getCompanyId(), payApplyVo.getPayType(), payApplyVo.getRelatedId(),payApplyVo.getTraderSubject(),payApplyVo.getTraderMode(),payApplyVo.getTraderName(),
				payApplyVo.getAmount(), payApplyVo.getBank(), payApplyVo.getBankAccount(), payApplyVo.getBankCode(), payApplyVo.getAddTime(),
				payApplyVo.getCreator(),payApplyVo.getModTime(),payApplyVo.getUpdater());
		payApply.setComments(payApplyVo.getComments());
		int res = payApplyMapper.insertSelective(payApply);
		if(res == 0){
			resultInfo = new ResultInfo(-1,"保存售后付款申请失败");
			return resultInfo;
		}
		// 更新往来单位类型
		payApplyApiService.updateAccountType(payApply.getPayApplyId());
		PayApplyDetail payApplyDetail = new PayApplyDetail(payApply.getPayApplyId(), payApplyVo.getDetailgoodsId(), payApplyVo.getPrice(),
				payApplyVo.getNum(), payApplyVo.getTotalAmount());
		res = payApplyDetailMapper.insertSelective(payApplyDetail);
		if(res == 0){
			resultInfo = new ResultInfo(-1,"保存售后付款申请明细失败");
			return resultInfo;
		}
		resultInfo = new ResultInfo(0,"保存成功", payApply.getPayApplyId());
		return resultInfo;
	}

	@Autowired
	PayApplyService payApplyService;
	@Autowired
	ActionProcdefService actionProcdefService;
	@Autowired
	ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
	@Autowired
	VerifiesRecordService verifiesRecordService;

	@Override
	public void saveApplyApprovalProcess(CreateRefundApplyRequest createRefundApplyRequest, Integer payApplyId, Integer orgId ,HttpServletRequest request) {

		try {
			Map<String, Object> variableMap = new HashMap<String, Object>();
			// 开始生成流程(如果没有taskId表示新流程需要生成)
			AfterSales afterSales = afterSalesMapper.selectByPrimaryKey(createRefundApplyRequest.getAfterSalesId());
			PayApply payApplyInfo = payApplyService.getPayApplyInfo(payApplyId);

			payApplyInfo.setOrderNo(afterSales.getAfterSalesNo());
			payApplyInfo.setPayApplyId(payApplyId);

			variableMap.put("payApply", payApplyInfo);
			variableMap.put("currentAssinee", createRefundApplyRequest.getApplyUserName());
			variableMap.put("processDefinitionKey", "paymentVerify");
			variableMap.put("businessKey", "paymentVerify_" + payApplyInfo.getPayApplyId());
			variableMap.put("relateTableKey", payApplyInfo.getPayApplyId());
			variableMap.put("relateTable", "T_PAY_APPLY");
			variableMap.put("orgId", orgId);
			//售后付款申请
			variableMap.put("applyPayType", ErpConst.ONE);
			//售后付款为0
			variableMap.put("needManagerVerify", ErpConst.ZERO);
			// 流程条件标识
			variableMap.put("activitiType", "PaymentVerify");
			actionProcdefService.createProcessInstance(request, "paymentVerify",
					"paymentVerify_" + payApplyInfo.getPayApplyId(), variableMap);
			// 默认申请人通过
			// 根据BusinessKey获取生成的审核实例
			Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
					"paymentVerify_" + payApplyInfo.getPayApplyId());

			if (historicInfo.get("endStatus") != "审核完成") {

				Task taskInfo = (Task) historicInfo.get("taskInfo");
				String taskId = taskInfo.getId();

				Authentication.setAuthenticatedUserId(createRefundApplyRequest.getApplyUserName());

				Map<String, Object> variables = new HashMap<String, Object>();
				// 设置审核完成监听器回写参数
				variables.put("tableName", "T_PAY_APPLY");
				variables.put("id", "PAY_APPLY_ID");
				variables.put("idValue", payApplyInfo.getPayApplyId());
				variables.put("key", "VALID_STATUS");
				variables.put("value", ErpConst.ONE);
				// 回写数据的表在db中
				variables.put("db", ErpConst.TWO);

				// 默认审批通过
				ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, "",
				createRefundApplyRequest.getApplyUserName(), variables);

				// 如果未结束添加审核对应主表的审核状态
				if (!complementStatus.getData().equals("endEvent")) {
					verifiesRecordService.saveVerifiesInfo(taskId, ErpConst.ZERO);
				}

			}

		} catch (Exception e) {
			logger.error("saveRealRefundAmountApplyPay:", e);
			throw new ServiceException("任务完成操作失败：" + e.getMessage());

		}

	}

	@Override
	public ResultInfo sendInstallstionSms(AfterSalesInstallstionVo afterSalesInstallstionVo, User user) {
		ResultInfo resultInfo = new ResultInfo<>();
		//获取安调信息
		String url = httpUrl + "aftersalesinstallstion/getinstallstioninfo.htm";
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<AfterSalesInstallstionVo>> TypeRef2 = new TypeReference<ResultInfo<AfterSalesInstallstionVo>>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, afterSalesInstallstionVo, clientId, clientKey, TypeRef2);
			if(null != result){
				AfterSalesInstallstionVo installstionInfo = (AfterSalesInstallstionVo) result.getData();
				if(null != installstionInfo){
					String afterSalesNo = installstionInfo.getAfterSalesNo();
					Integer type = installstionInfo.getType();
					String traderContactName = installstionInfo.getTraderContactName();
					String traderContactMobile = installstionInfo.getTraderContactMobile();

					String name = installstionInfo.getName();
					String mobile = installstionInfo.getMobile();
					String typeName = "";
					switch (type) {
						case 541:
						case 4090:
						case 4091://安调
						typeName = "安调";
						break;
					case 550://安调
						typeName = "安调";
						break;
					case 584://维修
						typeName = "维修";
						break;
					case 585://维修
						typeName = "维修";
						break;
					default:
						break;
					}

					//发个工程师
					String terminal = afterSalesInstallstionVo.getTerminal();
					String equipment = afterSalesInstallstionVo.getEquipment();
					String comments = afterSalesInstallstionVo.getComments();
					if(null != mobile && !mobile.equals("")
							&& null != afterSalesNo && !afterSalesNo.equals("")
							&& null != typeName && !typeName.equals("")
							&& null != traderContactName && !traderContactName.equals("")
							&& null != traderContactMobile && !traderContactMobile.equals("")
							&& null != terminal && !terminal.equals("")
							&& null != equipment && !equipment.equals("")
							&& null != comments && !comments.equals("")
							){

						String content = "@1@="+afterSalesNo+",@2@="+typeName+",@3@="+traderContactName+",@4@="+traderContactMobile+",@5@="+terminal+",@6@="+equipment+",@7@="+comments;
						Boolean sendTplSms = smsService.sendTplSms(mobile, "JSM40187-0016", content);
						if(!sendTplSms){
							resultInfo.setMessage("发送给工程师短信失败");
							return resultInfo;
						}
					}

					//发给客户
					if(null != traderContactMobile && !traderContactMobile.equals("")
							&& null != afterSalesNo && !afterSalesNo.equals("")
							&& null != typeName && !typeName.equals("")
							&& null != name && !name.equals("")
							&& null != mobile && !mobile.equals("")
							){
						String traderContent = "@1@="+afterSalesNo+",@2@="+typeName+",@3@="+name+",@4@="+mobile;
						Boolean sendTplSms = smsService.sendTplSms(traderContactMobile, "JSM40187-0017", traderContent);
						if(!sendTplSms){
							resultInfo.setMessage("发送给客户短信失败");
							return resultInfo;
						}
					}

					AfterSalesInstallstion asiv = new AfterSalesInstallstion();
					asiv.setAfterSalesInstallstionId(afterSalesInstallstionVo.getAfterSalesInstallstionId());
					Long time = DateUtil.sysTimeMillis();
					asiv.setLastNoticeTime(time);
					asiv.setModTime(time);
					asiv.setUpdater(user.getUserId());
					asiv.setNoticeTimes(afterSalesInstallstionVo.getNoticeTimes() + 1);
					String url2 = httpUrl + ErpConst.SAVE_UPDATE_INSTALLSTION;

					// 定义反序列化 数据格式
					final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
					};
					ResultInfo<?> res = (ResultInfo<?>) HttpClientUtils.post(url2, asiv, clientId, clientKey, TypeRef);
					resultInfo.setCode(0);
					resultInfo.setMessage("操作成功");
				}
			}
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}

		return resultInfo;
	}

	/**
	 * <b>Description:</b><br> 保存售后开票申请
	 * @param invoiceApply
	 * @return
	 * @throws Exception
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年8月28日 下午2:08:31
	 */
	@Override
	public ResultInfo<?> saveOpenInvoceApply(InvoiceApply invoiceApply) {
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
			String url=httpUrl + "aftersales/order/saveopeninvoceapply.htm";
			ResultInfo<?> result = (ResultInfo<?>)HttpClientUtils.post(url, invoiceApply,clientId,clientKey, TypeRef);
			if(result!=null){
				return result;
			}
		} catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
			return new ResultInfo<>(-1, "操作异常");
		}
		return new ResultInfo<>(-1, "操作失败");
	}

	/**
	 * <b>Description:</b><br> 售后直发产品确认全部收货
	 * @param
	 * @return
	 * @throws Exception
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年8月28日 下午2:08:31
	 */
	@Override
	public ResultInfo<?> updateAfterSalesGoodsArrival(AfterSalesGoods afterSalesGoods) {
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
			String url=httpUrl + "aftersales/order/updateaftersalesgoodsarrival.htm";
			ResultInfo<?> result = (ResultInfo<?>)HttpClientUtils.post(url, afterSalesGoods,clientId,clientKey, TypeRef);
			if(result!=null){
				return result;
			}
		} catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
			return new ResultInfo<>(-1, "操作异常");
		}
		return new ResultInfo<>(-1, "操作失败");
	}

	/**
	 * <b>Description:</b><br> 查询售后服务费信息
	 * @param afterSalesDetail
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2018年4月16日 上午11:04:41
	 */
	@Override
	public AfterSalesDetailVo getAfterSalesDetailVoByParam(AfterSalesDetail afterSalesDetail) {
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
			String url=httpUrl + "aftersales/order/getaftersalesorderdetailvo.htm";
			ResultInfo<?> result = (ResultInfo<?>)HttpClientUtils.post(url, afterSalesDetail,clientId,clientKey, TypeRef);
			if(result == null  || result.getCode() == -1){
				return null;
			}
			JSONObject json = JSONObject.fromObject(result.getData());
			AfterSalesDetailVo sa = JsonUtils.readValue(json.toString(), AfterSalesDetailVo.class);
			if(sa != null){
				List<TraderAddress> taList = sa.getTaList();
				if(taList != null && taList.size() > 0){
					List<TraderAddressVo> list = new ArrayList<>();
					TraderAddressVo tav = null;
					for (TraderAddress ta : taList) {
						tav = new TraderAddressVo();
						tav.setTraderAddress(ta);
						tav.setArea(getAddressByAreaId(ta.getAreaId()));
						list.add(tav);
					}
					sa.setTavList(list);
				}
			}

			return sa;
		} catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}

	@Override
	public AfterSalesDetail selectadtById(AfterSales as) {
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
			String url=httpUrl + "aftersales/order/selectadtbyid.htm";
			ResultInfo<?> result = (ResultInfo<?>)HttpClientUtils.post(url, as,clientId,clientKey, TypeRef);
			if(result == null  || result.getCode() == -1){
				return null;
			}
			JSONObject json = JSONObject.fromObject(result.getData());
			AfterSalesDetail sa = JsonUtils.readValue(json.toString(), AfterSalesDetail.class);
			return sa;
		} catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}

	@Override
	public List<AfterSalesGoods> getAfterSalesGoodsNoOutList(Integer afterSalesId) {
		String url = httpUrl + "aftersales/order/getaftersalesgoodsnooutlist.htm";
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<List<AfterSalesGoods>>> TypeRef2 = new TypeReference<ResultInfo<List<AfterSalesGoods>>>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, afterSalesId, clientId, clientKey, TypeRef2);
			if(result == null || result.getCode() ==-1){
				return null;
			}
			JSONArray json = JSONArray.fromObject(result.getData());
			@SuppressWarnings("unchecked")
			List<AfterSalesGoods> list = (List<AfterSalesGoods>) JSONArray.toCollection(json, AfterSalesGoods.class);
			return list;
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}

	/**
	 *
	 * <b>Description:</b>获取费用类型(废弃)<br>
	 * @param
	 * @return
	 * @Note
	 * <b>Author:</b> Barry
	 * <br><b>Date:</b> 2018年07月24日 19:53
	 */
	@Override
	public List<CostType> getCostTypeById(Integer costType){
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<List<CostType>>> TypeRef = new TypeReference<ResultInfo<List<CostType>>>() {};
			String url=httpUrl + "aftersales/order/getCostTypeById.htm";
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, costType,clientId,clientKey, TypeRef);
			if(result == null  || result.getCode() == -1){
				return null;
			}
			List<CostType> costTypes = (List<CostType>)result.getData();
			return costTypes;
		} catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}

	@Override
	public List<AfterSalesCostVo> getAfterSalesCostListById(AfterSalesCost afterSalesCost) {
		try {
			List<AfterSalesCostVo> costList = afterSalesMapper.selectAfterSalesCostListById(afterSalesCost);
			costList.forEach(item -> item.setUserName(userMapper.selectByPrimaryKey(item.getUpdater()).getUsername()));
			return  costList;
		} catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}

	@Override
	public ResultInfo addAfterSalesCost(AfterSalesCost afterSalesCost) {
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
			String url=httpUrl + "aftersales/order/addAfterSalesCost.htm";
			ResultInfo<?> result = (ResultInfo<?>)HttpClientUtils.post(url, afterSalesCost,clientId,clientKey, TypeRef);
			return result;
		} catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}

	}

	@Override
	public ResultInfo deleteAfterSalesCostById(AfterSalesCost afterSalesCost) {
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
			String url=httpUrl + "aftersales/order/deleteAfterSalesCostById.htm";
			ResultInfo<?> result = (ResultInfo<?>)HttpClientUtils.post(url, afterSalesCost,clientId,clientKey, TypeRef);
			return result;
		} catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}

	}

	@Override
	public ResultInfo updateAfterSalesCostById(AfterSalesCost afterSalesCost) {
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
			String url=httpUrl + "aftersales/order/updateAfterSalesCostById.htm";
			ResultInfo<?> result = (ResultInfo<?>)HttpClientUtils.post(url, afterSalesCost,clientId,clientKey, TypeRef);
			return result;
		} catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}

	@Override
	public AfterSalesCostVo getAfterSalesCostBycostId(AfterSalesCost afterSalesCost) {
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
			String url=httpUrl + "aftersales/order/getAfterSalesCostBycostId.htm";
			ResultInfo<?> result = (ResultInfo<?>)HttpClientUtils.post(url, afterSalesCost,clientId,clientKey, TypeRef);
			if(result == null  || result.getCode() == -1){
				return null;
			}
			JSONObject json = JSONObject.fromObject(result.getData());
			AfterSalesCostVo costVo = JsonUtils.readValue(json.toString(), AfterSalesCostVo.class);
			return costVo;
		} catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}

	/**
	 * <b>Description:</b><br>新增或编辑售后对象
	 * @param afterSalesTrader
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2018年7月31日 下午6:12:18
	 */
	@Override
	public ResultInfo saveOrUpdateAfterTradder(AfterSalesTrader afterSalesTrader) {
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
			String url=httpUrl + "aftersales/order/saveorupdateaftertrader.htm";
			ResultInfo<?> result = (ResultInfo<?>)HttpClientUtils.post(url, afterSalesTrader,clientId,clientKey, TypeRef);
			return result;
		} catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}

	/**
	 * <b>Description:</b><br> 查询售后对象
	 * @param afterSalesTrader
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2018年8月1日 上午8:50:16
	 */
	@Override
	public AfterSalesTrader getAfterSalesTrader(AfterSalesTrader afterSalesTrader) {
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<AfterSalesTrader>> TypeRef = new TypeReference<ResultInfo<AfterSalesTrader>>() {};
			String url=httpUrl + "aftersales/order/getaftertrader.htm";
			@SuppressWarnings("unchecked")
			ResultInfo<AfterSalesTrader> result = (ResultInfo<AfterSalesTrader>)HttpClientUtils.post(url, afterSalesTrader,clientId,clientKey, TypeRef);
			return result.getData();
		} catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}

	/**
	 *
	 * <b>Description: 获取售后单对应的售后对象列表</b><br>
	 * @param
	 * @return
	 * @Note
	 * <b>Author:</b> Barry
	 * <br><b>Date:</b> 2018年08月09日 09:32
	 */
	@Override
	public List<AfterSalesTrader> getAfterSalesTraderList(Integer afterSalesId) {
		// TODO 获取售后单对应的售后对象列表
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<List<AfterSalesTrader>>> TypeRef = new TypeReference<ResultInfo<List<AfterSalesTrader>>>() {};
			String url=httpUrl + "aftersales/order/getaftertraderlist.htm";
			ResultInfo<?> result = (ResultInfo<?>)HttpClientUtils.post(url, afterSalesId,clientId,clientKey, TypeRef);
			@SuppressWarnings("unchecked")
			List<AfterSalesTrader> list = (List<AfterSalesTrader>) result.getData();
			return list;
		} catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}


	@Override
	public ResultInfo<?> updateAfterSalesById(AfterSalesVo afterSales) {
		try {
			logger.info("updateAfterSalesById afterSales:{}", JSON.toJSONString(afterSales));
			afterSalesMapper.updateByPrimaryKeySelective(afterSales);
		} catch (Exception e) {
			logger.error("updateAfterSalesById error", e);
			return ResultInfo.error("updateAfterSalesById error");
		}
		return ResultInfo.success();
	}

	/**
	 * <b>Description:</b><br> 销售执行退款前验证财务是否已确认退票
	 * @param afterSalesVo
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2018年9月4日 下午2:37:34
	 */
	@Override
	public ResultInfo<?> isAllReturnTicket(AfterSalesVo afterSalesVo) {
		String url=httpUrl + "aftersales/order/isallreturnticket.htm";
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, afterSalesVo, clientId, clientKey, TypeRef2);
			return result;
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return new ResultInfo<>();
		}
	}

	@Override
	public void getNoLockSaleorderGoodsVo(AfterSalesVo afterSalesVo) {
		String url=httpUrl + "aftersales/order/outofSaleorderGoodsLock.htm";
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, afterSalesVo, clientId, clientKey, TypeRef2);

		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
	}

	/** @description: getRefundBalanceAmountBySaleorderId.
	 * @notes: add by Tomcat.Hui 2019/9/5 10:44 .Desc: VDERP-1053.
	 * @author: Tomcat.Hui.
	 * @date: 2019/9/5 11:45.
	 * @param saleorderId
	 * @return: java.math.BigDecimal.
	 * @throws: .
	 */
	@Override
	public BigDecimal getRefundBalanceAmountBySaleorderId(Integer saleorderId) {
		return afterSalesMapper.getRefundBalanceAmountBySaleorderId(saleorderId);
	}
	@Override
	public Map<String, Object> searchAfterOrder(AfterSalesVo afterSalesVo, Page page) {
		Map<String, Object> map = new HashMap<>();
		map.put("page", page);
		map.put("afterSalesVo",afterSalesVo);
		List<AfterSalesVo> list = afterSalesMapper.searchAfterOrderListPage(map);
		WarehouseGoodsOperateLog wl = new WarehouseGoodsOperateLog();
		WarehouseGoodsStatus warehouseGoodsStatus = new WarehouseGoodsStatus();
		for (AfterSalesVo asgv : list) {
			wl.setRelatedId(asgv.getAfterSalesGoodsId());
			wl.setOperateType(3);//销售换货入库
			Integer exchangeReturnNum =  warehouseGoodsOperateLogMapper.getAftersalesGoodsSum(wl);
			if(warehouseGoodsOperateLogMapper.getAftersalesGoodsSum(wl)==null) {
				exchangeReturnNum = 0;
			}
			asgv.setExchangeReturnNum(exchangeReturnNum);//已退回数量
			wl.setOperateType(4);//销售换货出库
			Integer exchangeDeliverNum =  warehouseGoodsOperateLogMapper.getAftersalesGoodsSum(wl);
			if(warehouseGoodsOperateLogMapper.getAftersalesGoodsSum(wl)==null) {
				exchangeDeliverNum = 0;
			}
			if(asgv.getAfterGoodsNum()>exchangeDeliverNum) {
				asgv.seteFlag("1");
			}
			asgv.setExchangeDeliverNum(exchangeDeliverNum);//已重发数量
			warehouseGoodsStatus.setCompanyId(afterSalesVo.getCompanyId());
			warehouseGoodsStatus.setGoodsId(asgv.getGoodsId());
			Integer goodsStockNum = warehouseGoodsStatusMapper.getGoodsStock(warehouseGoodsStatus);//库存量
			Integer goodsOccupyNum = saleorderMapper.getGoodsOccupyNum(asgv.getGoodsId());//暂用数量
			Integer canUseGoodsStock = goodsStockNum- goodsOccupyNum;//可用库存
			asgv.setGoodsStockNum(goodsStockNum);
			asgv.setCanUseGoodsStock(canUseGoodsStock);
		}
		map.put("list",list);
		return map;
	}

	@Override
	public List<AfterSalesGoods> getAfterSalesGoodsNoOutNumList(Integer afterSalesId) {
		List<AfterSalesGoods> list  = afterSalesGoodsMapper.getAfterSalesGoodsNoOutNumList(afterSalesId);
		return list;
	}

	@Override
	public List<AfterSales> getAfterSaleListById(AfterSales afterSales) {

		return afterSalesMapper.getAfterSaleListById(afterSales);
	}

	@Override
	public void notifyStatusToMjx(Integer saleorderId,Integer afterSalesId){
		// add by Tomcat.Hui 2020/3/6 1:31 下午 .Desc: VDERP-2057 BD订单流程优化-ERP部分  售后产生的付款状态变更需要通知mjx. start
		try {
			Saleorder sv = saleorderMapper.getSaleOrderById(saleorderId);
			//BD订单
			if (sv.getOrderType().equals(1)||sv.virtualBdOrder()) {
				AfterSales afterSales = afterSalesMapper.getAfterSalesById(afterSalesId);

				//售后单已完结
				if (null != afterSales && null != afterSales.getAtferSalesStatus() && afterSales.getAtferSalesStatus().equals(2)) {
					OrderData orderData = new OrderData();
					orderData.setOrderNo(sv.getSaleorderNo());
					orderData.setTraderId(sv.getTraderId());
//					if(sv.getOrderType()==0){
//						sv.setCreateMobile(sv.getTraderContactMobile());
//					}
//					WebAccount web = webAccountMapper.getWenAccountInfoByMobile(sv.getCreateMobile());
//					if(web!=null){
//						orderData.setAccountId(web.getWebAccountId());
//						orderData.setSsoAccountId(web.getSsoAccountId());
//					}

					orderData.setGoodsList(new ArrayList<>());
					if (sv.getPaymentStatus() == 2) {
						orderData.setOrderStatus(3);//发货
					} else if (sv.getPaymentStatus() == 1) {
						orderData.setOrderStatus(7);
					} else if (sv.getPaymentStatus() == 0) {
						orderData.setOrderStatus(2);
					}
					//重构
					saleorderSyncService.syncSaleorderStatus2Mjx(sv.getSaleorderId()
							, PCOrderStatusEnum.get(orderData.getOrderStatus()),
							SaleorderSyncEnum.AFTERSALE_PAY_CHANGE);
				}
			}
		}catch(Exception e){
			logger.error("通知mjx订单付款状态变更出现异常：" ,e);
		}

		// add by Tomcat.Hui 2020/3/6 1:31 下午 .Desc: VDERP-2057 BD订单流程优化-ERP部分  售后产生的付款状态变更需要通知mjx. end
	}


	@Override
	public AfterSalesDetail getAfterSalesDetailByAfterSaleDetailId(Integer afterSalesDetailId){
		return afterSalesDetailMapper.selectByPrimaryKey(afterSalesDetailId);
	}

	//添加是否闪电发货字段
	@Override
	public void addIsLightning(Integer afterSalesId, Integer isLightning) {
		afterSalesDetailMapper.addIsLightning(afterSalesId,isLightning);
	}

	@Override
	public AfterSales getAfterSalesById(Integer afterSalesId) {
		return afterSalesMapper.getAfterSalesById(afterSalesId);
	}

	@Override
	public List<AfterSalesInvoiceVo> getFinanceAfterBackInvoice(AfterSales afterSales) {
		return afterSalesMapper.getAfterSalesBackInvoiceVoList(afterSales);
	}

	@Override
	public int getFinishAfterSaleNum(Integer buyorderGoodsId) {

		//已完结售后数量
		AfterSalesGoods searchGoods = new AfterSalesGoods();
		searchGoods.setOrderDetailId(buyorderGoodsId);
		searchGoods.setOperateType(StockOperateTypeConst.AFTERBUYORDER_BACK_FINSH);

		return afterSalesGoodsMapper.getAfterbuyorderNumByBuyorderGoodsId(searchGoods);
	}

	@Override
	public List<AfterSalesGoods> getAfterSalesGoodsByAfterSalesNo(String afterSalesNo) {
		return afterSalesGoodsMapper.getAfterSalesGoodsByAfterSalesNo(afterSalesNo);
	}

	@Override
	public Map<String, Object> getLongDistanceVos(LongDistanceFreeSearchDto longDistanceFreeSearchDto, Page page) {
	    if (longDistanceFreeSearchDto != null && StringUtil.isNotBlank(longDistanceFreeSearchDto.getRegionIds())){
            ArrayList<Integer> regionList = new ArrayList<>(16);
            org.apache.commons.collections.CollectionUtils
                    .collect(Arrays.stream(longDistanceFreeSearchDto.getRegionIds().split("@"))
                                    .distinct().collect(Collectors.toList()), new Transformer() {
                @Override
                public Object transform(Object o) {
                    return Integer.parseInt(o != null ? o.toString() : "0");
                }
            }, regionList);
            longDistanceFreeSearchDto.setRegionIdsList(regionList);
        }
		HashMap<String, Object> params = new HashMap<>(4);
		params.put("longDistanceFreeSearchDto", longDistanceFreeSearchDto);
		params.put("page", page);
		List<AfterSaleServiceStandardLongdistanceFeeVo> longDistanceFeeVos = afterSalesLongDistanceFeeMapper.getLongdisranceFeeVoListPage(params);

		HashMap<String, Object> result = new HashMap<>(4);
		result.put("longDistanceFeeVos", longDistanceFeeVos);
		result.put("page", page);
		return result;
	}

    @Override
    public ResultInfo batchSaveLongDistanceFee(FileInfo fileInfo, User user) throws Exception {
        ResultInfo<Object> resultInfo = new ResultInfo<>();
        if (fileInfo.getCode() != 0){
            resultInfo.setMessage("临时文件上传失败!");
            return resultInfo;
        }
        //1.校验上传的文件类型为Excel
        String suffix = fileInfo.getFilePath().substring(fileInfo.getFilePath().lastIndexOf(".") + 1);
        if (! "xlsx".equalsIgnoreCase(suffix)){
            resultInfo.setMessage("上传文件格式校验失败!");
            return resultInfo;
        }
		Workbook workbook = null;

		FileInputStream inputStream = new FileInputStream(new File(fileInfo.getFilePath()));
		try {
			workbook = WorkbookFactory.create(inputStream);
			Sheet sheet = workbook.getSheetAt(0);
			int startRowNum = sheet.getFirstRowNum() + 1;
			int realRows = sheet.getLastRowNum();

			//2.验证模板是否为标准模板
			Row firstRow = sheet.getRow(0);
			if (5 != firstRow.getLastCellNum()) {
				resultInfo.setMessage("模板不正确，请重新下载模板文件");
				return resultInfo;
			}

			if (0 == realRows) {
				resultInfo.setMessage("第1行，第1列不可为空!");
				return resultInfo;
			}

			// 创建一个用于保存Excel数据的对象
			ArrayList<AfterSaleServiceStandardLongdistanceFeePo> longdistanceFeePos = new ArrayList<>();
			//最小击级别区域用于校验
			ArrayList<Integer> zoneList = new ArrayList<>();

			for (int rowNum = startRowNum; rowNum <= realRows; rowNum++) {
				Row row = sheet.getRow(rowNum);
				int startCellNum = row.getFirstCellNum();
				int endCellNum = row.getLastCellNum() - 1;
				AfterSaleServiceStandardLongdistanceFeePo longdistanceFeePo = new AfterSaleServiceStandardLongdistanceFeePo();

				// 获取excel的值
				for (int cellNum = startCellNum; cellNum <= endCellNum; cellNum++) {
					Cell cell = row.getCell(cellNum);
					if (cellNum == 0) {
						//设置单元格类型防止类型转换错误
						row.getCell(cellNum).setCellType(CellType.STRING);
						String provinceStr = cell.getStringCellValue().trim();
						if (StringUtil.isBlank(provinceStr)) {
							resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列数据省份不能为空，请修改后重新导入");
							return resultInfo;
						}
						Region provinceInfo = regionMapper.getRegionByRegionNameAndType(provinceStr, 1);
						if (provinceInfo == null) {
							resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列数据有误，请修改后重新导入");
							return resultInfo;
						}
						longdistanceFeePo.setProvince(provinceInfo.getRegionId());
					}

					if (cellNum == 1) {
						//设置单元格类型防止类型转换错误
						row.getCell(cellNum).setCellType(CellType.STRING);
						String cityStr = cell.getStringCellValue().trim();
						if (StringUtil.isBlank(cityStr)) {
							resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列数据市级不能为空，请修改后重新导入");
							return resultInfo;
						}
						Region cityInfo = regionMapper.getRegionByRegionNameAndType(cityStr, 2);
						if (cityInfo == null) {
							resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列数据有误，请修改后重新导入");
							return resultInfo;
						}
						longdistanceFeePo.setCity(cityInfo.getRegionId());
					}

					if (cellNum == 2) {
						//设置单元格类型防止类型转换错误
						row.getCell(cellNum).setCellType(CellType.STRING);
						String zoneStr = cell.getStringCellValue().trim();
						if (StringUtil.isBlank(zoneStr)) {
							resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列数据区域不能为空，请修改后重新导入");
							return resultInfo;
						}
						Region zoneInfo = regionMapper.getRegionByReginFullName(zoneStr, 3, longdistanceFeePo.getCity());
						if (zoneInfo == null) {
							resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列数据有误，请修改后重新导入");
							return resultInfo;
						}
						if (zoneList.contains(zoneInfo.getRegionId())) {
							resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列数据区域重复，请修改后重新导入");
							return resultInfo;
						}
						longdistanceFeePo.setRegion(zoneInfo.getRegionId());
						zoneList.add(zoneInfo.getRegionId());
					}
					if (cellNum == 3) {
						try {
							//设置单元格类型防止类型转换错误
							row.getCell(cellNum).setCellType(CellType.STRING);
							BigDecimal longDistanceFee = new BigDecimal(cell.getStringCellValue().trim());
							longdistanceFeePo.setDistanceFee(longDistanceFee);
						} catch (Exception e) {
							logger.error("售后长途费转换 error", e);
							resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列数据格式有误，请修改后重新导入");
							return resultInfo;
						}
					}
					if (cellNum == 4) {
						row.getCell(cellNum).setCellType(CellType.STRING);
						switch (cell.getStringCellValue().toString().trim()) {
							case "是": {
								longdistanceFeePo.setLongdistanceFeeType(3);
								break;
							}
							case "否": {
								longdistanceFeePo.setLongdistanceFeeType(BigDecimal.ZERO.compareTo(longdistanceFeePo.getDistanceFee()) == 0 ? 1 : 2);
								continue;
							}
							default: {
								resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列数据有误，请修改后重新导入");
								return resultInfo;
							}
						}
					}
				}
				longdistanceFeePo.setCreator(user != null ? user.getUserId() : 1);
				longdistanceFeePo.setAddTime(System.currentTimeMillis());
				longdistanceFeePos.add(longdistanceFeePo);
			}

			if (CollectionUtils.isEmpty(longdistanceFeePos)) {
				resultInfo.setMessage("表格上传至少需要一条有效数据，请检查重新上传!");
				return resultInfo;
			}

			afterSalesLongDistanceFeeMapper.disableAllLongDistanceFee(user != null ? user.getUserId() : 1);

			longdistanceFeePos.forEach(longdistanceFeePo -> {
				afterSalesLongDistanceFeeMapper.insertLongDistanceFee(longdistanceFeePo);
			});
			resultInfo.setCode(0);
		} catch (Exception e) {
			logger.error("", e);
		} finally {
			try {
				inputStream.close();
			} catch (Exception e2) {
			}
			try {
				workbook.close();
			} catch (Exception e2) {
			}

		}
        return resultInfo;
    }

	@Override
	public AfterSalesDetailVo getAfterSalesDetail(AfterSalesVo afterSales) {
		AfterSalesDetailVo query = new AfterSalesDetailVo();
		query.setAfterSalesId(afterSales.getAfterSalesId());
		return afterSalesDetailMapper.getAfterSalesDetailVo(query);
	}

	@Override
	public void addApplySaleAfterOrder(SaleAfterOrderVo saleAfterOrderVo) throws Exception {

		Saleorder saleorder = saleorderMapper.getSaleorderByOrderNo(saleAfterOrderVo.getOrderNo());

		logger.info("addApplySaleAfterOrder 前台提交售后申请：{}",saleAfterOrderVo.toString());

		if(saleorder == null){
			logger.info("保存集采售后,订单不存在 单号:{}",saleAfterOrderVo.getOrderNo());
			throw new RuntimeException("保存集采售后,订单不存在");
		}

		long nowTime = System.currentTimeMillis();
		AfterSales afterSales = new AfterSales();

		afterSales.setOrderId(saleorder.getSaleorderId());
		afterSales.setOrderNo(saleorder.getSaleorderNo());
		//售后类型
		//更改默认安调类型
		if (AfterSalesProcessEnum.AFTERSALES_AT.getCode().equals(saleAfterOrderVo.getType())){
			afterSales.setType(AfterSalesProcessEnum.AFTERSALES_ATN.getCode());
		}else {
			afterSales.setType(saleAfterOrderVo.getType());
		}
		afterSales.setCompanyId(1);
		afterSales.setAddTime(nowTime);
		afterSales.setCreator(2);
		// 耗材售后  默认 0 `FIRST_VALID_STATUS` tinyint(1) unsigned DEFAULT '1' COMMENT '首次审核状态0待审核1审核通过2审核不通过'
		afterSales.setFirstValidStatus(CommonConstants.ZERO);
		// 售后主体类型
		afterSales.setSubjectType(SysOptionConstant.ID_535);
		// 订单来源
		afterSales.setSource(CommonConstants.TWO);

		//订单流新订单标识
		afterSales.setIsNew(ErpConst.ONE);

		Saleorder saleorderLock = new Saleorder();
		saleorderLock.setSaleorderId(saleorder.getSaleorderId());
		saleorderLock.setLockedStatus(1);
		saleorderLock.setLockedReason("售后锁定");

		saleorderMapper.updateByPrimaryKeySelective(saleorderLock);
		try{
			List<SaleorderGoods> saleorderGoodsLists = saleorderMapper.getSaleorderGoodsById(saleorder.getSaleorderId());
			if(org.apache.commons.collections.CollectionUtils.isNotEmpty(saleorderGoodsLists)){
				for (SaleorderGoods sg : saleorderGoodsLists) {
					SaleorderGoodsVo saleorderGoodsVo = new SaleorderGoodsVo();
					saleorderGoodsVo.setSaleorderGoodsId(sg.getSaleorderGoodsId());
					saleorderGoodsVo.setWarnLevel(null);
					saleorderGoodsMapper.updateWarnStatusSelective(saleorderGoodsVo);
				}
			}
		}catch(Exception e){
			logger.error("锁定销售单时，清除预警失败，销售单ID：{},错误原因：{}",saleorder.getSaleorderId(),e);
		}
		afterSalesMapper.insertSelective(afterSales);

		// 售后订单ID
		Integer afterSalesId = afterSales.getAfterSalesId();
		// 售后类型
		Integer type = afterSales.getType();
		// 售后订单号
		String saleAfterNo = orderNoDict.getOrderNum(afterSalesId, 6);
		// 设置售后订单号
		afterSales.setAfterSalesNo(saleAfterNo);
		// 更新售后单号
		afterSalesMapper.updateByPrimaryKeySelective(afterSales);

		// 售后详情
		AfterSalesDetail afterSalesDetail = new AfterSalesDetail();
		afterSalesDetail.setAfterSalesId(afterSalesId);
		afterSalesDetail.setReason(saleAfterOrderVo.getReason());
		afterSalesDetail.setComments(saleAfterOrderVo.getComments());
		afterSalesDetail.setTraderId(saleorder.getTraderId());
		afterSalesDetail.setTraderName(saleorder.getTraderName());
		afterSalesDetail.setTraderContactId(saleorder.getTraderContactId());
		afterSalesDetail.setTraderContactName(saleorder.getTraderContactName());
		afterSalesDetail.setTraderContactMobile(saleorder.getTraderContactMobile());

		afterSalesDetailMapper.insertSelective(afterSalesDetail);

		// 获取附件list
		List<MapVo<String>> imgList = saleAfterOrderVo.getImgList();
		// 非空
		if(org.apache.commons.collections.CollectionUtils.isNotEmpty(imgList))
		{
			// 附件类型
			List<Attachment> attList = new LinkedList<Attachment>();
			for(MapVo<String> img : imgList)
			{
				if(null == img)
				{
					continue;
				}
				// 附件信息
				Attachment att = new Attachment();

				// 图片名称
				att.setName(img.getValue());
				// 域名
				att.setDomain(img.getKey());
				// uri
				att.setUri(img.getValue());
				// 附件类型
				att.setAttachmentType(460);
				// 售后类型
				att.setAttachmentFunction(type);
				// 关联ID
				att.setRelatedId(afterSalesId);
				// 添加时间
				att.setAddTime(nowTime);

				// 添加
				attList.add(att);
			}
			// 附件集非空
			if(!CollectionUtils.isEmpty(attList)){
				for (Attachment attachment : attList) {
					attachmentMapper.insertSelective(attachment);
				}
			}
		}
	}

	@Override
	public ResultInfo directSalesAfterSales(Integer afterSalesId,String domain) {
		//VDERP-6890直发的销售售后与关联的采购单售后强耦合---销售售后退换货单流程变更start
		//创建的采购售后单
		List<AfterSalesVo> buyorderAfterList = new ArrayList<>();
		//销售sku关联的采购单--用于校验销售售后单的sku是否属于同一个采购单
		List<String> buyorderNoList = new ArrayList<>();
		ResultInfo resultInfo = new ResultInfo(0,"操作成功");
		StringBuffer msg = new StringBuffer("");
		boolean ispass = true;
		AfterSales afterSales = afterSalesMapper.selectByPrimaryKey(afterSalesId);
		if(afterSales.getAtferSalesStatus() != 0){
			return new ResultInfo(0,"非售后单待确认审核");
		}
		if(!SysOptionConstant.ID_539.equals(afterSales.getType()) && !SysOptionConstant.ID_540.equals(afterSales.getType())){
			return new ResultInfo(0,"非售后退换货审核");
		}
		AfterSalesGoods afterGoods = new AfterSalesGoods();
		afterGoods.setAfterSalesId(afterSales.getAfterSalesId());
		List<AfterSalesGoodsVo> afterSalesGoodsVosList = afterSalesGoodsMapper.getAftersalesGoodsListIsDirect(afterGoods);
		for(AfterSalesGoodsVo item : afterSalesGoodsVosList){
			if(item.getDeliveryDirect() == 1 && item.getOrderDetailId() != 0) {
				//只有直发的售后sku会执行
				List<Buyorder> buyorderList = buyorderMapper.selectSalesBuyorderList(item.getAfterSalesGoodsId());
				buyorderList = buyorderList.parallelStream().filter(Objects::nonNull).collect(Collectors.toList());
				if(buyorderList.size() == 0){
					//销售单未采购
					return new ResultInfo(2,"销售单未关联采购单");
				}
				logger.info("当前售后明细id{}关联的采购单数量{}",item.getAfterSalesGoodsId(),buyorderList.size());
				if (buyorderList.size() == 1) {
					Buyorder buyorder = buyorderList.get(0);
					if(buyorder.getOrderType() == 1){
						//sku只关联了1个VB采购单
						resultInfo = sendOrderAssistant(item.getSku(),afterSales.getAfterSalesNo(),item.getAfterSalesId());
						if (resultInfo.getCode() == -1) {
							ispass = false;
							msg.append(resultInfo.getMessage());
						}
					}else {
						//SKU只关联了1个VP采购单
						BuyorderGoods buyorderGood = buyorderGoodsMapper.selectBuySkuInfo(buyorder.getBuyorderId(), item.getSku());
						Integer buyorderSum = (buyorderGood.getNum() - buyorderGood.getAfterReturnNum());
						User creator = userMapper.selectByPrimaryKey(buyorder.getCreator());
						logger.info("当前售后明细id{}售后数量{}采购数量{}", item.getAfterSalesGoodsId(), item.getNum(), buyorderSum);
						if (item.getNum() <= buyorderSum) {
							//SKU售后的数量 <= 采购单中该SKU的采购数量（去除已退货）
							resultInfo = autoCreateAfterSales(buyorderList, afterSales, item, domain, buyorderAfterList, buyorderNoList);
							if (resultInfo.getCode() == -1) {
								ispass = false;
								msg.append(resultInfo.getMessage());
							}
						} else {
							//SKU售后的数量 > 采购单中该SKU的采购数量（去除已退货）
							ispass = false;
							msg.append("售后数量大于了所有关联的采购单的实际采购数量，审核通过失败，请和关联采购单").append(buyorder.getBuyorderNo())
									.append("的创建人").append(creator.getUsername()).append("确认后再重新审核");
						}
					}
				} else {
					//SKU只关联了多个采购单
					boolean isAllvp = true;
					for(Buyorder buyorder : buyorderList){
						if(buyorder.getOrderType() == 1){
							//判断是否全vp订单
							isAllvp = false;
						}
					}
					logger.info("当前售后明细id{}是否含有VB采购单{}",item.getAfterSalesGoodsId(),isAllvp);
					if(!isAllvp){
						//非全部是vp订单的情况->审核通过成功--站内信提醒
						resultInfo = sendOrderAssistant(item.getSku(),afterSales.getAfterSalesNo(),item.getAfterSalesId());
						if(resultInfo.getCode() == -1){
							ispass = false;
							msg.append(resultInfo.getMessage());
						}
					}else {
						//全部是vp订单的情况
						List<BuyorderGoods> allBuyorderGoodList = new ArrayList<>();
						int buySum = 0;
						for(Buyorder buyorder : buyorderList){
							BuyorderGoods buyorderGood = buyorderGoodsMapper.selectBuySkuInfo(buyorder.getBuyorderId(),item.getSku());
							allBuyorderGoodList.add(buyorderGood);
							buySum += (buyorderGood.getNum() - buyorderGood.getAfterReturnNum());
						}
						logger.info("当前售后明细id{}售后数量{}采购数量{}",item.getAfterSalesGoodsId(),item.getNum(),buySum);
						if(buySum == item.getNum()){
							//全量退换货
							resultInfo =  autoCreateAfterSales(buyorderList,afterSales,item,domain,buyorderAfterList,buyorderNoList);
							if(resultInfo.getCode() == -1){
								ispass = false;
								msg.append(resultInfo.getMessage());
							}
						}else if(buySum > item.getNum()){
							//非全量退换货且售后的数量<所有VP采购单中该SKU的采购数量总和（去除已退货）
							resultInfo = sendOrderAssistant(item.getSku(),afterSales.getAfterSalesNo(),item.getAfterSalesId());
							if(resultInfo.getCode() == -1){
								ispass = false;
								msg.append(resultInfo.getMessage());
							}
						}else if(buySum < item.getNum()){
							//非全量退换货且售后的数量＞所有VP采购单中该SKU的采购数量总和（去除已退货）
							//审核通过失败
							ispass = false;
							msg.append(rejectMsg(buyorderList,msg,3));
						}
					}
				}
			}
		}
		if(buyorderAfterList.size() != 0 && ispass){
			//所有sku都满足创建采购售后的情况
			for(AfterSalesVo buyorderAfter : buyorderAfterList){
				User creator = userMapper.selectByPrimaryKey(buyorderAfter.getCreator());
				resultInfo = saveAddAfterSales(buyorderAfter, creator);
				if (resultInfo.getCode() == 0) {
					logger.info("销售直发售后关联创建采购售后成功");
					HashMap<String, String> map = new HashMap();
					AfterSales assAfter = afterSalesMapper.selectByPrimaryKey(Integer.parseInt(resultInfo.getData().toString()));
					map.put("aftersalesNo", assAfter.getAfterSalesNo());
					List<Integer> users = new ArrayList<>();
					users.add(creator.getUserId());
					try {
						MessageUtil.sendMessage(189, users, map,
								".//aftersales/order/viewAfterSalesDetail.do?afterSalesId="+resultInfo.getData()+"&traderType=2");
					}catch (Exception e){
						logger.info("发送站内信异常，历史问题");
					}
				} else {
					logger.info("销售直发售后关联创建采购售后失败");
				}
			}
		}
		if(ispass){
			return new ResultInfo(0,"操作成功");
		}else {
			return new ResultInfo(-1,msg.toString());
		}
		//VDERP-6890直发的销售售后与关联的采购单售后强耦合---销售售后退换货单流程变更end
	}
	/**
	 * <b>Description:</b><br>
	 * 站内信提醒订单助理
	 *
	 * @param sku, aftersalesNo, afterSalesId
	 * @return void
	 * @Note <b>Author:</b> Thor <br>
	 *       <b>Date:</b> 2021/6/21 9:51
	 */
	public ResultInfo sendOrderAssistant(String sku,String aftersalesNo,Integer afterSalesId){
		List<Integer> orderAssistantUser = coreSpuMapper.selectOrderAssistantByNo(sku);
		if(orderAssistantUser == null){
			return new ResultInfo(-1,"sku"+sku+"没有对应的订单助理");
		}
		HashMap<String,String> map = new HashMap();
		map.put("sku",sku);
		map.put("aftersalesNo",aftersalesNo);
		try {
			MessageUtil.sendMessage(190,orderAssistantUser,map,
					"./order/saleorder/viewAfterSalesDetail.do?afterSalesId="+afterSalesId);
		}catch (Exception e){
			logger.info("发送站内信异常，历史问题");
		}
		return new ResultInfo(0,"发送站内信成功");
	}

	/**
	 * <b>Description:</b><br>
	 * 判断是否自动创建采购售后单
	 *
	 * @param buyorders, afterSales, item, domain
	 * @return com.vedeng.common.model.ResultInfo
	 * @Note <b>Author:</b> Thor <br>
	 *       <b>Date:</b> 2021/6/21 13:26
	 */
	@Transactional(rollbackFor = Exception.class)
	public ResultInfo autoCreateAfterSales(List<Buyorder> buyorders,AfterSales afterSales,AfterSalesGoods item,
										   String domain,List<AfterSalesVo> buyorderAfterList,List<String> buyorderNoList){
		long start=System.currentTimeMillis();
		logger.info("直发销售 自动生成 采购售后 {} {} {} {} {} {} ",JSON.toJSONString(buyorders),
				JSON.toJSONString(afterSales),item,domain,JSON.toJSONString(buyorderAfterList),JSON.toJSONString(buyorderNoList));
		StringBuffer msg = new StringBuffer("");
		boolean ispass = true;
		for(Buyorder buyorder : buyorders){
			User creator = userMapper.selectByPrimaryKey(buyorder.getCreator());
			BuyorderGoods buyorderGood = buyorderGoodsMapper.selectBuySkuInfo(buyorder.getBuyorderId(), item.getSku());
			if (buyorder.getLockedStatus() == 1) {
				//采购单锁定状态，已锁定 ->审批不通过，返回信息提示
				String orderstring = rejectMsg(buyorders,msg,1);
				//审批不通过，直接返回，不需要进行其他逻辑
				logger.info("直发销售 自动生成 采购售后 {} {} ",orderstring,JSON.toJSONString(buyorder));
				return new ResultInfo(-1,orderstring);
			} else {
				//采购单锁定状态，未锁定
				boolean isallopen = isAllOpen(buyorders);
				if(isallopen){
					//所有的采购单中不包括已关闭的采购单
					if (buyorder.getStatus() == 1 || buyorder.getStatus() == 2) {
						//进行中或已完结的状态-->自动创建采购售后退换货单+站内信提醒
						if (buyorderNoList.contains(buyorder.getBuyorderNo())) {
							//销售售后单sku来自同一个采购单
							int now = buyorderNoList.indexOf(buyorder.getBuyorderNo());
							AfterSalesVo buyorderAfter = buyorderAfterList.get(now);
							//TraderAddress traderAddress = traderAddressMapper.getAddressInfoById(buyorder.getTraderAddressId(), 1);
							buyorderAfter.setAfterSalesNum((String[]) ArrayUtils.add(buyorderAfter.getAfterSalesNum(),
									buyorderGood.getBuyorderGoodsId() + "|" + item.getNum() + "|1|" + buyorderGood.getGoodsId()));
							String[] skus = buyorderAfter.getComments().split("SKU");
							buyorderAfter.setComments(skus[0]+"SKU"+item.getSku()+","+skus[1]);//拼接多个sku
							if (SysOptionConstant.ID_539.equals(afterSales.getType())) {
								//销售退货->采购退货
								buyorderAfter.setRefundAmount(buyorderAfter.getRefundAmount().add(buyorderGood.getPrice().multiply(new BigDecimal(item.getNum()))));
							}
						} else {
							AfterSalesVo buyorderAfter = new AfterSalesVo();
							if (SysOptionConstant.ID_539.equals(afterSales.getType())) {
								//销售退货->采购退货
								buyorderAfter.setType(SysOptionConstant.ID_546);
								buyorderAfter.setRefund(1);//默认退还至公司账户，换货无需传值
								buyorderAfter.setRefundAmount(buyorderGood.getPrice().multiply(new BigDecimal(item.getNum())));
							} else if (SysOptionConstant.ID_540.equals(afterSales.getType())) {
								//销售换货->采购换货
								buyorderAfter.setType(SysOptionConstant.ID_547);
							}
							buyorderAfter.setReason(559);//售后原因--其他
							buyorderAfter.setOrderId(buyorder.getBuyorderId());
							buyorderAfter.setOrderNo(buyorder.getBuyorderNo());
							buyorderAfter.setCompanyId(1);
							buyorderAfter.setTraderId(buyorder.getTraderId());
							buyorderAfter.setComments("因销售单" + afterSales.getOrderNo() + "中的SKU" + item.getSku() + "发生直发售后退货或换货");
							List<String> afterSalesNums = new ArrayList<>();
							afterSalesNums.add(buyorderGood.getBuyorderGoodsId() + "|" + item.getNum() + "|1|" + buyorderGood.getGoodsId());//默认直发
							buyorderAfter.setAfterSalesNum(afterSalesNums.toArray(new String[0]));
							buyorderAfter.setTraderContactId(buyorder.getTraderContactId());
							TraderAddress traderAddress = traderAddressMapper.getAddressInfoById(buyorder.getTraderAddressId(), 1);
							buyorderAfter.setTakeMsg(buyorder.getTraderAddressId() + "|" + traderAddress.getAreaId() + "|" + buyorder.getTraderArea() + "|" + buyorder.getTraderAddress());
							buyorderAfter.setTraderType(2);
							buyorderAfter.setSubjectType(536);
							buyorderAfter.setStatus(0);
							buyorderAfter.setCreateType(1);//自动创建的采购售后单
							buyorderAfter.setValidStatus(0);
							buyorderAfter.setAtferSalesStatus(0);
							buyorderAfter.setServiceUserId(buyorder.getUserId());
							buyorderAfter.setDomain(domain);
							buyorderAfter.setPayee(creator.getCompanyName());
							buyorderAfter.setCreator(buyorder.getCreator());
							buyorderAfterList.add(buyorderAfter);
							buyorderNoList.add(buyorder.getBuyorderNo());
							logger.info("自动创建采购单售后信息{}",JSONObject.fromObject(buyorderAfter));
						}
					} else if (buyorder.getStatus() == 0) {
						//待确认的采购单-->站内消息提醒对应所有采购单的创建人
						HashMap<String, String> map = new HashMap();
						map.put("saleorderNo", afterSales.getOrderNo());
						map.put("buyorderNo", buyorder.getBuyorderNo());
						List<Integer> users = new ArrayList<>();
						users.add(creator.getUserId());
						try {
							MessageUtil.sendMessage(188, users, map,
									"./order/buyorder/viewBuyorder.do?buyorderId=" + buyorder.getBuyorderId());
						}catch (Exception e){
							logger.info("发送站内信异常，历史问题");
						}
					}
				}else{
					//存在关闭的采购单-->审核通过失败，并弹窗提醒
					String orderstring = rejectMsg(buyorders,msg,2);
					logger.info("直发销售 自动生成 采购售后 {} {} ",orderstring,JSON.toJSONString(buyorder));
					return new ResultInfo(-1,orderstring);
				}
			}
		}
		long end=System.currentTimeMillis();
		if((end-start)>10000){
			logger.error("事务过长");
		}
		if(ispass){
			return new ResultInfo(0,"关联设置采购售后信息成功");
		}else {
			return new ResultInfo(-1,msg.toString());
		}
	}
	/**
	 * <b>Description:</b><br>
	 * 方法注释
	 *
	 * @param buyorders, msg, msgType 1锁定订单返回消息2关闭订单返回消息3售后数量大于采购数量返回消息
	 * @return java.lang.String
	 * @Note <b>Author:</b> Thor <br>
	 *       <b>Date:</b> 2021/6/21 14:48
	 */
	public String rejectMsg(List<Buyorder> buyorders,StringBuffer msg,Integer msgType){
		StringBuffer midstring = new StringBuffer("");
		if(msgType == 1){
			msg.append("有关联的采购单处于锁定状态，请先联系");
		}else if(msgType == 2){
			msg.append("有关联的采购单");
		}else if(msgType == 3){
			msg.append("售后数量大于了所有关联的采购单的实际采购数量，审核通过失败，请和关联采购单");
		}
		//返回所有符合条件的采购订单
		List<String> lockedUserName = new ArrayList<>();
		for(Buyorder buyorder : buyorders){
			if(buyorder.getLockedStatus() == 1){
				//采购订单锁定
				midstring.append(buyorder.getBuyorderNo()).append(",");
				User assUser = userMapper.selectByPrimaryKey(buyorder.getCreator());
				if(!lockedUserName.contains(assUser.getUsername())){
					msg.append(assUser.getUsername()).append(",");
					lockedUserName.add(assUser.getUsername());
				}
			}else if(buyorder.getStatus() == 3){
				//采购订单关闭
				msg.append(buyorder.getBuyorderNo()).append(",");
				User assUser = userMapper.selectByPrimaryKey(buyorder.getCreator());
				if(!lockedUserName.contains(assUser.getUsername())){
					midstring.append(assUser.getUsername());
					lockedUserName.add(assUser.getUsername());
				}
			}else if(msgType == 3){
				msg.append(buyorder.getBuyorderNo()).append(",");
				User assUser = userMapper.selectByPrimaryKey(buyorder.getCreator());
				if(!lockedUserName.contains(assUser.getUsername())){
					midstring.append(assUser.getUsername()).append(",");
					lockedUserName.add(assUser.getUsername());
				}
			}
		}
		if(msgType == 1){
			msg.append("解除采购单");
			msg.append(midstring);
			msg.append("的锁定");
		}else if(msgType == 2){
			msg.append("已关闭，无法审核通过，请联系");
			msg.append(midstring).append("去处理");
		}else if(msgType == 3){
			msg.append("的创建人");
			msg.append(midstring).append("确认后再重新审核");
		}
		return msg.toString();
	}
	/**
	 * <b>Description:</b><br>
	 * 判断所有采购单是否全部为待确认或进行中或已完结
	 *
	 * @param buyorders
	 * @return boolean
	 * @Note <b>Author:</b> Thor <br>
	 *       <b>Date:</b> 2021/6/21 15:22
	 */
	public boolean isAllOpen(List<Buyorder> buyorders){
		for(Buyorder buyorder : buyorders){
			if(buyorder.getStatus() == 3){
				return false;
			}
		}
		return true;
	}

	@Override
	public void transAfterSalesVoForBlueInValid(AfterSalesVo afterSalesVo) {
		if (Objects.isNull(afterSalesVo) || org.apache.commons.collections.CollectionUtils.isEmpty(afterSalesVo.getAfterSalesInvoiceVoList())) {
			return;
		}

		try {

			List<AfterSalesInvoiceVo> afterSalesInvoiceVoList = afterSalesVo.getAfterSalesInvoiceVoList();
			// 处理自动生成作废票展示问题
			Set<Integer> invoiceSet = afterSalesInvoiceVoList.stream()
					.map(AfterSalesInvoiceVo::getInvoiceId).collect(Collectors.toSet());

			// 判断发票是否有已废
			Set<Integer> hxInValidSet = invoiceMapper.getInValidInvoiceListByIds(new ArrayList<>(invoiceSet))
					.stream().map(Invoice::getInvoiceId).collect(Collectors.toSet());

			if (hxInValidSet.size() > 0) {
				boolean isAddInValid = false;
				for (AfterSalesInvoiceVo item : afterSalesInvoiceVoList) {
					if (hxInValidSet.contains(item.getInvoiceId())) {
						isAddInValid = true;
						break;
					}
				}

				if (isAddInValid) {
					List<AfterSalesInvoiceVo> invoiceForInValid = invoiceMapper.getAfterSalesInvoiceForInValid(afterSalesVo.getAfterSalesId());
					Map<String, AfterSalesInvoiceVo> invoiceNumMap = afterSalesInvoiceVoList.stream().
							collect(Collectors.toMap(AfterSalesInvoiceVo::getInvoiceNo, i -> i));

					Iterator<AfterSalesInvoiceVo> iterator = invoiceForInValid.iterator();
					while (iterator.hasNext()) {
						AfterSalesInvoiceVo next = iterator.next();
						if (invoiceNumMap.containsKey(next.getInvoiceNo())) {
							next.setValidUsername("admin");
							next.setCreatorName("admin");
						}else {
							iterator.remove();
						}
					}

					if (invoiceForInValid.size() > 0) {
						afterSalesInvoiceVoList.addAll(invoiceForInValid);
					}

				}


			}
		}catch (Exception e) {
			logger.error("作废展示失败 , msg : "+afterSalesVo.getAfterSalesNo(), e );
		}

	}

    @Override
    public List<SaleorderGoods> getRelatedSaleOrderGoods(AfterSalesVo afterSalesVo) {
		List<SaleorderGoods> saleorderGoodsList = afterSalesGoodsMapper.getRelatedSaleOrderGoods(afterSalesVo.getAfterSalesId());
		return saleorderGoodsList;
    }

	/**
	 * <b>Description:</b><br>
	 * 根据订单ID获取结算金额
	 *
	 * @param buyOrderId
	 * @return java.math.BigDecimal
	 */
	private BigDecimal getAllAfterSalseSettlementAmount(Integer buyOrderId){
		List<AfterSalesApiDto> allAfterSalesByBuyOrderId = buyorderAfterSalesApiService.getAllAfterSalesByBuyOrderId(buyOrderId);
		BigDecimal amount = BigDecimal.ZERO;
		for (AfterSalesApiDto afterSalesApiDto : allAfterSalesByBuyOrderId) { // todo 调用Jez接口
			BigDecimal sigAmount = getSettlementAmount(afterSalesApiDto.getAfterSalesId(), BusinessSourceTypeEnum.buyOrderAfterSale, SettlementTypeEnum.REBATE_REFUND);
			amount = amount.add(sigAmount);
		}
		logger.info("根据订单ID获取结算金额,orderId : {},所有售后结算总金额 : {}",buyOrderId,amount);
		return amount;
	}
	/**
	 * <b>Description:</b><br>
	 * 根据订单ID获取结算金额
	 *
	 * @param orderID
	 * @param businessSourceTypeEnum
	 * @param settlementTypeEnum
	 * @return java.math.BigDecimal
	 */
	public BigDecimal getSettlementAmount(Integer orderID,BusinessSourceTypeEnum businessSourceTypeEnum,SettlementTypeEnum settlementTypeEnum){
		SettlementBillQueryCmd cmd = new SettlementBillQueryCmd(orderID,businessSourceTypeEnum);
		SettlementBillApiDto settlementBillApiDto = settlementBillApiService.getSettlementByBusiness( cmd);
		if(Objects.isNull(settlementBillApiDto)){
			logger.info("未查询到结算单");
			return BigDecimal.ZERO;
		}

		List<SettlementBillItemApiDto> settlementBillItemDtoList = settlementBillApiDto.getSettlementBillItemDtoList();
		if (Objects.nonNull(settlementBillItemDtoList)){
			BigDecimal sum = settlementBillItemDtoList.stream().filter(e -> settlementTypeEnum.getSupplierAssetEnum().getCode().equals(e.getSettlementType())).map(SettlementBillItemApiDto::getAmount).reduce(BigDecimal::add).get();
			logger.info("结算单求和：{}",sum);
			return sum.setScale(2,BigDecimal.ROUND_HALF_UP);
		}
		return BigDecimal.ZERO;
	}

	/**
	 * <b>Description:</b><br>
	 * 获取本次退款金额
	 *
	 * @param afterSales
	 * @return java.math.BigDecimal
	 */
	public BigDecimal getRefundAmountFL(AfterSalesVo afterSales){
		Integer buyOrderID = afterSales.getOrderId();
		Integer afterSalesId = afterSales.getAfterSalesId();
		// 获取采购单返利支付结算单
		SettlementBillQueryCmd cmd = new SettlementBillQueryCmd(buyOrderID, BusinessSourceTypeEnum.buyOrder);
		SettlementBillApiDto settlementBillApiDto = settlementBillApiService.getSettlementByBusiness( cmd);
		if (Objects.isNull(settlementBillApiDto)){
			return BigDecimal.ZERO;
		}
		List<SettlementBillItemApiDto> buyOrderSettlementBillItem = settlementBillApiDto.getSettlementBillItemDtoList();
		// 采购单返利支付结算单明细
		Map<Integer,SettlementBillItemApiDto> buyOrderSettlementItemMap = buyOrderSettlementBillItem.stream().collect(Collectors.toMap(SettlementBillItemApiDto::getBusinessItemId, Function.identity(),(key1, key2)->key1));

		List<AfterSalesGoodsDto> afterSalesGoodsList = buyorderAfterSalesApiService.getAfterSalesGoodsByAfterSalesId(afterSalesId);
		List<SettlementBillItemApiDto> settlementBillItemDtoList = new ArrayList<>();
		for (AfterSalesGoodsDto afterSalesGoods : afterSalesGoodsList) {
			Integer orderDetailId = afterSalesGoods.getOrderDetailId();
			// 匹配返利支付结算单明细
			SettlementBillItemApiDto buyOrderSettlementItem = buyOrderSettlementItemMap.get(orderDetailId);
			if(Objects.isNull(buyOrderSettlementItem)){
				continue;
			}
			// 返利支付时结算单价
			BigDecimal price = buyOrderSettlementItem.getPrice();
			// 售后数量
			BigDecimal number = BigDecimal.valueOf(afterSalesGoods.getNum());
			SettlementBillItemApiDto settlementBillItemDto = new SettlementBillItemApiDto();
			settlementBillItemDto.setPrice(price);
			settlementBillItemDto.setNumber(number);
			settlementBillItemDtoList.add(settlementBillItemDto);
		}
		// 计算售后返利结算总额
		BigDecimal afterSalesSettleAmount = settlementBillItemDtoList.stream().map(e-> e.getPrice().multiply(e.getNumber())).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
		return afterSalesSettleAmount;
	}

	@Override
	public AfterSalesVo getbuyorderAfterSalesVoDetail(AfterSalesVo afterSales) {
		try {
			AfterSalesVo afterSalesVo = afterSalesMapper.viewAfterSalesDetailBuyorder(afterSales);//采购的售后详情
			afterSalesVo.setTraderType(afterSales.getTraderType());
			if(afterSalesVo.getType() == 546){
				calBuyOrderAfterSalesTh(afterSalesVo);
			}

			TraderBussinessData traderBussinessData = traderDataService.getTraderBussinessData(afterSalesVo.getTraderId(),afterSales.getTraderType());
			if(traderBussinessData != null){
				afterSalesVo.setOrderCount(traderBussinessData.getOrderTimes());
				afterSalesVo.setOrderTotalAmount(traderBussinessData.getOrderTotalAmount());
				afterSalesVo.setLastOrderTime(traderBussinessData.getLastOrderTime());
			}

			//附件列表
			afterSalesVo.setAttachmentList(getAttachmentListByParam(afterSalesVo));

			//售后产品
			afterSalesVo.setCompanyId(afterSales.getCompanyId());
			afterSalesVo.setAfterSalesGoodsList(getBuyorderAfterSalesGoodsVoListByParamNew(afterSalesVo));


			//退换货手续费
			afterSalesVo.setThhGoodsList(getTHHGoodsList(afterSalesVo));
			if(afterSalesVo.getStatus() != 2){
				//条码出库记录
				afterSalesVo.setWarehouseGoodsOperateLogList(getWarehouseOutListByParam(afterSalesVo));
			}

			if(afterSalesVo.getType() == 546 ){//退货
				//退票信息
				afterSalesVo.setAfterSalesInvoiceVoList(getAfterSalesInvoiceVoListNew(afterSalesVo));
			}
			if(afterSalesVo.getStatus() == 2){//审核完成
				//退换货入库记录
				if(afterSalesVo.getType() == 547){
					afterSalesVo.setAfterReturnInstockList(getAfterReturnGoodsStorageList(afterSalesVo));
				}

				if(afterSalesVo.getType() == 546 || afterSalesVo.getType() == 547){//采购退货出库记录
					afterSalesVo.setAfterReturnOutstockList(getAfterReturnOutStockList(afterSalesVo));
					afterSalesVo.setDirectReturnOutstockList(getDirectReturnOutList(afterSalesVo));
				}

				//开票记录
				afterSalesVo.setAfterOpenInvoiceList(getInvoice(afterSalesVo));

                //交易记录
                afterSalesVo.setAfterCapitalBillList(getAfterCapitalBillList(afterSalesVo));

                //合同回传
                afterSalesVo.setAfterContractAttachmentList(getAfterAttachmentList(afterSalesVo.getAfterSalesId(),SysOptionConstant.ID_583));//583售后合同回传

                //申请付款列表
				afterSalesVo.setAfterPayApplyList(getAfterAtPaymentApply(afterSalesVo.getAfterSalesId()));

				//售后对象
				afterSalesVo.setAfterSalesTraderList(getBuyorderAfterSalesTraderList(afterSalesVo.getAfterSalesId()));

            }

			//售后记录
			afterSalesVo.setAfterSalesRecordVoList(getAfterSalesRecordVoListByParam(afterSalesVo));
			//售后的关闭状态
			if(CollectionUtils.isEmpty(afterSalesVo.getAfterReturnInstockList()) && CollectionUtils.isEmpty(afterSalesVo.getAfterReturnOutstockList()) && CollectionUtils.isEmpty(afterSalesVo.getDirectReturnOutstockList())&& CollectionUtils.isEmpty(afterSalesVo.getAfterOpenInvoiceList())
					&& CollectionUtils.isEmpty(afterSalesVo.getAfterCapitalBillList()) && CollectionUtils.isEmpty(afterSalesVo.getAfterPayApplyList()) && isReturnInvoice(afterSalesVo)){
				afterSalesVo.setCloseStatus(1);
			}else{
				afterSalesVo.setCloseStatus(2);
			}

			//取出商品表里现在的退货金额
			if (null != afterSales.getTraderType() && afterSales.getTraderType() == 1) {
				if( afterSalesVo.getAfterSalesGoodsList().size()>0) {
					afterSalesVo.getAfterSalesGoodsList().stream().forEach(afterSalesGoodsVo -> {
						if (afterSalesGoodsVo.getSkuRefundAmount().compareTo(afterSalesGoodsVo.getSkuOldRefundAmount()) == 0) {
							afterSalesGoodsVo.setFag(1);
						} else {
							afterSalesGoodsVo.setFag(2);
						}

					});
				}
			}


			afterSalesVo.setCreatorName(getUserNameByUserId(afterSalesVo.getCreator()));
			//查询关闭人名字
			afterSalesVo.setAfterSalesStatusUserName(getUserNameByUserId(afterSalesVo.getAfterSalesStatusUser()));
			afterSalesVo.setUserName(getUserNameByUserId(afterSalesVo.getUserId()));
			afterSalesVo.setBuyorderCreatorName(getUserNameByUserId(afterSalesVo.getBuyorderCreator()));
			afterSalesVo.setOrgName(getOrgNameByOrgId(afterSalesVo.getOrgId()));
			if(afterSalesVo.getAfterSalesGoodsList() != null){
				for (AfterSalesGoodsVo asgv : afterSalesVo.getAfterSalesGoodsList()) {
					asgv.setUserList(rCategoryJUserMapper.getUserByCategory(asgv.getCategoryId(), afterSales.getCompanyId()));
				}
			}
			if(afterSalesVo.getAfterSalesRecordVoList() != null){
				for (AfterSalesRecordVo asrv : afterSalesVo.getAfterSalesRecordVoList()) {
					asrv.setOptName(getUserNameByUserId(asrv.getCreator()));
				}
			}
			if(afterSalesVo.getAfterSalesInvoiceVoList() != null){//退票
				for (AfterSalesInvoiceVo asi : afterSalesVo.getAfterSalesInvoiceVoList()) {
					if (null!=asi){
						asi.setCreatorName(getUserNameByUserId(asi.getCreator()));;
						asi.setValidUsername(getUserNameByUserId(asi.getValidUserId()));
					}

					/*asi.setCreatorName(getUserNameByUserId(asi.getCreator()));;
					asi.setValidUsername(getUserNameByUserId(asi.getValidUserId()));*/
				}
			}
			if(afterSalesVo.getAfterContractAttachmentList() != null){
				for (Attachment aca : afterSalesVo.getAfterContractAttachmentList()) {
					aca.setUsername(getUserNameByUserId(aca.getCreator()));
				}
			}
			if(afterSalesVo.getAfterInvoiceAttachmentList() != null){
				for (Attachment aca : afterSalesVo.getAfterInvoiceAttachmentList()) {
					aca.setUsername(getUserNameByUserId(aca.getCreator()));
				}
			}
			if(afterSalesVo.getAfterReturnInstockList() != null){
				for (WarehouseGoodsOperateLog aca : afterSalesVo.getAfterReturnInstockList()) {
					aca.setOperator(getUserNameByUserId(aca.getCreator()));
				}
			}
			if(afterSalesVo.getAfterReturnOutstockList() != null){
				for (WarehouseGoodsOperateLog aca : afterSalesVo.getAfterReturnOutstockList()) {
					aca.setOperator(getUserNameByUserId(aca.getCreator()));
				}
			}
			if(afterSalesVo.getAfterPayApplyList() != null){
				for (PayApply app : afterSalesVo.getAfterPayApplyList()) {
					app.setCreatorName(getUserNameByUserId(app.getCreator()));
				}
			}
			if(afterSalesVo.getAfterOpenInvoiceList() != null){//退票
				for (AfterSalesInvoiceVo asi : afterSalesVo.getAfterOpenInvoiceList()) {
					asi.setCreatorName(getUserNameByUserId(asi.getCreator()));
					asi.setValidUsername(getUserNameByUserId(asi.getValidUserId()));
				}
			}
			if(afterSalesVo.getAfterCapitalBillList() != null){//交易信息
				for (CapitalBill asi : afterSalesVo.getAfterCapitalBillList()) {
					asi.setCreatorName(getUserNameByUserId(asi.getCreator()));;
				}
			}

			return afterSalesVo;
		} catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}

	/**
	 * 计算采购售后属性
	 * @param afterSalesVo
	 */
	public void calBuyOrderAfterSalesTh(AfterSalesVo afterSalesVo) {
		Integer buyOrderId = afterSalesVo.getOrderId();
		Integer afterSalesId = afterSalesVo.getAfterSalesId();
		// 已退金额（返利）
		BigDecimal haveRefundAmountFL;
		// 已退金额（不含返利）
		BigDecimal haveRefundAmount = BigDecimal.ZERO;

//				BigDecimal payPeriodAmount = afterSalesVo.getPayPeriodAmount();

		// 返利支付总金额
		BigDecimal payAmountFL = getSettlementAmount(buyOrderId,BusinessSourceTypeEnum.buyOrder,SettlementTypeEnum.REBATE_PAY);
		// 已售后返利
		BigDecimal afterFl = capitalBillApiService.getAfterSalesFlByBuyOrderId(buyOrderId);
		// 剩余返利
		BigDecimal payAmountFLRemain = payAmountFL.subtract(afterFl);
		logger.info("返利支付金额:{},已售后返利：{}",payAmountFL,afterFl);
		afterSalesVo.setPayAmountFL(payAmountFLRemain);
		// 本次返利退款金额
		BigDecimal currentPayAmountFL = getSettlementAmount(afterSalesId,BusinessSourceTypeEnum.buyOrderAfterSale,SettlementTypeEnum.REBATE_REFUND);
		logger.info("本次返利退款金额:{}",currentPayAmountFL);
		if (currentPayAmountFL.compareTo(BigDecimal.ZERO) > 0){
			logger.info("已售后返利（非本次）=已售后返利（总）-本次售后返利：{}={}-{}",afterFl.subtract(currentPayAmountFL),afterFl,currentPayAmountFL);
			afterFl = afterFl.subtract(currentPayAmountFL);
		}

		BigDecimal payAmountFLwithoutCurrent = payAmountFL.subtract(afterFl);
		logger.info("已付款金额（返利）:{}={}-{}",payAmountFLwithoutCurrent,payAmountFL,afterFl);
		//订单已付款金额不含账期----2018-06-04x修改：新增已付款不含帐期字段，之前数据需查询，肯能存在不准确（算的是实际流水金额）
//		if(afterSalesVo.getPaymentAmount().compareTo(new BigDecimal(0)) == 0){
//			BigDecimal pa = buyorderDataService.getPaymentAndPeriodAmount(afterSalesVo.getOrderId());
//			afterSalesVo.setPayAmount(pa.subtract(payAmountFL));
//			logger.info("getPaymentAmount为0时，计算PayAmount:{}={}-{}",afterSalesVo.getPayAmount(),pa,payAmountFL);
//		}else{
//			// 已售后的返利
//			BigDecimal afterSalesFl = capitalBillApiService.getAfterSalesFlByBuyOrderId(buyOrderId);
//
//			BigDecimal payAmountFLWithOutAfterSales = payAmountFL.subtract(afterSalesFl);
//			logger.info("计算已付款（含返利、不含账期、减去售后）:{}={}-{}",payAmountFLWithOutAfterSales,payAmountFL,afterSalesFl);
//			logger.info("订单总额：{}",afterSalesVo.getTotalAmount());
//			if (payAmountFL.compareTo(afterSalesVo.getTotalAmount()) != 0){
//				// 非全部返利支付，如果是全部返利不需要更新，已付款金额（不含信用支付、返利）=0
////						afterSalesVo.setPayAmount(afterSalesVo.getPaymentAmount().subtract(payAmountFLWithOutAfterSales));  // APGR041887623121547435 去除掉的
//				BigDecimal paymentAmountWithoutFl = afterSalesVo.getPaymentAmount().subtract(payAmountFLwithoutCurrent);
//				logger.info("已付款金额(不含返利)=总额-已付返利总额：{}={}-{}",paymentAmountWithoutFl,afterSalesVo.getPaymentAmount(),payAmountFLwithoutCurrent);
//				afterSalesVo.setPayAmount(paymentAmountWithoutFl.compareTo(BigDecimal.ZERO) <0 ? afterSalesVo.getPaymentAmount().subtract(payAmountFLWithOutAfterSales) : paymentAmountWithoutFl);
//			}else{
//				afterSalesVo.setPayAmount(BigDecimal.ZERO);
//			}
//		}
		BigDecimal buyOrderPayMoney = capitalBillApiService.getBuyOrderPayMoney(buyOrderId);
		afterSalesVo.setPayAmount(buyOrderPayMoney);

		haveRefundAmountFL = getSettlementAmount(afterSalesId, BusinessSourceTypeEnum.buyOrderAfterSale, SettlementTypeEnum.REBATE_REFUND);

		afterSalesVo.setHaveRefundAmountFL(haveRefundAmountFL);
		//已退金额
		if(afterSalesVo.getRefund() == 2){//退余额
			BigDecimal haveRefundAmountWithOutPeriod = afterSalesMapper.getHaveRefundAmountWithOutPeriod(afterSalesVo.getAfterSalesId());
			haveRefundAmount = haveRefundAmountWithOutPeriod.subtract(haveRefundAmountFL);
		}
		if(afterSalesVo.getRefund() == 1){ //退账户
			BigDecimal haveRefundAmountWithOutPeriod = afterSalesMapper.getHaveRefundAmountWithOutPeriod(afterSalesVo.getAfterSalesId());
			haveRefundAmount = haveRefundAmountWithOutPeriod.subtract(haveRefundAmountFL);
		}
		afterSalesVo.setHaveRefundAmount(haveRefundAmount);

		// 实际退款金额
//		BigDecimal realRefundAmountDB = afterSalesVo.getRealRefundAmount();
		// 应退金额（返利）
		BigDecimal realRefundAmountFL = getRefundAmountFL(afterSalesVo);

//				BigDecimal payPeriodAmountWithoutFl = payPeriodAmount.subtract(realRefundAmountFL);
//				if(payPeriodAmountWithoutFl.compareTo(BigDecimal.ZERO) > 0){
//					logger.info("偿还账期(返利)=原保存账期-本次退货返利金额：{}={}-{}",payPeriodAmountWithoutFl,payPeriodAmount,realRefundAmountFL);
//					afterSalesVo.setPayPeriodAmount(payPeriodAmountWithoutFl);
//				}

//		if (realRefundAmountDB.compareTo(BigDecimal.ZERO) > 0 ){
//			BigDecimal realRefundAmount = realRefundAmountDB.subtract(realRefundAmountFL);
//			//  重新赋值 实际退款金额
//			afterSalesVo.setRealRefundAmount(realRefundAmount);
//			logger.info("应退金额（不含返利）=实际退款金额-已退返利退款金额：{}={}-{}",realRefundAmount,realRefundAmountDB,realRefundAmountFL);
//		}
		afterSalesVo.setRealRefundAmountFL(realRefundAmountFL);

		// 退款金额
		BigDecimal refundAmountDB = afterSalesVo.getRefundAmount();

		if (BigDecimal.ZERO.compareTo(refundAmountDB) <= 0 ){
			// 重新赋值
			afterSalesVo.setRefundAmount(refundAmountDB.subtract(realRefundAmountFL));
		}
		// 退款金额(返利)
		afterSalesVo.setRefundAmountFL(realRefundAmountFL);
	}

	/**
	 * 获取发票模块
	 * @param afterSalesVo
	 * @return
	 */
	private List<AfterSalesInvoiceVo> getInvoice(AfterSalesVo afterSalesVo){
		List<AfterSalesInvoiceVo> afterInvoice = new ArrayList<>();
		// 开票
		List<AfterSalesInvoiceVo> afterInvoiceList1 = getAfterInvoiceList(afterSalesVo.getAfterSalesId(), SysOptionConstant.ID_504, 1);
		afterInvoice.addAll(afterInvoiceList1);
		// VDERP-11975 采购退货|换货
		if (afterSalesVo.getType() == 546 || afterSalesVo.getType() == 547){
			// 收票
			List<AfterSalesInvoiceVo> afterInvoiceList2 = getAfterInvoiceList(afterSalesVo.getAfterSalesId(), SysOptionConstant.ID_504, 2);
			List<AfterSalesInvoiceVo> collect = afterInvoiceList2.stream().filter(e -> BigDecimal.ZERO.compareTo(e.getAmount()) < 0).collect(Collectors.toList());
			afterInvoice.addAll(collect);
		}
		return afterInvoice;
	}

	/**
	 * 获取采购售后退货单的 退票信息模块
	 * @param afterSales
	 * @return
	 */
	private List<AfterSalesInvoiceVo> getAfterSalesInvoiceVoListNew(AfterSalesVo afterSales) {
		// 查询发票信息
		List<AfterSalesInvoiceVo> afterSalesInvoiceVoList = getBuyOrderAfterSaleInvoiceList(afterSales);

		if (org.apache.commons.collections.CollectionUtils.isNotEmpty(afterSalesInvoiceVoList)) {
			// 处理自动生成作废票展示问题
			List<Integer> invoiceIds = afterSalesInvoiceVoList.stream().map(AfterSalesInvoiceVo::getInvoiceId).collect(Collectors.toList());

			// 判断发票是否有已废
			Set<Integer> allHxInValidList = invoiceMapper.getInValidInvoiceListByIds(invoiceIds).stream().map(Invoice::getInvoiceId).collect(Collectors.toSet());

			if (allHxInValidList.size() > 0) {
				boolean isAddInValid = false;
				for (AfterSalesInvoiceVo item : afterSalesInvoiceVoList) {
					if (allHxInValidList.contains(item.getInvoiceId())) {
						isAddInValid = true;
						break;
					}
				}

				if (isAddInValid) {
					List<AfterSalesInvoiceVo> invoiceForInValid = afterSalesInvoiceMapper.getAfterSalesInvoiceForInValid(afterSales.getAfterSalesId());
					invoiceForInValid.removeAll(Collections.singleton(null));
					if (invoiceForInValid.size() > 0) {
						afterSalesInvoiceVoList.addAll(invoiceForInValid);
					}
				}
			}
		}
		return afterSalesInvoiceVoList;
	}

	private List<AfterSaleBuyorderDirectOutLog> getDirectReturnOutList(AfterSalesVo afterSalesVo) {
		return afterSaleBuyorderDirectOutLogMapper.getDirectReturnOutList(afterSalesVo);
	}


	/**
	 * 查询采购售后退货单发票列表
	 * @param afterSalesVo
	 * @return
	 */
	private List<AfterSalesInvoiceVo> getBuyOrderAfterSaleInvoiceList(AfterSalesVo afterSalesVo) {
		List<AfterSalesInvoiceVo> afterSalesInvoiceVoList = new ArrayList<>();
		List<AfterSalesGoodsVo> list = afterSalesVo.getAfterSalesGoodsList();
		if (list != null && list.size() > 0) {
			List<Integer> orderGoodsIdList = new ArrayList<>();
			for (AfterSalesGoodsVo asg : list) {
				orderGoodsIdList.add(asg.getOrderDetailId());
			}
			AfterSalesInvoiceVo afterSalesInvoiceVo = new AfterSalesInvoiceVo();
			afterSalesInvoiceVo.setCompanyId(afterSalesVo.getCompanyId());

			// 采购订单详情id，可能是采购商品id
			afterSalesInvoiceVo.setOrderGoodsIdList(orderGoodsIdList);
			// 采购售后单id
			afterSalesInvoiceVo.setAfterSalesId(afterSalesVo.getAfterSalesId());

			// 当前方法只能在546时调用，因此这里去掉多余的判断
			List<Integer> typeList = new ArrayList<>();
			typeList.add(504);
			typeList.add(503);
			// 采购开票和售后开票
			afterSalesInvoiceVo.setTypeList(typeList);
			afterSalesInvoiceVo.setRelatedId(afterSalesVo.getOrderId());
			afterSalesInvoiceVoList = afterSalesInvoiceMapper.getAfterSalesInvoiceVosByParamNew(afterSalesInvoiceVo);
			//查询是否有申请中的冲销申请
			for(AfterSalesInvoiceVo aftersalesInvoice : afterSalesInvoiceVoList) {
				InvoiceReversalDto invoiceReversalDto = invoiceReversalApiService.queryByInvoiceAndRelated(aftersalesInvoice.getInvoiceId(),afterSalesVo.getAfterSalesNo(),ErpConst.ONE);
				if(invoiceReversalDto != null){
					//已有冲销申请
					aftersalesInvoice.setIsHaveCoverApply(ErpConst.ONE);
				}else {
					aftersalesInvoice.setIsHaveCoverApply(ErpConst.ZERO);
				}
			}
		}
		return afterSalesInvoiceVoList;
	}


	@Override
	public AfterSalesDetailVo getAfterSalesDetailByAfterSalesId(Integer afterSalesId) {
		return afterSalesDetailMapper.getAfterSalesDetailByAfterSalesId(afterSalesId);
	}

	@Override
	public List<AfterSalesGoodsVo> getDirectBuyorderAfterSaleGoods(AfterSalesVo afterSalesVo) {
		List<AfterSalesGoodsVo> resultList = new ArrayList<>();
		List<AfterSalesGoodsVo> afterSalesGoodList = afterSalesGoodsMapper.getAfterSalesGoodList(afterSalesVo.getAfterSalesId());
		if(CollectionUtils.isEmpty(afterSalesGoodList)){
			return null;
		}
		for (AfterSalesGoodsVo afterSalesGoodsVo : afterSalesGoodList) {
		    if(afterSalesGoodsVo.getDeliveryDirect().equals(new Integer(1))) {
                Integer outNum = afterSaleBuyorderDirectOutLogMapper.getTotalNumByAfterSalesGoodsId(afterSalesGoodsVo);
				int i = afterSalesGoodsVo.getBuyorderNum() - afterSalesGoodsVo.getReturnBackNum() - afterSalesGoodsVo.getReceiveNum();
				if(i<= 0){
					i = 0;
				}
				Integer maxOutNum = afterSalesGoodsVo.getNum() - i - outNum;
                if (maxOutNum.compareTo(new Integer(0)) > 0) {
                    afterSalesGoodsVo.setMaxOutNum(maxOutNum);
                    resultList.add(afterSalesGoodsVo);
                }
            }
		}
		return resultList;
	}

	@Override
	public ResultInfo saveDirectAfterSaleOutLog(List<AfterSaleBuyorderDirectOutLog> afterSaleBuyorderDirectOutLog) {
	    if(CollectionUtils.isEmpty(afterSaleBuyorderDirectOutLog)){
            return ResultInfo.success();
        }
        for (AfterSaleBuyorderDirectOutLog saleBuyorderDirectOutLog : afterSaleBuyorderDirectOutLog) {
			if(StringUtils.isNotBlank(saleBuyorderDirectOutLog.getProduceTimeStr())){
				saleBuyorderDirectOutLog.setProduceTime(DateUtil.convertLong(saleBuyorderDirectOutLog.getProduceTimeStr(),DateUtil.TIME_FORMAT));
			}
			if(StringUtils.isNotBlank(saleBuyorderDirectOutLog.getOutTimeStr())){
				saleBuyorderDirectOutLog.setOutTime(DateUtil.convertLong(saleBuyorderDirectOutLog.getOutTimeStr(),DateUtil.TIME_FORMAT));
			}
			if(StringUtils.isNotBlank(saleBuyorderDirectOutLog.getValidTimeStr())){
				saleBuyorderDirectOutLog.setValidTime(DateUtil.convertLong(saleBuyorderDirectOutLog.getValidTimeStr(),DateUtil.TIME_FORMAT));
			}
			AfterSaleBuyorderDirectOutLog similiarLog = afterSaleBuyorderDirectOutLogMapper.getSimiliarLog(saleBuyorderDirectOutLog);
            if(similiarLog != null) {
				similiarLog.setNum(similiarLog.getNum()+saleBuyorderDirectOutLog.getNum());
				afterSaleBuyorderDirectOutLogMapper.updateByPrimaryKeySelective(similiarLog);
			} else {
				afterSaleBuyorderDirectOutLogMapper.insertSelective(saleBuyorderDirectOutLog);
			}
        }
		return ResultInfo.success();
	}

	@Override
	public AfterSaleBuyorderDirectOutLog getAfterSaleBuyorderDirectOutLog(AfterSaleBuyorderDirectOutLog afterSaleBuyorderDirectOutLog) {

		AfterSaleBuyorderDirectOutLog result = afterSaleBuyorderDirectOutLogMapper.selectByPrimaryKey(afterSaleBuyorderDirectOutLog.getAfterSaleBuyorderDirectOutLogId());
		AfterSalesGoodsVo afterSalesGoodsVo = new AfterSalesGoodsVo();
		afterSalesGoodsVo.setAfterSalesGoodsId(result.getAfterSalesGoodsId());
		Integer totalNum = afterSaleBuyorderDirectOutLogMapper.getTotalNumByAfterSalesGoodsId(afterSalesGoodsVo);
		AfterSalesGoodsVo afterSalesGoodsVo1 = afterSalesGoodsMapper.getAfterSalesGoodsInfoDetail(afterSalesGoodsVo);
		int i = afterSalesGoodsVo1.getBuyorderNum() - afterSalesGoodsVo1.getReturnBackNum() - afterSalesGoodsVo1.getReceiveNum();
		if(i<=0){
			i = 0;
		}
		Integer maxOutNum = afterSalesGoodsVo1.getNum()-i-totalNum + result.getNum();
		result.setMaxOutNum(maxOutNum);
		return result;
	}

	@Override
	public ResultInfo saveEditDirectAfterSaleOutLog(AfterSaleBuyorderDirectOutLog afterSaleBuyorderDirectOutLog) {
		afterSaleBuyorderDirectOutLogMapper.updateByPrimaryKeySelective(afterSaleBuyorderDirectOutLog);
		return ResultInfo.success();
	}

    @Override
    public ResultInfo deleteDirectAfterSaleOutLog(AfterSaleBuyorderDirectOutLog afterSaleBuyorderDirectOutLog) {
        int i = afterSaleBuyorderDirectOutLogMapper.delectById(afterSaleBuyorderDirectOutLog.getAfterSaleBuyorderDirectOutLogId());
        return ResultInfo.success();
    }

	@Override
	public ResultInfo saveInvoiceHandleInfo(Integer afterSalesId, Integer afterSalesInvoiceId, Integer handleStatus, String handleComments) {
		logger.info("saveInvoiceHandleInfo start afterSalesId:{}, handleStatus:{}, handleComments:{}",
				afterSalesId, handleStatus, handleComments);
		if (afterSalesId == null || afterSalesInvoiceId == null || handleStatus == null){
			return ResultInfo.error("保存操作信息缺少相应参数");
		}

		//1.处理售后发票的操作信息
        afterSalesInvoiceMapper.saveInvoiceHandleInfo(afterSalesInvoiceId, handleStatus, handleComments);

        //2.计算售后单的处理信息
		refreshOrderHandleStatus(afterSalesId);
		return ResultInfo.success();
	}

	@Override
	public ResultInfo refreshOrderHandleStatus(Integer afterSalesId) {
		logger.info("refreshOrderHandleStatus afterSalesId:{}", afterSalesId);
		AfterSales afterSalesParam = new AfterSales();
		afterSalesParam.setAfterSalesId(afterSalesId);
		List<AfterSalesInvoiceVo> afterSalesInvoiceVoList= getAfterSalesInvoiceVoListByParam(
				afterSalesMapper.viewAfterSalesDetailSaleorder(afterSalesParam));
		if (CollectionUtils.isEmpty(afterSalesInvoiceVoList)){
			logger.info("售后单无发票处理状态 afterSalesId:{}", afterSalesId);
			afterSalesMapper.saveOrderHandleInfo(afterSalesId, ErpConst.ZERO);
			return ResultInfo.success();
		}

		if (CollectionUtils.isEmpty(afterSalesInvoiceVoList.stream()
				.filter(item -> ErpConst.ONE.equals(item.getHandleStatus()))
				.collect(Collectors.toList()))){
			logger.info("售后单的发票处理状态为未处理 afterSalesId:{}", afterSalesId);
			afterSalesMapper.saveOrderHandleInfo(afterSalesId, ErpConst.ONE);
			return ResultInfo.success();
		}

		if (CollectionUtils.isEmpty(afterSalesInvoiceVoList.stream()
				.filter(item -> ErpConst.ZERO.equals(item.getHandleStatus()))
				.collect(Collectors.toList()))){
			logger.info("售后单的发票处理状态为全部处理 afterSalesId:{}", afterSalesId);
			afterSalesMapper.saveOrderHandleInfo(afterSalesId, ErpConst.THREE);
			return ResultInfo.success();
		}
		logger.info("售后单的发票处理状态为部分处理 afterSalesId:{}", afterSalesId);
		afterSalesMapper.saveOrderHandleInfo(afterSalesId, ErpConst.TWO);
		return ResultInfo.success();
	}


	private boolean isReturnInvoice(AfterSalesVo afterSalesVo) {
		if(afterSalesVo.getType() == 539 || afterSalesVo.getType() == 542 || afterSalesVo.getType() == 546){
			Integer num = afterSalesInvoiceMapper.getIsReturnAfterInvoice(afterSalesVo.getAfterSalesId());
			if(num == null || num == 0){
				return true;
			}else{
				return false;
			}
		}else{
			return true;
		}
	}

	private List<AfterSalesTrader> getBuyorderAfterSalesTraderList(Integer afterSalesId) {
		return afterSalesTraderMapper.getAfterSalesTraderList(afterSalesId);
	}

	private List<AfterSalesRecordVo> getAfterSalesRecordVoListByParam(AfterSalesVo afterSales) {
		AfterSalesRecord afterSalesRecord = new AfterSalesRecord();
		afterSalesRecord.setAfterSalesId(afterSales.getAfterSalesId());
		List<AfterSalesRecordVo> afterSalesRecordVoList = afterSalesRecordMapper.getAfterSalesRecordVoList(afterSalesRecord);
		return afterSalesRecordVoList;
	}


	private List<PayApply> getAfterAtPaymentApply(Integer afterSalesId) {
		return afterSalesInvoiceMapper.getAfterAtPaymentApply(afterSalesId);
	}

	private List<AfterSalesInvoiceVo> getAfterInvoiceList(Integer afterSalesId, Integer type, int i) {
		return afterSalesInvoiceMapper.getAfterInvoiceList(afterSalesId,type,i);
	}

	private List<WarehouseGoodsOperateLog> getAfterReturnOutStockList(AfterSalesVo afterSalesVo) {
		//换货出库记录
		Saleorder saleorder = new Saleorder();
		saleorder.setBussinessId(afterSalesVo.getAfterSalesId());
		if(afterSalesVo.getType()==540){
			saleorder.setBussinessType(4);
		}else if(afterSalesVo.getType()==546){//采购退货出库
			saleorder.setBussinessType(6);
		}else if(afterSalesVo.getType()==547){//采购换货出库
			saleorder.setBussinessType(7);
		}
		List<WarehouseGoodsOperateLog> wlog = warehouseGoodsOperateLogMapper.getWarehouseOutList(saleorder);
		return wlog;
	}

	private List<WarehouseGoodsOperateLog> getAfterReturnGoodsStorageList(AfterSalesVo afterSalesVo) {
		//退换货入库记录
		WarehouseGoodsOperateLog wlg = new WarehouseGoodsOperateLog();
		wlg.setIsEnable(1);
		wlg.setCompanyId(afterSalesVo.getCompanyId());
		wlg.setAfterSalesId(afterSalesVo.getAfterSalesId());
		if(afterSalesVo.getType()== 540){//销售换货
			wlg.setOperateType(3);
			wlg.setYwType(540);
		}else if(afterSalesVo.getType()== 539){//销售退货
			wlg.setOperateType(5);
			wlg.setYwType(539);
		}else if(afterSalesVo.getType()== 547){//采购换货
			wlg.setOperateType(8);
			wlg.setYwType(547);
			AfterSales afterSalesReturn = afterSalesMapper.getAfterSalesById(afterSalesVo.getAfterSalesId());
			List<WarehouseGoodsOperateLog> warehouseGoodsOperateLogList = new ArrayList<>();
			if(Objects.nonNull(afterSalesReturn)) {
				List<WarehouseGoodsOutInItem> outInItemList = warehouseGoodsOutInItemMapper.getRetrunOutInItem(afterSalesReturn.getAfterSalesNo());
				//如果新的入库表有数据，则展示新的入库表数据
				for (WarehouseGoodsOutInItem warehouseGoodsOutInItem : outInItemList) {
					OutInDetail outInDetail = new OutInDetail();
					warehouseGoodsInService.querySkuInfo(outInDetail, warehouseGoodsOutInItem.getGoodsId());
					WarehouseGoodsOperateLog warehouseGoodsOperateLog = new WarehouseGoodsOperateLog();
					warehouseGoodsOperateLog.setSku("V"+warehouseGoodsOutInItem.getGoodsId());
					warehouseGoodsOperateLog.setGoodsId(warehouseGoodsOutInItem.getGoodsId());
					warehouseGoodsOperateLog.setVedengBatchNumer(warehouseGoodsOutInItem.getVedengBatchNumber());
					warehouseGoodsOperateLog.setNum(warehouseGoodsOutInItem.getNum().intValue());
					warehouseGoodsOperateLog.setProductDate(DateUtil.convertLong(warehouseGoodsOutInItem.getProductDate(), DateUtil.TIME_FORMAT));
					warehouseGoodsOperateLog.setExpirationDate(DateUtil.convertLong(warehouseGoodsOutInItem.getExpirationDate(), DateUtil.TIME_FORMAT));
					warehouseGoodsOperateLog.setAddTime(warehouseGoodsOutInItem.getAddTime().getTime());
					warehouseGoodsOperateLog.setBatchNumber(warehouseGoodsOutInItem.getBatchNumber());
					warehouseGoodsOperateLog.setSterilizationBatchNo(warehouseGoodsOutInItem.getSterilizationBatchNumber());
					warehouseGoodsOperateLog.setRegistrationNo(outInDetail.getRegistrationNumber());
					warehouseGoodsOperateLog.setFirstEngageId(String.valueOf(outInDetail.getFirstEngageId()));
					warehouseGoodsOperateLog.setRegistrationNumber(outInDetail.getRegistrationNumber());
					warehouseGoodsOperateLog.setCheckStatusTime(DateUtil.convertLong(warehouseGoodsOutInItem.getCheckStatusTime(), DateUtil.TIME_FORMAT));
					warehouseGoodsOperateLog.setModel(outInDetail.getModel());
					warehouseGoodsOperateLog.setSpec(outInDetail.getSpec());
					warehouseGoodsOperateLogList.add(warehouseGoodsOperateLog);
				}
			}
			if(!CollectionUtils.isEmpty(warehouseGoodsOperateLogList)){
				return warehouseGoodsOperateLogList;
			}
		}
		List<WarehouseGoodsOperateLog> wlog = warehouseGoodsOperateLogMapper.getWGOlog(wlg);
		return wlog;
	}

	private List<WarehouseGoodsOperateLog> getWarehouseOutListByParam(AfterSalesVo afterSales) {
		Saleorder saleorder = new Saleorder();
		saleorder.setSaleorderId(afterSales.getOrderId());
		List<WarehouseGoodsOperateLog> warehouseGoodsOperateLogList = warehouseGoodsOperateLogMapper.getWarehouseOutList(saleorder);
		return warehouseGoodsOperateLogList;
	}

	private List<AfterSalesGoods> getTHHGoodsList(AfterSalesVo afterSales) {
		AfterSalesGoods afterSalesGoods = new AfterSalesGoods();
		afterSalesGoods.setAfterSalesId(afterSales.getAfterSalesId());
		List<AfterSalesGoods> thhList = afterSalesGoodsMapper.getThhGoodsList(afterSalesGoods);
		return thhList;

	}

	private List<AfterSalesGoodsVo> getBuyorderAfterSalesGoodsVoListByParamNew(AfterSalesVo afterSales) {
		//退货，换货，安调,维修
		AfterSalesGoods afterSalesGoods = new AfterSalesGoods();
		afterSalesGoods.setAfterSalesId(afterSales.getAfterSalesId());
		//退货信息
		List<AfterSalesGoodsVo> list = afterSalesGoodsMapper.getBuyorderAfterSalesGoodsVosList(afterSalesGoods);
		if(list != null && afterSales.getType() != null && (afterSales.getType() == 546 || afterSales.getType() == 547)){
			for (AfterSalesGoodsVo asgv : list) {
				GoodsData stockInfo = getStockInfoByGoodsId(asgv.getGoodsId());
				//库存量
				Integer goodsStockNum = stockInfo.getStockNum();
				Integer goodsOccupyNum = stockInfo.getOccupyNum();
				//产品库存信息
				asgv.setGoodsStock(goodsStockNum);
				//订单占用数量
				asgv.setOrderOccupy(goodsOccupyNum);
				Integer buynum = buyorderGoodsMapper.getBuyorderGoodsSum(asgv.getOrderDetailId());
				asgv.setBuyNum(buynum);
				//换货
				if(afterSales.getType() == 547){
					WarehouseGoodsOperateLog wl = new WarehouseGoodsOperateLog();
					wl.setRelatedId(asgv.getAfterSalesGoodsId());
					wl.setOperateType(7);//采购换货出库库
					asgv.setExchangeReturnNum(warehouseGoodsOperateLogMapper.getAftersalesGoodsSum(wl));
					wl.setOperateType(8);//采购换货入库
					asgv.setExchangeDeliverNum(warehouseGoodsOperateLogMapper.getAftersalesGoodsSum(wl));
					Integer buyorderGoodsId = asgv.getOrderDetailId();
					List<Integer> saleorderGoodsIdList = saleorderGoodsMapper.getSaleorderGoodsByBuyorderGoodsId(buyorderGoodsId);
					if(!CollectionUtils.isEmpty(saleorderGoodsIdList)) {
						List<AfterSalesVo> afterSalesVos = afterSalesGoodsMapper.getAfterSaleInfobySaleorderGoodsIds(saleorderGoodsIdList, 540);
						asgv.setSaleorderAfterSales(afterSalesVos);
					}
				}
				//退货
				if(afterSales.getType() == 546){
					//销售退货单
					Integer buyorderGoodsId = asgv.getOrderDetailId();
					List<Integer> saleorderGoodsIdList = saleorderGoodsMapper.getSaleorderGoodsByBuyorderGoodsId(buyorderGoodsId);
					if(!CollectionUtils.isEmpty(saleorderGoodsIdList)) {
						List<AfterSalesVo> afterSalesVos = afterSalesGoodsMapper.getAfterSaleInfobySaleorderGoodsIds(saleorderGoodsIdList, 539);
						asgv.setSaleorderAfterSales(afterSalesVos);
					}
				}

			}
		}
		return list;
	}

	private GoodsData getStockInfoByGoodsId(Integer goodsId) {
		String sku = "V"+goodsId;
		List<String> skuList = new ArrayList<>();
		skuList.add(sku);
		Map<String, WarehouseStock> stockInfo = warehouseStockService.getStockInfo(skuList);
		GoodsData goodsData = new GoodsData();
		goodsData.setGoodsId(goodsId);
		goodsData.setStockNum(stockInfo.get(sku).getStockNum());
		goodsData.setOccupyNum(stockInfo.get(sku).getOccupyNum());
		return goodsData;
	}

	private List<Attachment> getAttachmentListByParam(AfterSalesVo afterSales) {
		//排除技术咨询和其他
		Attachment attachment = new Attachment();
		attachment.setRelatedId(afterSales.getAfterSalesId());
		attachment.setAttachmentFunction(afterSales.getType());
		List<Attachment> attachments = attachmentMapper.getAfterSaleAttachmentList(attachment);
		return attachments;
	}

	@Override
	public ResultInfo refreshInvoiceRefundStatus(Integer afterSalesId) {
		logger.info("刷新售后单退票状态 start afterSalesId:{}", afterSalesId);
		if (afterSalesId == null){
			return ResultInfo.error("刷新售后单退票状态参数不全");
		}
        AfterSales afterSaleInfo = getAfterSalesById(afterSalesId);
		if (afterSaleInfo == null){
		    return ResultInfo.error("刷新售后单退票状态售后单信息错误");
        }

		switch (AfterSalesProcessEnum.getInstance(afterSaleInfo.getType())){
			case AFTERSALES_TH:
            case AFTERSALES_TP:{
                List<AfterSalesInvoice> afterSalesInvoices = afterSalesInvoiceMapper.getAfterSalesInvoiceByAfterSaleId(afterSalesId);

                //1.销售售后退票为无退票情况
                if (CollectionUtils.isEmpty(afterSalesInvoices)){
                    logger.info("销售售后退票为无退票情况 afterSalesId:{}", afterSalesId);
                    afterSalesMapper.saveOrderInvoiceRefundStatus(afterSalesId, ErpConst.ZERO);
                    return ResultInfo.success();
                }
                //2.销售售后为全部退票情况
                if (CollectionUtils.isEmpty(afterSalesInvoices.stream()
                        .filter(item -> ErpConst.ZERO.equals(item.getStatus()))
                        .collect(Collectors.toList()))){
                    logger.info("销售售后为全部退票情况 afterSalesId:{}", afterSalesId);
                    afterSalesMapper.saveOrderInvoiceRefundStatus(afterSalesId, ErpConst.THREE);
                    return ResultInfo.success();
                }
                //3.销售售后为未退票情况
                if (CollectionUtils.isEmpty(afterSalesInvoices.stream()
                        .filter(item -> ErpConst.ONE.equals(item.getStatus()))
                        .collect(Collectors.toList()))) {
                    logger.info("销售售后为未退票情况 afterSalesId:{}", afterSalesId);
                    afterSalesMapper.saveOrderInvoiceRefundStatus(afterSalesId, ErpConst.ONE);
                    return ResultInfo.success();
                }
                //销售售后为部分退票情况
                logger.info("销售售后为部分退票情况 afterSalesId:{}", afterSalesId);
                afterSalesMapper.saveOrderInvoiceRefundStatus(afterSalesId, ErpConst.TWO);
                break;
            }
            default:{
                logger.info("刷新售后单退票状态未匹配到订单类型");
            }
        }
		return ResultInfo.success();
	}

	@Override
	public BigDecimal getHaveRefundedAmount(AfterSalesVo afterSalesVo) {
		//非退款类型售后单计算暂时跳过，若计算方法一致需要维护
		BigDecimal refundAmount = BigDecimal.ZERO.setScale(BigDecimal.ROUND_CEILING);
		if (!AfterSalesProcessEnum.AFTERSALES_TK.getCode().equals(afterSalesVo.getType())
				&& !AfterSalesProcessEnum.AFTERSALES_TH.getCode().equals(afterSalesVo.getType())){
			return refundAmount;
		}
		if(AfterSalesProcessEnum.AFTERSALES_TK.getCode().equals(afterSalesVo.getType())
				||(AfterSalesProcessEnum.AFTERSALES_TH.getCode().equals(afterSalesVo.getType()) && ErpConst.TWO.equals(afterSalesVo.getRefund()))){
			if (ErpConst.ONE.equals(afterSalesVo.getRefund())){
				return afterSalesVo.getFinalRefundableAmount();
			}
			if (CollectionUtils.isEmpty(afterSalesVo.getAfterCapitalBillList()) ||
					CollectionUtils.isEmpty(getCapitalBillCollect(afterSalesVo))){
				return refundAmount;
			}
			//如果是售后退款或售后退货退给客户
			for (CapitalBill capitalBill : getCapitalBillCollect(afterSalesVo)) {
				refundAmount = refundAmount.add(capitalBill.getAmount());
			}
		}else {
			refundAmount = afterSalesVo.getFinalRefundableAmount().abs();
		}

		return refundAmount;
	}

	private List<CapitalBill> getCapitalBillCollect(AfterSalesVo afterSalesVo) {
		return afterSalesVo.getAfterCapitalBillList().stream().filter(getCapitalBillPredicate())
				.collect(Collectors.toList());
	}

	@Override
	public ResultInfo refreshAmountRefundStatus(Integer afterSalesId) {
		logger.info("刷新售后单退款状态 afterSalesId:{}", afterSalesId);
		if (afterSalesId == null){
			return ResultInfo.error("刷新售后单退款状态参数不全");
		}

		//获取售后类型
		AfterSalesDetailVo afterSalesDetailVo = afterSalesDetailMapper.getAfterSalesDetailByAfterSalesId(afterSalesId);

		if (afterSalesDetailVo == null){
			return ResultInfo.error("刷新售后单退款状态售后单信息错误");
		}

		/**
		 * 采购类型跳过退款状态的刷新
		 */
		if (afterSalesDetailVo.getSubjectType().equals(536)){
			logger.info("采购类型跳过退款状态的刷新 afterSalesId:{}", afterSalesId);
			return ResultInfo.success();
		}


		if (!ErpConst.ONE.equals(afterSalesDetailVo.getIsNew())){
			logger.info("非订单流订单跳过付款状态的刷新 afterSalesId:{}", afterSalesId);
			return ResultInfo.success();
		}

		logger.info("刷新售后单退款状态 售后单信息 afterSaleInfo:{}", JSON.toJSONString(afterSalesDetailVo));

		//获取交易记录
		CapitalBill capitalBill = new CapitalBill();
		capitalBill.setRelatedId(afterSalesDetailVo.getAfterSalesId());
		List<CapitalBill> list = getCaptionBillList(capitalBill);

		switch (AfterSalesProcessEnum.getInstance(afterSalesDetailVo.getAfterType())){
			case AFTERSALES_TK:{
				if (afterSalesDetailVo.getFinalRefundableAmount().compareTo(BigDecimal.ZERO) == 0){
					logger.info("刷新售后单退款状态为退款类型并且实际应退金额等于0 afterSalesId:{}", afterSalesId);
					afterSalesMapper.saveOrderAmountRefundStatus(afterSalesId, ErpConst.ZERO);
					return ResultInfo.success();
				}

				if (ErpConst.ONE.equals(afterSalesDetailVo.getRefund())){
					afterSalesMapper.saveOrderAmountRefundStatus(afterSalesId, ErpConst.THREE);
					return ResultInfo.success();
				}

				BigDecimal haveRefundedAmount = list.stream().filter(getCapitalBillPredicate())
						.map(CapitalBill::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
				saveOrderAmountRefundStatusInfo(afterSalesDetailVo, haveRefundedAmount);
				break;
			}
			case AFTERSALES_TH:{
				if(afterSalesDetailVo.getFinalRefundableAmount().compareTo(BigDecimal.ZERO) <= 0){
					logger.info("退货售后单，实际应退金额为{},更新退款状态为无退款",afterSalesDetailVo.getFinalRefundableAmount());
					afterSalesMapper.saveOrderAmountRefundStatus(afterSalesId, ErpConst.ZERO);
					return ResultInfo.success();
				}
				BigDecimal haveRefundedAmount = BigDecimal.ZERO;
				if(ErpConst.ONE.equals(afterSalesDetailVo.getRefund())){
					//退还客户余额
					haveRefundedAmount = afterSalesDetailVo.getFinalRefundableAmount();
				}else if(ErpConst.TWO.equals(afterSalesDetailVo.getRefund())){
					//退还客户
					haveRefundedAmount = list.stream().filter(getCapitalBillPredicate())
							.map(CapitalBill::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
				}
				saveOrderAmountRefundStatusInfo(afterSalesDetailVo, haveRefundedAmount);
				break;
			}
			case AFTERASALES_THIRD_AT:
			case AFTERASALES_THIRD_WX:
            case AFTERSALES_ATY:
            case AFTERSALES_ATN:
			case AFTERSALES_WX:
			case AFTERSALES_AT:{
				savePayAmountAtStatuts(afterSalesId);
				//savePayAmountStatutsInfo(afterSalesDetailVo,list);
				break;
			}
			default:{
				logger.info("该售后单类型未接入退款状态更新 afterSalesId:{},type:{}", afterSalesId,
						AfterSalesProcessEnum.getInstance(afterSalesDetailVo.getAfterType()).getDesc());
				break;
			}
		}

		return ResultInfo.success();
	}

	@Override
	public void savePayAmountAtStatuts(Integer relatedId) {

		AfterSalesDetailVo detailVo = afterSalesDetailMapper.getAfterSalesDetailByAfterSalesId(relatedId);
		if(null != detailVo) {
			switch (AfterSalesProcessEnum.getInstance(detailVo.getAfterType())) {
				case AFTERASALES_THIRD_AT:
				case AFTERASALES_THIRD_WX:
                case AFTERSALES_ATY:
                case AFTERSALES_ATN:
				case AFTERSALES_WX:
				case AFTERSALES_AT: {
					CapitalBill capitalBill = new CapitalBill();
					capitalBill.setRelatedId(relatedId);
					List<CapitalBill> list = getCaptionBillList(capitalBill);
					//实际应付
					//AfterSalesDetailVo afterSalesDetailVo = afterSalesDetailMapper.getAfterSalesDetailByAfterSalesId(capitalBill.getRelatedId());
					//BigDecimal seriveAmount = afterSalesDetailVo == null ? BigDecimal.ZERO: afterSalesDetailVo.getServiceAmount();
					//List<PayApply> payApplyList = afterSalesInvoiceMapper.getAfterAtPaymentApply(capitalBill.getRelatedId());
                    /*if (CollectionUtils.isEmpty(payApplyList)){
                        savePayAmountStatuts(capitalBill.getRelatedId(), ErpConst.ZERO);
                        break;
                    }*/
					// 获取工程师酬金总金额
					BigDecimal seriveAmount = afterSalesInstallstionMapper.getAfterSalesInstallstionAmount(capitalBill.getRelatedId());
					if (seriveAmount.compareTo(BigDecimal.ZERO) == 0){
						savePayAmountStatuts(capitalBill.getRelatedId(), ErpConst.ZERO);
					}else if (seriveAmount.compareTo(BigDecimal.ZERO) > 0){
						savePayAmountStatuts(capitalBill.getRelatedId(), ErpConst.ONE);
					}
					if (org.apache.commons.collections.CollectionUtils.isNotEmpty(list)){
						//已付金额
						BigDecimal amountReceived = list.stream().filter(t->null != t.getCapitalBillDetail() && SysOptionConstant.ID_525.equals(t.getCapitalBillDetail().getBussinessType())).map(CapitalBill::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

						//BigDecimal seriveAmount = payApplyList.stream().filter(Objects::nonNull).filter(s-> ErpConst.ONE.equals(s.getValidStatus())).map(PayApply::getAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
						//BigDecimal seriveAmount = payApplyList.stream().filter(Objects::nonNull).filter(s-> ErpConst.ONE.equals(s.getValidStatus())).map(PayApply::getAmount).reduce(BigDecimal.ZERO,BigDecimal::add);

						if (seriveAmount.compareTo(BigDecimal.ZERO) > 0){
							if (amountReceived.compareTo(BigDecimal.ZERO) == 0){
								savePayAmountStatuts(capitalBill.getRelatedId(), ErpConst.ONE);
							}else{
								if (amountReceived.compareTo(seriveAmount) < 0){
									savePayAmountStatuts(capitalBill.getRelatedId(), ErpConst.TWO);
								}else{
									savePayAmountStatuts(capitalBill.getRelatedId(), ErpConst.THREE);
								}
							}
						}
					}
					break;
				}
			}
		}

	}

	public void savePayAmountStatuts(Integer afterSalesId, Integer status) {
		afterSalesMapper.savePayAmountStatuts(afterSalesId,status);
	}

	private Predicate<CapitalBill> getCapitalBillPredicate() {
		return item ->
				CapitalBillBussinessTypeEnum.ORDER_REFUND.getCode().equals(item.getCapitalBillDetail().getBussinessType()) &&
						(TraderModeEnum.ALI_PAY.getCode().equals(item.getTraderMode()) ||
								TraderModeEnum.BANK_PAY.getCode().equals(item.getTraderMode()) ||
								TraderModeEnum.WE_CHAT_PAY.getCode().equals(item.getTraderMode()));
	}

	public List<CapitalBill> getCaptionBillList(CapitalBill capitalBill){
		//获取交易记录
		CapitalBillDetail capitalBillDetail = new CapitalBillDetail();
		capitalBillDetail.setOrderType(Constants.THREE);//售后订单类型
		/*capitalBillDetail.setOrderNo(afterSalesVo.getOrderNo());*/
		capitalBillDetail.setRelatedId(capitalBill.getRelatedId());
		capitalBill.setCapitalBillDetail(capitalBillDetail);
		List<CapitalBill> list = capitalBillMapper.getCapitalBillList(capitalBill);
		return list;
	}

	public void saveOrderAmountRefundStatusInfo(AfterSalesDetailVo afterSalesDetailVo,BigDecimal haveRefundedAmount){
		haveRefundedAmount = haveRefundedAmount.abs();
		logger.info("刷新售后单退款状态已经退款总金额 haveRefundedAmount:{}", haveRefundedAmount);

		if (afterSalesDetailVo.getFinalRefundableAmount().compareTo(BigDecimal.ZERO) > 0) {
			if (haveRefundedAmount.compareTo(BigDecimal.ZERO) == 0) {
				logger.info("退款刷新售后单退款状态为未退款 afterSalesId:{}", afterSalesDetailVo.getAfterSalesId());
				afterSalesMapper.saveOrderAmountRefundStatus(afterSalesDetailVo.getAfterSalesId(), ErpConst.ONE);
				return;
			}
			if (afterSalesDetailVo.getFinalRefundableAmount().compareTo(haveRefundedAmount) > 0) {
				logger.info("退款刷新售后单退款状态为部分退款 afterSalesId:{}", afterSalesDetailVo.getAfterSalesId());
				afterSalesMapper.saveOrderAmountRefundStatus(afterSalesDetailVo.getAfterSalesId(), ErpConst.TWO);
				return;
			}
			if (haveRefundedAmount.compareTo(afterSalesDetailVo.getFinalRefundableAmount()) == 0) {
				logger.info("退款刷新售后单退款状态为全部退款 afterSalesId:{}", afterSalesDetailVo.getAfterSalesId());
				afterSalesMapper.saveOrderAmountRefundStatus(afterSalesDetailVo.getAfterSalesId(), ErpConst.THREE);
			}
		}
	}

	public void savePayAmountStatutsInfo(AfterSalesDetailVo afterSalesDetailVo,List<CapitalBill> list){
		//修改付款状态,此处判断付款状态
		if (!CollectionUtils.isEmpty(list)){
			//已付金额
			BigDecimal payReceived = list.stream().filter(t->null != t.getCapitalBillDetail() && CapitalBillBussinessTypeEnum.ORDER_PAYMENT.getCode().equals(t.getCapitalBillDetail().getBussinessType()) ).map(CapitalBill::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
			//实际应付
			BigDecimal payNeed = afterSalesDetailVo == null ? BigDecimal.ZERO: afterSalesDetailVo.getServiceAmount();
			if (payNeed.compareTo(BigDecimal.ZERO) > 0) {

				if (payReceived.compareTo(BigDecimal.ZERO)==0){
					logger.info("刷新售后单付款状态为无付款 afterSalesId:{}", afterSalesDetailVo.getAfterSalesId());
					afterSalesMapper.savePayAmountStatuts(afterSalesDetailVo.getAfterSalesId(),ErpConst.ONE);
				}else {
					if (payReceived.compareTo(payNeed) < 0){
						logger.info("刷新售后单付款状态为部分付款 afterSalesId:{}", afterSalesDetailVo.getAfterSalesId());
						afterSalesMapper.savePayAmountStatuts(afterSalesDetailVo.getAfterSalesId(),ErpConst.TWO);
					}else if (payReceived.compareTo(payNeed) == 0){
						logger.info("刷新售后单付款状态为全部付款 afterSalesId:{}", afterSalesDetailVo.getAfterSalesId());
						afterSalesMapper.savePayAmountStatuts(afterSalesDetailVo.getAfterSalesId(), ErpConst.THREE);
					}
				}

			}else if (payNeed.compareTo(BigDecimal.ZERO)==0){
				logger.info("刷新售后单付款状态为未付款 afterSalesId:{}", afterSalesDetailVo.getAfterSalesId());
				afterSalesMapper.savePayAmountStatuts(afterSalesDetailVo.getAfterSalesId(), ErpConst.ZERO);
			}
		}

	}

	@Override()
	public List<AfterSalesGoodsVo> getOutStockResult(AfterSalesVo afterSalesVo) {
		List<AfterSalesGoodsVo> afterSalesGoodList = afterSalesGoodsMapper.getAfterSalesGoodList(afterSalesVo.getAfterSalesId());
		if(CollectionUtils.isEmpty(afterSalesGoodList)) {
			return null;
		}
		for (AfterSalesGoodsVo afterSalesGoodsVo : afterSalesGoodList) {
			Integer outNum = 0;
			if(afterSalesGoodsVo.getDeliveryDirect().equals(0)){
				outNum = warehouseGoodsOperateLogMapper.getBuyorderAfterSaleLogNumByType(afterSalesGoodsVo.getAfterSalesGoodsId(), 6);
			} else if (afterSalesGoodsVo.getDeliveryDirect().equals(1)){
				outNum = afterSaleBuyorderDirectOutLogMapper.getTotalNumByAfterSalesGoodsId(afterSalesGoodsVo);
			}
			Integer notReceiveNum = afterSalesGoodsVo.getBuyorderNum()- afterSalesGoodsVo.getReturnBackNum() - afterSalesGoodsVo.getReceiveNum();
			if(notReceiveNum < 0){
				notReceiveNum = 0;
			}
			Integer flagNum = outNum - afterSalesGoodsVo.getNum() + notReceiveNum;
			afterSalesGoodsVo.setFlagNum(flagNum);
		}
		return afterSalesGoodList;
	}

    @Override
    public List<AfterSalesGoodsVo> getInOrOutLog(AfterSalesVo afterSalesVo) {
        List<AfterSalesGoodsVo> afterSalesGoodList = afterSalesGoodsMapper.getAfterSalesGoodList(afterSalesVo.getAfterSalesId());
        if(CollectionUtils.isEmpty(afterSalesGoodList)) {
            return null;
        }
		afterSalesGoodList.removeIf(e->ErpConst.ONE.equals(e.getDeliveryDirect()));
		if(CollectionUtils.isEmpty(afterSalesGoodList)) {
			return null;
		}
        for (AfterSalesGoodsVo afterSalesGoodsVo : afterSalesGoodList) {
            Integer outNum = warehouseGoodsOperateLogMapper.getBuyorderAfterSaleLogNumByType(afterSalesGoodsVo.getAfterSalesGoodsId(), 7);
            Integer inNum = warehouseGoodsOperateLogMapper.getBuyorderAfterSaleLogNumByType(afterSalesGoodsVo.getAfterSalesGoodsId(), 8);
            if(outNum.compareTo(afterSalesGoodsVo.getNum()) == 0 && inNum.compareTo(afterSalesGoodsVo.getNum())==0){
                afterSalesGoodsVo.setCloseFlag(1);
            } else {
                afterSalesGoodsVo.setCloseFlag(0);
            }

        }

	    return afterSalesGoodList;
    }

	@Override
	public List<AfterSalesGoodsVo> getAfterSalesGoodsList(Integer afterSalesId) {
		return afterSalesGoodsMapper.getAfterSalesGoodList(afterSalesId);
	}

	@Override
	public List<AfterSalesGoodsVo> getBuyorderAfterSalesGoodsVosList(AfterSalesGoods afterSalesGoods) {
		return afterSalesGoodsMapper.getBuyorderAfterSalesGoodsVosList(afterSalesGoods);
	}

	@Override
	public List<RInstallstionJGoods> getRInstallstionJGoodsList(RInstallstionJGoods rInstallstionJGoods) {
		return rInstallstionJGoodsMapper.getRInstallstionJGoodsList(rInstallstionJGoods);
	}

	@Override
	@Transactional
	public void updateSaleorderAllStatus(Integer afterSalesId, Integer saleorderId) {
		long start=System.currentTimeMillis();
		if(afterSalesId > 0 && saleorderId > 0){

			Saleorder so = saleorderMapper.selectByPrimaryKey(saleorderId);

			//销售
			Saleorder saleorder = new Saleorder();

			saleorder.setSaleorderId(saleorderId);
			saleorder.setServiceStatus(ErpConst.TWO);//售后关闭
//			saleorder.setLockedStatus(0);

			//退货时刷新销售单的发货、收货、付款、开票状态
			//销售单商品发货收货状态
			List<SaleorderGoodsVo> list = afterSalesGoodsMapper.getReturnGoodsList(afterSalesId);

			if(!CollectionUtils.isEmpty(list)){

				List<SaleorderGoodsVo> saleAfterList = afterSalesGoodsMapper.batchSaleorderAftersaleReturnGoods(list);//当前售后单中销售商品和总退货数量

				List<SaleorderGoods> sgList = saleorderGoodsMapper.getSaleorderGoodsListBySaleorderId(saleorderId);

				SaleorderGoods saleorderGoods = null;
				Express express = null;

				Set<Integer> arrivalSet = new HashSet<>();

				for (SaleorderGoods sg : sgList) {

					for (SaleorderGoodsVo sgv : saleAfterList) {

						if(sg.getSaleorderGoodsId().equals(sgv.getSaleorderGoodsId()) && sg.getDeliveryStatus() == 1){

							saleorderGoods = new SaleorderGoods();
							saleorderGoods.setSaleorderGoodsId(sg.getSaleorderGoodsId());

							if(sg.getNum()-sgv.getSaleAfterNum() > 0 && sg.getNum()-sgv.getSaleAfterNum() <= sg.getDeliveryNum()&& sg.getDeliveryNum() > 0){
								saleorderGoods.setDeliveryStatus(ErpConst.TWO);
								sg.setDeliveryStatus(ErpConst.TWO);
							}

							if(ErpConst.ONE.equals(sg.getArrivalStatus())){

								express = new Express();

								express.setOrderGoodsId(sg.getSaleorderGoodsId());

								Express e = expressMapper.getSEGoodsNum(express);

								if (e.getAllnum().equals(e.getFnum()) ) {
									// 全部签收
									saleorderGoods.setArrivalStatus(ErpConst.TWO);
									sg.setArrivalStatus(ErpConst.TWO);
								}

							}

							if(saleorderGoods.getArrivalStatus() != null && saleorderGoods.getArrivalStatus() != 0){
								saleorderGoodsMapper.updateByPrimaryKeySelectiveDB(saleorderGoods);
							}

						}

					}

					if(sg.getArrivalStatus() != 0){
						arrivalSet.add(sg.getArrivalStatus());
					}

				}
				//订单发货状态,除去特殊商品的只有一个是部分发货，整体就是部分发货，同理收货
				if(ErpConst.ONE.equals(so.getDeliveryStatus())){

					boolean isAllDelivery = true;//默认全部发货

					for(SaleorderGoods sg : sgList) {

						if(sg.getDeliveryStatus() == 1 || sg.getDeliveryStatus() == 0){
							isAllDelivery = false;
							break;
						}

					}

					if(isAllDelivery ){
						saleorder.setDeliveryStatus(ErpConst.TWO);
					}

				}

				//订单收货状态
				if(ErpConst.ONE.equals(so.getArrivalStatus())){

					boolean isAllArrival = true;//默认全部收货

					for(SaleorderGoods sg : sgList) {

						if(sg.getArrivalStatus() == 1 || sg.getArrivalStatus() == 0){
							isAllArrival = false;
							break;
						}

					}

					if(isAllArrival){
						saleorder.setArrivalStatus(ErpConst.TWO);
					}

				}

				BigDecimal afterAmount = invoiceMapper.getSaleOrderAfterRecord(saleorderId);

				//开票状态
				if(ErpConst.ONE.equals(so.getInvoiceStatus())){

					BigDecimal amount = invoiceMapper.getSaleOpenInvoiceAmount(saleorderId);//开票金额

					if(so.getTotalAmount().subtract(afterAmount).compareTo(amount) <= 0 && amount.compareTo(new BigDecimal(0)) > 0){
						saleorder.setInvoiceStatus(ErpConst.TWO);
					}

				}

				if(ErpConst.ONE.equals(so.getPaymentStatus())){

					//付款状态
					BigDecimal realAmount = saleorderDataService.getPaymentAndPeriodAmount(saleorderId);

					if(so.getTotalAmount().subtract(afterAmount).compareTo(realAmount) <= 0 && realAmount.compareTo(new BigDecimal(0)) > 0){
						saleorder.setPaymentStatus(ErpConst.TWO);
						saleorder.setSatisfyDeliveryTime(DateUtil.sysTimeMillis());

					}

				}

			}

			saleorderMapper.updateByPrimaryKeySelective(saleorder);
		}
		long end=System.currentTimeMillis();
		if((end-start)>10000){
			logger.error("事务过长"+saleorderId);
		}
	}

	@Transactional
	@Override
	public void updateBuyorderAllStatus(Integer afterSalesId, Integer buyorderId) {
		long start=System.currentTimeMillis();
		if(afterSalesId > 0 && buyorderId > 0){

			Buyorder so = buyorderMapper.selectByPrimaryKey(buyorderId);
			//采购
			Buyorder buyorder = new Buyorder();

			buyorder.setBuyorderId(buyorderId);
			buyorder.setServiceStatus(ErpConst.TWO);//售后关闭
			buyorder.setLockedStatus(ErpConst.ZERO);

			//采购售后完成后需刷新销售单的采购状态
			AfterSalesGoods afterSalesGoods = new AfterSalesGoods();

			afterSalesGoods.setAfterSalesId(afterSalesId);

			List<AfterSalesGoodsVo> list = afterSalesGoodsMapper.getBuyorderAfterSalesGoodsVosList(afterSalesGoods);

			if(!CollectionUtils.isEmpty(list)){

				for (AfterSalesGoodsVo asgv : list) {

					//查询采购数量
					Integer buyNum = rBuyorderSaleorderMapper.getBuyorderGoodsNumByParam(asgv.getOrderDetailId());

					if(buyNum.intValue() >= asgv.getNum().intValue()){//采购数量大于采购售后数量，销售单采购状态肯定为部分采购
						//最简单情况--同类产品售后全退
						List<SaleorderGoods> sgvList = saleorderGoodsMapper.getSaleorderGoodsVoByBuyorderGoodsId(asgv.getOrderDetailId());

						if(sgvList != null && sgvList.size() == 1){

							//如果当前销售订单产品有多个，且已全部采购，则本次修改为部分采购，&& sgvList.get(0).getNum().intValue() == buyNum.intValue()
							Saleorder saleorder = new Saleorder();

							saleorder.setSaleorderId(sgvList.get(0).getSaleorderId());

							List<SaleorderGoodsVo> saleorderGoodsList = saleorderGoodsMapper.getSaleorderGoodsVoListBySaleorderId(saleorder);

							if(!CollectionUtils.isEmpty(saleorderGoodsList) && saleorder.getSaleorderId() > 0){

								saleorder.setPurchaseStatus(ErpConst.ZERO);

								for (SaleorderGoodsVo sgv : saleorderGoodsList) {

									Integer everybuyNum = rBuyorderSaleorderMapper.getBuyorderGoodsNum(sgv.getSaleorderGoodsId());//每个销售商品的采购数量
									Integer everybuyafterNum = afterSalesGoodsMapper.getBuyorderAftersaleReturnGoodsBySaleorderGoodsId(sgv.getSaleorderGoodsId());//每个销售商品对应的采购商品的采购退货数量

									if(everybuyNum - everybuyafterNum > 0 ){
										saleorder.setPurchaseStatus(1);
										break;
									}

								}

								saleorderMapper.updateByPrimaryKeySelective(saleorder);

							}

						}

					}

				}

			}


			//退货时刷新销售单的发货、收货、付款、开票状态
			//采购单商品发货收货状态
			List<SaleorderGoodsVo> slist = afterSalesGoodsMapper.getReturnGoodsList(afterSalesId);

			if(slist != null && slist.size() > 0){

				//当前售后单中采购商品和总退货数量
				List<BuyorderGoodsVo> buyAfterList = afterSalesGoodsMapper.getBuyorderAfterSalesNumByIds(slist);
				List<BuyorderGoodsVo> bgList = buyorderGoodsMapper.getBuyorderGoodsVoListByBuyorderIdNoSpecial(buyorderId);

				//商品已发货数量
				List<BuyorderGoodsVo> deliveryList = expressDetailMapper.batchBuyorderAllDeliveryNum(bgList);

				BuyorderGoods buyorderGoods = null;

				for (BuyorderGoodsVo bg : bgList) {

					//每个商品的发货数量,后面刷采购单的发货状态用
					for (BuyorderGoodsVo dbg : deliveryList) {

						if(bg.getBuyorderGoodsId().equals(dbg.getBuyorderGoodsId())){
							bg.setDeliveryNum(dbg.getDeliveryNum());
							break;
						}

					}

					//排除售后刷新采购数量
					for (BuyorderGoodsVo bgv : buyAfterList) {

						if(bg.getBuyorderGoodsId().equals(bgv.getBuyorderGoodsId()) && ErpConst.ONE.equals(bg.getArrivalStatus())){

							buyorderGoods = new BuyorderGoods();
							buyorderGoods.setBuyorderGoodsId(bg.getSaleorderGoodsId());

							if(bg.getNum() - bgv.getAfterReturnNum() <= bg.getArrivalNum() && bg.getArrivalNum() > 0){

								buyorderGoods.setArrivalStatus(ErpConst.TWO);
								bg.setArrivalStatus(ErpConst.TWO);
								bg.setNum(bg.getNum() - bgv.getAfterReturnNum());

								buyorderGoodsMapper.updateByPrimaryKeySelectiveDB(buyorderGoods);

							}

						}

					}

				}
				//订单发货状态
				if(ErpConst.ONE.equals(so.getDeliveryStatus())){

					boolean isAllDelivery = true;//默认全部发货

					for (BuyorderGoodsVo bg : bgList) {

						if((bg.getDeliveryNum() != null && bg.getNum() > bg.getDeliveryNum()) || bg.getDeliveryNum() == null){
							isAllDelivery = false;
							break;
						}

					}

					if(isAllDelivery){
						buyorder.setDeliveryStatus(ErpConst.TWO);
					}

				}

				//订单收货状态
				if(ErpConst.ONE.equals(so.getArrivalStatus())){

					boolean isAllArrival = true;//默认全部收货

					for (BuyorderGoodsVo bg : bgList) {

						if(bg.getArrivalStatus() == 1 || bg.getArrivalStatus() == 0){
							isAllArrival = false;
							break;
						}

					}

					if(isAllArrival){
						buyorder.setArrivalStatus(ErpConst.TWO);
					}

				}

				BigDecimal afterAmount = invoiceMapper.getBuyOrderAfterRecord(buyorderId);

				//录票状态
				setInvoiceStatus(buyorderId, so, buyorder, afterAmount);

				if(ErpConst.ONE.equals(so.getPaymentStatus())){

					//付款状态
					BigDecimal realAmount = buyorderDataService.getPaymentAndPeriodAmount(buyorderId);

					if(so.getTotalAmount().subtract(afterAmount).compareTo(realAmount) <= 0 && realAmount.compareTo(new BigDecimal(0)) > 0){
						buyorder.setPaymentStatus(ErpConst.TWO);
					}

				}

			}

			buyorderMapper.updateByPrimaryKeySelective(buyorder);

		}
		long end=System.currentTimeMillis();
		if((end-start)>10000){
			logger.error("事务过长"+buyorderId);
		}

	}

	@Override
	public ResultInfo dealRefundInvoiceSendStatus(Integer afterSalesId) {
		logger.info("dealRefundInvoiceSendStatus afterSalesId:{}", afterSalesId);
		if (afterSalesId == null){
			return ResultInfo.error("处理售后订单退票信息的是否需寄送问题没缺少售后订单ID");
		}

		AfterSales afterSalesParam = new AfterSales();
		afterSalesParam.setAfterSalesId(afterSalesId);
		AfterSalesVo afterSalesVo = afterSalesMapper.viewAfterSalesDetailSaleorder(afterSalesParam);
		if (afterSalesVo == null || !AfterSalesProcessEnum.AFTERSALES_TP.getCode().equals(afterSalesVo.getType())){
			logger.info("处理售后订单退票信息的是否需寄送问题售后信息不满足 afterSalesId:{}", afterSalesId);
			return ResultInfo.error("处理售后订单退票信息的是否需寄送问题售后信息不满足");
		}

		afterSalesVo.setTraderType(ErpConst.ONE);
		List<AfterSalesInvoiceVo> afterSalesInvoiceVoList = getAfterSalesInvoiceVoListByParam(afterSalesVo);
		if (CollectionUtils.isEmpty(afterSalesInvoiceVoList)){
			logger.info("处理售后订单退票信息的是否需寄送问题退票信息为空 afterSalesId:{}", afterSalesId);
			return ResultInfo.error("处理售后订单退票信息的是否需寄送问题退票信息为空");
		}
		logger.info("发票快照信息 afterSalesInvoiceVoList:{}", JSON.toJSONString(afterSalesInvoiceVoList));
		/**
		 * 1.若该发票为“纸质发票”且已寄送，此时显示为“是”
		 * 2.其他状态下显示为“否”（审核通过后自动显示）
		 */
		afterSalesInvoiceVoList.stream().filter(item ->ErpConst.ONE.equals(item.getInvoiceProperty()) &&
				item.getExpressId() != null && item.getExpressId() > 0).forEach(item -> {
					logger.info("处理售后订单退票信息的是否需寄送问题设置为已寄送 info:{}", JSON.toJSONString(item));
			AfterSalesInvoice afterSalesInvoiceParam = new AfterSalesInvoice();
			afterSalesInvoiceParam.setAfterSalesInvoiceId(item.getAfterSalesInvoiceId());
			afterSalesInvoiceParam.setIsSendInvoice(ErpConst.ONE);
			afterSalesInvoiceMapper.update(afterSalesInvoiceParam);
		});
		return ResultInfo.success();
	}

	/**
	 * 更新采购收票状态
	 *
	 * @param buyorderId
	 * @param so
	 * @param buyorder
	 * @param afterAmount
	 */
	private void setInvoiceStatus(Integer buyorderId, Buyorder so, Buyorder buyorder, BigDecimal afterAmount) {

		if(so == null || so.getInvoiceStatus() == null || so.getInvoiceStatus() != 1){
			return;
		}

		/**
		 * 采购单录票金额
		 */
		BigDecimal amount = invoiceMapper.getBuyRecordInvoiceAmount(buyorderId);

		logger.info("参数：录票金额{}，总金额{}，售后金额{}，buyOrderId:{}",afterAmount,amount,so.getTotalAmount(),afterAmount,buyorderId);

		if (amount == null || amount.compareTo(BigDecimal.ZERO) < 1){
			logger.info("采购售后单完结，采购单未进行有效录票 buyOrderId:{}", buyorderId);
			return;
		}

		if (amount.subtract(so.getTotalAmount().subtract(afterAmount)).abs().compareTo(BigDecimal.ONE) > 0){
			logger.info("采购售后单完结，采购单录票金额不符合微笑差异 buyOrderId:{}", buyorderId);
			return;
		}

		logger.info("采购售后单完结，更新采购收票状态为全部收票 buyOrderId:{}", buyorderId);
		buyorder.setInvoiceStatus(ErpConst.TWO);

	}

	@Override
	public List<AfterSalesGoodsVo> getAfterSalesDetailForWareHouseGoods(AfterSalesVo afterSales) {
		return getBuyorderAfterSalesGoodsVoListByParamNew(afterSales);
	}

	@Override
	public void updateRefund(Integer afterSalesId, Integer selectedRefundMethod) {
		AfterSalesDetailVo afterSalesDetailVo = afterSalesDetailMapper.getAfterSalesDetailByAfterSalesId(afterSalesId);
		if (Objects.nonNull(afterSalesDetailVo)) {
			AfterSalesDetail afterSalesDetail = new AfterSalesDetail();
			afterSalesDetail.setAfterSalesDetailId(afterSalesDetailVo.getAfterSalesDetailId());
			afterSalesDetail.setRefund(selectedRefundMethod);
			afterSalesDetailMapper.updateByPrimaryKeySelective(afterSalesDetail);
		}
	}
}
