<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.dao.DeliverTimeMapper">

    <select id="getDeliverTimeByAreaIdAndAreaLevel" resultType="java.lang.Integer">
        SELECT DELIVER_TIME
        FROM T_DELIVER_TIME
        WHERE AREA_ID = #{areaId,jdbcType=INTEGER} AND AREA_LEVEL = #{areaLevel,jdbcType=INTEGER} AND IS_DELETE = 0
    </select>
</mapper>