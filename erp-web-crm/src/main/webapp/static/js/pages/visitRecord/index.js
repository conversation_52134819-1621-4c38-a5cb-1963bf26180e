void function () {
    //获取来源
    let pageFrom = GLOBAL.getQuery('from');

    new Vue({
        el: '#page-container',
        data: {
            tableHeaders: [
                {
                    key: "visitRecordNo",
                    label: "拜访编号",
                    width: "130px",
                    hidden: true
                },
                {
                    key: "traderName",
                    label: "客户名称",
                    width: "220px",
                },
                {
                    key: "customerGrade",
                    label: "等级",
                    width: "46px"
                },
                {
                    key: "contactName",
                    label: "联系人",
                    width: "80px"
                },
                {
                    key: "contactMobile",
                    label: "手机",
                    width: "125px",
                    tel: true,
                },
                {
                    key: "contactTele",
                    label: "固话",
                    width: "130px",
                    tel: true,
                },
                {
                    key: "area",
                    label: "拜访地区",
                    width: "160px",
                },
                {
                    key: "visitAddress",
                    label: "详细地址",
                    width: "320px",
                },
                {
                    key: "visitTargetStr",
                    label: "拜访目标",
                    width: "140px",
                },
                {
                    key: "visitorName",
                    label: "拜访人",
                    width: "140px"
                },
                {
                    key: "planVisitDate",
                    label: "计划拜访时间",
                    width: "100px",
                    sortable: true,
                    sortKey: 'plan_visit_date',
                },
                {
                    key: "visitRecordStatus",
                    label: "拜访状态",
                    width: "70px"
                },
                {
                    key: "completeDatetime",
                    label: "拜访完成时间",
                    width: "140px",
                    sortable: true,
                    sortKey: 'complete_datetime',
                },
                {
                    key: "addUserName",
                    label: "创建人",
                    width: "100px",
                    avatar: 'addUserPic'
                },
                {
                    key: "addTime",
                    label: "创建时间",
                    width: "140px",
                    sortable: true,
                    sortKey: 'add_time',
                },
                {
                    key: "option",
                    label: "操作",
                    width: "80px",
                },
            ],
            defaultTab: [{
                label: '全部',
                id: 'all'
            }],
            searchParams: {
                customerName: '',
                visitRecordNo: '',
                visitRecordStatusList: [],
                visitorIdList: [],
                tongxingIdList: [],
                creatorIdList: [],
                planVisitDate: [],
                planVisitDateStart: '',
                planVisitDateEnd: '',
                completeDate: [],
                completeDateStart: '',
                completeDateEnd: '',
                addTimeStart: '',
                addTimeEnd: '',
                addTime: [],
                customerNature: '',
                contactName: '',
                contactMobile: '',
                contactTele: '',
                visitTargetList: [],
                bussinessChanceNo: '',
                commucateContent: ''
            },
            visitStatusList: [{
                label: '待拜访',
                value: 1,
            }, {
                label: '拜访中',
                value: 2,
            }, {
                label: '已拜访',
                value: 3,
            }, {
                label: '已关闭',
                value: 4,
            }],
            visitTypeList: [{
                label: '客情维护',
                value: 'C'
            }, {
                label: '新客开发',
                value: 'A'
            }, {
                label: '会员签约',
                value: 'D'
            }, {
                label: '产品推广',
                value: 'E'
            }, {
                label: '线索商机跟进',
                value: 'B'
            }],
            customerNatureList: [
                {
                    label: '渠道商',
                    value: 465
                },
                {
                    label: '终端',
                    value: 466
                }
            ],
            userListRemoteInfo: {
                url: '/crm/businessLeads/profile/findAllBelongUser',
                paramsType: 'url',
                paramsKey: 'name',
                parseLabel: 'username',
                parseValue: 'userId',
                parseAvatar: 'aliasHeadPicture'
            },
            visitorList: [],
            tongxingList: [],
            creatorList: [],
            isUserLoading: false,
        },
        async created() {
            let planVisitDateStart = GLOBAL.getQuery('planVisitDateStart');
            let planVisitDateEnd = GLOBAL.getQuery('planVisitDateEnd');

            if(planVisitDateStart && planVisitDateEnd) {
                this.searchParams.planVisitDateStart = planVisitDateStart;
                this.searchParams.planVisitDateEnd = planVisitDateEnd;
                this.searchParams.planVisitDate = [planVisitDateStart, planVisitDateEnd];
            } else {
                let endTime = moment(new Date()).format('YYYY-MM-DD');
                let startTime = moment(new Date().getTime() - 90 * 24 * 60 * 60 * 1000).format('YYYY-MM-DD');

                this.searchParams.addTimeEnd = endTime;
                this.searchParams.addTimeStart = startTime;
                this.searchParams.addTime = [startTime, endTime];
            }

            await this.getListUsers();
        },
        methods: {
            getListUsers() {
                this.isUserLoading = true;
                return this.$axios.post('/crm/visitrecord/m/pageDetail').then(({data}) => {
                    this.isUserLoading = false;
                    if(data.success) {
                        this.visitorList = this.parseList(data.data.visitUserList || []);
                        this.tongxingList = this.parseList(data.data.tongXingUserList || []);
                        this.creatorList = this.parseList(data.data.createUserList || []);
                    }
                })
            },
            parseList(list) {
                let resList = [];

                list.forEach(item => {
                    resList.push({
                        label: item.username,
                        value: item.userId,
                        avatar: item.aliasHeadPicture
                    })
                });

                return resList;
            },
            //日期范围选择后塞值
            handlerFilterDateChange(key, value) {
                let startKey = key + 'Start';
                let endKey = key + 'End';

                this.searchParams[startKey] = value[0] || '';
                this.searchParams[endKey] = value[1] || '';
            },
            handlerCallNumber(data) {
                GLOBAL.callNumber({
                    phone: data.phone || '',
                    traderId: data.data.traderId || 0,
                    traderType: 1,
                    callType: 7,
                    orderId: data.data.id || '',
                    traderContactId: data.data.traderContactId || 0
                })
            },
            gotoAdd() {
                if (GLOBAL.auth('C0401')) {
                    GLOBAL.link({ name: '新建拜访计划', url: '/crm/visitRecord/profile/add', multi: true })
                } else {
                    GLOBAL.showNoAuth();
                }
            },
            // 天眼查详情
            openTyc(traderName) {
                this.$refs.tycDetail.open(traderName);
            },
            getAreaStr(item) {
                let areaStrs = [];

                if (item.provinceName) {
                    areaStrs.push(item.provinceName)
                }

                if (item.cityName) {
                    areaStrs.push(item.cityName)
                }

                if (item.areaName) {
                    areaStrs.push(item.areaName)
                }

                if (areaStrs.length) {
                    return areaStrs.join('-');
                } else {
                    return '-';
                }
            },
            getVisitorUsers(item) {
                let userName = item.visitorName;

                if (item.tongXingUserNames) {
                    userName += ',' + item.tongXingUserNames;
                }

                return userName;
            }
        }
    })
}.call(this);