package com.vedeng.erp.finance.mapper;


import com.vedeng.erp.finance.domain.entity.PayApplyDefineEntity;
import com.vedeng.erp.finance.dto.PayApplyReqDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PayApplyDefineMapper {

    List<PayApplyDefineEntity> pageList(PayApplyReqDto payApplyReqDto);

    void updateOffline(@Param("payApplyId") Integer payApplyId, @Param("offline") Integer offline);
}
