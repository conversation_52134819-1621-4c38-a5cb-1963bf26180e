package com.vedeng.erp.buyorder.dto;

import java.io.Serializable;

/**
 * 采购订单修改申请
 * @TableName T_BUYORDER_MODIFY_APPLY
 */
public class BuyOrderModifyApply implements Serializable {
    /**
     * 
     */
    private Integer buyorderModifyApplyId;

    /**
     * 采购订单修改申请单号
     */
    private String buyorderModifyApplyNo;

    /**
     * 公司ID
     */
    private Integer companyId;

    /**
     * 
     */
    private Integer buyorderId;

    /**
     * 是否生效 0否 1是
     */
    private Integer validStatus;

    /**
     * 生效时间
     */
    private Long validTime;

    /**
     * 是否直发 0否 1是
     */
    private Integer deliveryDirect;

    /**
     * 收货公司ID
     */
    private Integer takeTraderId;

    /**
     * 收货公司名称
     */
    private String takeTraderName;

    /**
     * 收货联系人ID
     */
    private Integer takeTraderContactId;

    /**
     * 收货联系人名称
     */
    private String takeTraderContactName;

    /**
     * 收货联系人手机
     */
    private String takeTraderContactMobile;

    /**
     * 收货联系人电话
     */
    private String takeTraderContactTelephone;

    /**
     * 收货地址ID
     */
    private Integer takeTraderAddressId;

    /**
     * 收货地区
     */
    private String takeTraderArea;

    /**
     * 收货地址
     */
    private String takeTraderAddress;

    /**
     * 物流备注
     */
    private String logisticsComments;

    /**
     * 发票类型 字典表
     */
    private Integer invoiceType;

    /**
     * 开票备注
     */
    private String invoiceComments;

    /**
     * 原是否直发 0否 1是
     */
    private Integer oldDeliveryDirect;

    /**
     * 原收货公司ID
     */
    private Integer oldTakeTraderId;

    /**
     * 原收货公司名称
     */
    private String oldTakeTraderName;

    /**
     * 原收货联系人ID
     */
    private Integer oldTakeTraderContactId;

    /**
     * 原收货联系人名称
     */
    private String oldTakeTraderContactName;

    /**
     * 原收货联系人手机
     */
    private String oldTakeTraderContactMobile;

    /**
     * 原收货联系人电话
     */
    private String oldTakeTraderContactTelephone;

    /**
     * 原收货地址ID
     */
    private Integer oldTakeTraderAddressId;

    /**
     * 原收货地区
     */
    private String oldTakeTraderArea;

    /**
     * 原收货地址
     */
    private String oldTakeTraderAddress;

    /**
     * 原物流备注
     */
    private String oldLogisticsComments;

    /**
     * 原发票类型 字典表
     */
    private Integer oldInvoiceType;

    /**
     * 原开票备注
     */
    private String oldInvoiceComments;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 添加人
     */
    private Integer creator;

    /**
     * 是否调用WMS取消接口 0否 1是
     */
    private Integer synWmsCancel;

    /**
     * 修改发货方式的原因
     */
    private String deliveryDirectChangeReason;

    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    public Integer getBuyorderModifyApplyId() {
        return buyorderModifyApplyId;
    }

    /**
     * 
     */
    public void setBuyorderModifyApplyId(Integer buyorderModifyApplyId) {
        this.buyorderModifyApplyId = buyorderModifyApplyId;
    }

    /**
     * 采购订单修改申请单号
     */
    public String getBuyorderModifyApplyNo() {
        return buyorderModifyApplyNo;
    }

    /**
     * 采购订单修改申请单号
     */
    public void setBuyorderModifyApplyNo(String buyorderModifyApplyNo) {
        this.buyorderModifyApplyNo = buyorderModifyApplyNo;
    }

    /**
     * 公司ID
     */
    public Integer getCompanyId() {
        return companyId;
    }

    /**
     * 公司ID
     */
    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    /**
     * 
     */
    public Integer getBuyorderId() {
        return buyorderId;
    }

    /**
     * 
     */
    public void setBuyorderId(Integer buyorderId) {
        this.buyorderId = buyorderId;
    }

    /**
     * 是否生效 0否 1是
     */
    public Integer getValidStatus() {
        return validStatus;
    }

    /**
     * 是否生效 0否 1是
     */
    public void setValidStatus(Integer validStatus) {
        this.validStatus = validStatus;
    }

    /**
     * 生效时间
     */
    public Long getValidTime() {
        return validTime;
    }

    /**
     * 生效时间
     */
    public void setValidTime(Long validTime) {
        this.validTime = validTime;
    }

    /**
     * 是否直发 0否 1是
     */
    public Integer getDeliveryDirect() {
        return deliveryDirect;
    }

    /**
     * 是否直发 0否 1是
     */
    public void setDeliveryDirect(Integer deliveryDirect) {
        this.deliveryDirect = deliveryDirect;
    }

    /**
     * 收货公司ID
     */
    public Integer getTakeTraderId() {
        return takeTraderId;
    }

    /**
     * 收货公司ID
     */
    public void setTakeTraderId(Integer takeTraderId) {
        this.takeTraderId = takeTraderId;
    }

    /**
     * 收货公司名称
     */
    public String getTakeTraderName() {
        return takeTraderName;
    }

    /**
     * 收货公司名称
     */
    public void setTakeTraderName(String takeTraderName) {
        this.takeTraderName = takeTraderName;
    }

    /**
     * 收货联系人ID
     */
    public Integer getTakeTraderContactId() {
        return takeTraderContactId;
    }

    /**
     * 收货联系人ID
     */
    public void setTakeTraderContactId(Integer takeTraderContactId) {
        this.takeTraderContactId = takeTraderContactId;
    }

    /**
     * 收货联系人名称
     */
    public String getTakeTraderContactName() {
        return takeTraderContactName;
    }

    /**
     * 收货联系人名称
     */
    public void setTakeTraderContactName(String takeTraderContactName) {
        this.takeTraderContactName = takeTraderContactName;
    }

    /**
     * 收货联系人手机
     */
    public String getTakeTraderContactMobile() {
        return takeTraderContactMobile;
    }

    /**
     * 收货联系人手机
     */
    public void setTakeTraderContactMobile(String takeTraderContactMobile) {
        this.takeTraderContactMobile = takeTraderContactMobile;
    }

    /**
     * 收货联系人电话
     */
    public String getTakeTraderContactTelephone() {
        return takeTraderContactTelephone;
    }

    /**
     * 收货联系人电话
     */
    public void setTakeTraderContactTelephone(String takeTraderContactTelephone) {
        this.takeTraderContactTelephone = takeTraderContactTelephone;
    }

    /**
     * 收货地址ID
     */
    public Integer getTakeTraderAddressId() {
        return takeTraderAddressId;
    }

    /**
     * 收货地址ID
     */
    public void setTakeTraderAddressId(Integer takeTraderAddressId) {
        this.takeTraderAddressId = takeTraderAddressId;
    }

    /**
     * 收货地区
     */
    public String getTakeTraderArea() {
        return takeTraderArea;
    }

    /**
     * 收货地区
     */
    public void setTakeTraderArea(String takeTraderArea) {
        this.takeTraderArea = takeTraderArea;
    }

    /**
     * 收货地址
     */
    public String getTakeTraderAddress() {
        return takeTraderAddress;
    }

    /**
     * 收货地址
     */
    public void setTakeTraderAddress(String takeTraderAddress) {
        this.takeTraderAddress = takeTraderAddress;
    }

    /**
     * 物流备注
     */
    public String getLogisticsComments() {
        return logisticsComments;
    }

    /**
     * 物流备注
     */
    public void setLogisticsComments(String logisticsComments) {
        this.logisticsComments = logisticsComments;
    }

    /**
     * 发票类型 字典表
     */
    public Integer getInvoiceType() {
        return invoiceType;
    }

    /**
     * 发票类型 字典表
     */
    public void setInvoiceType(Integer invoiceType) {
        this.invoiceType = invoiceType;
    }

    /**
     * 开票备注
     */
    public String getInvoiceComments() {
        return invoiceComments;
    }

    /**
     * 开票备注
     */
    public void setInvoiceComments(String invoiceComments) {
        this.invoiceComments = invoiceComments;
    }

    /**
     * 原是否直发 0否 1是
     */
    public Integer getOldDeliveryDirect() {
        return oldDeliveryDirect;
    }

    /**
     * 原是否直发 0否 1是
     */
    public void setOldDeliveryDirect(Integer oldDeliveryDirect) {
        this.oldDeliveryDirect = oldDeliveryDirect;
    }

    /**
     * 原收货公司ID
     */
    public Integer getOldTakeTraderId() {
        return oldTakeTraderId;
    }

    /**
     * 原收货公司ID
     */
    public void setOldTakeTraderId(Integer oldTakeTraderId) {
        this.oldTakeTraderId = oldTakeTraderId;
    }

    /**
     * 原收货公司名称
     */
    public String getOldTakeTraderName() {
        return oldTakeTraderName;
    }

    /**
     * 原收货公司名称
     */
    public void setOldTakeTraderName(String oldTakeTraderName) {
        this.oldTakeTraderName = oldTakeTraderName;
    }

    /**
     * 原收货联系人ID
     */
    public Integer getOldTakeTraderContactId() {
        return oldTakeTraderContactId;
    }

    /**
     * 原收货联系人ID
     */
    public void setOldTakeTraderContactId(Integer oldTakeTraderContactId) {
        this.oldTakeTraderContactId = oldTakeTraderContactId;
    }

    /**
     * 原收货联系人名称
     */
    public String getOldTakeTraderContactName() {
        return oldTakeTraderContactName;
    }

    /**
     * 原收货联系人名称
     */
    public void setOldTakeTraderContactName(String oldTakeTraderContactName) {
        this.oldTakeTraderContactName = oldTakeTraderContactName;
    }

    /**
     * 原收货联系人手机
     */
    public String getOldTakeTraderContactMobile() {
        return oldTakeTraderContactMobile;
    }

    /**
     * 原收货联系人手机
     */
    public void setOldTakeTraderContactMobile(String oldTakeTraderContactMobile) {
        this.oldTakeTraderContactMobile = oldTakeTraderContactMobile;
    }

    /**
     * 原收货联系人电话
     */
    public String getOldTakeTraderContactTelephone() {
        return oldTakeTraderContactTelephone;
    }

    /**
     * 原收货联系人电话
     */
    public void setOldTakeTraderContactTelephone(String oldTakeTraderContactTelephone) {
        this.oldTakeTraderContactTelephone = oldTakeTraderContactTelephone;
    }

    /**
     * 原收货地址ID
     */
    public Integer getOldTakeTraderAddressId() {
        return oldTakeTraderAddressId;
    }

    /**
     * 原收货地址ID
     */
    public void setOldTakeTraderAddressId(Integer oldTakeTraderAddressId) {
        this.oldTakeTraderAddressId = oldTakeTraderAddressId;
    }

    /**
     * 原收货地区
     */
    public String getOldTakeTraderArea() {
        return oldTakeTraderArea;
    }

    /**
     * 原收货地区
     */
    public void setOldTakeTraderArea(String oldTakeTraderArea) {
        this.oldTakeTraderArea = oldTakeTraderArea;
    }

    /**
     * 原收货地址
     */
    public String getOldTakeTraderAddress() {
        return oldTakeTraderAddress;
    }

    /**
     * 原收货地址
     */
    public void setOldTakeTraderAddress(String oldTakeTraderAddress) {
        this.oldTakeTraderAddress = oldTakeTraderAddress;
    }

    /**
     * 原物流备注
     */
    public String getOldLogisticsComments() {
        return oldLogisticsComments;
    }

    /**
     * 原物流备注
     */
    public void setOldLogisticsComments(String oldLogisticsComments) {
        this.oldLogisticsComments = oldLogisticsComments;
    }

    /**
     * 原发票类型 字典表
     */
    public Integer getOldInvoiceType() {
        return oldInvoiceType;
    }

    /**
     * 原发票类型 字典表
     */
    public void setOldInvoiceType(Integer oldInvoiceType) {
        this.oldInvoiceType = oldInvoiceType;
    }

    /**
     * 原开票备注
     */
    public String getOldInvoiceComments() {
        return oldInvoiceComments;
    }

    /**
     * 原开票备注
     */
    public void setOldInvoiceComments(String oldInvoiceComments) {
        this.oldInvoiceComments = oldInvoiceComments;
    }

    /**
     * 添加时间
     */
    public Long getAddTime() {
        return addTime;
    }

    /**
     * 添加时间
     */
    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    /**
     * 添加人
     */
    public Integer getCreator() {
        return creator;
    }

    /**
     * 添加人
     */
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**
     * 是否调用WMS取消接口 0否 1是
     */
    public Integer getSynWmsCancel() {
        return synWmsCancel;
    }

    /**
     * 是否调用WMS取消接口 0否 1是
     */
    public void setSynWmsCancel(Integer synWmsCancel) {
        this.synWmsCancel = synWmsCancel;
    }

    /**
     * 修改发货方式的原因
     */
    public String getDeliveryDirectChangeReason() {
        return deliveryDirectChangeReason;
    }

    /**
     * 修改发货方式的原因
     */
    public void setDeliveryDirectChangeReason(String deliveryDirectChangeReason) {
        this.deliveryDirectChangeReason = deliveryDirectChangeReason;
    }

    private Integer isNew;

    public Integer getIsNew() {
        return isNew;
    }

    public void setIsNew(Integer isNew) {
        this.isNew = isNew;
    }
}