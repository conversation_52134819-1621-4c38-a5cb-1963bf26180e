package com.vedeng.erp.trader.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.authorization.model.User;
import com.vedeng.authorization.service.AuthService;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.trace.data.TrackParamsData;
import com.vedeng.common.trace.enums.EventTrackingEnum;
import com.vedeng.common.trace.track.TrackStrategy;
import com.vedeng.common.trace.track.factory.TrackStrategyFactory;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.trader.common.enums.TagChangeRecordOperTypeEnum;
import com.vedeng.erp.trader.common.enums.TraderCustomerMarketingPrincipalEnum;
import com.vedeng.erp.trader.common.enums.TraderInstitutionNatureEnums;
import com.vedeng.erp.trader.domain.dto.DiffDto;
import com.vedeng.erp.trader.domain.entity.TraderCustomerTagChangeRecordEntity;
import com.vedeng.erp.trader.dto.TraderCustomerMarketingNodeDto;
import com.vedeng.erp.trader.dto.TraderCustomerMarketingPrincipalDto;
import com.vedeng.erp.trader.dto.TraderCustomerMarketingTerminalDto;
import com.vedeng.erp.trader.dto.TraderCustomerTagChangeRecordDto;
import com.vedeng.erp.trader.mapper.TraderCustomerTagChangeRecordMapper;
import com.vedeng.erp.trader.mapstruct.TraderCustomerTagChangeRecordConvertor;
import com.vedeng.erp.trader.service.TraderCustomerMarketingNodeApiService;
import com.vedeng.erp.trader.service.TraderCustomerTagChangeRecordService;
import com.vedeng.goods.dto.BrandFrontDto;
import com.vedeng.goods.service.BaseCategoryApiService;
import com.vedeng.goods.service.BrandApiService;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/17 15:43
 **/
@Service
@Slf4j
public class TraderCustomerTagChangeRecordServiceImpl implements TraderCustomerTagChangeRecordService {


    @Autowired
    private BaseCategoryApiService baseCategoryApiService;

    @Autowired
    private BrandApiService brandApiService;

    @Autowired
    private TraderCustomerTagChangeRecordMapper traderCustomerTagChangeRecordMapper;

    @Autowired
    private TraderCustomerTagChangeRecordConvertor traderCustomerTagChangeRecordConvertor;

    @Autowired
    private TraderCustomerMarketingNodeApiService traderCustomerMarketingNodeApiService;
    
    @Autowired
    private TrackStrategyFactory trackStrategyFactory;
    
    @Autowired
    private UserApiService userApiService;


    /**
     * 编辑模板
     */
    private final String updateTemple = "{}:{}{},{}{};";

    /**
     * 新增删除模板
     */
    private final String addOrDelTemple = "{}:{}{};";


    List<TraderCustomerMarketingNodeDto> traderCustomerMarketList;

    Map<Integer, String> threeCustomerTypeMap;

    Map<String, String> institutionTypeMap;

    Map<String, String> institutionLevelMap;

    Map<String, String> institutionTypeChildMap;


    @PostConstruct
    public void initNode() {
        traderCustomerMarketList = traderCustomerMarketingNodeApiService.getTraderCustomerMarketList(6, false);

        threeCustomerTypeMap = traderCustomerMarketList.stream()
                .map(TraderCustomerMarketingNodeDto::getThreeCustomerType)
                .collect(Collectors.toMap(x -> Integer.parseInt(x.getLabel()), TraderCustomerMarketingNodeDto.Node::getValue, (k1, k2) -> k1));
        institutionTypeMap = traderCustomerMarketList.stream()
                .map(TraderCustomerMarketingNodeDto::getInstitutionType)
                .flatMap(List::stream)
                .collect(Collectors.toMap(TraderCustomerMarketingNodeDto.Node::getLabel, TraderCustomerMarketingNodeDto.Node::getValue, (k1, k2) -> k1));
        institutionLevelMap = traderCustomerMarketList.stream()
                .map(TraderCustomerMarketingNodeDto::getInstitutionLevel)
                .flatMap(List::stream)
                .collect(Collectors.toMap(TraderCustomerMarketingNodeDto.Node::getLabel, TraderCustomerMarketingNodeDto.Node::getValue, (k1, k2) -> k1));
        institutionTypeChildMap = traderCustomerMarketList.stream()
                .map(TraderCustomerMarketingNodeDto::getInstitutionType)
                .flatMap(List::stream)
                .map(TraderCustomerMarketingNodeDto.Node::getChildren).filter(CollUtil::isNotEmpty)
                .flatMap(List::stream)
                .collect(Collectors.toMap(TraderCustomerMarketingNodeDto.Node::getLabel, TraderCustomerMarketingNodeDto.Node::getValue, (k1, k2) -> k1));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void calcTraderCustomerMarketingPrincipalDiffAndSave(Integer saveOrUpdate, TraderCustomerMarketingPrincipalDto oldData, TraderCustomerMarketingPrincipalDto newData) {

        log.info("calcTraderCustomerMarketingPrincipalDiffAndSave oldData:{},newData:{}", JSON.toJSONString(oldData), JSON.toJSONString(newData));
        List<DiffDto> diffDtoList = bindDiffDto(oldData, newData);
        if (CollUtil.isEmpty(diffDtoList)) {
            return;
        }
        List<TraderCustomerTagChangeRecordEntity> traderCustomerTagChangeRecordEntities = null;
        List<TraderCustomerTagChangeRecordDto> recordDtoList = bindRecord(diffDtoList,0);
        if (CollUtil.isNotEmpty(recordDtoList)) {
            traderCustomerTagChangeRecordEntities = traderCustomerTagChangeRecordConvertor.toEntity(recordDtoList);
            log.info("calcTraderCustomerMarketingPrincipalDiffAndSave save:{}",JSON.toJSONString(traderCustomerTagChangeRecordEntities));
            traderCustomerTagChangeRecordEntities.forEach(x -> traderCustomerTagChangeRecordMapper.insertSelective(x));
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void calcTraderCustomerMarketingTerminalDiffAndSave(Integer saveOrUpdate, List<TraderCustomerMarketingTerminalDto> oldData, List<TraderCustomerMarketingTerminalDto> newData, Integer source) {

        log.info("calcTraderCustomerMarketingTerminalDiffAndSave oldData:{},newData:{}", JSON.toJSONString(oldData), JSON.toJSONString(newData));
        List<DiffDto> diffDtoList = bindDiffDto(oldData, newData);
        if (CollUtil.isEmpty(diffDtoList)) {
            return;
        }
        List<TraderCustomerTagChangeRecordDto> recordDtoList = bindRecord(diffDtoList, source);
        List<TraderCustomerTagChangeRecordEntity> traderCustomerTagChangeRecordEntities = null;
        if (CollUtil.isNotEmpty(recordDtoList)) {
            traderCustomerTagChangeRecordEntities = traderCustomerTagChangeRecordConvertor.toEntity(recordDtoList);
            log.info("calcTraderCustomerMarketingTerminalDiffAndSave save:{}", JSON.toJSONString(traderCustomerTagChangeRecordEntities));
            traderCustomerTagChangeRecordEntities.forEach(x -> traderCustomerTagChangeRecordMapper.insertSelective(x));
        }
    }
    
    @Override
    public List<List<TraderCustomerTagChangeRecordDto>> queryByTraderCustomerId(Integer traderCustomerId) {

        if (Objects.isNull(traderCustomerId)) {
            return Collections.emptyList();
        }
        List<TraderCustomerTagChangeRecordEntity> traderCustomerTagChangeRecordEntities = traderCustomerTagChangeRecordMapper.selectByTraderCustomerId(traderCustomerId);
        List<TraderCustomerTagChangeRecordDto> recordDtos = traderCustomerTagChangeRecordConvertor.toDto(traderCustomerTagChangeRecordEntities);
        Map<String, List<TraderCustomerTagChangeRecordDto>> collect = recordDtos.stream().sorted(Comparator.comparing(x -> x.getOperTime().getTime()))
                .collect(Collectors.groupingBy(x -> String.valueOf(x.getOperTime().getTime())+x.getSource(),LinkedHashMap::new,Collectors.toList()));
        return new ArrayList<>(collect.values());
    }


    @Override
    public void batchInsertTraderCustomerTagChangeRecordEntity(List<TraderCustomerTagChangeRecordDto> records) {
        List<TraderCustomerTagChangeRecordEntity> traderCustomerTagChangeRecordEntities = traderCustomerTagChangeRecordConvertor.toEntity(records);
         traderCustomerTagChangeRecordMapper.batchInsert(traderCustomerTagChangeRecordEntities);
    }

    /**
     * 设置操作类型
     *
     * @param data     数据
     * @param oldValue 原值
     * @param newValue 新值
     */
    private void setOperType(TraderCustomerTagChangeRecordDto data, String oldValue, String newValue) {

        // 判断具体的操作
        if (StrUtil.isEmpty(oldValue) && StrUtil.isNotEmpty(newValue)) {
            data.setOperType(TagChangeRecordOperTypeEnum.ADD.getType());
        }
        if (StrUtil.isNotEmpty(oldValue) && StrUtil.isEmpty(newValue)) {
            data.setOperType(TagChangeRecordOperTypeEnum.DELETE.getType());
        }
        if (StrUtil.isNotEmpty(oldValue) && StrUtil.isNotEmpty(newValue)) {
            data.setOperType(TagChangeRecordOperTypeEnum.UPDATE.getType());
        }

    }

    /**
     * 分装对象
     *
     * @param diffDtoList 比较对象
     * @return List<TraderCustomerTagChangeRecordDto>
     */
    private List<TraderCustomerTagChangeRecordDto> bindRecord(List<DiffDto> diffDtoList,Integer source) {
        Date date = new Date();
        return diffDtoList.stream().map(x -> {
            String oldValue = StrUtil.isEmpty(x.getOldValue()) ? "" : x.getOldValue();
            String newValue = StrUtil.isEmpty(x.getNewValue()) ? "" : x.getNewValue();

            if (oldValue.equals(newValue)) {
                return null;
            }
            TraderCustomerTagChangeRecordDto data = new TraderCustomerTagChangeRecordDto();
            setOperType(data, oldValue, newValue);

            data.setSource(source);
            data.setTagModelName(x.getTagModel());
            data.setTagName(x.getTag());
            data.setOldTagLabel(oldValue);
            data.setNewTagLabel(newValue);
            data.setDelete(oldValue);
            data.setAdd(newValue);
            data.setTraderCustomerId(x.getTraderCustomerId());
            data.setTraderId(x.getTraderId());
            data.setOperTime(date);

            // 集合的处理
            if (x.isNeedConvertorCollection() && TagChangeRecordOperTypeEnum.UPDATE.getType().equals(data.getOperType())) {

                List<String> oldList = StrUtil.split(oldValue, StrUtil.COMMA);
                List<String> newList = StrUtil.split(newValue, StrUtil.COMMA);

                String delete = oldList.stream().filter(a -> !newList.contains(a)).collect(Collectors.joining(StrUtil.COMMA));
                String add = newList.stream().filter(a -> !oldList.contains(a)).collect(Collectors.joining(StrUtil.COMMA));
                data.setDelete(delete);
                data.setAdd(add);
                if (StrUtil.isEmpty(delete)) {
                    data.setOperType(TagChangeRecordOperTypeEnum.ADD.getType());
                }
                if (StrUtil.isEmpty(add)) {
                    data.setOperType(TagChangeRecordOperTypeEnum.DELETE.getType());
                }
                // 都相同但是顺序不同
                if (StrUtil.isEmpty(delete) && StrUtil.isEmpty(add)) {
                    return null;
                }
            }

            // 调用自定义函数 计算描述文字
            data.setTagChangeLog(x.getMethod().apply(data));
            return data;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }


    /**
     * 构建比较对象
     *
     * @param oldData 原始值
     * @param newData 新值
     * @return List<DiffDto>
     */
    public List<DiffDto> bindDiffDto(List<TraderCustomerMarketingTerminalDto> oldData, List<TraderCustomerMarketingTerminalDto> newData) {

        if (CollUtil.isEmpty(oldData) && CollUtil.isEmpty(newData)) {
            return Collections.emptyList();
        }

        List<DiffDto> result = new ArrayList<>();

        oldData = CollUtil.isEmpty(oldData) ? Collections.emptyList() : oldData;
        newData = CollUtil.isEmpty(newData) ? Collections.emptyList() : newData;

        Integer traderId = CollUtil.isNotEmpty(oldData) ? oldData.get(0).getTraderId() : newData.get(0).getTraderId();
        Integer traderCustomerId = CollUtil.isNotEmpty(oldData) ? oldData.get(0).getTraderCustomerId() : newData.get(0).getTraderCustomerId();


        List<TraderCustomerMarketingTerminalDto> finalOldData = oldData;
        List<TraderCustomerMarketingTerminalDto> finalNewData = newData;
        threeCustomerTypeMap.forEach((k, v) -> {

            Optional<TraderCustomerMarketingTerminalDto> newFirst = finalNewData.stream().filter(x -> k.equals(x.getTraderCustomerMarketingType())).findFirst();
            Optional<TraderCustomerMarketingTerminalDto> oldFirst = finalOldData.stream().filter(x -> k.equals(x.getTraderCustomerMarketingType())).findFirst();

            // 机构类型
            String oldValue = oldFirst.isPresent() ? oldFirst.get().getInstitutionType() : "";
            String newValue = newFirst.isPresent() ? newFirst.get().getInstitutionType() : "";
            DiffDto institutionType = createDiffDto(true, traderId, traderCustomerId, v, oldValue, newValue, "机构类型", getInstitutionFunction(institutionTypeMap));
            result.add(institutionType);

            // 专科类型
            String oldValue1 = oldFirst.isPresent() ? oldFirst.get().getInstitutionTypeChild() : "";
            String newValue1 = newFirst.isPresent() ? newFirst.get().getInstitutionTypeChild() : "";
            DiffDto institutionTypeChild = createDiffDto(true, traderId, traderCustomerId, v, oldValue1, newValue1, "专科类型", getInstitutionFunction(institutionTypeChildMap));
            result.add(institutionTypeChild);


            // 机构评级
            String oldValue2 = oldFirst.isPresent() ? oldFirst.get().getInstitutionLevel() : "";
            String newValue2 = newFirst.isPresent() ? newFirst.get().getInstitutionLevel() : "";
            DiffDto institutionLevel = createDiffDto(true, traderId, traderCustomerId, v, oldValue2, newValue2, "机构评级", getInstitutionFunction(institutionLevelMap));
            result.add(institutionLevel);

            // 机构性质
            String oldValue3 = oldFirst.isPresent() ? oldFirst.get().getInstitutionNature() : "";
            String newValue3 = newFirst.isPresent() ? newFirst.get().getInstitutionNature() : "";
            DiffDto institutionNature = createDiffDto(true, traderId, traderCustomerId, v, oldValue3, newValue3, "机构性质", getInstitutionNatureFunction());
            result.add(institutionNature);

            // 其他机构类型
            String oldValue4 = oldFirst.isPresent() ? oldFirst.get().getOtherInstitutionType() : "";
            String newValue4 = newFirst.isPresent() ? newFirst.get().getOtherInstitutionType() : "";
            DiffDto otherInstitutionType = createDiffDto(false, traderId, traderCustomerId, v, oldValue4, newValue4, "其他机构类型", getOtherInstitutionType());
            result.add(otherInstitutionType);
        });

        return result;
    }


    /**
     * 函数
     *
     * @param needConvertorCollection 是否是集合判断
     * @param traderId                客户id
     * @param traderCustomerId        客户customer id
     * @param tagModel                一级名
     * @param oldValue                原值
     * @param newValue                新值
     * @param tag                     二级名
     * @param method                  计算方法
     * @return DiffDto
     */
    private DiffDto createDiffDto(boolean needConvertorCollection, Integer traderId, Integer traderCustomerId, String tagModel, String oldValue, String newValue, String tag, Function<TraderCustomerTagChangeRecordDto, String> method) {
        return DiffDto.builder()
                .needConvertorCollection(needConvertorCollection)
                .oldValue(oldValue)
                .newValue(newValue)
                .traderId(traderId)
                .traderCustomerId(traderCustomerId)
                .tagModel(tagModel).tag(tag)
                .method(method).build();
    }


    /**
     * 其他机构类型值判断
     * @return Function<TraderCustomerTagChangeRecordDto, String>
     */
    private Function<TraderCustomerTagChangeRecordDto, String> getOtherInstitutionType() {
        return (x) -> {
            if (TagChangeRecordOperTypeEnum.ADD.getType().equals(x.getOperType())) {

                return StrUtil.format(addOrDelTemple, x.getTagName(), TagChangeRecordOperTypeEnum.ADD.getDesc(), x.getNewTagLabel());
            }

            if (TagChangeRecordOperTypeEnum.DELETE.getType().equals(x.getOperType())) {

                return StrUtil.format(addOrDelTemple, x.getTagName(), TagChangeRecordOperTypeEnum.DELETE.getDesc(), x.getOldTagLabel());
            }

            if (TagChangeRecordOperTypeEnum.UPDATE.getType().equals(x.getOperType())) {

                return StrUtil.format(updateTemple, x.getTagName(), TagChangeRecordOperTypeEnum.DELETE.getDesc(), x.getOldTagLabel(), TagChangeRecordOperTypeEnum.ADD.getDesc(),  x.getNewTagLabel());
            }
            return "";
        };
    }
    /**
     * 机构性质 计算函数
     *
     * @return Function<TraderCustomerTagChangeRecordDto, String>
     */
    private Function<TraderCustomerTagChangeRecordDto, String> getInstitutionNatureFunction() {
        return (x) -> {

            if (TagChangeRecordOperTypeEnum.ADD.getType().equals(x.getOperType())) {
                List<String> splitAdd = StrUtil.split(x.getAdd(), StrUtil.COMMA);
                String string = splitAdd.stream().map(TraderInstitutionNatureEnums::getEnumName).collect(Collectors.joining(StrUtil.COMMA));

                return StrUtil.format(addOrDelTemple, x.getTagName(), TagChangeRecordOperTypeEnum.ADD.getDesc(), string);
            }

            if (TagChangeRecordOperTypeEnum.DELETE.getType().equals(x.getOperType())) {
                List<String> splitDele = StrUtil.split(x.getDelete(), StrUtil.COMMA);
                String string = splitDele.stream().map(TraderInstitutionNatureEnums::getEnumName).collect(Collectors.joining(StrUtil.COMMA));

                return StrUtil.format(addOrDelTemple, x.getTagName(), TagChangeRecordOperTypeEnum.DELETE.getDesc(), string);
            }

            if (TagChangeRecordOperTypeEnum.UPDATE.getType().equals(x.getOperType())) {

                List<String> splitAdd = StrUtil.split(x.getAdd(), StrUtil.COMMA);
                List<String> splitDele = StrUtil.split(x.getDelete(), StrUtil.COMMA);

                String delString = splitDele.stream().map(TraderInstitutionNatureEnums::getEnumName).collect(Collectors.joining(StrUtil.COMMA));
                String addString = splitAdd.stream().map(TraderInstitutionNatureEnums::getEnumName).collect(Collectors.joining(StrUtil.COMMA));

                return StrUtil.format(updateTemple, x.getTagName(), TagChangeRecordOperTypeEnum.DELETE.getDesc(), delString, TagChangeRecordOperTypeEnum.ADD.getDesc(), addString);
            }
            return "";
        };
    }

    /**
     * 计算函数
     * Terminal 相关
     * @param stringMap 入参
     * @return Function<TraderCustomerTagChangeRecordDto, String>
     */
    private Function<TraderCustomerTagChangeRecordDto, String> getInstitutionFunction(Map<String, String> stringMap) {
        return (x) -> {

            // 根据类型组合
            if (TagChangeRecordOperTypeEnum.ADD.getType().equals(x.getOperType())) {

                List<Integer> splitAdd = StrUtil.split(x.getAdd(), StrUtil.COMMA).stream().filter(StringUtils::isNotBlank).map(Integer::valueOf).collect(Collectors.toList());
                String string = splitAdd.stream().map(a -> Optional.ofNullable(stringMap.get(a.toString())).orElse("其他")).collect(Collectors.joining(StrUtil.COMMA));
                return StrUtil.format(addOrDelTemple, x.getTagName(), TagChangeRecordOperTypeEnum.ADD.getDesc(), string);
            }

            if (TagChangeRecordOperTypeEnum.DELETE.getType().equals(x.getOperType())) {
                List<Integer> splitDel = StrUtil.split(x.getDelete(), StrUtil.COMMA).stream().filter(StringUtils::isNotBlank).map(Integer::valueOf).collect(Collectors.toList());
                String string = splitDel.stream().map(a -> Optional.ofNullable(stringMap.get(a.toString())).orElse("其他")).collect(Collectors.joining(StrUtil.COMMA));
                return StrUtil.format(addOrDelTemple, x.getTagName(), TagChangeRecordOperTypeEnum.DELETE.getDesc(), string);
            }

            if (TagChangeRecordOperTypeEnum.UPDATE.getType().equals(x.getOperType())) {
                List<Integer> splitAdd = StrUtil.split(x.getAdd(), StrUtil.COMMA).stream().filter(StringUtils::isNotBlank).map(Integer::valueOf).collect(Collectors.toList());
                List<Integer> splitDel = StrUtil.split(x.getDelete(), StrUtil.COMMA).stream().filter(StringUtils::isNotBlank).map(Integer::valueOf).collect(Collectors.toList());
                String delString = splitDel.stream().map(a -> Optional.ofNullable(stringMap.get(a.toString())).orElse("其他")).collect(Collectors.joining(StrUtil.COMMA));

                String addString = splitAdd.stream().map(a -> Optional.ofNullable(stringMap.get(a.toString())).orElse("其他")).collect(Collectors.joining(StrUtil.COMMA));

                return StrUtil.format(updateTemple, x.getTagName(), TagChangeRecordOperTypeEnum.DELETE.getDesc(), delString, TagChangeRecordOperTypeEnum.ADD.getDesc(), addString);
            }
            return "";
        };
    }


    /**
     * 绑定比较对象
     *
     * @param oldData 原值
     * @param newData 新值
     * @return List<DiffDto>
     */
    public List<DiffDto> bindDiffDto(TraderCustomerMarketingPrincipalDto oldData, TraderCustomerMarketingPrincipalDto newData) {

        List<DiffDto> result = new ArrayList<>();

        if (Objects.isNull(oldData) && Objects.isNull(newData)) {
            return Collections.emptyList();
        }

        oldData = Optional.ofNullable(oldData).orElse(new TraderCustomerMarketingPrincipalDto());
        newData = Optional.ofNullable(newData).orElse(new TraderCustomerMarketingPrincipalDto());

        Integer traderId = Objects.nonNull(oldData.getTraderId()) ? oldData.getTraderId() : newData.getTraderId();
        Integer traderCustomerId = Objects.nonNull(oldData.getTraderCustomerId()) ? oldData.getTraderCustomerId() : newData.getTraderCustomerId();

        // 商品类型
        DiffDto buildSkuType = createDiffDto(true, traderId, traderCustomerId, "主营信息", oldData.getSkuType(), newData.getSkuType(), "商品类型", getSimpleEnumFunction("SKU_TYPE"));
        result.add(buildSkuType);
        // 商品范畴
        DiffDto buildSkuScope = createDiffDto(false, traderId, traderCustomerId, "主营信息", oldData.getSkuScope(), newData.getSkuScope(), "商品范畴", getSimpleEnumFunction("SKU_SCOPE"));
        result.add(buildSkuScope);
        // 商品分类
        DiffDto buildSkuCategory = createDiffDto(true, traderId, traderCustomerId, "主营信息", oldData.getSkuCategory(), newData.getSkuCategory(), "商品分类", getSkuCategoryFunction());
        result.add(buildSkuCategory);
        // 销售类别
        DiffDto buildSalesType = createDiffDto(false, traderId, traderCustomerId, "主营信息", oldData.getSalesType(), newData.getSalesType(), "销售类别", getSimpleEnumFunction("SALES_TYPE"));
        result.add(buildSalesType);
        // 代理品牌
        DiffDto buildAgencyBrand = createDiffDto(true, traderId, traderCustomerId, "主营信息", oldData.getAgencyBrand(), newData.getAgencyBrand(), "代理品牌", getAgencyBrandFunction());
        result.add(buildAgencyBrand);
        // 政府关系
        DiffDto buildGovernmentRelation = createDiffDto(true, traderId, traderCustomerId, "主营信息", oldData.getGovernmentRelation(), newData.getGovernmentRelation(), "政府关系", getSimpleEnumFunction("GOVERNMENT_RELATION"));
        result.add(buildGovernmentRelation);

        return result;
    }

    /**
     * 商品分类
     *
     * @return Function<TraderCustomerTagChangeRecordDto, String>
     */
    private Function<TraderCustomerTagChangeRecordDto, String> getSkuCategoryFunction() {
        return (x) -> {



            // 数据库查值
            if (TagChangeRecordOperTypeEnum.ADD.getType().equals(x.getOperType())) {
                List<Integer> splitAdd = StrUtil.split(x.getAdd(), StrUtil.COMMA).stream().map(Integer::valueOf).collect(Collectors.toList());
                String categoryNameByList = baseCategoryApiService.getCategoryNameByList(splitAdd);

                return StrUtil.format(addOrDelTemple, x.getTagName(), TagChangeRecordOperTypeEnum.ADD.getDesc(), categoryNameByList);
            }

            if (TagChangeRecordOperTypeEnum.DELETE.getType().equals(x.getOperType())) {
                List<Integer> splitDel = StrUtil.split(x.getDelete(), StrUtil.COMMA).stream().map(Integer::valueOf).collect(Collectors.toList());
                String categoryNameByList = baseCategoryApiService.getCategoryNameByList(splitDel);

                return StrUtil.format(addOrDelTemple, x.getTagName(), TagChangeRecordOperTypeEnum.DELETE.getDesc(), categoryNameByList);
            }

            if (TagChangeRecordOperTypeEnum.UPDATE.getType().equals(x.getOperType())) {
                List<Integer> splitAdd = StrUtil.split(x.getAdd(), StrUtil.COMMA).stream().map(Integer::valueOf).collect(Collectors.toList());
                List<Integer> splitDel = StrUtil.split(x.getDelete(), StrUtil.COMMA).stream().map(Integer::valueOf).collect(Collectors.toList());
                String categoryNameByListDel = baseCategoryApiService.getCategoryNameByList(splitDel);
                String categoryNameByListAdd = baseCategoryApiService.getCategoryNameByList(splitAdd);

                return StrUtil.format(updateTemple, x.getTagName(), TagChangeRecordOperTypeEnum.DELETE.getDesc(), categoryNameByListDel, TagChangeRecordOperTypeEnum.ADD.getDesc(), categoryNameByListAdd);
            }
            return "";
        };
    }

    /**
     * 代理品牌
     *
     * @return Function<TraderCustomerTagChangeRecordDto, String>
     */
    private Function<TraderCustomerTagChangeRecordDto, String> getAgencyBrandFunction() {
        return (x) -> {
            // 数据库查值
            if (TagChangeRecordOperTypeEnum.ADD.getType().equals(x.getOperType())) {
                List<Integer> add = StrUtil.split(x.getAdd(), StrUtil.COMMA).stream().map(Integer::valueOf).collect(Collectors.toList());
                List<BrandFrontDto> brand = brandApiService.getBrand(add);
                String brandStr = brand.stream().map(BrandFrontDto::getBrandName).filter(StrUtil::isNotEmpty).collect(Collectors.joining(StrUtil.COMMA));

                return StrUtil.format(addOrDelTemple, x.getTagName(), TagChangeRecordOperTypeEnum.ADD.getDesc(), brandStr);
            }

            if (TagChangeRecordOperTypeEnum.DELETE.getType().equals(x.getOperType())) {
                List<Integer> delete = StrUtil.split(x.getDelete(), StrUtil.COMMA).stream().map(Integer::valueOf).collect(Collectors.toList());
                List<BrandFrontDto> brand = brandApiService.getBrand(delete);
                String brandStr = brand.stream().map(BrandFrontDto::getBrandName).filter(StrUtil::isNotEmpty).collect(Collectors.joining(StrUtil.COMMA));

                return StrUtil.format(addOrDelTemple, x.getTagName(), TagChangeRecordOperTypeEnum.DELETE.getDesc(), brandStr);
            }

            if (TagChangeRecordOperTypeEnum.UPDATE.getType().equals(x.getOperType())) {
                List<Integer> add = StrUtil.split(x.getAdd(), StrUtil.COMMA).stream().map(Integer::valueOf).collect(Collectors.toList());
                List<Integer> delete = StrUtil.split(x.getDelete(), StrUtil.COMMA).stream().map(Integer::valueOf).collect(Collectors.toList());
                List<BrandFrontDto> brand = brandApiService.getBrand(delete);
                String brandStrDel = brand.stream().map(BrandFrontDto::getBrandName).filter(StrUtil::isNotEmpty).collect(Collectors.joining(StrUtil.COMMA));

                List<BrandFrontDto> brandAdd = brandApiService.getBrand(add);
                String brandStrAdd = brandAdd.stream().map(BrandFrontDto::getBrandName).filter(StrUtil::isNotEmpty).collect(Collectors.joining(StrUtil.COMMA));

                return StrUtil.format(updateTemple, x.getTagName(), TagChangeRecordOperTypeEnum.DELETE.getDesc(), brandStrDel, TagChangeRecordOperTypeEnum.ADD.getDesc(), brandStrAdd);
            }
            return "";
        };
    }

    /**
     * 计算函数
     * Principal 相关计算
     * @param enumCode code
     * @return Function<TraderCustomerTagChangeRecordDto, String>
     */
    private Function<TraderCustomerTagChangeRecordDto, String> getSimpleEnumFunction(String enumCode) {
        return (x) -> {
            // 枚举类取值
            if (TagChangeRecordOperTypeEnum.ADD.getType().equals(x.getOperType())) {
                String desc = StrUtil.split(x.getAdd(), StrUtil.COMMA).stream()
                        .map(a -> TraderCustomerMarketingPrincipalEnum.getEnumName(enumCode,  StrUtil.isNotEmpty(a)?Integer.parseInt(a):-100))
                        .collect(Collectors.joining(StrUtil.COMMA));

                return StrUtil.format(addOrDelTemple, x.getTagName(), TagChangeRecordOperTypeEnum.ADD.getDesc(), desc);
            }

            if (TagChangeRecordOperTypeEnum.DELETE.getType().equals(x.getOperType())) {
                String desc = StrUtil.split(x.getDelete(), StrUtil.COMMA).stream()
                        .map(a -> TraderCustomerMarketingPrincipalEnum.getEnumName(enumCode, StrUtil.isNotEmpty(a)?Integer.parseInt(a):-100))
                        .collect(Collectors.joining(StrUtil.COMMA));

                return StrUtil.format(addOrDelTemple, x.getTagName(), TagChangeRecordOperTypeEnum.DELETE.getDesc(), desc);
            }

            if (TagChangeRecordOperTypeEnum.UPDATE.getType().equals(x.getOperType())) {

                String delDesc = StrUtil.split(x.getDelete(), StrUtil.COMMA).stream()
                        .map(a -> TraderCustomerMarketingPrincipalEnum.getEnumName(enumCode,  StrUtil.isNotEmpty(a)?Integer.parseInt(a):-100))
                        .collect(Collectors.joining(StrUtil.COMMA));

                String addDesc = StrUtil.split(x.getDelete(), StrUtil.COMMA).stream()
                        .map(a -> TraderCustomerMarketingPrincipalEnum.getEnumName(enumCode, StrUtil.isNotEmpty(a)?Integer.parseInt(a):-100))
                        .collect(Collectors.joining(StrUtil.COMMA));

                return StrUtil.format(updateTemple, x.getTagName(), TagChangeRecordOperTypeEnum.DELETE.getDesc(), delDesc, TagChangeRecordOperTypeEnum.ADD.getDesc(), addDesc);
            }
            return "";
        };
    }


}
