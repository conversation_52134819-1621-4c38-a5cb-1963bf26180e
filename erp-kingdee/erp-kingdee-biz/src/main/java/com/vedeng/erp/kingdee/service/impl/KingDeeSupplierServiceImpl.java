package com.vedeng.erp.kingdee.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.repository.BatchSupplierFinanceDtoMapper;
import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeeSupplierCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeSupplierEntity;
import com.vedeng.erp.kingdee.dto.KingDeeMaterialDto;
import com.vedeng.erp.kingdee.dto.KingDeeSupplierDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeSupplierCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeSupplierConvertor;
import com.vedeng.erp.kingdee.repository.KingDeeSupplierRepository;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeSupplierMapper;
import com.vedeng.erp.kingdee.service.KingDeeSupplierApiService;
import com.vedeng.erp.kingdee.service.KingDeeSupplierService;
import com.vedeng.infrastructure.kingdee.domain.command.UpdateExtCommand;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.K;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶供应商Service实现
 * @date 2022/8/26 15:18
 */
@Service
@Slf4j
public class KingDeeSupplierServiceImpl extends KingDeeBaseServiceImpl<
        KingDeeSupplierEntity,
        KingDeeSupplierDto,
        KingDeeSupplierCommand,
        KingDeeSupplierRepository,
        KingDeeSupplierConvertor,
        KingDeeSupplierCommandConvertor>
        implements KingDeeSupplierService, KingDeeSupplierApiService {


    @Autowired
    private BatchSupplierFinanceDtoMapper batchSupplierFinanceDtoMapper;


    @Override
    public void update(KingDeeSupplierDto dto) {
        log.info("更新金蝶供应商:{}", JSON.toJSONString(dto));
        UpdateExtCommand<KingDeeSupplierCommand> updateExtCommandIsDeleteEntry = new UpdateExtCommand<>(dto.getFormId());
        updateExtCommandIsDeleteEntry.buildUpdateExtCommandIsDeleteEntry(updateExtCommandIsDeleteEntry, true);
        doUpdate(dto, updateExtCommandIsDeleteEntry);
    }

    @Override
    public void updatePost(Object... objects) {
        super.updatePost(objects);
        KingDeeSupplierDto dto = getD(objects);
        batchSupplierFinanceDtoMapper.updateKingDeePushStatus(dto.getTraderSupplierFinanceId());
    }
}
