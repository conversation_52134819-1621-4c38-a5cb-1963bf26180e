package com.vedeng.erp.trader.web.api;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.trader.dto.TraderAddressDto;
import com.vedeng.erp.trader.dto.TraderContactDto;
import com.vedeng.erp.trader.service.TraderAddressApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @description 客户或供应商的地址
 * @date 2022/10/20 19:12
 **/
@ExceptionController
@RestController
@RequestMapping("/traderAddress")
@Slf4j
public class TraderAddressApi {

    @Autowired
    private TraderAddressApiService traderAddressApiService;

    /**
     * 根据查询条件查询客户地址  traderId 必填 否则为空
     * @param recordDtoPageParam 数据
     * @return
     */
    @RequestMapping(value = "/getTraderAddress", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<List<TraderAddressDto>> getTraderAddress(@RequestBody TraderAddressDto recordDtoPageParam) {

        return R.success(traderAddressApiService.list(recordDtoPageParam));
    }
}
