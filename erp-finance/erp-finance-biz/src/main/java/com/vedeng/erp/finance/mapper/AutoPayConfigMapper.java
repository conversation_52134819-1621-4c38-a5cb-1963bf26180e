package com.vedeng.erp.finance.mapper;

import com.vedeng.erp.finance.domain.entity.AutoPayConfig;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AutoPayConfigMapper {
    int deleteByPrimaryKey(Integer autoPayConfigId);

    int insert(AutoPayConfig record);

    int insertOrUpdate(AutoPayConfig record);

    int insertOrUpdateSelective(AutoPayConfig record);

    int insertSelective(AutoPayConfig record);

    AutoPayConfig selectByPrimaryKey(Integer autoPayConfigId);

    int updateByPrimaryKeySelective(AutoPayConfig record);

    int updateByPrimaryKey(AutoPayConfig record);

    int updateBatch(List<AutoPayConfig> list);

    int updateBatchSelective(List<AutoPayConfig> list);

    int batchInsert(@Param("list") List<AutoPayConfig> list);

    AutoPayConfig findLastAutoPayConfig();
}