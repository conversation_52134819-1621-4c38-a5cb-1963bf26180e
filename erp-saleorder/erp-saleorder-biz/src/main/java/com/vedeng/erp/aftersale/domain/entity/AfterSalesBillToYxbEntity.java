package com.vedeng.erp.aftersale.domain.entity;


import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Table: T_AFTER_SALES_BILL_TO_YXB
 */
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AfterSalesBillToYxbEntity extends BaseEntity {
    /**
     * Column: AFTER_SALES_BILL_TO_YXB_ID
     * Type: INT
     * Remark: id
     */
    private Integer afterSalesBillToYxbId;

    /**
     * Column: CAPITAL_BILL_NO
     * Type: VARCHAR(50)
     * Remark: 流水号
     */
    private String capitalBillNo;

    /**
     * Column: CAPITAL_BILL_ID
     * Type: INT
     * Default value: -1
     * Remark: 流水记录表id
     */
    private Integer capitalBillId;

    /**
     * Column: AMOUNT
     * Type: DECIMAL
     * Default value: 0.00
     * Remark: 金额
     */
    private BigDecimal amount;

    /**
     * Column: TRANSACTION_MODE
     * Type: VARCHAR(50)
     * Remark: 交易方式
     */
    private String transactionMode;

    /**
     * Column: TRADER_SUBJECT
     * Type: INT
     * Default value: -1
     * Remark: 交易主体1对公2对私
     */
    private Integer traderSubject;

    /**
     * Column: RESULT
     * Type: JSON(0)
     * Remark: 结果
     */
    private String result;

    /**
     * Column: IS_DELETE
     * Type: INT
     * Default value: 0
     * Remark: 删除 0 否 1 是
     */
    private Integer isDelete;

    /**
     * Column: PAYER
     * Type: VARCHAR(128)
     * Remark: 交易名
     */
    private String payer;

    /**
     * Column: COMMENTS
     * Type: VARCHAR(512)
     * Remark: 交易备注
     */
    private String comments;

    /**
     * Column: AFTER_SALES_ORDER_ID
     * Type: INT
     * Default value: -1
     * Remark: 售后单id
     */
    private Integer afterSalesOrderId;

    /**
     * Column: AFTER_SALES_ORDER_NO
     * Type: VARCHAR(50)
     * Remark: 售后单号
     */
    private String afterSalesOrderNo;


}