package com.vedeng.erp.saleorder.enums;

/**
 * 售后单收款状态
 */
public enum AfterSalesReceivePaymentStatusEnum {

    NOT(0, "未收款"),
    PART(1, "部分收款"),
    ALL(2, "全部收款"),
    ;
    private Integer code;

    private String desc;

    AfterSalesReceivePaymentStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static String getDesc(Integer code){
        for(AfterSalesReceivePaymentStatusEnum v : values()){
            if(v.getCode().equals(code)){
                return v.getDesc();
            }
        }
        return "";
    }
}
