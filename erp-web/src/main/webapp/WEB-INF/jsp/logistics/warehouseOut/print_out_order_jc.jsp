<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html lang="en">
<head>
    <%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
    <%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
    <%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
    <%@ taglib prefix="shiro" uri="http://shiro.apache.org/tags"%>
    <%@ taglib prefix="tags" tagdir="/WEB-INF/tags"%>
    <%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>
    <%@ taglib uri="http://com.vedeng.common.util/definetags" prefix="define"%>
    <%@taglib prefix="sf" uri="http://www.springframework.org/tags/form"%>

    <%@ page trimDirectiveWhitespaces="true" %>

    <%
        String path = request.getContextPath();
        String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
                + path + "/";
    %>
    <c:set var="path" value="<%=basePath%>" scope="application" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta http-equiv="Cache-Control" content="no-siteapp" />
    <title>出库单</title>
    <!--  <link rel="stylesheet" href="<%=basePath%>static/css/content.css">-->
    <link rel="stylesheet" href="<%=basePath%>static/css/general.css?rnd=${resourceVersionKey}" />
    <link rel="stylesheet" href="<%=basePath%>static/css/manage.css" />
    <!-- 模糊搜索下拉框css引入 -->
    <link rel="stylesheet" href="<%=basePath%>static/libs/searchableSelect/jquery.searchableSelect.css" />

    <script type="text/javascript" src='<%=basePath%>static/js/jquery.min.js'></script>
    <script type="text/javascript" src="<%=basePath%>static/js/jquery/validation/jquery-form.js"></script>
    <script type="text/javascript" src='<%=basePath%>static/libs/jquery/plugins/layer/layer.js'></script>
    <script type="text/javascript" src="<%=basePath%>static/libs/jquery/plugins/DatePicker/WdatePicker.js"></script>
    <script type="text/javascript" charset="UTF-8" src='<%=basePath%>static/js/form.js?rnd=${resourceVersionKey}'></script>
    <script type="text/javascript" charset="UTF-8" src='<%=basePath%>static/js/closable-tab.js?rnd=${resourceVersionKey}'></script>
    <script type="text/javascript" src='<%=basePath%>static/js/common.js?rnd=${resourceVersionKey}'></script>
    <script type="text/javascript" src="<%=basePath%>static/js/call/call.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src='<%= basePath %>static/js/movinghead.js?rnd=${resourceVersionKey}'></script>
    <!-- 模糊搜索下拉框js引入 -->
    <script type="text/javascript" src='<%=basePath%>static/libs/searchableSelect/jquery.searchableSelect.js'></script>

</head>
<body>

<link rel="stylesheet"
      href="<%=basePath%>static/css/print_out_order.css?rnd=${resourceVersionKey}" />

<style type="text/css">
    .red {
        color: red;
    }

    * {
        font-family: Arial;
    }

    /** {
        font-weight: bold;
    }
    table th, table td{
        font-weight: bold;
    }*/

    .bold {
        font-weight: bold;
    }
</style>
<div id="printdiv" style="margin-left: 0">
    <input type="hidden"  id="printAllOrder" value="${printAllOrder}">
    <c:forEach var="item" items="${dataList}" varStatus="numItem">
    <table cellpadding="0" cellspacing="0" width="650" border="0">
        <tbody>
        <tr>
            <td>
                <!-- header -->
                <table cellpadding="0" cellspacing="0" width="100%" border="0"
                       height="90" style="padding: 0px;">
                    <tbody>
                    <tr>
                        <td class="vedeng_bg" style="padding-left: 10px;">
                            <img width="162" src="<%=basePath%>static/images/vedeng_log_HC1.png">
                        </td>

                        <td class="align_c"
                            style="line-height: 30px; font-size: 17px; font-family: Arial; font-weight: bold;">
                            <b style="line-height: 30px; font-size: 19px; font-family: Arial; font-weight: bold;">南京贝登医疗股份有限公司</b>
                            <br>
                            <b style="line-height: 30px; font-size: 13px; font-family: Arial; font-weight: bold;">医械购随货同行单</b>
                        </td>

                        <td class="" >
                            <b >发货单号：</b>
                            <br>
                            <b >发货日期：</b>
                        </td>
                        <td >
                            <span style="width: 155px">${bussinessNo}</span>
                            <br>
                            <date:date value="${item.currTime}" />
                        </td>

                    </tr>
                    <tr>
                        <th colspan="2" class="align_l">&nbsp;</th>
                    </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr>
            <td valign="top" align="center">
                <table cellpadding="0" cellspacing="0" width="650" border="0"
                       align="center">
                    <tbody>
                    <tr>
                        <td colspan="20" style="font-family: '微软雅黑', Arial; "><strong style="font-weight: bold;">客户信息</strong></td>
                    </tr>
                    <tr height="17">
                        <td style="line-height: 18px;width: 80px;" ><b>收货单位：</b></td>
                        <td class="align_l" style="line-height: 18px;" >
                            <c:if test="${bussinessType != 496}">
                                ${afterSalesDetail.traderName}
                            </c:if>
                            <c:if test="${bussinessType == 496}">
                                ${saleorder.takeTraderName}
                            </c:if>
                        </td>
                        <td style="line-height: 18px;width: 70px;" ><b>收货地址：</b></td>
                        <td style="line-height: 18px;" >
                            <c:if test="${bussinessType != 496}">
                                ${fn:replace(afterSalesDetail.area,' ', '')}${afterSalesDetail.address}
                            </c:if>
                            <c:if test="${bussinessType == 496}">
                                ${fn:replace(saleorder.takeTraderArea,' ', '')}${saleorder.takeTraderAddress}
                            </c:if>
                        </td>
                    </tr>
                    <tr>
                        <td style="line-height: 18px;width: 80px;" ><b>收货人：</b></td>
                        <td style="line-height: 18px;" >
                            <c:if test="${bussinessType != 496}">
                                ${afterSalesDetail.traderContactName}
                            </c:if>
                            <c:if test="${bussinessType == 496}">
                                ${saleorder.takeTraderContactName}
                            </c:if>
                        </td>
                        <td style="line-height: 18px;width: 80px;" ><b>收货电话：</b></td>
                        <td style="line-height: 18px;" >
                            <c:if test="${bussinessType != 496}">
                                ${afterSalesDetail.traderContactMobile}
                                / ${afterSalesDetail.traderContactTelephone}
                            </c:if>
                            <c:if test="${bussinessType == 496}">
                                ${saleorder.takeTraderContactMobile}
                                / ${saleorder.takeTraderContactTelephone}
                            </c:if>
                        </td>
                    </tr>

                    <tr>
                        <th colspan="20" class="align_l">&nbsp;</th>
                    </tr>
                    <tr>
                        <td colspan="20" style="font-family: '微软雅黑', Arial"><strong style="font-weight: bold;">产品信息</strong>
                            <table cellpadding="0" width="100%" cellspacing="0" border="1"
                                   class="table_form"
                                   style="border-collapse: collapse; border: 1px solid #000;">
                                <tbody>
                                <tr>
                                    <td class="align_c" nowrap="" width="10px" style="font-family: '微软雅黑', Arial"><b>序号</b></td>
                                    <td class="align_c" nowrap="" width="35px" style="word-wrap:break-word;font-family: '微软雅黑', Arial"><b>通用名称</b></td>
                                    <td class="align_c" nowrap="" width="60px" style="word-wrap:break-word;font-family: '微软雅黑', Arial"><b>商品名称</b></td>
                                    <td class="align_c" nowrap="" width="40px" style="word-wrap:break-word;font-family: '微软雅黑', Arial"><b>品牌</b></td>
                                    <td class="align_c" cz-tab="J-model" nowrap="" width="40px" style="word-wrap:break-word;font-family: '微软雅黑', Arial"><b>型号/规格</b></td>
                                    <td class="align_c" width="40px" nowrap="" style="font-family: '微软雅黑', Arial"><b>数量单位</b></td>
                                    <c:if test="${priceFlag eq 1}">
                                        <td class="align_c" cz-tab="J-realPrice" nowrap="" width="75px" style="font-family: '微软雅黑', Arial"><b>原单价</b></td>
                                        <td class="align_c" cz-tab="J-price" nowrap="" width="75px" style="font-family: '微软雅黑', Arial"><b>单价</b></td>
                                        <td class="align_c" cz-tab="J-totalPrice" nowrap="" width="80px" style="font-family: '微软雅黑', Arial"><b>总价</b></td>
                                    </c:if>
                                    <td class="align_c" cz-tab="J-batchNumber" nowrap="" style="font-family: '微软雅黑', Arial"><b>生产批号/序列号</b></td>
                                    <td class="align_c" cz-tab="J-batchNumber" nowrap="" style="font-family: '微软雅黑', Arial"><b>生产日期</b></td>
                                    <td class="align_c" cz-tab="J-batchNumber" nowrap="" style="font-family: '微软雅黑', Arial"><b>有效期至</b></td>
                                    <td class="align_c" cz-tab="J-number" width="80px" nowrap="" style="font-family: '微软雅黑', Arial"><b>注册证号/备案凭证编号</b></td>
                                    <td class="align_c" cz-tab="J-manufacturer" nowrap="" width="60px" style="font-family: '微软雅黑', Arial"><b>生产企业</b></td>
                                    <td class="align_c" cz-tab="J-productCompanyLicence" nowrap="" width="80px" style="font-family: '微软雅黑', Arial"><b>生产企业许可证号/备案凭证编号</b></td>
                                    <td class="align_c" cz-tab="J-temperaTure" nowrap="" style="font-family: '微软雅黑', Arial"><b>储运条件</b></td>
                                </tr>
                                <c:forEach var="list" items="${item.woList}" varStatus="num">
                                    <tr>
                                        <td class="align_c" style="font-family: '微软雅黑', Arial">${num.count}</td>
                                        <td class="align_c" style="font-family: '微软雅黑', Arial"><c:choose>
                                            <c:when test="${list.productChineseName != null && list.productChineseName != ''}">
                                                ${list.productChineseName}
                                            </c:when>
                                            <c:otherwise>\</c:otherwise>
                                        </c:choose></td>
                                        <td class="align_c" style="font-family: '微软雅黑', Arial">
                                            <c:if test="${list.isActionGoods eq 1}">
                                                <span style="color:red;">【活动】</span>
                                            </c:if>
                                                ${list.goodsName}
                                        </td>
                                        <td class="align_c" style="font-family: '微软雅黑', Arial">${list.brandName}</td>
                                        <td class="align_c J-model" style="font-family: '微软雅黑', Arial">
                                            <c:choose>
                                                <c:when test="${list.spuType == 316 || list.spuType == 1008}">
                                                    <c:choose>
                                                        <c:when test="${list.model != null && list.model != ''}">
                                                            ${list.model}
                                                        </c:when>
                                                        <c:otherwise>\</c:otherwise>
                                                    </c:choose>
                                                </c:when>
                                                <c:when test="${list.spuType == 317 || list.spuType == 318}">
                                                    <c:choose>
                                                        <c:when test="${list.spec != null && list.spec != ''}">
                                                            ${list.spec}
                                                        </c:when>
                                                        <c:otherwise>\</c:otherwise>
                                                    </c:choose>
                                                </c:when>
                                                <c:otherwise>
                                                    <c:choose>
                                                        <c:when test="${list.model != null && list.model != ''}">
                                                            ${list.model}
                                                        </c:when>
                                                        <c:when test="${list.spec != null && list.spec != ''}">
                                                            ${list.spec}
                                                        </c:when>
                                                        <c:otherwise>\</c:otherwise>
                                                    </c:choose>
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                        <td class="align_c" style="font-family: '微软雅黑', Arial">${0-list.num}${list.unitName}</td>
                                        <c:if test="${priceFlag eq 1}">
                                            <td class="align_c J-realPrice" nowrap="" style="font-family: '微软雅黑', Arial">${list.realPrice}</td>
                                            <td class="align_c J-price" nowrap="" style="font-family: '微软雅黑', Arial">${list.price}</td>
                                            <td class="align_c J-totalPrice" style="font-family: '微软雅黑', Arial" nowrap="">${list.price * (0-list.num)}</td>
                                        </c:if>
<%--                                        <c:if test="${priceFlag eq 1 && list.spuType eq 316}">--%>
<%--                                            <td class="align_c J-realPrice" nowrap="" style="font-family: '微软雅黑', Arial">\</td>--%>
<%--                                            <td class="align_c J-price" nowrap="" style="font-family: '微软雅黑', Arial">\</td>--%>
<%--                                            <td class="align_c J-totalPrice" style="font-family: '微软雅黑', Arial" nowrap="">\</td>--%>
<%--                                        </c:if>--%>
                                        <td class="align_c J-batchNumber" nowrap="" style="font-family: '微软雅黑', Arial"><c:choose><c:when test="${list.barcodeFactory != null && list.barcodeFactory != ''}">${list.barcodeFactory}</c:when><c:when test="${list.batchNumber != null && list.batchNumber != ''}">${list.batchNumber}</c:when><c:otherwise>\</c:otherwise></c:choose></td>
                                        <td class="align_c J-batchNumber" nowrap="" style="font-family: '微软雅黑', Arial">
                                            <date:date value ="${list.productDate}" format="yyyy-MM-dd"/>
                                        </td>
                                        <td class="align_c J-batchNumber" nowrap="" style="font-family: '微软雅黑', Arial">
<%--                                            2099-12-31 00：00：00--%>
                                            <c:if test="${list.expirationDate < 4102329600000}"><date:date value ="${list.expirationDate}" format="yyyy-MM-dd"/></c:if>
                                            <c:if test="${list.expirationDate >= 4102329600000}">见铭牌或说明书</c:if>
                                        </td>
                                        <td class="align_c J-number" nowrap="">
                                            <c:if test="${titleType == 1}"><span style="font-family: '微软雅黑', Arial">${list.recordNumber}</span></c:if>
                                            <c:if test="${titleType == 2}"><span style="font-family: '微软雅黑', Arial">${list.registrationNumber}</span></c:if>
                                            <c:if test="${titleType == null}"><span style="font-family: '微软雅黑', Arial">\</span></c:if>
                                        </td>
                                        <td class="align_c J-manufacturer" style="font-family: '微软雅黑', Arial">${list.manufacturer}</td>
                                        <td class="align_c J-productCompanyLicence" style="font-family: '微软雅黑', Arial">${list.productCompanyLicence}</td>
                                        <td class="align_c J-temperaTure" nowrap="" style="font-family: '微软雅黑', Arial">
                                            <c:choose>
                                                <c:when test="${ list.storageConditionOne ==1 }"> 常温0-30℃ </c:when>
                                                <c:when test="${ list.storageConditionOne ==2 }"> 阴凉0-20℃ </c:when>
                                                <c:when test="${ list.storageConditionOne ==3 }"> 冷藏2-10℃   </c:when>
                                                <c:when test="${ list.storageConditionOne ==4 }">
                                                    <c:if test="${not empty list.storageConditionOneLowerValue and not empty list.storageConditionOneUpperValue}">
                                                        其他温度<fmt:formatNumber value="${list.storageConditionOneLowerValue}" type="number"/>℃
                                                        -
                                                        <fmt:formatNumber value="${list.storageConditionOneUpperValue}" type="number"/>℃
                                                    </c:if>
                                                </c:when>
                                                <c:otherwise> \ </c:otherwise>
                                            </c:choose>
<%--                                                ${list.temperaTure} --%>
                                        </td>
                                    </tr>
                                </c:forEach>
                                </tbody>
                            </table></td>
                        <c:if test="${printFlag == 5}">
                        <c:if test="${priceFlag eq 1}">
                    <tr>
                        <table cellpadding="0" width="100%" cellspacing="0" border="1"
                               class="table_form"
                               style="border-collapse: collapse; border: 1px solid #000;">
                            <tr class="align_c ">
                                <td class="align_c" style="font-family: '微软雅黑', Arial">商品总额: ${item.totalPrice}    优惠券: \    运费: ${item.expressPrice}   实付金额: ${item.realTotalPrice}</td>
                            </tr>
                        </table>
                    </tr>
                    </c:if>

                    </c:if>
                    </tr>

                    <tr>
                        <td colspan="6">
                            <table width="100%" cellspacing="0">
                                <tbody >
                                <tr >
                                    <td width="33%" style="font-family: '微软雅黑', Arial"><b>制单人：${loginChineseName }</b></td>
                                    <td style="font-family: '微软雅黑', Arial"><b>发货人：王继勇</b></td>
                                    <div></div>
                                    <td width="33%" style="font-family: '微软雅黑', Arial"><b >承运人：</b></td>
                                </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="8">
                            <table width="100%" border="0" cellpadding="0" cellspacing="0">
                                <tbody>
                                <tr>
                                    <td style="position:relative;font-family: '微软雅黑', Arial">
                                        <b>客户签收：</b>____________________________
                                        <br>
                                        <b>日期：</b>__________<b>年</b>__________<b>月</b>__________<b>日</b>
                                        <br>
                                        <b>(签收后请回传贝登公司)</b>
                                        &nbsp;&nbsp;&nbsp;&nbsp;<br>

                                    </td>
                                    <td  style="position:relative;font-family: '微软雅黑', Arial">
                                        <table width="100%" border="0" cellpadding="0" cellspacing="0">
                                            <tbody>
                                            <tr>
                                                <td style="padding:0px;font-family: '微软雅黑', Arial;">
                                                    <b>供货单位：南京贝登医疗股份有限公司</b>
                                                    <br>
                                                    <b>仓库地址：江苏省南京市栖霞区经天路6号中电熊猫物流园区4#库二层</b>
                                                    <br>
                                                    <b >联系电话：************</b>
                                                    <c:if test="${!item.jcPrintOutExpress}">
                                                        <img src="<%=basePath%>static/images/printoutlog.png" style="z-index: 12;position: absolute;right: 47px;top: -21.5px;display: inline-block;height: 100px;width: 140px;font-family: '微软雅黑', Arial">
                                                    </c:if>
                                                </td>

                                            </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                                </tbody>
                            </table>

                        </td>

                    </tr>

                    </tbody>
                </table>
            </td>
        </tr>
        </tbody>
    </table>
        <div style="page-break-after: always;"></div>
    </c:forEach>

</div>
<c:if test="${wmsflag ne 'wms'}">
<span class="confSearch bt-small bt-bg-style bg-light-blue"
      onclick="preview('printdiv')" style="margin-top: 50px" id="searchSpan">打印</span>
</c:if>

<script type="text/javascript"
        src='<%=basePath%>static/js/jquery.PrintArea.js'></script>
<script type="text/javascript"
        src='<%=basePath%>static/js/logistics/warehouseIn/addBarcode.js?rnd=${resourceVersionKey}'></script>
</body>
</html>