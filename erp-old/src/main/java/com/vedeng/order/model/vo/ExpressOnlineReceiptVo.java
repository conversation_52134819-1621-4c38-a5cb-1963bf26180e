package com.vedeng.order.model.vo;

import lombok.Data;

/**
 * 快递签收视图层
 *
 * <AUTHOR>
 */
@Data
public class ExpressOnlineReceiptVo {
    /**
     * 快递单号
     */
    private String logisticsNo;

    /**
     * 快递公司
     */
    private String logisticsName;

    /**
     * 客户名称
     */
    private String traderName;

    /**
     * 客户ID
     */
    private Integer traderId;

    /**
     * 手机
     */
    private String mobile;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 签收时间
     */
    private Long signTime;

    /**
     * 备注
     */
    private String comments;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 销售单号
     */
    private String saleorderNo;

    /**
     * 订单联系人
     */
    private String traderContactName;


    /**
     * 在线签收id
     */
    private Integer expressOnlineRecceiptReordId;

    /**
     * 快递id
     */
    private Integer expressId;
}
