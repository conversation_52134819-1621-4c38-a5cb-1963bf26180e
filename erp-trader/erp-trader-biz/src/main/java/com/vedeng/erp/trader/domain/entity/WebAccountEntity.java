package com.vedeng.erp.trader.domain.entity;

import java.util.Date;

/**
 * Table: T_WEB_ACCOUNT
 */
public class WebAccountEntity {
    /**
     * Column: ERP_ACCOUNT_ID
     * Type: INT UNSIGNED
     * Remark: 自增ID
     */
    private Integer erpAccountId;

    /**
     * Column: WEB_ACCOUNT_ID
     * Type: INT UNSIGNED
     * Default value: 0
     * Remark: 网站用户ID
     */
    private Integer webAccountId;

    /**
     * Column: SSO_ACCOUNT_ID
     * Type: INT UNSIGNED
     * Default value: 0
     * Remark: sso账户ID
     */
    private Integer ssoAccountId;

    /**
     * Column: TRADER_ID
     * Type: INT
     * Default value: 0
     * Remark: 客户ID
     */
    private Integer traderId;

    /**
     * Column: TRADER_CONTACT_ID
     * Type: INT UNSIGNED
     * Default value: 0
     * Remark: ERP联系人ID
     */
    private Integer traderContactId;

    /**
     * Column: TRADER_ADDRESS_ID
     * Type: INT UNSIGNED
     * Default value: 0
     * Remark: ERP联系地址ID
     */
    private Integer traderAddressId;

    /**
     * Column: USER_ID
     * Type: INT UNSIGNED
     * Default value: 0
     * Remark: ERP归属人ID
     */
    private Integer userId;

    /**
     * Column: IS_ENABLE
     * Type: BIT
     * Default value: 0
     * Remark: 是否有效 0否 1是
     */
    private Boolean isEnable;

    /**
     * Column: FROM
     * Type: TINYINT(3)
     * Remark: 由 来源1WEB,2微信,3APP(ios),4APP(android),5其它  改为 来源1PC端,2M端,3APP端,4APP端,5其它
     */
    private Byte from;

    /**
     * Column: COMPANY_STATUS
     * Type: BIT
     * Default value: 0
     * Remark: 0待审核1审核通过2审核不通过（三证审核状态）
     */
    private Boolean companyStatus;

    /**
     * Column: INDENTITY_STATUS
     * Type: BIT
     * Default value: 0
     * Remark: 0待审核1审核通过2审核不通过（资质审核状态）
     */
    private Boolean indentityStatus;

    /**
     * Column: IS_OPEN_STORE
     * Type: BIT
     * Default value: 0
     * Remark: 是否开通商家0未申请1申请待审核2审核通过3审核不通过
     */
    private Boolean isOpenStore;

    /**
     * Column: IS_VEDENG_JX
     * Type: BIT
     * Default value: 0
     * Remark: 是否是贝登精选会员0否1是(已废除)
     */
    private Boolean isVedengJx;

    /**
     * Column: ACCOUNT
     * Type: VARCHAR(32)
     * Remark: 用户名
     */
    private String account;

    /**
     * Column: EMAIL
     * Type: VARCHAR(64)
     * Remark: 邮箱地址
     */
    private String email;

    /**
     * Column: MOBILE
     * Type: VARCHAR(16)
     * Remark: 账号名（注册手机号码）
     */
    private String mobile;

    /**
     * Column: COMPANY_NAME
     * Type: VARCHAR(256)
     * Remark: 公司名称
     */
    private String companyName;

    /**
     * Column: NAME
     * Type: VARCHAR(16)
     * Remark: 注册姓名
     */
    private String name;

    /**
     * Column: SEX
     * Type: BIT
     * Default value: 0
     * Remark: 性别:0女 1 男 2 保密
     */
    private Boolean sex;

    /**
     * Column: WEIXIN_OPENID
     * Type: VARCHAR(32)
     * Remark: 微信OPENID
     */
    private String weixinOpenid;

    /**
     * Column: UUID
     * Type: VARCHAR(36)
     * Remark: 唯一ID
     */
    private String uuid;

    /**
     * Column: ADD_TIME
     * Type: BIGINT UNSIGNED
     * Default value: 0
     * Remark: 创建时间
     */
    private Long addTime;

    /**
     * Column: LAST_LOGIN_TIME
     * Type: BIGINT UNSIGNED
     * Default value: 0
     * Remark: 最后登录时间
     */
    private Long lastLoginTime;

    /**
     * Column: IS_VEDENG_JOIN
     * Type: TINYINT(3)
     * Default value: 0
     * Remark: 是否申请加入:0 否 1 是
     */
    private Byte isVedengJoin;

    /**
     * Column: MOD_TIME_JOIN
     * Type: BIGINT UNSIGNED
     * Remark: 申请时间
     */
    private Long modTimeJoin;

    /**
     * Column: IS_SEND_MESSAGE
     * Type: BIT
     * Default value: 0
     * Remark: 是否推送过消息（0 否  1 是）
     */
    private Boolean isSendMessage;

    /**
     * Column: REGISTER_PLATFORM
     * Type: INT
     * Remark: 注册平台(1贝登医疗，2医械购，3科研购，4集团业务部，5其他，6集采)
     */
    private Integer registerPlatform;

    /**
     * Column: BELONG_PLATFORM
     * Type: INT
     * Remark: 注册平台(1贝登医疗，2医械购，3科研购，4集团业务部，5其他，6集采)
     */
    private Integer belongPlatform;

    /**
     * Column: MOD_TIME
     * Type: DATETIME
     * Remark: 更新时间
     */
    private Date modTime;

    /**
     * Column: IS_VEDENG_MEMBER
     * Type: BIT
     * Default value: 0
     * Remark: 是否是贝登会员0否1是
     */
    private Boolean isVedengMember;

    /**
     * Column: VEDENG_MEMBER_TIME
     * Type: DATETIME
     * Remark: 加入贝登会员时间
     */
    private Date vedengMemberTime;

    /**
     * Column: IS_REGIONAL_MALL
     * Type: INT
     * Default value: 0
     * Remark: 是否注册于区域商城，0否，1是
     */
    private Integer isRegionalMall;

    /**
     * Column: REGISTER_REGIONAL_MALL
     * Type: INT
     * Remark: 注册的区域商城id
     */
    private Integer registerRegionalMall;

    /**
     * Column: AUTH_TYPE
     * Type: INT UNSIGNED
     * Remark: 认证方式:1-营业执照认证 2-个人名片认证
     */
    private Integer authType;

    private Integer isOnJob;

    private String position;

    public Integer getIsOnJob() {
        return isOnJob;
    }

    public void setIsOnJob(Integer isOnJob) {
        this.isOnJob = isOnJob;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public Integer getErpAccountId() {
        return erpAccountId;
    }

    public void setErpAccountId(Integer erpAccountId) {
        this.erpAccountId = erpAccountId;
    }

    public Integer getWebAccountId() {
        return webAccountId;
    }

    public void setWebAccountId(Integer webAccountId) {
        this.webAccountId = webAccountId;
    }

    public Integer getSsoAccountId() {
        return ssoAccountId;
    }

    public void setSsoAccountId(Integer ssoAccountId) {
        this.ssoAccountId = ssoAccountId;
    }

    public Integer getTraderId() {
        return traderId;
    }

    public void setTraderId(Integer traderId) {
        this.traderId = traderId;
    }

    public Integer getTraderContactId() {
        return traderContactId;
    }

    public void setTraderContactId(Integer traderContactId) {
        this.traderContactId = traderContactId;
    }

    public Integer getTraderAddressId() {
        return traderAddressId;
    }

    public void setTraderAddressId(Integer traderAddressId) {
        this.traderAddressId = traderAddressId;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Boolean getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(Boolean isEnable) {
        this.isEnable = isEnable;
    }

    public Byte getFrom() {
        return from;
    }

    public void setFrom(Byte from) {
        this.from = from;
    }

    public Boolean getCompanyStatus() {
        return companyStatus;
    }

    public void setCompanyStatus(Boolean companyStatus) {
        this.companyStatus = companyStatus;
    }

    public Boolean getIndentityStatus() {
        return indentityStatus;
    }

    public void setIndentityStatus(Boolean indentityStatus) {
        this.indentityStatus = indentityStatus;
    }

    public Boolean getIsOpenStore() {
        return isOpenStore;
    }

    public void setIsOpenStore(Boolean isOpenStore) {
        this.isOpenStore = isOpenStore;
    }

    public Boolean getIsVedengJx() {
        return isVedengJx;
    }

    public void setIsVedengJx(Boolean isVedengJx) {
        this.isVedengJx = isVedengJx;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account == null ? null : account.trim();
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email == null ? null : email.trim();
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile == null ? null : mobile.trim();
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName == null ? null : companyName.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public Boolean getSex() {
        return sex;
    }

    public void setSex(Boolean sex) {
        this.sex = sex;
    }

    public String getWeixinOpenid() {
        return weixinOpenid;
    }

    public void setWeixinOpenid(String weixinOpenid) {
        this.weixinOpenid = weixinOpenid == null ? null : weixinOpenid.trim();
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid == null ? null : uuid.trim();
    }

    public Long getAddTime() {
        return addTime;
    }

    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    public Long getLastLoginTime() {
        return lastLoginTime;
    }

    public void setLastLoginTime(Long lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }

    public Byte getIsVedengJoin() {
        return isVedengJoin;
    }

    public void setIsVedengJoin(Byte isVedengJoin) {
        this.isVedengJoin = isVedengJoin;
    }

    public Long getModTimeJoin() {
        return modTimeJoin;
    }

    public void setModTimeJoin(Long modTimeJoin) {
        this.modTimeJoin = modTimeJoin;
    }

    public Boolean getIsSendMessage() {
        return isSendMessage;
    }

    public void setIsSendMessage(Boolean isSendMessage) {
        this.isSendMessage = isSendMessage;
    }

    public Integer getRegisterPlatform() {
        return registerPlatform;
    }

    public void setRegisterPlatform(Integer registerPlatform) {
        this.registerPlatform = registerPlatform;
    }

    public Integer getBelongPlatform() {
        return belongPlatform;
    }

    public void setBelongPlatform(Integer belongPlatform) {
        this.belongPlatform = belongPlatform;
    }

    public Date getModTime() {
        return modTime;
    }

    public void setModTime(Date modTime) {
        this.modTime = modTime;
    }

    public Boolean getIsVedengMember() {
        return isVedengMember;
    }

    public void setIsVedengMember(Boolean isVedengMember) {
        this.isVedengMember = isVedengMember;
    }

    public Date getVedengMemberTime() {
        return vedengMemberTime;
    }

    public void setVedengMemberTime(Date vedengMemberTime) {
        this.vedengMemberTime = vedengMemberTime;
    }

    public Integer getIsRegionalMall() {
        return isRegionalMall;
    }

    public void setIsRegionalMall(Integer isRegionalMall) {
        this.isRegionalMall = isRegionalMall;
    }

    public Integer getRegisterRegionalMall() {
        return registerRegionalMall;
    }

    public void setRegisterRegionalMall(Integer registerRegionalMall) {
        this.registerRegionalMall = registerRegionalMall;
    }

    public Integer getAuthType() {
        return authType;
    }

    public void setAuthType(Integer authType) {
        this.authType = authType;
    }
}