$(function(){
	init();
	$("#buyorder").click(function(){
		checkLogin();
		var directs ="";
		var saleorderType="";
		var saleorderGoodsIds="";
		var orderType = "";
		var goodsIds = "";
		//订单号调整
		var newSaleorderType = "";
		$.each($("input[name='oneSelect']"),function(i,n){
			if($(this).prop("checked")){
				saleorderType += $(this).siblings("input[name='saleorderNo']").val()+",";
				newSaleorderType += $(this).siblings("input[name='saleorderType']").val()+",";
				directs += $(this).siblings("input[name='deliveryDirect']").val()+",";
				saleorderId = $(this).siblings("input[name='saleorderId']").val();
				saleorderGoodsIds += $(this).siblings("input[name='saleorderGoodId']").val()+",";
				if(goodsIds.indexOf($(this).siblings("input[name='goodsId']").val())<0){
					goodsIds += $(this).siblings("input[name='goodsId']").val()+",";
				}
			}
		});
		if(goodsIds == "" || saleorderGoodsIds == ""){
			layer.alert("未选择任何信息");
			return false;
		}
		// if(saleorderType.indexOf("BH")>=0&&(saleorderType.indexOf("VS")>=0 || saleorderType.indexOf("DH")>=0 || saleorderType.indexOf("JX")>=0)){
		// 	layer.alert("备货单、销售订单不允许同时被选择");
		// 	return false;
		// }
		if(newSaleorderType.indexOf("2")>=0&&(newSaleorderType.indexOf("0")>=0 || newSaleorderType.indexOf("3") >= 0)){
			layer.alert("备货单、销售订单不允许同时被选择");
			return false;
		}
		if(directs.indexOf("1")>=0&&directs.indexOf("0")>=0){
			layer.alert("直发与普发不允许同时被选择");
			return false;
		}
		if(directs.indexOf("1")>=0&&directs.indexOf("0")<0){
			var saleorderNos = saleorderType.split(",");
			var flag =false;
			if(saleorderNos.length>1){
				for(var i=1; i<saleorderNos.length;i++){
					if(saleorderNos[i] != '' && saleorderNos[0] != saleorderNos[i]){
						flag=true;
						return false;
					}
				}
			}
			if(flag){
				layer.alert("直发仅允许选择同订单直发产品");
				return false;
			}
		}
		if(saleorderType.indexOf("BH")>=0){
			orderType = 1;
		}else{
			orderType = 0;
		}
		var direct= '';
		if(directs.indexOf("1")>=0&&directs.indexOf("0")<0){
			direct = 1;
		}else{
			direct = 0;
		}
		index = layer.confirm("您是否确认该操作？", {
			  btn: ['确定','取消'] //按钮
			}, function(){
			var timestamp = Date.parse(new Date());
			var	url = page_url+"/order/buyorder/saveAddBuyorder.do?orderType="+orderType+"&saleorderGoodsIds="
						+escape(saleorderGoodsIds)+"&deliveryDirect="+direct+"&goodsIds="+goodsIds+"&saleorderId="+saleorderId+"&isIgnore="+0;
			$("#addpf").attr('tabTitle','{"num":"view'+timestamp+'","title":"新增采购订单","link":"'+url+'"}');
			$("#addpf").click();
			layer.close(index);
			});
	})


// 订单流 新 采购单生成 start
	$("#newBuyorder").click(function(){
		checkLogin();
		var directs ="";
		var saleorderType="";
		var saleorderGoodsIds="";
		var orderType = "";
		var goodsIds = "";
		var newSaleorderType = "";
		$.each($("input[name='oneSelect']"),function(i,n){
			if($(this).prop("checked")){
				saleorderType += $(this).siblings("input[name='saleorderNo']").val()+",";
				newSaleorderType += $(this).siblings("input[name='saleorderType']").val()+",";
				directs += $(this).siblings("input[name='deliveryDirect']").val()+",";
				saleorderId = $(this).siblings("input[name='saleorderId']").val();
				saleorderGoodsIds += $(this).siblings("input[name='saleorderGoodId']").val()+",";
				if(goodsIds.indexOf($(this).siblings("input[name='goodsId']").val())<0){
					goodsIds += $(this).siblings("input[name='goodsId']").val()+",";
				}
			}
		});
		if(goodsIds == "" || saleorderGoodsIds == ""){
			layer.alert("未选择任何信息");
			return false;
		}
		// if(saleorderType.indexOf("BH")>=0&&(saleorderType.indexOf("VS")>=0 || saleorderType.indexOf("DH")>=0 || saleorderType.indexOf("JX")>=0)){
		// 	layer.alert("备货单、销售订单不允许同时被选择");
		// 	return false;
		// }
		if(newSaleorderType.indexOf("2")>=0&&(newSaleorderType.indexOf("0")>=0 || newSaleorderType.indexOf("3")>=0)){
			layer.alert("备货单、销售订单不允许同时被选择");
			return false;
		}
		if(directs.indexOf("1")>=0&&directs.indexOf("0")>=0){
			layer.alert("直发与普发不允许同时被选择");
			return false;
		}
		if(directs.indexOf("1")>=0&&directs.indexOf("0")<0){
			var saleorderNos = saleorderType.split(",");
			var flag =false;
			if(saleorderNos.length>1){
				for(var i=1; i<saleorderNos.length;i++){
					if(saleorderNos[i] != '' && saleorderNos[0] != saleorderNos[i]){
						flag=true;
						return false;
					}
				}
			}
			if(flag){
				layer.alert("直发仅允许选择同订单直发产品");
				return false;
			}
		}
		if(saleorderType.indexOf("BH")>=0){
			orderType = 1;
		}else{
			orderType = 0;
		}
		var direct= '';
		if(directs.indexOf("1")>=0&&directs.indexOf("0")<0){
			direct = 1;
		}else{
			direct = 0;
		}
		index = layer.confirm("您是否确认该操作？", {
			btn: ['确定','取消'] //按钮
		}, function(){
			var timestamp = Date.parse(new Date());
			var	url = page_url+"/order/newBuyorder/newCreateBuyOrder.do?orderType="+orderType+"&saleorderGoodsIds="
				+escape(saleorderGoodsIds)+"&deliveryDirect="+direct+"&goodsIds="+goodsIds+"&saleorderId="+saleorderId+"&isIgnore="+0;
			$("#addpf").attr('tabTitle','{"num":"view'+timestamp+'","title":"新增采购订单","link":"'+url+'"}');
			$("#addpf").click();
			layer.close(index);
		});
	})

	// 新增采购费用单
	$("#addFeebuy").click(function(){
		checkLogin();
		var direct ="";
		var saleorderType="";
		var saleorderGoodsIds="";
		var goodsIds = "";
		var saleorderId = "";
		var newSaleorderType = "";
		$.each($("input[name='oneSelect']"),function(i,n){
			if($(this).prop("checked")){
				saleorderType += $(this).siblings("input[name='saleorderNo']").val()+",";
				newSaleorderType += $(this).siblings("input[name='saleorderType']").val()+",";
				direct += $(this).siblings("input[name='deliveryDirect']").val()+",";
				saleorderId = $(this).siblings("input[name='saleorderId']").val();
				saleorderGoodsIds += $(this).siblings("input[name='saleorderGoodId']").val()+",";
				if(goodsIds.indexOf($(this).siblings("input[name='goodsId']").val())<0){
					goodsIds += $(this).siblings("input[name='goodsId']").val()+",";
				}
			}
		});
		if(goodsIds==""||saleorderGoodsIds==""){
			layer.alert("未选择任何信息");
			return false;
		}

		index = layer.confirm("您是否确认该操作？", {
			btn: ['确定','取消'] //按钮
		}, function(){
			var timestamp = Date.parse(new Date());
			var	url = page_url+"/buyorderExpense/preBuyOrder/edit.do?saleorderGoodsIds="
				+escape(saleorderGoodsIds)+"&goodsIds="+goodsIds+"&source=prePurchse";
			$("#addbuyorder").attr('tabTitle','{"num":"view'+timestamp+'","title":"新增采购费用订单","link":"'+url+'"}');
			$("#addbuyorder").click();
			layer.close(index);
		}, function(){
		});
	})

// 订单流 新 采购单生成 end


	$("#addbuy").click(function(){
		checkLogin();
		var direct ="";
		var saleorderType="";
		var saleorderGoodsIds="";
		var goodsIds = "";
		var saleorderId = "";
		var newSaleorderType = "";
		$.each($("input[name='oneSelect']"),function(i,n){
			if($(this).prop("checked")){
				saleorderType += $(this).siblings("input[name='saleorderNo']").val()+",";
				newSaleorderType += $(this).siblings("input[name='saleorderType']").val()+",";
				direct += $(this).siblings("input[name='deliveryDirect']").val()+",";
				saleorderId = $(this).siblings("input[name='saleorderId']").val();
				saleorderGoodsIds += $(this).siblings("input[name='saleorderGoodId']").val()+",";
				if(goodsIds.indexOf($(this).siblings("input[name='goodsId']").val())<0){
					goodsIds += $(this).siblings("input[name='goodsId']").val()+",";
				}
			}
		});
		if(goodsIds==""||saleorderGoodsIds==""){
			layer.alert("未选择任何信息");
			return false;
		}
		// if(saleorderType.indexOf("BH")>=0&&(saleorderType.indexOf("VS")>=0 || saleorderType.indexOf("DH")>=0 || saleorderType.indexOf("JX")>=0)){
		// 	layer.alert("备货单、销售订单不允许同时被选择");
		// 	return false;
		// }
		if(newSaleorderType.indexOf("2")>=0&&(newSaleorderType.indexOf("0")>=0 || newSaleorderType.indexOf("3") >= 0)){
			layer.alert("备货单、销售订单不允许同时被选择");
			return false;
		}
		if(direct.indexOf("1")>=0&&direct.indexOf("0")>=0){
			layer.alert("直发与普发不允许同时被选择");
			return false;
		}
		if(direct.indexOf("1")>=0&&direct.indexOf("0")<0){
			var saleorderNos = saleorderType.split(",");
			var flag =false;
			if(saleorderNos.length>1){
				for(var i=1; i<saleorderNos.length;i++){
					if(saleorderNos[i] != '' && saleorderNos[0] != saleorderNos[i]){
						flag=true;
						return false;
					}
				}
			}
			if(flag){
				layer.alert("直发仅允许选择同订单直发产品");
				return false;
			}
		}

		index = layer.confirm("您是否确认该操作？", {
			  btn: ['确定','取消'] //按钮
			}, function(){
			var timestamp = Date.parse(new Date());
			var	url = page_url+"/order/buyorder/addSaleorderToBuyorderPage.do?saleorderGoodsIds="
						+escape(saleorderGoodsIds)+"&goodsIds="+goodsIds;
			$("#addbuyorder").attr('tabTitle','{"num":"view'+timestamp+'","title":"加入采购订单","link":"'+url+'"}');
			$("#addbuyorder").click();
			layer.close(index);
		layer.close(index);
			}, function(){
			});
	})
	$("#addvbbutton").click(function(){
		checkLogin();
		var saleorderGoodsIds="";
		var goodId = "";
		let goodNum = 0;
		var flag=true;
		$.each($("input[name='oneSelect']"),function(i,n){
			if($(this).prop("checked")){
				if($(this).siblings("input[name='saleorderNo']").val().substr(0, 2) == "BH"){
					layer.alert("备货单（BH单）不支持绑定在途VB单！");
					flag=false;
					return false;
				}
				saleorderGoodsIds += ($(this).siblings("input[name='saleorderGoodId']").val()).split('|')[1]+'NUM'+$(this).siblings("input[name='noBuyNum']").val()+",";
				goodNum+=Number($(this).siblings("input[name='noBuyNum']").val());
				if(goodId!=$(this).siblings("input[name='goodsId']").val()){
					if(goodId == "") {
						goodId = $(this).siblings("input[name='goodsId']").val();
					}else{
						layer.alert("仅支持相同产品关联备货单,可通过查询条件定位相同产品。");
						flag=false;
						return false;
					}
				}
			}
		});
		if(!flag){
			return false;
		}
		if(goodId==""||saleorderGoodsIds==""||goodNum==0){
			layer.alert("未选择任何信息");
			return false;
		}
		saleorderGoodsIds = saleorderGoodsIds.substring(0,saleorderGoodsIds.length-1);
		index = layer.confirm("您是否确认该操作？", {
			btn: ['确定','取消'] //按钮
		}, function(){
			var searchUrl = page_url+"/order/buyorder/searchVB.do?saleorderGoodsIds="+saleorderGoodsIds+"&goodId="+goodId+"&goodNum="+goodNum+"&buyOrderId=";
			$("#addvbview").attr('layerParams','{"width":"800px","height":"500px","title":"关联在途VB单","link":"'+searchUrl+'"}');
			$("#addvbview").click();
			layer.close(index);
		});
	})
});

//忽略
function ignore(goodsId){
	checkLogin();
	var saleorderGoodsIDs = "";
	$.each($("input[alt='"+goodsId+"']"),function(i,n){
		if($(this).prop("checked")){
			saleorderGoodsIDs += $(this).siblings("input[name='saleorderGoodId']").val()+",";
		}
	});
	if(saleorderGoodsIDs ==''){
		layer.alert("请选择要忽略数据");
		return false;
	}
	index = layer.confirm("您是否确认该操作？", {
		  btn: ['确定','取消'] //按钮
		}, function(){
			$.ajax({
				type: "POST",
				url: page_url+"/order/buyorder/saveIgnore.do",
				data: {'saleorderGoodsIDs':saleorderGoodsIDs},
				dataType:'json',
				success: function(data){
					if(data.code==0){
						self.location.reload();
					}else{
						layer.alert(data.message);
					}
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
		layer.close(index);
		}, function(){
		});
}

function oneSelect(obj){
	checkLogin();
	if($(obj).prop("checked")){
		//计算所有已选择的，排除刚点击的
		var allSelect = 0;
		var allnos = '';
		var isDirects = '';
		var isGift = '';
		var beforeIsVirture = '';
		var saleorderNoss = '';
		var newAllOrderType = '';
		var objsaleorderGoodsId = $(obj).siblings("input[name='saleorderGoodId']").val();
		$.each($("input[name='oneSelect']"),function(i,n){
			var goodsId = $(this).siblings("input[name='saleorderId']").val();
			var thissaleorderGoodsId = $(this).siblings("input[name='saleorderGoodId']").val();
			if($(this).prop("checked") && objsaleorderGoodsId != thissaleorderGoodsId){
				allnos += $(this).siblings("input[name='saleorderNo']").val().substring(0,2)+",";
				newAllOrderType += $(this).siblings("input[name='saleorderType']").val()+",";
				isDirects += $(this).siblings("input[name='deliveryDirect']").val()+",";
				isGift += $(this).siblings("input[name='isGift']").val()+",";
				beforeIsVirture += $(this).siblings("input[name='isVirture']").val()+",";
				saleorderNoss += $(this).siblings("input[name='saleorderNo']").val()+",";
				allSelect += Number($(this).siblings("input[name='noBuyNum']").val());
			}
		});
		$("#selected").html(allSelect);
		//本次点击数据
		var direct = $(obj).siblings("input[name='deliveryDirect']").val();
		var gift = $(obj).siblings("input[name='isGift']").val();
		var isVirture = $(obj).siblings("input[name='isVirture']").val();
		var oneno = $(obj).siblings("input[name='saleorderNo']").val().substring(0,2);
		var saleorderNo = $(obj).siblings("input[name='saleorderNo']").val();
		var nowSaleorderType = $(obj).siblings("input[name='saleorderType']").val();
		if(isDirects!=''&&((isDirects.indexOf("0")>=0&&direct.indexOf("1")>=0)||(isDirects.indexOf("1")>=0&&direct.indexOf("0")>=0))){
			layer.alert("直发与普发不允许同时被选择");
			$(obj).prop("checked", false);
			return false;
		}else if(isDirects!=''&&isDirects.indexOf("0")<0&&direct.indexOf("1")>=0&&saleorderNoss!=''&&saleorderNoss.indexOf(saleorderNo)<0 ){
			layer.alert("直发仅允许选择同订单直发产品");
			$(obj).prop("checked", false);
			return false;
		}
		if (isGift != ''&&((isGift.indexOf("0")>=0&&gift.indexOf("1")>=0)||(isGift.indexOf("1")>=0&&gift.indexOf("0")>=0))){
			layer.alert("赠品产品与非赠品产品不允许同时被选择");
			$(obj).prop("checked",false);
			return false;
		}
		//如果选中均为实物商品，【生成采购费用订单】按钮置灰不可点击
		//之前选择的集合中都为实物商品，后面单个选择和组合选择的也都为实物商品
		if((beforeIsVirture!='' && beforeIsVirture.indexOf("2")==-1 && isVirture.indexOf("2")==-1)||
			(beforeIsVirture =='' && isVirture.indexOf("2")==-1)){
			//生成采购费用订单置灰
			$("#addFeebuy").attr ("disabled","true");
			$("#addFeebuy").removeClass("bg-light-green");
			$("#addFeebuy").addClass("bg-light-grey");
			//生成采购订单可点击
			$("#newBuyorder").removeAttr ("disabled");
			$("#newBuyorder").removeClass("bg-light-grey");
			$("#newBuyorder").addClass("bg-light-green");
		}
		//如果选中均为虚拟商品，【生成采购订单】按钮置灰不可点击
		//之前选择的集合中都为虚拟商品，后面单个选择和组合选择的也都为虚拟商品
		else if((beforeIsVirture!='' && beforeIsVirture.indexOf("0")==-1 && beforeIsVirture.indexOf("1")==-1 && isVirture.indexOf("0")==-1 && isVirture.indexOf("1")==-1)||
			(beforeIsVirture=='' && isVirture.indexOf("0")==-1 && isVirture.indexOf("1")==-1))
		{
			//生成采购订单置灰
			$("#newBuyorder").attr ("disabled","true");
			$("#newBuyorder").removeClass("bg-light-green");
			$("#newBuyorder").addClass("bg-light-grey");
			//生成采购费用订单可点击
			$("#addFeebuy").removeAttr ("disabled");
			$("#addFeebuy").removeClass("bg-light-grey");
			$("#addFeebuy").addClass("bg-light-green");
		}else{
			//生成采购费用订单置灰
			$("#addFeebuy").attr ("disabled","true");
			$("#addFeebuy").removeClass("bg-light-green");
			$("#addFeebuy").addClass("bg-light-grey");
			//生成采购订单可点击
			$("#newBuyorder").removeAttr ("disabled");
			$("#newBuyorder").removeClass("bg-light-grey");
			$("#newBuyorder").addClass("bg-light-green");
		}

		if(newAllOrderType!=''&&(newAllOrderType.indexOf("2")>=0&&(nowSaleorderType.indexOf("0")>=0||nowSaleorderType.indexOf("3")>=0))
			||((newAllOrderType.indexOf("1")>=0||newAllOrderType.indexOf("3")>=0) && nowSaleorderType.indexOf("2")>=0)){
			layer.alert("备货单、销售订单不允许同时被选择");
			$(obj).prop("checked", false);
			return false;
		}
		var goodsid = $(obj).siblings("input[name='saleorderId']").val();
		allSelect += Number($(obj).siblings("input[name='noBuyNum']").val());
		$("#selected").html(allSelect);
		//判断本次表格是否全选
		if(!$("input[alt2='"+goodsid+"']").prop("checked")){
			var falg =false;
			var oneTableNum =Number(0);
			$.each($("input[alt='"+goodsid+"']"),function(i,n){
				if($(this).prop("checked")){
					oneTableNum += Number($(this).siblings("input[name='noBuyNum']").val());
				}else{
					falg = true;
				}
			});
			$("span[alt1='"+goodsid+"']").html(oneTableNum);
			if(!falg){
				$("input[alt2='"+goodsid+"']").prop("checked", "checked");
			}
		}
		//判断当前页面是否已经全选
		var allSelect = true;
		$.each($("input[name='oneAllSelect']"),function(i,n){
			if(!$(this).prop("checked")){
				allSelect = false;
				return false;
			}
		});
		if(allSelect){
			$("input[name='allSelect']").prop("checked", "checked");
		}
	}else{
		var goodsId = $(obj).siblings("input[name='saleorderId']").val();
		var oneTableNum =Number(0);
		$.each($("input[alt='"+goodsId+"']"),function(i,n){
			if($(this).prop("checked")){
				oneTableNum += Number($(this).siblings("input[name='noBuyNum']").val());
			}
		});
		$("span[alt1='"+goodsId+"']").html(oneTableNum);
		$("input[alt2='"+goodsId+"']").prop("checked", false);
		$("input[name='allSelect']").prop("checked", false);
		var allSelect = 0;
		$.each($("input[name='oneSelect']"),function(i,n){
			if($(this).prop("checked")){
				allSelect += Number($(this).siblings("input[name='noBuyNum']").val());
			}
		});
		$("#selected").html(allSelect);
		var nowIsVirture ='';
		var objgoodsId = $(obj).attr("alt2");
		$.each($("input[name='oneSelect']"),function(i,n){
			$.each($("input[name='oneSelect']"),function(i,n){
				var goodsId = $(this).siblings("input[name='saleorderId']").val();
				if($(this).prop("checked") && objgoodsId != goodsId){
					nowIsVirture += $(this).siblings("input[name='isVirture']").val()+",";
				}
			});
		});
		//实物商品
		if(nowIsVirture!='' && nowIsVirture.indexOf("2")==-1){
			//生成采购费用订单置灰
			$("#addFeebuy").attr ("disabled","true");
			$("#addFeebuy").removeClass("bg-light-green");
			$("#addFeebuy").addClass("bg-light-grey");
			//采购订单可点击
			$("#newBuyorder").removeAttr ("disabled");
			$("#newBuyorder").removeClass("bg-light-grey");
			$("#newBuyorder").addClass("bg-light-green");
		}
		//虚拟商品
		else if(nowIsVirture!='' && nowIsVirture.indexOf("0")==-1 && nowIsVirture.indexOf("1")==-1){
			//生成采购订单置灰
			$("#newBuyorder").attr ("disabled","true");
			$("#newBuyorder").removeClass("bg-light-green");
			$("#newBuyorder").addClass("bg-light-grey");
			//采购费用单可点击
			$("#addFeebuy").removeAttr ("disabled");
			$("#addFeebuy").removeClass("bg-light-grey");
			$("#addFeebuy").addClass("bg-light-green");
		}else{
			if(nowIsVirture == ''){
				$("#addFeebuy").removeAttr ("disabled");
				$("#addFeebuy").removeClass("bg-light-grey");
				$("#addFeebuy").addClass("bg-light-green");
				$("#newBuyorder").removeAttr ("disabled");
				$("#newBuyorder").removeClass("bg-light-grey");
				$("#newBuyorder").addClass("bg-light-green");
			}else {
				//生成采购费用订单置灰
				$("#addFeebuy").attr("disabled", "true");
				$("#addFeebuy").removeClass("bg-light-green");
				$("#addFeebuy").addClass("bg-light-grey");
				//采购订单可点击
				$("#newBuyorder").removeAttr("disabled");
				$("#newBuyorder").removeClass("bg-light-grey");
				$("#newBuyorder").addClass("bg-light-green");
			}
		}
	}
}
//单个表格全选
function oneAllSelect(obj){
	checkLogin();
	if($(obj).prop("checked")){
		//计算所有已选择的，排除刚点击的
		var allSelect = 0;
		var allnos = '';
		var isDirects = '';
		var isGift = '';
		var beforeIsVirture ='';
		var saleorderNoss = '';
		var objgoodsId = $(obj).attr("alt2");
		var newAllSaleorderType = '';
		$.each($("input[name='oneSelect']"),function(i,n){
			var goodsId = $(this).siblings("input[name='saleorderId']").val();
			if($(this).prop("checked") && objgoodsId != goodsId){
				allnos += $(this).siblings("input[name='saleorderNo']").val().substring(0,2)+",";
				newAllSaleorderType += $(this).siblings("input[name='saleorderType']").val()+",";
				isDirects += $(this).siblings("input[name='deliveryDirect']").val()+",";
				isGift += $(this).siblings("input[name='isGift']").val()+",";
				beforeIsVirture += $(this).siblings("input[name='isVirture']").val()+",";
				saleorderNoss += $(this).siblings("input[name='saleorderNo']").val()+",";
				allSelect += Number($(this).siblings("input[name='noBuyNum']").val());
			}
		});
		$("#selected").html(allSelect);

		//本次全选的表格
		var flag = false;
		var oneTableNum = Number(0);
		var giftList = '';
		var afterIsVirtureList = '';

		$.each($("input[alt='"+objgoodsId+"']"),function(i,n){
			var direct = $(this).siblings("input[name='deliveryDirect']").val();
			var gift = $(this).siblings("input[name='isGift']").val();
			var oneIsVirture = $(this).siblings("input[name='isVirture']").val();
			afterIsVirtureList += $(this).siblings("input[name='isVirture']").val()+",";
			giftList += $(this).siblings("input[name='isGift']").val()+",";
			var oneno = $(this).siblings("input[name='saleorderNo']").val().substring(0,2);
			var saleorderNo = $(this).siblings("input[name='saleorderNo']").val();
			var nowSaleorderType = $(this).siblings("input[name='saleorderType']").val();
			if(isDirects!=''&&((isDirects.indexOf("0")>=0&&direct.indexOf("1")>=0)||(isDirects.indexOf("1")>=0&&direct.indexOf("0")>=0))){
				layer.alert("直发与普发不允许同时被选择");
				flag = true;
			}else if(isDirects!=''&&isDirects.indexOf("0")<0&&direct.indexOf("1")>=0&&saleorderNoss!=''&&saleorderNoss.indexOf(saleorderNo)<0 ){
				layer.alert("直发仅允许选择同订单直发产品");
				flag = true;
			}
			if ((isGift != ''&&((isGift.indexOf("0")>=0&&gift.indexOf("1")>=0)||(isGift.indexOf("1")>=0&&gift.indexOf("0")>=0))) ||
				(giftList!=''&&((giftList.indexOf("0")>=0&&gift.indexOf("1")>=0)||(giftList.indexOf("1")>=0&&gift.indexOf("0")>=0)))){
				layer.alert("赠品产品与非赠品产品不允许同时被选择");
				flag = true;
			}
			//如果选中均为实物商品，【生成采购费用订单】按钮置灰不可点击
			//之前选择的集合中都为实物商品，后面单个选择和组合选择的也都为实物商品
			if((beforeIsVirture!='' && beforeIsVirture.indexOf("2")==-1 && oneIsVirture.indexOf("2")==-1 && afterIsVirtureList.indexOf("2")==-1)||
				beforeIsVirture=='' && oneIsVirture.indexOf("2")==-1 && afterIsVirtureList.indexOf("2")==-1){
				//生成采购费用订单置灰
				$("#addFeebuy").attr ("disabled","true");
				$("#addFeebuy").removeClass("bg-light-green");
				$("#addFeebuy").addClass("bg-light-grey");
				//生成采购单可点击
				$("#newBuyorder").removeAttr ("disabled");
				$("#newBuyorder").removeClass("bg-light-grey");
				$("#newBuyorder").addClass("bg-light-green");
			}
			//如果选中均为虚拟商品，【生成采购订单】按钮置灰不可点击
			//之前选择的集合中都为虚拟商品，后面单个选择和组合选择的也都为虚拟商品
			else if((beforeIsVirture!='' && beforeIsVirture.indexOf("0")==-1 && beforeIsVirture.indexOf("1")==-1 && oneIsVirture.indexOf("0")==-1 && oneIsVirture.indexOf("1")==-1
				&& afterIsVirtureList.indexOf("0")==-1 && afterIsVirtureList.indexOf("1")==-1)||
				(beforeIsVirture=='' && oneIsVirture.indexOf("0")==-1 && oneIsVirture.indexOf("1")==-1
					&& afterIsVirtureList.indexOf("0")==-1 && afterIsVirtureList.indexOf("1")==-1)){
				//生成采购订单置灰
				$("#newBuyorder").attr ("disabled","true");
				$("#newBuyorder").removeClass("bg-light-green");
				$("#newBuyorder").addClass("bg-light-grey");
				//生成采购费用订单可点击
				$("#addFeebuy").removeAttr ("disabled");
				$("#addFeebuy").removeClass("bg-light-grey");
				$("#addFeebuy").addClass("bg-light-green");
			}else{
				//生成采购费用订单置灰
				$("#addFeebuy").attr ("disabled","true");
				$("#addFeebuy").removeClass("bg-light-green");
				$("#addFeebuy").addClass("bg-light-grey");
				//生成采购单可点击
				$("#newBuyorder").removeAttr ("disabled");
				$("#newBuyorder").removeClass("bg-light-grey");
				$("#newBuyorder").addClass("bg-light-green");
			}


			if(newAllSaleorderType!=''&&(newAllSaleorderType.indexOf("2")>=0&&(nowSaleorderType.indexOf("0")>=0||nowSaleorderType.indexOf("3")>=0)
				||((newAllSaleorderType.indexOf("0")>=0||newAllSaleorderType.indexOf("3")>=0)&&nowSaleorderType.indexOf("2")>=0))){
				layer.alert("备货单、销售订单不允许同时被选择");
				flag = true;
			}
			isDirects += $(this).siblings("input[name='deliveryDirect']").val()+",";
			allnos += $(this).siblings("input[name='saleorderNo']").val().substring(0,2)+",";
			saleorderNoss += $(this).siblings("input[name='saleorderNo']").val()+",";
			oneTableNum += Number($(this).siblings("input[name='noBuyNum']").val());
		});
		if(!flag){
			$.each($("input[alt='"+objgoodsId+"']"),function(i,n){
				$(this).prop("checked","checked");
			})
			$("span[alt1='"+objgoodsId+"']").html(oneTableNum);
			allSelect += Number(oneTableNum);
			$("#selected").html(allSelect);
		}else{
			$(obj).prop("checked",false);
		}
		//判断当前页面是否已经全选
		var allSelect = true;
		$.each($("input[name='oneAllSelect']"),function(i,n){
			if(!$(this).prop("checked")){
				allSelect = false;
				return false;
			}
		});
		if(allSelect){
			$("input[name='allSelect']").prop("checked", "checked");
		}

	}else{
		var allSelect = 0;
		var objgoodsId = $(obj).attr("alt2");
		$.each($("input[alt='"+objgoodsId+"']"),function(i,n){
			$(this).prop("checked",false);
		})
		$("span[alt1='"+objgoodsId+"']").html(0);
		$.each($("input[name='oneSelect']"),function(i,n){
			if($(this).prop("checked")){
				allSelect += Number($(this).siblings("input[name='noBuyNum']").val());
			}
		});
		$("#selected").html(allSelect);
		$("input[name='allSelect']").prop("checked", false);
		var nowIsVirture ='';
		var objgoodsId = $(obj).attr("alt2");
		$.each($("input[name='oneSelect']"),function(i,n){
			$.each($("input[name='oneSelect']"),function(i,n){
				var goodsId = $(this).siblings("input[name='saleorderId']").val();
				if($(this).prop("checked") && objgoodsId != goodsId){
					nowIsVirture += $(this).siblings("input[name='isVirture']").val()+",";
				}
			});
		});
		if(nowIsVirture!='' && nowIsVirture.indexOf("2")==-1){
			//生成采购费用订单置灰
			$("#addFeebuy").attr ("disabled","true");
			$("#addFeebuy").removeClass("bg-light-green");
			$("#addFeebuy").addClass("bg-light-grey");
		}else{
			$("#addFeebuy").removeAttr ("disabled");
			$("#addFeebuy").removeClass("bg-light-grey");
			$("#addFeebuy").addClass("bg-light-green");
		}
		if(nowIsVirture!='' && nowIsVirture.indexOf("0")==-1 && nowIsVirture.indexOf("1")==-1){
			//生成采购费用订单置灰
			$("#newBuyorder").attr ("disabled","true");
			$("#newBuyorder").removeClass("bg-light-green");
			$("#newBuyorder").addClass("bg-light-grey");
		}else{
			$("#newBuyorder").removeAttr ("disabled");
			$("#newBuyorder").removeClass("bg-light-grey");
			$("#newBuyorder").addClass("bg-light-green");
		}

	}
}

function allSelect(obj){
	checkLogin();
	if($(obj).prop("checked")){
		var allSelect = 0;
		var allnos = '';
		var isDirects = '';
		var saleorderNoss = '';
		var newAllSaleorderType = '';
		var flag = false;
		$.each($("input[name='oneSelect']"),function(i,n){
			var goodsId = $(this).siblings("input[name='saleorderId']").val();
			if($(this).prop("checked") && objgoodsId != goodsId){
				allnos += $(this).siblings("input[name='saleorderNo']").val().substring(0,2)+",";
				newAllSaleorderType += $(this).siblings("input[name='saleorderType']").val()+",";
				isDirects += $(this).siblings("input[name='deliveryDirect']").val()+",";
				saleorderNoss += $(this).siblings("input[name='saleorderNo']").val()+",";
				allSelect += Number($(this).siblings("input[name='noBuyNum']").val());
			}
		});
		var giftList='';
		$.each($("input[name='oneSelect']"),function(i,n){
			var oneno = $(this).siblings("input[name='saleorderNo']").val().substring(0,2)+",";
			var direct = $(this).siblings("input[name='deliveryDirect']").val()+",";
			giftList += $(this).siblings("input[name='isGift']").val()+",";
			var saleorderNo = $(this).siblings("input[name='saleorderNo']").val()+",";
			var nowSaleorderType = $(this).siblings("input[name='saleorderType']").val()+",";
			var buynum = Number($(this).siblings("input[name='noBuyNum']").val());
			var goodsId = $(this).siblings("input[name='saleorderId']").val();
			if(isDirects!=''&&((isDirects.indexOf("0")>=0&&direct.indexOf("1")>=0)||(isDirects.indexOf("1")>=0&&direct.indexOf("0")>=0))){
				layer.alert("直发与普发不允许同时被选择");
				$(this).prop("checked", false);

				buynum = Number(0);
				flag = true;
				return false;
			}else if(isDirects!=''&&isDirects.indexOf("0")<0&&isDirects.indexOf("1")>=0&&saleorderNoss!=''&&saleorderNoss.indexOf(saleorderNo)<0 ){
				layer.alert("直发仅允许选择同订单直发产品");
				$(this).prop("checked", false);
				buynum = Number(0);
				flag = true;
				return false;
			}
			if (giftList.indexOf("0")>=0 && giftList.indexOf("1")>=0){
				layer.alert("赠品产品与非赠品产品不允许同时被选择");
				$(this).prop("checked", false);
				flag = true;
				return false;
			}
			// if(allnos!=''&&(allnos.indexOf("BH")>=0&&(oneno.indexOf("VS")>=0||oneno.indexOf("DH")>=0||oneno.indexOf("JX")>=0)
			// 		||((allnos.indexOf("VS")>=0||allnos.indexOf("DH")>=0||allnos.indexOf("JX")>=0)&&oneno.indexOf("BH")>=0))){
			// 	layer.alert("备货单、销售订单不允许同时被选择");
			// 	$(this).prop("checked", false);
			// 	buynum = Number(0);
			// 	flag = true;
			// 	return false;
			// }
			if(newAllSaleorderType!=''&&(newAllSaleorderType.indexOf("2")>=0&&(nowSaleorderType.indexOf("0")>=0||nowSaleorderType.indexOf("3")>=0)
				||((newAllSaleorderType.indexOf("0")>=0||newAllSaleorderType.indexOf("3")>=0)&&nowSaleorderType.indexOf("2")>=0))){
				layer.alert("备货单、销售订单不允许同时被选择");
				$(this).prop("checked", false);
				buynum = Number(0);
				flag = true;
				return false;
			}
			$(this).prop("checked",true);
			if(!$("input[alt2='"+goodsId+"']").prop("checked")){
				//判断单个表是否全选
				var falg =false;
				var oneTableNum =Number(0);
				$.each($("input[alt='"+goodsId+"']"),function(i,n){
					oneTableNum += Number($(this).siblings("input[name='noBuyNum']").val());
					if(!$(this).prop("checked")){
						falg = true;
					}
				});
				$("span[alt1='"+goodsId+"']").html(oneTableNum);
				if(!falg){
					$("input[alt2='"+goodsId+"']").prop("checked", "checked");
				}
			}
			allnos +=oneno;
			isDirects +=direct;
			saleorderNoss +=saleorderNo;
			allSelect += buynum;

		});
		if(flag){
			$.each($("input[name='oneSelect']"),function(i,n){
				if($(this).prop("checked")){
					$(this).prop("checked", false);
				}
			});
			$.each($("input[name='oneAllSelect']"),function(i,n){
				if($(this).prop("checked")){
					$(this).prop("checked", false);
				}
			});
			$(obj).prop("checked", false);
		}else{
			$("#selected").html(allSelect);
		}

	}else{
		$.each($("input[name='oneSelect']"),function(i,n){
			$(this).prop("checked", false);
		});
		$.each($("input[name='oneAllSelect']"),function(i,n){
			$(this).prop("checked", false);
			var goodsId = $(this).attr("alt2");
			$("span[alt1='"+goodsId+"']").html(0);
		});
		$("#selected").html(0);
	}
}

function changeOrg(obj){
	checkLogin();
	var orgId = $(obj).val();

	if(orgId != ''){
		$.ajax({
			type: "POST",
			url: page_url+"/order/buyorder/getProductorUserList.do",
			data: {'orgId':orgId},
			dataType:'json',
			success: function(data){
				if(data.code==0){
					$("#proUserId").empty();
					var option = '<option value="">全部</option>';
					for(var i=0; i<data.listData.length;i++){
						option +='<option value="'+data.listData[i].userId+'">'+data.listData[i].username+'</option>'
					}
					$("#proUserId").append(option);
				}else{
					layer.alert(data.message);
				}
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
	}
	//选择全部显示全部
	if (orgId == '') {
		$.ajax({
			type: "POST",
			url: page_url+"/order/buyorder/getProductorUserListCount.do",
			dataType:'json',
			success: function(data){
				if(data.code==0){
					$("#proUserId").empty();
					var option = '<option value="">全部</option>';
					for(var i=0; i<data.listData.length;i++){
						option +='<option value="'+data.listData[i].userId+'">'+data.listData[i].username+'</option>'
					}
					$("#proUserId").append(option);
				}else{
					layer.alert(data.message);
				}
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
	}
}
function changeTab(index) {
	let urlSaleorderNo = $('#urlSaleorderNo').val();
	window.location = '/order/buyorder/indexPendingPurchase.do?tabFlag=' + index + '&urlSaleorderNo=' + urlSaleorderNo;
}
function buyNow(saleorderId){
	checkLogin();
	var saleorderGoodsIds = "";
	$.each($("input[alt='"+saleorderId+"']"),function(i,n){
		if($(this).prop("checked")){
			saleorderGoodsIds += $(this).siblings("input[name='saleorderGoodId']").val().split('|')[1]+",";
		}
	});
	saleorderGoodsIds=saleorderGoodsIds.substring(0,saleorderGoodsIds.length-1);
	if(saleorderGoodsIds != '') {
		$.ajax({
			type: "POST",
			url: page_url + "/order/buyorder/changeComponent.do",
			//立即采购在REMARK表中的ID为2
			data: {'saleorderGoodsIds': saleorderGoodsIds,'componentId': 2},
			dataType: 'json',
			success: function (data) {
				if (data.code == 0) {
					layer.alert(data.message);
					location.reload();
				}
			},
			error: function (data) {
				if (data.status == 1001) {
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
				}
			}
		});
	}else{
		layer.alert("请选择数据。");
	}
}
function buyLater(saleorderId){
	checkLogin();
	var saleorderGoodsIds = "";
	$.each($("input[alt='"+saleorderId+"']"),function(i,n){
		if($(this).prop("checked")){
			saleorderGoodsIds += $(this).siblings("input[name='saleorderGoodId']").val().split('|')[1]+",";
		}
	});
	saleorderGoodsIds=saleorderGoodsIds.substring(0,saleorderGoodsIds.length-1);
	if(saleorderGoodsIds != '') {
		//立即采购在REMARK表中的ID为3
		var url = page_url+"/order/buyorder/showResonView.do?saleorderGoodsIds="+saleorderGoodsIds+"&componentId="+3;
		$("#addvbview").attr('layerParams','{"width":"515px","height":"240px","title":"修改采购要求","link":"'+url+'"}');
		$("#addvbview").click();
	}else{
		layer.alert("请选择数据。");
	}
}
function dontBuy(saleorderId){
	checkLogin();
	var saleorderGoodsIds = "";
	$.each($("input[alt='"+saleorderId+"']"),function(i,n){
		if($(this).prop("checked")){
			saleorderGoodsIds += $(this).siblings("input[name='saleorderGoodId']").val().split('|')[1]+",";
		}
	});
	saleorderGoodsIds=saleorderGoodsIds.substring(0,saleorderGoodsIds.length-1);
	if(saleorderGoodsIds != '') {
		//无需采购在REMARK表中的ID为14
		var url = page_url+"/order/buyorder/showResonView.do?saleorderGoodsIds="+saleorderGoodsIds+"&componentId="+14;
		$("#addvbview").attr('layerParams','{"width":"515px","height":"240px","title":"修改采购要求","link":"'+url+'"}');
		$("#addvbview").click();
	}else{
		layer.alert("请选择数据。");
	}
}
function init(){
	if($("#orderType").val()==2){
		$("#addvbbutton").css("display","none");
	}else{
		$("#addvbbutton").css("display","");
	}
}

function searchReset() {
	$('#urlSaleorderNo').val('');
	reset();
}

function exportDetail(){
	checkLogin();
	location.href = page_url + '/order/buyorder/indexPendingPurchaseExport.do';
}
