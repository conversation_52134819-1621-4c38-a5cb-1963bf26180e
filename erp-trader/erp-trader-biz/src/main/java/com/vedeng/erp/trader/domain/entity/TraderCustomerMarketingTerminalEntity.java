package com.vedeng.erp.trader.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 客户营销-终端属性表
 * @date 2023/8/7 10:17
 */
@Getter
@Setter
public class TraderCustomerMarketingTerminalEntity extends BaseEntity {


    /**
     * 主键
     */
    private Integer traderCustomerMarketingId;

    /**
     * 交易者id
     */
    private Integer traderId;

    /**
     * 客户id
     */
    private Integer traderCustomerId;

    /**
     * 机构类型
     * 0	综合
     * 1	专科
     * 2	疾病预防控制中心
     * 3	妇幼保健机构
     * 4	专科疾病防治院（所、站）
     * 5	卫生监督所（中心）
     * 6	计划生育技术服务机构
     * 7	陆军
     * 8	海军
     * 9	空军
     * 10	火箭军
     * 11	战略支援部队
     * 12	联勤保障医院
     * 13	战区医院
     * 14	特色医学中心
     * 15	军医大学附属医院
     * 16	武警医院
     * 17	武警支队
     * 18	离退休干部休养所
     * 19	人民武装部
     * 20	军队院校
     * 21	卫勤训练中心
     * 22	联勤保障采购站
     * 23	第三方检验中心
     * 24	第三方影像中心
     * 25	体检中心
     * 26	血透中心
     * 27	个人
     * 28	企业（国企、外企、民企）
     * 29	政府
     * 30	事业单位
     * 31	大学
     * 32	工厂
     * 33	公检法
     * 34	红会
     * 35	教育局
     * 36	救援队
     * 37	企业楼宇
     * 38	消防
     * 39	学校
     * 40	医院
     * 41	银行
     * 42	急救中心
     * 43	应急管理局
     * 44	基金会
     * 45	文旅局
     * 46	旅游景区
     * 47	体育局
     * 48	卫健委
     */
    private String institutionType;

    /**
     * 机构评级
     * 0	一级医院
     * 1	二级医院
     * 2	三级医院
     * 3	未定级医院
     * 4	社区卫生服务中心（站）
     * 5	乡镇卫生院
     * 6	诊所（医务室）
     * 7	村卫生室
     * 8	应急三级医院
     * 9	应急二级医院
     * 10	应急基层医疗
     */
    private String institutionLevel;

    /**
     * 机构性质（0公立 1非公）
     */
    private String institutionNature;

    /**
     * 经营状态
     */
    private String managementForms;

    /**
     * 法人代表
     */
    private String legalRepresentative;

    /**
     * 床位数
     */
    private String bedNumber;

    /**
     * 科室
     */
    private String hospitalDepartment;

    /**
     * 营销客户类型
     * 0 等级医院
     * 1 基层医疗
     * 2 应急医疗机构
     * 3 专业公共卫生机构
     * 4 其他医疗机构
     * 5 非医疗机构
     */
    private Integer traderCustomerMarketingType;

    /**
     * 机构类型子集
     * 0 康复
     * 1 医美
     * 2 口腔
     * 3 眼科
     * 4 骨科
     * 5 妇产
     * 6 其他
     */
    private String institutionTypeChild;

    /**
     * 经销商查询关联的终端ids
     */
    private String terminalIds;

    /**
     * 其他机构类型
     */
    private String otherInstitutionType;


    public TraderCustomerMarketingTerminalEntity init() {
        TraderCustomerMarketingTerminalEntity entity = new TraderCustomerMarketingTerminalEntity();
        entity.setManagementForms("");
        entity.setBedNumber("");
        entity.setLegalRepresentative("");
        entity.setOtherInstitutionType("");
        entity.setTerminalIds("");
        entity.setHospitalDepartment("");
        return entity;
    }


}