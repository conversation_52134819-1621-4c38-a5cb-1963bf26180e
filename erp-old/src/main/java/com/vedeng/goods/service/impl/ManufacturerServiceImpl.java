package com.vedeng.goods.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.comparator.CompareUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.vedeng.authorization.dao.RoleMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.goods.GoodsConstants;
import com.vedeng.common.constant.goods.LogTypeEnum;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.exception.ShowErrorMsgException;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.redis.utils.RedisUtil;
import com.vedeng.common.shiro.SpringContextHolder;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.EmptyUtils;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.docSync.service.SyncSupplierService;
import com.vedeng.goods.dao.LogCheckGenerateMapper;
import com.vedeng.goods.enums.ManufacturerStatusEnum;
import com.vedeng.goods.manufacturer.constants.ManufacturerConstants;
import com.vedeng.goods.manufacturer.dao.ManufacturerMapper;
import com.vedeng.goods.manufacturer.model.Manufacturer;
import com.vedeng.goods.model.LogCheckGenerate;
import com.vedeng.goods.model.LogCheckGenerateExample;
import com.vedeng.goods.service.ManufacturerService;
import com.vedeng.goods.service.VgoodsService;
import com.vedeng.system.dao.AttachmentMapper;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.service.ThreeCertificatesStamp;
import com.vedeng.trader.dao.TraderSupplierMapper;
import com.vedeng.trader.model.TraderSupplier;
import com.vedeng.trader.service.TraderSupplierService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.method.P;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import springfox.documentation.spring.web.json.Json;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ManufacturerServiceImpl implements ManufacturerService, com.vedeng.goods.service.impl.ManufacturerService {

    @Resource
    private ManufacturerMapper manufacturerMapper;

    @Autowired
    private AttachmentMapper attachmentMapper;

    @Autowired
    private TraderSupplierService traderSupplierService;

    @Resource
    private VgoodsService vgoodsService;

    @Resource
    private LogCheckGenerateMapper logCheckGenerateMapper;

    @Resource
    private TraderSupplierMapper traderSupplierMapper;

    @Resource
    private RoleMapper roleMapper;

    @Value("${api_http}")
    protected String apiHttp;

    @Override
    public Map<String, Object> getProductCompanyInfoListPage(Map<String, Object> paramMap, Page page, Manufacturer manufacturer) {

        Map<String, Object> resultMap = new HashMap<>();
        //查询全部的归属产品经理/助理
        List<User> userList = manufacturerMapper.findUserName();
        // 生产企业管理列表
        List<Manufacturer> manufacturerList = manufacturerMapper.getProductCompanyInfoListPage(paramMap);
        resultMap.put("manufacturerList", manufacturerList);
        resultMap.put("userList", userList);
        resultMap.put("page", page);
        return resultMap;
    }

    @Override
    public void save(Manufacturer manufacturer) {
        Manufacturer saveDo = new Manufacturer();
        BeanUtils.copyProperties(manufacturer, saveDo);

        if (saveDo.getManufacturerId() != null && saveDo.getManufacturerId() > 0) {
            saveDo.setUpdateTime(new Date());
            manufacturerMapper.updateByPrimaryKeySelective(saveDo);
        } else {
            if (StringUtils.isBlank(saveDo.getManufacturerName())) {
                throw new RuntimeException("生产企业保存失败，名称不可为空！");
            }
            saveDo.setUpdateTime(new Date());
            saveDo.setAddTime(new Date());
            manufacturerMapper.insertSelective(saveDo);
            manufacturer.setManufacturerId(saveDo.getManufacturerId());
        }
    }

    @Override
    public void saveTimeChange(Manufacturer manufacturer) {
        Manufacturer saveDo = new Manufacturer();
        BeanUtils.copyProperties(manufacturer, saveDo);

        if (saveDo.getManufacturerId() != null && saveDo.getManufacturerId() > 0) {
            saveDo.setUpdateTime(new Date());
            manufacturerMapper.updateByPrimaryKeySelectiveTimeChange(saveDo);
        } else {
            if (StringUtils.isBlank(saveDo.getManufacturerName())) {
                throw new RuntimeException("生产企业保存失败，名称不可为空！");
            }
            saveDo.setUpdateTime(new Date());
            saveDo.setAddTime(new Date());
            manufacturerMapper.insertSelective(saveDo);
            manufacturer.setManufacturerId(saveDo.getManufacturerId());
        }
    }

    @Override
    public boolean isValidByRegistrationNumber(Integer registrationNumberId) {
        Objects.requireNonNull(registrationNumberId, "验证失败，注册证id不可为空");

        return manufacturerMapper.isValidByRegistrationNumberId(registrationNumberId, ManufacturerStatusEnum.CHECK_SUCCESS.getStatus()) > 0;
    }

    /**
     * 校验生产列表信息
     *
     * @param manufacturer
     * @throws ShowErrorMsgException
     */
    @Override
    public void checkProductInfo(Manufacturer manufacturer) throws ShowErrorMsgException {
        if (null == manufacturer) {
            throw new ShowErrorMsgException(CommonConstants.RESULTINFO_CODE_FAIL_1.toString(), "请填写生产列表信息！");
        }

        if (manufacturer.getManufacturerName() == null || "".equals(manufacturer.getManufacturerName())) {
            throw new ShowErrorMsgException(CommonConstants.RESULTINFO_CODE_FAIL_1.toString(), "请输入生产企业名称！");
        }
        manufacturer.setManufacturerName(StringUtils.trim(manufacturer.getManufacturerName()));//将名称进行trim处理

        Map<String, Object> paramMap = new HashMap<>(4);
        paramMap.put("manufacturerName", manufacturer.getManufacturerName());
        //判断 ManufacturerId是否为空 如果为空 则代表是新增
        if (manufacturer.getManufacturerId() != null) {
            paramMap.put("manufacturerId", manufacturer.getManufacturerId());
        }
        // 根据名称查询是否存在
        List<String> res = manufacturerMapper.getManufacturerVoInfoByStr(paramMap);
        if (CollectionUtils.isNotEmpty(res)) {
            throw new ShowErrorMsgException(CommonConstants.RESULTINFO_CODE_FAIL_1.toString(), "该生产企业名称已存在！");
        }

        List<TraderSupplier> traderSupplier = traderSupplierMapper.getTraderSupplierByTraderNameAndTraderType(manufacturer.getManufacturerName(), 0);
        if (CollUtil.isNotEmpty(traderSupplier)) {
            throw new ShowErrorMsgException(CommonConstants.RESULTINFO_CODE_FAIL_1, "该企业已存在且类型为“渠道商”！请至ERP-供应链管理-供应商列表，对其类型进行修改");
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer addProductInfo(Manufacturer manufacturer, Integer userId, boolean resetAudit) {
        if (manufacturer.getManufacturerId() != null) {
            manufacturer.setIsUpload(getCurUploadStatusById(manufacturer.getManufacturerId()));
            manufacturer.setUpdateNo(userId);
            manufacturer.setSignature(0);
            if (resetAudit) {
                manufacturer.setStatus(ManufacturerStatusEnum.UN_SUBMIT.getStatus());
            }
        } else {
            manufacturer.setIsUpload(ErpConst.STATUS_STATE.NO);
            manufacturer.setStatus(ManufacturerStatusEnum.UN_SUBMIT.getStatus());
            manufacturer.setAddNo(userId);
            manufacturer.setSignature(0);
        }
        this.save(manufacturer);
        // 保存首营生产厂商对应附件
        this.saveManufacturerAttachments(manufacturer, userId);

        // 同步到【供应商生产厂商】
        log.info("新增【首营生产厂商】,触发同步信息到 【供应商生产厂商】");
        try {
            Manufacturer manufacturerQuery = manufacturerMapper.selectByPrimaryKey(manufacturer.getManufacturerId());
            traderSupplierService.manufacturerSyncTraderSupplier(manufacturerQuery);
        } catch (Exception e) {
            try {
                log.error("新增【首营生产厂商】，同步供应商出错 msg :{}", manufacturer.toString(), e);
            }catch (Exception e2){log.error("",e);}
        }
        return manufacturer.getManufacturerId();
    }
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer addProductInfoTimeChange(Manufacturer manufacturer, Integer userId, boolean resetAudit) {
        if (manufacturer.getManufacturerId() != null) {
            manufacturer.setIsUpload(getCurUploadStatusById(manufacturer.getManufacturerId()));
            manufacturer.setUpdateNo(userId);
            manufacturer.setSignature(0);
            if (resetAudit) {
                manufacturer.setStatus(ManufacturerStatusEnum.UN_SUBMIT.getStatus());
            }
        } else {
            manufacturer.setIsUpload(ErpConst.STATUS_STATE.NO);
            manufacturer.setStatus(ManufacturerStatusEnum.UN_SUBMIT.getStatus());
            manufacturer.setAddNo(userId);
            manufacturer.setSignature(0);
        }
        this.saveTimeChange(manufacturer);
        // 保存首营生产厂商对应附件
        this.saveManufacturerAttachments(manufacturer, userId);

        // 同步到【供应商生产厂商】
        log.info("新增【首营生产厂商】,触发同步信息到 【供应商生产厂商】");
        try {
            Manufacturer manufacturerQuery = manufacturerMapper.selectByPrimaryKey(manufacturer.getManufacturerId());
            traderSupplierService.manufacturerSyncTraderSupplier(manufacturerQuery);
        } catch (Exception e) {
            try {
                log.error("新增【首营生产厂商】，同步供应商出错 msg :{}", manufacturer.toString(), e);
            }catch (Exception e2){log.error("",e);}
        }
        return manufacturer.getManufacturerId();
    }

    /**
     * 获取当前上传状态
     *
     * @param manufacturerId
     * @return
     */
    private int getCurUploadStatusById(Integer manufacturerId) {
        Map<String, Object> tempQueryMap = Maps.newHashMapWithExpectedSize(2);
        tempQueryMap.put("attachmentFunction", ManufacturerConstants.ATTACHMENT_FUN_WITH_B);
        tempQueryMap.put("manufacturerId", manufacturerId);
        tempQueryMap.put("attachmentType", CommonConstants.ATTACHMENT_TYPE_974);
        List<Manufacturer> list = manufacturerMapper.IsUpLoad(tempQueryMap);

        int uploadStatus = ErpConst.STATUS_STATE.NO;
        if (list.size() == ManufacturerConstants.ATTACHMENT_FUN_WITH_B.size()) {
            //根据类型去重查
            uploadStatus = ErpConst.STATUS_STATE.YES;
        } else if (list.size() > 0 && list.size() < ManufacturerConstants.ATTACHMENT_FUN_WITH_B.size()) {
            uploadStatus = ErpConst.STATUS_STATE.SOME;
        }

        return uploadStatus;
    }

    /**
     * @param manufacturer
     * @param userId
     */
    private void saveManufacturerAttachments(Manufacturer manufacturer, Integer userId) {
        List<Attachment> attachmentList = new LinkedList<>();
        // 注册登记表附件
        // 营业执照 1307
        if (CollectionUtils.isNotEmpty(manufacturer.getYzAttachments())) {
            attachmentList.addAll(manufacturer.getYzAttachments());
        }
        // 生产企业生产许可证 1306
        if (CollectionUtils.isNotEmpty(manufacturer.getScAttachments())) {
            attachmentList.addAll(manufacturer.getScAttachments());
        }

        // 生产企业备案凭证 1308
        if (CollectionUtils.isNotEmpty(manufacturer.getRcAttachments())) {
            attachmentList.addAll(manufacturer.getRcAttachments());
        }

        // 注册登记表附件 1305
        if (CollectionUtils.isNotEmpty(manufacturer.getDjbAttachments())) {
            attachmentList.addAll(manufacturer.getDjbAttachments());
        }

        //附件信息模块
        HashMap<String, Object> queryTempMap = Maps.newHashMapWithExpectedSize(3);
        queryTempMap.put("attachmentType", CommonConstants.ATTACHMENT_TYPE_974);
        queryTempMap.put("manufacturerId", manufacturer.getManufacturerId());
        queryTempMap.put("attachmentFunction", ManufacturerConstants.ATTACHMENT_FUN);
        //软删除之前的附件 新附件 更新 1307 1306 1305的照片
        attachmentMapper.updataByParamNew(queryTempMap);
        //新增附件信息
        if (CollectionUtils.isNotEmpty(attachmentList)) {
            for (Attachment attachment : attachmentList) {
                attachment.setCreator(userId);
                attachment.setAttachmentType(CommonConstants.ATTACHMENT_TYPE_974);
                attachment.setRelatedId(manufacturer.getManufacturerId());
                attachment.setIsDeleted(ErpConst.DELETE_STATE.IS_NOT_DELETE);
                attachment.setAddTime(System.currentTimeMillis());
                manufacturerMapper.insertAttachement(attachment);
            }
        }
    }

    @Override
    public void buildAttachmentFile(Manufacturer manufacturer) {
        this.buildAttachment(manufacturer.getManufacturerId(), manufacturer, ManufacturerConstants.ATTACHMENT_FUN);
    }

    private void buildAttachmentWithB(Manufacturer manufacturerTarget) {
        Objects.requireNonNull(manufacturerTarget, "manufacturerTarget不可为空！");

        buildAttachment(manufacturerTarget.getManufacturerId(), manufacturerTarget, ManufacturerConstants.ATTACHMENT_FUN_WITH_B);
    }

    /**
     * 构建附件文件信息
     *
     * @param manufacturerId     厂商id
     * @param manufacturerTarget 厂商
     * @param funList            附件应用类型 字典库list
     */
    private void buildAttachment(Integer manufacturerId, Manufacturer manufacturerTarget, List<Integer> funList) {
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(3);
        paramMap.put("attachmentType", CommonConstants.ATTACHMENT_TYPE_974);
        paramMap.put("attachmentFunction", funList);
        paramMap.put("registrationNumberId", manufacturerId);
        //查询新附件类型
        Map<Integer, List<Attachment>> attachmentMap = attachmentMapper.getUndeletedAttachmentsList(paramMap).stream()
                .filter(i -> !EmptyUtils.isEmpty(i.getUri()))
                .peek(item -> item.setHttpUrl(apiHttp + item.getDomain()))
                .collect(Collectors.groupingBy(Attachment::getAttachmentFunction));
        try {
            manufacturerTarget.setYzAttachments(attachmentMap.get(CommonConstants.ATTACHMENT_FUNCTION_1307));
            manufacturerTarget.setScAttachments(attachmentMap.get(CommonConstants.ATTACHMENT_FUNCTION_1306));
            manufacturerTarget.setDjbAttachments(attachmentMap.get(CommonConstants.ATTACHMENT_FUNCTION_1305));
            manufacturerTarget.setRcAttachments(attachmentMap.get(CommonConstants.ATTACHMENT_FUNCTION_1308));

            manufacturerTarget.setYzBAttachments(attachmentMap.get(CommonConstants.ATTACHMENT_FUNCTION_1302));
            manufacturerTarget.setScBAttachments(attachmentMap.get(CommonConstants.ATTACHMENT_FUNCTION_1303));
            manufacturerTarget.setDjbBAttachments(attachmentMap.get(CommonConstants.ATTACHMENT_FUNCTION_1304));
            manufacturerTarget.setRcBAttachments(attachmentMap.get(CommonConstants.ATTACHMENT_FUNCTION_1309));
        } catch (Exception e) {
            log.error("", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = true)
    public Manufacturer getManufacturerDetail(Integer manufacturerId) {
        //根据生产企业id查询信息
        Manufacturer manufacturer = manufacturerMapper.selectByPrimaryKey(manufacturerId);
        this.buildAttachment(manufacturerId, manufacturer, ManufacturerConstants.MANUFACTURER_ALL_FUN);
        RedisUtil.StringOps.set(manufacturerId.toString(), JSON.toJSONString(manufacturer));
        return manufacturer;
    }

    @Override
    public Manufacturer getOfficialB(Integer manufacturerId) {
        Manufacturer manufacturer = new Manufacturer();
        manufacturer.setManufacturerId(manufacturerId);
        buildAttachmentWithB(manufacturer);
        return manufacturer;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void uploadOfficialWithB(Manufacturer manufacturer, User sessUser) {
        List<Attachment> attachmentsList = new ArrayList<>();
        Long addTime = System.currentTimeMillis();
        //定义一个map存附件信息
        Map<String, Object> attachmentMap = Maps.newHashMapWithExpectedSize(5);
        //附件信息模块
        attachmentMap.put("attachmentType", CommonConstants.ATTACHMENT_TYPE_974);
        //生产企业id
        attachmentMap.put("manufacturerId", manufacturer.getManufacturerId());
        //插入的照片类型
        attachmentMap.put("attachmentFunction", ManufacturerConstants.ATTACHMENT_FUN_WITH_B);
        //将之前的附件状态更新为 1 新附件 更新 1302 1303 1304的照片
        attachmentMapper.updataByParamNew(attachmentMap);

        // 注册登记表附件
        // 营业执照（贝） 1302
        if (CollectionUtils.isNotEmpty(manufacturer.getYzBAttachments())) {
            attachmentsList.addAll(manufacturer.getYzBAttachments());
        }

        // 生产企业生产许可证（贝） 1303
        if (CollectionUtils.isNotEmpty(manufacturer.getScBAttachments())) {
            attachmentsList.addAll(manufacturer.getScBAttachments());
        }

        // 生产企业备案凭证（贝） 1309
        if (CollectionUtils.isNotEmpty(manufacturer.getRcBAttachments())) {
            attachmentsList.addAll(manufacturer.getRcBAttachments());
        }

        // 注册登记表附件（贝） 1304
        if (CollectionUtils.isNotEmpty(manufacturer.getDjbBAttachments())) {
            attachmentsList.addAll(manufacturer.getDjbBAttachments());
        }
        //新增附件信息
        if (CollectionUtils.isNotEmpty(attachmentsList)) {
            // 添加人
            attachmentMap.put("userId", sessUser.getUserId());
            attachmentMap.put("attachmentsList", attachmentsList);
            for (Attachment attachment : attachmentsList) {
                attachment.setCreator(sessUser.getUserId());
                attachment.setAttachmentType(CommonConstants.ATTACHMENT_TYPE_974);
                attachment.setRelatedId(manufacturer.getManufacturerId());
                attachment.setIsDeleted(ErpConst.DELETE_STATE.IS_NOT_DELETE);
                attachment.setAddTime(addTime);
                manufacturerMapper.insertAttachement(attachment);
            }
        }

        //更新T_MANUFACTURER
        manufacturer.setIsUpload(getCurUploadStatusById(manufacturer.getManufacturerId()));
        manufacturerMapper.updateIsUpLoad(manufacturer);
    }


    private boolean isNeedDel(Map<String, Object> paramMap) {
        return manufacturerMapper.findIsNeedDel(paramMap).size() > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultInfo deleteManufacturer(Integer manufacturerId, Integer userId) {
        Objects.requireNonNull(manufacturerId, "manufacturerId不可为空！");
        // 参数集
        Map<String, Object> paramMap = new HashMap<>(4);
        paramMap.put("manufacturerId", manufacturerId);
        paramMap.put("userId", userId);

        if (isNeedDel(paramMap)) {
            return new ResultInfo<>(1, "该生产企业已有商品上架，无法删除");
        } else {
            Manufacturer manufacturerQuery = manufacturerMapper.selectByPrimaryKey(manufacturerId);
            if (Objects.isNull(manufacturerQuery)) {
                log.info("该生产企业已经被删除, id :{}", manufacturerId);
                return new ResultInfo<>(0, "操作成功");
            }

            Integer relateId = traderSupplierService.isExistManufacturer(manufacturerQuery.getManufacturerName(), false);
            if (Objects.nonNull(relateId) && relateId > 0) {

                traderSupplierService.isDisabled(userId, relateId, 0, "首营管理列表-生产企业被删除");
            }

            Manufacturer deleteDo = new Manufacturer();
            deleteDo.setUpdateNo(userId);
            deleteDo.setManufacturerId(manufacturerId);
            deleteDo.setIsDelete(ErpConst.DELETE_STATE.IS_DELETE);
            save(deleteDo);

            return new ResultInfo<>(0, "操作成功");
        }
    }

    /**
     * 审核生产企业处理
     *
     * @param manufacturer status
     * @param sessUser     当前user
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditManufacture(Manufacturer manufacturer, User sessUser) {
        if (null == manufacturer || null == manufacturer.getManufacturerId()) {
            throw new ShowErrorMsgException("找不到对应的生产企业信息！");
        }
        if (ManufacturerStatusEnum.CHECK_FAIL.getStatus().equals(manufacturer.getStatus()) && EmptyUtils.isBlank(manufacturer.getReason())) {
            throw new ShowErrorMsgException("审核不通过原因不能为空！");
        }

        Integer manufacturerId = manufacturer.getManufacturerId();
        // 校验是否有改动
        Manufacturer manufacturerQuery = manufacturerMapper.selectByPrimaryKey(manufacturerId);
        this.buildAttachment(manufacturerId, manufacturerQuery, ManufacturerConstants.MANUFACTURER_ALL_FUN);
        String jsonManufacturer = RedisUtil.StringOps.get(manufacturerId.toString());
        Manufacturer oldManufacturer = JSON.parseObject(jsonManufacturer, Manufacturer.class);
        String errMessageTemp = "{}资质已更新";
        List<String> errList = new ArrayList<>();

        // fixme: 重构供应链相关时，优化
        String newYzAttachments = JSON.toJSONString(Convert.toList(Attachment.class, manufacturerQuery.getYzAttachments())
                .stream().map(Attachment::getUri)
                .filter(Objects::nonNull).sorted().collect(Collectors.toList()));
        String oldYzAttachments = JSON.toJSONString(Convert.toList(Attachment.class, oldManufacturer.getYzAttachments())
                .stream().map(Attachment::getUri)
                .filter(Objects::nonNull).sorted().collect(Collectors.toList()));
        if (!newYzAttachments.equals(oldYzAttachments)) {
            errList.add("营业执照");
        }

        if (!Convert.toLong(manufacturerQuery.getBcIssueDate(),1L).equals(Convert.toLong(oldManufacturer.getBcIssueDate(),1L))) {
            errList.add("营业执照发证日期");
        }

        if (!Convert.toLong(manufacturerQuery.getBcStartTime(),1L).equals(Convert.toLong(oldManufacturer.getBcStartTime(),1L))) {
            errList.add("营业执照有效日期开始时间");
        }

        if (!Convert.toLong(manufacturerQuery.getBcEndTime(),1L).equals(Convert.toLong(oldManufacturer.getBcEndTime(),1L))) {
            errList.add("营业执照有效日期结束时间");
        }


        String newScAttachments = JSON.toJSONString(Convert.toList(Attachment.class, manufacturerQuery.getScAttachments()).stream().map(Attachment::getUri)
                .filter(Objects::nonNull).sorted().collect(Collectors.toList()));
        String oldScAttachments = JSON.toJSONString(Convert.toList(Attachment.class, oldManufacturer.getScAttachments()).stream().map(Attachment::getUri)
                .filter(Objects::nonNull).sorted().collect(Collectors.toList()));
        if (!newScAttachments.equals(oldScAttachments)) {
            errList.add("生产企业生产许可证");
        }

        if (!Convert.toLong(manufacturerQuery.getPeStartTime(),1L).equals(Convert.toLong(oldManufacturer.getPeStartTime(),1L))) {
            errList.add("生产企业许可证开始时间");
        }

        if (!Convert.toLong(manufacturerQuery.getPeEndTime(),1L).equals(Convert.toLong(oldManufacturer.getPeEndTime(),1L))) {
            errList.add("生产企业许可证结束时间");
        }

        if (!Convert.toStr(manufacturerQuery.getProductCompanyLicence(),"").equals(Convert.toStr(oldManufacturer.getProductCompanyLicence(),""))) {
            errList.add("生产企业生产许可证编号");
        }


        String newRcAttachments = JSON.toJSONString(Convert.toList(Attachment.class, manufacturerQuery.getRcAttachments()).stream().map(Attachment::getUri)
                .filter(Objects::nonNull).sorted().collect(Collectors.toList()));
        String oldRcAttachments = JSON.toJSONString(Convert.toList(Attachment.class, oldManufacturer.getRcAttachments()).stream().map(Attachment::getUri)
                .filter(Objects::nonNull).sorted().collect(Collectors.toList()));
        if (!newRcAttachments.equals(oldRcAttachments)) {
            errList.add("生产备案证");
        }

        if (!Convert.toLong(manufacturerQuery.getRcStartTime(),1L).equals(Convert.toLong(oldManufacturer.getRcStartTime(),1L))) {
            errList.add("生产企业备案凭证编号开始时间");
        }

        if (!Convert.toLong(manufacturerQuery.getRcEndTime(),1L).equals(Convert.toLong(oldManufacturer.getRcEndTime(),1L))) {
            errList.add("生产企业备案凭证编号结束时间");
        }

        if (!Convert.toStr(manufacturerQuery.getRecordCertificateLicence(),"").equals(Convert.toStr(oldManufacturer.getRecordCertificateLicence(),""))) {
            errList.add("生产企业备案凭证编号");
        }

        String newDjbAttachments = JSON.toJSONString(Convert.toList(Attachment.class, manufacturerQuery.getDjbAttachments()).stream().map(Attachment::getUri)
                .filter(Objects::nonNull).sorted().collect(Collectors.toList()));
        String oldDjbAttachments = JSON.toJSONString(Convert.toList(Attachment.class, oldManufacturer.getDjbAttachments()).stream().map(Attachment::getUri)
                .filter(Objects::nonNull).sorted().collect(Collectors.toList()));
        if (!newDjbAttachments.equals(oldDjbAttachments)) {
            errList.add("生产企业生产产品登记表");
        }

        if (!Convert.toLong(manufacturerQuery.getPepStartTime(),1L).equals(Convert.toLong(oldManufacturer.getPepStartTime(),1L))) {
            errList.add("生产企业生产产品登记表开始时间");
        }

        if (!Convert.toLong(manufacturerQuery.getPepEndTime(),1L).equals(Convert.toLong(oldManufacturer.getPepEndTime(),1L))) {
            errList.add("生产企业生产产品登记表结束时间");
        }

        if (CollUtil.isNotEmpty(errList)) {
            String errString = String.join("、", errList);
            throw new ServiceException(StrUtil.format(errMessageTemp, errString));
        }
        RedisUtil.KeyOps.delete(manufacturerId.toString());

        Integer status = manufacturer.getStatus();
        Integer signature = manufacturer.getSignature() == null ? 0 : manufacturer.getSignature();

        // 修改审核状态,审核本身就是通过的不进行修改
        manufacturerQuery.setStatus(status);
        if (ManufacturerStatusEnum.UN_CHECK.getStatus().equals(status)) {
            manufacturerQuery.setSignature(signature);
        }
        manufacturerQuery.setUpdateTime(new Date());
        manufacturerQuery.setUpdateNo(sessUser.getUserId());
        manufacturerMapper.updateByPrimaryKeySelective(manufacturerQuery);

        // 添加审核记录
        vgoodsService.generateCheckLogAndSave(manufacturerId, status, manufacturer.getReason(), LogTypeEnum.MANUFACTURER.getLogType());

        // 发送站内消息
        this.sendMessageWhenManufacturerCheck(manufacturerQuery, status);

        // 同步到【供应商生产厂商】
        if (ManufacturerStatusEnum.CHECK_SUCCESS.getStatus().equals(status)) {
            log.info("审核通过,触发同步信息到 【供应商生产厂商】");
            try {
                // 审核通过开始同步 Manufacturer -> TraderSupplier
                traderSupplierService.manufacturerSyncTraderSupplier(manufacturerQuery);
            } catch (Exception e) {
                log.error("同步供应商出错 msg :{}", JsonUtils.convertObjectToJsonStr(manufacturer), e);
            }
        }

        // 电子签章
        if (status == 3 && manufacturerQuery.getSignature() != null && manufacturerQuery.getSignature() != 0) {
            ThreeCertificatesStamp threeCertificatesStamp = SpringContextHolder.getBean("threeCertificatesStamp");
            threeCertificatesStamp.certificatesStamp(manufacturer.getManufacturerId(), sessUser);
        }

    }

    private void sendMessageWhenManufacturerCheck(Manufacturer manufacturer, Integer checkStatus) {
        int messageTemplateId = 0;
        List<Integer> userIdList = new ArrayList<>();

        Map<String, String> paramMap = new HashMap<>(1);

        paramMap.put("manufacturerName", manufacturer.getManufacturerName());
        if (ManufacturerStatusEnum.UN_CHECK.getStatus().equals(checkStatus)) {
            messageTemplateId = 185;
            userIdList = roleMapper.getUserIdByRoleName(GoodsConstants.GOODS_CHECK_ROLE, 1);

        }
        if (ManufacturerStatusEnum.CHECK_FAIL.getStatus().equals(checkStatus)) {
            messageTemplateId = 186;
            if (manufacturer.getUpdateNo() != null && manufacturer.getUpdateNo() > 0) {
                userIdList.add(manufacturer.getUpdateNo());
            } else {
                userIdList.add(manufacturer.getAddNo());
            }
        }
        if (ManufacturerStatusEnum.CHECK_SUCCESS.getStatus().equals(checkStatus)) {
            messageTemplateId = 187;
            if (manufacturer.getUpdateNo() != null && manufacturer.getUpdateNo() > 0) {
                userIdList.add(manufacturer.getUpdateNo());
            } else {
                userIdList.add(manufacturer.getAddNo());
            }
        }

        MessageUtil.sendMessage(messageTemplateId, userIdList, paramMap, "./goods/manufacturer/getManufacturerDetail.do?manufacturerId=" + manufacturer.getManufacturerId());
    }


    @Override
    public List<LogCheckGenerate> listCheckLog(Integer manufacturerId) {
        LogCheckGenerateExample example = new LogCheckGenerateExample();
        example.createCriteria().andLogBizIdEqualTo(manufacturerId).andLogTypeEqualTo(LogTypeEnum.MANUFACTURER.getLogType());
        example.setOrderByClause("LOG_ID DESC");
        return logCheckGenerateMapper.selectByExample(example);
    }


}
