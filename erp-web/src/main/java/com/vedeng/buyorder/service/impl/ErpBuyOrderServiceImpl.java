package com.vedeng.buyorder.service.impl;

import com.vedeng.buyorder.service.ErpBuyOrderService;
import com.vedeng.erp.buyorder.mapper.BuyorderMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class ErpBuyOrderServiceImpl implements ErpBuyOrderService {

    @Autowired
    private BuyorderMapper buyorderMapper;


    @Override
    public List<Map<String, String>> getBdSaleOrderListByBuyOrderNo(List<String> buyOrderNoList) {
        return buyorderMapper.getBdSaleOrderListByBuyOrderNo(buyOrderNoList);
    }
}
