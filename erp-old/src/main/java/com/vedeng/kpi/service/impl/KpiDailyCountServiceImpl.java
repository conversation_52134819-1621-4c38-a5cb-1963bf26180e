package com.vedeng.kpi.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.vedeng.kpi.cache.KpiLoadingCache;
import com.vedeng.kpi.dao.KpiDailyCountMapper;
import com.vedeng.kpi.model.DO.KpiDailyCountDo;
import com.vedeng.kpi.model.DTO.*;
import com.vedeng.kpi.model.DTO.emptyEntity.NullKpiDailyCountExtDto;
import com.vedeng.kpi.model.VO.KpiDailyCountVo;
import com.vedeng.kpi.model.VO.KpiGroupCountSumVo;
import com.vedeng.kpi.model.VO.KpiGroupVo;
import com.vedeng.kpi.model.VO.KpiTeamVo;
import com.vedeng.kpi.model.VO.emptyEntity.NullKpiDailyCountVo;
import com.vedeng.kpi.model.VO.emptyEntity.NullKpiGroupCountSumVo;
import com.vedeng.kpi.service.KpiDailyCountService;
import com.vedeng.kpi.service.KpiParamTransService;
import com.vedeng.kpi.share.KpiCommonConstant;
import com.vedeng.kpi.share.KpiUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
public class KpiDailyCountServiceImpl implements KpiDailyCountService {

    private static final Logger log = LoggerFactory.getLogger("kpilog");

    @Resource
    KpiDailyCountMapper kpiDailyCountMapper;

    @Autowired
    KpiLoadingCache kpiLoadingCache;

    @Autowired
    private KpiParamTransService kpiParamTransService;


    @Override
    public Boolean insertOrUpdate(KpiDailyCountDo query) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String idName = query.getUserId() + "/" + kpiLoadingCache.getUserConfig(query.getUserId()).getUserName();
        String dateStr = sdf.format(query.getKpiDate());

        log.info("开始插入/更新 " + idName + " " + dateStr + " 日统计kpi");
        int result = 0;
        try {
            KpiDailyCountExtDto exists = kpiDailyCountMapper.selectByUserDay(query);
            if (null == exists) {
                result = kpiDailyCountMapper.insertSelective(query);
                log.info("插入 " + idName + " " + dateStr + " 日统计kpi结果: " + result);
            } else {
                query.setId(exists.getId());
                query.setKpiDate(null);
                result = kpiDailyCountMapper.updateByPrimaryKeySelective(query);
                log.info("更新 " + idName + " " + dateStr + " 日统计kpi结果: " + result + " 主键:" + exists.getId());
            }
        }catch (Exception e){
            log.error("插入/更新 " + idName + " " + dateStr + " 日统计kpi出现异常: ",e);
            return false;
        }
        return result == 1;
    }

    @Override
    public List<KpiDailyCountExtDto> selectByGroupDate(KpiUserInfoDto userInfo,String type) {
//        log.info("开始查询用户 " + userInfo.getUserIdName() + " 统计表数据 时间范围: "
//                + userInfo.getKpiDateStart() + "-" + userInfo.getKpiDateEnd());

        KpiDataQueryDto query = kpiParamTransService.baseParamsToQuery(userInfo);
        List<Integer> userIds = kpiLoadingCache.getGroupUserIdsByUserId(query.getUserId());

        if (userIds.size() > 0) {
            query.setUserIds(userIds);

            List<KpiDailyCountExtDto> countList = kpiDailyCountMapper.selectByGroupDate(query);
            Map<Integer,KpiDailyCountExtDto> groupList = countList.stream().collect(
                    Collectors.groupingBy(
                            KpiDailyCountExtDto::getUserId,
                            Collectors.collectingAndThen(
                                    Collectors.maxBy(Comparator.comparing(KpiDailyCountExtDto::getKpiDate))
                                    ,Optional::get)
                    )
            );

            return Lists.newArrayList(groupList.values()).stream()
                    .map(this::initNullParam)
                    .sorted(getComparator(type).reversed())
                    .collect(Collectors.toList());

        } else {
            return Lists.newArrayList();
        }
    }

    @Override
    public Map<String,List<KpiDailyCountExtDto>> selectBySingleUserHis(KpiDataQueryDto query,String type) {
        query.setUserIds(kpiLoadingCache.getGroupUserIdsByUserId(query.getUserId()));
        if(CollectionUtils.isEmpty(query.getUserIds())){
            return Collections.emptyMap();
        }
        List<KpiDailyCountExtDto> countList = kpiDailyCountMapper.selectByBatchUser(query);
        Map<String,Map<Integer,KpiDailyCountExtDto>> hisMap = getMonthGroupByUser(countList);

        Map<String,List<KpiDailyCountExtDto>> result = hisMap.entrySet().stream().collect(
                Collectors.toMap(Map.Entry::getKey,
                        e -> e.getValue().values().stream()
                                .sorted(getComparator(type).reversed())
                                .collect(Collectors.toList())));
        return result;
    }


    private Comparator<KpiDailyCountExtDto> getComparator(String type) {
        switch (type) {
            case KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_YJ :
                return Comparator.comparing(KpiDailyCountExtDto::getKpiAmountScore);
            case KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_KH :
                return Comparator.comparing(KpiDailyCountExtDto::getCustomerScore);
            case KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_BDKH:
                return Comparator.comparing(KpiDailyCountExtDto::getBdCustomerScore);
            case KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_XJ:
                return Comparator.comparing(KpiDailyCountExtDto::getChanceTransScore);
            default:
                return null;
        }
    }

    /**
     * @description: 获取yyyyMM格式时间字符串.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/6/21 10:47 上午.
     * @author: Tomcat.Hui.
     * @param date: .
     * @return: java.lang.String.
     * @throws: .
     */
    private String formatDate(Date date){
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMM");
        return simpleDateFormat.format(date);
    }

    @Override
    public List<KpiDailyCountExtDto> getKpiCountList(KpiUserInfoDto userInfo, Date start, Date end, String type) {
        userInfo.setKpiDateStart(start);
        userInfo.setKpiDateEnd(end);

        List<KpiDailyCountExtDto> sortList = this.selectByGroupDate(userInfo,type);
        if (null == sortList) {
            log.info("用户 " + userInfo.getUserIdName() + " 未查询到统计表记录");
            return Lists.newArrayList();
        }
        AtomicInteger index = new AtomicInteger(1);
        sortList.stream().forEach(kpi -> kpi.setSort(index.getAndIncrement()));
        return sortList;
    }

    @Override
    public KpiDailyCountExtDto getKpiCountVoByUser(KpiUserInfoDto userInfo, Date start, Date end, String type){
        return getKpiCountList(userInfo,start,end, type).stream()
                .filter(kpi -> kpi.getUserId().equals(userInfo.getUserId()))
                .findFirst().orElse(new NullKpiDailyCountExtDto(userInfo.getUserId()));
    }

    @Override
    public Map<String, Map<Integer,KpiDailyCountExtDto>> getGroupCountDataAll(KpiDataQueryDto query) {
        List<KpiDailyCountExtDto> allData = getKpiDailyCountMonthList(query);

        return getMonthGroupByUser(allData);
    }

    @Override
    public Map<KpiGroupVo,Map<KpiTeamVo, KpiGroupCountSumVo>> getGroupCountData(Map<String, Map<Integer,KpiDailyCountExtDto>> allData,
                                                                         KpiDataQueryDto query) {

        //获取管理的团队
        List<KpiGroupConfigDto> managerGroups = kpiLoadingCache.getAllGroupConfig()
                .stream().filter(g -> g.getGroupManagerId().contains(query.getUserId())).collect(Collectors.toList());

        return getThisMonthGroupsData(allData,managerGroups,query.getKpiDateEnd());
    }

    @Override
    public List<KpiDailyCountVo> getKpiDailyCountMonthSortList(KpiDataQueryDto query){

        //本月
        Date today = new Date();
        query.setKpiDateStart(KpiUtils.getMonthStart(today));
        query.setKpiDateEnd(KpiUtils.getDateStart(today));
        query.setUserIds(kpiLoadingCache.getGroupUserIdsByUserId(query.getUserId()));
        List<KpiDailyCountVo> groupData = this.kpiDailyCountDataHandle(query);

        //昨日
        Date yesterday = KpiUtils.getYesterday(new Date());
        query.setKpiDateStart(KpiUtils.getMonthStart(yesterday));
        query.setKpiDateEnd(yesterday);
        List<KpiDailyCountVo> groupDataYesterday = this.kpiDailyCountDataHandle(query);

        //上个月
        Date lastMonth = KpiUtils.getLastMonthLastDay(new Date());
        query.setKpiDateStart(KpiUtils.getMonthStart(lastMonth));
        query.setKpiDateEnd(lastMonth);
        List<KpiDailyCountVo> groupDataLastmonth = this.kpiDailyCountDataHandle(query);

        for (KpiDailyCountVo currentUser : groupData){

            //设置昨日总排名
            currentUser.setYesterdaySort(Optional.ofNullable(groupDataYesterday.stream()
                    .filter(k -> k.getUserId().equals(currentUser.getUserId()))
                    .findFirst().orElse(new NullKpiDailyCountVo())
                    .getTotalSort()).orElse(-1));

            //设置上月总排名
            currentUser.setLastmonthSort(Optional.ofNullable(groupDataLastmonth.stream()
                    .filter(k -> k.getUserId().equals(currentUser.getUserId()))
                    .findFirst().orElse(new NullKpiDailyCountVo())
                    .getTotalSort()).orElse(-1));
        }
        return groupData.stream().sorted(Comparator.comparing(KpiDailyCountVo::getTotalSort)).collect(Collectors.toList());
    }

    private List<KpiDailyCountVo> kpiDailyCountDataHandle(KpiDataQueryDto query){

        List<KpiDailyCountExtDto> queryResult = this.getKpiDailyCountMonthList(query);
        for (Integer userId : kpiLoadingCache.getGroupUserIdsByUserId(query.getUserId())){
            if(queryResult.stream().noneMatch(k -> k.getUserId().equals(userId))) {
                //该月没数据
                NullKpiDailyCountExtDto count = new NullKpiDailyCountExtDto();
                count.setUserId(userId);
                queryResult.add(count);
            }
        }

        List<KpiDailyCountVo> countList = queryResult.stream()
                .collect(Collectors.groupingBy(KpiDailyCountExtDto::getUserId,
                        Collectors.collectingAndThen(Collectors.maxBy(Comparator.comparing(KpiDailyCountExtDto::getKpiDate))
                                ,Optional::get))).values()
                .stream().map(this::getUserCountKpiVo).collect(Collectors.toList());

        return sortKpiUserList(countList);
    }

    @Override
    public List<KpiDailyCountExtDto> getKpiDailyCountMonthList(KpiDataQueryDto query) {
        return query.getUserIds().size() == 0 ? Lists.newArrayList() : kpiDailyCountMapper.selectAll(query);
    }

    @Override
    public Map<String,List<KpiDailyCountVo>> getTeamCountData(Map<String, Map<Integer, KpiDailyCountExtDto>> monthList,
                                                              KpiUserInfoDto userInfo,Date queryMonth) {

        String monthStr = formatDate(queryMonth);
        List<KpiDailyCountExtDto> dataList = Optional.ofNullable(monthList.get(monthList.keySet().stream()
                .filter(s -> s.equals(monthStr)).findFirst().orElse(""))).orElse(Maps.newHashMap())
                .values().stream().collect(Collectors.toList());

        return getThisMonthTeamsData(dataList,userInfo);
    }

    @Override
    public Map<String, Map<Integer, KpiDailyCountExtDto>> getTeamCountDataAll(KpiDataQueryDto query) {
        List<KpiDailyCountExtDto> allData = getKpiDailyCountMonthList(query);

        return getMonthGroupByUser(allData);
    }

    /**
     * 获取页面展示结果.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: 返回的外层map的key为团队名称,内层map的key为小组名称.
     * @version: 1.0.
     * @date: 2020/6/21 9:03 下午.
     * @author: Tomcat.Hui.
     * @param monthList: .
     * @return: java.util.Map<java.lang.String,java.util.Map<java.lang.String,com.vedeng.kpi.model.VO.KpiDailyCountSumVo>>.
     * @throws: .
     */
    private Map<KpiGroupVo,Map<KpiTeamVo, KpiGroupCountSumVo>> getThisMonthGroupsData(Map<String, Map<Integer,
            KpiDailyCountExtDto>> monthList, List<KpiGroupConfigDto> managerGroups,Date queryDate){
        String monthStr = formatDate(queryDate);
        List<KpiDailyCountExtDto> dataList = Optional.ofNullable(monthList.get(monthList.keySet().stream()
                .filter(s -> s.equals(monthStr)).findFirst().orElse(""))).orElse(Maps.newHashMap())
                .values().stream().collect(Collectors.toList());

        Map<Integer,Map<Integer, KpiGroupCountSumVo>> groupMap = dataList.stream()
                .collect(Collectors.groupingBy(k -> kpiLoadingCache.getUserConfig(k.getUserId()).getGroupId(),
                        Collectors.groupingBy(k1 -> kpiLoadingCache.getUserConfig(k1.getUserId()).getTeamId(),
                                Collectors.collectingAndThen(Collectors.toList(),c -> getTeamSumKpiCount(c,null)))
                ));

        //兼容没有数据的小组
        for (KpiGroupConfigDto groupConfig : managerGroups) {
            if (!groupMap.keySet().contains(groupConfig.getGroupName())) {
                Map<Integer, KpiGroupCountSumVo> nullGroup = Maps.newHashMap();
                for (KpiTeamDto teamConfig : groupConfig.getTeams()) {
                    NullKpiGroupCountSumVo countData = new NullKpiGroupCountSumVo();
                    countData.setAmountTarget(kpiLoadingCache.getTeamMonthGoal(groupConfig.getGroupId(),
                            teamConfig.getTeamId(),
                            new Date()));
                    nullGroup.put(teamConfig.getTeamId(),countData);
                }
                groupMap.put(groupConfig.getGroupId(),nullGroup);
            } else {
                for (KpiTeamDto teamConfig : groupConfig.getTeams()) {
                    if (!groupMap.get(groupConfig.getGroupName()).keySet().contains(teamConfig.getTeamName())) {
                        NullKpiGroupCountSumVo countData = new NullKpiGroupCountSumVo();
                        countData.setAmountTarget(kpiLoadingCache.getTeamMonthGoal(groupConfig.getGroupId(),
                                teamConfig.getTeamId(),
                                new Date()));
                        groupMap.get(groupConfig.getGroupName()).put(teamConfig.getTeamId(),countData);
                    }
                }
            }
        }

        //各个指标排序
        groupMap.values().stream().forEach(e -> sortKpiTeamList(Lists.newArrayList(e.values())));

        //内部按照综和得分排序
        groupMap = groupMap.entrySet().stream().collect(Collectors.toMap(e -> e.getKey(),e -> e.getValue().entrySet().stream()
                .sorted(Map.Entry.comparingByValue(Comparator.comparing(KpiGroupCountSumVo::getIntegrateScore).reversed()))
                .collect(Collectors.toMap(Map.Entry::getKey,Map.Entry::getValue,(oldValue,newValue) -> newValue,LinkedHashMap::new))));

        //组装vo
        return getKpiGroupVoMapMap(groupMap);
    }

    /**
     * 组装返回vo.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/15 10:58 上午.
     * @author: Tomcat.Hui.
     * @param groupMap: .
     * @return: java.util.Map<com.vedeng.kpi.model.VO.KpiGroupVo,java.util.Map<com.vedeng.kpi.model.VO.KpiTeamVo,com.vedeng.kpi.model.VO.KpiGroupCountSumVo>>.
     * @throw: .
     */
    private Map<KpiGroupVo, Map<KpiTeamVo, KpiGroupCountSumVo>> getKpiGroupVoMapMap(Map<Integer,
            Map<Integer, KpiGroupCountSumVo>> groupMap) {

        Map<KpiGroupVo,Map<KpiTeamVo, KpiGroupCountSumVo>> result = Maps.newLinkedHashMap();
        for (Integer groupId : groupMap.keySet()) {
            KpiGroupConfigDto groupConfig = kpiLoadingCache.getGroupConfig(groupId);
            KpiGroupVo groupVo = new KpiGroupVo();
            groupVo.setGroupId(groupId);
            groupVo.setGroupName(groupConfig.getGroupName());

            Map<KpiTeamVo, KpiGroupCountSumVo> innerMap = Maps.newLinkedHashMap();
            for(Integer teamId : groupMap.get(groupId).keySet()){

                KpiTeamVo teamVo = new KpiTeamVo();
                teamVo.setTeamId(teamId);
                teamVo.setTeamName(groupConfig.getTeams().stream()
                        .filter(t -> t.getTeamId().equals(teamId))
                        .map(KpiTeamDto::getTeamName)
                        .findFirst().orElse(""));

                innerMap.put(teamVo,groupMap.get(groupId).get(teamId));
            }
            result.put(groupVo,innerMap);
        }

        return result;
    }

    /**
     * 小组数据分组排序.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/16 6:30 下午.
     * @author: Tomcat.Hui.
     * @param monthList: .
     * @param userInfo: .
     * @return: java.util.Map<java.lang.String,java.util.List<com.vedeng.kpi.model.VO.KpiDailyCountVo>>.
     * @throw: .
     */
    private Map<String, List<KpiDailyCountVo>> getThisMonthTeamsData(List<KpiDailyCountExtDto> monthList,KpiUserInfoDto userInfo){

        // changed by Tomcat.Hui 2020/7/16 2:35 下午 .Desc: 要求按照全团设置总排名,不再分组排序.
        List<KpiDailyCountVo> result = monthList.stream().map(this::getUserCountKpiVo).collect(Collectors.toList());

        List<KpiUserConfigDto> users = kpiLoadingCache.getManagerUsers(userInfo);
        if (userInfo.getTeamId() != null && userInfo.getGroupId() != null) {
            //如果由团队详情页面跳转过来(指定小组查看)需要再次过滤
            users = users.stream()
                    .filter(u -> u.getGroupId().equals(userInfo.getGroupId()) && u.getTeamId().equals(userInfo.getTeamId()))
                    .collect(Collectors.toList());
        }

        //兼容没有数据的人员(查询时用userId in (xx)查询,没有数据的user为空,这里进行补齐)
        for (KpiUserConfigDto userConfig : users){
            if (result.stream().noneMatch(kpi -> kpi.getUserId().equals(userConfig.getUserId()))) {
                NullKpiDailyCountVo vo = new NullKpiDailyCountVo();
                vo.setGroupId(userConfig.getGroupId());
                vo.setTeamId(userConfig.getTeamId());
                vo.setUserId(userConfig.getUserId());
                vo.setUserName(kpiLoadingCache.getUserConfig(userConfig.getUserId()).getUserName());
                vo.setAmountTarget(kpiLoadingCache.getUserConfig(userConfig.getUserId()).getMonthAmountGoal());
                result.add(vo);
            }
        }

        //先全团队排名,再删除非当前负责人所管理的用户
        List<Integer> teamIds = users.stream().map(KpiUserConfigDto::getTeamId)
                .distinct().collect(Collectors.toList());

        Map<Integer,List<KpiDailyCountVo>> groupMap = result.stream()
                .collect(Collectors.groupingBy(KpiDailyCountVo::getGroupId,
                Collectors.collectingAndThen(Collectors.toList(),list -> sortKpiUserList(list))))
                .entrySet().stream().collect(Collectors.toMap(e -> e.getKey(),e -> e.getValue().stream()
                        .filter(kpi -> teamIds.contains(kpi.getTeamId())).collect(Collectors.toList())));

        return groupMap.values().stream().flatMap(v -> v.stream())
                .collect(Collectors.groupingBy(kpi ->
                        kpiLoadingCache.getTeamConfig(kpi.getGroupId(),kpi.getTeamId()).getTeamName()));

    }

    /**
     * 按照各指标设置排序值.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/6/21 2:43 下午.
     * @author: Tomcat.Hui.
     * @param list: .
     * @return: java.util.List<com.vedeng.kpi.model.VO.KpiDailyCountSumVo>.
     * @throws: .
     */
    private List<KpiGroupCountSumVo> sortKpiTeamList(List<KpiGroupCountSumVo> list){

        // changed by Tomcat.Hui 2020/8/10 3:33 下午 .Desc: VDERP-3094【五行剑法】优化得分排名和转化率逻辑 .
        AtomicInteger amountSortIndex = new AtomicInteger(1);
        list.stream().sorted(Comparator.comparing(KpiGroupCountSumVo::getAveAmountScore).reversed())
                .forEach(k -> k.setAmountSort(amountSortIndex.getAndIncrement()));
        AtomicInteger bdCustomerSortIndex = new AtomicInteger(1);
        list.stream().sorted(Comparator.comparing(KpiGroupCountSumVo::getAveBdCustomerScore).reversed())
                .forEach(k -> k.setBdCustomerSort(bdCustomerSortIndex.getAndIncrement()));
        AtomicInteger customerSortIndex = new AtomicInteger(1);
        list.stream().sorted(Comparator.comparing(KpiGroupCountSumVo::getAveCustomerScore).reversed())
                .forEach(k -> k.setCustomerSort(customerSortIndex.getAndIncrement()));
        AtomicInteger chanceSortIndex = new AtomicInteger(1);
        list.stream().sorted(Comparator.comparing(KpiGroupCountSumVo::getAveChanceScore).reversed())
                .forEach(k -> k.setChanceSort(chanceSortIndex.getAndIncrement()));
        AtomicInteger scoreSortIndex = new AtomicInteger(1);
        list.stream().sorted(Comparator.comparing(KpiGroupCountSumVo::getIntegrateScore).reversed())
                .forEach(k -> k.setIntegrateSort(scoreSortIndex.getAndIncrement()));
        return list;
    }

    /**
     * 各个指标排序.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/16 4:25 下午.
     * @author: Tomcat.Hui.
     * @param list: .
     * @return: java.util.List<com.vedeng.kpi.model.VO.KpiDailyCountVo>.
     * @throw: .
     */
    private List<KpiDailyCountVo> sortKpiUserList(List<KpiDailyCountVo> list){
        AtomicInteger amountSortIndex = new AtomicInteger(1);
        list.stream().sorted(Comparator.comparing(KpiDailyCountVo::getKpiAmountScore).reversed())
                .forEach(k -> k.setAmountSort(amountSortIndex.getAndIncrement()));
        AtomicInteger bdCustomerSortIndex = new AtomicInteger(1);
        list.stream().sorted(Comparator.comparing(KpiDailyCountVo::getBdCustomerScore).reversed())
                .forEach(k -> k.setBdCustomerSort(bdCustomerSortIndex.getAndIncrement()));
        AtomicInteger customerSortIndex = new AtomicInteger(1);
        list.stream().sorted(Comparator.comparing(KpiDailyCountVo::getCustomerScore).reversed())
                .forEach(k -> k.setCustomerSort(customerSortIndex.getAndIncrement()));
        AtomicInteger chanceSortIndex = new AtomicInteger(1);
        list.stream().sorted(Comparator.comparing(KpiDailyCountVo::getChanceScore).reversed())
                .forEach(k -> k.setChanceSort(chanceSortIndex.getAndIncrement()));
        AtomicInteger totalScoreSortIndex = new AtomicInteger(1);
        list.stream().sorted(Comparator.comparing(KpiDailyCountVo::getTotalScore).reversed())
                .forEach(k -> k.setTotalSort(totalScoreSortIndex.getAndIncrement()));
        list.stream().sorted(Comparator.comparing(KpiDailyCountVo::getTotalSort));
        return list;
    }

    /**
     *  计算个人统计vo.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/6/22 12:59 上午.
     * @author: Tomcat.Hui.
     * @param kpi: .
     * @return: com.vedeng.kpi.model.VO.KpiDailyCountVo.
     * @throws: .
     */
    private KpiDailyCountVo getUserCountKpiVo(KpiDailyCountExtDto kpi){
        KpiDailyCountVo result = new KpiDailyCountVo();
        result.setUserId(kpi.getUserId());
        result.setGroupId(kpi.getGroupId());
        result.setUserName(kpiLoadingCache.getUserConfig(kpi.getUserId()).getUserName());
        result.setTeamId(kpi.getTeamId());
        result.setAmountTarget(kpiLoadingCache.getUserConfig(kpi.getUserId()).getMonthAmountGoal());
        result.setKpiAmount(kpi.getKpiAmount());
        result.setKpiAmountProgress(kpi.getKpiAmountProgress());
        result.setKpiAmountScore(Optional.ofNullable(kpi.getKpiAmountScore()).orElse(BigDecimal.ZERO));
        result.setBdNewCustomerNum(Optional.ofNullable(kpi.getBdNewCustomerNum()).orElse(0));
        result.setBdLostCustomerNum(Optional.ofNullable(kpi.getBdLostCustomerNum()).orElse(0));
        result.setBdCustomerScore(Optional.ofNullable(kpi.getBdCustomerScore()).orElse(BigDecimal.ZERO));
        result.setCoCustomerNum(Optional.ofNullable(kpi.getCoCustomerNum()).orElse(0));
        result.setLostCustomerNum(Optional.ofNullable(kpi.getLostCustomerNum()).orElse(0));
        result.setCustomerScore(Optional.ofNullable(kpi.getCustomerScore()).orElse(BigDecimal.ZERO));
        result.setChanceSuccessNum(Optional.ofNullable(kpi.getChanceSuccessNum()).orElse(0));
        result.setChanceFailNum(Optional.ofNullable(kpi.getChanceFailNum()).orElse(0));
        result.setChanceTransProportion(Optional.ofNullable(kpi.getChanceTransProportion()).orElse(BigDecimal.ZERO));
        result.setChanceScore(Optional.ofNullable(kpi.getChanceTransScore()).orElse(BigDecimal.ZERO));
        BigDecimal totalScore = result.getKpiAmountScore()
                .add(result.getCustomerScore())
                .add(result.getBdCustomerScore())
                .add(result.getChanceScore());
        result.setTotalScore(totalScore.setScale(2, RoundingMode.HALF_UP));
        return result;
    }

    @Override
    public Map<String,KpiGroupCountSumVo> transGroupHisData(Map<String, List<KpiDailyCountExtDto>> data){
        //合并list
        Map<String,KpiGroupCountSumVo> result = data.entrySet().stream()
                .collect(Collectors.toMap(e -> e.getKey(),e -> getTeamSumKpiCount(e.getValue(),e.getKey())));

        //排序
        return result.entrySet().stream().sorted(Map.Entry.comparingByKey(Comparator.comparing(String::trim).reversed()))
                .collect(Collectors.toMap(Map.Entry::getKey,Map.Entry::getValue,(a,b) -> a,LinkedHashMap::new));

    }

    /**
     * @description: 获取小组内各平均值.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/6/21 9:04 下午.
     * @author: Tomcat.Hui.
     * @param list: .
     * @return: com.vedeng.kpi.model.VO.KpiDailyCountSumVo.
     * @throws: .
     */
    private KpiGroupCountSumVo getTeamSumKpiCount(List<KpiDailyCountExtDto> list, String monthStr){
        if (list.size() < 0) {
            return new NullKpiGroupCountSumVo();
        }
        int num = kpiLoadingCache.getTeamConfig(list.get(0).getGroupId(),list.get(0).getTeamId()).getUsers().size();
        KpiGroupCountSumVo result = new NullKpiGroupCountSumVo();
        Double sumKpiAmount = list.stream().collect(Collectors.summingDouble(kpi -> Optional.ofNullable(kpi.getKpiAmount().doubleValue()).orElse(0.0)));
        BigDecimal aveAmountScore = new BigDecimal(list.stream().collect(Collectors.summingDouble(kpi -> Optional.ofNullable(kpi.getKpiAmountScore().doubleValue()).orElse(0.0))) / num);

        Integer sumCoCustomerNum = list.stream().collect(Collectors.summingInt(kpi -> Optional.ofNullable(kpi.getCoCustomerNum()).orElse(0)));
        Integer sumLostCustomerNum = list.stream().collect(Collectors.summingInt(kpi -> Optional.ofNullable(kpi.getLostCustomerNum()).orElse(0)));
        Double aveCustomerNum = new Double(list.stream().
                collect(Collectors.summingInt(kpi -> Optional.ofNullable(kpi.getCoCustomerNum() - kpi.getLostCustomerNum()).orElse(0)))) / num;
        BigDecimal aveCustomerScore = new BigDecimal(list.stream().collect(Collectors.summingDouble(kpi -> Optional.ofNullable(kpi.getCustomerScore().doubleValue()).orElse(0.0))) / num);

        Integer sumBdNewCustomer = list.stream().collect(Collectors.summingInt(kpi -> Optional.ofNullable(kpi.getBdNewCustomerNum()).orElse(0)));
        Integer sumBdLostCustomer = list.stream().collect(Collectors.summingInt(kpi -> Optional.ofNullable(kpi.getBdLostCustomerNum()).orElse(0)));
        Double aveBdCustomerNum = list.stream().collect(Collectors.averagingInt(kpi -> Optional.ofNullable(kpi.getBdNewCustomerNum() - kpi.getBdLostCustomerNum()).orElse(0)));
        BigDecimal aveBdCustomerScore = new BigDecimal(list.stream().collect(Collectors.summingDouble(kpi -> Optional.ofNullable(kpi.getBdCustomerScore().doubleValue()).orElse(0.0))) / num);

        Integer successChanceNum = list.stream().collect(Collectors.summingInt(kpi -> Optional.ofNullable(kpi.getChanceSuccessNum()).orElse(0)));
        Integer failChanceNum = list.stream().collect(Collectors.summingInt(kpi -> Optional.ofNullable(kpi.getChanceFailNum()).orElse(0)));
        BigDecimal aveTransProportion = new BigDecimal(list.stream().collect(Collectors
                .averagingDouble(kpi -> Optional.ofNullable(kpi.getChanceTransProportion().doubleValue()).orElse(0.0))));
        BigDecimal aveChanceScore = new BigDecimal(list.stream().collect(Collectors.summingDouble(kpi -> Optional.ofNullable(kpi.getChanceTransScore().doubleValue()).orElse(0.0))) / num);

        result.setSumChancefailNum(failChanceNum);
        result.setSumChanceSuccessNum(successChanceNum);
        result.setAveTransProportion(aveTransProportion);
        result.setChanceScore(list.stream().map(KpiDailyCountExtDto::getChanceTransScore).reduce(BigDecimal.ZERO,BigDecimal::add));
        result.setAveChanceScore(aveChanceScore);

        result.setSumBdNewCustomerNum(new BigDecimal(sumBdNewCustomer));
        result.setSumBdLostCustomerNum(new BigDecimal(sumBdLostCustomer));
        result.setAveBdCustomerNum(new BigDecimal(aveBdCustomerNum));
        result.setBdCustomerScore(list.stream().map(KpiDailyCountExtDto::getBdCustomerScore).reduce(BigDecimal.ZERO,BigDecimal::add));
        result.setAveBdCustomerScore(aveBdCustomerScore);

        result.setSumCoCustomerNum(new BigDecimal(sumCoCustomerNum));
        result.setSumLostCustomerNum(new BigDecimal(sumLostCustomerNum));
        result.setAveCustomerNum(new BigDecimal(aveCustomerNum));
        result.setCustomerScore(list.stream().map(KpiDailyCountExtDto::getCustomerScore).reduce(BigDecimal.ZERO,BigDecimal::add));
        result.setAveCustomerScore(aveCustomerScore);

        result.setSumAmount(new BigDecimal(sumKpiAmount));
        result.setAmountTarget(new BigDecimal(list.stream()
                .map(KpiDailyCountExtDto::getUserId)
                .map(userId -> null == monthStr ?
                        kpiLoadingCache.getUserConfig(userId).getMonthAmountGoal() :
                        kpiLoadingCache.getHisTargetByUser(userId,monthStr))
                .collect(Collectors.summingDouble(BigDecimal::doubleValue))));
        if (result.getAmountTarget().compareTo(BigDecimal.ZERO) != 0) {
            result.setAmountProgress(result.getSumAmount().divide(result.getAmountTarget().multiply(new BigDecimal(10000)),8,RoundingMode.HALF_UP));
        } else {
            result.setAmountProgress(BigDecimal.ZERO);
        }
        result.setAmountScore(list.stream().map(KpiDailyCountExtDto::getKpiAmountScore).reduce(BigDecimal.ZERO,BigDecimal::add));
        result.setAveAmountScore(aveAmountScore);

        Double totalScore = list.stream()
                .map(kpi -> kpi.getCustomerScore()
                        .add(kpi.getKpiAmountScore())
                        .add(kpi.getBdCustomerScore())
                        .add(kpi.getChanceTransScore()))
                .collect(Collectors.summingDouble(BigDecimal::doubleValue));
        result.setIntegrateScore(new BigDecimal(totalScore / num));
        return result;
    }

    /**
     * @description: 获取每月每人kpi统计结果.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: 按 `yyyyMM` 格式groupBy,然后取每月每人最迟一条数据作为当月最终结果.
     * @version: 1.0.
     * @date: 2020/6/21 10:45 上午.
     * @author: Tomcat.Hui.
     * @param allData: .
     * @return: java.util.Map<java.lang.String,java.util.Map<java.lang.Integer,com.vedeng.kpi.model.DTO.KpiDailyCountExtDto>>.
     * @throws: .
     */
    private Map<String, Map<Integer,KpiDailyCountExtDto>> getMonthGroupByUser(List<KpiDailyCountExtDto> allData) {
        return allData.stream().map(this::initNullParam)
                .collect(Collectors.groupingBy(k ->formatDate(k.getKpiDate()),
                        Collectors.groupingBy(KpiDailyCountExtDto::getUserId,
                                Collectors.collectingAndThen(Collectors.maxBy(Comparator.comparing(k -> k.getKpiDate().getTime())),
                                        Optional::get))));
    }

    private KpiDailyCountExtDto initNullParam(KpiDailyCountExtDto entity){
        entity.setKpiAmount(Optional.ofNullable(entity.getKpiAmount()).orElse(BigDecimal.ZERO));
        entity.setKpiAmountProgress(Optional.ofNullable(entity.getKpiAmountProgress()).orElse(BigDecimal.ZERO));
        entity.setKpiAmountScore(Optional.ofNullable(entity.getKpiAmountScore()).orElse(BigDecimal.ZERO));
        entity.setCoCustomerNum(Optional.ofNullable(entity.getCoCustomerNum()).orElse(0));
        entity.setLostCustomerNum(Optional.ofNullable(entity.getLostCustomerNum()).orElse(0));
        entity.setCustomerScore(Optional.ofNullable(entity.getCustomerScore()).orElse(BigDecimal.ZERO));
        entity.setBdNewCustomerNum(Optional.ofNullable(entity.getBdNewCustomerNum()).orElse(0));
        entity.setBdLostCustomerNum(Optional.ofNullable(entity.getBdLostCustomerNum()).orElse(0));
        entity.setBdCustomerScore(Optional.ofNullable(entity.getBdCustomerScore()).orElse(BigDecimal.ZERO));
        entity.setChanceSuccessNum(Optional.ofNullable(entity.getChanceSuccessNum()).orElse(0));
        entity.setChanceFailNum(Optional.ofNullable(entity.getChanceFailNum()).orElse(0));
        entity.setChanceTransProportion(Optional.ofNullable(entity.getChanceTransProportion()).orElse(BigDecimal.ZERO));
        entity.setChanceTransScore(Optional.ofNullable(entity.getChanceTransScore()).orElse(BigDecimal.ZERO));
        entity.setChanceRangeCustomerNum(Optional.ofNullable(entity.getChanceRangeCustomerNum()).orElse(0));
        return entity;
    }

    @Override
    public List<KpiDailyCountExtDto> getByTeams(KpiDataQueryDto query) {
        return null;
    }

}
