package com.vedeng.mobile.system.dto;

import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2023/12/6
 */
@Data
public class PageResult <T> {
    private Integer pageNo;
    private Integer pageSize;
    private Long totalRecord;
    private Integer totalPage;
    private List<T> dataList;



    public PageResult(Integer pageNo, Integer pageSize, Long totalRecord, Integer totalPage, List<T> dataList) {
        this.pageNo = pageNo;
        this.pageSize = pageSize;
        this.totalRecord = totalRecord;
        this.totalPage = totalPage;
        this.dataList = dataList;
    }

    public PageResult() {
    }


}

