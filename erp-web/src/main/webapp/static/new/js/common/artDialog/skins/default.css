@charset "utf-8";
/*
 * artDialog skin
 * http://code.google.com/p/artdialog/
 * (c) 2009-2011 TangBin, http://www.planeArt.cn
 *
 * This is licensed under the GNU LGPL, version 2.1 or later.
 * For details, see: http://creativecommons.org/licenses/LGPL/2.1/
 */

/* common start */
body { _margin:0; _height:100%; /*IE6 BUG*/ }
.aui_outer { text-align:left; }
table.aui_border, table.aui_dialog { border:0; margin:0; border-collapse:collapse; width:auto; }
.aui_nw, .aui_n, .aui_ne, .aui_w, .aui_c, .aui_e, .aui_sw, .aui_s, .aui_se, .aui_header, .aui_tdIcon, .aui_main, .aui_footer { padding:0; }
.aui_title { overflow:hidden; text-overflow: ellipsis; }
.aui_state_noTitle .aui_title { display:none; }
.aui_close { display:block; position:absolute; text-decoration:none; outline:none; _cursor:pointer; }
.aui_close:hover { text-decoration:none; }
.aui_main { min-width:9em; min-width:0\9/*IE8 BUG*/; position:relative;}
.aui_content {border:none 0; padding:20px;position:relative;}
.aui_content.aui_state_full { display:block; width:100%; margin:0; padding:0!important; height:100%; }
.aui_loading { width:150px; height:80px; text-align:left; text-indent:-999em; overflow:hidden; background:url(icons/loading.gif) no-repeat center center; }
.aui_icon { vertical-align: middle; }
.aui_icon div { width:48px; height:48px; margin:10px 0 10px 10px; background-position: center center; background-repeat:no-repeat; }

.aui_buttons { padding:8px; padding-right:20px; text-align:right; white-space:nowrap; }


.aui_buttons button { margin-left:15px;
  line-height: 28px;
  height: 28px;
  *line-height: 16px;
  display: -moz-inline-stack;
  display: inline-block;
  vertical-align: middle;
  *vertical-align: auto;
  zoom: 1;
  *display: inline;
  text-align: center;
  padding: 0 15px;
  color: #333;
  overflow: visible;
  cursor: pointer;
  text-shadow: 1px 1px white;
  background-color: #f3f3f3;
  border: solid 1px #cccccc;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -ms-border-radius: 2px;
  -o-border-radius: 2px;
  border-radius: 2px;
  background-image: linear-gradient(to bottom, #f7f7f7, #e6e6e6);
 }


.aui_buttons button::-moz-focus-inner{ border:0; padding:0; margin:0; }


.aui_buttons button:focus {}


.aui_buttons button:hover {
 color: #333;
  background-color: #e9e9e9;
  text-decoration: none;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.1), 0 1px 1px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.1), 0 1px 1px rgba(0, 0, 0, 0.2);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.1), 0 1px 1px rgba(0, 0, 0, 0.2);
  background-image: linear-gradient(to bottom, #f7f7f7, #e6e6e6);
}


.aui_buttons button:active {
 -webkit-box-shadow: inset 0 4px 4px rgba(140, 140, 140, 0.3);
  -moz-box-shadow: inset 0 4px 4px rgba(140, 140, 140, 0.3);
  box-shadow: inset 0 4px 4px rgba(140, 140, 140, 0.3);
}




.aui_buttons button.disabled,
.aui_buttons button.disabled:hover {
  cursor: default;
  color: #999;
  background-color: #dcdcdc;
  border: solid 1px #d4d4d4;
  text-shadow: 1px 1px rgba(255, 255, 255, 0.6);
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -ms-border-radius: 2px;
  -o-border-radius: 2px;
  border-radius: 2px;
  -webkit-box-shadow: inset 1px 1px rgba(255, 255, 255, 0.1);
  -moz-box-shadow: inset 1px 1px rgba(255, 255, 255, 0.1);
  box-shadow: inset 1px 1px rgba(255, 255, 255, 0.1);
  background-image: linear-gradient(to bottom, #e0e0e0, #d9d9d9);
}

button.highlight:focus,button.aui_state_highlight:focus{
	outline:0;
}

button.highlight,button.aui_state_highlight {
  color: #fff;
  background-color: #e64545;
  border: solid 1px #e64545;
  text-shadow: 0 1px rgba(51, 51, 51, 0.3);
  background-image: -webkit-gradient(linear, 50% 100%, 50% 0%, color-stop(0%, #e64545), color-stop(100%, #e65050));
  background-image: -webkit-linear-gradient(bottom, #e64545, #e65050);
  background-image: -moz-linear-gradient(bottom, #e64545, #e65050);
  background-image: -o-linear-gradient(bottom, #e64545, #e65050);
  background-image: linear-gradient(bottom, #e64545, #e65050);
  background-image: linear-gradient(to bottom, #e65050, #e64545);
}


button.highlight:hover,button.aui_state_highlight:hover {
 color: #fff;
  border: solid 1px #e64545;
  background-color: #e64545;
  text-shadow: 0 1px rgba(51, 51, 51, 0.3);
  -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1), 0 1px 1px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1), 0 1px 1px rgba(0, 0, 0, 0.2);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1), 0 1px 1px rgba(0, 0, 0, 0.2);
  background-image: linear-gradient(to bottom, #e65050, #e64545);
}



button.highlight:active,button.aui_state_highlight:active {
  color: #fff;
  border: solid 1px #e64545;
  background-color: #e65050;
  text-shadow: 0 1px rgba(51, 51, 51, 0.3);
  -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2);
  background-image: linear-gradient(to bottom, #e65050, #e64545);
}



/* common end */

.aui_inner { background:#FFF; }
.aui_nw, .aui_ne, .aui_sw, .aui_se { width:6px; height:6px; }
.aui_nw, .aui_n, .aui_ne, .aui_w, .aui_e, .aui_sw, .aui_s, .aui_se { background:#3d4b66;}
.aui_nw{
	border-radius: 3px 0 0 0;
}
.aui_ne{
	border-radius: 0 3px 0 0;
}
.aui_sw{
	border-radius: 0 0 0 3px;
}
.aui_se{
	border-radius: 0 0 3px 0;
}
.aui_state_lock .aui_nw, .aui_state_lock .aui_n, .aui_state_lock .aui_ne, .aui_state_lock .aui_w, .aui_state_lock .aui_e, .aui_state_lock .aui_sw, .aui_state_lock .aui_s, .aui_state_lock .aui_se { background:#333; background:#333\9!important }
.aui_titleBar { position:relative; height:100%; }
.aui_title { height:32px; line-height:32px; padding:0 20px;font-weight:bold; background-color:#f1f1f1;  border-bottom:1px solid #e6e6e6; }
.aui_state_drag .aui_title {}
.aui_close { padding:0; top:7px; right:4px; width:21px; height:21px; line-height:21px; font-size:22px; font-weight:bold; color:#b8b8b8; text-align:center; }
.aui_close:hover { color:#666; }
.aui_content { color:#666; }
.aui_state_focus .aui_content { }
.aui_buttons { background-color:#f1f1f1; border-top:solid 1px #e6e6e6; }
.aui_state_noTitle .aui_nw, .aui_state_noTitle .aui_ne, .aui_state_noTitle .aui_sw, .aui_state_noTitle .aui_se { width:3px; height:3px; }
.aui_state_noTitle .aui_inner { background:none; }
.aui_state_noTitle .aui_outer { border:none 0; }
.aui_state_noTitle .aui_nw, .aui_state_noTitle .aui_n, .aui_state_noTitle .aui_ne, .aui_state_noTitle .aui_w, .aui_state_noTitle .aui_e, .aui_state_noTitle .aui_sw, .aui_state_noTitle .aui_s, .aui_state_noTitle .aui_se { background: none !important; }
.aui_state_noTitle .aui_titleBar { bottom:0; _bottom:0; _margin-top:0; z-index: 5}
.aui_state_noTitle .aui_close { top:0; right:0; width:18px; height:18px; line-height:18px; text-align:center; text-indent:0; font-size:18px; text-decoration:none; color:#999; background:#fff; z-index:5;zoom: 1}
.aui_state_noTitle .aui_close:hover, .aui_state_noTitle .aui_close:active { text-decoration:none; color:#000; }