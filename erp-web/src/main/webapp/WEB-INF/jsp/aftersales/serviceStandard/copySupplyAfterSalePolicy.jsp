<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="复制供应商售后政策" scope="application" />
<%@ include file="../../common/common.jsp"%>
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/tag.css?rnd=${resourceVersionKey}">
<script type="text/javascript">
    function copyAfterSales() {
        var selectNos = "";
        $('input[name="checkOne"]:checked').each(function(){
            selectNos += $(this).val() + ",";
        });

        if(selectNos == ""){
            layer.alert("请先选择数据");
            return;
        }

        index=layer.confirm("确定将该售后政策复制到所选的sku里吗？", {
            btn: ['确定','取消'] //按钮
        }, function(){
            $.ajax({
                type: "POST",
                url: "./copySupplyAfterSalePolicyList.do",
                data: {"skuNo":selectNos,"supplyPolicyId":${queryDto.supplyPolicyId}},
                dataType:'json',
                success: function(data){
                    if(data.code == '0'){
                        window.parent.location.reload();
                        layer.close(index);
                    }else{
                        layer.alert(data.message);
                    }
                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });
        }, function(){
        });


    }
</script>
<div class="main-container">
    <div class="list-pages-search">
        <form method="post" id="search" action="<%=basePath%>aftersale/serviceStandard/toCopySupplyAfterSalePolicy.do">
            <span style="margin-left: 42px">
                售后政策: 【供应商】${afterSaleSupplyPolicyDto.traderName} --【商品】${afterSaleSupplyPolicyDto.skuNo}&nbsp; ${afterSaleSupplyPolicyDto.showName}
            </span>

            </br>
            </br>
            <ul>
                <input type="hidden" name="supplyPolicyId" value="${queryDto.supplyPolicyId}">
                <li>
                    <label class="infor_name">商品名称</label>
                    <input type="text" class="input-small" placeholder="请输入订货号/商品名称" name="keyWord1" id="keyWord1" value="${queryDto.keyWord1}"/>
                </li>

                <li>
                    <label class="infor_name">注册证号</label>
                    <input type="text" class="input-small" style="width: 200px" placeholder="请输入注册证号/备案号/生产企业" name="keyWord2" id="keyWord2" value="${queryDto.keyWord2}"/>
                </li>

                <li>
                    <label class="infor_name">品牌</label>
                    <input type="text" class="input-small" placeholder="请输入品牌" name="brandName" id="brandName" value="${queryDto.brandName}"/>
                </li>

                <li>
                    <label class="infor_name">是否维护</label>
                    <select class="input-small" name="policyMatained">
                        <option <c:if test="${queryDto.policyMatained == '-1'}">selected</c:if> value="-1">全部</option>
                        <option <c:if test="${queryDto.policyMatained == '0'}">selected</c:if> value="0">未维护</option>
                        <option <c:if test="${queryDto.policyMatained == '1'}">selected </c:if> value="1">已维护</option>
                    </select>
                </li>

            </ul>
            <div class="tcenter">

                <span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();" id="searchSpan">查询</span>

                <span class="bt-small bg-light-blue bt-bg-style mr20" onclick="reset();">重置</span>

            </div>
        </form>
    </div>
    <div class="normal-list-page">
        <table class="table table-bordered table-striped table-condensed table-centered">
            <thead>
                <tr>
                    <th class="wid6"><input type="checkbox" name="checkAll"/>选择</th>
                    <th class="wid6">订货号</th>
                    <th class="wid6">商品名称</th>
                    <th class="wid6">品牌</th>
                    <th class="wid6">规格/型号</th>
                    <th class="wid6">注册证号</th>
                    <th class="wid6">
                        <div class="customername pos_rel">
                            <span>
                                是否维护
                                <i class="iconbluesigh ml4 contorlIcon"></i>
                            </span>
                            <div class="pos_abs customernameshow" style="top: 16px;">
                                指该供应商的这个SKU是否维护了售后政策
                            </div>
                        </div>
                    </th>
                </tr>
            </thead>
            <tbody>
            <c:forEach var="supplyAfterSale" items="${supplyAfterSaleList}"  varStatus="num">
                <tr>
                    <td><input class="cid_input" type="checkbox" name="checkOne" value="${supplyAfterSale.skuNo}" autocomplete="off"></td>
                    <td>${supplyAfterSale.skuNo}</td>
                    <td>${supplyAfterSale.showName}</td>
                    <td>${supplyAfterSale.brandName}</td>
                    <td>${supplyAfterSale.spec}</td>
                    <td>${supplyAfterSale.registrationNumber}</td>
                    <td>
                        <c:choose>
                            <c:when test="${queryDto.policyMatained == '0'}">
                                未维护
                            </c:when>
                            <c:when test="${queryDto.policyMatained == '1'}">
                                已维护
                            </c:when>
                            <c:otherwise>
                                <c:if test="${supplyAfterSale.maintained == '0'}">否</c:if>
                                <c:if test="${supplyAfterSale.maintained == '1'}">是</c:if>
                            </c:otherwise>
                        </c:choose>
                    </td>
                </tr>
            </c:forEach>
                <c:if test="${empty supplyAfterSaleList}">
                    <tr>
                        <td colspan='7'>查询无结果！请尝试使用其它搜索条件。</td>
                    </tr>
                </c:if>
            </tbody>
        </table>
    </div>
    <tags:page page="${page}" />
</div>
<br>
<div class="add-tijiao  tcenter">
    <button id="close-layer" type="button" class="dele">取消</button>&nbsp;&nbsp;
    <button class="bt-bg-style bg-deep-green" type="button"
            onclick="copyAfterSales();">复制售后政策</button>
</div>
<br>
<br>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/price/index.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../../common/footer.jsp"%>
