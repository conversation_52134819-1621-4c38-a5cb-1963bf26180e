package com.vedeng.common.shiro.cas.service;

import com.vedeng.uac.api.dto.AccountSignOnLogDto;
import com.vedeng.uac.api.dto.MenuNodeDTO;
import com.vedeng.uac.api.dto.PermissionDTO;
import com.vedeng.uac.api.dto.UserDTO;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR> [<EMAIL>]
 */
public interface UacAccountFacadeService {


    UserDTO getUserByAccountId(Integer accountId);

    MenuNodeDTO getMenu(Integer accountId);

    List<PermissionDTO> listPermission(Integer accountId);

    boolean validAccount(Integer accountId);

    /**
     *
     * UAC记录登录日志
     */
    void insertAccountSignOnLog(AccountSignOnLogDto accountSignOnLogDto);
}
