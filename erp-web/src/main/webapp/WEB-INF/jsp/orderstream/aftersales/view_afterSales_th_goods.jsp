<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
            <table class="table  table-style6">
                <thead>
                    <tr>
                        <th class="wid4">序号</th>
                        <th class="wid8">订货号</th>
                        <th class="wid20">产品名称</th>
                        <th class="wid10">品牌</th>
                        <th class="wid8">制造商型号</th>
                        <th class="wid8">物料编码</th>
                        <th class="wid10">所属采购单号</th>
                        <th>单位</th>
                        <th class="wid8">单价</th>
                        <th>原预定总数</th>
                        <th class="wid9">总额</th>
                        <th>已采购数量</th>
                        <th>退货数量</th>
                        <th>退货金额</th>
                        <th class="wid5">销售发货方式</th>
                        <th class="wid6">退货方式</th>
                        <th>已发货数量</th>
                        <th>系统已拦截数量</th>
                    </tr>
                </thead>
                <tbody>
                <c:set var="sum" value="0"></c:set>
                	<c:if test="${not empty afterSalesGoodsVoListPage}">
                		<c:forEach items="${afterSalesGoodsVoListPage}" var="asg" varStatus="sttaus">
                            <c:set var="sum" value="${sum+asg.num}"></c:set>
	                		<tr class="J-skuInfo-tr" skuId="${asg.goodsId}">
		                        <td>${sttaus.count }</td>
                                <td class="JskuCode"> </td>

		                         <td class="text-left">
			                            <div class="customername pos_rel">
                                            <c:if test="${asg.isActionGoods==1}"><span style="color:red;">【活动】</span></c:if>
                                            <c:if test="${asg.isGift eq '1'}">
                                                <img src="<%= basePath %>static/images/gift_icon.svg" style="width: 15px" />
                                            </c:if>
                                            <c:if test="${asg.isDirectPurchase == 1}">
                                                <img id="isDirectPurchase" src="<%= basePath %>static/images/promote_icon.svg" style="width: 15px" title="商城&quot;现货现价&quot;促销"/>
                                            </c:if>
		                                       <span class="brand-color1 addtitle JskuName" style="float:none;" tabTitle='{"num":"viewgoods${asg.goodsId}",
		                                       		"link":"./goods/goods/viewbaseinfo.do?goodsId=${asg.goodsId}","title":"产品信息"}'> </span><i class="iconbluemouth"></i>

		                                   <div class="pos_abs customernameshow JskuInfo"   style="display: none;">
<%--		                                            注册证：${asg.registrationNumber}<br>--%>
<%--		                                            管理类别：${asg.manageCategoryName}<br>--%>
<%--		                                            产品归属：<c:if test="${not empty asg.userList }">--%>
<%--                                        <c:forEach items="${asg.userList }" var="user" varStatus="st">--%>
<%--										${user.username } <c:if test="${st.count != asg.userList.size() }">、</c:if>--%>
<%--									</c:forEach>--%>
<%--								  </c:if> <br>--%>
<%--&lt;%&ndash;		                                            采购提醒：${asg.purchaseRemind}<br>&ndash;%&gt;--%>
<%--		                                            包装清单：${asg.packingList}<br>--%>
<%--		                                           审核状态：${asg.tos}<br>--%>
<%--&lt;%&ndash;		                               view_afterSales_th.jsp             订单占用：${asg.orderOccupy}<br>&ndash;%&gt;--%>
<%--&lt;%&ndash;		                                            可调剂：${asg.adjustableNum}<br>&ndash;%&gt;--%>
<%--&lt;%&ndash;		                                            库存：${asg.goodsStock}&ndash;%&gt;--%>
		                                        </div>
		                            </div>
		                        </td>
                                <td class="JbrandName"> </td>
                                <td  class="JskuModel"> </td>
                                <td class="JmaterialCode"> </td>
                                <td>
                                    <c:forEach items="${asg.buyorderNos}" var="buyorder">
                                        ${buyorder.buyorderNo}<br>
                                    </c:forEach>
                                </td>
                                <td class="JskuUnit"> </td>
								<td><fmt:formatNumber type="number" value="${asg.saleorderPrice}" pattern="0.00" maxFractionDigits="2" /></td>
		                        <td>${asg.saleorderNum - asg.excludeGoodNum}</td>
		                        <td>

                                    <c:choose>
                                        <c:when test="${saleorder.orderType eq 1}">
                                            <fmt:formatNumber type="number" value="${asg.maxSkuRefundAmount}" pattern="0.00" maxFractionDigits="2" />
                                        </c:when>
                                        <c:otherwise>
                                            <fmt:formatNumber type="number" value="${asg.totalAmount}" pattern="0.00" maxFractionDigits="2" />
                                        </c:otherwise>
                                    </c:choose>

                                </td>
                                <td>${asg.buyNum}</td>
                                <td class="warning-color1">${asg.num}</td>
                                <td>
                                    <c:if test="${empty asg.fag || asg.fag==1}"><fmt:formatNumber type="number" value="${asg.skuRefundAmount}" pattern="0.00" maxFractionDigits="2" /></c:if>
                                    <c:if test="${asg.fag==2}">
                                        <div class="customername pos_rel">
				                        	<span>
				                        		<fmt:formatNumber type="number" value="${asg.skuRefundAmount}" pattern="0.00" maxFractionDigits="2" />
				                        		<i class="iconredsigh ml4 contorlIcon"></i>
				                        	</span>
                                            <div class="pos_abs customernameshow">原值：<fmt:formatNumber type="number" value="${asg.skuOldRefundAmount}" pattern="0.00" maxFractionDigits="2" /></div>
                                        </div>
                                    </c:if>
                                </td>
                                <td>
                                    <c:if test="${asg.saleorderDeliveryDirect eq 0}">普发</c:if>
                                    <c:if test="${asg.saleorderDeliveryDirect eq 1}">直发</c:if>
                                </td>
                                <td class="warning-color1">
                                    <c:if test="${asg.deliveryDirect eq 0}">普发
                                        <c:set var="deliveryNum" value="${deliveryNum + asg.deliveryNum}"></c:set>
                                        <c:set var="nowSalesNum" value="${nowSalesNum + asg.nowSalesNum}"></c:set>
                                    </c:if>
                                    <c:if test="${asg.deliveryDirect eq 1}">直发<c:set var="directNum" value="${directNum+asg.num}"></c:set></c:if>
                                </td>
                                <td>${asg.saleorderDelivery}</td>
                                <td>
                                <%--系统拦截出库数量--%>
                                <%--直发已拦截数量为0--%>
                                <%--普发已拦截数量 = 当前售后数量-实际应退数量--%>
                                    <c:if test="${asg.deliveryDirect eq 1 || (afterSalesVo.atferSalesStatus eq 2 || afterSalesVo.atferSalesStatus eq 3)}">
                                        0
                                    </c:if>
                                    <c:if test="${asg.deliveryDirect eq 0 && (afterSalesVo.atferSalesStatus eq 0 || afterSalesVo.atferSalesStatus eq 1)}">
                                        ${asg.num - asg.rknum}
                                    </c:if>
                                        <%--                                    <span class="warning-color1 mr10"  directNum="${directNum}"--%>
                                        <%--                                          deliveryNum="${deliveryNum}"  nowSalesNum="${nowSalesNum}">--%>
                                        <%--                            	 <c:set var="systemInterceptNum" value="0"></c:set>--%>
                                        <%--                            	 <c:if test="${sum - directNum + deliveryNum le nowSalesNum }">${sum - directNum}<c:set var="systemInterceptNum" value="${sum - directNum}"></c:set></c:if>--%>
                                        <%--                            	 <c:if test="${sum - directNum + deliveryNum gt nowSalesNum }">${nowSalesNum-deliveryNum > 0 ? nowSalesNum-deliveryNum : 0}--%>
                                        <%--                                     <c:set var="systemInterceptNum" value="${nowSalesNum-deliveryNum > 0 ? nowSalesNum-deliveryNum : 0}"></c:set>--%>
                                        <%--                                 </c:if>--%>
                                        <%--                                    </span>--%>
                                </td>
		                    </tr>
	                	</c:forEach>
                    </c:if>
                    <c:if test="${empty afterSalesGoodsVoListPage}">
	                    <tr>
	                        <td colspan="20">暂无记录</td>
	                    </tr>
                    </c:if>
                </tbody>
            </table>

<script type="text/javascript" src='<%= basePath %>static/new/js/pages/goods/goodinfoajax.js?rnd=${resourceVersionKey}'></script>
