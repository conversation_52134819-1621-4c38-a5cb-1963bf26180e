package com.vedeng.kpi.controller;

import com.google.common.collect.Lists;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.StringUtil;
import com.vedeng.kpi.cache.KpiLoadingCache;
import com.vedeng.kpi.model.DO.KpiDailyCountDo;
import com.vedeng.kpi.model.DTO.*;
import com.vedeng.kpi.model.DTO.emptyEntity.NullKpiConfigItemDto;
import com.vedeng.kpi.model.DTO.emptyEntity.NullKpiDailyCountExtDto;
import com.vedeng.kpi.model.VO.*;
import com.vedeng.kpi.model.VO.emptyEntity.NullKpiDetailBdKhOverviewVo;
import com.vedeng.kpi.model.VO.emptyEntity.NullKpiDetailKhOverviewVo;
import com.vedeng.kpi.model.VO.emptyEntity.NullKpiDetailXjOverviewVo;
import com.vedeng.kpi.model.VO.emptyEntity.NullKpiDetailYjOverviewVo;
import com.vedeng.kpi.service.*;
import com.vedeng.kpi.share.KpiCommonConstant;
import com.vedeng.kpi.share.KpiUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/kpi")
/**
 * @description: 统一controller.
 * @jira: VDERP-2376【五行剑法】规则修改.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/6/28 4:14 下午.
 * @author: Tomcat.Hui.
 */ 
public class KpiController {

    private static final Logger log = LoggerFactory.getLogger("kpilog");

    private static final ThreadLocal<DateFormat> commonSdf =
            ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM"));

    @Autowired
    KpiParamTransService kpiParamTransService;

    @Autowired
    KpiUpdateService kpiUpdateService;

    @Autowired
    KpiOrderLogService kpiOrderLogService;

    @Autowired
    KpiDailyCountService kpiDailyCountService;

    @Autowired
    KpiLoadingCache kpiLoadingCache;

    @Autowired
    KpiChanceService kpiChanceService;

    @Autowired
    KpiBdCustomerService kpiBdCustomerService;

    @Autowired
    KpiCustomerService kpiCustomerService;

    @Autowired
    KpiAmountService kpiAmountService;


    @RequestMapping("/update/updateSingleKpi")
    public ResultInfo updateSingleKpi(HttpServletRequest request, @RequestBody KpiUserInfoDto userInfo){
        try {
            KpiDataQueryDto query = new KpiDataQueryDto();
            query.setKpiDateEnd(KpiUtils.getDateStart(new Date()));

            log.info("开始更新 " + userInfo.getUserId() + userInfo.getKpiDateEnd().toString() + " 日五行数据");
            if(null == kpiLoadingCache.getUserConfig(userInfo.getUserId())) {
                return new ResultInfo(-1 ,"用户不存在");
            }
            return new ResultInfo(0,"success");
        } catch (Exception e){
            log.error("更新 " + userInfo.getUserId() + userInfo.getKpiDateEnd().toString() + " 日五行数据出现异常: ",e);
            return new ResultInfo(-1 ,"error");
        }

    }

    @RequestMapping("/query/queryKpiDetailYj")
    @ResponseBody
    public ResultInfo<KpiDetailYjPageVo> queryKpiDetailYj(HttpServletRequest request,KpiUserInfoDto userInfo,String month){

        try {
            Integer currentUserId = userInfo.getUserId();
            userInfo = (KpiUserInfoDto) kpiParamTransService.getCurrentUserInfo(userInfo);

            //如果不是历史月,则截止到今日,如果是历史月,则截止到当月最后一天
            Date now = KpiUtils.getDateStart(new Date());
            if (!month.equals(commonSdf.get().format(now))) {
                now = KpiUtils.getMonthEnd(commonSdf.get().parse(month));
            }

            //概况
            KpiDetailYjPageVo result = getYjOverview(userInfo,KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_YJ,now);

            //当前用户到款额、退款额记录
            KpiDataQueryDto logQuery = new KpiDataQueryDto();
            logQuery.setUserId(currentUserId);
            logQuery.setKpiDateEnd(now);
            logQuery.setKpiDateStart(KpiUtils.getMonthStart(now));
            List<KpiDataQueryDto> logDetail = kpiOrderLogService.getKpiLogDetail(logQuery);
            List<KpiLogDetailVo> logDetailVoList = logDetail.stream().map(log -> getKpiLogVo(log)).collect(Collectors.toList());
            result.setSaleOrderLogList(logDetailVoList.stream()
                    .filter(kpi -> kpi.getOperation().equals(1)).collect(Collectors.toList()));
            result.setAfterSaleLogList(logDetailVoList.stream()
                    .filter(kpi -> Arrays.asList(2,3).contains(kpi.getOperation())).collect(Collectors.toList()));

            //已计入业绩&未到全款
            List<KpiDataQueryDto> sendAndNotPaid = kpiAmountService.getSendNotPaid(logQuery);
            result.setSendAndNotPaid(sendAndNotPaid.stream().map(kpi -> getKpiLogVo(kpi)).collect(Collectors.toList()));

            //可发货&未计入业绩
            List<KpiDataQueryDto> paidNotKpi = kpiAmountService.getPaidNotKpi(logQuery);
            result.setPaidNotKpi(paidNotKpi.stream().map(kpi -> getKpiLogVo(kpi)).collect(Collectors.toList()));

            //当前用户历史记录(按月)
            KpiDataQueryDto hisQuery = new KpiDataQueryDto();
            hisQuery.setUserId(currentUserId);
            hisQuery.setKpiDateEnd(now);
            Map<String,List<KpiDailyCountExtDto>> hisMap = kpiDailyCountService.selectBySingleUserHis(hisQuery,
                    KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_YJ);

            result.setHisMonthList(hisMap.entrySet().stream()
                    .map(e -> yjHisDataHandle(e.getKey(),e.getValue(),currentUserId))
                    .sorted(Comparator.comparing(KpiDetailYjOverviewVo::getMonthStr).reversed())
                    .collect(Collectors.toList()));

            return new ResultInfo<>(0,"success",result);
        } catch (Exception e){
            log.error("查询五行业绩接口出现异常 ",e);
            return new ResultInfo<>(-1,"查询五行业绩接口出现异常");
        }

    }

    @RequestMapping("/query/queryKpiDetailKh")
    @ResponseBody
    public ResultInfo<KpiDetailKhPageVo> queryKpiDetailKh(HttpServletRequest request,KpiUserInfoDto userInfo,String month){

        try {
            Integer currentUserId = userInfo.getUserId();
            userInfo = (KpiUserInfoDto) kpiParamTransService.getCurrentUserInfo(userInfo);
            //如果不是历史月,则截止到今日,如果是历史月,则截止到当月最后一天
            Date now = KpiUtils.getDateStart(new Date());
            if (!month.equals(commonSdf.get().format(now))) {
                now = KpiUtils.getMonthEnd(commonSdf.get().parse(month));
            }

            //概况
            KpiDetailKhPageVo result = getKhOverview(userInfo,KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_KH,now);

            //明细
            KpiDataQueryDto logQuery = new KpiDataQueryDto();
            logQuery.setUserId(currentUserId);
            logQuery.setKpiDateEnd(now);
            logQuery.setKpiDateStart(KpiUtils.getMonthStart(now));
            logQuery.setDays(90);
            List<KpiDataQueryDto> logTraders = kpiCustomerService.getUserCoCustomers(logQuery);

            //合作(90天)
            logQuery.setOperation(1);
            List<Integer> ids = logTraders.stream()
                    .filter(t -> t.getCoOrderNum() > 0)
                    .map(KpiDataQueryDto::getTraderId).collect(Collectors.toList());
            if (null != ids && ids.size() > 0) {
                logQuery.setTraderIds(ids);

                List<KpiLogDetailVo> coLogs_90 = kpiCustomerService.getUserCoCustomersDetail(logQuery).stream()
                        .map(log -> getKpiLogVo(log)).collect(Collectors.toList());
                result.setCoCustomerList_90(this.getLastTimes(coLogs_90));
            } else {
                result.setCoCustomerList_90(Lists.newArrayList());
            }

            //流失(90天)
            logQuery.setOperation(3);
            ids = logTraders.stream()
                    .filter(t -> t.getCoOrderNum() <= 0)
                    .map(KpiDataQueryDto::getTraderId).collect(Collectors.toList());
            if (null != ids && ids.size() > 0) {
                logQuery.setTraderIds(ids);
                List<KpiLogDetailVo> lostLogs_90 = kpiCustomerService.getUserCoCustomersDetail(logQuery).stream()
                        .map(log -> getKpiLogVo(log)).collect(Collectors.toList());
                result.setLostCustomerList_90(this.getLastTimes(lostLogs_90));
            } else {
                result.setLostCustomerList_90(Lists.newArrayList());
            }

            //流失(180天)
            logQuery.setDays(180);
            logTraders = kpiCustomerService.getUserCoCustomers(logQuery);
            ids = logTraders.stream()
                    .filter(t -> t.getCoOrderNum() <= 0)
                    .map(KpiDataQueryDto::getTraderId).collect(Collectors.toList());
            if (null != ids && ids.size() > 0) {
                logQuery.setTraderIds(ids);
                List<KpiLogDetailVo> lostLogs_180 = kpiCustomerService.getUserCoCustomersDetail(logQuery).stream()
                        .map(log -> getKpiLogVo(log)).collect(Collectors.toList());
                result.setLostCustomerList_180(this.getLastTimes(lostLogs_180));
            } else {
                result.setLostCustomerList_180(Lists.newArrayList());
            }

            return new ResultInfo<>(0,"success",result);
        }catch (Exception e){
            log.error("查询五行BD客户详情接口出现异常 ",e);
            return new ResultInfo<>(-1,"查询客户询价接口出现异常");
        }

    }

    @RequestMapping("/query/queryKpiDetailBdkh")
    @ResponseBody
    public ResultInfo<KpiDetailBdKhPageVo> queryKpiDetailBdkh(HttpServletRequest request,KpiUserInfoDto userInfo,String month){

        try {
            Integer currentUserId = userInfo.getUserId();
            userInfo = (KpiUserInfoDto) kpiParamTransService.getCurrentUserInfo(userInfo);
            //如果不是历史月,则截止到今日,如果是历史月,则截止到当月最后一天
            Date now = KpiUtils.getDateStart(new Date());
            if (!month.equals(commonSdf.get().format(now))) {
                now = KpiUtils.getMonthEnd(commonSdf.get().parse(month));
            }

            //概况
            KpiDetailBdKhPageVo result = getBdKhOverview(userInfo,KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_BDKH,now);

            //明细
            KpiDataQueryDto logQuery = new KpiDataQueryDto();
            logQuery.setUserId(currentUserId);
            logQuery.setKpiDateEnd(now);
            logQuery.setKpiDateStart(KpiUtils.getMonthStart(now));


            //新增BD客户数
            logQuery.setOrderType(1);
            List<KpiDataQueryDto> logDetail = kpiBdCustomerService.getKpiBdCoDetail(logQuery);
            List<KpiLogDetailVo> coList = logDetail.stream()
                    .map(log -> getKpiLogVo(log)).collect(Collectors.toList());
            result.setMonthCoLog(coList);

            //流失BD客户数
            List<KpiLogDetailVo> lostList = kpiBdCustomerService.getKpiBdLostDetail(logQuery).stream()
                    .filter(log -> log.getRetFlag().equals(1))
                    .map(log -> getKpiLogVo(log)).collect(Collectors.toList());
            result.setMonthLostLog(lostList);

            //当前用户历史记录(按月)
            KpiDataQueryDto hisQuery = new KpiDataQueryDto();
            hisQuery.setUserId(currentUserId);
            hisQuery.setKpiDateEnd(now);
            Map<String,List<KpiDailyCountExtDto>> hisMap = kpiDailyCountService
                    .selectBySingleUserHis(hisQuery, KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_BDKH);

            result.setHisMonthList(hisMap.entrySet().stream()
                    .map(e -> this.bdkhHisDataHandle(e.getKey(),e.getValue(),currentUserId))
                    .sorted(Comparator.comparing(KpiDetailBdKhOverviewVo::getMonthStr).reversed())
                    .collect(Collectors.toList()));
            return new ResultInfo<>(0,"success",result);
        }catch (Exception e) {
            log.error("查询五行BD客户详情接口出现异常 ",e);
            return new ResultInfo<>(-1,"查询客户询价接口出现异常");
        }
    }

    @RequestMapping("/query/queryKpiDetailXj")
    @ResponseBody
    public ResultInfo<KpiDetailXjPageVo> queryKpiDetailXj(HttpServletRequest request,KpiUserInfoDto userInfo,String month){

        try {
            Integer currentUserId = userInfo.getUserId();
            userInfo = (KpiUserInfoDto) kpiParamTransService.getCurrentUserInfo(userInfo);
            //如果不是历史月,则截止到今日,如果是历史月,则截止到当月最后一天
            Date now = KpiUtils.getDateStart(new Date());
            if (!month.equals(commonSdf.get().format(now))) {
                now = KpiUtils.getMonthEnd(commonSdf.get().parse(month));
            }

            //概况
            KpiDetailXjPageVo result = getXjOverview(userInfo,KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_XJ,now);

            //明细(成交)
            KpiDataQueryDto logQuery = new KpiDataQueryDto();
            logQuery.setUserId(currentUserId);
            logQuery.setKpiDateEnd(now);
            logQuery.setKpiDateStart(KpiUtils.getMonthStart(now));
            List<KpiDataQueryDto> logDetail = kpiOrderLogService.getKpiLogDetail(logQuery);
            List<KpiLogDetailVo> logDetailVoList = logDetail.stream().map(log -> getKpiLogVo(log))
                    .collect(Collectors.toList());
            result.setSuccessChanceDetailList(logDetailVoList.stream()
                    .filter(kpi -> null != kpi.getChanceTransFlag() && kpi.getChanceTransFlag().equals(1))
                    .collect(Collectors.toList()));
            result.setFailChanceDetailList(logDetailVoList.stream()
                    .filter(kpi -> null != kpi.getChanceTransFlag() && kpi.getChanceTransFlag().equals(0) )
                    .collect(Collectors.toList()));

            //当前用户历史记录(按月)
            KpiDataQueryDto hisQuery = new KpiDataQueryDto();
            hisQuery.setUserId(currentUserId);
            hisQuery.setKpiDateEnd(now);
            Map<String,List<KpiDailyCountExtDto>> hisMap = kpiDailyCountService.selectBySingleUserHis(hisQuery, KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_XJ);

            result.setHisMonthList(hisMap.entrySet().stream()
                    .map(e -> this.xjHisDataHandle(e.getKey(),e.getValue(),currentUserId))
                    .sorted(Comparator.comparing(KpiDetailXjOverviewVo::getMonthStr).reversed())
                    .collect(Collectors.toList()));

            return new ResultInfo<>(0,"success",result);
        } catch (Exception e){
            log.error("查询五行询价详情接口出现异常 ",e);
            return new ResultInfo<>(-1,"查询客户询价接口出现异常");
        }
    }

    @RequestMapping("/query/queryGroupPageData")
    @ResponseBody
    public ModelAndView queryGroupPageData(HttpServletRequest request,String queryMonth) throws ParseException {

        ModelAndView mv = new ModelAndView();
        mv.setViewName("saleperformance/group/group_detail");

        KpiDataQueryDto query = new KpiDataQueryDto();
        // add by Tomcat.Hui 2020/8/10 11:42 上午 .Desc: VDERP-3094【五行剑法】优化得分排名和转化率逻辑. start
        //历史月份的筛选项只需要从2020.7月份开始即可，在此之前的无需展示
        //小组详情页和团队详情页也加上如此的历史月份筛选框
        List<String> monthList = KpiUtils.getMonthListStr(new Date(),12)
                .stream().sorted(Comparator.comparing(String::trim).reversed()).collect(Collectors.toList());
        mv.addObject("monthStrList",monthList);

        //根据查询月份展示数据
        Date now = KpiUtils.getDateStart(new Date());
        Date queryDate = null;
        if (StringUtil.isBlank(queryMonth) || queryMonth.equals(commonSdf.get().format(now))) {
            queryDate = KpiUtils.getDateStart(new Date());
        } else {
            queryDate = KpiUtils.getMonthEnd(commonSdf.get().parse(queryMonth));
        }
        query.setKpiDateEnd(queryDate);
        mv.addObject("dataMonth",commonSdf.get().format(queryDate));
        // add by Tomcat.Hui 2020/8/10 11:42 上午 .Desc: VDERP-3094【五行剑法】优化得分排名和转化率逻辑. end


        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        KpiUserInfoDto userInfo = new KpiUserInfoDto();
        userInfo.setUserName(user.getUsername());
        userInfo.setUserId(user.getUserId());

        // change by Tomcat.Hui 2020/7/16 2:35 下午 .Desc: 要求按照全团设置总排名,不再分组排序.
        List<KpiUserConfigDto> users;
        if (user.getUsername().equals("njadmin")) {
            users = kpiLoadingCache.getAllUserConfig();
        } else {
            users = kpiLoadingCache.getManagerUsers(userInfo).stream()
                    .map(KpiUserConfigDto::getGroupId).distinct().map(id ->
                            kpiLoadingCache.getGroupUsersByGroupId(id)).flatMap(l -> l.stream()).collect(Collectors.toList());        }


        if (users.size() == 0) {
            mv.addObject("data", null);
            mv.addObject("dataAll", null);
            return mv;
        }

        query.setUserIds(users.stream().map(KpiUserConfigDto::getUserId).collect(Collectors.toList()));

        Map<String, Map<Integer, KpiDailyCountExtDto>> groupDataAll = kpiDailyCountService.getGroupCountDataAll(query);
        Map<KpiGroupVo,Map<KpiTeamVo, KpiGroupCountSumVo>> groupData = kpiDailyCountService.getGroupCountData(groupDataAll,query);

        Map<String, List<KpiDailyCountExtDto>> temp = groupDataAll.entrySet().stream()
                .collect(Collectors.toMap(e -> e.getKey(),
                        e -> e.getValue().values().stream().collect(Collectors.toList())));

        mv.addObject("data", groupData);
        mv.addObject("dataAll", kpiDailyCountService.transGroupHisData(temp));
        return mv;
    }

    @RequestMapping("/query/queryTeamPageData")
    @ResponseBody
    public ModelAndView queryTeamPageData(HttpServletRequest request,Integer groupId,Integer teamId,String queryMonth) throws ParseException {
        ModelAndView mv = new ModelAndView();
        mv.setViewName("saleperformance/group/dept_detail");
        KpiDataQueryDto query = new KpiDataQueryDto();

        // add by Tomcat.Hui 2020/8/10 11:42 上午 .Desc: VDERP-3094【五行剑法】优化得分排名和转化率逻辑. start
        //历史月份的筛选项只需要从2020.7月份开始即可，在此之前的无需展示
        //小组详情页和团队详情页也加上如此的历史月份筛选框
        List<String> monthList = KpiUtils.getMonthListStr(new Date(),12)
                .stream().sorted(Comparator.comparing(String::trim).reversed()).collect(Collectors.toList());
        mv.addObject("monthStrList",monthList);

        //根据查询月份展示数据
        Date now = KpiUtils.getDateStart(new Date());
        Date queryDate = null;
        if (StringUtil.isBlank(queryMonth) || queryMonth.equals(commonSdf.get().format(now))) {
            queryDate = KpiUtils.getDateStart(new Date());
        } else {
            queryDate = KpiUtils.getMonthEnd(commonSdf.get().parse(queryMonth));
        }
        query.setKpiDateEnd(queryDate);
        mv.addObject("dataMonth",commonSdf.get().format(queryDate));
        mv.addObject("teamId",teamId);
        mv.addObject("groupId",groupId);
        // add by Tomcat.Hui 2020/8/10 11:42 上午 .Desc: VDERP-3094【五行剑法】优化得分排名和转化率逻辑. end

        //权限
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        KpiUserInfoDto userInfo = new KpiUserInfoDto();
        userInfo.setUserName(user.getUsername());
        userInfo.setUserId(user.getUserId());

        // change by Tomcat.Hui 2020/7/16 2:35 下午 .Desc: 要求按照全团设置总排名,不再分组排序.
        List<KpiUserConfigDto> users;
        if (user.getUsername().equals("njadmin")) {
            users = kpiLoadingCache.getAllUserConfig();
        } else {
            users = kpiLoadingCache.getManagerUsers(userInfo).stream()
                    .map(KpiUserConfigDto::getGroupId).distinct().map(id ->
                    kpiLoadingCache.getGroupUsersByGroupId(id)).flatMap(l -> l.stream()).collect(Collectors.toList());
        }

        if (users.size() == 0) {
            mv.addObject("data", null);
            mv.addObject("dataAll", null);
            return mv;
        }
        //如果是团队详情页面跳转来的,需要再次过滤
        if (teamId != null && groupId != null) {
            userInfo.setTeamId(teamId);
            userInfo.setGroupId(groupId);
            users = users.stream().filter(u -> u.getGroupId().equals(groupId) && u.getTeamId().equals(teamId))
                    .collect(Collectors.toList());
        }

        query.setUserIds(users.stream().map(KpiUserConfigDto::getUserId).collect(Collectors.toList()));

        Map<String, Map<Integer, KpiDailyCountExtDto>> allData = kpiDailyCountService.getTeamCountDataAll(query);
        Map<String,List<KpiDailyCountVo>> teamData = kpiDailyCountService.getTeamCountData(allData,userInfo,queryDate);

        Map<String, List<KpiDailyCountExtDto>> temp = allData.entrySet().stream()
                .collect(Collectors.toMap(e -> e.getKey(),
                        e -> e.getValue().values().stream().collect(Collectors.toList())));


        mv.addObject("data", teamData);
        mv.addObject("dataAll", kpiDailyCountService.transGroupHisData(temp));
        return mv;
    }

    private KpiDetailYjPageVo getYjOverview(KpiUserInfoDto userInfo, String kpiType, Date now){
        Integer currentUserId = userInfo.getUserId();
        KpiDetailYjPageVo result = new KpiDetailYjPageVo();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        //查询当日团队内所有用户当月kpi(每人取最后一条)
        List<KpiDailyCountExtDto> nowKpiList = kpiDailyCountService.getKpiCountList(
                userInfo,
                KpiUtils.getMonthStart(now),
                now,
                kpiType);
        //设置当前用户
        KpiDetailYjOverviewVo currentUserOverview = nowKpiList.stream()
                .filter(kpi -> kpi.getUserId().equals(currentUserId))
                .map(kpi -> getKpiYjOverviewVo(kpi))
                .findFirst().orElseGet(() -> {
                    NullKpiDetailYjOverviewVo nullResult =new NullKpiDetailYjOverviewVo();
                    return nullResult;
                });

        currentUserOverview.setMonthTarget(kpiLoadingCache.getHisTargetByUser(currentUserId,
                sdf.format(KpiUtils.getMonthStart(now))));
        result.setPersonalKpi(currentUserOverview);

        //查询团队统计结果
        KpiDetailYjOverviewVo groupKpi = new KpiDetailYjOverviewVo();
        BigDecimal groupAmount = nowKpiList.stream()
                .map(KpiDailyCountExtDto::getKpiAmount)
                .reduce(BigDecimal.ZERO,BigDecimal::add);
        groupKpi.setYjAmount(groupAmount);
        groupKpi.setMonthTarget(
                kpiLoadingCache.getGroupMonthGoal(userInfo.getGroupId(),now));
        groupKpi.setProgress(new BigDecimal(nowKpiList.stream()
                .map(KpiDailyCountDo::getKpiAmountProgress)
                .collect(Collectors.averagingDouble(BigDecimal::doubleValue))));
        groupKpi.setSort(-1);
        // changed by Tomcat.Hui 2020/8/10 11:21 上午 .Desc: VDERP-3094 【五行剑法】优化得分排名和转化率逻辑.
        groupKpi.setScore(getKpiGroupScore(userInfo, kpiType));
        result.setGroupKpi(groupKpi);

        //查询榜首统计结果
        KpiDetailYjOverviewVo first = getKpiYjOverviewVo(nowKpiList.stream().filter(kpi -> kpi.getSort().equals(1))
                .findFirst().orElse(new NullKpiDailyCountExtDto()));
        first.setMonthTarget( null == first.getUserId() ? BigDecimal.ZERO:
                kpiLoadingCache.getHisTargetByUser(first.getUserId(),
                        sdf.format(KpiUtils.getMonthStart(now))));
        result.setFirstKpi(first);

        //昨日统计结果
        Date yesterday = KpiUtils.getYesterday(KpiUtils.getDateStart(now));
        KpiDailyCountExtDto yesterdayKpi = kpiDailyCountService.getKpiCountVoByUser(
                userInfo,
                KpiUtils.getMonthStart(yesterday),
                yesterday,
                kpiType);
        KpiDetailYjOverviewVo yesterdayOverview = getKpiYjOverviewVo(yesterdayKpi);
        yesterdayOverview.setMonthTarget(kpiLoadingCache.getHisTargetByUser(currentUserId,
                sdf.format(KpiUtils.getMonthStart(yesterday))));
        result.setYesterdayKpi(yesterdayOverview);

        //上月统计结果
        Date lastMonthLastDay = KpiUtils.getLastMonthLastDay(now);
        KpiDailyCountExtDto lastMonthKpi = kpiDailyCountService.getKpiCountVoByUser(
                userInfo,
                KpiUtils.getMonthStart(lastMonthLastDay),
                lastMonthLastDay,
                kpiType);
        lastMonthKpi.setMonthStr(sdf.format(lastMonthLastDay));
        KpiDetailYjOverviewVo lastmonthOverview = getKpiYjOverviewVo(lastMonthKpi);
        lastmonthOverview.setMonthTarget(kpiLoadingCache.getHisTargetByUser(currentUserId,
                sdf.format(KpiUtils.getMonthStart(lastMonthLastDay))));
        result.setLastMonthKpi(lastmonthOverview);
        return result;
    }

    /**
     * 获取团队得分.
     * @jira: VDERP-3094 【五行剑法】优化得分排名和转化率逻辑.
     * @notes: 五行各模块详情页中的团队分数，取每项考核比例*100.
     * @version: 1.0.
     * @date: 2020/8/10 11:28 上午.
     * @author: Tomcat.Hui.
     * @param userInfo: 当前用户.
     * @param kpiType: 五行类型.
     * @return: java.math.BigDecimal.
     * @throw: .
     */
    private BigDecimal getKpiGroupScore(KpiUserInfoDto userInfo, String kpiType) {
        return kpiLoadingCache.getGroupConfig(userInfo.getGroupId()).getConfigItems().stream()
                    .filter(item -> item.getItem().equals(kpiType))
                    .findFirst().orElse(new NullKpiConfigItemDto()).getWeight();
    }

    private KpiDetailKhPageVo getKhOverview(KpiUserInfoDto userInfo, String kpiType, Date now){
        Integer currentUserId = userInfo.getUserId();
        KpiDetailKhPageVo result = new KpiDetailKhPageVo();
        //查询当日团队内所有用户当月kpi(每人取最后一条)

        List<KpiDailyCountExtDto> nowKpiList = kpiDailyCountService.getKpiCountList(
                userInfo,
                KpiUtils.getMonthStart(now),
                now,
                kpiType);

        //设置当前用户
        result.setPersonalKpi(nowKpiList.stream()
                .filter(kpi -> kpi.getUserId().equals(currentUserId))
                .map(kpi -> getKpiKhOverviewVo(kpi))
                .findFirst().orElseGet(() -> {
                    NullKpiDetailKhOverviewVo nullResult = new NullKpiDetailKhOverviewVo();
                    return nullResult;
                }));

        //查询团队统计结果
        KpiDetailKhOverviewVo groupKpi = new KpiDetailKhOverviewVo();
        AtomicReference<Integer> sumCoNum = new AtomicReference<>(0);
        AtomicReference<Integer> sumLostNum = new AtomicReference<>(0);
        nowKpiList.stream()
                .forEach(kpi -> {
                    sumCoNum.updateAndGet(v -> v + Optional.ofNullable(kpi.getCoCustomerNum()).orElse(0));
                    sumLostNum.updateAndGet(v -> v + Optional.ofNullable(kpi.getLostCustomerNum()).orElse(0));
                });
        groupKpi.setCoCustomerNum(sumCoNum.get());
        groupKpi.setLostCustomerNum(sumLostNum.get());
        groupKpi.setSort(-1);
        // changed by Tomcat.Hui 2020/8/10 11:21 上午 .Desc: VDERP-3094 【五行剑法】优化得分排名和转化率逻辑.
        groupKpi.setScore(getKpiGroupScore(userInfo, kpiType));
        result.setGroupKpi(groupKpi);

        //查询榜首统计结果
        KpiDetailKhOverviewVo first = nowKpiList.stream().filter(kpi -> kpi.getSort().equals(1))
                .map(kpi -> getKpiKhOverviewVo(kpi))
                .findFirst().orElse(new NullKpiDetailKhOverviewVo());
        result.setFirstKpi(first);

        //昨日统计结果
        Date yesterday = KpiUtils.getYesterday(KpiUtils.getDateStart(now));
        KpiDailyCountExtDto yesterdayKpi = kpiDailyCountService.getKpiCountVoByUser(
                userInfo,
                KpiUtils.getMonthStart(yesterday),
                yesterday,
                kpiType);
        result.setYesterdayKpi(getKpiKhOverviewVo(yesterdayKpi));

        //上月统计结果
        Date lastMonthLastDay = KpiUtils.getLastMonthLastDay(now);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        KpiDailyCountExtDto lastMonthKpi = kpiDailyCountService.getKpiCountVoByUser(
                userInfo,
                KpiUtils.getMonthStart(lastMonthLastDay),
                lastMonthLastDay,
                kpiType);
        lastMonthKpi.setMonthStr(sdf.format(lastMonthLastDay));
        result.setLastMonthKpi(getKpiKhOverviewVo(lastMonthKpi));
        return result;
    }

    private KpiDetailBdKhPageVo getBdKhOverview(KpiUserInfoDto userInfo, String kpiType, Date now){
        Integer currentUserId = userInfo.getUserId();
        KpiDetailBdKhPageVo result = new KpiDetailBdKhPageVo();
        //查询当日团队内所有用户当月kpi(每人取最后一条)

        List<KpiDailyCountExtDto> nowKpiList = kpiDailyCountService.getKpiCountList(
                userInfo,
                KpiUtils.getMonthStart(now),
                now,
                kpiType);

        //设置当前用户
        result.setPersonalKpi(nowKpiList.stream()
                .filter(kpi -> kpi.getUserId().equals(currentUserId))
                .map(kpi -> getKpiBdKhOverviewVo(kpi))
                .findFirst().orElseGet(() -> new NullKpiDetailBdKhOverviewVo()));

        //查询团队统计结果
        KpiDetailBdKhOverviewVo groupKpi = new KpiDetailBdKhOverviewVo();
        AtomicReference<Integer> sumCoNum = new AtomicReference<>(0);
        AtomicReference<Integer> sumLostNum = new AtomicReference<>(0);
        nowKpiList.stream()
                .forEach(kpi -> {
                    sumCoNum.updateAndGet(v -> v + Optional.ofNullable(kpi.getBdNewCustomerNum()).orElse(0));
                    sumLostNum.updateAndGet(v -> v + Optional.ofNullable(kpi.getBdLostCustomerNum()).orElse(0));
                });
        groupKpi.setCoBdCustomerNum(sumCoNum.get());
        groupKpi.setLostBdCustomerNum(sumLostNum.get());
        groupKpi.setSort(-1);
        // changed by Tomcat.Hui 2020/8/10 11:21 上午 .Desc: VDERP-3094 【五行剑法】优化得分排名和转化率逻辑.
        groupKpi.setScore(getKpiGroupScore(userInfo, kpiType));
        result.setGroupKpi(groupKpi);

        //查询榜首统计结果
        KpiDetailBdKhOverviewVo first = nowKpiList.stream().filter(kpi -> kpi.getSort().equals(1))
                .map(kpi -> getKpiBdKhOverviewVo(kpi))
                .findFirst().orElse(new NullKpiDetailBdKhOverviewVo());
        result.setFirstKpi(first);

        //昨日统计结果
        Date yesterday = KpiUtils.getYesterday(KpiUtils.getDateStart(now));
        KpiDailyCountExtDto yesterdayKpi = kpiDailyCountService.getKpiCountVoByUser(
                userInfo,
                KpiUtils.getMonthStart(yesterday),
                yesterday,
                kpiType);
        result.setYesterdayKpi(getKpiBdKhOverviewVo(yesterdayKpi));

        //上月统计结果
        Date lastMonthLastDay = KpiUtils.getLastMonthLastDay(now);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        KpiDailyCountExtDto lastMonthKpi = kpiDailyCountService.getKpiCountVoByUser(
                userInfo,
                KpiUtils.getMonthStart(lastMonthLastDay),
                lastMonthLastDay,
                kpiType);
        lastMonthKpi.setMonthStr(sdf.format(lastMonthLastDay));
        result.setLastMonthKpi(getKpiBdKhOverviewVo(lastMonthKpi));
        return result;
    }

    private KpiDetailXjPageVo getXjOverview(KpiUserInfoDto userInfo, String kpiType, Date now){
        Integer currentUserId = userInfo.getUserId();
        KpiDetailXjPageVo result = new KpiDetailXjPageVo();
        //查询当日团队内所有用户当月kpi(每人取最后一条)

        List<KpiDailyCountExtDto> nowKpiList = kpiDailyCountService.getKpiCountList(
                userInfo,
                KpiUtils.getMonthStart(now),
                now,
                kpiType);

        //设置当前用户
        result.setPersonalKpi(nowKpiList.stream()
                .filter(kpi -> kpi.getUserId().equals(currentUserId))
                .map(kpi -> getKpiXjOverviewVo(kpi,KpiUtils.getMonthStart(now),now))
                .findFirst().orElseGet(() -> new NullKpiDetailXjOverviewVo()));

        //查询团队统计结果
        KpiDetailXjOverviewVo groupKpi = new KpiDetailXjOverviewVo();
        AtomicReference<Integer> successNum = new AtomicReference<>(0);
        AtomicReference<Integer> failNum = new AtomicReference<>(0);
        nowKpiList.stream()
                .forEach(kpi -> {
                    successNum.updateAndGet(v -> v + Optional.ofNullable(kpi.getChanceSuccessNum()).orElse(0));
                    failNum.updateAndGet(v -> v + Optional.ofNullable(kpi.getChanceFailNum()).orElse(0));
                });
        groupKpi.setSuccessChance(successNum.get());
        groupKpi.setFailChance(failNum.get());
        groupKpi.setSort(-1);
        // changed by Tomcat.Hui 2020/8/10 11:21 上午 .Desc: VDERP-3094 【五行剑法】优化得分排名和转化率逻辑.
        groupKpi.setScore(getKpiGroupScore(userInfo, kpiType));

        groupKpi.setRangeCustomerNum(nowKpiList.stream().map(KpiDailyCountExtDto::getChanceRangeCustomerNum)
                .collect(Collectors.summingInt(Integer::intValue)));
        groupKpi.setTransProgress(new BigDecimal(nowKpiList.stream()
                .map(KpiDailyCountExtDto::getChanceTransProportion)
                .collect(Collectors.averagingDouble(BigDecimal::doubleValue))));
        result.setGroupKpi(groupKpi);

        //查询榜首统计结果
        KpiDetailXjOverviewVo first = nowKpiList.stream().filter(kpi -> kpi.getSort().equals(1))
                .map(kpi -> getKpiXjOverviewVo(kpi,KpiUtils.getMonthStart(now),now))
                .findFirst().orElse(new NullKpiDetailXjOverviewVo());
        result.setFirstKpi(first);

        //昨日统计结果
        Date yesterday = KpiUtils.getYesterday(KpiUtils.getDateStart(now));
        KpiDailyCountExtDto yesterdayKpi = kpiDailyCountService.getKpiCountVoByUser(
                userInfo,
                KpiUtils.getMonthStart(yesterday),
                yesterday,
                kpiType);
        result.setYesterdayKpi(getKpiXjOverviewVo(yesterdayKpi,KpiUtils.getMonthStart(yesterday),yesterday));

        //上月统计结果
        Date lastMonthLastDay = KpiUtils.getLastMonthLastDay(now);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        KpiDailyCountExtDto lastMonthKpi = kpiDailyCountService.getKpiCountVoByUser(
                userInfo,
                KpiUtils.getMonthStart(lastMonthLastDay),
                lastMonthLastDay,
                kpiType);
        lastMonthKpi.setMonthStr(sdf.format(lastMonthLastDay));
        result.setLastMonthKpi(getKpiXjOverviewVo(lastMonthKpi,KpiUtils.getMonthStart(lastMonthLastDay),lastMonthLastDay));
        return result;
    }

    /**
     * @description: 个人业绩详情历史数据vo类转换.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/6/21 9:14 下午.
     * @author: Tomcat.Hui.
     * @param monthStr: .
     * @param monthData: .
     * @param userId: .
     * @return: com.vedeng.kpi.model.VO.KpiDetailYjOverviewVo.
     * @throws: .
     */
    private KpiDetailYjOverviewVo yjHisDataHandle(String monthStr, List<KpiDailyCountExtDto> monthData, Integer userId){

        KpiDetailYjOverviewVo userData = new KpiDetailYjOverviewVo();
        AtomicInteger index = new AtomicInteger(1);

        KpiDailyCountExtDto kpi = monthData.stream()
                .map(d -> {d.setSort(index.getAndIncrement());return d;})
                .filter(k -> k.getUserId().equals(userId))
                .findFirst().orElse(new NullKpiDailyCountExtDto());

        userData.setMonthStr(monthStr);
        userData.setProgress(kpi.getKpiAmountProgress());
        userData.setMonthTarget(kpiLoadingCache.getHisTargetByUser(userId,monthStr));
        userData.setYjAmount(kpi.getKpiAmount());
        userData.setGroupAverageProgress(new BigDecimal(monthData.stream()
                .map(KpiDailyCountDo::getKpiAmountProgress)
                .collect(Collectors.averagingDouble(BigDecimal::doubleValue))));
        userData.setScore(kpi.getKpiAmountScore());
        userData.setSort(kpi.getSort());

        return userData;
    }

    private KpiDetailBdKhOverviewVo bdkhHisDataHandle(String monthStr, List<KpiDailyCountExtDto> monthData, Integer userId){

        KpiDetailBdKhOverviewVo userData = new KpiDetailBdKhOverviewVo();
        AtomicInteger index = new AtomicInteger(1);

        KpiDailyCountExtDto kpi = monthData.stream()
                .filter(k -> k.getUserId().equals(userId))
                .map(d -> {d.setSort(index.getAndIncrement());return d;})
                .findFirst().orElse(new NullKpiDailyCountExtDto());
        userData.setMonthStr(monthStr);
        userData.setCoBdCustomerNum(kpi.getBdNewCustomerNum());
        userData.setLostBdCustomerNum(kpi.getLostCustomerNum());
        userData.setSort(kpi.getSort());
        return userData;
    }

    private KpiDetailXjOverviewVo xjHisDataHandle(String monthStr, List<KpiDailyCountExtDto> monthData, Integer userId) {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        Date firstDay = null;
        try {
            firstDay = sdf.parse(monthStr);
        } catch (ParseException e) {
            log.error("转换日期字符串出现异常 ",e);
            return null;
        }

        KpiDetailXjOverviewVo userData = new KpiDetailXjOverviewVo();
        AtomicInteger index = new AtomicInteger(1);
        KpiDailyCountExtDto kpi = monthData.stream()
                .map(k -> {k.setSort(index.getAndIncrement());return k;})
                .filter(k -> k.getUserId().equals(userId))
                .findFirst().orElse(new NullKpiDailyCountExtDto());

        userData.setMonthStr(monthStr);
        userData.setTransProgress(kpi.getChanceTransProportion());
        userData.setSuccessChance(kpi.getChanceSuccessNum());
        userData.setScore(Optional.ofNullable(kpi.getChanceTransScore()).orElse(BigDecimal.ZERO));
        userData.setSort(kpi.getSort());
//        KpiDataQueryDto query = new KpiDataQueryDto();
//        query.setUserId(userId);
//        query.setKpiDateStart(KpiUtils.getMonthStart(firstDay));
//        query.setKpiDateEnd(KpiUtils.getMonthEnd(firstDay));
//        userData.setMonthReceiveNum(kpiChanceService.getUserBussinessChancesNum(query));
        userData.setRangeCustomerNum(Optional.ofNullable(kpi.getChanceRangeCustomerNum()).orElse(0));

        BigDecimal aveTransProgress = new BigDecimal(monthData.stream().map(KpiDailyCountExtDto::getChanceTransProportion)
                .collect(Collectors.averagingDouble(BigDecimal::doubleValue)));
        userData.setGroupTransProgress(aveTransProgress);

        return userData;
    }

    /**
     * @description: 个人详情页业绩展示vo类转换.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/6/21 9:08 下午.
     * @author: Tomcat.Hui.
     * @param log: .
     * @return: com.vedeng.kpi.model.VO.KpiLogDetailVo.
     * @throws: .
     */
    private KpiLogDetailVo getKpiLogVo(KpiDataQueryDto log){
        KpiLogDetailVo result = new KpiLogDetailVo();
        result.setTraderName(log.getTraderName());
        result.setTraderId(log.getTraderId());
        result.setOrderNo(log.getOrderNo());
        result.setOrderId(log.getOrderId());
        result.setOrderType(log.getOrderType());
        result.setUserId(log.getUserId());
        result.setAfterSaleId(log.getAfterSaleId());
        result.setAfterSaleNo(log.getAfterSaleNo());
        result.setAmount(log.getAmount());
        result.setKpiAmount(log.getKpiAmount());
        result.setOrderRealAmount(log.getOrderRealAmount());
        result.setOrderPaidAmount(log.getOrderPaidAmount());
        result.setOrderSumAmount(log.getOrderSumAmount());
        result.setOperation(log.getOperation());
        result.setKpiDate(log.getKpiDate());
        result.setTraderName(log.getTraderName());
        result.setChanceId(log.getChanceId());
        result.setChanceNo(log.getChanceNo());
        result.setChanceTransFlag(log.getChanceTransFlag());
        result.setCustomersNums(log.getCustNum());
        result.setAssignTime(log.getAssignTime());
        if (null != log.getValidTime()) {
            result.setValidDate(new Date(log.getValidTime()));
        }
        if (null != log.getShValidTime()) {
            result.setShValidDate(new Date(log.getShValidTime()));
        }
        return result;
    }

    private List<KpiLogDetailVo> getLastTimes(List<KpiLogDetailVo> vos){
        KpiDataQueryDto query = new KpiDataQueryDto();
        List<Integer> traderIds = vos.stream().map(KpiLogDetailVo::getTraderId).collect(Collectors.toList());
        if (null != traderIds && traderIds.size() != 0) {
            query.setTraderIds(traderIds);
            List<KpiDataQueryDto> times = kpiCustomerService.getTraderLastTimes(query);
            return vos.stream().map(v -> {
                v.setLastOrderTime(times.stream()
                        .filter(t -> t.getTraderId().equals(v.getTraderId()))
                        .findFirst()
                        .map(KpiDataQueryDto::getLastOrderTime)
                        .map(t -> null == t ? null : new Date(t))
                        .orElse(null));
                v.setLastChatTime(times.stream()
                        .filter(t -> t.getTraderId().equals(v.getTraderId()))
                        .findFirst()
                        .map(KpiDataQueryDto::getLastChatTime)
                        .map(t -> null == t ? null : new Date(t))
                        .orElse(null));
                v.setChatApartDays(times.stream()
                        .filter(t -> t.getTraderId().equals(v.getTraderId()))
                        .findFirst()
                        .map(KpiDataQueryDto::getChatApartDays)
                        .orElse(null));
                return v;
            }).collect(Collectors.toList());
        } else {
            return Lists.newArrayList();
        }
    }


    private KpiDetailYjOverviewVo getKpiYjOverviewVo(KpiDailyCountExtDto kpi) {
        KpiDetailYjOverviewVo result = new KpiDetailYjOverviewVo();
        result.setUserId(kpi.getUserId());
        result.setUserName(null == kpi.getUserId() ? "" : kpiLoadingCache.getUserConfig(kpi.getUserId()).getUserName());
        result.setYjAmount(kpi.getKpiAmount());
        result.setProgress(kpi.getKpiAmountProgress());
        result.setScore(kpi.getKpiAmountScore());
        result.setSort(kpi.getSort());
        return result;
    }

    private KpiDetailKhOverviewVo getKpiKhOverviewVo(KpiDailyCountExtDto kpi) {
        KpiDetailKhOverviewVo result = new KpiDetailKhOverviewVo();
        result.setUserName(null == kpi.getUserId() ? "" : kpiLoadingCache.getUserConfig(kpi.getUserId()).getUserName());
        result.setCoCustomerNum(kpi.getCoCustomerNum());
        result.setLostCustomerNum(kpi.getLostCustomerNum());
        result.setScore(kpi.getCustomerScore());
        result.setSort(kpi.getSort());
        return result;
    }

    private KpiDetailBdKhOverviewVo getKpiBdKhOverviewVo(KpiDailyCountExtDto kpi) {
        KpiDetailBdKhOverviewVo result = new KpiDetailBdKhOverviewVo();
        result.setUserName(null == kpi.getUserId() ? "" : kpiLoadingCache.getUserConfig(kpi.getUserId()).getUserName());
        result.setCoBdCustomerNum(kpi.getBdNewCustomerNum());
        result.setLostBdCustomerNum(kpi.getBdLostCustomerNum());
        result.setScore(kpi.getBdCustomerScore());
        result.setSort(kpi.getSort());
        return result;
    }

    private KpiDetailXjOverviewVo getKpiXjOverviewVo(KpiDailyCountExtDto kpi,Date start,Date end) {
        KpiDetailXjOverviewVo result = new KpiDetailXjOverviewVo();
        result.setUserName(null == kpi.getUserId() ? "" : kpiLoadingCache.getUserConfig(kpi.getUserId()).getUserName());
        result.setSuccessChance(kpi.getChanceSuccessNum());
        result.setFailChance(kpi.getChanceFailNum());
        result.setTransProgress(kpi.getChanceTransProportion());
        result.setRangeCustomerNum(kpi.getChanceRangeCustomerNum());
        result.setScore(kpi.getChanceTransScore());
        result.setSort(kpi.getSort());
//        result.setMonthReceiveNum(getMonthChanceNum(kpi.getUserId(),start,end));
        return result;
    }

    private Integer getMonthChanceNum(Integer userId,Date start,Date end){
        KpiDataQueryDto query = new KpiDataQueryDto();
        query.setUserId(userId);
        query.setKpiDateStart(start);
        query.setKpiDateEnd(end);
        return kpiChanceService.getUserBussinessChancesNum(query);
    }
}
