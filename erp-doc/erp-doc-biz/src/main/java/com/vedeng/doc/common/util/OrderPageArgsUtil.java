package com.vedeng.doc.common.util;

/**
 * <AUTHOR>
 * @create 2022/1/11 13:01
 */

import cn.hutool.core.collection.CollUtil;
import com.vedeng.doc.domain.entity.DocBuzTag;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * xx信息页面参数排序
 *
 * <AUTHOR>
 */
public class OrderPageArgsUtil {

    private static Map<String, Integer> pageArgsOrderSkuInfo = PageArgsOrder.SkuInfo.getOrderSkuPageArgs();
    private static Map<String, Integer> pageArgsOrderSupplierInfo = PageArgsOrder.SupplierInfo.getOrderSupplierPageArgs();

    /**
     * 商品顺序比较器
     */
    private static Comparator<? super DocBuzTag> skuComparator = (before, after) -> {

        if (pageArgsOrderSkuInfo.get(after.getBuzTagName()) == null) {
            return -1;
        }
        if (pageArgsOrderSkuInfo.get(before.getBuzTagName()) == null) {
            return 1;
        }
        return pageArgsOrderSkuInfo.get(before.getBuzTagName()) - pageArgsOrderSkuInfo.get(after.getBuzTagName());
    };

    /**
     * 厂商顺序比较器
     */
    private static Comparator<? super DocBuzTag> supplierComparator = (before, after) -> {

        if (pageArgsOrderSupplierInfo.get(after.getBuzTagName()) == null) {
            return -1;
        }
        if (pageArgsOrderSupplierInfo.get(before.getBuzTagName()) == null) {
            return 1;
        }
        return pageArgsOrderSupplierInfo.get(before.getBuzTagName()) - pageArgsOrderSupplierInfo.get(after.getBuzTagName());
    };

    /**
     * @param docBuzTagDos     排序集合
     * @param sku2SupplierFlag 标识当前是商品排序还是厂商排序
     *                         1   -   商品
     *                         2   -   厂商
     * @param comparator       自定义比较器
     */
    public static void sort(List<? extends DocBuzTag> docBuzTagDos, Integer sku2SupplierFlag, Comparator<? super DocBuzTag> comparator) {

        if (comparator != null) {
            sort(docBuzTagDos, comparator);
        }

        if (sku2SupplierFlag == null) {
            sort(docBuzTagDos);
        }

        if (Integer.valueOf(1).equals(sku2SupplierFlag)) {
            sort(docBuzTagDos, skuComparator);
        }

        if (Integer.valueOf(2).equals(sku2SupplierFlag)) {
            sort(docBuzTagDos, supplierComparator);
        }

    }

    /**
     * VDERP-8397 【资料库】商品资料、厂商资质维护参数调序
     * 默认 商品资料
     *
     * @param docBuzTagDos 即排序集合
     */
    private static void sort(List<? extends DocBuzTag> docBuzTagDos) {

        if (CollUtil.isEmpty(docBuzTagDos)) {
            return;
        }
        docBuzTagDos.sort(skuComparator);
    }

    /**
     * 自定义顺序
     *
     * @param docBuzTagDos 即排序集合
     * @param comparator   自定义比较器
     */
    private static void sort(List<? extends DocBuzTag> docBuzTagDos, Comparator<? super DocBuzTag> comparator) {

        if (CollUtil.isEmpty(docBuzTagDos)) {
            return;
        }
        docBuzTagDos.sort(comparator);
    }

    /**
     * 页面参数展示顺序
     */
    public static class PageArgsOrder {

        /**
         * 商品资料
         */
        static class SkuInfo {

            private static Map<String, Integer> ORDER_SKU_PAGE_ARGS = new HashMap<>();

            static {

                ORDER_SKU_PAGE_ARGS.put("彩页", 1);
                ORDER_SKU_PAGE_ARGS.put("招标参数(技术参数)", 2);
                ORDER_SKU_PAGE_ARGS.put("技术参数（非招标参数）", 3);
                ORDER_SKU_PAGE_ARGS.put("配置清单", 4);
                ORDER_SKU_PAGE_ARGS.put("注册证", 5);
                ORDER_SKU_PAGE_ARGS.put("一类备案凭证", 6);
                ORDER_SKU_PAGE_ARGS.put("产品推荐书", 7);
                ORDER_SKU_PAGE_ARGS.put("用户名单", 8);
                ORDER_SKU_PAGE_ARGS.put("销售话术", 9);
                ORDER_SKU_PAGE_ARGS.put("使用说明书", 10);
                ORDER_SKU_PAGE_ARGS.put("中标通知书", 11);
                ORDER_SKU_PAGE_ARGS.put("检验报告", 12);
                ORDER_SKU_PAGE_ARGS.put("产品图片（主图）", 13);
                ORDER_SKU_PAGE_ARGS.put("产品实景图", 14);
                ORDER_SKU_PAGE_ARGS.put("产品效果图", 15);
                ORDER_SKU_PAGE_ARGS.put("选型手册", 16);
                ORDER_SKU_PAGE_ARGS.put("科室方案", 17);
                ORDER_SKU_PAGE_ARGS.put("产品PPT", 18);
                ORDER_SKU_PAGE_ARGS.put("宣传视频", 19);
                ORDER_SKU_PAGE_ARGS.put("装机视频", 20);
                ORDER_SKU_PAGE_ARGS.put("装机指导", 21);
                ORDER_SKU_PAGE_ARGS.put("产品快速操作卡/使用指南卡", 22);
                ORDER_SKU_PAGE_ARGS.put("收费编码", 23);
                ORDER_SKU_PAGE_ARGS.put("广审表", 24);
                ORDER_SKU_PAGE_ARGS.put("安全评估报告", 25);
                ORDER_SKU_PAGE_ARGS.put("CE认证", 26);
                ORDER_SKU_PAGE_ARGS.put("FDA认证", 27);
                ORDER_SKU_PAGE_ARGS.put("全套招标资料（盖章版）压缩包", 28);
                ORDER_SKU_PAGE_ARGS.put("产品注册标准/技术要求", 29);
                ORDER_SKU_PAGE_ARGS.put("打印报告模板", 30);
                ORDER_SKU_PAGE_ARGS.put("其他资料", 31);

            }

            static Map<String, Integer> getOrderSkuPageArgs() {
                return ORDER_SKU_PAGE_ARGS;
            }
        }

        /**
         * 厂商资质
         */
        static class SupplierInfo {

            private static Map<String, Integer> ORDER_SUPPLIER_PAGE_ARGS = new HashMap<>();

            static {

                ORDER_SUPPLIER_PAGE_ARGS.put("营业执照", 1);
                ORDER_SUPPLIER_PAGE_ARGS.put("生产许可", 2);
                ORDER_SUPPLIER_PAGE_ARGS.put("生产产品登记表", 3);
                ORDER_SUPPLIER_PAGE_ARGS.put("一类生产备案凭证", 4);
                ORDER_SUPPLIER_PAGE_ARGS.put("二类备案", 5);
                ORDER_SUPPLIER_PAGE_ARGS.put("经营许可", 6);
                ORDER_SUPPLIER_PAGE_ARGS.put("产品授权书", 7);
                ORDER_SUPPLIER_PAGE_ARGS.put("销售人员授权书", 8);
                ORDER_SUPPLIER_PAGE_ARGS.put("售后服务承诺书", 9);
                ORDER_SUPPLIER_PAGE_ARGS.put("商标注册证", 10);
                ORDER_SUPPLIER_PAGE_ARGS.put("高新企业认证", 11);
                ORDER_SUPPLIER_PAGE_ARGS.put("厂家其他荣誉", 12);
                ORDER_SUPPLIER_PAGE_ARGS.put("一般纳税人证明", 13);
                ORDER_SUPPLIER_PAGE_ARGS.put("税务登记证", 14);
                ORDER_SUPPLIER_PAGE_ARGS.put("组织机构代码", 15);
                ORDER_SUPPLIER_PAGE_ARGS.put("营业执照（盖贝登章）", 16);
                ORDER_SUPPLIER_PAGE_ARGS.put("注册证（盖贝登章）", 17);
                ORDER_SUPPLIER_PAGE_ARGS.put("生产许可证（盖贝登章）", 18);
            }

            static Map<String, Integer> getOrderSupplierPageArgs() {
                return ORDER_SUPPLIER_PAGE_ARGS;
            }

        }
    }
}