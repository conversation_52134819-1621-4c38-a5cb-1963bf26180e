package com.vedeng.erp.saleorder.enums;

import com.vedeng.common.core.exception.ServiceException;

/**
 * 售后单收款状态
 */
public enum AfterSalesCollectionStatusEnum {

    NULL(0, "无收款"),
    NOT(1, "未收款"),
    PART(2, "部分收款"),
    ALL(3, "全部收款"),
    ;
    private Integer code;

    private String desc;

    AfterSalesCollectionStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static String getDesc(Integer code){
        for(AfterSalesCollectionStatusEnum v : values()){
            if(v.getCode().equals(code)){
                return v.getDesc();
            }
        }
        return "";
    }
}
