package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.KingDeeInstallServiceRecordEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface KingDeeInstallServiceRecordMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(KingDeeInstallServiceRecordEntity record);

    int insertOrUpdate(KingDeeInstallServiceRecordEntity record);

    int insertOrUpdateSelective(KingDeeInstallServiceRecordEntity record);

    int insertSelective(KingDeeInstallServiceRecordEntity record);

    KingDeeInstallServiceRecordEntity selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(KingDeeInstallServiceRecordEntity record);

    int updateByPrimaryKey(KingDeeInstallServiceRecordEntity record);

    int updateBatch(List<KingDeeInstallServiceRecordEntity> list);

    int updateBatchSelective(List<KingDeeInstallServiceRecordEntity> list);

    int batchInsert(@Param("list") List<KingDeeInstallServiceRecordEntity> list);

    List<KingDeeInstallServiceRecordEntity> findByFBillNo(@Param("fBillNo")String fBillNo);


}