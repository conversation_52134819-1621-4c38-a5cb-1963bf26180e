package com.vedeng.erp.finance.mapper;

import com.vedeng.erp.finance.domain.entity.RPayApplyJBankReceiptEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface RPayApplyJBankReceiptMapper {
    /**
     * delete by primary key
     * @param rPayApplyJBankReceiptId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long rPayApplyJBankReceiptId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(RPayApplyJBankReceiptEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(RPayApplyJBankReceiptEntity record);

    /**
     * select by primary key
     * @param rPayApplyJBankReceiptId primary key
     * @return object by primary key
     */
    RPayApplyJBankReceiptEntity selectByPrimaryKey(Long rPayApplyJBankReceiptId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(RPayApplyJBankReceiptEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(RPayApplyJBankReceiptEntity record);

    int updateBatch(List<RPayApplyJBankReceiptEntity> list);

    int updateBatchSelective(List<RPayApplyJBankReceiptEntity> list);

    int batchInsert(@Param("list") List<RPayApplyJBankReceiptEntity> list);

    List<RPayApplyJBankReceiptEntity> findByPayApplyId(@Param("payApplyId") Integer payApplyId);
}
