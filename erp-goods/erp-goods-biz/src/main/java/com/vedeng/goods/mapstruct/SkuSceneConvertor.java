package com.vedeng.goods.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.goods.domain.entity.SkuSceneEntity;
import com.vedeng.goods.dto.SkuSceneDto;
import com.vedeng.goods.dto.VHostWordDTOExample;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;


/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface SkuSceneConvertor extends BaseMapStruct<SkuSceneEntity, SkuSceneDto> {
}
