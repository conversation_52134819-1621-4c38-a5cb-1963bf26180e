package com.vedeng.goods.command;

import com.vedeng.common.controller.BaseCommand;
import com.vedeng.common.model.FileInfo;
import com.vedeng.goods.manager.extension.GoodsValidResultVo;
import com.vedeng.goods.model.vo.*;
import lombok.Data;
import lombok.ToString;

import java.beans.Transient;
import java.util.Arrays;
import java.util.List;

@ToString
public class SkuAddCommand extends BaseCommand {
	private CoreSpuBaseVO coreSpuBaseVO;
	private Integer spuId;
	private Integer skuId;

	/**
	 * 检测报告
	 */
	private transient List<GoodsDetectReportAttachment> goodsDetectReportAttachment;


	public Integer[] getBaseAttributeValueId() {
		return baseAttributeValueId;
	}

	public void setBaseAttributeValueId(Integer[] baseAttributeValueId) {
		this.baseAttributeValueId = baseAttributeValueId;
	}

	private Integer[] baseAttributeValueId;


	public String getSkuInfo() {
		return skuInfo;
	}

	public void setSkuInfo(String skuInfo) {
		this.skuInfo = skuInfo;
	}

	private String skuInfo;//临时商品 ， model spec

	private String skuCheckFilesJson;// 检测回显

	private Long priceChangeApplyId;

	private List<FileInfo> skuCheck;//回显

	/**
	 * 养护类型
	 */
	private Integer curingType;
	private String curingTypeDesc;

	/**
	 * 技术参数
	 */
	private String[] paramsName1;

	private String[] paramsValue1;
	private String[] paramsValue2;
	private String[] paramsValue3;

	/**
	 * 签约模式
	 */
	private String goodsSignContractModeStr;


	/**
	 * 商品级别
	 *
	 * @since ERP_LV_2020_105
	 */
	private Integer goodsLevelNo;

	/**
	 * 商品档位
	 *
	 * @since  ERP_LV_2020_105
	 */
	private Integer goodsPositionNo;

	private GoodsLevelVo goodsLevelVo;

	private GoodsPositionVo goodsPositionVo;


	public String getSkuIds() {
		return skuIds;
	}

	public void setSkuIds(String skuIds) {
		this.skuIds = skuIds;
	}

	private String skuIds;//批量设置备货

	public String getHasBackupMachine() {
		return hasBackupMachine;
	}

	public void setHasBackupMachine(String hasBackupMachine) {
		this.hasBackupMachine = hasBackupMachine;
	}

	private String hasBackupMachine;//批量设置备货



	//一些数字的验证回显
	private String changeNumStr;
	private String effectiveDaysStr;
	private String goodsHeightStr;
	private String goodsLengthStr;
	private String goodsWidthStr;
	private String grossWeightStr;
	private String minOrderStr;
	private String netWeightStr;
	private String packageHeightStr;
	private String packageLengthStr;
	private String packageWidthStr;
	private String qaOutPriceStr;
	private String qaResponseTimeStr;
	private String supplierExtendsGuaranteePriceStr;

	private String showType;//显示类型（区分显示字段）


	public String getShowType() {
		return showType;
	}

	public void setShowType(String showType) {
		this.showType = showType;
	}

	public String getChangeNumStr() {
		return changeNumStr;
	}

	public void setChangeNumStr(String changeNumStr) {
		this.changeNumStr = changeNumStr;
	}

	public String getEffectiveDaysStr() {
		return effectiveDaysStr;
	}

	public void setEffectiveDaysStr(String effectiveDaysStr) {
		this.effectiveDaysStr = effectiveDaysStr;
	}

	public String getGoodsHeightStr() {
		return goodsHeightStr;
	}

	public void setGoodsHeightStr(String goodsHeightStr) {
		this.goodsHeightStr = goodsHeightStr;
	}

	public String getGoodsLengthStr() {
		return goodsLengthStr;
	}

	public void setGoodsLengthStr(String goodsLengthStr) {
		this.goodsLengthStr = goodsLengthStr;
	}

	public String getGoodsWidthStr() {
		return goodsWidthStr;
	}

	public void setGoodsWidthStr(String goodsWidthStr) {
		this.goodsWidthStr = goodsWidthStr;
	}

	public String getGrossWeightStr() {
		return grossWeightStr;
	}

	public void setGrossWeightStr(String grossWeightStr) {
		this.grossWeightStr = grossWeightStr;
	}

	public String getMinOrderStr() {
		return minOrderStr;
	}

	public void setMinOrderStr(String minOrderStr) {
		this.minOrderStr = minOrderStr;
	}

	public String getNetWeightStr() {
		return netWeightStr;
	}

	public void setNetWeightStr(String netWeightStr) {
		this.netWeightStr = netWeightStr;
	}

	public String getPackageHeightStr() {
		return packageHeightStr;
	}

	public void setPackageHeightStr(String packageHeightStr) {
		this.packageHeightStr = packageHeightStr;
	}

	public String getPackageLengthStr() {
		return packageLengthStr;
	}

	public void setPackageLengthStr(String packageLengthStr) {
		this.packageLengthStr = packageLengthStr;
	}

	public String getPackageWidthStr() {
		return packageWidthStr;
	}

	public void setPackageWidthStr(String packageWidthStr) {
		this.packageWidthStr = packageWidthStr;
	}

	public String getQaOutPriceStr() {
		return qaOutPriceStr;
	}

	public void setQaOutPriceStr(String qaOutPriceStr) {
		this.qaOutPriceStr = qaOutPriceStr;
	}

	public String getQaResponseTimeStr() {
		return qaResponseTimeStr;
	}

	public void setQaResponseTimeStr(String qaResponseTimeStr) {
		this.qaResponseTimeStr = qaResponseTimeStr;
	}

	public String getSupplierExtendsGuaranteePriceStr() {
		return supplierExtendsGuaranteePriceStr;
	}

	public void setSupplierExtendsGuaranteePriceStr(String supplierExtendsGuaranteePriceStr) {
		this.supplierExtendsGuaranteePriceStr = supplierExtendsGuaranteePriceStr;
	}

	public String[] getParamsName1() {
		return paramsName1;
	}

	public void setParamsName1(String[] paramsName1) {
		this.paramsName1 = paramsName1;
	}


	public String[] getParamsValue1() {
		return paramsValue1;
	}

	public void setParamsValue1(String[] paramsValue1) {
		this.paramsValue1 = paramsValue1;
	}


	public List<FileInfo> getSkuCheck() {
		return skuCheck;
	}

	public void setSkuCheck(List<FileInfo> skuCheck) {
		this.skuCheck = skuCheck;
	}


	private Integer operateInfoId;

	public Integer getCheckStatus() {
		return checkStatus;
	}

	public void setCheckStatus(Integer checkStatus) {
		this.checkStatus = checkStatus;
	}

	private Integer checkStatus;

	public String getLastCheckReason() {
		return lastCheckReason;
	}

	public void setLastCheckReason(String lastCheckReason) {
		this.lastCheckReason = lastCheckReason;
	}

	private String lastCheckReason;


	private Integer skuType;//1器械  2 耗材

	public String getTips() {
		return tips;
	}

	public void setTips(String tips) {
		this.tips = tips;
	}

	private String tips;


	public Integer getSkuType() {
		return skuType;
	}

	public void setSkuType(Integer skuType) {
		this.skuType = skuType;
	}

	public Integer getOperateInfoId() {
		return operateInfoId;
	}

	public void setOperateInfoId(Integer operateInfoId) {
		this.operateInfoId = operateInfoId;
	}

	public Integer getSpuId() {
		return spuId;
	}

	public void setSpuId(Integer spuId) {
		this.spuId = spuId;
	}



	public CoreSpuBaseVO getCoreSpuBaseVO() {
		return coreSpuBaseVO;
	}

	public void setCoreSpuBaseVO(CoreSpuBaseVO coreSpuBaseVO) {
		this.coreSpuBaseVO = coreSpuBaseVO;
	}


	public Integer getSkuId() {
		return skuId;
	}

	public void setSkuId(Integer skuId) {
		this.skuId = skuId;
	}

	public String getSkuCheckFilesJson() {
		return skuCheckFilesJson;
	}

	public void setSkuCheckFilesJson(String skuCheckFilesJson) {
		this.skuCheckFilesJson = skuCheckFilesJson;
	}


	public Long getPriceChangeApplyId() {
		return priceChangeApplyId;
	}

	public void setPriceChangeApplyId(Long priceChangeApplyId) {
		this.priceChangeApplyId = priceChangeApplyId;
	}

	public Integer getCuringType() {
		return curingType;
	}

	public void setCuringType(Integer curingType) {
		this.curingType = curingType;
	}

	public String getCuringTypeDesc() {
		return curingTypeDesc;
	}

	public void setCuringTypeDesc(String curingTypeDesc) {
		this.curingTypeDesc = curingTypeDesc;
	}

	public String getGoodsSignContractModeStr() {
		return goodsSignContractModeStr;
	}

	public void setGoodsSignContractModeStr(String goodsSignContractModeStr) {
		this.goodsSignContractModeStr = goodsSignContractModeStr;
	}

	public Integer getGoodsLevelNo() {
		return goodsLevelNo;
	}

	public void setGoodsLevelNo(Integer goodsLevelNo) {
		this.goodsLevelNo = goodsLevelNo;
	}

	public Integer getGoodsPositionNo() {
		return goodsPositionNo;
	}

	public void setGoodsPositionNo(Integer goodsPositionNo) {
		this.goodsPositionNo = goodsPositionNo;
	}

	public GoodsLevelVo getGoodsLevelVo() {
		return goodsLevelVo;
	}

	public void setGoodsLevelVo(GoodsLevelVo goodsLevelVo) {
		this.goodsLevelVo = goodsLevelVo;
	}

	public GoodsPositionVo getGoodsPositionVo() {
		return goodsPositionVo;
	}

	public void setGoodsPositionVo(GoodsPositionVo goodsPositionVo) {
		this.goodsPositionVo = goodsPositionVo;
	}


	public List<GoodsDetectReportAttachment> getGoodsDetectReportAttachment() {
		return goodsDetectReportAttachment;
	}

	public void setGoodsDetectReportAttachment(List<GoodsDetectReportAttachment> goodsDetectReportAttachment) {
		this.goodsDetectReportAttachment = goodsDetectReportAttachment;
	}

	@Data
	public static class GoodsDetectReportAttachment {

		/**
		 * 相对路径
		 */
		private String relativePath;

		/**
		 * 显示名称
		 */
		private String displayName;
	}

	/**
	 *  配置名称
	 */
	private String[] configurationName;

	public String[] getConfigurationName() {
		return configurationName;
	}

	public void setConfigurationName(String[] configurationName) {
		this.configurationName = configurationName;
	}

	/**
	 * 配置数量
	 */
	private String[] configurationQuantity;

	public String[] getConfigurationQuantity() {
		return configurationQuantity;
	}

	public void setConfigurationQuantity(String[] configurationQuantity) {
		this.configurationQuantity = configurationQuantity;
	}

	private String pushedOrgIdList;

	public String getPushedOrgIdList() {
		return pushedOrgIdList;
	}

	public void setPushedOrgIdList(String pushedOrgIdList) {
		this.pushedOrgIdList = pushedOrgIdList;
	}
}
