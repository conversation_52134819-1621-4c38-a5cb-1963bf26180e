package com.vedeng.goods.dao;

import com.vedeng.goods.api.dto.SysOptionDefinition;
import com.vedeng.goods.model.dto.SkuAuthrizationItemDto;
import com.vedeng.goods.model.entity.SkuAuthorizationRegion;
import com.vedeng.goods.model.entity.SkuAuthorizationTerminalType;
import org.apache.ibatis.annotations.Param;
import org.springframework.security.access.method.P;

import javax.inject.Named;
import java.util.List;

/**
 * SKU报备信息Mapper
 *
 * <AUTHOR>
 * @date 2020/9/17
 */
@Named("skuAuthorizationMapper")
public interface SkuAuthorizationMapper {
    /**
     * 获取所有的终端类型
     *
     * @return
     */
    List<SysOptionDefinition> getAllTerminalTypes();

    /**
     * 通过SKU获取SKU的报备明细信息
     *
     * @param skuId
     * @return
     */
    List<SkuAuthrizationItemDto> getSkuAuthrizationItemBySkuId(Integer skuId);

    /**
     * 保存报备信息的地区信息
     *
     * @param skuAuthrizationRegion
     * @return
     */
    int insertSkuAuthrizationRegion(SkuAuthorizationRegion skuAuthrizationRegion);

    /**
     * 删除报备信息的地区信息
     *
     * @param regionIds
     * @param snowFlakeId
     * @param userId
     * @return
     */
    int deleteSkuAuthrizationRegionByCondition(@Param("regionIds") List<Integer> regionIds,
                                               @Param("snowFlakeId") Long snowFlakeId,
                                               @Param("userId") Integer userId);

    /**
     * 通过雪花ID关键Item删除地区信息
     *
     * @param snowFlakeIds
     * @param userId
     * @return
     */
    int disabledSkuAuthrizationRegionBySnowFlakeIds(@Param("snowFlakeIds") List<Long> snowFlakeIds,
                                                    @Param("userId") Integer userId);

    /**
     * 保存报备信息的类型信息
     *
     * @param skuAuthorizationTerminalType
     * @return
     */
    int insertSkuAuthrizationTerminalType(SkuAuthorizationTerminalType skuAuthorizationTerminalType);

    /**
     * 删除地区信息的类型信息
     *
     * @param terminaltypeIds
     * @param snowFlakeId
     * @param userId
     * @return
     */
    int deleteSkuAuthrizationTerminalTypeByCondition(@Param("terminaltypeIds") List<Integer> terminaltypeIds,
                                                     @Param("snowFlakeId") Long snowFlakeId,
                                                     @Param("userId") Integer userId);

    /**
     * 通过雪花ID关联信息删除报备类型信息
     *
     * @param snowFlakeIds
     * @param userId
     * @return
     */
    int disabledSkuAuthrizationTerminalTypeBySnowFlakeIds(@Param("snowFlakeIds") List<Long> snowFlakeIds,
                                                          @Param("userId") Integer userId);
}