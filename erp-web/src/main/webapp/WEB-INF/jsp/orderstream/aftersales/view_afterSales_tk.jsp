<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="售后详情-退款" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<script type="text/javascript"
        src='<%= basePath %>static/js/orderstream/aftersales/view_afterSales.js?rnd=${resourceVersionKey}'></script>
<script src="<%= basePath %>static/js/orderstream/jquery.js"></script>
<script src="${pageContext.request.contextPath}/static/js/orderstream/openfile.js?rnd=${resourceVersionKey}"></script>
<script src="<%= basePath %>static/js/orderstream/afterSales_index.js?rnd=${resourceVersionKey}"></script>
<link rel="stylesheet" href="<%= basePath %>static/css/orderstream/style.css">
<link rel="stylesheet" href="<%= basePath %>static/css/orderstream/aftersales-detail.css">
<script type="text/javascript">
    $(function () {
        var url = page_url + '/order/aftersalesUpgrade/viewAfterSalesDetail.do?afterSalesId=' + $("#afterSalesId").val();
        if ($(window.frameElement).attr('src').indexOf("viewAfterSalesDetail") < 0) {
            $(window.frameElement).attr('data-url', url);
        }
    });
</script>
<div class="main-container">
    <div class="t-line-wrap J-line-wrap"
         data-json='[
     {"label":"确认","status":${topStatusList.get(0)}},
     {"label":"退款","status":${topStatusList.get(1)}},
     {"label":"${lastLabelName}","status":${topStatusList.get(2)}}]'>
    </div>

    <div class="table-buttons">
        <form action="" method="post" id="myform">
            <input type="hidden" name="afterSalesId" id="afterSalesId" value="${afterSalesVo.afterSalesId}"/>
            <input type="hidden" name="orderId" value="${afterSalesVo.orderId}"/>
            <input type="hidden" name="subjectType" value="${afterSalesVo.subjectType}"/>
            <input type="hidden" name="type" value="${afterSalesVo.type}"/>
            <input type="hidden" name="formToken" value="${formToken}"/>
            <input type="hidden" name="taskId" value="${taskInfo.id == null ?0: taskInfo.id}"/>

            <%--
             展示条件  1.审核通过；2.非已关闭  非已完结订单；3.非已退款，非全部退款；--%>
            <c:choose>
                <c:when test="${afterSalesVo.status eq 2 && afterSalesVo.atferSalesStatus eq 1 &&
                (user.positType eq 310 || user.positType eq 312 || user.positType eq 589) &&
                ((null==taskInfoOver and null==taskInfoOver.getProcessInstanceId())or (null!=taskInfoOver and taskInfoOver.assignee==null and empty candidateUserMapOver[taskInfoOver.id])) &&
                afterSalesVo.isCalRefund ne 1}">
                    <button type="button" class="bt-bg-style bg-light-blue bt-small" onclick="executeRefundOperation()">
                        执行退款运算
                    </button>
                </c:when>
                <c:otherwise>
                    <button type="button" class="bt-bg-style bg-light-grey bt-small">执行退款运算</button>
                </c:otherwise>
            </c:choose>

            <%--      售后单在待确认状态，申请人与售后处理人可点击，否则置灰--%>
            <c:choose>
                <c:when test="${afterSalesVo.atferSalesStatus eq 0 && afterSalesVo.status ne 1 &&
                     (user.positType eq 310 || user.positType eq 312 || user.positType eq 589)}">
                    <button type="button" class="bt-bg-style bg-light-blue bt-small" onclick="editAfterSales(1);">
                        编辑订单
                    </button>
                </c:when>

                <c:otherwise>
                    <button type="button" class="bt-bg-style bg-light-grey bt-small">
                        编辑订单
                    </button>
                </c:otherwise>
            </c:choose>

            <%--售后单在待确认状态，售后处理人可点击，否则置灰--%>
            <c:choose>
                <c:when test="${afterSalesVo.atferSalesStatus eq 0 && afterSalesVo.status ne 1 &&
                     (user.positType eq 310 || user.positType eq 312 || user.positType eq 589)}">
                    <button type="button" class="bt-bg-style bg-light-blue bt-small" onclick="applyAudit();">
                        提交审核
                    </button>
                </c:when>
                <c:otherwise>
                    <button type="button" class="bt-bg-style bg-light-grey bt-small">
                        提交审核
                    </button>
                </c:otherwise>
            </c:choose>

            <%--   售后单入库状态为“无入库/全部入库”、退票状态为“无退票/全部退票”退款状态为“无退款/全部退款”开票状态为“全部开票/无开票”，
               且售后单未在完结审核流程中，此时售后处理人可点击，否则置灰--%>
            <c:choose>
                <c:when test="${(afterSalesVo.amountRefundStatus eq 0 || afterSalesVo.amountRefundStatus eq 3) &&
                (user.positType eq 310 || user.positType eq 312 || user.positType eq 589) &&
                afterSalesVo.atferSalesStatus eq 1 &&
                ((null==taskInfoOver and null==taskInfoOver.getProcessInstanceId())or (null!=taskInfoOver and taskInfoOver.assignee==null and empty candidateUserMapOver[taskInfoOver.id]))}">
                    <button type="button" class="bt-bg-style bg-light-blue bt-small pop-new-data"
                            layerParams='{"width":"780px","height":"310px","title":"选择售后原因","link":"./saleorderComplete.do?afterSalesId=${afterSalesVo.afterSalesId}&afterSalesType=760&type=${afterSalesVo.type}&orderId=${afterSalesVo.orderId }&subjectType=${afterSalesVo.subjectType }&formToken=${formToken }&traderId=${afterSalesVo.traderId }"}'>
                        申请完结
                    </button>
                </c:when>
                <c:otherwise>
                    <button type="button" class="bt-bg-style bg-light-grey bt-small">申请完结</button>
                </c:otherwise>
            </c:choose>

            <c:choose>
                <c:when test="${(afterSalesVo.status ne 1 && afterSalesVo.closeStatus eq 1 && afterSalesVo.atferSalesStatus ne 3 && afterSalesVo.atferSalesStatus ne 2 &&
                   (user.positType eq 310 || user.positType eq 312 || user.positType eq 589 || (afterSalesVo.atferSalesStatus eq 0 && user.userId eq afterSalesVo.creator)) && empty payPlayList && afterSalesVo.amountRefundStatus == null &&
                     ((null==taskInfoOver and null==taskInfoOver.getProcessInstanceId())
                    or (null!=taskInfoOver and taskInfoOver.assignee==null and empty candidateUserMapOver[taskInfoOver.id])))}">
                    <button type="button" class="bt-bg-style bg-light-blue bt-small" onclick="colse();">
                        关闭订单
                    </button>
                </c:when>
                <c:otherwise>
                    <button type="button" class="bt-bg-style bg-light-grey bt-small">
                        关闭订单
                    </button>
                </c:otherwise>
            </c:choose>

            <c:choose>
                <c:when test="${isSupply ne 1 && ((null!=taskInfo and null!=taskInfo.getProcessInstanceId() and null!=taskInfo.assignee) or !empty candidateUserMap[taskInfo.id]) &&
                            (taskInfo.assignee == user.username or candidateUserMap['belong'])}">
                    <%--售后单在待确认、审核中状态下，审核节点对应审核账号可点击，否则置灰--%>
                    <button type="button" class="bt-bg-style bg-light-blue bt-small mr10 pop-new-data"
                            layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./complement.do?afterSalesId=${afterSalesVo.afterSalesId}&taskId=${taskInfo.id}&pass=true&type=2"}'>
                        审核通过
                    </button>
                    <button type="button" class="bt-bg-style bg-light-blue bt-small mr10 pop-new-data"
                            layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./complement.do?afterSalesId=${afterSalesVo.afterSalesId}&taskId=${taskInfo.id}&pass=false&type=2"}'>
                        审核不通过
                    </button>
                </c:when>
                <c:otherwise>
                    <button type="button" class="bt-bg-style bg-light-grey bt-small mr10">
                        审核通过
                    </button>
                    <button type="button" class="bt-bg-style bg-light-grey bt-small mr10">
                        审核不通过
                    </button>
                </c:otherwise>
            </c:choose>

            <c:choose>
                <c:when test="${((null!=taskInfoOver and null!=taskInfoOver.getProcessInstanceId() and null!=taskInfoOver.assignee) or !empty candidateUserMapOver[taskInfoOver.id])
                    && (taskInfoOver.assignee == user.username or candidateUserMapOver['belong'])}">
                    <button type="button" class="bt-bg-style bg-light-blue bt-small mr10 pop-new-data"
                            layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./complement.do?taskId=${taskInfoOver.id}&pass=true&type=2&orderId=${afterSalesVo.orderId}&afterSalesId=${afterSalesVo.afterSalesId}"}'>
                        完结/关闭审核通过
                    </button>
                    <button type="button" class="bt-bg-style bg-light-blue bt-small mr10 pop-new-data"
                            layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./complement.do?afterSalesId=${afterSalesVo.afterSalesId}&taskId=${taskInfoOver.id}&pass=false&type=2"}'>
                        完结/关闭审核不通过
                    </button>
                </c:when>
                <c:otherwise>
                    <button type="button" class="bt-bg-style bg-light-grey bt-small">
                        完结/关闭审核通过
                    </button>
                    <button type="button" class="bt-bg-style bg-light-grey bt-small">
                        完结/关闭审核不通过
                    </button>
                </c:otherwise>
            </c:choose>
        </form>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                基本信息
            </div>
        </div>
        <table class="table">
            <tbody>
            <tr>
                <td class="wid20">订单号</td>
                <td>${afterSalesVo.afterSalesNo}</td>
                <td class="wid20">售后类型</td>
                <td>${afterSalesVo.typeName}</td>
            </tr>

            <tr>
                <td class="wid20">订单状态</td>
                <td>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 0}">待确认</c:if>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 1}">进行中</c:if>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 2}">已完结</c:if>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 3}">已关闭</c:if>
                </td>

                <td>审核状态</td>
                <td>
                    <c:if test="${afterSalesVo.status eq 0}">待审核</c:if>
                    <c:if test="${afterSalesVo.status eq 1}">审核中</c:if>
                    <c:if test="${afterSalesVo.status eq 2}">审核通过</c:if>
                    <c:if test="${afterSalesVo.status eq 3}">审核不通过</c:if>
                </td>
            </tr>


            <tr>
                <td>生效状态</td>
                <td>
                    <c:if test="${afterSalesVo.validStatus eq 0}">未生效</c:if>
                    <c:if test="${afterSalesVo.validStatus eq 1}">已生效</c:if>
                </td>

                <td>售后处理人</td>
                <td>${afterSalesVo.serviceUserName}</td>
            </tr>


            <tr>
                <td>申请人</td>
                <td>${afterSalesVo.creatorName}</td>

                <td>申请时间</td>
                <td><date:date value="${afterSalesVo.addTime}"/></td>
            </tr>

            <tr>
                <td>生效时间</td>
                <td><date:date value="${afterSalesVo.validTime}"/></td>

                <td>完结时间</td>
                <td><c:if test="${afterSalesVo.atferSalesStatus == 2}"><date:date
                        value="${afterSalesVo.modTime}"/></c:if></td>
            </tr>

            <tr>
                <td>完结/关闭原因</td>
                <td>${afterSalesVo.afterSalesStatusResonName}</td>
                <td>完结/关闭人员</td>
                <td>${afterSalesVo.afterSalesStatusUserName}</td>
            </tr>

            <tr>
                <td>完结/关闭备注</td>
                <td colspan="3" class="text-left">${afterSalesVo.afterSalesStatusComments}</td>
            </tr>
            </tbody>
        </table>
    </div>


    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                售后信息
            </div>
            <c:if test="${(afterSalesVo.atferSalesStatus eq 0 || afterSalesVo.atferSalesStatus eq 1) && afterSalesVo.status ne 1 && user.positType eq 312}">
                <div class="title-click  pop-new-data"
                     layerParams='{"width":"700px","height":"480px","title":"编辑售后信息","link":"/order/afterSalesCommon/editAfterSalesInfo.do?afterSalesId=${afterSalesVo.afterSalesId}&&type=${afterSalesVo.type}"}'>
                    编辑售后信息
                </div>
            </c:if>
        </div>
        <table class="table">
            <tbody>
            <tr>
                <td class="wid20">售后原因</td>
                <td>${afterSalesVo.reasonName}</td>
                <td class="wid20">第一责任部门</td>
                <td>${afterSalesVo.firstResponsibleDepartmentStr}</td>
            </tr>

            <tr>
                <td class="wid20">售后联系人</td>
                <td>${afterSalesVo.afterConnectUserName}</td>

                <td>售后联系人电话</td>
                <td>
                    ${afterSalesVo.afterConnectPhone}
                    <c:if test="${not empty afterSalesVo.afterConnectPhone}">
                        <i class="icontel cursor-pointer" title="点击拨号"
                           onclick="callout('${afterSalesVo.afterConnectPhone}',${afterSalesVo.traderId},1,4,${afterSalesVo.afterSalesId},${afterSalesVo.traderContactId});"></i>
                    </c:if>
                </td>
            </tr>

            <tr>
                <td class="wid20">售后报单人</td>
                <td>${afterSalesVo.traderContactName}</td>

                <td>售后报单人电话</td>
                <td>
                    ${afterSalesVo.traderContactTelephone}
                    <c:if test="${not empty afterSalesVo.traderContactTelephone}">
                        <i class="icontel cursor-pointer" title="点击拨号"
                           onclick="callout('${afterSalesVo.traderContactTelephone}',${afterSalesVo.traderId},1,4,${afterSalesVo.afterSalesId},${afterSalesVo.traderContactId});"></i>
                    </c:if>
                </td>
            </tr>

            <tr>
                <td>售后报单人手机号</td>
                <td>
                    ${afterSalesVo.traderContactMobile}
                    <c:if test="${not empty afterSalesVo.traderContactMobile}">
                        <i class="icontel cursor-pointer" title="点击拨号"
                           onclick="callout('${afterSalesVo.traderContactMobile}',${afterSalesVo.traderId},1,4,${afterSalesVo.afterSalesId},${afterSalesVo.traderContactId});"></i>
                    </c:if>
                </td>
            </tr>

            <tr>
                <td>详情说明</td>
                <td colspan="3" class="text-left">${afterSalesVo.comments}</td>

            </tr>
            <tr>
                <td>附件</td>
                <td colspan="3" class="text-left">
                    <%@ include file="view_afterSales_files.jsp" %>
                </td>

            </tr>
            </tbody>
        </table>
    </div>

    <form action="" id="editAfterSaleOrderForm">
        <input type="hidden" name="afterSalesId" value="${afterSalesVo.afterSalesId}"/>
        <input type="hidden" name="afterSalesDetailId" value="${afterSalesVo.afterSalesDetailId}">
        <input type="hidden" name="refund" value="${afterSalesVo.refund}">
        <input type="hidden" name="traderSubject" value="${afterSalesVo.traderSubject}">
        <input type="hidden" name="payee" value="${afterSalesVo.payee}">
        <input type="hidden" name="bank" value="${afterSalesVo.bank}">
        <input type="hidden" name="bankCode" value="${afterSalesVo.bankCode}">
        <input type="hidden" name="bankAccount" value="${afterSalesVo.bankAccount}">
        <input type="hidden" name="traderMode" value="${afterSalesVo.traderMode}">
        <input type="hidden" name="refundAmountStatus" value="${afterSalesVo.refundAmountStatus}">
        <input type="hidden" name="type" value="${afterSalesVo.type}">
        <input class="child" type="hidden" id="layerIndex">
        <input type="hidden" id="typeFlag" value="0">
    </form>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                退款金额信息
            </div>
            <c:if test="${afterSalesVo.atferSalesStatus eq 1 && user.positType eq 312 && ((null==taskInfoOver and null==taskInfoOver.getProcessInstanceId())or (null!=taskInfoOver and taskInfoOver.assignee==null and empty candidateUserMapOver[taskInfoOver.id]))
            && updateRefundCheck}">
<%--                <div class="title-click nobor"  onclick="editAfterSalesBackFundInfo(${afterSalesVo.afterSalesId},${afterSalesVo.type},${afterSalesVo.refund},${afterSalesVo.finalRefundableAmount})">编辑退款金额信息</div>--%>
                <span class="title-click nobor pop-new-data-noclose" layerParams='{"width":"80%","height":"600px","title":"退还款项分配","link":"/afterSalesOrder/refundInfo/edit.do?afterSalesId=${afterSalesVo.afterSalesId}&type=${afterSalesVo.type}&refund=${afterSalesVo.refund}&finalRefundableAmount=${afterSalesVo.finalRefundableAmount}"}'>退还款项分配</span>
            </c:if>
            <c:if test="${detailRefundCheck}">
                <span class="title-click nobor pop-new-data" layerParams='{"width":"80%","height":"500px","title":"查看退还款项分配","link":"/afterSalesOrder/refundInfo/detail.do?afterSalesId=${afterSalesVo.afterSalesId}&type=${afterSalesVo.type}&refund=${afterSalesVo.refund}&finalRefundableAmount=${afterSalesVo.finalRefundableAmount}"}'>查看退还款项分配</span>
            </c:if>
        </div>

        <table class="table">
            <tbody>
            <tr>
                <td>退款金额</td>
                <td class="warning-color1"><fmt:formatNumber type="number" value="${afterSalesVo.refundAmount}"
                                                             pattern="0.00" maxFractionDigits="2"/></td>
                <td>交易方式</td>
                <td>
                    <c:if test="${afterSalesVo.traderMode eq 520}">支付宝</c:if>
                    <c:if test="${afterSalesVo.traderMode eq 521}">银行</c:if>
                </td>
            </tr>

            <tr>
                <td>主体交易方式</td>
                <td>
                    <c:if test="${afterSalesVo.traderSubject eq 1}">对公</c:if>
                    <c:if test="${afterSalesVo.traderSubject eq 2}">对私</c:if>
                </td>
                <td>收款名称</td>
                <td>${afterSalesVo.payee}</td>
            </tr>

            <tr>
                <td>开户银行</td>
                <td>${afterSalesVo.bank}</td>
                <td>银行/支付宝账号</td>
                <td>${afterSalesVo.bankAccount}</td>
            </tr>

            <tr>
                <td>开户行支付联行号</td>
                <td>${afterSalesVo.bankCode}</td>

                <td class="wid20">款项退还</td>
                <td>
                    <c:if test="${afterSalesVo.refund eq 0}">无</c:if>
                    <c:if test="${afterSalesVo.refund eq 1}">退到余额</c:if>
                    <c:if test="${afterSalesVo.refund eq 2}">退回账户</c:if>
                </td>
            </tr>
            </tbody>
        </table>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                跟进记录
            </div>
            <c:if test="${afterSalesVo.atferSalesStatus eq 1 && user.positType eq 312}">
                <div class="title-click nobor  pop-new-data" layerParams='{"width":"600px","height":"400px","title":"新增跟进记录",
		                  "link":"/order/afterSalesCommon/addFollowUpRecord.do?afterSalesId=${afterSalesVo.afterSalesId}"}'>
                    新增跟进记录
                </div>
            </c:if>
        </div>
        <%@ include file="add_followUp_common.jsp" %>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                所属订单信息
            </div>
        </div>
        <table class="table">
            <tbody>
            <tr>
                <td class="wid20">所属销售单号</td>
                <td>
                    <div class="customername pos_rel">
                               <span class="brand-color1 addtitle" style="float:none;"
                                     tabTitle='{"num":"viewsaleorder${afterSalesVo.orderNo}","title":"订单信息",
                               		"link":"./orderstream/saleorder/detail.do?saleOrderId=${afterSalesVo.orderId}"}'>${afterSalesVo.orderNo}</span><i
                            class="iconbluemouth"></i>
                        <div class="pos_abs customernameshow" style="display: none;">
                            付款状态：<c:if test="${afterSalesVo.paymentStatus eq 0}">未付款</c:if>
                            <c:if test="${afterSalesVo.paymentStatus eq 1}">部分付款</c:if>
                            <c:if test="${afterSalesVo.paymentStatus eq 2}">全部付款</c:if><br>
                            发货状态：<c:if test="${afterSalesVo.deliveryStatus eq 0}">未发货</c:if>
                            <c:if test="${afterSalesVo.deliveryStatus eq 1}">部分发货</c:if>
                            <c:if test="${afterSalesVo.deliveryStatus eq 2}">全部发货</c:if><br>
                            开票状态：<c:if test="${afterSalesVo.invoiceStatus eq 0}">未开票</c:if>
                            <c:if test="${afterSalesVo.invoiceStatus eq 1}">部分开票</c:if>
                            <c:if test="${afterSalesVo.invoiceStatus eq 2}">全部开票</c:if><br>
                            收货状态：<c:if test="${afterSalesVo.arrivalStatus eq 0}">未收货</c:if>
                            <c:if test="${afterSalesVo.arrivalStatus eq 1}">部分收货</c:if>
                            <c:if test="${afterSalesVo.arrivalStatus eq 2}">全部收货</c:if>
                        </div>
                    </div>
                </td>
                <td class="wid20">销售单金额</td>
                <td><fmt:formatNumber type="number" value="${afterSalesVo.totalAmount}" pattern="0.00"
                                      maxFractionDigits="2"/></td>
            </tr>

            <tr>
                <td>归属销售</td>
                <td>${afterSalesVo.userName}</td>
                <td>归属部门</td>
                <td>${afterSalesVo.orgName}</td>
            </tr>

            <tr>
                <td>订单状态</td>
                <td>
                    <c:if test="${afterSalesVo.saleorderStatus eq 0}">待确认</c:if>
                    <c:if test="${afterSalesVo.saleorderStatus eq 1}">进行中</c:if>
                    <c:if test="${afterSalesVo.saleorderStatus eq 2}">已完结</c:if>
                    <c:if test="${afterSalesVo.saleorderStatus eq 3}">已关闭</c:if>
                </td>
                <td>生效时间</td>
                <td><date:date value="${afterSalesVo.saleorderValidTime}"/></td>
            </tr>

            <tr>
                <td>客户名称</td>
                <td>
                    <div class="customername pos_rel">
                                  <span class="brand-color1 addtitle" style="float:none;"
                                        tabTitle='{"num":"viewcustomer${afterSalesVo.traderId}","title":"客户信息",
										"link":"./trader/customer/baseinfo.do?traderId=${afterSalesVo.traderId}"}'>${afterSalesVo.traderName}</span><i
                            class="iconbluemouth"></i>
                        <div class="pos_abs customernameshow" style="display: none;">
                            客户性质：<c:if test="${afterSalesVo.customerNature eq 465}">分销</c:if>
                            <c:if test="${afterSalesVo.customerNature eq 466}">终端</c:if><br>
                            交易次数：${afterSalesVo.orderCount}<br>
                            交易金额：<fmt:formatNumber type="number" value="${afterSalesVo.orderTotalAmount}" pattern="0.00"
                                                   maxFractionDigits="2"/><br>
                            上次交易日期：<date:date value="${afterSalesVo.lastOrderTime}"/>
                        </div>
                    </div>
                </td>
                <td>客户等级</td>
                <td>
                    ${afterSalesVo.customerLevelStr}
                </td>
            </tr>
            </tbody>
        </table>
    </div>


    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">付款申请</div>
            <%--   1.售后单审核通过；2.非已完结 已关闭订单； 3.可申请金额大于0；--%>
            <c:if test="${shouldPayApplyButton}">
                <div class="title-click nobor  pop-new-data" layerParams='{"width":"80%","height":"600px","title":"新增退款付款申请",
		                  "link":"/afterSalesOrder/payApply.do?afterSalesId=${afterSalesVo.afterSalesId}"}'>
                    新增退款付款申请
                </div>
            </c:if>
        </div>
        <table class="table  table-style6">
            <thead>
            <tr>
                <th class="wid6">申请金额</th>
                <th class="wid18">申请时间</th>
                <th class="wid12">申请人</th>
                <th class="wid8">交易名称</th>
                <th class="wid8">开户行及联行号</th>
                <th class="wid10">银行帐号</th>
                <th class="wid10">付款备注</th>
                <th class="wid5">审核状态</th>
                <th class="wid5">查看</th>
            </tr>
            </thead>
            <tbody>
            <c:if test="${not empty afterSalesVo.afterPayApplyList}">
                <c:forEach items="${afterSalesVo.afterPayApplyList}" var="apal" varStatus="num_index">
                    <tr>
                        <td><fmt:formatNumber type="number" value="${apal.amount}" pattern="0.00"
                                              maxFractionDigits="2"/></td>
                        <td><date:date value="${apal.addTime}" format="yyyy.MM.dd HH:mm:ss"/></td>
                        <td>
                                ${apal.creatorName}
                        </td>
                        <td>${apal.traderName}</td>
                        <td>
                                ${apal.bank}<br/>${apal.bankCode}
                        </td>
                        <td>${apal.bankAccount}</td>
                        <td>${apal.comments}</td>
                        <td>
                            <c:choose>
                                <c:when test="${apal.validStatus eq 0}">待审核</c:when>
                                <c:when test="${apal.validStatus eq 1}">通过</c:when>
                                <c:when test="${apal.validStatus eq 2}">不通过</c:when>
                                <c:otherwise>--</c:otherwise>
                            </c:choose>
                        </td>
                        <td>
                            <div class="caozuo">
                                <span class="caozuo-blue pop-new-data"
                                      layerparams='{"width":"50%","height":"30%","title":"付款申请审核信息","link":"<%=basePath%>finance/after/paymentVerify.do?payApplyId=${apal.payApplyId}"}'>查看</span>
                            </div>
                        </td>
                    </tr>
                </c:forEach>
            </c:if>
            <c:if test="${empty afterSalesVo.afterPayApplyList}">
                <tr>
                    <td colspan="9">暂无记录！</td>
                </tr>
            </c:if>
            </tbody>
        </table>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">交易记录</div>
        </div>
        <table class="table">
            <thead>
            <tr>
                <td colspan="11">
                    <div style="float: left; margin-left: 100px;">最终应退金额：
                        <fmt:formatNumber value="${afterSalesVo.finalRefundableAmount}" type="numer" pattern="0.00"
                                          maxFractionDigits="2"/>
                    </div>
                    <div style="float: left; margin-left: 100px;">客户已退金额：
                        <fmt:formatNumber value="${refundAmount}" type="numer" pattern="0.00"
                                          maxFractionDigits="2"/>
                    </div>

                    <div style="float: left; margin-left: 100px;">退款状态：
                        <c:choose>
                            <c:when test="${afterSalesVo.amountRefundStatus eq 0}">
                                无退款
                            </c:when>
                            <c:when test="${afterSalesVo.amountRefundStatus eq 1}">
                                未退款
                            </c:when>
                            <c:when test="${afterSalesVo.amountRefundStatus eq 2}">
                                部分退款
                            </c:when>
                            <c:when test="${afterSalesVo.amountRefundStatus eq 3}">
                                全部退款
                            </c:when>
                        </c:choose>
                    </div>
                </td>
            </tr>
            <tr>
                <th>记账编号</th>
                <th>业务类型</th>
                <th>交易时间</th>
                <th>主体交易方式</th>
                <th>交易金额</th>
                <th>交易方式</th>
                <th>交易名称</th>
                <th>交易备注</th>
                <th>操作时间</th>
                <th>操作人</th>
                <th>操作</th>
            </tr>
            </thead>
            <tbody>
            <c:if test="${not empty afterSalesVo.afterCapitalBillList}">
                <c:forEach items="${afterSalesVo.afterCapitalBillList}" var="acb">
                    <tr>
                        <td>${acb.capitalBillNo}</td>
                        <td>
                            <c:if test="${acb.capitalBillDetail.bussinessType eq 525}">订单付款</c:if>
                            <c:if test="${acb.capitalBillDetail.bussinessType eq 526}">订单收款</c:if>
                            <c:if test="${acb.capitalBillDetail.bussinessType eq 531}">退款</c:if>
                            <c:if test="${acb.capitalBillDetail.bussinessType eq 532}">资金转移</c:if>
                            <c:if test="${acb.capitalBillDetail.bussinessType eq 533}">信用还款</c:if>
                        </td>
                        <td>
                            <c:if test="${acb.traderTime != 0}">
                                <date:date value="${acb.traderTime}"/>
                            </c:if>
                        </td>
                        <td>
                            <c:if test="${acb.traderSubject == 1}">
                                对公
                            </c:if>
                            <c:if test="${acb.traderSubject == 2}">
                                对私
                            </c:if>
                        </td>
                        <td>${acb.amount}</td>
                        <td>
                            <c:if test="${acb.traderMode eq 520}">支付宝</c:if>
                            <c:if test="${acb.traderMode eq 521}">银行</c:if>
                            <c:if test="${acb.traderMode eq 522}">微信</c:if>
                            <c:if test="${acb.traderMode eq 522}">现金</c:if>
                            <c:if test="${acb.traderMode eq 527}">信用支付</c:if>
                            <c:if test="${acb.traderMode eq 528}">余额支付</c:if>
                            <c:if test="${acb.traderMode eq 529}">退还信用</c:if>
                            <c:if test="${acb.traderMode eq 530}">退还余额</c:if>
                        </td>
                        <td>${acb.payer}</td>
                        <td class="text-left">${acb.comments}</td>
                        <td>
                            <c:if test="${acb.addTime != 0}">
                                <date:date value="${acb.addTime}"/>
                            </c:if>
                        </td>
                        <td>${acb.creatorName}</td>
                        <td>
                            <c:if test="${isSupply ne 1}">
                                <c:if test="${(acb.traderType == 2 || acb.traderType == 5) && acb.bankBillId != 0}">
                                    <div class="caozuo">
                                        <c:choose>
                                            <c:when test="${not empty acb.receiptUrl}">
                                                <span class="caozuo-blue" onclick="openFile('${acb.receiptUrl}')">回单</span>
                                            </c:when>
                                            <c:otherwise>
                                        <span class="caozuo-blue addtitle"
                                              tabTitle='{"num":"credentials${acb.bankBillId}", "link":"<%=basePath%>finance/capitalbill/credentials.do?bankBillId=${acb.bankBillId}","title":"电子回执单"}'>预览</span>
                                            </c:otherwise>
                                        </c:choose>
                                    </div>
                                </c:if>
                            </c:if>
                        </td>
                    </tr>
                </c:forEach>
            </c:if>
            <c:if test="${empty afterSalesVo.afterCapitalBillList}">
                <!-- 查询无结果弹出 -->
                <tr>
                    <td colspan='11'>暂无记录！</td>
                </tr>
            </c:if>
            </tbody>
        </table>
    </div>


    <%--支出记录--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">支出记录</div>
            <c:if test="${afterSalesVo.atferSalesStatus eq 1 && user.positType eq 312}">
                <div class="title-click nobor  pop-new-data" layerParams='{"width":"600px","height":"400px","title":"新增支出记录",
		                  "link":"/order/afterSalesCommon/addExpenditureRecord.do?afterSalesId=${afterSalesVo.afterSalesId}"}'>
                    新增支出记录
                </div>
            </c:if>
        </div>
        <%@ include file="add_expenditure_common.jsp" %>
    </div>

    <%--收入记录--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">收入记录</div>
            <c:if test="${afterSalesVo.atferSalesStatus eq 1 && user.positType eq 312}">
                <div class="title-click nobor  pop-new-data" layerParams='{"width":"600px","height":"400px","title":"新增收入记录",
		                  "link":"/order/afterSalesCommon/addRevenueRecord.do?afterSalesId=${afterSalesVo.afterSalesId}"}'>
                    新增收入记录
                </div>
            </c:if>
        </div>
        <%@ include file="add_revenue_common.jsp" %>
    </div>

    <!-- 沟通记录只能让售后看 -->
    <c:if test="${sessionScope.curr_user.positType eq 312}">
        <div class="parts">
            <div class="title-container">
                <div class="table-title nobor">
                    沟通记录
                </div>
                <c:if test="${afterSalesVo.status eq 2}">
                    <div class="title-click nobor  pop-new-data" layerParams='{"width":"850px","height":"460px","title":"新增沟通记录",
	                		"link":"<%= basePath %>/aftersales/order/addCommunicatePage.do?afterSalesId=${afterSalesVo.afterSalesId}&&traderId=${afterSalesVo.traderId }&&traderType=1"}'>
                        新增
                    </div>
                </c:if>
            </div>
            <table class="table table-bordered table-striped table-condensed table-centered">
                <thead>
                <tr>
                    <th class="wid10">沟通时间</th>
                    <th class="">录音</th>
                    <th class="">联系人</th>
                    <th class="">联系方式</th>
                    <th class="">沟通方式</th>
                    <th class="wid30">沟通内容（AI分析整理）</th>
                    <th class="">操作人</th>
                    <th class="wid8">下次联系日期</th>
                    <th class="wid15">下次沟通内容</th>
                    <th class="">备注</th>
                    <th class="wid10">创建时间</th>
                    <th class="wid6">操作</th>
                </tr>
                </thead>
                <tbody>
                <c:if test="${not empty communicateList}">
                    <c:forEach items="${communicateList}" var="communicateRecord" varStatus="">
                        <tr>
                            <td><date:date value="${communicateRecord.begintime} "/>~<date:date
                                    value="${communicateRecord.endtime}" format="HH:mm:ss"/></td>
                            <td><c:if
                                    test="${not empty communicateRecord.coidUri }">${communicateRecord.communicateRecordId }</c:if></td>
                            <td>${communicateRecord.contactName}</td>
                            <td>${communicateRecord.phone}</td>
                            <td>${communicateRecord.communicateModeName}</td>
                            <td>
                                <ul class="communicatecontent ml0">
                                    <c:choose>
                                        <c:when test="${not empty communicateRecord.tag }">
                                            <c:forEach items="${communicateRecord.tag }" var="tag">
                                                <li class="bluetag" title="${tag.tagName}">${tag.tagName}</li>
                                            </c:forEach>
                                        </c:when>
                                        <c:otherwise>
                                            <li>${communicateRecord.contactContent}</li>
                                        </c:otherwise>
                                    </c:choose>
                                </ul>
                            </td>
                            <td>${communicateRecord.user.username}</td>
                            <c:choose>
                                <c:when test="${communicateRecord.isDone == 0 }">
                                    <td class="font-red">${communicateRecord.nextContactDate }</td>
                                </c:when>
                                <c:otherwise>
                                    <td>${communicateRecord.nextContactDate }</td>
                                </c:otherwise>
                            </c:choose>
                            <td>${communicateRecord.nextContactContent}</td>
                            <td>${communicateRecord.comments}</td>
                            <td><date:date value="${communicateRecord.addTime} "/></td>

                            <td class="caozuo">
                                <c:if test="${isSupply ne 1}">
                                    <c:if test="${afterSalesVo.atferSalesStatus ne 3 && afterSalesVo.atferSalesStatus ne 2 && afterSalesVo.verifyStatus ne 0}">
		                        	<span class="border-blue pop-new-data" layerParams='{"width":"60%","height":"63%","title":"编辑沟通记录",
		                        		"link":"<%= basePath %>/aftersales/order/editcommunicate.do?orderFlag=${afterSalesVo.atferSalesStatus }&flag=${afterSalesVo.status }&communicateRecordId=${communicateRecord.communicateRecordId}&afterSalesId=${afterSalesVo.afterSalesId}&&traderId=${afterSalesVo.traderId }&&traderType=1"}'>编辑</span>
                                    </c:if>
                                </c:if>
                            </td>
                        </tr>
                    </c:forEach>
                </c:if>
                <c:if test="${empty communicateList}">
                    <!-- 查询无结果弹出 -->
                    <tr>
                        <td colspan='12'>暂无记录！</td>
                    </tr>
                </c:if>
                </tbody>
            </table>
        </div>
    </c:if>


    <%--回访记录模块--%>
    <div class="parts">
        <%@ include file="afterSales_return_visit.jsp"%>
    </div>


    <%--各项审核记录模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                各项审核记录
            </div>
        </div>
        <%@ include file="add_auditRecords_common.jsp" %>
    </div>
</div>
