package com.vedeng.erp.finance.dto;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/18 14:28
 **/
@Setter
@Getter
public class InvoiceNumApiDto {


    List<SalesOrderInvoiceDto> goodsList;


    List<Integer> goodsIds;

    Integer saleOrderId;



    @Setter
    @Getter
    public static class SalesOrderInvoiceDto {

        private Integer salesOrderGoodsId;

        private BigDecimal arrivalNum;

        private Date time;

        private String sku;

        /**
         * 无理由退货
         */
        private Integer n=0;

        /**
         * 销售单数量
         */
        private BigDecimal salesOrderNum;

        /**
         * 售后单
         */
        private BigDecimal afterSalesNum = BigDecimal.ZERO;

        /**
         * 开票数量
         */
        private BigDecimal invoiceNum = BigDecimal.ZERO;


        private Integer isVirtualSku;

    }


}

