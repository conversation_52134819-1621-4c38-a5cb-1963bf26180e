package com.vedeng.erp.kingdee.service.impl;

import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeeFileCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeFileDataEntity;
import com.vedeng.erp.kingdee.dto.KingDeeFileDataDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeFileCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeFileConvertor;
import com.vedeng.erp.kingdee.repository.KingDeeFileDataRepository;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeFileDataMapper;
import com.vedeng.erp.kingdee.service.KingDeeFileDataApiService;
import com.vedeng.erp.kingdee.service.KingDeeFileDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/13 14:09
 */
@Service
@Slf4j
public class KingDeeFileDataServiceImpl extends KingDeeBaseServiceImpl<
        KingDeeFileDataEntity,
        KingDeeFileDataDto,
        KingDeeFileCommand,
        KingDeeFileDataRepository,
        KingDeeFileConvertor,
        KingDeeFileCommandConvertor> implements KingDeeFileDataService, KingDeeFileDataApiService {

    @Autowired
    private KingDeeFileDataMapper kingDeeFileDataMapper;

    @Autowired
    private KingDeeFileConvertor kingDeeFileConvertor;

    @Override
    public List<KingDeeFileDataDto> getByBusinessIdAndUri(KingDeeFileDataDto query) {
        List<KingDeeFileDataEntity> kingDeeFileDataList = kingDeeFileDataMapper.getByBusinessIdAndUri(kingDeeFileConvertor.toEntity(query));
        return kingDeeFileConvertor.toDto(kingDeeFileDataList);
    }
}