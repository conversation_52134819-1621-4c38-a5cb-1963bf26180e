package com.vedeng.trader.model;

import java.util.ArrayList;
import java.util.List;

public class SupplierRegistrationNumberExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_SUPPLIER_REGISTRATION_NUMBER
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_SUPPLIER_REGISTRATION_NUMBER
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_SUPPLIER_REGISTRATION_NUMBER
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SUPPLIER_REGISTRATION_NUMBER
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    public SupplierRegistrationNumberExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SUPPLIER_REGISTRATION_NUMBER
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SUPPLIER_REGISTRATION_NUMBER
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SUPPLIER_REGISTRATION_NUMBER
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SUPPLIER_REGISTRATION_NUMBER
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SUPPLIER_REGISTRATION_NUMBER
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SUPPLIER_REGISTRATION_NUMBER
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SUPPLIER_REGISTRATION_NUMBER
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SUPPLIER_REGISTRATION_NUMBER
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SUPPLIER_REGISTRATION_NUMBER
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SUPPLIER_REGISTRATION_NUMBER
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_SUPPLIER_REGISTRATION_NUMBER
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andSupplierRegistrationNumberIdIsNull() {
            addCriterion("SUPPLIER_REGISTRATION_NUMBER_ID is null");
            return (Criteria) this;
        }

        public Criteria andSupplierRegistrationNumberIdIsNotNull() {
            addCriterion("SUPPLIER_REGISTRATION_NUMBER_ID is not null");
            return (Criteria) this;
        }

        public Criteria andSupplierRegistrationNumberIdEqualTo(Integer value) {
            addCriterion("SUPPLIER_REGISTRATION_NUMBER_ID =", value, "supplierRegistrationNumberId");
            return (Criteria) this;
        }

        public Criteria andSupplierRegistrationNumberIdNotEqualTo(Integer value) {
            addCriterion("SUPPLIER_REGISTRATION_NUMBER_ID <>", value, "supplierRegistrationNumberId");
            return (Criteria) this;
        }

        public Criteria andSupplierRegistrationNumberIdGreaterThan(Integer value) {
            addCriterion("SUPPLIER_REGISTRATION_NUMBER_ID >", value, "supplierRegistrationNumberId");
            return (Criteria) this;
        }

        public Criteria andSupplierRegistrationNumberIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("SUPPLIER_REGISTRATION_NUMBER_ID >=", value, "supplierRegistrationNumberId");
            return (Criteria) this;
        }

        public Criteria andSupplierRegistrationNumberIdLessThan(Integer value) {
            addCriterion("SUPPLIER_REGISTRATION_NUMBER_ID <", value, "supplierRegistrationNumberId");
            return (Criteria) this;
        }

        public Criteria andSupplierRegistrationNumberIdLessThanOrEqualTo(Integer value) {
            addCriterion("SUPPLIER_REGISTRATION_NUMBER_ID <=", value, "supplierRegistrationNumberId");
            return (Criteria) this;
        }

        public Criteria andSupplierRegistrationNumberIdIn(List<Integer> values) {
            addCriterion("SUPPLIER_REGISTRATION_NUMBER_ID in", values, "supplierRegistrationNumberId");
            return (Criteria) this;
        }

        public Criteria andSupplierRegistrationNumberIdNotIn(List<Integer> values) {
            addCriterion("SUPPLIER_REGISTRATION_NUMBER_ID not in", values, "supplierRegistrationNumberId");
            return (Criteria) this;
        }

        public Criteria andSupplierRegistrationNumberIdBetween(Integer value1, Integer value2) {
            addCriterion("SUPPLIER_REGISTRATION_NUMBER_ID between", value1, value2, "supplierRegistrationNumberId");
            return (Criteria) this;
        }

        public Criteria andSupplierRegistrationNumberIdNotBetween(Integer value1, Integer value2) {
            addCriterion("SUPPLIER_REGISTRATION_NUMBER_ID not between", value1, value2, "supplierRegistrationNumberId");
            return (Criteria) this;
        }

        public Criteria andTraderIdIsNull() {
            addCriterion("TRADER_ID is null");
            return (Criteria) this;
        }

        public Criteria andTraderIdIsNotNull() {
            addCriterion("TRADER_ID is not null");
            return (Criteria) this;
        }

        public Criteria andTraderIdEqualTo(Integer value) {
            addCriterion("TRADER_ID =", value, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdNotEqualTo(Integer value) {
            addCriterion("TRADER_ID <>", value, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdGreaterThan(Integer value) {
            addCriterion("TRADER_ID >", value, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("TRADER_ID >=", value, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdLessThan(Integer value) {
            addCriterion("TRADER_ID <", value, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdLessThanOrEqualTo(Integer value) {
            addCriterion("TRADER_ID <=", value, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdIn(List<Integer> values) {
            addCriterion("TRADER_ID in", values, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdNotIn(List<Integer> values) {
            addCriterion("TRADER_ID not in", values, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdBetween(Integer value1, Integer value2) {
            addCriterion("TRADER_ID between", value1, value2, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdNotBetween(Integer value1, Integer value2) {
            addCriterion("TRADER_ID not between", value1, value2, "traderId");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdIsNull() {
            addCriterion("REGISTRATION_NUMBER_ID is null");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdIsNotNull() {
            addCriterion("REGISTRATION_NUMBER_ID is not null");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdEqualTo(Integer value) {
            addCriterion("REGISTRATION_NUMBER_ID =", value, "registrationNumberId");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdNotEqualTo(Integer value) {
            addCriterion("REGISTRATION_NUMBER_ID <>", value, "registrationNumberId");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdGreaterThan(Integer value) {
            addCriterion("REGISTRATION_NUMBER_ID >", value, "registrationNumberId");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("REGISTRATION_NUMBER_ID >=", value, "registrationNumberId");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdLessThan(Integer value) {
            addCriterion("REGISTRATION_NUMBER_ID <", value, "registrationNumberId");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdLessThanOrEqualTo(Integer value) {
            addCriterion("REGISTRATION_NUMBER_ID <=", value, "registrationNumberId");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdIn(List<Integer> values) {
            addCriterion("REGISTRATION_NUMBER_ID in", values, "registrationNumberId");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdNotIn(List<Integer> values) {
            addCriterion("REGISTRATION_NUMBER_ID not in", values, "registrationNumberId");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdBetween(Integer value1, Integer value2) {
            addCriterion("REGISTRATION_NUMBER_ID between", value1, value2, "registrationNumberId");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdNotBetween(Integer value1, Integer value2) {
            addCriterion("REGISTRATION_NUMBER_ID not between", value1, value2, "registrationNumberId");
            return (Criteria) this;
        }

        public Criteria andCheckStatusIsNull() {
            addCriterion("CHECK_STATUS is null");
            return (Criteria) this;
        }

        public Criteria andCheckStatusIsNotNull() {
            addCriterion("CHECK_STATUS is not null");
            return (Criteria) this;
        }

        public Criteria andCheckStatusEqualTo(Byte value) {
            addCriterion("CHECK_STATUS =", value, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusNotEqualTo(Byte value) {
            addCriterion("CHECK_STATUS <>", value, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusGreaterThan(Byte value) {
            addCriterion("CHECK_STATUS >", value, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("CHECK_STATUS >=", value, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusLessThan(Byte value) {
            addCriterion("CHECK_STATUS <", value, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusLessThanOrEqualTo(Byte value) {
            addCriterion("CHECK_STATUS <=", value, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusIn(List<Byte> values) {
            addCriterion("CHECK_STATUS in", values, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusNotIn(List<Byte> values) {
            addCriterion("CHECK_STATUS not in", values, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusBetween(Byte value1, Byte value2) {
            addCriterion("CHECK_STATUS between", value1, value2, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("CHECK_STATUS not between", value1, value2, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckUserIdIsNull() {
            addCriterion("CHECK_USER_ID is null");
            return (Criteria) this;
        }

        public Criteria andCheckUserIdIsNotNull() {
            addCriterion("CHECK_USER_ID is not null");
            return (Criteria) this;
        }

        public Criteria andCheckUserIdEqualTo(Integer value) {
            addCriterion("CHECK_USER_ID =", value, "checkUserId");
            return (Criteria) this;
        }

        public Criteria andCheckUserIdNotEqualTo(Integer value) {
            addCriterion("CHECK_USER_ID <>", value, "checkUserId");
            return (Criteria) this;
        }

        public Criteria andCheckUserIdGreaterThan(Integer value) {
            addCriterion("CHECK_USER_ID >", value, "checkUserId");
            return (Criteria) this;
        }

        public Criteria andCheckUserIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("CHECK_USER_ID >=", value, "checkUserId");
            return (Criteria) this;
        }

        public Criteria andCheckUserIdLessThan(Integer value) {
            addCriterion("CHECK_USER_ID <", value, "checkUserId");
            return (Criteria) this;
        }

        public Criteria andCheckUserIdLessThanOrEqualTo(Integer value) {
            addCriterion("CHECK_USER_ID <=", value, "checkUserId");
            return (Criteria) this;
        }

        public Criteria andCheckUserIdIn(List<Integer> values) {
            addCriterion("CHECK_USER_ID in", values, "checkUserId");
            return (Criteria) this;
        }

        public Criteria andCheckUserIdNotIn(List<Integer> values) {
            addCriterion("CHECK_USER_ID not in", values, "checkUserId");
            return (Criteria) this;
        }

        public Criteria andCheckUserIdBetween(Integer value1, Integer value2) {
            addCriterion("CHECK_USER_ID between", value1, value2, "checkUserId");
            return (Criteria) this;
        }

        public Criteria andCheckUserIdNotBetween(Integer value1, Integer value2) {
            addCriterion("CHECK_USER_ID not between", value1, value2, "checkUserId");
            return (Criteria) this;
        }

        public Criteria andCheckTimeIsNull() {
            addCriterion("CHECK_TIME is null");
            return (Criteria) this;
        }

        public Criteria andCheckTimeIsNotNull() {
            addCriterion("CHECK_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andCheckTimeEqualTo(Long value) {
            addCriterion("CHECK_TIME =", value, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeNotEqualTo(Long value) {
            addCriterion("CHECK_TIME <>", value, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeGreaterThan(Long value) {
            addCriterion("CHECK_TIME >", value, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("CHECK_TIME >=", value, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeLessThan(Long value) {
            addCriterion("CHECK_TIME <", value, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeLessThanOrEqualTo(Long value) {
            addCriterion("CHECK_TIME <=", value, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeIn(List<Long> values) {
            addCriterion("CHECK_TIME in", values, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeNotIn(List<Long> values) {
            addCriterion("CHECK_TIME not in", values, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeBetween(Long value1, Long value2) {
            addCriterion("CHECK_TIME between", value1, value2, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeNotBetween(Long value1, Long value2) {
            addCriterion("CHECK_TIME not between", value1, value2, "checkTime");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNull() {
            addCriterion("IS_DELETE is null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNotNull() {
            addCriterion("IS_DELETE is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteEqualTo(Boolean value) {
            addCriterion("IS_DELETE =", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotEqualTo(Boolean value) {
            addCriterion("IS_DELETE <>", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThan(Boolean value) {
            addCriterion("IS_DELETE >", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThanOrEqualTo(Boolean value) {
            addCriterion("IS_DELETE >=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThan(Boolean value) {
            addCriterion("IS_DELETE <", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThanOrEqualTo(Boolean value) {
            addCriterion("IS_DELETE <=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIn(List<Boolean> values) {
            addCriterion("IS_DELETE in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotIn(List<Boolean> values) {
            addCriterion("IS_DELETE not in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteBetween(Boolean value1, Boolean value2) {
            addCriterion("IS_DELETE between", value1, value2, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotBetween(Boolean value1, Boolean value2) {
            addCriterion("IS_DELETE not between", value1, value2, "isDelete");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("ADD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("ADD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Long value) {
            addCriterion("ADD_TIME =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Long value) {
            addCriterion("ADD_TIME <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Long value) {
            addCriterion("ADD_TIME >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("ADD_TIME >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Long value) {
            addCriterion("ADD_TIME <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Long value) {
            addCriterion("ADD_TIME <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Long> values) {
            addCriterion("ADD_TIME in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Long> values) {
            addCriterion("ADD_TIME not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Long value1, Long value2) {
            addCriterion("ADD_TIME between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Long value1, Long value2) {
            addCriterion("ADD_TIME not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("UPDATE_TIME is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("UPDATE_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Long value) {
            addCriterion("UPDATE_TIME =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Long value) {
            addCriterion("UPDATE_TIME <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Long value) {
            addCriterion("UPDATE_TIME >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("UPDATE_TIME >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Long value) {
            addCriterion("UPDATE_TIME <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Long value) {
            addCriterion("UPDATE_TIME <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Long> values) {
            addCriterion("UPDATE_TIME in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Long> values) {
            addCriterion("UPDATE_TIME not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Long value1, Long value2) {
            addCriterion("UPDATE_TIME between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Long value1, Long value2) {
            addCriterion("UPDATE_TIME not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("CREATOR is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("CREATOR is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(Integer value) {
            addCriterion("CREATOR =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(Integer value) {
            addCriterion("CREATOR <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(Integer value) {
            addCriterion("CREATOR >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(Integer value) {
            addCriterion("CREATOR >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(Integer value) {
            addCriterion("CREATOR <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(Integer value) {
            addCriterion("CREATOR <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<Integer> values) {
            addCriterion("CREATOR in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<Integer> values) {
            addCriterion("CREATOR not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andUpdatorIsNull() {
            addCriterion("UPDATOR is null");
            return (Criteria) this;
        }

        public Criteria andUpdatorIsNotNull() {
            addCriterion("UPDATOR is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatorEqualTo(Integer value) {
            addCriterion("UPDATOR =", value, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorNotEqualTo(Integer value) {
            addCriterion("UPDATOR <>", value, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorGreaterThan(Integer value) {
            addCriterion("UPDATOR >", value, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorGreaterThanOrEqualTo(Integer value) {
            addCriterion("UPDATOR >=", value, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorLessThan(Integer value) {
            addCriterion("UPDATOR <", value, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorLessThanOrEqualTo(Integer value) {
            addCriterion("UPDATOR <=", value, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorIn(List<Integer> values) {
            addCriterion("UPDATOR in", values, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorNotIn(List<Integer> values) {
            addCriterion("UPDATOR not in", values, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorBetween(Integer value1, Integer value2) {
            addCriterion("UPDATOR between", value1, value2, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorNotBetween(Integer value1, Integer value2) {
            addCriterion("UPDATOR not between", value1, value2, "updator");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_SUPPLIER_REGISTRATION_NUMBER
     *
     * @mbggenerated do_not_delete_during_merge Mon Nov 30 18:50:07 CST 2020
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_SUPPLIER_REGISTRATION_NUMBER
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}