.vd-ui-upload-wrap {
    .vd-ui-upload-file-wrap {
        .vd-ui-button {
            margin-bottom: 10px;
        }

        &.no-info {
            .vd-ui-button {
                margin-bottom: 0;
            }
        }

        .vd-ui-file-item {
            width: 357px;

            .vd-ui-file-info {
                display: flex;
                align-items: center;
                padding: 5px;

                &:hover {
                    background: #F5F7FA;
                }

                .vd-ui-file-icon {
                    width: 13px;
                    height: 16px;
                    margin-right: 5px;

                    img {
                        width: 100%;
                        height: 100%;
                        vertical-align: top;
                    }
                }

                .vd-ui-file-name {
                    flex: 1;
                }

                .vd-ui-file-size {
                    margin-left: 20px;
                    color: #999;
                }

                .vd-ui-file-option {
                    font-size: 16px;
                    color: #666;
                    cursor: pointer;
                    line-height: 1;
                    margin-left: 20px;

                    &:hover {
                        color: #f60;
                    }
                }
            }

            @keyframes uploadProgress {
                0% {
                    width: 0;
                }

                100% {
                    width: 95%;
                }
            }

            .vd-ui-file-progress {
                width: 100%;
                height: 3px;
                background: #EBEFF2;
                position: relative;

                &::before {
                    content: "";
                    width: 95%;
                    height: 3px;
                    background: #13BF13;
                    position: absolute;
                    top: 0;
                    left: 0;
                    animation: uploadProgress 5s linear;
                }
            }
        }

        .vd-ui-file-list {
            margin-bottom: 10px;
        }

    }

    .vd-ui-upload-tips {
        color: #999;
    }

    .vd-ui-input-error {
        margin-top: 0;
        margin-bottom: 10px;
    }

    .vd-ui-upload-img-wrap {
        display: flex;
        margin-bottom: 10px;

        .vd-ui-upload-img-btn {
            width: 100px;
            height: 100px;
            border: 1px dashed #babfc2;
            background: #fafbfc;
            border-radius: 3px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;

            &:hover {
                border-color: #969b9e;
            }

            .icon-add {
                font-size: 24px;
            }
        }

        .vd-ui-upload-img-item {
            width: 100px;
            height: 100px;
            border: 1px solid #babfc2;
            background: #fff;
            border-radius: 3px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            position: relative;

            img {
                max-width: 100%;
                max-height: 100%;
            }

            .vd-ui-upload-img-options {
                display: flex;
                align-items: center;
                position: absolute;
                bottom: 0;
                width: 100%;
                left: 0;
                background: rgba(0, 0, 0, .3);

                .vd-ui-upload-img-option-item {
                    flex: 1;
                    color: #fff;
                    cursor: pointer;
                    height: 24px;
                    font-size: 12px;
                    text-align: center;
                    line-height: 24px;

                    &:hover {
                        color: #f60;;
                    }
                }
            }
        }

        @keyframes circle {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .vd-ui-upload-img-loading {
            width: 100px;
            height: 100px;
            border: 1px dashed #babfc2;
            background: #fff;
            border-radius: 3px;
            display: flex;
            align-items: center;
            justify-content: center;

            .icon-loading {
                font-size: 24px;
                color: #0099FF;
                animation: circle 2s linear infinite;
            }
        }
    }
}