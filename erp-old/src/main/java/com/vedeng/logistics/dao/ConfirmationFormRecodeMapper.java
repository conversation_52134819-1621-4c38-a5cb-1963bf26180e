package com.vedeng.logistics.dao;

import com.vedeng.logistics.model.ConfirmationFormRecode;
import com.vedeng.ordergoods.model.OrdergoodsStoreAccount;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;
import java.util.List;
@Named("confirmationFormRecodeMapper")
public interface ConfirmationFormRecodeMapper {

    /**
     * 根据主键 确认单id集合获取确认单数据
     * @param ids
     * @return
     */
    List<ConfirmationFormRecode> selectAllByIds(@Param("ids") List<Integer> ids);

    int updateByPrimaryKey(ConfirmationFormRecode confirmationFormRecode);

    ConfirmationFormRecode selectByPrimaryKey(Integer confirmationId);

    int insert(ConfirmationFormRecode confirmationFormRecode);

    int insertSelective(ConfirmationFormRecode confirmationFormRecode);

    List<String> getConfirmationNameByBatchNo(@Param("batchNo") String batchNo);
}
