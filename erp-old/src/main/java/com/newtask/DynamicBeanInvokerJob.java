package com.newtask;

import com.vedeng.common.shiro.SpringContextHolder;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 动态的bean method执行器：注意，beanName为实例类的名称，不能是接口的名称
 * params s : {"bean":"userService", "method":"disableUser", "args":["12345"]}
 * 示例1：{
 *     "bean": "authorizationApplyApiServiceImpl",
 *     "method": "createTraceCode",
 *     "args": [
 *         "http://file.ivedeng.com/file/display?resourceId=dcec23750f794b498dfc8bc35f5655b4",
 *         4523
 *     ]
 * }
 * 示例2：{
 *     "bean": "authorizationApplyApiServiceImpl",
 *     "method": "electronicSign",
 *     "args": [
 *         4523
 *     ]
 * }
 *
 * <AUTHOR>
 * @date 2026/06/25
 **/
@JobHandler("dynamicBeanInvokerJob")
@Component
@Slf4j
public class DynamicBeanInvokerJob extends AbstractJobHandler {

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        try {
            // 参数解析：假设是 JSON 格式
            // 示例: {"bean":"userService", "method":"disableUser", "args":["12345"]}
            Map<String, Object> paramMap = new ObjectMapper().readValue(s, Map.class);

            String beanName = (String) paramMap.get("bean");
            String methodName = (String) paramMap.get("method");
            List<String> args = (List<String>) paramMap.getOrDefault("args", new ArrayList<>());

            Object bean = SpringContextHolder.getBean(beanName);
            if (bean == null) {
                throw new IllegalArgumentException("找不到Bean: " + beanName);
            }

            Method targetMethod = Arrays.stream(bean.getClass().getMethods())
                    .filter(m -> m.getName().equals(methodName))
                    .filter(m -> m.getParameterCount() == args.size())
                    .findFirst()
                    .orElseThrow(() -> new NoSuchMethodException("找不到方法：" + methodName));

            // 转换参数类型
            Class<?>[] paramTypes = targetMethod.getParameterTypes();
            Object[] realArgs = new Object[paramTypes.length];

            for (int i = 0; i < paramTypes.length; i++) {
                Class<?> type = paramTypes[i];
                String argStr = String.valueOf(args.get(i));
                realArgs[i] = convert(argStr, type);
            }

            targetMethod.invoke(bean, realArgs);
            log.info("方法执行完成");
            return ReturnT.SUCCESS;
        }catch (Exception e){
            log.error("动态job执行器执行失败",e);
            return ReturnT.FAIL;
        }

    }


    private Object convert(String value, Class<?> type) {
        if (type == String.class) {
            return value;
        } else if (type == int.class || type == Integer.class) {
            return Integer.parseInt(value);
        } else if (type == long.class || type == Long.class) {
            return Long.parseLong(value);
        } else if (type == boolean.class || type == Boolean.class) {
            return Boolean.parseBoolean(value);
        } else if (type == double.class || type == Double.class) {
            return Double.parseDouble(value);
        } else {
            throw new IllegalArgumentException("不支持的参数类型：" + type.getName());
        }
    }
}
