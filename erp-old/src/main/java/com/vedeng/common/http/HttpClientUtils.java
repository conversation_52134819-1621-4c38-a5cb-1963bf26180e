package com.vedeng.common.http;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.fasterxml.jackson.core.type.TypeReference;
import com.vedeng.common.key.CryptBase64Tool;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.model.ResultInfo4Op;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.core.trace.constant.MdcConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.http.Consts;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 除了调用dbcenter,其他地方不要用这个，默认废弃
 */
@Deprecated
@Slf4j
public class HttpClientUtils {

	private static Logger logger = LoggerFactory.getLogger(HttpClientUtils.class);
	// 设置连接超时时间，单位毫秒。
	private static final int CONNECT_TIMEOUT = 60000;

	// 请求获取数据的超时时间(即响应时间)，单位毫秒。
	private static final int SOCKET_TIMEOUT = 60000;

	public static final int LOGGER_MIN_TIME=60000;

	private final static RequestConfig DEFAULT_REQUEST_CONFIG = RequestConfig.custom().setConnectTimeout(CONNECT_TIMEOUT).setSocketTimeout(SOCKET_TIMEOUT).build();

	private final static CloseableHttpClient HTTP_CLIENT =  HttpClientBuilder.create()
			.disableCookieManagement()
			.disableRedirectHandling()
			.disableAuthCaching()
			.setConnectionTimeToLive(10, TimeUnit.MINUTES)
			.setMaxConnPerRoute(100)
			.setMaxConnTotal(100)
			.build();

	/**
	 * <b>Description:</b><br>
	 * post方式请求Api(包含分页信息)
	 * 
	 * @param url
	 * @param paraMap
	 * @param type
	 * @return
	 * @throws IOException
	 * @Note <b>Author:</b> duke <br>
	 *       <b>Date:</b> 2017年5月8日 下午6:45:27
	 */
	public static Object post(String url, Object paraMap, String clientId, String clientKey, TypeReference<?> type,
							  Page page) throws IOException {

		// 序列化成 json
		String data = JSONObject.toJSONString(paraMap);
		logger.info("http to dbcenter url: {} and param: {} " , url , data);
		// 准备发送的参数
		Map<String, String> paramMap = new HashMap<String, String>();
		paramMap.put("systime", System.currentTimeMillis() + "");
		paramMap.put("data", data);
		paramMap.put("clientId", clientId);
		paramMap.put("clientKey", clientKey);
		// 分页信息
		String pageJson = JsonUtils.translateToJson(page == null ? "" : page);
		paramMap.put("page", pageJson);
		paramMap.put(MdcConstant.TRANCE_ID, getMDC());
		// http post 参数
		List<NameValuePair> params = new ArrayList<NameValuePair>();
		Iterator<Map.Entry<String, String>> $it = paramMap.entrySet().iterator();
		while ($it.hasNext()) {
			Map.Entry<String, String> entry = $it.next();
			params.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
		}
		// http post 请求
		HttpPost httpPost = new HttpPost(url);
		httpPost.setConfig(DEFAULT_REQUEST_CONFIG);
		httpPost.setEntity(new UrlEncodedFormEntity(params, Consts.UTF_8));

		long start=System.currentTimeMillis();
		Transaction t = Cat.newTransaction("HTTP:POST", url);

		try {
			CloseableHttpResponse response = HTTP_CLIENT.execute(httpPost);
			t.setSuccessStatus();
			if(System.currentTimeMillis()-start>LOGGER_MIN_TIME && url.indexOf("getsalesordergoodslist")<0 ){
				logger.error("大于20秒的dbcenter请求"+url+"\t"+paraMap);
			}
			int status = response.getStatusLine().getStatusCode();
			//logger.info(status+"end get dbcenter data:" + url+data+status);
			// logger.debug("Http Post Status:" + status);

			if (status != 200) {
				logger.error("Http Post Status:" +url+ status+EntityUtils.toString(response.getEntity(), Consts.UTF_8));
				return null;
			}
			try {
				// 返回结果 格式转换
				String result = EntityUtils.toString(response.getEntity(), Consts.UTF_8);

				if (StringUtils.isNotBlank(result)) {
					logger.info("http to dbcenter encryption result bytes: " + result.getBytes().length);
					logger.info("http to dbcenter encryption result length: " + result.length());
				}

				//logger.info(status+"end get dbcenter data:" + url+data+"\t"+result);
				JSONObject jsStr = JSONObject.parseObject(result);
				if (jsStr.getIntValue("code") != -1 && jsStr.get("data") != null
						&& StringUtils.isNotBlank(jsStr.getString("data"))) {
					JSONObject param = JSONObject.parseObject(jsStr.getString("param"));
					String sysdate = param.getString("systime");
					String skey = param.getString("skey");
					String result_data = (CryptBase64Tool.desDecrypt(jsStr.getString("data"), skey + sysdate)).toString();
					jsStr.put("data", JSON.parse(result_data));
					jsStr.put("page", JSON.parse(param.getString("page")));
					result = jsStr.toJSONString();

					if (StringUtils.isNotBlank(result)) {
						logger.info("http to dbcenter decryption result bytes: " + result.getBytes().length);
						logger.info("http to dbcenter decryption result length: " + result.length());
					}
				}
				return JsonUtils.readValueByType(result, type);

			} catch (Exception e) {
				logger.error(status+"error get dbcenter data:" + url+data+"\t");
				return new ResultInfo<>(-1, "客户端解析数据失败");
			} finally {
				response.close();
			}
		} catch (Throwable e) {
			t.setStatus(e);
			Cat.logError(e);
			throw e;
		} finally {
			t.complete();
		}
	}

	public static Object post120(String url, Object paraMap, String clientId, String clientKey, TypeReference<?> type,
							  Page page) throws IOException {

		// 序列化成 json
		String data = JSONObject.toJSONString(paraMap);
		//logger.info("start get dbcenter data:" + url+data);
		// 准备发送的参数
		Map<String, String> paramMap = new HashMap<String, String>();
		paramMap.put("systime", System.currentTimeMillis() + "");
		paramMap.put("data", data);
		paramMap.put("clientId", clientId);
		paramMap.put("clientKey", clientKey);
		// 分页信息
		String pageJson = JsonUtils.translateToJson(page == null ? "" : page);
		paramMap.put("page", pageJson);
		paramMap.put(MdcConstant.TRANCE_ID, getMDC());
		// http post 参数
		List<NameValuePair> params = new ArrayList<NameValuePair>();
		Iterator<Map.Entry<String, String>> $it = paramMap.entrySet().iterator();
		while ($it.hasNext()) {
			Map.Entry<String, String> entry = $it.next();
			params.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
		}
		// http post 请求
		HttpPost httpPost = new HttpPost(url);
		//临时将结款调整为120秒
		RequestConfig tempConfig = RequestConfig.custom().
				 setConnectTimeout(CONNECT_TIMEOUT).setSocketTimeout(150000).build();

		httpPost.setConfig(tempConfig);
		httpPost.setEntity(new UrlEncodedFormEntity(params, Consts.UTF_8));

		long start=System.currentTimeMillis();


		Transaction t = Cat.newTransaction("HTTP:POST", url);
		try {
				CloseableHttpResponse response = HTTP_CLIENT.execute(httpPost);
				if(System.currentTimeMillis()-start>LOGGER_MIN_TIME && url.indexOf("getsalesordergoodslist")<0){
					logger.error("大于20秒的dbcenter请求"+url+"\t"+paraMap);
				}
				int status = response.getStatusLine().getStatusCode();
				//logger.info(status+"end get dbcenter data:" + url+data+status);
				// logger.debug("Http Post Status:" + status);

				if (status != 200) {
					logger.error("Http Post Status:" +url+ status+EntityUtils.toString(response.getEntity(), Consts.UTF_8));
					return null;
				}
				try {
					// 返回结果 格式转换
					String result = EntityUtils.toString(response.getEntity(), Consts.UTF_8);
					//logger.info(status+"end get dbcenter data:" + url+data+"\t"+result);
					JSONObject jsStr = JSONObject.parseObject(result);
					if (jsStr.getIntValue("code") != -1 && jsStr.get("data") != null
							&& StringUtils.isNotBlank(jsStr.getString("data"))) {
						JSONObject param = JSONObject.parseObject(jsStr.getString("param"));
						String sysdate = param.getString("systime");
						String skey = param.getString("skey");
						String result_data = (CryptBase64Tool.desDecrypt(jsStr.getString("data"), skey + sysdate)).toString();
						jsStr.put("data", JSON.parse(result_data));
						jsStr.put("page", JSON.parse(param.getString("page")));
						result = jsStr.toJSONString();
					}
					return JsonUtils.readValueByType(result, type);

				} catch (Exception e) {
					logger.error(status+"error get dbcenter data:" + url+data+"\t");
					return new ResultInfo<>(-1, "客户端解析数据失败");
				} finally {
					response.close();
				}
		} catch (Throwable e) {
			t.setStatus(e);
			Cat.logError(e);
			throw e;
		} finally {
			t.complete();
		}
	}

	/**
	 * <b>Description:</b><br>
	 * post方式请求Api
	 *
	 * @param url
	 * @param paraMap
	 * @param type
	 * @return
	 * @throws IOException
	 * @Note <b>Author:</b> duke <br>
	 *       <b>Date:</b> 2017年5月8日 下午6:45:27
	 */
	public static Object post(String url, Object paraMap, String clientId, String clientKey, TypeReference<?> type)
			throws IOException {

		// 序列化成 json
		String data = JSONObject.toJSONString(paraMap);
		// 准备发送的参数
		Map<String, String> paramMap = new HashMap<String, String>();

		paramMap.put("systime", System.currentTimeMillis() + "");
		paramMap.put("data", data);

		paramMap.put("clientId", clientId);
		paramMap.put("clientKey", clientKey);
		paramMap.put(MdcConstant.TRANCE_ID, getMDC());
		// http post 参数
		List<NameValuePair> params = new ArrayList<NameValuePair>();
		Iterator<Map.Entry<String, String>> $it = paramMap.entrySet().iterator();
		while ($it.hasNext()) {
			Map.Entry<String, String> entry = $it.next();
			params.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
		}

		// logger.debug("Http Post Para:" + paramMap);

		// http post 请求

		HttpPost httpPost = new HttpPost(url);
		httpPost.setConfig(DEFAULT_REQUEST_CONFIG);
		httpPost.setEntity(new UrlEncodedFormEntity(params, Consts.UTF_8));
		long start=System.currentTimeMillis();

		Transaction t = Cat.newTransaction("HTTP:POST", url);
		try {
			CloseableHttpResponse response = HTTP_CLIENT.execute(httpPost);
			t.setSuccessStatus();
			if(System.currentTimeMillis()-start>LOGGER_MIN_TIME && url.indexOf("getsalesordergoodslist")<0){
				logger.error("大于20秒的dbcenter请求"+url+"\t"+paraMap);
			}
			int status = response.getStatusLine().getStatusCode();

			// logger.debug("Http Post Status:" + status);

			if (status != 200) {
				logger.error("Http Post Status:500 error ，紧急重要，一定要处理:" +url+ status+EntityUtils.toString(response.getEntity(), Consts.UTF_8));
				return null;
			}
			String result="";
			try {
				// 返回结果 格式转换
				result = EntityUtils.toString(response.getEntity(), Consts.UTF_8);

				// logger.debug("Http Post Result:" + result);

				JSONObject jsStr = JSONObject.parseObject(result);
				if (null!=jsStr&&jsStr.getIntValue("code") != -1 && jsStr.get("data") != null
						&& StringUtils.isNotBlank(jsStr.getString("data"))) {
					JSONObject param = JSONObject.parseObject(jsStr.getString("param"));
					if(param!=null){
						String sysdate = param.getString("systime");
						String skey = param.getString("skey");// String random =
						// param.getString("random");
						String result_data = (CryptBase64Tool.desDecrypt(jsStr.getString("data"), skey + sysdate)).toString();
						jsStr.put("data", JSON.parse(result_data));
						result = jsStr.toJSONString();
					}
				}
				return JsonUtils.readValueByType(result, type);
			} catch (Exception e) {
				logger.error("Http Post Status:解析数据失败！"+url+data+result,e);
				return new ResultInfo<>(-1, "客户端解析数据失败");
			} finally {
				response.close();
			}
		} catch (Throwable e) {
			t.setStatus(e);
			Cat.logError(e);
			throw e;
		} finally {
			t.complete();
		}
	}
	public static Object postTimeOut5Min(String url, Object paraMap, String clientId, String clientKey, TypeReference<?> type)
			throws Exception {

		// 序列化成 json
		String data = JSONObject.toJSONString(paraMap);
		// 准备发送的参数
		Map<String, String> paramMap = new HashMap<String, String>();

		paramMap.put("systime", System.currentTimeMillis() + "");
		paramMap.put("data", data);

		paramMap.put("clientId", clientId);
		paramMap.put("clientKey", clientKey);
		paramMap.put(MdcConstant.TRANCE_ID, getMDC());
		// http post 参数
		List<NameValuePair> params = new ArrayList<NameValuePair>();
		Iterator<Map.Entry<String, String>> $it = paramMap.entrySet().iterator();
		while ($it.hasNext()) {
			Map.Entry<String, String> entry = $it.next();
			params.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
		}
		HttpPost httpPost = new HttpPost(url);
		RequestConfig config=RequestConfig.custom().setConnectTimeout(CONNECT_TIMEOUT*5).setSocketTimeout(SOCKET_TIMEOUT*5).build();
		httpPost.setConfig(config);
		httpPost.setEntity(new UrlEncodedFormEntity(params, Consts.UTF_8));
		Transaction t = Cat.newTransaction("HTTP:POST", url);

		try {
			CloseableHttpResponse response = HTTP_CLIENT.execute(httpPost);
			t.setSuccessStatus();
			String result="";
			try {
				// 返回结果 格式转换
				result = EntityUtils.toString(response.getEntity(), Consts.UTF_8);
				JSONObject jsStr = JSONObject.parseObject(result);
				if (null!=jsStr&&jsStr.getIntValue("code") != -1 && jsStr.get("data") != null
						&& StringUtils.isNotBlank(jsStr.getString("data"))) {
					JSONObject param = JSONObject.parseObject(jsStr.getString("param"));
					String sysdate = param.getString("systime");
					String skey = param.getString("skey");// String random =
					String result_data = (CryptBase64Tool.desDecrypt(jsStr.getString("data"), skey + sysdate)).toString();
					jsStr.put("data", JSON.parse(result_data));
					result = jsStr.toJSONString();
				}

				return JsonUtils.readValueByType(result, type);
			} catch (Exception e) {
				logger.error("Http Post Status:解析数据失败！"+url+data+result,e);
				throw e;
			} finally {
				response.close();
			}
		} catch (Throwable e) {
			t.setStatus(e);
			Cat.logError(e);
			throw e;
		} finally {
			t.complete();
		}
	}
	/**
	 * 仅针对OP
	 * @param url
	 * @param paraMap
	 * @return
	 * @throws IOException
	 */
	public static void postOp(String url, Map<String, String> paraMap )
			throws IOException {
		final TypeReference<ResultInfo4Op> TypeRef4Save = new TypeReference<ResultInfo4Op>() {
		};
		// 序列化成 json
		String data = JSONObject.toJSONString(paraMap);
		// 准备发送的参数
		Map<String, String> paramMap = new HashMap<String, String>();

		paramMap.put("systime", System.currentTimeMillis() + "");
		paramMap.put("data", data);
		paramMap.put(MdcConstant.TRANCE_ID, getMDC());
//		paramMap.put("clientId", clientId);
//		paramMap.put("clientKey", clientKey);

		paramMap.putAll(paraMap);

		// http post 参数
		List<NameValuePair> params = new ArrayList<NameValuePair>();
		Iterator<Map.Entry<String, String>> $it = paramMap.entrySet().iterator();
		while ($it.hasNext()) {
			Map.Entry<String, String> entry = $it.next();
			params.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
		}
		// http post 请求
		HttpPost httpPost = new HttpPost(url);
		httpPost.setConfig(DEFAULT_REQUEST_CONFIG);
		httpPost.setEntity(new UrlEncodedFormEntity(params, Consts.UTF_8));
		long start=System.currentTimeMillis();
		Transaction t = Cat.newTransaction("HTTP:POST", url);

		try {
			CloseableHttpResponse response = HTTP_CLIENT.execute(httpPost);
			t.setSuccessStatus();
			if(System.currentTimeMillis()-start>LOGGER_MIN_TIME && url.indexOf("getsalesordergoodslist")<0){
				logger.error("大于20秒的dbcenter请求"+url+"\t"+paraMap);
			}
			int status = response.getStatusLine().getStatusCode();

			// logger.debug("Http Post Status:" + status);

			if (status != 200) {
				response.close();
				logger.error("Http Post Status:500，error 紧急重要，一定要处理:" +url+ status+EntityUtils.toString(response.getEntity(), Consts.UTF_8));
				throw  new RuntimeException("请求报错："+"Http Post Status:500，error 紧急重要，一定要处理:" +url+ status+EntityUtils.toString(response.getEntity(), Consts.UTF_8));
			}

			try {
				// 返回结果 格式转换
				String result = EntityUtils.toString(response.getEntity(), Consts.UTF_8);
	//				if (jsStr.get("code").toString().equals("unknown_exception")) {
	//					logger.error("请求报错："+url+JsonUtils.translateToJson(paramMap)+",result="+result);
	//					 throw  new RuntimeException("请求报错："+url+JsonUtils.translateToJson(paramMap)+",result="+result);
	//				}
					ResultInfo4Op resultInfo4Op=JsonUtils.readValueByType(result, TypeRef4Save);
					if(!resultInfo4Op.isSuccess()){
						throw  new RuntimeException("请求报错："+url+JsonUtils.translateToJson(paramMap)+",result="+result);
					}
	//			if (jsStr.getIntValue("code") != -1 && jsStr.get("data") != null
	//					&& StringUtils.isNotBlank(jsStr.getString("data"))) {
	//
	//				JSONObject param = JSONObject.parseObject(jsStr.getString("param"));
	//
	//				String sysdate = param.getString("systime");
	//				String skey = param.getString("skey");// String random =
	//				// param.getString("random");
	//				String result_data = (CryptBase64Tool.desDecrypt(jsStr.getString("data"), skey + sysdate)).toString();
	//				/*
	//				 * String result_data = ""; if (!logger.isDebugEnabled()){//非debug模式--进行解密
	//				 * result_data = (CryptBase64Tool.desDecrypt(jsStr.getString("data"), skey +
	//				 * sysdate)).toString(); }else{ result_data = jsStr.getString("data"); }
	//				 */
	//				// jsStr.fluentPut("data", JSONObject.parseObject(result_data));
	//				jsStr.put("data", JSON.parse(result_data));
	//				result = jsStr.toJSONString();
	//			}
	//
	//			return JsonUtils.readValueByType(result, type);
			} catch (Exception e) {
				//logger.warn("Http Post Status:解析数据失败！",e);
				throw  new RuntimeException(e);
				//return "-1";
			} finally {
				response.close();
			}
		} catch (Throwable e) {
			t.setStatus(e);
			Cat.logError(e);
			throw e;
		} finally {
			t.complete();
		}
	}

	public static Object postTemp(String url, Object paraMap, String clientId, String clientKey, TypeReference<?> type)
			throws IOException {

		// 序列化成 json
		String data = JSONObject.toJSONString(paraMap);
		// 准备发送的参数
		Map<String, String> paramMap = new HashMap<String, String>();

		paramMap.put("systime", System.currentTimeMillis() + "");
		paramMap.put("data", data);

		paramMap.put("clientId", clientId);
		paramMap.put("clientKey", clientKey);
		paramMap.put(MdcConstant.TRANCE_ID, getMDC());
		// http post 参数
		List<NameValuePair> params = new ArrayList<NameValuePair>();
		Iterator<Map.Entry<String, String>> $it = paramMap.entrySet().iterator();
		while ($it.hasNext()) {
			Map.Entry<String, String> entry = $it.next();
			params.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
		}

		// logger.debug("Http Post Para:" + paramMap);

		// http post 请求

		Transaction t = Cat.newTransaction("HTTP:POST", url);

		try {
			HttpPost httpPost = new HttpPost(url);
			t.setSuccessStatus();
			httpPost.setConfig(DEFAULT_REQUEST_CONFIG);
			httpPost.setEntity(new UrlEncodedFormEntity(params, Consts.UTF_8));
			long start=System.currentTimeMillis();
			CloseableHttpResponse response = HTTP_CLIENT.execute(httpPost);
			if(System.currentTimeMillis()-start>LOGGER_MIN_TIME && url.indexOf("getsalesordergoodslist")<0){
				logger.error("大于20秒的dbcenter请求"+url+"\t"+paraMap);
			}
			int status = response.getStatusLine().getStatusCode();

			// logger.debug("Http Post Status:" + status);

			if (status != 200) {
				response.close();
				logger.error("Http Post Status:500， error 紧急重要，一定要处理:" +url+ status+EntityUtils.toString(response.getEntity(), Consts.UTF_8));
				return null;
			}

			try {
				// 返回结果 格式转换
				String result = EntityUtils.toString(response.getEntity(), Consts.UTF_8);

				// logger.debug("Http Post Result:" + result);

				JSONObject jsStr = JSONObject.parseObject(result);
				if (null != jsStr && jsStr.getIntValue("code") != -1 && jsStr.get("data") != null
						&& StringUtils.isNotBlank(jsStr.getString("data"))) {

					JSONObject param = JSONObject.parseObject(jsStr.getString("param"));

					String sysdate = param.getString("systime");
					String skey = param.getString("skey");// String random =
					// param.getString("random");
					String result_data = (CryptBase64Tool.desDecrypt(jsStr.getString("data"), skey + sysdate)).toString();
					/*
					 * String result_data = ""; if (!logger.isDebugEnabled()){//非debug模式--进行解密
					 * result_data = (CryptBase64Tool.desDecrypt(jsStr.getString("data"), skey +
					 * sysdate)).toString(); }else{ result_data = jsStr.getString("data"); }
					 */
					// jsStr.fluentPut("data", JSONObject.parseObject(result_data));
					jsStr.put("data", JSON.parse(result_data));
					result = jsStr.toJSONString();
				}

				return JsonUtils.readValueByType(result, type);
			} catch (Exception e) {
				logger.error("Http Post Status:解析数据失败！",e);
				return new ResultInfo<>(-1, "客户端解析数据失败");
			} finally {
				response.close();
			}
		} catch (Throwable e) {
			t.setStatus(e);
			Cat.logError(e);
			throw e;
		} finally {
			t.complete();
		}
	}

//	/**
//	 * <b>Description:</b><br>
//	 * 压缩post方式请求Api
//	 *
//	 * @param url
//	 * @param paraMap
//	 * @param type
//	 * @return
//	 * @throws IOException
//	 * @Note <b>Author:</b> duke <br>
//	 *       <b>Date:</b> 2017年5月8日 下午6:45:27
//	 */
//	public static Object compressPost(String url, Object paraMap, String clientId, String clientKey,
//			TypeReference<?> type) throws IOException {
//
//		logger.debug("Http Post Url:" + url);
//
//		// 序列化成 json
//		String data = JSONObject.toJSONString(paraMap);
//		// 准备发送的参数
//		Map<String, String> paramMap = new HashMap<String, String>();
//
//		paramMap.put("systime", System.currentTimeMillis() + "");
//		paramMap.put("data", data);
//
//		paramMap.put("clientId", clientId);
//		paramMap.put("clientKey", clientKey);
//
//		// http post 参数
//		List<NameValuePair> params = new ArrayList<NameValuePair>();
//		Iterator<Map.Entry<String, String>> $it = paramMap.entrySet().iterator();
//		while ($it.hasNext()) {
//			Map.Entry<String, String> entry = $it.next();
//			params.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
//		}
//
//		// logger.debug("Http Post Para:" + paramMap);
//
//		// http post 请求
//		CloseableHttpClient httpclient = HttpClients.createDefault();
//		HttpPost httpPost = new HttpPost(url);
//		httpPost.setEntity(new UrlEncodedFormEntity(params, Consts.UTF_8));
//		CloseableHttpResponse response = httpclient.execute(httpPost);
//
//		int status = response.getStatusLine().getStatusCode();
//
//		// logger.debug("Http Post Status:" + status);
//
//		if (status != 200) {
//			logger.warn("Http Post Status:" +url+ status+EntityUtils.toString(response.getEntity(), Consts.UTF_8));
//			return null;
//		}
//
//		try {
//			// 返回结果 格式转换
//			String result = EntityUtils.toString(response.getEntity(), Consts.UTF_8);
//
//			// logger.debug("Http Post Result:" + result);
//
//			JSONObject jsStr = JSONObject.parseObject(result);
//			if (jsStr.getIntValue("code") != -1 && jsStr.get("data") != null
//					&& StringUtils.isNotBlank(jsStr.getString("data"))) {
//
//				JSONObject param = JSONObject.parseObject(jsStr.getString("param"));
//
//				String sysdate = param.getString("systime");
//				String skey = param.getString("skey");// String random =
//														// param.getString("random");
//				String result_data = (CryptBase64Tool.desDecrypt(jsStr.getString("data"), skey + sysdate)).toString();
//				/*
//				 * String result_data = ""; if (!logger.isDebugEnabled()){//非debug模式--进行解密
//				 * result_data = (CryptBase64Tool.desDecrypt(jsStr.getString("data"), skey +
//				 * sysdate)).toString(); }else{ result_data = jsStr.getString("data"); }
//				 */
//				// jsStr.fluentPut("data", JSONObject.parseObject(result_data));
//				jsStr.put("data", result_data);
//				result = jsStr.toJSONString();
//			}
//
//			return result;
//		} catch (Exception e) {
//			logger.error("Http Post Status:解析数据失败！");
//			return new ResultInfo<>(-1, "客户端解析数据失败");
//		} finally {
//			response.close();
//		}
//	}
	@Deprecated
	public static Object getForYxg(String url, Object paraMap, String clientId, String clientKey, TypeReference<?> type)
			throws IOException {

		logger.debug("Http Get Url:" + url);

		// 序列化成 json
		String data = JSONObject.toJSONString(paraMap);
		// 准备发送的参数
		Map<String, String> paramMap = new HashMap<String, String>();

		paramMap.put("timestamp", System.currentTimeMillis() + "");
		paramMap.put("data", data);

		paramMap.put("clientId", clientId);
		paramMap.put("clientKey", clientKey);
		// http post 参数
		List<NameValuePair> params = new ArrayList<NameValuePair>();
		Iterator<Map.Entry<String, String>> $it = paramMap.entrySet().iterator();
		while ($it.hasNext()) {
			Map.Entry<String, String> entry = $it.next();
			// params.add(new BasicNameValuePair(entry.getKey(),
			// entry.getValue()));
			params.add(new BasicNameValuePair(entry.getKey(),
					new String(entry.getValue().getBytes("ISO-8859-1"), "UTF-8")));
		}

		// http post 请求
		Transaction t = Cat.newTransaction("HTTP:GET", url);

		try {
			HttpGet httpGet = new HttpGet(url);
			CloseableHttpResponse response = HTTP_CLIENT.execute(httpGet);
			t.setSuccessStatus();

			int status = response.getStatusLine().getStatusCode();

			if (status != 200) {
				return null;
			}

			try {
				// 返回结果 格式转换
				String result = EntityUtils.toString(response.getEntity(), Consts.UTF_8);
				logger.debug("Http Get Result:" + result);

				JSONObject jsStr = JSONObject.parseObject(result);
				if (jsStr.getIntValue("code") != -1) {
					result = jsStr.toJSONString();
				}
				return JsonUtils.readValueByType(result, type);
			} catch (Exception e) {
				logger.debug("Http Post Status:解析数据失败！");
				e.printStackTrace();
				return new ResultInfo<>(-1, "客户端解析数据失败");
			} finally {
				response.close();
			}
		} catch (Throwable e) {
			t.setStatus(e);
			Cat.logError(e);
			throw e;
		} finally {
			t.complete();
		}
	}

	/**
	 *
	 * <b>Description:PUT请求</b>
	 *
	 * @param url
	 * @param paraMap
	 * @param type
	 * @return
	 * @throws IOException
	 *             Object
	 * @Note <b>Author：</b> cooper.xu <b>Date:</b> 2018年11月23日 上午11:08:08
	 */
	@Deprecated
	public static Object put(String url, Object paraMap, Map<String, String> headers, TypeReference<?> type)
			throws IOException {
		logger.debug("Http Post Url:" + url);
		StringEntity stringEntity = new StringEntity((String) paraMap, "UTF-8");
		HttpPut request = new HttpPut(url);
		request.setHeader("Content-Type", "application/json");
		if (null != headers) {
			for (Map.Entry<String, String> entry : headers.entrySet()) {
				if (null == entry || null == entry.getKey()) {
					continue;
				}
				String key = entry.getKey();

				if (null == key) {
					continue;
				}

				String value = entry.getValue();
				value = null == value ? "" : value;
				// 避免被覆盖
				if ("Accept".equals(key)) {
					value = "application/json;" + value;
				} else if ("Content-Type".equals(key)) {
					value = "application/json;" + value;
				}

				request.setHeader(key, value);
			}
			request.setEntity(stringEntity);
		}
		// http post 请求
		Transaction t = Cat.newTransaction("HTTP:PUT", url);
		try {
			CloseableHttpResponse response = HTTP_CLIENT.execute(request);
			t.setSuccessStatus();
			int status = response.getStatusLine().getStatusCode();
			if (status != 200) {
				return null;
			}
			try {
				// 返回结果 格式转换
				String result = EntityUtils.toString(response.getEntity(), Consts.UTF_8);
				return JsonUtils.readValueByType(result, type);
			} catch (Exception e) {
				logger.error("Http Post Status:解析数据失败！",e);
				return new ResultInfo<>(-1, "客户端解析数据失败");
			} finally {
				response.close();
			}
		} catch (Throwable e) {
			t.setStatus(e);
			Cat.logError(e);
			throw e;
		} finally {
			t.complete();
		}
	}

	/**
	 * <b>Description:</b><br>
	 * post方式请求Api(包含分页信息)
	 *
	 * @param url
	 * @return
	 * @throws IOException
	 * @Note <b>Author:</b> duke <br>
	 *       <b>Date:</b> 2017年5月8日 下午6:45:27
	 */
	@Deprecated
	public static String postJSON(String url, String json) throws IOException {
		// http post 请求
		HttpPost httpPost = new HttpPost(url);
		httpPost.setHeader("Content-Type", "application/json;charset=UTF-8");
		StringEntity s = new StringEntity(json.toString(), "UTF-8");
		s.setContentEncoding("UTF-8");
		s.setContentType("application/json");// 发送json数据需要设置contentType
		httpPost.setEntity(s);

		Transaction t = Cat.newTransaction("HTTP:POST", url);
		try {
			CloseableHttpResponse response = HTTP_CLIENT.execute(httpPost);
			t.setSuccessStatus();
			try {
				// 返回结果 格式转换
				String result = EntityUtils.toString(response.getEntity(), Consts.UTF_8);
				return result;

			} catch (Exception e) {
				logger.error("HpostJSON:" +url,e);
			} finally {
				response.close();
			}
			return null;
		} catch (Throwable e) {
			t.setStatus(e);
			Cat.logError(e);
			throw e;
		} finally {
			t.complete();
		}

	}

	private static String getMDC(){
		String traceId = MDC.get("traceId");
		return StringUtils.isBlank(traceId) ? "" : traceId;
	}
}
