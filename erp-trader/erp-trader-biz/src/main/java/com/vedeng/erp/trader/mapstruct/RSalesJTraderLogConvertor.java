package com.vedeng.erp.trader.mapstruct;

import com.vedeng.erp.trader.domain.entity.RSalesJTraderEntity;
import com.vedeng.erp.trader.domain.entity.RSalesJTraderLogEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.trader.mapstruct
 * @Date 2024/1/4 16:07
 */
@Mapper(componentModel = "spring")
public interface RSalesJTraderLogConvertor {

    @Mapping(target = "id", source = "id", ignore = true)
    RSalesJTraderLogEntity to(RSalesJTraderEntity entity);

}
