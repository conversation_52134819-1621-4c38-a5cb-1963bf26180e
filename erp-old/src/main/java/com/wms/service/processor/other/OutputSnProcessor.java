package com.wms.service.processor.other;

import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesGoods;
import com.vedeng.common.util.DateUtil;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.constant.WmsOperateType;
import com.wms.dao.WmsOutputOrderGoodsMapper;
import com.wms.dao.WmsOutputOrderMapper;
import com.wms.dao.WmsOutputSnCodeMapper;
import com.wms.dto.OutputSnDetailDto;
import com.wms.dto.OutputSnDto;
import com.wms.model.WmsOutSnCodeOrder;
import com.wms.model.po.WmsOutputOrder;
import com.wms.model.po.WmsOutputOrderGoods;
import com.wms.service.util.WmsCommonUtil;
import com.wms.service.processor.AbstractWMSCalllBackProcessor;
import com.wms.unitconversionorder.dao.WmsUnitConversionOrderMapper;
import com.wms.unitconversionorder.model.entity.WmsUnitConversionOrder;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 出库厂家SN码回传处理
 * @date 2020/11/1610:37
 */
@Service
public class OutputSnProcessor extends AbstractWMSCalllBackProcessor<OutputSnDto> {

    private static final Logger logger = LoggerFactory.getLogger(OutputSnProcessor.class);

    @Autowired
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Autowired
    private SaleorderMapper saleorderMapper;

    @Autowired
    private AfterSalesMapper afterSalesMapper;

    @Autowired
    private AfterSalesGoodsMapper afterSalesGoodsMapper;

    @Autowired
    private WmsOutputSnCodeMapper wmsOutputSnCodeMapper;

    @Autowired
    private WmsOutputOrderMapper wmsOutputOrderMapper;

    @Autowired
    private WmsOutputOrderGoodsMapper wmsOutputOrderGoodsMapper;

    @Autowired
    private WmsUnitConversionOrderMapper wmsUnitConversionOrderMapper;

    @Override
    protected String getBusinessKey(OutputSnDto outputSnDto) {
        return "SN_OUT" + outputSnDto.getOrderNo();
    }

    @Override
    protected boolean needIdempotentValidator() {
        return false;
    }

    @Override
    protected void commonValidator(OutputSnDto outputSnDto) throws Exception {
        if (outputSnDto == null || CollectionUtils.isEmpty(outputSnDto.getDetails())) {
            logger.error("出库厂家SN码回传单信息异常" + outputSnDto.toString());
            throw new Exception("出库厂家SN码回传单信息异常" + outputSnDto.toString());
        }


    }

    public boolean validateNum(OutputSnDto outputSnDto,Integer skuId) throws Exception{
        //校验回传产品数量
        int operateType = this.getOperateType(outputSnDto);
        String orderNo = WmsCommonUtil.getOriginalOrderNo(outputSnDto.getSoReference1());
        Integer outputNum = wmsOutputSnCodeMapper.getSnNum(operateType,orderNo,skuId);
        Integer num = 0;
        if (WmsOperateType.BUYORDER_WAREHOUSE_OUT.equals(operateType) ||
                WmsOperateType.ORDER_WAREHOUSE_OUT.equals(operateType)){
            AfterSalesGoods afterSalesGood = afterSalesGoodsMapper.getAfterSalesGoodsBySalesNo(orderNo,skuId);
            num = afterSalesGood.getNum();
        }else if (WmsOperateType.WAREHOUSE_OUT.equals(operateType)){
            SaleorderGoods saleorderGood = saleorderGoodsMapper.getSalesGoodsBySalesNo(orderNo,skuId);
            num = saleorderGood.getNum();
        }else if (WmsOperateType.LENDOUT_WAREHOUSE_OUT.equals(operateType)){
            WmsOutputOrderGoods wmsOutputOrderGoods = wmsOutputOrderGoodsMapper.getOrderGoods(orderNo,"V"+skuId);
            num = wmsOutputOrderGoods.getOutputNum();
        }else if (WmsOperateType.CRASH_OUT.equals(operateType)){
            WmsOutputOrderGoods OutputOrderGoods = wmsOutputOrderGoodsMapper.getOrderGoods(orderNo,"V"+skuId);
            num = OutputOrderGoods.getOutputNum();
        }
        if (outputNum.equals(num)){
            return true;
        }
        return false;
    }

    @Override
    protected void doDealWithRequest(OutputSnDto outputSnDto) throws Exception {
        logger.info("出库厂家SN码 info:" + JSON.toJSONString(outputSnDto));
        List<OutputSnDetailDto> snDetailDtos = outputSnDto.getDetails();
        for (OutputSnDetailDto detail : snDetailDtos) {
            WmsOutSnCodeOrder wmsOutSnCodeOrder = new WmsOutSnCodeOrder();
            wmsOutSnCodeOrder.setSku(detail.getSku());
            wmsOutSnCodeOrder.setSerialNo(detail.getSerialNo());
            wmsOutSnCodeOrder.setSnCode(detail.getSecondSerialNo());
            int operateType = this.getOperateType(outputSnDto);
            int relateId = this.getRelateId(outputSnDto, operateType);
            int orderGoodsId = Integer.valueOf(detail.getDedi04());
            wmsOutSnCodeOrder.setOrderId(relateId);
            wmsOutSnCodeOrder.setOperateType(operateType);
            wmsOutSnCodeOrder.setOrderGoodsId(orderGoodsId);
            WmsOutSnCodeOrder order = wmsOutputSnCodeMapper.getOutputSnCode(wmsOutSnCodeOrder);
            if (null != order){
                throw new Exception("出库厂家SN码单号:" + outputSnDto.getSoReference1() + "的回传数据重复!");
            }
            WmsOutSnCodeOrder wms = new WmsOutSnCodeOrder();
            if (StringUtils.isEmpty(detail.getDedi04())) {
                throw new Exception("出库厂家SN码单号:" + outputSnDto.getSoReference1() + "的ERP订单商品ID为空!");
            }
//            //校验出库回传SN数量
//            if (validateNum(outputSnDto,Integer.valueOf(detail.getSku().substring(1)))){
//                continue;
//            }

            wms.setOrderId(relateId);
            wms.setOrderGoodsId(orderGoodsId);
            wms.setOperateType(operateType);
            wms.setWmsOrderNo(outputSnDto.getOrderNo());
            wms.setSku(detail.getSku());
            wms.setSkuId(Integer.valueOf(detail.getSku().substring(1)));
            wms.setSnCode(detail.getSecondSerialNo());
            wms.setSerialNo(detail.getSerialNo());
            wms.setOutTime(DateUtil.StringToDate(outputSnDto.getShipmentTime(), "yyyy-MM-dd HH:mm:ss"));
            if (StringUtils.isNotEmpty(detail.getLotAtt01())){
                wms.setProductDate(DateUtil.StringToDate(detail.getLotAtt01() + " 00:00:00", "yyyy-MM-dd HH:mm:ss"));
            }
            if (StringUtils.isNotEmpty(detail.getLotAtt02())){
                wms.setExpirationDate(DateUtil.StringToDate(detail.getLotAtt02() + " 00:00:00", "yyyy-MM-dd HH:mm:ss"));
            }
            wms.setBatchNumber(detail.getLotAtt04());
            wms.setVedengBatchNumer(detail.getLotAtt11());
            wms.setComments(outputSnDto.getSoReference1());
            wms.setIsDelete(0);
            wms.setAddTime(new Date());
            wms.setModeTime(new Date());
            wms.setCreator(0);
            wms.setUpdater(0);
            wmsOutputSnCodeMapper.insertSelective(wms);
        }
    }

    public int getRelateId(OutputSnDto outputSnDto,int operateType) throws Exception {
        String orderNo = WmsCommonUtil.getOriginalOrderNo(outputSnDto.getSoReference1());
       try {
           if (WmsOperateType.BUYORDER_WAREHOUSE_OUT.equals(operateType) ||
                   WmsOperateType.ORDER_WAREHOUSE_OUT.equals(operateType)) {
               //售后出库
               AfterSales afterSale = afterSalesMapper.getAfterSalesByNo(orderNo);
               return afterSale.getAfterSalesId();
           } else if (WmsOperateType.WAREHOUSE_OUT.equals(operateType)||WmsOperateType.DELIVERY_DIRECT_WAREHOUSE_OUT.equals(operateType)) {
               //销售出库
               Saleorder saleorder = saleorderMapper.getSaleOrderId(orderNo);
               return saleorder.getSaleorderId();
           }else if (WmsOperateType.SAMPLE_ORDER_OUT.equals(operateType) || WmsOperateType.LENDOUT_WAREHOUSE_OUT.equals(operateType)){
               //借货出库
               WmsOutputOrder wmsOutputOrder = wmsOutputOrderMapper.selectByOrderNo(orderNo);
               return wmsOutputOrder.getId().intValue();
           }else if (WmsOperateType.CRASH_OUT.equals(operateType)){
               //报废出库
               WmsOutputOrder wmsOutputOrder = wmsOutputOrderMapper.selectByOrderNo(orderNo);
               return wmsOutputOrder.getId().intValue();
           }else if (WmsOperateType.UNIT_CONVERSION_OUT.equals(operateType)){
               // 转换出库
               WmsUnitConversionOrder wmsUnitConversionOrder = wmsUnitConversionOrderMapper.selectByWmsUnitConversionOrderNo(orderNo);
               return wmsUnitConversionOrder.getWmsUnitConversionOrderId();
           }else {
               throw new Exception("出库厂家SN码单号:" + outputSnDto.getSoReference1() + "的订单类型异常!");
           }
       }catch (Exception e){
           throw new Exception("出库厂家SN码单号:" + outputSnDto.getSoReference1() + "的关联Relateid不存在!");
       }
    }

    public int getOperateType(OutputSnDto outputSnDto) throws Exception {
        //销售出库单
        if(WmsInterfaceOrderType.OUT_SALE_OUT.equals(outputSnDto.getOrderType())){
            return WmsOperateType.WAREHOUSE_OUT;
        }
        //销售出库单
        if(WmsInterfaceOrderType.OUT_DIRECT_SALE_OUT.equals(outputSnDto.getOrderType())){
            return WmsOperateType.DELIVERY_DIRECT_WAREHOUSE_OUT;
        }
        //销售售后出库
        else if(WmsInterfaceOrderType.EXCHANG_SALEORDER.equals(outputSnDto.getOrderType())){
            return WmsOperateType.ORDER_WAREHOUSE_OUT;
        }
        //采购售后出库
        else if (WmsInterfaceOrderType.OUT_PURCHASE_RETURN.equals(outputSnDto.getOrderType()) || WmsInterfaceOrderType.OUT_PURCHASE_EXG.equals(outputSnDto.getOrderType())){
            return WmsOperateType.BUYORDER_WAREHOUSE_OUT;
        }
        //借货出库
        else if(WmsInterfaceOrderType.OUT_LENDOUT.equals(outputSnDto.getOrderType())){
            return WmsOperateType.LENDOUT_WAREHOUSE_OUT;
        }
        //报废出库
        else if (WmsInterfaceOrderType.OUT_CRASH.equals(outputSnDto.getOrderType())){
            return WmsOperateType.CRASH_OUT;
        }
        //调拔出库
        else if (WmsInterfaceOrderType.TRANSFER_OUT.equals(outputSnDto.getOrderType())){
            return WmsOperateType.TRANSFER_OUT;
        }
        //样品出库
        else if (WmsInterfaceOrderType.OUT_SAMPLE.equals(outputSnDto.getOrderType())){
            return WmsOperateType.SAMPLE_OUT;
        }
        //领用出库
        else if (WmsInterfaceOrderType.OUT_RECEIVER.equals(outputSnDto.getOrderType())){
            return WmsOperateType.RECEIVER_OUT;
        }else if (WmsInterfaceOrderType.INVENTORY_OUT.equals(outputSnDto.getOrderType())){
            //  hollis 盘亏出库单
            return WmsOperateType.INVENTORY_OUT;
        }else if (WmsInterfaceOrderType.UNIT_CONVERSION_OUT.equals(outputSnDto.getOrderType())){
            //  单位转换单出库
            return WmsOperateType.UNIT_CONVERSION_OUT;
        } else if (WmsInterfaceOrderType.SAMPLE_ORDER_OUT.equals(outputSnDto.getOrderType())){
            return WmsOperateType.SAMPLE_ORDER_OUT;
        }else {
            throw new Exception("出库厂家SN码单号:" + outputSnDto.getSoReference1() + "的订单类型异常!");
        }
    }

}
