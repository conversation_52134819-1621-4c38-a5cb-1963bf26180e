package com.vedeng.goods.service;

import com.vedeng.goods.domain.dto.GoodsFinanceInfoDto;
import org.springframework.web.multipart.MultipartFile;


public interface GoodsFinanceInfoService {

    /**
     * 根据 SKU_NO 查询商品财务信息
     * @param skuNo
     * @return 商品财务信息
     */
    public GoodsFinanceInfoDto getGoodsFinanceInfoDetailBySku(String skuNo);

    /**
     * 根据主键 查询商品财务信息
     * @param goodsFinanceId
     * @return 商品财务信息
     */
    public GoodsFinanceInfoDto getGoodsFinanceInfoDetailById(Long goodsFinanceId);

    /**
     * 保存商品财务信息
     * @param financeInfoDto
     * @return
     */
    public Integer saveGoodsFinanceInfo(GoodsFinanceInfoDto financeInfoDto);

    /**
     * 导入excel数据
     * @param file excel文件
     * @throws Exception
     */
    public void importExcelFile(MultipartFile file) throws Exception;

    /**
     * 审核
     * @param goodsFinanceInfoDto
     */
    public void audit(GoodsFinanceInfoDto goodsFinanceInfoDto);

    /**
     * 审核按钮
     * @param goodsFinanceInfoDto
     */
    public void auditButton(GoodsFinanceInfoDto goodsFinanceInfoDto);
}