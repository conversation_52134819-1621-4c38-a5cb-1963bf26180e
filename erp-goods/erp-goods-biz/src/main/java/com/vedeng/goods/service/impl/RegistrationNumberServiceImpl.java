package com.vedeng.goods.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.vedeng.goods.service.RegistrationNumberApiService;
import com.vedeng.goods.common.constant.ErpGoodsConstant;
import com.vedeng.goods.dto.AttachmentRegistrationNumberDto;
import com.vedeng.goods.dto.RegistrationNumberSpuDto;
import com.vedeng.goods.query.AttachmentRegistrationNumberQuery;
import com.vedeng.goods.query.RegistrationNumberSpuQuery;
import com.vedeng.goods.mapper.CoreSpuMapper;
import com.vedeng.goods.service.RegistrationNumberService;
import com.vedeng.infrastructure.file.domain.Attachment;
import com.vedeng.infrastructure.file.mapper.AttachmentMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class RegistrationNumberServiceImpl implements RegistrationNumberService, RegistrationNumberApiService {

    @Autowired
    private AttachmentMapper attachmentMapper;
    @Autowired
    private CoreSpuMapper coreSpuMapper;

    @Override
    public List<AttachmentRegistrationNumberDto> queryAttachmentsByRegistrationNumberId(AttachmentRegistrationNumberQuery registrationNumberQuery) {

        if (Objects.isNull(registrationNumberQuery) || Objects.isNull(registrationNumberQuery.getRegistrationNumberId())) {
            return null;
        }

        registrationNumberQuery.setRegistrationNumberId(registrationNumberQuery.getRegistrationNumberId());
        registrationNumberQuery.setAttachmentFunction(ErpGoodsConstant.ATTACHMENT_FUNCTION_975);
        registrationNumberQuery.setAttachmentType(ErpGoodsConstant.ATTACHMENT_TYPE_974);
        Attachment attachmentQuery = new Attachment();
        attachmentQuery.setRelatedId(registrationNumberQuery.getRegistrationNumberId());
        attachmentQuery.setAttachmentType(registrationNumberQuery.getAttachmentType());
        attachmentQuery.setAttachmentFunction(registrationNumberQuery.getAttachmentFunction());
        List<Attachment> attachmentList = attachmentMapper.queryByRelatedIdAndAttachmentTypeAndAttachmentFunction(attachmentQuery);
        List<AttachmentRegistrationNumberDto> attachmentRegistrationNumberDtoList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(attachmentList)) {
            for (Attachment attachment : attachmentList) {
                AttachmentRegistrationNumberDto attachmentRegistrationNumberDto = new AttachmentRegistrationNumberDto();
                BeanUtil.copyProperties(attachment, attachmentRegistrationNumberDto);
                attachmentRegistrationNumberDtoList.add(attachmentRegistrationNumberDto);
            }
        }
        return attachmentRegistrationNumberDtoList;
    }

    @Override
    public RegistrationNumberSpuDto queryRegistrationNumberBySpuId(RegistrationNumberSpuQuery registrationNumberSpuQuery) {

        if (Objects.isNull(registrationNumberSpuQuery) || Objects.isNull(registrationNumberSpuQuery.getSpuId())) {
            return null;
        }
        return coreSpuMapper.queryRegistrationNumberBySpuId(registrationNumberSpuQuery.getSpuId());
    }

    /**
     * 查询注册证和生产厂家
     * @param registrationNumberSpuQuery
     * @return
     */
    @Override
    public RegistrationNumberSpuDto queryRegistraAndManufactureBySpuId(RegistrationNumberSpuQuery registrationNumberSpuQuery) {
        if (Objects.isNull(registrationNumberSpuQuery) || Objects.isNull(registrationNumberSpuQuery.getSpuId())) {
            return null;
        }
        return coreSpuMapper.queryRegistraAndManufactureBySpuId(registrationNumberSpuQuery.getSpuId());
    }
}




