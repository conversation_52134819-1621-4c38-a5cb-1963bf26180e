package com.newtask.data.saleorderdata;

import com.alibaba.fastjson.JSON;
import com.newtask.data.AbstractDataByTimeSync;
import com.newtask.data.dao.SaleorderDataMapper;
import com.newtask.data.dto.SaleoderDataDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description com.newtask.data.saleorder
 * @Date 2021/10/28 13:33
 */
@Slf4j
@Component
public class SaleorderNameConcatInfoSync extends AbstractDataByTimeSync {

    @Resource
    SaleorderDataMapper saleorderDataMapper;


    @Override
    public List<Map<String, Object>> loadBizByTimeData(Long startTime, Long endTime) {
        List<Integer> saleorderIds = saleorderDataMapper.getSaleorderIdBySaleorderGoodsModTime(startTime,endTime);
        if(CollectionUtils.isEmpty(saleorderIds)){
            return new ArrayList<>();
        }
        log.info("变更涉及销售单id{},", JSON.toJSONString(saleorderIds));
        List<Map<String,Object>> maps = saleorderDataMapper.getSaleorderConcatNameInfoByTime(saleorderIds);
        log.info("变更涉及销售单商品信息{},",JSON.toJSONString(maps));
        return maps;
    }

    @Override
    public void updateData(List<Map<String, Object>> dataList) {
        if (CollectionUtils.isNotEmpty(dataList)){
            long timeMillis = System.currentTimeMillis();
            List<SaleoderDataDto> saleoderDataDtos = new ArrayList<>();
            dataList.forEach(item->{
                SaleoderDataDto saleoderDataDto = new SaleoderDataDto();
                Integer saleorderId = Integer.parseInt(item.get("saleorderId").toString());
                makeExist(saleorderId,timeMillis);
                saleoderDataDto.setSaleorderId(saleorderId);
                saleoderDataDto.setSkuBrandNameModel(item.get("concatName").toString());
                saleoderDataDtos.add(saleoderDataDto);
            });
            saleorderDataMapper.updateSaleorderNameConcatInfo(saleoderDataDtos,timeMillis);
        }

    }

    @Override
    public void makeExist(Integer id, Long nowTime) {
        Long count = saleorderDataMapper.getSaleorderById(id);
        if (count <= 0) {
            saleorderDataMapper.insertSaleOrder(id, nowTime);
        }
    }
}



