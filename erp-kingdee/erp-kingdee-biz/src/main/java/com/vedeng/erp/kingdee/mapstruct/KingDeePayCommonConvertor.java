package com.vedeng.erp.kingdee.mapstruct;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.kingdee.domain.entity.KingDeePayCommonEntity;
import com.vedeng.erp.kingdee.dto.KingDeePayCommonDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeePayCommonDto;
import org.mapstruct.*;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 预处理金蝶费用应付单明细  贝登dto 转 金蝶 command
 * @date
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,builder=@Builder(disableBuilder = true))
public interface KingDeePayCommonConvertor extends BaseMapStruct<KingDeePayCommonEntity, KingDeePayCommonDto> {
	
	/**
     * DTO转Entity
     *
     * @param dto
     * @return
     */
    @Mapping(target = "FEntityDetail", source = "FEntityDetail", qualifiedByName = "expensesToJsonArray")
    @Mapping(target = "FBillTypeId", source = "FBillTypeID")
    @Override
    KingDeePayCommonEntity toEntity(KingDeePayCommonDto dto);

    /**
     * Entity转DTO
     *
     * @param entity
     * @return
     */
    @Mapping(target = "FEntityDetail", source = "FEntityDetail", qualifiedByName = "expensesJsonArrayToList")
    @Mapping(target = "FBillTypeID", source = "FBillTypeId")
    @Override
    KingDeePayCommonDto toDto(KingDeePayCommonEntity entity);
	
    
    /**
     * entity 中JSONArray 转 原对象
     *
     * @param payBillEntryDtos JSONArray
     * @return List<KingDeePayBillDto> dto中的对象
     */
    @Named("expensesJsonArrayToList")
    default List<KingDeePayCommonDetailDto> entryJsonArrayToList(JSONArray payBillEntryDtos) {
        if (CollUtil.isEmpty(payBillEntryDtos)) {
            return Collections.emptyList();
        }
        return payBillEntryDtos.toJavaList(KingDeePayCommonDetailDto.class);
    }

    /**
     * dto 原对象中 转 JSONArray
     *
     * @param source 对象
     * @return JSONArray JSONArray
     */
    @Named("expensesToJsonArray")
    default JSONArray entryListToJsonArray(List<KingDeePayCommonDetailDto> source) {
        if (CollUtil.isEmpty(source)) {
            return null;
        }
        return JSONArray.parseArray(JSON.toJSONString(source));
    }
    
}
