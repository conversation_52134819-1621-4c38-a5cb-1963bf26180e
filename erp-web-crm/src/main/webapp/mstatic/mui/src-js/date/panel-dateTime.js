Vue.component('ui-date-time-panel', {
    template: `
        <div class="vd-ui-datetime-panel">
            <div class="datetime-content">
                <div class="datetime-date-panel">
                    <ui-date-panel
                        :type="type"
                        :value="value"
                        @pick="pickDate"
                        :firstDayOfWeek="firstDayOfWeek"
                        :selectionMode="selectionMode"
                        v-bind="pickerOptions"
                    ></ui-date-panel>
                </div>
                <div class="datetime-time-panel">
                    <ui-time-panel
                        :type="type"
                        :value="value"
                        @pick="pickTime"
                        v-bind="pickerOptions"
                    ></ui-time-panel>
                </div>
            </div>

            <div class="datetime-btn">
                <button
                    type="button"
                    class="time-cancel"
                    @click="handleNow"
                >此时</button>
                <button
                    type="button"
                    class="time-confirm"
                    @click="handleConfirm"
                >确定</button>
            </div>
        </div>
    `,

    props: {
        type: {
            type: String,
            default: 'date'
        },
        // 此层value接收到的是上层 userInput
        value: {
            type: [Date, String]
        },
        selectionMode: String,
        firstDayOfWeek: Number,
        pickerOptions: {},
    },
    data() {
        return {
            datetime: '',
        }
    },
    watch: {
        value: {
            handler (val) {
                if (util_date.isDate(val)) {
                    this.datetime = val;
                }
                else if (typeof(val) == 'string' &&  util_date.checkToDatetime(val)) {
                    this.datetime = util_date.timeToDatetime(val);
                } else {
                    this.datetime = this.getDefaultValue();
                }
            },
            immediate: true
        },
    },
    computed: {
        date () {
            let t = new Date(this.datetime);
            let Y = t.getFullYear();
            let M = t.getMonth();
            let D = t.getDate();
            return `${ Y }-${ M }-${ D }`;
        },
        time () {
            let t = new Date(this.datetime);
            let h = t.getHours();
            let m = t.getMinutes();
            let s = t.getSeconds();
            return `${ h }-${ m }-${ s }`;
        }
    },
    created () {
    },
    mounted () {
    },
    methods:{
        getDefaultValue () {
            return this.defaultValue ? new Date(this.defaultValue) : new Date();
        },
        pickDate (val) {
            let pVal = new Date(`${ util_date.getDateStr(val) } ${ util_date.getTimeStr(this.value) }`);
            this.$emit('pick', pVal);
        },
        pickTime (val) {
            let date = util_date.getDateStr(this.value);
            !date && (date = util_date.getDateStr(new Date()));

            let pVal = new Date(`${ date } ${ util_date.getTimeStr(val) }`);
            this.$emit('pick', pVal);
        },
        handleNow () {
            let pVal = new Date();
            this.$emit('pick', pVal, true);
        },
        handleConfirm () {
            let pVal = this.datetime || null;
            this.$emit('pick', pVal, true);  // 最后一个true 标识，关闭弹窗
        }
    }
})