package com.vedeng.erp.aftersale.web.api;

import com.common.dto.StepsNodeDto;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.utils.validator.ValidatorUtils;
import com.vedeng.erp.aftersale.dto.*;
import com.vedeng.erp.aftersale.service.ExpenseAfterSalesApiService;
import com.vedeng.erp.aftersale.service.ExpenseAfterSalesService;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseItemDto;
import com.vedeng.erp.finance.dto.BankBillDto;
import com.vedeng.erp.finance.dto.CapitalBillDto;
import com.vedeng.erp.finance.dto.InvoiceDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/17 16:37
 */
@RestController
@ExceptionController
@RequestMapping("/buyorderExpense/aftersale")
public class BuyOrderExpenseAfterSaleApi {

    @Autowired
    private ExpenseAfterSalesService expenseAfterSalesService;

    @Autowired
    private ExpenseAfterSalesApiService expenseAfterSalesApiService;

    /**
     * 查询费用售后单详情页基础信息
     *
     * @param expenseAfterSalesId 费用售后单id
     * @return ExpenseAfterSalesDto
     */
    @RequestMapping("/getDetail")
    @NoNeedAccessAuthorization
    public R<?> getDetail(@RequestParam Long expenseAfterSalesId) {
        return R.success(expenseAfterSalesService.getExpenseAfterSalesDetail(expenseAfterSalesId));
    }

    /**
     * 新增/编辑费用售后单页面 回显信息查询
     *
     * @param expenseAfterSalesId 费用售后单id(只有编辑页面有)
     * @param buyorderExpenseId   费用单id
     * @return ExpenseAfterSalesDto
     */
    @RequestMapping("/getAddEditInfo")
    @NoNeedAccessAuthorization
    public R<ExpenseAfterSalesDto> getAddEditInfo(@RequestParam(required = false) Long expenseAfterSalesId, @RequestParam Integer buyorderExpenseId) {
        return R.success(expenseAfterSalesService.getAddEditExpenseAfterSalesInfo(expenseAfterSalesId, buyorderExpenseId));
    }

    /**
     * 只适用于 新增售后单页面，切换售后类型为退货退票时，动态查询出对应的 退货商品信息，保证实时计算的准确性
     *
     * @param buyorderExpenseId   费用单id
     * @param expenseAfterSalesId 费用售后单id(只有编辑页面有,新增时传入null)
     * @return List<BuyorderExpenseItemDto>
     */
    @RequestMapping("/getReturnItemList")
    @NoNeedAccessAuthorization
    public R<List<BuyorderExpenseItemDto>> getExpenseItemInfoList(@RequestParam(required = false) Long expenseAfterSalesId, @RequestParam Integer buyorderExpenseId) {
        return R.success(expenseAfterSalesService.getExpenseItemInfoForAfterSale(expenseAfterSalesId, buyorderExpenseId));
    }

    /**
     * 只适用于 新增售后单页面，切换售后类型为仅退票时，动态查询出对应的 退票信息，保证实时计算的准确性
     *
     * @param buyorderExpenseId   费用单id
     * @param expenseAfterSalesId 费用售后单id(只有编辑页面有，新增时传入null)
     * @return List<ExpenseItemAndInvoiceDto>
     */
    @RequestMapping("/getExpenseItemAndInvoiceList")
    @NoNeedAccessAuthorization
    public R<List<ExpenseItemAndInvoiceDto>> getExpenseItemAndInvoiceList(@RequestParam(required = false) Long expenseAfterSalesId, @RequestParam Integer buyorderExpenseId) {
        return R.success(expenseAfterSalesService.getExpenseItemAndInvoiceForAfterSale(expenseAfterSalesId, buyorderExpenseId));
    }

    /**
     * 保存 新增采购费用售后单信息
     *
     * @param expenseAfterSalesDto 采购费用售后单信息
     * @return 售后单id，用于页面跳转
     */
    @RequestMapping("/saveAddExpenseAfterSale")
    public R<?> saveAddExpenseAfterSale(@RequestBody ExpenseAfterSalesDto expenseAfterSalesDto) {
        return R.success(expenseAfterSalesService.saveAddExpenseAfterSale(expenseAfterSalesDto));
    }

    /**
     * 保存 编辑费用售后单
     *
     * @param expenseAfterSalesDto 采购费用售后单信息
     * @return 售后单id，用于页面跳转
     */
    @RequestMapping("/saveEditExpenseAfterSale")
    public R<?> saveEditExpenseAfterSale(@RequestBody ExpenseAfterSalesDto expenseAfterSalesDto) {
        return R.success(expenseAfterSalesService.saveEditExpenseAfterSale(expenseAfterSalesDto));
    }

    @RequestMapping("/getExpenseAfterSalesByExpenseId")
    @NoNeedAccessAuthorization
    public R<?> getExpenseAfterSalesByExpenseId(Integer buyorderExpenseId) {
        return R.success(expenseAfterSalesService.getExpenseAfterSalesBybuyorderExpenseId(buyorderExpenseId));
    }

    /**
     * 执行退款运算
     *
     * @param executeRefundDto
     * @return
     */
    @RequestMapping("/executeRefundOperation")
    public R<?> executeRefundOperation(@RequestBody ExecuteRefundDto executeRefundDto) {
        ValidatorUtils.validate(executeRefundDto);
        return R.success(expenseAfterSalesService.executeRefundOperation(executeRefundDto));
    }

    /**
     * 查询费用售后单状态 进度条
     *
     * @param expenseAfterSalesId 费用售后单id
     * @return List<StepsNodeDto>
     */
    @RequestMapping("/getStepNodeList")
    @NoNeedAccessAuthorization
    public R<List<StepsNodeDto>> getExpenseAfterSalesStepNodeList(@RequestParam Long expenseAfterSalesId) {
        return R.success(expenseAfterSalesService.getStepNodeList(expenseAfterSalesId));
    }

    /**
     * 可退票数据
     * 根据业务id获取
     *
     * @param expenseAfterSalesId 订单id
     * @return R List<InvoiceDto>
     */
    @RequestMapping("/getCanRefundInvoices")
    @NoNeedAccessAuthorization
    public R<List<RefundInvoiceRecordDto>> getCanRefundInvoices(@RequestParam Long expenseAfterSalesId) {
        return R.success(expenseAfterSalesService.getCanRefundInvoicesByRelatedId(expenseAfterSalesId));
    }

    /**
     * 无需退票
     *
     * @param returnInvoiceWriteBackDto 包含expenseAfterSalesId、invoiceNo、invoiceCode
     * @return R List<InvoiceDto>
     */
    @RequestMapping("/noNeedRefundInvoice")
    public R<?> noNeedRefundInvoice(@RequestBody ReturnInvoiceWriteBackDto returnInvoiceWriteBackDto) {
        expenseAfterSalesService.noNeedRefundInvoice(returnInvoiceWriteBackDto);
        return R.success();
    }

    /**
     * 申请冲销
     *
     * @param expenseAfterSalesId 订单id
     * @return R List<InvoiceDto>
     */
    @RequestMapping("/reversalInvoices")
    public R<?> reversalInvoices(@RequestParam Long expenseAfterSalesId, @RequestParam String invoiceCode, @RequestParam String invoiceNo) {
        expenseAfterSalesService.reversalInvoices(expenseAfterSalesId, invoiceCode, invoiceNo);
        return R.success();
    }

    /**
     * 查询费用售后单 发票记录
     *
     * @param expenseAfterSalesId 售后单id
     * @return List<InvoiceDto>
     */
    @RequestMapping("/getInvoiceList")
    @NoNeedAccessAuthorization
    public R<List<InvoiceDto>> getInvoiceList(@RequestParam Long expenseAfterSalesId) {
        return R.success(expenseAfterSalesService.getInvoiceListByAfterSalesId(expenseAfterSalesId));
    }

    /**
     * 查询退票信息的的商品信息
     *
     * @param query 退票的票号 编码 售后单号
     */
    @RequestMapping("/getReturnInvoiceGoodsData")
    @NoNeedAccessAuthorization
    public R<ReturnInvoiceDto> getReturnInvoiceGoodsData(@RequestBody ExpenseAfterSalesViewDto query) {
        return R.success(expenseAfterSalesService.getReturnInvoiceGoodsData(query));
    }

    /**
     * 关闭费用售后单
     *
     * @param expenseAfterSalesId 费用售后单id
     * @param buyorderExpenseId   费用单id
     * @return R<?>
     */
    @RequestMapping("/closeExpenseAfter")
    public R<?> closeExpenseAfter(@RequestParam Long expenseAfterSalesId, @RequestParam Integer buyorderExpenseId) {
        expenseAfterSalesService.closeExpenseAfterSales(expenseAfterSalesId, buyorderExpenseId);
        return R.success();
    }

    /**
     * 完结 采购费用售后订单
     *
     * @param expenseAfterSalesId 费用售后单id
     * @return R<?>
     */
    @RequestMapping("/completeExpenseAfterSale")
    public R<?> completeExpenseAfterSale(@RequestParam Long expenseAfterSalesId) {
        expenseAfterSalesApiService.completeExpenseAfterSales(expenseAfterSalesId);
        return R.success();
    }

    /**
     * 采购费用单模块
     * 保存退票信息
     *
     * @param data 保存到数据
     * @return 保存的票的id
     */
    @RequestMapping("/recordRefundInvoice")
    public R<Integer> recordRefundInvoice(@RequestBody ReturnInvoiceDto data) {
        return R.success(expenseAfterSalesService.recordRefundInvoice(data));
    }

    /**
     * 采购费用单模块
     * 详情页 退款信息模块
     *
     * @param expenseAfterSalesId 采购费用售后单id
     * @return ExpenseAfterSalesPaymentDto 付款信息
     */
    @RequestMapping("/getReturnPaymentData")
    @NoNeedAccessAuthorization
    public R<ExpenseAfterSalesPaymentDto> getReturnPaymentData(Long expenseAfterSalesId) {
        return R.success(expenseAfterSalesService.getReturnPaymentData(expenseAfterSalesId));
    }


    /**
     * 交易记录模块
     *
     * @param expenseAfterSalesId 费用售后单id
     */
    @RequestMapping("/getExpenseAfterSalesCapitalBill")
    @NoNeedAccessAuthorization
    public R<List<CapitalBillDto>> recordRefundInvoice(@RequestParam Long expenseAfterSalesId) {
        return R.success(expenseAfterSalesService.getExpenseAfterSalesCapitalBill(expenseAfterSalesId));
    }

    /**
     * 新增交易记录
     *
     * @param capitalBill 交易记录
     */
    @RequestMapping("/saveExpenseAfterSalesCapitalBill")
    @NoNeedAccessAuthorization
    public R<List<CapitalBillDto>> saveExpenseAfterSalesCapitalBill(@RequestBody CapitalBillDto capitalBill) {
        expenseAfterSalesService.saveExpenseAfterSalesCapitalBill(capitalBill);
        return R.success();
    }


    /**
     * 根据流水号模糊查询
     *
     * @param flowNo 流水号
     */
    @RequestMapping("/getBankBill")
    @NoNeedAccessAuthorization
    public R<List<BankBillDto>> getBankBill(@RequestParam String flowNo) {
        return R.success(expenseAfterSalesService.getBankBill(flowNo));
    }


}