package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.KingDeeInPutFeeSpecialInvoiceEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface KingDeeInPutFeeSpecialInvoiceMapper {
    /**
     * delete by primary key
     * @param inPutFeeSpecialInvoiceId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer inPutFeeSpecialInvoiceId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(KingDeeInPutFeeSpecialInvoiceEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(KingDeeInPutFeeSpecialInvoiceEntity record);

    /**
     * select by primary key
     * @param inPutFeeSpecialInvoiceId primary key
     * @return object by primary key
     */
    KingDeeInPutFeeSpecialInvoiceEntity selectByPrimaryKey(Integer inPutFeeSpecialInvoiceId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(KingDeeInPutFeeSpecialInvoiceEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(KingDeeInPutFeeSpecialInvoiceEntity record);

    int updateBatchSelective(List<KingDeeInPutFeeSpecialInvoiceEntity> list);

    int batchInsert(@Param("list") List<KingDeeInPutFeeSpecialInvoiceEntity> list);

    KingDeeInPutFeeSpecialInvoiceEntity selectByFQzokBddjtid(String FQzokBddjtid);
}