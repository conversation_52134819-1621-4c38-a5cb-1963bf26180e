package com.vedeng.erp.kingdee.dto;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 原单实体
 * @date 2022/9/6 9:03
 */
@Getter
@Setter
public class KingDeePayBillSrcEntryDto {

    /**
     * fEntryID
     */
    private String fEntryID;
    /**
     * 源单类型
     */
    private String fsourcetype;
    /**
     * 源单编号
     */
    private String fsrcbillno;
    /**
     * 计划付款金额
     */
    private String fplanpayamount;
    /**
     * 本次付款金额
     */
    private String frealpayamountS;
    /**
     * 应付金额
     */
    private String fafttaxtotalamount;
    /**
     * 结算金额
     */
    private String fsettleamount;
    /**
     * 到期日
     */
    private String fexpiry;
    /**
     * 源单内码
     */
    private String fsrcbillid;
    /**
     * 源单行内码
     */
    private String fsrcrowid;
    /**
     * 源单行号
     */
    private String fsrcseq;
    /**
     * fsrcqty
     */
    private String fsrcqty;
    /**
     * 备注
     */
    private String fsrcremark;
    /**
     * 源单币别
     */
    private String fsrccurrencyid;

    /**
     * FPAYBILLSRCENTRY_Link
     */
    private List<KingDeePayBillSrcEntryLinkDto> fpaybillsrcentryLink;
    /**
     * 默认值初始化
     */
    public KingDeePayBillSrcEntryDto(){
        this.fsourcetype = "AP_Payable";
        this.fsrcbillno = "";
        this.fplanpayamount = "";
        this.frealpayamountS = "";
        this.fafttaxtotalamount = "";
        this.fsettleamount = "";
        this.fexpiry = "";
        this.fsrcbillid = "";
        this.fsrcrowid = "";
        this.fsrcseq = "";
        this.fsrcremark = "";
        this.fsrccurrencyid = "PRE001";
    }
}
