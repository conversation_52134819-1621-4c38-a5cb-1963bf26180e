# ERP Temporal 工作流引擎模块

基于 Temporal 工作流引擎实现的 ERP 多公司业务流程自动化模块，支持数据库驱动的动态工作流配置和跨公司业务单据同步处理。

## 📋 目录

- [项目概述](#项目概述)
- [技术架构](#技术架构)
- [核心功能](#核心功能)
- [技术集成](#技术集成)
- [配置说明](#配置说明)
- [使用示例](#使用示例)
- [异常处理](#异常处理)
- [目录结构](#目录结构)

## 项目概述

### 技术目标
- **分布式工作流引擎**：基于 Temporal 提供可靠的分布式工作流解决方案
- **多公司业务自动化**：支持跨公司业务流程的自动化处理和状态同步
- **数据库驱动配置**：基于 T_FLOW_ORDER、T_FLOW_NODE 表的动态流程配置
- **Spring 4.1.9 集成**：与现有 ERP 系统（Spring 4.1.9 + XXL-Job）无缝集成

### 核心特性
- **工作流引擎**: Temporal 1.22.0 分布式工作流引擎
- **流程处理架构**: BusinessFlowProcessor 统一流程处理框架
- **多公司 API 调用**: SystemApiClient 支持动态多公司配置
- **补偿式事务**: 基于 HTTP API 调用的补偿机制
- **异常处理**: 统一的异常处理和重试策略
- **执行日志**: 完整的工作流执行日志记录
- **Java Bean 配置**: 兼容 Spring 4.1.9 的配置方式

## 技术架构

### 系统架构图

```mermaid
graph TB
    subgraph "调度层"
        XXL[XXL-Job 调度器]
        Job[MultiCompanyBusinessJob]
    end

    subgraph "工作流引擎层"
        WC[WorkflowClient]
        WF[WorkerFactory]
        Worker[Worker实例]
    end

    subgraph "业务编排层"
        MCW[MultiCompanyBusinessWorkflow<br/>主工作流]
        PSI[PurchaseSalesInventoryProcess<br/>采购销售库存流程]
        IE[InvoiceEntryProcess<br/>发票录入流程]
        PT[PaymentTransferProcess<br/>付款转账流程]
    end

    subgraph "流程处理层"
        BFP[BusinessFlowProcessor<br/>流程处理器基类]
        SOF[SalesOrderFlowProcessor<br/>销售订单流程]
        POF[PurchaseOrderFlowProcessor<br/>采购订单流程]
        IRF[InventoryReceiptFlowProcessor<br/>入库单流程]
        IEF[InvoiceEntryFlowProcessor<br/>发票录入流程]
        FC[FlowContext<br/>流程上下文]
    end

    subgraph "活动执行层"
        CBA[CompanyBusinessActivity<br/>公司业务活动]
        SPA[StatusPollingActivity<br/>状态轮询活动]
        DCA[DependencyCheckActivity<br/>依赖检查活动]
    end

    subgraph "API调用层"
        SAC[SystemApiClient<br/>系统API客户端]
        CACS[CompanyApiConfigService<br/>公司API配置服务]
    end

    subgraph "数据访问层"
        TFOM[TemporalFlowOrderMapper]
        FNCM[FlowNodeCompanyMapper]
        TBCIM[TemporalBaseCompanyInfoMapper]
    end

    subgraph "异常处理层"
        BPE[BusinessProcessException<br/>统一异常体系]
        EC[ErrorClassifier<br/>错误分类器]
        EH[ExceptionHandler<br/>异常处理工具]
        ASE[ApiStandardException]
    end

    subgraph "数据库层"
        TFO[(T_FLOW_ORDER<br/>业务流转单)]
        TFN[(T_FLOW_NODE<br/>流程节点)]
        TBCI[(T_BASE_COMPANY_INFO<br/>公司基础信息)]
    end

    XXL --> Job
    Job --> WC
    WC --> MCW
    MCW --> PSI
    MCW --> IE
    MCW --> PT
    PSI --> BFP
    IE --> BFP
    PT --> BFP
    BFP --> SOF
    BFP --> POF
    BFP --> IRF
    BFP --> IEF
    SOF --> CBA
    POF --> CBA
    IRF --> CBA
    IEF --> CBA
    CBA --> SAC
    CBA --> SPA
    CBA --> DCA
    SAC --> CACS
    CACS --> TBCIM
    SAC --> TFOM
    SAC --> FNCM
    TFOM --> TFO
    FNCM --> TFN
    WEM --> TWEL
    TBCIM --> TBCI

    classDef scheduler fill:#e1f5fe,stroke:#0277bd,stroke-width:2px
    classDef workflow fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef business fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef processor fill:#fff8e1,stroke:#ff8f00,stroke-width:2px
    classDef activity fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef api fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef data fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px
    classDef exception fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef database fill:#f9fbe7,stroke:#827717,stroke-width:2px

    class XXL,Job scheduler
    class WC,WF,Worker workflow
    class MCW,PSI,IE,PT business
    class BFP,SOF,POF,IRF,IEF,FC processor
    class CBA,SPA,DCA activity
    class SAC,CACS api
    class TFOM,FNCM,WEM,TBCIM data
    class WEH,BEH,ASE exception
    class TFO,TFN,TWEL,TBCI database
```

### 架构分层说明

#### 1. 调度层
- **XXL-Job 调度器**: 定时任务调度，触发多公司业务流程
- **MultiCompanyBusinessJob**: 多公司业务任务，负责启动 Temporal 工作流

#### 2. 工作流引擎层
- **WorkflowClient**: Temporal 客户端，管理工作流的启动和查询
- **WorkerFactory**: Worker 工厂，管理 Worker 实例的生命周期
- **Worker 实例**: 具体的工作流和活动执行器

#### 3. 业务编排层
- **MultiCompanyBusinessWorkflow**: 主工作流，协调多公司业务流程
- **PurchaseSalesInventoryProcess**: 采购-销售-库存业务流程
- **InvoiceEntryProcess**: 发票录入业务流程
- **PaymentTransferProcess**: 付款转账业务流程

#### 4. 流程处理层
- **BusinessFlowProcessor**: 流程处理器基类，提供统一的流程编排能力
- **SalesOrderFlowProcessor**: 销售订单流程处理器
- **PurchaseOrderFlowProcessor**: 采购订单流程处理器
- **InventoryReceiptFlowProcessor**: 入库单流程处理器
- **InvoiceEntryFlowProcessor**: 发票录入流程处理器
- **FlowContext**: 流程上下文，管理流程数据传递和状态

#### 5. 活动执行层
- **CompanyBusinessActivity**: 公司业务活动接口，定义具体业务操作
- **StatusPollingActivity**: 状态轮询活动接口，提供传统轮询机制

#### 6. API调用层
- **SystemApiClient**: 系统API客户端，支持多公司API调用
- **CompanyApiConfigService**: 公司API配置服务，管理多公司域名配置

#### 7. 数据访问层
- **TemporalFlowOrderMapper**: 业务流转单数据访问接口
- **FlowNodeCompanyMapper**: 流程节点公司数据访问接口
- **TemporalBaseCompanyInfoMapper**: 公司基础信息数据访问接口

#### 8. 异常处理层
- **BusinessProcessException**: 统一异常体系，支持 ACTIVITY/STEP/PROCESS 三层架构
- **ErrorClassifier**: 错误分类器，智能判断重试策略
- **ExceptionHandler**: 异常处理工具，提供统一的异常转换和处理方法
- **PollingException**: 轮询专用异常，继承 BusinessProcessException
- **ApiStandardException**: API标准模块专用异常类

#### 9. 数据库层
- **T_FLOW_ORDER**: 业务流转单主表
- **T_FLOW_NODE**: 流程节点配置表
- **T_BASE_COMPANY_INFO**: 公司基础信息表

## 核心功能

### 1. 采购-销售-库存-发票链条工作流

基于 BusinessFlowProcessor 架构实现的多步骤业务流程：

**技术实现**：
- **PurchaseOrderFlowProcessor**: 采购订单流程处理器
- **SalesOrderFlowProcessor**: 销售订单流程处理器
- **InventoryReceiptFlowProcessor**: 入库单流程处理器
- **InvoiceEntryFlowProcessor**: 发票录入流程处理器

**流程特点**：
- 固定业务步骤序列：采购订单 → 销售订单 → 库存处理 → 发票生成
- 基于 T_FLOW_NODE 的动态公司顺序管理
- 严格的公司间依赖控制和状态同步
- 支持复杂的多接口业务流程编排

### 2. 付款转账链条工作流

**技术实现**：
- **PaymentTransferProcess**: 付款转账流程处理器
- **补偿式事务**: 基于 HTTP API 调用的补偿机制
- **状态管理**: 基于 Temporal 原生状态管理

**流程特点**：
- 付款单生成和传递的固定步骤序列
- 支持客户和供应商双重角色识别
- 严格的付款传递顺序控制

### 3. 多步骤审批工作流

**技术实现**：
- 支持超过5步的动态审批流程
- 基于审批阶段的动态用户配置
- executeOneStepApproval 支持循环审核功能

**流程特点**：
- 多步骤审批流程的增量推进
- 不同审批阶段的用户动态注入
- 与 Temporal 工作流引擎的状态管理集成

### 4. 数据库驱动的动态工作流配置

**核心表结构**：

| 表名 | 用途 | 关键字段 |
|------|------|----------|
| T_FLOW_ORDER | 业务流转单主表 | flowOrderId, baseOrderId, baseBusinessType |
| T_FLOW_NODE | 流程节点配置表 | flowNodeId, traderId, nodeLevel |

**配置机制**：
- 基于 T_FLOW_NODE.NODE_LEVEL 确定公司执行顺序
- 通过 TRADER_ID 关联获取实际公司信息
- FlowNodeCompanyMapper 提供公司顺序查询接口
- 实时数据获取，避免配置同步问题
## 技术集成

### 1. Spring 4.1.9 集成

**Java Bean 配置方式**：

<augment_code_snippet path="erp-temporal/src/main/java/com/vedeng/temporal/config/TemporalConfig.java" mode="EXCERPT">
````java
@Configuration
@ComponentScan(basePackages = "com.vedeng.temporal")
public class TemporalConfig {

    @Bean
    public WorkflowServiceStubs workflowServiceStubs() {
        return WorkflowServiceStubs.newServiceStubs(
            WorkflowServiceStubsOptions.newBuilder()
                .setTarget(serverHost + ":" + serverPort)
                .build()
        );
    }

    @Bean
    public WorkflowClient workflowClient(WorkflowServiceStubs serviceStubs) {
        return WorkflowClient.newInstance(serviceStubs,
            WorkflowClientOptions.newBuilder()
                .setNamespace(namespace)
                .build());
    }

    @Bean
    public WorkerFactory workerFactory(WorkflowClient workflowClient) {
        return WorkerFactory.newInstance(workflowClient);
    }
}
````
</augment_code_snippet>

**集成特性**：
- 兼容 Spring 4.1.9 框架的 Java Bean 配置
- 自动组件扫描和依赖注入
- Worker 生命周期管理
- 工作流客户端连接池管理

### 2. Temporal 工作流引擎集成

**核心组件**：

| 组件 | 用途 | 配置方式 |
|------|------|----------|
| WorkflowServiceStubs | 服务连接管理 | 直接创建 |
| WorkflowClient | 工作流客户端管理 | 直接创建 |
| WorkerFactory | Worker 实例管理 | 直接创建 |
| Worker 注册 | 工作流和活动注册 | 自动注册 |
| 命名空间管理 | 动态创建和配置 | TemporalConfig |

**超时和重试配置**：

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| 活动超时 | 1分钟 | 单个活动执行超时 |
| 工作流超时 | 2小时 | 整个工作流执行超时 |
| 重试次数 | 3次 | 活动级别最大重试次数 |
| 重试间隔 | 1秒起步 | 指数退避重试策略 |

### 3. SystemApiClient 多公司 API 调用

**核心功能**：

<augment_code_snippet path="erp-temporal/src/main/java/com/vedeng/temporal/util/SystemApiClient.java" mode="EXCERPT">
````java
@Component
public class SystemApiClient implements ISystemApiClient {

    @Autowired
    private CompanyApiConfigService companyApiConfigService;

    public ISystemApiClient withCompany(String companyCode) {
        this.currentCompanyCode.set(companyCode);
        return this;
    }

    public String postToSystemApi(String apiPath, Object requestData) {
        String fullUrl = buildFullUrl(apiPath);
        // HTTP 调用实现
    }
}
````
</augment_code_snippet>

**技术特性**：
- 线程安全的多公司上下文管理
- 动态公司域名配置
- 统一的认证和错误处理
- 支持用户身份模拟

### 4. 补偿式事务处理

**实现机制**：
- 基于 HTTP API 调用的补偿机制
- 无法提供 ACID 事务的完全回滚
- 通过业务补偿操作处理异常情况
- WorkflowExecutionLogEntity 记录补偿状态

**补偿策略**：
- 网络异常：自动重试机制
- 业务异常：记录失败状态，人工处理
- 系统异常：工作流失败，触发告警

## 配置说明

### 技术栈配置

| 技术组件 | 版本 | 用途 | 配置方式 |
|---------|------|------|----------|
| Spring Framework | 4.1.9 | 核心应用框架 | Java Bean 配置 |
| Temporal | 1.22.0 | 工作流引擎 | WorkflowClient + Worker |
| XXL-Job | 2.1.1 | 定时任务调度 | 外部依赖 |
| MySQL | 5.x | 数据存储 | MyBatis 映射 |

### 核心配置参数

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| temporal.server.host | ************* | Temporal 服务器地址 |
| temporal.server.port | 7233 | Temporal 服务器端口 |
| temporal.namespace | erp-namespace | 工作流命名空间 |
| temporal.worker.threads | 10 | Worker 线程数 |
| temporal.taskQueue.multiCompany | erp-multi-company-queue | 任务队列名称 |

### 数据库表结构

| 表名 | 用途 | 关键字段 |
|------|------|----------|
| T_FLOW_ORDER | 业务流转单主表 | flowOrderId, baseOrderId, baseBusinessType |
| T_FLOW_NODE | 流程节点配置表 | flowNodeId, traderId, nodeLevel |
| T_WORKFLOW_EXECUTION_LOG | 工作流执行日志表 | workflowId, businessId, companyCode, executionStatus |
| T_BASE_COMPANY_INFO | 公司基础信息表 | companyCode, erpDomain, companyShortName |

## 使用示例

### 1. Spring Bean 配置

在 Spring 配置类中导入 Temporal 配置：

```java
@Configuration
@Import(TemporalConfig.class)
@ComponentScan(basePackages = "com.vedeng.temporal")
public class ERPConfiguration {

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}
```

### 2. 工作流启动示例

```java
@Component
public class WorkflowExample {

    @Autowired
    private WorkflowOrchestrationService workflowService;

    public void startBusinessWorkflow() {
        CompanyBusinessRequest request = CompanyBusinessRequest.builder()
                .businessId("SO202412260001")
                .businessType("PURCHASE_SALES_INVENTORY_INVOICE")
                .sourceCompanyCode("COMPANY_A")
                .targetCompanyCode("COMPANY_B")
                .businessData("{\"productId\":1001,\"quantity\":100}")
                .createTimestamp(System.currentTimeMillis())
                .build();

        CompanyBusinessResponse response = workflowService
                .startMultiCompanyWorkflow(request);

        log.info("工作流启动结果: {}", response);
    }
}
```

### 3. SystemApiClient 使用示例

```java
@Component
public class ApiClientExample {

    @Autowired
    private SystemApiClient systemApiClient;

    public void callCompanyApi() {
        // 设置公司上下文
        String response = systemApiClient
                .withCompany("COMPANY_B")
                .withUser("admin")
                .postToSystemApi("/api/v1/purchase/create", requestData);

        log.info("API调用结果: {}", response);
    }
}
```

### 4. 流程处理器使用示例

```java
@Component
public class FlowProcessorExample extends BusinessFlowProcessor {

    @Override
    protected FlowResult executeFlow(FlowContext context) {
        try {
            // 阶段1：前置条件检查
            if (!checkPreconditions(context)) {
                return context.failure("前置条件检查失败");
            }

            // 阶段2：数据准备
            if (!prepareData(context)) {
                return context.failure("数据准备失败");
            }

            // 阶段3：业务执行
            if (!executeBusinessLogic(context)) {
                return context.failure("业务执行失败");
            }

            return context.success("流程执行成功");
        } catch (Exception e) {
            return context.failure("流程执行异常: " + e.getMessage());
        }
    }
}
```

## 异常处理

### 1. 异常分类

**系统级异常**（工作流失败）：
- 系统错误：数据库连接失败、配置错误
- 验证错误：参数格式不正确、必要配置缺失
- 网络异常：Temporal 服务器连接失败

**业务级异常**（工作流完成，返回失败响应）：
- 业务规则违反：库存不足、审核失败
- 外部服务错误：API 调用失败（支持重试）
- 数据异常：业务数据不完整或不正确

### 2. 重试机制

**重试策略配置**：

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| 最大重试次数 | 3次 | 活动级别重试上限 |
| 初始重试间隔 | 1秒 | 第一次重试等待时间 |
| 退避系数 | 2.0 | 指数退避倍数 |
| 最大重试间隔 | 60秒 | 重试间隔上限 |

**重试条件**：
- 网络超时异常
- HTTP 5xx 服务器错误
- 临时性数据库连接异常
- 可配置的业务错误码

### 3. 补偿机制

**补偿策略**：
- HTTP API 调用无法提供 ACID 事务
- 通过业务补偿操作处理异常情况
- 记录详细的执行日志用于问题排查
- 支持人工干预和数据修复

**监控和日志**：
- WorkflowExecutionLogEntity 记录完整执行状态
- 详细的错误信息和堆栈跟踪
- 支持按业务ID、公司代码查询执行历史
- 提供执行时长和重试次数统计
## 目录结构

```
erp-temporal/
├── src/main/java/com/vedeng/temporal/
│   ├── activity/                    # 活动层
│   │   ├── CompanyBusinessActivity.java
│   │   ├── StatusPollingActivity.java
│   │   ├── DependencyCheckActivity.java
│   │   └── impl/                    # 活动实现类
│   ├── config/                      # 配置层
│   │   ├── TemporalConfig.java      # Temporal 主配置类
│   │   ├── CompanyApiConfigService.java
│   │   └── ActivityConfigManager.java
│   ├── flow/                        # 流程处理层
│   │   ├── BusinessFlowProcessor.java    # 流程处理器基类
│   │   ├── FlowContext.java             # 流程上下文
│   │   ├── SalesOrderFlowProcessor.java
│   │   ├── PurchaseOrderFlowProcessor.java
│   │   ├── InventoryReceiptFlowProcessor.java
│   │   └── InvoiceEntryFlowProcessor.java
│   ├── process/                     # 业务流程层
│   │   ├── PurchaseSalesInventoryProcess.java
│   │   ├── InvoiceEntryProcess.java
│   │   ├── PaymentTransferProcess.java
│   │   └── impl/                    # 流程实现类
│   ├── workflow/                    # 工作流层
│   │   ├── MultiCompanyBusinessWorkflow.java
│   │   └── impl/
│   │       └── MultiCompanyBusinessWorkflowImpl.java
│   ├── util/                        # 工具类
│   │   └── SystemApiClient.java     # 系统API客户端
│   ├── mapper/                      # 数据访问层
│   │   ├── TemporalFlowOrderMapper.java
│   │   ├── FlowNodeCompanyMapper.java
│   │   └── TemporalBaseCompanyInfoMapper.java
│   ├── domain/                      # 领域对象
│   │   ├── entity/                  # 实体类
│   │   │   └── FlowOrderEntity.java
│   │   └── dto/                     # 数据传输对象
│   ├── exception/                   # 异常处理层
│   │   ├── BusinessProcessException.java
│   │   ├── ErrorClassifier.java
│   │   ├── ExceptionHandler.java
│   │   ├── PollingException.java
│   │   └── ApiStandardException.java
│   ├── constants/                   # 常量定义
│   │   └── BusinessConstants.java
│   └── task/                        # 任务层
│       └── MultiCompanyBusinessJob.java
├── src/main/resources/
│   ├── mapping/                     # MyBatis 映射文件
│   ├── sql/                         # SQL 脚本
│   └── business-process.properties  # 业务流程配置
└── pom.xml                          # Maven 配置
```

### 核心包说明

| 包名 | 职责 | 主要类 |
|------|------|--------|
| activity | 活动执行层 | CompanyBusinessActivity, StatusPollingActivity |
| config | 配置管理 | TemporalConfig, CompanyApiConfigService |
| flow | 流程处理层 | BusinessFlowProcessor, FlowContext |
| process | 业务流程层 | PurchaseSalesInventoryProcess, InvoiceEntryProcess |
| workflow | 工作流层 | MultiCompanyBusinessWorkflow |
| util | 工具类 | SystemApiClient |
| mapper | 数据访问层 | TemporalFlowOrderMapper, FlowNodeCompanyMapper |
| exception | 异常处理 | BusinessProcessException, ErrorClassifier, ExceptionHandler |

---

**技术文档版本**: 1.0
**最后更新**: 2025-01-07
**维护者**: Claude 4.0 sonnet

