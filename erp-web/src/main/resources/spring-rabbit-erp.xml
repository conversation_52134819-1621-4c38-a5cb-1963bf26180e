<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:mvc="http://www.springframework.org/schema/mvc" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:apollo="http://www.ctrip.com/schema/apollo" xmlns:rabbit="http://www.springframework.org/schema/rabbit"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.ctrip.com/schema/apollo http://www.ctrip.com/schema/apollo.xsd
		http://www.springframework.org/schema/rabbit http://www.springframework.org/schema/rabbit/spring-rabbit.xsd">

    <!--rabbit连接-->
    <rabbit:connection-factory
            id="erpConnectionFactory"
            addresses="${erp.rabbitmq.host}"
            username="${erp.rabbitmq.username}"
            password="${erp.rabbitmq.password}"
            virtual-host="${erp.rabbitmq.virtualHost}"
            channel-cache-size="100"
            publisher-returns="true"
            publisher-confirms="true" />

    <!-- 定义mq管理 -->
    <rabbit:admin connection-factory="erpConnectionFactory" />
    <rabbit:template id="erpRabbitTemplate"
                     connection-factory="erpConnectionFactory" message-converter="messageConverter" />

    <!--  集采解绑 -->
    <!-- 声明队列 -->
    <rabbit:queue name="jcErpUnboundQueue" durable="true" auto-delete="false" exclusive="false" ></rabbit:queue>
    <!-- 将队列绑定交换机 -->
    <rabbit:direct-exchange name="jcErpOrgExchange" durable="true" auto-delete="false">
        <rabbit:bindings>
            <rabbit:binding queue="jcErpUnboundQueue" key="jcErpUnboundRoutingkey" />
        </rabbit:bindings>
    </rabbit:direct-exchange>

    <!-- 解绑消费者 -->
    <!-- 声明队列 -->
    <rabbit:queue name="jcPushAccountForbiddenQueue" durable="true" auto-delete="false" exclusive="false" ></rabbit:queue>
    <!--禁用 消费者监听-->
    <rabbit:listener-container connection-factory="erpConnectionFactory" acknowledge="manual" failed-declaration-retry-interval="${failed-declaration-retry-interval}"
                               declaration-retries="${declaration-retries}">
        <rabbit:listener  queues="jcPushAccountForbiddenQueue "  ref="userAccountUnboundConsumer"/>
    </rabbit:listener-container>



    <rabbit:queue name="accountRegisterQueue" auto-declare="false" durable="true" exclusive="false" />

    <!--消费者监听-->
    <rabbit:listener-container connection-factory="erpConnectionFactory" acknowledge="manual" failed-declaration-retry-interval="${failed-declaration-retry-interval}"
                               declaration-retries="${declaration-retries}">
        <rabbit:listener  queues="accountRegisterQueue"  ref="accountRegisterQueueConsumer"/>
    </rabbit:listener-container>

    <rabbit:queue name="generateAftersaleOrderQueue" durable="true" auto-declare="false" exclusive="false" />
    <rabbit:listener-container connection-factory="erpConnectionFactory" acknowledge="manual" failed-declaration-retry-interval="${failed-declaration-retry-interval}"
                               declaration-retries="${declaration-retries}">
        <rabbit:listener ref="afterSaleorderConsumer" queues="generateAftersaleOrderQueue"></rabbit:listener>
    </rabbit:listener-container>

    <!--消费者监听-->
    <rabbit:queue name="pushOrderQueue" durable="true" auto-delete="false" exclusive="false" />
    <rabbit:listener-container connection-factory="erpConnectionFactory" acknowledge="manual" failed-declaration-retry-interval="${failed-declaration-retry-interval}"
                               declaration-retries="${declaration-retries}">
        <rabbit:listener queues="pushOrderQueue"  ref="jcOrderConsumer"/>
    </rabbit:listener-container>

    <!--数据权限平台-消费者监听-->
    <rabbit:queue name="userInfoChangedQueue" durable="true" auto-delete="false" exclusive="false" />
    <rabbit:listener-container connection-factory="erpConnectionFactory" acknowledge="manual">
        <rabbit:listener queues="userInfoChangedQueue"  ref="wechatUserConsumer"/>
    </rabbit:listener-container>

    <!--数据权限平台-消费者监听-->
    <rabbit:queue name="accountInfoChangedQueue" durable="true" auto-delete="false" exclusive="false" />
    <rabbit:listener-container connection-factory="erpConnectionFactory" acknowledge="manual">
        <rabbit:listener queues="accountInfoChangedQueue"  ref="accountInfoChangedListener"/>
    </rabbit:listener-container>

    <!--自动注册队列-->
    <rabbit:queue name="AutomaticRegistrationMsgQueue" durable="true" auto-delete="false" exclusive="false" />

    <!--自动注册交换机绑定-->
    <rabbit:direct-exchange name="AutomaticallyRegisterExchange" durable="true" auto-delete="false">
        <rabbit:bindings>
            <rabbit:binding queue="AutomaticRegistrationMsgQueue" key="AutomaticallyRegisterRoutingkey" />
        </rabbit:bindings>
    </rabbit:direct-exchange>

    <!-- 采购直发订单能否确认收货 延时队列 时间单位:毫秒-->
    <rabbit:queue name="buyOrderEnableReceiveLockQueue" auto-declare="true" durable="true" exclusive="false" auto-delete="false">
        <rabbit:queue-arguments>
            <entry key="x-message-ttl" value="${buyOrder-enableReceive-unlock-time}" value-type="java.lang.Long" />
            <entry key="x-dead-letter-exchange" value="buyOrderEnableReceive_dlx" />
            <entry key="x-dead-letter-routing-key" value="buyOrderEnableReceive_dlx_key" />
        </rabbit:queue-arguments>
    </rabbit:queue>

    <rabbit:direct-exchange name="buyOrderEnableReceiveLockExchange" durable="true" auto-delete="false">
        <rabbit:bindings>
            <rabbit:binding queue="buyOrderEnableReceiveLockQueue" key="buyOrderEnableReceiveLockRoutingKey"/>
        </rabbit:bindings>
    </rabbit:direct-exchange>

    <!-- 采购直发订单能否确认收货 死信队列 -->
    <rabbit:queue name="buyOrderEnableReceiveUnlockQueue" durable="true" auto-declare="true" exclusive="false" auto-delete="false"/>
    <rabbit:direct-exchange name="buyOrderEnableReceive_dlx" durable="true">
        <rabbit:bindings>
            <rabbit:binding queue="buyOrderEnableReceiveUnlockQueue" key="buyOrderEnableReceive_dlx_key"/>
        </rabbit:bindings>
    </rabbit:direct-exchange>

    <rabbit:listener-container connection-factory="erpConnectionFactory" acknowledge="manual"
                               failed-declaration-retry-interval="${failed-declaration-retry-interval}"
                               declaration-retries="${declaration-retries}">
        <rabbit:listener ref="buyOrderEnableReceiveUnlockConsumer" queues="buyOrderEnableReceiveUnlockQueue"/>
    </rabbit:listener-container>


    <!-- 公私海客户锁定 - 延迟队列 -->
    <rabbit:queue name="publicCustomerLockQueue" auto-declare="true">
        <rabbit:queue-arguments>
            <!--			<entry key="x-message-ttl" value="********" value-type="java.lang.Long" />-->
            <entry key="x-message-ttl" value="${public-trader-unlock-time}" value-type="java.lang.Long" />
            <entry key="x-dead-letter-exchange" value="publicCustomerLock_dlx" />
            <entry key="x-dead-letter-routing-key" value="publicCustomerLock_dlx_key" />
        </rabbit:queue-arguments>
    </rabbit:queue>

    <rabbit:direct-exchange name="publicCustomerLockExchange" durable="true" auto-delete="false">
        <rabbit:bindings>
            <rabbit:binding queue="publicCustomerLockQueue" key="publicCustomerLockRoutingKey" />
        </rabbit:bindings>
    </rabbit:direct-exchange>

    <!-- 公私海客户锁定 - 死信队列 -->
    <rabbit:queue name="publicCustomerUnlockQueue" auto-declare="true"/>
    <rabbit:direct-exchange name="publicCustomerLock_dlx" durable="true" auto-delete="false">
        <rabbit:bindings>
            <rabbit:binding queue="publicCustomerUnlockQueue" key="publicCustomerLock_dlx_key" />
        </rabbit:bindings>
    </rabbit:direct-exchange>

    <rabbit:listener-container connection-factory="erpConnectionFactory" acknowledge="manual"
                               failed-declaration-retry-interval="${failed-declaration-retry-interval}"
                               declaration-retries="${declaration-retries}">
        <rabbit:listener ref="publicCustomerUnlockConsumer" queues="publicCustomerUnlockQueue"/>
    </rabbit:listener-container>

    <!--前台下载出库单，order推送消息给erp处理 -->
    <rabbit:queue name="erpOrderQueue" auto-declare="true"/>
    <rabbit:listener-container connection-factory="erpConnectionFactory" acknowledge="manual" failed-declaration-retry-interval="${failed-declaration-retry-interval}"
                               declaration-retries="${declaration-retries}">
        <rabbit:listener queues="erpOrderQueue" ref="printerOutOrderConsumer"/>
    </rabbit:listener-container>
    <bean id="printerOutOrderConsumer" class="com.vedeng.consumer.saleorder.PrinterOutOrderConsumer"/>

    <!--自动注册队列-->
    <rabbit:queue name="skuInfoChangedQueue" durable="true" auto-delete="false" exclusive="false" />

    <!--自动注册交换机绑定-->
    <rabbit:direct-exchange name="skuInfoChangedExchange" durable="true" auto-delete="false">
        <rabbit:bindings>
            <rabbit:binding queue="skuInfoChangedQueue" key="skuInfoChangedKey" />
        </rabbit:bindings>
    </rabbit:direct-exchange>


    <!--    在线签收开票相关-->
    <rabbit:queue name="invoiceOpenInfoQueue" auto-declare="true"/>
    <rabbit:listener-container connection-factory="erpConnectionFactory" acknowledge="manual" failed-declaration-retry-interval="${failed-declaration-retry-interval}"
                               declaration-retries="${declaration-retries}" concurrency="1" prefetch="1">
        <rabbit:listener queues="invoiceOpenInfoQueue" ref="onlineInvoiceOpenConsumer"/>
    </rabbit:listener-container>
    <bean id="onlineInvoiceOpenConsumer" class="com.vedeng.consumer.saleorder.OnlineInvoiceOpenConsumer"/>


    <rabbit:queue name="expressReceiptQueue" auto-declare="true"/>
    <rabbit:listener-container connection-factory="erpConnectionFactory" acknowledge="manual" failed-declaration-retry-interval="${failed-declaration-retry-interval}"
                               declaration-retries="${declaration-retries}" concurrency="1" prefetch="1">
        <rabbit:listener queues="expressReceiptQueue" ref="onlineExpressReceiptConsumer"/>
    </rabbit:listener-container>
    <bean id="onlineExpressReceiptConsumer" class="com.vedeng.consumer.saleorder.OnlineExpressReceiptConsumer"/>


    <!--自动注册队列-->
    <rabbit:queue name="invoiceOpenResultQueue" durable="true" auto-delete="false" exclusive="false" />

    <!--自动注册交换机绑定-->
    <rabbit:direct-exchange name="onLineInvoicingExchange" durable="true" auto-delete="false">
        <rabbit:bindings>
            <rabbit:binding queue="invoiceOpenResultQueue" key="onLineInvoiceOpenResultKey" />
        </rabbit:bindings>
    </rabbit:direct-exchange>

    <rabbit:queue name="noticeInvoiceQueue" auto-declare="true"/>
    <rabbit:listener-container connection-factory="erpConnectionFactory" acknowledge="manual" failed-declaration-retry-interval="${failed-declaration-retry-interval}"
                               declaration-retries="${declaration-retries}">
        <rabbit:listener queues="noticeInvoiceQueue" ref="urgesOnlineConsumer"/>
    </rabbit:listener-container>
    <bean id="urgesOnlineConsumer" class="com.vedeng.consumer.saleorder.UrgesOnlineConsumer"/>

    <!--  精准营销运营活动计划mq-->
    <rabbit:queue name="marketPlanQueue" auto-declare="true" durable="true"/>
    <rabbit:listener-container connection-factory="erpConnectionFactory" acknowledge="manual"
                               failed-declaration-retry-interval="${failed-declaration-retry-interval}"
                               declaration-retries="${declaration-retries}">
        <rabbit:listener ref="marketPlanConsumer" queues="marketPlanQueue"/>
    </rabbit:listener-container>


</beans>