package com.vedeng.erp.saleorder.service;

import com.vedeng.erp.saleorder.dto.ActivityPreOrderDto;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2022/12/25 19:50
 */
public interface ActivityPreOrderApiService {

    /**
     * 根据订单id获取
     *
     * @param saleorderId saleorderId
     * @return ActivityPreOrderEntity
     */
    ActivityPreOrderDto getActivityPreOrderBySaleorderId(Integer saleorderId);

    /**
     * 修改
     *
     * @param dto
     */
    void update(ActivityPreOrderDto dto);
}
