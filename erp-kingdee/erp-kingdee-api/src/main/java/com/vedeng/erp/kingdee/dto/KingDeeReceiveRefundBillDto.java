package com.vedeng.erp.kingdee.dto;

import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.infrastructure.kingdee.annotation.BusinessID;
import com.vedeng.infrastructure.kingdee.annotation.KingDeeID;
import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

/**
 * 收款退款单Dto
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@WriteBackField(needBackField = "FID")
public class KingDeeReceiveRefundBillDto extends KingDeeMqBaseDto {
    /**
     * 主键
     */
    private Integer kingDeeReceiveRefundBillId;

    /**
     * 是否自动提交审核
     */
    private Boolean isAutoSubmitAndAudit;

    /**
     * 云星空系统单据FID值
     */
    @KingDeeID
    @WriteBackField(backWriteByErpField = "fId")
    private String FID;

    /**
     * 单据编号
     */
    @BusinessID
    private String FBillNo;

    /**
     * 单据类型id
     */
    private String FBillTypeID;

    /**
     * 单据日期
     */
    private String FDATE;

    /**
     * 往来单位类型
     */
    private String FCONTACTUNITTYPE;

    /**
     * 往来单位
     */
    private String FCONTACTUNIT;

    /**
     * 收款单位类型
     */
    private String FRECTUNITTYPE;

    /**
     * 收款单位
     */
    private String FRECTUNIT;

    /**
     * 结算组织
     */
    private String FSETTLEORGID;

    /**
     * 销售组织
     */
    private String FSALEORGID;

    /**
     * 收款组织
     */
    private String FPAYORGID;

    /**
     * 业务类型
     */
    private String FBUSINESSTYPE;

    /**
     * 流水号
     */
    private String F_QZOK_LSH;

    /**
     * 单据头id
     */
    private String F_QZOK_BDDJTID;

    /**
     * "往来单位" 所选择资金流水的订单单号
     * T_CAPITAL_BILL_DETAIL.ORDER_NO（订单单号）
     */
    private String FQzokPzgsywdh;

    /**
     * 提交银行标识
     * 启用推送金蝶付款单自动审核：是  1
     * 启用推送金蝶付款单自动审核：否  2
     *
     */
    private String FQzokZdtjyh;

    /**
     * 明细
     */
    private List<KingDeeReceiveRefundEntryDto> FREFUNDBILLENTRY;

    @Override
    public String getFormId() {
        return KingDeeFormConstant.RECEIVE_REFUND;
    }

    public KingDeeReceiveRefundBillDto(){
        this.FBillTypeID = "SKTKDLX01_SYS";
        this.FSETTLEORGID = KingDeeConstant.ORG_ID.toString();
        this.FSALEORGID = KingDeeConstant.ORG_ID.toString();
        this.FPAYORGID = KingDeeConstant.ORG_ID.toString();
        this.FBUSINESSTYPE = ErpConstant.ONE.toString();
        this.FREFUNDBILLENTRY = new ArrayList<>();
    }


}
