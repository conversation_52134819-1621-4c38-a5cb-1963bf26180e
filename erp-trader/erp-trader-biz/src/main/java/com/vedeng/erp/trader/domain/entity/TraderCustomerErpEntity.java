package com.vedeng.erp.trader.domain.entity;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;


/**
 * @description 客户(分销+终端)
 * <AUTHOR>
 * @date 2023/8/11 14:43
 **/

@Getter
@Setter
@ToString
@NoArgsConstructor
public class TraderCustomerErpEntity {
    /**
    * 客户ID
    */
    private Integer traderCustomerId;

    /**
    * 交易者ID
    */
    private Integer traderId;

    /**
    * 客户类型分类ID
    */
    private Integer traderCustomerCategoryId;

    /**
    * 客户类型 426科研医疗 427临床医疗 (字典库)
    */
    private Integer customerType;

    /**
    * 客户性质 465分销 466终端 （字典库）
    */
    private Integer customerNature;

    /**
    * 是否有效 0否 1是
    */
    private Integer isEnable;

    /**
    * 是否置顶0否 1是
    */
    private Integer isTop;

    private BigDecimal amount;

    private BigDecimal periodAmount;

    /**
    * 帐期天数
    */
    private Integer periodDay;

    /**
    * 禁用时间
    */
    private Long disableTime;

    /**
    * 客户来源 SYS_OPTION_DEFINITION_ID
    */
    private Integer customerFrom;

    /**
    * 首次询价方式 SYS_OPTION_DEFINITION_ID
    */
    private Integer firstEnquiryType;

    /**
    * 客户等级 SYS_OPTION_DEFINITION_ID
    */
    private Integer customerLevel;

    /**
    * 客户分类，0为未交易客户，1为新客户,2为流失客户，3为留存客户
    */
    private Integer customerCategory;

    /**
    * 合作分级 SYS_OPTION_DEFINITION_ID
    */
    private Integer traderLevel;

    /**
    * 销售评级 SYS_OPTION_DEFINITION_ID
    */
    private Integer userEvaluate;

    /**
    * 客户资料分数
    */
    private Integer customerScore;

    /**
    * 是否基础医疗经销商0否 1是
    */
    private Integer basicMedicalDealer;

    /**
    * 是否VIP 0否 1是
    */
    private Integer isVip;

    /**
    * 所有制公立私立SYS_OPTION_DEFINITION_ID
    */
    private Integer ownership;

    /**
    * 医学类型 中医西医中西结合SYS_OPTION_DEFINITION_ID
    */
    private Integer medicalType;

    /**
    * 医院等级SYS_OPTION_DEFINITION_ID
    */
    private Integer hospitalLevel;

    /**
    * 员工人数SYS_OPTION_DEFINITION_ID
    */
    private Integer employees;

    /**
    * 年销售额SYS_OPTION_DEFINITION_ID
    */
    private Integer annualSales;

    /**
    * 销售模式 SYS_OPTION_DEFINITION_ID
    */
    private Integer salesModel;

    /**
    * 注册资金
    */
    private String registeredCapital;

    /**
    * 注册年份
    */
    private Date registeredDate;

    /**
    * 直销比率
    */
    private BigDecimal directSelling;

    /**
    * 批发比率
    */
    private BigDecimal wholesale;

    /**
    * 组织机构代码
    */
    private String organizationCode;

    /**
    * 统一社会信用代码
    */
    private String unifiedSocialCreditIdentifier;

    private String businessScope;

    /**
    * 历史名称
    */
    private String historyName;

    /**
    * 禁用原因
    */
    private String disableReason;

    /**
    * 备注
    */
    private String comments;

    /**
    * 财务备注
    */
    private String financeComments;

    /**
    * 物流备注
    */
    private String logisticsComments;

    /**
    * 简介
    */
    private String brief;

    /**
    * 历史背景
    */
    private String history;

    /**
    * 现在生意情况
    */
    private String businessCondition;

    /**
    * 发展计划
    */
    private String businessPlan;

    /**
    * 公司优势
    */
    private String advantage;

    /**
    * 面临主要问题
    */
    private String primalProblem;

    /**
    * 美年系统采购单位编码
    */
    private String mnCode;

    /**
    * 是否为集中开票客户0否1是
    */
    private Integer isCollect;

    /**
    * 是否限制改价:0否1是
    */
    private Integer isLimitprice;

    /**
    * 新增集中开票客户时间
    */
    private Long collectTime;

    /**
    * 是否为盈利机构，0为是，1为不是
    */
    private Integer isProfit;

    /**
    * 添加时间
    */
    private Long addTime;

    /**
    * 添加人
    */
    private Integer creator;

    /**
    * 最近一次编辑时间
    */
    private Long modTime;

    /**
    * 最近一次编辑人
    */
    private Integer updater;

    /**
    * 是否贝登会员 0否1是
    */
    private Integer isVedengMember;

    /**
    * 是否展示会员价（医械购），0不展示，1展示
    */
    private Integer isShowVipPrice;

    private Integer hasQuoted;

    private Integer isCooperated;

    /**
    * 关联的客户分组ID
    */
    private Long associatedCustomerGroup;

    /**
    * 公海预警次数
    */
    private Integer publicCustomerEarlyWarningCount;

    /**
     * 无效原因
     */
    private String invalidReason;

    private String otherReason;
}
