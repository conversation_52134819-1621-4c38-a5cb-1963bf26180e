<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.goods.dao.GoodsChannelPriceMapper">
	<resultMap id="BaseResultMap" type="com.vedeng.goods.model.GoodsChannelPrice">
		<id column="GOODS_CHANNEL_PRICE_ID" property="goodsChannelPriceId" jdbcType="INTEGER" />
		<result column="GOODS_ID" property="goodsId" jdbcType="INTEGER" />
		<result column="PROVINCE_ID" property="provinceId" jdbcType="INTEGER" />
		<result column="PROVINCE_NAME" property="provinceName" jdbcType="VARCHAR" />
		<result column="CITY_ID" property="cityId" jdbcType="INTEGER" />
		<result column="CITY_NAME" property="cityName" jdbcType="VARCHAR" />
		<result column="DISTRIBUTION_PRICE" property="distributionPrice"	jdbcType="DECIMAL" />
		<result column="PUBLIC_PRICE" property="publicPrice" jdbcType="DECIMAL" />
		<result column="PRIVATE_PRICE" property="privatePrice" jdbcType="DECIMAL" />
		<result column="DELIVERY_CYCLE" property="deliveryCycle" jdbcType="INTEGER" />
		<result column="PERIOD_DATE" property="periodDate" jdbcType="DATE" />
		<result column="IS_REPORT_TERMINAL" property="isReportTerminal" jdbcType="BIT" />
		<result column="IS_MANUFACTURER_AUTHORIZATION" property="isManufacturerAuthorization" jdbcType="BIT" />
		<result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
		<result column="CREATOR" property="creator" jdbcType="INTEGER" />
		<result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
		<result column="UPDATER" property="updater" jdbcType="INTEGER" />
		<result column="MIN_AMOUNT" property="minAmount" jdbcType="DECIMAL" />
		<result column="VIP_PRICE" property="vipPrice" jdbcType="DECIMAL" />
		<result column="MARKET_PRICE" property="marketPrice" jdbcType="DECIMAL" />
		<result column="MIN_NUM" property="minNum" jdbcType="INTEGER" />
		<result column="CUSTOMER_TYPE_COMMENTS" property="customerTypeComments" jdbcType="VARCHAR" />
		<result column="BATCH_POLICY" property="batchPolicy" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List">
		GOODS_CHANNEL_PRICE_ID, GOODS_ID, PROVINCE_ID, PROVINCE_NAME, CITY_ID, CITY_NAME,
		DISTRIBUTION_PRICE, PUBLIC_PRICE, PRIVATE_PRICE,
		DELIVERY_CYCLE, PERIOD_DATE,
		IS_REPORT_TERMINAL, IS_MANUFACTURER_AUTHORIZATION, ADD_TIME, CREATOR, MOD_TIME, UPDATER
	</sql>
	<select id="selectByPrimaryKey" resultMap="BaseResultMap"
		parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from T_GOODS_CHANNEL_PRICE
		where GOODS_CHANNEL_PRICE_ID = #{goodsChannelPriceId,jdbcType=INTEGER}
	</select>
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		delete from T_GOODS_CHANNEL_PRICE
		where GOODS_CHANNEL_PRICE_ID = #{goodsChannelPriceId,jdbcType=INTEGER}
	</delete>
	<insert id="insert" parameterType="com.vedeng.goods.model.GoodsChannelPrice">
		insert into T_GOODS_CHANNEL_PRICE (GOODS_CHANNEL_PRICE_ID, GOODS_ID,
		PROVINCE_ID,
		PROVINCE_NAME, CITY_ID, CITY_NAME,
		DISTRIBUTION_PRICE, PUBLIC_PRICE,
		PRIVATE_PRICE, DELIVERY_CYCLE, PERIOD_DATE,
		IS_REPORT_TERMINAL, IS_MANUFACTURER_AUTHORIZATION,
		ADD_TIME, CREATOR, MOD_TIME,
		UPDATER)
		values (#{goodsChannelPriceId,jdbcType=INTEGER},
		#{goodsId,jdbcType=INTEGER}, #{provinceId,jdbcType=INTEGER},
		#{provinceName,jdbcType=VARCHAR}, #{cityId,jdbcType=INTEGER},
		#{cityName,jdbcType=VARCHAR},
		#{distributionPrice,jdbcType=DECIMAL}, #{publicPrice,jdbcType=DECIMAL},
		#{privatePrice,jdbcType=DECIMAL}, #{deliveryCycle,jdbcType=INTEGER},
		#{periodDate,jdbcType=DATE},
		#{isReportTerminal,jdbcType=BIT}, #{isManufacturerAuthorization,jdbcType=BIT},
		#{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER},
		#{modTime,jdbcType=BIGINT},
		#{updater,jdbcType=INTEGER})
	</insert>
	<insert id="insertSelective" parameterType="com.vedeng.goods.model.GoodsChannelPrice" >
		<selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="goodsChannelPriceId">
          SELECT LAST_INSERT_ID()
        </selectKey>
		
		insert into T_GOODS_CHANNEL_PRICE
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="goodsChannelPriceId != null">
				GOODS_CHANNEL_PRICE_ID,
			</if>
			<if test="goodsId != null">
				GOODS_ID,
			</if>
			<if test="provinceId != null">
				PROVINCE_ID,
			</if>
			<if test="provinceName != null">
				PROVINCE_NAME,
			</if>
			<if test="cityId != null">
				CITY_ID,
			</if>
			<if test="cityName != null">
				CITY_NAME,
			</if>
			<if test="distributionPrice != null">
				DISTRIBUTION_PRICE,
			</if>
			<if test="publicPrice != null">
				PUBLIC_PRICE,
			</if>
			<if test="privatePrice != null">
				PRIVATE_PRICE,
			</if>
			<if test="periodDate != null">
				PERIOD_DATE,
			</if>
			<if test="isReportTerminal != null">
				IS_REPORT_TERMINAL,
			</if>
			<if test="isManufacturerAuthorization != null">
				IS_MANUFACTURER_AUTHORIZATION,
			</if>
			<if test="customerTypeComments != null">
				CUSTOMER_TYPE_COMMENTS,
			</if>
			<if test="minNum != null">
				MIN_NUM,
			</if>
			<if test="minAmount != null">
				MIN_AMOUNT,
			</if>
			<if test="batchPolicy != null">
				BATCH_POLICY,
			</if>
			<if test="marketPrice != null">
				MARKET_PRICE,
			</if>
			<if test="vipPrice != null">
				VIP_PRICE,
			</if>
			<if test="addTime != null">
				ADD_TIME,
			</if>
			<if test="creator != null">
				CREATOR,
			</if>
			<if test="modTime != null">
				MOD_TIME,
			</if>
			<if test="updater != null">
				UPDATER,
			</if>
			<if test="type != null">
				TYPE,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="goodsChannelPriceId != null">
				#{goodsChannelPriceId,jdbcType=INTEGER},
			</if>
			<if test="goodsId != null">
				#{goodsId,jdbcType=INTEGER},
			</if>
			<if test="provinceId != null">
				#{provinceId,jdbcType=INTEGER},
			</if>
			<if test="provinceName != null">
				#{provinceName,jdbcType=VARCHAR},
			</if>
			<if test="cityId != null">
				#{cityId,jdbcType=INTEGER},
			</if>
			<if test="cityName != null">
				#{cityName,jdbcType=VARCHAR},
			</if>
			<if test="distributionPrice != null">
				#{distributionPrice,jdbcType=DECIMAL},
			</if>
			<if test="publicPrice != null">
				#{publicPrice,jdbcType=DECIMAL},
			</if>
			<if test="privatePrice != null">
				#{privatePrice,jdbcType=DECIMAL},
			</if>
			<if test="deliveryCycle != null">
				#{deliveryCycle,jdbcType=INTEGER},
			</if>
			<if test="periodDate != null">
				#{periodDate,jdbcType=DATE},
			</if>
			<if test="isReportTerminal != null">
				#{isReportTerminal,jdbcType=BIT},
			</if>
			<if test="isManufacturerAuthorization != null">
				#{isManufacturerAuthorization,jdbcType=BIT},
			</if>
			<if test="customerTypeComments != null">
			    #{customerTypeComments,jdbcType=VARCHAR},
			</if>
			<if test="minNum != null">
				#{minNum,jdbcType=INTEGER},
			</if>
			<if test="minAmount != null">
				#{minAmount,jdbcType=DECIMAL},
			</if>
			<if test="batchPolicy != null">
				#{batchPolicy,jdbcType=VARCHAR},
			</if>
			<if test="marketPrice != null">
				#{marketPrice,jdbcType=DECIMAL},
			</if>
			<if test="vipPrice != null">
				#{vipPrice,jdbcType=DECIMAL},
			</if>
			<if test="addTime != null">
				#{addTime,jdbcType=BIGINT},
			</if>
			<if test="creator != null">
				#{creator,jdbcType=INTEGER},
			</if>
			<if test="modTime != null">
				#{modTime,jdbcType=BIGINT},
			</if>
			<if test="updater != null">
				#{updater,jdbcType=INTEGER},
			</if>
			<if test="type != null">
				#{type,jdbcType=INTEGER},
			</if>
		</trim>
	</insert>
	<update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.model.GoodsChannelPrice">
		update T_GOODS_CHANNEL_PRICE
		<set>
			<if test="goodsId != null">
				GOODS_ID = #{goodsId,jdbcType=INTEGER},
			</if>
			<if test="provinceId != null">
				PROVINCE_ID = #{provinceId,jdbcType=INTEGER},
			</if>
			<if test="provinceName != null">
				PROVINCE_NAME = #{provinceName,jdbcType=VARCHAR},
			</if>
			<if test="cityId != null">
				CITY_ID = #{cityId,jdbcType=INTEGER},
			</if>
			<if test="cityName != null">
				CITY_NAME = #{cityName,jdbcType=VARCHAR},
			</if>
			<if test="purchasePrice != null">
				DISTRIBUTION_PRICE = #{distributionPrice,jdbcType=DECIMAL},
			</if>
			<if test="councilPrice != null">
				PUBLIC_PRICE = #{publicPrice,jdbcType=DECIMAL},
			</if>
			<if test="privatePrice != null">
				PRIVATE_PRICE = #{privatePrice,jdbcType=DECIMAL},
			</if>
			<if test="deliveryCycle != null">
				DELIVERY_CYCLE = #{deliveryCycle,jdbcType=INTEGER},
			</if>
			<if test="periodDate != null">
				PERIOD_DATE = #{periodDate,jdbcType=DATE},
			</if>
			<if test="isReportTerminal != null">
				IS_REPORT_TERMINAL = #{isReportTerminal,jdbcType=BIT},
			</if>
			<if test="isManufacturerAuthorization != null">
				IS_MANUFACTURER_AUTHORIZATION =
				#{isManufacturerAuthorization,jdbcType=BIT},
			</if>
			<if test="addTime != null">
				ADD_TIME = #{addTime,jdbcType=BIGINT},
			</if>
			<if test="creator != null">
				CREATOR = #{creator,jdbcType=INTEGER},
			</if>
			<if test="modTime != null">
				MOD_TIME = #{modTime,jdbcType=BIGINT},
			</if>
			<if test="updater != null">
				UPDATER = #{updater,jdbcType=INTEGER},
			</if>
		</set>
		where GOODS_CHANNEL_PRICE_ID = #{goodsChannelPriceId,jdbcType=INTEGER}
	</update>
	<update id="updateByPrimaryKey" parameterType="com.vedeng.goods.model.GoodsChannelPrice">
		update T_GOODS_CHANNEL_PRICE
		set GOODS_ID = #{goodsId,jdbcType=INTEGER},
		PROVINCE_ID = #{provinceId,jdbcType=INTEGER},
		PROVINCE_NAME = #{provinceName,jdbcType=VARCHAR},
		CITY_ID = #{cityId,jdbcType=INTEGER},
		CITY_NAME = #{cityName,jdbcType=VARCHAR},
		DISTRIBUTION_PRICE = #{distributionPrice,jdbcType=DECIMAL},
		PUBLIC_PRICE = #{publicPrice,jdbcType=DECIMAL},
		PRIVATE_PRICE = #{privatePrice,jdbcType=DECIMAL},
		DELIVERY_CYCLE = #{deliveryCycle,jdbcType=INTEGER},
		PERIOD_DATE = #{periodDate,jdbcType=DATE},
		IS_REPORT_TERMINAL = #{isReportTerminal,jdbcType=BIT},
		IS_MANUFACTURER_AUTHORIZATION = #{isManufacturerAuthorization,jdbcType=BIT},
		ADD_TIME = #{addTime,jdbcType=BIGINT},
		CREATOR = #{creator,jdbcType=INTEGER},
		MOD_TIME = #{modTime,jdbcType=BIGINT},
		UPDATER = #{updater,jdbcType=INTEGER}
		where GOODS_CHANNEL_PRICE_ID = #{goodsChannelPriceId,jdbcType=INTEGER}
	</update>

	<select id="getPurchasePriceByGoodsID" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from T_GOODS_CHANNEL_PRICE
		where GOODS_ID = #{goodsId,jdbcType=INTEGER} order by
		GOODS_CHANNEL_PRICE_ID desc limit 1
	</select>

	<select id="getPurchasePrice" resultType="com.vedeng.goods.model.GoodsChannelPrice" parameterType="java.lang.Integer">
		select
		GOODS_CHANNEL_PRICE_ID, GOODS_ID, PROVINCE_ID, PROVINCE_NAME, CITY_ID, CITY_NAME,
		DISTRIBUTION_PRICE, PUBLIC_PRICE, PRIVATE_PRICE,
		DELIVERY_CYCLE, PERIOD_DATE,
		IS_REPORT_TERMINAL, IS_MANUFACTURER_AUTHORIZATION, ADD_TIME, CREATOR, MOD_TIME, UPDATER
		from T_GOODS_CHANNEL_PRICE
		where
		GOODS_ID in
		<foreach collection="goodsIds" item="goodsId" index="index" open="(" close=")" separator=",">
			#{goodsId}
		</foreach>
		group by GOODS_CHANNEL_PRICE_ID
	</select>
	<!-- 删除核价 -->
	<delete id="deleteGoodsPrice" parameterType="com.vedeng.goods.model.GoodsChannelPrice">
		DELETE FROM T_GOODS_CHANNEL_PRICE
				WHERE GOODS_ID = #{goodsId,jdbcType=INTEGER} AND PROVINCE_ID = #{provinceId,jdbcType=INTEGER}
				<!-- AND CUSTOMER_TYPE_COMMENTS like CONCAT('%',#{customerTypeComments,jdbcType=VARCHAR},'%' ) -->
					<if test="cityId != null and cityId != ''">
						 AND CITY_ID = #{cityId,jdbcType=INTEGER}
					</if>
					<if test="type != null and type != ''">
						 AND TYPE = #{type,jdbcType=INTEGER}
					</if>
	</delete>
	<!-- 查询要更新的核价信息 -->
	<select id="getGoodsChannelByG" resultType="com.vedeng.goods.model.GoodsChannelPrice" parameterType="com.vedeng.goods.model.GoodsChannelPrice">
		SELECT *  FROM T_GOODS_CHANNEL_PRICE
				WHERE GOODS_ID = #{goodsId,jdbcType=INTEGER} AND PROVINCE_ID = #{provinceId,jdbcType=INTEGER}
				<!-- AND CUSTOMER_TYPE_COMMENTS like CONCAT('%',#{customerTypeComments,jdbcType=VARCHAR},'%' ) -->
					<if test="cityId != null and cityId != ''">
						 AND CITY_ID = #{cityId,jdbcType=INTEGER}
					</if>
					<!-- <if test="customerTypeComments != null and customerTypeComments != ''">
						 AND CUSTOMER_TYPE_COMMENTS like CONCAT('%',#{customerTypeComments,jdbcType=VARCHAR},'%' )
					</if> -->
	</select>
	<!-- REPLACE INTO T_GOODS_CHANNEL_PRICE -->
	<update id="batchGoodsPriceSave" parameterType="java.util.List">
		<foreach collection="goodsList" item="list" separator=";">
			DELETE FROM T_GOODS_CHANNEL_PRICE
				WHERE GOODS_ID = #{list.goodsId,jdbcType=INTEGER} AND PROVINCE_ID = #{list.provinceId,jdbcType=INTEGER}
					<if test="list.cityId != null and list.cityId != ''">
						 AND CITY_ID = #{list.cityId,jdbcType=INTEGER}
					</if>
					;
			INSERT INTO T_GOODS_CHANNEL_PRICE (
			  GOODS_ID
			  ,PROVINCE_ID
			  ,PROVINCE_NAME
			  ,CITY_ID
			  ,CITY_NAME
			  ,COST_PRICE
			  ,DISTRIBUTION_PRICE
			  ,PUBLIC_PRICE
			  ,PRIVATE_PRICE
			  ,DELIVERY_CYCLE
			  ,PERIOD_DATE
			  ,IS_REPORT_TERMINAL
			  ,IS_MANUFACTURER_AUTHORIZATION
			  ,ADD_TIME
			  ,CREATOR
			  ,MOD_TIME
			  ,UPDATER
			) VALUES (
			  #{list.goodsId,jdbcType=INTEGER}
			  ,#{list.provinceId,jdbcType=INTEGER}
			  ,#{list.provinceName,jdbcType=VARCHAR}
			  ,#{list.cityId,jdbcType=INTEGER}
			  ,#{list.cityName,jdbcType=VARCHAR}
			  ,#{list.costPrice,jdbcType=DECIMAL}
			  ,#{list.distributionPrice,jdbcType=DECIMAL}
			  ,#{list.publicPrice,jdbcType=DECIMAL}
			  ,#{list.privatePrice,jdbcType=DECIMAL}
			  ,#{list.deliveryCycle,jdbcType=INTEGER}
			  ,#{list.periodDate,jdbcType=INTEGER}
			  ,#{list.isReportTerminal,jdbcType=INTEGER}
			  ,#{list.isManufacturerAuthorization,jdbcType=INTEGER}
			  ,#{list.addTime,jdbcType=BIGINT}
			  ,#{list.creator,jdbcType=INTEGER}
			  ,#{list.modTime,jdbcType=BIGINT}
			  ,#{list.updater,jdbcType=INTEGER}
			)
		</foreach>
	</update>
	<!-- ON DUPLICATE KEY UPDATE GOODS_ID = #{list.goodsId,jdbcType=INTEGER}, PROVINCE_ID = #{list.provinceId,jdbcType=INTEGER}, CITY_ID = #{list.cityId,jdbcType=INTEGER} -->
	<select id="getSaleChannelPriceList" parameterType="java.lang.Integer" resultType="com.vedeng.goods.model.GoodsChannelPrice">
		SELECT IFNULL(
		          IF(#{customerNature,jdbcType=INTEGER} = 465,
		             A.DISTRIBUTION_PRICE,
		             IF(#{customerNature,jdbcType=INTEGER} = 466, IF(#{ownerShip,jdbcType=INTEGER} = 155,A.PUBLIC_PRICE, A.PRIVATE_PRICE), 0)),
		          0)
		          AS CHANNEL_PRICE,
		       A.GOODS_ID,A.DELIVERY_CYCLE,B.BATCH_PRICE AS COST_PRICE
		FROM T_GOODS_CHANNEL_PRICE A
		LEFT JOIN (
			SELECT GCP.GOODS_ID,GCPE.BATCH_PRICE 
			FROM T_GOODS_CHANNEL_PRICE GCP
			LEFT JOIN T_GOODS_CHANNEL_PRICE_EXTEND GCPE ON GCP.GOODS_CHANNEL_PRICE_ID = GCPE.GOODS_CHANNEL_PRICE_ID
			WHERE GCP.GOODS_ID IN 
			<foreach collection="goodsIdList" item="goodsId" open="(" close=")" separator=",">
				#{goodsId,jdbcType=INTEGER}
			</foreach>
				AND GCP.PROVINCE_ID = #{provinceId,jdbcType=INTEGER}
			<if test="cityId == null">
				AND (GCP.CITY_ID IS NULL OR GCP.CITY_ID = 0)
			</if> 
			<if test="cityId != null">
				AND GCP.CITY_ID = #{cityId,jdbcType=INTEGER}
			</if> 
			<if test="nowTime!=null and nowTime!=0">
            	AND GCPE.END_TIME >= #{nowTime,jdbcType=BIGINT}
            </if>
            <if test="nowTime!=null and nowTime!=0">
				and GCPE.START_TIME <![CDATA[ <= ]]> #{nowTime,jdbcType=BIGINT}
			</if>
			AND GCPE.PRICE_TYPE = 1
			AND GCPE.CONDITION_TYPE = 1
			AND GCP.TYPE = #{type,jdbcType=INTEGER}
		) B ON A.GOODS_ID = B.GOODS_ID
		WHERE A.GOODS_ID IN 
			<foreach collection="goodsIdList" item="goodsId" open="(" close=")" separator=",">
				#{goodsId,jdbcType=INTEGER}
			</foreach>
				AND A.PROVINCE_ID = #{provinceId,jdbcType=INTEGER}
			<if test="cityId == null">
				AND (A.CITY_ID IS NULL OR A.CITY_ID = 0)
			</if> 
			<if test="cityId != null">
				AND A.CITY_ID = #{cityId,jdbcType=INTEGER}
			</if> 
			AND A.TYPE = 0
			AND SYSDATE() <![CDATA[ < ]]> DATE_ADD(A.PERIOD_DATE, INTERVAL 1 DAY)
	</select>
	
	<!-- 获取全部品牌信息 -->
	<select id="getGoodsChannelPriceByGoodsId" resultMap="BaseResultMap" parameterType="java.util.Map">
		select
		a.*, b.USERNAME
		from T_GOODS_CHANNEL_PRICE a join T_USER b on a.CREATOR = b.USER_ID 
		where
		1=1
		<if test="goodsId != null">
			and a.GOODS_ID = #{goodsId}
		</if>
		<if test="type != null">
			and a.TYPE = #{type}
		</if>
		<if test="provinceName != null">
			and	a.PROVINCE_NAME = #{provinceName,jdbcType=VARCHAR}
		</if>
		order by a.GOODS_CHANNEL_PRICE_ID asc
	</select>
	<!-- 根据sku+省市id查询核价采购端信息 -->
	<select id="getGoodsChannelPriceInfo" resultMap="BaseResultMap" parameterType="com.vedeng.goods.model.GoodsChannelPrice">
		SELECT
			*
		FROM
			T_GOODS_CHANNEL_PRICE
		WHERE
			GOODS_ID =#{goodsId,jdbcType=INTEGER}
		AND PROVINCE_ID =  #{provinceId,jdbcType=INTEGER}
		AND CITY_ID = #{cityId,jdbcType=INTEGER}
		AND TYPE = 1
		LIMIT  1
	</select>
	<!-- 重置产品核价信息 -->
	<delete id="delGoodsChannelPrice" parameterType="com.vedeng.goods.model.GoodsChannelPrice">
		delete from T_GOODS_CHANNEL_PRICE
		where GOODS_ID = #{goodsId,jdbcType=INTEGER}
	</delete>
	
	<select id="getGoodsChannelByGoodsId" parameterType="java.lang.Integer" resultType="com.vedeng.goods.model.GoodsChannelPrice">
		SELECT IFNULL(
		          IF(465 = 465,
		             A.DISTRIBUTION_PRICE,
		             IF(466 = 466, A.PRIVATE_PRICE, 0)),
		          0)
		          AS CHANNEL_PRICE,
		       A.GOODS_ID,A.DELIVERY_CYCLE
		FROM T_GOODS_CHANNEL_PRICE A
		WHERE A.GOODS_ID =	#{goodsId,jdbcType=INTEGER}
			AND A.PROVINCE_ID = #{provinceId,jdbcType=INTEGER} AND A.CITY_ID = #{cityId,jdbcType=INTEGER}
			AND SYSDATE() <![CDATA[ < ]]> DATE_ADD(A.PERIOD_DATE, INTERVAL 1 DAY)
	</select>
	 <resultMap id="ExtendBaseResultMap" type="com.vedeng.goods.model.GoodsChannelPrice" extends="BaseResultMap">  
	    <collection property="goodsChannelPriceExtendList"  ofType="com.vedeng.goods.model.GoodsChannelPriceExtend">  
	        <id column="GOODS_CHANNEL_PRICE_EXTEND_ID" property="goodsChannelPriceExtendId" jdbcType="INTEGER" />
		    <result column="GOODS_CHANNEL_PRICE_ID" property="goodsChannelPriceId" jdbcType="INTEGER" />
		    <result column="PRICE_TYPE" property="priceType" jdbcType="BIT" />
		    <result column="CONDITION_TYPE" property="conditionType" jdbcType="BIT" />
		    <result column="START_TIME" property="startTime" jdbcType="BIGINT" />
		    <result column="END_TIME" property="endTime" jdbcType="BIGINT" />
		    <result column="MIN_NUM" property="minNum" jdbcType="INTEGER" />
		    <result column="MAX_NUM" property="maxNum" jdbcType="INTEGER" />
		    <result column="BATCH_PRICE" property="batchPrice" jdbcType="DECIMAL" />
	    </collection>  
	 </resultMap>  
	 <!-- 商品授权与定价 -->
	 <select id="getGoodsChannelPriceList" parameterType="com.vedeng.goods.model.Goods" resultMap="ExtendBaseResultMap">
		SELECT
			*
		FROM
			T_GOODS_CHANNEL_PRICE a
		LEFT JOIN T_GOODS_CHANNEL_PRICE_EXTEND b ON a.GOODS_CHANNEL_PRICE_ID = b.GOODS_CHANNEL_PRICE_ID
		WHERE
			a.GOODS_ID = #{goodsId,jdbcType=INTEGER}
	</select>
	<select id="getGoodsChannelPriceListByChina" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" />
		FROM T_GOODS_CHANNEL_PRICE a
		WHERE a.GOODS_ID IN 
			<foreach collection="goodsIdList" item="goodsId" open="(" close=")" separator=",">
				#{goodsId,jdbcType=INTEGER}
			</foreach>
			<if test="provinceId != null">
				AND a.PROVINCE_ID = #{provinceId,jdbcType=INTEGER}
			</if>
	</select>
	<select id="getGoodsChannelPriceListAll" resultMap="BaseResultMap">
		SELECT GOODS_ID
			FROM T_GOODS_CHANNEL_PRICE a WHERE GOODS_ID IS NOT NULL
	</select>
	<select id="getGoodsChannelPriceByGoodsIdOne" parameterType="com.vedeng.goods.model.Goods" resultMap="BaseResultMap">
		SELECT
			*
		FROM
			T_GOODS_CHANNEL_PRICE a
		WHERE
			a.GOODS_ID = #{goodsId,jdbcType=INTEGER} LIMIT 1
	</select>
	<!-- 重置产品核价信息 -->
	<delete id="delGoodsChannelPriceById" parameterType="com.vedeng.goods.model.GoodsChannelPrice">
		delete from T_GOODS_CHANNEL_PRICE
		where GOODS_CHANNEL_PRICE_ID = #{goodsChannelPriceId,jdbcType=INTEGER}
	</delete>
</mapper>