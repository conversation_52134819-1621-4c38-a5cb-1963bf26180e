package com.vedeng.common.orderstrategy;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.orderstrategy.model.BuyorderAmount;
import com.vedeng.flash.dao.EarlyWarningTaskMapper;
import com.vedeng.flash.model.EarlyWarningTask;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.BuyorderGoods;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @ClassName BuyOrderAmountStrategy.java
 * @Description TODO 采购单算法策略
 * @createTime 2020年10月30日 13:36:00
 */
@Component
public class BuyOrderAmountStrategy extends OrderStrategy{
    public static Logger logger = LoggerFactory.getLogger(BuyOrderAmountStrategy.class);

    @Resource
    private BuyorderMapper buyorderMapper;

    @Resource
    private BuyorderGoodsMapper buyorderGoodsMapper;


    @Override
   // @Transactional 死锁
    public void execute(Object object) {
        Buyorder buyorder = (Buyorder) object;
        Integer orderId = buyorder.getBuyorderId();
        try {

            Buyorder updateOrder = new Buyorder();
            updateOrder.setBuyorderId(orderId);
            //订单付款金额
            BigDecimal paymentAmount = buyorderMapper.getPaymentAmount(orderId);
            if(paymentAmount == null){
                paymentAmount = BigDecimal.ZERO;
            }
            //采购单退款退货流水总金额
            BigDecimal afterReturnAmount = buyorderMapper.getAfterReturnAmount(orderId);
            if(afterReturnAmount == null){
                afterReturnAmount = BigDecimal.ZERO;
            }
            updateOrder.setRealPayAmount(paymentAmount.subtract(afterReturnAmount));
            updateOrder.setRealReturnAmount(afterReturnAmount);
            //订单未还账期金额
            BuyorderAmount buyorderAmount = buyorderMapper.getNoPaybackAmount(orderId);
            if(buyorderAmount == null || buyorderAmount.getNoPaybackAmount() == null){
                updateOrder.setNoPaybackAmount(BigDecimal.ZERO);
            }else{
                updateOrder.setNoPaybackAmount(buyorderAmount.getNoPaybackAmount());
            }

            List<BuyorderGoodsVo> buyorderGoodsVoList= buyorderGoodsMapper.getBuyorderGoodsVoListByBuyorderId(orderId);

            BigDecimal totalAmount = buyorder.getTotalAmount();
            //商品售后退货信息
            List<BuyorderGoods> afterReturnList = buyorderGoodsMapper.getAfterReturnInfo(orderId);

            //商品录票信息
            //采购商品录票总数,总额(蓝字有效)
            BigDecimal realInvoiceAmount = BigDecimal.ZERO;

            List<BuyorderGoods> buyorderInvoiceInfo = buyorderGoodsMapper.getBuyorderInvoiceInfo(orderId);

            Set<Integer> afterGoodsIds = new HashSet<>();
            Set<Integer> invoiceGoodsIds = new HashSet<>();

            for (BuyorderGoods buyorderGoods : afterReturnList) {
                if(buyorderGoods.getBuyorderGoodsId().equals(0)){
                    continue;
                }
                totalAmount = totalAmount.subtract(buyorderGoods.getAfterReturnAmount());
                logger.info("更新T_BUYORDER_GOODS：{}", JSON.toJSONString(buyorderGoods));
                buyorderGoodsMapper.updateBuyOrderAfterAmountInfo(buyorderGoods);
                afterGoodsIds.add(buyorderGoods.getBuyorderGoodsId());
            }

            for (BuyorderGoods buyorderGoods : buyorderInvoiceInfo) {
                logger.info("更新T_BUYORDER_GOODS：{}", JSON.toJSONString(buyorderGoods));
                buyorderGoodsMapper.updateBuyordeInvoiceInfo(buyorderGoods);
                realInvoiceAmount = realInvoiceAmount.add(buyorderGoods.getRealInvoiceAmount());
                invoiceGoodsIds.add(buyorderGoods.getBuyorderGoodsId());
            }

            //清除售后商品信息
            BuyorderGoods updateClearAfterGoods = new BuyorderGoods();
            updateClearAfterGoods.setAfterReturnAmount(BigDecimal.ZERO);
            updateClearAfterGoods.setAfterReturnNum(0);
            //清除开票商品信息
            BuyorderGoods updateClearInvoiceGoods = new BuyorderGoods();
            updateClearInvoiceGoods.setRealInvoiceAmount(BigDecimal.ZERO);
            updateClearInvoiceGoods.setRealInvoiceNum(BigDecimal.ZERO);

            for (BuyorderGoodsVo buyorderGoodsVo : buyorderGoodsVoList) {
                Integer buyorderGoodsId = buyorderGoodsVo.getBuyorderGoodsId();
                if(!afterGoodsIds.contains(buyorderGoodsId)){
                    updateClearAfterGoods.setBuyorderGoodsId(buyorderGoodsId);
                    logger.info("更新T_BUYORDER_GOODS：{}", JSON.toJSONString(updateClearAfterGoods));
                    buyorderGoodsMapper.updateBuyOrderAfterAmountInfo(updateClearAfterGoods);
                }
                if(!invoiceGoodsIds.contains(buyorderGoodsId)){
                    updateClearInvoiceGoods.setBuyorderGoodsId(buyorderGoodsId);
                    logger.info("更新T_BUYORDER_GOODS：{}", JSON.toJSONString(updateClearInvoiceGoods));
                    buyorderGoodsMapper.updateBuyordeInvoiceInfo(updateClearInvoiceGoods);
                }
            }

            updateOrder.setRealInvoiceAmount(realInvoiceAmount);
            updateOrder.setRealTotalAmount(totalAmount);
            if(updateOrder.getRealInvoiceAmount() == null){
                updateOrder.setRealInvoiceAmount(BigDecimal.ZERO);
            }
            logger.info("更新T_BUYORDER：{}", JSON.toJSONString(updateOrder));
            buyorderMapper.updateBuyorderAmount(updateOrder);
//            try {
//                buyorderMapper.updateSaleGoodsDataTimeByBuyOrderId(orderId);
//            }catch (Exception ee){
//                logger.error("",ee);
//            }
        }catch (Exception e){
            logger.error("BuyOrderAmountStrategy error buyorderId:"+orderId,e);
        }
    }
}
