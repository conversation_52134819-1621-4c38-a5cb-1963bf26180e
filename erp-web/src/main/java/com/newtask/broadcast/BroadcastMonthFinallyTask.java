package com.newtask.broadcast;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.broadcast.statistics.project.BroadcastMonth;
import com.vedeng.erp.common.broadcast.param.TimePeriod;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;

import java.util.Calendar;
import java.util.Date;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 月到款最终数据落表，每月第一天0点执行，执行前一月的数据
 * @ClassName:  BroadcastMonthTask   
 * @author: Neil.yang
 * @date:   2025年6月18日 下午1:04:01    
 * @Copyright:
 */
@JobHandler(value = "broadcastMonthFinallyTask")
@Component
public class BroadcastMonthFinallyTask extends AbstractJobHandler {

    Logger logger = LoggerFactory.getLogger(BroadcastMonthFinallyTask.class);
    
    @Autowired
    private BroadcastMonth broadcastMonth;
    
    @Override
    public ReturnT<String> doExecute(String params) throws Exception {
        XxlJobLogger.log("月到款最终数据落表 start-----------");
        logger.info("月到款最终数据落表  start-----------");
        try {
        	Calendar calendar = Calendar.getInstance();
            
            // 获取上个月
            calendar.add(Calendar.MONTH, -1);
            
            // 设置上个月第一天00:00:00
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            Date firstDay = calendar.getTime();
            
            // 获取上个月最后一天
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            calendar.set(Calendar.MILLISECOND, 999);
            Date lastDay = calendar.getTime();
        	TimePeriod timePeriod = new TimePeriod(null,null,firstDay,lastDay);
        	broadcastMonth.invocation(timePeriod,false);
        }catch(Exception e) {
        	XxlJobLogger.log("月到款最终数据落表失败");
        	logger.error("月到款最终数据落表失败");
        	return FAIL;
        }
        logger.info("月到款最终数据落表 end-----------");
        XxlJobLogger.log("月到款最终数据落表 end-----------");
        return SUCCESS;
    }
    
    
    
    public static void main(String[] args) {
    	Calendar calendar = Calendar.getInstance();
        
        // 获取上个月
        calendar.add(Calendar.MONTH, -1);
        
        // 设置上个月第一天00:00:00
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date firstDay = calendar.getTime();
        
        // 获取上个月最后一天
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        Date lastDay = calendar.getTime();
    	System.out.println(firstDay);
    	System.out.println(lastDay);
	}
}
