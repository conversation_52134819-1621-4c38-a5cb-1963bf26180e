package com.vedeng.erp.kingdee.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeeSaleOutStockCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeSaleOutStockEntity;
import com.vedeng.erp.kingdee.dto.KingDeeSaleOutStockDto;
import com.vedeng.erp.kingdee.dto.result.KingDeeSaleOutStockQueryResultDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeSaleOutStockCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeSaleOutStockConvertor;
import com.vedeng.erp.kingdee.repository.KingDeeSaleOutStockRepository;
import com.vedeng.erp.kingdee.service.KingDeeSaleOutStockApiService;
import com.vedeng.erp.kingdee.service.KingDeeSaleOutStockService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryExtParam;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryFilterDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 销售出库单
 * @author: guoning
 * @date: 2023/2/2 10:00
 **/
@Service
@Slf4j
public class KingDeeSaleOutStockServiceImpl extends KingDeeBaseServiceImpl<
        KingDeeSaleOutStockEntity,
        KingDeeSaleOutStockDto,
        KingDeeSaleOutStockCommand,
        KingDeeSaleOutStockRepository,
        KingDeeSaleOutStockConvertor,
        KingDeeSaleOutStockCommandConvertor> implements KingDeeSaleOutStockService, KingDeeSaleOutStockApiService {

    @Override
    public List<KingDeeSaleOutStockQueryResultDto> getKingDeeSaleOutStock(String outInNo) {
        // 调用查询金蝶接口
        KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
        queryParam.setFormId(KingDeeFormConstant.SAL_OUT_STOCK);
        List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
        queryFilterDtos.add(KingDeeQueryFilterDto.builder().fieldName("fBillNo").value(outInNo).build());
        queryParam.setFilterString(queryFilterDtos);
        log.info("金蝶销售出库单查询入参：{}", JSON.toJSONString(queryParam));
        return kingDeeBaseApi.query(queryParam, KingDeeSaleOutStockQueryResultDto.class);
    }

    @Override
    public List<KingDeeSaleOutStockQueryResultDto> getKingDeeSaleOutStock(String f_qzok_bddjtid, String f_qzok_bddjhid) {
        // 调用查询金蝶接口
        KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
        queryParam.setFormId(KingDeeFormConstant.SAL_OUT_STOCK);
        List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
        queryFilterDtos.add(KingDeeQueryFilterDto.builder()
                .fieldName("F_QZOK_BDDJTID")
                .value(f_qzok_bddjtid).build());
        queryFilterDtos.add(KingDeeQueryFilterDto.builder()
                .fieldName("F_QZOK_BDDJHID")
                .value(f_qzok_bddjhid).build());
        queryParam.setFilterString(queryFilterDtos);
        log.info("金蝶销售出库单查询入参：{}", JSON.toJSONString(queryParam));
        return kingDeeBaseApi.query(queryParam, KingDeeSaleOutStockQueryResultDto.class);
    }

}
