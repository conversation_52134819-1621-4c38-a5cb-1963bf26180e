package com.vedeng.erp.finance.mapper;

import com.vedeng.erp.finance.domain.entity.CheckRuleEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CheckRuleMapper {
    int deleteByPrimaryKey(Long checkRuleId);

    int insert(CheckRuleEntity record);

    int insertOrUpdate(CheckRuleEntity record);

    int insertOrUpdateSelective(CheckRuleEntity record);

    int insertSelective(CheckRuleEntity record);

    CheckRuleEntity selectByPrimaryKey(Long checkRuleId);

    int updateByPrimaryKeySelective(CheckRuleEntity record);

    int updateByPrimaryKey(CheckRuleEntity record);

    int updateBatch(List<CheckRuleEntity> list);

    int updateBatchSelective(List<CheckRuleEntity> list);

    int batchInsert(@Param("list") List<CheckRuleEntity> list);

    List<CheckRuleEntity> queryByRuleType(Integer ruleType);
}