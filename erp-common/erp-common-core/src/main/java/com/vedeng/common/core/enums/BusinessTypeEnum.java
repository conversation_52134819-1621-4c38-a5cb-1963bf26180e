package com.vedeng.common.core.enums;

import com.vedeng.common.core.exception.ServiceException;

import java.util.Arrays;

/**
 * 业务类型
 * 1.采购单
 * 2.采购费用单
 * 3.采购售后单
 * 4.采购费用售后单
 * 5.采购返利结算收款申请
 *
 * <AUTHOR>
 */
public enum BusinessTypeEnum {


    /**
     * 销售单
     */
    saleOrder("saleOrder", "销售单"),

    /**
     * 采购单
     */
    buyOrder("buyOrder", "采购单"),
    /**
     * 采购费用单
     */
    buyOrderExpense("buyOrderExpense", "采购费用单"),
    /**
     * 采购售后单
     */
    buyOrderAfterSale("buyOrderAfterSale", "采购售后单"),
    /**
     * 采购费用售后单
     */
    buyOrderExpenseAfterSale("buyOrderExpenseAfterSale", "采购费用售后单");

    private final String code;

    private final String type;

    BusinessTypeEnum(String code, String type) {
        this.code = code;
        this.type = type;
    }

    public String getCode() {
        return code;
    }

    public String getType() {
        return type;
    }


    /**
     * 获取资产业务枚举
     *
     * @param code 业务类型
     * @return KingDeeBizEnums
     */
    public static BusinessTypeEnum getEnum(String code) {
        return Arrays.stream(BusinessTypeEnum.values())
                .filter(enums -> enums.getCode().equals(code))
                .findFirst().orElseThrow(() -> new ServiceException("无法获取对应的业务类型"));
    }

}
