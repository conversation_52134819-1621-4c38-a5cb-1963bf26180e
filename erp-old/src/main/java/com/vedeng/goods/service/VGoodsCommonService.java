package com.vedeng.goods.service;

import com.vedeng.authorization.model.User;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.goods.model.CoreSpuGenerate;

public interface VGoodsCommonService {
    ResultInfo checkDisableSpuOrSku(User user, Integer relatedId,Integer type,String reason);

    ResultInfo checkAbleSpuOrSku(User user, Integer relatedId,Integer type);

    ResultInfo checkDisableSpuOrSkuWithoutReason(User user, Integer relatedId, Integer goodsType);
}
