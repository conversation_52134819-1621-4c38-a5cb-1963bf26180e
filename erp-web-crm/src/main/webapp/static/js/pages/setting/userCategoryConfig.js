void function () {
    new Vue({
        el: '#page-container',
        data: {
            tableHeaders: [
                {
                    key: "userName",
                    label: "业务人员",
                    width: "150px",
                },
                {
                    key: "department",
                    label: "部门",
                    width: "300px"
                },
                {
                    key: "userPositions",
                    label: "职位",
                    width: "240px"
                },
                {
                    key: "categoryCount",
                    label: "商品分类",
                    width: "80px",
                    align: 'center'
                },
                {
                    key: "option",
                    label: "操作",
                    width: "140px",
                },
            ],
            searchParams: {
                categoryIdList: [],
                businessUserIds: [],
                employmentStatus: '',
                departments: [],
            },
            filterUserRemoteInfo: {
                url: '/crm/role/profile/role-user-category-config/all-business-users',
                paramsType: 'url',
                paramsKey: 'userName',
                parseLabel: 'userName',
                parseValue: 'userId',
                parseAvatar: 'userAvatar'
            },
            userStatusList: [{
                label: '离职',
                value: '1'
            }, {
                label: '在职',
                value: '0'
            }],
            loading: true,
            isShowEditDialog: false,
            editTitle: '',
            editId: '',
            editInfo: {},
            userData: [], //选择销售的级联数据
            categoryList: [], //选择分类的级联数据
            departmentData: [], //筛选的部门数据
            filterCategoryData: [], //筛选的分类数据
            formUsers: [],
            formCategorys: [],
            cansubmit: true,
        },
        created() {
            this.loading = false;
            this.getUserList();
            this.getCategoryList();
        },
        methods: {
            getUserList() {
                this.$axios.get(`/crm/user/profile/getFullDepartmentTree`).then(({ data }) => {
                    if(data.success) {
                        let departmentData = [];

                        let allDep = data.data.childOrganization[0].childOrganization;

                        this.userData = this.setUserData(allDep);

                        allDep.forEach(item1 => {
                            let item1Data = {
                                label: item1.organizationName,
                                value: item1.organizationId,
                                children: []
                            }

                            if(item1.childOrganization && item1.childOrganization.length) {
                                item1.childOrganization.forEach(item2 => {
                                    let item2Data = {
                                        label: item2.organizationName,
                                        value: item2.organizationId,
                                        children: []
                                    }

                                    if(item2.childOrganization && item2.childOrganization.length) {
                                        item2.childOrganization.forEach(item3 => {
                                            let item3Data = {
                                                label: item3.organizationName,
                                                value: item3.organizationId,
                                            }

                                            item2Data.children.push(item3Data);
                                        })
                                    }

                                    item1Data.children.push(item2Data);
                                })

                                departmentData.push(item1Data);
                            }
                        })

                        this.departmentData = departmentData;
                    }
                })
            },
            setUserData(list) {
                let resList = [];

                list.forEach(item => {
                    let itemData = {
                        departmentName: item.organizationName,
                        departmentId: item.organizationId,
                        userList: item.users
                    }

                    if(item.childOrganization && item.childOrganization.length) {
                        itemData.childDepartment = this.setUserData(item.childOrganization);
                    }

                    resList.push(itemData);
                }) 

                return resList;
            },
            setCategoryData(list) {
                let resList = [];
                let categorys = [];

                list.forEach(item => {
                    let itemData = {
                        departmentName: item.baseCategoryName,
                        departmentId: item.baseCategoryId,
                        childDepartment: []
                    }

                    let cateItem = {
                        label: item.baseCategoryName,
                        value: item.baseCategoryId,
                        children: []
                    }

                    if(item.children && item.children.length) {
                        item.children.forEach(item2 => {
                            let item2Data = {
                                departmentName: item2.baseCategoryName,
                                departmentId: item2.baseCategoryId,
                            }

                            let cate2Data = {
                                label: item2.baseCategoryName,
                                value: item2.baseCategoryId,
                                children: []
                            }
        
                            if(item2.children && item2.children.length) {
                                let users = [];
                                
                                item2.children.forEach(item3 => {
                                    users.push({
                                        userId: item3.baseCategoryId,
                                        userName: item.baseCategoryName + '/' + item2.baseCategoryName + '/' + item3.baseCategoryName
                                    })
                                    cate2Data.children.push({
                                        label: item3.baseCategoryName,
                                        value: item3.baseCategoryId
                                    })
                                })

                                item2Data.userList = users;
                            }
        
                            itemData.childDepartment.push(item2Data);
                            cateItem.children.push(cate2Data);
                        }) 
                    }

                    resList.push(itemData);
                    categorys.push(cateItem);
                }) 

                this.categoryList = resList;
                this.filterCategoryData = categorys;
            },
            getCategoryList() {
                this.$axios.get(`/crm/category/public/getAllCategory`).then(({ data }) => {
                    if(data.success) {
                        this.setCategoryData(data.data || []);
                    }
                })
            },
            remoteSelectChange(key) {
                this.searchParams[key + 'List'] = [this.searchParams[key]]
            },
            parseFilterCategory(ids, list) {
                let resIds = [];
                if(!list) {
                    list = this.filterCategoryData;
                }
                list.forEach(item => {
                    if(ids.indexOf(item.value) !== -1) {
                        if(item.children && item.children.length) {
                            let cIds = [];
                            item.children.forEach(item1 => {
                                cIds.push(item1.value);
                            })
                            resIds = resIds.concat(this.parseFilterCategory(cIds, item.children));
                        } else {
                            resIds.push(item.value);
                        }
                    } else {
                        if(item.children && item.children.length) {
                            item.children.forEach(item1 => {
                                if(ids.indexOf(item1.value) !== -1) {
                                    if(item1.children && item1.children.length) {
                                        let cIds = [];
                                        item1.children.forEach(item2 => {
                                            cIds.push(item2.value);
                                        })
                                        resIds = resIds.concat(this.parseFilterCategory(cIds, item1.children));
                                    } else {
                                        resIds.push(item.value);
                                    }
                                }
                            })
                        }
                    }
                })

                return resIds;
            },
            handleFilterCategoryChange(value) {
                this.searchParams.categoryIdList = this.parseFilterCategory(value.level1).concat(this.parseFilterCategory(value.level2).concat(value.level3));

                console.log(this.searchParams.categoryIdList);
            },
            handleFilterDepartChange(value) {
                let arr = [];
                if(value) {
                    arr = value.level1.concat(value.level2.concat(value.level3));
                }

                this.searchParams.departments = arr;
            },
            getPositionStr(list) {
                if(list && list.length) {
                    let arr = [];

                    list.forEach(item => {
                        arr.push(item.positionName);
                    })

                    return arr.join('/');
                } else {
                    return '-';
                }
            },
            deleteItem(item) {
                if (!GLOBAL.auth('C0603')) {
                    GLOBAL.showNoAuth();
                    return;
                }

                let _this = this;
                this.$popup.warn({
                    message: `是否删除"${item.userName}"的商品分类关系？`,
                    buttons: [{
                        txt: '删除',
                        btnClass: 'delete',
                        callback() {
                            _this.submitDelete([item.id]);
                        }
                    },
                    {
                        txt: '取消',
                    }]
                })
            },
            multiDelete() {
                if (!GLOBAL.auth('C0603')) {
                    GLOBAL.showNoAuth();
                    return;
                }

                let ids = this.$refs.listContainer.getSelectedData('id');
                let _this = this;

                if (!ids || !ids.length) {
                    this.$message.warn('请勾数据选行');
                } else {
                    this.$popup.warn({
                        message: '是否确认删除？',
                        buttons: [{
                            txt: '删除',
                            btnClass: 'delete',
                            callback() {
                                _this.submitDelete(ids);
                            }
                        },
                        {
                            txt: '取消',
                        }]
                    })
                }
            },
            submitDelete(ids) {
                this.$axios.post('/crm/role/profile/role-user-category-config/batch-delete', ids).then(({data}) => {
                    if(data.success) {
                        this.$refs.listContainer.refresh();
                    } else {
                        this.$message.warn(data.message);
                    }
                })
            },
            addConfig() {
                if (!GLOBAL.auth('C0601')) {
                    GLOBAL.showNoAuth();
                    return;
                }

                let rules = {
                    formUsers: {
                        required: '请选择业务人员'
                    },
                    formCategorys: {
                        required: '请选择商品分类'
                    }
                };
                
                this.$form && this.$form.rules(rules, 'AddSalerCategory', this);

                this.formUsers = [];
                this.formCategorys = [];
                this.editId = '';

                this.editTitle = '添加商品分类';
                this.isShowEditDialog = true;
            },
            editConfig(row) {
                if (!GLOBAL.auth('C0602')) {
                    GLOBAL.showNoAuth();
                    return;
                }

                let rules = {
                    formCategorys: {
                        required: '请选择商品分类'
                    }
                };
                
                this.$form && this.$form.rules(rules, 'AddSalerCategory', this);

                this.formCategorys = row.categoryIds.split(',').map(Number);
                this.editId = row.id;       
                this.editInfo = row;                 

                this.editTitle = '编辑商品分类';
                this.isShowEditDialog = true;
            },
            copyItem(row) {
                if (!GLOBAL.auth('C0601')) {
                    GLOBAL.showNoAuth();
                    return;
                }

                let rules = {
                    formUsers: {
                        required: '请选择业务人员'
                    },
                    formCategorys: {
                        required: '请选择商品分类'
                    }
                };
                
                this.$form && this.$form.rules(rules, 'AddSalerCategory', this);

                this.formCategorys = row.categoryIds.split(',').map(Number);
                
                if(row.userStatus === 0) {
                    this.formUsers = [row.userId];
                }

                this.editId = '';

                this.editTitle = '复制商品分类';
                this.isShowEditDialog = true;
            },
            submitCategoryConfig() {
                if(!this.$form.validForm('AddSalerCategory') || !this.cansubmit) {
                    return;
                }

                this.cansubmit = false;

                let url = '/crm/role/profile/role-user-category-config/add';
                let reqData = {
                    userIds: this.formUsers,
                    categoryIds: this.formCategorys.join(',')
                }

                if(this.editId) {
                    url = '/crm/role/profile/role-user-category-config/update';
                    reqData = {
                        id: this.editId,
                        userId: this.editInfo.userId,
                        categoryIds: this.formCategorys.join(',')
                    }
                }

                this.$axios.post(url, reqData).then(({data}) => {
                    if(data.success) {
                        this.$message.success(this.editId ? '编辑成功' : '添加成功');
                        this.isShowEditDialog = false;

                        if(!this.editId) {
                            setTimeout(() => {
                                window.location.reload();
                            }, 1000);
                        } else {
                            this.cansubmit = true;
                            this.$refs.listContainer.refresh();
                        }
                    } else {
                        this.$message.warn(data.message);
                        this.cansubmit = true;
                    }
                })
            }
        }
    })
}.call(this);