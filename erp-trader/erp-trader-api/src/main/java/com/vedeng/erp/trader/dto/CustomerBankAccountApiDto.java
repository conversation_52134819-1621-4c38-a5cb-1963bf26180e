package com.vedeng.erp.trader.dto;

import com.vedeng.common.core.base.BaseDto;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 客户账户表
 */
@Getter
@Setter
public class CustomerBankAccountApiDto extends BaseDto {
    /**
     * 主键
     */
    private Long customerBankAccountId;

    /**
     * 交易者ID
     */
    private Integer traderId;

    /**
     * 账户类型 1银行 2微信 3支付宝
     */
    private Integer accountType;

    /**
     * 账号
     */
    private String accountNo;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 银行ID
     */
    private Integer bankId;

    /**
     * 联行号
     */
    private String bankCode;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 是否验证 0否 1是
     */
    private Integer isVerify;

    /**
     * 是否可用 0否 1是
     */
    private Integer isEnable;

    /**
     * 是否删除 0否 1是
     */
    private Integer isDelete;

    private Date lastUseTime;

    /**
     * 标签
     */
    private Integer tag;

    /**
     * 回单银行名称
     */
    private String receiptBankName;

    /**
     * 回单解析id
     */
    private Long bankReceiptAliasId;


    /**
     * 开户银行
     */
    private Integer openingBank;

    private String updateRemark;

    public Integer getOpeningBank() {
        if (this.bankId == null || this.bankId == 0) {
            return null;
        }
        return this.bankId;
    }
}
