package com.vedeng.erp.saleorder.strategy;

import com.alibaba.fastjson.JSONObject;
import com.vedeng.erp.saleorder.dao.SaleOrderMapper;
import com.vedeng.erp.saleorder.domain.entity.SaleorderEntity;
import com.vedeng.erp.saleorder.dto.OrderTerminalDto;
import com.vedeng.erp.saleorder.service.OrderTerminalApiService;
import com.vedeng.erp.system.service.RegionApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 销售单终端信息处理类
 * @Date 2023/9/5 13:23
 */
@Service
@Slf4j
public class SaleOrderTerminalStrategy implements OrderTerminalStrategy {

    @Autowired
    private OrderTerminalApiService orderTerminalApiService;

    @Autowired
    private SaleOrderMapper saleOrderMapper;

    @Autowired
    private RegionApiService regionApiService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void execute(OrderTerminalDto orderTerminalDto) {
        // 只有当必填项 省市区有值 才存
//        if (Objects.nonNull(orderTerminalDto.getAreaId()) && orderTerminalDto.getAreaId() > 0) {
            orderTerminalDto.setBusinessType(0);
            SaleorderEntity update = new SaleorderEntity();
            update.setSaleorderId(orderTerminalDto.getBusinessId());
            orderTerminalApiService.save(orderTerminalDto);
            update.setSalesAreaId(orderTerminalDto.getAreaId());
            String salesAreaName = getRegionName(orderTerminalDto.getAreaId());
            update.setSalesArea(salesAreaName);
            update.setTerminalTraderName(orderTerminalDto.getTerminalName());
            update.setTerminalTraderNature(orderTerminalDto.getTerminalTraderNature());
            update.setTerminalTraderId(0);
            update.setTerminalTraderType(0);
            log.info("保存终端信息:{}",new Object[]{JSONObject.toJSONString(update)});
            saleOrderMapper.updateByPrimaryKeySelective(update);
//        }
    }



    private String getRegionName(Integer areaId){
        if(Objects.isNull(areaId)){
            return StringUtils.EMPTY;
        }
        if(areaId == 0){
            return null;
        }
        String regionName = regionApiService.getThisRegionToParentRegionP(areaId);
        if(StringUtils.isBlank(regionName)){
            return StringUtils.EMPTY;
        }
        return regionName;

    }

}
