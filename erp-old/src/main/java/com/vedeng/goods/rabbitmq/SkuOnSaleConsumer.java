package com.vedeng.goods.rabbitmq;

import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import com.vedeng.common.trace.mq.AbstractMessageListener;
import com.vedeng.common.util.StringUtil;
import com.vedeng.goods.service.VgoodsService;
import com.vedeng.op.api.dto.sku.SkuOnSaleDto;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 前台推送商机消息
 * <AUTHOR>
 * @date 2020/07/28
 **/
@Component
public class SkuOnSaleConsumer extends AbstractMessageListener {

    public static final Logger LOGGER = LoggerFactory.getLogger(SkuOnSaleConsumer.class);
    @Autowired
    private VgoodsService vgoodsService;
    @Override
    public void doBusiness(Message message, Channel channel) throws Exception {
        try {
            String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);

            if (StringUtil.isBlank(messageBody)) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(),false);
                return;
            }
            LOGGER.info("op推送商品上下架状态, params: {}", messageBody);
            List<SkuOnSaleDto> list = JSONObject.parseArray(messageBody,SkuOnSaleDto.class);
            if(CollectionUtils.isNotEmpty(list)){
                for(SkuOnSaleDto dto:list) {
                    vgoodsService.updateSkuOnSale(dto);
                }
            }

            channel.basicAck(message.getMessageProperties().getDeliveryTag(),false);
        }catch (Exception ex){
            LOGGER.error("op推送商品上下架状态出错",ex);
            try{channel.basicNack(message.getMessageProperties().getDeliveryTag(),false,false);}catch (Exception exception){
                LOGGER.error("p推送商品上下架状态出错，将消息返回给rabbitmq错误：",exception);
            }
        }
    }
}
