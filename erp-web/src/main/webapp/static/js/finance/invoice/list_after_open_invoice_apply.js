function saveOpenInvoiceAudit(invoiceApplyId,status){
	layer.confirm("您是否确认审核不通过？", {
		btn : [ '确定', '取消' ]
	}, function() {
		checkLogin();
		$.ajax({
			async : true,
			url : './saveOpenInvoiceAudit.do',
			data : {"invoiceApplyId":invoiceApplyId,"validStatus":status},
			type : "POST",
			dataType : "json",
			success : function(data) {
				refreshNowPageList(data);
			},error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
	});
}
