package com.vedeng.infrastructure.feign.uac;


import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.uac.api.constants.UacConstants;
import com.vedeng.uac.api.dto.*;
import feign.Headers;
import feign.Param;
import feign.RequestLine;
import me.chanjar.weixin.cp.bean.WxCpChat;
import me.chanjar.weixin.cp.bean.message.WxCpAppChatMessage;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 微信用户Uac查询服务
 */
@FeignApi(serverName = "uacServer")
public interface UacWxUserInfoApiService {


    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /api/user/{jobNumber}")
    RestfulResult<UserDTO> getUserInfoByJobNumber(@Param("jobNumber") String jobNumber);


    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /api/wxuser/getWxUserListByUserId")
    RestfulResult<List<WxUserDto>> getWxUserListByUserId(@RequestBody List<Integer> idList);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /api/wxuser/sendMsg")
    RestfulResult sendMsg(@RequestBody MessageSendDto messageSendDto);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /api/wxuser/sendMsgTempleteForLxCrm")
    RestfulResult sendMsgTempleteForLxCrm(@RequestBody MessageTempleteDto messageTempleteDto);
    
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /api/wxuser/updateMsgTempleteForLxCrm?responseCode={responseCode}&toUserId={toUserId}")
	RestfulResult updateMsgTempleteForLxCrm(@Param("responseCode") String responseCode,@Param("toUserId") String toUserId);
    
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /api/wxuser/sendMsgForLxCrm")
    RestfulResult sendMsgForLxCrm(@RequestBody MessageSendDto messageSendDto);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /api/wxuser/sendBatchMsg")
    RestfulResult sendBatchMsg(@RequestBody BatchMessageSendDto messageSendDto);
    
    @RequestLine("GET /api/wxuser/jobNumber?jobNumber={jobNumber}")
    RestfulResult<WxUserDto> getWxUserByJobNumber(@Param("jobNumber") String jobNumber);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("GET /api/account/user/{accountId}")
    RestfulResult<WxUserDto> getByAccountId(@Param("accountId") Integer accountId);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /api/appchat/create")
    RestfulResult<AppchatResDto> create(@RequestBody AppChatReqDto appChatReqDto);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /api/appchat/update")
    RestfulResult<Void> update(@RequestBody AppChatReqDto appChatReqDto);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /api/appchat/get")
    RestfulResult<WxCpChat> get(@RequestBody AppChatReqDto appChatReqDto);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /api/appchat/send")
    RestfulResult<Void> send(@RequestBody WxCpAppChatMessage message);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /api/appchat/sendToUser")
    RestfulResult<Void> sendToUser(@RequestBody WxCpMessage message);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /api/uac/user/department")
    RestfulResult<Void> sendToAll(@RequestBody WxCpMessage message);


    @RequestLine("GET /api/uac/user/department?departmentId={departmentId}")
    RestfulResult<List<UserInfoDto>> getUserListByDepartmentId(@Param("departmentId") Integer departmentId);


    @RequestLine("GET /api/uac/user/getUserByDepartment?departmentId={departmentId}")
    RestfulResult<List<UserInfoDto>> getUserByDepartment(@Param("departmentId") Integer departmentId);

    /**
     * 请求获取指定用户的所有子用户信息
     *
     * @param userId userId
     * @return RestfulResult<List < UserInfoDto>>
     */
    @RequestLine("GET /api/uac/user/getAllSubUser?userId={userId}")
    RestfulResult<List<UserInfoDto>> getAllSubUser(@Param("userId") Integer userId);


    /**
     * 请求获取指定用户的所有子用户信息
     *
     * @param userId userId
     * @return RestfulResult<List < UserInfoDto>>
     */
    @RequestLine("GET /api/uac/user?userId={userId}&enable={enable}")
    RestfulResult<UserInfoDto> getUser(@Param("userId") Integer userId, @Param("enable") Integer enable);

    /**
     * 根据用户名搜索
     *
     * @param userName userName
     * @return RestfulResult<List < UserInfoDto>>
     */
    @RequestLine("GET /api/uac/user/search?userName={userName}")
    RestfulResult<List<UserInfoDto>> search(@Param("userName") String userName);


    @RequestLine("GET /api/uac/user/searchIsParent")
    RestfulResult<List<UserInfoDto>> searchIsParent();


    @RequestLine("GET /api/uac/getUserByUserId?userId={userId}")
    RestfulResult<UserInfoDto> getUserByUserId(@Param("userId") Integer userId);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /api/uac/getUserByUserIds")
    RestfulResult<List<UserInfoDto>> getUserByUserIds(@RequestBody List<Integer> userIds);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("GET /api/account/permission/{accountId}/{platformId}")
    RestfulResult<List<PermissionDTO>> getPermissionByAccount(@Param("accountId") Integer accountId, @Param("platformId") Integer platformId);


    /**
     * 根据用户ID列表获取用户详细信息（用户名、头像、部门、职位、在职状态）
     *
     * @param userIdList 用户id
     * @return 用户及部门信息Dto
     */
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /api/uac/users/details")
    RestfulResult<List<UserDetailInfoDTO>> getUserDetailsByIds(@RequestBody List<Integer> userIdList);

    /**
     * 根据用户在离职状态查询用户信息
     *
     * @param status 用户在离职状态（0-在职，1-离职）
     * @return 用户信息列表
     */
    @RequestLine("GET /api/uac/user/status?status={status}")
    RestfulResult<List<UserInfoDto>> getUserByStatus(@Param("status") Integer status);

    /**
     * 获取所有部门树形结构
     *
     * @return 部门树形结构
     */
    @RequestLine("GET /api/uac/department/tree")
    RestfulResult<List<OrganizationResDTO>> departmentTreeInfo();

    /**
     * 根据多个部门id查询这些部门下所有的用户
     *
     * @param departmentIds 部门id列表
     * @return List<UserInfoDto>
     */
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /api/uac/user/departments")
    RestfulResult<List<UserInfoDto>> getUserListByDepartmentIds(@RequestBody List<Integer> departmentIds);


    /**
     * 获取完整的部门和成员树形结构
     *
     * @return 部门和成员树形结构
     */
    @RequestLine("GET /api/uac/department/tree/full")
    RestfulResult<OrganizationResDTO> getFullDepartmentTree();

    /**
     * 获取我的完整的部门和成员树形结构
     *
     * @return 部门和成员树形结构
     */
    @RequestLine("GET /api/uac/department/tree/myfull?userId={userId}")
    RestfulResult<OrganizationResDTO> getMyFullDepartmentTree(@Param("userId") Integer userId);


}
