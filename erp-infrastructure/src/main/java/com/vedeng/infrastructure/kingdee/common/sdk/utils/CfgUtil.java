package com.vedeng.infrastructure.kingdee.common.sdk.utils;

import com.vedeng.infrastructure.kingdee.common.sdk.entity.AppCfg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.Properties;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class CfgUtil {

    static String cfgFilePath = null;
    static AppCfg instance;


    @Autowired
    public CfgUtil(AppCfg instance) {
        CfgUtil.instance = instance;
    }

    static void setCfgFilePath(String cfgPath) {
        cfgFilePath = cfgPath;
    }

    public static AppCfg getAppDefaultCfg() {
        File file;
        if (instance != null) {
            return instance;
        }
        AppCfg cfg = null;
        Properties properties = new Properties();
        boolean isLoad = false;
        try {
            if (cfgFilePath != null) {
                file = new File(cfgFilePath);
                log.info("configuration_path " + file.getPath());
            } else {
                file = new File(new File(".").getCanonicalPath() + "/kdwebapi.properties");
                log.info("configuration_path " + file.getPath());
            }
            if (!file.exists()) {
                InputStream inputStream = CfgUtil.class.getResourceAsStream("kdwebapi.properties");
                if (inputStream == null) {
                    inputStream = CfgUtil.class.getResourceAsStream("/kdwebapi.properties");
                }
                if (inputStream == null) {
                    inputStream = CfgUtil.class.getResourceAsStream("/resource/kdwebapi.properties");
                }
                if (inputStream != null) {
                    properties.load(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
                    inputStream.close();
                    isLoad = true;
                }
            } else {
                InputStream inputStream2 = new FileInputStream(file.getPath());
                log.info("configuration_path " + file.getPath());
                properties.load(new InputStreamReader(inputStream2, StandardCharsets.UTF_8));
                inputStream2.close();
                isLoad = true;
            }
        } catch (IOException e) {
            log.error("【getAppDefaultCfg】处理异常",e);
        }
        if (isLoad) {
            AppCfg scfg = new AppCfg();
            String svr = properties.getProperty("X-KDApi-ServerUrl");
            if (svr != null) {
                scfg.setServerUrl(svr);
            }
            scfg.setdCID(properties.getProperty("X-KDApi-AcctID"));
            scfg.setUserName(properties.getProperty("X-KDApi-UserName"));
            scfg.setAppId(properties.getProperty("X-KDApi-AppID"));
            scfg.setAppSecret(properties.getProperty("X-KDApi-AppSec"));
            String lcid = properties.getProperty("X-KDApi-LCID");
            if (lcid != null) {
                scfg.setlCID(new Integer(lcid));
            }
            scfg.setOrgNum(properties.getProperty("X-KDApi-OrgNum"));
            String timeout = properties.getProperty("X-KDApi-ConnectTimeout");
            if (timeout != null) {
                scfg.setConnectTimeout(new Integer(timeout));
            }
            String timeout2 = properties.getProperty("X-KDApi-RequestTimeout");
            if (timeout2 != null) {
                scfg.setRequestTimeout(new Integer(timeout2));
            }
            String timeout3 = properties.getProperty("X-KDApi-StockTimeout");
            if (timeout3 != null) {
                scfg.setStockTimeout(new Integer(timeout3));
            }
            scfg.setProxy(properties.getProperty("X-KDApi-Proxy"));
            cfg = scfg;
        }
        instance = cfg;
        return instance;
    }
}
