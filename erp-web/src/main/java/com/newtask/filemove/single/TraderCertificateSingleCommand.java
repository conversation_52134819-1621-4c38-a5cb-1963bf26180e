package com.newtask.filemove.single;

import com.google.common.collect.Lists;
import com.newtask.filemove.AbstractFileMoveInterface;
import com.newtask.filemove.dto.CommonConstant;
import com.newtask.filemove.dto.FileMoveDto;
import com.newtask.filemove.theadpool.TraderCertificateFileMoveTask;
import com.vedeng.filemove.dao.FileMoveDao;
import com.vedeng.trader.model.TraderCertificate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 客户资质单处理命令
 */
@Service
public class TraderCertificateSingleCommand extends AbstractFileMoveInterface {

    @Resource
    private FileMoveDao fileMoveDao;

    @Override
    protected String getCommandType() {
        return CommonConstant.SINGLE;
    }

    @Override
    protected List<FileMoveDto> getPendingList(String requsetParam) {

        String[] params = requsetParam.split(",");

        List<TraderCertificate> traderCertificateList = fileMoveDao.getTraderCertificateByParam(Integer.valueOf(params[0]),Integer.valueOf(params[1]));

        return Lists.transform(traderCertificateList, (entity) -> {
                                                            FileMoveDto fileMoveDto = new FileMoveDto();
                                                            fileMoveDto.setId(entity.getTraderCertificateId());
                                                            fileMoveDto.setDomain(entity.getDomain());
                                                            fileMoveDto.setUri(entity.getUri());
                                                            return fileMoveDto;
                                                        });
    }

    protected Runnable getFileMoveTask(List<FileMoveDto> fileMoveDtoList){
        return new TraderCertificateFileMoveTask(fileMoveDtoList);
    }
}
