package com.vedeng.erp.trader.mapper;
import java.util.List;
import java.util.Collection;

import com.vedeng.erp.trader.domain.entity.RCommunicateTodoJAiEntity;
import org.apache.ibatis.annotations.Param;

public interface RCommunicateTodoJAiMapper {
    /**
     * delete by primary key
     *
     * @param communicateInfoId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer communicateInfoId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(RCommunicateTodoJAiEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(RCommunicateTodoJAiEntity record);

    /**
     * select by primary key
     *
     * @param communicateInfoId primary key
     * @return object by primary key
     */
    RCommunicateTodoJAiEntity selectByPrimaryKey(Integer communicateInfoId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(RCommunicateTodoJAiEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(RCommunicateTodoJAiEntity record);

    RCommunicateTodoJAiEntity findByCommunicateRecordId(@Param("communicateRecordId") Integer communicateRecordId);

    List<RCommunicateTodoJAiEntity> findByCommunicateRecordIdIn(@Param("communicateRecordIdCollection")Collection<Integer> communicateRecordIdCollection);


}