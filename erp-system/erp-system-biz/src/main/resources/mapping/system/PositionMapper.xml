<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.system.mapper.PositionMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.system.domain.entity.PositionEntity">
    <!--@mbg.generated-->
    <!--@Table T_POSITION-->
    <id column="POSITION_ID" jdbcType="INTEGER" property="positionId" />
    <result column="ORG_ID" jdbcType="INTEGER" property="orgId" />
    <result column="POSITION_NAME" jdbcType="VARCHAR" property="positionName" />
    <result column="TYPE" jdbcType="INTEGER" property="type" />
    <result column="LEVEL" jdbcType="INTEGER" property="level" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    POSITION_ID, ORG_ID, POSITION_NAME, `TYPE`, `LEVEL`, ADD_TIME, CREATOR, MOD_TIME, 
    UPDATER
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_POSITION
    where POSITION_ID = #{positionId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_POSITION
    where POSITION_ID = #{positionId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="POSITION_ID" keyProperty="positionId" parameterType="com.vedeng.erp.system.domain.entity.PositionEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_POSITION (ORG_ID, POSITION_NAME, `TYPE`, 
      `LEVEL`, ADD_TIME, CREATOR, 
      MOD_TIME, UPDATER)
    values (#{orgId,jdbcType=INTEGER}, #{positionName,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, 
      #{level,jdbcType=INTEGER}, #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, 
      #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="POSITION_ID" keyProperty="positionId" parameterType="com.vedeng.erp.system.domain.entity.PositionEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_POSITION
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orgId != null">
        ORG_ID,
      </if>
      <if test="positionName != null">
        POSITION_NAME,
      </if>
      <if test="type != null">
        `TYPE`,
      </if>
      <if test="level != null">
        `LEVEL`,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orgId != null">
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="positionName != null">
        #{positionName,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="level != null">
        #{level,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.system.domain.entity.PositionEntity">
    <!--@mbg.generated-->
    update T_POSITION
    <set>
      <if test="orgId != null">
        ORG_ID = #{orgId,jdbcType=INTEGER},
      </if>
      <if test="positionName != null">
        POSITION_NAME = #{positionName,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `TYPE` = #{type,jdbcType=INTEGER},
      </if>
      <if test="level != null">
        `LEVEL` = #{level,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where POSITION_ID = #{positionId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.system.domain.entity.PositionEntity">
    <!--@mbg.generated-->
    update T_POSITION
    set ORG_ID = #{orgId,jdbcType=INTEGER},
      POSITION_NAME = #{positionName,jdbcType=VARCHAR},
      `TYPE` = #{type,jdbcType=INTEGER},
      `LEVEL` = #{level,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER}
    where POSITION_ID = #{positionId,jdbcType=INTEGER}
  </update>

  <select id="getPositionsByUserId" resultType="com.vedeng.erp.system.domain.dto.PositionDto">
    select TP.*
    from T_R_USER_POSIT TRUP
           left join T_POSITION TP on TRUP.POSITION_ID = TP.POSITION_ID
    where TRUP.USER_ID = #{userId,jdbcType=INTEGER}
  </select>
</mapper>