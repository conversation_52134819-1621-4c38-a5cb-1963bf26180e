package com.vedeng.todolist.dto;

import com.vedeng.todolist.service.visitor.*;

public class SupplyChainStaffTodoDtoBuild {

    private SupplyChainStaffTodoDtoBuild(){};

    public static SupplyChainStaffTodoDtoBuild newBuild(){
        return new SupplyChainStaffTodoDtoBuild();
    }

    private SupplyChainStaffTodoDto supplyChainStaffTodoDto = new SupplyChainStaffTodoDto();

    public SupplyChainStaffTodoDtoBuild setRiskCheckSkuTodoDto(TodoVisitor riskSkuTodoStaffVisitor){
        riskSkuTodoStaffVisitor.visitor(this);
        return this;
    }

    public SupplyChainStaffTodoDtoBuild setRiskCheckTraderTodo(TodoVisitor riskTraderTodoStaffVisitor){
        riskTraderTodoStaffVisitor.visitor(this);
        return this;
    }

    public SupplyChainStaffTodoDtoBuild setPurchaseInfoToto(TodoVisitor purchaseInfoTotoDtoVisitor){
        purchaseInfoTotoDtoVisitor.visitor(this);
        return this;
    }

    public SupplyChainStaffTodoDtoBuild setDailyManagementTodo(TodoVisitor dailyManageTodoStaffVisitor){
        dailyManageTodoStaffVisitor.visitor(this);
        return this;
    }

    public SupplyChainStaffTodoDtoBuild setMessageTodoDto(TodoVisitor messageTodoVisitor){
        messageTodoVisitor.visitor(this);
        return this;
    }

    public SupplyChainStaffTodoDtoBuild setEarlyWarningTicksTaskDto(TodoVisitor earlyWarningGoodsTaskVisitor){
        earlyWarningGoodsTaskVisitor.visitor(this);
        return this;
    }

    public SupplyChainStaffTodoDtoBuild setEarlyWarningGoodsTaskDto(TodoVisitor earlyWarningTicksTaskVisitor){
        earlyWarningTicksTaskVisitor.visitor(this);
        return this;
    }

    public SupplyChainStaffTodoDtoBuild setPrepareStockTaskDto(TodoVisitor earlyWarningGoodsTaskStaffVisitor){
        earlyWarningGoodsTaskStaffVisitor.visitor(this);
        return this;
    }

    public SupplyChainStaffTodoDtoBuild setReviewTaskToDoDto(TodoVisitor reviewTaskVisitor){
        reviewTaskVisitor.visitor(this);
        return this;
    }

    public SupplyChainStaffTodoDtoBuild setEnableReceiveToDoDto(TodoVisitor enableReceiveStaffVisitor){
        enableReceiveStaffVisitor.visitor(this);
        return this;
    }

    public SupplyChainStaffTodoDto get(){
        return supplyChainStaffTodoDto;
    }


    public SupplyChainStaffTodoDto bulid(){
        return supplyChainStaffTodoDto;
    }
}
