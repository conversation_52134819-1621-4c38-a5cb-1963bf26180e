package com.vedeng.erp.kingdee.task.batch;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.kingdee.batch.job.InvoiceRollbackBatchJob;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 蓝字发票作废处理对接金蝶定时任务
 *
 * <AUTHOR>
 */
@Component
@JobHandler(value = "RollbackInvoiceBatchTask")
public class RollbackInvoiceBatchTask extends AbstractJobHandler {

    @Autowired
    private JobLauncher jobLauncher;

    @Autowired
    private InvoiceRollbackBatchJob batchJob;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("BlueValidInvoiceRollbackTask start param:" + param);
        JobParameters jobParameters = new TaskBatchHandle().buildJobParameters(param);
        Job job = batchJob.rollbackBlueValidInvoiceJob();
        jobLauncher.run(job, jobParameters);
        XxlJobLogger.log("BlueValidInvoiceRollbackTask end");
        return SUCCESS;
    }
}
