<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.system.dao.ReadMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.system.model.Read" >
    <id column="READ_ID" property="readId" jdbcType="INTEGER" />
    <result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
    <result column="USER_ID" property="userId" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    READ_ID, COMPANY_ID, USER_ID
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from T_READ
    where READ_ID = #{readId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from T_READ
    where READ_ID = #{readId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.system.model.Read" >
    insert into T_READ (READ_ID, COMPANY_ID, USER_ID
      )
    values (#{readId,jdbcType=INTEGER}, #{companyId,jdbcType=INTEGER}, #{userId,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.system.model.Read" >
    insert into T_READ
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="readId != null" >
        READ_ID,
      </if>
      <if test="companyId != null" >
        COMPANY_ID,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="readId != null" >
        #{readId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null" >
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.system.model.Read" >
    update T_READ
    <set >
      <if test="companyId != null" >
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="userId != null" >
        USER_ID = #{userId,jdbcType=INTEGER},
      </if>
    </set>
    where READ_ID = #{readId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.system.model.Read" >
    update T_READ
    set COMPANY_ID = #{companyId,jdbcType=INTEGER},
      USER_ID = #{userId,jdbcType=INTEGER}
    where READ_ID = #{readId,jdbcType=INTEGER}
  </update>
  
  <select id="getReadByUserId" resultType="java.lang.Integer" parameterType="com.vedeng.authorization.model.User" >
    select 
    	count(1)
    from T_READ
    where USER_ID = #{userId,jdbcType=INTEGER} and COMPANY_ID = #{companyId,jdbcType=INTEGER}
  </select>
  
  <delete id="delAllReadByCompanyId" parameterType="java.lang.Integer" >
  	<if test="companyId != null and companyId > 0" >
	    delete from T_READ
	    where COMPANY_ID = #{companyId,jdbcType=INTEGER}
    </if>
  </delete>
</mapper>