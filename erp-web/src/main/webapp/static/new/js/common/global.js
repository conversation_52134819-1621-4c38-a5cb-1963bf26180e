//获取当前页面请求路径
var curWwwPath = window.document.location.href;
var pathName = window.document.location.pathname;
var pos = curWwwPath.indexOf(pathName);
var projectName = pathName.substring(0, pathName.substr(1).indexOf('/') + 1);
if (projectName != '/erp') {
    projectName = "";
}
var page_url = curWwwPath.substring(0, pos) + projectName;

var parentWindow = $(window.parent.document);
var parentParentWindow = $(window.parent.parent.document);

// 1、关闭当前页，跳到上一页，控制它的上一页（刷新/不刷新）；
// 2、不关当前页，控制它的上一页（刷新/不刷新）；// ；页面控制页面
// 15/16/17
function pagesContrlpages(closeSelf, freshSelf, freshFrontPage) {
    //上一页的Id；
    var frontPageId = parentWindow.find('.active').eq(1).children('iframe').attr('data-frontpageid');
    var frontPageFrame = parentWindow.find("#" + frontPageId).children('iframe');
    var frontPageSrc = parentWindow.find("#" + frontPageId).children('iframe').attr('src');
    var frontPageIndex = parentWindow.find("#" + frontPageId).index();
    var frontPageDataUrl = parentWindow.find("#" + frontPageId).children('iframe').attr('data-url');
    var selfId = parentWindow.find('.active').eq(0).attr('id');
    if (freshFrontPage) {
        // 效果，页面在加载数据的时候有提示框一直在加载
        var div = '<div class="tip-loadingNewData" id="loadingNewData-' + frontPageId + '" style="position:fixed;width:100%;height:100%; z-index:100; background:rgba(0,0,0,0.3)"><i class="iconloadingblue"></i></div>';
        $('#' + frontPageId, window.parent.document).prepend(div); //jq获取上一页框架的父级框架；
        if (frontPageDataUrl != undefined && frontPageDataUrl != '') {
            frontPageSrc = frontPageDataUrl;
        }
        frontPageFrame.attr('src', frontPageSrc);
    }
    // 刷新当前页
    if (freshSelf) {
        reloading();
    }
    // 关闭当前页    
    if (closeSelf) {
        parentWindow.find("#" + frontPageId).addClass('active');
        parentWindow.find('.nav li').removeClass('active').eq(frontPageIndex).addClass('active');
        parentWindow.find('#' + selfId + ' i[tabclose]').click(); //关闭当前页。
    }
}
// 18
//关闭上一页;
function closeFrontPage(closeFrontPage) {
    var frontPageId = parentWindow.find('.active').eq(1).children('iframe').attr('data-frontpageid');
    var frontPageIndex = parentWindow.find("#" + frontPageId).index();
    if (closeFrontPage) {
        parentWindow.find('.title li').eq(frontPageIndex).find('i[tabclose]').click();
    }
}
// 19
// 调用时加上参数，ID，名称，标题名称，是否关闭
function openNewPage(num, title, url) {
    var frontPageId = parentWindow.find('.active').eq(1).attr('id');
    var newPageId;
    var id = num;
    var closable = 1;
    // window.parent.postMessage({
    //     from:'olderp',
    //     name: name,
    //     url:uri,
    //     id:id
    // }, '*');
    var item = { 'id': id, 'name': title, 'url': url, 'closable': closable == 1 ? true : false };
    if (typeof(self.parent.closableTab) != 'undefined') {
        self.parent.closableTab.addTab(item);
        self.parent.closableTab.resizeMove();
        parentWindow.find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
    }else{
        try{
            var uniqueName = url.replace('./', '').replace(/["&'./:=%?[\]]/gi, '-').replace(/(--)/gi, '');
            window.parent.postMessage({
                from:'ez',
                name: title,
                url:url,
                id:"tab-"+uniqueName
            }, '*');
        }catch (e){}
    }

}


//---------------------------------------------------------- 上面是老框架的代码 暂时没办法啃 后面有机会直接干掉   -------------------------------------------------------------------


var GLOBAL = {};

//查看大图
GLOBAL.showLargePic = function (elem) {
    if (!$('.J-large-pic-wrap').length) {
        $('body').append(
            '<div class="vd-large-pic-wrap J-large-wrap" style="display: none;">' +
            '<div class= "vd-large-pic-cnt" >' +
            '<img class="J-large-img" src="" alt="">' +
            '</div>' +
            '<div class="vd-large-pic-mask"></div>' +
            '</div >'
        )

        $(document).on('click', elem, function () {
            $('.J-large-wrap').show().find('.J-large-img').attr('src', $(this).data('src'));
        })

        var type = /firefox/.test(navigator.userAgent.toLowerCase()) && !(!window.ActiveXObject && /function[\s\S]+?\[native\s+code\]/.test(window.ActiveXObject + '')/* ie 11+ */);
        var event = type ? 'DOMMouseScroll' : 'mousewheel';

        $('.J-large-wrap').on(event, function (e) {
            e.preventDefault();
        })

        $('.J-large-wrap').click(function () {
            $(this).hide();
        })
    }
};

GLOBAL.addtip = function (tip) {
    var tip = tip || '确认放弃操作，关闭当前页面么？';
    parentWindow.find('.title .active .glyphicon').attr('cz-tip', tip);
};

GLOBAL.removetip = function (tip) {
    parentWindow.find('.title .active .glyphicon').attr('cz-tip', '');
};

GLOBAL.removetip();

GLOBAL.showTip = function (TYPE, tip) {
    $('.J-alert-tip').remove();
    $('body').append('<div class="vd-alert-wrap J-alert-tip"><div class="vd-alert-cnt alert-' + TYPE + '">' + tip + '</div>');
    $('.J-alert-tip').show();
};

//气泡提示
GLOBAL.globalTipTimeout = null;
GLOBAL.showGlobalTip = function (tip, type, storage) {
    GLOBAL.globalTipTimeout && clearTimeout(GLOBAL.globalTipTimeout);

    var type = type || 'success';
    var TYPE = {
        success: 'succ',
        error: 'err',
        warn: 'warn'
    }[type];

    var tip = tip || '';

    if (storage) {
        if (localStorage.getItem(storage)) {
            GLOBAL.showTip(TYPE, tip);
            localStorage.removeItem(storage);
        }
    } else {
        GLOBAL.showTip(TYPE, tip);
    }

    GLOBAL.globalTipTimeout = setTimeout(function () {
        $('.J-alert-tip').hide();
    }, 2000)
};

GLOBAL.checkKeyCode = function (code) {
    var enterCode = [8, 37, 38, 39, 40, 46, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105];
    return enterCode.indexOf((code)) == -1;
};

GLOBAL.IMGUPLOADURL = page_url + '/firstengage/baseinfo/fileUploadImg.do';

GLOBAL.strTimeToNum = function (str) {
    var num = new Date(str).valueOf();

    return num - 8 * 60 * 60 * 1000;
};

void function () {
    $(document).on('click', '[tabTitle]', function () {
        var frontPageId = $(window.parent.document).find('.active').eq(1).attr('id');
        var newPageId;
        var tabTitle = $(this).attr('tabTitle');
        if (typeof (tabTitle) == 'undefined') {
            alert('参数错误');
        } else {
            tabTitle = $.parseJSON(tabTitle);
        }
        var id = tabTitle.num;
        // var id = 'index' + Date.parse(new Date()) + Math.floor(Math.random()*1000);
        var name = tabTitle.title;
        var uri = tabTitle.link;
        var needRandom = tabTitle.random;

        if (needRandom == '1') {
            id += (new Date()).valueOf();
        }

        var closable = 1;
        var item = { 'id': id, 'name': name, 'url': uri, 'closable': closable == 1 ? true : false };
        if (tabTitle.needTip) {
            item.needTip = tabTitle.needTip;
        }
        // self.parent.closableTab.addTab(item);
        // self.parent.closableTab.resizeMove();
        // $(window.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
        if (typeof(self.parent.closableTab) != 'undefined') {
            self.parent.closableTab.addTab(item);
            self.parent.closableTab.resizeMove();
            $(window.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
        }else{
            try{
                var uniqueName = uri.replace('./', '').replace(/["&'./:=%?[\]]/gi, '-').replace(/(--)/gi, '');
                window.parent.postMessage({
                    from:'ez',
                    name: title,
                    url:uri,
                    id:"tab-"+uniqueName
                }, '*');
            }catch (e){}
        }
    })

    if ($('.J-pager-wrap').length) {
        var $wrap = $('.J-pager-wrap');
        var $input = $wrap.find('.J-pager-input');
        var $jump = $wrap.find('.J-pager-jump');

        $input.on('keydown', function (e) {
            if (GLOBAL.checkKeyCode(e.keyCode)) {
                e.preventDefault();
                return false;
            }
        })

        $jump.on('click', function (e) {
            var val = $.trim($input.val());

            if (!val) {
                $input.focus();
                return false;
            }

            if (!/^\d*$/.test(val)) {
                $input.val('').focus();
            } else {
                window.location.href = $(this).data('link') + val;
            }
        })
    }

    //全局ajaxerror报错
    $(document).ajaxError(function (event, xhr, settings, exception) {
        if (xhr.responseJSON && xhr.responseJSON.message === "nopermission") {
            var url = settings.url; // 获取请求的URL
            var dia = artDialog.alert("无操作权限，请求的URL为：" + url, "信息", {
                fn: function () {
                    dia.close();
                    window.location.reload();
                },
                text: '确定'
            }, { type: "warn" });
        } else {
            GLOBAL.showGlobalTip('网络异常或请求错误。', 'warn');
        }
    });

    //全局code等于-1时，提示错误信息
    // $(document).ajaxComplete(function (evt, req, settings) {
    //     if (req && req.responseJSON ) {
    //         GLOBAL.showGlobalTip(req.responseJSON.message || '请求错误。', 'warn');
    //     }

    //     // if (req && req.responseJSON && req.responseJSON.success !== 'undefined' && !req.responseJSON.success) {
    //     //     GLOBAL.showGlobalTip(req.responseJSON.message || '请求错误。', 'warn');
    //     // }
    // });

    //全局统一将当前页面的tab换成title的值
    var $currentTab = parentWindow.find('.active').eq(0).find('.shuaxin').next('span');
    var title = $('title').html();
    $currentTab.html(title);

    //全局统一将tab的url当前页面的url
    // var $currentIframe = parentWindow.find('.active').eq(1).find('iframe');

    // if ($currentIframe.length && $currentIframe[0].src.split('?')[0] != window.location.href.split('?')[0]) {
    //     $currentIframe.attr('src', window.location.href);
    // }

}.call(this);




function watermark(settings) {
    if($(".mask_div").length>0){
        return;
    }
    //默认设置
    var defaultSettings = {
        watermark_txt: "text",
        watermark_x: 20, //水印起始位置x轴坐标
        watermark_y: 20, //水印起始位置Y轴坐标
        watermark_rows: 0, //水印行数
        watermark_cols: 0, //水印列数
        watermark_x_space: 10, //水印x轴间隔
        watermark_y_space: 20, //水印y轴间隔
        watermark_color: '#aaa', //水印字体颜色
        watermark_alpha: 0.2, //水印透明度
        watermark_fontsize: '12px', //水印字体大小
        watermark_font: '宋体', //水印字体
        watermark_width: 140, //水印宽度
        watermark_height: 80, //水印长度
        watermark_angle: 20 //水印倾斜度数
    };
    if (arguments.length === 1 && typeof arguments[0] === "object") {
        var src = arguments[0] || {};
        for (key in src) {
            if (src[key] && defaultSettings[key] && src[key] === defaultSettings[key]) continue;
            else if (src[key]) defaultSettings[key] = src[key];
        }
    }
    var oTemp = document.createDocumentFragment();
    //获取页面最大宽度
    var page_width = Math.max(document.body.scrollWidth, document.body.clientWidth);
    var cutWidth = page_width * 0.0150;
    var page_width = page_width - cutWidth;
    //获取页面最大高度
    var page_height = Math.max(document.body.scrollHeight, document.body.clientHeight) + 30;
    page_height = Math.max(page_height, window.innerHeight - 30);
    //如果将水印列数设置为0，或水印列数设置过大，超过页面最大宽度，则重新计算水印列数和水印x轴间隔
    if (defaultSettings.watermark_cols == 0 || (parseInt(defaultSettings.watermark_x + defaultSettings.watermark_width * defaultSettings.watermark_cols + defaultSettings.watermark_x_space * (defaultSettings.watermark_cols - 1)) > page_width)) {
        defaultSettings.watermark_cols = parseInt((page_width - defaultSettings.watermark_x + defaultSettings.watermark_x_space) / (defaultSettings.watermark_width + defaultSettings.watermark_x_space));
        defaultSettings.watermark_x_space = parseInt((page_width - defaultSettings.watermark_x - defaultSettings.watermark_width * defaultSettings.watermark_cols) / (defaultSettings.watermark_cols - 1));
    }
    //如果将水印行数设置为0，或水印行数设置过大，超过页面最大长度，则重新计算水印行数和水印y轴间隔
    if (defaultSettings.watermark_rows == 0 || (parseInt(defaultSettings.watermark_y + defaultSettings.watermark_height * defaultSettings.watermark_rows + defaultSettings.watermark_y_space * (defaultSettings.watermark_rows - 1)) > page_height)) {
        defaultSettings.watermark_rows = parseInt((defaultSettings.watermark_y_space + page_height - defaultSettings.watermark_y) / (defaultSettings.watermark_height + defaultSettings.watermark_y_space));
        defaultSettings.watermark_y_space = parseInt(((page_height - defaultSettings.watermark_y) - defaultSettings.watermark_height * defaultSettings.watermark_rows) / (defaultSettings.watermark_rows - 1));
    }
    var x;
    var y;
    for (var i = 0; i < defaultSettings.watermark_rows-1; i++) {
        y = defaultSettings.watermark_y + (defaultSettings.watermark_y_space + defaultSettings.watermark_height) * i;
        for (var j = 0; j < defaultSettings.watermark_cols; j++) {
            x = defaultSettings.watermark_x + (defaultSettings.watermark_width + defaultSettings.watermark_x_space) * j;
            var mask_div = document.createElement('div');
            mask_div.id = 'mask_div' + i + j;
            mask_div.className = 'mask_div';
            mask_div.appendChild(document.createTextNode(defaultSettings.watermark_txt));
            //设置水印div倾斜显示
            mask_div.style.webkitTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
            mask_div.style.MozTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
            mask_div.style.msTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
            mask_div.style.OTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
            mask_div.style.transform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
            mask_div.style.visibility = "";
            mask_div.style.position = "absolute";
            mask_div.style.left = x + 'px';
            mask_div.style.top = y + 'px';
            mask_div.style.overflow = "hidden";
            mask_div.style.zIndex = "9999";
            //让水印不遮挡页面的点击事件
            mask_div.style.pointerEvents = 'none';
            mask_div.style.opacity = defaultSettings.watermark_alpha;
            mask_div.style.fontSize = defaultSettings.watermark_fontsize;
            mask_div.style.fontFamily = defaultSettings.watermark_font;
            mask_div.style.color = defaultSettings.watermark_color;
            mask_div.style.textAlign = "center";
            mask_div.style.width = defaultSettings.watermark_width + 'px';
            mask_div.style.height = defaultSettings.watermark_height + 'px';
            mask_div.style.display = "block";
            oTemp.appendChild(mask_div);
        };
    };
    document.body.appendChild(oTemp);
}

function getNow() {
    var d = new Date();
    var year = d.getFullYear();
    var month = change(d.getMonth() + 1);
    var day = change(d.getDate());
    var hour = change(d.getHours());
    var minute = change(d.getMinutes());
    var second = change(d.getSeconds());

    function change(t) {
        if (t < 10) {
            return "0" + t;
        } else {
            return t;
        }
    }
    var time = year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second + '';
    return time;
}