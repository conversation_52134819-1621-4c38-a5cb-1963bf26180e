<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.TraderContactTransferRecordMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.TraderContactTransferRecordEntity">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="TRADER_CONTACT_TRANSFER_RECORD_ID" jdbcType="INTEGER" property="traderContactTransferRecordId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_CUSTOMER_ID" jdbcType="INTEGER" property="traderCustomerId" />
    <result column="TRADER_CUSTOMER_NAME" jdbcType="VARCHAR" property="traderCustomerName" />
    <result column="AFTER_TRADER_CUSTOMER_ID" jdbcType="INTEGER" property="afterTraderCustomerId" />
    <result column="AFTER_TRADER_CUSTOMER_NAME" jdbcType="VARCHAR" property="afterTraderCustomerName" />
    <result column="TRADER_CONTACT_ID" jdbcType="INTEGER" property="traderContactId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    TRADER_CONTACT_TRANSFER_RECORD_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME,
    UPDATER_NAME, TRADER_ID, TRADER_CUSTOMER_ID, TRADER_CUSTOMER_NAME, AFTER_TRADER_CUSTOMER_ID,
    AFTER_TRADER_CUSTOMER_NAME,TRADER_CONTACT_ID
  </sql>
  <select id="selectByExample" parameterType="com.vedeng.erp.trader.domain.entity.TraderContactTransferRecordExample" resultMap="BaseResultMap">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from T_TRADER_CONTACT_TRANSFER_RECORD
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    select
    <include refid="Base_Column_List" />
    from T_TRADER_CONTACT_TRANSFER_RECORD
    where TRADER_CONTACT_TRANSFER_RECORD_ID = #{traderContactTransferRecordId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    delete from T_TRADER_CONTACT_TRANSFER_RECORD
    where TRADER_CONTACT_TRANSFER_RECORD_ID = #{traderContactTransferRecordId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.erp.trader.domain.entity.TraderContactTransferRecordExample">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    delete from T_TRADER_CONTACT_TRANSFER_RECORD
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.erp.trader.domain.entity.TraderContactTransferRecordEntity">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    insert into T_TRADER_CONTACT_TRANSFER_RECORD (TRADER_CONTACT_TRANSFER_RECORD_ID, ADD_TIME,
      MOD_TIME, CREATOR, UPDATER,
      CREATOR_NAME, UPDATER_NAME, TRADER_ID,
      TRADER_CUSTOMER_ID, TRADER_CUSTOMER_NAME, AFTER_TRADER_CUSTOMER_ID,
      AFTER_TRADER_CUSTOMER_NAME,TRADER_CONTACT_ID)
    values (#{traderContactTransferRecordId,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP},
      #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER},
      #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, #{traderId,jdbcType=INTEGER},
      #{traderCustomerId,jdbcType=INTEGER}, #{traderCustomerName,jdbcType=VARCHAR}, #{afterTraderCustomerId,jdbcType=INTEGER},
      #{afterTraderCustomerName,jdbcType=VARCHAR},#{traderContactId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.erp.trader.domain.entity.TraderContactTransferRecordEntity">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    insert into T_TRADER_CONTACT_TRANSFER_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="traderContactTransferRecordId != null">
        TRADER_CONTACT_TRANSFER_RECORD_ID,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderCustomerId != null">
        TRADER_CUSTOMER_ID,
      </if>
      <if test="traderCustomerName != null">
        TRADER_CUSTOMER_NAME,
      </if>
      <if test="afterTraderCustomerId != null">
        AFTER_TRADER_CUSTOMER_ID,
      </if>
      <if test="afterTraderCustomerName != null">
        AFTER_TRADER_CUSTOMER_NAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="traderContactTransferRecordId != null">
        #{traderContactTransferRecordId,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderCustomerId != null">
        #{traderCustomerId,jdbcType=INTEGER},
      </if>
      <if test="traderCustomerName != null">
        #{traderCustomerName,jdbcType=VARCHAR},
      </if>
      <if test="afterTraderCustomerId != null">
        #{afterTraderCustomerId,jdbcType=INTEGER},
      </if>
      <if test="afterTraderCustomerName != null">
        #{afterTraderCustomerName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.erp.trader.domain.entity.TraderContactTransferRecordExample" resultType="java.lang.Long">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    select count(*) from T_TRADER_CONTACT_TRANSFER_RECORD
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    update T_TRADER_CONTACT_TRANSFER_RECORD
    <set>
      <if test="row.traderContactTransferRecordId != null">
        TRADER_CONTACT_TRANSFER_RECORD_ID = #{row.traderContactTransferRecordId,jdbcType=INTEGER},
      </if>
      <if test="row.addTime != null">
        ADD_TIME = #{row.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.modTime != null">
        MOD_TIME = #{row.modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.creator != null">
        CREATOR = #{row.creator,jdbcType=INTEGER},
      </if>
      <if test="row.updater != null">
        UPDATER = #{row.updater,jdbcType=INTEGER},
      </if>
      <if test="row.creatorName != null">
        CREATOR_NAME = #{row.creatorName,jdbcType=VARCHAR},
      </if>
      <if test="row.updaterName != null">
        UPDATER_NAME = #{row.updaterName,jdbcType=VARCHAR},
      </if>
      <if test="row.traderId != null">
        TRADER_ID = #{row.traderId,jdbcType=INTEGER},
      </if>
      <if test="row.traderCustomerId != null">
        TRADER_CUSTOMER_ID = #{row.traderCustomerId,jdbcType=INTEGER},
      </if>
      <if test="row.traderCustomerName != null">
        TRADER_CUSTOMER_NAME = #{row.traderCustomerName,jdbcType=VARCHAR},
      </if>
      <if test="row.afterTraderCustomerId != null">
        AFTER_TRADER_CUSTOMER_ID = #{row.afterTraderCustomerId,jdbcType=INTEGER},
      </if>
      <if test="row.afterTraderCustomerName != null">
        AFTER_TRADER_CUSTOMER_NAME = #{row.afterTraderCustomerName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    update T_TRADER_CONTACT_TRANSFER_RECORD
    set TRADER_CONTACT_TRANSFER_RECORD_ID = #{row.traderContactTransferRecordId,jdbcType=INTEGER},
      ADD_TIME = #{row.addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{row.modTime,jdbcType=TIMESTAMP},
      CREATOR = #{row.creator,jdbcType=INTEGER},
      UPDATER = #{row.updater,jdbcType=INTEGER},
      CREATOR_NAME = #{row.creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{row.updaterName,jdbcType=VARCHAR},
      TRADER_ID = #{row.traderId,jdbcType=INTEGER},
      TRADER_CUSTOMER_ID = #{row.traderCustomerId,jdbcType=INTEGER},
      TRADER_CUSTOMER_NAME = #{row.traderCustomerName,jdbcType=VARCHAR},
      AFTER_TRADER_CUSTOMER_ID = #{row.afterTraderCustomerId,jdbcType=INTEGER},
      AFTER_TRADER_CUSTOMER_NAME = #{row.afterTraderCustomerName,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.TraderContactTransferRecordEntity">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    update T_TRADER_CONTACT_TRANSFER_RECORD
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderCustomerId != null">
        TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER},
      </if>
      <if test="traderCustomerName != null">
        TRADER_CUSTOMER_NAME = #{traderCustomerName,jdbcType=VARCHAR},
      </if>
      <if test="afterTraderCustomerId != null">
        AFTER_TRADER_CUSTOMER_ID = #{afterTraderCustomerId,jdbcType=INTEGER},
      </if>
      <if test="afterTraderCustomerName != null">
        AFTER_TRADER_CUSTOMER_NAME = #{afterTraderCustomerName,jdbcType=VARCHAR},
      </if>
    </set>
    where TRADER_CONTACT_TRANSFER_RECORD_ID = #{traderContactTransferRecordId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.TraderContactTransferRecordEntity">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    update T_TRADER_CONTACT_TRANSFER_RECORD
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER},
      TRADER_CUSTOMER_NAME = #{traderCustomerName,jdbcType=VARCHAR},
      AFTER_TRADER_CUSTOMER_ID = #{afterTraderCustomerId,jdbcType=INTEGER},
      AFTER_TRADER_CUSTOMER_NAME = #{afterTraderCustomerName,jdbcType=VARCHAR}
    where TRADER_CONTACT_TRANSFER_RECORD_ID = #{traderContactTransferRecordId,jdbcType=INTEGER}
  </update>
</mapper>