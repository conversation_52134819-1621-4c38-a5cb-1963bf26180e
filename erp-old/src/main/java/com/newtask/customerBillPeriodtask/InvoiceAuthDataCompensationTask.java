package com.newtask.customerBillPeriodtask;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.constant.OrderDataUpdateConstant;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.StringUtil;
import com.vedeng.finance.dao.InvoiceMapper;
import com.vedeng.finance.model.Invoice;
import com.vedeng.finance.model.InvoiceDetail;
import com.vedeng.finance.service.InvoiceService;
import com.vedeng.order.service.OrderCommonService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * VDERP-7303 客户信用记录 页面展示  导致的数据补偿问题
 * (给 startDate与endDate时检索审核用过时间段的发票 否则给具体发票信息)
 * <AUTHOR>
 */
@Component
@JobHandler(value = "invoiceAuthDataCompensationTask")
public class InvoiceAuthDataCompensationTask extends AbstractJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(InvoiceAuthDataCompensationTask.class);

    @Autowired
    private InvoiceService invoiceService;

    @Resource
    private InvoiceMapper invoiceMapper;

    @Resource
    private OrderCommonService orderCommonService;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        XxlJobLogger.log("PeriodUseNodeRecordHandleTask-JOB, START");
        if (StringUtil.isBlank(s)){
            XxlJobLogger.log("数据补偿必须拥有相应的参数");
            return FAIL;
        }

        logger.info("发票审核补偿任务 start s:{}", s);
        Invoice invoice = JSON.parseObject(s, Invoice.class);
        if (invoice.getStartDate() != null && invoice.getEndDate() != null){
            invoiceMapper.getInvoiceListByValidTime(invoice.getStartDate(), invoice.getEndDate()).forEach(this::compensateByAuditInvoice);
            return SUCCESS;
        }

        compensateByAuditInvoice(invoice);
        return SUCCESS;
    }

    /**
     * 处理相关补偿信息
     *
     * @param invoice
     */
    private void compensateByAuditInvoice(Invoice invoice) {
        //处理采购订单状态信息
        invoiceService.dealOrderInvoiceStatus(invoice);

        //处理采购订单催票预警信息
        invoiceService.dealEarlyWarningTicketTask(invoice);

        List<Integer> buyorderIds = invoiceMapper.getInvoiceGoodsNum(invoice).stream()
                .map(InvoiceDetail::getRelatedId).distinct().collect(Collectors.toList());

        buyorderIds.forEach(buyorderId -> {
            orderCommonService.updateBuyOrderDataUpdateTime(buyorderId,null, OrderDataUpdateConstant.BUY_ORDER_INVOICE);
        });
    }
}
