package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchBankBillDto;
import com.vedeng.erp.kingdee.dto.KingDeeFileDataDto;
import com.vedeng.erp.kingdee.dto.KingDeePayBillDto;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveBillDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeePayBillMapper;
import com.vedeng.erp.kingdee.service.KingDeeFileDataService;
import com.vedeng.erp.kingdee.service.KingDeePayBillService;
import com.vedeng.erp.kingdee.service.KingDeeReceiveBillService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryExtParam;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryFilterDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class PayBillFillProcessor extends BaseProcessor<BatchBankBillDto, KingDeeFileDataDto> {
    public static final String ZERO = "0";

    @Autowired
    private KingDeePayBillService kingDeePayBillService;

    @Autowired
    private KingDeePayBillMapper kingDeePayBillMapper;

    @Autowired
    private KingDeeFileDataService kingDeeFileDataService;

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Override
    public KingDeeFileDataDto doProcess(BatchBankBillDto batchBankBillDto, JobParameters params, ExecutionContext stepContext) throws Exception {
        KingDeeFileDataDto query = KingDeeFileDataDto.builder()
                .formId(KingDeeFormConstant.AP_PAYBILL)
                .erpId(batchBankBillDto.getBankBillId().toString())
                .url(batchBankBillDto.getReceiptUrl())
                .build();
        List<KingDeeFileDataDto> existFile = kingDeeFileDataService.getByBusinessIdAndUri(query);
        if (!CollectionUtils.isEmpty(existFile)) {
            log.info("当前附件已经推送过金蝶，{}", JSON.toJSONString(query));
            return null;
        }
        String FBillNo = "";
        if (KingDeeConstant.ZERO.equals(batchBankBillDto.getStatus()) && batchBankBillDto.getMatchedAmount().compareTo(BigDecimal.ZERO) != 0) {
            FBillNo = "C_" + batchBankBillDto.getBankBillId();
        } else {
            FBillNo = "B_" + batchBankBillDto.getBankBillId();
        }
        //查询付款单是否已推送金蝶
        KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
        queryParam.setFormId(KingDeeFormConstant.AP_PAYBILL);
        //查询金蝶字段
        queryParam.setFieldKeys("fid,fbillno");
        List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
        queryFilterDtos.add(KingDeeQueryFilterDto.builder().fieldName("fbillno").value(FBillNo).build());
        queryParam.setFilterString(queryFilterDtos);
        List<Map<String, Object>> queryPayBill = kingDeeBaseApi.queryReturnMap(queryParam);
        if (ObjectUtil.isNull(queryPayBill)){
            log.info("付款单附件推送金蝶,未查到付款单,查询报文:{},查询结果:{}",JSONUtil.toJsonStr(queryParam),JSONUtil.toJsonStr(queryPayBill));
            return null;
        }
        //根据fBillNo查询Fid
        KingDeePayBillDto kingDeePayBillDto = kingDeePayBillMapper.selectByFBillNo(FBillNo);
        if (ObjectUtil.isNull(kingDeePayBillDto)){
            log.info("付款回写表未根据FBillNo查询到FID,查询参数:{}", FBillNo);
            return null;
        }
        return KingDeeFileDataDto.builder()
                .fileName(batchBankBillDto.getBankBillId() + ".pdf")
                .aliasFileName(batchBankBillDto.getBankBillId() + ".pdf")
                .billNo(FBillNo)
                .formId(KingDeeFormConstant.AP_PAYBILL)
                .isLast(true)
                .fId(kingDeePayBillDto.getFId())
                .url(batchBankBillDto.getReceiptUrl())
                .erpId(batchBankBillDto.getBankBillId().toString())
                .businessId(KingDeeFormConstant.AP_PAYBILL + batchBankBillDto.getBankBillId().toString())
                .build();
    }
}
