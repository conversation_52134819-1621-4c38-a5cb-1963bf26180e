void function () {

    new Vue({
        el: '#page-container',
        data: {
            prodListHeader: [
                {
                    label: "",
                    width: "32px",
                    key: "tablenum",
                    vertical: 'center'
                },
                {
                    label: "基础信息",
                    key: "baseinfo",
                    width: "273px"
                },
                {
                    label: "主要参数",
                    key: "mainparam",
                    width: "242px"
                },
                {
                    label: "价格信息",
                    key: "priceinfo",
                    width: "180px"
                },
                {
                    label: "售后信息",
                    key: "saleinfo",
                    width: "127px"
                },
                {
                    label: "产品状态",
                    key: "prodstatus",
                    width: "151px"
                },
                {
                    label: "产品负责人",
                    key: "manager",
                    width: "195px"
                }
            ],
            tableFixedTop: 73,
            tabInfo: [],
            currentProdList: [],
            currentTabIndex: 0,
            isloading: true,
            tabFixed: false,
            tabHeight: '',
            id: '',
            sceneInfo: {}
        },
        mounted() {
            this.id = VD_UI_GLOBAL.getQuery('id');

            if (!this.id) {
                window.location.href = '/404';
                return;
            }

            this.initList();
        },
        methods: {
            initList() {
                VD_UI_GLOBAL.showGlobalLoading();
                this.isloading = true;
                axios.post('/sku/scene/get.do?id=' + this.id).then(({ data }) => {
                    VD_UI_GLOBAL.hideGlobalLoading();
                    this.isloading = false;
                    if (data.success) {
                        this.tabInfo = data.data.skuSceneCategoryDtoList || [];
                        this.sceneInfo = data.data || {};
                        if (this.tabInfo) {
                            this.currentProdList = this.tabInfo[0].productInfoList;
                        }

                        this.$nextTick(() => {
                            this.tabHeight = this.$refs.tabList.offsetHeight;
                            this.tableFixedTop = this.tabHeight + 73;

                            window.addEventListener('scroll', this.checkTabFixed);
                        })
                    } else {
                        this.$message.warn(data.message)
                    }
                })
            },
            checkTabFixed() {
                if (window.scrollY > this.$refs.tabWrap.offsetTop - 73) {
                    this.tabFixed = true;
                } else {
                    this.tabFixed = false;
                }
            },
            changeTab(index) {
                this.currentTabIndex = index;
                this.currentProdList = this.tabInfo[index].productInfoList;
            },
            gotoEdit() {
                window.location.href = "/sku/scene/edit.do?id=" + this.id;
            },
            copyScene() {
                VD_UI_GLOBAL.openLink("/sku/scene/edit.do?copyid=" + this.id, {
                    name: '新增场景方案'
                })
            },
            exportScene() {
                window.open("/sku/scene/exportExcel.do?id=" + this.id);
            },
            updateScene(status) {
                let tip = ['上架后该方案在各选项场景中可见，确定上架吗？', '下架后该方案在各产品选型场景中不可见，确定下架吗？'][status];
                let btnTxt = ['上架', '下架'][status];
                let btnClass = ['confirm', 'delete'][status];
                let _this = this;

                this.$popup.warn({
                    message: tip,
                    buttons: [{
                        txt: btnTxt,
                        btnClass: btnClass,
                        callback() {
                            VD_UI_GLOBAL.showGlobalLoading();
                            axios.post(`/sku/scene/updateStatus.do?id=${_this.id}&status=${status}`).then(({ data }) => {
                                VD_UI_GLOBAL.hideGlobalLoading();

                                if (data.code === 0) {
                                    _this.$message.success(['上架成功', '下架成功'][status]);

                                    setTimeout(() => {
                                        window.location.reload();
                                    }, 1000)
                                } else {
                                    _this.$message.warn(data.message);
                                }
                            })
                        }
                    }, {
                        txt: '取消',
                    }]
                })
            },
            gotoGoodsDetail(skuNo) {
                VD_UI_GLOBAL.openLink("/goods/vgoods/viewSku.do?skuId=" + skuNo.replace(/[vV]/, ''), {
                    name: '查看SKU'
                })
            },
        }
    })
}.call(this);