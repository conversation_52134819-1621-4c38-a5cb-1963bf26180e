package com.vedeng.flash.service.prepare.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.pricecenter.dto.SkuPriceChangeApplyDto;
import com.pricecenter.service.imp.BasePriceMaintainServiceImpl;
import com.vedeng.authorization.model.Organization;
import com.vedeng.authorization.model.User;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.http.HttpRestClientUtil;
import com.vedeng.common.http.HttpURLConstant;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.flash.constant.FlashConstant;
import com.vedeng.flash.dao.*;
import com.vedeng.flash.dto.*;
import com.vedeng.flash.dto.temp.OneThreeMonthLastYearSaleNumTemp;
import com.vedeng.flash.enums.AdjustReasonEnum;
import com.vedeng.flash.enums.DealTypeEnum;
import com.vedeng.flash.enums.WarnLevelEnum;
import com.vedeng.flash.model.PrepareStockLog;
import com.vedeng.flash.model.RegularOperateLog;
import com.vedeng.flash.model.RegularSnapshot;
import com.vedeng.flash.service.oreding.OrderingPoolService;
import com.vedeng.flash.service.prepare.PrepareStockService;
import com.vedeng.goods.dao.GoodsMapper;
import com.vedeng.goods.model.Goods;
import com.vedeng.logistics.dao.WarehouseGoodsOperateLogMapper;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.logistics.model.WarehouseStock;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.order.model.Buyorder;
import com.vedeng.price.api.price.dto.price.SkuPriceInfoBatchQueryDto;
import com.vedeng.price.api.price.dto.price.SkuPriceInfoDetailResponseDto;
import com.wms.constant.LogicalEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:  备货计划服务接口实现
 * @Author:       davis
 * @Date:         2021/5/18 下午7:52
 * @Version:      1.0
 */
@Slf4j
@Service("prepareStockService")
public class PrepareStockServiceImpl extends BaseServiceimpl implements PrepareStockService {

    @Autowired
    private RegularPrepareSkuMapper regularPrepareSkuMapper;

    @Autowired
    private BasePriceMaintainServiceImpl basePriceMaintainService;

    @Autowired
    private WarehouseGoodsOperateLogMapper warehouseGoodsOperateLogMapper;

    @Autowired
    private PrepareStockLogMapper prepareStockLogMapper;

    @Autowired
    private RegularOperateLogMapper regularOperateLogMapper;

    @Autowired
    private GoodsMapper goodsMapper;

    @Autowired
    private WarehouseStockService warehouseStockService;

    @Autowired
    private RegularSnapshotMapper regularSnapshotMapper;

    @Autowired
    private OrderingPoolService orderingPoolService;

    @Autowired
    private EarlyWarningTaskMapper earlyWarningTaskMapper;

    @Value("${price.url}")
    private String priceApiUrl;

    @Value("${prepare_warn_admin_id}")
    private String prepareWarnAdminId;

    @Override
    public List<PrepareStockDto> getPrepareStockListPage(PrepareStockCommand command, Page page) {
        Map<String, Object> paramMap = new HashMap<String, Object>();
        if (command.getNeedPage() == null || command.getNeedPage() != 1) {
            paramMap.put("page", page);
        }
        paramMap.put("command", command);
        if (StringUtil.isNotEmpty(command.getSkuNo()) && !command.getSkuNo().startsWith("V")) {
            command.setSkuNo("V" + command.getSkuNo());
        }
        List<PrepareStockDto> prepareStockDtoList = regularPrepareSkuMapper.getPrepareStockListPage(paramMap);

        if (CollectionUtils.isEmpty(prepareStockDtoList)) {
            return new ArrayList<>();
        }
        List<String> skuNoList = new ArrayList<>();
        List<Integer> skuIdList = new ArrayList<>();

        prepareStockDtoList.stream().forEach(item -> {
            skuNoList.add(item.getSkuNo());
            skuIdList.add(item.getSkuId());
        });

        // 获取价格中心采购成本价
        List<SkuPriceChangeApplyDto> skuPriceApplyList = querySkuCostFromPriceCenter(skuNoList);
        // 设置采购成本价
        if (!CollectionUtils.isEmpty(skuPriceApplyList)) {
            prepareStockDtoList.stream().forEach(prepareStock -> {
                skuPriceApplyList.stream().forEach(skuPrice -> {
                    if (StringUtil.isNotEmpty(skuPrice.getSkuNo()) && StringUtil.isNotEmpty(prepareStock.getSkuNo())
                            && prepareStock.getSkuNo().equals(skuPrice.getSkuNo())) {
                        prepareStock.setCost(skuPrice.getMiddlePrice());
                    }
                });
            });
        }
        List<PrepareStockDto> inTransitSkuList = regularPrepareSkuMapper.getInTransitSkuNumList(skuNoList);
        if (!CollectionUtils.isEmpty(inTransitSkuList)) {
            prepareStockDtoList.stream().forEach(prepareStock -> {
                inTransitSkuList.stream().forEach(inTransitSku -> {
                    if (StringUtil.isNotEmpty(inTransitSku.getSkuNo()) && StringUtil.isNotEmpty(prepareStock.getSkuNo())
                            && prepareStock.getSkuNo().equals(inTransitSku.getSkuNo())) {
                        prepareStock.setIntransitStock(inTransitSku.getIntransitStock());
                    }
                });
            });
        }
        List<PrepareStockDto> receiveTimesList = regularPrepareSkuMapper.getReceiveTimesList(skuNoList);
        if (!CollectionUtils.isEmpty(receiveTimesList)) {
            prepareStockDtoList.forEach(prepareStock -> {
                prepareStock.setReceiveTimes(new BigDecimal("0.0").setScale(1, BigDecimal.ROUND_HALF_UP));
                receiveTimesList.forEach(receiveTimes -> {
                    if (StringUtil.isNotEmpty(receiveTimes.getSkuNo()) && StringUtil.isNotEmpty(prepareStock.getSkuNo())
                            && prepareStock.getSkuNo().equals(receiveTimes.getSkuNo())) {
                        prepareStock.setReceiveTimes(receiveTimes.getReceiveTimes().setScale(1, BigDecimal.ROUND_HALF_UP));
                    }
                });
            });
        }
        PrepareStockCommand paramCommand = new PrepareStockCommand();
        paramCommand.setSkuNoList(skuNoList);
        // 设置当前日期获取近三个月天数
        paramCommand.setThreeMonthDays(DateUtil.getRecentMonthsDays(-3));
        paramCommand.setStartDate(DateUtil.getStartDateMonths(-3));
        paramCommand.setEndDate(DateUtil.getEndDateMonths(-3));
        List<PrepareStockDto> recentSkuSaleNumList = regularPrepareSkuMapper.getRecentSkuSaleNumList(paramCommand);
        // List<OneThreeMonthLastYearSaleNumTemp> recentSkuSaleNumList = regularPrepareSkuMapper.selectThreeMonthSumSaleNum(skuIdList);
        if (!CollectionUtils.isEmpty(recentSkuSaleNumList)) {
            prepareStockDtoList.stream().forEach(prepareStock -> {
                recentSkuSaleNumList.stream().forEach(recentSkuSaleNum -> {
                    if (recentSkuSaleNum.getSkuId() != null && prepareStock.getSkuId() != null
                            && recentSkuSaleNum.getSkuId().intValue() == prepareStock.getSkuId().intValue()) {
                        prepareStock.setThreeMonthDaysSaleNum(recentSkuSaleNum.getThreeMonthDaysSaleNum());
                    }
                });
            });
        }

        for (PrepareStockDto stockDto : prepareStockDtoList) {
            // 如果安全库存为空，表示未设置过安全库存，安全库存=预测预测安全库存
            stockDto.setForecastSafeStock(stockDto.getThreeMonthDaysSaleNum()*stockDto.getGlobalSafeRatio());
            if (stockDto.getSafeStock() == null) {
                stockDto.setSafeStock(stockDto.getForecastSafeStock());
            }
            // 处理库存数量
            dealWithStock(stockDto);
//            dealWidthReceiveTimes(paramCommand, stockDto);
            // 安全库存-现有库存-在途数量+占有数量
            Integer supplementStock = stockDto.getSafeStock()-stockDto.getStock()-stockDto.getIntransitStock()+stockDto.getOrderStock();
            if (supplementStock <= 0) {
                supplementStock = 0;
            }
            stockDto.setSupplementStock(supplementStock);
        }
        log.info("getPrepareStockListPage数据返回：{}", prepareStockDtoList);
        return prepareStockDtoList;
    }

    @Override
    public PrepareStockDto initSafeStockPage(PrepareStockCommand command) {
        PrepareStockDto stockDto = regularPrepareSkuMapper.getPrepareStockByCommand(command);
        // 如果安全库存为空，表示未设置过安全库存，安全库存=预测预测安全库存
        if (stockDto.getSafeStock() == null) {
            stockDto.setSafeStock(command.getThreeMonthDaysSaleNum()*stockDto.getGlobalSafeRatio());
        }
        stockDto.setForecastSafeStock(command.getThreeMonthDaysSaleNum()*stockDto.getGlobalSafeRatio());
        stockDto.setThreeMonthDaysSaleNum(command.getThreeMonthDaysSaleNum());
        stockDto.setStock(command.getStock());
        stockDto.setSupplementStock(command.getSupplementStock());
        stockDto.setDealType(command.getDealType());
        stockDto.setIntransitStock(command.getIntransitStock());
        stockDto.setOrderStock(command.getOrderStock());
        stockDto.setReceiveTimes(command.getReceiveTimes());
        stockDto.setOutStockTimes(command.getOutStockTimes());
        stockDto.setCost(command.getCost());
        // 处理销量
        dealWithSaleNum(stockDto);
        return stockDto;
    }

    /**
     * 处理销量
     * @param stockDto
     */
    @Override
    public void dealWithSaleNum(PrepareStockDto stockDto) {
        // 计算销量
        List<Integer> skuIdList = new ArrayList<>();
        skuIdList.add(stockDto.getSkuId());
        List<OneThreeMonthLastYearSaleNumTemp> oneThreeMonthLastYearSaleNumTemps = orderingPoolService.getOneThreeMonthLastYearSaleNum(skuIdList);
        if (!CollectionUtils.isEmpty(oneThreeMonthLastYearSaleNumTemps)) {
            OneThreeMonthLastYearSaleNumTemp saleNumTemp = oneThreeMonthLastYearSaleNumTemps.get(0);
            stockDto.setAmount(saleNumTemp.getLastYearSum());
            stockDto.setThreeMonthAmount(saleNumTemp.getThreeMonthSum());
            stockDto.setOneMonthAmount(saleNumTemp.getOneMonthSum());
            stockDto.setYxgAmount(saleNumTemp.getLastYearPart());
            stockDto.setYxgThreeMonthAmount(saleNumTemp.getThreeMonthPart());
            stockDto.setYxgOneMonthAmount(saleNumTemp.getOneMonthPart());
        } else {
            stockDto.setAmount(BigDecimal.ZERO);
            stockDto.setThreeMonthAmount(BigDecimal.ZERO);
            stockDto.setOneMonthAmount(BigDecimal.ZERO);
            stockDto.setYxgAmount(BigDecimal.ZERO);
            stockDto.setYxgThreeMonthAmount(BigDecimal.ZERO);
            stockDto.setYxgOneMonthAmount(BigDecimal.ZERO);
        }
    }

    @Override
    public ResultInfo setSafeStock(HttpServletRequest request, PrepareStockDto stockDto) {
        ResultInfo<?> resultInfo = new ResultInfo<>(0, "操作成功");
        // 更新定品安全库存相关
        User currentUser = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        long nowTime = DateUtil.gainNowDate();
        stockDto.setUpdator(currentUser.getUserId());
        stockDto.setUpdateTime(nowTime);
        if (stockDto.getDeployType() == 1) {
            stockDto.setSafeStock(stockDto.getSafeStockSys());
        }
        try {
            regularPrepareSkuMapper.updatePrepareSkuById(stockDto);
        } catch (Exception e) {
            log.error("updatePrepareSkuById【ERROR】", e);
            log.info("更新备货SKU安全库存失败：{}", stockDto);
            resultInfo = new ResultInfo<>(-1, "更新备货SKU安全库存失败!");
            return resultInfo;
        }
        PrepareStockLog prepareStockLog = new PrepareStockLog();
        prepareStockLog.setSafeStock(stockDto.getSafeStock());
        prepareStockLog.setOriginSafeStock(stockDto.getOriginSafeStock());
        prepareStockLog.setSkuId(stockDto.getSkuId());
        prepareStockLog.setOperateType(stockDto.getOperateType());
        prepareStockLog.setOperateUserId(currentUser.getUserId());
        prepareStockLog.setOperateUserName(currentUser.getUsername());
        prepareStockLog.setOperateTime(nowTime);
        prepareStockLog.setOperateReason(stockDto.getOperateReason());
        prepareStockLog.setPrepareId(stockDto.getRegularId());
        try {
            prepareStockLogMapper.insert(prepareStockLog);
        } catch (Exception e) {
            log.error("prepareStockLogMapper.insert【ERROR】", e);
            log.info("新增安全库存调整日志失败：{}", prepareStockLog);
            resultInfo = new ResultInfo<>(-1, "新增安全库存调整日志失败!");
            return resultInfo;
        }
        return resultInfo;
    }

    @Override
    public ResultInfo<Goods> searchSku(HttpServletRequest request, PrepareStockCommand command) {
        if (command.getSkuNo() == null) {
            return new ResultInfo<>(2, "订货号不能为空！");
        }
        Goods goods = goodsMapper.getSkuInfoBySku(command.getSkuNo());
        if (goods == null) {
            return new ResultInfo<>(2, "没有与搜索条件匹配的数据！");
        }
        PrepareStockDto stockDto = new PrepareStockDto();
        stockDto.setSkuNo(command.getSkuNo());
        dealWithStock(stockDto);
        goods.setStockNum(stockDto.getStock());
        List<String> skuNoList = new ArrayList<>();
        skuNoList.add(command.getSkuNo());
        List<SkuPriceChangeApplyDto> skuPriceApplyList = querySkuCostFromPriceCenter(skuNoList);
        if (CollectionUtils.isEmpty(skuPriceApplyList)) {
            goods.setCost("");
        } else {
            goods.setCost(skuPriceApplyList.get(0).getMiddlePrice());
        }
        ResultInfo<Goods> resultInfo = new ResultInfo<>(0, "查询成功");
        resultInfo.setData(goods);
        return resultInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultInfo dealPrepareStock(HttpServletRequest request, PrepareStockDto stockDto) {
        ResultInfo resultInfo = new ResultInfo(0, "操作成功");
        log.info("处理备货计划对象：{}", stockDto);
        User currentUser = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        long nowTime = DateUtil.gainNowDate();
        stockDto.setUpdator(currentUser.getUserId());
        stockDto.setUpdateTime(nowTime);

        // 删除
        if (DealTypeEnum.PREPARE_DELETE.getCode().equals(stockDto.getDealType())) {
            // 删除定品数据
            try {
                regularPrepareSkuMapper.deletePrepareRegularById(stockDto);
                // 删除备货任务
               earlyWarningTaskMapper.deletePrepareStockBySkuId(stockDto);
            } catch (Exception e) {
                log.error("deletePrepareRegularById【ERROR】", e);
                log.info("删除定品失败：{}", stockDto);
                resultInfo = new ResultInfo<>(-1, "删除定品失败!");
                return resultInfo;
            }
        }
        // 记录操作日志
        int logId = saveOperateLog(currentUser, nowTime, stockDto);
        if (0 == logId) {
            resultInfo = new ResultInfo<>(-1, "新增操作日志失败!");
            return resultInfo;
        }
        if (DealTypeEnum.PREPARE_REPLACE.getCode().equals(stockDto.getDealType())
                || DealTypeEnum.PREPARE_CONVERT.getCode().equals(stockDto.getDealType())) {
            // 增加替换/转换SKU日志
            String replaceSKuNo = stockDto.getReplaceSkuNo();
            if (!replaceSKuNo.startsWith("V")) {
                replaceSKuNo = "V" + replaceSKuNo;
            }
            if (StringUtil.isEmpty(replaceSKuNo)) {
                return resultInfo;
            }
            PrepareStockCommand command = new PrepareStockCommand();
            command.setSkuNo(replaceSKuNo);
            ResultInfo<Goods> goodsResultInfo = searchSku(request, command);
            if (0 == goodsResultInfo.getCode()) {
                Goods goods = goodsResultInfo.getData();
                RegularOperateLog operateLog = new RegularOperateLog();
                operateLog.setParentId(logId);
                operateLog.setRegularId(stockDto.getRegularId());
                operateLog.setLogType(1);
                operateLog.setSkuId(goods.getGoodsId());
                operateLog.setSkuNo(goods.getSku());
                operateLog.setSkuName(goods.getGoodsName());
                operateLog.setBrandName(goods.getBrandName());
                operateLog.setCategoryName(goods.getCategoryName());
                operateLog.setSpec(goods.getModel());
                operateLog.setUnit(goods.getUnitName());
                operateLog.setCost(goods.getCost());
                operateLog.setOwerUserId(goods.getManagerUserId());
                operateLog.setOwerUser(goods.getManagerUserName());
                regularOperateLogMapper.insert(operateLog);
            } else {
                return resultInfo;
            }
         }
        return resultInfo;
    }

    @Override
    public ResultInfo updatePrepare(HttpServletRequest request, String regularIds) {
        ResultInfo resultInfo = new ResultInfo(0, "操作成功");
        if (StringUtil.isEmpty(regularIds)) {
            resultInfo = new ResultInfo(-1, "备货计划不能为空");
        }
        String[] idList = regularIds.split(",");
        List<Integer> list = new ArrayList<>();
        for (String id : idList) {
            list.add(Integer.parseInt(id));
        }
        regularPrepareSkuMapper.updatePrepare(list);
        return resultInfo;
    }

    @Override
    public Map<String, Object> exportPrepareStock(HttpServletRequest request, PrepareStockCommand command) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        String nowMonth = sdf.format(new Date());
        Map<String, Object> exportMap = new HashMap<>();
        List<RegularSnapshot> regularSnapshotList = new ArrayList<>();
        // 导出当前月份定品
        if (nowMonth.equals(command.getExportMonth())) {
            regularSnapshotList = this.syncRegularSkuSnapshot();
        } else {
            regularSnapshotList = regularSnapshotMapper.selectSnapshotListByMonth(command);
        }
        exportMap.put("regularSnapshotList", regularSnapshotList);
        // 查询操作日志
        long startDate = DateUtil.convertLong(command.getExportMonth() + "-01 00:00:00", "yyyy-MM-dd HH:mm:ss");
        long endDate = DateUtil.convertLong(DateUtil.getLastDayOfMonth(command.getExportMonth()) + " 23:59:59", "yyyy-MM-dd HH:mm:ss");
        command.setStartDate(startDate);
        command.setEndDate(endDate);
        // 备货计划操作日志 （不需要控制）
        // command.setLogType(1);
        List<RegularOperateLog> operateLogList = regularOperateLogMapper.getRegularOperateLogList(command);
        List<RegularOperateLog> regularOperateLogList = new ArrayList<>();
        RegularOperateLog emptyLine = new RegularOperateLog();
        if (!CollectionUtils.isEmpty(operateLogList)) {
            for (RegularOperateLog operateLog : operateLogList) {
                if (operateLog == null) {
                    continue;
                }
                operateLog.setOperateTypeStr(DealTypeEnum.codeToMessage(operateLog.getOperateType()));
                regularOperateLogList.add(operateLog);
                if (operateLog.getOperateType() == DealTypeEnum.PREPARE_REPLACE.getCode()
                        || operateLog.getOperateType() == DealTypeEnum.PREPARE_CONVERT.getCode()) {
                    RegularOperateLog regularOperateLog = regularOperateLogMapper.getChileRegularSku(operateLog.getLogId());
                    if (regularOperateLog != null) {
                        regularOperateLogList.add(regularOperateLog);
                    }
                }
                // 插入一个空行
                regularOperateLogList.add(emptyLine);
            }
        }
        exportMap.put("regularOperateLogList", regularOperateLogList);
        log.info("导出备货计划列表：{}", exportMap);
        return exportMap;
    }

    @Override
    public List<RegularSnapshot> syncRegularSkuSnapshot() {
        long nowTime = DateUtil.gainNowDate();
        String lastMonth = DateUtil.getLastMonth();

        PrepareStockCommand command = new PrepareStockCommand();
        command.setNeedPage(1);
        List<PrepareStockDto> prepareStockDtoList = this.getPrepareStockListPage(command, null);
        if (CollectionUtils.isEmpty(prepareStockDtoList)) {
            return null;
        }

        List<Integer> skuIdList = prepareStockDtoList.stream().map(PrepareStockDto::getSkuId).collect(Collectors.toList());

        List<OneThreeMonthLastYearSaleNumTemp> oneThreeMonthLastYearSaleNumTemps = orderingPoolService.getOneThreeMonthLastYearSaleNum(skuIdList);

        if (!CollectionUtils.isEmpty(oneThreeMonthLastYearSaleNumTemps)) {
            prepareStockDtoList.stream().forEach(prepareStock -> {
                oneThreeMonthLastYearSaleNumTemps.stream().forEach(saleNumTemp -> {
                    if (saleNumTemp.getSkuId().intValue() == prepareStock.getSkuId().intValue()) {
                        prepareStock.setAmount(saleNumTemp.getLastYearSum());
                        prepareStock.setThreeMonthAmount(saleNumTemp.getThreeMonthSum());
                        prepareStock.setOneMonthAmount(saleNumTemp.getOneMonthSum());
                        prepareStock.setYxgAmount(saleNumTemp.getLastYearPart());
                        prepareStock.setYxgThreeMonthAmount(saleNumTemp.getThreeMonthPart());
                        prepareStock.setYxgOneMonthAmount(saleNumTemp.getOneMonthPart());
                    }
                });
            });
        }

        List<RegularSnapshot> regularSnapshotList = new ArrayList<>();

        prepareStockDtoList.stream().forEach(prepareStock -> {
            RegularSnapshot regularSnapshot = new RegularSnapshot();
            BeanUtils.copyProperties(prepareStock, regularSnapshot);
            regularSnapshot.setStockWarn(WarnLevelEnum.codeToMessage(prepareStock.getWarnLevel()));
            regularSnapshot.setAddTime(nowTime);
            regularSnapshot.setSnapshotTime(lastMonth);
            regularSnapshotList.add(regularSnapshot);
        });
        return regularSnapshotList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> prepareStockWarn() {
        Map<String, Object> taskMap = new HashMap<>();
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("command", new PrepareStockCommand());
        List<PrepareStockDto> prepareStockDtoList = regularPrepareSkuMapper.getPrepareStockListPage(paramMap);
        if (CollectionUtils.isEmpty(prepareStockDtoList)) {
            return null;
        }
        String nowTime = DateUtil.convertString(DateUtil.gainNowDate(), "yyyy-MM-dd HH:mm:ss");
        long nowTimeLong = DateUtil.gainNowDate();
        List<EarlyWarningTaskDto> insertEarlyWarningTaskList = new ArrayList<>();
        List<EarlyWarningTaskDto> updateEarlyWarningTaskList = new ArrayList<>();
        for (PrepareStockDto prepareStockDto : prepareStockDtoList) {
            dealWithStock(prepareStockDto);
            prepareStockDto.setUpdator(2);
            prepareStockDto.setUpdateTime(nowTimeLong);
            // 处理首次断货时间
            if (0 == prepareStockDto.getHgStock() && prepareStockDto.getFirstOutStockTime() == null) {
                prepareStockDto.setFirstOutStockTime(nowTimeLong);
                regularPrepareSkuMapper.updatePrepareFirstOutStockTimeById(prepareStockDto);
            }
            // 补货，更新断货时间为空
            if (0 != prepareStockDto.getHgStock() && prepareStockDto.getFirstOutStockTime() != null) {
                prepareStockDto.setFirstOutStockTime(null);
                regularPrepareSkuMapper.updatePrepareFirstOutStockTimeById(prepareStockDto);
            }
            // 安全库存为0，不作处理
            if (prepareStockDto.getSafeStock() == 0) {
                continue;
            }
            // 处理人 产品助理+产品经理
            // 升级 + Daisy
            String dealUser = "";
            if (prepareStockDto.getAssignmentAssistantId() != null) {
                dealUser = prepareStockDto.getAssignmentAssistantId() + "";
            }
            if (prepareStockDto.getOwerUserId() != null) {
                if (StringUtil.isEmpty(dealUser)) {
                    dealUser = prepareStockDto.getOwerUserId() + "";
                } else {
                    dealUser += "," + prepareStockDto.getOwerUserId();
                }
            }
            // 预警级别 = 库存数量 / 安全库存
            int warnPercent = new BigDecimal(prepareStockDto.getStock()).divide(new BigDecimal(prepareStockDto.getSafeStock()), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).intValue();
            // 查询是否存在备货计划预警任务
            EarlyWarningTaskDto earlyWarningTaskDto = earlyWarningTaskMapper.getPrepareStockTask(prepareStockDto);
            // 无需预警
            if (warnPercent >= 80) {
                // 原来存在预警，删除该记录
                if (earlyWarningTaskDto != null) {
                    earlyWarningTaskMapper.deleteByPrimaryKey(earlyWarningTaskDto.getEarlyWarningTaskId());
                }
                // 更新备货计划预警等级
                prepareStockDto.setWarnLevel(WarnLevelEnum.SAFE.getCode());
                // 处理人置空
                prepareStockDto.setTaskDealer("");
                regularPrepareSkuMapper.updatePrepareWarnLevelById(prepareStockDto);
                continue;
            }
            // 二级预警
            if (60 < warnPercent && warnPercent < 80) {
                // 新增预警
                if (earlyWarningTaskDto == null) {
                    addTask(insertEarlyWarningTaskList, WarnLevelEnum.SECOND_LEVEL.getCode(), nowTime, prepareStockDto, dealUser);
                } else {
                    // 预警等级相同不作处理
                    if (WarnLevelEnum.SECOND_LEVEL.getCode() == earlyWarningTaskDto.getWaningLevel()) {
                        continue;
                    }
                    // 备货任务存在，且为一级，更新预警等级为二级
                    if (WarnLevelEnum.FIRST_LEVEL.getCode() == earlyWarningTaskDto.getWaningLevel()) {
                        earlyWarningTaskDto.setWaningLevel(WarnLevelEnum.SECOND_LEVEL.getCode());
                        earlyWarningTaskDto.setTaskDealer(dealUser);
                        earlyWarningTaskDto.setUpdateTime(nowTime);
                        earlyWarningTaskDto.setUpdator(2);
                        updateEarlyWarningTaskList.add(earlyWarningTaskDto);
                    }
                }
                // 更新备货计划预警等级
                prepareStockDto.setWarnLevel(WarnLevelEnum.SECOND_LEVEL.getCode());
                // 设置处理人
                prepareStockDto.setTaskDealer(dealUser);
                regularPrepareSkuMapper.updatePrepareWarnLevelById(prepareStockDto);
            }
            // 一级预警
            if (warnPercent <= 60) {
                if (StringUtil.isEmpty(dealUser)) {
                    dealUser = prepareWarnAdminId;
                } else {
                    dealUser += "," + prepareWarnAdminId;
                }
                // 新增预警
                if (earlyWarningTaskDto == null) {
                    addTask(insertEarlyWarningTaskList, WarnLevelEnum.FIRST_LEVEL.getCode(), nowTime, prepareStockDto, dealUser);
                } else {
                    // 预警等级相同不作处理
                    if (WarnLevelEnum.FIRST_LEVEL.getCode() == earlyWarningTaskDto.getWaningLevel()) {
                        continue;
                    }
                    // 备货任务存在，且为二级，更新预警等级为一级
                    if (WarnLevelEnum.SECOND_LEVEL.getCode() == earlyWarningTaskDto.getWaningLevel()) {
                        earlyWarningTaskDto.setWaningLevel(WarnLevelEnum.FIRST_LEVEL.getCode());
                        earlyWarningTaskDto.setTaskDealer(dealUser);
                        earlyWarningTaskDto.setUpdateTime(nowTime);
                        earlyWarningTaskDto.setUpdator(2);
                        updateEarlyWarningTaskList.add(earlyWarningTaskDto);
                    }
                }
                // 更新备货计划预警等级
                prepareStockDto.setWarnLevel(WarnLevelEnum.FIRST_LEVEL.getCode());
                // 设置处理人
                prepareStockDto.setTaskDealer(dealUser);
                regularPrepareSkuMapper.updatePrepareWarnLevelById(prepareStockDto);
            }
        }
        taskMap.put("insertEarlyWarningTaskList", insertEarlyWarningTaskList);
        taskMap.put("updateEarlyWarningTaskList", updateEarlyWarningTaskList);
        return taskMap;
    }

    @Override
    public ResultInfo setForecastSafeRatio(HttpServletRequest request, PrepareStockDto prepareStockDto) {
        ResultInfo resultInfo = new ResultInfo(0, "操作成功");
        if (prepareStockDto.getSafeRatio() == null) {
            resultInfo = new ResultInfo(-1, "安全库存系数不能为空");
        }
        User currentUser = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        long nowTime = DateUtil.gainNowDate();
        prepareStockDto.setCreator(currentUser.getUserId());
        prepareStockDto.setAddTime(nowTime);
        regularPrepareSkuMapper.deleteForecastSafeRatio(prepareStockDto);
        regularPrepareSkuMapper.insertForecastSafeRatio(prepareStockDto);
        return resultInfo;
    }

    @Override
    public Map<String, Object> initSafeStockLogPage(PrepareStockCommand command) {
        Map<String, Object> safeLogMap = new HashMap<>();
        PrepareStockDto prepareStockDto = regularPrepareSkuMapper.selectByPrimaryKey(command.getRegularId());
        List<PrepareStockLogDto> stockLogList = prepareStockLogMapper.getStockLogListByRegularId(command.getRegularId());
        if (!CollectionUtils.isEmpty(stockLogList)) {
            stockLogList.stream().forEach(log -> {
                if (StringUtil.isEmpty(log.getOperateReason())) {
                    log.setOperateReason(AdjustReasonEnum.codeToMessage(log.getOperateType()));
                } else {
                    log.setOperateReason(AdjustReasonEnum.codeToMessage(log.getOperateType()) + "，" + log.getOperateReason());
                }
            });
        }
        safeLogMap.put("prepareStock", prepareStockDto);
        safeLogMap.put("stockLogList", stockLogList);
        return safeLogMap;
    }

    @Override
    public void saveBhOperateLog(HttpSession session, List<PrepareStockDto> stockDtoList) {
        User currentUser = (User) session.getAttribute(ErpConst.CURR_USER);
        long nowTime = DateUtil.gainNowDate();
        if (CollectionUtils.isEmpty(stockDtoList)) {
            return;
        }
        for (PrepareStockDto stockDto : stockDtoList) {
            saveOperateLog(currentUser, nowTime, stockDto);
        }
    }

    @Override
    public List<Buyorder> getIntranstiStockList(PrepareStockCommand command) {
        return regularPrepareSkuMapper.getIntranstiStockList(command);
    }

    @Override
    public DealDetailDto initDealDetailPage(PrepareStockCommand command,Page page){
        DealDetailDto dealDetailResult = new DealDetailDto();
        Map<String, Object> map = new HashMap<>();
        map.put("regularId", command.getRegularId());
        map.put("page", page);
        dealDetailResult.setPrepareStockDto(regularPrepareSkuMapper.selectByPrimaryKey(command.getRegularId()));
        dealDetailResult.setPrepareLog(prepareStockLogMapper.getPrepareLogListByRegularIdListPage(map));
        dealDetailResult.setDeleteLog(prepareStockLogMapper.getDeleteLogListByRegularId(command.getRegularId()));
        dealDetailResult.setDoNotPrepareLog(prepareStockLogMapper.getDoNotPrepareLogListByRegularId(command.getRegularId()));
        dealDetailResult.setRepalceLog(prepareStockLogMapper.getRepalceLogListByRegularId(command.getRegularId()));
        dealDetailResult.setConversionLog(prepareStockLogMapper.getConversionLogListByRegularId(command.getRegularId()));
        dealDetailResult.setPage(page);
        return dealDetailResult;
    }
    /**
     * 添加备货任务
     * @param earlyWarningTaskList
     * @param warnLevel
     * @param nowTime
     */
    private void addTask(List<EarlyWarningTaskDto> earlyWarningTaskList, Integer warnLevel, String nowTime, PrepareStockDto prepareStockDto, String dealUser) {
        EarlyWarningTaskDto newEarlyWarningTask = new EarlyWarningTaskDto();
        newEarlyWarningTask.setEarlyWarningType(3);
        newEarlyWarningTask.setRelateBusinessId(prepareStockDto.getSkuId());
        newEarlyWarningTask.setBusinessExtra1(prepareStockDto.getSkuNo());
        newEarlyWarningTask.setWaningLevel(warnLevel);
        newEarlyWarningTask.setTaskStatus(0);
        newEarlyWarningTask.setTaskDealer(dealUser);
        newEarlyWarningTask.setIsDeleted(0);
        newEarlyWarningTask.setAddTime(nowTime);
        newEarlyWarningTask.setCreator(2);
        newEarlyWarningTask.setUpdateTime(nowTime);
        newEarlyWarningTask.setUpdator(2);
        earlyWarningTaskList.add(newEarlyWarningTask);
    }

    private int saveOperateLog(User currentUser, long nowTime, PrepareStockDto stockDto) {
        RegularOperateLog operateLog = new RegularOperateLog();
        operateLog.setParentId(null);
        operateLog.setRegularId(stockDto.getRegularId());
        operateLog.setLogType(1);
        operateLog.setSkuId(stockDto.getSkuId());
        operateLog.setSkuNo(stockDto.getSkuNo());
        operateLog.setSkuName(stockDto.getSkuName());
        operateLog.setBrandName(stockDto.getBrandName());
        operateLog.setSpuType(stockDto.getSpuType());
        operateLog.setCategoryName(stockDto.getCategoryName());
        operateLog.setSpec(stockDto.getSpec());
        operateLog.setUnit(stockDto.getUnit());
        operateLog.setCost("undefined".equals(stockDto.getCost()) ? "":stockDto.getCost());
        operateLog.setOwerUserId(stockDto.getOwerUserId());
        operateLog.setOwerUser(stockDto.getOwerUser());
        operateLog.setStock(stockDto.getStock());
        operateLog.setIntransitStock(stockDto.getIntransitStock());
        operateLog.setOrderStock(stockDto.getOrderStock());
        operateLog.setForecastSafeStock(stockDto.getForecastSafeStock());
        operateLog.setSafeStock(stockDto.getSafeStock());
        operateLog.setStockWarn(stockDto.getStockWarn());
        operateLog.setSupplementStock(stockDto.getSupplementStock());
        operateLog.setReceiveTimes(stockDto.getReceiveTimes());
        operateLog.setOutStockTimes(stockDto.getOutStockTimes());
        operateLog.setOperateType(stockDto.getDealType());
        operateLog.setOperateUserId(currentUser.getUserId());
        operateLog.setOperateUserName(currentUser.getUsername());
        operateLog.setOperateTime(nowTime);
        operateLog.setOperateReason(stockDto.getOperateReason());
        operateLog.setSaleorderId(stockDto.getSaleorderId());
        operateLog.setLastAddTime(stockDto.getLastAddTime());
        operateLog.setAmount(stockDto.getAmount());
        operateLog.setYxgAmount(stockDto.getYxgAmount());
        operateLog.setThreeMonthAmount(stockDto.getThreeMonthAmount());
        operateLog.setYxgThreeMonthAmount(stockDto.getYxgThreeMonthAmount());
        operateLog.setOneMonthAmount(stockDto.getOneMonthAmount());
        operateLog.setYxgOneMonthAmount(stockDto.getYxgOneMonthAmount());
        int num;
        int logId = 0;
        try {
            num = regularOperateLogMapper.insert(operateLog);
            if (num == 1) {
                logId = operateLog.getLogId();
            }
        } catch (Exception e) {
            log.error("regularOperateLogMapper.insert【ERROR】", e);
            log.info("新增操作日志失败：{}", logId);
        }
        return logId;
    }

    /**
     * 计算平均到货天数
     * @param stockDto
     */
    public void dealWidthReceiveTimes(PrepareStockCommand command, PrepareStockDto stockDto) {
        // 获取包含此SKU的采购单
        command.setSkuNo(stockDto.getSkuNo());
        command.setSkuId(stockDto.getSkuId());
        List<Buyorder> buyorderList = regularPrepareSkuMapper.getBuyorderListByPrepareSku(command);
        if (CollectionUtils.isEmpty(buyorderList)) {
            stockDto.setReceiveTimes(BigDecimal.ZERO);
            return;
        }
        // 采购单数量
        BigDecimal buyorderNum = new BigDecimal(buyorderList.size());
        BigDecimal evgInTime = BigDecimal.ZERO;
        for (Buyorder buyorder : buyorderList) {
            BigDecimal validTime = new BigDecimal(buyorder.getValidTime());
            command.setBuyorderId(buyorder.getBuyorderId());
            List<WarehouseGoodsOperateLog> operateLogs = warehouseGoodsOperateLogMapper.getInputOrderLogListByOrderIdAndGoodsId(command);
            if(CollectionUtils.isEmpty(operateLogs)) {
                continue;
            }
            BigDecimal inStockNum = new BigDecimal(operateLogs.size());
            BigDecimal oneDayTimes = new BigDecimal(24*60*60*1000);
            BigDecimal inStockTime = new BigDecimal(0);
            for (WarehouseGoodsOperateLog operateLog : operateLogs) {
                if (operateLog.getExpirationDate() == null) {
                    continue;
                }
                BigDecimal operateTime = new BigDecimal(operateLog.getExpirationDate());

                inStockTime = inStockTime.add(operateTime.subtract(validTime).divide(oneDayTimes, 1, BigDecimal.ROUND_HALF_UP));
            }
            evgInTime = evgInTime.add(inStockTime.divide(inStockNum, 1, BigDecimal.ROUND_HALF_UP));
        }

        BigDecimal receiveTimes = evgInTime.divide(buyorderNum, 1, BigDecimal.ROUND_HALF_UP);
        stockDto.setReceiveTimes(receiveTimes);
    }

    /**
     * 处理库存数量
     * @param stockDto
     */
    public void dealWithStock(PrepareStockDto stockDto) {
        List<String> skuNoList = new ArrayList<>();
        skuNoList.add(stockDto.getSkuNo());
        Map<String, WarehouseStock> logicalStockMapInfo = warehouseStockService.getLogicalStockMapInfo(skuNoList);
        Map<String, WarehouseStock> stockInfoMap = warehouseStockService.getStockInfo(skuNoList);
        if (stockInfoMap == null) {
            stockDto.setOrderStock(0);
        } else {
            WarehouseStock warehouseStock = stockInfoMap.get(stockDto.getSkuNo());
            if (warehouseStock == null) {
                stockDto.setOrderStock(0);
            } else {
                stockDto.setOrderStock(warehouseStock.getOccupyNum());
            }
        }

        if (logicalStockMapInfo == null) {
            stockDto.setStock(0);
        } else {
            WarehouseStock stockInfoHG = logicalStockMapInfo.get(stockDto.getSkuNo() + LogicalEnum.HG.getLogicalWarehouseId());
            WarehouseStock stockInfoJXQ = logicalStockMapInfo.get(stockDto.getSkuNo() + LogicalEnum.JXQ.getLogicalWarehouseId());
            Integer heStock = stockInfoHG == null ? 0 : stockInfoHG.getAvailableStockNum();
            Integer jxqStock = stockInfoJXQ == null ? 0 : stockInfoJXQ.getAvailableStockNum();
            stockDto.setHgStock(heStock);
            stockDto.setStock(heStock + jxqStock);
        }
    }


    /**
     * 获取采购成本价
     * @param skuNos
     * @return
     */
    public List<SkuPriceChangeApplyDto> querySkuCostFromPriceCenter(List<String> skuNos) {
        if (CollectionUtils.isEmpty(skuNos)) {
            return null;
        }
        List<SkuPriceChangeApplyDto> skuPriceChangeApplyList = new ArrayList<>();
        //skuNo去重
        List<String> uniqueSkuNos = skuNos.stream().distinct().collect(Collectors.toList());
        TypeReference<RestfulResult<List<SkuPriceInfoDetailResponseDto>>> resultTypeReference = new TypeReference<RestfulResult<List<SkuPriceInfoDetailResponseDto>>>(){};
        SkuPriceInfoBatchQueryDto skuPriceInfoBatchQueryDto = new SkuPriceInfoBatchQueryDto();
        skuPriceInfoBatchQueryDto.setSkuNos(uniqueSkuNos);
        log.info("querySkuCostFromPriceCenter:{}", JSONObject.toJSONString(skuPriceInfoBatchQueryDto));
        RestfulResult<List<SkuPriceInfoDetailResponseDto>> listRestfulResult = HttpRestClientUtil.restPost(priceApiUrl + HttpURLConstant.PRICE_BATCH_QUERY_PRICE_INFO_URL, resultTypeReference, new HashMap<>(), skuPriceInfoBatchQueryDto);
        log.info("querySkuCostFromPriceCenter[result]:{}", JSONObject.toJSONString(listRestfulResult));

        if (listRestfulResult == null || Objects.isNull(listRestfulResult.getData())){
            return null;
        }

        List<SkuPriceInfoDetailResponseDto> skuPriceInfoDetailResponseDtoList = listRestfulResult.getData();
        log.info("从价格中心获取成本价 -- {}",skuPriceInfoDetailResponseDtoList);

        for (SkuPriceInfoDetailResponseDto skuPriceInfo : skuPriceInfoDetailResponseDtoList) {
            if(CollectionUtils.isEmpty(skuPriceInfo.getPurchaseList())){
                continue;
            }
            List<BigDecimal> purchasePrice = skuPriceInfo.getPurchaseList().stream().filter(apply -> apply != null && apply.getPurchasePrice() != null).map(apply -> apply.getPurchasePrice()).collect(Collectors.toList());
            SkuPriceChangeApplyDto skuApply = new SkuPriceChangeApplyDto();
            skuApply.setSkuNo(skuPriceInfo.getSkuNo());
            if(CollectionUtils.isEmpty(purchasePrice)){
                skuApply.setMiddlePrice("-");
            }else {
                skuApply.setMiddlePrice(basePriceMaintainService.dealWithPurchasePrice(purchasePrice));
            }
            skuPriceChangeApplyList.add(skuApply);
        }

        return skuPriceChangeApplyList;
    }

}
