package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.annotation.BusinessID;
import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶 应收余额调整单
 */
@Getter
@Setter
@WriteBackField(needBackField = {"FID"})
public class KingDeeNeedReceiveDto extends KingDeeMqBaseDto {


    /**
     * 单据内码  0：表示新增
     * 非0：云星空系统单据FID值，表示修改
     */
    @WriteBackField("FID")
    private String fid;
    /**
     * 单据号
     */
    @BusinessID
    private String fBillNo;
    /**
     * 单据日期 2022-10-10
     */
    private String fVpfnDate;
    /**
     * 组织代码
     */
    private String fVpfnJg;
    /**
     * 客户
     */
    private String fVpfnKh;
    /**
     * fEntity
     */
    private List<KingDeeNeedReceiveEntityDto> fEntityList;

    @Override
    public String getFormId() {
        return KingDeeFormConstant.BD_NEEDREVEIVE;
    }
}
