package com.vedeng.erp.system.web.api;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.annotation.ExcludeAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.utils.validator.ValidatorUtils;
import com.vedeng.common.core.utils.validator.group.AddGroup;
import com.vedeng.common.core.utils.validator.group.UpdateGroup;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.finance.dto.ValueLabelDto;
import com.vedeng.erp.finance.enums.FlowOrderInvoiceTypeEnum;
import com.vedeng.erp.system.domain.dto.*;
import com.vedeng.erp.system.dto.BaseCompanyInfoDto;
import com.vedeng.erp.system.dto.LikeTraderAddressDto;
import com.vedeng.erp.system.dto.LikeTraderContactDto;
import com.vedeng.erp.system.dto.LikeTraderDto;
import com.vedeng.erp.system.service.BaseCompanyInfoApiService;
import com.vedeng.erp.system.service.FlowOrderService;
import com.vedeng.erp.trader.dto.TraderAddressDto;
import com.vedeng.erp.trader.dto.TraderContactDto;
import com.vedeng.erp.trader.dto.TraderSupplierDto;
import com.vedeng.erp.trader.service.TraderAddressApiService;
import com.vedeng.erp.trader.service.TraderContactApiService;
import com.vedeng.erp.trader.service.TraderCustomerApiService;
import com.vedeng.erp.trader.service.TraderSupplierApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 集团业务流转单
 * @date 2022/8/15 15:26
 * @menu 集团业务流转单
 */
@ExceptionController
@RestController
@RequestMapping("/flowOrder")
@Slf4j
public class FlowOrderApi {

    @Autowired
    private FlowOrderService flowOrderService;

    @Autowired
    private TraderCustomerApiService traderCustomerApiService;

    @Autowired
    private TraderSupplierApiService traderSupplierApiService;

    @Autowired
    private TraderContactApiService traderContactApiService;
    @Autowired
    private TraderAddressApiService traderAddressApiService;
    
    @Autowired 
    private BaseCompanyInfoApiService baseCompanyInfoApiService;

    /**
     * 查询所有公司信息
     * @return
     */
    @RequestMapping(value = "/queryAllCompanyInfo", method = RequestMethod.GET)
    @ExcludeAuthorization
    public List<BaseCompanyInfoDto> queryAllCompanyInfo(){
        List<BaseCompanyInfoDto> all = baseCompanyInfoApiService.findAll();
        List<BaseCompanyInfoDto> enableList = all.stream().filter(e -> StrUtil.isNotBlank(e.getCompanyShortName())).collect(Collectors.toList());
        return enableList;
    }


    /**
     * 分页
     */
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @ExcludeAuthorization
    public R<PageInfo<FlowOrderRespDto>> page(@RequestBody PageParam<FlowOrderReqDto> flowOrderDto) {
        return R.success(flowOrderService.page(flowOrderDto));
    }

    /**
     * 徽标
     *
     * @param flowOrderDto
     * @return
     */
    @RequestMapping(value = "/cornerNum", method = RequestMethod.POST)
    @ExcludeAuthorization
    public R<CornerNumDto> cornerNum(@RequestBody PageParam<FlowOrderReqDto> flowOrderDto) {
        CornerNumDto cornerNumDto = flowOrderService.cornerNum(flowOrderDto);
        return R.success(cornerNumDto);
    }

    /**
     * 基于单据类型id初始化
     */
    @RequestMapping(value = "/init", method = RequestMethod.POST)
    @ExcludeAuthorization
    public R<FlowOrderDto> init(@RequestParam String baseBusinessNo, 
                                @RequestParam Integer baseBusinessType, 
                                @RequestParam Integer pushDirection,
                                @RequestParam String sourceErp) {
        return R.success(flowOrderService.init(baseBusinessNo, baseBusinessType,pushDirection,sourceErp));
    }


    /**
     * 添加
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public R<Long> add(@RequestBody FlowOrderDto flowOrderDto) {
        ValidatorUtils.validate(flowOrderDto, AddGroup.class);
        flowOrderService.checkGoodAptitude(flowOrderDto);
        flowOrderService.add(flowOrderDto);
        return R.success(flowOrderDto.getFlowOrderId());
    }

    /**
     * 修改
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public R<Void> update(@RequestBody FlowOrderDto flowOrderDto) {
        ValidatorUtils.validate(flowOrderDto, UpdateGroup.class);
        flowOrderService.checkGoodAptitude(flowOrderDto);
        flowOrderService.update(flowOrderDto);
        
        return R.success();
    }

    /**
     * 删除
     */
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    public R<Void> delete(@RequestParam Long flowOrderId) {
        flowOrderService.delete(CollUtil.newHashSet(flowOrderId));
        return R.success();
    }


    /**
     * 获取详情
     */
    @RequestMapping(value = "/get", method = RequestMethod.GET)
    @ExcludeAuthorization
    public R<FlowOrderDto> get(@RequestParam Long flowOrderId) {
        return R.success(flowOrderService.get(flowOrderId));
    }

    /**
     * 获取单据信息（销售和采购）
     *
     * @param flowOrderId
     * @param baseBusinessType 基础业务类型 1.采购 2.销售
     */
    @RequestMapping(value = "/getBuySaleOrderInfo", method = RequestMethod.GET)
    @ExcludeAuthorization
    public R<List<FlowOrderBuySaleOrderDto>> getBuySaleOrderInfo(@RequestParam Long flowOrderId, @RequestParam Integer baseBusinessType) {
        return R.success(flowOrderService.getBuySaleOrderInfo(flowOrderId, baseBusinessType));
    }


    /**
     * 审核
     */
    @RequestMapping(value = "/audit", method = RequestMethod.GET)
    public R<Void> audit(@RequestParam Long flowOrderId) {
        flowOrderService.audit(flowOrderId);
        return R.success();
    }

    /**
     * 计算节点金额
     */
    @RequestMapping(value = "/calculateNodeAmount", method = RequestMethod.POST)
    @ExcludeAuthorization
    public R<FlowOrderDto> calculateNodeAmount(@RequestBody FlowOrderDto flowOrderDto) {
        return R.success(flowOrderService.calculateNodeAmount(flowOrderDto));
    }

    /**
     * 获取发票类型
     */
    @RequestMapping(value = "/getInvoiceType", method = RequestMethod.POST)
    @ExcludeAuthorization
    public R<List<ValueLabelDto>> getInvoiceType() {
        List<ValueLabelDto> valueLabelDtoList = FlowOrderInvoiceTypeEnum.getValueLabelDtoList();
        return R.success(valueLabelDtoList);
    }

    /**
     * 获取客户/供应商信息
     *
     * @param traderType 1客户 2供应商
     * @param keywords   关键字
     * @return
     */
    @RequestMapping(value = "/getTrader", method = RequestMethod.GET)
    @ExcludeAuthorization
    public R<List<LikeTraderDto>> getTrader(@RequestParam("traderType") Integer traderType, @RequestParam("keywords") String keywords) {
        log.info("查询客户/供应商");
        if (Objects.equals(traderType, 1)) {
            List<LikeTraderDto> trader = traderCustomerApiService.getTrader(keywords);
            return R.success(trader);
        }
        if (Objects.equals(traderType, 2)) {
            List<LikeTraderDto> trader = traderSupplierApiService.findByTraderTypeAndTraderName(keywords);
            return R.success(trader);
        }
        return R.success();
    }

    /**
     * 获取联系人
     *
     * @param traderType 1客户 2供应商
     * @param traderId   供应商ID
     * @param keywords   关键字
     * @return
     */
    @RequestMapping(value = "/getTraderContact", method = RequestMethod.GET)
    @ExcludeAuthorization
    public R<List<LikeTraderContactDto>> getTraderContact(@RequestParam("traderType") Integer traderType,
                                                          @RequestParam("traderId") Integer traderId,
                                                          @RequestParam("keywords") String keywords) {


        List<TraderContactDto> traderContactDtos = traderContactApiService.findLikeNameAndMobile(keywords, traderId, traderType);

        if (CollUtil.isEmpty(traderContactDtos)) {
            return R.success();
        }

        List<LikeTraderContactDto> likeTraderContactDtoList = new ArrayList<>();
        traderContactDtos.forEach(item -> {
            LikeTraderContactDto likeTraderContactDto = new LikeTraderContactDto();
            likeTraderContactDto.setTraderContactId(item.getTraderContactId());
            likeTraderContactDto.setContactName(item.getName());
            likeTraderContactDto.setContactPhone(item.getMobile());
            likeTraderContactDto.setIsDefault(item.getIsDefault());
            likeTraderContactDtoList.add(likeTraderContactDto);
        });


        return R.success(likeTraderContactDtoList);
    }

    /**
     * 获取联系地址
     *
     * @param traderType 1客户 2供应商
     * @param traderId   供应商
     * @param keywords   关键字
     * @return
     */
    @RequestMapping(value = "/getTraderAddress", method = RequestMethod.GET)
    @ExcludeAuthorization
    public R<List<LikeTraderAddressDto>> getTraderAddress(@RequestParam("traderType") Integer traderType,
                                                          @RequestParam("traderId") Integer traderId,
                                                          @RequestParam("keywords") String keywords) {

        List<TraderAddressDto> byTraderIdAndTraderTypeAndAddressLike = traderAddressApiService.findByTraderIdAndTraderTypeAndAddressLike(traderId, traderType, keywords);
        if (CollUtil.isEmpty(byTraderIdAndTraderTypeAndAddressLike)) {
            return R.success();
        }
        List<LikeTraderAddressDto> likeTraderAddressDtoList = new ArrayList<>();
        byTraderIdAndTraderTypeAndAddressLike.forEach(item -> {
            LikeTraderAddressDto likeTraderAddressDto = new LikeTraderAddressDto();
            likeTraderAddressDto.setTraderAddressId(item.getTraderAddressId());
            likeTraderAddressDto.setRegion(item.getArea());
            likeTraderAddressDto.setAddress(item.getAddress());
            likeTraderAddressDto.setIsDefault(item.getIsDefault());
            likeTraderAddressDtoList.add(likeTraderAddressDto);
        });
        return R.success(likeTraderAddressDtoList);
    }

    /**
     * 上传流转单合同文件
     *
     * @param file              合同文件
     * @param flowOrderId       流转单ID
     * @param nodeLevel         节点级别
     * @param flowOrderInfoType 流转单信息类型（0:采购，1:销售）
     * @return 上传结果
     */
    @RequestMapping(value = "/uploadContract", method = RequestMethod.POST)
    @ExcludeAuthorization
    public R<String> uploadContract(HttpServletRequest request,
                                    @RequestParam("file") MultipartFile file,
                                    @RequestParam Long flowOrderId,
                                    @RequestParam Integer nodeLevel,
                                    @RequestParam Integer flowOrderInfoType) {
        try {
            String contractUrl = flowOrderService.uploadContract(request, file, flowOrderId, nodeLevel, flowOrderInfoType);
            return R.success(contractUrl, "合同上传成功");
        } catch (Exception e) {
            log.error("合同上传失败", e);
            return R.error("合同上传失败：" + e.getMessage());
        }
    }
}
