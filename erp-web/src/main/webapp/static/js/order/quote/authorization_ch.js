function chehui() {
	checkLogin();
	var comments = $('#comment').val().length;
	if (comments==0){
		layer.confirm("撤回理由不能为空", {
			btn : [ '确定']
			//按钮
		}, function() {
			layer.closeAll();
		});
		return;
	}

	$.ajax({
		type : "POST",
		url : page_url+"/order/quote/authorizationWithdrow.do",
		data: $('#zzform').serialize(),
		dataType : 'json',
		success : function(data) {
			if (data.data) {
				window.parent.location.reload();
				//window.parent.pagesContrlpages(true,false,true);
			}else {
				layer.alert(data.message);
			}
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});

}
