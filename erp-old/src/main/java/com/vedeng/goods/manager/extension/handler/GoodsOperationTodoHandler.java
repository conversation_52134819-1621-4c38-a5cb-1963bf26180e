package com.vedeng.goods.manager.extension.handler;

import com.vedeng.goods.model.vo.CoreOperateInfoGenerateVo;
import com.vedeng.goods.service.CoreOperateInfoService;
import com.vedeng.todolist.service.ITodoInstance;
import com.vedeng.todolist.service.impl.MaintainDataOperationInfo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Component
public class GoodsOperationTodoHandler extends BaseGoodsTodoHandler {
    @Resource
    private MaintainDataOperationInfo maintainDataOperationInfo;
    @Resource
    private CoreOperateInfoService coreOperateInfoService;

    @Override
    public int getServiceId() {
        return OPERATION_SERVICE_ID;
    }

    @Override
    public ITodoInstance getITodoInstance() {
        return maintainDataOperationInfo;
    }

    @Override
    public boolean isDone() {
        CoreOperateInfoGenerateVo coreOperateInfoQuery = coreOperateInfoService.getCoreOperateInfoBySkuId(getGoodId());
        return coreOperateInfoQuery != null;
    }

    @Override
    public void onHandler() {
        if (!isDone()) {
            addGoodsTodoRecord(getGoodId(), getSkuNo());
        }
    }

    @Override
    public void finish(Object param) {
        if (param instanceof Integer) {
            maintainDataOperationInfo.finish((Integer) param);
        }
    }
}
