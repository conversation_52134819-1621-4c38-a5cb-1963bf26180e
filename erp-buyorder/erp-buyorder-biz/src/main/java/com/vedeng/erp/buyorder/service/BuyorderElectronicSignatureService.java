package com.vedeng.erp.buyorder.service;

import com.vedeng.authorization.model.User;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.order.model.Saleorder;
import org.springframework.web.servlet.ModelAndView;

/**
 * @version 1.0
 * @date 2021/12/9 11:08
 * @describe 电子签章
 */
public interface BuyorderElectronicSignatureService {
    ResultInfo<?> signature(Integer saleorderId, User user);

}
