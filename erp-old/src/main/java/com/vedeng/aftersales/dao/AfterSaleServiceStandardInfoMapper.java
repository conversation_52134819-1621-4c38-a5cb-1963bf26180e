package com.vedeng.aftersales.dao;

import com.newtask.dto.SkuLabelDto;
import com.vedeng.aftersales.model.AfterSaleServiceStandardInfo;
import com.vedeng.aftersales.model.dto.AfterSaleServiceStandardInfoDto;
import org.apache.ibatis.annotations.Param;

public interface AfterSaleServiceStandardInfoMapper {
    int deleteByPrimaryKey(Long serviceStandardInfoId);

    int insert(AfterSaleServiceStandardInfo record);

    int insertSelective(AfterSaleServiceStandardInfo record);

    AfterSaleServiceStandardInfo selectByPrimaryKey(Long serviceStandardInfoId);

    int updateByPrimaryKeySelective(AfterSaleServiceStandardInfo record);

    int updateByPrimaryKey(AfterSaleServiceStandardInfo record);

    AfterSaleServiceStandardInfoDto selectBySkuNo(String skuNo);

    int updateBySkuNo(@Param("skuLabelDto") SkuLabelDto skuLabelDto);

    int insertIntiServiceLabels(@Param("skuLabelDto") SkuLabelDto skuLabelDto);
}