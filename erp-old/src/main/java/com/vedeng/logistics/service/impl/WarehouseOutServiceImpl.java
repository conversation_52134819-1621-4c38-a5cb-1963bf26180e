package com.vedeng.logistics.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.common.constants.Contant;
import com.ctrip.framework.apollo.core.utils.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.vedeng.aftersales.dao.AfterSalesDetailMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesDetail;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.authorization.dao.OrganizationMapper;
import com.vedeng.authorization.model.Organization;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.http.HttpClientUtils;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.util.*;
import com.vedeng.erp.buyorder.dto.PurchaseDeliveryBatchDetailDto;
import com.vedeng.erp.buyorder.service.PurchaseDeliveryBatchDetailService;
import com.vedeng.goods.dao.GoodsMapper;
import com.vedeng.infrastructure.constants.ThirdConstant;
import com.vedeng.infrastructure.sms.service.SmsService;
import com.vedeng.logistics.dao.*;
import com.vedeng.logistics.exception.UrgeDeliveryOrderException;
import com.vedeng.logistics.model.*;
import com.vedeng.logistics.model.vo.WarehouseGoodsOperateLogVo;
import com.vedeng.logistics.service.ExpressService;
import com.vedeng.logistics.service.WarehouseOutService;
import com.vedeng.order.dao.RBuyorderSaleorderMapper;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.model.dto.SmsSaleorderDTO;
import com.vedeng.trader.dao.TraderMapper;
import com.vedeng.trader.model.Trader;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.TaskService;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.runtime.ProcessInstanceQuery;
import org.activiti.engine.task.Task;
import org.activiti.engine.task.TaskQuery;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.vedeng.logistics.eums.UrgerDeliveryOrderResponeCode.DISSATISFY_CONDITION;
import static com.vedeng.logistics.eums.UrgerDeliveryOrderResponeCode.JUST_CAN_CLICK_ONCE;

@Service("warehouseOutService")
public class WarehouseOutServiceImpl extends BaseServiceimpl implements WarehouseOutService {
	public static Logger logger = LoggerFactory.getLogger(WarehouseOutServiceImpl.class);

	@Autowired
	@Qualifier("saleorderMapper")
	private SaleorderMapper saleorderMapper;

	@Autowired
	private LendOutMapper lendOutMapper;

	@Autowired
	@Qualifier("afterSalesMapper")
	private AfterSalesMapper afterSalesMapper;

	@Autowired
	@Qualifier("traderMapper")
	private TraderMapper traderMapper;

	@Autowired
	@Qualifier("afterSalesDetailMapper")
	private AfterSalesDetailMapper afterSalesDetailMapper;

	@Autowired
	@Qualifier("goodsMapper")
	private GoodsMapper goodsMapper;
	@Autowired
	@Qualifier("barcodeMapper")
	private BarcodeMapper barcodeMapper;

	@Autowired
	private ExpressMapper expressMapper;

	@Autowired
	private ExpressService expressService;

	@Autowired
	private SaleorderGoodsMapper saleorderGoodsMapper;

	@Autowired
	@Qualifier("warehouseGoodsOperateLogMapper")
	private WarehouseGoodsOperateLogMapper warehouseGoodsOperateLogMapper;

	@Autowired
	private WarehouseGoodsStatusMapper warehouseGoodsStatusMapper;

	@Autowired
	private OrganizationMapper organizationMapper;

	@Resource
	private OrderNoDict orderNoDict;


	@Value("#{'${notSendMessageTranderIds}'.split(',')}")
	private List<Integer> notSendMessageTranderIds;

	@Resource
	private WarehouseGoodsOperateLogVirtualMapper warehouseGoodsOperateLogVirtualMapper;

	@Resource
	private RBuyorderSaleorderMapper rBuyorderSaleorderMapper;

	@Resource
	private ExpressDetailMapper expressDetailMapper;

	@Autowired
	private PurchaseDeliveryBatchDetailService purchaseDeliveryBatchDetailService;

	@Autowired
	private SmsService smsService;

	@Autowired
	private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

	public static final List<Integer> NEED_BINDING_TYPE = Arrays.asList(SysOptionConstant.ID_496, SysOptionConstant.ID_660);


	@Override
	public List<Map<String,Object>> getWarehouseBySaleorderId(Integer saleorderId){
		return warehouseGoodsOperateLogMapper.getWarehouseBySaleorderId(saleorderId);
	}

	@Override
	public List<WarehouseLog> getWarehouseLogGroupByAddTime(List<Integer> idList) {
		return warehouseGoodsOperateLogMapper.getWarehouseLogById(idList);
	}

	@Override
	public List<WarehouseLog> getWarehouseLogDirectGroupByAddTime(List<Integer> idList) {
		return warehouseGoodsOperateLogVirtualMapper.getWarehouseLogDirectById(idList);
	}

	@Override
	public List<WarehouseGoodsOperateLog> getOutDetil(Saleorder saleorder) {

		List<WarehouseGoodsOperateLog> list = warehouseGoodsOperateLogMapper.getWarehouseOutList(saleorder);
		return list;
	}

	@Override
	public List<WarehouseGoodsOperateLog> getInventoryOutOrderDetail(Long inventoryOutOrderId) {

		return  warehouseGoodsOperateLogMapper.getWarehouseOutListByInventoryOutOrderId(inventoryOutOrderId);
	}

	@Override
	public List<WarehouseGoodsOperateLog> getBarcodeOutDetil(Saleorder saleorder) {
		List<WarehouseGoodsOperateLog> list = null;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<List<WarehouseGoodsOperateLog>>> TypeRef = new TypeReference<ResultInfo<List<WarehouseGoodsOperateLog>>>() {};
		String url=httpUrl + "warehouseout/getwarehousebarcodeoutlist.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url,saleorder,clientId,clientKey, TypeRef);
			list = (List<WarehouseGoodsOperateLog>) result.getData();
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return list;
	}

	@Override
	public WarehouseGoodsOperateLog getSMGoods(WarehouseGoodsOperateLog warehouseGoodsOperateLog) {
		// 接口调用
		String url = httpUrl + "warehousegoodsoperatelog/getsmwgolog.htm";
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<WarehouseGoodsOperateLog>> TypeRef2 = new TypeReference<ResultInfo<WarehouseGoodsOperateLog>>() {};
		try {
			ResultInfo<WarehouseGoodsOperateLog> result2 = (ResultInfo<WarehouseGoodsOperateLog>) HttpClientUtils.post(url, warehouseGoodsOperateLog, clientId, clientKey,TypeRef2);
			if (null == result2) {
				return null;
			}
			WarehouseGoodsOperateLog res = (WarehouseGoodsOperateLog) result2.getData();
			return res;
		} catch (IOException e) {
			return null;
		}
	}

	@Override
	public List<WarehouseGoodsOperateLog> getSGoods(WarehouseGoodsOperateLog warehouseGoodsOperateLog) {
		List<WarehouseGoodsOperateLog> list = null;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<List<WarehouseGoodsOperateLog>>> TypeRef = new TypeReference<ResultInfo<List<WarehouseGoodsOperateLog>>>() {};
		String url=httpUrl + "warehousegoodsoperatelog/getsgoods.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, warehouseGoodsOperateLog,clientId,clientKey, TypeRef);
			list = (List<WarehouseGoodsOperateLog>) result.getData();
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return list;
	}

	@Override
	public List<WarehouseGoodsOperateLog> getOutDetilList(Saleorder saleorder) {
		List<WarehouseGoodsOperateLog> list = null;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<List<WarehouseGoodsOperateLog>>> TypeRef = new TypeReference<ResultInfo<List<WarehouseGoodsOperateLog>>>() {};
		String url=httpUrl + "warehouseout/getwarehouseshoutlist.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url,saleorder,clientId,clientKey, TypeRef);
			list = (List<WarehouseGoodsOperateLog>) result.getData();
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return list;
	}

	@Override
	@Transactional
	public LendOut saveLendOut(LendOut lendout) {
		lendout.setModTime(DateUtil.gainNowDate());
		lendout.setAddTime(DateUtil.gainNowDate());
		lendout.setLendOutStatus(0);
		SaleorderGoods goods = goodsMapper.getGoodsInfoById(lendout.getGoodsId());
		lendout.setGoodsName(goods.getGoodsName());
		lendout.setSku(goods.getSku());
		if(lendout.getLendOutType()==1) {//样品外借
			Trader trader = traderMapper.getTraderByTraderId(lendout.getTraderId());
			lendout.setTraderName(trader.getTraderName());
//			Integer type =traderMapper.getTraderTypeById(lendout.getTraderId());
//			lendout.setTraderType(type);//1经销商  2供应商
			lendout.setAfterSalesId(0);
			lendout.setAfterSalesNo("");
			Integer i = lendOutMapper.insertSelective(lendout);
			lendout.setLendOutNo(orderNoDict.getOrderNum(lendout.getLendOutId(), OrderNoDict.LEND_OUT_TYPE));
			Integer j = lendOutMapper.updateByPrimaryKeySelective(lendout);
		}else {//售后垫货
			AfterSales aftersales = new AfterSales();
			aftersales.setAfterSalesNo(lendout.getAfterSalesNo());
			AfterSales aftersaleInfo = afterSalesMapper.getAfterSalesByNo(lendout.getAfterSalesNo());
			AfterSalesDetail afterSalesDetail = afterSalesDetailMapper.selectadtbyid(aftersaleInfo);
			lendout.setTraderName(afterSalesDetail.getTraderName());
			lendout.setAfterSalesId(aftersaleInfo.getAfterSalesId());
			lendout.setTraderId(afterSalesDetail.getTraderId());
			lendout.setTakeTraderAddressId(afterSalesDetail.getAddressId());
			lendout.setTakeTraderAreaId(afterSalesDetail.getAreaId());
			lendout.setTakeTraderContactId(afterSalesDetail.getTraderContactId());
//			Integer type =traderMapper.getTraderTypeById(lendout.getTraderId());
			lendout.setTraderType(1);//1经销商  2供应商
			Saleorder saleorderInfo = saleorderMapper.getSaleOrderById(aftersaleInfo.getOrderId());
			lendout.setLogisticsId(saleorderInfo.getLogisticsId());//物流公司id
			lendout.setLogisticsComments(saleorderInfo.getLogisticsComments());//物流备注
			lendout.setFreightDescription(saleorderInfo.getFreightDescription());//运费说明
			Integer i = lendOutMapper.insertSelective(lendout);
			lendout.setLendOutNo(orderNoDict.getOrderNum(lendout.getLendOutId(), OrderNoDict.LEND_OUT_TYPE));
			Integer j = lendOutMapper.updateByPrimaryKeySelective(lendout);
		}
		LendOut lendoutResult = lendOutMapper.selectByPrimaryKey(lendout.getLendOutId());
		logger.info("saveLendOut:"+lendoutResult);
		return lendoutResult;
	}

	@Override
	public LendOut getLendOutInfo(LendOut lendout) {
		return lendOutMapper.selectByPrimaryKey(lendout.getLendOutId());
	}

	@Override
	public List<WarehouseGoodsOperateLogVo> getLendOutDetil(Saleorder saleorder) {
		List<WarehouseGoodsOperateLogVo> list = warehouseGoodsOperateLogMapper.getWarehouseLendOutList(saleorder);
		return list;
	}

	@Override
	public List<LendOut> getLendOutInfoList(AfterSalesVo afterSalesVo) {
		LendOut lendout = LendOut.getinstance();
		lendout.setAfterSalesId(afterSalesVo.getAfterSalesId());
		lendout.setAfterSalesNo(afterSalesVo.getAfterSalesNo());
		lendout.setLendOutStatus(0);//进行中
		lendout.setLendOutType(2);//售后换货
		return lendOutMapper.getLendOutInfoList(lendout);
	}

	@Override
	public Integer getkdNum(LendOut lendout) {
		lendout.setBusinessType(660);
		return lendOutMapper.getKdNum(lendout);
	}

	@Override
	public Integer getdeliveryNum(LendOut lendout) {
		Integer num = lendOutMapper.getdeliveryNum(lendout);
		return -num;
	}

	@Override
	public Integer updateLendoutDeliverNum(LendOut lendout) {
		return lendOutMapper.editDeliverNum(lendout);
	}
	/***
	 * 根据入库id获取条码信息
	 */
	@Override
	public Barcode getBarcodeByWarehouseGoodsOperateLogId(Integer warehouseGoodsOperateLogId) {
		Barcode barcode = barcodeMapper.getBarcodeByWarehouseGoodsOperateLogId(warehouseGoodsOperateLogId);
		return barcode;
	}

	@Override
	public String getBuyOrderNoByBarcodeId(Barcode barcode) {
		String orderNo= barcodeMapper.getBuyOrderNoByBarcodeId(barcode);
		return orderNo;
	}

	@Override
	public Integer updatefirstRegistraionInfo(List<WarehouseGoodsOperateLog> woList, List<WarehouseGoodsOperateLog> firstInfo, Integer titleType, String type_f, Integer printFlag) {
		StringBuffer sb = new StringBuffer();
		//更新为首营的信息
		for (WarehouseGoodsOperateLog log : woList) {
			try {
				BigDecimal num = new BigDecimal(-log.getNum());
				//排除外借出库
				if (log.getOperateType() != 10) {
					log.setMaxSkuRefundAmount(log.getPrice().multiply(num));
				}
				sb.replace(0, sb.length() + 1, "");
				//判断是否为医疗器械
				if (StringUtil.isNotEmpty(log.getRegistrationNumber()) && log.getRegistrationNumber().length() > 0) {
					if(log.getTitle() != null) {
						titleType = log.getTitle().equals("一类") ? 1 : 2;
					}else{
						titleType = 2;
					}
				} else {
					log.setRegistrationNumber(" ");
					log.setRecordNumber(" ");
					log.setManufacturer(" ");
					log.setProductionLicenseNumber(" ");
				}

				if (log.getConditionOne() != null && log.getConditionOne() == 983) {
					sb.append("常温  ");
				} else if (log.getConditionOne() != null && log.getConditionOne() == 984) {
					sb.append("冷藏  ");
				} else if (log.getConditionOne() != null && log.getConditionOne() == 985) {
					sb.append("冷冻  ");
				}
				if (StringUtil.isNotEmpty(log.getTemperaTure()) && log.getTemperaTure() != null) {
					sb.append(log.getTemperaTure()).append("度");
				}
				log.setTemperaTure(sb.toString());
				//是否是医疗器械
				Boolean ismachine = isMachineByGoodsId(log.getGoodsId());
				if(type_f.equals(ErpConst.PRINT_HC_TYPE_F)&&!ismachine){
					updateBlankfield(log);
				}
				if(printFlag.equals(ErpConst.PRICE_PRINT_ORDERTYPE) || printFlag.equals(ErpConst.NOPRICE_PRINT_ORDERTYPE)){
					updateBlankfield2(log);
				}
			} catch (Exception e) {
				logger.error("打印出库单error:{}", e);
			}
		}
		return titleType;
	}
	/**
	* 是否是医疗器械 ture 是  false 否
	* @Author:strange
	* @Date:16:24 2020-03-12
	*/
	private Boolean isMachineByGoodsId(Integer goodsId) {
		Integer spuType = goodsMapper.getGoodsSpuType(goodsId);
		if(spuType!= null && spuType.equals(316)){
			return true;
		}
		return false;
	}

	private void updateBlankfield2(WarehouseGoodsOperateLog log) {
		if (StringUtils.isBlank(log.getRegistrationNumber())) {
			log.setRegistrationNumber("\\");
		}
		if (StringUtils.isBlank(log.getRecordNumber())) {
			log.setRecordNumber("\\");
		}
		if (StringUtils.isBlank(log.getManufacturer())) {
			log.setManufacturer("\\");
		}
		if (StringUtils.isBlank(log.getProductionLicenseNumber())) {
			log.setProductionLicenseNumber("\\");
		}
		if (StringUtils.isBlank(log.getTemperaTure())) {
			log.setTemperaTure("\\");
		}
		if (StringUtils.isBlank(log.getBatchNumber())) {
			log.setBatchNumber("\\");
		}
		if (StringUtils.isBlank(log.getMaterialCode())) {
			log.setMaterialCode("\\");
		}
		if(StringUtils.isBlank(log.getProductCompanyLicence())){
			log.setProductCompanyLicence("\\");
		}

		if(log.getExpirationDate() == null || log.getExpirationDate().equals(0L)){
			log.setTitle("\\");
		}
		if(log.getProductDate() == null || log.getProductDate().equals(0L)){
			log.setProductDateStr("\\");
		}
	}

	private void updateBlankfield(WarehouseGoodsOperateLog log) {
		if (StringUtils.isBlank(log.getRegistrationNumber())) {
			log.setRegistrationNumber("\\");
		}
		if (StringUtils.isBlank(log.getRecordNumber())) {
			log.setRecordNumber("\\");
		}
		if (StringUtils.isBlank(log.getManufacturer())) {
			log.setManufacturer("\\");
		}
		if (StringUtils.isBlank(log.getProductionLicenseNumber())) {
			log.setProductionLicenseNumber("\\");
		}
		if (StringUtils.isBlank(log.getTemperaTure())) {
			log.setTemperaTure("\\");
		}
		if (StringUtils.isBlank(log.getBatchNumber())) {
			log.setBatchNumber("\\");
		}
		if (StringUtils.isBlank(log.getMaterialCode())) {
			log.setMaterialCode("\\");
		}
		if(StringUtils.isBlank(log.getProductCompanyLicence())){
			log.setProductCompanyLicence("\\");
		}

		if(log.getExpirationDate() == null || log.getExpirationDate().equals(0L)){
			log.setTitle("\\");
		}
	}

	/**
	*保存快递详情和出入库记录关联关系
	* @Author:strange
	* @Date:14:14 2020-02-10
	*/
	@Override
	public void addExpressDeatilsWarehouse(Express express1) {

		List<ExpressDetail> expressDetailList = express1.getExpressDetail();
		if(CollectionUtils.isEmpty(expressDetailList)) {
			return;
		}
		for (ExpressDetail expressDetail : expressDetailList) {
			if(expressDetail.getBusinessType() != null && NEED_BINDING_TYPE.contains(expressDetail.getBusinessType())){
				expressDetail.setWmsOrderNo(express1.getWmsOrderNo());
				//获取未关联快递详情的出入库记录id
				List<WarehouseGoodsOperateLog> warehouseLogIdList = warehouseGoodsOperateLogMapper.getWarehouseIdByExpressDetail(expressDetail);
				if(CollectionUtils.isEmpty(warehouseLogIdList)) {
					return;
				}
				for (WarehouseGoodsOperateLog warehouseLog : warehouseLogIdList) {
					warehouseGoodsOperateLogMapper.updateExpressWarehouse(warehouseLog.getWarehouseGoodsOperateLogId());
					int i = warehouseGoodsOperateLogMapper.insertExpressWarehouse(warehouseLog.getWarehouseGoodsOperateLogId(), expressDetail.getExpressDetailId());
				}
			}
		}
	}
	/**
	 *判断此出入库记录是否关联快递单
	 * @Author:strange
	 * @Date:14:39 2020-02-10
	 */
	@Override
	public Boolean isEnableExpress(Integer wlogId) {
		Integer expressId = expressMapper.getExpressIdByWlogId(wlogId);
		if(expressId != null){
			return true;
		}
		return false;
	}
	/**
	 *获取关联快递的出入库记录id
	 * @Author:strange
	 * @Date:19:28 2020-02-10
	 */
	@Override
	public List<Integer> getExpressWlogIds(Integer expressId) {

		return warehouseGoodsOperateLogMapper.getExpressWlogIds(expressId);
	}

	@Override
	public HashMap<String,BigDecimal> addMvAmoutinfo(List<WarehouseGoodsOperateLog> woList, Saleorder saleorder,Integer expressType) {
		HashMap<String,BigDecimal> result = new HashMap<>();
		List<SaleorderGoods> saleorderGoodsList = saleorderGoodsMapper.getSaleorderGoodsById(saleorder);

		Map<Integer, SaleorderGoods> saleorderGoodsMap = saleorderGoodsList.stream().collect(Collectors.toMap(SaleorderGoods::getSaleorderGoodsId, goods -> goods));
		//获取物流信息
		List<Express> expressList = getExpressListBySaleOrderGoodsList(saleorderGoodsList,expressType);
		Integer expressId = saleorder.getExpressId();
		List<ExpressDetail> nowExpressDetail = expressMapper.getExpressDetailByExpressId(expressId);
		//获取运费
		BigDecimal expressPrice = getExpressPrice(saleorderGoodsList,expressId,expressList);
		//总金额
		BigDecimal totalPrice = BigDecimal.ZERO;
		for (WarehouseGoodsOperateLog warehouseGoodsOperateLog : woList) {
			totalPrice = totalPrice.add(warehouseGoodsOperateLog.getRealPrice().multiply(new BigDecimal(-warehouseGoodsOperateLog.getNum())));
		}
		//获取原有快递中商品数量
		HashMap<Integer,Integer> oldExpressDetailNumMap = getOldExpressDetailNumMap(expressId,expressList,expressType);

		//实付金额
		BigDecimal realTotalPrice = getRealTotalPrice(saleorderGoodsMap,oldExpressDetailNumMap,nowExpressDetail,expressId,expressType);

		//优惠券价格
		BigDecimal couponsPrice = totalPrice.subtract(realTotalPrice);

		realTotalPrice = realTotalPrice.add(expressPrice);

		result.put("expressPrice",expressPrice);
		result.put("totalPrice",totalPrice);
		result.put("realTotalPrice",realTotalPrice);
		result.put("couponsPrice",couponsPrice);

		return result;
	}

    private HashMap<Integer, Integer> getOldExpressDetailNumMap(Integer expressId, List<Express> expressList,Integer isDirect) {
        HashMap<Integer, Integer> oldExpressDetailNumMap = new HashMap<>(16);
        expressList.forEach(express -> {
            if(!express.getExpressId().equals(expressId) && express.getExpressId() < expressId){
                List<ExpressDetail> expressDetaillist = express.getExpressDetail();
                expressDetaillist.forEach(detail -> {
                    Integer oldNum = oldExpressDetailNumMap.get(detail.getRelatedId());
                    if (oldNum == null) {
                        oldNum = 0;
                    }
					if(ErpConst.ZERO.equals(isDirect)){
						//普发
						oldExpressDetailNumMap.put(detail.getRelatedId(), oldNum + detail.getNum());
					}else {
						//直发
						//快递单明细关联的是采购商品id，需要查到对应销售单id
						Integer saleorderGoodsId = rBuyorderSaleorderMapper.querySaleorderGoodsIdByBuyorderGoodsId(detail.getRelatedId());
						oldExpressDetailNumMap.put(saleorderGoodsId, oldNum + detail.getNum());
					}
                });
            }
        });
        return oldExpressDetailNumMap;
    }

    private BigDecimal getRealTotalPrice(Map<Integer, SaleorderGoods> saleorderGoodsMap, HashMap<Integer, Integer> oldExpressDetailNumMap,
										 List<ExpressDetail> nowExpressDetail, Integer expressId,Integer isDirect) {
		BigDecimal realTotalPrice = BigDecimal.ZERO;
		for (ExpressDetail expressDetail : nowExpressDetail) {
			Integer nowDetailNum = expressDetail.getNum();
			SaleorderGoods saleorderGoods = new SaleorderGoods();
			Integer oldDetailNum = 0;
			if(ErpConst.ZERO.equals(isDirect)){
				saleorderGoods = saleorderGoodsMap.get(expressDetail.getRelatedId());
				oldDetailNum = oldExpressDetailNumMap.get(expressDetail.getRelatedId());
			}else {
				Integer saleorderGoodsId = rBuyorderSaleorderMapper.querySaleorderGoodsIdByBuyorderGoodsId(expressDetail.getRelatedId());
				saleorderGoods = saleorderGoodsMap.get(saleorderGoodsId);
				oldDetailNum = oldExpressDetailNumMap.get(saleorderGoodsId);
			}
			Integer saleGoodsNum = saleorderGoods.getNum()- saleorderGoods.getAfterReturnNum();
			if(oldDetailNum == null){
				oldDetailNum = 0;
			}
			//本次快递数量+原有快递数量 < 商品总数 则实付为单价 * 数量
			if(nowDetailNum + oldDetailNum < saleGoodsNum){
				realTotalPrice = realTotalPrice.add(saleorderGoods.getPrice().multiply(new BigDecimal(nowDetailNum)));

			}else if((nowDetailNum + oldDetailNum) >= saleGoodsNum ){
				//本次快递数量+原有快递数量 >= 商品总数 则实付为总价-原快递内的总价
				BigDecimal oldTotalPrice = saleorderGoods.getPrice().multiply(new BigDecimal(oldDetailNum));
				realTotalPrice = realTotalPrice.add(saleorderGoods.getMaxSkuRefundAmount().subtract(oldTotalPrice));
			}
		}
		return realTotalPrice;
	}

	private BigDecimal getExpressPrice(List<SaleorderGoods> saleorderGoodsList, Integer expressId,List<Express> expressList) {
		//当前快递运费价格
		BigDecimal expressPrice = BigDecimal.ZERO;
		for (SaleorderGoods saleorderGoods : saleorderGoodsList) {
			if(saleorderGoods.getSku().equals("V127063")){
				expressPrice = saleorderGoods.getPrice();
				continue;
			}
		}
		//如果不是第一个快递单则运费为0
		for (Express express : expressList) {
			if(express.getExpressId() < expressId){
				expressPrice = BigDecimal.ZERO;
				break;
			}
		}
		return expressPrice;
	}

	private List<Express> getExpressListBySaleOrderGoodsList(List<SaleorderGoods> saleorderGoodsList,Integer isDirect) {
		//获取订单内商品快递
		List<Express> expressList = null;
		// 物流信息
		Express express = new Express();
		express.setCompanyId(1);
		List<Integer> relatedIds = new ArrayList<Integer>();
		for (SaleorderGoods sg : saleorderGoodsList) {
			if(ErpConst.ZERO.equals(isDirect) && ErpConst.ZERO.equals(sg.getDeliveryDirect())){
				//普发
				relatedIds.add(sg.getSaleorderGoodsId());
			}else if(ErpConst.ONE.equals(isDirect) && ErpConst.ONE.equals(sg.getDeliveryDirect())){
				//直发
				relatedIds.add(sg.getSaleorderGoodsId());
			}
		}
		if(ErpConst.ZERO.equals(isDirect)){
			//普发查询物流信息
			express.setBusinessType(SysOptionConstant.ID_496);
		}else {
			//直发查询物流信息
			express.setBusinessType(SysOptionConstant.ID_515);
			if(CollectionUtils.isNotEmpty(relatedIds)){
				relatedIds = rBuyorderSaleorderMapper.queryBuyorderGoodsIdsBySaleorderGoodsIds(relatedIds);
			}else {
				logger.info("直发查询物流信息为空{},", JSON.toJSONString(saleorderGoodsList));
			}
		}

		if (relatedIds != null && relatedIds.size() > 0) {
			express.setRelatedIds(relatedIds);
			try {
				expressList =  expressMapper.getLendOutExpressInfo(express);
			} catch (Exception e) {
				logger.error("addMvAmoutinfo  error", e);
			}
		}
		return expressList;
	}
    @Override
    public int getWarehouseoutRecordCounts(int saleorderId) {
        return this.warehouseGoodsOperateLogMapper.getWarehouseoutRecordCounts(saleorderId);
    }

	@Override
	public Long getLastOutTime(WarehouseGoodsOperateLog w,Integer isDirect) {
		List<Integer> idList = w.getIdList();
		if(CollectionUtils.isNotEmpty(idList)){
			Long lastOutTime = DateUtil.sysTimeMillis();
			if(ErpConst.ZERO.equals(isDirect)){
				lastOutTime = warehouseGoodsOperateLogMapper.getLastOutTime(w);
			}else {
				lastOutTime = warehouseGoodsOperateLogVirtualMapper.getLastOutTime(w);
			}
			return lastOutTime;
		}
		return DateUtil.sysTimeMillis();
	}
	/**
	 *获取订单下有效出库记录
	 * @Author:strange
	 * @Date:13:48 2020-02-26
	 */
	@Override
	public List<Integer> getWarehouseLogIdBy(Integer saleorderId) {

		return warehouseGoodsOperateLogMapper.getWarehouseLogIdBy(saleorderId);
	}

	/**
	 * 获取出库单商品总额
	 * <AUTHOR>
	 * @Date 7:04 下午 2020/5/26
	 * @Param
	 * @return
	 **/
	@Override
	public BigDecimal getPrintOutTotalPrice(List<WarehouseGoodsOperateLog> woList) {
		BigDecimal reslut = BigDecimal.ZERO;
		if(CollectionUtils.isNotEmpty(woList)){
			for (WarehouseGoodsOperateLog warehouseGoodsOperateLog : woList) {
				logger.info("获取商品总价：{}", JSON.toJSONString(warehouseGoodsOperateLog));
				reslut = reslut.add(warehouseGoodsOperateLog.getAmount());
			}
		}
		return reslut;
	}
	/**
	 *组装出库单内普发出库记录id
	 * @Author:strange
	 * @param isDirect 是否直发0否1是
	 * @Date:17:45 2020-02-26
	 */
	@Override
	public List<Integer> getPrintOutIdListByType(Saleorder saleorder,Integer isDirect,Integer expressType) {
		List<Integer> idList = new ArrayList<>();
		String type_f = saleorder.getOptType();
		if(StringUtils.isBlank(type_f)){
			return idList;
		}
		String wdlIds = saleorder.getSearch();
		if(type_f.equals(ErpConst.PRINT_EXPRESS_HC_TYPE_F) ||
				type_f.equals(ErpConst.PRINT_EXPRESS_KYG_TYPE_F) ||
				type_f.equals(ErpConst.PRINT_EXPRESS_PRICE_TYPE_F) ||
				type_f.equals(ErpConst.PRINT_EXPRESS_NOPRICE_TYPE_F) ||
				type_f.equals(ErpConst.PRINT_EXPRESS_PRICE_JC_TYPE_F) ||
				type_f.equals(ErpConst.PRINT_EXPRESS_JC_TYPE_F) ||
				type_f.equals(ErpConst.PRINT_EXPRESS_STANDARD_TYPE_F)){
			Integer expressId = saleorder.getExpressId();
			Integer type = saleorder.getType();
			/**直发项目重构，此处逻辑不会触发调用（视为作废）
			if(type != null && type.equals(1)){
			 直发项目重构，此处逻辑不会触发调用（视为作废）**/
			//wms出库记录id
			//直发普发出库记录id和快递单的绑定信息保存在V_E_W_EXPRESS_WAREHOUSE表，因此此处查询逻辑不用变化
			if(isDirect.equals(expressType)){
				if (ObjectUtils.isEmpty(expressId) && saleorder.getExpressIds().size() > 0) {
					if(ErpConst.ONE.equals(isDirect)){
						//直发需校验快递单已维护的批次是否已完全出库
						for(Integer expressId1 : saleorder.getExpressIds()){
							if(!isAllDelivery(expressId1)){
								return new ArrayList<>();
							}
						}
					}
					idList = warehouseGoodsOperateLogMapper.getExpressWMSWlogIdsByExpressIds(saleorder.getExpressIds());
				} else {
					if(ErpConst.ONE.equals(isDirect)){
						//直发需校验快递单已维护的批次是否已完全出库
						if(!isAllDelivery(expressId)){
							return new ArrayList<>();
						}
					}
					idList = warehouseGoodsOperateLogMapper.getExpressWMSWlogIds(expressId);
				}
			}
				/**直发项目重构，此处逻辑不会触发调用（视为作废）
			}else {
				if (ObjectUtils.isEmpty(expressId) && saleorder.getExpressIds().size() > 0) {
					idList = warehouseGoodsOperateLogMapper.getExpressWMSWlogIdsByExpressIds(saleorder.getExpressIds());
				} else {
					idList = warehouseGoodsOperateLogMapper.getExpressWMSWlogIds(expressId);
				}
			}直发项目重构，此处逻辑不会触发调用（视为作废）**/
		}else if(type_f.equals(ErpConst.PRINT_OUT_TYPE_F)||
				type_f.equals(ErpConst.PRINT_EXPIRATIONDATE_TYPE_F) ||
				type_f.equals(ErpConst.PRINT_HC_TYPE_F) ||
				type_f.equals(ErpConst.PRINT_KYG_TYPE_F) ||
				type_f.equals(ErpConst.PRINT_PRICE_TYPE_F) ||
				type_f.equals(ErpConst.PRINT_NOPRICE_TYPE_F) ||
				type_f.equals(ErpConst.PRINT_JC_TYPE_F) ||
				type_f.equals(ErpConst.PRINT_PRICE_JC_TYPE_F) ||
				type_f.equals(ErpConst.PRINT_STANDARD_TYPE_F)){

			if (null != wdlIds && !wdlIds.equals("")) {
				// 切割拼接成的字符串
				String[] p = wdlIds.split("#");
				if (p != null && p.length > 1 && !p[0].equals("")) {
					String[] ids = p[0].split("_");
					for (String id : ids) {
						idList.add(Integer.valueOf(id));
					}
				}
			}
		}else if(type_f.equals(ErpConst.PRINT_FLOWERORDER_TYPE_F)){
			if(ErpConst.ZERO.equals(isDirect)){
				//查询普发关联出库记录id
				idList = getWarehouseLogIdBy(saleorder.getSaleorderId());
			}else {
				//查询直发关联出库记录id
				idList = warehouseGoodsOperateLogVirtualMapper.queryWarehouseLogIdBySaleorderId(saleorder.getSaleorderId());
			}
		}
		return idList;
	}

	/**
	 * <AUTHOR>
	 * @desc 根据快递单id查询已维护批次是否已全部出库
	 * @param expressId
	 * @return
	 */
	private boolean isAllDelivery(Integer expressId){
		List<ExpressDetail> expressDetailList = expressDetailMapper.selectAllByExpressId(expressId);
		for(ExpressDetail expressDetail : expressDetailList){
			List<PurchaseDeliveryBatchDetailDto> deliveryDirectBatchDetailDtoList = purchaseDeliveryBatchDetailService.queryInfoByExpressDetailId(expressDetail.getExpressDetailId());
			for(PurchaseDeliveryBatchDetailDto purchaseDeliveryBatchDetailDto : deliveryDirectBatchDetailDtoList){
				if(!purchaseDeliveryBatchDetailDto.getArrivalCount().equals(purchaseDeliveryBatchDetailDto.getWmsHandledDeliveryCount())){
					return false;
				}
			}
		}
		return true;
	}

	@Override
	public List<WarehouseGoodsOperateLog> getPrintOutSkuAmount(List<WarehouseGoodsOperateLog> woList, Saleorder saleorder1,
															   boolean pricePrintoutFlag,Integer expressType) {
		if(CollectionUtils.isNotEmpty(woList)){
			WarehouseGoodsOperateLog warehouseGoodsOperateLog1 = woList.get(0);
			Saleorder saleorder = saleorderMapper.getSaleorderBySaleorderGoodsId(warehouseGoodsOperateLog1.getRelatedId());
			if(saleorder != null && saleorder.getOrderType().equals(1)) {
				List<SaleorderGoods> saleorderGoodsList = saleorderGoodsMapper.getSaleorderGoodsById(saleorder);
				Map<Integer, SaleorderGoods> saleorderGoodsMap = saleorderGoodsList.stream().collect(Collectors.toMap(SaleorderGoods::getSaleorderGoodsId, goods -> goods));
				HashMap<Integer,Integer> oldExpressDetailNumMap = new HashMap<>();
				if(pricePrintoutFlag){
					Integer expressId = saleorder1.getExpressId();
					List<Express> expressList = new ArrayList();
					if(ErpConst.ZERO.equals(expressType)){
						//普发快递单
						//获取物流信息
						expressList = getExpressListBySaleOrderGoodsList(saleorderGoodsList,ErpConst.ZERO);
						//获取原有快递中商品数量
						oldExpressDetailNumMap = getOldExpressDetailNumMap(expressId,expressList,ErpConst.ZERO);
					}else {
						//直发快递单
						//获取采购物流信息
						expressList = getExpressListBySaleOrderGoodsList(saleorderGoodsList,ErpConst.ONE);
						//获取原有快递中商品数量
						oldExpressDetailNumMap = getOldExpressDetailNumMap(expressId,expressList,ErpConst.ONE);
					}
				}
				//设置商品总金额
				setLogAmount(woList, saleorderGoodsMap, oldExpressDetailNumMap);
			}else {
				for (WarehouseGoodsOperateLog warehouseGoodsOperateLog : woList) {
					Integer num = warehouseGoodsOperateLog.getNum();
					BigDecimal price = warehouseGoodsOperateLog.getPrice();
					warehouseGoodsOperateLog.setAmount(price.multiply(new BigDecimal(-num)));
				}
			}
		}
		return woList;
	}

	private void setLogAmount(List<WarehouseGoodsOperateLog> woList, Map<Integer, SaleorderGoods> saleorderGoodsMap, HashMap<Integer, Integer> oldExpressDetailNumMap) {
		logger.info("setLogAmount-woList:{}", JSONObject.toJSONString(woList));
		logger.info("setLogAmount-saleorderGoodsMap:{}", JSONObject.toJSONString(saleorderGoodsMap));
		logger.info("setLogAmount-oldExpressDetailNumMap:{}", JSONObject.toJSONString(oldExpressDetailNumMap));

		//本次数量
		HashMap<Integer, Integer> nowNumMap = new HashMap<>();
		for (WarehouseGoodsOperateLog log : woList) {
			SaleorderGoods saleorderGoods = saleorderGoodsMap.get(log.getRelatedId());
			int realNum = saleorderGoods.getNum() - saleorderGoods.getAfterReturnNum();
			Integer nowNum = nowNumMap.get(log.getRelatedId());
			if(nowNum == null){
				nowNum = 0;
			}
			Integer oldNum = oldExpressDetailNumMap.get(log.getRelatedId());
			if(oldNum == null){
				oldNum = 0;
			}
			nowNum = nowNum + (-log.getNum());
			if(nowNum + oldNum < realNum){
				log.setAmount(saleorderGoods.getPrice().multiply(new BigDecimal(-log.getNum())));
			}else if(nowNum + oldNum >= realNum){
				BigDecimal maxSkuRefundAmount = saleorderGoods.getMaxSkuRefundAmount().subtract(saleorderGoods.getAfterReturnAmount());
				BigDecimal oldTotalPrice = saleorderGoods.getPrice().multiply(new BigDecimal(oldNum));
				log.setAmount(maxSkuRefundAmount.subtract(oldTotalPrice).subtract(log.getPrice().multiply(new BigDecimal(nowNum+log.getNum()))));
			}
			nowNumMap.put(log.getRelatedId(),nowNum);
		}
	}

	@Override
	public List<WarehouseGoodsOperateLogVo> getSameBatchGoodsInfo(WarehouseGoodsOperateLogVo warehouseGoodsOperateLogVo) {
		return warehouseGoodsOperateLogMapper.getSameBatchGoodsInfo(warehouseGoodsOperateLogVo);
	}

	@Override
	public Integer getLendOutdeliveryNum(LendOut lo) {
		return lendOutMapper.getdeliveryNum(lo);
	}

	@Override
	public Integer getGoodsStockByGoodsStatus(WarehouseGoodsStatus warehouseGoodsStatus) {
		return  warehouseGoodsStatusMapper.getGoodsStock(warehouseGoodsStatus);
	}

	@Override
	public Integer updateLendOutInfo(LendOut lendout) {
		return lendOutMapper.updateByPrimaryKeySelective(lendout);
	}

	@Override
	public Integer getLendOutKdNum(LendOut lendout) {
		return lendOutMapper.getKdNum(lendout);
	}

	@Override
	public void sendDeliveryShortMessage() {
		// 查询出所有符合条件的订单信息
//		logger.info("traderIds:{}", JSON.toJSONString(traderIds));
		notSendMessageTranderIds = notSendMessageTranderIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
//		logger.info("traderIds:{}", JSON.toJSONString(traderIds));
		List<SmsSaleorderDTO> allSmsSaleOrder = saleorderMapper.getAllSmsSaleOrder(notSendMessageTranderIds);
		if (!allSmsSaleOrder.isEmpty()) {
			allSmsSaleOrder.forEach(smsSaleorderDTO -> {
				String content = null;
				String traderContactMobile = smsSaleorderDTO.getTraderContactMobile();
				String takeTraderContactMobile = smsSaleorderDTO.getTakeTraderContactMobile();
				// 发送短信后将Express表的SENT_SMS置为1
				Express express = new Express();
				express.setExpressId(smsSaleorderDTO.getExpressId());
				express.setSentSms(1);
				// 贝登
				if (StringUtil.isNotEmpty(traderContactMobile) && StringUtil.isNotEmpty(takeTraderContactMobile)) {
					if (smsSaleorderDTO.getOrderType() == 1 || smsSaleorderDTO.getOrderType() == 3) {
						content = "@1@=" + smsSaleorderDTO.getSaleorderNo() + ",@2@=" + smsSaleorderDTO.getExpressName() + ",@3@=" + smsSaleorderDTO.getLogisticsNo();
						Boolean sendTplSms = smsService.sendTplSms(traderContactMobile, "JSM40187-0067", content);
						expressMapper.updateByPrimaryKeySelective(express);
					} else if (smsSaleorderDTO.getOrderType() == 5 || smsSaleorderDTO.getOrderType() == 9) {
						// 医械购
						content = "@1@=" + smsSaleorderDTO.getSaleorderNo() + ",@2@=" + smsSaleorderDTO.getExpressName() + ",@3@=" + smsSaleorderDTO.getLogisticsNo();
						Boolean sendTplSms = smsService.sendTplSms(traderContactMobile, "JSM40187-0068", content, ThirdConstant.YXG);
						expressMapper.updateByPrimaryKeySelective(express);
					} else if (smsSaleorderDTO.getOrderType() == 8 || smsSaleorderDTO.getOrderType() == 7) {
						// 集采(下单人和收货人都发)
						content = "@1@=" + smsSaleorderDTO.getTraderName() + ",@2@=" + smsSaleorderDTO.getTraderContactName() + ",@3@=" + smsSaleorderDTO.getSaleorderNo() + ",@4@=" + smsSaleorderDTO.getExpressName() + ",@5@=" + smsSaleorderDTO.getLogisticsNo() + ",@6@=" + smsSaleorderDTO.getSaleMobile();
						Boolean sendTplSms = smsService.sendTplSms(traderContactMobile, "JSM40187-0069", content, ThirdConstant.YXG);
						content = "@1@=" + smsSaleorderDTO.getTraderName() + ",@2@=" + smsSaleorderDTO.getTakeTraderName() + ",@3@=" + smsSaleorderDTO.getSaleorderNo() + ",@4@=" + smsSaleorderDTO.getExpressName() + ",@5@=" + smsSaleorderDTO.getLogisticsNo() + ",@6@=" + smsSaleorderDTO.getSaleMobile();
						Boolean sendTplSms2 = smsService.sendTplSms(takeTraderContactMobile, "JSM40187-0069", content, ThirdConstant.YXG);
						expressMapper.updateByPrimaryKeySelective(express);
					} else if (smsSaleorderDTO.getOrderType() == 0) {
						// VS单特殊判断
						if (getVsOrderDep(smsSaleorderDTO.getSaleId()) == 36) {
							// 科研购
							content = "@1@=" + smsSaleorderDTO.getSaleorderNo() + ",@2@=" + smsSaleorderDTO.getExpressName() + ",@3@=" + smsSaleorderDTO.getLogisticsNo();
							Boolean sendTplSms = smsService.sendTplSms(traderContactMobile, "JSM40187-0070", content, ThirdConstant.KYG);
						} else if (getVsOrderDep(smsSaleorderDTO.getSaleId()) == 39) {
							// 医械购
							content = "@1@=" + smsSaleorderDTO.getSaleorderNo() + ",@2@=" + smsSaleorderDTO.getExpressName() + ",@3@=" + smsSaleorderDTO.getLogisticsNo();
							Boolean sendTplSms = smsService.sendTplSms(traderContactMobile, "JSM40187-0068", content, ThirdConstant.YXG);
						} else {
							// 贝登
							content = "@1@=" + smsSaleorderDTO.getSaleorderNo() + ",@2@=" + smsSaleorderDTO.getExpressName() + ",@3@=" + smsSaleorderDTO.getLogisticsNo();
							Boolean sendTplSms = smsService.sendTplSms(traderContactMobile, "JSM40187-0067", content);
						}
						expressMapper.updateByPrimaryKeySelective(express);
					}
				}
			});
		}
	}

    @Override
    public void urgeDeliveryOrder(Integer saleorderId) {

		//查询是否已催办
		 List<Buyorder> buyorderList = saleorderMapper.queryUrgeDeliveryOrder(saleorderId);


		if (CollectionUtils.isEmpty(buyorderList)){
			throw new UrgeDeliveryOrderException(DISSATISFY_CONDITION);
		}

		if (ErpConst.ONE.equals(buyorderList.get(0).getUrgedMaintainBatchInfo())){
			throw new UrgeDeliveryOrderException(JUST_CAN_CLICK_ONCE);
		}

        for (Buyorder buyorder : buyorderList) {
            //催办
            saleorderMapper.urgeDeliveryOrder(buyorder.getBuyorderId());

            //站内信通知
            List<Integer> userlist = new ArrayList<>();
            userlist.add(buyorder.getCreator());

            Map<String, String> map = new HashMap<>();
            map.put("buyorderNo",buyorder.getBuyorderNo());

            String url = "./order/buyorder/viewBuyorder.do?buyorderId=" + buyorder.getBuyorderId();

            MessageUtil.sendMessage(224, userlist, map, url);
        }

	}

	@Override
	public List<WarehouseGoodsOperateLog> getWGOlistByComments(String batChNo,Integer saleorderId) {
		List<WarehouseGoodsOperateLog> wgOlistByComments = warehouseGoodsOperateLogMapper.getWGOlistByComments(batChNo,saleorderId);
		return wgOlistByComments;
	}


	// 根据销售id查询所属的三个一级部门(贝登、医械购、科研购)
    private Integer getVsOrderDep(Integer saleId) {
        Organization organization = organizationMapper.getOrgIdByUserId(saleId);
        return getParentOrg(organization.getOrgId());
    }

    // 递归方法，根据部门id查询所属的一级部门(贝登、医械购、科研购)
    private Integer getParentOrg(Integer orgId) {
        Organization organization = organizationMapper.selectByPrimaryKey(orgId);
        if (organization.getLevel() <= 3) {
            return organization.getOrgId();
        } else {
            return getParentOrg(organization.getParentId());
        }
    }

	@Override
	public List<Integer> getOrderIdUnderReview(String processDefinitionKey) {
		try {
			CurrentUser user = CurrentUser.getCurrentUser();
			TaskService taskService = processEngine.getTaskService();

			Set<String> processInstanceIds = taskService.createTaskQuery()
					.taskCandidateOrAssigned(user.getUsername())
					.list()
					.stream()
					.map(Task::getProcessInstanceId)
					.collect(Collectors.toSet());

			if (CollectionUtils.isEmpty(processInstanceIds)) {
				return new ArrayList<>();
			}
			List<ProcessInstance> processInstanceList = processEngine.getRuntimeService()
					.createProcessInstanceQuery()
					.processDefinitionKey(processDefinitionKey)
					.processInstanceIds(processInstanceIds)
					.list();
			return processInstanceList.stream()
					.map(item -> Integer.valueOf(item.getBusinessKey().split("_")[1]))
					.collect(Collectors.toList());

		} catch (Exception e) {
			logger.error("获取审核流程信息异常", e);
			return new ArrayList<>();
		}
	}

}
