package com.vedeng.authorization.service;

import com.vedeng.authorization.model.User;
import java.util.List;

/**
 * 权限校验service.
 * @jira: VDERP-5839 数据越权修复.
 * @notes: .
 * @version: 1.0.
 * @date: 2021/3/16 上午10:29.
 * @author: Tomcat.Hui.
 */
public interface AuthService {


     String BUSSCHANCE_TYPE = "商机类型";

     String SALEORDER_TYPE = "销售订单类型";

     String BH_SALEORDER_TYPE = "备货订单类型";

     String QUOTEORDER_TYPE = "报价单类型";

     String TRADER_CUSTOMER_TYPE = "供应商客户类型";

     String AUTHORIZATION_APPLY_TYPE = "授权书类型";

    /**
     * 获取客户对应销售(报价单、客户信息、耗材订单详情).
     * @jira: VDERP-5839 数据越权修复.
     * @notes: 根据客户获取.
     * @version: 1.0.
     * @date: 2021/3/16 下午1:09.
     * @author: Tomcat.Hui.
     * @param traderId: .
     * @param traderType: .
     * @return: java.lang.Integer.
     * @throw: .
     */
    User getTraderOwnerUser(Integer traderId, Integer traderType);

    /**
     * 获取用户ID.
     * @jira: VDERP-5839 数据越权修复.
     * @notes: 售后订单创建者、商机.
     * @version: 1.0.
     * @date: 2021/3/16 下午1:37.
     * @author: Tomcat.Hui.
     * @param userId: .
     * @return: com.vedeng.authorization.model.User.
     * @throw: .
     */
    User getUserById(Integer userId);


    /**
     * 更具角色名获取用户名字
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2021/3/26 14:17.
     * @author: Randy.Xu.
     * @param
     * @return: java.util.List<com.vedeng.authorization.model.User>.
     * @throws:  .
     */
    List<User> getUserByRoleNameList(List<String> roleNameList);

    /**
     * 判断用户是否为特定用户人员
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2021/3/26 15:27.
     * @author: Randy.Xu.
     * @param user
     * @return: java.lang.Boolean.
     * @throws:  .
     */
    Boolean checkUserInRole(User user, String roleNameStr);




    /**
     * 根据用户ID查询出所有上级信息
     */
    List<User> getUserParentListById(Integer userId);

    /**
     * 根据商机、售后、销售订单Id 查询归属以及归属的所有上级
     * @param orderId
     * @param orderType
     * @return
     */
    List<User> getUserListByorderId(Integer orderId, String orderType);


    /**
     * 判断user的Id是否存在于userlist中
     * @param user
     * @param userList
     * @return
     */
    Boolean existOrNot(User user, List<User> userList);


    /**
     * 根据商机、售后、销售订单、客户ID 查询出归属用户ID
     */

    Integer getOwnerUserIdByorderId(Integer orderId, String orderType);


    /**
     * 根据沟通记录得到saleorderId;
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2021/4/2 11:21.
     * @author: Randy.Xu.
     * @param
     * @param communicateRecordId
     * @return: java.lang.Integer.
     * @throws:  .
     */
    Integer getSaleorderIdByCommunicateRecordId(Integer communicateRecordId);

    /**
     * 判断是否是销售
     * @param user
     * @return
     */
    boolean checkUserIsSale(User user);
}
