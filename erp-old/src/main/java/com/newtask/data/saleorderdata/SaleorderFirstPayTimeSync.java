package com.newtask.data.saleorderdata;

import com.newtask.data.AbstractDataSync;
import com.newtask.data.dao.SaleorderDataMapper;
import com.newtask.data.dto.SaleoderDataDto;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 初始化首次付款时间
 */
@Component
public class SaleorderFirstPayTimeSync extends AbstractDataSync {

    @Resource
    SaleorderDataMapper saleorderDataMapper;

    @Override
    public List<Integer> loadBizId(Long startTime, Long endTime) {
        // 获取首次付款时间数据id
        List<Integer> saleorderIdsList = saleorderDataMapper.getSaleorderFirstPayTimeIdsList(startTime, endTime);

        return saleorderIdsList;
    }

    @Override
    public List<Map<String, Object>> loadBizData(List<Integer> bizIds) {
        List<Map<String, Object>> firstPayTime = null;

        if (CollectionUtils.isNotEmpty(bizIds)) {
            firstPayTime = saleorderDataMapper.findFirstPayTimeBySaleorderId(bizIds);
        }

        return firstPayTime;
    }

    @Override
    public void updateData(List<Map<String, Object>> dataList) {
        if (CollectionUtils.isNotEmpty(dataList)) {
            long timeMillis = System.currentTimeMillis();
            List<SaleoderDataDto> saleoderDataDtos = new ArrayList<>();
            dataList.forEach(item -> {
                Integer saleorderId = NumberUtils.toInt(item.get("saleorderId").toString());

                SaleoderDataDto saleoderDataDto = new SaleoderDataDto();
                saleoderDataDto.setSaleorderId(saleorderId);
                saleoderDataDto.setFirstPayTime(NumberUtils.toLong(item.get("firstPayTime")+""));
                saleoderDataDtos.add(saleoderDataDto);
                // 判断saleorderId是否存在
                makeExist(saleorderId, timeMillis);
            });
            // 根据id修改firstPayTime
            saleorderDataMapper.updateSaleorderFirstPayTime(saleoderDataDtos, timeMillis);
        }
    }

    @Override
    public void makeExist(Integer id, Long nowTime) {

        Long count = saleorderDataMapper.getSaleorderById(id);
        // 如果count小于等于0，添加数据
        if (count <= 0) {
            saleorderDataMapper.insertSaleOrder(id, nowTime);
        }

    }
}
