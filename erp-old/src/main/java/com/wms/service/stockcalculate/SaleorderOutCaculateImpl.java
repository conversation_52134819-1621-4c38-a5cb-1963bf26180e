package com.wms.service.stockcalculate;

import com.vedeng.logistics.model.WarehouseStock;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.wms.dto.StockCalculateDto;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 销售出库单库存计算实现类
 */
@Service("saleorderOutCaculateImpl")
public class SaleorderOutCaculateImpl extends AbstractStockinfoCaculateInterace {


    @Override
    protected void calculateStockNum(List<StockCalculateDto> details, List<WarehouseDto> warehouseStockList) {
        /*details.stream().forEach(stockCalculateDto->{

            WarehouseDto warehouseStock = super.getWarehouseStockBySkuAndLogcic(warehouseStockList,
                    stockCalculateDto.getSku(),
                    stockCalculateDto.getLogicalWarehouseId());

            //减少总的库存数量
            warehouseStock.setStockNum(-Math.abs(stockCalculateDto.getStockNum()));
        });*/

        super.calculateStockOrOccupyNumBase(details, warehouseStockList, true,true);
    }

    @Override
    protected void calculateOcupyNum(List<StockCalculateDto> details, List<WarehouseDto> warehouseStockList) {
        /*details.stream().forEach(stockCalculateDto->{

            WarehouseDto warehouseStock = super.getWarehouseStockBySkuAndLogcic(warehouseStockList,
                    stockCalculateDto.getSku(),
                    stockCalculateDto.getLogicalWarehouseId());

            //减少占用库存数量
            warehouseStock.setOccupyNum(-Math.abs(stockCalculateDto.getOccupyNum()));
        });*/

        super.calculateStockOrOccupyNumBase(details, warehouseStockList, true,false);
    }
}
