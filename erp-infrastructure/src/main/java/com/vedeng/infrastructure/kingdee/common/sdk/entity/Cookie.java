package com.vedeng.infrastructure.kingdee.common.sdk.entity;


import java.util.Date;

/* loaded from: k3cloud-webapi-sdk7.9.2.jar:com/kingdee/bos/webapi/entity/Cookie.class */
public class Cookie {
    String name;
    String value;
    Date expires;
    String path;
    String domain;
    boolean secure;

    public Cookie(String cookie) {
        String[] array = cookie.split(";");
        for (int i = 0; i < array.length; i++) {
            String s = array[i].trim();
            if (s.length() > 2) {
                String[] sarr = s.split("=");
                if (sarr.length == 2) {
                    String lowerCase = sarr[0].toLowerCase();
                    char c = 65535;
                    switch (lowerCase.hashCode()) {
                        case -1326197564:
                            if (lowerCase.equals("domain")) {
                                c = 2;
                                break;
                            }
                            break;
                        case -1309235404:
                            if (lowerCase.equals("expires")) {
                                c = 0;
                                break;
                            }
                            break;
                        case 3433509:
                            if (lowerCase.equals("path")) {
                                c = 1;
                                break;
                            }
                            break;
                    }
                    switch (c) {
                        case 0:
                            break;
                        case 1:
                            this.path = sarr[1];
                        case ConstDefine.QueryInvode_State_Complete /* 2 */:
                            this.domain = sarr[1];
                            break;
                        default:
                            if (i == 0) {
                                this.name = sarr[0];
                                this.value = sarr[1];
                                break;
                            }
                            break;
                    }
                }
                if (s.equals("SECURE")) {
                    this.secure = true;
                }
            }
        }
    }

    public static Cookie parse(String ck) {
        Cookie c = new Cookie(ck);
        if (c.getName() == null || c.getName() == "") {
            return null;
        }
        return c;
    }

    @Override
    public String toString() {
        return String.format("%s=%s", this.name, this.value);
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return this.value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public Date getExpires() {
        return this.expires;
    }

    public void setExpires(Date expires) {
        this.expires = expires;
    }

    public String getPath() {
        return this.path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getDomain() {
        return this.domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public boolean isSecure() {
        return this.secure;
    }

    public void setSecure(boolean secure) {
        this.secure = secure;
    }
}
