<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.finance.mapper.InvoiceRedConfirmationItemMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.finance.domain.entity.InvoiceRedConfirmationItemEntity">
    <!--@mbg.generated-->
    <!--@Table T_INVOICE_RED_CONFIRMATION_ITEM-->
    <id column="INVOICE_RED_CONFIRMATION_ITEM_ID" jdbcType="INTEGER" property="invoiceRedConfirmationItemId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="INVOICE_RED_CONFIRMATION_ID" jdbcType="INTEGER" property="invoiceRedConfirmationId" />
    <result column="UUID" jdbcType="VARCHAR" property="uuid" />
    <result column="INVOICE_RED_CONFIRMATION_NO" jdbcType="VARCHAR" property="invoiceRedConfirmationNo" />
    <result column="SERIAL_NUMBER" jdbcType="INTEGER" property="serialNumber" />
    <result column="PROJECT_NAME" jdbcType="VARCHAR" property="projectName" />
    <result column="PROJECT_SIMPLE_NAME" jdbcType="VARCHAR" property="projectSimpleName" />
    <result column="PROJECT_FULL_NAME" jdbcType="VARCHAR" property="projectFullName" />
    <result column="TAX_CATEGORY_NO" jdbcType="VARCHAR" property="taxCategoryNo" />
    <result column="SPECIFICATIONS" jdbcType="VARCHAR" property="specifications" />
    <result column="UNIT" jdbcType="VARCHAR" property="unit" />
    <result column="TAX_RATE" jdbcType="DECIMAL" property="taxRate" />
    <result column="QUANTITY" jdbcType="DECIMAL" property="quantity" />
    <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
    <result column="UNIT_PRICE" jdbcType="DECIMAL" property="unitPrice" />
    <result column="UAX_AMOUNT" jdbcType="DECIMAL" property="uaxAmount" />
    <result column="PRICE_PLUS_TAXES" jdbcType="DECIMAL" property="pricePlusTaxes" />
    <result column="BUSINESS_ORDER_ITEM_ID" jdbcType="INTEGER" property="businessOrderItemId" />
    <result column="AFTER_SALE_BUSINESS_ORDER_ITEM_ID" jdbcType="INTEGER" property="afterSaleBusinessOrderItemId" />
    <result column="BLUE_INVOICE_ITEM_ID" jdbcType="INTEGER" property="blueInvoiceItemId" />
    <result column="THIS_TIME_SURPLUS_NUMBER" jdbcType="DECIMAL" property="thisTimeSurplusNumber" />
    <result column="THIS_TIME_SURPLUS_AMOUNT" jdbcType="DECIMAL" property="thisTimeSurplusAmount" />
    <result column="THIS_TIME_SURPLUS_UAX_AMOUNT" jdbcType="DECIMAL" property="thisTimeSurplusUaxAmount" />
    <result column="THIS_TIME_SURPLUS_PRICE_PLUS_TAXES" jdbcType="DECIMAL" property="thisTimeSurplusPricePlusTaxes" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    INVOICE_RED_CONFIRMATION_ITEM_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, 
    UPDATER_NAME, INVOICE_RED_CONFIRMATION_ID, UUID, INVOICE_RED_CONFIRMATION_NO, SERIAL_NUMBER, 
    PROJECT_NAME, PROJECT_SIMPLE_NAME, PROJECT_FULL_NAME, TAX_CATEGORY_NO, SPECIFICATIONS, 
    UNIT, TAX_RATE, QUANTITY, AMOUNT, UNIT_PRICE, UAX_AMOUNT, PRICE_PLUS_TAXES, BUSINESS_ORDER_ITEM_ID, 
    AFTER_SALE_BUSINESS_ORDER_ITEM_ID, BLUE_INVOICE_ITEM_ID, THIS_TIME_SURPLUS_NUMBER, 
    THIS_TIME_SURPLUS_AMOUNT, THIS_TIME_SURPLUS_UAX_AMOUNT, THIS_TIME_SURPLUS_PRICE_PLUS_TAXES
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_INVOICE_RED_CONFIRMATION_ITEM
    where INVOICE_RED_CONFIRMATION_ITEM_ID = #{invoiceRedConfirmationItemId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_INVOICE_RED_CONFIRMATION_ITEM
    where INVOICE_RED_CONFIRMATION_ITEM_ID = #{invoiceRedConfirmationItemId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="INVOICE_RED_CONFIRMATION_ITEM_ID" keyProperty="invoiceRedConfirmationItemId" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceRedConfirmationItemEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_INVOICE_RED_CONFIRMATION_ITEM (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      INVOICE_RED_CONFIRMATION_ID, UUID, INVOICE_RED_CONFIRMATION_NO, 
      SERIAL_NUMBER, PROJECT_NAME, PROJECT_SIMPLE_NAME, 
      PROJECT_FULL_NAME, TAX_CATEGORY_NO, SPECIFICATIONS, 
      UNIT, TAX_RATE, QUANTITY, 
      AMOUNT, UNIT_PRICE, UAX_AMOUNT, 
      PRICE_PLUS_TAXES, BUSINESS_ORDER_ITEM_ID, AFTER_SALE_BUSINESS_ORDER_ITEM_ID, 
      BLUE_INVOICE_ITEM_ID, THIS_TIME_SURPLUS_NUMBER, 
      THIS_TIME_SURPLUS_AMOUNT, THIS_TIME_SURPLUS_UAX_AMOUNT, 
      THIS_TIME_SURPLUS_PRICE_PLUS_TAXES)
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{invoiceRedConfirmationId,jdbcType=INTEGER}, #{uuid,jdbcType=VARCHAR}, #{invoiceRedConfirmationNo,jdbcType=VARCHAR}, 
      #{serialNumber,jdbcType=INTEGER}, #{projectName,jdbcType=VARCHAR}, #{projectSimpleName,jdbcType=VARCHAR}, 
      #{projectFullName,jdbcType=VARCHAR}, #{taxCategoryNo,jdbcType=VARCHAR}, #{specifications,jdbcType=VARCHAR}, 
      #{unit,jdbcType=VARCHAR}, #{taxRate,jdbcType=DECIMAL}, #{quantity,jdbcType=DECIMAL}, 
      #{amount,jdbcType=DECIMAL}, #{unitPrice,jdbcType=DECIMAL}, #{uaxAmount,jdbcType=DECIMAL}, 
      #{pricePlusTaxes,jdbcType=DECIMAL}, #{businessOrderItemId,jdbcType=INTEGER}, #{afterSaleBusinessOrderItemId,jdbcType=INTEGER}, 
      #{blueInvoiceItemId,jdbcType=INTEGER}, #{thisTimeSurplusNumber,jdbcType=DECIMAL}, 
      #{thisTimeSurplusAmount,jdbcType=DECIMAL}, #{thisTimeSurplusUaxAmount,jdbcType=DECIMAL}, 
      #{thisTimeSurplusPricePlusTaxes,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" keyColumn="INVOICE_RED_CONFIRMATION_ITEM_ID" keyProperty="invoiceRedConfirmationItemId" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceRedConfirmationItemEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_INVOICE_RED_CONFIRMATION_ITEM
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="invoiceRedConfirmationId != null">
        INVOICE_RED_CONFIRMATION_ID,
      </if>
      <if test="uuid != null">
        UUID,
      </if>
      <if test="invoiceRedConfirmationNo != null">
        INVOICE_RED_CONFIRMATION_NO,
      </if>
      <if test="serialNumber != null">
        SERIAL_NUMBER,
      </if>
      <if test="projectName != null">
        PROJECT_NAME,
      </if>
      <if test="projectSimpleName != null">
        PROJECT_SIMPLE_NAME,
      </if>
      <if test="projectFullName != null">
        PROJECT_FULL_NAME,
      </if>
      <if test="taxCategoryNo != null">
        TAX_CATEGORY_NO,
      </if>
      <if test="specifications != null">
        SPECIFICATIONS,
      </if>
      <if test="unit != null">
        UNIT,
      </if>
      <if test="taxRate != null">
        TAX_RATE,
      </if>
      <if test="quantity != null">
        QUANTITY,
      </if>
      <if test="amount != null">
        AMOUNT,
      </if>
      <if test="unitPrice != null">
        UNIT_PRICE,
      </if>
      <if test="uaxAmount != null">
        UAX_AMOUNT,
      </if>
      <if test="pricePlusTaxes != null">
        PRICE_PLUS_TAXES,
      </if>
      <if test="businessOrderItemId != null">
        BUSINESS_ORDER_ITEM_ID,
      </if>
      <if test="afterSaleBusinessOrderItemId != null">
        AFTER_SALE_BUSINESS_ORDER_ITEM_ID,
      </if>
      <if test="blueInvoiceItemId != null">
        BLUE_INVOICE_ITEM_ID,
      </if>
      <if test="thisTimeSurplusNumber != null">
        THIS_TIME_SURPLUS_NUMBER,
      </if>
      <if test="thisTimeSurplusAmount != null">
        THIS_TIME_SURPLUS_AMOUNT,
      </if>
      <if test="thisTimeSurplusUaxAmount != null">
        THIS_TIME_SURPLUS_UAX_AMOUNT,
      </if>
      <if test="thisTimeSurplusPricePlusTaxes != null">
        THIS_TIME_SURPLUS_PRICE_PLUS_TAXES,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceRedConfirmationId != null">
        #{invoiceRedConfirmationId,jdbcType=INTEGER},
      </if>
      <if test="uuid != null">
        #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="invoiceRedConfirmationNo != null">
        #{invoiceRedConfirmationNo,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null">
        #{serialNumber,jdbcType=INTEGER},
      </if>
      <if test="projectName != null">
        #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="projectSimpleName != null">
        #{projectSimpleName,jdbcType=VARCHAR},
      </if>
      <if test="projectFullName != null">
        #{projectFullName,jdbcType=VARCHAR},
      </if>
      <if test="taxCategoryNo != null">
        #{taxCategoryNo,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="taxRate != null">
        #{taxRate,jdbcType=DECIMAL},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=DECIMAL},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="unitPrice != null">
        #{unitPrice,jdbcType=DECIMAL},
      </if>
      <if test="uaxAmount != null">
        #{uaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="pricePlusTaxes != null">
        #{pricePlusTaxes,jdbcType=DECIMAL},
      </if>
      <if test="businessOrderItemId != null">
        #{businessOrderItemId,jdbcType=INTEGER},
      </if>
      <if test="afterSaleBusinessOrderItemId != null">
        #{afterSaleBusinessOrderItemId,jdbcType=INTEGER},
      </if>
      <if test="blueInvoiceItemId != null">
        #{blueInvoiceItemId,jdbcType=INTEGER},
      </if>
      <if test="thisTimeSurplusNumber != null">
        #{thisTimeSurplusNumber,jdbcType=DECIMAL},
      </if>
      <if test="thisTimeSurplusAmount != null">
        #{thisTimeSurplusAmount,jdbcType=DECIMAL},
      </if>
      <if test="thisTimeSurplusUaxAmount != null">
        #{thisTimeSurplusUaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="thisTimeSurplusPricePlusTaxes != null">
        #{thisTimeSurplusPricePlusTaxes,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceRedConfirmationItemEntity">
    <!--@mbg.generated-->
    update T_INVOICE_RED_CONFIRMATION_ITEM
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceRedConfirmationId != null">
        INVOICE_RED_CONFIRMATION_ID = #{invoiceRedConfirmationId,jdbcType=INTEGER},
      </if>
      <if test="uuid != null">
        UUID = #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="invoiceRedConfirmationNo != null">
        INVOICE_RED_CONFIRMATION_NO = #{invoiceRedConfirmationNo,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null">
        SERIAL_NUMBER = #{serialNumber,jdbcType=INTEGER},
      </if>
      <if test="projectName != null">
        PROJECT_NAME = #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="projectSimpleName != null">
        PROJECT_SIMPLE_NAME = #{projectSimpleName,jdbcType=VARCHAR},
      </if>
      <if test="projectFullName != null">
        PROJECT_FULL_NAME = #{projectFullName,jdbcType=VARCHAR},
      </if>
      <if test="taxCategoryNo != null">
        TAX_CATEGORY_NO = #{taxCategoryNo,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        SPECIFICATIONS = #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        UNIT = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="taxRate != null">
        TAX_RATE = #{taxRate,jdbcType=DECIMAL},
      </if>
      <if test="quantity != null">
        QUANTITY = #{quantity,jdbcType=DECIMAL},
      </if>
      <if test="amount != null">
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="unitPrice != null">
        UNIT_PRICE = #{unitPrice,jdbcType=DECIMAL},
      </if>
      <if test="uaxAmount != null">
        UAX_AMOUNT = #{uaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="pricePlusTaxes != null">
        PRICE_PLUS_TAXES = #{pricePlusTaxes,jdbcType=DECIMAL},
      </if>
      <if test="businessOrderItemId != null">
        BUSINESS_ORDER_ITEM_ID = #{businessOrderItemId,jdbcType=INTEGER},
      </if>
      <if test="afterSaleBusinessOrderItemId != null">
        AFTER_SALE_BUSINESS_ORDER_ITEM_ID = #{afterSaleBusinessOrderItemId,jdbcType=INTEGER},
      </if>
      <if test="blueInvoiceItemId != null">
        BLUE_INVOICE_ITEM_ID = #{blueInvoiceItemId,jdbcType=INTEGER},
      </if>
      <if test="thisTimeSurplusNumber != null">
        THIS_TIME_SURPLUS_NUMBER = #{thisTimeSurplusNumber,jdbcType=DECIMAL},
      </if>
      <if test="thisTimeSurplusAmount != null">
        THIS_TIME_SURPLUS_AMOUNT = #{thisTimeSurplusAmount,jdbcType=DECIMAL},
      </if>
      <if test="thisTimeSurplusUaxAmount != null">
        THIS_TIME_SURPLUS_UAX_AMOUNT = #{thisTimeSurplusUaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="thisTimeSurplusPricePlusTaxes != null">
        THIS_TIME_SURPLUS_PRICE_PLUS_TAXES = #{thisTimeSurplusPricePlusTaxes,jdbcType=DECIMAL},
      </if>
    </set>
    where INVOICE_RED_CONFIRMATION_ITEM_ID = #{invoiceRedConfirmationItemId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceRedConfirmationItemEntity">
    <!--@mbg.generated-->
    update T_INVOICE_RED_CONFIRMATION_ITEM
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      INVOICE_RED_CONFIRMATION_ID = #{invoiceRedConfirmationId,jdbcType=INTEGER},
      UUID = #{uuid,jdbcType=VARCHAR},
      INVOICE_RED_CONFIRMATION_NO = #{invoiceRedConfirmationNo,jdbcType=VARCHAR},
      SERIAL_NUMBER = #{serialNumber,jdbcType=INTEGER},
      PROJECT_NAME = #{projectName,jdbcType=VARCHAR},
      PROJECT_SIMPLE_NAME = #{projectSimpleName,jdbcType=VARCHAR},
      PROJECT_FULL_NAME = #{projectFullName,jdbcType=VARCHAR},
      TAX_CATEGORY_NO = #{taxCategoryNo,jdbcType=VARCHAR},
      SPECIFICATIONS = #{specifications,jdbcType=VARCHAR},
      UNIT = #{unit,jdbcType=VARCHAR},
      TAX_RATE = #{taxRate,jdbcType=DECIMAL},
      QUANTITY = #{quantity,jdbcType=DECIMAL},
      AMOUNT = #{amount,jdbcType=DECIMAL},
      UNIT_PRICE = #{unitPrice,jdbcType=DECIMAL},
      UAX_AMOUNT = #{uaxAmount,jdbcType=DECIMAL},
      PRICE_PLUS_TAXES = #{pricePlusTaxes,jdbcType=DECIMAL},
      BUSINESS_ORDER_ITEM_ID = #{businessOrderItemId,jdbcType=INTEGER},
      AFTER_SALE_BUSINESS_ORDER_ITEM_ID = #{afterSaleBusinessOrderItemId,jdbcType=INTEGER},
      BLUE_INVOICE_ITEM_ID = #{blueInvoiceItemId,jdbcType=INTEGER},
      THIS_TIME_SURPLUS_NUMBER = #{thisTimeSurplusNumber,jdbcType=DECIMAL},
      THIS_TIME_SURPLUS_AMOUNT = #{thisTimeSurplusAmount,jdbcType=DECIMAL},
      THIS_TIME_SURPLUS_UAX_AMOUNT = #{thisTimeSurplusUaxAmount,jdbcType=DECIMAL},
      THIS_TIME_SURPLUS_PRICE_PLUS_TAXES = #{thisTimeSurplusPricePlusTaxes,jdbcType=DECIMAL}
    where INVOICE_RED_CONFIRMATION_ITEM_ID = #{invoiceRedConfirmationItemId,jdbcType=INTEGER}
  </update>

  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_INVOICE_RED_CONFIRMATION_ITEM
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when INVOICE_RED_CONFIRMATION_ITEM_ID = #{item.invoiceRedConfirmationItemId,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when INVOICE_RED_CONFIRMATION_ITEM_ID = #{item.invoiceRedConfirmationItemId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when INVOICE_RED_CONFIRMATION_ITEM_ID = #{item.invoiceRedConfirmationItemId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when INVOICE_RED_CONFIRMATION_ITEM_ID = #{item.invoiceRedConfirmationItemId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when INVOICE_RED_CONFIRMATION_ITEM_ID = #{item.invoiceRedConfirmationItemId,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when INVOICE_RED_CONFIRMATION_ITEM_ID = #{item.invoiceRedConfirmationItemId,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_RED_CONFIRMATION_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceRedConfirmationId != null">
            when INVOICE_RED_CONFIRMATION_ITEM_ID = #{item.invoiceRedConfirmationItemId,jdbcType=INTEGER} then #{item.invoiceRedConfirmationId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UUID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.uuid != null">
            when INVOICE_RED_CONFIRMATION_ITEM_ID = #{item.invoiceRedConfirmationItemId,jdbcType=INTEGER} then #{item.uuid,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_RED_CONFIRMATION_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceRedConfirmationNo != null">
            when INVOICE_RED_CONFIRMATION_ITEM_ID = #{item.invoiceRedConfirmationItemId,jdbcType=INTEGER} then #{item.invoiceRedConfirmationNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SERIAL_NUMBER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.serialNumber != null">
            when INVOICE_RED_CONFIRMATION_ITEM_ID = #{item.invoiceRedConfirmationItemId,jdbcType=INTEGER} then #{item.serialNumber,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="PROJECT_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.projectName != null">
            when INVOICE_RED_CONFIRMATION_ITEM_ID = #{item.invoiceRedConfirmationItemId,jdbcType=INTEGER} then #{item.projectName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="PROJECT_SIMPLE_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.projectSimpleName != null">
            when INVOICE_RED_CONFIRMATION_ITEM_ID = #{item.invoiceRedConfirmationItemId,jdbcType=INTEGER} then #{item.projectSimpleName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="PROJECT_FULL_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.projectFullName != null">
            when INVOICE_RED_CONFIRMATION_ITEM_ID = #{item.invoiceRedConfirmationItemId,jdbcType=INTEGER} then #{item.projectFullName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TAX_CATEGORY_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.taxCategoryNo != null">
            when INVOICE_RED_CONFIRMATION_ITEM_ID = #{item.invoiceRedConfirmationItemId,jdbcType=INTEGER} then #{item.taxCategoryNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SPECIFICATIONS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.specifications != null">
            when INVOICE_RED_CONFIRMATION_ITEM_ID = #{item.invoiceRedConfirmationItemId,jdbcType=INTEGER} then #{item.specifications,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UNIT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.unit != null">
            when INVOICE_RED_CONFIRMATION_ITEM_ID = #{item.invoiceRedConfirmationItemId,jdbcType=INTEGER} then #{item.unit,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TAX_RATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.taxRate != null">
            when INVOICE_RED_CONFIRMATION_ITEM_ID = #{item.invoiceRedConfirmationItemId,jdbcType=INTEGER} then #{item.taxRate,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="QUANTITY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.quantity != null">
            when INVOICE_RED_CONFIRMATION_ITEM_ID = #{item.invoiceRedConfirmationItemId,jdbcType=INTEGER} then #{item.quantity,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.amount != null">
            when INVOICE_RED_CONFIRMATION_ITEM_ID = #{item.invoiceRedConfirmationItemId,jdbcType=INTEGER} then #{item.amount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="UNIT_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.unitPrice != null">
            when INVOICE_RED_CONFIRMATION_ITEM_ID = #{item.invoiceRedConfirmationItemId,jdbcType=INTEGER} then #{item.unitPrice,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="UAX_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.uaxAmount != null">
            when INVOICE_RED_CONFIRMATION_ITEM_ID = #{item.invoiceRedConfirmationItemId,jdbcType=INTEGER} then #{item.uaxAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="PRICE_PLUS_TAXES = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.pricePlusTaxes != null">
            when INVOICE_RED_CONFIRMATION_ITEM_ID = #{item.invoiceRedConfirmationItemId,jdbcType=INTEGER} then #{item.pricePlusTaxes,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="BUSINESS_ORDER_ITEM_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.businessOrderItemId != null">
            when INVOICE_RED_CONFIRMATION_ITEM_ID = #{item.invoiceRedConfirmationItemId,jdbcType=INTEGER} then #{item.businessOrderItemId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="AFTER_SALE_BUSINESS_ORDER_ITEM_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.afterSaleBusinessOrderItemId != null">
            when INVOICE_RED_CONFIRMATION_ITEM_ID = #{item.invoiceRedConfirmationItemId,jdbcType=INTEGER} then #{item.afterSaleBusinessOrderItemId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="BLUE_INVOICE_ITEM_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.blueInvoiceItemId != null">
            when INVOICE_RED_CONFIRMATION_ITEM_ID = #{item.invoiceRedConfirmationItemId,jdbcType=INTEGER} then #{item.blueInvoiceItemId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="THIS_TIME_SURPLUS_NUMBER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.thisTimeSurplusNumber != null">
            when INVOICE_RED_CONFIRMATION_ITEM_ID = #{item.invoiceRedConfirmationItemId,jdbcType=INTEGER} then #{item.thisTimeSurplusNumber,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="THIS_TIME_SURPLUS_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.thisTimeSurplusAmount != null">
            when INVOICE_RED_CONFIRMATION_ITEM_ID = #{item.invoiceRedConfirmationItemId,jdbcType=INTEGER} then #{item.thisTimeSurplusAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="THIS_TIME_SURPLUS_UAX_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.thisTimeSurplusUaxAmount != null">
            when INVOICE_RED_CONFIRMATION_ITEM_ID = #{item.invoiceRedConfirmationItemId,jdbcType=INTEGER} then #{item.thisTimeSurplusUaxAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="THIS_TIME_SURPLUS_PRICE_PLUS_TAXES = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.thisTimeSurplusPricePlusTaxes != null">
            when INVOICE_RED_CONFIRMATION_ITEM_ID = #{item.invoiceRedConfirmationItemId,jdbcType=INTEGER} then #{item.thisTimeSurplusPricePlusTaxes,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
    </trim>
    where INVOICE_RED_CONFIRMATION_ITEM_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.invoiceRedConfirmationItemId,jdbcType=INTEGER}
    </foreach>
  </update>



  <insert id="insertOrUpdateSelective" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceRedConfirmationItemEntity">
    <!--@mbg.generated-->
    insert into T_INVOICE_RED_CONFIRMATION_ITEM
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="invoiceRedConfirmationItemId != null">
        INVOICE_RED_CONFIRMATION_ITEM_ID,
      </if>
      <if test="invoiceRedConfirmationId != null">
        INVOICE_RED_CONFIRMATION_ID,
      </if>
      <if test="uuid != null and uuid != ''">
        UUID,
      </if>
      <if test="invoiceRedConfirmationNo != null and invoiceRedConfirmationNo != ''">
        INVOICE_RED_CONFIRMATION_NO,
      </if>
      <if test="serialNumber != null">
        SERIAL_NUMBER,
      </if>
      <if test="projectName != null and projectName != ''">
        PROJECT_NAME,
      </if>
      <if test="projectSimpleName != null and projectSimpleName != ''">
        PROJECT_SIMPLE_NAME,
      </if>
      <if test="projectFullName != null and projectFullName != ''">
        PROJECT_FULL_NAME,
      </if>
      <if test="taxCategoryNo != null and taxCategoryNo != ''">
        TAX_CATEGORY_NO,
      </if>
      <if test="specifications != null and specifications != ''">
        SPECIFICATIONS,
      </if>
      <if test="unit != null and unit != ''">
        UNIT,
      </if>
      <if test="taxRate != null">
        TAX_RATE,
      </if>
      <if test="quantity != null">
        QUANTITY,
      </if>
      <if test="amount != null">
        AMOUNT,
      </if>
      <if test="unitPrice != null">
        UNIT_PRICE,
      </if>
      <if test="uaxAmount != null">
        UAX_AMOUNT,
      </if>
      <if test="pricePlusTaxes != null">
        PRICE_PLUS_TAXES,
      </if>
      <if test="businessOrderItemId != null">
        BUSINESS_ORDER_ITEM_ID,
      </if>
      <if test="afterSaleBusinessOrderItemId != null">
        AFTER_SALE_BUSINESS_ORDER_ITEM_ID,
      </if>
      <if test="blueInvoiceItemId != null">
        BLUE_INVOICE_ITEM_ID,
      </if>
      <if test="thisTimeSurplusNumber != null">
        THIS_TIME_SURPLUS_NUMBER,
      </if>
      <if test="thisTimeSurplusAmount != null">
        THIS_TIME_SURPLUS_AMOUNT,
      </if>
      <if test="thisTimeSurplusUaxAmount != null">
        THIS_TIME_SURPLUS_UAX_AMOUNT,
      </if>
      <if test="thisTimeSurplusPricePlusTaxes != null">
        THIS_TIME_SURPLUS_PRICE_PLUS_TAXES,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="invoiceRedConfirmationItemId != null">
        #{invoiceRedConfirmationItemId,jdbcType=INTEGER},
      </if>
      <if test="invoiceRedConfirmationId != null">
        #{invoiceRedConfirmationId,jdbcType=INTEGER},
      </if>
      <if test="uuid != null and uuid != ''">
        #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="invoiceRedConfirmationNo != null and invoiceRedConfirmationNo != ''">
        #{invoiceRedConfirmationNo,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null">
        #{serialNumber,jdbcType=INTEGER},
      </if>
      <if test="projectName != null and projectName != ''">
        #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="projectSimpleName != null and projectSimpleName != ''">
        #{projectSimpleName,jdbcType=VARCHAR},
      </if>
      <if test="projectFullName != null and projectFullName != ''">
        #{projectFullName,jdbcType=VARCHAR},
      </if>
      <if test="taxCategoryNo != null and taxCategoryNo != ''">
        #{taxCategoryNo,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null and specifications != ''">
        #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="unit != null and unit != ''">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="taxRate != null">
        #{taxRate,jdbcType=DECIMAL},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=DECIMAL},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="unitPrice != null">
        #{unitPrice,jdbcType=DECIMAL},
      </if>
      <if test="uaxAmount != null">
        #{uaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="pricePlusTaxes != null">
        #{pricePlusTaxes,jdbcType=DECIMAL},
      </if>
      <if test="businessOrderItemId != null">
        #{businessOrderItemId,jdbcType=INTEGER},
      </if>
      <if test="afterSaleBusinessOrderItemId != null">
        #{afterSaleBusinessOrderItemId,jdbcType=INTEGER},
      </if>
      <if test="blueInvoiceItemId != null">
        #{blueInvoiceItemId,jdbcType=INTEGER},
      </if>
      <if test="thisTimeSurplusNumber != null">
        #{thisTimeSurplusNumber,jdbcType=DECIMAL},
      </if>
      <if test="thisTimeSurplusAmount != null">
        #{thisTimeSurplusAmount,jdbcType=DECIMAL},
      </if>
      <if test="thisTimeSurplusUaxAmount != null">
        #{thisTimeSurplusUaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="thisTimeSurplusPricePlusTaxes != null">
        #{thisTimeSurplusPricePlusTaxes,jdbcType=DECIMAL},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="invoiceRedConfirmationItemId != null">
        INVOICE_RED_CONFIRMATION_ITEM_ID = #{invoiceRedConfirmationItemId,jdbcType=INTEGER},
      </if>
      <if test="invoiceRedConfirmationId != null">
        INVOICE_RED_CONFIRMATION_ID = #{invoiceRedConfirmationId,jdbcType=INTEGER},
      </if>
      <if test="uuid != null and uuid != ''">
        UUID = #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="invoiceRedConfirmationNo != null and invoiceRedConfirmationNo != ''">
        INVOICE_RED_CONFIRMATION_NO = #{invoiceRedConfirmationNo,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null">
        SERIAL_NUMBER = #{serialNumber,jdbcType=INTEGER},
      </if>
      <if test="projectName != null and projectName != ''">
        PROJECT_NAME = #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="projectSimpleName != null and projectSimpleName != ''">
        PROJECT_SIMPLE_NAME = #{projectSimpleName,jdbcType=VARCHAR},
      </if>
      <if test="projectFullName != null and projectFullName != ''">
        PROJECT_FULL_NAME = #{projectFullName,jdbcType=VARCHAR},
      </if>
      <if test="taxCategoryNo != null and taxCategoryNo != ''">
        TAX_CATEGORY_NO = #{taxCategoryNo,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null and specifications != ''">
        SPECIFICATIONS = #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="unit != null and unit != ''">
        UNIT = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="taxRate != null">
        TAX_RATE = #{taxRate,jdbcType=DECIMAL},
      </if>
      <if test="quantity != null">
        QUANTITY = #{quantity,jdbcType=DECIMAL},
      </if>
      <if test="amount != null">
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="unitPrice != null">
        UNIT_PRICE = #{unitPrice,jdbcType=DECIMAL},
      </if>
      <if test="uaxAmount != null">
        UAX_AMOUNT = #{uaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="pricePlusTaxes != null">
        PRICE_PLUS_TAXES = #{pricePlusTaxes,jdbcType=DECIMAL},
      </if>
      <if test="businessOrderItemId != null">
        BUSINESS_ORDER_ITEM_ID = #{businessOrderItemId,jdbcType=INTEGER},
      </if>
      <if test="afterSaleBusinessOrderItemId != null">
        AFTER_SALE_BUSINESS_ORDER_ITEM_ID = #{afterSaleBusinessOrderItemId,jdbcType=INTEGER},
      </if>
      <if test="blueInvoiceItemId != null">
        BLUE_INVOICE_ITEM_ID = #{blueInvoiceItemId,jdbcType=INTEGER},
      </if>
      <if test="thisTimeSurplusNumber != null">
        THIS_TIME_SURPLUS_NUMBER = #{thisTimeSurplusNumber,jdbcType=DECIMAL},
      </if>
      <if test="thisTimeSurplusAmount != null">
        THIS_TIME_SURPLUS_AMOUNT = #{thisTimeSurplusAmount,jdbcType=DECIMAL},
      </if>
      <if test="thisTimeSurplusUaxAmount != null">
        THIS_TIME_SURPLUS_UAX_AMOUNT = #{thisTimeSurplusUaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="thisTimeSurplusPricePlusTaxes != null">
        THIS_TIME_SURPLUS_PRICE_PLUS_TAXES = #{thisTimeSurplusPricePlusTaxes,jdbcType=DECIMAL},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <select id="getByInvoiceRedConfirmationId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_INVOICE_RED_CONFIRMATION_ITEM
    where INVOICE_RED_CONFIRMATION_ID = #{invoiceRedConfirmationId,jdbcType=INTEGER}
  </select>
</mapper>