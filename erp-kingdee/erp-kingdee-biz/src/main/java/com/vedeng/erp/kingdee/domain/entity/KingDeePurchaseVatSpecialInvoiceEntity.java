package com.vedeng.erp.kingdee.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import com.vedeng.common.mybatis.jbatis.annotation.GeneratedValue;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.common.mybatis.jbatis.annotation.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * 采购专票
 */
@Getter
@Setter
@Table(name = "KING_DEE_PURCHASE_VAT_SPECIAL_INVOICE")
public class KingDeePurchaseVatSpecialInvoiceEntity extends BaseEntity {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer purchaseVatSpecialInvoiceId;

    /**
     * fid
     */
    private String fid;

    /**
     * 业务日期 录票时间
     */
    private String fdate;

    /**
     * 贝登erp对应的单据头ID
     */
    private String fQzokBddjtid;

    /**
     * 发票号 发票123456789
     */
    private String finvoiceno;

    /**
     * 发票代码12345
     */
    private String fQzokFpdm;

    /**
     * 供应商
     */
    private String fsupplierid;

    /**
     * 单据状态
     */
    private String fdocumentstatus;

    /**
     * 单据类型 默认:CGZZSZYFP01_SYS
     */
    private String fBillTypeId;

    /**
     * 结算组织 101
     */
    private String fsettleorgid;

    private String fpurchaseorgid;

    /**
     * 作废状态 A （正常）
     */
    private String fCancelStatus;

    /**
     * fRedBlue红蓝字标识 0 蓝字  1 红字
     */
    private String fRedBlue;

    /**
     * 发票明细
     */
    private String fpurchaseicentry;
}