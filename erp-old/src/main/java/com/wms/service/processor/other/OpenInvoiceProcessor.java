package com.wms.service.processor.other;

import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.StringUtil;
import com.vedeng.finance.model.InvoiceApply;
import com.vedeng.finance.service.InvoiceService;
import com.wms.constant.WMSContant;
import com.wms.constant.WmsInvoiceFlagEnum;
import com.wms.dto.WmsInvoiceCallbackDto;
import com.wms.dto.WmsInvoiceDto;
import com.wms.service.util.WmsCommonUtil;
import com.wms.service.WMSInterfaceFactory;
import com.wms.service.WmsInterface;
import com.wms.service.processor.AbstractWMSCalllBackProcessor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * WMS触发ERP开票接口处理器
 *
 * <AUTHOR>
 * @date 2020/8/18 13:50:12
 */
@Service
public class OpenInvoiceProcessor extends AbstractWMSCalllBackProcessor<WmsInvoiceCallbackDto> {
    private static final Logger logger = LoggerFactory.getLogger(OpenInvoiceProcessor.class);


    @Override
    protected String getBusinessKey(WmsInvoiceCallbackDto requestBean) {
        return null;
    }

    @Override
    protected boolean needIdempotentValidator() {
        return false;
    }

    @Override
    protected void commonValidator(WmsInvoiceCallbackDto requestBean) throws Exception {
        if (StringUtil.isBlank(requestBean.getINVOICEID())) {
            logger.error("WMS触发ERP开票缺少开票申请ID invoiceApplyId:{}" + requestBean.getINVOICEID());
            throw new Exception("WMS触发ERP开票缺少开票申请ID invoiceApplyId:{}" + requestBean.getINVOICEID());
        }

    }

    @Override
    protected void doDealWithRequest(WmsInvoiceCallbackDto requestBean) throws Exception {
        logger.info("WMS触发ERP进行开票 requestBean:{}" , requestBean.toString());
    }
}
