<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wms.dao.generate.VWarehouseGoodsOperateLogMapper">
  <resultMap id="BaseResultMap" type="com.wms.model.ddi.generate.VWarehouseGoodsOperateLog">
    <id column="WAREHOUSE_GOODS_OPERATE_LOG_ID" jdbcType="INTEGER" property="warehouseGoodsOperateLogId" />
    <result column="BARCODE_ID" jdbcType="INTEGER" property="barcodeId" />
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
    <result column="OPERATE_TYPE" jdbcType="TINYINT" property="operateType" />
    <result column="RELATED_ID" jdbcType="INTEGER" property="relatedId" />
    <result column="WAREHOUSE_PICKING_DETAIL_ID" jdbcType="INTEGER" property="warehousePickingDetailId" />
    <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId" />
    <result column="BARCODE_FACTORY" jdbcType="VARCHAR" property="barcodeFactory" />
    <result column="NUM" jdbcType="INTEGER" property="num" />
    <result column="WAREHOUSE_ID" jdbcType="INTEGER" property="warehouseId" />
    <result column="STORAGE_ROOM_ID" jdbcType="INTEGER" property="storageRoomId" />
    <result column="STORAGE_AREA_ID" jdbcType="INTEGER" property="storageAreaId" />
    <result column="STORAGE_LOCATION_ID" jdbcType="INTEGER" property="storageLocationId" />
    <result column="STORAGE_RACK_ID" jdbcType="INTEGER" property="storageRackId" />
    <result column="BATCH_NUMBER" jdbcType="VARCHAR" property="batchNumber" />
    <result column="EXPIRATION_DATE" jdbcType="BIGINT" property="expirationDate" />
    <result column="CHECK_STATUS" jdbcType="BOOLEAN" property="checkStatus" />
    <result column="CHECK_STATUS_USER" jdbcType="INTEGER" property="checkStatusUser" />
    <result column="CHECK_STATUS_TIME" jdbcType="BIGINT" property="checkStatusTime" />
    <result column="RECHECK_STATUS" jdbcType="BOOLEAN" property="recheckStatus" />
    <result column="RECHECK_STATUS_USER" jdbcType="INTEGER" property="recheckStatusUser" />
    <result column="RECHECK_STATUS_TIME" jdbcType="BIGINT" property="recheckStatusTime" />
    <result column="COMMENTS" jdbcType="VARCHAR" property="comments" />
    <result column="IS_ENABLE" jdbcType="BOOLEAN" property="isEnable" />
    <result column="IS_EXPRESS" jdbcType="BOOLEAN" property="isExpress" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="IS_PROBLEM" jdbcType="BOOLEAN" property="isProblem" />
    <result column="PROBLEM_REMARK" jdbcType="VARCHAR" property="problemRemark" />
    <result column="PRODUCT_DATE" jdbcType="BIGINT" property="productDate" />
    <result column="COST_PRICE" jdbcType="DECIMAL" property="costPrice" />
    <result column="IS_USE" jdbcType="BOOLEAN" property="isUse" />
    <result column="LOGICAL_WAREHOUSE_ID" jdbcType="INTEGER" property="logicalWarehouseId" />
    <result column="VEDENG_BATCH_NUMER" jdbcType="VARCHAR" property="vedengBatchNumer" />
    <result column="LAST_STOCK_NUM" jdbcType="INTEGER" property="lastStockNum" />
    <result column="STERILZATION_BATCH_NUMBER" jdbcType="VARCHAR" property="sterilzationBatchNumber" />
    <result column="LOG_TYPE" jdbcType="BOOLEAN" property="logType" />
  </resultMap>
  <sql id="Base_Column_List">
    WAREHOUSE_GOODS_OPERATE_LOG_ID, BARCODE_ID, COMPANY_ID, OPERATE_TYPE, RELATED_ID, 
    WAREHOUSE_PICKING_DETAIL_ID, GOODS_ID, BARCODE_FACTORY, NUM, WAREHOUSE_ID, STORAGE_ROOM_ID, 
    STORAGE_AREA_ID, STORAGE_LOCATION_ID, STORAGE_RACK_ID, BATCH_NUMBER, EXPIRATION_DATE, 
    CHECK_STATUS, CHECK_STATUS_USER, CHECK_STATUS_TIME, RECHECK_STATUS, RECHECK_STATUS_USER, 
    RECHECK_STATUS_TIME, COMMENTS, IS_ENABLE, IS_EXPRESS, ADD_TIME, CREATOR, MOD_TIME, 
    UPDATER, IS_PROBLEM, PROBLEM_REMARK, PRODUCT_DATE, COST_PRICE, IS_USE, LOGICAL_WAREHOUSE_ID, 
    VEDENG_BATCH_NUMER, LAST_STOCK_NUM, STERILZATION_BATCH_NUMBER, LOG_TYPE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_WAREHOUSE_GOODS_OPERATE_LOG
    where WAREHOUSE_GOODS_OPERATE_LOG_ID = #{warehouseGoodsOperateLogId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_WAREHOUSE_GOODS_OPERATE_LOG
    where WAREHOUSE_GOODS_OPERATE_LOG_ID = #{warehouseGoodsOperateLogId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="WAREHOUSE_GOODS_OPERATE_LOG_ID" keyProperty="warehouseGoodsOperateLogId" parameterType="com.wms.model.ddi.generate.VWarehouseGoodsOperateLog" useGeneratedKeys="true">
    insert into T_WAREHOUSE_GOODS_OPERATE_LOG (BARCODE_ID, COMPANY_ID, OPERATE_TYPE, 
      RELATED_ID, WAREHOUSE_PICKING_DETAIL_ID, GOODS_ID, 
      BARCODE_FACTORY, NUM, WAREHOUSE_ID, 
      STORAGE_ROOM_ID, STORAGE_AREA_ID, STORAGE_LOCATION_ID, 
      STORAGE_RACK_ID, BATCH_NUMBER, EXPIRATION_DATE, 
      CHECK_STATUS, CHECK_STATUS_USER, CHECK_STATUS_TIME, 
      RECHECK_STATUS, RECHECK_STATUS_USER, RECHECK_STATUS_TIME, 
      COMMENTS, IS_ENABLE, IS_EXPRESS, 
      ADD_TIME, CREATOR, MOD_TIME, 
      UPDATER, IS_PROBLEM, PROBLEM_REMARK, 
      PRODUCT_DATE, COST_PRICE, IS_USE, 
      LOGICAL_WAREHOUSE_ID, VEDENG_BATCH_NUMER, LAST_STOCK_NUM, 
      STERILZATION_BATCH_NUMBER, LOG_TYPE)
    values (#{barcodeId,jdbcType=INTEGER}, #{companyId,jdbcType=INTEGER}, #{operateType,jdbcType=TINYINT}, 
      #{relatedId,jdbcType=INTEGER}, #{warehousePickingDetailId,jdbcType=INTEGER}, #{goodsId,jdbcType=INTEGER}, 
      #{barcodeFactory,jdbcType=VARCHAR}, #{num,jdbcType=INTEGER}, #{warehouseId,jdbcType=INTEGER}, 
      #{storageRoomId,jdbcType=INTEGER}, #{storageAreaId,jdbcType=INTEGER}, #{storageLocationId,jdbcType=INTEGER}, 
      #{storageRackId,jdbcType=INTEGER}, #{batchNumber,jdbcType=VARCHAR}, #{expirationDate,jdbcType=BIGINT}, 
      #{checkStatus,jdbcType=BOOLEAN}, #{checkStatusUser,jdbcType=INTEGER}, #{checkStatusTime,jdbcType=BIGINT}, 
      #{recheckStatus,jdbcType=BOOLEAN}, #{recheckStatusUser,jdbcType=INTEGER}, #{recheckStatusTime,jdbcType=BIGINT}, 
      #{comments,jdbcType=VARCHAR}, #{isEnable,jdbcType=BOOLEAN}, #{isExpress,jdbcType=BOOLEAN}, 
      #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, 
      #{updater,jdbcType=INTEGER}, #{isProblem,jdbcType=BOOLEAN}, #{problemRemark,jdbcType=VARCHAR}, 
      #{productDate,jdbcType=BIGINT}, #{costPrice,jdbcType=DECIMAL}, #{isUse,jdbcType=BOOLEAN}, 
      #{logicalWarehouseId,jdbcType=INTEGER}, #{vedengBatchNumer,jdbcType=VARCHAR}, #{lastStockNum,jdbcType=INTEGER}, 
      #{sterilzationBatchNumber,jdbcType=VARCHAR}, #{logType,jdbcType=BOOLEAN})
  </insert>
  <insert id="insertSelective" keyColumn="WAREHOUSE_GOODS_OPERATE_LOG_ID" keyProperty="warehouseGoodsOperateLogId" parameterType="com.wms.model.ddi.generate.VWarehouseGoodsOperateLog" useGeneratedKeys="true">
    insert into T_WAREHOUSE_GOODS_OPERATE_LOG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="barcodeId != null">
        BARCODE_ID,
      </if>
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="operateType != null">
        OPERATE_TYPE,
      </if>
      <if test="relatedId != null">
        RELATED_ID,
      </if>
      <if test="warehousePickingDetailId != null">
        WAREHOUSE_PICKING_DETAIL_ID,
      </if>
      <if test="goodsId != null">
        GOODS_ID,
      </if>
      <if test="barcodeFactory != null">
        BARCODE_FACTORY,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="warehouseId != null">
        WAREHOUSE_ID,
      </if>
      <if test="storageRoomId != null">
        STORAGE_ROOM_ID,
      </if>
      <if test="storageAreaId != null">
        STORAGE_AREA_ID,
      </if>
      <if test="storageLocationId != null">
        STORAGE_LOCATION_ID,
      </if>
      <if test="storageRackId != null">
        STORAGE_RACK_ID,
      </if>
      <if test="batchNumber != null">
        BATCH_NUMBER,
      </if>
      <if test="expirationDate != null">
        EXPIRATION_DATE,
      </if>
      <if test="checkStatus != null">
        CHECK_STATUS,
      </if>
      <if test="checkStatusUser != null">
        CHECK_STATUS_USER,
      </if>
      <if test="checkStatusTime != null">
        CHECK_STATUS_TIME,
      </if>
      <if test="recheckStatus != null">
        RECHECK_STATUS,
      </if>
      <if test="recheckStatusUser != null">
        RECHECK_STATUS_USER,
      </if>
      <if test="recheckStatusTime != null">
        RECHECK_STATUS_TIME,
      </if>
      <if test="comments != null">
        COMMENTS,
      </if>
      <if test="isEnable != null">
        IS_ENABLE,
      </if>
      <if test="isExpress != null">
        IS_EXPRESS,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="isProblem != null">
        IS_PROBLEM,
      </if>
      <if test="problemRemark != null">
        PROBLEM_REMARK,
      </if>
      <if test="productDate != null">
        PRODUCT_DATE,
      </if>
      <if test="costPrice != null">
        COST_PRICE,
      </if>
      <if test="isUse != null">
        IS_USE,
      </if>
      <if test="logicalWarehouseId != null">
        LOGICAL_WAREHOUSE_ID,
      </if>
      <if test="vedengBatchNumer != null">
        VEDENG_BATCH_NUMER,
      </if>
      <if test="lastStockNum != null">
        LAST_STOCK_NUM,
      </if>
      <if test="sterilzationBatchNumber != null">
        STERILZATION_BATCH_NUMBER,
      </if>
      <if test="logType != null">
        LOG_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="barcodeId != null">
        #{barcodeId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=TINYINT},
      </if>
      <if test="relatedId != null">
        #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="warehousePickingDetailId != null">
        #{warehousePickingDetailId,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null">
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="barcodeFactory != null">
        #{barcodeFactory,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="warehouseId != null">
        #{warehouseId,jdbcType=INTEGER},
      </if>
      <if test="storageRoomId != null">
        #{storageRoomId,jdbcType=INTEGER},
      </if>
      <if test="storageAreaId != null">
        #{storageAreaId,jdbcType=INTEGER},
      </if>
      <if test="storageLocationId != null">
        #{storageLocationId,jdbcType=INTEGER},
      </if>
      <if test="storageRackId != null">
        #{storageRackId,jdbcType=INTEGER},
      </if>
      <if test="batchNumber != null">
        #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="expirationDate != null">
        #{expirationDate,jdbcType=BIGINT},
      </if>
      <if test="checkStatus != null">
        #{checkStatus,jdbcType=BOOLEAN},
      </if>
      <if test="checkStatusUser != null">
        #{checkStatusUser,jdbcType=INTEGER},
      </if>
      <if test="checkStatusTime != null">
        #{checkStatusTime,jdbcType=BIGINT},
      </if>
      <if test="recheckStatus != null">
        #{recheckStatus,jdbcType=BOOLEAN},
      </if>
      <if test="recheckStatusUser != null">
        #{recheckStatusUser,jdbcType=INTEGER},
      </if>
      <if test="recheckStatusTime != null">
        #{recheckStatusTime,jdbcType=BIGINT},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null">
        #{isEnable,jdbcType=BOOLEAN},
      </if>
      <if test="isExpress != null">
        #{isExpress,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="isProblem != null">
        #{isProblem,jdbcType=BOOLEAN},
      </if>
      <if test="problemRemark != null">
        #{problemRemark,jdbcType=VARCHAR},
      </if>
      <if test="productDate != null">
        #{productDate,jdbcType=BIGINT},
      </if>
      <if test="costPrice != null">
        #{costPrice,jdbcType=DECIMAL},
      </if>
      <if test="isUse != null">
        #{isUse,jdbcType=BOOLEAN},
      </if>
      <if test="logicalWarehouseId != null">
        #{logicalWarehouseId,jdbcType=INTEGER},
      </if>
      <if test="vedengBatchNumer != null">
        #{vedengBatchNumer,jdbcType=VARCHAR},
      </if>
      <if test="lastStockNum != null">
        #{lastStockNum,jdbcType=INTEGER},
      </if>
      <if test="sterilzationBatchNumber != null">
        #{sterilzationBatchNumber,jdbcType=VARCHAR},
      </if>
      <if test="logType != null">
        #{logType,jdbcType=BOOLEAN},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wms.model.ddi.generate.VWarehouseGoodsOperateLog">
    update T_WAREHOUSE_GOODS_OPERATE_LOG
    <set>
      <if test="barcodeId != null">
        BARCODE_ID = #{barcodeId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="operateType != null">
        OPERATE_TYPE = #{operateType,jdbcType=TINYINT},
      </if>
      <if test="relatedId != null">
        RELATED_ID = #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="warehousePickingDetailId != null">
        WAREHOUSE_PICKING_DETAIL_ID = #{warehousePickingDetailId,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null">
        GOODS_ID = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="barcodeFactory != null">
        BARCODE_FACTORY = #{barcodeFactory,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="warehouseId != null">
        WAREHOUSE_ID = #{warehouseId,jdbcType=INTEGER},
      </if>
      <if test="storageRoomId != null">
        STORAGE_ROOM_ID = #{storageRoomId,jdbcType=INTEGER},
      </if>
      <if test="storageAreaId != null">
        STORAGE_AREA_ID = #{storageAreaId,jdbcType=INTEGER},
      </if>
      <if test="storageLocationId != null">
        STORAGE_LOCATION_ID = #{storageLocationId,jdbcType=INTEGER},
      </if>
      <if test="storageRackId != null">
        STORAGE_RACK_ID = #{storageRackId,jdbcType=INTEGER},
      </if>
      <if test="batchNumber != null">
        BATCH_NUMBER = #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="expirationDate != null">
        EXPIRATION_DATE = #{expirationDate,jdbcType=BIGINT},
      </if>
      <if test="checkStatus != null">
        CHECK_STATUS = #{checkStatus,jdbcType=BOOLEAN},
      </if>
      <if test="checkStatusUser != null">
        CHECK_STATUS_USER = #{checkStatusUser,jdbcType=INTEGER},
      </if>
      <if test="checkStatusTime != null">
        CHECK_STATUS_TIME = #{checkStatusTime,jdbcType=BIGINT},
      </if>
      <if test="recheckStatus != null">
        RECHECK_STATUS = #{recheckStatus,jdbcType=BOOLEAN},
      </if>
      <if test="recheckStatusUser != null">
        RECHECK_STATUS_USER = #{recheckStatusUser,jdbcType=INTEGER},
      </if>
      <if test="recheckStatusTime != null">
        RECHECK_STATUS_TIME = #{recheckStatusTime,jdbcType=BIGINT},
      </if>
      <if test="comments != null">
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null">
        IS_ENABLE = #{isEnable,jdbcType=BOOLEAN},
      </if>
      <if test="isExpress != null">
        IS_EXPRESS = #{isExpress,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="isProblem != null">
        IS_PROBLEM = #{isProblem,jdbcType=BOOLEAN},
      </if>
      <if test="problemRemark != null">
        PROBLEM_REMARK = #{problemRemark,jdbcType=VARCHAR},
      </if>
      <if test="productDate != null">
        PRODUCT_DATE = #{productDate,jdbcType=BIGINT},
      </if>
      <if test="costPrice != null">
        COST_PRICE = #{costPrice,jdbcType=DECIMAL},
      </if>
      <if test="isUse != null">
        IS_USE = #{isUse,jdbcType=BOOLEAN},
      </if>
      <if test="logicalWarehouseId != null">
        LOGICAL_WAREHOUSE_ID = #{logicalWarehouseId,jdbcType=INTEGER},
      </if>
      <if test="vedengBatchNumer != null">
        VEDENG_BATCH_NUMER = #{vedengBatchNumer,jdbcType=VARCHAR},
      </if>
      <if test="lastStockNum != null">
        LAST_STOCK_NUM = #{lastStockNum,jdbcType=INTEGER},
      </if>
      <if test="sterilzationBatchNumber != null">
        STERILZATION_BATCH_NUMBER = #{sterilzationBatchNumber,jdbcType=VARCHAR},
      </if>
      <if test="logType != null">
        LOG_TYPE = #{logType,jdbcType=BOOLEAN},
      </if>
    </set>
    where WAREHOUSE_GOODS_OPERATE_LOG_ID = #{warehouseGoodsOperateLogId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wms.model.ddi.generate.VWarehouseGoodsOperateLog">
    update T_WAREHOUSE_GOODS_OPERATE_LOG
    set BARCODE_ID = #{barcodeId,jdbcType=INTEGER},
      COMPANY_ID = #{companyId,jdbcType=INTEGER},
      OPERATE_TYPE = #{operateType,jdbcType=TINYINT},
      RELATED_ID = #{relatedId,jdbcType=INTEGER},
      WAREHOUSE_PICKING_DETAIL_ID = #{warehousePickingDetailId,jdbcType=INTEGER},
      GOODS_ID = #{goodsId,jdbcType=INTEGER},
      BARCODE_FACTORY = #{barcodeFactory,jdbcType=VARCHAR},
      NUM = #{num,jdbcType=INTEGER},
      WAREHOUSE_ID = #{warehouseId,jdbcType=INTEGER},
      STORAGE_ROOM_ID = #{storageRoomId,jdbcType=INTEGER},
      STORAGE_AREA_ID = #{storageAreaId,jdbcType=INTEGER},
      STORAGE_LOCATION_ID = #{storageLocationId,jdbcType=INTEGER},
      STORAGE_RACK_ID = #{storageRackId,jdbcType=INTEGER},
      BATCH_NUMBER = #{batchNumber,jdbcType=VARCHAR},
      EXPIRATION_DATE = #{expirationDate,jdbcType=BIGINT},
      CHECK_STATUS = #{checkStatus,jdbcType=BOOLEAN},
      CHECK_STATUS_USER = #{checkStatusUser,jdbcType=INTEGER},
      CHECK_STATUS_TIME = #{checkStatusTime,jdbcType=BIGINT},
      RECHECK_STATUS = #{recheckStatus,jdbcType=BOOLEAN},
      RECHECK_STATUS_USER = #{recheckStatusUser,jdbcType=INTEGER},
      RECHECK_STATUS_TIME = #{recheckStatusTime,jdbcType=BIGINT},
      COMMENTS = #{comments,jdbcType=VARCHAR},
      IS_ENABLE = #{isEnable,jdbcType=BOOLEAN},
      IS_EXPRESS = #{isExpress,jdbcType=BOOLEAN},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      IS_PROBLEM = #{isProblem,jdbcType=BOOLEAN},
      PROBLEM_REMARK = #{problemRemark,jdbcType=VARCHAR},
      PRODUCT_DATE = #{productDate,jdbcType=BIGINT},
      COST_PRICE = #{costPrice,jdbcType=DECIMAL},
      IS_USE = #{isUse,jdbcType=BOOLEAN},
      LOGICAL_WAREHOUSE_ID = #{logicalWarehouseId,jdbcType=INTEGER},
      VEDENG_BATCH_NUMER = #{vedengBatchNumer,jdbcType=VARCHAR},
      LAST_STOCK_NUM = #{lastStockNum,jdbcType=INTEGER},
      STERILZATION_BATCH_NUMBER = #{sterilzationBatchNumber,jdbcType=VARCHAR},
      LOG_TYPE = #{logType,jdbcType=BOOLEAN}
    where WAREHOUSE_GOODS_OPERATE_LOG_ID = #{warehouseGoodsOperateLogId,jdbcType=INTEGER}
  </update>
</mapper>