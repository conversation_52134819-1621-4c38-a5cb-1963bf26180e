package com.vedeng.temporal.task;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vedeng.common.core.utils.ErpSpringBeanUtil;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.entity.FlowOrderEntity;
import com.vedeng.temporal.mapper.TemporalFlowOrderMapper;
import com.vedeng.temporal.service.FlowNodeBasedCompanyService;
import com.vedeng.temporal.service.WorkflowOrchestrationService;
import com.vedeng.temporal.util.SystemApiClient;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 多公司业务流程定时任务
 * 通过XXL-Job触发多公司业务流程自动化
 *
 * 支持两种执行模式：
 * 1. 批量模式：参数为空、"batch"、"auto" 时，从数据库读取有效流程配置批量启动
 * 2. 单个模式：传入流转单编号（如：FO202501010001），启动指定流程的工作流
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-06-20
 */
@Component
@JobHandler(value = "multiCompanyBusinessJob")
@Slf4j
public class MultiCompanyBusinessJob extends AbstractJobHandler {

    @Autowired
    private WorkflowOrchestrationService workflowOrchestrationService;

    @Autowired
    private TemporalFlowOrderMapper temporalFlowOrderMapper;

    @Autowired
    private FlowNodeBasedCompanyService flowNodeBasedCompanyService;

    @Autowired
    private SystemApiClient systemApiClient;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("==================多公司业务流程自动化任务开始==================");
        log.info("多公司业务流程自动化任务开始，参数: {}", param);

        try {
            // 判断执行模式
            if (isBatchMode(param)) {
                XxlJobLogger.log("启用批量模式，从数据库读取有效流程配置");
                log.info("启用批量模式，从数据库读取有效流程配置");
                return executeBatchMode();
            } else {
                XxlJobLogger.log("启用单个模式，处理传入的业务参数");
                log.info("启用单个模式，处理传入的业务参数");
                return executeSingleMode(param);
            }

        } catch (Exception e) {
            XxlJobLogger.log("多公司业务流程自动化任务执行异常: {}", e.getMessage());
            log.error("多公司业务流程自动化任务执行异常", e);
            // 只有在系统级错误时才返回失败
            return ReturnT.FAIL;
        } finally {
            XxlJobLogger.log("==================多公司业务流程自动化任务结束==================");
            log.info("多公司业务流程自动化任务结束");
        }
    }

    /**
     * 批量模式执行
     * 从数据库读取有效的流程配置，批量启动工作流
     * 只要能成功发起工作流，就返回SUCCESS给xxl-job
     */
    private ReturnT<String> executeBatchMode() {
        try {
            XxlJobLogger.log("开始查询有效的流程配置数据");
            log.info("开始查询有效的流程配置数据");

            // 查询有效的流程配置
            List<FlowOrderEntity> validFlowOrders = temporalFlowOrderMapper.selectValidFlowOrders();

            if (CollectionUtils.isEmpty(validFlowOrders)) {
                XxlJobLogger.log("未找到有效的流程配置，任务结束");
                log.info("未找到有效的流程配置，任务结束");
                return ReturnT.SUCCESS;
            }

            XxlJobLogger.log("找到 {} 个有效的流程配置，开始批量启动工作流", validFlowOrders.size());
            log.info("找到 {} 个有效的流程配置，开始批量启动工作流", validFlowOrders.size());

            int totalCount = validFlowOrders.size();
            int triggeredCount = 0;
            int skippedCount = 0;

            // 遍历每个流程配置，启动对应的工作流
            for (FlowOrderEntity flowOrder : validFlowOrders) {
                try {
                    // 获取公司执行顺序
                    List<String> companySequence = flowNodeBasedCompanyService.getCompanySequenceByBusinessType(
                            flowOrder.getFlowOrderId(), flowOrder.getBaseBusinessType());

                    if (CollectionUtils.isEmpty(companySequence)) {
                        log.warn("流程配置 {} 未找到有效的公司执行顺序，跳过", flowOrder.getFlowOrderNo());
                        skippedCount++;
                        continue;
                    }

                    // 检查采购订单生效状态
                    boolean isValid = checkBuyOrderValidStatus(flowOrder, companySequence);
                    if (!isValid) {
                        log.warn("采购订单未生效，跳过工作流发起: {}", flowOrder.getFlowOrderNo());
                        skippedCount++;
                        continue;
                    }

                    // 构建工作流请求对象
                    CompanyBusinessRequest request = buildCompanyBusinessRequest(flowOrder, companySequence);

                    // 启动工作流（不等待执行结果）
                    workflowOrchestrationService.startMultiCompanyWorkflow(request);
                    triggeredCount++;
                    
                    log.info("流程配置 {} 工作流已发起", flowOrder.getFlowOrderNo());

                } catch (Exception e) {
                    log.error("处理流程配置 {} 时发生异常", flowOrder.getFlowOrderNo(), e);
                }
            }

            // 只要成功发起了工作流，就返回成功
            String resultMessage = String.format("批量触发完成 - 总数: %d, 已发起: %d, 跳过: %d", totalCount, triggeredCount, skippedCount);
            XxlJobLogger.log(resultMessage);
            log.info(resultMessage);

            return ReturnT.SUCCESS;

        } catch (Exception e) {
            XxlJobLogger.log("批量模式执行异常: {}", e.getMessage());
            log.error("批量模式执行异常", e);
            // 只有在无法连接数据库等系统级错误时才返回失败
            return ReturnT.FAIL;
        }
    }

    /**
     * 单个模式执行
     * 通过流转单编号启动工作流
     * 只要能成功发起工作流，就返回SUCCESS给xxl-job
     */
    private ReturnT<String> executeSingleMode(String param) {
        try {
            // 参数校验
            if (!StringUtils.hasText(param)) {
                XxlJobLogger.log("流转单编号参数为空");
                return ReturnT.FAIL;
            }

            String flowOrderNo = param.trim();
            XxlJobLogger.log("开始处理单个流程，流转单编号: {}", flowOrderNo);
            log.info("开始处理单个流程，流转单编号: {}", flowOrderNo);

            // 根据流转单编号查询流程配置
            FlowOrderEntity flowOrder = temporalFlowOrderMapper.selectByFlowOrderNo(flowOrderNo);
            if (flowOrder == null) {
                XxlJobLogger.log("未找到流转单配置，流转单编号: {}", flowOrderNo);
                log.warn("未找到流转单配置，流转单编号: {}", flowOrderNo);
                return new ReturnT<>(ReturnT.FAIL_CODE, "未找到流转单配置: " + flowOrderNo);
            }

            // 获取公司执行顺序
            List<String> companySequence = flowNodeBasedCompanyService.getCompanySequenceByBusinessType(
                    flowOrder.getFlowOrderId(), flowOrder.getBaseBusinessType());

            if (CollectionUtils.isEmpty(companySequence)) {
                XxlJobLogger.log("流程配置 {} 未找到有效的公司执行顺序", flowOrderNo);
                log.warn("流程配置 {} 未找到有效的公司执行顺序", flowOrderNo);
                return new ReturnT<>(ReturnT.FAIL_CODE, "未找到有效的公司执行顺序");
            }

            // 构建工作流请求对象
            CompanyBusinessRequest request = buildCompanyBusinessRequest(flowOrder, companySequence);

            // 启动多公司业务工作流（不等待执行结果）
            workflowOrchestrationService.startMultiCompanyWorkflow(request);
            
            XxlJobLogger.log("流程配置 {} 工作流已发起", flowOrderNo);
            log.info("流程配置 {} 工作流已发起", flowOrderNo);

            return ReturnT.SUCCESS;

        } catch (Exception e) {
            XxlJobLogger.log("单个模式执行异常: {}", e.getMessage());
            log.error("单个模式执行异常", e);
            // 只有在系统级错误时才返回失败
            return ReturnT.FAIL;
        }
    }



    /**
     * 判断是否为批量模式
     *
     * @param param 任务参数
     * @return 是否为批量模式
     */
    private boolean isBatchMode(String param) {
        // 参数为空、"batch"、"auto" 时启用批量模式
        // 其他情况视为流转单编号，启用单个模式
        return !StringUtils.hasText(param) ||
               "batch".equalsIgnoreCase(param.trim()) ||
               "auto".equalsIgnoreCase(param.trim());
    }

    /**
     * 构建工作流请求对象
     *
     * @param flowOrder 流程配置
     * @param companySequence 公司执行顺序
     * @return 工作流请求对象
     */
    private CompanyBusinessRequest buildCompanyBusinessRequest(FlowOrderEntity flowOrder, List<String> companySequence) {
        CompanyBusinessRequest request = new CompanyBusinessRequest();

        // 基本信息
        request.setBusinessId(flowOrder.getFlowOrderId().toString());
        request.setBusinessType(convertBusinessType(null));
        request.setCreateTimestamp(System.currentTimeMillis());

        // 公司信息（取序列中的前两个公司）
        if (companySequence.size() >= 1) {
            request.setSourceCompanyCode(companySequence.get(0));
        }
        if (companySequence.size() >= 2) {
            request.setTargetCompanyCode(companySequence.get(0));
        }

        // 业务数据（JSON格式）
        Map<String, Object> businessData = new HashMap<>();
        businessData.put("flowOrderId", flowOrder.getFlowOrderId());
        businessData.put("flowOrderNo", flowOrder.getFlowOrderNo());
        businessData.put("baseOrderId", flowOrder.getBaseOrderId());
        businessData.put("baseOrderNo", flowOrder.getBaseOrderNo());
        businessData.put("baseBusinessType", flowOrder.getBaseBusinessType());

        request.setBusinessData(JSON.toJSONString(businessData));
        // 扩展属性
        Map<String, Object> extendedProperties = new HashMap<>();
        request.setExtendedProperties(extendedProperties);
        return request;
    }

    /**
     * 检查采购订单生效状态
     *
     * @param flowOrder 流程配置
     * @param companySequence 公司执行顺序
     * @return 是否已生效（true=已生效，false=未生效）
     */
    private boolean checkBuyOrderValidStatus(FlowOrderEntity flowOrder, List<String> companySequence) {
        String flowOrderNo = flowOrder.getFlowOrderNo();
        String baseOrderNo = flowOrder.getBaseOrderNo();

        try {
            // 获取调用接口的公司代码（取公司序列中的第一个）
            if (CollectionUtils.isEmpty(companySequence)) {
                log.warn("公司执行顺序为空，无法检查生效状态: {}", flowOrderNo);
                return false;
            }

            String companyCode = companySequence.get(0);
            log.info("开始检查采购订单生效状态: {} - {} - {}", flowOrderNo, baseOrderNo, companyCode);

            // 构建查询请求参数
            Map<String, Object> queryRequest = new HashMap<>();
            queryRequest.put("buyorderNo", baseOrderNo);

            // 调用采购订单查询接口
            String apiResult = systemApiClient
                    .withCompany(companyCode)
                    .postToSystemApi("/api/v1/buyorder/query.do", queryRequest);

            // 解析API返回结果
            if (!StringUtils.hasText(apiResult)) {
                log.warn("采购订单查询接口返回结果为空: {} - {}", flowOrderNo, baseOrderNo);
                return false;
            }

            // 获取ObjectMapper进行JSON解析
            ObjectMapper objectMapper = ErpSpringBeanUtil.getBean(ObjectMapper.class);
            if (objectMapper == null) {
                log.error("无法获取ObjectMapper，解析API返回结果失败: {} - {}", flowOrderNo, baseOrderNo);
                return false;
            }

            // 解析响应为Map对象
            @SuppressWarnings("unchecked")
            Map<String, Object> apiResponseMap = objectMapper.readValue(apiResult, Map.class);

            // 检查API调用是否成功
            Integer code = (Integer) apiResponseMap.get("code");
            String message = (String) apiResponseMap.get("message");
            Object data = apiResponseMap.get("data");

            if (code == null || !code.equals(0)) {
                log.warn("采购订单查询接口调用失败: {} - {} - {} (错误码: {})", 
                        flowOrderNo, baseOrderNo, message, code);
                return false;
            }

            // 从data中提取validStatus
            if (!(data instanceof Map)) {
                log.warn("采购订单查询接口返回数据格式错误: {} - {}", flowOrderNo, baseOrderNo);
                return false;
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> dataMap = (Map<String, Object>) data;

            Object validStatusObj = dataMap.get("validStatus");
            if (!(validStatusObj instanceof Number)) {
                log.warn("采购订单validStatus字段格式错误: {} - {} - {}", 
                        flowOrderNo, baseOrderNo, validStatusObj);
                return false;
            }

            int validStatus = ((Number) validStatusObj).intValue();
            boolean isValid = (validStatus == 1);

            log.info("采购订单生效状态检查完成: {} - {} - validStatus={} - 结果={}", 
                    flowOrderNo, baseOrderNo, validStatus, isValid ? "已生效" : "未生效");

            return isValid;

        } catch (Exception e) {
            log.error("检查采购订单生效状态异常: {} - {}", flowOrderNo, baseOrderNo, e);
            return false;
        }
    }

    /**
     * 业务类型转换
     * 由于新的并行执行模式会同时执行两个链条，业务类型主要用于日志记录和业务数据标识
     *
     * @param baseBusinessType 基础业务类型：1-采购，2-销售
     * @return Temporal 业务类型标识
     */
    private String convertBusinessType(Integer baseBusinessType) {
        if (baseBusinessType == null) {
            return "MULTI_COMPANY_BUSINESS"; // 默认类型
        }

        switch (baseBusinessType) {
            case 1: // 采购
                return "PURCHASE_BUSINESS";
            case 2: // 销售
                return "SALES_BUSINESS";
            default:
                log.warn("未知的基础业务类型: {}，使用默认类型", baseBusinessType);
                return "MULTI_COMPANY_BUSINESS";
        }
    }
}


