package com.vedeng.erp.finance.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <b>Description:</b><br> 售后开票申请信息
 */
@Data
public class AfterSaleApplyInfoDto {

    /**
     * 售后单详情id
     */
    private Integer afterSalesDetailId;

    /**
     * 售后单id
     */
    private Integer afterSalesId;

    private Integer saleorderId;

    /**
     * 发票类型
     */
    private Integer invoiceType;

    /**
     * 发票类型名称
     */
    private String invoiceTypeStr;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 开票备注
     */
    private String invoiceComments;

    /**
     * 开票留言
     */
    private String invoiceMessage;

    /**
     * @组合对象@ 售后开票申请信息-商品信息
     */
    private List<AfterSaleApplyInfoGoodsDto> afterSaleApplyInfoGoodsDtos;

    @Data
    public static class AfterSaleApplyInfoGoodsDto {

        /**
         * 售后单商品id
         */
        private Integer afterSalesGoodsId;
        /**
         * 产品名称
         */
        private String goodsName;

        /**
         * 规格
         */
        private String spec;

        /**
         * 单位
         */
        private String unitName;

        /**
         * 单价
         */
        private BigDecimal price;

        /**
         * 申请数量
         */
        private BigDecimal applyNum;

        /**
         * 申请金额
         */
        private BigDecimal applyAmount;


        private Integer goodsId;

        private String taxCategoryNo="";

    }

}