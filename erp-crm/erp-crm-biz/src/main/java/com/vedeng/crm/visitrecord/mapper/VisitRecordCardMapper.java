package com.vedeng.crm.visitrecord.mapper;

import com.vedeng.crm.visitrecord.domain.vo.VisitRecordCardVo;
import org.apache.ibatis.annotations.Param;
import java.util.List;

public interface VisitRecordCardMapper {
    
    /**
     * 根据拜访记录ID查询打卡记录列表(每个人只取最新一条)
     */
    List<VisitRecordCardVo> selectLatestCardByRecordId(@Param("recordId") Integer recordId);

    /**
     * 插入打卡记录
     * @param record 打卡记录
     * @return 影响行数
     */
    int insert(VisitRecordCardVo record);


    /**
     * 最某个人最近一次打卡时间
     */
    String selectLastestCheckInTime(@Param("userId") Integer userId, @Param("recordId") Integer recordId);
} 