package com.vedeng.erp.finance.service.impl.check.handler;

import cn.hutool.core.util.StrUtil;
import com.vedeng.erp.finance.service.AbstractCheckHandler;
import com.vedeng.erp.finance.dto.InvoiceCheckRequestDto;
import com.vedeng.erp.finance.dto.InvoiceCheckResultDto;
import com.vedeng.erp.finance.enums.CheckHandlerEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 开票留言校验
 */
@Service
@Slf4j
public class InvoiceMessageCheckHandler extends AbstractCheckHandler {

    @Override
    public void handleSalesCheck(InvoiceCheckRequestDto invoiceCheckRequestDto, InvoiceCheckResultDto invoiceCheckResultDto) {
        log.info("开票留言校验-销售,invoiceCheckRequestDto:{}", invoiceCheckRequestDto);
        invoiceMessageCheck(invoiceCheckRequestDto, invoiceCheckResultDto);
    }

    @Override
    public void handleAfterCheck(InvoiceCheckRequestDto invoiceCheckRequestDto, InvoiceCheckResultDto invoiceCheckResultDto) {
        log.info("开票留言校验-售后,invoiceCheckRequestDto:{}", invoiceCheckRequestDto);
        invoiceMessageCheck(invoiceCheckRequestDto, invoiceCheckResultDto);
    }

    private void invoiceMessageCheck(InvoiceCheckRequestDto invoiceCheckRequestDto, InvoiceCheckResultDto invoiceCheckResultDto) {
        String invoiceMessage = invoiceCheckRequestDto.getInvoiceMessage();
        if (StrUtil.isNotBlank(invoiceMessage)) {
            CheckHandlerEnum checkHandlerEnum = getCheckHandlerEnum();
            buildResult(invoiceCheckResultDto, checkHandlerEnum.getPromptText());
        }
    }

}
