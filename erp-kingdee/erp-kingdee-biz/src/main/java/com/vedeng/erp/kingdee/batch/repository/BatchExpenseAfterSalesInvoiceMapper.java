package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchExpenseAfterSalesInvoiceDto;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2023/3/22 16:46
 **/
public interface BatchExpenseAfterSalesInvoiceMapper {
    int deleteByPrimaryKey(Long expenseAfterSalesInvoiceId);

    int insert(BatchExpenseAfterSalesInvoiceDto record);

    int insertSelective(BatchExpenseAfterSalesInvoiceDto record);

    BatchExpenseAfterSalesInvoiceDto selectByPrimaryKey(Long expenseAfterSalesInvoiceId);

    int updateByPrimaryKeySelective(BatchExpenseAfterSalesInvoiceDto record);

    int updateByPrimaryKey(BatchExpenseAfterSalesInvoiceDto record);
}