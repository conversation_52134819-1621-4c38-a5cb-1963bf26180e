void function () {
    new Vue({
        el: '#page-container',
        data: {
            tableHeaders: [
                {
                    key: "onlineSalesName",
                    label: "线上销售人员",
                    width: "150px",
                },
                {
                    key: "onlineSalesDepartment",
                    label: "线上销售部门",
                    width: "240px"
                },
                {
                    key: "fullRegionName",
                    label: "参考地区",
                    width: "200px"
                },
                {
                    key: "offlineSalesName",
                    label: "线下销售人员",
                    width: "150px"
                },
                {
                    key: "productionUserName",
                    label: "产线人员",
                    width: "150px"
                },
                {
                    key: "option",
                    label: "操作",
                    width: "45px",
                },
            ],
            searchParams: {
                provinceIds: [],
                cityIds: [],
                businessUserIds: [],
                employmentStatus: '',
                departments: [],
            },
            loading: true,
            isShowEditDialog: false,
            userList: [], //选择销售的级联数据
            categoryList: [], //选择分类的级联数据
            importFile: null,
            importErrorMsg: '',
            isShowImportDialog: false,
            addressData: [],
            addressLv2Data: [],
            formSaler: '',
            formOfflineSaler: '',
            formProductor: '',
            formAreas: [],
            filterUserRemoteInfo: {
                url: '/crm/role/profile/role-user-region-config/business-users',
                paramsType: 'url',
                paramsKey: 'userName',
                parseLabel: 'userName',
                parseValue: 'userId',
                parseAvatar: 'userAvatar'
            },
            allUserListRemoteInfo: {
                url: '/crm/user/profile/getAllUserInfo',
                paramsKey: 'name',
                parseLabel: 'username',
                parseValue: 'userId',
                parseAvatar: 'aliasHeadPicture'
            },
            userStatusList: [{
                label: '离职',
                value: '1'
            }, {
                label: '在职',
                value: '0'
            }],
            cansubmit: true,
            departmentData: []
        },
        async created() {
            this.loading = false;
            this.getdepartmentList();
            await this.getAddressData();
        },
        methods: {
            async getAddressData() {
                this.$axios.post('/crm/common/profile/getRegionAll').then(({ data }) => {
                    if (data.success) {
                        this.addressData = data.data;
                        let lv2Data = [];
                        this.addressData.forEach(item => {
                            let itemData = JSON.parse(JSON.stringify(item));
                            if(itemData.children && itemData.children.length) {
                                itemData.children.forEach(lv2 => {
                                    delete lv2.children;
                                })
                            }

                            lv2Data.push(itemData)
                        })

                        this.addressLv2Data = lv2Data;
                    }
                })
            },
            parseDepartmentData(list) {
                let departmentData = [];
                list.forEach(item => {
                    let itemData = {
                        label: item.organizationName,
                        value: item.organizationId,
                        children: []
                    }

                    if(item.childOrganization && item.childOrganization.length) {

                        itemData.children = this.parseDepartmentData(item.childOrganization);

                        departmentData.push(itemData);
                    }
                })

                return departmentData;
            },
            getdepartmentList() {
                this.$axios.get(`/crm/user/profile/getFullDepartmentTree`).then(({ data }) => {
                    if(data.success) {
                        let allDep = data.data.childOrganization[0].childOrganization;

                        this.departmentData = this.parseDepartmentData(allDep)
                    }
                })
            },
            remoteSelectChange(key) {
                this.searchParams[key + 'List'] = [this.searchParams[key]]
            },
            handlerFilterDateChange(key, value) {
                let startKey = 'start' + key;
                let endKey = 'end' + key;

                this.searchParams[startKey] = value[0] ? value[0] + ' 00:00:00' : '';
                this.searchParams[endKey] = value[1] ? value[1] + ' 23:59:59' : '';
            },
            handleFilterAddressChange(value) {
                this.searchParams.provinceIds = value.level1;
                this.searchParams.cityIds = value.level2;
            },
            handleFilterDepartChange(value) {
                let arr = [];
                if(value) {
                    arr = value.level1.concat(value.level2.concat(value.level3));
                }

                this.searchParams.departments = arr;
            },
            deleteItem(item) {
                if (!GLOBAL.auth('C0502')) {
                    GLOBAL.showNoAuth();
                    return;
                }
                
                let _this = this;
                this.$popup.warn({
                    message: `是否删除"${item.onlineSalesName}"的人员关系？`,
                    buttons: [{
                        txt: '删除',
                        btnClass: 'delete',
                        callback() {
                            _this.submitDelete([item.id]);
                        }
                    },
                    {
                        txt: '取消',
                    }]
                })
            },
            multiDelete() {
                if (!GLOBAL.auth('C0502')) {
                    GLOBAL.showNoAuth();
                    return;
                }

                let ids = this.$refs.listContainer.getSelectedData('id');
                let _this = this;

                if (!ids || !ids.length) {
                    this.$message.warn('请勾数据选行');
                } else {
                    this.$popup.warn({
                        message: '是否确认删除？',
                        buttons: [{
                            txt: '删除',
                            btnClass: 'delete',
                            callback() {
                                _this.submitDelete(ids);
                            }
                        },
                        {
                            txt: '取消',
                        }]
                    })
                }
            },
            submitDelete(ids) {
                this.$axios.post('/crm/role/profile/role-user-region-config/batch-delete', ids).then(({data}) => {
                    if(data.success) {
                        this.$message.success('删除成功');
                        this.$refs.listContainer.refresh();
                    } else {
                        this.$message.warn(data.message);
                    }
                })
            },
            clearFormData() {
                this.formSaler = '';
                this.formOfflineSaler = '';
                this.formProductor = '';
                this.formAreas = [];
            },
            showAddConfig() {
                if (!GLOBAL.auth('C0501')) {
                    GLOBAL.showNoAuth();
                    return;
                }

                let rules = {
                    formSaler: {
                        required: '请选择线上销售人员'
                    }
                };
                
                this.$form && this.$form.rules(rules, 'AddSalerRegion', this);
                this.clearFormData();
                
                this.isShowEditDialog = true;
            }, 
            addSalerRegion() {
                if(this.$form.validForm('AddSalerRegion') || !this.cansubmit) {
                    let reqData = {
                        onlineSalesId: this.formSaler,
                        offlineSalesId: this.formOfflineSaler,
                        productionUserId: this.formProductor,
                        regions: this.formAreas,
                    };

                    this.cansubmit = false;

                    this.$axios.post('/crm/role/profile/role-user-region-config/add', reqData).then(({data}) => {
                        if(data.success) {
                            this.$message.success('添加成功');
                            this.isShowEditDialog = false;

                            setTimeout(() => {
                                window.location.reload();
                            }, 1000)
                            // this.$refs.listContainer.refresh();
                        } else {
                            this.cansubmit = true;
                            this.$message.warn(data.message);
                        }
                    })

                    console.log(reqData)
                }
            },
            handleFormAddressChange(value) {
                var areas = [];

                if(value.level1.length) {
                    value.level1.forEach(item => {
                        areas.push({
                            provinceId: item,
                            cityId: ''
                        })
                    })
                } 
                
                if(value.level2.length) {
                    value.level2.forEach(item => {
                        areas.push({
                            provinceId: '',
                            cityId: item
                        })
                    })
                }

                this.formAreas = areas;
            },
            showImport() {
                if (!GLOBAL.auth('C0503')) {
                    GLOBAL.showNoAuth();
                    return;
                }
                this.isShowImportDialog = true;
                this.importFile = null;
            },
            handlerImportChange(file) {
                if (file) {
                    this.importErrorMsg = '';
                }

                this.importFile = file;
            },
            submitImportFile() {
                if (this.importFile) { 
                    let form = new FormData();
                    form.append('file', this.importFile);

                    GLOBAL.showGlobalLoading();

                    this.$axios.post('/crm/role/profile/role-user-region-config/import', form, {
                        headers: { 'Content-Type': 'multipart/form-data' }
                    }).then(({ data }) => {
                        GLOBAL.hideGlobalLoading();
                        this.tempFile = null;
                        if (data.success) {
                            this.isShowImportDialog = false;
                            let _this = this;
                            this.$popup.success({
                                message: data.message || '导入成功',
                                buttons: [{
                                    txt: '我知道了',
                                    btnClass: 'confirm',
                                    callback() {
                                    //    window.location.reload();
                                    }
                                }]
                            });
                        } else {
                            this.$popup.warn({
                                message: data.message || '导入失败',
                                buttons: [{
                                    txt: '我知道了',
                                    btnClass: 'confirm',
                                }]
                            });
                        }
                    })
                } else {
                    this.importErrorMsg = '请上传需要导入的数据文件';
                }
            },
            exportConfig() {
                if (!GLOBAL.auth('C0504')) {
                    GLOBAL.showNoAuth();
                    return;
                }
                window.open('/crm/role/profile/role-user-region-config/export');
            },
            triggerDocumentClick() {
                document.body.click();
            }
           
        }
    })
}.call(this);