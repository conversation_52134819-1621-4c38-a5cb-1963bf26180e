package com.vedeng.crm.task.mapper;

import com.vedeng.crm.task.domain.dto.TaskDto;
import com.vedeng.crm.task.domain.dto.TaskQueryDto;
import com.vedeng.crm.task.domain.entity.TaskEntity;
import com.vedeng.crm.task.domain.vo.MyTaskVo;
import com.vedeng.crm.task.domain.vo.TaskGroupSub;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TaskMapper {
    /**
     * delete by primary key
     *
     * @param taskId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long taskId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(TaskEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(TaskEntity record);

    /**
     * select by primary key
     *
     * @param taskId primary key
     * @return object by primary key
     */
    TaskEntity selectByPrimaryKey(Long taskId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(TaskEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(TaskEntity record);

    int getTaskCount(@Param("bizId") Integer bizId, @Param("todoUserId") Integer todoUserId, @Param("bizType") Integer bizType);

    int getTaskForBussinessChance(@Param("bizId") Integer bizId,  @Param("taskTypeList") List<Integer> taskTypeList);

    List<MyTaskVo> findMyTask(TaskQueryDto taskQueryDto);

    List<TaskGroupSub> findBussinessChanceProcessTask();


    TaskEntity findTaskEntityForVisit(@Param("bizId") Integer bizId, @Param("mainTaskType") Integer mainTaskType);

}