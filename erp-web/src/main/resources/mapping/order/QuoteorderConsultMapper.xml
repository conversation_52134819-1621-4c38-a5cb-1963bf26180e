<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.order.dao.QuoteorderConsultMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.order.model.QuoteorderConsult">
        <id column="QUOTEORDER_CONSULT_ID" property="quoteorderConsultId" jdbcType="INTEGER" />
        <result column="QUOTEORDER_ID" property="quoteorderId" jdbcType="INTEGER" />
        <result column="TYPE" property="type" jdbcType="BIT" />
        <result column="CONTENT" property="content" jdbcType="VARCHAR" />
        <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
        <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    </resultMap>

    <insert id="insert" useGeneratedKeys="true" keyProperty="quoteorderConsultId">
        insert into T_QUOTEORDER_CONSULT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="quoteorderConsultId != null">
                QUOTEORDER_CONSULT_ID,
            </if>
            <if test="quoteorderId != null">
                QUOTEORDER_ID,
            </if>
            <if test="type != null">
                TYPE,
            </if>
            <if test="content != null">
                CONTENT,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="quoteorderConsultId != null">
                #{quoteorderConsultId,jdbcType=INTEGER},
            </if>
            <if test="quoteorderId != null">
                #{quoteorderId,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                #{type,jdbcType=BIT},
            </if>
            <if test="content != null">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <select id="listQuotedConsult" parameterType="integer" resultMap="BaseResultMap">
        SELECT *
        FROM T_QUOTEORDER_CONSULT A
        WHERE A.QUOTEORDER_ID = #{quoteOrderId, jdbcType=INTEGER}
        AND A.TYPE = #{consultType, jdbcType=INTEGER}
    </select>
    <select id="getAllQuotedConsultlistpage" resultType="com.vedeng.order.model.QuoteorderConsult">
        SELECT *
        FROM T_QUOTEORDER_CONSULT
    </select>

</mapper>