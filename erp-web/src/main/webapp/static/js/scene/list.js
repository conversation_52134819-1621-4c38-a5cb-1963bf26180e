void function () {

    new Vue({
        el: '#page-container',
        data: {
            tableHeaders: [{
                key: "sceneNo",
                label: "方案编号",
                width: "140px"
            }, {
                key: "name",
                label: "名称",
                width: "200px"
            }, {
                key: "count",
                label: "异常数/产品总数",
                width: "180px",
                tipinfo: '异常数：场景商品等级=淘汰清退或审核状态≠审核通过的SKU'
            }, {
                key: "creator",
                label: "创建时间/创建人",
                width: "280px"
            }, {
                key: "updater",
                label: "编辑时间/编辑人",
                width: "280px"
            }, {
                key: "status",
                label: "状态",
                width: "85px"
            }, {
                key: "sort",
                label: "排序",
                width: "120px"
            }, {
                key: "option",
                label: "操作",
                width: "190px"
            },],
            searchParams: {
                sceneNo: '',
                name: '',
                filterAddTime: [],
                filterModTime: [],
                createStartTime: '',
                createEndTime: '',
                modifyStartTime: '',
                modifyEndTime: '',
                createUserList: [],
                modifyUserList: [],
                status: ''
            },
            createrListRemoteInfo: {
                url: '/sku/scene/getCreateUser.do',
                paramsType: 'url',
                paramsKey: 'username',
                parseLabel: 'userName',
                parseValue: 'userId',
                parseAvatar: 'aliasHeadPicture'
            },
            updaterListRemoteInfo: {
                url: '/sku/scene/getEditUser.do',
                paramsType: 'url',
                paramsKey: 'username',
                parseLabel: 'userName',
                parseValue: 'userId',
                parseAvatar: 'aliasHeadPicture'
            },
            statusList: [{
                label: '已下架',
                value: '1'
            }, {
                label: '已上架',
                value: '0'
            }]
        },
        methods: {
            handlerFilterDateChange(type, data) {
                let startTime = data[0] ? data[0] + ' 00:00:00' : '';
                let endTime = data[1] ? data[1] + ' 23:59:59' : '';
                this.searchParams[type + 'StartTime'] = startTime;
                this.searchParams[type + 'EndTime'] = endTime;
            },
            forceNum(item) {
                item.sort = item.sort.replace(/[^\d]/g, '');
            },
            hanlderSortChange(item) {
                if(!item.sort || (item.sort && (item.sort === '0' || parseInt(item.sort) === 0))) {
                    item.sort = item.prevSort;
                    return;
                }

                if (item.sort != item.listUiIndex + 1) {
                    VD_UI_GLOBAL.showGlobalLoading();

                    axios.post(`/sku/scene/sort.do?id=${item.id}&sort=${item.sort}`).then(({ data }) => {
                        VD_UI_GLOBAL.hideGlobalLoading();
                        if (data.code === 0) {
                            this.$refs.listContainer.refresh();
                        }
                    })
                }
            },
            updateScene(id, status) {
                let tip = ['上架后该方案在各选型场景中可见，确定上架吗？', '下架后该方案在各产品选型场景中不可见，确定下架吗？'][status];
                let btnTxt = ['上架', '下架'][status];
                let btnClass = ['confirm', 'delete'][status];
                let _this = this;

                this.$popup.warn({
                    message: tip,
                    buttons: [{
                        txt: btnTxt,
                        btnClass: btnClass,
                        callback() {
                            VD_UI_GLOBAL.showGlobalLoading();
                            axios.post(`/sku/scene/updateStatus.do?id=${id}&status=${status}`).then(({ data }) => {
                                VD_UI_GLOBAL.hideGlobalLoading();

                                if (data.code === 0) {
                                    _this.$message.success(['上架成功', '下架成功'][status]);

                                    setTimeout(() => {
                                        _this.$refs.listContainer.refresh();
                                    }, 1000)
                                } else {
                                    _this.$message.warn(data.message);
                                }
                            })
                        }
                    }, {
                        txt: '取消',
                    }]
                })
            },
            gotoDetail(id) {
                VD_UI_GLOBAL.openLink("/sku/scene/detail.do?id=" + id, {
                    name: '场景方案详情',
                    id: 'view' + id
                })
            },
            gotoAdd() {
                VD_UI_GLOBAL.openLink("/sku/scene/edit.do", {
                    name: '新增场景方案'
                })
            },
            gotoEdit(id) {
                VD_UI_GLOBAL.openLink("/sku/scene/edit.do?id=" + id, {
                    name: '编辑场景方案',
                    id: 'edit' + id
                })
            },
            copyScene(id) {
                VD_UI_GLOBAL.openLink("/sku/scene/edit.do?copyid=" + id, {
                    name: '新增场景方案'
                })
            },
            exportScene(id) {
                window.open("/sku/scene/exportExcel.do?id=" + id);
            },
        }
    })
}.call(this);