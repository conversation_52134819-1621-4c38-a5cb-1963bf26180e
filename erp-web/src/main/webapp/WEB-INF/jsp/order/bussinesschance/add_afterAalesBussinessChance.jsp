
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="新增售后商机" scope="application" />
<%@ include file="../../common/common.jsp"%>
<link rel="stylesheet" href="<%=basePath%>static/css/select2.css?rnd=${resourceVersionKey}" />
<link rel="stylesheet"
      href="${pageContext.request.contextPath}/static/new/css/common/font/font.css">
<link rel="stylesheet"
      href="${pageContext.request.contextPath}/static/new/css/common/lib/suggestSelect.css?rnd=${resourceVersionKey}">
<script type="text/javascript" src="<%= basePath %>static/js/jquery/ajaxfileupload.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src='<%= basePath %>static/js/select2.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src='<%= basePath %>static/js/select2_locale_zh-CN.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/suggestSelect.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/order/bussinesschance/add_afterAalesBussinessChance.js?rnd=${resourceVersionKey}"></script>

<style>
    .select-suggest {
        width: 450px;
    }

    .select-suggest .select-selected {
        padding-bottom: 24px;
    }

    .select-suggest .select-list-wrap {
        width: 450px;
    }

    .select-suggest .select-list-wrap li {
        float: none;
    }

    .select-suggest .select-list {
        width: 100%;
        max-width: none;
    }

    .select-suggest i {
        background: none;
    }

    .select-suggest .search-no-result {
        padding: 50px 0;
        text-align: center;
    }

    .select-suggest .search-no-result .icon-caution2 {
        color: #f60;
        float: none;
        vertical-align: -3px;
        margin-right: 5px;
        margin-bottom: 20px;
    }

    .select-suggest .select-opt-tip {
        text-align: left!important;
        padding-right: 120px;
        position: relative;
    }

    .select-suggest .select-opt-tip .select-opt-tip-txt {
        position: absolute;
        right: 0;
    }

    .select-suggest .sucsess-ok {
        float: none;
    }

    .list-no-prod {
        text-align: center;
        padding: 20px 0;
    }

    .list-no-prod .sucsess-ok {
        margin-top: 10px;
    }
</style>

	<div class="formpublic formpublic1 pt0">
        <form method="post" id="myform" action="${pageContext.request.contextPath}/order/bussinesschance/saveAddServiceBussinessChance.do">
            <div class="formtitle">请填写商机信息</div>
            <input type="hidden" name="bussinessChanceId" value="${bussinessChanceVo.bussinessChanceId}">
            <input type="hidden" id="ptype" value="${bussinessChanceVo.type}">
            <input type="hidden" id="psource" value="${bussinessChanceVo.source}">
            <input type="hidden" id="pinquiry" value="${bussinessChanceVo.communication}">
            <input type="hidden" id="sourceZj" value='${sourceZj}'>
            <input type="hidden" id="sourceBd" value='${sourceBd}'>
            <input type="hidden" name="formToken" value="${formToken}"/>
            <div class="line">
                <ul>
                    <li>
                        <div class="infor_name">
                            <span>*</span>
                            <label>商机时间</label>
                        </div>
                        <div class="f_left">
                            <input class="Wdate m0 input-middle Wdate2" name ="time" id="receiveTime" type="text" placeholder="请选择日期" 
                            	onFocus="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})" autocomplete="off"
                            	value='<date:date value ="${bussinessChanceVo.receiveTime} " format="yyyy-MM-dd HH:mm:ss"/>'>
                        </div>
                    </li>
                    <li>
                        <div class="infor_name mt0">
                            <span>*</span>
                            <label>商机类型</label>
                        </div>
                        <div class="f_left  ">
                            <ul style="width:688px">
                            			<li style="float: left;margin: 0 10px 4px 0;">
                            				<input type="radio" name="type" <c:if test="${391 eq bussinessChanceVo.type }">checked="checked"</c:if>
	                                    			value="391"><label>总机询价</label>
	                                	</li>
                            </ul>
                            <div id="type" class="font-red " style="display: none">商机来源不允许为空</div>
                        </div>
                    </li>
                    <li>
                        <div class="infor_name mt0">
                            <span>*</span>
                            <label>询价行为</label>
                        </div>
                        <div class="f_left  ">
                            <ul id="ul_inquire" style="width:688px">
                                <c:if test="${not empty inquiryData }">
                                    <c:forEach items="${inquiryData}" var ="sl">
                                            <li style="float: left;margin: 0 10px 4px 0;">
                                                <input type="radio" name="inquiry" <c:if test="${sl.sysOptionDefinitionId eq bussinessChanceVo.inquiry }">checked="checked"</c:if>
                                                       value="${sl.sysOptionDefinitionId}"><label>${sl.title}</label>
                                            </li>
                                    </c:forEach>
                                </c:if>

                            </ul>
                            <div id="inquire" class="font-red " style="display: none">询价行为不允许为空</div>
                        </div>
                    </li>
<%--                    <li>--%>
<%--                        <div class="infor_name mt0">--%>
<%--                            <span>*</span>--%>
<%--                            <label>渠道</label>--%>
<%--                        </div>--%>
<%--                        <div class="f_left  ">--%>
<%--                            <ul id="ul_source" style="width:688px">--%>
<%--                                    <li>--%>
<%--                                        <select class="wid16 selector" name="source">--%>
<%--                                            <c:if test="${not empty bussSource }">--%>
<%--                                                <c:forEach items="${bussSource }" var="sl">--%>
<%--                                                    <option value="${sl.sysOptionDefinitionId }" <c:if test="${sl.sysOptionDefinitionId eq bussinessChanceVo.source }">selected="selected"</c:if>>${sl.title }</option>--%>
<%--                                                </c:forEach>--%>
<%--                                            </c:if>--%>
<%--                                        </select>--%>
<%--                                        <select class="wid16 selector" name="communication" id="communication">--%>
<%--                                            <c:if test="${not empty communications }">--%>
<%--                                                <c:forEach items="${communications }" var="il">--%>
<%--                                                    <option value="${il.sysOptionDefinitionId }" <c:if test="${il.sysOptionDefinitionId eq bussinessChanceVo.communication}">selected="selected"</c:if>>${il.title }</option>--%>
<%--                                                </c:forEach>--%>
<%--                                            </c:if>--%>
<%--                                        </select>--%>
<%--                                    </li>--%>
<%--                            </ul>--%>
<%--                            <div id="sourceError" class="font-red " style="display: none">请选择渠道名称</div>--%>
<%--                        </div>--%>
<%--                    </li>--%>
                    <li>
                        <div class="infor_name mt0">
                            <span>*</span>
                            <label>渠道类型</label>
                        </div>
                        <div class="f_left  ">
                            <ul id="ul_source" style="width:688px">
                                <c:if test="${not empty bussSource }">
                                    <c:forEach items="${bussSource}" var ="sl">
                                            <li style="float: left;margin: 0 10px 4px 0;">
                                                <input type="radio" name="source" onclick="changeData('source')" <c:if test="${sl.sysOptionDefinitionId eq bussSource[0].sysOptionDefinitionId }">checked="checked"</c:if>
                                                       value="${sl.sysOptionDefinitionId}"><label>${sl.title}</label>
                                            </li>
                                    </c:forEach>
                                </c:if>
                            </ul>
                            <div id="source" class="font-red " style="display: none">渠道类型不允许为空</div>
                        </div>
                    </li>
                    <li>
                        <div class="infor_name mt0">
                            <span>*</span>
                            <label>渠道名称</label>
                        </div>
                        <div class="f_left  ">
                            <ul id="ul_inquiry" style="width:688px">
                                <c:if test="${not empty communications }">
                                    <c:forEach items="${communications}" var ="il">
                                        <li style="float: left;margin: 0 10px 4px 0;">
                                            <input type="radio" name="communication" value="${il.sysOptionDefinitionId}"
                                                   <c:if test="${il.sysOptionDefinitionId eq communications[0].sysOptionDefinitionId}">checked="checked"</c:if>><label>${il.title}</label>
                                        </li>
                                    </c:forEach>
                                </c:if>
                            </ul>
                            <div id="communication" class="font-red " style="display: none">渠道名称不允许为空</div>
                        </div>
                    </li>

                    <li>
                        <div class="infor_name mt0">
                            <span>*</span>
                            <label>产品分类</label>
                        </div>
                        <div class="f_left  ">
                            <ul style="width:688px">
                                <c:if test="${not empty goodsTypeList }">
                                    <c:forEach items="${goodsTypeList}" var ="gyl">
                                        <li style="float: left;margin: 0 10px 4px 0;">
                                            <input type="radio" name="goodsCategory" value="${gyl.sysOptionDefinitionId}"
                                                   <c:if test="${gyl.sysOptionDefinitionId eq bussinessChanceVo.goodsCategory}">checked="checked"</c:if>><label>${gyl.title}</label>
                                        </li>
                                    </c:forEach>
                                </c:if>
                            </ul>
                            <div id="goodsCategory" class="font-red " style="display: none">产品分类不允许为空</div>
                        </div>
                    </li>

                    <li id="productDiv_0">
                        <div class="infor_name" id="productTitleDiv_0" idFlag="0">
                            <span>*</span>
                            <label>询价产品</label>
                        </div>
                        <div class="f_left">
                            <input class="input-larger"  id="content_0"/>
                            <a onclick="deleteCategoryName(0)">删除</a>
                        </div>
                    </li>
                    <input type="hidden" id="productCountFlag" value="0">
                    <input  id="content" name="content" type="hidden">

                    <li>
                        <div class="infor_name">
                        </div>
                        <div class="f_left  ">
                            <a class="pop-new-data" layerparams='{"width":"80%","height":"788px","title":"","link":"/order/bussinesschance/addBussinessChanceProduct.do"}'>
                                + 添加
                            </a>
                        </div>
                    </li>

                    <li>
                        <div class="infor_name">
                            <label>产品备注（总机）</label>
                        </div>
                        <div class="f_left">
                            <textarea class="askprice" id="productComments" name="productComments"
                                      placeholder="请输入商机产品备注" >${bussinessChanceVo.productComments}
                            </textarea>
                        </div>
                    </li>

                    <li>
                        <div class="infor_name">
                            <label>附件</label>
                        </div>
                        <div class="f_left">
                        	<div class='pos_rel f_left'>
	                        	<input type="file" class="uploadErp" id='lwfile' name="lwfile"  onchange="uploadFile(this,1);">
				                <input type="text" class="input-larger f_left" id="name_1" name="name" readonly="readonly" value="${bussinessChanceVo.attachmentName}"
				                       placeholder="使用pdf、jpg、word或excel等文件，不允许超过2MB"  onclick="lwfile.click();">
				                <input type="hidden" name="uri" id="uri_1" value="${bussinessChanceVo.attachmentUri}" />
				                <input type="hidden" name="" id="domain" value="${bussinessChanceVo.attachmentDomain}" />
	                            <label class="bt-bg-style bt-small bg-light-blue ml8" type="file" class="f_left">浏览</label>
	                       </div>
	                       <div class="f_left">      
	                            <c:choose>
									<c:when test="${!empty bussinessChanceVo.attachmentUri}">
										<i class="iconsuccesss mt5" id="img_icon_1"></i>
				                    	<a href="http://${bussinessChanceVo.attachmentDomain}${bussinessChanceVo.attachmentUri}" target="_blank" class="font-blue cursor-pointer mr5 ml10 mt4" id="img_view_1">查看</a>
				                    	<span class="font-red cursor-pointer mt4" onclick="del(1)" id="img_del_1">删除</span>
									</c:when>
									<c:otherwise>
										<i class="iconsuccesss mt5 none" id="img_icon_1"></i>
			                    		<a href="" target="_blank" class="font-blue cursor-pointer mr5 ml10 mt4 none" id="img_view_1">查看</a>
				                    	<span class="font-red cursor-pointer mt4 none" onclick="del(1)" id="img_del_1">删除</span>
									</c:otherwise>
								</c:choose>
							</div>	
	                        <div style="clear:both"></div>    
                            <div id="upload1" class="font-red " style="display: none">请选择正确文件格式</div>
                            <div id="upload2" class="font-red " style="display: none">上传内容不允许超过2MB</div>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="formtitle ">请填写客户信息</div>
            <div class='line'>
                <ul>
                    <li>
                        <div class="infor_name">
                            <label>客户名称</label>
                        </div>

                        <div class="f_left inputfloat">
                            <span class=" mr10 mt3" id="trader_name_span_1"></span>
                            <input type="hidden" name="customerType" id="customer_type_1"
                                   value="${traderCustomerVo.customerType}">
                            <input type="hidden" id="traderName" name="traderName" value="${traderCustomerVo.traderName }">
                            <div class="J-trader-select-wrap"></div>
                            <div id="e-traderId" class="font-red " style="display: none">客户名称不允许为空</div>
                        </div>
                    </li>
                    <li>
                        <div class="infor_name">
                            <label>客户地区</label>
                        </div>
                        <div class="f_left inputfloat">
                            <select name="province" id="province" style="width:120px;">
                                <option value="0">请输入省份</option>
		                    	<c:if test="${not empty provinceList }">
		                    		<c:forEach items="${provinceList }" var="prov">
		                    			<option value="${prov.regionId }" <c:if test="${province eq prov.regionId }">selected="selected"</c:if>>${prov.regionName }</option>
		                    		</c:forEach>
		                    	</c:if>
                            </select>
                            <select name="city" id ="city" style="width:120px; margin-left: 10px">
                                <option value="0" class="gg">请输入城市</option>
                                <c:if test="${not empty cityList }">
		                    		<c:forEach items="${cityList }" var="ci">
		                    			<option value="${ci.regionId }" <c:if test="${city eq ci.regionId }">selected="selected"</c:if>>${ci.regionName }</option>
		                    		</c:forEach>
		                    	</c:if>
                            </select>
                            <select name="zone" id="zone" style="width:120px;margin-left: 10px">
                                <option value="0" class="gg">请输入县区</option>
                                <c:if test="${not empty zoneList }">
		                    		<c:forEach items="${zoneList }" var="zo">
		                    			<option value="${zo.regionId }" <c:if test="${zone eq zo.regionId }">selected="selected"</c:if>>${zo.regionName }</option>
		                    		</c:forEach>
		                    	</c:if>
                            </select>
                            <label id="text"></label>
                        </div>
                    </li>
                    <li>
                        <div class="infor_name">
                            <label>联系人</label>
                        </div>
                        <div class="f_left">
                            <input type="text" class="input-middle" value="${bussinessChanceVo.traderContactName}"  name="traderContactName" id="traderContactName">
                        </div>
                    </li>
                    <li>
                        <div class="infor_name">
                            <label>手机号</label>
                        </div>
                        <div class="f_left">
                            <input type="text" class="input-middle" value="${bussinessChanceVo.mobile}"  name="mobile" id="mobile">
                        </div>
                    </li>
                    <li>
                        <div class="infor_name">
                            <label>电话</label>
                        </div>
                        <div class="f_left">
                            <input type="text" class="input-middle" value="${bussinessChanceVo.telephone}"  name="telephone" id="telephone">
                        </div>
                    </li>
                    <li>
                        <div class="infor_name">
                            <label>其他联系方式</label>
                        </div>
                        <div class="f_left">
                            <input type="text" class="input-middle" value="${bussinessChanceVo.otherContact}"  name="otherContact" id="otherContact">
                        </div>
                    </li>
                </ul>
            </div>
            <div class="formtitle">请分配商机</div>
            <div>
                <ul>
                    <li>
                        <div class="infor_name">
                        <span>*</span>
                            <label>分配销售</label>
                        </div>
                        <div class="f_left">
                            <select name="userId" id="userId">
                                <option value="">请选择</option>
                                <c:if test="${not empty userList }">
                                	<c:forEach items="${userList}" var="user">
                                		<option value="${user.userId}" <c:if test="${bussinessChanceVo.userId eq user.userId }">selected="selected"</c:if>>${user.username}</option>
                                	</c:forEach>
                                </c:if>
                            </select>
                        </div>
                    </li>
                     <li>
                        <div class="infor_name">
                            <label>备注</label>
                        </div>
                        <div class="f_left">
                            <input type="text" class="input-large" value="${bussinessChanceVo.comments}"  name="comments" id="comments">
                        </div>
                    </li>
                </ul>
            </div>
            <div class="add-tijiao">
                <button id="submit" type="submit">提交</button>
            </div>
        </form>
    </div>

<%--   //天眼查按钮--%>
<div class="pop-new-data J-eye-layer" style="display: none;" layerParams='{"width":"800px","height":"600px","title":"天眼查",
                               "link":"${pageContext.request.contextPath}/trader/customer/add.do?optType=1&traderName", "noEncodeURI": true}'></div>
<div class="pop-new-data J-eye-layer1" style="display: none;" layerParams='{"width":"800px","height":"600px","title":"手工创建",
                               "link":"${pageContext.request.contextPath}/trader/customer/add.do?optType=1"}'></div>
<script class="J-empty" type="text/tmpl">
    <div class="search-no-result">
        <i class="vd-icon icon-caution2"></i>公司库未能匹配到该公司信息。
        <div class="btn-wrap">
            <span  class="sucsess-ok J-eye-look">天眼查</span>
            <span class="sucsess-ok J-eye-look1">手工创建</span>
        </div>
    </div>
</script>

<script type="text/javascript">
    $(function () {

        window.getProvinceData = function(val, callback){
            checkLogin();
            var regionId = val;
            if(regionId > 0){
                $.ajax({
                    type : "POST",
                    url : page_url+"/system/region/getregion.do",
                    data :{'regionId':regionId},
                    dataType : 'json',
                    success : function(data) {
                        $option = "<option value='0'>请选择</option>";
                        $.each(data.listData,function(i,n){
                            $option += "<option value='"+data.listData[i]['regionId']+"'>"+data.listData[i]['regionName']+"</option>";
                        });
                        $("select[name='city'] option:gt(0)").remove();
                        $("select[name='zone'] option:gt(0)").remove();
                        $("#zone").val("0").trigger("change");
                        $("#city").val("0").trigger("change");

                        $("select[name='city']").html($option);
                        $("select[name='zone']").html("<option value='0'>请选择</option>");

                        callback && callback();
                    },
                    error:function(data){
                        if(data.status ==1001){
                            layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                        }
                    }
                });
            }else if(regionId==0){
                $("select[name='city'] option:gt(0)").remove();
                $("select[name='zone'] option:gt(0)").remove();
            }
        };

        window.getCityData = function(val, callback){
            checkLogin();
            var regionId = val;
            if(regionId > 0){
                $.ajax({
                    type : "POST",
                    url : page_url+"/system/region/getregion.do",
                    data :{'regionId':regionId},
                    dataType : 'json',
                    success : function(data) {
                        $option = "<option value='0'>请输入县区</option>";
                        $.each(data.listData,function(i,n){
                            $option += "<option value='"+data.listData[i]['regionId']+"'>"+data.listData[i]['regionName']+"</option>";
                        });
                        $("select[name='zone'] option:gt(0)").remove();

                        $("#zone").val("0").trigger("change");
                        $("select[name='zone']").html($option);

                        callback && callback();
                    },
                    error:function(data){
                        if(data.status ==1001){
                            layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                        }
                    }
                });
            }
        };

        $("#province").select2();
        $("#zone").select2();
        $("#city").select2();

        $("select[name='province']").change(function(){
            getProvinceData($(this).val());
        });

        $("select[name='city']").change(function(){
            getCityData($(this).val());
        });

        $("select[name='source']").change(function(){
            checkLogin();
            var parentId = $(this).val();
            if(parentId > 0){
                $.ajax({
                    type : "POST",
                    url : "/sysOptionDefinition/get/parentId.do",
                    data :parentId,
                    dataType : 'json',
                    contentType: 'application/json;charset=utf-8',
                    success : function(data) {
                        $option = "<option value='0'>全部</option>";
                        $.each(data.data,function(i,n){

                            $option += "<option value='"+data.data[i]['sysOptionDefinitionId']+"'>"+data.data[i]['title']+"</option>";
                        });
                        $("select[name='communication'] option:gt(0)").remove();

                        $("select[name='communication']").html($option);
                    },
                    error:function(data){
                        if(data.code ==1001){
                            layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                        }
                    }
                });
            }else if(parentId==0){
                $("select[name='communication'] option:gt(0)").remove();
            }
        });
    });
</script>
</body>

</html>
