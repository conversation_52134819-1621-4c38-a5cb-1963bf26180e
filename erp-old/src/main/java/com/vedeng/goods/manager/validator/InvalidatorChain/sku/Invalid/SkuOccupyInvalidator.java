package com.vedeng.goods.manager.validator.InvalidatorChain.sku.Invalid;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.goods.exception.InvalidatorChainException;
import com.vedeng.goods.manager.validator.GoodsInvalidatorChain;
import com.vedeng.logistics.model.WarehouseStock;
import com.vedeng.logistics.service.WarehouseStockService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.vedeng.goods.manager.validator.enums.GoodsInvalidatorErrorEnum.*;

/**
 * .(2)	该SKU在所有逻辑库的占用为0；
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2021/9/1 16:01.
 * @author: <PERSON><PERSON>.
 */
@Service
@Slf4j
public class SkuOccupyInvalidator extends GoodsInvalidatorChain {

    @Autowired
    private WarehouseStockService warehouseStockService;

    @Override
    public void doInvalid() {
        String invalidSkuNo = getSkuNo();
        List<String> skuNos = Collections.singletonList(invalidSkuNo);
        Map<String, List<WarehouseStock>> logicalStockInfo = warehouseStockService.getLogicalStockInfo(skuNos);
        log.info("获得查询库存服务结果,查询参数:"+ JSON.toJSONString(skuNos)+"，查询结果："+JSON.toJSONString(logicalStockInfo));
        List<WarehouseStock> warehouseStocks = logicalStockInfo.get(invalidSkuNo);
        if(CollectionUtils.isNotEmpty(warehouseStocks)){
            Optional<WarehouseStock> first = warehouseStocks.stream().filter(e -> e.getOccupyNum() != 0).findFirst();
            if(first.isPresent()){
                throw new InvalidatorChainException(SkuOccupyInvalidatorError.getCode(),SkuOccupyInvalidatorError.getMessage());
            }
        }

    }
}
