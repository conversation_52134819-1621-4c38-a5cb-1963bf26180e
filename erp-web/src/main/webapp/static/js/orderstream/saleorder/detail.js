function applySaleorder(saleorderId,taskId,isZeroPrice,type) {
    var formToken = $("input[name='formToken']").val();
    var _self=self;
    $.ajax({
        url: "/order/saleorder/checkSaleorder.do",
        type: "post",
        data: {'saleorderId':saleorderId},
        dataType:'json',
        success: function(data){
            if (data.code == 0) {
                var msg = "";
                if(isZeroPrice == 1){
                    msg = "有产品价格为0,是否确认操作";
                }else{
                    msg = "您是否确认申请审核该订单？";
                }
                if(type==1) {
                    layer.confirm(msg, {
                        btn: ['确定', '取消'] //按钮
                    }, function () {
                        editApplyValidSaleorder(saleorderId, taskId, formToken);
                    }, function () {
                    });
                }else{
                    editApplyValidSaleorder(saleorderId, taskId, formToken);
                }
            } else {
                var riskmodel = data.data;
                if(riskmodel != null && riskmodel.isRisk != null && data.code == -2){
                    layer.confirm(data.message,{
                            btn: ['确认']
                        },function () {
                            if(riskmodel.isRedirect == 1){
                                var frontPageId = $(window.parent.parent.document).find('.active').eq(1).attr('id');
                                var url=riskmodel.url;
                                var item = { 'name':"客户信息", 'url': url, 'closable': true };
                                // _self.parent.parent.closableTab.addTab(item);
                                // _self.parent.parent.closableTab.resizeMove();
                                // $(window.parent.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
                                if (typeof(_self.parent.parent.closableTab) != 'undefined') {
                                    _self.parent.parent.closableTab.addTab(item);
                                    _self.parent.parent.closableTab.resizeMove();
                                    $(window.parent.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
                                }else{
                                    try{
                                        var uniqueName = url.replace('./', '').replace(/["&'./:=%?[\]]/gi, '-').replace(/(--)/gi, '');
                                        window.parent.parent.postMessage({
                                            from:'ez',
                                            name: title,
                                            url:url,
                                            id:"tab-"+uniqueName
                                        }, '*');
                                    }catch (e){}
                                }
                            }
                            window.location.reload();
                        }
                    );
                    return false;
                } else if (data.code === -3) {

                    var period_html = '<div class="checkBillPeriodForOrder"> <br/>' +
                        '<p style="color:red">当前客户账期额度不足，请至客户详情/财务与资质信息中申请 ! </p> ' +
                        '<br/>' +
                        '<table class="table table-bordered table-striped table-condensed table-centered">\n' +
                        '    <thead>\n' +
                        '    <tr>\n' +
                        '        <th>账期类型</th>\n' +
                        '        <th>可用额度（元）</th>\n' +
                        '        <th>结算标准</th>\n' +
                        '        <th>结算周期（天）</th>\n' +
                        '    </tr>\n' +
                        '    </thead>\n' +
                        '    <tbody>\n' +
                        ' ';
                    var responseData = data.data.allBillPeriod;
                    for (let i = 0; i < responseData.length; i++) {
                        if (responseData[i].billPeriodType === 1) {
                            period_html += "<tr> <td>正式账期</td>";
                        } else if (responseData[i].billPeriodType === 2) {
                            period_html += "<tr> <td>临时账期</td>";
                        } else if (responseData[i].billPeriodType === 3) {
                            period_html += "<tr> <td>订单账期</td>";
                        } else {
                            period_html += "<tr> <td></td> "
                        }
                        period_html += "<td>" + responseData[i].availableAmount + "</td>";
                        if (responseData[i].billPeriodSettlementType === 1) {
                            period_html += "<td>订单发货</td>";
                        } else if (responseData[i].billPeriodSettlementType === 2) {
                            period_html += "<td>订单开票</td>";
                        } else {
                            period_html += "<td></td> "
                        }
                        period_html += "<td>" + responseData[i].settlementPeriod + "</td> </tr>";
                    }

                    period_html += '</tbody></table></div>'

                    layer.open({
                        type: 1,
                        id: 'checkBillPeriodForOrder',
                        title: '温馨提示:',
                        area: ['460px', '250px'],
                        resize: false,
                        btn: ['前往', '取消'],
                        content: period_html,
                        yes: function (index, layero) {
                            // 请至客户详情/财务与资质信息中申请 跳转
                            layer.close(index);
                            $("#toCustomerInfo").attr("tabtitle",'{"num":"viewcustomer'+data.data.traderCustomerId+'","link":"/trader/customer/getFinanceAndAptitude.do?traderId='+data.data.traderId+'&traderCustomerId='+data.data.traderCustomerId+'","title":"客户信息"}');
                            $("#toCustomerInfo").click();
                            // window.location.href = page_url+"/trader/customer/getFinanceAndAptitude.do?traderId="+data.data.traderId +"&traderCustomerId="+data.data.traderCustomerId;

                        },

                    });

                    return false;

                } else {
                    layer.alert(data.message);
                    return false;
                }
            }

        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    });
}

function applyValidSaleOrderOfNew(saleOrderId,paymentType, taskId,isZeroPrice){
    if (paymentType === 423){
        layer.confirm(
            '此订单为"先货后款，预付0%"，订单审核通过后，需要点击【信用支付】按钮二次确认付款。',
            {
                title: '提示',
                btn: ['我知道了']
            },
            function (index){
                applyValidSaleOrder(saleOrderId,taskId,isZeroPrice);
            }
        )
    } else {
        applyValidSaleOrder(saleOrderId,taskId,isZeroPrice);
    }
}


function applyValidSaleOrder(saleorderId, taskId,isZeroPrice){
    checkLogin();
    // 校验产品是否配置内部备注标签
    let specialSkuList = getSpecialGoodsList();
    let remarkError = 0
    $('#goodsTbody').children('tr').each(function(i, v){
        if (i === $('#goodsTbody').children('tr') - 1) {
            return false;
        }
        $(v).children('td').each(function(_i, _v){
            if ($(_v).hasClass('c-comments') && !$(_v).children('.customername').children('.customernameshow').html()) {
                let skuId = parseInt($(_v).attr('skuId'));
                let skuList = specialSkuList.filter(function (sku) {
                    return skuId === sku;
                })
                if (skuList.length > 0) {
                    return false;
                }
                remarkError++;
                $(_v).children('.no_remark_error').css('display','block');
            }
        })
    })
    if (remarkError > 0) {
        return false;
    }
    var _self=self;
    $.ajax({
        type: "POST",
        url: "/order/saleorder/getOrderTraderAptitudeStatus.do",
        data: {'saleorderId':saleorderId},
        dataType:'json',
        success: function(data){
            if (data.code == 0) {
                var customer=data.data;
                if(customer.aptitudeStatus==null||customer.aptitudeStatus==3||customer.aptitudeStatus==2){
                    layer.confirm("客户资质尚未提交审核，请前往维护", {
                        btn: ['继续提交','去维护'] //按钮
                    }, function(){
                        applySaleorder(saleorderId,taskId,isZeroPrice,2)
                    }, function(){
                        window.location.reload();
                        var frontPageId = $(window.parent.parent.document).find('.active').eq(1).attr('id');
                        var url=page_url+"/trader/customer/getFinanceAndAptitude.do?traderId="+customer.traderId+"&traderCustomerId="+customer.traderCustomerId;
                        var item = { 'id': customer.traderCustomerId, 'name':"财务与资质信息", 'url': url, 'closable': true };
                        // _self.parent.parent.closableTab.addTab(item);
                        // _self.parent.parent.closableTab.resizeMove();
                        // $(window.parent.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
                        if (typeof(_self.parent.parent.closableTab) != 'undefined') {
                            _self.parent.parent.closableTab.addTab(item);
                            _self.parent.parent.closableTab.resizeMove();
                            $(window.parent.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
                        }else{
                            try{
                                var uniqueName = url.replace('./', '').replace(/["&'./:=%?[\]]/gi, '-').replace(/(--)/gi, '');
                                window.parent.parent.postMessage({
                                    from:'ez',
                                    name: title,
                                    url:url,
                                    id:"tab-"+uniqueName
                                }, '*');
                            }catch (e){}
                        }
                    });

                }else{
                    applySaleorder(saleorderId,taskId,isZeroPrice,1)
                }
            } else {
                layer.alert(data.message);
            }
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    });
}


function editApplyValidSaleorder(saleorderId,taskId,formToken) {
    var loadingIndex = layer.open({
        type: 3,
        content: '提交中...',
        shade: [0.5, '#000']
    });
    $.ajax({
        type: "POST",
        url: "/order/saleorder/editApplyValidSaleorder.do",
        data: {'saleorderId':saleorderId,'taskId':taskId,'formToken':formToken},
        dataType:'json',
        success: function(data){

            if (data.code == 0) {
                //layer.close(loadingIndex);//这边不做关闭，因为reload需要时间防止页面停留在上一个，给人卡顿的感觉
                window.location.reload();
            } else {
                layer.close(loadingIndex);
                layer.alert(data.message);
            }
        },
        error:function(data){
            layer.close(loadingIndex);
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    });
}

function synUpdateTermianalInfo(quoteorderId,orderType) {

    checkLogin();
    index = layer.confirm("是否确认同步更新?", {
        btn : [ '是', '否' ]
    }, function() {

        $.ajax({
            async : false,
            url : page_url + '/newQuote/updateTerminalInfo.do',
            data : {"orderId":quoteorderId,"orderType":orderType},
            type : "POST",
            dataType : "json",
            success : function(data) {
                if(data.code == 0){
                    layer.alert(data.message);
                    location.reload();
                }
                else {
                    layer.alert(data.message)
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    });
}