package com.vedeng.goods.enums;

import lombok.Getter;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Getter
public enum GoodsTypeEnum {

    /**
     * 商品类型未知
     */
    UNKNOWN(0, "未知"),

    DEVICE(316, "设备"),

    CONSUMABLES(317, "耗材"),

    REAGENT(318, "试剂"),

    PARTS(1008, "配件"),

    /**
     * @deprecated 已废弃
     */
    @Deprecated
    CONSUMABLES_PLUS(653, "高值耗材"),

    /**
     * @deprecated 已废弃
     */
    @Deprecated
    OTHERS(319, "其他");


    /**
     * DB数据字典唯一编号
     */
    private Integer id;
    private String name;

    GoodsTypeEnum(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    public static GoodsTypeEnum getByType(Integer input) {
        if (input == null || input <= 0) {
            return UNKNOWN;
        }

        for (GoodsTypeEnum typeEnum : values()) {
            if (typeEnum.getId().equals(input)) {
                return typeEnum;
            }
        }

        return UNKNOWN;
    }
}
