package com.vedeng.erp.market.service;

import com.vedeng.common.page.Page;
import com.vedeng.erp.market.domain.entity.MarketPlan;
import com.vedeng.erp.market.domain.vo.MarketPlanTraderResponseVo;
import com.vedeng.erp.market.domain.vo.MarketPlanVo;
import com.vedeng.system.model.Tag;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2023/9/20
 */
public interface MarketService {

    /**
     * 解析从前台过来的精准营销运营活动
     * @param marketPlanVo
     * @return
     */
    boolean handleMarketPlanFromOp(MarketPlanVo marketPlanVo);

    /**
     * 查询任务抬头
     * @param planId
     * @return
     */
    MarketPlan queryMarketPlanByPlanId(Integer planId);

    /**
     * 查询任务明细清单
     * @param planId
     * @param userId
     * @return
     */
    List<MarketPlanTraderResponseVo> queryMarketPlanTraderList(Integer planId,Integer sendMsg, Integer userId, Page page );


    List<MarketPlan> queryMarketPlanTongqi(Integer planId,
                                           Integer userId,
                                           Integer traderId,
                                           Integer traderCustomerId
                                            ) ;

    Map<String,Integer> queryUserMarketPlanTraderList(Integer userId);



}
