package com.vedeng.activiti.taskassign;


import com.vedeng.erp.buyorder.dto.BuyorderExpenseDto;
import com.vedeng.erp.buyorder.service.BuyorderExpenseApiService;
import com.vedeng.order.service.BuyorderService;
import com.vedeng.order.service.SaleorderService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import java.util.Objects;

/**
 * 清空BD的审核信息
 */
public class ClearBhOrderAuditInfoListener implements ExecutionListener {

    private static final long serialVersionUID = 1L;

    // 运行时注入service
    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();

    private SaleorderService saleorderService = (SaleorderService) context.getBean("saleorderService");

    private BuyorderService buyorderService = (BuyorderService) context.getBean("buyorderService");

    private BuyorderExpenseApiService buyorderExpenseApiService = (BuyorderExpenseApiService)context.getBean("buyorderExpenseServiceImpl");

    @Override
    public void notify(DelegateExecution execution) throws Exception {
        // 申请人名称
        String businessKey = execution.getVariable("businessKey").toString();

        String processKey = businessKey.split("_")[0];
        Integer orderId = Integer.valueOf(businessKey.split("_")[1]);

        if("bhSaleorderVerify".equals(processKey)){
            saleorderService.clearBhOrderAuditInfo(orderId);
        }else if("buyorderVerify".equals(processKey)){
            buyorderService.clearBuyOrderAuditInfo(orderId);

            BuyorderExpenseDto orderMainData = buyorderExpenseApiService.queryAttributiveExpenseInfoByBuyorderId(orderId);


        } else if ("buyorderExpenseVerify".equals(processKey)) {
            buyorderExpenseApiService.unlockBuyOrderExpense(orderId);
        }
    }
}
