package com.vedeng.customerbillperiod.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: daniel
 * @Date: 2021/7/26 15 07
 * @Description: 回滚客户账期管理编码的传输类
 */
@Data
public class RollbackCustomerBillPeriodManagementDetailDto {

    private Integer companyId;

    private Long customerId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 生成账期逾期管理明细的类型，3售后退货，4售后退票 @CustomerBillPeriodOverdueManageDetailTypeEnum
     */
    private Integer type;

    /**
     * 明细金额
     */
    private BigDecimal amount;

    /**
     * 关联的售后单id/流水id
     */
    private Long relatedId;

    private Long addTime;
}
