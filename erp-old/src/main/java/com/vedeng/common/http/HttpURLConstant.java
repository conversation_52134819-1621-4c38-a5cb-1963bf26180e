package com.vedeng.common.http;

/**
 * @Description:
 * @Auther: Duke.li
 * @Date: 2019/7/12 13:43
 */
public class HttpURLConstant {


    /**
     * 获取运营系统：各平台集合
     */
    public final static String OP_PLAT_FROM_LIST = "/goods/platFromList";

    /**
     * 获取运营系统：各平台集合
     */
    public final static String OP_PUSH_GOODS_INFO = "/goods/pushGoodsInfo";

    public final static String OP_PUSH_SPU_INFO = "/goods/pushSpuInfo";

    /**
     * 排序批量查询成本价
     */
    public final static String PRICE_BATCH_QUERY_PRICE_INFO_URL = "/sku_price_info/batchFindSkuPriceInfoBySkuNos";


    /**
     * 查询skuNO运营分类
     */
    public final static String GOODS_QUERY_CATEGORY_BYSKUNO = "/goods/queryCategoryBySkuNo";

    /**
     * 排序批量查询
     */
    public final static String GOODS_QUERY_SKU_LIST_SORT_URL = "/goods/batchQueryBySort";

    /**
     * 普通库存批量查询
     */
    public final static String STOCK_GET_STOCK_INFO_URL = "/stock/getStockInfo";

    /**
     * 回调库存转移单信息
     */
    public final static String STOCK_POST_INVENTORY_TRANSFER = "/promotion/action/InventoryTransfer";

    /**
     * 库存转移单完成回调
     */
    public final static String COMPLETE_INVENTORY_TRANSFER = "/promotion/action/completeInventoryTransfer";

    /**
     * 请求stock完成库存调整
     */
    public final static String COMPLETE_INVENTORY_ADJUSTMENT = "/promotion/action/completeInventoryAdjustment";

    /**
     * 请求商品马良图文详情
     */
    public static final String SKU_GRAPHIC_DETAIL= "ml/getSkuGraphicDetailInfo";
}
