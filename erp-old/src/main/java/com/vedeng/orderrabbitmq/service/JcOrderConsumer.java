package com.vedeng.orderrabbitmq.service;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.rabbitmq.client.Channel;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.constant.OrderConstant;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.trace.data.TrackParamsData;
import com.vedeng.common.trace.enums.EventTrackingEnum;
import com.vedeng.common.trace.mq.AbstractMessageListener;
import com.vedeng.common.trace.track.TrackStrategy;
import com.vedeng.common.trace.track.factory.TrackStrategyFactory;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.common.util.StringUtil;
import com.vedeng.erp.saleorder.api.OrderInfoSyncService;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.enums.SaleOrderStatusEnum;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.service.JcOrderService;
import com.vedeng.order.service.OrderAccountPeriodService;
import com.vedeng.order.service.OrderReviewProcessService;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.rabbitmqlogs.service.RabbitMqLogService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Component
public class JcOrderConsumer extends AbstractMessageListener {

    private final static Logger LOGGER = LoggerFactory.getLogger(JcOrderConsumer.class);

    @Resource
    private RabbitMqLogService rabbitMqLogService;
    @Resource
    private SaleorderService saleorderService;
    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;
    @Resource
    private JcOrderService jcOrderService;

    @Autowired
    private OrderAccountPeriodService orderAccountPeriodService;

    @Autowired
    private OrderReviewProcessService orderReviewProcessService;

    @Autowired
    private OrderInfoSyncService orderInfoSyncService;

    /**
     * 推送的消息类型
     */
    private static final Integer SUBMIT_ORDER_TYPE = 1;
    private static final Integer CONFIRM_ORDER_TYPE = 2;
    private static final Integer CANCEL_ORDER_TYPE = 4;

    private static final String ORDER_NO_NAME = "orderNo";
    private static final String PUSH_TYPE_NAME = "pushType";
    private static final String GOODS_INFO_NAME = "orderSkus";

    private static final int ERROR_LOG_LIMITED_SIZE = 250;

    @Override
    public void doBusiness(Message message, Channel channel) throws Exception {
        boolean success = false;
        try {
            String paramsInJson = new String(message.getBody(), StandardCharsets.UTF_8);

            LOGGER.info("接收到前台集采线上订单消息 - params: {}", paramsInJson);

            handleAcceptedMessage(paramsInJson);
            success = true;

        } catch (Exception e) {
            LOGGER.error("消费者接受集采订单商品消息失败", e);
        }

        //确认并保存消费失败消息
        if (success) {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } else {
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    @Autowired
    private TrackStrategyFactory trackStrategyFactory;

    /**
     * 埋点
     * @param orderNo
     * @param traderId
     */
    private void addTrackCreateSaleOrder( String orderNo, Integer traderId) {
        EventTrackingEnum eventTrackingEnum = EventTrackingEnum.SALE_CREATE_ORDER_FRONT;
        try {
            TrackParamsData trackParamsData = new TrackParamsData();
            Map<String, Object> trackParams = new HashMap<>();
            trackParams.put("traderId",traderId);
            trackParams.put("orderNo",orderNo);
            TrackStrategy trackStrategy = trackStrategyFactory.getStrategyByType(eventTrackingEnum);
            trackParamsData.setEventTrackingEnum(eventTrackingEnum);
            trackParamsData.setTrackParams(trackParams);
            trackParamsData.setTrackResult(ResultInfo.success());
            trackStrategy.track(trackParamsData);
        }catch(Exception e) {
            LOGGER.error("埋点：{}，失败，不影响正常业务",eventTrackingEnum.getArchivedName(),e);
        }
    }

    private void handleAcceptedMessage(String paramsInJson) throws Exception {
        Preconditions.checkArgument(StringUtils.isNotEmpty(paramsInJson), "请求参数为空");

        Map<String, Object> paramsMap = convertParamsInJsonToMap(paramsInJson);

        if (SUBMIT_ORDER_TYPE.equals(paramsMap.get(PUSH_TYPE_NAME))) {
            //处理前台下单信息
            handleMessageWhenSubmittingOrderInfo(paramsMap, paramsInJson);
        } else if (CONFIRM_ORDER_TYPE.equals(paramsMap.get(PUSH_TYPE_NAME))) {
            String orderNo = checkOrderNoIfExist(paramsMap,"集采订单前台确认订单时订单单号为空");

            Saleorder orderQuery = saleorderService.getBySaleOrderNo(orderNo);
            if (orderQuery == null) {
                throw new IllegalArgumentException(String.format("查询订单orderNo:[%s]信息失败", orderNo));
            }

            //更新订单状态
            Saleorder saleorderExtra = new Saleorder();
            saleorderExtra.setSaleorderNo(orderNo);
            saleorderExtra.setSaleorderId(orderQuery.getSaleorderId());
            saleorderExtra.setStatus(SaleOrderStatusEnum.CONFIRMING.getStatus());
            saleorderService.updateByJCOrder(saleorderExtra);

            // 订单商品信息
            if (paramsMap.containsKey("orderSkus")) {
                List<Map<String, Object>> list = (List<Map<String, Object>>) paramsMap.get("orderSkus");
                for (Map<String, Object> goodsMap : list) {
                    SaleorderGoods orderGoods = goodsInfoFromMap(goodsMap);
                    orderGoods.setSaleorderId(orderQuery.getSaleorderId());
                    if (orderGoods == null) {
                        continue;
                    }
                    saleorderGoodsMapper.updateJCSaleOrderGoods(orderGoods);
                }
            }
        } else if (CANCEL_ORDER_TYPE.equals(paramsMap.get(PUSH_TYPE_NAME))) {
            String orderNo = checkOrderNoIfExist(paramsMap,"集采订单前台取消订单时订单单号为空");
            Saleorder saleorderQuery = saleorderService.getBySaleOrderNo(orderNo);
            if (saleorderQuery == null) {
                throw new IllegalArgumentException(String.format("查询订单orderNo:[%s]信息失败", orderNo));
            }

            //关闭订单处理订单的账期业务
            LOGGER.info("JC订单关闭处理订单的账期业 saleOrder:{}", JSON.toJSONString(saleorderQuery));
            orderAccountPeriodService.dealCloseOrderCustomerBillPeriodOfOrder(saleorderQuery);


            LOGGER.info("集采订单前台推送关闭处理审核信息 saleOrderId:{}", saleorderQuery.getSaleorderId());
            orderReviewProcessService.dealSaleOrderReviewProcess(saleorderQuery.getSaleorderId(), 1);

            Saleorder saleorder = new Saleorder();
            String cancelReason = null;
            if (paramsMap.containsKey("cancelReason")){
                cancelReason = MapUtils.getString(paramsMap,"cancelReason");
                saleorder.setCloseComments(cancelReason);
            }else{
                saleorder.setCloseComments("客户在前台关闭");
            }
            saleorder.setSaleorderNo(orderNo);
            saleorder.setSaleorderId(saleorderQuery.getSaleorderId());
            saleorder.setStatus(SaleOrderStatusEnum.CANCEL.getStatus());
            saleorderService.updateByJCOrder(saleorder);
        } else {
            throw new UnsupportedOperationException(String.format("暂不支持此类型[%s]消息", paramsMap.get(PUSH_TYPE_NAME)));
        }

    }



    private void handleMessageWhenSubmittingOrderInfo(Map<String, Object> paramsMap, String paramsInJson) throws Exception {
        String orderNoToSave = null;
        Exception caughtException = null;
        try {
            //获取订单信息
            Saleorder orderToUse = obtainOrderInfoFromMap(paramsMap);

            orderNoToSave = orderToUse.getSaleorderNo();

            List<SaleorderGoods> goodsList = obtainGoodsInfoFromMap(paramsMap);

            //===============================================================================================订单价格 Begin

            BigDecimal totalAmount= new BigDecimal(MapUtils.getString(paramsMap, "totalMoney","0"));

            if (totalAmount.compareTo(BigDecimal.ZERO) == 0){
                throw new IllegalArgumentException(String.format("保存JCO订单[%s]缺少订单总价", orderNoToSave));
            }

            //先款后货，预付0% (帐期支付)
            orderToUse.setPaymentType(OrderConstant.PREPAY_100_PERCENT);
            // 支付状态, 始终取0
            orderToUse.setPaymentStatus(0);
            //订单原金额
            orderToUse.setOriginalAmount(totalAmount);
            orderToUse.setPrepaidAmount(totalAmount);
            orderToUse.setTotalAmount(totalAmount);

            //===============================================================================================订单价格 End


            if (CollectionUtils.isEmpty(goodsList)) {
                LOGGER.error("集采线上下单订单信息中商品信息为空 - params:{}", paramsInJson);
            }

            orderToUse.setGoodsList(goodsList);
            //订单流二期--集采后续订单走新销售页面
            orderToUse.setIsNew(orderInfoSyncService.setSaleorderIsNewByOrg(orderToUse.getSaleorderNo(),orderToUse.getTraderId()));
            //保存订单信息
            jcOrderService.saveJcoOrder(orderToUse);

            addTrackCreateSaleOrder(orderToUse.getSaleorderNo(),orderToUse.getTraderId());
        } catch (Exception e) {
            caughtException = e;
        }

        if (caughtException != null) {
            try {
                rabbitMqLogService.saveRabbitmq(paramsInJson, StringUtils.substring(caughtException.getMessage(),0,ERROR_LOG_LIMITED_SIZE), orderNoToSave);
            } catch (Exception e) {
                LOGGER.error("保存Mq错误记录日志失败", e);
            }

            throw caughtException;
        }
    }


    @SuppressWarnings("unchecked")
    private Map<String, Object> convertParamsInJsonToMap(String paramsInJson) {
        Map<String, Object> paramsMap;
        try {
            paramsMap = Collections.checkedMap(JsonUtils.readValue(paramsInJson, Map.class), String.class, Object.class);
        } catch (Exception e) {
            LOGGER.error("反序列化Json时发生异常", e);
            throw new IllegalStateException("反序列化Json时发生异常", e);
        }

        if (MapUtils.isEmpty(paramsMap)) {
            throw new IllegalArgumentException("解析后参数为空");
        }
        return paramsMap;
    }

    private SaleorderGoods goodsInfoFromMap(Map<String, Object> goodsMap) {
        if (MapUtils.isEmpty(goodsMap)) {
            return null;
        }
        SaleorderGoods orderGoods = new SaleorderGoods();
        for (String key : goodsMap.keySet()) {
            switch (key) {
                case "skuTags":
                    orderGoods.setSkuTags(goodsMap.get(key).toString());
                    break;
                case "skuCode":
                    orderGoods.setSku(goodsMap.get(key).toString());
                    break;
                default:
                    break;
            }

        }
        return orderGoods;
    }


    private Saleorder obtainOrderInfoFromMap(Map<String, Object> paramsMap) {
        final String orderNo = checkOrderNoIfExist(paramsMap, "集采线上订单提交时订单单号为空");

        Saleorder orderToUse = new Saleorder();

        orderToUse.setSaleorderNo(orderNo);

        //===============================================================================================下单客户信息 Begin

        //付款客户ID - 集采客户订单结算主体
        Integer settlementTraderId = MapUtils.getInteger(paramsMap, "settlementTraderId");
        if (settlementTraderId == null) {
            throw new IllegalArgumentException(String.format("集采订单[%s]保存时结算主体为空", orderNo));
        }
        String settlementTraderName = MapUtils.getString(paramsMap, "settlementTraderName");
        if (Strings.isNullOrEmpty(settlementTraderName)) {
            throw new IllegalArgumentException(String.format("集采订单[%s]保存时结算名称为空", orderNo));
        }

        orderToUse.setTraderId(settlementTraderId);
        orderToUse.setTraderName(settlementTraderName);
        orderToUse.setInvoiceTraderId(settlementTraderId);
        orderToUse.setInvoiceTraderName(settlementTraderName);

        //创建订单用户手机号
        String creatorMobileNo = MapUtils.getString(paramsMap, "mobile");
        if(StringUtils.isEmpty(creatorMobileNo)) {
            throw new IllegalArgumentException(String.format("保存集采订单[%s]时下单人用户手机号为空", orderNo));
        }

        String accountName = MapUtils.getString(paramsMap, "accountName");
        if(StringUtils.isEmpty(accountName)) {
            throw new IllegalArgumentException(String.format("保存集采订单[%s]时下单人用户名称为空", orderNo));
        }

        orderToUse.setTraderContactName(accountName);
        orderToUse.setTraderContactMobile(creatorMobileNo);
        //保存创建订单人手机号
        orderToUse.setCreateMobile(creatorMobileNo);

        // 是否打印出库单
        Integer isPrintout = MapUtils.getInteger(paramsMap, "showOutboundPrice");
        if(isPrintout != null && isPrintout == 0){
            isPrintout = 2;
        }
        orderToUse.setIsPrintout(isPrintout == null? 2:isPrintout);
        //===============================================================================================下单客户信息 End


        //===============================================================================================收货客户信息 Begin


        // 收货客户编号
        orderToUse.setTakeTraderId(Objects.requireNonNull(MapUtils.getInteger(paramsMap, "deliveryTraderId"), "收货客户ID为空"));
        // 收货客户名称
        orderToUse.setTakeTraderName(MapUtils.getString(paramsMap,"deliveryTraderName"));
        //收货人姓名
        orderToUse.setTakeTraderContactName(MapUtils.getString(paramsMap, "deliveryUserName"));
        //收货人手机号
        orderToUse.setTakeTraderContactMobile(MapUtils.getString(paramsMap, "deliveryUserMobile"));
        // 收货地区最小级ID
        orderToUse.setTakeTraderAreaId(Objects.requireNonNull(MapUtils.getInteger(paramsMap, "minDeliveryAddressId"), "收货地区最小级ID为空"));
        //收货人详细地址
        orderToUse.setTakeTraderAddress(MapUtils.getString(paramsMap, "deliveryUserDetailAddress"));

        //发货方式（默认分批发货）
        orderToUse.setDeliveryType(482);
        //发货要求（默认立即发货）
        orderToUse.setDeliveryClaim(0);


        //===============================================================================================收货客户信息 End


        //===============================================================================================收票信息 Begin


        //收票人名称
        orderToUse.setInvoiceTraderContactName(MapUtils.getString(paramsMap, "invoiceUserName"));
        // 默认为客户手机号
        orderToUse.setInvoiceTraderContactMobile(MapUtils.getString(paramsMap, "invoiceUserMobile"));
        // 收票地址 最小地址Id
        orderToUse.setInvoiceTraderAreaId(Objects.requireNonNull(MapUtils.getInteger(paramsMap, "minInvoiceAddressId"),"收票地址最小地址Id为空"));
        // 收票人详细地址
        orderToUse.setInvoiceTraderAddress(MapUtils.getString(paramsMap, "invoiceDetailAddress"));

        if (paramsMap.containsKey("invoiceType")) {
            //发票类型 971: 13%增值税普通发票  972: 13%增值税专用发票
            orderToUse.setInvoiceType(MapUtils.getInteger(paramsMap, "invoiceType"));
        }

        if (paramsMap.containsKey("invoiceMethod")) {
            orderToUse.setInvoiceMethod(MapUtils.getInteger(paramsMap, "invoiceMethod"));
        }

        //是否票货同行 0-否 1-是
        orderToUse.setIsSameAddress(MapUtils.getInteger(paramsMap, "invoiceGoodsTogether"));
        //发票寄送方式 0-无 1-全部发货时寄送 2-分配发货时寄送 票货同行是必须有值1货2
        Integer invoiceDeliveryType = MapUtils.getInteger(paramsMap, "invoiceDeliveryType");
        if (Objects.nonNull(invoiceDeliveryType)){
            if(invoiceDeliveryType == 1){
                orderToUse.setInvoiceSendNode(0);
            }
            if (invoiceDeliveryType == 2){
                orderToUse.setInvoiceSendNode(1);
            }
        }
        //=============================================================================================== 收票信息 End


        String remarks = MapUtils.getString(paramsMap, "remarks");
        if (StringUtil.isNotEmpty(remarks)) {
            orderToUse.setBdtraderComments(remarks);
        }

        if (paramsMap.containsKey("addTime")) {
            Long addTime = MapUtils.getLong(paramsMap, "addTime");
            orderToUse.setAddTime(addTime);
            orderToUse.setModTime(addTime);
        }

        return orderToUse;
    }


    private List<SaleorderGoods> obtainGoodsInfoFromMap(Map<String, Object> paramsMap) {
        if (!paramsMap.containsKey(GOODS_INFO_NAME)) {
            return Collections.emptyList();
        }

        // 订单下单的商品信息
        List<Map<String, Object>> saleGoodsList = (ArrayList<Map<String, Object>>) paramsMap.get(GOODS_INFO_NAME);

        if (CollectionUtils.isEmpty(saleGoodsList)) {
            return Collections.emptyList();
        }

        List<SaleorderGoods> goodsList = new ArrayList<>();

        for (Map<String, Object> goodsMap : saleGoodsList) {
            SaleorderGoods orderGoods = parseSaleGoodsFromMap(goodsMap);
            if (orderGoods == null) {
                continue;
            }
            goodsList.add(orderGoods);
        }

        return goodsList;
    }


    private SaleorderGoods parseSaleGoodsFromMap(Map<String, Object> goodsMap) {
        SaleorderGoods orderGoods = new SaleorderGoods();

        if (goodsMap.containsKey("skuId")) {
            orderGoods.setGoodsId(MapUtils.getInteger(goodsMap, "skuId"));
        }

        if (goodsMap.containsKey("skuName")) {
            orderGoods.setGoodsName(MapUtils.getString(goodsMap,"skuName"));
        }

        if (goodsMap.containsKey("skuUnitName")) {
            orderGoods.setUnitName(MapUtils.getString(goodsMap, "skuUnitName"));
        }

        //商品订货号
        if (goodsMap.containsKey("skuCode")) {
            orderGoods.setSku(MapUtils.getString(goodsMap,"skuCode"));
        }

        if (goodsMap.containsKey("brandName")) {
            orderGoods.setBrandName(MapUtils.getString(goodsMap, "brandName"));
        }

        //商品规格
        if (goodsMap.containsKey("skuSpecifications")) {
            orderGoods.setModel(MapUtils.getString(goodsMap, "skuSpecifications"));
        }

        // 实际单价
        if (goodsMap.containsKey("skuPrice") && goodsMap.containsKey("skuNum")) {
            Integer skuNum = MapUtils.getInteger(goodsMap, "skuNum", 0);
            BigDecimal price = new BigDecimal(MapUtils.getString(goodsMap, "skuPrice","0"));
            BigDecimal totalAmount = price.multiply(BigDecimal.valueOf(skuNum));
            if (totalAmount.compareTo(BigDecimal.ZERO) <= 0) {
                throw new IllegalStateException("JCO订单保存时商品单价或者商品商量为空");
            }

            orderGoods.setNum(skuNum);
            orderGoods.setPrice(price);
            orderGoods.setRealPrice(price);
            orderGoods.setMaxSkuRefundAmount(totalAmount);
        }

        // 实际商品成本价
        if (goodsMap.containsKey("skuCostPrice")) {
            orderGoods.setReferenceCostPrice(new BigDecimal(MapUtils.getString(goodsMap,"skuCostPrice")));
        }

        //商品标签
        if (goodsMap.containsKey("skuTags")) {
            orderGoods.setSkuTags(MapUtils.getString(goodsMap,"skuTags"));
        }

        if (goodsMap.containsKey("addTime")) {
            orderGoods.setAddTime(MapUtils.getLong(goodsMap,"addTime"));
        }

        if (goodsMap.containsKey("creator")) {
            orderGoods.setCreator(MapUtils.getInteger(goodsMap,"creator"));
        }

        if (goodsMap.containsKey("modTime")) {
            orderGoods.setModTime(MapUtils.getLong(goodsMap,"modTime"));
        }

        if (goodsMap.containsKey("updater")) {
            orderGoods.setUpdater(MapUtils.getInteger(goodsMap,"updater"));
        }

        //集采商品暂无优惠券
        orderGoods.setIsCoupons(CommonConstants.DISABLE);
        orderGoods.setIsActionGoods(CommonConstants.DISABLE);

        return orderGoods;
    }

    private String checkOrderNoIfExist(Map<String, Object> paramsMap, String errorMessage) {
        String orderNo = MapUtils.getString(paramsMap, ORDER_NO_NAME);
        if (StringUtils.isBlank(orderNo)) {
            throw new IllegalArgumentException(StringUtils.isNotEmpty(errorMessage) ? errorMessage : "订单号为空");
        }
        return orderNo;
    }
}
