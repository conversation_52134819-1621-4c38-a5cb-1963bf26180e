package com.vedeng.erp.buyorder.task.buyorder.statusSync;

import com.newtask.data.AbstractDataByTimeSync;
import com.newtask.data.dao.BuyorderDataMapper;
import com.newtask.data.dto.BuyorderDataDto;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description com.newtask.data.buyorder.statusSync
 * @Date 2021/10/20 13:27
 */
@Component
public class BuyorderIsFinanceAlreadyStatus extends AbstractDataByTimeSync {

    @Resource
    BuyorderDataMapper buyorderDataMapper;

    @Override
    public List<Map<String, Object>> loadBizByTimeData(Long startTime, Long endTime) {
        return buyorderDataMapper.getOrderIsFinanceAlreadyByTime(startTime, endTime);
    }


    @Override
    public void updateData(List<Map<String, Object>> dataList) {
        if (CollectionUtils.isNotEmpty(dataList)) {
            List<BuyorderDataDto> buyorderDataDtos = new ArrayList<>();
            //获取财务人员姓名集合
            List<String> financeUserNameList = buyorderDataMapper.getFinanceUserNameList();
            if (CollectionUtils.isNotEmpty(financeUserNameList)){
                long nowTime = System.currentTimeMillis();
                dataList.forEach(item -> {
                    BuyorderDataDto buyorderDataDto = new BuyorderDataDto();
                    Integer buyorderId = Integer.valueOf(item.get("buyorderId").toString());
                    makeExist(buyorderId, nowTime);
                    buyorderDataDto.setBuyorderId(buyorderId);
                    String userName = item.get("userName").toString();
                    boolean contains = financeUserNameList.stream().anyMatch(userName::equalsIgnoreCase);
                    if (contains) {
                        buyorderDataDto.setIsFinanceAlreadyStatus(1);
                    } else {
                        buyorderDataDto.setIsFinanceAlreadyStatus(0);
                    }
                    buyorderDataDtos.add(buyorderDataDto);
                });
                buyorderDataMapper.updateOrderIsFinanceAlreadyByTime(buyorderDataDtos, nowTime);
            }
        }
    }

    @Override
    public void makeExist(Integer id, Long nowTime) {
        int buyorderDataId = buyorderDataMapper.getBuyorderDataById(id);
        if (buyorderDataId <= 0) {
            buyorderDataMapper.insertBuyorderData(id, nowTime);
        }
    }
}
