package com.vedeng.goods.service.impl;

import com.vedeng.goods.domain.entity.AfterSaleSupplyPolicy;
import com.vedeng.goods.dto.AfterSaleSupplyPolicyDto;
import com.vedeng.goods.mapstruct.AfterSaleSupplyPolicyConvert;
import com.vedeng.goods.service.AfterSaleSupplyPolicyApiService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import com.vedeng.goods.mapper.GoodsAfterSaleSupplyPolicyMapper;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
public class AfterSaleSupplyPolicyApiServiceImpl implements AfterSaleSupplyPolicyApiService {

    @Resource
    private GoodsAfterSaleSupplyPolicyMapper goodsAfterSaleSupplyPolicyMapper;

    @Resource
    private AfterSaleSupplyPolicyConvert afterSaleSupplyPolicyConvert;

    @Override
    public AfterSaleSupplyPolicyDto queryBySkuNoAndTraderId(Integer traderId, String skuNo) {
        List<AfterSaleSupplyPolicy> list = goodsAfterSaleSupplyPolicyMapper.findBySkuNo(traderId.longValue(), skuNo);
        if (CollectionUtils.isEmpty(list)){
            return null;
        }
        List<AfterSaleSupplyPolicyDto> dto = afterSaleSupplyPolicyConvert.toDto(list);
        AfterSaleSupplyPolicyDto afterSaleSupplyPolicyDto = dto.get(0);
        return afterSaleSupplyPolicyDto;
    }
}
