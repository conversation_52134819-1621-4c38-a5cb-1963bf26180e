package com.vedeng.erp.kingdee.batch.writer;

import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeeFileDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeFileCommandConvertor;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeReceiveBillMapper;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 推送回单信息
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class BankBillFileWriter extends BaseWriter<KingDeeFileDto> {

    @Autowired
    private KingDeeFileCommandConvertor kingDeeFileCommandConvertor;

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private KingDeeReceiveBillMapper kingDeeReceiveBillMapper;

    @Override
    public void doWrite(KingDeeFileDto item, JobParameters params, ExecutionContext stepContext) throws Exception {
//        log.info("收款单-推送金蝶附件信息{},", JSON.toJSONString(item));
//        KingDeeFileCommand kingDeeFileCommand = kingDeeFileCommandConvertor.toCommand(item);
//        RepoRet fileResult = kingDeeBaseApi.attachmentUpload(kingDeeFileCommand);
//        if (fileResult == null) {
//            log.info("收款单-回单上传金蝶失败");
//            throw new ServiceException("收款单-回单上传失败");
//        } else {
//            if (fileResult.getResult().getResponseStatus().isIsSuccess()) {
//                //收到成功保存附件信息
//                log.info("收款单-回单保存成功{},", JSON.toJSONString(fileResult));
//                //更新附件是否上传成功信息
//                kingDeeReceiveBillMapper.updatePushStatus(Integer.parseInt(stepContext.get("kingDeeReceiveBillId").toString()));
//            } else {
//                log.info("收款单-回单上传金蝶失败,金蝶反馈{},", JSON.toJSONString(fileResult));
//                throw new ServiceException("收款单-回单上传失败");
//            }
//        }
    }
}
