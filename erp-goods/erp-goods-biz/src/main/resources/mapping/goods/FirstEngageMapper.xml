<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.mapper.FirstEngageMapper">

    <resultMap id="BaseResultMap" type="com.vedeng.goods.domain.entity.FirstEngage">
        <id property="firstEngageId" column="FIRST_ENGAGE_ID" jdbcType="INTEGER"/>
        <result property="registrationNumberId" column="REGISTRATION_NUMBER_ID" jdbcType="INTEGER"/>
        <result property="goodsType" column="GOODS_TYPE" jdbcType="INTEGER"/>
        <result property="brandType" column="BRAND_TYPE" jdbcType="BOOLEAN"/>
        <result property="standardCategoryType" column="STANDARD_CATEGORY_TYPE" jdbcType="BOOLEAN"/>
        <result property="newStandardCategoryId" column="NEW_STANDARD_CATEGORY_ID" jdbcType="INTEGER"/>
        <result property="oldStandardCategoryId" column="OLD_STANDARD_CATEGORY_ID" jdbcType="INTEGER"/>
        <result property="effectiveDayUnit" column="EFFECTIVE_DAY_UNIT" jdbcType="BOOLEAN"/>
        <result property="effectiveDays" column="EFFECTIVE_DAYS" jdbcType="INTEGER"/>
        <result property="sortDays" column="SORT_DAYS" jdbcType="INTEGER"/>
        <result property="conditionOne" column="CONDITION_ONE" jdbcType="INTEGER"/>
        <result property="checkAgain" column="CHECK_AGAIN" jdbcType="BOOLEAN"/>
        <result property="status" column="STATUS" jdbcType="BOOLEAN"/>
        <result property="signature" column="SIGNATURE" jdbcType="BOOLEAN"/>
        <result property="isDeleted" column="IS_DELETED" jdbcType="BOOLEAN"/>
        <result property="addTime" column="ADD_TIME" jdbcType="BIGINT"/>
        <result property="creator" column="CREATOR" jdbcType="INTEGER"/>
        <result property="modTime" column="MOD_TIME" jdbcType="BIGINT"/>
        <result property="updater" column="UPDATER" jdbcType="INTEGER"/>
        <result property="comments" column="COMMENTS" jdbcType="VARCHAR"/>
        <result property="temperature" column="TEMPERATURE" jdbcType="VARCHAR"/>
        <result property="storageConditionTemperature" column="STORAGE_CONDITION_TEMPERATURE" jdbcType="BOOLEAN"/>
        <result property="storageConditionTemperatureLowerValue" column="STORAGE_CONDITION_TEMPERATURE_LOWER_VALUE"
                jdbcType="FLOAT"/>
        <result property="storageConditionTemperatureUpperValue" column="STORAGE_CONDITION_TEMPERATURE_UPPER_VALUE"
                jdbcType="FLOAT"/>
        <result property="storageConditionHumidityLowerValue" column="STORAGE_CONDITION_HUMIDITY_LOWER_VALUE"
                jdbcType="FLOAT"/>
        <result property="storageConditionHumidityUpperValue" column="STORAGE_CONDITION_HUMIDITY_UPPER_VALUE"
                jdbcType="FLOAT"/>
        <result property="storageConditionOthers" column="STORAGE_CONDITION_OTHERS" jdbcType="VARCHAR"/>
        <result property="modifiedProperty" column="MODIFIED_PROPERTY" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        FIRST_ENGAGE_ID,REGISTRATION_NUMBER_ID,GOODS_TYPE,
        BRAND_TYPE,STANDARD_CATEGORY_TYPE,NEW_STANDARD_CATEGORY_ID,
        OLD_STANDARD_CATEGORY_ID,EFFECTIVE_DAY_UNIT,EFFECTIVE_DAYS,
        SORT_DAYS,CONDITION_ONE,CHECK_AGAIN,
        STATUS,SIGNATURE,IS_DELETED,
        ADD_TIME,CREATOR,MOD_TIME,
        UPDATER,COMMENTS,TEMPERATURE,
        STORAGE_CONDITION_TEMPERATURE,STORAGE_CONDITION_TEMPERATURE_LOWER_VALUE,STORAGE_CONDITION_TEMPERATURE_UPPER_VALUE,
        STORAGE_CONDITION_HUMIDITY_LOWER_VALUE,STORAGE_CONDITION_HUMIDITY_UPPER_VALUE,STORAGE_CONDITION_OTHERS,
        MODIFIED_PROPERTY
    </sql>
</mapper>
