package com.vedeng.erp.kingdee.domain.entity;

import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseEntity;
import com.vedeng.common.mybatis.jbatis.annotation.Column;
import com.vedeng.common.mybatis.jbatis.annotation.GeneratedValue;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.common.mybatis.jbatis.annotation.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
* 采购退料单
*/
@Getter
@Setter
@ToString
@Table(name = "KING_DEE_PURCHASE_BACK")
public class KingDeePurchaseBackEntity extends BaseEntity {
    /**
    * id
    */
    @Id
    @GeneratedValue(generator = "JDBC")
    private String id;

    /**
     * 0：表示新增 非0：云星空系统单据FID值，表示修改
     */
    private String fId;

    /**
     * 单据编号 填写单据编号，若为空则调用系统的编码规则生成
     */
    private String fBillNo;

    /**
     * 贝登单据头ID 贝登单据头ID号（预留）
     */
    private String fQzokBddjtId;

    /**
     * 单据日期 填单据日期，格式yyyy-MM-dd
     */
    private String fDate;

    /**
     * 退料类型 默认：B
     */
    private String fMrType;

    /**
     * 退料方式 默认：B
     */
    private String fMrMode;

    /**
     * 业务类型
     */
    private String fBusinessType;

    /**
     * 单据类型  填单据类型编码，默认填TLD01_SYS
     */
    private String fBillTypeId;

    /**
     * 退料组织 填写组织编码
     */
    private String fStockOrgId;

    /**
     * 供应商 填写供应商编码
     */
    private String fSupplierId;

    /**
    * 退料数量 填写实际的退料数量 FPURMRBENTRY_Link为此数组总计退货数量
    */
    @Column(jdbcType = "VARCHAR")
    private JSONArray fpurmrbentry;
}