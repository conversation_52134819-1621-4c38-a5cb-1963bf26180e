package com.vedeng.goods.model;

public class VerifiesInfoGenerate {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_VERIFIES_INFO.VERIFIES_INFO_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer verifiesInfoId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_VERIFIES_INFO.RELATE_TABLE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String relateTable;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_VERIFIES_INFO.RELATE_TABLE_KEY
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer relateTableKey;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_VERIFIES_INFO.VERIFIES_TYPE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer verifiesType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_VERIFIES_INFO.LAST_VERIFY_USERNAME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String lastVerifyUsername;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_VERIFIES_INFO.VERIFY_USERNAME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String verifyUsername;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_VERIFIES_INFO.STATUS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_VERIFIES_INFO.ADD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Long addTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_VERIFIES_INFO.MOD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Long modTime;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_VERIFIES_INFO.VERIFIES_INFO_ID
     *
     * @return the value of T_VERIFIES_INFO.VERIFIES_INFO_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getVerifiesInfoId() {
        return verifiesInfoId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_VERIFIES_INFO.VERIFIES_INFO_ID
     *
     * @param verifiesInfoId the value for T_VERIFIES_INFO.VERIFIES_INFO_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setVerifiesInfoId(Integer verifiesInfoId) {
        this.verifiesInfoId = verifiesInfoId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_VERIFIES_INFO.RELATE_TABLE
     *
     * @return the value of T_VERIFIES_INFO.RELATE_TABLE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getRelateTable() {
        return relateTable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_VERIFIES_INFO.RELATE_TABLE
     *
     * @param relateTable the value for T_VERIFIES_INFO.RELATE_TABLE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setRelateTable(String relateTable) {
        this.relateTable = relateTable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_VERIFIES_INFO.RELATE_TABLE_KEY
     *
     * @return the value of T_VERIFIES_INFO.RELATE_TABLE_KEY
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getRelateTableKey() {
        return relateTableKey;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_VERIFIES_INFO.RELATE_TABLE_KEY
     *
     * @param relateTableKey the value for T_VERIFIES_INFO.RELATE_TABLE_KEY
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setRelateTableKey(Integer relateTableKey) {
        this.relateTableKey = relateTableKey;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_VERIFIES_INFO.VERIFIES_TYPE
     *
     * @return the value of T_VERIFIES_INFO.VERIFIES_TYPE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getVerifiesType() {
        return verifiesType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_VERIFIES_INFO.VERIFIES_TYPE
     *
     * @param verifiesType the value for T_VERIFIES_INFO.VERIFIES_TYPE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setVerifiesType(Integer verifiesType) {
        this.verifiesType = verifiesType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_VERIFIES_INFO.LAST_VERIFY_USERNAME
     *
     * @return the value of T_VERIFIES_INFO.LAST_VERIFY_USERNAME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getLastVerifyUsername() {
        return lastVerifyUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_VERIFIES_INFO.LAST_VERIFY_USERNAME
     *
     * @param lastVerifyUsername the value for T_VERIFIES_INFO.LAST_VERIFY_USERNAME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setLastVerifyUsername(String lastVerifyUsername) {
        this.lastVerifyUsername = lastVerifyUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_VERIFIES_INFO.VERIFY_USERNAME
     *
     * @return the value of T_VERIFIES_INFO.VERIFY_USERNAME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getVerifyUsername() {
        return verifyUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_VERIFIES_INFO.VERIFY_USERNAME
     *
     * @param verifyUsername the value for T_VERIFIES_INFO.VERIFY_USERNAME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setVerifyUsername(String verifyUsername) {
        this.verifyUsername = verifyUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_VERIFIES_INFO.STATUS
     *
     * @return the value of T_VERIFIES_INFO.STATUS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_VERIFIES_INFO.STATUS
     *
     * @param status the value for T_VERIFIES_INFO.STATUS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_VERIFIES_INFO.ADD_TIME
     *
     * @return the value of T_VERIFIES_INFO.ADD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Long getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_VERIFIES_INFO.ADD_TIME
     *
     * @param addTime the value for T_VERIFIES_INFO.ADD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_VERIFIES_INFO.MOD_TIME
     *
     * @return the value of T_VERIFIES_INFO.MOD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Long getModTime() {
        return modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_VERIFIES_INFO.MOD_TIME
     *
     * @param modTime the value for T_VERIFIES_INFO.MOD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setModTime(Long modTime) {
        this.modTime = modTime;
    }
}