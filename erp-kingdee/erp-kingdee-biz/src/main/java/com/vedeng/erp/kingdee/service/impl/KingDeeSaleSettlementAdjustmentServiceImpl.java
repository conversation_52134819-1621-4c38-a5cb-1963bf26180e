package com.vedeng.erp.kingdee.service.impl;

import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeeSaleSettlementAdjustmentCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeSaleSettlementAdjustmentEntity;
import com.vedeng.erp.kingdee.dto.KingDeeSaleSettlementAdjustmentDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeSaleSettlementAdjustmentCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeSaleSettlementAdjustmentConvertor;
import com.vedeng.erp.kingdee.repository.KingDeeSaleSettlementAdjustmentRepository;
import com.vedeng.erp.kingdee.service.KingDeeSaleSettlementAdjustmentApiService;
import com.vedeng.erp.kingdee.service.KingDeeSaleSettlementAdjustmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @description:
 * @author: Suqin
 * @date: 2023/3/7 10:31
 **/
@Slf4j
@Service
public class KingDeeSaleSettlementAdjustmentServiceImpl extends KingDeeBaseServiceImpl<
        KingDeeSaleSettlementAdjustmentEntity,
        KingDeeSaleSettlementAdjustmentDto,
        KingDeeSaleSettlementAdjustmentCommand,
        KingDeeSaleSettlementAdjustmentRepository,
        KingDeeSaleSettlementAdjustmentConvertor,
        KingDeeSaleSettlementAdjustmentCommandConvertor>
        implements KingDeeSaleSettlementAdjustmentService, KingDeeSaleSettlementAdjustmentApiService {
}
