package com.vedeng.order.model.dto;

import com.vedeng.order.model.query.LabelQuery;
import com.vedeng.order.model.vo.SkuVo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description:
 * @Author:       davis
 * @Date:         2021/4/16 上午10:44
 * @Version:      1.0
 */
@Data
public class LabelQueryDto implements Serializable {
    private static final long serialVersionUID = 1L;

    protected Integer id;

    protected Integer parentId;

    /**
     * 展示名称
     */
    private String label;

    /**
     * 是否选中
     */
    private Boolean selected;

    /**
     * 是否需要展示原因框
     */
    private Boolean needReason;

    /**
     * 是否需要展示日期框
     */
    private Boolean needDate;

    /**
     * 是否禁用
     */
    private Boolean disabled;

    /**
     * 产品数据源
     */
    private List<SkuVo> skuList;

    /**
     * 日期
     */
    private String date;

    /**
     * 原因
     */
    private String reason;

    /**
     * 子组件
     */
    private List<LabelQueryDto> child;
}
