package com.vedeng.erp.saleorder.service;

import java.math.BigDecimal;
import java.util.Map;

public interface NewSaleOrderService {

    /**
     * 获取产品价格信息
     *
     * @param saleorderId
     * @param skuNo
     * @param price
     * @return
     */
    Map<String, String> getSaleOrderGoodsPrice(Integer saleorderId, String skuNo, BigDecimal price);

    /**
     * 获取产品价格
     *
     * @param traderId
     * @param skuNo
     * @param price
     * @return
     */
    Map<String, String> getPriceCenterBySKU(Integer traderId, String skuNo, BigDecimal price);




}
