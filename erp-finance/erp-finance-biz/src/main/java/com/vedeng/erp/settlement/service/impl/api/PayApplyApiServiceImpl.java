package com.vedeng.erp.settlement.service.impl.api;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.aftersale.dto.AfterSalesDto;
import com.vedeng.erp.aftersale.service.AfterSalesApiService;
import com.vedeng.erp.buyorder.dto.BuyOrderApiDto;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseDto;
import com.vedeng.erp.buyorder.service.BuyorderApiService;
import com.vedeng.erp.buyorder.service.BuyorderExpenseApiService;
import com.vedeng.erp.finance.constants.FinanceConstant;
import com.vedeng.erp.finance.domain.entity.AutoPayConfig;
import com.vedeng.erp.finance.dto.AutoPayConfigDto;
import com.vedeng.erp.finance.dto.PayApplyCreateBillDto;
import com.vedeng.erp.finance.dto.PayApplyDto;
import com.vedeng.erp.finance.mapper.AutoPayConfigMapper;
import com.vedeng.erp.finance.mapstruct.PayApplyConvertor;
import com.vedeng.erp.finance.mapstruct.PayApplyCreateBillConvertor;
import com.vedeng.erp.finance.mapstruct.PayApplyDetailConvertor;
import com.vedeng.erp.finance.service.PayApplyApiService;
import com.vedeng.erp.settlement.domain.entity.PayApplyDetailEntity;
import com.vedeng.erp.settlement.domain.entity.PayApplyEntity;
import com.vedeng.erp.settlement.mapper.PayApplyDetailMapper;
import com.vedeng.erp.settlement.mapper.PayApplyMapper;
import com.vedeng.erp.system.dto.SysOptionDefinitionDto;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.SysOptionDefinitionApiService;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.system.service.UserWorkApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 接口实现
 * @date 2022/8/26 17:12
 **/
@Service
@Slf4j
public class PayApplyApiServiceImpl implements PayApplyApiService {

    @Autowired
    private PayApplyMapper payApplyMapper;

    @Autowired
    private PayApplyDetailMapper payApplyDetailMapper;

    @Autowired
    private UserApiService userApiService;

    @Autowired
    private SysOptionDefinitionApiService sysOptionDefinitionApiService;

    @Autowired
    private PayApplyConvertor payApplyConvertor;

    @Autowired
    private PayApplyCreateBillConvertor payApplyCreateBillConvertor;

    @Autowired
    private PayApplyDetailConvertor payApplyDetailConvertor;

    @Autowired
    private AfterSalesApiService afterSalesApiService;
    
    @Autowired
    private BuyorderApiService buyorderApiService;
    
    @Autowired
    private BuyorderExpenseApiService buyorderExpenseApiService;

    @Autowired
    private UserWorkApiService userWorkApiService;

    @Autowired
    private AutoPayConfigMapper autoPayConfigMapper;

    private static final String passMsg = "<font color=\"warning\">** 付款申请通过 **</font>\n您申请的订单%s付款已通过";

    private static final String rejectMsg = "<font color=\"warning\">** 付款申请驳回 **</font>\n您申请的订单%s付款已驳回\n驳回原因：%s";
    
    


    @Override
    public List<PayApplyDto> getPayAppyListByParam(PayApplyDto payApplyDto) {

        List<PayApplyDto> payApplyDtos = payApplyMapper.selectByPayTypeAndRelatedIdAndOtherParam(payApplyDto);

        if (CollectionUtil.isNotEmpty(payApplyDtos)) {
            Set<Integer> users = new HashSet<>();
            payApplyDtos.forEach(c->{
                if (Objects.nonNull(c.getCreator())) {
                    users.add(c.getCreator());
                }
                if (Objects.nonNull(c.getUpdater())) {
                    users.add(c.getUpdater());
                }
            });

            if (users.size() > 0) {
                List<UserDto> userInfoByUserIds = userApiService.getUserInfoByUserIds(new ArrayList<>(users));
                if (CollectionUtil.isNotEmpty(userInfoByUserIds)) {
                    Map<Integer, String> userDtoMap = userInfoByUserIds.stream().collect(Collectors.toMap(UserDto::getUserId, UserDto::getUsername));

                    for (PayApplyDto payApply : payApplyDtos) {
                        payApply.setCreatorName(userDtoMap.get(payApply.getCreator()));
                        payApply.setUpdaterName(userDtoMap.get(payApply.getUpdater()));
                    }
                }
            }


            Set<Integer> traderModes = new HashSet<>();
            payApplyDtos.forEach(c->{
                if (Objects.nonNull(c.getTraderMode())) {
                    traderModes.add(c.getTraderMode());
                }

            });

            if (traderModes.size() > 0) {
                List<SysOptionDefinitionDto> byIds = sysOptionDefinitionApiService.getByIds(new ArrayList<>(traderModes));
                if (CollectionUtil.isNotEmpty(byIds)) {
                    Map<Integer, String> dtoMap = byIds.stream().collect(Collectors.toMap(SysOptionDefinitionDto::getSysOptionDefinitionId, SysOptionDefinitionDto::getTitle));

                    for (PayApplyDto payApply : payApplyDtos) {
                        payApply.setTraderModeStr(dtoMap.get(payApply.getTraderMode()));
                    }
                }
            }
        }

        return payApplyDtos;
    }

    @Override
    public List<PayApplyCreateBillDto> getPayApplyByDto(PayApplyCreateBillDto payApplyCreateBillDto) {
        // dto 转 entity
        List<PayApplyEntity> payApplyEntities = payApplyMapper.queryByAll(payApplyCreateBillDto);
        return payApplyCreateBillConvertor.toDto(payApplyEntities);
    }

    @Override
    public PayApplyDto queryInfoByPayApplyId(Integer payApplyId) {
        return payApplyMapper.queryInfoByPayApplyId(payApplyId);
    }

    @Override
    public void updatePayStatusBill(Integer payStatus, Integer payApplyId) {
        PayApplyEntity update = new PayApplyEntity();
        update.setPayApplyId(payApplyId);
        update.setPayStatus(payStatus);
        payApplyMapper.updateByPrimaryKeySelective(update);
    }

    @Override
    public void updateValidStatus(Integer validStatus, Integer payApplyId) {
        PayApplyEntity update = new PayApplyEntity();
        update.setPayApplyId(payApplyId);
        update.setValidStatus(validStatus);
        update.setModTime(System.currentTimeMillis());
        update.setValidTime(System.currentTimeMillis());
        update.setUpdater(CurrentUser.getCurrentUser().getId());
        payApplyMapper.updateByPrimaryKeySelective(update);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void addPayApply(PayApplyDto payApplyDto) {

        log.info("保存付款申请：{}", JSON.toJSONString(payApplyDto));
        PayApplyEntity payApplyEntity = payApplyConvertor.toEntity(payApplyDto);
        List<PayApplyDetailEntity> payApplyDetailEntities = payApplyDetailConvertor.toEntity(payApplyDto.getPayApplyDetailDtos());
        payApplyMapper.insertSelective(payApplyEntity);
        payApplyDto.setPayApplyId(payApplyEntity.getPayApplyId());
        if (CollUtil.isNotEmpty(payApplyDetailEntities)) {
            payApplyDetailEntities.forEach(c-> c.setPayApplyId(payApplyEntity.getPayApplyId()));
            payApplyDetailMapper.batchInsert(payApplyDetailEntities);
        }

    }

    @Override
    public Integer getPayApplyMaxRecord(Integer relatedId) {
        PayApplyDto payApplyMaxRecord = payApplyMapper.getPayApplyMaxRecord(relatedId);
        if (ObjectUtil.isNotNull(payApplyMaxRecord) && StringUtils.isNotBlank(payApplyMaxRecord.getTraderName())){
            //根据TraderName获取供应商Id
            return payApplyMapper.getTraderSupplierIdByTraderName(payApplyMaxRecord.getTraderName());
        }
        return null;
    }

    @Override
    public void updateAccountType(Integer payApplyId) {
        log.info("更新往来单位类型(0-客户,1-供应商),payApplyId:{}", payApplyId);
        int accountType = FinanceConstant.MINUS_ONE;
        PayApplyEntity payApplyEntity = payApplyMapper.selectByPrimaryKey(payApplyId);
        if (Objects.isNull(payApplyEntity)) {
            log.info("更新往来单位类型(0-客户,1-供应商),payApplyId:{},未查询到付款申请信息", payApplyId);
            return;
        }
        Integer payType = payApplyEntity.getPayType();
        if(FinanceConstant.ID_517.equals(payType)||FinanceConstant.ID_4125.equals(payType)) {
            accountType = FinanceConstant.SUPPLIER;
        } else if (FinanceConstant.ID_518.equals(payType)) {
            AfterSalesDto afterSalesDto = afterSalesApiService.getAfterSalesById(payApplyEntity.getRelatedId());
            if (Objects.isNull(afterSalesDto)) {
                log.info("更新往来单位类型(0-客户,1-供应商),payApplyId:{},未查询到售后信息", payApplyId);
                return;
            }
            Integer subjectType = afterSalesDto.getSubjectType();
            Integer type = afterSalesDto.getType();
            if (FinanceConstant.ID_537.equals(subjectType)) {
                accountType = FinanceConstant.SUPPLIER;
            } else if (FinanceConstant.ID_535.equals(subjectType)) {
                if (FinanceConstant.ID_541.equals(type) || FinanceConstant.ID_584.equals(type) ||
                        FinanceConstant.ID_4090.equals(type) || FinanceConstant.ID_4091.equals(type)) {
                    accountType = FinanceConstant.SUPPLIER;
                } else {
                    accountType = FinanceConstant.CUSTOMER;
                }
            }
        }
        if (FinanceConstant.MINUS_ONE.equals(accountType)) {
            log.info("更新往来单位类型(0-客户,1-供应商),payApplyId:{},未查询到对应关系的往来单位类型", payApplyId);
            return;
        }
        log.info("更新往来单位类型(0-客户,1-供应商),payApplyId:{},accountType:{}", payApplyId, accountType);
        payApplyMapper.updateAccountTypeByPayApplyId(accountType, payApplyId);
    }

    @Override
    public AutoPayConfigDto paymentAllocationInfo() {
        AutoPayConfig config = autoPayConfigMapper.findLastAutoPayConfig();
        if (Objects.isNull(config)) {
            throw new ServiceException("未找到付款配置");
        }
        List<Integer> supplierWhitelist = Arrays.stream(config.getSupplierWhitelist().split(",")).map(Integer::valueOf).collect(Collectors.toList());
        String[] payApplyTimeSplit = config.getPayApplyTime().split(",");
        String[] payBankTimeSplit = config.getPayBankTime().split(",");

        AutoPayConfigDto autoPayConfigDto = AutoPayConfigDto.builder()
                .autoPayConfigId(config.getAutoPayConfigId())
                .enableAutoPay(ErpConstant.ONE.equals(config.getEnableAutoPay()))
                .enableContractAudit(ErpConstant.ONE.equals(config.getEnableContractAudit()))
                .enableSupplierWhitelist(ErpConstant.ONE.equals(config.getEnableSupplierWhitelist()))
                .supplierWhitelist(supplierWhitelist)
                .enableKingDeeAutoAudit(ErpConstant.ONE.equals(config.getEnableKingDeeAutoAudit()))
                .payApplyTimeStart(payApplyTimeSplit[0])
                .payApplyTimeEnd(payApplyTimeSplit[1])
                .payLimit(config.getPayLimit())
                .payBankTimeStart(payBankTimeSplit[0])
                .payBankTimeEnd(payBankTimeSplit[1])
                .payBank(config.getPayBank()).build();
        log.info("查询付款配置信息:{}", JSON.toJSONString(autoPayConfigDto));
        return autoPayConfigDto;
    }

    @Override
    public void paymentAllocationSubmit(AutoPayConfigDto dto) {
        log.info("付款配置信息提交,入参：{}", JSON.toJSONString(dto));
        AutoPayConfig config = new AutoPayConfig();
        config.setAutoPayConfigId(dto.getAutoPayConfigId());
        config.setEnableAutoPay(dto.getEnableAutoPay() ? 1 : 0);
        config.setEnableContractAudit(dto.getEnableContractAudit() ? 1 : 0);
        config.setEnableSupplierWhitelist(dto.getEnableSupplierWhitelist() ? 1 : 0);
        config.setEnableKingDeeAutoAudit(dto.getEnableKingDeeAutoAudit() ? 1 : 0);
        config.setPayApplyTime(dto.getPayApplyTimeStart() + "," + dto.getPayApplyTimeEnd());
        config.setPayLimit(dto.getPayLimit());
        config.setPayBankTime(dto.getPayBankTimeStart() + "," + dto.getPayBankTimeEnd());
        autoPayConfigMapper.updateByPrimaryKeySelective(config);
    }

    @Override
    public void updateAutoBill(Integer payApplyId, Integer autoBill) {
        log.info("更新是否满足自动制单入参,payApplyId:{},autoBill:{}", payApplyId, autoBill);
        if (Objects.isNull(payApplyId) || Objects.isNull(autoBill)) {
            return;
        }
        payApplyMapper.updateAutoBill(payApplyId, autoBill);
    }

    @Override
    public PayApplyDto getByPayTypeAndRelatedIdLast(Integer payType, Integer relatedId) {
        log.info("根据付款类型和关联ID查询付款申请信息,payType:{},relatedId:{}", payType, relatedId);
        return payApplyMapper.getByPayTypeAndRelatedIdLast(payType, relatedId);
    }

    @Override
    public List<PayApplyDto> getListByPayTypeAndRelatedId(Integer payType, Integer relatedId) {
        return payApplyMapper.getListByPayTypeAndRelatedId(payType, relatedId);
    }
    
    @Override
    public List<PayApplyDto> findByPayTypeAndRelatedId(Integer payType, Integer relatedId) {
        return payApplyMapper.findByPayTypeAndRelatedId(payType,relatedId);
    }

    @Override
    public void sendWxMsgAfterAudit(Integer payApplyId, boolean flag, Integer userId) {
        log.info("付款申请审核结束发送企微消息，入参payApplyId{},flag:{},userId:{}", payApplyId, flag, userId);
        if (Objects.isNull(payApplyId) || Objects.isNull(userId)) {
            log.info("付款申请审核结束发送企微消息，入参异常");
            return;
        }
        PayApplyDto payApplyDto = payApplyMapper.queryInfoByPayApplyId(payApplyId);
        if (Objects.isNull(payApplyDto)) {
            log.info("付款申请审核结束发送企微消息，未查询到付款申请信息");
            return;
        }
        log.info("付款申请审核结束发送企微消息,payApplyDto:{}", JSON.toJSONString(payApplyDto));
        Integer payType = payApplyDto.getPayType();
        Integer relatedId = payApplyDto.getRelatedId();
        String orderNo = "";
        if (payType == 517) {
            BuyOrderApiDto buyOrderApiDto = buyorderApiService.getBuyorderByBuyorderId(relatedId);
            orderNo = buyOrderApiDto.getBuyorderNo();
        } else if (payType == 518) {
            AfterSalesDto afterSalesDto = afterSalesApiService.getAfterSalesById(relatedId);
            orderNo = afterSalesDto.getAfterSalesNo();
        } else if (payType == 4125) {
            BuyorderExpenseDto buyorderExpenseDto = buyorderExpenseApiService.getOrderMainData(relatedId);
            orderNo = buyorderExpenseDto.getBuyorderExpenseNo();
        }
        String content;
        if (flag){
            content = String.format(passMsg, orderNo);
        }else {
            content = String.format(rejectMsg, orderNo,payApplyDto.getValidComments());
        }
        if (!ErpConstant.ONE.equals(userId) && !ErpConstant.TWO.equals(userId)) {
            try {
                userWorkApiService.sendInvoiceMsg(userId, content);
            } catch (Exception e) {
                log.info("付款申请审核结束发送企微消息,发送通知失败：{}", userId, e);
            }
        }
    }

    @Override
    public PayApplyDto getPayApplyByPayType(Integer relatedId,Integer payType) {
        return  payApplyMapper.findByRelatedIdAndPayType(relatedId,payType);
    }

}
