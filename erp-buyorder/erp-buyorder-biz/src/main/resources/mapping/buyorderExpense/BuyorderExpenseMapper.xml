<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.buyorderexpense.mapper.BuyorderExpenseMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseEntity">
        <!--@mbg.generated-->
        <!--@Table T_BUYORDER_EXPENSE-->
        <id column="BUYORDER_EXPENSE_ID" jdbcType="INTEGER" property="buyorderExpenseId"/>
        <result column="BUYORDER_EXPENSE_NO" jdbcType="VARCHAR" property="buyorderExpenseNo"/>
        <result column="BUYORDER_NO" jdbcType="VARCHAR" property="buyorderNo"/>
        <result column="BUYORDER_ID" jdbcType="INTEGER" property="buyorderId"/>
        <result column="ORDER_TYPE" jdbcType="INTEGER" property="orderType"/>
        <result column="VALID_STATUS" jdbcType="INTEGER" property="validStatus"/>
        <result column="VALID_TIME" jdbcType="TIMESTAMP" property="validTime"/>
        <result column="STATUS" jdbcType="INTEGER" property="status"/>
        <result column="LOCKED_STATUS" jdbcType="INTEGER" property="lockedStatus"/>
        <result column="INVOICE_STATUS" jdbcType="INTEGER" property="invoiceStatus"/>
        <result column="PAYMENT_STATUS" jdbcType="INTEGER" property="paymentStatus"/>
        <result column="DELIVERY_STATUS" jdbcType="INTEGER" property="deliveryStatus"/>
        <result column="ARRIVAL_STATUS" jdbcType="INTEGER" property="arrivalStatus"/>
        <result column="SERVICE_STATUS" jdbcType="INTEGER" property="serviceStatus"/>
        <result column="AUDIT_STATUS" jdbcType="INTEGER" property="auditStatus"/>
        <result column="INVOICE_TIME" jdbcType="TIMESTAMP" property="invoiceTime"/>
        <result column="PAYMENT_TIME" jdbcType="TIMESTAMP" property="paymentTime"/>
        <result column="AUDIT_TIME" jdbcType="TIMESTAMP" property="auditTime"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName"/>
        <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete"/>
        <result column="BUSINESS_TYPE" jdbcType="INTEGER" property="businessType" />
        <result column="AFTER_SALES_NO" jdbcType="VARCHAR" property="afterSalesNo" />
        <result column="AFTER_SALES_ID" jdbcType="INTEGER" property="afterSalesId" />
        <result column="IS_RETURN_EARLY_WARN" jdbcType="INTEGER" property="isReturnEarlyWarn" />
        <result column="IS_AUTO" jdbcType="INTEGER" property="isAuto" />

    </resultMap>

    <resultMap id="buyorderExpenseAndDetail" type="com.vedeng.erp.buyorder.dto.BuyorderExpenseDto">
        <id column="BUYORDER_EXPENSE_ID" jdbcType="INTEGER" property="buyorderExpenseId"/>
        <result column="BUYORDER_EXPENSE_NO" jdbcType="VARCHAR" property="buyorderExpenseNo"/>
        <result column="BUYORDER_NO" jdbcType="VARCHAR" property="buyorderNo"/>
        <result column="BUYORDER_ID" jdbcType="INTEGER" property="buyorderId"/>
        <result column="ORDER_TYPE" jdbcType="INTEGER" property="orderType"/>
        <result column="VALID_STATUS" jdbcType="INTEGER" property="validStatus"/>
        <result column="VALID_TIME" jdbcType="TIMESTAMP" property="validTime"/>
        <result column="STATUS" jdbcType="INTEGER" property="status"/>
        <result column="LOCKED_STATUS" jdbcType="INTEGER" property="lockedStatus"/>
        <result column="INVOICE_STATUS" jdbcType="INTEGER" property="invoiceStatus"/>
        <result column="PAYMENT_STATUS" jdbcType="INTEGER" property="paymentStatus"/>
        <result column="DELIVERY_STATUS" jdbcType="INTEGER" property="deliveryStatus"/>
        <result column="ARRIVAL_STATUS" jdbcType="INTEGER" property="arrivalStatus"/>
        <result column="SERVICE_STATUS" jdbcType="INTEGER" property="serviceStatus"/>
        <result column="AUDIT_STATUS" jdbcType="INTEGER" property="auditStatus"/>
        <result column="INVOICE_TIME" jdbcType="TIMESTAMP" property="invoiceTime"/>
        <result column="PAYMENT_TIME" jdbcType="TIMESTAMP" property="paymentTime"/>
        <result column="AUDIT_TIME" jdbcType="TIMESTAMP" property="auditTime"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName"/>
        <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete"/>
        <result column="BUSINESS_TYPE" jdbcType="INTEGER" property="businessType" />
        <result column="AFTER_SALES_NO" jdbcType="VARCHAR" property="afterSalesNo" />
        <result column="AFTER_SALES_ID" jdbcType="INTEGER" property="afterSalesId" />
        <association property="buyorderExpenseDetailDto"
                     javaType="com.vedeng.erp.buyorder.dto.BuyorderExpenseDetailDto">
            <id column="BUYORDER_EXPENSE_DETAIL_ID" jdbcType="INTEGER" property="buyorderExpenseDetailId"/>
            <result column="BUYORDER_EXPENSE_ID" jdbcType="INTEGER" property="buyorderExpenseId"/>
            <result column="PAYMENT_TYPE" jdbcType="INTEGER" property="paymentType"/>
            <result column="PAYMENT_COMMENTS" jdbcType="VARCHAR" property="paymentComments"/>
            <result column="INVOICE_TYPE" jdbcType="INTEGER" property="invoiceType"/>
            <result column="INVOICE_COMMENTS" jdbcType="VARCHAR" property="invoiceComments"/>
            <result column="TOTAL_AMOUNT" jdbcType="DECIMAL" property="totalAmount"/>
            <result column="TRADER_ID" jdbcType="INTEGER" property="traderId"/>
            <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName"/>
            <result column="TRADER_CONTACT_ID" jdbcType="INTEGER" property="traderContactId"/>
            <result column="TRADER_CONTACT_NAME" jdbcType="VARCHAR" property="traderContactName"/>
            <result column="TRADER_CONTACT_MOBILE" jdbcType="VARCHAR" property="traderContactMobile"/>
            <result column="TRADER_CONTACT_TELEPHONE" jdbcType="VARCHAR" property="traderContactTelephone"/>
            <result column="TRADER_ADDRESS_ID" jdbcType="INTEGER" property="traderAddressId"/>
            <result column="TRADER_AREA" jdbcType="VARCHAR" property="traderArea"/>
            <result column="TRADER_ADDRESS" jdbcType="VARCHAR" property="traderAddress"/>
            <result column="TRADER_COMMENTS" jdbcType="VARCHAR" property="traderComments"/>
            <result column="PREPAID_AMOUNT" jdbcType="DECIMAL" property="prepaidAmount"/>
            <result column="ACCOUNT_PERIOD_AMOUNT" jdbcType="DECIMAL" property="accountPeriodAmount"/>
            <result column="PERIOD_DAY" jdbcType="INTEGER" property="periodDay"/>
            <result column="RETAINAGE_AMOUNT" jdbcType="DECIMAL" property="retainageAmount"/>
            <result column="RETAINAGE_AMOUNT_MONTH" jdbcType="INTEGER" property="retainageAmountMonth"/>
            <result column="EXPENSE_SOURCE" jdbcType="VARCHAR" property="expenseSource"/>
            <result column="ORG_ID" jdbcType="INTEGER" property="orgId"/>
            <result column="ORG_NAME" jdbcType="VARCHAR" property="orgName"/>
            <result column="CONTRACT_URL" jdbcType="VARCHAR" property="contractUrl"/>
            <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
            <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
            <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName"/>
            <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
            <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
            <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName"/>
            <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete"/>
            <result column="GRADE" jdbcType="INTEGER" property="traderGrade"/>
            <result column="ORDER_DESC" jdbcType="VARCHAR"
                    javaType="com.vedeng.erp.buyorder.dto.OrderRemarkDto"
                    property="orderDesc"
                    typeHandler="com.vedeng.common.mybatis.handler.CustomSqlJsonArrayHandler"/>
        </association>
    </resultMap>

    <resultMap id="BUYORDER_EXPENSE_DTO" type="com.vedeng.erp.buyorder.dto.BuyorderExpenseDto">
        <id column="BUYORDER_EXPENSE_ID" jdbcType="INTEGER" property="buyorderExpenseId"/>
        <result column="BUYORDER_EXPENSE_NO" jdbcType="VARCHAR" property="buyorderExpenseNo"/>
        <result column="BUYORDER_NO" jdbcType="VARCHAR" property="buyorderNo"/>
        <result column="BUYORDER_ID" jdbcType="INTEGER" property="buyorderId"/>
        <result column="ORDER_TYPE" jdbcType="INTEGER" property="orderType"/>
        <result column="VALID_STATUS" jdbcType="INTEGER" property="validStatus"/>
        <result column="VALID_TIME" jdbcType="TIMESTAMP" property="validTime"/>
        <result column="STATUS" jdbcType="INTEGER" property="status"/>
        <result column="LOCKED_STATUS" jdbcType="INTEGER" property="lockedStatus"/>
        <result column="INVOICE_STATUS" jdbcType="INTEGER" property="invoiceStatus"/>
        <result column="PAYMENT_STATUS" jdbcType="INTEGER" property="paymentStatus"/>
        <result column="DELIVERY_STATUS" jdbcType="INTEGER" property="deliveryStatus"/>
        <result column="ARRIVAL_STATUS" jdbcType="INTEGER" property="arrivalStatus"/>
        <result column="SERVICE_STATUS" jdbcType="INTEGER" property="serviceStatus"/>
        <result column="AUDIT_STATUS" jdbcType="INTEGER" property="auditStatus"/>
        <result column="INVOICE_TIME" jdbcType="TIMESTAMP" property="invoiceTime"/>
        <result column="PAYMENT_TIME" jdbcType="TIMESTAMP" property="paymentTime"/>
        <result column="AUDIT_TIME" jdbcType="TIMESTAMP" property="auditTime"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName"/>
        <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete"/>
        <result column="BUSINESS_TYPE" jdbcType="INTEGER" property="businessType" />
        <result column="AFTER_SALES_NO" jdbcType="VARCHAR" property="afterSalesNo" />
        <result column="AFTER_SALES_ID" jdbcType="INTEGER" property="afterSalesId" />
        <result column="IS_RETURN_EARLY_WARN" jdbcType="INTEGER" property="isReturnEarlyWarn" />
        <result column="IS_AUTO" jdbcType="INTEGER" property="isAuto" />
        <association property="buyorderExpenseDetailDto"
                     javaType="com.vedeng.erp.buyorder.dto.BuyorderExpenseDetailDto">
            <id column="BUYORDER_EXPENSE_DETAIL_ID" jdbcType="INTEGER" property="buyorderExpenseDetailId"/>
            <result column="BUYORDER_EXPENSE_ID" jdbcType="INTEGER" property="buyorderExpenseId"/>
            <result column="PAYMENT_TYPE" jdbcType="INTEGER" property="paymentType"/>
            <result column="PAYMENT_COMMENTS" jdbcType="VARCHAR" property="paymentComments"/>
            <result column="INVOICE_TYPE" jdbcType="INTEGER" property="invoiceType"/>
            <result column="INVOICE_COMMENTS" jdbcType="VARCHAR" property="invoiceComments"/>
            <result column="TOTAL_AMOUNT" jdbcType="DECIMAL" property="totalAmount"/>
            <result column="TRADER_ID" jdbcType="INTEGER" property="traderId"/>
            <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName"/>
            <result column="TRADER_CONTACT_ID" jdbcType="INTEGER" property="traderContactId"/>
            <result column="TRADER_CONTACT_NAME" jdbcType="VARCHAR" property="traderContactName"/>
            <result column="TRADER_CONTACT_MOBILE" jdbcType="VARCHAR" property="traderContactMobile"/>
            <result column="TRADER_CONTACT_TELEPHONE" jdbcType="VARCHAR" property="traderContactTelephone"/>
            <result column="TRADER_ADDRESS_ID" jdbcType="INTEGER" property="traderAddressId"/>
            <result column="TRADER_AREA" jdbcType="VARCHAR" property="traderArea"/>
            <result column="TRADER_ADDRESS" jdbcType="VARCHAR" property="traderAddress"/>
            <result column="TRADER_COMMENTS" jdbcType="VARCHAR" property="traderComments"/>
            <result column="PREPAID_AMOUNT" jdbcType="DECIMAL" property="prepaidAmount"/>
            <result column="ACCOUNT_PERIOD_AMOUNT" jdbcType="DECIMAL" property="accountPeriodAmount"/>
            <result column="PERIOD_DAY" jdbcType="INTEGER" property="periodDay"/>
            <result column="RETAINAGE_AMOUNT" jdbcType="DECIMAL" property="retainageAmount"/>
            <result column="RETAINAGE_AMOUNT_MONTH" jdbcType="INTEGER" property="retainageAmountMonth"/>
            <result column="EXPENSE_SOURCE" jdbcType="VARCHAR" property="expenseSource"/>
            <result column="ORG_ID" jdbcType="INTEGER" property="orgId"/>
            <result column="CONTRACT_URL" jdbcType="VARCHAR" property="contractUrl"/>
            <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
            <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
            <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName"/>
            <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
            <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
            <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName"/>
            <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete"/>
            <result column="ORDER_DESC" jdbcType="VARCHAR"
                    javaType="com.vedeng.erp.buyorder.dto.OrderRemarkDto"
                    property="orderDesc"
                    typeHandler="com.vedeng.common.mybatis.handler.CustomSqlJsonArrayHandler"/>
        </association>
        <collection property="buyorderExpenseItemDtos" javaType="java.util.List"
                    ofType="com.vedeng.erp.buyorder.dto.BuyorderExpenseItemDto">
            <id column="BUYORDER_EXPENSE_ITEM_ID" jdbcType="INTEGER" property="buyorderExpenseItemId"/>
            <result column="BUYORDER_EXPENSE_ID" jdbcType="INTEGER" property="buyorderExpenseId"/>
            <result column="ARRIVAL_STATUS" jdbcType="INTEGER" property="arrivalStatus"/>
            <result column="ARRIVAL_TIME" jdbcType="TIMESTAMP" property="arrivalTime"/>
            <result column="DELIVERY_STATUS" jdbcType="INTEGER" property="deliveryStatus"/>
            <result column="DELIVERY_TIME" jdbcType="TIMESTAMP" property="deliveryTime"/>
            <result column="INVOICE_STATUS" jdbcType="INTEGER" property="invoiceStatus"/>
            <result column="INVOICE_TIME" jdbcType="TIMESTAMP" property="invoiceTime"/>
            <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId"/>
            <result column="NUM" jdbcType="INTEGER" property="num"/>
            <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete"/>
            <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
            <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
            <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName"/>
            <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
            <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
            <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName"/>
            <association property="buyorderExpenseItemDetailDto"
                         javaType="com.vedeng.erp.buyorder.dto.BuyorderExpenseItemDetailDto">
                <id column="BUYORDER_EXPENSE_ITEM_DETAIL_ID" jdbcType="INTEGER" property="buyorderExpenseItemDetailId"/>
                <result column="BUYORDER_EXPENSE_ITEM_ID" jdbcType="INTEGER" property="buyorderExpenseItemId"/>
                <result column="SKU" jdbcType="VARCHAR" property="sku"/>
                <result column="GOODS_NAME" jdbcType="VARCHAR" property="goodsName"/>
                <result column="BRAND_NAME" jdbcType="VARCHAR" property="brandName"/>
                <result column="MODEL" jdbcType="VARCHAR" property="model"/>
                <result column="UNIT_NAME" jdbcType="VARCHAR" property="unitName"/>
                <result column="PRICE" jdbcType="DECIMAL" property="price"/>
                <result column="CURRENCY_UNIT_ID" jdbcType="INTEGER" property="currencyUnitId"/>
                <result column="EXPENSE_CATEGORY_ID" jdbcType="INTEGER" property="expenseCategoryId"/>
                <result column="EXPENSE_CATEGORY_NAME" jdbcType="VARCHAR" property="expenseCategoryName"/>
                <result column="HAVE_STOCK_MANAGE" jdbcType="INTEGER" property="haveStockManage"/>
                <result column="INSIDE_COMMENTS" jdbcType="VARCHAR" property="insideComments"/>
                <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete"/>
            </association>
        </collection>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        BUYORDER_EXPENSE_ID,
        BUYORDER_EXPENSE_NO,
        BUYORDER_NO,
        BUYORDER_ID,
        ORDER_TYPE,
        VALID_STATUS,
        VALID_TIME,
        `STATUS`,
        LOCKED_STATUS,
        INVOICE_STATUS,
        PAYMENT_STATUS,
        DELIVERY_STATUS,
        ARRIVAL_STATUS,
        SERVICE_STATUS,
        AUDIT_STATUS,
        INVOICE_TIME,
        PAYMENT_TIME,
        AUDIT_TIME,
        ADD_TIME,
        CREATOR,
        CREATOR_NAME,
        MOD_TIME,
        UPDATER,
        UPDATER_NAME,
        IS_DELETE,
        BUSINESS_TYPE, AFTER_SALES_NO, AFTER_SALES_ID, IS_RETURN_EARLY_WARN, IS_AUTO
</sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from T_BUYORDER_EXPENSE
        where BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete
        from T_BUYORDER_EXPENSE
        where BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="BUYORDER_EXPENSE_ID" keyProperty="buyorderExpenseId"
            parameterType="com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_BUYORDER_EXPENSE (BUYORDER_EXPENSE_NO, BUYORDER_NO, BUYORDER_ID,
                                        ORDER_TYPE, VALID_STATUS, VALID_TIME,
                                        `STATUS`, LOCKED_STATUS, INVOICE_STATUS,
                                        PAYMENT_STATUS, DELIVERY_STATUS, ARRIVAL_STATUS,
                                        SERVICE_STATUS, AUDIT_STATUS, INVOICE_TIME,
                                        PAYMENT_TIME, AUDIT_TIME, ADD_TIME,
                                        CREATOR, CREATOR_NAME, MOD_TIME,
                                        UPDATER, UPDATER_NAME, IS_DELETE,
        BUSINESS_TYPE, AFTER_SALES_NO, AFTER_SALES_ID, IS_RETURN_EARLY_WARN, IS_AUTO
        )
        values (#{buyorderExpenseNo,jdbcType=VARCHAR}, #{buyorderNo,jdbcType=VARCHAR}, #{buyorderId,jdbcType=INTEGER},
                #{orderType,jdbcType=INTEGER}, #{validStatus,jdbcType=INTEGER}, #{validTime,jdbcType=TIMESTAMP},
                #{status,jdbcType=INTEGER}, #{lockedStatus,jdbcType=INTEGER}, #{invoiceStatus,jdbcType=INTEGER},
                #{paymentStatus,jdbcType=INTEGER}, #{deliveryStatus,jdbcType=INTEGER},
                #{arrivalStatus,jdbcType=INTEGER},
                #{serviceStatus,jdbcType=INTEGER}, #{auditStatus,jdbcType=INTEGER}, #{invoiceTime,jdbcType=TIMESTAMP},
                #{paymentTime,jdbcType=TIMESTAMP}, #{auditTime,jdbcType=TIMESTAMP}, #{addTime,jdbcType=TIMESTAMP},
                #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{modTime,jdbcType=TIMESTAMP},
                #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR}, #{isDelete,jdbcType=INTEGER},
        #{businessType,jdbcType=INTEGER}, #{afterSalesNo,jdbcType=VARCHAR}, #{afterSalesId,jdbcType=INTEGER}
        ,#{isReturnEarlyWarn,jdbcType=INTEGER}, #{isAuto,jdbcType=INTEGER}
        )
    </insert>
    <insert id="insertSelective" keyColumn="BUYORDER_EXPENSE_ID" keyProperty="buyorderExpenseId"
            parameterType="com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_BUYORDER_EXPENSE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="buyorderExpenseNo != null">
                BUYORDER_EXPENSE_NO,
            </if>
            <if test="buyorderNo != null">
                BUYORDER_NO,
            </if>
            <if test="buyorderId != null">
                BUYORDER_ID,
            </if>
            <if test="orderType != null">
                ORDER_TYPE,
            </if>
            <if test="validStatus != null">
                VALID_STATUS,
            </if>
            <if test="validTime != null">
                VALID_TIME,
            </if>
            <if test="status != null">
                `STATUS`,
            </if>
            <if test="lockedStatus != null">
                LOCKED_STATUS,
            </if>
            <if test="invoiceStatus != null">
                INVOICE_STATUS,
            </if>
            <if test="paymentStatus != null">
                PAYMENT_STATUS,
            </if>
            <if test="deliveryStatus != null">
                DELIVERY_STATUS,
            </if>
            <if test="arrivalStatus != null">
                ARRIVAL_STATUS,
            </if>
            <if test="serviceStatus != null">
                SERVICE_STATUS,
            </if>
            <if test="auditStatus != null">
                AUDIT_STATUS,
            </if>
            <if test="invoiceTime != null">
                INVOICE_TIME,
            </if>
            <if test="paymentTime != null">
                PAYMENT_TIME,
            </if>
            <if test="auditTime != null">
                AUDIT_TIME,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="creatorName != null">
                CREATOR_NAME,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="updaterName != null">
                UPDATER_NAME,
            </if>
            <if test="isDelete != null">
                IS_DELETE,
            </if>
            <if test="businessType != null">
                BUSINESS_TYPE,
            </if>
            <if test="afterSalesNo != null">
                AFTER_SALES_NO,
            </if>
            <if test="afterSalesId != null">
                AFTER_SALES_ID,
            </if>
            <if test="isReturnEarlyWarn != null">
                IS_RETURN_EARLY_WARN,
            </if>
            <if test="isAuto != null">
                IS_AUTO,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="buyorderExpenseNo != null">
                #{buyorderExpenseNo,jdbcType=VARCHAR},
            </if>
            <if test="buyorderNo != null">
                #{buyorderNo,jdbcType=VARCHAR},
            </if>
            <if test="buyorderId != null">
                #{buyorderId,jdbcType=INTEGER},
            </if>
            <if test="orderType != null">
                #{orderType,jdbcType=INTEGER},
            </if>
            <if test="validStatus != null">
                #{validStatus,jdbcType=INTEGER},
            </if>
            <if test="validTime != null">
                #{validTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="lockedStatus != null">
                #{lockedStatus,jdbcType=INTEGER},
            </if>
            <if test="invoiceStatus != null">
                #{invoiceStatus,jdbcType=INTEGER},
            </if>
            <if test="paymentStatus != null">
                #{paymentStatus,jdbcType=INTEGER},
            </if>
            <if test="deliveryStatus != null">
                #{deliveryStatus,jdbcType=INTEGER},
            </if>
            <if test="arrivalStatus != null">
                #{arrivalStatus,jdbcType=INTEGER},
            </if>
            <if test="serviceStatus != null">
                #{serviceStatus,jdbcType=INTEGER},
            </if>
            <if test="auditStatus != null">
                #{auditStatus,jdbcType=INTEGER},
            </if>
            <if test="invoiceTime != null">
                #{invoiceTime,jdbcType=TIMESTAMP},
            </if>
            <if test="paymentTime != null">
                #{paymentTime,jdbcType=TIMESTAMP},
            </if>
            <if test="auditTime != null">
                #{auditTime,jdbcType=TIMESTAMP},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null">
                #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="updaterName != null">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="businessType != null">
                #{businessType,jdbcType=INTEGER},
            </if>
            <if test="afterSalesNo != null">
                #{afterSalesNo,jdbcType=VARCHAR},
            </if>
            <if test="afterSalesId != null">
                #{afterSalesId,jdbcType=INTEGER},
            </if>
            <if test="isReturnEarlyWarn != null">
                 #{isReturnEarlyWarn,jdbcType=INTEGER},
            </if>
            <if test="isAuto != null">
                 #{isAuto,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseEntity">
        <!--@mbg.generated-->
        update T_BUYORDER_EXPENSE
        <set>
            <if test="buyorderExpenseNo != null">
                BUYORDER_EXPENSE_NO = #{buyorderExpenseNo,jdbcType=VARCHAR},
            </if>
            <if test="buyorderNo != null">
                BUYORDER_NO = #{buyorderNo,jdbcType=VARCHAR},
            </if>
            <if test="buyorderId != null">
                BUYORDER_ID = #{buyorderId,jdbcType=INTEGER},
            </if>
            <if test="orderType != null">
                ORDER_TYPE = #{orderType,jdbcType=INTEGER},
            </if>
            <if test="validStatus != null">
                VALID_STATUS = #{validStatus,jdbcType=INTEGER},
            </if>
            <if test="validTime != null">
                VALID_TIME = #{validTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                `STATUS` = #{status,jdbcType=INTEGER},
            </if>
            <if test="lockedStatus != null">
                LOCKED_STATUS = #{lockedStatus,jdbcType=INTEGER},
            </if>
            <if test="invoiceStatus != null">
                INVOICE_STATUS = #{invoiceStatus,jdbcType=INTEGER},
            </if>
            <if test="paymentStatus != null">
                PAYMENT_STATUS = #{paymentStatus,jdbcType=INTEGER},
            </if>
            <if test="deliveryStatus != null">
                DELIVERY_STATUS = #{deliveryStatus,jdbcType=INTEGER},
            </if>
            <if test="arrivalStatus != null">
                ARRIVAL_STATUS = #{arrivalStatus,jdbcType=INTEGER},
            </if>
            <if test="serviceStatus != null">
                SERVICE_STATUS = #{serviceStatus,jdbcType=INTEGER},
            </if>
            <if test="auditStatus != null">
                AUDIT_STATUS = #{auditStatus,jdbcType=INTEGER},
            </if>
            <if test="invoiceTime != null">
                INVOICE_TIME = #{invoiceTime,jdbcType=TIMESTAMP},
            </if>
            <if test="paymentTime != null">
                PAYMENT_TIME = #{paymentTime,jdbcType=TIMESTAMP},
            </if>
            <if test="auditTime != null">
                AUDIT_TIME = #{auditTime,jdbcType=TIMESTAMP},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null">
                CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="updaterName != null">
                UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                IS_DELETE = #{isDelete,jdbcType=INTEGER} ,
            </if>
            <if test="businessType != null">
                BUSINESS_TYPE = #{businessType,jdbcType=INTEGER},
            </if>
            <if test="afterSalesNo != null">
                AFTER_SALES_NO = #{afterSalesNo,jdbcType=VARCHAR},
            </if>
            <if test="afterSalesId != null">
                AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
            </if>
            <if test="isReturnEarlyWarn != null">
                IS_RETURN_EARLY_WARN = #{isReturnEarlyWarn,jdbcType=INTEGER},
            </if>
            <if test="isAuto != null">
                IS_AUTO = #{isAuto,jdbcType=INTEGER},
            </if>
        </set>
        where BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseEntity">
        <!--@mbg.generated-->
        update T_BUYORDER_EXPENSE
        set BUYORDER_EXPENSE_NO = #{buyorderExpenseNo,jdbcType=VARCHAR},
            BUYORDER_NO         = #{buyorderNo,jdbcType=VARCHAR},
            BUYORDER_ID         = #{buyorderId,jdbcType=INTEGER},
            ORDER_TYPE          = #{orderType,jdbcType=INTEGER},
            VALID_STATUS        = #{validStatus,jdbcType=INTEGER},
            VALID_TIME          = #{validTime,jdbcType=TIMESTAMP},
            `STATUS`            = #{status,jdbcType=INTEGER},
            LOCKED_STATUS       = #{lockedStatus,jdbcType=INTEGER},
            INVOICE_STATUS      = #{invoiceStatus,jdbcType=INTEGER},
            PAYMENT_STATUS      = #{paymentStatus,jdbcType=INTEGER},
            DELIVERY_STATUS     = #{deliveryStatus,jdbcType=INTEGER},
            ARRIVAL_STATUS      = #{arrivalStatus,jdbcType=INTEGER},
            SERVICE_STATUS      = #{serviceStatus,jdbcType=INTEGER},
            AUDIT_STATUS        = #{auditStatus,jdbcType=INTEGER},
            INVOICE_TIME        = #{invoiceTime,jdbcType=TIMESTAMP},
            PAYMENT_TIME        = #{paymentTime,jdbcType=TIMESTAMP},
            AUDIT_TIME          = #{auditTime,jdbcType=TIMESTAMP},
            ADD_TIME            = #{addTime,jdbcType=TIMESTAMP},
            CREATOR             = #{creator,jdbcType=INTEGER},
            CREATOR_NAME        = #{creatorName,jdbcType=VARCHAR},
            MOD_TIME            = #{modTime,jdbcType=TIMESTAMP},
            UPDATER             = #{updater,jdbcType=INTEGER},
            UPDATER_NAME        = #{updaterName,jdbcType=VARCHAR},
            IS_DELETE           = #{isDelete,jdbcType=INTEGER},
            BUSINESS_TYPE = #{businessType,jdbcType=INTEGER},
            AFTER_SALES_NO = #{afterSalesNo,jdbcType=VARCHAR},
            AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
            IS_RETURN_EARLY_WARN = #{isReturnEarlyWarn,jdbcType=INTEGER},
            IS_AUTO = #{isAuto,jdbcType=INTEGER}
        where BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER}
    </update>
    <insert id="batchInsert" keyColumn="BUYORDER_EXPENSE_ID" keyProperty="buyorderExpenseId" parameterType="map"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_BUYORDER_EXPENSE
        (BUYORDER_EXPENSE_NO, BUYORDER_NO, BUYORDER_ID, ORDER_TYPE, VALID_STATUS, VALID_TIME,
         `STATUS`, LOCKED_STATUS, INVOICE_STATUS, PAYMENT_STATUS, DELIVERY_STATUS, ARRIVAL_STATUS,
         SERVICE_STATUS, AUDIT_STATUS, INVOICE_TIME, PAYMENT_TIME, AUDIT_TIME, ADD_TIME,
         CREATOR, CREATOR_NAME, MOD_TIME, UPDATER, UPDATER_NAME, IS_DELETE, BUSINESS_TYPE,
        AFTER_SALES_NO, AFTER_SALES_ID, IS_RETURN_EARLY_WARN, IS_AUTO)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.buyorderExpenseNo,jdbcType=VARCHAR}, #{item.buyorderNo,jdbcType=VARCHAR},
             #{item.buyorderId,jdbcType=INTEGER}, #{item.orderType,jdbcType=INTEGER},
             #{item.validStatus,jdbcType=INTEGER},
             #{item.validTime,jdbcType=TIMESTAMP}, #{item.status,jdbcType=INTEGER},
             #{item.lockedStatus,jdbcType=INTEGER},
             #{item.invoiceStatus,jdbcType=INTEGER}, #{item.paymentStatus,jdbcType=INTEGER},
             #{item.deliveryStatus,jdbcType=INTEGER}, #{item.arrivalStatus,jdbcType=INTEGER},
             #{item.serviceStatus,jdbcType=INTEGER}, #{item.auditStatus,jdbcType=INTEGER},
             #{item.invoiceTime,jdbcType=TIMESTAMP},
             #{item.paymentTime,jdbcType=TIMESTAMP}, #{item.auditTime,jdbcType=TIMESTAMP},
             #{item.addTime,jdbcType=TIMESTAMP},
             #{item.creator,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, #{item.modTime,jdbcType=TIMESTAMP},
             #{item.updater,jdbcType=INTEGER}, #{item.updaterName,jdbcType=VARCHAR}, #{item.isDelete,jdbcType=INTEGER},
            #{item.businessType,jdbcType=INTEGER}, #{item.afterSalesNo,jdbcType=VARCHAR}, #{item.afterSalesId,jdbcType=INTEGER},
            #{item.isReturnEarlyWarn,jdbcType=INTEGER}, #{item.isAuto,jdbcType=INTEGER})
        </foreach>
    </insert>

    <select id="queryExpenseInfoByType" resultMap="BUYORDER_EXPENSE_DTO">
        SELECT TBE.BUYORDER_EXPENSE_ID,
               TBE.BUYORDER_EXPENSE_NO,
               TBE.BUYORDER_NO,
               TBE.BUYORDER_ID,
               TBE.ORDER_TYPE,
               TBE.VALID_STATUS,
               TBE.VALID_TIME,
               TBE.STATUS,
               TBE.LOCKED_STATUS,
               TBE.INVOICE_STATUS,
               TBE.PAYMENT_STATUS,
               TBE.DELIVERY_STATUS,
               TBE.ARRIVAL_STATUS,
               TBE.SERVICE_STATUS,
               TBE.AUDIT_STATUS,
               TBE.INVOICE_TIME,
               TBE.PAYMENT_TIME,
               TBE.AUDIT_TIME,
               TBE.ADD_TIME,
               TBE.CREATOR,
               TBE.CREATOR_NAME,
               TBE.MOD_TIME,
               TBE.UPDATER,
               TBE.UPDATER_NAME,
               TBE.IS_DELETE,
               TBE.BUSINESS_TYPE,
               TBE.AFTER_SALES_NO,
               TBE.AFTER_SALES_ID,
               TBE.IS_RETURN_EARLY_WARN,
               TBE.IS_AUTO,
               TBED.BUYORDER_EXPENSE_DETAIL_ID,
               TBED.BUYORDER_EXPENSE_ID,
               TBED.PAYMENT_TYPE,
               TBED.PAYMENT_COMMENTS,
               TBED.INVOICE_TYPE,
               TBED.INVOICE_COMMENTS,
               TBED.TOTAL_AMOUNT,
               TBED.TRADER_ID,
               TBED.TRADER_NAME,
               TBED.TRADER_CONTACT_ID,
               TBED.TRADER_CONTACT_NAME,
               TBED.TRADER_CONTACT_MOBILE,
               TBED.TRADER_CONTACT_TELEPHONE,
               TBED.TRADER_ADDRESS_ID,
               TBED.TRADER_AREA,
               TBED.TRADER_ADDRESS,
               TBED.TRADER_COMMENTS,
               TBED.PREPAID_AMOUNT,
               TBED.ACCOUNT_PERIOD_AMOUNT,
               TBED.PERIOD_DAY,
               TBED.RETAINAGE_AMOUNT,
               TBED.RETAINAGE_AMOUNT_MONTH,
               TBED.EXPENSE_SOURCE,
               TBED.ORG_ID,
               TBED.CONTRACT_URL,
               TBED.IS_DELETE,
               TBED.ORDER_DESC,
               TBEI.BUYORDER_EXPENSE_ITEM_ID,
               TBEI.BUYORDER_EXPENSE_ID,
               TBEI.ARRIVAL_STATUS,
               TBEI.ARRIVAL_TIME,
               TBEI.DELIVERY_STATUS,
               TBEI.DELIVERY_TIME,
               TBEI.INVOICE_STATUS,
               TBEI.INVOICE_TIME,
               TBEI.GOODS_ID,
               TBEI.NUM,
               TBEI.IS_DELETE,
               TBEID.BUYORDER_EXPENSE_ITEM_DETAIL_ID,
               TBEID.BUYORDER_EXPENSE_ITEM_ID,
               TBEID.SKU,
               TBEID.GOODS_NAME,
               TBEID.BRAND_NAME,
               TBEID.MODEL,
               TBEID.UNIT_NAME,
               TBEID.PRICE,
               TBEID.CURRENCY_UNIT_ID,
               TBEID.EXPENSE_CATEGORY_ID,
               TBEID.EXPENSE_CATEGORY_NAME,
               TBEID.HAVE_STOCK_MANAGE,
               TBEID.INSIDE_COMMENTS,
               TBEID.IS_DELETE
        FROM T_BUYORDER_EXPENSE TBE
                 LEFT JOIN T_BUYORDER_EXPENSE_DETAIL TBED on TBE.BUYORDER_EXPENSE_ID = TBED.BUYORDER_EXPENSE_ID
                 LEFT JOIN T_BUYORDER_EXPENSE_ITEM TBEI
                           ON TBE.BUYORDER_EXPENSE_ID = TBEI.BUYORDER_EXPENSE_ID AND TBEI.IS_DELETE = 0
                 LEFT JOIN T_BUYORDER_EXPENSE_ITEM_DETAIL TBEID
                           ON TBEI.BUYORDER_EXPENSE_ITEM_ID = TBEID.BUYORDER_EXPENSE_ITEM_ID AND TBEID.IS_DELETE = 0
        WHERE TBE.BUYORDER_ID = #{buyorderId,jdbcType=INTEGER}
          AND TBE.ORDER_TYPE = #{orderType,jdbcType=TINYINT}
          AND TBE.IS_DELETE = 0
            AND TBE.BUSINESS_TYPE = 1
    </select>

    <update id="updateVerifyStatusByBuyorderExpenseId">
        update T_BUYORDER_EXPENSE
        SET AUDIT_STATUS=#{verifyStatus,jdbcType=INTEGER}
        where BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER}
    </update>

    <update id="updateVerifyStatusByBuyorderId">
        update T_BUYORDER_EXPENSE
        SET AUDIT_STATUS=#{verifyStatus,jdbcType=INTEGER}
        where BUYORDER_ID = #{buyorderId,jdbcType=INTEGER}
          and ORDER_TYPE = 0
        AND BUSINESS_TYPE = 1
    </update>

    <update id="updateStatusByBuyorderId">
        update T_BUYORDER_EXPENSE
        SET STATUS=#{status,jdbcType=INTEGER}
        where BUYORDER_ID = #{buyorderId,jdbcType=INTEGER}
          and ORDER_TYPE = 0
    </update>

    <select id="getBuyOrderExpenseListByBuyOrderId" resultMap="buyorderExpenseAndDetail">
        SELECT TBE.BUYORDER_EXPENSE_ID, TBE.BUYORDER_EXPENSE_NO, TBE.BUYORDER_NO, TBE.BUYORDER_ID, TBE.ORDER_TYPE,
        TBE.VALID_STATUS, TBE.VALID_TIME, TBE.STATUS, TBE.LOCKED_STATUS, TBE.INVOICE_STATUS, TBE.PAYMENT_STATUS,
        TBE.DELIVERY_STATUS, TBE.ARRIVAL_STATUS, TBE.SERVICE_STATUS, TBE.AUDIT_STATUS, TBE.INVOICE_TIME,
        TBE.PAYMENT_TIME, TBE.AUDIT_TIME, TBE.ADD_TIME, TBE.CREATOR, TBE.CREATOR_NAME, TBE.MOD_TIME, TBE.UPDATER,
        TBE.UPDATER_NAME, TBE.IS_DELETE,TBE.BUSINESS_TYPE,TBE.AFTER_SALES_NO,TBE.AFTER_SALES_ID,
        TBED.BUYORDER_EXPENSE_DETAIL_ID, TBED.BUYORDER_EXPENSE_ID, TBED.PAYMENT_TYPE, TBED.PAYMENT_COMMENTS,
        TBED.INVOICE_TYPE, TBED.INVOICE_COMMENTS, TBED.TOTAL_AMOUNT, TBED.TRADER_ID, TBED.TRADER_NAME,
        TBED.TRADER_CONTACT_ID, TBED.TRADER_CONTACT_NAME, TBED.TRADER_CONTACT_MOBILE, TBED.TRADER_CONTACT_TELEPHONE,
        TBED.TRADER_ADDRESS_ID, TBED.TRADER_AREA, TBED.TRADER_ADDRESS, TBED.TRADER_COMMENTS, TBED.PREPAID_AMOUNT,
        TBED.ACCOUNT_PERIOD_AMOUNT, TBED.PERIOD_DAY, TBED.RETAINAGE_AMOUNT, TBED.RETAINAGE_AMOUNT_MONTH,
        TBED.EXPENSE_SOURCE, TBED.ORG_ID, TBED.CONTRACT_URL, TBED.IS_DELETE,TBED.ORDER_DESC
        FROM T_BUYORDER_EXPENSE TBE
        LEFT JOIN T_BUYORDER_EXPENSE_DETAIL TBED on TBE.BUYORDER_EXPENSE_ID = TBED.BUYORDER_EXPENSE_ID
        WHERE TBE.IS_DELETE = 0
        and TBE.BUYORDER_ID = #{buyOrderId,jdbcType=INTEGER}
        AND TBE.BUSINESS_TYPE = 1

    </select>

    <update id="updateContractUrlOfBuyorder">
        UPDATE T_BUYORDER_EXPENSE_DETAIL
        SET CONTRACT_URL = #{url,jdbcType=VARCHAR}
        WHERE BUYORDER_EXPENSE_ID = #{buyOrderExpenseId,jdbcType=INTEGER}
    </update>
    <update id="updateStatusByBuyorderIdWithTicket">
        update T_BUYORDER_EXPENSE
        SET STATUS=#{status,jdbcType=INTEGER}
        where BUYORDER_EXPENSE_ID = #{buyOrderExpenseId,jdbcType=INTEGER}
    </update>

    <select id="getBuyorerExpenseByOrderNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_BUYORDER_EXPENSE
        where BUYORDER_EXPENSE_NO = #{buyOrderExpenseNo,jdbcType=VARCHAR}
        and IS_DELETE = 0
    </select>

    <select id="selectBuyorderData" resultType="com.vedeng.erp.buyorder.dto.BuyOrderDto">
        select a.ORG_ID, c.ORG_NAME, a.CREATOR, b.USERNAME creatorName, a.ADD_TIME, a.TRADER_NAME
        from T_BUYORDER a
                 left join T_USER b on a.CREATOR = b.USER_ID
                 left join T_ORGANIZATION c on a.ORG_ID = c.ORG_ID
        where a.BUYORDER_ID = #{buyorderId,jdbcType=INTEGER}
    </select>
    <select id="selectDirectExpenseInfo" resultType="com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseEntity">
        SELECT BUYORDER_EXPENSE_ID,
                DELIVERY_STATUS,
                ARRIVAL_STATUS
        FROM T_BUYORDER_EXPENSE
        WHERE ORDER_TYPE = 0
          AND BUYORDER_ID = #{buyOrderId,jdbcType=INTEGER}
          AND IS_DELETE = 0
        AND BUSINESS_TYPE = 1
    </select>

    <select id="getExpenseGoodsList4InvoiceSave"
            resultType="com.vedeng.erp.buyorder.dto.BuyorderExpenseItemDto">
        SELECT
            A.BUYORDER_EXPENSE_ID,
            A.BUYORDER_ID,
            C.BUYORDER_EXPENSE_ITEM_ID,
            C.GOODS_ID,
            D.SKU,
            D.PRICE,
            C.NUM,
            IF( C.ARRIVAL_STATUS = 2, C.NUM, 0 ) -  IFNULL( F.AFTER_SALES_NUM, 0 ) ARRIVAL_NUM,
            ROUND(IFNULL( E.INVOICE_NUM, 0 ), 2) INVOICED_NUM,
            ROUND(IFNULL( E.INVOICE_TOTAL_AMOUNT, 0 ), 2) INVOICED_TOTAL_AMOUNT
        FROM
        T_BUYORDER_EXPENSE A
        LEFT JOIN T_BUYORDER_EXPENSE_DETAIL B ON A.BUYORDER_EXPENSE_ID = B.BUYORDER_EXPENSE_ID
        AND B.IS_DELETE = 0
        LEFT JOIN T_BUYORDER_EXPENSE_ITEM C ON A.BUYORDER_EXPENSE_ID = C.BUYORDER_EXPENSE_ID
        AND C.IS_DELETE = 0
        LEFT JOIN T_BUYORDER_EXPENSE_ITEM_DETAIL D ON C.BUYORDER_EXPENSE_ITEM_ID = D.BUYORDER_EXPENSE_ITEM_ID
        AND D.IS_DELETE = 0
        LEFT JOIN (
            SELECT
            T1.INVOICE_ID,
            T1.RELATED_ID,
            T2.DETAILGOODS_ID,
            SUM(
            IF
            ( T1.COLOR_TYPE = 2 AND T1.IS_ENABLE = 1, T2.NUM, T2.NUM * - 1 )) INVOICE_NUM,
            SUM( IFNULL( T2.TOTAL_AMOUNT, 0 ) ) INVOICE_TOTAL_AMOUNT
            FROM
            T_INVOICE T1
            INNER JOIN T_INVOICE_DETAIL T2 ON T1.INVOICE_ID = T2.INVOICE_ID
            WHERE
            T1.VALID_STATUS <![CDATA[<]]> 2
            AND T1.COMPANY_ID = 1
            AND T1.TYPE = 4126
            AND T2.INVOICE_DETAIL_ID IS NOT NULL
            GROUP BY
            T1.RELATED_ID,
            T2.DETAILGOODS_ID
        ) E ON C.BUYORDER_EXPENSE_ITEM_ID = E.DETAILGOODS_ID
        AND A.BUYORDER_EXPENSE_ID = E.RELATED_ID
        LEFT JOIN (
            SELECT
            T3.BUYORDER_EXPENSE_ITEM_ID,
            SUM( T3.RETURN_NUM ) AFTER_SALES_NUM
            FROM
            T_EXPENSE_AFTER_SALES T1
            INNER JOIN T_EXPENSE_AFTER_SALES_STATUS T2 ON T1.EXPENSE_AFTER_SALES_ID = T2.EXPENSE_AFTER_SALES_ID
            AND T2.IS_DELETE = 0
            LEFT JOIN T_EXPENSE_AFTER_SALES_ITEM T3 ON T1.EXPENSE_AFTER_SALES_ID = T3.EXPENSE_AFTER_SALES_ID
            AND T3.IS_DELETE = 0
            WHERE
            T1.IS_DELETE = 0
            AND T1.EXPENSE_AFTER_SALES_TYPE = 4121
            AND T2.VALID_STATUS = 1
            AND T2.AFTER_SALES_STATUS IN ( 1, 2 )
            GROUP BY T3.BUYORDER_EXPENSE_ITEM_ID
        ) F ON C.BUYORDER_EXPENSE_ITEM_ID = F.BUYORDER_EXPENSE_ITEM_ID
        WHERE
        A.VALID_STATUS = 1
        AND A.STATUS = 1
        AND A.BUSINESS_TYPE = 1
        AND A.INVOICE_STATUS != 2
        AND A.LOCKED_STATUS = 0
        AND A.IS_DELETE = 0
        AND B.TOTAL_AMOUNT > 0
        AND IF( C.ARRIVAL_STATUS = 2, C.NUM, 0 ) -  IFNULL( F.AFTER_SALES_NUM, 0 ) - ROUND(IFNULL( E.INVOICE_NUM, 0 ), 2) > 0
        <if test="orderNo != null and orderNo != ''">
            AND A.BUYORDER_EXPENSE_NO LIKE CONCAT('%',#{orderNo,jdbcType=VARCHAR},'%' )
        </if>
        <if test="model != null and model != ''">
            AND D.MODEL LIKE CONCAT('%',#{model,jdbcType=VARCHAR},'%' )
        </if>
        <if test="goodsName != null and goodsName != ''">
            AND D.GOODS_NAME LIKE CONCAT('%',#{goodsName,jdbcType=VARCHAR},'%' )
        </if>
        <if test="brandName != null and brandName != ''">
            AND D.BRAND_NAME LIKE CONCAT('%',#{brandName,jdbcType=VARCHAR},'%' )
        </if>
        <if test="traderName != null and traderName != ''">
            AND B.TRADER_NAME LIKE CONCAT('%',#{traderName,jdbcType=VARCHAR},'%' )
        </if>
        <if test="invoiceType != null and invoiceType != ''">
            AND B.INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER}
        </if>
        <if test="insideComments != null and insideComments != ''">
            AND D.INSIDE_COMMENTS LIKE CONCAT('%',#{insideComments,jdbcType=VARCHAR},'%' )
        </if>
        <choose>
            <when test="isBelongBuyOrder == 1">
                AND A.BUYORDER_ID > 0
            </when>
            <when test="isBelongBuyOrder == 2">
                AND A.BUYORDER_ID = 0
            </when>
            <otherwise>
                AND 1 = 1
            </otherwise>
        </choose>
        GROUP BY
        C.BUYORDER_EXPENSE_ITEM_ID
    </select>

    <select id="getOrderExpenseListByOrderIds" resultType="com.vedeng.erp.buyorder.dto.BuyorderExpenseDto">
        SELECT
            A.BUYORDER_EXPENSE_ID,
            A.BUYORDER_EXPENSE_NO,
            A.VALID_TIME,
            B.TRADER_ID,
            B.TRADER_NAME,
            B.TOTAL_AMOUNT,
            A.VALID_TIME,
            A.PAYMENT_TIME,
            B.INVOICE_TYPE,
            B.INVOICE_COMMENTS,
            A.ARRIVAL_STATUS,
            A.LOCKED_STATUS
        FROM T_BUYORDER_EXPENSE A
        LEFT JOIN T_BUYORDER_EXPENSE_DETAIL B on A.BUYORDER_EXPENSE_ID = B.BUYORDER_EXPENSE_ID
        AND B.IS_DELETE = 0
        WHERE A.BUYORDER_EXPENSE_ID IN
            <foreach collection="orderIds" open="(" close=")" item="orderId" separator=",">
                #{orderId,jdbcType=INTEGER}
            </foreach>
        GROUP BY A.BUYORDER_EXPENSE_ID
    </select>
    <select id="getBuyOrderExpenseInfoById" resultMap="buyorderExpenseAndDetail">
        SELECT
            tbe.BUYORDER_EXPENSE_ID,
            tbe.BUYORDER_EXPENSE_NO,
            tbed.TOTAL_AMOUNT,
            org.ORG_NAME ,
            tbe.CREATOR_NAME ,
            tbe.STATUS,
            tbe.PAYMENT_STATUS,
            tbe.INVOICE_STATUS,
            tbe.VALID_TIME,
            tbed.TRADER_NAME,
            tbed.TRADER_ID ,
            tts.GRADE
        FROM
            T_BUYORDER_EXPENSE tbe
        LEFT JOIN T_BUYORDER_EXPENSE_DETAIL tbed ON
            tbe.BUYORDER_EXPENSE_ID = tbed.BUYORDER_EXPENSE_ID
        LEFT JOIN T_ORGANIZATION org ON
            org.ORG_ID = tbed.ORG_ID
        LEFT JOIN T_TRADER_SUPPLIER tts ON
            tts.TRADER_ID = tbed.TRADER_ID
        WHERE
            tbe.BUYORDER_EXPENSE_ID = #{buyOrderExpenseId,jdbcType=INTEGER}
    </select>
    <select id="getBuyorerExpenseByAfterSalesId"
            resultType="com.vedeng.erp.buyorder.dto.BuyorderExpenseDto">
        select *
        from T_BUYORDER_EXPENSE a
        where a.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
        and a.IS_DELETE = 0
        LIMIT 1
    </select>

    <select id="selectByExpenseAfterIds" resultMap="buyorderExpenseAndDetail">
        select TBE.BUYORDER_EXPENSE_ID, TBE.BUYORDER_EXPENSE_NO, TBE.BUYORDER_NO, TBE.BUYORDER_ID, TBE.ORDER_TYPE,
        TBE.VALID_STATUS, TBE.VALID_TIME, TBE.STATUS, TBE.LOCKED_STATUS, TBE.INVOICE_STATUS, TBE.PAYMENT_STATUS,
        TBE.DELIVERY_STATUS, TBE.ARRIVAL_STATUS, TBE.SERVICE_STATUS, TBE.AUDIT_STATUS, TBE.INVOICE_TIME,
        TBE.PAYMENT_TIME, TBE.AUDIT_TIME, TBE.ADD_TIME, TBE.CREATOR, TBE.CREATOR_NAME, TBE.MOD_TIME, TBE.UPDATER,
        TBE.UPDATER_NAME, TBE.IS_DELETE, TBE.BUSINESS_TYPE, TBE.AFTER_SALES_NO, TBE.AFTER_SALES_ID,
        TBE.IS_RETURN_EARLY_WARN, TBE.IS_AUTO from
        T_EXPENSE_AFTER_SALES TEAS left join T_BUYORDER_EXPENSE TBE on TBE.BUYORDER_EXPENSE_ID =
        TEAS.BUYORDER_EXPENSE_ID
        where TEAS.EXPENSE_AFTER_SALES_ID in
        <foreach item="item" collection="list" open="(" close=")" separator=",">
            #{item}
       </foreach>
    </select>

    <select id="getBuyOrderExpenseListByBuyOrderExpenseIdList" resultMap="buyorderExpenseAndDetail">
        SELECT TBE.BUYORDER_EXPENSE_ID, TBE.BUYORDER_EXPENSE_NO, TBE.BUYORDER_NO, TBE.BUYORDER_ID, TBE.ORDER_TYPE,
        TBE.VALID_STATUS, TBE.VALID_TIME, TBE.STATUS, TBE.LOCKED_STATUS, TBE.INVOICE_STATUS, TBE.PAYMENT_STATUS,
        TBE.DELIVERY_STATUS, TBE.ARRIVAL_STATUS, TBE.SERVICE_STATUS, TBE.AUDIT_STATUS, TBE.INVOICE_TIME,
        TBE.PAYMENT_TIME, TBE.AUDIT_TIME, TBE.ADD_TIME, TBE.CREATOR, TBE.CREATOR_NAME, TBE.MOD_TIME, TBE.UPDATER,
        TBE.UPDATER_NAME, TBE.IS_DELETE,TBE.BUSINESS_TYPE,TBE.AFTER_SALES_NO,TBE.AFTER_SALES_ID,
        TBED.BUYORDER_EXPENSE_DETAIL_ID, TBED.BUYORDER_EXPENSE_ID, TBED.PAYMENT_TYPE, TBED.PAYMENT_COMMENTS,
        TBED.INVOICE_TYPE, TBED.INVOICE_COMMENTS, TBED.TOTAL_AMOUNT, TBED.TRADER_ID, TBED.TRADER_NAME,
        TBED.TRADER_CONTACT_ID, TBED.TRADER_CONTACT_NAME, TBED.TRADER_CONTACT_MOBILE, TBED.TRADER_CONTACT_TELEPHONE,
        TBED.TRADER_ADDRESS_ID, TBED.TRADER_AREA, TBED.TRADER_ADDRESS, TBED.TRADER_COMMENTS, TBED.PREPAID_AMOUNT,
        TBED.ACCOUNT_PERIOD_AMOUNT, TBED.PERIOD_DAY, TBED.RETAINAGE_AMOUNT, TBED.RETAINAGE_AMOUNT_MONTH,
        TBED.EXPENSE_SOURCE, TBED.ORG_ID, TBED.CONTRACT_URL, TBED.IS_DELETE,TBED.ORDER_DESC
        FROM T_BUYORDER_EXPENSE TBE
        LEFT JOIN T_BUYORDER_EXPENSE_DETAIL TBED on TBE.BUYORDER_EXPENSE_ID = TBED.BUYORDER_EXPENSE_ID
        WHERE TBE.IS_DELETE = 0
        and TBE.BUYORDER_EXPENSE_ID IN
        <foreach item="buyOrderExpenseId" index="index" collection="buyOrderExpenseIdList" open="(" separator="," close=")">
            #{buyOrderExpenseId,jdbcType=INTEGER}
        </foreach>
        ORDER BY TBE.BUYORDER_EXPENSE_ID DESC
    </select>

    <select id="getBuyOrderExpenseByOrderNoAndBuyorderExpenseItemId" resultType="java.math.BigDecimal">
        select  C.PRICE
        from T_BUYORDER_EXPENSE A
                                 LEFT JOIN T_BUYORDER_EXPENSE_ITEM B ON A.BUYORDER_EXPENSE_ID = B.BUYORDER_EXPENSE_ID
                                 LEFT JOIN T_BUYORDER_EXPENSE_ITEM_DETAIL C ON B.BUYORDER_EXPENSE_ITEM_ID = C.BUYORDER_EXPENSE_ITEM_ID
        WHERE A.BUYORDER_EXPENSE_NO = #{orderNo,jdbcType=VARCHAR}
          AND B.BUYORDER_EXPENSE_ITEM_ID = #{expenseItemId,jdbcType=INTEGER}
    </select>

<!--auto generated by MybatisCodeHelper on 2023-12-09-->
    <select id="findByBuyorderExpenseNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_BUYORDER_EXPENSE
        where BUYORDER_EXPENSE_NO = #{buyorderExpenseNo,jdbcType=VARCHAR}
          and IS_DELETE = 0
    </select>
</mapper>