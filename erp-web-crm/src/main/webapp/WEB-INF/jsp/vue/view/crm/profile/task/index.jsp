<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!DOCTYPE html>
<html>
<head>
    <title>CRM-任务</title>
    <link rel="stylesheet" href="/static/css/common/common.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/ui/ui.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/css/pages/records.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/css/pages/task.css?staticResourceVersion=${requestScope.staticResourceVersion}">
</head>

<body>
    <!--隐藏的登录用户信息 -->
    <jsp:include page="../common/head_import.jsp"></jsp:include>

    <div class="page-wrap" id="page-container">
        <page-header></page-header>
        <div class="page-container">
            <div class="page-main" v-if="!loading">
                <div class="list-title">任务</div>
                <ui-search-container 
                    ref="listContainer" 
                    :default-tab="defaultTab" 
                    :headers="tableHeaders" 
                    url="/crm/task/profile/taskPage" 
                    :search-params="searchParams" 
                    :can-choose="false" 
                    :is-list-sort="true" 
                    :list-option="false" 
                    :need-custom="false" 
                    :need-setting="false"
                    :current-tab-id="currentTabId"
                    :default-search-params="defaultSearchParams"
                    :custom-reset="true"
                    @reset="handlerReset"
                    @listtabchange="hanlderTabChange"
                    @search="handlerListSearch"
                >
                    <template v-slot:filter-list>
                        <!-- <ui-search-item label="客户名称" :lock="true">
                            <ui-input v-model="searchParams.traderCustomerName" placeholder="请输入客户名称关键词"></ui-input>
                        </ui-search-item> -->
                        <ui-search-item label="业务编号" :lock="true">
                            <ui-input v-model="searchParams.bizNo" placeholder="请输入商机或线索编号"></ui-input>
                        </ui-search-item>
                        <ui-search-item label="任务内容">
                            <ui-input v-model="searchParams.taskContent" placeholder="请输入关键词"></ui-input>
                        </ui-search-item>
                        <ui-search-item label="发起人">
                            <ui-select :remote="true" multiple-type="fixed" :avatar="true" placeholder="全部" v-model="searchParams.creatorIdList" clearable :remote-info="creatorRemoteInfo" :default-multi="defaultCreatorItems"></ui-select>
                        </ui-search-item>
                        <ui-search-item label="待办人">
                            <ui-select :remote="true" multiple-type="fixed" :avatar="true" placeholder="全部" v-model="searchParams.todoUserIdList" clearable :remote-info="touserRemoteInfo" :default-multi="defaultTodoUserItems"></ui-select>
                        </ui-search-item>
                        <ui-search-item label="任务类型">
                            <ui-select placeholder="全部" multiple-type="fixed" :data="taskTypeList" v-model="searchParams.mainTaskTypeList" v-if="taskTypeList && taskTypeList.length" clearable></ui-select>
                        </ui-search-item>
                        <ui-search-item label="处理状态">
                            <ui-select placeholder="全部" :data="doneStatusList" v-model="searchParams.doneStatus" clearable></ui-select>
                        </ui-search-item>
                        <ui-search-item label="是否超时">
                            <ui-select placeholder="全部" :data="overTimeList" v-model="searchParams.overdueStatus" clearable></ui-select>
                        </ui-search-item>
                        <ui-search-item label="截止时间">
                            <!-- <ui-date-picker
                                v-model="searchParams.filterDeadline"
                                type="daterange"
                                start-placeholder="起始时间"
                                end-placeholder="截止时间"
                                @input="handlerFilterDateChange('Deadline', $event)"
                            ></ui-date-picker> -->
                            <ui-date-range v-model="searchParams.filterDeadline" @change="handlerFilterDateChange('Deadline', $event)"></ui-date-range>
                        </ui-search-item>
                        <ui-search-item label="提交时间">
                            <!-- <ui-date-picker
                                v-model="searchParams.filterCommitTime"
                                type="daterange"
                                start-placeholder="起始时间"
                                end-placeholder="截止时间"
                                @input="handlerFilterDateChange('CommitTime', $event)"
                            ></ui-date-picker> -->
                            <ui-date-range v-model="searchParams.filterCommitTime" @change="handlerFilterDateChange('CommitTime', $event)"></ui-date-range>
                        </ui-search-item>
                    </template>

                    <template v-slot:traderCustomerName="{ row }">
                        <template v-if="row.traderCustomerName">
                            <div class="text-line-1" :title="row.traderCustomerName">
                                <!-- <template v-if="row.traderNameLink">
                                    <div class="td-link" @click="GLOBAL.link({name:'客户详情', url: row.traderNameInnerLink, link: row.traderNameLink, nohost: true})">{{ row.traderCustomerName }}</div>
                                </template>
                                <template v-else>{{ row.traderCustomerName }}</template> -->
                                {{ row.traderCustomerName }}
                            </div>
                        </template>
                        <template v-else>-</template>
                    </template>

                    <template v-slot:bizNo="{ row }">
                        <div class="td-link leadsno" v-if="row.bizNo" @click="GLOBAL.link({name:{1:'商机详情',2:'线索详情',3:'商机详情', 7:'拜访计划详情'}[row.bizType], url: {1:'/crm/businessChance/profile/detail?id=' + row.bizId,2:'/crm/businessLeads/profile/detail?id=' + row.bizId,3:'/crm/businessChance/profile/detail?id=' + row.bizId, 7: '/crm/visitRecord/profile/detail?id=' + row.bizId}[row.bizType]})">{{row.bizNo}}</div>
                        <template v-else>-</template>
                    </template>
                    
                    <template v-slot:doneStatus="{ row }">
                        <div :class="['orange', 'green', 'gray'][row.doneStatus]">{{ ['待处理', '已处理', '已关闭'][row.doneStatus] || '-' }}</div>
                    </template>
                    
                    <template v-slot:todoUserNames="{ row }">
                        <div class="text-line-1" :title="getToUserName(row)">{{ getToUserName(row) }}</div>
                    </template>
                    
                    <template v-slot:isOverTime="{ row }">
                        <div :class="{red: row.isOverTime == 1}">{{ {0:'正常', 1:'超时'}[row.isOverTime] || '-' }}</div>
                    </template>
                    
                    <template v-slot:option="{ row }">
                        <div class="option-wrap">
                            <div class="table-edit" v-if="row.canHandle" @click="handleRw(row)">处理</div>
                            <div class="table-edit" v-else @click="lookRenwu(row)">查看</div>
                        </div>
                    </template>
                </ui-search-container>

                <renwu-handle ref="handleRenwu" :success-fun="taskDone"></renwu-handle>
                <renwu-review ref="lookRenwu"></renwu-review>
            </div>
        </div>
    </div>
    <script src="/static/js/common/vue.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/axios.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/vuedraggable.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/lodash.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/moment.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/ui/ui.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/layout.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/components/business/records.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <!-- 任务相关 -->
    <script src="/static/js/common/components/business/renwu.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <!-- 任务列表 -->
    <script src="/static/js/pages/taskList.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    
</body>
</html>
