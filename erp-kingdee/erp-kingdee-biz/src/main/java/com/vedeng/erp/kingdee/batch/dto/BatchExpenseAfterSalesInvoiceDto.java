package com.vedeng.erp.kingdee.batch.dto;

import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2023/3/22 16:46
 **/
/**
    * 费用售后退票表
    */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class BatchExpenseAfterSalesInvoiceDto {
    /**
    * 主键
    */
    private Long expenseAfterSalesInvoiceId;

    /**
    * 费用售后表主键ID
    */
    private Long expenseAfterSalesId;

    /**
    * 费用单明细表主键ID
    */
    private Integer buyorderExpenseItemId;

    /**
    * 发票代码
    */
    private String invoiceCode;

    /**
    * 发票号码
    */
    private String invoiceNo;

    /**
    * 订货号
    */
    private String sku;

    /**
    * 本次退票数
    */
    private BigDecimal returnNum;

    /**
    * 是否需退票 0否 1是
    */
    private Boolean isRefundInvoice;

    /**
    * 退票状态 0未退票 1已退票 2退票中
    */
    private Boolean returnInvoiceStatus;

    /**
    * 此记录生成的退票红票id
    */
    private Integer invoiceId;

    /**
    * 是否删除 0否 1是
    */
    private Boolean isDelete;

    /**
    * 添加人ID
    */
    private Integer creator;

    /**
    * 添加人名称
    */
    private String creatorName;

    /**
    * 修改时间
    */
    private Date modTime;

    /**
    * 更新人ID
    */
    private Integer updater;

    /**
    * 更新人名称
    */
    private String updaterName;

    /**
    * 备注
    */
    private String remark;

    /**
    * 更新备注
    */
    private String updateRemark;

    /**
    * 创建时间
    */
    private Date addTime;
}