package com.vedeng.coupon.service.impl;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.vedeng.common.http.NewHttpClientUtils;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.coupon.service.BaseCouponService;
import com.vedeng.market.api.dto.request.QueryCouponCenterRequest;
import com.vedeng.market.api.dto.request.QuerySkusCouponRequest;
import com.vedeng.market.api.dto.request.SubCouponSkusRequest;
import com.vedeng.market.api.dto.request.UpdateCouponRequest;
import com.vedeng.market.api.dto.response.couponcenter.QueryCouponCenterResponse;
import com.vedeng.market.api.dto.response.mycoupon.MyCouponDto;
import com.vedeng.market.api.dto.response.mycoupon.MyCouponSkuDTO;
import com.vedeng.market.api.dto.response.mycoupon.MyCouponSkuResponse;
import com.vedeng.market.api.dto.response.mycoupon.MyCouponSkuSubResponse;
import com.vedeng.order.model.SaleorderGoods;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 基本的优惠券接口
 */
@Service
public class BaseCouponServiceImpl implements BaseCouponService {

    private static Logger LOGGER = LoggerFactory.getLogger(BaseCouponServiceImpl.class);

    @Value("${coupon.url}")
    private String couponUrl;

    private static String SUCCESS_CODE = "success";

    private static String GetMyCouponsBySkuNos = "coupon/myCoupon/skus";

    private static String GetCouponByTraderId = "coupon/getCouponByTraderId";

    private static String SubCouponSkuNos = "coupon/subCoupon/skus";

    private static String CutCouponBytraderId = "/coupon/cutCouponByTraderId";

    /**
     * 查看订单的优惠券
     * @param subCouponSkusRequest
     * @return
     */
    @Override
    public MyCouponSkuResponse findOrderCouponBySkuNosAndTraderId(SubCouponSkusRequest subCouponSkusRequest) {

        MyCouponSkuResponse myCouponSkuResponse = new MyCouponSkuResponse();

        try {

            String requestJson = JsonUtils.translateToJson(subCouponSkusRequest);

            LOGGER.info("BaseCouponServiceImpl->findOrderCouponBySkuNosAndTraderId 请求参数:" + requestJson);

            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(couponUrl + GetMyCouponsBySkuNos,requestJson);

            LOGGER.info("BaseCouponServiceImpl->findOrderCouponBySkuNosAndTraderId 响应:" + resultJsonObj.toString());

            if(!SUCCESS_CODE.equals(resultJsonObj.get("code"))
                    || "null".equals(resultJsonObj.getJSONObject("data").toString())){
                return myCouponSkuResponse;
            }

            Gson gson = new Gson();
            myCouponSkuResponse = gson.fromJson(resultJsonObj.getJSONObject("data").toString(),
                    new TypeToken<MyCouponSkuResponse>(){}.getType());

        } catch (Exception e) {
            LOGGER.error("查询订单优惠券失败",e);
        }

        return myCouponSkuResponse;
    }

    /**
     * 查看我的优惠券
     * @param queryCouponCenterRequest
     * @return
     */
    @Override
    public MyCouponDto getCouponByTraderId(QueryCouponCenterRequest queryCouponCenterRequest) {

        MyCouponDto myCouponDto = new MyCouponDto();

        try {

            String requestJson = JsonUtils.translateToJson(queryCouponCenterRequest);

            LOGGER.info("BaseCouponServiceImpl->getCouponByTraderId 请求参数:" + requestJson);

            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(couponUrl + GetCouponByTraderId,requestJson);

            LOGGER.info("BaseCouponServiceImpl->getCouponByTraderId 响应:" + resultJsonObj.toString());

            if(!SUCCESS_CODE.equals(resultJsonObj.get("code")) ){
                return myCouponDto;
            }

            Gson gson = new Gson();
            myCouponDto = gson.fromJson(resultJsonObj.getJSONObject("data").toString(),
                    new TypeToken<MyCouponDto>(){}.getType());

        } catch (Exception e) {
            LOGGER.error("查看我的优惠券失败",e);
        }

        return myCouponDto;
    }

    @Override
    public MyCouponSkuSubResponse queryCouponPrice(SubCouponSkusRequest subCouponSkusRequest) throws Exception {

        try {
            String requestJson = JsonUtils.translateToJson(subCouponSkusRequest);

            LOGGER.info("BaseCouponServiceImpl->SubCouponSkuNos 请求参数:" + requestJson);

            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(couponUrl + SubCouponSkuNos,requestJson);

            if(resultJsonObj == null){
                throw new Exception("连接券中心失败");
            }

            LOGGER.info("BaseCouponServiceImpl->SubCouponSkuNos 响应:" + resultJsonObj.toString());

            if(!SUCCESS_CODE.equals(resultJsonObj.get("code")) ){
                throw new Exception(resultJsonObj.getString("message"));
            }

            Gson gson = new Gson();
            MyCouponSkuSubResponse myCouponSkuSubResponse = gson.fromJson(resultJsonObj.getJSONObject("data").toString(),
                    new TypeToken<MyCouponSkuSubResponse>(){}.getType());

            return myCouponSkuSubResponse;

        } catch (Exception e) {
            LOGGER.error("查看优惠券扣减金额",e);
            throw new Exception("连接券中心失败");
        }
    }

    @Override
    public QueryCouponCenterResponse cutCouponByTraderId(UpdateCouponRequest subCouponSkusRequest) {

        QueryCouponCenterResponse queryCouponCenterResponse = null;

        try {

            String requestJson = JsonUtils.translateToJson(subCouponSkusRequest);

            LOGGER.info("BaseCouponServiceImpl->cutCouponByTraderId 请求参数:" + subCouponSkusRequest);

            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(couponUrl + CutCouponBytraderId,requestJson);

            LOGGER.info("BaseCouponServiceImpl->cutCouponByTraderId 响应:" + resultJsonObj.toString());

            if(!SUCCESS_CODE.equals(resultJsonObj.get("code")) ){
                return queryCouponCenterResponse;
            }

            Gson gson = new Gson();
            queryCouponCenterResponse = gson.fromJson(resultJsonObj.getJSONObject("data").toString(),
                    new TypeToken<QueryCouponCenterResponse>(){}.getType());

        } catch (Exception e) {

            LOGGER.error("扣减优惠券",e);
        }

        return queryCouponCenterResponse;
    }
}
