/**
 * 删除产品
 * 
 * @param ordergoodsGoodsId
 * @returns
 */
function delAccountGoods(ordergoodsGoodsAccountId,goodsId,webAccountId,ordergoodsStoreId) {
	checkLogin();
	if (ordergoodsGoodsAccountId > 0 
			&& goodsId> 0
			&& webAccountId> 0
			&& ordergoodsStoreId> 0
			) {
		layer.confirm("确认删除产品", {
			btn : [ '确定', '取消' ]
		// 按钮
		}, function() {
			$.ajax({
				type : "POST",
				url : "./deleteaccountgoods.do",
				data : {
					'ordergoodsGoodsAccountId' : ordergoodsGoodsAccountId,
					'goodsId' : goodsId,
					'webAccountId' : webAccountId,
					'ordergoodsStoreId' : ordergoodsStoreId,
				},
				dataType : 'json',
				success : function(data) {
					refreshNowPageList(data)
				},
				error : function(data) {
					if (data.status == 1001) {
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
		}, function() {
		});
	}
}
