package com.vedeng.goods.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 商品分类信息表
 * <AUTHOR>
 */
@Setter
@Getter
public class CategoryFrontDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private Integer baseCategoryId;

    /**
     * 分类名称
     */
    private String baseCategoryName;


    /**
     * 分类级别（1开始依次递增）
     */
    private Integer baseCategoryLevel;

    /**
     * 分类类型：1:医疗器械；2:非医疗器械
     */
    private Integer baseCategoryType;


    /**
     * 父级id
     */
    private Integer parentId;

    /**
     * 当前节点之前的所有节点，ID拼接以英文逗号分割
     */
    private String treenodes;

    /**
     * 分类子节点
     */
    private List<CategoryFrontDto> children;

}