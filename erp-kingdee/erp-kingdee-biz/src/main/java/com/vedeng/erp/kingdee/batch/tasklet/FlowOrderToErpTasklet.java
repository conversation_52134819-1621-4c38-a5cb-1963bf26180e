package com.vedeng.erp.kingdee.batch.tasklet;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.tasklet.BaseTasklet;
import com.vedeng.erp.kingdee.batch.dto.BatchFlowOrderDto;
import com.vedeng.erp.kingdee.batch.dto.BatchFlowOrderInfoDto;
import com.vedeng.erp.kingdee.batch.repository.BatchFlowOrderDtoMapper;
import com.vedeng.erp.kingdee.dto.result.KingDeeInternalProcurementQueryResultDto;
import com.vedeng.erp.kingdee.service.KingDeeInternalProcurementService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 流传单信息会传到erp
 * @date 2023/1/31 13:54
 */
@Service
@Slf4j
public class FlowOrderToErpTasklet extends BaseTasklet {

    @Autowired
    private BatchFlowOrderDtoMapper batchFlowOrderDtoMapper;

    @Autowired
    private KingDeeInternalProcurementService kingDeeInternalProcurementService;

    @Override
    public void doExec(Map<String, Object> jobParameters) throws Exception {
        log.info("流传单信息会传到erp开始");
        List<BatchFlowOrderDto> noPushInternalProcurement = batchFlowOrderDtoMapper.findNoPushInternalProcurement();
        if (CollUtil.isEmpty(noPushInternalProcurement)) {
            return;
        }
        for (BatchFlowOrderDto batchFlowOrderDto : noPushInternalProcurement) {
            log.info("传入erp的流传单信息:{}", JSON.toJSONString(batchFlowOrderDto));
            // 推送到erp
            // 推送成功后修改状态
            // 1.基于 F_QZOK_BDDJTID 获取金蝶单据
            List<KingDeeInternalProcurementQueryResultDto> kingDeeInternalProcurement = kingDeeInternalProcurementService.getKingDeeInternalProcurement(batchFlowOrderDto.getFlowOrderId().toString());
            log.info("金蝶单据:{}", JSON.toJSONString(kingDeeInternalProcurement));

            if (CollUtil.isEmpty(kingDeeInternalProcurement)) {
                log.info("未查询到金蝶单据,忽略");
                continue;
            }

            for (KingDeeInternalProcurementQueryResultDto dto : kingDeeInternalProcurement) {

                List<BatchFlowOrderInfoDto> flowOrderInfoDtoList = batchFlowOrderDtoMapper.findByflowNodeId(Long.valueOf(dto.getF_QZOK_BLZID()));
                if (CollUtil.isNotEmpty(flowOrderInfoDtoList)) {
                    for (BatchFlowOrderInfoDto batchFlowOrderInfoDto : flowOrderInfoDtoList) {
                        // 处理采购业务相关字段，当字段值为-1时不存储当前节点信息
                        int cgddValue = Integer.parseInt(dto.getF_QZOK_CGDD());
                        int cgfkValue = Integer.parseInt(dto.getF_QZOK_CGFK());
                        int cgrkValue = Integer.parseInt(dto.getF_QZOK_CGRK());
                        int cgfpValue = Integer.parseInt(dto.getF_QZOK_CGFP());

                        // 只有当采购相关字段不全为-1时才处理采购业务
                        if (cgddValue != -1 || cgfkValue != -1 || cgrkValue != -1 || cgfpValue != -1) {
                            batchFlowOrderInfoDto.setOrderStatus(cgddValue);
                            batchFlowOrderInfoDto.setPaymentStatus(cgfkValue);
                            batchFlowOrderInfoDto.setStorageStatus(cgrkValue);
                            batchFlowOrderInfoDto.setInvoiceStatus(cgfpValue);
                            Map<String, Object> buyOrderMap = new HashMap<>();
                            buyOrderMap.put("invoiceNo", dto.getF_QZOK_CGFPH());
                            buyOrderMap.put("invoiceUrl", dto.getF_QZOK_CGDZFP());
                            batchFlowOrderInfoDto.setInvoiceInfo(JSON.toJSONString(CollUtil.newArrayList(buyOrderMap)));
                            batchFlowOrderDtoMapper.updateByPrimaryKeySelectiveFlowOrderInfo(batchFlowOrderInfoDto);
                        } else {
                            log.info("采购业务字段全为-1，跳过处理采购业务信息，流转节点ID:{}", dto.getF_QZOK_BLZID());
                        }

                        // 处理销售业务相关字段，当字段值为-1时不存储当前节点信息
                        int xsddValue = Integer.parseInt(dto.getF_QZOK_XSDD());
                        int xsskValue = Integer.parseInt(dto.getF_QZOK_XSSK());
                        int xsckValue = Integer.parseInt(dto.getF_QZOK_XSCK());
                        int xsfpValue = Integer.parseInt(dto.getF_QZOK_XSFP());

                        // 只有当销售相关字段不全为-1时才处理销售业务
                        if (xsddValue != -1 || xsskValue != -1 || xsckValue != -1 || xsfpValue != -1) {
                            batchFlowOrderInfoDto.setOrderStatus(xsddValue);
                            batchFlowOrderInfoDto.setPaymentStatus(xsskValue);
                            batchFlowOrderInfoDto.setStorageStatus(xsckValue);
                            batchFlowOrderInfoDto.setInvoiceStatus(xsfpValue);
                            Map<String, Object> saleOrderMap = new HashMap<>();
                            saleOrderMap.put("invoiceNo", dto.getF_QZOK_XSFPH());
                            saleOrderMap.put("invoiceUrl", dto.getF_QZOK_DZFP());
                            batchFlowOrderInfoDto.setInvoiceInfo(JSON.toJSONString(CollUtil.newArrayList(saleOrderMap)));
                            batchFlowOrderDtoMapper.updateByPrimaryKeySelectiveFlowOrderInfo(batchFlowOrderInfoDto);
                        } else {
                            log.info("销售业务字段全为-1，跳过处理销售业务信息，流转节点ID:{}", dto.getF_QZOK_BLZID());
                        }
                    }

                } else {

                    // 处理采购业务相关字段，当字段值为-1时不存储当前节点信息
                    int cgddValue = Integer.parseInt(dto.getF_QZOK_CGDD());
                    int cgfkValue = Integer.parseInt(dto.getF_QZOK_CGFK());
                    int cgrkValue = Integer.parseInt(dto.getF_QZOK_CGRK());
                    int cgfpValue = Integer.parseInt(dto.getF_QZOK_CGFP());

                    // 只有当采购相关字段不全为-1时才创建采购业务记录
                    if (cgddValue != -1 || cgfkValue != -1 || cgrkValue != -1 || cgfpValue != -1) {
                        BatchFlowOrderInfoDto buyOrder = BatchFlowOrderInfoDto.builder()
                                .flowOrderInfoType(0)
                                .flowOrderInfoNo(dto.getF_QZOK_GSYWDH())
                                .flowNodeId(Integer.parseInt(dto.getF_QZOK_BLZID()))
                                .orderStatus(cgddValue)
                                .paymentStatus(cgfkValue)
                                .storageStatus(cgrkValue)
                                .invoiceStatus(cgfpValue)
                                .build();
                        Map<String, Object> buyOrderMap = new HashMap<>();
                        buyOrderMap.put("invoiceNo", dto.getF_QZOK_CGFPH());
                        buyOrderMap.put("invoiceUrl", dto.getF_QZOK_CGDZFP());
                        buyOrder.setInvoiceInfo(JSON.toJSONString(CollUtil.newArrayList(buyOrderMap)));
                        batchFlowOrderDtoMapper.insertSelectiveFlowOrderInfo(buyOrder);
                    } else {
                        log.info("采购业务字段全为-1，跳过创建采购业务记录，流转节点ID:{}", dto.getF_QZOK_BLZID());
                    }

                    // 处理销售业务相关字段，当字段值为-1时不存储当前节点信息
                    int xsddValue = Integer.parseInt(dto.getF_QZOK_XSDD());
                    int xsskValue = Integer.parseInt(dto.getF_QZOK_XSSK());
                    int xsckValue = Integer.parseInt(dto.getF_QZOK_XSCK());
                    int xsfpValue = Integer.parseInt(dto.getF_QZOK_XSFP());

                    // 只有当销售相关字段不全为-1时才创建销售业务记录
                    if (xsddValue != -1 || xsskValue != -1 || xsckValue != -1 || xsfpValue != -1) {
                        BatchFlowOrderInfoDto saleOrder = BatchFlowOrderInfoDto.builder()
                                .flowOrderInfoType(1)
                                .flowOrderInfoNo(dto.getF_QZOK_GSYWDH())
                                .flowNodeId(Integer.parseInt(dto.getF_QZOK_BLZID()))
                                .orderStatus(xsddValue)
                                .paymentStatus(xsskValue)
                                .storageStatus(xsckValue)
                                .invoiceStatus(xsfpValue)
                                .build();
                        Map<String, Object> saleOrderMap = new HashMap<>();
                        saleOrderMap.put("invoiceNo", dto.getF_QZOK_XSFPH());
                        saleOrderMap.put("invoiceUrl", dto.getF_QZOK_DZFP());
                        saleOrder.setInvoiceInfo(JSON.toJSONString(CollUtil.newArrayList(saleOrderMap)));
                        batchFlowOrderDtoMapper.insertSelectiveFlowOrderInfo(saleOrder);
                    } else {
                        log.info("销售业务字段全为-1，跳过创建销售业务记录，流转节点ID:{}", dto.getF_QZOK_BLZID());
                    }
                }

            }


        }

    }
}




