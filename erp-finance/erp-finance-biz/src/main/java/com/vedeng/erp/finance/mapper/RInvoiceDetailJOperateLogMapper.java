package com.vedeng.erp.finance.mapper;

import com.vedeng.erp.finance.domain.entity.RInvoiceDetailJOperateLogEntity;
import com.vedeng.erp.finance.dto.InvoiceRelationOperateLogDto;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

public interface RInvoiceDetailJOperateLogMapper {
    int deleteByPrimaryKey(Integer RInvoiceDetailJOperateLogEntityId);

    int insert(RInvoiceDetailJOperateLogEntity record);

    int insertSelective(RInvoiceDetailJOperateLogEntity record);

    RInvoiceDetailJOperateLogEntity selectByPrimaryKey(Integer RInvoiceDetailJOperateLogEntityId);

    int updateByPrimaryKeySelective(RInvoiceDetailJOperateLogEntity record);

    int updateByPrimaryKey(RInvoiceDetailJOperateLogEntity record);

    List<RInvoiceDetailJOperateLogEntity> findAllByInvoiceId(@Param("invoiceId") Integer invoiceId);

    List<InvoiceRelationOperateLogDto> getValidWarehousingLogByRelatedIds(@Param("detailGoodsIdList") List<Integer> detailGoodsIdList,
                                                                          @Param("operateType") Integer operateType);

    BigDecimal sumWarehouseGoodsOutInItem(@Param("detailGoodsId") Integer detailGoodsId);
}