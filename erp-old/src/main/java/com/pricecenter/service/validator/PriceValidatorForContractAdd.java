package com.pricecenter.service.validator;

import com.pricecenter.dto.PriceInfoUploadValidatorDto;
import com.pricecenter.dto.ValidatorResult;
import com.pricecenter.service.Validator;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 价格相关校验器
 */
@Service
public class PriceValidatorForContractAdd implements Validator<PriceInfoUploadValidatorDto> {

    public static BigDecimal Three_Bill = BigDecimal.valueOf(300000000);

    @Override
    public ValidatorResult validator(PriceInfoUploadValidatorDto priceInfoUploadValidatorDto) {

        ValidatorResult result = ValidatorResult.newBuild();

        //获取所有的行
        List<Row> rowList = priceInfoUploadValidatorDto.getRows();

        Row thisRow = null;
        int rowNum = 1;
        int contractCellNum = 3;

        for(int i = 0;i < rowList.size();i++){

            rowNum++;

            thisRow = rowList.get(i);

            Cell contractCell = thisRow.getCell(contractCellNum);

            //行XX中“采购成本”/“市场价”/“终端价”/“经销价”为空，提交失败
            if (contractCell == null || contractCell.getCellType() == CellType.BLANK) {
                return result.setMessage("行"+ rowNum +"中价格为空，提交失败");
            }


            contractCell.setCellType(CellType.STRING);

            String contractValue = contractCell.getStringCellValue();

            //数字保留2位小数
            if(!priceVadator(contractValue)){
                return result.setMessage("行"+ rowNum +"中协议价只能是正数，且最多2位小数，提交失败");
            }

            BigDecimal  contractPrice = new BigDecimal(contractValue);

            //行XX中“采购成本”/“市场价”/“终端价”/“经销价”为0，提交失败
            if(contractPrice.compareTo(BigDecimal.ZERO) == 0){
                return result.setMessage("行"+ rowNum +"中协议价为0，提交失败");
            }

            //行XX中“采购成本”/“市场价”/“终端价”/“经销价”超过3亿，提交失败
            if(contractPrice.compareTo(Three_Bill) > 0){
                return result.setMessage("行"+ rowNum +"中协议价超过3亿，提交失败");
            }
        }

        return result.setResult(true);
    }

    public boolean priceVadator(String priceStr) {
        Pattern pattern = Pattern.compile("^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,2})?$"); // 判断小数点后2位的数字的正则表达式
        Matcher match = pattern.matcher(priceStr);
        return match.matches();
    }

}
