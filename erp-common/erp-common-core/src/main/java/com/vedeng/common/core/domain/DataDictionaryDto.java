package com.vedeng.common.core.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 数据字典dto
 * @date 2024/8/1 9:14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataDictionaryDto {

    private String value;
    private String label;
    private List<DataDictionaryDto> children;

    public DataDictionaryDto(String value, String label) {
        this.value = value;
        this.label = label;
    }
}
