package com.vedeng.erp.kingdee.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import com.vedeng.common.mybatis.jbatis.annotation.Column;
import com.vedeng.common.mybatis.jbatis.annotation.GeneratedValue;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.common.mybatis.jbatis.annotation.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
    * 销售合同
    */
@Getter
@Setter
@ToString
@Table(name = "KING_DEE_BUYORDER_CONTRACT")
public class KingDeeBuyOrderContractEntity extends BaseEntity {
    /**
    * 主键
    */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer kingDeeBuyorderContractId;

    /**
    * 云星空系统单据FID值
    */
    private Integer fId;

    /**
    * 组织
    */
    private String fQzokOrgid;

    /**
    * 合同号
    */
    private String fQzokHth;

    /**
    * 合同日期
    */
    private String fQzokHtrq;

    /**
    * 合同金额
    */
    private String fQzokHtje;

    /**
    * 税率
    */
    private String fQzokSll;

    /**
    * 销售订单
    */
    private String fQzokDdh;
    @Column(name = "FBillNo")
    private String FBillNo;
}