package com.vedeng.api.standard.duplicate.enums;

/**
 * 幂等性记录状态枚举
 * 定义幂等性记录的处理状态
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-15
 */
public enum IdempotencyStatus {
    
    /**
     * 处理中 - 请求正在处理
     * 对应数据库值：0
     */
    PROCESSING(0, "处理中", "请求正在处理中"),
    
    /**
     * 成功 - 请求处理成功
     * 对应数据库值：1
     */
    SUCCESS(1, "成功", "请求处理成功"),
    
    /**
     * 失败 - 请求处理失败
     * 对应数据库值：2
     */
    FAILED(2, "失败", "请求处理失败");
    
    /**
     * 数据库存储值
     */
    private final int code;
    
    /**
     * 状态名称
     */
    private final String name;
    
    /**
     * 状态描述
     */
    private final String description;
    
    IdempotencyStatus(int code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }
    
    public int getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取状态枚举
     * 
     * @param code 状态代码
     * @return 对应的状态枚举
     * @throws IllegalArgumentException 如果代码无效
     */
    public static IdempotencyStatus fromCode(int code) {
        for (IdempotencyStatus status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("无效的幂等性状态代码: " + code);
    }
    
    /**
     * 根据名称获取状态枚举
     * 
     * @param name 状态名称
     * @return 对应的状态枚举
     * @throws IllegalArgumentException 如果名称无效
     */
    public static IdempotencyStatus fromName(String name) {
        for (IdempotencyStatus status : values()) {
            if (status.name.equals(name)) {
                return status;
            }
        }
        throw new IllegalArgumentException("无效的幂等性状态名称: " + name);
    }
    
    /**
     * 判断是否为终态（成功或失败）
     * 
     * @return 是否为终态
     */
    public boolean isTerminal() {
        return this == SUCCESS || this == FAILED;
    }
    
    /**
     * 判断是否为成功状态
     * 
     * @return 是否为成功状态
     */
    public boolean isSuccess() {
        return this == SUCCESS;
    }
    
    /**
     * 判断是否为失败状态
     * 
     * @return 是否为失败状态
     */
    public boolean isFailed() {
        return this == FAILED;
    }
    
    /**
     * 判断是否为处理中状态
     * 
     * @return 是否为处理中状态
     */
    public boolean isProcessing() {
        return this == PROCESSING;
    }
    
    @Override
    public String toString() {
        return String.format("IdempotencyStatus{code=%d, name='%s', description='%s'}", 
                code, name, description);
    }
}
