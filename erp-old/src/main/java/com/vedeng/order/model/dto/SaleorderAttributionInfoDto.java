package com.vedeng.order.model.dto;

import lombok.Data;

/**
 * 销售订单归属信息封装实体
 */
@Data
public class SaleorderAttributionInfoDto {

    /**
     * 销售订单主键ID
     */
    private Integer saleorderId;

    /**
     * 销售订单号
     */
    private String saleorderNo;

    /**
     * 客户ID
     */
    private Integer traderId;

    /**
     * 付款类型
     */
    private Integer paymentType;

    /**
     * 归属销售人员Id
     */
    private Integer optUserId;

    /**
     * 归属销售人员名称
     */
    private String optUserName;

    /**
     * 归属销售部门ID
     */
    private Integer salesDeptId;

    /**
     * 归属销售部门名称
     */
    private String salesDeptName;

    public Boolean checkData(){
        return saleorderId != null && traderId != null && paymentType != null && optUserId != null && salesDeptId != null;
    }

}
