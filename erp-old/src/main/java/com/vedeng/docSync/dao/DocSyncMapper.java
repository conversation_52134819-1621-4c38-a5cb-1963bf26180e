package com.vedeng.docSync.dao;

import com.vedeng.docSync.model.TempSyncSku;
import com.vedeng.docSync.model.pojo.*;
import com.vedeng.docSync.model.pojo.generate.DocOfGoodsDo;
import com.vedeng.goods.model.CoreSku;
import com.vedeng.goods.model.CoreSpu;
import com.vedeng.order.model.vo.SkuDto;
import com.vedeng.trader.model.TraderSupplier;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 拆到一个mapper 为了后期好拆
 *
 * @Author: Cherny.chen
 * @Create: 2021/7/20 13:30
 */
public interface DocSyncMapper {

    /**
     * 保存文件
     *
     * @param docFileExtDo
     */
    void saveDocFile(DocFileExtDo docFileExtDo);


    /**
     * 保存操作日志
     *
     * @param docBuzTagMaintainLogExtDo
     */
    void saveActionLog(DocBuzTagMaintainLogExtDo docBuzTagMaintainLogExtDo);

    /**
     * 保存文件业务关系
     *
     * @param docRBuzTagJFileExtDo
     */
    int saveBuzTagJFile(DocRBuzTagJFileExtDo docRBuzTagJFileExtDo);


    /**
     * 批量保存操作日志
     *
     * @param docRBuzTagJFileExtDoList
     * @return
     */
    int batchSaveActionLog(List<DocRBuzTagJFileExtDo> docRBuzTagJFileExtDoList);

    /**
     * 临时删除
     *
     * @param rIds
     * @return
     */
    int batchRemoveOldBuzTagFile(List<Integer> rIds);

    /**
     * 根据spuId 查询doc已存在
     * @param spuId
     * @return
     */
    @Select("SELECT * FROM T_DOC_OF_GOODS  WHERE SPU_ID = #{spuId} AND SKU_ID IS NULL AND IS_DELETE = 0;")
    List<DocOfGoodsExtDo> getDocOfGoodsBySpuId(@Param("spuId") Integer spuId);

    /**
     * 根据skuId 查询doc已存在
     * @param skuId skuId
     * @return docGoods
     */
    @Select("SELECT * FROM T_DOC_OF_GOODS WHERE SKU_ID = #{skuId}  ")
    List<DocOfGoodsExtDo> getDocOfGoodsBySkuId(@Param("skuId") Integer skuId);

    /**
     * 根据spuId 查询doc已存在
     * @param spuIdList
     * @return
     */
    List<DocOfGoodsExtDo> getDocOfGoodsBySpuIdList(@Param("spuIdList") List<Integer> spuIdList);

    /**
     *  查询doc 已有厂商
     * @param traderId
     * @return
     */
    List<DocSupplierExtDo> getDocSupplierByTraderSupplier(@Param("traderId") Integer traderId);


    /**
     * 通过厂商查询关系表
     *
     * @param docOfGoodsExtDoId
     * @param docBuzTagExtDoId
     * @return
     */
    List<DocRBuzTagJFileExtDo> listBuzTagFileForGoods(@Param("docOfGoodsExtDoId") Integer docOfGoodsExtDoId, @Param("docBuzTagExtDoId") Integer docBuzTagExtDoId);

    /**
     * 通过厂商查询关系表
     *
     * @param supplierExtDoId
     * @param docBuzTagExtDoId
     * @return
     */
    List<DocRBuzTagJFileExtDo> listBuzTagFileForSupplier(@Param("supplierExtDoId") Integer supplierExtDoId, @Param("docBuzTagExtDoId") Integer docBuzTagExtDoId);


    /**
     * 查询所有标签
     *
     * @param buzType
     * @return
     */
    @Select("SELECT * FROM T_DOC_BUZ_TAG WHERE IS_DELETE = 0 AND BUZ_TYPE = #{buzType} ")
    List<DocBuzTagExtDo> listBuzTagByBuzType(@Param("buzType") Integer buzType);

    /**
     * 根据md5获取文件
     *
     * @param md5
     * @return
     */
    @Select("SELECT * FROM T_DOC_FILE WHERE MD5 = #{md5} AND IS_DELETE = 0 limit 1")
    DocFileExtDo getDocFileByMd5(@Param("md5") String md5);

    /**
     * 获取全量的文件
     * @return
     */
    List<DocFileExtDo> getDocFileList();

    /**
     * 更新文件信息
     * @param docFile
     */
    void updateDocFile(DocFileExtDo docFile);

    /**
     * 查询所有资料库商品资料信息
     * @return
     */
    List<DocOfGoodsExtDo> listDocOfGoods();

    void updateDocGoodsTitle(@Param("docOfGoodsExtDo") DocOfGoodsExtDo docOfGoodsExtDo);


    void updateBCIdBySpuInfo(@Param("spuInfoById") CoreSpu spuInfoById);

    void updateBCIdBySkuInfo(@Param("skuDto") SkuDto skuDto);

    void updateDocFileOriginLink(DocFileExtDo docFile);

    @Select("SELECT * FROM T_DOC_FILE WHERE ORIGIN_LINK = #{originLink} AND IS_DELETE = 0 limit 1")
    DocFileExtDo getDocFileByOriginUrl(@Param("originLink") String originLink);

    /**
     * 获取未同步数据
     * @return
     */
    List<TempSyncSku> selectNotSyncSku();
}
