void function () {
    //tab切换
    window.triggerTabChange = (index) => {
        if(index == 1) {
            $('.J-leads-wrap').css('display', 'block');

            if(!$('.J-leads-iframe').length) {
                $('.J-leads-wrap').append('<iframe src="/crm/businessLeads/profile/index?from=chance&parentiframe=1" class="leads-iframe J-leads-iframe" frameborder="0"></iframe>');
            }

           $('.J-chance-wrap').css('display', 'none');
        } else {
            $('.J-leads-wrap').css('display', 'none');
            $('.J-chance-wrap').css('display', 'block');

            setTimeout(() => {
                pageVue.$refs.listContainer.checkNeedToggle();
            })
        }
    };

    // let leadsIframeUrl = '/crm/businessLeads/profile/index?from=chance';

    // if(window.parent === window) {

    // }
    
    let pageVue = new Vue({
        el: '#page-container',
        data: {
            tableHeaders: [
                {
                    key: "bussinessChanceNo",
                    label: "商机编号",
                    width: "130px",
                    lock: true,
                },
                {
                    key: "traderName",
                    label: "客户名称",
                    width: "220px",
                    lock: true,
                    sortable: true,
                    filter: true,
                },
                {
                    key: "businessTypeStr",
                    label: "类型",
                    width: "75px",
                    sortable: true,
                    filter: true
                },
                {
                    key: "systemBusinessLevelStr",
                    label: "等级",
                    width: "75px",
                    sortable: true,
                    sortType: 'SABC',
                    filter: true
                },
                {
                    key: "stageStr",
                    parse: {
                        key: 'stage',
                        data: GLOBAL.BUSINESSCHANCE_STAGE
                    },
                    label: "阶段",
                    width: "75px",
                    sortable: true,
                    sortType: 'number',
                    sortId: 'stage',
                    filter: true,
                },
                {
                    key: "latestCommunicateRecordContent",
                    label: "跟进记录",
                    width: "220px"
                },
                {
                    key: "addTimeDate",
                    label: "创建时间",
                    width: "150px"
                },
                {
                    key: "creatorName",
                    label: "创建人",
                    width: "130px",
                    avatar: 'creatorPic',
                },
                {
                    key: "username",
                    label: "归属销售",
                    width: "130px",
                    avatar: 'belongPic',
                    sortable: true,
                    filter: true,
                },
                {
                    key: "latestTaskContent",
                    label: "任务",
                    width: "240px"
                },
                {
                    key: "amount",
                    label: "预计成单金额",
                    width: "130px",
                    sortable: true,
                    sortType: 'number',
                    edit: true,
                    align: 'right',
                    sum: '当前页商机预计成单总金额：%sum%'
                },
                {
                    key: "orderTimeDate",
                    label: "预计成单日期",
                    width: "110px",
                    sortable: true,
                    sortType: 'time',
                    edit: true,
                },
                {
                    key: "traderContactName",
                    label: "联系人",
                    width: "80px",
                    edit: true,
                },
                {
                    key: "mobile",
                    label: "手机",
                    width: "130px",
                    tel: true
                },
                {
                    key: "telephone",
                    label: "固话",
                    width: "130px",
                    tel: true
                },
                {
                    key: "productCommentsSale",
                    label: "产品信息",
                    width: "220px",
                    edit: true,
                },
                {
                    key: "comments",
                    label: "备注",
                    width: "200px",
                    edit: true,
                },
                {
                    key: "tags",
                    label: "标签",
                    width: "100px",
                    filter: true,
                    filterType: 'array',
                    filterKey: 'name',
                    edit: true,
                },
                {
                    key: "terminalTraderName",
                    label: "终端名称",
                    width: "180px"
                },
                {
                    key: "terminalTraderNatureStr",
                    label: "终端性质",
                    filter: true,
                    width: "85px",
                    edit: true,
                },
                {
                    key: "terminalTraderRegionStr",
                    label: "终端区域",
                    sortable: true,
                    sortType: 'address',
                    sortId: 'terminalTraderRegion',
                    width: "220px",
                    filter: true,
                    edit: true,
                },
                {
                    key: "customerRelationship",
                    label: "客情关系",
                    width: "75px",
                },
                {
                    key: "purchasingTypeStr",
                    label: "采购方式",
                    width: "125px",
                    filter: true,
                    sortable: true
                },
                {
                    key: "biddingPhaseStr",
                    label: "招标阶段",
                    width: "115px",
                    filter: true,
                    sortable: true
                },
                {
                    key: "biddingParameterStr",
                    parse: {
                        key: 'biddingParameter',
                        data: { 1: '可调整', 2: '不可调整' }
                    },
                    label: "招标参数",
                    width: "115px",
                    filter: true,
                    sortable: true
                },
                {
                    key: "typeStr",
                    label: "来源",
                    width: "75px",
                },
                {
                    key: "leadsNo",
                    label: "线索编号",
                    width: "130px",
                },
                {
                    key: "option",
                    label: "操作",
                    width: "85px",
                },
            ],
            defaultTab: [{
                label: '全部商机',
                id: 'all'
            }],
            searchParams: {
                bussinessChanceNo: '',
                businessTypeList: [],
                systemBusinessLevelList: [],
                stageList: [],
                latestFollowUpTimeRange: [],
                latestFollowUpTimeStartStr: '',
                latestFollowUpTimeEndStr: '',
                deadlineDateRange: [],
                deadlineDateStart: '',
                deadlineDateEnd: '',
                doneStatus: '',
                addTimeRange: [],
                addTimeStartStr: '',
                addTimeEndStr: '',
                belongerIdList: [],
                belongerMultiItems: [],
                //创建人
                creatorIdList: [],
                creatorMultiItems: [],
                collaboratorIdList: [],
                collaboratorMultiItems: [],
                attentionState: '',
                amountMin: '',
                amountMax: '',
                orderTimeRange: [],
                orderTimeStartStr: '',
                orderTimeEndStr: '',
                traderName: '',
                traderContactName: '',
                mobile: '',
                telephone: '',
                productCommentsSale: '',
                comments: '',
                tagIdList: [],
                tagsMultiItems: [],
                terminalTraderName: '',
                terminalTraderNatureList: [],
                //终端区域
                areaSelectedIds: {},
                provinceIdList: [], 
                cityIdList: [],
                countyIdList: [],
                //end
                //销售区域
                salerAreaSelectedIds: {},
                provinceIds: [],
                cityIds: [],
                //end
                //分类
                selectedCategoryIdList: {},
                categoryIdList: [],
                //end
                customerRelationshipList: [],
                purchasingType: '',
                biddingPhaseList: [],
                biddingParameter: '',
                type: '',
                leadsNo: '',
                skuNo: '',
                productName: '',
                brandName: '',
                modelNumber: '',
                //销售部门
                organizationIdList: [], //所有勾选部门id,后台筛选用
                organizationIdValues: [] //所有勾选最下级部门id，组件的model值
            },
            searchDefaultParams: {},
            stageList: [],
            doneStatusList: [{
                label: '已处理',
                value: '1'
            }, {
                label: '未处理',
                value: '0'
            }],
            attentionStateList: [{
                label: '是',
                value: '1'
            }, {
                label: '否',
                value: '0'
            }],
            customerRelationshipList: [
                {
                    label: '决策人',
                    value: '1',
                },
                {
                    label: '使用人',
                    value: '2',
                }
            ],
            biddingParameterList: [{
                label: '可调整',
                value: '1'
            }, {
                label: '不可调整',
                value: '2'
            }],
            sourceList: [{
                label: '总机',
                value: 391
            }, {
                label: '销售',
                value: 392
            }, {
                label: '自有商城',
                value: 394
            }, {
                label: '拜访',
                value: 6012
            }],
            belongerListRemoteInfo: {
                url: '/crm/businessChance/profile/findAllBelongUser',
                paramsMethod: 'get',
                paramsKey: 'name',
                parseLabel: 'username',
                parseValue: 'userId',
                parseAvatar: 'aliasHeadPicture'
            },
            creatorListRemoteInfo: {
                url: '/crm/businessChance/profile/findAllCreator',
                paramsMethod: 'get',
                paramsKey: 'name',
                parseLabel: 'username',
                parseValue: 'userId',
                parseAvatar: 'aliasHeadPicture'
            },
            shareUserListRemoteInfo: {
                url: '/crm/user/profile/getAllUserInfo',
                paramsKey: 'name',
                parseLabel: 'username',
                parseValue: 'userId',
                parseAvatar: 'aliasHeadPicture'
            },
            tagsRemoteInfo: {
                url: '/crm/businessChance/profile/findAllTag',
                paramsMethod: 'get',
                paramsKey: 'name',
                parseLabel: 'name',
                parseValue: 'id'
            },
            sourceRemoteInfo: {
                url: '/crm/sysOption/public/getByParentCode?parentCode=businessType',
                parseLabel: 'title',
                parseValue: 'sysOptionDefinitionId'
            },
            addressData: [],
            addressLv2Data: [],
            isShowEditWrapper: false,
            tableEditEl: null,
            editKey: '',
            editId: '',
            edit_amount: '',
            edit_traderContactName: '',
            edit_productCommentsSale: '',
            edit_comments: '',
            edit_terminalTraderName: '',
            edit_orderTimeDate: '',
            edit_terminalTraderNature: '',
            edit_tagIds: [],
            edit_tagsSelectedItems: [],
            edit_terminalTraderRegion: [],
            editErrorMsg: '',
            editErrorable: false,
            allCategoryList: [],
            departmentData: []
        },
        created() {
            //默认近九十天
            this.initAddTime();
            this.getStageList();
            this.getAddressData();
            this.getCategoryList();
            this.getDepartmentList();
        },
        mounted() { },
        methods: {
            triggerTabChange(index) {
                triggerTabChange(index);
            },
            initAddTime() {
                let endTime = moment(new Date()).format('YYYY-MM-DD');
                let startTime = moment(new Date().getTime() - 90 * 24 * 60 * 60 * 1000).format('YYYY-MM-DD');

                this.searchParams.addTimeEndStr = endTime + ' 23:59:59';
                this.searchParams.addTimeStartStr = startTime + ' 00:00:00';
                this.searchParams.addTimeRange = [startTime, endTime];
                this.searchDefaultParams = {
                    addTimeEndStr: endTime + ' 23:59:59',
                    addTimeStartStr: startTime + ' 00:00:00',
                    addTimeRange: [startTime, endTime]
                }
            },
            hanlderTabChange(data) {
                console.log(data)
            },
            getStageList() {
                let list = [];
                for (let item in GLOBAL.BUSINESSCHANCE_STAGE) {
                    list.push({
                        label: GLOBAL.BUSINESSCHANCE_STAGE[item],
                        value: item + ''
                    })
                }

                this.stageList = list;
            },
            getAddressData() {
                this.$axios.post('/crm/common/profile/getRegionAll').then(({ data }) => {
                    if (data.success) {
                        this.addressData = data.data;

                        let lv2Data = [];
                        this.addressData.forEach(item => {
                            let itemData = JSON.parse(JSON.stringify(item));
                            if(itemData.children && itemData.children.length) {
                                itemData.children.forEach(lv2 => {
                                    delete lv2.children;
                                })
                            }

                            lv2Data.push(itemData)
                        })

                        this.addressLv2Data = lv2Data;
                    }
                })
            },
            parseCategoryData(list) {
                let categorys = [];

                list.forEach(item => {
                    let cateItem = {
                        label: item.baseCategoryName,
                        value: item.baseCategoryId,
                        children: []
                    }

                    if(item.children && item.children.length) {
                        cateItem.children = this.parseCategoryData(item.children)
                    }

                    categorys.push(cateItem);
                }) 

                return categorys;
            },
            getCategoryList() {
                this.$axios.get(`/crm/category/public/getAllCategory`).then(({ data }) => {
                    if(data.success) {
                        this.allCategoryList = this.parseCategoryData(data.data || []);
                    }
                })
            },
            //获取组织架构数据
            getDepartmentList() {
                this.$axios.get(`/crm/user/profile/getFullDepartmentTree`).then(({ data }) => {
                    if(data.success) {
                        let allDep = data.data.childOrganization[0].childOrganization;

                        this.departmentData = this.parseDepartmentList(allDep);
                    }
                })
            },
            parseDepartmentList(list) {
                let parseList =  [];
                list.forEach(item => {
                    let itemData = {
                        label: item.organizationName,
                        value: item.organizationId,
                        children: [],
                        checked: false,
                        hasChecked: false
                    }

                    if(item.childOrganization && item.childOrganization.length) {
                        itemData.children = this.parseDepartmentList(item.childOrganization);
                    }

                    parseList.push(itemData);
                })

                return parseList;
            },
            handlerDepartmentChange(data) {
                this.searchParams.organizationIdList = data.allSelectedIds;
            },
            handleFilterAddressChange(val) {
                this.searchParams.areaSelectedIds = val || {};
                this.searchParams.provinceIdList = val.level1 || [];
                this.searchParams.cityIdList = val.level2 || [];
                this.searchParams.countyIdList = val.level3 || [];
            },
            handleFilterSalerAddressChange(val) {
                this.searchParams.salerAreaSelectedIds = val || {};
                this.searchParams.provinceIds = val.level1 || [];
                this.searchParams.cityIds = val.level2 || [];
            },
            parseFilterCategory(ids, list) {
                let resIds = [];
                if(!list) {
                    list = this.allCategoryList;
                }
                list.forEach(item => {
                    if(ids.indexOf(item.value) !== -1) {
                        if(item.children && item.children.length) {
                            let cIds = [];
                            item.children.forEach(item1 => {
                                cIds.push(item1.value);
                            })
                            resIds = resIds.concat(this.parseFilterCategory(cIds, item.children));
                        } else {
                            resIds.push(item.value);
                        }
                    } else {
                        if(item.children && item.children.length) {
                            item.children.forEach(item1 => {
                                if(ids.indexOf(item1.value) !== -1) {
                                    if(item1.children && item1.children.length) {
                                        let cIds = [];
                                        item1.children.forEach(item2 => {
                                            cIds.push(item2.value);
                                        })
                                        resIds = resIds.concat(this.parseFilterCategory(cIds, item1.children));
                                    } else {
                                        resIds.push(item.value);
                                    }
                                }
                            })
                        }
                    }
                })

                return resIds;
            },
            handleFilterCategoryChange(value) {
                this.searchParams.categoryIdList = this.parseFilterCategory(value.level1).concat(this.parseFilterCategory(value.level2).concat(value.level3));
                this.searchParams.selectedCategoryIdList = value || {};

                console.log(this.searchParams.categoryIdList);
            },
            //日期范围选择后塞值
            handlerFilterDateChange(key, value) {
                let startKey = key + 'Start';
                let endKey = key + 'End';

                if (key == 'latestFollowUpTime' || key == 'addTime' || key == 'orderTime') {
                    startKey = key + 'StartStr';
                    endKey = key + 'EndStr';
                }

                this.searchParams[startKey] = value[0] ? value[0] + ' 00:00:00' : '';
                this.searchParams[endKey] = value[1] ? value[1] + ' 23:59:59' : '';
            },
            //标签列表字段渲染处理
            getTagsStr(row) {
                if (!row.tags || !row.tags.length) {
                    return '-'
                }

                let list = [];

                row.tags.forEach(item => {
                    list.push(item.name);
                });

                return list.join('/')
            },
            //客情关系列表字段渲染处理
            getCustomerRelationshipStr(data) {
                if (!data) {
                    return '-';
                } else {
                    let strs = [];
                    data.toString().split(',').forEach(item => {
                        strs.push({
                            1: '决策人',
                            2: '使用人'
                        }[item] || '')
                    })

                    return strs.join(',')
                }
            },
            remoteSelectChange(key, data) {
                console.log(data)
                this.searchParams[key] = data.list || [];
            },
            multiAttention() {
                let ids = this.$refs.listContainer.getSelectedData('bussinessChanceId');

                if (!ids || !ids.length) {
                    this.$message.warn('请勾选具体商机');
                } else {
                    this.attention(ids, 1);
                }
            },
            attentionItem(data, type) {
                if ((type == 1 && !GLOBAL.auth('C0204')) || (type == 0 && !GLOBAL.auth('C0203'))) {
                    GLOBAL.showNoAuth();
                    return;
                }

                this.attention([data.bussinessChanceId], type);
            },
            attention(data, type) {
                let url = '';
                let params = '';

                if (type == 1) {
                    url = '/crm/businessChance/profile/attention';
                    params = data;
                } else {
                    url = '/crm/businessChance/profile/cancelAttention?id=' + data[0];
                }

                this.$axios.post(url, data).then(({ data }) => {
                    if (data.success) {
                        this.$message.success(type == 1 ? '已关注' : '已取消关注');
                        this.$refs.listContainer.refresh();
                    } else {
                        this.$message.warn(data.message)
                    }
                })
            },
            tableItemEdit(params) {
                this.tableEditEl = null;
                this.isShowEditWrapper = false;
                this.editKey = '';
                this.editErrorMsg = '';
                this.editErrorable = false;

                setTimeout(() => {
                    this.tableEditEl = params.el;
                    this.isShowEditWrapper = true;
                    this.editKey = params.key;
                    this.editId = params.data.bussinessChanceId;
                    if (params.key === 'terminalTraderNatureStr') {
                        this.edit_terminalTraderNature = params.data.terminalTraderNature;
                    } else if (params.key === 'terminalTraderRegionStr') {
                        this.edit_terminalTraderRegion = params.data.terminalTraderRegion ? params.data.terminalTraderRegion.split(',') : [];
                    } else if (params.key === 'tags') {
                        let tagIds = [];
                        let tagsSelectedItems = [];
                        if (params.data.tags && params.data.tags.length) {
                            params.data.tags.forEach(item => {
                                tagIds.push(item.id)
                                tagsSelectedItems.push({
                                    label: item.name,
                                    value: item.id
                                })
                            })
                        };
                        this.edit_tagIds = tagIds;
                        this.edit_tagsSelectedItems = tagsSelectedItems;
                    } else {
                        this['edit_' + params.key] = params.data[params.key];
                    }

                    this.$nextTick(() => {
                        if(this.$refs.editTdInput) {
                            this.$refs.editTdInput.triggerFocus();
                        }

                        if(this.$refs.editDate) {
                            this.$refs.editDate.triggerFocus();
                        }
                    })
                })
            },
            validSubmitEdit() {
                this.editErrorMsg = "";
                this.editErrorable = false;

                if (this.editKey === 'amount') {
                    if (!this.edit_amount.toString().trim()) {
                        this.editErrorMsg = "请输入预计成单金额";
                        this.editErrorable = true;
                        return false;
                    } else if (!/^\d+(\.\d{1,2})?$/.test(this.edit_amount)) {
                        this.editErrorMsg = "请输入2位小数";
                        this.editErrorable = true;
                        return false;
                    }
                }

                if (this.editKey === 'orderTimeDate') {
                    if (!this.edit_orderTimeDate.trim()) {
                        this.editErrorMsg = "请选择预计成单日期";
                        this.editErrorable = true;
                        return false;
                    }
                }

                if (this.editKey === 'productCommentsSale') {
                    if (!this.edit_productCommentsSale.trim()) {
                        this.editErrorMsg = "请输入产品需求";
                        this.editErrorable = true;
                        return false;
                    }
                }

                if (this.editKey === 'traderContactName') {
                    if (!this.edit_traderContactName.trim()) {
                        this.editErrorMsg = "请输入联系人";
                        this.editErrorable = true;
                        return false;
                    }
                }

                return true;

            },
            submitEdit() {
                if (!this.validSubmitEdit()) {
                    return false;
                }

                this.updateItem();
            },
            cancelEdit() {
                this.tableEditEl = null;
                this.isShowEditWrapper = false;
            },
            listScroll() {
                this.tableEditEl = null;
                if (this.$refs.editDate) {
                    this.$refs.editDate.hidePanel();
                }
                this.isShowEditWrapper = false;
            },
            updateItem() {
                let reqdata = {
                    bussinessChanceId: this.editId
                };

                if (this.editKey === 'tags') {
                    reqdata.tagIds = this['edit_tagIds'].join(',');
                } if (this.editKey === 'terminalTraderNatureStr') {
                    reqdata.terminalTraderNature = this.edit_terminalTraderNature;
                } else if (this.editKey === 'terminalTraderRegionStr') {
                    reqdata.terminalTraderRegion = this.edit_terminalTraderRegion.join(',');
                } else {
                    reqdata[this.editKey] = this['edit_' + this.editKey];
                }

                this.$axios.post('/crm/businessChance/profile/updateSingleData', reqdata).then(({ data }) => {
                    if (data.success) {
                        this.tableEditEl = null;
                        this.isShowEditWrapper = false;
                        this.$refs.listContainer.refresh();
                    } else {
                        this.$message.warn(data.message)
                    }
                })
            },
            handleEditAreaChange(val) {
                if (val && val.length) {
                    this.edit_terminalTraderRegion = [val[0].value, val[1].value, val[2].value];
                } else {
                    this.edit_terminalTraderRegion = [];
                }
            },
            gotoAdd() {
                if (GLOBAL.auth('C0201')) {
                    GLOBAL.link({ name: '新建商机/线索', url: '/crm/businessChance/profile/add', multi: true })
                } else {
                    GLOBAL.showNoAuth();
                }
            },
            showRecordDialog(row) {
                this.$refs.followRecordListDialog.open({
                    relatedId: row.bussinessChanceId,
                    traderId: row.traderId,
                    traderName: row.traderName,
                    belongerId: row.userId,
                    belonger: row.username,
                    belongerPic: row.belongPic,
                    traderNameLink: row.traderNameLink,
                    traderNameInnerLink: row.traderNameInnerLink,
                    tycFlag: row.tycFlag,
                    contact: row.traderContactName,
                    traderContactId: row.traderContactId,
                    phone: row.mobile,
                    telePhone: row.telephone,
                    communicateType: 244
                });
            },
            showTaskDialog(row) {
                this.$refs.renwuDialog.open({
                    bizType: '1', //  1:商机 2:线索 3:报价
                    relatedId: row.bussinessChanceId, // 业务id
                    quoteorderId: row.quoteorderDto.quoteorderId || '', // 报价单id
                });
            },
            handlerCallNumber(data) {
                GLOBAL.callNumber({
                    phone: data.phone || '',
                    traderId: data.data.traderId || 0,
                    traderType: 1,
                    callType: 1,
                    orderId: data.data.bussinessChanceId || '',
                    traderContactId: data.data.traderContactId || 0
                })
            },
            checkCanEdit(data) {
                return data.stage != 5 && data.stage != 6;
            },
            handlerRecordAdd (data) {
                console.log(data)
                if(data.noneNextDate === 1) {
                    this.$popup.warn({
                        message: '无下次跟进时间，是否需要关闭该条商机？',
                        buttons: [{
                            txt: '关闭商机',
                            btnClass: 'delete',
                            callback() {
                                GLOBAL.link({name: '商机详情', url: '/crm/businessChance/profile/detail?id=' + data.relatedId + '&isdelete=1'})
                            }
                        },
                        {
                            txt: '无需关闭',
                        }]
                    })
                }
            },
            // 天眼查详情
            openTyc (traderName) {
                this.$refs.tycDetail.open(traderName);
            },
            multiAddPartner() {
                let selectedArr = this.$refs.listContainer.getSelectedData();

                if (!selectedArr || !selectedArr.length) {
                    this.$message.warn('请勾选具体商机');
                } else {
                    let bizs = [];

                    selectedArr.forEach(item => {
                        bizs.push({
                            businessType: 1, // 1.商机 2.报价 3.订单 4.售后 5.线索
                            businessId: item.bussinessChanceId,
                            businessNo: item.bussinessChanceNo
                        })
                    })

                    this.$refs.partnerCreateDialog.open({
                        multi: true,
                        list: bizs
                    });
                }
            },
            handlerAddPartner() {

            }

        }
    })
}.call(this);