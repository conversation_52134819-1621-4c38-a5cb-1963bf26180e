<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.finance.dao.HxInvoiceMapper">

    <resultMap id="BaseResultMap" type="com.vedeng.finance.vo.HxInvoiceVo">
        <id column="HX_INVOICE_ID" property="hxInvoiceId" jdbcType="INTEGER"/>
        <result column="INVOICE_TYPE" property="invoiceType" jdbcType="INTEGER" />
        <result column="INVOICE_CATEGORY" property="invoiceCategory" jdbcType="VARCHAR"/>
        <result column="INVOICE_CODE" property="invoiceCode" jdbcType="VARCHAR"/>
        <result column="INVOICE_NUM" property="invoiceNum" jdbcType="VARCHAR"/>
        <result column="SALER_TAX_NUM" property="salerTaxNum" jdbcType="VARCHAR"/>
        <result column="SALER_NAME" property="salerName" jdbcType="VARCHAR"/>
        <result column="SALER_ADDRESS" property="salerAddress" jdbcType="VARCHAR"/>
        <result column="SALER_TEL" property="salerTel" jdbcType="VARCHAR"/>
        <result column="SALER_BANK" property="salerBank" jdbcType="VARCHAR"/>
        <result column="SALER_BANK_ACCOUNT" property="salerBankAccount" jdbcType="VARCHAR"/>
        <result column="BUYER_TAX_NUM" property="buyerTaxNum" jdbcType="VARCHAR"/>
        <result column="BUYER_NAME" property="buyerName" jdbcType="VARCHAR"/>
        <result column="BUYER_ADDRESS" property="buyerAddress" jdbcType="VARCHAR"/>
        <result column="BUYER_TEL" property="buyerTel" jdbcType="VARCHAR"/>
        <result column="BUYER_BANK" property="buyerBank" jdbcType="VARCHAR"/>
        <result column="BUYER_BANK_ACCOUNT" property="buyerBankAccount" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="BIGINT"/>
        <result column="INVOICE_AMOUNT" property="invoiceAmount" jdbcType="DECIMAL"/>
        <result column="TAX_AMOUNT" property="taxAmount" jdbcType="DECIMAL"/>
        <result column="AMOUNT" property="amount" jdbcType="DECIMAL"/>
        <result column="TAX_RATE" property="taxRate" jdbcType="INTEGER"/>
        <result column="COLOR_TYPE" property="colorType" jdbcType="INTEGER"/>
        <result column="COMMENT" property="comment" jdbcType="VARCHAR"/>
        <result column="CREATOR" property="creator" jdbcType="VARCHAR"/>
        <result column="CHECKER" property="checker" jdbcType="VARCHAR"/>
        <result column="PAYEE" property="payee" jdbcType="VARCHAR"/>
        <result column="ATTACHMENT" property="attachment" jdbcType="VARCHAR"/>
        <result column="INVOICE_STATUS" property="invoiceStatus" jdbcType="INTEGER"/>
        <result column="INVOICE_REFUND_STATUS" property="invoiceRefundStatus" jdbcType="INTEGER"/>
        <result column="ADD_TIME" property="addTime" jdbcType="BIGINT"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="BIGINT"/>
        <result column="UPDATER" property="updater" jdbcType="INTEGER"/>
        <result column="TRADER_ID" property="traderId" jdbcType="INTEGER" />

        <result column="ENTRY_TIME" property="entryTime" jdbcType="BIGINT"/>
        <result column="ENTRY_USER_ID" property="entryUserId" jdbcType="INTEGER"/>
        <result column="VALID_TIME" property="validTime" jdbcType="BIGINT"/>
        <result column="VALID_USER_ID" property="validUserId" jdbcType="INTEGER"/>
        <result column="VALID_USER" property="validUser" jdbcType="VARCHAR"/>
        <result column="VALID_STATUS" property="validStatus" jdbcType="INTEGER"/>
        <result column="VALID_COMMENTS" property="validComments" jdbcType="VARCHAR"/>
        <result column="AUTH_STATUS" property="authStatus" jdbcType="INTEGER"/>
        <result column="IS_AUTH" property="authStatus" jdbcType="TINYINT"/>
        <result column="AUTH_TIME" property="authTime" jdbcType="BIGINT"/>
        <result column="AUTH_MONTH" property="authMonth" jdbcType="INTEGER"/>
        <result column="FINANCE_VOUCHER_NO" property="financeVoucherNo" jdbcType="VARCHAR"/>
        <result column="TYPE" property="type" jdbcType="INTEGER"/>

        <collection property="buyorders" ofType="com.vedeng.order.model.Buyorder">
            <id column="BUYORDER_ID" property="buyorderId" jdbcType="INTEGER"/>
            <result column="BUYORDER_NO" property="buyorderNo" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>


    <!-- 获取采购订单列表 -->
    <resultMap id="BuyorderBaseResultMap" type="com.vedeng.order.model.Buyorder">
        <id column="BUYORDER_ID" property="buyorderId" jdbcType="INTEGER"/>
        <result column="BUYORDER_NO" property="buyorderNo" jdbcType="VARCHAR"/>
        <result column="ORDER_TYPE" property="orderType" jdbcType="BIT"/>
        <result column="COMPANY_ID" property="companyId" jdbcType="INTEGER"/>
        <result column="ORG_ID" property="orgId" jdbcType="INTEGER"/>
        <result column="USER_ID" property="userId" jdbcType="INTEGER"/>
        <result column="VALID_STATUS" property="validStatus" jdbcType="BIT"/>
        <result column="VALID_TIME" property="validTime" jdbcType="BIGINT"/>
        <result column="STATUS" property="status" jdbcType="BIT"/>
        <result column="LOCKED_STATUS" property="lockedStatus" jdbcType="BIT"/>
        <result column="INVOICE_STATUS" property="invoiceStatus" jdbcType="BIT"/>
        <result column="INVOICE_TIME" property="invoiceTime" jdbcType="BIGINT"/>
        <result column="PAYMENT_STATUS" property="paymentStatus" jdbcType="BIT"/>
        <result column="PAYMENT_TIME" property="paymentTime" jdbcType="BIGINT"/>
        <result column="DELIVERY_STATUS" property="deliveryStatus" jdbcType="BIT"/>
        <result column="DELIVERY_TIME" property="deliveryTime" jdbcType="BIGINT"/>
        <result column="ARRIVAL_STATUS" property="arrivalStatus" jdbcType="BIT"/>
        <result column="ARRIVAL_TIME" property="arrivalTime" jdbcType="BIGINT"/>
        <result column="SERVICE_STATUS" property="serviceStatus" jdbcType="BIT"/>
        <result column="HAVE_ACCOUNT_PERIOD" property="haveAccountPeriod" jdbcType="BIT"/>
        <result column="DELIVERY_DIRECT" property="deliveryDirect" jdbcType="BIT"/>
        <result column="TOTAL_AMOUNT" property="totalAmount" jdbcType="DECIMAL"/>
        <result column="TRADER_ID" property="traderId" jdbcType="INTEGER"/>
        <result column="TRADER_NAME" property="traderName" jdbcType="VARCHAR"/>
        <result column="TRADER_CONTACT_ID" property="traderContactId" jdbcType="INTEGER"/>
        <result column="TRADER_CONTACT_NAME" property="traderContactName" jdbcType="VARCHAR"/>
        <result column="TRADER_CONTACT_MOBILE" property="traderContactMobile" jdbcType="VARCHAR"/>
        <result column="TRADER_CONTACT_TELEPHONE" property="traderContactTelephone" jdbcType="VARCHAR"/>
        <result column="TRADER_ADDRESS_ID" property="traderAddressId" jdbcType="INTEGER"/>
        <result column="TRADER_ADDRESS" property="traderAddress" jdbcType="VARCHAR"/>
        <result column="TRADER_COMMENTS" property="traderComments" jdbcType="VARCHAR"/>
        <result column="TAKE_TRADER_ID" property="takeTraderId" jdbcType="INTEGER"/>
        <result column="TAKE_TRADER_NAME" property="takeTraderName" jdbcType="VARCHAR"/>
        <result column="TAKE_TRADER_CONTACT_ID" property="takeTraderContactId" jdbcType="INTEGER"/>
        <result column="TAKE_TRADER_CONTACT_NAME" property="takeTraderContactName" jdbcType="VARCHAR"/>
        <result column="TAKE_TRADER_CONTACT_MOBILE" property="takeTraderContactMobile" jdbcType="VARCHAR"/>
        <result column="TAKE_TRADER_CONTACT_TELEPHONE" property="takeTraderContactTelephone" jdbcType="VARCHAR"/>
        <result column="TAKE_TRADER_ADDRESS_ID" property="takeTraderAddressId" jdbcType="INTEGER"/>
        <result column="TAKE_TRADER_ADDRESS" property="takeTraderAddress" jdbcType="VARCHAR"/>
        <result column="PAYMENT_TYPE" property="paymentType" jdbcType="INTEGER"/>
        <result column="PREPAID_AMOUNT" property="prepaidAmount" jdbcType="DECIMAL"/>
        <result column="ACCOUNT_PERIOD_AMOUNT" property="accountPeriodAmount" jdbcType="DECIMAL"/>
        <result column="RETAINAGE_AMOUNT" property="retainageAmount" jdbcType="DECIMAL"/>
        <result column="RETAINAGE_AMOUNT_MONTH" property="retainageAmountMonth" jdbcType="INTEGER"/>
        <result column="LOGISTICS_ID" property="logisticsId" jdbcType="INTEGER"/>
        <result column="INVOICE_TYPE" property="invoiceType" jdbcType="INTEGER"/>
        <result column="FREIGHT_DESCRIPTION" property="freightDescription" jdbcType="INTEGER"/>
        <result column="PAYMENT_COMMENTS" property="paymentComments" jdbcType="VARCHAR"/>
        <result column="LOGISTICS_COMMENTS" property="logisticsComments" jdbcType="VARCHAR"/>
        <result column="INVOICE_COMMENTS" property="invoiceComments" jdbcType="VARCHAR"/>
        <result column="COMMENTS" property="comments" jdbcType="VARCHAR"/>
        <result column="ADDITIONAL_CLAUSE" property="additionalClause" jdbcType="VARCHAR"/>
        <result column="ADD_TIME" property="addTime" jdbcType="BIGINT"/>
        <result column="CREATOR" property="creator" jdbcType="INTEGER"/>
        <result column="MOD_TIME" property="modTime" jdbcType="BIGINT"/>
        <result column="UPDATER" property="updater" jdbcType="INTEGER"/>
        <result column="TAKE_TRADER_AREA" property="takeTraderArea" jdbcType="VARCHAR"/>
        <result column="TRADER_AREA" property="traderArea" jdbcType="VARCHAR"/>
        <result column="SATISFY_DELIVERY_TIME" property="satisfyDeliveryTime" jdbcType="BIGINT"/>
        <result column="ESTIMATE_ARRIVAL_TIME" property="estimateArrivalTime" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap id="BuyorderResultMap" type="com.vedeng.order.model.vo.BuyorderVo" extends="BuyorderBaseResultMap">
        <result column="INVOICE_TYPE_STR" property="invoiceTypeStr" jdbcType="VARCHAR"/>
        <result column="LOCKED_STATUS" property="lockedStatus" jdbcType="INTEGER"/>
        <collection property="buyorderGoodsVoList" javaType="java.util.ArrayList"
                    ofType="com.vedeng.order.model.vo.BuyorderGoodsVo">
            <id column="BUYORDER_GOODS_ID" property="buyorderGoodsId" jdbcType="INTEGER"/>
            <result column="GOODS_ID" property="goodsId" jdbcType="INTEGER"/>
            <result column="SKU" property="sku" jdbcType="VARCHAR"/>
            <result column="GOODS_NAME" property="goodsName" jdbcType="VARCHAR"/>
            <result column="BRAND_NAME" property="brandName" jdbcType="VARCHAR"/>
            <result column="MODEL" property="model" jdbcType="VARCHAR"/>
            <result column="UNIT_NAME" property="unitName" jdbcType="VARCHAR"/>
            <result column="PRICE" property="price" jdbcType="DECIMAL"/>
            <result column="NUM" property="num" jdbcType="INTEGER"/>
            <result column="ESTIMATE_ARRIVAL_TIME" property="estimateArrivalTime" jdbcType="BIGINT"/>
            <result column="ARRIVAL_NUM" property="arrivalNum" jdbcType="INTEGER"/>
            <result column="MATERIAL_CODE" property="materialCode" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        HX_INVOICE_ID, INVOICE_TYPE, INVOICE_CATEGORY, INVOICE_CODE, INVOICE_NUM, SALER_TAX_NUM, SALER_NAME,
    SALER_ADDRESS, SALER_TEL, SALER_BANK, SALER_BANK_ACCOUNT, BUYER_TAX_NUM, BUYER_NAME,
    BUYER_ADDRESS, BUYER_TEL, BUYER_BANK, BUYER_BANK_ACCOUNT, CREATE_TIME, INVOICE_AMOUNT,
    TAX_AMOUNT, AMOUNT, TAX_RATE, `COMMENT`, CREATOR, CHECKER, PAYEE, ATTACHMENT, INVOICE_STATUS,
    INVOICE_REFUND_STATUS, ADD_TIME, UPDATE_TIME, UPDATER, COLOR_TYPE, TRADER_ID
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from T_HX_INVOICE
        where HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from T_HX_INVOICE
        where HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="HX_INVOICE_ID" keyProperty="hxInvoiceId" parameterType="com.vedeng.finance.model.HxInvoiceDetail" useGeneratedKeys="true">
        insert into T_HX_INVOICE (INVOICE_CATEGORY, INVOICE_TYPE, INVOICE_CODE, INVOICE_NUM,
                                  SALER_TAX_NUM, SALER_NAME, SALER_ADDRESS,
                                  SALER_TEL, SALER_BANK, SALER_BANK_ACCOUNT,
                                  BUYER_TAX_NUM, BUYER_NAME, BUYER_ADDRESS,
                                  BUYER_TEL, BUYER_BANK, BUYER_BANK_ACCOUNT,
                                  CREATE_TIME, INVOICE_AMOUNT, TAX_AMOUNT,
                                  AMOUNT, TAX_RATE, `COMMENT`,
                                  CREATOR, CHECKER, PAYEE,
                                  ATTACHMENT, INVOICE_STATUS, INVOICE_REFUND_STATUS,
                                  ADD_TIME, UPDATE_TIME, UPDATER,
                                  COLOR_TYPE,TRADER_ID)
        values (#{invoiceCategory,jdbcType=VARCHAR},#{invoiceType,jdbcType=INTEGER}, #{invoiceCode,jdbcType=VARCHAR}, #{invoiceNum,jdbcType=VARCHAR},
                #{salerTaxNum,jdbcType=VARCHAR}, #{salerName,jdbcType=VARCHAR}, #{salerAddress,jdbcType=VARCHAR},
                #{salerTel,jdbcType=VARCHAR}, #{salerBank,jdbcType=VARCHAR}, #{salerBankAccount,jdbcType=VARCHAR},
                #{buyerTaxNum,jdbcType=VARCHAR}, #{buyerName,jdbcType=VARCHAR}, #{buyerAddress,jdbcType=VARCHAR},
                #{buyerTel,jdbcType=VARCHAR}, #{buyerBank,jdbcType=VARCHAR}, #{buyerBankAccount,jdbcType=VARCHAR},
                #{createTime,jdbcType=BIGINT}, #{invoiceAmount,jdbcType=DECIMAL}, #{taxAmount,jdbcType=DECIMAL},
                #{amount,jdbcType=DECIMAL}, #{taxRate,jdbcType=INTEGER}, #{comment,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR}, #{checker,jdbcType=VARCHAR}, #{payee,jdbcType=VARCHAR},
                #{attachment,jdbcType=VARCHAR}, #{invoiceStatus,jdbcType=TINYINT}, #{invoiceRefundStatus,jdbcType=TINYINT},
                #{addTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER},
                #{colorType,jdbcType=INTEGER},#{traderId,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" keyColumn="HX_INVOICE_ID" keyProperty="hxInvoiceId" parameterType="com.vedeng.finance.model.HxInvoiceDetail" useGeneratedKeys="true">
        insert into T_HX_INVOICE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="invoiceType != null">
                INVOICE_TYPE,
            </if>
            <if test="invoiceCategory != null">
                INVOICE_CATEGORY,
            </if>
            <if test="invoiceCode != null">
                INVOICE_CODE,
            </if>
            <if test="invoiceNum != null">
                INVOICE_NUM,
            </if>
            <if test="salerTaxNum != null">
                SALER_TAX_NUM,
            </if>
            <if test="salerName != null">
                SALER_NAME,
            </if>
            <if test="salerAddress != null">
                SALER_ADDRESS,
            </if>
            <if test="salerTel != null">
                SALER_TEL,
            </if>
            <if test="salerBank != null">
                SALER_BANK,
            </if>
            <if test="salerBankAccount != null">
                SALER_BANK_ACCOUNT,
            </if>
            <if test="buyerTaxNum != null">
                BUYER_TAX_NUM,
            </if>
            <if test="buyerName != null">
                BUYER_NAME,
            </if>
            <if test="buyerAddress != null">
                BUYER_ADDRESS,
            </if>
            <if test="buyerTel != null">
                BUYER_TEL,
            </if>
            <if test="buyerBank != null">
                BUYER_BANK,
            </if>
            <if test="buyerBankAccount != null">
                BUYER_BANK_ACCOUNT,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="invoiceAmount != null">
                INVOICE_AMOUNT,
            </if>
            <if test="taxAmount != null">
                TAX_AMOUNT,
            </if>
            <if test="amount != null">
                AMOUNT,
            </if>
            <if test="taxRate != null">
                TAX_RATE,
            </if>
            <if test="comment != null">
                `COMMENT`,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="checker != null">
                CHECKER,
            </if>
            <if test="payee != null">
                PAYEE,
            </if>
            <if test="attachment != null">
                ATTACHMENT,
            </if>
            <if test="invoiceStatus != null">
                INVOICE_STATUS,
            </if>
            <if test="invoiceRefundStatus != null">
                INVOICE_REFUND_STATUS,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="updateTime != null">
                UPDATE_TIME,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="colorType != null">
                COLOR_TYPE,
            </if>
            <if test="traderId != null">
                TRADER_ID,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="invoiceType != null">
                #{invoiceType,jdbcType=INTEGER},
            </if>
            <if test="invoiceCategory != null">
                #{invoiceCategory,jdbcType=VARCHAR},
            </if>
            <if test="invoiceCode != null">
                #{invoiceCode,jdbcType=VARCHAR},
            </if>
            <if test="invoiceNum != null">
                #{invoiceNum,jdbcType=VARCHAR},
            </if>
            <if test="salerTaxNum != null">
                #{salerTaxNum,jdbcType=VARCHAR},
            </if>
            <if test="salerName != null">
                #{salerName,jdbcType=VARCHAR},
            </if>
            <if test="salerAddress != null">
                #{salerAddress,jdbcType=VARCHAR},
            </if>
            <if test="salerTel != null">
                #{salerTel,jdbcType=VARCHAR},
            </if>
            <if test="salerBank != null">
                #{salerBank,jdbcType=VARCHAR},
            </if>
            <if test="salerBankAccount != null">
                #{salerBankAccount,jdbcType=VARCHAR},
            </if>
            <if test="buyerTaxNum != null">
                #{buyerTaxNum,jdbcType=VARCHAR},
            </if>
            <if test="buyerName != null">
                #{buyerName,jdbcType=VARCHAR},
            </if>
            <if test="buyerAddress != null">
                #{buyerAddress,jdbcType=VARCHAR},
            </if>
            <if test="buyerTel != null">
                #{buyerTel,jdbcType=VARCHAR},
            </if>
            <if test="buyerBank != null">
                #{buyerBank,jdbcType=VARCHAR},
            </if>
            <if test="buyerBankAccount != null">
                #{buyerBankAccount,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="invoiceAmount != null">
                #{invoiceAmount,jdbcType=DECIMAL},
            </if>
            <if test="taxAmount != null">
                #{taxAmount,jdbcType=DECIMAL},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="taxRate != null">
                #{taxRate,jdbcType=INTEGER},
            </if>
            <if test="comment != null">
                #{comment,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="checker != null">
                #{checker,jdbcType=VARCHAR},
            </if>
            <if test="payee != null">
                #{payee,jdbcType=VARCHAR},
            </if>
            <if test="attachment != null">
                #{attachment,jdbcType=VARCHAR},
            </if>
            <if test="invoiceStatus != null">
                #{invoiceStatus,jdbcType=TINYINT},
            </if>
            <if test="invoiceRefundStatus != null">
                #{invoiceRefundStatus,jdbcType=TINYINT},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="colorType != null">
                #{colorType,jdbcType=INTEGER},
            </if>
            <if test="traderId != null">
                #{traderId,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.finance.model.HxInvoiceDetail">
        update T_HX_INVOICE
        <set>
            <if test="invoiceType != null">
                INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
            </if>
            <if test="invoiceCategory != null">
                INVOICE_CATEGORY = #{invoiceCategory,jdbcType=VARCHAR},
            </if>
            <if test="invoiceCode != null">
                INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR},
            </if>
            <if test="invoiceNum != null">
                INVOICE_NUM = #{invoiceNum,jdbcType=VARCHAR},
            </if>
            <if test="salerTaxNum != null">
                SALER_TAX_NUM = #{salerTaxNum,jdbcType=VARCHAR},
            </if>
            <if test="salerName != null">
                SALER_NAME = #{salerName,jdbcType=VARCHAR},
            </if>
            <if test="salerAddress != null">
                SALER_ADDRESS = #{salerAddress,jdbcType=VARCHAR},
            </if>
            <if test="salerTel != null">
                SALER_TEL = #{salerTel,jdbcType=VARCHAR},
            </if>
            <if test="salerBank != null">
                SALER_BANK = #{salerBank,jdbcType=VARCHAR},
            </if>
            <if test="salerBankAccount != null">
                SALER_BANK_ACCOUNT = #{salerBankAccount,jdbcType=VARCHAR},
            </if>
            <if test="buyerTaxNum != null">
                BUYER_TAX_NUM = #{buyerTaxNum,jdbcType=VARCHAR},
            </if>
            <if test="buyerName != null">
                BUYER_NAME = #{buyerName,jdbcType=VARCHAR},
            </if>
            <if test="buyerAddress != null">
                BUYER_ADDRESS = #{buyerAddress,jdbcType=VARCHAR},
            </if>
            <if test="buyerTel != null">
                BUYER_TEL = #{buyerTel,jdbcType=VARCHAR},
            </if>
            <if test="buyerBank != null">
                BUYER_BANK = #{buyerBank,jdbcType=VARCHAR},
            </if>
            <if test="buyerBankAccount != null">
                BUYER_BANK_ACCOUNT = #{buyerBankAccount,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="invoiceAmount != null">
                INVOICE_AMOUNT = #{invoiceAmount,jdbcType=DECIMAL},
            </if>
            <if test="taxAmount != null">
                TAX_AMOUNT = #{taxAmount,jdbcType=DECIMAL},
            </if>
            <if test="amount != null">
                AMOUNT = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="taxRate != null">
                TAX_RATE = #{taxRate,jdbcType=INTEGER},
            </if>
            <if test="comment != null">
                `COMMENT` = #{comment,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="checker != null">
                CHECKER = #{checker,jdbcType=VARCHAR},
            </if>
            <if test="payee != null">
                PAYEE = #{payee,jdbcType=VARCHAR},
            </if>
            <if test="attachment != null">
                ATTACHMENT = #{attachment,jdbcType=VARCHAR},
            </if>
            <if test="invoiceStatus != null">
                INVOICE_STATUS = #{invoiceStatus,jdbcType=TINYINT},
            </if>
            <if test="invoiceRefundStatus != null">
                INVOICE_REFUND_STATUS = #{invoiceRefundStatus,jdbcType=TINYINT},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="colorType != null">
                COLOR_TYPE = #{colorType,jdbcType=INTEGER},
            </if>
            <if test="traderId != null">
                TRADER_ID = #{traderId,jdbcType=INTEGER},
            </if>
        </set>
        where HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.finance.model.HxInvoiceDetail">
        update T_HX_INVOICE
        set INVOICE_CATEGORY = #{invoiceCategory,jdbcType=VARCHAR},
            INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
            INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR},
            INVOICE_NUM = #{invoiceNum,jdbcType=VARCHAR},
            SALER_TAX_NUM = #{salerTaxNum,jdbcType=VARCHAR},
            SALER_NAME = #{salerName,jdbcType=VARCHAR},
            SALER_ADDRESS = #{salerAddress,jdbcType=VARCHAR},
            SALER_TEL = #{salerTel,jdbcType=VARCHAR},
            SALER_BANK = #{salerBank,jdbcType=VARCHAR},
            SALER_BANK_ACCOUNT = #{salerBankAccount,jdbcType=VARCHAR},
            BUYER_TAX_NUM = #{buyerTaxNum,jdbcType=VARCHAR},
            BUYER_NAME = #{buyerName,jdbcType=VARCHAR},
            BUYER_ADDRESS = #{buyerAddress,jdbcType=VARCHAR},
            BUYER_TEL = #{buyerTel,jdbcType=VARCHAR},
            BUYER_BANK = #{buyerBank,jdbcType=VARCHAR},
            BUYER_BANK_ACCOUNT = #{buyerBankAccount,jdbcType=VARCHAR},
            CREATE_TIME = #{createTime,jdbcType=BIGINT},
            INVOICE_AMOUNT = #{invoiceAmount,jdbcType=DECIMAL},
            TAX_AMOUNT = #{taxAmount,jdbcType=DECIMAL},
            AMOUNT = #{amount,jdbcType=DECIMAL},
            TAX_RATE = #{taxRate,jdbcType=INTEGER},
            `COMMENT` = #{comment,jdbcType=VARCHAR},
            CREATOR = #{creator,jdbcType=VARCHAR},
            CHECKER = #{checker,jdbcType=VARCHAR},
            PAYEE = #{payee,jdbcType=VARCHAR},
            ATTACHMENT = #{attachment,jdbcType=VARCHAR},
            INVOICE_STATUS = #{invoiceStatus,jdbcType=TINYINT},
            INVOICE_REFUND_STATUS = #{invoiceRefundStatus,jdbcType=TINYINT},
            ADD_TIME = #{addTime,jdbcType=BIGINT},
            UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
            UPDATER = #{updater,jdbcType=INTEGER},
            COLOR_TYPE = #{colorType,jdbcType=INTEGER}
        where HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER}
    </update>


    <select id="getHxInvoiceAllListPage" resultMap="BaseResultMap">
        SELECT
        P.*
        FROM
        (
        SELECT
            HI.HX_INVOICE_ID,
            HI.INVOICE_TYPE,
            HI.INVOICE_CATEGORY,
            HI.INVOICE_CODE,
            HI.INVOICE_NUM,
            HI.SALER_TAX_NUM,
            HI.SALER_NAME,
            HI.SALER_ADDRESS,
            HI.SALER_TEL,
            HI.SALER_BANK,
            HI.SALER_BANK_ACCOUNT,
            HI.BUYER_TAX_NUM,
            HI.BUYER_NAME,
            HI.BUYER_ADDRESS,
            HI.BUYER_TEL,
            HI.BUYER_BANK,
            HI.BUYER_BANK_ACCOUNT,
            HI.CREATE_TIME,
            HI.INVOICE_AMOUNT,
            HI.TAX_AMOUNT,
            HI.AMOUNT,
            HI.TAX_RATE,
            HI.COMMENT,
            HI.CREATOR,
            HI.CHECKER,
            HI.PAYEE,
            HI.ATTACHMENT,
            HI.INVOICE_STATUS,
            HI.INVOICE_REFUND_STATUS,
            HI.ADD_TIME,
            HI.UPDATE_TIME,
            HI.UPDATER,
            HI.COLOR_TYPE,
            HI.TRADER_ID,
            HI.IS_AUTH HX_IS_AUTH,
            CASE
            TI.TYPE
            WHEN 503 THEN
            TB.BUYORDER_NO
            WHEN 504 THEN
            TAS.AFTER_SALES_NO
            WHEN 4126 THEN
            TBE.BUYORDER_EXPENSE_NO
            ELSE '' END BUYORDER_NO,
            CASE
            TI.TYPE
            WHEN 503 THEN
            TB.BUYORDER_ID
            WHEN 504 THEN
            TAS.AFTER_SALES_ID
            WHEN 4126 THEN
            TBE.BUYORDER_EXPENSE_ID
             ELSE '' END BUYORDER_ID,
            TI.TYPE,
            TI.ADD_TIME AS ENTRY_TIME,
            TI.CREATOR AS ENTRY_USER_ID,
            TI.IS_MONTH_AUTH,
            TI.IS_AUTH,
            TI.VALID_TIME,
            TI.VALID_USERID AS VALID_USER_ID,
            TI.VALID_STATUS,
            TI.VALID_COMMENTS,
            CASE
            HI.INVOICE_STATUS
            WHEN 5 THEN
            HI.AUTH_TIME ELSE TI.AUTH_TIME
            END AUTH_TIME,
            CASE
            HI.INVOICE_STATUS
            WHEN 5 THEN
            HI.IS_AUTH ELSE TI.IS_AUTH
            END AUTH_STATUS,
            TI.IS_MONTH_AUTH AS AUTH_MONTH,
            TFVN.FINANCE_VOUCHER_NO
        FROM T_HX_INVOICE HI
        LEFT JOIN (SELECT MAX(INVOICE_ID) AS K, HX_INVOICE_ID FROM T_INVOICE WHERE HX_INVOICE_ID > 0 AND INVOICE_FROM = 1 GROUP BY HX_INVOICE_ID ) A ON A.HX_INVOICE_ID = HI.HX_INVOICE_ID
        LEFT JOIN T_INVOICE TI ON TI.INVOICE_ID = A.K
        <choose>
            <when test="searchDTO != null and searchDTO.sendResult != null">
                LEFT JOIN
                (
                SELECT
                A.INVOICE_ID,
                B.FINANCE_VOUCHER_NO
                FROM T_INVOICE A
                RIGHT JOIN T_FINANCE_VOUCHER_NO B ON A.INVOICE_ID = B.ID
                WHERE
                B.TYPE = 1
                AND
                A.TYPE = 503
                GROUP BY
                A.INVOICE_ID
                )
                as TFVN ON TI.HX_INVOICE_ID = TFVN.INVOICE_ID
            </when>
            <otherwise>
                LEFT JOIN T_FINANCE_VOUCHER_NO TFVN on TI.INVOICE_ID = TFVN.ID AND TFVN.TYPE = 1
            </otherwise>
        </choose>
        LEFT JOIN T_BUYORDER TB on TI.RELATED_ID = TB.BUYORDER_ID
        LEFT JOIN T_BUYORDER_EXPENSE TBE ON TI.RELATED_ID = TBE.BUYORDER_EXPENSE_ID
        LEFT JOIN T_AFTER_SALES TAS ON  TI.RELATED_ID = TAS.AFTER_SALES_ID
        ) P
        GROUP BY
        P.HX_INVOICE_ID
        <trim prefixOverrides="AND" prefix="HAVING">
            <if test="searchDTO != null and searchDTO.keyword != null">
                (P.INVOICE_CODE LIKE CONCAT('%',#{searchDTO.keyword,jdbcType=VARCHAR},'%')
                or P.INVOICE_NUM LIKE CONCAT('%',#{searchDTO.keyword,jdbcType=VARCHAR},'%')
                or P.SALER_NAME LIKE CONCAT('%',#{searchDTO.keyword,jdbcType=VARCHAR},'%')
                or P.FINANCE_VOUCHER_NO LIKE CONCAT('%',#{searchDTO.keyword,jdbcType=VARCHAR},'%')
                or P.BUYORDER_NO LIKE CONCAT('%',#{searchDTO.keyword,jdbcType=VARCHAR},'%'))
            </if>
            <if test="searchDTO != null and searchDTO.invoiceStatus != null and searchDTO.invoiceStatus != -1">
                AND P.INVOICE_STATUS = #{searchDTO.invoiceStatus}
            </if>
            <if test="searchDTO != null and searchDTO.timeSearchType != null and (searchDTO.startTime != null or searchDTO.endTime != null)">
                <choose>
                    <when test="searchDTO.timeSearchType == 1">
                        <if test="searchDTO.startTime != null">
                            AND  P.CREATE_TIME <![CDATA[ >= ]]> #{searchDTO.startTime}
                        </if>
                        <if test="searchDTO.endTime != null">
                            AND P.CREATE_TIME <![CDATA[ <= ]]> #{searchDTO.endTime}
                        </if>
                    </when>
                    <when test="searchDTO.timeSearchType == 2">
                        <if test="searchDTO.startTime != null">
                            AND P.ADD_TIME <![CDATA[ >= ]]> #{searchDTO.startTime}
                        </if>
                        <if test="searchDTO.endTime != null">
                            AND P.ADD_TIME <![CDATA[ <= ]]> #{searchDTO.endTime}
                        </if>
                    </when>
                    <when test="searchDTO.timeSearchType == 3">
                        <if test="searchDTO.startTime != null">
                            AND P.VALID_TIME <![CDATA[ >= ]]> #{searchDTO.startTime}
                        </if>
                        <if test="searchDTO.endTime != null">
                            AND P.VALID_TIME <![CDATA[ <= ]]> #{searchDTO.endTime}
                        </if>
                    </when>
                    <when test="searchDTO.timeSearchType == 4">
                        <if test="searchDTO.startTime != null">
                            AND P.AUTH_TIME <![CDATA[ >= ]]> #{searchDTO.startTime}
                        </if>
                        <if test="searchDTO.endTime != null">
                            AND P.AUTH_TIME <![CDATA[ <= ]]> #{searchDTO.endTime}
                        </if>
                    </when>
                </choose>
            </if>
            <if test="searchDTO != null and searchDTO.invoiceAmountFrom">
                AND  P.AMOUNT <![CDATA[ >= ]]> #{searchDTO.invoiceAmountFrom}
            </if>
            <if test="searchDTO != null and searchDTO.invoiceAmountTo != null">
                AND P.AMOUNT <![CDATA[ <= ]]> #{searchDTO.invoiceAmountTo}
            </if>
            <if test="searchDTO != null and searchDTO.invoiceTaxRate != null">
                AND  P.TAX_RATE = #{searchDTO.invoiceTaxRate}
            </if>
            <if test="searchDTO != null and searchDTO.invoiceCategory != null and searchDTO.invoiceCategory != ''">
                AND P.INVOICE_CATEGORY = #{searchDTO.invoiceCategory,jdbcType=INTEGER}
            </if>
            <if test="searchDTO != null and searchDTO.colorType != null">
                AND P.COLOR_TYPE = #{searchDTO.colorType}
            </if>
            <if test="searchDTO != null and searchDTO.entryUser != null">
                AND P.ENTRY_USER_ID = #{searchDTO.entryUser}
            </if>
            <if test="searchDTO != null and searchDTO.validUser != null">
                AND P.VALID_USER_ID = #{searchDTO.validUser}
            </if>
            <if test="searchDTO != null and searchDTO.validStatus != null">
                AND P.VALID_STATUS = #{searchDTO.validStatus}
            </if>
            <if test="searchDTO != null and searchDTO.authStatus != null  and searchDTO.authStatus != 0">
                AND (P.IS_AUTH = #{searchDTO.authStatus,jdbcType=TINYINT}
                OR  P.HX_IS_AUTH = #{searchDTO.authStatus,jdbcType=TINYINT})
            </if>
            <if test="searchDTO != null and searchDTO.authStatus != null  and searchDTO.authStatus == 0">
                AND ( P.IS_AUTH = 0 OR P.IS_AUTH = 0 OR P.HX_IS_AUTH IS NULL )
            </if>
            <if test="searchDTO != null and searchDTO.authMonth != null">
                AND  P.IS_MONTH_AUTH = #{searchDTO.authMonth}
            </if>
            <if test="searchDTO != null and searchDTO.sendResult != null">
                <choose>
                    <when test="searchDTO.sendResult == 1">
                        AND P.FINANCE_VOUCHER_NO IS NOT NULL
                    </when>
                    <otherwise>
                        AND  P.FINANCE_VOUCHER_NO IS NULL
                    </otherwise>
                </choose>
            </if>
        </trim>
        ORDER BY
        P.CREATE_TIME DESC
        </select>

    <select id="getHxInvoiceWaitListPage" resultType="com.vedeng.finance.vo.HxInvoiceVo">
        SELECT INVOICE_CODE, INVOICE_NUM, SALER_NAME, AMOUNT, INVOICE_AMOUNT, TAX_AMOUNT, TAX_RATE, CREATE_TIME,
        COLOR_TYPE, ATTACHMENT,HX_INVOICE_ID,INVOICE_CATEGORY
        FROM T_HX_INVOICE
        WHERE INVOICE_STATUS = 4
        <if test="searchDTO != null and searchDTO.keyword != null">
            AND (INVOICE_CODE LIKE CONCAT('%',#{searchDTO.keyword,jdbcType=VARCHAR},'%')
            OR INVOICE_NUM LIKE CONCAT('%',#{searchDTO.keyword,jdbcType=VARCHAR},'%')
            OR SALER_NAME LIKE CONCAT('%',#{searchDTO.keyword,jdbcType=VARCHAR},'%'))
        </if>
        <if test="searchDTO != null and searchDTO.startTime != null">
            AND CREATE_TIME <![CDATA[ >= ]]> #{searchDTO.startTime,jdbcType=BIGINT}
        </if>
        <if test="searchDTO != null and searchDTO.endTime != null">
            AND CREATE_TIME <![CDATA[ <= ]]> #{searchDTO.endTime,jdbcType=BIGINT}
        </if>
        <if test="searchDTO != null and searchDTO.invoiceAmountFrom">
            AND AMOUNT <![CDATA[ >= ]]> #{searchDTO.invoiceAmountFrom}
        </if>
        <if test="searchDTO != null and searchDTO.invoiceAmountTo != null">
            AND AMOUNT <![CDATA[ <= ]]> #{searchDTO.invoiceAmountTo}
        </if>
        <if test="searchDTO != null and searchDTO.invoiceTaxRate != null">
            AND TAX_RATE = #{searchDTO.invoiceTaxRate}
        </if>
        <if test="searchDTO != null and searchDTO.colorType != null">
            AND COLOR_TYPE = #{searchDTO.colorType}
        </if>
        <if test="searchDTO != null and searchDTO.invoiceCategory != null and searchDTO.invoiceCategory != ''">
            AND INVOICE_CATEGORY = #{searchDTO.invoiceCategory,jdbcType=INTEGER}
        </if>
        ORDER BY
        CREATE_TIME ASC
    </select>

    <select id="getHxInvoiceCostListPage" resultType="com.vedeng.finance.vo.HxInvoiceVo">
        SELECT
            INVOICE_CODE,
            INVOICE_NUM,
            SALER_NAME,
            AMOUNT,
            INVOICE_AMOUNT,
            TAX_AMOUNT,
            TAX_RATE,
            CREATE_TIME,
            COLOR_TYPE,
            ATTACHMENT,
            HX_INVOICE_ID,
            INVOICE_CATEGORY,
            IS_AUTHING,
            IS_AUTH AS AUTH_STATUS,
            AUTH_FAIL_REASON,
            AUTH_TIME,
            AUTH_MODE
        FROM
        T_HX_INVOICE
        WHERE
        INVOICE_STATUS = 5
        <if test="searchDTO != null and searchDTO.keyword != null">
            AND (INVOICE_CODE LIKE CONCAT('%',#{searchDTO.keyword,jdbcType=VARCHAR},'%')
            OR INVOICE_NUM LIKE CONCAT('%',#{searchDTO.keyword,jdbcType=VARCHAR},'%')
            OR SALER_NAME LIKE CONCAT('%',#{searchDTO.keyword,jdbcType=VARCHAR},'%'))
        </if>
        <if test="searchDTO != null and searchDTO.startTime != null">
            AND CREATE_TIME <![CDATA[ >= ]]> #{searchDTO.startTime,jdbcType=BIGINT}
        </if>
        <if test="searchDTO != null and searchDTO.endTime != null">
            AND CREATE_TIME <![CDATA[ <= ]]> #{searchDTO.endTime,jdbcType=BIGINT}
        </if>
        <if test="searchDTO != null and searchDTO.invoiceAmountFrom">
            AND AMOUNT <![CDATA[ >= ]]> #{searchDTO.invoiceAmountFrom,jdbcType=DECIMAL}
        </if>
        <if test="searchDTO != null and searchDTO.invoiceAmountTo != null">
            AND AMOUNT <![CDATA[ <= ]]> #{searchDTO.invoiceAmountTo,jdbcType=DECIMAL}
        </if>
        <if test="searchDTO != null and searchDTO.invoiceTaxRate != null">
            AND TAX_RATE = #{searchDTO.invoiceTaxRate,jdbcType=INTEGER}
        </if>
        <if test="searchDTO != null and searchDTO.colorType != null">
            AND COLOR_TYPE = #{searchDTO.colorType,jdbcType=INTEGER}
        </if>
        <if test="searchDTO != null and searchDTO.invoiceCategory != null and searchDTO.invoiceCategory != ''">
            AND INVOICE_CATEGORY = #{searchDTO.invoiceCategory,jdbcType=INTEGER}
        </if>
        <if test="searchDTO != null and searchDTO.authStatus != null and searchDTO.authStatus != 0">
            AND IS_AUTH = #{searchDTO.authStatus,jdbcType=TINYINT}
        </if>
        <if test="searchDTO != null and searchDTO.authStatus != null and searchDTO.authStatus == 0">
            AND (IS_AUTH = 0 OR IS_AUTH = 0 OR IS_AUTH IS NULL )
        </if>
        <if test="searchDTO != null and searchDTO.authMode != null">
            AND IS_AUTH != 0
            AND AUTH_MODE = #{searchDTO.authMode,jdbcType=TINYINT}
        </if>
        ORDER BY
        CREATE_TIME DESC
    </select>
    <select id="getHxInvoiceExceptionListPage" resultType="com.vedeng.finance.vo.HxInvoiceVo">
        SELECT INVOICE_CODE, INVOICE_NUM, SALER_NAME, AMOUNT, CREATE_TIME, SALER_BANK, SALER_BANK_ACCOUNT,
        SALER_ADDRESS, SALER_TEL, ATTACHMENT,HX_INVOICE_ID,COLOR_TYPE,BUYER_TAX_NUM,BUYER_NAME,BUYER_ADDRESS,
        BUYER_TEL,BUYER_BANK,BUYER_BANK_ACCOUNT
        FROM T_HX_INVOICE
        WHERE INVOICE_STATUS = 6
        <if test="searchDTO != null and searchDTO.keyword != null">
            AND (INVOICE_CODE LIKE CONCAT('%',#{searchDTO.keyword,jdbcType=VARCHAR},'%')
            OR INVOICE_NUM LIKE CONCAT('%',#{searchDTO.keyword,jdbcType=VARCHAR},'%')
            OR SALER_NAME LIKE CONCAT('%',#{searchDTO.keyword,jdbcType=VARCHAR},'%'))
        </if>
        <if test="searchDTO != null and searchDTO.startTime != null">
            AND CREATE_TIME <![CDATA[ >= ]]> #{searchDTO.startTime,jdbcType=BIGINT}
        </if>
        <if test="searchDTO != null and searchDTO.endTime != null">
            AND CREATE_TIME <![CDATA[ <= ]]> #{searchDTO.endTime,jdbcType=BIGINT}
        </if>
        <if test="searchDTO != null and searchDTO.invoiceAmountFrom">
            AND AMOUNT <![CDATA[ >= ]]> #{searchDTO.invoiceAmountFrom}
        </if>
        <if test="searchDTO != null and searchDTO.invoiceAmountTo != null">
            AND AMOUNT <![CDATA[ <= ]]> #{searchDTO.invoiceAmountTo}
        </if>
        ORDER BY
        CREATE_TIME DESC
    </select>
    <select id="getHxInvoiceNegativeListPage" resultType="com.vedeng.finance.vo.HxInvoiceVo">
        SELECT INVOICE_CODE, INVOICE_NUM, SALER_NAME, AMOUNT, INVOICE_AMOUNT, TAX_AMOUNT, TAX_RATE, CREATE_TIME,
        COLOR_TYPE, ATTACHMENT,HX_INVOICE_ID,INVOICE_CATEGORY
        FROM T_HX_INVOICE
        WHERE INVOICE_STATUS = 7
        <if test="searchDTO != null and searchDTO.keyword != null">
            AND (INVOICE_CODE LIKE CONCAT('%',#{searchDTO.keyword,jdbcType=VARCHAR},'%')
            OR INVOICE_NUM LIKE CONCAT('%',#{searchDTO.keyword,jdbcType=VARCHAR},'%')
            OR SALER_NAME LIKE CONCAT('%',#{searchDTO.keyword,jdbcType=VARCHAR},'%'))
        </if>
        <if test="searchDTO != null and searchDTO.startTime != null">
            AND CREATE_TIME <![CDATA[ >= ]]> #{searchDTO.startTime,jdbcType=BIGINT}
        </if>
        <if test="searchDTO != null and searchDTO.endTime != null">
            AND CREATE_TIME <![CDATA[ <= ]]> #{searchDTO.endTime,jdbcType=BIGINT}
        </if>
        <if test="searchDTO != null and searchDTO.invoiceAmountFrom">
            AND AMOUNT <![CDATA[ >= ]]> #{searchDTO.invoiceAmountFrom}
        </if>
        <if test="searchDTO != null and searchDTO.invoiceAmountTo != null">
            AND AMOUNT <![CDATA[ <= ]]> #{searchDTO.invoiceAmountTo}
        </if>
        <if test="searchDTO != null and searchDTO.invoiceTaxRate != null">
            AND TAX_RATE = #{searchDTO.invoiceTaxRate}
        </if>
        <if test="searchDTO != null and searchDTO.colorType != null">
            AND COLOR_TYPE = #{searchDTO.colorType}
        </if>
        <if test="searchDTO != null and searchDTO.invoiceCategory != null and searchDTO.invoiceCategory != ''">
            AND INVOICE_CATEGORY = #{searchDTO.invoiceCategory,jdbcType=INTEGER}
        </if>
        ORDER BY
        CREATE_TIME DESC
    </select>

    <select id="getSupplyHxInvoiceWaitListPage" resultType="com.vedeng.finance.vo.HxInvoiceVo">
        SELECT
        T1.HX_INVOICE_ID,
        T1.INVOICE_NUM,
        T1.INVOICE_CODE,
        T1.SALER_NAME,
        T1.AMOUNT,
        T1.TAX_RATE,
        T1.ADD_TIME,
        T1.ATTACHMENT,
        T1.CREATE_TIME,
        T1.TRADER_ID,
        T1.INVOICE_CATEGORY,
        T1.COMMENT,
        T4.USER_ID ORDER_USER_ID,
        SUM(IFNULL( T2.HAS_ENTRY_COUNT, 0 ) * IFNULL( T3.PRICE, 0 )) RECORDED_AMOUNT
        FROM
        T_HX_INVOICE T1
        LEFT JOIN T_INVOICE_ENTRY_STASH T2 ON T1.HX_INVOICE_ID = T2.HX_INVOICE_ID
        AND T2.IS_DELETE = 0
        LEFT JOIN T_BUYORDER_GOODS T3 ON T2.BUYORDER_GOODS_ID = T3.BUYORDER_GOODS_ID
        AND T3.IS_DELETE = 0
        LEFT JOIN T_R_TRADER_J_USER T4 ON T1.TRADER_ID = T4.TRADER_ID
        AND T4.TRADER_TYPE = 2
        WHERE
        T1.INVOICE_STATUS = 1
        <if test="searchDTO != null and searchDTO.keyword != null and searchDTO.keyword != ''">
            AND (INVOICE_CODE LIKE CONCAT('%',#{searchDTO.keyword,jdbcType=VARCHAR},'%')
            OR INVOICE_NUM LIKE CONCAT('%',#{searchDTO.keyword,jdbcType=VARCHAR},'%')
            OR SALER_NAME LIKE CONCAT('%',#{searchDTO.keyword,jdbcType=VARCHAR},'%'))
        </if>
        <if test="searchDTO != null and searchDTO.userId != null and searchDTO.userId != 0">
            AND T4.USER_ID = #{searchDTO.userId,jdbcType=INTEGER}
        </if>
        <if test="searchDTO != null and searchDTO.invoiceTaxRate != null">
            AND T1.TAX_RATE = #{searchDTO.invoiceTaxRate,jdbcType=INTEGER}
        </if>
        <if test="searchDTO != null and searchDTO.invoiceCategory != null and searchDTO.invoiceCategory != ''">
            AND T1.INVOICE_CATEGORY = #{searchDTO.invoiceCategory,jdbcType=INTEGER}
        </if>
        <if test="searchDTO != null and searchDTO.startTime != null">
            AND T1.CREATE_TIME <![CDATA[>=]]> #{searchDTO.startTime,jdbcType=BIGINT}
        </if>
        <if test="searchDTO != null and searchDTO.endTime != null">
            AND T1.CREATE_TIME <![CDATA[<=]]> #{searchDTO.endTime,jdbcType=BIGINT}
        </if>
        <if test="searchDTO != null and searchDTO.comment != null and searchDTO.comment != ''">
            AND T1.COMMENT LIKE CONCAT('%',#{searchDTO.comment,jdbcType=VARCHAR},'%')
        </if>
        GROUP BY
        T1.HX_INVOICE_ID
        <if test="searchDTO != null and searchDTO.isRecording != null and searchDTO.isRecording == 1">
            HAVING RECORDED_AMOUNT > 0
        </if>
        ORDER BY
        T1.CREATE_TIME ASC
    </select>

    <select id="getSupplyHxInvoiceWaitOldListPage" resultType="com.vedeng.finance.vo.HxInvoiceVo">
        SELECT
            T1.HX_INVOICE_ID,
            T1.INVOICE_NUM,
            T1.INVOICE_CODE,
            T1.SALER_NAME,
            T1.AMOUNT,
            T1.TAX_RATE,
            T1.ADD_TIME,
            T1.ATTACHMENT,
            T1.CREATE_TIME,
            T1.TRADER_ID,
            T1.INVOICE_CATEGORY,
            T1.INVOICE_STATUS,
            T1.COMMENT,
            T4.USER_ID ORDER_USER_ID,
        SUM( IFNULL( T2.AMOUNT, 0 ) ) AS RECORDED_AMOUNT
        FROM
        T_HX_INVOICE T1
        LEFT JOIN T_INVOICE T2 ON T1.HX_INVOICE_ID = T2.HX_INVOICE_ID
        AND T2.INVOICE_FROM = 1
        AND T2.VALID_STATUS != 2
        AND T2.HX_INVOICE_ID > 0
        AND ((T2.COLOR_TYPE = 2 AND T2.IS_ENABLE = 1) OR T2.COLOR_COMPLEMENT_TYPE = 1)
        LEFT JOIN T_R_TRADER_J_USER T4 ON T1.TRADER_ID = T4.TRADER_ID
        AND T4.TRADER_TYPE = 2
        WHERE
        T1.INVOICE_STATUS IN (1, 5)
        <if test="searchDTO != null and searchDTO.keyword != null and searchDTO.keyword != ''">
            AND (T1.INVOICE_CODE LIKE CONCAT('%',#{searchDTO.keyword,jdbcType=VARCHAR},'%')
            OR T1.INVOICE_NUM LIKE CONCAT('%',#{searchDTO.keyword,jdbcType=VARCHAR},'%')
            OR T1.SALER_NAME LIKE CONCAT('%',#{searchDTO.keyword,jdbcType=VARCHAR},'%'))
        </if>
        <if test="searchDTO != null and searchDTO.userId != null and searchDTO.userId != 0">
            AND T4.USER_ID = #{searchDTO.userId,jdbcType=INTEGER}
        </if>
        <if test="searchDTO != null and searchDTO.invoiceTaxRate != null">
            AND T1.TAX_RATE = #{searchDTO.invoiceTaxRate,jdbcType=INTEGER}
        </if>
        <if test="searchDTO != null and searchDTO.invoiceCategory != null and searchDTO.invoiceCategory != ''">
            AND T1.INVOICE_CATEGORY = #{searchDTO.invoiceCategory,jdbcType=INTEGER}
        </if>
        <if test="searchDTO != null and searchDTO.startTime != null">
            AND T1.CREATE_TIME <![CDATA[>=]]> #{searchDTO.startTime,jdbcType=BIGINT}
        </if>
        <if test="searchDTO != null and searchDTO.endTime != null">
            AND T1.CREATE_TIME <![CDATA[<=]]> #{searchDTO.endTime,jdbcType=BIGINT}
        </if>
        <if test="searchDTO != null and searchDTO.comment != null and searchDTO.comment != ''">
            AND T1.COMMENT LIKE CONCAT('%',#{searchDTO.comment,jdbcType=VARCHAR},'%')
        </if>
        <if test="searchDTO != null and searchDTO.invoiceStatus != null and searchDTO.invoiceStatus != 0">
            AND T1.INVOICE_STATUS = #{searchDTO.invoiceStatus,jdbcType=INTEGER}
        </if>
        GROUP BY
        T1.HX_INVOICE_ID
        <if test="searchDTO != null and searchDTO.isRecording != null and searchDTO.isRecording == 1">
            HAVING RECORDED_AMOUNT > 0
        </if>
        HAVING
            RECORDED_AMOUNT <![CDATA[<]]> T1.AMOUNT
        ORDER BY
            T1.CREATE_TIME ASC
    </select>

    <select id="getSupplyHxInvoiceWaitRetuenListPage" resultType="com.vedeng.finance.vo.HxInvoiceVo">
        SELECT
        T1.HX_INVOICE_ID,
        T1.INVOICE_NUM,
        T1.INVOICE_CODE,
        T1.SALER_NAME,
        T1.AMOUNT,
        T1.ATTACHMENT,
        T1.TAX_RATE,
        T1.INVOICE_CATEGORY,
        T1.COLOR_TYPE,
        T1.CREATE_TIME,
        T1.ADD_TIME,
        T1.INVOICE_REFUND_STATUS,
        T4.USER_ID ORDER_USER_ID
        FROM
        T_HX_INVOICE T1
        LEFT JOIN T_INVOICE_ENTRY_STASH T2 ON T1.HX_INVOICE_ID = T2.HX_INVOICE_ID
        AND T2.IS_DELETE = 0
        LEFT JOIN T_BUYORDER_GOODS T3 ON T2.BUYORDER_GOODS_ID = T3.BUYORDER_GOODS_ID
        AND T3.IS_DELETE = 0
        LEFT JOIN T_R_TRADER_J_USER T4 ON T1.TRADER_ID = T4.TRADER_ID
        AND T4.TRADER_TYPE = 2
        WHERE
        T1.INVOICE_STATUS = 9
        <if test="searchDTO != null and searchDTO.keyword != null and searchDTO.keyword != ''">
            AND (INVOICE_CODE LIKE CONCAT('%',#{searchDTO.keyword,jdbcType=VARCHAR},'%')
            OR INVOICE_NUM LIKE CONCAT('%',#{searchDTO.keyword,jdbcType=VARCHAR},'%')
            OR SALER_NAME LIKE CONCAT('%',#{searchDTO.keyword,jdbcType=VARCHAR},'%'))
        </if>
        <if test="searchDTO != null and searchDTO.userId != null and searchDTO.userId != 0">
            AND T4.USER_ID = #{searchDTO.userId,jdbcType=INTEGER}
        </if>
        <if test="searchDTO != null and searchDTO.invoiceRefundStatus != null and searchDTO.invoiceRefundStatus != -1">
            AND T1.INVOICE_REFUND_STATUS = #{searchDTO.invoiceRefundStatus,jdbcType=TINYINT}
        </if>
        <if test="searchDTO != null and searchDTO.startTime != null">
            AND T1.CREATE_TIME <![CDATA[>=]]> #{searchDTO.startTime,jdbcType=BIGINT}
        </if>
        <if test="searchDTO != null and searchDTO.endTime != null">
            AND T1.CREATE_TIME <![CDATA[<=]]> #{searchDTO.endTime,jdbcType=BIGINT}
        </if>
        GROUP BY
        T1.HX_INVOICE_ID
        ORDER BY
        T1.INVOICE_REFUND_STATUS ASC ,
        T1.CREATE_TIME DESC
    </select>

    <select id="getSupplyHxInvoiceVerifyingListPage" resultType="com.vedeng.finance.vo.HxInvoiceVo">
        SELECT
        T1.INVOICE_NO INVOICE_NUM,
        SUM( IFNULL( T2.TOTAL_AMOUNT, 0 ) ) RECORDED_AMOUNT,
        T1.AMOUNT / ( 1 + IFNULL( T1.RATIO, 0 ) ) INVOICE_AMOUNT,
        T1.AMOUNT - T1.AMOUNT / ( 1 + IFNULL( T1.RATIO, 0 ) ) TAX_AMOUNT,
        T1.INVOICE_TYPE AS TAX_RATE,
        T3.SALER_NAME,
        T3.COLOR_TYPE,
        T3.ATTACHMENT,
        T3.HX_INVOICE_ID,
        T1.ADD_TIME ENTRY_TIME,
        T1.CREATOR ENTRY_USER_ID,
        T1.INVOICE_FROM,
        T1.RATIO
        FROM
        T_INVOICE T1
        LEFT JOIN T_INVOICE_DETAIL T2 ON T1.INVOICE_ID = T2.INVOICE_ID
        LEFT JOIN T_HX_INVOICE T3 ON T1.HX_INVOICE_ID = T3.HX_INVOICE_ID
        WHERE
        T1.INVOICE_FROM = 1
        AND T1.VALID_STATUS = 0
        <if test="searchDTO != null and searchDTO.invoiceNum != null and searchDTO.invoiceNum != ''">
            AND T1.INVOICE_NO LIKE CONCAT('%',#{searchDTO.invoiceNum,jdbcType=VARCHAR},'%')
        </if>
        <if test="searchDTO != null and searchDTO.invoiceTaxRate != null and searchDTO.invoiceTaxRate != 0">
            AND T1.INVOICE_TYPE = #{searchDTO.invoiceTaxRate,jdbcType=INTEGER}
        </if>
        <if test="searchDTO != null and searchDTO.salerName != null and searchDTO.salerName != ''">
            AND T3.SALER_NAME LIKE CONCAT('%',#{searchDTO.salerName,jdbcType=VARCHAR},'%')
        </if>
        <if test="searchDTO != null and searchDTO.entryUser != null and searchDTO.entryUser != 0">
            AND T1.CREATOR = #{searchDTO.entryUser,jdbcType=INTEGER}
        </if>
        <if test="searchDTO != null and searchDTO.startTime != null">
            AND T1.ADD_TIME <![CDATA[>=]]> #{searchDTO.startTime,jdbcType=BIGINT}
        </if>
        <if test="searchDTO != null and searchDTO.endTime != null">
            AND T1.ADD_TIME <![CDATA[<=]]> #{searchDTO.endTime,jdbcType=BIGINT}
        </if>
        GROUP BY
        T1.INVOICE_ID
        ORDER BY
        T3.CREATE_TIME DESC
    </select>

    <select id="getSupplyHxInvoiceVerifiedListPage" resultType="com.vedeng.finance.vo.HxInvoiceVo">
        SELECT
            P.*
        FROM
         (
            SELECT
            T1.INVOICE_NO INVOICE_NUM,
            T1.AMOUNT,
            T1.AMOUNT / ( 1 + IFNULL( T1.RATIO, 0 ) ) INVOICE_AMOUNT,
            T1.AMOUNT - T1.AMOUNT / ( 1 + IFNULL( T1.RATIO, 0 ) ) TAX_AMOUNT,
            T1.INVOICE_FROM,
            T1.INVOICE_TYPE AS TAX_RATE,
            T3.COLOR_TYPE,
            T3.HX_INVOICE_ID,
            T1.CREATOR ENTRY_USER_ID,
            T4.BUYORDER_NO AS SALEORDER_NO,
            T3.SALER_NAME,
            T3.ATTACHMENT,
            T1.ADD_TIME ENTRY_TIME,
            T1.VALID_TIME,
            T5.USERNAME VALID_USER,
            T1.VALID_STATUS,
            T1.VALID_COMMENTS,
            T3.CREATE_TIME
            FROM
            T_INVOICE T1
            LEFT JOIN T_INVOICE_DETAIL T2 ON T1.INVOICE_ID = T2.INVOICE_ID
            LEFT JOIN T_HX_INVOICE T3 ON T1.HX_INVOICE_ID = T3.HX_INVOICE_ID
            LEFT JOIN T_BUYORDER T4 ON T1.RELATED_ID = T4.BUYORDER_ID
            LEFT JOIN T_USER T5 ON T1.VALID_USERID = T5.USER_ID
            WHERE
            T1.INVOICE_FROM = 1
            AND T1.TYPE = 503
            AND T1.VALID_STATUS != 0
            <if test="searchDTO != null and searchDTO.invoiceNum != null and searchDTO.invoiceNum != ''">
                AND T1.INVOICE_NO LIKE CONCAT('%',#{searchDTO.invoiceNum,jdbcType=VARCHAR},'%')
            </if>
            <if test="searchDTO != null and searchDTO.saleorderNo != null and searchDTO.saleorderNo != ''">
                AND T4.BUYORDER_NO LIKE CONCAT('%',#{searchDTO.saleorderNo,jdbcType=VARCHAR},'%')
            </if>
            <if test="searchDTO != null and searchDTO.salerName != null and searchDTO.salerName != ''">
                AND T3.SALER_NAME LIKE CONCAT('%',#{searchDTO.salerName,jdbcType=VARCHAR},'%')
            </if>
            <if test="searchDTO != null and searchDTO.invoiceTaxRate != null and searchDTO.invoiceTaxRate != 0">
                AND T1.INVOICE_TYPE = #{searchDTO.invoiceTaxRate,jdbcType=INTEGER}
            </if>
            <if test="searchDTO != null and searchDTO.invoiceAmountFrom">
                AND T1.AMOUNT <![CDATA[>=]]> #{searchDTO.invoiceAmountFrom,jdbcType=DECIMAL}
            </if>
            <if test="searchDTO != null and searchDTO.invoiceAmountTo != null">
                AND T1.AMOUNT <![CDATA[<=]]> #{searchDTO.invoiceAmountTo,jdbcType=DECIMAL}
            </if>
            <if test="searchDTO != null and searchDTO.validStatus != null and searchDTO.validStatus != 0">
                AND T1.VALID_STATUS = #{searchDTO.validStatus,jdbcType=	TINYINT}
            </if>
            <if test="searchDTO != null and searchDTO.entryUser != null and searchDTO.entryUser != 0">
                AND T1.CREATOR = #{searchDTO.entryUser,jdbcType=INTEGER}
            </if>
            <if test="searchDTO != null and searchDTO.timeSearchType != null and searchDTO.startTime != null">
                <choose>
                    <when test="searchDTO.timeSearchType == 0">
                        AND T1.ADD_TIME <![CDATA[>=]]> #{searchDTO.startTime,jdbcType=BIGINT}
                    </when>
                    <when test="searchDTO.timeSearchType == 1">
                        AND T1.VALID_TIME <![CDATA[>=]]> #{searchDTO.startTime,jdbcType=BIGINT}
                    </when>
                </choose>
            </if>
            <if test="searchDTO != null and searchDTO.timeSearchType != null and searchDTO.endTime  != null">
                <choose>
                    <when test="searchDTO.timeSearchType == 0">
                        AND T1.ADD_TIME <![CDATA[<=]]> #{searchDTO.endTime,jdbcType=BIGINT}
                    </when>
                    <when test="searchDTO.timeSearchType == 1">
                        AND T1.VALID_TIME <![CDATA[<=]]> #{searchDTO.endTime,jdbcType=BIGINT}
                    </when>
                </choose>
            </if>
            GROUP BY
            T1.INVOICE_ID
            UNION ALL
            SELECT
            T1.INVOICE_NO INVOICE_NUM,
            T1.AMOUNT,
            T1.AMOUNT / ( 1 + IFNULL( T1.RATIO, 0 ) ) INVOICE_AMOUNT,
            T1.AMOUNT - T1.AMOUNT / ( 1 + IFNULL( T1.RATIO, 0 ) ) TAX_AMOUNT,
            T1.INVOICE_FROM,
            T1.INVOICE_TYPE AS TAX_RATE,
            T3.COLOR_TYPE,
            T3.HX_INVOICE_ID,
            T1.CREATOR ENTRY_USER_ID,
            T4.AFTER_SALES_NO AS SALEORDER_NO,
            T3.SALER_NAME,
            T3.ATTACHMENT,
            T1.ADD_TIME ENTRY_TIME,
            T1.VALID_TIME,
            T5.USERNAME VALID_USER,
            T1.VALID_STATUS,
            T1.VALID_COMMENTS,
            T3.CREATE_TIME
            FROM
            T_INVOICE T1
            LEFT JOIN T_INVOICE_DETAIL T2 ON T1.INVOICE_ID = T2.INVOICE_ID
            LEFT JOIN T_HX_INVOICE T3 ON T1.HX_INVOICE_ID = T3.HX_INVOICE_ID
            LEFT JOIN T_AFTER_SALES T4 ON T1.RELATED_ID = T4.AFTER_SALES_ID
            LEFT JOIN T_USER T5 ON T1.VALID_USERID = T5.USER_ID
            WHERE
            T1.INVOICE_FROM = 1
            AND T1.TYPE = 504
            AND T1.VALID_STATUS != 0
            <if test="searchDTO != null and searchDTO.invoiceNum != null and searchDTO.invoiceNum != ''">
                AND T1.INVOICE_NO LIKE CONCAT('%',#{searchDTO.invoiceNum,jdbcType=VARCHAR},'%')
            </if>
            <if test="searchDTO != null and searchDTO.saleorderNo != null and searchDTO.saleorderNo != ''">
                AND T4.AFTER_SALES_NO LIKE CONCAT('%',#{searchDTO.saleorderNo,jdbcType=VARCHAR},'%')
            </if>
            <if test="searchDTO != null and searchDTO.salerName != null and searchDTO.salerName != ''">
                AND T3.SALER_NAME LIKE CONCAT('%',#{searchDTO.salerName,jdbcType=VARCHAR},'%')
            </if>
            <if test="searchDTO != null and searchDTO.invoiceTaxRate != null and searchDTO.invoiceTaxRate != 0">
                AND T1.INVOICE_TYPE = #{searchDTO.invoiceTaxRate,jdbcType=INTEGER}
            </if>
            <if test="searchDTO != null and searchDTO.invoiceAmountFrom">
                AND T1.AMOUNT <![CDATA[>=]]> #{searchDTO.invoiceAmountFrom,jdbcType=DECIMAL}
            </if>
            <if test="searchDTO != null and searchDTO.invoiceAmountTo != null">
                AND T1.AMOUNT <![CDATA[<=]]> #{searchDTO.invoiceAmountTo,jdbcType=DECIMAL}
            </if>
            <if test="searchDTO != null and searchDTO.validStatus != null and searchDTO.validStatus != 0">
                AND T1.VALID_STATUS = #{searchDTO.validStatus,jdbcType=	TINYINT}
            </if>
            <if test="searchDTO != null and searchDTO.entryUser != null and searchDTO.entryUser != 0">
                AND T1.CREATOR = #{searchDTO.entryUser,jdbcType=INTEGER}
            </if>
            <if test="searchDTO != null and searchDTO.timeSearchType != null and searchDTO.startTime != null">
                <choose>
                    <when test="searchDTO.timeSearchType == 0">
                        AND T1.ADD_TIME <![CDATA[>=]]> #{searchDTO.startTime,jdbcType=BIGINT}
                    </when>
                    <when test="searchDTO.timeSearchType == 1">
                        AND T1.VALID_TIME <![CDATA[>=]]> #{searchDTO.startTime,jdbcType=BIGINT}
                    </when>
                </choose>
            </if>
            <if test="searchDTO != null and searchDTO.timeSearchType != null and searchDTO.endTime  != null">
                <choose>
                    <when test="searchDTO.timeSearchType == 0">
                        AND T1.ADD_TIME <![CDATA[<=]]> #{searchDTO.endTime,jdbcType=BIGINT}
                    </when>
                    <when test="searchDTO.timeSearchType == 1">
                        AND T1.VALID_TIME <![CDATA[<=]]> #{searchDTO.endTime,jdbcType=BIGINT}
                    </when>
                </choose>
            </if>
            GROUP BY
            T1.INVOICE_ID
        UNION ALL
        SELECT
        T1.INVOICE_NO INVOICE_NUM,
        T1.AMOUNT,
        T1.AMOUNT / ( 1 + IFNULL( T1.RATIO, 0 ) ) INVOICE_AMOUNT,
        T1.AMOUNT - T1.AMOUNT / ( 1 + IFNULL( T1.RATIO, 0 ) ) TAX_AMOUNT,
        T1.INVOICE_FROM,
        T1.INVOICE_TYPE AS TAX_RATE,
        T3.COLOR_TYPE,
        T3.HX_INVOICE_ID,
        T1.CREATOR ENTRY_USER_ID,
        T4.BUYORDER_EXPENSE_NO AS SALEORDER_NO,
        T3.SALER_NAME,
        T3.ATTACHMENT,
        T1.ADD_TIME ENTRY_TIME,
        T1.VALID_TIME,
        T5.USERNAME VALID_USER,
        T1.VALID_STATUS,
        T1.VALID_COMMENTS,
        T3.CREATE_TIME
        FROM
        T_INVOICE T1
        LEFT JOIN T_INVOICE_DETAIL T2 ON T1.INVOICE_ID = T2.INVOICE_ID
        LEFT JOIN T_HX_INVOICE T3 ON T1.HX_INVOICE_ID = T3.HX_INVOICE_ID
        LEFT JOIN T_BUYORDER_EXPENSE T4 ON T1.RELATED_ID = T4.BUYORDER_EXPENSE_ID
        LEFT JOIN T_USER T5 ON T1.VALID_USERID = T5.USER_ID
        WHERE
        T1.INVOICE_FROM = 1
        AND T1.TYPE = 4126
        AND T1.VALID_STATUS != 0
        <if test="searchDTO != null and searchDTO.invoiceNum != null and searchDTO.invoiceNum != ''">
            AND T1.INVOICE_NO LIKE CONCAT('%',#{searchDTO.invoiceNum,jdbcType=VARCHAR},'%')
        </if>
        <if test="searchDTO != null and searchDTO.saleorderNo != null and searchDTO.saleorderNo != ''">
            AND T4.BUYORDER_EXPENSE_NO LIKE CONCAT('%',#{searchDTO.saleorderNo,jdbcType=VARCHAR},'%')
        </if>
        <if test="searchDTO != null and searchDTO.salerName != null and searchDTO.salerName != ''">
            AND T3.SALER_NAME LIKE CONCAT('%',#{searchDTO.salerName,jdbcType=VARCHAR},'%')
        </if>
        <if test="searchDTO != null and searchDTO.invoiceTaxRate != null and searchDTO.invoiceTaxRate != 0">
            AND T1.INVOICE_TYPE = #{searchDTO.invoiceTaxRate,jdbcType=INTEGER}
        </if>
        <if test="searchDTO != null and searchDTO.invoiceAmountFrom">
            AND T1.AMOUNT <![CDATA[>=]]> #{searchDTO.invoiceAmountFrom,jdbcType=DECIMAL}
        </if>
        <if test="searchDTO != null and searchDTO.invoiceAmountTo != null">
            AND T1.AMOUNT <![CDATA[<=]]> #{searchDTO.invoiceAmountTo,jdbcType=DECIMAL}
        </if>
        <if test="searchDTO != null and searchDTO.validStatus != null and searchDTO.validStatus != 0">
            AND T1.VALID_STATUS = #{searchDTO.validStatus,jdbcType=	TINYINT}
        </if>
        <if test="searchDTO != null and searchDTO.entryUser != null and searchDTO.entryUser != 0">
            AND T1.CREATOR = #{searchDTO.entryUser,jdbcType=INTEGER}
        </if>
        <if test="searchDTO != null and searchDTO.timeSearchType != null and searchDTO.startTime != null">
            <choose>
                <when test="searchDTO.timeSearchType == 0">
                    AND T1.ADD_TIME <![CDATA[>=]]> #{searchDTO.startTime,jdbcType=BIGINT}
                </when>
                <when test="searchDTO.timeSearchType == 1">
                    AND T1.VALID_TIME <![CDATA[>=]]> #{searchDTO.startTime,jdbcType=BIGINT}
                </when>
            </choose>
        </if>
        <if test="searchDTO != null and searchDTO.timeSearchType != null and searchDTO.endTime  != null">
            <choose>
                <when test="searchDTO.timeSearchType == 0">
                    AND T1.ADD_TIME <![CDATA[<=]]> #{searchDTO.endTime,jdbcType=BIGINT}
                </when>
                <when test="searchDTO.timeSearchType == 1">
                    AND T1.VALID_TIME <![CDATA[<=]]> #{searchDTO.endTime,jdbcType=BIGINT}
                </when>
            </choose>
        </if>
        GROUP BY
        T1.INVOICE_ID
         ) P
        ORDER BY
        P.CREATE_TIME DESC
    </select>

    <update id="saveHxInvoiceStatus">
        UPDATE T_HX_INVOICE
        SET INVOICE_STATUS = #{invoiceStatus,jdbcType=TINYINT}
        WHERE
            HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER}
    </update>

    <select id="getHxInvoiceInfoById" resultType="com.vedeng.finance.vo.HxInvoiceVo">
        SELECT
            T1.HX_INVOICE_ID,
            T1.INVOICE_CODE,
            T1.INVOICE_NUM,
            T1.SALER_NAME,
            T1.AMOUNT,
            T1.TAX_RATE,
            T1.ATTACHMENT,
            T1.CREATE_TIME,
            T1.INVOICE_CATEGORY,
            T1.INVOICE_STATUS,
            T1.SALER_NAME,
            T1.COMMENT,
            T1.COLOR_TYPE,
            T1.COMMENT,
            SUM( IFNULL( T2.HAS_ENTRY_COUNT, 0 ) * IFNULL( T3.PRICE, 0 ) ) RECORDED_AMOUNT
        FROM
            T_HX_INVOICE T1
            LEFT JOIN T_INVOICE_ENTRY_STASH T2 ON T1.HX_INVOICE_ID = T2.HX_INVOICE_ID
            AND T2.IS_DELETE = 0
            LEFT JOIN T_BUYORDER_GOODS T3 ON T2.BUYORDER_GOODS_ID = T3.BUYORDER_GOODS_ID
            AND T3.IS_DELETE = 0
        WHERE
            T1.HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER}
    </select>

    <select id="getHxInvoiceInfoByIdNew" resultType="com.vedeng.finance.vo.HxInvoiceVo">
        SELECT
            T1.HX_INVOICE_ID,
            T1.INVOICE_CODE,
            T1.INVOICE_NUM,
            T1.SALER_NAME,
            T1.AMOUNT,
            T1.TAX_RATE,
            T1.ATTACHMENT,
            T1.CREATE_TIME,
            T1.INVOICE_CATEGORY,
            T1.INVOICE_STATUS,
            T1.SALER_NAME,
            T1.COMMENT,
            T1.COLOR_TYPE,
            T1.COMMENT,
            ROUND(SUM( IFNULL( T2.AMOUNT, 0 ) ),2) RECORDED_AMOUNT
        FROM
            T_HX_INVOICE T1
                LEFT JOIN T_INVOICE T2 ON T1.HX_INVOICE_ID = T2.HX_INVOICE_ID
                AND T2.VALID_STATUS != 2
            AND ((T2.COLOR_TYPE = 2
            AND T2.IS_ENABLE = 1)
            OR T2.COLOR_COMPLEMENT_TYPE = 1)
        WHERE
            T1.HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER}
        GROUP BY
            T1.HX_INVOICE_ID
    </select>

    <select id="getHxInvoiceDetailsByHxInvoiceId" resultType="com.vedeng.finance.vo.HxInvoiceDetailVo">
        SELECT
            T4.*
        FROM
            (
            SELECT
                T1.HX_INVOICE_DETAIL_ID,
                T1.HX_INVOICE_ID,
                T1.NUMBER,
                T1.GOODS_NAME,
                T1.SPECIFICATION,
                T1.UNIT,
                T1.QUANTITY,
                T1.PRICE,
                T1.AMOUNT,
                T1.TAX_AMOUNT,
                SUM( IFNULL( T2.HAS_ENTRY_COUNT, 0 ) * IFNULL( T3.PRICE, 0 ) ) RECORDED_AMOUNT
            FROM
                T_HX_INVOICE_DETAIL T1
                LEFT JOIN T_INVOICE_ENTRY_STASH T2 ON T1.HX_INVOICE_DETAIL_ID = T2.HX_INVOICE_DETAIL_ID
                AND T2.IS_DELETE = 0
                LEFT JOIN T_BUYORDER_GOODS T3 ON T2.BUYORDER_GOODS_ID = T3.BUYORDER_GOODS_ID
                AND T3.IS_DELETE = 0
                where T1.HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER}
            GROUP BY
                T1.HX_INVOICE_DETAIL_ID
            ) T4
        WHERE
            T4.HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER}
    </select>

    <select id="getInvoiceEntryStashsByDetailId" resultType="com.vedeng.finance.vo.InvoiceEntryStashVo">
        SELECT
        	T1.INVOICE_ENTRY_STASH_ID,
            T3.BUYORDER_NO,
            T3.TRADER_NAME,
            T2.GOODS_NAME,
            T2.MODEL,
            T2.PRICE,
            T2.ARRIVAL_NUM,
            T1.HAS_ENTRY_COUNT
        FROM
            T_INVOICE_ENTRY_STASH T1
            LEFT JOIN T_BUYORDER_GOODS T2 ON T1.BUYORDER_GOODS_ID = T2.BUYORDER_GOODS_ID
            LEFT JOIN T_BUYORDER T3 ON T2.BUYORDER_ID = T3.BUYORDER_ID
        WHERE
            T1.HX_INVOICE_DETAIL_ID = #{hxInvoiceDetailId,jdbcType=INTEGER}
            AND T1.IS_DELETE = 0
            AND T2.IS_DELETE = 0
    </select>

    <insert id="insertInvoiceEntryStash" parameterType="com.vedeng.finance.model.InvoiceEntryStash">
        INSERT INTO T_INVOICE_ENTRY_STASH
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="invoiceEntryStashId != null">
                INVOICE_ENTRY_STASH_ID,
            </if>
            <if test="hxInvoiceId != null">
                HX_INVOICE_ID,
            </if>
            <if test="hxInvoiceDetailId != null">
                HX_INVOICE_DETAIL_ID,
            </if>
            <if test="buyorderGoodsId != null">
                BUYORDER_GOODS_ID,
            </if>
            <if test="hasEntryCount != null">
                HAS_ENTRY_COUNT,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            IS_DELETE
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="invoiceEntryStashId != null">
                #{invoiceEntryStashId,jdbcType=INTEGER},
            </if>
            <if test="hxInvoiceId != null">
                #{hxInvoiceId,jdbcType=INTEGER},
            </if>
            <if test="hxInvoiceDetailId != null">
                #{hxInvoiceDetailId,jdbcType=INTEGER},
            </if>
            <if test="buyorderGoodsId != null">
                #{buyorderGoodsId,jdbcType=INTEGER},
            </if>
            <if test="hasEntryCount != null">
                #{hasEntryCount,jdbcType=INTEGER},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            0
        </trim>
    </insert>

    <update id="updateInvoiceEntryStash">
        UPDATE T_INVOICE_ENTRY_STASH
        <set>
            <if test="hxInvoiceId != null">
                HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER},
            </if>
            <if test="hxInvoiceDetailId != null">
                HX_INVOICE_DETAIL_ID = #{hxInvoiceDetailId,jdbcType=INTEGER},
            </if>
            <if test="buyorderGoodsId != null">
                BUYORDER_GOODS_ID = #{buyorderGoodsId,jdbcType=INTEGER},
            </if>
            <if test="hasEntryCount != null">
                HAS_ENTRY_COUNT = #{hasEntryCount,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="isDelete != null">
                IS_DELETE = #{isDelete,jdbcType=TINYINT},
            </if>
        </set>
        WHERE
        INVOICE_ENTRY_STASH_ID = #{invoiceEntryStashId,jdbcType=INTEGER}
    </update>

    <select id="getInvoiceEntryStashBaseInfoById" resultType="com.vedeng.finance.model.InvoiceEntryStash">
        SELECT
            INVOICE_ENTRY_STASH_ID,
            HX_INVOICE_ID,
            HX_INVOICE_DETAIL_ID,
            BUYORDER_GOODS_ID,
            HAS_ENTRY_COUNT
        FROM
            T_INVOICE_ENTRY_STASH
        WHERE
            INVOICE_ENTRY_STASH_ID = #{invoiceEntryStashId,jdbcType=INTEGER}
            AND IS_DELETE = 0
    </select>

    <select id="getInvoiceBuyorderList" parameterType="com.vedeng.order.model.dto.BuyorderSearchDTO"
            resultMap="BuyorderResultMap">
        SELECT
        A.BUYORDER_NO,
        A.BUYORDER_ID,A.COMPANY_ID,
        A.VALID_TIME,
        A.TRADER_ID,
        A.TRADER_NAME,
        A.TOTAL_AMOUNT,
        A.INVOICE_TYPE,
        A.PAYMENT_TIME,
        A.INVOICE_COMMENTS,
        A.LOCKED_STATUS,
        B.BUYORDER_GOODS_ID,
        B.GOODS_ID,
        B.SKU,
        B.GOODS_NAME,
        B.BRAND_NAME,
        B.MODEL,
        B.PRICE,
        B.UNIT_NAME,
        B.NUM,
        B.ARRIVAL_NUM,
        C.MATERIAL_CODE
        FROM T_BUYORDER A
        LEFT JOIN T_BUYORDER_GOODS B ON A.BUYORDER_ID = B.BUYORDER_ID AND B.IS_DELETE = 0
        LEFT JOIN T_GOODS C ON B.GOODS_ID = C.GOODS_ID
        LEFT JOIN T_VERIFIES_INFO D ON D.RELATE_TABLE_KEY = A.BUYORDER_ID
        AND D.RELATE_TABLE = 'T_BUYORDER'
        WHERE A.VALID_STATUS = 1
        AND A.STATUS = 1 AND B.PRICE > 0
        AND D.STATUS = 1
        AND A.INVOICE_STATUS <![CDATA[ <> ]]>
        2 <!-- 票状态0未收票 1部分收票 2全部收票 -->
        AND A.COMPANY_ID = 1
        <!-- 产品型号 -->
        <if test="buyorderSearchDTO!= null and buyorderSearchDTO.specification != null and buyorderSearchDTO.specification != ''">
            AND B.MODEL LIKE CONCAT('%',#{buyorderSearchDTO.specification,jdbcType=VARCHAR},'%' )
        </if>
        <!-- 供应商 -->
        <if test="buyorderSearchDTO != null and buyorderSearchDTO.salerName != null and buyorderSearchDTO.salerName != ''">
            AND A.TRADER_NAME LIKE CONCAT('%',#{buyorderSearchDTO.salerName,jdbcType=VARCHAR},'%' )
        </if>
        <!-- 产品名称 -->
        <if test="buyorderSearchDTO!= null and buyorderSearchDTO.goodsName != null and buyorderSearchDTO.goodsName != ''">
            AND B.GOODS_NAME LIKE CONCAT('%',#{buyorderSearchDTO.goodsName,jdbcType=VARCHAR},'%' )
        </if>
        <!-- 产品品牌 -->
        <if test="buyorderSearchDTO != null and buyorderSearchDTO.brandName != null and buyorderSearchDTO.brandName != ''">
            AND B.BRAND_NAME LIKE CONCAT('%',#{buyorderSearchDTO.brandName,jdbcType=VARCHAR},'%' )
        </if>
        <!-- 订单号 -->
        <if test="buyorderSearchDTO != null and buyorderSearchDTO.buyorderNo != null and buyorderSearchDTO.buyorderNo != ''">
            AND A.BUYORDER_NO LIKE CONCAT('%',#{buyorderSearchDTO.buyorderNo,jdbcType=VARCHAR},'%' )
        </if>
        <!-- 票种 -->
        <if test="buyorderSearchDTO != null and buyorderSearchDTO.invoiceType != null and buyorderSearchDTO.invoiceType != ''">
            AND A.INVOICE_TYPE LIKE CONCAT('%',#{buyorderSearchDTO.invoiceType,jdbcType=VARCHAR},'%' )
        </if>
        <!-- 采购价 -->
        <if test="buyorderSearchDTO != null and buyorderSearchDTO.price != null">
            AND B.PRICE = #{buyorderSearchDTO.price,jdbcType=DECIMAL}
        </if>
        <!-- 生效时间 -->
        <if test="buyorderSearchDTO != null and buyorderSearchDTO.startTime != null">
            AND A.VALID_TIME <![CDATA[>=]]> #{buyorderSearchDTO.startTime,jdbcType=BIGINT}
        </if>
        <if test="buyorderSearchDTO != null and buyorderSearchDTO.endTime != null">
            AND A.VALID_TIME <![CDATA[<=]]> #{buyorderSearchDTO.endTime,jdbcType=BIGINT}
        </if>
    </select>

    <insert id="insertHxInvoiceDetail" parameterType="com.vedeng.finance.model.HxInvoiceDetail">
        INSERT INTO T_HX_INVOICE_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hxInvoiceDetailId != null">
                HX_INVOICE_DETAIL_ID,
            </if>
            <if test="hxInvoiceId != null">
                HX_INVOICE_ID,
            </if>
            <if test="num != null">
                NUMBER,
            </if>
            <if test="goodsName != null">
                GOODS_NAME,
            </if>
            <if test="specification != null">
                SPECIFICATION,
            </if>
            <if test="unit != null">
                UNIT,
            </if>
            <if test="quantity != null">
                QUANTITY,
            </if>
            <if test="price != null">
                PRICE,
            </if>
            <if test="amount != null">
                AMOUNT,
            </if>
            <if test="taxAmount != null">
                TAX_AMOUNT,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="hxInvoiceDetailId != null">
                #{.hxInvoiceDetailId,jdbcType=INTEGER},
            </if>
            <if test="hxInvoiceId != null">
                #{hxInvoiceId,jdbcType=INTEGER},
            </if>
            <if test="num != null">
                #{num ,jdbcType=VARCHAR},
            </if>
            <if test="goodsName != null">
                #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="specification != null">
                #{specification,jdbcType=VARCHAR},
            </if>
            <if test="unit != null">
                #{unit,jdbcType=VARCHAR},
            </if>
            <if test="quantity != null">
                #{quantity,jdbcType=INTEGER},
            </if>
            <if test="price != null">
                #{price,jdbcType=DECIMAL},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="taxAmount != null">
                #{taxAmount,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>



    <delete id="deleteInvoiceEntryStash">
        UPDATE T_INVOICE_ENTRY_STASH
        SET IS_DELETE = 1
        WHERE
            HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER}
    </delete>

    <select id="getHxInvoiceDetailById" resultType="com.vedeng.finance.model.HxInvoiceDetail">
        SELECT
            HX_INVOICE_DETAIL_ID,
            HX_INVOICE_ID,
            NUMBER,
            GOODS_NAME,
            SPECIFICATION,
            UNIT,
            QUANTITY,
            PRICE,
            AMOUNT,
            TAX_AMOUNT
        FROM
            T_HX_INVOICE_DETAIL
        WHERE
            HX_INVOICE_DETAIL_ID = #{hxInvoiceDetailId,jdbcType=INTEGER}
    </select>

    <update id="updateHxInvoice">
        UPDATE T_HX_INVOICE
        <set>
            <if test="invoiceCategory != null">
                INVOICE_CATEGORY = #{invoiceCategory,jdbcType=INTEGER},
            </if>
            <if test="invoiceCode != null">
                INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR},
            </if>
            <if test="invoiceStatus != null">
                INVOICE_STATUS = #{invoiceStatus,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
            </if>
        </set>
        WHERE
        HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER}
    </update>

    <select id="getBuyorderGoodsBaseInfoByInvoiceEntryStashId" resultType="com.vedeng.order.model.BuyorderGoods">
       SELECT
            T2.BUYORDER_GOODS_ID,
            T2.BUYORDER_ID,
            T2.GOODS_ID,
            T2.SKU,
            T2.GOODS_NAME,
            T2.PRICE,
            T2.NUM,
            T2.ARRIVAL_NUM,
            T2.IS_DELETE
        FROM
            T_INVOICE_ENTRY_STASH T1
            LEFT JOIN T_BUYORDER_GOODS T2 ON T1.BUYORDER_GOODS_ID = T2.BUYORDER_GOODS_ID
        WHERE
            T1.INVOICE_ENTRY_STASH_ID = #{invoiceEntryStashId,jdbcType=INTEGER}
    </select>

    <select id="getHxInvocieBaseInfoById" resultType="com.vedeng.finance.model.HxInvoice">
        SELECT
            HX_INVOICE_ID,
            INVOICE_CATEGORY,
            INVOICE_CODE,
            INVOICE_NUM,
            SALER_TAX_NUM,
            SALER_NAME,
            SALER_ADDRESS,
            SALER_BANK,
            SALER_BANK_ACCOUNT,
            BUYER_TAX_NUM,
            BUYER_NAME,
            BUYER_ADDRESS,
            BUYER_BANK,
            BUYER_BANK_ACCOUNT,
            CREATE_TIME,
            INVOICE_AMOUNT,
            TAX_AMOUNT,
            AMOUNT,
            TAX_RATE,
            COMMENT,
            CREATOR,
            CHECKER,
            PAYEE,
            ATTACHMENT,
            INVOICE_STATUS,
            INVOICE_REFUND_STATUS,
            ADD_TIME,
            UPDATE_TIME,
            UPDATER,
            SALER_TEL,
            BUYER_TEL,
            COLOR_TYPE,
            TRADER_ID
        FROM
            T_HX_INVOICE
        WHERE
            HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER}
    </select>

    <select id="getBuyorderGoodsRecordDTOByGoodsId" resultType="com.vedeng.order.model.dto.BuyorderGoodsRecordDTO">
        SELECT
            SUM( IFNULL( T2.HAS_ENTRY_COUNT, 0 ) ) RECORDED_NUM,
            SUM( IFNULL( T1.PRICE, 0 ) * ( IFNULL( T2.HAS_ENTRY_COUNT, 0 ) ) ) RECORDED_AMOUNT
        FROM
            T_BUYORDER_GOODS T1
            LEFT JOIN T_INVOICE_ENTRY_STASH T2 ON T1.BUYORDER_GOODS_ID = T2.BUYORDER_GOODS_ID
        WHERE
            T2.IS_DELETE = 0
            AND T1.BUYORDER_GOODS_ID = #{buyorderGoodsId,jdbcType=INTEGER}
    </select>

    <insert id="insertHxInvoiceJBuyorderGoods" parameterType="com.vedeng.finance.model.po.HxInvoiceJBuyorderGoodsPO">
        INSERT INTO T_HXINVOICE_J_BUYORDERGOODS
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hxInvoiceJBuyorderGoodsId != null">
                HXINVOICE_J_BUYORDERGOODS_ID,
            </if>
            <if test="hxInvoiceId != null">
                HX_INVOICE_ID,
            </if>
            <if test="hxInvoiceDetailId != null">
                HX_INVOICE_DETAIL_ID,
            </if>
            <if test="buyorderGoodsId != null">
                BUYORDER_GOODS_ID,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="hxInvoiceJBuyorderGoodsId != null">
                #{hxInvoiceJBuyorderGoodsId,jdbcType=INTEGER},
            </if>
            <if test="hxInvoiceId != null">
                #{hxInvoiceId,jdbcType=INTEGER},
            </if>
            <if test="hxInvoiceDetailId != null">
                #{hxInvoiceDetailId,jdbcType=INTEGER},
            </if>
            <if test="buyorderGoodsId != null">
                #{buyorderGoodsId,jdbcType=DECIMAL},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <update id="updateInvoiceRefundStatus">
        UPDATE T_HX_INVOICE
        SET INVOICE_REFUND_STATUS = #{invoiceRefundStatus,jdbcType=INTEGER}
        WHERE
            HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER}
    </update>

    <update id="batchSaveHxInvoiceStatus">
        UPDATE T_HX_INVOICE
        SET INVOICE_STATUS = #{invoiceStatus,jdbcType=TINYINT}
        WHERE
            HX_INVOICE_ID IN
        <foreach collection="hxInvoiceIds" item="hxInvoiceId" index="index" open="(" close=")" separator=",">
            #{hxInvoiceId,jdbcType=INTEGER}
        </foreach>
    </update>

    <update id="saveHxInvoiceHref">
        UPDATE `T_HX_INVOICE`
        SET ATTACHMENT = #{invoiceHref,jdbcType=VARCHAR},
        UPDATER = #{updater,jdbcType=INTEGER},
        UPDATE_TIME = #{modTime,jdbcType=BIGINT}
        WHERE
            HX_INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
    </update>

    <select id="getHxInvoiceInfoByCondition" resultType="com.vedeng.finance.model.HxInvoice">
        SELECT
            HX_INVOICE_ID,
            INVOICE_CATEGORY,
            INVOICE_CODE,
            INVOICE_NUM,
            INVOICE_STATUS
        FROM
            `T_HX_INVOICE`
        WHERE
            INVOICE_TYPE = 1
            AND INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR}
            AND INVOICE_NUM = #{invoiceNum,jdbcType=INTEGER}
    </select>
    <select id="getHxInvoiceByCodeAndNumAndColorType" resultType="com.vedeng.finance.model.HxInvoice">
            SELECT * FROM T_HX_INVOICE WHERE INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR} AND INVOICE_NUM = #{invoiceNum,jdbcType=VARCHAR} AND COLOR_TYPE = #{colorType,jdbcType=INTEGER}
    </select>

    <select id="getSupplyHxInvoiceVerifiedCount" resultType="com.vedeng.finance.model.Invoice">
         SELECT
            COUNT( IF ( T3.INVOICE_FROM = 1, 1, 0 ) ) AS LZYX_NUM,
            IFNULL( SUM( T3.AMOUNT ), 0 ) AS LZYX_AMOUNT,
            IFNULL( SUM( T3.INVOICE_AMOUNT ), 0 ) AS LZYX_TAX_FREE_AMOUNT,
            IFNULL( SUM( T3.TAX_AMOUNT ), 0 ) AS LZYX_TAX_AMOUNT
        FROM
            (
            SELECT
                T1.AMOUNT,
                T1.AMOUNT / ( 1 + IFNULL( T1.RATIO, 0 ) ) INVOICE_AMOUNT,
                T1.AMOUNT - T1.AMOUNT / ( 1 + IFNULL( T1.RATIO, 0 ) ) TAX_AMOUNT,
                T1.INVOICE_FROM
            FROM
                T_INVOICE T1
                LEFT JOIN T_INVOICE_DETAIL T2 ON T1.INVOICE_ID = T2.INVOICE_ID
            WHERE
                T1.INVOICE_FROM = 1
                AND T1.VALID_STATUS != 0
            GROUP BY
            T1.INVOICE_ID
            ) T3
    </select>

    <select id="getSupplyHxInvoiceVerifingCount" resultType="com.vedeng.finance.model.Invoice">
        SELECT
            COUNT( IF ( T3.INVOICE_FROM = 1, 1, 0 ) ) AS LZYX_NUM,
            IFNULL( SUM( T3.RECORDED_AMOUNT ), 0 ) AS LZYX_AMOUNT,
            IFNULL( SUM( T3.INVOICE_AMOUNT ), 0 ) AS LZYX_TAX_FREE_AMOUNT,
            IFNULL( SUM( T3.TAX_AMOUNT ), 0 ) AS LZYX_TAX_AMOUNT
        FROM
            (
            SELECT
                T1.INVOICE_NO INVOICE_NUM,
                SUM( IFNULL( T2.TOTAL_AMOUNT, 0 ) ) RECORDED_AMOUNT,
                ROUND(
                    SUM( IFNULL( T2.TOTAL_AMOUNT, 0 ) ) / ( 1 + IFNULL( T1.RATIO, 0 ) ),
                    2
                ) INVOICE_AMOUNT,
                ROUND(
                    SUM( IFNULL( T2.TOTAL_AMOUNT, 0 ) ) - SUM( IFNULL( T2.TOTAL_AMOUNT, 0 ) ) / ( 1 + IFNULL( T1.RATIO, 0 ) ),
                    2
                ) TAX_AMOUNT,
                T1.INVOICE_FROM
            FROM
                T_INVOICE T1
                LEFT JOIN T_INVOICE_DETAIL T2 ON T1.INVOICE_ID = T2.INVOICE_ID
            WHERE
                T1.INVOICE_FROM = 1
                AND T1.VALID_STATUS = 0
                <if test="searchDTO != null and searchDTO.invoiceNum != null and searchDTO.invoiceNum != ''">
                    AND T1.INVOICE_NO LIKE CONCAT('%',#{searchDTO.invoiceNum,jdbcType=VARCHAR},'%')
                </if>
                <if test="searchDTO != null and searchDTO.invoiceTaxRate != null and searchDTO.invoiceTaxRate != 0">
                    AND T1.INVOICE_TYPE = #{searchDTO.invoiceTaxRate,jdbcType=INTEGER}
                </if>
                <if test="searchDTO != null and searchDTO.salerName != null and searchDTO.salerName != ''">
                    AND T3.SALER_NAME LIKE CONCAT('%',#{searchDTO.salerName,jdbcType=VARCHAR},'%')
                </if>
                <if test="searchDTO != null and searchDTO.entryUser != null and searchDTO.entryUser != 0">
                    AND T1.CREATOR = #{searchDTO.entryUser,jdbcType=INTEGER}
                </if>
                <if test="searchDTO != null and searchDTO.startTime != null">
                    AND T1.ADD_TIME <![CDATA[>=]]> #{searchDTO.startTime,jdbcType=BIGINT}
                </if>
                <if test="searchDTO != null and searchDTO.endTime != null">
                    AND T1.ADD_TIME <![CDATA[<=]]> #{searchDTO.endTime,jdbcType=BIGINT}
                </if>
            GROUP BY
            T1.INVOICE_ID
            ) T3
    </select>
    <select id="getHxInvoiceBaseInfoByInvoiceIds" resultType="com.vedeng.finance.model.Invoice">
        SELECT
            HX_INVOICE_ID HX_INVOICE_ID,
            INVOICE_CODE INVOICE_CODE,
            INVOICE_NUM INVOICE_NO,
            INVOICE_TYPE INVOICE_TYPE,
            AMOUNT AMOUNT,
            ROUND( TAX_RATE / 100, 2 ) RATIO,
            COLOR_TYPE COLOR_TYPE,
            TAX_AMOUNT RATIO_AMOUNT,
            CREATE_TIME ADD_TIME
        FROM
            T_HX_INVOICE
        WHERE
            HX_INVOICE_ID IN
        <foreach collection="invoiceIds" item="invoiceId" index="index" open="(" close=")" separator=",">
            #{invoiceId,jdbcType=INTEGER}
        </foreach>
        GROUP BY
            HX_INVOICE_ID
    </select>

    <select id="getHxInvoiceCount" resultType="com.vedeng.finance.model.Invoice">
        SELECT
            COUNT( * ) AS invoiceCount,
            SUM( T2.LZYX_AMOUNT - T2.LZZF_AMOUNT - T2.HZYX_AMOUNT ) AS amountCount,
            SUM( T2.LZYX_NUM ) AS LZYX_NUM,
            SUM( T2.HZYX_NUM ) AS HZYX_NUM,
            SUM( T2.LZZF_NUM ) AS LZZF_NUM,
            SUM( T2.LZYX_AMOUNT ) AS LZYX_AMOUNT,
            SUM( T2.LZYX_TAX_FREE_AMOUNT ) AS LZYX_TAX_FREE_AMOUNT,
            SUM( T2.LZYX_TAX_AMOUNT ) AS LZYX_TAX_AMOUNT,
            SUM( T2.LZZF_AMOUNT ) AS LZZF_AMOUNT,
            SUM( T2.LZZF_TAX_FREE_AMOUNT ) AS LZZF_TAX_FREE_AMOUNT,
            SUM( T2.LZZF_TAX_AMOUNT ) AS LZZF_TAX_AMOUNT,
            SUM( T2.HZYX_AMOUNT ) AS HZYX_AMOUNT,
            SUM( T2.HZYX_TAX_FREE_AMOUNT ) AS HZYX_TAX_FREE_AMOUNT,
            SUM( T2.HZYX_TAX_AMOUNT ) AS HZYX_TAX_AMOUNT
        FROM
            (
            SELECT
                HX_INVOICE_ID,
                ROUND( IF ( T1.COLOR_TYPE = 1, ABS( T1.AMOUNT ), 0 ), 2 ) AS LZYX_AMOUNT,
                ROUND( IF ( T1.COLOR_TYPE = 1, ABS( T1.TAX_AMOUNT ), 0 ), 2 ) AS LZYX_TAX_AMOUNT,
                ROUND(
                IF
                    ( T1.COLOR_TYPE = 1, ABS( T1.AMOUNT ) - ABS( T1.TAX_AMOUNT ), 0 ),
                    2
                ) AS LZYX_TAX_FREE_AMOUNT,
                ROUND( IF ( T1.COLOR_TYPE = 2, ABS( T1.AMOUNT ), 0 ), 2 ) AS HZYX_AMOUNT,
                ROUND( IF ( T1.COLOR_TYPE = 2, ABS( T1.TAX_AMOUNT ), 0 ), 2 ) AS HZYX_TAX_AMOUNT,
                ROUND(
                IF
                    ( T1.COLOR_TYPE = 2, ABS( T1.AMOUNT ) - ABS( T1.TAX_AMOUNT ), 0 ),
                    2
                ) AS HZYX_TAX_FREE_AMOUNT,
                ROUND( IF ( T1.COLOR_TYPE = 3, ABS( T1.AMOUNT ), 0 ), 2 ) AS LZZF_AMOUNT,
                ROUND( IF ( T1.COLOR_TYPE = 3, ABS( T1.TAX_AMOUNT ), 0 ), 2 ) AS LZZF_TAX_AMOUNT,
                ROUND(
                IF
                    ( T1.COLOR_TYPE = 3, ABS( T1.AMOUNT ) - ABS( T1.TAX_AMOUNT ), 0 ),
                    2
                ) AS LZZF_TAX_FREE_AMOUNT,
            IF
                ( T1.COLOR_TYPE = 1, 1, 0 ) AS LZYX_NUM,
            IF
                ( T1.COLOR_TYPE = 2, 1, 0 ) AS HZYX_NUM,
            IF
                ( T1.COLOR_TYPE = 3, 1, 0 ) AS LZZF_NUM
            FROM
                T_HX_INVOICE T1
            <if test="invoiceStatus != null">
                WHERE T1.INVOICE_STATUS != 11
                AND T1.INVOICE_STATUS =  #{invoiceStatus,jdbcType=TINYINT}
            </if>
            ) T2
    </select>
    <select id="getHxInvoiceByCodeAndNum" resultType="com.vedeng.finance.model.HxInvoice">
        SELECT * FROM T_HX_INVOICE WHERE INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR} AND INVOICE_NUM = #{invoiceNum,jdbcType=VARCHAR}
    </select>
    <select id="getValidHxInvoice" resultType="com.vedeng.finance.model.HxInvoice">
        SELECT * FROM T_HX_INVOICE WHERE COLOR_TYPE = 1
    </select>

    <select id="getRecordedAmountByInvoiceInfo" resultType="com.vedeng.finance.vo.HxInvoiceVo">
        SELECT
            T2.AMOUNT AS AMOUNT,
            SUM( IFNULL( T1.AMOUNT, 0 ) ) AS RECORDED_AMOUNT
        FROM
            T_INVOICE T1
            LEFT JOIN T_HX_INVOICE T2 ON T1.HX_INVOICE_ID = T2.HX_INVOICE_ID
        WHERE
            T1.INVOICE_FROM = 1
            AND T1.COLOR_TYPE = 2
            AND T1.IS_ENABLE = 1
            AND T1.TYPE IN (503, 504)
            AND T1.VALID_STATUS != 2
            AND T1.INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
            AND T1.HX_INVOICE_ID =  #{hxInvoiceId,jdbcType=INTEGER}
    </select>


    <select id="getHxInvoiceCountCommonInfo" resultType="com.vedeng.finance.model.Invoice">
        SELECT
        COUNT( * ) AS invoiceCount,
        SUM( T2.LZYX_AMOUNT - T2.LZZF_AMOUNT - T2.HZYX_AMOUNT ) AS amountCount,
        SUM( T2.LZYX_NUM ) AS LZYX_NUM,
        SUM( T2.HZYX_NUM ) AS HZYX_NUM,
        SUM( T2.LZZF_NUM ) AS LZZF_NUM,
        SUM( T2.LZYX_AMOUNT ) AS LZYX_AMOUNT,
        SUM( T2.LZYX_TAX_FREE_AMOUNT ) AS LZYX_TAX_FREE_AMOUNT,
        SUM( T2.LZYX_TAX_AMOUNT ) AS LZYX_TAX_AMOUNT,
        SUM( T2.LZZF_AMOUNT ) AS LZZF_AMOUNT,
        SUM( T2.LZZF_TAX_FREE_AMOUNT ) AS LZZF_TAX_FREE_AMOUNT,
        SUM( T2.LZZF_TAX_AMOUNT ) AS LZZF_TAX_AMOUNT,
        SUM( T2.HZYX_AMOUNT ) AS HZYX_AMOUNT,
        SUM( T2.HZYX_TAX_FREE_AMOUNT ) AS HZYX_TAX_FREE_AMOUNT,
        SUM( T2.HZYX_TAX_AMOUNT ) AS HZYX_TAX_AMOUNT
        FROM
        (
        SELECT
        HX_INVOICE_ID,
        ROUND( IF ( T1.COLOR_TYPE = 1, ABS( T1.AMOUNT ), 0 ), 2 ) AS LZYX_AMOUNT,
        ROUND( IF ( T1.COLOR_TYPE = 1, ABS( T1.TAX_AMOUNT ), 0 ), 2 ) AS LZYX_TAX_AMOUNT,
        ROUND(
        IF
        ( T1.COLOR_TYPE = 1, ABS( T1.AMOUNT ) - ABS( T1.TAX_AMOUNT ), 0 ),
        2
        ) AS LZYX_TAX_FREE_AMOUNT,
        ROUND( IF ( T1.COLOR_TYPE = 2, ABS( T1.AMOUNT ), 0 ), 2 ) AS HZYX_AMOUNT,
        ROUND( IF ( T1.COLOR_TYPE = 2, ABS( T1.TAX_AMOUNT ), 0 ), 2 ) AS HZYX_TAX_AMOUNT,
        ROUND(
        IF
        ( T1.COLOR_TYPE = 2, ABS( T1.AMOUNT ) - ABS( T1.TAX_AMOUNT ), 0 ),
        2
        ) AS HZYX_TAX_FREE_AMOUNT,
        ROUND( IF ( T1.COLOR_TYPE = 3, ABS( T1.AMOUNT ), 0 ), 2 ) AS LZZF_AMOUNT,
        ROUND( IF ( T1.COLOR_TYPE = 3, ABS( T1.TAX_AMOUNT ), 0 ), 2 ) AS LZZF_TAX_AMOUNT,
        ROUND(
        IF
        ( T1.COLOR_TYPE = 3, ABS( T1.AMOUNT ) - ABS( T1.TAX_AMOUNT ), 0 ),
        2
        ) AS LZZF_TAX_FREE_AMOUNT,
        IF
        ( T1.COLOR_TYPE = 1, 1, 0 ) AS LZYX_NUM,
        IF
        ( T1.COLOR_TYPE = 2, 1, 0 ) AS HZYX_NUM,
        IF
        ( T1.COLOR_TYPE = 3, 1, 0 ) AS LZZF_NUM
        FROM
        T_HX_INVOICE T1
        WHERE T1.INVOICE_STATUS != 11
        <if test="invoiceStatus != null">
        <choose>
            <when test="invoiceStatus == 12">
                AND
                (T1.INVOICE_STATUS = 12 || T1.INVOICE_STATUS = 7)
                AND T1.HX_INVOICE_ID not in
                (
                SELECT
                HX_INVOICE_ID
                FROM T_HX_INVOICE
                WHERE
                (INVOICE_STATUS = 12 || INVOICE_STATUS = 7)
                GROUP BY INVOICE_CODE,INVOICE_NUM
                having COUNT(*)=1
                )
            </when>
            <otherwise>
                AND T1.INVOICE_STATUS =  #{invoiceStatus,jdbcType=TINYINT}
            </otherwise>
        </choose>
        </if>
        <if test="keyword != null">
            AND (T1.INVOICE_CODE LIKE CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
            OR T1.INVOICE_NUM LIKE CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
            OR T1.SALER_NAME LIKE CONCAT('%',#{keyword,jdbcType=VARCHAR},'%'))
        </if>
        <if test="startTime != null">
            AND T1.CREATE_TIME <![CDATA[ >= ]]> #{startTime,jdbcType=BIGINT}
        </if>
        <if test="endTime != null">
            AND T1.CREATE_TIME <![CDATA[ <= ]]> #{endTime,jdbcType=BIGINT}
        </if>
        <if test="invoiceAmountFrom">
            AND T1.AMOUNT <![CDATA[ >= ]]> #{invoiceAmountFrom}
        </if>
        <if test="invoiceAmountTo != null">
            AND T1.AMOUNT <![CDATA[ <= ]]> #{invoiceAmountTo}
        </if>
        <if test="invoiceTaxRate != null">
            AND T1.TAX_RATE = #{invoiceTaxRate}
        </if>
        <if test="colorType != null">
            AND T1.COLOR_TYPE = #{colorType}
        </if>
        <if test="invoiceCategory != null and invoiceCategory != ''">
            AND T1.INVOICE_CATEGORY = #{invoiceCategory,jdbcType=INTEGER}
        </if>
        <if test="authStatus != null and authStatus != 0">
            AND  T1.IS_AUTH = #{authStatus,jdbcType=TINYINT}
        </if>
        <if test="authStatus != null and authStatus == 0">
            AND ( T1.IS_AUTH = 0 OR  T1.IS_AUTH = 0 OR  T1.IS_AUTH IS NULL )
        </if>
        <if test="authMode != null">
            AND  T1.IS_AUTH != 0
            AND  T1.AUTH_MODE = #{authMode,jdbcType=TINYINT}
        </if>
        ) T2
    </select>

    <select id="getHxInvoiceCountWithAllPage" resultType="com.vedeng.finance.model.Invoice">
        SELECT
            COUNT( * ) AS invoiceCount,
            SUM( RESULT.LZYX_AMOUNT - RESULT.LZZF_AMOUNT - RESULT.HZYX_AMOUNT ) AS amountCount,
            SUM( RESULT.LZYX_NUM ) AS LZYX_NUM,
            SUM( RESULT.HZYX_NUM ) AS HZYX_NUM,
            SUM( RESULT.LZZF_NUM ) AS LZZF_NUM,
            SUM( RESULT.LZYX_AMOUNT ) AS LZYX_AMOUNT,
            SUM( RESULT.LZYX_TAX_FREE_AMOUNT ) AS LZYX_TAX_FREE_AMOUNT,
            SUM( RESULT.LZYX_TAX_AMOUNT ) AS LZYX_TAX_AMOUNT,
            SUM( RESULT.LZZF_AMOUNT ) AS LZZF_AMOUNT,
            SUM( RESULT.LZZF_TAX_FREE_AMOUNT ) AS LZZF_TAX_FREE_AMOUNT,
            SUM( RESULT.LZZF_TAX_AMOUNT ) AS LZZF_TAX_AMOUNT,
            SUM( RESULT.HZYX_AMOUNT ) AS HZYX_AMOUNT,
            SUM( RESULT.HZYX_TAX_FREE_AMOUNT ) AS HZYX_TAX_FREE_AMOUNT,
            SUM( RESULT.HZYX_TAX_AMOUNT ) AS HZYX_TAX_AMOUNT
        FROM
            (
            SELECT
            P.*
            FROM
            (
            SELECT
                ROUND( IF ( HI.COLOR_TYPE = 1, ABS( HI.AMOUNT ), 0 ), 2 ) AS LZYX_AMOUNT,
                ROUND( IF ( HI.COLOR_TYPE = 1, ABS( HI.TAX_AMOUNT ), 0 ), 2 ) AS LZYX_TAX_AMOUNT,
                ROUND(
                IF
                ( HI.COLOR_TYPE = 1, ABS( HI.AMOUNT ) - ABS( HI.TAX_AMOUNT ), 0 ),
                2
                ) AS LZYX_TAX_FREE_AMOUNT,
                ROUND( IF ( HI.COLOR_TYPE = 2, ABS( HI.AMOUNT ), 0 ), 2 ) AS HZYX_AMOUNT,
                ROUND( IF ( HI.COLOR_TYPE = 2, ABS( HI.TAX_AMOUNT ), 0 ), 2 ) AS HZYX_TAX_AMOUNT,
                ROUND(
                IF
                ( HI.COLOR_TYPE = 2, ABS( HI.AMOUNT ) - ABS( HI.TAX_AMOUNT ), 0 ),
                2
                ) AS HZYX_TAX_FREE_AMOUNT,
                ROUND( IF ( HI.COLOR_TYPE = 3, ABS( HI.AMOUNT ), 0 ), 2 ) AS LZZF_AMOUNT,
                ROUND( IF ( HI.COLOR_TYPE = 3, ABS( HI.TAX_AMOUNT ), 0 ), 2 ) AS LZZF_TAX_AMOUNT,
                ROUND(
                IF
                ( HI.COLOR_TYPE = 3, ABS( HI.AMOUNT ) - ABS( HI.TAX_AMOUNT ), 0 ),
                2
                ) AS LZZF_TAX_FREE_AMOUNT,
                IF
                ( HI.COLOR_TYPE = 1, 1, 0 ) AS LZYX_NUM,
                IF
                ( HI.COLOR_TYPE = 2, 1, 0 ) AS HZYX_NUM,
                IF
                ( HI.COLOR_TYPE = 3, 1, 0 ) AS LZZF_NUM,
                HI.HX_INVOICE_ID,
                HI.INVOICE_TYPE,
                HI.INVOICE_CATEGORY,
                HI.INVOICE_CODE,
                HI.INVOICE_NUM,
                HI.SALER_TAX_NUM,
                HI.SALER_NAME,
                HI.SALER_ADDRESS,
                HI.SALER_TEL,
                HI.SALER_BANK,
                HI.SALER_BANK_ACCOUNT,
                HI.BUYER_TAX_NUM,
                HI.BUYER_NAME,
                HI.BUYER_ADDRESS,
                HI.BUYER_TEL,
                HI.BUYER_BANK,
                HI.BUYER_BANK_ACCOUNT,
                HI.CREATE_TIME,
                HI.INVOICE_AMOUNT,
                HI.TAX_AMOUNT,
                HI.AMOUNT,
                HI.TAX_RATE,
                HI.COMMENT,
                HI.CREATOR,
                HI.CHECKER,
                HI.PAYEE,
                HI.ATTACHMENT,
                HI.INVOICE_STATUS,
                HI.INVOICE_REFUND_STATUS,
                HI.ADD_TIME,
                HI.UPDATE_TIME,
                HI.UPDATER,
                HI.COLOR_TYPE,
                HI.TRADER_ID,
                HI.IS_AUTH HX_IS_AUTH,
                TB.BUYORDER_NO,
                TB.BUYORDER_ID,
                TI.ADD_TIME AS ENTRY_TIME,
                TI.CREATOR AS ENTRY_USER_ID,
                TI.IS_MONTH_AUTH,
                TI.IS_AUTH,
                TI.VALID_TIME,
                TI.VALID_USERID AS VALID_USER_ID,
                TI.VALID_STATUS,
                TI.VALID_COMMENTS,
                CASE
                HI.INVOICE_STATUS
                WHEN 5 THEN
                HI.AUTH_TIME ELSE TI.AUTH_TIME
                END AUTH_TIME,
                CASE
                HI.INVOICE_STATUS
                WHEN 5 THEN
                HI.IS_AUTH ELSE TI.IS_AUTH
                END AUTH_STATUS,
                TI.IS_MONTH_AUTH AS AUTH_MONTH,
                TFVN.FINANCE_VOUCHER_NO
            FROM T_HX_INVOICE HI
            <choose>
                <when test="entryUser != null or validUser != null or validStatus != null or authStatus != null or authMonth != null or sendResult != null">
                    LEFT JOIN T_INVOICE TI on HI.HX_INVOICE_ID = TI.HX_INVOICE_ID
                    AND TI.HX_INVOICE_ID > 0
                    AND TI.INVOICE_FROM >= 1
                </when>
                <otherwise>
                    LEFT JOIN T_INVOICE TI on HI.HX_INVOICE_ID = TI.HX_INVOICE_ID
                    AND TI.HX_INVOICE_ID > 0
                </otherwise>
            </choose>
            <choose>
                <when test="sendResult != null">
                    LEFT JOIN
                    (
                    SELECT
                    A.INVOICE_ID,
                    B.FINANCE_VOUCHER_NO
                    FROM T_INVOICE A
                    RIGHT JOIN T_FINANCE_VOUCHER_NO B ON A.INVOICE_ID = B.ID
                    WHERE
                    B.TYPE = 1
                    AND
                    A.TYPE = 503
                    GROUP BY
                    A.INVOICE_ID
                    )
                    as TFVN ON TI.HX_INVOICE_ID = TFVN.INVOICE_ID
                </when>
                <otherwise>
                    LEFT JOIN T_FINANCE_VOUCHER_NO TFVN on TI.INVOICE_ID = TFVN.ID AND TFVN.TYPE = 1
                </otherwise>
            </choose>
            LEFT JOIN T_BUYORDER TB on TI.RELATED_ID = TB.BUYORDER_ID
            ) P
            GROUP BY
            P.HX_INVOICE_ID
            <trim prefixOverrides="AND" prefix="HAVING">
                <if test="keyword != null">
                    (P.INVOICE_CODE LIKE CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
                    or P.INVOICE_NUM LIKE CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
                    or P.SALER_NAME LIKE CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
                    or P.FINANCE_VOUCHER_NO LIKE CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
                    or P.BUYORDER_NO LIKE CONCAT('%',#{keyword,jdbcType=VARCHAR},'%'))
                </if>
                <if test="invoiceStatus != null and invoiceStatus != -1">
                    AND P.INVOICE_STATUS = #{invoiceStatus}
                </if>
                <if test="timeSearchType != null and (startTime != null or endTime != null)">
                    <choose>
                        <when test="timeSearchType == 1">
                            <if test="startTime != null">
                                AND  P.CREATE_TIME <![CDATA[ >= ]]> #{startTime}
                            </if>
                            <if test="endTime != null">
                                AND P.CREATE_TIME <![CDATA[ <= ]]> #{endTime}
                            </if>
                        </when>
                        <when test="timeSearchType == 2">
                            <if test="startTime != null">
                                AND P.ADD_TIME <![CDATA[ >= ]]> #{startTime}
                            </if>
                            <if test="endTime != null">
                                AND P.ADD_TIME <![CDATA[ <= ]]> #{endTime}
                            </if>
                        </when>
                        <when test="timeSearchType == 3">
                            <if test="startTime != null">
                                AND P.VALID_TIME <![CDATA[ >= ]]> #{startTime}
                            </if>
                            <if test="endTime != null">
                                AND P.VALID_TIME <![CDATA[ <= ]]> #{endTime}
                            </if>
                        </when>
                        <when test="timeSearchType == 4">
                            <if test="startTime != null">
                                AND P.AUTH_TIME <![CDATA[ >= ]]> #{startTime}
                            </if>
                            <if test="endTime != null">
                                AND P.AUTH_TIME <![CDATA[ <= ]]> #{endTime}
                            </if>
                        </when>
                    </choose>
                </if>
                <if test="invoiceAmountFrom">
                    AND  P.AMOUNT <![CDATA[ >= ]]> #{invoiceAmountFrom}
                </if>
                <if test="invoiceAmountTo != null">
                    AND P.AMOUNT <![CDATA[ <= ]]> #{invoiceAmountTo}
                </if>
                <if test="invoiceTaxRate != null">
                    AND  P.TAX_RATE = #{invoiceTaxRate}
                </if>
                <if test="invoiceCategory != null and invoiceCategory != ''">
                    AND P.INVOICE_CATEGORY = #{invoiceCategory,jdbcType=INTEGER}
                </if>
                <if test="colorType != null">
                    AND P.COLOR_TYPE = #{colorType}
                </if>
                <if test="entryUser != null">
                    AND P.ENTRY_USER_ID = #{entryUser}
                </if>
                <if test="validUser != null">
                    AND P.VALID_USER_ID = #{validUser}
                </if>
                <if test="validStatus != null">
                    AND P.VALID_STATUS = #{validStatus}
                </if>
                <if test="authStatus != null  and authStatus != 0">
                    AND (P.IS_AUTH = #{authStatus,jdbcType=TINYINT}
                    OR  P.HX_IS_AUTH = #{authStatus,jdbcType=TINYINT})
                </if>
                <if test="authStatus != null  and authStatus == 0">
                    AND ( P.IS_AUTH = 0 OR P.IS_AUTH = 0 OR P.HX_IS_AUTH IS NULL )
                </if>
                <if test="authMonth != null">
                    AND  P.IS_MONTH_AUTH = #{authMonth}
                </if>
                <if test="sendResult != null">
                    <choose>
                        <when test="sendResult == 1">
                            AND P.FINANCE_VOUCHER_NO IS NOT NULL
                        </when>
                        <otherwise>
                            AND  P.FINANCE_VOUCHER_NO IS NULL
                        </otherwise>
                    </choose>
                </if>
            </trim>
            ) RESULT
    </select>
    <select id="getHxInvoiceInvalidListPage" resultType="com.vedeng.finance.vo.HxInvoiceVo">
        SELECT INVOICE_CODE, INVOICE_NUM, SALER_NAME, AMOUNT, INVOICE_AMOUNT, TAX_AMOUNT, TAX_RATE, CREATE_TIME,
        COLOR_TYPE, ATTACHMENT,HX_INVOICE_ID,INVOICE_CATEGORY
        FROM T_HX_INVOICE
        WHERE
        (INVOICE_STATUS = 12 || INVOICE_STATUS = 7)
        AND HX_INVOICE_ID not in
        (
        SELECT
        HX_INVOICE_ID
        FROM T_HX_INVOICE
        WHERE
        (INVOICE_STATUS = 12 || INVOICE_STATUS = 7)
        GROUP BY INVOICE_CODE,INVOICE_NUM
        having COUNT(*)=1
        )
        <if test="searchDTO != null and searchDTO.keyword != null">
            AND (INVOICE_CODE LIKE CONCAT('%',#{searchDTO.keyword,jdbcType=VARCHAR},'%')
            OR INVOICE_NUM LIKE CONCAT('%',#{searchDTO.keyword,jdbcType=VARCHAR},'%')
            OR SALER_NAME LIKE CONCAT('%',#{searchDTO.keyword,jdbcType=VARCHAR},'%'))
        </if>
        <if test="searchDTO != null and searchDTO.startTime != null">
            AND CREATE_TIME <![CDATA[ >= ]]> #{searchDTO.startTime,jdbcType=BIGINT}
        </if>
        <if test="searchDTO != null and searchDTO.endTime != null">
            AND CREATE_TIME <![CDATA[ <= ]]> #{searchDTO.endTime,jdbcType=BIGINT}
        </if>
        <if test="searchDTO != null and searchDTO.invoiceAmountFrom">
            AND AMOUNT <![CDATA[ >= ]]> #{searchDTO.invoiceAmountFrom}
        </if>
        <if test="searchDTO != null and searchDTO.invoiceAmountTo != null">
            AND AMOUNT <![CDATA[ <= ]]> #{searchDTO.invoiceAmountTo}
        </if>
        <if test="searchDTO != null and searchDTO.invoiceTaxRate != null">
            AND TAX_RATE = #{searchDTO.invoiceTaxRate}
        </if>
        <if test="searchDTO != null and searchDTO.colorType != null">
            AND COLOR_TYPE = #{searchDTO.colorType}
        </if>
        <if test="searchDTO != null and searchDTO.invoiceCategory != null and searchDTO.invoiceCategory != ''">
            AND INVOICE_CATEGORY = #{searchDTO.invoiceCategory,jdbcType=INTEGER}
        </if>
        GROUP BY
        INVOICE_CODE,INVOICE_NUM,COLOR_TYPE
    </select>
    <select id="listInvoiceByHxInvoiceId" resultType="com.vedeng.finance.model.Invoice">
        SELECT
            *
        FROM T_INVOICE
        WHERE
            TAG = 2
            AND INVOICE_FROM = 1
            AND HX_INVOICE_ID = #{hxInvoiceId, jdbcType = INTEGER}

    </select>

    <select id="getInValidList" resultType="java.lang.Integer">
           SELECT HX_INVOICE_ID FROM T_HX_INVOICE WHERE INVOICE_TYPE = 1 AND  COLOR_TYPE = 1 AND INVOICE_STATUS = 12;
    </select>

    <select id="getInExceptionList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM T_HX_INVOICE WHERE INVOICE_STATUS = 6;
    </select>

    <select id="getRecordedAmountByHxInvoiceId" resultType="java.math.BigDecimal">
        SELECT
            ROUND(SUM(IFNULL( AMOUNT, 0 )),2)
        FROM
            T_INVOICE
        WHERE
            INVOICE_FROM = 1
          AND VALID_STATUS != 2
         AND ((COLOR_TYPE = 2 AND IS_ENABLE = 1) OR COLOR_COMPLEMENT_TYPE = 1)
          AND HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER}
    </select>

    <select id="getHxInvoiceRecordInfoByHxInvoiceId" resultType="com.vedeng.finance.vo.HxInvoiceVo">
        SELECT
            A.HX_INVOICE_ID,
            A.INVOICE_STATUS,
            ROUND(A.AMOUNT, 2)                 AMOUNT,
            SUM(ROUND(IFNULL(B.AMOUNT, 0), 2)) RECORDED_AMOUNT
        FROM T_HX_INVOICE A
                 LEFT JOIN T_INVOICE B ON A.HX_INVOICE_ID = B.HX_INVOICE_ID
            AND B.TYPE IN (503, 504, 4126)
            AND B.VALID_STATUS != 2
    AND ((B.COLOR_TYPE = 2 AND B.IS_ENABLE = 1) OR B.COLOR_COMPLEMENT_TYPE = 1)
        WHERE A.HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER}
    </select>

    <select id="getRecentHxInvoiceInfoByCondition" resultType="com.vedeng.finance.model.HxInvoice">
        SELECT
            HX_INVOICE_ID,
            INVOICE_CATEGORY,
            INVOICE_CODE,
            INVOICE_NUM,
            INVOICE_STATUS
        FROM
            `T_HX_INVOICE`
        WHERE
            INVOICE_TYPE = 1
          AND INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR}
          AND INVOICE_NUM = #{invoiceNum,jdbcType=VARCHAR}
        ORDER BY INVOICE_STATUS DESC
        LIMIT 1
    </select>
</mapper>