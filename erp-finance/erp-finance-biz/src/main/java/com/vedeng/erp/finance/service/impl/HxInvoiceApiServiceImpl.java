package com.vedeng.erp.finance.service.impl;

import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.finance.dto.HxInvoiceDto;
import com.vedeng.erp.finance.enums.HxInvoiceStatusEnum;
import com.vedeng.erp.finance.mapper.HxInvoiceMapper;
import com.vedeng.erp.finance.service.HxInvoiceApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Service
@Slf4j
public class HxInvoiceApiServiceImpl implements HxInvoiceApiService {
    @Autowired
    private HxInvoiceMapper hxInvoiceMapper;

    @Override
    public void refreshHxInvoiceStatus(Integer hxInvoiceId) {
        if (hxInvoiceId == null || ErpConstant.ZERO.equals(hxInvoiceId)){
            return;
        }
        log.info("发票审核状态航信发票信息处理 hxInvoiceId:{}", hxInvoiceId);
        HxInvoiceDto hxInvoiceRecordInfo = hxInvoiceMapper.getHxInvoiceRecordInfoByHxInvoiceId(hxInvoiceId);
        if (hxInvoiceRecordInfo == null){
            log.error("刷新航信发票状态已录信息异常 hxInvoiceId:{}", hxInvoiceId);
            return;
        }

        Integer invoiceStatus = hxInvoiceRecordInfo.getRecordedAmount().add(BigDecimal.ONE).compareTo(hxInvoiceRecordInfo.getAmount()) > -1 ?
                HxInvoiceStatusEnum.RECORDED.getStatus() : HxInvoiceStatusEnum.WAIT_RECORD.getStatus();
        if (HxInvoiceStatusEnum.RECORDED.getStatus().equals(invoiceStatus) && HxInvoiceStatusEnum.RECORDED.getStatus() > hxInvoiceRecordInfo.getInvoiceStatus()){
            log.info("流转航信发票录票信息 getHxInvoiceId:{},hxInvoiceStatus:{}" ,hxInvoiceId, invoiceStatus);
            hxInvoiceMapper.saveHxInvoiceStatus(hxInvoiceId, invoiceStatus);
        } else if (HxInvoiceStatusEnum.RECORDED.getStatus().equals(hxInvoiceRecordInfo.getInvoiceStatus()) && HxInvoiceStatusEnum.RECORDED.getStatus() > invoiceStatus){
            log.info("流转航信发票录票信息 getHxInvoiceId:{},hxInvoiceStatus:{}" ,hxInvoiceId, invoiceStatus);
            hxInvoiceMapper.saveHxInvoiceStatus(hxInvoiceId, invoiceStatus);
        }
    }

    @Override
    public BigDecimal queryLeftAmountByInvoiceNo(String invoiceNo, String invoiceCode) {
        return hxInvoiceMapper.queryLeftAmountByInvoiceNo(invoiceNo,invoiceCode);
    }

    @Override
    public HxInvoiceDto queryHxInvoiceInfoByNo(String invoiceNo, String invoiceCode) {
        return hxInvoiceMapper.getRecentHxInvoiceDtoByNo(invoiceNo,invoiceCode);
    }
}
