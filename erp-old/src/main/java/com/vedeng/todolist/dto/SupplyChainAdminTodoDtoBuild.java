package com.vedeng.todolist.dto;

import com.vedeng.todolist.service.visitor.*;

public class SupplyChainAdminTodoDtoBuild {

    private SupplyChainAdminTodoDtoBuild(){};

    public static SupplyChainAdminTodoDtoBuild newBuild(){
        return new SupplyChainAdminTodoDtoBuild();
    }

    private SupplyChainAdminTodoDto supplyChainAdminTodoDto = new SupplyChainAdminTodoDto();

    public SupplyChainAdminTodoDtoBuild setRiskCheckSkuTodoDto(TodoVisitor riskCheckSkuTodoVisitor){
        riskCheckSkuTodoVisitor.visitor(this);
        return this;
    }

    public SupplyChainAdminTodoDtoBuild setRiskCheckTraderTodo(TodoVisitor riskTraderTodoAdminVisitor){
        riskTraderTodoAdminVisitor.visitor(this);
        return this;
    }

    public SupplyChainAdminTodoDtoBuild setPurchaseInfoToto(TodoVisitor purchaseInfoTotoAdminVisitor){
        purchaseInfoTotoAdminVisitor.visitor(this);
        return this;
    }

    public SupplyChainAdminTodoDtoBuild setDailyManagementTodo(TodoVisitor dailyManageTodoAdminVisitor){
        dailyManageTodoAdminVisitor.visitor(this);
        return this;
    }

    public SupplyChainAdminTodoDtoBuild setMessageTodoDto(TodoVisitor messageTodoVisitor){
        messageTodoVisitor.visitor(this);
        return this;
    }

    public SupplyChainAdminTodoDtoBuild setRiskSkuRankingTodoDto(RiskSkuRankingTodoVisitor riskSkuRankingTodoVisitor){
        riskSkuRankingTodoVisitor.visitor(this);
        return this;
    }

    public SupplyChainAdminTodoDtoBuild setEarlyWarningGoodsTaskDto(TodoVisitor prepareStockTaskAdminVisitor){
        prepareStockTaskAdminVisitor.visitor(this);
        return this;
    }

    public SupplyChainAdminTodoDtoBuild setPrepareStockTaskDto(TodoVisitor earlyWarningGoodsTaskAdminVisitor){
        earlyWarningGoodsTaskAdminVisitor.visitor(this);
        return this;
    }

    public SupplyChainAdminTodoDtoBuild setReviewTaskToDoDto(TodoVisitor reviewTaskVisitor){
        reviewTaskVisitor.visitor(this);
        return this;
    }
    public SupplyChainAdminTodoDto get(){
        return supplyChainAdminTodoDto;
    }


    public SupplyChainAdminTodoDto bulid(){
        return supplyChainAdminTodoDto;
    }


    public SupplyChainAdminTodoDtoBuild setEarlyWarningTicksTaskDto(EarlyWarningTicksTaskAdminVisitor earlyWarningTicksTaskAdminVisitor) {
        earlyWarningTicksTaskAdminVisitor.visitor(this);
        return this;
    }
    public SupplyChainAdminTodoDtoBuild setEnableReceiveToDoDto(TodoVisitor enableReceiveAdminVisitor){
        enableReceiveAdminVisitor.visitor(this);
        return this;
    }

}
