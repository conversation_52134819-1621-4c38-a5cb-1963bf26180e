package com.vedeng.billsync.dao;

import com.vedeng.billsync.task.model.entity.generate.TmpAlipayBillDataDo;

public interface TmpAlipayBillDataMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TmpAlipayBillDataDo record);

    int insertSelective(TmpAlipayBillDataDo record);

    TmpAlipayBillDataDo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TmpAlipayBillDataDo record);

    int updateByPrimaryKey(TmpAlipayBillDataDo record);

    TmpAlipayBillDataDo selectByFinancialTurnover(String financialTurnover);

}