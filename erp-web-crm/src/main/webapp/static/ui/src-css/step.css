.ui-step-wrap .ui-step-list {
  display: flex;
  align-items: center;
}
.ui-step-wrap .ui-step-list .ui-step-item {
  flex: 1;
  text-align: center;
  padding: 30px 0 20px 0;
  position: relative;
}
.ui-step-wrap .ui-step-list .ui-step-item::before {
  content: "";
  width: 100%;
  border-bottom: 1px dashed #c9ced1;
  position: absolute;
  top: 38px;
  left: calc(50% + 8px);
}
.ui-step-wrap .ui-step-list .ui-step-item:last-child::before {
  display: none;
}
.ui-step-wrap .ui-step-list .ui-step-item .ui-step-item-icon {
  width: 17px;
  height: 17px;
  border-radius: 50%;
  background: #babfc2;
  display: inline-block;
  vertical-align: top;
  margin-bottom: 5px;
}
.ui-step-wrap .ui-step-list .ui-step-item .ui-step-item-txt {
  color: #999;
}
.ui-step-wrap .ui-step-list .ui-step-item.active .ui-step-item-icon {
  background-color: #09f;
}
.ui-step-wrap .ui-step-list .ui-step-item.active .ui-step-item-txt {
  color: #09f;
}
.ui-step-wrap .ui-step-list.wait .ui-step-item.active .ui-step-item-icon {
  background-color: #f60;
}
.ui-step-wrap .ui-step-list.wait .ui-step-item.active .ui-step-item-txt {
  color: #f60;
}
.ui-step-wrap .ui-step-list.finish .ui-step-item .ui-step-item-icon {
  background-color: #13BF13;
}
.ui-step-wrap .ui-step-list.finish .ui-step-item .ui-step-item-txt {
  color: #13BF13;
}
.ui-step-wrap .ui-step-list.close .ui-step-item .ui-step-item-icon {
  background-color: #1A4D80;
}
.ui-step-wrap .ui-step-list.close .ui-step-item .ui-step-item-txt {
  color: #1A4D80;
}
