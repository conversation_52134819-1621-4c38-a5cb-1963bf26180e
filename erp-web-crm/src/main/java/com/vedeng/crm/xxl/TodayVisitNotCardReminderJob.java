package com.vedeng.crm.xxl;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.crm.visitrecord.service.CrmVisitRecordService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 当天待拜访提醒任务-18点执行，不可重复执行，会重复发送提醒
 * - 查询当天待拜访的计划并发送提醒消息
 */
@Component
@JobHandler(value="TodayVisitNotCardReminderJob")
@Slf4j
public class TodayVisitNotCardReminderJob extends AbstractJobHandler {

    @Autowired
    private CrmVisitRecordService crmVisitRecordService;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("==================当天待拜访提醒job开始====================");
        crmVisitRecordService.sendTodayVisitNotCardReminder();
        XxlJobLogger.log("==================当天待拜访提醒job结束====================");
        return SUCCESS;
    }
} 
