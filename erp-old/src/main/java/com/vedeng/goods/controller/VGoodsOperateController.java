package com.vedeng.goods.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.authorization.model.User;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.goods.GoodsConstants;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.core.utils.imageupload.ChainResult;
import com.vedeng.common.core.utils.imageupload.ImgUploadVerifyActuator;
import com.vedeng.common.exception.ShowErrorMsgException;
import com.vedeng.common.http.HttpURLConstant;
import com.vedeng.common.http.NewHttpClientUtils;
import com.vedeng.common.model.FileInfo;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.model.ResultJSON;
import com.vedeng.common.util.EmptyUtils;
import com.vedeng.common.util.StringUtil;
import com.vedeng.common.validator.FormToken;
import com.vedeng.goods.dto.VHostWordDTO;
import com.vedeng.goods.model.*;
import com.vedeng.goods.service.HostSkuService;
import com.vedeng.goods.command.SkuAddCommand;
import com.vedeng.goods.dao.CoreOperateInfoGenerateExtendMapper;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.enums.OperateInfoSourceEnum;
import com.vedeng.goods.model.dto.CoreSkuBaseDTO;
import com.vedeng.goods.model.dto.CoreSpuBaseDTO;
import com.vedeng.goods.model.vo.CoreOperateInfoGenerateVo;
import com.vedeng.goods.service.BaseGoodsService;
import com.vedeng.goods.service.CoreOperateInfoService;
import com.vedeng.goods.service.MlFacadeService;
import com.vedeng.goods.service.GoodsCommonService;
import com.vedeng.goods.service.VgoodsService;
import com.vedeng.goods.utils.GoodsUtils;
import com.vedeng.system.service.OssUtilsService;
import net.sf.json.JSONArray;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/vgoods/operate")
public class VGoodsOperateController extends BaseController {

	@Autowired
	VgoodsService goodsService;

	@Autowired
	BaseGoodsService baseGoodsService;

	@Autowired
	CoreOperateInfoService coreOperateInfoService;

	@Autowired
	private OssUtilsService ossUtilsService;

	@Autowired
	private GoodsCommonService goodsCommonService;

	@Autowired
	private MlFacadeService mlFacadeService;

	@Autowired
	private HostSkuService hostSkuService;

	@Value("${operate_url}")
	private String operateUrl;

	@Value("${api_http}")
	protected String apiHttp;

	public static final String OSS_DOMAIN = "file.ivedeng.com";
	 @Value("${ml.server.url}")
	private String mlServerUrl ;

	@Value("${invi_url}")
	private String inviUrl;

	@Autowired
	private CoreSkuMapper coreSkuMapper;

	@Autowired
	private ImgUploadVerifyActuator imgUploadVerifyActuator;

	@Value("${noPowerContract:Aadi}")
	private String noPowerContract;

	/**
	 * 打开新增/编辑商品运营信息
	 * <AUTHOR>
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	@FormToken(save = true)
	@RequestMapping(value = "/openOperate")
	public ModelAndView openOperate(CoreOperateInfoGenerateVo coreOperateInfoGenerateVo, HttpServletRequest request,
									   HttpServletResponse response) throws Exception {
		try{
			ModelAndView openOperateMv = new ModelAndView();
			//进行编辑
			if (coreOperateInfoGenerateVo != null ){
				if(coreOperateInfoGenerateVo.getOperateInfoId() != null && !"".equals(coreOperateInfoGenerateVo.getOperateInfoId())) {
					//查询运营信息
					coreOperateInfoGenerateVo = coreOperateInfoService.getCoreOperateInfoById(coreOperateInfoGenerateVo);
					if (coreOperateInfoGenerateVo.getOperateInfoType().equals(CommonConstants.OPERATE_INFO_TYPE_SKU_2)){
						//查询该sku对应的spuId
						SkuAddCommand skuAddCommand = new SkuAddCommand();
						skuAddCommand.setSkuId(coreOperateInfoGenerateVo.getSkuId());
						CoreSkuGenerate coreSkuGenerate = goodsService.initSku(skuAddCommand, false);
						coreOperateInfoGenerateVo.setUpSpuId(coreSkuGenerate.getSpuId());
					}
				}else{
					if (coreOperateInfoGenerateVo.getSkuId() != null && !"".equals(coreOperateInfoGenerateVo.getSkuId())){//SKUID不为空则绑定的是SKU
						CoreSkuBaseDTO coreSkuBaseDTO = baseGoodsService.selectSkuBaseById(coreOperateInfoGenerateVo.getSkuId());
						if (!coreSkuBaseDTO.getCheckStatus().equals(CommonConstants.GOODS_CHECK_STATUS_3)){
							openOperateMv.addObject("noPowerContract",noPowerContract);
							openOperateMv.setViewName("common/nopower");
							return openOperateMv;
						}
						Integer skuId = coreOperateInfoGenerateVo.getSkuId();
						//查询该sku对应的运营信息
						coreOperateInfoGenerateVo = coreOperateInfoService.getCoreOperateInfoBySkuId(coreOperateInfoGenerateVo.getSkuId());
						if (coreOperateInfoGenerateVo == null){
							coreOperateInfoGenerateVo = new CoreOperateInfoGenerateVo();
							coreOperateInfoGenerateVo.setSkuId(skuId);
							//查询sku名称
							coreOperateInfoGenerateVo.setGoodsName(coreOperateInfoService.getProductNameBySkuId(skuId));
							coreOperateInfoGenerateVo.setOperateInfoType(CommonConstants.OPERATE_INFO_TYPE_SKU_2);
							//查询该sku对应的SPUID
							SkuAddCommand skuAddCommand = new SkuAddCommand();
							skuAddCommand.setSkuId(skuId);
							CoreSkuGenerate coreSkuGenerate = goodsService.initSku(skuAddCommand, false);
							coreOperateInfoGenerateVo.setUpSpuId(coreSkuGenerate.getSpuId());
							//查询该sku对应的spu图片
							GoodsAttachment goodsAttachment = new GoodsAttachment();
							goodsAttachment.setGoodsId(coreSkuGenerate.getSpuId());
							goodsAttachment.setStatus(CommonConstants.STATUS_1);
							goodsAttachment.setAttachmentType(CommonConstants.ATTACHMENT_TYPE_SPU_1002);
							List<GoodsAttachment> goodsAttachmentList = coreOperateInfoService.getGoodsAttachment(goodsAttachment);
							//处理商品图片，处理成json格式
							String goodsImgJson = this.doGoodsAttachmentToJsonInList(goodsAttachmentList);
							openOperateMv.addObject("goodsImgJson",goodsImgJson);
						}else {
							//查询该sku对应的SPUID
							SkuAddCommand skuAddCommand = new SkuAddCommand();
							skuAddCommand.setSkuId(skuId);
							CoreSkuGenerate coreSkuGenerate = goodsService.initSku(skuAddCommand, false);
							coreOperateInfoGenerateVo.setUpSpuId(coreSkuGenerate.getSpuId());
						}

					}else if (coreOperateInfoGenerateVo.getSpuId() != null && !"".equals(coreOperateInfoGenerateVo.getSpuId())){//SPUID不为空则绑定的是SPU
						Integer spuId = coreOperateInfoGenerateVo.getSpuId();
						//检查spu的审核状态是否已经通过
						CoreSpuBaseDTO coreSpuBaseDTO = baseGoodsService.selectSpuBaseById(spuId);
						if (!coreSpuBaseDTO.getCheckStatus().equals(CommonConstants.GOODS_CHECK_STATUS_3)){
							openOperateMv.addObject("noPowerContract",noPowerContract);
							openOperateMv.setViewName("common/nopower");
							return openOperateMv;
						}
						//查询该spu对应的运营信息
						coreOperateInfoGenerateVo = coreOperateInfoService.getCoreOperateInfoBySpuId(spuId);
						if (coreOperateInfoGenerateVo == null){
							coreOperateInfoGenerateVo = new CoreOperateInfoGenerateVo();
							//查询spu名称
							coreOperateInfoGenerateVo.setSpuId(spuId);
							coreOperateInfoGenerateVo.setGoodsName(coreOperateInfoService.getProductNameBySpuId(spuId));
							coreOperateInfoGenerateVo.setOperateInfoType(CommonConstants.OPERATE_INFO_TYPE_SPU_1);
						}
					}
				}
			    if (coreOperateInfoGenerateVo.getOperateInfoId() != null && !"".equals(coreOperateInfoGenerateVo.getOperateInfoId())){
					//查询商品图片信息
					GoodsAttachment goodsAttachment = new GoodsAttachment();
					if (coreOperateInfoGenerateVo.getOperateInfoType().equals(CommonConstants.OPERATE_INFO_TYPE_SKU_2)){
						goodsAttachment.setAttachmentType(CommonConstants.ATTACHMENT_TYPE_SKU_1001);
						goodsAttachment.setStatus(CommonConstants.STATUS_1);
						goodsAttachment.setGoodsId(coreOperateInfoGenerateVo.getSkuId());
					}else{
						goodsAttachment.setAttachmentType(CommonConstants.ATTACHMENT_TYPE_SPU_1002);
						goodsAttachment.setStatus(CommonConstants.STATUS_1);
						goodsAttachment.setGoodsId(coreOperateInfoGenerateVo.getSpuId());
					}
					List<GoodsAttachment> goodsAttachmentList = coreOperateInfoService.getGoodsAttachment(goodsAttachment);
					//处理商品图片，处理成json格式
					String goodsImgJson = this.doGoodsAttachmentToJsonInList(goodsAttachmentList);
					openOperateMv.addObject("goodsImgJson",goodsImgJson);
					//处理商品关键词，处理成数组格式
					if(EmptyUtils.isNotBlank(coreOperateInfoGenerateVo.getSeoKeywords())){
						coreOperateInfoGenerateVo.setSeoKeyWordsArray(coreOperateInfoGenerateVo.getSeoKeywords().split(","));
					}
				}
			}

			// 马良图解详情处理
			mlFacadeService.mlSkuGraphicDetailHandler(coreOperateInfoGenerateVo);

			openOperateMv.addObject("coreOperateInfoGenerateVo",coreOperateInfoGenerateVo);
			openOperateMv.setViewName("goods/vgoods/addOperate");
			return openOperateMv;
		}catch (Exception e){
			logger.error("打开新增/编辑商品运营信息异常：",e);
			return this.page500(request);
		}
	}

	/**
	 * 保存商品运营信息
	 * <AUTHOR>
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	@FormToken(remove = true)
	@RequestMapping(value = "/static/changeOperateInfoSource")
	@ResponseBody
	public ModelAndView saveSkuOperateRich(CoreOperateInfoGenerateVo coreOperateInfoGenerateVo,HttpServletRequest request,
									HttpServletResponse response) throws Exception {
		try{
			User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
			if (user != null){
				//检查商品是否已审核通过，未通过不可保存
				CoreSkuBaseDTO coreSkuBaseDTO = baseGoodsService.selectSkuBaseById(coreOperateInfoGenerateVo.getSkuId());
				if (!coreSkuBaseDTO.getCheckStatus().equals(CommonConstants.GOODS_CHECK_STATUS_3)){
					ModelAndView openOperateMv = new ModelAndView("common/nopower");
					openOperateMv.addObject("noPowerContract",noPowerContract);
					return openOperateMv;
				}
				//检查运营信息是否可保存，字段验证
				ResultInfo resultInfo = this.checkOperateInfo(coreOperateInfoGenerateVo);
				if (CommonConstants.SUCCESS_CODE.equals(resultInfo.getCode())){//验证通过
					//处理图片列表
					List<GoodsAttachment> goodsAttachmentList = null;
					if (coreOperateInfoGenerateVo.getOperateInfoType().equals(CommonConstants.OPERATE_INFO_TYPE_SKU_2)){
						goodsAttachmentList = this.doGoodsAttachment(coreOperateInfoGenerateVo.getGoodsImage(),coreOperateInfoGenerateVo.getSkuId(),coreOperateInfoGenerateVo.getOperateInfoType());
					}
					//保存运营信息
					Integer operateInfoId = coreOperateInfoService.saveCoreOperateInfo(coreOperateInfoGenerateVo, goodsAttachmentList);

						//goodsService.updateSkuOperateInfoFlag(operateInfoId,coreOperateInfoGenerateVo.getSkuId());


					if (coreOperateInfoGenerateVo != null && coreOperateInfoGenerateVo.getSpuId() != null) {
						baseGoodsService.updatePushStatusBySpuId(coreOperateInfoGenerateVo.getSpuId(), GoodsConstants.PUSH_STATUS_UN_PUSH);
					} else if (coreOperateInfoGenerateVo != null && coreOperateInfoGenerateVo.getSkuId() != null) {
						baseGoodsService.updatePushStatusBySkuId(coreOperateInfoGenerateVo.getSkuId(), GoodsConstants.PUSH_STATUS_UN_PUSH);
					}

					return new ModelAndView("redirect:./viewOperate.do?operateInfoId="+operateInfoId);
				}else{
					request.setAttribute("error",resultInfo.getMessage());
				}
			}else{
				request.setAttribute("error","登陆信息已失效，请重新登陆！");
			}


			return new ModelAndView("forward:./openNewOperate.do");
		}catch (Exception e){
			logger.error("打开新增/编辑商品运营信息异常：",e);
			return this.page500(request);
		}
	}

	/**
	 * 保存商品运营信息
	 * <AUTHOR>
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	//@FormToken(remove = true)
	@RequestMapping(value = "/saveOperate")
	@ResponseBody
	public ModelAndView saveOperate( CoreOperateInfoGenerateVo coreOperateInfoGenerateVo,HttpServletRequest request,
								  HttpServletResponse response) throws Exception {
		try{
			User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);

			if (user != null){
				//检查商品是否已审核通过，未通过不可保存
				if (CommonConstants.OPERATE_INFO_TYPE_SPU_1.equals(coreOperateInfoGenerateVo.getOperateInfoType())){
					CoreSpuBaseDTO coreSpuBaseDTO = baseGoodsService.selectSpuBaseById(coreOperateInfoGenerateVo.getSpuId());
					if (!coreSpuBaseDTO.getCheckStatus().equals(CommonConstants.GOODS_CHECK_STATUS_3)){
						ModelAndView openOperateMv = new ModelAndView("common/nopower");
						openOperateMv.addObject("noPowerContract",noPowerContract);
						return openOperateMv;
					}
				}else {
					CoreSkuBaseDTO coreSkuBaseDTO = baseGoodsService.selectSkuBaseById(coreOperateInfoGenerateVo.getSkuId());
					if (!coreSkuBaseDTO.getCheckStatus().equals(CommonConstants.GOODS_CHECK_STATUS_3)){
						ModelAndView openOperateMv = new ModelAndView("common/nopower");
						openOperateMv.addObject("noPowerContract",noPowerContract);
						return openOperateMv;
					}
				}
                //检查运营信息是否可保存，字段验证
				ResultInfo resultInfo = this.checkOperateInfo(coreOperateInfoGenerateVo);
				if (CommonConstants.SUCCESS_CODE.equals(resultInfo.getCode())){//验证通过
					//处理图片列表
					List<GoodsAttachment> goodsAttachmentList = null;
					if (coreOperateInfoGenerateVo.getOperateInfoType().equals(CommonConstants.OPERATE_INFO_TYPE_SKU_2)){
						goodsAttachmentList = this.doGoodsAttachment(coreOperateInfoGenerateVo.getGoodsImage(),coreOperateInfoGenerateVo.getSkuId(),coreOperateInfoGenerateVo.getOperateInfoType());
					}else{
						goodsAttachmentList = this.doGoodsAttachment(coreOperateInfoGenerateVo.getGoodsImage(),coreOperateInfoGenerateVo.getSpuId(),coreOperateInfoGenerateVo.getOperateInfoType());
					}
					//保存运营信息
					Integer operateInfoId = coreOperateInfoService.saveCoreOperateInfo(coreOperateInfoGenerateVo, goodsAttachmentList);
					if (CommonConstants.OPERATE_INFO_TYPE_SPU_1.equals(coreOperateInfoGenerateVo.getOperateInfoType())){
						goodsService.updateSpuOperateInfoFlag(operateInfoId,coreOperateInfoGenerateVo.getSpuId());
					}else {
						goodsService.updateSkuOperateInfoFlag(operateInfoId,coreOperateInfoGenerateVo.getSkuId());
					}

					if (coreOperateInfoGenerateVo != null && coreOperateInfoGenerateVo.getSpuId() != null) {
						baseGoodsService.updatePushStatusBySpuId(coreOperateInfoGenerateVo.getSpuId(), GoodsConstants.PUSH_STATUS_UN_PUSH);
					} else if (coreOperateInfoGenerateVo != null && coreOperateInfoGenerateVo.getSkuId() != null) {
						baseGoodsService.updatePushStatusBySkuId(coreOperateInfoGenerateVo.getSkuId(), GoodsConstants.PUSH_STATUS_UN_PUSH);
					}

					return new ModelAndView("redirect:./viewOperate.do?operateInfoId="+operateInfoId);
				}else{
					request.setAttribute("error",resultInfo.getMessage());
				}
			}else{
				request.setAttribute("error","登陆信息已失效，请重新登陆！");
			}
				return new ModelAndView("forward:./openNewOperate.do");



		}catch (Exception e){
			logger.error("打开新增/编辑商品运营信息异常：",e);
			return this.page500(request);
		}
	}
	@RequestMapping(value = "/static/saveSkuOperate")
	@ResponseBody
	public ResultJSON saveSkuOperate(@RequestParam(value = "goodsImageArray[]",required = false) String[] goodsImageArray,CoreOperateInfoGenerateVo coreOperateInfoGenerateVo,HttpServletRequest request,
									HttpServletResponse response) throws Exception {
		try{
			User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
			coreOperateInfoGenerateVo.setGoodsImage(goodsImageArray);
			if (user != null){

				CoreSkuBaseDTO coreSkuBaseDTO = baseGoodsService.selectSkuBaseById(coreOperateInfoGenerateVo.getSkuId());
				if (!coreSkuBaseDTO.getCheckStatus().equals(CommonConstants.GOODS_CHECK_STATUS_3)){
					return ResultJSON.failed().message("SKU不是审核通过状态，请审核通过之后再提交。");
				}

//				if (coreOperateInfoGenerateVo.getGoodsImage() == null || coreOperateInfoGenerateVo.getGoodsImage().length <= 0){
//					return ResultJSON.failed().message("商品图片至少上传一张，无法提交");
//				}
				if (coreOperateInfoGenerateVo.getGoodsImage() != null && coreOperateInfoGenerateVo.getGoodsImage().length > 5){
					return ResultJSON.failed().message("商品图片最多只能上传5张，无法提交");
				}

					//处理图片列表
					List<GoodsAttachment>  goodsAttachmentList = this.doGoodsAttachment(coreOperateInfoGenerateVo.getGoodsImage(),coreOperateInfoGenerateVo.getSkuId(),coreOperateInfoGenerateVo.getOperateInfoType());

					//保存运营信息
					Integer operateInfoId = coreOperateInfoService.saveCoreOperateInfo(coreOperateInfoGenerateVo, goodsAttachmentList);


				    CoreOperateInfoGenerate operateInfoGenerate = coreOperateInfoService.getCoreOperateInfoBySkuId(coreOperateInfoGenerateVo.getSkuId()) ;
					CoreOperateInfoGenerateVo optVo = new CoreOperateInfoGenerateVo();
					optVo.setSkuId(coreOperateInfoGenerateVo.getSkuId());
					mlFacadeService.mlSkuGraphicDetailHandler(optVo);
					GoodsAttachment goodsAttachment = new GoodsAttachment();
					goodsAttachment.setAttachmentType(CommonConstants.ATTACHMENT_TYPE_SKU_1001);
					goodsAttachment.setStatus(CommonConstants.STATUS_1);
					goodsAttachment.setGoodsId(coreOperateInfoGenerateVo.getSkuId());
					List<GoodsAttachment> goodsAttachmentListDb = coreOperateInfoService.getGoodsAttachment(goodsAttachment);
				    if(CollectionUtils.isNotEmpty(goodsAttachmentListDb)
						&&operateInfoGenerate!=null&&(StringUtils.isNotBlank(optVo.getMlSkuGraphicDetail())
					     ||StringUtils.isNotBlank(operateInfoGenerate.getOprateInfoHtml()))){
						goodsService.updateSkuOperateInfoFlag(operateInfoId,coreOperateInfoGenerateVo.getSkuId());
						baseGoodsService.updatePushStatusBySkuId(coreOperateInfoGenerateVo.getSkuId(), GoodsConstants.PUSH_STATUS_UN_PUSH);
					}



					//return new ModelAndView("redirect:./viewOperate.do?operateInfoId="+operateInfoId);

			}else{
				request.setAttribute("error","登陆信息已失效，请重新登陆！");
			}

				return ResultJSON.success().message("保存成功");



		}catch (Exception e){
			logger.error("打开新增/编辑商品运营信息异常：",e);
			return ResultJSON.failed().message(e.getMessage());
		}
	}



	/**
	 * 转发打开新增/编辑商品运营信息
	 * <AUTHOR>
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	@FormToken(save = true)
	@RequestMapping(value = "/openNewOperate")
	public ModelAndView openNewOperate(CoreOperateInfoGenerateVo coreOperateInfoGenerateVo, HttpServletRequest request,
									HttpServletResponse response) throws Exception {
		try{
			ModelAndView openOperateMv = new ModelAndView();
			//处理商品图片信息,处理成json格式
			String goodsImgJson = this.doGoodsAttachmentToJsonInArray(coreOperateInfoGenerateVo.getGoodsImage());
			openOperateMv.addObject("goodsImgJson",goodsImgJson);
			openOperateMv.addObject("error", request.getAttribute("error"));
			openOperateMv.addObject("coreOperateInfoGenerateVo",coreOperateInfoGenerateVo);
			openOperateMv.setViewName("goods/vgoods/addOperate");
			return openOperateMv;
		}catch (Exception e){
			logger.error("转发打开新增/编辑商品运营信息异常：",e);
			return this.page500(request);
		}
	}

	@NoNeedAccessAuthorization
	@RequestMapping(value = "/getAllOrgList")
	@ResponseBody
	public ResultInfo<?> getAllOrgList() {

		List<com.vedeng.invi.api.dto.OrgDTO> allOrgList = new ArrayList<>();
		try {
			ResultInfo<?> orgResult = NewHttpClientUtils.doPost(inviUrl + "/org/queryOrg");
			if (orgResult.getCode() == 200) {
				//1、使用JSONObject
				com.vedeng.invi.api.dto.OrgListDTO orgListDTO = JSON.parseObject((String) orgResult.getData(), com.vedeng.invi.api.dto.OrgListDTO.class);
				allOrgList = orgListDTO.getOrgList();
			}
		} catch (Exception e) {
			logger.error("区域商城后台接口异常", e);
			return ResultInfo.error("调用区域商城获取区域商城列表失败");
		}
		return ResultInfo.success(allOrgList);
	}
		/**
         * 查看商品运营信息
         * <AUTHOR>
         * @param request
         * @param response
         * @return
         * @throws Exception
         */
	@RequestMapping(value = "/viewOperate")
	public ModelAndView viewOperate(CoreOperateInfoGenerateVo coreOperateInfoGenerateVo, HttpServletRequest request,
									HttpServletResponse response) throws Exception {
		List<com.vedeng.invi.api.dto.OrgDTO> allOrgList = new ArrayList<>();
		try {
			ResultInfo<?> orgResult = NewHttpClientUtils.doPost(inviUrl + "/org/queryOrg");
			if (orgResult.getCode() == 200) {
				//1、使用JSONObject
				com.vedeng.invi.api.dto.OrgListDTO orgListDTO = JSON.parseObject((String)orgResult.getData(),com.vedeng.invi.api.dto.OrgListDTO.class);
				allOrgList = orgListDTO.getOrgList();
			}
		} catch (Exception e) {
			logger.error("区域商城后台接口异常" , e);
		}


		try{
			ModelAndView viewOprateMv = new ModelAndView();

			// 此处是获取所有的区域商城，用以“其他信息”模块编辑sku归属的选项值
			viewOprateMv.addObject("allOrg", allOrgList);

			if (coreOperateInfoGenerateVo != null ){
				if(coreOperateInfoGenerateVo.getOperateInfoId() != null && !"".equals(coreOperateInfoGenerateVo.getOperateInfoId())) {
					//查询运营信息
					coreOperateInfoGenerateVo = coreOperateInfoService.getCoreOperateInfoById(coreOperateInfoGenerateVo);
					if (coreOperateInfoGenerateVo.getOperateInfoType().equals(CommonConstants.OPERATE_INFO_TYPE_SKU_2)){
						//查询该sku对应的spuId
						SkuAddCommand skuAddCommand = new SkuAddCommand();
						skuAddCommand.setSkuId(coreOperateInfoGenerateVo.getSkuId());
						CoreSkuGenerate coreSkuGenerate = goodsService.initSku(skuAddCommand, false);
						coreOperateInfoGenerateVo.setUpSpuId(coreSkuGenerate.getSpuId());
					}
				}else{
					if (coreOperateInfoGenerateVo.getSkuId() != null && !"".equals(coreOperateInfoGenerateVo.getSkuId())) {//SKUID不为空则绑定的是SKU
						Integer skuId = coreOperateInfoGenerateVo.getSkuId();

						//查询该sku对应的运营信息
						coreOperateInfoGenerateVo = coreOperateInfoService.getCoreOperateInfoBySkuId(coreOperateInfoGenerateVo.getSkuId());

						if (coreOperateInfoGenerateVo == null) {
							coreOperateInfoGenerateVo = new CoreOperateInfoGenerateVo();
							coreOperateInfoGenerateVo.setSkuId(skuId);
							//查询sku名称
							coreOperateInfoGenerateVo.setGoodsName(coreOperateInfoService.getProductNameBySkuId(skuId));
							coreOperateInfoGenerateVo.setOperateInfoType(CommonConstants.OPERATE_INFO_TYPE_SKU_2);
						}
						//查询该sku对应的SPUID
						SkuAddCommand skuAddCommand = new SkuAddCommand();
						skuAddCommand.setSkuId(skuId);
						CoreSkuGenerate coreSkuGenerate = goodsService.initSku(skuAddCommand, false);
						coreOperateInfoGenerateVo.setUpSpuId(coreSkuGenerate.getSpuId());
						viewOprateMv.setViewName("goods/vgoods/viewOperate");
					}else {
						Integer spuId = coreOperateInfoGenerateVo.getSpuId();
						//查询该spu对应的运营信息
						coreOperateInfoGenerateVo = coreOperateInfoService.getCoreOperateInfoBySpuId(spuId);
						if (coreOperateInfoGenerateVo == null){
							coreOperateInfoGenerateVo = new CoreOperateInfoGenerateVo();
							//查询spu名称
							coreOperateInfoGenerateVo.setSpuId(spuId);
							coreOperateInfoGenerateVo.setGoodsName(coreOperateInfoService.getProductNameBySpuId(spuId));
							coreOperateInfoGenerateVo.setOperateInfoType(CommonConstants.OPERATE_INFO_TYPE_SPU_1);
						}
						viewOprateMv.setViewName("goods/vgoods/viewOperate1");
					}
				}
				if (coreOperateInfoGenerateVo.getOperateInfoId() != null && !"".equals(coreOperateInfoGenerateVo.getOperateInfoId())){
					//查询商品图片信息
					List<GoodsAttachment> goodsAttachmentList = null;
					GoodsAttachment goodsAttachment = new GoodsAttachment();
					if (coreOperateInfoGenerateVo.getOperateInfoType().equals(CommonConstants.OPERATE_INFO_TYPE_SKU_2)){
						goodsAttachment.setAttachmentType(CommonConstants.ATTACHMENT_TYPE_SKU_1001);
						goodsAttachment.setStatus(CommonConstants.STATUS_1);
						goodsAttachment.setGoodsId(coreOperateInfoGenerateVo.getSkuId());
						coreOperateInfoGenerateVo.setProductName(coreOperateInfoService.getProductNameBySkuId(coreOperateInfoGenerateVo.getSkuId()));
					}else{
						goodsAttachment.setAttachmentType(CommonConstants.ATTACHMENT_TYPE_SPU_1002);
						goodsAttachment.setStatus(CommonConstants.STATUS_1);
						goodsAttachment.setGoodsId(coreOperateInfoGenerateVo.getSpuId());
						coreOperateInfoGenerateVo.setProductName(coreOperateInfoService.getProductNameBySpuId(coreOperateInfoGenerateVo.getSpuId()));
					}
					goodsAttachmentList = coreOperateInfoService.getGoodsAttachment(goodsAttachment);
					coreOperateInfoGenerateVo.setGoodsAttachmentList(goodsAttachmentList);
					// 理商品关键词,处理成数组形式
					if(coreOperateInfoGenerateVo.getSeoKeywords() != null){
						coreOperateInfoGenerateVo.setSeoKeyWordsArray(coreOperateInfoGenerateVo.getSeoKeywords().split("@"));
					}
					String goodsImgJson = this.doGoodsAttachmentToJsonInList(goodsAttachmentList);
					viewOprateMv.addObject("goodsImgJson",goodsImgJson);
				}
			}
			if (coreOperateInfoGenerateVo != null && coreOperateInfoGenerateVo.getSkuId() != null) {
				int pushStatus = baseGoodsService.getPushStatusBySkuId(coreOperateInfoGenerateVo.getSkuId());
				log.info("根据skuid获取推送标识，skuid:{},pushStatus:{}",coreOperateInfoGenerateVo.getSkuId(),pushStatus);
				int onSale=baseGoodsService.getOnsaleBySkuId(coreOperateInfoGenerateVo.getSkuId());

				CoreSkuBaseDTO coreSkuBaseDTO = baseGoodsService.selectSkuBaseById(coreOperateInfoGenerateVo.getSkuId());
				if (!coreSkuBaseDTO.getCheckStatus().equals(CommonConstants.GOODS_CHECK_STATUS_3)){
					viewOprateMv.addObject("canedit","2");
				}
				int synchronizationStatus = baseGoodsService.getSynchronizationStatusBySkuId(coreOperateInfoGenerateVo.getSkuId());
				CoreSku coreSku = coreSkuMapper.selectByPrimaryKey(coreOperateInfoGenerateVo.getSkuId());
				// 查询基本信息模块
				coreOperateInfoGenerateVo.setIsAvailableSale(coreSku.getIsAvailableSale());
				viewOprateMv.addObject("orgIdArray", Arrays.asList(coreSku.getOrgIdList().split(",")));

				// 推送的区域商城，是 16区域商城的子单位，因此需要特殊处理后拼接至原有的平台后面
                String temp = GoodsUtils.getPlatformStrByInteger(pushStatus,"；","");
                List<String> platforms = Arrays.asList(temp.split("；"));
                // 使用Arrays.asList转换的List的remove方会报错，需要一个新的变量转一下
                List<String> newPlatforms = new ArrayList<>(platforms);
				newPlatforms.remove(CommonConstants.REGIONAL_MALL);
                String pushStatusStr = newPlatforms.stream().map(String::valueOf).collect(Collectors.joining("；"));
                // 根据pushedOrgIdList去invi中查询对应的商城名称，拼接
				String pushedOrgIdList = coreSku.getPushedOrgIdList();
				if (StringUtils.isNotBlank(pushedOrgIdList)) {
					List<String> orgIdList = Arrays.asList(pushedOrgIdList.split(","));
					StringBuffer tempPushStatusStr = new StringBuffer(pushStatusStr);
					tempPushStatusStr.append("；");
					allOrgList.forEach(item -> {
						if (orgIdList.contains(item.getOrgId())) {
							tempPushStatusStr.append(item.getOrgName()).append("；");
						}
					});
					pushStatusStr = tempPushStatusStr.toString();
				}

                viewOprateMv.addObject("pushStatusStr", pushStatusStr);

				viewOprateMv.addObject("synchronizationStatus", synchronizationStatus);

				// 各个区域商城 默认为下架状态，需要分开拼接

				String onSaleList = GoodsUtils.getOnSaleStrByPushStatus(pushStatus,onSale,"/","未推送");

				List<String> onSaleAsList = new ArrayList<>(Arrays.asList(onSaleList.split("/")));
				if (StringUtils.isNotBlank(pushedOrgIdList)){
					List<String> orgIdAsList = Arrays.asList(pushedOrgIdList.split(","));
					if (CollectionUtils.isNotEmpty(orgIdAsList)){
						for (String s : orgIdAsList) {
							onSaleAsList.add("下架");
						}
					}
				}

				if (platforms.contains(CommonConstants.REGIONAL_MALL) && StringUtils.isNotBlank(pushedOrgIdList)) {
					int index = platforms.indexOf(CommonConstants.REGIONAL_MALL);
					onSaleAsList.remove(index);
				}
				String tempOnSaleList = onSaleAsList.stream().collect(Collectors.joining("/"));

				viewOprateMv.addObject("onSaleStr",tempOnSaleList);
			}
			// 马良图解详情处理
			mlFacadeService.mlSkuGraphicDetailHandler(coreOperateInfoGenerateVo);


			viewOprateMv.addObject("coreOperateInfoGenerateVo",coreOperateInfoGenerateVo);

			viewOprateMv.addObject("mlUrl", mlServerUrl+"/ml-form?skuId="+coreOperateInfoGenerateVo.getSkuId());


			try {
				// 获取运营平台中：各个平台集合列表
				ResultInfo<?> resultInfo = NewHttpClientUtils.doGet(operateUrl + HttpURLConstant.OP_PLAT_FROM_LIST);
				if(resultInfo != null && resultInfo.getCode() == 200){
					//1、使用JSONObject
					JSONArray arrayStr= JSONArray.fromObject(resultInfo.getData());
					List<Platfrom> platfromList =(List<Platfrom>)JSONArray.toList(JSONArray.fromObject(arrayStr), Platfrom.class);
					viewOprateMv.addObject("platfromList",platfromList);
				}else{
					logger.error("运营后台接口异常"+coreOperateInfoGenerateVo.getSkuId()+JSON.toJSONString(resultInfo));
				}
			} catch (Exception e) {
				logger.error("运营后台接口异常" + operateUrl + HttpURLConstant.OP_PLAT_FROM_LIST, e);
			}

			// 获取归属区域商城列表或者全部区域商城列表（当归属为空时返回全部列表）

			// step：获取sku对应的orgIdList
			SkuAddCommand skuAddCommand = new SkuAddCommand();
			skuAddCommand.setSkuId(coreOperateInfoGenerateVo.getSkuId());
			CoreSkuGenerate coreSkuGenerate = goodsService.initSku(skuAddCommand, false);
			String orgIdList = coreSkuGenerate.getOrgIdList();

			List<String> tempOrgIdArray = Arrays.asList(orgIdList.split(","));
			List<String> orgIdArray = new ArrayList<>(tempOrgIdArray);

			List<com.vedeng.invi.api.dto.OrgDTO> orgDTOList = new ArrayList<>();
			allOrgList.forEach(item -> {
				if (orgIdArray.contains(item.getOrgId())) {
					orgDTOList.add(item);
				}
			});

			// 此处是查询出该sku归属的区域商城集合用于“推送区域商城”弹窗页的选择项
			if (orgDTOList.size() > 0) {
				viewOprateMv.addObject("orgDTOList", orgDTOList);
			} else {
				viewOprateMv.addObject("orgDTOList", allOrgList);
			}

			// ======= 商品分类 热词=======
			List<VHostWordDTO> vHostWordDTOS = hostSkuService.queryHotWordByCategory(coreOperateInfoGenerateVo.getUpSpuId(), coreOperateInfoGenerateVo.getSkuId());
			viewOprateMv.addObject("vHostWordDTOS", vHostWordDTOS);
			if (CollectionUtils.isNotEmpty(vHostWordDTOS)) {
				int count = 0;
				for (VHostWordDTO c : vHostWordDTOS) {
					if (c.getChecked() != null && c.getChecked()) {
						count++;
					}
				}
				if (count == vHostWordDTOS.size()) {
					viewOprateMv.addObject("vHostWordAllCheck", true);
				} else {
					viewOprateMv.addObject("vHostWordAllCheck", false);
				}
			}
			// ======= 商品分类 热词=======

			return viewOprateMv;

		}catch (Exception e){
			logger.error("查看商品运营信息异常：",e);
			return this.page500(request);
		}
	}

	/**
	 * @description 预览运营信息
	 * <AUTHOR>
	 * @param
	 * @date 2019/6/20
	 */
	@RequestMapping("/previewOperate")
	public ModelAndView previewOperate(String randomStr, HttpServletRequest request){
		ModelAndView previewMv = new ModelAndView();
		previewMv.addObject("randomStr",randomStr);
		previewMv.setViewName("goods/vgoods/previewOperate");
		return previewMv;
	}

	/**
	 * 处理商品图片信息,处理成list格式
	 * <AUTHOR>
	 * @param goodsImgArray
	 * @param goodsId
	 * @param operateInfoType
	 * @return
	 * @throws Exception
	 */
	public List<GoodsAttachment> doGoodsAttachment(String[] goodsImgArray,Integer goodsId,Integer operateInfoType){
		List<GoodsAttachment> goodsImgList = new ArrayList<>();
		if (goodsImgArray != null && goodsImgArray.length > 0){
			for (int i = 0; i < goodsImgArray.length; i++){
				GoodsAttachment goodsAttachment = new GoodsAttachment();
				goodsAttachment.setGoodsId(goodsId);
				String path,url_domain = "";
				if(goodsImgArray[i].indexOf("vedeng.com") > 0){
                    path = goodsImgArray[i].substring(goodsImgArray[i].indexOf("vedeng.com") + 10);
                    url_domain = goodsImgArray[i].substring(goodsImgArray[i].indexOf("://") + 3, goodsImgArray[i].indexOf("vedeng.com") + 10).replaceAll("/","");
                } else {
                    path = "/" + goodsImgArray[i].substring(goodsImgArray[i].indexOf("upload"));
                    url_domain = goodsImgArray[i].substring(goodsImgArray[i].indexOf("://")+3, goodsImgArray[i].indexOf("upload")).replaceAll("/","");
                }
				if (operateInfoType.equals(CommonConstants.OPERATE_INFO_TYPE_SPU_1)){
					goodsAttachment.setAttachmentType(CommonConstants.ATTACHMENT_TYPE_SPU_1002);
				}else{
					goodsAttachment.setAttachmentType(CommonConstants.ATTACHMENT_TYPE_SKU_1001);
				}
				goodsAttachment.setUri(path);
				goodsAttachment.setSort(i+1);
				goodsAttachment.setStatus(CommonConstants.STATUS_1);
				goodsAttachment.setDomain(EmptyUtils.isBlank(url_domain)?domain:url_domain);
				goodsImgList.add(goodsAttachment);
			}
		}
		return goodsImgList;
	}

	/**
	 * 处理商品图片信息,处理成json格式
	 * <AUTHOR>
	 * @param
	 * @return
	 * @throws Exception
	 */
	public String doGoodsAttachmentToJsonInList(List<GoodsAttachment> goodsImgList){
		String imgJson = "[";
		if (CollectionUtils.isNotEmpty(goodsImgList)){
			for (GoodsAttachment goodsAttachment : goodsImgList){

				//不是阿里云的domain
				if (goodsAttachment.getUri().indexOf("resourceId") == -1) {
					String uri = goodsAttachment.getUri();
					String fileName = "";
					String prefix = uri.substring(uri.lastIndexOf(".")+1);
					String filePath = uri;
					if(StringUtils.isNotBlank(goodsAttachment.getDomain())){
						imgJson = imgJson + "{\"fileName\":\""+fileName+"\",\"httpUrl\":\""
								+"http://"+goodsAttachment.getDomain()+"\",\"prefix\":\""+prefix+"\",\"filePath\":\""+filePath+"\"},";
					}else{
						imgJson = imgJson + "{\"fileName\":\""+fileName+"\",\"httpUrl\":\""
								+"http://"+domain+"\",\"prefix\":\""+prefix+"\",\"filePath\":\""+filePath+"\"},";
					}
				}

				//已同步
				if (goodsAttachment.getUri().indexOf("resourceId") >= 0) {
					String uri = goodsAttachment.getUri();
					String fileName = "";
					String prefix = uri.substring(uri.lastIndexOf(".")+1);
					String filePath = goodsAttachment.getUri();
					if(StringUtils.isNotBlank(goodsAttachment.getDomain())){
						imgJson = imgJson + "{\"fileName\":\""+fileName+"\",\"httpUrl\":\""
								+"http://"+goodsAttachment.getDomain()+"\",\"prefix\":\""+prefix+"\",\"filePath\":\""+filePath+"\"},";
					}else{
						imgJson = imgJson + "{\"fileName\":\""+fileName+"\",\"httpUrl\":\""
								+"http://"+domain+"\",\"prefix\":\""+prefix+"\",\"filePath\":\""+filePath+"\"},";
					}
				}


			}
		}
		imgJson = imgJson.length() == 1 ? imgJson+"]" : imgJson.substring(0,imgJson.length()-1) + "]";
		return imgJson;
	}

	/**
	 * 处理商品图片信息,处理成json格式
	 * <AUTHOR>
	 * @param
	 * @return
	 * @throws Exception
	 */
	public String doGoodsAttachmentToJsonInArray(String[] array){
		String imgJson = "[";
		if (array != null && array.length > 0){
			for (String uri : array){
				String fileName = uri.substring(uri.lastIndexOf("/"));
				String prefix = uri.substring(uri.lastIndexOf("."));
				String filePath = uri.substring(0,uri.lastIndexOf("/"));
				imgJson = imgJson + "{\"fileName\":\""+fileName+"\",\"httpUrl\":'"
						+"http://"+domain+"\",\"prefix\":\""+prefix+"\",\"filePath\":\""+filePath+"\"},";
			}
		}
		imgJson = imgJson.length() == 1 ? imgJson+"]" : imgJson.substring(0,imgJson.length()-1) + "]";
		return imgJson;
	}

	/**
	 * 检查字段合法性
	 * <AUTHOR>
	 * @param
	 * @return
	 * @throws Exception
	 */
	public ResultInfo checkOperateInfo(CoreOperateInfoGenerateVo coreOperateInfoGenerateVo){
		if (coreOperateInfoGenerateVo.getGoodsImage() == null || coreOperateInfoGenerateVo.getGoodsImage().length <= 0){
			return new ResultInfo(CommonConstants.FAIL_CODE,"商品图片至少上传一张，无法提交");
		}
		if (coreOperateInfoGenerateVo.getGoodsImage() != null && coreOperateInfoGenerateVo.getGoodsImage().length > 5){
			return new ResultInfo(CommonConstants.FAIL_CODE,"商品图片最多只能上传5张，无法提交");
		}
		// 信息来源判断
		OperateInfoSourceEnum sourceEnum = OperateInfoSourceEnum.getBySource(coreOperateInfoGenerateVo.getOperateInfoSource());
//		if (OperateInfoSourceEnum.UNKNOWN.equals(sourceEnum)) {
//			return new ResultInfo(CommonConstants.FAIL_CODE, "图文详情类型不可为空，无法提交");
//		}

		if (OperateInfoSourceEnum.ERP.equals(sourceEnum) && StringUtils.isBlank(coreOperateInfoGenerateVo.getOprateInfoHtml())) {
			return new ResultInfo(CommonConstants.FAIL_CODE, "商品详情不可为空，无法提交");
		}

		return new ResultInfo(CommonConstants.SUCCESS_CODE,CommonConstants.SUCCESS_MSG);
	}

	/**
	 * 图片上传压缩
	 * <AUTHOR>
	 * @param
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping(value = "fileUploadImg")
	public FileInfo fileUploadImg(HttpServletRequest request, HttpServletResponse response, @RequestBody MultipartFile file) {
		ChainResult result = imgUploadVerifyActuator.ruleExecute(request, file);
		if(!result.isSuccess()){
			return new FileInfo(-1,result.getData().toString());
		}
		FileInfo fileInfo = ossUtilsService.upload2Oss(request,file,true);
		fileInfo.setPrefix(fileInfo.getFileName().substring(fileInfo.getFileName().indexOf(".")+1));
		fileInfo.setHttpUrl(api_http + fileInfo.getHttpUrl());
		fileInfo.setFullPath(fileInfo.getHttpUrl() + fileInfo.getFilePath());
		return fileInfo;
	}

	@RequestMapping(value = "/fileUploadEz")
	@ResponseBody
	@NoNeedAccessAuthorization
	public RestfulResult fileUploadEz( @RequestBody  MultipartFile file, HttpServletResponse response, HttpServletRequest request) throws IOException {
		String w = request.getParameter("width");
		String h = request.getParameter("height");
		byte[] bytes = file.getBytes();
		if((NumberUtils.toInt(w)>0 && NumberUtils.toInt(h)>0)  ){
			BufferedImage image = ImageIO.read(new ByteArrayInputStream(bytes));
			int width = image.getWidth();
			int height = image.getHeight();
			if( (width != Integer.parseInt(w) || height != Integer.parseInt(h))){
				return RestfulResult.fail("IMAGE_PIXEL_ERROR","尺寸异常：请上传"+w+"*"+h+"尺寸的图片");
			}
		}
		Map<String,String> map=new HashMap<>(8);
		try {
			MultipartFile  upfile = file;
			FileInfo fileInfo = ossUtilsService.upload2Oss(request,file,false);
			//FileDto fileDto = ossFileUploadService.uploadFile("", "",  upfile);
			map.put("resourceId",fileInfo.getOssResourceId());
			map.put("fileId",fileInfo.getOssResourceId());
			map.put("fileName",file.getOriginalFilename());
			map.put("url", api_http + fileInfo.getOssUrl());
			map.put("src", api_http + fileInfo.getOssUrl());
			map.put("downloadUrl", api_http + fileInfo.getOssUrl());
			//  resourceId = OSSManageUtil.uploadFileNew(new ByteArrayInputStream(bytes), file.getOriginalFilename(), true);
		} catch (Exception e) {
			log.error("uploadFileNew Error",e);
		}
		map.put("fileName", file.getOriginalFilename());
		map.put("size",bytes.length+"");
//        String path = request.getContextPath();
//        map.put("downloadUrl", "/file/download/"+resourceId);
		return RestfulResult.success(map);
	}


	@RequestMapping(value = "/static/toBatchPushGoods")
	public  ModelAndView toBatchPushGoods(){
		ModelAndView previewMv = new ModelAndView();

		previewMv.setViewName("goods/vgoods/toBatchPush");
		return previewMv;
	}
	@ResponseBody
	@RequestMapping(value = "/static/batchPushGoods")
	public ResultInfo batchPushGoods(@RequestParam(required = false, value = "platfromIds") String platfromIds,
									@RequestParam(required = false, value = "skuIds") String skuIds){
		if(StringUtils.isBlank(platfromIds) || skuIds == null){
			return new ResultInfo(-1,"参数错误");
		}
		try {
			Arrays.stream(skuIds.split(",")).forEach(skuId->{
				StringBuilder sb=new StringBuilder();
				for (int i = 0; i < skuId.toCharArray().length; i++) {
					if(Character.isDigit(skuId.charAt(i))){
						sb.append(skuId.charAt(i));
					}
				}
				goodsService.pushSkuInfo(platfromIds, Integer.parseInt(sb.toString()), 0, null);
			});
		}catch (ShowErrorMsgException e){
			return new ResultInfo(-1,e.getMessage());
		}
		catch (Exception e) {
			logger.error("batchPushGoods 推送商品【skuId:" + skuIds + "】时，运营后台接口异常" + operateUrl + HttpURLConstant.OP_PUSH_GOODS_INFO, e);
		}
		return new ResultInfo();
	}
	@ResponseBody
	@RequestMapping(value = "pushGoodsInfo")
	public ResultInfo pushGoodsInfo(@RequestParam(required = false, value = "platfromIds") String platfromIds,
									@RequestParam(required = false, value = "skuId") Integer skuId,
									@RequestParam(required = false, value = "spuId") Integer spuId,
									@RequestParam(required = false, value = "pushOrgIdList") String pushOrgIdList){
		if(StringUtils.isBlank(platfromIds) || skuId == null){
			return new ResultInfo(-1,"参数错误");
		}
		try {
            ResultInfo resultInfo4Check = goodsCommonService.checkGoodsInfo4Push(skuId);
            if (resultInfo4Check != null){
                StringBuffer assignmentStr = new StringBuffer();
                List<User> assignmenters = goodsService.getAssignmentersBySkuId(skuId);
                if (CollectionUtils.isNotEmpty(assignmenters)){
                    assignmenters.forEach(user -> {
                        assignmentStr.append(user.getUsername());
                        assignmentStr.append("&");
                    });
                    assignmentStr.deleteCharAt(assignmentStr.length() - 1);
                }
                resultInfo4Check.setData(assignmentStr.toString());
                return resultInfo4Check;
            }

            if (goodsService.pushSkuInfo(platfromIds, skuId, spuId, pushOrgIdList)) {
                return new ResultInfo(0, "操作成功");
            }
		}catch (ShowErrorMsgException e){
			return new ResultInfo(-1,e.getMessage());
		}
		catch (Exception e) {
			logger.error("推送商品【skuId:" + skuId + "】时，运营后台接口异常" + operateUrl + HttpURLConstant.OP_PUSH_GOODS_INFO, e);
		}
		return new ResultInfo();
	}


	@ResponseBody
	@RequestMapping(value = "/handleOldData")
	public ResultInfo handleOldData(@RequestParam(required = false, value = "skuNos") String skuNos,
									@RequestParam(required = false, value = "platfromId") String platfromId) {
		try {
			logger.info("handleOldData接收到参数skuNos{}" + skuNos + ";platfromId{}" + platfromId);
			List<String> skuNoList = new ArrayList<>();
			if (EmptyUtils.isNotBlank(skuNos)) {
				String[] strings = skuNos.split(",");
				skuNoList = Arrays.asList(strings);
			}

			if (CollectionUtils.isEmpty(skuNoList) || EmptyUtils.isBlank(platfromId)) {
				return new ResultInfo(-1, "推送平台编号和推送skuNo不允许为空，或格式错误");
			}

			List<Integer> skuIdList = coreOperateInfoService.getCoreSkuInfoBySkuNo(skuNoList);
			for (int i = 0; i < skuIdList.size(); i++) {
				// 默认贝登
				JSONObject jsonObject = coreOperateInfoService.getPushGoodsInfo(platfromId, skuIdList.get(i), null);
				logger.info("推送运营后台初始化数据：" + skuIdList.get(i) + ":" + jsonObject);
				if (jsonObject != null) {
					Map<String, String> map = new HashMap<>();
					map.put("operateInfo", jsonObject.toJSONString());
					// 获取运营平台中：各个平台集合列表
					ResultInfo<?> resultInfo = NewHttpClientUtils.doPost(operateUrl + HttpURLConstant.OP_PUSH_GOODS_INFO, map);
					if (resultInfo != null && resultInfo.getCode() == 200 && "success".equals(JSON.parseObject(resultInfo.getData().toString()).get("code"))) {
						logger.info("推送商品【skuId:" + skuIdList.get(i) + "】运营信息:success");

						// 更新二进制的已推送平台字段
						String[] split = platfromId.split(",");
						List<String> platfromIdList = Arrays.asList(split);
						coreOperateInfoService.updateSkuPushStatus(platfromIdList, skuIdList.get(i));

					} else {
						logger.error("推送商品【skuId:" + skuIdList.get(i) + "】运营信息失败：" + resultInfo.toString());
					}
				}
			}
			return new ResultInfo(0, "操作成功");
		} catch (Exception e) {
			logger.error("运营后台接口异常" + operateUrl + HttpURLConstant.OP_PUSH_GOODS_INFO, e);
		}
		return new ResultInfo();
	}

	@Autowired
	CoreOperateInfoGenerateExtendMapper coreOperateInfoGenerateExtendMapper;


//	@ResponseBody
//	@RequestMapping("/spuSend")
//	public ResultInfo spuSend(@RequestParam("spuIdsn")String  spuIdsn,@RequestParam("platfromId") Integer platfromId){
//		String [] spuIds=spuIdsn.split(",");
//		List<String> platfromIds=new ArrayList<>();
//		platfromIds.add(platfromId.toString());
//		for(int i =0;i<spuIds.length;i++){
//			OperateSpuVo operateSpuVo = coreOperateInfoGenerateExtendMapper.selectSpuOperateBySpuId(Integer.parseInt(spuIds[i]));
//			Map<String,String> map = new HashMap<>();
//			map.put("operateInfo", JSON.toJSONString(operateSpuVo));
//			try {
//				ResultInfo<?> resultInfo = NewHttpClientUtils.doPost("http://localhost:8084/" + HttpURLConstant.OP_PUSH_GOODS_INFO, map);
//				if(resultInfo != null && resultInfo.getCode() == 200 && "success".equals(JSON.parseObject(resultInfo.getData().toString()).get("code"))){
//					logger.info("推送商品spu运营信息:success");
//				}else{
//					logger.info("推送商品spu运营信息:fail");
//				}
//			} catch (Exception e) {
//				logger.error("运营后台接口异常" + operateUrl + HttpURLConstant.OP_PUSH_GOODS_INFO, e);
//			}
//
//		}
//		return new ResultInfo();
//	}



	/**
	 * <b>Description:</b>获取上架状态字符串<br>
	 * @param
	 * @return
	 * @Note
	 * <b>Author:calvin</b>
	 * <br><b>Date:</b> 2020/7/30
	 */
	private String getSaleStr(Integer pushStatus,Integer onSale){
		int[] pushArr=getBitArrayFromInt(pushStatus);
		int[] onSaleArr=getBitArrayFromInt(onSale);
		String resStr="";
		for(int i=GoodsConstants.ZERO;i<GoodsConstants.THREE;i++){
			String mid="";
			if(pushArr[i]==1){
				if(onSaleArr[i]==GoodsConstants.ZERO){
					mid="下架";
				}else if(onSaleArr[i]==GoodsConstants.ONE){
					mid="上架";
				}
				if(StringUtil.isBlank(resStr)){
					resStr=mid;
				}else{
					resStr=resStr+"/"+mid;
				}
			}

		}
		if(StringUtil.isBlank(resStr)){
			resStr="未上架";
		}
		return resStr;
	}
	private int[] getBitArrayFromInt(Integer i){
		int[] res={0,0,0};
		if(i==null){
			return res;
		}
		res[0]=i & GoodsConstants.ONE;
		res[1]=i>>>GoodsConstants.ONE & GoodsConstants.ONE;
		res[2]=i>>>GoodsConstants.TWO & GoodsConstants.ONE;
		return res;
	}

	/**
	 * 更新sku的seo关键字
	 */
	@NoNeedAccessAuthorization
	@ResponseBody
	@RequestMapping(value = "/updateSeoKeyWords")
	public ResultInfo updateSeoKeyWords(@RequestParam(value = "seoKeyWordsArray") String[] seoKeyWordsArray, @RequestParam(value = "skuId") Integer skuId,@RequestParam(value = "hotWords",required = false)List<String> hotWords) {
		if ( skuId != null) {
			if (hotWords == null) {
				hotWords = Collections.emptyList();
			}
			List<Integer> integers = hostSkuService.saveHotWord(hotWords);
			if (integers == null) {
				integers = Collections.emptyList();
			}
			ServletRequestAttributes ra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
			HttpServletRequest request = ra.getRequest();
			User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
			if (user == null||StringUtils.isEmpty(user.getUsername())) {
				user = new User();
				user.setUsername("njadmin");
			}
			hostSkuService.bindGoodsHotWords(integers, skuId,user.getUsername());
		}
		if (seoKeyWordsArray.length == 0) {
			return new ResultInfo(CommonConstants.FAIL_CODE,"SEO信息不能为空");
		} else {
			coreOperateInfoService.updateSeoKeyWords(seoKeyWordsArray, skuId);
		}
		return new ResultInfo();
	}

	/**
	 * 更新其他信息模块
	 */
	@NoNeedAccessAuthorization
	@ResponseBody
	@RequestMapping(value = "/updateOtherInfo")
	public ResultInfo updateOtherInfo(@RequestParam(value = "skuId") Integer skuId, @RequestParam(value = "isAvailableSale") Integer isAvailableSale, @RequestParam(value = "orgIdArray", required = false) String[] orgIdArray) {
		CoreSku sku = new CoreSku();
		sku.setSkuId(skuId);
		sku.setIsAvailableSale(isAvailableSale);

		// 不归属任何区域商城的时候，设置为空字符，否则Selective方法的sql会被过滤掉
		String orgIdList = Objects.isNull(orgIdArray) ? "" : org.apache.commons.lang.StringUtils.join(orgIdArray, ",");
		sku.setOrgIdList(orgIdList);
		coreSkuMapper.updateByPrimaryKeySelective(sku);
		return new ResultInfo();
	}

}