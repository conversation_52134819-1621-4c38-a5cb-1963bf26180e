package com.vedeng.erp.saleorder.dto;

import com.vedeng.common.core.base.BaseDto;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/4 14:55
 */
@Getter
@Setter
public class SaleOrderTerminalCustomerDto extends BaseDto {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 销售单id
     */
    private Integer saleOrderId;

    /**
     * 交易者id
     */
    private Integer traderId;

    /**
     * 客户id
     */
    private Integer traderCustomerId;

    /**
     * 终端名称
     */
    private String traderName;

    /**
     * 是否删除
     */
    private Integer isDeleted;
}