package com.vedeng.erp.settlement.manager;

import com.vedeng.erp.finance.cmd.SettlementBillCreateCmd;
import com.vedeng.erp.finance.cmd.SettlementBillSettleCmd;

/**
 * 结算单
 *
 * <AUTHOR>
 */
public interface Settlement {

    /**
     * 生成结算单
     *
     * @param settlementBillCreateCmd 结算参数上下文类
     * @return 结算单id
     */
    Integer create(SettlementBillCreateCmd settlementBillCreateCmd);

    /**
     * 结算
     *
     * @param settlementBillSettleCmd 结算参数上下文类
     */
    void settle(SettlementBillSettleCmd settlementBillSettleCmd);
}
