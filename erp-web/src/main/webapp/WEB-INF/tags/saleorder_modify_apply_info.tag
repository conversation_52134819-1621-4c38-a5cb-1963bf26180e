<%@ tag language="java" pageEncoding="UTF-8"%>
<%@attribute name="saleorderModifyApplyList" type="java.util.List" required="true" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>


<div class="parts" liname="订单修改" id="订单修改">
    <div class="title-container">
        <div class="table-title nobor">
            订单修改申请
        </div>
    </div>
    <table class="table">
        <thead>
        <tr>
            <th>订单修改申请单</th>
            <th>申请人</th>
            <th>审核状态</th>
        </tr>
        </thead>
        <tbody>
        <c:forEach var="list" items="${saleorderModifyApplyList}" varStatus="num3">
            <tr>
                <td>
                    <a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewsaleordermodifyapply${list.saleorderModifyApplyId}","link":"./orderstream/saleorder/viewModifyApply.do?saleorderModifyApplyId=${list.saleorderModifyApplyId}&saleorderId=${list.saleorderId}","title":"订单信息"}'>${list.saleorderModifyApplyNo}</a>
                </td>
                <td>
                    <c:choose>
                        <c:when test="${list.onlinePersonId > 0}">
                            ID：${list.onlinePersonId}
                        </c:when>
                        <c:otherwise>
                            ${list.createName}
                        </c:otherwise>
                    </c:choose>
                </td>
                <td>
                    <c:choose>
                        <c:when test="${list.verifyStatus eq 0}">
                            审核中
                        </c:when>
                        <c:when test="${list.verifyStatus eq 1}">
                            审核通过
                        </c:when>
                        <c:when test="${list.verifyStatus eq 2}">
                            审核未通过
                        </c:when>
                        <c:otherwise>
                            待审核
                        </c:otherwise>
                    </c:choose>
                </td>
            </tr>
        </c:forEach>
        <c:if test="${empty saleorderModifyApplyList}">
            <tr>
                <td colspan="3">暂无订单修改申请。</td>
            </tr>
        </c:if>
        </tbody>
    </table>
</div>
