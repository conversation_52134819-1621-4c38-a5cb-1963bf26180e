package com.vedeng.price.dao;

import com.pricecenter.dto.SkuPriceModifyRecordIndexDto;
import com.vedeng.common.page.Page;
import com.vedeng.price.dto.SkuPriceModifyRecordSearchDto;
import com.vedeng.price.model.SkuPriceModifyRecord;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

public interface SkuPriceModifyRecordMapper {

    int deleteByPrimaryKey(Long skuPriceModifyRecordId);

    int insert(SkuPriceModifyRecord record);

    int insertSelective(SkuPriceModifyRecord record);

    SkuPriceModifyRecord selectByPrimaryKey(Long skuPriceModifyRecordId);

    int updateByPrimaryKeySelective(SkuPriceModifyRecord record);

    int updateByPrimaryKey(SkuPriceModifyRecord record);

    List<SkuPriceModifyRecordIndexDto> findByListPage(Map<String, Object> map);

    SkuPriceModifyRecordIndexDto getLastestPriceChangeRecord(Integer skuId);

    List<SkuPriceModifyRecord> getYestedayPriceChangeInfo();
}