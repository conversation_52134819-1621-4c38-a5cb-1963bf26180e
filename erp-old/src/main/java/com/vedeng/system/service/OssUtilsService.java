package com.vedeng.system.service;

import com.vedeng.common.model.FileInfo;
import com.vedeng.common.model.OssInfo;
import com.vedeng.system.model.vo.UrlToPdfParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
/**
 * <AUTHOR>
 * @date created in 2020/3/5 11:49
 */
public interface OssUtilsService {
    /**
     * <b>Description:</b>上传文件到Oss<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/3/5
     */
    FileInfo upload2Oss(HttpServletRequest request, MultipartFile upfile );
    FileInfo upload2Oss(HttpServletRequest request, MultipartFile upfile,boolean isCompress);
    /**
     * <b>Description:</b>上传流到Oss<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/3/5
     */
    String upload2OssForInputStream(String suffix, String newFileName, String ossTargetUrl, InputStream inputStream);

    String migrateFile2Oss(String originSourceUrl, String suffix, String fileName, UrlToPdfParam urlToPdfParam);

    String uploadFileStream2Oss(InputStream inputStream, String suffixOfFile);
      OssInfo sendFile2Oss(String fileSourceUrl, InputStream inputStream, boolean compress);

    }
