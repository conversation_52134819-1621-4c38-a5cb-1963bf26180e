package com.vedeng.erp.finance.common.config.statemachine;

import com.vedeng.erp.finance.domain.context.InvoiceRedConfirmationContext;
import com.vedeng.erp.finance.enums.InvoiceRedConfirmationEvent;
import com.vedeng.erp.finance.enums.InvoiceRedConfirmationStateEnum;
import com.vedeng.erp.finance.manager.action.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import com.vedeng.common.statemachine.Condition;
import com.vedeng.common.statemachine.StateMachine;
import com.vedeng.common.statemachine.builder.StateMachineBuilder;
import com.vedeng.common.statemachine.builder.StateMachineBuilderFactory;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 财务模块红字确认单状态机配置类
 * @date 2023/10/17 11:22
 */
@Configuration
@Slf4j
public class InvoiceRedConfirmationOperaMachineBuilderConfig {

    private static final String STATE_MACHINE_ID = "invoiceRedConfirmation_oper";

    @Bean(name = "invoiceRedConfirmationOperaMachine")
    public StateMachine<InvoiceRedConfirmationStateEnum, InvoiceRedConfirmationEvent, InvoiceRedConfirmationContext> invoiceRedConfirmationOperaMachine(
            InitAction initAction,
            ApplyAction applyAction,
            OpenInvoiceAction openInvoiceAction,
            CancelAction cancelAction,
            ConfirmAction confirmAction) {

        StateMachineBuilder<InvoiceRedConfirmationStateEnum, InvoiceRedConfirmationEvent, InvoiceRedConfirmationContext> builder = StateMachineBuilderFactory.create();

        /** 初始化 **/
        builder.internalTransition()
                .within(InvoiceRedConfirmationStateEnum.INIT)
                .on(InvoiceRedConfirmationEvent.USER_INIT)
                .when(checkCondition())
                .perform(initAction);
        builder.internalTransition()
                .within(InvoiceRedConfirmationStateEnum.INIT)
                .on(InvoiceRedConfirmationEvent.SYS_INIT)
                .when(checkCondition())
                .perform(initAction);
        builder.internalTransition()
                .within(InvoiceRedConfirmationStateEnum.HAVE_BEEN_CANCEL)
                .on(InvoiceRedConfirmationEvent.SYS_INIT)
                .when(checkCondition())
                .perform(initAction);
        builder.internalTransition()
                .within(InvoiceRedConfirmationStateEnum.HAVE_BEEN_INVOICE)
                .on(InvoiceRedConfirmationEvent.SYS_INIT)
                .when(checkCondition())
                .perform(initAction);
        builder.internalTransition()
                .within(InvoiceRedConfirmationStateEnum.HAVE_BEEN_CONFIRM)
                .on(InvoiceRedConfirmationEvent.SYS_INIT)
                .when(checkCondition())
                .perform(initAction);
        builder.internalTransition()
                .within(InvoiceRedConfirmationStateEnum.HAVE_BEEN_APPLY)
                .on(InvoiceRedConfirmationEvent.SYS_INIT)
                .when(checkCondition())
                .perform(initAction);


        /** 申请 **/
        builder.internalTransition()
                .within(InvoiceRedConfirmationStateEnum.HAVE_BEEN_APPLY)
                .on(InvoiceRedConfirmationEvent.APPLY)
                .when(checkCondition())
                .perform(applyAction);

        builder.internalTransition()
                .within(InvoiceRedConfirmationStateEnum.HAVE_BEEN_CONFIRM)
                .on(InvoiceRedConfirmationEvent.APPLY)
                .when(checkCondition())
                .perform(confirmAction);

        builder.internalTransition()
                .within(InvoiceRedConfirmationStateEnum.HAVE_BEEN_INVOICE)
                .on(InvoiceRedConfirmationEvent.APPLY)
                .when(checkCondition())
                .perform(openInvoiceAction);

        builder.internalTransition()
                .within(InvoiceRedConfirmationStateEnum.HAVE_BEEN_CANCEL)
                .on(InvoiceRedConfirmationEvent.APPLY)
                .when(checkCondition())
                .perform(cancelAction);


        /** 同意 **/
        builder.internalTransition()
                .within(InvoiceRedConfirmationStateEnum.HAVE_BEEN_APPLY)
                .on(InvoiceRedConfirmationEvent.AGREE)
                .when(checkCondition())
                .perform(confirmAction);

        /** 拒绝 **/
        builder.internalTransition()
                .within(InvoiceRedConfirmationStateEnum.HAVE_BEEN_CANCEL)
                .on(InvoiceRedConfirmationEvent.REJECT)
                .when(checkCondition())
                .perform(cancelAction);

        /** 开票 **/
        builder.internalTransition()
                .within(InvoiceRedConfirmationStateEnum.HAVE_BEEN_CONFIRM)
                .on(InvoiceRedConfirmationEvent.OPEN_INVOICE)
                .perform(openInvoiceAction);
        builder.internalTransition()
                .within(InvoiceRedConfirmationStateEnum.HAVE_BEEN_INVOICE)
                .on(InvoiceRedConfirmationEvent.OPEN_INVOICE)
                .perform(openInvoiceAction);


        /** 撤销 **/
        builder.internalTransition()
                .within(InvoiceRedConfirmationStateEnum.HAVE_BEEN_CANCEL)
                .on(InvoiceRedConfirmationEvent.CANCEL)
                .perform(cancelAction);


        /** 更新 **/
        builder.internalTransition()
                .within(InvoiceRedConfirmationStateEnum.HAVE_BEEN_APPLY)
                .on(InvoiceRedConfirmationEvent.RENEW)
                .when(checkCondition())
                .perform(applyAction);

        builder.internalTransition()
                .within(InvoiceRedConfirmationStateEnum.HAVE_BEEN_CONFIRM)
                .on(InvoiceRedConfirmationEvent.RENEW)
                .when(checkCondition())
                .perform(confirmAction);

        builder.internalTransition()
                .within(InvoiceRedConfirmationStateEnum.HAVE_BEEN_INVOICE)
                .on(InvoiceRedConfirmationEvent.RENEW)
                .when(checkCondition())
                .perform(openInvoiceAction);


        builder.internalTransition()
                .within(InvoiceRedConfirmationStateEnum.HAVE_BEEN_CANCEL)
                .on(InvoiceRedConfirmationEvent.RENEW)
                .when(checkCondition())
                .perform(cancelAction);

        StateMachine<InvoiceRedConfirmationStateEnum, InvoiceRedConfirmationEvent, InvoiceRedConfirmationContext> invoiceRedConfirmationOperaMachine = builder.build(STATE_MACHINE_ID);

        //打印uml图
        String plantUml = invoiceRedConfirmationOperaMachine.generatePlantUML();
        log.info(plantUml);
        return invoiceRedConfirmationOperaMachine;
    }

    private Condition<InvoiceRedConfirmationContext> checkCondition() {
        return (ctx) -> true;
    }

}
