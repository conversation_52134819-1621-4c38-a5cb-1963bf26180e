<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="库存调整单" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<script type="text/javascript" src='<%= basePath %>static/js/goods/brand/index.js?rnd=${resourceVersionKey}'></script>
<div class="content">
    <div class="searchfunc">

        <form method="post" id="search" action="<%=basePath%>wms/inventory/inventoryAdjustment.do">
            <ul>
                <li>
                    <label class="infor_name">库存调整单号：</label>
                    <input type="text" class="input-middle" name="inventoryAdjustmentNo" id="inventoryAdjustmentNo"
                           value="${inventoryAdjustmentSearchDto.inventoryAdjustmentNo}">
                </li>

                <li>
                    <label class="infor_name">单据类型：</label>
                    <select name="type" class="input-middle f_left">
                        <option value="0"
                                <c:if test="${inventoryAdjustmentSearchDto.type eq 0}">selected="selected"</c:if>>全部
                        </option>

                        <option value="1"
                                <c:if test="${inventoryAdjustmentSearchDto.type eq 1}">selected="selected"</c:if>>
                            库存调整单
                        </option>
                    </select>
                </li>

                <li>
                    <label class="infor_name">创建时间：</label>
                    <input class="Wdate f_left input-smaller96 mr5" type="text" placeholder="请选择日期"
                           onClick="WdatePicker()" autocomplete="off"
                           onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'searchEndTime\')}'})"
                           name="searchBeginTime" id="searchBeginTime"
                           value='<date:date value ="${inventoryAdjustmentSearchDto.searchBeginTime}" format="yyyy-MM-dd" />'>
                    <div class="gang">-</div>
                    <input class="Wdate f_left input-smaller96" type="text" placeholder="请选择日期" autocomplete="off" onClick="WdatePicker()"
                           onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'searchBeginTime\')}'})"
                           name="searchEndTime" id="searchEndTime"
                           value='<date:date value ="${inventoryAdjustmentSearchDto.searchEndTime}" format="yyyy-MM-dd" />'>
                </li>
            </ul>
            <div class="tcenter">
                <span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();"
                      id="searchSpan">搜索</span>
                <span class="bt-small bg-light-blue bt-bg-style" onclick="reset();">重置</span>
            </div>
        </form>
    </div>
    <div class='normal-list-page list-page'>
        <table
                class="table table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr class="sort">
                <th class="wid5">库存调整单号</th>
                <th class="wid6">单据类型</th>
                <th class="wid10">创建时间</th>
            </tr>
            </thead>
            <tbody class="brand">
            <c:if test="${not empty inventoryAdjustmentVos}">
                <c:forEach items="${inventoryAdjustmentVos }" var="list" varStatus="status">
                    <tr>
                        <td>
                            <a href="javascript:void(0)"
                               onclick="viewInventoryAdjustmentrDetail('${list.inventoryAdjustmentId}')">${list.inventoryAdjustmentNo}</a>
                        </td>
                        <td>
                                ${list.typeStr}
                        </td>
                        <td><date:date value="${list.creatTime}"/></td>
                    </tr>
                </c:forEach>
            </c:if>
            </tbody>
        </table>
        <c:if test="${empty inventoryAdjustmentVos}">
            <!-- 查询无结果弹出 -->
            <div class="noresult">查询无结果！请尝试使用其他搜索条件。</div>
        </c:if>
        <tags:page page="${page}"/>
    </div>
</div>
<script>
    /**
     * 查看库存调整单详情
     * @param inventoryTransferId 库存调整单ID
     */
    function viewInventoryAdjustmentrDetail(inventoryAdjustmentId) {
        $.ajax({
            url: '/wms/inventory/inventoryAdjustmentDetail.do?inventoryAdjustmentId=' + inventoryAdjustmentId,
            type: 'get',
            dataType: "html",
            success: function (res) {
                layer.open({
                    type: 1,
                    shade: 0.1,
                    area: ['850', '600px'],
                    title: '库存调整单',
                    content: res,
                    success: function (layero, index) {

                    }
                });
            },
            error: function (data) {
                if (data.status == 1001) {
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        })
    }

</script>
<%@ include file="../../common/footer.jsp" %>
