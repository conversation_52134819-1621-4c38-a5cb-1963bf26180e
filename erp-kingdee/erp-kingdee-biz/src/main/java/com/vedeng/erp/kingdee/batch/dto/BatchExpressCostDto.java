package com.vedeng.erp.kingdee.batch.dto;

import com.vedeng.erp.kingdee.batch.common.bean.BatchBaseDto;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
    * 快递成本表
    */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BatchExpressCostDto extends BatchBaseDto {
    /**
    * 主键ID（单据编号）
    */
    private Integer expressCostId;

    /**
    * 唯一键（贝登单据编号）
    */
    private String uniqueKey;

    /**
    * 快递详情ID
    */
    private Integer expressDetailId;

    /**
    * 快递主键ID
    */
    private Integer expressId;

    /**
    * 销售单号（销售单号）
    */
    private String saleorderNo;

    /**
    * 出库单号（出入库单号）
    */
    private String outInNo;

    /**
    * 承运商
    */
    private String logistics;

    /**
    * 收件地址（收件人地址）
    */
    private String takeTraderAddress;

    /**
    * 收件人（收件人）
    */
    private String takeTraderContactName;

    /**
    * 收件人电话（收件人电话）
    */
    private String takeTraderContactTelephone;

    /**
    * 物流单号（快递单号）
    */
    private String logisticsNo;

    /**
    * 数据类型（ 0实物普发，1实物直发，2纯虚拟商品销售订单，3混虚拟商品销售订单）
    */
    private Integer dataType;

    /**
    * 订货号（物料编码）
    */
    private String sku;

    /**
    * 批次号（批次号）
    */
    private String batchNumber;

    /**
    * SN码（物料序列号）
    */
    private String barcodeFactory;

    /**
    * 分摊数量（发货数量）
    */
    private Integer shareNum;

    /**
    * 公允价
    */
    private BigDecimal fairPrice;

    /**
    * 销售商品ID
    */
    private Integer saleorderGoodsId;

    /**
    * 是否赠品 0否1是
    */
    private Integer isGift;

    /**
    * 快递费总额
    */
    private BigDecimal totalExpressAmount;

    /**
    * 分摊快递费
    */
    private BigDecimal shareExpressAmount;

    /**
    * 分摊快递费(不含税)（快递成本）
    */
    private BigDecimal shareExpressAmountNoTax;

    /**
    * 成本计算标记（1新增，2更新，4删除）
     * @see  com.vedeng.erp.kingdee.batch.common.enums.CostActEnum
    */
    private Integer costActFlag;

    /**
    * 计算时间
    */
    private String etlTime;

    /**
    * 推送状态（0未推送，1已推送）
    */
    private Integer pushStatus;

    /**
    * 是否删除 0否 1是
    */
    private Integer isDelete;

    /**
    * 更新备注
    */
    private String updateRemark;

    // 额外字段
    /**
     * 成本计算标记（1新增，2更新，4删除）
     */
    private List<Integer> costActFlags;

    /**
     * 最大时间
     */
    private Date beginTime;

    /**
     * 最小时间
     */
    private Date endTime;

}