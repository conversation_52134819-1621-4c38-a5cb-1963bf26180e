package com.vedeng.erp.kingdee.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.erp.kingdee.domain.command.KingDeePurchaseVatSpecialInvoiceCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeePurchaseVatSpecialInvoiceEntity;
import com.vedeng.erp.kingdee.repository.mappers.KingDeePurchaseVatSpecialInvoiceMapper;
import com.vedeng.erp.kingdee.mapstruct.*;
import com.vedeng.erp.kingdee.service.KingDeePVSInvoiceApiService;
import com.vedeng.erp.kingdee.service.KingDeePVSInvoiceService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.RepoStatus;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.OperateExtCommand;
import com.vedeng.infrastructure.kingdee.domain.command.UpdateExtCommand;
import com.vedeng.infrastructure.kingdee.service.impl.KingDeeMqBaseServiceImpl;
import com.vedeng.erp.kingdee.dto.PurchaseVatSpecialInvoiceDto;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;

/**
 * @description: 采购增值税专用发票类
 * @return:
 * @author: Suqin
 * @date: 2022/12/2 17:21
 **/
@Service
@Slf4j
public class KingDeePVSInvoiceServiceImpl extends KingDeeMqBaseServiceImpl<PurchaseVatSpecialInvoiceDto> implements KingDeePVSInvoiceService, KingDeePVSInvoiceApiService {

    @Autowired
    KingDeePurchaseVatSpecialInvoiceMapper kingDeePurchaseVatSpecialInvoiceMapper;

    @Autowired
    KingDeePurchaseVatSpecialInvoiceCommandConvertor kingDeePurchaseVatSpecialInvoiceCommandConvertor;

    @Autowired
    KingDeePurchaseVatSpecialInvoiceConvertor kingDeePurchaseVatSpecialInvoiceConvertor;

    private static final Logger logger = LoggerFactory.getLogger(KingDeePVSInvoiceServiceImpl.class);

    @Override
    public void update(PurchaseVatSpecialInvoiceDto purchaseVatSpecialInvoiceDto) {

        KingDeePurchaseVatSpecialInvoiceEntity entity = kingDeePurchaseVatSpecialInvoiceMapper.selectByFQzokBddjtid(purchaseVatSpecialInvoiceDto.getFQzokBddjtid());
        if (entity == null) {
            logger.info("当前客户信息尚未推送过金蝶，客户信息：{}", JSONObject.toJSONString(purchaseVatSpecialInvoiceDto));
            return;
        }

        purchaseVatSpecialInvoiceDto.setFid(entity.getFid());
        OperateExtCommand operateExtCommand = new OperateExtCommand(purchaseVatSpecialInvoiceDto.getFormId(), purchaseVatSpecialInvoiceDto.getFid(),
                KingDeeConstant.ORG_ID.toString(), null);
        ArrayList<SuccessEntity> successUnAudit = kingDeeBaseApi.unAudit(operateExtCommand);

        if (CollUtil.isNotEmpty(successUnAudit)) {
            logger.info("反审核成功:{}",successUnAudit);
        }
        KingDeePurchaseVatSpecialInvoiceCommand command = kingDeePurchaseVatSpecialInvoiceCommandConvertor.toCommand(purchaseVatSpecialInvoiceDto);
        command.setFID(entity.getFid());
        command.setFDATE(entity.getFdate());
        RepoStatus update = kingDeeBaseApi.update(new UpdateExtCommand<>(command, purchaseVatSpecialInvoiceDto.getFormId()));
        ArrayList<SuccessEntity> successEntities = update.getSuccessEntitys();
        if (CollUtil.isNotEmpty(successEntities)) {
            KingDeePurchaseVatSpecialInvoiceEntity invoiceEntity = kingDeePurchaseVatSpecialInvoiceConvertor.toEntity(purchaseVatSpecialInvoiceDto);
            invoiceEntity.setPurchaseVatSpecialInvoiceId(entity.getPurchaseVatSpecialInvoiceId());
            kingDeePurchaseVatSpecialInvoiceMapper.updateByPrimaryKeySelective(invoiceEntity);
        }


        logger.info("修改采购增值税专用发票时间推送金蝶：{}", JSON.toJSONString(purchaseVatSpecialInvoiceDto));
    }
}
