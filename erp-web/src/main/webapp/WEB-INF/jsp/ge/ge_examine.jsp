<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="GE商机审核页" scope="application" />
<%@ include file="../common/common.jsp"%>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/ge/ge_examine.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src='<%= basePath %>static/js/region/index.js?rnd=${resourceVersionKey}'></script>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<style>
    .title-container-orange {
        background: #ffce7b;
    }
</style>
<body>
<div class="parts">


    <div class="title-container title-container-blue">
        <div class="table-title nobor">基本信息</div>
    </div>
    <table class="table table-bordered table-striped table-condensed table-centered">
        <tbody>
        <tr>
            <td class="table-smaller">报价单号</td>
            <td>
                <c:choose>
                    <c:when test="${geExamineBasicDto.quoteorderNo =='' || geExamineBasicDto.quoteorderNo==null}">
                        -
                    </c:when>

                    <c:otherwise>
                        ${geExamineBasicDto.quoteorderNo}
                    </c:otherwise>
                </c:choose>
            </td>


            <td>报单日期</td>
            <td>
                <c:choose>
                    <c:when test="${geExamineBasicDto.addTime =='' || geExamineBasicDto.addTime==null}">
                        -
                    </c:when>

                    <c:otherwise>
                        <fmt:formatDate value="${geExamineBasicDto.addTime}" pattern="yyyy-MM-dd HH:mm:ss" />
                    </c:otherwise>
                </c:choose>

            </td>
        </tr>

        <tr>
            <td>报单状态</td>
            <td>
                <c:choose>
                    <c:when test="${geExamineBasicDto.status eq 0}">待审核</c:when>
                    <c:when test="${geExamineBasicDto.status eq 1}">可跟进</c:when>
                    <c:when test="${geExamineBasicDto.status eq 2}">不可跟进</c:when>
                    <c:otherwise>
                        -
                    </c:otherwise>
                </c:choose>
            </td>

            <td>商机状态</td>
            <td>
                <c:choose>
                    <c:when test="${geExamineBasicDto.businessChanceStatus eq 1}">跟进中</c:when>
                    <c:when test="${geExamineBasicDto.businessChanceStatus eq 2}">赢单</c:when>
                    <c:when test="${geExamineBasicDto.businessChanceStatus eq 3}">失单</c:when>
                    <c:otherwise>
                        -
                    </c:otherwise>
                </c:choose>
            </td>
        </tr>

        <tr>
            <td>终端医院名称</td>
            <td>
                <c:choose>
                    <c:when test="${geExamineBasicDto.terminalTraderName =='' || geExamineBasicDto.terminalTraderName==null}">
                        -
                    </c:when>
                    <c:otherwise>
                        ${geExamineBasicDto.terminalTraderName}
                    </c:otherwise>
                </c:choose>
            </td>

            <td>医院性质</td>
            <td>
                <c:choose>
                    <c:when test="${geExamineBasicDto.hospitalType eq 1}">公立</c:when>
                    <c:when test="${geExamineBasicDto.hospitalType eq 2}">非公</c:when>
                </c:choose>
            </td>
        </tr>

        <tr>
            <td>意向型号</td>
            <td>
                <c:choose>
                    <c:when test="${geExamineBasicDto.goodsName =='' || geExamineBasicDto.goodsName==null}">
                        -
                    </c:when>

                    <c:otherwise>
                        ${geExamineBasicDto.goodsName}
                    </c:otherwise>
                </c:choose>

            </td>

            <td>所属地区-详细地址</td>
            <td>
                ${geExamineBasicDto.salesArea}-${geExamineBasicDto.address}
            </td>
        </tr>

        <tr>
            <td>商机来源</td>
            <td>
                <c:choose>
                    <c:when test=" ${geExamineBasicDto.title == '' || geExamineBasicDto.title ==null }">
                        -
                    </c:when>

                    <c:otherwise>
                        ${geExamineBasicDto.title}
                    </c:otherwise>
                </c:choose>

            </td>

            <td>经销商名称</td>
            <td>
                <c:choose>
                    <c:when test="${geExamineBasicDto.traderName =='' || geExamineBasicDto.traderName==null}">
                        -
                    </c:when>

                    <c:otherwise>
                        ${geExamineBasicDto.traderName}
                    </c:otherwise>
                </c:choose>

            </td>
        </tr>

        </tbody>
    </table>
</div>

<form action="${pageContext.request.contextPath}/businesschance/ge/saveGeExamine.do" method="post" id="editForm">

    <div>
        <input type="hidden" name="accountAreaId" id="accountAreaId" class="terminal" value="${geExamineFeedBackDto.accountAreaId}"/>
        <input type="hidden" name="geBussinessChanceId" id="geBussinessChanceId" class="terminal" value="${geExamineBasicDto.geBussinessChanceId}"/>
        <input type="hidden" name="oldStatus" id="oldStatus" class="terminal" value="${geExamineFeedBackDto.status}"/>

    </div>



    <div class="parts" id="geExamine">
        <div class="title-container title-container-orange">
            <div class="table-title nobor">
                GE反馈
            </div>
        </div>

        <c:forEach var="error" items="${geExamineFeedBackDto.errors}" varStatus="status">
        <div class="vd-tip tip-red">
            <i class="vd-tip-icon vd-icon icon-error2"></i>
            <div class="vd-tip-cnt">${error}</div>
        </div>
        </c:forEach>

        <div style="margin-top: 20px">
            <ul class="payplan">
                <li>
                    <div class="infor_name infor_name120">
                        <span>*</span>
                        <label>报单状态:</label>
                    </div>

                    <div class="f_left inputfloat">
                        <select class="input-middle" name="status" id="status" >
                            <option value="">请选择</option>
                            <option value="1"
                                    <c:if test="${geExamineFeedBackDto.status == 1}">selected="selected"</c:if>>可跟进</option>
                            <option value="2"
                                    <c:if test="${geExamineFeedBackDto.status == 2}">selected="selected"</c:if>>不可跟进</option>
                        </select>

                        <div class="f_left inputfloat">
                            <input type="text" name="content" id="content" value="${geExamineFeedBackDto.content}" placeholder="请输入" class="input-xx" maxLength=100/>
                        </div>

                    </div>
                </li>

                <li>
                    <div class="infor_name infor_name120">
                        <span>*</span>
                        <label>是否有account:</label>
                    </div>

                    <div class="f_left inputfloat">
                        <select class="input-middle" name="isHavingAccount" id="isHavingAccount" onchange="setMust1()">
                            <option value="">请选择</option>
                            <option value="0"
                                    <c:if test="${geExamineFeedBackDto.isHavingAccount == 0}">selected="selected"</c:if>>无</option>
                            <option value="1"
                                    <c:if test="${geExamineFeedBackDto.isHavingAccount == 1}">selected="selected"</c:if>>有</option>
                        </select>

                    </div>
                </li>

                <li>
                    <div class="infor_name infor_name120">
                        <span id="accountMust1" style="display: none">*</span>
                        <label>account名:</label>
                    </div>

                    <div class="f_left inputfloat">
                        <input type="text" name="accountName" id="accountName" value="${geExamineFeedBackDto.accountName}" placeholder="请输入" class="input-xx" maxLength=100/>
                    </div>
                </li>

                <li>
                    <div class="infor_name infor_name120">
                        <span id="accountMust2" style="display: none">*</span>
                        <label>account地址:</label>
                    </div>
                    <div class="f_left">
                        <c:choose>
                            <c:when test="${empty geExamineFeedBackDto.accountAreaId}">
                                <select class="input-small f_left mr10" name="province" id="province">
                                    <option value="0">请选择</option>
                                    <c:if test="${not empty provinceList }">
                                        <c:forEach items="${provinceList }" var="prov">
                                            <option value="${prov.regionId }" <c:if test="${province eq prov.regionId }">selected="selected"</c:if>>${prov.regionName }</option>
                                        </c:forEach>
                                    </c:if>
                                </select>
                                <select class="input-small f_left mr10" name="city" id="city">
                                    <option value="0">请选择</option>
                                </select>
                                <select class="input-small f_left" name="zone" id="zone">
                                    <option value="0">请选择</option>
                                </select>
                            </c:when>
                            <c:otherwise>
                                <select class="input-small f_left mr10" name="province" id="province">
                                    <option value="0">请选择</option>
                                    <c:if test="${not empty provinceList }">
                                        <c:forEach items="${provinceList }" var="province">
                                            <option value="${province.regionId }"
                                                    <c:if test="${ not empty provinceRegion &&  province.regionId == provinceRegion.regionId }">selected="selected"</c:if>>${province.regionName }</option>
                                        </c:forEach>
                                    </c:if>
                                </select>
                                <select class="input-small f_left mr10" name="city" id="city">
                                    <option value="0">请选择</option>
                                    <c:if test="${not empty cityList }">
                                        <c:forEach items="${cityList }" var="city">
                                            <option value="${city.regionId }"
                                                    <c:if test="${ not empty cityRegion &&  city.regionId == cityRegion.regionId }">selected="selected"</c:if>>${city.regionName }</option>
                                        </c:forEach>
                                    </c:if>
                                </select>
                                <select class="input-small f_left" name="zone" id="zone">
                                    <option value="0">请选择</option>
                                    <c:if test="${not empty zoneList }">
                                        <c:forEach items="${zoneList }" var="zone">
                                            <option value="${zone.regionId }"
                                                    <c:if test="${ not empty zoneRegion &&  zone.regionId == zoneRegion.regionId }">selected="selected"</c:if>>${zone.regionName }</option>
                                        </c:forEach>
                                    </c:if>
                                </select>
                            </c:otherwise>
                        </c:choose>
                        <div id="sales_area_msg_div" style="clear:both"></div>
                        <div class="" style="margin-top: 10px" >
                            <input type="text" name="accountAddress" id="accountAddress" value="${geExamineFeedBackDto.accountAddress}" placeholder="请输入具体地址" class="input-xx" maxLength=100/>
                        </div>

                        <input type="hidden" name="accountArea" id="accountArea">
                    </div>

                </li>

                <li>
                    <div class="infor_name infor_name120">
                        <span>*</span>
                        <label>是否有MPC:</label>
                    </div>

                    <div class="f_left inputfloat">
                        <select class="input-middle" name="isHavingMpc" id="isHavingMpc" onchange="setMust2()">
                            <option value="">请选择</option>
                            <option value="0"
                                    <c:if test="${geExamineFeedBackDto.isHavingMpc == 0}">selected="selected"</c:if>>无</option>
                            <option value="1"
                                    <c:if test="${geExamineFeedBackDto.isHavingMpc == 1}">selected="selected"</c:if>>有</option>
                        </select>

                    </div>
                </li>

                <li>
                    <div class="infor_name infor_name120">
                        <span id="mpcMust1" style="display: none">*</span>
                        <label>MPC详情:</label>
                    </div>

                    <div class="f_left inputfloat">
                        <input type="text" name="mpcDetail" id="mpcDetail" value="${geExamineFeedBackDto.mpcDetail}" placeholder="请输入" class="input-xx" maxLength=100 />
                    </div>
                </li>
            </ul>
        </div>


        <tags:ge_business_chance_logs geActionLogs="${geActionLogs}" />

        <div class="add-tijiao">
            <button type="button" class="bt-bg-style bg-deep-green" onclick="editSubmit(${orderType});">确定</button>
        </div>
</form>

</div>
</body>
