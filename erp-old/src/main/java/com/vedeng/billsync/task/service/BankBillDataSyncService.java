package com.vedeng.billsync.task.service;


import com.vedeng.common.model.ResultInfo;

import java.util.Date;

public interface BankBillDataSyncService {
    /**
     * 将微信和支付宝流水的信息插入银行流水表
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/12/16 16:33.
     * @author: <PERSON><PERSON>.
     * @param syncDate
     * @param tag
     * @return: com.vedeng.common.model.ResultInfo.
     * @throws:  .
     */

    ResultInfo syncBillDate2bankBill(Date syncDate, String type, Integer tag);

    /**
     * 将微信和支付宝流水的信息插入银行流水表  历史记录处理
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2023/8/23 16:33.
     * @author: <PERSON><PERSON>.
     * @param syncDate
     * @param tag
     * @return: com.vedeng.common.model.ResultInfo.
     * @throws:  .
     */

    ResultInfo syncBillDate2bankBillHistory(Date syncDate, Integer tag,String type);
}
