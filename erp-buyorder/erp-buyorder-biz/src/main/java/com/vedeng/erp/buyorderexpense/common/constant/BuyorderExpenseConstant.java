package com.vedeng.erp.buyorderexpense.common.constant;

/**
 * <AUTHOR>
 * @description 采购费用单常量类
 * @date 2022/8/23 11:28
 **/
public class BuyorderExpenseConstant {

    /**
     * 直属
     */
    public static final Integer ORDER_TYPE_ZERO = 0;

    /**
     * 付款类型
     */
    public static final Integer PAYMENT_TYPE_419 = 419;

    /**
     * 付款类型
     */
    public static final Integer PAYMENT_TYPE_424 = 424;

    /**
     * 采购费用单申请付款
     */
    public static final Integer PAYMENT_TYPE_4125 = 4125;

    public static final String PLAY_ONE = "第一期";

    public static final String PLAY_TWO = "第二期";

    public static final String PLAY_THREE = "第三期";

    public static final String PLAY_CONTENT_PREPAID = "预付款";

    public static final String PLAY_CONTENT_PERIOD = "帐期付款";

    public static final String PLAY_CONTENT_FINAL = "尾款";

    /**
     * 费用单类型 1采购单生效后创建
     */
    public static final Integer ORDER_TYPE_VALID = 1;

    public static final String VEDENG = "南京贝登医疗股份有限公司";

    public static final Integer VEDENG_ID = 1;
    /**
     * 收票状态 部分
     */
    public static final Integer INVOICE_STATUS_PART = 1;

    /**
     * 收票状态 未收票
     */
    public static final Integer INVOICE_STATUS_NONE = 0;

    /**
     * 收票状态 全部
     */
    public static final Integer INVOICE_STATUS_ALL = 2;

    /**
     * 费用单售后状态 进行中
     */
    public static final Integer SERVICE_STATUS_PROCESS = 1;

    /**
     * 费用单发货状态 全部发货
     */
    public static final Integer DELIVERY_STATUS_ALL = 2;

    /**
     * 费用单收货状态 全部收货
     */
    public static final Integer ARRIVAL_STATUS_ALL = 2;


    /**
     * 审核状态 待审核
     */
    public static final Integer AUDIT_STATUS_WAIT = 0;
    /**
     * 审核状态 审核中
     */
    public static final Integer AUDIT_STATUS_PROCESS = 1;
    /**
     * 审核状态 通过
     */
    public static final Integer AUDIT_STATUS_SUCCESS = 2;
    /**
     * 审核状态 不通过
     */
    public static final Integer AUDIT_STATUS_ERROR = 3;

    /**
     * 锁定
     */
    public static final Integer LOCKED_STATUS_ON = 1;

    /**
     * 未锁定
     */
    public static final Integer LOCKED_STATUS_UN = 0;

    /**
     * 订单状态 待确认
     */
    public static final Integer STATUS_WAIT = 0;

    /**
     * 订单状态 进行中
     */
    public static final Integer STATUS_PROCESS = 1;

    /**
     * 订单状态 完结
     */
    public static final Integer STATUS_COMPLETE = 2;

    /**
     * 订单状态 关闭
     */
    public static final Integer STATUS_CLOSE = 3;

    /**
     * 删除状态 1
     */
    public static final Integer IS_DELETED = 1;

    /**
     * 付款状态 未付款
     */
    public static final Integer PAYMENT_STATUS_NO = 0;
    /**
     * 付款状态 部分
     */
    public static final Integer PAYMENT_STATUS_PART = 1;
    /**
     * 付款状态 全部
     */
    public static final Integer PAYMENT_STATUS_ALL = 2;

    /**
     * 生效
     */
    public static final Integer VALID_STATUS = 1;

    /**
     * 默认创建者ID
     */
    public static final Integer NJADMIN_ID = 2;

    /**
     * 默认创建者名称
     */
    public static final String NJADMIN_NAME = "njadmin";

    /**
     * 供应链管理部供应管理组 ORG_ID
     */
    public static final Integer SUPPLY_CHAIN_MANAGEMENT = 66;

    /**
     * 退货预警
     */
    public static final Integer RETURN_EARLY_WARN = 1;

    /**
     * 无退货预警
     */
    public static final Integer NO_RETURN_EARLY_WARN = 0;

    /**
     * 费用订单——订单说明
     * com.vedeng.erp.buyorder.dto.OrderRemarkDto#type
     */
    public static final Integer ORDER_REMARK_TYPE_EXPENSE = 1;

    /**
     * 费用售后订单——订单说明
     * com.vedeng.erp.buyorder.dto.OrderRemarkDto#type
     */
    public static final Integer ORDER_REMARK_TYPE_EXPENSE_AFTER = 2;


    /**
     * 自动转单备注
     */
    public static final String PAY_COMMENT = "自动转单，系统生成";



}
