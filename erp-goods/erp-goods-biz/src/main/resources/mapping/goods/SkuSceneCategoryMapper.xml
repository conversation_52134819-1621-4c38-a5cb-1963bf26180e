<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.mapper.SkuSceneCategoryMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.goods.domain.entity.SkuSceneCategoryEntity">
    <!--@mbg.generated-->
    <!--@Table T_SKU_SCENE_CATEGORY-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="SCENE_ID" jdbcType="BIGINT" property="sceneId" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="DESCRIPTION" jdbcType="LONGVARCHAR" property="description" />
    <result column="SORT" jdbcType="INTEGER" property="sort" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="SKU_NOS" jdbcType="VARCHAR" property="skuNos" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, SCENE_ID, `NAME`, DESCRIPTION, SORT, IS_DELETE, SKU_NOS, CREATOR, UPDATER, CREATOR_NAME, 
    UPDATER_NAME, ADD_TIME, MOD_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_SKU_SCENE_CATEGORY
    where ID = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_SKU_SCENE_CATEGORY
    where ID = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.goods.domain.entity.SkuSceneCategoryEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SKU_SCENE_CATEGORY (SCENE_ID, `NAME`, DESCRIPTION, 
      SORT, IS_DELETE, SKU_NOS, 
      CREATOR, UPDATER, CREATOR_NAME, 
      UPDATER_NAME, ADD_TIME, MOD_TIME
      )
    values (#{sceneId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{description,jdbcType=LONGVARCHAR}, 
      #{sort,jdbcType=INTEGER}, #{isDelete,jdbcType=INTEGER}, #{skuNos,jdbcType=VARCHAR}, 
      #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, 
      #{updaterName,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.goods.domain.entity.SkuSceneCategoryEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SKU_SCENE_CATEGORY
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sceneId != null">
        SCENE_ID,
      </if>
      <if test="name != null and name != ''">
        `NAME`,
      </if>
      <if test="description != null and description != ''">
        DESCRIPTION,
      </if>
      <if test="sort != null">
        SORT,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="skuNos != null and skuNos != ''">
        SKU_NOS,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="sceneId != null">
        #{sceneId,jdbcType=BIGINT},
      </if>
      <if test="name != null and name != ''">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null and description != ''">
        #{description,jdbcType=LONGVARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="skuNos != null and skuNos != ''">
        #{skuNos,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.domain.entity.SkuSceneCategoryEntity">
    <!--@mbg.generated-->
    update T_SKU_SCENE_CATEGORY
    <set>
      <if test="sceneId != null">
        SCENE_ID = #{sceneId,jdbcType=BIGINT},
      </if>
      <if test="name != null and name != ''">
        `NAME` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null and description != ''">
        DESCRIPTION = #{description,jdbcType=LONGVARCHAR},
      </if>
      <if test="sort != null">
        SORT = #{sort,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="skuNos != null and skuNos != ''">
        SKU_NOS = #{skuNos,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.domain.entity.SkuSceneCategoryEntity">
    <!--@mbg.generated-->
    update T_SKU_SCENE_CATEGORY
    set SCENE_ID = #{sceneId,jdbcType=BIGINT},
      `NAME` = #{name,jdbcType=VARCHAR},
      DESCRIPTION = #{description,jdbcType=LONGVARCHAR},
      SORT = #{sort,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      SKU_NOS = #{skuNos,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_SKU_SCENE_CATEGORY
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="SCENE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.sceneId != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.sceneId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="`NAME` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.name != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.name,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="DESCRIPTION = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.description != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.description,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SORT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.sort != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.sort,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDelete != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.isDelete,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="SKU_NOS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.skuNos != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.skuNos,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
    </trim>
    where ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="ID" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SKU_SCENE_CATEGORY
    (SCENE_ID, `NAME`, DESCRIPTION, SORT, IS_DELETE, SKU_NOS, CREATOR, UPDATER, CREATOR_NAME, 
      UPDATER_NAME, ADD_TIME, MOD_TIME)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.sceneId,jdbcType=BIGINT}, #{item.name,jdbcType=VARCHAR}, #{item.description,jdbcType=LONGVARCHAR}, 
        #{item.sort,jdbcType=INTEGER}, #{item.isDelete,jdbcType=INTEGER}, #{item.skuNos,jdbcType=VARCHAR}, 
        #{item.creator,jdbcType=INTEGER}, #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, 
        #{item.updaterName,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>

    <!--auto generated by MybatisCodeHelper on 2024-12-04-->
    <select id="findBySceneIdAndIsDelete" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from T_SKU_SCENE_CATEGORY
        where SCENE_ID = #{sceneId,jdbcType=BIGINT}
          and IS_DELETE = 0
        order by SORT
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-12-04-->
    <update id="updateIsDeleteById">
        update T_SKU_SCENE_CATEGORY
        set IS_DELETE=1
        where ID = #{id,jdbcType=BIGINT}
    </update>
</mapper>