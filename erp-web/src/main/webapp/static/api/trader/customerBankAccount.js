function getCustomerBankAccountList(data){
    return axios({
        url: '/customerBankAccountApi/list.do',
        method: 'post',
        data:data,
        headers: {
            'Content-Type': 'application/json'  //如果写成contentType会报错,如果不写这条也报错
        }
    })
}

function del(data) {
    return axios({
        url: '/customerBankAccountApi/delete.do',
        method: 'post',
        data: data,
        headers: {
            'Content-Type': 'application/json'  //如果写成contentType会报错,如果不写这条也报错
        }
    })
}

function updateCustomerBankAccount(data) {
    return axios({
        url: '/customerBankAccountApi/update.do',
        method: 'post',
        data: data,
        headers: {
            'Content-Type': 'application/json'  //如果写成contentType会报错,如果不写这条也报错
        }
    })
}

function download(data) {
    return axios({
        url: '/customerBankAccountApi/download.do',
        method: 'post',
        data: data,
        responseType: 'blob',
        headers: {
            'Content-Type': 'application/json'  //如果写成contentType会报错,如果不写这条也报错
        }
    })
}