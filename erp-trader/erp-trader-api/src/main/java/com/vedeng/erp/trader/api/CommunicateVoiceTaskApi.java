package com.vedeng.erp.trader.api;

import com.vedeng.erp.trader.dto.CommunicateVoiceAiLogApiDto;
import com.vedeng.erp.trader.dto.CommunicateVoiceTaskDto;
import com.vedeng.erp.trader.dto.VoiceFieldResultDto;

import java.util.List;

public interface CommunicateVoiceTaskApi {
    CommunicateVoiceTaskDto selectByCommunicateRecordId(Integer communicateRecordId);

    CommunicateVoiceTaskDto selectByCommunicateRecordIdAndSence(Integer communicateRecordId,String sence);

    /**
     * 保存语音请求日志
     * @param communicateVoiceAiLogApiDto
     */
    Long saveCommunicateVoiceAiLog(CommunicateVoiceAiLogApiDto communicateVoiceAiLogApiDto);

    /**
     * 保存AI结果
     * @param logId
     * @param content
     */
    void updateVoiceAiLog(Integer logId, String content);

    List<VoiceFieldResultDto> selectVoiceResultByCommunicateRecordIdAndSence(Integer communicateRecordId, String senceCode);

    List<VoiceFieldResultDto> selectVoiceResultByCommunicateRecordIdAndSenceGroup(Integer communicateRecordId,String senceCode,String groupCode);

    CommunicateVoiceAiLogApiDto queryLastComunicateVoiceLog(Integer communicateRecordId,String senceCode,String groupCode);
}
