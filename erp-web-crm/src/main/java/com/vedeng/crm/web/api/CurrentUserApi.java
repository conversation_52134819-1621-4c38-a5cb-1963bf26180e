package com.vedeng.crm.web.api;

import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 当前登录用户
 * @date 2024/7/19 10:10
 */
@ExceptionController
@RestController
@RequestMapping("/crm/currentUser/profile")
@Slf4j
public class CurrentUserApi {


    /**
     * 获取当前登录用户
     */
    @RequestMapping(value = "/get", method = RequestMethod.GET)
    public R<?> get() {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        return R.success(currentUser);
    }
}
