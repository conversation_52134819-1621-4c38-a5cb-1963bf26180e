package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.annotation.BusinessID;
import com.vedeng.infrastructure.kingdee.annotation.KingDeeID;
import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import lombok.*;

import java.util.List;

/**
 * 盈亏单  dto  https://www.yuque.com/manhuo/gf1570/gen125
 * <AUTHOR>
 * @version 1.0
 * @description: 预处理金蝶盈亏单 dto  由erp实际业务转换
 * @date
 */
@Getter
@Setter
@WriteBackField(needBackField = {"FID"})
public class KingDeeProfitLossDto extends KingDeeMqBaseDto {


    /**
     * id
     */
    private Integer id;

    /**
     * 单据内码0：表示新增 非0：云星空系统单据FID值，表示修改
     */
    @WriteBackField
    @KingDeeID
    private String fId;
    /**
     * 单据类型填单据类型编码，默认：PK01_SYS
     */
    private String fBillTypeId;
    /**
     * fBillNo
     */
    @BusinessID
    private String fBillNo;
    /**
     * 库存组织写组织编码
     */
    private String fStockOrgId;
    /**
     * 贝登单据头ID
     */
    private String fQzokBddjtId;
    /**
     * 货主类型
     */
    private String fOwnerTypeIdHead;
    /**
     * 单据日期
     */
    private String fDate;
    /**
     * fDeptId
     */
    private String fDeptId;
    /**
     * fBillEntry
     */
    private List<KingDeeProfitLossDetailDto> fBillEntry;


    public KingDeeProfitLossDto() {
        this.fBillTypeId = "PK01_SYS";
        this.fStockOrgId =  KingDeeConstant.ORG_ID.toString();
        this.fOwnerTypeIdHead = "BD_OwnerOrg";
        this.fDeptId = "BM9999";
    }


    @Override
    public String getFormId() {
        return KingDeeFormConstant.STK_STOCK_COUNT_LOSS;
    }
}
