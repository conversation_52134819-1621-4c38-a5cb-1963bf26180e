package com.vedeng.erp.kingdee.mapstruct;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.kingdee.domain.entity.KingDeePurchaseBackEntity;
import com.vedeng.erp.kingdee.dto.KingDeePurchaseBackDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeePurchaseBackDto;
import org.mapstruct.*;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购退料单 entity 、dto 互转
 * @date
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface KingDeePurchaseBackConvertor extends BaseMapStruct<KingDeePurchaseBackEntity, KingDeePurchaseBackDto> {
	
	/**
     * DTO转Entity
     *
     * @param dto
     * @return
     */
    @Mapping(target = "fpurmrbentry", source = "fpurmrbentry", qualifiedByName = "expensesToJsonArray")
    @Override
    KingDeePurchaseBackEntity toEntity(KingDeePurchaseBackDto dto);

    /**
     * Entity转DTO
     *
     * @param entity
     * @return
     */
    @Mapping(target = "fpurmrbentry", source = "fpurmrbentry", qualifiedByName = "expensesJsonArrayToList")
    @Override
    KingDeePurchaseBackDto toDto(KingDeePurchaseBackEntity entity);
	
    
    /**
     * entity 中JSONArray 转 原对象
     *
     * @param payBillEntryDtos JSONArray
     * @return List<KingDeePayBillDto> dto中的对象
     */
    @Named("expensesJsonArrayToList")
    default List<KingDeePurchaseBackDetailDto> entryJsonArrayToList(JSONArray payBillEntryDtos) {
        if (CollUtil.isEmpty(payBillEntryDtos)) {
            return Collections.emptyList();
        }
        return payBillEntryDtos.toJavaList(KingDeePurchaseBackDetailDto.class);
    }

    /**
     * dto 原对象中 转 JSONArray
     *
     * @param source 对象
     * @return JSONArray JSONArray
     */
    @Named("expensesToJsonArray")
    default JSONArray entryListToJsonArray(List<KingDeePurchaseBackDetailDto> source) {
        if (CollUtil.isEmpty(source)) {
            return null;
        }
        return JSONArray.parseArray(JSON.toJSONString(source));
    }
    
}
