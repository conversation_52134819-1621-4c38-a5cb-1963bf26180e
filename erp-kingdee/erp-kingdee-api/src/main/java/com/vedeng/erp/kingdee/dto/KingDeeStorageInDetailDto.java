package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import lombok.*;

import java.math.BigDecimal;

/**
 * 其他入库   https://www.yuque.com/manhuo/gf1570/uilh7d
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @see KingDeeStorageInDto
 */
@Getter
@Setter
public class KingDeeStorageInDetailDto {

    /**
     * 金蝶行id
     */
    @WriteBackField(value = "FEntryID",backWriteByErpField = "fQzokBddjhId",backWriteToKingDeeField = "F_QZOK_BDDJHID")
    private String fEntryId;

    /**
     * 实收数量
     */
    private String fQty;
    /**
     * 成本单价 填写成本价（不含税）
     */
    private String fPrice;
    /**
     * 成本金额 填写成本（不含税）
     */
    private String fAmount;
    /**
     * 原始订单号
     */
    private String fQzokYsddh;
    /**
     * 归属业务单号
     */
    private String fQzokGsywdh;
    /**
     * 业务类型
     */
    private String fQzokYwlx;
    /**
     * 批次号
     */
    private String fQzokPch;
    /**
     * 序列号
     */
    private String fQzokXlh;
    /**
     * 授权类型
     */
    private String fQzokSqlx;
    /**
     * 是否直发
     */
    private String fQzokSfzf;

    /**
     * 填写物料编码 sku
     */
    private String fMaterialId;
    /**
     * 填写仓库编码，默认值 ：CK9999
     */
    private String fStockId;

    /**
     * 贝登订单行ID
     */
    private String fQzokBddjhId;


    /**
     * 含税价
     */
    private BigDecimal ftaxprice;
    /**
     * fentrytaxrate
     */
    private BigDecimal fentrytaxrate;
}
