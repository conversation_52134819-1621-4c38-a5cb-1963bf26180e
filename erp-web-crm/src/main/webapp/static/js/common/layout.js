let windowGetQuery = (key) => {
    let params = new URLSearchParams(window.location.search);
    return params.get(key) || '';
}

// layout_hidden_value true:没有头  false:有头
let layout_hidden_value = window.parent != window;

if(windowGetQuery('parentiframe') == 1) {
    layout_hidden_value = window.parent != window.parent.parent;
}

let GLOBAL_ERP_HOST = '';

if(layout_hidden_value) {
    if(windowGetQuery('parentiframe') == 1) {
        window.parent.parent.postMessage({
            pageType: 'crm',
            name: 'host',
            requestId: 'get_host'
        }, '*');
    } else {
        window.parent.postMessage({
            pageType: 'crm',
            name: 'host',
            requestId: 'get_host'
        }, '*')
    }
}

window.addEventListener('message', function(e) {
    if(e.data && e.data.type=='response' && e.data.requestId == 'get_host') {
        GLOBAL_ERP_HOST = e.data.host;
    }
})

let GLOBAL = {
    //新窗口跳转处理，erp嵌套调用erp的方法
    link(params) {
        let crmType = this.checkCrmType().type;
        let diffSys = false; //是否是不同的系统

        if(params.crmType && params.crmType !== crmType) {
            diffSys = true;
        }

        if (layout_hidden_value && !diffSys) {

            let url = params.url;
            if (!/^http/.test(url) && !params.nohost) {
                url = window.location.origin + url;
            }

            if (params.multi) {
                if (url.indexOf('?') === -1) {
                    url += '?ttt=' + new Date().getTime();
                } else {
                    url += '&ttt=' + new Date().getTime();
                }
            }

            if(windowGetQuery('parentiframe') == 1) {
                window.parent.parent.postMessage({
                    'pageType': 'crm',
                    'from': 'lxcrm',
                    'id': url,
                    'name': params.name,
                    'url': url,
                    'closable': true
                }, '*');
            } else {
                window.parent.postMessage({
                    'pageType': 'crm',
                    'from': 'lxcrm',
                    'id': url,
                    'name': params.name,
                    'url': url,
                    'closable': true
                }, '*')
            }
            
        } else {
            if (params.link) {
                this.checkNetwork(() => {
                    window.open(params.link)
                })
            } else {
                window.open(params.url)
            }
        }
    },
    checkNetwork(callback) {
        let netType = document.querySelector('#golbal_innerSite').value;

        if (netType == 'outSite') {
            Vue.prototype.$popup.warn({
                message: '当前操作仅限在公司内网使用，可尝试链接VPN后操作。',
                buttons: [{
                    txt: '我知道了',
                    btnClass: 'confirm',
                }]
            })
        } else {
            callback && callback();
        }
    },
    // 复制
    copyTextToClipboard(text, queryThis) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text);
        } else {
            let input = document.createElement('textarea');
            input.value = text;
            document.body.appendChild(input);
            input.select();
            document.execCommand('copy');
            document.body.removeChild(input);
        }

        queryThis.$message({
            message: '复制成功',
            duration: 800
        });
    },
    //电话点击处理，erp嵌套调用erp的方法
    callNumber(params) {
        console.log(params)
        if (layout_hidden_value) {
            if(windowGetQuery('parentiframe') == 1) {
                window.parent.parent.postMessage({
                    name: 'call',
                    pageType: 'crm',
                    phone: params.phone || '',
                    traderId: params.traderId || '',
                    traderType: params.traderType || '',
                    callType: params.callType || '',
                    orderId: params.orderId || '',
                    traderContactId: params.traderContactId || ''
                }, '*');
            } else {
                window.parent.postMessage({
                    name: 'call',
                    pageType: 'crm',
                    phone: params.phone || '',
                    traderId: params.traderId || '',
                    traderType: params.traderType || '',
                    callType: params.callType || '',
                    orderId: params.orderId || '',
                    traderContactId: params.traderContactId || ''
                }, '*');
            }
        } else {
            // window.location.href = 'tel:' + params.phone;
        }
    },
    // 全局加载状态
    showGlobalLoading(font) {
        console.log('font:', font);
        let loadingEle = document.createElement('div');
        loadingEle.setAttribute('class', 'global__loading__wrap');
        loadingEle.setAttribute('id', 'J-global-loading-wrap');
        loadingEle.innerHTML = `<i class="vd-ui_icon icon-loading"></i>`;
        if (font) {
            let fontEle = document.createElement('p');
            fontEle.setAttribute('class', 'global__loading__p');
            fontEle.innerText = font;
            loadingEle.appendChild(fontEle);
        }
        document.body.appendChild(loadingEle);
    },
    hideGlobalLoading() {
        var element = document.getElementById('J-global-loading-wrap');
        if (element && element.parentNode) {
            element.parentNode.removeChild(element);
        }
    },
    //获取路由参数
    getQuery(key) {
        let params = new URLSearchParams(window.location.search);
        return params.get(key) || '';
    },
    defaultAvatar: '/static/image/crm-user-avatar.svg',
    avatarerror(e) {
        console.log(e)
    },
    BUSINESSLEADS_STATUS: {
        0: "待分配",
        1: "待跟进",
        2: "跟进中",
        3: "已关闭",
        4: "已商机"
    },
    BUSINESSCHANCE_STAGE: {
        1: "初步洽谈",
        2: "商机验证",
        3: "初步方案",
        4: "最终方案",
        5: "赢单",
        6: "关闭"
    },
    auth(key) {
        if (!key) {
            return false;
        }

        let auths = document.querySelector('#golbal_permissions') ? document.querySelector('#golbal_permissions').value : '';
        let authList = auths.split(',');

        if (authList.indexOf(key) !== -1) {
            return true;
        } else {
            return false;
        }
    },
    showNoAuth() {
        if (Vue) {
            Vue.prototype.$popup.warn({
                title: '抱歉，当前您没有访问权限',
                message: '权限开通：请企业微信联系研发部Aadi。',
                buttons: [{
                    txt: '我知道了',
                    btnClass: 'confirm',
                }]
            })
        }
    },
    download(url, name) {
        axios.get(url, {
            responseType: 'blob',
        }).then(res => {
            let blob = new Blob([res.data]);
            let objectUrl = URL.createObjectURL(blob) // 创建URL

            let link = document.createElement('a');
            link.href = objectUrl;
            link.download = name;
            document.body.appendChild(link)

            let event = new MouseEvent('click')
            link.dispatchEvent(event)
            setTimeout(() => {
                document.body.removeChild(link)
            }, 500)
        }).catch(() => { })
    },
    ERP_HOST: document.querySelector('#erpHost').value || '',
    //校验当前crm属于哪个系统，默认贝登erp,医购优选：2
    checkCrmType() {
        let sysList = [];

        try {
            sysList = JSON.parse(document.querySelector('#companyConfig').value);
        } catch (error) {
            
        }

        let sysType = {
            type: 1,
            label: '贝登医疗'
        };

        sysList.forEach(item => {
            let regx = new RegExp(item.keyword);

            if(item.keyword && regx.test(GLOBAL_ERP_HOST)) {
                sysType = item;
            }
        })

        return sysType;
    },
    getSysName(code) {
        let sysList = [];

        try {
            sysList = JSON.parse(document.querySelector('#companyConfig').value);
        } catch (error) {
            
        }

        let name = '贝登医疗'

        sysList.forEach(item => {
            if(item.type == code) {
                name = item.label
            }
        })

        return name;
    }
};

if (layout_hidden_value) {
    document.querySelector('.page-wrap').classList.add("layout-hidden");
}

let USERINFO = {
    userId: document.querySelector('#golbal_userId') ? document.querySelector('#golbal_userId').value : '',
    userName: document.querySelector('#golbal_userName') ? document.querySelector('#golbal_userName').value : '',
    avatar: document.querySelector('#golbal_headPic') ? document.querySelector('#golbal_headPic').value : GLOBAL.defaultAvatar,
}

Vue.component('page-header', {
    template: `<div class="page-header-wrap" v-if="isShow">
        <div class="side-trigger" @click="triggerSideShow" v-if="(!side || !isShowSide) && menuShowList.length"></div>
        <a :href="menuShowList.length ? '/' : 'javascript:void(0);'" class="header-system-info" :class="{'no-link': !menuShowList.length}">
            <div class="header-system-logo"></div>
            <div class="header-system-name">灵犀客户关系管理系统</div>
        </a>
        <div class="header-user-wrap" @click.stop="isShowDrop=!isShowDrop" :class="{active: isShowDrop}">
            <div class="user-avatar">
                <img :src="USERINFO.avatar"/>
            </div>
            <div class="user-name">{{USERINFO.userName}}</div>
            <i class="vd-ui_icon icon-down"></i>
            <div class="header-user-drop-wrap">
                <a class="login-out" :href="'/logout?id=' + USERINFO.userId + '&username=' + USERINFO.userName">退出登录</a>
            </div>
        </div>
        <div class="page-side-wrap" v-if="!loading && menuShowList.length" :class="{fixed: !side || !isShowSide, 'fixed-show': isShowSideFix}">
            <div class="page-side-title">分类导航</div>
            <i class="vd-ui_icon icon-delete page-side-close" @click="triggerSideShow"></i>
            <div class="side-menu-block" v-for="(item, index) in menuShowList">
                <a class="side-menu-item" :href="item.link" :class="{active: item.active}" v-if="item.link">
                    <img :src="'/static/image/side-icon/' + item.icon + '.svg'" class="menu-icon"/>
                    <div class="menu-name">{{ item.label }}</div>
                </a>
            </div>
        </div>
        <div class="page-side-fixed-mask" @click="triggerSideShow" :class="{show: isShowSideFix}"></div>
    </div>`,
    props: {
        headerInfo: {},
        side: {
            type: Boolean,
            default: false
        },
        isShowMenu: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            isShow: !layout_hidden_value,
            menuList: [{
                link: '/crm/businessLeads/profile/index',
                label: '线索列表',
                icon: 'menu1',
                authId: 'C01'
            }, {
                link: '/crm/businessChance/profile/index',
                label: '商机列表',
                icon: 'menu2',
                authId: 'C02'
            }, {
                link: '/crm/visitRecord/profile/index',
                label: '拜访计划',
                icon: 'menu3',
                authId: 'C04'
            }, {
                link: '/role/userRegionConfig/index',
                label: '销售关系配置',
                icon: 'menu-setting',
                authId: 'C05'
            }, {
                link: '/role/userCategoryConfig/index',
                label: '商品分类人员配置',
                icon: 'menu-setting',
                authId: 'C06'
            }],
            menuShowList: [],
            isShowSide: true,
            isShowSideFix: false,
            loading: true,
            isShowDrop: false
        }
    },
    computed: {

    },
    mounted() {
        if (!this.side) {
            document.querySelector('.page-wrap').classList.add("side-hidden");
        }

        if (this.isShowMenu) {
            this.getMenuList();
        }

        if (this.side) {
            this.checkSideType();

            window.addEventListener('resize', this.checkSideType);
        }

        document.addEventListener('click', () => {
            this.isShowDrop = false;
        });

        this.loading = false
    },
    methods: {
        getMenuList() {
            this.menuList.forEach(item => {
                    if (window.location.pathname === '/crm/businessLeads/profile/index' && GLOBAL.getQuery('from') === 'chance' && item.label === "商机列表") {
                        item.active = true;
                    } else if (window.location.pathname === item.link && GLOBAL.getQuery('from') !== 'chance') {
                        item.active = true;
                    }

                if (GLOBAL.auth(item.authId)) {
                    this.menuShowList.push(item)
                }
            });
        },
        checkSideType() {
            let winWidth = window.innerWidth;

            if (winWidth <= 1366 || !this.menuShowList.length) {
                this.isShowSide = false;
                document.querySelector('.page-wrap').classList.add("side-hidden");
            } else {
                this.isShowSide = true;
                this.isShowSideFix = false;
                document.querySelector('.page-wrap').classList.remove("side-hidden");
            }
        },
        triggerSideShow() {
            this.isShowSideFix = !this.isShowSideFix;
        }
    }
})

window.onload = () => {
    document.querySelector('#page-container').classList.add('show');
}
