package com.vedeng.erp.saleorder.strategy;

import com.vedeng.erp.saleorder.dto.OrderTerminalDto;

/**
 * 订单终端信息处理上下文
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/5 13:11
 */
public class OrderTerminalContext {

    private OrderTerminalStrategy orderTerminalStrategy;

    public void setOrderTerminalStrategy(OrderTerminalStrategy orderTerminalStrategy) {
        this.orderTerminalStrategy = orderTerminalStrategy;
    }

    public void executeStrategy(OrderTerminalDto orderTerminalDto) {
        orderTerminalStrategy.execute(orderTerminalDto);
    }
}