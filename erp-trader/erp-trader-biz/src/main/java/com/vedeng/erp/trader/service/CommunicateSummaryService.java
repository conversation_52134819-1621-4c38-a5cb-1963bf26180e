package com.vedeng.erp.trader.service;

import com.vedeng.erp.trader.domain.dto.CommunicateAiSummaryDto;

/**
 * <AUTHOR>
 * @since 2024/1/18 14:56
 */
public interface CommunicateSummaryService {

    /**
     * 根据沟communicateSummaryId查询AI语音提取结果
     *
     * @param communicateSummaryId 沟通记录id
     * @return CommunicateAiSummaryDto
     */
    CommunicateAiSummaryDto getByCommunicateSummaryId(Long communicateSummaryId);

    /**
     * 根据客户id查询最近的一条沟通记录的转写结果
     *
     * @param traderId      客户id
     * @param currentUserId currentUserId
     * @return CommunicateAiSummaryDto
     */
    CommunicateAiSummaryDto getByTraderId(Integer traderId, Integer currentUserId);

    /**
     * 保存更新AI语音提取结果（只更新至UPDATE_LOG字段，保留原始的识别结果）
     *
     * @param summaryDto CommunicateAiSummaryDto
     */
    void saveUpdate(CommunicateAiSummaryDto summaryDto);
}
