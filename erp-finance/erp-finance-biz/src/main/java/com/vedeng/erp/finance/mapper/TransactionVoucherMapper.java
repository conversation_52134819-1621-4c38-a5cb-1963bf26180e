package com.vedeng.erp.finance.mapper;

import com.vedeng.erp.finance.domain.entity.TransactionVoucherEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TransactionVoucherMapper {
    int deleteByPrimaryKey(Long transactionVoucherId);

    int insert(TransactionVoucherEntity record);

    int insertOrUpdate(TransactionVoucherEntity record);

    int insertOrUpdateSelective(TransactionVoucherEntity record);

    int insertSelective(TransactionVoucherEntity record);

    TransactionVoucherEntity selectByPrimaryKey(Long transactionVoucherId);

    int updateByPrimaryKeySelective(TransactionVoucherEntity record);

    int updateFileUrlByVoucherUrlAndVoucherNo(TransactionVoucherEntity record);

    int updateByPrimaryKey(TransactionVoucherEntity record);

    int updateBatch(List<TransactionVoucherEntity> list);

    int updateBatchSelective(List<TransactionVoucherEntity> list);

    int batchInsert(List<TransactionVoucherEntity> list);

    TransactionVoucherEntity findByBillNo(String billNo);

    TransactionVoucherEntity getByVoucherDateAndNo(@Param("voucherDate") String voucherDate, @Param("voucherNo") String voucherNo);

    String getTranFlowByKingDeePayBill(@Param("billNo") String billNo);

    String getTranFlowByKingDeeReceiveBill(@Param("billNo") String billNo);

    String getTranFlowByKingDeePayRefundBill(@Param("billNo") String billNo);

    String getTranFlowByKingDeeReceiveRefundBill(@Param("billNo") String billNo);

    List<TransactionVoucherEntity> findByRelateIdAndSource(@Param("relateId")Integer relateId,@Param("source")Integer source);
}