<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>客户资质审核-待处理</title> 
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="F3JAN4Cc640" datasource="erp-reportdatasource" fixednumber="2" append_column_url="" append_row_url="" empty_show="" firstcol> 
  <div id="appendHead"></div> 
  <div class="layui-fluid"> 
   <div class="layui-tab"> 
    <ul class="layui-tab-title" id="tab"> 
    </ul> 
   </div> 
   <form class="layui-form" id="search">
    <div class=" layui-inline "><label class="layui-form-label">送达时间</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="daterange" name="ADD_TIME" placeholder="" style="" alias="FROM_UNIXTIME(L.ADD_TIME/1000)" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">销售部门</label>
     <div class="layui-input-inline">
         <object class=" layui-input list-search-item " type="19" name="ORG_NAME" placeholder="" style="" alias="O" jdbctype="VARCHAR" data="" datatype="" oper="LIKE"></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">归属销售</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="20" name="USERNAME" placeholder="" style="" alias="" jdbctype="" data="SELECT 	USERNAME K,USERNAME V FROM  	T_USER U 	JOIN T_R_USER_POSIT UP ON U.USER_ID = UP.USER_ID  	AND U.IS_DISABLED = 0 AND U.COMPANY_ID = 1 	JOIN T_POSITION P ON UP.POSITION_ID = P.POSITION_ID  WHERE 	P.TYPE = 310 	ORDER BY USERNAME asc" datatype="KVSQLCACHE" oper=""></object> 
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">客户名称</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="TRADER_NAME" placeholder="" style="" alias="T" jdbctype="VARCHAR" data="" datatype="" oper="LIKE"></object> 
     </div>
    </div>
    <div class=" layui-inline ">
    <label class="layui-form-label">资质审核状态</label>
       <div class="layui-input-inline">
           <object class=" layui-input list-search-item " type="xm-select" name="STATUS" placeholder="" style="" alias="V" jdbctype="" data="[
           {&quot;V&quot;:&quot;待审核&quot;,&quot;K&quot;:&quot;3&quot;},
           {&quot;V&quot;:&quot;初审中&quot;,&quot;K&quot;:&quot;4&quot;},
           {&quot;V&quot;:&quot;公章审核&quot;,&quot;K&quot;:&quot;5&quot;},
           {&quot;V&quot;:&quot;复审中&quot;,&quot;K&quot;:&quot;0&quot;}]" datatype="JSON" oper="IN"></object>
       </div>
    </div>
   </form> 
   <hr class="layui-border-blue"> 
   <div class="btn-group   bd-highlight" id="tableButton"> 
   </div> 
   <table id="table" class="layui-table" style=" width:100%"> 
    <thead> 
     <tr id="column"> 
      <th type="rowbutton" id="rowbutton"></th> 
      <th item_name="TRADER_NAME" name="TRADER_NAME" body="td-link-color" order="0" url="/trader/customer/baseinfo.do?traderId=${TRADER_ID}" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">客户名称</th>
      <th item_name="CUSTOMER_TYPE" name="CUSTOMER_TYPE" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">客户类型</th>
      <th item_name="CUSTOMER_NATURE" name="CUSTOMER_NATURE" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">客户性质</th>
      <th item_name="STATUS" name="STATUS" body="td-select" order="" url="" opentype="" windowname="" datatype="JSON" data="[
      {&quot;V&quot;:&quot;待审核&quot;,&quot;K&quot;:&quot;3&quot;},
      {&quot;V&quot;:&quot;初审中&quot;,&quot;K&quot;:&quot;4&quot;},
      {&quot;V&quot;:&quot;公章审核&quot;,&quot;K&quot;:&quot;5&quot;},
      {&quot;V&quot;:&quot;复审中&quot;,&quot;K&quot;:&quot;0&quot;},
      {&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;1&quot;},
      {&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">资质审核状态</th>
      <th item_name="USERNAME" name="USERNAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">归属销售</th>
      <th item_name="ORG_NAME" name="ORG_NAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">销售部门</th>
      <th item_name="ADD_TIME" name="ADD_TIME" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">送达时间</th>
      <th item_name="OPERATE" name="OPERATE" body="td-link-color" order="0" url="/trader/customer/getFinanceAndAptitude.do?traderId=${TRADER_ID}&amp;traderCustomerId=${TRADER_CUSTOMER_ID}&amp;customerNature=${CUSTOMER_NATURE_ID}" opentype="PARENT" windowname="" datatype="" data="" style="" head="18">操作</th>
     </tr> 
    </thead> 
    <tbody> 
     <tr> 
      <td colspan="100"> express: <pre id="express" class="layui-code" orderby="order by L.ADD_TIME desc" groupby="group BY L.BUZ_ID">StringBuilder sql=new StringBuilder("SELECT
	T.TRADER_NAME,
        C.TRADER_CUSTOMER_ID,
	CASE C.CUSTOMER_TYPE WHEN 426 THEN '科研医疗' WHEN 427 THEN '临床医疗' END CUSTOMER_TYPE,
	CASE C.CUSTOMER_NATURE WHEN 465 THEN '分销' WHEN 466 THEN '终端' END CUSTOMER_NATURE,
	C.CUSTOMER_NATURE CUSTOMER_NATURE_ID,
        U.USER_ID,
	U.USERNAME,
        O.ORG_ID,
	O.ORG_NAME,
	FROM_UNIXTIME(L.ADD_TIME/1000) ADD_TIME,
        T.TRADER_ID,
        V.STATUS,
        '去审核' OPERATE
FROM
	T_TODO_LIST L
	JOIN T_TRADER_CUSTOMER C ON L.BUZ_ID = C.TRADER_CUSTOMER_ID AND L.BUZ_TYPE = 1 AND L.`STATUS` = 0
	JOIN T_TRADER T ON C.TRADER_ID= T.TRADER_ID
	JOIN T_R_TRADER_J_USER TU ON TU.TRADER_ID = T.TRADER_ID AND TU.TRADER_TYPE=1
	JOIN T_USER U ON TU.USER_ID = U.USER_ID
	LEFT JOIN T_R_USER_POSIT UP ON U.USER_ID = UP.USER_ID
	LEFT JOIN T_POSITION P ON UP.POSITION_ID = P.POSITION_ID
	LEFT JOIN T_ORGANIZATION O ON P.ORG_ID = O.ORG_ID
    LEFT JOIN T_VERIFIES_INFO V on V.RELATE_TABLE_KEY = C.TRADER_CUSTOMER_ID and V.RELATE_TABLE = 'T_CUSTOMER_APTITUDE'
	where 1=1 AND T.COMPANY_ID=1 AND  U.USERNAME NOT IN(
'alina.huang' ,'testb2b','test','testyxg','testkyg','testgyl'
        )");
list=search(sql);
return list;</pre> count: <pre id="count" class="layui-code"></pre></td> 
     </tr> 
    </tbody> 
   </table> 
  </div> 
  <div id="appendFoot"></div> 
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script> 
  <script>
    layui.use(function () {

    })
</script> 
 </body>
</html>