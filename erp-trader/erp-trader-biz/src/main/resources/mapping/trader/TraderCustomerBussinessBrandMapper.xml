<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.TraderCustomerBussinessBrandMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.TraderCustomerBussinessBrandEntity">
    <!--@mbg.generated-->
    <!--@Table T_TRADER_CUSTOMER_BUSSINESS_BRAND-->
    <id column="TRADER_CUSTOMER_BUSSINESS_BRAND_ID" jdbcType="INTEGER" property="traderCustomerBussinessBrandId" />
    <result column="TRADER_CUSTOMER_ID" jdbcType="INTEGER" property="traderCustomerId" />
    <result column="BRAND_ID" jdbcType="INTEGER" property="brandId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    TRADER_CUSTOMER_BUSSINESS_BRAND_ID, TRADER_CUSTOMER_ID, BRAND_ID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_TRADER_CUSTOMER_BUSSINESS_BRAND
    where TRADER_CUSTOMER_BUSSINESS_BRAND_ID = #{traderCustomerBussinessBrandId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_TRADER_CUSTOMER_BUSSINESS_BRAND
    where TRADER_CUSTOMER_BUSSINESS_BRAND_ID = #{traderCustomerBussinessBrandId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="TRADER_CUSTOMER_BUSSINESS_BRAND_ID" keyProperty="traderCustomerBussinessBrandId" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerBussinessBrandEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_CUSTOMER_BUSSINESS_BRAND (TRADER_CUSTOMER_ID, BRAND_ID)
    values (#{traderCustomerId,jdbcType=INTEGER}, #{brandId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="TRADER_CUSTOMER_BUSSINESS_BRAND_ID" keyProperty="traderCustomerBussinessBrandId" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerBussinessBrandEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_CUSTOMER_BUSSINESS_BRAND
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="traderCustomerId != null">
        TRADER_CUSTOMER_ID,
      </if>
      <if test="brandId != null">
        BRAND_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="traderCustomerId != null">
        #{traderCustomerId,jdbcType=INTEGER},
      </if>
      <if test="brandId != null">
        #{brandId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerBussinessBrandEntity">
    <!--@mbg.generated-->
    update T_TRADER_CUSTOMER_BUSSINESS_BRAND
    <set>
      <if test="traderCustomerId != null">
        TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER},
      </if>
      <if test="brandId != null">
        BRAND_ID = #{brandId,jdbcType=INTEGER},
      </if>
    </set>
    where TRADER_CUSTOMER_BUSSINESS_BRAND_ID = #{traderCustomerBussinessBrandId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerBussinessBrandEntity">
    <!--@mbg.generated-->
    update T_TRADER_CUSTOMER_BUSSINESS_BRAND
    set TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER},
      BRAND_ID = #{brandId,jdbcType=INTEGER}
    where TRADER_CUSTOMER_BUSSINESS_BRAND_ID = #{traderCustomerBussinessBrandId,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="TRADER_CUSTOMER_BUSSINESS_BRAND_ID" keyProperty="traderCustomerBussinessBrandId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_CUSTOMER_BUSSINESS_BRAND
    (TRADER_CUSTOMER_ID, BRAND_ID)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.traderCustomerId,jdbcType=INTEGER}, #{item.brandId,jdbcType=INTEGER})
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2023-08-14-->
  <delete id="deleteByTraderCustomerId">
        delete from T_TRADER_CUSTOMER_BUSSINESS_BRAND
        where TRADER_CUSTOMER_ID=#{traderCustomerId,jdbcType=INTEGER}
    </delete>
</mapper>