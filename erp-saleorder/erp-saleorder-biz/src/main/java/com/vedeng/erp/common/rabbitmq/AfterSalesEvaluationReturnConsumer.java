package com.vedeng.erp.common.rabbitmq;

import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import com.vedeng.common.trace.mq.AbstractMessageListener;
import com.vedeng.erp.aftersale.domain.dto.AfterSalesEvaluationReturnDto;
import com.vedeng.erp.aftersale.domain.dto.ModifyAfterSalesContactInfoDto;
import com.vedeng.erp.aftersale.service.AfterSalesCommonService;
import com.vedeng.order.service.ReturnVisitRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Component
@Slf4j
public class AfterSalesEvaluationReturnConsumer extends AbstractMessageListener {


    @Autowired
    private AfterSalesCommonService afterSalesCommonService;

    @Override
    public void doBusiness(Message message, Channel channel) throws Exception {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("前台售后评价推送：[{}]", message);
        try {
            AfterSalesEvaluationReturnDto afterSalesEvaluationReturnDto = JSONObject.parseObject(messageBody, AfterSalesEvaluationReturnDto.class);
            afterSalesCommonService.afterSalesEvaluationReturn(afterSalesEvaluationReturnDto);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            log.info("前台售后评价推送处理结束");
        } catch (Exception e) {
            log.error("前台售后评价推送发生错误:消息内容：[{}],异常信息:", message, e);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            } catch (IOException ex) {
                log.error("前台售后评价推送消息发生错误:消息内容：[{}],异常信息:", message, e);
            }
        }
    }

}
