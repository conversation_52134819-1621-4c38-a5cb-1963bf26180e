package com.newtask.doc;


import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.docSync.service.SyncSupplierService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 每隔半个小时，将发货单信息发送给艾迪康
 * 
 * <AUTHOR> adkDeliverySyncTask
 *
 */
@JobHandler(value = "SupplierSyncDocTask")
@Component
public class SupplierSyncDocTask extends AbstractJobHandler {
	Logger log = LoggerFactory.getLogger(SupplierSyncDocTask.class);

	@Autowired
	SyncSupplierService syncSupplierService;
	String path="/data/web/tomcat/erp.ivedeng.com/ROOT/upload/attachment";
	// @Transactional(rollbackFor = Exception.class, readOnly = false, propagation =
	// Propagation.REQUIRED)
	@Override
	public ReturnT<String> doExecute(String param) throws Exception {
		if(StringUtils.equalsIgnoreCase("all",param)){
			param="44,45,47,57,77,101,126,151,157,193,230,235,250,278,289,302,304,315,338,344,355,381,444,450,535,544,551,562,579,610,640,650,658,749,755,875,885,903,1092,1137,1138,1161,1191,1192,1194,1202,1234,1283,1288,1303,1318,1321,1332,1333,1334,1348,1364,1373,1380,1383,1384,1387,1403,1409,1410,1419,1424,1438,1447,1450,1463,1472,1473,1476,1477,1487,1511,1512,1514,1521,1547,1551,1553,1564,1570,1601,1612,1616,1619,1644,1666,1678,1696,1734,1739,1746,1747,1755,1770,1773,1781,1786,1787,1803,1839,1841,1848,1857,1881,1887,1900,1906,1912,1914,1928,1930,1939,1951,1988,1989,2019,2030,2041,2057,2087,2090,2099,2100,2103,2107,2134,2141,2146,2154,2156,2157,2163,2168,2173,2178,2183,2249,2250,2263,2265,2266,2286,2291,2293,2296,2310,2317,2337,2338,2350,2359,2362,2375,2379,2380,2393,2396,2397,2398,2411,2413,2420,2427,2428,2433,2438,2443,2450,2452,2453,2457,2460,2469,2481,2484,2505,2507,2515,2516,2524,2535,2541,2543,2559,2564,2569,2581,2595,2602,2608,2626,2634,2653,2655,2657,2659,2666,2670,2676,2687,2706,2708,2709,2721,2724,2725,2726,2728,2735,2736,2738,2739,2740,2742,2750,2753,2761,2763,2766,2768,2769,2770,2776,2780,2783,2784,2800,2802,2807,2808,2810,2817,2822,2848,2849,2850,2861,2869,2871,2887,2895,2904,2916,2921,2923,2941,2947,2956,2973,2974,2991,2998,3003,3013,3016,3017,3063,3068,3075,3076,3106,3109,3111,3113,3118,3124,3136,3138,3149,3153,3173,3204,3222,3225,3230,3231,3236,3243,3246,3260,3261,3284,3288,3291,3301,3315,3326,3334,3338,3339,3346,3352,3353,3370,3374,3387,3393,3397,3400,3408,3413,3419,3422,3423,3430,3432,3434,3438,3439,3445,3465,3478,3485,3487,3494,3514,3515,3527,3532,3540,3548,3554,3556,3559,3568,3569,3593,3594,3601,3609,3620,3631,3662,3664,3680,3692,3698,3700,3737,3741,3745,3747,3773,3777,3778,3784,3798,3799,3806,3811,3813,3821,3822,3836,3838,3845,3853,3861,3865,3870,3876,3878,3879,3885,3886,3894,3898,3899,3903,3909,3916,3917,3920,3921,3926,3932,3936,3940,3942,3952,3954,3960,3970,3973,3982,3996,4003,4005,4012,4020,4024,4029,4037,4044,4048,4051,4052,4057,4063,4065,4077,4092,4093,4096,4100,4106,4120,4133,4139,4141,4148,4149,4153,4166,4170,4175,4176,4182,4194,4205,4219,4236,4237,4242,4245,4246,4261,4263,4274,4279,4280,4284,4287,4288,4293,4295,4297,4298,4312,4319,4323,4328,4337,4344,4347,4352,4355,4358,4361,4363,4389,4407,4412,4419,4444,4449,4459,4460,4461,4463,4468,4473,4475,4477,4481,4486,4521,4532,4533,4548,4567,4570,4571,4585,4591,4593,4616,4620,4629,4631,4641,4656,4657,4658,4659,4663,4664,4674,4679,4680,4681,4687,4690,4691,4696,4701,4702,4711,4716,4717,4723,4725,4736,4737,4743,4746,4750,4752,4753,4754,4758,4760,4764,4766,4769,4777,4778,4780,4781,4789,4791,4794,4795,4797,4802,4803,4804,4813,4814,4820,4829,4835,4838,4839,4842,4849,4856,4857,4858,4862,4870,4878,4884,4887,4892,4896,4898,4902,4904,4906,4912,4917,4918,4924,4925,4932,4940,4943,4945,4954,4955,4960,4962,4965,4966,4967,4968,4969,4971,4978,4980,4991,4997,4998,5006,5008,5010,5015,5017,5024,5026,5033,5036,5038,5042,5043,5049,5052,5056,5057,5059,5066,5068,5070,5077,5080,5083,5085,5086,5089,5091,5092,5093,5095,5097,5098,5099,5101,5103,5108,5116,5118,5129,5130,5149,5154,5158,5159,5168,5172,5174,5175,5178,5188,5197,5199,5200,5201,5202,5204,5205,5206,5207,5208,5210,5212,5213,5215,5216,5217,5218,5219,5222,5223,5224,5225,5226,5227,5229,5231,5241,5243,5244,5246,5250,5251,5252,5253,5262,5263,5264,5272,5274,5276,5281,5283,5291,5293,5298,5305,5318,5320,5324,5328,5334,5337,5342,5343,5345,5349,5350,5355,5363,5368,5372,5373,5376,5382,5384,5390,5408,5411,5416,5419,5422,5432,5438,5440,5444,5447,5448,5457,5458,5460,5464,5470,5484,5488,5496,5500,5509,5510,5519,5521,5528,5535,5552,5554,5565,5567,5581,5587,5589,5602,5619,5621,5622,5630,5635,5646,5648";
		}


	 	String ids[]= StringUtils.split(param,",");
	 	if(ids==null||ids.length==0){
	 		return SUCCESS;
		}

		for (int i = 0; i < ids.length; i++) {
			syncSupplierService.syncSupplier2Doc(path, NumberUtils.toInt(ids[i]));
		}
		return SUCCESS;
	}

}
