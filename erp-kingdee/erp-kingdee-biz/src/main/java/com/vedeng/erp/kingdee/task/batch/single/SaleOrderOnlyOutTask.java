package com.vedeng.erp.kingdee.task.batch.single;

import cn.hutool.core.date.DateUtil;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.kingdee.batch.job.SaleOrderBatchJob;
import com.vedeng.erp.kingdee.task.batch.TaskBatchHandle;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/30 14:11
 *
 * 销售正逆项流程
 */
@JobHandler(value = "SaleOrderOnlyOutTask")
@Component
public class SaleOrderOnlyOutTask extends AbstractJobHandler {

    @Autowired
    private JobLauncher jobLauncher;

    @Autowired
    private SaleOrderBatchJob saleOrderBatchJob;





    /**
     * {"beginTime":"2022-11-01 00:00:00",
     * "endTime":"2022-12-01 00:00:00",
     * "timestamp":"1666687179395"}
     */
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        if (StringUtils.isBlank(param)){
            // 近一天
            Date now = new Date();
            String endTime = DateUtil.format(now, "yyyy-MM-dd HH:mm:ss");
            String startTime = DateUtil.format(DateUtil.offsetDay(now, -1), "yyyy-MM-dd HH:mm:ss");
            param = "{\"beginTime\":\"" + startTime + "\",\"endTime\":\"" + endTime + "\"}";
        }
        JobParameters jobParameters = new TaskBatchHandle().buildJobParameters(param);
        XxlJobLogger.log("====================销售仅出库流程开始==================");
        Job saleOrderOnlyOutJob = saleOrderBatchJob.saleOrderOnlyOutJob();
        jobLauncher.run(saleOrderOnlyOutJob, jobParameters);
        XxlJobLogger.log("====================销售仅出库流程结束==================");


        return SUCCESS;
    }


}
