<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>    
<div class="customer">
    <ul>
        <li>
            <a href="${pageContext.request.contextPath}/trader/customer/new/portrait.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}&customerNature=${traderCustomer.customerNature}"
               <c:if test="${method == 'portrait' }">class="customer-color"</c:if> >客户360</a>
        </li>
        <li>
            <a href="${pageContext.request.contextPath}/trader/customer/baseinfo.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}" 
            <c:if test="${method == 'baseinfo' }">class="customer-color"</c:if> >基本信息</a>
        </li>
        <li>
            <a href="${pageContext.request.contextPath}/trader/customer/new/distribution/link.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}&customerNature=${traderCustomer.customerNature}"
               <c:if test="${method == 'distributionLink' }">class="customer-color"</c:if> >经销链路</a>
        </li>
        <li >
            <a href="${pageContext.request.contextPath}/trader/customer/new/customeDetail.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}"
            <c:if test="${method == 'manageinfo' }">class="customer-color"</c:if> >客户行为</a>
        </li>
        <li>
            <a id="finace" href="${pageContext.request.contextPath}/trader/customer/getFinanceAndAptitude.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}&customerNature=${traderCustomer.customerNature}&aptitudeStatus=${traderCustomerVo.aptitudeStatus}"
            <c:if test="${method == 'financeandaptitude' }">class="customer-color"</c:if> >财务与资质信息</a>
        </li>
        <li>
            <a id="contact" href="${pageContext.request.contextPath}/ezadmin/list/list-traderContractList?IS_ENABLE=1&perPageInt=50&TRADER_ID=${traderCustomer.traderId}&TRADER_CUSTOMERID=${traderCustomer.traderCustomerId}&customerNature=${traderCustomer.customerNature}"
            <c:if test="${method == 'contactsaddress' }">class="customer-color"</c:if> >联系人</a>
        </li>
        <li>
            <a id="address" href="${pageContext.request.contextPath}/ezadmin/list/list-addressInformationList?TRADER_ID=${traderCustomer.traderId}&TRADER_CUSTOMERID=${traderCustomer.traderCustomerId}&customerNature=${traderCustomer.customerNature}"
               <c:if test="${method == 'contactsaddress' }">class="customer-color"</c:if> >联系地址</a>
        </li>
        <li>
        <a id="caddress" href="${pageContext.request.contextPath}/ezadmin/list/list-customerAddAddressList?TRADER_ID=${traderCustomer.traderId}&TRADER_CUSTOMERID=${traderCustomer.traderCustomerId}&customerNature=${traderCustomer.customerNature}"
               <c:if test="${method == 'contactsaddress' }">class="customer-color"</c:if> >客户添加的地址</a>
        </li>
        <li>
            <a href="${pageContext.request.contextPath}/trader/customer/businesslist.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}&traderCustomerId=${traderCustomer.traderCustomerId}&customerNature=${traderCustomer.customerNature}"
            <c:if test="${method == 'saleorder' }">class="customer-color"</c:if>>交易记录</a>
        </li>
        <li>
            <a href="${pageContext.request.contextPath}/trader/customer/communicaterecord.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}&traderCustomerId=${traderCustomer.traderCustomerId}&customerNature=${traderCustomer.customerNature}"
            <c:if test="${method == 'communicaterecord' }">class="customer-color"</c:if> >沟通记录</a>
        </li>

        <c:if test="${traderCustomer.belongPlatform == 1}">
            <li>
                <a href="${pageContext.request.contextPath}/trader/relation/info.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}"
                   <c:if test="${method == 'relation' }">class="customer-color"</c:if> >关联客户</a>
            </li>
        </c:if>

        <li>
            <a href="${pageContext.request.contextPath}/trader/customer/new/share.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}"
               <c:if test="${method == 'share' }">class="customer-color"</c:if> >分享客户</a>
        </li>

    </ul>
</div>