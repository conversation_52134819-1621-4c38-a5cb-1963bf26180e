package com.vedeng.erp.saleorder.enums;

import com.vedeng.common.core.exception.ServiceException;

/**
 * 开票状主状态
 *
 * 开票状态0未开票 1部分开票 2全部开票
 */
public enum SaleOrderInvoiceStatusEnum {

    NULL(0, "未开票"),
    PART(1, "部分开票"),
    ALL(2, "全部开票"),
    ;
    private Integer code;

    private String desc;

    SaleOrderInvoiceStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static String getDesc(Integer code){
        for(SaleOrderInvoiceStatusEnum v : values()){
            if(v.getCode().equals(code)){
                return v.getDesc();
            }
        }
        throw new ServiceException("没有对应的订单状态枚举值");
    }
}
