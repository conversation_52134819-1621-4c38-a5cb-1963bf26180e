@import (less) '../common.css';

.vd-ui-cascader {
    position: relative;
    width: 300px;
    height: 100%;

    .vd-ui-cascader-wrapper {
        position: relative;
        width: 100%;

        .icon {
            position: absolute;
            right: 5px;
            top: 1px;
            font-size: 16px;
            color: @Text-3;
            cursor: pointer;
            pointer-events: none;
            line-height: 29px;
            transition: 0.19s;
            &.large {
                line-height: 42px;
            }  
            &.small {
                line-height: 26px;
            }  
            &.rotate {
                transform: rotate(180deg);
                transition: 0.22s;
            }
        }
        
        input::-webkit-input-placeholder {
            color:@Text-2;
        }
        /deep/.vd-ui-input input {
            cursor: pointer;
        }

        // 多选input局域
        .multiple-wrap {
            position: relative;
            color: #333;
            background: #fff;
            border: solid 1px #BABFC2;
            border-radius: 3px;
            height: 30px;
            padding: 0px 10px;
            font-size: 14px;
            padding: 0 40px 0 10px; 
            display: flex;
            align-items: center;

            .multiple-tags {
                flex-shrink: 0;
                display: flex;
                align-items: center;
                margin-right: 5px;
                max-width: 80%;

                .tag {
                    position: relative;
                    height: 22px;
                    padding: 0 5px;
                    background: #F5F7FA;
                    border: solid 1px #E1E5E8;
                    border-radius: 2px;
                    font-size: 12px;
                    line-height: 19px;
                    color: #333;
                    margin-right: 5px;
                    padding-right: 22px;

                    .tag-del {
                        width: 20px;
                        height: 21px;
                        font-size: 14px;
                        color: #999;
                        cursor: pointer;
                        display: flex;
                        justify-content: flex-start;
                        align-items: center;
                        position: absolute;
                        right: 0;
                        top: 0;

                        &:hover {
                            color: #f60;
                        }
                    }
                }

                .tag-add {
                    height: 22px;
                    padding: 0 5px;
                    background: #F5F7FA;
                    border: solid 1px #E1E5E8;
                    border-radius: 2px;
                    font-size: 12px;
                    line-height: 19px;
                    color: #333;
                }
            }

            .input {
                flex: 1;

                >input {
                    width: 100%;
                }
            }

            .vd-ui-input__suffix {
                position: absolute;
                height: 100%;
                right: 5px;
                top: 0!important;
                text-align: center;
                color: #c0c4cc;
                transition: all .3s;
                pointer-events: none;

                .vd-ui-input__suffix-inner {
                    .icon {
                        top: 0!important;
                    }
                }

                &.clear {
                    pointer-events: all;
                    right: 0;

                    .icon-error2 {
                        width: 33px;
                        height: 30px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        cursor: pointer;

                        &:hover {
                            color: #333;
                        }
                    }
                }
            }


        }
    }

    .vd-ui-cascader-panel-wrap {
        display: inline-flex;
        // width:100%;
        position: absolute;
        z-index: 1500;
    }

    .vd-ui-cascader-menu {
        // width: 100%;
        display: inline-flex;
        position: absolute;
        z-index: 1500;

        &.width {
            width: 100%;
        }

        &.appear-up {
            transform-origin : center bottom;
            animation: appear 0.22s ease-out;
        }
        &.appear-down {
            transform-origin : center top;
            animation: appear 0.22s ease-out;
        }
        .loading-li {    
            box-sizing: border-box;
            width: 300px;
            overflow: auto;
            border-radius: 3px;  
            border: solid 1px @Gray-7;
            border-radius: 3px;
            box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
            background-color: @Text-7;
            padding:5px 0px;
            list-style-type: none;
            margin:0;
            .scrollbar;

            p {
                padding-left: 10px;
                height: 29px;
                line-height: 29px;
                i {
                    animation: loading 1.8s linear  infinite;
                    display: inline-block;
                    position: relative;
                    top: 2px;
                    font-size: 16px;
                    margin-right: 5px;
                    color: @Brand-6;
                    
                }
            }
        }
        .failed-li {
            box-sizing: border-box;
            width: 300px;
            overflow: auto;
            border-radius: 3px;  
            border: solid 1px @Gray-7;
            border-radius: 3px;
            box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
            background-color: @Text-7;
            padding: 5px 0px;
            list-style-type: none;
            margin: 0;
            .scrollbar;

            p {
                padding-left: 10px;
                height: 29px;
                line-height: 29px;

                i {
                    position: relative;
                    top: 2px;
                    font-size: 16px;
                    color: @Crimson-6;
                    margin-right: 5px;
                }
                .reload{
                    color: @Brand-6;
                    cursor: pointer;
                }
            }
        }
    }

    .suggestion-list {
        width: 100%;
        box-sizing: border-box;
        overflow: auto;
        border: solid 1px #BABFC2;
        border-radius: 3px;
        box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
        background-color: #ffffff;
        padding: 5px 0px;
        list-style-type: none;
        margin: 0;

        .filter-list {
            padding: 0 10px;
            max-height: 200px;
            overflow-y: auto;
            .scrollbar;

            .filter-item {
                height: 33px;
                line-height: 33px;
                display: flex;
                align-items: center;
                cursor: pointer;

                &.active {
                    color: #09f;
                }
            }
        }
        .no-filter {
            padding: 5px 0;
            color: #999;
            text-align: center;
        }
    }

    .menu-wrap {
        // background: pink;
        // padding: 10px;
        display: inline-flex;
    }
}

@keyframes appear {
    0% {
        opacity : 0;
        -webkit-transform : scale(1,0);
    }
    100% {
        -webkit-transform : scale(1,1);
        opacity : 1;
    }
}
@keyframes loading {
  0% {
        transform : rotate(0deg);
    }

    100% {
        transform : rotate(360deg);
    }
}


.vd-ui-cascader-node {
    box-sizing: border-box;
    overflow: auto;
    border-radius: 3px;
    border: solid 1px @Gray-7;
    border-radius: 3px;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
    background-color: @Text-7;
    padding: 5px 0px;
    list-style-type: none;
    margin: 0;
    .scrollbar;

    &.multiple {
        width: 200px!important;
    }

    &.appear-up {
        transform-origin : center bottom;
        animation: appear 0.22s ease-out;
    }
    &.appear-down {
        transform-origin : center top;
        animation: appear 0.22s ease-out;
    }
    li {
        width: 100%;
        cursor: pointer;
        padding: 0px 10px;
        padding-right: 36px;
        height: 33px;
        box-sizing: border-box;
        line-height: 33px;
        position: relative;
        display: flex;
        align-items: center;

        &.selected {
            color: #09f;
        }

        p {
            flex: 1;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
        }
        .icon-right {
            position: absolute ;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 16px;
        }

        .icon-radio1, .icon-radio3 {
            line-height: 1;
            margin-right: 5px;
        }

        .icon-radio1 {
            color: #666;
        }

        .icon-radio3 {
            color: #09f;
        }
    }
}

// 复选框
.ui-cascader-checkbox-wrap {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
    border: solid 1px #969B9E;
    border-radius: 3px;
    margin-right: 5px;

    &.active {
        border: solid 1px #09f;
        background: #09f;
        display: flex;
        justify-content: center;
        align-items: center;

        .icon-selected2 {
            font-size: 12px;
            color: #fff;
        }
        .icon-deduct {
            font-size: 12px;
            color: #fff;
        }
    }
}









