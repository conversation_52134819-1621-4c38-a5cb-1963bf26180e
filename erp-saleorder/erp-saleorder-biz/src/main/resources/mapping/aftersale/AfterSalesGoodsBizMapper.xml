<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.aftersale.mapper.AfterSalesGoodsBizMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.aftersale.domain.entity.AfterSalesGoodsEntity">
    <!--@mbg.generated-->
    <!--@Table T_AFTER_SALES_GOODS-->
    <id column="AFTER_SALES_GOODS_ID" jdbcType="INTEGER" property="afterSalesGoodsId" />
    <result column="AFTER_SALES_ID" jdbcType="INTEGER" property="afterSalesId" />
    <result column="ORDER_DETAIL_ID" jdbcType="INTEGER" property="orderDetailId" />
    <result column="GOODS_TYPE" jdbcType="INTEGER" property="goodsType" />
    <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId" />
    <result column="NUM" jdbcType="INTEGER" property="num" />
    <result column="PRICE" jdbcType="DECIMAL" property="price" />
    <result column="DELIVERY_DIRECT" jdbcType="INTEGER" property="deliveryDirect" />
    <result column="ARRIVAL_NUM" jdbcType="INTEGER" property="arrivalNum" />
    <result column="ARRIVAL_TIME" jdbcType="BIGINT" property="arrivalTime" />
    <result column="ARRIVAL_USER_ID" jdbcType="INTEGER" property="arrivalUserId" />
    <result column="ARRIVAL_STATUS" jdbcType="INTEGER" property="arrivalStatus" />
    <result column="DELIVERY_NUM" jdbcType="INTEGER" property="deliveryNum" />
    <result column="DELIVERY_STATUS" jdbcType="INTEGER" property="deliveryStatus" />
    <result column="DELIVERY_TIME" jdbcType="BIGINT" property="deliveryTime" />
    <result column="SKU_REFUND_AMOUNT" jdbcType="DECIMAL" property="skuRefundAmount" />
    <result column="SKU_OLD_REFUND_AMOUNT" jdbcType="DECIMAL" property="skuOldRefundAmount" />
    <result column="IS_ACTION_GOODS" jdbcType="INTEGER" property="isActionGoods" />
    <result column="UPDATE_DATA_TIME" jdbcType="TIMESTAMP" property="updateDataTime" />
    <result column="FACTORY_CODE" jdbcType="VARCHAR" property="factoryCode" />
    <result column="GOOD_CREATE_TIME" jdbcType="BIGINT" property="goodCreateTime" />
    <result column="GOOD_VAILD_TIME" jdbcType="BIGINT" property="goodVaildTime" />
    <result column="RKNUM" jdbcType="INTEGER" property="rknum" />
    <result column="AFTER_INVOICE_NUM" jdbcType="DECIMAL" property="afterInvoiceNum" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    AFTER_SALES_GOODS_ID, AFTER_SALES_ID, ORDER_DETAIL_ID, GOODS_TYPE, GOODS_ID, NUM, 
    PRICE, DELIVERY_DIRECT, ARRIVAL_NUM, ARRIVAL_TIME, ARRIVAL_USER_ID, ARRIVAL_STATUS, 
    DELIVERY_NUM, DELIVERY_STATUS, DELIVERY_TIME, SKU_REFUND_AMOUNT, SKU_OLD_REFUND_AMOUNT, 
    IS_ACTION_GOODS, UPDATE_DATA_TIME, FACTORY_CODE, GOOD_CREATE_TIME, GOOD_VAILD_TIME, 
    RKNUM, AFTER_INVOICE_NUM
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_AFTER_SALES_GOODS
    where AFTER_SALES_GOODS_ID = #{afterSalesGoodsId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_AFTER_SALES_GOODS
    where AFTER_SALES_GOODS_ID = #{afterSalesGoodsId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="AFTER_SALES_GOODS_ID" keyProperty="afterSalesGoodsId" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSalesGoodsEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALES_GOODS (AFTER_SALES_ID, ORDER_DETAIL_ID, GOODS_TYPE, 
      GOODS_ID, NUM, PRICE, 
      DELIVERY_DIRECT, ARRIVAL_NUM, ARRIVAL_TIME, 
      ARRIVAL_USER_ID, ARRIVAL_STATUS, DELIVERY_NUM, 
      DELIVERY_STATUS, DELIVERY_TIME, SKU_REFUND_AMOUNT, 
      SKU_OLD_REFUND_AMOUNT, IS_ACTION_GOODS, UPDATE_DATA_TIME, 
      FACTORY_CODE, GOOD_CREATE_TIME, GOOD_VAILD_TIME, 
      RKNUM, AFTER_INVOICE_NUM)
    values (#{afterSalesId,jdbcType=INTEGER}, #{orderDetailId,jdbcType=INTEGER}, #{goodsType,jdbcType=INTEGER}, 
      #{goodsId,jdbcType=INTEGER}, #{num,jdbcType=INTEGER}, #{price,jdbcType=DECIMAL}, 
      #{deliveryDirect,jdbcType=INTEGER}, #{arrivalNum,jdbcType=INTEGER}, #{arrivalTime,jdbcType=BIGINT}, 
      #{arrivalUserId,jdbcType=INTEGER}, #{arrivalStatus,jdbcType=INTEGER}, #{deliveryNum,jdbcType=INTEGER}, 
      #{deliveryStatus,jdbcType=INTEGER}, #{deliveryTime,jdbcType=BIGINT}, #{skuRefundAmount,jdbcType=DECIMAL}, 
      #{skuOldRefundAmount,jdbcType=DECIMAL}, #{isActionGoods,jdbcType=INTEGER}, #{updateDataTime,jdbcType=TIMESTAMP}, 
      #{factoryCode,jdbcType=VARCHAR}, #{goodCreateTime,jdbcType=BIGINT}, #{goodVaildTime,jdbcType=BIGINT}, 
      #{rknum,jdbcType=INTEGER}, #{afterInvoiceNum,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" keyColumn="AFTER_SALES_GOODS_ID" keyProperty="afterSalesGoodsId" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSalesGoodsEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALES_GOODS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="afterSalesId != null">
        AFTER_SALES_ID,
      </if>
      <if test="orderDetailId != null">
        ORDER_DETAIL_ID,
      </if>
      <if test="goodsType != null">
        GOODS_TYPE,
      </if>
      <if test="goodsId != null">
        GOODS_ID,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="price != null">
        PRICE,
      </if>
      <if test="deliveryDirect != null">
        DELIVERY_DIRECT,
      </if>
      <if test="arrivalNum != null">
        ARRIVAL_NUM,
      </if>
      <if test="arrivalTime != null">
        ARRIVAL_TIME,
      </if>
      <if test="arrivalUserId != null">
        ARRIVAL_USER_ID,
      </if>
      <if test="arrivalStatus != null">
        ARRIVAL_STATUS,
      </if>
      <if test="deliveryNum != null">
        DELIVERY_NUM,
      </if>
      <if test="deliveryStatus != null">
        DELIVERY_STATUS,
      </if>
      <if test="deliveryTime != null">
        DELIVERY_TIME,
      </if>
      <if test="skuRefundAmount != null">
        SKU_REFUND_AMOUNT,
      </if>
      <if test="skuOldRefundAmount != null">
        SKU_OLD_REFUND_AMOUNT,
      </if>
      <if test="isActionGoods != null">
        IS_ACTION_GOODS,
      </if>
      <if test="updateDataTime != null">
        UPDATE_DATA_TIME,
      </if>
      <if test="factoryCode != null">
        FACTORY_CODE,
      </if>
      <if test="goodCreateTime != null">
        GOOD_CREATE_TIME,
      </if>
      <if test="goodVaildTime != null">
        GOOD_VAILD_TIME,
      </if>
      <if test="rknum != null">
        RKNUM,
      </if>
      <if test="afterInvoiceNum != null">
        AFTER_INVOICE_NUM,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="afterSalesId != null">
        #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="orderDetailId != null">
        #{orderDetailId,jdbcType=INTEGER},
      </if>
      <if test="goodsType != null">
        #{goodsType,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null">
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="deliveryDirect != null">
        #{deliveryDirect,jdbcType=INTEGER},
      </if>
      <if test="arrivalNum != null">
        #{arrivalNum,jdbcType=INTEGER},
      </if>
      <if test="arrivalTime != null">
        #{arrivalTime,jdbcType=BIGINT},
      </if>
      <if test="arrivalUserId != null">
        #{arrivalUserId,jdbcType=INTEGER},
      </if>
      <if test="arrivalStatus != null">
        #{arrivalStatus,jdbcType=INTEGER},
      </if>
      <if test="deliveryNum != null">
        #{deliveryNum,jdbcType=INTEGER},
      </if>
      <if test="deliveryStatus != null">
        #{deliveryStatus,jdbcType=INTEGER},
      </if>
      <if test="deliveryTime != null">
        #{deliveryTime,jdbcType=BIGINT},
      </if>
      <if test="skuRefundAmount != null">
        #{skuRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="skuOldRefundAmount != null">
        #{skuOldRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="isActionGoods != null">
        #{isActionGoods,jdbcType=INTEGER},
      </if>
      <if test="updateDataTime != null">
        #{updateDataTime,jdbcType=TIMESTAMP},
      </if>
      <if test="factoryCode != null">
        #{factoryCode,jdbcType=VARCHAR},
      </if>
      <if test="goodCreateTime != null">
        #{goodCreateTime,jdbcType=BIGINT},
      </if>
      <if test="goodVaildTime != null">
        #{goodVaildTime,jdbcType=BIGINT},
      </if>
      <if test="rknum != null">
        #{rknum,jdbcType=INTEGER},
      </if>
      <if test="afterInvoiceNum != null">
        #{afterInvoiceNum,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSalesGoodsEntity">
    <!--@mbg.generated-->
    update T_AFTER_SALES_GOODS
    <set>
      <if test="afterSalesId != null">
        AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="orderDetailId != null">
        ORDER_DETAIL_ID = #{orderDetailId,jdbcType=INTEGER},
      </if>
      <if test="goodsType != null">
        GOODS_TYPE = #{goodsType,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null">
        GOODS_ID = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        PRICE = #{price,jdbcType=DECIMAL},
      </if>
      <if test="deliveryDirect != null">
        DELIVERY_DIRECT = #{deliveryDirect,jdbcType=INTEGER},
      </if>
      <if test="arrivalNum != null">
        ARRIVAL_NUM = #{arrivalNum,jdbcType=INTEGER},
      </if>
      <if test="arrivalTime != null">
        ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
      </if>
      <if test="arrivalUserId != null">
        ARRIVAL_USER_ID = #{arrivalUserId,jdbcType=INTEGER},
      </if>
      <if test="arrivalStatus != null">
        ARRIVAL_STATUS = #{arrivalStatus,jdbcType=INTEGER},
      </if>
      <if test="deliveryNum != null">
        DELIVERY_NUM = #{deliveryNum,jdbcType=INTEGER},
      </if>
      <if test="deliveryStatus != null">
        DELIVERY_STATUS = #{deliveryStatus,jdbcType=INTEGER},
      </if>
      <if test="deliveryTime != null">
        DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
      </if>
      <if test="skuRefundAmount != null">
        SKU_REFUND_AMOUNT = #{skuRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="skuOldRefundAmount != null">
        SKU_OLD_REFUND_AMOUNT = #{skuOldRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="isActionGoods != null">
        IS_ACTION_GOODS = #{isActionGoods,jdbcType=INTEGER},
      </if>
      <if test="updateDataTime != null">
        UPDATE_DATA_TIME = #{updateDataTime,jdbcType=TIMESTAMP},
      </if>
      <if test="factoryCode != null">
        FACTORY_CODE = #{factoryCode,jdbcType=VARCHAR},
      </if>
      <if test="goodCreateTime != null">
        GOOD_CREATE_TIME = #{goodCreateTime,jdbcType=BIGINT},
      </if>
      <if test="goodVaildTime != null">
        GOOD_VAILD_TIME = #{goodVaildTime,jdbcType=BIGINT},
      </if>
      <if test="rknum != null">
        RKNUM = #{rknum,jdbcType=INTEGER},
      </if>
      <if test="afterInvoiceNum != null">
        AFTER_INVOICE_NUM = #{afterInvoiceNum,jdbcType=DECIMAL},
      </if>
    </set>
    where AFTER_SALES_GOODS_ID = #{afterSalesGoodsId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSalesGoodsEntity">
    <!--@mbg.generated-->
    update T_AFTER_SALES_GOODS
    set AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      ORDER_DETAIL_ID = #{orderDetailId,jdbcType=INTEGER},
      GOODS_TYPE = #{goodsType,jdbcType=INTEGER},
      GOODS_ID = #{goodsId,jdbcType=INTEGER},
      NUM = #{num,jdbcType=INTEGER},
      PRICE = #{price,jdbcType=DECIMAL},
      DELIVERY_DIRECT = #{deliveryDirect,jdbcType=INTEGER},
      ARRIVAL_NUM = #{arrivalNum,jdbcType=INTEGER},
      ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
      ARRIVAL_USER_ID = #{arrivalUserId,jdbcType=INTEGER},
      ARRIVAL_STATUS = #{arrivalStatus,jdbcType=INTEGER},
      DELIVERY_NUM = #{deliveryNum,jdbcType=INTEGER},
      DELIVERY_STATUS = #{deliveryStatus,jdbcType=INTEGER},
      DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
      SKU_REFUND_AMOUNT = #{skuRefundAmount,jdbcType=DECIMAL},
      SKU_OLD_REFUND_AMOUNT = #{skuOldRefundAmount,jdbcType=DECIMAL},
      IS_ACTION_GOODS = #{isActionGoods,jdbcType=INTEGER},
      UPDATE_DATA_TIME = #{updateDataTime,jdbcType=TIMESTAMP},
      FACTORY_CODE = #{factoryCode,jdbcType=VARCHAR},
      GOOD_CREATE_TIME = #{goodCreateTime,jdbcType=BIGINT},
      GOOD_VAILD_TIME = #{goodVaildTime,jdbcType=BIGINT},
      RKNUM = #{rknum,jdbcType=INTEGER},
      AFTER_INVOICE_NUM = #{afterInvoiceNum,jdbcType=DECIMAL}
    where AFTER_SALES_GOODS_ID = #{afterSalesGoodsId,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="AFTER_SALES_GOODS_ID" keyProperty="afterSalesGoodsId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALES_GOODS
    (AFTER_SALES_ID, ORDER_DETAIL_ID, GOODS_TYPE, GOODS_ID, NUM, PRICE, DELIVERY_DIRECT, 
      ARRIVAL_NUM, ARRIVAL_TIME, ARRIVAL_USER_ID, ARRIVAL_STATUS, DELIVERY_NUM, DELIVERY_STATUS, 
      DELIVERY_TIME, SKU_REFUND_AMOUNT, SKU_OLD_REFUND_AMOUNT, IS_ACTION_GOODS, UPDATE_DATA_TIME, 
      FACTORY_CODE, GOOD_CREATE_TIME, GOOD_VAILD_TIME, RKNUM, AFTER_INVOICE_NUM)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.afterSalesId,jdbcType=INTEGER}, #{item.orderDetailId,jdbcType=INTEGER}, #{item.goodsType,jdbcType=INTEGER}, 
        #{item.goodsId,jdbcType=INTEGER}, #{item.num,jdbcType=INTEGER}, #{item.price,jdbcType=DECIMAL}, 
        #{item.deliveryDirect,jdbcType=INTEGER}, #{item.arrivalNum,jdbcType=INTEGER}, #{item.arrivalTime,jdbcType=BIGINT}, 
        #{item.arrivalUserId,jdbcType=INTEGER}, #{item.arrivalStatus,jdbcType=INTEGER}, 
        #{item.deliveryNum,jdbcType=INTEGER}, #{item.deliveryStatus,jdbcType=INTEGER}, 
        #{item.deliveryTime,jdbcType=BIGINT}, #{item.skuRefundAmount,jdbcType=DECIMAL}, 
        #{item.skuOldRefundAmount,jdbcType=DECIMAL}, #{item.isActionGoods,jdbcType=INTEGER}, 
        #{item.updateDataTime,jdbcType=TIMESTAMP}, #{item.factoryCode,jdbcType=VARCHAR}, 
        #{item.goodCreateTime,jdbcType=BIGINT}, #{item.goodVaildTime,jdbcType=BIGINT}, 
        #{item.rknum,jdbcType=INTEGER}, #{item.afterInvoiceNum,jdbcType=DECIMAL})
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2023-01-05-->
  <select id="selectByAfterSalesIdAndIsVirtual" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_AFTER_SALES_GOODS TASG
    left join V_CORE_SKU VCS on TASG.GOODS_ID = VCS.SKU_ID
    where AFTER_SALES_ID=#{afterSalesId,jdbcType=INTEGER}
    and VCS.IS_VIRTURE_SKU = 1
  </select>

  <select id="expSaleorderAfterNum" resultType="com.vedeng.erp.buyorder.dto.RBuyorderExpenseJSaleorderDto">
    select
    COALESCE(SUM(a.NUM),0) as Num , a.ORDER_DETAIL_ID as SALEORDER_GOODS_ID
    from T_AFTER_SALES_GOODS a
    left join T_AFTER_SALES b on a.AFTER_SALES_ID = b.AFTER_SALES_ID
    where a.GOODS_TYPE = 0  AND b.TYPE = 539 and a.ORDER_DETAIL_ID in
    <foreach item="item" collection="buyOrderSaleOrderGoodsDetailDtoList" open="(" separator="," close=")">
      #{item.saleorderGoodsId}
    </foreach>
    and b.ATFER_SALES_STATUS in (0,1,2)
    group by a.ORDER_DETAIL_ID
  </select>

  <select id="getAfterSalesGoodsListByAfterSalesIds"
            resultType="com.vedeng.erp.mobile.dto.AfterSalesGoodsListResultDto">
    select TASG.AFTER_SALES_GOODS_ID as afterSalesGoodsId,
           TASG.AFTER_SALES_ID as afterSalesId,
           TASG.ORDER_DETAIL_ID as orderDetailId,
           TASG.GOODS_TYPE as goodsType,
           TASG.GOODS_ID as goodsId,
           TASG.NUM as num,
           TASG.PRICE as price,
           TSG.SKU as sku,
           TSG.GOODS_NAME as goodsName
    from T_AFTER_SALES_GOODS TASG
           left join T_SALEORDER_GOODS TSG on TASG.ORDER_DETAIL_ID = TSG.SALEORDER_GOODS_ID
           left join T_AFTER_SALES TAS on TASG.AFTER_SALES_ID = TAS.AFTER_SALES_ID
    where TASG.GOODS_TYPE = 0
      and TAS.SUBJECT_TYPE = 535
      and TASG.AFTER_SALES_ID IN
      <foreach item="item" collection="afterSalesIds" open="(" separator="," close=")">
        #{item,jdbcType=INTEGER}
      </foreach>
    </select>

  <select id="getAfterSalesGoodsListByAfterSalesIdAndGoodTypeSpecial"
          resultType="com.vedeng.erp.mobile.dto.AfterSalesGoodsListResultDto">
    select TASG.AFTER_SALES_GOODS_ID as afterSalesGoodsId,
    TASG.AFTER_SALES_ID as afterSalesId,
    TASG.ORDER_DETAIL_ID as orderDetailId,
    TASG.GOODS_TYPE as goodsType,
    TASG.GOODS_ID as goodsId,
    TASG.NUM as num,
    TASG.PRICE as price,
    TSG.SKU_NO as sku,
    TSG.SHOW_NAME as goodsName
    from T_AFTER_SALES_GOODS TASG
    left join V_CORE_SKU TSG on TASG.GOODS_ID = TSG.SKU_ID
    left join T_AFTER_SALES TAS on TASG.AFTER_SALES_ID = TAS.AFTER_SALES_ID
    where TASG.GOODS_TYPE = 1
    and  TASG.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
  </select>
</mapper>