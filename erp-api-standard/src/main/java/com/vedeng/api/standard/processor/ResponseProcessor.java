package com.vedeng.api.standard.processor;

import com.vedeng.api.standard.core.ApiRequest;

/**
 * 响应处理器接口
 * 定义响应后处理的标准规范
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-18
 */
public interface ResponseProcessor {
    
    /**
     * 后处理响应
     * 在返回给客户端前对响应进行后处理
     * 
     * @param apiRequest 统一请求对象
     * @param result 业务执行结果
     * @return 处理后的结果
     */
    Object postProcess(ApiRequest apiRequest, Object result);
    
    /**
     * 记录响应日志
     * 
     * @param apiRequest 统一请求对象
     * @param result 业务执行结果
     */
    void logResponse(ApiRequest apiRequest, Object result);
    
    /**
     * 处理敏感数据
     * 对响应中的敏感数据进行脱敏处理
     * 
     * @param result 业务执行结果
     * @return 脱敏后的结果
     */
    Object sanitizeSensitiveData(Object result);
}
