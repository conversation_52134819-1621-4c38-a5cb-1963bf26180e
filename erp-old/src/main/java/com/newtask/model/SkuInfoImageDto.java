package com.newtask.model;

import java.io.Serializable;
import java.sql.Date;

public class SkuInfoImageDto implements Serializable {

    private Integer skuId;

    private String skuNo;

    private String skuName;

    private String belongProductManager;

    private String belongProductAssit;

    private String addTime;

    private String modTime;

    private Integer isDeleted;

    private Integer isNeedReport;

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    public String getSkuNo() {
        return skuNo;
    }

    public void setSkuNo(String skuNo) {
        this.skuNo = skuNo;
    }

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    public String getBelongProductManager() {
        return belongProductManager;
    }

    public void setBelongProductManager(String belongProductManager) {
        this.belongProductManager = belongProductManager;
    }

    public String getBelongProductAssit() {
        return belongProductAssit;
    }

    public void setBelongProductAssit(String belongProductAssit) {
        this.belongProductAssit = belongProductAssit;
    }

    public String getAddTime() {
        return addTime;
    }

    public void setAddTime(String addTime) {
        this.addTime = addTime;
    }

    public String getModTime() {
        return modTime;
    }

    public void setModTime(String modTime) {
        this.modTime = modTime;
    }

    public Integer getIsNeedReport() {
        return isNeedReport;
    }

    public void setIsNeedReport(Integer isNeedReport) {
        this.isNeedReport = isNeedReport;
    }
}
