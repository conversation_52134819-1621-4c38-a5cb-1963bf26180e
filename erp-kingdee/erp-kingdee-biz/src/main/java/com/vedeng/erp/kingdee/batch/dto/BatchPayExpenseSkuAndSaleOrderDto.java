package com.vedeng.erp.kingdee.batch.dto;

import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 费用商品和销售单
 * @date 2023/3/21 14:55
 **/
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BatchPayExpenseSkuAndSaleOrderDto {
    /**
     * sku
     */
    private String skuNo;
    /**
     * 关联数量
     */
    private BigDecimal num;

    /**
     * 销售单号
     */
    private String saleOrderNo;

    private Integer saleOrderId;
}
