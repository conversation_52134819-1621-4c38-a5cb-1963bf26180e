package com.vedeng.customerbillperiod.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <b>Description:</b><br>
 * 账期使用记录明细
 *
 * <AUTHOR>
 * @Note <b>ProjectName:</b> erp <br>
 * <b>PackageName:</b> com.vedeng.customerbillperiod.dto <br>
 * <b>ClassName:</b> CustomerBillPeriodDetailManageDto <br>
 * <b>Date:</b> 2021/8/5 14:09 <br>
 */
@Data
public class CustomerBillPeriodDetailManageDto {
    private Long billPeriodId;

    /**
     * 账期逾期编码
     */
    private String billPeriodOverdueManagementCode;

    /**
     * 客户账期结算方式，1产品发货，2产品开票
     */
    private Integer settlementType;

    /**
     * 账期风险管理明细类型，1订单发货，2订单开票，3售后退货，4售后退票，5还款
     */
    private Integer type;

    /**
     * 账期逾期管理关联表的ID，与TYPE字段对应：物流ID，发票ID，售后退货单ID，售后退票单ID，支付流水ID
     */
    private Long relatedId;

    /**
     * 账期使用关联表的ID，与TYPE字段对应：订单ID，售后单ID，支付流水ID
     */
    private Long orderRelatedId;

    /**
     * 客户账期逾期管理明细添加时间
     */
    private Long addTime;

    /**
     * 账期结算周期的，单位：天
     */
    private Integer settlementPeriod;

    /**
     * 账期使用金额，支付订单时，金额大于0，售后退货、还款时，金额小于0
     */
    private BigDecimal amount;

    /**
     * 逾期天数
     */
    private Integer overdueDays;

    /**
     * 计算还款后的金额
     */
    private BigDecimal calAmount;
}
