package com.vedeng.trader.enums;

import io.swagger.models.auth.In;

public enum WebAccountRoleEnum {
    BOSS(1,"老板"),
    PURCHASE_MANAGER(2,"采购经理"),
    SALE_MANAGER(3,"销售经理"),
    TECHNICAL_TEACHER(4,"技术或研发老师")
    ;

    WebAccountRoleEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private Integer code;
    private String desc;

    public static String getDescByCode(Integer code){
        for (WebAccountRoleEnum value : WebAccountRoleEnum.values()) {
            if (code.equals(value.code)){
                return value.desc;
            }
        }
        return "";
    }
}
