package com.newtask.trader;

import com.report.dao.PublicCustomerCalculateMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.BitsetUtils;
import com.vedeng.common.util.DateUtil;
import com.vedeng.erp.trader.domain.dto.TraderCustomerDto;
import com.vedeng.erp.trader.mapper.PublicCustomerRecordMapper;
import com.vedeng.erp.trader.mapper.PublicTraderMapper;
import com.vedeng.erp.trader.service.PublicCustomerCalculateService;
import com.vedeng.trader.dao.TraderCustomerMapper;
import com.vedeng.trader.model.dto.TraderCustomerAssociateInfoDto;
import com.vedeng.trader.service.TraderCustomerService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 公海规则计算，符合规则，纳入公海
 * 每天19点，将符合掉入公海条件的客户掉入公海（当日参与统计窗口期数据计算），并从客户列表中移除
 * 0 0 19 * * ？
 *
 * <AUTHOR>
 */
@Component
@JobHandler(value = "publicCustomerRecordTask")
public class PublicCustomerRecordTask extends AbstractJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(PublicCustomerRecordTask.class);

    /**
     * 公海虚拟账户配置人员ID
     */
    @Value("${VIRTUAL_PUBLIC_CUSTOMER_USER_ID}")
    private Integer virtualPublicCustomerUserId;

    @Autowired
    private TraderCustomerService traderCustomerService;

    @Resource
    private PublicTraderMapper publicTraderMapper;

    @Resource
    private PublicCustomerRecordMapper publicCustomerRecordMapper;

    @Resource
    private TraderCustomerMapper traderCustomerMapper;

    @Autowired
    private PublicCustomerCalculateService publicCustomerCalculateService;

    @Autowired
    private PublicCustomerCalculateMapper publicCustomerCalculateMapper;


    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("PublicCustomerRecordTask-JOB, START");

        logger.info("PublicCustomerRecordTask start param:{}", param);

        List<Integer> traderCustomerIdList = new ArrayList<>();

        if (StringUtils.isNotEmpty(param)){
            List<Integer> customerIdList = Arrays.stream(param.split(",")).map(Integer::valueOf).collect(Collectors.toList());
            List<TraderCustomerDto> traderCustomerDtoList = publicTraderMapper.getTraderCustomerByTraderCustomerId(customerIdList);
            List<Long> associateGroupList =
                    traderCustomerDtoList
                            .stream()
                            .map(TraderCustomerDto::getAssociatedCustomerGroup)
                            .distinct()
                            .filter(item -> item > 0)
                            .collect(Collectors.toList());
            if (associateGroupList.size() > 0) {
                traderCustomerIdList.addAll(publicTraderMapper.getTraderCustomerIdListByAssociateGroup(associateGroupList));
            } else {
                traderCustomerIdList.addAll(customerIdList);
            }
        }

        List<TraderCustomerAssociateInfoDto> waitingForEnterPublicCustomerRecords = publicCustomerCalculateMapper.getB2bCustomerExceptExempt(traderCustomerIdList);

        List<Integer> waitingForEnterPublicCustomerId = waitingForEnterPublicCustomerRecords.stream().map(TraderCustomerAssociateInfoDto::getTraderCustomerId).collect(Collectors.toList());

        BitSet customerResultBitSet =
                publicCustomerCalculateService.calculatePublicCustomer(BitsetUtils.collection2BitSet(waitingForEnterPublicCustomerId),2);

        //锁定时间在24小时之内的不纳入公海
        List<Integer> lockedCustomerRecently = publicCustomerCalculateMapper.getPublicCustomerLockedRecently(1);
        customerResultBitSet.andNot(BitsetUtils.collection2BitSet(lockedCustomerRecently));

         waitingForEnterPublicCustomerRecords =
                waitingForEnterPublicCustomerRecords.parallelStream().filter(item -> customerResultBitSet.get(item.getTraderCustomerId())).collect(Collectors.toList());

        /**
         * 3.客户流入公海
         * 将客户分配给虚拟账户
         */
        User user = new User();
        user.setUserId(ErpConst.ADMIN_ID);
        waitingForEnterPublicCustomerRecords.stream()
                .filter(Objects::nonNull).distinct()
                .forEach(traderCustomerDto -> {
                    traderCustomerService.assignSingleCustomer(traderCustomerDto.getTraderId(), virtualPublicCustomerUserId, ErpConst.ONE, user,1);
                    //保存公海客户记录
                    TraderCustomerDto publicCustomerDto = new TraderCustomerDto();
                    BeanUtils.copyProperties(traderCustomerDto,publicCustomerDto);
                    publicCustomerDto.setUserId(publicCustomerDto.getUserId() == null ? virtualPublicCustomerUserId : publicCustomerDto.getUserId());
                    publicCustomerRecordMapper.batchSavePublicCustomerRecords(Collections.singletonList(publicCustomerDto));
                    //重置客户的预警次数
                    traderCustomerMapper.resetCustomerEarlyWarningCount(Collections.singletonList(traderCustomerDto.getTraderCustomerId()));
                    logger.info("客户id{},掉入公海时间{},",traderCustomerDto.getTraderCustomerId(), DateUtil.gainNowDate());
                });

        return SUCCESS;
    }
}
