package com.vedeng.erp.trader.domain.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * 终端数据
 */
@Getter
@Setter
public class TerminalDistributionLinkResponseDto {

    /**
     * 终端名称
     */
    private String distributionLinkName;

    private String traderName;

    private Integer traderId;

    private Integer provinceCode;

    private String provinceName;

    private Integer cityCode;

    private String cityName;

    private Integer countyCode;

    private String countyName;

    /**
     * 归属销售
     */
    private String saleUser;

    /**
     * 合作情况
     */
    private String cooperationStatus;

    /**
     * 注册地区
     */
    private String registerArea;

    /**
     * 合作终端
     */
    private String cooperationTerminal;

    /**
     * 最新中标时间
     */
    private String lastBiddingTime;

    /**
     * 和贝登最新交易时间
     */
    private String lastSaleTime;

    /**
     * 是否归属当前销售 0-否 1-是
     */
    private Integer isBelongCurrentSales;

    /**
     * 该经销商是否是直销  1 是  0否
     */
    private Integer directSale;
}