package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.domain.command.KingDeePayCommonCommand;
import com.vedeng.erp.kingdee.dto.KingDeePayCommonDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeePayCommonDetailLinkDto;
import com.vedeng.erp.kingdee.dto.KingDeePayCommonDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 标准应付单关联的源单是采购入库单和采购退料单，同一个接口，两种场景入参，请注意入参参数。未防止错误，下面分两个场景示例。
 * @date
 */
@Mapper(componentModel = "spring")
public interface KingDeePayCommonCommandConvertor extends BaseCommandMapStruct<KingDeePayCommonCommand,KingDeePayCommonDto> {

    @Mapping(target = "FId", source = "FId")
    @Mapping(target = "FDate", source = "FDate")
    @Mapping(target = "f_QZOK_BDDJTID", source = "FQzokBddjtId")
    @Mapping(target = "FBusinessType", source = "FBusinessType")
    @Mapping(target = "FInStockBusType", source = "FInStockBusType")
    @Mapping(target = "FSetAccountType", source = "FSetAccountType")
    @Mapping(target = "FBillTypeID.FNumber", source = "FBillTypeID")
    @Mapping(target = "FSUPPLIERID.FNumber", source = "FSupplierId")
    @Mapping(target = "FSETTLEORGID.FNumber", source = "FSettleOrgId")
    @Mapping(target = "FPAYORGID.FNumber", source = "FPayOrgId")
    @Override
    KingDeePayCommonCommand toCommand(KingDeePayCommonDto dto);

    @Mapping(target = "FPriceQty", source = "FPriceQty")
    @Mapping(target = "FTaxPrice", source = "FTaxPrice")
    @Mapping(target = "FEntryTaxRate", source = "FEntryTaxRate")
    @Mapping(target = "f_QZOK_BDDJHID", source = "f_QZOK_BDDJHID")
    @Mapping(target = "FNoTaxAmountFor_D", source = "FNoTaxAmountFor_D")
    @Mapping(target = "FTaxAmountFor_D", source = "FTaxAmountFor_D")
    @Mapping(target = "FAllAmountFor_D", source = "FAllAmountFor_D")
    @Mapping(target = "FMATERIALID.FNumber", source = "FMaterialId")
    KingDeePayCommonCommand.FEntityDetail toCommand(KingDeePayCommonDetailDto dto);

    @Mapping(target = "FLinkId", source = "FLinkId")
    @Mapping(target = "FEntityDetail_Link_FRuleId", source = "FEntityDetail_Link_FRuleId")
    @Mapping(target = "FEntityDetail_Link_FFlowLineId", source = "FEntityDetail_Link_FFlowLineId")
    @Mapping(target = "FEntityDetail_Link_FSTableId", source = "FEntityDetail_Link_FSTableId")
    @Mapping(target = "FEntityDetail_Link_FSTableName", source = "FEntityDetail_Link_FSTableName")
    @Mapping(target = "FEntityDetail_Link_FSBillId", source = "FEntityDetail_Link_FSBillId")
    @Mapping(target = "FEntityDetail_Link_FSId", source = "FEntityDetail_Link_FSId")
    @Mapping(target = "FEntityDetail_Link_FBASICUNITQTYOld", source = "FEntityDetail_Link_FBASICUNITQTYOld")
    @Mapping(target = "FEntityDetail_Link_FBASICUNITQTY", source = "FEntityDetail_Link_FBASICUNITQTY")
    @Mapping(target = "FEntityDetail_Link_FStockBaseQtyOld", source = "FEntityDetail_Link_FStockBaseQtyOld")
    @Mapping(target = "FEntityDetail_Link_FStockBaseQty", source = "FEntityDetail_Link_FStockBaseQty")
    @Mapping(target = "FEntityDetail_Link_FALLAMOUNTFOR_DOld", source = "FEntityDetail_Link_FALLAMOUNTFOR_DOld")
    @Mapping(target = "FEntityDetail_Link_FALLAMOUNTFOR_D", source = "FEntityDetail_Link_FALLAMOUNTFOR_D")
    KingDeePayCommonCommand.FEntityDetail.FEntityDetailLink toCommand(KingDeePayCommonDetailLinkDto dto);

}
