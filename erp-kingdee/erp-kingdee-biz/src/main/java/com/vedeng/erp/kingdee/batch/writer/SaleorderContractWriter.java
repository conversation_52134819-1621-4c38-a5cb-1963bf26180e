package com.vedeng.erp.kingdee.batch.writer;

import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeeSaleorderContractDto;
import com.vedeng.erp.kingdee.service.KingDeeSaleorderContractApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SaleorderContractWriter extends BaseWriter<KingDeeSaleorderContractDto> {

    @Autowired
    private KingDeeSaleorderContractApiService kingDeeSaleorderContractApiService;
    @Override
    public void doWrite(KingDeeSaleorderContractDto item, JobParameters params, ExecutionContext stepContext) throws Exception {
        item.setKingDeeBizEnums(KingDeeBizEnums.saveSaleorderContract);
        kingDeeSaleorderContractApiService.register(item,true);
    }
}
