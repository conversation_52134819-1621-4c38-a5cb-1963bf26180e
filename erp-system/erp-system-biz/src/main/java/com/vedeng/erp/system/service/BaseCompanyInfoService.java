package com.vedeng.erp.system.service;

import com.vedeng.erp.system.domain.entity.BaseCompanyInfoEntity;

import java.util.List;

public interface BaseCompanyInfoService{

    int deleteByPrimaryKey(Integer id);

    int insert(BaseCompanyInfoEntity record);

    BaseCompanyInfoEntity selectByCompanyName(String companyName);

    BaseCompanyInfoEntity selectByShortName(String shortName);


    List<BaseCompanyInfoEntity> selectByCompanyNames(List<String> companyNames);

    int insertSelective(BaseCompanyInfoEntity record);

    BaseCompanyInfoEntity selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(BaseCompanyInfoEntity record);

    int updateByPrimaryKey(BaseCompanyInfoEntity record);

    List<BaseCompanyInfoEntity> findAll();
}
