package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.batch.dto.BatchSaleorderContractDto;
import com.vedeng.erp.kingdee.domain.entity.KingDeeSaleorderContractEntity;

import java.util.List;

public interface KingDeeSaleorderContractMapper {
    int deleteByPrimaryKey(Integer kingDeeSaleorderContractId);

    int insert(KingDeeSaleorderContractEntity record);

    int insertSelective(KingDeeSaleorderContractEntity record);

    KingDeeSaleorderContractEntity selectByPrimaryKey(Integer kingDeeSaleorderContractId);

    int updateByPrimaryKeySelective(KingDeeSaleorderContractEntity record);

    int updateByPrimaryKey(KingDeeSaleorderContractEntity record);
    Integer getFidByDDH(String saleorderNo);
}