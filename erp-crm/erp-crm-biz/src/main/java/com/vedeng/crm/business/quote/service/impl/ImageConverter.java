package com.vedeng.crm.business.quote.service.impl;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.util.IoUtils;
import com.alibaba.excel.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;

@Slf4j
public class ImageConverter implements Converter<String> {

    @Override
    public WriteCellData<?> convertToExcelData(WriteConverterContext<String> context) throws Exception {
        String urlString = context.getValue();
        if (StringUtils.isBlank(urlString)) {
            return new WriteCellData<>("");
        }
        try {
            URL url = new URL(urlString);
            // 使用 try-with-resources 确保输入流关闭
            try (InputStream inputStream = url.openStream()) {
                
                //获取图片宽高，用以计算比例
                BufferedImage image = ImageIO.read(inputStream);
                int width = image.getWidth();
                int height = image.getHeight();
                
                return TemplateExcelUtils.convertToImageCellData(urlString,1,1);
            }
        } catch (IOException e) {
            log.info("IOException",e);
        } catch (Exception e) {
            log.info("Exception",e);
        }
        return new WriteCellData<>("");
    }
}
