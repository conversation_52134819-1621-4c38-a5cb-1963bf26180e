package com.newtask.ddi.dao;

import com.newtask.ddi.dao.generate.TDdiGeOrderMapper;
import com.newtask.ddi.model.DdiGeOrder;
import com.wms.model.WmsOutSnCodeOrder;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2020/11/19 8:59
 */
@Repository
public interface DdiGeOrderMapper extends TDdiGeOrderMapper {

    void insertSelectiveBatch(List<DdiGeOrder> geOrders);
    /**
     * @description: 根据snCodeOrder查询DDI出库信息
     * @param snCodeOrder
     * @return {@link DdiGeOrder}
     * @throws
     * <AUTHOR>
     * @date 2020/11/19 17:41
     */
    DdiGeOrder getGeOrders(WmsOutSnCodeOrder snCodeOrder);
}
