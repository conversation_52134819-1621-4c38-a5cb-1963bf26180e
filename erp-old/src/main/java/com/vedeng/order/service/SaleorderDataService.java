package com.vedeng.order.service;

import com.vedeng.common.model.ResultAssist;
import com.vedeng.finance.model.SaleorderData;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.vo.BuyorderGoodsVo;

import java.math.BigDecimal;
import java.util.List;

/**
 * <b>Description:</b><br>
 * 销售订单数据
 *
 * <AUTHOR>
 * @Note <b>ProjectName:</b> dbcenter <br>
 * <b>PackageName:</b> com.vedeng.service.common <br>
 * <b>ClassName:</b> SaleorderDataService <br>
 * <b>Date:</b> 2017年10月16日 上午10:18:17
 */
public interface SaleorderDataService {
    /**
     * <b>Description:</b><br>
     * 订单已收款金额(不含账期) --- 业务类型(交易类型1收入4转入)减去（交易类型5转移2支出）
     *
     * @param saleorderId
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年10月16日 上午10:19:57
     */
    BigDecimal getPaymentAmount(Integer saleorderId);

    /**
     * <b>Description:</b><br>
     * 订单已收款金额此方法给EAST用
     *
     * @param saleorderId
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2018年5月29日 下午5:05:10
     */
    BigDecimal getPaymentAndPeriodAmount(Integer saleorderId);

    /**
     * <b>Description:</b><br>
     * 订单对公已收款金额(不含账期) --- 业务类型(交易类型1收入4转入)
     *
     * @param saleorderId
     * @return
     * @Note <b>Author:</b> duke <br>
     * <b>Date:</b> 2017年12月2日 上午11:47:40
     */
    BigDecimal getPubPaymentAmount(Integer saleorderId);

    /**
     * <b>Description:</b><br>
     * 订单账期金额(已收款) --- 已收款账期金额减去退还账期金额
     *
     * @param saleorderId
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年10月23日 上午10:57:30
     */
    BigDecimal getPeriodAmount(Integer saleorderId);

    /**
     * <b>Description:</b><br>
     * 剩余账期未还金额 --- 使用的账期额度减去归还的账期额度减去退还的账期额度
     *
     * @param saleorderId
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年10月16日 上午10:20:11
     */
    BigDecimal getLackAccountPeriodAmount(Integer saleorderId);

    /**
     * <b>Description:</b><br>
     * 销售订单数据
     *
     * @param saleorderId
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年10月16日 上午10:20:38
     */
    SaleorderData getSaleorderData(Integer saleorderId);

    /**
     * <b>Description:</b><br>
     * 订单产品客户收货数量 --- 根据快递单收货状态查询收货数量
     *
     * @param saleorderGoodsId
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年10月17日 下午6:40:51
     */
    Integer getArrivalNum(Integer saleorderGoodsId);

    /**
     * <b>Description:</b><br>
     * 批量查询订单信息
     *
     * @param saleorderIds
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年10月18日 下午4:16:21
     */
    List<SaleorderData> getSaleorderDatas(List<Integer> saleorderIds);

    /**
     * <b>Description:</b><br>
     * 订单金额（总额-退款金额）-- 订单产品数量-退货产品数量-仅退款售后的金额= 计算出订单金额
     *
     * @param saleorderId
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年10月23日 下午2:43:32
     */
    BigDecimal getRealAmount(Integer saleorderId);

    /**
     * <b>Description:</b><br>
     * 订单金额（总额-退货金额）-- 根据资金流水记录计算
     *
     * @param saleOrderIdList
     * @return
     * @Note <b>Author:</b> duke <br>
     * <b>Date:</b> 2018年9月10日 上午11:48:00
     */
    List<ResultAssist> getNewRealAmount(List<Integer> saleOrderIdList, Integer companyId);

    /**
     * <b>Description:</b><br>
     * 销售订单采购总额 --- 根据采购记录查询
     *
     * @param saleorderId
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年10月30日 上午10:08:46
     */
    BigDecimal getBuyAmount(Integer saleorderId);

    /**
     * <b>Description:</b><br>
     * 销售单是否完结 --- 达到条件未完结的改为已完结
     *
     * @param saleorderId
     * @return true是 false否
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年10月31日 下午3:21:48
     */
    Boolean getIsEnd(Integer saleorderId);

    /**
     * <b>Description:</b><br>
     * 获取销售产品已开票数量 --- 开票数量减去退票数量（发票单价加，发票单价负减）
     *
     * @param saleorderGoodsId
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年11月15日 下午5:52:07
     */
    BigDecimal getHaveInvoiceNums(Integer saleorderGoodsId);

    /**
     * <b>Description:</b><br>
     * 订单预付款金额（订单预付款金额-退款金额）-- 订单产品数量减去退货产品数量后，计算出订单预付款金额金额
     *
     * @param saleorderId
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2018年1月30日 下午2:15:44
     */
    BigDecimal getRealPreAmount(Integer saleorderId);

    /**
     * <b>Description:</b><br>
     * 计算订单详情产品ID对应的成本价
     *
     * @param saleorderGoodsId
     * @param deliveryDirect
     * @param companyId
     * @return
     * @Note <b>Author:</b> leo.yang <br>
     * <b>Date:</b> 2018年2月6日 下午8:43:49
     */
    BigDecimal getCostPrice(Integer saleorderGoodsId, Integer deliveryDirect, Integer companyId);

    /**
     * <b>Description:</b><br>
     * 根据交易主体1对公2对私查询订单金额（减去退款的金额）
     *
     * @param saleorderId
     * @param traderSubject 1对公2对私
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2018年4月3日 下午1:21:59
     */
    BigDecimal getAmountByTraderSubject(Integer saleorderId, Integer traderSubject);

    /**
     * <b>Description:</b><br>
     * 根据销售商品ID查询对应的采购单单号集合
     *
     * @param saleorderGoodsId
     * @return
     * @Note <b>Author:</b> duke <br>
     * <b>Date:</b> 2018年4月12日 下午2:54:28
     */
    List<Buyorder> getBuyorderListBySaleorderGoodsId(Integer saleorderGoodsId);


    /**
     * <b>Description:</b><br>
     * 销售订单详情页使用 订单已收款金额 {订单已收款金额 = （业务类型==订单收款）的流水总额-（业务类型==退款 && 交易类型==转出）-支付宝提现的流水总额；}
     *
     * @param saleorderId
     * @return
     * @Note <b>Author:</b> leo.yang <br>
     * <b>Date:</b> 2018年6月26日 下午5:26:17
     */
    BigDecimal getReceivedAmount(Integer saleorderId);

    /**
     * <b>Description:</b><br>
     * 根据销售订单商品ID查询采购订单详情
     *
     * @param saleorderGoodsId
     * @return
     * @Note <b>Author:</b> duke <br>
     * <b>Date:</b> 2018年7月17日 上午10:05:32
     */
    List<BuyorderGoodsVo> getBuyOrderInfoBySaleGoodsId(List<Integer> saleOrderGoodsIdList);

    /**
     * 同getBuyOrderInfoBySaleGoodsId  取消了一个耗时SQL
     *
     * @param saleOrderGoodsIdList
     * @return
     */
    List<BuyorderGoodsVo> getBuyOrderInfoBySaleGoodsIdForAfterSale(List<Integer> saleOrderGoodsIdList);

    /**
     * 查询订单对公收款金额
     *
     * @param saleorderId
     * @param traderSubject
     * @return
     */
    BigDecimal getPublicAmount(Integer saleorderId, Integer traderSubject);

    /* * 判断是否能开票使用
     * 功能描述: 实际已收款金额,对公+银行+（交易客户名称一致）
     * @param: [saleorderId]
     * @return: java.math.BigDecimal
     * @auther: duke.li
     * @date: 2019/3/28 10:11
     */
    BigDecimal getOpenInvoiceOrderAmount(Integer saleorderId, String traderName);

    BigDecimal getRealPreAmountForHcOrder(Integer saleorderId);

    /**
     * 5.订单完结<p>订单完结逻辑在db开票及快递签收中因此可以合并.方法为saleorderDataService.getIsEnd
     * 8.订单开票<p>-自动纸质票开票方法:ST_INVOICE,为航信软件直调db
     * 更新销售单updateDataTime
     *
     * @param operateType 和上述编号一致
     * @Author:strange
     * @Date:10:15 2020-04-06
     */
    void updateSaleOrderDataUpdateTime(Integer orderId, Integer orderDetailId, String operateType);

    /**
     * 获取销售订单实付金额
     *
     * @return
     * <AUTHOR>
     * @Date 4:54 下午 2020/5/23
     * @Param
     **/
    BigDecimal getRealPayAmount(Integer saleorderId);
}
