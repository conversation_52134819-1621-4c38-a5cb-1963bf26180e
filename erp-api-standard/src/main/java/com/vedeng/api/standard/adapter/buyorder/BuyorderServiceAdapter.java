package com.vedeng.api.standard.adapter.buyorder;


import cn.hutool.core.bean.BeanUtil;
import com.vedeng.api.standard.adapter.buyorder.dto.BuyOrderApprovalRequest;
import com.vedeng.api.standard.adapter.buyorder.dto.BuyOrderQueryRequest;
import com.vedeng.api.standard.adapter.buyorder.dto.BuyOrderResponse;
import com.vedeng.api.standard.approval.ApprovalConfig;
import com.vedeng.api.standard.approval.ApprovalExecutor;
import com.vedeng.api.standard.approval.ApprovalResult;
import com.vedeng.api.standard.converter.ResponseConfig;
import com.vedeng.api.standard.converter.ResponseMappingConfig;
import com.vedeng.api.standard.core.AbstractServiceAdapter;
import com.vedeng.api.standard.core.ApiRequest;
import com.vedeng.api.standard.core.BaseResponseCode;
import com.vedeng.api.standard.core.exception.ApiStandardException;
import com.vedeng.api.standard.template.BusinessTemplate;
import com.vedeng.api.standard.template.ParameterConfig;
import com.vedeng.api.standard.validation.rules.BuyOrderExistsRule;
import com.vedeng.api.standard.validation.rules.BuyOrderStatusRule;
import com.vedeng.erp.buyorder.dto.BuyOrderApiDto;
import com.vedeng.erp.buyorder.dto.BuyorderGoodsApiDto;
import com.vedeng.erp.buyorder.service.BuyorderApiService;
import com.vedeng.erp.saleorder.dto.SaleOrderGoodsDetailDto;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import com.vedeng.erp.saleorder.service.SaleOrderGoodsApiService;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.vo.BuyorderVo;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.TaskService;
import org.activiti.engine.task.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 采购单服务适配器
 * 实现采购单相关的标准化API接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-18
 */
@Component("buyorderServiceAdapter")
public class BuyorderServiceAdapter extends AbstractServiceAdapter {


    @Autowired
    private BusinessTemplate businessTemplate;

    @Autowired
    private SaleOrderApiService saleOrderApiService;
    @Autowired
    private SaleOrderGoodsApiService saleOrderGoodsApiService;

    @Autowired
    private ApprovalExecutor approvalExecutor;

    @Autowired
    private ProcessEngine processEngine;

    @Autowired
    private BuyorderApiService buyorderApiService;

    @Override
    public Object postProcess(String action, ApiRequest request, Object result) {
        return super.postProcess(action, request, result);
    }

    @Override
    public String getModuleName() {
        return "buyorder";
    }

    /**
     * 获取不需要身份认证的操作列表
     * <p>
     * 采购单模块中，查询操作通常不需要身份认证，
     * 允许外部系统或匿名用户查询采购单信息
     *
     * @return 不需要认证的操作名称数组
     */
    @Override
    public String[] getNoAuthActions() {
        return new String[]{"query", "updateBuyOrderPrices"};
    }

    /**
     * 注册操作处理器
     * 使用简单的映射机制替代 switch 语句
     */
    @Override
    protected void registerOperationHandlers() {
        // 注册支持的操作处理器（使用支持异常的版本）
        registerThrowingHandler("create", this::executeCreateOperation);
        registerThrowingHandler("update", this::executeUpdateOperation);
        registerThrowingHandler("query", this::executeQueryOperation);
        registerThrowingHandler("submit", this::executeSubmitOperation);
        registerThrowingHandler("approve", this::executeApproveOperation);
        registerThrowingHandler("updateBuyOrderPrices", this::executeUpdateBuyOrderPricesOperation);

        logger.info("采购单服务适配器注册完成，支持{}个操作", getHandlerCount());
    }

    /**
     * 执行创建操作
     * 使用BusinessTemplate统一幂等性处理（包括自动构建上下文）
     */
    private Object executeCreateOperation(ApiRequest request) throws Exception {

        ResponseMappingConfig responseConfig = ResponseConfig.create("采购单创建成功", "buyorderId");

        // 1. 构建请求对象
        BuyorderVo buyorderVo = new BuyorderVo();
        BeanUtil.fillBeanWithMap(request.getData(), buyorderVo, true);
        buyorderVo = convertToBuyorderVo(buyorderVo);

        logger.info("BuyorderVo转换成功，准备调用Controller: buyorderId={}, saleorderId={}, saleorderGoodsIds={}",
                buyorderVo.getBuyorderId(), buyorderVo.getSaleorderId(), buyorderVo.getSaleorderGoodsIds());

        // 2. 使用BusinessTemplate执行，框架自动处理一切幂等性逻辑
        return businessTemplate.<BuyorderVo, BuyOrderResponse>executeCreate(request)
                .requestType(BuyorderVo.class)
                .responseType(BuyOrderResponse.class)
                .withIdempotencyHandling("BUY_ORDER_CREATE", "buyorderId", "buyorderNo") // 框架自动生成规则、构建上下文、处理异常
                .controller("newBuyorderController", "saveAddBuyorder")
                .withHttpParameters(ParameterConfig.of(BuyorderVo.class, buyorderVo))
                .responseConfig(responseConfig)
                .execute();
    }

    /**
     * 执行更新操作
     */
    private Object executeUpdateOperation(ApiRequest request) {
        ResponseMappingConfig responseConfig = ResponseConfig.update("采购单更新成功");

        try {
            // 转换为Buyorder对象
            Buyorder buyorder = new Buyorder();
            BeanUtil.fillBeanWithMap(request.getData(), buyorder, true);

            logger.info("Buyorder转换成功，准备调用Controller: buyorderId={}", buyorder.getBuyorderId());

            // 提取商品明细数组参数
            Map<String, Object> requestData = request.getData();
            ParameterConfig[] httpParameters = buildHttpParametersWithGoodsArrays(buyorder, requestData);

            return businessTemplate.<Buyorder, BuyOrderResponse>executeUpdate(request)
                    .requestType(Buyorder.class)
                    .responseType(BuyOrderResponse.class)
                    .validationRules(BuyOrderExistsRule.class)
                    .controller("newBuyorderController", "saveEditBuyorder")
                    .withHttpParameters(httpParameters)
                    .responseConfig(responseConfig)
                    .execute();

        } catch (Exception e) {
            logger.error("执行更新操作失败: requestId={}", request.getRequestId(), e);
            throw ApiStandardException.serviceExecutionError("采购单更新失败: " + e.getMessage(), e);
        }
    }

    /**
     * 执行查询操作
     */
    private Object executeQueryOperation(ApiRequest request) throws Exception {
        // 直接创建并转换请求对象
        Map<String, Object> requestMap = request.getData();
        if (requestMap.get("buyorderId") != null) {
            Integer buyorderId = Integer.parseInt(requestMap.get("buyorderId").toString());
            return businessTemplate.<BuyOrderQueryRequest, BuyOrderResponse>executeQuery(request)
                    .requestType(BuyOrderQueryRequest.class)
                    .responseType(BuyOrderResponse.class)
                    .controller("buyorderApiServiceImpl", "getBuyorderByBuyorderId")
                    .withoutHttpParameters(ParameterConfig.of(Integer.class, buyorderId))
                    .execute();
        } else if (requestMap.get("buyorderNo") != null) {
            String buyorderNo = (String) requestMap.get("buyorderNo");
            return businessTemplate.<BuyOrderQueryRequest, BuyOrderResponse>executeQuery(request)
                    .requestType(BuyOrderQueryRequest.class)
                    .responseType(BuyOrderResponse.class)
                    .controller("buyorderApiServiceImpl", "getBuyorderByBuyorderNo")
                    .withoutHttpParameters(ParameterConfig.of(String.class, buyorderNo))
                    .execute();
        } else {
            throw ApiStandardException.dataConversionError("查询采购单时，buyorderId 和 buyorderNo 不能都为空");
        }
    }

    /**
     * 执行提交审核操作
     */
    private Object executeSubmitOperation(ApiRequest request) throws Exception {
        ResponseMappingConfig responseConfig = ResponseConfig.submit("提交审核成功");
        // 创建Buyorder对象用于Controller方法调用
        Buyorder buyorder = new Buyorder();
        BeanUtil.fillBeanWithMap(request.getData(), buyorder, true);

        // 先查询采购单的审核状态
        BuyOrderApiDto buyOrder = buyorderApiService.getBuyorderByBuyorderId(buyorder.getBuyorderId());
        if (buyOrder == null) {
            throw new ApiStandardException(BaseResponseCode.BUSINESS_ERROR, "采购单[" + buyorder.getBuyorderId() + "]不存在");
        }

        // 检查采购单是否已经审核，VALID_TIME = 1 表示已审核
        if (buyOrder.getValidTime() != null && buyOrder.getValidTime().equals(1L)) {
            throw new ApiStandardException(BaseResponseCode.SUCCESS, "采购单[" + buyorder.getBuyorderId() + "]已审核通过");
        }

        return businessTemplate.<BuyOrderApprovalRequest, BuyOrderResponse>executeUpdate(request)
                .requestType(BuyOrderApprovalRequest.class)
                .responseType(BuyOrderResponse.class)
                .controller("buyorderController", "editApplyValidBuyorder")
                .validationRules(BuyOrderStatusRule.class)
                .withHttpParameters(
                        ParameterConfig.of(Buyorder.class, buyorder),
                        ParameterConfig.of(String.class, "0")
                )
                .withIdempotencyHandling("BUY_ORDER_SUBMIT")
                .responseConfig(responseConfig)
                .execute();
    }

    /**
     * 执行审核操作
     * 使用通用审核框架，提供统一的审核执行能力
     */
    private ApprovalResult executeApproveOperation(ApiRequest request) throws Exception {
        // 从请求数据中获取采购单ID
        Integer buyorderId = Integer.parseInt(request.getData().get("buyorderId").toString());
        if (buyorderId == null) {
            throw new ApiStandardException(BaseResponseCode.PARAMETER_ERROR, "采购单ID不能为空");
        }

        String taskId;
        try {
            // 通过采购单ID查询最新的任务ID
            taskId = getLatestTaskIdByBuyorderId(buyorderId);
        } catch (ApiStandardException e) {
            // 如果采购单已经审核，直接返回成功结果
            if (BaseResponseCode.SUCCESS.equals(e.getCode())) {
                ApprovalResult successResult = new ApprovalResult();
                successResult.setSuccess(true);
                successResult.setMessage(e.getMessage());
                successResult.setData(new BuyOrderResponse());
                return successResult;
            }
            // 其他异常继续抛出
            throw e;
        }

        // 创建BuyOrderApprovalRequest对象并设置参数
        BuyOrderApprovalRequest approvalRequest = new BuyOrderApprovalRequest();
        approvalRequest.setBuyOrderId(buyorderId);
        approvalRequest.setTaskId(taskId);
        approvalRequest.setComment("业务流转单自动审核");
        approvalRequest.setPass(true);

        approvalRequest.validate();

        // 创建审核配置
        ApprovalConfig<BuyOrderApprovalRequest, BuyOrderResponse> config = ApprovalConfig.<BuyOrderApprovalRequest, BuyOrderResponse>builder()
                .requestType(BuyOrderApprovalRequest.class)
                .responseType(BuyOrderResponse.class)
                .controller("buyorderController", "complementTaskForBuyOrder")
                .responseConfig(ResponseConfig.create("审核步骤成功", "stepResult"))
                .withHttpParameters(approvalReq -> new ParameterConfig[]{
                        ParameterConfig.of(String.class, approvalReq.getTaskId()),      // taskId
                        ParameterConfig.of(String.class, approvalReq.getComment()),    // comment
                        ParameterConfig.of(Boolean.class, approvalReq.getPass()),      // pass
                        ParameterConfig.of(Integer.class, approvalReq.getBuyOrderId()), // buyorder
                })
                .build();

        // 使用通用审核执行器 - 循环执行直到审核流程完成
        return approvalExecutor.executeMultiStepApproval(request, approvalRequest, config);
    }

    /**
     * 根据采购单ID获取最新的任务ID
     *
     * @param buyorderId 采购单ID
     * @return 任务ID
     */
    private String getLatestTaskIdByBuyorderId(Integer buyorderId) {
        if (buyorderId == null) {
            throw new ApiStandardException(BaseResponseCode.PARAMETER_ERROR, "采购单ID不能为空");
        }

        // 先查询采购单的审核状态
        BuyOrderApiDto buyOrder = buyorderApiService.getBuyorderByBuyorderId(buyorderId);
        if (buyOrder == null) {
            throw new ApiStandardException(BaseResponseCode.BUSINESS_ERROR, "采购单[" + buyorderId + "]不存在");
        }

        // 检查采购单是否已经审核，VALID_TIME = 1 表示已审核
        if (buyOrder.getValidTime() != null && buyOrder.getValidTime().equals(1L)) {
            throw new ApiStandardException(BaseResponseCode.SUCCESS, "采购单[" + buyorderId + "]已审核通过");
        }

        TaskService taskService = processEngine.getTaskService();
        // 根据业务键查询任务，获取最新的任务ID
        Task taskInfo = taskService.createTaskQuery()
                .processInstanceBusinessKey("buyorderVerify_" + buyorderId)
                .singleResult();

        if (taskInfo != null) {
            return taskInfo.getId();
        }

        throw new ApiStandardException(BaseResponseCode.BUSINESS_ERROR, "未找到采购单[" + buyorderId + "]的审核任务");
    }

    /**
     * 根据销售单信息构建并转换为BuyorderVo对象
     */
    private BuyorderVo convertToBuyorderVo(BuyorderVo createRequest) {
        String saleorderNo = createRequest.getSaleorderNo();

        SaleorderInfoDto saleorderInfoDto = saleOrderApiService.getBySaleOrderNo(saleorderNo);
        Integer saleorderId = saleorderInfoDto.getSaleorderId();

        logger.info("开始构建采购单参数并转换为BuyorderVo: saleorderId={}", saleorderId);

        try {
            // 1. 获取销售单信息
            SaleorderInfoDto saleOrder = saleOrderApiService.getBySaleOrderId(saleorderId);
            if (saleOrder == null) {
                throw ApiStandardException.dataConversionError("销售单不存在: " + saleorderId);
            }
            logger.info("获取销售单信息成功: saleorderNo={}, traderId={}", saleOrder.getSaleorderNo(), saleOrder.getTraderId());

            // 2. 获取销售商品列表
            List<SaleOrderGoodsDetailDto> saleorderGoodsList = saleOrderGoodsApiService.getBySaleorderId(saleorderId);
            if (saleorderGoodsList == null || saleorderGoodsList.isEmpty()) {
                throw ApiStandardException.dataConversionError("销售单下没有商品: " + saleorderId);
            }
            logger.info("获取销售商品列表成功: 商品数量={}", saleorderGoodsList.size());

            // 4. 构建 saleorderGoodsIds 和 goodsIds 字符串
            StringBuilder saleorderGoodsIdsBuilder = new StringBuilder();
            StringBuilder goodsIdsBuilder = new StringBuilder();

            for (SaleOrderGoodsDetailDto goods : saleorderGoodsList) {
                // 构建 goodsId|saleorderGoodsId 格式
                saleorderGoodsIdsBuilder.append(goods.getGoodsId()).append("|").append(goods.getSaleorderGoodsId()).append(",");

                // 构建 goodsIds 字符串
                goodsIdsBuilder.append(goods.getGoodsId()).append(",");
            }

            String saleorderGoodsIds = saleorderGoodsIdsBuilder.toString();
            String goodsIds = goodsIdsBuilder.toString();

            // 5. 创建并设置BuyorderVo对象
            BuyorderVo buyorderVo = new BuyorderVo();

            // 设置订单类型（从销售单获取，默认为0-销售单）
            buyorderVo.setOrderType(0);
            // 设置发货方式
            buyorderVo.setDeliveryDirect(1);

            // 设置基本属性
            buyorderVo.setSaleorderId(saleorderId);
            buyorderVo.setIsIgnore(0);

            // 设置商品相关信息（这是关键字段，checkBuyorderSpecial方法需要）
            buyorderVo.setSaleorderGoodsIds(saleorderGoodsIds);
            buyorderVo.setGoodsIds(goodsIds);

            logger.info("转换为BuyorderVo成功: saleorderId={}, orderType={}, deliveryDirect={}, saleorderGoodsIds={}, goodsIds={}", buyorderVo.getSaleorderId(), buyorderVo.getOrderType(), buyorderVo.getDeliveryDirect(), buyorderVo.getSaleorderGoodsIds(), buyorderVo.getGoodsIds());

            // 6. 验证关键字段
            if (buyorderVo.getSaleorderGoodsIds() == null || buyorderVo.getSaleorderGoodsIds().trim().isEmpty()) {
                throw ApiStandardException.dataConversionError("销售商品ID列表不能为空");
            }

            return buyorderVo;

        } catch (Exception e) {
            logger.error("构建采购单参数并转换为BuyorderVo失败: saleorderId={}", saleorderId, e);
            throw ApiStandardException.dataConversionError("构建采购单参数并转换为BuyorderVo失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建包含商品明细数组参数的HTTP参数配置
     * 将API请求数据中的商品明细字段转换为旧业务逻辑期望的数组格式
     *
     * @param buyorder    采购单对象
     * @param requestData API请求数据
     * @return HTTP参数配置数组
     */
    private ParameterConfig[] buildHttpParametersWithGoodsArrays(Buyorder buyorder, Map<String, Object> requestData) {
        logger.info("开始构建商品明细数组参数: buyorderId={}", buyorder.getBuyorderId());

        // 基础参数：采购单对象
        ParameterConfig buyorderParam = ParameterConfig.of(Buyorder.class, buyorder);

        // 构建商品明细数组参数
        List<ParameterConfig> paramList = new ArrayList<>();
        paramList.add(buyorderParam);

        // 处理单值参数（与原方法保持一致）
        addSingleParameterIfExists(paramList, requestData, "firstSendReceiveFlag", "firstSendReceiveFlag");
        addSingleParameterIfExists(paramList, requestData, "traderContactStr", "traderContactStr");
        addSingleParameterIfExists(paramList, requestData, "traderContactStrAct", "traderContactStrAct");
        addSingleParameterIfExists(paramList, requestData, "traderAddressStr", "traderAddressStr");
        addSingleParameterIfExists(paramList, requestData, "takeAddressId", "takeAddressId");

        // 处理各种商品明细参数数组
        addArrayParameterIfExists(paramList, requestData, "buySum", "buySum");
        addArrayParameterIfExists(paramList, requestData, "price", "price");
        addArrayParameterIfExists(paramList, requestData, "isGiftGoods", "isGiftGoods");
        addArrayParameterIfExists(paramList, requestData, "sendGoodsTimeStr", "sendGoodsTimeStr");
        addArrayParameterIfExists(paramList, requestData, "receiveGoodsTimeStr", "receiveGoodsTimeStr");
        addArrayParameterIfExists(paramList, requestData, "deliveryCycle", "deliveryCycle");
        addArrayParameterIfExists(paramList, requestData, "isHaveAuth", "isHaveAuth");
        addArrayParameterIfExists(paramList, requestData, "goodsComments", "goodsComments");
        addArrayParameterIfExists(paramList, requestData, "installation", "installation");
        addArrayParameterIfExists(paramList, requestData, "insideComments", "insideComments");
        addArrayParameterIfExists(paramList, requestData, "referPrice", "referPrice");
        addArrayParameterIfExists(paramList, requestData, "dbBuyNum", "dbBuyNum");

        logger.info("构建商品明细数组参数完成: 参数数量={}", paramList.size());
        return paramList.toArray(new ParameterConfig[0]);
    }

    /**
     * 添加数组参数到参数列表中
     * 将字符串格式的商品明细数据转换为数组参数
     * 支持处理List<String>和String[]两种类型
     *
     * @param paramList   参数列表
     * @param requestData 请求数据
     * @param sourceKey   源字段名
     * @param targetKey   目标参数名
     */
    private void addArrayParameterIfExists(List<ParameterConfig> paramList, Map<String, Object> requestData,
                                           String sourceKey, String targetKey) {
        Object value = requestData.get(sourceKey);
        if (value != null) {
            String[] arrayValue = null;

            // 处理不同类型的参数值
            if (value instanceof List) {
                // 处理List<String>类型（来自JSON反序列化）
                @SuppressWarnings("unchecked")
                List<String> listValue = (List<String>) value;
                if (!listValue.isEmpty()) {
                    arrayValue = listValue.toArray(new String[0]);
                }
            } else if (value instanceof String[]) {
                // 处理String[]类型（直接传入的数组）
                String[] stringArrayValue = (String[]) value;
                if (stringArrayValue.length > 0) {
                    arrayValue = stringArrayValue;
                }
            } else if (value instanceof String) {
                // 处理字符串类型（逗号分隔）
                String stringValue = value.toString().trim();
                if (!stringValue.isEmpty()) {
                    arrayValue = convertToParameterArray(stringValue);
                }
            }

            // 添加数组参数
            if (arrayValue != null && arrayValue.length > 0) {
                ParameterConfig arrayParam = ParameterConfig.requestParam(targetKey, arrayValue);
                paramList.add(arrayParam);
                logger.debug("添加数组参数: {}={} (长度: {})", targetKey, arrayValue, arrayValue.length);
            }
        }
    }

    /**
     * 添加单值参数到参数列表中
     * 处理请求数据中的单个参数值
     *
     * @param paramList   参数列表
     * @param requestData 请求数据
     * @param sourceKey   源字段名
     * @param targetKey   目标参数名
     */
    private void addSingleParameterIfExists(List<ParameterConfig> paramList, Map<String, Object> requestData,
                                            String sourceKey, String targetKey) {
        Object value = requestData.get(sourceKey);
        if (value != null) {
            String stringValue = value.toString().trim();
            if (!stringValue.isEmpty()) {
                ParameterConfig param = ParameterConfig.requestParam(targetKey, stringValue);
                paramList.add(param);
                logger.debug("添加单值参数: {}={}", targetKey, stringValue);
            }
        }
    }

    /**
     * 将商品明细字符串转换为参数数组
     * 处理格式如："1001|100,1002|200" -> ["1001|100", "1002|200"]
     *
     * @param valueString 商品明细字符串
     * @return 参数数组
     */
    private String[] convertToParameterArray(String valueString) {
        if (valueString == null || valueString.trim().isEmpty()) {
            return null;
        }

        // 按逗号分割，每个部分包含 "商品ID|值" 格式
        String[] parts = valueString.split(",");
        List<String> result = new ArrayList<>();

        for (String part : parts) {
            part = part.trim();
            if (!part.isEmpty() && part.contains("|")) {
                result.add(part);
            }
        }

        return result.isEmpty() ? null : result.toArray(new String[0]);
    }

    /**
     * 执行更新采购单价格操作
     * 使用BusinessTemplate统一模板调用 buyorderApiService.updateBuyorderGoodsPrices 方法
     */
    private Object executeUpdateBuyOrderPricesOperation(ApiRequest request) throws Exception {
        ResponseMappingConfig responseConfig = ResponseConfig.update("采购单价格更新成功");

        try {
            Map<String, Object> requestData = request.getData();

            // 获取采购单ID
            Integer buyorderId = null;
            if (requestData.get("buyorderId") != null) {
                buyorderId = Integer.parseInt(requestData.get("buyorderId").toString());
            }

            if (buyorderId == null) {
                throw ApiStandardException.dataConversionError("采购单ID不能为空");
            }

            // 获取商品价格列表
            List<BuyorderGoodsApiDto> goodsList = new ArrayList<>();
            Object goodsListObj = requestData.get("goodsList");

            if (goodsListObj instanceof List) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> goodsListMaps = (List<Map<String, Object>>) goodsListObj;

                for (Map<String, Object> goodsMap : goodsListMaps) {
                    BuyorderGoodsApiDto goods = new BuyorderGoodsApiDto();

                    if (goodsMap.get("buyorderGoodsId") != null) {
                        goods.setBuyorderGoodsId(Integer.parseInt(goodsMap.get("buyorderGoodsId").toString()));
                    }

                    if (goodsMap.get("price") != null) {
                        goods.setPrice(new BigDecimal(goodsMap.get("price").toString()));
                    }

                    if (goodsMap.get("num") != null) {
                        goods.setNum(Integer.parseInt(goodsMap.get("num").toString()));
                    }

                    goodsList.add(goods);
                }
            }

            if (goodsList.isEmpty()) {
                throw ApiStandardException.dataConversionError("商品价格列表不能为空");
            }

            logger.info("准备更新采购单价格: buyorderId={}, 商品数量={}", buyorderId, goodsList.size());

            // 使用BusinessTemplate统一模板调用
            return businessTemplate.<Object, BuyOrderResponse>executeUpdate(request)
                    .requestType(Object.class)
                    .responseType(BuyOrderResponse.class)
                    .controller("buyorderApiServiceImpl", "updateBuyorderGoodsPrices")
                    .withoutHttpParameters(
                            ParameterConfig.of(Integer.class, buyorderId),
                            ParameterConfig.of(List.class, goodsList)
                    )
                    .responseConfig(responseConfig)
                    .execute();

        } catch (Exception e) {
            logger.error("执行更新采购单价格操作失败: requestId={}", request.getRequestId(), e);
            throw ApiStandardException.serviceExecutionError("采购单价格更新失败: " + e.getMessage(), e);
        }
    }


}
