package com.vedeng.logistics.dao.ext;

import com.vedeng.logistics.dao.GoodsAcceptanceReportMapper;
import com.vedeng.logistics.model.vo.GoodsAcceptanceReportDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface GoodsAcceptanceReportExtMapper extends GoodsAcceptanceReportMapper {
    List<GoodsAcceptanceReportDto>  getAcceptanceReportList(@Param("orderNo") String orderNo, @Param("attachmentType") Integer attachmentType, @Param("attachmentFunction") Integer attachmentFunction);
}