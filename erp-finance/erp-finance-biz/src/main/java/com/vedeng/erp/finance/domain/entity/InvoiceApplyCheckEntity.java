package com.vedeng.erp.finance.domain.entity;

import java.util.Date;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 发票规则检验不通过原因表
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class InvoiceApplyCheckEntity extends BaseEntity {
    /**
    * 主键
    */
    private Long invoiceApplyCheckId;

    /**
    * 开票申请ID
    */
    private Integer invoiceApplyId;

    /**
    * 申请检验不通过规则编码（英文逗号分割）
    */
    private String applyNoPassRuleCode;

    /**
    * 开票检验不通过规则编码（英文逗号分割）
    */
    private String openNoPassRuleCode;

    /**
    * 是否删除 0否 1是
    */
    private Integer isDelete;

    /**
    * 更新备注
    */
    private String updateRemark;
}