<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="tags" tagdir="/WEB-INF/tags"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
<%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>

<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='<%= basePath %>static/js/order/saleorder/hc_order_details.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src='<%= basePath %>static/js/order/saleorder/saleorder_common.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src='<%= basePath %>static/js/order/saleorder/saleorder_invoice_common.js?rnd=${resourceVersionKey}'></script>
<%--<!DOCTYPE html">--%>
    <title>医疗器械注册证详情</title>
    <link rel="stylesheet"
          href="${pageContext.request.contextPath}/static/new/css/common/global.css">
    <link rel="stylesheet"
          href="${pageContext.request.contextPath}/static/new/css/pages/firstengage/first/view.css?rnd=${resourceVersionKey}">
    <style type="text/css">
        a.printAtta {
            margin-right: 30px;
        }
        .vd-icon{
            background: none;
        }
    </style>

<div class="detail-wrap">
        <div class="detail-title">${firstEngage.registration.registrationNumber }</div>
        <div class="detail-option-wrap">
            <div class="option-btns">
                <c:if test="${firstEngage.status ne 1 and firstEngage.hasEditAuth}">
                    <a href="./add.do?firstEngageId=${firstEngage.firstEngageId }" class="btn btn-blue btn-small">编辑</a>
                    <a class="btn btn-small J-del" data-id="${firstEngage.firstEngageId }">删除</a>

                    <c:if test="${firstEngage.registration.dealStatus eq 1}">
                    <!-- <a class="btn btn-small J-overday" data-id="${firstEngage.registrationNumberId }">过期处理</a> -->
                    </c:if>

                    <div>
                        <c:if test="${firstEngage.status eq 3 and firstEngage.signature eq 2}">
                            <a  class="btn btn-red btn-small J-audit3"  data-type="${firstEngage.status}" data-t="0" data-id="${firstEngage.firstEngageId}">电子签章</a>
                        </c:if>

                        <c:if test="${firstEngage.signature eq 2}">
                            <div class="tip-wrap">
                                <i class="vd-icon icon-info2">
                                </i>
                            </div>
                        </c:if>
                    </div>

                </c:if>

                <c:if test="${firstEngage.status eq 5 and firstEngage.hasEditAuth}">
                    <%--<a class="btn btn-small J-submitCheck" data-type="1" data-id="${firstEngage.firstEngageId }">提交审核</a>--%>
                    <div class="option-select-wrap J-option-select-wrap" data-id="${firstEngage.firstEngageId }">
                        <div class="option-select-btn J-option-select-icon">提交审核</div>
                        <div class="option-select-icon J-option-select-icon">
                            <i class="vd-icon icon-down" style="height: 21px;width: 12px"></i>
                        </div>
                        <div class="option-select-list">
                            <div class="option-select-item J-submitCheck" data-type="1" data-signature="0" data-id = "${firstEngage.firstEngageId}">提交审核</div>
                            <div class="option-select-item J-submitCheck-e" data-type="1" data-signature="1" data-id = "${firstEngage.firstEngageId}">提交审核并电子签章</div>
                        </div>
                    </div>
                </c:if>

                <c:if test="${firstEngage.status eq 1}">
                    <c:choose>
                        <c:when test="${firstEngage.hasCheckAuth}">
                            <a class="btn btn-small J-audit" data-type="3" data-id="${firstEngage.firstEngageId }">审核通过</a>
                            <a class="btn btn-small J-audit" data-type="2" data-id="${firstEngage.firstEngageId }">审核不通过</a>
                        </c:when>
                        <c:otherwise>
                            <a class="btn btn-small" style="color: grey">已申请审核</a>
                        </c:otherwise>
                    </c:choose>
                </c:if>
            </div>
        </div>
        <div class="detail-block">
            <div class="block-title">注册证信息</div>
            <div class="detail-table">
            <div class="table-item">
                <div class="table-th">
                    <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                    注册证号/备案凭证号：
                </div>
                <div class="table-td">${firstEngage.registration.registrationNumber }</div>
            </div>

            <div class="table-item">
                <div class="table-th">
                    <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                    注册人/备案人名称：
                </div>
                <div class="table-td">
                    ${firstEngage.registration.productCompany.productCompanyChineseName }
                </div>
            </div>

            <div class="table-item">
                <div class="table-th">注册人/备案人住所：</div>
                <div class="table-td">
                    <c:if test="${null ne firstEngage.registration.productCompany.productCompanyAddress and '' ne firstEngage.registration.productCompany.productCompanyAddress}">
                        ${firstEngage.registration.productCompany.productCompanyAddress}
                    </c:if>
                </div>
            </div>

            <div class="table-item">
                <div class="table-th">生产地址：</div>
                <div class="table-td">
                    <c:if test="${null ne firstEngage.registration.productionAddress and '' ne firstEngage.registration.productionAddress}">
                        ${firstEngage.registration.productionAddress}
                    </c:if>
                </div>
            </div>
            <div class="table-item">
                <div class="table-th">代理人名称：</div>
                <div class="table-td">
                    <c:if test="${null ne firstEngage.registration.registeredAgent and '' ne firstEngage.registration.registeredAgent}">
                        ${firstEngage.registration.registeredAgent}
                    </c:if>
                </div>
            </div>
            <div class="table-item">
                <div class="table-th">代理人住所：</div>
                <div class="table-td">
                    <c:if test="${null ne firstEngage.registration.registeredAgentAddress and '' ne firstEngage.registration.registeredAgentAddress}">
                        ${firstEngage.registration.registeredAgentAddress}
                    </c:if>
                </div>
            </div>
            <div class="table-item">
                <div class="table-th">
                    <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                    产品名称（注册证/备案凭证）：
                </div>
                <div class="table-td">
                    <c:if test="${null ne firstEngage.registration.productChineseName and '' ne firstEngage.registration.productChineseName}">
                        ${firstEngage.registration.productChineseName}
                    </c:if>
                </div>
            </div>
            <div class="table-item">
                <div class="table-th">
                    <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                    管理类别：
                </div>
                <div class="table-td">
                    <c:choose>
                        <c:when test="${firstEngage.registration.manageCategoryLevel eq 968 }">
                            一类医疗器械
                        </c:when>
                        <c:when test="${firstEngage.registration.manageCategoryLevel eq 969 }">
                            二类医疗器械
                        </c:when>
                        <c:when test="${firstEngage.registration.manageCategoryLevel eq 970 }">
                            三类医疗器械
                        </c:when>
                        <c:otherwise>
                        </c:otherwise>
                    </c:choose>
                </div>
            </div>
            <div class="table-item">
                <div class="table-th">
                    <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                    规格、型号（注册证/备案凭证）：
                </div>
                <div class="table-td">
                    <c:if test="${null ne firstEngage.registration.model and '' ne firstEngage.registration.model}">
                        ${firstEngage.registration.model}
                    </c:if>
                </div>
            </div>

            <div class="table-item">
                <div class="table-th">结构及组成/主要组成成分/产品描述：</div>
                <div class="table-td">
                    <c:if test="${null ne firstEngage.registration.proPerfStruAndComp and '' ne firstEngage.registration.proPerfStruAndComp}">
                        ${firstEngage.registration.proPerfStruAndComp}
                    </c:if>
                </div>
            </div>

            <div class="table-item">
                <div class="table-th">适用范围/预期用途：</div>
                <div class="table-td">
                    <c:if test="${null ne firstEngage.registration.productUseRange and '' ne firstEngage.registration.productUseRange}">
                        ${firstEngage.registration.productUseRange}
                    </c:if>
                </div>
            </div>
            <div class="table-item">
                <div class="table-th">附件：</div>
                <div class="table-td">
                    <c:if test="${null ne firstEngage.registration.attachment and '' ne firstEngage.registration.attachment}">
                        ${firstEngage.registration.attachment}
                    </c:if>
                </div>
            </div>
            <div class="table-item">
                <div class="table-th">产品存储条件及有效期：</div>
                <div class="table-td">
                    <c:if test="${null ne firstEngage.registration.storageAndExpiryDate and '' ne firstEngage.registration.storageAndExpiryDate}">
                        ${firstEngage.registration.storageAndExpiryDate}
                    </c:if>
                </div>
            </div>
            <div class="table-item">
                <div class="table-th">其他内容：</div>
                <div class="table-td">
                    <c:if test="${null ne firstEngage.registration.otherContents and '' ne firstEngage.registration.otherContents}">
                        ${firstEngage.registration.otherContents}
                    </c:if>
                </div>
            </div>
            <div class="table-item">
                <div class="table-th">备注：</div>
                <div class="table-td">
                    <c:if test="${null ne firstEngage.registration.remarks and '' ne firstEngage.registration.remarks}">
                        ${firstEngage.registration.remarks}
                    </c:if>
                </div>
            </div>

            <div class="table-item">
                <div class="table-th">审批部门/备案部门：</div>
                <div class="table-td">
                    <c:if test="${null ne firstEngage.registration.approvalDepartment and '' ne firstEngage.registration.approvalDepartment}">
                        ${firstEngage.registration.approvalDepartment}
                    </c:if>
                </div>
            </div>

            <div class="table-item">
                <div class="table-th">
                    <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                    批准日期/备案日期：
                </div>
                <div class="table-td">
                    <c:if test="${null ne firstEngage.registration.issuingDateStr and '' ne firstEngage.registration.issuingDateStr}">
                        ${firstEngage.registration.issuingDateStr }
                    </c:if>
                </div>
            </div>

            <div class="table-item">
                <div class="table-th">
                    有效期至：
                </div>
                <div class="table-td">
                    <c:choose>
                        <c:when test="${firstEngage.registration.manageCategoryLevel eq 969 || firstEngage.registration.manageCategoryLevel eq 970}">
                            <c:if test="${null ne firstEngage.registration.effectiveDateStr and '' ne firstEngage.registration.effectiveDateStr}">
                                ${firstEngage.registration.effectiveDateStr}
                            </c:if>
                        </c:when>
                        <c:otherwise>
                            无限期
                        </c:otherwise>
                    </c:choose>

                </div>
            </div>

            <div class="table-item">
                <div class="table-th">变更情况：</div>
                <div class="table-td">
                    <c:if test="${null ne firstEngage.registration.changeContents and '' ne firstEngage.registration.changeContents}">
                        ${firstEngage.registration.changeContents}
                    </c:if>
                </div>
            </div>

            <div class="table-item">
                <div class="table-th">
                    <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                    国标分类：
                </div>
                <div class="table-td">

                    <c:if test="${1 eq firstEngage.standardCategoryType}">
                        新国标&nbsp;&nbsp;
                    </c:if>
                    <c:if test="${2 eq firstEngage.standardCategoryType}">
                        旧国标&nbsp;&nbsp;
                    </c:if>
                    <c:if test="${3 eq firstEngage.standardCategoryType}">
                        二者都是&nbsp;&nbsp;
                    </c:if>
                    <c:if test="${2 eq firstEngage.standardCategoryType || 3 eq firstEngage.standardCategoryType}">
                    <c:choose>
                        <c:when test="${null ne firstEngage.oldStandardCategoryName and '' ne firstEngage.oldStandardCategoryName}">
                            ${firstEngage.oldStandardCategoryName}&nbsp;&nbsp;
                        </c:when>
                        <c:otherwise>
                        </c:otherwise>
                    </c:choose>
                    </c:if>
                    <c:if test="${1 eq firstEngage.standardCategoryType || 3 eq firstEngage.standardCategoryType}">
                    <c:if test="${null ne firstEngage.newStandardCategoryName and '' ne firstEngage.newStandardCategoryName}">
                        ${firstEngage.newStandardCategoryName}&nbsp;&nbsp;
                    </c:if>
                    </c:if>
                </div>
            </div>

                <div class="table-item">
                    <div class="table-th">
                        <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                        注册证附件/备案凭证附件：
                    </div>
                    <div class="table-td">
                        <div class="info-pic">
                            <c:if test="${not empty firstEngage.registration.zczAttachments }">
                                <c:forEach var="attachments" items="${firstEngage.registration.zczAttachments }">
                                    <div class="info-pic-item J-show-big"
                                         data-src="${api_http}${attachments.domain }${attachments.uri }">
                                        <img style="width:100%;height:100%;margin-left: 13px"
                                             src="${api_http}${attachments.domain }${attachments.uri }" alt="">
                                    </div>
                                    <a class="printAtta" href="javascript:;">打印</a>
                                </c:forEach>
                            </c:if>

                            <c:if test="${not empty firstEngage.registration.zcZBAttachments }">
                                <c:forEach var="attachments" items="${firstEngage.registration.zcZBAttachments }">
                                    <div class="info-pic-item J-show-big"
                                         data-src="${api_http}${attachments.domain }${attachments.uri }">
                                        <img style="width:100%;height:100%"
                                             src="${api_http}${attachments.domain }${attachments.uri }" alt="">
                                    </div>
                                    <a class="printAtta" href="javascript:;">打印（贝）</a>
                                </c:forEach>
                            </c:if>
                        </div>
                    </div>
                </div>
                <div class="table-item">
                    <div class="table-th">
                        <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                        注册证源文件/备案凭证源文件：
                    </div>
                    <div class="table-td">
                        <div>
                            <c:if test="${not empty firstEngage.registration.zczyAttachments }">
                                <c:forEach var="attachments" items="${firstEngage.registration.zczyAttachments }"
                                           begin="0" varStatus="i">
                                    <div style="line-height: 30px">
                                        <a target="_blank" href="http://${attachments.domain }${attachments.uri}">注册证/备案凭证源文件
                                            - ${i.index + 1}</a><br>
                                    </div>
                                </c:forEach>
                            </c:if>
                        </div>
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-th">
                        标签样稿附件：
                    </div>
                    <div class="table-td">
                        <div class="info-pic">
                            <c:if test="${not empty firstEngage.registration.labelAttachments }">
                                <c:forEach var="attachments" items="${firstEngage.registration.labelAttachments }">
                                    <div class="info-pic-item J-show-big"
                                         data-src="${api_http}${attachments.domain }${attachments.uri }">
                                        <img style="width:100%;height:100%;margin-left: 13px"
                                             src="${api_http}${attachments.domain }${attachments.uri }" alt="">
                                    </div>
                                    <a class="printAtta" href="javascript:;">打印</a>
                                </c:forEach>
                            </c:if>
                        </div>
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-th">
                    </div>
                    <div class="table-td">
                    </div>
                </div>
        </div>

<%--            生产企业 --%>
        <div class="detail-block" id="manufacturerParent">
            <div class="manufacturerClass" id="manufacturer"></div>

            <div class="manufacturerWeituoClass" id="manufacturerWeituo" ></div>
        </div>

        <c:if test="${empty attachmentHistory}">
        <div class="detail-block">
            <div class="block-title">资质文件修改记录</div>
            <div class="status-title">
                <div class="status-item">修改时间</div>
                <div class="status-item">修改人</div>
                <div class="status-item">修改内容</div>
            </div>
            <div class="status-list">
                <div style="width:100%;line-height: 40px;text-align: center;">暂无资质修改记录</div>
            </div>
        </div>
        </c:if>
        <c:if test="${not empty attachmentHistory}">
        <div class="detail-block">
            <div class="block-title">资质文件修改记录</div>
            <div class="status-title">
                <div class="status-item">修改时间</div>
                <div class="status-item">修改人</div>
                <div class="status-item">修改内容</div>
            </div>
            <div class="status-list">
                <c:forEach items="${attachmentHistory}" var="ah" varStatus="hindex">
                    <c:if test="${hindex.count==5}">
                        <div class="status-more J-optional-more">
                    </c:if>
                    <div class="status-cnt">
                        <div class="status-item">
                            <date:date value="${ah.addTime}" />
                        </div>
                        <div class="status-item">${ah.userName}</div>
                        <div class="status-item-spe">
                            <div style="display: block">
                                更新：<span style="color: red">${ah.titleStr}</span>
                            </div>
                            <div style="display: block;margin-top: 15px">
                                <span>原文件：</span>
                                <c:forEach items="${ah.attachments}" var="a">
                                    <a style="padding: 7px" href="http://${a.domain}${a.uri}" target="_blank">
                                            <img style="width:50px;height:50px"
                                                 src="${api_http}${a.domain }${a.uri }" alt="">
                                    </a>
                                </c:forEach>
                            </div>
                        </div>
                    </div>
                    <c:if test="${attachmentHistory.size()== hindex.count && hindex.count>=5 }">
                        </div>
                    </c:if>
                </c:forEach>

                <c:if test="${fn:length(attachmentHistory) gt 4}">
            </div>
            <div class="detail-optional J-toggle-show">
                <span class="toggle-txt J-more">展开更多<i class="vd-icon icon-down" style="background: none;"></i></span>
                <span class="toggle-txt J-less" style="display: none;">收起<i class="vd-icon icon-up" style="background: none;"></i></span>
            </div>

                </c:if>
        </div>
        </c:if>
        <div class="detail-block">
        <div class="block-title">审核记录</div>
        <div class="status-title">
            <div class="status-item">操作时间</div>
            <div class="status-item">操作人</div>
            <div class="status-item">操作事项</div>
            <div class="status-item">备注</div>
            <div class="status-item">审核状态</div>
        </div>

        <div class="status-list">
            <c:choose>
                <c:when test="${not empty newLogCheckList}">
                    <c:forEach items="${newLogCheckList}" var="log" varStatus="index">
                        <c:if test="${index.count==5}">
                            <div class="status-more J-optional-more">
                        </c:if>
                        <div class="status-cnt">
                            <div class="status-item"><fmt:formatDate value="${log.addTime}" pattern="yyyy-MM-dd HH:mm:ss" /></div>
                            <div class="status-item">${log.creatorName}</div>
                            <div class="status-item">${log.logStatusName}</div>
                            <div class="status-item">${log.logMessage}</div>
                            <div class="status-item">
                                <c:choose>
                                    <c:when test="${log.logStatus == 1}">
                                        审核中
                                    </c:when>
                                    <c:when test="${log.logStatus == 2}">
                                        审核不通过
                                    </c:when>
                                    <c:when test="${log.logStatus == 3}">
                                        审核通过
                                    </c:when>
                                </c:choose>
                            </div>
                        </div>
                        <c:if test="${newLogCheckList.size()== index.count && index.count>=5 }">
                            </div>
                        </c:if>
                    </c:forEach>
                </c:when>
                <c:otherwise>
                    <div class="status-cnt" style="text-align: center;display: block;line-height: 30px;">
                        暂无审核记录！
                    </div>
                </c:otherwise>
            </c:choose>
            <c:if test="${fn:length(newLogCheckList) gt 4}">
            <div>
            <div class="detail-optional J-toggle-show">
                <span class="toggle-txt J-more">展开更多<i class="vd-icon icon-down"></i></span>
                <span class="toggle-txt J-less" style="display: none;">收起<i class="vd-icon icon-up"></i></span>
            </div>
            </div>
            </c:if>
        </div>
        </div>


       <div class="detail-block">
            <div class="block-title">审核记录（旧）</div>
            <div class="status-title">
                <div class="status-item">操作时间</div>
                <div class="status-item">操作人</div>
                <div class="status-item">操作事项</div>
                <div class="status-item">备注</div>
                <div class="status-item">审核状态</div>
            </div>
            <div class="status-list">
                <c:choose>
                    <c:when test="${not empty oldLogCheckList}">
                        <c:forEach items="${oldLogCheckList}" var="log" varStatus="index">
                            <c:if test="${index.count==5}">
                                <div class="status-more J-optional-more">
                            </c:if>
                            <div class="status-cnt">
                                <div class="status-item"><fmt:formatDate value="${log.addTime}" pattern="yyyy-MM-dd HH:mm:ss" /></div>
                                <div class="status-item">${log.creatorName}</div>
                                <div class="status-item">${log.logStatusName}</div>
                                <div class="status-item">${log.logMessage}</div>
                                <div class="status-item">
                                    <c:choose>
                                        <c:when test="${log.logStatus == 1}">
                                            审核中
                                        </c:when>
                                        <c:when test="${log.logStatus == 2}">
                                            审核不通过
                                        </c:when>
                                        <c:when test="${log.logStatus == 3}">
                                            审核通过
                                        </c:when>
                                    </c:choose>
                                </div>
                            </div>
                            <c:if test="${oldLogCheckList.size()== index.count && index.count>=5 }">
                                </div>
                            </c:if>
                        </c:forEach>
                    </c:when>
                    <c:otherwise>
                        <div class="status-cnt">
                            <div class="status-item"></div>
                            <div class="status-item"></div>
                            <div class="status-item" style="text-align: center;">暂无审核记录！</div>
                        </div>
                    </c:otherwise>
                </c:choose>
            <c:if test="${fn:length(oldLogCheckList) gt 4}">
            <div>
                <div class="detail-optional J-toggle-show">
                    <span class="toggle-txt J-more">展开更多<i class="vd-icon icon-down"></i></span>
                    <span class="toggle-txt J-less" style="display: none;">收起<i class="vd-icon icon-up"></i></span>
                </div>
            </div>
            </c:if>
            </div>
       </div>
        <div class="detail-block">
                <div class="block-title">问题反馈记录</div>
                <div class="status-title">
                    <div class="status-item2">反馈时间</div>
                    <div class="status-item2">反馈人</div>
                    <div class="status-item2">问题类型</div>
                    <div class="status-item2">处理人</div>
                    <div class="status-item2">处理答复</div>
                    <div class="status-item2">处理状态</div>
                    <div class="status-item2">操作</div>
                </div>
                <div class="status-list">
                    <c:choose>
                        <c:when test="${not empty registrationFeedbackList}">
                            <c:forEach items="${registrationFeedbackList}" var="log" varStatus="index">
                                <div class="status-cnt">
                                    <div class="status-item2"><fmt:formatDate value="${log.addTimeDate}"
                                                                             pattern="yyyy-MM-dd HH:mm:ss"/></div>
                                    <div class="status-item2">${log.creatorLabel}</div>
                                    <div class="status-item2">
                                        <c:choose>
                                            <c:when test="${log.problemType == 1}">
                                                印章缺失/不清晰
                                            </c:when>
                                            <c:when test="${log.problemType == 2}">
                                                打印内容模糊
                                            </c:when>
                                            <c:when test="${log.problemType == 3}">
                                                水印内容不当
                                            </c:when>
                                            <c:when test="${log.problemType == 4}">
                                                已过期
                                            </c:when>
                                            <c:when test="${log.problemType == 5}">
                                                其他(${log.problemRemark})
                                            </c:when>
                                        </c:choose>
                                    </div>
                                    <div class="status-item2">
                                        <c:if test="${log.dealerUserId == null}"> - </c:if>
                                        <c:if test="${log.dealerUserId != null}">${log.dealerLabel}</c:if>
                                    </div>
                                    <div class="status-item2">
                                        <c:if test="${log.messageReply == null || log.messageReply == ''}"> - </c:if>
                                        <c:if  test="${log.messageReply != null and fn:length(log.messageReply) > 18}"><div title="${log.messageReply}">${fn:substring(log.messageReply,0,15)}...</div></c:if>
                                        <c:if test="${log.messageReply != null and fn:length(log.messageReply) <= 18}">${log.messageReply}</c:if>
                                    </div>
                                    <div class="status-item2">
                                        <c:if test="${log.state == 0}">未处理</c:if>
                                        <c:if test="${log.state == 1}">已处理</c:if>
                                    </div>
                                    <c:choose>
                                        <c:when test = "${(log.assignmentManagerId == sessionScope.curr_user.userId && log.state == 0) || (log.assignmentAssistantId == sessionScope.curr_user.userId && log.state == 0)}">
                                            <div class="status-item2 title-click nobor  pop-new-data"
                                                 layerParams='{"width":"728px","height":"410px","title":"确认处理并答复","link":"/registrationFeedbackController/handle.do?registrationFeedbackRecordId=${log.registrationFeedbackRecordId}&problemType=${log.problemType}&creatorLabel=${log.creatorLabel}"}'>
                                                确认处理并答复
                                            </div>
                                        </c:when>
                                        <c:otherwise>
                                            <div class="status-item2">-</div>
                                        </c:otherwise>
                                    </c:choose>
                                </div>
                            </c:forEach>
                        </c:when>
                        <c:otherwise>
                            <div class="status-cnt" style="text-align: center;display: block;line-height: 30px;">暂无数据</div>

                        </c:otherwise>
                    </c:choose>
                </div>
        </div>
    <div class="detail-block"></div>
    <script type="text/tmpl" class="J-del-firstengage-tmpl">
        <div class="del-wrap">
            <div class="del-tip">
                <i class="vd-icon icon-caution2"></i>确认删除该首营品种资质？
            </div>
            <form class="J-del-firstengage-form">
                <div class="del-cnt base-form">
                    <textarea name="content" id="" cols="30" rows="10" class="input-textarea" placeholder="必填：请填写删除原因，最少10个字，最多300个字"></textarea>
                </div>
            </form>
        </div>

    </script>
    <script type="text/tmpl" class="J-audit-tmpl">
        <div class="del-wrap">
            <div class="del-tip">
                <i class="vd-icon icon-caution2" style="height: 0px;"></i>确定{{=auditTip}}么？
            </div>
            {{ if(type != 3){ }}
            <form class="J-audit-form">
                <div class="del-cnt base-form">
                    <textarea name="content" id="" cols="30" rows="10" class="input-textarea" placeholder="必填：请填写{{=auditTip}}原因，最多64个字"></textarea>
                </div>
            </form>
            {{ } }}
        </div>

    </script>
    <script src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/static/new/js/common/artDialog/2.0.0/artDialog.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/util.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/select.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/pages/modules/list.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/static/new/js/common/template.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/static/new/js/common/jquery.validate.js"></script>
    <script src="${pageContext.request.contextPath}/static/new/js/pages/firstengage/first/view.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/static/libs/jquery/plugins/layer/layer.js"></script>

    <script type="text/javascript"
            src='${pageContext.request.contextPath}/static/js/jquery.PrintArea.js'></script>
    <script>
        function getFactoryDetail(manufacturerId,enterpriseName,divName){
            console.log(divName+"#########"+manufacturerId);
            if($("#"+divName+"_"+manufacturerId).length<1){
                // var req_url = '/goods/manufacturer/getManufacturerDetail.do?manufacturerId=' + manufacturerId + '&showCheck=0&weituo='+(divName=='manufacturerWeituo'?"Y":"");
                // var htmlObj = '<div enterpriseName="'+enterpriseName+'" manufacturerId="'+manufacturerId+'" class="detail-table '+divName+'Class" id="'+divName+'_'+manufacturerId+'"><div class="detail-table-remove"><a href="javascript:void(0);" onclick="javascript:removeThis(this)" >移除</a></div><iframe src="'+req_url+'"></iframe></div>';
                // //<div class="detail-table" id="manufacturer"></div>
                // $(htmlObj).insertBefore($("."+divName+"Class")[0]);

                $.ajax({
                    url: '/goods/manufacturer/getManufacturerDetail.do?isFrame=true&manufacturerId=' + manufacturerId + '&showCheck=0&weituo='+(divName=='manufacturerWeituo'?"Y":""),
                    type: 'GET',
                    dataType: 'html',
                    success: function (html) {
                        if (html != null) {
                            var htmlObj = '<div enterpriseName="'+enterpriseName+'" manufacturerId="'+manufacturerId+'" class="detail-table '+divName+'Class" id="'+divName+'_'+manufacturerId+'">'+html+'</div>';
                            //<div class="detail-table" id="manufacturer"></div>
                            $(htmlObj).insertBefore($("."+divName+"Class")[0]);
                            //document.getElementById("manufacturer").innerHTML = html;
                        }
                        // 删除重复元素
                        //$('#manufacturer_inner').remove()
                    }, error: function (data) {

                    }
                })
            }


        }

        $(function() {
            var manufacturerList='${manufacturerList}';
            manufacturerList =manufacturerList==''?[]:manufacturerList;
            // 将 JSON 字符串解析为 JavaScript 对象
            var manufacturers = JSON.parse(manufacturerList );
            if(manufacturers == undefined || manufacturers.length<1){//无生产企业数据,则将自行生产选中
                // setTimeout(function () {
                //     $("#manufacturerWeituoDiv").hide();
                //     $("#isSubcontractProduction").click();
                // },1000);
            }else{
                manufacturers.forEach(function(manufacturer) {
                    // 取出 manufacturerId 字段
                    var manufacturerId = manufacturer.manufacturerId;
                    var enterpriseName = manufacturer.enterpriseName;
                    var productionMode = manufacturer.productionMode;//0自行生产,1委托生产
                    // 调用 sendMsg 函数
                    getFactoryDetail(manufacturerId,enterpriseName,productionMode==0?"manufacturer":"manufacturerWeituo");
                });
            }


        });
        var manufacturerId = '${manufacturerId}';
        <%--var manufacturerId = '${manufacturerId}';--%>
        <%--&lt;%&ndash;let manageCategoryLevel = ${firstEngage.registration.manageCategoryLevel};&ndash;%&gt;--%>
        <%--$.ajax({--%>
        <%--    url: page_url + '/goods/manufacturer/getManufacturerDetail.do?manufacturerId=' + manufacturerId +'&showCheck=0',--%>
        <%--    type: 'GET',--%>
        <%--    dataType: 'html',--%>
        <%--    success: function (data) {--%>
        <%--        if (data != null) {--%>
        <%--            document.getElementById("manufacturer").innerHTML = data;--%>
        <%--        }--%>
        <%--        $("#detail_block_inner").closest('div.detail-wrap').removeClass("detail-wrap");--%>
        <%--    }, error: function (data) {--%>
        <%--    }--%>
        <%--})--%>

    </script>

<%@ include file="../../common/footer.jsp"%>