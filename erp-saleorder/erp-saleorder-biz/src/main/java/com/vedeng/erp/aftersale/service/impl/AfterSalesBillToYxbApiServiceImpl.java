package com.vedeng.erp.aftersale.service.impl;

import com.vedeng.erp.aftersale.domain.entity.AfterSalesBillToYxbEntity;
import com.vedeng.erp.aftersale.mapper.AfterSalesBillToYxbMapper;
import com.vedeng.erp.aftersale.service.AfterSalesBillToYxbApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class AfterSalesBillToYxbApiServiceImpl implements AfterSalesBillToYxbApiService {
    @Autowired
    private AfterSalesBillToYxbMapper afterSalesBillToYxbMapper;

    @Override
    public Integer countAfterSalesBillByAfterSalesIdAndCapitalBillId(Integer afterSalesId, Integer capitalBillId) {
        AfterSalesBillToYxbEntity afterSalesBillToYxbEntity = new AfterSalesBillToYxbEntity();
        afterSalesBillToYxbEntity.setAfterSalesOrderId(afterSalesId);
        afterSalesBillToYxbEntity.setCapitalBillId(capitalBillId);
        List<AfterSalesBillToYxbEntity> afterSalesBillToYxbEntities = afterSalesBillToYxbMapper.selectByCondition(afterSalesBillToYxbEntity);
        return afterSalesBillToYxbEntities.size();
    }

    @Override
    public Integer insertAfterSalesBillToYxb(AfterSalesBillToYxbEntity afterSalesBillToYxbEntity) {
        return afterSalesBillToYxbMapper.insert(afterSalesBillToYxbEntity);
    }
}
