package com.pricecenter.dto;

import java.math.BigDecimal;

public class EditBasePriceDto {

    private String skuNo;

    private boolean secondChangePrice;

    private Long skuPriceChangeApplyId;

    private BigDecimal marketPrice;

    private BigDecimal terminalPrice;

    private BigDecimal distributionPrice;

    private BigDecimal groupPrice;

    private String marketPriceChangeReason;

    private String distributionPriceChangeReason;

    private String terminalPriceChangeReason;

    private String groupPriceChangeReason;

    private String firstVerifyPirce;

    private Long[] traderId;

    private String[] supplierName;

    private String[] purchasePrice;

    private String[] changeReason;

    private Long skuId;

    private BigDecimal electronicCommercePrice;

    private BigDecimal researchTerminalPrice;

    private String electronicCommercePriceChangeReason;

    private String researchTerminalChangeReason;

    private Integer purchaseContainsFee;

    private Integer saleContainsFee;

    public Long getSkuPriceChangeApplyId() {
        return skuPriceChangeApplyId;
    }

    public void setSkuPriceChangeApplyId(Long skuPriceChangeApplyId) {
        this.skuPriceChangeApplyId = skuPriceChangeApplyId;
    }

    public BigDecimal getMarketPrice() {
        return marketPrice;
    }

    public void setMarketPrice(BigDecimal marketPrice) {
        this.marketPrice = marketPrice;
    }

    public BigDecimal getTerminalPrice() {
        return terminalPrice;
    }

    public void setTerminalPrice(BigDecimal terminalPrice) {
        this.terminalPrice = terminalPrice;
    }

    public BigDecimal getDistributionPrice() {
        return distributionPrice;
    }

    public void setDistributionPrice(BigDecimal distributionPrice) {
        this.distributionPrice = distributionPrice;
    }

    public Long[] getTraderId() {
        return traderId;
    }

    public void setTraderId(Long[] traderId) {
        this.traderId = traderId;
    }

    public String[] getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String[] supplierName) {
        this.supplierName = supplierName;
    }

    public String[] getPurchasePrice() {
        return purchasePrice;
    }

    public void setPurchasePrice(String[] purchasePrice) {
        this.purchasePrice = purchasePrice;
    }

    public String[] getChangeReason() {
        return changeReason;
    }

    public void setChangeReason(String[] changeReason) {
        this.changeReason = changeReason;
    }

    public String getSkuNo() {
        return skuNo;
    }

    public void setSkuNo(String skuNo) {
        this.skuNo = skuNo;
    }

    public boolean isSecondChangePrice() {
        return secondChangePrice;
    }

    public void setSecondChangePrice(boolean secondChangePrice) {
        this.secondChangePrice = secondChangePrice;
    }

    public String getMarketPriceChangeReason() {
        return marketPriceChangeReason;
    }

    public void setMarketPriceChangeReason(String marketPriceChangeReason) {
        this.marketPriceChangeReason = marketPriceChangeReason;
    }

    public String getDistributionPriceChangeReason() {
        return distributionPriceChangeReason;
    }

    public void setDistributionPriceChangeReason(String distributionPriceChangeReason) {
        this.distributionPriceChangeReason = distributionPriceChangeReason;
    }

    public String getTerminalPriceChangeReason() {
        return terminalPriceChangeReason;
    }

    public void setTerminalPriceChangeReason(String terminalPriceChangeReason) {
        this.terminalPriceChangeReason = terminalPriceChangeReason;
    }

    public String getFirstVerifyPirce() {
        return firstVerifyPirce;
    }

    public void setFirstVerifyPirce(String firstVerifyPirce) {
        this.firstVerifyPirce = firstVerifyPirce;
    }

    public BigDecimal getGroupPrice() {
        return groupPrice;
    }

    public void setGroupPrice(BigDecimal groupPrice) {
        this.groupPrice = groupPrice;
    }

    public String getGroupPriceChangeReason() {
        return groupPriceChangeReason;
    }

    public void setGroupPriceChangeReason(String groupPriceChangeReason) {
        this.groupPriceChangeReason = groupPriceChangeReason;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public BigDecimal getElectronicCommercePrice() {
        return electronicCommercePrice;
    }

    public void setElectronicCommercePrice(BigDecimal electronicCommercePrice) {
        this.electronicCommercePrice = electronicCommercePrice;
    }

    public BigDecimal getResearchTerminalPrice() {
        return researchTerminalPrice;
    }

    public void setResearchTerminalPrice(BigDecimal researchTerminalPrice) {
        this.researchTerminalPrice = researchTerminalPrice;
    }

    public String getElectronicCommercePriceChangeReason() {
        return electronicCommercePriceChangeReason;
    }

    public void setElectronicCommercePriceChangeReason(String electronicCommercePriceChangeReason) {
        this.electronicCommercePriceChangeReason = electronicCommercePriceChangeReason;
    }

    public String getResearchTerminalChangeReason() {
        return researchTerminalChangeReason;
    }

    public void setResearchTerminalChangeReason(String researchTerminalChangeReason) {
        this.researchTerminalChangeReason = researchTerminalChangeReason;
    }

    public Integer getPurchaseContainsFee() {
        return purchaseContainsFee;
    }

    public void setPurchaseContainsFee(Integer purchaseContainsFee) {
        this.purchaseContainsFee = purchaseContainsFee;
    }

    public Integer getSaleContainsFee() {
        return saleContainsFee;
    }

    public void setSaleContainsFee(Integer saleContainsFee) {
        this.saleContainsFee = saleContainsFee;
    }
}
