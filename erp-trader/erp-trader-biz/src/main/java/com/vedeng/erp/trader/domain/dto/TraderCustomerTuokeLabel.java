package com.vedeng.erp.trader.domain.dto;

import java.io.Serializable;

/**
 * 客户群标签表
 * @TableName T_TRADER_CUSTOMER_TUOKE_LABEL
 */
public class TraderCustomerTuokeLabel implements Serializable {
    /**
     * 客户群标签表
     */
    private Integer traderCustomerTuokeLabelId;

    /**
     * 客户id
     */
    private Integer traderId;

    /**
     * 经营对象，逗号分隔，1：公立，2：民营
     */
    private String businessTarget;

    /**
     * 医院等级，逗号分隔，1市级以上，2区县级医疗机构，3基层医疗，4公共卫生
     */
    private String hospitalLevel;

    /**
     * 经营科室，逗号分隔，T_DEPARTMENTS_HOSPITAL主键
     */
    private String businessDepartment;

    /**
     * 经营产品，逗号分隔，V_BASE_CATEGORY一级分类的ID
     */
    private String businessGoods;

    /**
     * 业务模式，逗号分隔，1：直销，2：分销
     */
    private String businessModel;

    /**
     * 销售模式，逗号分隔，1：代理权，2：关系
     */
    private String salesModel;

    /**
     * 
     */
    private Integer creator;

    /**
     * 
     */
    private Long addTime;

    /**
     * 
     */
    private Integer updater;

    /**
     * 
     */
    private Long modTime;

    private static final long serialVersionUID = 1L;

    /**
     * 客户群标签表
     */
    public Integer getTraderCustomerTuokeLabelId() {
        return traderCustomerTuokeLabelId;
    }

    /**
     * 客户群标签表
     */
    public void setTraderCustomerTuokeLabelId(Integer traderCustomerTuokeLabelId) {
        this.traderCustomerTuokeLabelId = traderCustomerTuokeLabelId;
    }

    /**
     * 客户id
     */
    public Integer getTraderId() {
        return traderId;
    }

    /**
     * 客户id
     */
    public void setTraderId(Integer traderId) {
        this.traderId = traderId;
    }

    /**
     * 经营对象，逗号分隔，1：公立，2：民营
     */
    public String getBusinessTarget() {
        return businessTarget;
    }

    /**
     * 经营对象，逗号分隔，1：公立，2：民营
     */
    public void setBusinessTarget(String businessTarget) {
        this.businessTarget = businessTarget;
    }

    /**
     * 医院等级，逗号分隔，1市级以上，2区县级医疗机构，3基层医疗，4公共卫生
     */
    public String getHospitalLevel() {
        return hospitalLevel;
    }

    /**
     * 医院等级，逗号分隔，1市级以上，2区县级医疗机构，3基层医疗，4公共卫生
     */
    public void setHospitalLevel(String hospitalLevel) {
        this.hospitalLevel = hospitalLevel;
    }

    /**
     * 经营科室，逗号分隔，T_DEPARTMENTS_HOSPITAL主键
     */
    public String getBusinessDepartment() {
        return businessDepartment;
    }

    /**
     * 经营科室，逗号分隔，T_DEPARTMENTS_HOSPITAL主键
     */
    public void setBusinessDepartment(String businessDepartment) {
        this.businessDepartment = businessDepartment;
    }

    /**
     * 经营产品，逗号分隔，V_BASE_CATEGORY一级分类的ID
     */
    public String getBusinessGoods() {
        return businessGoods;
    }

    /**
     * 经营产品，逗号分隔，V_BASE_CATEGORY一级分类的ID
     */
    public void setBusinessGoods(String businessGoods) {
        this.businessGoods = businessGoods;
    }

    /**
     * 业务模式，逗号分隔，1：直销，2：分销
     */
    public String getBusinessModel() {
        return businessModel;
    }

    /**
     * 业务模式，逗号分隔，1：直销，2：分销
     */
    public void setBusinessModel(String businessModel) {
        this.businessModel = businessModel;
    }

    /**
     * 销售模式，逗号分隔，1：代理权，2：关系
     */
    public String getSalesModel() {
        return salesModel;
    }

    /**
     * 销售模式，逗号分隔，1：代理权，2：关系
     */
    public void setSalesModel(String salesModel) {
        this.salesModel = salesModel;
    }

    /**
     * 
     */
    public Integer getCreator() {
        return creator;
    }

    /**
     * 
     */
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**
     * 
     */
    public Long getAddTime() {
        return addTime;
    }

    /**
     * 
     */
    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    /**
     * 
     */
    public Integer getUpdater() {
        return updater;
    }

    /**
     * 
     */
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    /**
     * 
     */
    public Long getModTime() {
        return modTime;
    }

    /**
     * 
     */
    public void setModTime(Long modTime) {
        this.modTime = modTime;
    }
}