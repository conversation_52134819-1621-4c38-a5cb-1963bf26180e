package com.vedeng.erp.kingdee.batch.job;

import cn.hutool.core.date.DateUtil;
import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.dto.BatchCapitalBillDto;
import com.vedeng.erp.kingdee.batch.processor.BatchCapitalBillProcessor;
import com.vedeng.erp.kingdee.batch.writer.BatchCapitalBillWriter;
import com.vedeng.erp.kingdee.dto.KingDeeNeedPayDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @Description 余额支付流程
 * @Date 2022/11/30 19:19
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class PayBuyOrderBalanceBatchJob extends BaseJob {

    @Autowired
    private BatchCapitalBillProcessor batchCapitalBillProcessor;

    @Autowired
    private BatchCapitalBillWriter batchCapitalBillWriter;


    public Job balancePaymentFlowJob() {
        return jobBuilderFactory.get("balancePaymentBatchJob")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(capitalBillPush())
                .build();
    }


    private Step capitalBillPush() {
        return stepBuilderFactory.get("余额-采购付款流水推送")
                .<BatchCapitalBillDto, KingDeeNeedPayDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchCapitalBillDtoItemReader(null, null))
                .processor(batchCapitalBillProcessor)
                .writer(batchCapitalBillWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchCapitalBillDto> batchCapitalBillDtoItemReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                      @Value("#{jobParameters['endTime']}") String endTime) {
        BatchCapitalBillDto capitalBillQueryDto = new BatchCapitalBillDto();
        // 时间要转化成时间戳，传入sql查询
        capitalBillQueryDto.setBeginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(beginTime).getTime());
        capitalBillQueryDto.setEndTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(endTime).getTime());
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchCapitalBillDto.class.getSimpleName(), capitalBillQueryDto);
    }
}
