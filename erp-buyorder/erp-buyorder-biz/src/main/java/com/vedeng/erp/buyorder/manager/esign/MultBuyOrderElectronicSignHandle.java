package com.vedeng.erp.buyorder.manager.esign;

import com.vedeng.base.api.dto.resParam.SignCompanyInfo;
import com.vedeng.infrastructure.esign.domain.ElectronicSignParam;
import com.vedeng.infrastructure.esign.handle.AbstractElectronicSignHandle;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.model.Buyorder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 采购订单多主体盖章
 */
@Component
@Slf4j
public class MultBuyOrderElectronicSignHandle extends BuyOrderElectronicSignHandle {

//    private static final String SEAL_NAME = "合同专用章";
//
//    private static final String HIDE_NAME_JIA = "$yi$";
//
//    private static final String HIDE_NAME_YI = "$yifang$";
//
//    @Value("${taxes.taxName}")
//    private String erpCompanyName;

//    @Autowired
//    private BuyorderMapper buyorderMapper;


//    @Override
//    protected void getSignCompanyInfoList(ElectronicSignParam electronicSignParam,List<SignCompanyInfo> signCompanyInfoList) {
//        Integer buyorderId = electronicSignParam.getBuyOrderId();
//        Buyorder buyorder = buyorderMapper.getBuyorderVoById(buyorderId);
//        if (buyorder == null) {
//            log.error("发起电子签单时，多主体，该订单未找到");
//            return;
//        }
//
//        //甲方
//        SignCompanyInfo signCompanyInfo = new SignCompanyInfo();
//        signCompanyInfo.setSignCompanyName(erpCompanyName);
//        signCompanyInfo.setHideCompanyName(HIDE_NAME_JIA);
//        signCompanyInfo.setSignAllSeal(true);
//        signCompanyInfo.setSignSealName(SEAL_NAME);
//        signCompanyInfoList.add(signCompanyInfo);
//
//        //已方
//        SignCompanyInfo signCompanyInfoYi = new SignCompanyInfo();
//        signCompanyInfoYi.setSignCompanyName(buyorder.getTraderName());
//        signCompanyInfoYi.setHideCompanyName(HIDE_NAME_YI);
//        signCompanyInfoYi.setSignAllSeal(true);
//        signCompanyInfoYi.setSignSealName(SEAL_NAME);
//        signCompanyInfoList.add(signCompanyInfoYi);
//
//
//    }
}
