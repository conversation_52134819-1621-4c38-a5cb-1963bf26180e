html,
body {
  font-size: 13px;
  margin: 0;
  background: #fff;
}
html *,
body * {
  box-sizing: border-box;
}
.wrap {
  display: flex;
  border: 1px solid #DCDFE6;
  height: 100vh;
  min-width: 1200px;
}
.wrap .container-l {
  width: 320px;
  border-right: 1px solid #DCDFE6;
  position: relative;
  transition: all 0.22s ease;
}
.wrap .container-l .l-trigger-show {
  position: absolute;
  top: 40px;
  right: -1px;
  width: 32px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #DCDFE6;
  cursor: pointer;
  z-index: 111;
  background-color: #fff;
  font-size: 20px;
  color: #666;
}
.wrap .container-l .l-trigger-show:hover {
  background: #F5F7FA;
}
.wrap .container-l.hide {
  width: 0;
  border: 0;
}
.wrap .container-l.hide .l-trigger-show {
  right: -32px;
}
.wrap .container-l.hide .l-trigger-show .el-icon-arrow-left {
  transform: rotate(180deg);
}
.wrap .container-l.hide .l-title,
.wrap .container-l.hide .l-steps {
  display: none;
}
.wrap .container-l .l-title {
  padding: 10px 0;
  text-align: center;
  border-bottom: 1px solid #DCDFE6;
  font-weight: 700;
}
.wrap .container-l .l-steps {
  height: calc(100% - 41px);
  display: flex;
  align-items: center;
  font-size: 12px;
}
.wrap .container-l .l-steps .step-list {
  width: 100%;
  padding: 20px;
  max-height: 100%;
  overflow-y: auto;
}
.wrap .container-l .l-steps .step-list.reverse .step-item .step-gap {
  transform: rotate(180deg);
  top: -10px;
}
.wrap .container-l .l-steps .step-list.reverse .step-item .step-r .step-gap {
  top: -20px;
  bottom: auto;
}
.wrap .container-l .l-steps .step-list.reverse .step-item:nth-child(2) .step-gap::before {
  height: calc(100% + 20px);
}
.wrap .container-l .l-steps .step-list.reverse .step-item:nth-child(2) .step-r .step-gap::before {
  height: 100%;
}
.wrap .container-l .l-steps .step-item {
  display: flex;
  margin-bottom: 40px;
}
.wrap .container-l .l-steps .step-item:last-child {
  margin-bottom: 0;
}
.wrap .container-l .l-steps .step-item .step-l {
  margin-right: 20px;
  position: relative;
}
.wrap .container-l .l-steps .step-item .step-r {
  flex: 1;
  position: relative;
}
.wrap .container-l .l-steps .step-item .step-r .step-gap {
  top: auto;
  left: calc(50% - 7px);
  bottom: 2px;
}
.wrap .container-l .l-steps .step-item .step-r .step-gap::before {
  height: calc(100% + 20px);
}
.wrap .container-l .l-steps .step-item .step-l-txt {
  font-weight: 700;
  line-height: 39px;
  width: 30px;
  text-align: center;
}
.wrap .container-l .l-steps .step-item .step-company {
  padding: 10px;
  border: 1px solid #67C23A;
  text-align: center;
}
.wrap .container-l .l-steps .step-item .step-rate {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 5px;
}
.wrap .container-l .l-steps .step-item .step-gap {
  position: absolute;
  top: 30px;
  left: 8px;
}
.wrap .container-l .l-steps .step-item .step-gap i {
  color: #ccc;
  font-weight: 700;
}
.wrap .container-l .l-steps .step-item .step-gap::before {
  content: "";
  width: 1px;
  height: calc(100% + 40px);
  border-right: 2px dashed #ccc;
  position: absolute;
  top: 17px;
  left: 5px;
}
.wrap .container-r {
  flex: 1;
  padding: 20px;
  height: calc(100vh - 2px);
  overflow: auto;
  padding-bottom: 63px;
}
.wrap .container-r.left-hide .block-table {
  width: calc(100vw - 60px);
}
.wrap .container-r.left-hide .container-r-footer {
  width: 100%;
}
.wrap .container-r .content-block {
  margin-bottom: 30px;
}
.wrap .container-r .content-block:last-child {
  margin-bottom: 0;
}
.wrap .container-r .block-title {
  font-weight: 700;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}
.wrap .container-r .block-title .block-title-options {
  font-weight: normal;
  margin-left: 20px;
}
.wrap .container-r .block-title .block-title-options .option-item {
  display: flex;
  align-items: center;
  color: #409EFF;
  cursor: pointer;
  user-select: none;
}
.wrap .container-r .block-title .block-title-options .option-item:hover {
  color: #f60;
}
.wrap .container-r .block-title .block-title-options .option-item i {
  margin-right: 5px;
}
.wrap .container-r .block-title .block-title-options .option-item.disabled {
  color: #999;
  cursor: not-allowed;
}
.wrap .container-r .block-table {
  width: calc(100vw - 460px);
  overflow-x: auto;
}
.wrap .container-r .block-table .el-input-number--mini {
  width: 100%;
}
.wrap .container-r .block-table th .must.cell {
  padding-left: 15px;
  position: relative;
  display: inline-block;
  width: auto;
}
.wrap .container-r .block-table th .must.cell::before {
  content: "*";
  position: absolute;
  color: #F56C6C;
  left: 8px;
}
.wrap .container-r .block-table .el-select {
  width: 100%;
}
.wrap .container-r .block-table .rate-wrap {
  display: flex;
  align-items: center;
}
.wrap .container-r .block-table .rate-wrap .el-input-number {
  margin-right: 3px;
}
.wrap .container-r .block-table .rate-header-wrap {
  display: flex;
  align-items: center;
}
.wrap .container-r .block-table .rate-header-wrap .el-icon-edit {
  color: #409EFF;
  font-size: 16px;
  margin-left: 3px;
  cursor: pointer;
}
.wrap .container-r .block-table .rate-header-wrap .el-icon-edit:hover {
  color: #f60;
}
.wrap .container-r .block-table .vd-ui-select {
  width: 100%;
}
.wrap .container-r .option-btn {
  cursor: pointer;
}
.wrap .container-r .option-btn.option-red {
  color: #F56C6C;
}
.wrap .container-r .option-btn.option-red:hover {
  color: #f60;
}
.wrap .container-r-footer {
  position: fixed;
  bottom: 0;
  background: #fff;
  padding: 10px 20px;
  border-top: 1px solid #DCDFE6;
  width: calc(100% - 401px);
  right: 0;
  display: flex;
  justify-content: center;
  z-index: 9;
}
.text-line-1 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  word-break: break-all;
}
.global-loading {
  font-size: 32px;
}
.global-loading .el-icon-loading {
  color: #fff;
}
.supplier-option-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  line-height: 33px;
  width: 100%;
}
.supplier-option-item .item-label {
  flex: 1;
}
.supplier-option-item .item-icon {
  width: 16px;
  height: 16px;
  background-size: 100% 100%;
  background-image: url(/static/images/floworder/fk-icon.jpg);
  margin-left: 10px;
}
.supplier-option-item .item-icon.disabled {
  background-image: url(/static/images/floworder/fk-icon-grey.jpg);
}
.supplier-option-item .item-tel {
  margin-left: 10px;
}
.ui-select-option-li.selected .supplier-option-item {
  color: #09f;
}
.ui-select-option-li.disabled .supplier-option-item {
  color: #999;
  cursor: not-allowed;
}
.dialog-form-wrap .el-dialog__title {
  font-size: 16px;
}
.dialog-form-wrap .el-form-item:last-child {
  margin-bottom: 0;
}
.dialog-form-wrap .el-dialog__body {
  padding: 20px;
}
.dialog-form-wrap .rate-wrap {
  display: flex;
  align-items: center;
}
.dialog-form-wrap .rate-wrap .el-input-number {
  margin-right: 5px;
  text-align: left;
}
.dialog-form-wrap .el-dialog__footer {
  padding: 0 20px 20px;
}
.ui-select-option-li p {
  font-size: 12px;
}
.form-error-wrap {
  display: flex;
  align-items: center;
  color: #e64545;
  margin-top: 5px;
}
.form-error-wrap i {
  font-size: 16px;
  margin-right: 5px;
  margin-top: 2px;
}
.error .el-input__inner {
  border-color: #e64545;
}
