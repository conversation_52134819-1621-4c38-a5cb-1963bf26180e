package com.vedeng.finance.enums;

import lombok.Getter;

/**
 * 航信发票状态
 *
 * @Author: Cherny.chen
 * @Create: 2021/7/2 14:58
 */
@Getter
public enum HxInvoiceStatusEnum {

    WAIT_RECORD(1, "待录票"), CHECKING(2, "审核中"), CHKCKED(3, "已审核"), UNCLAIM(4, "待认领"),
    COST(5, "费用票"), EXCEPTION(6, "异常票"), NEGATIVE_NUMBER(7, "负数票"), OTHER(8, "其他"),
    WAIT_RETURN(9, "待退票"), RETURNED(10, "已退票"), RECORDED(11, "已录票"), INVALID(12, "无效票");

    private final Integer status;

    private final String desc;

    HxInvoiceStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static HxInvoiceStatusEnum getInstanceByCode(Integer code){
        for (HxInvoiceStatusEnum value : HxInvoiceStatusEnum.values()) {
            if (value.getStatus().equals(code)){
                return value;
            }
        }
        return EXCEPTION;
    }
}
