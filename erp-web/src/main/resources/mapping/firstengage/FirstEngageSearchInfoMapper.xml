<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.firstengage.dao.FirstEngageSearchInfoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.firstengage.model.FirstEngageSearchInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 20 18:39:49 CST 2019.
    -->
    <id column="FIRST_ENGAGE_SEARCH_INFO_ID" jdbcType="INTEGER" property="firstEngageSearchInfoId" />
    <result column="USER_ID" jdbcType="INTEGER" property="userId" />
    <result column="CONTENT" jdbcType="VARCHAR" property="content" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 20 18:39:49 CST 2019.
    -->
    FIRST_ENGAGE_SEARCH_INFO_ID, USER_ID, CONTENT
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 20 18:39:49 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    from T_FIRST_ENGAGE_SEARCH_INFO
    where FIRST_ENGAGE_SEARCH_INFO_ID = #{firstEngageSearchInfoId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 20 18:39:49 CST 2019.
    -->
    delete from T_FIRST_ENGAGE_SEARCH_INFO
    where FIRST_ENGAGE_SEARCH_INFO_ID = #{firstEngageSearchInfoId,jdbcType=INTEGER}
  </delete>
  
  <delete id="deleteByContent" parameterType="java.util.Map">
    delete from T_FIRST_ENGAGE_SEARCH_INFO
    where USER_ID = #{userId,jdbcType=INTEGER} AND CONTENT = #{keyWords, jdbcType=VARCHAR}
  </delete>
  
  <insert id="insert" parameterType="com.vedeng.firstengage.model.FirstEngageSearchInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 20 18:39:49 CST 2019.
    -->
    insert into T_FIRST_ENGAGE_SEARCH_INFO (FIRST_ENGAGE_SEARCH_INFO_ID, USER_ID, 
      CONTENT)
    values (#{firstEngageSearchInfoId,jdbcType=INTEGER}, #{userId,jdbcType=INTEGER}, 
      #{content,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.firstengage.model.FirstEngageSearchInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 20 18:39:49 CST 2019.
    -->
    insert into T_FIRST_ENGAGE_SEARCH_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="firstEngageSearchInfoId != null">
        FIRST_ENGAGE_SEARCH_INFO_ID,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="content != null">
        CONTENT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="firstEngageSearchInfoId != null">
        #{firstEngageSearchInfoId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  
  <insert id="insertSelectiveInfo" parameterType="java.util.Map">
    insert into T_FIRST_ENGAGE_SEARCH_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        USER_ID,
      </if>
      ADD_TIME,
      <if test="content != null">
        CONTENT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      unix_timestamp(now())*1000,
      <if test="content != null">
        #{keyWords,jdbcType=VARCHAR},
      </if>
      
    </trim>
  </insert>
  
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.firstengage.model.FirstEngageSearchInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 20 18:39:49 CST 2019.
    -->
    update T_FIRST_ENGAGE_SEARCH_INFO
    <set>
      <if test="userId != null">
        USER_ID = #{userId,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        CONTENT = #{content,jdbcType=VARCHAR},
      </if>
    </set>
    where FIRST_ENGAGE_SEARCH_INFO_ID = #{firstEngageSearchInfoId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.firstengage.model.FirstEngageSearchInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 20 18:39:49 CST 2019.
    -->
    update T_FIRST_ENGAGE_SEARCH_INFO
    set USER_ID = #{userId,jdbcType=INTEGER},
      CONTENT = #{content,jdbcType=VARCHAR}
    where FIRST_ENGAGE_SEARCH_INFO_ID = #{firstEngageSearchInfoId,jdbcType=INTEGER}
  </update>
</mapper>