package com.vedeng.temporal.workflow.step.impl;

import com.vedeng.temporal.context.CompanyExecutionContext;
import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import com.vedeng.temporal.enums.BusinessStepType;
import com.vedeng.temporal.exception.BusinessProcessException;
import com.vedeng.temporal.polling.universal.enums.DataSourceType;
import com.vedeng.temporal.polling.universal.request.UniversalPollingRequest;
import com.vedeng.temporal.polling.universal.result.UniversalPollingResult;
import com.vedeng.temporal.polling.universal.workflow.UniversalPollingWorkflow;
import com.vedeng.temporal.workflow.activity.PurchaseOrderActivity;
import com.vedeng.temporal.workflow.step.BusinessStep;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 采购订单业务步骤 - V5 集成FlowOrderInfo创建版本
 * <p>
 * 架构优化说明：
 * - 使用新的 PurchaseOrderActivity 替代 CompanyBusinessActivity
 * - 每个业务操作都是独立的 Activity 方法
 * - 通过 CompanyBusinessRequest.businessData 传递数据
 * - 支持数据串联：创建 → 提交 → 审核 → 状态确认
 * - **新增：在订单审核成功后创建FlowOrderInfo记录**
 * - 异常处理和重试由 Temporal 和 UniversalBusinessTemplate 统一管理
 * <p>
 * 业务流程：
 * 1. waitForSalesOrderCompletion - 等待前置销售订单完成
 * 2. createPurchaseOrder - 创建采购订单，获取订单ID
 * 3. updatePurchaseOrder - 修改采购订单，传递订单ID
 * 4. submitPurchaseOrderForApproval - 提交审核，传递订单ID
 * 5. approvePurchaseOrder - 审核通过，传递订单ID
 * 6. createFlowOrderInfo - 创建FlowOrderInfo记录（审核成功后创建）
 *
 * <AUTHOR> 4.0 sonnet
 * @version 5.0 (集成FlowOrderInfo创建版本)
 * @since 2025-01-29
 */
@Slf4j
public class PurchaseOrderStep implements BusinessStep {

    private final PurchaseOrderActivity purchaseOrderActivity;

    public PurchaseOrderStep(PurchaseOrderActivity purchaseOrderActivity) {
        this.purchaseOrderActivity = purchaseOrderActivity;
    }

    @Override
    public CompanyBusinessResponse execute(CompanyBusinessRequest request, CompanyExecutionContext context) {
        if (context.isFirst()) {
            return CompanyBusinessResponse.success("采购订单流程执行成功", "结束");
        }
        // 当公司是最后一个时，需要查看flownode信息中是否有下一节点，如果没有下一个节点，则跳过生成采购单
        if(context.isLast()){
            try {
                // 检查是否存在下一个流程节点
                boolean hasNextNode = purchaseOrderActivity.hasNextFlowNode(request);
                
                if (!hasNextNode) {
                    log.info("当前公司是最后一个且没有下一流程节点，跳过生成采购单，业务ID: {}, 当前公司: {}", 
                            request.getBusinessId(), context.getCurrentCompany());
                    return CompanyBusinessResponse.success("跳过采购单生成", "SKIPPED");
                } else {
                    log.info("当前公司是最后一个但存在下一流程节点，继续生成采购单，业务ID: {}, 当前公司: {}", 
                            request.getBusinessId(), context.getCurrentCompany());
                }
            } catch (Exception e) {
                log.error("检查下一流程节点异常，继续执行采购单生成，业务ID: {}, 当前公司: {}", 
                        request.getBusinessId(), context.getCurrentCompany(), e);
                // 异常情况下继续执行，避免阻塞业务流程
            }
        }

        // 固化关键参数，防止 Temporal 重试时参数变化
        final String currentCompany = context.getCurrentCompany();
        final String originalSourceCompany = request.getSourceCompanyCode();
        final String originalTargetCompany = request.getTargetCompanyCode();

        log.info("开始执行采购订单步骤，业务ID: {}, 源公司: {}, 目标公司: {}, 当前公司: {}",
                request.getBusinessId(), originalSourceCompany, originalTargetCompany, currentCompany);
        String saleOrderNo = null;
        // 1. 等待前置当前销售订单完成
        if (currentCompany != null) {
            log.info("等待当前公司销售订单完成， 当前公司: {}", currentCompany);
            saleOrderNo = waitForSalesOrderCompletion(request, currentCompany);
            log.info("当前公司销售订单已完成，当前公司: {}, 销售单号: {}", currentCompany, saleOrderNo);
        }

        // 2. 创建采购订单
        CompanyBusinessRequest createRequest = request.toBuilder()
                .targetCompanyCode(currentCompany)  // 只更新目标公司，保持源公司不变
                .build();
        CompanyBusinessResponse createResult = purchaseOrderActivity.createPurchaseOrder(createRequest);
        if (!Boolean.TRUE.equals(createResult.getSuccess())) {
            return createResult;
        }

        String orderId = createResult.getGeneratedDocumentId();
        // 验证订单ID的有效性
        if (orderId == null || orderId.trim().isEmpty()) {
            String errorMsg = "创建采购订单失败：未获取到有效的订单ID";
            log.error("{}, 业务ID: {}", errorMsg, request.getBusinessId());
            return CompanyBusinessResponse.failure(errorMsg, "ORDER_ID_INVALID");
        }

        log.info("采购订单创建成功，业务ID: {}, 订单ID: {}", request.getBusinessId(), orderId);

        // 3. 修改采购订单（传递订单ID）
        createRequest.getExtendedProperties().put("saleOrderNo", saleOrderNo);
        CompanyBusinessRequest updateRequest = buildRequestWithOrderId(createRequest, orderId);
        CompanyBusinessResponse updatePurchaseOrder = purchaseOrderActivity.updatePurchaseOrder(updateRequest);
        if (!Boolean.TRUE.equals(updatePurchaseOrder.getSuccess())) {
            return updatePurchaseOrder;
        }
        log.info("采购订单修改成功，业务ID: {}, 订单ID: {}", request.getBusinessId(), orderId);

        // 4. 创建FlowOrderInfo记录（提交成功后创建）
        CompanyBusinessRequest flowOrderRequest = buildRequestWithOrderId(createRequest, orderId);
        CompanyBusinessResponse flowOrderResult = purchaseOrderActivity.createFlowOrderInfoRecord(flowOrderRequest);
        if (!Boolean.TRUE.equals(flowOrderResult.getSuccess())) {
            log.warn("创建FlowOrderInfo记录失败，但继续执行后续流程，业务ID: {}, 订单ID: {}, 错误: {}",
                    request.getBusinessId(), orderId, flowOrderResult.getMessage());
            return flowOrderResult;
        }

        // 5. 提交审核（传递订单ID）
        CompanyBusinessRequest submitRequest = buildRequestWithOrderId(createRequest, orderId);
        CompanyBusinessResponse submitPurchaseOrderForApproval = purchaseOrderActivity.submitPurchaseOrderForApproval(submitRequest);
        if (!Boolean.TRUE.equals(submitPurchaseOrderForApproval.getSuccess())) {
            return submitPurchaseOrderForApproval;
        }
        log.info("采购订单提交审核成功，业务ID: {}, 订单ID: {}", request.getBusinessId(), orderId);

        // 6. 审核通过（传递订单ID）
        CompanyBusinessRequest approveRequest = buildRequestWithOrderId(createRequest, orderId);
        CompanyBusinessResponse approvePurchaseOrder = purchaseOrderActivity.approvePurchaseOrder(approveRequest);
        if (!Boolean.TRUE.equals(approvePurchaseOrder.getSuccess())) {
            return approvePurchaseOrder;
        }
        log.info("采购订单审核成功，业务ID: {}, 订单ID: {}", request.getBusinessId(), orderId);



        return CompanyBusinessResponse.success("采购订单流程执行成功", orderId);
    }

    @Override
    public BusinessStepType getStepType() {
        return BusinessStepType.PURCHASE_ORDER;
    }

    @Override
    public String getStepName() {
        return "采购订单步骤";
    }

    @Override
    public String getStepDescription() {
        return "执行采购订单完整流程：等待销售订单完成 → 创建订单 → 修改订单 → 提交审核 → 审核通过 → 创建FlowOrderInfo";
    }

    // ========== 私有辅助方法 ==========

    /**
     * 构建包含订单ID的请求对象
     * 用于在步骤间传递订单ID数据
     */
    private CompanyBusinessRequest buildRequestWithOrderId(CompanyBusinessRequest originalRequest, String orderId) {
        // 构建包含订单ID的业务数据
        Map<String, Object> businessData = new HashMap<>();
        businessData.put("orderId", orderId);

        // 如果原请求有业务数据，先解析并合并
        if (StringUtils.hasText(originalRequest.getBusinessData())) {
            // 简化处理，只保留必要的订单ID
            log.debug("原请求包含业务数据，保留订单ID: {}", orderId);
        }

        // 构建新的请求对象
        return originalRequest.toBuilder()
                .businessData("{\"orderId\":\"" + orderId + "\"}")
                .build();
    }

    /**
     * 等待前置销售订单完成并返回销售单号
     * 使用统一轮询组件进行数据库查询轮询
     *
     * @return 销售单号
     */
    private String waitForSalesOrderCompletion(CompanyBusinessRequest request, String currentCompany) {
        log.info("开始等待销售订单完成，业务ID: {}, 公司: {}", request.getBusinessId(), currentCompany);
        String businessId = request.getBusinessId();

        try {
            // 构建数据库查询参数
            Map<String, Object> queryParameters = new HashMap<>();
            queryParameters.put("queryTypes", Arrays.asList("SALE_ORDER"));
            queryParameters.put("currentCompany", currentCompany);

            // 构建统一轮询请求 - 使用Lombok Builder
            UniversalPollingRequest pollingRequest =
                    UniversalPollingRequest.builder()
                            .businessId(businessId)
                            .businessType("SALES_ORDER")
                            .dataSourceType(DataSourceType.LOCAL_DATABASE)
                            .companyCode(currentCompany)
                            .queryType("TEMPORAL_FLOW_ORDER_QUERY")
                            .queryParameters(queryParameters)
                            .completionCheckConfig("saleOrderNo:isNotBlank")
                            .build();

            // 执行统一轮询（使用静态方法保持确定性）
            UniversalPollingResult<Map<String, Object>> result = UniversalPollingWorkflow.universalPoll(pollingRequest);

            if (!result.isSuccess()) {
                String context = "Step=" + getStepName() + ", Company=" + currentCompany + ", BusinessId=" + businessId;
                throw BusinessProcessException.retryable("销售订单完成确认失败: " + result.getMessage(),
                        "SALES_ORDER_POLLING_INCOMPLETE", context);
            }

            // 直接从轮询结果获取销售单号
            Map<String, Object> data = result.getData();
            String saleOrderNo = data != null ? (String) data.get("saleOrderNo") : null;

            // 存储到扩展属性
            Map<String, Object> extendedProperties = request.getExtendedProperties();
            if (extendedProperties == null) {
                extendedProperties = new HashMap<>();
            }
            extendedProperties.put("saleOrderNo", saleOrderNo);
            request.setExtendedProperties(extendedProperties);

            log.info("销售订单完成确认成功，业务ID: {}, 公司: {}, 销售单号: {}",
                    businessId, currentCompany, saleOrderNo);

            return saleOrderNo;

        } catch (BusinessProcessException e) {
            // 重新抛出业务流程异常
            throw e;
        } catch (Exception e) {
            log.error("等待销售订单完成异常，业务ID: {}, 公司: {}", businessId, currentCompany, e);
            String context = "Step=" + getStepName() + ", Company=" + currentCompany + ", BusinessId=" + businessId;
            throw BusinessProcessException.retryable("销售订单完成系统异常", "SALES_ORDER_POLLING_ERROR", context);
        }
    }
}
