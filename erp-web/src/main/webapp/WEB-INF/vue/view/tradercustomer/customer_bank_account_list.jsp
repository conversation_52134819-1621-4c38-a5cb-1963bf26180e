<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
            + path + "/";
%>
<c:set var="path" value="<%=basePath%>" scope="application" />
<div id="app" style="display: none;">
    <el-card class="box-card J-block">
        <div slot="header" class="clearfix">
            <span>客户账户列表</span>
        </div>
        <template>
            <el-form :inline="true" :model="customerBankAccountDto" :rules="searchRules" ref="form">
                <el-form-item label="客户名称" size="mini" prop="traderName">
                    <el-input v-model="customerBankAccountDto.traderName" size="mini" placeholder="" style="width: 200px"></el-input>
                </el-form-item>
                <el-form-item label="账户名称" size="mini" prop="accountName">
                    <el-input v-model="customerBankAccountDto.accountName" size="mini" placeholder="" style="width: 200px"></el-input>
                </el-form-item>

                <el-form-item label="账号" size="mini" prop="accountNo">
                    <el-input v-model="customerBankAccountDto.accountNo" size="mini" placeholder="" style="width: 200px"></el-input>
                </el-form-item>
                <el-form-item label="验证状态" size="mini" prop="isVerify">
                    <el-select v-model="customerBankAccountDto.isVerify" size="mini">
                        <el-option label="全部" :value="-1"></el-option>
                        <el-option label="未验证" :value="0"></el-option>
                        <el-option label="已验证" :value="1"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>

            <el-row style="margin-bottom: 20px; text-align: center">
                <el-button type="primary" :loading="loading" @click="submitForm()" size="mini">搜索</el-button>
                <el-button type="primary" plain @click="reset()" size="mini">重置</el-button>
                <el-button type="primary" plain @click="importMark()" size="mini">导入标记</el-button>
            </el-row>

            <el-table size="mini"
                    :data="customerBankAccountDtoList"
                    ref="distributionLinkTable"
                    border
                    style="width: 100%; "
                    v-loading="loading"
                    key="customerBankAccountId">
                <el-table-column
                        align="center"
                        text-align="center"
                        label="客户ID" width="80">
                    <template slot-scope="scope">
                        <span>{{scope.row.traderId || '-'}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        label="客户名称" width="200">
                    <template slot-scope="scope">
                        <span :title="scope.row.traderName || '-'" style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{{scope.row.traderName || '-'}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        label="账户类型" width="80">
                    <template slot-scope="scope">
                        <span>{{scope.row.accountTypeName}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        label="账户名称" width="200">
                    <template slot-scope="scope">
                        <span :title="scope.row.accountName || '-'" style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{{scope.row.accountName || '-'}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        label="账号" width="200">
                    <template slot-scope="scope">
                        <span>{{scope.row.accountNo || '-'}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        label="开户银行">
                    <template slot-scope="scope">
                        <span :title="scope.row.bankName || '-'" style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{{scope.row.bankName || '-'}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        label="联行号" width="200">
                    <template slot-scope="scope">
                        <span>{{scope.row.bankNo || '-'}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        label="验证状态" width="100">
                    <template slot-scope="scope">
                        <span>{{scope.row.isVerifyName || '-'}}</span>
                    </template>
                </el-table-column>
                <el-table-column    
                        align="center"
                        text-align="center"
                        label="操作" width="120">
                    <template slot-scope="scope" >
                        <el-button type="text" size="small" @click="handleDelete(scope.row)">删除</el-button>
                        <el-button type="text" size="small" @click="openMarkDialog(scope.row)">标记</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPageNo"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="currentSize"
                    layout="->,total, sizes, prev, pager, next, jumper"
                    :total="totalLines">
            </el-pagination>
        </template>
    </el-card>
    
    <!-- 标记对话框 -->
    <el-dialog title="标记" :visible.sync="markDialogVisible">
        <el-radio-group v-model="markStatus">
            <el-radio :label="0">未验证</el-radio>
            <el-radio :label="1">已验证</el-radio>
        </el-radio-group>
        <span slot="footer" class="dialog-footer">
            <el-button size="small" type="primary" @click="submitMark">提交</el-button>
            <el-button size="small" @click="markDialogVisible = false">取消</el-button>
        </span>
    </el-dialog>
    
    <!-- 导入对话框 -->
    <el-dialog title="导入标记" :visible.sync="importDialogVisible">
        <el-container>
            <el-main>
                <el-row>
                    <el-col>
                        <p>请选择要导入的文件：</p>
                        <el-upload
                                class="upload-demo"
                                ref="upload"
                                :multiple="false"
                                action="/customerBankAccountApi/upload.do"
                                show-file-list="false"
                                accept=".xlsx,.xls"
                                limit="2"
                                :before-upload="beforeUpload"
                                :on-remove="handleRemove"
                                :on-change="onChangeToolFile"
                                :on-success="onSuccess"
                                :file-list="fileList"
                                :auto-upload="false">
                            <el-button slot="trigger" size="small" type="primary">添加文件</el-button>
                        </el-upload>
                    </el-col>
                </el-row>
                <el-row style="margin-top: 20px;">
                    <el-col>
                        如果您没有标准模版，请
                        <el-link href="/static/template/客户账户导入标记模板.xlsx" :underline="false" type="primary">下载数据模板</el-link>
                    </el-col>
                </el-row>
    
            </el-main>
            <el-footer>
                <el-row style="text-align: center">
                    <el-col>
                        <el-button size="small" type="primary" @click="submitUpload">导入</el-button>
                        <el-button size="small" @click="uploadCancel">取消</el-button>
                    </el-col>
                </el-row>
            </el-footer>
        </el-container>
    </el-dialog>
</div>

<script src="${pageContext.request.contextPath}/static/api/trader/customerBankAccount.js?rnd=${resourceVersionKey}"></script>

<script type="text/javascript">
    let vm = new Vue({
        el: '#app',
        data() {
            return {
                currentSize: 10,
                currentPageNo: 1,
                totalLines: 0,

                loading: false,
                
                customerBankAccountDto: {
                    traderName:'',
                    accountName: '',
                    accountNo: '',
                    isVerify: -1
                },
                customerBankAccountDtoList: [],
                
                markDialogVisible: false,
                markStatus: null,
                currentRow: null,
                
                importDialogVisible: false,
                fileList: [],

                searchRules: {
                    accountName: [{max: 100, message: "最多输入100个字符", trigger: "blur"}],
                    accountNo: [{max: 100, message: "最多输入100个字符", trigger: "blur"}]
                },
            }
        },

        mounted() {
            loadingApp();
        },

        created() {
            this.doSearch();
        },

        methods: {
            submitForm() {
                this.$refs.form.validate(valid => {
                    if(valid) {
                        this.currentPageNo = 1;
                        this.doSearch();
                    } else {
                        return false;
                    }
                })
            },

            // 重置按钮
            reset() {
                this.customerBankAccountDto.traderName = '';
                this.customerBankAccountDto.accountName = '';
                this.customerBankAccountDto.accountNo = '';
                this.customerBankAccountDto.isVerify = -1;
                this.$nextTick(() => {
                    this.doSearch();
                });
            },

            doSearch() {
                var pageParam = {
                    "pageSize": this.currentSize,
                    "pageNum": this.currentPageNo,
                    "param": this.customerBankAccountDto
                };
                this.loading = true;
                getCustomerBankAccountList(pageParam).then(res => {
                    this.loading = false;
                    console.log("res", res);
                    this.customerBankAccountDtoList = res.data.data.list;
                    this.totalLines = res.data.data.total;
                })
            },

            handleSizeChange(val) {
                this.currentSize = val;
                this.currentPageNo = 1;
                this.doSearch();
            },

            handleCurrentChange(val) {
                this.currentPageNo = val;
                this.doSearch();
            },
            
            handleDelete(row) {
                this.$confirm('确定删除该记录吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    let dto = {
                        "customerBankAccountId": row.customerBankAccountId
                    };
                    del(dto).then(res => {
                        this.$message({
                            type: 'success',
                            message: '删除成功'
                        });
                        this.doSearch();
                    }).catch(error => {
                        this.$message.error('删除失败，请稍后重试');
                    });
                }).catch(() => {
                    // 取消删除
                    this.$message.info('已取消删除');
                });
            },
            
            openMarkDialog(row) {
                this.currentRow = row;
                this.markDialogVisible = true;
                this.markStatus = row.isVerify;
            },
            
            submitMark() {
                const dto = {
                    customerBankAccountId: this.currentRow.customerBankAccountId,
                    isVerify: this.markStatus,
                };
                
                updateCustomerBankAccount(dto).then(res => {
                    this.$message({
                        type: 'success',
                        message: '标记成功'
                    });
                    this.markDialogVisible = false;
                    this.doSearch(); // 更新列表
                }).catch(error => {
                    this.$message.error('标记失败，请稍后重试');
                });
            },

            importMark() {
                this.importDialogVisible = true;
            },

            beforeUpload(){
                console.log("导入前");
            },

            onChangeToolFile(file, fileList) {
                // 只保留当前选择的文件
                this.fileList = [file];
                // 清空上传组件的文件列表
                this.$refs.upload.clearFiles();
                // 手动添加新文件到上传组件
                this.$refs.upload.fileList = [file];
            },
            
            uploadCancel() {
                this.importDialogVisible = false;
                this.fileList = [];
                this.$refs.upload.clearFiles();
            },

            submitUpload() {
                if (this.fileList.length === 0 || this.$refs.upload.fileList.length === 0) {
                    this.$message({
                        message: '请选择需要上传的文件！',
                        duration: 1000,
                        showClose: true,
                        type: 'error'
                    });
                    return;
                }
                debugger;
                // 校验上传文件类型
                const validTypes = ['.xls', '.xlsx'];
                const invalidFiles = this.fileList.filter(file => {
                    // 获取文件扩展名
                    const fileExtension = file.name.slice(((file.name.lastIndexOf(".")) >>> 0));
                    console.log("fileExtension",fileExtension);
                    return !validTypes.includes(fileExtension);
                });
                
                if (invalidFiles.length > 0) {
                    this.$message({
                        message: '上传的文件类型必须是 .xls 或 .xlsx',
                        duration: 1000,
                        showClose: true,
                        type: 'error'
                    });
                    return;
                }
                this.$refs.upload.submit();
            },
            handleRemove(file, fileList) {
                this.fileList = [];
                this.$refs.upload.clearFiles();
            },
            handlePreview(file) {
                console.log(file);
            },
            onSuccess(data) {
                console.log('====>', data);
                const latestFile = this.fileList[this.fileList.length - 1]; // 获取最后一个文件
                const fileName = latestFile.name.split('.').slice(0, -1).join('.'); // 去除后缀
                if (data.code === 0) {
                    if (Array.isArray(data.data) && data.data.length === 0) {
                        // data.data 是空数组的情况
                        this.$message({
                            message: '导入成功',
                            type: 'success',
                            duration: 1000,
                            showClose: true,
                            onClose: () => {
                                this.importDialogVisible = false;
                                this.fileList = [];
                                this.$refs.upload.clearFiles();
                                this.doSearch();
                            }
                        });
                    } else if (data.data) {
                        // data.data 有有效数据的情况
                        this.importDialogVisible = false;
                        this.fileList = [];
                        this.$refs.upload.clearFiles();
                        
                        this.$confirm('部分导入失败', '提示', {
                            confirmButtonText: '下载详情',
                            cancelButtonText: '取消',
                            type: 'error'
                        }).then(() => {
                            let dto = {
                                "list": data.data,
                                "fileName": fileName
                            };
                            download(dto).then(res => {
                                this.fileList = [];
                                this.$refs.upload.clearFiles();
                                const blob = new Blob([res.data]);  // 创建 Blob 对象
                                const link = document.createElement('a');
                                link.href = window.URL.createObjectURL(blob);  // 创建下载链接
                                link.setAttribute('download', fileName + '_导入失败明细.xlsx');  // 设置下载文件名
                                document.body.appendChild(link);
                                link.click();  // 触发下载
                                document.body.removeChild(link);  // 移除链接
                            }).catch(error => {
                                this.fileList = [];
                                this.$refs.upload.clearFiles();
                                this.$message.error('下载失败');
                            });
                        }).catch(() => {
                                this.fileList = [];
                                this.$refs.upload.clearFiles();
                        });
                    }
                } else {
                    this.$message({
                        message: data.message + ",请重新添加文件",
                        type: 'error'
                    });
                    this.fileList = [];
                    this.$refs.upload.clearFiles();
                }
            }
            
        }
    })
</script>


<style>
    .el-dialog__body {
        padding: 0px 20px;
    }
    .el-card__body {
        margin-top:10px;
        padding: 10px;
    }
    .el-form-item{
        margin-bottom: 10px;
    }
    .el-card__header {
        display: none;
        height: 34px;
        background-color: #c5ddfb;
        box-sizing: border-box;
        padding-left: 15px;
        font-weight: 700;
        padding-top: 7px;
        padding-bottom: 22px;
        color: #303133;
    }

    .el-input--mini .el-input__icon {
        line-height: 0;
    }

    .el-pagination .btn-next .el-icon, .el-pagination .btn-prev .el-icon {
        margin-right: 5px;
    }

    .el-date-editor .el-range__icon {
        display: contents;
    }

    .el-date-editor .el-range__close-icon{
        display: contents;
    }

    .el-date-editor .el-range-separator {
        width: 10%;
    }

    .el-select .el-input .el-select__caret {
        display: none;
    }
    
    .el-dialog {
        position: relative;
        box-shadow: rgba(0, 0, 0, 0.3) 0px 1px 3px;
        box-sizing: border-box;
        width: 30%;
        margin: 0px auto 50px;
        border-radius: 2px;
    }
</style>