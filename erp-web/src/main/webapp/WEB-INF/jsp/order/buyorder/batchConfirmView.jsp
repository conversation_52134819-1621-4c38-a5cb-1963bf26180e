<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="确认审核" scope="application" />
<%@ include file="../../common/common.jsp"%>

<style>
	#payVedengBankIdLi .warning{
		margin-left: 110px;
	}
</style>

 <div class="formpublic">
	 <input type="hidden" name="expenseCheckStatus" value="${expenseCheckStatus}">
            <form method="post" action="" id="complement">
                <ul>
					<c:if test="${weatherShow == 0}">
						<li id="payVedengBankIdLi">
							<label class="infor_name"><span style="color: red">*</span>交易方式</label>
							<select id="payVedengBankId" class="input-middle f_left" name="payVedengBankId" style="width: 297px">
								<option disabled selected value="-1">请选择该申请的付款银行</option>
								<c:forEach var="list" items="${paymentBankList}">
									<option value="${list.payVedengBankId}" >${list.payBankName}</option>
								</c:forEach>
							</select>
						</li>
					</c:if>
                   <li>
					<div class="infor_name">
						<lable for='name'>备注</lable>
					</div>
					<div class="f_left">
						<input type="text" name="comment" id="comment" class="input-larger" value="" />
					</div>
				</li>
                </ul>
                <div class="add-tijiao tcenter">
                    <button type="button" class="bg-light-green" onclick="complementCommit()">提交</button>
                    <button class="dele" type="button" id="close-layer">取消</button>
                </div>
           </form>
</div>


<script type="text/javascript">
	function complementCommit(){
		var comment = $("input[name='comment']").val()

		var  payVedengBankId = $("select[name='payVedengBankId']").val();

		if(payVedengBankId == null){
			warnTips("payVedengBankId","请选择付款银行");
			return false;
		}

		if(comment.length > 100){
			warnTips("comment","备注内容不允许超过100个字符");
			return false;
		}

		$("#payVedengBankIdParent",parent.document).val(payVedengBankId);
		$("#commentParent",parent.document).val(comment);
		// 确保 Vue 实例是 accessible
		console.log(parent);
		console.log(parent.vm);
		if (parent.vm && parent.vm.batchConfirm) {
			parent.vm.batchConfirm();
		} else {
			console.error("batchConfirm 方法未找到");
		}
	};

	$(function () {
		let expenseCheckStatus = $('input[name=expenseCheckStatus]').val();
		expenseCheckStatus = eval(expenseCheckStatus);
		if (!expenseCheckStatus) {
			layer.confirm('当前采购费用订单有审核不通过的付款申请，请核实后再付款', {
				btn: ['确定继续', '取消']
			}, function(){
				layer.close(layer.index);
			}, function(index){
				parent.layer.closeAll();
			});
		}
	});

</script>

<%@ include file="../../common/footer.jsp"%>