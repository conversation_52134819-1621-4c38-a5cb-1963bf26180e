$(function () {
    $("#submit").click(function () {
        checkLogin();
        $(".warning").remove();
        $("input").removeClass("errorbor");

        var afterSalesId = $('#afterSalesId').val();
        var logisticsNo = $("#childLogisticsNo").val();
        var logisticsId = $('#childLogistics').val();
        var invoiceIdArr = $('#invoiceIdArr').val();

        if (logisticsNo == undefined || logisticsNo == '') {
            warnTips("logisticsNoError", " 快递单号不允许为空");
            return false;
        }

        if (logisticsNo.length > 200) {
            warnTips("logisticsNoError", " 快递单号最多输入200字符，请检查后提交");
            return false;
        }

        $.ajax({
            type: "POST",
            url: page_url + "/finance/invoice/saveExpress.do",
            data: {
                "logisticsNo": logisticsNo,
                "logisticsId": logisticsId,
                "invoiceIdArr": invoiceIdArr,
                "qpType": $("#qpType").val(),
                "afterSaleId": afterSalesId
            },
            dataType: 'json',
            success: function (data) {
                if (data.code == 0) {
                    window.location.reload();
                } else {
                    layer.alert(data.message, {icon: 2},
                        function (index) {
                            layer.close(index);
                            return false;
                        }
                    );
                }

            }, error: function (data) {
                if (data.status == 1001) {
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });

    });
});

function selfClose() {
    layer.closeAll();
}