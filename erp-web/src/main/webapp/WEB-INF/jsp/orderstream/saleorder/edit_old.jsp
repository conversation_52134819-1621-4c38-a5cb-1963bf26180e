<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html lang="en">
<head>
    <%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
    <%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
    <%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
    <%@ taglib prefix="shiro" uri="http://shiro.apache.org/tags"%>
    <%@ taglib prefix="tags" tagdir="/WEB-INF/tags"%>
    <%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>
    <%@ taglib uri="http://com.vedeng.common.util/definetags" prefix="define"%>
    <%@taglib prefix="sf" uri="http://www.springframework.org/tags/form"%>

    <%@ page trimDirectiveWhitespaces="true" %>

    <%
        String path = request.getContextPath();
        String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
                + path + "/";
    %>
    <c:set var="path" value="<%=basePath%>" scope="application" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta http-equiv="Cache-Control" content="no-siteapp" />
    <c:choose>
        <c:when test="${pageType eq 1}">
            <title>新增订单</title>
        </c:when>
        <c:otherwise>
            <title>编辑订单</title>
        </c:otherwise>
    </c:choose>

    <!--  <link rel="stylesheet" href="<%=basePath%>static/css/content.css">-->
    <link rel="stylesheet" href="<%=basePath%>static/css/general.css?rnd=${resourceVersionKey}" />
    <link rel="stylesheet" href="<%=basePath%>static/css/manage.css" />
    <!-- 模糊搜索下拉框css引入 -->
    <link rel="stylesheet" href="<%=basePath%>static/libs/searchableSelect/jquery.searchableSelect.css" />
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/labelMark.css">
    <script type="text/javascript" src='<%=basePath%>static/js/jquery.min.js'></script>
    <script type="text/javascript" src="<%=basePath%>static/js/jquery/validation/jquery-form.js"></script>
    <script type="text/javascript" src='<%=basePath%>static/libs/jquery/plugins/layer/layer.js'></script>
    <script type="text/javascript" src="<%=basePath%>static/libs/jquery/plugins/DatePicker/WdatePicker.js"></script>
    <script type="text/javascript" charset="UTF-8" src='<%=basePath%>static/js/form.js?rnd=${resourceVersionKey}'></script>
    <script type="text/javascript" charset="UTF-8" src='<%=basePath%>static/js/closable-tab.js?rnd=${resourceVersionKey}'></script>
    <script type="text/javascript" src="<%=basePath%>static/js/call/call.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src='<%= basePath %>static/js/movinghead.js?rnd=${resourceVersionKey}'></script>
    <!-- 模糊搜索下拉框js引入 -->
    <script type="text/javascript" src='<%=basePath%>static/libs/searchableSelect/jquery.searchableSelect.js'></script>
    <!-- 模糊搜索下拉框 -->
    <link rel="stylesheet" href="<%=basePath%>static/css/select2.css?rnd=${resourceVersionKey}" />
    <script type="text/javascript" src='<%= basePath %>static/js/select2.js?rnd=${resourceVersionKey}'></script>
    <script type="text/javascript" src='<%= basePath %>static/js/select2_locale_zh-CN.js?rnd=${resourceVersionKey}'></script>
    <script type="text/javascript" src='<%= basePath %>static/js/region/index.js?rnd=${resourceVersionKey}'></script>
    <script type="text/javascript" src='<%= basePath %>static/js/order/saleorder/saleorder_common.js?rnd=${resourceVersionKey}'></script>
    <script type="text/javascript" src='${pageContext.request.contextPath}/static/js/orderstream/saleorder/common.js?rnd=${resourceVersionKey}'></script>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/order/style.css">
    <%@ include file="../../component/remarkComponent.jsp"%>
    <script type="text/javascript">
        $(document).ready(function(){
            var now = getNow();
            watermark({"watermark_txt":"${ sessionScope.curr_user.username}"+now});

            // 终端信息-省 改变
            $('select[name="provinceId"]').change(function() {
                var selectedValue = $(this).val();
                setRegion(selectedValue, "orderTerminal-city");
                $("#orderTerminal-area").empty();
                $("#orderTerminal-area").html("<option value='0'>请选择</option>");
            });
            // 市
            $('select[name="cityId"]').change(function() {
                var selectedValue = $(this).val();
                setRegion(selectedValue, "orderTerminal-area");
            });
        })
    </script>
    <script type="text/javascript">
        $(function(){
            var saleorderId = $("input[name='saleorderId']").val();


            var url = page_url + '/orderstream/saleorder/edit.do?saleorderId=' + saleorderId;
            if ($(window.frameElement).attr('src').indexOf("saleorder/edit") < 0) {
                $(window.frameElement).attr('data-url', url);
            }
            //补订单产品详情相关数据
            $.ajax({
                async:true,
                url:page_url+'/order/saleorder/getsaleordergoodsextrainfo.do',
                data:{"saleorderId":saleorderId, "extraType":"order_saleorder"},//销售订单详情（占用，库存，采购状态，到库状态，发货状态，收货状态）
                type:"POST",
                dataType : "json",
                success:function(data){
                    if(data.code==0){
                        for (var i = 0; i < data.data.length; i++) {
                            //alert(data.data[i].saleorderGoodsId);
                            $("#orderOccupy_stockNum_"+data.data[i].saleorderGoodsId).html(data.data[i].goods.orderOccupy+"/"+data.data[i].goods.stockNum);
                            $("#kc_"+data.data[i].saleorderGoodsId).html(data.data[i].goods.stockNum);
                            $("#kykc_"+data.data[i].saleorderGoodsId).html(data.data[i].goods.stockNum-data.data[i].goods.orderOccupy);
                            $("#dzzy_"+data.data[i].saleorderGoodsId).html(data.data[i].goods.orderOccupy);
                            $("#ktj_"+data.data[i].saleorderGoodsId).html(data.data[i].goods.adjustableNum);
                        }
                    }else{
                        layer.alert(data.message);
                    }
                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            })
            //补订单产品详情相关数据
            $.ajax({
                async:true,
                url:page_url+'/orderstream/saleorder/getsaleordergoodsPriceInfo.do',
                data:{"saleorderId":saleorderId},//销售订单产品价格信息
                type:"POST",
                dataType : "json",
                success:function(data){
                    if(data.code==0){
                        var result = data.data
                        for (var key in result){
                            $(".skuNoAndPrice_"+key).html(result[key]);
                        }
                    }else{
                        layer.alert(data.message);
                    }
                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            })

        })


        function validateOrderCouponInfo(id){

            $.ajax({
                url: page_url + '/orderstream/saleorder/validCheckStatus.do',
                data: {"saleorderId": $("input[name='saleorderId']").val()},
                type: "POST",
                dataType: "json",
                async: false,
                success: function (data) {
                    if (data.code == 0) {

                        saveBeforeOperateSku();

                        if(${orderType != '1'}){
                            return $("#"+id).click();
                        }

                        if("${saleorder.isCoupons}" == "1"){

                            if(id.startsWith("editSaleOrderGood")){
                                return $("#"+id).click();
                            }else{
                                layer.alert("客户已选择优惠券，无法更改产品信息！");
                                return;
                            }
                        }

                        var hasCoupon = false;
                        var hasAuthority = true;

                        $.ajax({
                            url:page_url+'/order/saleorder/orderHasCouponInfo.do',
                            data:{"saleOrderId":"${saleorder.saleorderId}"},
                            type:"POST",
                            dataType : "json",
                            async: false,
                            success:function(data)
                            {
                                if(data.data == true){
                                    hasCoupon = true;
                                }
                            },
                            error:function(data){
                                if(data.status ==1001){
                                    hasAuthority = false;
                                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                                }
                            }
                        });

                        if(!hasAuthority){
                            return;
                        }

                        if(!hasCoupon){
                            return $("#"+id).click();
                        }

                        //有优惠券
                        layer.confirm("已使用优惠券，继续操作将清空已选择的优惠券？", {
                            btn : [ '确定', '取消' ]
                            //按钮
                        }, function() {
                            //清空优惠券
                            $.ajax({
                                url:page_url+'/order/saleorder/clearCoupon.do',
                                data:{"saleOrderId":"${saleorder.saleorderId}"},
                                type:"POST",
                                dataType : "json",
                                async: false,
                                success:function(data)
                                {
                                    layer.closeAll();
                                    window.location.reload();
                                },
                                error:function(data){
                                    if(data.status ==1001){
                                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                                    }
                                }
                            });

                        }, function() {

                        });
                    } else {
                        layer.alert(data.message);
                    }
                },
                error: function (data) {
                    if (data.status == 1001) {
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });

        }

        //选择优惠券
        function chooseCoupon() {
            //如果是BD订单且已经使用了优惠券的 就不让选择优惠券
            if(${orderType == '1' && saleorder.isCoupons == '1'}){
                layer.alert("客户已选择优惠券，无法更改优惠券信息！");
                return;
            }


            var trs = $("#goodsTbody tr");
            if(trs.length < 2){
                layer.alert("订单没有对应的产品信息，请添加产品后重试");
                return;
            }

            var skuNos = "";
            for(var i = 0;i <= trs.length - 2;i++){
                skuNos += trs.eq(i).find("td input:first").val();
                if(i != trs.length - 2){
                    skuNos += ",";
                }
            }


            $("#chooseCouponSpan").attr('layerparams','{"width":"700px","height":"550px","title":"选择优惠券","link":"/order/saleorder/chooseCoupon.do?saleorderId=${saleorder.saleorderId}&skuNos='+skuNos+'"}');
            $("#chooseCouponSpan").click();
        }

        /**
         * 查看客户所有额优惠券
         */
        function viewCustomAllCoupons() {
            $("#myCouponSpan").click();
        }

        /**
         * 选择优惠券的回调函数
         * @param couponId
         * @param denomination
         * @param useThreshold
         * @param limitTypeStr
         * @param effevtiveEndTime
         */
        function selectCoupon(couponInfo) {

            $.ajax({
                url:page_url+'/order/saleorder/selectedCoupon.do',
                data:{"saleOrderId":"${saleorder.saleorderId}","couponInfoStr":JSON.stringify(couponInfo)},
                type:"POST",
                dataType : "json",
                async: false,
                success:function(data)
                {
                    if(data.code == 0){
                        window.location.reload();
                    }else{
                        layer.alert(data.message);
                    }
                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });

        }

        $(function(){
            //联系人下拉搜索初始化
            $(".contact").select2();
            // 联系地址下拉搜索初始化
            $(".address").select2();
        });

        function setContentSelect(content,contact){
            $(contact).select2().val(content).trigger("change");
            $(contact).select2()
        }

    </script>
    <style>
        .select2-container .select2-choice .select2-arrow {
            background: white;
        }
    </style>
</head>
<body>



<div class="content mt10 ">
    <!-- 这一块内容是页头进度条 -->
    <div style="height: 60px;text-align: center">
        <div class="t-line-wrap J-line-wrap" data-json='${statusList}' style="margin-bottom: 20px;margin-top: 30px;margin-left: 8%;"></div>
    </div>
    <!-- end -->
    <!-- 这一块内容是测试用的模块，只需要绑定id -->
    <%--    <div class="block">模块1</div>--%>
    <%--    <div class="block">模块2</div>--%>
    <%--    <div class="block" id="J-test">模块3</div>--%>
    <%--    <div class="block">模块4</div>--%>
    <%--    <div class="block">模块5</div>--%>
    <!-- end -->
    <c:set var="orderType" value="${null == orderType ? -1 : orderType}"></c:set>
    <div class="parts">
        <div class="title-container title-container-blue">
            <div class="table-title nobor">基本信息</div>
            <input type="hidden" id="is_scientificDept" value="${isScientificDept}">
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <tbody>
            <tr>
                <td class="table-smaller">订单号</td>
                <td class="J-salerorder-no">${saleorder.saleorderNo}</td>
                <td class="table-smaller">订单状态</td>
                <td>
                    <c:choose>
                        <c:when test="${saleorder.status eq 0}">待确认</c:when>
                        <c:when test="${saleorder.status eq 1}">进行中</c:when>
                        <c:when test="${saleorder.status eq 2}">已完结</c:when>
                        <c:when test="${saleorder.status eq 3}">已关闭</c:when>
                        <c:when test="${saleorder.status eq 4}">待客户确认</c:when>
                        <c:otherwise></c:otherwise>
                    </c:choose>
                </td>
            </tr>
            <tr>
                <td>创建者</td>
                <td>${saleorder.creatorName}</td>
                <td>创建时间</td>
                <td><date:date value ="${saleorder.addTime}"/></td>
            </tr>
            <tr>
                <td>销售部门</td>
                <td>${saleorder.salesDeptName}</td>
                <td>归属销售</td>
                <td>${saleorder.optUserName}</td>
            </tr>
            <c:if test="${saleorder.bussinessChanceId ne null or saleorder.quoteorderId ne null}">
                <tr>
                    <c:if test="${saleorder.bussinessChanceId ne null && saleorder.bussinessChanceNo ne null}">
                        <td>商机编号</td>
                        <td>
                            <a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewsaleorder${saleorder.bussinessChanceId}","link":"./order/bussinesschance/toSalesDetailPage.do?bussinessChanceId=${saleorder.bussinessChanceId}","title":"商机信息"}'>${saleorder.bussinessChanceNo}</a>
                        </td>
                    </c:if>
                    <c:if test="${saleorder.quoteorderId ne null && saleorder.quoteorderNo ne null}">
                        <td></td>
                        <td>
                        </td>
                    </c:if>
                </tr>
            </c:if>
            <c:choose>
                <c:when test="${saleorder.orderType eq 5}">
                    <tr>
                        <td>用户确认收货时间</td>
                        <td colspan="3"><date:date value="${saleorder.webTakeDeliveryTime}" format="yyyy-MM-dd" /></td>
                    </tr>
                </c:when>
                <c:otherwise></c:otherwise>
            </c:choose>
            </tbody>
        </table>
    </div>
    <form action="${pageContext.request.contextPath}/orderstream/saleorder/saveeditsaleorderinfo.do" method="post" id="editForm">
        <input type="hidden" value="${saleorder.ownerUserId}" name="ownerUserId" id="ownerUserId"/>
        <input type="hidden" value="${orderType}" id="orderType" name="orderType">
        <div class="parts content1 " >
            <div class="title-container title-container-blue">
                <div class="table-title nobor">客户信息</div>
            </div>
            <%--                <div class="formtitle mt10">客户信息</div>--%>
            <div style="border: 1px solid #ddd;">

                <ul class="payplan visible" style="padding-top:10px">
                    <c:choose>
                        <c:when test="${saleorder.orderType eq 8 || saleorder.orderType eq 7}">
                            <li class="visible">
                                <div class="infor_name infor_name96 mt0">
                                    <span>*</span>
                                    <label>客户名称：</label>
                                </div>
                                <div class="f_left  customername pos_rel">

                            <span class="font-blue">
                            <a id="costomerLink" class="addtitle" href="javascript:void(0);"
                               tabTitle='{"num":"viewcustomer${customer.traderCustomerId}",
										"link":"./trader/customer/baseinfo.do?traderCustomerId=${customer.traderCustomerId}",
										"title":"客户信息"}'>
                                    ${customer.traderName}</a>
							</span>
                                </div>
                            </li>
                            <li>
                                <div class="infor_name infor_name96 mt0">
                                    <span></span>
                                    <label>客户类型：</label>
                                </div>
                                <div class="f_left">
                                    <label>${saleorder.customerTypeStr} ${saleorder.customerNatureStr}</label>
                                    <c:if test="${jcTraderContactDto.groupLevel eq 1}">
                                        <span class="group-label parent" style="margin-left:10px;float: right;">集团 总部</span>
                                    </c:if>
                                    <c:if test="${jcTraderContactDto.groupLevel eq 2}">
                                        <span class="group-label child" style="margin-left:10px;float: right;">集团 分院</span>
                                    </c:if>
                                </div>
                            </li>
                        </c:when>
                        <c:otherwise>
                            <li class="visible">
                                <div class="infor_name infor_name96 mt0">
                                    <span>*</span>
                                    <label>客户名称：</label>
                                </div>
                                <div class="f_left  customername pos_rel">
                                <span class="font-blue">
                                    <a class="addtitle" href="javascript:void(0);"
                                       tabTitle='{"num":"viewcustomer${customer.traderCustomerId}",
                                                "link":"./trader/customer/baseinfo.do?traderCustomerId=${customer.traderCustomerId}&traderId=${saleorder.traderId}",
                                                "title":"客户信息"}'>
                                            ${customer.traderName}
                                    </a>
                                </span>
                                </div>
                            </li>
                            <li>
                                <div class="infor_name infor_name96 mt0">
                                    <span></span>
                                    <label>客户类型：</label>
                                </div>
                                <div class="f_left">
                                    <label>${saleorder.customerTypeStr}&nbsp;&nbsp;${saleorder.customerNatureStr}</label>
                                </div>
                            </li>
                        </c:otherwise>
                    </c:choose>
                    <c:choose>
                        <c:when test="${saleorder.orderType eq 5}">
                            <li>
                                <div class="infor_name infor_name96">
                                    <span>*</span>
                                    <label>联系人：</label>
                                </div>
                                <div class="f_left">
                                    <input maxLength="64" type="text" id="traderContactName_5" name="traderContactName" value="${saleorder.traderContactName}" placeholder="请输入联系人" />
                                    <div id="5_traderContactNameMsg" style="clear:both"></div>
                                </div>
                            </li>
                            <li>
                                <div class="infor_name infor_name96">
                                    <span>*</span>
                                    <label>联系人手机号：</label>
                                </div>
                                <div class="f_left">
                                    <input maxLength="11" type="text" id="traderContactMobile_5" name="traderContactMobile" value="${saleorder.traderContactMobile}" placeholder="请输入联系人手机号" />
                                    <div id="5_traderContactMobileMsg" style="clear:both"></div>
                                </div>
                            </li>
                            <li>
                                <div class="infor_name infor_name96">
                                    <span>*</span>
                                    <label>联系地址：</label>
                                </div>
                                <div class="f_left">
                                    <select id="traderAddressId-province" onchange="changeArea('traderAddressId', 'traderArea_5', 1)" >
                                        <option value="0">请选择</option>
                                        <c:if test="${not empty provinceList }">
                                            <c:forEach items="${provinceList }" var="prov">
                                                <option value="${prov.regionId }" <c:if test="${traderAddressIdProvince eq prov.regionId }">selected="selected"</c:if> >${prov.regionName }</option>
                                            </c:forEach>
                                        </c:if>
                                    </select>
                                    <select id="traderAddressId-city" onchange="changeArea('traderAddressId', 'traderArea_5', 2)" >
                                        <option value="0">请选择</option>
                                        <c:if test="${not empty traderAddressIdCityList }">
                                            <c:forEach items="${traderAddressIdCityList }" var="cy">
                                                <option value="${cy.regionId }" <c:if test="${traderAddressIdCity eq cy.regionId }">selected="selected"</c:if> >${cy.regionName }</option>
                                            </c:forEach>
                                        </c:if>
                                    </select>
                                    <select id="traderAddressId-zone" onchange="changeArea('traderAddressId', 'traderArea_5', 3)" >
                                        <option value="0">请选择</option>
                                        <c:if test="${not empty traderAddressZoneList }">
                                            <c:forEach items="${traderAddressZoneList }" var="zo">
                                                <option value="${zo.regionId }" <c:if test="${traderAddressIdZone eq zo.regionId }">selected="selected"</c:if> >${zo.regionName }</option>
                                            </c:forEach>
                                        </c:if>
                                    </select>
                                    <input type="hidden" id="traderArea_5" name="traderArea" value="${saleorder.traderArea}" />
                                    <input type="hidden" id="traderAddressId" name="traderAreaId" value="${traderAddressIdZone}" />
                                    <input maxLength="256" class="input-xx" type="text" id="traderAddress_5" name="traderAddress" value="${saleorder.traderAddress}" placeholder="请输入联系地址" />
                                    <div id="5_traderAddressMsg" style="clear:both"></div>
                                </div>
                            </li>
                            <li>
                                <div class="infor_name infor_name96">
                                    <label>客户备注：</label>
                                </div>
                                <div class="f_left" style="max-width:1000px;">
                                    <span>${saleorder.traderComments}</span>
                                </div>
                            </li>
                        </c:when>
                        <c:otherwise>
                            <li>
                                <div class="infor_name infor_name96">
                                    <span>*</span>
                                    <label>联系人：</label>
                                </div>
                                <div class="f_left ">
                                    <select class="input-xx mr10 contact" name="traderContactId" id="trader_contact_3" onclick="triggerContactInfoChanged()">
                                        <option value="0">请输入联系人姓名或手机号</option>
                                        <c:if test="${not empty traderContactList}">
                                            <c:forEach items="${traderContactList}" var="list" varStatus="status">
                                                <option value="${list.traderContactId}" <c:if test="${list.traderContactId eq saleorder.traderContactId}">selected="selected"</c:if>>${list.name}/${list.telephone}/${list.mobile}</option>
                                            </c:forEach>
                                        </c:if>
                                    </select>
                                    <input type="hidden" name="traderContactName">
                                    <input type="hidden" name="traderContactTelephone">
                                    <input type="hidden" name="traderContactMobile">
                                    <span class="mt4 font-blue" onclick="addContact('./addContact.do?traderId=${saleorder.traderId}&traderCustomerId=${customer.traderCustomerId}&indexId=3')" style="cursor: pointer;">新增</span>
                                    <div id="traderContactIdMsg" style="clear:both"></div>
                                </div>
                            </li>
                            <c:if test="${saleorder.orderType eq 8 || saleorder.orderType eq 7}">
                                <li>
                                    <div class="infor_name infor_name96">
                                        <label>职位：</label>
                                    </div>
                                    <div class="f_left">
                                        <span id="contact_position_1">${jcTraderContactDto.positions}</span>
                                    </div>
                                    <div class="infor_name infor_name96">
                                        <label>可下单范围：</label>
                                    </div>
                                    <div class="f_left">
                                        <span id="allowed_goods_types_1" style="color:red;">${jcTraderContactDto.allowedGoodsTypes}</span>
                                    </div>
                                </li>
                            </c:if>
                            <li>
                                <div class="infor_name infor_name96">
                                    <span>*</span>
                                    <label>联系地址：</label>
                                </div>
                                <div class="f_left">
                                    <div>
                                        <select class="input-xx mr10 address" name="traderAddressId" id="address_3">
                                            <option value="0">请输入联系地址</option>
                                            <c:if test="${not empty traderAddressList}">
                                                <c:forEach items="${traderAddressList}" var="list" varStatus="status">
                                                    <option value="${list.traderAddress.traderAddressId}" <c:if test="${list.traderAddress.traderAddressId eq saleorder.traderAddressId}">selected="selected"</c:if>>${list.area}/${list.traderAddress.address}</option>
                                                </c:forEach>
                                            </c:if>
                                        </select>
                                        <input type="hidden" name="traderArea">
                                        <input type="hidden" name="traderAddress">
                                        <span class="mt4  font-blue" onclick="addAddress('./addAddress.do?traderId=${saleorder.traderId}&indexId=3')" style="cursor: pointer;">新增</span>
                                        <div id="traderAddressIdMsg" style="clear:both"></div>
                                    </div>
                                </div>
                            </li>
                        </c:otherwise>
                    </c:choose>
                </ul>
            </div>

        </div>
        <c:if test="${saleorder.customerNature eq 465}"><!-- 分销 -->
        <div id="quotePayMoneForm">
            <input type="hidden" value="${saleorder.customerNature}" id="saleCustomerNature">
            <input type="hidden" name="terminalTraderName" id="terminalTraderName" class="terminal" value="${saleorder.terminalTraderName}"/>
            <input type="hidden" name="terminalTraderId" id="terminalTraderId" class="terminal" value="${saleorder.terminalTraderId}"/>
            <input type="hidden" name="terminalTraderType" id="terminalTraderType" class="terminal" value="${saleorder.terminalTraderType}"/>
            <input type="hidden" name="salesArea" id="salesArea" class="terminal" value="${saleorder.salesArea}"/>
            <input type="hidden" name="salesAreaId" id="salesAreaId" class="terminal" value="${saleorder.salesAreaId}"/>

            <input type="hidden" name="dwhTerminalId" id="dwhTerminalId" class="orderTerminal" value="${orderTerminalDto.dwhTerminalId}"/>
            <input type="hidden" name="terminalName" class="orderTerminal" value="${orderTerminalDto.terminalName}"/>
            <input type="hidden" name="unifiedSocialCreditIdentifier" class="orderTerminal" id="unifiedSocialCreditIdentifier" value="${orderTerminalDto.unifiedSocialCreditIdentifier}"/>
            <input type="hidden" name="organizationCode" class="orderTerminal" id="organizationCode" value="${orderTerminalDto.organizationCode}"/>
<%--            <input type="hidden" name="provinceId" id="provinceId" class="orderTerminal" value="${orderTerminalDto.provinceId}"/>--%>
<%--            <input type="hidden" name="cityId" id="cityId" class="orderTerminal" value="${orderTerminalDto.cityId}"/>--%>
<%--            <input type="hidden" name="areaId" id="areaId" class="orderTerminal" value="${orderTerminalDto.areaId}"/>--%>
<%--            <input type="hidden" name="provinceName" id="provinceName" class="orderTerminal" value="${orderTerminalDto.provinceName}"/>--%>
<%--            <input type="hidden" name="cityName" id="cityName" class="orderTerminal" value="${orderTerminalDto.cityName}"/>--%>
<%--            <input type="hidden" name="areaName" id="areaName" class="orderTerminal" value="${orderTerminalDto.areaName}"/>--%>
        </div>

        <div class="parts" id="updateTerminalInfo" liname="终端信息" style="padding-top: 15px">
            <div class="title-container title-container-blue">
                <div class="table-title nobor">
                    终端信息
                </div>
                <div class="title-click nobor J-terminal-edit">添加终端</div>
            </div>
            <table class="table new-table new-ui">
                <colgroup>
                    <col width="240px">
                    <col width="120px">
                    <col width="100px">
                    <col width="130px">
                    <col width="200px">
                    <col width="356px">
                    <col width="44px">
                </colgroup>
                <thead>
                <tr>
                    <th>终端名称</th>
                    <th><span style="color: #e64545;">*</span>终端性质</th>
                    <th>终端联系人</th>
                    <th>终端联系电话<div class="new-tip-wrap">
                        <span class="vd-icon icon-info1"></span>
                        <div class="new-tip-cnt">可输入手机号或固定电话，固定电话需要输入区号</div>
                    </div></th>
                    <th>地区</th>
                    <th>详细地址</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody class="J-terminal-list-body"></tbody>
            </table>
        </div>
        <div class="goods-table-error J-terminal-table-error">
            <span class="vd-icon icon-error2"></span>
            <span class="goods-table-error-txt">请选择终端性质</span>
        </div>
        </c:if>

        <div class="parts" style="padding-top: 15px">
            <tags:saleorder_goods_info_edit saleorder="${saleorder}" saleorderGoodsList="${saleorderGoodsList}" newSkuInfosMap="${newSkuInfosMap}"
                                            terminalTypes="${terminalTypes}" regions="${regions}" skuNoAndPriceMap="${skuNoAndPriceMap}"

                                            realAmount="${saleorderDataInfo['realAmount']}"
                                            saleorderCoupon="${saleorderCoupon}" awardAmount="${saleorderDataInfo['awardAmount']}"/>
        </div>

        <c:choose>
            <c:when test="${(saleorder.orderType eq 9
            || (saleorder.orderType eq 5 && !empty saleorderCoupon))}">
                <div class="parts">
                    <div class="title-container title-container-blue">
                        <div class="table-title nobor">
                            促销信息
                        </div>
                        <div class="f_right  customername pos_rel">
                            <%--<a href="javascript:void(0);" onclick="chooseCoupon()">选择优惠券</a>--%>
                            <span class="mt4 pop-new-data font-blue" id="chooseCouponSpan" style="display: none;"></span>
                            <span class="edit-user addtitle" id="myCouponSpan"
                                  tabTitle='{"num":"customer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                                    "link":"./order/saleorder/getTraderCoupons.do?traderId=${saleorder.traderId}","title":"我的优惠券"}' style="display: none"></span>
                        </div>
                    </div>
                    <table class="table table-bordered table-striped table-condensed table-centered">
                        <thead>
                        <tr>
                            <th>优惠劵名称</th>
                            <th>优惠劵类型</th>
                            <th>优惠金额</th>
                            <th>使用门槛</th>
                            <th>品类限制</th>
                            <th>有效期</th>
                        </tr>
                        </thead>
                        <tbody>
                        <c:if test="${saleorderCoupon != null}">
                            <td>${saleorderCoupon.couponCode}</td>
                            <td>${saleorderCoupon.couponType eq 1 ? "满减卷":""}</td>
                            <td><fmt:formatNumber type="number" value="${saleorderCoupon.denomination}" pattern="0.00" maxFractionDigits="2" /></td>
                            <td><fmt:formatNumber type="number" value="${saleorderCoupon.useThreshold}" pattern="0.00" maxFractionDigits="2" /></td>
                            <td>${saleorderCoupon.limitTypeStr == null ? '-' : saleorderCoupon.limitTypeStr}</td>
                            <td>
                                <date:date value ="${saleorderCoupon.effevtiveStartTime} " format="yyyy-MM-dd"/>
                                ~
                                <date:date value ="${saleorderCoupon.effevtiveEndTime} " format="yyyy-MM-dd"/>
                            </td>
                        </c:if>
                        </tbody>
                    </table>
                </div>
            </c:when>
            <c:when test="${!(orderType eq 8 || orderType eq 9 || orderType eq 7 || orderType eq 5)}">
                <%--集采订单不展示优惠卷信息--%>
                <div class="parts" id="couponsInfo">
                    <div class="title-container title-container-blue">
                        <div class="table-title nobor">优惠券信息</div>
                    </div>
                    <div style="border: 1px solid #ddd; padding-top:10px">

                        <ul class="payplan" style="padding:10px">
                            <li class="visible">
                                <div class="f_left  customername pos_rel">
                                    &nbsp;&nbsp;&nbsp;
                                    <%--<a href="javascript:void(0);" onclick="chooseCoupon()">选择优惠券</a>--%>
                                    <span class="mt4 pop-new-data font-blue" id="chooseCouponSpan" style="display: none;"></span>
                                    <span class="edit-user addtitle" id="myCouponSpan"
                                          tabTitle='{"num":"customer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                                "link":"./order/saleorder/getTraderCoupons.do?traderId=${saleorder.traderId}","title":"我的优惠券"}' style="display: none">我的优惠券</span>
                                </div>
                            </li>
                            <li>
                                <div class="f_left" id="couponInfo">
                                    <c:if test="${saleorderCoupon != null}">
                                        ${saleorderCoupon.denomination}元
                                        满${saleorderCoupon.useThreshold}减${saleorderCoupon.denomination}元
                                        ${saleorderCoupon.limitTypeStr}
                                        有效期至:${saleorderCoupon.effevtiveEndTime}
                                    </c:if>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </c:when>
            <c:otherwise></c:otherwise>
        </c:choose>

        <div class="parts " style="padding-top: 15px">
            <div class="title-container title-container-blue">
                <div class="table-title nobor">收货信息 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <c:if test="${type eq 1}">
                        <span style="color: red">GE订单，【收货信息】需正确填写终端用户信息</span>
                    </c:if>
                </div>
            </div>
            <div style="border: 1px solid #ddd;padding-top:10px">
                <ul class="payplan" style="padding:10px">
                    <li>
                        <div class="infor_name infor_name96">
                            <span>*</span>
                            <label>收货客户：</label>
                        </div>
                        <c:choose>
                            <c:when test="${saleorder.orderType eq 8 || saleorder.orderType eq 7}">
                                <div class="f_left inputfloat">
                                        <%--<span class=" mr10 mt3" id="trader_name_span_1">${saleorder.takeTraderName}</span>--%>
                                    <input type="hidden" name="takeTraderId" id="trader_id_1" value="${saleorder.takeTraderId}">
                                    <input type="hidden" name="takeTraderName" id="trader_name_1" value="${saleorder.takeTraderName}">
                                    <select class="input-xx" id="take_trader_1" lay-search onclick="changeTakeTraderFilter();isSameAddressChecked()">
                                        <option value="0">请选择</option>
                                        <c:if test="${not empty groupCustomerList}">
                                            <c:forEach items="${groupCustomerList}" var="list" varStatus="status">
                                                <option value="${list.traderId}" <c:if test="${list.traderId eq saleorder.takeTraderId}">selected="selected"</c:if> >${list.traderName}</option>
                                            </c:forEach>
                                        </c:if>
                                    </select>
                                    <div id="takeTraderIdMsg" style="clear:both"></div>
                                </div>
                            </c:when>
                            <c:otherwise>
                                <div class="f_left  customername pos_rel">
                                    <input type="hidden" name="takeTraderId" id="trader_id_1" value="${saleorder.takeTraderId}">
                                    <input type="hidden" name="takeTraderName" id="trader_name_1" value="${saleorder.takeTraderName}">
                                    <span class="font-blue">
                                        <a class="addtitle" href="javascript:void(0);"
                                           tabTitle='{"num":"viewcustomer${takeCustomer.traderCustomerId}",
                                                    "link":"./trader/customer/baseinfo.do?traderCustomerId=${takeCustomer.traderCustomerId}&traderId=${(saleorder.takeTraderId == null || saleorder.takeTraderId eq 0)?saleorder.traderId:saleorder.takeTraderId}",
                                                    "title":"客户信息"}'>
                                                ${saleorder.takeTraderName}
                                        </a>
                                            </span>
                                </div>
                            </c:otherwise>
                        </c:choose>
                    </li>
                    <c:choose>
                        <c:when test="${saleorder.orderType eq 5}">
                            <li>
                                <div class="infor_name infor_name96">
                                    <span>*</span>
                                    <label>收货联系人：</label>
                                </div>
                                <div class="f_left">
                                    <input maxLength="64" type="text" id="takeTraderContactName_5" name="takeTraderContactName" value="${saleorder.takeTraderContactName}" placeholder="请输入收货联系人" />
                                    <div id="5_takeTraderContactName" style="clear:both"></div>
                                </div>
                            </li>
                            <li>
                                <div class="infor_name infor_name96">
                                    <span>*</span>
                                    <label>收货手机号：</label>
                                </div>
                                <div class="f_left">
                                    <input maxLength="11" type="text" id="takeTraderContactMobile_5" name="takeTraderContactMobile" value="${saleorder.takeTraderContactMobile}" placeholder="请输入收货手机号" />
                                    <div id="5_takeTraderContactMobile" style="clear:both"></div>
                                </div>
                            </li>
                            <li>
                                <div class="infor_name infor_name96">
                                    <span>*</span>
                                    <label>收货地址：</label>
                                </div>
                                <div class="f_left">
                                    <select  id="takeTraderAddressId-province" onchange="changeArea('takeTraderAddressId', 'takeTraderArea_5', 1);isSameAddressChecked()" >
                                        <option value="0">请选择</option>
                                        <c:if test="${not empty provinceList }">
                                            <c:forEach items="${provinceList }" var="prov">
                                                <option value="${prov.regionId }" <c:if test="${takeTraderAddressIdProvince eq prov.regionId }">selected="selected"</c:if> >${prov.regionName }</option>
                                            </c:forEach>
                                        </c:if>
                                    </select>
                                    <select  id="takeTraderAddressId-city" onchange="changeArea('takeTraderAddressId', 'takeTraderArea_5', 2);isSameAddressChecked()">
                                        <option value="0">请选择</option>
                                        <c:if test="${not empty takeTraderAddressCityList }">
                                            <c:forEach items="${takeTraderAddressCityList }" var="cy">
                                                <option value="${cy.regionId }" <c:if test="${takeTraderAddressIdCity eq cy.regionId }">selected="selected"</c:if> >${cy.regionName }</option>
                                            </c:forEach>
                                        </c:if>
                                    </select>
                                    <select  id="takeTraderAddressId-zone" onchange="changeArea('takeTraderAddressId', 'takeTraderArea_5', 3);isSameAddressChecked()">
                                        <option value="0">请选择</option>
                                        <c:if test="${not empty takeTraderAddressZoneList }">
                                            <c:forEach items="${takeTraderAddressZoneList }" var="zo">
                                                <option value="${zo.regionId }" <c:if test="${takeTraderAddressIdZone eq zo.regionId }">selected="selected"</c:if> >${zo.regionName }</option>
                                            </c:forEach>
                                        </c:if>
                                    </select>
                                    <input type="hidden" id="takeTraderArea_5" name="takeTraderArea" value="${saleorder.takeTraderArea}" />
                                    <input type="hidden" id="takeTraderAddressId" name="takeTraderAreaId" value="${takeTraderAddressIdZone}" />
                                    <input maxLength="256" class="input-xx" id="takeTraderAddress_5" type="text" oninput="isSameAddressChecked()" name="takeTraderAddress" value="${saleorder.takeTraderAddress}" placeholder="请输入收货地址" />
                                    <div id="5_takeTraderAddress" style="clear:both"></div>
                                </div>
                            </li>
                        </c:when>
                        <c:otherwise>
                            <li>
                                <div class="infor_name infor_name96">
                                    <span>*</span>
                                    <label>收货联系人：</label>
                                </div>
                                <div class="f_left">
                                    <select class="input-xx contact" id="trader_contact_1" name="takeTraderContactId">
                                        <option value="0">请输入联系人姓名或手机号</option>
                                        <c:if test="${not empty takeTraderContactList}">
                                            <c:forEach items="${takeTraderContactList}" var="list" varStatus="status">
                                                <option value="${list.traderContactId}"
                                                        <c:if test="${list.traderContactId eq saleorder.takeTraderContactId}">selected="selected"</c:if>>${list.name}/${list.telephone}/${list.mobile}</option>
                                            </c:forEach>
                                        </c:if>
                                    </select>
                                    <input type="hidden" name="takeTraderContactName">
                                    <input type="hidden" name="takeTraderContactTelephone">
                                    <input type="hidden" name="takeTraderContactMobile">
                                    <span class="mt4 font-blue" id="add_contact_1" onclick="addContact('./addContact.do?traderId=${saleorder.takeTraderId}&traderCustomerId=${takeTraderCustomerInfo.traderCustomerId}&indexId=1')" style="cursor: pointer;">新增</span>
                                    <div id="takeTraderContactIdMsg" style="clear:both"></div>
                                </div>
                            </li>
                            <li>
                                <div class="infor_name infor_name96">
                                    <span>*</span>
                                    <label>收货地址：</label>
                                </div>
                                <div class="f_left">
                                    <select class="input-xx address" id="address_1" name="takeTraderAddressId" onclick="isSameAddressChecked();">
                                        <option value="0">请输入收货地址</option>
                                        <c:if test="${not empty takeTraderAddressList}">
                                            <c:forEach items="${takeTraderAddressList}" var="list" varStatus="status">
                                                <option value="${list.traderAddress.traderAddressId}" <c:if test="${list.traderAddress.traderAddressId eq saleorder.takeTraderAddressId}">selected="selected"</c:if>>${list.area}/${list.traderAddress.address}</option>
                                            </c:forEach>
                                        </c:if>
                                    </select>
                                    <input type="hidden" name="takeTraderArea">
                                    <input type="hidden" name="takeTraderAddress">
                                    <span class="mt4  font-blue" id="add_address_1" onclick="addAddress('./addAddress.do?traderId=${saleorder.takeTraderId}&indexId=1')" style="cursor: pointer;">新增</span>
                                    <div id="takeTraderAddressIdMsg" style="clear:both"></div>
                                </div>
                            </li>
                        </c:otherwise>
                    </c:choose>
                    <li>
                        <div class="infor_name infor_name96">
                            <span>*</span>
                            <label>发货方式：</label>
                        </div>
                        <div class="f_left inputfloat">
                            <select class="input-middle" name="deliveryType" id="deliveryTypeSelect"
                                    <c:if test="${saleorder.status == 1 and (saleorder.deliveryStatus != 0 or saleorder.arrivalStatus != 0)}"> disabled="disabled" </c:if>>
                                <c:choose>
                                    <c:when test="${saleorder.orderType eq 8 || saleorder.orderType eq 7}">
                                        <option value="0" <c:if test="${saleorder.deliveryType == 0 or saleorder.deliveryType == null}">selected</c:if>>请选择</option>
                                        <c:forEach var="list" items="${deliveryTypes}">
                                            <option value="${list.sysOptionDefinitionId}"
                                                    <c:if test="${saleorder.deliveryType == list.sysOptionDefinitionId}">selected="selected"</c:if>>${list.title}</option>
                                        </c:forEach>
                                    </c:when>
                                    <c:otherwise>
                                        <c:forEach var="list" items="${deliveryTypes}">
                                            <option value="${list.sysOptionDefinitionId}"
                                                    <c:if test="${saleorder.deliveryType == list.sysOptionDefinitionId}">selected="selected"</c:if>
                                                    <c:if test="${(saleorder.deliveryType == 0 or saleorder.deliveryType == null) and list.title == '分批发货'}">selected="selected"</c:if>>${list.title}</option>
                                        </c:forEach>
                                    </c:otherwise>
                                </c:choose>

                            </select>
                        </div>
                        <div id="deliveryTypeSelectMsg" style="clear:both"></div>
                    </li>
                    <li>
                        <div class="infor_name infor_name96">
                            <span>*</span>
                            <label>发货要求：</label>
                        </div>
                        <div class="f_left inputfloat">
                            <select class="input-middle" name="deliveryClaim" id="deliveryClaimSelect" onchange="deliveryClaimChange();"
                                    <c:if test="${saleorder.status == 1 and (saleorder.deliveryStatus != 0 or saleorder.arrivalStatus != 0)}"> disabled="disabled" </c:if>>
                                <option value="0"
                                        <c:if test="${saleorder.deliveryClaim == 0}">selected="selected"</c:if>>立即发货</option>
                                <option value="1"
                                        <c:if test="${saleorder.deliveryClaim == 1}">selected="selected"</c:if>>等通知发货</option>
                            </select>
                        </div>
                        <div id = "waitDeadlineDiv">
                            <div class="infor_name infor_name96">
                                <span>*</span>
                                <label>等待截止日：</label>
                            </div>
                            <div class="f_left inputfloat">
                                <input class="Wdate f_left input-smaller96 mr5" autocomplete="off" type="text" placeholder="请选择日期"
                                <c:if test="${saleorder.status == 1 and (saleorder.deliveryStatus != 0 or saleorder.arrivalStatus != 0)}"> disabled="disabled" </c:if>
                                       onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-{%d+1}',maxDate:'%y-%M-{%d+91}'})" autocomplete="off"
                                       name="deliveryDelayTimeStr" id="deliveryDelayTimeStr" value='<date:date value ="${saleorder.deliveryDelayTime}" format="yyyy-MM-dd"/>' style="width:240px;" >
                            </div>
                            <div id="deliveryDelayTimeStrMsg" style="clear:both;"></div>
                        </div>

                    </li>

                    <c:choose>
                        <c:when test="${saleorder.isPrintout == -1}">
                            <li id="is_print_li">
                                <di id="print_out_order">
                                    <div class="infor_name infor_name96">
                                        <span>*</span>
                                        <label>随货出库单：</label>
                                    </div>
                                    <div  style="display: inline-block">
                                        <select  class="input-middle" id="is_print" onchange="changeIsPrintout()" >
                                            <option value="-1">请选择</option>
                                            <option value="1">打印</option>
                                            <option value="2">不打印</option>
                                        </select>
                                    </div>
                                </di>
                                <input type="hidden" id="is_scientificDept" value="${isScientificDep}">
                                <input type="hidden" id="isYxgOrgFlag" value="${isYxgOrgFlag}">

                                <div id="isPrintoutMsg" style="clear:both;"></div>
                                <div id="isPriceMsg" ></div>
                                <input id="is_printout" type="hidden" name="isPrintout" value="${saleorder.isPrintout}">
                            </li>
                        </c:when>

                        <c:when test="${saleorder.isPrintout == 0}">
                            <li id="is_print_li">
                                <di id="print_out_order">
                                    <div class="infor_name infor_name96">
                                        <span>*</span>
                                        <label>随货出库单：</label>
                                    </div>
                                    <div  style="display: inline-block">
                                        <select  class="input-middle" id="is_print" onchange="changeIsPrintout()" >
                                            <option value="-1">请选择</option>
                                            <option value="1">打印</option>
                                            <option value="2" selected = selected>不打印</option>
                                        </select>
                                    </div>
                                </di>
                                <input type="hidden" id="is_scientificDept" value="${isScientificDep}">
                                <input type="hidden" id="isYxgOrgFlag" value="${isYxgOrgFlag}">
                                <div id="isPrintoutMsg"></div>
                                <div id="isPriceMsg" ></div>
                                <input id="is_printout" type="hidden" name="isPrintout" value="${saleorder.isPrintout}">
                            </li>
                        </c:when>
                        <c:when test="${saleorder.isPrintout == 1}">

                            <li id="is_print_li">
                                <di id="print_out_order">
                                    <div class="infor_name infor_name96">
                                        <span>*</span>
                                        <label>随货出库单：</label>
                                    </div>
                                    <div  style="display: inline-block">
                                        <select  class="input-middle" id="is_print" onchange="changeIsPrintout()" >
                                            <option value="-1">请选择</option>
                                            <option value="1" selected = selected>打印</option>
                                            <option value="2" >不打印</option>
                                        </select>
                                    </div>
                                    <div id = "print_price" style="display: inline-block">
                                        <select  id='is_price' name = "isPrintout" class="input-middle"   onchange='changeIsPrice()' >
                                            <option value="0">请选择</option>
                                            <option value="1" selected = selected>含价格</option>
                                            <option value="2">不含价格</option>
                                        </select>
                                    </div>
                                </di>
                            </li>
                            <input type="hidden" id="is_scientificDept" value="${isScientificDep}">
                            <input type="hidden" id="isYxgOrgFlag" value="${isYxgOrgFlag}">
                            <div id="isPrintoutMsg"></div>
                            <div id="isPriceMsg"></div>
                            <input id="is_printout" type="hidden" name="isPrintout" value="${saleorder.isPrintout}">
                        </c:when>

                        <c:when test="${saleorder.isPrintout == 2}">

                            <li id="is_print_li">
                                <div id="print_out_order">
                                    <div class="infor_name infor_name96">
                                        <span>*</span>
                                        <label>随货出库单：</label>
                                    </div>
                                    <div  style="display: inline-block">
                                        <select  class="input-middle" id="is_print" onchange="changeIsPrintout()" >
                                            <option value="-1">请选择</option>
                                            <option value="1" selected = selected>打印</option>
                                            <option value="2" >不打印</option>
                                        </select>
                                    </div>
                                    <div id = "print_price" style="display: inline-block">
                                        <select  id='is_price' name = "isPrintout" class="input-middle"  onchange='changeIsPrice()' >
                                            <option value="0">请选择</option>
                                            <option value="1" >含价格</option>
                                            <option value="2" selected = selected>不含价格</option>
                                        </select>
                                    </div>
                                </div>
                                <input type="hidden" id="is_scientificDept" value="${isScientificDep}">
                                <input type="hidden" id="isYxgOrgFlag" value="${isYxgOrgFlag}">
                                <div id="isPrintoutMsg"></div>
                                <div id="isPriceMsg" ></div>
                                <input id="is_printout" type="hidden" name="isPrintout" value="${saleorder.isPrintout}">
                            </li>
                        </c:when>

                        <c:when test="${saleorder.isPrintout == 3}">

                            <li id="is_print_li">
                                <div id="print_out_order">
                                    <div class="infor_name infor_name96">
                                        <span>*</span>
                                        <label>随货出库单：</label>
                                    </div>
                                    <div  style="display: inline-block">
                                        <select  class="input-middle" id="is_print" onchange="changeIsPrintout()" >
                                            <option value="-1">请选择</option>
                                            <option value="1" selected = selected>打印</option>
                                            <option value="2" >不打印</option>
                                        </select>
                                    </div>
                                </div>
                                <input type="hidden" id="is_scientificDept" value="${isScientificDep}">
                                <input type="hidden" id="isYxgOrgFlag" value="${isYxgOrgFlag}">
                                <div id="isPrintoutMsg" style="clear:both;"></div>
                                <input id="is_printout" type="hidden" name="isPrintout" value="${saleorder.isPrintout}">
                            </li>
                        </c:when>
                        <c:when test="${saleorder.isPrintout == 4}">

                            <li id="is_print_li">
                                <div id="print_out_order">
                                    <div class="infor_name infor_name96">
                                        <span>*</span>
                                        <label>随货出库单：</label>
                                    </div>
                                    <div  style="display: inline-block">
                                        <select  class="input-middle" id="is_print" onchange="changeIsPrintout()" >
                                            <option value="-1">请选择</option>
                                            <option value="1" selected = selected>打印</option>
                                            <option value="2" >不打印</option>
                                        </select>
                                    </div>
                                    <div id = "print_price" style="display: inline-block">
                                        <select  id='is_price' name = "isPrintout" class="input-middle" onchange='changeIsPrice()' >
                                            <option value="0">请选择</option>
                                            <option value="1" >含价格</option>
                                            <option value="2">不含价格</option>
                                        </select>
                                    </div>
                                </div>
                                <input type="hidden" id="is_scientificDept" value="${isScientificDep}">
                                <input type="hidden" id="isYxgOrgFlag" value="${isYxgOrgFlag}">
                                <div id="isPrintoutMsg"></div>
                                <div id="isPriceMsg" ></div>
                                <input id="is_printout" type="hidden" name="isPrintout" value="${saleorder.isPrintout}">
                            </li>
                        </c:when>
                        <c:otherwise>
                            <li id="is_print_li">
                                <div id="print_out_order">
                                    <div class="infor_name infor_name96">
                                        <span>*</span>
                                        <label>随货出库单：</label>
                                    </div>
                                    <div  style="display: inline-block">
                                        <select  class="input-middle" id="is_print" onchange="changeIsPrintout()" >
                                            <option value="-1">请选择</option>
                                            <option value="1" >打印</option>
                                            <option value="2" >不打印</option>
                                        </select>
                                    </div>
                                </div>
                                <input type="hidden" id="is_scientificDept" value="${isScientificDep}">
                                <input type="hidden" id="isYxgOrgFlag" value="${isYxgOrgFlag}">
                                <div id="isPrintoutMsg" style="clear:both;"></div>
                                <div id="isPriceMsg" ></div>
                                <input id="is_printout" type="hidden" name="isPrintout" value="${saleorder.isPrintout}">
                            </li>
                        </c:otherwise>
                    </c:choose>

                    <li style="display:none">
                        <div class="infor_name infor_name96">
                            <label>指定物流公司：</label>
                        </div>
                        <div class="f_left inputfloat">
                            <select class="input-middle" name="logisticsId">
                                <option value="0">请选择</option>
                                <c:forEach var="list" items="${logisticsList}">
                                    <c:if test="${list.isEnable == 1}">
                                        <option value="${list.logisticsId}" <c:if test="${saleorder.logisticsId == list.logisticsId}">selected="selected"</c:if> >${list.name}</option>
                                    </c:if>
                                </c:forEach>
                            </select>
                        </div>
                    </li>
                    <li>
                        <div class="infor_name infor_name96">
                            <label>运费说明：</label>
                        </div>
                        <div class="f_left inputfloat">
                            <select class="input-middle" name="freightDescription">
                                <option value="0">请选择</option>
                                <c:forEach var="list" items="${freightDescriptions}">
                                    <c:choose>
                                        <c:when test="${(saleorder.freightDescription == null ||saleorder.freightDescription eq 0) && saleorder.orderType eq 5}">
                                            <option value="${list.sysOptionDefinitionId}" <c:if test="${470 eq list.sysOptionDefinitionId}">selected="selected"</c:if> >${list.title}</option>
                                        </c:when>
                                        <c:otherwise>
                                            <option value="${list.sysOptionDefinitionId}" <c:if test="${saleorder.freightDescription == list.sysOptionDefinitionId}">selected="selected"</c:if> >${list.title}</option>
                                        </c:otherwise>
                                    </c:choose>
                                </c:forEach>
                            </select>
                        </div>
                    </li>
                    <li>

                        <div class="infor_name infor_name96">
                            <label>物流备注：</label>
                        </div>
                        <div class="f_left inputfloat">
                            <input type="text" name="logisticsComments" id="logisticsComments" value="${saleorder.logisticsComments}" placeholder="对内使用，适用于向物流部同事告知发货要求，默认同步客户信息中物流备注" class="input-xx" />
                        </div>
                    </li>
                </ul>
            </div>
        </div>
        <div class="parts content1 content2" style="padding-top: 15px">
            <div class="title-container title-container-blue">
                <div class="table-title nobor">
                    收票信息
                </div>
            </div>
            <div style="border: 1px solid #ddd;padding-top:10px">
                <ul class="payplan" style="padding:10px">
<%--                    <li>--%>
<%--                        <div class="infor_name infor_name96">--%>
<%--                            <span>*</span>--%>
<%--                            <label>发票是否寄送：</label>--%>
<%--                        </div>--%>
<%--                        <div class="f_left inputfloat">--%>
<%--                            <input type="radio" name="isSendInvoice" onclick="isSendInvoiceChecked('${orderType}', 1)"--%>
<%--                                   <c:if test="${saleorder.isSendInvoice eq 1}">checked</c:if> value="1">--%>
<%--                            <label>寄送</label> &nbsp;&nbsp;--%>
<%--                            <input type="radio" name="isSendInvoice" onclick="isSendInvoiceChecked('${orderType}',0)"--%>
<%--                                   <c:if test="${saleorder.isSendInvoice eq 0}">checked</c:if> value="0">--%>
<%--                            <label>不寄送</label>--%>
<%--                        </div>--%>
<%--                    </li>--%>
                    <li>
                        <input id="isSendInvoice" type="hidden" name="isSendInvoice" value="${saleorder.isSendInvoice}">
                    </li>
                    <%--<c:if test="${saleorder.orderType eq 8 || saleorder.orderType eq 9 || saleorder.orderType eq 5 || saleorder.orderType eq 7}">--%>
                        <%--集采线下和线下直销，新增是否票货同行选项--%>
                        <li id="isSameAddressLi">
                            <div class="infor_name infor_name96">
                                <span>*</span>
                                <label>是否票货同行：</label>
                            </div>
                            <div class="f_left inputfloat customername pos_rel">
                                <input type="radio" name="isSameAddress" onclick="isSameAddressChecked(1)"
                                       <c:if test="${saleorder.isSameAddress eq 1}">checked</c:if> value="1" lay-ignore>
                                <label>票货同行</label> &nbsp;&nbsp;
                                <i class="iconbluesigh ml4 contorlIcon"></i>
                                <div class="pos_abs customernameshow">
                                    票货同行规则：<br>
                                    1、发票是否寄送：寄送<br>
                                    2、是否票货同行：票货同行<br>
                                    3、开票方式：自动电子发票/自动数电发票<br>
                                    4、订单中全部商品的发货方式为“普发”<br>
                                    5、订单无退票、退货售后单<br>
                                </div>
                                <input type="radio" name="isSameAddress" onclick="isSameAddressChecked(0)"
                                       <c:if test="${saleorder.isSameAddress eq 0 or saleorder.isSameAddress == null}">checked</c:if> value="0" lay-ignore>
                                <label>票货不同行</label>
                                <div id="isSameAddressMsg" style="clear:both"></div>
                            </div>
                        </li>
                        <li id="invoiceSendNodeLi">
                            <div class="infor_name infor_name96">
                                <span>*</span>
                                <label>发票寄送节点：</label>
                            </div>
                            <div class="f_left inputfloat">
                                <input type="radio" name="invoiceSendNode" onclick=""
                                       <c:if test="${saleorder.invoiceSendNode eq 0}">checked</c:if> value="0" lay-ignore>
                                <label>全部发货时一次寄送</label> &nbsp;
                                <input type="radio" name="invoiceSendNode" onclick=""
                                       <c:if test="${saleorder.invoiceSendNode eq 1}">checked</c:if> value="1" lay-ignore>
                                <label>每次发货时分别寄送</label>
                                <div id="invoiceSendNodeMsg" style="clear:both"></div>
                            </div>
                            <label style="color: red; font-weight:bold">（只针对"票货同行"订单）</label>
                        </li>
                    <%--</c:if>--%>
                    <li id="invoiceCustomerLi">
                        <div class="infor_name infor_name96">
                            <span>*</span>
                            <label>收票客户：</label>
                        </div>
                        <c:choose>
                            <c:when test="${saleorder.orderType eq 8 || saleorder.orderType eq 7}">
                                <div class="f_left inputfloat" id="chooseInvoiceTraderPart">
                                        <%--<span class="mr10 mt3" id="trader_name_span_2">${saleorder.invoiceTraderName}</span>--%>
                                    <input type="hidden" name="invoiceTraderId" id="trader_id_2" value="${saleorder.invoiceTraderId}">
                                    <input type="hidden" name="invoiceTraderName" id="trader_name_2" value="${saleorder.invoiceTraderName}">
                                    <div class="layui-form-item">
                                        <div class="layui-inline">
                                            <select class="input-xx" id="take_trader_2" lay-search onclick="changeInvoiceTraderFilter()">
                                                <option value="0">请选择</option>
                                                <c:if test="${not empty groupCustomerList}">
                                                    <c:forEach items="${groupCustomerList}" var="list" varStatus="status">
                                                        <option value="${list.traderId}" <c:if test="${list.traderId eq saleorder.invoiceTraderId}">selected="selected"</c:if> >${list.traderName}</option>
                                                    </c:forEach>
                                                </c:if>
                                            </select>
                                        </div>
                                        <div class="layui-inline" >
                                            <span style="color: red; line-height: 26px;">收票客户即为订单结算主体！订单付款、发票抬头、合同甲方都为收票客户！</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="f_left" id="syncInvoiceTrader">
                                    <input class="input-xx" id="showInvoiceTrader" readonly="readonly">
                                </div>
                                <div id="invoiceTraderIdMsg" style="clear:both"></div>
                            </c:when>
                            <c:otherwise>
                                <div class="f_left  customername pos_rel">
                                    <input type="hidden" name="invoiceTraderId" id="trader_id_2" value="${saleorder.invoiceTraderId}">
                                    <input type="hidden" name="invoiceTraderName" id="trader_name_2" value="${saleorder.invoiceTraderName}">
                                    <span class="font-blue">
                                            <a class="addtitle" href="javascript:void(0);"
                                               tabTitle='{"num":"viewcustomer${invoiceCustomer.traderCustomerId}",
                                                        "link":"./trader/customer/baseinfo.do?traderCustomerId=${invoiceCustomer.traderCustomerId}&traderId=${(saleorder.invoiceTraderId == null || saleorder.invoiceTraderId eq 0)?saleorder.traderId:saleorder.invoiceTraderId}",
                                                        "title":"客户信息"}'>
                                                    ${saleorder.invoiceTraderName}
                                            </a>
                                            </span>
                                </div>
                            </c:otherwise>
                        </c:choose>
                    </li>
                    <c:choose>
                        <c:when test="${saleorder.orderType eq 5}">
                            <li id="invoiceTraderContactLi">
                                <div class="infor_name infor_name96">
                                    <span>*</span>
                                    <label>收票联系人：</label>
                                </div>
                                <div class="f_left">
                                    <input maxLength="64" type="text" id="invoiceTraderContactName_5" name="invoiceTraderContactName" value="${saleorder.invoiceTraderContactName}" placeholder="请输入收票联系人" />
                                    <div id="5_invoiceTraderContactName" style="clear:both"></div>
                                </div>
                            </li>
                            <li id="invoiceTraderContactMobileLi">
                                <div class="infor_name infor_name96">
                                    <span>*</span>
                                    <label>收票手机号：</label>
                                </div>
                                <div class="f_left">
                                    <input maxLength="11" type="text" id="invoiceTraderContactMobile_5" name="invoiceTraderContactMobile" value="${saleorder.invoiceTraderContactMobile}" placeholder="请输入收票手机号" />
                                    <div id="5_invoiceTraderContactMobile" style="clear:both"></div>
                                </div>
                            </li>
                            <li id="invoiceTraderAddressLi" style="display: none;">
                                <div class="infor_name infor_name96">
                                    <span>*</span>
                                    <label>收票地址：</label>
                                </div>
                                <div class="f_left">
                                    <select id="invoiceTraderAddressId-province" onchange="changeArea('invoiceTraderAddressId', 'invoiceTraderArea_5', 1)" >
                                        <option value="0">请选择</option>
                                        <c:if test="${not empty provinceList }">
                                            <c:forEach items="${provinceList }" var="prov">
                                                <option value="${prov.regionId }" <c:if test="${invoiceTraderAddressIdProvince eq prov.regionId }">selected="selected"</c:if> >${prov.regionName }</option>
                                            </c:forEach>
                                        </c:if>
                                    </select>
                                    <select id="invoiceTraderAddressId-city" onchange="changeArea('invoiceTraderAddressId', 'invoiceTraderArea_5', 2)" >
                                        <option value="0">请选择</option>
                                        <c:if test="${not empty invoiceCityList }">
                                            <c:forEach items="${invoiceCityList }" var="cy">
                                                <option value="${cy.regionId }" <c:if test="${invoiceTraderAddressIdCity eq cy.regionId }">selected="selected"</c:if> >${cy.regionName }</option>
                                            </c:forEach>
                                        </c:if>
                                    </select>
                                    <select id="invoiceTraderAddressId-zone" onchange="changeArea('invoiceTraderAddressId', 'invoiceTraderArea_5', 3)" >
                                        <option value="0">请选择</option>
                                        <c:if test="${not empty invoiceZoneList }">
                                            <c:forEach items="${invoiceZoneList }" var="zo">
                                                <option value="${zo.regionId }" <c:if test="${invoiceTraderAddressIdZone eq zo.regionId }">selected="selected"</c:if> >${zo.regionName }</option>
                                            </c:forEach>
                                        </c:if>
                                    </select>
                                    <input type="hidden" name="invoiceTraderArea" id="invoiceTraderArea_5" value="${saleorder.invoiceTraderArea}"/>
                                    <input type="hidden" name="invoiceTraderAreaId" id="invoiceTraderAddressId" value="${invoiceTraderAddressIdZone}"/>
                                    <input maxLength="256" type="text" id="invoiceTraderAddress_5" class="input-xx" name="invoiceTraderAddress" value="${saleorder.invoiceTraderAddress}" placeholder="请输入收票地址" />
                                    <div id="5_invoiceTraderAddress" style="clear:both"></div>
                                </div>
                            </li>
                        </c:when>
                        <c:when test="${saleorder.orderType eq 8|| saleorder.orderType eq 7}">
                            <li id="invoiceTraderContactLi">
                                <div class="infor_name infor_name96">
                                    <span>*</span>
                                    <label>收票联系人：</label>
                                </div>
                                <div class="f_left">
                                    <select class="input-xx contact" id="trader_contact_2" name="invoiceTraderContactId">
                                        <option value="0">请输入联系人姓名或手机号</option>
                                        <c:if test="${not empty invoiceTraderContactList}">
                                            <c:forEach items="${invoiceTraderContactList}" var="list" varStatus="status">
                                                <option value="${list.traderContactId}"
                                                        <c:if test="${list.traderContactId eq saleorder.invoiceTraderContactId}">selected="selected"</c:if>>
                                                        ${list.name}/${list.telephone}/${list.mobile}
                                                </option>
                                            </c:forEach>
                                        </c:if>
                                    </select>
                                    <input type="hidden" name="invoiceTraderContactName">
                                    <input type="hidden" name="invoiceTraderContactTelephone">
                                    <input type="hidden" name="invoiceTraderContactMobile">
                                    <span class="mt4 font-blue" id="add_contact_2" onclick="addContact('./addContact.do?traderId=${saleorder.invoiceTraderId}&traderCustomerId=${invoiceTraderCustomerInfo.traderCustomerId}&indexId=2')" style="cursor: pointer;">新增</span>
                                    <div id="invoiceTraderContactIdMsg" style="clear:both"></div>
                                </div>
                            </li>
                            <li id="invoiceTraderAddressLi" style="display: none;">
                                <div class="infor_name infor_name96">
                                    <span>*</span>
                                    <label>收票地址：</label>
                                </div>
                                <div class="f_left " id="chooseInvoicePart">
                                    <select class="input-xx address" id="address_2" name="invoiceTraderAddressId">
                                        <option value="0">请输入收票地址</option>
                                        <c:if test="${not empty invoiceTraderAddressList}">
                                            <c:forEach items="${invoiceTraderAddressList}" var="list" varStatus="status">
                                                <option value="${list.traderAddress.traderAddressId}" <c:if test="${list.traderAddress.traderAddressId eq saleorder.invoiceTraderAddressId}">selected="selected"</c:if>>${list.area}/${list.traderAddress.address}</option>
                                            </c:forEach>
                                        </c:if>
                                    </select>
                                    <input type="hidden" name="invoiceTraderArea">
                                    <input type="hidden" name="invoiceTraderAddress">
                                    <span class="mt4  font-blue"  id="add_address_2" onclick="addAddress('./addAddress.do?traderId=${saleorder.invoiceTraderId}&indexId=2')" style="cursor: pointer;">新增</span>

                                </div>
                                <div class="f_left" id="syncInvoiceAddress">
                                    <input class="input-xx" id="showInvoice" readonly="readonly">
                                </div>
                                <div id="invoiceTraderAddressIdMsg" style="clear:both"></div>
                            </li>
                        </c:when>
                        <c:otherwise>

                            <li id="invoiceTraderContactLi">
                                <div class="infor_name infor_name96">
                                    <span>*</span>
                                    <label>收票联系人：</label>
                                </div>
                                <div class="f_left">
                                    <select class="input-xx contact" id="trader_contact_2" name="invoiceTraderContactId">
                                        <option value="0">请输入联系人姓名或手机号</option>
                                        <c:if test="${not empty invoiceTraderContactList}">
                                            <c:forEach items="${invoiceTraderContactList}" var="list" varStatus="status">
                                                <option value="${list.traderContactId}"
                                                        <c:if test="${list.traderContactId eq saleorder.invoiceTraderContactId}">selected="selected"</c:if>>
                                                        ${list.name}/${list.telephone}/${list.mobile}
                                                </option>
                                            </c:forEach>
                                        </c:if>
                                    </select>
                                    <input type="hidden" name="invoiceTraderContactName">
                                    <input type="hidden" name="invoiceTraderContactTelephone">
                                    <input type="hidden" name="invoiceTraderContactMobile">
                                    <span class="mt4 font-blue" id="add_contact_2" onclick="addContact('./addContact.do?traderId=${saleorder.invoiceTraderId}&traderCustomerId=${invoiceTraderCustomerInfo.traderCustomerId}&indexId=2')" style="cursor: pointer;">新增</span>
                                    <div id="invoiceTraderContactIdMsg" style="clear:both"></div>
                                </div>
                            </li>
                            <li id="invoiceTraderAddressLi" style="display: none;">
                                <div class="infor_name infor_name96">
                                    <span>*</span>
                                    <label>收票地址：</label>
                                </div>
                                <div class="f_left " id="chooseInvoicePart">
                                    <select class="input-xx address" id="address_2" name="invoiceTraderAddressId">
                                        <option value="0">请输入收票地址</option>
                                        <c:if test="${not empty invoiceTraderAddressList}">
                                            <c:forEach items="${invoiceTraderAddressList}" var="list" varStatus="status">
                                                <option value="${list.traderAddress.traderAddressId}" <c:if test="${list.traderAddress.traderAddressId eq saleorder.invoiceTraderAddressId}">selected="selected"</c:if>>${list.area}/${list.traderAddress.address}</option>
                                            </c:forEach>
                                        </c:if>
                                    </select>
                                    <input type="hidden" name="invoiceTraderArea">
                                    <input type="hidden" name="invoiceTraderAddress">
                                    <span class="mt4  font-blue"  id="add_address_2" onclick="addAddress('./addAddress.do?traderId=${saleorder.invoiceTraderId}&indexId=2')" style="cursor: pointer;">新增</span>

                                </div>
                                <div class="f_left" id="syncInvoiceAddress">
                                    <input class="input-xx" id="showInvoice" readonly="readonly">
                                </div>
                                <div id="invoiceTraderAddressIdMsg" style="clear:both"></div>
                            </li>
                        </c:otherwise>
                    </c:choose>
                    <div id="invoiceEmailPart">
                        <%--<c:if  test="${saleorder.orderType eq 8 || saleorder.orderType eq 9 || saleorder.orderType eq 5 || saleorder.orderType eq 7}">--%>
<%--                            <li>--%>
<%--                                <div class="infor_name infor_name96">--%>
<%--                                    <label>收票邮箱：</label>--%>
<%--                                </div>--%>
<%--                                <div class="f_left inputfloat">--%>
<%--                                    <input type="text" name="invoiceEmail" value="${saleorder.invoiceEmail}" placeholder="请输入收票邮箱" />--%>
<%--                                </div>--%>
<%--                            </li>--%>
                        <%--</c:if>--%>
                    </div>
                    <li>
                        <div class="infor_name infor_name96">
                            <span>*</span>
                            <label>发票类型：</label>
                        </div>
                        <!-- 获取当前日期 -->
                        <jsp:useBean id="now" class="java.util.Date" />
                        <fmt:formatDate value="${now}" type="both" dateStyle="long" var="today" pattern="yyyy-MM-dd"/>
                        <div class="f_left inputfloat">
                            <select class="input-middle" name="invoiceType" id="invoiceType">
                                <option value="0">请选择</option>
                                <!-- 4月1号后税率只有13% -->
                                <c:choose>
                                    <c:when test="${today >= '2019-04-01'}">
                                        <c:forEach var="list" items="${invoiceTypes}">
                                            <c:if test="${list.sysOptionDefinitionId eq 971 or list.sysOptionDefinitionId eq 972}">
                                                <option value="${list.sysOptionDefinitionId}" <c:if test="${saleorder.invoiceType == list.sysOptionDefinitionId}">selected="selected"</c:if> >${list.title}</option>
                                            </c:if>
                                        </c:forEach>
                                    </c:when>
                                    <c:otherwise>
                                        <c:forEach var="list" items="${invoiceTypes}">
                                            <c:if test="${list.sysOptionDefinitionId eq 681 or list.sysOptionDefinitionId eq 682 or list.sysOptionDefinitionId eq 971 or list.sysOptionDefinitionId eq 972}">
                                                <option value="${list.sysOptionDefinitionId}" <c:if test="${saleorder.invoiceType == list.sysOptionDefinitionId}">selected="selected"</c:if> >${list.title}</option>
                                            </c:if>
                                        </c:forEach>
                                    </c:otherwise>
                                </c:choose>
                            </select>
                            <%-- <input type="checkbox" name="isSendInvoiceCheckbox" class="mt5" onclick="isSendInvoiceChecked(${orderType});"  <c:if test="${saleorder.isSendInvoice == '0'}">checked</c:if>>--%>
                            <%-- <input type="hidden" id="isSendInvoice" name="isSendInvoice" value="${saleorder.isSendInvoice}">--%>
                            <%--   <span class="mt3">不寄送</span><c:if test="${orderType eq 5}"><span class="mt3" style="color:#666666;" >注：耗材商城订单，选择不寄送时，收票信息中收票联系人，收票手机号，收票地址，收票邮箱非必填</span></c:if>--%>
                            <div id="invoiceTypeMsg" style="clear:both"></div>
                        </div>
                    </li>
                    <li>
                        <div class="infor_name infor_name96">
                            <label>收票邮箱：</label>
                        </div>
                        <div class="f_left inputfloat">
                            <input class="input-middle" type="text" id="invoiceEmail" name="invoiceEmail" value="${saleorder.invoiceEmail}" placeholder="输入后发票会发送至该邮箱" />
                        </div>
                    </li>
                    <li>
                        <select class="input-middle" name="invoiceMethod" id="invoiceMethod" disabled="disabled" style="display: none;">
                            <option value="4" selected></option>
                        </select>
                        <input type="hidden" name="invoiceMethod">
                    </li>
<%--                    <li>--%>
<%--                        <div class="infor_name infor_name96">--%>
<%--                            <span>*</span>--%>
<%--                            <label>开票方式：</label>--%>

<%--                        </div>--%>

<%--                        <div class="f_left inputfloat tips-all">--%>
<%--                            <select class="input-middle" name="invoiceMethod" id="invoiceMethod" disabled="disabled">--%>
<%--                                <option value="0">请选择</option>--%>
<%--                                <c:if test="${saleorder.invoiceType == 681 or saleorder.invoiceType == 971}">--%>
<%--                                    <option value="1" <c:if test="${saleorder.invoiceMethod == 1}">selected</c:if>>手动纸质开票</option>--%>
<%--                                    <option value="2" <c:if test="${saleorder.invoiceMethod == 2}">selected</c:if>>自动纸质开票</option>--%>
<%--                                    <option value="3" <c:if test="${saleorder.invoiceMethod == 3}">selected</c:if>>自动电子发票</option>--%>
<%--                                    <option value="4" <c:if test="${saleorder.invoiceMethod == 4}">selected</c:if>>自动数电发票</option>--%>
<%--                                </c:if>--%>
<%--                                <c:if test="${saleorder.invoiceType == 682 or saleorder.invoiceType == 972}">--%>
<%--                                    <option value="1" <c:if test="${saleorder.invoiceMethod == 1}">selected</c:if>>手动纸质开票</option>--%>
<%--                                    <option value="2" <c:if test="${saleorder.invoiceMethod == 2}">selected</c:if>>自动纸质开票</option>--%>
<%--                                    <option value="4" <c:if test="${saleorder.invoiceMethod == 4}">selected</c:if>>自动数电发票</option>--%>
<%--                                </c:if>--%>
<%--                            </select>--%>
<%--                            <div id="invoiceMethodMsg" style="clear:both"></div>--%>
<%--                        </div>--%>
<%--                        <div class="tips-error" style="display: none;color:red;line-height: 26px;">--%>
<%--                            “手动纸质开票”的订单，不在自动开票推送的范围内，后期需要手动申请开票。--%>
<%--                        </div>--%>
<%--                    </li>--%>
<%--                    <li>--%>
<%--                        <div class="infor_name infor_name96">--%>
<%--                            <label>开票备注：</label>--%>
<%--                        </div>--%>
<%--                        <div class="f_left inputfloat">--%>
<%--                            <input type="text" name="invoiceComments" id="invoiceComments" value="${saleorder.invoiceComments}" placeholder="对内使用，适用于向财务部同事告知开票要求" class="input-xx" />--%>
<%--                        </div>--%>
<%--                    </li>--%>

                    <li>
                        <div class="infor_name infor_name96">
                            <label>暂缓开票：</label>
                        </div>
                        <div class="f_left inputfloat">
                            <input type="checkbox" name="isDelayInvoiceCheckbox" class="mt5" onclick="isDelayInvoiceChecked();" <c:if test="${saleorder.isDelayInvoice == 1}">checked="checked"</c:if> >
                            <input type="hidden" name="isDelayInvoice" value="${saleorder.isDelayInvoice}">
                        </div>
                    </li>
                    <li>
                        <span style="color: red">
                            若本单需开具专票，请在申请审核前，维护好客户专票资质包括注册地址、注册电话、税务登记号、一般纳税人资质、开户银行、银行账号，否则会造成订单无法顺利审核通过
                        </span>
                    </li>
                </ul>
            </div>
        </div>


        <div class="parts content1" style="padding-top: 15px">
            <div class="title-container title-container-blue">
                <div class="table-title nobor">
                    付款计划
                </div>
            </div>
            <div style="border: 1px solid #ddd;padding-top:10px">
                <ul class="payplan" id="payplan" style="padding:10px">
                    <li>
                        <div class="infor_name infor_name96">
                            <label>付款方式：</label>
                        </div>
                        <div class="f_left inputfloat">
                            <select <c:if test="${ 0 eq saleorder.paymentMode }">disabled="disabled"</c:if> <c:if test="${5 eq orderType }"></c:if> id="paymentType" autocomplete="off" name="paymentType" onchange="updatePayment(this,${totleMoney});checkprepaidAmount()" class="input-middle">
                                <c:forEach var="list" items="${paymentTermList}" varStatus="status">
                                    <option value="${list.sysOptionDefinitionId}" <c:if test="${list.sysOptionDefinitionId eq saleorder.paymentType}">selected</c:if>>${list.title}</option>
                                </c:forEach>
                            </select>
                        </div>
                    </li>
                    <li>
                        <div class="infor_name infor_name96 ">
                            <label>预付金额：</label>
                        </div>

                        <div class="f_left">
                            <!-- modify by tomcat.Hui 20190918 VDERP-1294 编辑订单金额错误  改为直接取订单totalamount -->
                            <div><input type="text" autocomplete="off" class="input-middle" id="prepaidAmount" name="prepaidAmount" onchange="checkprepaidAmount()"
                            <c:if test="${5 eq orderType || 1 eq orderType || saleorder.paymentType ne 424}">
                                        readonly
                            </c:if>
                            <c:choose>
                            <c:when test="${saleorder.prepaidAmount == '0.00' && saleorder.paymentType == 419}">
                            <c:choose>
                            <c:when test="${5 eq orderType }">
                                        value="${saleorder.totalAmount}"
                            </c:when>
                            <c:when test="${1 eq orderType }">
                                        value="${saleorder.totalAmount}"
                            </c:when>
                            <c:otherwise>
                                        value="${totleMoney}"
                            </c:otherwise>
                            </c:choose>
                            </c:when>
                            <c:otherwise>
                                        value="${saleorder.prepaidAmount}"
                            </c:otherwise>
                            </c:choose>
                            > </div>
                            <div id="prepaidAmountError"></div>
                        </div>

                    </li>
                    <li id="accountPeriodLi" <c:if test="${(saleorder.paymentType eq 419) or (saleorder.paymentType eq 0)}">style="display:none"</c:if>>
                        <div class="infor_name infor_name96 ">
                            <label>账期支付：</label>
                        </div>
                        <div class="f_left inputfloat">
                            <!-- 账期支付最大限额（剩余账期额度） -->
                            <input type="hidden" id="accountPeriodLeft" value="<fmt:formatNumber type="number" value="${customer.accountPeriodLeft}" pattern="0.00" maxFractionDigits="2" />">
                            <input type="text" class="input-middle" name="accountPeriodAmount" id="accountPeriodAmount"
                                   onchange="accountPeriodAmountChange()" value="<fmt:formatNumber type="number" value="${saleorder.accountPeriodAmount}" pattern="0.00" maxFractionDigits="2" />">
                            <input type="hidden" name="logisticsCollection" id="logisticsCollection" />
                            <c:if test="${saleorder.orderType != 5}">
                                <label class="mt4">（其中包含质保金:</label>
                                <input type="text" class="input-smallest" name="retentionMoney" id="retentionMoney" onchange="retainageAmountChange()" value="${saleorder.retentionMoney}">
                                <label class="mt4">元）</label>
                            </c:if>
                            <input type="checkbox" style="margin-top: 7px" name="logisticsCheckBox" id="logisticsCheckBox" <c:if test="${saleorder.logisticsCollection eq 1}">checked</c:if>>
                            <label class="mt4">物流代收账期款</label>
                            <div id="accountPeriodAmountError"></div>
                        </div>
                    </li>
                    <li id="retentionMoneyLi">
                        <div class="f_left inputfloat">
                            <div class="infor_name infor_name96 ">
                            </div>
                            <label class="mt4">合同余款</label>
                            <label class="mt4" id="spareMoneyLab"><fmt:formatNumber type="number" value="${saleorder.accountPeriodAmount - saleorder.retentionMoney}" pattern="0.00" maxFractionDigits="2" /></label>
                            <label class="mt4">元，需在发货/开票后</label>
                            <input type="text" class="input-smallest" name="periodDay" id="periodDay" value="${saleorder.periodDay eq 0 ? customer.periodDay : saleorder.periodDay}" onchange="periodDayChange()">
                            <label class="mt4">天内支付</label>
                            <c:if test="${saleorder.orderType != 5}">
                                   <span id="retentionMoneySpan">
                                        <label class="mt4">；质保金<font id="retentionMoneyFont">${saleorder.retentionMoney}</font>元，需要在发货/开票后</label>
                                        <input type="text" class="input-smallest" name="retentionMoneyDay"  value="${saleorder.retentionMoneyDay eq 0 ? customer.periodDay : saleorder.retentionMoneyDay}" id="retentionMoneyDay" onchange=" retainageAmountDayChange()">
                                        <label class="mt4">天内支付</label>
                                   </span>
                            </c:if>
                            <div id="retentionMoneyDayError"></div>
                        </div>
                    </li>

                    <li>
                        <div class="infor_name infor_name96">
                            <label>收款备注：</label>
                        </div>
                        <div class="f_left">
                            <input type="text" placeholder="对内使用，适用于向财务部同事告知收款相关特殊要求，默认同步客户信息中财务备注" class="input-xx" name="paymentComments" id="paymentComments" value="${saleorder.paymentComments}" />
                            <div class="font-grey9 mt10" id="error_div">客户当前账期剩余额度<fmt:formatNumber type="number" value="${customer.accountPeriodLeft}" pattern="0.00" maxFractionDigits="2" />元，账期天数${customer.periodDay}天；如需更改账期，您需要在客户详情财务信息中申请账期；	</div
                        </div>
                    </li>
                    <li id="billPeriodSettlementTypeLi" <c:if test="${(saleorder.paymentType eq 419) or (saleorder.paymentType eq 0)}">style="display:none"</c:if>>
                        <div class="infor_name infor_name96">
                            <label>结算标准：</label>
                        </div>
                        <div class="f_left">
                            <select class="input-small f_left mr10" name="billPeriodSettlementType" id="billPeriodSettlementType">
                                <option value="1" <c:if test="${1 eq saleorder.billPeriodSettlementType}">selected="selected"</c:if>>产品发货</option>
                                <option value="2" <c:if test="${2 eq saleorder.billPeriodSettlementType || null eq saleorder.billPeriodSettlementType}">selected="selected"</c:if>>产品开票</option>
                            </select>
                        </div>
                    </li>
                </ul>
            </div>
        </div>

        <div class="parts content1" style="padding-top:15px">
            <div class="title-container title-container-blue">
                <div class="table-title nobor">
                    其他信息
                </div>
            </div>
            <div style="border: 1px solid #ddd;padding-top:10px">
                <ul class="payplan" style="padding:10px">
                    <li>
                        <tags:saleorder_additional/>
                        <!-- <div class="infor_name infor_name96">
                            <label>附加条款2：</label>
                        </div>
                        <div class="f_left">
                            <input type="text" class="input-xx" name="additionalClause" id="additionalClause" value="${saleorder.additionalClause}" placeholder="面向客户条款，客户可见">
                        </div> -->
                    </li>
                    <li>
                        <div class="infor_name infor_name96">
                            <label>内部备注：</label>
                        </div>
                        <div class="customername pos_rel f_left inputfloat">
                            <input type="text" class="input-xx" name="comments" id="comments" value="${saleorder.comments}" placeholder="对内使用，客户不可见,可用作自己的备注">
                        </div>
                    </li>
                    <li>
                        <div class="infor_name infor_name96">
                            <label>订单测试：</label>
                        </div>
                        <div class="f_left inputfloat">
                            <shiro:lacksPermission  name="/order/edit/orderTestBtnNoUrl.do">
                                <input type="text" class="input-xx" name="orderTestContent-marker" id="orderTestContent-marker" value="无权限填写"
                                       readonly="readonly" title="无权限填写" style="background-color: #e7e7e7;"
                                       placeholder="无权限填写" lay-ignore>
                                <input type="hidden"  name="orderTestContent" id="orderTestContent" value="${saleorder.orderTestContent}"  >
                            </shiro:lacksPermission >
                            <shiro:hasPermission  name="/order/edit/orderTestBtnNoUrl.do">
                                <input type="text" class="input-xx" name="orderTestContent" id="orderTestContent" value="${saleorder.orderTestContent}" maxlength="512"
                                       placeholder="请按照格式填写" lay-ignore>
                            </shiro:hasPermission >
                        </div>
                    </li>
                </ul>
            </div>
        </div>

        <input type="hidden" id="retentionMoneyFlag" value="${saleorder.retentionMoney}">
        <input type="hidden" id="prepaidAmountFlag" value="${saleorder.prepaidAmount}">
        <input type="hidden" id="accountPeriodAmountFlag" value="${saleorder.accountPeriodAmount}">
        <div class="add-tijiao">
            <input type="hidden" name="formToken" value="${formToken}"/>
            <input type="hidden" name="saleorderId" value="${saleorder.saleorderId}">
            <input type="hidden" name="beforeParams" value='${beforeParams}'>
            <input type="hidden" name="belongPlatform" id="belongPlatform" value="${belongPlatform}">
            <input type="hidden" name="oldAccountPeriodAmount" id="oldAccountPeriodAmount" value='${saleorder.accountPeriodAmount}'>
            <button type="button" class="bt-bg-style bg-deep-green" onclick="editSubmit(${orderType});">确定</button>
        </div>
        <!-- 终端操作 -->
        <tags:terminal_edit_table/>
    </form>
</div>

<script type="text/json" class="J-contact-json">
             ${traderContactList}
  	</script>
<script>
    function receiveTerminal(terminal) {
        // 在这里处理传递过来的参数
        $('input[name="terminalName"]').val(terminal.terminalName);
        $('input[name="dwhTerminalId"]').val(terminal.dwhTerminalId);
        $('input[name="unifiedSocialCreditIdentifier"]').val(terminal.unifiedSocialCreditIdentifier);
        $('input[name="organizationCode"]').val(terminal.organizationCode);

        $("#terminalTraderNameDiv").html(terminal.terminalName);
        $("#terminalNameDetail").css("display", "");
        $("#terminalNameCheck").css("display", "none");
        $("#regionMust").css("display", "");

        // 销售区域省市区 回显
        initRegProCityArea(terminal.address);



        $('input[name="provinceId"]').val($('select[name="provinceId"]').val());
        $('input[name="cityId"]').val($('select[name="cityId"]').val());
        $('input[name="areaId"]').val($('select[name="areaId"]').val());


        var provinceName = $('#orderTerminal-province').find('option:selected').text();
        var cityName = $('#orderTerminal-city').find('option:selected').text();
        var areaName = $('#orderTerminal-area').find('option:selected').text();
        $('input[name="provinceName"]').val(provinceName == "请选择" ? "" : provinceName);
        $('input[name="cityName"]').val(cityName == "请选择" ? "" : cityName);
        $('input[name="areaName"]').val(areaName == "请选择" ? "" : areaName);
    }

    // 根据省市区的地址字符串解析出对应的regionId
    function initRegProCityArea(regAddress) {
        var regLocation = "";//获取页面是否有经过天眼查带过来的注册地址
        if (regAddress.length > 0) {
            regLocation = regAddress;
            //第一步，先判断地址中，是否有省份
            var provinceNameDefault = "";
            var cityNameDefault = "";
            var areaNameDefault = "";
            if(regLocation.startsWith("北京")){  //先判断是否是四个直辖市
                provinceNameDefault = "北京市";
                cityNameDefault = "北京市";
                regLocation = regLocation.replaceAll("北京市","");
            } else if(regLocation.startsWith("天津")){
                provinceNameDefault = "天津市";
                cityNameDefault = "天津市";
                regLocation = regLocation.replaceAll("天津市","");
            }else if(regLocation.startsWith("上海")){
                provinceNameDefault = "上海市";
                cityNameDefault = "上海市";
                regLocation = regLocation.replaceAll("上海市","");
            }else if(regLocation.startsWith("重庆")){
                provinceNameDefault = "重庆市";
                cityNameDefault = "重庆市";
                regLocation = regLocation.replaceAll("重庆市","");
            }else{
                $("select[name=\"provinceId\"] option").each(function() {
                    var optionText = $(this).text();
                    var optionSuf = optionText.replaceAll("省","").replaceAll("自治区","");
                    if(regLocation.startsWith(optionSuf)){
                        provinceNameDefault = optionText;
                        if(regLocation.startsWith(optionText)){
                            regLocation = regLocation.replaceAll(optionText,"");
                        }else{
                            regLocation = regLocation.replaceAll(optionSuf,"");
                        }
                        return false;//跳出该each循环
                    }
                });
            }
            if(provinceNameDefault == ''){//未匹配到省份，再匹配一次城市，有可能地址是	岳阳县新开镇胜天村长塘组- 实际是岳阳市。
                $.ajax({
                    type : "POST",
                    url : page_url+"/system/region/getRegionCityAll.do",
                    dataType : 'json',
                    async:true,
                    success : function(data) {
                        var provinceCode = 0;
                        $.each(data.listData,function(i,n){
                            var regionName = data.listData[i]['regionName']; //岳阳市   regLocation 岳阳县新开镇胜天村长塘组 regionName  岳阳市
                            var cityNameSuf = regionName.replaceAll("县","").replaceAll("市","");
                            if(regLocation.startsWith(regionName) || regLocation.startsWith(cityNameSuf)){
                                cityNameDefault = regionName;
                                regLocation = regLocation.startsWith(regionName)?regLocation.replaceAll(regionName,""):regLocation.replaceAll(cityNameSuf,"");
                                provinceCode =data.listData[i]['parentId'];
                                return false;
                            }
                        });
                        $('select[name="provinceId"]').val(provinceCode);
                        $("select[name='provinceId']").trigger('change');
                        $('input[name="provinceName"]').val($('#orderTerminal-province').find('option:selected').text() == "请选择" ? "" : $('#orderTerminal-province').find('option:selected').text());

                        setTimeout(function () {
                            $("select[name='cityId'] option:contains('"+cityNameDefault+"')").prop("selected", true);
                            $("select[name='cityId']").trigger('change');
                            $('input[name="cityId"]').val($('select[name="cityId"]').val());
                            $('input[name="cityName"]').val($('#orderTerminal-city').find('option:selected').text() == "请选择" ? "" : $('#orderTerminal-city').find('option:selected').text());


                            setTimeout(function(){  //计算区的选择逻辑
                                $("select[name=\"areaId\"] option").each(function() {
                                    var optionText = $(this).text();
                                    var optionSuf = optionText.replaceAll("区","").replaceAll("自治州","").replaceAll("市","");
                                    regLocation = regLocation.startsWith("市")?regLocation.replace("市",""):regLocation;
                                    regLocation = regLocation.startsWith("自治州")?regLocation.replace("自治州",""):regLocation;
                                    regLocation = regLocation.startsWith("县")?regLocation.replace("县",""):regLocation;

                                    if(regLocation.startsWith(optionSuf)){//optionSuf  相城
                                        areaNameDefault = optionText;
                                        //江苏苏州相城经济开发区（澄波路） 相城经济开发区（澄波路）
                                        regLocation = regLocation.replace(optionSuf,'');
                                        regLocation = regLocation.replace('区','');
                                        regLocation = regLocation.replace('自治区','');
                                        regLocation = regLocation.replace('县','');

                                        return false;//跳出该each循环
                                    }
                                });
                                if(areaNameDefault!=''){
                                    $("select[name='areaId'] option:contains('"+areaNameDefault+"')").prop("selected", true);
                                    $('input[name="areaId"]').val($('select[name="areaId"]').val());
                                    $('input[name="areaName"]').val($('#orderTerminal-area').find('option:selected').text() == "请选择" ? "" : $('#orderTerminal-area').find('option:selected').text());

                                }
                            },500);
                        },500);

                    },
                    error:function(data){
                        if(data.status ==1001){
                            layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
                        }
                    }
                });
            }else{//如果省份已经匹配到了
                $("select[name='provinceId'] option:contains('"+provinceNameDefault+"')").prop("selected", true);
                $("select[name='provinceId']").trigger('change');
                $('input[name="provinceId"]').val($('select[name="provinceId"]').val());
                $('input[name="provinceName"]').val($('#orderTerminal-province').find('option:selected').text() == "请选择" ? "" : $('#orderTerminal-province').find('option:selected').text());

                setTimeout(function () {
                    if(cityNameDefault ==''){
                        $("select[name=\"cityId\"] option").each(function() {
                            var optionText = $(this).text();
                            var optionSuf = optionText.replaceAll("市","").replaceAll("县","").replaceAll("自治州","");
                            if(regLocation.startsWith(optionSuf)){
                                cityNameDefault = optionText;
                                regLocation = regLocation.startsWith(optionText)?regLocation.replaceAll(optionText,""):regLocation.replaceAll(optionSuf,"");
                                regLocation = regLocation.startsWith("市")?regLocation.replace("市",""):regLocation;
                                regLocation = regLocation.startsWith("自治州")?regLocation.replace("自治州",""):regLocation;
                                regLocation = regLocation.startsWith("县")?regLocation.replace("县",""):regLocation;
                                return false;//跳出该each循环
                            }
                        });
                        if(cityNameDefault  == ''){ //如果仍然没有匹配到城市
                            return ;
                        }
                    }
                    $("select[name='cityId'] option:contains('"+cityNameDefault+"')").prop("selected", true);
                    $("select[name='cityId']").trigger('change');
                    $('input[name="cityId"]').val($('select[name="cityId"]').val());
                    $('input[name="cityName"]').val($('#orderTerminal-city').find('option:selected').text() == "请选择" ? "" : $('#orderTerminal-city').find('option:selected').text());


                    setTimeout(function(){  //计算区的选择逻辑
                        $("select[name=\"areaId\"] option").each(function() {
                            var optionText = $(this).text();
                            var optionSuf = optionText.replaceAll("区","").replaceAll("镇","");
                            regLocation = regLocation.startsWith("市")?regLocation.replace("市",""):regLocation;
                            regLocation = regLocation.startsWith("县")?regLocation.replace("县",""):regLocation;
                            if(regLocation.startsWith(optionSuf)){
                                areaNameDefault = optionText;
                                //江苏苏州相城经济开发区（澄波路） 相城经济开发区（澄波路）
                                regLocation = regLocation.replace(optionSuf,'');
                                regLocation = regLocation.replace('区','');
                                regLocation = regLocation.replace('自治区','');
                                regLocation = regLocation.replace('县','');
                                return false;//跳出该each循环
                            }
                        });
                        if(areaNameDefault!=''){
                            $("select[name='areaId'] option:contains('"+areaNameDefault+"')").prop("selected", true);
                            $('input[name="areaId"]').val($('select[name="areaId"]').val());
                            $('input[name="areaName"]').val($('#orderTerminal-area').find('option:selected').text() == "请选择" ? "" : $('#orderTerminal-area').find('option:selected').text());

                        }
                    },500);
                },500);
            }
        }
    }

</script>
<script src="${pageContext.request.contextPath}/static/new/js/common/suggestSelect.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/js/orderstream/index.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/js/orderstream/saleorder/edit_order.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../../common/footer.jsp"%>
