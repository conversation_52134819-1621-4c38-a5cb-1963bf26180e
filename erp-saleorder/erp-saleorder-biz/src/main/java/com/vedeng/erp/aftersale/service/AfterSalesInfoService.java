package com.vedeng.erp.aftersale.service;

import com.vedeng.aftersales.model.vo.AfterSalesDetailVo;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.trader.model.TraderContact;
import com.vedeng.trader.model.vo.TraderFinanceVo;

import java.util.List;

/**
 * <AUTHOR> simgo.wang
 * @date : 2021/10/14 10:31
 * @desc :
 */
public interface AfterSalesInfoService {

    List<TraderContact> getTraderContact(TraderContact traderContact);

    TraderFinanceVo getTraderCustomerFinance(TraderFinanceVo traderFinance);

    int updateByPrimaryKeyInfo(AfterSalesVo afterSalesVo);

    int updateByPrimaryKeyDetailInfo(AfterSalesDetailVo afterSalesDetailVo);

    TraderContact selectTraderContactInfoByPrimaryKey(Integer traderContactId);
}
