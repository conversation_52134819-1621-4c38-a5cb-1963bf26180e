<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.finance.mapper.SettlementNoticeRecordMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.finance.domain.entity.SettlementNoticeRecordEntity">
    <!--@mbg.generated-->
    <!--@Table T_SETTLEMENT_NOTICE_RECORD-->
    <id column="SETTLEMENT_NOTICE_RECORD_ID" jdbcType="BIGINT" property="settlementNoticeRecordId" />
    <result column="BANK_BILL_ID" jdbcType="INTEGER" property="bankBillId" />
    <result column="NOTICE_TYPE" jdbcType="INTEGER" property="noticeType" />
    <result column="NOTICE_COUNT" jdbcType="INTEGER" property="noticeCount" />
    <result column="LAST_NOTICE_TIME" jdbcType="TIMESTAMP" property="lastNoticeTime" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    SETTLEMENT_NOTICE_RECORD_ID, BANK_BILL_ID, NOTICE_TYPE, NOTICE_COUNT, LAST_NOTICE_TIME, 
    IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, CREATOR_NAME, UPDATER, UPDATER_NAME, UPDATE_REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_SETTLEMENT_NOTICE_RECORD
    where SETTLEMENT_NOTICE_RECORD_ID = #{settlementNoticeRecordId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_SETTLEMENT_NOTICE_RECORD
    where SETTLEMENT_NOTICE_RECORD_ID = #{settlementNoticeRecordId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="SETTLEMENT_NOTICE_RECORD_ID" keyProperty="settlementNoticeRecordId" parameterType="com.vedeng.erp.finance.domain.entity.SettlementNoticeRecordEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SETTLEMENT_NOTICE_RECORD (BANK_BILL_ID, NOTICE_TYPE, NOTICE_COUNT, 
      LAST_NOTICE_TIME, IS_DELETE, ADD_TIME, 
      MOD_TIME, CREATOR, CREATOR_NAME, 
      UPDATER, UPDATER_NAME, UPDATE_REMARK
      )
    values (#{bankBillId,jdbcType=INTEGER}, #{noticeType,jdbcType=INTEGER}, #{noticeCount,jdbcType=INTEGER}, 
      #{lastNoticeTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, 
      #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, 
      #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR}, #{updateRemark,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="SETTLEMENT_NOTICE_RECORD_ID" keyProperty="settlementNoticeRecordId" parameterType="com.vedeng.erp.finance.domain.entity.SettlementNoticeRecordEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SETTLEMENT_NOTICE_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bankBillId != null">
        BANK_BILL_ID,
      </if>
      <if test="noticeType != null">
        NOTICE_TYPE,
      </if>
      <if test="noticeCount != null">
        NOTICE_COUNT,
      </if>
      <if test="lastNoticeTime != null">
        LAST_NOTICE_TIME,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bankBillId != null">
        #{bankBillId,jdbcType=INTEGER},
      </if>
      <if test="noticeType != null">
        #{noticeType,jdbcType=INTEGER},
      </if>
      <if test="noticeCount != null">
        #{noticeCount,jdbcType=INTEGER},
      </if>
      <if test="lastNoticeTime != null">
        #{lastNoticeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.finance.domain.entity.SettlementNoticeRecordEntity">
    <!--@mbg.generated-->
    update T_SETTLEMENT_NOTICE_RECORD
    <set>
      <if test="bankBillId != null">
        BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER},
      </if>
      <if test="noticeType != null">
        NOTICE_TYPE = #{noticeType,jdbcType=INTEGER},
      </if>
      <if test="noticeCount != null">
        NOTICE_COUNT = #{noticeCount,jdbcType=INTEGER},
      </if>
      <if test="lastNoticeTime != null">
        LAST_NOTICE_TIME = #{lastNoticeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where SETTLEMENT_NOTICE_RECORD_ID = #{settlementNoticeRecordId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.finance.domain.entity.SettlementNoticeRecordEntity">
    <!--@mbg.generated-->
    update T_SETTLEMENT_NOTICE_RECORD
    set BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER},
      NOTICE_TYPE = #{noticeType,jdbcType=INTEGER},
      NOTICE_COUNT = #{noticeCount,jdbcType=INTEGER},
      LAST_NOTICE_TIME = #{lastNoticeTime,jdbcType=TIMESTAMP},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where SETTLEMENT_NOTICE_RECORD_ID = #{settlementNoticeRecordId,jdbcType=BIGINT}
  </update>
  
  <select id="querySettlementNoticeRecord" resultMap="BaseResultMap">
    select * from T_SETTLEMENT_NOTICE_RECORD
    where 
    BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER} 
    and IS_DELETE = 0
    <if test="noticeType != null">
      and NOTICE_TYPE = #{noticeType,jdbcType=INTEGER}
    </if>
    order by LAST_NOTICE_TIME desc
  </select>
</mapper>
