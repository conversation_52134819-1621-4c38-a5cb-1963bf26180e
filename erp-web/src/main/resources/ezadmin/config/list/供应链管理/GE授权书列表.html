<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>GE授权书列表</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="P_lqw4zvL-Y" datasource="erp-reportdatasource" fixednumber="0" append_column_url="" append_row_url=""
      empty_show="" firstcol="numbers">
<div id="appendHead"></div>
<div class="layui-fluid">
    <div class="layui-tab">
        <ul class="layui-tab-title" id="tab">
        </ul>
    </div>
    <form class="layui-form" id="search">
        <div class=" layui-inline "><label class="layui-form-label">产品类型</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="20" name="TYPE" placeholder="" style="" alias="b"
                        jdbctype=""
                        data="[{&quot;V&quot;: &quot;CT&quot;,&quot;K&quot;: &quot;1&quot;},{&quot;V&quot;: &quot;超声&quot;,&quot;K&quot;: &quot;2&quot;}]"
                        datatype="JSON" oper=""></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">授权书状态</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="20" name="STATUS" placeholder="" style="" alias="b"
                        jdbctype=""
                        data="[{&quot;V&quot;: &quot;待审核&quot;,&quot;K&quot;: &quot;0&quot;},{&quot;V&quot;: &quot;审核中&quot;,&quot;K&quot;: &quot;1&quot;},{&quot;V&quot;: &quot;审核通过&quot;,&quot;K&quot;: &quot;2&quot;},{&quot;V&quot;: &quot;审核不通过&quot;,&quot;K&quot;: &quot;3&quot;},{&quot;V&quot;: &quot;已关闭&quot;,&quot;K&quot;: &quot;4&quot;}]"
                        datatype="JSON" oper=""></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">申请时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" name="ADD_TIME" placeholder="" style=""
                        alias="b" jdbctype="DATE" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">申请人</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="19" name="CREATOR_NAME" placeholder="" style=""
                        alias="b" jdbctype="VARCHAR" data="" datatype="" oper="LIKE"></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">GE商机编号</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="19" name="GE_BUSSINESS_CHANCE_NO" placeholder=""
                        style="" alias="a" jdbctype="VARCHAR" data="" datatype="" oper="LIKE"></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">授权书编号</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="19" name="AUTHORIZATION_NO" placeholder="" style=""
                        alias="b" jdbctype="VARCHAR" data="" datatype="" oper="LIKE"></object>
            </div>
        </div>
    </form>
    <hr class="layui-border-blue">
    <div class="btn-group   bd-highlight" id="tableButton">
        <button item_name="导出所有" name="导出所有" url="/ezadmin/list/export-P_lqw4zvL-Y" opentype="_BLANK_PARAM"
                windowname="导出所有" style type="table">导出所有
        </button>
    </div>
    <table id="table" class="layui-table" style=" width:100%">
        <thead>
        <tr id="column">
            <th type="rowbutton" id="rowbutton"></th>
            <th item_name="AUTHORIZATION_NO" name="AUTHORIZATION_NO" body="td-link" order="0"
                url="/ge/authorization/itemView.do?authorizationId=${AUTHORIZATION_ID}" opentype="PARENT" windowname=""
                datatype="" data="" style="" head="18">GE授权书编号
            </th>
            <th item_name="TYPE" name="TYPE" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype=""
                data="" style="" head="18">产品类型
            </th>
            <th item_name="CREATOR_NAME" name="CREATOR_NAME" body="td-text" order="0" url="" opentype="MODEL" windowname=""
                datatype="" data="" style="" head="18">申请人
            </th>
            <th item_name="ADD_TIME" name="ADD_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname=""
                datatype="" data="" style="" head="18">申请时间
            </th>
            <th item_name="STATUS" name="STATUS" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype=""
                data="" style="" head="18">GE授权书状态
            </th>
            <th item_name="GE_BUSSINESS_CHANCE_NO" name="GE_BUSSINESS_CHANCE_NO" body="td-link" order="0"
                url="/businesschance/ge/viewgebusinesschance.do?geBussinessChanceId=${GE_BUSSINESS_CHANCE_ID}"
                opentype="PARENT" windowname="" datatype="TEXT" data="" style="" head="18">GE商机编号
            </th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td colspan="100"> express:
                <pre id="express" class="layui-code" orderby="order by b.ADD_TIME desc" groupby="">SELECT
b.AUTHORIZATION_ID,
b.GE_BUSSINESS_CHANCE_ID,
	b.AUTHORIZATION_NO,
CASE
		
		WHEN b.TYPE = 1 THEN
		'CT' 
		WHEN b.TYPE = 2 THEN
		'超声' 
	END TYPE,
	b.CREATOR_NAME,
	DATE_FORMAT(b.ADD_TIME,'%Y-%m-%d %H:%i:%s') ADD_TIME,
CASE
		
		WHEN b.STATUS = 0 THEN
		'待审核' 
		WHEN b.STATUS = 1 THEN
		'审核中' 
		WHEN b.STATUS = 2 THEN
		'审核通过' 
		WHEN b.STATUS = 3 THEN
		'审核不通过' 
		WHEN b.STATUS = 4 THEN
		'已关闭' 
	END STATUS,
	a.GE_BUSSINESS_CHANCE_NO 
FROM
T_GE_BUSINESS_CHANCE a
	LEFT JOIN T_GE_AUTHORIZATION b on a.GE_BUSSINESS_CHANCE_ID = b.GE_BUSSINESS_CHANCE_ID 
WHERE
	1 =1
	and a.IS_DELETE=0
	and b.IS_DELETE=0</pre>
                count:
                <pre id="count" class="layui-code"></pre>
            </td>
        </tr>
        </tbody>
    </table>
</div>
<div id="appendFoot"></div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    })
</script>
</body>
</html>