package com.vedeng.flash.dto.vo;

import lombok.*;

import java.util.List;

/**
 * @program: erp.vedeng.com
 * @description: 定品池查询参数
 * @author: Pusan
 * @create: 2021-05-18 15:23
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class OrderingPoolQueryVO {

    /** 订货号 */
    private String skuNo;

    /** 商品名称 */
    private String goodsName;

    /** 品牌 */
    private String brandName;

    /** 商品类型 */
    private Integer goodsType;

    /** 商品分类Id */
    private Integer categoryId;

    private Integer categoryOpt0;
    private Integer categoryOpt1;
    private Integer categoryOpt2;

    /** 归属人 */
    private Integer goodsUserId;

    /** 定品状态 0：启用，1：删除 */
    private Integer skuStatus;
}
