<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.aftersales.dao.AfterSalesInstallstionMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.aftersales.model.AfterSalesInstallstion">
        <id column="AFTER_SALES_INSTALLSTION_ID" property="afterSalesInstallstionId" jdbcType="INTEGER"/>
        <result column="AFTER_SALES_ID" property="afterSalesId" jdbcType="INTEGER"/>
        <result column="ENGINEER_ID" property="engineerId" jdbcType="INTEGER"/>
        <result column="SERVICE_TIME" property="serviceTime" jdbcType="BIGINT"/>
        <result column="ENGINEER_AMOUNT" property="engineerAmount" jdbcType="DECIMAL"/>
        <result column="NOTICE_TIMES" property="noticeTimes" jdbcType="BIT"/>
        <result column="LAST_NOTICE_TIME" property="lastNoticeTime" jdbcType="BIGINT"/>
        <result column="SERVICE_SCORE" property="serviceScore" jdbcType="BIT"/>
        <result column="SKILL_SCORE" property="skillScore" jdbcType="BIT"/>
        <result column="SCORE_USER_ID" property="scoreUserId" jdbcType="INTEGER"/>
        <result column="SCORE_TIME" property="scoreTime" jdbcType="BIGINT"/>
        <result column="SCORE_COMMENTS" property="scoreComments" jdbcType="VARCHAR"/>
        <result column="PAYMENT_STATUS" property="paymentStatus" jdbcType="BIT"/>
        <result column="PAYMENT_TIME" property="paymentTime" jdbcType="BIGINT"/>
        <result column="ADD_TIME" property="addTime" jdbcType="BIGINT"/>
        <result column="CREATOR" property="creator" jdbcType="INTEGER"/>
        <result column="MOD_TIME" property="modTime" jdbcType="BIGINT"/>
        <result column="UPDATER" property="updater" jdbcType="INTEGER"/>
    </resultMap>
    <resultMap type="com.vedeng.aftersales.model.vo.AfterSalesInstallstionVo" id="VoBaseResultMap"
               extends="BaseResultMap">
        <result column="AFTER_SALES_ID" property="afterSalesId" jdbcType="INTEGER"/>
        <result column="AFTER_SALES_NO" property="afterSalesNo" jdbcType="VARCHAR"/>
        <result column="VALID_STATUS" property="validStatus" jdbcType="BIT"/>
        <result column="STATUS" property="status" jdbcType="BIT"/>
        <result column="ATFER_SALES_STATUS" property="atferSalesStatus" jdbcType="BIT"/>
        <result column="SCORE_TIMES" property="scoreTimes" jdbcType="INTEGER"/>
        <result column="SERVICE_SCORE_TOTAL" property="serviceScoreTotal" jdbcType="INTEGER"/>
        <result column="SKILL_SCORE_TOTAL" property="skillScoreTotal" jdbcType="INTEGER"/>
        <result column="NAME" property="name" jdbcType="VARCHAR"/>
        <result column="MOBILE" property="mobile" jdbcType="VARCHAR"/>
        <result column="BANK" property="bank" jdbcType="VARCHAR"/>
        <result column="BANK_CODE" property="bankCode" jdbcType="VARCHAR"/>
        <result column="BANK_ACCOUNT" property="bankAccount" jdbcType="VARCHAR"/>
        <result column="COMPANY" property="company" jdbcType="VARCHAR"/>
        <result column="SERVICE_SCORE_AVERAGE" property="serviceScoreAverage" jdbcType="DECIMAL"/>
        <result column="SKILL_SCORE_AVERAGE" property="skillScoreAverage" jdbcType="DECIMAL"/>
        <result column="CARD" property="card" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
    AFTER_SALES_INSTALLSTION_ID, AFTER_SALES_ID, ENGINEER_ID, SERVICE_TIME, 
    ENGINEER_AMOUNT, NOTICE_TIMES, LAST_NOTICE_TIME, 
    SERVICE_SCORE, SKILL_SCORE, SCORE_USER_ID, SCORE_TIME, SCORE_COMMENTS, PAYMENT_STATUS, 
    PAYMENT_TIME, ADD_TIME, CREATOR, MOD_TIME, UPDATER
  </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List"/>
        from T_AFTER_SALES_INSTALLSTION
        where AFTER_SALES_INSTALLSTION_ID = #{afterSalesInstallstionId,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_AFTER_SALES_INSTALLSTION
    where AFTER_SALES_INSTALLSTION_ID = #{afterSalesInstallstionId,jdbcType=INTEGER}
  </delete>
    <insert id="insert" parameterType="com.vedeng.aftersales.model.AfterSalesInstallstion">
        insert into T_AFTER_SALES_INSTALLSTION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="afterSalesInstallstionId != null">
                AFTER_SALES_INSTALLSTION_ID,
            </if>
            <if test="afterSalesId != null">
                AFTER_SALES_ID,
            </if>
            <if test="engineerId != null">
                ENGINEER_ID,
            </if>
            <if test="serviceTime != null">
                SERVICE_TIME,
            </if>
            <if test="engineerAmount != null">
                ENGINEER_AMOUNT,
            </if>
            <if test="noticeTimes != null">
                NOTICE_TIMES,
            </if>
            <if test="lastNoticeTime != null">
                LAST_NOTICE_TIME,
            </if>
            <if test="serviceScore != null">
                SERVICE_SCORE,
            </if>
            <if test="skillScore != null">
                SKILL_SCORE,
            </if>
            <if test="scoreUserId != null">
                SCORE_USER_ID,
            </if>
            <if test="scoreTime != null">
                SCORE_TIME,
            </if>
            <if test="scoreComments != null">
                SCORE_COMMENTS,
            </if>
            <if test="paymentStatus != null">
                PAYMENT_STATUS,
            </if>
            <if test="paymentTime != null">
                PAYMENT_TIME,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="afterSalesInstallstionId != null">
                #{afterSalesInstallstionId,jdbcType=INTEGER},
            </if>
            <if test="afterSalesId != null">
                #{afterSalesId,jdbcType=INTEGER},
            </if>
            <if test="engineerId != null">
                #{engineerId,jdbcType=INTEGER},
            </if>
            <if test="serviceTime != null">
                #{serviceTime,jdbcType=BIGINT},
            </if>
            <if test="engineerAmount != null">
                #{engineerAmount,jdbcType=DECIMAL},
            </if>
            <if test="noticeTimes != null">
                #{noticeTimes,jdbcType=BIT},
            </if>
            <if test="lastNoticeTime != null">
                #{lastNoticeTime,jdbcType=BIGINT},
            </if>
            <if test="serviceScore != null">
                #{serviceScore,jdbcType=BIT},
            </if>
            <if test="skillScore != null">
                #{skillScore,jdbcType=BIT},
            </if>
            <if test="scoreUserId != null">
                #{scoreUserId,jdbcType=INTEGER},
            </if>
            <if test="scoreTime != null">
                #{scoreTime,jdbcType=BIGINT},
            </if>
            <if test="scoreComments != null">
                #{scoreComments,jdbcType=VARCHAR},
            </if>
            <if test="paymentStatus != null">
                #{paymentStatus,jdbcType=BIT},
            </if>
            <if test="paymentTime != null">
                #{paymentTime,jdbcType=BIGINT},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
        </trim>
        <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="afterSalesInstallstionId">
            SELECT LAST_INSERT_ID() AS afterSalesInstallstionId
        </selectKey>
    </insert>
    <update id="update" parameterType="com.vedeng.aftersales.model.AfterSalesInstallstion">
        update T_AFTER_SALES_INSTALLSTION
        <set>
            <if test="afterSalesId != null">
                AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
            </if>
            <if test="engineerId != null">
                ENGINEER_ID = #{engineerId,jdbcType=INTEGER},
            </if>
            <if test="serviceTime != null">
                SERVICE_TIME = #{serviceTime,jdbcType=BIGINT},
            </if>
            <if test="engineerAmount != null">
                ENGINEER_AMOUNT = #{engineerAmount,jdbcType=DECIMAL},
            </if>
            <if test="noticeTimes != null">
                NOTICE_TIMES = #{noticeTimes,jdbcType=BIT},
            </if>
            <if test="lastNoticeTime != null">
                LAST_NOTICE_TIME = #{lastNoticeTime,jdbcType=BIGINT},
            </if>
            <if test="serviceScore != null">
                SERVICE_SCORE = #{serviceScore,jdbcType=BIT},
            </if>
            <if test="skillScore != null">
                SKILL_SCORE = #{skillScore,jdbcType=BIT},
            </if>
            <if test="scoreUserId != null">
                SCORE_USER_ID = #{scoreUserId,jdbcType=INTEGER},
            </if>
            <if test="scoreTime != null">
                SCORE_TIME = #{scoreTime,jdbcType=BIGINT},
            </if>
            <if test="scoreComments != null">
                SCORE_COMMENTS = #{scoreComments,jdbcType=VARCHAR},
            </if>
            <if test="paymentStatus != null">
                PAYMENT_STATUS = #{paymentStatus,jdbcType=BIT},
            </if>
            <if test="paymentTime != null">
                PAYMENT_TIME = #{paymentTime,jdbcType=BIGINT},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
        </set>
        where AFTER_SALES_INSTALLSTION_ID = #{afterSalesInstallstionId,jdbcType=INTEGER}
    </update>

    <select id="getEngineerInstallstionCount" parameterType="java.lang.Integer" resultType="java.lang.Integer">
  	select count(*) from T_AFTER_SALES_INSTALLSTION where ENGINEER_ID = #{engineerId,jdbcType=INTEGER}
  </select>

    <select id="getInstallstionByEngineerListPage" parameterType="Map" resultMap="VoBaseResultMap">
  	select 
  		b.AFTER_SALES_ID,b.AFTER_SALES_NO,b.VALID_STATUS,b.STATUS,b.ATFER_SALES_STATUS,
  		a.SERVICE_TIME,a.ENGINEER_AMOUNT,a.SERVICE_SCORE,a.SKILL_SCORE,a.AFTER_SALES_INSTALLSTION_ID,a.SCORE_COMMENTS
  	from
  		T_AFTER_SALES_INSTALLSTION a
  	left join
  		T_AFTER_SALES b
  	on
  		a.AFTER_SALES_ID = b.AFTER_SALES_ID
  	where
  		a.ENGINEER_ID=#{engineer.engineerId,jdbcType=INTEGER}	
  	order by a.SERVICE_TIME desc
  </select>

    <select id="getScoreInfoByEngineer" parameterType="java.lang.Integer" resultMap="VoBaseResultMap">
  	select 
  		count(*) as SCORE_TIMES,sum(SERVICE_SCORE) as SERVICE_SCORE_TOTAL,sum(SKILL_SCORE) as SKILL_SCORE_TOTAL
  	from
  		T_AFTER_SALES_INSTALLSTION
  	where
  		SERVICE_SCORE > 0 
		and 
  		SKILL_SCORE > 0
  		and
  		ENGINEER_ID=#{engineerId,jdbcType=INTEGER}		
  </select>
    <select id="getAfterSalesInstallstionVoByParam" parameterType="com.vedeng.aftersales.model.AfterSalesInstallstion"
            resultMap="VoBaseResultMap">
        select
        a.AFTER_SALES_INSTALLSTION_ID, a.AFTER_SALES_ID, a.ENGINEER_ID, a.SERVICE_TIME,
        a.ENGINEER_AMOUNT, a.NOTICE_TIMES, a.LAST_NOTICE_TIME, a.SERVICE_SCORE,a.SKILL_SCORE,
        b.SERVICE_SCORE AS SERVICE_SCORE_AVERAGE, b.SKILL_SCORE AS SKILL_SCORE_AVERAGE, a.SCORE_USER_ID, a.SCORE_TIME,
        a.SCORE_COMMENTS, a.PAYMENT_STATUS,
        a.PAYMENT_TIME, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER, b.NAME, b.MOBILE, b.BANK, b.BANK_CODE,
        b.BANK_ACCOUNT, b.COMPANY, b.CARD
        from
        T_AFTER_SALES_INSTALLSTION a
        left join
        T_ENGINEER b on a.ENGINEER_ID = b.ENGINEER_ID
        where 1=1
        <if test="afterSalesId != null">
            AND a.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
        </if>
        order by a.SERVICE_TIME desc
    </select>

    <select id="getAfterSalesPayStatus" resultMap="VoBaseResultMap">
  	SELECT
		t1.AFTER_SALES_INSTALLSTION_ID, t1.AFTER_SALES_ID, t1.ENGINEER_ID, t4.VALID_STATUS
	FROM
		T_AFTER_SALES_INSTALLSTION t1
	LEFT JOIN T_AFTER_SALES t2 ON t1.AFTER_SALES_ID = t2.AFTER_SALES_ID
	LEFT JOIN T_PAY_APPLY_DETAIL t3 ON t1.AFTER_SALES_INSTALLSTION_ID = t3.DETAILGOODS_ID
	JOIN T_PAY_APPLY t4 ON t3.PAY_APPLY_ID = t4.PAY_APPLY_ID
	WHERE
		t1.ENGINEER_ID = #{engineerId}
	AND t2.SUBJECT_TYPE IN (535, 537)
	AND t2.TYPE IN (541, 550, 584, 585,4090,4091)
	AND t2.AFTER_SALES_ID = #{afterSalesId}
	AND t4.PAY_TYPE = 518
  </select>

    <select id="getEditAfterSalesInstallstionVo" parameterType="com.vedeng.aftersales.model.AfterSalesInstallstion"
            resultMap="VoBaseResultMap">
        select
        a.AFTER_SALES_INSTALLSTION_ID, a.AFTER_SALES_ID, a.ENGINEER_ID, a.SERVICE_TIME,
        a.ENGINEER_AMOUNT, a.NOTICE_TIMES, a.LAST_NOTICE_TIME,
        a.SERVICE_SCORE, a.SKILL_SCORE, a.SCORE_USER_ID, a.SCORE_TIME, a.SCORE_COMMENTS, a.PAYMENT_STATUS,
        a.PAYMENT_TIME, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER, b.NAME, b.MOBILE
        from
        T_AFTER_SALES_INSTALLSTION a
        left join
        T_ENGINEER b on a.ENGINEER_ID = b.ENGINEER_ID
        where 1=1
        <if test="afterSalesInstallstionId != null">
            AND a.AFTER_SALES_INSTALLSTION_ID = #{afterSalesInstallstionId,jdbcType=INTEGER}
        </if>
        <if test="afterSalesId != null">
            AND a.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
        </if>
        <if test="engineerId != null">
            and a.ENGINEER_ID=#{engineerId,jdbcType=INTEGER}
        </if>
        order by a.SERVICE_TIME desc
    </select>

    <select id="getSafetyListByParam" resultMap="BaseResultMap"
            parameterType="com.vedeng.aftersales.model.AfterSalesInstallstion">
        select
        <include refid="Base_Column_List"/>
        from T_AFTER_SALES_INSTALLSTION
        where 1=1
        <if test="afterSalesId != null">
            AND AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
        </if>
        order by SERVICE_TIME desc
    </select>

    <!-- 获取待同步售后安调数据 -->
    <select id="getWaitSyncAfterSalesInstallstionList" resultMap="BaseResultMap"
            parameterType="com.vedeng.aftersales.model.AfterSalesInstallstion">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        T_AFTER_SALES_INSTALLSTION
        WHERE
        AFTER_SALES_INSTALLSTION_ID NOT IN (
        SELECT
        a.AFTER_SALES_INSTALLSTION_ID
        FROM
        T_AFTER_SALES_INSTALLSTION a
        JOIN T_DATA_SYNC_STATUS b ON a.AFTER_SALES_INSTALLSTION_ID = b.RELATED_ID
        WHERE
        b.SOURCE_TABLE = 'T_AFTER_SALES_INSTALLSTION'
        AND b.GOAL_TYPE = 591
        AND b. STATUS = 1
        );
    </select>

    <select id="getInstallstionInfo" parameterType="com.vedeng.aftersales.model.vo.AfterSalesInstallstionVo"
            resultType="com.vedeng.aftersales.model.vo.AfterSalesInstallstionVo">
  		select
  			a.AFTER_SALES_ID,b.AFTER_SALES_NO,b.TYPE,
  			d.TRADER_CONTACT_NAME,d.TRADER_CONTACT_MOBILE,
  			c.NAME,c.MOBILE
  		from
  			T_AFTER_SALES_INSTALLSTION a
  		left join
  			T_AFTER_SALES b
  		on
  			a.AFTER_SALES_ID = b.AFTER_SALES_ID
  		left join
  			T_ENGINEER c
  		on
  			c.ENGINEER_ID = a.ENGINEER_ID
  		left join
  			T_AFTER_SALES_DETAIL d
  		on
  			a.AFTER_SALES_ID = d.AFTER_SALES_ID
  		where
  			a.AFTER_SALES_INSTALLSTION_ID = #{afterSalesInstallstionId,jdbcType=INTEGER}
  	</select>

    <select id="getAfterSalesInstallstionAmount" resultType="java.math.BigDecimal">
  		select COALESCE(sum(ENGINEER_AMOUNT),0)
	  	from
	  		T_AFTER_SALES_INSTALLSTION
	  	where
	  		AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
    </select>
</mapper>