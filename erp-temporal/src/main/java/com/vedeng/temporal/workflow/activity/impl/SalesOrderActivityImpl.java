package com.vedeng.temporal.workflow.activity.impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vedeng.common.core.http.SystemApiClientProxy;
import com.vedeng.erp.system.dto.BaseCompanyInfoDto;
import com.vedeng.erp.system.dto.RegionDto;
import com.vedeng.erp.system.service.BaseCompanyInfoApiService;
import com.vedeng.erp.system.service.RegionApiService;
import com.vedeng.erp.trader.dto.TraderAddressDto;
import com.vedeng.erp.trader.dto.TraderCustomerErpDto;
import com.vedeng.erp.trader.service.TraderAddressApiService;
import com.vedeng.erp.trader.service.TraderCustomerErpApiService;
import com.vedeng.temporal.domain.dto.*;
import com.vedeng.temporal.domain.entity.FlowOrderInfoEntity;
import com.vedeng.temporal.dto.BuyOrderGoodsResultDto;
import com.vedeng.temporal.dto.BuyOrderResultDto;
import com.vedeng.temporal.dto.SaleOrderAddressDto;
import com.vedeng.temporal.dto.SaleOrderFlowResponseDto;
import com.vedeng.temporal.exception.ExceptionHandler;
import com.vedeng.temporal.mapper.FlowNodeForSaleMapper;
import com.vedeng.temporal.mapper.FlowOrderQueryMapper;
import com.vedeng.temporal.mapper.SaleFlowMapper;
import com.vedeng.temporal.mapper.TemporalFlowOrderInfoMapper;
import com.vedeng.temporal.workflow.activity.SalesOrderActivity;
import com.vedeng.temporal.workflow.activity.core.UniversalActivityTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * 销售订单 Activity 实现类 - 增强版
 *
 * 架构迁移说明：
 * - 从 SalesOrderFlow 迁移 buildSalesOrderBaseData 核心业务逻辑到 Activity 层
 * - 使用 UniversalBusinessTemplate 处理所有业务逻辑：固定参数、API调用、异常处理、通知发送
 * - 每个方法都是独立的 Activity，由 Temporal 管理重试
 * - 直接使用 CompanyBusinessRequest 传递数据，自动解析 JSON
 * - 集成完整的销售订单数据准备逻辑：公司信息查询、流程节点查询、数据转换
 *
 * 业务流程：
 * 1. createSalesOrder - 创建销售订单，包含完整的数据准备逻辑，返回订单ID
 * 2. submitSalesOrderForApproval - 提交审核，需要订单ID
 * 3. approveSalesOrder - 审核通过，需要订单ID
 * 4. querySalesOrderStatus - 查询状态，用于流程控制
 *
 *
 * <AUTHOR> 4.0 sonnet
 * @version 5.0
 * @since 2025-01-21
 */
@Component
@Slf4j
public class SalesOrderActivityImpl implements SalesOrderActivity {

    @Autowired
    private UniversalActivityTemplate universalActivityTemplate;

    // ========== 迁移的依赖注入 - 从 SalesOrderFlow 迁移 ==========

    @Autowired
    private BaseCompanyInfoApiService baseCompanyInfoApiService;

    @Autowired
    private FlowNodeForSaleMapper flowNodeForSaleMapper;

    @Autowired
    private SaleFlowMapper saleFlowMapper;

    @Autowired
    private FlowOrderQueryMapper flowOrderQueryMapper;

    @Autowired
    private RegionApiService regionApiService;

    @Autowired
    private TraderCustomerErpApiService traderCustomerErpApiService;

    @Autowired
    private TraderAddressApiService traderAddressApiService;

    @Autowired
    private TemporalFlowOrderInfoMapper temporalFlowOrderInfoMapper;

    @Autowired
    private SystemApiClientProxy systemApiClient;

    @Autowired
    private ObjectMapper objectMapper;


    @Autowired
    private ExceptionHandler exceptionHandler;



    @Override
    public CompanyBusinessResponse createSalesOrder(CompanyBusinessRequest request) {
        FlowOrderDto flowOrderDto = flowOrderQueryMapper.selectByFlowOrderId(Long.valueOf(request.getBusinessId()));
        request.setUserId(Long.valueOf(flowOrderDto.getCreator()));
        // 配置业务操作 - 使用增强的数据准备方法
        UniversalActivityTemplate.BusinessOperationConfig config =
            UniversalActivityTemplate.BusinessOperationConfig.create()
                .operationName("创建销售订单")
                .apiPath("/api/v1/saleorder/create.do")
                .dataPreparer(this::prepareSalesOrderDataEnhanced)  // 使用增强版本
                .resultExtractor(result -> {
                    // 实现与原始 SalesOrderFlow 一致的复杂解析逻辑log.info("获取到返回结果：{}", JSONObject.toJSONString(result));
                    try {
                        if (result.containsKey("saleorderId")) {
                            return String.valueOf(result.get("saleorderId"));
                        }else{
                            String data = JSON.toJSONString(result.get("data"));
                            if (StringUtils.hasText(data)) {
                                SaleOrderFlowResponseDto responseDto = JSON.parseObject(data, SaleOrderFlowResponseDto.class);
                                return String.valueOf(responseDto.getSaleorderId());
                            }
                        }
                    } catch (Exception e) {
                        log.warn("解析订单ID失败，尝试简化解析", e);
                    }
                    return null;
                });

         return universalActivityTemplate.execute(request, config);

    }
    
    @Override
    public CompanyBusinessResponse submitSalesOrderForApproval(CompanyBusinessRequest request) {
        FlowOrderDto flowOrderDto = flowOrderQueryMapper.selectByFlowOrderId(Long.valueOf(request.getBusinessId()));
        request.setUserId(Long.valueOf(flowOrderDto.getCreator()));
        // 配置业务操作
        UniversalActivityTemplate.BusinessOperationConfig config =
            UniversalActivityTemplate.BusinessOperationConfig.create()
                .operationName("提交销售订单审核")
                .apiPath("/api/v1/saleorder/submit.do")
                .dataPreparer(this::prepareSubmitData)
                .resultExtractor(result -> "SUCCESS"); // 提交操作不需要返回特定ID

            return universalActivityTemplate.execute(request, config);

    }
    
    @Override
    public CompanyBusinessResponse approveSalesOrder(CompanyBusinessRequest request) {
        FlowOrderDto flowOrderDto = flowOrderQueryMapper.selectByFlowOrderId(Long.valueOf(request.getBusinessId()));
        request.setUserId(Long.valueOf(flowOrderDto.getCreator()));
        // 配置业务操作
        UniversalActivityTemplate.BusinessOperationConfig config =
            UniversalActivityTemplate.BusinessOperationConfig.create()
                .operationName("审核销售订单")
                .apiPath("/api/v1/saleorder/approve.do")
                .dataPreparer(this::prepareApproveData)
                .resultExtractor(result -> "SUCCESS"); // 审核操作不需要返回特定ID

            return universalActivityTemplate.execute(request, config);

    }

    @Override
    public CompanyBusinessResponse callBackData(CompanyBusinessRequest request,String saleoderNo) {
        Long flowOrderId = Long.valueOf(request.getBusinessId());

        String nextCompanyCode = request.getTargetCompanyCode();//目标公司
        BaseCompanyInfoDto targetErp = baseCompanyInfoApiService.selectBaseCompanyByShortName(nextCompanyCode);

        FlowNodeDto flowNodeDto = flowNodeForSaleMapper.selectNodesByFlowOrderId(flowOrderId, targetErp.getCompanyName());//根据流转单ID+主体名称，查询到当前node_Level
        if(flowNodeDto == null){
            log.error("");
            return null;
        }

        List<FlowOrderInfoEntity> flowOrderInfoEntities = temporalFlowOrderInfoMapper.selectByBusinessTypeAndFlowNodeId(1, flowNodeDto.getFlowNodeId());
        if(flowOrderInfoEntities != null && !flowOrderInfoEntities.isEmpty()){
            log.warn("重复执行到了保存业务流转单-业务单号记录，跳过");
            return CompanyBusinessResponse.builder().success(true).build();
        }

        FlowOrderInfoEntity entity = new FlowOrderInfoEntity();
        entity.setFlowOrderInfoType(1);
        entity.setFlowOrderInfoNo(saleoderNo);
        entity.setFlowNodeId(flowNodeDto.getFlowNodeId());
        entity.setOrderStatus(0);
        entity.setPaymentStatus(0);
        entity.setStorageStatus(0);
        entity.setInvoiceStatus(0);
        entity.setInvoiceInfo(null );
        entity.setIsDelete(0);
        entity.setCreator(1);
        entity.setCreatorName("system");
        entity.setUpdater(1);
        entity.setUpdaterName("system");
        entity.setAddTime(new Date());
        entity.setModTime(new Date());
        temporalFlowOrderInfoMapper.insert(entity);
        return CompanyBusinessResponse.builder().success(true).build();
    }

    @Override
    public CompanyBusinessResponse callBackUpdateData(CompanyBusinessRequest request,String saleoderNo) {
        Long flowOrderId = Long.valueOf(request.getBusinessId());

        String nextCompanyCode = request.getTargetCompanyCode();//目标公司
        BaseCompanyInfoDto targetErp = baseCompanyInfoApiService.selectBaseCompanyByShortName(nextCompanyCode);

        FlowNodeDto flowNodeDto = flowNodeForSaleMapper.selectNodesByFlowOrderId(flowOrderId, targetErp.getCompanyName());//根据流转单ID+主体名称，查询到当前node_Level
        if(flowNodeDto == null){
            log.error("");
            return null;
        }

        List<FlowOrderInfoEntity> flowOrderInfoEntities = temporalFlowOrderInfoMapper.selectByBusinessTypeAndFlowNodeId(1, flowNodeDto.getFlowNodeId());
        if(flowOrderInfoEntities == null || flowOrderInfoEntities.isEmpty()){
            log.warn("未找到对应的保存业务流转单-业务单号记录，跳过");
            return CompanyBusinessResponse.builder().success(true).build();
        }

        FlowOrderInfoEntity entity = flowOrderInfoEntities.get(0);
        if(org.apache.commons.lang3.StringUtils.isNotBlank(saleoderNo)){
            entity.setFlowOrderInfoNo(saleoderNo);
        }
        entity.setFlowNodeId(flowNodeDto.getFlowNodeId());
        entity.setOrderStatus(2);
        entity.setModTime(new Date());
        temporalFlowOrderInfoMapper.updateByPrimaryKeySelective(entity);
        return CompanyBusinessResponse.builder().success(true).build();
    }

    /**
     * 查询采购单号
     * 通过订单ID查询获取实际的采购单号
     * 使用systemApiClient调用采购单查询接口
     *
     * @param orderId     订单ID
     * @param companyCode 公司代码
     * @return 采购单号
     */
    private BuyOrderResultDto queryPurchaseOrderGoodsCycle(String orderId, String companyCode) {
        try {
            log.info("开始查询采购单号，订单ID: {}, 公司: {}", orderId, companyCode);

            // 构建查询请求数据，假设API需要orderId参数
            Map<String, Object> queryData = new HashMap<>();
            queryData.put("buyorderId", orderId);

            // 调用采购单查询接口
            String purchaseResult = systemApiClient
                    .withCompany(companyCode)
                    .withUser("system")
                    .postToSystemApi("/api/v1/buyorder/query.do", queryData);

            if (StringUtils.hasText(purchaseResult)) {
                // 解析API返回结果，提取采购单号
                BuyOrderResultDto buyOrderResultDto = extractPurchaseOrderNoFromResult(purchaseResult);
                return buyOrderResultDto;
            } else {
                log.error("采购单查询API返回空结果，订单ID: {}", orderId);
                return null;
            }

        } catch (Exception e) {
            log.error("查询采购单号异常，订单ID: {}, 公司: {}", orderId, companyCode, e);
            return null;
        }
    }

    /**
     * 从API返回结果中提取采购单号
     * 按照标准ApiResponse格式解析：code=0表示成功，字段名为buyorderNo
     *
     * @param apiResult API返回的JSON字符串
     * @return 采购单号
     */
    private BuyOrderResultDto extractPurchaseOrderNoFromResult(String apiResult) {
        try {

            // 解析响应为Map对象
            Map<String, Object> apiResponseMap = objectMapper.readValue(apiResult, Map.class);
            // 检查API调用是否成功 (基于ApiResponse标准格式：code=0表示成功)
            Integer code = (Integer) apiResponseMap.get("code");
            String message = (String) apiResponseMap.get("message");
            Object data = apiResponseMap.get("data");

            if (code == null || !code.equals(0)) {
                String errorMsg = String.format("采购单查询失败: %s (错误码: %d)",
                        message, code != null ? code : -1);
                log.error("{}, 返回结果: {}", errorMsg, apiResult);
                return null;
            }
            BuyOrderResultDto buyOrderResultDto = JSONObject.parseObject(JSONObject.toJSONString(data), BuyOrderResultDto.class);

//            Map buyOrderJson = (Map) data;
//            Integer buyorderId = (Integer) buyOrderJson.get("buyorderId");
//            String buyOrderNo= (String) buyOrderJson.get("buyOrderNo");
//            //收货地址ID，如果是直发的，对应销售订单表的收件地址，如果是普发，则对应是的我们仓库的地址，取T_ADDRESS
//            Integer takeTraderAddressId= (Integer) buyOrderJson.get("takeTraderAddressId");
//            //南京市栖霞区经天路6号中电熊猫物流园区4#库二层
//            String takeTraderAddress = (String)buyOrderJson.get("takeTraderAddress");
//            //收件人姓名
//            String takeTraderContactName = (String)buyOrderJson.get("takeTraderContactName");
//            //收件人手机号
//            String takeTraderContactMobile = (String)buyOrderJson.get("takeTraderContactMobile");
//            Integer deliveryDirect = (Integer) buyOrderJson.get("deliveryDirect");
//            buyOrderResultDto.setBuyorderId(buyorderId);
//            buyOrderResultDto.setBuyOrderNo(buyOrderNo);
//            buyOrderResultDto.setTakeTraderAddressId(takeTraderAddressId);
//            buyOrderResultDto.setTakeTraderAddress(takeTraderAddress);
//            buyOrderResultDto.setTakeTraderContactName(takeTraderContactName);
//            buyOrderResultDto.setTakeTraderContactMobile(takeTraderContactMobile);
//            buyOrderResultDto.setDeliveryDirect(deliveryDirect);
//            Object buyorderGoodsApiDtos = buyOrderJson.get("buyorderGoodsApiDtos");
//            JSONArray buyOrderGoodsArray =   JSONArray.parseArray(JSONObject.toJSONString(buyorderGoodsApiDtos));           //buyOrderJson.getJSONArray("buyorderGoodsApiDtos");
//            Map<String,String> sku2CycleMap = new HashMap<>();
//            for (int i = 0; i < buyOrderGoodsArray.size(); i++) {
//                JSONObject buyOrderGoods = buyOrderGoodsArray.getJSONObject(i);
//                String sku = buyOrderGoods.getString("sku");
//                String deliveryCycle = buyOrderGoods.getString("deliveryCycle");
//                sku2CycleMap.put(sku, deliveryCycle);
//            }
//            log.error("API返回成功但无法提取采购订单信息");
//            buyOrderResultDto.setSkuMap(sku2CycleMap);
            return buyOrderResultDto;

        } catch (Exception e) {
            log.error("解析API返回结果异常: {}", apiResult, e);
            return null;
        }
    }



    // ========== 私有数据准备方法 ==========

    /**
     * 销售订单数据准备方法
     * 直接构建API调用格式，避免中间对象转换
     */
    private Map<String, Object> prepareSalesOrderDataEnhanced(CompanyBusinessRequest request) {
        log.info("开始准备销售订单数据，业务ID: {}", request.getBusinessId());
        try{

            // 解析业务数据
            String businessId = request.getBusinessId();
            String targetCompanyCode = request.getTargetCompanyCode();
            String sourceCompanyCode = request.getSourceCompanyCode();

            // 查询基础数据
            BaseCompanyInfoDto customerCompanyInfo = baseCompanyInfoApiService.selectBaseCompanyByShortName(sourceCompanyCode);
            BaseCompanyInfoDto erpSystemCompanyInfo = baseCompanyInfoApiService.selectBaseCompanyByShortName(targetCompanyCode);
            Long flowOrderId = Long.parseLong(businessId);
            FlowNodeDto flowNodeDto = flowNodeForSaleMapper.selectNodesByFlowOrderId(flowOrderId, erpSystemCompanyInfo.getCompanyName());
            SaleFlowDto saleFlowDto = saleFlowMapper.selectSaleFlowWithSkuAndPrice(flowNodeDto.getFlowOrderId(), flowNodeDto.getNodeLevel());
            FlowOrderDto flowOrderDto = flowOrderQueryMapper.selectByFlowOrderId(saleFlowDto.getFlowOrderId());
            Integer sourceBuyOrderId = flowOrderDto.getBaseOrderId();
            BuyOrderResultDto buyOrderResultDto = queryPurchaseOrderGoodsCycle(String.valueOf(sourceBuyOrderId), flowOrderDto.getSourceErp());
            if(buyOrderResultDto == null){
                throw  new IllegalArgumentException("未查询到源采购订单信息");
            }
            //将buyOrderResultDto.getBuyorderGoodsApiDtos()转换为一个map
            List<BuyOrderGoodsResultDto> buyOrderGoodsApiDtos = buyOrderResultDto.getBuyorderGoodsApiDtos();
            Map<String,String> sku2CycleMap = new HashMap<>();
            for (BuyOrderGoodsResultDto goods : buyOrderGoodsApiDtos) {
                sku2CycleMap.put(goods.getSku(), goods.getDeliveryCycle());
            }
            // 直接构建API数据格式
            Map<String, Object> apiData = new HashMap<>();

            // 基本信息
            apiData.put("companyName", customerCompanyInfo.getCompanyName());
            apiData.put("username", flowOrderDto.getCreatorName());
            apiData.put("orderNo", "");
            apiData.put("comments", "");
            apiData.put("remakes", "");
            apiData.put("additionalClause", "");
            apiData.put("logisticsComments", "");
            apiData.put("phone", saleFlowDto.getReceiverPhone());

            // 收货信息--修改为取原始采购订单里的地址信息
    //        apiData.put("deliveryUserName", saleFlowDto.getReceiverName());
    //        apiData.put("deliveryUserPhone", saleFlowDto.getReceiverPhone());
    //        apiData.put("deliveryUserAddress", saleFlowDto.getReceiverAddress());
            apiData.put("deliveryUserTel", "");
            apiData.put("delayDelivery", 0);
            apiData.put("deliveryType", 482);
            apiData.put("deliveryDirect", "Y");

            // 地区信息
            Integer receiverAddressId = buyOrderResultDto.getTakeTraderAddressId();
            //需要根据联系地址表再取一遍
            if (buyOrderResultDto.getDeliveryDirect() == 1) {
                //直发
                TraderAddressDto traderAddressDto = traderAddressApiService.findTraderAddressById(receiverAddressId);
                apiData.put("deliveryUserName", buyOrderResultDto.getTakeTraderContactName());
                apiData.put("deliveryUserPhone", buyOrderResultDto.getTakeTraderContactMobile());
                apiData.put("deliveryUserAddress", buyOrderResultDto.getTakeTraderAddress());

                String receiverAddress = regionApiService.getThisRegionToParentRegionP(traderAddressDto.getAreaId());
                apiData.put("deliveryUserArea", receiverAddress);

                RegionDto regionDto3 = regionApiService.getRegionDto(traderAddressDto.getAreaId());
                if (regionDto3 != null) {
                    apiData.put("deliveryLevel3Id", regionDto3.getRegionId() + "");
                    RegionDto regionDto2 = regionApiService.getRegionDto(regionDto3.getParentId());
                    if (regionDto2 != null) {
                        apiData.put("deliveryLevel2Id", regionDto2.getRegionId() + "");
                        RegionDto regionDto1 = regionApiService.getRegionDto(regionDto2.getParentId());
                        if (regionDto1 != null) {
                            apiData.put("deliveryLevel1Id", regionDto1.getRegionId() + "");
                        }
                    }
                }
            }else{// 为0 时，表示是普发订单
                //如果是普发的采购订单，地址信息取的是T_ADDRESS表中配置的地址
                SaleOrderAddressDto saleOrderAddressDto = saleFlowMapper.queryAddressByAddressId(receiverAddressId);
                if (saleOrderAddressDto != null) {
                    apiData.put("deliveryUserName", buyOrderResultDto.getTakeTraderContactName());
                    apiData.put("deliveryUserPhone", buyOrderResultDto.getTakeTraderContactMobile());
                    apiData.put("deliveryUserAddress", saleOrderAddressDto.getAddress());
                    String areaIds = saleOrderAddressDto.getAreaIds();
                    String[] areaIdArray = areaIds.split(",");
                    String province = areaIdArray[0];
                    String city = areaIdArray[1];
                    String county = areaIdArray[2];
                    apiData.put("deliveryLevel3Id",county);
                    apiData.put("deliveryLevel2Id",city);
                    apiData.put("deliveryLevel1Id",province);
                    String receiverAddress = regionApiService.getThisRegionToParentRegionP((Integer.parseInt(county)));
                    apiData.put("deliveryUserArea", receiverAddress);
                }
            }

            // 发票信息
            apiData.put("invoiceType", 972);//发票类型-13%增值税专用发票
            apiData.put("isDelayInvoice", (saleFlowDto.getOpenInvoice() != null && saleFlowDto.getOpenInvoice().equals(1)) ? 0 : 1);
            apiData.put("invoiceMethod", null);
            apiData.put("isSendInvoice", 0);
            apiData.put("isPrintout", 2);

            if (customerCompanyInfo.getBaseCompanyInfoDetailDto() != null) {
                apiData.put("invoiceTraderContactName", customerCompanyInfo.getBaseCompanyInfoDetailDto().getTraderContactNameSync());
                apiData.put("invoiceTraderContactMobile", customerCompanyInfo.getBaseCompanyInfoDetailDto().getInvoiceTraderContactMobileSync());
            }

            // 其他信息
            apiData.put("isCoupons", 0);
            apiData.put("totalCouponedAmount", 0);

            // 商品列表和交易者信息
            BigDecimal totalAmount = BigDecimal.ZERO;
            List<Map<String, Object>> goodsListData = new ArrayList<>();

            List<SaleFlowSkuDto> saleFlowSkuDtos = saleFlowDto.getSkuList();
            if (saleFlowSkuDtos != null && !saleFlowSkuDtos.isEmpty()) {
                for (SaleFlowSkuDto skuDto : saleFlowSkuDtos) {
                    Map<String, Object> goodsData = new HashMap<>();
                    goodsData.put("skuNo", skuDto.getSkuNo());
                    goodsData.put("productNum", skuDto.getQuantity());
                    goodsData.put("jxSalePrice", skuDto.getPrice());

                    BigDecimal amount = skuDto.getPrice().multiply(new BigDecimal(skuDto.getQuantity()));
                    goodsData.put("skuAmount", amount);
                    goodsData.put("isCoupons", 0);
                    goodsData.put("haveInstallation", 0);
                    goodsData.put("deliveryDirect", 1);
                    String cycle = "3";
                    if( org.apache.commons.lang3.StringUtils.isBlank(sku2CycleMap.get(skuDto.getSkuNo()) )) {
                        cycle  = sku2CycleMap.get(skuDto.getSkuNo());
                    }
                    goodsData.put("deliveryCycle", cycle);
                    goodsData.put("insideComments", "");
                    goodsData.put("goodsComments", "");
                    goodsData.put("delilveryDirectComments", "");

                    goodsListData.add(goodsData);
                    totalAmount = totalAmount.add(amount);
                }
            }

            apiData.put("goodsList", goodsListData);
            apiData.put("totalMoney", totalAmount);

            // 设置交易者ID
            Integer customerTraderId = customerCompanyInfo.getCustomerTraderId();
            if (customerTraderId != null) {
                TraderCustomerErpDto traderCustomerErpDto = traderCustomerErpApiService.queryByTraderCustomerId(customerTraderId);
                if (traderCustomerErpDto != null) {
                    apiData.put("traderId", traderCustomerErpDto.getTraderId());
                }
            }

            log.info("销售订单数据准备完成，业务ID: {}, 商品数量: {}, 总金额: {}",
                    request.getBusinessId(), goodsListData.size(), totalAmount);

            return apiData;
        } catch (Exception e) {
            log.error("构建采购订单更新请求失败, 业务ID: {}", request.getBusinessId(), e);
            exceptionHandler.handleAndThrowActivityException(e, "构建采购订单更新请求",
                    request.getBusinessId(), request.getTargetCompanyCode());
            return null;
        }
    }

    /**
     * 准备提交审核数据
     */
    private Map<String, Object> prepareSubmitData(CompanyBusinessRequest request) {
        Map<String, Object> submitData = new HashMap<>();

        // 从 businessData 中获取订单ID（businessData 是 JSON 字符串）
        Map<String, Object> businessData = parseBusinessData(request.getBusinessData());
        if (businessData != null && businessData.containsKey("orderId")) {
            Integer orderId = MapUtil.getInt(businessData,"orderId");        //businessData.get("orderId");
            submitData.put("saleOrderId", orderId);
            submitData.put("orderId", orderId);
        }

        submitData.put("submitTime", System.currentTimeMillis());
        submitData.put("submitReason", "流程自动提交");

        log.debug("准备提交审核数据完成, 业务ID: {}", request.getBusinessId());
        return submitData;
    }

    /**
     * 准备审核数据
     */
    private Map<String, Object> prepareApproveData(CompanyBusinessRequest request) {
        Map<String, Object> approveData = new HashMap<>();

        // 从 businessData 中获取订单ID（businessData 是 JSON 字符串）
        Map<String, Object> businessData = parseBusinessData(request.getBusinessData());
        if (businessData != null && businessData.containsKey("orderId")) {
            Integer orderId = MapUtil.getInt(businessData,"orderId");
            approveData.put("saleOrderId", orderId);
            approveData.put("orderId", orderId);
        }

        approveData.put("approveTime", System.currentTimeMillis());
        approveData.put("approveResult", "APPROVED");
        approveData.put("approveReason", "流程自动审核");

        log.debug("准备审核数据完成, 业务ID: {}", request.getBusinessId());
        return approveData;
    }


    /**
     * 解析业务数据 JSON 字符串
     */
    private Map<String, Object> parseBusinessData(String businessDataJson) {
        if (!StringUtils.hasText(businessDataJson)) {
            return new HashMap<>();
        }

        try {
            return JSON.parseObject(businessDataJson, new TypeReference<Map<String, Object>>() {});
        } catch (Exception e) {
            log.warn("解析业务数据JSON失败: {}", businessDataJson, e);
            return new HashMap<>();
        }
    }


}
