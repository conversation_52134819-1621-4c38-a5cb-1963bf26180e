package com.newtask.esign;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.system.SystemUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.base.api.dto.reqParam.BusinessInfo;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.buyorder.manager.esign.BuyOrderElectronicSignHandle;
import com.vedeng.erp.saleorder.manager.esign.SaleOrderElectronicSignHandle;
import com.vedeng.infrastructure.esign.domain.ElectronicSignParam;
import com.vedeng.infrastructure.esign.enums.ElectronicSignBusinessEnums;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 补充历史订单电子签章
 * @date 2022/5/16 10:57
 */
@JobHandler(value = "SupplementOrderSign")
@Component
public class SupplementOrderSign extends AbstractJobHandler {

    @Autowired
    private SaleOrderElectronicSignHandle saleOrderElectronicSignHandle;

    @Autowired
    private BuyOrderElectronicSignHandle buyOrderElectronicSignHandle;

    private final static String FILE_PATH = SystemUtil.getOsInfo().isLinux() ? "/tmp/orderExcel.xlsx" : "C:/Users/<USER>/Desktop/orderExcel.xlsx";


    /**
     * @param param {"orderType":"","orderId":""}
     *              orderType->订单类型：2=销售单 3=采购单
     *              orderId-> 订单id
     */
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("===================补充历史订单电子签章任务开始===================");
        int index = 0;
        Integer orderType = null;
        List<Integer> orderIds = new ArrayList<>();
        if (StrUtil.isNotEmpty(param)) {
            JSONObject jsonObject = JSON.parseObject(param);
            orderType = jsonObject.getInteger("orderType");
            String orderIdsStringByComma = jsonObject.getString("orderId");
            orderIds = StrUtil.split(orderIdsStringByComma, StrUtil.COMMA).stream().map(Integer::parseInt).collect(Collectors.toList());
        } else {
            ExcelReader reader = ExcelUtil.getReader(FileUtil.file(FILE_PATH));
            List<Map<String, Object>> readAll = reader.readAll();
            if (CollUtil.isNotEmpty(readAll)) {
                orderType = Integer.parseInt(CollUtil.getFirst(readAll).get("orderType").toString());
                for (Map<String, Object> r : readAll) {
                    Integer orderId = Integer.parseInt(r.get("orderId").toString());
                    orderIds.add(orderId);
                }
            }
        }

        if (CollUtil.isNotEmpty(orderIds)) {
            XxlJobLogger.log("总任务数：" + orderIds.size());
            for (Integer orderId : orderIds) {
                Thread.sleep(3000);
                index++;
                BusinessInfo businessInfo = new BusinessInfo();
                businessInfo.setOperator(ErpConstant.DEFAULT_USERNAME);

                if (orderType == null) {
                    return FAIL;
                }
                if (orderType.equals(ElectronicSignBusinessEnums.SALE_ORDER.getType())) {
//                    ElectronicSignParam electronicSignParam = ElectronicSignParam
//                            .builder()
//                            .flowType(2)
//                            .businessInfo(businessInfo)
//                            .electronicSignBusinessEnums(ElectronicSignBusinessEnums.getEnum(orderType))
//                            .build();
//                    electronicSignParam.setSaleOrderId(orderId);
                    ElectronicSignParam electronicSignParam = saleOrderElectronicSignHandle.buildElectronicSignParam(orderId,businessInfo);
                    saleOrderElectronicSignHandle.electronicSign(electronicSignParam);
                } else {
//                    electronicSignParam.setBuyOrderId(orderId);
//                    buyOrderElectronicSignHandle.electronicSign(electronicSignParam);

                    ElectronicSignParam electronicSignParam = buyOrderElectronicSignHandle.buildElectronicSignParam(orderId,businessInfo);
                    buyOrderElectronicSignHandle.electronicSign(electronicSignParam);
                }
                XxlJobLogger.log("正在处理第" + index + "条数据,orderId=" + orderId);
            }
        }
        XxlJobLogger.log("===================补充历史订单电子签章任务结束===================");
        return SUCCESS;
    }


}
