package com.vedeng.mobile.system.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2023/12/7
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VisitContentInputDto {

    /**
     * 拜访记划的ID
     */
    private Integer id;

    /**
     * 用户的ID
     */
    private Integer userId;

    /**
     * 拜访内容-联系人信息-姓名
     */
    private String contactName;

    /**
     * 拜访内容-联系人信息-手机
     */
    private String contactMobile;

    /**
     * 拜访内容-联系人信息-电话
     */
    private String contactTele;

    /**
     * 拜访内容-联系人信息-职位
     */
    private String contactPosition;


    /**
     * 沟通事项-讲解PPT (默认否，Y是N否)
     */
    private String showPpt;

    /**
     * 沟通事项-邀请客户注册 (默认否，Y是N否)
     */
    private String inviteReg;

    /**
     * 沟通事项-邀请客户注册-手机号
     */
    private String regMobile;

    /**
     * 沟通事项-邀请客户注册-联系人ID
     */
    private Integer traderContractId;

    /**
     * 沟通事项-沟通情况
     */
    private String commucateContent;

    /**
     * 沟通事项-预计下次拜访时间
     */
    private Date nextVisitDate;

    /**
     * 沟通事项-是否产生商机 (Y是N否)
     */
    private String createBusinessChange;
}
