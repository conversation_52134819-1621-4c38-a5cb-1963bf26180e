package com.vedeng.erp.trader.dto;

import com.vedeng.common.core.base.BaseDto;
import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 通话场景详细字段表-返回页面端展示用
 */
@Getter
@Setter
public class VoiceFieldResultDto extends BaseDto {
    /**
     * 自增主键
     */
    private Integer fieldResultId;

    /**
     * 沟通记录id
     */
    private Integer communicateRecordId;

    /**
     * 场景编码
     */
    private String senceCode;

    /**
     * 分组编码
     */
    private String groupCode;

    /**
     * 字段英文名
     */
    private String fieldCode;

    /**
     * 字段名称
     */
    private String fieldName;

    /**
     * gpt解析结果
     */
    private String fieldResult;

    /**
     * 处理结果（Y是N否）
     */
    private String doFlag;

    /**
     * 序号
     */
    private Integer fieldOrder;

    /**
     * 添加人
     */
    private Integer addUserId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private Integer updateUserId;

    private List<AfterSaleGoodsTodo> afterSaleGoodsTodos;

    /**
     * 售后政策产品信息
     */
    @Data
    public static class AfterSaleGoodsTodo{
        private String skuNo;
        private String skuName;
    }

}