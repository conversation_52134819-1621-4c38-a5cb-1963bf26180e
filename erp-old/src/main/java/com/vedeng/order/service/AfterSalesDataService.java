package com.vedeng.order.service;

import java.math.BigDecimal;

public interface AfterSalesDataService {
    /**
     * <b>Description:</b><br> 获取销售售后订单收款（客户支付服务费，手续费）/采购售后订单付款金额（支付给供应商服务费，手续费）
     *
     * @param afterSalesId
     * @param orderType    1销售订单售后 2采购订单售后
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年11月2日 下午2:22:59
     */
    BigDecimal getPayAmount(Integer afterSalesId, Integer orderType);

    /**
     * <b>Description:</b><br> 获取销售售后订单退款（退款给客户）/采购售后订单退款金额（供应商退款给我们）
     *
     * @param afterSalesId
     * @param orderType    1销售订单售后 2采购订单售后
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年11月3日 上午9:26:59
     */
    BigDecimal getRefundAmount(Integer afterSalesId, Integer orderType);

    /**
     * <b>Description:</b><br> 获取销售售后订单退款（退款给客户）/采购售后订单退款金额（供应商退款给我们）
     *
     * @param afterSalesId
     * @param orderType    1销售订单售后 2采购订单售后
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年11月3日 上午9:26:59
     */
    BigDecimal getRefundTraderAmount(Integer afterSalesId, Integer orderType);

    /**
     * <b>Description:</b><br> 销售订单付款金额（工程师安调费用）
     *
     * @param afterSalesId
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年11月3日 上午9:30:56
     */
    BigDecimal getSaleorderAfterSalesPayAmount(Integer afterSalesId);

    /**
     * <b>Description:</b><br> 获取销售售后订单账期退款金额（退款给客户）/采购售后订单账期退款金额（供应商退款给我们）
     *
     * @param afterSalesId
     * @param orderType
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年11月17日 下午1:29:48
     */
    BigDecimal getPeriodAmount(Integer afterSalesId, Integer orderType);

    /**
     * <b>Description:</b><br> 采购售后订单退款金额（供应商退款给我们）
     *
     * @param afterSalesId
     * @param orderType
     * @return
     * @Note <b>Author:</b> Administrator
     * <br><b>Date:</b> 2018年1月17日 下午1:30:45
     */
    BigDecimal getRefundVedengAmount(Integer afterSalesId, int orderType);

    /**
     * <b>Description:</b><br> 查询销售订单的所有已完结的售后退货金额
     *
     * @param saleorderId
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2018年2月3日 下午3:37:53
     */
    BigDecimal getRefundAmountBySaleorderId(Integer saleorderId);

    /**
     * <b>Description:</b><br> 查询销售订单退还余额的金额
     *
     * @param saleorderId
     * @return
     * @Note <b>Author:</b> leo.yang
     * <br><b>Date:</b> 2018年6月4日 上午11:13:22
     */
    BigDecimal getRefundBalanceAmountBySaleorderId(Integer saleorderId);

    /**
     * <b>Description:</b><br> 获取售后工程师酬金总和
     *
     * @param afterSalesId
     * @return
     * @Note <b>Author:</b> leo.yang
     * <br><b>Date:</b> 2018年9月10日 下午3:43:22
     */
    BigDecimal getAfterSalesInstallstionAmount(Integer afterSalesId);
}
