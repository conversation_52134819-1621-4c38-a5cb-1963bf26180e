package com.vedeng.erp.trader.service.impl;

import com.report.dao.PublicCustomerCalculateMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.common.util.BitsetUtils;
import com.vedeng.customerbillperiod.dao.CustomerBillPeriodUseDetailMapper;
import com.vedeng.erp.trader.domain.PublicCustomerCalculateRules;
import com.vedeng.erp.trader.domain.PublicCustomerRecord;
import com.vedeng.erp.trader.domain.dto.TraderCustomerDto;
import com.vedeng.erp.trader.mapper.PublicCustomerCalculateRulesMapper;
import com.vedeng.erp.trader.mapper.PublicCustomerRecordMapper;
import com.vedeng.erp.trader.mapper.PublicTraderMapper;
import com.vedeng.erp.trader.service.PublicCustomerCalculateService;
import com.vedeng.order.model.dto.CustomerCountDto;
import com.vedeng.trader.model.TraderCustomer;
import com.vedeng.trader.model.dto.TraderCustomerAssociateInfoDto;
import io.swagger.models.auth.In;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.Arrays;
import java.util.BitSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: daniel
 * @Date: 2022/2/22 14 47
 * @Description:
 */
@Service
public class PublicCustomerCalculateServiceImpl implements PublicCustomerCalculateService {

    @Resource
    private PublicCustomerCalculateRulesMapper publicCustomerCalculateRulesMapper;

    @Resource
    private CustomerBillPeriodUseDetailMapper customerBillPeriodUseDetailMapper;

    @Resource
    private AfterSalesMapper afterSalesMapper;

    @Autowired
    private PublicCustomerCalculateMapper publicCustomerCalculateMapper;


    @Override
    public BitSet calculatePublicCustomer(BitSet bitSetOfBasePublicCustomer, Integer scenarios) {
        //检索最新一次编辑的公海客户计算规则
        PublicCustomerCalculateRules publicCustomerCalculateRules = publicCustomerCalculateRulesMapper
                .getRecentlyPublicCustomerCalculateRule();

        int maxCalculateRuleDays = Math.max(Math.max(publicCustomerCalculateRules.getCustomerCreatedDays(), publicCustomerCalculateRules.getValidOrderDays())
                ,publicCustomerCalculateRules.getCommunicationDays());
        int newProtectionDays = scenarios == 1 ? Math.max(maxCalculateRuleDays - 10, 10) : maxCalculateRuleDays;

        //1、排除新客保护
        bitSetOfBasePublicCustomer.andNot(getCustomerOfNewProtection(newProtectionDays));

        //2、非公海客户
        bitSetOfBasePublicCustomer.andNot(getUnPrivateCustomer());

        //3、非账期客户
        bitSetOfBasePublicCustomer.andNot(getCustomerOfUnreturnedBillPeriod());

        //4、非进行中的售后单客户
        bitSetOfBasePublicCustomer.andNot(getCustomerOfExistOnGoingAfterSales());

        //5、非商机解锁保护期的客户
//        int unlockedDays = scenarios == 1 ? 20 : 30;
//        bitSetOfBasePublicCustomer.andNot(getCustomerOfUnlockedByCreateBusinessChance(unlockedDays));

        //6、非撤销公海保护期客户
        int protectedDays = scenarios == 1 ? 10 : 0;
        bitSetOfBasePublicCustomer.andNot(getCustomerOfInProtectedDays(protectedDays));

        //7、排除不满足交易频次规则的客户
        int validOrderDays = scenarios == 1 ? Math.min(publicCustomerCalculateRules.getValidOrderDays(), publicCustomerCalculateRules.getValidOrderDays() - 10)
                : publicCustomerCalculateRules.getValidOrderDays();
        bitSetOfBasePublicCustomer.andNot(getInValidCustomerByValidOrderRule(validOrderDays,publicCustomerCalculateRules.getValidOrderCount()));

        //8、排除不满足沟通频次规则的客户
        int validCommunicationDays = scenarios == 1 ? Math.min(publicCustomerCalculateRules.getCommunicationDays(),publicCustomerCalculateRules.getCommunicationDays() - 10) :
                publicCustomerCalculateRules.getCommunicationDays();
        bitSetOfBasePublicCustomer.andNot(getInValidCustomerByValidCommunicationRule(validCommunicationDays,publicCustomerCalculateRules.getCommunicationCount()));
        
        //9、排除解锁保护期
        Integer lockProtectDays = publicCustomerCalculateRules.getLockProtectDays();
        bitSetOfBasePublicCustomer.andNot(getLockProtectedDaysRule(lockProtectDays));

        //同关联的客户，如果有未能预警的客户，则所有关联的客户都不能预警
        return excludeCustomerByAssociatedCustomerGroup(bitSetOfBasePublicCustomer);
    }

    /**
     * 解锁保护期
     * @param lockProtectDays
     * @return
     */
    private BitSet getLockProtectedDaysRule(Integer lockProtectDays) {
        List<Integer> lockProtectDayCustomerIds = publicCustomerCalculateMapper.getLockProtectDays(lockProtectDays);
        return BitsetUtils.collection2BitSet(lockProtectDayCustomerIds);
    }


    /**
     * 保护期的新客户集合
     * @param createdDaysOfRule 保护期
     * @return 保护期客户
     */
    private BitSet getCustomerOfNewProtection(Integer createdDaysOfRule){
        List<Integer> customerListOfNewProtection = publicCustomerCalculateMapper.getOldTraderCustomerByCondition(createdDaysOfRule)
                .stream()
                .map(TraderCustomerAssociateInfoDto::getTraderCustomerId)
                .distinct()
                .collect(Collectors.toList());
        return BitsetUtils.collection2BitSet(customerListOfNewProtection);
    }


    /**
     * 获取公海客户
     * @return 公海客户
     */
    private BitSet getUnPrivateCustomer(){
        List<Integer> unPrivateCustomerRecordIds = publicCustomerCalculateMapper.getUnPrivateCustomerRecords().stream()
                .distinct().collect(Collectors.toList());
        return BitsetUtils.collection2BitSet(unPrivateCustomerRecordIds);
    }


    /**
     * 获取未归还账期客户
     * @return 未归还账期客户
     */
    private BitSet getCustomerOfUnreturnedBillPeriod(){
        List<Integer> unReturnedBillPeriodCustomerIds = customerBillPeriodUseDetailMapper.getUnReturnedBillPeriodCustomerIds();
        return BitsetUtils.collection2BitSet(unReturnedBillPeriodCustomerIds);
    }


    /**
     * 获取存在进行中售后单的客户
     * @return 存在进行中售后单的客户集合
     */
    private BitSet getCustomerOfExistOnGoingAfterSales(){
        List<Integer> existOnGoingAfterSalesCustomerIds = afterSalesMapper.getExistOnGoingAfterSalesCustomerIds();
        return BitsetUtils.collection2BitSet(existOnGoingAfterSalesCustomerIds);
    }


    /**
     * 获取商机解锁保护期的客户
     * @param unlockedDays 商机解锁天数
     * @return 客户集合
     */
    private BitSet getCustomerOfUnlockedByCreateBusinessChance(Integer unlockedDays){
        List<Integer> customerOfUnlockedByCreateBusinessChance = publicCustomerCalculateMapper.getCustomerListUnlockedByBusinessChance(unlockedDays);
        return BitsetUtils.collection2BitSet(customerOfUnlockedByCreateBusinessChance);
    }


    /**
     * 获取保护期的客户集合
     * @param protectedDays 保护期，公海预警时，等于10；公海计算时，等于0
     * @return 保护期客户集合
     */
    private BitSet getCustomerOfInProtectedDays(Integer protectedDays){
        List<Integer> protectedCustomerIds = publicCustomerCalculateMapper.getCustomerIdsOfGrateThenProtectedDays(protectedDays);
        return BitsetUtils.collection2BitSet(protectedCustomerIds);
    }


    /**
     * 获取不满足有效订单计算规则的客户集合
     * @param validDays 有效天数
     * @param validOrderCount 有效订单数
     * @return 客户集合
     */
    private BitSet getInValidCustomerByValidOrderRule(Integer validDays, Integer validOrderCount) {
        //获取所有贝登客户在该时间段内的生效订单数
        Map<Integer, Integer> validOrderCountMap =
                publicCustomerCalculateMapper.getValidOrderCountOfB2bCustomer(validDays).stream().collect(Collectors.toMap(CustomerCountDto::getTraderCustomerId,
                        CustomerCountDto::getMyCount));
        Map<Long, String> associatedGroupCustomerMap =
                publicCustomerCalculateMapper.getCustomerListByAssociatedGroup().stream().collect(Collectors.toMap(TraderCustomer::getAssociatedCustomerGroup,
                        TraderCustomer::getComments));
        associatedGroupCustomerMap.keySet().forEach(key -> {
            int orderCountOfAssociateGroup = Arrays.stream(associatedGroupCustomerMap.get(key).split(","))
                    .map(customerId -> validOrderCountMap.getOrDefault(Integer.valueOf(customerId),0))
                    .reduce(0,Integer::sum);
            Arrays.stream(associatedGroupCustomerMap.get(key).split(","))
                    .forEach(customerId -> validOrderCountMap.put(Integer.valueOf(customerId),orderCountOfAssociateGroup));
        });
        List<Integer> invalidOrderCustomerList = validOrderCountMap.keySet()
                .stream()
                .filter(key -> validOrderCountMap.get(key) > validOrderCount)
                .collect(Collectors.toList());
        return BitsetUtils.collection2BitSet(invalidOrderCustomerList);
    }


    /**
     * 获取不满足沟通频次规则的客户集合
     * @param communicationDays 沟通时间
     * @param validCommunicationRecordCount 沟通次数
     * @return 客户集合
     */
    private BitSet getInValidCustomerByValidCommunicationRule(Integer communicationDays, Integer validCommunicationRecordCount){
        //获取所有贝登客户在该时间段内的有效沟通次数
        Map<Integer, Integer> validCommunicationCountMap =
                publicCustomerCalculateMapper.getValidCommunicationCountOfB2bCustomer(communicationDays).stream().collect(Collectors.toMap(CustomerCountDto::getTraderCustomerId,CustomerCountDto::getMyCount));
        Map<Long, String> associatedGroupCustomerMap =
                publicCustomerCalculateMapper.getCustomerListByAssociatedGroup().stream().collect(Collectors.toMap(TraderCustomer::getAssociatedCustomerGroup,
                        TraderCustomer::getComments));
        associatedGroupCustomerMap.keySet().forEach(key -> {
            int orderCountOfAssociateGroup = Arrays.stream(associatedGroupCustomerMap.get(key).split(","))
                    .map(customerId -> validCommunicationCountMap.getOrDefault(Integer.valueOf(customerId), 0))
                    .reduce(0,Integer::sum);
            Arrays.stream(associatedGroupCustomerMap.get(key).split(","))
                    .forEach(customerId -> validCommunicationCountMap.put(Integer.valueOf(customerId),orderCountOfAssociateGroup));
        });
        List<Integer> invalidCommunicationCustomerList = validCommunicationCountMap.keySet()
                .stream()
                .filter(key -> validCommunicationCountMap.get(key) > validCommunicationRecordCount)
                .collect(Collectors.toList());
        return BitsetUtils.collection2BitSet(invalidCommunicationCustomerList);
    }


    /**
     * 同关联的客户，如果有未能预警的客户，则所有关联的客户都不能预警
     * @param bitSetOfBasePublicCustomer 基础客户集合
     * @return 最终客户
     */
    private BitSet excludeCustomerByAssociatedCustomerGroup(BitSet bitSetOfBasePublicCustomer){
        publicCustomerCalculateMapper.getCustomerListByAssociatedGroup().forEach(item -> {
            List<Integer> customerIdListOfGroup = Arrays.stream(item.getComments().split(",")).map(Integer::valueOf).collect(Collectors.toList());
            boolean allEarlyWarning = true;
            for (Integer customerId : customerIdListOfGroup){
                if (!bitSetOfBasePublicCustomer.get(customerId)){
                    allEarlyWarning = false;
                    break;
                }
            }
            if (!allEarlyWarning){
                //如果同一个组的客户没有全部能够预警，则都不纳入预警
                bitSetOfBasePublicCustomer.andNot(BitsetUtils.collection2BitSet(customerIdListOfGroup));
            }
        });
        return bitSetOfBasePublicCustomer;
    }
}
