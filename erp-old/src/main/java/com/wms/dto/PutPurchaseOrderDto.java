package com.wms.dto;

import cn.hutool.core.util.StrUtil;

import java.util.List;

public class PutPurchaseOrderDto {
    private String warehouseId;

    private String docNo;

    private String poType;

    private String poCreationTime;

    private String expectedArriveTime1;

    private String expectedArriveTime2;

    private Integer supplierId;

    private String SupplierName;

    private String poReferenceA;

    private String PoReferenceB;

    private String poReferenceC;

    //原始销售单号
    private String hedi01;

    private String notes;

    private List<PutPurchaseOrderGoodsDto> details;

    public String getDocNo() {
        return docNo;
    }

    public void setDocNo(String docNo) {
        this.docNo = docNo;
    }

    public String getPoType() {
        return poType;
    }

    public void setPoType(String poType) {
        this.poType = poType;
    }

    public String getPoCreationTime() {
        return poCreationTime;
    }

    public void setPoCreationTime(String poCreationTime) {
        this.poCreationTime = poCreationTime;
    }

    public String getExpectedArriveTime1() {
        return expectedArriveTime1;
    }

    public void setExpectedArriveTime1(String expectedArriveTime1) {
        this.expectedArriveTime1 = expectedArriveTime1;
    }

    public String getExpectedArriveTime2() {
        return expectedArriveTime2;
    }

    public void setExpectedArriveTime2(String expectedArriveTime2) {
        this.expectedArriveTime2 = expectedArriveTime2;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierName() {
        return SupplierName;
    }

    public void setSupplierName(String supplierName) {
        SupplierName = supplierName;
    }

    public String getPoReferenceA() {
        return poReferenceA;
    }

    public void setPoReferenceA(String poReferenceA) {
        if (StrUtil.isNotEmpty(poReferenceA) && poReferenceA.length() > 20) {
            this.poReferenceA = StrUtil.sub(poReferenceA, 0, 20);
        } else {
            this.poReferenceA = poReferenceA;
        }
    }

    public String getPoReferenceB() {
        return PoReferenceB;
    }

    public void setPoReferenceB(String poReferenceB) {
        PoReferenceB = poReferenceB;
    }

    public String getPoReferenceC() {
        return poReferenceC;
    }

    public void setPoReferenceC(String poReferenceC) {
        this.poReferenceC = poReferenceC;
    }

    public List<PutPurchaseOrderGoodsDto> getDetails() {
        return details;
    }

    public void setDetails(List<PutPurchaseOrderGoodsDto> details) {
        this.details = details;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getHedi01() {
        return hedi01;
    }

    public void setHedi01(String hedi01) {
        this.hedi01 = hedi01;
    }

    public String getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(String warehouseId) {
        this.warehouseId = warehouseId;
    }
}
