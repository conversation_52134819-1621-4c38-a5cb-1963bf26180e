package com.vedeng.order.service.impl;

import com.common.dto.CanalMqBodyDTO;
import com.newtask.data.saleorderdata.SaleorderAfterSaleStatusSync;

import com.newtask.data.saleorder.SaleorderCurrentOrgAndUserIdSync;

import com.newtask.data.saleorderdata.*;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.order.service.SaleorderDataSyncService;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @Author: daniel
 * @Date: 2022/3/7 11 05
 * @Description:  此service不要依赖于其他service,仅限依赖mapper.主要目的是 确保不要相互依赖。
 */
@Service("saleorderDataSyncService")
public class SaleorderDataSyncServiceImpl implements SaleorderDataSyncService {
    Logger logger = LoggerFactory.getLogger(SaleorderDataSyncServiceImpl.class);
    @Autowired
    private AfterSalesMapper afterSalesMapper;

    @Autowired
    private SaleorderCurrentOrgAndUserIdSync saleorderCurrentOrgAndUserIdSync;

    @Autowired
    private SaleorderSubStatusSync saleorderSubStatusSync;

    @Autowired
    private SaleorderVerifyStatusSync saleorderVerifyStatusSync;

    @Autowired
    private SaleorderContractVerifyStatusSync saleorderContractVerifyStatusSync;

    @Autowired
    private SaleorderInvoiceApplyFlaySync saleorderInvoiceApplyFlaySync;

    @Autowired
    private SaleorderContractStatusSync saleorderContractStatusSync;

    @Autowired
    private SaleorderNameConcatInfoSync saleorderNameConcatInfoSync;

    @Autowired
    private SaleorderAfterSaleStatusSync saleorderAfterSaleStatusSync;

    @Autowired
    private SaleorderCommunicateNumSync saleorderCommunicateNumSync;

    @Autowired
    private SaleorderLeftAmountPeriodSync saleorderLeftAmountPeriodSync;

    @Override
    public void syncDataBySaleorderTable(CanalMqBodyDTO mqBodyDTO) {
        Arrays.stream(mqBodyDTO.getData()).forEach(data -> {
            JSONObject item = JSONObject.fromObject(data);
            Integer saleorderId = item.getInt("SALEORDER_ID");
            saleorderCurrentOrgAndUserIdSync.process(saleorderId);
            saleorderSubStatusSync.process(saleorderId);
         //   saleorderLeftAmountPeriodSync.process(saleorderId);
        });
    }

    @Override
    public void syncDataByVerifiesInfoTable(CanalMqBodyDTO mqBodyDTO) {
        Arrays.stream(mqBodyDTO.getData()).forEach(data -> {
            JSONObject item = JSONObject.fromObject(data);
            Integer relatedId = item.getInt("RELATE_TABLE_KEY");
            saleorderVerifyStatusSync.process(relatedId);
            saleorderContractStatusSync.process(relatedId);
            saleorderContractVerifyStatusSync.process(relatedId);
            saleorderSubStatusSync.process(relatedId);
        });
    }

    @Override
    public void syncDataByInvoiceApplyTable(CanalMqBodyDTO mqBodyDTO) {
        Arrays.stream(mqBodyDTO.getData()).forEach(data -> {
            JSONObject item = JSONObject.fromObject(data);
            Integer relatedId = item.getInt("RELATED_ID");
            saleorderInvoiceApplyFlaySync.process(relatedId);
            saleorderSubStatusSync.process(relatedId);
        });
    }

    @Override
    public void syncDataByCapitalBillDetailTable(CanalMqBodyDTO mqBodyDTO) {
        Arrays.stream(mqBodyDTO.getData()).forEach(data -> {
            JSONObject item = JSONObject.fromObject(data);
            int relatedId = item.getInt("RELATED_ID");
            int orderType = item.getInt("ORDER_TYPE");
            if (orderType == 3){
                //根据售后单查询对应的销售单
                relatedId = afterSalesMapper.getAfterSalesById(relatedId).getOrderId();
            }
            saleorderLeftAmountPeriodSync.process(relatedId);
        });
    }

    @Override
    public void syncDataByCommunicationRecordTable(CanalMqBodyDTO mqBodyDTO) {
        Arrays.stream(mqBodyDTO.getData()).forEach(data -> {
            JSONObject item = JSONObject.fromObject(data);
            if (item.getInt("COMMUNICATE_TYPE") == 246){
                saleorderCommunicateNumSync.process(item.getInt("RELATED_ID"));
            }
        });
    }

    @Override
    public void syncDataByAfterSalesTable(CanalMqBodyDTO mqBodyDTO) {
        Arrays.stream(mqBodyDTO.getData()).forEach(data -> {
            JSONObject item = JSONObject.fromObject(data);
            if (item.getInt("SUBJECT_TYPE") == 535) {
                saleorderAfterSaleStatusSync.process(item.getInt("ORDER_ID"));
            }
        });
    }

    /**'4' -- 待提交审核
     *'5' -- 未上传
     *  0待审核 1审核通过 2审核不通过
     *
     *
     * @param saleorderId
     * @param from
     */
    public void syncContractStatusBySaleorderId(Integer saleorderId,String from){
        try {
            logger.info("同步订单合同状态：：{} 来源：{}", saleorderId, from);
            List<Integer> ids = new ArrayList<>();
            ids.add(saleorderId);
            List<Map<String, Object>> saleorderList = saleorderContractVerifyStatusSync.loadBizData(ids);
            saleorderContractVerifyStatusSync.updateData(saleorderList);
        }catch (Exception e){
            logger.error("同步订单合同状态异常：：{} 来源：{}", saleorderId, from,e);
        }
    }
}
