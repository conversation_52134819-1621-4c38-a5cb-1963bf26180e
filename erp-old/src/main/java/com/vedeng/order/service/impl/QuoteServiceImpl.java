package com.vedeng.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.common.constants.Contant;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Preconditions;
import com.newtask.quoteorder.QuotedAlarmMonitor;
import com.newtask.quoteorder.model.QuotedAlarmRecord;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.authorization.dao.RoleMapper;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.Organization;
import com.vedeng.authorization.model.Region;
import com.vedeng.authorization.model.User;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.activiti.dto.ActivitiNoticeInfoEntity;
import com.vedeng.common.activiti.entity.ActivitiTaskUnDoEntity;
import com.vedeng.common.activiti.service.ActivitiNoticeService;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.http.HttpClientUtils;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.shiro.JedisUtils;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.erp.saleorder.dto.OrderTerminalDto;
import com.vedeng.erp.saleorder.enums.LowPriceOrderTypeEnum;
import com.vedeng.erp.saleorder.service.OrderGoodsLowerPriceApiService;
import com.vedeng.erp.saleorder.strategy.OrderTerminalContext;
import com.vedeng.erp.system.common.enums.ChangeRelatedTableEnums;
import com.vedeng.erp.system.service.ChangeLogApiService;
import com.vedeng.goods.dao.CoreSpuMapper;
import com.vedeng.goods.dao.GoodsMapper;
import com.vedeng.goods.dao.RCategoryJUserMapper;
import com.vedeng.goods.model.CoreSkuGenerate;
import com.vedeng.goods.model.CoreSpu;
import com.vedeng.goods.model.vo.SkuAuthorizationItemVo;
import com.vedeng.goods.model.vo.SkuAuthorizationVo;
import com.vedeng.goods.service.GoodsService;
import com.vedeng.goods.service.SkuAuthorizationService;
import com.vedeng.infrastructure.feign.uac.UacWxUserInfoApiService;
import com.vedeng.logistics.model.WarehouseStock;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.order.dao.*;
import com.vedeng.order.enums.QuotedAlarmModeEnum;
import com.vedeng.order.enums.QuotedConsultStateEnum;
import com.vedeng.order.model.*;
import com.vedeng.order.model.vo.AuthorizationApplyVo;
import com.vedeng.order.model.vo.ConsultGooodsVo;
import com.vedeng.order.model.vo.QuoteorderConsultContentVo;
import com.vedeng.order.model.vo.QuoteorderVo;
import com.vedeng.order.service.QuoteService;
import com.vedeng.order.strategy.QuoteOrderTerminalStrategy;
import com.vedeng.price.api.price.dto.price.PriceInfoResponseDto;
import com.vedeng.price.service.BasePriceService;
import com.vedeng.system.dao.SysOptionDefinitionMapper;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.service.OrgService;
import com.vedeng.system.service.RegionService;
import com.vedeng.system.service.UserService;
import com.vedeng.system.service.VerifiesRecordService;
import com.vedeng.trader.dao.CommunicateRecordMapper;
import com.vedeng.trader.model.CommunicateRecord;
import com.vedeng.uac.api.dto.BatchMessageSendDto;
import com.wms.model.po.WmsOutputOrder;
import net.sf.json.JSONObject;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisSentinelPool;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

@Service("quoteService")
public class QuoteServiceImpl extends BaseServiceimpl implements QuoteService{
	private static Logger log = LoggerFactory.getLogger(QuoteServiceImpl.class);

	public final int ROLE_ID_17=17;

	private final static Integer ON = 1;
	private final static Integer OFF = 0;

	@Autowired
	@Qualifier("communicateRecordMapper")
	private CommunicateRecordMapper communicateRecordMapper;

	@Autowired
	@Qualifier("rCategoryJUserMapper")
	private RCategoryJUserMapper rCategoryJUserMapper;

	@Autowired
	@Qualifier("userMapper")
	private UserMapper userMapper;

	@Autowired
	@Qualifier("quoteorderMapper")
	private QuoteorderMapper quoteorderMapper;

	@Autowired
	private QuoteorderConsultMapper quoteorderConsultMapper;

	@Autowired
	private QuoteorderConsultReplyMapper quoteorderConsultReplyMapper;

	@Autowired
	private QuoteorderConsultDetailMapper quoteorderConsultDetailMapper;

	@Autowired
	private AuthorizationMapper authorizationMapper;

	@Autowired
	private AuthorizationStorageMapper authorizationStorageMapper;
	@Resource
	private OrgService orgService;
	@Resource
	private UserService userService;
	@Resource(name = "jedisPool")
	private JedisSentinelPool jedisSentinelPool;

	@Autowired
	private SaleorderMapper saleorderMapper;

	@Autowired
	private SysOptionDefinitionMapper sysOptionDefinitionMapper;

	@Autowired
	private CoreSpuMapper coreSpuMapper;

	@Autowired
	private RoleMapper roleMapper;

	@Autowired
	private GoodsService goodsService;

	@Autowired
	private BasePriceService basePriceService;

	private final static int USER_ID_OF_KELLAY = 290;
	@Autowired
	private WarehouseStockService warehouseStockService;

	@Autowired
	private SkuAuthorizationService skuAuthorizationService;

	@Autowired
	private RegionService regionService;

	@Autowired
	private OrderGoodsLowerPriceApiService orderGoodsLowerPriceApiService;

	@Autowired
	private QuoteOrderTerminalStrategy quoteOrderTerminalStrategy;

	public static final String NOTICE_MESSAGE_TEMPLATE_FOR_ACTIVITI = "**授权书审批提醒**\n>    " +
			"授权书单号：**{applyNum}**\n " +
			"授权产品：{skuName}\n" +
			"审批到您已经30分钟了，请及时处理。  \n>    [查看详情]({jumpUrl})";

	@Value("${erp_url}")
	private String erp_url; //erp_url	http://erp.ivedeng.com/ 此为apollo里的值

	@Autowired
	private ActivitiNoticeService activitiNoticeService;

	@Autowired
	private UacWxUserInfoApiService uacWxUserInfoApiService ;

	@Override
	public Map<String, Object> getQuoteListPage(Quoteorder quoteorder, Page page) {
//		Map<String, Object> result_map = null;
		Map<String,Object> map = new HashMap<>();
		try {

			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<List<Quoteorder>>> TypeRef = new TypeReference<ResultInfo<List<Quoteorder>>>() {};
			String url = httpUrl + "order/quote/getquotelistpage.htm";

			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, quoteorder,clientId,clientKey, TypeRef,page);


			map.put("quoteList",(List<Quoteorder>)result.getData());

			map.put("page", result.getPage());
		} catch (IOException e) {
			log.error(Contant.ERROR_MSG, e);
		}
		return map;
	}

	@Override
	public Integer getCommunicateRecordCount(CommunicateRecord cr,Integer bussinessType,Integer quoteType) {
		return communicateRecordMapper.getCommunicateRecordCount(cr,bussinessType,quoteType);
	}

	@Override
	public List<CommunicateRecord> getCommunicateNumList(List<Integer> saleIdList, List<Integer> quoteIdList, List<Integer> businessIdList) {
		return communicateRecordMapper.getCommunicateNumList(saleIdList,quoteIdList,businessIdList);
	}

	@Override
	public List<Integer> getCommunicateRecordByDate(Long beginDate, Long endDate,String communicateType) {
		return communicateRecordMapper.getCommunicateRecordByDate(beginDate,endDate,communicateType);
	}

	@Override
	public ResultInfo<Quoteorder> saveQuote(Quoteorder quote) {
		ResultInfo<Quoteorder> result = null;

		CommunicateRecord cr = new CommunicateRecord();
		if(quote.getBussinessChanceId()!=null){
			cr.setBussinessChanceId(quote.getBussinessChanceId());
			cr.setQuoteorderId(null);
			//沟通类型ID_244商机,ID_245报价
			Integer count = communicateRecordMapper.getCommunicateRecordCount(cr,SysOptionConstant.ID_244,SysOptionConstant.ID_245);
			if(count>0){
				quote.setHaveCommunicate(1);//存在沟通记录
			}
		}

		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Quoteorder>> TypeRef = new TypeReference<ResultInfo<Quoteorder>>() {};
		String url=httpUrl + "order/quote/savequote.htm";
		try {
			result = (ResultInfo<Quoteorder>) HttpClientUtils.post(url, quote,clientId,clientKey, TypeRef);
		} catch (IOException e) {
			// TODO Auto-generated catch block
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}

	@Override
	public Quoteorder getQuoteInfoByKey(Integer quoteorderId) {
		Quoteorder quote = null;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Quoteorder>> TypeRef = new TypeReference<ResultInfo<Quoteorder>>() {};
		String url=httpUrl + "order/quote/getquoteinfobykey.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, quoteorderId,clientId,clientKey, TypeRef);
			quote = (Quoteorder) result.getData();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			logger.error(Contant.ERROR_MSG, e);
		}
		return quote;
	}

	@Override
	public Quoteorder getQuoteOrderInfoById(Integer quoteorderId) {

		return quoteorderMapper.getQuoteInfoByKey(quoteorderId);
	}


	@Override
	public Quoteorder relateCloseQuote(Integer quoteorderId, String optionType) {
		Quoteorder quoteInfo = quoteorderMapper.getQuoteInfoByKey(quoteorderId);
		if (quoteInfo != null && quoteInfo.getQuoteorderId() != null) {
			Integer closeReason = getSysOptionDefIdBypt(1600,"SYS_AUTO_CLOSE_TYPE_2");
			String closeComment = "订单关闭后，自动关闭报价单";
			quoteorderMapper.relateCloseQuoteOrderById(quoteInfo.getQuoteorderId(),closeReason,closeComment);
		}
		return quoteInfo;
	}

	@Override
	public ResultInfo<?> updateQuoteCustomer(Quoteorder quote) {
		ResultInfo<?> result = null;

		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Quoteorder>> TypeRef = new TypeReference<ResultInfo<Quoteorder>>() {};
		String url=httpUrl + "order/quote/updatequotecustomer.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, quote,clientId,clientKey, TypeRef);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}

	@Override
	public ResultInfo<?> updateQuoteTerminal(Quoteorder quote) {
		ResultInfo<?> result = null;

		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Quoteorder>> TypeRef = new TypeReference<ResultInfo<Quoteorder>>() {};
		String url=httpUrl + "order/quote/updatequoteterminal.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, quote,clientId,clientKey, TypeRef);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}
	@Autowired
	private ChangeLogApiService changeLogApiService;
	@Override
	public ResultInfo<?> saveQuoteGoods(QuoteorderGoods quoteGoods,Attachment ach) {
		ResultInfo<?> result = null;

		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
		String url=httpUrl + "order/quote/savequotegoods.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, quoteGoods,clientId,clientKey, TypeRef);
			if(result.getCode()!=-1){
				if(result.getData()!=null && ach!=null && StringUtils.isNotBlank(ach.getUri()) && StringUtils.isNotBlank(ach.getName())){
					//保存附近记录信息
					quoteGoods = (QuoteorderGoods)JSONObject.toBean(JSONObject.fromObject(result.getData()), QuoteorderGoods.class);
					ach.setRelatedId(quoteGoods.getQuoteorderGoodsId());
					ach.setAttachmentType(SysOptionConstant.ID_343);
					ach.setAttachmentFunction(SysOptionConstant.ID_494);
					// 定义反序列化 数据格式
					final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {};
					String url2 = httpUrl + "attachment/saveattachment.htm";
					result = (ResultInfo<?>) HttpClientUtils.post(url2, ach,clientId,clientKey, TypeRef2);
				}
			}
			//VDERP-13616 【ERP】客户下单流程优化 - 报价单
			ChangeRelatedTableEnums tQuoteorderGoods = ChangeRelatedTableEnums.T_QUOTEORDER;
			tQuoteorderGoods.setRelatedId(quoteGoods.getQuoteorderId());
//			changeLogApiService.saveLog(tQuoteorderGoods,Collections.singletonList(quoteGoods),null,ach.getCreator());
			saveChangeLog(tQuoteorderGoods,quoteGoods,null,ach.getCreator());

			this.handleOrderTerminal(quoteGoods);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}



	@Override
	public Map<String,Object> getQuoteGoodsByQuoteId(Integer quoteorderId,Integer companyId,HttpSession hs,Integer viewType,Integer traderId) {
		Map<String,Object> map = new HashMap<>();
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<List<QuoteorderGoods>>> TypeRef = new TypeReference<ResultInfo<List<QuoteorderGoods>>>() {};
		String url=httpUrl + "order/quote/getquotegoodsbyquoteid.htm";
		try {
			Map<String,Integer> map_parm = new HashMap<String,Integer>();
			if(viewType != null){
				map_parm.put("viewType", viewType);
			}
			map_parm.put("quoteorderId", quoteorderId);map_parm.put("companyId", companyId);map_parm.put("traderId", traderId);
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, map_parm,clientId,clientKey, TypeRef);
			List<QuoteorderGoods> quoteGoodsList = (List<QuoteorderGoods>) result.getData();
			if(quoteGoodsList != null){
				List<Integer> userIdList = new ArrayList<>();
				Integer usercompanyId = 1;
				if (hs != null){
					CurrentUser currentUser = CurrentUser.getCurrentUser();
					usercompanyId = currentUser.getCompanyId();
				}
				//产品负责人
				for(int i=0;i<quoteGoodsList.size();i++){
					userIdList.add(quoteGoodsList.get(i).getLastReferenceUser());
					quoteGoodsList.get(i).setGoodsUserNm(rCategoryJUserMapper.getUserByCategoryNm(quoteGoodsList.get(i).getGoods().getCategoryId(),usercompanyId));
				}
				map.put("quoteGoodsList", quoteGoodsList);

				userIdList = new ArrayList<Integer>(new HashSet<Integer>(userIdList));
				if(userIdList!=null && userIdList.size() > 0){
					List<User> userList = userMapper.getUserByUserIds(userIdList);
					map.put("userList", userList);//申请人，审核人
				}
			}
		} catch (IOException e) {
			// TODO Auto-generated catch block
			logger.error(Contant.ERROR_MSG, e);
		}
		return map;
	}

	@Override
	public QuoteorderGoods getQuoteGoodsById(Integer quoteGoodsId,HttpSession httpSession) {
		QuoteorderGoods new_quoteGoods = null;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<QuoteorderGoods>> TypeRef = new TypeReference<ResultInfo<QuoteorderGoods>>() {};
		String url=httpUrl + "order/quote/getquotegoodsbyid.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, quoteGoodsId,clientId,clientKey, TypeRef);
			new_quoteGoods = (QuoteorderGoods) result.getData();

			User user = (User)httpSession.getAttribute(Consts.SESSION_USER);
			new_quoteGoods.setGoodsUserNm(rCategoryJUserMapper.getUserByCategoryNm(new_quoteGoods.getCategoryId(), user.getCompanyId()));
		} catch (Exception e) {
			// TODO Auto-generated catch block
			logger.error(Contant.ERROR_MSG, e);
		}
		return new_quoteGoods;
	}

	@Override
	public ResultInfo<?> editQuoteGoods(QuoteorderGoods quoteGoods,Attachment ach) {
		ResultInfo<?> result = null;
		QuoteorderGoods oldquoteGoods = quoteGoodsMapper.selectQuoteGoodsId(quoteGoods.getQuoteorderGoodsId());
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
		String url=httpUrl + "order/quote/editquotegoods.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, quoteGoods,clientId,clientKey, TypeRef);
			if(quoteGoods!=null && ach!=null && StringUtils.isNotBlank(ach.getAttachmentId()+"")){
				//保存附近记录信息
				ach.setRelatedId(quoteGoods.getQuoteorderGoodsId());
				ach.setAttachmentType(SysOptionConstant.ID_343);
				ach.setAttachmentFunction(SysOptionConstant.ID_494);
				// 定义反序列化 数据格式
				final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {};
				String url2 = httpUrl + "attachment/saveattachment.htm";
				result = (ResultInfo<?>) HttpClientUtils.post(url2, ach,clientId,clientKey, TypeRef2);
			}
			//VDERP-13616 【ERP】客户下单流程优化 - 报价单
			ChangeRelatedTableEnums tQuoteorderGoods = ChangeRelatedTableEnums.T_QUOTEORDER;
			tQuoteorderGoods.setRelatedId(oldquoteGoods.getQuoteorderId());
//			changeLogApiService.saveLog(tQuoteorderGoods,Collections.singletonList(quoteGoods),Collections.singletonList(oldquoteGoods),quoteGoods.getUpdater());
			saveChangeLog(tQuoteorderGoods,quoteGoods,oldquoteGoods,quoteGoods.getUpdater());

			this.handleOrderTerminal(quoteGoods);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}

	@Override
	public ResultInfo<?> delQuoteGoodsById(QuoteorderGoods quoteGoods) {
		ResultInfo<?> result = null;
		QuoteorderGoods oldquoteGoods = quoteGoodsMapper.selectQuoteGoodsId(quoteGoods.getQuoteorderGoodsId());
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
		String url=httpUrl + "order/quote/delquotegoodsbyid.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, quoteGoods,clientId,clientKey, TypeRef);
			if (result != null && result.getCode() != null && result.getCode() == 0){
				QuoteorderGoods quoteorderGoods = quoteorderMapper.getQuoteorderGoodsByQuoteorderGoodsId(quoteGoods.getQuoteorderGoodsId());
				//同步报价单的咨询状态，更新被删除sku的咨询状态为0
				quoteorderConsultReplyMapper.updateAllConsultStatusByQuoteorderIdAndSkuList(quoteGoods.getQuoteorderId(), Collections.singletonList(quoteorderGoods.getSku()),0);
				updateConsultStatusOfQuoteorder(quoteGoods.getQuoteorderId(),1);
			}
			//VDERP-13616 【ERP】客户下单流程优化 - 报价单
			ChangeRelatedTableEnums tQuoteorderGoods = ChangeRelatedTableEnums.T_QUOTEORDER;
			tQuoteorderGoods.setRelatedId(quoteGoods.getQuoteorderId());
//			changeLogApiService.saveLog(tQuoteorderGoods,null,Collections.singletonList(oldquoteGoods),quoteGoods.getUpdater());
			saveChangeLog(tQuoteorderGoods,null,oldquoteGoods,quoteGoods.getUpdater());

			this.handleOrderTerminal(quoteGoods);

		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}

	/**
	 * VDERP-15595 处理终端信息
	 *
	 * @param quoteGoods 报价商品
	 */
	private void handleOrderTerminal(QuoteorderGoods quoteGoods) {
		OrderTerminalDto orderTerminalDto = new OrderTerminalDto();
		orderTerminalDto.setBusinessId(quoteGoods.getQuoteorderId());
		orderTerminalDto.setTerminalName(quoteGoods.getTerminalTraderName());
		orderTerminalDto.setBusinessType(2);
		orderTerminalDto.setDwhTerminalId(quoteGoods.getDwhTerminalId());
		orderTerminalDto.setUnifiedSocialCreditIdentifier(quoteGoods.getUnifiedSocialCreditIdentifier());
		orderTerminalDto.setOrganizationCode(quoteGoods.getOrganizationCode());
		orderTerminalDto.setProvinceId(quoteGoods.getProvinceId());
		orderTerminalDto.setProvinceName(quoteGoods.getProvinceName());
		orderTerminalDto.setCityId(quoteGoods.getCityId());
		orderTerminalDto.setCityName(quoteGoods.getCityName());
		orderTerminalDto.setAreaId(quoteGoods.getAreaId());
		orderTerminalDto.setAreaName(quoteGoods.getAreaName());
		OrderTerminalContext orderTerminalContext = new OrderTerminalContext();
		orderTerminalContext.setOrderTerminalStrategy(quoteOrderTerminalStrategy);
		orderTerminalContext.executeStrategy(orderTerminalDto);
	}


	@Override
	public ResultInfo<?> editQuoteAmount(Quoteorder quote) {
		ResultInfo<?> result = null;

		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
		String url=httpUrl + "order/quote/editquoteamount.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, quote,clientId,clientKey, TypeRef);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}

	@Override
	public List<CommunicateRecord> getQuoteCommunicateListPage(String relatedIds, String communicateTypes,Page page) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("relatedIds", relatedIds);
		map.put("communicateTypes", communicateTypes);
		map.put("page", page);
		return communicateRecordMapper.getQuoteCommunicateListPage(map);
	}

	@Override
	public Map<Integer, String> getCommnuicateTraderTag(List<Integer> commnuicateIdList) {
		Map<Integer, String> tagMap = new HashMap<Integer, String>();
			try {
				// 定义反序列化 数据格式
				final TypeReference<ResultInfo<List<Map<String,String>>>> TypeRef = new TypeReference<ResultInfo<List<Map<String,String>>>>() {};
				String url = httpUrl + "order/quote/getcommnuicatetradertag.htm";

				ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, commnuicateIdList,clientId,clientKey, TypeRef);


				List<Map<String,String>> list = (List<Map<String, String>>) result.getData();

				if(list!=null){
					for (Map<String, String> stringStringMap : list) {
						tagMap.put(Integer.valueOf(stringStringMap.get("key")), stringStringMap.get("value"));
					}
				}
			} catch (IOException e) {
				logger.error(Contant.ERROR_MSG, e);
			}
		return tagMap;
	}

	@Override
	public ResultInfo<?> editQuoteValIdSave(Quoteorder quote) {
		ResultInfo<?> result = null;

		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
		String url=httpUrl + "order/quote/editquotevalidsave.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, quote,clientId,clientKey, TypeRef);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}

	@Autowired
	@Qualifier("goodsMapper")
	private GoodsMapper goodsMapper;
	@Override
	public ResultInfo<?> addQuoteConsultSave(QuoteorderConsult quoteConsult,User user) {
		Preconditions.checkArgument(quoteConsult.getQuoteorderId() != null, "保价单对应DB主键不能为空");
		Preconditions.checkArgument(StringUtils.isNotEmpty(quoteConsult.getQuoteorderNo()), "保价单单号不能为空");

		Quoteorder quoteOrderQuery = quoteorderMapper.getQuoteorderById(quoteConsult.getQuoteorderId());
		if (quoteOrderQuery == null) {
			logger.info("保存询价记录时未查询到保价单 - quoteOrderId:{}", quoteConsult.getQuoteorderId());
			return new ResultInfo<>();
		}
		//是否为首次发送,如果时之后让记录信息保存起来
		boolean triggerFlag = false;
		if (!Objects.equals(quoteOrderQuery.getIsSend(), ON)) {
			if (!Objects.equals(quoteOrderQuery.getConsultStatus(), QuotedConsultStateEnum.SUB_DONE_AND_SUP_NOT_DONE.getState())) {
				triggerFlag = true;
			}
		}

		ResultInfo<?> result = null;

		//分类负责人列表
		List<Integer> productMgrIdList=null;

		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<List<Integer>>> TypeRef = new TypeReference<ResultInfo<List<Integer>>>() {};
		String url=httpUrl + "order/quote/addquoteconsultsave.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, quoteConsult,clientId,clientKey, TypeRef);
			if(result != null){
				if(result.getCode() == 0 && result.getData() != null){
					List<Integer> goodsIds = (List<Integer>)result.getData();
					if(CollectionUtils.isNotEmpty(goodsIds)) {
						//分类负责人列表
						List<Integer> userIdList = goodsMapper.getAssignUserIdsByGoods(goodsIds);
						//发送消息
                        if(CollectionUtils.isNotEmpty(userIdList)) {
                        	productMgrIdList=userIdList;
							Map<String, String> map = new HashMap<>();
							map.put("quoteorderNo", quoteConsult.getQuoteorderNo());
							MessageUtil.sendMessage(31, userIdList, map, "./order/quote/getQuoteDetail.do?quoteorderId=" + quoteConsult.getQuoteorderId() + "&viewType=5");
						}
					}
				}
			}
		} catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
			return new ResultInfo<>(-1, "保存询价记录信息失败");
		}

		return result;
	}

	@Override
	public List<QuoteorderConsult> getQuoteConsultList(Integer quoteorderId) {
		List<QuoteorderConsult> list = null;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<List<QuoteorderConsult>>> TypeRef = new TypeReference<ResultInfo<List<QuoteorderConsult>>>() {};
		String url=httpUrl + "order/quote/getquoteconsultlist.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, quoteorderId,clientId,clientKey, TypeRef);
			list = (List<QuoteorderConsult>) result.getData();

		} catch (IOException e) {
			// TODO Auto-generated catch block
			logger.error(Contant.ERROR_MSG, e);
		}
		return list;
	}

	@Override
	public List<QuoteorderGoods> getQuoteOrderGoodsConsultList(Integer quoteorderId){
		return quoteorderMapper.getQuoteorderConsultById(quoteorderId);
	}

	@Override
	public List<Integer> findByQuoteorderIdGetUserId(Integer quoteorderId) {
		return quoteorderMapper.findByQuoteorderIdGetUserId(quoteorderId);
	}


	@Override
	public ResultInfo<?> editQuoteHaveCommunicate(Quoteorder quote) {
		ResultInfo<?> result = null;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
		String url=httpUrl + "order/quote/editquotehavecommunicate.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, quote,clientId,clientKey, TypeRef);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}

	@Override
	public ResultInfo<?> editLoseOrderStatus(Quoteorder quote) {
		ResultInfo<?> result = null;

		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
		String url=httpUrl + "order/quote/editloseorderstatus.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, quote,clientId,clientKey, TypeRef);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}

	@Override
	public Map<String, Object> getQuoteConsultListPage(QuoteorderConsult quoteConsult, Page page,HttpSession session) {
		Map<String, Object> result_map = null;Map<String,Object> map = new HashMap<>();
		User user = (User) session.getAttribute(ErpConst.CURR_USER);
		try {
//			if(quoteConsult.getGoods() != null && quoteConsult.getGoods().getGoodsUserId() != null) {
//				List<Integer> categoryIdList = rCategoryJUserMapper.getCategoryIdsByUserId(quoteConsult.getGoods().getGoodsUserId());
//				if(categoryIdList.isEmpty()) {
//					categoryIdList.add(-1);
//				}
//				quoteConsult.setCategoryIdList(categoryIdList);
//			}
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<Map<String, Object>>> TypeRef = new TypeReference<ResultInfo<Map<String, Object>>>() {};
			String url = httpUrl + "order/quote/getquoteconsultlistpage.htm";

			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, quoteConsult,clientId,clientKey, TypeRef, page);
			if(result != null && result.getCode() == 0 && result.getData() != null){
				result_map = (Map<String, Object>) result.getData();
				page = result.getPage();
				net.sf.json.JSONArray json = null;

				//报价咨询记录
				String quoteStr = result_map.get("quoteConsultList").toString();
				json = net.sf.json.JSONArray.fromObject(quoteStr==null?"":quoteStr);
				List<QuoteorderConsult> quoteConsultList = (List<QuoteorderConsult>) json.toCollection(json, QuoteorderConsult.class);

				//产品分类记录
				String cateoryListStr = (result_map.get("cateoryList")==null?"":result_map.get("cateoryList")).toString();
				if(StringUtils.isNotBlank(cateoryListStr)){
					json = net.sf.json.JSONArray.fromObject(cateoryListStr==null?"":cateoryListStr);
					List<QuoteorderConsult> cateoryList = (List<QuoteorderConsult>) json.toCollection(json, QuoteorderConsult.class);

/*					List<Integer> cateoryIdList = new ArrayList<>();
					for(int i=0;i<cateoryList.size();i++){
						cateoryIdList.add(cateoryList.get(i).getCategoryId());
					}
					cateoryIdList = new ArrayList<Integer>(new HashSet<Integer>(cateoryIdList));
					if(cateoryIdList!=null && cateoryIdList.size() > 0){
						List<User> cateoryUserList = rCategoryJUserMapper.getTypeUserByCategoryIds(userIdList, user.getCompanyId(),2);
						map.put("cateoryUserList", cateoryUserList);
					}*/


					if(null != quoteConsultList && quoteConsultList.size() > 0 && cateoryList != null){
//						String goodsUserNm = "";
						List<User> goodsUserList = null;
						for(QuoteorderConsult consult : quoteConsultList){
							if(null != consult.getCategoryId()){
								goodsUserList = new ArrayList<>();
								for(int i=0;i<cateoryList.size();i++){
									if(consult.getQuoteorderId().intValue() == cateoryList.get(i).getQuoteorderId().intValue()){
										goodsUserList.addAll((rCategoryJUserMapper.getUserByCategory(cateoryList.get(i).getCategoryId(), user.getCompanyId())));
										/*if(StringUtils.isNotBlank(str)){
											if(goodsUserNm.indexOf(str) < 0){
												goodsUserNm = (goodsUserNm.equals("")?"":(goodsUserNm + "、")) + str;
											}
										}*/
									}
								}

								Set<User> personSet = new TreeSet<>((o1, o2) -> o1.getUserId().compareTo(o2.getUserId()));
						        personSet.addAll(goodsUserList);

								consult.setUserList(new ArrayList<>(personSet));
//								consult.setGoodsUserNm(goodsUserNm);goodsUserNm = "";
							}
						}

					}
				}

				map.put("quoteConsultList", quoteConsultList);

				String quoteUserStr = result_map.get("quoteConsultUserList").toString();
				json = net.sf.json.JSONArray.fromObject(quoteUserStr==null?"":quoteUserStr);
				List<Integer> quoteConsultUserList = (List<Integer>) json.toCollection(json, Integer.class);
				map.put("quoteConsultUserList", quoteConsultUserList);

				map.put("page", page);
			}

		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return map;
	}

	@Override
	public ResultInfo<?> saveReplyQuoteConsult(QuoteorderConsult quoteConsult) {
		ResultInfo<?> result = null;

		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Quoteorder>> TypeRef = new TypeReference<ResultInfo<Quoteorder>>() {};
		String url=httpUrl + "order/quote/savereplyquoteconsult.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, quoteConsult,clientId,clientKey, TypeRef);
			if(result != null){
				if(result.getCode() == 0 && result.getData() != null){
					Quoteorder quote = (Quoteorder)result.getData();
					if(quote != null && quote.getTraderId()!=null){
						//根据客户Id查询客户负责人
						List<Integer> userIdList = userMapper.getUserIdListByTraderId(quote.getTraderId(),ErpConst.ONE);
						//咨詢保存完成，發送消息給產品負責人
						MessageUtil.sendMessage(32, userIdList, new HashMap<String, String>() {{put("quoteorderNo", quote.getQuoteorderNo());}}, "./order/quote/getQuoteDetail.do?quoteorderId="+quoteConsult.getQuoteorderId()+"&viewType=2");
					}
				}
			}
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}

	@Override
	public ResultInfo<?> editConsultStatus(Quoteorder quote) {
		ResultInfo<?> result = null;

		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
		String url=httpUrl + "order/quote/editconsultstatus.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, quote,clientId,clientKey, TypeRef);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}

	@Override
	public Attachment getQuoteGoodsAttachment(Attachment ach) {
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Attachment>> TypeRef = new TypeReference<ResultInfo<Attachment>>() {};
		String url=httpUrl + "attachment/getattachment.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, ach,clientId,clientKey, TypeRef);
			ach = (Attachment) result.getData();

		} catch (IOException e) {
			// TODO Auto-generated catch block
			logger.error(Contant.ERROR_MSG, e);
		}
		return ach;
	}

	@Override
	public Integer getQuoteListCount(Quoteorder quote) {
		Integer num = 0;

		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Integer>> TypeRef = new TypeReference<ResultInfo<Integer>>() {};
		String url=httpUrl + "order/quote/getquotelistcount.htm";
		try {
			ResultInfo<Integer> result = (ResultInfo<Integer>) HttpClientUtils.post(url, quote,clientId,clientKey, TypeRef);
			if(result != null && result.getCode() == 0){
				num = result.getData();
			}
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return num;
	}

	@Override
	public List<Quoteorder> getQuoteListSize(Quoteorder quote, Integer startSize, Integer endSize) {
		List<Quoteorder> list = null;
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("quote", quote);
		map.put("startSize", startSize);
		map.put("endSize", endSize);
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<List<Quoteorder>>> TypeRef = new TypeReference<ResultInfo<List<Quoteorder>>>() {};
		String url=httpUrl + "order/quote/getquotelistsize.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, map,clientId,clientKey, TypeRef);
			if(result!=null && result.getCode()==0){
				list = (List<Quoteorder>) result.getData();
			}
		} catch (IOException e) {
			// TODO Auto-generated catch block
			logger.error(Contant.ERROR_MSG, e);
		}
		return list;
	}

	@Override
	public ResultInfo<?> getQuoteGoodsPriceAndCycle(Quoteorder quote) {
		ResultInfo<?> result = null;

		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
		String url=httpUrl + "order/quote/getquotegoodspriceandcycle.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, quote,clientId,clientKey, TypeRef);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}

	@Override
	public ResultInfo<?> getTraderCustomerStatus(Integer traderCustomerId) {
		ResultInfo<?> result = null;

		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
		String url=httpUrl + "order/quote/gettradercustomerstatus.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, traderCustomerId,clientId,clientKey, TypeRef);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		if(result!=null){
			return result;
		}else{
			return new ResultInfo<>();
		}
	}

	@Override
	public ResultInfo<?> vailQuoteGoodsRepeat(QuoteorderGoods quoteGoods) {

		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
		String url=httpUrl + "order/quote/vailquotegoodsrepeat.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, quoteGoods,clientId,clientKey, TypeRef);
			if(result == null) {
				return new ResultInfo<>();
			}
			return result;
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return new ResultInfo<>();
		}
	}

	@Override
	public ResultInfo<?> isvalidQuoteOrder(Quoteorder quote) {
		ResultInfo<?> result = null;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
		String url=httpUrl + "order/quote/isvalidquoteorder.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, quote,clientId,clientKey, TypeRef);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}

	@Override
	public QuoteorderVo getPrintInfo(Quoteorder quote) {
		return quoteorderMapper.getPrintInfo(quote);
	}

	@Override
	public Quoteorder getQuoteorderForSync(Integer quoteorderId) {
		Quoteorder quote = null;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Quoteorder>> TypeRef = new TypeReference<ResultInfo<Quoteorder>>() {};
		String url=httpUrl + "order/quote/getquoteorderforsync.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, quoteorderId,clientId,clientKey, TypeRef);
			quote = (Quoteorder) result.getData();
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return quote;
	}

	@Override
	public Quoteorder getMessageInfoForSync(Integer orderId) {
		Quoteorder quote = null;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Quoteorder>> TypeRef = new TypeReference<ResultInfo<Quoteorder>>() {};
		String url=httpUrl + "order/quote/getmessageinfoforsync.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, orderId,clientId,clientKey, TypeRef);
			quote = (Quoteorder) result.getData();
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return quote;
	}

	@Override
	public List<User> getGoodsCategoryUserList(List<Integer> categoryIdList,Integer companyId) {
		try {
			return rCategoryJUserMapper.getGoodsCategoryUserList(categoryIdList, companyId);
		} catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}

	public ResultInfo<?> editQuoteOrderGoods(Quoteorder quote){
	    	ResultInfo<?> result = null;

		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
		String url=httpUrl + "order/quote/editquotegoodslist.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, quote,clientId,clientKey, TypeRef);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}

	@Override
	public void sendAllocation(String mobile,Integer userId) {
		try {
			List<User> userList = userMapper.getRoleUserId(ROLE_ID_17);
			List<Integer> userListId = new ArrayList<>();
			Map<String, String> map = new HashMap();
			map.put("mobile", mobile);
			for (User user : userList) {
				userListId.add(user.getUserId());
			}
			//	MessageUtil.sendErpMessage(94,userListId,"./erp/aftersales/webaccount/list.do?="+mobile);
			MessageUtil.sendMessage2(94, userListId, map, "./aftersales/webaccount/list.do?mobile=" + mobile);
		}catch (Exception e){
			log.error("sendAllocation",e);
		}

	}

	@Override
	public void getIsDisabled(String mobile, Integer userId,String saleorderNo,Integer saleorderId) {
		try {
			User user = userMapper.getIsDisabled(userId);
			List<User> userList = userMapper.getRoleUserId(ROLE_ID_17);
			List<Integer> userd=new ArrayList<>();
			userd.add(userId);
			List<Integer> userListId = new ArrayList<>();
			Map<String, String> map = new HashMap();
			Map<String, String> map2 = new HashMap();
			map.put("mobile", mobile);
			map2.put("saleorderNo",saleorderNo);
			for (User user1 : userList) {
				userListId.add(user1.getUserId());
			}
			if(null!=user){
				if (user.getIsDisabled() == 1) {
					MessageUtil.sendMessage2(95, userListId, map, "./aftersales/webaccount/list.do?mobile=" + mobile);
				}
				if(user.getIsDisabled() == 0){
					//VDERP-7899 BD订单库存不足时在新订单消息中提醒销售
					//库存不足
					Boolean canUserflag = Boolean.FALSE;
					//多个不足
					Boolean canUserflagMore = Boolean.FALSE;
					int i = 1;

					List<SaleorderGoods> saleorderGoodsById = saleorderMapper.getSaleorderGoodsById(saleorderId);
					if (CollectionUtils.isNotEmpty(saleorderGoodsById)){
						List<String> skus = saleorderGoodsById.stream().map(SaleorderGoods::getSku).collect(Collectors.toList());
						Map<String, WarehouseStock> map1 = warehouseStockService.getStockInfo(skus);
						//查个库存
						if (map1 != null) {
							for (SaleorderGoods saleorderGoods : saleorderGoodsById) {
								WarehouseStock w = map1.get(saleorderGoods.getSku());
								if(w.getAvailableStockNum().compareTo(saleorderGoods.getNum()) == -1) {
									if (i == 1){
										canUserflag = Boolean.TRUE;
										map2.put("skuAndName",saleorderGoods.getSku() + saleorderGoods.getGoodsName());
									}
									if (i > 1){
										canUserflagMore = Boolean.TRUE;
										break;
									}
									i++;
								}
							}
						}
					}

					if (canUserflag){
						if (canUserflagMore){
							MessageUtil.sendMessage2(196,userd,map2,"./order/saleorder/view.do?saleorderId="+saleorderId);
						}else {
							MessageUtil.sendMessage2(195,userd,map2,"./order/saleorder/view.do?saleorderId="+saleorderId);
						}
					}else{
					MessageUtil.sendMessage2(96,userd,map2,"./order/saleorder/view.do?saleorderId="+saleorderId);
					}
				}
			}
		}catch (Exception e){
			log.error("getIsDisabled",e);
		}
		}

	@Override
	public void updateQuote(Quoteorder quoteorder) {
		quoteorderMapper.updateQuote(quoteorder);
	}

	@Override
	public Integer isExistBussinessChanceId(int bussinessChanceId) {
		return quoteorderMapper.isExistBussinessChanceId(bussinessChanceId);
	}


	@Override
	public int getAuthorizationSum(Integer quoteorderId,Integer applyStatus) {
		return authorizationMapper.getAuthorizationSum(quoteorderId,applyStatus);
	}

	@Override
	public AuthorizationStorage getAuthorizationStorageInfoByQuoteOrderId(Integer quoteorderId) {
		//判断该授权书申请下有无保存的授权书申请
		return authorizationStorageMapper.getAuthorizationStorageInfoByQuoteOrderId(quoteorderId);
	}
	@Override
	public List<AuthorizationStorage> getAuthorizationStorageInfoListByQuoteOrderId(Integer quoteorderId){
		return authorizationStorageMapper.getAuthorizationStorageInfoListByQuoteOrderId(quoteorderId);
	}

	@Override
	public void insertAuthorizationStorageInfo(AuthorizationStorage authorizationStorage) {
		authorizationStorageMapper.insertAuthorizationStorageInfo(authorizationStorage);
	}

	@Override
	public void updateAuthorizationStorageInfo(AuthorizationStorage authorizationStorage) {
		authorizationStorageMapper.updateAuthorizationStorageInfo(authorizationStorage);
	}

	@Override
	public AuthorizationApply getAuthorizationApplyInfoBySkuIdAndQuoteId(Integer quoteorderId, Integer skuId, Integer authorizationApplyId) {
		return authorizationMapper.getAuthorizationApplyInfoBySkuIdAndQuoteId(quoteorderId,skuId,authorizationApplyId);
	}

	@Override
	public List<String> getSqNumByYearAndMonth(String yearAndMonth) {
		return authorizationMapper.getSqNumByYearAndMonth(yearAndMonth);
	}

	@Override
	public void insertAuthorizationApplyInfo(AuthorizationApply authorizationApply) {
		authorizationMapper.insertAuthorizationApplyInfo(authorizationApply);
	}

	@Override
	public void updateAuthorizationApplyInfo(AuthorizationApply authorizationApply) {
		authorizationMapper.updateAuthorizationApplyInfo(authorizationApply);
	}

	@Override
	public List<Integer> getQuoteGoodsByQuoteOrderId(Integer quoteorderId) {
		return quoteorderMapper.getQuoteGoodsByQuoteOrderId(quoteorderId);
	}

	@Override
	public AuthorizationApply getAutnorizationApplyByNum(String authorizationApplyNum) {
		return authorizationMapper.getAutnorizationApplyByNum(authorizationApplyNum);
	}

	@Override
	public List<AuthorizationApply> getAuthorizationApplyByQuoteId(Integer quoteorderId) {
		return authorizationMapper.getAuthorizationApplyByQuoteId(quoteorderId);
	}

	@Override
	public AuthorizationApply getAuthorizationIsRepeat(AuthorizationApply authorizationApply) {
		return authorizationMapper.getAuthorizationIsRepeat(authorizationApply);
	}

	@Override
	public AuthorizationApply getAuthorizationApplyByKeyId(Integer authorizationApplyId) {
		return authorizationMapper.getAuthorizationApplyByKeyId(authorizationApplyId);
	}

	@Override
	public void updateAuthorizationApplyModTimeAndStatus(Integer authorizationApplyId,Integer userId, Long time, Integer authorizationPass) {
		authorizationMapper.updateAuthorizationApplyModTimeAndStatus(authorizationApplyId,userId,time,authorizationPass);
	}

	@Override
	public List<AuthorizationApply> getAuthorizationApplyListByQuoteId(Integer quoteorderId) {
		return authorizationMapper.getAuthorizationApplyListByQuoteId(quoteorderId);
	}

	@Override
	public List<AuthorizationApplyDto> getAuthorizationApplylistpage(AuthorizationApplyVo authorizationApplyVo, Page page) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("authorizationApplyVo", authorizationApplyVo);
		map.put("page", page);
		return authorizationMapper.getAuthorizationApplylistpage(map);
	}

	@Override
	public void delAuthorizationStorageById(Integer temporaryStorageId,Integer userId,Long time) {
		authorizationStorageMapper.delAuthorizationStorageById(temporaryStorageId,userId,time);
	}

	@Override
	public AuthorizationStorage getTemporaryStorageByNum(String authorizationApplyNum) {
		return authorizationStorageMapper.getTemporaryStorageByNum(authorizationApplyNum);
	}

	@Override
	public AuthorizationApply getAuthorizationApplyByNum(String authorizationApplyNum) {
		return authorizationMapper.getAuthorizationApplyByNum(authorizationApplyNum);
	}

	@Override
	public void updateAuthorizationApplyReviewer(Integer authorizationApplyId, String verifyUsers) {
		authorizationMapper.updateAuthorizationApplyReviewer(authorizationApplyId,verifyUsers);
	}

	@Override
	public void updateAuthorizationApplyComments(Integer authorizationApplyId, String comments) {
		authorizationMapper.updateAuthorizationApplyComments(authorizationApplyId,comments);
	}

	@Override
	public List<String> getApplyPreson() {
		return authorizationMapper.getApplyPreson();
	}

	@Override
	public void updateAuthorizationStorage(String authorizationApplyNum) {
		authorizationStorageMapper.updateAuthorizationStorage(authorizationApplyNum);
	}

	@Override
	public List<AuthorizationApply> getAuthorizationApplyListByQuoteIdAndPass(Integer quoteorderId) {
		return authorizationMapper.getAuthorizationApplyListByQuoteIdAndPass(quoteorderId);
	}

	@Override
	public int getAuthorizationMaxId() {
		return authorizationMapper.getAuthorizationMaxId();
	}

	@Override
	public AuthorizationStorage getAuthorizationStorageInfoByQuoteOrderIdAndNum(Integer quoteorderId, String authorizationApplyNum) {
		return authorizationStorageMapper.getAuthorizationStorageInfoByQuoteOrderIdAndNum(quoteorderId,authorizationApplyNum);
	}

	@Override
	public void setQuteOrderUserInfo(QuoteorderVo quote, User user) {
		quote.setComments(quote.getQuoteComments());
		if(user != null){
			quote.setCompanyId(user.getCompanyId());
			quote.setCreator(user.getUserId());
			quote.setAddTime(DateUtil.sysTimeMillis());

			quote.setUpdater(user.getUserId());
			quote.setModTime(DateUtil.sysTimeMillis());

			quote.setUserId(user.getUserId());
			//销售部门（若一个多个部门，默认取第一个部门）
			Organization org = orgService.getOrgNameByUserId(user.getUserId());
			quote.setOrgId(org==null?null:org.getOrgId());
		}
	}

    @Override
    public String getCloseReasonInfo(Integer closeReasonId) {
		return sysOptionDefinitionMapper.getCloseReasonInfo(closeReasonId);
    }


    @Autowired
	private ActionProcdefService actionProcdefService;
    @Autowired
	private VerifiesRecordService verifiesRecordService;
	@Autowired // 自动装载
	private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

	@Override
	public ResultInfo<?> editApplyValidQuoteorder(HttpServletRequest request, Quoteorder quote, String taskId, HttpSession session) {
		ResultInfo<?> res = this.isvalidQuoteOrder(quote);
		if (res.getCode() == -1) {
			return res;
		}
		try {
			Map<String, Object> variableMap = new HashMap<String, Object>();
			// 查询当前订单的一些状态
			Quoteorder quoteorderInfo = this.getQuoteInfoByKey(quote.getQuoteorderId());
			User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
			Map<String,Object> map = this.getQuoteGoodsByQuoteId(quote.getQuoteorderId(),user.getCompanyId(),session,null,quote.getTraderId());
			//报价单中产品类型（0未维护,1 只有设备,2 只有试剂,3 又有试剂又有设备）
			quoteorderInfo.setGoodsType(0);
			List<Integer> goodsTypeList = new ArrayList<>();
			List<QuoteorderGoods> quoteGoodsList = (List<QuoteorderGoods>)map.get("quoteGoodsList");
			for(QuoteorderGoods qg:quoteGoodsList) {
				if(qg.getGoods() != null ){
					if(qg.getIsDelete() !=null && qg.getIsDelete() ==0 && qg.getGoods().getGoodsType() != null && (qg.getGoods().getGoodsType() == 316 || qg.getGoods().getGoodsType() == 319)){
						goodsTypeList.add(1);
					}else if(qg.getIsDelete() !=null && qg.getIsDelete() ==0 && qg.getGoods().getGoodsType() != null && (qg.getGoods().getGoodsType() == 317 || qg.getGoods().getGoodsType() == 318)){
						goodsTypeList.add(2);
					}
				}
			}
			//试剂和设备都有
			if(!goodsTypeList.isEmpty()) {
				List<Integer> newList = new ArrayList(new HashSet(goodsTypeList));
				if(newList.size() == 2) {
					quoteorderInfo.setGoodsType(3);
				}

				if(newList.size() == 1) {

					if(newList.get(0) == 1) {
						//只有设备
						quoteorderInfo.setGoodsType(1);
					}else if(newList.get(0) == 2){
						//只有试剂
						quoteorderInfo.setGoodsType(2);
					}

				}
			}

			//VDERP-10399 报价单提交审核时，如果包含了直发产品，直发产品的直发原因为空的情况下，自动用默认值填充，默认值为“客户着急发货”
			List<Integer> quoteGoodsIdList = quoteGoodsList.stream()
					.filter(item -> {
						if (item.getDeliveryDirect() == 1 && StringUtils.isBlank(item.getDeliveryDirectComments())){
							return true;
						}
						return false;
					})
					.map(QuoteorderGoods::getQuoteorderGoodsId)
					.collect(Collectors.toList());
			if (CollectionUtils.isNotEmpty(quoteGoodsIdList)){
				quoteGoodsMapper.updateDefaultDeliveryDirectCommentByQuoteGoodsId(quoteGoodsIdList);
			}


			//开始生成流程(如果没有taskId表示新流程需要生成)
			// 设置当前审核人(订单归属人)
			User userInfo = userService.getUserByTraderId(quoteorderInfo.getTraderId(), 1);// 1客户，2供应商
			Long userTime = userInfo.getAddTime();
			Long nowTime = DateUtil.sysTimeMillis();
			Integer day = (int) ((nowTime-userTime)/(3600*24*1000));
			variableMap.put("day", day);
			quoteorderInfo.setOptUserName(user==null?"":userInfo.getUsername());
			if(taskId.equals("0")){
				/**
				 * 流程名称quoteorder+variables+businessKey
				 */

				variableMap.put("quoteorder", quoteorderInfo);
				variableMap.put("currentAssinee", quoteorderInfo.getOptUserName());
				variableMap.put("processDefinitionKey","quoteVerify");
				variableMap.put("businessKey","quoteVerify_" + quoteorderInfo.getQuoteorderId());
				variableMap.put("relateTableKey",quoteorderInfo.getQuoteorderId());
				variableMap.put("relateTable", "T_QUOTEORDER");
				actionProcdefService.createProcessInstance(request,"quoteVerify","quoteVerify_" + quoteorderInfo.getQuoteorderId(),variableMap);
			}
			//默认申请人通过
			//根据BusinessKey获取生成的审核实例
			Map<String, Object> data = new HashMap<String, Object>();
			Map<String, Object> historicInfo=actionProcdefService.getHistoric(processEngine, "quoteVerify_"+ quoteorderInfo.getQuoteorderId());

			data.put("quoteorderId", quote.getQuoteorderId());
			if(historicInfo.get("endStatus") != "审核完成"){
				Task taskInfo = (Task) historicInfo.get("taskInfo");
				taskId = taskInfo.getId();
				Authentication.setAuthenticatedUserId(user.getUsername());
				Map<String, Object> variables = new HashMap<String, Object>();
				//设置审核完成监听器回写参数
				variables.put("tableName", "T_QUOTEORDER");
				variables.put("id", "QUOTEORDER_ID");
				variables.put("idValue", quote.getQuoteorderId());
				variables.put("key", "VALID_STATUS");
				variables.put("value", 1);
				variables.put("key1", "BUSSINESS_CHANCE_ID");
				variables.put("value1", quoteorderInfo.getBussinessChanceId());
				//回写数据的表在db中
				variables.put("db", 2);
				//默认审批通过
				ResultInfo<?> complementStatus = actionProcdefService.complementTask(request,taskId,"",quoteorderInfo.getOptUserName(),variables);
				//如果未结束添加审核对应主表的审核状态
				if(!complementStatus.getData().equals("endEvent")){
					verifiesRecordService.saveVerifiesInfo(taskId,0);
				}
				Integer status = null;
				// 查询当前订单的一些状态
				Quoteorder quoteorderInfoNew = this.getQuoteInfoByKey(quote.getQuoteorderId());
				if(quoteorderInfoNew.getValidStatus() == 1){
					status = 1;
				}else{
					status = 0;
				}
				// 添加改低价列表
				orderGoodsLowerPriceApiService.tryInsertLowPrice(LowPriceOrderTypeEnum.QUOTATION,quote.getQuoteorderId());
				return new ResultInfo(0, "操作成功",status,data);
			}
			return new ResultInfo(0, "操作成功",1,data);
		} catch (Exception e) {
			logger.error("editApplyValidQuoteorder:", e);
			return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
		}
	}


	@Override
	public List<ConsultGooodsVo> getConsultGoodsInfoBySaleorderOrQuoteorder(Integer consultRelatedId, Integer consultType) {
		List<ConsultGooodsVo> consultGooodsVoList = new ArrayList<>();
		if (consultType == 1){
			Saleorder saleorder = saleorderMapper.getSaleOrderById(consultRelatedId);
			List<SaleorderGoods> saleorderGoodsList = saleorderMapper.getSaleorderGoodsById(consultRelatedId);
			List<String> skuList = saleorderGoodsList.stream().map(SaleorderGoods::getSku).collect(Collectors.toList());
			if (skuList.size() == 0) {
				return consultGooodsVoList;
			}
			Map<String, WarehouseStock> warehouseStockMap = warehouseStockService.getStockInfo(skuList);
			consultGooodsVoList = saleorderGoodsList
					.stream()
					.map(saleorderGoods -> {
						ConsultGooodsVo item = new ConsultGooodsVo();
						item.setSku(saleorderGoods.getSku());
						item.setQuoteorderGoodsId(quoteorderMapper.getQuoteorderGoodsIdByQuoteorderAndSku(saleorder.getQuoteorderId(),saleorderGoods.getSku()));
						item.setGoodsId(saleorderGoods.getGoodsId());
						item.setGoodsName(saleorderGoods.getGoodsName());
						item.setBrandName(saleorderGoods.getBrandName());
						item.setModel(saleorderGoods.getModel());
						item.setUnitName(saleorderGoods.getUnitName());
						item.setNum(saleorderGoods.getNum());
						//获取sku的预计可发货时间
						if (StringUtils.isNotBlank(item.getSku())){
							item.setDeliveryCycle(goodsService.getDeliveryRangeOfSku(item.getSku()));
							item.setReferencePrice(getReferencePriceOfSku(item.getSku(),saleorder.getTraderId(),saleorder.getCustomerNature()));
							if (warehouseStockMap.containsKey(item.getSku())){
								item.setWarehouseStock(warehouseStockMap.get(item.getSku()));
							}
						}
						return item;
					}).collect(Collectors.toList());

		} else if (consultType == 2){
			Quoteorder quoteorder = quoteorderMapper.getQuoteorderById(consultRelatedId);
			List<QuoteorderGoods> quoteorderGoodsList = quoteorderMapper.getQuoteorderGoodsByIdsample(consultRelatedId);
			List<String> skuList = quoteorderGoodsList.stream().filter(quoteorderGoods -> StringUtils.isNotBlank(quoteorderGoods.getSku())).map(QuoteorderGoods::getSku).collect(Collectors.toList());
			Map<String, WarehouseStock> warehouseStockMap = new HashMap<>();
			if (skuList.size() > 0) {
				warehouseStockMap = warehouseStockService.getStockInfo(skuList);
			}
			for (QuoteorderGoods quoteorderGoods : quoteorderGoodsList){
				ConsultGooodsVo item = new ConsultGooodsVo();
				item.setSku(quoteorderGoods.getSku());
				item.setQuoteorderGoodsId(quoteorderGoods.getQuoteorderGoodsId());
				item.setGoodsId(quoteorderGoods.getGoodsId());
				item.setGoodsName(quoteorderGoods.getGoodsName());
				item.setBrandName(quoteorderGoods.getBrandName());
				item.setModel(quoteorderGoods.getModel());
				item.setUnitName(quoteorderGoods.getUnitName());
				item.setNum(quoteorderGoods.getNum());
				//获取sku的预计可发货时间
				if (StringUtils.isNotBlank(item.getSku())){
					item.setDeliveryCycle(goodsService.getDeliveryRangeOfSku(item.getSku()));
					item.setReferencePrice(getReferencePriceOfSku(item.getSku(),quoteorder.getTraderId(),quoteorder.getCustomerNature()));
					if (warehouseStockMap.containsKey(item.getSku())){
						item.setWarehouseStock(warehouseStockMap.get(item.getSku()));
					}
				}
				consultGooodsVoList.add(item);
			}
		}
		return consultGooodsVoList;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean saveQuoteorderConsult(QuoteorderConsultContentVo consultContentVo, Integer userId) {
		Integer quoteorderId;
		List<Integer> assignUserOfSkuList = new ArrayList<>();
		List<Integer> supplyManagerList = new ArrayList<>();
		List<Integer> saleManager = new ArrayList<>();

		if (consultContentVo.getConsultType() == 1){
			//获取订单的报价单
			Saleorder saleorder = saleorderMapper.getSaleOrderById(consultContentVo.getConsultRelatedId());
			if (saleorder == null || saleorder.getQuoteorderId() == null || saleorder.getQuoteorderId() == 0){
				return false;
			} else {
				quoteorderId = saleorder.getQuoteorderId();
			}
		} else {
			quoteorderId = consultContentVo.getConsultRelatedId();
		}
		Quoteorder quoteorder = quoteorderMapper.getQuoteorderById(quoteorderId);
		if (consultContentVo.getSkuConsults() != null && !consultContentVo.getSkuConsults().isEmpty()){
			saveConsultSupplyContent(quoteorderId,userId,consultContentVo);
			consultContentVo.getSkuConsults().stream()
					.filter(skuConsult -> StringUtils.isNotBlank(skuConsult.getSku()))
					.forEach(skuConsult -> {
						List<Integer> assignUserOfSku = getAssignUserOfSku(skuConsult.getSku());
						if (assignUserOfSku.size() > 0){
							//咨询产品经理和助理
							assignUserOfSkuList.addAll(assignUserOfSku);
						} else {
							supplyManagerList.addAll(getSupplyManager());
						}
					});
		} else if (StringUtils.isNotBlank(consultContentVo.getConsultContent())){
			//咨询主管
			saveConsultExecutiveContent(quoteorderId,userId,consultContentVo.getConsultContent());
			//获取订单归属销售的直接上级
			saleManager.add(getSalerManagerOfQuoteorder(quoteorder));
		}
		//同步更新报价单的咨询状态和咨询时间
		updateConsultStatusOfQuoteorder(quoteorderId,0);

		if (assignUserOfSkuList.size() > 0){
			//咨询产品经理和助理
			Map<String,String> messageMap = new HashMap<>(1);
			messageMap.put("quoteorderNo",quoteorder.getQuoteorderNo());
			MessageUtil.sendMessage(31,assignUserOfSkuList.stream().distinct().collect(Collectors.toList()),messageMap,"./order/quote/getQuoteDetail.do?quoteorderId=" + quoteorderId + "&viewType=5");
		}
		if (supplyManagerList.size() > 0){
			//咨询供应链主管
			Map<String,String> messageMap = new HashMap<>(1);
			messageMap.put("quoteorderNo",quoteorder.getQuoteorderNo());
			MessageUtil.sendMessage(118,supplyManagerList.stream().distinct().collect(Collectors.toList()),messageMap,"./order/quote/getQuoteDetail.do?quoteorderId=" + quoteorderId + "&viewType=5");
		}
		if (saleManager.size() > 0){
			Map<String,String> messageMap = new HashMap<>(1);
			messageMap.put("orderNo",quoteorder.getQuoteorderNo());
			String url;
			if (consultContentVo.getConsultType() == 1){
				Saleorder saleorder = saleorderMapper.getSaleOrderById(consultContentVo.getConsultRelatedId());
				messageMap.put("orderNo",saleorder.getSaleorderNo());
				url = "./order/saleorder/view.do?saleorderId=" + saleorder.getSaleorderId();
			} else {
				messageMap.put("orderNo",quoteorder.getQuoteorderNo());
				url = "./order/quote/getQuoteDetail.do?quoteorderId=" + quoteorderId + "&viewType=4";
			}
			MessageUtil.sendMessage(116,saleManager,messageMap,url);
		}

		if(consultContentVo.getConsultType() == 2){
			//报价预警记录点--采购端触发(咨询供应链时)
			if(CollectionUtils.isNotEmpty(consultContentVo.getSkuConsults())) {
				logger.info("咨询记录："+consultContentVo.toString()+"开始预先记录预警信息。");
				consultContentVo.getSkuConsults().stream().forEach(consult -> logger.info("咨询的goods："+consult.toString()));
				List<Integer> QuotedGoodsIdList = consultContentVo.getSkuConsults().stream()
						.map(QuoteorderConsultContentVo.SkuConsult::getQuoteorderGoodsId).collect(Collectors.toList());
				boolean consultPrice = consultContentVo.getSkuConsults().stream().anyMatch(consult -> Objects.equals(consult.getConsultReferencePrice(),Boolean.TRUE));
				if(consultPrice){
					collectQuotedAlarmRecordWhenFirstTrigger(quoteorder.getQuoteorderId(), QuotedAlarmModeEnum.PURCHASER_MODE,QuotedGoodsIdList);
				}
			}
		}

		return true;
	}


	@Override
	public List<QuoteorderGoods> getConsultResultOfQuoteorderGoods(List<QuoteorderGoods> quoteorderGoodsList, Integer viewType){
		return quoteorderGoodsList.parallelStream()
				//在报价咨询页，不展示被删除的商品信息
				.filter(quoteorderGoods -> quoteorderGoods.getIsDelete() != 1 || viewType == null || viewType != 5)
				.map(
					item -> {
						List<QuoteorderConsultReply> consultReplyList = quoteorderConsultReplyMapper.getByQuoteorderIdAndQuoteorderGoods(item.getQuoteorderId(),item.getQuoteorderGoodsId());
						if (consultReplyList.isEmpty()){
							item.setQuoteorderConsultReply(new QuoteorderConsultReply());
							return item;
						} else {
							QuoteorderConsultReply consultReply = consultReplyList.get(0);
							List<Integer> assignUser = getAssignUserOfSku(item.getSku());
							if (assignUser.size() > 0){
								consultReply.setConsultReplierRole(1);
								consultReply.setConsultReplierList(assignUser);
								consultReply.setConsultReplierName(String.join(",", userMapper.getUserNameByUserIdList(assignUser)));
							} else if (consultReplyList.get(0).getConsultReplier() != null && consultReplyList.get(0).getConsultReplier() > 0){
								consultReply.setConsultReplierRole(2);
								consultReply.setConsultReplierList(Collections.singletonList(consultReplyList.get(0).getConsultReplier()));
								consultReply.setConsultReplierName(String.join(",",userMapper.getUserNameByUserId(consultReplyList.get(0).getConsultReplier())));
							} else {
								List<Integer> managerList = getSupplyManager();
								consultReply.setConsultReplierRole(0);
								consultReply.setConsultReplierList(managerList);
								consultReply.setConsultReplierName(String.join(",", userMapper.getUserNameByUserIdList(managerList)));
							}
							item.setQuoteorderConsultReply(consultReply);
							return item;
						}
					}
		).collect(Collectors.toList());
	}

	@Override
	public List<SaleorderGoods> getConsultResultOfSaleorderGoods(List<SaleorderGoods> saleorderGoodsList){
		Saleorder saleorder = saleorderMapper.getSaleorderBySaleorderGoodsId(saleorderGoodsList.get(0).getSaleorderGoodsId());
		if (saleorder.getQuoteorderId() == null || saleorder.getQuoteorderId() == 0){
			return saleorderGoodsList;
		}
		return saleorderGoodsList.parallelStream().map(
				item -> {
					List<QuoteorderConsultReply> consultReplyList = quoteorderConsultReplyMapper.getByQuoteorderIdAndSku(saleorder.getQuoteorderId(),item.getSku());
					if (consultReplyList.isEmpty()){
						item.setQuoteorderConsultReply(new QuoteorderConsultReply());
						return item;
					} else {
						QuoteorderConsultReply consultReply = consultReplyList.get(0);
						List<Integer> assignUser = getAssignUserOfSku(item.getSku());
						if (assignUser.size() > 0){
							consultReply.setConsultReplierRole(1);
							consultReply.setConsultReplierList(assignUser);
							consultReply.setConsultReplierName(String.join(",", userMapper.getUserNameByUserIdList(assignUser)));
						} else if (consultReplyList.get(0).getConsultReplier() != null && consultReplyList.get(0).getConsultReplier() > 0){
							consultReply.setConsultReplierRole(2);
							consultReply.setConsultReplierList(Collections.singletonList(consultReplyList.get(0).getConsultReplier()));
							consultReply.setConsultReplierName(String.join(",",userMapper.getUserNameByUserId(consultReplyList.get(0).getConsultReplier())));
						} else {
							List<Integer> managerList = getSupplyManager();
							consultReply.setConsultReplierRole(0);
							consultReply.setConsultReplierList(managerList);
							consultReply.setConsultReplierName(String.join(",", userMapper.getUserNameByUserIdList(managerList)));
						}
						item.setQuoteorderConsultReply(consultReply);
						return item;
					}
				}
		).collect(Collectors.toList());
	}

	@Override
	public void saveAssignConsultReplier(QuoteorderConsultReply consultReply) {
		List<QuoteorderConsultReply> consultReplyList = quoteorderConsultReplyMapper.getByQuoteorderIdAndQuoteorderGoods(consultReply.getQuoteorderId(),consultReply.getQuoteorderGoodsId());
		if (!consultReplyList.isEmpty()){
			consultReply.setQuoteorderConsultReplyId(consultReplyList.get(0).getQuoteorderConsultReplyId());
			quoteorderConsultReplyMapper.updateByPrimaryKeySelective(consultReply);
			Integer quoteorderId = consultReplyList.get(0).getQuoteorderId();
			Map<String,String> map = new HashMap<>(1);
			map.put("quoteorderNo",quoteorderMapper.getQuoteorderById(quoteorderId).getQuoteorderNo());
			MessageUtil.sendMessage(119,Collections.singletonList(consultReply.getConsultReplier()),map,"./order/quote/getQuoteDetail.do?quoteorderId=" + quoteorderId + "&viewType=5");
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveManagerReplyOfQuoteorderConsult(QuoteorderConsultContentVo consultReply, Integer userId) {
		Long timestamp = DateUtil.sysTimeMillis();
		Integer quoteorderId = consultReply.getConsultRelatedId();
		Saleorder saleorder = null;
		if (consultReply.getConsultType() == 1){
			//根据订单查询报价单的id
			saleorder = saleorderMapper.getSaleOrderById(consultReply.getConsultRelatedId());
			if (saleorder != null){
				quoteorderId = saleorder.getQuoteorderId();
			}
		}
		List<QuoteorderConsultReply> quoteorderConsultReplies = quoteorderConsultReplyMapper.getByQuoteorderIdAndConsultType(quoteorderId,2);
		if (quoteorderConsultReplies.size() > 0){
			//更新咨询主管回复状态
			quoteorderConsultReplyMapper.updateConsultExecutiveStatus(quoteorderConsultReplies.get(0).getQuoteorderConsultReplyId(),consultReply.getConsultContent(),userId,timestamp);
		}
		//保存主管回复信息
		QuoteorderConsult quoteorderConsult = new QuoteorderConsult();
		quoteorderConsult.setQuoteorderId(quoteorderId);
		quoteorderConsult.setContent(consultReply.getConsultContent());
		quoteorderConsult.setType(4);
		quoteorderConsult.setAddTime(timestamp);
		quoteorderConsult.setCreator(userId);
		quoteorderConsultMapper.insert(quoteorderConsult);

		//信息同步一份到报价咨询记录表
		QuoteorderConsultDetail quoteorderConsultDetail = new QuoteorderConsultDetail();
		quoteorderConsultDetail.setQuoteorderConsultId(quoteorderConsult.getQuoteorderConsultId());
		quoteorderConsultDetail.setQuoteorderId(quoteorderId);
		quoteorderConsultDetail.setCreator(userId);
		quoteorderConsultDetail.setAddTime(timestamp);
		quoteorderConsultDetail.setOtherContent(consultReply.getConsultContent());
		quoteorderConsultDetail.setQuoteConsultType(4);
		quoteorderConsultDetailMapper.insert(quoteorderConsultDetail);

		//更新报价单咨询状态和回复时间
		updateConsultStatusOfQuoteorder(quoteorderId,1);

		if (quoteorderConsultReplies.size() > 0){
			String urlOfMessage;
			Map<String,String> mapOfMessage = new HashMap<>(1);
			Integer saler = quoteorderConsultReplies.get(0).getCreator();
			if (saleorder != null){
				urlOfMessage = "./order/saleorder/view.do?saleorderId=" + saleorder.getSaleorderId();
				mapOfMessage.put("orderNo",saleorder.getSaleorderNo());
			} else {
				urlOfMessage = "./order/quote/getQuoteDetail.do?quoteorderId=" + quoteorderId + "&viewType=5";
				mapOfMessage.put("orderNo",quoteorderMapper.getQuoteorderById(quoteorderId).getQuoteorderNo());
			}
			MessageUtil.sendMessage(117,Collections.singletonList(saler),mapOfMessage,urlOfMessage);
		}

	}


	@Override
	public void saveSupplyReplyOfQuoteorderConsult(QuoteorderConsultContentVo consultReply, Integer userId) {
		Long timestamp = DateUtil.sysTimeMillis();
		Integer quoteorderId = consultReply.getConsultRelatedId();
		StringBuilder replyContentBuilder = new StringBuilder();
		List<QuoteorderConsultDetail> quoteorderConsultDetailList = new ArrayList<>();
		consultReply.getSkuConsults().forEach(skuConsult -> {
			QuoteorderConsultReply reply = new QuoteorderConsultReply();
			//信息同步一份到报价咨询记录表
			QuoteorderConsultDetail quoteorderConsultDetail = new QuoteorderConsultDetail();
			List<QuoteorderConsultReply> consultReplyList = quoteorderConsultReplyMapper.getByQuoteorderIdAndQuoteorderGoods(quoteorderId,skuConsult.getQuoteorderGoodsId());
			if (consultReplyList.size() > 0){
				reply.setQuoteorderId(consultReplyList.get(0).getQuoteorderId());
				reply.setSku(consultReplyList.get(0).getSku());
				reply.setQuoteorderConsultReplyId(consultReplyList.get(0).getQuoteorderConsultReplyId());
			} else {
				QuoteorderGoods quoteorderGoods = quoteorderMapper.getQuoteorderGoodsByQuoteorderGoodsId(skuConsult.getQuoteorderGoodsId());
				reply.setQuoteorderId(quoteorderGoods.getQuoteorderId());
				reply.setSku(quoteorderGoods.getSku());
			}
			reply.setQuoteorderGoodsId(skuConsult.getQuoteorderGoodsId());
			replyContentBuilder.append("【");
			if (StringUtils.isNotBlank(skuConsult.getSku())){
				replyContentBuilder.append(skuConsult.getSku());
				quoteorderConsultDetail.setSkuNo(skuConsult.getSku());
			} else {
				String goodsName = quoteorderMapper.getQuoteorderGoodsByQuoteorderGoodsId(skuConsult.getQuoteorderGoodsId()).getGoodsName();
				replyContentBuilder.append(goodsName);
				quoteorderConsultDetail.setSkuNo(goodsName);
			}
			replyContentBuilder.append("】");
			reply.setUpdateTime(timestamp);
			reply.setUpdater(userId);
			quoteorderConsultDetail.setAddTime(timestamp);
			quoteorderConsultDetail.setCreator(userId);
			quoteorderConsultDetail.setQuoteorderId(quoteorderId);
			quoteorderConsultDetail.setQuoteConsultType(3);
			StringBuilder consultDetailBuilder = new StringBuilder();
			if (StringUtils.isNotBlank(skuConsult.getReferencePrice())){
				reply.setReferencePriceReply(skuConsult.getReferencePrice());
				reply.setReferencePriceReplyStatus(1);
				consultDetailBuilder.append("报价：").append(skuConsult.getReferencePrice()).append("；");
			}
			if (StringUtils.isNotBlank(skuConsult.getDeliveryCycle())){
				reply.setReferenceDeliveryCycleReply(skuConsult.getDeliveryCycle());
				reply.setReferenceDeliveryCycleReplyStatus(1);
				consultDetailBuilder.append("货期：").append(skuConsult.getDeliveryCycle()).append("；");
			}
			if (skuConsult.getReport() != null){
				reply.setReportConsultReply(skuConsult.getReport());
				reply.setReportConsultReplyContent(skuConsult.getReportContet());
				reply.setReportConsultReplyStatus(1);
				consultDetailBuilder.append("报备结果：");
				if (skuConsult.getReport() == 3){
					consultDetailBuilder.append("失败；");
					if (StringUtils.isNotBlank(skuConsult.getReportContet())){
						consultDetailBuilder.append("失败原因：").append(skuConsult.getReportContet()).append("；");
					}
				}
				if (skuConsult.getReport() == 0){
					consultDetailBuilder.append("无需报备；");
				} else if (skuConsult.getReport() == 2){
					consultDetailBuilder.append("成功；");
				}
			}
			if (StringUtils.isNotBlank(skuConsult.getConsultOtherReplyContent())){
				reply.setConsultOtherReply(skuConsult.getConsultOtherReplyContent());
				reply.setConsultOtherReplyStatus(1);
				consultDetailBuilder.append("其他：").append(skuConsult.getConsultOtherReplyContent()).append("；");
			}
			replyContentBuilder.append(consultDetailBuilder);
			replyContentBuilder.append("<br/>");
			quoteorderConsultDetail.setOtherContent(consultDetailBuilder.toString());
			quoteorderConsultDetailList.add(quoteorderConsultDetail);
			if (consultReplyList.size() > 0){
				quoteorderConsultReplyMapper.updateByPrimaryKeySelective(reply);
			} else {
				quoteorderConsultReplyMapper.insertSelective(reply);
			}
			//同步更新quoteorderGoods表里的报价咨询回复结果
			if (StringUtils.isNotBlank(reply.getReferencePriceReply()) || StringUtils.isNotBlank(reply.getReferenceDeliveryCycleReply()) || reply.getReportConsultReply() != null){
				quoteorderMapper.updateConsultResultOfQuoteorderGoods(reply);
			}
		});

		//保存供应链的回复内容
		QuoteorderConsult quoteorderConsult = new QuoteorderConsult();
		quoteorderConsult.setQuoteorderId(quoteorderId);
		quoteorderConsult.setCreator(userId);
		quoteorderConsult.setAddTime(DateUtil.sysTimeMillis());
		quoteorderConsult.setType(3);
		quoteorderConsult.setContent(replyContentBuilder.substring(0,replyContentBuilder.length()-5));
		quoteorderConsultMapper.insert(quoteorderConsult);

		if (CollectionUtils.isNotEmpty(quoteorderConsultDetailList)){
			Integer quoteorderConsultId = quoteorderConsult.getQuoteorderConsultId();
			quoteorderConsultDetailList.forEach(item->{
				item.setQuoteorderConsultId(quoteorderConsultId);
			});
			quoteorderConsultDetailMapper.batchInsert(quoteorderConsultDetailList);
		}

		//更新报价单的咨询状态和回复时间
		updateConsultStatusOfQuoteorder(quoteorderId,1);
		Quoteorder quoteorder = quoteorderMapper.getQuoteorderById(quoteorderId);
		List<Integer> userIdList = userMapper.getUserIdListByTraderId(quoteorder.getTraderId(),ErpConst.ONE);
		MessageUtil.sendMessage(32, userIdList, new HashMap<String, String>() {{put("quoteorderNo", quoteorder.getQuoteorderNo());}}, "./order/quote/getQuoteDetail.do?quoteorderId="+quoteorderId+"&viewType=2");

		if(consultReply.getConsultType() == 2){
			//报价预警记录点回复报价时触发--销售端触发
			if(CollectionUtils.isNotEmpty(consultReply.getSkuConsults())) {
				boolean quotedReply = consultReply.getSkuConsults().stream().anyMatch(reply -> StringUtils.isNotEmpty(reply.getReferencePrice()));
				if (quotedReply) {
					collectQuotedAlarmRecordWhenFirstTrigger(consultReply.getConsultRelatedId(),  QuotedAlarmModeEnum.SALESMAN_MODE,null);
				}
			}
		}
	}


	/**
	 * 更新报价单的咨询状态
	 * 咨询答复状态0无 1供应链未处理，主管未处理；2供应链部分处理，主管未处理；3供应链已全部处理，主管未处理；4供应链未处理，主管已处理；5供应链部分处理，主管已处理；6供应链全部处理，主管已处理
	 * @param quoteorderId 报价单id
	 * @param type 0咨询， 1答复
	 */
	@Override
	public void updateConsultStatusOfQuoteorder(Integer quoteorderId, Integer type){
		int consultStatusOfQuoteorder;
		Integer countOfConsultSupplyReply = quoteorderConsultReplyMapper.getCountOfConsultSupplyReply(quoteorderId);
		Integer unhandledCountOfConsultSupply = quoteorderConsultReplyMapper.getUnHandledCountOfConsultSupply(quoteorderId);
		Integer unhandledCountOfConsultExecutive = quoteorderConsultReplyMapper.getUnhandledCountOfConsultManager(quoteorderId);
		//供应链未处理，主管未处理
		if (countOfConsultSupplyReply == 0 && unhandledCountOfConsultSupply > 0 && unhandledCountOfConsultExecutive > 0){
			consultStatusOfQuoteorder = 1;
		} else if (countOfConsultSupplyReply > 0 && unhandledCountOfConsultSupply > 0 && unhandledCountOfConsultExecutive > 0){
			//供应链部分处理，主管未处理
			consultStatusOfQuoteorder = 2;
		} else if (unhandledCountOfConsultSupply == 0 && unhandledCountOfConsultExecutive > 0){
			//供应链已全部处理，主管未处理
			consultStatusOfQuoteorder = 3;
		} else if (countOfConsultSupplyReply == 0 && unhandledCountOfConsultSupply > 0 && unhandledCountOfConsultExecutive == 0){
			//供应链未处理，主管已处理
			consultStatusOfQuoteorder = 4;
		} else if (countOfConsultSupplyReply > 0 && unhandledCountOfConsultSupply > 0 && unhandledCountOfConsultExecutive == 0){
			//供应链部分处理，主管已处理
			consultStatusOfQuoteorder = 5;
		} else if (unhandledCountOfConsultSupply == 0 && unhandledCountOfConsultExecutive == 0){
			//供应链全部处理，主管已处理
			consultStatusOfQuoteorder = 6;
		} else {
			consultStatusOfQuoteorder = 0;
		}
		quoteorderMapper.updateConsultStatusOfQuoteorder(quoteorderId,consultStatusOfQuoteorder,type,DateUtil.sysTimeMillis());
	}

	/**
	 * 获取咨询商品的归属人，如果sku没有归属人，那么归属人默认为供应主管
	 * @param sku sku
	 * @return 归属人
	 */
	private List<Integer> getConsultReplierOfConsultGoods(String sku){
		List<Integer> assignUserOfSku = getAssignUserOfSku(sku);
		if (assignUserOfSku.size() > 0) {
			return assignUserOfSku;
		}
		//查询供应主管
		return getSupplyManager();
	}


	private List<Integer> getSupplyManager(){
		//查询供应主管
		return roleMapper.getUserIdByRoleName("供应主管",1);
	}


	@Override
	public List<Integer> getAssignUserOfSku(String sku){
		List<Integer> userIdList = new ArrayList<>();
		CoreSpu spu = coreSpuMapper.getSpuBySku(sku);
		if (spu == null){
			return userIdList;
		}
		boolean assignManagerOfSpu = spu.getAssignmentManagerId() != null && spu.getAssignmentManagerId() > 0;
		boolean assignAssistantOfSpu = spu.getAssignmentAssistantId() != null && spu.getAssignmentAssistantId() > 0;
		if (assignManagerOfSpu){
			userIdList.add(spu.getAssignmentManagerId());
		}
		if (assignAssistantOfSpu){
			userIdList.add(spu.getAssignmentAssistantId());
		}
		return userIdList;
	}

	@Override
	public List<QuoteorderGoods> getQuoteorderGoodsByOrderId(Integer orderId) {
		return 	quoteorderMapper.getQuoteorderGoodsByIdsample(orderId);
	}

	@Override
	public Integer saveQuoteConsultReplyStatus(Integer quoteorderId, QuoteorderConsultReply quoteorderConsultReply) {
		if (quoteorderId == null || quoteorderConsultReply == null){
			return 0;
		}
		List<QuoteorderConsultReply> QuoteorderGoods = quoteorderConsultReplyMapper
				.getByQuoteorderIdAndQuoteorderGoods(quoteorderId, quoteorderConsultReply.getQuoteorderGoodsId());
		if (CollectionUtils.isNotEmpty(QuoteorderGoods)){
			QuoteorderGoods.stream().forEach(quoteorderConsultReplyInfo -> {
				QuoteorderConsultReply consultReply = new QuoteorderConsultReply();
				consultReply.setQuoteorderConsultReplyId(quoteorderConsultReplyInfo.getQuoteorderConsultReplyId());
				consultReply.setReportConsultReplyStatus(quoteorderConsultReply.getReportConsultReplyStatus());
				consultReply.setReportConsultReply(quoteorderConsultReply.getReportConsultReply());
				quoteorderConsultReplyMapper.updateByPrimaryKeySelective(consultReply);
			});
			return 1;
		}
		quoteorderConsultReply.setQuoteorderId(quoteorderId);
		quoteorderConsultReplyMapper.insertSelective(quoteorderConsultReply);
		return 1;
	}

	@Override
	public Integer saveQuoteGoodsReportStatus(Integer goodsId, Integer reportStatus) {
		if (goodsId == null || reportStatus == null){
			return 0;
		}
		return quoteorderMapper.updateQuoteGoodsReportStatus(goodsId,reportStatus);
	}


	public Integer getSalerManagerOfQuoteorder(Quoteorder quoteorder){
		if (quoteorder.getTraderId() == null || quoteorder.getTraderId() == 0){
			return 0;
		}
		//归属销售
		User user = userService.getUserByTraderId(quoteorder.getTraderId(),1);
		if (user == null) {
			return 0;
		}
		return user.getParentId();
	}

	/**
	 * 保存咨询主管的内容
	 * @param quoteorderId 报价单id
	 * @param userId 用户id
	 * @param consultContent 咨询内容
	 */
	private void saveConsultExecutiveContent(Integer quoteorderId, Integer userId, String consultContent){
		QuoteorderConsult quoteorderConsult = new QuoteorderConsult();
		List<QuoteorderConsultReply> quoteorderConsultReplyList = quoteorderConsultReplyMapper.getByQuoteorderIdAndConsultType(quoteorderId,2);
		if (quoteorderConsultReplyList.size() == 0){
			//保存咨询主管的内容
			QuoteorderConsultReply consultExecutive = new QuoteorderConsultReply();
			consultExecutive.setQuoteorderId(quoteorderId);
			consultExecutive.setConsultType(2);
			consultExecutive.setConsultExecutive(consultContent);
			consultExecutive.setConsultExecutiveReplyStatus(-1);
			consultExecutive.setAddTime(DateUtil.sysTimeMillis());
			consultExecutive.setCreator(userId);
			quoteorderConsultReplyMapper.insertSelective(consultExecutive);
		} else {
			//更新咨询主管的内容
			QuoteorderConsultReply consultExecutive = new QuoteorderConsultReply();
			consultExecutive.setQuoteorderConsultReplyId(quoteorderConsultReplyList.get(0).getQuoteorderConsultReplyId());
			consultExecutive.setConsultExecutive(consultContent);
			consultExecutive.setConsultExecutiveReplyStatus(-1);
			consultExecutive.setUpdater(userId);
			consultExecutive.setUpdateTime(DateUtil.sysTimeMillis());
			quoteorderConsultReplyMapper.updateByPrimaryKeySelective(consultExecutive);
		}
		//保存咨询内容
		quoteorderConsult.setQuoteorderId(quoteorderId);
		quoteorderConsult.setType(2);
		quoteorderConsult.setContent(consultContent);
		quoteorderConsult.setAddTime(DateUtil.sysTimeMillis());
		quoteorderConsult.setCreator(userId);
		quoteorderConsultMapper.insert(quoteorderConsult);

		//信息同步一份到报价咨询记录表
		QuoteorderConsultDetail quoteorderConsultDetail = new QuoteorderConsultDetail();
		quoteorderConsultDetail.setQuoteorderConsultId(quoteorderConsult.getQuoteorderConsultId());
		quoteorderConsultDetail.setQuoteorderId(quoteorderId);
		quoteorderConsultDetail.setCreator(userId);
		quoteorderConsultDetail.setAddTime(DateUtil.sysTimeMillis());
		quoteorderConsultDetail.setOtherContent(consultContent);
		quoteorderConsultDetail.setQuoteConsultType(2);
		quoteorderConsultDetailMapper.insert(quoteorderConsultDetail);
	}

	/**
	 * 保存咨询供应链的内容
	 * @param quoteorderId 报价单id
	 * @param userId 发起咨询的用户id
	 * @param consultContentVo 咨询内容
	 */
	private void saveConsultSupplyContent(Integer quoteorderId, Integer userId, QuoteorderConsultContentVo consultContentVo){
		StringBuilder contentBuilder = new StringBuilder();
		Long timestamp = DateUtil.sysTimeMillis();
		List<QuoteorderConsultDetail> quoteorderConsultDetailList = new ArrayList<>();
		Map<Integer,Integer> reportStatusMapOfSku = dealWithQuoteOrderGoodsAuthorizationResult(quoteorderId);
		for (QuoteorderConsultContentVo.SkuConsult skuConsult : consultContentVo.getSkuConsults()){
			//信息同步一份到报价咨询记录表
			QuoteorderConsultDetail quoteorderConsultDetail = new QuoteorderConsultDetail();
			//VDERP-3214【询价工具】若咨询的产品为手填产品，则报价咨询记录中，显示产品名称
			contentBuilder.append("【");
			if (StringUtils.isNotBlank(skuConsult.getSku())){
				contentBuilder.append(skuConsult.getSku());
				quoteorderConsultDetail.setSkuNo(skuConsult.getSku());
			} else {
				String goodsName = quoteorderMapper.getQuoteorderGoodsByQuoteorderGoodsId(skuConsult.getQuoteorderGoodsId()).getGoodsName();
				contentBuilder.append(goodsName);
				quoteorderConsultDetail.setSkuNo(goodsName);
			}
			contentBuilder.append("】");
			QuoteorderConsultReply consultReply = new QuoteorderConsultReply();
			consultReply.setQuoteorderId(quoteorderId);
			consultReply.setConsultType(1);
			consultReply.setQuoteorderGoodsId(skuConsult.getQuoteorderGoodsId());

			quoteorderConsultDetail.setAddTime(timestamp);
			quoteorderConsultDetail.setCreator(userId);
			quoteorderConsultDetail.setQuoteorderId(quoteorderId);
			quoteorderConsultDetail.setQuoteConsultType(1);
			StringBuilder consultDetailBuilder = new StringBuilder();
			if (StringUtils.isNotBlank(skuConsult.getSku())){
				consultReply.setSku(skuConsult.getSku());
			}
			List<QuoteorderConsultReply> consultReplyList = quoteorderConsultReplyMapper.getByQuoteorderIdAndQuoteorderGoods(quoteorderId,skuConsult.getQuoteorderGoodsId());
			if (skuConsult.getConsultReferencePrice() != null && skuConsult.getConsultReferencePrice()){
				consultDetailBuilder.append("报价；");
				consultReply.setReferencePriceReplyStatus(-1);
			}
			if (skuConsult.getConsultDeliveryCycle() != null && skuConsult.getConsultDeliveryCycle()){
				consultDetailBuilder.append("货期；");
				consultReply.setReferenceDeliveryCycleReplyStatus(-1);
			}
			if (skuConsult.getConsultReport() != null && skuConsult.getConsultReport()){
				consultDetailBuilder.append("报备情况；");
				consultReply.setReportConsultReplyStatus(-1);
			}
			if (reportStatusMapOfSku.containsKey(skuConsult.getQuoteorderGoodsId())){
				if (reportStatusMapOfSku.get(skuConsult.getQuoteorderGoodsId()) == 1){
					//需要报备
					consultDetailBuilder.append("报备情况；");
					consultReply.setReportConsultReplyStatus(-1);
				} else {
					consultReply.setReportConsultReplyStatus(1);
					consultReply.setReportConsultReply(0);
					consultReply.setReportConsultReplyContent("");
				}
			}
			if (skuConsult.getConsultOther() != null && skuConsult.getConsultOther() && StringUtils.isNotBlank(skuConsult.getConsultOtherContent())){
				consultDetailBuilder.append("其他（").append(skuConsult.getConsultOtherContent()).append("）；");
				consultReply.setConsultOtherReplyStatus(-1);
				consultReply.setConsultOther(skuConsult.getConsultOtherContent());
			}
			contentBuilder.append(consultDetailBuilder);
			contentBuilder.append("<br/>");
			quoteorderConsultDetail.setOtherContent(consultDetailBuilder.toString());
			quoteorderConsultDetailList.add(quoteorderConsultDetail);
			if (consultReplyList.size() == 0) {
				//保存Sku咨询行为
				consultReply.setCreator(userId);
				consultReply.setAddTime(DateUtil.sysTimeMillis());
				quoteorderConsultReplyMapper.insertSelective(consultReply);
			} else {
				//更新sku咨询行为
				consultReply.setQuoteorderConsultReplyId(consultReplyList.get(0).getQuoteorderConsultReplyId());
				consultReply.setUpdater(userId);
				consultReply.setUpdateTime(DateUtil.sysTimeMillis());
				quoteorderConsultReplyMapper.updateByPrimaryKeySelective(consultReply);
			}
			//当报备为无需报备时，同步更新报备状态到QuoteorderGoods表，但是不同步consultReply.updater，因为当前updater为发起咨询的销售
			if (consultReply.getReportConsultReplyStatus() != null && consultReply.getReportConsultReply() != null &&
					consultReply.getReportConsultReplyStatus() == 1 && consultReply.getReportConsultReply() == 0){
				consultReply.setUpdater(null);
				quoteorderMapper.updateConsultResultOfQuoteorderGoods(consultReply);
			}
		}
		//保存咨询内容
		QuoteorderConsult quoteorderConsult = new QuoteorderConsult();
		quoteorderConsult.setQuoteorderId(quoteorderId);
		quoteorderConsult.setCreator(userId);
		quoteorderConsult.setAddTime(DateUtil.sysTimeMillis());
		quoteorderConsult.setType(1);
		quoteorderConsult.setContent(contentBuilder.substring(0,contentBuilder.length()-5));
		quoteorderConsultMapper.insert(quoteorderConsult);

		if (CollectionUtils.isNotEmpty(quoteorderConsultDetailList)){
			Integer quoteorderConsultId = quoteorderConsult.getQuoteorderConsultId();
			quoteorderConsultDetailList.forEach(item->{
				item.setQuoteorderConsultId(quoteorderConsultId);
			});
			quoteorderConsultDetailMapper.batchInsert(quoteorderConsultDetailList);
		}

	}



	private String getReferencePriceOfSku(String sku, Integer traderId, Integer customerNature){
		String referencePriceDesc = "";
		List<PriceInfoResponseDto> priceInfoResponseDtoList = basePriceService.batchFindPriceInfo(Collections.singletonList(sku),traderId);
		if (priceInfoResponseDtoList==null||priceInfoResponseDtoList.isEmpty()){
			return referencePriceDesc;
		}
		PriceInfoResponseDto priceInfo = priceInfoResponseDtoList.get(0);
		if (priceInfo.getPriceType() == 1){
			referencePriceDesc = priceInfo.getContractPrice() + "（合约价）";
		} else if (priceInfo.getPriceType() == 2){
			if (customerNature == 465){
				referencePriceDesc = priceInfo.getDistributionPrice() +"（分销价）";
			} else if (customerNature == 466){
				referencePriceDesc = priceInfo.getTerminalPrice() + "（终端价）";
			}
		}
		return referencePriceDesc;
	}


	private void collectQuotedAlarmRecordWhenFirstTrigger(Integer quotedOrderId, QuotedAlarmModeEnum mode, List<Integer> quotedGoodsIdList) {
		if (quotedOrderId == null || mode == null || mode.equals(QuotedAlarmModeEnum.OFF)) {
			return;
		}

		Quoteorder quoteOrderQuery = quoteorderMapper.getQuoteorderById(quotedOrderId);
		if (quoteOrderQuery == null) {
			logger.error("【采集报价预警记录】报价单信不存在，quoteOrderId:{}, mode:{}", quotedOrderId, mode.getMessage());
			return;
		}

		QuotedAlarmRecord quotedAlarmRecordQuery = getQuotedAlarmRecord(quotedOrderId);
		if (quotedAlarmRecordQuery != null && quotedAlarmRecordQuery.getMode().equals(mode.getMode())) {
			return;
		}

		List<QuoteorderConsult> quoteOrderConsults = null;
		if (mode == QuotedAlarmModeEnum.SALESMAN_MODE) {
			//首次咨询时记录报价预警信息--销售端触发
			quoteOrderConsults = quoteorderConsultMapper.listQuotedConsult(quotedOrderId, 3);
		} else if (mode == QuotedAlarmModeEnum.PURCHASER_MODE) {
			//首次咨询时记录报价预警信息--采购端触发
			quoteOrderConsults = quoteorderConsultMapper.listQuotedConsult(quotedOrderId, 1);
		}

		int quotedCount = 0;
		if (quoteOrderConsults!=null && quoteOrderConsults.size() > 1) {
			for (QuoteorderConsult consultRecord : quoteOrderConsults) {
				if (StringUtils.isNotEmpty(consultRecord.getContent())) {
					if(consultRecord.getContent().contains("报价")){
						quotedCount++;
					}
				}
			}
		}

		if (quotedCount > 1) {
			//存在多条关于报价的咨询记录
			return;
		}

		List<QuoteorderGoods> quoteOrderGoodsList = quoteorderMapper.getQuoteorderGoodsByIdsample(quotedOrderId);
		if (CollectionUtils.isEmpty(quoteOrderGoodsList)) {
			logger.error("【采集报价预警记录】报价单信不存在，quoteOrderId:{}, mode:{}", quotedOrderId, mode.getMessage());
			return;
		}

		List<Integer> notDeletedQuotedGoodsIdList = quoteOrderGoodsList.stream().filter(e -> !Objects.equals(e.getIsDelete(), CommonConstants.IS_DELETE_1))
				.map(QuoteorderGoods::getQuoteorderGoodsId).collect(Collectors.toList());
		if (CollectionUtils.isEmpty(notDeletedQuotedGoodsIdList)) {
			return;
		}

		List<Integer> goodsIdListToDelete = new ArrayList<>();
		for (Integer currentQuotedGoodsId : notDeletedQuotedGoodsIdList) {
			if (!hasQuotedGoodsIfTriggerAlarm(quotedOrderId, mode, currentQuotedGoodsId)) {
				goodsIdListToDelete.add(currentQuotedGoodsId);
			}
		}

		Set<Integer> quotedGoodsIdSet = new HashSet<>(notDeletedQuotedGoodsIdList);
		quotedGoodsIdSet.removeAll(goodsIdListToDelete);

		if (quotedGoodsIdSet.isEmpty()) {
			return;
		}

		QuotedAlarmRecord quotedAlarmRecord = new QuotedAlarmRecord();
		quotedAlarmRecord.setQuoteOrderKey(quotedOrderId);
		quotedAlarmRecord.setMode(mode.getMode());
		quotedAlarmRecord.setQuotedGoodsIdList(new ArrayList<>(quotedGoodsIdSet));
		quotedAlarmRecord.setFirstMarkTime(System.currentTimeMillis());

		//保存报价预警记录信息到redis中
		saveQuotedAlarmRecord(quotedOrderId, quotedAlarmRecord);
	}


	/**
	 * 报价回复状态(待回复)
	 */
	private static final Integer WAIT_REPLYING = -1;

	/**
	 * 报价回复状态(未咨询)
	 */
	private static final Integer NOT_DONE = 0;

	/**
	 * 报价回复状态(已回复)
	 */
	private static final Integer REPLIED = 1;

	private boolean hasQuotedGoodsIfTriggerAlarm(Integer quotedOrderId, QuotedAlarmModeEnum mode, Integer quotedGoodsId) {
		if (!ObjectUtils.allNotNull(quotedOrderId, mode, quotedGoodsId)) {
			return false;
		}

		//没有沟通记录
		List<QuoteorderConsultReply> quotedGoodsRecordList = quoteorderConsultReplyMapper.getByQuoteorderIdAndQuoteorderGoods(quotedOrderId, quotedGoodsId);
		if (CollectionUtils.isEmpty(quotedGoodsRecordList)) {
			logger.warn("未查询报价沟通记录 - quoteOrderId:{} , quotedGoodsId:{}",quotedOrderId, quotedGoodsId);
			return false;
		}

		QuoteorderConsultReply lastQuotedConsultReply = quotedGoodsRecordList.get(0);
		if (Objects.isNull(lastQuotedConsultReply.getReferencePriceReplyStatus()) || NOT_DONE.equals(lastQuotedConsultReply.getReferencePriceReplyStatus())) {
			return false;
		}

		if (mode == QuotedAlarmModeEnum.SALESMAN_MODE) {
			//供应链已回复报价排除此商品监控
			if (REPLIED.equals(lastQuotedConsultReply.getReferencePriceReplyStatus())) {
				return false;
			}
		} else if(mode == QuotedAlarmModeEnum.PURCHASER_MODE) {
			//销售端未咨询此商品价格
			if (WAIT_REPLYING.equals(lastQuotedConsultReply.getReferencePriceReplyStatus())) {
				return false;
			}
		} else {
			return false;
		}

		return true;
	}

	private void saveQuotedAlarmRecord(Integer quotedOrderId, QuotedAlarmRecord quotedAlarmRecord) {
		Jedis jedis = JedisUtils.getResource();
		try {
			Map<String, String> keyValuePair = Collections.singletonMap(String.valueOf(quotedOrderId), JSON.toJSONString(quotedAlarmRecord));
			jedis.hmset(QuotedAlarmMonitor.QUOTED_RECORDS_REDIS_KEY, keyValuePair);
			logger.info("保存报预警记录信息成功, record:{}", keyValuePair);
		} catch (Exception e) {
			logger.error("保存报价预警记录信息失败 - quoteOrderKey :{} , record:{}", quotedOrderId, quotedAlarmRecord, e);
		} finally {
			JedisUtils.returnResource(jedis);
		}
	}

	private QuotedAlarmRecord getQuotedAlarmRecord(Integer quotedOrderId) {
		Jedis jedis = JedisUtils.getResource();
		try {
			String valueInJson = jedis.hget(QuotedAlarmMonitor.QUOTED_RECORDS_REDIS_KEY, String.valueOf(quotedOrderId));
			return JSON.parseObject(valueInJson, QuotedAlarmRecord.class);
		} catch (Exception e) {
			logger.error("保存报价预警记录信息失败 - quoteOrderKey :{}", quotedOrderId, e);
		} finally {
			JedisUtils.returnResource(jedis);
		}
		return null;
	}

	@Autowired
	private QuoteLinkBdLogMapper quoteLinkBdLogMapper;

	@Override
	public QuoteLinkBdLog getQuoteLinkBdLog(Integer logId) {
		return quoteLinkBdLogMapper.selectByPrimaryKey(logId);
	}

	@Override
	public QuoteLinkBdLog saveQuoteLinkBdLog(QuoteLinkBdLog log) {
		quoteLinkBdLogMapper.insert(log);
		return log;
	}

	@Autowired
	private QuoteorderGoodsMapper quoteGoodsMapper;
	@Override
	public ResultInfo saveQuoteGoodsReal(QuoteorderGoods quoteGoods) {
		//验证产品是否存在重复
		if (org.apache.commons.lang3.StringUtils.isNotBlank(quoteGoods.getSku())) {
			int j = quoteGoodsMapper.vailQuoteGoods(quoteGoods);
			if (j > 0) {
				return new ResultInfo(-1, "已有相同订货号的产品，不允许重复添加！");
			}
		}
		//报价默认1
		quoteGoods.setCurrencyUnitId(1);
		int i = quoteGoodsMapper.insertQuoteGoods(quoteGoods);
		logger.info("保存新增报价商品信息:{}，SQL返回值:{}", JSON.toJSONString(quoteGoods), i);
		if (i == 1) {
			//单价和数据均大于零时，并且新的产品金额不等于原来的--修改付款方式
			Integer n = quoteGoodsMapper.vailQuoteTotle(quoteGoods);
			logger.info("saveQuoteGoodsReal校验报价单总额:{}", n);
			if (n > 0) {
				double price = quoteGoods.getPrice() == null ? 0.00 : quoteGoods.getPrice().doubleValue();
				if (price > 0 && quoteGoods.getNum() > 0) {
					//修改主表中报价总金额
					int j = quoteGoodsMapper.updateQuoteTotal(quoteGoods);
					logger.info("saveQuoteGoodsReal修改报价单总额:{}", JSON.toJSONString(quoteGoods));
					if (j > 0) {
						return new ResultInfo(0, "操作成功");
					}
				}
			}
			return new ResultInfo(0, "操作成功", quoteGoods);
		}
		return new ResultInfo();
	}

	@Override
	public Integer updateQuoteGoodsReal(QuoteorderGoods goods) {
		Integer i=quoteGoodsMapper.editQuoteGoods(goods);
		quoteGoodsMapper.updateQuoteTotal(goods);
		return i;
	}

    @Override
    public QuoteLinkBdLog getQuoteLinkBdLogByInfo(QuoteLinkBdLog log) {
        return quoteLinkBdLogMapper.getLinkLog(log);
    }

	@Override
	public Map<Integer, Integer> dealWithQuoteOrderGoodsAuthorizationResult(Integer quoteorderId){
        HashMap<Integer, Integer> result = new HashMap<>(16);

        //商品流报价商品
		List<QuoteorderGoods> quoteorderGoodsList = getQuoteorderGoodsByOrderId(quoteorderId);
		List<QuoteorderGoods> skuGoodsList = quoteorderGoodsList.stream()
                .filter(quoteorderGoods -> quoteorderGoods.getIsTemp().equals(0) && ! quoteorderGoods.getGoodsId().equals(0))
                .collect(Collectors.toList());

        //报价单临时商品
		quoteorderGoodsList.stream().filter(quoteorderGoods -> quoteorderGoods.getIsTemp().equals(1) && quoteorderGoods.getGoodsId().equals(0))
                .forEach(item -> {
                    result.put(item.getQuoteorderGoodsId(), 0);
                });

        if (CollectionUtils.isEmpty(skuGoodsList)){
            logger.info("该报价单下无已维护的SKU报价商品 consultRelatedId:{}", quoteorderId);
            return result;
        }

        Quoteorder quoteorder = getQuoteInfoByKey(quoteorderId);
        if (quoteorder == null || quoteorder.getSalesAreaId() == null){
            throw new RuntimeException("报价单基本信息查询异常 consultRelatedId:{}" + quoteorderId);
        }

        boolean unknownAuthorization = (quoteorder.getSalesArea() != null && quoteorder.getSalesArea().equals("未知")) ||
                quoteorder.getSalesAreaId().equals(0) || quoteorder.getTerminalType().equals(1);
        if (unknownAuthorization){
            logger.info("报价单的报备信息选择未知 consultRelatedId:{}", quoteorderId);
            //未知报备情况待回复并且等待报备
            skuGoodsList.stream().forEach(quoteorderGoods -> {
                CoreSkuGenerate skuGenerate = goodsService.getSkuAuthotizationInfoBySku(quoteorderGoods.getGoodsId().longValue());
                if (skuGenerate == null ){
                    logger.warn("报价单终端信息时未知sku信息检索错误需要人工回复 skuId:{}" ,  quoteorderGoods.getGoodsId());
                   result.put(quoteorderGoods.getQuoteorderGoodsId(), 1);
                    return;
                }

                Integer isNeedReply = skuGenerate.getIsNeedReport() == null || skuGenerate.getIsNeedReport().equals(1) ? 1 : 0;
                result.put(quoteorderGoods.getQuoteorderGoodsId(), isNeedReply);
            });
            //报价单未知情况下 处理完人工回复结果直接返回
            return result;
        }

        List<Region> regionList = (List<Region>) regionService.getRegion(quoteorder.getSalesAreaId(), 1);
        Region region = null;
        for (Region regionInfo : regionList) {
            if (regionInfo.getRegionType().equals(1)){
                region = regionInfo;
            }
        }

        for (QuoteorderGoods quoteorderGoods : skuGoodsList) {
            CoreSkuGenerate skuGenerate = goodsService.getSkuAuthotizationInfoBySku(quoteorderGoods.getGoodsId().longValue());
            if (skuGenerate == null){
                throw new RuntimeException("报价单中商品sku不存在或者sku报备信息未维护完整 goodsId:{}" + quoteorderGoods.getGoodsId());
            }

            if (skuGenerate.getIsNeedReport() == null || skuGenerate.getIsNeedReport().equals(0)){
            	result.put(quoteorderGoods.getQuoteorderGoodsId(), skuGenerate.getIsNeedReport() == null ?  1 : 0);
            	continue;
            }

            SkuAuthorizationVo skuAuthorizationVo = skuAuthorizationService.getSkuAuthorizationInfoBySkuId(quoteorderGoods.getGoodsId());
			if (ErpConst.ZERO.equals(skuGenerate.getIsAuthorized()) || Objects.isNull(skuAuthorizationVo) ||
					CollectionUtils.isEmpty(skuAuthorizationVo.getSkuAuthorizationItemVoList())) {
				logger.info("报价单中的sku需要报备或者sku报备信息不全 报价单商品需要报备 goodsId:{}", quoteorderGoods.getGoodsId());
				result.put(quoteorderGoods.getQuoteorderGoodsId(), 1);
				continue;
			}


            boolean checkAuthorization = false;
            for (SkuAuthorizationItemVo skuAuthorizationItemVo : skuAuthorizationVo.getSkuAuthorizationItemVoList()) {
                if (skuAuthorizationItemVo.getRegionIds().contains(region.getRegionId()) &&
                        skuAuthorizationItemVo.getTerminalTypeIds().contains(quoteorder.getTerminalType())){
                    checkAuthorization = true;
                    break;
                }
            }
            Integer isNeedReply = checkAuthorization ? 0 : 1;
            logger.info("报价商品报备结果 goodsId:{},isNeedReply:{}",quoteorderGoods.getQuoteorderGoodsId(),isNeedReply);
            result.put(quoteorderGoods.getQuoteorderGoodsId(), isNeedReply);
        }

	    return result;
	}

	@Override
	public Integer saveIsNeedReply(Integer quoteorderGoodsId, Integer isNeedReply) {
		if (quoteorderGoodsId == null || isNeedReply == null){
			return 0;
		}
		return quoteorderMapper.saveIsNeedReply(quoteorderGoodsId, isNeedReply);
	}

	@Override
	public Integer getSysOptionDefIdBypt(Integer parentId,String optionType){
		SysOptionDefinition sysOptionDefinition;
		List<SysOptionDefinition> sysOptionDefinitionListByParentId = getSysOptionDefinitionListByParentId(parentId);
		sysOptionDefinition = sysOptionDefinitionListByParentId.stream().filter(item -> optionType.equals(item.getOptionType())).findFirst().orElse(null);
		return sysOptionDefinition != null ? sysOptionDefinition.getSysOptionDefinitionId():0;
	}
	@Override
	public List<Integer> getAffectOrderBySkuId(Long skuId){
		return quoteorderMapper.getPriceChangeAffectQuoteorderBySkuId(skuId);
	}

	@Override
	public QuoteLinkBdLog getQuoteLinkBdLogByQuoteId(Integer quoteId) {
		return quoteLinkBdLogMapper.getLinkLogByQuoteId(quoteId);
	}

	@Override
	public List<QuoteorderGoods> getQuoteGoodsByQuoteId(Integer quoteorderId) {
		return quoteorderMapper.getQuoteorderGoodsByIdsample(quoteorderId);
	}

	@Override
	public void updateOnlineSharetime(Integer quoteorderId) {
		quoteorderMapper.updateOnlineSharetime(quoteorderId,DateUtil.getStringDateNow2());
	}

	@Override
	public QuoteorderVo getQuoteOrderInfoById2(Integer quoteorderId) {
		return quoteorderMapper.getQuoteInfoById(quoteorderId);
	}

	@Override
	public List<ActivitiTaskUnDoEntity> selectTimeoutTaskForNotice(){
		return authorizationMapper.selectTimeoutTaskForNotice();
	}

	@Override
	public void sendWeixinNoticeForActiviti(Long authorizationApplyId, String businessKey, String taskId, String userName) {
		try{
			AuthorizationApply authorizationApply = getAuthorizationApplyByKeyId(authorizationApplyId.intValue());
			String message = new String(NOTICE_MESSAGE_TEMPLATE_FOR_ACTIVITI);
			//String lendOutNum = wmsOutputOrder.getOrderNo();//外借单号
			message = message.replace("{applyNum}",authorizationApply.getAuthorizationApplyNum());
			message = message.replace("{skuName}",authorizationApply.getSkuName());

			String targetUrl = URLEncoder.encode("/order/quote/authorizationExamine.do?authorizationApplyId="+authorizationApplyId.intValue()+"&title=授权书详情","UTF-8");
			String jumpUrl = erp_url+"index.do?target="+targetUrl;
			message = message.replace("{jumpUrl}",jumpUrl);
			List<User> userList =  userService.getUserByUserIds(Arrays.asList(userName));


			ActivitiNoticeInfoEntity activitiNoticeInfoEntity = new ActivitiNoticeInfoEntity();
			activitiNoticeInfoEntity.setTaskId(taskId);
			activitiNoticeInfoEntity.setBusinessKey(businessKey);
			activitiNoticeInfoEntity.setIsClick(0);
			activitiNoticeInfoEntity.setClickTime(null);
			activitiNoticeInfoEntity.setAcceptUserName(userName);
			activitiNoticeInfoEntity.setAcceptUserId(userList.stream().map(User::getUserId).collect(Collectors.toList()).get(0));
			activitiNoticeInfoEntity.setNoticeTime(new Date());
			activitiNoticeService.saveActivitiTaskNoticeInfo(activitiNoticeInfoEntity);

			BatchMessageSendDto batchMessageSendDto = new BatchMessageSendDto();
			batchMessageSendDto.setUserIdList(userList.stream().map(User::getUserId).collect(Collectors.toList()));
			batchMessageSendDto.setMsgContent(message);
			RestfulResult result = uacWxUserInfoApiService.sendBatchMsg(batchMessageSendDto);
			if("success".equals(result.getCode())){
				return;
			}else{
				log.warn("外借单推送消息可能含有失败的内容:{}", com.alibaba.fastjson.JSONObject.toJSONString(result));
			}
		}catch (Exception e){
			log.error("外借单审批提醒失败",e);
		}
	}

	private void saveChangeLog(ChangeRelatedTableEnums tQuoteorderGoods, QuoteorderGoods newData, QuoteorderGoods oldData, Integer creator) {
		StringBuilder logMessage = new StringBuilder();
		if(newData != null && oldData != null){
			String key = getChangeKey(oldData);
			if(!newData.getGoodsName().equals(oldData.getGoodsName())){
				logMessage.append("编辑: ").append(key).append(" 产品名称").
						append(" 原值: ").append(oldData.getGoodsName()).append(" 新值: ").append(newData.getGoodsName()).append("</br> ");
			}
			if (newData.getPrice().compareTo(oldData.getPrice()) != 0){
				logMessage.append("编辑: ").append(key).append(" 报价").
						append(" 原值: ").append(oldData.getPrice()).append(" 新值: ").append(newData.getPrice()).append("</br> ");
			}
			if(!newData.getNum().equals(oldData.getNum())){
				logMessage.append("编辑: ").append(key).append(" 数量").
						append(" 原值: ").append(oldData.getNum()).append(" 新值: ").append(newData.getNum()).append("</br> ");
			}

		} else if(newData == null ){
			String key = getChangeKey(oldData);
			logMessage.append("删除: ").append(key).append("</br> ");
		}else if(oldData == null){
			String key = getChangeKey(newData);
			logMessage.append("新增: ").append(key).append("</br> ");
		}

		changeLogApiService.insertSelective(tQuoteorderGoods,logMessage.toString(),creator);
	}

	private  String getChangeKey(QuoteorderGoods oldData) {
		String key = oldData.getSku();
		if(StringUtils.isBlank(key)){
			key = oldData.getGoodsName();
		}
		return key;
	}
}
