package com.vedeng.goods.model;

import java.util.Date;

public class SkuAttrMappingGenerate {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_SKU_ATTR_MAPPING.SKU_ATTR_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer skuAttrId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_SKU_ATTR_MAPPING.SKU_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer skuId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_SKU_ATTR_MAPPING.BASE_ATTRIBUTE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer baseAttributeId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_SKU_ATTR_MAPPING.STATUS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_SKU_ATTR_MAPPING.UPDATER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer updater;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_SKU_ATTR_MAPPING.CREATOR
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_SKU_ATTR_MAPPING.ADD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Date addTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_SKU_ATTR_MAPPING.MOD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Date modTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_SKU_ATTR_MAPPING.BASE_ATTRIBUTE_VALUE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer baseAttributeValueId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_SKU_ATTR_MAPPING.SKU_ATTR_ID
     *
     * @return the value of V_SKU_ATTR_MAPPING.SKU_ATTR_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getSkuAttrId() {
        return skuAttrId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_SKU_ATTR_MAPPING.SKU_ATTR_ID
     *
     * @param skuAttrId the value for V_SKU_ATTR_MAPPING.SKU_ATTR_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setSkuAttrId(Integer skuAttrId) {
        this.skuAttrId = skuAttrId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_SKU_ATTR_MAPPING.SKU_ID
     *
     * @return the value of V_SKU_ATTR_MAPPING.SKU_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getSkuId() {
        return skuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_SKU_ATTR_MAPPING.SKU_ID
     *
     * @param skuId the value for V_SKU_ATTR_MAPPING.SKU_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_SKU_ATTR_MAPPING.BASE_ATTRIBUTE_ID
     *
     * @return the value of V_SKU_ATTR_MAPPING.BASE_ATTRIBUTE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getBaseAttributeId() {
        return baseAttributeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_SKU_ATTR_MAPPING.BASE_ATTRIBUTE_ID
     *
     * @param baseAttributeId the value for V_SKU_ATTR_MAPPING.BASE_ATTRIBUTE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setBaseAttributeId(Integer baseAttributeId) {
        this.baseAttributeId = baseAttributeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_SKU_ATTR_MAPPING.STATUS
     *
     * @return the value of V_SKU_ATTR_MAPPING.STATUS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_SKU_ATTR_MAPPING.STATUS
     *
     * @param status the value for V_SKU_ATTR_MAPPING.STATUS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_SKU_ATTR_MAPPING.UPDATER
     *
     * @return the value of V_SKU_ATTR_MAPPING.UPDATER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getUpdater() {
        return updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_SKU_ATTR_MAPPING.UPDATER
     *
     * @param updater the value for V_SKU_ATTR_MAPPING.UPDATER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_SKU_ATTR_MAPPING.CREATOR
     *
     * @return the value of V_SKU_ATTR_MAPPING.CREATOR
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_SKU_ATTR_MAPPING.CREATOR
     *
     * @param creator the value for V_SKU_ATTR_MAPPING.CREATOR
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_SKU_ATTR_MAPPING.ADD_TIME
     *
     * @return the value of V_SKU_ATTR_MAPPING.ADD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Date getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_SKU_ATTR_MAPPING.ADD_TIME
     *
     * @param addTime the value for V_SKU_ATTR_MAPPING.ADD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_SKU_ATTR_MAPPING.MOD_TIME
     *
     * @return the value of V_SKU_ATTR_MAPPING.MOD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Date getModTime() {
        return modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_SKU_ATTR_MAPPING.MOD_TIME
     *
     * @param modTime the value for V_SKU_ATTR_MAPPING.MOD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setModTime(Date modTime) {
        this.modTime = modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_SKU_ATTR_MAPPING.BASE_ATTRIBUTE_VALUE_ID
     *
     * @return the value of V_SKU_ATTR_MAPPING.BASE_ATTRIBUTE_VALUE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getBaseAttributeValueId() {
        return baseAttributeValueId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_SKU_ATTR_MAPPING.BASE_ATTRIBUTE_VALUE_ID
     *
     * @param baseAttributeValueId the value for V_SKU_ATTR_MAPPING.BASE_ATTRIBUTE_VALUE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setBaseAttributeValueId(Integer baseAttributeValueId) {
        this.baseAttributeValueId = baseAttributeValueId;
    }
}