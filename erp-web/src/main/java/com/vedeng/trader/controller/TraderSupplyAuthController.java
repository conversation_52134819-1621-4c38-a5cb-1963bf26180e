package com.vedeng.trader.controller;

import com.alibaba.fastjson.JSON;
import com.vedeng.authorization.model.User;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.erp.trader.domain.SkuSupplyAuthBo;
import com.vedeng.erp.trader.service.TraderSupplyAuthService;
import com.vedeng.goods.dao.BrandGenerateMapper;
import com.vedeng.goods.model.BrandGenerate;
import com.vedeng.goods.model.BrandGenerateExample;
import com.vedeng.goods.service.BrandService;
import com.vedeng.trader.dao.TraderSupplierMapper;
import com.vedeng.trader.model.TraderSupplier;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 供应商授权书
 *
 * <AUTHOR>
 * @date 2022/6/13 9:22
 **/
@Controller
@RequestMapping("/trader/supply")
@Slf4j
public class TraderSupplyAuthController extends BaseController {

    @Autowired
    private TraderSupplyAuthService traderSupplyAuthService;

    @Resource
    private TraderSupplierMapper traderSupplierMapper;

    @Resource
    BrandService brandService;

    /**
     * 新增授权书页面
     * @param traderSupplierId
     * @param request
     * @return
     * @see <a href="http://jira.ivedeng.com/browse/VDERP-11243">新增授权书页面</a>
     */
    @RequestMapping("/auth")
    public ModelAndView addAuthView(Integer traderSupplierId,HttpServletRequest request) {

        if (Objects.isNull(traderSupplierId)) {
            log.error("入参缺失 供应商id不可为空");
            throw new ServiceException("供应商id不可为空");
        }
        ModelAndView mv = new ModelAndView("trader/supplier/add_auth_view");

        // 品牌数据
        List<BrandGenerate> brandGenerates =brandService.getBrandInfoByParam();
        if (!CollectionUtils.isEmpty(brandGenerates)) {
            List<Map<String, Object>> collect = brandGenerates.stream().map(item -> {
                Map<String, Object> brand = new HashMap<>();
                brand.put("name", item.getBrandName());
                brand.put("value", item.getBrandId());
                return brand;
            }).collect(Collectors.toList());
            mv.addObject("brands", JSON.toJSONString(collect));
        }
        TraderSupplier traderSupplier = traderSupplierMapper.selectByPrimaryKey(traderSupplierId);
        mv.addObject("traderSupplier", traderSupplier);

        return mv;
    }


    /**
     * 授权书保存接口
     * @param skuSupplyAuthBo
     * @param request
     * @return
     * @see <a href="http://jira.ivedeng.com/browse/VDERP-11243">新增授权书</a>
     */
    @RequestMapping("/auth/save")
    @ResponseBody
    public ResultInfo addAuthView(@RequestBody SkuSupplyAuthBo skuSupplyAuthBo, HttpServletRequest request) {

        User sessionUser = getSessionUser(request);
        TraderSupplier traderSupplier = traderSupplierMapper.selectByPrimaryKey(skuSupplyAuthBo.getTraderSupplyId());
        if (traderSupplier != null && Objects.equals(0, traderSupplier.getTraderType())) {
            log.info("保存的授权书的供应商类型为0：traderSupplyId:{}",skuSupplyAuthBo.getTraderSupplyId());
        }
        try {
            traderSupplyAuthService.saveAuthData(skuSupplyAuthBo, sessionUser.getUserId());
            return ResultInfo.success();
        } catch (Exception e) {
            log.error("addAuthView 保存数据,异常", e);
            return ResultInfo.error(e.getMessage());
        }

    }
}
