Vue.component('ui-wxuser-select', {
    template: `<div class="vd-ui-wxuser-select">
        <div class="vd-ui-select-trigger" v-if="false" @click="showSelect">
            <div class="vd-ui-select-trigger-txt">{{ selectedItem.name || placeholder }}</div>
            <i class="vd-ui_icon icon-app-right"></i>
        </div>
        <template v-else> 
            <div class="vd-ui-select-trigger multi-select" :style="'--line:' + line" @click="showSelect">
                <div class="vd-ui-select-multi-tags" v-if="selectedItemList.length">
                    <div class="vd-ui-select-multi-tags-inner">
                        <template v-for="(item, index) in selectedItemList">
                            <div class="vd-ui-select-tag tag-avatar" ref="tagItem" :key="index">
                                <template>
                                    <div class="vd-ui-select-tag-avatar">
                                        <img :src="item.avatar || '/mstatic/mui/image/crm-user-avatar.svg'" />
                                    </div>
                                    <div class="vd-ui-select-tag-txt">{{ item.name }}</div>
                                    <i class="vd-ui_icon icon-delete" @click.stop="deleteItem(index)"></i>
                                </template>
                            </div>
                        </template>
                    </div>
                </div>
                <div class="vd-ui-select-trigger-txt placeholder" v-else>{{ placeholder }}</div>
                <i class="vd-ui_icon icon-app-right"></i>
            </div>
        </template>
    </div>`,
    props: {
        value: {
            type: Array,
            default() {
                return [];
            }
        },
        type: {
            type: String,
            default: 'single' // multi:多选
        },
        line: {
            type: Number,
            default: 100 // 回显默认行数
        },
        multi: {
            type: Boolean,
            default: false
        },
        placeholder: {
            type: String,
            default: '请选择'
        },
        defaultItems: {
            type: Array | Object,
        }
    },
    data() {
        return {
            selectedItemList: [],
        };
    },
    watch: {
        defaultItems: {
            handler (newV) {
                if (Object.prototype.toString.call(newV) == '[object Array]') {
                    this.selectedItemList = newV;
                } else if (Object.prototype.toString.call(newV) == '[object Object]' && Object.keys(newV).length) {
                    this.selectedItemList = [newV];
                }
            },
            immediate: true,
            deep: true,
        }
    },
    computed: {

    },
    mounted() {
        
    },
    methods: {
        async showSelect() {
            let res = await ww.selectEnterpriseContact({
                fromDepartmentId: 0,
                mode: this.type,
                type: ['user'],
                selectedUserIds: this.value
            })

            console.log('  =============== value ===> ', this.value)
            console.log(' select res ===> ', res)

            if (res.result && res.result.userList) {
                this.selectedItemList = res.result.userList || [];
                let ids = [];
                this.selectedItemList.forEach(item => {
                    ids.push(item.id)
                })

                this.value = ids;
            }

            this.triggerChange();
        },
        deleteItem(index) {
            this.value.splice(index, 1);
            this.selectedItemList.splice(index, 1);
            this.triggerChange();
        },
        triggerChange() {
            this.$emit('input', this.value);
            this.$emit('change', {
                value: this.value,
                list: this.selectedItemList
            });
        }
    }
})