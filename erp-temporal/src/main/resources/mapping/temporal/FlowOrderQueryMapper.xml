<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.temporal.mapper.FlowOrderQueryMapper">

    <resultMap id="FlowOrderDtoMap" type="com.vedeng.temporal.domain.dto.FlowOrderDto">
        <id column="FLOW_ORDER_ID" property="flowOrderId"/>
        <result column="FLOW_ORDER_NO" property="flowOrderNo"/>
        <result column="BASE_ORDER_ID" property="baseOrderId"/>
        <result column="BASE_ORDER_NO" property="baseOrderNo"/>
        <result column="BASE_BUSINESS_TYPE" property="baseBusinessType"/>
        <result column="AUDIT_STATUS" property="auditStatus"/>
        <result column="AUDIT_USER_ID" property="auditUserId"/>
        <result column="AUDIT_USERNAME" property="auditUsername"/>
        <result column="AUDIT_TIME" property="auditTime"/>
        <result column="IS_DELETE" property="isDelete"/>
        <result column="CREATOR" property="creator"/>
        <result column="UPDATER" property="updater"/>
        <result column="CREATOR_NAME" property="creatorName"/>
        <result column="UPDATER_NAME" property="updaterName"/>
        <result column="ADD_TIME" property="addTime"/>
        <result column="MOD_TIME" property="modTime"/>
        <result column="CONTRACT_STATUS" property="contractStatus"/>
        <result column="PUSH_DIRECTION" property="pushDirection"/>
        <result column="SOURCE_ERP" property="sourceErp"/>
    </resultMap>

    <select id="selectByFlowOrderId" resultMap="FlowOrderDtoMap">
        SELECT *
        FROM T_FLOW_ORDER
        WHERE FLOW_ORDER_ID = #{flowOrderId}
          AND IS_DELETE = 0
        LIMIT 1
    </select>
</mapper> 