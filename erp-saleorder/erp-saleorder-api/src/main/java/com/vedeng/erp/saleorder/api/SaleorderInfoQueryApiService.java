package com.vedeng.erp.saleorder.api;

import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;

import java.util.List;

public interface SaleorderInfoQueryApiService {
    /**
     * @param saleorderNo
     * @return
     * <AUTHOR>
     * @desc 根据销售单编号查询销售信息
     */
    SaleorderInfoDto queryInfoByNo(String saleorderNo);

    /**
     * 根据客户id 查询当前客户是否有过成功的订单交易
     *
     * @param traderId 客户id
     * @return boolean true 有 false 无
     */
    boolean queryTraderHaveOrder(Integer traderId);

    /**
     * 获取某个sku符合条件的十个销售单信息 用以计算公允价
     *
     * @param goodsId skuId
     * @return List<SaleorderInfoDto>
     */
    List<SaleorderInfoDto> getSkuFairValueInfo(Integer goodsId);

    SaleorderInfoDto queryInfoByJdOrderNo(String jdOrderNo);

}
