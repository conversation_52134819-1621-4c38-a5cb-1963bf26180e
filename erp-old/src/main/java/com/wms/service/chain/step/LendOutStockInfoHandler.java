package com.wms.service.chain.step;

import com.alibaba.fastjson.JSON;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.order.model.Buyorder;
import com.vedeng.stock.api.stock.dto.StockInfoDto;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.wms.constant.LogicalEnum;
import com.wms.dao.WmsOutputOrderMapper;
import com.wms.dto.PutSaleOrderGoodsDto;
import com.wms.dto.StockAllocationResult;
import com.wms.dto.StockCalculateDto;
import com.wms.model.po.WmsOutputOrder;
import com.wms.service.chain.AbstractHandlerStep;
import com.wms.service.chain.HandlerStepContext;
import com.wms.service.stockcalculate.StockinfoCaculateInterface;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 外接单出库
 */
@Service
public class LendOutStockInfoHandler extends AbstractHandlerStep {

    private static final Logger LOGGER = LoggerFactory.getLogger(LendOutStockInfoHandler.class);

    @Autowired
    @Qualifier("purchaseExgCaculateImpl")
    private StockinfoCaculateInterface stockinfoCaculateInterface;

    @Autowired
    private WmsOutputOrderMapper outputOrderMapper;

    @Autowired
    private WarehouseStockService warehouseStockService;

    @Override
    protected void doDealWith(HandlerStepContext context) throws Exception {

        List<StockAllocationResult> allocationResultList = context.get("allocationResultList");

        Long lendOutOrderId = context.get("lendOutOrderId");

        WmsOutputOrder wmsOutputOrder = outputOrderMapper.selectByPrimaryKey(lendOutOrderId);

        //更新库存服务
        StockInfoDto stockInfoDto  = new StockInfoDto();
        stockInfoDto.setRelatedNo(wmsOutputOrder.getOrderNo());

        List<StockCalculateDto> stockCalculateList  = new ArrayList<>();

        allocationResultList.stream().forEach(allocationResult -> {
            StockCalculateDto stockCalculateDto = new StockCalculateDto();
            stockCalculateDto.setSku(allocationResult.getSku());
            stockCalculateDto.setLogicalWarehouseId(allocationResult.getLogicalWarehouseId());
            stockCalculateDto.setOccupyNum(allocationResult.getNum());
            stockCalculateList.add(stockCalculateDto);
        });

        //如果没有库存 直接报错
        if(CollectionUtils.isEmpty(stockCalculateList)){
            throw new Exception("商品库存不足，分配库存失败");
        }

        LOGGER.info("外借单:"+wmsOutputOrder.getOrderNo()+",更新库存服务数据=====================:" + JSON.toJSONString(stockInfoDto));

        stockInfoDto.setWarehouseStockList(stockinfoCaculateInterface.calculateStockInfo(stockCalculateList));
        warehouseStockService.updateStockInfo(stockInfoDto);

    }
}
