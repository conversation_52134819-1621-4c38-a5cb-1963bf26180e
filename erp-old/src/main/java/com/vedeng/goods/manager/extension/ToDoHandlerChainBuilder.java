package com.vedeng.goods.manager.extension;

import com.vedeng.goods.manager.constants.GoodsValidConstants;
import com.vedeng.goods.manager.extension.handler.GoodsTodoHandler;
import com.vedeng.goods.manager.rule.GoodsExtValidElement;
import com.vedeng.todolist.service.ITodoInstance;
import org.springframework.aop.support.AopUtils;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class ToDoHandlerChainBuilder {

    private List<ITodoInstance> todoInstances = new LinkedList<>();

    private List<GoodsTodoHandler> goodsTodoHandlers = new LinkedList<>();

    private List<GoodsExtValidElement> goodsExtValidElements;

    private boolean failFast;

    private ToDoHandlerChainBuilder() {
    }

    public static ToDoHandlerChainBuilder newBuilder() {
        return new ToDoHandlerChainBuilder();
    }

    public ToDoHandlerChainBuilder addTodoInstance(List<ITodoInstance> todoInstances) {
        this.todoInstances.addAll(todoInstances);
        return this;
    }

    public ToDoHandlerChainBuilder addGoodsTodoHandlers(List<GoodsTodoHandler> goodsTodoHandlers) {
        this.goodsTodoHandlers.addAll(goodsTodoHandlers);
        return this;
    }

    public ToDoHandlerChainBuilder setGoodsExtValidElements(List<GoodsExtValidElement> goodsExtValidElements) {
        this.goodsExtValidElements = goodsExtValidElements;
        return this;
    }

    public ToDoHandlerChainBuilder setFailFast(boolean failFast) {
        this.failFast = failFast;
        return this;
    }

    public GoodsToDoHandlerChain build() {
        GoodsToDoHandlerChain goodsToDoHandlerChain = new GoodsToDoHandlerChain();
        if (todoInstances == null) {
            throw new IllegalStateException();
        }

        if (goodsExtValidElements == null || goodsExtValidElements.isEmpty()) {
            throw new IllegalStateException();
        }

        goodsToDoHandlerChain.setGoodsExtValidElements(goodsExtValidElements);

        if (failFast) {
            goodsToDoHandlerChain.setFailFast(failFast);
        }
        for (GoodsTodoHandler goodsTodoHandler : goodsTodoHandlers) {
            GoodsExtValidModuleEnum goodsExtValidModuleEnum = GoodsExtValidModuleEnum.getByServiceId(goodsTodoHandler.getServiceId());
            if (goodsExtValidModuleEnum == null || goodsExtValidModuleEnum.getStatus().equals(GoodsValidConstants.DISABLE)) {
                continue;
            }

            for (ITodoInstance todoInstance : todoInstances) {
                Class<?> targetClass = AopUtils.getTargetClass(todoInstance);
                if (targetClass.isInstance(goodsTodoHandler.getITodoInstance())) {
                    goodsToDoHandlerChain.addHandler(goodsTodoHandler);
                }
            }
        }

        return goodsToDoHandlerChain;
    }
}
