package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.KingDeeOutPutFeeSpecialInvoiceEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface KingDeeOutPutFeeSpecialInvoiceMapper {
    /**
     * delete by primary key
     * @param specialInvoicId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer specialInvoicId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(KingDeeOutPutFeeSpecialInvoiceEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(KingDeeOutPutFeeSpecialInvoiceEntity record);

    /**
     * select by primary key
     * @param specialInvoicId primary key
     * @return object by primary key
     */
    KingDeeOutPutFeeSpecialInvoiceEntity selectByPrimaryKey(Integer specialInvoicId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(KingDeeOutPutFeeSpecialInvoiceEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(KingDeeOutPutFeeSpecialInvoiceEntity record);

    int updateBatchSelective(List<KingDeeOutPutFeeSpecialInvoiceEntity> list);

    int batchInsert(@Param("list") List<KingDeeOutPutFeeSpecialInvoiceEntity> list);
}