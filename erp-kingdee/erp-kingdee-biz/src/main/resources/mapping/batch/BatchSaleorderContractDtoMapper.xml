<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchSaleorderContractDtoMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.domain.entity.KingDeeSaleorderContractEntity">
        <!--@mbg.generated-->
        <!--@Table KING_DEE_SALEORDER_CONTRACT-->
        <id column="KING_DEE_SALEORDER_CONTRACT_ID" jdbcType="INTEGER" property="kingDeeSaleorderContractId" />
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
        <result column="CREATOR" jdbcType="INTEGER" property="creator" />
        <result column="UPDATER" jdbcType="INTEGER" property="updater" />
        <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
        <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
        <result column="F_ID" jdbcType="INTEGER" property="fId" />
        <result column="F_QZOK_OrgId" jdbcType="VARCHAR" property="fQzokOrgid" />
        <result column="F_QZOK_HTH" jdbcType="VARCHAR" property="fQzokHth" />
        <result column="F_QZOK_HTRQ" jdbcType="VARCHAR" property="fQzokHtrq" />
        <result column="F_QZOK_HTJE" jdbcType="VARCHAR" property="fQzokHtje" />
        <result column="F_QZOK_SLL" jdbcType="VARCHAR" property="fQzokSll" />
        <result column="F_QZOK_DDH" jdbcType="VARCHAR" property="fQzokDdh" />
        <result column="FBillNo" jdbcType="VARCHAR" property="FBillNo" />
    </resultMap>
    <sql id="Base_Column_List">
    <!--@mbg.generated-->
    KING_DEE_SALEORDER_CONTRACT_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME,
    UPDATER_NAME, F_ID, F_QZOK_OrgId, F_QZOK_HTH, F_QZOK_HTRQ, F_QZOK_HTJE, F_QZOK_SLL,
    F_QZOK_DDH,FBillNo
    </sql>

    <select id="querySaleorderContract" resultType="com.vedeng.erp.kingdee.batch.dto.BatchSaleorderContractDto">
        select A.SALEORDER_ID,
        A.SALEORDER_NO,
        A.INVOICE_TYPE,
        A.TOTAL_AMOUNT,
        A.CONTRACT_VERIFY_STATUS,
        CONCAT(ta.DOMAIN, ta.URI) as url,
        sod.COMMENTS              as rate,
        A.VALID_TIME,
        fd.ID as dataId,
        IF(LOCATE('.', ta.NAME) = 0, CONCAT(ta.NAME, '.pdf'), ta.NAME) AS NAME
        from (select ts.SALEORDER_ID,
        ts.SALEORDER_NO,
        ts.INVOICE_TYPE,
        ts.TOTAL_AMOUNT,
        tsd.CONTRACT_VERIFY_STATUS,
        ts.VALID_TIME,
        kdsc.F_QZOK_DDH as kingdeeDDH
        from T_SALEORDER_DATA tsd
        left join T_SALEORDER ts on ts.SALEORDER_ID = tsd.SALEORDER_ID
        left join KING_DEE_SALEORDER_CONTRACT kdsc on ts.SALEORDER_NO = kdsc.F_QZOK_DDH
        where tsd.CONTRACT_VERIFY_STATUS = 1
        <if test="validTimeBegin == null and validTimeEnd == null" >
            and ts.VALID_TIME >= 1609430400000
        </if>
        <if test="validTimeBegin != null" >
            and ts.VALID_TIME <![CDATA[>=]]> #{validTimeBegin,jdbcType=BIGINT}
        </if>
        <if test="validTimeEnd != null" >
            and ts.VALID_TIME <![CDATA[<=]]> #{validTimeEnd,jdbcType=BIGINT}
        </if>
        ) A
        left join T_ATTACHMENT ta
        on A.SALEORDER_ID = ta.RELATED_ID and ta.ATTACHMENT_FUNCTION = 492 and ta.IS_DELETED = 0
        left join KING_DEE_FILE_DATA fd on CONCAT(#{ossPre,jdbcType=VARCHAR},ta.DOMAIN, ta.URI) = fd.URL and
        fd.FORM_ID = 'QZOK_BDXSDD'
        left join T_SYS_OPTION_DEFINITION sod on A.INVOICE_TYPE = sod.SYS_OPTION_DEFINITION_ID
        where ta.URI is not null
        and fd.ID is null
        group by A.SALEORDER_ID
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="querySaleorderContractFile" resultType="com.vedeng.erp.kingdee.batch.dto.BatchSaleorderContractDto">
        select A.SALEORDER_ID,
        A.SALEORDER_NO,
        A.INVOICE_TYPE,
        A.TOTAL_AMOUNT,
        A.CONTRACT_VERIFY_STATUS,
        CONCAT(ta.DOMAIN, ta.URI) as url,
        sod.COMMENTS              as rate,
        A.VALID_TIME,
        fd.ID as dataId,
        ta.ATTACHMENT_ID,
        IF(LOCATE('.', ta.NAME) = 0, CONCAT(ta.NAME, '.pdf'), ta.NAME) AS NAME
        from (select ts.SALEORDER_ID,
        ts.SALEORDER_NO,
        ts.INVOICE_TYPE,
        ts.TOTAL_AMOUNT,
        tsd.CONTRACT_VERIFY_STATUS,
        ts.VALID_TIME
        from T_SALEORDER_DATA tsd
        left join T_SALEORDER ts on ts.SALEORDER_ID = tsd.SALEORDER_ID
        where tsd.CONTRACT_VERIFY_STATUS = 1
        <if test="validTimeBegin == null and validTimeEnd == null" >
            and ts.VALID_TIME >= 1609430400000
        </if>
        <if test="validTimeBegin != null" >
            and ts.VALID_TIME <![CDATA[>=]]> #{validTimeBegin,jdbcType=BIGINT}
        </if>
        <if test="validTimeEnd != null" >
            and ts.VALID_TIME <![CDATA[<=]]> #{validTimeEnd,jdbcType=BIGINT}
        </if>
        ) A
        left join T_ATTACHMENT ta
        on A.SALEORDER_ID = ta.RELATED_ID and ta.ATTACHMENT_FUNCTION = 492 and ta.IS_DELETED = 0
        left join KING_DEE_FILE_DATA fd on CONCAT(#{ossPre,jdbcType=VARCHAR},ta.DOMAIN, ta.URI) = fd.URL and
        fd.FORM_ID = 'QZOK_BDXSDD'
        left join T_SYS_OPTION_DEFINITION sod on A.INVOICE_TYPE = sod.SYS_OPTION_DEFINITION_ID
        where ta.URI is not null
        and fd.ID is null
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="queryConfirmation" resultType="com.vedeng.erp.kingdee.batch.dto.BatchSaleorderContractDto">
        select ts.SALEORDER_ID,
               ts.SALEORDER_NO,
               ts.TOTAL_AMOUNT,
               ts.VALID_TIME,
               tsod.COMMENTS as rate
        from T_CONFIRMATION_FORM_RECODE tcfr
                 left join T_SALEORDER ts on tcfr.SALEORDER_ID = ts.SALEORDER_ID and ts.CONFIRMATION_FORM_AUDIT = 2
                 left join KING_DEE_SALEORDER_CONTRACT kdsc on kdsc.F_QZOK_DDH = ts.SALEORDER_NO
                 left join T_SYS_OPTION_DEFINITION tsod on ts.INVOICE_TYPE = tsod.SYS_OPTION_DEFINITION_ID
        <where>
            <if test="validTimeBegin == null and validTimeEnd == null" >
                and ts.VALID_TIME >= 1609430400000
            </if>
            <if test="validTimeBegin != null" >
                and ts.VALID_TIME <![CDATA[>=]]> #{validTimeBegin,jdbcType=BIGINT}
            </if>
            <if test="validTimeEnd != null" >
                and ts.VALID_TIME <![CDATA[<=]]> #{validTimeEnd,jdbcType=BIGINT}
            </if>
            and tcfr.IS_ENABLE = 0
            and kdsc.KING_DEE_SALEORDER_CONTRACT_ID is null
        </where>
        group by ts.SALEORDER_ID
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="onlineQueryConfirmation" resultType="com.vedeng.erp.kingdee.batch.dto.BatchSaleorderContractDto">
        select ts.SALEORDER_ID,
        ts.SALEORDER_NO,
        ts.TOTAL_AMOUNT,
        ts.VALID_TIME,
        tsod.COMMENTS as rate
        from T_EXPRESS_ONLINE_RECCEIPT_REORD TEORR
        left join T_SALEORDER ts on TEORR.ORDER_ID = ts.SALEORDER_ID and ts.CONFIRMATION_FORM_AUDIT = 2
        left join KING_DEE_SALEORDER_CONTRACT kdsc on kdsc.F_QZOK_DDH = ts.SALEORDER_NO
        left join T_SYS_OPTION_DEFINITION tsod on ts.INVOICE_TYPE = tsod.SYS_OPTION_DEFINITION_ID
        <where>
            <if test="validTimeBegin == null and validTimeEnd == null" >
                and ts.VALID_TIME >= 1609430400000
            </if>
            <if test="validTimeBegin != null" >
                and ts.VALID_TIME <![CDATA[>=]]> #{validTimeBegin,jdbcType=BIGINT}
            </if>
            <if test="validTimeEnd != null" >
                and ts.VALID_TIME <![CDATA[<=]]> #{validTimeEnd,jdbcType=BIGINT}
            </if>
            and TEORR.IS_ENABLE = 1
            and kdsc.KING_DEE_SALEORDER_CONTRACT_ID is null
        </where>
        group by ts.SALEORDER_ID
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="queryConfirmationFile" resultType="com.vedeng.erp.kingdee.batch.dto.BatchSaleorderContractDto">
        select kdsc.F_ID as dataId,
               ta.URI    as url,
               ts.SALEORDER_NO,
               ta.ATTACHMENT_ID,
               tcfr.CONFIRMATION_NAME,
               ta.SUFFIX
        from T_CONFIRMATION_FORM_RECODE tcfr
                 left join T_SALEORDER ts on tcfr.SALEORDER_ID = ts.SALEORDER_ID and ts.CONFIRMATION_FORM_AUDIT = 2
                 left join T_ATTACHMENT ta
                           on tcfr.ID = ta.RELATED_ID and ta.ATTACHMENT_TYPE = 980 and ta.ATTACHMENT_FUNCTION = 1020 and
                              ta.IS_DELETED = 0
                 left join KING_DEE_SALEORDER_CONTRACT kdsc on ts.SALEORDER_NO = kdsc.F_QZOK_DDH
                 left join KING_DEE_FILE_DATA fd on fd.URL = ta.URI and fd.FORM_ID='QZOK_BDXSDD'
        where tcfr.IS_ENABLE = 0
          and kdsc.F_QZOK_DDH is not null
          and fd.ID is null
          and ta.ATTACHMENT_ID is not null
        <if test="validTimeBegin == null and validTimeEnd == null" >
            and ts.VALID_TIME >= 1609430400000
        </if>
        <if test="validTimeBegin != null" >
            and ts.VALID_TIME <![CDATA[>=]]> #{validTimeBegin,jdbcType=BIGINT}
        </if>
        <if test="validTimeEnd != null" >
            and ts.VALID_TIME <![CDATA[<=]]> #{validTimeEnd,jdbcType=BIGINT}
        </if>
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="queryOnlineConfirmationFile" resultType="com.vedeng.erp.kingdee.batch.dto.BatchSaleorderContractDto">

        select kdsc.F_ID as dataId,
        ta.URI    as url,
        ts.SALEORDER_NO,
        ta.ATTACHMENT_ID,
        ta.NAME confirmationName,
        ta.SUFFIX
        from T_EXPRESS_ONLINE_RECCEIPT_REORD TEORR
        left join T_SALEORDER ts on TEORR.ORDER_ID = ts.SALEORDER_ID and ts.CONFIRMATION_FORM_AUDIT = 2
        left join T_ATTACHMENT ta
        on TEORR.EXPRESS_ONLINE_RECCEIPT_REORD_ID = ta.RELATED_ID and ta.ATTACHMENT_TYPE = 980 and ta.ATTACHMENT_FUNCTION = 1021 and
        ta.IS_DELETED = 0
        left join KING_DEE_SALEORDER_CONTRACT kdsc on ts.SALEORDER_NO = kdsc.F_QZOK_DDH
        left join KING_DEE_FILE_DATA fd on fd.URL = ta.URI and fd.FORM_ID='QZOK_BDXSDD'
        where TEORR.IS_ENABLE = 1
        and kdsc.F_QZOK_DDH is not null
        and fd.ID is null
        and ta.ATTACHMENT_ID is not null
        <if test="validTimeBegin == null and validTimeEnd == null" >
            and ts.VALID_TIME >= 1609430400000
        </if>
        <if test="validTimeBegin != null" >
            and ts.VALID_TIME <![CDATA[>=]]> #{validTimeBegin,jdbcType=BIGINT}
        </if>
        <if test="validTimeEnd != null" >
            and ts.VALID_TIME <![CDATA[<=]]> #{validTimeEnd,jdbcType=BIGINT}
        </if>
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>


</mapper>