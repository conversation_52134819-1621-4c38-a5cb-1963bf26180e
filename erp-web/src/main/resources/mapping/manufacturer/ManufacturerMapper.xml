<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.goods.manufacturer.dao.ManufacturerMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.goods.manufacturer.model.Manufacturer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Mar 11 13:56:59 CST 2021.
    -->
    <id column="MANUFACTURER_ID" property="manufacturerId" jdbcType="INTEGER" />
    <result column="MANUFACTURER_NAME" property="manufacturerName" jdbcType="VARCHAR" />
    <result column="IS_DELETE" property="isDelete" jdbcType="INTEGER" />
    <result column="ADD_TIME" property="addTime" jdbcType="TIMESTAMP" />
    <result column="ADD_NO" property="addNo" jdbcType="INTEGER" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_NO" property="updateNo" jdbcType="INTEGER" />
    <result column="IS_UPLOAD" property="isUpload" jdbcType="INTEGER" />
    <result column="BC_ISSUE_DATE" property="bcIssueDate" jdbcType="BIGINT" />
    <result column="BC_START_TIME" property="bcStartTime" jdbcType="BIGINT" />
    <result column="BC_END_TIME" property="bcEndTime" jdbcType="BIGINT" />
    <result column="PE_START_TIME" property="peStartTime" jdbcType="BIGINT" />
    <result column="PE_END_TIME" property="peEndTime" jdbcType="BIGINT" />
    <result column="PEP_START_TIME" property="pepStartTime" jdbcType="BIGINT" />
    <result column="PEP_END_TIME" property="pepEndTime" jdbcType="BIGINT" />
    <result column="PRODUCT_COMPANY_LICENCE" property="productCompanyLicence" jdbcType="VARCHAR" />
    <result column="RC_START_TIME" property="rcStartTime" jdbcType="BIGINT" />
    <result column="RC_END_TIME" property="rcEndTime" jdbcType="BIGINT" />
    <result column="RECORD_CERTIFICATE_LICENCE" property="recordCertificateLicence" jdbcType="VARCHAR" />

    <result column="ASSIGNMENT_MANAGER_ID" jdbcType="INTEGER" property="assignmentManagerId" />
    <result column="ASSIGNMENT_ASSISTANT_ID" jdbcType="INTEGER" property="assignmentAssistantId" />
    <result column="USERNAME" jdbcType="INTEGER" property="userName" />
    <result column="STATUS" jdbcType="TINYINT" property="status" />
    <result column="SIGNATURE" jdbcType="TINYINT" property="signature" />
    <result column="RELATE_ID" jdbcType="INTEGER" property="relateId" />

    <result column="ATTACHMENT_FUNCTION" jdbcType="INTEGER" property="attachmentFunction" />




  </resultMap>
  <sql id="Example_Where_Clause" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Mar 11 13:56:59 CST 2021.
    -->
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Mar 11 13:56:59 CST 2021.
    -->
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Mar 11 13:56:59 CST 2021.
    -->
    MANUFACTURER_ID, MANUFACTURER_NAME, IS_DELETE, ADD_TIME, ADD_NO, UPDATE_TIME, UPDATE_NO,
    IS_UPLOAD, BC_ISSUE_DATE, BC_START_TIME, BC_END_TIME, PE_START_TIME, PE_END_TIME,
    PEP_START_TIME, PEP_END_TIME, PRODUCT_COMPANY_LICENCE,STATUS,SIGNATURE,RELATE_ID,RC_START_TIME,RC_END_TIME,RECORD_CERTIFICATE_LICENCE
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.vedeng.goods.manufacturer.model.ManufacturerExample" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Mar 11 13:56:59 CST 2021.
    -->
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from T_MANUFACTURER
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Mar 11 13:56:59 CST 2021.
    -->
    select
    <include refid="Base_Column_List" />
    from T_MANUFACTURER
    where MANUFACTURER_ID = #{manufacturerId,jdbcType=INTEGER}
    AND IS_DELETE=0
  </select>
  <insert id="insertSelective" parameterType="com.vedeng.goods.manufacturer.model.Manufacturer" useGeneratedKeys="true" keyProperty="manufacturerId" keyColumn="MANUFACTURER_ID" >
    insert into T_MANUFACTURER
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="manufacturerName != null" >
        MANUFACTURER_NAME,
      </if>
      <if test="isDelete != null" >
        IS_DELETE,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="addNo != null" >
        ADD_NO,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>
      <if test="updateNo != null" >
        UPDATE_NO,
      </if>
      <if test="isUpload != null" >
        IS_UPLOAD,
      </if>
      <if test="bcIssueDate != null" >
        BC_ISSUE_DATE,
      </if>
      <if test="bcStartTime != null" >
        BC_START_TIME,
      </if>
      <if test="bcEndTime != null" >
        BC_END_TIME,
      </if>
      <if test="peStartTime != null" >
        PE_START_TIME,
      </if>
      <if test="peEndTime != null" >
        PE_END_TIME,
      </if>
      <if test="pepStartTime != null" >
        PEP_START_TIME,
      </if>
      <if test="pepEndTime != null" >
        PEP_END_TIME,
      </if>
      <if test="productCompanyLicence != null" >
        PRODUCT_COMPANY_LICENCE,
      </if>
      <if test="status != null">
        STATUS,
      </if>
      <if test="relateId != null">
        RELATE_ID,
      </if>
      <if test="rcStartTime != null">
        RC_START_TIME,
      </if>
      <if test="rcEndTime != null">
        RC_END_TIME,
      </if>
      <if test="recordCertificateLicence != null">
        RECORD_CERTIFICATE_LICENCE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="manufacturerName != null" >
        #{manufacturerName,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null" >
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addNo != null" >
        #{addNo,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateNo != null" >
        #{updateNo,jdbcType=INTEGER},
      </if>
      <if test="isUpload != null" >
        #{isUpload,jdbcType=INTEGER},
      </if>
      <if test="bcIssueDate != null" >
        #{bcIssueDate,jdbcType=DATE},
      </if>
      <if test="bcStartTime != null" >
        #{bcStartTime,jdbcType=DATE},
      </if>
      <if test="bcEndTime != null" >
        #{bcEndTime,jdbcType=DATE},
      </if>
      <if test="peStartTime != null" >
        #{peStartTime,jdbcType=DATE},
      </if>
      <if test="peEndTime != null" >
        #{peEndTime,jdbcType=DATE},
      </if>
      <if test="pepStartTime != null" >
        #{pepStartTime,jdbcType=DATE},
      </if>
      <if test="pepEndTime != null" >
        #{pepEndTime,jdbcType=DATE},
      </if>
      <if test="productCompanyLicence != null" >
        #{productCompanyLicence,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=TINYINT},
      </if>
      <if test="relateId != null">
        #{relateId,jdbcType=INTEGER},
      </if>
      <if test="rcStartTime != null">
        #{rcStartTime,jdbcType=DATE},
      </if>
      <if test="rcEndTime != null">
        #{rcEndTime,jdbcType=DATE},
      </if>
      <if test="recordCertificateLicence != null">
        #{recordCertificateLicence,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelectiveTimeChange" parameterType="com.vedeng.goods.manufacturer.model.Manufacturer">
    update T_MANUFACTURER
    <set >
      <if test="manufacturerName != null" >
        MANUFACTURER_NAME = #{manufacturerName,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null" >
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addNo != null" >
        ADD_NO = #{addNo,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateNo != null" >
        UPDATE_NO = #{updateNo,jdbcType=INTEGER},
      </if>
      <if test="isUpload != null" >
        IS_UPLOAD = #{isUpload,jdbcType=INTEGER},
      </if>
        BC_ISSUE_DATE = #{bcIssueDate,jdbcType=DATE},
        BC_START_TIME = #{bcStartTime,jdbcType=DATE},
        BC_END_TIME = #{bcEndTime,jdbcType=DATE},
        PE_START_TIME = #{peStartTime,jdbcType=DATE},
        PE_END_TIME = #{peEndTime,jdbcType=DATE},
        PEP_START_TIME = #{pepStartTime,jdbcType=DATE},
        PEP_END_TIME = #{pepEndTime,jdbcType=DATE},
        RC_START_TIME = #{rcStartTime,jdbcType=DATE},
        RC_END_TIME = #{rcEndTime,jdbcType=DATE},
      <if test="productCompanyLicence != null" >
        PRODUCT_COMPANY_LICENCE = #{productCompanyLicence,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        STATUS = #{status,jdbcType=TINYINT},
      </if>
      <if test="signature != null">
        SIGNATURE = #{signature,jdbcType=TINYINT},
      </if>
      <if test="recordCertificateLicence != null">
        RECORD_CERTIFICATE_LICENCE = #{recordCertificateLicence,jdbcType=VARCHAR},
      </if>
    </set>
    where MANUFACTURER_ID = #{manufacturerId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.manufacturer.model.Manufacturer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Mar 11 13:56:59 CST 2021.
    -->
    update T_MANUFACTURER
    <set >
      <if test="manufacturerName != null" >
        MANUFACTURER_NAME = #{manufacturerName,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null" >
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addNo != null" >
        ADD_NO = #{addNo,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateNo != null" >
        UPDATE_NO = #{updateNo,jdbcType=INTEGER},
      </if>
      <if test="isUpload != null" >
        IS_UPLOAD = #{isUpload,jdbcType=INTEGER},
      </if>
      <if test="bcIssueDate != null" >
        BC_ISSUE_DATE = #{bcIssueDate,jdbcType=DATE},
      </if>
      <if test="bcStartTime != null" >
        BC_START_TIME = #{bcStartTime,jdbcType=DATE},
      </if>
      <if test="bcEndTime != null" >
        BC_END_TIME = #{bcEndTime,jdbcType=DATE},
      </if>
      <if test="peStartTime != null" >
        PE_START_TIME = #{peStartTime,jdbcType=DATE},
      </if>
      <if test="peEndTime != null" >
        PE_END_TIME = #{peEndTime,jdbcType=DATE},
      </if>
      <if test="pepStartTime != null" >
        PEP_START_TIME = #{pepStartTime,jdbcType=DATE},
      </if>
      <if test="pepEndTime != null" >
        PEP_END_TIME = #{pepEndTime,jdbcType=DATE},
      </if>
      <if test="productCompanyLicence != null" >
        PRODUCT_COMPANY_LICENCE = #{productCompanyLicence,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        STATUS = #{status,jdbcType=TINYINT},
      </if>
      <if test="signature != null">
        SIGNATURE = #{signature,jdbcType=TINYINT},
      </if>
      <if test="rcStartTime != null">
        RC_START_TIME = #{rcStartTime,jdbcType=DATE},
      </if>
      <if test="rcEndTime != null">
        RC_END_TIME = #{rcEndTime,jdbcType=DATE},
      </if>
      <if test="recordCertificateLicence != null">
        RECORD_CERTIFICATE_LICENCE = #{recordCertificateLicence,jdbcType=VARCHAR},
      </if>
    </set>
    where MANUFACTURER_ID = #{manufacturerId,jdbcType=INTEGER}
  </update>

  <select id="findUserName" resultType="com.vedeng.authorization.model.User">
SELECT * FROM (
    SELECT  U.USERNAME,U.USER_ID   from T_MANUFACTURER A
                                LEFT JOIN T_REGISTRATION_NUMBER TRN
                                          on A.MANUFACTURER_ID = TRN.MANUFACTURER_ID
                                LEFT JOIN T_FIRST_ENGAGE TFE
                                          on TRN.REGISTRATION_NUMBER_ID = TFE.REGISTRATION_NUMBER_ID
                                LEFT JOIN  V_CORE_SPU P
                                           ON P.FIRST_ENGAGE_ID=TFE.FIRST_ENGAGE_ID
                                LEFT JOIN T_USER U ON P.ASSIGNMENT_MANAGER_ID=U.USER_ID
                                LEFT JOIN T_USER U2 ON P.ASSIGNMENT_ASSISTANT_ID=U2.USER_ID
    where U.IS_DISABLED=0 and A.IS_DELETE=0
    union
    SELECT  U2.USERNAME,U2.USER_ID    from T_MANUFACTURER A
                                 LEFT JOIN T_REGISTRATION_NUMBER TRN
                                           on A.MANUFACTURER_ID = TRN.MANUFACTURER_ID
                                 LEFT JOIN T_FIRST_ENGAGE TFE
                                           on TRN.REGISTRATION_NUMBER_ID = TFE.REGISTRATION_NUMBER_ID
                                 LEFT JOIN  V_CORE_SPU P
                                            ON P.FIRST_ENGAGE_ID=TFE.FIRST_ENGAGE_ID
                                 LEFT JOIN T_USER U ON P.ASSIGNMENT_MANAGER_ID=U.USER_ID
                                 LEFT JOIN T_USER U2 ON P.ASSIGNMENT_ASSISTANT_ID=U2.USER_ID
    where U2.IS_DISABLED=0 and A.IS_DELETE=0
              ) A GROUP BY   A.USER_ID ORDER BY A.USERNAME ASC
    </select>

  <select id="getProductCompanyInfoListPage" resultMap="BaseResultMap">
    select
    A.MANUFACTURER_ID,
    A.MANUFACTURER_NAME,
    A.RC_START_TIME,
    A.RC_END_TIME,
    A.RECORD_CERTIFICATE_LICENCE,
    SUBSTRING_INDEX(group_concat( U.USERNAME order by U.USERNAME asc ),',',1) as USERNAME,
    P.ASSIGNMENT_MANAGER_ID,
    P.ASSIGNMENT_ASSISTANT_ID,
    A.IS_UPLOAD,
    A.STATUS
    from T_MANUFACTURER A
    LEFT JOIN T_REGISTRATION_NUMBER TRN
    on A.MANUFACTURER_ID = TRN.MANUFACTURER_ID
    LEFT JOIN T_FIRST_ENGAGE TFE
    on TRN.REGISTRATION_NUMBER_ID = TFE.REGISTRATION_NUMBER_ID
    LEFT JOIN  V_CORE_SPU P
    ON P.FIRST_ENGAGE_ID=TFE.FIRST_ENGAGE_ID
    LEFT JOIN T_USER U ON P.ASSIGNMENT_MANAGER_ID=U.USER_ID
    or P.ASSIGNMENT_ASSISTANT_ID=U.USER_ID

    WHERE
    1 = 1 AND A.IS_DELETE=0
    <if test="null != manufacturerName and manufacturerName != '' ">
      AND (A.MANUFACTURER_NAME like CONCAT('%',#{manufacturerName,jdbcType=VARCHAR},'%'))
    </if>

    <if test="null != userId and userId != '' and manufacturer.assignmentManagerId!='-1'">
      AND ( U.USER_ID=#{userId})
    </if>

    <if test=" manufacturer.isUpload !=null ">
      AND A.IS_UPLOAD=#{manufacturer.isUpload,jdbcType=INTEGER}
    </if>
    <if test="null != manufacturer.status and manufacturer.status != -1">
      AND A.STATUS=#{manufacturer.status,jdbcType=TINYINT}
    </if>

    <if test="manufacturer.assignmentManagerId !=null and manufacturer.assignmentManagerId!='-1'">
      AND (P.ASSIGNMENT_MANAGER_ID=#{manufacturer.assignmentManagerId}
      OR P.ASSIGNMENT_ASSISTANT_ID=#{manufacturer.assignmentManagerId})
    </if>
    GROUP BY A.MANUFACTURER_ID
  </select>

  <select id="getManufacturerVoInfoByStr" resultType="java.lang.String">
    select a.MANUFACTURER_NAME
    from T_MANUFACTURER a
    where a.IS_DELETE = 0
    <if test="manufacturerName != null">
      and a.MANUFACTURER_NAME = #{manufacturerName,jdbcType=VARCHAR}
    </if>
    <if test="manufacturerId != null and manufacturerId > 0">
      and a.MANUFACTURER_ID <![CDATA[ != ]]> #{manufacturerId, jdbcType=INTEGER}
    </if>
    LIMIT 1000
  </select>

  <insert id="insertAttachement" parameterType="com.vedeng.system.model.Attachment" useGeneratedKeys="true" keyProperty="attachmentId">
    insert into T_ATTACHMENT
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="attachmentId != null" >
        ATTACHMENT_ID,
      </if>
      <if test="attachmentType != null" >
        ATTACHMENT_TYPE,
      </if>
      <if test="attachmentFunction != null" >
        ATTACHMENT_FUNCTION,
      </if>
      <if test="relatedId != null" >
        RELATED_ID,
      </if>
      <if test="name != null" >
        NAME,
      </if>
      <if test="domain != null" >
        DOMAIN,
      </if>
      <if test="uri != null" >
        URI,
      </if>
      <if test="alt != null" >
        ALT,
      </if>
      <if test="sort != null" >
        SORT,
      </if>
      <if test="isDefault != null" >
        IS_DEFAULT,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="ossResourceId != null" >
        OSS_RESOURCE_ID,
      </if>
      <if test="originalFilepath != null" >
        ORIGINAL_FILEPATH,
      </if>
      <if test="isDeleted != null" >
        IS_DELETED,
      </if>
      <if test="suffix != null" >
        SUFFIX,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="attachmentId != null" >
        #{attachmentId,jdbcType=INTEGER},
      </if>
      <if test="attachmentType != null" >
        #{attachmentType,jdbcType=INTEGER},
      </if>
      <if test="attachmentFunction != null" >
        #{attachmentFunction,jdbcType=INTEGER},
      </if>
      <if test="relatedId != null" >
        #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="domain != null" >
        #{domain,jdbcType=VARCHAR},
      </if>
      <if test="uri != null" >
        #{uri,jdbcType=VARCHAR},
      </if>
      <if test="alt != null" >
        #{alt,jdbcType=VARCHAR},
      </if>
      <if test="sort != null" >
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="isDefault != null" >
        #{isDefault,jdbcType=BIT},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="ossResourceId != null" >
        #{ossResourceId,jdbcType=VARCHAR},
      </if>
      <if test="originalFilepath != null" >
        #{originalFilepath,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null" >
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="suffix != null" >
        #{suffix,jdbcType=VARCHAR}
      </if>
    </trim>
  </insert>

  <select id="IsUpLoad" parameterType="com.vedeng.goods.manufacturer.model.Manufacturer" resultMap="BaseResultMap" >
    select
    distinct ATTACHMENT_FUNCTION
    from T_ATTACHMENT
    where ATTACHMENT_TYPE = #{attachmentType,jdbcType=INTEGER}
    <if test="manufacturerId != null and manufacturerId != ''">
      and RELATED_ID = #{manufacturerId,jdbcType=INTEGER}
    </if>
    and IS_DELETED =0
    and ATTACHMENT_FUNCTION IN
    <foreach collection="attachmentFunction" item="attachment" open="(" close=")" separator=",">
      #{attachment,jdbcType=INTEGER}
    </foreach>
  </select>

  <update id="updateIsUpLoad" parameterType="com.vedeng.goods.manufacturer.model.Manufacturer" >

    update T_MANUFACTURER
      <set >
        <if test="isUpload != null" >
          IS_UPLOAD = #{isUpload,jdbcType=INTEGER},
        </if>

      </set>
    where MANUFACTURER_ID = #{manufacturerId,jdbcType=INTEGER}
    and IS_DELETE=0
  </update>
  <select id="findIsNeedDel" resultType="com.vedeng.goods.manufacturer.model.Manufacturer">
        select MANUFACTURER_ID from T_REGISTRATION_NUMBER
         WHERE MANUFACTURER_ID =#{manufacturerId,jdbcType=INTEGER}
  </select>
  <select id="isValidByRegistrationNumberId" resultType="java.lang.Integer">
    select COUNT(1) FROM T_REGISTRATION_NUMBER RN
    INNER JOIN T_MANUFACTURER M ON RN.MANUFACTURER_ID = M.MANUFACTURER_ID AND M.IS_DELETE = 0
    WHERE RN.REGISTRATION_NUMBER_ID = #{registrationNumberId,jdbcType=INTEGER } AND M.STATUS = #{status,jdbcType=INTEGER}
  </select>

  <update id="deleteManufacturer" parameterType="map">
        UPDATE T_MANUFACTURER set
         IS_DELETE=#{isDelete},
        UPDATE_TIME=#{manufacturer.updateTime,jdbcType=DATE},
        UPDATE_NO =#{userId}
        where MANUFACTURER_ID=#{manufacturerId,jdbcType=INTEGER}
  </update>

  <select id="getOneByName" resultType="com.vedeng.goods.manufacturer.model.Manufacturer">
    select MANUFACTURER_ID,
       MANUFACTURER_NAME,
       IS_DELETE,
       ADD_TIME,
       ADD_NO,
       UPDATE_TIME,
       UPDATE_NO,
       IS_UPLOAD,
       BC_ISSUE_DATE,
       BC_START_TIME,
       BC_END_TIME,
       PE_START_TIME,
       PE_END_TIME,
       PEP_START_TIME,
       PEP_END_TIME,
       PRODUCT_COMPANY_LICENCE,
       RC_START_TIME,
       RC_END_TIME,
       RELATE_ID,
       RECORD_CERTIFICATE_LICENCE,
       STATUS,
       SIGNATURE
  from T_MANUFACTURER
  where MANUFACTURER_NAME = #{productNameToUse,jdbcType=VARCHAR}
  limit 1
  </select>

<!--auto generated by MybatisCodeHelper on 2022-05-31-->
  <select id="getByRelateId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_MANUFACTURER
        where RELATE_ID=#{relateId,jdbcType=INTEGER}
    </select>

  <!--auto generated by MybatisCodeHelper on 2022-05-31-->
  <update id="updateByManufacturerId">
    update T_MANUFACTURER
    <set>
      RELATE_ID = #{relateId,jdbcType=INTEGER},
    </set>
    where MANUFACTURER_ID = #{manufacturerId,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2022-06-01-->
  <select id="getByManufacturerNameIn" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_MANUFACTURER
    where MANUFACTURER_NAME in
    <foreach item="item" index="index" collection="manufacturerNameCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
  </select>


    <update id="updateBatchNameSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update T_MANUFACTURER
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="RELATE_ID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.relateId != null">
                        when MANUFACTURER_ID = #{item.manufacturerId,jdbcType=INTEGER}
                            then #{item.relateId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
        </trim>
        where MANUFACTURER_ID in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.manufacturerId,jdbcType=INTEGER}
        </foreach>
    </update>

</mapper>