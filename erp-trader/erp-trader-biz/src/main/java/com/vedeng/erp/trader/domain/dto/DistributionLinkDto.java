package com.vedeng.erp.trader.domain.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 经销链路Dto
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/13 9:58
 */
@Getter
@Setter
public class DistributionLinkDto {

    /**
     * erp traderId
     */
    private Integer traderId;

    /**
     * 客户类型
     */
    private Integer customerNature;

    /**
     * erp traderName
     */
    private String traderName;

    /**
     * 合作次数
     */
    private Integer cooperationCount;

    /**
     * 最后合作时间
     */
    private String lastCooperationTime;

    /**
     * 链路来源
     * 全部:0
     * 中标数据:1
     * 贝登交易:2
     * 商机:3
     * 报价:4
     */
    private Integer linkSourceType;

    /**
     * 合作时间范围
     * 1近一年 2近两年 3近三年
     */
    private Integer cooperationTimeFrame;

    /**
     * 最近合作时间开始
     */
    private Date lastCooperationTimeStart;

    /**
     * 最近合作时间结束
     */
    private Date lastCooperationTimeEnd;

    /**
     * 最近中标时间开始
     */
    private Date lastBiddingTimeStart;

    /**
     * 最近中标时间结束
     */
    private Date lastBiddingTimeEnd;

    /**
     * 最近交易时间开始
     */
    private Date lastSaleTimeStart;

    /**
     * 最近交易时间结束
     */
    private Date lastSaleTimeEnd;

    /**
     * 排序的字段
     * <p>
     * 1:中标次数
     * <p>
     * 2:交易次数
     * <p>
     * 3:商机次数
     * <p>
     * 4:报价次数
     * <p>
     * 5:最近中标时间
     * <p>
     * 6:最近交易时间
     */
    private Integer sortColumn;

    /**
     * 0 升序 1降序 (默认升序)
     */
    private Integer sortType;

    /**
     * 中标次数
     */
    private Integer biddingCount;

    /**
     * 交易次数
     */
    private Integer saleCount;

    /**
     * 商机次数
     */
    private Integer businessChanceCount;

    /**
     * 报价次数
     */
    private Integer quoteCount;

    /**
     * 最近中标时间
     */
    private String lastBiddingTime;

    /**
     * 最近交易时间
     */
    private String lastSaleTime;

    /**
     * 搜索名称
     */
    private String searchName;
}