package com.vedeng.erp.kingdee.batch.writer;

import cn.hutool.json.JSONUtil;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.batch.dto.BatchPayExpensesDto;
import com.vedeng.erp.kingdee.dto.KingDeePayExpensesDto;
import com.vedeng.erp.kingdee.service.KingDeePayExpensesApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
/**
 * @Author: ckt
 * @Date: 2023/6/5
 * @Description: 销售售后录票（蓝字有效）费用应付单，推送金蝶
 *
 */

@Service
@Slf4j
public class SaleOrderAfterSalePayExpenseWriter extends BaseWriter<BatchPayExpensesDto> {

    @Autowired
    KingDeePayExpensesApiService kingDeePayExpensesApiService;

    @Override
    public void doWrite(BatchPayExpensesDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("销售售后录票(蓝字有效),费用应付单推送金蝶,BatchPayExpensesDto:{}", JSONUtil.toJsonStr(dto));
        KingDeePayExpensesDto kingDeePayExpensesDto = dto.getKingDeePayExpensesDto();
        kingDeePayExpensesDto.setKingDeeBizEnums(KingDeeBizEnums.savePayExpenses);
        kingDeePayExpensesApiService.register(kingDeePayExpensesDto,true);
    }
}
