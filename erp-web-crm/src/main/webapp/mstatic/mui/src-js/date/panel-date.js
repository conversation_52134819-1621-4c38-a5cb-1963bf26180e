// 日期面板
Vue.component('ui-date-panel', {
    template: `
        <div class="vd-ui-panel">
            <!-- 头部控制器 -->
            <div class="vd-ui-panel-header">
                <ul>
                    <li 
                        label="前一年"
                        class="arrow arrow-left1"
                        @click="prevYear">
                        <i class="vd-ui_icon icon-slide-up"></i>
                    </li>

                    <li label="上个月"
                        class="arrow"
                        v-show="currentView === 'date'"
                        @click="prevMonth">
                        <i class="vd-ui_icon icon-app-left"></i>
                    </li>

                    <li class="year-month">
                        <span
                            label="年份"
                            class="choose-year"
                            @click="showYearPicker"
                        >
                            {{ yearLabel }}
                        </span>

                        <span
                            label="月份"
                            v-show="currentView === 'date'"
                            class="choose-month"
                            @click="showMonthPicker"
                        >
                            {{ month + 1 }}月
                        </span>
                    </li>

                    <li 
                        label="下个月"
                        class="arrow"
                        v-show="currentView === 'date'"
                        @click="nextMonth">
                        <i class="vd-ui_icon icon-app-right"></i>
                    </li>

                    <li 
                        label="后一年"
                        class="arrow arrow-right1"
                        @click="nextYear">
                        <i class="vd-ui_icon icon-slide-up"></i>
                    </li>
                </ul>
            </div>

            <!-- table面板 -->
            <div class="vd-ui-panel-body">
                <ui-date-table
                    v-show="currentView == 'date'"
                    @pick="handleDatePick"
                    :selection-mode="selectionMode"
                    :firstDayOfWeek="firstDayOfWeek"
                    :value="value"
                    :defaultValue="defaultValue ? new Date(defaultValue) : null"
                    :date="date"
                    :cell-class-name="cellClassName"
                    :disabled-date="disabledDate"
                    :max="max"
                    :min="min"
                ></ui-date-table>
                
                <ui-year-table
                    v-show="currentView === 'year'"
                    @pick="handleYearPick"
                    :value="value"
                    :default-value="defaultValue ? new Date(defaultValue) : null"
                    :date="date"
                    :disabled-date="disabledDate"
                ></ui-year-table>
                
                <ui-month-table
                    v-show="currentView === 'month'"
                    @pick="handleMonthPick"
                    :value="value"
                    :default-value="defaultValue ? new Date(defaultValue) : null"
                    :date="date"
                    :disabled-date="disabledDate"
                ></ui-month-table>
            </div>

            <div class="date-shortcuts" v-if="shortcuts && shortcuts.length">
                <span 
                    v-for="(item, key) in shortcuts" 
                    :key="key" 
                    class="item-sc"
                    @click="handleShortcutClick(item)"
                >{{ item.text }}</span>
            </div>

        </div>
    `,
    props: {
        type: {
            type: String,
            default: 'date'
        },
        // 此层value接收到的是上层 userInput
        value: {
            type: [Date, String]
        },
        firstDayOfWeek: Number,
        disabledDate: {},
        cellClassName: {},
        rangeState: {
            default() {
                return {
                    endDate: null,
                    selecting: false
                };
            }
        },
        defaultValue: {},
        // week dates range
        selectionMode: {
            type: String,
            default: 'day'
        },
        shortcuts: {
            type: Array,
            default: ()=> {
                return []
            }
        },
        min: '',
        max: '',
    },
    data () {
        return { 
            date: new Date(),
            tableRows: [ [], [], [], [], [], [] ],
            lastRow: null,
            lastColumn: null,
            
            currentView: "date"
        }
    },
    watch: {
        value: {
            handler (val) {
                if (this.selectionMode === 'dates' && this.value) return;

                if (util_date.isDate(val)) {
                    this.date = val;
                }
                else if (typeof(val) == 'string' && util_date.checkToDate(val)) {
                    this.date = new Date(val);
                } else {
                    this.date = this.getDefaultValue();
                }
            },
            immediate: true
        },
        selectionMode (newVal) {
            if (newVal === 'month') {
                if (this.currentView !== 'year' || this.currentView !== 'month') {
                    this.currentView = 'month';
                }
            } else if (newVal === 'dates') {
                this.currentView = 'date';
            }
        }
    },
    computed: {
        year () {
            return this.date.getFullYear();
        },
        month () {
            return this.date.getMonth();
        },

        yearLabel () {
            const yearTranslation = '年';
            if (this.currentView === 'year') {
                const startYear = Math.floor(this.year / 12) * 12;
                if (yearTranslation) {
                    return startYear + ' ' + yearTranslation + ' - ' + (startYear + 11) + ' ' + yearTranslation;
                }
                return startYear + ' - ' + (startYear + 11);
            }
            return this.year + ' ' + yearTranslation;
        },
    },
    mounted () {
        this.resetView(); // 初始化类型
    },
    methods: {
        prevMonth () {
            this.date = util_date.prevMonth(this.date);
        },
        nextMonth () {
            this.date = util_date.nextMonth(this.date);
        },
        prevYear () {
            if (this.currentView === 'year') {
                this.date = util_date.prevYear(this.date, 12);
            } else {
                this.date = util_date.prevYear(this.date);
            }
        },
        nextYear() {
            if (this.currentView === 'year') {
                this.date = util_date.nextYear(this.date, 12);
            } else {
                this.date = util_date.nextYear(this.date);
            }
        },
        showYearPicker() {
            this.currentView = 'year';
        },
        showMonthPicker() {
            this.currentView = 'month';
        },

        // 处理点击日期 emit
        handleDatePick (val) {
            if (this.selectionMode === 'day') {
                this.date = val;
                this.$emit('pick', val);
            }
        },
        // 处理点击年 emit
        handleYearPick (year) {
            if (this.selectionMode === 'year') {
                this.date = util_date.modifyDate(this.date, year, 0, 1);
                this.$emit('pick', this.date);
            } else {
                // 是点击日期头部 弹出的年份选择，应该继续选择月份，而不是emit年份
                this.date = util_date.changeYearMonthAndClampDate(this.date, year, this.month);
                this.currentView = 'month';
            }
        },

        handleMonthPick (month) {
            if (this.selectionMode === 'month') {
                this.date = util_date.modifyDate(this.date, this.year, month, 1);
                this.$emit('pick', this.date);
            } else {
                this.date = util_date.changeYearMonthAndClampDate(this.date, this.year, month);
                this.currentView = 'date';
            }
        },

        getDefaultValue () {
            return this.defaultValue ? new Date(this.defaultValue) : new Date();
        },

        resetView () {
            if (this.selectionMode === 'month') {
                this.currentView = 'month';
            } else if (this.selectionMode === 'year') {
                this.currentView = 'year';
            } else {
                this.currentView = 'date';
            }
        },

        handleShortcutClick (shortcut) {
            if (shortcut.onClick) {
                shortcut.onClick(this);
            }
        },
    }
})