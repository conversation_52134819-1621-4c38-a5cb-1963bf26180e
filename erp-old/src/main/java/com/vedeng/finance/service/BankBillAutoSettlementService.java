package com.vedeng.finance.service;

import com.vedeng.erp.finance.dto.BankBillDto;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/6 10:00
 **/
public interface BankBillAutoSettlementService {


    /**
     * 银行流水
     * 销售单id
     *
     * @param bankBillDto 银行
     * @param saleOrderId 销售单id
     * @param endTime
     */
    void doSettlement(BankBillDto bankBillDto, Integer saleOrderId, Date endTime);


    /**
     * 根据时间范围分页处理 银行流水
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @param loading 是否开启进度
     */
    void doWithPage(Date beginTime, Date endTime,boolean loading);

    Integer checkBankBillSaleOrder(BankBillDto bankBillDto, Date date);

}
