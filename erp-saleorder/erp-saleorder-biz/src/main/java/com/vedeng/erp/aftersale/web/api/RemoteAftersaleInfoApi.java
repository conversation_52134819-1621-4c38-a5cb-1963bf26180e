package com.vedeng.erp.aftersale.web.api;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.erp.aftersale.api.AftersaleInfoApi;
import com.vedeng.erp.aftersale.domain.entity.AfterSaleServiceStandardInfoInstallArea;
import com.vedeng.erp.aftersale.dto.AfterSaleServiceStandardInfoDto;
import com.vedeng.erp.aftersale.mapper.AfterSaleServiceStandardInfoInstallAreaMapper;
import com.vedeng.erp.aftersale.mapper.AfterSaleServiceStandardInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.saleorder.service.impl
 * @Date 2022/1/10 19:52
 */
@Slf4j
@Controller
public class RemoteAftersaleInfoApi implements AftersaleInfoApi {

    @Autowired
    private AfterSaleServiceStandardInfoInstallAreaMapper afterSaleServiceStandardInfoInstallAreaMapper;

    @Autowired
    private AfterSaleServiceStandardInfoMapper afterSaleServiceStandardInfoMapper;

    @Override
    @NoNeedAccessAuthorization
    @ResponseBody
    public AfterSaleServiceStandardInfoDto getEffectAfterSalePolicy(String skuNo) {

        AfterSaleServiceStandardInfoDto afterSaleServiceStandardInfoDto = afterSaleServiceStandardInfoMapper.selectBySkuNo(skuNo);

        //安装区域
        if (Objects.nonNull(afterSaleServiceStandardInfoDto)) {
            AfterSaleServiceStandardInfoInstallArea afterSaleServiceStandardInfoInstallArea = afterSaleServiceStandardInfoInstallAreaMapper.selectByServiceStandardInfoId(afterSaleServiceStandardInfoDto.getServiceStandardInfoId());
            if (Objects.nonNull(afterSaleServiceStandardInfoInstallArea)) {
                afterSaleServiceStandardInfoDto.setProvinceCityJsonvalue(afterSaleServiceStandardInfoInstallArea.getProvinceCityJsonvalue());
            }
        }
        return afterSaleServiceStandardInfoDto;
    }

}
