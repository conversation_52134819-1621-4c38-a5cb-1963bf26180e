package com.rabbitmq;

import com.vedeng.order.model.SaleOrderWarnVo;
import com.vedeng.order.service.UpdateSaleOrderGoodsAgingService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.LinkedList;
import java.util.Queue;
import java.util.UUID;
import java.util.concurrent.*;

/**
 * @Description:  消费待采购订单预警队列
 * @Author:       davis
 * @Date:         2021/4/20 下午2:08
 * @Version:      1.0
 */
@Slf4j
@Component
public class ConsumeSaleOrderQueue {

    private static final Logger LOGGER = LoggerFactory.getLogger(ConsumeSaleOrderQueue.class);

    //普通的业务类
    @Autowired
    UpdateSaleOrderGoodsAgingService updateSaleOrderGoodsAgingService;

    // 线程池维护线程的最少数量
    private final static int CORE_POOL_SIZE = 10;
    // 线程池维护线程的最大数量
    private final static int MAX_POOL_SIZE = 10;
    // 线程池维护线程所允许的空闲时间
    private final static int KEEP_ALIVE_TIME = 0;
    // 线程池所使用的缓冲队列大小
    private final static int WORK_QUEUE_SIZE = 2000;

    // 消息缓冲队列
    Queue<SaleOrderWarnVo> msgQueue = new LinkedList<>();

    //由于超出线程范围和队列容量而使执行被阻塞时所使用的处理程序
    final RejectedExecutionHandler handler = (r, executor) -> {
        LOGGER.info("线程池太忙了处理不了过多任务.........多余的线程将会放入msgQueue");
        //可以新开调度器进行处理这些调度任务，或者把没处理的任务保存到数据库中，然后定时任务继续处理
        msgQueue.add(((PollSaleOrder)r).getSaleOrderWarnVo());
    };

    // 任务线程池
    final ThreadPoolExecutor threadPool = new ThreadPoolExecutor(
            CORE_POOL_SIZE, MAX_POOL_SIZE, KEEP_ALIVE_TIME,
            TimeUnit.SECONDS, new LinkedBlockingQueue<>(), handler);


    // 调度线程池。此线程池支持定时以及周期性执行任务的需求。暂时不用
    final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);


    @PostConstruct
    public void init() {

        //开启待采购订单预警消费队列检查
        new Thread(() -> {
            try {
                while (true){
                    String uuid = UUID.randomUUID().toString().replace("-", "");
                    log.info("开始执行 消费待采购队列任务，uuid”{}", uuid);
                    SaleOrderWarnVo saleOrderWarnVo = SaleOrderWarningQueue.getSaleOrderQueue().consume();
                    LOGGER.info(" 剩余待采购订单总数:{}",  SaleOrderWarningQueue.getSaleOrderQueue().size());
                    threadPool.execute(new PollSaleOrder(updateSaleOrderGoodsAgingService, saleOrderWarnVo));
                    log.info("结束执行 消费待采购队列任务，uuid”{}", uuid);
                }
            } catch (InterruptedException e) {
                LOGGER.error("待采购订单队列消费失败，失败原因为---->",e);
            }
        }).start();
    }


    //lombok
    @Data
    class PollSaleOrder implements Runnable {

        UpdateSaleOrderGoodsAgingService updateSaleOrderGoodsAgingService;

        SaleOrderWarnVo saleOrderWarnVo;

        public PollSaleOrder(UpdateSaleOrderGoodsAgingService updateSaleOrderGoodsAgingService,SaleOrderWarnVo saleOrderWarnVo) {
            this.updateSaleOrderGoodsAgingService = updateSaleOrderGoodsAgingService;
            this.saleOrderWarnVo = saleOrderWarnVo;
        }

        @Override
        public void run() {
            String uuid = UUID.randomUUID().toString().replace("-", "");
            log.info("开始执行 dealSaleOrderGoodsAging 方法，uuid：{}",uuid);
            LOGGER.info(" 正在处理的待采购订单为----->{}" , this.saleOrderWarnVo.getSaleorderNo());
            updateSaleOrderGoodsAgingService.dealSaleOrderGoodsAging(this.saleOrderWarnVo);

            log.info("结束执行 dealSaleOrderGoodsAging 方法，uuid：{}",uuid);
        }
    }

    @PreDestroy
    public void stopThread() throws InterruptedException {

        /**
         * pool.awaitTermination(1, TimeUnit.SECONDS)
         * 会每隔一秒钟检查一次是否执行完毕（状态为 TERMINATED），
         * 当从 while 循环退出时就表明线程池已经完全终止了。
         */
        scheduler.shutdown();
        threadPool.shutdown();
        while (!threadPool.awaitTermination(1, TimeUnit.SECONDS)) {
            LOGGER.info("线程还在执行。。。");
        }
    }
}
