package com.vedeng.logistics.service;

import com.vedeng.common.service.BaseService;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.logistics.model.WarehouseGoodsOperateLogVirtual;
import com.vedeng.order.model.Saleorder;

import java.util.List;

/**
 * 产品出入库日志服务层
 *
 * <AUTHOR>
 */
public interface WarehouseGoodsOperateLogVirtualService extends BaseService {
    /**
     * 保存产品出入库日志信息
     *
     * @param record
     * @return
     */
    int insertSelective(WarehouseGoodsOperateLogVirtual record);
    /**
     * <b>Description:</b><br> 当前订单下的直发出库清单
     * @param saleorder
     * @return
     * @Note
     * <b>Author:</b> thor
     * <br><b>Date:</b> 2017年8月11日 下午2:56:11
     */
    List<WarehouseGoodsOperateLogVirtual> getOutDetil(Saleorder saleorder);
    /**
     * <b>Description:</b><br>
     * @param warehouseGoodsOperateLog 获取出入库日志列表（不分页）
     * @return
     * @Note
     * <b>Author:</b> thor
     * <br><b>Date:</b> 2017年8月18日 上午9:51:57
     */
    List<WarehouseGoodsOperateLogVirtual> getWGOlog(WarehouseGoodsOperateLog warehouseGoodsOperateLog)throws Exception;


    /**
     * 根据批次号模糊查询
     * @param batChNo
     * @return
     */
    List<WarehouseGoodsOperateLogVirtual> getWGOlistByComments(String batChNo);
}
