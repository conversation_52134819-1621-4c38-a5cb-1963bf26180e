<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.erp.saleorder.dao.QuSaleHistMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.erp.saleorder.model.dto.QuSaleHist" >
    <!--          -->
    <id column="QU_SALE_HIS_ID" property="quSaleHisId" jdbcType="INTEGER" />
    <result column="SALEORDER_ID" property="saleorderId" jdbcType="INTEGER" />
    <result column="QUOTEORDER_ID" property="quoteorderId" jdbcType="INTEGER" />
    <result column="SKU" property="sku" jdbcType="VARCHAR" />
    <result column="GOODS_ID" property="goodsId" jdbcType="INTEGER" />
    <result column="QU_NUM" property="quNum" jdbcType="INTEGER" />
    <result column="SALE_NUM" property="saleNum" jdbcType="INTEGER" />
    <result column="PRICE" property="price" jdbcType="DECIMAL" />
    <result column="ADD_TIME" property="addTime" jdbcType="TIMESTAMP" />
    <result column="MODE_TIME" property="modeTime" jdbcType="TIMESTAMP" />
    <result column="IS_DELETE" property="isDelete" jdbcType="BIT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--          -->
    QU_SALE_HIS_ID, SALEORDER_ID, QUOTEORDER_ID, SKU, GOODS_ID, QU_NUM, SALE_NUM, PRICE,
    ADD_TIME, MODE_TIME, IS_DELETE, CREATOR, UPDATER
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--          -->
    select
    <include refid="Base_Column_List" />
    from V_QU_SALE_HIST
    where QU_SALE_HIS_ID = #{quSaleHisId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--          -->
    delete from V_QU_SALE_HIST
    where QU_SALE_HIS_ID = #{quSaleHisId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.erp.saleorder.model.dto.QuSaleHist" >
    <!--          -->
    insert into V_QU_SALE_HIST (QU_SALE_HIS_ID, SALEORDER_ID, QUOTEORDER_ID,
      SKU, GOODS_ID, QU_NUM,
      SALE_NUM, PRICE, ADD_TIME,
      MODE_TIME, IS_DELETE, CREATOR,
      UPDATER)
    values (#{quSaleHisId,jdbcType=INTEGER}, #{saleorderId,jdbcType=INTEGER}, #{quoteorderId,jdbcType=INTEGER},
      #{sku,jdbcType=VARCHAR}, #{goodsId,jdbcType=INTEGER}, #{quNum,jdbcType=INTEGER},
      #{saleNum,jdbcType=INTEGER}, #{price,jdbcType=DECIMAL}, #{addTime,jdbcType=TIMESTAMP},
      #{modeTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=BIT}, #{creator,jdbcType=INTEGER},
      #{updater,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.erp.saleorder.model.dto.QuSaleHist" >
    <!--          -->
    insert into V_QU_SALE_HIST
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="quSaleHisId != null" >
        QU_SALE_HIS_ID,
      </if>
      <if test="saleorderId != null" >
        SALEORDER_ID,
      </if>
      <if test="quoteorderId != null" >
        QUOTEORDER_ID,
      </if>
      <if test="sku != null" >
        SKU,
      </if>
      <if test="goodsId != null" >
        GOODS_ID,
      </if>
      <if test="quNum != null" >
        QU_NUM,
      </if>
      <if test="saleNum != null" >
        SALE_NUM,
      </if>
      <if test="price != null" >
        PRICE,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="modeTime != null" >
        MODE_TIME,
      </if>
      <if test="isDelete != null" >
        IS_DELETE,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="quSaleHisId != null" >
        #{quSaleHisId,jdbcType=INTEGER},
      </if>
      <if test="saleorderId != null" >
        #{saleorderId,jdbcType=INTEGER},
      </if>
      <if test="quoteorderId != null" >
        #{quoteorderId,jdbcType=INTEGER},
      </if>
      <if test="sku != null" >
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="goodsId != null" >
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="quNum != null" >
        #{quNum,jdbcType=INTEGER},
      </if>
      <if test="saleNum != null" >
        #{saleNum,jdbcType=INTEGER},
      </if>
      <if test="price != null" >
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modeTime != null" >
        #{modeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null" >
        #{isDelete,jdbcType=BIT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.saleorder.model.dto.QuSaleHist" >
    <!--          -->
    update V_QU_SALE_HIST
    <set >
      <if test="saleorderId != null" >
        SALEORDER_ID = #{saleorderId,jdbcType=INTEGER},
      </if>
      <if test="quoteorderId != null" >
        QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER},
      </if>
      <if test="sku != null" >
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="goodsId != null" >
        GOODS_ID = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="quNum != null" >
        QU_NUM = #{quNum,jdbcType=INTEGER},
      </if>
      <if test="saleNum != null" >
        SALE_NUM = #{saleNum,jdbcType=INTEGER},
      </if>
      <if test="price != null" >
        PRICE = #{price,jdbcType=DECIMAL},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modeTime != null" >
        MODE_TIME = #{modeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null" >
        IS_DELETE = #{isDelete,jdbcType=BIT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where QU_SALE_HIS_ID = #{quSaleHisId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.saleorder.model.dto.QuSaleHist" >
    <!--          -->
    update V_QU_SALE_HIST
    set SALEORDER_ID = #{saleorderId,jdbcType=INTEGER},
      QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER},
      SKU = #{sku,jdbcType=VARCHAR},
      GOODS_ID = #{goodsId,jdbcType=INTEGER},
      QU_NUM = #{quNum,jdbcType=INTEGER},
      SALE_NUM = #{saleNum,jdbcType=INTEGER},
      PRICE = #{price,jdbcType=DECIMAL},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MODE_TIME = #{modeTime,jdbcType=TIMESTAMP},
      IS_DELETE = #{isDelete,jdbcType=BIT},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER}
    where QU_SALE_HIS_ID = #{quSaleHisId,jdbcType=INTEGER}
  </update>

  <select id="getHistInfoBySaleorderId" resultType="com.vedeng.erp.saleorder.model.dto.QuSaleHist">
    SELECT
      A.*,B.SHOW_NAME SKU_NAME
    FROM
      V_QU_SALE_HIST A
        LEFT JOIN V_CORE_SKU B ON A.SKU = B.SKU_NO
    WHERE A.SALEORDER_ID=#{saleorderId} AND A.IS_DELETE=0
  </select>

</mapper>
