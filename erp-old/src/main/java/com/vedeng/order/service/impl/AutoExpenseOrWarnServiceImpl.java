package com.vedeng.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.erp.aftersale.dto.AfterSaleAuditInfoDto;
import com.vedeng.erp.aftersale.service.AfterSaleAuditInfoApiService;
import com.vedeng.erp.aftersale.service.ExpenseAfterSalesApiService;
import com.vedeng.erp.aftersale.service.ExpenseAfterSalesAutoApiService;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseDto;
import com.vedeng.erp.buyorder.service.BuyorderExpenseAutoApiService;
import com.vedeng.order.service.AutoExpenseOrWarnService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2023/2/2 16:30
 **/
@Service
@Slf4j
public class AutoExpenseOrWarnServiceImpl implements AutoExpenseOrWarnService {

    @Autowired
    private  AfterSaleAuditInfoApiService afterSaleAuditInfoApiService;

    @Autowired
    private  ExpenseAfterSalesAutoApiService expenseAfterSalesAutoApiService;

    @Autowired
    private  BuyorderExpenseAutoApiService expenseAutoApiService;

    @Autowired
    private ExpenseAfterSalesApiService expenseAfterSalesApiService;

    @Autowired
    private AfterSalesMapper afterSalesMapper;



    @Override
    public void extractedWarnOrAutoDo(Integer afterSalesId,String salesOrderNo) {

        log.info("预警或自动创建费用单售后等：{},销售单：{}",JSON.toJSONString(afterSalesId),JSON.toJSONString(salesOrderNo));
        List<AfterSaleAuditInfoDto> afterSaleAuditInfoDto = afterSaleAuditInfoApiService.getAfterSaleAuditInfoDto(afterSalesId);
        if (CollUtil.isEmpty(afterSaleAuditInfoDto)) {
            log.info("销售售后单：{}，无虚拟商品", JSON.toJSONString(afterSalesId));
            return;
        }

        Optional<AfterSaleAuditInfoDto> any = afterSaleAuditInfoDto.stream().filter(c -> !c.getAuditStatus().equals(1)).findAny();
        if (any.isPresent()) {
            log.error("审核数据不正确：{}", JSON.toJSONString(afterSaleAuditInfoDto));
            return;
        }

        Optional<AfterSaleAuditInfoDto> pass = afterSaleAuditInfoDto.stream().filter(c -> c.getIsReturn().equals(1)).findAny();
        Optional<AfterSaleAuditInfoDto> noPass = afterSaleAuditInfoDto.stream().filter(c -> c.getIsReturn().equals(0)).findAny();
        if (pass.isPresent() && noPass.isPresent()) {
            log.error("表中的虚拟商品可退状态不一致:{}",JSON.toJSONString(afterSaleAuditInfoDto));
            return;
        }
        String creatorName=afterSalesMapper.getCreatorNameByAfterSalesId(afterSalesId);

        List<BuyorderExpenseDto> warns = Collections.emptyList();
        //采销联动退货采购费用订单，退货预警
        try {
            warns=expenseAfterSalesApiService.expenseReturnAndWarning(afterSalesId);
            if (CollUtil.isNotEmpty(warns)){
                warns.forEach(c -> {
                    Map<String, String> variableMap = new HashMap();
                    // 共用采购单的模板 些buyorderNo
                    variableMap.put("orderNo", c.getBuyorderExpenseNo());
                    variableMap.put("saleOrderNo", salesOrderNo);
                    String url = "./buyorderExpense/details.do?buyorderExpenseId=" + c.getBuyorderExpenseId();
                    // 发送消息
                    MessageUtil.sendMessage(249, CollUtil.newArrayList(c.getCreator()), variableMap, url,creatorName);
                });
            }
        }catch (Exception e) {
            log.error("销售售后单：{}，触发采购费用订单，退货预警异常：",JSON.toJSONString(afterSalesId),e);
        }
        List<Integer> warnsIds = warns.stream().filter(Objects::nonNull).map(BuyorderExpenseDto::getBuyorderExpenseId).distinct().collect(Collectors.toList());
        // 自动售后
        if (pass.isPresent()) {
            try {

                List<BuyorderExpenseDto> buyorderExpenseDtos = expenseAfterSalesAutoApiService.autoExpenseAfterSales(afterSalesId,warnsIds);
                if (CollUtil.isNotEmpty(buyorderExpenseDtos)) {
                    buyorderExpenseDtos.forEach(c -> {
                        Map<String, String> variableMap = new HashMap<>();
                        // 共用采购单的模板 些buyorderNo
                        variableMap.put("orderNo", c.getBuyorderExpenseNo());
                        variableMap.put("saleOrderNo", salesOrderNo);
                        String url = "./buyorderExpense/details.do?buyorderExpenseId=" + c.getBuyorderExpenseId();
                        // 发送消息
                        MessageUtil.sendMessage(249, CollUtil.newArrayList(c.getCreator()), variableMap, url,creatorName);
                    });
                }
            } catch (Exception e) {
                log.error("销售售后单：{}，费用单自动售后异常：",JSON.toJSONString(afterSalesId),e);
            }

        }
        // 自动转单
        if (noPass.isPresent()) {
            try {
                List<BuyorderExpenseDto> buyorderExpenseDtos = expenseAutoApiService.autoBuyorderExpense(afterSalesId,warnsIds);
                if (CollUtil.isNotEmpty(buyorderExpenseDtos)) {
                    buyorderExpenseDtos.forEach(c -> {
                        Map<String, String> variableMap = new HashMap();
                        // 共用采购单的模板 些buyorderNo
                        variableMap.put("orderNo", c.getOriginalBuyorderExpenseNo());
                        variableMap.put("saleOrderNo", salesOrderNo);
                        String url = "./buyorderExpense/details.do?buyorderExpenseId=" + c.getOriginalBuyorderExpenseId();
                        // 发送消息
                        MessageUtil.sendMessage(249, CollUtil.newArrayList(c.getCreator()), variableMap, url,creatorName);
                    });
                }
            } catch (Exception e) {
                log.error("销售售后单：{}，费用单自动转单异常：",JSON.toJSONString(afterSalesId),e);
            }
        }
    }
}
