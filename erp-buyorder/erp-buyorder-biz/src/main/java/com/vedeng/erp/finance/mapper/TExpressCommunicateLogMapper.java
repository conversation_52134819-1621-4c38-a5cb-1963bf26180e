package com.vedeng.erp.finance.mapper;

import com.vedeng.erp.finance.domain.entity.TExpressCommunicateLog;

import java.util.List;

public interface TExpressCommunicateLogMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(TExpressCommunicateLog record);

    int insertSelective(TExpressCommunicateLog record);

    TExpressCommunicateLog selectByPrimaryKey(Integer id);

    List<TExpressCommunicateLog> selectByExpressId(Integer expressId);

    int updateByPrimaryKeySelective(TExpressCommunicateLog record);

    int updateByPrimaryKey(TExpressCommunicateLog record);
}