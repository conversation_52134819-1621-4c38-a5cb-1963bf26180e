package com.vedeng.erp.trader.mapper;

import com.vedeng.erp.system.dto.LikeTraderDto;
import com.vedeng.erp.trader.domain.dto.TraderCustomerActionDto;
import com.vedeng.erp.trader.domain.dto.TraderCustomerInfoDto;
import com.vedeng.erp.trader.domain.dto.TraderCustomerPortraitDto;
import com.vedeng.erp.trader.domain.vo.TraderCustomerInfoVo;
import com.vedeng.erp.trader.dto.TraderBelongDto;
import com.vedeng.erp.trader.dto.TraderCustomerDto;
import com.vedeng.erp.trader.dto.TraderForSmartQuoteDto;
import com.vedeng.trader.model.TraderCustomer;
import io.swagger.models.auth.In;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Entity com.vedeng.erp.goods.model.Brand
 */
public interface TraderCustomerBaseMapper {

    /**
     * 根据traderId获取客户信息
     * @param traderId
     * @return
     */
    TraderCustomerInfoDto getTraderCutomerInfoByTraderId(Integer traderId);

    /**
     * 根据分组获取关联客户信息
     * @param associatedCustomerGroup
     * @return
     */
    List<TraderCustomerInfoDto> getRelatedTraderByGroupId(Long associatedCustomerGroup);

    /**
     * 根据TraderCutomerId获取客户信息
     * @param traderCustomerId
     * @return
     */
    TraderCustomerInfoDto getTraderCutomerInfoById(Integer traderCustomerId);

    /**
     * 更新客户的关联关系组
     * @param traderCustomerList 客户集合
     * @param associatedCustomerGroup 关联关系组
     */
    void updateAssociatedCustomerGroupOfCustomer(@Param("traderCustomerList") List<Integer> traderCustomerList, @Param("associatedCustomerGroup") Long associatedCustomerGroup);


    /**
     * 查询客户关系组下的所有客户集合
     * @param associatedCustomerGroup 客户关系组
     * @return 客户集合
     */
    List<Integer> getTraderCustomerListByAssociatedCustomerGroup(Long associatedCustomerGroup);

    List<TraderCustomerInfoDto> getAllTraderCustomerByUserId(Integer userId);


    /**
     * 获取客户基本信息
     * @param traderId 客户id
     * @return TraderCustomerInfoVo 基本信息
     */
    TraderCustomerInfoVo getTraderCustomerInfoVo(@Param("traderId")Integer traderId);
    /**
     * 获取客户基本信息
     * @param traderId 客户id
     * @return TraderCustomerInfoVo 基本信息
     */
    TraderCustomerInfoVo getTraderCustomerInfo(@Param("traderId")Integer traderId);

    /**
     * 客户名模糊查询
     *
     * @param limit 限制条数
     * @param name  客户名
     * @param userId 归属销售id
     * @param belong 是否归属 userId
     * @return List<TraderCustomerInfoVo>
     */
    List<TraderCustomerInfoVo> getTraderCustomerInfoVoByName(@Param("limit") Integer limit, @Param("name") String name, @Param("userId") Integer userId,@Param("belong") boolean belong);


    List<TraderCustomerInfoVo> getTraderCustomerInfoVoByNameForCRM(@Param("limit") Integer limit, @Param("name") String name);

    List<TraderCustomerInfoVo> getTraderCustomerInfoVoByNameAndUserIdForCRM(@Param("limit") Integer limit, @Param("name") String name, @Param("userId") Integer userId,@Param("belong") boolean belong);

    List<TraderCustomerInfoVo> getTraderCustomerInfoVoByNameAndUserIdForCRMShared(@Param("limit") Integer limit, @Param("name") String name, @Param("userId") Integer userId,@Param("notIncludeTraderIds") List<Integer> notIncludeTraderIds,@Param("share") boolean share);


    /**
     * 获取归属销售id
     * @param traderId 客户id
     * @return 销售id
     */
    Integer getTraderCustomerUserIdByTraderId(Integer traderId);

    /**
     * 审核通过且生效的
     * @param begin 开始
     * @param end 结束
     * @return List<TraderCustomerDto>
     */
    List<TraderCustomerDto> selectPushKingDeeTraderCustomerData(@Param("begin") Long begin, @Param("end") Long end,@Param("limit") Integer limit);

    /**
     * 在指定的客户id 集合里 拿去审核通过的
     * @param ids id集合
     * @return List<TraderCustomerDto>
     */
    List<TraderCustomerDto> selectPushKingDeeTraderCustomerDataByIds(@Param("ids") List<Integer> ids);

    /**
     * <AUTHOR>
     * @desc 查询资质审核通过需要推送金蝶的信息
     * @param begin
     * @param end
     * @param limit
     * @return
     */
    List<TraderCustomerDto> selectPushKingDeeTraderCustomerlicenceData(@Param("begin") Long begin, @Param("end") Long end,@Param("limit") Integer limit);

    /**
     * 查询2021年7月之后交易的销售客户
     * <AUTHOR>
     * @return
     */
    List<TraderCustomerDto> queryHaveCapitalTraderCustomer();

    /**
     * 查询并组装金蝶客户信息
     *
     * @param traderCustomerId   客户id
     * @return KingDeeCustomerDto
     */
    TraderCustomerDto getTraderInfoForKingDee(@Param("traderCustomerId") Integer traderCustomerId);

    /**
     * 根据traderId查询客户信息
     *
     * @param traderId traderId
     * @return TraderCustomerDto
     */
    TraderCustomerDto getTraderCustomerInfoByTraderId(@Param("traderId") Integer traderId);
    /**
     * 根据traderId查询客户信息
     *
     * @param traderIds traderId集合
     */
    List<TraderCustomerDto> getTraderCustomerInfoByTraderIds(@Param("traderIds") List<Integer> traderIds);

    /**
     * 获取客户资质信息
     *
     * @param traderId 交易者id
     * @return TraderCustomerDto
     */
    TraderCustomerDto getTraderCustomerAptitudeInfoByTraderId(@Param("traderId") Integer traderId);

    /**
     * 获取客户行为基本信息
     *
     * @param traderId 交易者id
     * @return TraderCustomerPortraitDto
     */
    TraderCustomerActionDto getTraderCustomerAction(@Param("traderId") Integer traderId);

    /**
     * 获取客户360信息
     *
     * @param traderId 交易者id
     * @return TraderCustomerPortraitDto
     */
    TraderCustomerPortraitDto getTraderCustomerPortrait(@Param("traderId") Integer traderId);

    /**
     * 根据条件查询客户集合
     *
     * @param traderCustomerDto traderCustomerDto
     * @return TraderCustomerDto
     */
    List<TraderCustomerDto> findByAll(TraderCustomerDto traderCustomerDto);

    List<TraderForSmartQuoteDto> getTraderListForSmartQuote(@Param("traderName") String traderName, @Param("userId") Integer userId);

    /**
     * 客户审核状态
     */
    Integer findTraderCustomerStatus(@Param("traderCustomerId") Integer traderCustomerId);
    /**
     * 根据交易者id集合查询客户信息
     *
     * @param traderIds 交易者id集合
     * @return TraderCustomerDto
     */
    List<TraderCustomerDto> getTraderCustomerListByTraderIds(@Param("traderIds") List<Integer> traderIds);

    /**
     * 根据客户ID获取客户等级对应的分数
     *
     * @param traderId
     * @return
     */
    Integer getTraderScoreByTraderId(@Param("traderId") Integer traderId);
    TraderCustomerDto getTraderByPayApply(Integer payApplyId);

    void updateTraderAmount(@Param("traderId") Integer traderId, @Param("amount") BigDecimal amount);

    List<TraderCustomerInfoVo> getTraderCustomerByTraderName(@Param("keyword") String keyword,@Param("start")  int start,@Param("pageSize")  int pageSize);

    long countTraderCustomerByTraderName(@Param("keyword")String keyword);

    List<LikeTraderDto> getTrader(@Param("keyword") String keywords);

    List<TraderBelongDto> getTraderBelongList(@Param("traderNameList") List<String> traderNameList);

    List<TraderBelongDto> getTraderBelong(@Param("traderId") Integer traderId);


    List<TraderCustomerInfoVo> queryTraderCustomerInfoByNameAndSubUserId(@Param("limit") Integer limit, @Param("name") String name, @Param("subUserIds") List<Integer> subUserIds,@Param("notIncludeTraderIds") List<Integer> notIncludeTraderIds);
}
