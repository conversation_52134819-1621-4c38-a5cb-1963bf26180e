package com.vedeng.goods.service.impl;

import com.vedeng.authorization.model.User;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.dao.CoreSpuMapper;
import com.vedeng.goods.exception.InvalidatorChainException;
import com.vedeng.goods.manager.constants.GoodsValidConstants;
import com.vedeng.goods.manager.validator.InvalidatorChain.ReasonNumInvalidator;
import com.vedeng.goods.manager.validator.InvalidatorChain.sku.Invalid.*;
import com.vedeng.goods.manager.validator.InvalidatorChain.sku.valid.SkuNotUsingValidator;
import com.vedeng.goods.manager.validator.InvalidatorChain.sku.valid.SkuUserValidator;
import com.vedeng.goods.manager.validator.InvalidatorChain.sku.valid.SkuUsingSpuValidator;
import com.vedeng.goods.manager.validator.InvalidatorChain.spu.Invalid.*;
import com.vedeng.goods.manager.validator.InvalidatorChain.spu.valid.SpuNoUsingValidator;
import com.vedeng.goods.manager.validator.InvalidatorChain.spu.valid.SpuUserValidator;
import com.vedeng.goods.manager.validator.builder.GoodsInvalidatorChainBuilder;
import com.vedeng.goods.model.CoreSku;
import com.vedeng.goods.model.CoreSpu;
import com.vedeng.goods.service.VGoodsCommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class VGoodsCommonServiceImpl implements VGoodsCommonService {

    @Resource
    CoreSpuMapper coreSpuMapper;

    @Resource
    CoreSkuMapper coreSkuMapper;

    @Autowired
    SkuBanInvalidator skuBanInvalidator;

    @Autowired
    SkuExistSpuInvalidator skuExistSpuInvalidator;

    @Autowired
    SkuNoRelatedInorderInvalidator skuNoRelatedInorderInvalidator;

    @Autowired
    SkuNoRelatedOrderForConditionFiveInvalidator skuNoRelatedOrderForConditionFiveInvalidator;

    @Autowired
    SkuNoRelatedOrderForConditionFourInvalidator skuNoRelatedOrderForConditionFourInvalidator;

    @Autowired
    SkuNoRelatedUnbackLendOutInvalidator skuNoRelatedUnbackLendOutInvalidator;

    @Autowired
    SkuNoRelatedValidLendOutInvalitor skuNoRelatedValidLendOutInvalitor;

    @Autowired
    SkuNotValidInvalidator skuNotValidInvalidator;

    @Autowired
    SkuOccupyInvalidator skuOccupyInvalidator;

    @Autowired
    SkuStockInvalidator skuStockInvalidator;

    @Autowired
    SkuUserInvalidator skuUserInvalidator;

    @Autowired
    SkuUsingInvalidator skuUsingInvalidator;

    @Autowired
    SpuBanInvalidator spuBanInvalidator;

    @Autowired
    SpuNoRelatedInorderInvalidator spuNoRelatedInorderInvalidator;

    @Autowired
    SpuNoRelatedOrderForConditionFiveInvalidator spuNoRelatedOrderForConditionFiveInvalidator;

    @Autowired
    SpuNoRelatedOrderForConditionFourInvalidator spuNoRelatedOrderForConditionFourInvalidator;

    @Autowired
    SpuNoRelatedUnbackLendOutInvalidator spuNoRelatedUnbackLendOutInvalidator;

    @Autowired
    SpuNoRelatedValidLendoutInvalidator spuNoRelatedValidLendoutInvalidator;

    @Autowired
    SpuNotValidInvalidator spuNotValidInvalidator;

    @Autowired
    SpuOccupyInvalidator spuOccupyInvalidator;

    @Autowired
    SpuStockInvalidator spuStockInvalidator;

    @Autowired
    SpuUserInvalidator spuUserInvalidator;

    @Autowired
    SpuUsingInvalidator spuUsingInvalidator;

    @Autowired
    ReasonNumInvalidator reasonNumInvalidator;

    @Autowired
    SkuNotUsingValidator skuNotUsingValidator;

    @Autowired
    SkuUserValidator skuUserValidator;

    @Autowired
    SkuUsingSpuValidator skuUsingSpuValidator;

    @Autowired
    SpuNoUsingValidator spuNoUsingValidator;

    @Autowired
    SpuUserValidator spuUserValidator;

    @Override
    public ResultInfo checkDisableSpuOrSku(User user, Integer relatedId,Integer type,String reason) {
        ResultInfo resultInfo = ResultInfo.error();
        try {
            if(GoodsValidConstants.SKU_TYPE.equals(type)){
                CoreSku coreSku = coreSkuMapper.selectByPrimaryKey(relatedId);
                resultInfo = GoodsInvalidatorChainBuilder.build().setSkuNo(coreSku.getSkuNo()).setReason(reason).setUser(user).add(reasonNumInvalidator).add(skuUserInvalidator).add(skuUsingInvalidator).add(skuExistSpuInvalidator)
                        .add(skuNotValidInvalidator).add(skuBanInvalidator).add(skuStockInvalidator).add(skuOccupyInvalidator).add(skuNoRelatedOrderForConditionFourInvalidator)
                        .add(skuNoRelatedOrderForConditionFiveInvalidator).add(skuNoRelatedValidLendOutInvalitor).add(skuNoRelatedUnbackLendOutInvalidator).add(skuNoRelatedInorderInvalidator).execute();
            } else if(GoodsValidConstants.SPU_TYPE.equals(type)){
                CoreSpu spuinfoById = coreSpuMapper.getSpuinfoById(relatedId);
                List<String> skuNos = coreSkuMapper.getSkuNoBySpuId(relatedId);

                resultInfo = GoodsInvalidatorChainBuilder.build().setSpuNo(spuinfoById.getSpuNo()).setUser(user).setSkuNoList(skuNos).setReason(reason).add(reasonNumInvalidator)
                        .add(spuUserInvalidator).add(spuUsingInvalidator).add(spuBanInvalidator).add(spuNotValidInvalidator).add(spuStockInvalidator).add(spuOccupyInvalidator)
                        .add(spuNoRelatedOrderForConditionFourInvalidator).add(spuNoRelatedOrderForConditionFiveInvalidator).add(spuNoRelatedInorderInvalidator)
                        .add(spuNoRelatedValidLendoutInvalidator).add(spuNoRelatedUnbackLendOutInvalidator).execute();
            }
        } catch (InvalidatorChainException e){
            return new ResultInfo(e.getErrorCode(),e.getErrorMessage());
        }
        catch (Exception e) {
            log.info("商品禁用审核校验出错"+e.getMessage());
            return ResultInfo.error() ;
        }

        return resultInfo;
    }


    @Override
    public ResultInfo checkAbleSpuOrSku(User user, Integer relatedId, Integer type) {
        ResultInfo resultInfo = ResultInfo.error();
        if(GoodsValidConstants.SKU_TYPE.equals(type)){
            CoreSku coreSku = coreSkuMapper.selectByPrimaryKey(relatedId);
            resultInfo = GoodsInvalidatorChainBuilder.build().setUser(user).setSkuNo(coreSku.getSkuNo()).add(skuNotUsingValidator).add(skuUserValidator).add(skuUsingSpuValidator).execute();
        } else if(GoodsValidConstants.SPU_TYPE.equals(type)){
            CoreSpu spuinfoById = coreSpuMapper.getSpuinfoById(relatedId);
            List<String> skuNos = coreSkuMapper.getSkuNoBySpuId(relatedId);
            resultInfo= GoodsInvalidatorChainBuilder.build().setUser(user).setSpuNo(spuinfoById.getSpuNo()).setSkuNoList(skuNos).add(spuUserValidator).add(spuNoUsingValidator).execute();
        }
        return resultInfo;
    }

    @Override
    public ResultInfo checkDisableSpuOrSkuWithoutReason(User user, Integer relatedId, Integer goodsType) {
        ResultInfo resultInfo = ResultInfo.error();
        if(GoodsValidConstants.SKU_TYPE.equals(goodsType)){
            CoreSku coreSku = coreSkuMapper.selectByPrimaryKey(relatedId);
            resultInfo = GoodsInvalidatorChainBuilder.build().setSkuNo(coreSku.getSkuNo()).add(skuExistSpuInvalidator)
                    .add(skuNotValidInvalidator).add(skuBanInvalidator).add(skuStockInvalidator).add(skuOccupyInvalidator).add(skuNoRelatedOrderForConditionFourInvalidator)
                    .add(skuNoRelatedOrderForConditionFiveInvalidator).add(skuNoRelatedValidLendOutInvalitor).add(skuNoRelatedUnbackLendOutInvalidator).add(skuNoRelatedInorderInvalidator).execute();
        } else if(GoodsValidConstants.SPU_TYPE.equals(goodsType)){
            CoreSpu spuinfoById = coreSpuMapper.getSpuinfoById(relatedId);
            List<String> skuNos = coreSkuMapper.getSkuNoBySpuId(relatedId);

            resultInfo = GoodsInvalidatorChainBuilder.build().setSpuNo(spuinfoById.getSpuNo()).setSkuNoList(skuNos)
                    .add(spuBanInvalidator).add(spuNotValidInvalidator).add(spuStockInvalidator).add(spuOccupyInvalidator)
                    .add(spuNoRelatedOrderForConditionFourInvalidator).add(spuNoRelatedOrderForConditionFiveInvalidator).add(spuNoRelatedInorderInvalidator)
                    .add(spuNoRelatedValidLendoutInvalidator).add(spuNoRelatedUnbackLendOutInvalidator).execute();
        }

        return resultInfo;
    }
}
