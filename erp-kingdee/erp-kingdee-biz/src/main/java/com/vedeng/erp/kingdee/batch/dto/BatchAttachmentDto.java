package com.vedeng.erp.kingdee.batch.dto;

import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * @description 附件表
 * <AUTHOR>
 * @date 2022/11/28 19:14
 **/
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchAttachmentDto {
    private Integer attachmentId;

    /**
    * 附件类型SYS_OPTION_DEFINITION_ID
    */
    private Integer attachmentType;

    /**
    * 附件应用类型 字典库
    */
    private Integer attachmentFunction;

    /**
    * 关联表ID
    */
    private Integer relatedId;

    /**
    * 附件名称
    */
    private String name;

    /**
    * 域名
    */
    private String domain;

    /**
    * 地址
    */
    private String uri;

    /**
    * 描述
    */
    private String alt;

    /**
    * 排序
    */
    private Integer sort;

    /**
    * 是否默认0否1是
    */
    private Integer isDefault;

    /**
    * 添加时间
    */
    private Long addTime;

    /**
    * 添加人
    */
    private Integer creator;

    /**
    * OSS的唯一id
    */
    private String ossResourceId;

    /**
    * 原始的文件路径
    */
    private String originalFilepath;

    /**
    * 同步标志:0-未同步 1-同步成功 2-同步失败
    */
    private Integer synSuccess;

    /**
    * 耗时
    */
    private Long costTime;

    /**
    * 是否删除，0未删除，1已删除
    */
    private Integer isDeleted;

    /**
    * 文件后缀
    */
    private String suffix;

    /**
    * 文件大小
    */
    private BigDecimal fileSize;

    /**
    * 文件高度
    */
    private BigDecimal fileHeight;

    /**
    * 文件宽度
    */
    private BigDecimal fileWidth;

    /**
     * 金蝶库单id
     */
    private String fId;

    /**
     * 入库单号
     */
    private String outInNo;

    /**
     * 承载不同的formId
     */
    private String formId;
}