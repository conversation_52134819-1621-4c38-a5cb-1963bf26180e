<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="新增沟通记录" scope="application" />
<%@ include file="../common/common.jsp"%>

	<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/businessclues/add_communicate.js?rnd=${resourceVersionKey}"></script>
	<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/tag.js?rnd=${resourceVersionKey}"></script>
<div class="formpublic pt15">
        <form method="post" action="" id="myform">
            <ul>
                <li>
                    <div class="infor_name">
                        <lable>客户名称</lable>
                    </div>
                    <div class="f_left">
                        <div>
                            ${traderBaseInfo.traderName}
                        </div>
                        <div id="traderNameError"></div>
                    </div>
                </li>

                <li>
                    <div class="infor_name">
                        <lable>归属销售</lable>
                    </div>
                    <div class="f_left">
                        <div>
                            ${traderBaseInfo.salesNameStr}
                        </div>
                        <div id="salesNameError"></div>
                    </div>
                </li>

                <li>
                    <div class="infor_name">
                        <span>*</span>
                        <lable>联系人</lable>
                    </div>
                    <div class="f_left">
                    	<div>
                        <select class="mr5" name="traderContactId" id="traderContactId">
                            <option selected="selected" value="0">请选择</option>
                            <c:if test="${not empty contactList }">
                            	<c:forEach	items="${contactList }" var="contact">
                            		<option value="${contact.traderContactId }">
                            		${contact.name }
                            		<c:if test="${contact.telephone !='' and contact.telephone != null }">|${contact.telephone }</c:if>
                            		<c:if test="${contact.mobile !='' and contact.mobile != null }">|${contact.mobile }</c:if>
                            		<c:if test="${contact.mobile2 !=''and contact.mobile2 != null}">|${contact.mobile2 }</c:if>
                            		</option>
                            	</c:forEach>
                            </c:if>
                        </select>
                        </div>
                        <div id="traderContactIdError"></div>
                    </div>
                    <div class="title-click pop-new-data" style='float:left;margin:-4px 0 0 10px;' layerParams='{"width":"700px","height":"500px","title":"新增联系人","link":"${pageContext.request.contextPath}/order/bussinesschance/addTraderContact.do?traderId=${businessClues.traderId }"}'>
	                                           添加联系人
	                </div>
                </li>
                <li>
                    <div class="infor_name">
                        <span>*</span>
                        <lable>沟通时间</lable>
                    </div>
                    <div class="f_left inputfloat ">
                    	<div>
	                    	 <input class="Wdate input-small mr0" type="text"
							placeholder="请选择时间"
							onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',maxDate:'#F{$dp.$D(\'end\')}'})" autocomplete="off"
							name="begin" id="begin" value="<date:date value ="${communicateRecord.begintime} "/>" onchange="changeDate(this);"/>
							<div class="gang">-</div>
							<input class="Wdate input-small ml5" type="text" placeholder="请选择时间" autocomplete="off"
							onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',minDate:'#F{$dp.$D(\'begin\')}'})" name="end"
							id="end" onchange="changeDate(this);" value="<date:date value ="${communicateRecord.endtime} "/>"/>
                        </div>
                        <div id="timeError"></div>
                    </div>
                </li>

                <li>
                    <div class="infor_name">
                        <span>*</span>
                        <lable>沟通内容</lable>
                    </div>
                    <div class="f_left table-largest">
	                    <div>
							 <div class="inputfloat manageaddtag">
								<label class="mt4 mr8">您可以从这些标签中选择</label>
								<c:if test="${not empty tagList }">
									<c:forEach items="${tagList }" var="tag">
										<span onclick="addTag(${tag.tagId},'${tag.tagName }',this);">${tag.tagName }</span>
									</c:forEach>
								</c:if>
								<c:if test="${page.totalPage > 1}">
								<div class="change" onclick="changeTag(${page.totalPage},10,this,32);"><span class="m0">换一批(</span><span class="m0" id="leftNum">${page.totalPage}</span><span class="m0">)</span>
								<input type="hidden" id="pageNo" value="${page.pageNo}">
								</div>
								</c:if>
							</div>
							<div class="inputfloat <c:if test="${empty tagList }">mt8</c:if>">
								<input type="text" id="defineTag" placeholder="如果标签中没有您所需要的，请自行填写" class="input-large" style='height:26px;'>
								<div class="f_left bt-bg-style bg-light-blue bt-small  addbrand" onclick="addDefineTag(this);">添加</div>
							</div>
							<div class="addtags mt6 none">
								<ul id="tag_show_ul">
								</ul>
							</div>
                            <div>
                                <textarea  name="contentSuffix" placeholder="沟通内容最多输入200字符，请检查后提交"
                                           style="width: 450px; height: 100px"></textarea>
                                <div id="contentSuffixError" ></div>
                            </div>
						</div>
					</div>
                </li>
                <li>
                    <div class="infor_name">
                        <span>*</span>
                        <lable>下次沟通时间</lable>
                    </div>
                    <div class="f_left inputfloat ">
                    	<input class="Wdate input-small " type="hidden" placeholder="30天后时间" autocomplete="off" onFocus="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="15Date" id="15Date" value="<date:date value ='${hideDate} ' format="yyyy-MM-dd"/>"  />
                    	<input class="Wdate input-small " type="hidden" placeholder="当前时间" autocomplete="off" onFocus="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="nowDate" id="nowDate" value="<date:date value ='${nowDate} ' format="yyyy-MM-dd"/>"  />
                        <input class="Wdate input-small " type="text" placeholder="请选择时间" autocomplete="off" onClick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'15Date\')}',minDate:'#F{$dp.$D(\'nowDate\')}'})" name="nextDate" id="nextDate" />
                    </div>
                    <input type="checkbox" id="noneNextDate"> 暂无下次沟通时间
                    <input type="hidden" id="noneNextDateVal" name="noneNextDate">
                </li>
                <li>
                    <div class="infor_name">
                        <lable>下次沟通内容</lable>
                    </div>
                    <div class="f_left  ">
                        <input type="text" class="input-largest" name="nextContactContent" id="nextContactContent">
                    </div>
                </li>
                <li>
                    <div class="infor_name">
                        <lable>备注</lable>
                    </div>
                    <div class="f_left  ">
                        <input type="text" class="input-largest" name="comments" id="comments">
                    </div>
                </li>
            </ul>
            <div class="add-tijiao tcenter">
                <input type="hidden" name="name" id="name" value="" >
                <input type="hidden" name="telephone" id="telephone" value="">
            	<input type="hidden" name="traderId" value="${businessClues.traderId }" >
            	<input type="hidden" name="businessCluesId" value="${businessClues.businessCluesId}">
            	<input type="hidden" name="formToken" value="${formToken}"/>
                <button type="submit" id="submit">提交</button>
            </div>
        </form>
    </div>
</body>
<script type="text/javascript">
    $(document).ready(function() {
        setTimeout(function(){
            $.ajax({
                url: "${pageContext.request.contextPath}/system/call/pushVoiceMp3.do",
                type: "post",
                timeout: 60000,
                data: {
                    "coid":'${param.coid}'
                },
                dataType : "json",
                success: function(data) {
                    console.log("通话完成，发起AI解析。");
                }
            });
        }, 2000);
    });


</script>

</html>
