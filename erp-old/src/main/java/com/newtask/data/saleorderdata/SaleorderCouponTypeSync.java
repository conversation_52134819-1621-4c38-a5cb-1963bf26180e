package com.newtask.data.saleorderdata;

import com.newtask.data.AbstractDataSync;
import com.newtask.data.dao.SaleorderDataMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @desc 初始化优惠类型
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/19 13:18
 * @describe
 */
@Component
public class SaleorderCouponTypeSync extends AbstractDataSync {

    @Autowired
    private SaleorderDataMapper saleorderDataMapper;
    @Override
    public List<Integer> loadBizId(Long startTime, Long endTime) {
        // 根据订单创建时间或修改时间获取优惠类型对应订单Id
        List<Integer> saleorderIdsList = saleorderDataMapper.getSaleorderCouponTypeIdsList(startTime, endTime);

        return saleorderIdsList;
    }

    @Override
    public List<Map<String, Object>> loadBizData(List<Integer> bizIds) {
        List<Map<String, Object>> couponType = null;

        if (CollectionUtils.isNotEmpty(bizIds)) {
            couponType = saleorderDataMapper.findCouponTypeBySaleorderId(bizIds);
        }

        return couponType;
    }

    @Override
    public void updateData(List<Map<String, Object>> dataList) {

        dataList.forEach(item -> {
            Integer saleorderId = NumberUtils.toInt(item.get("saleorderId") + "");
            Long couponType = NumberUtils.toLong(item.get("couponType") + "");

            long nowTime = new Date().getTime();

            // 判断saleorderId是否存在
            makeExist(saleorderId, nowTime);

            // 根据id修改coupontype
            saleorderDataMapper.updateSaleorderCouponType(saleorderId, couponType, nowTime);
        });

    }

    @Override
    public void makeExist(Integer id, Long nowTime) {
        Long count = saleorderDataMapper.getSaleorderById(id);
        if (count <= 0) {
            saleorderDataMapper.insertSaleOrder(id, nowTime);
        }
    }
}
