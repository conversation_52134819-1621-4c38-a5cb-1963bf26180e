package com.vedeng.common.core.listenerEvent;

import lombok.Builder;
import lombok.Data;

/**
 * 销售订单合同回传事件
 * 用于处理销售业务流转单第一节点的合同回传记录逻辑
 */
@Data
@Builder
public class SaleOrderContractReturnEvent {

    /**
     * 销售订单ID
     */
    private Integer saleorderId;

    /**
     * 销售订单编号
     */
    private String saleorderNo;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 域名
     */
    private String domain;

    /**
     * OSS资源ID
     */
    private String ossResourceId;

    /**
     * 签章时间
     */
    private String signTime;

    /**
     * 文件URL
     */
    private String fileUrl;

    /**
     * 流转单ID
     */
    private Long flowOrderId;

    /**
     * 流转单编号
     */
    private String flowOrderNo;

    /**
     * 业务类型（用于区分是否为全程签章）
     */
    private Integer businessType;
}
