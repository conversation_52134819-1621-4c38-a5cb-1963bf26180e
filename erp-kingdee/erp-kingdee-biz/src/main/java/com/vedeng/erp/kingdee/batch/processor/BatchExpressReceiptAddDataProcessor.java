package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.BatchExpressReceiptBasicDataDto;
import com.vedeng.erp.kingdee.dto.KingDeeExpressReceiptDto;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 快递签收新增数据 处理类
 * @date 2023/04/14 15:00
 */

@Service
@Slf4j
public class BatchExpressReceiptAddDataProcessor implements ItemProcessor<BatchExpressReceiptBasicDataDto, KingDeeExpressReceiptDto> {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;


    @Override
    public KingDeeExpressReceiptDto process(BatchExpressReceiptBasicDataDto dto) throws Exception {

        log.info("BatchExpressReceiptAddDataProcessor process dto:{}", JSON.toJSONString(dto));

        log.info("查询新增快递签收数据是否已推送到金蝶,FQzokBddjbh:{}", dto.getUniqueKey());
        KingDeeExpressReceiptDto queryDto = KingDeeExpressReceiptDto.builder().FQzokBddjbh(dto.getUniqueKey()).build();
        if (kingDeeBaseApi.isExist(queryDto)) {
            log.info("新增快递签收数据已推送到金蝶:{}", JSON.toJSONString(dto));
            return null;
        }

        return KingDeeExpressReceiptDto.builder()
                .fid("0")
                .FQzokOrgid(KingDeeConstant.ORG_ID.toString())
                .FQzokYsddh(dto.getSaleorderNo())
                .FQzokGsywdh(dto.getSaleorderNo())
                .FQzokCrkdh(dto.getOutInNo())
                .FQzokKdh(dto.getLogisticsNo())
                .FQzokYwlx("销售订单")
                .FQzokQssj(DateUtil.formatDate(dto.getArrivalTime()))
                .FQzokWlgs(dto.getLogistics())
                .FQzokWlbm(dto.getSku())
                .FQzokXlh(dto.getBarcodeFactory())
                .FQzokPch(dto.getBatchNumber())
                .FQzokFhsl(Objects.isNull(dto.getNum()) ? BigDecimal.ZERO : new BigDecimal(dto.getNum().toString()))
                .FQzokSjr(dto.getTakeTraderContactName())
                .FQzokDh(dto.getTakeTraderContactTelephone())
                .FQzokDz(dto.getTakeTraderAddress())
                .FQzokBddjbh(dto.getUniqueKey())
                .FQzokSfsc(Boolean.FALSE.toString())
                .FQzokSfjrcb(Objects.nonNull(dto.getIsExpense()) && KingDeeConstant.ONE.equals(dto.getIsExpense()) ? "N" : "Y")
                .FQzokSfzp(Objects.nonNull(dto.getIsGift()) && KingDeeConstant.ONE.equals(dto.getIsGift()) ? 1 : 0)
                .build();
    }

}
