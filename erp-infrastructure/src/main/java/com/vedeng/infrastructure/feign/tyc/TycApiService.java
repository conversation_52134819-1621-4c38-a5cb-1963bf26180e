package com.vedeng.infrastructure.feign.tyc;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/7/17
 */
import com.github.pagehelper.PageInfo;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.infrastructure.tyc.domain.dto.TraderInfoTyc;
import com.vedeng.infrastructure.tyc.domain.dto.TycResultDto;
import feign.Headers;
import feign.Param;
import feign.RequestLine;

import java.util.List;

/**
 * 天眼查服务-调用ERP接口
 *
 */
@FeignApi(serverName = "erpServer")
public interface TycApiService {

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /api/tyc/locallist.do?names={names}")
    RestfulResult<List<TycResultDto>> locallist(@Param("names") String names);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /api/tyc/list.do?name={name}")
    RestfulResult<PageInfo<TycResultDto>> list(@Param("name") String name);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /api/tyc/detail.do?name={name}")
    RestfulResult<TraderInfoTyc> detail(@Param("name") String name);
}
