package com.vedeng.logistics.model;

import java.math.BigDecimal;

public class WarehouseGoodsOperateLogVirtual {
    /**   WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL_ID **/
    private Integer warehouseGoodsOperateLogVirtualId;

    /** 产品条码ID  BARCODE_ID **/
    private Integer barcodeId;

    /** 公司ID  COMPANY_ID **/
    private Integer companyId;

    /** 操作类型1入库 2出库3销售换货入库4销售换货出库5销售退货入库6采购退货出库7采购换货出库8采购换货入库 9外借入库 10外借出库 11调整盘盈入库 12盘盈入库 13报废出库 14领用出库 15 盘亏出库  OPERATE_TYPE **/
    private Integer operateType;

    /** 关联采购、销售、售后产品ID  RELATED_ID **/
    private Integer relatedId;

    /** 出库时对应拣货单详细ID  WAREHOUSE_PICKING_DETAIL_ID **/
    private Integer warehousePickingDetailId;

    /** 商品ID  GOODS_ID **/
    private Integer goodsId;

    /** SN码  BARCODE_FACTORY **/
    private String barcodeFactory;

    /** 产品数量  NUM **/
    private Integer num;

    private String model;//型号

    private String brandName;//品牌

    private String goodsName;//产品名称

    /** 仓库ID  WAREHOUSE_ID **/
    private Integer warehouseId;

    /** 库房ID  STORAGE_ROOM_ID **/
    private Integer storageRoomId;

    /** 货区ID  STORAGE_AREA_ID **/
    private Integer storageAreaId;

    /** 库位ID  STORAGE_LOCATION_ID **/
    private Integer storageLocationId;

    /** 货架ID  STORAGE_RACK_ID **/
    private Integer storageRackId;

    /** 厂商批号  BATCH_NUMBER **/
    private String batchNumber;

    /** 效期  EXPIRATION_DATE **/
    private Long expirationDate;

    /** 入库验收0未验收1验收通过2不通过  CHECK_STATUS **/
    private Integer checkStatus;

    /** 入库验收人  CHECK_STATUS_USER **/
    private Integer checkStatusUser;

    /** 入库时间  CHECK_STATUS_TIME **/
    private Long checkStatusTime;

    /** 出库复核0未复核1通过2不通过  RECHECK_STATUS **/
    private Integer recheckStatus;

    /** 出库复核人  RECHECK_STATUS_USER **/
    private Integer recheckStatusUser;

    /** 出库复核时间  RECHECK_STATUS_TIME **/
    private Long recheckStatusTime;

    /** 备注  COMMENTS **/
    private String comments;

    /** 是否有效 0否 1是  IS_ENABLE **/
    private Integer isEnable;

    /** 是否已绑定快递0否 1是  IS_EXPRESS **/
    private Integer isExpress;

    /** 创建时间  ADD_TIME **/
    private Long addTime;

    /** 创建人  CREATOR **/
    private Integer creator;

    /** 更新时间  MOD_TIME **/
    private Long modTime;

    /** 更新人  UPDATER **/
    private Integer updater;

    /** 0 否 1 是  IS_PROBLEM **/
    private Integer isProblem;

    /** 问题备注  PROBLEM_REMARK **/
    private String problemRemark;

    /** 生产日期  PRODUCT_DATE **/
    private Long productDate;

    /** 成本价  COST_PRICE **/
    private BigDecimal costPrice;

    /** 入库条码是否使用  0未使用  1使用  IS_USE **/
    private Integer isUse;

    /** 逻辑仓id   LOGICAL_WAREHOUSE_ID **/
    private Integer logicalWarehouseId;

    /** 贝登批次码  VEDENG_BATCH_NUMER **/
    private String vedengBatchNumer;

    /** 剩余库存数量  LAST_STOCK_NUM **/
    private Integer lastStockNum;

    /** 灭菌批号  STERILZATION_BATCH_NUMBER **/
    private String sterilzationBatchNumber;

    /** 日志类型 0入库 1出库  LOG_TYPE **/
    private Integer logType;

    /** 成本价(新)  NEW_COST_PRICE **/
    private BigDecimal newCostPrice;

    /** 来源信息  TAG_SOURCES **/
    private String tagSources;

    /** 专向发货关联VP单号  DEDICATED_BUYORDER_NO **/
    private String dedicatedBuyorderNo;
    /**
     * 锁定状态0未锁定 1已锁定
     */
    private Integer lockedStatus=0;

    /**
     * sku
     */
    private String sku;

    /**
     * 灭菌编号
     */
    private String sterilizationBatchNo;

    /**
     * 入库操作人
     */
    private String operator;

    private Integer fitstEngageId;

    private String firstEngageId;

    private String registrationNo;

    private String registrationNumber;

    private Integer registrationNumberId;
    /**
     * 实际收货数量
     * @return
     */
    private Integer realGoodsNum;

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getRegistrationNo() {
        return registrationNo;
    }

    public void setRegistrationNo(String registrationNo) {
        this.registrationNo = registrationNo;
    }

    public String getSterilizationBatchNo() {
        return sterilizationBatchNo;
    }

    public void setSterilizationBatchNo(String sterilizationBatchNo) {
        this.sterilizationBatchNo = sterilizationBatchNo;
    }

    public Integer getRealGoodsNum() {
        return realGoodsNum;
    }

    public void setRealGoodsNum(Integer realGoodsNum) {
        this.realGoodsNum = realGoodsNum;
    }

    public Integer getFitstEngageId() {
        return fitstEngageId;
    }

    public void setFitstEngageId(Integer fitstEngageId) {
        this.fitstEngageId = fitstEngageId;
    }

    /**     WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL_ID   **/
    public Integer getWarehouseGoodsOperateLogVirtualId() {
        return warehouseGoodsOperateLogVirtualId;
    }

    /**     WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL_ID   **/
    public void setWarehouseGoodsOperateLogVirtualId(Integer warehouseGoodsOperateLogVirtualId) {
        this.warehouseGoodsOperateLogVirtualId = warehouseGoodsOperateLogVirtualId;
    }

    /**   产品条码ID  BARCODE_ID   **/
    public Integer getBarcodeId() {
        return barcodeId;
    }

    /**   产品条码ID  BARCODE_ID   **/
    public void setBarcodeId(Integer barcodeId) {
        this.barcodeId = barcodeId;
    }

    /**   公司ID  COMPANY_ID   **/
    public Integer getCompanyId() {
        return companyId;
    }

    /**   公司ID  COMPANY_ID   **/
    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    /**   操作类型1入库 2出库3销售换货入库4销售换货出库5销售退货入库6采购退货出库7采购换货出库8采购换货入库 9外借入库 10外借出库 11调整盘盈入库 12盘盈入库 13报废出库 14领用出库 15 盘亏出库  OPERATE_TYPE   **/
    public Integer getOperateType() {
        return operateType;
    }

    /**   操作类型1入库 2出库3销售换货入库4销售换货出库5销售退货入库6采购退货出库7采购换货出库8采购换货入库 9外借入库 10外借出库 11调整盘盈入库 12盘盈入库 13报废出库 14领用出库 15 盘亏出库  OPERATE_TYPE   **/
    public void setOperateType(Integer operateType) {
        this.operateType = operateType;
    }

    /**   关联采购、销售、售后产品ID  RELATED_ID   **/
    public Integer getRelatedId() {
        return relatedId;
    }

    /**   关联采购、销售、售后产品ID  RELATED_ID   **/
    public void setRelatedId(Integer relatedId) {
        this.relatedId = relatedId;
    }

    /**   出库时对应拣货单详细ID  WAREHOUSE_PICKING_DETAIL_ID   **/
    public Integer getWarehousePickingDetailId() {
        return warehousePickingDetailId;
    }

    /**   出库时对应拣货单详细ID  WAREHOUSE_PICKING_DETAIL_ID   **/
    public void setWarehousePickingDetailId(Integer warehousePickingDetailId) {
        this.warehousePickingDetailId = warehousePickingDetailId;
    }

    /**   商品ID  GOODS_ID   **/
    public Integer getGoodsId() {
        return goodsId;
    }

    /**   商品ID  GOODS_ID   **/
    public void setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
    }

    /**   SN码  BARCODE_FACTORY   **/
    public String getBarcodeFactory() {
        return barcodeFactory;
    }

    /**   SN码  BARCODE_FACTORY   **/
    public void setBarcodeFactory(String barcodeFactory) {
        this.barcodeFactory = barcodeFactory == null ? null : barcodeFactory.trim();
    }

    /**   产品数量  NUM   **/
    public Integer getNum() {
        return num;
    }

    /**   产品数量  NUM   **/
    public void setNum(Integer num) {
        this.num = num;
    }

    /**   仓库ID  WAREHOUSE_ID   **/
    public Integer getWarehouseId() {
        return warehouseId;
    }

    /**   仓库ID  WAREHOUSE_ID   **/
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**   库房ID  STORAGE_ROOM_ID   **/
    public Integer getStorageRoomId() {
        return storageRoomId;
    }

    /**   库房ID  STORAGE_ROOM_ID   **/
    public void setStorageRoomId(Integer storageRoomId) {
        this.storageRoomId = storageRoomId;
    }

    /**   货区ID  STORAGE_AREA_ID   **/
    public Integer getStorageAreaId() {
        return storageAreaId;
    }

    /**   货区ID  STORAGE_AREA_ID   **/
    public void setStorageAreaId(Integer storageAreaId) {
        this.storageAreaId = storageAreaId;
    }

    /**   库位ID  STORAGE_LOCATION_ID   **/
    public Integer getStorageLocationId() {
        return storageLocationId;
    }

    /**   库位ID  STORAGE_LOCATION_ID   **/
    public void setStorageLocationId(Integer storageLocationId) {
        this.storageLocationId = storageLocationId;
    }

    /**   货架ID  STORAGE_RACK_ID   **/
    public Integer getStorageRackId() {
        return storageRackId;
    }

    /**   货架ID  STORAGE_RACK_ID   **/
    public void setStorageRackId(Integer storageRackId) {
        this.storageRackId = storageRackId;
    }

    /**   厂商批号  BATCH_NUMBER   **/
    public String getBatchNumber() {
        return batchNumber;
    }

    /**   厂商批号  BATCH_NUMBER   **/
    public void setBatchNumber(String batchNumber) {
        this.batchNumber = batchNumber == null ? null : batchNumber.trim();
    }

    /**   效期  EXPIRATION_DATE   **/
    public Long getExpirationDate() {
        return expirationDate;
    }

    /**   效期  EXPIRATION_DATE   **/
    public void setExpirationDate(Long expirationDate) {
        this.expirationDate = expirationDate;
    }

    /**   入库验收0未验收1验收通过2不通过  CHECK_STATUS   **/
    public Integer getCheckStatus() {
        return checkStatus;
    }

    /**   入库验收0未验收1验收通过2不通过  CHECK_STATUS   **/
    public void setCheckStatus(Integer checkStatus) {
        this.checkStatus = checkStatus;
    }

    /**   入库验收人  CHECK_STATUS_USER   **/
    public Integer getCheckStatusUser() {
        return checkStatusUser;
    }

    /**   入库验收人  CHECK_STATUS_USER   **/
    public void setCheckStatusUser(Integer checkStatusUser) {
        this.checkStatusUser = checkStatusUser;
    }

    /**   入库时间  CHECK_STATUS_TIME   **/
    public Long getCheckStatusTime() {
        return checkStatusTime;
    }

    /**   入库时间  CHECK_STATUS_TIME   **/
    public void setCheckStatusTime(Long checkStatusTime) {
        this.checkStatusTime = checkStatusTime;
    }

    /**   出库复核0未复核1通过2不通过  RECHECK_STATUS   **/
    public Integer getRecheckStatus() {
        return recheckStatus;
    }

    /**   出库复核0未复核1通过2不通过  RECHECK_STATUS   **/
    public void setRecheckStatus(Integer recheckStatus) {
        this.recheckStatus = recheckStatus;
    }

    /**   出库复核人  RECHECK_STATUS_USER   **/
    public Integer getRecheckStatusUser() {
        return recheckStatusUser;
    }

    /**   出库复核人  RECHECK_STATUS_USER   **/
    public void setRecheckStatusUser(Integer recheckStatusUser) {
        this.recheckStatusUser = recheckStatusUser;
    }

    /**   出库复核时间  RECHECK_STATUS_TIME   **/
    public Long getRecheckStatusTime() {
        return recheckStatusTime;
    }

    /**   出库复核时间  RECHECK_STATUS_TIME   **/
    public void setRecheckStatusTime(Long recheckStatusTime) {
        this.recheckStatusTime = recheckStatusTime;
    }

    /**   备注  COMMENTS   **/
    public String getComments() {
        return comments;
    }

    /**   备注  COMMENTS   **/
    public void setComments(String comments) {
        this.comments = comments == null ? null : comments.trim();
    }

    /**   是否有效 0否 1是  IS_ENABLE   **/
    public Integer getIsEnable() {
        return isEnable;
    }

    /**   是否有效 0否 1是  IS_ENABLE   **/
    public void setIsEnable(Integer isEnable) {
        this.isEnable = isEnable;
    }

    /**   是否已绑定快递0否 1是  IS_EXPRESS   **/
    public Integer getIsExpress() {
        return isExpress;
    }

    /**   是否已绑定快递0否 1是  IS_EXPRESS   **/
    public void setIsExpress(Integer isExpress) {
        this.isExpress = isExpress;
    }

    /**   创建时间  ADD_TIME   **/
    public Long getAddTime() {
        return addTime;
    }

    /**   创建时间  ADD_TIME   **/
    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    /**   创建人  CREATOR   **/
    public Integer getCreator() {
        return creator;
    }

    /**   创建人  CREATOR   **/
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**   更新时间  MOD_TIME   **/
    public Long getModTime() {
        return modTime;
    }

    /**   更新时间  MOD_TIME   **/
    public void setModTime(Long modTime) {
        this.modTime = modTime;
    }

    /**   更新人  UPDATER   **/
    public Integer getUpdater() {
        return updater;
    }

    /**   更新人  UPDATER   **/
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    /**   0 否 1 是  IS_PROBLEM   **/
    public Integer getIsProblem() {
        return isProblem;
    }

    /**   0 否 1 是  IS_PROBLEM   **/
    public void setIsProblem(Integer isProblem) {
        this.isProblem = isProblem;
    }

    /**   问题备注  PROBLEM_REMARK   **/
    public String getProblemRemark() {
        return problemRemark;
    }

    /**   问题备注  PROBLEM_REMARK   **/
    public void setProblemRemark(String problemRemark) {
        this.problemRemark = problemRemark == null ? null : problemRemark.trim();
    }

    /**   生产日期  PRODUCT_DATE   **/
    public Long getProductDate() {
        return productDate;
    }

    /**   生产日期  PRODUCT_DATE   **/
    public void setProductDate(Long productDate) {
        this.productDate = productDate;
    }

    /**   成本价  COST_PRICE   **/
    public BigDecimal getCostPrice() {
        return costPrice;
    }

    /**   成本价  COST_PRICE   **/
    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

    /**   入库条码是否使用  0未使用  1使用  IS_USE   **/
    public Integer getIsUse() {
        return isUse;
    }

    /**   入库条码是否使用  0未使用  1使用  IS_USE   **/
    public void setIsUse(Integer isUse) {
        this.isUse = isUse;
    }

    /**   逻辑仓id   LOGICAL_WAREHOUSE_ID   **/
    public Integer getLogicalWarehouseId() {
        return logicalWarehouseId;
    }

    /**   逻辑仓id   LOGICAL_WAREHOUSE_ID   **/
    public void setLogicalWarehouseId(Integer logicalWarehouseId) {
        this.logicalWarehouseId = logicalWarehouseId;
    }

    /**   贝登批次码  VEDENG_BATCH_NUMER   **/
    public String getVedengBatchNumer() {
        return vedengBatchNumer;
    }

    /**   贝登批次码  VEDENG_BATCH_NUMER   **/
    public void setVedengBatchNumer(String vedengBatchNumer) {
        this.vedengBatchNumer = vedengBatchNumer == null ? null : vedengBatchNumer.trim();
    }

    /**   剩余库存数量  LAST_STOCK_NUM   **/
    public Integer getLastStockNum() {
        return lastStockNum;
    }

    /**   剩余库存数量  LAST_STOCK_NUM   **/
    public void setLastStockNum(Integer lastStockNum) {
        this.lastStockNum = lastStockNum;
    }

    /**   灭菌批号  STERILZATION_BATCH_NUMBER   **/
    public String getSterilzationBatchNumber() {
        return sterilzationBatchNumber;
    }

    /**   灭菌批号  STERILZATION_BATCH_NUMBER   **/
    public void setSterilzationBatchNumber(String sterilzationBatchNumber) {
        this.sterilzationBatchNumber = sterilzationBatchNumber == null ? null : sterilzationBatchNumber.trim();
    }

    /**   日志类型 0入库 1出库  LOG_TYPE   **/
    public Integer getLogType() {
        return logType;
    }

    /**   日志类型 0入库 1出库  LOG_TYPE   **/
    public void setLogType(Integer logType) {
        this.logType = logType;
    }

    /**   成本价(新)  NEW_COST_PRICE   **/
    public BigDecimal getNewCostPrice() {
        return newCostPrice;
    }

    /**   成本价(新)  NEW_COST_PRICE   **/
    public void setNewCostPrice(BigDecimal newCostPrice) {
        this.newCostPrice = newCostPrice;
    }

    /**   来源信息  TAG_SOURCES   **/
    public String getTagSources() {
        return tagSources;
    }

    /**   来源信息  TAG_SOURCES   **/
    public void setTagSources(String tagSources) {
        this.tagSources = tagSources == null ? null : tagSources.trim();
    }

    /**   专向发货关联VP单号  DEDICATED_BUYORDER_NO   **/
    public String getDedicatedBuyorderNo() {
        return dedicatedBuyorderNo;
    }

    /**   专向发货关联VP单号  DEDICATED_BUYORDER_NO   **/
    public void setDedicatedBuyorderNo(String dedicatedBuyorderNo) {
        this.dedicatedBuyorderNo = dedicatedBuyorderNo == null ? null : dedicatedBuyorderNo.trim();
    }

    public Integer getLockedStatus() {
        return lockedStatus;
    }

    public void setLockedStatus(Integer lockedStatus) {
        this.lockedStatus = lockedStatus;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    /**
     * 添加时间
     */
    private Long createTime;

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Integer getRegistrationNumberId() {
        return registrationNumberId;
    }

    public void setRegistrationNumberId(Integer registrationNumberId) {
        this.registrationNumberId = registrationNumberId;
    }

    public String getFirstEngageId() {
        return firstEngageId;
    }

    public void setFirstEngageId(String firstEngageId) {
        this.firstEngageId = firstEngageId;
    }

    public String getRegistrationNumber() {
        return registrationNumber;
    }

    public void setRegistrationNumber(String registrationNumber) {
        this.registrationNumber = registrationNumber;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }
}