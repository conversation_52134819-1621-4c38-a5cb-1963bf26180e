package com.vedeng.erp.saleorder.dao;

import com.vedeng.erp.saleorder.domain.entity.OrderGoodsLowerPrice;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

public interface OrderGoodsLowerPriceMapper {
    int deleteByPrimaryKey(Long goodsLowerPriceId);

    int insert(OrderGoodsLowerPrice record);

    int insertSelective(OrderGoodsLowerPrice record);

    OrderGoodsLowerPrice selectByPrimaryKey(Long goodsLowerPriceId);

    int updateByPrimaryKeySelective(OrderGoodsLowerPrice record);

    int updateByPrimaryKey(OrderGoodsLowerPrice record);

    int batchInsert(@Param("list") List<OrderGoodsLowerPrice> list);

    List<OrderGoodsLowerPrice> getLowerPriceGoodsList(@Param("orderId") Integer orderId, @Param("orderType") Integer orderType, @Param("orderGoodsId")Integer orderGoodsId);

    int delByOrderIdAndType(@Param("orderId") Integer orderId, @Param("orderType") Integer orderType);

    int addHandleOpinion(@Param("goodsLowerPriceId") Integer goodsLowerPriceId,@Param("handleOpinion") String handleOpinion);

    int addComment(@Param("saleorderId") Integer saleorderId, @Param("comment") String comment);

    BigDecimal getCheckPriceBySaleorderGoodsId(Integer saleorderGoodsId);
}