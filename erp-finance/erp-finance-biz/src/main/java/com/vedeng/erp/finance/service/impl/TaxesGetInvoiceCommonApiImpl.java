package com.vedeng.erp.finance.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.vedeng.erp.finance.domain.dto.TaxesGetInvoiceResponseDto;
import com.vedeng.infrastructure.taxes.base.ITaxesParam;
import com.vedeng.infrastructure.taxes.base.ITaxesResult;
import com.vedeng.erp.finance.service.AbstractTaxesCommonApiHandler;
import com.vedeng.infrastructure.taxes.domain.TaxesCommonApiResult;
import com.vedeng.infrastructure.taxes.enums.TaxesReturnCodeEnum;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 公共-已开发票，发票号查询
 */
@Service
public class TaxesGetInvoiceCommonApiImpl  extends AbstractTaxesCommonApiHandler {

    /**
     * 后置方法
     * @param taxesParam
     * @param executeResult
     * @return
     */
    @Override
    protected ITaxesResult afterExecute(ITaxesParam taxesParam, ITaxesResult executeResult) {
        TaxesCommonApiResult apiResult = (TaxesCommonApiResult) executeResult;
        if (Objects.isNull(apiResult.getData()) && CollectionUtils.isEmpty(apiResult.getData().getData())){
            return setInstance(TaxesReturnCodeEnum.FAIL,null);
        }
        Gson gson = new Gson();
        String toJson = gson.toJson(apiResult.getData().getData());
        List<TaxesGetInvoiceResponseDto> dataList = JSON.parseArray(toJson, TaxesGetInvoiceResponseDto.class);
        if (CollectionUtils.isEmpty(dataList)){
            return setInstance(TaxesReturnCodeEnum.FAIL,null);
        }
        TaxesGetInvoiceResponseDto taxesGetInvoiceResponseDto = dataList.get(0);
        // 接口返回标准不一样，手工拼接成功标识
        setInstance(TaxesReturnCodeEnum.SUCCESS,taxesGetInvoiceResponseDto);
        return taxesGetInvoiceResponseDto;
    }

    private TaxesGetInvoiceResponseDto setInstance(TaxesReturnCodeEnum returnCodeEnum,TaxesGetInvoiceResponseDto responseDto){
        if (Objects.isNull(responseDto)){
            responseDto = new TaxesGetInvoiceResponseDto();
        }
        responseDto.setReturnCode(returnCodeEnum.getCode());
        responseDto.setReturnMessage(returnCodeEnum.getName());
        if (TaxesReturnCodeEnum.SUCCESS.equals(returnCodeEnum)){
            responseDto.setIsSuccess(Boolean.TRUE);
        }
        return responseDto;
    }

}
