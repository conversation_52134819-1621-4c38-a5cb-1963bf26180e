<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="申请付款" scope="application" />
<%@ include file="../../common/common.jsp"%>
<div class="form-list form-tips8">
    <form method="post" id="addBhSaleorderForm" action="./saveApplyPayment.do" onkeydown="if(event.keyCode==13)return false;" onsubmit="return checkForm();">
        <ul>
        	<li>
                <div class="form-tips">
                    <span>*</span>
                    <lable>付款方式</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <ul>
                            <li>
                                <input type="radio" name="isUseBalance" value="2" 
                                <c:if test="${(supplierInfo.amount - occupyAmount) le '0.00' and bankAcceptance ne 1}">checked="checked"</c:if> 
                                <c:if test="${bankAcceptance eq 1}">disabled="disabled"</c:if>
                                onClick="isUseBalanceRadio(this);">
                                <label>银行</label>
                            </li>
                            <li>
                                <input type="radio" name="isUseBalance" value="3" 
                                       <c:if test="${bankAcceptance eq 1}">checked="checked" disabled="disabled"</c:if> 
                                       <c:if test="${bankAcceptance eq 0}">disabled="disabled"</c:if>
                                >
                                <label>银行承兑汇票</label>
                            </li>
                            <li>
                                <input type="radio" name="isUseBalance" value="1"
                                <c:if test="${(supplierInfo.amount - occupyAmount) gt '0.00' and bankAcceptance ne 1}">checked="checked"</c:if> 
                                <c:if test="${(supplierInfo.amount - occupyAmount) le '0.00' or bankAcceptance eq 1}">disabled="disabled"</c:if> 
                                onClick="isUseBalanceRadio(this);">
                                <label>余额 </label>
                                <label style="color: gray;">
                                    账户余额:<fmt:formatNumber type="number" value="${supplierInfo.amount}" pattern="0.00" maxFractionDigits="2" />,
                                    占用余额:<fmt:formatNumber type="number" value="${occupyAmount}" pattern="0.00" maxFractionDigits="2" />,
                                    可用余额:<fmt:formatNumber type="number" value="${supplierInfo.amount - occupyAmount}" pattern="0.00" maxFractionDigits="2" />
                                </label>
                            </li>
                        </ul>
                    </div>
                </div>
            </li>
        	<li>
                <div class="form-tips">
                    <span>*</span>
                    <lable>交易主体</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <ul>
                            <li>
                                <input type="radio" name="traderSubject" value="1" checked="checked">
                                <label>对公</label>
                            </li>
                        </ul>
                    </div>
                </div>
            </li>
            <li>
                <div class="form-tips">
                	<span>*</span>
                    <lable>交易名称</lable>
                </div>
                <div class="f_left ">
                    <c:if test="${type eq 0}">
                        ${buyorderVo.traderName}
                        <input type="hidden" name="traderName" id="traderName" value="${buyorderVo.traderName}">
                        <input type="hidden" name="traderId" id="traderId" value="${buyorderVo.traderId}">
                    </c:if>
                    <c:if test="${type eq 1}">
                         ${buyorderExpenseDto.buyorderExpenseDetailDto.traderName}
                         <input type="hidden" name="traderName" id="traderName" value="${buyorderExpenseDto.buyorderExpenseDetailDto.traderName}">
                    </c:if>
                    <span style="color: #fc5151;" id="traderNameError"></span>
                </div>
            </li>

            <li class="notUseBalance <c:if test="${(supplierInfo.amount - occupyAmount) gt '0.00' and bankAcceptance ne 1}">none</c:if>">
                <div class="form-tips">
                	<span>*</span>
                    <lable>银行帐号</lable>
                </div>
                <div class="f_left ">
                	<c:forEach var="traderFinanceValue" items="${traderFinance}" varStatus="traderFinanceNum">
                    <div class="form-blanks <c:if test='${traderFinanceNum.count != 1}'>mt8</c:if>">
          			 <input value="${traderFinanceValue.traderFinanceId}" name="bank" type="radio"
                            <c:if test='${traderFinanceNum.count == 1}'>checked="checked"</c:if>>
                        <label id="bank_str_${traderFinanceValue.traderFinanceId}">
                                ${traderFinanceValue.bank} / ${traderFinanceValue.bankAccount} / ${traderFinanceValue.bankCode} / ${traderFinanceValue.comments}
                        </label>
                     <input type="hidden" id="bankCode_${traderFinanceValue.traderFinanceId}" value="${traderFinanceValue.bankCode}">
                    </div>
                    </c:forEach>
                    <span style="color: #fc5151; clear:both;" id="bankStrError"></span>
                </div>
            </li>

            <li>
                <div class="form-tips">
                    <lable>申请付款金额</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <input type="text" class="input-small" name="amount" id="amount" readonly="readonly" />
                        <input type="hidden" name="virture_amount" id="virture_amount" readonly="readonly"/>
                        <input type="hidden" name="buyorder_amount" id="buyorder_amount" readonly="readonly"/>
                    </div>
                </div>
            </li>
            <li>
                <div class="form-tips">
                    <lable>内部付款备注</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <c:if test="${type eq 0}">
                            <input type="text" class="input-largest" value="${buyorderVo.paymentComments}" name="comments" id="comments" />
                        </c:if>
                        <c:if test="${type eq 1}">
                            <input type="text" class="input-largest" value="${buyorderExpenseDto.buyorderExpenseDetailDto.paymentComments}" name="comments" id="comments" />
                        </c:if>
                        <label style="color: gray;">告知财务付款需关注的事项</label>
                    </div>
                </div>
            </li>
            <li>
                <div class="form-tips">
                    <lable>银行回单备注</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <input type="text" class="input-largest" name="bankRemark" id="bankRemark" />
                        <label style="color: gray;">写入银行回单的备注信息</label>
                    </div>
                </div>
            </li>
        </ul>
        <c:if test="${type eq 0}">
            <div class="parts">
                <table class="table">
                    <thead>
                        <tr>
                            <th class="wid5">选择</th>
                            <th>产品名称</th>
                            <th class="wid8">品牌</th>
                            <th class="wid8">型号</th>
                            <th class="wid7">单价</th>
                            <th class="wid5">数量</th>
                            <th class="wid4">单位</th>
                            <th>申请数量/已申请数量</th>
                            <th>申请总额/已申请总额</th>
                        </tr>
                    </thead>
                    <tbody>
                        <c:forEach var="bgv" items="${buyorderVo.buyorderGoodsVoList}" varStatus="num">
                        <tr>
                            <td>
                                <input type="checkbox" name="checkedOne" value="${bgv.buyorderGoodsId}" checked="checked">
                                <input type="hidden" name="buyorderGoodsId" value="${bgv.buyorderGoodsId}">
                                <input type="hidden" name="price" value="${bgv.price}">
                            </td>
                            <td class="text-left">
                                <div class="brand-color1"><span class="font-blue cursor-pointer addtitle"
                                        tabTitle='{"num":"viewgoods<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                                    "link":"./goods/goods/viewbaseinfo.do?goodsId=${bgv.goodsId}","title":"产品信息"}'>${bgv.goodsName}</span></div>
                                <div>${bgv.sku}</div>
                            </td>
                            <td>${bgv.brandName}</td>
                            <td>${bgv.model}</td>
                            <td id="price_${bgv.buyorderGoodsId}"><fmt:formatNumber type="number" value="${bgv.price}" pattern="0.00" maxFractionDigits="2" /></td>
                            <td id="allnum_${bgv.buyorderGoodsId}">${bgv.num-bgv.afterSaleUpLimitNum}</td>
                            <td>${bgv.unitName}</td>
                            <c:if test="${bgv.isGift ne 1}">
                                <td><input type="text" style="width:100px;" value="<fmt:formatNumber type="number" value="${bgv.num-bgv.afterSaleUpLimitNum-bgv.applyPaymentNum}" pattern="0.00" maxFractionDigits="2" />" id="num_${bgv.buyorderGoodsId}" name="num" value="" onBlur="changeNum(this,${bgv.buyorderGoodsId});"> /<span id="applyPaymentNum_${bgv.buyorderGoodsId}">${bgv.applyPaymentNum}</span></td>
                            </c:if>
                            <%--采购赠品订单申请数量和已申请数量展示0.00/订单数量--%>
                            <c:if test="${bgv.isGift eq 1}">
                                <td><input type="text" style="width:100px;" value="<fmt:formatNumber type="number" value="0.00" pattern="0.00" maxFractionDigits="2" />" id="num_${bgv.buyorderGoodsId}" name="num" value="" onBlur="changeNum(this,${bgv.buyorderGoodsId});"> /<span id="applyPaymentNum_${bgv.buyorderGoodsId}">${bgv.num}</span></td>
                            </c:if>
                            <td><input type="text" style="width:100px;" value="<fmt:formatNumber type="number" value="${bgv.price*(bgv.num-bgv.afterSaleUpLimitNum) - bgv.applyPaymentAmount}" pattern="0.00" maxFractionDigits="2" />" id="totalAmount_${bgv.buyorderGoodsId}" name="totalAmount" value="" onBlur="changePrice(this,${bgv.buyorderGoodsId});"> /<span id="applyPaymentAmount_${bgv.buyorderGoodsId}">${bgv.applyPaymentAmount}</span></td>
                        </tr>
                        </c:forEach>
                    </tbody>
                </table>
                <div class="table-style4">
                    <div class="allchose">
                        <input type="checkbox" name="checkedAll" checked="checked">
                        <span>全选</span>
                    </div>
                </div>
            </div>
            <%--直属采购费用单申请付款--%>
            <div class="parts">
                <table class="table">
                    <thead>
                    <tr>
                        <th class="wid5">选择</th>
                        <th>直属费用单内虚拟商品</th>
                        <th class="wid8">直属采购费用单号</th>
                        <th class="wid7">单价</th>
                        <th class="wid5">数量</th>
                        <th>申请数量/已申请数量</th>
                        <th>申请总额/已申请总额</th>
                    </tr>
                    </thead>
                    <tbody>
                        <c:forEach var="bgv" items="${buyorderVo.buyorderExpenseDto.buyorderExpenseItemDtos}" varStatus="num">
                            <tr>
                                <td>
                                    <input type="checkbox" name="virture_checkedOne" value="${bgv.buyorderExpenseItemId}" checked="checked">
                                    <input type="hidden" name="buyorderExpenseGoodsId" value="${bgv.buyorderExpenseItemId}">
                                    <input type="hidden" name="virture_price" value="${bgv.buyorderExpenseItemDetailDto.price}">
                                </td>
                                <td class="text-left">
                                    <div class="brand-color1"><span class="font-blue cursor-pointer addtitle"
                                                                    tabTitle='{"num":"viewgoods<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                                        "link":"./goods/goods/viewbaseinfo.do?goodsId=${bgv.goodsId}","title":"产品信息"}'>${bgv.buyorderExpenseItemDetailDto.goodsName}</span></div>
                                    <div>${bgv.sku}</div>
                                </td>

                                <td>
                                    <div class="brand-color1"><span class="font-blue cursor-pointer addtitle"
                                                                    tabTitle='{"num":"viewbuyorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                                        "link":"./buyorderExpense/details.do?buyorderExpenseId=${buyorderVo.buyorderExpenseDto.buyorderExpenseId}","title":"采购费用详情信息"}'>${buyorderVo.buyorderExpenseDto.buyorderExpenseNo}</span></div></td>
                                <td id="virture_price_${bgv.buyorderExpenseItemId}"><fmt:formatNumber type="number" value="${bgv.buyorderExpenseItemDetailDto.price}" pattern="0.00" maxFractionDigits="2" /></td>
                                <td id="virture_allnum_${bgv.buyorderExpenseItemId}">${bgv.num-bgv.afterReturnNum}</td>
                                <td><input type="text" style="width:100px;" value="<fmt:formatNumber type="number" value="${bgv.num-bgv.afterReturnNum-bgv.passedPayApplyNum}" pattern="0.00" maxFractionDigits="2" />"
                                           id="virture_num_${bgv.buyorderExpenseItemId}" name="virture_num" value=""
                                           onBlur="expenseChangeNum(this,${bgv.buyorderExpenseItemId});"> /<span id="applyPaymentNum_${bgv.buyorderExpenseItemId}">${bgv.passedPayApplyNum}</span></td>
                                <td><input type="text" style="width:100px;" value="<fmt:formatNumber type="number" value="${bgv.buyorderExpenseItemDetailDto.price*(bgv.num-bgv.afterReturnNum) - bgv.passedPayApplyAmount}" pattern="0.00" maxFractionDigits="2" />"
                                           id="virture_totalAmount_${bgv.buyorderExpenseItemId}" name="virture_totalAmount" value=""
                                           onBlur="expenseChangePrice(this,${bgv.buyorderExpenseItemId});"> /<span id="virture_applyPaymentAmount_${bgv.buyorderExpenseItemId}">${bgv.passedPayApplyAmount}</span></td>
                            </tr>
                        </c:forEach>
                    </tbody>
                </table>
                <div class="table-style4">
                    <div class="allchose">
                        <input type="checkbox" name="virture_checkedAll" checked="checked">
                        <span>全选</span>
                    </div>
                </div>
                <input type="hidden" name="relatedId" id="relatedId" value="${buyorderVo.buyorderId}">
                <input type="hidden" name="buyorderExpenseRelatedId" id="buyorderExpenseRelatedId" value="${buyorderVo.buyorderExpenseDto.buyorderExpenseId}">
            </div>
        </c:if>
        <c:if test="${type eq 1}">
            <%--生效后新增的费用单申请付款--%>
            <div class="parts">
                <table class="table">
                    <thead>
                    <tr>
                        <th class="wid5">选择</th>
                        <th>产品名称</th>
                        <th class="wid7">单价</th>
                        <th class="wid5">数量</th>
                        <th>申请数量/已申请数量</th>
                        <th>申请总额/已申请总额</th>
                    </tr>
                    </thead>
                    <tbody>
                    <c:forEach var="bgv" items="${buyorderExpenseDto.buyorderExpenseItemDtos}" varStatus="num">
                        <tr>
                            <td>
                                <input type="checkbox" name="virture_checkedOne" value="${bgv.buyorderExpenseItemId}" checked="checked">
                                <input type="hidden" name="buyorderExpenseGoodsId" value="${bgv.buyorderExpenseItemId}">
                                <input type="hidden" name="virture_price" value="${bgv.buyorderExpenseItemDetailDto.price}">
                            </td>
                            <td class="text-left">
                                <div class="brand-color1"><span class="font-blue cursor-pointer addtitle"
                                                                tabTitle='{"num":"viewgoods<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                                        "link":"./goods/goods/viewbaseinfo.do?goodsId=${bgv.goodsId}","title":"产品信息"}'>${bgv.buyorderExpenseItemDetailDto.goodsName}</span></div>
                                <div>${bgv.sku}</div>
                            </td>
                            <td id="virture_price_${bgv.buyorderExpenseItemId}"><fmt:formatNumber type="number" value="${bgv.buyorderExpenseItemDetailDto.price}" pattern="0.00" maxFractionDigits="2" /></td>
                            <td id="virture_allnum_${bgv.buyorderExpenseItemId}">${bgv.num-bgv.afterReturnNum}</td>
                            <td><input type="text" style="width:100px;" value="<fmt:formatNumber type="number" value="${bgv.num-bgv.afterReturnNum-bgv.passedPayApplyNum}" pattern="0.00" maxFractionDigits="2" />"
                                       id="virture_num_${bgv.buyorderExpenseItemId}" name="virture_num" value=""
                                       onBlur="expenseChangeNum(this,${bgv.buyorderExpenseItemId});"> /<span id="applyPaymentNum_${bgv.buyorderExpenseItemId}">${bgv.passedPayApplyNum}</span></td>
                            <td><input type="text" style="width:100px;" value="<fmt:formatNumber type='number' value='${bgv.buyorderExpenseItemDetailDto.price*(bgv.num-bgv.afterReturnNum) - bgv.passedPayApplyAmount}' pattern='0.00' maxFractionDigits='2'/>"
                                       id="virture_totalAmount_${bgv.buyorderExpenseItemId}" name="virture_totalAmount" value=""
                                       onBlur="expenseChangePrice(this,${bgv.buyorderExpenseItemId});"> /<span id="virture_applyPaymentAmount_${bgv.buyorderExpenseItemId}">${bgv.passedPayApplyAmount}</span></td>
                        </tr>
                    </c:forEach>
                    </tbody>
                </table>
                <div class="table-style4">
                    <div class="allchose">
                        <input type="checkbox" name="virture_checkedAll" checked="checked">
                        <span>全选</span>
                    </div>
                </div>
            </div>
            <input type="hidden" name="buyorderExpenseRelatedId" id="buyorderExpenseRelatedId" value="${buyorderExpenseDto.buyorderExpenseId}">
        </c:if>

        <div class="add-tijiao tcenter">
        	<input type="hidden" name="traderMode" id="traderMode"
                   value="<c:if test="${(supplierInfo.amount - occupyAmount) gt '0.00' and bankAcceptance ne 1}">528</c:if>
                            <c:if test="${(supplierInfo.amount - occupyAmount) le '0.00' and bankAcceptance ne 1}">521</c:if>
                            <c:if test="${bankAcceptance eq 1}">10001</c:if>">
        	<input type="hidden" name="formToken" value="${formToken}"/>
        	<input type="hidden" name="bank" id="bank" value="">
        	<input type="hidden" name="bankCode" id="bankCode" value="">
        	<input type="hidden" name="bankAccount" id="bankAccount" value="">
        	<input type="hidden" name="supplyAmount" id="supplyAmount" value="${supplierInfo.amount}">
        	<input type="hidden" name="occupyAmount" id="occupyAmount" value="${occupyAmount}">
        	<input type="hidden" name="traderSupplierId" id="traderSupplierId" value="${supplierInfo.traderSupplierId}">
            <input type="hidden" name="isHavePayed" id="isHavePayed" value="${isHavePayed}">
            <input type="hidden" name="virtureIsHavePayed" id="virtureIsHavePayed" value="${virtureIsHavePayed}">
            <input type="hidden" name="maxPrePaymentAmount" id="maxPrePaymentAmount" value="${maxPrePaymentAmount}">
            <input type="hidden" name="maxVirturePrePaymentAmount" id="maxVirturePrePaymentAmount" value="${virtureMaxPrePaymentAmount}"/>
            <input type="hidden" name="payApplyType" id="payApplyType" value="${type}"/>
            <button type="submit" id="apply_payment_submit">提交</button>
        </div>
    </form>
</div>
<script type="text/javascript" src='<%= basePath %>static/js/order/buyorder/apply_payment.js?rnd=${resourceVersionKey}'></script>
</body>
</html>
