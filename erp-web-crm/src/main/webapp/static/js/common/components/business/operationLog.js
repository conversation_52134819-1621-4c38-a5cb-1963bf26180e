// 操作记录
Vue.component('operation-log', {
    template: `
        <div class="records-panel">
            <div class="panel-title">操作记录</div>

            <div class="panel-wrap" id="operation-component">
                <div class="operation-records" v-show="!pageLoading">
                    <div class="list" v-if="list.length">
                        <div class="item" v-for="(item, index) in list" :key="index">
                            <div class="row">
                                <div class="creator">
                                    <img :src="item.aliasHeadPicture || GLOBAL.defaultAvatar" class="icon"/>
                                    <div class="name">{{ item.creatorName }}</div>
                                </div>
                                <div class="time">{{ item.operationTime }}</div>
                            </div>
                            <div class="text-line-7">{{ item.logContent }}</div>
                        </div>
                    </div>
                    <div class="panel-null-data" v-else>
                        <i class="vd-ui_icon icon-info1 icon"></i>
                        <p class="font">历史数据暂无操作记录</p>
                    </div>
                </div>
            </div>
        </div>
    `,
    props: {
        // 业务类型 01线索池 02商机库 03 报价单
        bizTypeEnum: {
            type: String
        },
        // 业务id
        relatedId: {
            type: [String, Number]
        },
        // 报价单id - 商机详情用
        quoteorderId: {
            type: [String, Number]
        },
        url: ''
    },
    data () {
        return {
            pageLoading: true,
            list: [],
            pageNum: 1,
            pageSize: 10,
            isEnd: false,
            isLoading: false,
        }
    },
    mounted () {
        this.initData();
        let scrollEle = document.getElementById('operation-component');
        scrollEle && scrollEle.addEventListener('scroll', this.scrollFun);
    },
    methods: {
        initData () {
            this.pageLoading = true;

            if(this.url) {
                this.$axios.post(this.url, {
                    "pageNum": 1,
                    "pageSize": this.pageSize,
                    param: this.relatedId
                }).then(({data}) => {
                    if (data.success) {
                        let total = data.data.total || 0;
                        let list = data.data.list || [];
                        this.list = list;
                        this.isEnd = (this.list.length >= total) || (list.length < this.pageSize);
                    }
                    this.isLoading = false;
                    this.pageLoading = false;
                });
            } else {
                let reqData = {
                    "param": {
                        "bizList": [{
                            bizTypeEnum: this.bizTypeEnum,
                            bizId: this.relatedId
                        }]
                    },
                    "pageNum": 1,
                    "pageSize": this.pageSize
                };
                if (this.quoteorderId) {
                    reqData.param.bizList.push({
                        bizTypeEnum: '03',
                        bizId: this.quoteorderId
                    });
                }
                this.$axios.post('/crm/operationLog/public/page', reqData).then(({data}) => {
                    if (data.success) {
                        let total = data.data.total || 0;
                        let list = data.data.list || [];
                        this.list = list;
                        this.isEnd = (this.list.length >= total) || (list.length < this.pageSize);
                    }
                    this.isLoading = false;
                    this.pageLoading = false;
                });
            }
        },
        scrollFun () {
            let scrollEle = document.getElementById('operation-component');
            let scrollTop = scrollEle.scrollTop; // 滑出屏幕之外的高度
            let scrollH = scrollEle.scrollHeight; // 文档总高度
            let screenH = scrollEle.clientHeight; // 屏幕可视高度

            if (scrollH - scrollTop - screenH < 100 && !this.isLoading && !this.isEnd) {
                this.isLoading = true;

                if(this.url) {
                    this.$axios.post(this.url, {
                        "pageNum": ++this.pageNum,
                        "pageSize": this.pageSize,
                        param: this.relatedId
                    }).then(({data}) => {
                        if (data.success) {
                            let total = data.data.total || 0;
                            let list = data.data.list || [];
                            this.list = this.list.concat(list);
                            this.isEnd = (this.list.length >= total) || (list.length < this.pageSize);
                        }
                        this.isLoading = false;
                        this.pageLoading = false;
                    });
                } else {

                    let reqData = {
                        "pageNum": ++this.pageNum,
                        "pageSize": this.pageSize,
                        "param": {
                            "bizList": [{
                                bizTypeEnum: this.bizTypeEnum,
                                bizId: this.relatedId
                            }]
                        },
                    };
                    if (this.quoteorderId) {
                        reqData.param.bizList.push({
                            bizTypeEnum: '03',
                            bizId: this.quoteorderId
                        });
                    }
                    this.$axios.post('/crm/operationLog/public/page', reqData).then(({data}) => {
                        if (data.success) {
                            let total = data.data.total || 0;
                            let list = data.data.list || [];
                            this.list = this.list.concat(list);
                            this.isEnd = (this.list.length >= total) || (list.length < this.pageSize);
                        }
                        this.isLoading = false;
                    });
                }

            }
        }
    }
})