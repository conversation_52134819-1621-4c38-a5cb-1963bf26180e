package com.vedeng.erp.saleorder.buzlogic.create.impl;

import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.erp.saleorder.buzlogic.create.CreateSaleOrderBuzLogic;
import com.vedeng.erp.saleorder.service.BaseSaleOrderService;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.service.OrderCommonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 医械购线下订单创建 zxf
 */
@Service
public class ZxfCreateSalesOrderBuzLogic extends CreateSaleOrderBuzLogic {

    @Autowired
    private BaseSaleOrderService baseSaleOrderService;

    public ZxfCreateSalesOrderBuzLogic(){
        List<String> sequence = new ArrayList<>();
        sequence.add("createOrder");
        sequence.add("syncSaleorderDate");
        super.setSequence(sequence);
    }

    public ResultInfo<?> saveCopyOrder(Saleorder saleorder){
        baseSaleOrderService.saveCopyOrder(saleorder);
        return new ResultInfo(0,"创建销售单成功");
    }

    @Override
    public ResultInfo run(Saleorder saleorder){
        new ZxfCreateSalesOrderBuzLogic();
        ResultInfo result = new ResultInfo(0,"操作成功");
        for (String methodName : getSequence()){
            switch (methodName) {
                case "createOrder":
                    if(ErpConst.ZERO.equals(result.getCode()) && ErpConst.ONE.equals(saleorder.getIsCopy())) {
                        result = saveCopyOrder(saleorder);
                    }else if(ErpConst.ZERO.equals(result.getCode())){
                        result = super.createOrder(saleorder);
                    }
                    break;
                case "syncSaleorderDate":
                    if(ErpConst.ZERO.equals(result.getCode())){
                        result = super.syncSaleorderDate(saleorder);
                    }
                    break;
                default:
                    break;
            }
        }
        return result;
    }
}
