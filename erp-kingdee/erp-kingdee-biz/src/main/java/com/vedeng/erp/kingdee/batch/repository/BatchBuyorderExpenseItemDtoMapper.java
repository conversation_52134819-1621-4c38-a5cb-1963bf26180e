package com.vedeng.erp.kingdee.batch.repository;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import com.vedeng.erp.kingdee.batch.dto.BatchBuyorderExpenseItemDto;
import java.util.Collection;

public interface BatchBuyorderExpenseItemDtoMapper {

    List<BatchBuyorderExpenseItemDto> findByBuyorderExpenseItemIdIn(@Param("buyorderExpenseItemIdCollection")Collection<Integer> buyorderExpenseItemIdCollection);

    /**
     * 根据sku查金蝶编码
     * @param sku
     * @return
     */
    String selectUnitKingDeeNo(@Param("sku") String sku);

}