<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.order.dao.SaleorderDeliveryNoticeGoodsMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.order.model.SaleorderDeliveryNoticeGoods">
    <id column="DELIVERY_NOTICE_GOODS_ID" jdbcType="INTEGER" property="deliveryNoticeGoodsId" />
    <result column="DELIVERY_NOTICE_ID" jdbcType="INTEGER" property="deliveryNoticeId" />
    <result column="SALEORDER_GOODS_ID" jdbcType="INTEGER" property="saleorderGoodsId" />
    <result column="SKU" jdbcType="VARCHAR" property="sku" />
    <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId" />
    <result column="ORDER_ID" jdbcType="INTEGER" property="orderId" />
    <result column="NUM" jdbcType="INTEGER" property="num" />
    <result column="OCCUPY_NUM" jdbcType="INTEGER" property="occupyNum" />
    <result column="ARRIVAL_NUM" jdbcType="INTEGER" property="arrivalNum" />
    <result column="DELETE_STATE" jdbcType="TINYINT" property="deleteState" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    DELIVERY_NOTICE_GOODS_ID, DELIVERY_NOTICE_ID, SALEORDER_GOODS_ID, GOODS_ID, ORDER_ID,
    NUM, OCCUPY_NUM, ARRIVAL_NUM, DELETE_STATE,SKU
  </sql>
  <select id="selectByExample" parameterType="com.vedeng.order.model.SaleorderDeliveryNoticeGoodsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from T_SALEORDER_DELIVERY_NOTICE_GOODS
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_SALEORDER_DELIVERY_NOTICE_GOODS
    where DELIVERY_NOTICE_GOODS_ID = #{deliveryNoticeGoodsId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_SALEORDER_DELIVERY_NOTICE_GOODS
    where DELIVERY_NOTICE_GOODS_ID = #{deliveryNoticeGoodsId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.order.model.SaleorderDeliveryNoticeGoodsExample">
    delete from T_SALEORDER_DELIVERY_NOTICE_GOODS
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="DELIVERY_NOTICE_GOODS_ID" keyProperty="deliveryNoticeGoodsId" parameterType="com.vedeng.order.model.SaleorderDeliveryNoticeGoods" useGeneratedKeys="true">
    insert into T_SALEORDER_DELIVERY_NOTICE_GOODS (DELIVERY_NOTICE_ID, SALEORDER_GOODS_ID,
      GOODS_ID, ORDER_ID, NUM,
      OCCUPY_NUM, ARRIVAL_NUM, DELETE_STATE,SKU
      )
    values (#{deliveryNoticeId,jdbcType=INTEGER}, #{saleorderGoodsId,jdbcType=INTEGER},
      #{goodsId,jdbcType=INTEGER}, #{orderId,jdbcType=INTEGER}, #{num,jdbcType=INTEGER},
      #{occupyNum,jdbcType=INTEGER}, #{arrivalNum,jdbcType=INTEGER}, #{deleteState,jdbcType=TINYINT},#{sku,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="DELIVERY_NOTICE_GOODS_ID" keyProperty="deliveryNoticeGoodsId" parameterType="com.vedeng.order.model.SaleorderDeliveryNoticeGoods" useGeneratedKeys="true">
    insert into T_SALEORDER_DELIVERY_NOTICE_GOODS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="deliveryNoticeId != null">
        DELIVERY_NOTICE_ID,
      </if>
      <if test="saleorderGoodsId != null">
        SALEORDER_GOODS_ID,
      </if>
      <if test="goodsId != null">
        GOODS_ID,
      </if>
      <if test="orderId != null">
        ORDER_ID,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="occupyNum != null">
        OCCUPY_NUM,
      </if>
      <if test="arrivalNum != null">
        ARRIVAL_NUM,
      </if>
      <if test="deleteState != null">
        DELETE_STATE,
      </if>
      <if test="sku != null">
        SKU,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="deliveryNoticeId != null">
        #{deliveryNoticeId,jdbcType=INTEGER},
      </if>
      <if test="saleorderGoodsId != null">
        #{saleorderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null">
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="occupyNum != null">
        #{occupyNum,jdbcType=INTEGER},
      </if>
      <if test="arrivalNum != null">
        #{arrivalNum,jdbcType=INTEGER},
      </if>
      <if test="deleteState != null">
        #{deleteState,jdbcType=TINYINT},
      </if>
      <if test="sku !=null ">
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.order.model.SaleorderDeliveryNoticeGoodsExample" resultType="java.lang.Long">
    select count(*) from T_SALEORDER_DELIVERY_NOTICE_GOODS
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update T_SALEORDER_DELIVERY_NOTICE_GOODS
    <set>
      <if test="record.deliveryNoticeGoodsId != null">
        DELIVERY_NOTICE_GOODS_ID = #{record.deliveryNoticeGoodsId,jdbcType=INTEGER},
      </if>
      <if test="record.deliveryNoticeId != null">
        DELIVERY_NOTICE_ID = #{record.deliveryNoticeId,jdbcType=INTEGER},
      </if>
      <if test="record.saleorderGoodsId != null">
        SALEORDER_GOODS_ID = #{record.saleorderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="record.goodsId != null">
        GOODS_ID = #{record.goodsId,jdbcType=INTEGER},
      </if>
      <if test="record.orderId != null">
        ORDER_ID = #{record.orderId,jdbcType=INTEGER},
      </if>
      <if test="record.num != null">
        NUM = #{record.num,jdbcType=INTEGER},
      </if>
      <if test="record.occupyNum != null">
        OCCUPY_NUM = #{record.occupyNum,jdbcType=INTEGER},
      </if>
      <if test="record.arrivalNum != null">
        ARRIVAL_NUM = #{record.arrivalNum,jdbcType=INTEGER},
      </if>
      <if test="record.deleteState != null">
        DELETE_STATE = #{record.deleteState,jdbcType=TINYINT},
      </if>
      <if test="sku !=null ">
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update T_SALEORDER_DELIVERY_NOTICE_GOODS
    set DELIVERY_NOTICE_GOODS_ID = #{record.deliveryNoticeGoodsId,jdbcType=INTEGER},
    DELIVERY_NOTICE_ID = #{record.deliveryNoticeId,jdbcType=INTEGER},
    SALEORDER_GOODS_ID = #{record.saleorderGoodsId,jdbcType=INTEGER},
    GOODS_ID = #{record.goodsId,jdbcType=INTEGER},
    ORDER_ID = #{record.orderId,jdbcType=INTEGER},
    NUM = #{record.num,jdbcType=INTEGER},
    OCCUPY_NUM = #{record.occupyNum,jdbcType=INTEGER},
    ARRIVAL_NUM = #{record.arrivalNum,jdbcType=INTEGER},
    DELETE_STATE = #{record.deleteState,jdbcType=TINYINT},
    SKU = #{sku,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.order.model.SaleorderDeliveryNoticeGoods">
    update T_SALEORDER_DELIVERY_NOTICE_GOODS
    <set>
      <if test="deliveryNoticeId != null">
        DELIVERY_NOTICE_ID = #{deliveryNoticeId,jdbcType=INTEGER},
      </if>
      <if test="saleorderGoodsId != null">
        SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null">
        GOODS_ID = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        ORDER_ID = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="occupyNum != null">
        OCCUPY_NUM = #{occupyNum,jdbcType=INTEGER},
      </if>
      <if test="arrivalNum != null">
        ARRIVAL_NUM = #{arrivalNum,jdbcType=INTEGER},
      </if>
      <if test="deleteState != null">
        DELETE_STATE = #{deleteState,jdbcType=TINYINT},
      </if>
      <if test="sku !=null ">
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
    </set>
    where DELIVERY_NOTICE_GOODS_ID = #{deliveryNoticeGoodsId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.order.model.SaleorderDeliveryNoticeGoods">
    update T_SALEORDER_DELIVERY_NOTICE_GOODS
    set DELIVERY_NOTICE_ID = #{deliveryNoticeId,jdbcType=INTEGER},
      SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER},
      GOODS_ID = #{goodsId,jdbcType=INTEGER},
      ORDER_ID = #{orderId,jdbcType=INTEGER},
      NUM = #{num,jdbcType=INTEGER},
      OCCUPY_NUM = #{occupyNum,jdbcType=INTEGER},
      ARRIVAL_NUM = #{arrivalNum,jdbcType=INTEGER},
      DELETE_STATE = #{deleteState,jdbcType=TINYINT},
      SKU = #{sku,jdbcType=VARCHAR}
    where DELIVERY_NOTICE_GOODS_ID = #{deliveryNoticeGoodsId,jdbcType=INTEGER}
  </update>
</mapper>