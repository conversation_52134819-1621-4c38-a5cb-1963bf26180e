<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.firstengage.dao.FirstengageBrandMapper">
	<select id="getBrandByKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from T_BRAND
		where BRAND_ID = #{brandId,jdbcType=INTEGER}
		and IS_DELETE = 0

	</select>

	<resultMap id="BaseResultMap" type="com.vedeng.firstengage.model.FirstengageBrand">
		<id column="BRAND_ID" property="brandId" jdbcType="INTEGER" />
		<result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
		<result column="BRAND_NATURE" property="brandNature" jdbcType="INTEGER" />
		<result column="BRAND_NAME" property="brandName" jdbcType="VARCHAR" />
		<result column="BRAND_NAME_CN" property="brandNameCn" jdbcType="VARCHAR" />
		<result column="BRAND_NAME_EN" property="brandNameEn" jdbcType="VARCHAR" />
		<result column="MANUFACTURER" property="manufacturer" jdbcType="VARCHAR" />
		<result column="BRAND_WEBSITE" property="brandWebsite" jdbcType="VARCHAR" />
		<result column="OWNER" property="owner" jdbcType="VARCHAR" />
		<result column="LOGO_DOMAIN" property="logoDomain" jdbcType="VARCHAR" />
		<result column="LOGO_URI" property="logoUri" jdbcType="VARCHAR" />
		<result column="SORT" property="sort" jdbcType="INTEGER" />
		<result column="INITIAL_CN" property="initialCn" jdbcType="VARCHAR" />
		<result column="INITIAL_EN" property="initialEn" jdbcType="VARCHAR" />
		<result column="DESCRIPTION" property="description" jdbcType="VARCHAR" />
		<result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR" />
		<result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
		<result column="CREATOR" property="creator" jdbcType="INTEGER" />
		<result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
		<result column="UPDATER" property="updater" jdbcType="INTEGER" />
		<result column="GOODS_NUM" property="goodsNum" jdbcType="INTEGER" />
		<result column="SOURCE" property="source" jdbcType="INTEGER" />
		<result column="FILE_NAME" property="fileName" jdbcType="VARCHAR" />
		<result column="IS_OWN_BRAND" property="isOwnBrand" jdbcType="TINYINT" />
	</resultMap>

	<sql id="Base_Column_List">
		BRAND_ID, COMPANY_ID, BRAND_NATURE,BRAND_NAME_CN, BRAND_NAME_EN, BRAND_NAME, MANUFACTURER, BRAND_WEBSITE, OWNER, LOGO_DOMAIN, LOGO_URI, SORT,
		INITIAL_CN, INITIAL_EN, DESCRIPTION, ADD_TIME, CREATOR, MOD_TIME, UPDATER, SOURCE,COMPANY_NAME,FILE_NAME,IS_OWN_BRAND
	</sql>

	<update id="delBrand" parameterType="com.vedeng.firstengage.model.FirstengageBrand">
			UPDATE T_BRAND SET IS_DELETE = 1,COMMENTS = #{comment}	where BRAND_ID = #{brandId,jdbcType=INTEGER}
	</update>

	<!-- 品牌信息添加保存 -->
	<insert id="addBrand" parameterType="com.vedeng.firstengage.model.FirstengageBrand" useGeneratedKeys="true" keyProperty="brandId">
		insert into T_BRAND
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="brandId != null">
				BRAND_ID,
			</if>
			<if test="companyId != null">
				COMPANY_ID,
			</if>
			<if test="brandNature != null">
				BRAND_NATURE,
			</if>
			<if test="brandName != null">
				BRAND_NAME,
			</if>
			<if test="brandNameCn != null">
				BRAND_NAME_CN,
			</if>
			<if test="brandNameEn != null">
				BRAND_NAME_EN,
			</if>

			<if test="manufacturer != null">
				MANUFACTURER,
			</if>

			<if test="brandWebsite != null">
				BRAND_WEBSITE,
			</if>
			<if test="owner != null">
				OWNER,
			</if>
			<if test="logoDomain != null">
				LOGO_DOMAIN,
			</if>
			<if test="logoUri != null">
				LOGO_URI,
			</if>
			<if test="sort != null">
				SORT,
			</if>
			<if test="initialCn != null">
				INITIAL_CN,
			</if>
			<if test="initialEn != null">
				INITIAL_EN,
			</if>
			<if test="description != null">
				DESCRIPTION,
			</if>
			<if test="addTime != null">
				ADD_TIME,
			</if>
			<if test="creator != null">
				CREATOR,
			</if>
			<if test="modTime != null">
				MOD_TIME,
			</if>
			<if test="updater != null">
				UPDATER,
			</if>
			<if test="source != null">
				SOURCE,
			</if>
			<if test="companyName != null">
				company_name,
			</if>
			<if test="fileName != null">
				FILE_NAME,
			</if>
			<if test="isOwnBrand != null">
				IS_OWN_BRAND,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="brandId != null">
				#{brandId,jdbcType=INTEGER},
			</if>
			<if test="companyId != null">
				#{companyId,jdbcType=INTEGER},
			</if>
			<if test="brandNature != null">
				#{brandNature,jdbcType=INTEGER},
			</if>
			<if test="brandName != null">
				#{brandName,jdbcType=VARCHAR},
			</if>
			<if test="brandNameCn != null">
				#{brandNameCn,jdbcType=VARCHAR},
			</if>
			<if test="brandNameEn != null">
				#{brandNameEn,jdbcType=VARCHAR},
			</if>
			<if test="manufacturer != null">
				#{manufacturer,jdbcType=VARCHAR},
			</if>
			<if test="brandWebsite != null">
				#{brandWebsite,jdbcType=VARCHAR},
			</if>
			<if test="owner != null">
				#{owner,jdbcType=VARCHAR},
			</if>
			<if test="logoDomain != null">
				#{logoDomain,jdbcType=VARCHAR},
			</if>
			<if test="logoUri != null">
				#{logoUri,jdbcType=VARCHAR},
			</if>
			<if test="sort != null">
				#{sort,jdbcType=INTEGER},
			</if>
			<if test="initialCn != null">
				#{initialCn,jdbcType=VARCHAR},
			</if>
			<if test="initialEn != null">
				#{initialEn,jdbcType=VARCHAR},
			</if>
			<if test="description != null">
				#{description,jdbcType=VARCHAR},
			</if>
			<if test="addTime != null">
				#{addTime,jdbcType=BIGINT},
			</if>
			<if test="creator != null">
				#{creator,jdbcType=INTEGER},
			</if>
			<if test="modTime != null">
				#{modTime,jdbcType=BIGINT},
			</if>
			<if test="updater != null">
				#{updater,jdbcType=INTEGER},
			</if>
			<if test="source != null">
				#{source,jdbcType=INTEGER},
			</if>
			<if test="companyName != null">
				#{companyName,jdbcType=VARCHAR},
			</if>
			<if test="fileName != null">
				#{fileName,jdbcType=VARCHAR},
			</if>
			<if test="isOwnBrand != null">
				#{isOwnBrand,jdbcType=TINYINT},
			</if>
		</trim>
	</insert>
	
	<update id="editBrand" parameterType="com.vedeng.firstengage.model.FirstengageBrand">
		update T_BRAND
		<set>
			<if test="companyId != null">
				COMPANY_ID = #{companyId,jdbcType=INTEGER},
			</if>
			<if test="brandNature != null">
				BRAND_NATURE = #{brandNature,jdbcType=INTEGER},
			</if>
			<if test="brandName != null">
				BRAND_NAME = #{brandName,jdbcType=VARCHAR},
			</if>
			<if test="brandNameCn != null">
				BRAND_NAME_Cn = #{brandNameCn,jdbcType=VARCHAR},
			</if>
			<if test="brandNameEn != null">
				BRAND_NAME_EN = #{brandNameEn,jdbcType=VARCHAR},
			</if>
			<if test="manufacturer != null">
				MANUFACTURER = #{manufacturer,jdbcType=VARCHAR},
			</if>

			<if test="brandWebsite != null">
				BRAND_WEBSITE = #{brandWebsite,jdbcType=VARCHAR},
			</if>
			<if test="owner != null">
				OWNER = #{owner,jdbcType=VARCHAR},
			</if>
			<if test="logoDomain != null">
				LOGO_DOMAIN = #{logoDomain,jdbcType=VARCHAR},
			</if>
			<if test="logoUri != null">
				LOGO_URI = #{logoUri,jdbcType=VARCHAR},
			</if>
			<if test="sort != null">
				SORT = #{sort,jdbcType=INTEGER},
			</if>
			<if test="initialCn != null">
				INITIAL_CN = #{initialCn,jdbcType=VARCHAR},
			</if>
			<if test="initialEn != null">
				INITIAL_EN = #{initialEn,jdbcType=VARCHAR},
			</if>
			<if test="description != null">
				DESCRIPTION = #{description,jdbcType=VARCHAR},
			</if>
			<if test="addTime != null">
				ADD_TIME = #{addTime,jdbcType=BIGINT},
			</if>
			<if test="creator != null">
				CREATOR = #{creator,jdbcType=INTEGER},
			</if>
			<if test="modTime != null">
				MOD_TIME = #{modTime,jdbcType=BIGINT},
			</if>
			<if test="updater != null">
				UPDATER = #{updater,jdbcType=INTEGER},
			</if>
			<if test="source != null">
				SOURCE = #{source,jdbcType=INTEGER},
			</if>
			<if test="companyName != null">
				COMPANY_NAME=#{companyName,jdbcType=VARCHAR},
			</if>
			<if test="fileName != null">
				FILE_NAME=#{fileName,jdbcType=VARCHAR},
			</if>
			<if test="isOwnBrand != null">
				IS_OWN_BRAND=#{isOwnBrand,jdbcType=TINYINT},
			</if>
		</set>
		where BRAND_ID = #{brandId,jdbcType=INTEGER}
	</update>

	<!-- 获取全部品牌信息 -->
	<select id="getAllBrand" resultMap="BaseResultMap" parameterType="com.vedeng.firstengage.model.FirstengageBrand">
		select
		<include refid="Base_Column_List" />
		from T_BRAND
		where
		1=1
		<if test="brandName != null">
			and BRAND_NAME like CONCAT('%',#{brandName},'%' )
		</if>
		<if test="companyId != null">
			and COMPANY_ID = #{companyId,jdbcType=INTEGER}
		</if>
		
		order by SORT desc
	</select>
	
	<!-- 查询品牌信息列表（分页） -->
	<!-- <include refid="Base_Column_List" /> -->
	<select id="getBrandlistpage" parameterType="java.util.Map" resultMap="BaseResultMap">
		SELECT IFNULL(B.GOODS_NUM, 0) AS GOODS_NUM, A.*
		FROM T_BRAND A
		     LEFT JOIN (SELECT A.BRAND_ID, COUNT(1) AS GOODS_NUM
		                FROM T_GOODS A
		                WHERE A.IS_DISCARD = 0
		                GROUP BY A.BRAND_ID) B
		        ON A.BRAND_ID = B.BRAND_ID
		<where>
			IS_DELETE = 0
			<if test="brand.searchStatus == 1 and brand.keyWords != null and brand.keyWords != ''">
				AND (A.BRAND_NAME like CONCAT('%',#{brand.keyWords},'%' ) OR A.BRAND_ID like CONCAT('%',#{brand.keyWords},'%' ) OR A.BRAND_NAME_EN like CONCAT('%',#{brand.keyWords},'%' ))
			</if>
			<if test="brand.searchStatus == 2 and brand.keyWords != null and brand.keyWords != ''">
				AND (A.BRAND_NAME like CONCAT('%',#{brand.keyWords},'%') OR A.BRAND_NAME_EN like CONCAT('%',#{brand.keyWords},'%' ))
			</if>
			<if test="brand.searchStatus == 3 and brand.keyWords != null and brand.keyWords != ''">
				AND  A.BRAND_ID like CONCAT('%',#{brand.keyWords},'%' )
			</if>
			<if test="brand.companyId != null and brand.companyId != 0">
				AND COMPANY_ID = #{brand.companyId,jdbcType=INTEGER}
			</if>
			<if test="brand.brandNature != null and brand.brandNature != 0">
				and BRAND_NATURE = #{brand.brandNature,jdbcType=INTEGER}
			</if>
			<if test="brand.isOwnBrand != null and brand.isOwnBrand != ''">
				and IS_OWN_BRAND = #{brand.isOwnBrand,jdbcType=TINYINT}
			</if>
			<if test="brand.effectStartDate != null and brand.effectStartDate != ''">
				and ADD_TIME <![CDATA[ > ]]> UNIX_TIMESTAMP(STR_TO_DATE(#{brand.effectStartDate}, '%Y-%m-%d'))*1000
			</if>
			<if test="brand.effectEndDate != null and brand.effectEndDate != ''">
				and ADD_TIME <![CDATA[ < ]]> UNIX_TIMESTAMP(DATE_ADD(STR_TO_DATE(#{brand.effectEndDate}, '%Y-%m-%d') ,INTERVAL 1 DAY) )*1000
			</if>	
		</where>
		<if test="brand.timeSort != null and brand.timeSort == 1">
			order by MOD_TIME DESC
		</if>	
		<if test="brand.timeSort != null and brand.timeSort == 2">
			order by MOD_TIME ASC
		</if>	
		<if test="brand.timeSort == null">
			order by MOD_TIME DESC
		</if>	
	</select>
	
	<!-- 驗證品牌名稱是否重複 -->
	<select id="vailBrandName" parameterType="com.vedeng.firstengage.model.FirstengageBrand" resultType="java.lang.Integer">
		SELECT COUNT(*) FROM T_BRAND
		<where>
			1 = 1
			<if test="brandNature == 1">
				and (BRAND_NAME = #{brandName}
				<if test="brandNameEn != null and '' != brandNameEn">
					or BRAND_NAME_EN = #{brandNameEn}
				</if>
				)
			</if>

			<if test="brandNature == 2">
				and (BRAND_NAME_EN = #{brandNameEn}
				<if test="brandName != null and '' != brandName">
					or BRAND_NAME = #{brandName}
				</if>
				)
			</if>
			<if test="brandId!=null and brandId!=''">
				AND BRAND_ID <![CDATA[ <> ]]> #{brandId,jdbcType=INTEGER}
			</if>
			<if test="companyId != null and companyId != 0">
				AND COMPANY_ID = #{companyId,jdbcType=INTEGER}
			</if>
			AND IS_DELETE = 0
		</where>
	</select>
	
	<select id="vailBrandGoods" parameterType="com.vedeng.firstengage.model.FirstengageBrand" resultType="java.lang.Integer">
		SELECT COUNT(*) FROM T_GOODS WHERE BRAND_ID = #{brandId,jdbcType=INTEGER} <!-- AND IS_DISCARD = 0 -->
	</select>
	
	<select id="getTraderSupplierAll" resultType="java.util.Map">
		SELECT
			a.PRODUCT_COMPANY_CHINESE_NAME AS label,
			a.PRODUCT_COMPANY_ID AS value
		FROM
			T_PRODUCT_COMPANY a
		where
			1=1
			<if test="array != null">
				and a.PRODUCT_COMPANY_ID IN
				<foreach collection="array" item="id" open="(" close=")" separator=",">
					#{id, jdbcType=INTEGER}
				</foreach>
			</if>
		GROUP BY
			a.PRODUCT_COMPANY_CHINESE_NAME
		ORDER BY
			a.PRODUCT_COMPANY_ID DESC
	</select>

	<select id="getBrandListByParam" resultType="com.vedeng.firstengage.model.FirstengageBrand">
		SELECT
			a.BRAND_ID,
			a.BRAND_NAME,
			a.LOGO_DOMAIN,
			a.LOGO_URI,
			a.BRAND_NATURE,
			a.BRAND_WEBSITE,
			a.MOD_TIME,
			a.IS_OWN_BRAND
		FROM
			T_BRAND a
		where
			1 = 1
		  <if test="null != brandIds and brandIds.size() > 0">
		  	  and a.BRAND_ID IN
			  <foreach collection="brandIds" item="brandId" open="(" close=")" separator=",">
				  #{brandId, jdbcType=INTEGER}
			  </foreach>
		  </if>
		  <if test="null != isDelete">
		  	  and a.IS_DELETE = #{isDelete, jdbcType=INTEGER}
		  </if>
		ORDER BY
			a.SORT DESC, a.BRAND_ID desc
			limit 10
	</select>

	<select id="getBrandGoodsCountByParam" resultType="java.util.Map">
		SELECT
			A.BRAND_ID AS brandId,
			COUNT(1) AS GOODS_NUM
		FROM
			T_GOODS A
		WHERE
			A.IS_DISCARD = 0
			AND BRAND_ID <![CDATA[ > ]]> 0
			<if test="null != brandIds and brandIds.size() > 0">
				and A.BRAND_ID IN
				<foreach collection="brandIds" item="brandId" open="(" close=")" separator=",">
					#{brandId, jdbcType=INTEGER}
				</foreach>
			</if>
		GROUP BY
			A.BRAND_ID

	</select>

	<select id="getBrandInfoByParam" resultType="java.util.Map">
		select
			BRAND_ID as brandId,
			BRAND_NAME as brandName
		from
			T_BRAND
		where
		company_id=1
			<if test="brandName != null">
				AND BRAND_NAME like CONCAT('%',#{brandName, jdbcType=VARCHAR},'%' )
			</if>
			<if test="companyId != null">
				AND COMPANY_ID = #{companyId}
			</if>
			<if test="isDelete != null">
				AND IS_DELETE = #{isDelete, jdbcType=INTEGER}
			</if>
	</select>

	<insert id="addBrandAndManufacturer" parameterType="java.util.Map">
		insert into
			V_BRAND_MANUFACTOR_MAPPING (BRAND_ID, PRODUCT_COMPANY_ID)
		values
			<foreach collection="manufacturer" item="id" separator=",">
				(#{brandId, jdbcType=INTEGER}, #{id, jdbcType=INTEGER})
			</foreach>
	</insert>

	<delete id="deleteManufacturer" parameterType="java.lang.Integer">
		delete from V_BRAND_MANUFACTOR_MAPPING where BRAND_ID = #{brandId, jdbcType=INTEGER}
	</delete>

	<select id="getTraderSupplier" resultType="java.util.Map">
		select
		b.TRADER_NAME as label,
		a.TRADER_SUPPLIER_ID as value
		from T_TRADER_SUPPLIER a
		left join T_TRADER b on a.TRADER_ID = b.TRADER_ID
		where a.TRADER_TYPE = 1
		and b.TRADER_NAME is not null
		and b.TRADER_NAME != ''
		<if test="array != null">
			and a.TRADER_SUPPLIER_ID IN
			<foreach collection="array" item="id" open="(" close=")" separator=",">
				#{id, jdbcType=INTEGER}
			</foreach>
		</if>
		group by b.TRADER_NAME
		order by b.TRADER_ID desc
	</select>
	<select id="getRelatedBrandSupplier" resultType="java.lang.String">
		select group_concat(TRADER_SUPPLIER_ID separator '@')
		from T_R_BRAND_J_TRADER_SUPPLIER
		where IS_ENABLE = 1
		  and TRADER_SUPPLIER_ID is not null
		  and BRAND_ID = #{brandId,jdbcType=INTEGER}
	</select>
	<insert id="addBrandAndTraderSupplier">
		insert into
		T_R_BRAND_J_TRADER_SUPPLIER(BRAND_ID, TRADER_SUPPLIER_ID, IS_ENABLE, ADD_TIME, CREATOR, MOD_TIME, UPDATER)
		values
		<foreach collection="manufacturer" item="id" separator=",">
			(#{brandId, jdbcType=INTEGER}, #{id, jdbcType=INTEGER},1, unix_timestamp() * 1000,
			#{sessionUser, jdbcType=INTEGER}, unix_timestamp() * 1000,#{sessionUser, jdbcType=INTEGER})
		</foreach>
	</insert>
	<update id="deleteRelatedTraderSupplier">
		update T_R_BRAND_J_TRADER_SUPPLIER set IS_ENABLE = 0 where BRAND_ID = #{brandId,jdbcType=INTEGER}
	</update>
	<select id="getAllRelatedBrandProductCompany"
			resultType="com.vedeng.firstengage.model.vo.BrandManufactorMapping">
		select BRAND_ID as brandId,
			PRODUCT_COMPANY_ID as productCompanyId
		from V_BRAND_MANUFACTOR_MAPPING
	</select>
	<select id="getProductCompanyInIds" resultType="com.vedeng.firstengage.model.ProductCompany">
		select * from T_PRODUCT_COMPANY
		<if test="array != null and array.length != 0">
			where PRODUCT_COMPANY_ID in
			<foreach collection="array" index="index" item="item" open="(" close=")" separator=",">
				#{item,jdbcType=INTEGER}
			</foreach>
		</if>
	</select>
	<insert id="insertBrandTraderSupplier">
		insert into T_R_BRAND_J_TRADER_SUPPLIER (BRAND_ID,
		TRADER_SUPPLIER_ID,
		IS_ENABLE,
		ADD_TIME,
		CREATOR,
		MOD_TIME,
		UPDATER)
		values (#{brandManufactorMapping.brandId,jdbcType=INTEGER},
		#{brandManufactorMapping.productCompanyId,jdbcType=INTEGER},
		1,
		unix_timestamp() * 1000,
		2,
		unix_timestamp() * 1000,
		2)
	</insert>
	<select id="getBrandTraderSupplier" resultType="com.vedeng.firstengage.model.vo.BrandManufactorMapping">
		select *
		from T_R_BRAND_J_TRADER_SUPPLIER
		where IS_ENABLE = 1
			and BRAND_ID = #{brandManufactorMapping.brandId,jdbcType=INTEGER}
			and TRADER_SUPPLIER_ID = #{brandManufactorMapping.productCompanyId,jdbcType=INTEGER}
	</select>


</mapper>