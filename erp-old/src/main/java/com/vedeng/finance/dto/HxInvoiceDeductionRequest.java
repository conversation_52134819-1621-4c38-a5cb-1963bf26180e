package com.vedeng.finance.dto;

import java.util.List;

/**
 * 航信发票勾选请求类
 * <AUTHOR>
 * @date 2020/08/12
 **/
public class HxInvoiceDeductionRequest {

    /**
     * 纳税人识别号
     */
    private String nsrsbh;

    private List<DeductionData> data;

    public String getNsrsbh() {
        return nsrsbh;
    }

    public void setNsrsbh(String nsrsbh) {
        this.nsrsbh = nsrsbh;
    }

    public List<DeductionData> getData() {
        return data;
    }

    public void setData(List<DeductionData> data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "HxInvoiceDeductionRequest{" +
                "nsrsbh='" + nsrsbh + '\'' +
                ", data=" + data +
                '}';
    }

    public static class DeductionData{

        /**
         * 发票代码
         */
        private String fpdm;

        /**
         * 发票号码
         */
        private String fphm;

        /**
         * 勾选类型，0撤销勾选，1勾选
         */
        private String gxlx;

        /**
         * 有效税额
         */
        private String se;

        /**
         * 开票日期，yyyy-MM-dd
         */
        private String kprq;

        public DeductionData(String fpdm, String fphm, String gxlx, String se, String kprq) {
            this.fpdm = fpdm;
            this.fphm = fphm;
            this.gxlx = gxlx;
            this.se = se;
            this.kprq = kprq;
        }

        public DeductionData() {
        }

        public String getFpdm() {
            return fpdm;
        }

        public void setFpdm(String fpdm) {
            this.fpdm = fpdm;
        }

        public String getFphm() {
            return fphm;
        }

        public void setFphm(String fphm) {
            this.fphm = fphm;
        }

        public String getGxlx() {
            return gxlx;
        }

        public void setGxlx(String gxlx) {
            this.gxlx = gxlx;
        }

        public String getSe() {
            return se;
        }

        public void setSe(String se) {
            this.se = se;
        }

        public String getKprq() {
            return kprq;
        }

        public void setKprq(String kprq) {
            this.kprq = kprq;
        }

        @Override
        public String toString() {
            return "DeductionData{" +
                    "fpdm='" + fpdm + '\'' +
                    ", fphm='" + fphm + '\'' +
                    ", gxlx='" + gxlx + '\'' +
                    ", se='" + se + '\'' +
                    ", kprq='" + kprq + '\'' +
                    '}';
        }
    }
}
