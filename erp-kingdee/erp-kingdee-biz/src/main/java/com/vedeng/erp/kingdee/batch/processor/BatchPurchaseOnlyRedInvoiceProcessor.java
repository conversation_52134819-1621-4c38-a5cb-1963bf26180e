package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.*;
import com.vedeng.erp.kingdee.domain.entity.KingDeePurchaseVatPlainInvoiceEntity;
import com.vedeng.erp.kingdee.domain.entity.KingDeePurchaseVatSpecialInvoiceEntity;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.erp.kingdee.repository.mappers.KingDeePurchaseVatPlainInvoiceMapper;
import com.vedeng.erp.kingdee.repository.mappers.KingDeePurchaseVatSpecialInvoiceMapper;
import com.vedeng.erp.kingdee.mapstruct.KingDeePurchaseVatPlainInvoiceConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeePurchaseVatSpecialInvoiceConvertor;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购仅退票 红票挂到原始蓝票上
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchPurchaseOnlyRedInvoiceProcessor implements ItemProcessor<BatchInvoiceDto, KingDeePayCommonAndInvoiceDto> {



    @Autowired
    private BatchInvoiceDetailDtoMapper batchInvoiceDetailDtoMapper;

    @Autowired
    private BatchRInvoiceJInvoiceDtoMapper batchRInvoiceJInvoiceDtoMapper;

    @Autowired
    private KingDeePurchaseVatSpecialInvoiceMapper kingDeePurchaseVatSpecialInvoiceMapper;

    @Autowired
    private KingDeePurchaseVatPlainInvoiceMapper kingDeePurchaseVatPlainInvoiceMapper;

    @Autowired
    private KingDeePurchaseVatSpecialInvoiceConvertor kingDeePurchaseVatSpecialInvoiceConvertor;

    @Autowired
    private KingDeePurchaseVatPlainInvoiceConvertor kingDeePurchaseVatPlainInvoiceConvertor;

    @Autowired
    private BatchBuyorderGoodsDtoMapper batchBuyorderGoodsDtoMapper;

    @Autowired
    private BatchRInvoiceDetailJOperateLogDtoMapper batchRInvoiceDetailJOperateLogDtoMapper;

    @Autowired
    private BatchAfterSalesDtoMapper batchAfterSalesDtoMapper;


    @Override
    public KingDeePayCommonAndInvoiceDto process(BatchInvoiceDto batchInvoiceDto) throws Exception {
        log.info("BatchPurchaseOnlyRedInvoiceProcessorService 处理采购单仅退票红字有效票{}" , JSON.toJSONString(batchInvoiceDto));


        List<BatchInvoiceDetailDto> batchInvoiceDetailDtoList = batchInvoiceDetailDtoMapper.findByInvoiceId(batchInvoiceDto.getInvoiceId());
        if (CollUtil.isEmpty(batchInvoiceDetailDtoList)) {
            log.error("当前采购单仅退票红字有效id:{},未查到发票明细",JSON.toJSONString(batchInvoiceDto.getInvoiceId()));
            throw new KingDeeException("当前采购单仅退票红字有效id:" + JSON.toJSONString(batchInvoiceDto.getInvoiceId()) + "未查到发票明细");
        }
        List<BatchInvoiceDto> originalBlueEnable = batchRInvoiceJInvoiceDtoMapper.findOriginalBlueEnable(batchInvoiceDto.getInvoiceId());


        if (CollUtil.isEmpty(originalBlueEnable)) {
            log.error("当前采购单仅退票红字有效id:{},未查到原始蓝票信息",JSON.toJSONString(batchInvoiceDto.getInvoiceId()));
            throw new KingDeeException("当前采购单仅退票红字有效id:" + JSON.toJSONString(batchInvoiceDto.getInvoiceId()) + "未查到原始蓝票信息");
        }

        List<BatchBuyorderGoodsDto> batchBuyorderGoodsDtos = batchBuyorderGoodsDtoMapper.selectByBuyorderIdNotDelete(batchInvoiceDto.getRelatedId());
        if (CollUtil.isEmpty(batchBuyorderGoodsDtos)) {
            log.error("当前采购单仅退票红字有效票id:{},未查到采购商品信息",JSON.toJSONString(batchInvoiceDto.getInvoiceId()));
            throw new KingDeeException("当前采购单仅退票红字有效票id:" + JSON.toJSONString(batchInvoiceDto.getInvoiceId()) + "未查到采购商品信息");
        }
        Map<Integer, BatchBuyorderGoodsDto> batchBuyorderGoods2Map = batchBuyorderGoodsDtos.stream().collect(Collectors.toMap(BatchBuyorderGoodsDto::getBuyorderGoodsId, c -> c, (k1, k2) -> k1));
        List<String> blueInvoiceId = originalBlueEnable.stream().map(c -> c.getInvoiceId().toString()).collect(Collectors.toList());
        List<Integer> blueInvoiceIds = originalBlueEnable.stream().map(BatchInvoiceDto::getInvoiceId).collect(Collectors.toList());

        List<BatchRInvoiceDetailJOperateLogDto> byInvoiceIds = batchRInvoiceDetailJOperateLogDtoMapper.findByInvoiceIds(blueInvoiceIds);
        Map<String, List<BatchRInvoiceDetailJOperateLogDto>> skuByInvoiceIds = byInvoiceIds.stream().collect(Collectors.groupingBy(BatchRInvoiceDetailJOperateLogDto::getSku));
        boolean isSpecial = StrUtil.isNotEmpty(batchInvoiceDto.getInvoiceTypeName()) && batchInvoiceDto.getInvoiceTypeName().contains("专用发票");
        // 发票类型 1电票 2 纸票
        String QZOK_FPLX = Objects.isNull(batchInvoiceDto.getInvoiceProperty()) ? "2" : batchInvoiceDto.getInvoiceProperty().equals(1) ? "2" : "1";
        DecimalFormat decimalFormat = new DecimalFormat("0.00#");
        String taxRate = Objects.isNull(batchInvoiceDto.getRatio()) ? "0.00" : isSpecial ? decimalFormat.format(batchInvoiceDto.getRatio().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP)) : "0.00";
        BigDecimal taxRateNum = Objects.isNull(batchInvoiceDto.getRatio()) ? BigDecimal.ZERO : isSpecial ? batchInvoiceDto.getRatio() : BigDecimal.ZERO;
        KingDeePayCommonAndInvoiceDto kingDeePayCommonAndInvoiceDto = new KingDeePayCommonAndInvoiceDto();
        BatchAfterSalesDto byAfterSalesId = batchAfterSalesDtoMapper.findByAfterSalesId(batchInvoiceDto.getAfterSalesId());
        if (Objects.nonNull(byAfterSalesId)) {
            batchInvoiceDto.setAfterSalesNo(byAfterSalesId.getAfterSalesNo());
        }
        if (isSpecial) {
            kingDeePayCommonAndInvoiceDto.setSpecial(true);
            PurchaseVatSpecialInvoiceDto dto = new PurchaseVatSpecialInvoiceDto();
            List<KingDeePurchaseVatSpecialInvoiceEntity> byFQzokBddjtid = kingDeePurchaseVatSpecialInvoiceMapper.findByFQzokBddjtid(blueInvoiceId);
            if (CollUtil.isEmpty(byFQzokBddjtid)) {
                log.warn("采购费用仅退票，未能查到原始蓝票的推送金蝶的信息");
                return null;
            }


            List<DateTime> dateTimes = byFQzokBddjtid.stream().map(x -> DateUtil.parseDateTime(x.getFdate())).collect(Collectors.toList());
            Optional<DateTime> max = dateTimes.stream().max(Comparator.comparing(x -> x));
            if (max.isPresent() && max.get().getTime() > batchInvoiceDto.getValidTime()) {
                batchInvoiceDto.setValidTime(max.get().getTime());
            }
            dto.setFid("0");
            dto.setFdate(DateUtil.formatDateTime(new Date(batchInvoiceDto.getValidTime())));
            dto.setFinvoicedate(DateUtil.formatDateTime(new Date(batchInvoiceDto.getValidTime())));
            dto.setFQzokBddjtid(batchInvoiceDto.getInvoiceId().toString());
            dto.setFinvoiceno(batchInvoiceDto.getInvoiceNo());
            dto.setFsupplierid(batchInvoiceDto.getTraderSupplierId().toString());
            dto.setFdocumentstatus("Z");
            dto.setFBillTypeID("CGZZSZYFP01_SYS");
            dto.setFsettleorgid(KingDeeConstant.ORG_ID.toString());
            dto.setFCancelStatus("A");
            dto.setFRedBlue("1");
            dto.setFQzokFpdm(batchInvoiceDto.getInvoiceCode());

            List<PurchaseVatSpecialInvoiceDto> purchaseVatSpecialInvoiceDtos = kingDeePurchaseVatSpecialInvoiceConvertor.toDto(byFQzokBddjtid);
            Map<String, List<PurchaseVatSpecialInvoiceDto>> collect = purchaseVatSpecialInvoiceDtos.stream().collect(Collectors.groupingBy(PurchaseVatSpecialInvoiceDto::getFQzokBddjtid));
            // 二级
            List<PurchaseVatSpecialInvoiceDetailDto> FPURCHASEICENTRY = new ArrayList<>();
            dto.setFPURCHASEICENTRY(FPURCHASEICENTRY);
            batchInvoiceDetailDtoList.forEach(c->{
                BatchBuyorderGoodsDto batchBuyorderGoodsDto = batchBuyorderGoods2Map.get(c.getDetailgoodsId());
                if (Objects.isNull(batchBuyorderGoodsDto)) {
                    return;
                }
                List<BatchRInvoiceDetailJOperateLogDto> detailJOperateLogDtos = skuByInvoiceIds.get(batchBuyorderGoodsDto.getSku());
                if (CollUtil.isEmpty(detailJOperateLogDtos)) {
                    log.warn("采购费用仅退票，未能查到原始蓝票的挂靠关系：{}",JSON.toJSONString(batchBuyorderGoodsDto));
                    return;
                }
                List<Integer> skuBlueInvoiceIds = detailJOperateLogDtos.stream().map(BatchRInvoiceDetailJOperateLogDto::getInvoiceId).distinct().collect(Collectors.toList());
                // 票明细总额
                BigDecimal totalAmount = Objects.isNull(c.getTotalAmount()) ? BigDecimal.ZERO : c.getTotalAmount();

                // 税额  [价税合计/(1+ 税率)]*税率
                BigDecimal price = c.getTotalAmount().divide(c.getNum(),6, RoundingMode.HALF_UP);
                BigDecimal taxAmouontFor = totalAmount.multiply(taxRateNum).divide(BigDecimal.ONE.add(taxRateNum), 2, RoundingMode.HALF_UP);
                PurchaseVatSpecialInvoiceDetailDto sonDto = new PurchaseVatSpecialInvoiceDetailDto();
                sonDto.setFmaterialid(batchBuyorderGoodsDto.getSku());
                sonDto.setFpriceqty(c.getNum().abs().negate().toString());
                sonDto.setFauxtaxprice(price.abs());
                sonDto.setFtaxrate(taxRate);
                // 发票不含税的金额 价税合计-税额
                sonDto.setFamountfor(totalAmount.subtract(taxAmouontFor));
                sonDto.setFdetailtaxamountfor(taxAmouontFor);
                sonDto.setFallamountfor(totalAmount);
                sonDto.setFQzokBddjhid(c.getInvoiceDetailId().toString());
                sonDto.setFsourcetype("IV_PURCHASEIC");
                sonDto.setFQzokYsddh(batchInvoiceDto.getOrderNo());
                sonDto.setFQzokGsywdh(batchInvoiceDto.getAfterSalesNo());
                sonDto.setFQzokYwlx("采购退票");

                // 三级
                List<PurchaseVatSpecialInvoiceDetailLinkDto> fpurchaseicentryLink = new ArrayList<>();
                sonDto.setFPURCHASEICENTRY_Link(fpurchaseicentryLink);
                FPURCHASEICENTRY.add(sonDto);
                // 当前红票明细的总金额
                AtomicReference<BigDecimal> redDetailAmount = new AtomicReference<>(c.getTotalAmount().abs());
                skuBlueInvoiceIds.forEach(a->{

                    List<PurchaseVatSpecialInvoiceDto> data = collect.get(a.toString());
                    if (CollUtil.isNotEmpty(data)) {
                        data.forEach(x->{
                            List<PurchaseVatSpecialInvoiceDetailDto> fpurchaseicentry = x.getFPURCHASEICENTRY();
                            if (CollUtil.isEmpty(fpurchaseicentry)) {
                                log.error("采购费用仅退票，原始蓝票金蝶的二级级关系为空,{}",JSON.toJSONString(x));
                                return;
                            }
                            List<PurchaseVatSpecialInvoiceDetailDto> originalBlueDetails = fpurchaseicentry.stream().filter(xx -> xx.getFmaterialid().equals(batchBuyorderGoodsDto.getSku())).collect(Collectors.toList());

                            if (CollUtil.isEmpty(originalBlueDetails)) {
                                log.error("采购费用仅退票，原始蓝票当前sku：{}的金蝶的二级关系为空,{}",batchBuyorderGoodsDto.getSku(),JSON.toJSONString(x));
                                return;
                            }
                            originalBlueDetails.forEach(xx->{

                                PurchaseVatSpecialInvoiceDetailLinkDto inSon1 = new PurchaseVatSpecialInvoiceDetailLinkDto();

                                inSon1.setFLinkId("0");
                                inSon1.setFpurchaseicentryLinkFflowlineid("0");
                                inSon1.setFpurchaseicentryLinkFruleid("IV_BlueToRedPurchaseIC");
                                inSon1.setFpurchaseicentryLinkFstableid("0");
                                inSon1.setFpurchaseicentryLinkFstablename("T_IV_PURCHASEICENTRY");
                                inSon1.setFpurchaseicentryLinkFsbillid(x.getFid());
                                inSon1.setFpurchaseicentryLinkFsid(xx.getFEntryId());
                                List<PurchaseVatSpecialInvoiceDetailLinkDto> fpurchaseicentry_link = xx.getFPURCHASEICENTRY_Link();
                                if (CollUtil.isEmpty(fpurchaseicentry_link)) {
                                    log.info("采购费用仅退票，原始蓝票当前sku：{}的金蝶的三级级关系为空,{}",batchBuyorderGoodsDto.getSku(),JSON.toJSONString(x));
                                    return;
                                }
                                BigDecimal all = fpurchaseicentry_link.stream().map(PurchaseVatSpecialInvoiceDetailLinkDto::getFpurchaseicentryLinkFallamountforold).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                                BigDecimal allNum = fpurchaseicentry_link.stream().map(l -> new BigDecimal(l.getFpurchaseicentryLinkFbasicunitqtyold())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                                BigDecimal subtract = redDetailAmount.get().subtract(all.abs());
                                redDetailAmount.getAndSet(subtract);
                                inSon1.setFpurchaseicentryLinkFbasicunitqtyold(allNum.abs().negate().toString());
                                inSon1.setFpurchaseicentryLinkFbasicunitqty(allNum.abs().negate().toString());
                                inSon1.setFpurchaseicentryLinkFallamountforold(all.abs().negate());
                                inSon1.setFpurchaseicentryLinkFallamountfor(all.abs().negate());
                                fpurchaseicentryLink.add(inSon1);
                            });

                        });
                    }

                });

                // 最后的分摊
                if (redDetailAmount.get().compareTo(BigDecimal.ZERO) != 0) {
                    log.info("红票存在微小差异最后补足");
                    if (CollUtil.isNotEmpty(fpurchaseicentryLink)) {
                        PurchaseVatSpecialInvoiceDetailLinkDto last = fpurchaseicentryLink.get(fpurchaseicentryLink.size() - 1);
                        last.setFpurchaseicentryLinkFallamountforold(last.getFpurchaseicentryLinkFallamountforold().subtract(redDetailAmount.get()));
                        last.setFpurchaseicentryLinkFallamountfor(last.getFpurchaseicentryLinkFallamountfor().subtract(redDetailAmount.get()));
                    }
                }

            });
            kingDeePayCommonAndInvoiceDto.setPurchaseVatSpecialInvoiceDto(dto);

        } else {
            kingDeePayCommonAndInvoiceDto.setSpecial(false);


            List<KingDeePurchaseVatPlainInvoiceEntity> byFQzokBddjtid = kingDeePurchaseVatPlainInvoiceMapper.findByFQzokBddjtid(blueInvoiceId);
            if (CollUtil.isEmpty(byFQzokBddjtid)) {
                log.warn("采购费用仅退票，未能查到原始蓝票的推送金蝶的信息");
                return null;
            }

            List<DateTime> dateTimes = byFQzokBddjtid.stream().map(x -> DateUtil.parseDateTime(x.getFdate())).collect(Collectors.toList());
            Optional<DateTime> max = dateTimes.stream().max(Comparator.comparing(x -> x));
            if (max.isPresent() && max.get().getTime() > batchInvoiceDto.getValidTime()) {
                batchInvoiceDto.setValidTime(max.get().getTime());
            }
            PurchaseVatPlainInvoiceDto dto = new PurchaseVatPlainInvoiceDto();
            // 1电票 2 纸票
            dto.setFQzokFplx(QZOK_FPLX);
            dto.setFid("0");
            dto.setFdate(DateUtil.formatDateTime(new Date(batchInvoiceDto.getValidTime())));
            dto.setFinvoicedate(DateUtil.formatDateTime(new Date(batchInvoiceDto.getValidTime())));
            dto.setFQzokBddjtid(batchInvoiceDto.getInvoiceId().toString());
            dto.setFinvoiceno(batchInvoiceDto.getInvoiceNo());
            dto.setFsupplierid(batchInvoiceDto.getTraderSupplierId().toString());
            dto.setFdocumentstatus("Z");
            dto.setFBillTypeID("CGPTFP01_SYS");
            dto.setFsettleorgid(KingDeeConstant.ORG_ID.toString());
            dto.setFpurchaseorgid(KingDeeConstant.ORG_ID.toString());
            dto.setFCancelStatus("A");
            dto.setFRedBlue("1");
            dto.setFQzokFpdm(batchInvoiceDto.getInvoiceCode());


            List<PurchaseVatPlainInvoiceDto> purchaseVatPlainInvoiceDtos = kingDeePurchaseVatPlainInvoiceConvertor.toDto(byFQzokBddjtid);
            Map<String, List<PurchaseVatPlainInvoiceDto>> collect = purchaseVatPlainInvoiceDtos.stream().collect(Collectors.groupingBy(PurchaseVatPlainInvoiceDto::getFQzokBddjtid));
            // 二级
            List<PurchaseVatPlainInvoiceDetailDto> FPURCHASEICENTRY = new ArrayList<>();
            dto.setFPURCHASEICENTRY(FPURCHASEICENTRY);
            batchInvoiceDetailDtoList.forEach(c-> {
                BatchBuyorderGoodsDto batchBuyorderGoodsDto = batchBuyorderGoods2Map.get(c.getDetailgoodsId());
                if (Objects.isNull(batchBuyorderGoodsDto)) {
                    return;
                }
                List<BatchRInvoiceDetailJOperateLogDto> detailJOperateLogDtos = skuByInvoiceIds.get(batchBuyorderGoodsDto.getSku());
                if (CollUtil.isEmpty(detailJOperateLogDtos)) {
                    log.warn("采购费用仅退票，未能查到原始蓝票的挂靠关系：{}", JSON.toJSONString(batchBuyorderGoodsDto));
                    return;
                }
                List<Integer> skuBlueInvoiceIds = detailJOperateLogDtos.stream().map(BatchRInvoiceDetailJOperateLogDto::getInvoiceId).distinct().collect(Collectors.toList());
                // 票明细总额
                BigDecimal price = c.getTotalAmount().divide(c.getNum(),6, RoundingMode.HALF_UP);
                PurchaseVatPlainInvoiceDetailDto sonDto = new PurchaseVatPlainInvoiceDetailDto();
                sonDto.setFmaterialid(batchBuyorderGoodsDto.getSku());
                sonDto.setFpriceqty(c.getNum().abs().negate().toString());
                sonDto.setFauxtaxprice(price.abs().toString());
                sonDto.setFQzokBddjhid(c.getInvoiceDetailId().toString());
                sonDto.setFsourcetype("IV_PURCHASEOC");
                sonDto.setFQzokYsddh(batchInvoiceDto.getOrderNo());
                sonDto.setFQzokGsywdh(batchInvoiceDto.getAfterSalesNo());
                sonDto.setFQzokYwlx("采购退票");

                // 三级
                List<PurchaseVatPlainInvoiceDetailLinkDto> fpurchaseicentryLink = new ArrayList<>();
                sonDto.setFpurchaseicentryLink(fpurchaseicentryLink);
                FPURCHASEICENTRY.add(sonDto);

                // 当前红票明细的总金额
                AtomicReference<BigDecimal> redDetailAmount = new AtomicReference<>(c.getTotalAmount().abs());
                skuBlueInvoiceIds.forEach(a -> {

                    List<PurchaseVatPlainInvoiceDto> data = collect.get(a.toString());
                    if (CollUtil.isNotEmpty(data)) {
                        data.forEach(x -> {
                            List<PurchaseVatPlainInvoiceDetailDto> fpurchaseicentry = x.getFPURCHASEICENTRY();
                            if (CollUtil.isEmpty(fpurchaseicentry)) {
                                log.error("采购费用仅退票，原始蓝票金蝶的二级级关系为空,{}", JSON.toJSONString(x));
                                return;
                            }
                            List<PurchaseVatPlainInvoiceDetailDto> originalBlueDetails = fpurchaseicentry.stream().filter(xx -> xx.getFmaterialid().equals(batchBuyorderGoodsDto.getSku())).collect(Collectors.toList());

                            if (CollUtil.isEmpty(originalBlueDetails)) {
                                log.error("采购费用仅退票，原始蓝票当前sku：{}的金蝶的二级关系为空,{}", batchBuyorderGoodsDto.getSku(), JSON.toJSONString(x));
                                return;
                            }
                            originalBlueDetails.forEach(xx -> {

                                PurchaseVatPlainInvoiceDetailLinkDto inSon1 = new PurchaseVatPlainInvoiceDetailLinkDto();
                                inSon1.setFLinkId("0");
                                inSon1.setFpurchaseicentryLinkFflowlineid("0");
                                inSon1.setFpurchaseicentryLinkFruleid("IV_BlueToRedPurchaseOC");
                                inSon1.setFpurchaseicentryLinkFstableid("0");
                                inSon1.setFpurchaseicentryLinkFstablename("T_IV_PURCHASEICENTRY1");
                                inSon1.setFpurchaseicentryLinkFsbillid(x.getFid());
                                inSon1.setFpurchaseicentryLinkFsid(xx.getFEntryId());
                                List<PurchaseVatPlainInvoiceDetailLinkDto> fpurchaseicentry_link = xx.getFpurchaseicentryLink();
                                if (CollUtil.isEmpty(fpurchaseicentry_link)) {
                                    log.info("采购费用仅退票，原始蓝票当前sku：{}的金蝶的三级级关系为空,{}", batchBuyorderGoodsDto.getSku(), JSON.toJSONString(x));
                                    return;
                                }
                                BigDecimal all = fpurchaseicentry_link.stream().map(PurchaseVatPlainInvoiceDetailLinkDto::getFpurchaseicentryLinkFallamountforold).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                                BigDecimal allNum = fpurchaseicentry_link.stream().map(l -> new BigDecimal(l.getFpurchaseicentryLinkFbasicunitqtyold())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                                BigDecimal subtract = redDetailAmount.get().subtract(all.abs());
                                redDetailAmount.getAndSet(subtract);
                                inSon1.setFpurchaseicentryLinkFbasicunitqtyold(allNum.abs().negate().toString());
                                inSon1.setFpurchaseicentryLinkFbasicunitqty(allNum.abs().negate().toString());
                                inSon1.setFpurchaseicentryLinkFallamountforold(all.abs().negate());
                                inSon1.setFpurchaseicentryLinkFallamountfor(all.abs().negate());
                                fpurchaseicentryLink.add(inSon1);
                            });

                        });
                    }

                });

                if (redDetailAmount.get().compareTo(BigDecimal.ZERO) != 0) {
                    log.info("红票存在微小差异最后补足:{}",JSON.toJSONString(redDetailAmount));
                    if (CollUtil.isNotEmpty(fpurchaseicentryLink)) {
                        PurchaseVatPlainInvoiceDetailLinkDto last = fpurchaseicentryLink.get(fpurchaseicentryLink.size() - 1);
                        last.setFpurchaseicentryLinkFallamountforold(last.getFpurchaseicentryLinkFallamountforold().subtract(redDetailAmount.get()));
                        last.setFpurchaseicentryLinkFallamountfor(last.getFpurchaseicentryLinkFallamountfor().subtract(redDetailAmount.get()));
                    }
                }
            });

            kingDeePayCommonAndInvoiceDto.setPurchaseVatPlainInvoiceDto(dto);
        }

        return kingDeePayCommonAndInvoiceDto;
    }

}
