package com.vedeng.kpi.service;

import com.vedeng.kpi.model.DTO.KpiDataQueryDto;
import com.vedeng.kpi.model.DTO.KpiUserInfoDto;

import java.util.List;

/**
 * 绩效数据service.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/6/5 9:54 上午.
 * @author: Tomcat.Hui.
 */
public interface KpiAmountService {

    /**
     * 获取已发货&未全到款list.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/6 11:06 上午.
     * @author: Tomcat.Hui.
     * @param query: .
     * @return: java.util.List<com.vedeng.kpi.model.DTO.KpiDataQueryDto>.
     * @throws: .
     */
    List<KpiDataQueryDto> getSendNotPaid(KpiDataQueryDto query);

    /**
     * 获取已付款&未计入业绩list.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/6 11:06 上午.
     * @author: Tomcat.Hui.
     * @param query: .
     * @return: java.util.List<com.vedeng.kpi.model.DTO.KpiDataQueryDto>.
     * @throws: .
     */
    List<KpiDataQueryDto> getPaidNotKpi(KpiDataQueryDto query);
}
