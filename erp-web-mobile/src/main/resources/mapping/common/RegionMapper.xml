<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.mobile.system.mapper.RegionMapper">

    <resultMap id="BaseResultMap" type="com.vedeng.mobile.system.model.Region">
            <id property="regionId" column="REGION_ID" jdbcType="BIGINT"/>
            <result property="parentId" column="PARENT_ID" jdbcType="BIGINT"/>
            <result property="regionName" column="REGION_NAME" jdbcType="VARCHAR"/>
            <result property="regionType" column="REGION_TYPE" jdbcType="BOOLEAN"/>
            <result property="agencyId" column="AGENCY_ID" jdbcType="INTEGER"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="regionFullName" column="REGION_FULL_NAME" jdbcType="VARCHAR"/>
            <result property="regionCode" column="REGION_CODE" jdbcType="VARCHAR"/>
            <result property="isDeleted" column="IS_DELETED" jdbcType="BOOLEAN"/>
    </resultMap>

    <sql id="Base_Column_List">
        REGION_ID,PARENT_ID,REGION_NAME,
        REGION_TYPE,AGENCY_ID,CREATE_TIME,
        UPDATE_TIME,REGION_FULL_NAME,REGION_CODE,
        IS_DELETED
    </sql>

    <resultMap id="ResultMap" type="com.vedeng.mobile.system.dto.RegionInnerDto">
        <id property="regionId" column="ONE_REGION_ID" jdbcType="BIGINT"/>
        <result property="regionCode" column="ONE_REGION_CODE" jdbcType="BIGINT"/>
        <result property="regionName" column="ONE_REGION_NAME" jdbcType="VARCHAR"/>
        <collection property="regionList" ofType="com.vedeng.mobile.system.dto.RegionInnerDto">
            <id property="regionId" column="TWO_REGION_ID" jdbcType="BIGINT"/>
            <result property="regionCode" column="TWO_REGION_CODE" jdbcType="BIGINT"/>
            <result property="regionName" column="TWO_REGION_NAME" jdbcType="VARCHAR"/>
            <collection property="regionList" ofType="com.vedeng.mobile.system.dto.RegionInnerDto">
                <id property="regionId" column="THREE_REGION_ID" jdbcType="BIGINT"/>
                <result property="regionCode" column="THREE_REGION_CODE" jdbcType="BIGINT"/>
                <result property="regionName" column="THREE_REGION_NAME" jdbcType="VARCHAR"/>
            </collection>
        </collection>
    </resultMap>

    <select id="queryRegionByTopId" parameterType="int" resultMap="ResultMap">
        select a.REGION_ID as ONE_REGION_ID,a.REGION_CODE as ONE_REGION_CODE,a.REGION_NAME as ONE_REGION_NAME,
               b.REGION_ID as TWO_REGION_ID,b.REGION_CODE as TWO_REGION_CODE,b.REGION_NAME as TWO_REGION_NAME,
               c.REGION_ID as THREE_REGION_ID,c.REGION_CODE as TWO_REGION_CODE,c.REGION_NAME as THREE_REGION_NAME
            from T_REGION a
                left join T_REGION b on a.REGION_ID = b.PARENT_ID
                left join T_REGION c on b.REGION_ID = c.PARENT_ID
        where
            a.PARENT_ID = #{parentId,jdbcType=INTEGER}
    </select>

    <select id="queryRegionByThreeId" parameterType="int" resultMap="ResultMap">
        select a.REGION_ID as ONE_REGION_ID,a.REGION_CODE as ONE_REGION_CODE,a.REGION_NAME as ONE_REGION_NAME,
               b.REGION_ID as TWO_REGION_ID,b.REGION_CODE as TWO_REGION_CODE,b.REGION_NAME as TWO_REGION_NAME,
               c.REGION_ID as THREE_REGION_ID,c.REGION_CODE as TWO_REGION_CODE,c.REGION_NAME as THREE_REGION_NAME
        from T_REGION a
                 left join T_REGION b on a.REGION_ID = b.PARENT_ID
                 left join T_REGION c on b.REGION_ID = c.PARENT_ID
        where
            c.REGION_ID = #{regionId,jdbcType=INTEGER}
    </select>

    <select id="queryRegionLevelTwo" parameterType="int" resultMap="ResultMap">
        select a.REGION_ID as ONE_REGION_ID,a.REGION_CODE as ONE_REGION_CODE,a.REGION_NAME as ONE_REGION_NAME,
               b.REGION_ID as TWO_REGION_ID,b.REGION_CODE as TWO_REGION_CODE,b.REGION_NAME as TWO_REGION_NAME
        from T_REGION a
                 left join T_REGION b on a.REGION_ID = b.PARENT_ID
        where
            a.PARENT_ID = #{parentId,jdbcType=INTEGER}
    </select>


    <select id="queryRegionByParentId" parameterType="int" resultMap="ResultMap">
        select a.REGION_ID as ONE_REGION_ID,a.REGION_CODE as ONE_REGION_CODE,a.REGION_NAME as ONE_REGION_NAME
        from T_REGION a
        where
            a.PARENT_ID = #{parentId,jdbcType=INTEGER}
    </select>
</mapper>
