package com.vedeng.erp.buyorder.task.buyorder.statusSync;

import com.newtask.data.AbstractDataByTimeSync;
import com.newtask.data.dao.BuyorderDataMapper;
import com.newtask.data.dto.BuyorderDataDto;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description com.newtask.data.buyorder.statusSync
 * @Date 2021/10/21 11:30
 */
@Component
public class BuyorderRecordInvoiceApplyStatus extends AbstractDataByTimeSync {

    @Resource
    BuyorderDataMapper buyorderDataMapper;


    @Override
    public List<Map<String, Object>> loadBizByTimeData(Long startTime, Long endTime) {

        //1 未录票：部分收货或全部收货，且未收票状态的订单
        List<Map<String, Object>> orderIsAllLogisticsByTime = buyorderDataMapper.getRecordInvoiceApplyOneByTime(startTime, endTime);
        List<Map<String, Object>> dataList = new ArrayList<>(orderIsAllLogisticsByTime);
        //2 部分录票：订单内任意sku收货数量-售后完成数量＞收票数量的订单
        List<Map<String, Object>> orderIsAllLogisticsByTime1 = buyorderDataMapper.getRecordInvoiceApplyTwoByTime(startTime, endTime);
        dataList.addAll(orderIsAllLogisticsByTime1);
        //3 已录票审核中：任意sku存在录票审核中的订单
        List<Map<String, Object>> orderIsAllLogisticsByTime2 = buyorderDataMapper.getRecordInvoiceApplyThreeByTime(startTime, endTime);
        dataList.addAll(orderIsAllLogisticsByTime2);
        return dataList;
    }


    @Override
    public void updateData(List<Map<String, Object>> dataList) {
        if (CollectionUtils.isNotEmpty(dataList)) {
            List<BuyorderDataDto> buyorderDataDtos = new ArrayList<>();
            long nowTime = System.currentTimeMillis();
            dataList.forEach(item -> {
                BuyorderDataDto buyorderDataDto = new BuyorderDataDto();
                Integer buyorderId = Integer.valueOf(item.get("buyorderId").toString());
                makeExist(buyorderId, nowTime);
                buyorderDataDto.setBuyorderId(buyorderId);
                buyorderDataDto.setRecordInvoiceApplyStatus(Integer.valueOf(item.get("recordInvoiceApplyStatus").toString()));
                buyorderDataDtos.add(buyorderDataDto);
            });
            buyorderDataMapper.updateOrderRecordInvoiceApplyStatusByTime(buyorderDataDtos, nowTime);
        }
    }

    @Override
    public void makeExist(Integer id, Long nowTime) {
        int buyorderDataId = buyorderDataMapper.getBuyorderDataById(id);
        if (buyorderDataId <= 0) {
            buyorderDataMapper.insertBuyorderData(id, nowTime);
        }
    }
}
