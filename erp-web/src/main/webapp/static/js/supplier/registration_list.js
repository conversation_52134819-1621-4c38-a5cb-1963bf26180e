function datetimeFormat(longTypeDate){
    var dateTypeDate = "";
    var date = new Date();
    date.setTime(longTypeDate);

    return date.toTimeString();
}
function addAttament(item,domain,uri){
    var parent=$(item).parents(".p")
    var rnd=$(parent).find(".register-number-id").val();
    var checkTd=$(parent).find(".check_r")
    var statusTd=$(parent).find(".status-td");
    var uTd=$(parent).find(".check-u");
    var timeTd=$(parent).find(".time-td");
    var traderId=$("#traderId").val();
    var id=0;
    $.ajax({
        type: "POST",
        url: page_url+"/trader/supplier/registration/attachment/add.do",
        data: {'traderId':traderId,"registrationNumberId":rnd,"domain":domain,"uri":uri},
        dataType:'json',
        async:false,
        success: function(data){
            if(data.code==0){
                // var str='<a href="javascript:void(0)" onclick="check(this)" class="btn btn-small btn-blue">审核通过</a>'
                // $(checkTd).html(str);
                $(statusTd).html("待审核")
                $(uTd).html("");
                $(timeTd).html("");
                debugger
                id= data.data.attachmentId
            }else{
                layer.alert(data.message,{ icon: 2 });
            }
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    });
    debugger
    return id;
}
function check(item){
    var parent=$(item).parents(".p");
    var rnd=$(parent).find(".register-number-id").val();
    var checkTd=$(parent).find(".check_r");
    var statusTd=$(parent).find(".status-td");
    var traderId=$("#traderId").val();
    var timeTd=$(parent).find(".time-td");
    var uTd=$(parent).find(".check-u");
    $.ajax({
        type: "POST",
        url: page_url+"/trader/supplier/registration/check.do",
        data: {'traderId':traderId,"registrationNumberId":rnd},
        dataType:'json',
        success: function(data){
            if(data.code==0){
                var checker=$("#checker").val();
                $(statusTd).html("审核通过");
                $(checkTd).html("");
                $(timeTd).html(data.data.checkTimeStr)
                $(uTd).html(checker)
            }else{
                layer.alert(data.message,{ icon: 2 });
            }
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    });
}
function delAttach(item,id){
    var parent=$(item).parents(".p");
    var rnd=$(parent).find(".register-number-id").val();
    var checkTd=$(parent).find(".check_r");
    var statusTd=$(parent).find(".status-td");
    var traderId=$("#traderId").val();
    var uTd=$(parent).find(".check-u");
    var timeTd=$(parent).find(".time-td");
    $.ajax({
        type: "POST",
        url: page_url+"/trader/supplier/registration/attachment/delete.do",
        data: {'traderId':traderId,"registrationNumberId":rnd,"attachmentId":id},
        dataType:'json',
        success: function(data){
            if(data.code==0){
                // var str='<a href="javascript:void(0)" onclick="check(this)" class="btn btn-small btn-blue">审核通过</a>'
                // $(checkTd).html(str);
                $(statusTd).html("待审核")
                $(uTd).html("");
                $(timeTd).html("");
            }else{
                layer.alert(data.message,{ icon: 2 });
            }
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    });
}

$(function () {
    $('.J-upload').each(function (i) {
        var _this = this;
        new Upload({
            limit: 5,
            url: GLOBAL.IMGUPLOADURL,
            wrapper: $(this),
            uploadName: 'upload' + i,
            list: JSON.parse($(this).siblings('.J-upload-data').val() || '[]'),
            onchange: function () {
                $(_this).find('.J-upload-item').each(function (ii) {
                    var data = $(this).data('item');
                    $(this).find('.J-item-name').remove();
                    if(!data.id){
                        var id=addAttament(this,data.domain,data.filePath);
                        data.id=id;
                        $(this).data('item',JSON.stringify(data));
                        $(this).data('id', id);
                    }else {
                        $(this).data('id', data.id);
                        $(this).data("w",1);
                    }
                })

            },
            filters: {
                mime_types: [
                    { title: "Image files", extensions: "jpg,jpeg,png,pdf" }
                ],
                max_file_size: '5MB'
            },
            onError: function (error) {
                var errorMsg = {
                    TYPE: '上传图片格式为：JPG、PNG、JEPG、PDF格式',
                    SIZE: '图片大小不超过5M'
                }
                if (error) {
                    var $error = $(_this).siblings('.J-upload-error');
                    $error.show().find('label').html(errorMsg[error]).show();
                    timeout && clearTimeout(timeout);
                    timeout = setTimeout(function () {
                        $error.hide();
                    }, 3000)
                }
            },
            delCallback: function (item) {
                var id=item.data('id');
                delAttach(item,id);
            }
        });
    })

    var canEdit=$("#canEdit").val();
    if("0"==canEdit){
       $(".J-upload-btn-wrap").each(function () {
           $(this).remove()
       });
       $(".J-upload-del").each(function () {
           $(this).remove()
       })
    }
})
