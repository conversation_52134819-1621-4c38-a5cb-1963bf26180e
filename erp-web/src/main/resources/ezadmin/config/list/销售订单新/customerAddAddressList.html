<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>客户添加的地址列表</title> 
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="customerAddAddressList" datasource="erp-datasourcetarget" fixednumber="" fixednumberright="1" append_column_url="" append_row_url="" empty_show="" firstcol="numbers">
  <div id="appendHead">
   <style>
    li{
     position: relative;
     float: left;
     padding: 10px 0;
     display: list-item;
    }
    li>a{    color: #333;
     border-right: 1px solid #ddd;
     padding: 0 10px;}
    ul{    overflow: hidden;
     list-style: none outside none;}
    .rowButtons{
     min-width: 160px;
    }
   </style>
  </div>
  <div class="layui-fluid"> 
   <div class="layui-tab">
    <ul class="layui-tab-title" id="tab">
     <li item_name="traderContract11"><a href="/trader/customer/new/portrait.do?traderId=${TRADER_ID}&amp;traderCustomerId=${TRADER_CUSTOMERID}" item_name="traderContract11">客户360</a></li>
     <li item_name="traderContract1"><a href="/trader/customer/baseinfo.do?traderId=${TRADER_ID}&amp;traderCustomerId=${TRADER_CUSTOMERID}" item_name="traderContract1">基本信息</a></li>
     <li item_name="traderContract12"><a href="/trader/customer/new/distribution/link.do?traderId=${TRADER_ID}&amp;traderCustomerId=${TRADER_CUSTOMERID}" item_name="traderContract12">经销链路</a></li>
     <li item_name="traderContract2"><a href="/trader/customer/new/customeDetail.do?traderId=${TRADER_ID}&amp;traderCustomerId=${TRADER_CUSTOMERID}" item_name="traderContract2">客户行为</a></li>
     <li item_name="traderContract3"><a href="/trader/customer/getFinanceAndAptitude.do?traderId=${TRADER_ID}&amp;traderCustomerId=${TRADER_CUSTOMERID}" item_name="traderContract3">财务与资质信息</a></li>
     <li item_name="traderContract4"><a href="/ezadmin/list/list-traderContractList?TRADER_ID=${TRADER_ID}&amp;TRADER_CUSTOMERID=${TRADER_CUSTOMERID}&amp;perPageInt=50" item_name="traderContract4">联系人</a></li>
     <li item_name="addressInformation5"><a href="/ezadmin/list/list-addressInformationList?TRADER_ID=${TRADER_ID}&amp;TRADER_CUSTOMERID=${TRADER_CUSTOMERID}" item_name="addressInformation5">联系地址</a></li>
     <li item_name="customerAddAddress6"><a href="/ezadmin/list/list-customerAddAddressList?TRADER_ID=${TRADER_ID}&amp;TRADER_CUSTOMERID=${TRADER_CUSTOMERID}" item_name="customerAddAddress6">客户添加的地址</a></li>
     <li item_name="customerAddAddress8"><a href="/trader/customer/businesslist.do?traderId=${TRADER_ID}&amp;traderCustomerId=${TRADER_CUSTOMERID}" item_name="customerAddAddress8">交易记录</a></li>
     <li item_name="customerAddAddress9"><a href="/trader/customer/communicaterecord.do?traderId=${TRADER_ID}&amp;traderCustomerId=${TRADER_CUSTOMERID}" item_name="customerAddAddress9">沟通记录</a></li>
     <li item_name="customerAddAddress10"><a href="/trader/relation/info.do?traderId=${TRADER_ID}&amp;traderCustomerId=${TRADER_CUSTOMERID}" item_name="customerAddAddress10">关联客户</a></li>
     <li item_name="traderContract12"><a href="/trader/customer/new/share.do?traderId=${TRADER_ID}&amp;traderCustomerId=${TRADER_CUSTOMERID}" item_name="traderContract12">分享客户</a></li>
    </ul>
   </div> 
   <form class="layui-form" id="search">
    <div class=" layui-inline "><label class="layui-form-label">trader_id</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="hidden" name="TRADER_ID" placeholder style alias="tmaa" jdbctype data datatype oper validate_rules validate_messages> </object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">收件人</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="DELIVERY_NAME" placeholder style="" alias="tmaa" jdbctype="" data="" datatype="" oper="LIKE" validate_rules="" validate_messages=""> 
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">手机号码</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="TELPHONE" placeholder style="" alias="tmaa" jdbctype="" data="" datatype="" oper="LIKE" validate_rules="" validate_messages=""> 
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">traderCustomerId</label>
     <div class="layui-input-inline"><hidden class=" layui-input list-search-item " type="nowhere" name="TRADER_CUSTOMERID" placeholder="" style="" alias="" jdbctype="" data="" datatype="" oper=""></hidden>
     </div>
    </div>
   </form>
   <hr class="layui-border-blue"> 
   <div class="btn-group   bd-highlight" id="tableButton"> 
   </div> 
   <table id="table" class="layui-table" style=" width:100%"> 
    <thead> 
     <tr id="column"> 
      <th item_name="DELIVERY_NAME" name="DELIVERY_NAME" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">收件人</th>
      <th item_name="TELPHONE" name="TELPHONE" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">手机号码</th>
      <th item_name="AREA" name="AREA" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">所在地区</th>
      <th item_name="ADDRESS" name="ADDRESS" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">详细地址</th>
      <th item_name="TITLE_NAME" name="TITLE_NAME" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">标签名称</th>
      <th item_name="DEFAULT_ADDRESS" name="DEFAULT_ADDRESS" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">默认地址</th>
      <th item_name="CREATOR" name="CREATOR" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">添加人</th>
      <th type="rowbutton" id="rowbutton"></th>
     </tr> 
    </thead> 
    <tbody> 
     <tr> 
      <td> express:</td> 
      <td colspan="100"> <pre id="express" class="layui-code" orderby="order BY 	tmaa.ADD_TIME DESC" groupby="">StringBuilder sql=new StringBuilder();
       TRADER_ID=$$("TRADER_ID");
       sql.append("
select
	tmaa.*,
	CONCAT(IF(tmaa.IS_DELIVERY_DEFAULT = 1, '默认的收货地址;', ''), IF(tmaa.IS_INVOICE_DEFAULT = 1, '默认的收票地址;', '')) DEFAULT_ADDRESS
from
	T_MJX_ACCOUNT_ADDRESS tmaa
where
	tmaa.IS_ENABLE = 1
   ");
if(isBlank("TRADER_ID")){
              sql.append(" AND 1=2 " );
              }
return search(sql);
      </pre> </td>
     </tr> 
     <tr> 
      <td> count:</td> 
      <td colspan="100"> <pre id="count" class="layui-code"></pre> </td> 
     </tr> 
    </tbody> 
   </table> 
  </div> 
  <div id="appendFoot">
   <script>
   $(function(){
   $("#list-tab").removeClass("layui-tab-title");
   $("li[item_name=customerAddAddress6]").addClass("layui-text");

   })
   </script>
  </div>
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script> 
  <script>
    layui.use(function () {

    })
</script> 
 </body>
</html>