package com.vedeng.dwh.externaldb.dao.generate;

import com.vedeng.dwh.model.generate.DwdErpOrganizationNjDfDo;
import org.springframework.stereotype.Repository;

@Repository
public interface DwdErpOrganizationNjDfMapper {
    int deleteByPrimaryKey(Long vdOdsPk);

    int insert(DwdErpOrganizationNjDfDo record);

    int insertSelective(DwdErpOrganizationNjDfDo record);

    DwdErpOrganizationNjDfDo selectByPrimaryKey(Long vdOdsPk);

    int updateByPrimaryKeySelective(DwdErpOrganizationNjDfDo record);

    int updateByPrimaryKey(DwdErpOrganizationNjDfDo record);
}