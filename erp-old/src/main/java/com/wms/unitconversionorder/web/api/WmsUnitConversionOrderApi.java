package com.wms.unitconversionorder.web.api;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.wms.unitconversionorder.service.WmsUnitConversionOrderService;
import com.wms.unitconversionorder.model.dto.WmsUnitConversionOrderDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @description json 接口
 * @date 2023/2/17 11:01
 **/
@ExceptionController
@RestController
@Slf4j
@RequestMapping("/wmsUnitConversionOrder")
public class WmsUnitConversionOrderApi {

    @Autowired
    private WmsUnitConversionOrderService wmsUnitConversionOrderService;

    /**
     * 保存库存转换单
     *
     * @param wmsUnitConversionOrderDto 数据
     * @return 主键id
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public R<Integer> add(@RequestBody WmsUnitConversionOrderDto wmsUnitConversionOrderDto, HttpServletRequest request) {
        Integer wmsUnitConversionOrderId = wmsUnitConversionOrderService.add(wmsUnitConversionOrderDto);
        // 发起审核流程
        wmsUnitConversionOrderService.applyAudit(request, wmsUnitConversionOrderId);
        return R.success(wmsUnitConversionOrderId);
    }


    /**
     * 保存库存转换单
     *
     * @param wmsUnitConversionOrderDto 数据
     * @return 主键id
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public R<Integer> edit(@RequestBody WmsUnitConversionOrderDto wmsUnitConversionOrderDto, HttpServletRequest request) {
        wmsUnitConversionOrderService.edit(wmsUnitConversionOrderDto);
        // 发起审核流程
        wmsUnitConversionOrderService.applyAudit(request, wmsUnitConversionOrderDto.getWmsUnitConversionOrderId());
        return R.success();
    }


    /**
     * 获取订单页面信息 商品 商品明细 + 库存信息
     *
     * @param wmsUnitConversionOrderId 主键
     * @return 数据
     */
    @RequestMapping(value = "/getViewData")
    @NoNeedAccessAuthorization
    public R<WmsUnitConversionOrderDto> getViewData(@RequestParam Integer wmsUnitConversionOrderId) {
        return R.success(wmsUnitConversionOrderService.getViewData(wmsUnitConversionOrderId));
    }

    /**
     * 出库记录
     * @param wmsUnitConversionOrderId 转换单id
     * @return data
     */
    @RequestMapping(value = "/getOutLog")
    @NoNeedAccessAuthorization
    public R<List<WarehouseGoodsOperateLog>> getOutLog(@RequestParam Integer wmsUnitConversionOrderId) {
        return R.success(wmsUnitConversionOrderService.getOutLog(wmsUnitConversionOrderId));
    }

    /**
     * 入库记录
     * @param wmsUnitConversionOrderId 转换单id
     * @return data
     */
    @RequestMapping(value = "/getInLog")
    @NoNeedAccessAuthorization
    public R<List<WarehouseGoodsOperateLog>> getInLog(@RequestParam Integer wmsUnitConversionOrderId) {
        return R.success(wmsUnitConversionOrderService.getInLog(wmsUnitConversionOrderId));
    }

    @RequestMapping(value = "/getStockNum")
    @NoNeedAccessAuthorization
    public R<Integer> getStockNum(@RequestParam String skuNo) {
        return R.success(wmsUnitConversionOrderService.getSkuStockNum(skuNo));
    }
}
