package com.vedeng.erp.system.domain.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 业务流转单主表
 */
@Getter
@Setter
public class FlowOrderReqDto implements Serializable {

    private static final long serialVersionUID = -3430298037138485870L;
    /**
     * 流转单号
     */
    private String flowOrderNo;

    /**
     * 业务单号
     */
    private String baseOrderNo;

    /**
     * 类型 1.采购 2.销售
     */
    private Integer baseBusinessType;

    /**
     * 审核状态，0:未审核, 1:已审核
     */
    private Integer auditStatus;

    /**
     * 末级名称
     */
    private String lastTraderName;

    /**
     * sku : V12345
     */
    private String skuNo;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 创建人id
     */
    private List<String> creatorList;

    /**
     * 创建时间 起 2025-01-01 00:00:00
     */
    private String startAddTime;

    /**
     * 创建时间 止 2025-01-01 23:59:59
     */
    private String endAddTime;

    /**
     * 末级合同状态：0-无需上传（末级节点为贝登子公司），1-未上传（末级节点非贝登子公司且未上传合同），2-已上传（末级节点非贝登子公司且已上传合同）
     */
    private Integer contractStatus;

    /**
     * 推送方向 1:金蝶, 2:ERP
     */
    private Integer pushDirection;
    
    private String sourceErp;
}
