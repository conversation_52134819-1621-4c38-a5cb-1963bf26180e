package com.vedeng.aftersales.dao;

import com.vedeng.aftersales.model.AfterSaleServiceStandardApplyAttachment;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AfterSaleServiceStandardApplyAttachmentMapper {
    int deleteByPrimaryKey(Long serviceStandardApplyAttachmentId);

    int insert(AfterSaleServiceStandardApplyAttachment record);

    int insertSelective(AfterSaleServiceStandardApplyAttachment record);

    AfterSaleServiceStandardApplyAttachment selectByPrimaryKey(Long serviceStandardApplyAttachmentId);

    int updateByPrimaryKeySelective(AfterSaleServiceStandardApplyAttachment record);

    int updateByPrimaryKey(AfterSaleServiceStandardApplyAttachment record);

    void deleteByServiceStandardApplyId(@Param("serviceStandardApplyId") Long serviceStandardApplyId);

    void batchInsertAttashment(List<AfterSaleServiceStandardApplyAttachment> attashmentList);

    List<AfterSaleServiceStandardApplyAttachment> selectByApplyId(@Param("serviceStandardApplyId") Long serviceStandardApplyId);
}