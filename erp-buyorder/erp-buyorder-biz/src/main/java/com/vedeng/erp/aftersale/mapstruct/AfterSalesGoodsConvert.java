package com.vedeng.erp.aftersale.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.aftersale.domain.entity.BuyOrderAfterSalesGoodsEntity;
import com.vedeng.erp.aftersale.dto.AfterSalesGoodsDto;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, builder = @Builder(disableBuilder = true))
public interface AfterSalesGoodsConvert extends BaseMapStruct<BuyOrderAfterSalesGoodsEntity, AfterSalesGoodsDto> {
}