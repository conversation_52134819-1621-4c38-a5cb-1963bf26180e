package com.vedeng.erp.aftersale.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AfterSalesFollowUpRecordDto {

    private Integer recordId;

    private Integer afterSalesId;

    private String afterSalesNo;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operateDate;

    private Integer orgId;

    private String content;

    private String operationalMatters;

    private Long addTime;

    private Long modeTime;

    private Integer creator;

    private Integer updater;

    private Integer isDelete;

}
