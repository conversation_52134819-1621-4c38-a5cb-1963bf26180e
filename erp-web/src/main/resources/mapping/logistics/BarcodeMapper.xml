<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.logistics.dao.BarcodeMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.logistics.model.Barcode" >
    <id column="BARCODE_ID" property="barcodeId" jdbcType="INTEGER" />
    <result column="BARCODE" property="barcode" jdbcType="VARCHAR" />
    <result column="TYPE" property="type" jdbcType="INTEGER" />
    <result column="DETAIL_GOODS_ID" property="detailGoodsId" jdbcType="INTEGER" />
    <result column="GOODS_ID" property="goodsId" jdbcType="INTEGER" />
    <result column="SEQUENCE" property="sequence" jdbcType="INTEGER" />
    <result column="IS_ENABLE" property="isEnable" jdbcType="BIT" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
    <result column="FTPPATH" property="ftpPath" jdbcType="VARCHAR" />
    <result column="CREATORNAME" property="creatorName" jdbcType="VARCHAR" />
    <result column="DOMAIN" property="domain" jdbcType="VARCHAR" />
    <result column="WAREHOUSE_IN_TIME" property="warehouseInTime" jdbcType="BIGINT" />
    <result column="COMMENT" property="comment" jdbcType="VARCHAR" />
    <result column="STORAGEADDRESS" property="storageAddress" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    BARCODE_ID, BARCODE, TYPE, DETAIL_GOODS_ID, GOODS_ID, SEQUENCE, IS_ENABLE, ADD_TIME, 
    CREATOR, MOD_TIME, UPDATER
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from T_BARCODE
    where BARCODE_ID = #{barcodeId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from T_BARCODE
    where BARCODE_ID = #{barcodeId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.logistics.model.Barcode" >
    insert into T_BARCODE (BARCODE_ID, BARCODE, TYPE, DETAIL_GOODS_ID, 
      GOODS_ID, SEQUENCE, IS_ENABLE, 
      ADD_TIME, CREATOR, MOD_TIME, 
      UPDATER)
    values (#{barcodeId,jdbcType=INTEGER}, #{barcode,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, #{detailGoodsId,jdbcType=INTEGER}, 
      #{goodsId,jdbcType=INTEGER}, #{sequence,jdbcType=INTEGER}, #{isEnable,jdbcType=BIT}, 
      #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, 
      #{updater,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.logistics.model.Barcode" useGeneratedKeys="true" keyProperty="barcodeId">
    insert into T_BARCODE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="barcodeId != null" >
        BARCODE_ID,
      </if>
      <if test="barcode != null" >
        BARCODE,
      </if>
      <if test="type != null" >
        TYPE,
      </if>
      <if test="detailGoodsId != null" >
        DETAIL_GOODS_ID,
      </if>
      <if test="goodsId != null" >
        GOODS_ID,
      </if>
      <if test="sequence != null" >
        SEQUENCE,
      </if>
      <if test="isEnable != null" >
        IS_ENABLE,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="barcodeId != null" >
        #{barcodeId,jdbcType=INTEGER},
      </if>
      <if test="barcode != null" >
        #{barcode,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        #{type,jdbcType=INTEGER},
      </if>
      <if test="detailGoodsId != null" >
        #{detailGoodsId,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null" >
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="sequence != null" >
        #{sequence,jdbcType=INTEGER},
      </if>
      <if test="isEnable != null" >
        #{isEnable,jdbcType=BIT},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
   <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.logistics.model.Barcode" >
	    update T_BARCODE
	    <set >
	      <if test="barcode != null" >
	        BARCODE = #{barcode,jdbcType=VARCHAR},
	      </if>
	      <if test="type != null" >
	        TYPE = #{type,jdbcType=INTEGER},
	      </if>
	      <if test="detailGoodsId != null" >
	        DETAIL_GOODS_ID = #{detailGoodsId,jdbcType=INTEGER},
	      </if>
	      <if test="goodsId != null" >
	        GOODS_ID = #{goodsId,jdbcType=INTEGER},
	      </if>
	      <if test="sequence != null" >
	        SEQUENCE = #{sequence,jdbcType=INTEGER},
	      </if>
	      <if test="isEnable != null" >
	        IS_ENABLE = #{isEnable,jdbcType=BIT},
	      </if>
	      <if test="addTime != null" >
	        ADD_TIME = #{addTime,jdbcType=BIGINT},
	      </if>
	      <if test="creator != null" >
	        CREATOR = #{creator,jdbcType=INTEGER},
	      </if>
	      <if test="modTime != null" >
	        MOD_TIME = #{modTime,jdbcType=BIGINT},
	      </if>
	      <if test="updater != null" >
	        UPDATER = #{updater,jdbcType=INTEGER},
	      </if>
	    </set>
	    where BARCODE_ID = #{barcodeId,jdbcType=INTEGER}
  </update>
  <update id="updateBarcodes">
    update T_BARCODE
    <trim prefix="set" suffixOverrides=",">
    	<trim prefix="BARCODE=case" suffix="end,">
		     <foreach collection="list" index="index" item="bar" >
			      <if test="bar.barcode != null" >
			      	when BARCODE_ID=#{bar.barcodeId} then  #{bar.barcode,jdbcType=VARCHAR}
			      </if>
			 </foreach>
		 </trim>
		 <trim prefix="TYPE=case" suffix="end,">
		     <foreach collection="list" index="index" item="bar" >
			      <if test="bar.type != null" >
			      	when BARCODE_ID=#{bar.barcodeId} then  #{bar.type,jdbcType=BIT}
			      </if>
			 </foreach>
		 </trim>
		 <trim prefix="DETAIL_GOODS_ID=case" suffix="end,">
		     <foreach collection="list" index="index" item="bar" >
			      <if test="bar.detailGoodsId != null" >
			      	when BARCODE_ID=#{bar.barcodeId} then  #{bar.detailGoodsId,jdbcType=INTEGER}
			      </if>
			 </foreach>
		 </trim>
		 <trim prefix="GOODS_ID=case" suffix="end,">
		     <foreach collection="list" index="index" item="bar" >
			      <if test="bar.goodsId != null" >
			      	when BARCODE_ID=#{bar.barcodeId} then  #{bar.goodsId,jdbcType=INTEGER}
			      </if>
			 </foreach>
		 </trim>
		 <trim prefix="SEQUENCE=case" suffix="end,">
		     <foreach collection="list" index="index" item="bar" >
			      <if test="bar.sequence != null" >
			      	when BARCODE_ID=#{bar.barcodeId} then  #{bar.sequence,jdbcType=INTEGER}
			      </if>
			 </foreach>
		 </trim>
		 <trim prefix="IS_ENABLE=case" suffix="end,">
		     <foreach collection="list" index="index" item="bar" >
			      <if test="bar.isEnable != null" >
			      	when BARCODE_ID=#{bar.barcodeId} then  #{bar.isEnable,jdbcType=BIT}
			      </if>
			 </foreach>
		 </trim>
		 <trim prefix="ADD_TIME=case" suffix="end,">
		     <foreach collection="list" index="index" item="bar" >
			      <if test="bar.addTime != null" >
			      	when BARCODE_ID=#{bar.barcodeId} then  #{bar.addTime,jdbcType=BIGINT}
			      </if>
			 </foreach>
		 </trim>
		 <trim prefix="CREATOR=case" suffix="end,">
		     <foreach collection="list" index="index" item="bar" >
			      <if test="bar.creator != null" >
			      	when BARCODE_ID=#{bar.barcodeId} then  #{bar.creator,jdbcType=INTEGER}
			      </if>
			 </foreach>
		 </trim>
		 <trim prefix="MOD_TIME=case" suffix="end,">
		     <foreach collection="list" index="index" item="bar" >
			      <if test="bar.modTime != null" >
			      	when BARCODE_ID=#{bar.barcodeId} then  #{bar.modTime,jdbcType=BIGINT}
			      </if>
			 </foreach>
		 </trim>
		 <trim prefix="UPDATER=case" suffix="end,">
		     <foreach collection="list" index="index" item="bar" >
			      <if test="bar.updater != null" >
			      	when BARCODE_ID=#{bar.barcodeId} then  #{bar.updater,jdbcType=INTEGER}
			      </if>
			 </foreach>
		 </trim>
	</trim>
    where  
    <foreach collection="list" index="index" item="bar" separator="," open="BARCODE_ID in (" close=")">
    	#{bar.barcodeId,jdbcType=INTEGER}
    </foreach> 
    and BARCODE_ID 
    not in (select l.BARCODE_ID from T_WAREHOUSE_GOODS_OPERATE_LOG l 
    where l.IS_ENABLE=1 AND l.OPERATE_TYPE IN (1,3,5,8,9) and l.BARCODE_ID in 
    <foreach collection="list" index="index" item="bar" separator="," open="(" close="))">
    	#{bar.barcodeId,jdbcType=INTEGER}
    </foreach> 
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.logistics.model.Barcode" >
    update T_BARCODE
    set BARCODE = #{barcode,jdbcType=VARCHAR},
      TYPE = #{type,jdbcType=INTEGER},
      DETAIL_GOODS_ID = #{detailGoodsId,jdbcType=INTEGER},
      GOODS_ID = #{goodsId,jdbcType=INTEGER},
      SEQUENCE = #{sequence,jdbcType=INTEGER},
      IS_ENABLE = #{isEnable,jdbcType=BIT},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER}
    where BARCODE_ID = #{barcodeId,jdbcType=INTEGER}
  </update>
  
  <resultMap id="BarcodeResultMap" type="com.vedeng.logistics.model.Barcode" extends="BaseResultMap">
  	<collection property="buyorderGood" ofType="com.vedeng.order.model.vo.BuyorderGoodsVo">
	    <id column="BUYORDER_GOODS_ID" property="buyorderGoodsId" jdbcType="INTEGER" />
    	<result column="BUYORDER_ID" property="buyorderId" jdbcType="INTEGER" />
	    <result column="GOODS_ID" property="goodsId" jdbcType="INTEGER" />
	    <result column="SKU" property="sku" jdbcType="VARCHAR" />
	    <result column="GOODS_NAME" property="goodsName" jdbcType="VARCHAR" />
	    <result column="BRAND_NAME" property="brandName" jdbcType="VARCHAR" />
	    <result column="MODEL" property="model" jdbcType="VARCHAR" />
	    <result column="UNIT_NAME" property="unitName" jdbcType="VARCHAR" />
	    <result column="PRICE" property="price" jdbcType="DECIMAL" />
	    <result column="CURRENCY_UNIT_ID" property="currencyUnitId" jdbcType="INTEGER" />
	    <result column="NUM" property="num" jdbcType="INTEGER" />
	    <result column="ARRIVAL_NUM" property="arrivalNum" jdbcType="INTEGER" />
	    <result column="ESTIMATE_DELIVERY_TIME" property="estimateDeliveryTime" jdbcType="BIGINT" />
	    <result column="ESTIMATE_ARRIVAL_TIME" property="estimateArrivalTime" jdbcType="BIGINT" />
	    <result column="ARRIVAL_USER_ID" property="arrivalUserId" jdbcType="INTEGER" />
	    <result column="ARRIVAL_STATUS" property="arrivalStatus" jdbcType="BIT" />
	    <result column="ARRIVAL_TIME" property="arrivalTime" jdbcType="BIGINT" />
	    <result column="IS_DELETE" property="isDelete" jdbcType="BIT" />
	    <result column="INSIDE_COMMENTS" property="insideComments" jdbcType="VARCHAR" />
	    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
	    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
	    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
	    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
	  	<result column="MATERIAL_CODE" property="materialCode" jdbcType="VARCHAR" />
	  	<result column="MANAGE_CATEGORY_NAME" property="manageCategoryName" jdbcType="VARCHAR" />
	  	<result column="PURCHASE_REMIND" property="purchaseRemind" jdbcType="VARCHAR" />
	  	<result column="REGISTRATION_NUMBER" property="registrationNumber" jdbcType="VARCHAR" />
	  	<result column="PACKING_LIST" property="packingList" jdbcType="VARCHAR" />
	    <result column="TOS" property="tos" jdbcType="VARCHAR" />
  	</collection>
  </resultMap>
  <!-- 获取二维码信息列表    -->
  <select id="getBarcode" resultMap="BarcodeResultMap" parameterType="com.vedeng.logistics.model.Barcode" >
    select a.*,c.MATERIAL_CODE,d.URI as FTPPATH,d.DOMAIN,e.COMMENTS as COMMENT, 
	    CONCAT(
			IFNULL(j.WAREHOUSE_NAME, ''), ' ',
			IFNULL(f.STORAGE_ROOM_NAME, ''), ' ',
			IFNULL(g.STORAGE_AREA_NAME, ''), ' ',
			IFNULL(h.STORAGE_RACK_NAME, ''), ' ',
			IFNULL(i.STORAGE_LOCATION_NAME, '')
		) AS STORAGEADDRESS
    <if test="type==1">
    ,b.BUYORDER_ID,b.SKU,b.GOODS_NAME,b.BRAND_NAME,b.MODEL,b.UNIT_NAME,b.PRICE,b.CURRENCY_UNIT_ID,
    b.NUM,b.ARRIVAL_NUM,b.ESTIMATE_DELIVERY_TIME,b.ARRIVAL_USER_ID,b.ARRIVAL_STATUS,b.ARRIVAL_TIME
    </if>
    <if test="type==2 or type==3" >
    ,b.*,c.GOODS_NAME,c.MODEL
    </if>
    from T_BARCODE a 
    <if test="type==1">
    left join T_BUYORDER_GOODS b on a.DETAIL_GOODS_ID = b.BUYORDER_GOODS_ID
    </if>
    <if test="type==2 or type==3">
    left join T_AFTER_SALES_GOODS b on a.DETAIL_GOODS_ID = b.AFTER_SALES_GOODS_ID
   <!--  left join T_AFTER_SALES e on b.AFTER_SALES_ID = e.AFTER_SALES_ID
    left join T_SALEORDER s on a.DETAIL_GOODS_ID = s.SALEORDER_ID and e.SUBJECT_TYPE = 535
    left join T_BUYORDER_GOODS b on a.DETAIL_GOODS_ID = b.BUYORDER_GOODS_ID and e.SUBJECT_TYPE = 535 -->
    </if>
    left join T_GOODS c on b.GOODS_ID = c.GOODS_ID
    left join T_ATTACHMENT d on a.BARCODE_ID = d.RELATED_ID and d.ATTACHMENT_TYPE = 500
    left join T_WAREHOUSE_GOODS_SET e on e.GOODS_ID = b.GOODS_ID
    LEFT JOIN T_WAREHOUSE j ON j.WAREHOUSE_ID = e.WAREHOUSE_ID
	LEFT JOIN T_STORAGE_ROOM f ON f.STORAGE_ROOM_ID = e.STORAGE_ROOM_ID
	LEFT JOIN T_STORAGE_AREA g ON g.STORAGE_AREA_ID = e.STORAGE_AREA_ID
	LEFT JOIN T_STORAGE_RACK h ON h.STORAGE_RACK_ID = e.STORAGE_RACK_ID
	LEFT JOIN T_STORAGE_LOCATION i ON i.STORAGE_LOCATION_ID = e.STORAGE_LOCATION_ID
    where 1=1 
    <if test="barcodeId!=null">
    	and a.BARCODE_ID = #{barcodeId,jdbcType=INTEGER}
    </if>
   	<if test="barcode!=null">
		and a.BARCODE=#{barcode,jdbcType=VARCHAR}
	</if>
	<if test="type!=null">
		and a.TYPE=#{type,jdbcType=INTEGER}
	</if>
	<if test="detailGoodsId !=null">
		and a.DETAIL_GOODS_ID=#{detailGoodsId,jdbcType=INTEGER}
	</if>
	<if test="goodsId!=null">
		and a.GOODS_ID=#{goodsId,jdbcType=INTEGER}
	</if>
	<if test="sequence!=null">
		and a.SEQUENCE=#{sequence,jdbcType=INTEGER}
	</if>
	<if test="isEnable!=null and isEnable==4">
		and a.IS_ENABLE= 1
	</if>
	<if test="creator!=null">
		and a.CREATOR=#{creator,jdbcType=INTEGER}
	</if>
	<if test="updater!=null">
		and a.UPDATER=#{updater,jdbcType=INTEGER}
	</if>
	<if test="buyorderIds != null and buyorderIds.size() > 0">
			 and a.DETAIL_GOODS_ID in 
				<foreach collection="buyorderIds" item="list" separator="," open="(" close=")">
					#{list,jdbcType=INTEGER}
				</foreach>
	</if>
	
	order by a.SEQUENCE DESC
  </select>
  <!-- 查询条码入库记录 -->
  <select id="getBarcodeInCnt" resultType="com.vedeng.logistics.model.WarehouseGoodsOperateLog" parameterType="com.vedeng.logistics.model.Barcode" >
	    SELECT
			*
		FROM
			T_WAREHOUSE_GOODS_OPERATE_LOG
		WHERE
			BARCODE_ID = #{barcodeId,jdbcType=INTEGER}
		AND IS_ENABLE =1 AND OPERATE_TYPE IN (1,3,5,8,9)
  </select>
  
  <!-- 获取二维码信息列表 -->
  <select id="getWarehouseInBarcode" resultMap="BarcodeResultMap" parameterType="com.vedeng.logistics.model.Barcode" >
    select a.*,c.MATERIAL_CODE,d.URI as FTPPATH,d.DOMAIN,c.GOODS_NAME,c.MODEL,wgol.ADD_TIME as WAREHOUSE_IN_TIME,wgol.COMMENTS as COMMENT,
     CONCAT(
			IFNULL(j.WAREHOUSE_NAME, ''), ' ',
			IFNULL(k.STORAGE_ROOM_NAME, ''), ' ',
			IFNULL(g.STORAGE_AREA_NAME, ''), ' ',
			IFNULL(h.STORAGE_RACK_NAME, ''), ' ',
			IFNULL(i.STORAGE_LOCATION_NAME, '')
		) AS STORAGEADDRESS
    from T_WAREHOUSE_GOODS_OPERATE_LOG wgol 
    LEFT JOIN T_BUYORDER_GOODS e ON e.BUYORDER_GOODS_ID = wgol.RELATED_ID
	LEFT JOIN T_BARCODE a ON a.BARCODE_ID = wgol.BARCODE_ID
	LEFT JOIN T_GOODS c ON a.GOODS_ID = c.GOODS_ID
	LEFT JOIN T_ATTACHMENT d ON a.BARCODE_ID = d.RELATED_ID
	LEFT JOIN T_WAREHOUSE_GOODS_SET f on f.GOODS_ID = c.GOODS_ID
	LEFT JOIN T_WAREHOUSE j ON j.WAREHOUSE_ID = wgol.WAREHOUSE_ID
	LEFT JOIN T_STORAGE_ROOM k ON k.STORAGE_ROOM_ID = wgol.STORAGE_ROOM_ID
	LEFT JOIN T_STORAGE_AREA g ON g.STORAGE_AREA_ID = wgol.STORAGE_AREA_ID
	LEFT JOIN T_STORAGE_RACK h ON h.STORAGE_RACK_ID = wgol.STORAGE_RACK_ID
	LEFT JOIN T_STORAGE_LOCATION i ON i.STORAGE_LOCATION_ID = wgol.STORAGE_LOCATION_ID
    where 1=1
    <if test="buyorderId!=null">
    	and e.BUYORDER_ID = #{buyorderId,jdbcType=INTEGER}
    </if> 
    <if test="barcodeId!=null">
    	and a.BARCODE_ID = #{barcodeId,jdbcType=INTEGER}
    </if>
   	<if test="barcode!=null">
		and a.BARCODE=#{barcode,jdbcType=VARCHAR}
	</if>
	<if test="type!=null">
		and a.TYPE=#{type,jdbcType=INTEGER}
	</if>
	<if test="detailGoodsId!=null">
		and a.DETAIL_GOODS_ID=#{detailGoodsId,jdbcType=INTEGER}
	</if>
	<if test="goodsId!=null">
		and a.GOODS_ID=#{goodsId,jdbcType=INTEGER}
	</if>
	<if test="sequence!=null">
		and a.SEQUENCE=#{sequence,jdbcType=INTEGER}
	</if>
	<if test="isEnable!=null and isEnable==4">
		and a.IS_ENABLE= 1
	</if>
	<if test="creator!=null">
		and a.CREATOR=#{creator,jdbcType=INTEGER}
	</if>
	<if test="updater!=null">
		and a.UPDATER=#{updater,jdbcType=INTEGER}
	</if>
	<if test="searchBegintime != null and searchBegintime != 0">
		and wgol.ADD_TIME <![CDATA[>=]]> #{searchBegintime} 
	</if>
	<if test="searchEndtime != null and searchEndtime != 0">
		and wgol.ADD_TIME <![CDATA[<=]]> #{searchEndtime} 
	</if>
	and d.ATTACHMENT_TYPE = 500 and d.ATTACHMENT_FUNCTION = 499
	and wgol.OPERATE_TYPE = 1
	and wgol.IS_ENABLE = 1
	order by e.BUYORDER_GOODS_ID ASC,a.SEQUENCE DESC
  </select>
    <insert id="insertSelectiveBatch" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="barcodeId">
    insert into T_BARCODE
    	(BARCODE,
    	TYPE,
    	DETAIL_GOODS_ID,
    	GOODS_ID,
    	SEQUENCE,
    	IS_ENABLE,
    	ADD_TIME,
    	CREATOR,
    	MOD_TIME,
    	UPDATER
    	)
    values
    <foreach collection="list" item="barcode" index="index" separator="," >  
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="barcode.barcode != null" >
        #{barcode.barcode,jdbcType=VARCHAR},
      </if>
      <if test="barcode.barcode == null" >
       null,
      </if>
      <if test="barcode.type != null" >
        #{barcode.type,jdbcType=INTEGER},
      </if>
      <if test="barcode.type == null" >
       1,
      </if>
      <if test="barcode.detailGoodsId != null" >
        #{barcode.detailGoodsId,jdbcType=INTEGER},
      </if>
      <if test="barcode.detailGoodsId == null" >
        0,
      </if>
      <if test="barcode.goodsId != null" >
        #{barcode.goodsId,jdbcType=INTEGER},
      </if>
      <if test="barcode.goodsId == null" >
        0,
      </if>
      <if test="barcode.sequence != null" >
        #{barcode.sequence,jdbcType=INTEGER},
      </if>
      <if test="barcode.sequence == null" >
        0,
      </if>
      <if test="barcode.isEnable != null" >
        #{barcode.isEnable,jdbcType=BIT},
      </if>
      <if test="barcode.isEnable == null" >
        0,
      </if>
      <if test="barcode.addTime != null" >
        #{barcode.addTime,jdbcType=BIGINT},
      </if>
      <if test="barcode.addTime == null" >
        0,
      </if>
      <if test="barcode.creator != null" >
        #{barcode.creator,jdbcType=INTEGER},
      </if>
      <if test="barcode.creator == null" >
        0,
      </if>
      <if test="barcode.modTime != null" >
        #{barcode.modTime,jdbcType=BIGINT},
      </if>
      <if test="barcode.modTime == null" >
        0,
      </if>
      <if test="barcode.updater != null" >
        #{barcode.updater,jdbcType=INTEGER},
      </if>
      <if test="barcode.updater == null" >
        0,
      </if>
    </trim>
    </foreach>
  </insert>
  
  <update id="updateByPrimaryKeySelectiveBatch" parameterType="java.util.List" >
   UPDATE T_BARCODE
        SET BARCODE = CASE BARCODE_ID 
        	<foreach collection="list" item="Barcode" index="index" separator="" >
        	  WHEN #{Barcode.barcodeId,jdbcType=INTEGER} THEN #{Barcode.barcode,jdbcType=INTEGER} 
			</foreach>
        END
    WHERE BARCODE_ID IN 
    	<foreach collection="list" item="Barcode" separator="," open="(" close=")">
					#{Barcode.barcodeId,jdbcType=INTEGER}
		</foreach>
  </update>
  <!-- 获取售后二维码信息列表 -->
  <select id="getWarehouseShInBarcode" resultMap="BarcodeResultMap" parameterType="com.vedeng.logistics.model.Barcode" >
	   SELECT
		c.*, d.MATERIAL_CODE,
		e.URI AS FTPPATH,
		e.DOMAIN,
		d.GOODS_NAME,
		d.MODEL,
		a.ADD_TIME AS WAREHOUSE_IN_TIME,
		a.COMMENTS,
		CONCAT(
			IFNULL(j.WAREHOUSE_NAME, ''), ' ',
			IFNULL(k.STORAGE_ROOM_NAME, ''), ' ',
			IFNULL(g.STORAGE_AREA_NAME, ''), ' ',
			IFNULL(h.STORAGE_RACK_NAME, ''), ' ',
			IFNULL(i.STORAGE_LOCATION_NAME, '')
		) AS STORAGEADDRESS
		FROM
			T_WAREHOUSE_GOODS_OPERATE_LOG a
		LEFT JOIN T_AFTER_SALES_GOODS b ON a.RELATED_ID = b.AFTER_SALES_GOODS_ID
		LEFT JOIN T_BARCODE c ON a.BARCODE_ID = c.BARCODE_ID
		LEFT JOIN T_GOODS d ON a.GOODS_ID = d.GOODS_ID
		LEFT JOIN T_ATTACHMENT e ON a.BARCODE_ID = e.RELATED_ID
		LEFT JOIN T_WAREHOUSE_GOODS_SET f ON f.GOODS_ID = c.GOODS_ID
		LEFT JOIN T_WAREHOUSE j ON j.WAREHOUSE_ID = a.WAREHOUSE_ID
		LEFT JOIN T_STORAGE_ROOM k ON k.STORAGE_ROOM_ID = a.STORAGE_ROOM_ID
		LEFT JOIN T_STORAGE_AREA g ON g.STORAGE_AREA_ID = a.STORAGE_AREA_ID
		LEFT JOIN T_STORAGE_RACK h ON h.STORAGE_RACK_ID = a.STORAGE_RACK_ID
		LEFT JOIN T_STORAGE_LOCATION i ON i.STORAGE_LOCATION_ID = a.STORAGE_LOCATION_ID
		WHERE
			1 = 1
		AND b.AFTER_SALES_ID = #{buyorderId,jdbcType=INTEGER}
		AND e.ATTACHMENT_TYPE = 500
		AND a.IS_ENABLE = 1
		<if test="searchBegintime != null and searchBegintime != 0">
		    and a.ADD_TIME <![CDATA[>=]]> #{searchBegintime} 
		</if>
		<if test="searchEndtime != null and searchEndtime != 0">
			and a.ADD_TIME <![CDATA[<=]]> #{searchEndtime} 
		</if>
		ORDER BY
			b.AFTER_SALES_GOODS_ID ASC,
			c.SEQUENCE DESC
  </select>
  <select id="getBarcodeByWarehouseGoodsOperateLogId" resultMap="BarcodeResultMap">
   select 
     b.BARCODE_ID, b.BARCODE, b.TYPE, b.DETAIL_GOODS_ID, b.GOODS_ID, b.SEQUENCE, b.IS_ENABLE, b.ADD_TIME, 
    b.CREATOR, b.MOD_TIME, b.UPDATER
	FROM
	`T_WAREHOUSE_GOODS_OPERATE_LOG` a
	LEFT JOIN T_BARCODE b
	ON a.`BARCODE_ID` = b.`BARCODE_ID`
	WHERE a.`WAREHOUSE_GOODS_OPERATE_LOG_ID`=#{id,jdbcType=INTEGER}
  </select>
  <select id="getBuyOrderNoByBarcodeId" parameterType="com.vedeng.logistics.model.Barcode" resultType="java.lang.String">
	  SELECT
	  c.`BUYORDER_NO`
	FROM
	  `T_BARCODE` a
	  LEFT JOIN `T_BUYORDER_GOODS` b
	    ON a.`DETAIL_GOODS_ID` = b.`BUYORDER_GOODS_ID`
	  LEFT JOIN `T_BUYORDER` c ON b.`BUYORDER_ID`=c.`BUYORDER_ID`
	  WHERE a.`BARCODE_ID`=#{barcodeId,jdbcType=INTEGER}
  </select>
  <select id="getBarcodeByBarcode" parameterType="java.lang.String" resultMap="BaseResultMap">
	  SELECT * FROM T_BARCODE A WHERE A.BARCODE = #{barcode,jdbcType=VARCHAR}
  </select>

	<select id="getBarcodeIdByBarcode" resultType="java.lang.Integer">
		SELECT
			BARCODE_ID
		FROM
			`T_BARCODE`
		WHERE
			BARCODE = #{barcode,jdbcType=VARCHAR}
	</select>
</mapper>