package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.domain.command.KingDeePurchaseBackCommand;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购退料单 dto  https://www.yuque.com/manhuo/gf1570/gs91wo
 * @date
 */
@Mapper(componentModel = "spring")
public interface KingDeePurchaseBackCommandConvertor extends BaseCommandMapStruct<KingDeePurchaseBackCommand, KingDeePurchaseBackDto> {

    @Mapping(target = "FId", source = "FId")
    @Mapping(target = "FBillNo", source = "FBillNo")
    @Mapping(target = "f_qzok_bddjtId", source = "FQzokBddjtId")
    @Mapping(target = "FDate", source = "FDate")
    @Mapping(target = "FMrType", source = "FMrType")
    @Mapping(target = "FMrMode", source = "FMrMode")
    @Mapping(target = "FBillTypeId.FNumber", source = "FBillTypeId")
    @Mapping(target = "FStockOrgId.FNumber", source = "FStockOrgId")
    @Mapping(target = "FSupplierId.FNumber", source = "FSupplierId")
    @Override
    KingDeePurchaseBackCommand toCommand(KingDeePurchaseBackDto dto);

    @Mapping(target = "FRmrealqty", source = "FRmrealqty")
    @Mapping(target = "FTaxPrice", source = "FTaxPrice")
    @Mapping(target = "FEntryTaxRate", source = "FEntryTaxRate")
    @Mapping(target = "FNote", source = "FNote")
    @Mapping(target = "f_qzok_ysddh", source = "f_qzok_ysddh")
    @Mapping(target = "f_qzok_gsywdh", source = "f_qzok_gsywdh")
    @Mapping(target = "f_qzok_ywlx", source = "f_qzok_ywlx")
    @Mapping(target = "f_qzok_pch", source = "f_qzok_pch")

    @Mapping(target = "f_qzok_xlh", source = "f_qzok_xlh")
    @Mapping(target = "f_qzok_sqlx", source = "f_qzok_sqlx")
    @Mapping(target = "f_qzok_sfzf", source = "f_qzok_sfzf")
    @Mapping(target = "f_qzok_bddjhid", source = "f_qzok_bddjhid")
    @Mapping(target = "fsourcetype", source = "fsourcetype")
    @Mapping(target = "FMaterialId.FNumber", source = "FMaterialId")
    @Mapping(target = "FStockId.FNumber", source = "FStockId")
    KingDeePurchaseBackCommand.FPurmrbEntry toCommand(KingDeePurchaseBackDetailDto dto);

    @Mapping(target = "FLinkId", source = "FLinkId")
    @Mapping(target = "fpurmrbentry_link_fruleid", source = "fpurmrbentry_link_fruleid")
    @Mapping(target = "fpurmrbentry_link_fflowlineid", source = "fpurmrbentry_link_fflowlineid")
    @Mapping(target = "fpurmrbentry_link_fstableid", source = "fpurmrbentry_link_fstableid")
    @Mapping(target = "fpurmrbentry_link_fstablename", source = "fpurmrbentry_link_fstablename")
    @Mapping(target = "fpurmrbentry_link_fsbillid", source = "fpurmrbentry_link_fsbillid")
    @Mapping(target = "fpurmrbentry_link_fsid", source = "fpurmrbentry_link_fsid")
    @Mapping(target = "fpurmrbentry_link_fbasicunitqtyold", source = "fpurmrbentry_link_fbasicunitqtyold")
    @Mapping(target = "fpurmrbentry_link_fbasicunitqty", source = "fpurmrbentry_link_fbasicunitqty")
    @Mapping(target = "fpurmrbentry_link_fcarrybaseqtyold", source = "fpurmrbentry_link_fcarrybaseqtyold")
    @Mapping(target = "fpurmrbentry_link_fcarrybaseqty", source = "fpurmrbentry_link_fcarrybaseqty")
    KingDeePurchaseBackCommand.FPurmrbEntry.FPurmrentryLink toCommand(KingDeePurchaseBackDetailLinkDto dto);

}
