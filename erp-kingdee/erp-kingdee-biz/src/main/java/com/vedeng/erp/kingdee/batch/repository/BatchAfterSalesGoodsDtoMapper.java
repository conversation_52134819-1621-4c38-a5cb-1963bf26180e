package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesGoodsDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BatchAfterSalesGoodsDtoMapper {
    int deleteByPrimaryKey(Integer afterSalesGoodsId);

    int insert(BatchAfterSalesGoodsDto record);

    int insertOrUpdate(BatchAfterSalesGoodsDto record);

    int insertOrUpdateSelective(BatchAfterSalesGoodsDto record);

    int insertSelective(BatchAfterSalesGoodsDto record);

    BatchAfterSalesGoodsDto selectByPrimaryKey(Integer afterSalesGoodsId);

    int updateByPrimaryKeySelective(BatchAfterSalesGoodsDto record);

    int updateByPrimaryKey(BatchAfterSalesGoodsDto record);

    int updateBatch(List<BatchAfterSalesGoodsDto> list);

    int updateBatchSelective(List<BatchAfterSalesGoodsDto> list);

    int batchInsert(@Param("list") List<BatchAfterSalesGoodsDto> list);

    List<BatchAfterSalesGoodsDto> findByAfterSalesId(Integer afterSalesId);

    BatchAfterSalesGoodsDto getSpecialGoods(Integer afterSalesId);

    String getKingDeeFCostID(String sku);

    List<Integer> findByAfterSalesGoodsIdList(@Param("list")List<Integer> afterSalesGoodsIds);


    List<BatchAfterSalesGoodsDto> findByAfterSalesGoodsIds(@Param("list")List<Integer> afterSalesGoodsIds);


    List<BatchAfterSalesGoodsDto> selectByOrderDetailIdsAndTotalAfterNum(@Param("list") List<Integer> orderDetailIds, @Param("type") Integer type, @Param("subjectType") Integer subjectType);




}