package com.vedeng.erp.kingdee.service;

import com.vedeng.erp.kingdee.dto.result.KingDeeOutPutFeeResultDto;
import com.vedeng.erp.kingdee.dto.result.KingDeeSaleInStockQueryResultDto;

import java.util.List;

/**
 * @description: 销售应收单
 * @author: Jez
 * @date: 2022/12/2 15:46
 **/
public interface KingDeeSaleInStockService  {


    /**
     * 根据ERP入库单编号获取金蝶入库单信息
     * @param outInNo 入库单编号
     * @return  List<KingDeeReceiveQueryResultDto>
     */
    List<KingDeeSaleInStockQueryResultDto> getKingDeeSaleInStocks(String outInNo);

    /**
     * 根据erp发票id查询金蝶对应发票Fid
     * @param formId
     * @param invoiceId
     * @return List<KingDeeOutPutFeeResultDto>
     */
    List<KingDeeOutPutFeeResultDto> getKingDeeSaleInvoiceFid(String formId,Integer invoiceId);

}
