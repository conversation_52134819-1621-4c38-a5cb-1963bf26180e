package com.vedeng.doc.domain.entity;


import lombok.Data;

import java.io.Serializable;

/**
 * 资料库生产厂商资料
 * <AUTHOR>
 * @TableName T_DOC_SUPPLIER
 */
@Data
public class DocSupplier implements Serializable {

    private Integer id;

    /**
     * 生产厂商ID
     */
    private Integer supplierId;

    /**
     * 生产厂商名称
     */
    private String supplierName;

    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 创建者
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private Long modTime;

    /**
     * 更新人
     */
    private Integer updater;

    /**
     * 是否禁用
     */
    private Integer disable;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 是否需要维护 0 不需要 1 需要
     */
    private Integer needMaintain;

    private static final long serialVersionUID = 1L;
}