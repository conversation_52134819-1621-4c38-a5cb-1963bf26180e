package com.newtask.ddi.model.generate;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * T_DDI_SALEORDER
 * <AUTHOR>
public class TDdiGeOrder implements Serializable {
    private Integer tDdiSaleorderId;

    /**
     * 经销商
     */
    private String distributor="贝登";

    /**
     * GE合同编号
     */
    private String quoteId;

    /**
     * GE销售订单单号
     */
    private String saleorderNo;

    /**
     * 出库日期
     */
    private Date deliveryTime;

    /**
     * 产品类型
     */
    private String goodsType;

    /**
     * 产品名称
     */
    private String goodsName;

    /**
     * 产品型号
     */
    private String goodsModel;

    /**
     * 注册证号
     */
    private String registrationNumber;

    /**
     * 序列号
     */
    private String serialNumber;

    /**
     * 产品有效期
     */
    private Date effectiveDays;

    /**
     * 客户代码
     */
    private Integer traderId;

    /**
     * 客户名称
     */
    private String traderName;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 单位
     */
    private String unit;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 出货类型
     */
    private String shipmentType;

    /**
     * 客户城市
     */
    private String traderCity;

    /**
     * 客户地址
     */
    private String traderAddress;

    /**
     * 备注
     */
    private String remark;

    /**
     * 出库单号
     */
    private String warehouseOutNo;

    /**
     * T2分销商代码
     */
    private Integer distributorId;

    /**
     * T2分销商名称
     */
    private String distributorName;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 更新时间
     */
    private Date modeTime;

    /**
     * 是否删除 0否 1是
     */
    private Boolean isDelete;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 更新人
     */
    private Integer updater;

    private static final long serialVersionUID = 1L;

    public Integer gettDdiSaleorderId() {
        return tDdiSaleorderId;
    }

    public void settDdiSaleorderId(Integer tDdiSaleorderId) {
        this.tDdiSaleorderId = tDdiSaleorderId;
    }

    public String getDistributor() {
        return distributor;
    }

    public void setDistributor(String distributor) {
        this.distributor = distributor;
    }

    public String getQuoteId() {
        return quoteId;
    }

    public void setQuoteId(String quoteId) {
        this.quoteId = quoteId;
    }

    public String getSaleorderNo() {
        return saleorderNo;
    }

    public void setSaleorderNo(String saleorderNo) {
        this.saleorderNo = saleorderNo;
    }

    public Date getDeliveryTime() {
        return deliveryTime;
    }

    public void setDeliveryTime(Date deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    public String getGoodsType() {
        return goodsType;
    }

    public void setGoodsType(String goodsType) {
        this.goodsType = goodsType;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsModel() {
        return goodsModel;
    }

    public void setGoodsModel(String goodsModel) {
        this.goodsModel = goodsModel;
    }

    public String getRegistrationNumber() {
        return registrationNumber;
    }

    public void setRegistrationNumber(String registrationNumber) {
        this.registrationNumber = registrationNumber;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public Date getEffectiveDays() {
        return effectiveDays;
    }

    public void setEffectiveDays(Date effectiveDays) {
        this.effectiveDays = effectiveDays;
    }

    public Integer getTraderId() {
        return traderId;
    }

    public void setTraderId(Integer traderId) {
        this.traderId = traderId;
    }

    public String getTraderName() {
        return traderName;
    }

    public void setTraderName(String traderName) {
        this.traderName = traderName;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getShipmentType() {
        return shipmentType;
    }

    public void setShipmentType(String shipmentType) {
        this.shipmentType = shipmentType;
    }

    public String getTraderCity() {
        return traderCity;
    }

    public void setTraderCity(String traderCity) {
        this.traderCity = traderCity;
    }

    public String getTraderAddress() {
        return traderAddress;
    }

    public void setTraderAddress(String traderAddress) {
        this.traderAddress = traderAddress;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getWarehouseOutNo() {
        return warehouseOutNo;
    }

    public void setWarehouseOutNo(String warehouseOutNo) {
        this.warehouseOutNo = warehouseOutNo;
    }

    public Integer getDistributorId() {
        return distributorId;
    }

    public void setDistributorId(Integer distributorId) {
        this.distributorId = distributorId;
    }

    public String getDistributorName() {
        return distributorName;
    }

    public void setDistributorName(String distributorName) {
        this.distributorName = distributorName;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Date getModeTime() {
        return modeTime;
    }

    public void setModeTime(Date modeTime) {
        this.modeTime = modeTime;
    }

    public Boolean getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Boolean isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Integer getUpdater() {
        return updater;
    }

    public void setUpdater(Integer updater) {
        this.updater = updater;
    }
}