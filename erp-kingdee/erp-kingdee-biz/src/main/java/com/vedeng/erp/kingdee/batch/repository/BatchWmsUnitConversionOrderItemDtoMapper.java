package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchWmsUnitConversionOrderItemDto;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2023/2/22 9:47
 **/
public interface BatchWmsUnitConversionOrderItemDtoMapper {
    /**
     * delete by primary key
     * @param wmsUnitConversionOrderItemId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer wmsUnitConversionOrderItemId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(BatchWmsUnitConversionOrderItemDto record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(BatchWmsUnitConversionOrderItemDto record);

    /**
     * select by primary key
     * @param wmsUnitConversionOrderItemId primary key
     * @return object by primary key
     */
    BatchWmsUnitConversionOrderItemDto selectByPrimaryKey(Integer wmsUnitConversionOrderItemId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(BatchWmsUnitConversionOrderItemDto record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(BatchWmsUnitConversionOrderItemDto record);

    int batchInsert(@Param("list") List<BatchWmsUnitConversionOrderItemDto> list);

    /**
     * 根据转换单单号查询商品明细
     * @param wmsUnitConversionOrderNo 订单编号
     * @return 明细数据
     */
    List<BatchWmsUnitConversionOrderItemDto> selectByWmsUnitConversionOrderNo(String wmsUnitConversionOrderNo);
}