<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <link rel="stylesheet"
          href="${pageContext.request.contextPath}/static/new/css/common/global.css">
    <link rel="stylesheet"
          href="${pageContext.request.contextPath}/static/new/css/pages/firstengage/first/add.css?rnd=${resourceVersionKey}">
</head>

<body>
<form action="" id="form_submit"
      class="J-form" method="POST">
    <input type="hidden" name="manufacturerId" value="${manufacturer.getManufacturerId() }">
    <input type="hidden" name="formToken" value="${formToken}"/>
    <div class="form-wrap">
        <div class="form-container base-form form-span-8">

                <div class="form-title">上传加盖贝登公章企业资质</div>

            <!-- 后台报错的区域 -->
            <c:forEach var="error" items="${manufacturer.errors}" varStatus="status">
                <div class="vd-tip tip-red">
                    <i class="vd-tip-icon vd-icon icon-error2"></i>
                    <div class="vd-tip-cnt">${error}</div>
                </div>
            </c:forEach>

            <div style="clear: both"></div>
            <div class="form-block">
                <div class="form-cnt">



                    <div class="form-item one yz">
                        <div class="form-label"><span class="c-title" data-a="营业执照（贝）："
                                                      >营业执照（贝）：</span></div>
                        <div class="form-fields">
                            <div class="J-upload"></div>
                            <input type="hidden" class="J-upload-data" value='${yzBMapList}'>
                            <%--<div class="form-fields-tip">-最多上传5张。</div>--%>
                            <div class="feedback-block" wrapfor="upload1"></div>
                            <div class="feedback-block J-upload-error">
                                <label class="error" for="" style="display: none;"></label>
                            </div>
                        </div>
                    </div>
                    <div class="form-item one pl">
                        <div class="form-label">
                            <span class="c-title"
                                  data-a="生产企业生产许可证（贝）："
                                  >生产企业生产许可证（贝）：</span>
                        </div>
                        <div class="form-fields">
                            <div class="J-upload"></div>
                            <input type="hidden" class="J-upload-data" value='${scBMapList}'>
                            <div class="feedback-block" wrapfor="upload2"></div>
                            <div class="feedback-block J-upload-error">
                                <label class="error" for="" style="display: none;"></label>
                            </div>
                        </div>
                    </div>

                    <div class="form-item one pl">
                        <div class="form-label">
                            <span class="c-title"
                                  data-a="第一类医疗器械生产备案凭证（贝）："
                            >第一类医疗器械生产备案凭证（贝）：</span>
                        </div>
                        <div class="form-fields">
                            <div class="J-upload"></div>
                            <input type="hidden" class="J-upload-data" value='${rcBMapList}'>
                            <div class="feedback-block" wrapfor="upload2"></div>
                            <div class="feedback-block J-upload-error">
                                <label class="error" for="" style="display: none;"></label>
                            </div>
                        </div>
                    </div>




                    <div class="form-item one cl ">
                        <div class="form-label"><span class="c-title" data-a="生产企业生产产品登记表（贝）："
                                                      >生产企业生产产品登记表（贝）：</span>
                        </div>
                        <div class="form-fields">
                            <div class="J-upload"></div>
                            <input type="hidden" class="J-upload-data" value='${djbBMapList}'>
                            <div class="feedback-block" wrapfor="upload3"></div>
                            <div class="feedback-block J-upload-error">
                                <label class="error" for="" style="display: none;"></label>
                            </div>
                        </div>
                    </div>


                </div>
                <div class="form-btn" style="margin-top: 30px">
                    <div class="form-item">
                        <div class="form-fields">
                            <button class="btn btn-blue btn-large" type="button" onclick="subForm()">保存</button>

                            <a href="javascript:void(0)" onclick="cancel()"
                               class="btn btn-large">取消</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>



<script src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/pikaday.2.1.0.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/util.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/select.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/jquery.validate.js"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/upload.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/lv-select.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/inputSuggest.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/artDialog/2.0.0/artDialog.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/dialogSearch.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/suggestSelect.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src='${pageContext.request.contextPath}/static/libs/jquery/plugins/layer/layer.js'></script>
<script src="${pageContext.request.contextPath}/static/new/js/pages/manufacturer/product/new_addOfficialB.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/pages/goods/commons/goodsStorageCondition.js?rnd=${resourceVersionKey}"></script>
</body>