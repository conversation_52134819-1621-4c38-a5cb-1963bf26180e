<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.aftersales.dao.AfterSaleServiceLabelMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.aftersales.component.dto.AfterSaleServiceLabelDto">
    <id column="AFTER_SALE_SERVICE_LABEL_ID" jdbcType="INTEGER" property="afterSaleServiceLabelId" />
    <result column="LABEL_NAME" jdbcType="VARCHAR" property="labelName" />
    <result column="LABEL_DESCRIPTION" jdbcType="VARCHAR" property="labelDescription" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATE_TIME" jdbcType="BIGINT" property="updateTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="IS_DELETED" jdbcType="TINYINT" property="isDeleted" />
    <result column="LABEL_CODE" jdbcType="VARCHAR" property="labelCode" />
  </resultMap>
  <sql id="Base_Column_List">
    AFTER_SALE_SERVICE_LABEL_ID, LABEL_NAME, LABEL_DESCRIPTION, ADD_TIME, CREATOR, UPDATE_TIME, 
    UPDATER, IS_DELETED,LABEL_CODE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_AFTER_SALE_SERVICE_LABEL
    where AFTER_SALE_SERVICE_LABEL_ID = #{afterSaleServiceLabelId,jdbcType=INTEGER}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_AFTER_SALE_SERVICE_LABEL
    where AFTER_SALE_SERVICE_LABEL_ID = #{afterSaleServiceLabelId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.aftersales.component.dto.AfterSaleServiceLabelDto">
    insert into T_AFTER_SALE_SERVICE_LABEL (AFTER_SALE_SERVICE_LABEL_ID, LABEL_NAME, 
      LABEL_DESCRIPTION, ADD_TIME, CREATOR, 
      UPDATE_TIME, UPDATER, IS_DELETED
      )
    values (#{afterSaleServiceLabelId,jdbcType=INTEGER}, #{labelName,jdbcType=VARCHAR}, 
      #{labelDescription,jdbcType=VARCHAR}, #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, 
      #{updateTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}, #{isDeleted,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.aftersales.component.dto.AfterSaleServiceLabelDto">
    insert into T_AFTER_SALE_SERVICE_LABEL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="afterSaleServiceLabelId != null">
        AFTER_SALE_SERVICE_LABEL_ID,
      </if>
      <if test="labelName != null">
        LABEL_NAME,
      </if>
      <if test="labelCode != null">
        LABEL_CODE,
      </if>
      <if test="labelDescription != null">
        LABEL_DESCRIPTION,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="isDeleted != null">
        IS_DELETED,
      </if>

    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="afterSaleServiceLabelId != null">
        #{afterSaleServiceLabelId,jdbcType=INTEGER},
      </if>
      <if test="labelName != null">
        #{labelName,jdbcType=VARCHAR},
      </if>
      <if test="labelCode != null">
        #{labelCode,jdbcType=VARCHAR},
      </if>
      <if test="labelDescription != null">
        #{labelDescription,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.aftersales.component.dto.AfterSaleServiceLabelDto">
    update T_AFTER_SALE_SERVICE_LABEL
    <set>
      <if test="labelName != null">
        LABEL_NAME = #{labelName,jdbcType=VARCHAR},
      </if>
      <if test="labelCode != null">
        LABEL_CODE = #{labelCode,jdbcType=VARCHAR},
      </if>
      <if test="labelDescription != null">
        LABEL_DESCRIPTION = #{labelDescription,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        IS_DELETED = #{isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    where AFTER_SALE_SERVICE_LABEL_ID = #{afterSaleServiceLabelId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.aftersales.component.dto.AfterSaleServiceLabelDto">
    update T_AFTER_SALE_SERVICE_LABEL
    set LABEL_NAME = #{labelName,jdbcType=VARCHAR},
      LABEL_DESCRIPTION = #{labelDescription,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      IS_DELETED = #{isDeleted,jdbcType=TINYINT},
      LABEL_CODE = #{labelCode,jdbcType=VARCHAR}
    where AFTER_SALE_SERVICE_LABEL_ID = #{afterSaleServiceLabelId,jdbcType=INTEGER}
  </update>
  <select id="getAllLabels" resultType="com.vedeng.aftersales.component.dto.AfterSaleServiceLabelDto">
    select * from T_AFTER_SALE_SERVICE_LABEL WHERE IS_DELETED = 0 OR IS_DELETED IS NULL ;
  </select>
</mapper>