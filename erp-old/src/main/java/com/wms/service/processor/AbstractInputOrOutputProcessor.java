package com.wms.service.processor;


/**
 * 抽象的出库和入库处理器
 */
public abstract class AbstractInputOrOutputProcessor<T> extends AbstractWMSCalllBackProcessor<T> {

    @Override
    protected final void doDealWithRequest(T requestBean) throws Exception{

        //更新相关单据的数据
        updateOrderData(requestBean);

        //新增或修改 V_WMS_LOGICAL_ORDER_GOODS
        insertOrUpdateLogicalOrderGoods(requestBean);

        //新增或修改 出入库日志
        insertOrUpdateWarehouseLog(requestBean);

        //同步库存服务
        synchronizeStockData(requestBean);

        //自定义的一些处理 标准步骤以外的一些处理
        customHandle(requestBean);

    }

    protected abstract void updateOrderData(T requestBean) throws Exception;

    protected abstract void insertOrUpdateLogicalOrderGoods(T requestBean) throws Exception;

    protected abstract void insertOrUpdateWarehouseLog(T requestBean) throws Exception;

    protected abstract void synchronizeStockData(T requestBean) throws Exception;

    /**
     * 提供空实现，需要的可以覆盖
     * @param requestBean
     */
    protected void customHandle(T requestBean) throws Exception {}

}
