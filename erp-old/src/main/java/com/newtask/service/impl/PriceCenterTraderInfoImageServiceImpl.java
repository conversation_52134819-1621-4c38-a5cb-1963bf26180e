package com.newtask.service.impl;

import com.newtask.model.SkuInfoImageDto;
import com.newtask.model.TraderInfoImageDto;
import com.newtask.service.PriceCenterTraderInfoImageService;
import com.vedeng.goods.dao.PriceCenterTraderInfoImageMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PriceCenterTraderInfoImageServiceImpl implements PriceCenterTraderInfoImageService {
    @Autowired
    private PriceCenterTraderInfoImageMapper priceCenterTraderInfoImageMapper;

    @Override
    public List<TraderInfoImageDto> getTraderInfoImage() {
        return priceCenterTraderInfoImageMapper.getTraderInfoImage();
    }

    @Override
    public List<TraderInfoImageDto> getTraderInfoImageAll() {
        return priceCenterTraderInfoImageMapper.getTraderInfoImageAll();
    }
}
