package com.vedeng.erp.kingdee.batch.writer;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.batch.dto.BatchKingDeePurchaseReceiptDto;
import com.vedeng.erp.kingdee.domain.entity.KingDeeSaleReturnStockEntity;
import com.vedeng.erp.kingdee.dto.KingDeeSaleReturnStockDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeSaleReturnStockDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeSaleBackCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeSaleBackConvertor;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeSaleReturnStockMapper;
import com.vedeng.erp.kingdee.service.KingDeeSaleReturnStockApiService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 销售退货入库
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchSaleReturnStockWriter extends BaseWriter<KingDeeSaleReturnStockDto> {


    @Autowired
    private KingDeeSaleReturnStockApiService kingDeeSaleReturnStockApiService;

    @Override
    public void doWrite(KingDeeSaleReturnStockDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {
        dto.setKingDeeBizEnums(KingDeeBizEnums.saveSaleReturn);
        kingDeeSaleReturnStockApiService.register(dto,true);
        BatchKingDeePurchaseReceiptDto build = BatchKingDeePurchaseReceiptDto.builder()
                .outInNo(dto.getFBillNo())
                .warehouseGoodsOutInId(Long.valueOf(dto.getFQzokBddjtid()))
                .fId(dto.getFid())
                .build();
        List<BatchKingDeePurchaseReceiptDto> purchaseInData = (List<BatchKingDeePurchaseReceiptDto>) getStepParameter("purchaseInData");
        List<BatchKingDeePurchaseReceiptDto> batchKingDeePurchaseReceiptDtos = new ArrayList<>();
        if (CollUtil.isEmpty(purchaseInData)) {
            batchKingDeePurchaseReceiptDtos.add(build);
            saveStepParameter("purchaseInData", batchKingDeePurchaseReceiptDtos);
            saveStepParameter("formId", dto.getFormId());
        } else {
            purchaseInData.add(build);
        }
    }
}

