package com.vedeng.erp.kingdee.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeeMaterialCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeMaterialEntity;
import com.vedeng.erp.kingdee.dto.KingDeeMaterialDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeMaterialCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeMaterialConvertor;
import com.vedeng.erp.kingdee.repository.KingDeeMaterialRepository;
import com.vedeng.erp.kingdee.service.KingDeeMaterialApiService;
import com.vedeng.erp.kingdee.service.KingDeeMaterialService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryExtParam;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryFilterDto;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/24 13:23
 */
@Service
@Slf4j
public class KingDeeMaterialServiceImpl extends KingDeeBaseServiceImpl<
        KingDeeMaterialEntity,
        KingDeeMaterialDto,
        KingDeeMaterialCommand,
        KingDeeMaterialRepository,
        KingDeeMaterialConvertor,
        KingDeeMaterialCommandConvertor> implements KingDeeMaterialService, KingDeeMaterialApiService {

    @Override
    public void savePre(Object... objects) {
        log.info("保存金蝶物料:{}", JSON.toJSONString(objects));
        KingDeeMaterialDto dto = getD(objects);
        this.getTaxCode(dto);
    }

    @Override
    public void update(KingDeeMaterialDto dto) {
        log.info("更新金蝶物料:{}", JSON.toJSONString(dto));
        Assert.notNull(dto.getFNumber(), "物料编码不能为空");
        List<KingdeeMaterialPayCommonQueryResultDto> kingDeeMaterial = getKingDeeMaterial(dto.getFNumber());

        // 如果物料信息为空，直接返回
        if (CollUtil.isEmpty(kingDeeMaterial)) {
            log.error("物料信息为空:{}", JSON.toJSONString(dto));
            throw new KingDeeException("物料信息为空:{}" + dto.getFNumber());
        }

        this.getTaxCode(dto);

        KingdeeMaterialPayCommonQueryResultDto first = CollUtil.getFirst(kingDeeMaterial);
        String fBaseUnitId = dto.getSubHeadEntity().getFBaseUnitId();
        if (fBaseUnitId == null || first.getFBaseUnitId().getFNumber().equals(fBaseUnitId)) {
            dto.getSubHeadEntity().setFBaseUnitId(null);
            super.update(dto);
        } else {
            if (!super.unAudit(dto)) return;
            super.update(dto);
            super.submitAndAudit(dto);
        }
    }

    private void getTaxCode(KingDeeMaterialDto dto) {
        log.info("金蝶获取税码信息:{}", JSON.toJSONString(dto));
        if (StrUtil.isNotEmpty(dto.getFTaxcategoryCodeId())) {
            List<KingdeeTaxcategoryCodeQueryResultDto> kingdeeTaxcategoryCode = getKingdeeTaxcategoryCode(dto.getFTaxcategoryCodeId());
            if (CollUtil.isEmpty(kingdeeTaxcategoryCode)) {
                throw new KingDeeException("金蝶未找到对应税码信息:" + dto.getFTaxcategoryCodeId());
            }
            KingdeeTaxcategoryCodeQueryResultDto first = CollUtil.getFirst(kingdeeTaxcategoryCode);
            log.info("金蝶税码信息:{}", JSON.toJSONString(first));
            dto.setFTaxcategoryCodeId(first.getFNumber());
        }
    }


    public List<KingdeeMaterialPayCommonQueryResultDto> getKingDeeMaterial(String businessId) {
        KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
        queryParam.setFormId(KingDeeFormConstant.BD_MATERIAL);
        List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
        queryFilterDtos.add(KingDeeQueryFilterDto.builder()
                .fieldName("fNumber")
                .value(businessId)
                .build());
        queryParam.setFilterString(queryFilterDtos);
        return kingDeeBaseApi.queryToObj(queryParam, KingdeeMaterialPayCommonQueryResultDto.class);
    }


    @Data
    private static class KingdeeMaterialPayCommonQueryResultDto {

        private String FMATERIALID;
        private String fNumber;
        private KingDeeNumberCommand FBaseUnitId;

    }


    public List<KingdeeTaxcategoryCodeQueryResultDto> getKingdeeTaxcategoryCode(String taxcategoryCode) {
        KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
        queryParam.setFormId(KingDeeFormConstant.IV_GTTAXCODE);
        java.util.List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
        queryFilterDtos.add(KingDeeQueryFilterDto.builder()
                .fieldName("FTAXCODE")
                .value(taxcategoryCode)
                .build());
        queryParam.setFilterString(queryFilterDtos);
        return kingDeeBaseApi.queryToObj(queryParam, KingdeeTaxcategoryCodeQueryResultDto.class);
    }

    @Data
    static class KingdeeTaxcategoryCodeQueryResultDto {
        private String fNumber;
    }
}

