package com.vedeng.common.trace.service.impl;

import org.apache.commons.lang3.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.common.trace.data.TrackParamsData;
import com.vedeng.common.trace.enums.EventTrackingEnum;
import com.vedeng.common.trace.service.TrackBaseService;
import com.vedeng.common.trace.track.TrackStrategy;
import com.vedeng.common.trace.util.TrackUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * 客户cpm维护
 * @ClassName:  CustomerChangeCpmStrategyImpl     
 * @author: Neil.yang
 * @date:   2024年6月13日 下午6:34:28    
 * @Copyright:
 */
@Component
public class CustomerChangeCpmStrategyImpl  extends TrackBaseService implements TrackStrategy {

	private static final Logger LOGGER = LoggerFactory.getLogger(CustomerChangeCpmStrategyImpl.class);
	
	@Override
	public EventTrackingEnum getTrackStrategy() {
		return EventTrackingEnum.BASE_INFO_CUSTOMER_CHANGE_CPM;
	}

	@Override
	public boolean checkParamsLegal(TrackParamsData trackParamsData) {
		//获取唯一标识符
		Map<String, Object> trackParams = trackParamsData.getTrackParams();
		String primaryKey = trackParamsData.getEventTrackingEnum().getPrimaryKey();
		String primaryValue = TrackUtils.getJsonValue(new JSONObject(trackParams),primaryKey);
		//唯一标识符获取不到，不能埋点
		if(StringUtils.isEmpty(primaryValue)) {
			LOGGER.info("=======埋点档案：{}，必填字段：{}缺失...",trackParamsData.getEventTrackingEnum().getArchivedName(),primaryKey);
			return false;
		}
		if(Objects.isNull(trackParams.get("track_user"))) {
			LOGGER.info("=======埋点档案：{}，必填字段：{}缺失...",trackParamsData.getEventTrackingEnum().getArchivedName(),"track_user");
			return false;
		}
		return true;
	}


}
