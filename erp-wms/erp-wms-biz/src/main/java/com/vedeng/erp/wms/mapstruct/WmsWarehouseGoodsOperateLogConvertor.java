package com.vedeng.erp.wms.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.wms.domain.entity.WmsWarehouseGoodsOperateLogEntity;
import com.vedeng.erp.wms.dto.WarehouseGoodsOperateLogDto;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface WmsWarehouseGoodsOperateLogConvertor extends BaseMapStruct<WmsWarehouseGoodsOperateLogEntity, WarehouseGoodsOperateLogDto> {
}
