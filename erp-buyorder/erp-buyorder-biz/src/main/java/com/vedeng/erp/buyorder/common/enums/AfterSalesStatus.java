package com.vedeng.erp.buyorder.common.enums;

public enum  AfterSalesStatus {


    CONFIRM("待确认"),
    VALID("审核中"),
    PROCRESSING("进行中"),
    COMPLETED("已完结"),
    CLOSED("已关闭"),

    PAY_APPLY_VALID_STATER("开始审核"),
    PAY_APPLY_VALID_APPLYER("申请人"),
    PAY_APPLY_VALID_ORDER("财务制单"),
    PAY_APPLY_VALID_VALID("财务审核"),
    PAY_APPLY_VALID_OVER("审核完成"),
    PAY_APPLY_VALID_REJECT("驳回"),


    AFTER_SALE_VALID_STATER("开始审核"),
    AFTER_SALE_VALID_APPLYER("申请人"),
    AFTER_SALE_VALID_MANAGE("产品主管审核"),
    AFTER_SALE_VALID_OVER("审核完成"),
    AFTER_SALE_VALID_REJECT("驳回"),


    OVER_AFTER_SALE_VALID_STATER("开始审核"),
    OVER_AFTER_SALE_VALID_APPLYER("申请人"),
    OVER_AFTER_SALE_VALID_MANAGE("产品主管"),
    OVER_AFTER_SALE_VALID_OVER("审核完成"),
    OVER_AFTER_SALE_VALID_REJECT("驳回"),
    ;
    //0待确认、1审核中、2进行中、3已完结，4已关闭
    private String status;

    public String getStatus() {
        return this.status;
    }

    AfterSalesStatus(String status) {
        this.status = status;
    }
}
