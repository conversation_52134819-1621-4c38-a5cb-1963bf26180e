<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.homepage.dao.SalesGoalSettingMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.homepage.model.SalesGoalSetting" >
    <id column="SALES_GOAL_SETTING_ID" property="salesGoalSettingId" jdbcType="INTEGER" />
    <result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
    <result column="YEAR" property="year" jdbcType="DATE" />
    <result column="MONTH" property="month" jdbcType="BIT" />
    <result column="GOAL" property="goal" jdbcType="DECIMAL" />
    <result column="ORG_ID" property="orgId" jdbcType="INTEGER" />
    <result column="USER_ID" property="userId" jdbcType="INTEGER" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    SALES_GOAL_SETTING_ID, COMPANY_ID, YEAR, MONTH, GOAL, ORG_ID, USER_ID, ADD_TIME, 
    CREATOR, MOD_TIME, UPDATER
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from T_SALES_GOAL_SETTING
    where SALES_GOAL_SETTING_ID = #{salesGoalSettingId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from T_SALES_GOAL_SETTING
    where SALES_GOAL_SETTING_ID = #{salesGoalSettingId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.homepage.model.SalesGoalSetting" >
    insert into T_SALES_GOAL_SETTING (SALES_GOAL_SETTING_ID, COMPANY_ID, YEAR, 
      MONTH, GOAL, ORG_ID, USER_ID, 
      ADD_TIME, CREATOR, MOD_TIME, 
      UPDATER)
    values (#{salesGoalSettingId,jdbcType=INTEGER}, #{companyId,jdbcType=INTEGER}, #{year,jdbcType=DATE}, 
      #{month,jdbcType=BIT}, #{goal,jdbcType=DECIMAL}, #{orgId,jdbcType=INTEGER}, #{userId,jdbcType=INTEGER}, 
      #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, 
      #{updater,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.homepage.model.SalesGoalSetting" >
    insert into T_SALES_GOAL_SETTING
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="salesGoalSettingId != null" >
        SALES_GOAL_SETTING_ID,
      </if>
      <if test="companyId != null" >
        COMPANY_ID,
      </if>
      YEAR,
      <!-- 
      <if test="year != null" >
        
      </if> -->
      <if test="month != null" >
        MONTH,
      </if>
      <if test="goal != null" >
        GOAL,
      </if>
      <if test="orgId != null" >
        ORG_ID,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="salesGoalSettingId != null" >
        #{salesGoalSettingId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null" >
        #{companyId,jdbcType=INTEGER},
      </if>
      YEAR(NOW()),
      <!-- 
      <if test="year != null" >
        #{year,jdbcType=DATE},
      </if> -->
      <if test="month != null" >
        #{month,jdbcType=BIT},
      </if>
      <if test="goal != null" >
        #{goal,jdbcType=DECIMAL},
      </if>
      <if test="orgId != null" >
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.homepage.model.SalesGoalSetting" >
    update T_SALES_GOAL_SETTING
    <set >
      <if test="companyId != null" >
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="year != null" >
        YEAR = #{year,jdbcType=DATE},
      </if>
      <if test="month != null" >
        MONTH = #{month,jdbcType=BIT},
      </if>
      <if test="goal != null" >
        GOAL = #{goal,jdbcType=DECIMAL},
      </if>
      <if test="orgId != null" >
        ORG_ID = #{orgId,jdbcType=INTEGER},
      </if>
      <if test="userId != null" >
        USER_ID = #{userId,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where SALES_GOAL_SETTING_ID = #{salesGoalSettingId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.homepage.model.SalesGoalSetting" >
    update T_SALES_GOAL_SETTING
    set COMPANY_ID = #{companyId,jdbcType=INTEGER},
      YEAR = #{year,jdbcType=DATE},
      MONTH = #{month,jdbcType=BIT},
      GOAL = #{goal,jdbcType=DECIMAL},
      ORG_ID = #{orgId,jdbcType=INTEGER},
      USER_ID = #{userId,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER}
    where SALES_GOAL_SETTING_ID = #{salesGoalSettingId,jdbcType=INTEGER}
  </update>
  
  <select id="getSalesGoalSettingList" parameterType="com.vedeng.homepage.model.SalesGoalSetting" resultMap="BaseResultMap">
  	select 
    <include refid="Base_Column_List" />
    from T_SALES_GOAL_SETTING
    where 1=1
    <if test="companyId != null" >
        and COMPANY_ID = #{companyId,jdbcType=INTEGER}
    </if>
    <if test="year != null" >
        and YEAR = YEAR(#{year,jdbcType=DATE})
    </if>
    <if test="orgId != null" >
        and ORG_ID = #{orgId,jdbcType=INTEGER}
    </if>
    <if test="userId != null" >
        and USER_ID = #{userId,jdbcType=INTEGER}
    </if>
    order by MONTH asc
  </select>
  
  <select id="getTotalMonthList"  resultType="java.math.BigDecimal" parameterType="Map">
  	select 
    	COALESCE(SUM(GOAL),0)
    from T_SALES_GOAL_SETTING
    where YEAR = YEAR(NOW())
    <if test="companyId != null" >
        and COMPANY_ID = #{companyId,jdbcType=INTEGER}
    </if>
    <if test="ids != null" >
        and ORG_ID in 
        <foreach item="orgId" index="index" collection="ids" open="(" separator="," close=")">  
		  #{orgId}  
		</foreach> 
    </if>
    <if test="orgId != null and ids == null" >
        and ORG_ID = #{orgId,jdbcType=INTEGER}
    </if>
    <if test="userList != null" >
        and USER_ID in 
        <foreach item="user" index="index" collection="userList" open="(" separator="," close=")">  
		  #{user.userId}  
		</foreach> 
    </if>
    group by MONTH
    order by MONTH asc
  </select>
  
  <select id="getSalesEngineerGoal"  resultType="java.math.BigDecimal" parameterType="com.vedeng.homepage.model.SalesGoalSetting">
  	select 
    	COALESCE(SUM(GOAL),0)
    from T_SALES_GOAL_SETTING
    where YEAR = YEAR(NOW()) and MONTH = MONTH(NOW())
    <if test="companyId != null" >
        and COMPANY_ID = #{companyId,jdbcType=INTEGER}
    </if>
    <if test="userId != null" >
        and USER_ID = #{userId,jdbcType=INTEGER}
    </if>
  </select>
  
    <select id="getNextDeptSalesGoalSettingList" parameterType="com.vedeng.homepage.model.vo.SaleEngineerDataVo" resultMap="BaseResultMap">
  	select 
    	<include refid="Base_Column_List" />
    from T_SALES_GOAL_SETTING
    where YEAR = YEAR(NOW()) and MONTH = MONTH(NOW())
    <if test="companyId != null" >
        and COMPANY_ID = #{companyId,jdbcType=INTEGER}
    </if>
    <if test="orgIds != null" >
        and ORG_ID in 
        <foreach item="orgId" index="index" collection="orgIds" open="(" separator="," close=")">  
		  #{orgId}  
		</foreach> 
    </if>
  </select>
</mapper>