<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="维护供应商售后政策" scope="application" />
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src="<%= basePath %>static/js/jquery/ajaxfileupload.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript">

    $(function(){
        debugger;


        if('${spuType}' == '316' || '${spuType}' == '1008'){
            if('${skuGenerate.isInstallable}' == 1){
                var installPolicyInstallTypeValue = '${afterSaleSupplyPolicyDto.installPolicyInstallType}';
                installPolicyInstallTypeChange(installPolicyInstallTypeValue);
            }

            var technicalDirectSupplyMaintainValue = '${afterSaleSupplyPolicyDto.technicalDirectSupplyMaintain}';
            technicalDirectSupplyMaintainChange(technicalDirectSupplyMaintainValue);

            var guaranteePolicyIsGuarantee = '${afterSaleSupplyPolicyDto.guaranteePolicyIsGuarantee}';
            guaranteePolicyIsGuaranteeChange(guaranteePolicyIsGuarantee);
        }

        if('${spuType}' == '316' || '${spuType}' == '1008' || '${spuType}' == '317' || '${spuType}' == '318'){
            var returnPolicySupportReturn = '${afterSaleSupplyPolicyDto.returnPolicySupportReturn}';
            returnPolicySupportReturnChange(returnPolicySupportReturn);
            var exchangePolicySupportChange = '${afterSaleSupplyPolicyDto.exchangePolicySupportChange}';
            exchangePolicySupportChangge(exchangePolicySupportChange);
        }

    })

    function returnPolicyNeedIdentifyChange(checkbox,tr) {
        //免费安装
        if(checkbox.value == 1){
            $("#"+tr).show();
        }else{
            $("#"+tr).hide();
        }
    }

    function supplyPolicyIsExsit(traderId,skuNo){

        var exsit = false;

        $.ajax({
            url:page_url+'/aftersale/serviceStandard/supplyPolicyIsExsit.do',
            data:{"traderId":traderId,"skuNo":skuNo,"excludeSupplyPolicyId":'${afterSaleSupplyPolicyDto.supplyPolicyId}'},
            type:"POST",
            dataType : "json",
            async: false,
            success:function(data)
            {
                if(data.code==1){
                    exsit = true;
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });

        return exsit;
    }

    function serviceProviderTypeChange(serviceProviderType){
        debugger;
        //如果选择的是原厂服务 请求下后台看是否有对应的原厂服务
        if(serviceProviderType == '1'){
            $.ajax({
                async:true,
                url:page_url+'/aftersale/serviceStandard/haveFactoryServicesPolicy.do?excludeSupplyPolicyId=${afterSaleSupplyPolicyDto.supplyPolicyId}',
                data:{"skuNo":'${skuNo}'},
                type:"POST",
                dataType : "json",
                success:function(data){
                    if(data.code==0){

                        index = layer.confirm("该SKU已有原厂服务的售后政策，是否需要覆盖？", {
                            btn : [ '确定', '取消' ]
                            //按钮
                        }, function() {

                            if($("#traderId").val() == ""){
                                layer.alert("供应商名称不能为空");
                                return false;
                            }

                            var exsit = supplyPolicyIsExsit($("#traderId").val(),'${skuNo}');
                            if(exsit){
                                layer.alert("该供应商的售后政策已经存在，请选择其他供应商");
                                return;
                            }

                            $.ajax({
                                url:page_url+'/aftersale/serviceStandard/copyOrginalFactoryToCurrentSuppplyPolicy.do',
                                data:{"supplyPolicyId":'${aftersalesupplypolicydto.supplypolicyid}'},
                                type:"POST",
                                dataType : "json",
                                async: false,
                                success:function(data)
                                {
                                    debugger;
                                    if(data.code==0){
                                        layer.close(index);
                                        window.location.href= page_url + "/aftersale/serviceStandard/toModifySupplyPolicyPage.do?supplyPolicyId=${afterSaleSupplyPolicyDto.supplyPolicyId}";
                                    }else{
                                        layer.alert(data.message);
                                    }
                                },
                                error:function(data){
                                    if(data.status ==1001){
                                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                                    }
                                }
                            });
                        }, function() {

                        });
                    }
                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            })
        }
    }

    function save() {

        if($("input[type='file']").length > 10){
            layer.alert("文件上传的个数不能超过10个");
            return;
        }

        if(!validatorValue()){
            return;
        }

        $("#addForm").submit();
    }

    function validatorValue() {

        if($("#traderId").val() == ""){
            layer.alert("供应商名称不能为空");
            return false;
        }

        var exsit = supplyPolicyIsExsit($("#traderId").val(),'${skuNo}');
        if(exsit){
            layer.alert("该供应商的售后政策已经存在，请选择其他供应商");
            return;
        }

        if($("input[name='serviceProviderType']:checked").val() == undefined){
            layer.alert("请选择服务提供商");
            return false;
        }

        if('${spuType}' == '316' || '${spuType}' == '1008'){

            //如果产品可以安装
            if('${skuGenerate.isInstallable}' == 1){
                var installPolicyInstallTypeValue = $("input[name='installPolicyInstallType']:checked").val();
                if(installPolicyInstallTypeValue == undefined){
                    layer.alert("请选择是否提供上门安装服务");
                    return false;
                }

                if(installPolicyInstallTypeValue == 0 || installPolicyInstallTypeValue == 1){
                    if($("input[name='installPolicyFreeRemoteInstall']:checked").val()== undefined){
                        layer.alert("请选择是否免费远程指导装机");
                        return false;
                    }
                }

            }

            //如果选择的保修
            if($("input[name='guaranteePolicyIsGuarantee']:checked").val() == 1){
                //保修方式必填
                if($("input[name='guaranteePolicyGuaranteeType']:checked").val() == undefined){
                    layer.alert("请选择保修方式");
                    return false;
                }

                if($("#guaranteePolicyHostGuaranteePeriodNum").val() == ""){
                    layer.alert("请输入主机保修期");
                    return false;
                }

                if($("input[name='guaranteePolicyCycleCaltype']:checked").val() == undefined){
                    layer.alert("请选择周期计算方式");
                    return false;
                }
                if($("input[name='guaranteePolicyArea']:checked").val() == undefined){
                    layer.alert("请选择保修区域");
                    return false;
                }

            }
        }

        if('${spuType}' == '316' || '${spuType}' == '1008' || '${spuType}' == '317' || '${spuType}' == '318') {
            //支持退货
            if ($("input[name='returnPolicySupportReturn']:checked").val() == 1) {
                if ($("#returnPolicyCondition").val() == "") {
                    layer.alert("请输入退货条件");
                    return false;
                }
            }

            //支持换货
            if ($("input[name='exchangePolicySupportChange']:checked").val() == 1) {
                if ($("#exchangePolicyExchangeContition").val() == "") {
                    layer.alert("请输入换货条件");
                    return false;
                }
            }
        }
        return true;
    }

    function installPolicyInstallTypeChange(installTypeValue) {

        var trs = $("tr[class='installPolicy']");
        for(i = 0; i < trs.length; i++){

            if(installTypeValue == 2){
                $(trs[i]).hide();
            }else{
                $(trs[i]).show();
            }
        }
    }

    function technicalDirectSupplyMaintainChange(technicalDirectValue){
        var trs = $("tr[class='technicalDirect']");
        for(i = 0; i < trs.length; i++){
            if(technicalDirectValue == 0){
                $(trs[i]).hide();
            }else{
                $(trs[i]).show();
            }
        }
    }

    function guaranteePolicyIsGuaranteeChange(guaranteePolicyIsGuaranteeValue) {
        var trs = $("tr[class='guaranteePolicy']");
        for(i = 0; i < trs.length; i++){
            if(guaranteePolicyIsGuaranteeValue == 0){
                $(trs[i]).hide();
            }else{
                $(trs[i]).show();
            }
        }
    }

    function returnPolicySupportReturnChange(returnPolicySupportReturnValue) {
        var trs = $("tr[class='returnPolicy']");
        for(i = 0; i < trs.length; i++){
            if(returnPolicySupportReturnValue == 0){
                $(trs[i]).hide();
            }else{
                $(trs[i]).show();
            }
        }
    }

    function exchangePolicySupportChangge(exchangePolicySupportReturnValue) {
        var trs = $("tr[class='exchangePolicy']");
        for(i = 0; i < trs.length; i++){
            if(exchangePolicySupportReturnValue == 0){
                $(trs[i]).hide();
            }else{
                $(trs[i]).show();
            }
        }
    }

    /**
     * 校验数字框输入合法性
     */
    function checkValue(obj) {
        if ($(obj).val() != undefined && $(obj).val() != '') {
            var reg = /^\d+(?=\.{0,1}\d+$|$)/;
            if (!reg.test($(obj).val())) {
                layer.alert('请输入数字!');
                $(obj).val('');
            }
        }
    }

    function toServiceContract(span){

        if($("#traderId").val() == ""){
            layer.alert("请先选择供应商");
            return;
        }

        var tabTitleObj = JSON.parse($(span).attr("tabTitle"));

        tabTitleObj.link = "${pageContext.request.contextPath}/trader/supplier/baseinfo.do?traderId=" + $("#traderId").val();

        $(span).attr("tabTitle",JSON.stringify(tabTitleObj));

        openTab(span);
    }

    function setModelInfo(modelstring,model) {
        $(":input[ name = '"+modelstring+"' ]").val(model);
    }
    function setModelTime(numstring,unitstring,num,unit) {
        $(":input[ name = '"+numstring+"' ]").val(num);
        $("select[ name = '"+unitstring+"' ]").val(unit).attr("selected","selected");
    }
</script>
<div class="content">
    <div class="formtitle"></div>
    <div  id="desc_div">
        <form method="post" id="addForm" action="${pageContext.request.contextPath}/aftersale/serviceStandard/modifySupplyPolicy.do">
            <input type="hidden" name="skuNo" value="${afterSaleSupplyPolicyDto.skuNo}"/>
            <ul class="payplan">
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">基础售后内容</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <input type="hidden" name="supplyPolicyId" value="${afterSaleSupplyPolicyDto.supplyPolicyId}">
                            <tbody>
                                <tr>
                                    <td>类型</td>
                                    <td>供应商售后政策</td>
                                </tr>
                                <tr>
                                    <td>订货号/商品名称</td>
                                    <td>${afterSaleSupplyPolicyDto.skuNo}/${afterSaleSupplyPolicyDto.showName}</td>
                                </tr>
                                <tr>
                                    <td><font color="red">*</font>供应商名称</td>
                                    <td align="center">
                                        <div class="f_center ">
                                            <div class="form-blanks">
                                                <span class="none" id="name"></span>
                                                <input type="text" placeholder="请输入供应商名称" class="input-middle" name="searchTraderName" id="searchTraderName" value="${afterSaleSupplyPolicyDto.traderName}">
                                                <label class="bt-bg-style bg-light-blue bt-small" onclick="searchSupplier();" id="errorMes" style='margin-top:-3px;'>搜索</label>
                                                <label class="bt-bg-style bg-light-blue bt-small none" onclick="research();" id="research" style='margin-top:-3px;'>重新搜索</label>
                                                <span style="display:none;">
                                                    <!-- 弹框 -->
                                                    <div class="title-click nobor  pop-new-data" id="popSupplier"></div>
                                                </span>
                                                <input type="hidden" id="traderId" name="traderId" value="${afterSaleSupplyPolicyDto.traderId}"/>
                                                <input type="hidden" id="traderName" name="traderName" value="${afterSaleSupplyPolicyDto.traderName}"/>
                                            </div>
                                            <div class="font-red none"></div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><font color="red">*</font>售后服务商</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                <input type="radio" value="1" name="serviceProviderType" <c:if test="${afterSaleSupplyPolicyDto.serviceProviderType == 1}">checked</c:if>  onchange="serviceProviderTypeChange(this.value)"> 原厂服务
                                                <input type="radio" value="2" name="serviceProviderType" <c:if test="${afterSaleSupplyPolicyDto.serviceProviderType == 2}">checked</c:if>  onchange="serviceProviderTypeChange(this.value)"> 该供应商提供服务
                                                <i class="iconbluesigh ml4 contorlIcon"></i>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                1.原厂服务：指由原厂家提供售后，且售后政策与原厂家提供的售后政策一致；<br>
                                                2.该供应商提供服务：指由供应商自己安排提供售后服务，且供应商的售后政策可能与厂家售后政策有区别。
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </li>
                <c:if test="${spuType == '316' or spuType == '1008'}">
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">安装政策</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <tbody>
                            <tr>
                                <td>产品是否可安装</td>
                                <td>
                                    <div class="customername pos_rel">
                                            <span>
                                                <c:if test="${skuGenerate.isInstallable == 1}">可安装</c:if>
                                                <c:if test="${skuGenerate.isInstallable == 0}">不可安装</c:if>
                                                <i class="iconbluesigh ml4 contorlIcon"></i>
                                            </span>
                                        <div class="pos_abs customernameshow">
                                            该字段表示商品本身是否具体安装特性，如口罩为“不可安装”,（若信息有误，请联系采购人员到SKU详情页修改。）
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <c:if test="${skuGenerate.isInstallable == 1}">
                                <tr>
                                    <td><font color="red">*</font>是否提供上门安装服务</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                <input type="radio" value="0" name="installPolicyInstallType" <c:if test="${afterSaleSupplyPolicyDto.installPolicyInstallType == '0'}">checked</c:if> onchange="installPolicyInstallTypeChange(this.value)"> 收费安装
                                                <input type="radio" value="1" name="installPolicyInstallType" <c:if test="${afterSaleSupplyPolicyDto.installPolicyInstallType == '1'}">checked</c:if> onchange="installPolicyInstallTypeChange(this.value)"> 免费安装
                                                <input type="radio" value="2" name="installPolicyInstallType" <c:if test="${afterSaleSupplyPolicyDto.installPolicyInstallType == '2'}">checked</c:if> onchange="installPolicyInstallTypeChange(this.value)"> 不提供安装
                                                <i class="iconbluesigh ml4 contorlIcon"></i>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                该字段表示商品本身是否具体安装特性，如口罩为“不可安装”,（若信息有误，请联系采购人员到SKU详情页修改。）
                                            </div>
                                        </div>
                                    </td>
                                </tr>

                                <tr class="installPolicy">
                                    <td>安装区域</td>
                                    <td>
                                        <input type="text" name="installPolicyInstallArea" class="input-middle" placeholder="比如：偏远地区除外、仅江苏等" value="${afterSaleSupplyPolicyDto.installPolicyInstallArea}"/>
                                    </td>
                                </tr>
                                <%--<tr>
                                    <td>安装费</td>
                                    <td>
                                        <input type="text" name="installPolicyInstallFee" class="input-middle" value="${afterSaleSupplyPolicyDto.installPolicyInstallFee}"/>
                                    </td>
                                </tr>--%>
                                <tr class="installPolicy">
                                    <td>是否需要装机资质</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                <input type="radio" value="1" name="installPolicyHaveInstallationQualification" <c:if test="${afterSaleSupplyPolicyDto.installPolicyHaveInstallationQualification == '1'}">checked</c:if>> 是
                                                <input type="radio" value="0" name="installPolicyHaveInstallationQualification" <c:if test="${afterSaleSupplyPolicyDto.installPolicyHaveInstallationQualification == '0'}">checked</c:if>> 否
                                                <i class="iconbluesigh ml4 contorlIcon"></i>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                是否必须为供应商或厂家认证过的工程师才能装机
                                            </div>
                                        </div>
                                    </td>
                                </tr>

                                <tr class="installPolicy">
                                    <td><font color="red">*</font>是否免费远程指导装机</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                <input type="radio" value="1" name="installPolicyFreeRemoteInstall" <c:if test="${afterSaleSupplyPolicyDto.installPolicyFreeRemoteInstall == '1'}">checked</c:if>> 是
                                                <input type="radio" value="0" name="installPolicyFreeRemoteInstall" <c:if test="${afterSaleSupplyPolicyDto.installPolicyFreeRemoteInstall == '0'}">checked</c:if>> 否
                                                <i class="iconbluesigh ml4 contorlIcon"></i>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                可提供对产品的远程装机指导，包含电话指导，视频指导，视频资料提供等
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="installPolicy">
                                    <td>响应时效</td>
                                    <td>
                                        <div class="customername pos_rel">
                                        <span>
                                            <input type="text" name="installPolicyResponseTimeNum" class="input-middle" placeholder="请输入" value="${fn:substringBefore(afterSaleSupplyPolicyDto.installPolicyResponseTime, ',') eq 'null'?'':fn:substringBefore(afterSaleSupplyPolicyDto.installPolicyResponseTime, ',')}" onblur="checkValue(this)"/>
                                            <select style="width: 100px;" name="installPolicyResponseTimeUnit">
                                                <option value="小时" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.installPolicyResponseTime, ',') == '小时'}">selected</c:if>>小时</option>
                                                <option value="天" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.installPolicyResponseTime, ',') == '天'}">selected</c:if>>天</option>
                                                <option value="月" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.installPolicyResponseTime, ',') == '月'}">selected</c:if>>月</option>
                                                <option value="年" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.installPolicyResponseTime, ',') == '年'}">selected</c:if>>年</option>
                                            </select>
                                            <i class="iconbluesigh ml4 contorlIcon"></i>
                                             <span class="bt-small bt-bg-style bg-light-blue"
                                                   onclick="setModelTime('installPolicyResponseTimeNum','installPolicyResponseTimeUnit',2,'小时');"
                                                   style="margin-left: 10px;margin-top: 5px">2小时</span>
                                        </span>
                                            <div class="pos_abs customernameshow">
                                                XX时间内与客户联系
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="installPolicy">
                                    <td>上门时效</td>
                                    <td>
                                        <div class="customername pos_rel">
                                        <span>
                                            <input type="text" name="installPolicyVisitTimeNum" class="input-middle" placeholder="请输入" value="${fn:substringBefore(afterSaleSupplyPolicyDto.installPolicyVisitTime, ',')eq 'null'?'':fn:substringBefore(afterSaleSupplyPolicyDto.installPolicyVisitTime, ',')}" onblur="checkValue(this)"/>
                                            <select style="width: 100px;" name="installPolicyVisitTimeUnit">
                                                <option value="小时" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.installPolicyVisitTime, ',') == '小时'}">selected</c:if>>小时</option>
                                                <option value="天" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.installPolicyVisitTime, ',') == '天'}">selected</c:if>>天</option>
                                                <option value="月" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.installPolicyVisitTime, ',') == '月'}">selected</c:if>>月</option>
                                                <option value="年" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.installPolicyVisitTime, ',') == '年'}">selected</c:if>>年</option>
                                            </select>
                                            <i class="iconbluesigh ml4 contorlIcon"></i>
                                            <span class="bt-small bt-bg-style bg-light-blue"
                                                  onclick="setModelTime('installPolicyVisitTimeNum','installPolicyVisitTimeUnit',72,'小时');"
                                                  style="margin-left: 10px;margin-top: 5px">72小时</span>
                                        </span>
                                            <div class="pos_abs customernameshow">
                                                在客户需求上门的XX时间内上门服务。
                                                一旦与客户约定具体上门时间，则需要在约定的时间上门
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="installPolicy">
                                    <td>安装时效</td>
                                    <td>
                                        <div class="customername pos_rel">
                                        <span>
                                            <input type="text" name="installPolicyInstallTimeNum" class="input-middle" placeholder="请输入" value="${fn:substringBefore(afterSaleSupplyPolicyDto.installPolicyInstallTime, ',')eq 'null'?'':fn:substringBefore(afterSaleSupplyPolicyDto.installPolicyInstallTime, ',')}" onblur="checkValue(this)"/>
                                            <select style="width: 100px;" name="installPolicyInstallTimeUnit">
                                                <option value="小时" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.installPolicyInstallTime, ',') == '小时'}">selected</c:if>>小时</option>
                                                <option value="天" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.installPolicyInstallTime, ',') == '天'}">selected</c:if>>天</option>
                                                <option value="月" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.installPolicyInstallTime, ',') == '月'}">selected</c:if>>月</option>
                                                <option value="年" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.installPolicyInstallTime, ',') == '年'}">selected</c:if>>年</option>
                                            </select>
                                            <i class="iconbluesigh ml4 contorlIcon"></i>
                                            <span class="bt-small bt-bg-style bg-light-blue"
                                                  onclick="setModelTime('installPolicyInstallTimeNum','installPolicyInstallTimeUnit',72,'小时');"
                                                  style="margin-left: 10px;margin-top: 5px">72小时</span>
                                        </span>
                                            <div class="pos_abs customernameshow">
                                                通常是当天（24）小时内装机完毕，某些产品装机时间可能超24小时，如大型设备
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </c:if>
                            </tbody>
                        </table>
                    </div>
                </li>
                </c:if>
                <c:if test="${spuType == '316' or spuType == '1008'}">
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">技术指导</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <tbody>
                            <tr>
                                <td><font color="red">*</font>是否提供技术维修指导</td>
                                <td>
                                    <input type="radio" value="1" name="technicalDirectSupplyMaintain" onchange="technicalDirectSupplyMaintainChange(1)" <c:if test="${afterSaleSupplyPolicyDto.technicalDirectSupplyMaintain == '1'}">checked</c:if>> 提供
                                    <input type="radio" value="0" name="technicalDirectSupplyMaintain" onchange="technicalDirectSupplyMaintainChange(0)" <c:if test="${afterSaleSupplyPolicyDto.technicalDirectSupplyMaintain == '0'}">checked</c:if>> 不提供
                                </td>
                            </tr>
                            <tr class="technicalDirect">
                                <td>响应时效</td>
                                <td>
                                    <div class="customername pos_rel">
                                        <span>
                                            <input type="text" name="technicalDirectResponseTimeNum" class="input-middle" placeholder="请输入" value="${fn:substringBefore(afterSaleSupplyPolicyDto.technicalDirectResponseTime, ',')eq 'null'?'':fn:substringBefore(afterSaleSupplyPolicyDto.technicalDirectResponseTime, ',')}" onblur="checkValue(this)"/>
                                            <select style="width: 100px;" name="technicalDirectResponseTimeUnit">
                                                <option value="小时" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.technicalDirectResponseTime, ',') == '小时'}">selected</c:if>>小时</option>
                                                <option value="天" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.technicalDirectResponseTime, ',') == '天'}">selected</c:if>>天</option>
                                                <option value="月" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.technicalDirectResponseTime, ',') == '月'}">selected</c:if>>月</option>
                                                <option value="年" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.technicalDirectResponseTime, ',') == '年'}">selected</c:if>>年</option>
                                            </select>
                                            <i class="iconbluesigh ml4 contorlIcon"></i>
                                            <span class="bt-small bt-bg-style bg-light-blue"
                                                  onclick="setModelTime('technicalDirectResponseTimeNum','technicalDirectResponseTimeUnit',2,'小时');"
                                                  style="margin-left: 10px;margin-top: 5px">2小时</span>
                                        </span>
                                        <div class="pos_abs customernameshow">
                                            XX时间内与客户联系
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="technicalDirect">
                                <td>技术指导时效</td>
                                <td>
                                    <div class="customername pos_rel">
                                        <span>
                                            <input type="text" name="technicalDirectEffectTimeNum" class="input-middle" placeholder="请输入" value="${fn:substringBefore(afterSaleSupplyPolicyDto.technicalDirectEffectTime, ',')eq 'null'?'':fn:substringBefore(afterSaleSupplyPolicyDto.technicalDirectEffectTime, ',')}" onblur="checkValue(this)"/>
                                            <select style="width: 100px;" name="technicalDirectEffectTimeUnit">
                                                <option value="小时" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.technicalDirectEffectTime, ',') == '小时'}">selected</c:if>>小时</option>
                                                <option value="天" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.technicalDirectEffectTime, ',') == '天'}">selected</c:if>>天</option>
                                                <option value="月" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.technicalDirectEffectTime, ',') == '月'}">selected</c:if>>月</option>
                                                <option value="年" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.technicalDirectEffectTime, ',') == '年'}">selected</c:if>>年</option>
                                            </select>
                                            <i class="iconbluesigh ml4 contorlIcon"></i>
                                            <span class="bt-small bt-bg-style bg-light-blue"
                                                  onclick="setModelTime('technicalDirectEffectTimeNum','technicalDirectEffectTimeUnit',24,'小时');"
                                                  style="margin-left: 10px;margin-top: 5px">24小时</span>
                                        </span>
                                        <div class="pos_abs customernameshow">
                                            技术指导的时间需要在客户需求的XX时间内
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </li>
                </c:if>

                <c:if test="${spuType == '316' or spuType == '1008'}">
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">保修政策</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <tbody>
                                <tr>
                                    <td><font color="red">*</font>是否保修</td>
                                    <td>
                                        <input type="radio" value="1" name="guaranteePolicyIsGuarantee" onchange="guaranteePolicyIsGuaranteeChange(this.value)" <c:if test="${afterSaleSupplyPolicyDto.guaranteePolicyIsGuarantee == '1'}">checked</c:if>> 是
                                        <input type="radio" value="0" name="guaranteePolicyIsGuarantee" onchange="guaranteePolicyIsGuaranteeChange(this.value)" <c:if test="${afterSaleSupplyPolicyDto.guaranteePolicyIsGuarantee == '0'}">checked</c:if>> 否
                                    </td>
                                </tr>
                                <tr class="guaranteePolicy">
                                    <td><font color="red">*</font>保修方式</td>
                                    <td>
                                        <input type="checkbox" value="1" name="guaranteePolicyGuaranteeType" <c:if test="${fn:contains(afterSaleSupplyPolicyDto.guaranteePolicyGuaranteeType,'1')}">checked</c:if>> 上门维修
                                        <input type="checkbox" value="2" name="guaranteePolicyGuaranteeType" <c:if test="${fn:contains(afterSaleSupplyPolicyDto.guaranteePolicyGuaranteeType,'2')}">checked</c:if>> 寄送修
                                    </td>
                                </tr>
                                <tr class="guaranteePolicy">
                                    <td><font color="red">*</font>主机保修期</td>
                                    <td>
                                        <input type="text" name="guaranteePolicyHostGuaranteePeriodNum" id="guaranteePolicyHostGuaranteePeriodNum" class="input-middle" placeholder="请输入" value="${fn:substringBefore(afterSaleSupplyPolicyDto.guaranteePolicyHostGuaranteePeriod, ',')}" onblur="checkValue(this)"/>
                                        <select style="width: 100px;" name="guaranteePolicyHostGuaranteePeriodUnit">
                                            <option value="小时" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.guaranteePolicyHostGuaranteePeriod, ',') == '小时'}">selected</c:if>>小时</option>
                                            <option value="天" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.guaranteePolicyHostGuaranteePeriod, ',') == '天'}">selected</c:if>>天</option>
                                            <option value="月" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.guaranteePolicyHostGuaranteePeriod, ',') == '月'}">selected</c:if>>月</option>
                                            <option value="年" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.guaranteePolicyHostGuaranteePeriod, ',') == '年'}">selected</c:if>>年</option>
                                        </select>
                                        <span class="bt-small bt-bg-style bg-light-blue"
                                              onclick="setModelTime('guaranteePolicyHostGuaranteePeriodNum','guaranteePolicyHostGuaranteePeriodUnit',12,'月');"
                                              style="margin-left: 10px;margin-top: 5px">12月</span>
                                    </td>
                                </tr>
                                <tr class="guaranteePolicy">
                                    <td>配件保修期</td>
                                    <td>
                                        <input type="text" name="guaranteePolicyPartsGuaranteePeriodNum" class="input-middle" placeholder="请输入" value="${fn:substringBefore(afterSaleSupplyPolicyDto.guaranteePolicyPartsGuaranteePeriod, ',')}" onblur="checkValue(this)"/>
                                        <select style="width: 100px;" name="guaranteePolicyPartsGuaranteePeriodUnit">
                                            <option value="小时" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.guaranteePolicyPartsGuaranteePeriod, ',') == '小时'}">selected</c:if>>小时</option>
                                            <option value="天" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.guaranteePolicyPartsGuaranteePeriod, ',') == '天'}">selected</c:if>>天</option>
                                            <option value="月" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.guaranteePolicyPartsGuaranteePeriod, ',') == '月'}">selected</c:if>>月</option>
                                            <option value="年" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.guaranteePolicyPartsGuaranteePeriod, ',') == '年'}">selected</c:if>>年</option>
                                        </select>
                                    </td>
                                </tr>
                                <tr class="guaranteePolicy">
                                    <td><font color="red">*</font>周期计算方式</td>
                                    <td>
                                        <input type="radio" value="1" name="guaranteePolicyCycleCaltype" <c:if test="${afterSaleSupplyPolicyDto.guaranteePolicyCycleCaltype == '1'}">checked</c:if>> 出厂时间
                                        <input type="radio" value="2" name="guaranteePolicyCycleCaltype" <c:if test="${afterSaleSupplyPolicyDto.guaranteePolicyCycleCaltype == '2'}">checked</c:if>> 客户签收时间
                                        <input type="radio" value="3" name="guaranteePolicyCycleCaltype" <c:if test="${afterSaleSupplyPolicyDto.guaranteePolicyCycleCaltype == '3'}">checked</c:if>> 发票时间
                                        <input type="radio" value="4" name="guaranteePolicyCycleCaltype" <c:if test="${afterSaleSupplyPolicyDto.guaranteePolicyCycleCaltype == '4'}">checked</c:if>> 安装时间
                                        <input type="radio" value="5" name="guaranteePolicyCycleCaltype" <c:if test="${afterSaleSupplyPolicyDto.guaranteePolicyCycleCaltype == '5'}">checked</c:if>> 贝登入库时间
                                    </td>
                                </tr>
                                <tr class="guaranteePolicy">
                                    <td><font color="red">*</font>保修区域</td>
                                    <td>
                                        <input type="radio" value="1" name="guaranteePolicyArea" <c:if test="${afterSaleSupplyPolicyDto.guaranteePolicyArea == '1'}">checked</c:if>> 全国
                                        <input type="radio" value="2" name="guaranteePolicyArea" <c:if test="${afterSaleSupplyPolicyDto.guaranteePolicyArea == '2'}">checked</c:if>> 部分区域
                                    </td>
                                </tr>
                                <tr class="guaranteePolicy">
                                    <td>保修区域说明</td>
                                    <td><input type="text" name="guaranteePolicyAreaComment" class="input-middle" placeholder="比如：偏远地区除外、仅江苏等" value="${afterSaleSupplyPolicyDto.guaranteePolicyAreaComment}"/>
                                        <span class="bt-small bt-bg-style bg-light-blue pos_rel" onclick="setModelInfo('guaranteePolicyAreaComment','参照供应商政策偏远地区除外');" style="margin-left: 10px;margin-top: 5px">参照供应商政策偏远地区除外</span>
                                    </td>
                                </tr>
                                <tr class="guaranteePolicy">
                                    <td>响应时效</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                <input type="text" name="guaranteePolicyResponseTimeNum" class="input-middle" placeholder="请输入" value="${fn:substringBefore(afterSaleSupplyPolicyDto.guaranteePolicyResponseTime, ',')eq 'null'?'':fn:substringBefore(afterSaleSupplyPolicyDto.guaranteePolicyResponseTime, ',')}" onblur="checkValue(this)"/>
                                                <select style="width: 100px;" name="guaranteePolicyResponseTimeUnit">
                                                    <option value="小时" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.guaranteePolicyResponseTime, ',') == '小时'}">selected</c:if>>小时</option>
                                                    <option value="天" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.guaranteePolicyResponseTime, ',') == '天'}">selected</c:if>>天</option>
                                                    <option value="月" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.guaranteePolicyResponseTime, ',') == '月'}">selected</c:if>>月</option>
                                                    <option value="年" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.guaranteePolicyResponseTime, ',') == '年'}">selected</c:if>>年</option>
                                                </select>
                                                <i class="iconbluesigh ml4 contorlIcon"></i>
                                                <span class="bt-small bt-bg-style bg-light-blue"
                                                      onclick="setModelTime('guaranteePolicyResponseTimeNum','guaranteePolicyResponseTimeUnit',2,'小时');"
                                                      style="margin-left: 10px;margin-top: 5px">2小时</span>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                XX时间内与客户联系
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="guaranteePolicy">
                                    <td>上门时效</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                <input type="text" name="guaranteePolicyVisitTimeNum" class="input-middle" placeholder="请输入" value="${fn:substringBefore(afterSaleSupplyPolicyDto.guaranteePolicyVisitTime, ',')eq 'null'?'':fn:substringBefore(afterSaleSupplyPolicyDto.guaranteePolicyVisitTime, ',')}" onblur="checkValue(this)"/>
                                                <select style="width: 100px;" name="guaranteePolicyVisitTimeUnit">
                                                    <option value="小时" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.guaranteePolicyVisitTime, ',') == '小时'}">selected</c:if>>小时</option>
                                                    <option value="天" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.guaranteePolicyVisitTime, ',') == '天'}">selected</c:if>>天</option>
                                                    <option value="月" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.guaranteePolicyVisitTime, ',') == '月'}">selected</c:if>>月</option>
                                                    <option value="年" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.guaranteePolicyVisitTime, ',') == '年'}">selected</c:if>>年</option>
                                                </select>
                                                <i class="iconbluesigh ml4 contorlIcon"></i>
                                                 <span class="bt-small bt-bg-style bg-light-blue"
                                                       onclick="setModelTime('guaranteePolicyVisitTimeNum','guaranteePolicyVisitTimeUnit',72,'小时');"
                                                       style="margin-left: 10px;margin-top: 5px">72小时</span>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                预约上门的时间需要在客户需求上门的XX时间内。
                                                一旦与客户约定具体上门时间，则需要在约定的时间上门
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="guaranteePolicy">
                                    <td>维修时效</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                <input type="text" name="guaranteePolicyRepaireTimeNum" class="input-middle" placeholder="请输入" value="${fn:substringBefore(afterSaleSupplyPolicyDto.guaranteePolicyRepaireTime, ',')eq 'null'?'':fn:substringBefore(afterSaleSupplyPolicyDto.guaranteePolicyRepaireTime, ',')}" onblur="checkValue(this)"/>
                                                <select style="width: 100px;" name="guaranteePolicyRepaireTimeUnit">
                                                    <option value="小时" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.guaranteePolicyRepaireTime, ',') == '小时'}">selected</c:if>>小时</option>
                                                    <option value="天" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.guaranteePolicyRepaireTime, ',') == '天'}">selected</c:if>>天</option>
                                                    <option value="月" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.guaranteePolicyRepaireTime, ',') == '月'}">selected</c:if>>月</option>
                                                    <option value="年" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.guaranteePolicyRepaireTime, ',') == '年'}">selected</c:if>>年</option>
                                                </select>
                                                <i class="iconbluesigh ml4 contorlIcon"></i>
                                                <span class="bt-small bt-bg-style bg-light-blue"
                                                      onclick="setModelTime('guaranteePolicyRepaireTimeNum','guaranteePolicyRepaireTimeUnit',72,'小时');"
                                                      style="margin-left: 10px;margin-top: 5px">72小时</span>
                                                <span class="bt-small bt-bg-style bg-light-blue"
                                                      onclick="setModelTime('guaranteePolicyRepaireTimeNum','guaranteePolicyRepaireTimeUnit',168,'小时');"
                                                      style="margin-left: 10px;margin-top: 5px">168小时</span>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                通常是当天（24）小时内维修完毕，某些产品维修时间超24小时，如大型设备
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="guaranteePolicy">
                                    <td>保修备注</td>
                                    <td><input type="text" name="guaranteePolicyRepaireComment" class="input-middle" placeholder="需要特殊情况的说明" value="${afterSaleSupplyPolicyDto.guaranteePolicyRepaireComment}"/></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </li>
                </c:if>
                <c:if test="${spuType == '316' or spuType == '1008' or spuType == '317' or spuType == '318'}">
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">退货政策</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <tbody>
                            <tr>
                                <td><font color="red">*</font>是否支持退货</td>
                                <td>
                                    <input type="radio" value="1" name="returnPolicySupportReturn" onchange="returnPolicySupportReturnChange(this.value)" <c:if test="${afterSaleSupplyPolicyDto.returnPolicySupportReturn == '1'}">checked</c:if>> 支持退货
                                    <input type="radio" value="0" name="returnPolicySupportReturn" onchange="returnPolicySupportReturnChange(this.value)" <c:if test="${afterSaleSupplyPolicyDto.returnPolicySupportReturn == '0'}">checked</c:if>> 不支持退货
                                </td>
                            </tr>
                            <tr class="returnPolicy">
                                <td><font color="red">*</font>退货条件</td>
                                <td>
                                    <div class="customername pos_rel">
                                        <span>
                                            <input type="text" name="returnPolicyCondition" class="input-middle" placeholder="质量问题" value="${afterSaleSupplyPolicyDto.returnPolicyCondition}"/>
                                            <i class="iconbluesigh ml4 contorlIcon"></i>
                                        </span>
                                        <div class="pos_abs customernameshow">
                                            列出可换货情况，或不可换货的情况
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="returnPolicy">
                                <td>是否需要鉴定</td>
                                <td>
                                    <div class="customername pos_rel">
                                        <span>
                                            <input type="radio" value="1" name="returnPolicyNeedIdentify" <c:if test="${afterSaleSupplyPolicyDto.returnPolicyNeedIdentify == '1'}">checked</c:if> onchange="returnPolicyNeedIdentifyChange(this,'returnPolicyTr')"> 是
                                            <input type="radio" value="0" name="returnPolicyNeedIdentify" <c:if test="${afterSaleSupplyPolicyDto.returnPolicyNeedIdentify == '0'}">checked</c:if> onchange="returnPolicyNeedIdentifyChange(this,'returnPolicyTr')"> 否
                                            <i class="iconbluesigh ml4 contorlIcon"></i>
                                        </span>
                                        <div class="pos_abs customernameshow">
                                            是否需要厂家人员鉴定（判断）产品有无故障，符不符合退换机条件
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="returnPolicy" id="returnPolicyTr">
                                <td>鉴定方式</td>
                                <td>
                                    <input type="checkbox" value="0" name="returnPolicyIdentifyType" <c:if test="${fn:contains(afterSaleSupplyPolicyDto.returnPolicyIdentifyType,'0')}">checked</c:if>> 电话鉴定
                                    <input type="checkbox" value="1" name="returnPolicyIdentifyType" <c:if test="${fn:contains(afterSaleSupplyPolicyDto.returnPolicyIdentifyType,'1')}">checked</c:if>> 上门鉴定
                                    <input type="checkbox" value="2" name="returnPolicyIdentifyType" <c:if test="${fn:contains(afterSaleSupplyPolicyDto.returnPolicyIdentifyType,'2')}">checked</c:if>> 返厂鉴定
                                </td>
                            </tr>
                            <tr class="returnPolicy">
                                <td>退货期限</td>
                                <td>
                                    <div class="customername pos_rel">
                                        <span>
                                            <input type="text" name="returnPolicyReturnPeriodNum" class="input-middle" placeholder="请输入" value="${fn:substringBefore(afterSaleSupplyPolicyDto.returnPolicyReturnPeriod, ',')}" onblur="checkValue(this)"/>
                                            <select style="width: 100px;" name="returnPolicyReturnPeriodUnit">
                                                <option value="小时" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.returnPolicyReturnPeriod, ',') == '小时'}">selected</c:if>>小时</option>
                                                <option value="天" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.returnPolicyReturnPeriod, ',') == '天'}">selected</c:if>>天</option>
                                                <option value="月" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.returnPolicyReturnPeriod, ',') == '月'}">selected</c:if>>月</option>
                                                <option value="年" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.returnPolicyReturnPeriod, ',') == '年'}">selected</c:if>>年</option>
                                            </select>
                                            <i class="iconbluesigh ml4 contorlIcon"></i>
                                            <span class="bt-small bt-bg-style bg-light-blue pos_rel"
                                                  onclick="setModelTime('returnPolicyReturnPeriodNum','returnPolicyReturnPeriodUnit',7,'天');"
                                                  style="margin-left: 10px;margin-top: 5px">7天</span>
                                        </span>
                                        <div class="pos_abs customernameshow">
                                            客户若需要退货，需要在XX时间内提交诉求，超过这个实现不支持退货。
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="returnPolicy">
                                <td>周期计算方式</td>
                                <td>
                                    <input type="radio" value="1" name="returnPolicyCycleCaltyp" <c:if test="${afterSaleSupplyPolicyDto.returnPolicyCycleCaltyp == '1'}">checked</c:if>> 出厂时间
                                    <input type="radio" value="2" name="returnPolicyCycleCaltyp" <c:if test="${afterSaleSupplyPolicyDto.returnPolicyCycleCaltyp == '2'}">checked</c:if>> 客户签收时间
                                    <input type="radio" value="3" name="returnPolicyCycleCaltyp" <c:if test="${afterSaleSupplyPolicyDto.returnPolicyCycleCaltyp == '3'}">checked</c:if>> 发票时间
                                    <input type="radio" value="4" name="returnPolicyCycleCaltyp" <c:if test="${afterSaleSupplyPolicyDto.returnPolicyCycleCaltyp == '4'}">checked</c:if>> 安装时间
                                    <input type="radio" value="5" name="returnPolicyCycleCaltyp" <c:if test="${afterSaleSupplyPolicyDto.returnPolicyCycleCaltyp == '5'}">checked</c:if>> 贝登入库时间
                                </td>
                            </tr>
                            <tr class="returnPolicy">
                                <td>包装要求</td>
                                <td><input type="text" name="returnPolicyPackagingRequirements" class="input-middle" placeholder="包装污损、破损时如何处理" value="${afterSaleSupplyPolicyDto.returnPolicyPackagingRequirements}"/>
                                    <span class="bt-small bt-bg-style bg-light-blue" onclick="setModelInfo('returnPolicyPackagingRequirements','无破损无污损');" style="margin-left: 10px;margin-top: 5px">无破损无污损</span>
                                </td>
                            </tr>
                            <tr class="returnPolicy">
                                <td>退货备注</td>
                                <td><input type="text" name="returnPolicyReturnComments" class="input-middle" placeholder="需要特殊情况的说明" value="${afterSaleSupplyPolicyDto.returnPolicyReturnComments}"/></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </li>
                </c:if>

                <c:if test="${spuType == '316' or spuType == '1008' or spuType == '317' or spuType == '318'}">
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">换货政策</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <tbody>
                            <tr>
                                <td><font color="red">*</font>是否支持换货</td>
                                <td>
                                    <input type="radio" value="1" name="exchangePolicySupportChange" onchange="exchangePolicySupportChangge(this.value)" <c:if test="${afterSaleSupplyPolicyDto.exchangePolicySupportChange == '1'}">checked</c:if>> 支持换货
                                    <input type="radio" value="0" name="exchangePolicySupportChange" onchange="exchangePolicySupportChangge(this.value)" <c:if test="${afterSaleSupplyPolicyDto.exchangePolicySupportChange == '0'}">checked</c:if>> 不支持换货
                                </td>
                            </tr>
                            <tr class="exchangePolicy">
                                <td><font color="red">*</font>换货条件</td>
                                <td>
                                    <div class="customername pos_rel">
                                        <span>
                                            <input type="text" name="exchangePolicyExchangeContition" class="input-middle" placeholder="质量问题" value="${afterSaleSupplyPolicyDto.exchangePolicyExchangeContition}"/>
                                            <i class="iconbluesigh ml4 contorlIcon"></i>
                                        </span>
                                        <div class="pos_abs customernameshow">
                                            列出可换货情况，或不可换货的情况
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="exchangePolicy">
                                <td>是否需要鉴定</td>
                                <td>
                                    <div class="customername pos_rel">
                                        <span>
                                            <input type="radio" value="1" name="exchangePolicyNeedIdentify" <c:if test="${afterSaleSupplyPolicyDto.exchangePolicyNeedIdentify == '1'}">checked</c:if> onchange="returnPolicyNeedIdentifyChange(this,'exchangePolicyTr')"> 是
                                            <input type="radio" value="0" name="exchangePolicyNeedIdentify" <c:if test="${afterSaleSupplyPolicyDto.exchangePolicyNeedIdentify == '0'}">checked</c:if> onchange="returnPolicyNeedIdentifyChange(this,'exchangePolicyTr')"> 否
                                            <i class="iconbluesigh ml4 contorlIcon"></i>
                                        </span>
                                        <div class="pos_abs customernameshow">
                                            是否需要厂家人员鉴定（判断）产品有无故障，符不符合退换机条件
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="exchangePolicy" id="exchangePolicyTr">
                                <td>鉴定方式</td>
                                <td>
                                    <input type="checkbox" value="0" name="exchangePolicyIdentifyType" <c:if test="${fn:contains(afterSaleSupplyPolicyDto.exchangePolicyIdentifyType,'0')}">checked</c:if>> 电话鉴定
                                    <input type="checkbox" value="1" name="exchangePolicyIdentifyType" <c:if test="${fn:contains(afterSaleSupplyPolicyDto.exchangePolicyIdentifyType,'1')}">checked</c:if>> 上门鉴定
                                    <input type="checkbox" value="2" name="exchangePolicyIdentifyType" <c:if test="${fn:contains(afterSaleSupplyPolicyDto.exchangePolicyIdentifyType,'2')}">checked</c:if>> 返厂鉴定
                                </td>
                            </tr>
                            <tr class="exchangePolicy">
                                <td>换货期限</td>
                                <td>
                                    <div class="customername pos_rel">
                                        <span>
                                            <input type="text" name="exchangePolicyExchangePeriodNum" class="input-middle" placeholder="请输入" value="${fn:substringBefore(afterSaleSupplyPolicyDto.exchangePolicyExchangePeriod, ',')}" onblur="checkValue(this)"/>
                                            <select style="width: 100px;" name="exchangePolicyExchangePeriodUnit">
                                                <option value="小时" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.exchangePolicyExchangePeriod, ',') == '小时'}">selected</c:if>>小时</option>
                                                <option value="天" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.exchangePolicyExchangePeriod, ',') == '天'}">selected</c:if>>天</option>
                                                <option value="月" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.exchangePolicyExchangePeriod, ',') == '月'}">selected</c:if>>月</option>
                                                <option value="年" <c:if test="${fn:substringAfter (afterSaleSupplyPolicyDto.exchangePolicyExchangePeriod, ',') == '年'}">selected</c:if>>年</option>
                                            </select>
                                            <i class="iconbluesigh ml4 contorlIcon"></i>
                                            <span class="bt-small bt-bg-style bg-light-blue"
                                                  onclick="setModelTime('exchangePolicyExchangePeriodNum','exchangePolicyExchangePeriodUnit',14,'天');"
                                                  style="margin-left: 10px;margin-top: 5px">14天</span>
                                        </span>
                                        <div class="pos_abs customernameshow">
                                            客户若需要换货，需要在XX时间内提交诉求，超过这个实现不支持换货
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="exchangePolicy">
                                <td>周期计算方式</td>
                                <td>
                                    <input type="radio" value="1" name="exchangePolicyCycleCaltyp" <c:if test="${afterSaleSupplyPolicyDto.exchangePolicyCycleCaltyp == '1'}">checked</c:if>> 出厂时间
                                    <input type="radio" value="2" name="exchangePolicyCycleCaltyp" <c:if test="${afterSaleSupplyPolicyDto.exchangePolicyCycleCaltyp == '2'}">checked</c:if>> 客户签收时间
                                    <input type="radio" value="3" name="exchangePolicyCycleCaltyp" <c:if test="${afterSaleSupplyPolicyDto.exchangePolicyCycleCaltyp == '3'}">checked</c:if>> 发票时间
                                    <input type="radio" value="4" name="exchangePolicyCycleCaltyp" <c:if test="${afterSaleSupplyPolicyDto.exchangePolicyCycleCaltyp == '4'}">checked</c:if>> 安装时间
                                    <input type="radio" value="5" name="exchangePolicyCycleCaltyp" <c:if test="${afterSaleSupplyPolicyDto.exchangePolicyCycleCaltyp == '5'}">checked</c:if>> 贝登入库时间
                                </td>
                            </tr>
                            <tr class="exchangePolicy">
                                <td>包装要求</td>
                                <td><input type="text" name="exchangePolicyPackagingRequirements" class="input-middle" placeholder="包装污损、破损时如何处理" value="${afterSaleSupplyPolicyDto.exchangePolicyPackagingRequirements}"/>
                                    <span class="bt-small bt-bg-style bg-light-blue" onclick="setModelInfo('exchangePolicyPackagingRequirements','无破损无污损');" style="margin-left: 10px;margin-top: 5px">无破损无污损</span>
                                </td>
                            </tr>
                            <tr class="exchangePolicy">
                                <td>换货备注</td>
                                <td><input type="text" name="exchangePolicyExchangeComments" class="input-middle" placeholder="需要特殊情况的说明" value="${afterSaleSupplyPolicyDto.exchangePolicyExchangeComments}"/></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </li>
                </c:if>
                <c:if test="${spuType == '316'}">
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">保外政策</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <tbody>
                                <tr>
                                    <td>是否提供有偿维修</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                <input type="radio" value="1" name="parolePolicySupportRepair" <c:if test="${afterSaleSupplyPolicyDto.parolePolicySupportRepair == '1'}">checked</c:if>> 是
                                                <input type="radio" value="0" name="parolePolicySupportRepair" <c:if test="${afterSaleSupplyPolicyDto.parolePolicySupportRepair == '0'}">checked</c:if>> 否
                                                <i class="iconbluesigh ml4 contorlIcon"></i>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                超过保修期之后，供应商能否提供有偿的维修服务
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>是否提供翻新服务</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                <input type="radio" value="1" name="parolePolicySupportRenovation" <c:if test="${afterSaleSupplyPolicyDto.parolePolicySupportRenovation == '1'}">checked</c:if>> 是
                                                <input type="radio" value="0" name="parolePolicySupportRenovation" <c:if test="${afterSaleSupplyPolicyDto.parolePolicySupportRenovation == '0'}">checked</c:if>> 否
                                                <i class="iconbluesigh ml4 contorlIcon"></i>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                产品因某些原因（破损、污损、故障）等，不能二次销售时，供应商能否对贝登提供翻新服务
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>是否提供纸箱</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                <input type="radio" value="1" name="parolePolicySupplyBox" <c:if test="${afterSaleSupplyPolicyDto.parolePolicySupplyBox == '1'}">checked</c:if>> 可提供
                                                <input type="radio" value="0" name="parolePolicySupplyBox" <c:if test="${afterSaleSupplyPolicyDto.parolePolicySupplyBox == '0'}">checked</c:if>> 不可提供
                                                <i class="iconbluesigh ml4 contorlIcon"></i>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                产品因某些原因包装破损、污损等，不能二次销售时，供应商能否对贝登提供原厂包装箱
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>是否提供附件</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                <input type="radio" value="1" name="parolePolicySupplyAttachment" <c:if test="${afterSaleSupplyPolicyDto.parolePolicySupplyAttachment == '1'}">checked</c:if>> 可提供
                                                <input type="radio" value="0" name="parolePolicySupplyAttachment" <c:if test="${afterSaleSupplyPolicyDto.parolePolicySupplyAttachment == '0'}">checked</c:if>> 不可提供
                                                <i class="iconbluesigh ml4 contorlIcon"></i>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                产品附件因某些原因丢失，损坏等，不能二次销售时，供应商能否对贝登提供响应的附件
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </li>
                </c:if>
                <c:if test="${spuType == '316' or spuType == '1008' or spuType == '317' or spuType == '318'}">
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">超期处理政策</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <tbody>
                            <tr>
                                <td>可否提供备用机</td>
                                <td>
                                    <input type="radio" value="1" name="overduePolicySupplyBackup" <c:if test="${afterSaleSupplyPolicyDto.overduePolicySupplyBackup == '1'}">checked</c:if>> 是
                                    <input type="radio" value="0" name="overduePolicySupplyBackup" <c:if test="${afterSaleSupplyPolicyDto.overduePolicySupplyBackup == '0'}">checked</c:if>> 否
                                </td>
                            </tr>
                            <tr>
                                <td>超期处理详情</td>
                                <td><input type="text" name="overduePolicyDetail" class="input-middle" placeholder="请输入" value="${afterSaleSupplyPolicyDto.overduePolicyDetail}"/></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </li>
                </c:if>
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">服务联系人</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <tbody>
                            <tr>
                                <td>
                                    <span class="edit-user" tabTitle='{"num":"customer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
												"link":"${pageContext.request.contextPath}/trader/supplier/baseinfo.do?traderId=${afterSaleSupplyPolicyDto.traderId}","title":"基本信息"}' onclick="toServiceContract(this)">去添加</span>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </li>

                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">相关附件</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <tbody>
                                <tr>
                                    <td>
                                        <div class="f_left insertli">
                                            <c:choose>
                                                <c:when test="${!empty attashmentList}">
                                                    <c:forEach items="${attashmentList}" var="attashment" varStatus="st">
                                                        <div class="mb8">
                                                            <div class="f_left">
                                                                <input type="file" class="upload_file" name="lwfile" id="file_${st.index}" style="display: none;" onchange="uploadFile(this,${st.index});"/>
                                                                <input type="text" class="input-middle" id="name_${st.index}"
                                                                       readonly="readonly"
                                                                       placeholder="请上传文件" name="attashmentName"
                                                                       onclick="file_${st.index}.click();" value="${attashment.fileName}">
                                                                <input type="hidden" id="uri_${st.index}" name="attashmentUri" value="${attashment.uri}">
                                                                <label class="bt-bg-style bt-middle bg-light-blue ml10" onclick="return $('#file_${st.index}').click();">浏览</label>
                                                            </div>

                                                            <div class="f_left">
                                                                <!-- 上传成功出现 -->
                                                                <i class="iconsuccesss ml7" id="img_icon_${st.index}"></i>
                                                                <a href="http://${attashment.domain}${attashment.uri}" target="_blank" class="font-blue cursor-pointer mr5 ml10 mt4" id="img_view_${st.index}">查看</a>

                                                                <c:if test="${st.index == 0}">
                                                                     <span class="font-red cursor-pointer mt4" onclick="del(${st.index})"  id="img_del_${st.index}">删除</span>
                                                                </c:if>
                                                                <c:if test="${st.index > 0}">
                                                                     <span class="font-red cursor-pointer mt4" onclick="delAttachment(this)" id="img_del_${st.index}">删除</span>
                                                                </c:if>
                                                            </div>
                                                        </div>
                                                    </c:forEach>
                                                </c:when>
                                                <c:otherwise>
                                                    <div class="mb8">
                                                        <div class="f_left">
                                                            <input type="file" class="upload_file" name="lwfile" id="file_1" style="display: none;" onchange="uploadFile(this,1);"/>
                                                            <input type="text" class="input-middle" id="name_1"
                                                                   readonly="readonly"
                                                                   placeholder="请上传文件" name="attashmentName"
                                                                   onclick="file_1.click();">
                                                            <input type="hidden" id="uri_1" name="attashmentUri">
                                                            <label class="bt-bg-style bt-middle bg-light-blue ml10" onclick="return $('#file_1').click();">浏览</label>
                                                        </div>

                                                        <!-- 上传成功出现 -->
                                                        <i class="iconsuccesss ml7 none" id="img_icon_1"></i>
                                                        <a href="" target="_blank" class="font-blue cursor-pointer mr5 ml10 mt4 none" id="img_view_1">查看</a>
                                                        <span class="font-red cursor-pointer mt4 none" onclick="del(1)" id="img_del_1">删除</span>
                                                        <div class="clear"></div>
                                                    </div>
                                                </c:otherwise>
                                            </c:choose>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="bt-border-style bt-small border-blue f_left" id="conadd1">
                                            <span class="f_left" onclick="con_add(1);">继续添加</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td align="left">
                                        <div>
                                            <lable style="float:left">- 可以上传各类文件，包括图片、word、excel、pdf等</lable>
                                            <br>
                                            <lable style="float:left"> - 单个最大限制10M,最多添加10个附件</lable>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </li>

                <li>
                    <div class="parts">
                        <div class="title-container" style="text-align:center">
                            <span class="bt-small bt-bg-style bg-light-blue" onclick="save();"  style="margin-left: 100px;margin-top: 5px">保存</span>
                        </div>
                    </div>
                </li>
            </ul>
        </form>
    </div>
</div>
<script type="text/javascript">


    function delAttachment(obj) {
        var uri = $(obj).parent().find("a").attr("href");
        if (uri == '') {
            $(obj).parent().parent().remove();
        } else {
            index = layer.confirm("您是否确认该操作？", {
                btn: ['确定', '取消'] //按钮
            }, function () {
                debugger;
                var length = $("input[type='file']").length;
                if (length == 1) {
                    $(obj).parent().find("i").hide();
                    $(obj).parent().find("a").hide();
                    $(obj).parent().find("span").hide();
                    $(obj).parent().parent().parent().find("input[type='text']").val('');
                } else {
                    $(obj).parent().parent().remove();
                }
                layer.close(index);
            }, function () {
            });
        }
    }

    function RndNum(n){
        var rnd="";
        for(var i=0;i<n;i++)
            rnd+=Math.floor(Math.random()*10);
        return rnd;
    }

    //点击继续添加按钮
    function con_add(id) {

        desc = "请上传文件";
        var rndNum = RndNum(8);
        var html = '<div >' +
            '<div class="pos_rel f_left mb8">' +
            '<input type="file" class=" uploadErp"  name="lwfile" id="lwfile_' + id + '_' + rndNum + '" onchange="uploadFile(this, ' + id + ')">' +
            '<input type="text" class="input-middle" id="name_' + id + '_' + rndNum + '" readonly="readonly" placeholder="' + desc + '" name="attashmentName" onclick="lwfile_' + id + '_' + rndNum + '.click();" value ="">' +
            '<input type="hidden" class="input-middle mr5" id="uri_' + id + '_' + rndNum + '" name="attashmentUri" value="">' +
            '<label class="bt-bg-style bt-middle bg-light-blue " type="file" style="margin:0 17px 0 14px;">浏览</label>' +
            '</div>' +
            '<div class="f_left ">' +
            '<i class="iconsuccesss mt5 none" id="img_icon_' + id + '"></i>' +
            '<a href="" target="_blank" class="font-blue cursor-pointer mr10  ml10 mt4 none" id="img_view_' + id + '" style="margin:0 8px 0 12px">查看</a>' +
            '<span class="font-red cursor-pointer mt4" onclick="delAttachment(this)" id="img_del_' + id + '">删除</span>' +
            '</div>' +
            '<div class="clear "></div></div>';
        $("#conadd" + id).before(html);
    }

    function del(num) {
        checkLogin();
        index = layer.confirm("您是否确认该操作？", {
            btn: ['确定', '取消'] //按钮
        }, function () {
            $("#img_icon_" + num).hide();
            $("#img_view_" + num).hide();
            $("#img_del_" + num).hide();
            $("#name_" + num).val("");
            $("#uri_" + num).val("");
            $("#file_" + num).val("");
            layer.close(index);
        }, function () {
        });
    }

    function uploadFile(obj, num) {
        var imgPath = $(obj).val();
        if (imgPath == '' || imgPath == undefined) {
            return false;
        }
        var oldName = imgPath.substr(imgPath.lastIndexOf('\\') + 1);
        var domain = $("#domain").val();
        //判断上传文件的后缀名
        var strExtension = imgPath.substr(imgPath.lastIndexOf('.') + 1);
        // 转换为小写格式
        strExtension = strExtension.toLowerCase();
        if (strExtension != 'jpg' && strExtension != 'gif' && strExtension != 'png' && strExtension != 'doc' && strExtension != 'docx' && strExtension != 'bmp' && strExtension != 'pdf' && strExtension != 'jpeg') {
            layer.alert("文件格式不正确");
            return;
        }

        var fileSize = 0;
        var isIE = /msie/i.test(navigator.userAgent) && !window.opera;
        if (isIE && !obj.files) {
            var filePath = obj.value;
            var fileSystem = new ActiveXObject("Scripting.FileSystemObject");
            var file = fileSystem.GetFile(filePath);
            fileSize = file.Size;
        } else {
            fileSize = obj.files[0].size;
        }
        fileSize = Math.round(fileSize / 1024 * 100) / 100; //单位为KB
        if (fileSize > 5120 * 2) {
            layer.alert("上传附件不得超过10M");
            return false;
        }
        $(obj).parent().parent().find("i").attr("class", "iconloading mt5").show();
        $(obj).parent().parent().find("a").hide();
        $(obj).parent().parent().find("span").hide();
        var objCopy1 = $(obj).parent();
        var objCopy2 = $(obj).parent().parent();
        $.ajaxFileUpload({
            url: page_url + '/fileUpload/ajaxFileUpload.do', //用于文件上传的服务器端请求地址
            secureuri: false, //一般设置为false
            fileElementId: $(obj).attr("id"), //文件上传控件的id属性  <input type="file" id="file" name="file" /> 注意，这里一定要有name值   //$("form").serialize(),表单序列化。指把所有元素的ID，NAME 等全部发过去
            dataType: 'json',//返回值类型 一般设置为json
            //服务器成功响应处理函数
            success: function (data) {
                if (data.code == 0) {

                    objCopy1.find("input[type='text']").val(data.fileName);
                    objCopy1.find("input[type='hidden']").val(data.filePath);
                    $("#domain").val(data.httpUrl);
                    objCopy2.find("i").attr("class", "iconsuccesss ml7").show();
                    objCopy2.find("a").attr("href", 'http://' + data.httpUrl + data.filePath).show();
                    objCopy2.find("span").show();
                    $("#brand_book").css("display", "none");
                    $("#name_" + num).removeClass("errorbor");
                } else {
                    layer.alert(data.message);
                }
            },
            //服务器响应失败处理函数
            error: function (data, status, e) {

                if (data.status == 1001) {
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                } else {
                    layer.alert(data.responseText);
                }

            }
        });
    }

    function searchSupplier(){
        checkLogin();
        var searchTraderName = $("#searchTraderName").val()==undefined?"":$("#searchTraderName").val();

        if($("#searchTraderName").val()==''){
            $("#searchTraderName").parent("div").siblings("div").removeClass("none").html("查询条件不允许为空");
            $("#searchTraderName").addClass("errorbor");
            return false;
        }else{
            $("#searchTraderName").parent("div").siblings("div").addClass("none");
            $("#searchTraderName").removeClass("errorbor");
        }

        var searchUrl = page_url+"/aftersale/serviceStandard/getSupplierByName.do?supplierName="+encodeURI(searchTraderName)+"&callbackFuntion=recieveTraderInfo";
        $("#popSupplier").attr('layerParams','{"width":"800px","height":"500px","title":"搜索供应商","link":"'+searchUrl+'"}');
        $("#popSupplier").click();
    }

    function research(){
        checkLogin();
        $("#searchTraderName").val("");
        $("#searchTraderName").show();
        $("#name").addClass("none");
        $("#errorMes").removeClass("none");
        $("#research").addClass("none");
    }

    function recieveTraderInfo(traderId,traderSupplierName) {
        $("#traderId").val(traderId);
        $("#traderName").val(traderSupplierName);
    }

    function openTab(span){
        var frontPageId = $(window.parent.document).find('.active').eq(1).attr('id');
        var newPageId;
        var tabTitle = $(span).attr('tabTitle');
        if (typeof(tabTitle) == 'undefined') {
            alert('参数错误');
        } else {
            tabTitle = $.parseJSON(tabTitle);
        }
        var id = tabTitle.num;
        // var id = 'index' + Date.parse(new Date()) + Math.floor(Math.random()*1000);
        var name = tabTitle.title;
        var uri = tabTitle.link;
        var closable = 1;
        var item = { 'id': id, 'name': name, 'url': uri, 'closable': closable == 1 ? true : false };
        self.parent.closableTab.addTab(item);
        self.parent.closableTab.resizeMove();
        $(window.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
    }

</script>
<%@ include file="../../common/footer.jsp"%>