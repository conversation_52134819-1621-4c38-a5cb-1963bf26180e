package com.vedeng.erp.system.domain.entity;

import lombok.Data;

import java.io.Serializable;

/**
    * 当前审核情况
    */
@Data
public class VerifiesInfoEntity implements Serializable {

    private static final long serialVersionUID = 7301331523076906472L;

    private Integer verifiesInfoId;

    /**
    * 关联表名
    */
    private String relateTable;

    /**
    * 关联表主键
    */
    private Integer relateTableKey;

    /**
    * 类型SYS_OPTION_DEFINITION_ID
    */
    private Integer verifiesType;

    /**
    * 上次审核人名称英文
    */
    private String lastVerifyUsername;

    /**
    * 当前审核人名称英文逗号拼接
    */
    private String verifyUsername;

    /**
    * 当前流程状态0审核中1审核通过2审核不通过
    */
    private Integer status;

    /**
    * 添加时间
    */
    private Long addTime;

    /**
    * 编辑时间
    */
    private Long modTime;

    /**
    * 审批人ID
    */
    private Integer verifyerId;

    /**
    * 申请人ID
    */
    private Integer applyerId;

    public Integer getVerifiesInfoId() {
        return verifiesInfoId;
    }

    public void setVerifiesInfoId(Integer verifiesInfoId) {
        this.verifiesInfoId = verifiesInfoId;
    }

    public String getRelateTable() {
        return relateTable;
    }

    public void setRelateTable(String relateTable) {
        this.relateTable = relateTable;
    }

    public Integer getRelateTableKey() {
        return relateTableKey;
    }

    public void setRelateTableKey(Integer relateTableKey) {
        this.relateTableKey = relateTableKey;
    }

    public Integer getVerifiesType() {
        return verifiesType;
    }

    public void setVerifiesType(Integer verifiesType) {
        this.verifiesType = verifiesType;
    }

    public String getLastVerifyUsername() {
        return lastVerifyUsername;
    }

    public void setLastVerifyUsername(String lastVerifyUsername) {
        this.lastVerifyUsername = lastVerifyUsername;
    }

    public String getVerifyUsername() {
        return verifyUsername;
    }

    public void setVerifyUsername(String verifyUsername) {
        this.verifyUsername = verifyUsername;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getAddTime() {
        return addTime;
    }

    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    public Long getModTime() {
        return modTime;
    }

    public void setModTime(Long modTime) {
        this.modTime = modTime;
    }

    public Integer getVerifyerId() {
        return verifyerId;
    }

    public void setVerifyerId(Integer verifyerId) {
        this.verifyerId = verifyerId;
    }

    public Integer getApplyerId() {
        return applyerId;
    }

    public void setApplyerId(Integer applyerId) {
        this.applyerId = applyerId;
    }
}