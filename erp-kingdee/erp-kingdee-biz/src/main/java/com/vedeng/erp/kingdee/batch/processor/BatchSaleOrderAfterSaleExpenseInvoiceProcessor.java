package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDetailDto;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceDetailDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceDtoMapper;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.erp.kingdee.dto.result.KingDeeReceiveQueryResultDto;
import com.vedeng.erp.kingdee.service.KingDeeReceiveCommonService;
import com.vedeng.erp.kingdee.service.KingDeeReceiveFeeService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 销售费用的负向红票组装
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchSaleOrderAfterSaleExpenseInvoiceProcessor implements ItemProcessor<BatchInvoiceDto, KingDeeRedInvoiceDto> {

    public static final String SPECIAL_INVOICE = "专用发票";
    @Autowired
    private BatchInvoiceDtoMapper batchInvoiceDtoMapper;
    @Autowired
    private BatchInvoiceDetailDtoMapper batchInvoiceDetailDtoMapper;
    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private KingDeeReceiveFeeService kingDeeReceiveFeeService;

    @Override
    public KingDeeRedInvoiceDto process(BatchInvoiceDto batchInvoiceDto) throws Exception {
        log.info("组装销售费用的负向红票组装{}", JSON.toJSONString(batchInvoiceDto));

        if (StrUtil.isBlank(batchInvoiceDto.getInvoiceNo()) || StrUtil.isBlank(batchInvoiceDto.getInvoiceCode())) {
            log.warn("红票无发票号或者发票代码");
            return null;
        }

        // 获取应收单
        List<KingDeeReceiveQueryResultDto> kingDeeReceiveQueryResultDtoList = kingDeeReceiveFeeService.getKingDeeReceiveFee(batchInvoiceDto.getInvoiceId().toString());
        if (CollUtil.isEmpty(kingDeeReceiveQueryResultDtoList)) {
            log.warn("无对应应收单数据，无法组装发票");
            return null;
        }

        KingDeeRedInvoiceDto kingDeeRedInvoiceDto = new KingDeeRedInvoiceDto();
        // 设置发票明细数据,虚拟商品明细
        List<BatchInvoiceDetailDto> saleOrderInvoiceDetailList;
        if (CollectionUtils.isEmpty(batchInvoiceDto.getBatchInvoiceDetailDtoList())) {
            // 发票明细为空，则表示是非历史数据，直接取数据库查询发票明细
            log.info("开始处理销售费用负向红票明细：{}", JSON.toJSONString(batchInvoiceDto));
            saleOrderInvoiceDetailList = batchInvoiceDetailDtoMapper.getSaleOrderInvoiceDetailList(batchInvoiceDto.getInvoiceId());
        } else {
            // 否则，则证明是从Excel中读取的发票明细
            log.info("开始处理21-22历史销售费用负向红票明细：{}", JSON.toJSONString(batchInvoiceDto));
            saleOrderInvoiceDetailList = batchInvoiceDto.getBatchInvoiceDetailDtoList();
        }
        saleOrderInvoiceDetailList = saleOrderInvoiceDetailList.stream().filter(detailDto -> Convert.toBool(detailDto.getIsVirtureSku(), false)).collect(Collectors.toList());
        batchInvoiceDto.setBatchInvoiceDetailDtoList(saleOrderInvoiceDetailList);
        BigDecimal taxRate = Objects.isNull(batchInvoiceDto.getRatio()) ? BigDecimal.ZERO : batchInvoiceDto.getRatio().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);

        String saleOrderNoBySaleOrderId = batchInvoiceDtoMapper.getSaleOrderNoBySaleOrderId(batchInvoiceDto.getRelatedId());
        String afterSalesNoByAfterSalesId = batchInvoiceDtoMapper.getAfterSalesNoByAfterSalesId(batchInvoiceDto.getAfterSalesId());

        // 是否专票
        boolean isSpecial = StrUtil.isNotEmpty(batchInvoiceDto.getInvoiceTypeName()) && batchInvoiceDto.getInvoiceTypeName().contains(SPECIAL_INVOICE);

        if (isSpecial) {
            OutPutFeeSpecialInvoiceDto outPutFeeSpecialInvoiceDto = this.specialInvoice(
                    batchInvoiceDto,
                    kingDeeReceiveQueryResultDtoList,
                    saleOrderNoBySaleOrderId,
                    afterSalesNoByAfterSalesId,
                    taxRate);
            kingDeeRedInvoiceDto.setOutPutFeeSpecialInvoiceDto(outPutFeeSpecialInvoiceDto);
        } else {
            OutPutFeePlainInvoiceDto outPutFeePlainInvoiceDto = this.vatPlainInvoice(
                    batchInvoiceDto,
                    kingDeeReceiveQueryResultDtoList,
                    saleOrderNoBySaleOrderId,
                    afterSalesNoByAfterSalesId,
                    taxRate);
            kingDeeRedInvoiceDto.setOutPutFeePlainInvoiceDto(outPutFeePlainInvoiceDto);
        }
        return kingDeeRedInvoiceDto;
    }

    private OutPutFeeSpecialInvoiceDto specialInvoice(BatchInvoiceDto batchInvoiceDto,
                                                      List<KingDeeReceiveQueryResultDto> kingDeeAfterSaleReceivableResultDtoList,
                                                      String saleOrderNoBySaleOrderId,
                                                      String afterSalesNoByAfterSalesId,
                                                      BigDecimal taxRate) {
        OutPutFeeSpecialInvoiceDto outPutFeeSpecialInvoiceDto = new OutPutFeeSpecialInvoiceDto();
        outPutFeeSpecialInvoiceDto.setFQzokBddjtid(batchInvoiceDto.getInvoiceId().toString());
        // 判断是否数据已存在
        boolean old = kingDeeBaseApi.isExist(outPutFeeSpecialInvoiceDto);
        if (old) {
            log.info("销售售后费用红票,金蝶数据已存在:{}", JSON.toJSONString(batchInvoiceDto));
            return null;
        }

        outPutFeeSpecialInvoiceDto.setFid("0");
        outPutFeeSpecialInvoiceDto.setFdate(DateUtil.formatDate(new Date(batchInvoiceDto.getAddTime())));
        outPutFeeSpecialInvoiceDto.setFinvoicedate(DateUtil.formatDate(new Date(batchInvoiceDto.getAddTime())));
        outPutFeeSpecialInvoiceDto.setFinvoiceno(batchInvoiceDto.getInvoiceNo());
        outPutFeeSpecialInvoiceDto.setFQzokFpdm(batchInvoiceDto.getInvoiceCode());
        outPutFeeSpecialInvoiceDto.setFRedBlue("1");
        outPutFeeSpecialInvoiceDto.setFcontactunit(batchInvoiceDto.getTraderCustomerId().toString());

        List<OutPutFeeSpecialInvoiceDetailDto> fsaleexinventry = outPutFeeSpecialInvoiceDto.getFSALEEXINVENTRY();

        List<BatchInvoiceDetailDto> batchInvoiceDetailDtoList = batchInvoiceDto.getBatchInvoiceDetailDtoList();

        batchInvoiceDetailDtoList.forEach(invoiceDetailDto -> {
            OutPutFeeSpecialInvoiceDetailDto fsalesicentry = new OutPutFeeSpecialInvoiceDetailDto();
            fsaleexinventry.add(fsalesicentry);
            fsalesicentry.setFexpenseid(invoiceDetailDto.getUnitKingDeeNo());
            fsalesicentry.setFtaxrate(taxRate.toString());
            fsalesicentry.setFQzokBddjhid(invoiceDetailDto.getInvoiceDetailId().toString());
            fsalesicentry.setFQzokYsddh(saleOrderNoBySaleOrderId);
            fsalesicentry.setFQzokGsywdh(afterSalesNoByAfterSalesId);
            fsalesicentry.setFQzokYwlx("销售售后");
            fsalesicentry.setFsrcbilltypeid("AR_receivable");
            fsalesicentry.setF_QZOK_WLBM(invoiceDetailDto.getSku());

            KingDeeReceiveQueryResultDto kingDeeReceiveQueryResultDto = CollUtil.getFirst(kingDeeAfterSaleReceivableResultDtoList);
            KingDeeReceiveQueryResultDto kingDeeReceiveQueryResultDtoDetail = kingDeeAfterSaleReceivableResultDtoList.stream()
                    .filter(q -> q.getF_QZOK_BDDJHID().equals(invoiceDetailDto.getInvoiceDetailId().toString())).findFirst().orElse(null);

            if (Objects.isNull(kingDeeReceiveQueryResultDtoDetail)) {
                log.error("无法查询费用应收单明细单{}", JSON.toJSONString(invoiceDetailDto));
                throw new KingDeeException("无法查询费用应收单明细单");
            }

            fsalesicentry.setFpriceqty(kingDeeReceiveQueryResultDtoDetail.getFPriceQty());
            fsalesicentry.setFauxtaxprice(kingDeeReceiveQueryResultDtoDetail.getFTaxPrice());

            List<OutPutFeeSpecialInvoiceDetailLinkDto> fsalesicentryLinks = fsalesicentry.getFSALEEXINVENTRY_LINK();
            OutPutFeeSpecialInvoiceDetailLinkDto fsalesicentryLink = new OutPutFeeSpecialInvoiceDetailLinkDto();
            fsalesicentryLinks.add(fsalesicentryLink);
            fsalesicentryLink.setFLinkId("0");
            fsalesicentryLink.setFsaleexinventryLinkFsbillid(kingDeeReceiveQueryResultDto.getFID());
            fsalesicentryLink.setFsaleexinventryLinkFsid(kingDeeReceiveQueryResultDtoDetail.getFEntityDetail_FEntryId());
            fsalesicentryLink.setFsaleexinventryLinkFallamountforold(new BigDecimal(kingDeeReceiveQueryResultDtoDetail.getFPriceQty())
                    .multiply(kingDeeReceiveQueryResultDtoDetail.getFTaxPrice()));
            fsalesicentryLink.setFsaleexinventryLinkFallamountfor(new BigDecimal(kingDeeReceiveQueryResultDtoDetail.getFPriceQty())
                    .multiply(kingDeeReceiveQueryResultDtoDetail.getFTaxPrice()));
        });

        return outPutFeeSpecialInvoiceDto;
    }

    private OutPutFeePlainInvoiceDto vatPlainInvoice(BatchInvoiceDto batchInvoiceDto,
                                                     List<KingDeeReceiveQueryResultDto> kingDeeAfterSaleReceivableResultDtoList,
                                                     String saleOrderNoBySaleOrderId,
                                                     String afterSalesNoByAfterSalesId,
                                                     BigDecimal taxRate) {

        OutPutFeePlainInvoiceDto outPutFeePlainInvoiceDto = new OutPutFeePlainInvoiceDto();
        outPutFeePlainInvoiceDto.setFQzokBddjtid(batchInvoiceDto.getInvoiceId().toString());
        // 判断是否数据已存在
        boolean old = kingDeeBaseApi.isExist(outPutFeePlainInvoiceDto);
        if (old) {
            log.info("销售售后费用红票,金蝶数据已存在:{}", JSON.toJSONString(batchInvoiceDto));
            return null;
        }
        outPutFeePlainInvoiceDto.setFid("0");
        outPutFeePlainInvoiceDto.setFdate(DateUtil.formatDate(new Date(batchInvoiceDto.getAddTime())));
        outPutFeePlainInvoiceDto.setFinvoicedate(DateUtil.formatDate(new Date(batchInvoiceDto.getAddTime())));
        outPutFeePlainInvoiceDto.setFcontactunit(batchInvoiceDto.getTraderCustomerId().toString());
        outPutFeePlainInvoiceDto.setFinvoiceno(batchInvoiceDto.getInvoiceNo());
        outPutFeePlainInvoiceDto.setFQzokFpdm(batchInvoiceDto.getInvoiceCode());
        outPutFeePlainInvoiceDto.setFRedBlue("1");

        List<OutPutFeePlainInvoiceDetailDto> fsalesicentries = outPutFeePlainInvoiceDto.getFSALEEXINVENTRY();


        List<BatchInvoiceDetailDto> batchInvoiceDetailDtoList = batchInvoiceDto.getBatchInvoiceDetailDtoList();

        batchInvoiceDetailDtoList.forEach(invoiceDetailDto -> {
            OutPutFeePlainInvoiceDetailDto fsalesicentry = new OutPutFeePlainInvoiceDetailDto();
            fsalesicentries.add(fsalesicentry);
            fsalesicentry.setFexpenseid(invoiceDetailDto.getUnitKingDeeNo());

            fsalesicentry.setFQzokBddjhid(invoiceDetailDto.getInvoiceDetailId().toString());
            fsalesicentry.setFQzokYsddh(saleOrderNoBySaleOrderId);
            fsalesicentry.setFQzokGsywdh(afterSalesNoByAfterSalesId);
            fsalesicentry.setFQzokYwlx("销售售后");
            fsalesicentry.setFsrcbilltypeid("AR_receivable");
            fsalesicentry.setFtaxrate(taxRate.toString());
            fsalesicentry.setF_QZOK_WLBM(invoiceDetailDto.getSku());

            KingDeeReceiveQueryResultDto kingDeeReceiveQueryResultDto = CollUtil.getFirst(kingDeeAfterSaleReceivableResultDtoList);
            KingDeeReceiveQueryResultDto kingDeeReceiveQueryResultDtoDetail = kingDeeAfterSaleReceivableResultDtoList.stream()
                    .filter(q -> q.getF_QZOK_BDDJHID().equals(invoiceDetailDto.getInvoiceDetailId().toString())).findFirst().orElse(null);
            if (Objects.isNull(kingDeeReceiveQueryResultDtoDetail)) {
                log.error("无法查询费用应收单明细单{}", JSON.toJSONString(invoiceDetailDto));
                throw new KingDeeException("无法查询费用应收单明细单");
            }

            fsalesicentry.setFpriceqty(kingDeeReceiveQueryResultDtoDetail.getFPriceQty());
            fsalesicentry.setFauxtaxprice(kingDeeReceiveQueryResultDtoDetail.getFTaxPrice());

            List<OutPutFeePlainInvoiceDetailLinkDto> fsalesicentryLinks = fsalesicentry.getFSALEEXINVENTRY_LINK();
            OutPutFeePlainInvoiceDetailLinkDto fsalesicentryLink = new OutPutFeePlainInvoiceDetailLinkDto();
            fsalesicentryLinks.add(fsalesicentryLink);
            fsalesicentryLink.setFLinkId("0");
            fsalesicentryLink.setFsaleexinventryLinkFsbillid(kingDeeReceiveQueryResultDto.getFID());
            fsalesicentryLink.setFsaleexinventryLinkFsid(kingDeeReceiveQueryResultDtoDetail.getFEntityDetail_FEntryId());
            fsalesicentryLink.setFsaleexinventryLinkFallamountforold(new BigDecimal(kingDeeReceiveQueryResultDtoDetail.getFPriceQty())
                    .multiply(kingDeeReceiveQueryResultDtoDetail.getFTaxPrice()));
            fsalesicentryLink.setFsaleexinventryLinkFallamountfor(new BigDecimal(kingDeeReceiveQueryResultDtoDetail.getFPriceQty())
                    .multiply(kingDeeReceiveQueryResultDtoDetail.getFTaxPrice()));
        });
        return outPutFeePlainInvoiceDto;
    }

}
