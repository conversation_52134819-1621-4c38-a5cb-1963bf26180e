<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>

<div id="app" style="font-size: 14px">
    <el-table :data="instanceVo" stripe style="width: 100%" :cell-style="cellStyle" :header-cell-style="{background:'#eef1f6',color:'#606266'}">
        <el-table-column prop="operatorName" label="操作人"></el-table-column>
        <el-table-column prop="operateTime" label="操作时间"></el-table-column>
        <el-table-column prop="operateInstance" label="操作事项"></el-table-column>
        <el-table-column prop="comment" label="备注"></el-table-column>
    </el-table>
</div>

<script type="text/javascript">
    let instanceVo = ${instanceVo};
    let vm = new Vue({
        el: '#app',
        data() {
            return {
                instanceVo: instanceVo
            }
        },

        methods: {
            cellStyle({row,column,rowIndex,columnIndex}){
                if (columnIndex === 3){
                    return "color: red";
                }
                return "";
            }
        }
    });
</script>

<style>

</style>
