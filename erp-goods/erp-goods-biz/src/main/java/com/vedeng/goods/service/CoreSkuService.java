package com.vedeng.goods.service;

import com.vedeng.goods.dto.*;
import com.vedeng.goods.query.CoreSkuDetailQuery;
import com.vedeng.goods.query.SearchGoodsQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品SKU_内部接口
 *
 * <AUTHOR>
 */
public interface CoreSkuService {

    /**
     * 搜索基本商品信息
     * @param serachGoodsQuery
     * @return
     */
    SearchGoodsDto searchCoreSkuList(SearchGoodsQuery serachGoodsQuery);

    /**
     * 查询商品信息详情
     *
     * @param coreSkuDetailQuery 查询参数
     * @return CoreSkuQueryDetailDto
     */
    CoreSkuQueryDetailDto queryCoreSkuDetail(CoreSkuDetailQuery coreSkuDetailQuery);

    CoreSkuInfoDto getCoreSkuInfoDtoBySkuNo(String skuNo,Integer customerNature);

    List<CoreSkuInfoDto> getCoreSkuInfoDtoBySkuNo(List<String> skuNo,Integer customerNature);


    CoreSkuInfoDto getCoreSkuInfo(String skuNo);

    List<CoreSkuDto> getInfoBySkuNos(List<String> skuNos);

    /**
     * 批量获取价格
     * @param resultList
     * @param customerNature
     */
    void setSaleSkuPrice(List<CoreSkuInfoDto> resultList,Integer customerNature );
}
