package com.vedeng.erp.kingdee.enums;

/**
 * <AUTHOR>
 * @desc 业务类型枚举类
 */
public enum KingDeeBaseEnums {

    BUY_PAY("采购订单付款"),
    SALES_COLLECTION("销售订单收款"),
    INSTALLATION_COLLECTION("安调维修收款"),
    BUY_EXPENSE_PAY("采购费用付款"),
    INSTALLATION_MAINTENANCE("安调维修付款"),
    SALE_AFTER_BACK_GOOD_PAY("销售售后退货"),
    SALE_AFTER_BACK_REFUND_PAY("销售售后退款"),
    THIRD_AFTER_BACK_REFUND_PAY("第三方售后退款"),
    SALE_AFTER_ENGINEER_PAY("售后工程师付款"),
    BUY_AFTER_BACK_GOOD_SERVICE_PAY("采购售后退货手续费"),
    BUY_AFTER_EXCHANGE_GOOD_SERVICE_PAY("采购售后换货手续费"),
    BUY_AFTER_SETTLE_REFUND("采购售后结款"),
    OTHER_PAY("其他付款");

    /**
     * 业务类型名
     */
    private final String name;

    public String getName() {
        return name;
    }

    KingDeeBaseEnums(String name){
        this.name = name;
    }
}
