package com.wms.inventoryadjustment.model.po;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 库存调整单详情
 *
 * @author: Hugo
 * @date: 2020/7/29 11:16:23
 */
public class InventoryAdjustmentDetailPo implements Serializable {
    /**
     * 库存调整单详情ID
     */
    private Integer inventoryAdjustmentDetailId;

    /**
     * 库存调整单ID
     */
    private Integer inventoryAdjustmentId;

    /**
     * 调整单行号
     */
    private String adjustmentNo;

    /**
     * 订货号
     */
    private String skuNo;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 调整数量
     */
    private BigDecimal num;

    /**
     * 单价(元)
     */
    private BigDecimal price;

    /**
     * 总计（元）
     */
    private BigDecimal totalPrice;

    /**
     * 入库单单号
     */
    private String orderNo;

    /**
     * 逻辑仓ID
     */
    private Integer logicalWarehouseId;

    /**
     * 生产时间
     */
    private Long productionTime;

    /**
     * 有效期至
     */
    private Long validUntilTime;

    /**
     * 入库时间
     */
    private Long warehousingTime;

    /**
     * 厂家批号
     */
    private String manufacturerBatchNo;

    /**
     * 灭菌编号
     */
    private String sterilizationBatchNo;

    /**
     * 注册证ID
     */
    private Integer registrationNumberId;

    /**
     * 注册证号
     */
    private String registrationNumber;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 添加人
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 最后一次编辑人
     */
    private Integer updater;

    /**
     * 贝登批次码
     */
    private String vedengBatchNumer;

    public String getVedengBatchNumer() {
        return vedengBatchNumer;
    }

    public void setVedengBatchNumer(String vedengBatchNumer) {
        this.vedengBatchNumer = vedengBatchNumer;
    }

    public Integer getInventoryAdjustmentDetailId() {
        return inventoryAdjustmentDetailId;
    }

    public void setInventoryAdjustmentDetailId(Integer inventoryAdjustmentDetailId) {
        this.inventoryAdjustmentDetailId = inventoryAdjustmentDetailId;
    }

    public Integer getInventoryAdjustmentId() {
        return inventoryAdjustmentId;
    }

    public void setInventoryAdjustmentId(Integer inventoryAdjustmentId) {
        this.inventoryAdjustmentId = inventoryAdjustmentId;
    }

    public String getSkuNo() {
        return skuNo;
    }

    public void setSkuNo(String skuNo) {
        this.skuNo = skuNo;
    }

    public BigDecimal getNum() {
        return num;
    }

    public void setNum(BigDecimal num) {
        this.num = num;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getLogicalWarehouseId() {
        return logicalWarehouseId;
    }

    public void setLogicalWarehouseId(Integer logicalWarehouseId) {
        this.logicalWarehouseId = logicalWarehouseId;
    }

    public Long getProductionTime() {
        return productionTime;
    }

    public void setProductionTime(Long productionTime) {
        this.productionTime = productionTime;
    }

    public Long getValidUntilTime() {
        return validUntilTime;
    }

    public void setValidUntilTime(Long validUntilTime) {
        this.validUntilTime = validUntilTime;
    }

    public Long getWarehousingTime() {
        return warehousingTime;
    }

    public void setWarehousingTime(Long warehousingTime) {
        this.warehousingTime = warehousingTime;
    }

    public String getManufacturerBatchNo() {
        return manufacturerBatchNo;
    }

    public void setManufacturerBatchNo(String manufacturerBatchNo) {
        this.manufacturerBatchNo = manufacturerBatchNo;
    }

    public String getSterilizationBatchNo() {
        return sterilizationBatchNo;
    }

    public void setSterilizationBatchNo(String sterilizationBatchNo) {
        this.sterilizationBatchNo = sterilizationBatchNo;
    }

    public Integer getRegistrationNumberId() {
        return registrationNumberId;
    }

    public void setRegistrationNumberId(Integer registrationNumberId) {
        this.registrationNumberId = registrationNumberId;
    }

    public Long getAddTime() {
        return addTime;
    }

    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getUpdater() {
        return updater;
    }

    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    public String getRegistrationNumber() {
        return registrationNumber;
    }

    public void setRegistrationNumber(String registrationNumber) {
        this.registrationNumber = registrationNumber;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getAdjustmentNo() {
        return adjustmentNo;
    }

    public void setAdjustmentNo(String adjustmentNo) {
        this.adjustmentNo = adjustmentNo;
    }
}
