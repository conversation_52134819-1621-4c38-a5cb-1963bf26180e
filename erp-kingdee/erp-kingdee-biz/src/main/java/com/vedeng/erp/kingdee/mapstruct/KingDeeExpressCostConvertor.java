package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.kingdee.domain.entity.KingDeeExpressCostEntity;
import com.vedeng.erp.kingdee.dto.KingDeeExpressCostDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;


@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface KingDeeExpressCostConvertor extends BaseMapStruct<KingDeeExpressCostEntity, KingDeeExpressCostDto> {

	/**
     * DTO转Entity
     *
     * @param dto
     * @return
     */
    @Mapping(target = "kingDeeExpressCostId", source = "kingDeeExpressCostId")
    @Mapping(target = "fid", source = "fid")
    @Mapping(target = "fBillNo", source = "FBillNo")
    @Mapping(target = "fQzokOrgId", source = "FQzokOrgId")
    @Mapping(target = "fQzokYsddh", source = "FQzokYsddh")
    @Mapping(target = "fQzokGsywdh", source = "FQzokGsywdh")
    @Mapping(target = "fQzokYwlx", source = "FQzokYwlx")
    @Mapping(target = "fQzokCrkdh", source = "FQzokCrkdh")
    @Mapping(target = "fQzokKddh", source = "FQzokKddh")
    @Mapping(target = "fQzokWlbm", source = "FQzokWlbm")
    @Mapping(target = "fQzokXlh", source = "FQzokXlh")
    @Mapping(target = "fQzokPch", source = "FQzokPch")
    @Mapping(target = "fQzokFhsl", source = "FQzokFhsl")
    @Mapping(target = "fQzokCb", source = "FQzokCb")
    @Mapping(target = "fQzokSjr", source = "FQzokSjr")
    @Mapping(target = "fQzokDh", source = "FQzokDh")
    @Mapping(target = "fQzokDz", source = "FQzokDz")
    @Mapping(target = "fQzokBddjbh", source = "FQzokBddjbh")
    @Mapping(target = "fQzokSfsc", source = "FQzokSfsc")
    @Mapping(target = "fQzokSfjrcb", source = "FQzokSfjrcb")
    @Mapping(target = "fQzokWlgs", source = "FQzokWlgs")
    @Mapping(target = "fQzokSfzp", source = "FQzokSfzp")
    @Override
    KingDeeExpressCostEntity toEntity(KingDeeExpressCostDto dto);

    /**
     * Entity转DTO
     *
     * @param entity
     * @return
     */
    @Mapping(target = "kingDeeExpressCostId", source = "kingDeeExpressCostId")
    @Mapping(target = "fid", source = "fid")
    @Mapping(target = "fBillNo", source = "FBillNo")
    @Mapping(target = "fQzokOrgId", source = "FQzokOrgId")
    @Mapping(target = "fQzokYsddh", source = "FQzokYsddh")
    @Mapping(target = "fQzokGsywdh", source = "FQzokGsywdh")
    @Mapping(target = "fQzokYwlx", source = "FQzokYwlx")
    @Mapping(target = "fQzokCrkdh", source = "FQzokCrkdh")
    @Mapping(target = "fQzokKddh", source = "FQzokKddh")
    @Mapping(target = "fQzokWlbm", source = "FQzokWlbm")
    @Mapping(target = "fQzokXlh", source = "FQzokXlh")
    @Mapping(target = "fQzokPch", source = "FQzokPch")
    @Mapping(target = "fQzokFhsl", source = "FQzokFhsl")
    @Mapping(target = "fQzokCb", source = "FQzokCb")
    @Mapping(target = "fQzokSjr", source = "FQzokSjr")
    @Mapping(target = "fQzokDh", source = "FQzokDh")
    @Mapping(target = "fQzokDz", source = "FQzokDz")
    @Mapping(target = "fQzokBddjbh", source = "FQzokBddjbh")
    @Mapping(target = "fQzokSfsc", source = "FQzokSfsc")
    @Mapping(target = "fQzokSfjrcb", source = "FQzokSfjrcb")
    @Mapping(target = "FQzokWlgs", source = "FQzokWlgs")
    @Mapping(target = "FQzokSfzp", source = "FQzokSfzp")
    @Override
    KingDeeExpressCostDto toDto(KingDeeExpressCostEntity entity);

    
}
