package com.vedeng.erp.system.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 自定义标签，按目前设计：标签领域能力仅属于线索和商机模块，后期可扩展到其他领域
 * @date 2022/7/9 10:33
 */
@Getter
@Setter
public class CustomTagEntity extends BaseEntity {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 标签类型（1:线索 2:商机）
     */
    private Integer type;

    /**
     * css样式
     */
    private String cssClass;

    /**
     * 归属人id
     */
    private Integer belongerId;

    /**
     * 是否默认
     */
    private Boolean defaultFlag;

}
