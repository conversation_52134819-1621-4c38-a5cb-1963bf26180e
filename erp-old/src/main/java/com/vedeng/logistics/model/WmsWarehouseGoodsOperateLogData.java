package com.vedeng.logistics.model;

import java.util.Date;

public class WmsWarehouseGoodsOperateLogData {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.ID
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.WAREHOUSE_GOODS_OPERATE_LOG_ID
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    private Integer warehouseGoodsOperateLogId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.OPERATE_TYPE
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    private Integer operateType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.ORDER_ID
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    private Integer orderId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.RELATED_ID
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    private Integer relatedId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.BUSINESS_NO
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    private String businessNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.TRADER_ID
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    private Integer traderId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.TRADER_NAME
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    private String traderName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.USER_NAME
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    private String userName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.DEPT_NAME
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    private String deptName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.SKU_NO
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    private String skuNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.SKU_NAME
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    private String skuName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.SKU_MODEL
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    private String skuModel;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.SKU_SPEC
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    private String skuSpec;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.SKU_NUM
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    private Integer skuNum;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.REGISTER_NUMBER
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    private String registerNumber;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.BRAND_NAME
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    private String brandName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.BARCODE_FACTORY
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    private String barcodeFactory;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.BATCH_NUMBER
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    private String batchNumber;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.VEDENG_BATCH_NUMER
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    private String vedengBatchNumer;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.PRODUCT_DATE
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    private Date productDate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.EXPIRATION_DATE
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    private Date expirationDate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.EXPRESS_NOS
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    private String expressNos;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.QUALITY_REPORT
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    private String qualityReport;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.QUALITY_REPORT_OSS
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    private String qualityReportOss;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.QUALITY_REPORT_SIZE
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    private Integer qualityReportSize;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.CREATOR
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    private Integer creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.UPDATER
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    private Integer updater;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.ADD_TIME
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    private Date addTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.MODE_TIME
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    private Date modeTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.IS_DELETE
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    private Byte isDelete;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.OUT_TIME
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    private Date outTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.STERILZATION_BATCH_NUMBER
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    private String sterilzationBatchNumber;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.ID
     *
     * @return the value of T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.ID
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.ID
     *
     * @param id the value for T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.ID
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.WAREHOUSE_GOODS_OPERATE_LOG_ID
     *
     * @return the value of T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.WAREHOUSE_GOODS_OPERATE_LOG_ID
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public Integer getWarehouseGoodsOperateLogId() {
        return warehouseGoodsOperateLogId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.WAREHOUSE_GOODS_OPERATE_LOG_ID
     *
     * @param warehouseGoodsOperateLogId the value for T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.WAREHOUSE_GOODS_OPERATE_LOG_ID
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setWarehouseGoodsOperateLogId(Integer warehouseGoodsOperateLogId) {
        this.warehouseGoodsOperateLogId = warehouseGoodsOperateLogId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.OPERATE_TYPE
     *
     * @return the value of T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.OPERATE_TYPE
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public Integer getOperateType() {
        return operateType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.OPERATE_TYPE
     *
     * @param operateType the value for T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.OPERATE_TYPE
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setOperateType(Integer operateType) {
        this.operateType = operateType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.ORDER_ID
     *
     * @return the value of T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.ORDER_ID
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public Integer getOrderId() {
        return orderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.ORDER_ID
     *
     * @param orderId the value for T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.ORDER_ID
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.RELATED_ID
     *
     * @return the value of T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.RELATED_ID
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public Integer getRelatedId() {
        return relatedId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.RELATED_ID
     *
     * @param relatedId the value for T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.RELATED_ID
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setRelatedId(Integer relatedId) {
        this.relatedId = relatedId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.BUSINESS_NO
     *
     * @return the value of T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.BUSINESS_NO
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public String getBusinessNo() {
        return businessNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.BUSINESS_NO
     *
     * @param businessNo the value for T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.BUSINESS_NO
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setBusinessNo(String businessNo) {
        this.businessNo = businessNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.TRADER_ID
     *
     * @return the value of T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.TRADER_ID
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public Integer getTraderId() {
        return traderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.TRADER_ID
     *
     * @param traderId the value for T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.TRADER_ID
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setTraderId(Integer traderId) {
        this.traderId = traderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.TRADER_NAME
     *
     * @return the value of T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.TRADER_NAME
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public String getTraderName() {
        return traderName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.TRADER_NAME
     *
     * @param traderName the value for T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.TRADER_NAME
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setTraderName(String traderName) {
        this.traderName = traderName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.USER_NAME
     *
     * @return the value of T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.USER_NAME
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public String getUserName() {
        return userName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.USER_NAME
     *
     * @param userName the value for T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.USER_NAME
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setUserName(String userName) {
        this.userName = userName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.DEPT_NAME
     *
     * @return the value of T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.DEPT_NAME
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public String getDeptName() {
        return deptName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.DEPT_NAME
     *
     * @param deptName the value for T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.DEPT_NAME
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.SKU_NO
     *
     * @return the value of T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.SKU_NO
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public String getSkuNo() {
        return skuNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.SKU_NO
     *
     * @param skuNo the value for T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.SKU_NO
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setSkuNo(String skuNo) {
        this.skuNo = skuNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.SKU_NAME
     *
     * @return the value of T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.SKU_NAME
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public String getSkuName() {
        return skuName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.SKU_NAME
     *
     * @param skuName the value for T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.SKU_NAME
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.SKU_MODEL
     *
     * @return the value of T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.SKU_MODEL
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public String getSkuModel() {
        return skuModel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.SKU_MODEL
     *
     * @param skuModel the value for T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.SKU_MODEL
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setSkuModel(String skuModel) {
        this.skuModel = skuModel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.SKU_SPEC
     *
     * @return the value of T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.SKU_SPEC
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public String getSkuSpec() {
        return skuSpec;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.SKU_SPEC
     *
     * @param skuSpec the value for T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.SKU_SPEC
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setSkuSpec(String skuSpec) {
        this.skuSpec = skuSpec;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.SKU_NUM
     *
     * @return the value of T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.SKU_NUM
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public Integer getSkuNum() {
        return skuNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.SKU_NUM
     *
     * @param skuNum the value for T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.SKU_NUM
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setSkuNum(Integer skuNum) {
        this.skuNum = skuNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.REGISTER_NUMBER
     *
     * @return the value of T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.REGISTER_NUMBER
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public String getRegisterNumber() {
        return registerNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.REGISTER_NUMBER
     *
     * @param registerNumber the value for T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.REGISTER_NUMBER
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setRegisterNumber(String registerNumber) {
        this.registerNumber = registerNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.BRAND_NAME
     *
     * @return the value of T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.BRAND_NAME
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public String getBrandName() {
        return brandName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.BRAND_NAME
     *
     * @param brandName the value for T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.BRAND_NAME
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.BARCODE_FACTORY
     *
     * @return the value of T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.BARCODE_FACTORY
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public String getBarcodeFactory() {
        return barcodeFactory;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.BARCODE_FACTORY
     *
     * @param barcodeFactory the value for T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.BARCODE_FACTORY
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setBarcodeFactory(String barcodeFactory) {
        this.barcodeFactory = barcodeFactory;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.BATCH_NUMBER
     *
     * @return the value of T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.BATCH_NUMBER
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public String getBatchNumber() {
        return batchNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.BATCH_NUMBER
     *
     * @param batchNumber the value for T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.BATCH_NUMBER
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setBatchNumber(String batchNumber) {
        this.batchNumber = batchNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.VEDENG_BATCH_NUMER
     *
     * @return the value of T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.VEDENG_BATCH_NUMER
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public String getVedengBatchNumer() {
        return vedengBatchNumer;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.VEDENG_BATCH_NUMER
     *
     * @param vedengBatchNumer the value for T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.VEDENG_BATCH_NUMER
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setVedengBatchNumer(String vedengBatchNumer) {
        this.vedengBatchNumer = vedengBatchNumer;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.PRODUCT_DATE
     *
     * @return the value of T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.PRODUCT_DATE
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public Date getProductDate() {
        return productDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.PRODUCT_DATE
     *
     * @param productDate the value for T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.PRODUCT_DATE
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setProductDate(Date productDate) {
        this.productDate = productDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.EXPIRATION_DATE
     *
     * @return the value of T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.EXPIRATION_DATE
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public Date getExpirationDate() {
        return expirationDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.EXPIRATION_DATE
     *
     * @param expirationDate the value for T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.EXPIRATION_DATE
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setExpirationDate(Date expirationDate) {
        this.expirationDate = expirationDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.EXPRESS_NOS
     *
     * @return the value of T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.EXPRESS_NOS
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public String getExpressNos() {
        return expressNos;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.EXPRESS_NOS
     *
     * @param expressNos the value for T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.EXPRESS_NOS
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setExpressNos(String expressNos) {
        this.expressNos = expressNos;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.QUALITY_REPORT
     *
     * @return the value of T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.QUALITY_REPORT
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public String getQualityReport() {
        return qualityReport;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.QUALITY_REPORT
     *
     * @param qualityReport the value for T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.QUALITY_REPORT
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setQualityReport(String qualityReport) {
        this.qualityReport = qualityReport;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.QUALITY_REPORT_OSS
     *
     * @return the value of T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.QUALITY_REPORT_OSS
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public String getQualityReportOss() {
        return qualityReportOss;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.QUALITY_REPORT_OSS
     *
     * @param qualityReportOss the value for T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.QUALITY_REPORT_OSS
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setQualityReportOss(String qualityReportOss) {
        this.qualityReportOss = qualityReportOss;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.QUALITY_REPORT_SIZE
     *
     * @return the value of T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.QUALITY_REPORT_SIZE
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public Integer getQualityReportSize() {
        return qualityReportSize;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.QUALITY_REPORT_SIZE
     *
     * @param qualityReportSize the value for T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.QUALITY_REPORT_SIZE
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setQualityReportSize(Integer qualityReportSize) {
        this.qualityReportSize = qualityReportSize;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.CREATOR
     *
     * @return the value of T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.CREATOR
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public Integer getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.CREATOR
     *
     * @param creator the value for T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.CREATOR
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.UPDATER
     *
     * @return the value of T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.UPDATER
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public Integer getUpdater() {
        return updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.UPDATER
     *
     * @param updater the value for T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.UPDATER
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.ADD_TIME
     *
     * @return the value of T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.ADD_TIME
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public Date getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.ADD_TIME
     *
     * @param addTime the value for T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.ADD_TIME
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.MODE_TIME
     *
     * @return the value of T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.MODE_TIME
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public Date getModeTime() {
        return modeTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.MODE_TIME
     *
     * @param modeTime the value for T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.MODE_TIME
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setModeTime(Date modeTime) {
        this.modeTime = modeTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.IS_DELETE
     *
     * @return the value of T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.IS_DELETE
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public Byte getIsDelete() {
        return isDelete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.IS_DELETE
     *
     * @param isDelete the value for T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.IS_DELETE
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setIsDelete(Byte isDelete) {
        this.isDelete = isDelete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.OUT_TIME
     *
     * @return the value of T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.OUT_TIME
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public Date getOutTime() {
        return outTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.OUT_TIME
     *
     * @param outTime the value for T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.OUT_TIME
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setOutTime(Date outTime) {
        this.outTime = outTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.STERILZATION_BATCH_NUMBER
     *
     * @return the value of T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.STERILZATION_BATCH_NUMBER
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public String getSterilzationBatchNumber() {
        return sterilzationBatchNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.STERILZATION_BATCH_NUMBER
     *
     * @param sterilzationBatchNumber the value for T_WAREHOUSE_GOODS_OPERATE_LOG_DATA.STERILZATION_BATCH_NUMBER
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setSterilzationBatchNumber(String sterilzationBatchNumber) {
        this.sterilzationBatchNumber = sterilzationBatchNumber;
    }
}