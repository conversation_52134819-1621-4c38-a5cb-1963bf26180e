package com.smallhospital.dto;

public class ELSkuDetailInfo extends ELSkuBasicInfo {

    private String approvalNumber;
    private Integer registrationNumberId;
    private String productionAddress;
    private String registrationCertificateProductName;
    private String majorComponent;
    private String intendedUse;

    private String validityDate;
    private String validityDateStart;
    private String validityDateEnd;
    private String crtificatePicAddress;
    private String companyName;

    private String companyAbbr;
    private String uniformCreditCode;
    private String enterpriseLicenseRecordNumber;
    private String registeredAddress;
    private String manufacturerId;

    private String terminalPrice;

    private Long issuingDate;
    private Long effectiveDate;

    public String getApprovalNumber() {
        return approvalNumber;
    }

    public void setApprovalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber;
    }

    public String getProductionAddress() {
        return productionAddress;
    }

    public void setProductionAddress(String productionAddress) {
        this.productionAddress = productionAddress;
    }

    public String getRegistrationCertificateProductName() {
        return registrationCertificateProductName;
    }

    public void setRegistrationCertificateProductName(String registrationCertificateProductName) {
        this.registrationCertificateProductName = registrationCertificateProductName;
    }

    public String getMajorComponent() {
        return majorComponent;
    }

    public void setMajorComponent(String majorComponent) {
        this.majorComponent = majorComponent;
    }

    public String getIntendedUse() {
        return intendedUse;
    }

    public void setIntendedUse(String intendedUse) {
        this.intendedUse = intendedUse;
    }

    public String getValidityDate() {
        return validityDate;
    }

    public void setValidityDate(String validityDate) {
        this.validityDate = validityDate;
    }

    public String getValidityDateStart() {
        return validityDateStart;
    }

    public void setValidityDateStart(String validityDateStart) {
        this.validityDateStart = validityDateStart;
    }

    public String getValidityDateEnd() {
        return validityDateEnd;
    }

    public void setValidityDateEnd(String validityDateEnd) {
        this.validityDateEnd = validityDateEnd;
    }

    public String getCrtificatePicAddress() {
        return crtificatePicAddress;
    }

    public void setCrtificatePicAddress(String crtificatePicAddress) {
        this.crtificatePicAddress = crtificatePicAddress;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyAbbr() {
        return companyAbbr;
    }

    public void setCompanyAbbr(String companyAbbr) {
        this.companyAbbr = companyAbbr;
    }

    public String getUniformCreditCode() {
        return uniformCreditCode;
    }

    public void setUniformCreditCode(String uniformCreditCode) {
        this.uniformCreditCode = uniformCreditCode;
    }

    public String getEnterpriseLicenseRecordNumber() {
        return enterpriseLicenseRecordNumber;
    }

    public void setEnterpriseLicenseRecordNumber(String enterpriseLicenseRecordNumber) {
        this.enterpriseLicenseRecordNumber = enterpriseLicenseRecordNumber;
    }

    public String getRegisteredAddress() {
        return registeredAddress;
    }

    public void setRegisteredAddress(String registeredAddress) {
        this.registeredAddress = registeredAddress;
    }

    public String getManufacturerId() {
        return manufacturerId;
    }

    public void setManufacturerId(String manufacturerId) {
        this.manufacturerId = manufacturerId;
    }

    public Integer getRegistrationNumberId() {
        return registrationNumberId;
    }

    public void setRegistrationNumberId(Integer registrationNumberId) {
        this.registrationNumberId = registrationNumberId;
    }

    public Long getIssuingDate() {
        return issuingDate;
    }

    public void setIssuingDate(Long issuingDate) {
        this.issuingDate = issuingDate;
    }

    public Long getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(Long effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public String getTerminalPrice() {
        return terminalPrice;
    }

    public void setTerminalPrice(String terminalPrice) {
        this.terminalPrice = terminalPrice;
    }
}
