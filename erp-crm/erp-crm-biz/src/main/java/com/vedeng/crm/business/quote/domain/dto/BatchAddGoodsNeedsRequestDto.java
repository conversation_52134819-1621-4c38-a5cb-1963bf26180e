package com.vedeng.crm.business.quote.domain.dto;

import com.vedeng.common.core.utils.validator.group.DefaultGroup;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class BatchAddGoodsNeedsRequestDto {

    /**
     * 报价ID
     */
    @NotNull(message = "quoteorderId不能为空", groups = DefaultGroup.class)
    private Integer quoteorderId;

    /**
     * 客户需求id
     */
    private Long quoteorderNeedsId;

    /**
     * 最新sku
     */
    @NotNull(message = "SKU不能为空", groups = DefaultGroup.class)
    private List<String> skuNos;

}
