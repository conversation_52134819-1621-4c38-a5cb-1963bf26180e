<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="goodsList" language="groovy" printOrder="Horizontal" pageWidth="3272" pageHeight="635" orientation="Landscape" columnWidth="3272" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" whenResourceMissingType="Empty" isIgnorePagination="true" uuid="4e52887f-2521-4218-a53d-9416a548937f">
	<property name="ireport.zoom" value="1.3310000000000282"/>
	<property name="ireport.x" value="3361"/>
	<property name="ireport.y" value="0"/>
	<style name="style_detail_left" hAlign="Left" vAlign="Middle" isBlankWhenNull="true" fontName="宋体" fontSize="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H">
		<box>
			<pen lineWidth="0.5"/>
			<topPen lineWidth="0.5"/>
			<leftPen lineWidth="0.5"/>
			<bottomPen lineWidth="0.5"/>
			<rightPen lineWidth="0.5"/>
		</box>
	</style>
	<style name="style_detail_center" hAlign="Center" vAlign="Middle" fontName="SansSerif" fontSize="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H">
		<box>
			<pen lineWidth="0.5"/>
			<topPen lineWidth="0.5"/>
			<leftPen lineWidth="0.5"/>
			<bottomPen lineWidth="0.5"/>
			<rightPen lineWidth="0.5"/>
		</box>
	</style>
	<style name="style_title_name" hAlign="Center" vAlign="Middle" fontName="宋体" fontSize="13" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H">
		<box>
			<pen lineWidth="0.5"/>
			<topPen lineWidth="0.5"/>
			<leftPen lineWidth="0.5"/>
			<bottomPen lineWidth="0.5"/>
			<rightPen lineWidth="0.5"/>
		</box>
	</style>
	<style name="style_title_value" hAlign="Left" vAlign="Middle" isBlankWhenNull="true" fontName="宋体" fontSize="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H">
		<box>
			<pen lineWidth="0.5"/>
			<topPen lineWidth="0.5"/>
			<leftPen lineWidth="0.5"/>
			<bottomPen lineWidth="0.5"/>
			<rightPen lineWidth="0.5"/>
		</box>
	</style>
	<style name="style_detail_right" hAlign="Right" vAlign="Middle" fontName="宋体" fontSize="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H">
		<box>
			<pen lineWidth="0.5"/>
			<topPen lineWidth="0.5"/>
			<leftPen lineWidth="0.5"/>
			<bottomPen lineWidth="0.5"/>
			<rightPen lineWidth="0.5"/>
		</box>
	</style>
	<style name="style_title_center" backcolor="#00FF00" hAlign="Center" vAlign="Middle" fontName="SansSerif" fontSize="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H">
		<box>
			<pen lineWidth="0.5"/>
			<topPen lineWidth="0.5"/>
			<leftPen lineWidth="0.5"/>
			<bottomPen lineWidth="0.5"/>
			<rightPen lineWidth="0.5"/>
		</box>
	</style>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="saleorderNo" class="java.lang.String"/>
	<field name="traderName" class="java.lang.String"/>
	<field name="addTimeStr" class="java.lang.String"/>
	<field name="statusStr" class="java.lang.String"/>
	<field name="validTimeStr" class="java.lang.String"/>
	<field name="saleUsername" class="java.lang.String"/>
	<field name="departmentTwo" class="java.lang.String"/>
	<field name="departmentThree" class="java.lang.String"/>
	<field name="paymentStatusStr" class="java.lang.String"/>
	<field name="deliveryStatusStr" class="java.lang.String"/>
	<field name="arrivalStatusStr" class="java.lang.String"/>
	<field name="totalAmount" class="java.lang.String"/>
	<field name="costAmount" class="java.lang.String"/>
	<field name="realReceiveAmount" class="java.math.BigDecimal"/>
	<field name="realOrderAmount" class="java.math.BigDecimal"/>
	<field name="sku" class="java.lang.String"/>
	<field name="goodsName" class="java.lang.String"/>
	<field name="brandName" class="java.lang.String"/>
	<field name="model" class="java.lang.String"/>
	<field name="price" class="java.lang.String"/>
	<field name="num" class="java.lang.String"/>
	<field name="referenceCostPrice" class="java.lang.String"/>
	<field name="goodsDeliveryStatusStr" class="java.lang.String"/>
	<field name="goodsArrivalStatusStr" class="java.lang.String"/>
	<field name="salesPerformanceModTimeStr" class="java.lang.String"/>
	<columnHeader>
		<band height="25" splitType="Stretch">
			<frame>
				<reportElement style="style_detail_center" mode="Opaque" x="0" y="0" width="3272" height="25" backcolor="#99CCFF" uuid="a144a4a6-65db-4f80-9384-bbbdad2c8cea">
					<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
				</reportElement>
				<box topPadding="0" bottomPadding="0">
					<pen lineWidth="0.3" lineStyle="Solid"/>
					<topPen lineWidth="0.3" lineStyle="Solid"/>
					<leftPen lineWidth="0.3" lineStyle="Solid"/>
					<bottomPen lineWidth="0.3" lineStyle="Solid"/>
					<rightPen lineWidth="0.3" lineStyle="Solid"/>
				</box>
				<staticText>
					<reportElement style="style_detail_center" x="0" y="0" width="120" height="25" forecolor="#000000" backcolor="#0099FF" uuid="18cedb62-bdd4-46f9-b1a8-270ff18d300a">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
						<paragraph tabStopWidth="30"/>
					</textElement>
					<text><![CDATA[订单号]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="120" y="0" width="230" height="25" backcolor="#0099FF" uuid="71432a89-05dd-4b4d-827f-d27dfec4ab6a">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[客户名称]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="350" y="0" width="140" height="25" backcolor="#0099FF" uuid="51e32d51-563a-4747-ab56-28951ec3a1e5">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[创建时间]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="630" y="0" width="140" height="25" backcolor="#0099FF" uuid="bea791f6-763b-41e8-8672-a64be45165ed">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[生效时间]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="770" y="0" width="88" height="25" backcolor="#0099FF" uuid="039277e6-dd25-48ac-b57e-3eea0a4763a8">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[归属人销售]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="1688" y="0" width="80" height="25" backcolor="#0099FF" uuid="cb6de4ce-0392-45fe-81e5-c5013295ed74">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[订单成本]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="1768" y="0" width="145" height="25" backcolor="#0099FF" uuid="c8461bf1-e0d2-4267-accf-16208f238993">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[订单到款金额]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="2113" y="0" width="320" height="25" backcolor="#0099FF" uuid="56dac134-7307-4a7f-ac87-d94aa9ff6cd0">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
						<paragraph tabStopWidth="30"/>
					</textElement>
					<text><![CDATA[产品名称]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="2033" y="0" width="80" height="25" backcolor="#0099FF" uuid="3a00f76f-ed2b-4150-9a78-2e4f4c96c83d">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[sku]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="490" y="0" width="140" height="25" backcolor="#0099FF" uuid="7495d6ab-f425-4989-a0d0-eb7af1c27b45">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[订单状态]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="1913" y="0" width="120" height="25" backcolor="#0099FF" uuid="865a6a9e-0e67-4899-8343-d2703c876773">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[订单实际金额]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="2433" y="0" width="119" height="25" backcolor="#0099FF" uuid="1ee94444-4f9d-4606-a810-5bd1799b36f5">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[品牌]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="2552" y="0" width="150" height="25" backcolor="#0099FF" uuid="c5fd4dc6-02b8-4f40-8fe6-ebab5ed6e246">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[型号]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="2702" y="0" width="150" height="25" backcolor="#0099FF" uuid="eb110458-2796-466f-bdc8-dd504178a9d2">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[销售价格]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="2852" y="0" width="64" height="25" backcolor="#0099FF" uuid="e0c45fcf-104f-4ae2-9a5b-30810099c578">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[数量]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="2916" y="0" width="90" height="25" backcolor="#0099FF" uuid="081330ff-2277-41d9-983e-3f4e703b3bc4">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[产品部手填成本价]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="858" y="0" width="126" height="25" backcolor="#0099FF" uuid="1ea42a18-52cd-4114-9086-ee0761c5ba03">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[二级部]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="984" y="0" width="154" height="25" backcolor="#0099FF" uuid="6e82d64f-86eb-468c-b901-e5bfbc803c53">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[三级部]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="1138" y="0" width="154" height="25" backcolor="#0099FF" uuid="06927936-cb50-4484-aaa2-ba6ccb0184e1">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[收款状态]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="1292" y="0" width="140" height="25" backcolor="#0099FF" uuid="01a736ef-ea9e-4eed-a867-0bb119bca612">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[订单发货状态]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="1432" y="0" width="131" height="25" backcolor="#0099FF" uuid="a3683514-1a07-4be3-aa1e-f76736d276be">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[订单收货状态]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="1563" y="0" width="125" height="25" backcolor="#0099FF" uuid="91fc981e-f220-4ebf-aaf9-e9c309d181a5">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[订单原始金额]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="3006" y="0" width="90" height="25" backcolor="#0099FF" uuid="239f5467-2a4c-450c-86b6-73401c6bb1d2">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[产品发货状态]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="3096" y="0" width="86" height="25" backcolor="#0099FF" uuid="d94a6de3-bf31-4ecf-8eaf-db11218200d0">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[产品收货状态]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="3182" y="0" width="90" height="25" backcolor="#0099FF" uuid="d75c32c3-decb-4c35-855a-1b52ad082eb5">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[业绩计算时间]]></text>
				</staticText>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="20" splitType="Stretch">
			<frame>
				<reportElement mode="Opaque" x="0" y="0" width="3272" height="20" backcolor="#FFFFFF" uuid="ff4f5f4f-741b-485d-b65c-040bc8627a9e">
					<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
				</reportElement>
				<box topPadding="0" bottomPadding="0">
					<pen lineWidth="0.3" lineStyle="Solid"/>
					<topPen lineWidth="0.3" lineStyle="Solid"/>
					<leftPen lineWidth="0.3" lineStyle="Solid"/>
					<bottomPen lineWidth="0.3" lineStyle="Solid"/>
					<rightPen lineWidth="0.3" lineStyle="Solid"/>
				</box>
				<textField isBlankWhenNull="true">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="0" y="0" width="120" height="20" uuid="ad9f938d-cc40-4433-a518-9da3e96b2997">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{saleorderNo}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="120" y="0" width="230" height="20" uuid="6dd9afab-7e34-42f5-b6e2-94ef768043ed">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{traderName}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="350" y="0" width="140" height="20" uuid="451aeb2a-cc72-401a-b61a-97a3c93e9f7b">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{addTimeStr}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="490" y="0" width="140" height="20" uuid="f4870aec-d05d-4b02-96df-00681e002472">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{statusStr}.equals( '0' )?'待确认':($F{statusStr}.equals( '1' )?'进行中':($F{statusStr}.equals( '2' )?'已完结':($F{statusStr}.equals( '3' )?'已关闭':'')))]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="770" y="0" width="88" height="20" uuid="d5105eb2-90fd-4e06-9958-e1d4ce1c6189">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{saleUsername}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="1688" y="0" width="80" height="20" uuid="f9c5610b-b456-432d-965f-24b0c0195588">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{costAmount}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="1768" y="0" width="145" height="20" uuid="e8ad7409-7951-4715-9f9b-6c52db6658f0">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{realReceiveAmount}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="1913" y="0" width="120" height="20" uuid="81bc1297-ee04-4458-86d5-8802bdebc9f8">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{realOrderAmount}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="2033" y="0" width="80" height="20" uuid="997d775b-7419-45ee-9c91-ff8065564018">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{sku}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="2113" y="0" width="320" height="20" uuid="5dc327cc-2c04-4351-a391-af520e6e8476">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{goodsName}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="630" y="0" width="140" height="20" uuid="f49fe5d4-a770-4a24-b2c2-aa5b0382c4d6">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{validTimeStr}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="2433" y="0" width="119" height="20" uuid="89c077f0-6c6d-49d9-8b8e-f44fa4560b4e">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{brandName}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="2552" y="0" width="150" height="20" uuid="14d21e38-4be4-445e-8c41-7d36c550ae93">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{model}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="2702" y="0" width="150" height="20" uuid="85d882c1-7f16-47d0-b37d-9b5fa3fa73d4">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{price}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="2852" y="0" width="64" height="20" uuid="53cdb931-2445-4a20-8537-ad150e3cdfd7">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{num}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="2916" y="0" width="90" height="20" uuid="3ff40462-accf-48af-a828-630287fad09b">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{referenceCostPrice}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="858" y="0" width="126" height="20" uuid="9ed566c2-71c6-4f66-9811-66986c54a671">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{departmentTwo}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="984" y="0" width="154" height="20" uuid="d15d2f59-1cb0-43c8-bd10-66ec3e9330b8">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{departmentThree}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="1138" y="0" width="154" height="20" uuid="ae601bc6-28f4-47d1-ba69-205bf4715792">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{paymentStatusStr}.equals( '0' )?'未收款':($F{paymentStatusStr}.equals( '1' )?'部分收款':($F{paymentStatusStr}.equals( '2' )?'全部收款':''))]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="1292" y="0" width="140" height="20" uuid="aff50ddb-d4d3-4d7b-b99c-ffe9b63f45f0">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{deliveryStatusStr}.equals( '0' )?'未发货':($F{deliveryStatusStr}.equals( '1' )?'部分发货':($F{deliveryStatusStr}.equals( '2' )?'全部发货':''))]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="1432" y="0" width="131" height="20" uuid="50df3aaa-f52c-46f0-92a0-3850a60c488b">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{arrivalStatusStr}.equals( '0' )?'未收货':($F{arrivalStatusStr}.equals( '1' )?'部分收货':($F{arrivalStatusStr}.equals( '2' )?'全部收货':''))]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="1563" y="0" width="125" height="20" uuid="a55e0784-b0c6-49c3-9a0d-29ae375e0b69">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{totalAmount}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="3006" y="0" width="90" height="20" uuid="656831e5-397f-4d5d-b055-dd376508b43e">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{goodsDeliveryStatusStr}.equals( '0' )?'未发货':($F{goodsDeliveryStatusStr}.equals( '1' )?'部分发货':($F{goodsDeliveryStatusStr}.equals( '2' )?'全部发货':''))]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="3182" y="0" width="90" height="20" uuid="a9002cb6-8f85-48d6-9d42-0320e545bf63">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{salesPerformanceModTimeStr}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="3096" y="0" width="86" height="20" uuid="fbaf2237-46cd-4f77-9dd0-7d239b5fcc19">
						<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					</reportElement>
					<box topPadding="0" bottomPadding="0">
						<pen lineWidth="0.3" lineStyle="Solid"/>
						<topPen lineWidth="0.3" lineStyle="Solid"/>
						<leftPen lineWidth="0.3" lineStyle="Solid"/>
						<bottomPen lineWidth="0.3" lineStyle="Solid"/>
						<rightPen lineWidth="0.3" lineStyle="Solid"/>
					</box>
					<textElement>
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{goodsArrivalStatusStr}.equals( '0' )?'未收货':($F{goodsArrivalStatusStr}.equals( '1' )?'部分收货':($F{goodsArrivalStatusStr}.equals( '2' )?'全部收货':''))]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
</jasperReport>
