package com.vedeng.erp.trader.domain.dto;

import lombok.Data;

/**
 * 客户信息运输对象
 *
 * <AUTHOR>
 */
@Data
public class TraderCustomerDto {

    /**
     * 客户ID
     */
    private Integer traderCustomerId;

    /**
     * 交易信息ID
     */
    private Integer traderId;

    /**
     * 客户名称
     */
    private String traderName;

    /**
     * 地区
     */
    private String address;

    /**
     * 归属销售
     */
    private Integer userId;

    /**
     * 归属销售名称
     */
    private String userName;

    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 关联的客户分组ID
     */
    private Long associatedCustomerGroup;
}
