package com.vedeng.orderstream.aftersales.model;

import java.util.ArrayList;
import java.util.List;

public class RInstallstionJGoodsExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_R_INSTALLSTION_J_GOODS
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_R_INSTALLSTION_J_GOODS
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_R_INSTALLSTION_J_GOODS
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_R_INSTALLSTION_J_GOODS
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    public RInstallstionJGoodsExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_R_INSTALLSTION_J_GOODS
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_R_INSTALLSTION_J_GOODS
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_R_INSTALLSTION_J_GOODS
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_R_INSTALLSTION_J_GOODS
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_R_INSTALLSTION_J_GOODS
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_R_INSTALLSTION_J_GOODS
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_R_INSTALLSTION_J_GOODS
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_R_INSTALLSTION_J_GOODS
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_R_INSTALLSTION_J_GOODS
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_R_INSTALLSTION_J_GOODS
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_R_INSTALLSTION_J_GOODS
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andRInstallstionJGoodsIdIsNull() {
            addCriterion("R_INSTALLSTION_J_GOODS_ID is null");
            return (Criteria) this;
        }

        public Criteria andRInstallstionJGoodsIdIsNotNull() {
            addCriterion("R_INSTALLSTION_J_GOODS_ID is not null");
            return (Criteria) this;
        }

        public Criteria andRInstallstionJGoodsIdEqualTo(Integer value) {
            addCriterion("R_INSTALLSTION_J_GOODS_ID =", value, "rInstallstionJGoodsId");
            return (Criteria) this;
        }

        public Criteria andRInstallstionJGoodsIdNotEqualTo(Integer value) {
            addCriterion("R_INSTALLSTION_J_GOODS_ID <>", value, "rInstallstionJGoodsId");
            return (Criteria) this;
        }

        public Criteria andRInstallstionJGoodsIdGreaterThan(Integer value) {
            addCriterion("R_INSTALLSTION_J_GOODS_ID >", value, "rInstallstionJGoodsId");
            return (Criteria) this;
        }

        public Criteria andRInstallstionJGoodsIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("R_INSTALLSTION_J_GOODS_ID >=", value, "rInstallstionJGoodsId");
            return (Criteria) this;
        }

        public Criteria andRInstallstionJGoodsIdLessThan(Integer value) {
            addCriterion("R_INSTALLSTION_J_GOODS_ID <", value, "rInstallstionJGoodsId");
            return (Criteria) this;
        }

        public Criteria andRInstallstionJGoodsIdLessThanOrEqualTo(Integer value) {
            addCriterion("R_INSTALLSTION_J_GOODS_ID <=", value, "rInstallstionJGoodsId");
            return (Criteria) this;
        }

        public Criteria andRInstallstionJGoodsIdIn(List<Integer> values) {
            addCriterion("R_INSTALLSTION_J_GOODS_ID in", values, "rInstallstionJGoodsId");
            return (Criteria) this;
        }

        public Criteria andRInstallstionJGoodsIdNotIn(List<Integer> values) {
            addCriterion("R_INSTALLSTION_J_GOODS_ID not in", values, "rInstallstionJGoodsId");
            return (Criteria) this;
        }

        public Criteria andRInstallstionJGoodsIdBetween(Integer value1, Integer value2) {
            addCriterion("R_INSTALLSTION_J_GOODS_ID between", value1, value2, "rInstallstionJGoodsId");
            return (Criteria) this;
        }

        public Criteria andRInstallstionJGoodsIdNotBetween(Integer value1, Integer value2) {
            addCriterion("R_INSTALLSTION_J_GOODS_ID not between", value1, value2, "rInstallstionJGoodsId");
            return (Criteria) this;
        }

        public Criteria andAfterSalesInstallstionIdIsNull() {
            addCriterion("AFTER_SALES_INSTALLSTION_ID is null");
            return (Criteria) this;
        }

        public Criteria andAfterSalesInstallstionIdIsNotNull() {
            addCriterion("AFTER_SALES_INSTALLSTION_ID is not null");
            return (Criteria) this;
        }

        public Criteria andAfterSalesInstallstionIdEqualTo(Integer value) {
            addCriterion("AFTER_SALES_INSTALLSTION_ID =", value, "afterSalesInstallstionId");
            return (Criteria) this;
        }

        public Criteria andAfterSalesInstallstionIdNotEqualTo(Integer value) {
            addCriterion("AFTER_SALES_INSTALLSTION_ID <>", value, "afterSalesInstallstionId");
            return (Criteria) this;
        }

        public Criteria andAfterSalesInstallstionIdGreaterThan(Integer value) {
            addCriterion("AFTER_SALES_INSTALLSTION_ID >", value, "afterSalesInstallstionId");
            return (Criteria) this;
        }

        public Criteria andAfterSalesInstallstionIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("AFTER_SALES_INSTALLSTION_ID >=", value, "afterSalesInstallstionId");
            return (Criteria) this;
        }

        public Criteria andAfterSalesInstallstionIdLessThan(Integer value) {
            addCriterion("AFTER_SALES_INSTALLSTION_ID <", value, "afterSalesInstallstionId");
            return (Criteria) this;
        }

        public Criteria andAfterSalesInstallstionIdLessThanOrEqualTo(Integer value) {
            addCriterion("AFTER_SALES_INSTALLSTION_ID <=", value, "afterSalesInstallstionId");
            return (Criteria) this;
        }

        public Criteria andAfterSalesInstallstionIdIn(List<Integer> values) {
            addCriterion("AFTER_SALES_INSTALLSTION_ID in", values, "afterSalesInstallstionId");
            return (Criteria) this;
        }

        public Criteria andAfterSalesInstallstionIdNotIn(List<Integer> values) {
            addCriterion("AFTER_SALES_INSTALLSTION_ID not in", values, "afterSalesInstallstionId");
            return (Criteria) this;
        }

        public Criteria andAfterSalesInstallstionIdBetween(Integer value1, Integer value2) {
            addCriterion("AFTER_SALES_INSTALLSTION_ID between", value1, value2, "afterSalesInstallstionId");
            return (Criteria) this;
        }

        public Criteria andAfterSalesInstallstionIdNotBetween(Integer value1, Integer value2) {
            addCriterion("AFTER_SALES_INSTALLSTION_ID not between", value1, value2, "afterSalesInstallstionId");
            return (Criteria) this;
        }

        public Criteria andAfterSalesGoodsIdIsNull() {
            addCriterion("AFTER_SALES_GOODS_ID is null");
            return (Criteria) this;
        }

        public Criteria andAfterSalesGoodsIdIsNotNull() {
            addCriterion("AFTER_SALES_GOODS_ID is not null");
            return (Criteria) this;
        }

        public Criteria andAfterSalesGoodsIdEqualTo(Integer value) {
            addCriterion("AFTER_SALES_GOODS_ID =", value, "afterSalesGoodsId");
            return (Criteria) this;
        }

        public Criteria andAfterSalesGoodsIdNotEqualTo(Integer value) {
            addCriterion("AFTER_SALES_GOODS_ID <>", value, "afterSalesGoodsId");
            return (Criteria) this;
        }

        public Criteria andAfterSalesGoodsIdGreaterThan(Integer value) {
            addCriterion("AFTER_SALES_GOODS_ID >", value, "afterSalesGoodsId");
            return (Criteria) this;
        }

        public Criteria andAfterSalesGoodsIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("AFTER_SALES_GOODS_ID >=", value, "afterSalesGoodsId");
            return (Criteria) this;
        }

        public Criteria andAfterSalesGoodsIdLessThan(Integer value) {
            addCriterion("AFTER_SALES_GOODS_ID <", value, "afterSalesGoodsId");
            return (Criteria) this;
        }

        public Criteria andAfterSalesGoodsIdLessThanOrEqualTo(Integer value) {
            addCriterion("AFTER_SALES_GOODS_ID <=", value, "afterSalesGoodsId");
            return (Criteria) this;
        }

        public Criteria andAfterSalesGoodsIdIn(List<Integer> values) {
            addCriterion("AFTER_SALES_GOODS_ID in", values, "afterSalesGoodsId");
            return (Criteria) this;
        }

        public Criteria andAfterSalesGoodsIdNotIn(List<Integer> values) {
            addCriterion("AFTER_SALES_GOODS_ID not in", values, "afterSalesGoodsId");
            return (Criteria) this;
        }

        public Criteria andAfterSalesGoodsIdBetween(Integer value1, Integer value2) {
            addCriterion("AFTER_SALES_GOODS_ID between", value1, value2, "afterSalesGoodsId");
            return (Criteria) this;
        }

        public Criteria andAfterSalesGoodsIdNotBetween(Integer value1, Integer value2) {
            addCriterion("AFTER_SALES_GOODS_ID not between", value1, value2, "afterSalesGoodsId");
            return (Criteria) this;
        }

        public Criteria andNumIsNull() {
            addCriterion("NUM is null");
            return (Criteria) this;
        }

        public Criteria andNumIsNotNull() {
            addCriterion("NUM is not null");
            return (Criteria) this;
        }

        public Criteria andNumEqualTo(Integer value) {
            addCriterion("NUM =", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumNotEqualTo(Integer value) {
            addCriterion("NUM <>", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumGreaterThan(Integer value) {
            addCriterion("NUM >", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("NUM >=", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumLessThan(Integer value) {
            addCriterion("NUM <", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumLessThanOrEqualTo(Integer value) {
            addCriterion("NUM <=", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumIn(List<Integer> values) {
            addCriterion("NUM in", values, "num");
            return (Criteria) this;
        }

        public Criteria andNumNotIn(List<Integer> values) {
            addCriterion("NUM not in", values, "num");
            return (Criteria) this;
        }

        public Criteria andNumBetween(Integer value1, Integer value2) {
            addCriterion("NUM between", value1, value2, "num");
            return (Criteria) this;
        }

        public Criteria andNumNotBetween(Integer value1, Integer value2) {
            addCriterion("NUM not between", value1, value2, "num");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_R_INSTALLSTION_J_GOODS
     *
     * @mbggenerated do_not_delete_during_merge Fri Oct 15 16:55:14 CST 2021
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_R_INSTALLSTION_J_GOODS
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}