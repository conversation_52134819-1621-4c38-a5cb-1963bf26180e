package com.vedeng.goods.manager.validator.InvalidatorChain.sku.valid;

import com.vedeng.authorization.dao.RoleMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.goods.dao.CoreSpuMapper;
import com.vedeng.goods.exception.InvalidatorChainException;
import com.vedeng.goods.manager.validator.GoodsInvalidatorChain;
import com.vedeng.goods.model.CoreSpu;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.vedeng.goods.manager.validator.enums.GoodsValidatorErrorEnum.SkuUserValidatorError;


@Service
public class SkuUserValidator extends GoodsInvalidatorChain {

    @Resource
    CoreSpuMapper coreSpuMapper;

    @Resource
    RoleMapper roleMapper;

    @Override
    public void doInvalid() {
        User user = getUser();
        List<Integer> candidateIds = roleMapper.getUserIdByRoleName("供应链质量官", 1);
        if(CollectionUtils.isEmpty(candidateIds)){
            throw new InvalidatorChainException(SkuUserValidatorError.getCode(),SkuUserValidatorError.getMessage());
        }
        Optional<Integer> any = candidateIds.stream().filter(e -> e.equals(user.getUserId())).findAny();
        if(!any.isPresent()){
            throw new InvalidatorChainException(SkuUserValidatorError.getCode(),SkuUserValidatorError.getMessage());
        }
    }

}
