<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>AI解析参数配置</title>
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="modifyAiSetting" datasource="erp-datasourcetarget" success_url="reload">
  <div id="APPEND_HEAD" class="append">
      <style>
          .layui-quote-nm{display: none;}

      </style>
  </div>
  <div class="layui-container"> 
   <form id="inputForm" method="post" class="layui-form">
       <div class="layui-form-item ">
           <label class="layui-form-label">分组名称</label>
           <div class="layui-input-inline form-group">
               <select class="form-item" name="SENCE_CODE" type="search"
                       value="" item_name="SENCE_CODE" placeholder=""
                       style="" alias="" jdbctype="" data="SELECT SENCE_CODE K, SENCE_NAME V FROM T_AI_SETTING"
                       datatype="KVSQL" lay_verify="required" item_desc="" group_data="" valid_rule="" valid_msg="" item_max_upload=""></select>

           </div>
       </div>
       <div class="layui-form-item ">
           <label class="layui-form-label">分组名称</label>
           <div class="layui-input-inline form-group">
               <select class="form-item" name="GROUP_CODE" type="search" value="" item_name="GROUP_CODE" placeholder="" style="" alias="" jdbctype="" data="SELECT GROUP_CODE K, GROUP_NAME V
                                                    FROM T_AI_GROUP" datatype="KVSQL" lay_verify="required" item_desc="" group_data="" valid_rule="" valid_msg="" item_max_upload=""></select>


           </div>
       </div>
      <div class="layui-form-item ">
          <label class="layui-form-label">字段英文名</label>
            <div class="layui-input-inline form-group">
            <input    lay_verify="required" datatype="varchar" jdbctype="VARCHAR"
                   type="text"    class="layui-input form-item"
                   value=""  name="FIELD_CODE" item_desc="字段英文名" maxlength="200">
            </div>
      </div>
      <div class="layui-form-item ">
           <label class="layui-form-label">字段中文名</label>
           <div class="layui-input-inline form-group">
               <input maxlength="200" lay_verify="required"
                         type="text"   class="layui-input form-item"
                         value=""  name="FIELD_NAME" item_desc="字段中文名">
           </div>
       </div>
       <div class="layui-form-item ">
           <label class="layui-form-label">字段顺序</label>
           <div class="layui-input-inline form-group">
               <input  lay_verify="required"
                       type="number"   class="layui-input form-item"
                       value=""  name="FIELD_ORDER" item_desc="字段顺序，每个分组下从1开始">
           </div>
       </div>
      <div class="layui-form-item "> <label class="layui-form-label">答案</label>
       <div class="layui-input-inline form-group">
           <object class="form-item" lay_verify="required"  name="FIELD_ABOUT"  oninput="updateWordCount();" item_desc="字段PROMPT"type="textarea" value="" item_name="FIELD_ABOUT" placeholder="" style="" alias="" jdbctype="" data="" datatype="" lay_verify="" item_desc=""  valid_rule="" valid_msg="" item_max_upload=""></object>
           <small id="DESC_FIELD_ABOUT" class="form-text text-muted">字数</small>
       </div>
      </div>
   </form> 
   <form id="configForm" class="layui-form"> 
    <div class="layui-card" >
     <div class="layui-card-header">
       表单数据配置 
     </div> 
     <div class="layui-card-body"> 
     </div> 
    </div> 
    <div class="layui-form-item "><label class="layui-form-label">初始表达式</label> 
     <div class="layui-input-block form-group"> 
      <pre class="form-item layui-code" id="init" name="INIT_EXPRESS" type="">list=select("
      SELECT
                F.FIELD_ID,
                F.SENCE_CODE,
                F.GROUP_CODE,
                F.FIELD_CODE,
                F.FIELD_NAME,
                F.FIELD_ORDER,
                F.FIELD_ABOUT,
                DATE_FORMAT(F.ADD_TIME, '%Y-%m-%d %H:%i:%s') AS ADD_TIME,
                F.ADD_USER_ID,
                DATE_FORMAT(F.UPDATE_TIME, '%Y-%m-%d %H:%i:%s') AS UPDATE_TIME,
                F.UPDATE_USER_ID,
                S.SENCE_NAME,
                G.GROUP_NAME
            FROM
                T_AI_FIELD F
            JOIN
                T_AI_SETTING S ON F.SENCE_CODE = S.SENCE_CODE
            JOIN
                T_AI_GROUP G ON F.GROUP_CODE = G.GROUP_CODE
WHERE
	FIELD_ID= ${ID}");
return list.get(0);</pre> 
     </div> 
    </div>
    <div class="layui-form-item "> <label class="layui-form-label">提交表达式</label> 
     <div class="layui-input-block form-group"> 
      <pre class="form-item layui-code" id="submit" name="SUBMIT_EXPRESS" type="">if(null==ID||"0".equals(ID)){
          sessionUserId=$$("EZ_SESSION_USER_ID_KEY");
          insert("INSERT INTO T_AI_FIELD
            (SENCE_CODE,GROUP_CODE,FIELD_CODE,FIELD_NAME,FIELD_ORDER,
          FIELD_ABOUT,ADD_TIME,ADD_USER_ID,UPDATE_TIME,UPDATE_USER_ID) VALUES
          (#{SENCE_CODE},#{GROUP_CODE},#{FIELD_CODE},#{FIELD_NAME},#{FIELD_ORDER},
          #{FIELD_ABOUT},now(),#{EZ_SESSION_USER_ID_KEY},now(),#{EZ_SESSION_USER_ID_KEY})");
     return 0;
}else{
    update("UPDATE T_AI_FIELD
          SET
          SENCE_CODE=#{SENCE_CODE},
          GROUP_CODE=#{GROUP_CODE},
          FIELD_CODE=#{FIELD_CODE},
          FIELD_NAME=#{FIELD_NAME},
          FIELD_ORDER=#{FIELD_ORDER},
          FIELD_ABOUT=#{FIELD_ABOUT} ,
          UPDATE_USER_ID = #{EZ_SESSION_USER_ID_KEY},
          UPDATE_TIME = NOW()
          WHERE FIELD_ID = #{ID}");
return ID;
}</pre> 
     </div> 
    </div>
    <div class="layui-form-item "> <label class="layui-form-label">删除表达式</label> 
     <div class="layui-input-block form-group"> 
      <pre class="form-item layui-code" id="delete" name="DELETE_EXPRESS" type="">
          <!--if(null!=ID||IS_DELETE ==0){-->
            <!--update("UPDATE T_ASK_QUESTION_MANAGEMENT  SET IS_DELETE = 1, UPDATER = #{EZ_SESSION_USER_ID_KEY}, MOD_TIME = NOW() WHERE ID = #{ID}");-->
          <!--}else{-->
            <!--update(" UPDATE T_ASK_QUESTION_MANAGEMENT SET IS_DELETE = 0, UPDATER = #{EZ_SESSION_USER_ID_KEY}, MOD_TIME = NOW() WHERE ID = #{ID}");-->
          <!--}-->
          update(" DELETE FROM  T_AI_FIELD WHERE FIELD_ID = #{ID}");
          return ID;
      </pre>
     </div> 
    </div>

   </form> 
  </div> 
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script> 
  <script>
    layui.use(function () {

    })
</script> 
  <div id="APPEND_FOOT" class="append"> 
   <script>
        // var id=  $("#ID").val();
        // if(id == undefined || id == ""){
        //     $("div[item_name='DOC_TYPE']").hide();
        // }
        function updateCharCount(obj){
            //var textarea = document.getElementsByName('ANSWER');
            var wordCount = document.getElementById('DESC_FIELD_ABOUT');
            // 使用正则表达式匹配非空白字符序列作为“词”的定义
            var words = $(obj).val();
            // 更新词的数量
            var current = (words!=undefined && words )? words.length : 0;
            wordCount.textContent = current +"/600";
        }

        $(function(){
            updateCharCount($('textarea[name="FIELD_ABOUT"]')[0]);
            $('textarea[name="FIELD_ABOUT"]').attr("maxlength",600);
            $('input[name="FIELD_NAME"]').attr("maxlength",200);
            $('textarea[name="FIELD_ABOUT"]').on('input', function(e) {
                updateCharCount(this);
            });
            
            // $("#submitbtn").click(function () {
            //     var question = $("[name='QUESTION']").val();
            //     var answer = $("[name='ANSWER']").val();
            //     if($.trim(question) == ''){
            //         var layer = layui.layer; // 弹层模块
            //         // 当需要弹出提示信息时
            //         layer.msg('请输入提问内容');
            //         return false;
            //     }
            //     if($.trim(answer)  == ''){
            //         var layer = layui.layer; // 弹层模块
            //         // 当需要弹出提示信息时
            //         layer.msg('请输入答案');
            //         return false;
            //     }
            // })
            
        });
    </script> 
  </div> 
 </body>
</html>