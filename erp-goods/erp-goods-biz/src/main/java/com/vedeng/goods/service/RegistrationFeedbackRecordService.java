package com.vedeng.goods.service;

import com.vedeng.goods.domain.entity.RegistrationFeedbackRecordEntity;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022−10-31 上午9:16
 * @description 注册证问题反馈
 */
public interface RegistrationFeedbackRecordService {

    /**
     * 新增反馈记录
     * @param registrationFeedbackRecord
     * @param userId
     */
    void addFeedbackRecord(RegistrationFeedbackRecordEntity registrationFeedbackRecord,Integer userId);

    Map<String,Object> handleFeedbackNum(Integer registrationNumberId);
}
