package com.vedeng.goods.manager.extension.handler;

import com.vedeng.todolist.service.ITodoInstance;
import com.vedeng.todolist.service.impl.MaintainDataSignContractMode;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Component
public class GoodsSignContractTodoHandler extends BaseGoodsTodoHandler {
    @Resource
    private MaintainDataSignContractMode maintainDataSignContractMode;

    @Override
    public int getServiceId() {
        return SIGN_CONTRACT_SERVICE_ID;
    }

    @Override
    public ITodoInstance getITodoInstance() {
        return maintainDataSignContractMode;
    }

    @Override
    public boolean isDone() {
        return true;
    }

    @Override
    public void onHandler() {
        throw new UnsupportedOperationException("目前暂不支持");
    }
}
