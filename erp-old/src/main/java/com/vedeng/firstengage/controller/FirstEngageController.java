package com.vedeng.firstengage.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.PdfReader;
import com.vedeng.authorization.dao.UserDetailMapper;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.core.annotation.ExcludeAuthorization;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.utils.imageupload.ChainResult;
import com.vedeng.common.core.utils.imageupload.ImgUploadVerifyActuator;
import com.vedeng.common.exception.ShowErrorMsgException;
import com.vedeng.common.model.FileInfo;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.model.ResultJSON;
import com.vedeng.common.page.Page;
import com.vedeng.common.support.CustomDefaultResourceCache;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.EmptyUtils;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.common.validator.FormToken;
import com.vedeng.erp.trader.dto.RegistrationProductionModeDto;
import com.vedeng.erp.trader.service.RegistrationProductionApiService;
import com.vedeng.firstengage.model.AttachmentHistoryLine;
import com.vedeng.firstengage.model.FirstEngage;
import com.vedeng.firstengage.model.RegistrationNumber;
import com.vedeng.firstengage.service.FirstEngageService;
import com.vedeng.ftpclient.client.FTPClientHelper;
import com.vedeng.goods.dto.RegistrationFeedbackDto;
import com.vedeng.goods.model.LogCheckGenerate;
import com.vedeng.goods.model.StandardCategory;
import com.vedeng.goods.service.RegistrationFeedbackApiService;
import com.vedeng.goods.service.VgoodsService;
import com.vedeng.order.service.RiskCheckService;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.service.FtpUtilService;
import com.vedeng.system.service.OssUtilsService;
import com.vedeng.system.service.SysOptionDefinitionService;
import com.vedeng.ty.api.res.ResProductInfoDto;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.pdfbox.io.MemoryUsageSetting;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPageTree;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.im4java.core.CompositeCmd;
import org.im4java.core.ConvertCmd;
import org.im4java.core.IM4JavaException;
import org.im4java.core.IMOperation;
import org.json.JSONArray;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;
import org.springframework.web.servlet.ModelAndView;
import sun.rmi.runtime.Log;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 首营信息
 * <p>Title: FirstEngageController</p>
 * <p>Description: </p>
 * <AUTHOR>
 * @date 2019年3月20日
 */
@Controller
@RequestMapping("/firstengage/baseinfo")
public class FirstEngageController extends BaseController{


	@Autowired
	private FirstEngageService firstEngageService;

	@Autowired
	private SysOptionDefinitionService sysOptionDefinitionService;

	@Autowired
	@Qualifier("ftpClientHelper")
	private FTPClientHelper ftpClientHelper;

	@Autowired
	private OssUtilsService ossUtilsService;

	@Resource
	private UserMapper userMapper;

	@Resource
	private UserDetailMapper userDetailMapper;

	@Value("${gm_url}")
	private String gmUrl;


	private static String imageMagickPath = "C:\\Program Files\\GraphicsMagick-1.3.31-Q16";

	@Value("${oldLogCheck.deadline}")
	private Long deadlineOfOldLogCheck;

	@Autowired
	private RiskCheckService riskCheckService;

	@Autowired
	private ImgUploadVerifyActuator imgUploadVerifyActuator;

	@Resource
	RegistrationFeedbackApiService registrationFeedbackApiService;

	/**
	 * 国标分类，一级
	 */
	private static final Integer STANDARD_PARRNT_ID_0 = 0;


	private static final Logger logger = LoggerFactory.getLogger(FirstEngageController.class);

	/**
	 * 商品首营列表
	 * 	 * <p>Title: getAfterSalesPage</p>
	 * 	 * <p>Description: </p>
	 * @param request
	 * @param pageNo
	 * @param pageSize
	 * @return
	 * <AUTHOR>
	 * @date 2019年3月20日
	 */
	@SuppressWarnings("unchecked")
	@RequestMapping("/getFirstEngageInfo")
	@ResponseBody
	public Object getFirstEngageInfoListPage(HttpServletRequest request, FirstEngage firstEngage,
												   @RequestParam(required = false, defaultValue = "1") Integer pageNo,
												   @RequestParam(required = false, defaultValue = "1") Integer tag,
												   @RequestParam(required = false, defaultValue = "10") Integer pageSize){
		ModelAndView mv = new ModelAndView();
		try {
			// 当前登陆用户
			User sessUser = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
			// 分页信息
			Page page = getPageTag(request, pageNo, pageSize);

			// 参数集
			Map<String, Object> paramMap = new HashMap<>();
			if(null != firstEngage.getStatus()){
				// 首营信息审核状态
				paramMap.put("status", firstEngage.getStatus());
			}

			// 排序默认更新使用更新时间
			if(null == firstEngage.getTimeSort()){
				firstEngage.setTimeSort(1);
			}

			// 关键词
			String keyWords = firstEngage.getKeyWords();
			paramMap.put("keyWords", keyWords);

			if(null != firstEngage.getIsOverDate()){
			    mv.addObject("organIdList", firstEngage.getIsOverDate());
				// 注册证过期状态 3 ~ 6月
				for (Integer isOverDate: firstEngage.getIsOverDate()) {
					if ((isOverDate).equals(1)){
						// 6月 时间戳
						Long firstEngageDateStart = DateUtil.convertLong(DateUtil.getDayOfMonth(3), DateUtil.DATE_FORMAT);
						// 6个月前
						Long firstEngageDateEnd = DateUtil.convertLong(DateUtil.getDayOfMonth(6), DateUtil.DATE_FORMAT);
						paramMap.put("firstEngageSDateStart", firstEngageDateStart);
						paramMap.put("firstEngageSDateEnd", firstEngageDateEnd);
					}
					// 1 ~ 3月
					if((isOverDate).equals(2)){
						// 1月 时间戳
						Long firstEngageDateStart = DateUtil.convertLong(DateUtil.getDayOfMonth(1), DateUtil.DATE_FORMAT);
						// 3个月前
						Long firstEngageDateEnd = DateUtil.convertLong(DateUtil.getDayOfMonth(3), DateUtil.DATE_FORMAT);
						paramMap.put("firstEngageThDateStart", firstEngageDateStart);
						paramMap.put("firstEngageThDateEnd", firstEngageDateEnd);
					}
					// 0 ~ 1月
					if((isOverDate).equals(3)){
						// 0月 时间戳
						Long firstEngageDateStart = DateUtil.convertLong(DateUtil.getDayOfMonth(0), DateUtil.DATE_FORMAT);
						// 1个月前
						Long firstEngageDateEnd = DateUtil.convertLong(DateUtil.getDayOfMonth(1), DateUtil.DATE_FORMAT);
						paramMap.put("firstEngageODateStart", firstEngageDateStart);
						paramMap.put("firstEngageODateEnd", firstEngageDateEnd);
					}

					// 已经过期
					if((isOverDate).equals(4)){
						Long firstEngageDateEnd = DateUtil.convertLong(DateUtil.getDayOfMonth(0), DateUtil.DATE_FORMAT);
						paramMap.put("firstEngageEDateEnd", firstEngageDateEnd);
					}
				}
			}
			if(EmptyUtils.isNotBlank(firstEngage.getEffectStartDate())){
				// 注册证有效期起始时间
				paramMap.put("registrationStartTime", DateUtil.convertLong(firstEngage.getEffectStartDate(), DateUtil.DATE_FORMAT));
			}
			if(EmptyUtils.isNotBlank(firstEngage.getEffectEndDate())){
				// 注册证有效期结束时间
				paramMap.put("registrationEndTime", DateUtil.convertLong(firstEngage.getEffectEndDate(), DateUtil.DATE_FORMAT));
			}

			// 用户信息
			paramMap.put("userId", sessUser.getUserId());

			paramMap.put("firstEngage", firstEngage);

			// 有无未处理问题状态 有未处理：0  无未处理：1
			paramMap.put("UnDealStatus",firstEngage.getUnDealStatus());

			// 获取商品首营列表
			Map<String, Object> mapResult = firstEngageService.getFirstEngageInfoListPage(paramMap, page, firstEngage);

			// 分页信息
			paramMap.put("page", page);

			mv.addObject("firstEngageList", (List<FirstEngage>) mapResult.get("firstEngageList"));
			Page page1 = (Page) mapResult.get("page");
			mv.addObject("total", mapResult.get("total"));
			mv.addObject("one", mapResult.get("one"));
			mv.addObject("two", mapResult.get("two"));
			mv.addObject("three", mapResult.get("three"));
			mv.addObject("waitToPre", mapResult.get("waitToPre"));
			mv.addObject("overDateCount", mapResult.get("overDateCount"));
			mv.addObject("page", page1);
			mv.addObject("tag", tag);
			// 关键词选中状态
			mv.addObject("searchStatus", firstEngage.getSearchStatus());


			// 全部归属人列表
			mv.addObject("assignments",mapResult.get("assignments"));

			// 未处理问题状态
			mv.addObject("unDealStatus",firstEngage.getUnDealStatus());

		} catch (Exception e) {
			logger.error("商品首营列表:", e);
		}
		mv.setViewName("firstengage/first/index");
		return mv;
	}


	@Autowired
	private RegistrationProductionApiService registrationProductionApiService;
	/**
	 * 添加首营信息
	 * <p>Title: addFirstEngage</p>
	 * <p>Description: </p>
	 * @param request
	 * @return
	 * <AUTHOR>
	 * @date 2019年3月25日
	 */
	@FormToken(save = true)
	@RequestMapping(value = "/add")
	public ModelAndView addFirstEngage(HttpServletRequest request, Integer firstEngageId) {
		ModelAndView mv = new ModelAndView();
		try {
			User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);

			// 管理类别
			Map<String, Object> paramMap = getManagermentMap(mv);

			mv.addObject("user", user);
			// 如果firstEngageId不为空，说明是编辑页
			if(null != firstEngageId){
				paramMap.put("userId", user.getUserId());
				paramMap.put("firstEngageId", firstEngageId);
				FirstEngage firstEngage = firstEngageService.getFirstSearchDetail(paramMap, firstEngageId);

				List<RegistrationProductionModeDto> productionModeDtoList = registrationProductionApiService.queryRegistrationProductionMode(firstEngage.getRegistrationNumberId());
				firstEngage.setManufacturerList(productionModeDtoList);



				mv.addObject("zczMapList", new JSONArray(firstEngage.getZczMapList()).toString());
				mv.addObject("zczyMapList", new JSONArray(firstEngage.getZczyMapList()).toString());
				mv.addObject("yzMapList", new JSONArray(firstEngage.getYzMapList()).toString());
				mv.addObject("scMapList", new JSONArray(firstEngage.getScMapList()).toString());
				mv.addObject("smsMapList", new JSONArray(firstEngage.getSmsMapList()).toString());
				mv.addObject("djbMapList", new JSONArray(firstEngage.getDjbMapList()).toString());
				mv.addObject("firstEngage", firstEngage);
				mv.addObject("manufacturerId",firstEngage.getRegistration().getManufacturerId());//传当前生产商的id
				mv.addObject("manufacturerList",JSONObject.toJSONString(productionModeDtoList));//传当前生产商的id

				mv.addObject("labelMapList", new JSONArray(firstEngage.getLabelMapList()).toString());
				mv.addObject("labelSourceMapList", new JSONArray(firstEngage.getLabelSourceMapList()).toString());

				if (firstEngage.getRegistration().getManufacturerId() != null && firstEngage.getRegistration().getManufacturerId() > 0) {
					mv.addObject("manufacturerName", firstEngageService.selectBymanufacturerId(firstEngage.getRegistration().getManufacturerId()));
				}

				mv.addObject("api_http", api_http); //展示照片用
			}

		} catch (Exception e) {
			logger.error("添加首营信息:", e);
		}
		mv.setViewName("firstengage/first/new_add");
		return mv;
	}

	/**
	 * 根据名字模糊查询数据
	 * @return
	 */
	@RequestMapping(value = "/queryByName", method = RequestMethod.POST)
	@ExcludeAuthorization
	@ResponseBody
	public R<List<RegistrationNumber>> queryByName(@RequestParam() String name) {
		return R.success(firstEngageService.queryByName(name));
	}


	//上传加盖贝登公章
	@FormToken(save = true)
	@RequestMapping(value = "/addJGBDGZ")
	public ModelAndView addJGBDGZ(HttpServletRequest request, Integer firstEngageId) {
		ModelAndView mv = new ModelAndView();
		try {
			User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);

			// 管理类别
			Map<String, Object> paramMap = getManagermentMap(mv);

			mv.addObject("user", user);
			// 如果firstEngageId不为空，说明是编辑页
			if(null != firstEngageId){
				paramMap.put("userId", user.getUserId());
				paramMap.put("firstEngageId", firstEngageId);
				FirstEngage firstEngage = firstEngageService.getFirstSearchDetail(paramMap, firstEngageId);
				//注册证附件/备案凭证附件（贝） 注册证(贝)
				mv.addObject("zcZBMapList", new JSONArray(firstEngage.getZcZBMapList()).toString());
				mv.addObject("firstEngage", firstEngage);
			}
		} catch (Exception e) {
			logger.error("添加首营信息:", e);
		}
		mv.setViewName("firstengage/first/new_addJGBDGZ");
		return mv;

	}


	@ResponseBody
	@RequestMapping("/getTyRegisterNumber")
	public ResultInfo<ResProductInfoDto> getTyRegisterNumber(String registerNumberNo){
		return firstEngageService.getTyRegisterCertificate(registerNumberNo);
	}
	/**
	 * 新增首营商品信息
	 * <p>Title: addFirstEngageInfo</p>
	 * <p>Description: </p>
	 * @param request
	 * @param firstEngage
	 * @return
	 * <AUTHOR>
	 * @date 2019年3月21日
	 */
	@FormToken(remove = true)
	@RequestMapping("/addFirstEngageInfo")
	public ModelAndView addFirstEngageInfo(HttpServletRequest request, FirstEngage firstEngage){
		Integer resId = null;

		try {
			// 当前登陆用户
			User sessUser = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
			try {
				// 校验首营参数
				firstEngageService.initFirstEngageInfo(firstEngage);
			} catch (ShowErrorMsgException e) {
				firstEngage.setErrors(Lists.newArrayList(e.getErrorMsg()));
				request.setAttribute("firstEngage", firstEngage);
				return new ModelAndView("forward:./newpagefirst.do");
			}
			// 添加首营商品信息，返回新增的首营id
			resId = firstEngageService.addFirstEngageInfo(firstEngage, sessUser);
			//风控校验是否通过
			riskCheckService.checkFirstEnageAndSkuTodo(resId);
		} catch (Exception e) {
			logger.error("新增首营商品信息:", e);
		}

		return new ModelAndView("redirect:/firstengage/baseinfo/getFirstSearchDetail.do?firstEngageId=" + resId);
	}

	/**
	 * @description 转发新增页
	 * <AUTHOR>
	 * @param
	 * @date 2019/5/29
	 */
	@FormToken(save = true)
	@RequestMapping("/newpagefirst")
	public ModelAndView getForwardPage(HttpServletRequest request){
		User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
		ModelAndView mav = new ModelAndView();
		try {
			// 首营信息参数
			FirstEngage firstEngage = (FirstEngage) request.getAttribute("firstEngage");
			mav.addObject("user", user);
			// 管理类别
			getManagermentMap(mav);

			// 注册证
			RegistrationNumber registration = firstEngage.getRegistration();
			// 注册证附件/备案凭证附件
			getAttachments(registration.getZczAttachments(), mav, "zczMapList");
			// 营业执照
			getAttachments(registration.getYzAttachments(), mav, "yzMapList");
			// 说明书
			getAttachments(registration.getSmsAttachments(), mav, "smsMapList");
			// 生产企业卫生许可证
			getAttachments(registration.getWsAttachments(), mav, "wsMapList");
			// 生产企业许可证
			getAttachments(registration.getScAttachments(), mav, "scMapList");
			// 商标注册证
			getAttachments(registration.getSbAttachments(), mav, "sbMapList");
			// 注册登记表附件
			getAttachments(registration.getDjbAttachments(), mav, "djbMapList");
			// 产品图片（单包装/大包装）
			getAttachments(registration.getCpAttachments(), mav, "cpMapList");
			mav.addObject("firstEngage", firstEngage);
		} catch (Exception e) {
			logger.error("转发跳转页：", e);
		}
		mav.setViewName("firstengage/first/new_add");
		return mav;
	}

	/**
	 * 首营信息详情页
	 * <p>Title: getFirstSearchDetail</p>
	 * <p>Description: </p>
	 * @param request
	 * @param firstEngageId
	 * @return
	 * <AUTHOR>
	 * @date 2019年4月1日
	 */
	@RequestMapping("/getFirstSearchDetail")
	public ModelAndView getFirstSearchDetail(HttpServletRequest request, Integer firstEngageId){
		// 获取商品首营列表
		ModelAndView mv = new ModelAndView();

		try {
			User sessUser = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
			// 校验空
			if(null == firstEngageId){
				return null;
			}

			List<LogCheckGenerate> logCheckGenerateList = firstEngageService.listSkuCheckLog(firstEngageId);

			List<LogCheckGenerate> oldLogCheckList = new ArrayList<>();
			//符合质量管理要求的审核记录
			List<LogCheckGenerate> newLogCheckList = new ArrayList<>();
			logCheckGenerateList.forEach(item -> {
				item.setCreatorName(getRealNameByUserName(item.getCreatorName()));
				if (item.getAddTime().getTime() < deadlineOfOldLogCheck){
					oldLogCheckList.add(item);
				} else {
					newLogCheckList.add(item);
				}
			});
			mv.addObject("oldLogCheckList", oldLogCheckList);
			mv.addObject("newLogCheckList", newLogCheckList);
			//查询问题反馈记录
			List<RegistrationFeedbackDto> registrationFeedbackList = registrationFeedbackApiService.listByFirstEngageId(firstEngageId);
			registrationFeedbackList.forEach(r -> {
				r.setAddTimeDate(new Date(r.getAddTime()));
				r.setCreatorLabel(userMapper.getUserNameByUserId(r.getCreator())+" ("+(userDetailMapper.getUserDetail(r.getCreator()).getRealName() == null ? "":userDetailMapper.getUserDetail(r.getCreator()).getRealName())+")");
				if (null != r.getDealerUserId()){
					r.setDealerLabel(userMapper.getUserNameByUserId(r.getDealerUserId()));
				}
			});
			mv.addObject("registrationFeedbackList",registrationFeedbackList);
			// 参数集
			Map<String, Object> paramMap = new HashMap<>();
			paramMap.put("userId", sessUser.getUserId());
			paramMap.put("firstEngageId", firstEngageId);
			FirstEngage firstEngage = firstEngageService.getFirstSearchDetail(paramMap, firstEngageId);
			mv.addObject("firstEngage", firstEngage);




			if(firstEngage!=null && firstEngage.getRegistration()!=null){
				List<AttachmentHistoryLine> lineList=firstEngageService.getNumberAttachmentHistoryLine(
						firstEngage.getRegistration().getRegistrationNumberId(),firstEngage.getRegistration().getIsSubcontractProduction());
				mv.addObject("attachmentHistory",lineList);
				mv.addObject("manufacturerId", firstEngage.getRegistration().getManufacturerId());
				List<RegistrationProductionModeDto> productionModeDtoList = registrationProductionApiService.queryRegistrationProductionMode(firstEngage.getRegistrationNumberId());
				firstEngage.setManufacturerList(productionModeDtoList);
				mv.addObject("manufacturerList",JSONObject.toJSONString(productionModeDtoList));//传当前生产商的id
			}
			mv.addObject("api_http", api_http);

		} catch (Exception e) {
			logger.error("首营信息详情页", e);
		}
		mv.setViewName("firstengage/first/view");
		return mv;
	}


	/**
	 * 根据注册证id查询信息
	 * <p>Title: getFirstSearchInfoById</p>
	 * <p>Description: </p>
	 * @param request
	 * @return
	 * <AUTHOR>
	 * @date 2019年3月27日
	 */
	@RequestMapping("/getFirstSearchInfoById")
	@ResponseBody
	public ResultInfo<RegistrationNumber> getFirstSearchInfoById(HttpServletRequest request, Integer registrationNumberId, String registrationNumber){
		RegistrationNumber registration = null;
		try {
			User sessUser = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
			// 参数集
			Map<String, Object> paramMap = new HashMap<>();
			paramMap.put("userId", sessUser.getUserId());
			paramMap.put("registrationNumberId", registrationNumberId);
			// 注册证号
			paramMap.put("registrationNumber", registrationNumber);
			// 获取商品首营列表
			registration = firstEngageService.getRegistrationNumberInfoById(paramMap);
		} catch (Exception e) {
			logger.error("根据注册证id查询信息", e);
		}
		return new ResultInfo<RegistrationNumber>(0, "查询成功", registration);
	}

	@RequestMapping("/getnewstandcategory")
	@ResponseBody
	public ResultInfo getNewStandCategory(HttpServletRequest request, String categoryName){
		try {
			User sessUser = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
			// 参数集
			Map<String, Object> paramMap = new HashMap<>();
			paramMap.put("userId", sessUser.getUserId());
			// 注册证号
			paramMap.put("categoryName", categoryName);
			// 启用状态
			paramMap.put("status", CommonConstants.STATUS_1);
			// 获取商品首营列表
			List<Map<String, Object>> newStandCategoryList = firstEngageService.getNewStandardCategoryByName(paramMap);
			if(CollectionUtils.isNotEmpty(newStandCategoryList)){
				//			JSONArray jsonArray = new JSONArray(newStandCategoryList);
				return new ResultInfo(0, "成功", newStandCategoryList);
			}
		} catch (Exception e) {
			logger.error("获取新国标分类：", e);
			return new ResultInfo(-1, "失败"+e.getMessage());
		}
		return new ResultInfo(0, "成功");
	}


	/**
	 * 删除首映商品信息
	 * <p>Title: deleteFirstEngage</p>
	 * <p>Description: </p>
	 * @param request
	 * @return
	 * <AUTHOR>
	 * @date 2019年3月21日
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping("/deleteFirstEngage")
	@ResponseBody
	public ResultInfo deleteFirstEngage(HttpServletRequest request, Integer[] ids, String comment){

		try {
			User sessUser = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
			// 参数集
			Map<String, Object> paramMap = new HashMap<>();
			paramMap.put("firstEngageArray", ids);
			paramMap.put("userId", sessUser.getUserId());
			// 删除，待审核的不给删除
			paramMap.put("status", CommonConstants.FIRST_ENGAGE_STATUS_1);
			// 删除状态
			paramMap.put("isDelete", CommonConstants.IS_DELETE_0);
			paramMap.put("comment", comment);
			// 获取商品首营列表
			Integer delResult = firstEngageService.deleteFirstEngage(paramMap);
			if(null != delResult && delResult > 0){
				return new ResultInfo<>(0, "操作成功");
			}
			// 删除失败
			else{
				return new ResultInfo<>(-1, "删除失败");
			}
		} catch (Exception e) {
			logger.error("删除首映商品信息", e);
		}
		return new ResultInfo<>();
	}

	/**
	 * 根据输入查询注册证
	 * <p>Title: getRegistrationInfoByStr</p>
	 * <p>Description: </p>
	 * @param request
	 * @param registrationStr
	 * @return
	 * <AUTHOR>
	 * @date 2019年3月21日
	 */
	@RequestMapping("/getRegistrationInfo")
	@ResponseBody
	public ResultInfo<RegistrationNumber> getRegistrationInfoByStr(HttpServletRequest request, String registrationStr){
		List<RegistrationNumber> aaa = null;
		try {
			// 参数集
			Map<String, Object> paramMap = new HashMap<>();
			paramMap.put("registrationStr", registrationStr.replaceAll(" ", ""));
			aaa = firstEngageService.getRegistrationInfoByStr(paramMap);
		} catch (Exception e) {
			logger.error("根据输入查询注册证:",e);
		}
		return new ResultInfo<RegistrationNumber>(0, "查询成功", aaa);
	}

	/**
	 * 上传
	 * <p>Title: fileUploadImg</p>
	 * <p>Description: </p>
	 * @param request
	 * @param response
	 * @param file
	 * @return
	 * <AUTHOR>
	 * @date 2019年4月2日
	 */
	@ResponseBody
	@RequestMapping(value = "/fileUploadImg")
	public FileInfo fileUploadImg(HttpServletRequest request, HttpServletResponse response, @RequestBody MultipartFile file) {
		FileInfo fileInfo = null;
		ChainResult result = imgUploadVerifyActuator.ruleExecute(request, file);
		if(!result.isSuccess()){
			return new FileInfo(-1,result.getData().toString());
		}

		try{
			if (null == file){
				//Processing of multipart/form-data request failed. null
				logger.warn("文件上传失败：文件为null");
				fileInfo =  new FileInfo(-1,"文件上传失败");
				return fileInfo;
			}
			// 上传文件名称
			String fileName = file.getOriginalFilename();
			// 文件后缀名称
			String prefix = fileName.substring(fileName.lastIndexOf(".") + 1);

			fileInfo = ossUtilsService.upload2Oss(request,file);
			fileInfo.setDomain(fileInfo.getHttpUrl());
			fileInfo.setHttpUrl(api_http + fileInfo.getHttpUrl());
			fileInfo.setPrefix(prefix);
			fileInfo.setFileName(fileName);
		}catch (Exception e){
			logger.error("文件上传失败：", e);
			fileInfo =  new FileInfo(-1,"文件上传失败");
		}

		return fileInfo;
	}


	/**
	 * 获取封装得MultipartFile
	 *
	 * @param inputStream inputStream
	 * @param fileName    fileName
	 * @return MultipartFile
	 */
	public MultipartFile getMultipartFile(InputStream inputStream, String fileName) {
		FileItem fileItem = createFileItem(inputStream, fileName);
		//CommonsMultipartFile是feign对multipartFile的封装，但是要FileItem类对象
		return new CommonsMultipartFile(fileItem);
	}


	/**
	 * FileItem类对象创建
	 *
	 * @param inputStream inputStream
	 * @param fileName    fileName
	 * @return FileItem
	 */
	public FileItem createFileItem(InputStream inputStream, String fileName) {
		FileItemFactory factory = new DiskFileItemFactory(16, null);
		String textFieldName = "file";
		FileItem item = factory.createItem(textFieldName, MediaType.MULTIPART_FORM_DATA_VALUE, true, fileName);
		int bytesRead = 0;
		byte[] buffer = new byte[10 * 1024 * 1024];
		OutputStream os = null;
		//使用输出流输出输入流的字节
		try {
			os = item.getOutputStream();
			while ((bytesRead = inputStream.read(buffer, 0, 8192)) != -1) {
				os.write(buffer, 0, bytesRead);
			}
			inputStream.close();
		} catch (IOException e) {
			log.error("Stream copy exception", e);
			throw new IllegalArgumentException("文件上传失败");
		} finally {
			if (os != null) {
				try {
					os.close();
				} catch (IOException e) {
					log.error("Stream close exception", e);

				}
			}
			if (inputStream != null) {
				try {
					inputStream.close();
				} catch (IOException e) {
					log.error("Stream close exception", e);
				}
			}
		}

		return item;
	}


	/**
	 * pdf转图片并上传 （多页）
	 * @param request
	 * @param response
	 * @param file
	 * @return
	 */
	String localPaths = "/tmp/ys";  //test
	String newPaths = "/tmp/";
	@ResponseBody
	@RequestMapping(value = "/fileUploadPDFtoImgList")
	@ExcludeAuthorization
	public ResultInfo fileUploadPDFtoImgListLinux(HttpServletRequest request, HttpServletResponse response, @RequestBody MultipartFile file) throws Exception {
		logger.info("开始上传图片");
		String fileNameNoSuffix = file.getOriginalFilename().substring(0,file.getOriginalFilename().lastIndexOf(".")) + UUID.randomUUID();
		//定义返回附件集合
		//pdf转图片并压缩
		// 文件后缀名称
		String prefixv =FilenameUtils.getExtension(file.getOriginalFilename());
		//判断是否是pdf
		if ("pdf".equalsIgnoreCase(prefixv)){
			return dealPdf(request, file, fileNameNoSuffix );
		}
		return dealImage(request, file, fileNameNoSuffix  );
	}



	private ResultInfo dealImage(HttpServletRequest request, MultipartFile file,  String fileNameNoSuffix ) throws IOException {
		//压缩图片 转为800
		//判断图片的宽度是否大于800，如果不大于提醒 源文件质量较低，建议检查生成的图片清晰度
		// 将文件保存在服务器目录中
		// 得到上传文件后缀

		 List<FileInfo> fileInfos=new ArrayList<>();


		String imagePath= null;
		String newPath= null;
		BufferedImage image = ImageIO.read(file.getInputStream());
		if (image == null) { //如果image=null 表示上传的不是图片格式
			return new ResultInfo(-1, "图片格式不正确");
		}
		try {
			String originalName = file.getOriginalFilename();
			String ext = "." + FilenameUtils.getExtension(originalName);
			imagePath = newPaths + System.nanoTime() + ext;
//			// 复制文件
			File targetFile= new File(imagePath);
			file.transferTo(targetFile);
			//重复
			newPath = newPaths + fileNameNoSuffix + ext;
			//压缩图片
			gmPicWith800(imagePath, newPath);
		} catch (Exception e) {
			logger.error("图片转化失败",e);
			return new ResultInfo(-1, "图片转化失败");
		}
		FileInfo fileInfo = new FileInfo();
		try{
			String prefix = FilenameUtils.getExtension(file.getOriginalFilename());
			MultipartFile uplouadFile = getMultipartFile(new FileInputStream(newPath), fileNameNoSuffix + "."+ prefix);

			if (image.getWidth() < 600) {//与前端约定传1，用作提示
				uplouadFile = file;
				// 文件后缀名称
				fileInfo = ossUtilsService.upload2Oss(request, uplouadFile);
				fileInfo.setDomain(fileInfo.getHttpUrl());
				fileInfo.setHttpUrl(api_http + fileInfo.getHttpUrl());
				fileInfo.setPrefix(prefix);
				fileInfo.setFileName(file.getOriginalFilename());
				fileInfo.setFileWidth(new BigDecimal(1));
			}else {
				// 文件后缀名称
				fileInfo = ossUtilsService.upload2Oss(request, uplouadFile);
				fileInfo.setDomain(fileInfo.getHttpUrl());
				fileInfo.setHttpUrl(api_http + fileInfo.getHttpUrl());
				fileInfo.setPrefix(prefix);
				fileInfo.setFileName(file.getOriginalFilename());
			}

		}catch (Exception e){
			logger.error("文件上传失败：", e);
			return new ResultInfo(-1, "文件上传失败");
		}finally {
			File filetes = new File(imagePath);
			File fileimg = new File(newPath);
			filetes.delete();
			fileimg.delete();
		}

		fileInfos.add(fileInfo);
		ResultInfo<FileInfo> success = new ResultInfo<>(0, "上传成功", fileInfos);
		success.setData(fileInfo);
		return success;
	}

	/**
	 * 检测pdf文件大小
	 * 原文链接：https://blog.csdn.net/pthill/article/details/98959898
	 * @param file
	 */
	public static String checkPdfFileSize(MultipartFile file)   {
		InputStream inputStream = null;
		try{
			inputStream = file.getInputStream(); //   file.get();new FileInputStream(file);/
			PdfReader reader = new PdfReader(inputStream);
			Rectangle pageSize = reader.getPageSize(1);
			float height = pageSize.getHeight();
			float width = pageSize.getWidth();
			float realHeight = height/72*25.4f;//转换为mm单位
			float realWidth = width/72*25.4f;//转换为mm单位
			if(realHeight > 500 || realWidth > 500){
				return "当前pdf文件尺寸宽：" + (int)(realWidth/10)+"cm，高："+(int)(realHeight/10)+"cm，正常A4大小为21cm*29cm，请调整尺寸后再次上传。";
			}
//			System.out.println("width = "+width+", height = "+height);
//			System.out.println("换算后："+"width = "+ (width*25.4/72) +", height = "+ (height*25.4/72) );
		}catch (Exception e){
			logger.error("检测pdf大小加载文件失败",e);
		}finally {
			try{
				if(inputStream != null){
					inputStream.close();
				}
			}catch (Exception e){
				logger.error("关闭inputStream失败",e);
			}
		}
		return null;

	}

//	public static void main(String[] args) {
//		File file = new File("D:\\pdf\\国械注准20153400163(2).pdf");
//		String x = checkPdfFileSize(file);
//		System.out.println(x);
//	}

	private ResultInfo dealPdf(HttpServletRequest request, MultipartFile file, String substring ) throws IOException {
		String checkResult = checkPdfFileSize(file);
		if(StringUtils.isNotBlank(checkResult)){
			return new ResultInfo(-1, checkResult);
		}
		PDDocument doc = null;
		InputStream stream = null;
		OutputStream out = null;
		MultipartFile orginFile = file;
		BufferedImage bim = null;
		 List<FileInfo> fileInfos = new ArrayList<>();
		 List<File> tempToDeleteFileList = new ArrayList<File>();
		try {
			// pdf路径
			stream = file.getInputStream();
			// 加载解析PDF文件
			doc = PDDocument.load(stream, MemoryUsageSetting.setupTempFileOnly());
			doc.setResourceCache(new CustomDefaultResourceCache());
			PDFRenderer pdfRenderer = new PDFRenderer(doc);
			pdfRenderer.setSubsamplingAllowed(true);
			PDPageTree pages = doc.getPages();
			int pageCount = pages.getCount();
			//循环pdf页数转为图片上传
			for (int i = 0; i < pageCount; i++) {



				bim = pdfRenderer.renderImageWithDPI(i, 150);
				ByteArrayOutputStream	os = new ByteArrayOutputStream();
				ImageIO.write(bim, "jpg", os);
				byte[] dataList = os.toByteArray();
				// jpg文件转出路径
				File filejpg = new File(localPaths + substring +"_"+(i+1)+".jpg");
				if (!filejpg.getParentFile().exists()) {
					// 不存在则创建父目录及子文件
					filejpg.getParentFile().mkdirs();
					filejpg.createNewFile();
				}
				try {
					out = new FileOutputStream(filejpg);
					out.write(dataList);
				} finally {
					out.flush();
					out.close();
				}
				//压缩图片 转为800

				String imagePath= localPaths + substring +"_"+(i+1)+".jpg";
				String newPath= newPaths + substring +"_"+(i+1) +".jpg";
				//压缩图片
				gmPicWith800( imagePath, newPath);

				File fileimg = new File( newPath);
				File filevj = new File(  imagePath);
				tempToDeleteFileList.add(fileimg);
				tempToDeleteFileList.add(filevj);
				//  判断转换后的图片是否大于800k并报错
				if(Files.size(fileimg.toPath())>819200){
					return new ResultInfo(-1, "生成的小图大于800k，无法保存至附件，请重试或人工处理");
				}
				//图片上传
				FileInfo fileInfo = null;

				// 上传文件名称
				String fileName = fileimg.getName();
				if (fileName.length()>20){
					 fileName = fileName.substring(0,10) +".jpg";
				}

				// 文件后缀名称
				String prefix = fileName.substring(fileName.lastIndexOf(".") + 1);
				MultipartFile uplouadFile = getMultipartFile(new FileInputStream(fileimg), fileName);

				fileInfo = ossUtilsService.upload2Oss(request, uplouadFile);
				fileInfo.setDomain(fileInfo.getHttpUrl());
				fileInfo.setHttpUrl(api_http + fileInfo.getHttpUrl());
				fileInfo.setPrefix(prefix);
				fileInfo.setFileName(fileName);
				fileInfos.add(fileInfo);
			}

			//pdf源文件上传
			// 上传文件名称
			String fileName = orginFile.getOriginalFilename();
			// 文件后缀名称
			String prefix = fileName.substring(fileName.lastIndexOf(".") + 1);
			 FileInfo fileInfo = ossUtilsService.upload2Oss(request, orginFile);
			fileInfo.setDomain(fileInfo.getHttpUrl());
			fileInfo.setHttpUrl(api_http + fileInfo.getHttpUrl());
			fileInfo.setPrefix(prefix);
			fileInfo.setFileName(fileName);

			//成功返回转化的图片地址和pdf文件地址  与前端已经约定好
			ResultInfo<FileInfo> success = new ResultInfo<>(0, "上传成功", fileInfos);
			success.setData(fileInfo);
			return success;
		} catch (Exception e) {
			logger.warn("图片转化失败：", e);
			return new ResultInfo(-1, "系统处理异常，图片转化失败。");
		} finally {
			if (doc != null) {
				doc.close();
			}
			if (bim != null) {
				bim.flush();
			}
			if (stream != null) {
				stream.close();
			}
			if (out != null) {
				out.close();
			}
			tempToDeleteFileList.forEach(File::delete); // 删除临时文件
		}
	}

	private void gmPicWith800( String imagePath, String newPath) throws IOException, InterruptedException, IM4JavaException {
		IMOperation op = new IMOperation();
		op.addImage(imagePath);

		 // 根据宽度缩放图片
		op.resize(800,null);

		op.addImage(newPath);
		// IM4JAVA是同时支持GraphicsMagick和ImageMagick的，如果为true则使用GM，如果为false支持IM。
		ConvertCmd convert = new ConvertCmd(true);
		// 判断系统
		convert.setSearchPath(gmUrl);
		convert.run(op);
	}


	/**
	 * 移除文件（单文件移除）
	 * <p>Title: delFile</p>
	 * <p>Description: </p>
	 * @param request
	 * @param response
	 * @param path
	 * @param filename
	 * @return
	 * <AUTHOR>
	 * @date 2019年4月2日
	 */
	@SuppressWarnings("rawtypes")
	@ResponseBody
	@RequestMapping(value = "/delFile")
	public ResultInfo delFile(HttpServletRequest request, HttpServletResponse response, String path,String filename) {
		try {
			File file=new File(path+"/"+filename);
			if(file.exists() && file.isFile()){
				file.delete();
				return new ResultInfo<>(0, "删除成功");
			}
		} catch (Exception e) {
			logger.error("移除图片：",e);
		}
		return new ResultInfo<>(-1, "删除失败");
	}

	/**
	 * @description 获取所有新国标分类
	 * <AUTHOR>
	 * @param
	 * @date 2019/5/21
	 */
	@ResponseBody
	@RequestMapping(value = "/allstandard")
	@NoNeedAccessAuthorization
	public ResultJSON  getallStandard(HttpServletRequest request, HttpServletResponse response){
		try {
			// 获取所有新国标分类
			List<StandardCategory> newStand = firstEngageService.getallStandard();
			return ResultJSON.success().data(newStand);
		} catch (Exception e) {
			logger.error("获取所有新国标分类：",e);
			return ResultJSON.failed(e);
		}
	}


	@ResponseBody
	@RequestMapping(value = "/allcompany")
	public List<Map<String,Object>> getallcompany(HttpServletRequest request, HttpServletResponse response){
		try {
			// 获取所有新国标分类
			List<Map<String,Object>> companies = firstEngageService.getallcompany("");
			if(CollectionUtils.isNotEmpty(companies)){
				return companies;
			}
		} catch (Exception e) {
			logger.error("获取所有新国标分类：",e);
		}

		return Collections.emptyList();
	}

	/**
	 * @description 过期处理状态
	 * <AUTHOR>
	 * @param
	 * @date 2019/5/28
	 */
	@ResponseBody
	@RequestMapping(value = "/dealstatus")
	public ResultInfo dealstatus(HttpServletRequest request, Integer registrationNumberId){
		try {

			return firstEngageService.dealstatus(registrationNumberId);

		} catch (Exception e) {
			logger.error("过期处理状态：",e);
		}

		return new ResultInfo();
	}

	/**
	 * @description 首营信息审核
	 * <AUTHOR>
	 * @param
	 * @date 2019/6/10
	 */
	@ResponseBody
	@RequestMapping(value = "/check")
	public ResultJSON checkFirstengage(HttpServletRequest request, FirstEngage firstEngage) {
		try {
			User sessUser = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
			firstEngageService.checkFirstengage(request, firstEngage, sessUser);
		} catch (Exception e) {
			logger.error("首营信息审核：",e);
			return ResultJSON.failed().message(e.getMessage());
		}
		return ResultJSON.success().message("操作成功");
	}

	/**
	 * @description 首营信息审核
	 * <AUTHOR>
	 * @param
	 * @date 2019/6/10
	 */
	@ResponseBody
	@RequestMapping(value = "/submitCheck")
	public ResultJSON submitCheckFirstengage(HttpServletRequest request, FirstEngage firstEngage) {
		try {
			User sessUser = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
			firstEngageService.checkFirstengage(request, firstEngage, sessUser);
		} catch (Exception e) {
			logger.error("提交首营信息审核错误：",e);
			return ResultJSON.failed().message(e.getMessage());
		}
		return ResultJSON.success().message("操作成功");
	}



	/**
	 * @description 处理管理类别
	 * <AUTHOR>
	 * @param
	 * @date 2019/5/29
	 */
	private Map<String, Object> getManagermentMap(ModelAndView mv) {
		List<Integer> scopeList = new ArrayList<>();
		// 管理类别作用域
		scopeList.add(CommonConstants.SCOPE_1090);
		// 产品类型 作用域
		scopeList.add(CommonConstants.SCOPE_1035);
		// 存储条件 作用域
		scopeList.add(CommonConstants.SCOPE_1092);
		// 医疗类别 作用域 1020
		scopeList.add(CommonConstants.SCOPE_1020);

		// 管理类别
		Map<String,List<SysOptionDefinition>> sysOptionMap = sysOptionDefinitionService.getSysOptionDefinitionByParam(scopeList);

		List<SysOptionDefinition> levelList=sysOptionMap.get("1090");
		sysOptionMap.put("1090",levelList.stream().sorted(Comparator.comparing(SysOptionDefinition::getSysOptionDefinitionId)).collect(Collectors.toList()));
		// 参数
		Map<String, Object> paramMap = new HashMap<>();
		// 启用状态
		paramMap.put("status", CommonConstants.STATUS_1);
		// 一级类别
		paramMap.put("parentId", STANDARD_PARRNT_ID_0);

		// 新国标分类信息
		List<Map<String, Object>> newStandCategoryList = firstEngageService.getNewStandardCategory(paramMap);

		// 旧国标分类解析成json格式
		List<SysOptionDefinition> oldStand = sysOptionMap.get("1020");
		if(EmptyUtils.isEmpty(oldStand)){
			mv.addObject("oldStandCategoryList", null);
		}
		List<Map<String, Object>> oldStandCategoryList = new ArrayList<>();
		int oldSize = oldStand.size();
		// 遍历旧国标分类
		for(int i=0; i < oldSize; i++){
			SysOptionDefinition sysOptionDefinition = oldStand.get(i);
			Map<String, Object> dataMap = new HashMap<>();
			// 旧国标名称
			dataMap.put("label", sysOptionDefinition.getTitle());
			// id
			dataMap.put("value", sysOptionDefinition.getSysOptionDefinitionId());
			oldStandCategoryList.add(dataMap);
		}
		JSONArray oldJsonArray = new JSONArray(oldStandCategoryList);
		mv.addObject("oldStandCategoryList", oldJsonArray.toString());
		mv.addObject("sysOptionMap", sysOptionMap);
		JSONArray jsonArray = new JSONArray(newStandCategoryList);
		mv.addObject("newStandCategoryList", jsonArray.toString());
		return paramMap;
	}


	/**
	 * @description 附件信息
	 * <AUTHOR>
	 * @param
	 * @date 2019/5/29
	 */
	private void getAttachments(List<Attachment> attachments, ModelAndView mv, String name){
		// 空判断
		if(CollectionUtils.isNotEmpty(attachments)){
			// 定义返回值
			List<Map<String, Object>> resMapList = new ArrayList<>();
			int size = attachments.size();
			for (int i = 0; i < size; i++) {
				Attachment attachment = attachments.get(i);
				// 编辑注册证附件信息
				Map<String, Object> attachmentMap = new HashMap<>();
				attachmentMap.put("message", "操作成功");
				attachmentMap.put("httpUrl", api_http+domain);
				// uri
				String uri = attachment.getUri();
				if(EmptyUtils.isEmpty(uri)){
					continue;
				}
				String[] uriArray = uri.split("/");
				String fileName = uriArray[uriArray.length-1];
				String fileNameTemp = "/" + fileName;
				// 文件后缀
				String[] prefixArray = fileNameTemp.split("\\.");
				String prefix = prefixArray[prefixArray.length-1];
				// 去除路径名
				String filePath = uri.replaceAll(fileNameTemp, "");
				attachmentMap.put("fileName", fileName);
				attachmentMap.put("filePath", filePath);
				attachmentMap.put("prefix", prefix);
				resMapList.add(attachmentMap);
			}
			// 放入modelandview
			if(CollectionUtils.isNotEmpty(resMapList)){
				mv.addObject(name, new JSONArray(resMapList).toString());
			}
		}
	}


	@ResponseBody
	@RequestMapping(value = "/doRegistrationImg",method = RequestMethod.GET)
	public ResultInfo doRegistrationImg(String addTime, boolean rollback){
		ResultInfo resultInfo = new ResultInfo();
		Map<String,Object> map = new HashMap<>();
		try{
			//查询注册证列表
			List<Attachment> attachmentList = firstEngageService.queryRegistrationAttachmentList(addTime);
			AtomicInteger notExistsCount = new AtomicInteger();
			if (!rollback){
				if (CollectionUtils.isNotEmpty(attachmentList)){
					long beginTime = System.currentTimeMillis();
					Map<String,Object> downloadMap = new HashMap<>();
					Map<String,String> attachmentMap = new HashMap<>();
					String waterMarkImg = "/app/image/waterMarkImg.svg";
					String localPath = "/app/image/backup/";
					String newPath = "/app/image/new/";
					//遍历注册证信息,下载到本地
					AtomicInteger downSize = new AtomicInteger();
					AtomicInteger errorCount = new AtomicInteger();
					List<String> downloadErrorUrlList = new ArrayList<>();
					List<String> downloadNotExistUrlList = new ArrayList<>();
					logger.info("=======================attachmentList start download ====>");
					long downLoadBeginTime = System.currentTimeMillis();
					attachmentList.forEach(attachment -> {
						String fileName = attachment.getUri().substring(attachment.getUri().lastIndexOf("/") + 1,attachment.getUri().lastIndexOf("."));
						String prefix = attachment.getUri().substring(attachment.getUri().lastIndexOf("."));
						try {
							if (ftpClientHelper.verifyIfExist(attachment.getUri()) && !new File(localPath + fileName + prefix).exists()){
								ftpClientHelper.downloadFile(attachment.getUri(), localPath, fileName);
								attachmentMap.put(fileName + prefix,attachment.getUri());
								downSize.getAndIncrement();
							}else {
								notExistsCount.getAndIncrement();
								downloadNotExistUrlList.add(attachment.getUri());

							}
						} catch (Exception e) {
							errorCount.getAndIncrement();
							downloadErrorUrlList.add(attachment.getUri());
							logger.error("doRegistrationImg download is error：" , e);
						}
					});
					long downLoadEndTime = System.currentTimeMillis();
					logger.info("attachmentList download totalSize:{}",attachmentList.size());
					logger.info("attachmentList download success size:{}",downSize.get());
					logger.info("attachmentList download notExists size:{}",notExistsCount.get());
					logger.info("attachmentList download error size:{}",errorCount.get());
					logger.info("attachmentList download not exist urlList:{}", JSONObject.toJSONString(downloadNotExistUrlList));
					logger.info("attachmentList download error urlList:{}", JSONObject.toJSONString(downloadErrorUrlList));
					downloadMap.put("total_size",attachmentList.size());
					downloadMap.put("success_size",downSize.get());
					downloadMap.put("notExist_size",notExistsCount.get());
					downloadMap.put("error_size",errorCount.get());
					downloadMap.put("notExistList",downloadNotExistUrlList);
					downloadMap.put("errorList",downloadErrorUrlList);
					downloadMap.put("downloadTime",downLoadEndTime - downLoadBeginTime);
					map.put("downloadMap",downloadMap);
					logger.info("=======================attachmentList end download ====>");
					//遍历本地文件夹的文件，改名字后上传到ftp，再加水印后上传
					File file = new File(localPath);
					File[] files = file.listFiles();
					logger.info("attachmentList composite file totalSize:{}", files.length);
					int fileZeroLengthCount = 0;
					int compositeSize = 0;
					errorCount.set(0);
					Map<String,Object> compositeMap = new HashMap<>();
					List<String> compositeErrorUrlList = new ArrayList<>();
					List<String> compositeNotExistUrlList = new ArrayList<>();
					long compositeBeginTime = System.currentTimeMillis();
					if (files != null && files.length > 0){
						File newFile = new File(newPath);
						if (!newFile.exists()){
							newFile.mkdirs();
						}
						logger.info("=======================attachmentList start composite ====>");
						for (int i = 0; i<files.length; i++){
							FTPClient ftpClient = null;
							try {
								ftpClient = ftpClientHelper.getFtpClientPool().borrowObject();
							} catch (Exception e) {
								logger.error("attachmentList composite getFtpClientPool error:",e);
							}
							try{
								if (files[i].length() != 0){
									long compositeOneBeginTime = System.currentTimeMillis();
									String fileName = files[i].getAbsolutePath().substring(files[i].getAbsolutePath().lastIndexOf("/") + 1,files[i].getAbsolutePath().lastIndexOf("."));
									String prefix = files[i].getAbsolutePath().substring(files[i].getAbsolutePath().lastIndexOf("."));
									//String url = attachmentMap.get(files[i].getName());
									String uploadPath =  attachmentMap.get(files[i].getName()).substring(0, attachmentMap.get(files[i].getName()).lastIndexOf("/") + 1);
									String uploadFileName = fileName + "_bf" + prefix;
									String beforeFileName = fileName +  prefix;
									ftpClient.changeWorkingDirectory(uploadPath);
									ftpClient.rename(beforeFileName, uploadFileName);
									//ftpClientHelper.storeFile(files[i].getAbsolutePath(),uploadPath,uploadFileName);
									IMOperation op = new IMOperation();
									int width = ImageIO.read(files[i]).getWidth();
									op.gravity("center");
									op.addRawArgs("-resize",width + "x" + width * 1.59 + "!");
									op.background("none");
									op.tile(waterMarkImg);
									op.addImage(files[i].getAbsolutePath());
									op.addImage(newPath + beforeFileName);
									CompositeCmd convert = new CompositeCmd(true);
									convert.setSearchPath(gmUrl);
									convert.run(op);
									logger.info("=======================attachmentList composite url:{}",files[i].getAbsolutePath());
									//将水印图上传到FTP
									ftpClientHelper.storeFile(newPath + beforeFileName, uploadPath, beforeFileName);
									//删除本地缓存文件
									//new File(files[i].getAbsolutePath()).delete();
									//new File(newPath + beforeFileName).delete();
									compositeSize ++;
									long compositeOneEndTime = System.currentTimeMillis();
									logger.info("attachmentList one composite time:{}",compositeOneEndTime - compositeOneBeginTime);
									logger.info("attachmentList compositeSize last:{}",files.length - compositeSize);
								}else {
									fileZeroLengthCount ++ ;
									compositeNotExistUrlList.add(attachmentMap.get(files[i].getName()));
								}
							}catch (Exception e){
								errorCount.getAndIncrement();
								compositeErrorUrlList.add(attachmentMap.get(files[i].getName()));
								logger.error("attachmentList composite error:", e);
							}finally {
								if (ftpClient != null){
									try {
										ftpClient.changeWorkingDirectory("/");
									} catch (IOException e) {
										logger.error("attachmentList composite ftpClient changeWorkingDirectory error:",e);
									}
									ftpClientHelper.getFtpClientPool().returnObject(ftpClient);
								}
							}
						}
					}
					long compositeEndTime = System.currentTimeMillis();
					logger.info("attachmentList composite success size:{}", compositeSize);
					logger.info("attachmentList composite fileZeroLength size:{}", fileZeroLengthCount);
					logger.info("attachmentList composite error size:{}", errorCount.get());
					logger.info("attachmentList composite fileZeroLength urlList:{}", JSONObject.toJSONString(compositeNotExistUrlList));
					logger.info("attachmentList composite error urlList:{}", JSONObject.toJSONString(compositeErrorUrlList));
					logger.info("=======================attachmentList end composite ====>");
					compositeMap.put("total_size",files.length);
					compositeMap.put("success_size",compositeSize);
					compositeMap.put("zeroLength_size",fileZeroLengthCount);
					compositeMap.put("error_size",errorCount.get());
					compositeMap.put("notExistList",compositeNotExistUrlList);
					compositeMap.put("errorList",compositeErrorUrlList);
					compositeMap.put("compositeTime",compositeEndTime - compositeBeginTime);
					map.put("compositeMap",compositeMap);
					long endTime = System.currentTimeMillis();
					map.put("useTime",endTime - beginTime);
				}
			} else {
				//数据回滚，重命名备份的文件，改为以前的名字
				logger.info("attachmentList start rollback================================>");
				for (int i = 0; i< attachmentList.size(); i++){
					Attachment attachment = attachmentList.get(i);
					FTPClient ftpClient = null;
					try {
						ftpClient = ftpClientHelper.getFtpClientPool().borrowObject();
					} catch (Exception e) {
						logger.error("attachmentList rollback getFtpClientPool error:",e);
					}
					try {
						String workingDirectory = attachment.getUri().substring(0, attachment.getUri().lastIndexOf("/")+1);
						String fileName = attachment.getUri().substring(attachment.getUri().lastIndexOf("/") + 1,attachment.getUri().lastIndexOf("."));
						String prefix = attachment.getUri().substring(attachment.getUri().lastIndexOf("."));
						String bfPath = workingDirectory + fileName + "_bf" + prefix;
						if (ftpClientHelper.verifyIfExist(bfPath)){
							ftpClient.changeWorkingDirectory(workingDirectory);
							ftpClientHelper.deleteFile(fileName + prefix);
							ftpClient.rename(fileName + "_bf" + prefix, fileName + prefix);
							logger.info("attachmentList rollback url:{}",attachment.getUri());
						}else {
							logger.info("attachmentList rollback not exist url:{}",attachment.getUri());
						}
					} catch (Exception e) {
						logger.error("attachmentList rollback is error:",e);
					}finally {
						if (ftpClient != null){
							try {
								ftpClient.changeWorkingDirectory("/");
							} catch (IOException e) {
								logger.error("attachmentList rollback ftpClient changeWorkingDirectory error:",e);
							}
							ftpClientHelper.getFtpClientPool().returnObject(ftpClient);
						}
					}
				}
			}
			resultInfo.setCode(CommonConstants.SUCCESS_CODE);
			resultInfo.setMessage(CommonConstants.SUCCESS_MSG);
			resultInfo.setData(map);
			logger.info("attachmentList map{}",JSONObject.toJSONString(map));
			return resultInfo;
		}catch (Exception e){
			logger.error("attachmentList error:",e);
			resultInfo.setCode(CommonConstants.FAIL_CODE);
			resultInfo.setMessage(CommonConstants.FAIL_MSG);
			return resultInfo;
		}
	}

	/**
	 * @Title doRegistnumpdfImg
	 * @Description 处理特殊目录下的文件
	 * @Params [addTime, rollback]
	 * <AUTHOR>
	 * @Date 2020/7/16 10:19 上午
	 * @return void
	 **/
	@ResponseBody
	@RequestMapping(value = "/doRegistnumpdfImg",method = RequestMethod.GET)
	public void doRegistnumpdfImg(String addTime, boolean rollback){
		try{
			String waterMarkImg = "/app/image/waterMarkImg.svg";
			String localPath = "/app/image/backup/";
			String newPath = "/app/image/new/";
			//查询注册证列表
			List<Attachment> attachmentList = firstEngageService.queryRegistrationAttachmentList(addTime);
			//筛选出目录中包含registnumpdf的文件
			List<Attachment> registnumpdfList = new ArrayList<>();
			if (CollectionUtils.isNotEmpty(attachmentList)){
				for (int i = 0; i < attachmentList.size(); i++){
					Attachment attachment = new Attachment();
					if (attachment.getUri().contains("registnumpdf")){
						registnumpdfList.add(attachment);
					}
				}
				logger.info("contains registnumpdf list:{}",JSONObject.toJSONString(registnumpdfList));
				if (CollectionUtils.isNotEmpty(registnumpdfList)){
					registnumpdfList.forEach(attachment -> {
						FTPClient ftpClient = null;
						try {
							ftpClient = ftpClientHelper.getFtpClientPool().borrowObject();
						} catch (Exception e) {
							logger.error("registnumpdf getFtpClientPool error:",e);
						}
						String url = attachment.getUri();
						String uploadPath =  url.substring(0, url.lastIndexOf("/") + 1);
						String fileName = url.substring(url.lastIndexOf("/") + 1,url.lastIndexOf("."));
						String prefix = url.substring(url.lastIndexOf("."));
						File file = new File(newPath + fileName + prefix);
						int existNum = 0;
						int notExistNum = 0;
						//加过水印的里面存在图片，将水印图片上传并备份,不存在，下载后，加水印上传
						if (file.exists()){
							try {
								ftpClient.changeWorkingDirectory(uploadPath);
								ftpClient.rename(fileName + prefix + prefix, fileName + "_bf" + prefix);
								ftpClientHelper.storeFile(newPath + fileName + prefix, uploadPath, fileName + prefix);
								logger.info("registnumpdf upload url:{}",url);
								existNum ++;
							} catch (Exception e) {
								logger.error("registnumpdf upload is error:", e);
							}finally {
								if (ftpClient != null){
									try {
										ftpClient.changeWorkingDirectory("/");
									} catch (IOException e) {
										logger.error("registnumpdf ftpClient changeWorkingDirectory error:",e);
									}
									ftpClientHelper.getFtpClientPool().returnObject(ftpClient);
								}
							}
						}else {
							try {
								//下载
								ftpClientHelper.downloadFile(url, localPath, fileName);
								logger.info("registnumpdf download url:{}",url);
								//加水印
								IMOperation op = new IMOperation();
								int width = ImageIO.read(file).getWidth();
								op.gravity("center");
								op.addRawArgs("-resize",width + "x" + width * 1.59 + "!");
								op.background("none");
								op.tile(waterMarkImg);
								op.addImage(localPath + fileName + prefix);
								op.addImage(newPath + fileName + prefix);
								CompositeCmd convert = new CompositeCmd(true);
								convert.setSearchPath(gmUrl);
								convert.run(op);
								logger.info("registnumpdf composite url:{}",url);
								//上传并备份
								ftpClient.rename(fileName + prefix + prefix, fileName + "_bf" + prefix);
								ftpClient.deleteFile(fileName + prefix);
								ftpClientHelper.storeFile(newPath + fileName + prefix, uploadPath, fileName + prefix);
								logger.info("registnumpdf upload url:{}",url);
								notExistNum ++;
							} catch (Exception e) {
								logger.error("registnumpdf download and composite and upload is error:", e);
							}finally {
								if (ftpClient != null){
									try {
										ftpClient.changeWorkingDirectory("/");
									} catch (IOException e) {
										logger.error("registnumpdf ftpClient changeWorkingDirectory error:",e);
									}
									ftpClientHelper.getFtpClientPool().returnObject(ftpClient);
								}
							}
						}
						logger.info("registnumpdf existNum:{},notExistNum:{}",existNum,notExistNum);
					});
				}
			}
		}catch (Exception e){
			logger.error("doRegistnumpdfImg  is error:", e);
		}
	}

	//上传 注册证附件/备案凭证附件（贝） ATTACHMENT_FUNCTION_1001 addFirstEngageInfo
	@FormToken(remove = true)
	@RequestMapping("/addZCZJB")
	@ResponseBody
	public ResultInfo addZCZJB(HttpServletRequest request, FirstEngage firstEngage){
		ResultInfo resultInfo = new ResultInfo();
		try {
			// 当前登陆用户
			User sessUser = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
			// 添加首营商品信息，返回新增的首营id
			firstEngageService.addZCZJB(firstEngage, sessUser);
			resultInfo.setCode(0);
			resultInfo.setMessage("操作成功");
		} catch (Exception e) {
			logger.error("操作异常:", e);
			resultInfo.setMessage("操作失败");
			return resultInfo;
		}

		return resultInfo;
	}


}
