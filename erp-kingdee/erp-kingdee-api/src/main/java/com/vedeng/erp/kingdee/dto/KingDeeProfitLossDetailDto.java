package com.vedeng.erp.kingdee.dto;

import lombok.*;

/**
 * 盈亏单 dto  https://www.yuque.com/manhuo/gf1570/gen125
 * <AUTHOR>
 * @version 1.0
 * @description: 预处理金蝶盈亏单 dto  由erp实际业务转换
 * @date
 */
@Getter
@Setter
public class KingDeeProfitLossDetailDto {
    /**
     * 物料
     */
    private String fMaterialId;
    /**
     * 仓库
     */
    private String fStockId;
    /**
     * 仓库状态
     */
    private String fStockStatusId;
    /**
     * 盘亏数量
     */
    private String fBaseLossQty;
    /**
     * 原始订单号
     */
    private String fQzokYsddh;
    /**
     * 归属业务单号
     */
    private String fQzokGsywdh;
    /**
     * 业务类型
     */
    private String fQzokYwlx;
    /**
     * 批次号
     */
    private String fQzokPch;
    /**
     * 序列号
     */
    private String fQzokXlh;
    /**
     * 授权类型
     */
    private String fQzokSqlx;
    /**
     * 是否直发
     */
    private String fQzokSfzf;


    public KingDeeProfitLossDetailDto() {
        this.fStockId = "CK9999";
        this.fStockStatusId = "KCZT01_SYS";
        this.fQzokYwlx = "盘亏单";
    }

}
