package com.vedeng.order.dao;

import com.vedeng.order.model.ge.VBuyorderSncodeSlave;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface VBuyorderSncodeSlaveMapper {

    int delGeSlaveInfoCollection(Integer buyorderSncodeMasterId);

    int insertSelective(VBuyorderSncodeSlave record);

    List<VBuyorderSncodeSlave> getGeSlaveInfo(Integer MasterId);

    List<VBuyorderSncodeSlave> checkSlaveSno(@Param("slaveSno") String slaveSno,@Param("masterSnoCodeList") List<Integer> masterSnoCodeList );
}