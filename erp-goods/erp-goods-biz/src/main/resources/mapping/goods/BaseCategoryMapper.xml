<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.mapper.BaseCategoryMapper">

    <resultMap id="BaseResultMap" type="com.vedeng.goods.domain.entity.BaseCategory">
            <id property="baseCategoryId" column="BASE_CATEGORY_ID" jdbcType="INTEGER"/>
            <result property="baseCategoryName" column="BASE_CATEGORY_NAME" jdbcType="VARCHAR"/>
            <result property="baseCategoryNickname" column="BASE_CATEGORY_NICKNAME" jdbcType="VARCHAR"/>
            <result property="baseCategoryLevel" column="BASE_CATEGORY_LEVEL" jdbcType="TINYINT"/>
            <result property="baseCategoryType" column="BASE_CATEGORY_TYPE" jdbcType="TINYINT"/>
            <result property="baseCategoryExampleProduct" column="BASE_CATEGORY_EXAMPLE_PRODUCT" jdbcType="VARCHAR"/>
            <result property="baseCategoryDescribe" column="BASE_CATEGORY_DESCRIBE" jdbcType="VARCHAR"/>
            <result property="baseCategoryIntendedUse" column="BASE_CATEGORY_INTENDED_USE" jdbcType="VARCHAR"/>
            <result property="isDeleted" column="IS_DELETED" jdbcType="TINYINT"/>
            <result property="parentId" column="PARENT_ID" jdbcType="INTEGER"/>
            <result property="treenodes" column="TREENODES" jdbcType="VARCHAR"/>
            <result property="creator" column="CREATOR" jdbcType="INTEGER"/>
            <result property="updater" column="UPDATER" jdbcType="INTEGER"/>
            <result property="modTime" column="MOD_TIME" jdbcType="TIMESTAMP"/>
            <result property="addTime" column="ADD_TIME" jdbcType="TIMESTAMP"/>
            <result property="taxClassificationCode" column="TAX_CLASSIFICATION_CODE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        BASE_CATEGORY_ID,BASE_CATEGORY_NAME,BASE_CATEGORY_NICKNAME,
        BASE_CATEGORY_LEVEL,BASE_CATEGORY_TYPE,BASE_CATEGORY_EXAMPLE_PRODUCT,
        BASE_CATEGORY_DESCRIBE,BASE_CATEGORY_INTENDED_USE,IS_DELETED,
        PARENT_ID,TREENODES,CREATOR,
        UPDATER,MOD_TIME,ADD_TIME,TAX_CLASSIFICATION_CODE
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from V_BASE_CATEGORY
        where  BASE_CATEGORY_ID = #{baseCategoryId,jdbcType=INTEGER} 
    </select>

    <select id="findLevelCategory" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from V_BASE_CATEGORY
        where IS_DELETED = #{isDelete}
        and BASE_CATEGORY_LEVEL = #{level}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from V_BASE_CATEGORY
        where  BASE_CATEGORY_ID = #{baseCategoryId,jdbcType=INTEGER} 
    </delete>
    <insert id="insert" keyColumn="BASE_CATEGORY_ID" keyProperty="baseCategoryId" parameterType="com.vedeng.goods.domain.entity.BaseCategory" useGeneratedKeys="true">
        insert into V_BASE_CATEGORY
        ( BASE_CATEGORY_ID,BASE_CATEGORY_NAME,BASE_CATEGORY_NICKNAME
        ,BASE_CATEGORY_LEVEL,BASE_CATEGORY_TYPE,BASE_CATEGORY_EXAMPLE_PRODUCT
        ,BASE_CATEGORY_DESCRIBE,BASE_CATEGORY_INTENDED_USE,IS_DELETED
        ,PARENT_ID,TREENODES,CREATOR
        ,UPDATER,MOD_TIME,ADD_TIME
        )
        values (#{baseCategoryId,jdbcType=INTEGER},#{baseCategoryName,jdbcType=VARCHAR},#{baseCategoryNickname,jdbcType=VARCHAR}
        ,#{baseCategoryLevel,jdbcType=TINYINT},#{baseCategoryType,jdbcType=TINYINT},#{baseCategoryExampleProduct,jdbcType=VARCHAR}
        ,#{baseCategoryDescribe,jdbcType=VARCHAR},#{baseCategoryIntendedUse,jdbcType=VARCHAR},#{isDeleted,jdbcType=TINYINT}
        ,#{parentId,jdbcType=INTEGER},#{treenodes,jdbcType=VARCHAR},#{creator,jdbcType=INTEGER}
        ,#{updater,jdbcType=INTEGER},#{modTime,jdbcType=TIMESTAMP},#{addTime,jdbcType=TIMESTAMP}
        )
    </insert>
    <insert id="insertSelective" keyColumn="BASE_CATEGORY_ID" keyProperty="baseCategoryId" parameterType="com.vedeng.goods.domain.entity.BaseCategory" useGeneratedKeys="true">
        insert into V_BASE_CATEGORY
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="baseCategoryId != null">BASE_CATEGORY_ID,</if>
                <if test="baseCategoryName != null">BASE_CATEGORY_NAME,</if>
                <if test="baseCategoryNickname != null">BASE_CATEGORY_NICKNAME,</if>
                <if test="baseCategoryLevel != null">BASE_CATEGORY_LEVEL,</if>
                <if test="baseCategoryType != null">BASE_CATEGORY_TYPE,</if>
                <if test="baseCategoryExampleProduct != null">BASE_CATEGORY_EXAMPLE_PRODUCT,</if>
                <if test="baseCategoryDescribe != null">BASE_CATEGORY_DESCRIBE,</if>
                <if test="baseCategoryIntendedUse != null">BASE_CATEGORY_INTENDED_USE,</if>
                <if test="isDeleted != null">IS_DELETED,</if>
                <if test="parentId != null">PARENT_ID,</if>
                <if test="treenodes != null">TREENODES,</if>
                <if test="creator != null">CREATOR,</if>
                <if test="updater != null">UPDATER,</if>
                <if test="modTime != null">MOD_TIME,</if>
                <if test="addTime != null">ADD_TIME,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="baseCategoryId != null">#{baseCategoryId,jdbcType=INTEGER},</if>
                <if test="baseCategoryName != null">#{baseCategoryName,jdbcType=VARCHAR},</if>
                <if test="baseCategoryNickname != null">#{baseCategoryNickname,jdbcType=VARCHAR},</if>
                <if test="baseCategoryLevel != null">#{baseCategoryLevel,jdbcType=TINYINT},</if>
                <if test="baseCategoryType != null">#{baseCategoryType,jdbcType=TINYINT},</if>
                <if test="baseCategoryExampleProduct != null">#{baseCategoryExampleProduct,jdbcType=VARCHAR},</if>
                <if test="baseCategoryDescribe != null">#{baseCategoryDescribe,jdbcType=VARCHAR},</if>
                <if test="baseCategoryIntendedUse != null">#{baseCategoryIntendedUse,jdbcType=VARCHAR},</if>
                <if test="isDeleted != null">#{isDeleted,jdbcType=TINYINT},</if>
                <if test="parentId != null">#{parentId,jdbcType=INTEGER},</if>
                <if test="treenodes != null">#{treenodes,jdbcType=VARCHAR},</if>
                <if test="creator != null">#{creator,jdbcType=INTEGER},</if>
                <if test="updater != null">#{updater,jdbcType=INTEGER},</if>
                <if test="modTime != null">#{modTime,jdbcType=TIMESTAMP},</if>
                <if test="addTime != null">#{addTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.domain.entity.BaseCategory">
        update V_BASE_CATEGORY
        <set>
                <if test="baseCategoryName != null">
                    BASE_CATEGORY_NAME = #{baseCategoryName,jdbcType=VARCHAR},
                </if>
                <if test="baseCategoryNickname != null">
                    BASE_CATEGORY_NICKNAME = #{baseCategoryNickname,jdbcType=VARCHAR},
                </if>
                <if test="baseCategoryLevel != null">
                    BASE_CATEGORY_LEVEL = #{baseCategoryLevel,jdbcType=TINYINT},
                </if>
                <if test="baseCategoryType != null">
                    BASE_CATEGORY_TYPE = #{baseCategoryType,jdbcType=TINYINT},
                </if>
                <if test="baseCategoryExampleProduct != null">
                    BASE_CATEGORY_EXAMPLE_PRODUCT = #{baseCategoryExampleProduct,jdbcType=VARCHAR},
                </if>
                <if test="baseCategoryDescribe != null">
                    BASE_CATEGORY_DESCRIBE = #{baseCategoryDescribe,jdbcType=VARCHAR},
                </if>
                <if test="baseCategoryIntendedUse != null">
                    BASE_CATEGORY_INTENDED_USE = #{baseCategoryIntendedUse,jdbcType=VARCHAR},
                </if>
                <if test="isDeleted != null">
                    IS_DELETED = #{isDeleted,jdbcType=TINYINT},
                </if>
                <if test="parentId != null">
                    PARENT_ID = #{parentId,jdbcType=INTEGER},
                </if>
                <if test="treenodes != null">
                    TREENODES = #{treenodes,jdbcType=VARCHAR},
                </if>
                <if test="creator != null">
                    CREATOR = #{creator,jdbcType=INTEGER},
                </if>
                <if test="updater != null">
                    UPDATER = #{updater,jdbcType=INTEGER},
                </if>
                <if test="modTime != null">
                    MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
                </if>
                <if test="addTime != null">
                    ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   BASE_CATEGORY_ID = #{baseCategoryId,jdbcType=INTEGER} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.domain.entity.BaseCategory">
        update V_BASE_CATEGORY
        set 
            BASE_CATEGORY_NAME =  #{baseCategoryName,jdbcType=VARCHAR},
            BASE_CATEGORY_NICKNAME =  #{baseCategoryNickname,jdbcType=VARCHAR},
            BASE_CATEGORY_LEVEL =  #{baseCategoryLevel,jdbcType=TINYINT},
            BASE_CATEGORY_TYPE =  #{baseCategoryType,jdbcType=TINYINT},
            BASE_CATEGORY_EXAMPLE_PRODUCT =  #{baseCategoryExampleProduct,jdbcType=VARCHAR},
            BASE_CATEGORY_DESCRIBE =  #{baseCategoryDescribe,jdbcType=VARCHAR},
            BASE_CATEGORY_INTENDED_USE =  #{baseCategoryIntendedUse,jdbcType=VARCHAR},
            IS_DELETED =  #{isDeleted,jdbcType=TINYINT},
            PARENT_ID =  #{parentId,jdbcType=INTEGER},
            TREENODES =  #{treenodes,jdbcType=VARCHAR},
            CREATOR =  #{creator,jdbcType=INTEGER},
            UPDATER =  #{updater,jdbcType=INTEGER},
            MOD_TIME =  #{modTime,jdbcType=TIMESTAMP},
            ADD_TIME =  #{addTime,jdbcType=TIMESTAMP}
        where   BASE_CATEGORY_ID = #{baseCategoryId,jdbcType=INTEGER} 
    </update>

<!--auto generated by MybatisCodeHelper on 2023-08-16-->
    <select id="selectByBaseCategoryLevel" resultType="com.vedeng.goods.dto.CategoryFrontDto">
        select
        BASE_CATEGORY_ID,BASE_CATEGORY_NAME,
        BASE_CATEGORY_LEVEL,
        PARENT_ID,TREENODES
        from V_BASE_CATEGORY
        where BASE_CATEGORY_LEVEL=#{baseCategoryLevel,jdbcType=TINYINT} and IS_DELETED =0
    </select>

<!--auto generated by MybatisCodeHelper on 2023-08-16-->
    <select id="selectByParentId" resultType="com.vedeng.goods.dto.CategoryFrontDto">
        select
        BASE_CATEGORY_ID,BASE_CATEGORY_NAME,
        BASE_CATEGORY_LEVEL,
        PARENT_ID,TREENODES
        from V_BASE_CATEGORY
        where PARENT_ID=#{parentId,jdbcType=INTEGER} and IS_DELETED = 0
    </select>

<!--auto generated by MybatisCodeHelper on 2023-08-17-->
    <select id="findByBaseCategoryIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from V_BASE_CATEGORY
        where BASE_CATEGORY_ID in
        <foreach collection="lists" item="item" separator="," close=")" open="(">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>
    <select id="getByParentIdList" resultType="com.vedeng.goods.domain.entity.BaseCategory">
        select
        <include refid="Base_Column_List"/>
        from V_BASE_CATEGORY
        where PARENT_ID in
        <foreach collection="parentIdList" item="item" separator="," close=")" open="(">
            #{item,jdbcType=INTEGER}
        </foreach>
        and IS_DELETED = 0
    </select>

    <select id="findParentByBaseCategoryIds" resultMap="BaseResultMap">
        select
        b.BASE_CATEGORY_ID, b.PARENT_ID
        from V_BASE_CATEGORY a left join V_BASE_CATEGORY b on a.PARENT_ID = b.BASE_CATEGORY_ID
        where a.BASE_CATEGORY_ID in
        <foreach collection="lists" item="item" separator="," close=")" open="(">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>




    <select id="queryThreeCategoryByKeyword" resultType="com.vedeng.goods.dto.CategoryResultDto">
        SELECT
            x.BASE_CATEGORY_ID as categoryId,
            x.NAME as categoryName,
            (SUM(IF(x.SPU_LEVEL = 1, x.NUM, 0))+SUM(IF(x.SPU_LEVEL = 2, x.NUM, 0))+SUM(IF(x.SPU_LEVEL = 0, x.NUM, 0))) AS countNum
        FROM
            (
                SELECT
                    a.BASE_CATEGORY_ID,
                    a.BASE_CATEGORY_NAME,
                    CONCAT(e.BASE_CATEGORY_NAME,'/',d.BASE_CATEGORY_NAME,'/',a.BASE_CATEGORY_NAME)  as NAME,
                    a.BASE_CATEGORY_TYPE,
                    a.BASE_CATEGORY_LEVEL,
                    a.PARENT_ID,
                    a.TREENODES,
                    b.SPU_LEVEL,
                    COUNT(c.SKU_ID) AS NUM
                FROM
                    V_BASE_CATEGORY a
                        LEFT JOIN V_BASE_CATEGORY d ON a.PARENT_ID = d.BASE_CATEGORY_ID  AND d.IS_DELETED = 0
                        LEFT JOIN V_BASE_CATEGORY e ON d.PARENT_ID = e.BASE_CATEGORY_ID AND e.IS_DELETED = 0
                        LEFT JOIN V_CORE_SPU b ON a.BASE_CATEGORY_ID = b.CATEGORY_ID AND b.CHECK_STATUS != 4
        AND b. STATUS = 1 AND b.SPU_LEVEL IS NOT NULL
        LEFT JOIN V_CORE_SKU c ON b.SPU_ID = c.SPU_ID AND c.SKU_ID IS NOT NULL
                    AND c.CHECK_STATUS != 4
                    AND c. STATUS = 1
                WHERE
                    a.IS_DELETED = 0
                  AND a.BASE_CATEGORY_LEVEL = 3
                  AND  (
                    a.BASE_CATEGORY_NAME LIKE CONCAT('%','${keyword}','%')
                   OR d.BASE_CATEGORY_NAME LIKE CONCAT('%','${keyword}','%')
                   OR e.BASE_CATEGORY_NAME LIKE CONCAT('%','${keyword}','%')
                    )
                GROUP BY
                    a.BASE_CATEGORY_ID,
                    a.BASE_CATEGORY_NAME,

                    a.BASE_CATEGORY_TYPE,
                    a.BASE_CATEGORY_LEVEL,
                    a.PARENT_ID,
                    a.TREENODES,
                    b.SPU_LEVEL
                ORDER BY
                    a.ADD_TIME DESC
            ) x
        GROUP BY
            x.BASE_CATEGORY_ID,
            x.BASE_CATEGORY_NAME,
            x.NAME,
            x.BASE_CATEGORY_TYPE,
            x.BASE_CATEGORY_LEVEL,
            x.PARENT_ID,
            x.TREENODES
    </select>


    <select id="getCategoryIdByFullPath" resultType="java.lang.Integer">
        SELECT c.BASE_CATEGORY_ID 
        FROM V_BASE_CATEGORY a
        LEFT JOIN V_BASE_CATEGORY b ON a.BASE_CATEGORY_ID = b.PARENT_ID
        LEFT JOIN V_BASE_CATEGORY c ON b.BASE_CATEGORY_ID = c.PARENT_ID
        WHERE a.BASE_CATEGORY_LEVEL = 1 
        AND b.BASE_CATEGORY_LEVEL = 2
        AND c.BASE_CATEGORY_LEVEL = 3
        AND a.IS_DELETED = 0
        AND b.IS_DELETED = 0
        AND c.IS_DELETED = 0
        AND CONCAT(a.BASE_CATEGORY_NAME, '/', b.BASE_CATEGORY_NAME, '/', c.BASE_CATEGORY_NAME) = #{fullCategoryPath}
        LIMIT 1
    </select>

    <select id="getAllLevelCategoryNameById" resultType="java.lang.String">
        SELECT CONCAT(C1.BASE_CATEGORY_NAME,'/',C2.BASE_CATEGORY_NAME,'/',C3.BASE_CATEGORY_NAME)
        FROM V_BASE_CATEGORY C3
        LEFT JOIN V_BASE_CATEGORY C2 ON C3.PARENT_ID=C2.BASE_CATEGORY_ID
        LEFT JOIN V_BASE_CATEGORY C1 ON C2.PARENT_ID=C1.BASE_CATEGORY_ID
        WHERE C3.BASE_CATEGORY_LEVEL=3 
        AND C3.IS_DELETED=0 
        AND C3.BASE_CATEGORY_ID=#{categoryId}
        LIMIT 1
    </select>

</mapper>
