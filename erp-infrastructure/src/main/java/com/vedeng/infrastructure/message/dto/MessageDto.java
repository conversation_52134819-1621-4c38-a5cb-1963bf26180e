package com.vedeng.infrastructure.message.dto;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/7/18
 */
@Data
@Setter
@Getter
public class MessageDto {

    private Integer messageTemplateId;
    private List<Integer> userIds;

    private Map<String, String> params;
    private String url;

}
