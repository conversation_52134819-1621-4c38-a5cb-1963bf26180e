<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="批量维护报备信息" scope="application"/>
<%@ include file="../../common/common.jsp" %>

<form id="quotePayMoneForm" onsubmit="false">
    <div class="parts" id="updateTerminalInfo">
        <div class="formtitle">&nbsp;&nbsp;&nbsp;终端信息</div>
        <ul class="payplan">
            <li id="terminalNameDetail">

                <div class="infor_name">
                    <span>*</span>
                    <label>终端名称</label>
                </div>
                <div class="f_left">
                    <div class=" inputfloat">
                        <input id="terminalTraderName" name="terminalTraderName" placeholder="请输入终端名称"
                               <c:if test="${quoteorder.customerNature == 466}">value="${quoteorder.traderName}" disabled</c:if>
                               <c:if test="${quoteorder.customerNature != 466}">value="${quoteorder.terminalTraderName}" </c:if>
                                            >
                        <c:if test="${quoteorder.customerNature != 466}">

                            <label id="searchTerminal" class="bt-bg-style bg-light-blue bt-small"
                                   onclick="searchTerminal();">搜索</label>
                        </c:if>
                    </div>
                </div>
                <c:if test="${quoteorder.customerNature != 466}">
                    <input type="checkbox" onclick="terminalTraderNameUnknown()"> 未知
                </c:if>
                <label style="color: red" id="terminalTraderNameError"></label>
            </li>
            <li>
                <div class="infor_name ">
                    <span>*</span>
                    <label>销售区域</label>
                </div>
                <div class="f_left">
                    <select class="input-small f_left mr10" name="province" id="province"
                            <c:if test="${quoteorder.customerNature == 466}">disabled</c:if> >
                        <option value="0">请选择</option>
                        <c:if test="${not empty provinceList}">
                            <c:forEach items="${provinceList}" var="prov">
                                <option value="${prov.regionId }"
                                        <c:if test="${provinceRegion.regionId eq prov.regionId }">selected="selected"</c:if>>${prov.regionName }</option>
                            </c:forEach>
                        </c:if>
                    </select>
                    <select class="input-small f_left mr10" name="city" id="city"
                            <c:if test="${quoteorder.customerNature == 466}">disabled</c:if> >
                        <option value="0">请选择</option>
                        <c:forEach items="${cityList}" var="city">
                            <option value="${city.regionId}"
                                    <c:if test="${cityRegion.regionId eq city.regionId }">selected="selected"</c:if>>${city.regionName }</option>
                        </c:forEach>
                    </select>
                    <select class="input-small f_left" name="zone" id="zone"
                            <c:if test="${quoteorder.customerNature == 466}">disabled</c:if>>
                        <option value="0">请选择</option>
                        <c:forEach items="${zoneList}" var="zone">
                            <option value="${zone.regionId }"
                                    <c:if test="${zoneRegion.regionId eq zone.regionId }">selected="selected"</c:if>>${zone.regionName }</option>
                        </c:forEach>
                    </select>


                    <div id="sales_area_msg_div" style="clear:both"></div>
                </div>&nbsp;&nbsp;
                <c:if test="${quoteorder.customerNature != 466}">
                    <input type="checkbox" onclick="terminalAreaUnknown()"> 未知
                </c:if>
                <label style="color: red" id="terminalAreaError"></label>
            </li>

            <li id="">

                <div class="infor_name">
                    <span>*</span>
                    <label>报备终端类型</label>
                </div>
                <div class="f_left">
                    <div class=" inputfloat" id="errorMsg">
                        <select id="terminalType" class="input-small f_left mr10" name="terminalType">
                            <option value="0">请选择</option>
                            <c:forEach items="${terminalTypes}" var="terminalType">
                                <option value="${terminalType.sysOptionDefinitionId}"
                                        <c:if test="${quoteorder.terminalType == terminalType.sysOptionDefinitionId}">selected</c:if>>
                                        ${terminalType.title}</option>
                            </c:forEach>
                        </select>
                    </div>
                </div>
                <c:if test="${quoteorder.customerNature != 466}">
                    <input type="checkbox" onclick="terminalTypeUnKnown()"> 未知
                </c:if>
                <label style="color: red" id="terminalTypeError"></label>
            </li>
        </ul>
        <c:if test="${quoteorder.customerNature == 466}">
            <label style="font-weight: bolder">
                【注：该客户为终端，终端名称和销售区域可前往
                <a href="javascript:void(0)" onclick="viewTraderInfo()">客户基本信息</a>
                修改。】
            </label>
        </c:if>

        <div class="mb15" style="margin-top: 12px;">
            <div class="add-tijiao tcenter">
                <button type="button" class="bt-bg-style bg-light-green bt-small mr10" onclick="myFormSubnit()">确定</button>
                <button class="dele" type="button" id="close-layer">取消</button>
            </div>
        </div>
    </div>
    <input id="customerNature" value="${quoteorder.customerNature}" type="hidden">
    <input id="quoteorderId" name="quoteorderId" value="${quoteorder.quoteorderId}" type="hidden">
    <input id="terminalTraderId" name="terminalTraderId" value="${quoteorder.terminalTraderId}" type="hidden">
</form>

<script>
    $(function () {
        window.getProvinceData = function (val, callback) {
            var regionId = val;
            if (regionId > 0) {
                $.ajax({
                    type: "POST",
                    url: page_url + "/system/region/getregion.do",
                    data: {'regionId': regionId},
                    dataType: 'json',
                    success: function (data) {
                        $option = "<option value='0'>请选择</option>";
                        $.each(data.listData, function (i, n) {
                            $option += "<option value='" + data.listData[i]['regionId'] + "'>" + data.listData[i]['regionName'] + "</option>";
                        });
                        $("select[name='city'] option:gt(0)").remove();
                        $("select[name='zone'] option:gt(0)").remove();
                        $("#zone").val("0").trigger("change");
                        $("#city").val("0").trigger("change");

                        $("select[name='city']").html($option);
                        $("select[name='zone']").html("<option value='0'>请选择</option>");

                        callback && callback();
                    },
                    error: function (data) {
                        if (data.status == 1001) {
                            layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                        }
                    }
                });
            } else if (regionId == 0) {
                $("select[name='city'] option:gt(0)").remove();
                $("select[name='zone'] option:gt(0)").remove();
            }
        };

        window.getCityData = function (val, callback) {
            checkLogin();
            var regionId = val;
            if (regionId > 0) {
                $.ajax({
                    type: "POST",
                    url: page_url + "/system/region/getregion.do",
                    data: {'regionId': regionId},
                    dataType: 'json',
                    success: function (data) {
                        $option = "<option value='0'>请输入县区</option>";
                        $.each(data.listData, function (i, n) {
                            $option += "<option value='" + data.listData[i]['regionId'] + "'>" + data.listData[i]['regionName'] + "</option>";
                        });
                        $("select[name='zone'] option:gt(0)").remove();

                        $("#zone").val("0").trigger("change");
                        $("select[name='zone']").html($option);

                        callback && callback();
                    },
                    error: function (data) {
                        if (data.status == 1001) {
                            layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                        }
                    }
                });
            }
        };


        $("select[name='province']").change(function () {
            getProvinceData($(this).val());
        });

        $("select[name='city']").change(function () {
            getCityData($(this).val());
        });
    });

    function searchTerminal() {
        layer.open({
            type: 2,
            title: '搜索',
            area: ['800px', '280px'],
            shade: 0.4,
            content: '/trader/customer/getTerminalList.do?searchTraderName=' + encodeURI($('#terminalTraderName').val()) + '&optType=addQuoteTerminal'
        });
    }

    function changeArea(areaIds) {
        if(areaIds){
            var area = areaIds.split(',');
            $("#province").val(area[0]);
            getProvinceData(area[0], function () {
                $("#city").val(area[1]);
                getCityData(area[1], function () {
                    if(area[2]){
                        $("#zone").val(area[2]);
                    }
                });
            });
        }
    }



    /**
     * 终端名称未知
     */
    function terminalTraderNameUnknown() {
        $('#terminalTraderName').val('未知');
        $('#terminalTraderId').val(1);
        $('#terminalTraderName').prop('disabled', true);
        $('#searchTerminal').hide();
    }

    /**
     * 终端地区未知
     */
    function terminalAreaUnknown() {
        $('#province').append(' <option value="-1">未知</option>')
        $('#province').val(-1);
        $('#province').prop('disabled', true);

        $('#city').append(' <option value="-1">未知</option>')
        $('#city').val(-1);
        $('#city').prop('disabled', true);

        $('#zone').append(' <option value="-1">未知</option>')
        $('#zone').val(-1);
        $('#zone').prop('disabled', true);
    }

    /**
     * 终端信息类型未知
     */
    function terminalTypeUnKnown() {
        $('#terminalType').append(' <option value="-1">未知</option>')
        $('#terminalType').val(-1);
        $('#terminalType').prop('disabled', true);
    }

    /**
     * 维护客户基本信息
     */
    function viewTraderInfo() {
        parent.viewTraderFunc();
    }

    /**
     * 表单提交
     */
   function myFormSubnit() {
        var check = checkQuotePayMoneForm();
        if (check == 1){
            $.ajax({
                type: "POST",
                url: "/order/quote/saveTerminalInfo.do",
                data: $('#quotePayMoneForm').serialize(),
                dataType:'json',
                success: function(data){
                    if (data.code == 0) {
                        layer.close(index);
                        window.parent.location.reload();
                    } else {
                        layer.alert(data.message);
                    }
                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });
        }
    }

    /**
     * 表单校验
     */
    function checkQuotePayMoneForm() {
        $('#terminalTraderNameError').html('');
        $('#terminalAreaError').html('');
        $('#terminalTypeError').html('');
        var terminalTraderName = $('#terminalTraderName').val();
        console.log('terminalTraderName' + terminalTraderName);
        if (terminalTraderName == undefined || terminalTraderName == '' || terminalTraderName == null){
            $('#terminalTraderNameError').html('终端名称不能为空');
            return 0
        }

        var zone = $('#zone').val();
        console.log('zone' + zone);
        if (zone == undefined || zone == '' || zone == null || zone == 0){
            $('#terminalAreaError').html('销售区域不能为空');
            return 0
        }

        var terminalType = $('#terminalType').val();
        console.log('terminalType' + terminalType);
        if (terminalType == undefined || terminalType == '' || terminalType == null || terminalType == 0){
            $('#terminalTypeError').html('报备终端类型不能为空');
            return 0
        }
        return 1;
    }
</script>

<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/libs/jquery/plugins/layer/layer.js"></script>