package com.vedeng.erp.saleorder.service.impl;

import com.vedeng.erp.saleorder.api.SaleorderInfoQueryApiService;
import com.vedeng.erp.saleorder.dao.SaleOrderMapper;
import com.vedeng.erp.saleorder.dao.VJdSaleorderMapper;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.erp.saleorder.model.po.VJdSaleorder;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class SaleorderInfoQueryServiceImpl implements SaleorderInfoQueryApiService {

    @Autowired
    private SaleorderMapper saleorderMapper;

    @Autowired
    private SaleOrderMapper saleOrderMapper;

    @Autowired
    private VJdSaleorderMapper vJdSaleorderMapper;

    @Override
    public SaleorderInfoDto queryInfoByNo(String saleorderNo) {
        Saleorder saleorder = saleorderMapper.getSaleorderByOrderNo(saleorderNo);
        if (saleorder != null) {
            SaleorderInfoDto saleorderInfoDto = new SaleorderInfoDto();
            saleorderInfoDto.setSaleorderId(saleorder.getSaleorderId());
            saleorderInfoDto.setSaleorderNo(saleorder.getSaleorderNo());
            saleorderInfoDto.setTraderName(saleorder.getTraderName());
            saleorderInfoDto.setDeliveryStatus(saleorder.getDeliveryStatus());
            saleorderInfoDto.setDeliveryTime(saleorder.getDeliveryTime());
            saleorderInfoDto.setConfirmationFormAudit(saleorder.getConfirmationFormAudit());
            return saleorderInfoDto;
        }
        return null;
    }

    @Override
    public boolean queryTraderHaveOrder(Integer traderId) {
        if (traderId == null || traderId == 0) {
            return false;
        }
        Integer saleorderId = saleorderMapper.selectTraderHaveOrder(traderId);
        return saleorderId != null;
    }

    @Override
    public List<SaleorderInfoDto> getSkuFairValueInfo(Integer goodsId) {
        return saleOrderMapper.getSkuFairValueSaleOrderList(goodsId);
    }


    @Override
    public SaleorderInfoDto queryInfoByJdOrderNo(String jdSaleOrderNo){
        VJdSaleorder jdSaleorder = vJdSaleorderMapper.selectByJdSaleOrderNo(jdSaleOrderNo);
        if(jdSaleorder == null){
            return null;
        }
        return queryInfoByNo(jdSaleorder.getSaleorderNo());
    }

}
