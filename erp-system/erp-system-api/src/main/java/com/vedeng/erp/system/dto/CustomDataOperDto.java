
package com.vedeng.erp.system.dto;

import com.vedeng.common.core.base.BaseDto;
import lombok.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 自定义数据操作 个人属性行为 如置顶某条数据
 * @date 2022/7/11 23:07
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CustomDataOperDto extends BaseDto {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 对应的业务主键id
     */
    private Integer relatedId;

    /**
     * 业务类型（1=线索 2=商机）
     */
    private Integer bizType;

    /**
     * 数据操作类型（1:置顶 2:取消置顶）
     */
    private Integer operType;

    /**
     * 数据操作时间
     */
    private Date operTime;

    /**
     * 归属人id
     */
    private Integer belongerId;

    /**
     * 归属人id
     */
    private String belonger;


}
