package com.newtask;

import com.alibaba.fastjson.JSON;
import com.rabbitmq.MarketMsgProducer;
import com.rabbitmq.RabbitConfig;
import com.vedeng.common.constant.OrderConstant;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.HolidayUtil;
import com.vedeng.erp.saleorder.api.SaleorderSyncService;
import com.vedeng.erp.saleorder.enums.PCOrderStatusEnum;
import com.vedeng.erp.saleorder.enums.SaleorderSyncEnum;
import com.vedeng.order.dao.SaleorderCouponMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderCoupon;
import com.vedeng.order.service.SaleorderService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 针对使用优惠券的BD订单 自动取消的任务
 */
@Component
@JobHandler(value="BDOrderAutoCancelTask")
public class BDOrderAutoCancelTask extends AbstractJobHandler {

    private Logger logger = LoggerFactory.getLogger(BDOrderAutoCancelTask.class);


    @Value("${mjx_url}")
    private String mjxUrl;

    @Autowired
    private SaleorderService saleorderService;

    @Autowired
    private SaleorderCouponMapper saleorderCouponMapper;


    @Autowired
    private MarketMsgProducer msgProducer;


    @Resource
    private SaleorderSyncService saleorderSyncService;


    @Override
    public ReturnT<String> doExecute(String executeParam) throws Exception {

        XxlJobLogger.log("BDOrderAutoCancelTask start.......");

        //获取所有BD且使用了优惠券的订单
        List<Saleorder> saleorderList = saleorderService.getBDSaleOrderAndHasCoupon();

        if(CollectionUtils.isEmpty(saleorderList)){
            logger.info("没有查询到对应的BD订单");
            XxlJobLogger.log("没有查询到对应的BD订单");
            return SUCCESS;
        }

        try {
            for(Saleorder saleorder : saleorderList){

                if(!needCancelOrder(saleorder)){
                    continue;
                }

                cancelOrder(saleorder);
            }
        }catch (Exception e){
            logger.error("取消BD订单错误",e);
            XxlJobLogger.log("取消BD订单错误",e);
            return FAIL;
        }

        XxlJobLogger.log("BDOrderAutoCancelTask end.......");

        return SUCCESS;
    }

    /**
     * 取消订单
     * @param saleorder
     */
    @Transactional
    public void cancelOrder(Saleorder saleorder) throws Exception{

        //修改订单状态为已关闭
        Saleorder updateSaleOrder = new Saleorder();
        updateSaleOrder.setSaleorderId(saleorder.getSaleorderId());
        updateSaleOrder.setModTime(System.currentTimeMillis());
        updateSaleOrder.setStatus(OrderConstant.ORDER_STATUS_CLOSE);

        if(saleorder.getStatus() == OrderConstant.ORDER_STATUS_UNCONFIRM){
            saleorder.setCloseComments("订单创建-订单生效，若超过2个工作日");
        }else{
            saleorder.setCloseComments("订单生效后超过5个工作日，均未付任何款项");
        }
        this.saleorderService.updateByPrimaryKeySelective(updateSaleOrder);

        //归还优惠券
        SaleorderCoupon saleorderCoupon = saleorderCouponMapper.selectBySaleOrderId(saleorder.getSaleorderId());
        if(saleorderCoupon != null){

            Map<String,String> couponReturnReqMap = new HashMap<>();
            couponReturnReqMap.put("couponCode",saleorderCoupon.getCouponCode());
            couponReturnReqMap.put("traderId",saleorder.getTraderId() + Strings.EMPTY);

            logger.info("归还优惠券消息 start=======" + JSON.toJSONString(couponReturnReqMap));
            msgProducer.sendMsg(RabbitConfig.MARKET_RETURNCOUPON_EXCHANGE, RabbitConfig.MARKET_RETURNCOUPON_ROUTINGKEY, JSON.toJSONString(couponReturnReqMap));
            logger.info("归还优惠券消息 end=======");

            //清空订单优惠券信息
            saleorderService.clearCoupon(saleorder.getSaleorderId());

        }
        //重构
        saleorderSyncService.syncSaleorderStatus2Mjx(saleorder.getSaleorderId()
        , PCOrderStatusEnum.CANCEL, SaleorderSyncEnum.CANCEL_BD_TIMEOUT);

    }

    /**
     * 判断一个订单是否需要取消
     * @param saleorder
     * @return
     */
    private boolean needCancelOrder(Saleorder saleorder) throws Exception{

        //如果订单未确认
        if(saleorder.getStatus() == OrderConstant.ORDER_STATUS_UNCONFIRM){
            //从订单创建开始 2个工作日没确认 需要取消
            if(System.currentTimeMillis() > HolidayUtil.getWorkDate(saleorder.getAddTime(),2)){
                return true;
            };
        }

        //如果订单已经生效且为付款
        if(saleorder.getStatus() == OrderConstant.ORDER_STATUS_PROCESSING
                && saleorder.getPaymentStatus() == OrderConstant.ORDER_PAYMENT_STATUS_UNPAY){
            //从订单生效时间开始,超过5个工作日还没有付款 则需要取消
            if(System.currentTimeMillis() > HolidayUtil.getWorkDate(saleorder.getValidTime(),5)){
                return true;
            };
        }

        return false;
    }

}
