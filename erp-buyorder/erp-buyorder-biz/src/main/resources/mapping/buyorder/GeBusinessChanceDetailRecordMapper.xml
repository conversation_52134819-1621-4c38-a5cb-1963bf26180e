<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.erp.buyorder.mapper.GeBusinessChanceDetailRecordMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.erp.buyorder.domain.entity.GeBusinessChanceDetailRecord" >
    <!--          -->
    <id column="GE_BUSSINESS_CHANCE_DETAIL_RECORD_ID" property="geBussinessChanceDetailRecordId" jdbcType="INTEGER" />
    <result column="GE_BUSSINESS_CHANCE_ID" property="geBussinessChanceId" jdbcType="INTEGER" />
    <result column="GE_BUSSINESS_CHANCE_DETAIL_ID" property="geBussinessChanceDetailId" jdbcType="INTEGER" />
    <result column="BUSINESS_CHANCE_STATUS" property="businessChanceStatus" jdbcType="BIT" />
    <result column="SALES_AMOUNT" property="salesAmount" jdbcType="DECIMAL" />
    <result column="HOSPITAL_SIZE" property="hospitalSize" jdbcType="BIT" />
    <result column="BED_NUM" property="bedNum" jdbcType="INTEGER" />
    <result column="YEAR_IN_SUM" property="yearInSum" jdbcType="DECIMAL" />
    <result column="CT_DAILY_NUM" property="ctDailyNum" jdbcType="INTEGER" />
    <result column="IS_NEW_HOSPITAL" property="isNewHospital" jdbcType="BIT" />
    <result column="NEW_HOSPITAL_PERCENT" property="newHospitalPercent" jdbcType="BIT" />
    <result column="EXPECT_BUY_TIME" property="expectBuyTime" jdbcType="TIMESTAMP" />
    <result column="COMPETE_NAME" property="competeName" jdbcType="VARCHAR" />
    <result column="COMPETE_SKU_NAME" property="competeSkuName" jdbcType="VARCHAR" />
    <result column="COMPETE_SKU_PRICE" property="competeSkuPrice" jdbcType="DECIMAL" />
    <result column="IS_NEW_INSTALL" property="isNewInstall" jdbcType="BIT" />
    <result column="ORIGIN_SKU_BAND" property="originSkuBand" jdbcType="VARCHAR" />
    <result column="ORIGIN_SKU_MODEL" property="originSkuModel" jdbcType="VARCHAR" />
    <result column="INSTALL_TIME" property="installTime" jdbcType="TIMESTAMP" />
    <result column="SALE_COMPANY_NAME" property="saleCompanyName" jdbcType="VARCHAR" />
    <result column="WIN_ORDER_PERCENT" property="winOrderPercent" jdbcType="BIT" />
    <result column="MONEY_SOURCE" property="moneySource" jdbcType="VARCHAR" />
    <result column="MONEY_SITUATION" property="moneySituation" jdbcType="VARCHAR" />
    <result column="PREPARE_AMOUNT" property="prepareAmount" jdbcType="DECIMAL" />
    <result column="BUY_TYPE" property="buyType" jdbcType="BIT" />
    <result column="IS_APPLY_INSPECT" property="isApplyInspect" jdbcType="BIT" />
    <result column="IS_ENGINE_COMPLETE" property="isEngineComplete" jdbcType="BIT" />
    <result column="QUOTE_METHOD" property="quoteMethod" jdbcType="VARCHAR" />
    <result column="QUOTE_METHOD_PRICE" property="quoteMethodPrice" jdbcType="DECIMAL" />
    <result column="PROJECT_PHASE" property="projectPhase" jdbcType="BIT" />
    <result column="NEED_COMPLETE" property="needComplete" jdbcType="VARCHAR" />
    <result column="NEED_TIME" property="needTime" jdbcType="TIMESTAMP" />
    <result column="ASSIST_DEPARTMENT" property="assistDepartment" jdbcType="VARCHAR" />
    <result column="PROJECT_EVOLVE" property="projectEvolve" jdbcType="VARCHAR" />
    <result column="EVOLVE_TIME" property="evolveTime" jdbcType="TIMESTAMP" />
    <result column="NEXT_PLAN" property="nextPlan" jdbcType="VARCHAR" />
    <result column="NEED_SUPPORT" property="needSupport" jdbcType="VARCHAR" />
    <result column="ADD_TIME" property="addTime" jdbcType="TIMESTAMP" />
    <result column="MOD_TIME" property="modTime" jdbcType="TIMESTAMP" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
    <result column="CREATOR_NAME" property="creatorName" jdbcType="VARCHAR" />
    <result column="UPDATER_NAME" property="updaterName" jdbcType="VARCHAR" />
    <result column="IS_DELETE" property="isDelete" jdbcType="BIT" />
    <result column="TERMINAL_PART_IS_CHANGE" property="terminalPartIsChange" jdbcType="BIT" />
    <result column="COMPETE_PART_IS_CHANGE" property="competePartIsChange" jdbcType="BIT" />
    <result column="ORIGIN_INSTALL_PART_IS_CHANGE" property="originInstallPartIsChange" jdbcType="BIT" />
    <result column="KEY_DEAL_PART_IS_CHANGE" property="keyDealPartIsChange" jdbcType="BIT" />
    <result column="CHANCE_DETAIL_PART_IS_CHANGE" property="chanceDetailPartIsChange" jdbcType="BIT" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--          -->
    GE_BUSSINESS_CHANCE_DETAIL_RECORD_ID, GE_BUSSINESS_CHANCE_ID, GE_BUSSINESS_CHANCE_DETAIL_ID, 
    BUSINESS_CHANCE_STATUS, SALES_AMOUNT, HOSPITAL_SIZE, BED_NUM, YEAR_IN_SUM, CT_DAILY_NUM,
    IS_NEW_HOSPITAL, NEW_HOSPITAL_PERCENT, EXPECT_BUY_TIME, COMPETE_NAME, COMPETE_SKU_NAME,
    COMPETE_SKU_PRICE, IS_NEW_INSTALL, ORIGIN_SKU_BAND, ORIGIN_SKU_MODEL, INSTALL_TIME, 
    SALE_COMPANY_NAME, WIN_ORDER_PERCENT, MONEY_SOURCE, MONEY_SITUATION, PREPARE_AMOUNT, 
    BUY_TYPE, IS_APPLY_INSPECT, IS_ENGINE_COMPLETE, QUOTE_METHOD, QUOTE_METHOD_PRICE, 
    PROJECT_PHASE, NEED_COMPLETE, NEED_TIME, ASSIST_DEPARTMENT, PROJECT_EVOLVE, EVOLVE_TIME, 
    NEXT_PLAN, NEED_SUPPORT, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, 
    IS_DELETE, TERMINAL_PART_IS_CHANGE, COMPETE_PART_IS_CHANGE, ORIGIN_INSTALL_PART_IS_CHANGE, 
    KEY_DEAL_PART_IS_CHANGE ,CHANCE_DETAIL_PART_IS_CHANGE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--          -->
    select 
    <include refid="Base_Column_List" />
    from T_GE_BUSINESS_CHANCE_DETAIL_RECORD
    where GE_BUSSINESS_CHANCE_DETAIL_RECORD_ID = #{geBussinessChanceDetailRecordId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--          -->
    delete from T_GE_BUSINESS_CHANCE_DETAIL_RECORD
    where GE_BUSSINESS_CHANCE_DETAIL_RECORD_ID = #{geBussinessChanceDetailRecordId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.erp.buyorder.domain.entity.GeBusinessChanceDetailRecord" >
    <!--          -->
    insert into T_GE_BUSINESS_CHANCE_DETAIL_RECORD (GE_BUSSINESS_CHANCE_DETAIL_RECORD_ID, GE_BUSSINESS_CHANCE_ID, 
      GE_BUSSINESS_CHANCE_DETAIL_ID, BUSINESS_CHANCE_STATUS, 
      SALES_AMOUNT, HOSPITAL_SIZE, BED_NUM,
      YEAR_IN_SUM, CT_DAILY_NUM, IS_NEW_HOSPITAL,
      NEW_HOSPITAL_PERCENT, EXPECT_BUY_TIME, COMPETE_NAME,
      COMPETE_SKU_NAME, COMPETE_SKU_PRICE, IS_NEW_INSTALL, 
      ORIGIN_SKU_BAND, ORIGIN_SKU_MODEL, INSTALL_TIME, 
      SALE_COMPANY_NAME, WIN_ORDER_PERCENT, MONEY_SOURCE, 
      MONEY_SITUATION, PREPARE_AMOUNT, BUY_TYPE, 
      IS_APPLY_INSPECT, IS_ENGINE_COMPLETE, QUOTE_METHOD, 
      QUOTE_METHOD_PRICE, PROJECT_PHASE, NEED_COMPLETE, 
      NEED_TIME, ASSIST_DEPARTMENT, PROJECT_EVOLVE, 
      EVOLVE_TIME, NEXT_PLAN, NEED_SUPPORT, 
      ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      IS_DELETE, TERMINAL_PART_IS_CHANGE, COMPETE_PART_IS_CHANGE, 
      ORIGIN_INSTALL_PART_IS_CHANGE, KEY_DEAL_PART_IS_CHANGE, CHANCE_DETAIL_PART_IS_CHANGE)
    values (#{geBussinessChanceDetailRecordId,jdbcType=INTEGER}, #{geBussinessChanceId,jdbcType=INTEGER}, 
      #{geBussinessChanceDetailId,jdbcType=INTEGER}, #{businessChanceStatus,jdbcType=BIT}, 
      #{salesAmount,jdbcType=DECIMAL}, #{hospitalSize,jdbcType=BIT}, #{bedNum,jdbcType=INTEGER},
      #{yearInSum,jdbcType=DECIMAL}, #{ctDailyNum,jdbcType=INTEGER}, #{isNewHospital,jdbcType=BIT},
      #{newHospitalPercent,jdbcType=BIT}, #{expectBuyTime,jdbcType=TIMESTAMP}, #{competeName,jdbcType=VARCHAR},
      #{competeSkuName,jdbcType=VARCHAR}, #{competeSkuPrice,jdbcType=DECIMAL}, #{isNewInstall,jdbcType=BIT}, 
      #{originSkuBand,jdbcType=VARCHAR}, #{originSkuModel,jdbcType=VARCHAR}, #{installTime,jdbcType=TIMESTAMP}, 
      #{saleCompanyName,jdbcType=VARCHAR}, #{winOrderPercent,jdbcType=BIT}, #{moneySource,jdbcType=VARCHAR}, 
      #{moneySituation,jdbcType=VARCHAR}, #{prepareAmount,jdbcType=DECIMAL}, #{buyType,jdbcType=BIT}, 
      #{isApplyInspect,jdbcType=BIT}, #{isEngineComplete,jdbcType=BIT}, #{quoteMethod,jdbcType=VARCHAR}, 
      #{quoteMethodPrice,jdbcType=DECIMAL}, #{projectPhase,jdbcType=BIT}, #{needComplete,jdbcType=VARCHAR}, 
      #{needTime,jdbcType=TIMESTAMP}, #{assistDepartment,jdbcType=VARCHAR}, #{projectEvolve,jdbcType=VARCHAR}, 
      #{evolveTime,jdbcType=TIMESTAMP}, #{nextPlan,jdbcType=VARCHAR}, #{needSupport,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{isDelete,jdbcType=BIT}, #{terminalPartIsChange,jdbcType=BIT}, #{competePartIsChange,jdbcType=BIT}, 
      #{originInstallPartIsChange,jdbcType=BIT}, #{keyDealPartIsChange,jdbcType=BIT}, #{chanceDetailPartIsChange,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.erp.buyorder.domain.entity.GeBusinessChanceDetailRecord" >
    <!--          -->
    insert into T_GE_BUSINESS_CHANCE_DETAIL_RECORD
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="geBussinessChanceDetailRecordId != null" >
        GE_BUSSINESS_CHANCE_DETAIL_RECORD_ID,
      </if>
      <if test="geBussinessChanceId != null" >
        GE_BUSSINESS_CHANCE_ID,
      </if>
      <if test="geBussinessChanceDetailId != null" >
        GE_BUSSINESS_CHANCE_DETAIL_ID,
      </if>
      <if test="businessChanceStatus != null" >
        BUSINESS_CHANCE_STATUS,
      </if>
      <if test="salesAmount != null" >
        SALES_AMOUNT,
      </if>
      <if test="hospitalSize != null" >
        HOSPITAL_SIZE,
      </if>
      <if test="bedNum != null" >
        BED_NUM,
      </if>
      <if test="yearInSum != null" >
        YEAR_IN_SUM,
      </if>
      <if test="ctDailyNum != null" >
        CT_DAILY_NUM,
      </if>
      <if test="isNewHospital != null" >
        IS_NEW_HOSPITAL,
      </if>
      <if test="newHospitalPercent != null" >
        NEW_HOSPITAL_PERCENT,
      </if>
      <if test="expectBuyTime != null" >
        EXPECT_BUY_TIME,
      </if>
      <if test="competeName != null" >
        COMPETE_NAME,
      </if>
      <if test="competeSkuName != null" >
        COMPETE_SKU_NAME,
      </if>
      <if test="competeSkuPrice != null" >
        COMPETE_SKU_PRICE,
      </if>
      <if test="isNewInstall != null" >
        IS_NEW_INSTALL,
      </if>
      <if test="originSkuBand != null" >
        ORIGIN_SKU_BAND,
      </if>
      <if test="originSkuModel != null" >
        ORIGIN_SKU_MODEL,
      </if>
      <if test="installTime != null" >
        INSTALL_TIME,
      </if>
      <if test="saleCompanyName != null" >
        SALE_COMPANY_NAME,
      </if>
      <if test="winOrderPercent != null" >
        WIN_ORDER_PERCENT,
      </if>
      <if test="moneySource != null" >
        MONEY_SOURCE,
      </if>
      <if test="moneySituation != null" >
        MONEY_SITUATION,
      </if>
      <if test="prepareAmount != null" >
        PREPARE_AMOUNT,
      </if>
      <if test="buyType != null" >
        BUY_TYPE,
      </if>
      <if test="isApplyInspect != null" >
        IS_APPLY_INSPECT,
      </if>
      <if test="isEngineComplete != null" >
        IS_ENGINE_COMPLETE,
      </if>
      <if test="quoteMethod != null" >
        QUOTE_METHOD,
      </if>
      <if test="quoteMethodPrice != null" >
        QUOTE_METHOD_PRICE,
      </if>
      <if test="projectPhase != null" >
        PROJECT_PHASE,
      </if>
      <if test="needComplete != null" >
        NEED_COMPLETE,
      </if>
      <if test="needTime != null" >
        NEED_TIME,
      </if>
      <if test="assistDepartment != null" >
        ASSIST_DEPARTMENT,
      </if>
      <if test="projectEvolve != null" >
        PROJECT_EVOLVE,
      </if>
      <if test="evolveTime != null" >
        EVOLVE_TIME,
      </if>
      <if test="nextPlan != null" >
        NEXT_PLAN,
      </if>
      <if test="needSupport != null" >
        NEED_SUPPORT,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
      <if test="creatorName != null" >
        CREATOR_NAME,
      </if>
      <if test="updaterName != null" >
        UPDATER_NAME,
      </if>
      <if test="isDelete != null" >
        IS_DELETE,
      </if>
      <if test="terminalPartIsChange != null" >
        TERMINAL_PART_IS_CHANGE,
      </if>
      <if test="competePartIsChange != null" >
        COMPETE_PART_IS_CHANGE,
      </if>
      <if test="originInstallPartIsChange != null" >
        ORIGIN_INSTALL_PART_IS_CHANGE,
      </if>
      <if test="keyDealPartIsChange != null" >
        KEY_DEAL_PART_IS_CHANGE,
      </if>
      <if test="chanceDetailPartIsChange != null" >
        CHANCE_DETAIL_PART_IS_CHANGE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="geBussinessChanceDetailRecordId != null" >
        #{geBussinessChanceDetailRecordId,jdbcType=INTEGER},
      </if>
      <if test="geBussinessChanceId != null" >
        #{geBussinessChanceId,jdbcType=INTEGER},
      </if>
      <if test="geBussinessChanceDetailId != null" >
        #{geBussinessChanceDetailId,jdbcType=INTEGER},
      </if>
      <if test="businessChanceStatus != null" >
        #{businessChanceStatus,jdbcType=BIT},
      </if>
      <if test="salesAmount != null" >
        #{salesAmount,jdbcType=DECIMAL},
      </if>
      <if test="hospitalSize != null" >
        #{hospitalSize,jdbcType=BIT},
      </if>
      <if test="bedNum != null" >
        #{bedNum,jdbcType=INTEGER},
      </if>
      <if test="yearInSum != null" >
        #{yearInSum,jdbcType=DECIMAL},
      </if>
      <if test="ctDailyNum != null" >
        #{ctDailyNum,jdbcType=INTEGER},
      </if>
      <if test="isNewHospital != null" >
        #{isNewHospital,jdbcType=BIT},
      </if>
      <if test="newHospitalPercent != null" >
        #{newHospitalPercent,jdbcType=BIT},
      </if>
      <if test="expectBuyTime != null" >
        #{expectBuyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="competeName != null" >
        #{competeName,jdbcType=VARCHAR},
      </if>
      <if test="competeSkuName != null" >
        #{competeSkuName,jdbcType=VARCHAR},
      </if>
      <if test="competeSkuPrice != null" >
        #{competeSkuPrice,jdbcType=DECIMAL},
      </if>
      <if test="isNewInstall != null" >
        #{isNewInstall,jdbcType=BIT},
      </if>
      <if test="originSkuBand != null" >
        #{originSkuBand,jdbcType=VARCHAR},
      </if>
      <if test="originSkuModel != null" >
        #{originSkuModel,jdbcType=VARCHAR},
      </if>
      <if test="installTime != null" >
        #{installTime,jdbcType=TIMESTAMP},
      </if>
      <if test="saleCompanyName != null" >
        #{saleCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="winOrderPercent != null" >
        #{winOrderPercent,jdbcType=BIT},
      </if>
      <if test="moneySource != null" >
        #{moneySource,jdbcType=VARCHAR},
      </if>
      <if test="moneySituation != null" >
        #{moneySituation,jdbcType=VARCHAR},
      </if>
      <if test="prepareAmount != null" >
        #{prepareAmount,jdbcType=DECIMAL},
      </if>
      <if test="buyType != null" >
        #{buyType,jdbcType=BIT},
      </if>
      <if test="isApplyInspect != null" >
        #{isApplyInspect,jdbcType=BIT},
      </if>
      <if test="isEngineComplete != null" >
        #{isEngineComplete,jdbcType=BIT},
      </if>
      <if test="quoteMethod != null" >
        #{quoteMethod,jdbcType=VARCHAR},
      </if>
      <if test="quoteMethodPrice != null" >
        #{quoteMethodPrice,jdbcType=DECIMAL},
      </if>
      <if test="projectPhase != null" >
        #{projectPhase,jdbcType=BIT},
      </if>
      <if test="needComplete != null" >
        #{needComplete,jdbcType=VARCHAR},
      </if>
      <if test="needTime != null" >
        #{needTime,jdbcType=TIMESTAMP},
      </if>
      <if test="assistDepartment != null" >
        #{assistDepartment,jdbcType=VARCHAR},
      </if>
      <if test="projectEvolve != null" >
        #{projectEvolve,jdbcType=VARCHAR},
      </if>
      <if test="evolveTime != null" >
        #{evolveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="nextPlan != null" >
        #{nextPlan,jdbcType=VARCHAR},
      </if>
      <if test="needSupport != null" >
        #{needSupport,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null" >
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null" >
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null" >
        #{isDelete,jdbcType=BIT},
      </if>
      <if test="terminalPartIsChange != null" >
        #{terminalPartIsChange,jdbcType=BIT},
      </if>
      <if test="competePartIsChange != null" >
        #{competePartIsChange,jdbcType=BIT},
      </if>
      <if test="originInstallPartIsChange != null" >
        #{originInstallPartIsChange,jdbcType=BIT},
      </if>
      <if test="keyDealPartIsChange != null" >
        #{keyDealPartIsChange,jdbcType=BIT},
      </if>
      <if test="chanceDetailPartIsChange != null" >
        #{chanceDetailPartIsChange,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.buyorder.domain.entity.GeBusinessChanceDetailRecord" >
    <!--          -->
    update T_GE_BUSINESS_CHANCE_DETAIL_RECORD
    <set >
      <if test="geBussinessChanceId != null" >
        GE_BUSSINESS_CHANCE_ID = #{geBussinessChanceId,jdbcType=INTEGER},
      </if>
      <if test="geBussinessChanceDetailId != null" >
        GE_BUSSINESS_CHANCE_DETAIL_ID = #{geBussinessChanceDetailId,jdbcType=INTEGER},
      </if>
      <if test="businessChanceStatus != null" >
        BUSINESS_CHANCE_STATUS = #{businessChanceStatus,jdbcType=BIT},
      </if>
      <if test="salesAmount != null" >
        SALES_AMOUNT = #{salesAmount,jdbcType=DECIMAL},
      </if>
      <if test="hospitalSize != null" >
        HOSPITAL_SIZE = #{hospitalSize,jdbcType=BIT},
      </if>
      <if test="bedNum != null" >
        BED_NUM = #{bedNum,jdbcType=INTEGER},
      </if>
      <if test="yearInSum != null" >
        YEAR_IN_SUM = #{yearInSum,jdbcType=DECIMAL},
      </if>
      <if test="ctDailyNum != null" >
        CT_DAILY_NUM = #{ctDailyNum,jdbcType=INTEGER},
      </if>
      <if test="isNewHospital != null" >
        IS_NEW_HOSPITAL = #{isNewHospital,jdbcType=BIT},
      </if>
      <if test="newHospitalPercent != null" >
        NEW_HOSPITAL_PERCENT = #{newHospitalPercent,jdbcType=BIT},
      </if>
      <if test="expectBuyTime != null" >
        EXPECT_BUY_TIME = #{expectBuyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="competeName != null" >
        COMPETE_NAME = #{competeName,jdbcType=VARCHAR},
      </if>
      <if test="competeSkuName != null" >
        COMPETE_SKU_NAME = #{competeSkuName,jdbcType=VARCHAR},
      </if>
      <if test="competeSkuPrice != null" >
        COMPETE_SKU_PRICE = #{competeSkuPrice,jdbcType=DECIMAL},
      </if>
      <if test="isNewInstall != null" >
        IS_NEW_INSTALL = #{isNewInstall,jdbcType=BIT},
      </if>
      <if test="originSkuBand != null" >
        ORIGIN_SKU_BAND = #{originSkuBand,jdbcType=VARCHAR},
      </if>
      <if test="originSkuModel != null" >
        ORIGIN_SKU_MODEL = #{originSkuModel,jdbcType=VARCHAR},
      </if>
      <if test="installTime != null" >
        INSTALL_TIME = #{installTime,jdbcType=TIMESTAMP},
      </if>
      <if test="saleCompanyName != null" >
        SALE_COMPANY_NAME = #{saleCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="winOrderPercent != null" >
        WIN_ORDER_PERCENT = #{winOrderPercent,jdbcType=BIT},
      </if>
      <if test="moneySource != null" >
        MONEY_SOURCE = #{moneySource,jdbcType=VARCHAR},
      </if>
      <if test="moneySituation != null" >
        MONEY_SITUATION = #{moneySituation,jdbcType=VARCHAR},
      </if>
      <if test="prepareAmount != null" >
        PREPARE_AMOUNT = #{prepareAmount,jdbcType=DECIMAL},
      </if>
      <if test="buyType != null" >
        BUY_TYPE = #{buyType,jdbcType=BIT},
      </if>
      <if test="isApplyInspect != null" >
        IS_APPLY_INSPECT = #{isApplyInspect,jdbcType=BIT},
      </if>
      <if test="isEngineComplete != null" >
        IS_ENGINE_COMPLETE = #{isEngineComplete,jdbcType=BIT},
      </if>
      <if test="quoteMethod != null" >
        QUOTE_METHOD = #{quoteMethod,jdbcType=VARCHAR},
      </if>
      <if test="quoteMethodPrice != null" >
        QUOTE_METHOD_PRICE = #{quoteMethodPrice,jdbcType=DECIMAL},
      </if>
      <if test="projectPhase != null" >
        PROJECT_PHASE = #{projectPhase,jdbcType=BIT},
      </if>
      <if test="needComplete != null" >
        NEED_COMPLETE = #{needComplete,jdbcType=VARCHAR},
      </if>
      <if test="needTime != null" >
        NEED_TIME = #{needTime,jdbcType=TIMESTAMP},
      </if>
      <if test="assistDepartment != null" >
        ASSIST_DEPARTMENT = #{assistDepartment,jdbcType=VARCHAR},
      </if>
      <if test="projectEvolve != null" >
        PROJECT_EVOLVE = #{projectEvolve,jdbcType=VARCHAR},
      </if>
      <if test="evolveTime != null" >
        EVOLVE_TIME = #{evolveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="nextPlan != null" >
        NEXT_PLAN = #{nextPlan,jdbcType=VARCHAR},
      </if>
      <if test="needSupport != null" >
        NEED_SUPPORT = #{needSupport,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null" >
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null" >
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null" >
        IS_DELETE = #{isDelete,jdbcType=BIT},
      </if>
      <if test="terminalPartIsChange != null" >
        TERMINAL_PART_IS_CHANGE = #{terminalPartIsChange,jdbcType=BIT},
      </if>
      <if test="competePartIsChange != null" >
        COMPETE_PART_IS_CHANGE = #{competePartIsChange,jdbcType=BIT},
      </if>
      <if test="originInstallPartIsChange != null" >
        ORIGIN_INSTALL_PART_IS_CHANGE = #{originInstallPartIsChange,jdbcType=BIT},
      </if>
      <if test="keyDealPartIsChange != null" >
        KEY_DEAL_PART_IS_CHANGE = #{keyDealPartIsChange,jdbcType=BIT},
      </if>
      <if test="chanceDetailPartIsChange != null" >
        CHANCE_DETAIL_PART_IS_CHANGE = #{chanceDetailPartIsChange,jdbcType=BIT},
      </if>
    </set>
    where GE_BUSSINESS_CHANCE_DETAIL_RECORD_ID = #{geBussinessChanceDetailRecordId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.buyorder.domain.entity.GeBusinessChanceDetailRecord" >
    <!--          -->
    update T_GE_BUSINESS_CHANCE_DETAIL_RECORD
    set GE_BUSSINESS_CHANCE_ID = #{geBussinessChanceId,jdbcType=INTEGER},
      GE_BUSSINESS_CHANCE_DETAIL_ID = #{geBussinessChanceDetailId,jdbcType=INTEGER},
      BUSINESS_CHANCE_STATUS = #{businessChanceStatus,jdbcType=BIT},
      SALES_AMOUNT = #{salesAmount,jdbcType=DECIMAL},
      HOSPITAL_SIZE = #{hospitalSize,jdbcType=BIT},
      BED_NUM = #{bedNum,jdbcType=INTEGER},
      YEAR_IN_SUM = #{yearInSum,jdbcType=DECIMAL},
      CT_DAILY_NUM = #{ctDailyNum,jdbcType=INTEGER},
      IS_NEW_HOSPITAL = #{isNewHospital,jdbcType=BIT},
      NEW_HOSPITAL_PERCENT = #{newHospitalPercent,jdbcType=BIT},
      EXPECT_BUY_TIME = #{expectBuyTime,jdbcType=TIMESTAMP},
      COMPETE_NAME = #{competeName,jdbcType=VARCHAR},
      COMPETE_SKU_NAME = #{competeSkuName,jdbcType=VARCHAR},
      COMPETE_SKU_PRICE = #{competeSkuPrice,jdbcType=DECIMAL},
      IS_NEW_INSTALL = #{isNewInstall,jdbcType=BIT},
      ORIGIN_SKU_BAND = #{originSkuBand,jdbcType=VARCHAR},
      ORIGIN_SKU_MODEL = #{originSkuModel,jdbcType=VARCHAR},
      INSTALL_TIME = #{installTime,jdbcType=TIMESTAMP},
      SALE_COMPANY_NAME = #{saleCompanyName,jdbcType=VARCHAR},
      WIN_ORDER_PERCENT = #{winOrderPercent,jdbcType=BIT},
      MONEY_SOURCE = #{moneySource,jdbcType=VARCHAR},
      MONEY_SITUATION = #{moneySituation,jdbcType=VARCHAR},
      PREPARE_AMOUNT = #{prepareAmount,jdbcType=DECIMAL},
      BUY_TYPE = #{buyType,jdbcType=BIT},
      IS_APPLY_INSPECT = #{isApplyInspect,jdbcType=BIT},
      IS_ENGINE_COMPLETE = #{isEngineComplete,jdbcType=BIT},
      QUOTE_METHOD = #{quoteMethod,jdbcType=VARCHAR},
      QUOTE_METHOD_PRICE = #{quoteMethodPrice,jdbcType=DECIMAL},
      PROJECT_PHASE = #{projectPhase,jdbcType=BIT},
      NEED_COMPLETE = #{needComplete,jdbcType=VARCHAR},
      NEED_TIME = #{needTime,jdbcType=TIMESTAMP},
      ASSIST_DEPARTMENT = #{assistDepartment,jdbcType=VARCHAR},
      PROJECT_EVOLVE = #{projectEvolve,jdbcType=VARCHAR},
      EVOLVE_TIME = #{evolveTime,jdbcType=TIMESTAMP},
      NEXT_PLAN = #{nextPlan,jdbcType=VARCHAR},
      NEED_SUPPORT = #{needSupport,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=BIT},
      TERMINAL_PART_IS_CHANGE = #{terminalPartIsChange,jdbcType=BIT},
      COMPETE_PART_IS_CHANGE = #{competePartIsChange,jdbcType=BIT},
      ORIGIN_INSTALL_PART_IS_CHANGE = #{originInstallPartIsChange,jdbcType=BIT},
      KEY_DEAL_PART_IS_CHANGE = #{keyDealPartIsChange,jdbcType=BIT},
      CHANCE_DETAIL_PART_IS_CHANGE = #{chanceDetailPartIsChange,jdbcType=BIT}
    where GE_BUSSINESS_CHANCE_DETAIL_RECORD_ID = #{geBussinessChanceDetailRecordId,jdbcType=INTEGER}
  </update>

  <select id="queryByGeBussinessChanceDetailId" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    SELECT
    <include refid="Base_Column_List" />
    FROM T_GE_BUSINESS_CHANCE_DETAIL_RECORD
    WHERE GE_BUSSINESS_CHANCE_DETAIL_ID = #{geBussinessChanceDetailId,jdbcType=INTEGER}
  </select>
</mapper>