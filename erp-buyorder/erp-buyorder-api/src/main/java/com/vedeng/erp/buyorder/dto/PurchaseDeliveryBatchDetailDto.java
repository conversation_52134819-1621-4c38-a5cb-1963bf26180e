package com.vedeng.erp.buyorder.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */

@Data
public class PurchaseDeliveryBatchDetailDto {
    private Integer purchaseDeliveryDirectBatchDetailId;

    /**
     * T_PURCHASE_DELIVERY_DIRECT_BATCH_INFO的主键
     */
    private Integer purchaseDeliveryDirectBatchInfoId;

    /**
     * 采购订单的物流明细ID
     */
    private Integer expressDetailId;

    /**
     * 订货号
     */
    private String sku;

    /**
     * 产品名称
     */
    private String skuName;

    /**
     * 型号/规格
     */
    private String model;

    /**
     * 单位
     */
    private String unit;

    /**
     * 生产企业
     */
    private String productCompany;

    /**
     * 生产许可证号
     */
    private String productionLicence;

    /**
     * 注册证号
     */
    private String registerNumber;

    /**
     * 生产批次号/序列号
     */
    private String batchNumber;

    /**
     * 是否时序列号，需要唯一
     */
    private Integer unionSequence;

    /**
     * 收货数量
     */
    private Integer arrivalCount;

    /**
     * WMS采购入库已作业的数量
     */
    private Integer wmsHandledArrivalCount;

    /**
     * WMS销售出库已作业的数量
     */
    private Integer wmsHandledDeliveryCount;

    /**
     * 生产日期
     */
    private Date manufactureDateTime;

    /**
     * 失效日期
     */
    private Date invalidDateTime;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 创建者
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private Date modTime;

    /**
     * 更新者
     */
    private Integer updater;

    /**
     * 创建者中文
     */
    private String creatorName;
}
