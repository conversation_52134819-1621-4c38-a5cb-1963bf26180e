package com.vedeng.erp.aftersale.enums;

/**
 * 用户端售后推送状态
 */
public enum PushClientStatusEnum {


    NONE(0, "无", "无",0),

    SEND(1, "下派工程师", "售后单已下派工程师为您服务，工程师稍后会与联系人电话沟通，确认服务方式与时间，请保持电话畅通。",4),

    APPOINTMENT(2, "工程师完成预约", "工程师已联系客户确认完成服务方式与时间，将会在约定时间为您提供专业售后服务。",5),

    COMPLETE(3, "工程师已完工", "工程师已完成服务，服务过程中如有任何问题，可来电咨询。", 6),
    ;

    private final Integer code;

    private final String title;

    private final String content;

    private final Integer foregroundCode;

    PushClientStatusEnum(Integer code, String title, String content, Integer foregroundCode) {
        this.code = code;
        this.title = title;
        this.content = content;
        this.foregroundCode = foregroundCode;
    }

    public Integer getCode() {
        return code;
    }

    public String getTitle() {
        return title;
    }

    public String getContent() {
        return content;
    }

    public Integer getForegroundCode() {
        return foregroundCode;
    }

    public static String getTitleByCode(Integer code) {
        for (PushClientStatusEnum value : PushClientStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getTitle();
            }
        }
        return "-";
    }

    public static String getContentByCode(Integer code) {
        for (PushClientStatusEnum value : PushClientStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getContent();
            }
        }
        return "-";
    }

    public static Integer getForegroundCodeByCode(Integer code) {
        for (PushClientStatusEnum value : PushClientStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getForegroundCode();
            }
        }
        return 0;
    }
}
