<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.trader.dao.TraderMedicalCategoryMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.trader.model.TraderMedicalCategory" >
    <id column="TRADER_MEDICAL_CATEGORY_ID" property="traderMedicalCategoryId" jdbcType="INTEGER" />
    <result column="TRADER_ID" property="traderId" jdbcType="INTEGER" />
    <result column="TRADER_TYPE" property="traderType" jdbcType="BIT" />
    <result column="MEDICAL_CATEGORY_ID" property="medicalCategoryId" jdbcType="INTEGER" />
    <result column="MEDICAL_CATEGORY_LEVEL" property="medicalCategoryLevel" jdbcType="INTEGER" />
  </resultMap>
  <resultMap id="VoResultMap" type="com.vedeng.trader.model.vo.TraderMedicalCategoryVo" extends="BaseResultMap">
    <result column="TITLE" property="title" jdbcType="VARCHAR" />
  </resultMap>
  
  <sql id="Base_Column_List" >
    TRADER_MEDICAL_CATEGORY_ID, TRADER_ID, TRADER_TYPE, MEDICAL_CATEGORY_ID, MEDICAL_CATEGORY_LEVEL
  </sql>
  <insert id="insertSelective" parameterType="com.vedeng.trader.model.TraderMedicalCategory" >
    insert into T_TRADER_MEDICAL_CATEGORY
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="traderMedicalCategoryId != null" >
        TRADER_MEDICAL_CATEGORY_ID,
      </if>
      <if test="traderId != null" >
        TRADER_ID,
      </if>
      <if test="traderType != null" >
        TRADER_TYPE,
      </if>
      <if test="medicalCategoryId != null" >
        MEDICAL_CATEGORY_ID,
      </if>
      <if test="medicalCategoryLevel != null" >
        MEDICAL_CATEGORY_LEVEL,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="traderMedicalCategoryId != null" >
        #{traderMedicalCategoryId,jdbcType=INTEGER},
      </if>
      <if test="traderId != null" >
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderType != null" >
        #{traderType,jdbcType=BIT},
      </if>
      <if test="medicalCategoryId != null" >
        #{medicalCategoryId,jdbcType=INTEGER},
      </if>
      <if test="medicalCategoryLevel != null" >
        #{medicalCategoryLevel,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

  <select id="getMedicalCategoryList" resultMap="BaseResultMap" parameterType="com.vedeng.trader.model.TraderMedicalCategory" >
    select
    tcmc.TRADER_MEDICAL_CATEGORY_ID, tcmc.TRADER_ID, tcmc.TRADER_TYPE, tcmc.MEDICAL_CATEGORY_ID, tcmc.MEDICAL_CATEGORY_LEVEL
    from T_TRADER_MEDICAL_CATEGORY tcmc
    where 1=1

      and tcmc.TRADER_ID = #{traderId,jdbcType=INTEGER}

    <if test="traderType != null" >
      and tcmc.TRADER_TYPE = #{traderType,jdbcType=BIT}
    </if>
    <if test="medicalCategoryLevel != null" >
      and tcmc.MEDICAL_CATEGORY_LEVEL = #{medicalCategoryLevel,jdbcType=INTEGER}
    </if>
  </select>

  <delete id="delObject" parameterType="com.vedeng.trader.model.TraderMedicalCategory" >
    delete from T_TRADER_MEDICAL_CATEGORY
    where 1=1

      and TRADER_ID = #{traderId,jdbcType=INTEGER}

    <if test="traderType != null" >
      and TRADER_TYPE = #{traderType,jdbcType=BIT}
    </if>
  </delete>
    
  <select id="getTraderMedicalCategoryList" resultMap="VoResultMap" parameterType="com.vedeng.trader.model.TraderMedicalCategory" >
    select 
    	tcmc.TRADER_MEDICAL_CATEGORY_ID, tcmc.TRADER_ID, tcmc.TRADER_TYPE, tcmc.MEDICAL_CATEGORY_ID, tcmc.MEDICAL_CATEGORY_LEVEL,sod.TITLE
	   from T_TRADER_MEDICAL_CATEGORY tcmc
	   LEFT JOIN T_SYS_OPTION_DEFINITION sod ON tcmc.MEDICAL_CATEGORY_ID = sod.SYS_OPTION_DEFINITION_ID
	   where 1=1

        and tcmc.TRADER_ID = #{traderId,jdbcType=INTEGER}

    <if test="traderType != null" >
        and tcmc.TRADER_TYPE = #{traderType,jdbcType=BIT}
    </if>
    <if test="medicalCategoryLevel != null" >
        and tcmc.MEDICAL_CATEGORY_LEVEL = #{medicalCategoryLevel,jdbcType=INTEGER}
    </if>
  </select>

</mapper>