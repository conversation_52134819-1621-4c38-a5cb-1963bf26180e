<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.saleorder.dao.extend.ActivityPreOrderExtendMapper">


    <select id="findExistNumByTraderIdAndActionId" resultType="int">
        SELECT SUM(B.NUM) FROM T_SALEORDER A LEFT JOIN T_SALEORDER_GOODS B ON A.SALEORDER_ID=B.SALEORDER_ID
        WHERE B.IS_ACTION_GOODS=1 AND A.ACTION_ID=#{actionId} AND B.IS_DELETE=0
          AND A.TRADER_ID=#{traderId} and B.GOODS_ID=#{skuId} and A.STATUS &lt; 3
    </select>
</mapper>