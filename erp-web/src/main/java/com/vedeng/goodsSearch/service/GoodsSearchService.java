package com.vedeng.goodsSearch.service;

import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.service.BaseService;
import com.vedeng.goodsSearch.model.dto.GoodsSearchReqDto;
import com.vedeng.price.api.price.dto.price.SkuPriceInfoDetailResponseDto;

import java.util.List;
import java.util.Map;

/**
 * 商品搜索服务层
 *
 * <AUTHOR>
 */
public interface GoodsSearchService extends BaseService {

    /**
     * 商品信息搜索
     *
     * @param goodsSearchReqDto
     * @return
     */
    ResultInfo goodsSearch(GoodsSearchReqDto goodsSearchReqDto);

    /**
     * 根据skuId集合查询价格中心的价格
     *
     * @param skuIdList skuId集合
     * @return Map<Long,SkuPriceInfoDetailResponseDto>
     */
    Map<Long, SkuPriceInfoDetailResponseDto> getPriceBySkuIdList(List<Integer> skuIdList);

}
