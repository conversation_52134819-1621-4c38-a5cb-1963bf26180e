package com.vedeng.common.core.utils;

public class ErpCharUtils {

    // 定义括号常量
    private static final char CHN_LEFT = '（';
    private static final char CHN_RIGHT = '）';
    private static final char ENG_LEFT = '(';
    private static final char ENG_RIGHT = ')';


    // 判断字符串是否包含中英文括号
    public static boolean containsBrackets(String traderName) {
        return traderName.indexOf(CHN_LEFT) != -1 ||
                traderName.indexOf(CHN_RIGHT) != -1 ||
                traderName.indexOf(ENG_LEFT) != -1 ||
                traderName.indexOf(ENG_RIGHT) != -1;
    }

    // 转换方法实现
    public static String toChinese(String s) {
        return s.replace(ENG_LEFT, CHN_LEFT).replace(ENG_RIGHT, CHN_RIGHT);
    }

    public static String toEnglish(String s) {
        return s.replace(CHN_LEFT, ENG_LEFT).replace(CHN_RIGHT, ENG_RIGHT);
    }

    public static String toLeftChineseRightEnglish(String s) {
        return s.replace(ENG_LEFT, CHN_LEFT)  // 左括号转中文
                .replace(CHN_RIGHT, ENG_RIGHT); // 右括号转英文
    }

    public static String toLeftEnglishRightChinese(String s) {
        return s.replace(CHN_LEFT, ENG_LEFT)  // 左括号转英文
                .replace(ENG_RIGHT, CHN_RIGHT); // 右括号转中文
    }
}
