package com.wms.service.output;

import com.vedeng.common.util.StringUtil;
import com.wms.constant.WMSContant;
import com.wms.dto.CancelPoDto;
import com.wms.dto.WmsRequest;
import com.wms.service.AbstractWmsInterface;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @ClassName CancelOutputInterface.java
 * @Description TODO 出库单撤销接口
 * @createTime 2020年08月05日 13:48:00
 */
@Service
public class CancelOutputInterface  extends AbstractWmsInterface {
    @Override
    protected void setMethodAndMessageId(WmsRequest wmsRequest) {
        wmsRequest.setMessageId(WMSContant.CANCEL_ORGGINCAL_SALESORDER);
        wmsRequest.setMethod(WMSContant.CANCEL_ORGGINCAL_SALESORDER);
    }

    @Override
    protected JSONObject getXmlDate(Object... param) {
        CancelPoDto cancelPoDto = (CancelPoDto)param[0];
        JSONObject item = new JSONObject();

        item.put("customerId","VEDENG");
        String warehouseId = cancelPoDto.getWarehouseId();
        if(StringUtil.isBlank(warehouseId)){
            warehouseId = "NJ01";
        }
        item.put("warehouseId",warehouseId);

        item.put("docNo",cancelPoDto.getDocNo());
        item.put("orderType",cancelPoDto.getOrderType());
        item.put("erpCancelReason",cancelPoDto.getErpCancelReason());

        JSONObject ordernoJsonObj = new JSONObject();
        ordernoJsonObj.put("ordernos",item);

        JSONObject dataJsonObj = new JSONObject();
        dataJsonObj.put("data",ordernoJsonObj);

        return dataJsonObj;
    }

    @Override
    protected <T> T parseResponseDate(JSONObject returnObject) {
        return null;
    }
}
