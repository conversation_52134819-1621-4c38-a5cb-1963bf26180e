package com.pricecenter.service.imp;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.pricecenter.dto.*;
import com.pricecenter.service.ContractPriceService;
import com.vedeng.common.http.NewHttpClientUtils;
import com.vedeng.common.util.JsonUtils;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;

@Service
public class ContractPriceServiceImp implements ContractPriceService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ContractPriceServiceImp.class);

    @Value("${price.url}")
    private String priceUrl;

    private static String SUCCESS_CODE = "success";

    private static String FAILED_CODE = "STOCK.UNKNOWN_EXCEPTION";

    private static String BASE_CONTRACTPRICE_PAGE = "contract_price/list/page";

    private static String BATCH_UPDATE_CONTRACTPRICE = "contract_price/batchUpdateContractPrice";

    private static String UPDATE_CONTRACT_PRICE = "contract_price/updateContractPrice";

    private static String ALL_CONTRACTPRICE_PAGE="contract_price/list/pageAll";

    private static String FIND_CUSTOMERID_BYPAGE = "contract_price/findCustomeridByPage";

    private static String BATCH_UPDATE_TRADERINFO = "contract_price/batchUpdateTraderInfo";

    private static String BATCH_ADD_CONTRACTPRICE = "contract_price/batchAddContractPrice";

    private static String VALIDATOR_CONTRACT_EXSIT = "contract_price/findExsitContractPrice";

    private static String VALIDATOR_CONTRACT_NOT_EXSIT = "contract_price/findNotExsitContractPrice";

    private static String DELETE_CONTRACT_PRICE = "contract_price/deleteContractPrice";

    @Override
    public PageResultDto<ContractPrice> findByPage(ContractPriceListPageQueryDto queryDto) {

        PageResultDto pageResultDto = new PageResultDto();


        String requestJson = null;
        try {
            requestJson = JsonUtils.translateToJson(queryDto);
            LOGGER.info("调用价格中心服务"+requestJson);
            JSONObject resultJsonObj= NewHttpClientUtils.httpPost(priceUrl + BASE_CONTRACTPRICE_PAGE,requestJson);
            LOGGER.info("调用价格中心服务成功"+resultJsonObj);
            if(!SUCCESS_CODE.equals(resultJsonObj.get("code")) ){
                return pageResultDto;
            }

            Gson gson = new Gson();

            List<ContractPrice> contractPriceList = gson.fromJson(resultJsonObj.getJSONObject("data").get("contractPriceList").toString(),
                    new TypeToken<List<ContractPrice>>(){}.getType());

            //获取总记录数
            String totleRecord = resultJsonObj.getJSONObject("data").get("totalRecord").toString();
            pageResultDto.setDatas(contractPriceList);
            pageResultDto.setTotalRecords(Integer.valueOf(totleRecord));

        } catch (IOException e) {
            LOGGER.error("调用价格中心服务失败",e);
        }
        return pageResultDto;
    }



    //批量更改商品
    @Override
    public Boolean batchUpdateContract(List<BatchUpdateContractPriceDto> batchUpdateContractPriceDtos) {
        Gson gson=new Gson();
        String priceStr=gson.toJson(batchUpdateContractPriceDtos);
        LOGGER.info("调用价格中心服务批量更改商品"+priceStr);
        JSONObject jsonObject= NewHttpClientUtils.httpPost(priceUrl + BATCH_UPDATE_CONTRACTPRICE,priceStr);
        LOGGER.info("调用价格中心服务批量更改商品成功"+jsonObject);
        return successOrFail(jsonObject);
    }


    //更改单个商品价格
    @Override
    public Boolean updateContractPrice(BatchUpdateContractPriceDto batchUpdateContractPriceDto) {
        Gson gson=new Gson();
        String priceStr=gson.toJson(batchUpdateContractPriceDto);
        LOGGER.info("调用价格中心服务更改单个商品"+priceStr);
        JSONObject jsonObject= NewHttpClientUtils.httpPost(priceUrl+UPDATE_CONTRACT_PRICE,priceStr);
        LOGGER.info("调用价格中心服务更改单个商品成功"+jsonObject);
        return successOrFail(jsonObject);
    }

    @Override
    public PageResultDto<ContractPrice> findByPageAll(ContractPriceListPageQueryDto queryDto) {
        PageResultDto pageResultDto = new PageResultDto();
        String requestJson = null;
        try {
            requestJson = JsonUtils.translateToJson(queryDto);
            LOGGER.info("调用价格中心服务"+requestJson);
            JSONObject resultJsonObj= NewHttpClientUtils.httpPost(priceUrl + ALL_CONTRACTPRICE_PAGE,requestJson);
            LOGGER.info("调用价格中心服务成功"+resultJsonObj);
            if(!SUCCESS_CODE.equals(resultJsonObj.get("code")) ){
                return pageResultDto;
            }

            Gson gson = new Gson();

            List<ContractPrice> contractPriceList = gson.fromJson(resultJsonObj.getJSONObject("data").get("contractPriceList").toString(),
                    new TypeToken<List<ContractPrice>>(){}.getType());
            //获取总记录数
            pageResultDto.setDatas(contractPriceList);

        } catch (IOException e) {
            LOGGER.error("调用价格中心服务失败",e);
        }
        return pageResultDto;
    }

    @Override
    public PageResultDto findCustomerIdByPage(CusomerInfoPageDto cusomerInfoPageDto) {

        PageResultDto pageResultDto = new PageResultDto();

        String requestJson = null;

        try {
            requestJson = JsonUtils.translateToJson(cusomerInfoPageDto);

            LOGGER.info("findCustomerIdByPage->分页查询客户信息请求:" + requestJson);

            JSONObject resultJsonObj= NewHttpClientUtils.httpPost(priceUrl + FIND_CUSTOMERID_BYPAGE,requestJson);

            LOGGER.info("findCustomerIdByPage->分页查询客户信息响应:" + resultJsonObj);

            if(!SUCCESS_CODE.equals(resultJsonObj.get("code")) ){
                return pageResultDto;
            }

            Gson gson = new Gson();

            List<Integer> traderIdList = gson.fromJson(resultJsonObj.getJSONObject("data").get("traderIdList").toString(),
                    new TypeToken<List<Integer>>(){}.getType());
            //获取总记录数
            pageResultDto.setDatas(traderIdList);

            //获取总记录数
            String totleRecord = resultJsonObj.getJSONObject("data").get("totalRecord").toString();
            pageResultDto.setTotalRecords(Integer.valueOf(totleRecord));


        } catch (Exception e) {
            LOGGER.error("调用价格中心服务失败:",e);
        }
        return pageResultDto;
    }

    @Override
    public void batchUpdateTraderinfo(List<ContractTraderDto> traders) {

        try {

            String requestJson = JsonUtils.translateToJson(traders);

            LOGGER.info("batchUpdateTraderinfo->批量更新客户信息请求:" + requestJson);

            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + BATCH_UPDATE_TRADERINFO,requestJson);

            LOGGER.info("batchUpdateTraderinfo->批量更新客户信息请求:" + resultJsonObj);

        } catch (Exception e) {
            LOGGER.error("调用价格中心服务失败:",e);
        }
    }

    /**
     * 校验合约价哪些已经存在了
     * @param validatorDtoList
     * @return
     */
    @Override
    public List<BatchContractPriceValidatorDto> validatorContractExsit(List<BatchContractPriceValidatorDto> validatorDtoList) throws Exception{

        String requestJson = JsonUtils.translateToJson(validatorDtoList);;

        LOGGER.info("validatorContractExsit->校验合约价是否存在请求:" + requestJson);

        JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + VALIDATOR_CONTRACT_EXSIT,requestJson);

        LOGGER.info("validatorContractExsit->校验合约价是否存在响应:" + resultJsonObj);

        if(!successOrFail(resultJsonObj)){
            throw new Exception("请求价格中心失败");
        }

        Gson gson = new Gson();

        List<BatchContractPriceValidatorDto> validatorResultList = gson.fromJson(resultJsonObj.get("data").toString(),
                new TypeToken<List<BatchContractPriceValidatorDto>>(){}.getType());
        return validatorResultList;
    }

    @Override
    public List<BatchContractPriceValidatorDto> validatorContractNotExsit(List<BatchContractPriceValidatorDto> validatorDtoList) throws Exception {
        String requestJson = JsonUtils.translateToJson(validatorDtoList);;

        LOGGER.info("validatorContractNotExsit->校验合约价哪些不存在请求:" + requestJson);

        JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + VALIDATOR_CONTRACT_NOT_EXSIT,requestJson);

        LOGGER.info("validatorContractNotExsit->校验合约价哪些不存在响应:" + resultJsonObj);

        if(!successOrFail(resultJsonObj)){
            throw new Exception("请求价格中心失败");
        }

        Gson gson = new Gson();

        List<BatchContractPriceValidatorDto> validatorResultList = gson.fromJson(resultJsonObj.get("data").toString(),
                new TypeToken<List<BatchContractPriceValidatorDto>>(){}.getType());
        return validatorResultList;
    }

    @Override
    public Boolean deleteContractPrice(DeleteContractPriceDto deleteContractPriceDto) {


        String requestJson = new Gson().toJson(deleteContractPriceDto);

        LOGGER.info("deleteContractPrice->删除合约价请求:" + requestJson);
        JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + DELETE_CONTRACT_PRICE,requestJson);
        LOGGER.info("deleteContractPrice->删除合约价请求:" + resultJsonObj);

        return successOrFail(resultJsonObj);
    }

    /**
     * 批量新增合约价
     * @param contractPriceDtoList
     */
    @Override
    public void batchAddContractPriceDtoList(List<BatchAddContractPriceDto> contractPriceDtoList) {
        try {

            String requestJson = JsonUtils.translateToJson(contractPriceDtoList);

            LOGGER.info("batchAddContractPriceDtoList->批量新增合约价请求:" + requestJson);

            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + BATCH_ADD_CONTRACTPRICE,requestJson);

            LOGGER.info("batchAddContractPriceDtoList->批量新增合约价请求:" + resultJsonObj);

        } catch (Exception e) {
            LOGGER.error("调用价格中心服务失败:",e);
        }
    }

    //调用价格中心服务是否成功
    private Boolean successOrFail(JSONObject jsonObject){
        if (jsonObject==null){
            return false;
        }
        if (SUCCESS_CODE.equals(jsonObject.get("code"))){
            return true;
        }
        return false;
    }
}
