<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="售后详情-换货" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<script type="text/javascript"
        src='${pageContext.request.contextPath}/static/js/orderstream/buyorder/view_afterSales_buyorder.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript">
    $(function () {
        var url = page_url + '/order/newBuyorder/viewAfterSalesDetail.do?afterSalesId=' + $("#afterSalesId").val();
        if ($(window.frameElement).attr('src').indexOf("viewAfterSalesDetail") < 0) {
            $(window.frameElement).attr('data-url', url);
        }
    });
</script>

<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<script src="${pageContext.request.contextPath}/static/js/orderstream/buyorder/jquery.js"></script>
<script src="${pageContext.request.contextPath}/static/js/orderstream/index.js?rnd=${resourceVersionKey}"></script>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/order/style.css">
<style>
    .warn {
        color: #fc5151;
    }

    .layui-form-label {
        width: 100px;
    }

    .length {
        width: 150px;
        text-align: left;
    }

    .distance {
        margin-left: 100px;
    }

    .verify{
        padding-bottom: 40px;
    }

    .no-wrap{
        max-width: 150px;
        display: inline-block;
        word-break: break-all;
        white-space: normal;
    }

    .t-line-wrap .t-line-item::before {
        top: 10px;
    }

    .t-line-wrap .t-line-item .t-line-item-tip {
        position: static;
    }

    .t-line-wrap .t-line-item {
        align-items: flex-start;
    }

</style>


<div class="main-container">
    <div class="t-line-wrap J-line-wrap" id="test" style="margin-left: 10%;"
         data-json='${statusList}'></div>
    <hr>

    <form action="" method="post" id="myform">
        <input type="hidden" name="afterSalesId" value="${afterSalesVo.afterSalesId}"/>
        <input type="hidden" name="orderId" value="${afterSalesVo.orderId}"/>
        <input type="hidden" name="subjectType" value="${afterSalesVo.subjectType}"/>
        <input type="hidden" name="type" value="${afterSalesVo.type}"/>
        <input type="hidden" name="formToken" value="${formToken}"/>
        <input type="hidden" name="taskId" value="${taskInfo.id == null ?0: taskInfo.id}"/>
    </form>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                基本信息
            </div>
        </div>
        <table class="table">
            <tbody>
            <tr>
                <td class="wid20">售后单号</td>
                <td>${afterSalesVo.afterSalesNo}</td>
                <td>售后类型</td>
                <td class="warning-color1">${afterSalesVo.typeName}</td>
            </tr>
            <tr>
                <td>创建人</td>
                <td>${afterSalesVo.creatorName}</td>
                <td class="wid20">售后状态</td>
                <td>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 0}">待确认</c:if>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 1}">进行中</c:if>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 2}">已完结</c:if>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 3}">已关闭</c:if>
                </td>
            </tr>

            <tr>
                <td>审核状态</td>
                <td>
                    <c:if test="${afterSalesVo.status eq 0}">待审核</c:if>
                    <c:if test="${afterSalesVo.status eq 1}">审核中</c:if>
                    <c:if test="${afterSalesVo.status eq 2}">审核通过</c:if>
                    <c:if test="${afterSalesVo.status eq 3}">审核不通过</c:if>
                </td>
                <td>生效状态</td>
                <td>
                    <c:if test="${afterSalesVo.validStatus eq 0}">未生效</c:if>
                    <c:if test="${afterSalesVo.validStatus eq 1}">已生效</c:if>
                </td>
            </tr>
            <tr>
                <td>创建时间</td>
                <td><date:date value ="${afterSalesVo.addTime}"/></td>
                <td>生效时间</td>
                <td><date:date value ="${afterSalesVo.validTime}"/></td>
            </tr>
            </tbody>
        </table>
    </div>

    <input type="hidden" id="afterSalesId" value="${afterSalesVo.afterSalesId}"/>

    <c:if test="${not empty afterSaleVerifyFlag}">
        <div class="parts verify" style="padding-bottom: 0;">
            <div class="layui-tab layui-tab-brief">
                <ul class="layui-tab-title">
                    <li <c:if test="${verifyShowFlag eq 1}">class="layui-this" </c:if>>订单审核</li>
                    <c:if test="${payVerifyFlag eq 1}">
                        <li <c:if test="${verifyShowFlag eq 3}">class="layui-this" </c:if>>付款审核</li>
                    </c:if>
                </ul>
                <div class="layui-tab-content">
                    <h3>审核进度</h3>
                    <div
                            <c:if test="${verifyShowFlag eq 1}">class="layui-tab-item layui-show" </c:if>
                            <c:if test="${verifyShowFlag ne 1}">class="layui-tab-item" </c:if>>
                        <div class="t-line-wrap J-line-wrap" id="orderValid"
                             data-json='${afterSaleVerifyInfoStr}'></div>
                    </div>
                    <c:if test="${payVerifyFlag eq 1}">
                        <div
                                <c:if test="${verifyShowFlag eq 3}">class="layui-tab-item layui-show" </c:if>
                                <c:if test="${verifyShowFlag ne 3}">class="layui-tab-item" </c:if>
                        >
                            <div class="t-line-wrap J-line-wrap" id="orderClose"
                                 data-json='${payVerifyInfoStr}'></div>
                        </div>
                    </c:if>
                </div>
            </div>
        </div>
    </c:if>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                售后申请
            </div>
        </div>
        <table class="table">
            <tbody>
            <tr>
                <td class="wid20">售后原因</td>
                <td>${afterSalesVo.reasonName}</td>
                <td class="wid20">联系人</td>
                <td>${afterSalesVo.traderContactName}</td>
            </tr>
            <tr>
                <td>电话</td>
                <td>
                    ${afterSalesVo.traderContactTelephone}
                    <c:if test="${not empty afterSalesVo.traderContactTelephone}">
                        <i class="icontel cursor-pointer" title="点击拨号"
                           onclick="callout('${afterSalesVo.traderContactTelephone}',${afterSalesVo.traderId},1,4,${afterSalesVo.afterSalesId},${afterSalesVo.traderContactId});"></i>
                    </c:if>
                </td>
                <td>手机</td>
                <td>
                    ${afterSalesVo.traderContactMobile}
                    <c:if test="${not empty afterSalesVo.traderContactMobile}">
                        <i class="icontel cursor-pointer" title="点击拨号"
                           onclick="callout('${afterSalesVo.traderContactMobile}',${afterSalesVo.traderId},1,4,${afterSalesVo.afterSalesId},${afterSalesVo.traderContactId});"></i>
                    </c:if>
                </td>
            </tr>
            <tr>
                <td>收货地址</td>
                <td cclass="text-left">${afterSalesVo.area}&nbsp&nbsp${afterSalesVo.address}</td>
                <td>详情说明</td>
                <td class="text-left">${afterSalesVo.comments}</td>
            </tr>
            <tr>
                <td>附件</td>
                <td colspan="3" class="text-left">
                    <c:if test="${not empty afterSalesVo.attachmentList }">
                        <c:forEach items="${afterSalesVo.attachmentList }" var="att">
                            <a href="http://${att.domain}${att.uri}" target="_blank">${att.name}</a>&nbsp;&nbsp;
                        </c:forEach>
                    </c:if>
                </td>

            </tr>
            </tbody>
        </table>
    </div>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                所属订单
            </div>
        </div>
        <table class="table">
            <tbody>
            <tr>
                <td class="wid20">采购单号</td>
                <td>
                    <div class="customername pos_rel">
                               <span class="brand-color1 addtitle" style="float:none;"
                                     tabTitle='{"num":"viewsaleorder${afterSalesVo.orderNo}","title":"订单信息",
                               		"link":"./order/newBuyorder/newViewBuyOrderDetail.do?buyorderId=${afterSalesVo.orderId}"}'><c:if test="${isGift == 1}"><span style="color:#FF0000">赠品订单</span></c:if>${afterSalesVo.orderNo}</span><i
                            class="iconbluemouth"></i>
                        <div class="pos_abs customernameshow" style="display: none;">
                            付款状态：<c:if test="${afterSalesVo.paymentStatus eq 0}">未付款</c:if>
                            <c:if test="${afterSalesVo.paymentStatus eq 1}">部分付款</c:if>
                            <c:if test="${afterSalesVo.paymentStatus eq 2}">全部付款</c:if><br>
                            发货状态：<c:if test="${afterSalesVo.deliveryStatus eq 0}">未发货</c:if>
                            <c:if test="${afterSalesVo.deliveryStatus eq 1}">部分发货</c:if>
                            <c:if test="${afterSalesVo.deliveryStatus eq 2}">全部发货</c:if><br>
                            收货状态：<c:if test="${afterSalesVo.arrivalStatus eq 0}">未收货</c:if>
                            <c:if test="${afterSalesVo.arrivalStatus eq 1}">部分收货</c:if>
                            <c:if test="${afterSalesVo.arrivalStatus eq 2}">全部收货</c:if><br>
                            收票状态：<c:if test="${afterSalesVo.invoiceStatus eq 0}">未收票</c:if>
                            <c:if test="${afterSalesVo.invoiceStatus eq 1}">部分收票</c:if>
                            <c:if test="${afterSalesVo.invoiceStatus eq 2}">全部收票</c:if>
                        </div>
                    </div>
                </td>
                <td class="wid20">订单金额</td>
                <td><fmt:formatNumber type="number" value="${afterSalesVo.totalAmount}" pattern="0.00"
                                      maxFractionDigits="2"/></td>
            </tr>
            <tr>
                <td>部门</td>
                <td>${afterSalesVo.orgName}</td>
                <td>创建人</td>
                <td>${afterSalesVo.buyorderCreatorName}</td>
            </tr>
            <tr>
                <td>订单状态</td>
                <td>
                    <c:if test="${afterSalesVo.buyorderStatus eq 0}">待确认</c:if>
                    <c:if test="${afterSalesVo.buyorderStatus eq 1}">进行中</c:if>
                    <c:if test="${afterSalesVo.buyorderStatus eq 2}">已完结</c:if>
                    <c:if test="${afterSalesVo.buyorderStatus eq 3}">已关闭</c:if>
                </td>
                <td>生效时间</td>
                <td><date:date value="${afterSalesVo.buyorderValidTime}"/></td>
            </tr>
            <tr>
                <td>供应商名称</td>
                <td>
                    <div class="customername pos_rel">
                                  <span class="brand-color1 addtitle" style="float:none;"
                                        tabTitle='{"num":"viewcustomer${afterSalesVo.traderId}","title":"供应商信息",
										"link":"./trader/supplier/baseinfo.do?traderId=${afterSalesVo.traderId}"}'>${afterSalesVo.traderName}</span><i
                            class="iconbluemouth"></i>
                        <div class="pos_abs customernameshow" style="display: none;">
                            交易次数：${afterSalesVo.orderCount}<br>
                            交易金额：<fmt:formatNumber type="number" value="${afterSalesVo.orderTotalAmount}" pattern="0.00"
                                                   maxFractionDigits="2"/><br>
                            上次交易日期：<date:date value="${afterSalesVo.lastOrderTime}"/>
                        </div>
                    </div>
                </td>
                <td>供应商等级</td>
                <td>
                    <c:if test="${afterSalesVo.grade eq 59}">核心供应商</c:if>
                    <c:if test="${afterSalesVo.grade eq 60}">普通供应商</c:if>
                </td>
            </tr>
            </tbody>
        </table>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">换货信息</div>
        </div>
        <table class="table  table-style6">
            <thead>
            <tr>
                <th class="wid6">序号</th>
                <th class="wid6">订货号</th>
                <th class="wid18">产品名称</th>
                <th class="wid10">品牌</th>
                <th class="wid8">规格/型号</th>
                <th class="wid5">单位</th>
                <th class="wid8">单价</th>
                <th class="wid8">数量</th>
                <th class="wid5">直发</th>
                <th class="wid8">已收数量</th>
                <th class="wid8">已退回数量</th>
                <th class="wid8">已重发数量</th>
                <th class="wid8">换货数量</th>
                <th class="wid8">换货方式</th>
                <th class="wid8">销售换货单</th>
            </tr>
            </thead>
            <tbody>
            <c:if test="${not empty afterSalesVo.afterSalesGoodsList}">
                <c:forEach items="${afterSalesVo.afterSalesGoodsList}" var="asg" varStatus="sttaus">
                    <tr>
                        <td>${sttaus.count}</td>
                        <td>${asg.sku}</td>
                        <td class="text-left">
                            <div class="customername pos_rel">
		                                       <span class="brand-color1 addtitle" style="float:none;"
                                                     tabTitle='{"num":"viewgoods${list.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${asg.goodsId}",
		                                       					"title":"产品信息"}'>${newSkuInfosMap[asg.sku].SHOW_NAME}</span><i
                                    class="iconbluemouth"></i><br>
                                ${newSkuInfosMap[asg.sku].MATERIAL_CODE}
                                <c:set var="skuNo" value="${asg.sku}"></c:set>
                                <%@ include file="../../common/new_sku_common_tip.jsp" %>
                            </div>
                        </td>
                        <td>${asg.brandName}</td>
                        <td>
                                ${asg.spec}/${asg.model}
                        </td>
                        <td>${asg.unitName}</td>
                        <td>${asg.buyorderPrice}</td>
                        <td>${asg.buyorderNum}</td>
                        <td>
                            <c:if test="${asg.buyorderDeliveryDirect eq 0}">否</c:if>
                            <c:if test="${asg.buyorderDeliveryDirect eq 1}">是</c:if>
                        </td>
                        <td>${asg.arrivalNum}</td>
                        <td>${asg.exchangeReturnNum}</td>
                        <td>${asg.exchangeDeliverNum}</td>
                        <td class="warning-color1">${asg.num}</td>
                        <td class="warning-color1">
                            <c:if test="${asg.deliveryDirect eq 0}">普发</c:if>
                            <c:if test="${asg.deliveryDirect eq 1}">直发</c:if>
                        </td>
                        <td class="warning-color1" style="text-align: center">
                            <c:if test="${not empty asg.saleorderAfterSales}">
                                <c:forEach items="${asg.saleorderAfterSales}" var="asgSub">
                                    <div class=" addtitle" style="color: #3384ef"
                                         tabTitle='{"num":"viewAfterSalesOrder${asgSub.afterSalesId}","link":"./order/aftersalesUpgrade/viewAfterSalesDetail.do?afterSalesId=${asgSub.afterSalesId}",
		                                       					"title":"售后详情"}'>
                                            ${asgSub.afterSalesNo}
                                    </div>
                                </c:forEach>
                            </c:if>
                        </td>
                    </tr>
                </c:forEach>

            </c:if>
            <c:if test="${empty afterSalesVo.afterSalesGoodsList}">
                <tr>
                    <td colspan="15">暂无记录！</td>
                </tr>
            </c:if>
            <tr>
                <td colspan="15" class="allchosetr text-left">
                    <c:set var="sum" value="0"></c:set>
                    <c:forEach items="${afterSalesVo.afterSalesGoodsList}" var="asg">
                        <c:set var="sum" value="${sum+asg.num}"></c:set>
                    </c:forEach>
                    换货总件数:<span class="warning-color1 mr10">${sum}</span>
                </td>
            </tr>
            </tbody>
        </table>
    </div>

    <div class="tcenter" style="margin-top: 30px; margin-bottom: 15px">
        <c:if test="${not empty applyFlag}">
            <button type="button" class="bt-bg-style bg-light-blue bt-small" onclick="applyAudit();">申请审核</button>
        </c:if>
        <c:if test="${not empty overFlag}">
            <c:if test="${ empty alertFlag}">
                <button type="button" class="bt-bg-style bg-light-blue bt-small" onclick="confirmComplete();">确认完成</button>
            </c:if>
            <c:if test="${alertFlag eq 1}">
                <button type="button" class="bt-bg-style bg-light-blue bt-small" onclick="confirmCompleteAlert2();">确认完成</button>
            </c:if>
        </c:if>
        <c:if test="${not empty closeFlag}">
            <button type="button" class="bt-bg-style bg-light-blue bt-small" onclick="colse();">关闭订单</button>
        </c:if>
        <c:if test="${not empty editFlag}">
            <button type="button" class="bt-bg-style bg-light-blue bt-small" onclick="editAfterSales();">编辑</button>
        </c:if>
        <c:if test="${not empty validFlag}">
            <button type="button" class="bt-bg-style bg-light-blue bt-small mr10 pop-new-data"
                    layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./complement.do?taskId=${taskInfo.id}&pass=true&type=2"}'>
                审核通过
            </button>
            <button type="button" class="bt-bg-style bg-light-blue bt-small mr10 pop-new-data"
                    layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./complement.do?taskId=${taskInfo.id}&pass=false&type=2"}'>
                审核不通过
            </button>
        </c:if>
    </div>



    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                售后服务费
            </div>
            <c:if test="${isCreator eq 1}">
                <c:if test="${(afterSalesVo.atferSalesStatus eq 0 && (afterSalesVo.status eq 0 || afterSalesVo.status eq 3)) || (afterSalesVo.atferSalesStatus eq 1 && (afterSalesVo.refundAmountStatus eq 0 || afterSalesVo.refundAmountStatus eq 1))}">
                <div class="title-click nobor  pop-new-data" layerParams='{"width":"600px","height":"250px","title":"编辑售后服务费",
		                  "link":"<%= basePath %>/aftersales/order/editInstallstionPage.do?afterSalesDetailId=${afterSalesVo.afterSalesDetailId}&&afterSalesId=${afterSalesVo.afterSalesId}&realRefundAmount=${afterSalesVo.realRefundAmount}&&refundAmount=${afterSalesVo.refundAmount}&&serviceAmount=${afterSalesVo.serviceAmount}&&isSendInvoice=${afterSalesVo.isSendInvoice}&&invoiceType=${afterSalesVo.invoiceType}&flag=bth"}'>
                    编辑
                </div>
            </c:if>
            </c:if>
        </div>
        <table class="table">
            <tbody>
            <tr>
                <td>退换货手续费</td>
                <td>${afterSalesVo.serviceAmount }</td>
                <td>票种</td>
                <td>
                    <c:if test="${afterSalesVo.invoiceType eq 429}">17%增值税专用发票
                        <c:if test="${afterSalesVo.isSendInvoice eq 1}">（寄送）</c:if>
                        <c:if test="${afterSalesVo.isSendInvoice eq 0}">（不寄送）</c:if>
                    </c:if>
                    <c:if test="${afterSalesVo.invoiceType eq 430}">17%增值税普通发票
                        <c:if test="${afterSalesVo.isSendInvoice eq 1}">（寄送）</c:if>
                        <c:if test="${afterSalesVo.isSendInvoice eq 0}">（不寄送）</c:if>
                    </c:if>
                    <c:if test="${afterSalesVo.invoiceType eq 682}">16%增值税专用发票
                        <c:if test="${afterSalesVo.isSendInvoice eq 1}">（寄送）</c:if>
                        <c:if test="${afterSalesVo.isSendInvoice eq 0}">（不寄送）</c:if>
                    </c:if>
                    <c:if test="${afterSalesVo.invoiceType eq 681}">16%增值税普通发票
                        <c:if test="${afterSalesVo.isSendInvoice eq 1}">（寄送）</c:if>
                        <c:if test="${afterSalesVo.isSendInvoice eq 0}">（不寄送）</c:if>
                    </c:if>

                    <c:if test="${afterSalesVo.invoiceType eq 972}">13%增值税专用发票
                        <c:if test="${afterSalesVo.isSendInvoice eq 1}">（寄送）</c:if>
                        <c:if test="${afterSalesVo.isSendInvoice eq 0}">（不寄送）</c:if>
                    </c:if>
                    <c:if test="${afterSalesVo.invoiceType eq 971}">13%增值税普通发票
                        <c:if test="${afterSalesVo.isSendInvoice eq 1}">（寄送）</c:if>
                        <c:if test="${afterSalesVo.isSendInvoice eq 0}">（不寄送）</c:if>
                    </c:if>
                    <c:if test="${afterSalesVo.invoiceType eq 683}">6%增值税普通发票
                        <c:if test="${afterSalesVo.isSendInvoice eq 1}">（寄送）</c:if>
                        <c:if test="${afterSalesVo.isSendInvoice eq 0}">（不寄送）</c:if>
                    </c:if>
                    <c:if test="${afterSalesVo.invoiceType eq 684}">6%增值税专用发票
                        <c:if test="${afterSalesVo.isSendInvoice eq 1}">（寄送）</c:if>
                        <c:if test="${afterSalesVo.isSendInvoice eq 0}">（不寄送）</c:if>
                    </c:if>
                    <c:if test="${afterSalesVo.invoiceType eq 685}">3%增值税普通发票
                        <c:if test="${afterSalesVo.isSendInvoice eq 1}">（寄送）</c:if>
                        <c:if test="${afterSalesVo.isSendInvoice eq 0}">（不寄送）</c:if>
                    </c:if>
                    <c:if test="${afterSalesVo.invoiceType eq 686}">3%增值税专用发票
                        <c:if test="${afterSalesVo.isSendInvoice eq 1}">（寄送）</c:if>
                        <c:if test="${afterSalesVo.isSendInvoice eq 0}">（不寄送）</c:if>
                    </c:if>
                    <c:if test="${afterSalesVo.invoiceType eq 687}">0%增值税普通发票
                        <c:if test="${afterSalesVo.isSendInvoice eq 1}">（寄送）</c:if>
                        <c:if test="${afterSalesVo.isSendInvoice eq 0}">（不寄送）</c:if>
                    </c:if>
                    <c:if test="${afterSalesVo.invoiceType eq 688}">0%增值税专用发票
                        <c:if test="${afterSalesVo.isSendInvoice eq 1}">（寄送）</c:if>
                        <c:if test="${afterSalesVo.isSendInvoice eq 0}">（不寄送）</c:if>
                    </c:if>
                </td>
            </tr>
            </tbody>
        </table>
    </div>

<c:if test="${payInfoFlag eq 1}">
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                付款申请
            </div>
            <c:if test="${isCreator eq 1}">
            <c:if test="${afterSalesVo.atferSalesStatus ne 3 && afterSalesVo.atferSalesStatus ne 2&& payStatus ne 0}">
                <div class="title-click nobor addtitle" tabTitle='{"num":"viewafterSalesId${afterSalesVo.afterSalesId}",
                    "link":"./aftersales/order/applyPayPage.do?afterSalesId=${afterSalesVo.afterSalesId}&traderSubject=536","title":"申请付款"}'>申请付款</div></c:if>
        </div>
        </c:if>
        <table class="table">
            <thead>
            <tr>
                <th class="wid6">申请金额</th>
                <th class="wid8">申请时间</th>
                <th class="wid12">申请人</th>
                <th class="wid8">交易名称</th>
                <th class="wid15">开户行及联行号</th>
                <th class="wid10">银行帐号</th>
                <th class="wid10">付款备注</th>
                <th class="wid5">审核状态</th>

                <th class="wid5">查看</th>
            </tr>
            </thead>
            <tbody>
            <c:if test="${not empty afterSalesVo.afterPayApplyList}">
                <c:forEach items="${afterSalesVo.afterPayApplyList}" var="apal"
                           varStatus="num_index">
                    <tr>
                        <td><fmt:formatNumber type="number" value="${apal.amount}" pattern="0.00"
                                              maxFractionDigits="2"/></td>
                        <td><date:date value="${apal.addTime}" format="yyyy.MM.dd HH:mm:ss"/></td>
                        <td>${apal.creatorName}</td>
                        <td>${apal.traderName}</td>
                        <td>
                                ${apal.bank}<br/>${apal.bankCode}
                        </td>
                        <td>${apal.bankAccount}</td>
                        <td>${apal.comments}</td>
                        <td>
                            <c:choose>
                                <c:when test="${apal.validStatus eq 0}">待审核</c:when>
                                <c:when test="${apal.validStatus eq 1}">通过</c:when>
                                <c:when test="${apal.validStatus eq 2}">不通过</c:when>
                                <c:otherwise>--</c:otherwise>
                            </c:choose>
                        </td>
                        <td>
                            <div class="caozuo">
                                <span class="caozuo-blue pop-new-data"
                                      layerparams='{"width":"50%","height":"30%","title":"付款申请审核信息","link":"<%=basePath%>finance/after/paymentVerify.do?payApplyId=${apal.payApplyId}"}'>查看</span>
                            </div>
                        </td>
                    </tr>
                </c:forEach>
            </c:if>
            <c:if test="${empty afterSalesVo.afterPayApplyList}">
                <tr>
                    <td colspan="9">暂无记录</td>
                </tr>
            </c:if>
            </tbody>
        </table>
    </div>
</c:if>

<c:if test="${invoiceFlag eq 1}">
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">交易信息</div>
        </div>
        <table class="table">
            <thead>
            <tr>
                <th>记账编号</th>
                <th>业务类型</th>
                <th>交易金额</th>
                <th>交易时间</th>
                <th>交易主体</th>
                <th>交易方式</th>
                <th>交易名称</th>
                <th>交易备注</th>
                <th>操作人</th>
                <th>操作时间</th>
                <th>电子回执单</th>
            </tr>
            </thead>
            <tbody>
            <c:if test="${not empty afterSalesVo.afterCapitalBillList}">
                <c:forEach items="${afterSalesVo.afterCapitalBillList}" var="acb">
                    <tr>
                        <td>${acb.capitalBillNo}</td>
                        <td>
                            <c:if test="${acb.capitalBillDetail.bussinessType eq 525}">订单付款</c:if>
                            <c:if test="${acb.capitalBillDetail.bussinessType eq 526}">订单收款</c:if>
                            <c:if test="${acb.capitalBillDetail.bussinessType eq 531}">退款</c:if>
                            <c:if test="${acb.capitalBillDetail.bussinessType eq 532}">资金转移</c:if>
                            <c:if test="${acb.capitalBillDetail.bussinessType eq 533}">信用还款</c:if>
                        </td>
                        <td><fmt:formatNumber type="number" value="${acb.amount}" pattern="0.00"
                                              maxFractionDigits="2"/></td>
                        <td>
                            <c:if test="${acb.traderTime != 0}">
                                <date:date value="${acb.traderTime}"/>
                            </c:if>
                        </td>
                        <td>
                            <c:if test="${acb.traderSubject == 1}">
                                对公
                            </c:if>
                            <c:if test="${acb.traderSubject == 2}">
                                对私
                            </c:if>
                        </td>
                        <td>
                            <c:if test="${acb.traderMode eq 520}">支付宝</c:if>
                            <c:if test="${acb.traderMode eq 521}">银行</c:if>
                            <c:if test="${acb.traderMode eq 522}">微信</c:if>
                            <c:if test="${acb.traderMode eq 522}">现金</c:if>
                            <c:if test="${acb.traderMode eq 527}">信用支付</c:if>
                            <c:if test="${acb.traderMode eq 528}">余额支付</c:if>
                            <c:if test="${acb.traderMode eq 529}">退还信用</c:if>
                            <c:if test="${acb.traderMode eq 530}">退还余额</c:if>
                        </td>
                        <td>${acb.payer}</td>
                        <td class="text-left">${acb.comments}</td>
                        <td>${acb.creatorName}
                        </td>
                        <td>
                            <c:if test="${acb.addTime != 0}">
                                <date:date value="${acb.addTime}"/>
                            </c:if>
                        </td>
                        <td>
                            <c:if test="${(acb.traderType == 2 || acb.traderType == 5) && acb.bankBillId != 0}">
                                <div class="caozuo">
                                    <span class="caozuo-blue addtitle"
                                          tabTitle='{"num":"credentials${acb.bankBillId}", "link":"<%=basePath%>finance/capitalbill/credentials.do?bankBillId=${acb.bankBillId}","title":"电子回执单"}'>查看</span>
                                </div>
                            </c:if>
                        </td>
                    </tr>
                </c:forEach>
            </c:if>
            <c:if test="${ empty afterSalesVo.afterCapitalBillList}">
                <tr>
                    <td colspan="11">暂无记录</td>
                </tr>
            </c:if>
            </tbody>
        </table>
    </div>
</c:if>
<c:if test="${refoundInGoodsFlag eq 1}">
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">换货入库记录</div>
            
            <!--入库数量  -->
            <c:set var="returnCount" value="0"></c:set>
			<c:forEach items="${afterSalesVo.afterReturnInstockList}" var="item">
			    <c:set var="returnCount" value="${returnCount+item.num }"></c:set>
			</c:forEach>
			<!--售后数量  -->
			<c:set var="salesCount" value="0"></c:set>
			<c:forEach items="${afterSalesVo.afterSalesGoodsList}" var="item">
			    <c:set var="salesCount" value="${salesCount+item.num }"></c:set>
			</c:forEach>

            <!--换货方式  -->
            <c:set var="salesDir" value="0"></c:set>
            <c:forEach items="${afterSalesVo.afterSalesGoodsList}" var="item">
                <c:if test="${item.deliveryDirect eq 1}">
                    <c:set var="salesDir" value="${item.deliveryDirect }"></c:set>
                </c:if>
            </c:forEach>

            <c:if test="${afterSalesVo.type eq 547 && salesDir eq 1 && afterSalesVo.atferSalesStatus eq 1 && returnCount < salesCount && afterSalesVo.status eq 2}">
                <div class="title-click nobor pop-new-data" layerParams='{"width":"1700px","height":"540px","title":"新增直发入库","link":"./toAddDirectWarehousePage.do?afterSalesNo=${afterSalesVo.afterSalesNo}&afterSalesId=${afterSalesVo.afterSalesId}&type=${afterSalesVo.type}"}'>确认入库</div>
            </c:if>
        </div>
        <table class="table  table-style6">
            <thead>
            <tr>
                <th class="wid5">序号</th>
                <th>订货号</th>
                <th>产品名称</th>
                <th>品牌</th>
                <th>规格/型号</th>
                <th>实际收货数量</th>
                <%--<th>物料编码</th>--%>
                <th>单位</th>
                <th>贝登批次码</th>
                <%--<th>厂家批次</th>--%>
                <%--<th>入库数量</th>--%>
                <th>生产日期</th>
                <th>有效期至</th>
                <%--<th class="wid12">操作人</th>--%>
                <th class="wid15">入库时间</th>
                <%--<th class="wid8">操作</th>--%>
                <th>厂家批次号</th>
                <th>灭菌批号</th>
                <th>注册证号</th>
            </tr>
            </thead>
            <tbody>
            <c:if test="${not empty afterSalesVo.afterReturnInstockList}">
                <c:forEach items="${afterSalesVo.afterReturnInstockList}" var="arg"
                           varStatus="num_index">
                    <tr>
                        <td>${num_index.count }</td>
                        <td>${arg.sku}</td>
                        <td class="text-left">
                            <a class="brand-color1 addtitle" href="javascript:void(0);"
                               tabTitle='{"num":"viewgoods${arg.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${arg.goodsId}","title":"产品信息"}'>${newSkuInfosMap[arg.sku].SHOW_NAME}</a>
                        </td>
                        <td>${newSkuInfosMap[arg.sku].BRAND_NAME}</td>
                        <td>${arg.spec}/${arg.model}</td>
                        <td>${arg.num}</td>
                        <td>${newSkuInfosMap[arg.sku].UNIT_NAME}</td>
                        <td>${arg.vedengBatchNumer}</td>
                            <%--生产日期--%>
                        <td><date:date value="${arg.productDate}" format="yyyy-MM-dd"/></td>
                        <td><date:date value="${arg.expirationDate}" format="yyyy-MM-dd"/></td>
                        <td><date:date value="${arg.checkStatusTime}" format="yyyy-MM-dd HH:mm:ss"/></td>
                            <%--<td>${arg.operator}</td>--%>
                        <td>${arg.batchNumber}</td>
                        <td>${arg.sterilizationBatchNo}</td>
                        <td>
                            <a class="brand-color1 addtitle"
                               href="javascript:void(0);"
                               tabTitle='{"link":"./firstengage/baseinfo/getFirstSearchDetail.do?firstEngageId=${arg.firstEngageId}","title":"注册证号详情页"}'>${arg.registrationNumber}</a>

                        </td>
                    </tr>
                </c:forEach>
            </c:if>
            <c:if test="${empty afterSalesVo.afterReturnInstockList}">
                <tr>
                    <td colspan="14">暂无记录</td>
                </tr>
            </c:if>
            </tbody>
        </table>
    </div>
</c:if>

    <c:if test="${not empty refoundOutGoodsFlag}">
        <div class="parts">
            <div class="title-container">
                <div class="table-title nobor">换货出库记录</div>
                <c:if test="${afterSalesVo.atferSalesStatus eq 1 && outShowFlag eq 1}">
                        <div class="title-click nobor  pop-new-data"
                             layerParams='{"width":"100%","height":"500px","title":"新增直发出库","link":"<%= basePath %>/order/newBuyorder/addDirectExchangeOutLogPage.do?afterSalesId=${afterSalesVo.afterSalesId}"}'>
                            确认出库
                        </div>
                </c:if>
            </div>
            <table class="table  table-style6">
                <thead>
                <tr>

                    <th class="wid5">序号</th>
                    <th class="wid10">订货号</th>
                    <th>产品名称</th>
                    <th>品牌</th>
                    <th>规格/型号</th>
                    <th>实际出货数量</th>
                        <%--	<th class="wid10">物料编码</th>--%>
                    <th class="wid4">单位</th>
                    <th>贝登批次码</th>
                    <th>生产日期</th>
                    <th>有效期至</th>
                    <th>出库日期</th>
                        <%--<th>贝登条码</th>--%>
                    <th>厂家批次号</th>
                        <%--<th>批次号</th>
                        <th>出库数量</th>
                        <th>出库时间</th>--%>


                    <th>灭菌编号</th>

                    <th class="wid12">注册证号</th>
                </tr>
                </thead>
                <tbody>
                <c:if test="${not empty afterSalesVo.afterReturnOutstockList}">
                    <c:forEach items="${afterSalesVo.afterReturnOutstockList}" var="arg"
                               varStatus="num_index">
                        <tr>
                            <td>${num_index.count }</td>
                            <td>${arg.sku}</td>
                            <td class="text-left">
                        <span class="brand-color1 addtitle"
                              tabTitle='{"num":"viewgoods${arg.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${arg.goodsId}", "title":"产品信息"}'>
                                ${newSkuInfosMap[arg.sku].SHOW_NAME}
                        </span>
                            </td>
                            <td>${newSkuInfosMap[arg.sku].BRAND_NAME}</td>
                            <td>${newSkuInfosMap[arg.sku].MODEL}</td>
                                <%--<td>${newSkuInfosMap[listout.sku].MATERIAL_CODE}</td>--%>
                            <td>${ arg.realGoodsNum}</td>
                            <td>${newSkuInfosMap[arg.sku].UNIT_NAME}</td>
                            <td>${ arg.vedengBatchNumer}</td>
                            <td><date:date value="${arg.productDate}"
                                           format="yyyy-MM-dd HH:mm:ss"/></td>
                            <td><date:date value="${arg.expirationDate}"
                                           format="yyyy-MM-dd HH:mm:ss"/></td>
                            <td><date:date value="${arg.checkStatusTime}"
                                           format="yyyy-MM-dd HH:mm:ss"/></td>

                            <td>${ arg.batchNumber}</td>

                                <%--<td>${ listout.barcode}</td>
                                <td>${ listout.barcodeFactory}</td>

                                <td>${0-listout.num}</td>
                                <td><date:date value ="${listout.addTime}" format="yyyy-MM-dd HH:mm:ss"/></td>--%>

                            <td>${ arg.sterilizationBatchNo}</td>


                            <td class="text-left">
                                <a class="addtitle" href="javascript:void(0);" style="color: #1E9FFF"
                                   tabTitle='{"num":"viewfirstgoods${arg.firstEngageId}","link":"./firstengage/baseinfo/getFirstSearchDetail.do?firstEngageId=${arg.firstEngageId}","title":"首营信息"}'>${ arg.registrationNumber}</a>
                            </td>
                        </tr>
                    </c:forEach>
                </c:if>

                <c:if test="${not empty afterSalesVo.directReturnOutstockList}">
                    <c:forEach items="${afterSalesVo.directReturnOutstockList}" var="dirArg"
                               varStatus="num_index">
                        <tr>
                            <td>${num_index.count }</td>
                            <td>${dirArg.sku}</td>
                            <td class="text-left">
                        <span class="brand-color1 addtitle"
                              tabTitle='{"num":"viewgoods${dirArg.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${dirArg.goodsId}", "title":"产品信息"}'>
                                ${dirArg.showName}
                        </span>
                            </td>
                            <td>${dirArg.brandName}</td>
                            <td>${dirArg.spec}/${dirArg.model}</td>
                            <td>${ dirArg.num}</td>
                            <td>${dirArg.unitName}</td>
                            <td>${ dirArg.vedengBatchNum}</td>
                            <td><date:date value="${dirArg.produceTime}" format="yyyy-MM-dd"/></td>
                            <td><date:date value="${dirArg.validTime}" format="yyyy-MM-dd"/></td>
                            <td><date:date value="${dirArg.outTime}" format="yyyy-MM-dd HH:mm:ss"/></td>
                            <td>${dirArg.industryBatchNumber}</td>
                            <td>${dirArg.sterilizationNumber}</td>
                            <td class="text-left">
                                <a class="addtitle" href="javascript:void(0);" style="color: #1E9FFF"
                                   tabTitle='{"num":"viewfirstgoods${dirArg.firstEngageId}","link":"./firstengage/baseinfo/getFirstSearchDetail.do?firstEngageId=${dirArg.firstEngageId}","title":"首营信息"}'>${ dirArg.registrationNumber}</a>
                            </td>
                        </tr>
                    </c:forEach>

                </c:if>
                <c:if test="${empty afterSalesVo.afterReturnOutstockList}">
                    <tr>
                        <td colspan="14">暂无记录</td>
                    </tr>
                </c:if>
                </tbody>
            </table>
        </div>



        <div class="parts">
            <div class="title-container">
                <div class="table-title nobor">发票记录</div>
            </div>
            <table class="table">
                <thead>
                <tr>
                    <th>发票号</th>
                    <th>票种</th>
                    <th>红蓝字</th>
                    <th>发票金额</th>
                    <th>操作人</th>
                    <th>操作时间</th>
                    <th>审核状态</th>
                    <th>审核人</th>
                    <th>审核时间</th>
                </tr>
                </thead>
                <tbody>
                <c:if test="${not empty afterSalesVo.afterOpenInvoiceList}">
                    <c:forEach items="${afterSalesVo.afterOpenInvoiceList}" var="aoi">
                        <tr>
                            <td>${aoi.invoiceNo}</td>
                            <td>
                                <c:if test="${aoi.invoiceType eq 429}">17%增值税专用发票</c:if>
                                <c:if test="${aoi.invoiceType eq 430}">17%增值税普通发票</c:if>
                                <c:if test="${aoi.invoiceType eq 682}">16%增值税专用发票</c:if>
                                <c:if test="${aoi.invoiceType eq 681}">16%增值税普通发票</c:if>

                                <c:if test="${aoi.invoiceType eq 972}">13%增值税专用发票</c:if>
                                <c:if test="${aoi.invoiceType eq 971}">13%增值税普通发票</c:if>
                                <c:if test="${aoi.invoiceType eq 683}">6%增值税普通发票</c:if>
                                <c:if test="${aoi.invoiceType eq 684}">6%增值税专用发票</c:if>
                                <c:if test="${aoi.invoiceType eq 685}">3%增值税普通发票</c:if>
                                <c:if test="${aoi.invoiceType eq 686}">3%增值税专用发票</c:if>
                                <c:if test="${aoi.invoiceType eq 687}">0%增值税普通发票</c:if>
                                <c:if test="${aoi.invoiceType eq 688}">0%增值税专用发票</c:if>
                            </td>

                            <td>
                                <c:choose>
                                    <c:when test="${aoi.colorType eq 1}">
                                        <c:choose>
                                            <c:when test="${aoi.isEnable eq 0}">
                                                <span style="color: red">红字作废</span>
                                            </c:when>
                                            <c:otherwise>
                                                <span style="color: red">红字有效</span>
                                            </c:otherwise>
                                        </c:choose>
                                    </c:when>
                                    <c:otherwise>
                                        <c:choose>
                                            <c:when test="${aoi.isEnable eq 0}">
                                                <span style="color: red">蓝字作废</span>
                                            </c:when>
                                            <c:otherwise>
                                                蓝字有效
                                            </c:otherwise>
                                        </c:choose>
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td>${aoi.amount}</td>
                            <td>${aoi.creatorName}</td>
                            <td><date:date value="${aoi.addTime}"/></td>
                            <td>
                                <c:if test="${aoi.validStatus eq 0 }">待审核</c:if>
                                <c:if test="${aoi.validStatus eq 1 }">审核通过</c:if>
                                <c:if test="${aoi.validStatus eq 2 }">审核不通过</c:if>
                            </td>
                            <td>${aoi.validUsername}</td>
                            <td><date:date value="${aoi.validTime} "/></td>
                        </tr>
                    </c:forEach>
                </c:if>
                <c:if test="${empty afterSalesVo.afterOpenInvoiceList}">
                    <!-- 查询无结果弹出 -->
                    <tr>
                        <td colspan='9'>暂无记录！</td>
                    </tr>
                </c:if>
                </tbody>
            </table>
        </div>
    </c:if>


    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                沟通记录
            </div>
            <c:if test="${isCreator eq 1}">
                <c:if test="${afterSalesVo.atferSalesStatus ne 3 && afterSalesVo.atferSalesStatus ne 2 }">
                    <div class="title-click nobor  pop-new-data" layerParams='{"width":"850px","height":"460px","title":"新增沟通记录",
                                "link":"<%= basePath %>/aftersales/order/addCommunicatePage.do?afterSalesId=${afterSalesVo.afterSalesId}&&traderId=${afterSalesVo.traderId }&&traderType=2"}'>
                        新增
                    </div>
                </c:if>
            </c:if>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <th class="wid10">沟通时间</th>
                <th class="">录音</th>
                <th class="">联系人</th>
                <th class="">联系方式</th>
                <th class="">沟通方式</th>
                <th class="wid30">沟通内容（AI分析整理）</th>
                <th class="">操作人</th>
                <th class="wid8">下次联系日期</th>
                <th class="wid15">下次沟通内容</th>
                <th class="">备注</th>
                <th class="wid10">创建时间</th>
                <th class="wid6">操作</th>
            </tr>
            </thead>
            <tbody>
            <c:if test="${not empty communicateList}">
                <c:forEach items="${communicateList}" var="communicateRecord" varStatus="">
                    <tr>
                        <td><date:date value="${communicateRecord.begintime} "/>~<date:date
                                value="${communicateRecord.endtime}" format="HH:mm:ss"/></td>
                        <td><c:if
                                test="${not empty communicateRecord.coidUri }">${communicateRecord.communicateRecordId }</c:if></td>
                        <td>${communicateRecord.contactName}</td>
                        <td>${communicateRecord.phone}</td>
                        <td>${communicateRecord.communicateModeName}</td>
                        <td>
                            <ul class="communicatecontent ml0">
                                <c:choose>
                                    <c:when test="${not empty communicateRecord.tag }">
                                        <c:forEach items="${communicateRecord.tag }" var="tag">
                                            <li class="bluetag" title="${tag.tagName}">${tag.tagName}</li>
                                        </c:forEach>
                                    </c:when>
                                    <c:otherwise>
                                        <li>${communicateRecord.contactContent }</li>
                                    </c:otherwise>
                                </c:choose>
                            </ul>
                        </td>
                        <td>${communicateRecord.user.username}</td>
                        <c:choose>
                            <c:when test="${communicateRecord.isDone == 0 }">
                                <td class="font-red">${communicateRecord.nextContactDate }</td>
                            </c:when>
                            <c:otherwise>
                                <td>${communicateRecord.nextContactDate }</td>
                            </c:otherwise>
                        </c:choose>
                        <td>${communicateRecord.nextContactContent}</td>
                        <td>${communicateRecord.comments}</td>
                        <td><date:date value="${communicateRecord.addTime} "/></td>

                        <td class="caozuo">
                            <c:if test="${isCreator eq 1}">
                                <c:if test="${afterSalesVo.atferSalesStatus ne 3 && afterSalesVo.atferSalesStatus ne 2}">
		                        	<span class="border-blue pop-new-data" layerParams='{"width":"60%","height":"63%","title":"编辑沟通记录",
		                        		"link":"<%= basePath %>/aftersales/order/editcommunicate.do?orderFlag=${afterSalesVo.atferSalesStatus }&flag=${afterSalesVo.status }&communicateRecordId=${communicateRecord.communicateRecordId}&afterSalesId=${afterSalesVo.afterSalesId}&&traderId=${afterSalesVo.traderId }&&traderType=2"}'>编辑</span>
                                </c:if>
                            </c:if>
                        </td>
                    </tr>
                </c:forEach>
            </c:if>
            <c:if test="${empty communicateList}">
                <!-- 查询无结果弹出 -->
                <tr>
                    <td colspan='12'>暂无记录！</td>
                </tr>
            </c:if>
            </tbody>
        </table>
    </div>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                售后过程
            </div>
            <c:if test="${isCreator eq 1}">
                <c:if test="${afterSalesVo.atferSalesStatus ne 3 &&  afterSalesVo.atferSalesStatus ne 2}">
                <div class="title-click nobor  pop-new-data"
                     layerParams='{"width":"700px","height":"250px","title":"新增售后过程","link":"<%= basePath %>/aftersales/order/addAfterSalesRecordPage.do?afterSalesId=${afterSalesVo.afterSalesId}"}'>
                    新增
                </div>
            </c:if>
            </c:if>
        </div>
        <table class="table">
            <thead>
            <tr>
                <th>沟通时间</th>
                <th>操作人</th>
                <th>售后内容</th>
                <th>操作</th>
            </tr>
            </thead>
            <tbody>
            <c:if test="${not empty afterSalesVo.afterSalesRecordVoList}">
                <c:forEach items="${afterSalesVo.afterSalesRecordVoList}" var="asi">
                    <tr>
                        <td><date:date value="${asi.addTime} "/></td>
                        <td>${asi.optName}</td>
                        <td>${asi.content}</td>
                        <td class="caozuo">
                            <c:if test="${afterSalesVo.atferSalesStatus ne 3 && afterSalesVo.atferSalesStatus ne 2}">
                            <c:if test="${isCreator eq 1}">
		                        	<span class="border-blue pop-new-data" layerParams='{"width":"500px","height":"250px","title":"编辑售后过程",
		                        			"link":"<%= basePath %>/aftersales/order/editAfterSalesRecordPage.do?afterSalesRecordId=${asi.afterSalesRecordId}"}'>编辑</span>
                            </c:if>
                            </c:if>
                        </td>
                    </tr>
                </c:forEach>
            </c:if>
            <c:if test="${empty afterSalesVo.afterSalesRecordVoList}">
                <!-- 查询无结果弹出 -->
                <tr>
                    <td colspan='3'>暂无记录！</td>
                </tr>
            </c:if>
            </tbody>
        </table>
    </div>


</div>
<script type="text/javascript">
    //注意：选项卡 依赖 element 模块，否则无法进行功能性操作
    layui.use('element', function () {
        var element = layui.element;

        //…
    });
</script>
</body>

</html>
