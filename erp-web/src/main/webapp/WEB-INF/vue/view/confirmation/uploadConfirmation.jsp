<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<!-- 引入样式 -->
<%--<link rel="stylesheet" href="${pageContext.request.contextPath}/static/vue/element-ui/css/index.css">--%>
<!-- 引入脚本 -->
<%--<script src="${pageContext.request.contextPath}/static/vue/element-ui/js/vue.js"></script>--%>
<%--<script src="${pageContext.request.contextPath}/static/vue/element-ui/js/ele-index.js"></script>--%>

<div id="app">
    <%--图片上传--%>
    <el-row :gutter="20" class="baseModel">
        <el-col :span="4">
            <div class="grid-content bg-purple">
                确认单回传
            </div>
        </el-col>
        <el-col :span="20">
            <div class="grid-content bg-purple-light" style="height:160px;overflow: auto">
                <div class="box">
                    <el-upload
                            action="/orderstream/saleorder/uploadImg.do"
                            :file-list="fileList"
                            limit="10"
                            multiple
                            :on-exceed="outOfLimit"
                            :before-upload="beforeUpload"
                            :on-change="onChange"
                            :on-remove="onChange"
                            :on-preview="previewImg"
                            accept=".jpg,.jpeg,.png,.pdf,.PDF">
                        <el-button size="small" type="primary">点击上传</el-button>
                    </el-upload>
                </div>
                <div slot="tip" class="box" style="color: #909399">
                    友情提示：<br/>
                    1、上传文件格式可以是jpg、png、pdf等格式;<br/>
                    2、上传文件不要超过10MB;<br/>
                    3、最多上传10个文件;
                </div>
            </div>
        </el-col>
    </el-row>
    <el-divider></el-divider>
    <%--批次列表--%>
    <el-row :gutter="20" class="baseModel">
        <el-col :span="4">
            <div class="grid-content bg-purple">
                请选择确认收货商品
            </div>
        </el-col>
        <el-col :span="20">
            <div class="grid-content bg-purple-light" style="margin-right: 40px">
                <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
                <el-checkbox-group v-model="checkedBatch" @change="handleCheck">
                <div v-for="b in batchInfoList" style="padding-top: 14px">
                    <el-checkbox :label="b.id">批次号:{{b.batchTime}}</el-checkbox>
                    <el-table border stripe :data="b.batchExpressVos">
                        <el-table-column width="60" type="index" label="序号"></el-table-column>
                        <el-table-column width="80" prop="sku" label="订货号"></el-table-column>
                        <el-table-column width="300" prop="goodsName" label="产品名称"></el-table-column>
                        <el-table-column width="100" prop="brand" label="品牌"></el-table-column>
                        <el-table-column width="100" prop="model" label="型号"></el-table-column>
                        <el-table-column width="70" prop="num" label="实际签收数量"></el-table-column>
                        <el-table-column  prop="logisticsOrderNo" label="物流单号"></el-table-column>
                    </el-table>
                </div>
                </el-checkbox-group>
            </div>
        </el-col>
    </el-row>

    <%--按钮--%>
    <span style="display: block;text-align: center;" class="baseModel">
            <el-button type="primary" @click="submit" :disabled="disSubmit">提 交</el-button>
            <el-button @click="reInit">取 消</el-button>
        </span>
</div>

<script type="text/javascript">
    let batchInfoList = ${outboundBatchesRecodes};
    let saleOrderId = ${saleOrderId};
    let vm = new Vue({
        el: '#app',
        data() {
            return {
                isIndeterminate: false,
                checkAll: false,
                checkedBatch: [],
                batchInfoList: [{
                    id: null,
                    batchNo: '',
                    batchTime: '',
                    batchExpressVos: []
                }],
                fileList: [],
                rules: {
                },
                popupIndex: 1,
                disSubmit: false,
            }
        },
        created() {
            // parent.$('.layui-layer-setwin').hide();
            this.batchInfoList = batchInfoList
        },
        methods: {
            onChange(file,fileList) {
                this.fileList = fileList;
            },
            beforeUpload(file) {
                //类型限制
                let fileType = false;
                let arr = [];
                arr.push('application/pdf')
                arr.push('image/jpeg')
                arr.push('image/png')
                if (arr.includes(file.type)){
                    fileType = true;
                }
                //大小限制
                const isLt10M = file.size/1024/1024 < 10
                if (!isLt10M) {
                    this.$message.error('上传文件不要超过10MB');
                }
                if (!fileType){
                    this.$message.error('无法查看其他文件格式的文档');
                }
                return isLt10M && fileType;
            },
            outOfLimit() {
                this.$message({
                    message: '最多上传10个文件',
                    type: 'error'
                });
            },
            /*选择批次*/
            handleCheck(v) {
                // console.log("v"+v);
                let checkedCount = v.length;
                this.checkAll = checkedCount === this.batchInfoList.length;
                this.isIndeterminate = checkedCount > 0 && checkedCount < this.batchInfoList.length;
            },
            /*全选*/
            handleCheckAllChange(val) {
                if (val){
                    this.batchInfoList.forEach(item => {
                        this.checkedBatch.push(item.id)
                    })
                }
                if (!val){
                    this.checkedBatch = []
                }
                this.isIndeterminate = false;
            },
            /*提交*/
            submit() {
                if (this.fileList.length === 0){
                    this.$message.error("请上传附件")
                    return;
                }
                if (this.checkedBatch.length === 0){
                    this.$message.error("请选择收货商品")
                    return;
                }
                this.disSubmit = true;
                let data = JSON.parse(JSON.stringify({
                    saleOrderId: saleOrderId,
                    fileInfos: [],
                    batchesRecodes: []
                }));
                var checkUpload = false;
                this.fileList.forEach((file=>{
                    if(file.status != "success"){
                        checkUpload = true;
                    }
                }));
                if(checkUpload){
                    this.$message.error("文件上传中，请等待文件上传完成！");
                    this.disSubmit = false;
                    return;
                }
                this.fileList.forEach(file => {
                    data.fileInfos.push(file.response)
                })
                this.batchInfoList.forEach(batch => {
                    this.checkedBatch.forEach(checkedId => {
                        if (batch.id === checkedId){
                            data.batchesRecodes.push(batch)
                        }
                    })
                });
                setTimeout(() => {
                    this.disSubmit = false;
                },1000);
                //发送请求
                axios({
                    url: '/orderstream/saleorder/uploadConfirmationForm.do',
                    method: 'post',
                    data: data
                }).then(res => {
                    if (res.data.code === 0){
                        this.popupIndex = -1;
                        this.reInit();
                        parent.window.location.reload();
                    }else {
                        this.$message.error("提交失败")
                    }
                });
            },
            /*取消*/
            reInit() {
                this.popupIndex = -1;
                layer.closeAll();
                parent.layer.closeAll();
            },
            /*预览*/
            previewImg(file) {
                window.open(file.response.ossUrl)
            }
        }
    });
</script>

<style>
    .baseModel {
        padding-top: 20px;
        padding-left: 10px;
        font-size: 13px;
    }

    .box {
        width: 200px;
        height: 180px;
        float: left;
        padding-right: 50px;
    }
    .el-upload-list__item .el-icon-close-tip {
        display: none !important;
    }

</style>