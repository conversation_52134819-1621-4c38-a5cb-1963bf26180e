<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wms.dao.WmsReceiveOutMapper">
    <resultMap id="BaseResultMap" type="com.wms.model.po.WMSScrappedOutOrder" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="type" property="type" jdbcType="INTEGER" />
        <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
        <result column="verify_status" property="verifyStatus" jdbcType="INTEGER" />
        <result column="out_status" property="outStatus" jdbcType="INTEGER" />
        <result column="return_status" property="returnStatus" jdbcType="INTEGER" />
        <result column="borrow_reason" property="borrowReason" jdbcType="VARCHAR" />
        <result column="borrow_trader_name" property="borrowTraderName" jdbcType="BIGINT" />
        <result column="logistic_commnet" property="logisticCommnet" jdbcType="VARCHAR" />
        <result column="logistic_commnet" property="logisticCommnet" jdbcType="VARCHAR" />
        <result column="belong_department" property="belongDepartment" jdbcType="VARCHAR" />
        <result column="scrap_type" property="scrapType" jdbcType="INTEGER" />
        <result column="scrap_level" property="scrapLevel" jdbcType="INTEGER" />
        <result column="scrap_deal_type" property="scrapDealType" jdbcType="INTEGER" />
        <result column="applyer" property="applyer" jdbcType="VARCHAR" />
        <result column="applyer_department" property="applyerDepartment" jdbcType="VARCHAR" />
        <result column="recipient_applyer" property="recipientApplyer" jdbcType="VARCHAR" />
        <result column="recipient_department" property="recipientDepartment" jdbcType="VARCHAR" />
        <result column="recipient_type" property="recipientType" jdbcType="INTEGER" />
        <result column="apple_out_date" property="appleOutDate" jdbcType="VARCHAR" />
        <result column="use_nature" property="useNature" jdbcType="VARCHAR" />
        <result column="use_name" property="useName" jdbcType="VARCHAR" />
        <result column="receiver" property="receiver" jdbcType="VARCHAR" />
        <result column="receiver_address" property="receiverAddress" jdbcType="VARCHAR" />
        <result column="receiver_telphone" property="receiverTelphone" jdbcType="VARCHAR" />
        <result column="receiver_phone" property="receiverPhone" jdbcType="VARCHAR" />
        <result column="detail_address" property="detailAddress" jdbcType="VARCHAR" />
        <result column="apply_reason" property="applyReason" jdbcType="VARCHAR" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="real_output_time" property="realOutputTime" jdbcType="VARCHAR" />
        <result column="customer_receive_time" property="customerReceiveTime" jdbcType="VARCHAR" />
        <result column="approval_time" property="approvalTime" jdbcType="VARCHAR" />
        <result column="add_time" property="addTime" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="VARCHAR" />
        <result column="creator" property="creator" jdbcType="VARCHAR" />
        <result column="updator" property="updator" jdbcType="VARCHAR" />
        <result column="is_delete" property="isDelete" jdbcType="INTEGER" />
        <result column="output_num " property="outputNum" jdbcType="INTEGER" />
        <result column="sku_no" property="skuNo" jdbcType="VARCHAR" />
        <result column="model" property="model" jdbcType="VARCHAR" />
        <result column="unit_name" property="unitName" jdbcType="VARCHAR" />
        <result column="brand_name" property="brandName" jdbcType="VARCHAR" />
        <result column="applyer_id " property="applyerId" jdbcType="INTEGER" />
        <result column="applyer_department_id " property="applyerDepartmentId" jdbcType="INTEGER" />
    </resultMap>
    <sql id="Base_Column_List" >
        id, type, order_no, verify_status,out_status,return_status,borrow_reason, borrow_trader_id,borrow_trader_name, logistic_commnet,
        belong_department, scrap_type, scrap_level, scrap_deal_type, applyer, applyer_department,
        recipient_applyer, recipient_department, recipient_type, apple_out_date, use_nature,
        use_name, receiver, receiver_address, receiver_telphone,receiver_phone, detail_address, apply_reason,
        remark, real_output_time, customer_receive_time,approval_time, add_time, update_time, creator,
        updator, is_delete,applyer_id,applyer_department_id
    </sql>


    <select id="selectById" resultType="com.wms.model.po.WMSScrappedOutOrder">
        SELECT
            T.*,
            DEA.TITLE scrapDealTypeStr,
            LE.TITLE scrapLevelStr,
            TY.TITLE scrapTypeStr
        FROM T_WMS_OUTPUT_ORDER T
                 LEFT JOIN T_SYS_OPTION_DEFINITION DEA ON DEA.SYS_OPTION_DEFINITION_ID=T.scrap_deal_type
                 LEFT JOIN T_SYS_OPTION_DEFINITION LE ON LE.SYS_OPTION_DEFINITION_ID=T.scrap_level
                 LEFT JOIN T_SYS_OPTION_DEFINITION TY ON TY.SYS_OPTION_DEFINITION_ID = T.scrap_type
        where T.ID = #{scrappedOutId,jdbcType=VARCHAR}
    </select>


    <select id="queryOutputGoodsByReceiveOutId" resultType="com.wms.model.dto.WmsOutputOrderGoodsDto" parameterType="java.lang.Long" >
        SELECT
            goods.*,
            sku.SKU_NAME,
            sku.SHOW_NAME,
            sku.MODEL,
            BR.BRAND_NAME
        FROM T_WMS_OUTPUT_ORDER_GOODS goods
                 LEFT JOIN V_CORE_SKU sku ON goods.sku_no = sku.SKU_NO
                 LEFT JOIN V_CORE_SPU spu ON spu.SPU_ID = sku.SPU_ID
                 LEFT JOIN T_BRAND BR ON BR.BRAND_ID = spu.BRAND_ID
        where wms_output_order_id = #{scrappedOutId,jdbcType=BIGINT}
    </select>

    <update id="updateByPrimaryKeySelective" parameterType="com.wms.model.po.WmsOutputOrder" >
        update T_WMS_OUTPUT_ORDER
        <set >
            <if test="type != null" >
                type = #{type,jdbcType=INTEGER},
            </if>
            <if test="returnStatus != null" >
                return_status = #{returnStatus,jdbcType=INTEGER},
            </if>
            <if test="outStatus != null" >
                out_status = #{outStatus,jdbcType=INTEGER},
            </if>
            <if test="orderNo != null" >
                order_no = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="verifyStatus != null" >
                verify_status = #{verifyStatus,jdbcType=INTEGER},
            </if>
            <if test="borrowReason != null" >
                borrow_reason = #{borrowReason,jdbcType=VARCHAR},
            </if>
            <if test="borrowTraderId != null" >
                borrow_trader_id = #{borrowTraderId,jdbcType=BIGINT},
            </if>
            <if test="logisticCommnet != null" >
                logistic_commnet = #{logisticCommnet,jdbcType=VARCHAR},
            </if>
            <if test="belongDepartment != null" >
                belong_department = #{belongDepartment,jdbcType=VARCHAR},
            </if>
            <if test="scrapType != null" >
                scrap_type = #{scrapType,jdbcType=INTEGER},
            </if>
            <if test="scrapLevel != null" >
                scrap_level = #{scrapLevel,jdbcType=INTEGER},
            </if>
            <if test="scrapDealType != null" >
                scrap_deal_type = #{scrapDealType,jdbcType=INTEGER},
            </if>
            <if test="applyer != null" >
                applyer = #{applyer,jdbcType=VARCHAR},
            </if>
            <if test="applyerDepartment != null" >
                applyer_department = #{applyerDepartment,jdbcType=VARCHAR},
            </if>
            <if test="recipientApplyer != null" >
                recipient_applyer = #{recipientApplyer,jdbcType=VARCHAR},
            </if>
            <if test="recipientDepartment != null" >
                recipient_department = #{recipientDepartment,jdbcType=VARCHAR},
            </if>
            <if test="recipientType != null" >
                recipient_type = #{recipientType,jdbcType=INTEGER},
            </if>
            <if test="appleOutDate != null" >
                apple_out_date = #{appleOutDate,jdbcType=VARCHAR},
            </if>
            <if test="useNature != null" >
                use_nature = #{useNature,jdbcType=VARCHAR},
            </if>
            <if test="useName != null" >
                use_name = #{useName,jdbcType=VARCHAR},
            </if>
            <if test="receiver != null" >
                receiver = #{receiver,jdbcType=VARCHAR},
            </if>
            <if test="receiverAddress != null" >
                receiver_address = #{receiverAddress,jdbcType=VARCHAR},
            </if>
            <if test="receiverTelphone != null" >
                receiver_telphone = #{receiverTelphone,jdbcType=VARCHAR},
            </if>
            <if test="detailAddress != null" >
                detail_address = #{detailAddress,jdbcType=VARCHAR},
            </if>
            <if test="applyReason != null" >
                apply_reason = #{applyReason,jdbcType=VARCHAR},
            </if>
            <if test="remark != null" >
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="realOutputTime != null" >
                real_output_time = #{realOutputTime,jdbcType=VARCHAR},
            </if>
            <if test="customerReceiveTime != null" >
                customer_receive_time = #{customerReceiveTime,jdbcType=VARCHAR},
            </if>
            <if test="approvalTime != null" >
                approval_time = #{approvalTime,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null" >
                add_time = #{addTime,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=VARCHAR},
            </if>
            <if test="creator != null" >
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="updator != null" >
                updator = #{updator,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null" >
                is_delete = #{isDelete,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>