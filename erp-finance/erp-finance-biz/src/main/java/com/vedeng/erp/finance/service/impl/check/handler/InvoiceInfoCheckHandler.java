package com.vedeng.erp.finance.service.impl.check.handler;

import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.finance.service.AbstractCheckHandler;
import com.vedeng.erp.finance.dto.InvoiceCheckRequestDto;
import com.vedeng.erp.finance.dto.InvoiceCheckResultDto;
import com.vedeng.erp.finance.enums.CheckHandlerEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 开票信息校验
 */
@Service
@Slf4j
public class InvoiceInfoCheckHandler extends AbstractCheckHandler {

    @Override
    public void handleSalesCheck(InvoiceCheckRequestDto invoiceCheckRequestDto, InvoiceCheckResultDto invoiceCheckResultDto) {
        log.info("开票信息校验-销售,invoiceCheckRequestDto:{}", invoiceCheckRequestDto);
        invoiceInfoCheck(invoiceCheckRequestDto, invoiceCheckResultDto);
    }

    @Override
    public void handleAfterCheck(InvoiceCheckRequestDto invoiceCheckRequestDto, InvoiceCheckResultDto invoiceCheckResultDto) {
        log.info("开票信息校验-售后,invoiceCheckRequestDto:{}", invoiceCheckRequestDto);
        invoiceInfoCheck(invoiceCheckRequestDto, invoiceCheckResultDto);
    }

    private void invoiceInfoCheck(InvoiceCheckRequestDto invoiceCheckRequestDto, InvoiceCheckResultDto invoiceCheckResultDto) {
        Integer invoiceInfoType = invoiceCheckRequestDto.getInvoiceInfoType();
        if (ErpConstant.ONE.equals(invoiceInfoType)) {
            CheckHandlerEnum checkHandlerEnum = getCheckHandlerEnum();
            buildResult(invoiceCheckResultDto, checkHandlerEnum.getPromptText());
        }
    }

}
