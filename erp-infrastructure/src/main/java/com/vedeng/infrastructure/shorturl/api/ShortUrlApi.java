package com.vedeng.infrastructure.shorturl.api;

import com.vedeng.base.api.dto.kuaidi.KuaiDiReqDTO;
import com.vedeng.base.api.dto.kuaidi.LogisticsDTO;
import com.vedeng.base.api.dto.reqParam.ShortUrlDto;
import com.vedeng.base.api.service.KuaiDiApiService;
import com.vedeng.base.api.service.ShortUrlApiService;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.common.feign.constants.ServerConstants;
import feign.Headers;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2023/9/25
 */
@FeignApi(serverName = ServerConstants.BASE_SERVER)
public interface ShortUrlApi extends ShortUrlApiService {

    @Override
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /shorturl/generate")
    RestfulResult<String> getShortUrl(@RequestBody ShortUrlDto var1);

    @Override
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("GET /shorturl/whitelist")
    RestfulResult<List<String>> getWhiteList();

    @Override
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /shorturl/generatePublic")
    RestfulResult<String> getShortUrlPublic(@RequestBody ShortUrlDto shortUrlDto);

    @Override
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /shorturl/list")
    RestfulResult<List<ShortUrlDto>> listAll(@RequestBody ShortUrlDto shortUrlDto);


}
