<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.mapper.DepartmentsHospitalMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.goods.domain.entity.DepartmentsHospital">
    <!--@Table T_DEPARTMENTS_HOSPITAL-->
    <id column="DEPARTMENT_ID" jdbcType="INTEGER" property="departmentId" />
    <result column="DEPARTMENT_NAME" jdbcType="VARCHAR" property="departmentName" />
    <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
    <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="SORT" jdbcType="INTEGER" property="sort" />
  </resultMap>

  <sql id="Base_Column_List">
    DEPARTMENT_ID, DEPARTMENT_NAME, DESCRIPTION, IS_DELETE, UPDATER, MOD_TIME, CREATOR,
    ADD_TIME, SORT
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_DEPARTMENTS_HOSPITAL
    where DEPARTMENT_ID = #{departmentId,jdbcType=INTEGER}
  </select>

  <update id="updateBatchSelective" parameterType="java.util.List">
    update T_DEPARTMENTS_HOSPITAL
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="SORT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.sort != null">
            when DEPARTMENT_ID = #{item.departmentId,jdbcType=INTEGER} then #{item.sort,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
    </trim>
    where DEPARTMENT_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.departmentId,jdbcType=INTEGER}
    </foreach>
  </update>

  <select id="findAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_DEPARTMENTS_HOSPITAL
  </select>

  <select id="findAllByDepartmentNameAndIsDelete" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_DEPARTMENTS_HOSPITAL
    where DEPARTMENT_NAME=#{departmentName,jdbcType=VARCHAR} and IS_DELETE=#{isDelete,jdbcType=BOOLEAN}
  </select>

  <select id="findAllNotDelete" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_DEPARTMENTS_HOSPITAL where IS_DELETE = 0
  </select>
</mapper>