package com.vedeng.goods.manager.extension;

import com.vedeng.goods.manager.constants.GoodsValidConstants;
import com.vedeng.goods.manager.extension.handler.GoodsTodoHandler;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Getter
public enum GoodsExtValidModuleEnum {

    HAS_PRICING(GoodsTodoHandler.PRICING_SERVICE_ID, "商品定价", GoodsValidConstants.ENABLE),

    HAS_DELIVERY_TIME(GoodsTodoHandler.GOODS_DELIVERY_DATE_SERVICE_ID, "预计可发货时间", GoodsValidConstants.DISABLE),

    EXTERNAL_AFTER_SALES(GoodsTodoHandler.SUPPLIER_AFTER_SALES_SERVICE_ID, "供应商售后政策", GoodsValidConstants.ENABLE),

    INTERNAL_AFTER_SALES(GoodsTodoHandler.VD_AFTER_SALES_SERVICE_ID, "贝登售后标准", GoodsValidConstants.ENABLE),

    HAS_REPORT(GoodsTodoHandler.REPORT_SERVICE_ID, "报备信息", GoodsValidConstants.ENABLE),

    HAS_SIGN_CONTRACT(GoodsTodoHandler.SIGN_CONTRACT_SERVICE_ID, "签约模式", GoodsValidConstants.DISABLE),

    HAS_OPERATION(GoodsTodoHandler.OPERATION_SERVICE_ID, "运营信息", GoodsValidConstants.ENABLE);

    private Integer id;
    private String name;
    private Integer status;

    public static final Integer ENABLE = 1;
    public static final Integer DISABLE = 0;

    GoodsExtValidModuleEnum(Integer id, String name, Integer status) {
        this.id = id;
        this.name = name;
        this.status = status;
    }

    public static GoodsExtValidModuleEnum getByServiceId(Integer serviceId) {
        return Arrays.stream(values()).filter(item -> item.getId().equals(serviceId)).findFirst().orElse(null);
    }
}
