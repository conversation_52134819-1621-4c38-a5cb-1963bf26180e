package com.vedeng.erp.aftersale.domain.entity;

import lombok.*;

import java.util.List;

/**
 * @Author: Pat<PERSON><PERSON><PERSON>
 * @CreateTime: 2023-01-11
 * @Description: TODO
 * @Version: 1.0
 */
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExpenseReturnEarlyWarnEntity {

    /**
     * 销售单表id
     */
    private Integer saleorderId;

    /**
     * 销售单明细表id
     */
    private Integer saleorderGoodsId;

    /**
     * 售后单ID
     */
    private Integer afterSalesOrderId;

    /**
     * 采购费用单id
     */
    private List<Integer> buyorderExpenseIdList;

    /**
     * skuId
     */
    private Integer skuId;

    /**
     * skuNo
     */
    private String skuNo;

    /**
     * 退货数量
     */
    private Integer num;
}
