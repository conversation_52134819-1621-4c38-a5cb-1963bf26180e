<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="新增售后产品与售后安调公司" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='<%=basePath%>/static/js/orderstream/aftersales/add_afterSales_enginneer.js?rnd=${resourceVersionKey}'></script>

<div class="form-list  form-tips5">
    <form method="post" action="<%= basePath %>/order/afterSalesCommon/saveAddAfterSalesEngineer.do">
        <ul>
            <li>
                <div class="form-tips">
                    <span>*</span>
                    <lable>公司名称</lable>
                </div>
                <div class="f_left f_left_wid90">
                    <div class="form-blanks">
                        <span class="none" id="selname"></span>
                        <input type="text" placeholder="请输入售后安调公司名称" class="input-small" name="searchName" id="searchName" >
                        <label class="bt-bg-style bg-light-blue bt-small" onclick="search();" id="search1" style='margin-top:-3px;'>搜索</label>
                        <label class="bt-bg-style bg-light-blue bt-small none" onclick="research();" id="search2" style='margin-top:-3px;'>重新搜索</label>
                        <span style="display:none;">
							<!-- 弹框 -->
							<div class="title-click nobor  pop-new-data" id="popEngineer"></div>
						</span>
                        <input type="hidden" name="name" id="name">
                        <input type="hidden" name="engineerId" id="engineerId">
                        <input type="hidden" name="type"  value="550">
                    </div>
                    <div id="searchNameError"></div>
                </div>
            </li>

            <li>
                <div class="form-tips">
                    <span>*</span>
                    <lable>服务时间</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <div class="form-blanks">
                            <input class="Wdate input-small input-smaller96 mr5" type="text" placeholder="请选择日期"
                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'end\')}'})" autocomplete="off"
                                   name="start" id="serviceTime" value="<date:date value ='${start}' format="yyyy-MM-dd"/>"/>
                            <input type="hidden" name="end" id="end" onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'serviceTime\')}'})" autocomplete="off"
                                   value="<date:date value ='${end}' format="yyyy-MM-dd"/>">
                        </div>
                        <div id="serviceTimeError"></div>
                    </div>
                </div>
            </li>
            <li>
                <div class="form-tips">
                    <span>*</span>
                    <lable>酬金</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <input class="input-small" type="text" name="engineerAmount" id="engineerAmount" value="">
                        <input type="hidden" name="afterSalesId" value="${afterSalesInstallstionVo.afterSalesId}">
                        <input type="hidden" name="areaId" id="areaId" value="${afterSalesInstallstionVo.areaId}">
                        <input type="hidden" name="formToken" value="${formToken}"/>
                    </div>
                    <div id="engineerAmountError"></div>
                    <div class="pop-friend-tips mt5">
                        友情提示：
                        <br/> 1、酬金指我方支付给售后安调公司的费用；
                        <br/> 2、售后服务费指我方向客户收取的费用；
                    </div>
                </div>
            </li>
        </ul>
        <div class="add-tijiao tcenter">
            <button type="submit" id="submit">提交</button>
        </div>
    </form>
</div>
<%@ include file="../../common/footer.jsp"%>