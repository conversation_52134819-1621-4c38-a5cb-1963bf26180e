package com.vedeng.erp.saleorder.service;

import com.vedeng.authorization.model.User;
import com.vedeng.common.page.Page;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.vo.SaleorderVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/9 14:03
 * @describe
 */
public interface PendingOrderService {

    /**
     * 获取待签收信息
     * @param saleorder 订单信息
     * @param page 分页
     * @return 获取待签收信息
     */
    Map<String, Object> getPendingOrderList(Saleorder saleorder, Page page);

    /**
     * 获取待签收信息
     * @param saleorder 订单信息
     * @param validTimeStart 生效时间
     * @param validTimeEnd 生效时间
     * @return 获取待签收信息
     */
    List<SaleorderVo> getPendingOrderListBySaleorder(Saleorder saleorder, Long validTimeStart, Long validTimeEnd);

    /**
     * 发送短信
     * @param saleorderVos 需要发送短信的待签收信息
     * @param user  用户信息
     * @param sendType 发送类型
     */
    void sendSms(List<SaleorderVo> saleorderVos, User user, Integer sendType);
}
