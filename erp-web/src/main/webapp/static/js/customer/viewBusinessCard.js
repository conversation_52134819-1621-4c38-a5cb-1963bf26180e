$(function() {
    $('.J-upload').each(function (i) {
        var _this = this;
        new Upload({
            limit: 3,
            url: GLOBAL.IMGUPLOADURL,
            wrapper: $(this),
            uploadName: 'upload' + i,
            list: JSON.parse($(this).siblings('.J-upload-data').val() || '[]'),
            onchange: function () {
                $(_this).find('.J-upload-item').each(function (idx, elem) {
                    var data = $(this).data('item');
                    $(this).find('.J-item-name').remove();
                    var targetElement=$(_this).attr("type")+"["+idx+"]";
                    $(this).append('<input type="hidden" class="J-item-name" name="'+targetElement+'.relativePath"  value="' + data.filePath + '" >');
                    $(this).append('<input type="hidden" class="J-item-name" name="'+targetElement+'.displayName"  value="' + data.fileName + '" >');
                    $(this).append('<input type="hidden" class="J-item-name" name="'+targetElement+'.traderCertificateId"  value="' + data.traderCertificateId + '" >');

                })
                if (i == 0) {
                    $(_this).find('[name^=upload]').valid();
                }


            },
            saveUrl: function (result) {
                result.relatedId = $("#traderContactId").val();
                $.ajax({
                    url:page_url+'/trader/customer/saveBusinessCards.do',
                    data:result,
                    type:"POST",
                    dataType : "json",
                    async: false,
                    success:function(data)
                    {
                        if(data.code ==0){
                            $(window.parent.document).find("iframe")[0].contentWindow.location.reload(true);
                        }else{
                            layer.alert(data.message);
                        }
                    },
                    error:function(data){
                        if(data.status ==1001){
                            layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                        }
                    }
                });
            },
            deleteUrl: function (result) {
                layer.confirm("删除后，名片无法再次获取，确认删除？", {
                    btn: ['确定','取消'], //按钮
                    cancel:function(index, layero){
                        $(window.parent.document).find("iframe")[0].contentWindow.location.reload(true);
                    }
                }, function(){
                    $.ajax({
                        url:page_url+'/trader/customer/updateBusinessCards.do',
                        data:result,
                        type:"POST",
                        dataType : "json",
                        async: false,
                        success:function(data)
                        {
                            if(data.code ==0){
                                $(window.parent.document).find("iframe")[0].contentWindow.location.reload(true);
                            }else{
                                layer.alert(data.message);
                            }
                        },
                        error:function(data){
                            if(data.status ==1001){
                                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                            }
                        }
                    });
                }, function(){
                    $(window.parent.document).find("iframe")[0].contentWindow.location.reload(true);
                });
            },
            filters: {
                mime_types: [
                    { title: "Image files", extensions: "jep,png,jpeg" }
                ],
                max_file_size: '10MB'
            },
            onError: function (error) {
                var errorMsg = {
                    TYPE: '上传图片格式为：jep、png、jpeg格式',
                    SIZE: '图片大小不超过10M'
                }
                if (error) {
                    var $error = $(_this).siblings('.J-upload-error');
                    $error.show().find('label').html(errorMsg[error]).show();
                    timeout && clearTimeout(timeout);
                    timeout = setTimeout(function () {
                        $error.hide();
                    }, 3000)
                }
            }
        });
    })

    // $(".J-upload").on('click', '.J-upload-del', function (e) {
    //     var res = $(this).parents('.J-upload-item:first').data('item');
    //     layer.confirm("删除后，名片无法再次获取，确认删除？", {
    //         btn: ['确定','取消'] //按钮
    //     }, function(){
    //         $.ajax({
    //             url:page_url+'/trader/customer/updateBusinessCards.do',
    //             data:res,
    //             type:"POST",
    //             dataType : "json",
    //             async: false,
    //             success:function(data)
    //             {
    //                 if(data.code ==0){
    //                     $(window.parent.document).find("iframe")[0].contentWindow.location.reload(true);
    //                 }else{
    //                     layer.alert(data.message);
    //                 }
    //             },
    //             error:function(data){
    //                 if(data.status ==1001){
    //                     layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
    //                 }
    //             }
    //         });
    //     }, function(){
    //         $(window.parent.document).find("iframe")[0].contentWindow.location.reload(true);
    //     });
    // })

    window.localStorage.removeItem('addsuccess');

    //增加关闭提示
    GLOBAL.addtip();
})