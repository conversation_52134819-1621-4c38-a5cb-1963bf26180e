package com.vedeng.finance.service;

import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpSession;

import com.vedeng.authorization.model.User;
import com.vedeng.billsync.task.model.entity.BankBillExtDo;
import com.vedeng.erp.finance.dto.BankBillIgnoreRecordVo;
import com.vedeng.erp.kingdee.dto.result.KingDeeCustomerResultDto;
import com.vedeng.finance.dto.BankNoDto;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.ss.formula.functions.T;

import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.finance.model.BankBill;
import com.vedeng.finance.model.PayApply;

public interface BankBillService {
    /**
     * 
     * <b>Description:</b><br>银行流水列表 
     * @param bankBill
     * @param page
     * @return
     * @Note
     * <b>Author:</b> Michael
     * <br><b>Date:</b> 2017年9月12日 上午11:42:54
     */
    Map<String, Object> getBankBillListPage(BankBill bankBill, Page page) throws Exception;
    /**
     * 
     * <b>Description:</b><br> 银行流水匹配列表 
     * @param bankBill
     * @param page
     * @return
     * @Note
     * <b>Author:</b> Michael
     * <br><b>Date:</b> 2017年9月15日 下午5:14:16
     */
    Map<String, Object> getBankBillMatchListPage(BankBill bankBill, Page page,boolean calCount);
    /**
     * 
     * <b>Description:</b><br> 修改银行流水 （暂时只用来忽略银行流水）
     * @param bankBill
     * @return
     * @Note
     * <b>Author:</b> Michael
     * <br><b>Date:</b> 2017年9月26日 下午3:13:41
     */
    ResultInfo editBankBill(BankBill bankBill);
    /**
     * 
     * <b>Description:</b><br>根据ID获取银行流水信息 
     * @param bankBillId
     * @return
     * @Note
     * <b>Author:</b> Michael
     * <br><b>Date:</b> 2017年9月28日 上午9:24:09
     */
    BankBill getBankBillById(Integer bankBillId);
    /**
     * 
     * <b>Description:</b><br> 新增银企直连银行付款
     * @param payApplyInfo
     * @return
     * @Note
     * <b>Author:</b> Michael
     * <br><b>Date:</b> 2018年1月22日 下午1:18:58
     */
    ResultInfo  addBankPayApply(PayApply payApplyInfo);
	
    ResultInfo sendBankBillList(BankBill bankBill, Page page, HttpSession session)  throws Exception;
    /**
     * 
     * <b>Description:</b><br> 付款流水匹配付款申请
     * @param bankBill
     * @param page
     * @return
     * @Note
     * <b>Author:</b> Michael
     * <br><b>Date:</b> 2018年8月6日 上午10:36:34
     */
    Map<String, Object> getBankBillPayMatchListPage(BankBill bankBill, Page page);
    
    /**
     * 发送付款记录至金蝶
     * <b>Description:</b><br> 
     * @param capitalBill
     * @param page
     * @param session
     * @return
     * @throws UnsupportedEncodingException 
     * @Note
     * <b>Author:</b> Bill
     * <br><b>Date:</b> 2018年8月9日 上午10:52:49
     */
	ResultInfo sendPayBillToKindlee(BankBill bankBill, Page page, HttpSession session) throws UnsupportedEncodingException;
	
	/**
	 * 发送中国银行流水记录至金蝶
	 * <b>Description:</b>
	 * @param bankBill
	 * @param page
	 * @param session
	 * @return ResultInfo
	 * @Note
	 * <b>Author：</b> bill.bo
	 * <b>Date:</b> 2018年10月18日 下午2:46:18
	 */
	ResultInfo<T> sendChPayBillToKindlee(BankBill bankBill, Page page, HttpSession session);

    ResultInfo<T> updataBankBillMatchedAmount(Integer bankBillId, Integer capitalBillId);

    ResultInfo batchUpdateBankBillMatchedAmount(Integer bankBillId, List<Integer> capitalBillIdList);

    /**
     * 获取银联号
     */
    ResultInfo<List<BankNoDto>> getChinaBanKNo();

    /**
     * 调用db接口请求前置机获取回单文件保存
     * @param bankBillList
     * @return
     */
    ResultInfo<T> getBankFile(List<BankBillExtDo> bankBillList);

    /**
     * 查询时间范围内未下载回单的银行流水
     *
     * @param beginTime
     * @param endTime
     * @param bankTag
     * @param flag1
     * @return
     */
    List<BankBillExtDo> queryNeedDownLoadBankBill(@Param("beginTime") Date beginTime, @Param("endTime") Date endTime,
                                                  @Param("bankTag") Integer bankTag);

    List<BankBillExtDo> queryBankBillOfNoReceiptUrl(String beginTime, String endTime, List<Integer> bankTagList, Integer flag1);

    void updataBankBillReceiptUrl(Integer bankBillId,String receiptUrl);

    /**
     * 根据银行流水ID查询流水信息推送金蝶
     * <AUTHOR>
     * @param bankBillList
     * @return
     */
    List<BankBillExtDo> queryBankBillByIdList(List<Integer> bankBillList);

    /**
     * 根据流水号查询银行流水信息
     */
    List<BankBillExtDo> findBankByTranFlow(String receiveRecordBankNum);

    /**
     * 调用前置机nginx下载回单文件
     * @param bankBillList
     * @param cbcBankFileUrl
     */
    void downloadBankFile(List<BankBillExtDo> bankBillList,String cbcBankFileUrl);

    /**
     * 保存忽略记录
     *
     * @param user
     * @param ignoreRecordVo
     */
    Boolean saveIgnoreRecord(User user, BankBillIgnoreRecordVo ignoreRecordVo);

    List<KingDeeCustomerResultDto> getKingDeeUnitNameList(String name, Integer unitTypeId, Integer bankBillId);

    /**
     * 根据资金匹配流水号查询支付宝流水信息
     */
    List<BankBillExtDo> findAlipayByCapitalSearchFlow(String receiveRecordAlipayNum);

    /**
     * 根据tranDate获取建行回单的bankBillIdList
     *
     * @param beginDate tranDate
     * @param endDate   tranDate
     */
    List<BankBillExtDo> queryCcbByTranDate(String beginDate, String endDate);
}
