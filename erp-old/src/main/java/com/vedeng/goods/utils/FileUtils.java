package com.vedeng.goods.utils;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class FileUtils {

    private static final String FILE_EXT_SEPARATOR = ".";

    public static String getFileExtName(String fileName) {
        int fileExtSeparatorIdx = fileName.lastIndexOf(FILE_EXT_SEPARATOR);
        if (fileExtSeparatorIdx == -1) {
            return null;
        }
        return fileExtSeparatorIdx < fileName.length() - 1 ? fileName.substring(fileExtSeparatorIdx + 1).trim() : null;
    }


    public static boolean isPdfExtension(String fileExtName) {
        if (StringUtils.isEmpty(fileExtName)) {
            return false;
        }
        String nameToCompare = fileExtName;
        if (fileExtName.startsWith(FILE_EXT_SEPARATOR)) {
            nameToCompare = fileExtName.substring(1);
        }

        return nameToCompare.toUpperCase().equals(FileExtensionType.PDF.getFileExtName());
    }


    @Getter
    private enum FileExtensionType {
        PDF("PDF");

        private String fileExtName;

        FileExtensionType(String fileExtName) {
            this.fileExtName = fileExtName;
        }
    }
}
