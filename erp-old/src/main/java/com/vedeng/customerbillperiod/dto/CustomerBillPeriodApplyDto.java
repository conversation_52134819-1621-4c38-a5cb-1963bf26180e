package com.vedeng.customerbillperiod.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: daniel
 * @Date: 2021/7/26 15 32
 * @Description: 客户账期申请传输类
 */
@Data
public class CustomerBillPeriodApplyDto {

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 客户所属公司ID
     */
    private Integer companyId;

    /**
     * 账期类型，1正式账期，2临时账期，3订单账期  @CustomerBillPeriodTypeEnum
     */
    private Integer billPeriodType;

    /**
     * 申请操作类型，1新增，2调整 @CustomerBillPeriodApplyTypeEnum
     */
    private Integer operateType;

    /**
     * 订单账期关联的订单号
     */
    private Integer relatedOrderId;

    /**
     * 账期申请额度
     */
    private BigDecimal applyAmount;

    /**
     * 账期有效期开始时间
     */
    private Long billPeriodStart;

    /**
     * 账期有效期截止时间
     */
    private Long billPeriodEnd;

    /**
     * 账期结算周期
     */
    private Integer settlementPeriod;

    /**
     * 预期毛利率
     */
    private BigDecimal expectedMargin;

    /**
     * 账期申请原因
     */
    private String applyReason;

    /**
     * 关联的账期ID，如果是新增账期，则为0
     */
    private Long billPeriodId;

    private Integer creator;
}
