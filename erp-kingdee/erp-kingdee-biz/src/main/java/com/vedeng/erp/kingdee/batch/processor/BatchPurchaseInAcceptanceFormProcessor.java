package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.domain.FileInfoDto;
import com.vedeng.common.core.utils.FileInfoUtils;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchAttachmentDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.repository.BatchAttachmentDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeeFileDataDto;
import com.vedeng.erp.kingdee.dto.KingDeePurchaseReceiptDto;
import com.vedeng.erp.kingdee.service.KingDeeFileDataService;
import com.vedeng.erp.kingdee.service.KingDeePurchaseReceiptService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购入库验收单推送
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchPurchaseInAcceptanceFormProcessor extends BaseProcessor<BatchWarehouseGoodsOutInDto, KingDeeFileDataDto> {

    public static final String ZERO = "0";

    @Value("${oss_http}")
    private String ossHttp;

    @Autowired
    private BatchAttachmentDtoMapper batchAttachmentDtoMapper;
    @Autowired
    private KingDeePurchaseReceiptService kingDeePurchaseReceiptService;

    @Autowired
    private KingDeeFileDataService kingDeeFileDataService;

    @Override
    public KingDeeFileDataDto doProcess(BatchWarehouseGoodsOutInDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("采购入库单数据：{}", JSON.toJSONString(dto));
        BatchAttachmentDto query = BatchAttachmentDto.builder()
                .attachmentType(462)
                .attachmentFunction(4211)
                .relatedId(dto.getWarehouseGoodsOutInId().intValue())
                .build();
        BatchAttachmentDto batchAttachmentDto = batchAttachmentDtoMapper.purchaseInfindByQuery(query);
        log.info("上传采购入库单附件入参对象：{}", JSON.toJSONString(batchAttachmentDto));
        if (Objects.isNull(batchAttachmentDto) || StrUtil.isEmpty(batchAttachmentDto.getUri()) || StrUtil.isEmpty(batchAttachmentDto.getDomain())) {
            log.info("暂无采购入库单附件: {}", JSON.toJSONString(dto));
            return null;
        }

        String fileUrl = ossHttp + batchAttachmentDto.getDomain() + batchAttachmentDto.getUri();
        KingDeeFileDataDto fileDataDto = KingDeeFileDataDto.builder()
                .formId(KingDeeFormConstant.STK_INSTOCK)
                .erpId(dto.getWarehouseGoodsOutInId().toString())
                .url(fileUrl)
                .build();
        List<KingDeeFileDataDto> existFile = kingDeeFileDataService.getByBusinessIdAndUri(fileDataDto);
        if (!CollectionUtils.isEmpty(existFile)) {
            log.info("当前附件已经推送过金蝶，{}", JSON.toJSONString(fileDataDto));
            return null;
        }

        KingDeePurchaseReceiptDto purchaseReceiptDto = new KingDeePurchaseReceiptDto();
        purchaseReceiptDto.setFBillNo(dto.getOutInNo());
        kingDeePurchaseReceiptService.query(purchaseReceiptDto);
        if (purchaseReceiptDto.getFId() == null || ZERO.equals(purchaseReceiptDto.getFId())) {
            log.info("上传入库单附件,入库单未推送金蝶：{}", dto.getOutInNo());
            return null;
        }

        FileInfoDto base64FromUrl = FileInfoUtils.getBase64FromUrl(fileUrl);
        String name = StrUtil.isEmpty(batchAttachmentDto.getName()) ? "file" + base64FromUrl.getSuffix() : batchAttachmentDto.getName() + base64FromUrl.getSuffix();
        return KingDeeFileDataDto.builder()
                .fileName(name)
//                .sendByte(base64FromUrl.getFileBase64())
                .aliasFileName(batchAttachmentDto.getName())
                .billNo(purchaseReceiptDto.getFBillNo())
                .formId(purchaseReceiptDto.getFormId())
                .isLast(true)
                .fId(purchaseReceiptDto.getFId())
                .url(fileUrl)
                .erpId(dto.getWarehouseGoodsOutInId().toString())
                .businessId(purchaseReceiptDto.getFormId() + dto.getWarehouseGoodsOutInId().toString())
                .build();
    }
}
