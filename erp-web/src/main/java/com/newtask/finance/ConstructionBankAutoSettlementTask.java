package com.newtask.finance;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.redis.redission.RedissonLockUtils;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.finance.service.BankBillAutoSettlementService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description 建设银行银行流水自动结款
 * @date 2024/1/6 9:54
 **/
@JobHandler("ConstructionBankAutoSettlementTask")
@Component
@Slf4j
public class ConstructionBankAutoSettlementTask extends AbstractJobHandler {


    @Autowired
    private BankBillAutoSettlementService bankBillAutoSettlementService;

    private final static String REDIS_KEY_LOAD = "ERP:BATCH:SETTLEMENT:LOADING:1";

    @Value("${autoBankTime}")
    public int autoBankTime;


    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        log.info("ConstructionBankAutoSettlementTask 定时任务开始，param:{}",JSON.toJSONString(param));
        boolean lock = RedissonLockUtils.tryLock(REDIS_KEY_LOAD);

        if (!lock) {
            log.info("定时任务加锁失败，手动触发自动结款,存在处理中的结款业务");
            return ReturnT.SUCCESS;
        }
        log.info("自动结款加锁成功, key = [{}]", REDIS_KEY_LOAD);

        try {
            if (StrUtil.isEmpty(param)) {
                Date end = new Date();
                DateTime begin = DateUtil.offsetDay(end, -autoBankTime);
                bankBillAutoSettlementService.doWithPage(begin, end, false);

            } else {
                JSONObject jsonObject = JSON.parseObject(param);
                String beginTime = jsonObject.getString("beginTime");
                String endTime = jsonObject.getString("endTime");
                DateTime begin = DateUtil.parseDateTime(beginTime);
                DateTime end = DateUtil.parseDateTime(endTime);
                bankBillAutoSettlementService.doWithPage(begin, end, false);
            }
        } catch (Exception e) {
            log.error("触发自动结款,异常",e);
        }finally {
            RedissonLockUtils.unlock(REDIS_KEY_LOAD);
            log.info("自动结款释放锁成功, key = [{}]", REDIS_KEY_LOAD);
        }

        log.info("ConstructionBankAutoSettlementTask 定时任务结束");

        return ReturnT.SUCCESS;
    }
}
