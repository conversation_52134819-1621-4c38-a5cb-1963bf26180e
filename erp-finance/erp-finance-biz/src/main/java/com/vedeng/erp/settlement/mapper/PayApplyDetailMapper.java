package com.vedeng.erp.settlement.mapper;

import com.vedeng.erp.settlement.domain.entity.PayApplyDetailEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2022/11/4 15:43
 **/
@Named("newPayApplyDetailMapper")
public interface PayApplyDetailMapper {
    /**
     * delete by primary key
     * @param payApplyDetailId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer payApplyDetailId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(PayApplyDetailEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(PayApplyDetailEntity record);

    /**
     * select by primary key
     * @param payApplyDetailId primary key
     * @return object by primary key
     */
    PayApplyDetailEntity selectByPrimaryKey(Integer payApplyDetailId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(PayApplyDetailEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(PayApplyDetailEntity record);

    int updateBatchSelective(List<PayApplyDetailEntity> list);

    int batchInsert(@Param("list") List<PayApplyDetailEntity> list);
}