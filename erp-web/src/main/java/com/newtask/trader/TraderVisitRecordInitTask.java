package com.newtask.trader;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.system.common.enums.UserOrgEnums;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.trader.dto.VisitInputApiDto;
import com.vedeng.erp.trader.dto.VisitInputDto;
import com.vedeng.erp.trader.service.VisitRecordApiService;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;

import java.util.List;


/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/5/14
 */
@JobHandler("TraderVisitRecordInitTask")
@Component
@Slf4j
public class TraderVisitRecordInitTask extends AbstractJobHandler {

    @Autowired
    private VisitRecordApiService visitRecordApiService;

    @Autowired
    private UserApiService userService;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        log.info("TraderVisitRecordInitTask 定时任务开始，param:{}", JSON.toJSONString(param));
        List<VisitInputDto> visitInputDtoList = visitRecordApiService.selectInitVisitRecordList();
        try {
            for(VisitInputDto visitInputDto:visitInputDtoList){
                UserOrgEnums userOrgEnums = userService.getUserOrgEnum(visitInputDto.getVisitorId());
                if(!userOrgEnums.equals(UserOrgEnums.ORG_OTHER)){//非其他部门，一律更新为具体的部门
                    visitInputDto.setOrgId(0);
                    visitInputDto.setOrgGroup(userOrgEnums.getOrgGroupName());
                    visitRecordApiService.initVisitRecord(visitInputDto);
                }
            }
        } catch (Exception e) {
            log.error("TraderVisitRecordInitTask 异常：", e);
            return ReturnT.FAIL;
        }
        log.info("TraderVisitRecordInitTask 定时任务结束");
        return ReturnT.SUCCESS;
    }



}
