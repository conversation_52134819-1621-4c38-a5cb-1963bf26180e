<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.aftersale.mapper.ExpenseAfterSalesItemMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesItemEntity">
    <id column="EXPENSE_AFTER_SALES_ITEM_ID" jdbcType="BIGINT" property="expenseAfterSalesItemId" />
    <result column="EXPENSE_AFTER_SALES_ID" jdbcType="BIGINT" property="expenseAfterSalesId" />
    <result column="BUYORDER_EXPENSE_ITEM_ID" jdbcType="INTEGER" property="buyorderExpenseItemId" />
    <result column="SKU" jdbcType="VARCHAR" property="sku" />
    <result column="RETURN_NUM" jdbcType="INTEGER" property="returnNum" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>

  <resultMap id="DtoResultMap" type="com.vedeng.erp.aftersale.dto.ExpenseAfterSalesItemDto">
    <id column="EXPENSE_AFTER_SALES_ITEM_ID" jdbcType="BIGINT" property="expenseAfterSalesItemId" />
    <result column="EXPENSE_AFTER_SALES_ID" jdbcType="BIGINT" property="expenseAfterSalesId" />
    <result column="BUYORDER_EXPENSE_ITEM_ID" jdbcType="INTEGER" property="buyorderExpenseItemId" />
    <result column="SKU" jdbcType="VARCHAR" property="sku" />
    <result column="RETURN_NUM" jdbcType="INTEGER" property="returnNum" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    EXPENSE_AFTER_SALES_ITEM_ID, EXPENSE_AFTER_SALES_ID, BUYORDER_EXPENSE_ITEM_ID, SKU, 
    RETURN_NUM, IS_DELETE, ADD_TIME, CREATOR, CREATOR_NAME, MOD_TIME, UPDATER, UPDATER_NAME, 
    UPDATE_REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_EXPENSE_AFTER_SALES_ITEM
    where EXPENSE_AFTER_SALES_ITEM_ID = #{expenseAfterSalesItemId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from T_EXPENSE_AFTER_SALES_ITEM
    where EXPENSE_AFTER_SALES_ITEM_ID = #{expenseAfterSalesItemId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="EXPENSE_AFTER_SALES_ITEM_ID" keyProperty="expenseAfterSalesItemId" parameterType="com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesItemEntity" useGeneratedKeys="true">
    insert into T_EXPENSE_AFTER_SALES_ITEM (EXPENSE_AFTER_SALES_ID, BUYORDER_EXPENSE_ITEM_ID, 
      SKU, RETURN_NUM, IS_DELETE, 
      ADD_TIME, CREATOR, CREATOR_NAME, 
      MOD_TIME, UPDATER, UPDATER_NAME, 
      UPDATE_REMARK)
    values (#{expenseAfterSalesId,jdbcType=BIGINT}, #{buyorderExpenseItemId,jdbcType=INTEGER}, 
      #{sku,jdbcType=VARCHAR}, #{returnNum,jdbcType=INTEGER}, #{isDelete,jdbcType=INTEGER},
      #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, 
      #{modTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR}, 
      #{updateRemark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="EXPENSE_AFTER_SALES_ITEM_ID" keyProperty="expenseAfterSalesItemId" parameterType="com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesItemEntity" useGeneratedKeys="true">
    insert into T_EXPENSE_AFTER_SALES_ITEM
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="expenseAfterSalesId != null">
        EXPENSE_AFTER_SALES_ID,
      </if>
      <if test="buyorderExpenseItemId != null">
        BUYORDER_EXPENSE_ITEM_ID,
      </if>
      <if test="sku != null">
        SKU,
      </if>
      <if test="returnNum != null">
        RETURN_NUM,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="expenseAfterSalesId != null">
        #{expenseAfterSalesId,jdbcType=BIGINT},
      </if>
      <if test="buyorderExpenseItemId != null">
        #{buyorderExpenseItemId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="returnNum != null">
        #{returnNum,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesItemEntity">
    update T_EXPENSE_AFTER_SALES_ITEM
    <set>
      <if test="expenseAfterSalesId != null">
        EXPENSE_AFTER_SALES_ID = #{expenseAfterSalesId,jdbcType=BIGINT},
      </if>
      <if test="buyorderExpenseItemId != null">
        BUYORDER_EXPENSE_ITEM_ID = #{buyorderExpenseItemId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="returnNum != null">
        RETURN_NUM = #{returnNum,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where EXPENSE_AFTER_SALES_ITEM_ID = #{expenseAfterSalesItemId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesItemEntity">
    update T_EXPENSE_AFTER_SALES_ITEM
    set EXPENSE_AFTER_SALES_ID = #{expenseAfterSalesId,jdbcType=BIGINT},
      BUYORDER_EXPENSE_ITEM_ID = #{buyorderExpenseItemId,jdbcType=INTEGER},
      SKU = #{sku,jdbcType=VARCHAR},
      RETURN_NUM = #{returnNum,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where EXPENSE_AFTER_SALES_ITEM_ID = #{expenseAfterSalesItemId,jdbcType=BIGINT}
  </update>

  <select id="getItemReturnNum" resultType="com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesItemEntity">
    SELECT
    teasi.BUYORDER_EXPENSE_ITEM_ID,
    SUM(teasi.RETURN_NUM) AS RETURN_NUM
    FROM
    T_EXPENSE_AFTER_SALES_ITEM teasi
    LEFT JOIN T_EXPENSE_AFTER_SALES_STATUS teass ON
    teasi.EXPENSE_AFTER_SALES_ID = teass.EXPENSE_AFTER_SALES_ID
    WHERE
    teasi.IS_DELETE = 0
    AND teass.AFTER_SALES_STATUS = 2
    AND teasi.BUYORDER_EXPENSE_ITEM_ID IN
    <foreach collection="expenseItemIdList" item="expenseItemId" close=")" open="(" separator=",">
      #{expenseItemId,jdbcType=INTEGER}
    </foreach>
    GROUP BY
    teasi.BUYORDER_EXPENSE_ITEM_ID
  </select>
  <select id="getReturnNum" resultType="com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesItemEntity">
    SELECT
        teasi.RETURN_NUM,
        teasi.EXPENSE_AFTER_SALES_ITEM_ID
    FROM
        T_EXPENSE_AFTER_SALES_ITEM teasi
    WHERE
        teasi.EXPENSE_AFTER_SALES_ID = #{expenseAfterSalesId,jdbcType=BIGINT}
        AND teasi.BUYORDER_EXPENSE_ITEM_ID = #{buyorderExpenseItemId,jdbcType=INTEGER}
        AND teasi.IS_DELETE = 0
    LIMIT 1
  </select>

  <insert id="batchInsertSelective" parameterType="map">
    insert into T_EXPENSE_AFTER_SALES_ITEM
      (EXPENSE_AFTER_SALES_ID, BUYORDER_EXPENSE_ITEM_ID, SKU, RETURN_NUM, ADD_TIME, CREATOR, CREATOR_NAME, MOD_TIME, UPDATER, UPDATER_NAME)
    values
      <foreach collection="list" item="item" separator=",">
        (#{item.expenseAfterSalesId,jdbcType=BIGINT}, #{item.buyorderExpenseItemId,jdbcType=INTEGER}, #{item.sku,jdbcType=VARCHAR},
        #{item.returnNum,jdbcType=INTEGER},#{item.addTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR},
        #{item.modTime,jdbcType=TIMESTAMP}, #{item.updater,jdbcType=INTEGER}, #{item.updaterName,jdbcType=VARCHAR})
      </foreach>
  </insert>

  <select id="getExpenseAfterSalesItemListByAfterSalesId" resultType="com.vedeng.erp.aftersale.dto.ExpenseAfterSalesItemDto">
    SELECT
        teasi.SKU,
        tbeid.GOODS_NAME ,
        tbeid.EXPENSE_CATEGORY_NAME ,
        tbei.NUM,
        tbeid.PRICE,
        teasi.RETURN_NUM,
        teasi.EXPENSE_AFTER_SALES_ITEM_ID,
        teasi.EXPENSE_AFTER_SALES_ID,
        tbei.GOODS_ID
    FROM
        T_EXPENSE_AFTER_SALES_ITEM teasi
        LEFT JOIN T_BUYORDER_EXPENSE_ITEM tbei ON teasi.BUYORDER_EXPENSE_ITEM_ID = tbei.BUYORDER_EXPENSE_ITEM_ID AND tbei.IS_DELETE = 0
        LEFT JOIN T_BUYORDER_EXPENSE_ITEM_DETAIL tbeid ON tbei.BUYORDER_EXPENSE_ITEM_ID = tbeid.BUYORDER_EXPENSE_ITEM_ID AND tbeid.IS_DELETE = 0
    WHERE
        teasi.EXPENSE_AFTER_SALES_ID = #{expenseAfterSalesId,jdbcType=BIGINT}
        AND teasi.IS_DELETE = 0
  </select>

  <select id="getExpenseAfterSalesReturnGoodsBybuyorderExpenseId" resultMap="DtoResultMap">
    select TEASI.* from T_EXPENSE_AFTER_SALES TEAS
      left join T_EXPENSE_AFTER_SALES_STATUS TEASS on TEAS.EXPENSE_AFTER_SALES_ID = TEASS.EXPENSE_AFTER_SALES_ID
                    left join T_EXPENSE_AFTER_SALES_ITEM TEASI  on TEAS.EXPENSE_AFTER_SALES_ID = TEASI.EXPENSE_AFTER_SALES_ID
    where TEASS.AFTER_SALES_STATUS = 2  and TEAS.IS_DELETE = 0 and TEASI.IS_DELETE = 0 and TEAS.EXPENSE_AFTER_SALES_TYPE = 4121
    and TEAS.BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER}
  </select>

  <select id="getExpenseAfterSalesReturnGoodsBybuyorderExpenseIdAndSaleOrderId" resultType="com.vedeng.erp.aftersale.dto.ExpenseAfterSalesItemDto">
    select TEASI.BUYORDER_EXPENSE_ITEM_ID,SUM(TREASJS.AFTER_SALES_NUM) returnNum
    from T_EXPENSE_AFTER_SALES TEAS
    left join T_EXPENSE_AFTER_SALES_STATUS TEASS on TEAS.EXPENSE_AFTER_SALES_ID = TEASS.EXPENSE_AFTER_SALES_ID
    left join T_EXPENSE_AFTER_SALES_ITEM TEASI on TEAS.EXPENSE_AFTER_SALES_ID = TEASI.EXPENSE_AFTER_SALES_ID
    left join T_R_EXPENSE_AFTER_SALES_J_SALEORDER TREASJS on TEASI.EXPENSE_AFTER_SALES_ITEM_ID = TREASJS.EXPENSE_AFTER_SALES_ITEM_ID
    where TEASS.AFTER_SALES_STATUS = 2
    and TEAS.IS_DELETE = 0
    and TEASI.IS_DELETE = 0
    and TEAS.EXPENSE_AFTER_SALES_TYPE = 4121
    and TEAS.BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER}
    and TREASJS.SALEORDER_ID = #{saleOrderId,jdbcType=INTEGER}
    group by TEASI.BUYORDER_EXPENSE_ITEM_ID
  </select>
</mapper>