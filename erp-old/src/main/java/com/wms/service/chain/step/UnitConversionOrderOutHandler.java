package com.wms.service.chain.step;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.logistics.model.WarehouseStock;
import com.vedeng.logistics.service.WarehouseStockService;
import com.wms.constant.LogicalEnum;
import com.wms.constant.WmsLogicalOperateTypeEnum;
import com.wms.dao.WmsLogicalOrdergoodsMapper;
import com.wms.dao.WmsOutputOrderGoodsMapper;
import com.wms.model.dto.WmsOutputOrderGoodsDto;
import com.wms.model.po.WmsLogicalOrdergoods;
import com.wms.model.po.WmsOutputOrderGoods;
import com.wms.service.chain.AbstractHandlerStep;
import com.wms.service.chain.HandlerStepContext;
import com.wms.unitconversionorder.dao.WmsUnitConversionOrderItemMapper;
import com.wms.unitconversionorder.model.entity.WmsUnitConversionOrderItem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 单位转换单出库 逻辑单处理
 * <AUTHOR>
 */
@Service
@Slf4j
public class UnitConversionOrderOutHandler extends AbstractHandlerStep {

    @Resource
    private WmsLogicalOrdergoodsMapper wmsLogicalOrdergoodsMapper;

    @Autowired
    private WmsUnitConversionOrderItemMapper wmsUnitConversionOrderItemMapper;

    @Autowired
    private WarehouseStockService warehouseStockService;

    @Override
    protected void doDealWith(HandlerStepContext context) throws Exception {

        Integer wmsUnitConversionOrderId = context.get("wmsUnitConversionOrderId");
        // 新增逻辑订单数据
        List<WmsLogicalOrdergoods> wmsLogicalOrdergoodsList = new ArrayList<>();
        List<WmsUnitConversionOrderItem> wmsUnitConversionOrderItems = wmsUnitConversionOrderItemMapper.selectByWmsUnitConversionOrderId(wmsUnitConversionOrderId);

        Map<String, WarehouseStock> logicalStockMapInfo = warehouseStockService.getLogicalStockMapInfo(wmsUnitConversionOrderItems.stream().map(WmsUnitConversionOrderItem::getSourceSkuNo).collect(Collectors.toList()));
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        for (WmsUnitConversionOrderItem wmsUnitConversionOrderItem : wmsUnitConversionOrderItems) {
            WarehouseStock warehouseStock = logicalStockMapInfo.get(wmsUnitConversionOrderItem.getSourceSkuNo() + LogicalEnum.JXQ.getLogicalWarehouseId());
            int stockNum = Objects.isNull(warehouseStock)?0:warehouseStock.getAvailableStockNum()<0?0:warehouseStock.getAvailableStockNum();

            // 小于的时候直接给jxq库存
            if (wmsUnitConversionOrderItem.getSourceNum().intValue() <= stockNum) {
                warehouseStock.setAvailableStockNum(stockNum-wmsUnitConversionOrderItem.getSourceNum().intValue());
                WmsLogicalOrdergoods insert = new WmsLogicalOrdergoods();
                insert.setNum(wmsUnitConversionOrderItem.getSourceNum().intValue());
                insert.setRelatedId(wmsUnitConversionOrderItem.getWmsUnitConversionOrderItemId());
                insert.setOccupyNum(wmsUnitConversionOrderItem.getSourceNum().intValue());
                insert.setSku(wmsUnitConversionOrderItem.getSourceSkuNo());
                insert.setGoodsId(wmsUnitConversionOrderItem.getSourceSkuId());
                insert.setLogicalWarehouseId(LogicalEnum.JXQ.getLogicalWarehouseId());
                bindWmsLogicalOrdergoods(wmsUnitConversionOrderId, wmsLogicalOrdergoodsList, currentUser, insert);

            } else {
                // 剩余的时候其他数量给合格仓
                warehouseStock.setAvailableStockNum(0);
                if (stockNum > 0) {
                    WmsLogicalOrdergoods insert = new WmsLogicalOrdergoods();
                    insert.setNum(stockNum);
                    insert.setRelatedId(wmsUnitConversionOrderItem.getWmsUnitConversionOrderItemId());
                    insert.setOccupyNum(stockNum);
                    insert.setSku(wmsUnitConversionOrderItem.getSourceSkuNo());
                    insert.setGoodsId(wmsUnitConversionOrderItem.getSourceSkuId());
                    insert.setLogicalWarehouseId(LogicalEnum.JXQ.getLogicalWarehouseId());
                    bindWmsLogicalOrdergoods(wmsUnitConversionOrderId, wmsLogicalOrdergoodsList, currentUser, insert);
                }

                int hgNum = wmsUnitConversionOrderItem.getSourceNum().intValue() - stockNum;
                WmsLogicalOrdergoods insert = new WmsLogicalOrdergoods();
                insert.setNum(hgNum);
                insert.setRelatedId(wmsUnitConversionOrderItem.getWmsUnitConversionOrderItemId());
                insert.setOccupyNum(hgNum);
                insert.setSku(wmsUnitConversionOrderItem.getSourceSkuNo());
                insert.setGoodsId(wmsUnitConversionOrderItem.getSourceSkuId());
                insert.setLogicalWarehouseId(LogicalEnum.HG.getLogicalWarehouseId());
                bindWmsLogicalOrdergoods(wmsUnitConversionOrderId, wmsLogicalOrdergoodsList, currentUser, insert);

            }


        }
        context.put("wmsLogicalOrdergoodsList",wmsLogicalOrdergoodsList);
    }

    @Transactional(rollbackFor = Throwable.class)
    public void bindWmsLogicalOrdergoods(Integer wmsUnitConversionOrderId, List<WmsLogicalOrdergoods> wmsLogicalOrdergoodsList, CurrentUser currentUser, WmsLogicalOrdergoods insert) {
        insert.setOperateType(WmsLogicalOperateTypeEnum.UNIT_CONVERSION_OUT.getOperateTypeCode());
        insert.setCreator(currentUser.getId());
        insert.setUpdater(currentUser.getId());
        log.info("库存转换出库单:" + wmsUnitConversionOrderId + ",新增逻辑订单数据:{}", JSON.toJSONString(insert));
        wmsLogicalOrdergoodsMapper.insertSelective(insert);
        wmsLogicalOrdergoodsList.add(insert);
    }

}
