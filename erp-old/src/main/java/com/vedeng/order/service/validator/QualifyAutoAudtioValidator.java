package com.vedeng.order.service.validator;

import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.order.service.validator.dto.QualifyAutoAudtioException;

/**
 * 资质自动审核接口
 */
public interface QualifyAutoAudtioValidator {

    boolean isMatch(BuyorderGoodsVo buyOrderGoods);

    String validator(BuyorderGoodsVo buyOrderGoods) throws QualifyAutoAudtioException;
    
    /**
     * 判断是否匹配（基于traderId和skuNo）
     * @param traderId 交易员ID
     * @param skuNo 商品编号
     * @return 是否匹配
     */
    boolean isMatch(Integer traderId, String skuNo);

    /**
     * 执行验证（基于traderId和skuNo）
     * @param traderId 交易员ID
     * @param skuNo 商品编号
     * @return 验证通过信息
     * @throws QualifyAutoAudtioException 验证失败时抛出此异常
     */
    String validator(Integer traderId, String skuNo) throws QualifyAutoAudtioException;

}
