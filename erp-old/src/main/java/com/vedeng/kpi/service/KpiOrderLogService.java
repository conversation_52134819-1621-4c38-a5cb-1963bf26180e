package com.vedeng.kpi.service;

import com.vedeng.kpi.model.DO.KpiOrderLogDo;
import com.vedeng.kpi.model.DTO.KpiDataQueryDto;
import com.vedeng.kpi.model.DTO.KpiUserInfoDto;

import java.util.Date;
import java.util.List;

/**
 * 五行log表操作.
 * @jira: VDERP-2376【五行剑法】规则修改.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/6/9 10:55 上午.
 * @author: Tomcat.Hui.
 */
public interface KpiOrderLogService {

    /**
     * 插入数据.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/9 11:46 上午.
     * @author: Tomcat.Hui.
     * @param kpiOrderLog: .
     * @return: java.lang.Integer.
     * @throw: .
     */
    Integer insertSelective(KpiOrderLogDo kpiOrderLog);

    /**
     * 获取log.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/9 11:47 上午.
     * @author: Tomcat.Hui.
     * @param query: .
     * @return: java.util.List<com.vedeng.kpi.model.DTO.KpiDataQueryDto>.
     * @throw: .
     */
    List<KpiDataQueryDto> getKpiLogDetail(KpiDataQueryDto query);

    /**
     * 获取未被消费的数据.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/9 11:47 上午.
     * @author: Tomcat.Hui.
     * @param updateKpiDate: .
     * @return: java.util.List<com.vedeng.kpi.model.DTO.KpiUserInfoDto>.
     * @throw: .
     */
    List<KpiOrderLogDo> getUserToUpdate(Date updateKpiDate);

    /**
     * 更新消费状态位.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/9 1:50 下午.
     * @author: Tomcat.Hui.
     * @param query: .
     * @return: java.lang.Integer.
     * @throw: .
     */
    Integer updateUnusedLog(KpiDataQueryDto query);
}

