package com.vedeng.temporal.domain.entity;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 业务流程汇总信息实体类
 * 对应表：T_FLOW_ORDER_INFO
 * 
 * 用途说明：
 * - 记录多公司业务流程中的关键业务单据汇总信息
 * - 跟踪采购单/销售单的各种状态变化
 * - 管理发票信息和合同文件
 * - 支持业务流程监控和数据分析
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-14
 */
@Getter
@Setter
public class FlowOrderInfoEntity {

    /**
     * 主键
     */
    private Long flowOrderInfoId;

    /**
     * 业务类型
     * 0: 采购
     * 1: 销售
     */
    private Integer flowOrderInfoType;

    /**
     * 业务编号
     * 采购单号或销售单号
     */
    private String flowOrderInfoNo;

    /**
     * 节点ID
     * 关联 T_FLOW_NODE.FLOW_NODE_ID
     */
    private Long flowNodeId;

    /**
     * 订单状态
     * 0: 处理中
     * 1: 已完结
     */
    private Integer orderStatus;

    /**
     * 款项状态
     * 0: 未付款
     * 1: 部分付款
     * 2: 已付款
     */
    private Integer paymentStatus;

    /**
     * 入库状态
     * 0: 未入库
     * 1: 部分入库
     * 2: 已入库
     */
    private Integer storageStatus;

    /**
     * 票据状态
     * 0: 未收票
     * 1: 部分收票
     * 2: 已收票
     */
    private Integer invoiceStatus;

    /**
     * 发票信息
     * JSON格式：[{"invoiceNo":"发票号","invoiceUrl":"发票链接"}]
     */
    private String invoiceInfo;

    /**
     * 是否删除
     * 0: 未删除
     * 1: 已删除
     */
    private Integer isDelete;

    /**
     * 创建者ID
     */
    private Integer creator;

    /**
     * 修改者ID
     */
    private Integer updater;

    /**
     * 创建者名称
     */
    private String creatorName;

    /**
     * 修改者名称
     */
    private String updaterName;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 修改时间
     */
    private Date modTime;

    /**
     * 合同文件链接
     */
    private String contractFileUrl;

    /**
     * 合同文件名称
     */
    private String contractFileName;

    /**
     * 合同上传时间
     */
    private Date contractUploadTime;

    // ==================== 便捷方法 ====================

    /**
     * 获取业务类型描述
     *
     * @return 业务类型描述
     */
    public String getFlowOrderInfoTypeDesc() {
        if (flowOrderInfoType == null) {
            return "未知";
        }
        switch (flowOrderInfoType) {
            case 0:
                return "采购";
            case 1:
                return "销售";
            default:
                return "未知";
        }
    }

    /**
     * 获取订单状态描述
     *
     * @return 订单状态描述
     */
    public String getOrderStatusDesc() {
        if (orderStatus == null) {
            return "未知";
        }
        switch (orderStatus) {
            case 0:
                return "处理中";
            case 1:
                return "已完结";
            default:
                return "未知";
        }
    }

    /**
     * 获取款项状态描述
     *
     * @return 款项状态描述
     */
    public String getPaymentStatusDesc() {
        if (paymentStatus == null) {
            return "未知";
        }
        switch (paymentStatus) {
            case 0:
                return "未付款";
            case 1:
                return "部分付款";
            case 2:
                return "已付款";
            default:
                return "未知";
        }
    }

    /**
     * 获取入库状态描述
     *
     * @return 入库状态描述
     */
    public String getStorageStatusDesc() {
        if (storageStatus == null) {
            return "未知";
        }
        switch (storageStatus) {
            case 0:
                return "未入库";
            case 1:
                return "部分入库";
            case 2:
                return "已入库";
            default:
                return "未知";
        }
    }

    /**
     * 获取票据状态描述
     *
     * @return 票据状态描述
     */
    public String getInvoiceStatusDesc() {
        if (invoiceStatus == null) {
            return "未知";
        }
        switch (invoiceStatus) {
            case 0:
                return "未收票";
            case 1:
                return "部分收票";
            case 2:
                return "已收票";
            default:
                return "未知";
        }
    }

    /**
     * 检查是否为有效记录
     *
     * @return true-有效记录，false-已删除记录
     */
    public boolean isValid() {
        return isDelete == null || isDelete == 0;
    }

    /**
     * 检查是否有合同文件
     *
     * @return true-有合同文件，false-无合同文件
     */
    public boolean hasContractFile() {
        return contractFileUrl != null && !contractFileUrl.trim().isEmpty();
    }

    /**
     * 检查是否有发票信息
     *
     * @return true-有发票信息，false-无发票信息
     */
    public boolean hasInvoiceInfo() {
        return invoiceInfo != null && !invoiceInfo.trim().isEmpty();
    }

    /**
     * 检查是否有有效的发票信息
     * 解析INVOICE_INFO JSON，检查是否有有效的发票记录
     * JSON格式：[{invoiceNo:"",invoiceUrl:""}]
     *
     * @return true-有有效的发票信息，false-无有效的发票信息
     */
    public boolean hasValidInvoiceInfo() {
        if (invoiceInfo == null || invoiceInfo.trim().isEmpty()) {
            return false;
        }

        try {
            // 简单的JSON解析检查，避免引入复杂的JSON库依赖
            String trimmedJson = invoiceInfo.trim();
            
            // 基本格式检查：应该是数组格式 []
            if (!trimmedJson.startsWith("[") || !trimmedJson.endsWith("]")) {
                return false;
            }
            
            // 检查是否是空数组
            if (trimmedJson.equals("[]") || trimmedJson.equals("[ ]")) {
                return false;
            }
            
            // 检查是否包含invoiceNo和invoiceUrl字段，且都有值
            // 这里使用简单的字符串检查，适用于标准的JSON格式
            String jsonContent = trimmedJson.substring(1, trimmedJson.length() - 1); // 去掉外层[]
            
            // 检查是否包含必要的字段且有值
            // 检查invoiceNo字段存在且不为空字符串
            boolean hasInvoiceNo = jsonContent.contains("invoiceNo") && 
                                   !jsonContent.contains("invoiceNo\":\"\"") && 
                                   !jsonContent.contains("invoiceNo\": \"\"");
                                   
            // 检查invoiceUrl字段存在且不为空字符串
            boolean hasInvoiceUrl = jsonContent.contains("invoiceUrl") && 
                                    !jsonContent.contains("invoiceUrl\":\"\"") && 
                                    !jsonContent.contains("invoiceUrl\": \"\"");
            
            return hasInvoiceNo && hasInvoiceUrl;
            
        } catch (Exception e) {
            // JSON解析异常，认为无效
            return false;
        }
    }

    /**
     * 检查是否有有效的合同文件
     *
     * @return true-有有效的合同文件，false-无有效的合同文件
     */
    public boolean hasValidContractFile() {
        return contractFileUrl != null && 
               !contractFileUrl.trim().isEmpty() && 
               contractFileUrl.trim().length() > 0;
    }

    /**
     * 检查所有状态是否都已完成
     * 当款项、入库、票据状态都为已完成(2)时，且有有效的发票信息和合同文件时，认为所有状态都已完成
     *
     * @return true-所有状态都已完成，false-还有未完成的状态
     */
    public boolean isAllStatusCompleted() {
        return paymentStatus != null && paymentStatus == 2 &&
               storageStatus != null && storageStatus == 2 &&
               invoiceStatus != null && invoiceStatus == 2 &&
               hasValidInvoiceInfo() &&
               hasValidContractFile();
    }

    /**
     * 获取完成度百分比
     *
     * @return 完成度百分比（0-100）
     */
    public int getCompletionPercentage() {
        int totalSteps = 3; // 款项、入库、票据
        int completedSteps = 0;

        if (paymentStatus != null && paymentStatus == 2) completedSteps++;
        if (storageStatus != null && storageStatus == 2) completedSteps++;
        if (invoiceStatus != null && invoiceStatus == 2) completedSteps++;

        return (completedSteps * 100) / totalSteps;
    }

    /**
     * 检查是否为订单已完结状态
     *
     * @return true-订单已完结，false-订单处理中
     */
    public boolean isOrderCompleted() {
        return orderStatus != null && orderStatus == 1;
    }

    /**
     * 检查是否应该将订单状态设为已完结
     * 当所有业务状态（款项、入库、票据）都完成时，订单应标记为已完结
     *
     * @return true-应该设为已完结，false-还不应该设为已完结
     */
    public boolean shouldMarkAsCompleted() {
        return !isOrderCompleted() && isAllStatusCompleted();
    }

    /**
     * 标记订单为已完结状态
     */
    public void markAsCompleted() {
        this.orderStatus = 1;
        this.modTime = new Date();
    }

    @Override
    public String toString() {
        return "FlowOrderInfoEntity{" +
                "flowOrderInfoId=" + flowOrderInfoId +
                ", flowOrderInfoType=" + flowOrderInfoType +
                ", flowOrderInfoNo='" + flowOrderInfoNo + '\'' +
                ", flowNodeId=" + flowNodeId +
                ", orderStatus=" + orderStatus +
                ", paymentStatus=" + paymentStatus +
                ", storageStatus=" + storageStatus +
                ", invoiceStatus=" + invoiceStatus +
                ", isDelete=" + isDelete +
                ", addTime=" + addTime +
                ", modTime=" + modTime +
                '}';
    }
}
