package com.vedeng.common.core.utils.numgenerator.enums;

import com.vedeng.common.core.utils.numgenerator.NumGenerator;
import com.vedeng.common.core.utils.numgenerator.PrimaryKeyNumGenerator;
import com.vedeng.common.core.utils.numgenerator.RandomNumGenerator;
import com.vedeng.common.core.utils.numgenerator.RedisNumGenerator;
import com.vedeng.common.core.utils.numgenerator.bean.NoGeneratorBean;

/**
 * 单据类型
 */
public enum BillType {


    /**
     * 商机
     **/
    BUSINESS_CHANCE("", 1, "businessChanceMapper", new PrimaryKeyNumGenerator()),


    /**
     * 线索
     **/
    BUSINESS_LEADS("L", 2, "businessLeadsMapper",new PrimaryKeyNumGenerator()),

    /**
     * 报价
     */
    QUOTE_ORDER("VD", 3, "quoteorderMapper",new PrimaryKeyNumGenerator()),

    /**
     * 采购费用单
     */
    BUY_ORDER_EXPENSE("CGFY",4,"", new RandomNumGenerator(),NoGeneratorBean.builder().dateFormat("yyMMddHH").randomDigits(5).build()),

    /**
     * 盘亏出库单
     */
    INVENTORY_OUT_ORDER("PK",5,"",  new RedisNumGenerator(), NoGeneratorBean.builder().redisLength(5).key("PK_ORDER_NO").dateFormat("yyMMdd").build()),

    /**
     * 交易流水单号
     **/
    CAPITAL_NO("", 6, "", new PrimaryKeyNumGenerator()),

    /**
     * 采购费用售后单 退货
     */
    BUY_ORDER_EXPENSE_AFTER_SALES("APFYGR", 7, "", new RedisNumGenerator(), NoGeneratorBean.builder().dateFormat("yyMMddHH").key("PK_BUY_ORDER_EXPENSE_AFTER_SALES").redisLength(5).build()),

    /**
     * 采购费用售后单 仅退票
     */
    BUY_ORDER_EXPENSE_AFTER_SALES_REFUND_INVOICE("APFYTR", 8, "", new RedisNumGenerator(),NoGeneratorBean.builder().dateFormat("yyMMddHH").key("PK_BUY_ORDER_EXPENSE_AFTER_SALES_REFUND_INVOICE").redisLength(5).build()),
    /**
     * 出入库日志主表出库单号规则
     */
    WAREHOUSE_GOODS_OUT_IN_CK("CK", 9, "", new RedisNumGenerator(), NoGeneratorBean.builder().dateFormat("yyMMddHH").key("PK_WAREHOUSE_GOODS_OUT_IN_CK").redisLength(5).build()),

    /**
     * 虚拟出入库日志主表出库单号规则
     */
    WAREHOUSE_GOODS_OUT_IN_XNCK("XNCK", 10, "", new RedisNumGenerator(), NoGeneratorBean.builder().dateFormat("yyMMddHH").key("PK_WAREHOUSE_GOODS_OUT_IN_XNCK").redisLength(5).build()),

    /**
     * 入库日志主表出库单号规则
     */
    WAREHOUSE_GOODS_OUT_IN_RK("RK", 11, "", new RedisNumGenerator(), NoGeneratorBean.builder().dateFormat("yyMMddHH").key("PK_WAREHOUSE_GOODS_OUT_IN_RK").redisLength(5).build()),


    /**
     * 虚拟入库日志主表出库单号规则
     */
    WAREHOUSE_GOODS_OUT_IN_XNRK("XNRK", 12, "", new RedisNumGenerator(), NoGeneratorBean.builder().dateFormat("yyMMddHH").key("PK_WAREHOUSE_GOODS_OUT_IN_XNRK").redisLength(5).build()),

    /**
     * 直发产品出入库日志SN码
     */
    WAREHOUSE_GOODS_OPERATE_LOG_DIRECT_SN("",13,"", new RandomNumGenerator(),NoGeneratorBean.builder().dateFormat("yyMMddHHmmss").randomDigits(4).build()),

    /**
     * 领用出库单号规则 LY+时间信息（8位，年月日时）+ 顺序码（5位，数字）
     */
    RECEIVE_OUT_ORDER_LY("LY", 14, "", new RedisNumGenerator(), NoGeneratorBean.builder().dateFormat("yyMMddHH").key("PK_RECEIVE_OUT_ORDER_LY").redisLength(5).build()),


    /**
     * 确认单
     */
    CONFIRMATION_FORM_NAME("QRD",15,"", new RandomNumGenerator(),NoGeneratorBean.builder().dateFormat("yyMMddHHmmss").randomDigits(5).build()),

    /**
     * 样品出库单
     */
    WMS_SAMPLE_OUTPUT_ORDER("YP",16,"", new RedisNumGenerator(),NoGeneratorBean.builder().dateFormat("yyMMddHH").key("PK_WMS_SAMPLE_OUTPUT_ORDER_ZH").redisLength(5).build()),


    /**
     * 单位转换单
     */
    WMS_UINT_CONVERSION_ORDER("ZH",17,"", new RedisNumGenerator(),NoGeneratorBean.builder().dateFormat("yyyyMMdd").key("PK_WMS_UINT_CONVERSION_ORDER_ZH").redisLength(5).build()),

    /**
     * 采购返利结算收款申请
     */
    BUY_ORDER_REBATE_CHARGE_APPLY("SD", 18, "", new RedisNumGenerator(), NoGeneratorBean.builder().dateFormat("yyyyMMdd").key("PK_BUY_ORDER_REBATE_CHARGE_APPLY").redisLength(3).build()),

    /**
     * 财务结算单
     */
    SETTLEMENT_BILL("JS", 19, "", new RedisNumGenerator(), NoGeneratorBean.builder().dateFormat("yyyyMMdd").key("PK_SETTLEMENT_BILL").redisLength(4).build()),

    /**
     * 方案场景
     */
    SCENE_BILL("KS", 20, "", new RedisNumGenerator(), NoGeneratorBean.builder().dateFormat("yyMMdd").key("PK_SCENE_BILL").redisLength(4).build()),

    /**
     * 流转单
     */
    FLOW_ORDER("YWLZ", 21, "", new RedisNumGenerator(), NoGeneratorBean.builder().dateFormat("yyMMdd").key("PK_SCENE_BILL").redisLength(4).build()),
    /**
     * 拜访计划编号
     */
    VISIT_RECORD("VIS", 22, "", new RedisNumGenerator(), NoGeneratorBean.builder().dateFormat("yyyyMMdd").key("PK_VISIT_RECORD").redisLength(4).build()),

    ;


    /**
     * 前缀
     */
    private final String prefix;
    /**
     * type
     */
    private final Integer type;

    /**
     * 持久化对应类
     */
    private final String aClass;

    private NumGenerator numGenerator;

    private NoGeneratorBean noGeneratorBean;


    public String getPrefix() {
        return prefix;
    }

    public Integer getType() {
        return type;
    }

    public NumGenerator getNumGenerator() {
        return numGenerator;
    }

    public String getaClass() {
        return aClass;
    }

    public NoGeneratorBean getNoGeneratorBean() {
        return noGeneratorBean;
    }

    BillType(String prefix, Integer type, String aClass, NumGenerator numGenerator,NoGeneratorBean noGeneratorBean) {
        this.prefix = prefix;
        this.type = type;
        this.aClass = aClass;
        this.numGenerator = numGenerator;
        this.noGeneratorBean = noGeneratorBean;
    }

    BillType(String prefix, Integer type, String aClass,NumGenerator numGenerator) {
        this.prefix = prefix;
        this.type = type;
        this.aClass = aClass;
        this.numGenerator = numGenerator;
    }
}
