package com.vedeng.erp.kingdee.service.impl;

import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeeSalesVatPlainInvoiceCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeSalesVatPlainInvoiceEntity;
import com.vedeng.erp.kingdee.dto.KingDeeSalesVatPlainInvoiceDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeSalesVatPlainInvoiceCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeSalesVatPlainInvoiceConvertor;
import com.vedeng.erp.kingdee.repository.KingDeeSalesVatPlainInvoiceRepository;
import com.vedeng.erp.kingdee.service.KingDeeSalesVatPlainInvoiceApiService;
import com.vedeng.erp.kingdee.service.KingDeeSalesVatPlainInvoiceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶销售增值税普通发票Service实现
 * @date 2022/8/26 15:18
 */
@Service
@Slf4j
public class KingDeeSalesVatPlainInvoiceServiceImpl extends KingDeeBaseServiceImpl<
        KingDeeSalesVatPlainInvoiceEntity,
        KingDeeSalesVatPlainInvoiceDto,
        KingDeeSalesVatPlainInvoiceCommand,
        KingDeeSalesVatPlainInvoiceRepository,
        KingDeeSalesVatPlainInvoiceConvertor,
        KingDeeSalesVatPlainInvoiceCommandConvertor>
        implements KingDeeSalesVatPlainInvoiceService, KingDeeSalesVatPlainInvoiceApiService {


}
