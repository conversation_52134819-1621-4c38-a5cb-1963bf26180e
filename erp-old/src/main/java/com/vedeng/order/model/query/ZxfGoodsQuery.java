package com.vedeng.order.model.query;

import com.vedeng.goods.model.dto.BaseQueryDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> [<EMAIL>]
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ZxfGoodsQuery extends BaseQueryDto {
   private String searchContent;
   private Integer saleorderId;
   private String callbackFuntion;
   private Integer lendOut;
   private Integer logicalId;
   //产品名称
   private String goodsName;
    //订货号
    private String skuNo;
    //品牌
    private String brandName;
    //单位
    private String unitName;
    //规格/型号
    private String typeSpecification;
}
