<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ include file="../../common/common.jsp"%>
<%@ include file="../../component/remarkComponent.jsp"%>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<script type="text/javascript" src='<%=basePath%>/static/js/orderstream/saleorder/add_order_goods.js?rnd=${resourceVersionKey}'></script>
<script>
    $(function (){
        var saleOrderId = ${saleorderId};
        var goodsId = ${good.goodsId};
        var sku = '${good.sku}';
        $.ajax({
            async: true,
            url: '/orderstream/saleorder/getOrderGoodsPrice.do',
            data: {"saleorderId":  saleOrderId, "goodsId": goodsId, "skuNo": sku},
            type: "GET",
            dataType: "json",
            success: function (data) {
                if (data.code == 0) {

                    var returnDateMap = data.data;
                    //价格中心查到价格，自动填充单价，可修改
                    if (returnDateMap.salePrice != ''){
                        $("#confirmGoodsDiv").find("#price").val(returnDateMap.salePrice);
                    }

                    //限制改价的客户并且有对应的销售价，那么价格不让改
                    if (returnDateMap.limitPrice == "true" && returnDateMap.salePrice != '') {
                        $("#confirmGoodsDiv").find("#price").val(returnDateMap.salePrice);
                        $("#confirmGoodsDiv").find("#price").attr("readOnly", "true");
                    }
                    $("#confirmGoodsDiv").find("#priceShowSpan").html(returnDateMap.salePriceShow);

                    //If 协议商品展示标示
                    if (returnDateMap.contractedGoodsFlag == '1') {
                        $('.contractedGoodsFlag').show();
                        if (returnDateMap.salePrice != '') {
                            $("#confirmGoodsDiv").find("#price").val(returnDateMap.salePrice);
                        }
                    }
                } else {
                    layer.alert(data.message,
                        {icon: 2},
                        function (index) {
                            layer.close(index);
                        }
                    );
                }
            },
            error: function (data) {
                if (data.status == 1001) {
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        })
    })

</script>
<div class="formpublic formpublic1">



        <!-- ------------选择列表产品后操作--------start------- -->
        <div class="controlled" id="confirmGoodsDiv">
            <!-- 搜索最后结果lastResult -->
            <form id="confirmForm">
                <input type="hidden" name="saleorderId" id="saleorderId" value="${saleorderId}">
                <input type="hidden" name="goodsId" id="goodsId" value="${good.goodsId}"/>
                <input type="hidden" name="sku" id="sku" value="${good.sku}"/>
                <input type="hidden" name="goodsName" id="goodsName" value="${good.goodsName}"/>
                <input type="hidden" name="brandName" id="brandName" value="${good.brandName}"/>
                <input type="hidden" name="brandId" id="brandId" value="${good.brandId}"/>
                <input type="hidden" name="model" id="model" value="${good.model}"/>
                <input type="hidden" name="unitName" id="unitName" value="${good.unitName}"/>
                <input type="hidden" name="belongPlatform" id="belongPlatform" value="${belongPlatform}">
                <input type="hidden" name="directDeliveryTimeStart" id="directDeliveryTimeStart" value="${good.directDeliveryTimeStart}">
                <input type="hidden" name="directDeliveryTimeEnd" id="directDeliveryTimeEnd" value="${good.directDeliveryTimeEnd}">
                <input type="hidden" name="commonDeliveryTimeStart" id="commonDeliveryTimeStart" value="${good.commonDeliveryTimeStart}">
                <input type="hidden" name="commonDeliveryTimeEnd" id="commonDeliveryTimeEnd" value="${good.commonDeliveryTimeEnd}">
                <ul class="lastResult">
                    <!-- 终端客户属性和区域 -->
                    <input type='hidden' name='terminalTraderName'
                           id='terminalTraderName' value=''>
                    <input type='hidden' name='terminalTraderId' id='terminalTraderId'
                           value=''>
                    <input type='hidden' name='terminalTraderType'
                           id='terminalTraderType' value=''>
                    <input type='hidden' name='salesArea' id='salesArea' value=''>
                    <input type='hidden' name='salesAreaId' id='salesAreaId' value=''>
                    <input type='hidden' name='dwhTerminalId' id='dwhTerminalId' value=''>
                    <input type='hidden' name='unifiedSocialCreditIdentifier' id='unifiedSocialCreditIdentifier' value=''>
                    <input type='hidden' name='organizationCode' id='organizationCode' value=''>
                    <input type="hidden" name="provinceId" id="provinceId" value="0"/>
                    <input type="hidden" name="cityId" id="cityId" value="0"/>
                    <input type="hidden" name="areaId" id="areaId" value="0"/>
                    <input type="hidden" name="provinceName" id="provinceName" value=""/>
                    <input type="hidden" name="cityName" id="cityName" value="" />
                    <input type="hidden" name="areaName" id="areaName" value=""/>
                    <li>
                        <div class="infor_name ">
                            <span>*</span> <label>产品名称</label>
                        </div>
                        <div class="f_left table-largest content1">
                            <div class="">
                                <c:if test="${priceInfoMap.contractedGoodsFlag eq 1}">
                                    <span class="contractedGoodsFlag" style="color:red;">【协议商品】</span>
                                </c:if>
                                <img id="isGift" src="<%= basePath %>static/images/gift_icon.svg" style="width: 17px;display: none">
                                <a href="javascript:void(0);"
                                   class="font-blue mr10 productname2 addtitle2"
                                   id="confirmGoodsName" tabTitle='{"num":"viewgoods"${good.goodsId},"link":"./goods/goods/viewbaseinfo.do?goodsId=${good.goodsId}","title":"产品信息"}'>${good.goodsName}</a> <span
                                    class="bt-bg-style bt-small bg-light-blue searchAgain"
                                    onclick="againSearch('${saleorderId}','${scene}');">重新搜索</span>

                            </div>
                            <span style="color: red">单价0元会自动转化为赠品。</span>
                        </div>
                    </li>
                    <li>
                        <div class="infor_name mt0">
                            <label>品牌/型号</label>
                        </div>
                        <div class="f_left" id="confirmGoodsBrandNameModel">
                            ${good.brandName}/${good.model}
                        </div>
                    </li>
                    <li>
                        <div class="infor_name mt0">
                            <label>产品信息</label>
                        </div>
                        <div class="f_left" id="confirmGoodsContent">
                            <c:choose>
                                <c:when test="${good.verifyStatus eq 0}">
                                    <c:set var="verifyStatus" value="待完善"></c:set>
                                </c:when>
                                <c:when test="${good.verifyStatus eq 1}">
                                    <c:set var="verifyStatus" value="审核中"></c:set>
                                </c:when>
                                <c:when test="${good.verifyStatus eq 2}">
                                    <c:set var="verifyStatus" value="审核不通过"></c:set>
                                </c:when>
                                <c:when test="${good.verifyStatus eq 3}">
                                    <c:set var="verifyStatus" value="审核通过"></c:set>
                                </c:when>
                                <c:otherwise>
                                    <c:set var="verifyStatus" value="待审核"></c:set>
                                </c:otherwise>
                            </c:choose>
                            ${good.sku}/${good.goodsLevelName}/${verifyStatus}/产品归属&nbsp${good.proUserName}
                        </div>
                    </li>
                    <li>
                        <div class="infor_name mt0">
                            <span>*</span> <label>单位</label>
                        </div>
                        <div class="f_left" id="confirmUnitName">${good.unitName}</div>
                    </li>
                    <li>
                        <div class="infor_name ">
                            <label>单价</label>
                        </div>
                        <div class="f_left">
                            <input type="text" class="input-middle mr5" name="price"
                                   id="price" onkeyup="confirmTotalMoney('price');">
                            <span class="mr20">元</span>
                            <label>核价参考价格:</label>
                            <span id="priceShowSpan"></span>
                            <c:if test="${priceInfoMap.contractedGoodsFlag eq 1}">
                                <div class="contractedGoodsFlag" >
                                    <span class="font-grey9 mt5" style="color:red;">协议商品，默认带出协议价</span>
                                </div>
                            </c:if>
                        </div>

                    </li>
                    <li>
                        <div class="infor_name ">
                            <span>*</span> <label>数量</label>
                        </div>
                        <div class="f_left">
                            <input type="text" class="input-middle" name="num" id="num"
                                   onkeyup="confirmTotalMoney('num');">
                        </div>
                    </li>

                    <li>
                        <div class="infor_name mt0">
                            <label>总额</label>
                        </div>
                        <div class="f_left" id="confirmTotalMoney"></div>
                    </li>
                    <li>
                        <div class="infor_name mt0">
                            <label>报价含安调</label>
                        </div>
                        <div class="f_left inputfloat inputfloatmb0">
                            <ul>
                                <li><input type="radio" name="installation" value="1">
                                    <label>是</label></li>
                                <li><input type="radio" name="installation" value="0"
                                           checked> <label>否</label></li>
                            </ul>
                            <input type="hidden" name="haveInstallation"
                                   id="haveInstallation" />
                        </div>
                    </li>
                    <li>
                        <div class="infor_name ">
                            <label>货期</label>
                        </div>
                        <div class="f_left">
                            <input type="text" class="input-middle mr5" name="deliveryCycle" id="deliveryCycle">
                            <c:choose>
                                <c:when test="${not empty good.commonDeliveryTimeStart  && not empty good.commonDeliveryTimeEnd}">
                                    <c:if test="${good.commonDeliveryTimeStart ne good.commonDeliveryTimeEnd}">
                                        <span class="font-grey9 mt4" id="deliveryCycleFlag">参考：近90天平均货期${good.commonDeliveryTimeStart}-${good.commonDeliveryTimeEnd}天</span>
                                    </c:if>
                                    <c:if test="${good.commonDeliveryTimeStart eq good.commonDeliveryTimeEnd}">
                                        <span class="font-grey9 mt4" id="deliveryCycleFlag">参考：近90天平均货期${good.commonDeliveryTimeStart}天</span>
                                    </c:if>
                                </c:when>
                                <c:otherwise>
                                    <span class="font-grey9 mt4" id="deliveryCycleFlag">近90天无成交，货期请咨询相关人员</span>
                                </c:otherwise>
                            </c:choose>
                            <div id="deliveryCycleDiv"></div>
                        </div>
                    </li>
                    <li>
                        <div class="infor_name">
                            <label>是否直发</label>
                        </div>
                        <div class="f_left inputfloat inputfloatmb0">
                            <ul>
                                <li class="mt4">
                                    <input type="radio" name="deliveryDirectRad" value="0" <c:if test="${good.isDirect==0}">checked</c:if> onclick="showDeliveryCycle(this, 0);">
                                    <label>否</label>
                                </li>
                                <li class="mt4">
                                    <input type="radio" name="deliveryDirectRad" value="1" <c:if test="${good.isDirect==1}">checked</c:if>  onclick="showDeliveryCycle(this, 1);">
                                    <label>是</label>
                                </li>
                                <li><input type="text"
                                           placeholder="请填写直发原因，含有直发商品的订单不允许提前采购" class="input-largest"
                                           name="deliveryDirectComments" id="deliveryDirectComments"></li>
                            </ul>
                            <div id="deliveryDirectCommentsDiv"></div>
                            <input type="hidden" name="deliveryDirect" id="deliveryDirect">
                        </div>
                    </li>
                    <li>
                        <div class="infor_name ">
                            <label>内部备注</label>
                        </div>
                        <div class="customername pos_rel f_left">
                            <textarea class="input-largest textarea-smallest" placeholder="内部备注不对外显示，最多支持512个字符。" name="insideComments"
                                      id="insideComments" label_data="" scene="${scene}"></textarea>
                            <i class="iconbluemouth contorlIcon" style="display: none;"></i>
                            <div class="pos_abs customernameshow" label_left="200" style="width: 400px;left: 200px; top: 25px;background-color: #00CD66;">
                            </div>
                        </div>
                    </li>
                    <li>
                        <div class="infor_name ">
                            <label>产品备注</label>
                        </div>
                        <div class="f_left">
                            <input type="text" class="input-largest mr5"
                                   placeholder="产品备注对外显示" name="goodsComments" id="goodsComments"
                                   maxlength="60">
                            <div>
                                <span class="font-grey9 mt5" style="color:red;">*友情提示：若果您的操作导致单据总额发生变化，需要重新编辑付款计划！</span>
                            </div>
                        </div>
                    </li>
                </ul>
                <div class="add-tijiao  tcenter">
                    <button class="bt-bg-style bg-deep-green" type="button"
                            onclick="confirmSubmit();">提交</button>
                    <button id="close-layer" type="button" class="dele">取消</button>
                </div>
            </form>
        </div>
        <!-- ------------选择列表产品后操作--------end------- -->
</div>


