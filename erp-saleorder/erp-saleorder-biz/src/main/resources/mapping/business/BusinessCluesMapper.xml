<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.business.dao.BusinessCluesMapper">

    <resultMap id="BaseResultMap" type="com.vedeng.erp.business.domain.entity.BusinessClues">
            <id property="businessCluesId" column="BUSINESS_CLUES_ID" jdbcType="INTEGER"/>
            <result property="businessCluesNo" column="BUSINESS_CLUES_NO" jdbcType="VARCHAR"/>
            <result property="traderId" column="TRADER_ID" jdbcType="INTEGER"/>
            <result property="cluesLabel" column="CLUES_LABEL" jdbcType="VARCHAR"/>
            <result property="zhongbiaoCount" column="ZHONGBIAO_COUNT" jdbcType="INTEGER"/>
            <result property="tracSuggestion" column="TRAC_SUGGESTION" jdbcType="VARCHAR"/>
            <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
            <result property="businessChanceId" column="BUSINESS_CHANCE_ID" jdbcType="INTEGER"/>
            <result property="top" column="TOP" jdbcType="TINYINT"/>
            <result property="addTime" column="ADD_TIME" jdbcType="BIGINT"/>
            <result property="groupId" column="GROUP_ID" jdbcType="INTEGER"/>
            <result property="worth" column="WORTH" jdbcType="TINYINT"/>
            <result property="comment" column="COMMENT" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        BUSINESS_CLUES_ID,BUSINESS_CLUES_NO,TRADER_ID,
        CLUES_LABEL,ZHONGBIAO_COUNT,TRAC_SUGGESTION,
        REMARK,BUSINESS_CHANCE_ID,TOP,
        ADD_TIME,GROUP_ID,WORTH,COMMENT
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from T_BUSINESS_CLUES
        where  BUSINESS_CLUES_ID = #{businessCluesId,jdbcType=INTEGER} 
    </select>
    <select id="getBusinessClueslistpage" parameterType="Map" resultType="com.vedeng.erp.business.domain.vo.BusinessCluesVo">
       SELECT
            BC.BUSINESS_CLUES_ID,
            BC.BUSINESS_CLUES_NO,
            BC.GROUP_NAME,
            BC.TRADER_ID,
            BC.ZHONGBIAO_COUNT,
            BC.TRAC_SUGGESTION,
            BC.BUSINESS_CHANCE_ID,
            BC.ADD_TIME,
            T.TRADER_NAME,
            U.USERNAME AS belongSaleName,
            T.BELONG_PLATFORM,
            T.AREA_IDS,
            BCH.BUSSINESS_CHANCE_NO  AS businessChanceNo,
            BC.REMARK,
            BC.TOP,
            BC.GROUP_ID
        FROM
            T_BUSINESS_CLUES BC
            LEFT JOIN T_TRADER T ON T.TRADER_ID = BC.TRADER_ID
            LEFT JOIN T_R_TRADER_J_USER TU ON T.TRADER_ID = TU.TRADER_ID
            LEFT JOIN T_USER U ON TU.USER_ID = U.USER_ID
            LEFT JOIN T_BUSSINESS_CHANCE BCH ON BC.BUSINESS_CHANCE_ID = BCH.BUSSINESS_CHANCE_ID
            LEFT JOIN T_COMMUNICATE_RECORD CR ON BC.BUSINESS_CLUES_ID = CR.RELATED_ID AND CR.COMMUNICATE_TYPE = 4083
        WHERE
            TU.USER_ID in
            <foreach collection="businessCluesVo.userIds" open="(" close=")" separator="," item="userId">
                #{userId,jdbcType=INTEGER}
            </foreach>
            <if test="businessCluesVo.businessCluesNo != null and businessCluesVo.businessCluesNo != '' " >
                and BC.BUSINESS_CLUES_NO like CONCAT('%',#{businessCluesVo.businessCluesNo},'%' )
            </if>
            <if test="businessCluesVo.traderName != null and businessCluesVo.traderName != '' " >
                and T.TRADER_NAME like CONCAT('%',#{businessCluesVo.traderName},'%' )
            </if>
            <if test="businessCluesVo.groupName != null and businessCluesVo.groupName != '' " >
                and BC.GROUP_NAME like CONCAT('%',#{businessCluesVo.groupName},'%' )
            </if>
            <if test="businessCluesVo.startTime != null and businessCluesVo.startTime != 0">
                and BC.ADD_TIME <![CDATA[>=]]> #{businessCluesVo.startTime}
            </if>
            <if test="businessCluesVo.endTime != null and businessCluesVo.endTime != 0">
                and BC.ADD_TIME <![CDATA[<=]]> #{businessCluesVo.endTime}
            </if>
            <if test="businessCluesVo.businessChanceNo != null and businessCluesVo.businessChanceNo != '' " >
                and BCH.BUSSINESS_CHANCE_NO like CONCAT('%',#{businessCluesVo.businessChanceNo},'%' )
            </if>
            <if test="businessCluesVo.belongSale != null and businessCluesVo.belongSale != 0 " >
                and U.USER_ID = #{businessCluesVo.belongSale}
            </if>
            <if test="businessCluesVo.belongPlatform != null and businessCluesVo.belongPlatform != 0 " >
                and T.BELONG_PLATFORM = #{businessCluesVo.belongPlatform}
            </if>
            <if test="businessCluesVo.areaId != null" >
                and FIND_IN_SET(#{businessCluesVo.areaId},T.AREA_IDS)
            </if>
            <if test="businessCluesVo.worth != null and businessCluesVo.worth != 99" >
                and BC.WORTH = #{businessCluesVo.worth}
            </if>
            <if test="businessCluesVo.worth == 99" >
                and BC.WORTH IS NULL
            </if>
            <if test="businessCluesVo.isCommunicate == 1" >
                and CR.COMMUNICATE_RECORD_ID IS NOT NULL
            </if>
            <if test="businessCluesVo.isCommunicate == 2" >
                and CR.COMMUNICATE_RECORD_ID IS NULL
            </if>
            GROUP BY BC.BUSINESS_CLUES_ID
            ORDER BY TOP DESC, TOP_TIME DESC, ADD_TIME DESC
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from T_BUSINESS_CLUES
        where  BUSINESS_CLUES_ID = #{businessCluesId,jdbcType=INTEGER} 
    </delete>
    <insert id="insert" keyColumn="BUSINESS_CLUES_ID" keyProperty="businessCluesId" parameterType="com.vedeng.erp.business.domain.entity.BusinessClues" useGeneratedKeys="true">
        insert into T_BUSINESS_CLUES
        ( BUSINESS_CLUES_ID,BUSINESS_CLUES_NO,TRADER_ID
        ,CLUES_LABEL,ZHONGBIAO_COUNT,TRAC_SUGGESTION
        ,REMARK,BUSINESS_CHANCE_ID,TOP
        ,ADD_TIME,GROUP_ID)
        values (#{businessCluesId,jdbcType=INTEGER},#{businessCluesNo,jdbcType=VARCHAR},#{traderId,jdbcType=INTEGER}
        ,#{cluesLabel,jdbcType=VARCHAR},#{zhongbiaoCount,jdbcType=INTEGER},#{tracSuggestion,jdbcType=VARCHAR}
        ,#{remark,jdbcType=VARCHAR},#{businessChanceId,jdbcType=INTEGER},#{top,jdbcType=TINYINT}
        ,#{addTime,jdbcType=BIGINT},#{groupId,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" keyColumn="BUSINESS_CLUES_ID" keyProperty="businessCluesId" parameterType="com.vedeng.erp.business.domain.entity.BusinessClues" useGeneratedKeys="true">
        insert into T_BUSINESS_CLUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="businessCluesId != null">BUSINESS_CLUES_ID,</if>
                <if test="businessCluesNo != null">BUSINESS_CLUES_NO,</if>
                <if test="traderId != null">TRADER_ID,</if>
                <if test="cluesLabel != null">CLUES_LABEL,</if>
                <if test="zhongbiaoCount != null">ZHONGBIAO_COUNT,</if>
                <if test="tracSuggestion != null">TRAC_SUGGESTION,</if>
                <if test="remark != null">REMARK,</if>
                <if test="businessChanceId != null">BUSINESS_CHANCE_ID,</if>
                <if test="top != null">TOP,</if>
                <if test="addTime != null">ADD_TIME,</if>
                <if test="groupId != null">GROUP_ID,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="businessCluesId != null">#{businessCluesId,jdbcType=INTEGER},</if>
                <if test="businessCluesNo != null">#{businessCluesNo,jdbcType=VARCHAR},</if>
                <if test="traderId != null">#{traderId,jdbcType=INTEGER},</if>
                <if test="cluesLabel != null">#{cluesLabel,jdbcType=VARCHAR},</if>
                <if test="zhongbiaoCount != null">#{zhongbiaoCount,jdbcType=INTEGER},</if>
                <if test="tracSuggestion != null">#{tracSuggestion,jdbcType=VARCHAR},</if>
                <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
                <if test="businessChanceId != null">#{businessChanceId,jdbcType=INTEGER},</if>
                <if test="top != null">#{top,jdbcType=TINYINT},</if>
                <if test="addTime != null">#{addTime,jdbcType=BIGINT},</if>
                <if test="groupId != null">#{groupId,jdbcType=INTEGER},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.business.domain.entity.BusinessClues">
        update T_BUSINESS_CLUES
        <set>
                <if test="businessCluesNo != null">
                    BUSINESS_CLUES_NO = #{businessCluesNo,jdbcType=VARCHAR},
                </if>
                <if test="traderId != null">
                    TRADER_ID = #{traderId,jdbcType=INTEGER},
                </if>
                <if test="cluesLabel != null">
                    CLUES_LABEL = #{cluesLabel,jdbcType=VARCHAR},
                </if>
                <if test="zhongbiaoCount != null">
                    ZHONGBIAO_COUNT = #{zhongbiaoCount,jdbcType=INTEGER},
                </if>
                <if test="tracSuggestion != null">
                    TRAC_SUGGESTION = #{tracSuggestion,jdbcType=VARCHAR},
                </if>
                <if test="remark != null">
                    REMARK = #{remark,jdbcType=VARCHAR},
                </if>
                <if test="businessChanceId != null">
                    BUSINESS_CHANCE_ID = #{businessChanceId,jdbcType=INTEGER},
                </if>
                <if test="top != null">
                    TOP = #{top,jdbcType=TINYINT},
                </if>
                <if test="addTime != null">
                    ADD_TIME = #{addTime,jdbcType=BIGINT},
                </if>
                <if test="groupId != null">
                    GROUP_ID = #{groupId,jdbcType=INTEGER},
                </if>
                <if test="topTime != null">
                    TOP_TIME = #{topTime,jdbcType=BIGINT},
                </if>
                <if test="worth != null">
                    WORTH = #{worth,jdbcType=INTEGER},
                </if>
                <if test="comment != null and comment != ''">
                    COMMENT = #{comment,jdbcType=VARCHAR},
                </if>
        </set>
        where   BUSINESS_CLUES_ID = #{businessCluesId,jdbcType=INTEGER} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.business.domain.entity.BusinessClues">
        update T_BUSINESS_CLUES
        set 
            BUSINESS_CLUES_NO =  #{businessCluesNo,jdbcType=VARCHAR},
            TRADER_ID =  #{traderId,jdbcType=INTEGER},
            GROUP_ID =  #{groupId,jdbcType=INTEGER},
            CLUES_LABEL =  #{cluesLabel,jdbcType=VARCHAR},
            ZHONGBIAO_COUNT =  #{zhongbiaoCount,jdbcType=INTEGER},
            TRAC_SUGGESTION =  #{tracSuggestion,jdbcType=VARCHAR},
            REMARK =  #{remark,jdbcType=VARCHAR},
            BUSINESS_CHANCE_ID =  #{businessChanceId,jdbcType=INTEGER},
            TOP =  #{top,jdbcType=TINYINT},
            ADD_TIME =  #{addTime,jdbcType=BIGINT}
        where   BUSINESS_CLUES_ID = #{businessCluesId,jdbcType=INTEGER} 
    </update>
</mapper>
