package com.vedeng.erp.saleorder.constant;

/**
 * <AUTHOR>
 */

public enum PaymentTypeEnum {

    /**
     * 先款后货，预付100%
     */
    PAY_IN_ADVANCE_100(419,"先款后货，预付100%"),

    /**
     * 先货后款，预付80%
     */
    GOODS_IN_ADVANCE_80(420,"先货后款，预付80%"),

    /**
     * 先货后款，预付50%
     */
    GOODS_IN_ADVANCE_50(421, "先货后款，预付50%"),

    /**
     * 先货后款，预付30%
     */
    GOODS_IN_ADVANCE_30(422, "先货后款，预付30%"),

    /**
     * 先货后款，预付0%
     */
    GOODS_IN_ADVANCE_0(423, "先货后款，预付0%"),

    /**
     * 待收款
     */
    CUSTOMIZE(424, "自定义"),
    ;

    private Integer type;

    private String desc;

    PaymentTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
