<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.temporal.mapper.FlowNodeForSaleMapper">

    <resultMap id="FlowNodeDtoMap" type="com.vedeng.temporal.domain.dto.FlowNodeDto">
        <id column="FLOW_NODE_ID" property="flowNodeId"/>
        <result column="FLOW_ORDER_ID" property="flowOrderId"/>
        <result column="OPEN_INVOICE" property="openInvoice"/>
        <result column="NODE_LEVEL" property="nodeLevel"/>
        <result column="PAYMENT_METHOD" property="paymentMethod"/>
        <result column="INVOICE_TYPE" property="invoiceType"/>
        <result column="AMOUNT" property="amount"/>
        <result column="CREDIT_PAYMENT" property="creditPayment"/>
        <result column="BALANCE" property="balance"/>
        <result column="BALANCE_DUE_DATE" property="balanceDueDate"/>
        <result column="TRADER_ID" property="traderId"/>
        <result column="TRADER_NAME" property="traderName"/>
        <result column="TRADER_CONTACT_ID" property="traderContactId"/>
        <result column="TRADER_CONTACT_NAME" property="traderContactName"/>
        <result column="TRADER_CONTACT_PHONE" property="traderContactPhone"/>
        <result column="TRADER_ADDRESS_ID" property="traderAddressId"/>
        <result column="TRADER_CONTACT_ADDRESS" property="traderContactAddress"/>
        <result column="RECEIVER_TRADER_CONTACT_ID" property="receiverTraderContactId"/>
        <result column="RECEIVER_NAME" property="receiverName"/>
        <result column="RECEIVER_PHONE" property="receiverPhone"/>
        <result column="RECEIVER_ADDRESS_ID" property="receiverAddressId"/>
        <result column="RECEIVER_ADDRESS" property="receiverAddress"/>
        <result column="INVOICE_RECEIVER_TRADER_CONTACT_ID" property="invoiceReceiverTraderContactId"/>
        <result column="INVOICE_RECEIVER_NAME" property="invoiceReceiverName"/>
        <result column="INVOICE_RECEIVER_PHONE" property="invoiceReceiverPhone"/>
        <result column="INVOICE_RECEIVER_ADDRESS_ID" property="invoiceReceiverAddressId"/>
        <result column="INVOICE_RECEIVER_ADDRESS" property="invoiceReceiverAddress"/>
        <result column="IS_DELETE" property="isDelete"/>
        <result column="CREATOR" property="creator"/>
        <result column="UPDATER" property="updater"/>
        <result column="CREATOR_NAME" property="creatorName"/>
        <result column="UPDATER_NAME" property="updaterName"/>
        <result column="ADD_TIME" property="addTime"/>
        <result column="MOD_TIME" property="modTime"/>
    </resultMap>

    <select id="selectNodesByFlowOrderId" resultMap="FlowNodeDtoMap">
        SELECT *
        FROM T_FLOW_NODE
        WHERE FLOW_ORDER_ID = #{flowOrderId} AND TRADER_NAME = #{traderName,jdbcType=VARCHAR}
          AND IS_DELETE = 0

        LIMIT 1
    </select>
</mapper> 