package com.vedeng.kpi.service;

import com.vedeng.kpi.model.DTO.KpiDataQueryDto;
import com.vedeng.kpi.model.DTO.KpiUserInfoDto;

import java.util.List;

/**
 * 五行bd客户service.
 * @jira: VDERP-2376【五行剑法】规则修改.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/6/9 10:46 上午.
 * @author: Tomcat.Hui.
 */
public interface KpiBdCustomerService {

    /**
     * 获取BD流失客户数list.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: 页面明细展示.
     * @version: 1.0.
     * @date: 2020/7/6 11:10 上午.
     * @author: Tomcat.Hui.
     * @param query: .
     * @return: java.util.List<com.vedeng.kpi.model.DTO.KpiDataQueryDto>.
     * @throws: .
     */
    List<KpiDataQueryDto> getKpiBdLostDetail(KpiDataQueryDto query);

    /**
     * 获取BD客户是否流失明细.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/6 11:10 上午.
     * @author: Tomcat.Hui.
     * @param query: .
     * @return: java.util.List<com.vedeng.kpi.model.DTO.KpiDataQueryDto>.
     * @throws: .
     */
    List<KpiDataQueryDto> getKpiBdCoDetail(KpiDataQueryDto query);
}
