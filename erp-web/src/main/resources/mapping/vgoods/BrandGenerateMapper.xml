<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.dao.BrandGenerateMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.goods.model.BrandGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <id column="BRAND_ID" jdbcType="INTEGER" property="brandId" />
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
    <result column="BRAND_NATURE" jdbcType="TINYINT" property="brandNature" />
    <result column="BRAND_NAME" jdbcType="VARCHAR" property="brandName" />
    <result column="BRAND_NAME_CN" jdbcType="VARCHAR" property="brandNameCn" />
    <result column="BRAND_NAME_EN" jdbcType="VARCHAR" property="brandNameEn" />
    <result column="MANUFACTURER" jdbcType="VARCHAR" property="manufacturer" />
    <result column="BRAND_WEBSITE" jdbcType="VARCHAR" property="brandWebsite" />
    <result column="OWNER" jdbcType="VARCHAR" property="owner" />
    <result column="LOGO_DOMAIN" jdbcType="VARCHAR" property="logoDomain" />
    <result column="LOGO_URI" jdbcType="VARCHAR" property="logoUri" />
    <result column="SORT" jdbcType="INTEGER" property="sort" />
    <result column="INITIAL_CN" jdbcType="VARCHAR" property="initialCn" />
    <result column="INITIAL_EN" jdbcType="VARCHAR" property="initialEn" />
    <result column="SOURCE" jdbcType="TINYINT" property="source" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="IS_DELETE" jdbcType="TINYINT" property="isDelete" />
    <result column="COMMENTS" jdbcType="VARCHAR" property="comments" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.vedeng.goods.model.BrandGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <result column="DESCRIPTION" jdbcType="LONGVARCHAR" property="description" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    BRAND_ID, COMPANY_ID, BRAND_NATURE, BRAND_NAME, BRAND_NAME_CN, BRAND_NAME_EN, MANUFACTURER, BRAND_WEBSITE,
    `OWNER`, LOGO_DOMAIN, LOGO_URI, SORT, INITIAL_CN, INITIAL_EN, `SOURCE`, ADD_TIME, 
    CREATOR, MOD_TIME, UPDATER, IS_DELETE, COMMENTS
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    DESCRIPTION
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.vedeng.goods.model.BrandGenerateExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from T_BRAND
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.vedeng.goods.model.BrandGenerateExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from T_BRAND
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from T_BRAND
    where BRAND_ID = #{brandId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    delete from T_BRAND
    where BRAND_ID = #{brandId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.goods.model.BrandGenerateExample">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    delete from T_BRAND
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.goods.model.BrandGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <selectKey keyProperty="brandId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_BRAND (COMPANY_ID, BRAND_NATURE, BRAND_NAME, 
      BRAND_NAME_EN, MANUFACTURER, BRAND_WEBSITE, 
      `OWNER`, LOGO_DOMAIN, LOGO_URI, 
      SORT, INITIAL_CN, INITIAL_EN, 
      `SOURCE`, ADD_TIME, CREATOR, 
      MOD_TIME, UPDATER, IS_DELETE, 
      COMMENTS, DESCRIPTION)
    values (#{companyId,jdbcType=INTEGER}, #{brandNature,jdbcType=TINYINT}, #{brandName,jdbcType=VARCHAR}, 
      #{brandNameEn,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR}, #{brandWebsite,jdbcType=VARCHAR}, 
      #{owner,jdbcType=VARCHAR}, #{logoDomain,jdbcType=VARCHAR}, #{logoUri,jdbcType=VARCHAR}, 
      #{sort,jdbcType=INTEGER}, #{initialCn,jdbcType=VARCHAR}, #{initialEn,jdbcType=VARCHAR}, 
      #{source,jdbcType=TINYINT}, #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, 
      #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}, #{isDelete,jdbcType=TINYINT}, 
      #{comments,jdbcType=VARCHAR}, #{description,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.goods.model.BrandGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <selectKey keyProperty="brandId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_BRAND
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="brandNature != null">
        BRAND_NATURE,
      </if>
      <if test="brandName != null">
        BRAND_NAME,
      </if>
      <if test="brandNameEn != null">
        BRAND_NAME_EN,
      </if>
      <if test="manufacturer != null">
        MANUFACTURER,
      </if>
      <if test="brandWebsite != null">
        BRAND_WEBSITE,
      </if>
      <if test="owner != null">
        `OWNER`,
      </if>
      <if test="logoDomain != null">
        LOGO_DOMAIN,
      </if>
      <if test="logoUri != null">
        LOGO_URI,
      </if>
      <if test="sort != null">
        SORT,
      </if>
      <if test="initialCn != null">
        INITIAL_CN,
      </if>
      <if test="initialEn != null">
        INITIAL_EN,
      </if>
      <if test="source != null">
        `SOURCE`,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="comments != null">
        COMMENTS,
      </if>
      <if test="description != null">
        DESCRIPTION,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="brandNature != null">
        #{brandNature,jdbcType=TINYINT},
      </if>
      <if test="brandName != null">
        #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="brandNameEn != null">
        #{brandNameEn,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="brandWebsite != null">
        #{brandWebsite,jdbcType=VARCHAR},
      </if>
      <if test="owner != null">
        #{owner,jdbcType=VARCHAR},
      </if>
      <if test="logoDomain != null">
        #{logoDomain,jdbcType=VARCHAR},
      </if>
      <if test="logoUri != null">
        #{logoUri,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="initialCn != null">
        #{initialCn,jdbcType=VARCHAR},
      </if>
      <if test="initialEn != null">
        #{initialEn,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.goods.model.BrandGenerateExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    select count(*) from T_BRAND
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update T_BRAND
    <set>
      <if test="record.brandId != null">
        BRAND_ID = #{record.brandId,jdbcType=INTEGER},
      </if>
      <if test="record.companyId != null">
        COMPANY_ID = #{record.companyId,jdbcType=INTEGER},
      </if>
      <if test="record.brandNature != null">
        BRAND_NATURE = #{record.brandNature,jdbcType=TINYINT},
      </if>
      <if test="record.brandName != null">
        BRAND_NAME = #{record.brandName,jdbcType=VARCHAR},
      </if>
      <if test="record.brandNameEn != null">
        BRAND_NAME_EN = #{record.brandNameEn,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        MANUFACTURER = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.brandWebsite != null">
        BRAND_WEBSITE = #{record.brandWebsite,jdbcType=VARCHAR},
      </if>
      <if test="record.owner != null">
        `OWNER` = #{record.owner,jdbcType=VARCHAR},
      </if>
      <if test="record.logoDomain != null">
        LOGO_DOMAIN = #{record.logoDomain,jdbcType=VARCHAR},
      </if>
      <if test="record.logoUri != null">
        LOGO_URI = #{record.logoUri,jdbcType=VARCHAR},
      </if>
      <if test="record.sort != null">
        SORT = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.initialCn != null">
        INITIAL_CN = #{record.initialCn,jdbcType=VARCHAR},
      </if>
      <if test="record.initialEn != null">
        INITIAL_EN = #{record.initialEn,jdbcType=VARCHAR},
      </if>
      <if test="record.source != null">
        `SOURCE` = #{record.source,jdbcType=TINYINT},
      </if>
      <if test="record.addTime != null">
        ADD_TIME = #{record.addTime,jdbcType=BIGINT},
      </if>
      <if test="record.creator != null">
        CREATOR = #{record.creator,jdbcType=INTEGER},
      </if>
      <if test="record.modTime != null">
        MOD_TIME = #{record.modTime,jdbcType=BIGINT},
      </if>
      <if test="record.updater != null">
        UPDATER = #{record.updater,jdbcType=INTEGER},
      </if>
      <if test="record.isDelete != null">
        IS_DELETE = #{record.isDelete,jdbcType=TINYINT},
      </if>
      <if test="record.comments != null">
        COMMENTS = #{record.comments,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        DESCRIPTION = #{record.description,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update T_BRAND
    set BRAND_ID = #{record.brandId,jdbcType=INTEGER},
      COMPANY_ID = #{record.companyId,jdbcType=INTEGER},
      BRAND_NATURE = #{record.brandNature,jdbcType=TINYINT},
      BRAND_NAME = #{record.brandName,jdbcType=VARCHAR},
      BRAND_NAME_EN = #{record.brandNameEn,jdbcType=VARCHAR},
      MANUFACTURER = #{record.manufacturer,jdbcType=VARCHAR},
      BRAND_WEBSITE = #{record.brandWebsite,jdbcType=VARCHAR},
      `OWNER` = #{record.owner,jdbcType=VARCHAR},
      LOGO_DOMAIN = #{record.logoDomain,jdbcType=VARCHAR},
      LOGO_URI = #{record.logoUri,jdbcType=VARCHAR},
      SORT = #{record.sort,jdbcType=INTEGER},
      INITIAL_CN = #{record.initialCn,jdbcType=VARCHAR},
      INITIAL_EN = #{record.initialEn,jdbcType=VARCHAR},
      `SOURCE` = #{record.source,jdbcType=TINYINT},
      ADD_TIME = #{record.addTime,jdbcType=BIGINT},
      CREATOR = #{record.creator,jdbcType=INTEGER},
      MOD_TIME = #{record.modTime,jdbcType=BIGINT},
      UPDATER = #{record.updater,jdbcType=INTEGER},
      IS_DELETE = #{record.isDelete,jdbcType=TINYINT},
      COMMENTS = #{record.comments,jdbcType=VARCHAR},
      DESCRIPTION = #{record.description,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update T_BRAND
    set BRAND_ID = #{record.brandId,jdbcType=INTEGER},
      COMPANY_ID = #{record.companyId,jdbcType=INTEGER},
      BRAND_NATURE = #{record.brandNature,jdbcType=TINYINT},
      BRAND_NAME = #{record.brandName,jdbcType=VARCHAR},
      BRAND_NAME_EN = #{record.brandNameEn,jdbcType=VARCHAR},
      MANUFACTURER = #{record.manufacturer,jdbcType=VARCHAR},
      BRAND_WEBSITE = #{record.brandWebsite,jdbcType=VARCHAR},
      `OWNER` = #{record.owner,jdbcType=VARCHAR},
      LOGO_DOMAIN = #{record.logoDomain,jdbcType=VARCHAR},
      LOGO_URI = #{record.logoUri,jdbcType=VARCHAR},
      SORT = #{record.sort,jdbcType=INTEGER},
      INITIAL_CN = #{record.initialCn,jdbcType=VARCHAR},
      INITIAL_EN = #{record.initialEn,jdbcType=VARCHAR},
      `SOURCE` = #{record.source,jdbcType=TINYINT},
      ADD_TIME = #{record.addTime,jdbcType=BIGINT},
      CREATOR = #{record.creator,jdbcType=INTEGER},
      MOD_TIME = #{record.modTime,jdbcType=BIGINT},
      UPDATER = #{record.updater,jdbcType=INTEGER},
      IS_DELETE = #{record.isDelete,jdbcType=TINYINT},
      COMMENTS = #{record.comments,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.model.BrandGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update T_BRAND
    <set>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="brandNature != null">
        BRAND_NATURE = #{brandNature,jdbcType=TINYINT},
      </if>
      <if test="brandName != null">
        BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="brandNameEn != null">
        BRAND_NAME_EN = #{brandNameEn,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        MANUFACTURER = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="brandWebsite != null">
        BRAND_WEBSITE = #{brandWebsite,jdbcType=VARCHAR},
      </if>
      <if test="owner != null">
        `OWNER` = #{owner,jdbcType=VARCHAR},
      </if>
      <if test="logoDomain != null">
        LOGO_DOMAIN = #{logoDomain,jdbcType=VARCHAR},
      </if>
      <if test="logoUri != null">
        LOGO_URI = #{logoUri,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        SORT = #{sort,jdbcType=INTEGER},
      </if>
      <if test="initialCn != null">
        INITIAL_CN = #{initialCn,jdbcType=VARCHAR},
      </if>
      <if test="initialEn != null">
        INITIAL_EN = #{initialEn,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        `SOURCE` = #{source,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="comments != null">
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        DESCRIPTION = #{description,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where BRAND_ID = #{brandId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.vedeng.goods.model.BrandGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update T_BRAND
    set COMPANY_ID = #{companyId,jdbcType=INTEGER},
      BRAND_NATURE = #{brandNature,jdbcType=TINYINT},
      BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      BRAND_NAME_EN = #{brandNameEn,jdbcType=VARCHAR},
      MANUFACTURER = #{manufacturer,jdbcType=VARCHAR},
      BRAND_WEBSITE = #{brandWebsite,jdbcType=VARCHAR},
      `OWNER` = #{owner,jdbcType=VARCHAR},
      LOGO_DOMAIN = #{logoDomain,jdbcType=VARCHAR},
      LOGO_URI = #{logoUri,jdbcType=VARCHAR},
      SORT = #{sort,jdbcType=INTEGER},
      INITIAL_CN = #{initialCn,jdbcType=VARCHAR},
      INITIAL_EN = #{initialEn,jdbcType=VARCHAR},
      `SOURCE` = #{source,jdbcType=TINYINT},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=TINYINT},
      COMMENTS = #{comments,jdbcType=VARCHAR},
      DESCRIPTION = #{description,jdbcType=LONGVARCHAR}
    where BRAND_ID = #{brandId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.model.BrandGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update T_BRAND
    set COMPANY_ID = #{companyId,jdbcType=INTEGER},
      BRAND_NATURE = #{brandNature,jdbcType=TINYINT},
      BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      BRAND_NAME_EN = #{brandNameEn,jdbcType=VARCHAR},
      MANUFACTURER = #{manufacturer,jdbcType=VARCHAR},
      BRAND_WEBSITE = #{brandWebsite,jdbcType=VARCHAR},
      `OWNER` = #{owner,jdbcType=VARCHAR},
      LOGO_DOMAIN = #{logoDomain,jdbcType=VARCHAR},
      LOGO_URI = #{logoUri,jdbcType=VARCHAR},
      SORT = #{sort,jdbcType=INTEGER},
      INITIAL_CN = #{initialCn,jdbcType=VARCHAR},
      INITIAL_EN = #{initialEn,jdbcType=VARCHAR},
      `SOURCE` = #{source,jdbcType=TINYINT},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=TINYINT},
      COMMENTS = #{comments,jdbcType=VARCHAR}
    where BRAND_ID = #{brandId,jdbcType=INTEGER}
  </update>

  <select id="getBrandInfoByParam" resultMap="BaseResultMap">
    select
    BRAND_ID as brandId,
    BRAND_NAME as brandName
    from
    T_BRAND
    where
    company_id=1

      AND IS_DELETE =0

  </select>
</mapper>