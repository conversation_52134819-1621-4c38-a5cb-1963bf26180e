package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.enums.OtherTypeConst;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInItemDto;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInItemDtoMapper;
import com.vedeng.erp.kingdee.domain.entity.LogDataKingDee;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInDto;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInResultDto;
import com.vedeng.erp.kingdee.repository.mappers.LogDataKingDeeMapper;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryExtParam;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryFilterDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @description 单位转换出库单历史数据
 * <AUTHOR>
 */
@Service
@Slf4j
public class BatchUnitConversionOutHistoryProcessor extends BaseProcessor<BatchWarehouseGoodsOutInDto, KingDeeStorageInDto> {
    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;
    @Autowired
    private LogDataKingDeeMapper logDataKingDeeMapper;
    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;

    @Override
    public KingDeeStorageInDto doProcess(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto, JobParameters params, ExecutionContext stepContext) throws Exception {

        log.info("BatchUnitConversionOutProcessor.process：{}", JSON.toJSONString(batchWarehouseGoodsOutInDto));
        if (!OtherTypeConst.UPDATE_REMARK_ORTHER_TYPE.equals(batchWarehouseGoodsOutInDto.getUpdateRemark())){
            return null;
        }
        log.info("查询是否推送金蝶出库单");
        KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
        queryParam.setFormId(KingDeeFormConstant.STK_MISCELLANEOUS);
        List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
        queryFilterDtos.add(KingDeeQueryFilterDto.builder().fieldName("fBillNo").compare("=").left("(").right(")").value(batchWarehouseGoodsOutInDto.getOutInNo()).logic("AND").build());
        queryParam.setFilterString(queryFilterDtos);
        List<KingDeeStorageInResultDto> query = kingDeeBaseApi.query(queryParam, KingDeeStorageInResultDto.class);
        log.info("单位转换出库单查询金蝶结果：{}", JSON.toJSONString(query));
        if (CollUtil.isNotEmpty(query)) {
            log.info("已推送金蝶数据，无需推送");
            return null;
        }

        KingDeeStorageInDto dto = new KingDeeStorageInDto();
        dto.setFId("0");
        dto.setFBillNo(batchWarehouseGoodsOutInDto.getOutInNo());
        dto.setFDate(DateUtil.formatDate(batchWarehouseGoodsOutInDto.getOutInTime()));
        dto.setFStockDirect("RETURN");
        dto.setFQzokBddjtId(batchWarehouseGoodsOutInDto.getWarehouseGoodsOutInId().toString());


        List<KingDeeStorageInDetailDto> detailDtoList = new ArrayList<>();
        List<BatchWarehouseGoodsOutInItemDto> byOutInNo = batchWarehouseGoodsOutInItemDtoMapper.findByOutInNo(batchWarehouseGoodsOutInDto.getOutInNo());
        if (CollUtil.isEmpty(byOutInNo)) {
            log.warn("未能查到出库单子单信息");
            return null;
        }
        batchWarehouseGoodsOutInDto.setBatchWarehouseGoodsOutInItemDtos(byOutInNo);
        batchWarehouseGoodsOutInDto.getBatchWarehouseGoodsOutInItemDtos().forEach(l -> {
            KingDeeStorageInDetailDto detailDto = new KingDeeStorageInDetailDto();
            detailDto.setFMaterialId("V" + l.getGoodsId());
            detailDto.setFStockId("CK9999");
            BigDecimal num = l.getNum().abs();
            detailDto.setFQty(num.toString());

            List<LogDataKingDee> logDataKingDee = logDataKingDeeMapper.findByOutInNoAndGoodsIdAndOrderType(l.getOutInNo(), l.getGoodsId(), "SKU转化出库");
            if (CollUtil.isEmpty(logDataKingDee)) {
                log.error("单位转换出库未能查到金额信息,BatchWarehouseGoodsOutInItemDto:{}", JSON.toJSONString(l));
                return;
            }
            BigDecimal realPrice = logDataKingDee.get(0).getRealPrice().abs();
            detailDto.setFPrice(realPrice.toString());
            detailDto.setFAmount(num.multiply(realPrice).setScale(2,RoundingMode.HALF_UP).toString());

            detailDto.setFStockId("CK9999");
            detailDto.setFQzokYsddh(batchWarehouseGoodsOutInDto.getRelateNo());
            detailDto.setFQzokGsywdh(batchWarehouseGoodsOutInDto.getRelateNo());
            detailDto.setFQzokYwlx("单位转换");
            detailDto.setFQzokPch(l.getBatchNumber());
            detailDto.setFQzokXlh(l.getBarcodeFactory());
            detailDto.setFQzokSfzf("false");
            detailDto.setFQzokBddjhId(l.getWarehouseGoodsOutInDetailId().toString());

            detailDtoList.add(detailDto);
        });

        dto.setFEntity(detailDtoList);
        return dto;
    }



}
