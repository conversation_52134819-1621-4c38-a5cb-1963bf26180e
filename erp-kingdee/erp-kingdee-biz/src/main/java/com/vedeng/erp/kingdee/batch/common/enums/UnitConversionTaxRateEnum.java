package com.vedeng.erp.kingdee.batch.common.enums;

import lombok.Getter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 库存转换单税率
 * @date 2023/3/1 10:53
 **/
@Getter
public enum UnitConversionTaxRateEnum {

    /**
     * 1
     */
    RATE0(1,BigDecimal.ZERO,"0%"),
    /**
     * 2
     */
    RATE1(2,new BigDecimal("0.01"),"1%"),

    /**
     * 3
     */
    RATE3(3,new BigDecimal("0.03"),"3%"),

    /**
     * 4
     */
    RATE5(4,new BigDecimal("0.05"),"5%"),

    /**
     * 5
     */
    RATE6(5,new BigDecimal("0.06"),"6%"),

    /**
     * 6
     */
    RATE9(6,new BigDecimal("0.09"),"9%"),

    /**
     * 7
     */
    RATE13(7,new BigDecimal("0.13"),"13%");

    /**
     * id
     */
    private final Integer id;

    /**
     * 税率
     */
    private final BigDecimal rate;

    /**
     * 描述
     */
    private final String desc;


    UnitConversionTaxRateEnum(Integer id, BigDecimal rate, String desc) {
        this.id = id;
        this.rate = rate;
        this.desc = desc;
    }

    /**
     * 根据id 获取税率
     * @param id 入参
     * @return BigDecimal
     */
    public static BigDecimal getRate(Integer id) {
        for (UnitConversionTaxRateEnum unitConversionTaxRateEnum : UnitConversionTaxRateEnum.values()) {
            if (unitConversionTaxRateEnum.getId().equals(id)) {
                return unitConversionTaxRateEnum.getRate();
            }
        }
        return BigDecimal.ZERO;
    }
}
