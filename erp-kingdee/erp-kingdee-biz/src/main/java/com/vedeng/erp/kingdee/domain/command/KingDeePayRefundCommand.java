package com.vedeng.erp.kingdee.domain.command;

import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/24 15:29
 * @version 1.0
 */
@Data
public class KingDeePayRefundCommand {

    /**
     * fid
     */
    private Integer fid;
    /**
     * fBillNo
     */
    private String fBillNo;
    /**
     * fcontactunittype
     */
    private String fcontactunittype;
    /**
     * fdate
     */
    private String fdate;
    /**
     * fcontactunit
     */
    private KingDeeNumberCommand fcontactunit = new KingDeeNumberCommand();
    /**
     * fbusinesstype
     */
    private String fbusinesstype;
    /**
     * fpayunittype
     */
    private String fpayunittype;
    /**
     * fsettleorgid
     */
    private KingDeeNumberCommand fsettleorgid = new KingDeeNumberCommand();
    /**
     * fpurchaseorgid
     */
    private KingDeeNumberCommand fpurchaseorgid = new KingDeeNumberCommand();
    /**
     * fpayorgid
     */
    private KingDeeNumberCommand fpayorgid = new KingDeeNumberCommand();
    /**
     * fQzokSfzk
     */
    private String F_QZOK_SFZK;
    /**
     * fQzokLsh
     */
    private String F_QZOK_LSH;
    /**
     * fQzokBddjtid
     */
    private String F_QZOK_BDDJTID;

    /**
     * VDERP-16089  "往来单位" 所选择资金流水的订单单号 F_QZOK_PZGSYWDH
     * T_CAPITAL_BILL_DETAIL.ORDER_NO（订单单号）
     */
    private String f_qzok_pzgsywdh;


    /**
     * frefundbillentry
     */
    private List<FREFUNDBILLENTRY> frefundbillentry;

    /**
     * FBillTypeID
     */
    private KingDeeNumberCommand FBillTypeID = new KingDeeNumberCommand();

    /**
     * FREFUNDBILLENTRY
     */
    @Data
    public static class FREFUNDBILLENTRY {
        /**
         * fsettletypeid
         */
        private KingDeeNumberCommand fsettletypeid = new KingDeeNumberCommand();
        /**
         * fpurposeid
         */
        private KingDeeNumberCommand fpurposeid = new KingDeeNumberCommand();
        /**
         * frefundamountfor
         */
        private BigDecimal frefundamountfor;
        /**
         * faccountid
         */
        private KingDeeNumberCommand faccountid = new KingDeeNumberCommand();
        /**
         * fhandlingchargefor
         */
        private BigDecimal fhandlingchargefor;
        /**
         * fQzokYsddh
         */
        private String F_QZOK_YSDDH;
        /**
         * fQzokGsywdh
         */
        private String F_QZOK_GSYWDH;
        /**
         * fQzokYwlx
         */
        private String F_QZOK_YWLX;
        /**
         * fQzokBddjhid
         */
        private String F_QZOK_BDDJHID;

    }
}