package com.vedeng.crm.config;

import com.vedeng.common.cat.config.CatFeignConfiguration;
import com.vedeng.common.cat.config.CatMybatisPlugin;
import com.vedeng.common.cat.filter.CatContextServletFilter;
import feign.RequestInterceptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;

/**
 * @Author: Patric.Cheng
 * @CreateTime: 2024-08-23
 * @Version: 1.0
 */
@Configuration
public class CatChainFilterConfig {
    private static final String CAT_EXCLUDE_URL_NAME = "exclude";
    /**
     * CAT监控放行URL的列表
     * 支持前缀*通配和具体URL，多个放行URL以英文分号间隔
     */
    @Value("${cat.excludeUrl}")
    private String catExcludeUrl;

    @Bean
    public FilterRegistrationBean catChainFilter() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        CatContextServletFilter filter = new CatContextServletFilter();
        // 放行静态资源，注意：CatFilter只支持前缀过滤和具体URL
        registration.addInitParameter(CAT_EXCLUDE_URL_NAME, catExcludeUrl);
        registration.setFilter(filter);
        registration.addUrlPatterns("/*");
        registration.setName("cat-chain-filter");
        registration.setOrder(Ordered.HIGHEST_PRECEDENCE + 1);
        return registration;
    }

    @Bean
    public CatMybatisPlugin catMybatisPlugin() {
        CatMybatisPlugin catMybatisPlugin = new CatMybatisPlugin();
        return catMybatisPlugin;
    }

    @Bean
    public RequestInterceptor requestInterceptor() {
        return new CatFeignConfiguration();
    }
}
