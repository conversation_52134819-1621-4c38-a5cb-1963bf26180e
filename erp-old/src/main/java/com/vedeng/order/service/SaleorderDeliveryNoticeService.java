package com.vedeng.order.service;

import com.vedeng.authorization.model.User;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderDeliveryNotice;
import com.vedeng.order.model.SaleorderDeliveryNoticeGoods;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName SaleorderDeliveryNoticeService
 * @Description 发货通知service
 * @Date 2020/7/21 11:45
 */
public interface SaleorderDeliveryNoticeService {

    /**
     * 新增或更新发货通知
     * @Param: [thisTimeDeliveryNums, saleorderId]
     * @Return: int
     * @Author: Rivan
     * @Date: 2020/7/21 16:19
     */
    int saveOrUpdateDeliveryNotice(String[] thisTimeDeliveryNums, Saleorder saleorderId, SaleorderDeliveryNotice deliveryNotice, User user) throws Exception;

    /**
     * 根据发货通知表id获取发货通知基本信息
     * @Param: [saleorderDeliveryNotice]
     * @Return: com.vedeng.order.model.SaleorderDeliveryNotice
     * @Author: Rivan
     * @Date: 2020/7/22 9:38
     */
    SaleorderDeliveryNotice getSaleorderDeliveryNoticeById(SaleorderDeliveryNotice saleorderDeliveryNotice);

    /**
     * 根据发货通知id获取发货通知单的产品信息list
     * @Param: [saleorderDeliveryNotice]
     * @Return: java.util.List<com.vedeng.order.model.SaleorderDeliveryNoticeGoods>
     * @Author: Rivan
     * @Date: 2020/7/22 16:38
     */
    List<SaleorderDeliveryNoticeGoods> getSaleorderDeliveryNoticeGoodsByDeliveryNoticeId(SaleorderDeliveryNotice saleorderDeliveryNotice);

    /**
     * 根据订单id获取发货通知列表
     * @Param: [saleorder]
     * @Return: java.util.List<com.vedeng.order.model.SaleorderDeliveryNotice>
     * @Author: Rivan
     * @Date: 2020/7/22 16:38
     */
    List<SaleorderDeliveryNotice> getSaleorderDeliveryNoticeByOrderId(Saleorder saleorder);

    /**
     * 关闭发货通知单
     * @Param: [deliveryNoticeId]
     * @Return: int
     * @Author: Rivan
     * @Date: 2020/7/23 11:16
     */
    int updateSaleorderDeliveryNoticeStatus(Integer deliveryNoticeId,Integer status);

    /**
     * 更新发货通知单的审核状态
     * @Param: [deliveryNoticeId]
     * @Return: int
     * @Author: Rivan
     * @Date: 2020/7/23 13:38
     */
    int updateSaleorderDeliveryNoticeAuditStatus(Integer deliveryNoticeId,Integer auditStatus);

    /**
     * 非已关闭的历史发货通知中的sku本次发货数量
     * @Param: [saleorderId, saleorderGoodsId]
     * @Return: int
     * @Author: Rivan
     * @Date: 2020/7/24 17:42
     */
    int getDeliveryNoticeGoodNum(Integer saleorderId, Integer saleorderGoodsId,Integer deliveryNoticeId);

    /**
     * 更新发货通知
     * @Param: [deliveryNoticeId]
     * @Return: int
     * @Author: Rivan
     * @Date: 2020/7/24 17:42
     */
    int updateDeliveryNoticeByPrimaryKeySelective(Integer deliveryNoticeId);
}
