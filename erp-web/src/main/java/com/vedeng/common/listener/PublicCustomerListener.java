package com.vedeng.common.listener;

import com.google.common.eventbus.Subscribe;
import com.vedeng.common.core.config.IObserver;
import com.vedeng.common.core.listenerEvent.PublicCustomerEvent;
import com.vedeng.trader.service.TraderCustomerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@Slf4j
public class PublicCustomerListener implements IObserver {

    @Autowired
    private TraderCustomerService TraderCustomerService;
    
    
    @Subscribe
    @Transactional(rollbackFor = Throwable.class)
    public void openBlueInvoiceCreateBillPeriod(PublicCustomerEvent publicCustomerEvent) {
        log.info("监听公海划拨事件，开始处理");
        TraderCustomerService.assignCustomer(publicCustomerEvent);
    }
}
