package com.vedeng.customerbillperiod.service;

import com.vedeng.common.page.Page;
import com.vedeng.customerbillperiod.dto.*;
import com.vedeng.customerbillperiod.exception.CustomerBillPeriodException;
import com.vedeng.customerbillperiod.model.CustomerBillPeriodApply;

import java.util.List;
import java.util.Map;

/**
 * @Author: daniel
 * @Date: 2021/7/23 10 09
 * @Description: 客户账期申请服务接口
 */
public interface CustomerBillPeriodApplyService {

    /**
     * 根据主键编号获取账期信息
     *
     * @param id
     * @return
     */
    CustomerBillPeriodApply selectByPrimaryKey(Long id);

    /**
     * 获取客户的所有账期申请记录
     * @param companyId 公司id
     * @param customerId 客户id
     * @return 客户的账期申请记录
     */
    List<CustomerBillPeriodApply> getCustomerBillPeriodApplyList(Integer companyId, Long customerId);

    /**
     * 获取客户的所有账期申请记录(分页)
     * @param customerId 客户id
     * @param page 分页信息
     * @return 客户的账期申请记录
     */
    List<CustomerBillPeriodApply> getCustomerBillPeriodApplyListByPage( Integer customerId, Page page);



    /**
     * 保存账期申请操作（修改账期原有额度时，不能小于已占用的金额）
     * 1、一个客户只有拥有一个有效正式账期
     * 2、账期调整额度不能小于已占用的金额
     * 3、新增
     * @param customerBillPeriodApplyDto 账期申请信息
     * @throws CustomerBillPeriodException 保存账期申请操作异常
     * @return 账期申请id
     */
    Long saveCustomerBillPeriodApply(CustomerBillPeriodApplyDto customerBillPeriodApplyDto) throws CustomerBillPeriodException;


    /**
     * 修改进行中的客户账期申请（只可以修改账期的额度和结算周期两个信息）
     * @param modifiedCustomerBillPeriodApply 修改的客户账期申请信息
     * @throws CustomerBillPeriodException 修改客户账期申请信息操作异常
     */
    void modifyCustomerBillPeriodApply(CustomerBillPeriodApplyModifyDto modifiedCustomerBillPeriodApply) throws CustomerBillPeriodException;


    /**
     * 审核客户账期申请
     * @param billPeriodApplyId 账期申请id
     * @param pass 是否审核通过
     * @throws CustomerBillPeriodException 审核客户账期申请的操作异常
     */
    void approvalCustomerBillPeriodApply(Long billPeriodApplyId, Boolean pass) throws CustomerBillPeriodException;


    /**
     * 分页查询客户账期申请记录
     * @param queryDto 查询类
     * @return 账期申请记录列表
     */
    CustomerBillPeriodPageDto<List<CustomerBillPeriodApplyDetailsDto>> getCustomerBillPeriodApplyListByPage(CustomerBillPeriodApplyQueryDto queryDto);
    /**
     * <b>Description:</b><br>
     * 根据生效账期id查询申请信息
     * 
     * @param billPeriodIds
     * @return java.util.List<com.vedeng.customerbillperiod.model.CustomerBillPeriodApply>
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/8/10 16:20
     */
    List<CustomerBillPeriodApply> getApplyInfoByBillPeriodId(List<Long> billPeriodIds);
}
