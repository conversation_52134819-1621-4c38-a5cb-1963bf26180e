package com.vedeng.order.service;

import com.vedeng.authorization.model.User;
import com.vedeng.order.model.SaleOrderWarnVo;
import com.vedeng.order.model.SaleOrderWarning;
import com.vedeng.order.model.vo.SaleorderGoodsVo;

import java.util.List;

/**
 * @Description:  更新待采购订单列表，产品时效状态和预警等级业务接口
 * @Author:       davis
 * @Date:         2021/4/19 下午10:09
 * @Version:      1.0
 */
public interface UpdateSaleOrderGoodsAgingService {

    /**
     * 处理订单产品时效状态
     * @param saleOrderWarnVo 消费对象
     */
    void dealSaleOrderGoodsAging(SaleOrderWarnVo saleOrderWarnVo);

    /**
     * 放入消息队列的等待处理
     * @param saleOrderWarnVo 消费对象
     */
    void sendSaleOrder(SaleOrderWarnVo saleOrderWarnVo);

    /**
     * 查询该条记录是否已经发生过预警
     * @param saleorderGoodsVo 消费对象
     * @return 预警记录
     */
    List<SaleOrderWarning> getHistorySaleOrderWarn(SaleorderGoodsVo saleorderGoodsVo);

    /**
     * 获取订单助手
     * @param sgv
     * @return
     */
    List<User> getOrderAssistant(SaleorderGoodsVo sgv);
}
