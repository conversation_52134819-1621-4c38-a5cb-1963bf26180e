package com.wms.dao;

import com.wms.model.dto.WmsInputOrderGoodsDto;
import com.wms.model.po.WmsInputOrderGoods;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WmsInputOrderGoodsMapper {
    int deleteByPrimaryKey(Long wmsInputOrderGoodsId);

    int insert(WmsInputOrderGoods record);

    int insertSelective(WmsInputOrderGoods record);

    WmsInputOrderGoods selectByPrimaryKey(Long wmsInputOrderGoodsId);

    int updateByPrimaryKeySelective(WmsInputOrderGoods record);

    int updateByPrimaryKey(WmsInputOrderGoods record);

    List<WmsInputOrderGoods> getWmsInfoOrderGoodsByWmsInputOrderId(Integer wmsInputOrderId);

    List<WmsInputOrderGoodsDto> getWmsInfoOrderGoodsDtoByWmsInputOrderId(Integer wmsInputOrderId);

    WmsInputOrderGoods getOrderGoods(@Param("orderNo")String orderNo,@Param("skuId")Integer skuId);
}