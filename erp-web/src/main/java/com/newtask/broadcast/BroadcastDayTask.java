package com.newtask.broadcast;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.util.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.broadcast.statistics.project.BroadcastDay;
import com.vedeng.erp.common.broadcast.param.TimePeriod;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;


/**
 * 日到款通知，时间参数 params，字符串，例如：2025-06-12 或者 2025-06
 * @ClassName:  BroadcastDayTask   
 * @author: <PERSON><PERSON>yang
 * @date:   2025年6月18日 上午10:30:29    
 * @Copyright:
 */
@JobHandler(value = "broadcastDayTask")
@Component
public class BroadcastDayTask extends AbstractJobHandler {

    Logger logger = LoggerFactory.getLogger(BroadcastDayTask.class);
    
    @Autowired
    private BroadcastDay broadcastDay;
    
    @Override
    public ReturnT<String> doExecute(String params) throws Exception {
        XxlJobLogger.log("到款日播报 start-----------");
        logger.info("到款日播报  start-----------");
        try {
        	List<TimePeriod> timePeriodList = processDate(params);
        	if(CollectionUtils.isEmpty(timePeriodList) && StringUtils.isEmpty(params)) {
        		broadcastDay.invocation(null,true);
        	}else {
        		for (TimePeriod timePeriod : timePeriodList) {
        			broadcastDay.invocation(timePeriod,false);
				}
        	}
        }catch(Exception e) {
        	XxlJobLogger.log("到款日播报失败");
        	logger.error("到款日播报失败");
        	return FAIL;
        }
        logger.info("到款日播报 end-----------");
        XxlJobLogger.log("到款日播报 end-----------");
        return SUCCESS;
    }
    
    public static List<TimePeriod> processDate(String params) {
    	if(StringUtils.isEmpty(params)) {
    		return null;
    	}
        List<TimePeriod> result = new ArrayList<>();
        String[] parts = params.split("-");
        Calendar cal = Calendar.getInstance();
        // yyyy-MM-dd格式
        if (parts.length == 3) { 
            int year = Integer.parseInt(parts[0]);
            int month = Integer.parseInt(parts[1]) - 1;
            int day = Integer.parseInt(parts[2]);
            
            // 设置当天开始时间
            cal.set(year, month, day, 0, 0, 0);
            Date startDate = cal.getTime();
            
            // 设置当天结束时间
            cal.set(year, month, day, 23, 59, 59);
            Date EndDate = cal.getTime();
            
            TimePeriod period = new TimePeriod(startDate,EndDate);
            result.add(period);
            
        } 
        // yyyy-MM格式
        else if (parts.length == 2) { 
            int year = Integer.parseInt(parts[0]);
            int month = Integer.parseInt(parts[1]) - 1;
            
            // 获取月份天数
            cal.set(year, month, 1);
            int daysInMonth = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
            
            for (int day = 1; day <= daysInMonth; day++) {
                // 设置当天开始时间
                cal.set(year, month, day, 0, 0, 0);
                Date startDate = cal.getTime();
                
                // 设置当天结束时间
                cal.set(year, month, day, 23, 59, 59);
                Date EndDate = cal.getTime();
                
                TimePeriod period = new TimePeriod(startDate,EndDate);
                result.add(period);
            }
        }else {
        	return null;
        }
        return result;
    }
    
    public static TimePeriod processDateMonth() {
    	Calendar calendar = Calendar.getInstance();
        int yearNow = calendar.get(Calendar.YEAR);
        int monthNow = calendar.get(Calendar.MONTH) + 1;  // 由于月份从0开始，所以要加1
        String timeMonth = String.format("%d-%02d", yearNow, monthNow);
    	if(StringUtils.isEmpty(timeMonth)) {
    		return null;
    	}
        String[] parts = timeMonth.split("-");
        Calendar cal = Calendar.getInstance();
        // yyyy-MM格式
        if (parts.length == 2) { 
            int year = Integer.parseInt(parts[0]);
            int month = Integer.parseInt(parts[1]) - 1;
            // 获取月份天数
            cal.set(year, month, 1);
            int daysInMonth = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
            // 设置当天开始时间
            cal.set(year, month, 1, 0, 0, 0);
            Date startDate = cal.getTime();
            // 设置当月最后一天结束时间
            cal.set(year, month, daysInMonth, 23, 59, 59);
            Date EndDate = cal.getTime();
            TimePeriod period = new TimePeriod(null,null,startDate,EndDate);
            return period;
        }else {
        	return null;
        }
    }
    
    public static void main(String[] args) {
    	List<TimePeriod> result = processDate("2025-06-01");
    	List<TimePeriod> result2 = processDate("2025-06");
    	System.out.println(JSON.toJSONString(result));
    	System.out.println(JSON.toJSONString(result2));
    	Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;  // 由于月份从0开始，所以要加1
        String timeMonth = String.format("%d-%02d", year, month);
        System.out.println(timeMonth);
        System.out.println("=============="+processDateMonth() );
	}
}
