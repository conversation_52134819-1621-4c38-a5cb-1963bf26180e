<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<head>
    <title>实际供应商信息</title>
</head>
<body>
<div id="app" style="display: none;">
    <div class="actual_supplier_info_table">
        <el-descriptions title="" :column="1" border>
            <el-descriptions-item label="供应商名称">{{ actualSupplier.traderName }}</el-descriptions-item>
            <el-descriptions-item label="联系人">{{ actualSupplier.traderContactName }}</el-descriptions-item>
            <el-descriptions-item label="电话">{{ actualSupplier.traderContactTelephone }}</el-descriptions-item>
            <el-descriptions-item label="手机">{{ actualSupplier.traderContactMobile }}</el-descriptions-item>
            <el-descriptions-item label="付款方式">{{ paymentInfoStr }}</el-descriptions-item>
            <el-descriptions-item label="收票类型">{{ invoiceTypeStr }}</el-descriptions-item>
            <el-descriptions-item label="开票">{{ needInvoice }}</el-descriptions-item>
        </el-descriptions>
    </div>
</div>

<script type="text/javascript">
    let actualSupplier = ${actualSupplier};
    let invoiceTypeStr = '${invoiceTypeStr}';
    let vm = new Vue({
        el: '#app',
        data() {
            return {
                invoiceTypeStr: '',
                paymentInfoStr: '',
                actualSupplier: {},
                needInvoice: actualSupplier.needInvoice ? '是' : '否'
            }
        },
        mounted() {
            loadingApp()
        },
        methods: {
            displayPaymentDetails(actualSupplier) {
                var paymentInfo = '';
                var paymentType = actualSupplier.paymentType;
                var prepaidAmountFormatted = actualSupplier.prepaidAmount ? actualSupplier.prepaidAmount.toFixed(2) : '0.00';
                var accountPeriodAmountFormatted = actualSupplier.accountPeriodAmount ? actualSupplier.accountPeriodAmount.toFixed(2) : '0.00';
                var retainageAmountFormatted = actualSupplier.retainageAmount ? actualSupplier.retainageAmount.toFixed(2) : '0.00';
                var retainageAmountMonth = actualSupplier.retainageAmountMonth;

                switch (paymentType) {
                    case 419:
                        paymentInfo = '先款后货，预付100%，预付金额' + prepaidAmountFormatted + '元。';
                        break;
                    case 420:
                        paymentInfo = '先货后款，预付80%，预付金额' + prepaidAmountFormatted + '元，账期支付' + accountPeriodAmountFormatted + '元。';
                        break;
                    case 421:
                        paymentInfo = '先货后款，预付50%，预付金额' + prepaidAmountFormatted + '元，账期支付' + accountPeriodAmountFormatted + '元。';
                        break;
                    case 422:
                        paymentInfo = '先货后款，预付30%，预付金额' + prepaidAmountFormatted + '元，账期支付' + accountPeriodAmountFormatted + '元。';
                        break;
                    case 423:
                        paymentInfo = '先货后款，预付0%，预付金额' + prepaidAmountFormatted + '元，账期支付' + accountPeriodAmountFormatted + '元。';
                        break;
                    case 424:
                        paymentInfo = '自定义，预付金额' + prepaidAmountFormatted + '元，账期支付' + accountPeriodAmountFormatted + '元，尾款' + retainageAmountFormatted + '元，尾款期限' + retainageAmountMonth + '月';
                        break;
                    case 3174:
                        paymentInfo = '先货后款，预付10%，预付金额' + prepaidAmountFormatted + '元，账期支付' + accountPeriodAmountFormatted + '元。';
                        break;
                    case 3175:
                        paymentInfo = '先货后款，预付90%，预付金额' + prepaidAmountFormatted + '元，账期支付' + accountPeriodAmountFormatted + '元。';
                        break;
                    default:
                        paymentInfo = '';
                }
                this.paymentInfoStr = paymentInfo;
            }
        },
        created() {
            this.actualSupplier = actualSupplier;
            this.invoiceTypeStr = invoiceTypeStr;
            this.displayPaymentDetails(this.actualSupplier);
        }
    });
</script>
<style>

</style>
