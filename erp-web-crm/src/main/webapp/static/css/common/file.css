.file-dialog-top {
  display: flex;
  align-items: center;
}
.file-dialog-top .btn {
  margin-right: 10px;
}
.file-dialog-top > span {
  color: #999;
}
.file-dialog-panel {
  position: relative;
  height: 100%;
  overflow: hidden;
}
.file-dialog-panel .panel-wrap {
  height: calc(100% - 56px);
  overflow-y: auto;
}
.file-dialog-panel .panel-wrap::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.file-dialog-panel .panel-wrap::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.file-dialog-panel .panel-wrap::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.file-dialog-panel .panel-wrap::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.file-dialog-panel .panel-wrap::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.file-dialog-panel .panel-wrap.has-fixed-btn {
  height: calc(100% - 56px - 53px);
}
.file-dialog-panel .panel-wrap.dialog-inner {
  max-height: 510px;
  margin-top: 20px;
}
.file-dialog-panel .panel-wrap .file-list .file-name > img {
  width: 16px;
  height: 20px;
  margin-right: 10px;
}
.file-dialog-panel .panel-wrap .file-list .file-deal-btn .btn {
  position: relative;
  color: #09f;
  cursor: pointer;
  margin-right: 10px;
  padding-right: 10px;
}
.file-dialog-panel .panel-wrap .file-list .file-deal-btn .btn::after {
  content: "";
  display: block;
  width: 2px;
  height: 15px;
  background: #ededed;
  position: absolute;
  right: 0;
  top: 3px;
}
.file-dialog-panel .panel-wrap .file-list .file-deal-btn .btn:hover {
  color: #f60;
}
.file-dialog-panel .panel-wrap .file-list .file-deal-btn .btn:last-child {
  margin-right: 0;
  padding-right: 0;
}
.file-dialog-panel .panel-wrap .file-list .file-deal-btn .btn:last-child::after {
  display: none;
}
.file-dialog-panel .panel-wrap .null-data {
  padding: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.file-dialog-panel .panel-wrap .null-data .empty-img {
  width: 110px;
  height: 95px;
  background: url('../../image/common/no-data2.svg') no-repeat;
  background-size: 110px 95px;
  background-position: center;
  margin-bottom: 20px;
}
