package com.vedeng.order.controller;

import cn.hutool.core.collection.CollUtil;
import com.common.constants.Contant;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.authorization.model.*;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.annotation.SystemControllerLog;
import com.vedeng.common.constant.*;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.money.OrderMoneyUtil;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.validator.FormToken;
import com.vedeng.erp.buyorder.dto.ExpenseBuyForSaleDetail;
import com.vedeng.erp.buyorder.service.RBuyorderExpenseJSaleorderService;
import com.vedeng.erp.saleorder.dto.ConfirmationDto;
import com.vedeng.erp.saleorder.dto.SaleOrderGoodsDetailDto;
import com.vedeng.erp.saleorder.dto.SaleorderAdditionalClauseDto;
import com.vedeng.erp.saleorder.service.OrderAdditionalClauseApiService;
import com.vedeng.erp.saleorder.service.SaleOrderGoodsApiService;
import com.vedeng.erp.wms.service.OrderPeerApiService;
import com.vedeng.finance.model.*;
import com.vedeng.finance.service.CapitalBillService;
import com.vedeng.finance.service.InvoiceService;
import com.vedeng.flash.dto.SpecialDeliveryInfoDto;
import com.vedeng.goods.model.CoreSpuGenerate;
import com.vedeng.goods.model.Goods;
import com.vedeng.goods.model.Unit;
import com.vedeng.goods.service.*;
import com.vedeng.logistics.dao.ConfirmationFormRecodeMapper;
import com.vedeng.logistics.dao.ExpressMapper;
import com.vedeng.logistics.dao.OutboundBatchesRecodeMapper;
import com.vedeng.logistics.model.*;
import com.vedeng.logistics.service.ExpressService;
import com.vedeng.logistics.service.WarehouseOutService;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.enums.SaleOrderTypeEnum;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.model.SaleorderModifyApply;
import com.vedeng.order.model.dto.JcTraderContactDto;
import com.vedeng.order.model.dto.RemarkComponentDto;
import com.vedeng.order.model.query.JcGoodsQuery;
import com.vedeng.order.model.query.LabelQuery;
import com.vedeng.order.model.vo.SaleorderGoodsWarrantyVo;
import com.vedeng.order.service.JcOrderService;
import com.vedeng.order.service.RiskCheckService;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.order.service.impl.RemarkComponentServiceImpl;
import com.vedeng.price.api.price.dto.price.PriceInfoResponseDto;
import com.vedeng.price.dto.ContractPriceInfoDetailResponseDto;
import com.vedeng.price.dto.GoodSalePrice;
import com.vedeng.price.dto.SkuPriceInfoDetailResponseDto;
import com.vedeng.price.service.BasePriceService;
import com.vedeng.price.service.PriceInfoDealWithService;
import com.vedeng.system.dao.VerifiesInfoMapper;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.model.VerifiesInfo;
import com.vedeng.system.service.OrgService;
import com.vedeng.system.service.RegionService;
import com.vedeng.system.service.UserService;
import com.vedeng.trader.dao.TraderMapper;
import com.vedeng.trader.model.CommunicateRecord;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.model.TraderContactGenerate;
import com.vedeng.trader.model.TraderCustomer;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import com.vedeng.trader.service.TraderContactService;
import com.vedeng.trader.service.TraderCustomerService;
import com.vedeng.wechat.JcWeChatEnum;
import com.vedeng.wechat.model.JcWeChatModel;
import com.vedeng.wechat.service.JcWeChatArrService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <b>Description: 集采订单controller</b><br>
 * <b>@Author: Rachel</b>
 *
 * @fileName JcOrderController.java <br>
 * <b>Date: 2021年2月20日 上午10：40 </b>
 */
@Controller
@RequestMapping("/order/jc")
public class JcOrderController extends BaseController {

    private final static Logger LOGGER = LoggerFactory.getLogger(JcOrderController.class);

    @Autowired // 自动装载
    protected ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();


    @Autowired
    private JcOrderService jcOrderService;

    @Autowired
    @Qualifier("orgService")
    protected OrgService orgService;

    @Autowired
    @Qualifier("userService")
    protected UserService userService;

    @Autowired
    @Qualifier("capitalBillService")
    protected CapitalBillService capitalBillService;

    @Autowired
    @Qualifier("saleorderService")
    protected SaleorderService saleorderService;

    @Resource
    protected AfterSalesService afterSalesOrderService;
    @Resource
    protected TraderCustomerService traderCustomerService;
    @Resource
    protected RegionService regionService;
    @Resource
    protected GoodsService goodsService;
    @Resource
    private BasePriceService basePriceService;
    @Resource
    private VgoodsService vgoodsService;

    @Autowired
    private JcWeChatArrService jcWeChatArrService;

    @Autowired
    private RiskCheckService riskCheckService;

    @Autowired
    @Qualifier("goodsSettlementPriceService")
    protected GoodsSettlementPriceService goodsSettlementPriceService;

    @Autowired
    @Qualifier("goodsChannelPriceService")
    protected GoodsChannelPriceService goodsChannelPriceService;

    @Autowired
    @Qualifier("invoiceService")
    protected InvoiceService invoiceService;

    @Autowired
    private OrderPeerApiService orderPeerApiService;

    @Value("${confirm_online_timestamp}")
    private Long confirmOnlineTimestamp;


    @Autowired
    private VgoodsService vGoodsService;

    @Autowired
    private PriceInfoDealWithService priceInfoDealWithService;

    @Autowired
    @Qualifier("actionProcdefService")
    protected ActionProcdefService actionProcdefService;

    @Value("${retention_money_uptime}")
    private Long retentionMoneyUptime;

    @Autowired
    @Qualifier("expressService")
    protected ExpressService expressService;

    @Autowired
    private  SkuAuthorizationService skuAuthorizationService;

    @Autowired
    private TraderMapper traderMapper;
    @Resource
    private TraderContactService traderContactService;

    @Autowired
    private RemarkComponentController remarkComponentController;

    @Autowired
    private RemarkComponentServiceImpl remarkComponentService;

    @Autowired
    private UnitService unitService;

    @Autowired
    VerifiesInfoMapper verifiesInfoMapper;

    @Resource
    private SaleorderMapper saleorderMapper;
    @Resource
    ExpressMapper expressMapper;
    @Resource
    SaleorderGoodsMapper saleorderGoodsMapper;
    @Resource
    ConfirmationFormRecodeMapper confirmationFormRecodeMapper;
    @Resource
    WarehouseOutService warehouseOutService;
    @Resource
    OutboundBatchesRecodeMapper outboundBatchesRecodeMapper;

    @Autowired
    SaleOrderGoodsApiService saleOrderGoodsApiService;
    @Autowired
    RBuyorderExpenseJSaleorderService rBuyorderExpenseJSaleorderService;
    @Autowired
    private OrderAdditionalClauseApiService orderAdditionalClauseApiService;


    @ResponseBody
    @RequestMapping(value = "/jcOrderListPage")
    public ModelAndView jcOrderListPage(HttpServletRequest request, Saleorder saleorder, HttpSession session,
                                        @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                        @RequestParam(required = false) Integer pageSize,@RequestParam(defaultValue = "2") Integer scene) {
        ModelAndView mv = new ModelAndView("order/jc/jc_order_list");
        mv.addObject("scene",scene);
        //获取当前用户信息
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        saleorder.setCompanyId(user.getCompanyId());
        saleorder.setUserId(user.getUserId());
        // 获取当前销售用户下级职位用户
        List<Integer> positionType = new ArrayList<>();
        positionType.add(SysOptionConstant.ID_310);
        List<User> userList = userService.getMyUserList(user, positionType, false);
        mv.addObject("loginUser", user);
        mv.addObject("userList", userList);// 操作人员

        //订单类型--没有值则赋值全部(包括JCO/JCF)
        Integer orderType = saleorder.getOrderType();
        if (orderType == null) {
            saleorder.setOrderType(-3);
        }
        Page page = getPageTag(request, pageNo, pageSize);
        //时间赋值
        setSearchTime(request, saleorder);

        List<User> saleUserList = new ArrayList<>();
        if (null != saleorder.getOptUserId() && saleorder.getOptUserId() != -1) {
            User saleUser = new User();
            saleUser.setUserId(saleorder.getOptUserId());
            saleUserList.add(saleUser);
            saleorder.setSaleUserList(saleUserList);
            // 销售
        } else if (null != user.getPositType() && SysOptionConstant.ID_310.equals(user.getPositType())) {
            saleorder.setSaleUserList(userList);
        }
        if(CollectionUtils.isEmpty(saleorder.getSaleUserList()) && CollectionUtils.isNotEmpty(user.getPositions())){
            for(Position positiont : user.getPositions()){
                if(SysOptionConstant.ID_310.equals(positiont.getPositionId())){
                    saleorder.setSaleUserList(userList);
                    break;
                }
            }
        }

        saleorder.setOptType("orderIndex");
        // 运营对合同审核的人员进入列表页后，默认加载合同待审核的列表，且生效时间>=2018.01.01
        if (SysOptionConstant.ID_399.equals(user.getUserId())) {
            if (saleorder.getContractStatus() == null) {
                saleorder.setContractStatus(3);
            }
            if ((request.getParameter("searchBegintimeStr") == null || request.getParameter("searchBegintimeStr") == "")
                    && saleorder.getSearchDateType() == null) {
                saleorder.setSearchBegintime(DateUtil.convertLong("2018-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss"));
                saleorder.setSearchDateType(2);
            }
        }



        Map<String, Object> resultMap = jcOrderService.getJcSaleOrderListPage(request, saleorder, page);
        List<Saleorder> list = (List<Saleorder>) resultMap.get("saleorderList");
        List<Integer> saleorderIdList = new ArrayList<>();

        for (int i = 0; i < list.size(); i++) {
            // 销售人员
            user = userService.getUserById(list.get(i).getUserId());
            list.get(i).setSalesName(user == null ? "" : user.getUsername());
            // 归属销售
            user = orgService.getTraderUserAndOrgByTraderId(list.get(i).getTraderId(), 1);// 1客户，2供应商
            list.get(i).setOptUserName(user == null ? "" : user.getUsername());
            list.get(i).setSalesDeptName(user == null ? "" : user.getOrgName());
            // 获取订单合同回传&订单送货单回传列表
            List<Attachment> saleorderAttachmentList = saleorderService
                    .getSaleorderAttachmentList(list.get(i).getSaleorderId());
            // mv.addObject("saleorderAttachmentList", saleorderAttachmentList);
            for (Attachment attachment : saleorderAttachmentList) {
                if (attachment.getAttachmentFunction() == 492)
                    list.get(i).setIsContractReturn(1);
                if (attachment.getAttachmentFunction() == 493)
                    list.get(i).setIsDeliveryOrderReturn(1);
            }
            saleorderIdList.add(list.get(i).getSaleorderId());
            // 审核人
            if (null != list.get(i).getVerifyUsername()) {
                List<String> verifyUsernameList = Arrays.asList(list.get(i).getVerifyUsername().split(","));
                list.get(i).setVerifyUsernameList(verifyUsernameList);
            }
            // 未添加产品成本人员
            if (null != list.get(i).getCostUserIds()) {
                List<String> costUserList = Arrays.asList(list.get(i).getCostUserIds().split(","));
                list.get(i).setCostUserIdsList(costUserList);
            }

            // 可否开票判断
            Saleorder invoiceSaleorder = new Saleorder();
            invoiceSaleorder.setSaleorderId(list.get(i).getSaleorderId());
            invoiceSaleorder.setOptType("orderIndex");
            invoiceSaleorder = saleorderService.getBaseSaleorderInfo(invoiceSaleorder);
            list.get(i).setIsOpenInvoice(invoiceSaleorder.getIsOpenInvoice());

            setSaleorderAmount(list.get(i));
        }

        List<SaleorderData> capitalBillList = capitalBillService.getCapitalListBySaleorderId(saleorderIdList);


        // add by Tomcat.Hui 2019/9/5 10:44 .Desc: VDERP-1053 每笔订单做实际金额运算. start
        if (CollectionUtils.isNotEmpty(capitalBillList)) {
            capitalBillList.forEach(order -> {
                try {
                    if (null != order.getSaleorderId()) {
                        BigDecimal periodAmount = saleorderService.getPeriodAmount(order.getSaleorderId());
                        BigDecimal lackAccountPeriodAmount = saleorderService.getLackAccountPeriodAmount(order.getSaleorderId());
                        BigDecimal refundBalanceAmount = afterSalesOrderService.getRefundBalanceAmountBySaleorderId(order.getSaleorderId());
                        order.setPeriodAmount(periodAmount == null ? BigDecimal.ZERO : periodAmount);
                        order.setLackAccountPeriodAmount(lackAccountPeriodAmount == null ? BigDecimal.ZERO : lackAccountPeriodAmount);
                        order.setRefundBalanceAmount(refundBalanceAmount == null ? BigDecimal.ZERO : refundBalanceAmount);
                    }
                } catch (Exception e) {
                    log.error("订单实际金额运算出现异常: ", e);
                }
            });
        }

        mv.addObject("capitalBillList", capitalBillList);

        mv.addObject("saleorderList", list);

        if(!CollectionUtils.isEmpty(list) &&  (saleorder.getIsReferenceCostPrice() == null ||
                saleorder.getIsReferenceCostPrice() == 0 || saleorder.getIsReferenceCostPrice() == -1)){
            //User currUser = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
            User currUser = (User) session.getAttribute(Consts.SESSION_USER);
            Set<Integer> userids = new HashSet<>();
            for(Saleorder saleorderItem : list){
                if(saleorderItem.getPaymentStatus() != 2 || saleorderItem.getStatus() == 3){
                    continue;
                }
                //满足未填写
                if (saleorderMapper.queryWhetherMaintain(saleorderItem.getSaleorderId()) > 0) {
                    saleorderItem.setIsReferenceCostPrice(1);
                }
                List<SaleorderGoods> saleorderGoods = saleorderService.getSaleorderGoodsBySaleorderId(saleorderItem.getSaleorderId());
                if(CollectionUtils.isEmpty(saleorderGoods)){
                    continue;
                }
                userids.clear();
                saleorderGoods.forEach(saleOrderGood->{
                    if(saleOrderGood.getIsDelete() == 0 && saleOrderGood.getReferenceCostPrice().compareTo(BigDecimal.ZERO) == 0){
                        CoreSpuGenerate spuGenerate = saleorderService.getSpuBase(saleOrderGood.getGoodsId());
                        if(spuGenerate != null){
                            userids.add(spuGenerate.getAssignmentAssistantId());
                            userids.add(spuGenerate.getAssignmentManagerId());
                        }
                    }
                });
                if(userids.contains(currUser.getUserId())){
                    saleorderItem.setLoginUserBelongToProductManager(1);
                }
            }
        }
        mv.addObject("page", resultMap.get("page"));
        mv.addObject("saleorder", saleorder);
        return mv;
    }

    private void setSaleorderAmount(Saleorder saleorder) {
        if (saleorder == null || saleorder.getSaleorderId() == null || saleorder.getSaleorderId().equals(0)) {
            return;
        }
        Map<String, BigDecimal> moneyData = saleorderService.getSaleorderDataInfo(saleorder.getSaleorderId());
        if (moneyData != null) {
            if (moneyData.get("realAmount") != null) {
                saleorder.setRealAmount(moneyData.get("realAmount"));

            }
            if (moneyData.get("paymentAmount") != null) {
                saleorder.setPaymentAmount(OrderMoneyUtil.getPaymentAmount(moneyData));
            }
        }
    }

    /**

     * @param request
     * @param saleorder
     * @param session
     * @param pageNo
     * @param pageSize
     * @return
     * @describr 线下直销订单列表页
     * @author: hugo
     */
    @ResponseBody
    @RequestMapping(value = "/offlineDirectSalesOrderListPage")
    public ModelAndView index(HttpServletRequest request, Saleorder saleorder, HttpSession session,
                              @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                              @RequestParam(required = false) Integer pageSize,
                              @RequestParam(defaultValue = "4",required = false)   Integer scene) {
        ModelAndView mv = new ModelAndView("order/jc/index");
        mv.addObject("scene",scene);
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        saleorder.setCompanyId(user.getCompanyId());
        saleorder.setUserId(user.getUserId());

        //获取订单来源
        saleorder.setOrderType(OrderConstant.ORDER_TYPE_ZXF);
        Page page = getPageTag(request, pageNo, pageSize);
        setSearchTime(request, saleorder);

        // 客户性质
        List<SysOptionDefinition> customerNatures = getSysOptionDefinitionList(464);
        // 获取销售部门


        Integer position = 0;
        if (user.getPositions() != null) {
            position = user.getPositions().get(0).getType();
        }
        mv.addObject("orgList", orgService.getSalesOrgList(SysOptionConstant.ID_310, user.getCompanyId()));
        mv.addObject("loginUser", user);
        mv.addObject("position", position);

        // 获取当前销售用户下级职位用户
        List<Integer> positionTypes = Arrays.asList(SysOptionConstant.ID_310);
        List<User> userList = userService.getMyUserList(user, positionTypes, false);
        mv.addObject("userList", userList);

        List<User> saleUserList = new ArrayList<>();
        if (null != saleorder.getOptUserId() && saleorder.getOptUserId() != -1) {
            User saleUser = new User();
            saleUser.setUserId(saleorder.getOptUserId());
            saleUserList.add(saleUser);
            saleorder.setSaleUserList(saleUserList);
            // 销售
        } else if (null != user.getPositType() && SysOptionConstant.ID_310.equals(user.getPositType())) {
            saleorder.setSaleUserList(userList);
        }
        if(CollectionUtils.isEmpty(saleorder.getSaleUserList()) && CollectionUtils.isNotEmpty(user.getPositions())){
            for(Position positiont : user.getPositions()){
                if(SysOptionConstant.ID_310.equals(positiont.getPositionId())){
                    saleorder.setSaleUserList(userList);
                    break;
                }
            }
        }
        saleorder.setOptType("orderIndex");
        // 运营对合同审核的人员进入列表页后，默认加载合同待审核的列表，且生效时间>=2018.01.01
        if (SysOptionConstant.ID_399.equals(user.getUserId())) {
            if (saleorder.getContractStatus() == null) {
                saleorder.setContractStatus(3);
            }
            if ((request.getParameter("searchBegintimeStr") == null || request.getParameter("searchBegintimeStr") == "")
                    && saleorder.getSearchDateType() == null) {
                saleorder.setSearchBegintime(DateUtil.convertLong("2018-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss"));
                saleorder.setSearchDateType(2);
            }
        }

        //查询当前操作员角色是否是 产品专员,产品主管,产品总监中的一个
        List<Integer> roleIdLists = Arrays.asList(new Integer[]{7,8,9});
        List<Integer> userRoleIdLists = user.getRoles().stream().map(Role::getRoleId).collect(Collectors.toList());

        userRoleIdLists.retainAll(roleIdLists);

        if(CollectionUtils.isNotEmpty(userRoleIdLists)){
            mv.addObject("hasProductRole", true);
            //所有的分配人
            mv.addObject("assUser", userService.selectAllAssignUser());
            //如果前台没有传值，默认就是当前登录人
            if ( user.getIsAdmin() != null && user.getIsAdmin() == 0){
                saleorder.setProductBelongUserId(saleorder.getProductBelongUserId()!= null ?
                        saleorder.getProductBelongUserId() :user.getUserId());
            }
        }else{
            mv.addObject("hasProductRole", false);
        }


        if(saleorder.getIsReferenceCostPrice() == null && SysOptionConstant.ID_311.equals(position)){
            //默认选中待填写
            saleorder.setIsReferenceCostPrice(0);
        }

        Map<String, Object> map = saleorderService.getOfflineDirectSalesOrderListPage(saleorder, page);

        List<Saleorder> list = (List<Saleorder>) map.get("saleorderList");
        CommunicateRecord cr = null;
        List<Integer> saleorderIdList = new ArrayList<>();

        for (Saleorder saleorderInfo : list) {
            // 销售人员
            user = userService.getUserById(saleorderInfo.getUserId());
            saleorderInfo.setSalesName(user == null ? "" : user.getUsername());
            // 归属销售   1客户，2供应商
            user = orgService.getTraderUserAndOrgByTraderId(saleorderInfo.getTraderId(), 1);
            saleorderInfo.setOptUserName(user == null ? "" : user.getUsername());
            saleorderInfo.setSalesDeptName(user == null ? "" : user.getOrgName());
            // 销售部门
            // list.get(i).setSalesDeptName(orgService.getOrgNameById(list.get(i).getOrgId()));
            // 沟通记录次数(参数使用List，多个参数，使方法能复用)
            cr = new CommunicateRecord();
            if (saleorderInfo.getSaleorderId() != null) {
                cr.setSaleorderId(saleorderInfo.getSaleorderId());
            }
            if (saleorderInfo.getQuoteorderId() != null) {
                cr.setQuoteorderId(saleorderInfo.getQuoteorderId());
            }
            if (saleorderInfo.getBussinessChanceId() != null) {
                cr.setBussinessChanceId(saleorderInfo.getBussinessChanceId());
            }
            // 沟通类型为商机和报价和销售订单
            int num = saleorderService.getSaleorderCommunicateRecordCount(cr);
            saleorderInfo.setCommunicateNum(num);

            saleorderInfo.setCustomerNatureStr(getSysOptionDefinition(saleorderInfo.getCustomerNature()).getTitle());
            // 获取订单合同回传&订单送货单回传列表
            List<Attachment> saleorderAttachmentList = saleorderService
                    .getSaleorderAttachmentList(saleorderInfo.getSaleorderId());
            // mv.addObject("saleorderAttachmentList", saleorderAttachmentList);
            for (Attachment attachment : saleorderAttachmentList) {
                if (attachment.getAttachmentFunction() == 492){
                    saleorderInfo.setIsContractReturn(1);
                }
                if (attachment.getAttachmentFunction() == 493){
                    saleorderInfo.setIsDeliveryOrderReturn(1);
                }
            }
            saleorderIdList.add(saleorderInfo.getSaleorderId());
            // 审核人
            if (null != saleorderInfo.getVerifyUsername()) {
                List<String> verifyUsernameList = Arrays.asList(saleorderInfo.getVerifyUsername().split(","));
                saleorderInfo.setVerifyUsernameList(verifyUsernameList);
            }
            // 未添加产品成本人员
            if (null != saleorderInfo.getCostUserIds()) {
                List<String> costUserList = Arrays.asList(saleorderInfo.getCostUserIds().split(","));
                saleorderInfo.setCostUserIdsList(costUserList);
            }

            // 可否开票判断
            Saleorder invoiceSaleorder = new Saleorder();
            invoiceSaleorder.setSaleorderId(saleorderInfo.getSaleorderId());
            invoiceSaleorder.setOptType("orderIndex");
            invoiceSaleorder = saleorderService.getBaseSaleorderInfo(invoiceSaleorder);
            saleorderInfo.setIsOpenInvoice(invoiceSaleorder.getIsOpenInvoice());

            setSaleorderAmount(saleorderInfo);
        }
        // 根据销售订单ID查询账期付款总额-订单未还账期款---换成Jerry写的方法
        List<SaleorderData> capitalBillList = capitalBillService.getCapitalListBySaleorderId(saleorderIdList);


        // add by Tomcat.Hui 2019/9/5 10:44 .Desc: VDERP-1053 每笔订单做实际金额运算. start
        if(CollectionUtils.isNotEmpty(capitalBillList)){
            capitalBillList.stream().forEach(order -> {
                try{
                    if (null != order.getSaleorderId()){
                        BigDecimal periodAmount = saleorderService.getPeriodAmount(order.getSaleorderId());
                        BigDecimal lackAccountPeriodAmount = saleorderService.getLackAccountPeriodAmount(order.getSaleorderId());
                        BigDecimal refundBalanceAmount = afterSalesOrderService.getRefundBalanceAmountBySaleorderId(order.getSaleorderId());
                        order.setPeriodAmount(periodAmount == null ? BigDecimal.ZERO : periodAmount);
                        order.setLackAccountPeriodAmount(lackAccountPeriodAmount == null ? BigDecimal.ZERO : lackAccountPeriodAmount);
                        order.setRefundBalanceAmount(refundBalanceAmount == null ? BigDecimal.ZERO : refundBalanceAmount);
                    }
                }catch (Exception e){
                    log.error("订单实际金额运算出现异常: {}",e);
                }
            });
        }
        mv.addObject("capitalBillList", capitalBillList);
        // 客户信息里面的交易记录
        if (null != saleorder.getTraderId() && saleorder.getTraderId() > 0) {
            mv.addObject("method", "saleorder");
            mv.addObject("traderId", saleorder.getTraderId());
        }

        mv.addObject("saleorderList", list);

        if(!CollectionUtils.isEmpty(list) &&  (saleorder.getIsReferenceCostPrice() == null ||
                saleorder.getIsReferenceCostPrice() == 0 || saleorder.getIsReferenceCostPrice() == -1)){
            User currUser = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
            Set<Integer> userids = new HashSet<>();
            for(Saleorder saleorderItem : list){
                if(saleorderItem.getPaymentStatus() != 2 || saleorderItem.getStatus() == 3){
                    continue;
                }
                List<SaleorderGoods> saleorderGoods = saleorderService.getSaleorderGoodsBySaleorderId(saleorderItem.getSaleorderId());
                if(CollectionUtils.isEmpty(saleorderGoods)){
                    continue;
                }
                userids.clear();
                saleorderGoods.forEach(saleOrderGood->{
                    if(saleOrderGood.getIsDelete() == 0 && saleOrderGood.getReferenceCostPrice().compareTo(BigDecimal.ZERO) == 0){
                        CoreSpuGenerate spuGenerate = saleorderService.getSpuBase(saleOrderGood.getGoodsId());
                        if(spuGenerate != null){
                            userids.add(spuGenerate.getAssignmentAssistantId());
                            userids.add(spuGenerate.getAssignmentManagerId());
                        }
                    }
                });
                if(userids.contains(currUser.getUserId())){
                    saleorderItem.setLoginUserBelongToProductManager(1);
                }
            }
        }

        //订单列表标签标识
        mv.addObject("salerorderIndex", 1);
        mv.addObject("page", map.get("page"));
        mv.addObject("total_amount", new BigDecimal(map.get("total_amount").toString()));
        mv.addObject("customerNatures", customerNatures);
        mv.addObject("saleorder", saleorder);
        return mv;
    }

    private void setSearchTime(HttpServletRequest request, Saleorder saleorder) {
        if (null != request.getParameter("searchBegintimeStr") && request.getParameter("searchBegintimeStr") != "") {
            saleorder.setSearchBegintime(DateUtil.convertLong(request.getParameter("searchBegintimeStr") + " 00:00:00",
                    "yyyy-MM-dd HH:mm:ss"));
        }
        if (null != request.getParameter("searchEndtimeStr") && request.getParameter("searchEndtimeStr") != "") {
            saleorder.setSearchEndtime(DateUtil.convertLong(request.getParameter("searchEndtimeStr") + " 23:59:59",
                    "yyyy-MM-dd HH:mm:ss"));
        }
    }


    @RequestMapping("/view")
    @FormToken(save = true)
    public ModelAndView view(HttpServletRequest request, Saleorder saleorder,@RequestParam(defaultValue = "0") Integer scene){
        User curr_user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        ModelAndView mv = new ModelAndView();
        mv.addObject("scene",scene);
        //是否是销售
        Boolean saleFlag = userService.getSaleFlagUserId(curr_user.getUserId());
        if(saleFlag){
            mv.addObject("roleStatus", 1);
        }

        mv.addObject("curr_user", curr_user);

        Integer saleorderId = saleorder.getSaleorderId();
        // 获取订单基本信息
        saleorder.setOptType("orderDetail");
        saleorder = saleorderService.getBaseSaleorderInfo(saleorder);

        //------------------分批开票--------------------------------

        //若订单状态为已完成，但开票状态为“未开票”或“部分开票”，仍然显示“申请修改”的按钮，只有收票信息模块可修改，其他内容不可修改。
        Integer status = saleorder.getStatus();
        Integer invoiceStatus = saleorder.getInvoiceStatus();
        if(status.equals(2)&&invoiceStatus!=2){
            mv.addObject("invoiceModifyflag",1);
        }
        //------------------分批开票--------------------------------

        Map<String,BigDecimal> moneyData=saleorderService.getSaleorderDataInfo(saleorderId);
        if(moneyData!=null&&moneyData.get("realAmount")!=null){
            mv.addObject("realAmount",moneyData.get("realAmount"));
        }
        try {
            // 根据客户ID查询客户信息
            TraderCustomerVo groupCustomer = traderCustomerService.getCustomerBussinessInfo(saleorder.getGroupCustomerId());
            TraderCustomerVo customer = traderCustomerService.getCustomerBussinessInfo(saleorder.getTraderId());
            Trader parentTrader = traderMapper.getTraderByTraderId(saleorder.getGroupCustomerId());
            mv.addObject("parentOfGroupCustomer", parentTrader == null ? 0 : parentTrader.getParentId());
            mv.addObject("groupCustomer", groupCustomer);// 客户信息
            mv.addObject("customer",customer);// 收票
            //获取产品信息
            Saleorder sale = new Saleorder();
            sale.setSaleorderId(saleorderId);
            sale.setTraderId(saleorder.getTraderId());
            sale.setCompanyId(curr_user.getCompanyId());
            sale.setReqType(1);
            List<SaleorderGoods> saleorderGoodsList = saleorderService.getSaleorderGoodsById(sale);

            List<ExpressDetail> expresseList = expressService.getSEGoodsNum(saleorderGoodsList.stream().map(SaleorderGoods::getSaleorderGoodsId).collect(Collectors.toList()));
            List<SaleOrderGoodsDetailDto> allVirtualGoods = saleOrderGoodsApiService.findAllVirtualGoodsBySaleorderId(saleorder.getSaleorderId());
            for (SaleOrderGoodsDetailDto allVirtualGood : allVirtualGoods) {
                for (ExpressDetail expressDetail : expresseList) {
                    if (allVirtualGood.getSaleorderGoodsId().equals(expressDetail.getSaleOrderGoodsId())){
                        if (allVirtualGood.getArrivalStatus() ==2){
                            expressDetail.setArriveNum(allVirtualGood.getNum());
                        }
                        break;
                    }
                }
            }
            mv.addObject("expresseList", expresseList);

            try {
                setSkuAuthorizationVo(saleorderGoodsList);
            } catch (Exception e) {
                logger.error("检索sku报备信息error", e);
            }

            mv.addObject("regions", regionService.getRegionByParentId(1));
            mv.addObject("terminalTypes", skuAuthorizationService.getAllTerminalTypes());
            //-------------风控------------------
            riskCheckService.setSaleorderIsRiskInfo(saleorder, saleorderGoodsList);
            Integer saleorderIsRisk = saleorder.getIsRisk();
            //获取风控申请审核按钮flag
            Integer riskFlag = riskCheckService.getRiskFlag(curr_user, saleorderIsRisk);
            mv.addObject("riskFlag",riskFlag);
            //-------------风控------------------
            replaceExpenseBuyDetails(saleorderGoodsList);
            mv.addObject("saleorderGoodsList", saleorderGoodsList);

            //----------------------------------------报价咨询信息--------------------------------
            // 产品结算价
            saleorderGoodsList = goodsSettlementPriceService
                    .getGoodsSettlePriceBySaleorderGoodsList(curr_user.getCompanyId(), saleorderGoodsList);
            // 计算核价信息
            if(customer!=null) {
                saleorderGoodsList = goodsChannelPriceService.getSaleChannelPriceList(saleorder.getSalesAreaId(),
                        saleorder.getCustomerNature(), customer.getOwnership(), saleorderGoodsList);
            }


            //添加产品的成本价
            //goodsChannelPriceService.setGoodsReferenceCostPrice(saleorderGoodsList);
            mv.addObject("saleorderGoodsList", saleorderGoodsList);

            mv.addObject("loginUserId", curr_user.getUserId() + ";");

            // add by Tomcat.Hui 2019/11/28 15:40 .Desc: VDERP-1325 分批开票 已开票数量/开票中数量/该订单该sku的数量. start
            //已开票数量 已申请数量
            Map<Integer , Map<String, Object>> taxNumsMap = invoiceService.getInvoiceNums(saleorder);
            if (null != taxNumsMap) {
                saleorderGoodsList.forEach(g -> {
                    g.setAppliedNum(new BigDecimal(taxNumsMap.get(g.getSaleorderGoodsId()).get("APPLY_NUM").toString()));
                    g.setInvoicedNum(new BigDecimal(taxNumsMap.get(g.getSaleorderGoodsId()).get("INVOICE_NUM").toString()));
                    g.setInvoicedAmount(new BigDecimal(taxNumsMap.get(g.getSaleorderGoodsId()).get("INVOICE_AMOUNT").toString()));
                    g.setAppliedAmount(new BigDecimal(taxNumsMap.get(g.getSaleorderGoodsId()).get("APPLY_AMOUNT").toString()));
                });
            }

            saleorder = saleorderService.updateisOpenInvoice(saleorder,saleorderGoodsList);//更改申请开票按钮显示逻辑
            // add by Tomcat.Hui 2019/11/28 15:40 .Desc: VDERP-1325 分批开票 已开票数量/开票中数量/该订单该sku的数量. end


            mv.addObject("saleorderGoodsList", saleorderGoodsList);

            //新商品流切换 VDERP-1970 add by brianna 2020/3/16 start
            List<Integer> skuIds = new ArrayList<>();
            saleorderGoodsList.forEach(saleGood -> skuIds.add(saleGood.getGoodsId()));
            List<Map<String,Object>> skuTipsMap = vGoodsService.skuTipList(skuIds);
            Map<String,Map<String,Object>> newSkuInfosMap = skuTipsMap.stream().collect(Collectors.toMap(key->key.get("SKU_NO").toString(), v -> v, (k, v) -> v));
            mv.addObject("newSkuInfosMap", newSkuInfosMap);
            //新商品流切换 VDERP-1970 add by brianna 2020/3/16 end


            List<String> skuNos = saleorderGoodsList.stream().map(SaleorderGoods::getSku).collect(Collectors.toList());

            //批量查看sku的价格信息
            List<PriceInfoResponseDto> priceInfoResponseDtos = this.basePriceService.batchFindPriceInfo(skuNos,saleorder.getTraderId());

            //核价信息应用 add by brianna 2020/5/29 start
            List<GoodSalePrice> goodSalePriceList = saleorderGoodsList.stream()
                    .map(
                            saleorderGood -> {
                                return new GoodSalePrice(saleorderGood.getSku(),saleorderGood.getChannelPrice());
                            }
                    ).collect(Collectors.toList());

            Map<String,String> skuNoAndPriceMap = priceInfoDealWithService.dealWithGoodsSalePrice(saleorder.getTraderId(),goodSalePriceList,priceInfoResponseDtos);
            mv.addObject("skuNoAndPriceMap", skuNoAndPriceMap);
            //核价信息应用 add by brianna 2020/5/29 end

            //处理销售单的成本价
            priceInfoDealWithService.dealWithCostPrice(saleorderGoodsList,priceInfoResponseDtos);

            if(saleorder.getTraderId()!=null) {
                User user;
                // 销售人员名称
                user = userService.getUserById(saleorder.getUserId());
                saleorder.setSalesName(user == null ? "" : user.getUsername());
                // 创建人员
                if (SaleOrderTypeEnum.JCO.getType().equals(saleorder.getOrderType())) {
                    TraderContactGenerate traderContactQuery = traderContactService.selectByPrimaryKey(saleorder.getCreator());
                    saleorder.setCreatorName(traderContactQuery == null ? "" : traderContactQuery.getName() + " (前台登录客户)");
                } else {
                    user = userService.getUserById(saleorder.getCreator());
                    saleorder.setCreatorName(user == null ? "" : user.getUsername());
                }

                if(sale.getTraderId()!=null && saleorder.getTraderId()>0) {
                    // 归属销售
                    user = userService.getUserByTraderId(saleorder.getTraderId(), 1);// 1客户，2供应商
                    saleorder.setOptUserName(user == null ? "" : user.getUsername());
                    saleorder.setOptUserId(user == null ? 0 : user.getUserId());
                    // 销售部门（若一个多个部门，默认取第一个部门）
                    User userOrg = orgService.getTraderUserAndOrgByTraderId(saleorder.getTraderId(), 1);
                    saleorder.setSalesDeptName(userOrg == null ? "" : userOrg.getOrgName());
                }else {
                    user = userService.getUserById(saleorder.getUserId());
                    saleorder.setOptUserName(user == null ? "" : user.getUsername());
                    saleorder.setOptUserId(user == null ? 0 : user.getUserId());
                    Organization org = orgService.getOrgNameByUserId(saleorder.getUserId());
                    saleorder.setSalesDeptName(org == null ? "" : org.getOrgName());
                }


                // 获取订单客户信息
                saleorder.setCustomerTypeStr(getSysOptionDefinition(saleorder.getCustomerType()).getTitle());
                // 客户性质
                saleorder.setCustomerNatureStr(getSysOptionDefinition(saleorder.getCustomerNature()).getTitle());

            }
            Page page = Page.newBuilder(null, null, null);

            // 开票申请
            List<InvoiceApply> saleInvoiceApplyList = invoiceService.getSaleInvoiceApplyList(saleorder.getSaleorderId(),
                    SysOptionConstant.ID_505, -1);
            mv.addObject("saleInvoiceApplyList", saleInvoiceApplyList);

            // 获取发票信息
            List<Invoice> saleInvoiceList = invoiceService.getInvoiceInfoByRelatedId(saleorder.getSaleorderId(),
                    SysOptionConstant.ID_505);
            mv.addObject("saleInvoiceList", saleInvoiceList);

            // 终端类型
            saleorder.setTerminalTraderTypeStr(getSysOptionDefinition(saleorder.getTerminalTraderType()).getTitle());

            if (null == customer && saleorder.getCustomerNature()!=null&& saleorder.getCustomerNature().equals(466)) {
                // 根据客户ID查询客户信息
                TraderCustomerVo customer2 = traderCustomerService.getCustomerInfo(saleorder.getTraderId());
                // 客户类型
                saleorder.setTerminalTraderName(customer2.getTraderName());
                // 客户性质
                saleorder.setTerminalTraderTypeStr(customer2.getCustomerTypeStr());
            }

            // 所有产品的手填成本总价1
            mv.addObject("totalReferenceCostPrice", sale.getFiveTotalAmount());

            if (saleorderGoodsList.size() > 0) {
                Express express = new Express();
                express.setBusinessType(SysOptionConstant.ID_496);
                express.setCompanyId(curr_user.getCompanyId());
                List<Integer> relatedIds = new ArrayList<Integer>();
                for (SaleorderGoods sg : saleorderGoodsList) {
                    relatedIds.add(sg.getSaleorderGoodsId());
                }
                express.setRelatedIds(relatedIds);

                try {

                    //判断当前用户是否是质量专员
                    Boolean qualityFlagByUserId = userService.getQualityFlagByUserId(curr_user);
                    if (qualityFlagByUserId){
                        mv.addObject("qualityFlagByUserId", 1);
                    }else {
                        mv.addObject("qualityFlagByUserId", 0);
                    }

                    // 特殊商品集合
                    List<Integer> specialSkuIdList = getSysOptionDefinitionList(SysOptionConstant.SPECIAL_SKU)
                            .stream()
                            .map(SysOptionDefinition::getComments)
                            .map(Integer::valueOf)
                            .collect(Collectors.toList());
                    mv.addObject("specialSkuIdList", specialSkuIdList);

                    if (saleorderGoodsList != null && saleorderGoodsList.size() > 0) {
                        Express express1 = new Express();
                        express1.setBusinessType(SysOptionConstant.ID_496);
                        express1.setCompanyId(ErpConst.NJ_COMPANY_ID);
                        express1.setSaleorderId(saleorder.getSaleorderId());
                        //发货状态
                        Integer deliveryStatus = saleorder.getDeliveryStatus();
                        List<Integer> relatedIds1 = new ArrayList<Integer>();
                        for (SaleorderGoods sg : saleorderGoodsList) {
                            relatedIds1.add(sg.getSaleorderGoodsId());
                        }
                        express1.setRelatedIds(relatedIds1);
                        List<Express> expressList = expressService.getExpressListNewConfirm(express1);

                            //老单子
                        int displayPrUpFs = 0;
                        mv.addObject("displayPrUpFs", displayPrUpFs);

                        mv.addObject("expressList", expressList);

                        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 start
                        if(!CollectionUtils.isEmpty(expressList)){

                            List<Integer> expressGoodsId = new ArrayList<>();

                            expressList.forEach(expressItem->{
                                if(!CollectionUtils.isEmpty(expressItem.getExpressDetail())){
                                    expressItem.getExpressDetail().forEach(expressDetail -> {
                                        expressGoodsId.add( expressDetail.getGoodsId());
                                    });
                                }
                            });

                            if(CollectionUtils.isNotEmpty(expressGoodsId)){
                                List<CoreSpuGenerate> spuLists = this.vGoodsService.findSpuNamesBySpuIds(expressGoodsId);
                                Map<Integer,String> spuMap = spuLists.stream().collect(Collectors.toMap(k->k.getSpuId(), v -> v.getSpuName(), (k, v) -> v));
                                mv.addObject("spuMap", spuMap);
                            }
                        }
                        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 end
                    }




                } catch (Exception e) {
                    logger.error(Contant.ERROR_MSG, e);
                }
            }


            // 付款方式列表
            List<SysOptionDefinition> paymentTermList = getSysOptionDefinitionList(SysOptionConstant.ID_418);
            mv.addObject("paymentTermList", paymentTermList);

            // 发货方式
            List<SysOptionDefinition> deliveryTypes = getSysOptionDefinitionList(480);
            mv.addObject("deliveryTypes", deliveryTypes);

            // 获取物流公司列表
            List<Logistics> logisticsList = getLogisticsList(curr_user.getCompanyId());
            mv.addObject("logisticsList", logisticsList);

            // 运费说明
            List<SysOptionDefinition> freightDescriptions = getSysOptionDefinitionList(469);
            mv.addObject("freightDescriptions", freightDescriptions);
            // 发票类型
            List<SysOptionDefinition> invoiceTypes = getSysOptionDefinitionList(428);
            mv.addObject("invoiceTypes", invoiceTypes);
            // 沟通类型为商机和报价和销售订单
            CommunicateRecord cr = new CommunicateRecord();
            if (saleorder.getQuoteorderId() != null && saleorder.getQuoteorderId() != 0) {
                cr.setQuoteorderId(saleorder.getQuoteorderId());
            }
            if (saleorder.getBussinessChanceId() != null && saleorder.getBussinessChanceId() != 0) {
                cr.setBussinessChanceId(saleorder.getBussinessChanceId());
            }
            cr.setSaleorderId(saleorder.getSaleorderId());
            cr.setCompanyId(curr_user.getCompanyId());
            List<CommunicateRecord> communicateList = traderCustomerService.getCommunicateRecordListPage(cr, page);
            if (!communicateList.isEmpty()) {
                // 沟通内容
                mv.addObject("communicateList", communicateList);
                mv.addObject("page", page);
            }

            // 获取订单合同回传&订单送货单回传列表
            List<Attachment> saleorderAttachmentList = saleorderService.getSaleorderAttachmentList(saleorderId);
            mv.addObject("saleorderAttachmentList", saleorderAttachmentList);

            // 录保卡
            List<SaleorderGoodsWarrantyVo> goodsWarrantys = saleorderService.getSaleorderGoodsWarrantys(saleorderId);
            mv.addObject("goodsWarrantys", goodsWarrantys);

            // 售后订单列表
            AfterSalesVo as = new AfterSalesVo();
            as.setOrderId(saleorderId);
            as.setSubjectType(535);
            List<AfterSalesVo> asList = afterSalesOrderService.getAfterSalesVoListByOrderId(as);
            mv.addObject("asList", asList);

            // 获取交易信息数据
            CapitalBill capitalBill = new CapitalBill();
            capitalBill.setOperationType("finance_sale_detail");
            CapitalBillDetail capitalBillDetail = new CapitalBillDetail();
            capitalBillDetail.setOrderType(ErpConst.ONE);// 销售订单类型
            capitalBillDetail.setOrderNo(saleorder.getSaleorderNo());
            capitalBillDetail.setRelatedId(saleorderId);
            capitalBill.setCapitalBillDetail(capitalBillDetail);
            List<CapitalBill> capitalBillList = capitalBillService.getCapitalBillList(capitalBill);
            mv.addObject("capitalBillList", capitalBillList);

            // 资金流水交易方式
            List<SysOptionDefinition> traderModes = getSysOptionDefinitionList(519);
            mv.addObject("traderModes", traderModes);

            // 资金流水业务类型
            List<SysOptionDefinition> bussinessTypes = getSysOptionDefinitionList(524);
            mv.addObject("bussinessTypes", bussinessTypes);
            // 订单修改申请列表（不分页）
            SaleorderModifyApply saleorderModifyApply = new SaleorderModifyApply();
            saleorderModifyApply.setSaleorderId(saleorderId);
            List<SaleorderModifyApply> saleorderModifyApplyList = saleorderService
                    .getSaleorderModifyApplyList(saleorderModifyApply);
            mv.addObject("saleorderModifyApplyList", saleorderModifyApplyList);

            // 查询当前订单的一些状态
            if(saleorder.getTraderId()!=null) {
                // 1客户，2供应商
                User userInfo = userService.getUserByTraderId(saleorder.getTraderId(), 1);
                saleorder.setOptUserName(userInfo == null ? "" : userInfo.getUsername());
                saleorder.setOptUserId(userInfo == null ? 0 : userInfo.getUserId());
            }
            Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
                    "saleorderVerify_" + saleorderId);
            mv.addObject("taskInfo", historicInfo.get("taskInfo"));
            mv.addObject("startUser", historicInfo.get("startUser") != null ?
                    getRealNameByUserName( historicInfo.get("startUser").toString())  : null);
            // 最后审核状态
            mv.addObject("endStatus", historicInfo.get("endStatus"));
            List<HistoricActivityInstance> historicActivityInstances = setAssignRealNames(mv, historicInfo);
            Map<String, Object> commentMap = (Map<String, Object>) historicInfo.get("commentMap");

            boolean permoissionsFlag = false;
            if (historicInfo.get("startUser") != null){
                User startUser = userService.getByUsername(historicInfo.get("startUser").toString(), 1);
                permoissionsFlag = startUser != null ?  riskCheckService.isOrgFlag(startUser, ErpConst.QUALITY_ORG) : false;
            }
            mv.addObject("permoissionsFlag", permoissionsFlag);

            mv.addObject("historicActivityInstance", historicActivityInstances);

            mv.addObject("commentMap", commentMap);
            mv.addObject("candidateUserMap", historicInfo.get("candidateUserMap"));

            //查看订单的审核状态
            VerifiesInfo verifiesInfoQuery = new VerifiesInfo();
            verifiesInfoQuery.setRelateTable("T_SALEORDER");
            verifiesInfoQuery.setRelateTableKey(saleorderId);
            List<VerifiesInfo> verifiesInfosOfSaleorder = verifiesInfoMapper.getVerifiesInfo(verifiesInfoQuery);
            int orderCheckStatus = CollectionUtils.isEmpty(verifiesInfosOfSaleorder) ? -1 : verifiesInfosOfSaleorder.get(0).getStatus();
            mv.addObject("orderCheckStatus",orderCheckStatus);

            Task taskInfo = (Task) historicInfo.get("taskInfo");
            // 当前审核人
            String verifyUsers = null;
            List<String> verifyUserList = new ArrayList<>();
            if (null != taskInfo) {
                Map<String, Object> taskInfoVariables = actionProcdefService.getVariablesMap(taskInfo);
                verifyUsers = getVerifyUserRealNames((String) taskInfoVariables.get("verifyUsers"));
                String verifyUser = getVerifyUserRealNames((String) taskInfoVariables.get("verifyUserList"));
                if (null != verifyUser) {
                    verifyUserList = Arrays.asList(verifyUser.split(","));
                }
            }
            List<String> verifyUsersList = new ArrayList<>();
            if (verifyUsers != null && !verifyUserList.isEmpty()) {
                verifyUsersList = Arrays.asList(verifyUsers.split(","));
            }
            mv.addObject("verifyUsers", getVerifyUserRealNames(verifyUsers));
            mv.addObject("verifyUserList", verifyUserList);
            mv.addObject("verifyUsersList", verifyUsersList);
            // 提前采购审核信息
            Map<String, Object> historicInfoEarly = actionProcdefService.getHistoric(processEngine,
                    "earlyBuyorderVerify_" + saleorderId);
            mv.addObject("taskInfoEarly", historicInfoEarly.get("taskInfo"));
            mv.addObject("startUserEarly", historicInfoEarly.get("startUser"));
            mv.addObject("endStatusEarly", historicInfoEarly.get("endStatus"));
            mv.addObject("historicActivityInstanceEarly", historicInfoEarly.get("historicActivityInstance"));
            mv.addObject("commentMapEarly", historicInfoEarly.get("commentMap"));
            mv.addObject("candidateUserMapEarly", historicInfoEarly.get("candidateUserMap"));
            Task taskInfoEarly = (Task) historicInfoEarly.get("taskInfo");
            // 当前审核人
            String verifyUsersEarly = null;
            List<String> verifyUserListEarly = new ArrayList<>();
            if (null != taskInfoEarly) {
                Map<String, Object> taskInfoVariablesEarly = actionProcdefService.getVariablesMap(taskInfoEarly);
                verifyUsersEarly = (String) taskInfoVariablesEarly.get("verifyUsers");
                String verifyUserEarly = (String) taskInfoVariablesEarly.get("verifyUserList");
                if (null != verifyUserEarly) {
                    verifyUserListEarly = Arrays.asList(verifyUserEarly.split(","));
                }
            }
            List<String> verifyUsersListEarly = new ArrayList<>();
            if (verifyUsersEarly != null && !verifyUserListEarly.isEmpty()) {
                verifyUsersListEarly = Arrays.asList(verifyUsersEarly.split(","));
            }
            mv.addObject("verifyUsersEarly", verifyUsersEarly);
            mv.addObject("verifyUserListEarly", verifyUserListEarly);
            mv.addObject("verifyUsersListEarly", verifyUsersListEarly);

            // 合同回传审核信息
            Map<String, Object> historicInfoContractReturn = actionProcdefService.getHistoric(processEngine,
                    "contractReturnVerify_" + saleorderId);
            Task taskInfoContractReturn = (Task) historicInfoContractReturn.get("taskInfo");
            mv.addObject("taskInfoContractReturn", taskInfoContractReturn);
            mv.addObject("startUserContractReturn", historicInfoContractReturn.get("startUser") != null ?
                    getRealNameByUserName(historicInfoContractReturn.get("startUser").toString()) : null);
            // 最后审核状态
            mv.addObject("endStatusContractReturn", historicInfoContractReturn.get("endStatus"));


            List<HistoricActivityInstance> historicActivityInstanceContractReturn = setAssignRealNames(mv, historicInfoContractReturn);
            mv.addObject("historicActivityInstanceContractReturn", historicActivityInstanceContractReturn);

            mv.addObject("commentMapContractReturn", historicInfoContractReturn.get("commentMap"));
            mv.addObject("candidateUserMapContractReturn", historicInfoContractReturn.get("candidateUserMap"));
            String verifyUsersContractReturn = null;
            if (null != taskInfoContractReturn) {
                Map<String, Object> taskInfoVariablesContractReturn = actionProcdefService
                        .getVariablesMap(taskInfoContractReturn);
                verifyUsersContractReturn = getVerifyUserRealNames((String) taskInfoVariablesContractReturn.get("verifyUsers"));
            }
            mv.addObject("verifyUsersContractReturn", getRealNameByUserName(verifyUsersContractReturn));

            // 开票申请审核信息
            InvoiceApply invoiceApplyInfo = invoiceService.getInvoiceApplyByRelatedId(saleorderId,
                    SysOptionConstant.ID_505, saleorder.getCompanyId());
            mv.addObject("invoiceApply", invoiceApplyInfo);
            if (invoiceApplyInfo != null && curr_user.getCompanyId() != 1) {
                Map<String, Object> historicInfoInvoice = actionProcdefService.getHistoric(processEngine,
                        "invoiceVerify_" + invoiceApplyInfo.getInvoiceApplyId());
                mv.addObject("taskInfoInvoice", historicInfoInvoice.get("taskInfo"));
                mv.addObject("startUserInvoice", historicInfoInvoice.get("startUser"));
                mv.addObject("candidateUserMapInvoice", historicInfoInvoice.get("candidateUserMap"));
                // 最后审核状态
                mv.addObject("endStatusInvoice", historicInfoInvoice.get("endStatus"));
                mv.addObject("historicActivityInstanceInvoice", historicInfoInvoice.get("historicActivityInstance"));
                mv.addObject("commentMapInvoice", historicInfoInvoice.get("commentMap"));

                Task taskInfoInvoice = (Task) historicInfoInvoice.get("taskInfo");
                // 当前审核人
                String verifyUsersInvoice = null;
                if (null != taskInfoInvoice) {
                    Map<String, Object> taskInfoVariables = actionProcdefService.getVariablesMap(taskInfoInvoice);
                    verifyUsersInvoice = (String) taskInfoVariables.get("verifyUsers");
                }
                mv.addObject("verifyUsersInvoice", verifyUsersInvoice);
            }
            /*
             * //获取订单对应客户的余额信息 TraderCustomer traderCustomerInfo =
             * traderCustomerService.getTraderCustomerInfo(saleorder.getTraderId ());
             * mv.addObject("traderCustomerInfo", traderCustomerInfo);
             */
            // 获取交易信息（订单实际金额，客户已付款金额）
            Map<String, BigDecimal> saleorderDataInfo = saleorderService.getSaleorderDataInfo(saleorderId);
            mv.addObject("saleorderDataInfo", saleorderDataInfo);

            //设置售后退货锁定,锁品不锁单
            setOrderLockStatus(saleorder,saleorderGoodsList);
            //根据使用场景，订单id，订货号查出采购要求和供应商要求
            remarkComponentController.getRemarlComponentInfo(mv,saleorderGoodsList,saleorder,RemarkComponentSceneEnum.SALE_ORDER.getCode());
            /**
             * 确认单审核汇总
             */
            try {
                getConfirmationList(mv,saleorder);
            } catch (Exception e){
                logger.error("确认单审核汇总信息查询异常 saleOrderId:{},e:{}",saleorderId,e);
            }

        } catch (Exception e) {
            logger.error("sale order view:", e);
        }

        //以开票数量和待审核开票数量
//		Map<String, Object> invoiceNumMap =invoiceService.getInvoiceNum(saleorderId);

        // 计算是否是当月
        String nextMonthFirst = null;
        // 跨月表示（0表示未跨月 1表示跨月）
        Integer isCrossMonth = 0;
        try {
            //VDERP-1377
//			Long time = saleorder.getValidTime();//原来的规则是按照生效时间的当月
            Long time = saleorder.getPaymentTime();//现在的规则是按照全部付款的时间当月
            // 如果有生效时间
            if (time != null && time != 0) {
                nextMonthFirst = DateUtil
                        .getNextMonthFirst(DateUtil.convertString(time, "yyyy-MM-dd"));
                String nowTimeDate = DateUtil.getNowDate("yyyy-MM-dd");
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                // 下个月1号
                Date nmf = sdf.parse(nextMonthFirst);
                // 当前时间
                Date ntd = sdf.parse(nowTimeDate);
                // 当前时间小于 跨月时间
                if (!ntd.before(nmf)) {
                    if(saleorder.getPaymentStatus().equals(2)) {//全部付款
                        isCrossMonth = 1;
                    }
                }
            }
            //VDERP-1377
        } catch (ParseException e) {
            logger.error("sale order view parse error:", e);
        }

        //赋值不满足票货同行条件
        saleorder.setReasonNo(saleorderService.getIsSameAddressNoRe(saleorder));

        //是否是订单归属销售的直接上级
        User saler = userService.getUserById(saleorder.getOptUserId());
        if (saler != null && saler.getParentId().equals(curr_user.getUserId())){
            mv.addObject("roleType",1);
        } else {
            mv.addObject("roleType",0);
        }

        mv.addObject("isCrossMonth", isCrossMonth);
        //设置合同下载地址
        String contractUrl = "";
        if(saleorder.getContractUrl() != null && !"".equals(saleorder.getContractUrl())){
            //有合同章
            contractUrl = saleorder.getContractUrl().replace("display","download");
        }else {
            if(saleorder.getContractNoStampUrl() != null && !"".equals(saleorder.getContractNoStampUrl())){
                contractUrl = saleorder.getContractNoStampUrl().replace("display","download");
            }
        }
        //是否显示确认单功能
        Boolean needShowConfirm = true;
        if(confirmOnlineTimestamp != null && (confirmOnlineTimestamp.compareTo(saleorder.getAddTime()) < 0)){
            needShowConfirm = false;
        }
        mv.addObject("contractUrl",contractUrl);
        saleorder.setNeedShowConfirm(needShowConfirm);
        //VDERP-14599 电话显示null
        saleorder.setTakeTraderContactTelephone(Optional.ofNullable(saleorder.getTakeTraderContactTelephone()).orElse(""));
        mv.addObject("saleorder", saleorder);
        //订单创建人的职位信息
        if (StringUtils.isNotBlank(saleorder.getCreateMobile())){
            mv.addObject("platformOfOrderCreateMobile",jcOrderService.getRoleValueOfJcAccount(saleorder.getCreateMobile()));
        }
        mv.addObject("retentionMoneyUptime", retentionMoneyUptime);
        Boolean isBelongCurrentUserOrder =saleorder.getOptUserName().equals(curr_user.getUsername());
        //订单的归属销售是否是当前登录人
        mv.addObject("isBelongCurrentUserOrder",isBelongCurrentUserOrder);

        setQuoteInfo(saleorder, mv);//票货同行隐藏提前开票按钮VDERP-6537
        if(orderPeerApiService.getOrderIsGoodsPeer(saleorderId)){
            //满足票货同行条件--隐藏提前开票按钮(管理员可以看到)
            if(curr_user.getIsAdmin() == 1){
                mv.addObject("preOpenInvoice",1);//1展示
            }else {
                // 2021/9/23	临时修改	将0改为1，销售人员也可以展示提前开票按钮
                //mv.addObject("preOpenInvoice",0);//0不展示
                mv.addObject("preOpenInvoice",1);//1展示
            }
        }else {
            mv.addObject("preOpenInvoice",1);//0不展示
        }
        setQuoteInfo(saleorder, mv);

        // add by Randy.Xu 2021/6/15 15:35 .Desc:VDERP-6927 销售单详情页发货标签信息 . begin
        List<SpecialDeliveryInfoDto> specialDeliveryInfoDtoList =  saleorderService.getSpecialDeliveryInfo(saleorder);
        mv.addObject("specialDeliveryList",specialDeliveryInfoDtoList);
        // add by Randy.Xu 2021/6/15 15:35 .Desc: . end

        // 展示发送签收通知按钮
        Saleorder saleorderVo = new Saleorder();
        BeanUtils.copyProperties(saleorder,saleorderVo);
        // saleorderVo.setValidTime(DateUtil.StringToLong("2021-12-20 00:00:00"));
        Boolean pendingOrderFlag = saleorderService.getPendingOrderBySaleorder(saleorderVo);
        mv.addObject("pendingOrderFlag",pendingOrderFlag);
        mv.setViewName("/order/jc/view");
        //附加条款（新）
        getAdditionalClause(mv,saleorder);
        return mv;
    }
    private void getAdditionalClause(ModelAndView mv, Saleorder saleorderInfo) {
        SaleorderAdditionalClauseDto queryDto=new SaleorderAdditionalClauseDto();
        queryDto.setSaleorderId(saleorderInfo.getSaleorderId());
        SaleorderAdditionalClauseDto result=orderAdditionalClauseApiService.getSaleorderAdditionalClauseByOrderId(queryDto);
        saleorderInfo.setAdditionalClause(result.getAdditionalClauseFinalShow());
    }

    private void getConfirmationList(ModelAndView mv, Saleorder saleOrderBaseInfo) {
        List<ConfirmationDto> confirmationList = new ArrayList<>();
        Express express = new Express();
        Express exQueryVo = new Express();
        exQueryVo.setSaleorderId(saleOrderBaseInfo.getSaleorderId());
        Integer saleorderId = saleOrderBaseInfo.getSaleorderId();
        express.setSaleorderId(saleorderId);
        List<Express> expressList = expressMapper.getExpressInfoConfirmation(express);
        List<SaleorderGoods> saleorderGoods = saleorderGoodsMapper.getAllSaleorderGoodsBySaleorderId(saleorderId);
        List<Express> buyExpressList = new ArrayList<>();
        if (saleorderGoods.size()>0){
            List<Integer> listSale = saleorderGoods.stream().map(SaleorderGoods::getSaleorderGoodsId).collect(Collectors.toList());
            exQueryVo.setRelatedIds(listSale);
            buyExpressList = expressMapper.getBuyExpressList(exQueryVo);
        }
        expressList.addAll(buyExpressList);
        List<String> batchNos = expressList.stream().map(Express::getBatchNo).distinct().collect(Collectors.toList());
        if (batchNos.size()>0){
            List<OutboundBatchesRecode> allByBatchNoList = outboundBatchesRecodeMapper.findAllByBatchNoList(batchNos);
            allByBatchNoList.forEach(o -> {
                if (null != o.getAuditStatus() && (0 == o.getAuditStatus() || 1 == o.getAuditStatus() || 2 == o.getAuditStatus())) {
                    ConfirmationDto dto = new ConfirmationDto();
                    //普发
                    if(o.getBatchType().equals(1)){
                        Saleorder saleorder = new Saleorder();
                        saleorder.setBussinessType(2);
                        saleorder.setSaleorderId(saleorderId);
                        saleorder.setBatchNoComments(o.getBatchNo());
                        List<WarehouseGoodsOperateLog> warehouseOutList = warehouseOutService.getOutDetil(saleorder);
                        if (warehouseOutList.size()>0){
                            Long addTime = warehouseOutList.get(0).getAddTime();
                            dto.setBatchTime(DateUtil.convertString(addTime,null));
                        }
                    }
                    //直发
                    else {
                        dto.setBatchTime(DateUtil.convertString(o.getDeliveryTime(),"yyyy-MM-dd"));
                    }

                    if (StringUtils.isBlank(o.getComments())){
                        o.setComments("");
                    }
                    dto.setAuditStatus(o.getAuditStatus())
                            .setBatchId(o.getId())
                            .setBatchNo(o.getBatchNo())
                            .setModTime(o.getModTime())
                            .setModTimeLabel(DateUtil.convertString(o.getModTime(),null));
                    if (o.getAuditStatus()!=null && o.getAuditStatus().equals(0)){
                        dto.setComments(o.getComments());
                    }else {
                        dto.setComments("");
                    }
                    //确认单名称
                    List<String> confirmationNameList = confirmationFormRecodeMapper.getConfirmationNameByBatchNo(o.getBatchNo());
                    String confirmationName = String.join(",", confirmationNameList);
                    dto.setConfirmationName(confirmationName);
                    confirmationList.add(dto);
                }
            });
        }
        //排序
        List<ConfirmationDto> collect = confirmationList.stream()
                .sorted(Comparator.comparing(ConfirmationDto::getModTime)
                        .thenComparing(ConfirmationDto::getConfirmationName))
                .collect(Collectors.toList());
        mv.addObject("confirmationList",collect);
    }

    private void setOrderLockStatus(Saleorder saleorder, List<SaleorderGoods> saleorderGoodsList) {
        if(OrderConstant.LOCKED_REASON.equals(saleorder.getLockedReason())){
            boolean present = saleorderGoodsList.parallelStream().
                    anyMatch(saleorderGoods -> OrderConstant.LOCKED.equals(saleorderGoods.getLockedStatus()));
            if(present){
                saleorder.setLockedStatus(0);
                saleorder.setLockedReason("");
            }
        }
    }


    /**
     * 新增集采订单
     *
     * @return
     */
    @RequestMapping(method = RequestMethod.GET, value = "/addOrder")
    public ModelAndView addJcOrder() {
        ModelAndView modelAndView = new ModelAndView("/order/jc/add_order");
        modelAndView.addObject("scene",2);
        modelAndView.addObject("customerFromPlatformNo", BelongPlatformEnum.JC.getBelong());
        return  modelAndView;
    }

    /**
     * 保存新增的集采线下订单（保存订单联系人及基本信息）
     *
     * @param request
     * @param session
     * @param saleorder
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/saveJcOrderInfo", produces = MediaType.TEXT_HTML_VALUE)
    public ModelAndView saveJcOrderInfo(HttpServletRequest request, HttpSession session, Saleorder saleorder,@RequestParam(defaultValue = "2") Integer scene) {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.addObject("scene",scene);
        Saleorder result = null;
        try {
            result = jcOrderService.saveJcfOrderAfterSelectingCustomer(saleorder, request, session);
        } catch (Exception e) {
            LOGGER.error("保存集采线下订单时发生错误", e);
        }

        if (result != null) {
            modelAndView.addObject("url", "/order/saleorder/edit.do?saleorderId=" + result.getSaleorderId()
                    + "&extraType=" + result.getExtraType() +"&scene="+scene);
            return success(modelAndView);
        } else {
            return fail(modelAndView);
        }
    }


    /**
     * 保存订单详情信息
     *
     * @param request
     * @param session
     * @param saleorder
     * @return
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/saveeditsaleorderinfo")
    @SystemControllerLog(operationType = "edit", desc = "保存编辑的订单信息")
    public ModelAndView saveEditSaleorderInfo(HttpServletRequest request, HttpSession session, Saleorder saleorder) {
        ModelAndView modelAndView = new ModelAndView();
        if (checkSaleOrderStatus(saleorder.getSaleorderId())){
            modelAndView.addObject("message","订单状态已发生变化，请勿编辑订单");
            return fail(modelAndView);
        }
        Integer saleOrderId;
        try {
            saleOrderId = jcOrderService.saveJcfOrderDetail(saleorder, request, session);
        } catch (Exception e) {
            LOGGER.error("保存集采订单时发生错误:", e);
            return fail(modelAndView);
        }

        if (saleOrderId != null) {
            //发送微信消息
            JcWeChatModel jcWeChatModel = new JcWeChatModel(saleOrderId);
            jcWeChatArrService.sendTemplateMsgJcorder(jcWeChatModel, JcWeChatEnum.SAVE_ORDER,false);

            modelAndView.addObject("url", "/order/saleorder/view.do?saleorderId=" + saleOrderId);
            return success(modelAndView);
        } else {
            return fail(modelAndView);
        }
    }


    @NoNeedAccessAuthorization
    @ResponseBody
    @RequestMapping(method = RequestMethod.GET, value = "/getContactInfo", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResultInfo getContactInfo(Integer contactId) {
        JcTraderContactDto jcTraderContactDto;
        try {
            jcTraderContactDto = jcOrderService.getJcAccountInfo(contactId);
        } catch(IllegalStateException e) {
            return ResultInfo.error(e.getMessage());
        }
        return ResultInfo.success(jcTraderContactDto);
    }




    /**
     * 保存内部备注标签
     * @param labelDataDto 存储对象
     * @return 保存结果
     */
    @ResponseBody
    @RequestMapping(value = "/saveInsideComments")
    public ResultInfo<?> saveInsideComments(@RequestBody RemarkComponentDto labelDataDto) {
        log.info("保存内部备注标签 saveInsideComments -- {}",labelDataDto);
        return saleorderService.saveInsideComments(labelDataDto);
    }


    /**
     * 编辑内部备注标签
     * @param labelDataDto 存储对象
     * @return 保存结果
     */
    @ResponseBody
    @RequestMapping(value = "/updateInsideComments")
    public ResultInfo<?> updateInsideComments(@RequestBody RemarkComponentDto labelDataDto) {
        log.info("编辑内部备注标签 updateInsideComments -- {}",labelDataDto);
        ResultInfo<?> resultInfo = new ResultInfo<>();
        try{
            resultInfo = saleorderService.updateInsideComments(labelDataDto);
        }catch (Exception e){
            resultInfo = new ResultInfo<>(-1,e.getMessage());
        }
        return resultInfo;
    }



    /**
     * 订单产品添加页面
     *
     * @param request
     * @param session
     * @param jcGoodsQuery
     * @return
     */
    @NoNeedAccessAuthorization
    @RequestMapping(method = RequestMethod.GET, value = "/addJcOrderGoods", produces = MediaType.TEXT_HTML_VALUE)
    public ModelAndView addJcOrderGoods(HttpServletRequest request, HttpSession session, JcGoodsQuery jcGoodsQuery,
                                        @RequestParam(defaultValue = "2" ,required = false) Integer scene) {
        ModelAndView modelAndView = new ModelAndView("/order/jc/add_jcorder_goods");
        modelAndView.addObject("scene",scene);
        Map<String, Object> map = null;
        if (!Strings.isNullOrEmpty(jcGoodsQuery.getSearchContent()) || jcGoodsQuery.getGoodsBrandId() != null
                || jcGoodsQuery.getGoodsType() != null || jcGoodsQuery.getTypeSpecification() != null
                || jcGoodsQuery.getUnitName() != null) {
            Page page = getPageTag(request, jcGoodsQuery.getPageNo(), jcGoodsQuery.getPageSize());
            Goods goodsCondition = new Goods();
            goodsCondition.setCompanyId(ErpConst.NJ_COMPANY_ID);
            goodsCondition.setBrandId(jcGoodsQuery.getGoodsBrandId());
            goodsCondition.setGoodsType(jcGoodsQuery.getGoodsType());
            goodsCondition.setSearchContent(jcGoodsQuery.getSearchContent());
            goodsCondition.setSpecModel(jcGoodsQuery.getTypeSpecification());
            goodsCondition.setUnitName(jcGoodsQuery.getUnitName());
            map = goodsService.queryGoodsListPage(goodsCondition, page, session);
            modelAndView.addObject("typeSpecification", jcGoodsQuery.getTypeSpecification());
            modelAndView.addObject("goodsBrandIdValue", jcGoodsQuery.getGoodsBrandId());
            modelAndView.addObject("goodsTypeValue", jcGoodsQuery.getGoodsType());
            modelAndView.addObject("zxfunitNameValue", jcGoodsQuery.getUnitName());

        }
        if (map != null) {
            modelAndView.addObject("goodsList", map.get("list"));
            modelAndView.addObject("page", map.get("page"));
        }
        modelAndView.addObject("goodsTypeList", getSysOptionDefinitionInNewGoodsFlow());
        modelAndView.addObject("searchContent", jcGoodsQuery.getSearchContent());
        modelAndView.addObject("saleorderId", jcGoodsQuery.getSaleorderId());
        modelAndView.addObject("lastGoodsType", jcGoodsQuery.getGoodsType());
        modelAndView.addObject("lastGoodsBrandId", jcGoodsQuery.getGoodsBrandId());
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        Unit unit = new Unit();
        unit.setCompanyId(user.getCompanyId());
        List<Unit> unitList = unitService.getAllUnitList(unit);
        modelAndView.addObject("unitList", unitList);
        return modelAndView;
    }


    @NoNeedAccessAuthorization
    @RequestMapping(method = RequestMethod.GET, value = "/editSaleorderGoods",produces = MediaType.TEXT_HTML_VALUE)
    public ModelAndView editJcOrderGoods(SaleorderGoods saleorderGoods,
                                         @RequestParam(defaultValue = "0", required = false) Integer scene) {
        ModelAndView modelAndView = new ModelAndView("/order/jc/edit_jcorder_goods");
        modelAndView.addObject("scene",scene);
        Integer saleorderGoodsId = saleorderGoods.getSaleorderGoodsId();
        // 根据订单产品ID获取对应产品信息
        saleorderGoods = saleorderService.getSaleorderGoodsInfoById(saleorderGoodsId);
        // 根据订单商品ID货区对应的订单信息
        Saleorder saleorderTmp = new Saleorder();
        saleorderTmp.setSaleorderId(saleorderGoods.getSaleorderId());
        Saleorder saleorderInfo = saleorderService.getBaseSaleorderInfo(saleorderTmp);
        modelAndView.addObject("saleorder", saleorderInfo);
        modelAndView.addObject("saleorderGoods", saleorderGoods);

        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 start
        Map<String,Object> newSkuInfo = vgoodsService.skuTip(saleorderGoods.getGoodsId());
        modelAndView.addObject("newSkuInfo", newSkuInfo);
        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 end

        Map<String, String> priceInfoMap = getGoodsPrice(saleorderGoods.getSaleorderId(),saleorderGoods.getSku(), saleorderGoods.getPrice());
        modelAndView.addObject("priceInfoMap", priceInfoMap);
        modelAndView.addObject("orderType", saleorderInfo.getOrderType());

        LabelQuery labelQuery = new LabelQuery();
        labelQuery.setScene(scene);
        labelQuery.setRelationId(saleorderGoods.getSaleorderId());
        labelQuery.setSkuNo(saleorderGoods.getSku());
        labelQuery.setSkuName(saleorderGoods.getGoodsName());
        String componentHtml = remarkComponentService.getComponentHtml(labelQuery);
        modelAndView.addObject("componentHtml", componentHtml);
        return modelAndView;
    }


    @NoNeedAccessAuthorization
    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/checkGoodsRange", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResultInfo<?> checkOrderGoodsRepeat(Integer traderContactId) {
        if (traderContactId == null) {
            return ResultInfo.error("请求参数不为空");
        }
        JcTraderContactDto jcAccountInfo = jcOrderService.getJcAccountInfo(traderContactId);
        String allowedGoodsTypes = jcAccountInfo.getAllowedGoodsTypes();
        Map<String, Object> resultMap = Maps.newHashMapWithExpectedSize(2);
        resultMap.put("contactName", jcAccountInfo.getContactName());
        if (!Strings.isNullOrEmpty(allowedGoodsTypes)) {
            resultMap.put("allowedGoodsRange", allowedGoodsTypes);
        }
        return ResultInfo.success(resultMap);
    }


    @NoNeedAccessAuthorization
    @ResponseBody
    @RequestMapping(value = "/vailSaleorderGoodsRepeat", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResultInfo<?> vailJcOrderGoodsRepeat(Integer saleorderId, Integer goodsId,String skuNo) {
        if (saleorderId == null || goodsId == null|| Strings.isNullOrEmpty(skuNo)) {
            return ResultInfo.error("请求参数不为空");
        }
        SaleorderGoods saleorderGoods=new SaleorderGoods();
        saleorderGoods.setSaleorderId(saleorderId);
        saleorderGoods.setGoodsId(goodsId);
        if(saleorderService.vailSaleorderGoodsRepeat(saleorderGoods)) {
            return ResultInfo.error( "已有相同订货号的产品，不允许重复添加！");
        }
        Map<String, String> returnDateMap = getGoodsPrice(saleorderId, skuNo, null);
        return ResultInfo.success( "无相同订货号产品，允许添加！",returnDateMap);
    }


    private Map<String, String> getGoodsPrice(Integer saleOrderId, String  skuNo, BigDecimal defaultPrice) {
        Saleorder saleorder = saleorderService.getSaleOrderById(saleOrderId);

        Integer traderId = saleorder.getGroupCustomerId();

        String salePrice = "";
        String salePriceShow = "";
        String contractedGoodsFlag = CommonConstants.OFF.toString();

        //集采订单要查商品的协议价
        ContractPriceInfoDetailResponseDto contractPriceInfoDetail = this.basePriceService.findSkuContractInfoByCon(traderId, skuNo);
        //有合约价 就设置合约价
        if (contractPriceInfoDetail != null && contractPriceInfoDetail.getContractPrice() != null) {
            salePrice = contractPriceInfoDetail.getContractPrice().toPlainString();
            salePriceShow += (salePrice + "(协议价)");
            contractedGoodsFlag = CommonConstants.ON.toString();
        } else {
            //没有合约价,查基础价
            TraderCustomer traderCustomer = this.traderCustomerService.getTraderCustomerId(traderId);
            SkuPriceInfoDetailResponseDto skuPriceInfoDetailResponseDto = this.basePriceService.findSkuPriceInfoBySkuNo(skuNo);
            //已经核价就设置成本价
            if (skuPriceInfoDetailResponseDto != null && traderCustomer != null) {
                if(ErpConst.CUSTOME_RNATURE.equals(traderCustomer.getCustomerNature())){
                    //分销商
                    salePrice = skuPriceInfoDetailResponseDto.getDistributionPrice().toPlainString();
                    salePriceShow += (salePrice + "(经销价)");
                }else if(ErpConst.CUSTOME_INTERMIAL.equals(traderCustomer.getCustomerNature())){
                    //终端
                    salePrice = skuPriceInfoDetailResponseDto.getTerminalPrice().toPlainString();
                    salePriceShow += (salePrice + "(终端价)");
                }
            }
        }

        Map<String,String> returnMap = new HashMap<>();
        returnMap.put("limitPrice","false");
        if (defaultPrice == null) {
            returnMap.put("salePrice",salePrice);
        } else {
            returnMap.put("salePrice", defaultPrice.toString());
        }
        returnMap.put("salePriceShow",salePriceShow);
        returnMap.put("contractedGoodsFlag", contractedGoodsFlag);
        return returnMap;
    }


    @NoNeedAccessAuthorization
    @RequestMapping(value = "/addContact", produces = MediaType.TEXT_HTML_VALUE)
    public ModelAndView addContact(HttpServletRequest request) {
        ModelAndView mv = new ModelAndView("/order/jc/add_contact");
        String traderId = request.getParameter("traderId");
        String traderCustomerId = request.getParameter("traderCustomerId");
        String indexId = request.getParameter("indexId");
        mv.addObject("traderId", traderId);
        mv.addObject("traderCustomerId", traderCustomerId);
        mv.addObject("indexId", indexId);
        return mv;
    }

    /**
     * <b>Description:</b><br>
     * 添加客户地址
     *
     * @param request
     * @return
     * @Note <b>Author:</b> leo.yang <br>
     *       <b>Date:</b> 2017年7月7日 上午11:49:32
     */
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/addAddress",produces = MediaType.TEXT_HTML_VALUE)
    public ModelAndView addAddress(HttpServletRequest request, Integer traderId) {
        ModelAndView mv = new ModelAndView("/order/jc/add_address");
        // 省级地区
        List<Region> provinceList = regionService.getRegionByParentId(1);
        String indexId = request.getParameter("indexId");
        mv.addObject("traderId", traderId);
        mv.addObject("provinceList", provinceList);
        mv.addObject("indexId", indexId);
        return mv;
        }



    /**
     * 根据客户ID获取联系人列表&地址列表
     *
     * @param traderId
     * @return
     * @Note <b>Author:</b> leo.yang <br>
     *       <b>Date:</b> 2017年8月14日 下午1:20:36
     */
    @ResponseBody
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/getCustomerContactAndAddress")
    public ResultInfo<?> getCustomerContactAndAddress(Integer traderId) {
        List<TraderContactGenerate> traderContactGenerates = jcOrderService.listContactWithGroupCustomer(traderId);
        ResultInfo resultInfo = ResultInfo.success();
        resultInfo.setParam(Collections.singletonMap("contactList", traderContactGenerates));
        return resultInfo;
    }

    @ResponseBody
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/isSameAddressSubmitCheck")
    public ResultInfo<String> isSameAddressSubmitCheck(Integer saleorderId) {

        List<SaleorderGoods> saleorderGoods= saleorderService.getSaleorderGoodsBySaleorderId(saleorderId);
        //不满足票货同行原因ID
        String reasonNo = "";
        if (CollectionUtils.isNotEmpty(saleorderGoods)){
           //订单中全部商品的发货方式为“普发”
            for (int i = 0; i < saleorderGoods.size(); i++) {
                if (saleorderGoods.get(i).getDeliveryDirect()== 1){
                    reasonNo = reasonNo + "3";
                    break;
                }
            }
        }
        //订单不存在非“已关闭”状态的“销售订单退货”或“销售订单退票”的售后单
        int closedAndOtherOrder = saleorderService.getClosedAndOtherOrder(saleorderId);

        if (closedAndOtherOrder > 0){
            reasonNo = reasonNo + "4";
        }

        return new ResultInfo<>(0,"查询成功",reasonNo);
    }


    @RequestMapping(value = "/returnTipsPage")
    @NoNeedAccessAuthorization
    public ModelAndView returnTipsPage(String reasonNo,Integer validStatus){
        ModelAndView mv = new ModelAndView("/order/jc/isSameAddressTipsPage");
        mv.addObject("validStatus",validStatus);
        mv.addObject("reasonNo",reasonNo);
        return mv;
    }

    private void replaceExpenseBuyDetails(List<SaleorderGoods> saleorderGoodsList){
        List<Integer> ids = saleorderGoodsList.stream().map(SaleorderGoods::getSaleorderGoodsId).collect(Collectors.toList());
        //需要替换交付信息的的虚拟商品List
        List<ExpenseBuyForSaleDetail> details = rBuyorderExpenseJSaleorderService.replaceExpenseBuyDetails(ids);
        for (ExpenseBuyForSaleDetail detail : details) {
            for (SaleorderGoods saleorderGoods : saleorderGoodsList) {
                if (detail.getSaleorderGoodsId().equals(saleorderGoods.getSaleorderGoodsId())){
                    saleorderGoods.setBuyDockUserName(detail.getBuyDockUserName());
                    saleorderGoods.setBuyOrderDemand(detail.getBuyorderDemand());
                    saleorderGoods.setBuyProcessModTimeString(detail.getBuyProcessModTimeString());
                    saleorderGoods.setBindBuyOrderId(detail.getBuyorderNo());
                    saleorderGoods.setBuyorderStatus(detail.getBuyorderStatus());
                    //虚拟订单页面展示标记
                    saleorderGoods.setBindedBuyOrder(2);
                    break;
                }
            }
        }
    }
}
