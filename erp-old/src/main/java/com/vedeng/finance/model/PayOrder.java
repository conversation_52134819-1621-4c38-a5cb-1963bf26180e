/*
 * Copyright 2018 Focus Technology, Co., Ltd. All rights reserved.
 */
package com.vedeng.finance.model;

import java.math.BigDecimal;

/**
 * PayOrder.java
 *
 * <AUTHOR>
 */
public class PayOrder {

    private Integer orderId;

    private String orderNo;

    private Integer orderStatus;

    private BigDecimal realTotalMoney;

    private Integer isPay;

    private Integer paymentMode;

    private Integer orderFlag;

    private Long addTime;

    private String goodsName;

    private String traderNo;// 支付宝/微信交易流水号

    private Long traderTime;// 支付宝/微信交易时间

    private Integer traderMode;// 31支付宝 32微信

    private Integer bussinessType;// 28订单收款 29订单退款

    private Integer payType;// 1支付宝、2微信、3银行

    private Integer capitalBillId;// 支付流水ID

    private Integer companyId;// 公司ID

    private Integer accountId;// ID

    private Integer traderType;// 交易类型1收入2支出3转移4转入5转出

    private String outRequestNo;// 退款单号

    private String afterSalesNo;// 售后单号

    public Integer getOrderId() {
	return orderId;
    }

    public void setOrderId(Integer orderId) {
	this.orderId = orderId;
    }

    public String getOrderNo() {
	return orderNo;
    }

    public void setOrderNo(String orderNo) {
	this.orderNo = orderNo;
    }

    public Integer getOrderStatus() {
	return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
	this.orderStatus = orderStatus;
    }

    public BigDecimal getRealTotalMoney() {
	return realTotalMoney;
    }

    public void setRealTotalMoney(BigDecimal realTotalMoney) {
	this.realTotalMoney = realTotalMoney;
    }

    public Integer getIsPay() {
	return isPay;
    }

    public void setIsPay(Integer isPay) {
	this.isPay = isPay;
    }

    public Integer getPaymentMode() {
	return paymentMode;
    }

    public void setPaymentMode(Integer paymentMode) {
	this.paymentMode = paymentMode;
    }

    public Integer getOrderFlag() {
	return orderFlag;
    }

    public void setOrderFlag(Integer orderFlag) {
	this.orderFlag = orderFlag;
    }

    public Long getAddTime() {
	return addTime;
    }

    public void setAddTime(Long addTime) {
	this.addTime = addTime;
    }

    public String getGoodsName() {
	return goodsName;
    }

    public void setGoodsName(String goodsName) {
	this.goodsName = goodsName;
    }

    public String getTraderNo() {
	return traderNo;
    }

    public void setTraderNo(String traderNo) {
	this.traderNo = traderNo;
    }

    public Long getTraderTime() {
	return traderTime;
    }

    public void setTraderTime(Long traderTime) {
	this.traderTime = traderTime;
    }

    public Integer getTraderMode() {
	return traderMode;
    }

    public void setTraderMode(Integer traderMode) {
	this.traderMode = traderMode;
    }

    public Integer getBussinessType() {
	return bussinessType;
    }

    public void setBussinessType(Integer bussinessType) {
	this.bussinessType = bussinessType;
    }

    public Integer getPayType() {
	return payType;
    }

    public void setPayType(Integer payType) {
	this.payType = payType;
    }

    public Integer getCapitalBillId() {
	return capitalBillId;
    }

    public void setCapitalBillId(Integer capitalBillId) {
	this.capitalBillId = capitalBillId;
    }

    public Integer getCompanyId() {
	return companyId;
    }

    public void setCompanyId(Integer companyId) {
	this.companyId = companyId;
    }

    public Integer getAccountId() {
	return accountId;
    }

    public void setAccountId(Integer accountId) {
	this.accountId = accountId;
    }

    public Integer getTraderType() {
	return traderType;
    }

    public void setTraderType(Integer traderType) {
	this.traderType = traderType;
    }

    public String getOutRequestNo() {
	return outRequestNo;
    }

    public void setOutRequestNo(String outRequestNo) {
	this.outRequestNo = outRequestNo;
    }

    public String getAfterSalesNo() {
	return afterSalesNo;
    }

    public void setAfterSalesNo(String afterSalesNo) {
	this.afterSalesNo = afterSalesNo;
    }

}
