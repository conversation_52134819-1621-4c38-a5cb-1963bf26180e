<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<!-- 引入样式 -->
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/vue/element-ui/css/index.css">
<!-- 引入脚本 -->
<script src="${pageContext.request.contextPath}/static/vue/element-ui/js/vue.js"></script>
<script src="${pageContext.request.contextPath}/static/vue/element-ui/js/ele-index.js"></script>

<style>
    .el-form-item__label {
        color: #999;
    }
</style>

<div id="app">
        <c:if test="${hasHistoryFeedback}">
            <div v-show="hasHistoryFeedback" style="font-size: small;background-color: #DEF1FF;height: 30px;padding-top: 15px;text-align: center">
                ${countInfo}
            </div>
        </c:if>
        <el-form :rules="rules" ref="communicateRecordForm" :model="registrationFeedbackDto" :label-position="labelPosition">
            <el-form-item label="注册证号/备案凭证号：" label-width="160px" prop="" style="padding-top: 25px">
            ${registrationNumber}
            </el-form-item>
            <el-form-item label="商品等级：" label-width="160px" prop="">
                ${levelName}
            </el-form-item>
            <el-form-item label="商品档位：" label-width="160px" prop="">
                ${positionName}
            </el-form-item>
            <el-form-item label="文件类型：" label-width="160px" prop="">
                ${fileTypeLabel}
            </el-form-item>
            <el-form-item label="问题类型：" label-width="160px" prop="problemType">
                <el-radio-group v-model="registrationFeedbackDto.problemType"  style="padding-top: 12px" @change="clear1">
                    <el-radio :label="1" >印章缺失/不清晰</el-radio>
                    <el-radio :label="2" >打印内容模糊</el-radio>
                    <el-radio :label="3" >水印内容不当</el-radio>
                    <el-radio :label="4" >已过期</el-radio>
                    <el-radio :label="5" >其他  <el-input :disabled="check1" size="small" maxlength="20" v-model="registrationFeedbackDto.problemRemark"></el-input></el-radio>
                </el-radio-group>
            </el-form-item>

            <el-form-item label="使用目的：" label-width="160px">
                <el-radio-group v-model="registrationFeedbackDto.purposeType" style="padding-top: 12px;padding-bottom: 4px" @change="clear2">
                    <el-radio :label="1">投标/陪标</el-radio>
                    <el-radio :label="2">客户询价</el-radio>
                    <el-radio :label="3">其他
                        <el-input maxlength="20" :disabled="check2" size="small" v-model="registrationFeedbackDto.purposeRemark"></el-input>
                    </el-radio>
                </el-radio-group>

            </el-form-item>

            <el-form-item label="归属产品经理：" label-width="160px" prop="">
                ${productMgrName}
            </el-form-item>
            <el-form-item label="归属产品助理：" label-width="160px" prop="">
                ${productAssistantName}
            </el-form-item>
        </el-form>
    <div style="text-align: center;font-size: small;margin-bottom: 28px;color: #C0C4CC">问题将以站内消息的方式反馈给归属产品经理和产品助理</div>
    <span style="display: block;text-align: center;">
            <el-button @click="reInitCommun()">取 消</el-button>
            <el-button :plain="true" type="primary" @click="saveRegistrationFeedback" :disabled="disabledForm">确 定</el-button>
    </span>
</div>

<script type="text/javascript">
    const viewInfo = {
        businessChanceId: '${bussinessChanceId}',
    };
    const registrationNumber = '${registrationNumber}'
    let vm0 = null
    const sendThis0 = (_this) => {
        vm0 = _this;
    };
    let vm = new Vue({
        el: '#app',
        data() {
            var dateTime = new Date().getTime();
            var dateTimeEnd = new Date(dateTime + 1000 * 60 * 2).getTime();
            return {
                check1: true,
                check2: true,
                hasHistoryFeedback: '${hasHistoryFeedback}',
                labelPosition: 'right',
                registrationFeedbackDto: {
                    'problemType': '',
                    'problemRemark': '',
                    'purposeType': '',
                    'purposeRemark': '',
                    'registrationNumberId': ${registrationNumberId},
                    'registrationNumber': '${registrationNumber}',
                    'goodsLevelNo': ${goodsLevelNo},
                    'goodsPositionNo': ${goodsPositionNo},
                    'fileType': ${fileType},
                    'assignmentManagerId': ${assignmentManagerId},
                    'assignmentAssistantId': ${assignmentAssistantId},
                    'firstEngageId': ${firstEngageId},
                    'levelName': '${levelName}',
                    'positionName': '${positionName}'
                },
                rules: {
                    'problemType': [
                        {required: true, message: '请选择问题类型', trigger: 'change'}
                    ]
                },
                disabledForm: false,
            }},
        methods: {
            saveRegistrationFeedback() {
                this.$refs['communicateRecordForm'].validate((valid) => {
                    if (valid){
                        this.disabledForm = true;
                        axios({
                            url: '/registrationFeedback/add.do',
                            method: 'post',
                            data: this.registrationFeedbackDto
                        }).then(res => {
                            if (res.data.code === 0) {
                                this.$message({
                                    message: '问题反馈提交成功',
                                    type: 'success'
                                });
                                setTimeout(() => {
                                    this.reInitCommun()
                                }, 1000)
                            }else {
                                this.$message({
                                    message: '提交失败，请重试',
                                    type: 'error'
                                })
                            }
                        }).finally(() =>{
                            setTimeout(() =>{
                                this.disabledForm = false;
                            },1000)
                        });
                    }
                });
            },
            reInitCommun() {
                layer.closeAll();
                parent.layer.closeAll();
            },
            clear1() {
                if (this.registrationFeedbackDto.problemType === 5){
                    this.check1 = false;
                }else {
                    this.registrationFeedbackDto.problemRemark = '';
                    this.check1 = true;
                }
            },
            clear2() {
                if (this.registrationFeedbackDto.purposeType === 3){
                    this.check2 = false;
                }else {
                    this.registrationFeedbackDto.purposeRemark = '';
                    this.check2 = true;
                }
            }

        }
        });
</script>

<style>
    .el-row {
        margin-bottom: 10px;
    }

    .el-descriptions {
        background-color: #e5f1ff;
        margin-bottom: 20px;
    }

    .el-descriptions__title {
        margin-left: 10px;
        margin-top: 10px;
    }

    .el-descriptions__header {
        margin-bottom: 10px;
    }

    .title-style {
        background-color: #e5f1ff;
        box-sizing: border-box;
        padding-left: 10px;
        font-size: 16px;
        font-weight: 700;
        color: #303133;
    }

    .el-descriptions-item__cell.el-descriptions-item__label.is-bordered-label {
        text-align: center;
        width: 10%;
    }

    .cell {
        text-align: center;
    }

    .el-descriptions-item__cell.el-descriptions-item__content {
        text-align: center !important;
    }

    .my-content {
        width: 40%;
    }

    .el-tag{
        white-space: normal;
        height:auto;
    }


</style>