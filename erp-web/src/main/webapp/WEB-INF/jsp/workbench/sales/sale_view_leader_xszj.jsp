<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="商机工作台" scope="application" />
<%@ include file="../../common/common.jsp" %>


<script type="text/javascript"
        src='${pageContext.request.contextPath}/static/js/datacenter/sale/index_saleuser.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/libs/jquery/plugins/DatePicker/WdatePicker.js"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/echarts/echarts-4.9.min.js?rnd=${resourceVersionKey}"></script>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>

<link rel="stylesheet"
      href="${pageContext.request.contextPath}/static/fonts/font-awesome-4.7.0/css/font-awesome.min.css" media="all">

<style>
    th {
        height: 20px;
        line-height: 20px;
    }

    .layui-table td, .layui-table th {
        text-align: center;
    }

    .layui-colla-icon {
        right: 15px;
        left: auto;
    }

    .layui-colla-title {
        padding: 0 15px 0 15px;
    }

    .panel-title-left2 {
        float: left;
        line-height: 50px;
        color: #18C3B1 !important;
    }

    .panel-title-left {
        float: left;
        color: #18C3B1;
        margin-left: 5px;
        line-height: 42px;
    }

    i {
        background: none !important;;
    }

    .card-content {
        height: 56px !important;
        text-align: center !important;

    }

    .card-container {
        height: 100px !important;
        border-radius: 10px;
        width: 80% !important;
        margin-top: 10px;
        margin-bottom: 10px;
    }

    .layui-tab-item {
        height: 300px;
        overflow: scroll;
    }

    .table .fa-phone {
        color: #85d260;
    }

    .bottomtitle {
        color: #AAAAAA;

        font-family: 'Arial Normal', 'Arial', sans-serif;
        font-weight: 400;
        font-style: normal;
        font-size: 12px;
        letter-spacing: normal;
        line-height: 100%;
    }

    .addtitle {
        color: #3384ef;
    }


</style>


<script type="text/javascript"
        src='${pageContext.request.contextPath}/static/js/workbench/sale_view_leader_xszj.js?rnd=${resourceVersionKey}'></script>

<div class="main-container">
    <div class="layui-container" style="width: 100%">
        <%--商机概况的div--%>
        <div class="module-container layui-row " style="margin-bottom: 25px">
            <div class="layui-collapse">
                <div class="layui-colla-item">
                    <div class="layui-colla-title">
                        <span class="panel-title-left2"> <i class="fa  fa-2x fa-bar-chart"></i></span>
                        <span class="panel-title-left">
                       商机概况</span>
                    </div>
                    <%--概况下属内容--%>
                    <div class="layui-colla-content layui-show">
                        <div class="showing-card showing-card2">
                            <div class="layui-row">
                                <div class="layui-col-md2 layui-col-sm2  layui-col-xs12 ">
                                    <div class="card-container " style="background: #F2F2F2;height: 100px ">
                                        <div class="card-content ">
                                            <font style="color: #18C3B1; font-size:20px;">
                                                <fmt:formatNumber value="${headSummary.totalBussinessAmount}"
                                                                  type="numer" pattern="0" maxFractionDigits="0"/>元
                                            </font>
                                        </div>
                                        <div class="card-title" style="padding: auto">
                                            <hr>
                                            <div class="bottomtitle">商机总额</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md2 layui-col-sm2  layui-col-xs12 ">
                                    <div class="card-container " style="background: #F2F2F2;height: 100px ">
                                        <div class="card-content ">
                                            <font style="color: #18C3B1; font-size: 20px;">${headSummary.totalBussinessNum}个</font>
                                            <%--<div style="color: red; font-size: 40px;">${}</div>--%>
                                            <%--<hr>--%>
                                        </div>
                                        <div class="card-title" style="padding: auto">
                                            <hr>
                                            <div class="bottomtitle">商机总数</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md2  layui-col-sm2 layui-col-xs12 ">
                                    <div class="card-container " style="background: #F2F2F2;height: 100px ">
                                        <div class="card-content ">
                                            <font style="color:#18C3B1; font-size: 20px;">
                                                <fmt:formatNumber value="${headSummary.yesterdayTotalBussinessAmount}"
                                                                  type="numer" pattern="0" maxFractionDigits="0"/>元
                                            </font>
                                            <%--<div style="color: red; font-size: 40px;">${}</div>--%>
                                        </div>
                                        <div class="card-title" style="padding: auto">
                                            <hr>
                                            <div class="bottomtitle">昨日新增商机金额
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md2 layui-col-sm2  layui-col-xs12 ">
                                    <div class="card-container " style="background: #F2F2F2;height: 150px ">
                                        <div class="card-content ">
                                            <font style="color: #18C3B1; font-size: 20px;">${headSummary.yesterdayTotalBussinessNum}个</font>
                                            <%--<div style="color: red; font-size: 40px;">${}</div>--%>
                                        </div>
                                        <div class="card-title" style="padding: auto">
                                            <hr>
                                            <div class="bottomtitle">昨日新增商机数量
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md2  layui-col-sm2 layui-col-xs12 ">
                                    <div class="card-container " style="background: #F2F2F2;height: 150px ">
                                        <div class="card-content ">
                                            <font style="color: #18C3B1; font-size: 20px;">
                                                <fmt:formatNumber value="${headSummary.thisMouthTotalAmount}"
                                                                  type="numer" pattern="0" maxFractionDigits="0"/>元
                                            </font>
                                            <%--<div style="color: red; font-size: 40px;">${}</div>--%>
                                        </div>
                                        <div class="card-title" style="padding: auto">
                                            <hr>
                                            <div class="bottomtitle">预计本月到款
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md2 layui-col-sm2  layui-col-xs12 ">
                                    <div class="card-container " style="background: #F2F2F2;height: 150px ">
                                        <div class="card-content ">
                                            <font style="color:#18C3B1; font-size: 20px;">${headSummary.thisMouthBussinessNum}个</font>
                                            <%--<div style="color: red; font-size: 40px;">${}</div>--%>
                                        </div>
                                        <div class="card-title" style="padding: auto">
                                            <hr>
                                            <div class="bottomtitle">预计本月到款商机数
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="module-container layui-row " style="margin-bottom: 25px">
            <div class="layui-collapse">
                <div class="layui-colla-item">
                    <div class="layui-colla-title">
                         <span class="panel-title-left2">
                    <i class="fa  fa-2x fa-line-chart"></i>
                </span>
                        <span class="panel-title-left">
                   数据总览
                </span>
                    </div>
                    <div class="layui-colla-content layui-show">

                        <table class="table table-bordered table-striped table-condensed table-centered"
                               style="table-layout: fixed;">
                            <thead>
                            <tr>
                                <th lay-data="{field:'field_1_1'}">
                                 部门
                                </th>
                                <th lay-data="{field:'field_1_2'}">商机总额(元)</th>
                                <th lay-data="{field:'field_1_3'}">商机总数</th>
                                <th lay-data="{field:'field_1_4'}">平均商机金额（元）</th>
                                <th lay-data="{field:'field_1_5'}">昨日新增商机金额（元）</th>
                                <th lay-data="{field:'field_1_6'}">昨日新增商机数量</th>
                                <th lay-data="{field:'field_1_7'}">本周新增商机金额（元）</th>
                                <th lay-data="{field:'field_1_8'}">本周新增商机数</th>
                            </tr>
                            </thead>
                            <tbody id="overviewTable">
                            <tr>
                                <td colspan="8">数据加载中</td>
                            </tr>
                                <%--</c:if>--%>
                            </tbody>
                        </table>

                    </div>
                </div>
            </div>
        </div>
        <%--商机状态和快捷入口--%>
        <%--todo--%>
        <div class="module-container layui-row layui-col-space30" style="margin-bottom: 25px">
            <div class="layui-col-md6">
                <div class="layui-collapse">
                    <div class="layui-colla-item">
                        <div class="layui-colla-title">
                            <span class="panel-title-left2"> <i class="fa  fa-2x fa-pie-chart"></i></span>
                            <span class="panel-title-left">
                       商机状态
                    </span>
                            <%--<img style="float: right" class="layui-colla-title"--%>
                            <%--src="<%= basePath %>static/images/businessChanceWorkbench/collapse.svg"  width="33" height="31">--%>
                        </div>
                        <div class="layui-colla-content layui-show">
                            <div class="layui-row">
                                    <div class="layui-row">
                                        <form class="layui-form" action="">
                                            <div class="layui-form-item">
                                                <div class="layui-row"
                                                     style="width: auto; margin-left: 0px;margin-bottom: 10px">
                                                    <select name="level1" lay-verify="">
                                                        <option value="">B2B事业部</option>
                                                    </select>
                                                </div>
                                                <div class="layui-row"
                                                     style="width: auto; margin-left: 0px;margin-bottom: 10px">
                                                    <select class="sub" name="level2" lay-verify=""
                                                            lay-filter="subSelect">
                                                        <c:forEach items="${workbenchUser.subDirectObjectList}"
                                                                   var="subObject">
                                                            <c:choose>
                                                                <c:when test="${subObject.subObjectId == selectId}">
                                                                    <option value="${subObject.subObjectId}"
                                                                            selected>${subObject.subObjectName}</option>
                                                                </c:when>
                                                                <c:otherwise>
                                                                    <option value="${subObject.subObjectId}">${subObject.subObjectName}</option>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </c:forEach>
                                                    </select>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                <div class="layui-row">
                                    <div class="charts">
                                        <div class="" id="status_1" style="width:auto;height:270px;">
                                        </div>
                                    </div>
                                    <div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-collapse">
                    <div class="layui-colla-item">
                        <div class="layui-colla-title">
                            <span class="panel-title-left2"><i class=" fa  fa-2x fa-sign-in"></i></span>
                            <span class="panel-title-left">快捷入口</span>
                            <%--<img style="float: right" class="layui-colla-title"--%>
                            <%--src="<%= basePath %>static/images/businessChanceWorkbench/collapse.svg"  width="33" height="31">--%>
                        </div>
                        <div class="layui-colla-content layui-show" style="height: 302px">
                            <div class="layui-row ">
                                <div class="layui-col-md3">
                                    <div style="text-align: center;margin: 10px">
                                        <a class="addtitle" href="javascript:void(0);"
                                           tabTitle='{"num":"view<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                        "link":"${pageContext.request.contextPath}/kpi/query/queryGroupPageData.do",
                                        "title":"总监"}'>
                                            <img src="<%= basePath %>static/images/workbench/wuxing.png"
                                                 width="73" height="65">
                                            <br>
                                            <p style="color: #AAAAAA;font-size: 100%; line-height: 100%">
                                                五行剑法</p></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <%--待沟通商机 for主管及以上--%>
            <div class="module-container layui-row " style="margin-bottom: 25px">
                <div class="layui-collapse">
                    <div class="layui-colla-item">
                        <div class="layui-colla-title">
                            <span class="panel-title-left2"><i style=" color: #18C3B1;" class=" fa  fa-2x fa-phone"></i></span>
                            <span class="panel-title-left">
                    待沟通商机
                    </span>
                        </div>
                        <div class="layui-colla-content layui-show">
                                <%--<table lay-filter="table_4">--%>
                            <table class="table table-bordered table-striped table-condensed table-centered"
                                   style="table-layout: fixed;">
                                <thead>
                                <tr align="center">
                                        <%--<th rowspan="3" lay-data="{field:'field_4_1' ;align: 'center'}">--%>
                                    <th rowspan="3">
                                        部门
                                    </th>
                                        <%--<th colspan="4" lay-data="{align: 'center'}">昨日沟通商机数</th>--%>
                                    <th colspan="4">昨日沟通商机数</th>
                                        <%--<th rowspan="3" lay-data="{field:'field_4_2' ;align: 'center'}">今日待沟通数</th>--%>
                                    <th rowspan="3">今日待沟通数</th>
                                </tr>
                                <tr align="center">
                                        <%--<th rowspan="2" lay-data="{field:'field_4_3' ;align: 'center'}">计划沟通数</th>--%>
                                    <th rowspan="2">计划沟通数</th>
                                        <%--<th colspan="3" lay-data="{align: 'center'}">实际沟通数</th>--%>
                                    <th colspan="3">实际沟通数</th>
                                </tr>
                                <tr align="center">
                                        <%--<th lay-data="{field:'field_4_4' ;align: 'center'}">计划内</th>--%>
                                    <th>计划内</th>
                                        <%--<th lay-data="{field:'field_4_5' ;align: 'center'}">沟通完成率</th>--%>
                                    <th>沟通完成率</th>
                                        <%--<th lay-data="{field:'field_4_6' ;align: 'center'}">计划外</th>--%>
                                    <th>计划外</th>
                                </tr>
                                </thead>
                                <tbody id="communicateTable">
                                <tr>
                                    <td colspan="6">数据加载中</td>
                                </tr>
                                    <%--</c:if>--%>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        <%--重点商机 for管理--%>
            <div class="module-container layui-row " style="margin-bottom: 25px">
                <div class="layui-collapse">
                    <div class="layui-colla-item">
                        <div class="layui-colla-title">
                            <span class="panel-title-left2"><i class=" fa  fa-2x fa-star-o"></i></span>
                            <span class="panel-title-left">
                    重点商机
                    </span>
                        </div>
                        <div class="layui-colla-content layui-show">
                            <table class="table table-bordered table-striped table-condensed table-centered"
                                   style="table-layout: fixed;">
                                <thead>
                                <tr>
                                    <th lay-data="{field:'field_6_1'}">
                                        部门
                                    </th>
                                    <th lay-data="{field:'field_6_2'}">S级商机数量</th>
                                    <th lay-data="{field:'field_6_3'}">S级商机金额（元）</th>
                                    <th lay-data="{field:'field_6_4'}">A级商机数量</th>
                                    <th lay-data="{field:'field_6_5'}">A级商机金额（元）</th>
                                </tr>
                                </thead>
                                <tbody id="importantTable">
                                <tr>
                                    <td colspan="5">数据加载中</td>
                                </tr>
                                    <%--</c:if>--%>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>


        <%--预计本周成单for主管及以上--%>
        <div class="module-container layui-row " style="margin-bottom: 25px">
                <div class="layui-collapse">
                    <div class="layui-colla-item">
                        <div class="layui-colla-title">
                            <span class="panel-title-left2"><i class=" fa  fa-2x fa-smile-o"></i></span>
                            <span class="panel-title-left">
                          预计本周成单
                    </span>
                        </div>
                        <div class="layui-colla-content layui-show">
                            <table class="table table-bordered table-striped table-condensed table-centered"
                                   id="layexpectTable"
                                   style="table-layout: fixed;">
                                <thead>
                                <tr>
                                    <th lay-data="{field:'field_8_1'}">部门</th>
                                    <th lay-data="{field:'field_8_2'}">上周预计成单商机数量</th>
                                    <th lay-data="{field:'field_8_3'}">上周未成单商机数量</th>
                                    <th lay-data="{field:'field_8_4'}">打靶率</th>
                                    <th lay-data="{field:'field_8_5'}">预计本周成单商机数</th>
                                    <th lay-data="{field:'field_8_6'}">预计本周成单商机金额（元）</th>
                                </tr>
                                </thead>
                                <tbody id="expectTable">
                                <tr>
                                    <td colspan="6">数据加载中</td>
                                </tr>
                                    <%--</c:if>--%>
                                </tbody>
                            </table>

                        </div>
                    </div>
                </div>
            </div>

        <%--预警商机for总监和经理--%>
        <div class="module-container layui-row " style="margin-bottom: 25px">
                <div class="layui-collapse">
                    <div class="layui-colla-item">
                        <div class="layui-colla-title">
                            <span class="panel-title-left2"><i class=" fa  fa-2x fa-bomb"></i></span>
                            <span class="panel-title-left">
                         预警商机
                    </span>
                        </div>
                        <div class="layui-colla-content layui-show">
                            <table class="table table-bordered table-striped table-condensed table-centered"
                                   style="table-layout: fixed;">
                                <thead>
                                <tr>
                                    <th lay-data="{field:'field_9_1'}">部门</th>
                                    <th lay-data="{field:'field_9_2'}">本周新增大项目</th>
                                    <th lay-data="{field:'field_9_3'}">2日未联系商机</th>
                                    <th lay-data="{field:'field_9_4'}">本周核心商品商机丢单</th>
                                    <th lay-data="{field:'field_9_5'}">本周预计到款商机未到</th>
                                </tr>
                                </thead>
                                <tbody id="leaderWaringTable">
                                <tr>
                                    <td colspan="5">数据加载中</td>
                                </tr>
                                    <%--</c:if>--%>
                                </tbody>
                            </table>

                        </div>
                    </div>
                </div>
            </div>
    </div>
</div>
<input id="position" type="hidden" value="${workbenchUser.positionName}">
<input id="id" type="hidden" value="${workbenchUser.userId}">
<input id="pathUrl" type="hidden" value="${pageContext.request.contextPath}">
<input id="thisWeekStartTimeStr" type="hidden" value="${workbenchUser.thisWeekStartTimeStr}">
<input id="thisWeekEndTimeStr" type="hidden" value="${workbenchUser.thisWeekEndTimeStr}">
<input id="bussinessChanceStatusString" type="hidden" value="${bussinessChanceStatusString}">
<input id="yesterdayEndtimeStr" type="hidden" value="${workbenchUser.yesterdayEndtimeStr}">

<script>
    //注意：折叠面板 依赖 element 模块，否则无法进行功能性操作
    layui.use('element', function () {
        var element = layui.element;

    });
</script>
<script type="text/javascript">
    var echartsStatusString = '${bussinessChanceStatusString}';
    var echartsStatusList = JSON.parse(echartsStatusString);
    var entrydata = [];
    for (var i = 0; i < echartsStatusList.length; i++) {
        if (echartsStatusList[i].statusNum == 0) {
            var data = {
                value: null,
                name: echartsStatusList[i].statusName,
            };
        } else {
            var data = {
                value: echartsStatusList[i].statusNum,
                name: echartsStatusList[i].statusName
            };
        }

        entrydata.push(data);
    }
    var option = {
        title: {
            text: '',
            subtext: '',
            x: 'center'
        },
        tooltip: {
            trigger: 'item',
            formatter: ""
        },
        legend: {

            orient: 'vertical',
            right: 0,
            top: 'middle',
            itemGap: 40,
            data: ['已订单', '已报价', '报价中', '处理中', '未处理'],
            //右方标签名称修改
            formatter: function (name) {
                var num = 0;
                var percent = 0;
                var amount = 0;
                var mydata = echartsStatusList;
                for (var i = 0; i < mydata.length; i++) {

                    if (name == mydata[i].statusName) {

                        num = mydata[i].statusNum;
                        amount = mydata[i].statusAmount;
                        percent = mydata[i].rate;
                    }
                }
                return name + '\t\t\t' + amount + '元\t\t\t' + num + '个\t\t\t' + percent;

            },
            selectedMode: true
        },
        series: [
            {
                name: '商机状态',
                type: 'pie',
                radius: '50%',
                center: ['20%', '50%'],//饼图的中心位置
                label: {  //饼图的文本标签
                    show: true,
                    position: 'inner',//标签位置
                    color: '#000000',
                    fontSize: 9,   //文字的字体大小
                    formatter: function (param) {
                        var mydata = echartsStatusList;
                        var percent = 0;
                        for (var i = 0; i < mydata.length; i++) {

                            if (param.name == mydata[i].statusName) {

                                percent = mydata[i].rate;
                            }
                        }
                        return percent;

                    }
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: '20',
                        fontWeight: 'bold'
                    }
                },
                data: entrydata
            }
        ]
    };
    var echats_1 = echarts.init(document.getElementById('status_1'));

    echats_1.on('legendselectchanged', function (params) {

        if ("${workbenchUser.positionName}" == "销售工程师" || "${workbenchUser.positionName}" == "销售专员") {
            echats_1.setOption({
                legend: {selected: {[params.name]: true}}
            });
            var status = 0;
            for (var i = 0; i < echartsStatusList.length; i++) {
                if (params.name == echartsStatusList[i].statusName) {
                    status = echartsStatusList[i].statusId;
                }
            }
            <%--var id ="view<%=System.currentTimeMillis() + (int)(Math.random()*10000)%>";--%>
            var id = 'index' + Date.parse(new Date()) + Math.floor(Math.random() * 1000);
            var name = "商机详情";
            var uri = "${pageContext.request.contextPath}/order/bussinesschance/saleindex.do?starttime=2019-11-1&endtime=${workbenchUser.yesterdayEndtimeStr}&timeType=1&userId=${workbenchUser.userId}&status=" + status;
            var closable = 1;
            var item = {'id': id, 'name': name, 'url': uri, 'closable': closable == 1 ? true : false};
            self.parent.closableTab.addTab(item);
            self.parent.closableTab.resizeMove();
        }


    });
    echats_1.setOption(option);
    setTimeout(function () {
        window.onresize = function () {
            echats_1.resize();
        }
    }, 200);

</script>
<script type="text/javascript">
    layui.use('table', function () {
        var table = layui.table;

    });
    layui.use('form', function () {
        var form = layui.form;

        form.render();
        //监听提交
        form.on('submit(formDemo)', function (data) {
            layer.msg(JSON.stringify(data.field));
            return false;
        });

        form.on('select(subSelect)', function (data) {
            var message = data.value;
            var echarts_1 = echarts.init(document.getElementById('status_1'));
            var newEntrydata = [];
            $.ajax({
                type: "GET",
                url: "${pageContext.request.contextPath}/workbench/bussinesschance/status/echarts.do?positionName=${workbenchUser.positionName}&userId=${workbenchUser.userId}&queryUserId=" + message + "&queryOrgId=" + message,
                dataType: "json",
                success: function (result) {
                    var newStatusList = result.data;
                    for (var i = 0; i < newStatusList.length; i++) {

                        if (newStatusList[i].statusNum == 0) {
                            var data = {
                                value: null,
                                name: newStatusList[i].statusName
                            };
                        } else {
                            var data = {
                                value: newStatusList[i].statusNum,
                                name: newStatusList[i].statusName
                            };
                        }


                        newEntrydata.push(data)


                    }
                    echarts_1.setOption({
                        title: {
                            text: '',
                            subtext: '',
                            x: 'center'
                        },
                        tooltip: {
                            trigger: 'item',
                            formatter: ""
                        },
                        legend: {
                            orient: 'vertical',
                            top: 'middle',
                            right: 0,
                            itemGap: 40,
                            data: ['已订单', '已报价', '报价中', '处理中', '未处理'],
                            //右方标签名称修改
                            formatter: function (name) {
                                var num = 0;
                                var percent = 0;
                                var amount = 0;
                                var newdata = newStatusList;
                                for (var i = 0; i < newdata.length; i++) {

                                    if (name == newdata[i].statusName) {

                                        num = newdata[i].statusNum;
                                        amount = newdata[i].statusAmount;
                                        percent = newdata[i].rate;
                                    }
                                }
                                return name + '\t\t\t' + amount + '元\t\t\t' + num + '个\t\t\t' + percent;

                            }
                        },
                        series: [
                            {
                                name: '商机状态',
                                type: 'pie',
                                radius: '50%',
                                center: ['20%', '50%'],//饼图的中心位置
                                label: {  //饼图的文本标签
                                    show: true,
                                    position: 'inner',//标签位置
                                    color: '#000000',
                                    fontSize: 9,   //文字的字体大小
                                    formatter: function (param) {
                                        var newdata = newStatusList;
                                        var percent = 0;
                                        for (var i = 0; i < newdata.length; i++) {

                                            if (param.name == newdata[i].statusName) {

                                                percent = newdata[i].rate;
                                            }
                                        }
                                        return percent;

                                    }
                                },
                                emphasis: {
                                    label: {
                                        show: true,
                                        fontSize: '20',
                                        fontWeight: 'bold'
                                    }
                                },
                                data: newEntrydata
                            }
                        ]
                    })
                }
            });

        });
    });
</script>
<script>
    $('#expectTable').on('click', '.addtitle_1', function () {
        var frontPageId = $(window.parent.document).find('.active').eq(1).attr('id');
        var newPageId;
        var tabTitle = $(this).attr('tabTitle');
        if (typeof (tabTitle) == 'undefined') {
            alert('参数错误');
        } else {
            tabTitle = $.parseJSON(tabTitle);
        }
        var id = tabTitle.num;
        // var id = 'index' + Date.parse(new Date()) + Math.floor(Math.random()*1000);
        var name = tabTitle.title;
        var uri = tabTitle.link;
        var closable = 1;
        var item = {'id': id, 'name': name, 'url': uri, 'closable': closable == 1 ? true : false};
        self.parent.closableTab.addTab(item);
        self.parent.closableTab.resizeMove();
        $(window.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
    });
</script>
<%@ include file="../../common/footer.jsp" %>