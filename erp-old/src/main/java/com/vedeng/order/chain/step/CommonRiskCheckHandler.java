package com.vedeng.order.chain.step;

import com.vedeng.common.util.StringUtil;
import com.vedeng.order.chain.AbstractRiskHandlerStep;
import com.vedeng.order.chain.RiskEnum;
import com.vedeng.order.chain.RiskHandlerKeyConstant;
import com.vedeng.order.chain.model.RiskModel;
import com.wms.service.chain.HandlerStepContext;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @ClassName CommonRiskCheckHandler.java
 * @Description TODO 通用风控校验
 * @createTime 2020年12月09日 15:25:00
 */
@Service
public class CommonRiskCheckHandler extends AbstractRiskHandlerStep {

    @Value("${risk_isCheck}")
    private String ischeck;

    @Override
    public RiskModel doDealWith(HandlerStepContext context) {
        RiskModel riskModel = context.get(RiskHandlerKeyConstant.RISK_MODEL);
        if(riskModel == null){
            riskModel = new RiskModel();
            riskModel.setIsRisk(true);
        }

        riskModel.setIsOneChek(true);

        if(StringUtil.isBlank(ischeck)){
            riskModel.setIsCheck(false);
            riskModel.setIsRisk(true);
            return riskModel;
        }

        if(CollectionUtils.isEmpty(riskModel.getRiskEnumList())){
            Set<RiskEnum> riskModelSet = new HashSet<>();
            riskModel.setRiskEnumList(riskModelSet);
        }
        riskModel.setIsCheck(true);

        return riskModel;
    }
}
