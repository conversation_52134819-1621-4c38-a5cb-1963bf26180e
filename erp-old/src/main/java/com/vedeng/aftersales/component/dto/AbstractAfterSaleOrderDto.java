package com.vedeng.aftersales.component.dto;

import lombok.Data;

import java.util.List;

@Data
public class AbstractAfterSaleOrderDto {
    /**
     * 售后单关联的销售单（仅仅是BD订单）
     */
    private String saleOrderNo;

    /**
     * 售后单号
     */
    private String afterSalesNo;

    /**
     * 售后单创建人（贝登前台当前登录人手机号）
     */
    private String loginMobile;

    /**
     * 售后单创建人名称
     */
    private String loginUsername;

    /**
     * 联系人名称
     */
    private String contactUsername;

    /**
     * 售后联系人手机号
     */
    private String contactMobile;

    /**
     * 售后联系地址-省编码
     */
    private String contactProvince;

    /**
     * 售后联系地址-市编码
     */
    private String contactCity;

    /**
     * 售后联系地址-区编码
     */
    private String contactArea;

    /**
     * 售后联系地址
     */
    private String contactAddress;

    /**
     * 售后原因编码
     */
    private Integer afterSalesReason;

    /**
     * 售后详细说明
     */
    private String afterSalesComment;

    /**
     * 售后附件链接集合
     */
    private List<AttachmentDto> attachments;

    /**
     * 售后商品集合
     */
    private List<AfterSaleOrderGoodAddDto> goodsList;

    /**
     * 退票、补票的发票号集合
     */
    private List<InvoiceAddDto> invoiceList;

    /**
     * 操作时间
     */
    private Long operateTime;
}
