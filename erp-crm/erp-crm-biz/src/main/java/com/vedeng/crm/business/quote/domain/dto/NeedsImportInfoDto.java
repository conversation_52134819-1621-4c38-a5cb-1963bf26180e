package com.vedeng.crm.business.quote.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.vedeng.common.core.utils.validator.group.DefaultGroup;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;


@Data
public class NeedsImportInfoDto implements Serializable {
    private static final long serialVersionUID = 6906719125697903013L;

    @Length(max = 50, message = "需求产品不能超过100个字", groups = DefaultGroup.class)
    @ExcelProperty(index = 0,value = "*需求产品")
    private String productNeeds;

    @Length(max = 20, message = "需求数量不能超过20个字", groups = DefaultGroup.class)
    @ExcelProperty(index = 1,value = "需求数量")
    private String numNeeds;

    @Length(max = 20, message = "经销预算不能超过20个字", groups = DefaultGroup.class)
    @ExcelProperty(index = 2,value = "经销预算(元)")
    private String distributeBudget;

    @Length(max = 20, message = "终端预算不能超过20个字", groups = DefaultGroup.class)
    @ExcelProperty(index = 3,value = "终端预算(元)")
    private String terminalBudget;

    @Length(max = 500, message = "需求备注不能超过500个字", groups = DefaultGroup.class)
    @ExcelProperty(index = 4,value = "需求备注")
    private String extraNeeds;
}
