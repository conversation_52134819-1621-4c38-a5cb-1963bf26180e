package com.smallhospital.service.impl.remote;

import com.google.gson.JsonObject;
import org.springframework.stereotype.Service;

/**
 * <b>Description:</b>同步合同修改状态<br>
 * @Note
 * <b>Author:calvin</b>
 * <br><b>Date:</b> 2020/4/22
 */
@Service
public class SynContractModifyService extends AbstractELThirdService<Integer>{
    @Override
    protected String encapsulaeRequestBodyParam(Integer param) {
        JsonObject data=new JsonObject();
        data.addProperty("contractId",param);
        return data.toString();
    }

    @Override
    protected String getRequestUIR() {
        return "/api/modifyContract.action";
    }
}
