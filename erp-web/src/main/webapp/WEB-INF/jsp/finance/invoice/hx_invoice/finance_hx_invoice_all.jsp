<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ include file="../../../common/common.jsp" %>
<script src="${pageContext.request.contextPath}/static/js/finance/invoice/hx_invoice/hx_invoice_common.js?rnd=${resourceVersionKey}"
        charset="utf-8"></script>
<div class="searchfunc ">
    <form method="post" id="search" action="/finance/invoice/hx_invoice.do?id=0">
        <ul>
            <li>
                <label class="keyword">关键词</label>
                <input type="text" class="input-small" name="keyword" id="keyword"
                       value="${invoiceSearch.keyword}"/>
            </li>
            <li>
                <label class="invoiceStatus">发票状态</label>
                <select class="input-smaller96 selector" name="invoiceStatus">
                    <option value="-1">全部</option>
                    <option value="1" <c:if test="${invoiceSearch.invoiceStatus eq 1}">selected="selected"</c:if>>
                        待录票
                    </option>
                 <%--   <option value="2" <c:if test="${invoiceSearch.invoiceStatus eq 2}">selected="selected"</c:if>>
                        审核中
                    </option>
                    <option value="3" <c:if test="${invoiceSearch.invoiceStatus eq 3}">selected="selected"</c:if>>
                        已审核
                    </option>--%>
                    <option value="4" <c:if test="${invoiceSearch.invoiceStatus eq 4}">selected="selected"</c:if>>
                        待认领
                    </option>
                    <option value="5" <c:if test="${invoiceSearch.invoiceStatus eq 5}">selected="selected"</c:if>>
                        费用票
                    </option>
                    <option value="6" <c:if test="${invoiceSearch.invoiceStatus eq 6}">selected="selected"</c:if>>
                        异常票
                    </option>
                    <option value="7" <c:if test="${invoiceSearch.invoiceStatus eq 7}">selected="selected"</c:if>>
                        负数票
                    </option>
                    <option value="8" <c:if test="${invoiceSearch.invoiceStatus eq 8}">selected="selected"</c:if>>
                        其他
                    </option>
                    <option value="11" <c:if test="${invoiceSearch.invoiceStatus eq 11}">selected="selected"</c:if>>
                        已录票
                    </option>
                    <option value="12" <c:if test="${invoiceSearch.invoiceStatus eq 12}">selected="selected"</c:if>>
                        无效票
                    </option>
                </select>
            </li>
            <li class="fold-li" style="display: list-item">
                <label></label>
                <select class="input-smaller96 selector" name="timeSearchType">
                    <option value="1" <c:if test="${invoiceSearch.timeSearchType eq 1}">selected="selected"</c:if>>
                        开票时间
                    </option>
                    <option value="2" <c:if test="${invoiceSearch.timeSearchType eq 2}">selected="selected"</c:if>>
                        录票时间
                    </option>
                    <option value="3" <c:if test="${invoiceSearch.timeSearchType eq 3}">selected="selected"</c:if>>
                        审核时间
                    </option>
                    <option value="4" <c:if test="${invoiceSearch.timeSearchType eq 4}">selected="selected"</c:if>>
                        认证时间
                    </option>
                </select>
            </li>

            <li class="fold-li" style="display: list-item">
                <input class="Wdate f_left input-smaller96 m0" type="text" placeholder="请选择日期" autocomplete="off"
                       onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endAddDateStr\')}'})"
                       name="startAddDateStr" id="startAddDateStr"
                       value='<date:date value ="${startAddDateStr}" format="yyyy-MM-dd"/>'>
                <div class="gang">-</div>
                <input class="Wdate f_left input-smaller96" type="text" placeholder="请选择日期" autocomplete="off"
                       onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'startAddDateStr\')}'})"
                       name="endAddDateStr" id="endAddDateStr"
                       value='<date:date value ="${endAddDateStr}" format="yyyy-MM-dd"/>'>
            </li>
            <li class="fold-li" style="display: list-item">
                <label class="infor_name">发票总额</label>
                <input class="f_left input-smaller96 mr5" type="text" name="invoiceAmountFrom"
                       id="invoiceAmountFrom" value='${invoiceSearch.invoiceAmountFrom}'
                       onchange="checkValue(this)">
                <div class="gang">-</div>
                <input class="f_left input-smaller96" type="text" name="invoiceAmountTo" id="invoiceAmountTo"
                       value='${invoiceSearch.invoiceAmountTo}' onchange="checkValue(this)">
            </li>
            <li class="fold-li" style="display: list-item">
                <label class="infor_name">票种</label>
                <select class="input-middle selector" name="invoiceTaxRate">
                    <option value="0"
                            <c:if test="${invoiceSearch.invoiceTaxRate == 0}">selected</c:if> >全部
                    </option>
                    <option value="971"
                            <c:if test="${invoiceSearch.invoiceTaxRate eq 13 && invoiceSearch.invoiceCategory == '0'}">selected="selected"</c:if>>
                        13%增值税普通发票
                    </option>
                    <option value="972"
                            <c:if test="${invoiceSearch.invoiceTaxRate eq 13 && invoiceSearch.invoiceCategory == '1'}">selected="selected"</c:if>>
                        13%增值税专用发票
                    </option>
                    <option value="681"
                            <c:if test="${invoiceSearch.invoiceTaxRate eq 16 && invoiceSearch.invoiceCategory == '0'}">selected="selected"</c:if>>
                        16%增值税普通发票
                    </option>
                    <option value="682"
                            <c:if test="${invoiceSearch.invoiceTaxRate eq 16 && invoiceSearch.invoiceCategory == '1'}">selected="selected"</c:if>>
                        16%增值税专用发票
                    </option>
                    <option value="683"
                            <c:if test="${invoiceSearch.invoiceTaxRate eq 6 && invoiceSearch.invoiceCategory == '0'}">selected="selected"</c:if>>
                        6%增值税普通发票
                    </option>
                    <option value="684"
                            <c:if test="${invoiceSearch.invoiceTaxRate eq 6 && invoiceSearch.invoiceCategory == '1'}">selected="selected"</c:if>>
                        6%增值税专用发票
                    </option>
                    <option value="685"
                            <c:if test="${invoiceSearch.invoiceTaxRate eq 3 && invoiceSearch.invoiceCategory == '0'}">selected="selected"</c:if>>
                        3%增值税普通发票
                    </option>
                    <option value="686"
                            <c:if test="${invoiceSearch.invoiceTaxRate eq 3 && invoiceSearch.invoiceCategory == '1'}">selected="selected"</c:if>>
                        3%增值税专用发票
                    </option>
                    <option value="687"
                            <c:if test="${invoiceSearch.invoiceTaxRate eq 0 && invoiceSearch.invoiceCategory == '0'}">selected="selected"</c:if>>
                        0%增值税普通发票
                    </option>
                </select>
            </li>
            <li class="fold-li" style="display: list-item">
                <label class="infor_name">红蓝字</label>
                <select class="input-smaller96 selector" name="colorType">
                    <option value="">全部</option>
                    <option value="1" <c:if test="${invoiceSearch.colorType eq 1}">selected="selected"</c:if>>蓝字有效
                    </option>
                    <option value="2" <c:if test="${invoiceSearch.colorType eq 2}">selected="selected"</c:if>>红字有效
                    </option>
                    <option value="3" <c:if test="${invoiceSearch.colorType eq 3}">selected="selected"</c:if>>蓝字作废
                    </option>
                    <option value="4" <c:if test="${invoiceSearch.colorType eq 4}">selected="selected"</c:if>>失控
                    </option>
                    <option value="5" <c:if test="${invoiceSearch.colorType eq 5}">selected="selected"</c:if>>异常
                    </option>
                </select>
            </li>
            <li class="fold-li" style="display: list-item">
                <label class="infor_name">录票人员</label>
                <select class="input-smaller96 selector" name="entryUser">
                    <option value="">全部</option>
                    <c:forEach items="${recordUsers}" var="user">
                        <option value="${user.userId}"
                                <c:if test="${invoiceSearch.entryUser eq user.userId}">selected="selected"</c:if>>${user.username}</option>
                    </c:forEach>
                </select>
            </li>
            <li class="fold-li" style="display: list-item">
                <label class="infor_name">审核人员</label>
                <select class="input-smaller96 selector" name="validUser">
                    <option value="">全部</option>
                    <c:forEach items="${validUsers}" var="user">
                        <option value="${user.userId}"
                                <c:if test="${invoiceSearch.validUser eq user.userId}">selected="selected"</c:if>>${user.username}</option>
                    </c:forEach>
                </select>
            </li>
            <li class="fold-li" style="display: list-item">
                <label class="infor_name">审核状态</label>
                <select class="input-smaller96 selector" name="validStatus">
                    <option value="">全部</option>
                    <option value="0" <c:if test="${invoiceSearch.validStatus eq 0}">selected="selected"</c:if>>
                        审核中
                    </option>
                    <option value="1" <c:if test="${invoiceSearch.validStatus eq 1}">selected="selected"</c:if>>
                        审核通过
                    </option>
                    <option value="2" <c:if test="${invoiceSearch.validStatus eq 2}">selected="selected"</c:if>>
                        审核不通过
                    </option>
                </select>
            </li>
            <li class="fold-li" style="display: list-item">
                <label class="infor_name">认证状态</label>
                <select class="input-smaller96 selector" name="authStatus">
                    <option value="">全部</option>
                    <option value="0" <c:if test="${invoiceSearch.authStatus eq 0}">selected="selected"</c:if>>
                        未认证
                    </option>
                    <option value="1" <c:if test="${invoiceSearch.authStatus eq 1}">selected="selected"</c:if>>
                        认证通过
                    </option>
                    <option value="2" <c:if test="${invoiceSearch.authStatus eq 2}">selected="selected"</c:if>>
                        认证失败
                    </option>
                </select>
            </li>
            <li class="fold-li" style="display: list-item">
                <label class="infor_name">当月认证</label>
                <select class="input-smaller96 selector" name="authMonth">
                    <option value="">全部</option>
                    <option value="1" <c:if test="${invoiceSearch.authMonth eq 1}">selected="selected"</c:if>>当月认证
                    </option>
                    <option value="2" <c:if test="${invoiceSearch.authMonth eq 2}">selected="selected"</c:if>>
                        非当月认证
                    </option>
                </select>
            </li>
            <li class="fold-li" style="display: list-item">
                <label class="infor_name">发送结果</label>
                <select class="input-smaller96 selector" name="sendResult">
                    <option value="">全部</option>
                    <option value="1" <c:if test="${invoiceSearch.sendResult eq 1}">selected="selected"</c:if>>是
                    </option>
                    <option value="2" <c:if test="${invoiceSearch.sendResult eq 2}">selected="selected"</c:if>>否
                    </option>
                </select>
            </li>

        </ul>
        <div class="tcenter">
                <span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();"
                      id="searchSpan">查询</span>
            <span class="bt-small bg-light-blue bt-bg-style mr20" onclick="searchReset();">重置</span>
            <span id="foldButton" class="bt-small bg-light-blue bt-bg-style mr20" onclick="foldItem()"
                  value="0">收起筛选条件</span>
            <span class=" bt-small bt-bg-style bg-light-green" onclick="captureInvoiceInit()">抓取发票</span>

        </div>
    </form>
    <br>
    <div class="content" style="height: 900px">
        <div class="fixdiv">
            <div style="width:1752px;" class='superdiv'>
                <table class="table table-bordered table-striped table-condensed table-centered">
                    <thead>
                    <tr>
                        <th class="wid4">序号</th>
                        <th class="wid6">发票号码</th>
                        <th class="wid6">发票代码</th>
                        <th class="wid5">发票状态</th>
                        <th class="wid12">开票方名称</th>
                        <th class="wid6">发票总额</th>
                        <th class="wid7">已录票金额</th>
                        <th class="wid7">不含税金额</th>
                        <th class="wid6">税额</th>
                        <th class="wid8">票种</th>
                        <th class="wid7">开票时间</th>
                        <th class="wid6">红蓝字</th>
                        <th class="wid8">关联订单号</th>
                        <th class="wid7">录票时间</th>
                        <th class="wid6">录票人员</th>
                        <th class="wid7">审核时间</th>
                        <th class="wid6">审核人员</th>
                        <th class="wid6">审核状态</th>
                        <th class="wid8">审核备注</th>
                        <th class="wid6">认证状态</th>
                        <th class="wid7">认证时间</th>
                        <th class="wid4">当月认证</th>
                        <th class="wid8">金蝶凭证号</th>
                        <th class="wid4">发送结果</th>
                    </tr>
                    </thead>
                    <tbody>
                    <c:set var="lzyxAmount" value="0"></c:set><!-- 蓝字有效总额 -->
                    <c:set var="lzyxTaxAmount" value="0"></c:set><!-- 蓝字有效税额 -->
                    <c:set var="lzyxTaxFreeAmount" value="0"></c:set><!-- 蓝字有效不含税额 -->

                    <c:set var="lzzfAmount" value="0"></c:set><!-- 蓝字作废总额 -->
                    <c:set var="lzzfTaxAmount" value="0"></c:set><!-- 蓝字作废税额 -->
                    <c:set var="lzzfTaxFreeAmount" value="0"></c:set><!-- 蓝字作废不含税额 -->

                    <c:set var="hzyxAmount" value="0"></c:set><!-- 红字有效总额 -->
                    <c:set var="hzyxTaxAmount" value="0"></c:set><!-- 红字有效税额 -->
                    <c:set var="hzyxTaxFreeAmount" value="0"></c:set><!-- 红字有效不含税额 -->


                    <c:set var="pageNum" value="0"></c:set><!-- 当前页记录数 -->
                    <c:forEach var="item" items="${list}" varStatus="status">
                        <c:set var="pageNum" value="${pageNum + 1}"></c:set>
                        <tr>
                            <td>
                                    ${status.count}
                            </td>
                            <td>
                                <a style="color:#438DEF;" onclick="viewAndDownloadHxInvoiceHref('${item.invoiceCode}','${item.invoiceNum}','${item.hxInvoiceId}',0)">
                                        ${item.invoiceNum}
                                </a>
                            </td>
                            <td>${item.invoiceCode}</td>
                            <td>
                                <c:choose>
                                    <c:when test="${item.invoiceStatus == 1}">
                                        待录票
                                    </c:when>
                                    <c:when test="${item.invoiceStatus == 2}">
                                        审核中
                                    </c:when>
                                    <c:when test="${item.invoiceStatus == 3}">
                                        已审核
                                    </c:when>
                                    <c:when test="${item.invoiceStatus == 4}">
                                        待认领
                                    </c:when>
                                    <c:when test="${item.invoiceStatus == 5}">
                                        费用票
                                    </c:when>
                                    <c:when test="${item.invoiceStatus == 6}">
                                        异常票
                                    </c:when>
                                    <c:when test="${item.invoiceStatus == 7}">
                                        负数票
                                    </c:when>
                                    <c:when test="${item.invoiceStatus == 8}">
                                        其他
                                    </c:when>
                                    <c:when test="${item.invoiceStatus == 9}">
                                        待退票
                                    </c:when>
                                    <c:when test="${item.invoiceStatus == 10}">
                                        已退票
                                    </c:when>
                                    <c:when test="${item.invoiceStatus == 11}">
                                        已录票
                                    </c:when>
                                    <c:when test="${item.invoiceStatus == 12}">
                                        无效票
                                    </c:when>
                                </c:choose>
                            </td>
                            <td>
                                <c:choose>
                                    <c:when test="${item.traderId eq 0 || item.traderId  == null}">
                                        ${item.salerName}
                                    </c:when>
                                    <c:otherwise>
                                        <a onclick="viewTraderInfo(${item.traderId})"> ${item.salerName}</a>
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td>
                                <fmt:formatNumber value="${item.amount}" type="numer" pattern="0.00"
                                                  maxFractionDigits="2"/>
                            </td>
                            <td>
                                <fmt:formatNumber value="${item.recordedAmount}" type="numer" pattern="0.00"
                                                  maxFractionDigits="2"/>
                            </td>
                            <td>
                                <fmt:formatNumber value="${item.invoiceAmount}" type="numer" pattern="0.00"
                                                  maxFractionDigits="2"/>
                            </td>
                            <td>
                                <fmt:formatNumber value="${item.taxAmount}" type="numer" pattern="0.00"
                                                  maxFractionDigits="2"/>
                            </td>
                            <td>
                                <c:if test="${item.taxRate != null && item.invoiceCategory != null}">
                                    ${item.taxRate}%增值税<c:if test="${item.invoiceCategory == '0'}">普通</c:if><c:if
                                        test="${item.invoiceCategory == '1'}">专用</c:if>发票
                                </c:if>
                            </td>
                            <td>
                                <c:if test="${item.createTime != null}">
                                    <jsp:useBean id="createTimeDateValue" class="java.util.Date"/>
                                    <jsp:setProperty name="createTimeDateValue" property="time"
                                                     value="${item.createTime}"/>
                                    <fmt:formatDate value="${createTimeDateValue}" pattern="yyyy-MM-dd"/>
                                </c:if>
                            </td>
                            <td>
                                <c:choose>
                                    <c:when test="${item.colorType == 1}">
                                        蓝字有效
                                        <c:set var="lzyxAmount" value="${lzyxAmount + item.amount}"></c:set>
                                        <c:set var="lzyxTaxAmount" value="${lzyxTaxAmount + item.taxAmount}"></c:set>
                                        <c:set var="lzyxTaxFreeAmount" value="${lzyxTaxFreeAmount + item.invoiceAmount}"></c:set>
                                    </c:when>
                                    <c:when test="${item.colorType == 2}">
                                        红字有效
                                        <c:set var="hzyxAmount" value="${hzyxAmount + item.amount}"></c:set>
                                        <c:set var="hzyxTaxAmount" value="${hzyxTaxAmount + item.taxAmount}"></c:set>
                                        <c:set var="hzyxTaxFreeAmount" value="${hzyxTaxFreeAmount + item.invoiceAmount}"></c:set>
                                    </c:when>
                                    <c:when test="${item.colorType == 3}">
                                        蓝字作废
                                        <c:set var="lzzfAmount" value="${lzzfAmount + item.amount}"></c:set>
                                        <c:set var="lzzfTaxAmount" value="${lzzfTaxAmount + item.taxAmount}"></c:set>
                                        <c:set var="lzzfTaxFreeAmount" value="${lzzfTaxFreeAmount + item.invoiceAmount}"></c:set>
                                    </c:when>
                                    <c:when test="${item.colorType == 4}">
                                        失控
                                    </c:when>
                                    <c:when test="${item.colorType == 5}">
                                        异常
                                    </c:when>
                                </c:choose>
                            </td>
                            <td>
                                <c:forEach items="${item.buyorders}" var="buyorder">
                                    <a href="javascript:void(0);" onclick="viewBuyOrder(${buyorder.buyorderId},${item.type})">${buyorder.buyorderNo}</a>
                                </c:forEach>
                            </td>
                            <td>
                                <c:if test="${item.entryTime != null}">
                                    <jsp:useBean id="entryTimeDateValue" class="java.util.Date"/>
                                    <jsp:setProperty name="entryTimeDateValue" property="time"
                                                     value="${item.entryTime}"/>
                                    <fmt:formatDate value="${entryTimeDateValue}" pattern="yyyy-MM-dd HH:mm:ss"/>
                                </c:if>
                            </td>
                            <td>${item.entryUser}</td>
                            <td>
                                <c:if test="${item.validTime != null and item.validTime ne 0}">
                                    <jsp:useBean id="validTimeDateValue" class="java.util.Date"/>
                                    <jsp:setProperty name="validTimeDateValue" property="time"
                                                     value="${item.validTime}"/>
                                    <fmt:formatDate value="${validTimeDateValue}" pattern="yyyy-MM-dd HH:mm:ss"/>
                                </c:if>
                            </td>
                            <td>${item.validUser}</td>
                            <td>
                                <c:if test="${item.validStatus eq 1}">
                                    审核通过
                                </c:if>
                                <c:if test="${item.validStatus eq 2}">
                                    审核不通过
                                </c:if>
                            </td>
                            <td>${item.validComments}</td>
                            <td>
                                <c:choose>
                                    <c:when test="${item.authStatus != null and item.authStatus == 2}">认证失败</c:when>
                                    <c:when test="${item.authStatus != null and item.authStatus == 1}">已认证</c:when>
                                    <c:when test="${item.authStatus != null and item.authStatus == 0}">未认证</c:when>
                                    <c:otherwise>未认证</c:otherwise>
                                </c:choose>
                            </td>
                            <td>
                                <c:if test="${item.authStatus != null and item.authStatus == 1 and item.authTime != null}">
                                    <jsp:useBean id="authTimeDateValue" class="java.util.Date"/>
                                    <jsp:setProperty name="authTimeDateValue" property="time"
                                                     value="${item.authTime}"/>
                                    <fmt:formatDate value="${authTimeDateValue}" pattern="yyyy-MM-dd HH:mm:ss"/>
                                </c:if>
                            </td>
                            <td>
                                <c:choose>
                                    <c:when test="${item.authMonth != null and item.authMonth == 1}">当月认证</c:when>
                                    <c:when test="${item.authMonth != null and item.authMonth == 2}">非当月认证</c:when>
                                </c:choose>
                            </td>
                            <td>${item.financeVoucherNo}</td>
                            <td>
                                <c:choose>
                                    <c:when test="${item.sendResult != null and item.sendResult == 1}">是</c:when>
                                    <c:otherwise>否</c:otherwise>
                                </c:choose>
                            </td>
                        </tr>
                        <tr style="display: none" id="imgTr${item.hxInvoiceId}">
                            <td colspan="23" height="350px" id="invoiceImg${item.hxInvoiceId}">
                            </td>
                        </tr>
                        <input id="imgFlag${item.hxInvoiceId}" value="0" type="hidden">
                        <input id="imgSrc${item.hxInvoiceId}" value="${item.attachment}" type="hidden">
                    </c:forEach>
                    </tbody>
                </table>

                <c:if test="${empty list}">
                    <!-- 查询无结果弹出 -->
                    <div class="noresult">查询无结果！请尝试使用其他搜索条件。</div>
                </c:if>
            </div>
        </div>
        <div>
            <tags:page page="${page}"/>
            <div class="total">
                <div class="clear"></div>
                <div class="fixtablelastline" style="height: initial;">
                    【本页统计 条目：${pageNum}
                    总金额：<fmt:formatNumber type="number" value="${lzyxAmount - lzzfAmount - hzyxAmount}" pattern="0.00" maxFractionDigits="2" />；
                    不含税总金额：<fmt:formatNumber type="number" value="${lzyxTaxFreeAmount - lzzfTaxFreeAmount - hzyxTaxFreeAmount}" pattern="0.00" maxFractionDigits="2" />；
                    税额总金额：<fmt:formatNumber type="number" value="${lzyxTaxAmount - lzzfTaxAmount - hzyxTaxAmount}" pattern="0.00" maxFractionDigits="2" />；】
                    <br/>
                    【蓝字有效金额总计： <fmt:formatNumber type="number" value="${lzyxAmount}" pattern="0.00" maxFractionDigits="2" />；
                    蓝字有效不含税总金额： <fmt:formatNumber type="number" value="${lzyxTaxFreeAmount}" pattern="0.00" maxFractionDigits="2" />；
                    蓝字有效税额总金额： <fmt:formatNumber type="number" value="${lzyxTaxAmount}" pattern="0.00" maxFractionDigits="2" />；

                    蓝字作废金额总计：<fmt:formatNumber type="number" value="${lzzfAmount}" pattern="0.00" maxFractionDigits="2" />；
                    蓝字作废不含税总金额：<fmt:formatNumber type="number" value="${lzzfTaxFreeAmount}" pattern="0.00" maxFractionDigits="2" />；
                    蓝字作废税额总金额：<fmt:formatNumber type="number" value="${lzzfTaxAmount}" pattern="0.00" maxFractionDigits="2" />；

                    红字有效金额总计：<fmt:formatNumber type="number" value="${hzyxAmount}" pattern="0.00" maxFractionDigits="2" />
                    红字有效不含税总金额：<fmt:formatNumber type="number" value="${hzyxTaxFreeAmount}" pattern="0.00" maxFractionDigits="2" />
                    红字有效税额总金额：<fmt:formatNumber type="number" value="${hzyxTaxAmount}" pattern="0.00" maxFractionDigits="2" /> 】
                </div>
                <div class="clear"></div>
                <div class="fixtablelastline" style="height: initial;">
                    【全部结果  蓝字有效发票：<fmt:formatNumber type="number" value="${invoice.lzyxNum == null ? 0 : invoice.lzyxNum}" pattern="0" maxFractionDigits="0" />；
                    蓝字作废：<fmt:formatNumber type="number" value="${invoice.lzzfNum == null ? 0 : invoice.lzzfNum}" pattern="0" maxFractionDigits="0" />；
                    红字有效：<fmt:formatNumber type="number" value="${invoice.hzyxNum == null ? 0 : invoice.hzyxNum}" pattern="0" maxFractionDigits="0" /> 】
                </div>
                <div class="clear"></div>
                <div class="fixtablelastline" style="height: initial;">
                    【全部结果 条目：${invoice.invoiceCount}
                    总金额：<fmt:formatNumber type="number" value="${invoice.amountCount == null ? 0 : invoice.amountCount}" pattern="0.00" maxFractionDigits="2" /> ；
                    不含税总金额：<fmt:formatNumber type="number" value="${invoice.lzyxTaxFreeAmount - invoice.lzzfTaxFreeAmount - invoice.hzyxTaxFreeAmount}" pattern="0.00" maxFractionDigits="2" />；
                    税额总金额：<fmt:formatNumber type="number" value="${invoice.lzyxTaxAmount - invoice.lzzfTaxAmount - invoice.hzyxTaxAmount}" pattern="0.00" maxFractionDigits="2" />；】
                    <br/>
                    【蓝字有效金额总计：<fmt:formatNumber type="number" value="${invoice.lzyxAmount == null ? 0 : invoice.lzyxAmount}" pattern="0.00" maxFractionDigits="2" />；
                    蓝字有效不含税总金额：<fmt:formatNumber type="number" value="${invoice.lzyxTaxFreeAmount == null ? 0 : invoice.lzyxTaxFreeAmount}" pattern="0.00" maxFractionDigits="2" />；
                    蓝字有效税额总金额：<fmt:formatNumber type="number" value="${invoice.lzyxTaxAmount == null ? 0 : invoice.lzyxTaxAmount}" pattern="0.00" maxFractionDigits="2" />；

                    蓝字作废金额总计：<fmt:formatNumber type="number" value="${invoice.lzzfAmount == null ? 0 : invoice.lzzfAmount}" pattern="0.00" maxFractionDigits="2" />；
                    蓝字作废不含税总金额：<fmt:formatNumber type="number" value="${invoice.lzzfTaxFreeAmount == null ? 0 : invoice.lzzfTaxFreeAmount}" pattern="0.00" maxFractionDigits="2" />；
                    蓝字作废税额总金额：<fmt:formatNumber type="number" value="${invoice.lzzfTaxAmount == null ? 0 : invoice.lzzfTaxAmount}" pattern="0.00" maxFractionDigits="2" />；

                    红字有效金额总计：<fmt:formatNumber type="number" value="${invoice.hzyxAmount == null ? 0 : invoice.hzyxAmount}" pattern="0.00" maxFractionDigits="2" />
                    红字有效不含税总金额：<fmt:formatNumber type="number" value="${invoice.hzyxTaxFreeAmount == null ? 0 : invoice.hzyxTaxFreeAmount}" pattern="0.00" maxFractionDigits="2" />
                    红字有效税额总金额：<fmt:formatNumber type="number" value="${invoice.hzyxTaxAmount == null ? 0 : invoice.hzyxTaxAmount}" pattern="0.00" maxFractionDigits="2" />】
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    function foldItem() {
        var s = $("#foldButton").text();
        if (s === '更多筛选条件') {
            $("#foldButton").text("收起筛选条件");
            $(".fold-li").css("display", "list-item");
        } else {
            $("#foldButton").text("更多筛选条件");
            $(".fold-li").css("display", "none");
        }

    }

    /**
     * 重置所有框内容
     */
    function searchReset() {
        $('#keyword').val('');
        $('#startAddDateStr').val('');
        $('#endAddDateStr').val('');
        $('#invoiceAmountFrom').val('');
        $('#invoiceAmountTo').val('');
        $('.selector').each(function (i, j) {
            var options = $(j).find("option");
            options.attr("selected", false);
            options.first().attr("selected", true);
        })
    }

    /**
     * 校验数字框输入合法性
     */
    function checkValue(obj) {
        if ($(obj).val() != undefined && $(obj).val() != '') {
            var reg = /^\d+(?=\.{0,1}\d+$|$)/;
            if (!reg.test($(obj).val())) {
                layer.alert('请输入数字!');
                $(obj).val('');
            }
        }
    }

    /**
     * 抓取发票操作
     */
    function captureInvoiceInit() {
        var open = layer.open({
            type: 1,
            title: '抓取发票',
            shadeClose: false,
            area : ['600px', '220px'],
            content: '<iframe style="width: calc(100% - 10px);height: calc(100% - 10px); border: 0;" src="/finance/invoice/batchCaptureInvoiceInit.do"></iframe>',
            success: function(layero, index){

            },
            error: function (data) {
                if (data.status == 1001) {
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    }

    /**
     * 进项票导出页面
     */
    function reportHxInvoice() {
        // window.open("http://ezadmin.ivedeng.com/ezlist/list/list.html?pageId=301");
    }

    /**
     * 查看供应商详情
     *
     * @param trader
     */
    function viewTraderInfo(trader) {
        parent.viewTraderInfo(trader);
    }

    /**
     * 查看采购订单信息
     * */
    function viewBuyOrder(buyOrderId,type) {
        parent.viewBuyOrderInfo(buyOrderId,type);
    }
</script>
<%@ include file="../../../common/footer.jsp" %>