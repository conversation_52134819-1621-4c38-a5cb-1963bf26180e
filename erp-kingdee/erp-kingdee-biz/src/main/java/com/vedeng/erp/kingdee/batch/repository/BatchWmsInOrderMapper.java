package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchWmsInOrderDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2023/12/11 9:05
 **/
public interface BatchWmsInOrderMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(BatchWmsInOrderDto record);

    int insertSelective(BatchWmsInOrderDto record);

    BatchWmsInOrderDto selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(BatchWmsInOrderDto record);

    int updateByPrimaryKey(BatchWmsInOrderDto record);

    List<BatchWmsInOrderDto> selectByWmsNo(@Param("wmsNo")String wmsNo);


}