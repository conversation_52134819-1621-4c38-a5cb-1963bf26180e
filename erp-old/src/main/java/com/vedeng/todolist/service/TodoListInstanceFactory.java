package com.vedeng.todolist.service;

import com.vedeng.todolist.constant.TodoListBuzSceneEnum;
import com.vedeng.todolist.service.impl.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 待办事项对象工厂类
 * @Author: daniel
 * @Date: 2020/12/11 09 34
 * @Description:
 */
@Service
public class TodoListInstanceFactory {

    @Autowired
    private ZlbCheckTraderCustomerCertificate zlbCheckTraderCustomerCertificate;

    @Autowired
    private ZlbCheckSaleOrder zlbCheckSaleOrder;

    @Autowired
    private ZlbCheckBuyOrder zlbCheckBuyOrder;

    @Autowired
    private RiskCheckTraderCustomerCertificate riskCheckTraderCustomerCertificate;

    @Autowired
    private RiskCheckTraderCustomerData riskCheckTraderCustomerData;

    @Autowired
    private RiskCheckTraderSupplyData riskCheckTraderSupplyData;

    @Autowired
    private RiskCheckSkuData riskCheckSkuData;

    @Autowired
    private MaintainDataSpu maintainDataSpu;

    @Autowired
    private MaintainDataSku maintainDataSku;

    @Autowired
    private MaintainDataDeliveryTime maintainDataDeliveryTime;

    @Autowired
    private MaintainDataPrice maintainDataPrice;

    @Autowired
    private MaintainDataAfterSalePolicy maintainDataAfterSalePolicy;

    @Autowired
    private MaintainDataReportInfo maintainDataReportInfo;

    @Autowired
    private MaintainDataSignContractMode maintainDataSignContractMode;

    @Autowired
    private MaintainDataSupplyAfterSalePolicy maintainDataSupplyAfterSalePolicy;

    @Autowired
    private MaintainDataOperationInfo maintainDataOperationInfo;

    public ITodoInstance createTodoInstance(TodoListBuzSceneEnum buzScene){

        if (buzScene == TodoListBuzSceneEnum.ZLB_CHECK_TRADER_CUSTOMER_CERTIFICATE) {
            return zlbCheckTraderCustomerCertificate;
        } else if (buzScene == TodoListBuzSceneEnum.ZLB_CHECK_SALE_ORDER){
            return zlbCheckSaleOrder;
        } else if (buzScene == TodoListBuzSceneEnum.ZLB_CHECK_BUY_ORDER){
            return zlbCheckBuyOrder;
        } else if (buzScene == TodoListBuzSceneEnum.RISK_CHECK_TRADER_CUSTOMER_CERTIFICATE){
            return riskCheckTraderCustomerCertificate;
        } else if (buzScene == TodoListBuzSceneEnum.RISK_CHECK_TRADER_CUSTOMER_DATA){
            return riskCheckTraderCustomerData;
        } else if (buzScene == TodoListBuzSceneEnum.RISK_CHECK_TRADER_SUPPLY_DATA){
            return riskCheckTraderSupplyData;
        } else if (buzScene == TodoListBuzSceneEnum.RISK_CHECK_SKU_DATA){
            return riskCheckSkuData;
        } else if (buzScene == TodoListBuzSceneEnum.MAINTAIN_DATA_SPU){
            return maintainDataSpu;
        } else if (buzScene == TodoListBuzSceneEnum.MAINTAIN_DATA_SKU){
            return maintainDataSku;
        } else if (buzScene == TodoListBuzSceneEnum.MAINTAIN_DATA_DELIVERY_TIME){
            return maintainDataDeliveryTime;
        } else if (buzScene == TodoListBuzSceneEnum.MAINTAIN_DATA_AFTER_SALE_POLICY){
            return maintainDataAfterSalePolicy;
        } else if (buzScene == TodoListBuzSceneEnum.MAINTAIN_DATA_PRICE){
            return maintainDataPrice;
        } else if (buzScene == TodoListBuzSceneEnum.MAINTAIN_DATA_SUPPLY_AFTER_SALE_POLICY){
            return maintainDataSupplyAfterSalePolicy;
        } else if (buzScene == TodoListBuzSceneEnum.MAINTAIN_DATA_OPERATION_INFO){
            return maintainDataOperationInfo;
        } else if (buzScene == TodoListBuzSceneEnum.MAINTAIN_DATA_REPORT_INFO){
            return maintainDataReportInfo;
        } else if (buzScene == TodoListBuzSceneEnum.MAINTAIN_DATA_SIGN_CONTRACT_MODE){
            return maintainDataSignContractMode;
        }
        return null;
    }
}
