package com.vedeng.erp.settlement.mapper;

import com.vedeng.erp.settlement.domain.entity.SettlementBillEntity;

import java.util.Collection;
import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface SettlementBillMapper {
    int deleteByPrimaryKey(Integer settleBillId);

    int insert(SettlementBillEntity record);

    int insertSelective(SettlementBillEntity record);

    SettlementBillEntity selectByPrimaryKey(Integer settleBillId);

    int updateByPrimaryKeySelective(SettlementBillEntity record);

    int updateByPrimaryKey(SettlementBillEntity record);

    int updateBatchSelective(List<SettlementBillEntity> list);

    int batchInsert(List<SettlementBillEntity> list);

    int updateIsDeleteBySettleBillId(@Param("updatedIsDelete") Integer updatedIsDelete, @Param("settleBillId") Integer settleBillId);

    SettlementBillEntity findByBusinessSourceTypeIdAndSourceType(@Param("businessSourceTypeId")Integer businessSourceTypeId,@Param("sourceType")String sourceType);


}