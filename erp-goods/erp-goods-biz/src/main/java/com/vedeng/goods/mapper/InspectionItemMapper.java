package com.vedeng.goods.mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

import com.vedeng.goods.domain.entity.InspectionItem;

/**
 * <AUTHOR>
 * @description 针对表【T_INSPECTION_ITEM(检查项目)】的数据库操作Mapper
 * @createDate 2022-02-10 14:28:51
 */
public interface InspectionItemMapper{

    /**
     * 获取所有检查项目并根据sort排序
     *
     * @return List<InspectionItem>
     */
    List<InspectionItem> findAllOrderBySort();

    List<InspectionItem> findAllByNameAndIsDeleted(@Param("name")String name,@Param("isDeleted")Integer isDeleted);


}
