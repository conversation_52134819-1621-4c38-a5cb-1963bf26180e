<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>


<div id="app" style="display: none;">

    <div class="container mx-auto">
        <el-steps finish-status="success" align-center>
            <template v-for="(item, index) in stepItems">
                <el-step :title="item.title" :status="item.type">
                    <template slot="icon">
                        <div class="el-step__icon-inner">&nbsp;</div>
                    </template>
                </el-step>
            </template>
        </el-steps>
    </div>

    <el-descriptions title="基本信息" border column="2">
        <el-descriptions-item label="售后单号" content-class-name="my-content">
            {{expenseAfterSalesDetail.expenseAfterSalesNo}}
        </el-descriptions-item>
        <el-descriptions-item label="售后类型">
            <span v-if="expenseAfterSalesDetail.expenseAfterSalesType == 4121" style="color: red">采购费用订单退货</span>
            <span v-else-if="expenseAfterSalesDetail.expenseAfterSalesType == 4122" style="color: red">采购费用订单退票</span>
            <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="创建人">{{expenseAfterSalesDetail.creatorName}}</el-descriptions-item>
        <el-descriptions-item label="售后状态">
            <span v-if="expenseAfterSalesDetail.expenseAfterSalesStatusDto.afterSalesStatus == 0">待确认</span>
            <span v-else-if="expenseAfterSalesDetail.expenseAfterSalesStatusDto.afterSalesStatus == 1">进行中</span>
            <span v-else-if="expenseAfterSalesDetail.expenseAfterSalesStatusDto.afterSalesStatus == 2">已完结</span>
            <span v-else-if="expenseAfterSalesDetail.expenseAfterSalesStatusDto.afterSalesStatus == 3">已关闭</span>
            <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="审核状态">
            <span v-if="expenseAfterSalesDetail.expenseAfterSalesStatusDto.auditStatus == 0">待审核</span>
            <span v-else-if="expenseAfterSalesDetail.expenseAfterSalesStatusDto.auditStatus == 1">审核中</span>
            <span v-else-if="expenseAfterSalesDetail.expenseAfterSalesStatusDto.auditStatus == 2">审核通过</span>
            <span v-else-if="expenseAfterSalesDetail.expenseAfterSalesStatusDto.auditStatus == 3">审核不通过</span>
            <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="生效状态">
            <span v-if="expenseAfterSalesDetail.expenseAfterSalesStatusDto.validStatus == 0">未生效</span>
            <span v-else-if="expenseAfterSalesDetail.expenseAfterSalesStatusDto.validStatus == 1">已生效</span>
            <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{parseTime(expenseAfterSalesDetail.addTime)}}</el-descriptions-item>
        <el-descriptions-item label="生效时间">
            <span v-if="expenseAfterSalesDetail.expenseAfterSalesStatusDto.validStatus == 1">{{parseTime(expenseAfterSalesDetail.expenseAfterSalesStatusDto.validTime)}}</span>
            <span v-else>-</span>
        </el-descriptions-item>
    </el-descriptions>

    <template>
        <el-tabs v-model="activeName" style="margin-top: 20px;">
            <el-tab-pane label="订单审核" name="orderVerify">
                <div class="container mx-auto">
                    <el-steps finish-status="success" align-center>
                        <template v-for="(item, index) in verifySteps">
                            <el-step :title="item.title" :status="item.type">
                                <template slot="description">
                                    <template v-for="(da,ind) in item.descriptions">

                                        <template v-if="ind==2">
                                            <template v-if="da != ''&&da!=null&&da!=undefined">
                                                <p class="break-all" >
                                                    备注：{{splitStrAddAllipsis(da,14)}}
                                                    <template v-if="isSplitStrAddAllipsis(da,14)">
                                                        <el-popover trigger="hover" placement="right" >
                                                            <p class="break-all w-28">备注：{{da}}</p>
                                                            <template slot="reference" >
                                                                <i class="el-icon-copy-document"></i>
                                                            </template>
                                                        </el-popover>
                                                    </template>
                                                </p>
                                            </template>
                                        </template>

                                        <template v-if="ind==0">
                                            <p class="break-all" >{{splitStrAddAllipsis(da,14)}}
                                                <template v-if="isSplitStrAddAllipsis(da,14)">
                                                    <el-popover trigger="hover" placement="right" >
                                                        <p class="break-all w-28">{{da}}</p>
                                                        <template slot="reference" >
                                                            <i class="el-icon-copy-document"></i>
                                                        </template>
                                                    </el-popover>
                                                </template>
                                            </p>
                                        </template>

                                        <template v-if="ind==1">
                                            <p class="break-all">{{da}}</p>
                                        </template>
                                    </template>
                                </template>
                            </el-step>
                        </template>
                    </el-steps>
                </div>
                <div class="container mx-auto" v-if="expenseAfterSalesDetail.isAuto==1" align="center">
                    <span align="center">该订单已自动审核，无需线下人工审核</span>
                </div>
            </el-tab-pane>
        </el-tabs>
    </template>



    <el-descriptions title="售后申请" border column="2">
        <el-descriptions-item label="售后原因" content-class-name="my-content">
            <span v-if="expenseAfterSalesDetail.expenseAfterSalesReason == 4200">退货</span>
            <span v-else-if="expenseAfterSalesDetail.expenseAfterSalesReason == 4201">退款</span>
            <span v-else-if="expenseAfterSalesDetail.expenseAfterSalesReason == 4202">重做</span>
            <span v-else-if="expenseAfterSalesDetail.expenseAfterSalesReason == 4203">其他</span>
            <span v-else-if="expenseAfterSalesDetail.expenseAfterSalesReason == 4204">供应商开错且已跨月</span>
            <span v-else-if="expenseAfterSalesDetail.expenseAfterSalesReason == 4205">发票录错</span>
            <span v-else-if="expenseAfterSalesDetail.expenseAfterSalesReason == 4206">其他</span>
            <span v-else-if="expenseAfterSalesDetail.expenseAfterSalesReason == 4284">销售联动退货</span>
            <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="联系人" v-if="expenseAfterSalesDetail.expenseAfterSalesType == 4121">{{expenseAfterSalesDetail.traderContactName}}</el-descriptions-item>
        <el-descriptions-item label="电话" v-if="expenseAfterSalesDetail.expenseAfterSalesType == 4121">
            <i class="el-icon-phone" style="color: #409eff;" v-if="expenseAfterSalesDetail.traderContactTelephone != null && expenseAfterSalesDetail.traderContactTelephone != ''"
               @click="call(expenseAfterSalesDetail.traderContactTelephone)"></i>
            {{expenseAfterSalesDetail.traderContactTelephone}}
        </el-descriptions-item>
        <el-descriptions-item label="手机" v-if="expenseAfterSalesDetail.expenseAfterSalesType == 4121">
            <i class="el-icon-phone" style="color: #409eff;" v-if="expenseAfterSalesDetail.traderContactMobile != null && expenseAfterSalesDetail.traderContactMobile != ''"
               @click="call(expenseAfterSalesDetail.traderContactMobile)"></i>
            {{expenseAfterSalesDetail.traderContactMobile}}
        </el-descriptions-item>
        <el-descriptions-item label="款项退还" v-if="expenseAfterSalesDetail.expenseAfterSalesType == 4121">
            <span v-if="expenseAfterSalesDetail.refundMethod == 1">退至公司账户</span>
            <span v-else-if="expenseAfterSalesDetail.refundMethod == 2">退至供应商余额</span>
            <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="详情说明">
            <template v-for="orderRemark in expenseAfterSalesDetail.orderDesc">
                <template v-for="goodsDto in orderRemark.originalOrderDto.goodsDtos">
                    <template v-if="orderRemark.type == 1">
                        该订单由销售单
                        <el-link type="primary" :underline="false" @click="viewSaleOrderInfo(orderRemark.originalOrderDto.orderId)" style="font-weight: 700;">{{ orderRemark.originalOrderDto.no }}</el-link>
                        中
                        <el-link type="primary" :underline="false" @click="viewSkuInfo(goodsDto.goodsId)" style="font-weight: 700;">{{ goodsDto.sku }}</el-link>
                        订货号退货，联动采购费用单
                        <el-link type="primary" :underline="false" @click="viewExpenseDetail(orderRemark.resultOrderDto.orderId)" style="font-weight: 700;">{{ orderRemark.resultOrderDto.no }}</el-link>
                        退货自动生成
                        <br>
                    </template>
                    <template v-if="orderRemark.type == 2">
                        该订单由销售单
                        <el-link type="primary" :underline="false" @click="viewSaleOrderInfo(orderRemark.originalOrderDto.orderId)" style="font-weight: 700;">{{ orderRemark.originalOrderDto.no }}</el-link>
                        中
                        <el-link type="primary" :underline="false" @click="viewSkuInfo(goodsDto.goodsId)" style="font-weight: 700;">{{ goodsDto.sku }}</el-link>
                        退货，联动采购费用单
                        <el-link type="primary" :underline="false" @click="viewExpenseDetail(orderRemark.resultOrderDto.orderId)" style="font-weight: 700;">{{ orderRemark.resultOrderDto.no }}</el-link>
                        退货，一键转单生成
                        <br>
                    </template>
                </template>
            </template>
<%--            <div v-html="getExpenseAfterSalesDetailComments(expenseAfterSalesDetail)"></div>--%>
            <span>{{expenseAfterSalesDetail.expenseAfterSalesComments}}</span>
        </el-descriptions-item>
        <el-descriptions-item label="附件">
            <el-link type="primary" :underline="false" v-for="item in expenseAfterSalesDetail.attachmentList" @click="viewAttachment(item.domain, item.uri)">
                {{item.name}}&nbsp;&nbsp;&nbsp;&nbsp;
            </el-link>

        </el-descriptions-item>
    </el-descriptions>

    <el-descriptions title="所属订单" border column="2" style="margin-bottom: 20px">
        <el-descriptions-item label="采购费用单号" content-class-name="my-content">
            <el-tooltip placement="bottom" effect="light">
                <div slot="content" class="tips">
                    付款状态：<span v-if="expenseAfterSalesDetail.buyorderExpenseDto.paymentStatus == 0">未付款</span>
                    <span v-else-if="expenseAfterSalesDetail.buyorderExpenseDto.paymentStatus == 1">部分付款</span>
                    <span v-else-if="expenseAfterSalesDetail.buyorderExpenseDto.paymentStatus == 2">全部付款</span>
                    <span v-else>-</span><br/>
                    收票状态：<span v-if="expenseAfterSalesDetail.buyorderExpenseDto.invoiceStatus == 0">未收票</span>
                    <span v-else-if="expenseAfterSalesDetail.buyorderExpenseDto.invoiceStatus == 1">部分收票</span>
                    <span v-else-if="expenseAfterSalesDetail.buyorderExpenseDto.invoiceStatus == 2">全部收票</span>
                    <span v-else>-</span>
                </div>
                <template>
                    <div class="content">
                        <el-link type="primary" :underline="false"
                                 @click="viewExpenseDetail(expenseAfterSalesDetail.buyorderExpenseDto.buyorderExpenseId)">
                            {{expenseAfterSalesDetail.buyorderExpenseDto.buyorderExpenseNo}}
                        </el-link>
                    </div>
                </template>
            </el-tooltip>
        </el-descriptions-item>
        <el-descriptions-item label="订单金额">
            {{fixed(expenseAfterSalesDetail.buyorderExpenseDto.buyorderExpenseDetailDto.totalAmount)}}
        </el-descriptions-item>
        <el-descriptions-item label="部门">{{expenseAfterSalesDetail.buyorderExpenseDto.buyorderExpenseDetailDto.orgName}}</el-descriptions-item>
        <el-descriptions-item label="创建人">{{expenseAfterSalesDetail.buyorderExpenseDto.creatorName}}</el-descriptions-item>
        <el-descriptions-item label="订单状态">
            <span v-if="expenseAfterSalesDetail.buyorderExpenseDto.status == 0">待确认</span>
            <span v-else-if="expenseAfterSalesDetail.buyorderExpenseDto.status == 1">进行中</span>
            <span v-else-if="expenseAfterSalesDetail.buyorderExpenseDto.status == 2">已完结</span>
            <span v-else-if="expenseAfterSalesDetail.buyorderExpenseDto.status == 3">已关闭</span>
            <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="生效时间">{{parseTime(expenseAfterSalesDetail.buyorderExpenseDto.validTime)}}</el-descriptions-item>
        <el-descriptions-item label="供应商名称">
            <el-tooltip placement="bottom" effect="light">
                <div slot="content" class="tips">
                    交易次数：{{expenseAfterSalesDetail.buyorderExpenseDto.buyorderExpenseDetailDto.orderCount}}<br/>
                    交易金额：{{expenseAfterSalesDetail.buyorderExpenseDto.buyorderExpenseDetailDto.orderTotalAmount}}<br/>
                    上次交易时间：{{parseTime(expenseAfterSalesDetail.buyorderExpenseDto.buyorderExpenseDetailDto.lastOrderTime)}}
                </div>
                <template>
                    <div class="content">
                        <el-link type="primary" :underline="false"
                                 @click="viewTraderSupplier(expenseAfterSalesDetail.buyorderExpenseDto.buyorderExpenseDetailDto.traderId)">
                            {{expenseAfterSalesDetail.buyorderExpenseDto.buyorderExpenseDetailDto.traderName}}
                        </el-link>
                    </div>
                </template>
            </el-tooltip>
        </el-descriptions-item>
        <el-descriptions-item label="供应商等级">
            <span v-if="expenseAfterSalesDetail.buyorderExpenseDto.buyorderExpenseDetailDto.traderGrade == 59">核心供应商</span>
            <span v-else-if="expenseAfterSalesDetail.buyorderExpenseDto.buyorderExpenseDetailDto.traderGrade == 60">普通供应商</span>
            <span v-else-if="expenseAfterSalesDetail.buyorderExpenseDto.buyorderExpenseDetailDto.traderGrade == 62">临时供应商</span>
            <span v-else>-</span>
        </el-descriptions-item>
    </el-descriptions>

    <el-card class="box-card" v-if="expenseAfterSalesDetail.expenseAfterSalesType == 4121">
        <div slot="header" class="clearfix">
            <span>退货信息</span>
        </div>
        <template>
            <el-table
                    :data="expenseAfterSalesDetail.expenseAfterSalesItemDtoList"
                    border
                    key="returnItem"
                    style="width: 100%;"
                    :cell-style="{'text-align':'center'}"
                    :header-cell-style="{'text-align':'center'}">
                <el-table-column
                        type="index"
                        label="序号"
                        min-width="5%">
                </el-table-column>
                <el-table-column
                        prop="sku"
                        label="订货号"
                        min-width="10%">
                </el-table-column>
                <el-table-column
                        label="产品名称"
                        min-width="30%">
                        <template slot-scope="scope">
                            <el-link type="primary" :underline="false" @click="viewSkuInfo(scope.row.goodsId)">{{ scope.row.goodsName }}</el-link>
                        </template>
                </el-table-column>
                <el-table-column
                        prop="expenseCategoryName"
                        label="费用类别"
                        min-width="15%">
                </el-table-column>
                <el-table-column
                        prop="num"
                        label="采购数量"
                        min-width="10%">
                </el-table-column>
                <el-table-column
                        prop="price"
                        label="单价"
                        min-width="10%">
                        <template slot-scope="scope">
                            <span>{{scope.row.price.toFixed(2)}}</span>
                        </template>

                </el-table-column>
                <el-table-column
                        label="总额"
                        min-width="10%">
                        <template slot-scope="scope">
                            <span>{{ (scope.row.num *  scope.row.price).toFixed(2)}}</span>
                        </template>
                </el-table-column>
                <el-table-column
                        prop="returnNum"
                        label="退货数量"
                        min-width="10%">
                </el-table-column>
            </el-table>
            <el-row class="my-sum">退货总件数：<span>{{this.totalNum}}</span></el-row>
        </template>
    </el-card>

    <el-card class="box-card" v-if="expenseAfterSalesDetail.expenseAfterSalesType == 4122">
    <div slot="header" class="clearfix">
        <span>退票商品</span>
    </div>
    <template>
        <el-table
                :data="expenseAfterSalesDetail.returnInvoiceGoodsDtoList"
                border
                style="width: 100%; "
                key="returnInvoiceGoodsDtoList"
                :cell-style="{'text-align':'center'}"
                :header-cell-style="{'text-align':'center'}">
            <el-table-column
                    type="index"
                    label="序号"
                    min-width="5%">
            </el-table-column>
            <el-table-column
                    label="产品名称"
                    min-width="30%">
                    <template slot-scope="scope">
                        <el-link type="primary" :underline="false" @click="viewSkuInfo(scope.row.goodsId)">{{ scope.row.goodsName }}</el-link><br>
                        <span>{{ scope.row.sku}}</span>
                    </template>
            </el-table-column>
            <el-table-column
                    prop="brandName"
                    label="品牌"
                    min-width="10%">
            </el-table-column>
            <el-table-column
                    label="型号"
                    prop="model"
                    min-width="10%">
            </el-table-column>
            <el-table-column
                    prop="price"
                    label="采购价"
                    min-width="10%">
            </el-table-column>
            <el-table-column
                    prop="num"
                    label="数量"
                    min-width="10%">
            </el-table-column>
            <el-table-column
                    prop="unitName"
                    label="单位"
                    min-width="10%">
            </el-table-column>
            <el-table-column
                    prop="returnNum"
                    label="本次退票数"
                    min-width="10%">
            </el-table-column>
            <el-table-column
                    label="退票状态"
                    min-width="10%">
                    <template slot-scope="scope">
                        <span v-if="scope.row.returnInvoiceStatus == 0" style="color: red">未退票</span>
                        <span v-else-if="scope.row.returnInvoiceStatus == 1">已退票</span>
                        <span v-else-if="scope.row.returnInvoiceStatus == 2">退票中</span>
                        <span v-else>-</span>
                    </template>
            </el-table-column>
        </el-table>
    </template>
</el-card>

    <el-row style="text-align: center; margin-bottom: 20px">
        <el-button type="primary" size="small" v-if="expenseAfterSalesDetail.expenseAfterSalesStatusDto.afterSalesStatus == 0 && expenseAfterSalesDetail.expenseAfterSalesStatusDto.auditStatus != 1
                                                    && isOperator && expenseAfterSalesDetail.expenseAfterSalesReason != 4284" @click="closeAfterSale()">关闭订单</el-button>

        <el-button type="primary" size="small" v-if="expenseAfterSalesDetail.expenseAfterSalesStatusDto.afterSalesStatus == 0 && isOperator &&
                                                    expenseAfterSalesDetail.expenseAfterSalesStatusDto.auditStatus != 1" @click="editAfterSale()">编辑</el-button>

        <el-button type="primary" size="small" v-if="expenseAfterSalesDetail.expenseAfterSalesStatusDto.afterSalesStatus == 0 && isOperator &&
                    (expenseAfterSalesDetail.expenseAfterSalesStatusDto.auditStatus == 0 || expenseAfterSalesDetail.expenseAfterSalesStatusDto.auditStatus == 3)"
                   @click="applyValid()">申请审核</el-button>

        <el-button type="primary" size="small" v-if="expenseAfterSalesDetail.expenseAfterSalesStatusDto.auditStatus == 1 && isCheckUser"
                   @click="auditRecord(true)">审核通过</el-button>

        <el-button  type="primary" size="small" v-if="expenseAfterSalesDetail.expenseAfterSalesStatusDto.auditStatus == 1 && isCheckUser"
                    @click="auditRecord(false)">审核不通过</el-button>

        <el-button type="info" disabled size="small" v-if="expenseAfterSalesDetail.expenseAfterSalesStatusDto.auditStatus == 1 && isOperator && !isCheckUser">已申请审核</el-button>

        <el-button type="primary" size="small" v-if="expenseAfterSalesDetail.expenseAfterSalesStatusDto.refundStatus != -1 && expenseAfterSalesDetail.expenseAfterSalesStatusDto.afterSalesStatus != 2"
                   @click="completeExpenseAfterSale()">完结订单</el-button>

        <el-button type="primary" size="small" @click="executeRefundOperation()"
                   v-if="expenseAfterSalesDetail.expenseAfterSalesStatusDto.validStatus == 1 && expenseAfterSalesDetail.expenseAfterSalesStatusDto.afterSalesStatus == 1 &&
                        expenseAfterSalesDetail.expenseAfterSalesStatusDto.auditStatus == 2 && expenseAfterSalesDetail.expenseAfterSalesType == 4121 &&
                        expenseAfterSalesDetail.expenseAfterSalesStatusDto.refundStatus == -1 && isOperator">执行退款运算</el-button>
    </el-row>


    <el-card class="box-card">
        <div slot="header" class="clearfix">
            <span>退票信息与记录</span>
        </div>
        <div class="my-title-style">
            <div style="font-size: 14px;">
                <i class="el-icon-success" style="color: #5fb878;" v-if="expenseAfterSalesDetail.expenseAfterSalesStatusDto.returnInvoiceStatus == 3"></i>
                <i class="el-icon-warning" style="color: #FF7F50;" v-else></i>
                <span>售后单退票状态：</span>
                <span v-if="expenseAfterSalesDetail.expenseAfterSalesStatusDto.returnInvoiceStatus == 0">无退票</span>
                <span v-else-if="expenseAfterSalesDetail.expenseAfterSalesStatusDto.returnInvoiceStatus == 1">未退票</span>
                <span v-else-if="expenseAfterSalesDetail.expenseAfterSalesStatusDto.returnInvoiceStatus == 2">部分退票</span>
                <span v-else-if="expenseAfterSalesDetail.expenseAfterSalesStatusDto.returnInvoiceStatus == 3">全部退票</span>
                <span v-else>-</span>
            </div>
        </div>
        <template>
            <el-table
                    :data="refundInvoiceList"
                    border
                    key="refundInvoiceList"
                    style="width: 100%; "
                    :cell-style="{'text-align':'center'}"
                    :header-cell-style="{'text-align':'center'}">
                <el-table-column
                        prop="invoiceNo"
                        label="发票号"
                        width="180">
                </el-table-column>
                <el-table-column
                        label="发票金额">
                        <template slot-scope="scope">
                            <span>{{scope.row.invoiceAmount.toFixed(2)}}</span>
                        </template>
                </el-table-column>
                <el-table-column
                        label="票种"
                        width="180">
                        <template slot-scope="scope">
                            <span v-if="scope.row.invoiceType == 429">17%增值税专用发票</span>
                            <span v-else-if="scope.row.invoiceType == 430">17%增值税普通发票</span>
                            <span v-else-if="scope.row.invoiceType == 681">16%增值税普通发票</span>
                            <span v-else-if="scope.row.invoiceType == 682">16%增值税专用发票</span>
                            <span v-else-if="scope.row.invoiceType == 683">6%增值税普通发票</span>
                            <span v-else-if="scope.row.invoiceType == 684">6%增值税专用发票</span>
                            <span v-else-if="scope.row.invoiceType == 685">3%增值税普通发票</span>
                            <span v-else-if="scope.row.invoiceType == 686">3%增值税专用发票</span>
                            <span v-else-if="scope.row.invoiceType == 687">0%增值税普通发票</span>
                            <span v-else-if="scope.row.invoiceType == 971">13%增值税普通发票</span>
                            <span v-else-if="scope.row.invoiceType == 972">13%增值税专用发票</span>
                            <span v-else-if="scope.row.invoiceType == 1758">1%增值税专用发票</span>
                            <span v-else>-</span>
                        </template>
                </el-table-column>
                <el-table-column
                        label="寄送状态"
                        width="180">
                        <template>
                            未寄送
                        </template>
                </el-table-column>
                <el-table-column
                        label="退票状态">
                        <template slot-scope="scope">
                            <span v-if="scope.row.refundInvoiceStatus == 0">未退票</span>
                            <span v-else-if="scope.row.refundInvoiceStatus == 1">已退票</span>
                            <span v-else-if="scope.row.refundInvoiceStatus == 2">退票中</span>
                            <span v-else-if="scope.row.refundInvoiceStatus == 3">无需退票</span>
                            <span v-else>-</span>
                        </template>
                </el-table-column>
                <el-table-column
                        label="操作"
                        width="180">
                        <template slot-scope="scope">
                            <el-link type="primary" :underline="false" v-if="topOrgId == 6 && expenseAfterSalesDetail.expenseAfterSalesType == 4121 && scope.row.refundInvoiceStatus == 0"
                                     :disabled="expenseAfterSalesDetail.expenseAfterSalesStatusDto.auditStatus != 2" @click="reversalInvoices(scope.row.invoiceCode,scope.row.invoiceNo)">申请冲销</el-link>
                            <el-link type="primary" :underline="false" v-if="topOrgId == 6 && expenseAfterSalesDetail.expenseAfterSalesType == 4121 && scope.row.refundInvoiceStatus == 0"
                                     :disabled="expenseAfterSalesDetail.expenseAfterSalesStatusDto.auditStatus != 2" @click="noNeedRefundInvoice(scope.row.invoiceCode,scope.row.invoiceNo)">无需退票</el-link>
                            <el-link type="primary" :underline="false" v-if="topOrgId == 8 && scope.row.refundInvoiceStatus == 0" :disabled="expenseAfterSalesDetail.expenseAfterSalesStatusDto.auditStatus != 2"
                                     @click="confirmRefundInvoice(scope.row.invoiceCode, scope.row.invoiceNo)">确认退票</el-link>
                        </template>
                </el-table-column>
            </el-table>
        </template>
    </el-card>

    <el-card class="box-card">
        <div slot="header" class="clearfix">
            <span>发票记录</span>
        </div>
        <template>
            <el-table
                    :data="invoiceList"
                    border
                    style="width: 100%; "
                    :cell-style="{'text-align':'center'}"
                    :header-cell-style="{'text-align':'center'}">
                <el-table-column
                        prop="invoiceNo"
                        label="发票号"
                        min-width="20%">
                </el-table-column>
                <el-table-column
                        prop="invoiceTypeName"
                        label="票种"
                        min-width="10%">
                </el-table-column>
                <el-table-column
                        label="红蓝字"
                        min-width="10%">
                        <template slot-scope="scope">
                            <span v-if="scope.row.colorType == 1 && scope.row.colorComplementType == 1">蓝字冲销</span>
                            <span v-else-if="scope.row.colorType == 1 && scope.row.colorComplementType == 0">红字有效</span>
                            <span v-else>-</span>
                        </template>
                </el-table-column>
                <el-table-column
                        label="发票金额"
                        min-width="10%">
                        <template slot-scope="scope">
                            <span>{{scope.row.amount.toFixed(2)}}</span>
                        </template>
                </el-table-column>
                <el-table-column
                        prop="creatorName"
                        label="操作人"
                        min-width="10%">
                </el-table-column>
                <el-table-column
                        label="操作时间"
                        min-width="10%">
                        <template slot-scope="scope">
                            <span>{{parseTime(scope.row.addTime)}}</span>
                        </template>
                </el-table-column>
                <el-table-column
                        label="审核状态"
                        min-width="10%">
                        <template slot-scope="scope">
                            <span v-if="scope.row.validStatus == 0">待审核</span>
                            <span v-else-if="scope.row.validStatus == 1">审核通过</span>
                            <span v-else-if="scope.row.validStatus == 2">审核不通过</span>
                            <span v-else>-</span>
                        </template>
                </el-table-column>
                <el-table-column
                        prop="validUserName"
                        label="审核人"
                        min-width="10%">
                </el-table-column>
                <el-table-column
                        label="审核时间 "
                        min-width="10%">
                        <template slot-scope="scope">
                            <span>{{parseTime(scope.row.validTime)}}</span>
                        </template>
                </el-table-column>
            </el-table>
        </template>
    </el-card>

    <el-card class="box-card" v-if="expenseAfterSalesDetail.expenseAfterSalesType == 4121">
        <div slot="header" class="clearfix">
            <span>退款信息</span>
        </div>
        <template>
            <el-table
                    :data="refundInfoList"
                    border
                    style="width: 100%; "
                    :cell-style="{'text-align':'center'}"
                    :header-cell-style="{'text-align':'center'}">
                <el-table-column
                        prop="paymentAmount"
                        label="已付款金额（不含信用支付）"
                        width="180">
                </el-table-column>
                <el-table-column
                        label="退货金额"
                        width="180">
                        <template slot-scope="scope">
                            <span>{{scope.row.returnAmount.toFixed(2)}}</span>
                        </template>
                </el-table-column>
                <el-table-column
                        label="偿还账期">
                        <template slot-scope="scope">
                            <span>{{scope.row.repaymentPeriod.toFixed(2)}}</span>
                        </template>
                </el-table-column>
                <el-table-column
                        label="款项退还"
                        width="180">
                        <template slot-scope="scope">
                            <span v-if="scope.row.refundMethod == 1">退至公司账户</span>
                            <span v-else-if="scope.row.refundMethod == 2">退至供应商余额</span>
                            <span v-else>-</span>
                        </template>
                </el-table-column>
                <el-table-column
                        v-if="topOrgId == 6"
                        label="应退金额"
                        width="180">
                        <template slot-scope="scope">
                            <span>{{scope.row.needReturnAmount.toFixed(2)}}</span>
                        </template>
                </el-table-column>
                <el-table-column
                        v-if="topOrgId == 8"
                        label="实退金额"
                        width="180">
                    <template slot-scope="scope">
                        <span>{{scope.row.needReturnAmount.toFixed(2)}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="已退金额">
                        <template slot-scope="scope">
                            <span>{{scope.row.haveReturnAmount.toFixed(2)}}</span>
                        </template>
                </el-table-column>
                <el-table-column
                        label="退款状态"
                        width="180">
                        <template slot-scope="scope">
                            <span v-if="scope.row.refundStatus == 0">无退款</span>
                            <span v-else-if="scope.row.refundStatus == 1">未退款</span>
                            <span v-else-if="scope.row.refundStatus == 2">部分退款</span>
                            <span v-else-if="scope.row.refundStatus == 3">已退款</span>
                            <span v-else>未退款</span>
                        </template>
                </el-table-column>
            </el-table>
        </template>
    </el-card>


<el-card class="box-card" v-if="expenseAfterSalesDetail.expenseAfterSalesType == 4121">
    <div slot="header" class="clearfix">
        <span>交易信息</span>
        <el-button v-if="topOrgId == 8 && expenseAfterSalesDetail.expenseAfterSalesStatusDto.refundStatus!=3 " style="float: right; padding: 3px 0" type="text" @click="toCapitalBillEdit()">新增交易记录</el-button>
    </div>
    <template>
        <el-table
                :data="capitalBills"
                border
                style="width: 100%; "
                :cell-style="{'text-align':'center'}"
                :header-cell-style="{'text-align':'center'}">
            <el-table-column
                    prop="capitalBillNo"
                    label="记账编号"
                    min-width="25%">
            </el-table-column>
            <el-table-column
                    prop="bussinessType"
                    label="业务类型"
                    min-width="8%"
                    :formatter="getBussinessType">
            </el-table-column>
            <el-table-column
                    label="交易金额"
                    min-width="10%">
                    <template slot-scope="scope">
                        <span>{{scope.row.amount.toFixed(2)}}</span>
                    </template>
            </el-table-column>
            <el-table-column
                    prop="traderTime"
                    label="交易时间"
                    min-width="10%"
                    :formatter="dateFormat">
            </el-table-column>
            <el-table-column
                    prop="traderSubject"
                    label="交易主体"
                    min-width="15%"
                    :formatter="getTraderSubject">
            </el-table-column>
            <el-table-column
                    prop="traderMode"
                    label="交易方式"
                    min-width="8%"
                    :formatter="getTraderMode">
            </el-table-column>
            <el-table-column
                    prop="payer"
                    label="交易名称"
                    min-width="10%">
            </el-table-column>
            <el-table-column
                    prop="comments"
                    label="交易备注"
                    min-width="15%">
            </el-table-column>
            <el-table-column
                    prop="creatorName"
                    label="操作人"
                    min-width="7%">
            </el-table-column>
            <el-table-column
                    prop="addTime"
                    label="操作时间"
                    min-width="10%"
                    :formatter="dateFormat">
            </el-table-column>
            <el-table-column label="操作" width="150" fixed="right">
                <template slot-scope="scope">
                    <el-button
                            size="mini"
                            type="primary" plain
                            @click="openFile(scope.$index, scope.row)">回单
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
    </template>
</el-card>

</div>

<script src="${pageContext.request.contextPath}/static/api/expenseAftersale/expenseAfterSales.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/api/trader/traderSupplier.js?rnd=${resourceVersionKey}"></script>

<script type="text/javascript">
    const expenseAfterSalesId = '${expenseAfterSalesId}';
    const topOrgId = '${topOrgId}';

        new Vue({
            el: '#app',
            data() {
                return {
                    // 接口返回的dto多层嵌套，因此这边需要都初始化一下
                    expenseAfterSalesDetail: {
                        expenseAfterSalesStatusDto: {},
                        // 费用单信息
                        buyorderExpenseDto: {
                            buyorderExpenseDetailDto: {}
                        },
                        // 退货商品信息列表
                        expenseAfterSalesItemDtoList: [],

                        // 仅退票时 退票商品信息列表
                        returnInvoiceGoodsDtoList: [],

                        // 附件信息
                        attachmentList: []
                    },
                    totalNum: 0,

                    // 订单状态进度条
                    stepItems: [
                        {
                            id: 1,
                            type: "wait",
                            title: "待确认"
                        },
                        {
                            id: 2,
                            type: "wait",
                            title: "待审核"
                        },
                        {
                            id: 3,
                            type: "wait",
                            title: "待退款"
                        },
                        {
                            id: 5,
                            type: "wait",
                            title: "待退票"
                        },
                        {
                            id: 6,
                            type: "wait",
                            title: "已完结"
                        }
                    ],

                    verifySteps: [
                        {
                            id: 1,
                            type: "wait",
                            title: "开始审核"
                        },
                        {
                            id: 2,
                            type: "wait",
                            title: "申请人"
                        },
                        {
                            id: 3,
                            type: "wait",
                            title: "产品主管审核"
                        },
                        {
                            id: 5,
                            type: "wait",
                            title: "审核完结"
                        }
                    ],

                    // 判断当前用户是否是审核节点候选人
                    isCheckUser: false,
                    isOperator: false,

                bussinessTypes: [
                    {
                        dictValue: '525',
                        dictLabel: '订单付款'
                    },
                    {
                        dictValue: '526',
                        dictLabel: '订单收款'
                    },
                    {
                        dictValue: '531',
                        dictLabel: '退款'
                    },
                    {
                        dictValue: '532',
                        dictLabel: '资金转移'
                    },
                    {
                        dictValue: '533',
                        dictLabel: '信用还款'
                    },
                    {
                        dictValue: '679',
                        dictLabel: '对私提现'
                    }
                ],

                traderModes: [
                    {
                        dictValue: '520',
                        dictLabel: '支付宝'
                    },
                    {
                        dictValue: '521',
                        dictLabel: '银行'
                    },
                    {
                        dictValue: '522',
                        dictLabel: '微信'
                    },
                    {
                        dictValue: '523',
                        dictLabel: '现金'
                    },
                    {
                        dictValue: '527',
                        dictLabel: '信用支付'
                    },
                    {
                        dictValue: '528',
                        dictLabel: '余额支付'
                    },
                    {
                        dictValue: '529',
                        dictLabel: '退还信用'
                    },
                    {
                        dictValue: '530',
                        dictLabel: '退还余额'
                    },
                ],

                traderSubjects: [
                    {
                        dictValue: '1',
                        dictLabel: '对公'
                    },
                    {
                        dictValue: '2',
                        dictLabel: '对私'
                    }
                ],

                taskInfo: null,
                // 退票信息
                refundInvoiceList: [],
                // 发票列表
                invoiceList: [],

                // 退款信息
                refundInfoList: [],

                activeName: 'orderVerify',

                // 交易信息
                capitalBills: []
            }
        },

        mounted() {
            loadingApp();

            this.initData()
        },

        methods: {

                // 页面初始化数据加载
                initData() {
                    getDetail({"expenseAfterSalesId": expenseAfterSalesId}).then(res => {
                        this.expenseAfterSalesDetail = res.data.data;
                        if (this.expenseAfterSalesDetail.expenseAfterSalesType == 4121) {
                            this.expenseAfterSalesDetail.expenseAfterSalesItemDtoList.forEach(item => {
                                this.totalNum = this.totalNum + item.returnNum;
                            })
                        }

                    });

                    // 处理售后单状态进度条
                    getStepItemList({"expenseAfterSalesId": expenseAfterSalesId}).then(res => {
                        this.stepItems = res.data.data;
                    });

                    // 审核状态进度条
                    auditRecordData({"expenseAfterSalesId": expenseAfterSalesId}).then(res => {
                        this.taskInfo = res.data.data.taskInfo;
                        this.verifySteps = res.data.data.lastAudit;
                        this.isCheckUser = res.data.data.isCheckUser;
                        this.isOperator = res.data.data.isOperator;
                    });

                    // 退票信息模块
                    getRefundInvoiceList({"expenseAfterSalesId": expenseAfterSalesId}).then(res => {
                        this.refundInvoiceList = res.data.data;
                    });

                    // 发票记录模块
                    getInvoiceList({"expenseAfterSalesId": expenseAfterSalesId}).then(res => {
                        this.invoiceList = res.data.data;
                    });

                    // 退款信息模块
                    getRefundInfo({"expenseAfterSalesId": expenseAfterSalesId}).then(res => {
                        this.refundInfoList.push(res.data.data);
                    });

                    // 交易信息模块
                    getExpenseAfterSalesCapitalBill({"expenseAfterSalesId": expenseAfterSalesId}).then(res => {
                        this.capitalBills = res.data.data;
                    })

                },
                openFile(index, data){
                    debugger
                    let url = data.receiptUrl;
                    if(url == null || url == ""){
                        layer.alert("暂无回单");
                    }else{
                        window.open(url);
                    }
                },
                auditRecord(pass) {
                    layer.open({
                        type: 2,
                        shadeClose: false,
                        area: ["500px", "180px"],
                        title: "操作确认",
                        content: "/old/buyorderExpenseAfterSales/complement.do?type=3&taskId=" + this.taskInfo + "&pass=" + pass + "&expenseAfterSalesId=" + this.expenseAfterSalesDetail.expenseAfterSalesId,
                        success: function (layero, index) {
                        }
                    });
                },

                applyValid() {

                    let expenseAfterSalesId = this.expenseAfterSalesDetail.expenseAfterSalesId;
                    layer.confirm("是否确认提交审核？", {
                        btn: ['确定', '取消'] //按钮
                    }, function () {
                        $.ajax({
                            type: "POST",
                            url: "/old/buyorderExpenseAfterSales/editApplyAudit.do",
                            data: {"expenseAfterSalesId": expenseAfterSalesId, "taskId": 0},
                            dataType: 'json',
                            success: function (data) {
                                if (data.code == 0) {
                                    window.location.reload();
                                } else {
                                    layer.alert(data.message);
                                }
                            },
                            error: function (data) {
                                if (data.status == 1001) {
                                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
                                }
                            }
                        });

                        layer.alert(data.message);
                        layer.confirm(data.message, {btn: ['确认']}, function () {
                            window.location.reload();
                        });
                        return false;

                    })

                },


                // 关闭售后单
                closeAfterSale() {
                    this.$confirm('您是否确认该操作？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        closeExpenseAfterSales({"expenseAfterSalesId": expenseAfterSalesId, "buyorderExpenseId": this.expenseAfterSalesDetail.buyorderExpenseId}).then(res => {
                            openTab("费用售后详情", '/buyorderExpense/aftersale/detail.do?expenseAfterSalesId=' + expenseAfterSalesId);
                            this.closeThis()
                        })
                    }).catch(() => {
                    });
                },

            closeThis() {
                window.parent.closableTab.close($(window.parent.document).find('[role=presentation].active .glyphicon.small'), window.parent.closableTab.resizeMove);
            },

                // 编辑售后单
                editAfterSale() {
                    openTab("编辑售后", '/buyorderExpense/aftersale/edit.do?expenseAfterSalesId='
                        + expenseAfterSalesId + '&buyorderExpenseId=' + this.expenseAfterSalesDetail.buyorderExpenseId + '&traderId=' + this.expenseAfterSalesDetail.traderId);
                    this.closeThis()
                },

                // 费用单详情页跳转
                viewExpenseDetail(id) {
                    openTab("采购费用详情", '/buyorderExpense/details.do?buyorderExpenseId=' + id);
                },

                viewTraderSupplier(id) {
                    openTab("供应商信息", '/trader/supplier/baseinfo.do?traderId=' + id);
                },

                // 查看商品信息
                viewSkuInfo(id) {
                    openTab("商品信息", '/goods/goods/viewbaseinfo.do?goodsId=' + id);
                },
                viewSaleOrderInfo(id){
                    openTab("销售详情", '/orderstream/saleorder/detail.do?saleOrderId=' + id +'&scene=0')
                },


            toCapitalBillEdit() {
                layer.open({
                    title: '新增交易记录',
                    type: 2,
                    shade: 0.2,
                    maxmin: true,
                    shadeClose: true,
                    area: ['40%', '90%'],
                    content: '/buyorderExpense/aftersale/toCapitalBillEdit.do?expenseAfterSalesId=' + expenseAfterSalesId,
                    moveOut: true
                });
            },

                splitStrAddAllipsis(str, len) {
                    if (str != '' && str != null && str != undefined) {
                        if (str.length > len) {
                            return str.substring(0, len) + '...';
                        }
                    }

                    return str;
                },

                isSplitStrAddAllipsis(str, len) {
                    if (str != ''&&str!=null&&str!=undefined) {
                        if (str.length>len) {
                            return true;
                        }
                    }
                    return false;
                },

                call(phone) {
                    callout(
                        phone,
                        this.expenseAfterSalesDetail.traderId,
                        2,
                        8,
                        this.expenseAfterSalesDetail.expenseAfterSalesId,
                        this.expenseAfterSalesDetail.traderContactId);
                },

                // 申请冲销
                reversalInvoices(invoiceCode,invoiceNo) {
                    this.$confirm('您是否确认该操作？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        reversalInvoices({
                            "expenseAfterSalesId": expenseAfterSalesId,
                            "invoiceCode": invoiceCode,
                            "invoiceNo": invoiceNo
                        }).then(res => {
                            if (res.data.success) {
                                this.$message({
                                    message: "申请冲销成功",
                                    type: 'success',
                                });
                            } else {
                                this.$message({
                                    message: res.data.message,
                                    type: 'error',
                                });
                            }
                            window.location.reload();
                        })
                    }).catch(() => {
                    });
                },

                // 无需退票
            noNeedRefundInvoice(invoiceCode, invoiceNo) {
                this.$confirm('您是否确认该操作？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    noNeedRefundInvoice(
                        {
                            "expenseAfterSalesId": expenseAfterSalesId,
                            "invoiceCode": invoiceCode,
                            "invoiceNo": invoiceNo
                        }).then(res => {
                        window.location.reload();
                    })
                }).catch(() => {
                });
            },

                // 确认退票
                confirmRefundInvoice(invoiceCode, invoiceNo) {
                    openTab("确认退票", '/buyorderExpense/aftersale/returninvoice.do?expenseAfterSalesId=' + expenseAfterSalesId + '&invoiceCode=' + invoiceCode + '&invoiceNo=' + invoiceNo);
                },

                //格式化表格时间
                dateFormat(row,column){
                    let date = row[column.property];
                    return parseTime(date)
                },

                // 执行退款运算
            executeRefundOperation() {
                this.$confirm('您是否确认该操作？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    executeRefundOperation({
                        "expenseAfterSalesId": expenseAfterSalesId,
                        "buyorderExpenseId": this.expenseAfterSalesDetail.buyorderExpenseId,
                        "totalAmount": this.expenseAfterSalesDetail.totalAmount
                    }).then(res => {
                        openTab("费用售后详情", '/buyorderExpense/aftersale/detail.do?expenseAfterSalesId=' + expenseAfterSalesId);
                        this.closeThis()
                    })
                }).catch(() => {
                });
            },

            // 完结订单
            completeExpenseAfterSale() {
                this.$confirm('您是否确认该操作？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    completeExpenseAfterSale({"expenseAfterSalesId": expenseAfterSalesId}).then(res => {
                        if (!res.data.success) {
                            this.$message({
                                message: res.data.message,
                                type: 'error',
                            });
                        } else {
                            openTab("费用售后详情", '/buyorderExpense/aftersale/detail.do?expenseAfterSalesId=' + expenseAfterSalesId);
                            this.closeThis()
                        }
                    })
                }).catch(() => {
                });
            },

            getBussinessType(row, column) {
                let dictLabel;
                this.bussinessTypes.forEach(item => {
                    if (item.dictValue == row.capitalBillDetailDto.bussinessType) {
                        dictLabel = item.dictLabel;
                    }
                });
                return dictLabel
            },


            getTraderMode(row, column) {
                let dictLabel;
                this.traderModes.forEach(item => {
                    if (item.dictValue == row.traderMode) {
                        dictLabel = item.dictLabel;
                    }
                });
                return dictLabel
            },

            getTraderSubject(row, column) {
                let dictLabel;
                this.traderSubjects.forEach(item => {
                    if (item.dictValue == row.traderSubject) {
                        dictLabel = item.dictLabel;
                    }
                });
                return dictLabel
            },

            viewAttachment(domain, uri) {
                window.open("http://" + domain + uri);
            },

            //格式化表格时间
            dateFormat(row, column) {
                let date = row[column.property];
                return parseTime(date)
            },

            getExpenseAfterSalesDetailComments(expenseAfterSalesDetail){
                let orderDescStr = parseExpenseAfterSalesDetailDesc(expenseAfterSalesDetail)
                let expenseAfterSalesComments = expenseAfterSalesDetail.expenseAfterSalesComments
                return  orderDescStr + "</br>" +  expenseAfterSalesComments
            }
        }

    })
</script>

<style>
    .el-descriptions__title {
        margin-left: 15px;
    }

    .el-descriptions__header {
        margin-bottom: 9px;
        padding-top: 9px;
    }

    .el-descriptions {
        background-color: #d7d7d7;
        margin-top: 20px;
    }

    .el-descriptions-item__cell.el-descriptions-item__content {
        text-align: center !important;
    }

    .el-descriptions-item__cell.el-descriptions-item__label.is-bordered-label {
        text-align: center;
        width: 10%;
    }

    .my-content {
        width: 40%;
    }

    .el-card__header {
        height: 34px;
        background-color: #d7d7d7;
        box-sizing: border-box;
        padding-left: 15px;
        font-weight: 700;
        padding-top: 9px;
        padding-bottom: 30px;
        color: #303133;
    }

    .my-sum {
        background-color: #e8ffff;
        height: 30px;
        padding-left: 15px;
        padding-top: 7px;
        font-size: 14px;
    }

    .el-card {
        margin-bottom: 20px;
    }

    .el-card__body {
        padding: 0;
    }

    .el-step__head.is-success {
        color: #409EFF;
        border-color: #409EFF;
    }

    .el-step__title.is-success {
        color: #409EFF;
    }

    .el-step__description.is-success {
        color: #409EFF;
    }

    .my-title-style {
        height: 20px;
        padding-left: 15px;
        padding-top: 7px;
        background-color: #fffbe6;
        padding-bottom: 7px;
    }
    .order-desc-style{
        color: #66b1ff;
    }

</style>
