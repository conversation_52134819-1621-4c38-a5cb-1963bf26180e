package com.wms.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.activiti.service.impl.ActivitiAssigneeServiceImpl;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.LendOutProcessConstants;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.BaseSonContrller;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.logistics.model.GoodsAcceptanceReport;
import com.vedeng.goods.service.VgoodsService;
import com.vedeng.logistics.eums.WarehouseGoodsOutEnum;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.logistics.model.WmsOutputOrderGoodsExtra;
import com.vedeng.logistics.model.vo.GoodsAcceptanceReportDto;
import com.vedeng.logistics.model.vo.WarehouseGoodsInLogVo;
import com.vedeng.logistics.model.vo.WarehouseGoodsOutLogVo;
import com.vedeng.logistics.service.WarehouseGoodsOutDetailService;
import com.vedeng.logistics.service.WarehouseInService;
import com.vedeng.logistics.service.WarehouseOutService;
import com.vedeng.logistics.service.impl.BaseWarehouseGoodsOutDetailService;
import com.vedeng.logistics.strategy.WarehouseGoodsOutDetailStrategyFactory;
import com.vedeng.order.api.base.VedengErrorCode;
import com.vedeng.order.model.dto.ProductManageAndAsistDto;
import com.vedeng.order.model.vo.BuyorderVo;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.service.AttachmentService;
import com.vedeng.system.service.UserService;
import com.vedeng.system.service.VerifiesRecordService;
import com.wms.constant.VerifyStatusEnum;
import com.wms.model.dto.AddLendOutDto;
import com.wms.model.dto.WMSLendOutQueryDto;
import com.wms.model.po.WmsLendOutOrder;
import com.wms.model.po.WmsOutputOrder;
import com.wms.model.po.WmsOutputOrderGoods;
import com.wms.service.WMSLendOutGoodsService;
import com.wms.service.WMSLendOutService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.TaskService;
import org.activiti.engine.task.IdentityLink;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * WMS商品外借列表
 */
@Controller
@RequestMapping("/wms/commodityLendOut")
public class WMSLendOutController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(WMSLendOutController.class);

    @Autowired
    private WMSLendOutService lendOutService;

    @Autowired
    private WMSLendOutGoodsService lendOutGoodsService;

    @Autowired
    private UserService userService;

    @Autowired
    @Qualifier("actionProcdefService")
    protected ActionProcdefService actionProcdefService;

    @Autowired
    private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

    @Autowired
    private WarehouseInService warehouseInService;

    @Autowired
    private VgoodsService vGoodsService;

    @Autowired
    @Qualifier("verifiesRecordService")
    private VerifiesRecordService verifiesRecordService;

    @Autowired
    private WarehouseGoodsOutDetailStrategyFactory warehouseGoodsOutDetailStrategyFactory;

    @Resource
    private AttachmentService attachmentService;

    @Autowired
    @Qualifier("activitiAssigneeService")
    private ActivitiAssigneeServiceImpl activitiAssigneeService;




    /**
     * 保存验收报告
     * @param request
     * @param report
     * @param nameList
     * @param uriList
     * @return
     */
    @RequestMapping(value = "/reportReturnSave")
    @ResponseBody
    public ResultInfo<?> reportReturnSave(HttpServletRequest request,GoodsAcceptanceReport report,@RequestParam("name_8") List<String> nameList,@RequestParam("uri_8") List<String> uriList ) {

        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        if(Objects.isNull(report)){
            return ResultInfo.error();
        }
        //1 新增或编辑
        if(Objects.isNull(report.getGoodsAcceptanceReportId())){
            //新增验收报告单 和 附件
            lendOutService.addReport(report,nameList,uriList,user);
        }else {
            //更新验收报告单 和 附件
            lendOutService.updateReport(report,nameList,uriList,user);
        }
        return ResultInfo.success();
    }

    /**
     * 去新增验收报告页面
     * @param report
     * @return
     */
    @RequestMapping("/toAddGoodsAcceptanceReport")
    public ModelAndView toAddGoodsAcceptanceReport(GoodsAcceptanceReport report,@RequestParam("lendOutId") Long lendOutId){
        ModelAndView mv = new ModelAndView();
        if(Objects.isNull(lendOutId) || Objects.isNull(report)){
            return fail(mv);
        }
        if(Objects.nonNull(report.getGoodsAcceptanceReportId())){
            report = lendOutService.getGooodsAcceptanceReportById(report.getGoodsAcceptanceReportId());
            List<Attachment> attachmentList = lendOutService.getReportAttachmentList(report.getGoodsAcceptanceReportId());
            mv.addObject("zb",attachmentList);
        }
        mv.addObject("report",report);
        List<WmsOutputOrderGoods> lendOutGoodList = this.lendOutGoodsService.queryOutputGoodsByLendOutId(lendOutId);
        mv.addObject("lendOutGoodList",lendOutGoodList);
        mv.setViewName("wms/lendOut/report_return");
        return mv;
    }

    @RequestMapping(value = "/notice")
    @ResponseBody
    public ResultInfo<?> notice(HttpServletRequest request,@RequestParam List<Integer> userId,@RequestParam("lendOutId") Long lendOutId){
       logger.info("userIdList:{},lendOutId:{}", JSONObject.toJSONString(userId),lendOutId);
        if(Objects.isNull(lendOutId) || CollectionUtils.isEmpty(userId)){
            return ResultInfo.error(VedengErrorCode.PARAMS_VERIFY_ERROR);
        }
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        WmsOutputOrder outputOrder = this.lendOutService.findLendOutById(lendOutId);
        //发送站内信
        Map<String, String> map = new HashMap<>();
        if(Objects.nonNull(outputOrder)){
            map.put("orderNo",outputOrder.getOrderNo());
        }else {
            map.put("orderNo","");
        }
        try {
            String url = "./wms/commodityLendOut/detail.do?lendOutId=" + lendOutId;
            MessageUtil.sendMessage(244, userId, map, url,user.getUsername());
            return ResultInfo.success();
        }catch (Exception e){
            logger.error("外借通知售后总监失败，e",e);
            return ResultInfo.error(VedengErrorCode.FAIL_CODE);
        }
    }

    @RequestMapping("/toChoose")
    public ModelAndView toChoose(HttpServletRequest request,@Param("lendOutId") Long lendOutId){
        ModelAndView mv = new ModelAndView();
        // 查询售后总监集合
        List<User> userList = userService.getAfterSalesDirectorList();
        mv.addObject("lendOutId",lendOutId);
        mv.addObject("userList",userList);
        mv.setViewName("wms/lendOut/lendOutNotice");
        return mv;
    }

    @ResponseBody
    @RequestMapping(value="/index")
    public ModelAndView index(HttpServletRequest request, WMSLendOutQueryDto lendOutQueryDto,
                              @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                              @RequestParam(required = false) Integer pageSize,
                              HttpSession session){

        ModelAndView mv = new ModelAndView();
        User user =(User)session.getAttribute(ErpConst.CURR_USER);

        //查询集合
        Page page = getPageTag(request,pageNo,pageSize);
        List<WmsLendOutOrder> lendOutList = lendOutService.querylistPage(lendOutQueryDto, page);

        mv.addObject("lendOutList",lendOutList);
        mv.addObject("lendOutQueryDto", lendOutQueryDto);
        mv.addObject("page", page);
        mv.setViewName("wms/lendOut/lendOutIndex");
        return mv;
    }


    @ResponseBody
    @RequestMapping(value = "/toAddLendOut")
    public ModelAndView toAddLendOut(HttpServletRequest request){
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);

        ModelAndView modelAndView = new ModelAndView();

        modelAndView.addObject("today",DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd"));
        modelAndView.setViewName("wms/lendOut/lendOutAdd");
        return modelAndView;
    }

    @ResponseBody
    @RequestMapping(value = "/canSubmitLendOut")
    public ResultInfo canSubmitLendOut(HttpServletRequest request){
        try{
            logger.info("是否可提交外借订单");
            User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
            logger.info("外借订单发起user:{}",user.getUsername());
            //如果无一级部门长，则报错提示
            String leaderName = activitiAssigneeService.getTopDepartmentLeader(user.getUsername(),1);
            logger.info("一级部门长name：{}",leaderName);
            if(StringUtils.isBlank(leaderName)){
                return new ResultInfo(-1, "请先设置一级部门长，再重新创建外借单");
            }

            //若无直接上级，则报错提示“请先设置直接上级，再重新创建外借单”
            User parentUser = userService.getUserParentInfo(user.getUsername(),user.getCompanyId());
            if(parentUser == null || StringUtil.isEmpty(parentUser.getpUsername())){
                return new ResultInfo(-1, "请先设置直接上级，再重新创建外借单");
            }

            return new ResultInfo(0, "成功");
        }catch (Exception e){
            logger.error("WMSLendOutController.canSubmitLendOut:",e);
            return new ResultInfo(-1, "服务器内部错误");
        }
    }

    /**
     * 判断当前用户是否有售后总监或者销售总监角色
     * @param user
     * @return
     */
    private boolean currentUserHasChiefRole(User user) {
        String username = user.getUsername();
        String leaderName = activitiAssigneeService.getTopDepartmentLeader(username,1);
        AtomicBoolean flag = new AtomicBoolean(false);
        if (username.equals(leaderName)){
            flag.set(true);
        }
        return flag.get();
    }

    /**
     * 新增外借出库单
     * @param addLendOutDto
     */
    @RequestMapping(value = "/addLendOut")
    public ModelAndView addLendOut(HttpServletRequest request, AddLendOutDto addLendOutDto){

        ModelAndView mv = new ModelAndView();

        try{

            User user = (User)request.getSession().getAttribute(ErpConst.CURR_USER);

            Long lendOutOrderId = lendOutService.addLendOutOrder(addLendOutDto,user);

            startProcessInstance(request,lendOutOrderId);


            mv.setViewName("redirect:/wms/commodityLendOut/detail.do?lendOutId=" + lendOutOrderId);
            return mv;
//            return success(mv);

        }catch(Exception e){

            LOGGER.error("新增外借出库单失败====================:",e);

            mv.addObject("message",e.getMessage());
            return fail(mv);
        }
    }

    /**
     *
     * @param lendOutOrderId
     */
    private void startProcessInstance(HttpServletRequest request,Long lendOutOrderId) throws Exception{

        //初始化工作流变量
        Map<String, Object> variableMap = new HashMap<String, Object>();

        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);

        variableMap.put("processDefinitionKey", "lendOutAudit");
        variableMap.put("businessKey", "lendOutAudit_" + lendOutOrderId);
        variableMap.put("currentAssinee", user.getUsername());
        variableMap.put("lendOutOrderId", lendOutOrderId);
        variableMap.put("num",1);
        WmsOutputOrder lendOut = this.lendOutService.findLendOutById(lendOutOrderId);
        variableMap.put("lendOutOrderNo", lendOut.getOrderNo());
        Boolean isChiefRole=currentUserHasChiefRole(user);
        variableMap.put("isLeader",isChiefRole?"true":"false");

        actionProcdefService.createProcessInstance(request, variableMap.get("processDefinitionKey").toString(), variableMap.get("businessKey").toString(), variableMap);

        // 获取当前活动节点
        Task taskInfo = processEngine.getTaskService()
                .createTaskQuery()
                .processInstanceBusinessKey(variableMap.get("businessKey").toString())
                .singleResult();

        //完成申请的任务
        //修复请求WMS接口异常|请求失败
        ResultInfo<?> resultInfo = actionProcdefService.complementTask(request, taskInfo.getId(), "", user.getUsername(), variableMap);
        //如果为一级部门长发起流程，直接跳转至产品经理或助理审核
        if(isChiefRole){
            //获取产品经理及助理列表
            WmsOutputOrderGoodsExtra extra=new WmsOutputOrderGoodsExtra();
            extra.setWmsOutputOrderId(lendOutOrderId);
            List<WmsOutputOrderGoodsExtra> auditList=lendOutService.findGoodsAuditList(extra);
            Set<String> uAuditorSet = getProductBelongNameList(auditList);
            TaskService taskService = processEngine.getTaskService();
            // 获取当前活动节点
            taskInfo = taskService
                    .createTaskQuery()
                    .processInstanceBusinessKey(variableMap.get("businessKey").toString())
                    .singleResult();
            String taskId=taskInfo.getId();
            String businessKey = (String) taskService.getVariable(taskId, "businessKey");
            setTaskCandidateUser(taskService, businessKey, uAuditorSet);
            verifiesRecordService.saveVerifiesInfo(taskId, 0);
            //发送消息提醒
            sendLendOutInfo(getProductManageAndAsistIdList(auditList), taskService.getVariables(taskInfo.getId()));
        }
        if (null!=resultInfo && ResultInfo.error().getCode().equals(resultInfo.getCode())){
            logger.info("申请的任务完成失败taskId :{} ", taskInfo.getId());
            throw new Exception();
        }

    }

    /**
     * 获取产品经理及助理列表
     * @param auditList
     * @return
     */
    private Set<String> getProductBelongNameList(List<WmsOutputOrderGoodsExtra> auditList) {
        Set<String> uAuditorSet = new HashSet<>();
        for (WmsOutputOrderGoodsExtra wmsOutputOrderGoodsExtra : auditList) {
            String productBelongNameInfo = wmsOutputOrderGoodsExtra.getProductBelongNameInfo();
            if (StringUtils.isNotBlank(productBelongNameInfo)){
                uAuditorSet.addAll(Arrays.asList(productBelongNameInfo.split(",")));
            }
        }
        return uAuditorSet;
    }
    /**
     * 获取产品经理及助理userId列表
     * @param auditList
     * @return
     */
    private List<Integer> getProductManageAndAsistIdList(List<WmsOutputOrderGoodsExtra> auditList) {
        Set<String> uAuditorSet = new HashSet<>();
        for (WmsOutputOrderGoodsExtra wmsOutputOrderGoodsExtra : auditList) {
            String productBelongIdInfo = wmsOutputOrderGoodsExtra.getProductBelongIdInfo();
            if (StringUtils.isNotBlank(productBelongIdInfo)){
                uAuditorSet.addAll(Arrays.asList(productBelongIdInfo.split(",")));
            }
        }
        List<Integer> list=new ArrayList<>();
        if (CollectionUtils.isNotEmpty(uAuditorSet)){
            list=uAuditorSet.stream().map(Integer::parseInt).collect(Collectors.toList());
        }
        return list;
    }
    /**
     * 详情页
     */
    @RequestMapping(value = "/detail")
    public ModelAndView detail(HttpServletRequest request,@Param("lendOutId") Long lendOutId) {

        WmsOutputOrder outputOrder = this.lendOutService.findLendOutById(lendOutId);

        ModelAndView mv = new ModelAndView();
        mv.addObject("lendOutId",lendOutId);
        mv.setViewName("wms/lendOut/lendOutDetail");
        mv.addObject("outputOrder",outputOrder);

        WarehouseGoodsOutDetailService lendWarehouseGoodsOutDetailService = warehouseGoodsOutDetailStrategyFactory.getWarehouseGoodsOutDetailService(WarehouseGoodsOutEnum.LEND_WAREHOUSE_OUT.getService());


        List<WarehouseGoodsOutLogVo> lendWarehouseGoodsOutLogList = new ArrayList<>();
        List<WarehouseGoodsInLogVo> lendWarehouseGoodsInLogList = new ArrayList<>();
        if (outputOrder != null && outputOrder.getOrderNo() != null){
            lendWarehouseGoodsOutLogList = lendWarehouseGoodsOutDetailService.relatedNoWarehouseGoodsOutLogList(outputOrder.getOrderNo());
            lendWarehouseGoodsInLogList = warehouseInService.relatedNoWarehouseGoodsInLogList(outputOrder.getOrderNo());
        }

        List<WmsOutputOrderGoods> lendOutGoodList = this.lendOutGoodsService.queryOutputGoodsByLendOutId(lendOutId);
        mv.addObject("lendOutGoodList",lendOutGoodList);

        mv.addObject("warehouseOutList",lendWarehouseGoodsOutLogList);
        mv.addObject("warehouseInList",lendWarehouseGoodsInLogList);

        List<Integer> skuIds = new ArrayList<>();
        for (WarehouseGoodsOutLogVo lendWarehouseGoodsOutLog : lendWarehouseGoodsOutLogList) {
            skuIds.add(lendWarehouseGoodsOutLog.getGoodsId());
        }

        for (WarehouseGoodsInLogVo lendWarehouseGoodsInLog : lendWarehouseGoodsInLogList) {
            skuIds.add(lendWarehouseGoodsInLog.getGoodsId());
        }

        if (!CollectionUtils.isEmpty(skuIds)) {
            List<Map<String, Object>> skuTipsMap = this.vGoodsService.skuTipList(skuIds);
            Map<String, Map<String, Object>> newSkuInfosMap = skuTipsMap.stream().collect(Collectors.toMap(key -> key.get("SKU_ID").toString(), v -> v, (k, v) -> v));
            mv.addObject("newSkuInfosMap", newSkuInfosMap);
        }

        String key = lendOutId +"";
        if(lendOutId > 10000 && lendOutId < 10151){
            key = (lendOutId - 10000)+"";
        }
        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, "lendOutAudit_"+key);
        mv.addObject("historicInfo", historicInfo);
        mv.addObject("historicActivityInstance", historicInfo.get("historicActivityInstance"));
        mv.addObject("commentMap", historicInfo.get("commentMap"));

        if (historicInfo.get("taskInfo") != null) {

            Task taskInfo = (Task) historicInfo.get("taskInfo");

            mv.addObject("taskId",taskInfo.getId());
            mv.addObject("taskInfo", taskInfo);

            //获取审核人候选组
            Map<String, Object> candidateUserMap = (Map<String, Object>) historicInfo.get("candidateUserMap");

            //获取审核人候选组
            List<IdentityLink> candidateUserList = (List<IdentityLink>) candidateUserMap.get(taskInfo.getId());

            if(CollectionUtils.isNotEmpty(candidateUserList)){

                List<String> userNameList = candidateUserList.stream().map(identityLink -> identityLink.getUserId()).collect(Collectors.toList());

                mv.addObject("verifyUsers", StringUtils.join(userNameList,","));
            }
        }
        List<GoodsAcceptanceReportDto> acceptanceReportList = lendOutService.getAcceptanceReportList(outputOrder.getOrderNo());
        mv.addObject("acceptanceReportList",acceptanceReportList);
        return mv;
    }

    /**
     * 审核结果页面
     * @param taskId
     * @param pass
     * @param lendOutId
     * @return
     */
    @RequestMapping(value = "/auditResult")
    public ModelAndView auditResult(String taskId, Boolean pass,Long lendOutId) {
        ModelAndView mv = new ModelAndView();
        mv.addObject("taskId", taskId);
        mv.addObject("pass", pass);
        mv.addObject("lendOutId", lendOutId);
        mv.setViewName("wms/lendOut/auditResult");
        return mv;
    }

    /**
     * 订单关闭
     * @return
     */
    @RequestMapping(value = "/closeOrder",method = RequestMethod.POST)
    @ResponseBody
    public ResultInfo closeOrder(Long lendOutId) {
        try {

            WmsOutputOrder wmsOutputOrder = this.lendOutService.findLendOutById(lendOutId);

            if(wmsOutputOrder.getVerifyStatus() == VerifyStatusEnum.Approved.getValue()){
                return new ResultInfo(-1, "外借单已经审核通过，不允许关闭");
            }

            this.lendOutService.updateLendOutAuditStatus(lendOutId,VerifyStatusEnum.COLSED.getValue());

            //如果是审核中,就删除现在的流程实例
            if(VerifyStatusEnum.Reviewing.getValue() == wmsOutputOrder.getVerifyStatus()){

                // 获取当前活动节点
                Task taskInfo = processEngine.getTaskService()
                        .createTaskQuery()
                        .processInstanceBusinessKey("lendOutAudit_"+lendOutId)
                        .singleResult();

                if(taskInfo != null){
                    //删除流程实例
                    actionProcdefService.deleteProcessInstance(taskInfo.getId());
                }
            }

            return new ResultInfo(0, "操作成功");

        } catch (Exception e) {
            logger.error("closeOrder:", e);
            return new ResultInfo(-1, "操作失败");
        }
    }

    /**
     * 外借出库单审核操作
     */
    @ResponseBody
    @RequestMapping(value = "/complementTask")
    public ResultInfo<?> complementTask(HttpServletRequest request,Long lendOutId,String taskId, String comment, Boolean pass,
                                        HttpSession session) {

        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);

        try {

            //完成当前任务，工作流往下走
            Map<String, Object> taskVaribles = new HashMap<String, Object>();
            taskVaribles.put("pass", pass);
            TaskService taskService = processEngine.getTaskService();
            // 使用任务id,获取任务对象，获取流程实例id
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            String businessKey = (String) taskService.getVariable(taskId, "businessKey");
            WmsOutputOrderGoodsExtra extra=new WmsOutputOrderGoodsExtra();
            extra.setWmsOutputOrderId(lendOutId);
            List<WmsOutputOrderGoodsExtra> auditList=lendOutService.findGoodsAuditList(extra);
            if(Boolean.TRUE.equals(pass)&&LendOutProcessConstants.TOP_DEPARTMENT_MANAGE.equals(task.getName())&&CollectionUtils.isNotEmpty(auditList)){
                //完成当前任务
                actionProcdefService.complementTask(request, taskId, comment, user.getUsername(), taskVaribles);
                Set<String> auditorSet = getProductBelongNameList(auditList);
                setTaskCandidateUser(taskService, businessKey, auditorSet);
                verifiesRecordService.saveVerifiesInfo(taskId, 0);
                // 获取当前活动节点
                Task taskInfo = taskService
                        .createTaskQuery()
                        .processInstanceBusinessKey(businessKey)
                        .singleResult();
                //发送消息提醒
                sendLendOutInfo(getProductManageAndAsistIdList(auditList), taskService.getVariables(taskInfo.getId()));
                return new ResultInfo(0, "操作成功");
            }
            boolean allPass = false;
            if (LendOutProcessConstants.PRODUCT_MANAGER_AUDIT.equals(task.getName()) && pass) {
                List<WmsOutputOrderGoodsExtra> collect = auditList.stream()
                        .filter(buyOrderGood -> {
                            return buyOrderGood.getProductAudit() == 0;
                        }).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    Set<String> set = getProductBelongNameList(auditList);
                    if (!set.contains(user.getUsername())) {
                        //判断当前人 对应的产品是否已经审核了 如果已经审核了 就直接返回
                        return new ResultInfo(-1, "当前产品已经有对应的产品经理和产品助理审核，请刷新页面重试");
                    }
                }
                log.info("出入库商品额外信息表:{}", JSON.toJSONString(auditList));
                //修改商品审核状态
                for (WmsOutputOrderGoodsExtra wmsOutputOrderGoodsExtra : auditList) {

                    ProductManageAndAsistDto productManageAndAsistDto = convertToProductManageAndAsistDto(wmsOutputOrderGoodsExtra);
                    if (currentUserIsProduct(user, productManageAndAsistDto)) {
                        wmsOutputOrderGoodsExtra.setProductAudit(1);
                        log.info("修改产品经理负责商品审核状态:{}",lendOutId);
                        lendOutService.updateWmsOutputOrderGoodsExtra(wmsOutputOrderGoodsExtra);
                    }

                }
                extra.setProductAudit(0);
                List<WmsOutputOrderGoodsExtra> unAuditSkuList=lendOutService.findGoodsAuditList(extra);
                //还有未审核的产品
                if (CollectionUtils.isNotEmpty(unAuditSkuList)) {
                    log.info("还有未审核的产品:{}", JSON.toJSONString(unAuditSkuList));
                    taskService.setVariable(taskId, "allPass", false);

                    //完成当前任务
                    actionProcdefService.complementTask(request, taskId, comment, user.getUsername(), taskVaribles);

                    Set<String> uAuditorSet = getProductBelongNameList(unAuditSkuList);

                    //设置下个任务的候选人
                    setTaskCandidateUser(taskService, businessKey, uAuditorSet);

                    verifiesRecordService.saveVerifiesInfo(taskId, 0);

                    return new ResultInfo(0, "操作成功");
                }
                allPass = true;
                taskService.setVariable(taskId, "allPass", true);

            }
            log.info("allPass状态:{}",taskService.getVariable(taskId,"allPass"));
            ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, comment,
                    user.getUsername(), taskVaribles);
            if(allPass && pass){
                //发起自动审核通过
                log.info("样品审核单，发起自动审核通过");
                Task taskInfoNotice = processEngine.getTaskService()
                        .createTaskQuery()
                        .processInstanceBusinessKey(businessKey)
                        .singleResult();

                Set<String> weixinUserNameList = new HashSet<>();
                List<String> noticeUserList = new ArrayList<>();
                List<String> jinUserList = activitiAssigneeService.getUserListByPosition("进存销主管");
                noticeUserList.add("进存销主管:"+String.join(",",jinUserList));
                weixinUserNameList.addAll(jinUserList);

                List<String> chanpingUserList = activitiAssigneeService.getUserListByPosition("产品总监");
                noticeUserList.add("产品总监:"+String.join(",",chanpingUserList));
                weixinUserNameList.addAll(chanpingUserList);

                List<String> caiwuUserList = activitiAssigneeService.getUserListByPosition("财务总监");
                noticeUserList.add("财务总监:"+String.join(",",caiwuUserList));
                weixinUserNameList.addAll(caiwuUserList);

                actionProcdefService.complementTask(request, taskInfoNotice.getId(), String.join("<br/>", noticeUserList), "njadmin", new HashMap<>());
                lendOutService.sendWeixinNotice(lendOutId,weixinUserNameList);

            }

            return new ResultInfo(0, "操作成功");

        } catch (Exception e) {
            logger.error("complementTask:", e);
            return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
        }

    }

    private ProductManageAndAsistDto convertToProductManageAndAsistDto(WmsOutputOrderGoodsExtra wmsOutputOrderGoodsExtra) {
        ProductManageAndAsistDto productManageAndAsistDto = new ProductManageAndAsistDto();
        productManageAndAsistDto.setProductAssitName(wmsOutputOrderGoodsExtra.getProductBelongNameInfo().split(",")[0]);
        productManageAndAsistDto.setProductManageName(wmsOutputOrderGoodsExtra.getProductBelongNameInfo().split(",")[1]);
        String[] productBelongId = wmsOutputOrderGoodsExtra.getProductBelongIdInfo().split(",");
        if (org.apache.commons.lang3.StringUtils.isNumeric(productBelongId[0])) {
            productManageAndAsistDto.setProductAssitUserId(Integer.valueOf(productBelongId[0]));
        }
        if (org.apache.commons.lang3.StringUtils.isNumeric(productBelongId[1])) {
            productManageAndAsistDto.setProductManageUserId(Integer.valueOf(productBelongId[1]));
        }
        return productManageAndAsistDto;
    }

    /**
     * 当前用户是否是产品助理后者产品经理
     *
     * @param user
     * @param productManageAndAsistDto
     * @return
     */
    private boolean currentUserIsProduct(User user, ProductManageAndAsistDto productManageAndAsistDto) {

        if (user.getUserId().equals(productManageAndAsistDto.getProductManageUserId()) ||
                user.getUserId().equals(productManageAndAsistDto.getProductAssitUserId())) {
            return true;
        }

        return false;
    }

    /**
     * 设置任务候选人
     *
     * @param taskService
     * @param businessKey
     * @param manageAndAsistNameSet
     */
    private void setTaskCandidateUser(TaskService taskService, String businessKey, Set<String> manageAndAsistNameSet) {

        Task nextTask = taskService.createTaskQuery().processInstanceBusinessKey(businessKey).singleResult();

        for (String manageAndAsistName : manageAndAsistNameSet) {
            processEngine.getTaskService().addCandidateUser(nextTask.getId() + "", manageAndAsistName);
        }
        ;

    }

    /**
     * 发送站内信
     * @param userIdList
     * @param variables
     */
    private void sendLendOutInfo(List<Integer> userIdList, Map<String, Object> variables) {

        Map<String, String> variableMap = new HashedMap();
        variableMap.put("lendOutOrderNo", variables.get("lendOutOrderNo").toString());

        String url = "./wms/commodityLendOut/detail.do?lendOutId=" + variables.get("lendOutOrderId").toString();
        String preAssignee = variables.get("currentAssinee").toString();

        MessageUtil.sendMessage(120, userIdList, variableMap, url, preAssignee);

    }

}
