package com.vedeng.trader.model.vo;

import com.vedeng.order.model.vo.BuyorderVo;
import com.vedeng.system.model.Attachment;
import com.vedeng.trader.model.CommunicateRecord;
import com.vedeng.trader.model.TraderOrderGoods;
import com.vedeng.trader.model.TraderSupplier;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;


/**
 * 供应商vo
 *
 * <AUTHOR>
 * <b>PackageName:</b> com.vedeng.trader.model.vo <br>
 * <b>ClassName:</b> TraderSupplierVo <br>
 * <b>Date:</b> 2017年5月12日 上午10:13:16
 */
@Getter
@Setter
public class TraderSupplierVo extends TraderSupplier {

    private static final long serialVersionUID = 1L;

    /**
     * 供应商名称
     */
    private String traderSupplierName;

    /**
     * 供应商地址
     */
    private String traderSupplierAddress;

    /**
     * 未合作过的供应商
     */
    private List<Integer> noCooperateTraderIds;

    private List<Integer> searchTraderIds;

    /**
     * 交易时间的交易者id
     */
    private List<Integer> traderTimeList;

    /**
     * 账期使用金额
     */
    private BigDecimal usedPeriodAmount;

    /**
     * 账期逾期金额
     */
    private BigDecimal overPeriodAmount;

    /**
     * 资金流水主键
     */
    private Integer capitalBillId;

    /**
     * 交易方式
     */
    private Integer traderMode;

    /**
     * 订单账期额度
     */
    private BigDecimal orderPeriodAmount;

    /**
     * 订单支付状态
     */
    private Integer orderPaymentStatus;

    /**
     * 订单ID
     */
    private Integer buyorderId;

    /**
     * 供应商账期余额
     */
    private BigDecimal periodBalance;

    /**
     * 供应商审核状态：状态0请求审核1审核通过2审核不通过
     */
    private Integer traderSupplierStatus;

    /**
     * 采购次数
     */
    private Integer buyCount;
    /**
     * 采购金额
     */
    private BigDecimal buyMoney;
    /**
     * 开始时间，用于页面搜索
     */
    private Long startTime;
    /**
     * 结束时间，用于页面搜索
     */
    private Long endTime;
    /**
     * 联系方式
     */
    private String contactWay;

    /**
     * 所属区域id
     */
    private Integer areaId;

    /**
     * 归属采购
     */
    private String personal;
    /**
     * 合作
     */
    private String cooperate;

    /**
     * 页面搜索时间：1、创建时间；2、交易时间3、更新时间
     */
    private Integer timeType;

    /**
     * 交易者id
     */
    private List<Integer> traderList;

    /**
     * 均单价
     */
    private BigDecimal averagePrice;
    /**
     * 交易频次
     */
    private BigDecimal buyFrequency;
    /**
     * 沟通次数
     */
    private Integer communcateCount;
    /**
     * 上次沟通时间
     */
    private Long lastCommuncateTime;
    /**
     * 采购订单ID
     */
    private List<Integer> buyorderIds;
    /**
     * 售后订单ID
     */
    private List<Integer> serviceOrderIds;
    /**
     * 上次交易时间
     */
    private Long lastBussinessTime;
    /**
     * 第一次交易时间
     */
    private Long firstBussinessTime;

    /**
     * 客户信息
     */
    private TraderCustomerVo traderCustomerVo;

    /**
     * 电话
     */
    private String phone;

    /**
     * ERP公司ID
     */
    private Integer companyId;

    /**
     * 上次沟通类型
     */
    private Integer lastCommunicateType;

    /**
     * 上次沟通主表ID
     */
    private Integer lastRelatedId;

    /**
     * 联系人名称
     */
    private String contactName;

    /**
     * 采购订单
     */
    private List<BuyorderVo> buyorderList;

    /**
     * 联系人ID
     */
    private Integer traderContactId;

    /**
     * 供应商等级
     */
    private String gradeStr;

    /**
     * 沟通记录
     */
    private List<CommunicateRecord> communicateRecordList;

    /**
     * 剩余账期
     */
    private BigDecimal accountPeriodLeft;

    /**
     * 账期使用次数
     */
    private Integer usedTimes;

    /**
     * 账期逾期次数
     */
    private Integer overdueTimes;

    private List<TraderContactVo> traderContactVoList;

    private List<TraderAddressVo> traderAddressVoList;

    /**
     * 归属销售--页面搜索条件
     */
    private Integer homePurchasing;
    /**
     * 省份id
     */
    private Integer province;
    /**
     * 市id
     */
    private Integer city;
    /**
     * 区县id
     */
    private Integer zone;
    /**
     * 查询开始时间
     */
    private String queryStartTime;
    /**
     * 查询结束时间
     */
    private String queryEndTime;
    /**
     * 列表也是否能查看0-否；1-是
     */
    private Integer isView;

    /**
     * 搜索信息
     */
    private String searchMsg;

    /**
     * 省
     */
    private String provinceName;

    /**
     * 市
     */
    private String cityName;

    /**
     * 区
     */
    private String zoneName;

    /**
     * 添加时间格式化
     */
    private String addTimeStr;

    /**
     * 沟通供应商ID
     */
    private List<Integer> communicateTraderIds;

    /**
     * 合作过的供应商
     */
    private List<Integer> cooperateTraderIds;

    /**
     * 联系方式客户集合id集合
     */
    private List<Integer> contactTraderIds;

    /**
     * 最后审核人
     */
    private String lastVerifyUsername;

    /**
     * 当前审核人
     */
    private String verifyUsername;

    /**
     * 审核状态
     */
    private Integer verifyStatus;

    /**
     * 当前审核人
     */
    private List<String> verifyUsernameList;

    /**
     * 交易次数
     */
    private Integer orderCount;

    List<TraderOrderGoods> traderOrderGoodsList;

    /**
     * 当前所有采购人员
     */
    private List<Integer> userIdList;

    /**
     * 采购搜索供应商
     */
    private String requestType;

    /**
     * 企业宣传片地址列表
     */
    private List<Attachment> companyUriList;

    /**
     * 订单覆盖品类
     */
    private List<String> categoryNameList;

    /**
     * 订单覆盖品牌
     */
    private List<String> brandNameList;

    /**
     * 订单覆盖产品
     */
    private List<String> goodNameList;

    /**
     * 可用结算单余额
     */
    private BigDecimal validRebateCharge;

    /**
     * 返利余额
     */
    private BigDecimal rebateAmount;
    /**
     * 供应商资质是否过期
     */
    private Boolean certificateOverdue;


    public void setTraderSupplierName(String traderSupplierName) {
        this.traderSupplierName = traderSupplierName == null ? null : traderSupplierName.replaceAll("\\(", "（").replaceAll("\\)", "）").trim();
    }

}
