$(function(){
	$("#contact_1").select2();
	$("#address_1").select2();
	$("#contact_2").select2();
	$("#address_2").select2();

	if($('.content2 .payplan .tips-all select').val() == 1){
		$('.tips-error').show();
	}
	$('.content2 .payplan .tips-all select').change(function () {
		if($(this).val() === '1'){
			$('.tips-error').show();
		}else{
			$('.tips-error').hide();
		}
	})
	deliveryClaimChange();
	isSendInvoiceChecked($("#orderTypeInput").val(),$("input[name='isSendInvoice']").val());
})


function submitCheck(type){
	checkLogin();
	var $form = $("#editForm");
	$("form").find('.warning').remove();

	var orderType = Number(type);
	// 不寄送
	var isSendInvoice = $("input[name='isSendInvoice']:checked").val();
//若订单状态为已完成，但开票状态为“未开票”或“部分开票”，仍然显示“申请修改”的按钮，只校验收票信息模块
	if ($('#invoiceModifyflag').val() != 1 && $('#deliveryStatus').val() != 2 && $('#orderStreamStatus').val() != 5) {
		if (orderType == 5) {
			// 收货联系人
			var takeTraderContactName = $.trim($("#takeTraderContactName_5").val());
			if (undefined == takeTraderContactName || takeTraderContactName == '') {
				warnTips("5_takeTraderContactName", "收货联系人不可为空");
				return false;
			}
			if(takeTraderContactName.length > 40) {
				warnTips("5_takeTraderContactName","收货联系人最大支持20个汉字");
				return false;
			}
			// 收货手机号
			var takeTraderContactMobile = $.trim($("#takeTraderContactMobile_5").val());
			if(!/^\d{11}$/.test(takeTraderContactMobile) || !/^1.*/.test(takeTraderContactMobile)
				|| /^10.*/.test(takeTraderContactMobile)
				|| /^11.*/.test(takeTraderContactMobile)
				|| /^12.*/.test(takeTraderContactMobile)) {
				warnTips("5_takeTraderContactMobile","收货手机号格式错误");
				return false;
			}
			// 收货地址
			var takeTraderAddressId = $("#takeTraderAddressId").val();
			if (undefined == takeTraderAddressId || '' == takeTraderAddressId || 0 == takeTraderAddressId) {
				warnTips("5_takeTraderAddress", "收货地址请补充完省市区");
				return false;
			}
			var takeTraderAddress = $.trim($("#takeTraderAddress_5").val());
			if (undefined == takeTraderAddress || '' == takeTraderAddress) {
				warnTips("5_takeTraderAddress", "收货地址不可为空");
				return false;
			}
			if(takeTraderAddress.length > 200) {
				warnTips("5_takeTraderContactName","收货地址最大支持100个汉字");
				return false;
			}
		}else {
			//非耗材订单
			if ($("select[name='takeTraderContactId']").val() == 0) {
				warnTips("takeTraderContactIdMsg", "收货联系人不允许为空");
				return false;
			}
			if ($("select[name='takeTraderAddressId']").val() == 0) {
				warnTips("takeTraderAddressIdMsg", "收货地址不允许为空");
				return false;
			}


			var traderContactIdText = $("select[name='traderContactId']").find("option:selected").text();
			var traderContactIdTextArr = traderContactIdText.split('/');
			$("input[name='traderContactName']").val(traderContactIdTextArr[0]);
			$("input[name='traderContactTelephone']").val(traderContactIdTextArr[1]);
			$("input[name='traderContactMobile']").val(traderContactIdTextArr[2]);
			var traderAddressIdText = $("select[name='traderAddressId']").find("option:selected").text();
			var traderAddressIdTextArr = traderAddressIdText.split('/');
			$("input[name='traderArea']").val(traderAddressIdTextArr[0]);
			$("input[name='traderAddress']").val(traderAddressIdTextArr[1]);
			var takeTraderContactIdText = $("select[name='takeTraderContactId']").find("option:selected").text();
			var takeTraderContactIdTextArr = takeTraderContactIdText.split('/');
			$("input[name='takeTraderContactName']").val(takeTraderContactIdTextArr[0]);
			$("input[name='takeTraderContactTelephone']").val(takeTraderContactIdTextArr[1]);
			$("input[name='takeTraderContactMobile']").val(takeTraderContactIdTextArr[2]);
			var takeTraderAddressIdText = $("select[name='takeTraderAddressId']").find("option:selected").text();
			var takeTraderAddressIdTextArr = takeTraderAddressIdText.split('/');
			$("input[name='takeTraderArea']").val(takeTraderAddressIdTextArr[0]);
			$("input[name='takeTraderAddress']").val(takeTraderAddressIdTextArr[1]);
			var takeAreaLength = takeTraderAddressIdTextArr[0].split(' ').length;
			if (takeAreaLength != 3) {
				warnTips("takeTraderAddressIdMsg", "收货地址请补充完省市区");
				return false;
			}
		}
		var deliveryClaimSelect = $("#deliveryClaimSelect").val();
		var deliveryDelayTimeStr = $("#deliveryDelayTimeStr").val();
		if (deliveryClaimSelect == 1 && deliveryDelayTimeStr=="" ) {
			warnTips("deliveryDelayTimeStrMsg", "请填写等待截止日期");
			return false;
		}

		var logisticsComments = $("#logisticsComments").val();
		if (logisticsComments.length > 256) {
			warnTips("logisticsComments", "物流备注长度应该在0-256个字符之间");
			return false;
		}
	}



	if ($("select[name='invoiceType']").val() == 0) {
		warnTips("invoiceTypeMsg", "发票类型不允许为空");
		return false;
	}
	// if ($("select[name='invoiceMethod']").val() == 0) {
	// 	warnTips("invoiceMethodMsg", "开票方式不允许为空");
	// 	return false;
	// }
	if(orderType != 5) {
		// if (isSendInvoice == 1 || isSendInvoice == '1') {
			if ($("select[name='invoiceTraderContactId']").val() == 0) {
				warnTips("invoiceTraderContactIdMsg", "收票联系人不允许为空");
				return false;
			}
			// if ($("select[name='invoiceTraderAddressId']").val() == 0) {
			// 	warnTips("invoiceTraderAddressIdMsg", "收票地址不允许为空");
			// 	return false;
			// }

			var invoiceTraderContactIdText = $("select[name='invoiceTraderContactId']").find("option:selected").text();
			var invoiceTraderContactIdTextArr = invoiceTraderContactIdText.split('/');
			$("input[name='invoiceTraderContactName']").val(invoiceTraderContactIdTextArr[0]);
			$("input[name='invoiceTraderContactTelephone']").val(invoiceTraderContactIdTextArr[1]);
			$("input[name='invoiceTraderContactMobile']").val(invoiceTraderContactIdTextArr[2]);
			// var invoiceTraderAddressIdText = $("select[name='invoiceTraderAddressId']").find("option:selected").text();
			// var invoiceTraderAddressIdTextArr = invoiceTraderAddressIdText.split('/');
			// $("input[name='invoiceTraderArea']").val(invoiceTraderAddressIdTextArr[0]);
			// $("input[name='invoiceTraderAddress']").val(invoiceTraderAddressIdTextArr[1]);
			//
			// var invoiceAreaLength = invoiceTraderAddressIdTextArr[0].split(' ').length;
			// if (invoiceAreaLength != 3) {
			// 	warnTips("invoiceTraderAddressIdMsg", "收票地址请补充完省市区");
			// 	return false;
			// }
		// }
	}else {
		// 是否寄送 -- 寄送则校验
		// if(isSendInvoice == 1 || isSendInvoice == '1') {
			// 收票联系人
			var invoiceTraderContactName = $.trim($("#invoiceTraderContactName_5").val());
			if(undefined == invoiceTraderContactName || '' == invoiceTraderContactName) {
				warnTips("5_invoiceTraderContactName","收票联系人不可为空");
				return false;
			}
			if(invoiceTraderContactName.length > 40) {
				warnTips("5_invoiceTraderContactName","收票联系人最大支持20个汉字");
				return false;
			}

			var invoiceTraderContactMobile = $.trim($("#invoiceTraderContactMobile_5").val());
			if(!/^\d{11}$/.test(invoiceTraderContactMobile) || !/^1.*/.test(invoiceTraderContactMobile)
				|| /^10.*/.test(invoiceTraderContactMobile)
				|| /^11.*/.test(invoiceTraderContactMobile)
				|| /^12.*/.test(invoiceTraderContactMobile)) {
				warnTips("5_invoiceTraderContactMobile","收票手机号格式错误");
				return false;
			}

			// var invoiceTraderAddressId = $("#invoiceTraderAddressId").val();
			// if(undefined == invoiceTraderAddressId || '' == invoiceTraderAddressId || 0 == invoiceTraderAddressId) {
			// 	warnTips("5_invoiceTraderAddress","收票地址请补充完省市区");
			// 	return false;
			// }
			// var invoiceTraderAddress = $.trim($("#invoiceTraderAddress_5").val());
			// if(undefined == invoiceTraderAddress || '' == invoiceTraderAddress) {
			// 	warnTips("5_invoiceTraderAddress","收票地址不可为空");
			// 	return false;
			// }
			//
			// if(invoiceTraderAddress.length > 200) {
			// 	warnTips("5_invoiceTraderAddress","收票地址最大支持100个汉字");
			// 	return false;
			// }

			var invoiceEmail = $.trim($("#invoiceEmail").val());
			if(invoiceEmail != '' && invoiceEmail != null && invoiceEmail != undefined &&
				!/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(invoiceEmail)) {
				warnTips("invoiceEmail", "收票邮箱格式错误");
				return false;
			}
		// }
	}

	// var invoiceComments = $("#invoiceComments").val();
	// if(invoiceComments.length>256){
	// 	warnTips("invoiceComments","开票备注长度应该在0-256个字符之间");
	// 	return false;
	// }
	var invoiceEmail = $("#invoiceEmail").val();
	if(invoiceEmail != '' && invoiceEmail != null && invoiceEmail !== undefined &&
		!/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(invoiceEmail)) {
		warnTips("invoiceEmail", "收票邮箱格式错误");
		return false;
	}
	if(invoiceEmail.length>64){
		warnTips("invoiceEmail","开票邮箱长度应该在0-64个字符之间");
		return false;
	}
	//如果展示商品，才需要对直发备注做判断
	var idStrArr = $("#id_str").val().split("_");
	for (var i = 0; i < idStrArr.length; i++) {
		if (idStrArr[i] == '') continue;
		if ($('input[name="deliveryDirect_'+idStrArr[i]+'"]:checked').val() == 1 && $("#deliveryDirectComments_" + idStrArr[i]).val() == '') {
			warnTips("commentsMsg_" + idStrArr[i] ,"直发备注不允许为空");
			return false;
		}

		let insideComments = $("#insideComments" + idStrArr[i]).val();
		if (insideComments != undefined  && insideComments.length > 512)  {
			warnTips("insideComments_" + idStrArr[i] ,"超过512个字符");
			return false;
		}
	}


	if(orderType != 5 && $("#invoiceModifyflag").val() !=1 && $('#deliveryStatus').val() != 2 && $('#orderStreamStatus').val() != 5){
		//校验随货出款单不能为空
		var isPrintout = $("#is_printout").val();
		if (isPrintout == ''){
			warnTips("isPrintoutMsg" ,"是否打印随货出库单不能为空");
			return false;
		}
		if (isPrintout == -2){
			warnTips("isPriceMsg" ,"随货出库单是否自带价格不能为空");
			return false;
		}
	}
	if ($("input[name='isSameAddress']:checked").val() == 1 && $("input[name='isSendInvoice']").val() ==1) {

		var reasonNo = isSameAddressSubmitChecked(orderType);

		if (reasonNo != ""){
			layer.open({
				type: 2,
				shadeClose: false, //点击遮罩关闭
				area: ["30%", "40%"],
				title: "提示",
				content: "/order/jc/returnTipsPage.do?reasonNo=" + reasonNo,
			});
			return false;
		}
	}
	$("select[disabled]").each(function() {
		if (parseInt($(this).val()) != -1) {
			$(this).attr("disabled", false);
		}
	});
	$("input[disabled]").each(function() {
		if (parseInt($(this).val()) != -1) {
			$(this).attr("disabled", false);
		}
	});
	// VDERP-15828 此处不知道为何上面两段代码要把所有disabled的组件都解禁，这边直接把invoiceMethod加上吧
	$('select[name="invoiceMethod"]').prop('disabled', true);
	// 序列化时，会将disabled的属性过滤掉，所以上面才会将disabled属性去除
	// 此处定义一个新的input用来序列化invoiceMethod
	$('input[name="invoiceMethod"]').val($('select[name="invoiceMethod"]').val());

	var saleorderId = $("#saleorderId").val();
	var invoiceType = $("select[name='invoiceType']").val();
	var invoiceTraderId = $('#trader_id_2').val();
	$.ajax({
		url : page_url+"/orderstream/saleorder/verifySpecialInvoice.do",
		data :{'saleorderId': saleorderId,'invoiceType' : invoiceType,'invoiceTraderId': invoiceTraderId},
		dataType : 'json',
		async: false,
		success: function(data) {
			if(data.code == 0){
				$form.submit();
			}else {
				layer.confirm(
					'该销售单需维护专票资质，请先维护后再提交修改申请',
					{
						title: '操作提醒',
						btn: ['前往维护']
					},
					function (index){
						$("#toCustomerInfo").attr("tabtitle",'{"num":"viewcustomer'+data.data.traderCustomerId+'","link":"/trader/customer/getFinanceAndAptitude.do?traderId='+data.data.traderId+'&traderCustomerId='+data.data.traderCustomerId+'","title":"客户信息"}');
						$("#toCustomerInfo").click();
					}
				);
			}
		}
	})
	//return false;
}


//  集采订单（JCO/JCF）满足“票货同行”条件：
// 	JCO、JCF订单；
// 1.	发票是否寄送：寄送；
// 2.	开票方式：自动电子发票；
// 3.	订单中全部商品的发货方式为“普发”；
// 4.	订单不存在非“已关闭”状态的“销售订单退货”或“销售订单退票”的售后单；
// 5.	“货票主体”（客户）相同，并且“货票地址”相同；
// 6.	“票货是否同行”：票货同行；
function isSameAddressSubmitChecked(type){
	//没有满足的原因
	var reasonNo="";
	//订单Id
	var saleorderId = $("input[name='saleorderId']").val();

	//发票是否寄送
	var isSendInvoice =  $("input[name='isSendInvoice']").val();
	if(isSendInvoice != 1){
		reasonNo = reasonNo + "1";
	}
	//开票方式
	var invoiceMethod =  $("select[name='invoiceMethod']").val();
	if(invoiceMethod != 3 && invoiceMethod != 4){
		reasonNo = reasonNo + "2";
	}
	// if(type == 5){
	// 	//线上订单
	// 	var takeTraderName = $("#trader_name_1").val();
	// 	var takeTraderAddressId = $("#takeTraderAddressId").val();
	// 	var invoiceTraderName = $("#trader_name_2").val();
	// 	var invoiceTraderAddressId = $("#invoiceTraderAddressId").val();
	// 	var takeTraderAddress_5 = $.trim($("#takeTraderAddress_5").val());
	// 	var invoiceTraderAddress_5 = $.trim($("#invoiceTraderAddress_5").val());
	// 	if(takeTraderName != invoiceTraderName || takeTraderAddressId != invoiceTraderAddressId || takeTraderAddress_5 != invoiceTraderAddress_5){
	// 		reasonNo = reasonNo + "5";
	// 	}
	// }else {
	// 	// 收货客户
	// 	var takeTraderId =  $('#trader_id_1').val();
	// 	//收货地址
	// 	var takeTraderAddressId =  $('select[name=takeTraderAddressId] > option:selected').val();
	// 	// 收票客户
	// 	var invoiceTraderId = $('#trader_id_2').val();
	// 	//收票地址
	// 	var invoiceTraderAddressId = $("select[name='invoiceTraderAddressId']").val();
	// 	if(takeTraderId != invoiceTraderId || takeTraderAddressId != invoiceTraderAddressId){
	// 		reasonNo = reasonNo + "5";
	// 	}
	// }
	//发货方式
	var idStrArr = $("#id_str").val().split("_");
	for (var i = 0; i < idStrArr.length; i++) {
		if (idStrArr[i] == '') continue;
		var deliveryDirecName = "input[name='"+"deliveryDirect_"+idStrArr[i]+"']:checked";
		var deliveryInfo = $(deliveryDirecName).val();
		if (deliveryInfo == 1) {
			reasonNo = reasonNo + "3";
		}
	}
	return reasonNo;
}

function isDelayInvoiceChecked() {
	checkLogin();
	if(typeof($("input[name='isDelayInvoiceCheckbox']:checked").val()) == "undefined") {
		$("input[name='isDelayInvoice']").val(0);
	} else {
		$("input[name='isDelayInvoice']").val(1);
	}
}

function updateInvoiceType(obj){
	checkLogin();
	/*if($(obj).val()=="681"){
		$("select[name='invoiceMethod']").html('<option value="0">请选择</option><option value="1">手动纸质开票</option><option value="2">自动纸质开票</option><option value="3">自动电子发票</option>');
	}else if($(obj).val()=="682"){
		$("select[name='invoiceMethod']").html('<option value="0">请选择</option><option value="1">手动纸质开票</option><option value="2">自动纸质开票</option>');
	}else {
		$("select[name='invoiceMethod']").html('<option value="0">请选择</option>');
	}*/
	if($(obj).val()=="681" || $(obj).val()=="971"){
		if($("input:checkbox[name='isSendInvoiceCheckbox']").is(":checked") && $("input[name='isSendInvoice']").val() == "0"){
			$("select[name='invoiceMethod']").html('<option value="0">请选择</option><option value="1">手动纸质开票</option><option value="2">自动纸质开票</option><option value="4">自动数电发票</option>');
		}else{
			$("select[name='invoiceMethod']").html('<option value="0">请选择</option><option value="1">手动纸质开票</option><option value="2">自动纸质开票</option><option value="3">自动电子发票</option><option value="4">自动数电发票</option>');
		}
	}else if($(obj).val() == "682" || $(obj).val() == "972"){
		$("select[name='invoiceMethod']").html('<option value="0">请选择</option><option value="1">手动纸质开票</option><option value="2">自动纸质开票</option><option value="4">自动数电发票</option>');
	}else {
		$("select[name='invoiceMethod']").html('<option value="0">请选择</option>');
	}
}


function goodsCheckClick(obj){
	if($(obj).is(":checked")){
		var num = 0;
		$("input:checkbox[name='goodsCheckName']").each(function(i){
			if($(this).is(":checked")){
				num ++;
			}
		});
		if(num == $("input:checkbox[name='goodsCheckName']").length){
			$("input:checkbox[name='goodsCheckAllName']").prop("checked",true);
		}
	}else{
		$("input:checkbox[name='goodsCheckAllName']").prop("checked",false);
	}
}

function goodsCheckAllClick(obj){
	if($(obj).is(":checked")){
		$("input:checkbox[name='goodsCheckName']").each(function(i){
			$(this).prop("checked",true);
		});
	}else{
		$("input:checkbox[name='goodsCheckName']").each(function(i){
			$(this).prop("checked",false);
		});
	}
}

function updateSaleGoodsInit(saleorderId,scene){
	checkLogin();
	var saleorderGoodsIdArr = [];
	let skuList = [];
	$("input:checkbox[name='goodsCheckName']:checked").each(function(i){
		saleorderGoodsIdArr.push($(this).val());
		skuList.push({
			skuId: $(this).attr('skuId'),
			skuNo: $(this).attr('skuNo'),
			skuName: $(this).attr('skuName')
		})
	});
	if(saleorderGoodsIdArr.length == 0){
		layer.alert("请选择要修改的商品！");
		return false;
	}
	if (!checkGoods(skuList, 0)) {
		return;
	}
	$("#saleGoodsDeliveryDirect").attr('layerParams','{"width":"760px","height":"650px","title":"修改订单商品","link":"'+ page_url+'/orderstream/saleorder/updateSaleGoodsInit.do?saleorderGoodsIdArr=' + saleorderGoodsIdArr + '&saleorderId='+saleorderId+'&scene='+scene+'"}');
	$("#saleGoodsDeliveryDirect").click();
}

function changeIsPrintout() {
	var isPrint = $("#is_print").val();
	var isScientificDept = $("#is_scientificDept").val();
	var isYxgOrgFlag=$("#isYxgOrgFlag").val();

	if (isPrint == 2){
		//不打印随货出库单
		$("#is_printout").val(0);
		if ($("#is_price").length == 1){
			$("#print_price").remove();
		}
	} else if (isPrint == 1){
		//按最新需求-删除
		//判断打印出库单是否带价格
		/*if (isScientificDept == "true"){
			$("#is_printout").val(3);
		} else {
			if ($("#is_price").length == 0){
				$("#is_print_li").append("<li id='is_price_li'>" +
					"<div style=\"height: 25px\">" +
					"<span style=\"color: red;text-indent: 15px\">  *</span>" +
					"<label style=\"width: 158px\">随货出库单是否自带价格</label>" +
					"<select  id='is_price' name = \"isPrintout\" class=\"input-middle\" style='height: auto' onchange='changeIsPrice()' >" +
					"<option value=\"0\">请选择</option>" +
					"<option value=\"1\">是</option>" +
					"<option value=\"2\">否</option>" +
					"</select>" +
					"</div>" +
					"<div id=\"isPriceMsg\" style=\"clear:both;\"></div>" +
					"</li>");
				$("#is_printout").val(-2);
			}
		}*/
		if ($("#is_price").length == 0){
			$("#print_out_order").append(" <div id =\"print_price\" style=\"display: inline-block\">\n" +
				"	<select  class=\"input-middle\" id='is_price' name = \"isPrintout\" onchange='changeIsPrice()' >\n" +
				"                                                <option value=\"0\">请选择</option>\n" +
				"                                                <option value=\"1\">含价格</option>\n" +
				"                                                <option value=\"2\">不含价格</option>\n" +
				"                                            </select>\n" +
                "<div id=\"isPriceMsg\" style=\"clear:both;\"></div>" +
				"                                        </div>");
			$("#is_printout").val(-2);
		}
	}else if (isPrint == -1){
		$("#is_printout").val(-1);
		if ($("#is_price").length == 1){
			$("#print_price").remove();
		};
	}
}

function changeIsPrice() {
	var isPrice = $("#is_price").val();
	if (isPrice == 1){
		$("#is_printout").val(1);
	} else if (isPrice == 2){
		$("#is_printout").val(2);
	} else if (isPrice == 0){
		$("#is_printout").val(-2);
	}
}
function deliveryClaimChange(){
	if($("#deliveryClaimSelect").val() ==1){
		$("#waitDeadlineDiv").show();
	}else{
		$("#waitDeadlineDiv").hide();
		$("#deliveryDelayTimeStr").val('');
	}
}

function addContact(url){
	checkLogin();
	var open = layer.open({
		type: 2,
		title: '新增联系人',
		shadeClose: false,
		area : ['800px', '600px'],
		content: url
	});
}

function addAddress(url){
	checkLogin();
	var open = layer.open({
		type: 2,
		title: '新增地址',
		shadeClose: false,
		area : ['800px', '600px'],
		content: url
	});
}
function closeGoBack() {
	window.parent.closableTab.close($(window.parent.document).find('[role=presentation].active .glyphicon.small'),window.parent.closableTab.resizeMove);
}

/**
 * 是否寄送发票信息
 *
 * @param orderType
 * @param sendFlag
 */
function isSendInvoiceChecked(orderType, sendFlag) {
	if(orderType == 8 || orderType == 7 || orderType == 5 || orderType == 9){
		if (sendFlag == 0){
			$('#isSameAddressLi').show();
			// $('#traderContactLi').hide();
			// $('#invoiceTraderAddressLi').hide();
			// $('#invoiceTraderContactLi').hide();
			// $('#invoiceTraderContactMobileLi').hide();
			$('#invoiceSendNodeLi').hide();
			$('#invoiceEmailPart').hide();
		} else {
			$('#isSameAddressLi').show();
			$('#invoiceCustomerLi').show();
			// $('#traderContactLi').show();
			// $('#invoiceTraderAddressLi').show();
			// $('#invoiceTraderContactLi').show();
			// $('#invoiceTraderContactMobileLi').show();
			$('#invoiceSendNodeLi').show();
			$('#invoiceEmailPart').show();
		}
	}else {
		if (sendFlag == 0){
			$('#isSameAddressLi').hide();
			$('#invoiceCustomerLi').hide();
			// $('#traderContactLi').hide();
			// $('#invoiceTraderAddressLi').hide();
			// $('#invoiceTraderContactLi').hide();
			// $('#invoiceTraderContactMobileLi').hide();
			$('#invoiceSendNodeLi').hide();
			$('#invoiceEmailPart').hide();
		} else {
			$('#isSameAddressLi').hide();
			$('#invoiceCustomerLi').show();
			// $('#traderContactLi').show();
			// $('#invoiceTraderAddressLi').show();
			// $('#invoiceTraderContactLi').show();
			// $('#invoiceTraderContactMobileLi').show();
			$('#invoiceSendNodeLi').hide();
			$('#invoiceEmailPart').show();
		}
	}
	$("#invoiceType").removeAttr("disabled");
	// $("#invoiceMethod").removeAttr("disabled");
	checkInvoiceGoodsStatus();
}
/**
 * 票货地址是否相同
 *
 * @param isSameAddressFlag
 */
function isSameAddressChecked(isSameAddressFlag) {
	checkInvoiceGoodsStatus();
}

/**
 * 检查票货同行状态
 */
function checkInvoiceGoodsStatus() {
	/**
	 * “发票寄送节点”，该按钮显示的前提是：
	 （1）“发票是否寄送”选择“寄送”
	 （2）“货票地址是否相同”选择“相同”
	 */
	if ($("input[name='isSameAddress']:checked").val() == 1){
		$('#invoiceSendNodeLi').show();
		$('#isSendInvoice').val(1);
	} else {
		$('#invoiceSendNodeLi').hide();
		$('#isSendInvoice').val(0);
	}
}

// 内部备注重复点击 比较最后一次点击时间和当前时间
var lastClick;
function lockClick(){
	let nowClick = new Date()
	if (lastClick === undefined) {
		lastClick = nowClick
		return true
	} else {
		if (Math.round((nowClick.getTime() - lastClick.getTime())) > 2000) {
			lastClick = nowClick
			return true
		}
		else {
			lastClick = nowClick
			return false
		}
	}
}
/**
 * 内部备注组件触发事件
 * @param dom 当前dom元素
 */
function editinsideRemark(dom,count) {
	if(!lockClick()){
		return;
	}

	var id = "#" +  $(dom).prev().attr("id")
	let labelData = $(id).attr('label_data');
	let scene = parseInt($(id).attr('scene'))
	let remark = $(id).val();
	let hasRemark = false;
	if (remark) {
		hasRemark = true;
	}
	let skuList = [
		{
			skuId: $('#goodsId_'+count).val(),
			skuNo: $('#sku_'+count).val(),
			skuName: $('#goodsName_'+count).val()
		}
	]
	if (!checkGoods(skuList, 1)) {
		return;
	}
	let relationId = $("#saleorderId").val();
	new LabelMark({
		el: id,
		value: labelData,
		url: page_url + '/order/remarkComponent/getInitComponent.do',
		query: {
			scene: 0,
			isAll: 1,
			remark: remark,
			hasRemark: hasRemark,
			relationId: relationId,
			skuList: skuList,
			showComponentIcon: true
		},
		saveUrl: page_url + '/orderstream/saleorder/updateInsideComments.do'
	});
}