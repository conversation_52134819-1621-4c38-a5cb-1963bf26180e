package com.task.service;

import com.vedeng.erp.finance.dto.BankBillDto;
import com.vedeng.erp.finance.dto.PayApplyDto;
import com.vedeng.erp.kingdee.dto.KingDeePayBillDto;

/**
 * <AUTHOR>
 * @description 订单流水，审核状态处理
 * @date 2022/9/19 13:40
 **/
public interface KingDeeBankBillDoOrderService {

    /**
     * 售后的
     *
     * @param payApplyDto
     * @param bankBillDto
     * @param data
     */
    void afterOrderVerify(PayApplyDto payApplyDto, BankBillDto bankBillDto, KingDeePayBillDto data);

    /**
     * 采购的
     *
     * @param payApplyDto
     * @param bankBillDto
     * @param data
     */
    void buyOrderVerify(PayApplyDto payApplyDto, BankBillDto bankBillDto, KingDeePayBillDto data);

    /**
     * 采购费用
     *
     * @param payApplyDto
     * @param bankBillDto
     * @param data
     */
    void buyOrderExpenseVerify(PayApplyDto payApplyDto, BankBillDto bankBillDto, KingDeePayBillDto data);
}
