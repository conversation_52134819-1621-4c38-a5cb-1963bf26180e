package com.vedeng.common.constant;

/**
 * <b>Description:</b><br>
 * 字典库常量
 *
 * <AUTHOR>
 * @Note <b>ProjectName:</b> erp <br>
 *       <b>PackageName:</b> com.vedeng.common.constant <br>
 *       <b>ClassName:</b> SysOptionConstant <br>
 *       <b>Date:</b> 2017年7月3日 下午2:51:26
 */
public class SysOptionConstant {

	/**
	 * 新国标二类医疗资质分类操作
	 */
	 public static final Integer NEW_TWO_MEDICAL_CATEGORY=250;
     /**
      * 新国标三类医疗资质分类
      */

	 public static final Integer NEW_THREE_MEDICAL_CATEGORY=251;

	/**
	 * @Fields ID_583 : TODO 售后合同回传
	 */
	public static final Integer ID_583 = 583;

	/**
	 * @Fields ID_588 : TODO 售后退票材料上传
	 */
	public static final Integer ID_588 = 588;

	 /**
	  *一类医疗器械
	  */
	 public static final Integer ID_968=968;
	 /**
	 *二类医疗器械
	 */
	 public static final Integer ID_969=969;
	 /**
	 *三类医疗器械
	 */
	 public static final Integer ID_970=970;

	/**
	 * 发票类型 - 13%增值税普通发票
	 */
	public static final Integer ID_971 = 971;

	/**
	 * 发票类型 - 13%增值税专用发票
	 */
	public static final Integer ID_972 = 972;

	/**
	 * 发票类型 - 16%增值税专用发票
	 */
	public static final Integer ID_682 = 682;

	/**
	 * 发票类型 - 6%增值税专用发票
	 */
	public static final Integer ID_684 = 684;

	/**
	 * @Fields ID_1 : TODO 交易者资质ID
	 */
	public static final Integer ID_1 = 1;

	/**
	 * @Fields SCOP_1001 : TODO 交易者资质SCOP
	 */
	public static final Integer SCOP_1001 = 1001;

	public static final Integer MINUS_ONE = -1;

	/**
	 * @Fields ID_2 : TODO 标签类型ID
	 */
	public static final Integer ID_2 = 2;

	/**
	 * @Fields SCOP_1002 : TODO 标签类型SCOP
	 */
	public static final Integer SCOP_1002 = 1002;

	/**
	 * @Fields ID_3 : TODO 学历ID
	 */
	public static final Integer ID_3 = 3;

	/**
	 * @Fields SCOP_1003 : TODO 学历SCOP
	 */
	public static final Integer SCOP_1003 = 1003;

	/**
	 * @Fields ID_4 : TODO 性格ID
	 */
	public static final Integer ID_4 = 4;

	/**
	 * @Fields SCOP_1004 : TODO 性格SCOP
	 */
	public static final Integer SCOP_1004 = 1004;

	/**
	 * @Fields ID_5 : TODO 信用评估ID
	 */
	public static final Integer ID_5 = 5;

	/**
	 * @Fields SCOP_1005 : TODO 信用评估SCOP
	 */
	public static final Integer SCOP_1005 = 1005;

	/**
	 * @Fields ID_6 : TODO 交易者账户流水(事项)ID
	 */
	public static final Integer ID_6 = 6;

	/**
	 * @Fields SCOP_1006 : TODO SCOP
	 */
	public static final Integer SCOP_1006 = 1006;

	/**
	 * @Fields ID_7 : TODO 供应商等级ID
	 */
	public static final Integer ID_7 = 7;

	/**
	 * @Fields SCOP_1007 : TODO 供应商等级SCOP
	 */
	public static final Integer SCOP_1007 = 1007;

	/**
	 * @Fields ID_8 : TODO 审核类型ID
	 */
	public static final Integer ID_8 = 8;

	/**
	 * @Fields SCOP_1008 : TODO 审核类型SCOP
	 */
	public static final Integer SCOP_1008 = 1008;

	/**
	 * @Fields ID_9 : TODO 客户来源ID
	 */
	public static final Integer ID_9 = 9;

	/**
	 * @Fields SCOP_1009 : TODO 客户来源SCOP
	 */
	public static final Integer SCOP_1009 = 1009;

	/**
	 * @Fields ID_10 : TODO 首次询价方式ID
	 */
	public static final Integer ID_10 = 10;

	/**
	 * @Fields SCOP_1010 : TODO 首次询价方式SCOP
	 */
	public static final Integer SCOP_1010 = 1010;

	/**
	 * @Fields ID_11 : TODO 客户等级ID
	 */
	public static final Integer ID_11 = 11;

	/**
	 * @Fields SCOP_1011 : TODO 客户等级SCOP
	 */
	public static final Integer SCOP_1011 = 1011;

	/**
	 * @Fields ID_12 : TODO 销售评级ID
	 */
	public static final Integer ID_12 = 12;

	/**
	 * @Fields SCOP_1013 : TODO 销售评级SCOP
	 */
	public static final Integer SCOP_1012 = 1012;

	/**
	 * @Fields ID_13 : TODO 所有制公立私立ID
	 */
	public static final Integer ID_13 = 13;

	/**
	 * @Fields SCOP_1013 : TODO 所有制公立私立SCOP
	 */
	public static final Integer SCOP_1013 = 1013;

	/**
	 * @Fields ID_14 : TODO 医学类型ID
	 */
	public static final Integer ID_14 = 14;

	/**
	 * @Fields SCOP_1014 : TODO 医学类型SCOP
	 */
	public static final Integer SCOP_1014 = 1014;

	/**
	 * @Fields ID_15 : TODO 医院等级ID
	 */
	public static final Integer ID_15 = 15;

	/**
	 * @Fields SCOP_1015 : TODO 医院等级SCOP
	 */
	public static final Integer SCOP_1015 = 1015;

	/**
	 * @Fields ID_16 : TODO 员工人数ID
	 */
	public static final Integer ID_16 = 16;

	/**
	 * @Fields SCOP_1016 : TODO 员工人数SCOP
	 */
	public static final Integer SCOP_1016 = 1016;

	/**
	 * @Fields ID_17 : TODO 年销售额ID
	 */
	public static final Integer ID_17 = 17;

	/**
	 * @Fields SCOP_1017 : TODO 年销售额SCOP
	 */
	public static final Integer SCOP_1017 = 1017;

	/**
	 * @Fields ID_18 : TODO 销售模式ID
	 */
	public static final Integer ID_18 = 18;

	/**
	 * @Fields SCOP_1018 : TODO 销售模式SCOP
	 */
	public static final Integer SCOP_1018 = 1018;

	/**
	 * @Fields ID_19 : TODO 客户属性ID
	 */
	public static final Integer ID_19 = 19;

	/**
	 * @Fields SCOP_1019 : TODO 客户属性SCOP
	 */
	public static final Integer SCOP_1019 = 1019;

	/**
	 * @Fields ID_20 : TODO 医疗类别ID
	 */
	public static final Integer ID_20 = 20;

	/**
	 * 医疗类别新国标
	 */
	 public static final Integer ID_1024=1024;

	/**
	 * @Fields SCOP_1020 : TODO 医疗类别SCOP
	 */
	public static final Integer SCOP_1020 = 1020;

	/**
	 * @Fields ID_21 : TODO 医疗类别级别ID
	 */
	public static final Integer ID_21 = 21;

	/**
	 * @Fields SCOP_1021 : TODO 医疗类别级别SCOP
	 */
	public static final Integer SCOP_1021 = 1021;

	/**
	 * @Fields ID_22 : TODO 沟通记录类型ID
	 */
	public static final Integer ID_22 = 22;

	/**
	 * @Fields SCOP_1022 : TODO 沟通记录类型SCOP
	 */
	public static final Integer SCOP_1022 = 1022;

	/**
	 * @Fields ID_23 : TODO 沟通方式ID
	 */
	public static final Integer ID_23 = 23;

	/**
	 * @Fields SCOP_1023 : TODO 沟通方式SCOP
	 */
	public static final Integer SCOP_1023 = 1023;

	/**
	 * @Fields ID_24 : TODO 沟通目的ID
	 */
	public static final Integer ID_24 = 24;

	/**
	 * @Fields SCOP_1024 : TODO 沟通目的SCOP
	 */
	public static final Integer SCOP_1024 = 1024;

	/**
	 * @Fields ID_86 : TODO 经营客户类型ID
	 */
	public static final Integer ID_86 = 86;

	/**
	 * @Fields SCOP_1025 : TODO 经营客户类型SCOP
	 */
	public static final Integer SCOP_1025 = 1025;

	/**
	 * @Fields ID_92 : TODO 经营客户类型ID
	 */
	public static final Integer ID_92 = 92;

	/**
	 * @Fields SCOP_1026 : TODO 经营客户类型SCOP
	 */
	public static final Integer SCOP_1026 = 1026;

	/**
	 * @Fields ID_106 : TODO 经营产品类型ID
	 */
	public static final Integer ID_106 = 106;

	/**
	 * @Fields SCOP_1027 : TODO 经营产品类型SCOP
	 */
	public static final Integer SCOP_1027 = 1027;

	/**
	 * @Fields ID_110 : TODO 经营产品类型ID
	 */
	public static final Integer ID_110 = 110;

	/**
	 * @Fields SCOP_1028 : TODO 经营产品类型SCOP
	 */
	public static final Integer SCOP_1028 = 1028;

	/**
	 * @Fields ID_116 : TODO 综合医院属性ID
	 */
	public static final Integer ID_116 = 116;

	/**
	 * @Fields SCOP_1029 : TODO 综合医院属性SCOP
	 */
	public static final Integer SCOP_1029 = 1029;

	/**
	 * @Fields ID_123 : TODO 综合医院等级ID
	 */
	public static final Integer ID_123 = 123;

	/**
	 * @Fields SCOP_1030 : TODO 综合医院等级SCOP
	 */
	public static final Integer SCOP_1030 = 1030;

	/**
	 * @Fields ID_272 : TODO 经营科室ID
	 */
	public static final Integer ID_272 = 272;

	/**
	 * @Fields SCOP_1031 : TODO 经营科室SCOP
	 */
	public static final Integer SCOP_1031 = 1031;

	/**
	 * @Fields ID_286 : TODO 临床分销经营产品ID
	 */
	public static final Integer ID_286 = 286;

	/**
	 * @Fields SCOP_1032 : TODO 临床分销经营产品SCOP
	 */
	public static final Integer SCOP_1032 = 1032;

	/**
	 * @Fields ID_303 : TODO 战略合作伙伴ID
	 */
	public static final Integer ID_303 = 303;

	/**
	 * @Fields SCOP_1033 : TODO 战略合作伙伴SCOP
	 */
	public static final Integer SCOP_1033 = 1033;

	/**
	 * @Fields ID_309 : TODO 职位类型ID
	 */
	public static final Integer ID_309 = 309;

	/**
	 * @Fields SCOP_1034 : TODO 职位类型SCOP
	 */
	public static final Integer SCOP_1034 = 1034;

	/**
	 * @Fields ID_315 : TODO 产品类型ID
	 */
	public static final Integer ID_315 = 315;

	public static final Integer ID_316 = 316;// 器械

	/**
	 * @Fields SCOP_1035 : TODO 产品类型SCOP
	 */
	public static final Integer SCOP_1035 = 1035;

	/**
	 * @Fields ID_320 : TODO 应用科室ID
	 */
	public static final Integer ID_320 = 320;

	/**
	 * @Fields SCOP_1036 : TODO 应用科室SCOP
	 */
	public static final Integer SCOP_1036 = 1036;

	/**
	 * @Fields ID_334 : TODO 产品级别ID
	 */
	public static final Integer ID_334 = 334;

	/**
	 * @Fields SCOP_1037 : TODO 产品级别SCOP
	 */
	public static final Integer SCOP_1037 = 1037;

	/**
	 * @Fields ID_338 : TODO 管理类别级别ID
	 */
	public static final Integer ID_338 = 338;

	/**
	 * @Fields SCOP_1038 : TODO 管理类别级别SCOP
	 */
	public static final Integer SCOP_1038 = 1038;

	/**
	 * @Fields ID_342 : TODO 产品附件类型ID
	 */
	public static final Integer ID_342 = 342;

	/**
	 * @Fields SCOP_1039 : TODO 产品附件类型SCOP
	 */
	public static final Integer SCOP_1039 = 1039;

	/**
	 * @Fields ID_346 : TODO 诊所规模ID
	 */
	public static final Integer ID_346 = 346;

	/**
	 * @Fields SCOP_1040 : TODO 诊所规模SCOP
	 */
	public static final Integer SCOP_1040 = 1040;

	/**
	 * @Fields ID_350 : TODO 诊所性质ID
	 */
	public static final Integer ID_350 = 350;

	/**
	 * @Fields SCOP_1041 : TODO 诊所性质SCOP
	 */
	public static final Integer SCOP_1041 = 1041;

	/**
	 * @Fields ID_354 : TODO 诊所投资背景ID
	 */
	public static final Integer ID_354 = 354;

	/**
	 * @Fields SCOP_1042 : TODO 诊所投资背景SCOP
	 */
	public static final Integer SCOP_1042 = 1042;

	/**
	 * @Fields ID_362 : TODO 诊所学科类型ID
	 */
	public static final Integer ID_362 = 362;

	/**
	 * @Fields SCOP_1043 : TODO 诊所学科类型SCOP
	 */
	public static final Integer SCOP_1043 = 1043;

	/**
	 * @Fields ID_365 : TODO 商机来源ID
	 */
	public static final Integer ID_365 = 365;

	/**
	 * @Fields ID_365 : TODO 商机来源ID：老客户询价
	 */
	public static final Integer ID_366 = 366;
	/**
	 * @Fields ID_477 : TODO 商机来源ID：新客户询价
	 */
	public static final Integer ID_477 = 477;

	/**
	 * @Fields SCOP_1044 : TODO 商机来源SCOP
	 */
	public static final Integer SCOP_1044 = 1044;

	/**
	 * @Fields ID_376 : TODO 询价方式ID
	 */
	public static final Integer ID_376 = 376;

	/**
	 * @Fields SCOP_1045 : TODO 询价方式SCOP
	 */
	public static final Integer SCOP_1045 = 1045;

	/**
	 * @Fields ID_387 : TODO 商机商品分类ID
	 */
	public static final Integer ID_387 = 387;

	/**
	 * @Fields SCOP_1046 : TODO 商机商品分类SCOP
	 */
	public static final Integer SCOP_1046 = 1046;

	/**
	 * @Fields ID_390 : TODO 商机类型ID
	 */
	public static final Integer ID_390 = 390;
	/**
	 * @Fields ID_390 : TODO 商机类型ID:销售新增询价
	 */
	public static final Integer ID_392 = 392;

	/**
	 * @Fields SCOP_1047 : TODO 商机类型SCOP
	 */
	public static final Integer SCOP_1047 = 1047;

	/**
	 * @Fields ID_395 : TODO 商机无法报价原因ID
	 */
	public static final Integer ID_395 = 395;

	/**
	 * @Fields ID_395 : TODO 商机作废原因ID
	 */
	public static final Integer ID_961 = 961;

	/**
	 * @Fields SCOP_1048 : TODO 商机无法报价原因SCOP
	 */
	public static final Integer SCOP_1048 = 1048;

	/**
	 * @Fields ID_404 : TODO 采购方式ID
	 */
	public static final Integer ID_404 = 404;

	/**
	 * @Fields SCOP_1049 : TODO 采购方式SCOP
	 */
	public static final Integer SCOP_1049 = 1049;

	/**
	 * @Fields ID_407 : TODO 付款条件ID
	 */
	public static final Integer ID_407 = 407;

	/**
	 * @Fields SCOP_1050 : TODO 付款条件SCOP
	 */
	public static final Integer SCOP_1050 = 1050;

	/**
	 * @Fields ID_410 : TODO 采购时间ID
	 */
	public static final Integer ID_410 = 410;

	/**
	 * @Fields SCOP_1051 : TODO 采购时间SCOP
	 */
	public static final Integer SCOP_1051 = 1051;

	/**
	 * @Fields ID_418 : TODO 付款方式ID
	 */
	public static final Integer ID_418 = 418;

	/**
	 * 先款后货，预付100%
	 */
	public static final Integer ID_419 = 419;

	/**
	 * 先款后货，预付0%
	 */
	public static final Integer ID_423 = 423;

	/**
	 * @Fields SCOP_1052 : TODO 付款方式SCOP
	 */
	public static final Integer SCOP_1052 = 1052;

	/**
	 * @Fields ID_425 : TODO 客户类型ID
	 */
	public static final Integer ID_425 = 425;

	/**
	 * @Fields SCOP_1053 : TODO 客户类型SCOP
	 */
	public static final Integer SCOP_1053 = 1053;

	/**
	 * @Fields ID_428 : TODO 发票类型ID
	 */
	public static final Integer ID_428 = 428;

	/**
	 * @Fields ID_429 : 发票类型-17%增值税专用发票
	 */
	public static final Integer ID_429 = 429;

	/**
	 * 开票类型
	 */
	public static final Integer ID_502 = 502;

	/**
	 * @Fields ID_428 : TODO 采购发票类型ID
	 */
	public static final Integer ID_506 = 506;

	/**
	 * @Fields SCOP_1054 : TODO 发票类型SCOP
	 */
	public static final Integer SCOP_1054 = 1054;

	/**
	 * @Fields ID_431 : TODO 帐期及余额记录事项ID
	 */
	public static final Integer ID_431 = 431;

	/**
	 * @Fields SCOP_1055 : TODO 帐期及余额记录事项SCOP
	 */
	public static final Integer SCOP_1055 = 10555;

	/**
	 * @Fields ID_440 : TODO 职位等级ID
	 */
	public static final Integer ID_440 = 440;

	/**
	 * @Fields SCOP_1056 : TODO 职位等级SCOP
	 */
	public static final Integer SCOP_1056 = 1056;

	/**
	 * @Fields ID_456 : TODO 公告类型ID
	 */
	public static final Integer ID_456 = 456;

	/**
	 * @Fields SCOP_1057 : TODO 公告类型SCOP
	 */
	public static final Integer SCOP_1057 = 1057;

	/**
	 * @Fields ID_464 : TODO 客户性质ID
	 */
	public static final Integer ID_464 = 464;

	/**
	 * @Fields SCOP_1058 : TODO 客户性质SCOP
	 */
	public static final Integer SCOP_1058 = 1058;
	/**
	 * @Fields ID_314 : TODO 职位类型为财务的的ID
	 */
	public static final Integer ID_314 = 314;
	/**
	 * @Fields ID_313 : TODO 职位类型为物流的的ID
	 */
	public static final Integer ID_313 = 313;
	/**
	 * @Fields ID_312 : TODO 职位类型为售后的的ID
	 */
	public static final Integer ID_312 = 312;

	/**南京银行银企直连支付**/
	public static final Integer ID_641 = 641;

	/**特殊商品**/
	public static final Integer SPECIAL_SKU = 693;
	/**
	 * @Fields ID_645 : TODO 售前客服
	 */
	public static final Integer ID_645 = 645;

	/**
	 * @Fields ID_311 : TODO 职位类型为采购的的ID
	 */
	public static final Integer ID_311 = 311;

	/**
	 * @Fields ID_310 : TODO 职位类型为销售的的ID
	 */
	public static final Integer ID_310 = 310;

	/**
	 * @Fields ID_391 : TODO 商机类型：总机询价ID
	 */
	public static final Integer ID_391 = 391;
	/**
	 * 商机来源-拜访
	 */
	public static final Integer ID_6012 = 6012;


	/**
	 * @Fields ID_399 : 运营ID
	 */
	public static final Integer ID_399 = 399;

	/**
	 * @Fields ID_438 : TODO 医疗机构执业许可证ID
	 */
	public static final Integer ID_438 = 438;

	/**
	 * @Fields ID_439 : TODO 医疗器械生产许可证ID
	 */
	public static final Integer ID_439 = 439;

	/**
	 * @Fields ID_460 : TODO 图片ID
	 */
	public static final Integer ID_460 = 460;

	/**
	 * @Fields ID_461 : TODO 文件ID
	 */
	public static final Integer ID_461 = 461;
	/**
	 * @Fields ID_462 : TODO
	 */
	public static final Integer ID_462 = 462;

	/**
	 * @Fields ID_463 : TODO 附件应用类型：商机ID
	 */
	public static final Integer ID_463 = 463;

	/**
	 * @Fields ID_29 : TODO 三类医疗资质ID
	 */
	public static final Integer ID_29 = 29;

	/**
	 * @Fields ID_28 : TODO 二类医疗资质ID
	 */
	public static final Integer ID_28 = 28;

	/**
	 * @Fields ID_27 : TODO 组织机构代码证ID
	 */
	public static final Integer ID_27 = 27;

	/**
	 * @Fields ID_26 : TODO 税务登记证ID
	 */
	public static final Integer ID_26 = 26;

	/**
	 * @Fields ID_25 : TODO 营业执照ID
	 */
	public static final Integer ID_25 = 25;

	/**
	 * @Fields ID_31 : TODO 供应商标签ID
	 */
	public static final Integer ID_31 = 31;

	/**
	 * @Fields ID_30 : TODO 经销商标签ID
	 */
	public static final Integer ID_30 = 30;

	/**
	 * @Fields ID_32 : TODO 沟通记录标签ID
	 */
	public static final Integer ID_32 = 32;

	/**
	 * @Fields ID_242 : TODO 沟通记录类型:客户ID
	 */
	public static final Integer ID_242 = 242;

	/**
	 * @Fields ID_243 : TODO 沟通记录类型:供应商ID
	 */
	public static final Integer ID_243 = 243;

	/**
	 * @Fields ID_244 : TODO 沟通记录类型:询价ID
	 */
	public static final Integer ID_244 = 244;

	/**
	 * @Fields ID_245 : TODO 沟通记录类型:报价ID
	 */
	public static final Integer ID_245 = 245;

	/**
	 * @Fields ID_246 : TODO 沟通记录类型:销售订单ID
	 */
	public static final Integer ID_246 = 246;

	/**
	 * @Fields ID_247 : TODO 沟通记录类型:采购订单ID
	 */
	public static final Integer ID_247 = 247;

	/**
	 * @Fields ID_248 : TODO 沟通记录类型:售后订单ID
	 */
	public static final Integer ID_248 = 248;

	/**
	 * 沟通类型 线索
	 */
	public static final Integer ID_4083 = 4083;

	/**
	 * 沟通类型 商机线索
	 */
	public static final Integer ID_4109 = 4109;

	/**
	 * 沟通类型 采购费用售后
	 */
	public static final Integer ID_4133 = 4133;

	/**
	 * 沟通类型 产品推广-9-5501
	 */
	public static final Integer ID_COMMNCATE_TYPE_5501 = 5501;

	/**
	 * 沟通类型 客户开发-10-5502
	 */
	public static final Integer ID_COMMNCATE_TYPE_5502= 5502;

	/**
	 * 沟通类型 拜访计划-11-5503
	 */
	public static final Integer ID_COMMNCATE_TYPE_5503= 5503;



	/**
	 * @Fields ID_136 : TODO 审核类型：客户ID
	 */
	public static final Integer ID_135 = 135;

	/**
	 * @Fields ID_136 : TODO 审核类型：供应商ID
	 */
	public static final Integer ID_136 = 136;

	/**
	 * @Fields ID_426 : TODO 科研医疗ID
	 */
	public static final Integer ID_426 = 426;

	/**
	 * @Fields ID_427 : TODO 临床医疗ID
	 */
	public static final Integer ID_427 = 427;

	/**
	 * @Fields ID_465 : TODO 分销ID
	 */
	public static final Integer ID_465 = 465;

	/**
	 * @Fields ID_466 : TODO 终端ID
	 */
	public static final Integer ID_466 = 466;

	/**
	 * @Fields ID_CUSTOMER_ATTRIBUTE_CATEGORY_26 : TODO 客户分类：战略合作伙伴
	 *         T_TRADER_CUSTOMER_ATTRIBUTE_CATEGORY =>ATTRIBUTE_CATEGORY_ID
	 */
	public static final Integer ID_CUSTOMER_ATTRIBUTE_CATEGORY_26 = 26;

	/**
	 * @Fields ID_432 : TODO 账期申请ID
	 */
	public static final Integer ID_432 = 432;

	/**
	 * @Fields ID_433 : TODO 账期审核ID
	 */
	public static final Integer ID_433 = 433;

	/**
	 * @Fields ID_469 : TODO 运费说明ID
	 */
	public static final Integer ID_469 = 469;

	/**
	 * @Fields ID_470 : TODO 合同总额包含运费，送货上门ID
	 */
	public static final Integer ID_470 = 470;

	/**
	 * @Fields ID_471 : TODO 合同总额包含运费，送货上门但不上楼ID
	 */
	public static final Integer ID_471 = 471;

	/**
	 * @Fields ID_472 : TODO 合同总额包含运费，买方至物流点自提ID
	 */
	public static final Integer ID_472 = 472;

	/**
	 * @Fields ID_473 : TODO 运费买方到付，送货上门但不上楼ID
	 */
	public static final Integer ID_473 = 473;

	/**
	 * @Fields ID_474 : TODO 运费买方到付，送货上门ID
	 */
	public static final Integer ID_474 = 474;

	/**
	 * @Fields ID_475 : TODO 运费买方到付，买方至物流点自提ID
	 */
	public static final Integer ID_475 = 475;

	/**
	 * @Fields ID_476 : TODO 买方上门自提ID
	 */
	public static final Integer ID_476 = 476;
	/**
	 * @Fields ID_478 : TODO 消息类型
	 */
	public static final Integer ID_478 = 478;
	/**
	 * @Fields ID_249 : TODO 沟通方式 来电
	 */
	public static final Integer ID_249 = 249;
	/**
	 * @Fields ID_250 : TODO 沟通方式 去电
	 */
	public static final Integer ID_250 = 250;
	/**
	 * @Fields ID_488 : TODO 寄送形式
	 */
	public static final Integer ID_488 = 488;
	/**
	 * @Fields ID_343 : TODO 产品图片
	 */
	public static final Integer ID_343 = 343;
	/**
	 * @Fields ID_494 : TODO 报价产品
	 */
	public static final Integer ID_494 = 494;
	/**
	 * @Fields ID_494 : TODO 销售订单合同回传
	 */
	public static final Integer ID_492 = 492;
	/**
	 * @Fields ID_494 : TODO 销售订单送货单回传
	 */
	public static final Integer ID_493 = 493;
	/**
	 * @Fields ID_498 : TODO 业务类型是销售
	 */
	public static final Integer ID_496 = 496;
	/**
	 * @Fields ID_498 : TODO 业务类型是销售发票
	 */
	public static final Integer ID_497 = 497;
	/**
	 * @Fields ID_498 : TODO 业务类型是文件寄送
	 */
	public static final Integer ID_498 = 498;
	/**
	 * @Fields ID_498 : TODO 业务类型是采购
	 */
	public static final Integer ID_515 = 515;
	/**
	 * @Fields ID_494 : TODO 采购订单合同回传
	 */
	public static final Integer ID_514 = 514;
	/**
	 * @Fields BUYGOODS_DOC : TODO 采购单随货同行单
	 */
	public static final Integer BUYGOODS_DOC = 1200;

	//售后付款类型申请
	public static final Integer ID_518 = 518;

	/**
	 * @Fields ID_499 : TODO 采购入库
	 */
	public static final Integer ID_499 = 499;
	/**
	 * @Fields ID_500 : TODO 二维码
	 */
	public static final Integer ID_500 = 500;

	/**
	 * @Fields ID_503 : TODO 采购开票
	 */
	public static final Integer ID_503 = 503;

	/**
	 * 采购费用收票
	 */
	public static final Integer ID_4126 = 4126;

	/**
	 * @Fields ID_504 : TODO 售后开票
	 */
	public static final Integer ID_504 = 504;

	/**
	 * @Fields ID_505 : TODO 销售开票
	 */
	public static final Integer ID_505 = 505;
	//销售开票和售后开票
	public static final Integer ID_505_504 = 505504;
	/**
	 * @Fields ID_525 : TODO 业务类型：订单付款
	 */
	public static final Integer ID_525 = 525;

	/**
	 * @Fields ID_526 : TODO 业务类型：订单收款
	 */
	public static final Integer ID_526 = 526;

	/**
	 * @Fields ID_533 : 信用还款
	 */
	public static final Integer ID_533 = 533;
	/**
	 * @Fields ID_505 : TODO 销售退货
	 */
	public static final Integer ID_539 = 539;

	/**
	 * @Fields ID_542 : TODO 销售订单退票
	 */
	public static final Integer ID_542 = 542;

	/**
	 * @Fields ID_531 : TODO 退款
	 */
	public static final Integer ID_531 = 531;
	/**
	 * @Fields ID_543 : TODO 销售订单退款
	 */
	public static final Integer ID_543 = 543;

	/**
	 * 销售售后-安调
	 */
	public static final Integer ID_541 = 541;

	/**
	 * @Fields ID_505 : TODO 销售换货
	 */
	public static final Integer ID_540 = 540;
	/**
	 * @Fields ID_535 : TODO 销售
	 */
	public static final Integer ID_535 = 535;
	/**
	 * @Fields ID_536 : TODO 采购
	 */
	public static final Integer ID_536 = 536;
	/**
	 * @Fields ID_537 : TODO 第三方
	 */
	public static final Integer ID_537 = 537;
	/**
	 * @Fields ID_505 : TODO 售后
	 */
	public static final Integer ID_582 = 582;
	/**
	 * @Fields ID_519 : TODO 交易方式
	 */
	public static final Integer ID_519 = 519;
	/**
	 * @Fields ID_519 : TODO 采购退货
	 */
	public static final Integer ID_546 = 546;
	/**
	 * @Fields ID_519 : TODO 采购换货
	 */
	public static final Integer ID_547 = 547;
	/**
	 * @Fields
	 */
	public static final Integer ID_548 = 548;

	/**
	 * @Fields ID_640 : TODO 交易方式（付款）
	 */
	public static final Integer ID_640 = 640;

	/**
	 * 供应商资质 --销售授权书与销售人信息
	 */
	public static final Integer ID_1100 = 1100;

	/**
	 * 供应商资质 -- 第一类医疗器械生产备案凭证
	 */
	public static final Integer ID_1101 = 1101;

	/**
	 * 供应商资质 -- 生产企业生产产品登记表
	 */
	public static final Integer ID_1102 = 1102;
	/**
	 * @Fields ID_718 : TODO 售后换货费用类型
	 */
	public static final Integer ID_718 = 718;

	/**
	 * @Fields ID_719 : TODO 售后退货费用类型
	 */
	public static final Integer ID_719 = 719;

	/**
	 * @Fields ID_720 : TODO 售后维修费用类型
	 */
	public static final Integer ID_720 = 720;

	/**
	 * @Fields ID_721 : TODO 售后发票费用类型
	 */
	public static final Integer ID_721 = 721;

	/**
	 * @Fields ID_722 : TODO 售后退款费用类型
	 */
	public static final Integer ID_722 = 722;

	/**
	 * @Fields ID_723 : TODO 售后安装费用类型
	 */
	public static final Integer ID_723 = 723;

	/**
	 * @Fields ID_724 : TODO 售后技术咨询费用类型
	 */
	public static final Integer ID_724 = 724;
	/**
	 * @Fields ID_837 : TODO 入库附件类型
	 */
	public static final Integer ID_837 = 837;

	/**
	 * @Fields ID_756 : TODO 售后退货原因
	 */
	public static final Integer ID_756 = 756;

	/**
	 * @Fields ID_757 : TODO 售后换货原因
	 */
	public static final Integer ID_757 = 757;

	/**
	 * @Fields ID_758 : TODO 售后维修原因
	 */
	public static final Integer ID_758 = 758;

	/**
	 * @Fields ID_759 : TODO 售后退票原因
	 */
	public static final Integer ID_759 = 759;

	/**
	 * @Fields ID_760 : TODO 售后退款原因
	 */
	public static final Integer ID_760 = 760;

	/**
	 * @Fields ID_894 : TODO 品牌授权书ID
	 */
	public static final Integer ID_894 = 894;

	/**
	 * @Fields ID_895 : TODO 其他医疗资质ID
	 */
	public static final Integer ID_895 = 895;

	/**
	 * @Fields 896 : 随货同行模板
	 */
	public static final Integer ID_896 = 896;

	/**
	 * @Fields 897 : 质量保证协议
	 */
	public static final Integer ID_897 = 897;

	/**
	 * @Fields 898 : 售后服务承诺书
	 */
	public static final Integer ID_898 = 898;

	/**
	 * @Fields 899 : 质量体系调查表或合格供应商档案
	 */
	public static final Integer ID_899 = 899;

	/**
	 * 订单归属人userId，多个以，分割 906
	 */
	public static final Integer ID_906 = 906;
	/**
	 * 商机询价类型
	 */
	public static final Integer ID_948 = 948;
	/**
	 * 商机阶段
	 */
	public static final Integer ID_943 = 943;
	/**
	 * 商机成单几率
	 */
	public static final Integer ID_951 = 951;
	/**
	 * 商机等级
	 */
	public static final Integer ID_938 = 938;
	/**
	 * 代付款证明
	 * 905
	 */
	public static final Integer ID_905 = 905;
	/**
	 * 产品检测报告
	 */
	public static final Integer ID_658 = 658;
	/**
	 * 产品检测报告
	 */
	public static final Integer ID_659 = 659;
	/**
	 * 外接单
	 */
	public static final Integer ID_660 = 660;
	/**
	*	sku二维码
	* @Author:strange
	* @Date:16:45 2020-03-20
	*/
	public static final Integer ID_1400 = 1400;


	/**
	 * 医疗器械经营许可
	 */
	public static final Integer ID_678 = 678;

	/**
	 *	报价关闭原因
	 * @Author:strange
	 * @Date:16:45 2020-03-20
	 */
	public static final Integer ID_1600 = 1600;
	//销售原因
	public static final Integer ID_1601 = 1601;
	//客户原因
	public static final Integer ID_1602 = 1602;
	//产品原因
	public static final Integer ID_1603 = 1603;
	//报废品分类
	public static final Integer SCRAP_TYPE = 1901;
	//报废品级别
	public static final Integer SCRAP_LEVEL = 1902;
	//报废品处理方式
	public static final Integer SCRAP_DEAL_TYPE = 1903;
	//销毁
	public static final Integer SCRAPE_DEAL_DESTORY = 1911;
	//内部领用
	public static final Integer SCRAPE_DEAL_NB = 1912;
	//零配件领用
	public static final Integer SCRAPE_DEAL_LY = 1913;
	//其他
	public static final Integer SCRAPE_DEAL_OTHER = 1914;




	/**
	 * SPU检测报告
	 */
	public static final String SPU_CHECK_FILES = "SPU_CHECK_FILE";
	/**
	 * SPU专利文件
	 */
	public static final String SPU_PATENT_FILES = "SPU_PATENT_FILE";
	/**
	 * SKU检测报告
	 */
	public static final String SKU_CHECK_FILES = "SKU_CHECK_FILE";
	/**
	 * SKU专利文件
	 */
	public static final String SKU_PATENT_FILES = "SKU_PATENT_FILE";


	public static final String SPU_TYPE_MACHINE = "SPU_TYPE_MACHINE";
	public static final String SPU_TYPE_HAOCAI = "SPU_TYPE_HAOCAI";




	/**
	 *试剂
	 */
	public static final String SPU_TYPE_3 ="SPU_TYPE_3" ;
	/**
	 *高值耗材
	 */
	public static final String SPU_TYPE_4 ="SPU_TYPE_4" ;


	public static final String SKU_CORE_PART_PRICE_FILE = "SKU_CORE_PART_PRICE_FILE";





	public static final Integer ID_319 = 319;

	/**
	 * 耗材消息推送人员
	 */
	public static final Integer ID_995 = 995;

	/**
	 * 尊敬的客户，您的订单已发货，请注意查收：
	 * ID后期修改
	 */
	public static final String ID_WECHAT_TEMPLATE_ORDER_SIGNING_NOTICE_FIRST = "WECHAT_TEMPLATE_ORDER_SIGNING_NOTICE_FIRST";

	/**
	 * 感谢您对科研购的支持与信任，如有疑问请联系电话：4006-999-569
	 * ID后期修改
	 */
	public static final String ID_WECHAT_TEMPLATE_KYG_DELIVERY_REMINDER_FIRST = "WECHAT_TEMPLATE_KYG_DELIVERY_REMINDER_FIRST";

	/**
	 * 订单签收
	 */
	public static final String ID_TEMPLATE_ORDER_SIGNING_NOTICE_FIRST = "TEMPLATE_ORDER_SIGNING_NOTICE_FIRST";

	/**
	 * 订单支付
	 */
	public static final String WECHAT_TEMOLATE_ORDER_PAY_MIDDLE="WECHAT_TEMOLATE_ORDER_PAY_MIDDLE";

    /**
    * @Description: 尊敬的客户，您的订单已通过审核，请在14天内完成确认
    * @Param:
    * @return:
    * @Author: addis
    * @Date: 2019/9/26
    */
	public static final String THE_ORDER_IS_SUBJECT_TO_CUSTOMER_CONFIRMATION="THE_ORDER_IS_SUBJECT_TO_CUSTOMER_CONFIRMATION";


	/**
	* @Description: 尊敬的客户，您的订单已自动关闭
	* @Param:
	* @return:
	* @Author: addis
	* @Date: 2019/9/27
	*/
	public static final String THE_ORDER_CLOSED="THE_ORDER_CLOSED";
	/**
	 * 感谢您对医械购的支持与信任，如有疑问请联系：18651638763
	 * ID后期修改
	 */
	public static final String ID_WECHAT_TEMPLATE_REMARK = "WECHAT_TEMPLATE_TATOL_REMARK";

	/**
	 * 感谢您对科研购的支持与信任，如有疑问请联系电话：4006-999-569
	 * ID后期修改
	 */
	public static final String WECHAT_TEMPLATE_KYG_DELIVERY_REMINDER_REMARK = "WECHAT_TEMPLATE_KYG_DELIVERY_REMINDER_REMARK";

    /**
    * @Description: 感谢您对贝登的支持与信任，如有疑问请联系：4006-999-569
    * @Param:
    * @return:
    * @Author: addis
    * @Date: 2019/9/25
    */
	public static final String WECHAT_TEMPLATE_BEDENG_REMARK="WECHAT_TEMPLATE_BEDENG_REMARK";
  /**
  * @Description:  尊敬的客户，您的贝登精选会员申请已审核通过：
  * @Param:
  * @return:
  * @Author: addis
  * @Date: 2019/9/27
  */
	public static final String MEMBERSHIP_APPLICATION_HAS_BEEN_APPROVED="MEMBERSHIP_APPLICATION_HAS_BEEN_APPROVED";


	/**
	* @Description:尊敬的客户，您的贝登商城会员申请已审核通过
	* @Param:
	* @return:
	* @Author: addis
	* @Date: 2020/3/16
	*/
	public static final String WECHAT_TEMPLATE_VEDENG_VIP_FIRST="WECHAT_TEMPLATE_VEDENG_VIP_FIRST";

	/**
	 * 发票寄出 头
	 */
	public static final String ID_TEMPLATE_INVOICE_REMINDER_FIRST = "WECHAT_TEMPLATE_INVOICE_REMINDER_FIRST";


	/**
	* @Description: 快件类型（1、普发 2、直发）
	* @Param:
	* @return:
	* @Author: addis
	* @Date: 2019/11/8
	*/
	public static final Integer LOGISTICS_TYPE_1=1;


	/**
	 * @Description: 快递新增（用于rabbitmq区别类型）
	 * @Param:
	 * @return:
	 * @Author: addis
	 * @Date: 2019/11/8
	 */
	public static final Integer LOGISTICS_ADD_1=1;

	/**
	 * @Description: 快递修改（用于rabbitmq区别类型）
	 * @Param:
	 * @return:
	 * @Author: addis
	 * @Date: 2019/11/8
	 */
	public static final Integer LOGISTICS_UPDATE_2=2;


	/**
	 * @Description: 快递删除（用于rabbitmq区别类型）
	 * @Param:
	 * @return:
	 * @Author: addis
	 * @Date: 2019/11/8
	 */
	public static final Integer LOGISTICS_DELETE_3=3;
	/**
	 * 货齐发货
	 */
	public static final Integer NO_PART_DELIVER = 481;

	/**
	 * 分批发货
	 */
	public static final Integer PART_DELIVER = 482;

	public static final String OSS_DISPLAY = "display";
	public static final String OSS_DOWNLOAD = "download";

	//批次质检报告
	public static final Integer QUALITY_REPORT = 2000;

	public static final Integer QUALITY_REPORT_SALEORDER = 2001;

	public static final Integer QUALITY_REPORT_AFTERORDER = 2002;


	/**
	 * 客户性质-- 终端
	 */
	public static final Integer CUSTOMER_NATURE_TERMINAL = ID_466;
	/**
	 * 客户性质-- 分销
	 */
	public static final Integer CUSTOMER_NATURE_AGENT = ID_465;

	/**
	 * 中医诊所备案证
	 */
	public static final Integer CHINESE_MEDICAL_CLINIC = 2110;

	/**
	 * 动物诊疗许可证
	 */
	public static final Integer ANIMAL_CLINIC = 2111;

	/**
	 * 其他资质
	 */
	public static final Integer OTHER = 2112;

	/** 中医器械旧国标编号 */
	public static final Integer OLD_CHN_MED_NO = 1044;
	/** 中医器械新国标编号 */
	public static final Integer NEW_CHN_MED_NO = 216;
	/** 营业执照包含医疗器械状态*/
	public static final Integer CONTAIN_MEDICAL = 1;
	/**订单流升级-售后原因**/
	public static final Integer AFTER_SALES_REASON_UPGRADE = 3306;
	/**订单流升级-费用类型**/
	public static final Integer AFTER_SALES_COST_TYPE = 3399;

	public static final Integer ID_589 = 589;

	public static final String SUFFIX_IMG = "img";

	public static final String SUFFIX_PNG = "png";

	public static final String SUFFIX_GIF = "gif";

	public static final String SUFFIX_BMP = "bmp";

	public static final Integer ELECTRONIC_SIGNATURE = 3411;

	/**
	 * 联系人名片
	 */
	public static final Integer BUSINESS_CARDS = 3600;

	// 纳税人附件
	public static final Integer ID_3900 = 3900;
	/**
	 * GE商机来源渠道
	 */
	public static final Integer GE_BUSINESS_CHANCE_SOURCE = 3705;
	/**
	 * GE-医院等级规模
	 */
	public static final Integer GE_HOSPITAL_SIZE = 3717;
	/**
	 * GE-采购形式
	 */
	public static final Integer GE_BUY_TYPE = 3724;
	/**
	 * GE-项目阶段
	 */
	public static final Integer GE_PROJECT_PHASE = 3730;

	/**
	 * 询价行为 ：即时通讯
	 */
	public static final Integer ID_4056 = 4056;

	/**
	 * 渠道类型 ：im
	 */
	public static final Integer ID_4058 = 4058;
	/**
	 * 附件类型-采购直发同行单
	 */
	public static final Integer ID_4080 = 4080;

	/**
	 * 附件类型-采购普发同行单
	 */
	public static final Integer ID_4330 = 4330;

	/**
	 * 附件类型-质检报告
	 */
	public static final Integer ID_4331 = 4331;

	/**
	 * 售后安调结论
	 */
	public static final Integer ID_4084 = 4084;
	/**
	 * 附件类型-售后安调服务记录凭证
	 */
	public static final Integer ID_4092 = 4092;
	/**
	 * 附件类型-sku供应商授权书
	 */
	public static final Integer ID_4101 = 4101;
	/**
	 * 附件类型-销售订单销售申明
	 */
	public static final Integer ID_1201 = 1201;

	/**
	 * 付款申请类型-采购费用申请付款
	 */
	public static final Integer ID_4125 = 4125;

	/**
	 * 付款申请类型-采购单申请付款
	 */
	public static final Integer ID_517 = 517;

	/**
	 * 交易方式-支付宝
	 */
	public static final Integer ID_520 = 520;
	/**
	 * 交易方式-银行
	 */
	public static final Integer ID_521 = 521;
	/**
	 * 交易方式-微信
	 */
	public static final Integer ID_522 = 522;
	/**
	 * 入库单附件
	 */
	public static final Integer ID_4212 = 4212;
	/**
	 * 入库验收单
	 */
	public static final Integer ID_4211 = 4211;

	/**
	 * 备货原因
	 */
	public static final Integer ID_4260 = 4260;

	/**
	 * 出库复核单
	 */
	public static final Integer ID_4213 = 4213;
	/**
	 * 销售--财务结款-忽略选项父id
	 */
	public static final Integer ID_4286 = 4286;

	/**
	 * 样品出库申请类型
	 */
	public static final Integer ID_4220 = 4220;

	public static final Integer ID_10000 = 10000;
	
	/**
	 * 寄件类型
	 */
	public static final Integer ID_5901 = 5901;
	
	/**
	 * 承运部门
	 */
	public static final Integer ID_5902 = 5902;
}
