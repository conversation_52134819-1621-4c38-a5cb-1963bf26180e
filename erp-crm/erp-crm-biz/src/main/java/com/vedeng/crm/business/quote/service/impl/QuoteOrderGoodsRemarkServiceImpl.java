package com.vedeng.crm.business.quote.service.impl;

import com.vedeng.crm.business.quote.domain.entity.QuoteorderGoodsRemarkEntity;
import com.vedeng.crm.business.quote.mapper.QuoteorderGoodsRemarkMapper;
import com.vedeng.crm.business.quote.service.QuoteOrderGoodsRemarkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class QuoteOrderGoodsRemarkServiceImpl implements QuoteOrderGoodsRemarkService {

    @Autowired
    private QuoteorderGoodsRemarkMapper quoteorderGoodsRemarkMapper;

    @Override
    public void insert(QuoteorderGoodsRemarkEntity record) {
        quoteorderGoodsRemarkMapper.insertSelective(record);
    }

    @Override
    public List<QuoteorderGoodsRemarkEntity> queryList(Integer quoteorderGoodsId) {
        List<QuoteorderGoodsRemarkEntity> list = quoteorderGoodsRemarkMapper.selectByQuoteGoodsId(quoteorderGoodsId);
        return list;
    }

    @Override
    public List<QuoteorderGoodsRemarkEntity> queryListByQuoteId(Integer quoteorderId) {
        List<QuoteorderGoodsRemarkEntity> list = quoteorderGoodsRemarkMapper.selectByQuoteId(quoteorderId);
        return list;
    }
}
