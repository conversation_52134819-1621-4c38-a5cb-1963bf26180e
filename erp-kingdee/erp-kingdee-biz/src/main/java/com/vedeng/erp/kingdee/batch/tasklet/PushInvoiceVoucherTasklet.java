package com.vedeng.erp.kingdee.batch.tasklet;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.tasklet.BaseTasklet;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDetailDto;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceVoucherDto;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceDetailDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceVoucherMapper;
import com.vedeng.erp.kingdee.dto.KingDeeSalesVatPlainInvoiceDto;
import com.vedeng.erp.kingdee.dto.KingDeeSalesVatSpecialInvoiceDto;
import com.vedeng.erp.kingdee.dto.OutPutFeePlainInvoiceDto;
import com.vedeng.erp.kingdee.dto.OutPutFeeSpecialInvoiceDto;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 销售侧 存入金蝶发票是否推送金蝶
 * @date 2023/2/2 11:23
 */
@Service
@Slf4j
public class PushInvoiceVoucherTasklet extends BaseTasklet {

    @Autowired
    private BatchInvoiceDtoMapper batchInvoiceDtoMapper;
    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;
    @Autowired
    private BatchInvoiceVoucherMapper batchInvoiceVoucherMapper;
    @Autowired
    private BatchInvoiceDetailDtoMapper batchInvoiceDetailDtoMapper;


    @Override
    public void doExec(Map<String, Object> jobParameters) throws Exception {
        log.info("存入金蝶发票是否推送金蝶开始{}", JSON.toJSONString(jobParameters));

        String beginTime = (String) jobParameters.get("beginTime");
        String endTime = (String) jobParameters.get("endTime");

        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                // 红字有效
                .colorType(1)
                .isEnable(1)
                // 审核通过
                .validStatus(1)
                .companyId(1)
                // 销售开票
                .type(505)
                // 通过时间是当天的
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(beginTime).getTime())
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(endTime).getTime())
                ._pagesize(Integer.MAX_VALUE)
                ._skiprows(0)
                .build();
        List<BatchInvoiceDto> batchRedInvoiceDtoList = batchInvoiceDtoMapper.saleorderRedInvoiceFindByAll(batchInvoiceDto);
        this.doInvoiceVoucher(batchRedInvoiceDtoList);


        BatchInvoiceDto batchBlueInvoiceDto = BatchInvoiceDto
                .builder()
                .type(505)
                // 通过时间是当天的
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(beginTime).getTime())
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(endTime).getTime())
                ._pagesize(Integer.MAX_VALUE)
                ._skiprows(0)
                .build();
        List<BatchInvoiceDto> batchBlueInvoiceDtoList = batchInvoiceDtoMapper.findSaleOrderBlueInvoiceBatch(batchBlueInvoiceDto);
        this.doInvoiceVoucher(batchBlueInvoiceDtoList);

        BatchInvoiceDto batchAfterSaleInvoiceDto = BatchInvoiceDto
                .builder()
                // 蓝字有效
                .colorType(2)
                .isEnable(1)
                // 审核通过
                .validStatus(1)
                .companyId(1)
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()).getTime() :
                        DateUtil.beginOfDay(DateUtil.parseDateTime(beginTime)).getTime())
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()).getTime() :
                        DateUtil.endOfDay(DateUtil.parseDateTime(endTime)).getTime())
                //订单类型为'销售售后' SUBJECT_TYPE = 535
                ._pagesize(Integer.MAX_VALUE)
                ._skiprows(0)
                .build();
        List<BatchInvoiceDto> batchAfterSaleInvoiceDtoList = batchInvoiceDtoMapper.saleOrderAfterSaleReceiveFeeInvoice(batchAfterSaleInvoiceDto);
        this.doAfterSaleInvoiceVoucher(batchAfterSaleInvoiceDtoList);
    }


    /**
     * 处理发票推送
     *
     * @param batchInvoiceDtoList 发票集合
     */
    private void doInvoiceVoucher(List<BatchInvoiceDto> batchInvoiceDtoList) {
        log.info("处理存入金蝶发票数量{}", batchInvoiceDtoList.size());
        if (CollUtil.isNotEmpty(batchInvoiceDtoList)) {
            List<Integer> list = batchInvoiceDtoList.stream().map(BatchInvoiceDto::getInvoiceId).collect(Collectors.toList());
            Map<Integer, List<BatchInvoiceDetailDto>> listMap = batchInvoiceDetailDtoMapper.getInvoiceDetailByInvoiceIdList(list)
                    .stream().collect(Collectors.groupingBy(BatchInvoiceDetailDto::getInvoiceId));
            list = list.stream().filter(invoiceId -> {
                List<BatchInvoiceDetailDto> saleOrderInvoiceDetailList = listMap.get(invoiceId);
                // 费用商品
                long feeCount = saleOrderInvoiceDetailList.stream()
                        .filter(detailDto -> Convert.toBool(detailDto.getIsVirtureSku(), false)).count();
                boolean feeInvoiceExist = true;
                if (feeCount > 0) {
                    feeInvoiceExist = this.isFeeInvoiceExist(invoiceId);
                }
                // 实物商品
                long saleCount = saleOrderInvoiceDetailList.stream()
                        .filter(detailDto -> !Convert.toBool(detailDto.getIsVirtureSku(), false)).count();
                boolean saleInvoiceExist = true;
                if (saleCount > 0) {
                    saleInvoiceExist = this.isSaleInvoiceExist(invoiceId);
                }
                return feeInvoiceExist && saleInvoiceExist;
            }).collect(Collectors.toList());

            this.saveInvoiceVoucher(list);
        }
    }

    private void doAfterSaleInvoiceVoucher(List<BatchInvoiceDto> batchInvoiceDtoList) {
        if (CollUtil.isNotEmpty(batchInvoiceDtoList)) {
            List<Integer> list = batchInvoiceDtoList.stream().map(BatchInvoiceDto::getInvoiceId).collect(Collectors.toList());
            Map<Integer, List<BatchInvoiceDetailDto>> listMap = batchInvoiceDetailDtoMapper.getInvoiceDetailByInvoiceIdList(list)
                    .stream().collect(Collectors.groupingBy(BatchInvoiceDetailDto::getInvoiceId));
            list = list.stream().filter(invoiceId -> {
                List<BatchInvoiceDetailDto> saleOrderInvoiceDetailList = listMap.get(invoiceId);
                // 费用商品
                long feeCount = saleOrderInvoiceDetailList.stream()
                        .filter(detailDto -> Convert.toBool(detailDto.getIsVirtureSku(), false)).count();
                boolean feeInvoiceExist = true;
                if (feeCount > 0) {
                    feeInvoiceExist = this.isFeeInvoiceExist(invoiceId);
                }
                return feeInvoiceExist;
            }).collect(Collectors.toList());
            ;
            this.saveInvoiceVoucher(list);
        }
    }

    /**
     * 判断实物商品发票是否以推送
     *
     * @param invoiceId 发票id
     * @return 是否推送
     */
    private boolean isSaleInvoiceExist(Integer invoiceId) {
        KingDeeSalesVatPlainInvoiceDto kingDeeSalesVatPlainInvoiceDto = new KingDeeSalesVatPlainInvoiceDto();
        kingDeeSalesVatPlainInvoiceDto.setFQzokBddjtid(invoiceId.toString());
        Boolean salePlainExist = kingDeeBaseApi.isExist(kingDeeSalesVatPlainInvoiceDto);
        KingDeeSalesVatSpecialInvoiceDto kingDeeSalesVatSpecialInvoiceDto = new KingDeeSalesVatSpecialInvoiceDto();
        kingDeeSalesVatSpecialInvoiceDto.setFQzokBddjtid(invoiceId.toString());
        Boolean saleSpecialExist = kingDeeBaseApi.isExist(kingDeeSalesVatSpecialInvoiceDto);
        return salePlainExist || saleSpecialExist;
    }

    /**
     * 判断费用发票是否以推送
     *
     * @param invoiceId 发票id
     * @return 是否推送
     */
    private boolean isFeeInvoiceExist(Integer invoiceId) {
        OutPutFeePlainInvoiceDto feePlainInvoiceDtoQuery = new OutPutFeePlainInvoiceDto();
        feePlainInvoiceDtoQuery.setFQzokBddjtid(invoiceId.toString());
        Boolean feePlainExist = kingDeeBaseApi.isExist(feePlainInvoiceDtoQuery);
        OutPutFeeSpecialInvoiceDto feeSpecialInvoiceDtoQuery = new OutPutFeeSpecialInvoiceDto();
        feeSpecialInvoiceDtoQuery.setFQzokBddjtid(invoiceId.toString());
        Boolean feeSpecialExist = kingDeeBaseApi.isExist(feeSpecialInvoiceDtoQuery);
        return feePlainExist || feeSpecialExist;
    }

    /**
     * 存入凭证记录表
     *
     * @param invoiceIds invoiceIds
     */
    private void saveInvoiceVoucher(List<Integer> invoiceIds) {
        if (CollUtil.isEmpty(invoiceIds)) {
            return;
        }
        List<BatchInvoiceVoucherDto> batchInvoiceVoucherDtos = batchInvoiceVoucherMapper.findByInvoiceIdIn(invoiceIds);

        Map<String, BatchInvoiceVoucherDto> map = batchInvoiceVoucherDtos.stream()
                .collect(Collectors.toMap(BatchInvoiceVoucherDto::getInvoiceId, invoiceVoucherDto -> invoiceVoucherDto));
        List<BatchInvoiceVoucherDto> dtoList = new ArrayList<>();
        invoiceIds.forEach(id -> {
            if (ObjectUtil.isEmpty(map.get(id.toString()))) {
                BatchInvoiceVoucherDto insert = BatchInvoiceVoucherDto.builder()
                        .invoiceId(id.toString())
                        .addTime(new Date())
                        .modTime(new Date())
                        .creator(KingDeeConstant.ONE)
                        .updater(KingDeeConstant.ONE)
                        .creatorName(KingDeeConstant.ADMIN)
                        .updaterName(KingDeeConstant.ADMIN)
                        .voucherNo("")
                        .isDelete(KingDeeConstant.ZERO)
                        .build();
                dtoList.add(insert);
            }
        });

        log.info("存入凭证记录表{}", JSON.toJSONString(dtoList));
        if (!CollectionUtils.isEmpty(dtoList)) {
            batchInvoiceVoucherMapper.batchInsert(dtoList);
        }
    }
}
