<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="配置长途费" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<link rel="stylesheet"
      href="${pageContext.request.contextPath}/static/new/css/common/lib/input.css?rnd=${resourceVersionKey}">
<link rel="stylesheet"
      href="${pageContext.request.contextPath}/static/new/css/common/lib/suggestSelect.css?rnd=${resourceVersionKey}">
<script type="text/javascript" src='<%= basePath %>static/js/goods/brand/index.js?rnd=${resourceVersionKey}'></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/suggestSelect.js?rnd=${resourceVersionKey}"></script>
<div class="content">
    <div class="searchfunc">
        <form method="post" id="search" action="<%=basePath%>aftersales/order/longDistanceFeeList.do">
            <ul>
                <li>
                    <label class="infor_name">省：</label>
                    <select name="provinceId" class="input-middle f_left">
                        <option value="0" <c:if test="${longDistanceFreeSearchDto.provinceId eq 0}">selected="selected"</c:if>>全部
                        </option>
                        <c:forEach items="${provinceList}" var="province">
                            <option value="${province.regionId}" <c:if test="${longDistanceFreeSearchDto.provinceId eq province.regionId}">selected="selected"</c:if>>${province.regionName}
                            </option>
                        </c:forEach>
                    </select>
                </li>

                <li>
                    <label class="infor_name">市：</label>
                    <select name="cityId" class="input-middle f_left J_city">
                        <option value="0" <c:if test="${longDistanceFreeSearchDto.cityId eq 0}">selected="selected"</c:if>>全部
                        </option>
                        <c:forEach items="${cityList}" var="city">
                            <option value="${city.regionId}" <c:if test="${longDistanceFreeSearchDto.cityId eq city.regionId}">selected="selected"</c:if>>${city.regionName}
                            </option>
                        </c:forEach>
                    </select>
                </li>

                <li>
                    <label class="infor_name">区：</label>
                    <div class="fanwei-item" style="display: flex">
                        <div class="J-muiti-select fields-select-suggest"></div>
                        <input type="hidden" class="J-value J-RegionIds" name="regionIds" id="regionFlag"
                               value="${longDistanceFreeSearchDto.regionIds}">
                        <select class="J-select" name="" id="regionIds" >
                            <c:forEach items="${regionList}" var="region">
                                <option value="${region.regionId}">${region.regionName}</option>
                            </c:forEach>
                        </select>
                    </div>
                </li>
            </ul>
            <div class="tcenter">
                <span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();"
                      id="searchSpan">搜索</span>
                <span class="bt-small bg-light-blue bt-bg-style" onclick="reset();" id="resetButton">重置</span>
                <span class="bt-small bg-light-green bt-bg-style" onclick="batchAddLongDistanceFee()">批量导入长途费信息</span>
            </div>
        </form>
    </div>
    <div class='normal-list-page list-page'>
        <table
                class="table table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr class="sort">
                <th class="wid2">序号</th>
                <th class="wid5">省</th>
                <th class="wid5">市</th>
                <th class="wid6">区</th>
                <th class="wid10">长途费</th>
            </tr>
            </thead>
            <tbody class="brand">
            <c:if test="${not empty longDistanceFeeVos}">
                <c:forEach items="${longDistanceFeeVos }" var="longDistanceFeeVo" varStatus="status">
                    <tr>
                        <td>${status.count}</td>
                        <td>${longDistanceFeeVo.provinceStr}</td>
                        <td>${longDistanceFeeVo.cityStr}</td>
                        <td>${longDistanceFeeVo.regionStr}</td>
                        <td>
                            <c:choose>
                                <c:when test="${longDistanceFeeVo.longdistanceFeeType eq 1}">
                                    0.00
                                </c:when>
                                <c:when test="${longDistanceFeeVo.longdistanceFeeType eq 2}">
                                    ${longDistanceFeeVo.distanceFee}
                                </c:when>
                                <c:otherwise>
                                    另议
                                </c:otherwise>
                            </c:choose>
                        </td>
                    </tr>
                </c:forEach>
            </c:if>
            </tbody>
        </table>
        <c:if test="${empty longDistanceFeeVos}">
            <!-- 查询无结果弹出 -->
            <div class="noresult">查询无结果！请尝试使用其他搜索条件。</div>
        </c:if>
        <tags:page page="${page}"/>
    </div>
</div>
<script>
    $(function(){
        $("select[name='provinceId']").change(function(){
            checkLogin();
            var regionId = $(this).val();
            if(regionId > 0){
                $.ajax({
                    type : "POST",
                    url : page_url+"/system/region/getregion.do",
                    data :{'regionId':regionId},
                    dataType : 'json',
                    success : function(data) {
                        $option = "<option value='0'>请选择</option>";
                        $.each(data.listData,function(i,n){
                            $option += "<option value='"+data.listData[i]['regionId']+"'>"+data.listData[i]['regionName']+"</option>";
                        });
                        $("select[name='cityId'] option:gt(0)").remove();
                        $("select[name='zoneId'] option:gt(0)").remove();
                        $("#zoneId").val("0").trigger("change");
                        $("#cityId").val("0").trigger("change");

                        $("select[name='cityId']").html($option).trigger('change');
                        $("select[name='zonIde']").html("<option value='0'>请选择</option>");
                    },
                    error:function(data){
                        if(data.status ==1001){
                            layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                        }
                    }
                });
            }else if(regionId==0){
                $("select[name='cityId'] option:gt(0)").remove();
            }
        });

        var resetMultiSelect = function(option){
            $('.J-muiti-select').after('<select class="J-select" id="regionIds">' + (option || '') + '</select>');
            $('.J-muiti-select').after('<div class="J-muiti-select fields-select-suggest"></div>');
            $('.J-muiti-select').eq(0).remove();
            $('.J-muiti-select').each(function () {
                var data = getMultiData($(this));
                $(this).siblings('select,.select').remove();

                new SuggestSelect({
                    placeholder: '请选择',
                    wrap: $(this),
                    data: data,
                    multi: true,
                    multiAll: true,
                    input: $(this).siblings('.J-value')
                })
            });
        };

        $("select[name='cityId']").change(function(){
            checkLogin();
            var regionId = $(this).val();
            if(regionId > 0){
                $.ajax({
                    type : "POST",
                    url : page_url+"/system/region/getregion.do",
                    data :{'regionId':regionId},
                    dataType : 'json',
                    success : function(data) {
                        $option = "";
                        $.each(data.listData,function(i,n){
                            $option += "<option value='"+data.listData[i]['regionId']+"'>"+data.listData[i]['regionName']+"</option>";
                        });
                        //$("select[name='zoneId'] option:gt(0)").remove();
                        resetMultiSelect($option);

                    },
                    error:function(data){
                        if(data.status ==1001){
                            layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                        }
                    }
                });
            }else{
                resetMultiSelect();
            }
        });


       /*
        var resetRegionIds = function () {
            $('.J-RegionIds').each(function(i){
                $(this).find('.J-RegionIds').attr('name', 'skuAuthorizationDtos[' + i + '].regionsStr');
            })
        };*/
        //resetRegionIds();
        var getMultiData = function ($item) {
            var data = [];
            $item.siblings('select').find('option').each(function () {
                data.push({
                    label: $.trim($(this).html()),
                    value: $(this).val()
                })
            });
            return data;
        };

        $('.J-muiti-select').each(function () {
            var data = getMultiData($(this));
            $(this).siblings('select,.select').remove();

            new SuggestSelect({
                placeholder: '请选择',
                wrap: $(this),
                data: data,
                multi: true,
                multiAll: true,
                input: $(this).siblings('.J-value')
            })
        });

        $('#resetButton').click(function () {
            $("select[name='cityId'] option:gt(0)").remove();
            resetMultiSelect();
        })
    });

    function batchAddLongDistanceFee() {
        var open = layer.open({
            type: 1,
            title: '批量导入长途费信息',
            shadeClose: false,
            area : ['500px', '200px'],
            content: '<iframe style="width: calc(100% - 10px);height: calc(100% - 10px); border: 0;" src="/aftersales/order/batchSaveLongDistanceFeeInit.do"></iframe>',
            success: function(layero, index){

            }
        });
    }
</script>
<%@ include file="../../common/footer.jsp" %>
