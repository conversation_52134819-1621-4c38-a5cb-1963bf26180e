Vue.component('ui-checkbox', {
    template: `
        <div
            class="vd-ui-checkbox-item"
            :class="{
                'vd-ui-checkbox-item-checked': currentChecked,
                'vd-ui-checkbox-item-disabled': disabled,
                'vd-ui-checkbox-item-progress': isSelectedAll && onProgress === 2,
            }"
            @click="handlerClick(!currentChecked)"
            :title="label"
        >
            <div class="vd-ui-checkbox-inner">
                <div class="vd-ui-checkbox-icon">
                    <div class="vd-ui-checkbox-icon-selected2">
                        <i class="vd-ui_icon icon-selected2"></i>
                    </div>
                </div>
                <span v-html="label"></span>
            </div>
        </div>
    `,
    props: {
        label: {
            type: String,
            default: "",
        },
        checked: {
            type: Boolean,
            default: false,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        onProgress: {
            type: Number,
            default: 1,
        },
        isSelectedAll: {
            type: <PERSON>olean,
            default: false,
        },
    },
    data() {
        return {
            currentChecked: false,
        };
    },
    mounted() {
        this.currentChecked = this.checked;
    },
    watch: {
        onProgress() {
            this.currentChecked = this.onProgress === 3;
            this.$emit("update:checked", this.onProgress === 3);
        },
        checked() {
            this.currentChecked = this.checked;
        },
    },
    methods: {
        handlerClick() {
            if (this.isSelectedAll) {
                if (this.onProgress === 3) {
                    this.$emit("update:onProgress", 1);
                } else {
                    this.$emit("update:onProgress", 3);
                }
            }
            
            if (!this.disabled) {
                this.currentChecked = !this.currentChecked;
                this.$emit("update:checked", this.currentChecked);
                this.$emit("change", this.currentChecked);
            }
        },
    },
})