package com.vedeng.erp.buyorder.mapper;

import com.vedeng.erp.buyorder.domain.entity.GeBusinessChanceDetailRecord;

public interface GeBusinessChanceDetailRecordMapper {
    int deleteByPrimaryKey(Integer geBussinessChanceDetailRecordId);

    int insert(GeBusinessChanceDetailRecord record);

    int insertSelective(GeBusinessChanceDetailRecord record);

    GeBusinessChanceDetailRecord selectByPrimaryKey(Integer geBussinessChanceDetailRecordId);

    int updateByPrimaryKeySelective(GeBusinessChanceDetailRecord record);

    int updateByPrimaryKey(GeBusinessChanceDetailRecord record);

    /**
     *
     * @param geBussinessChanceDetailId
     * @return
     */
    GeBusinessChanceDetailRecord queryByGeBussinessChanceDetailId(Integer geBussinessChanceDetailId);
}