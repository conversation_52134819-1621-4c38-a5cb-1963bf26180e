package com.vedeng.erp.settlement.mapper;

import com.vedeng.erp.finance.dto.PayApplyPayBankResultDto;
import com.vedeng.erp.settlement.domain.entity.PayVedengBankEntity;
import java.util.List;

import com.vedeng.erp.finance.dto.PayVedengBankDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2022/8/29 14:05
 **/
@Repository
public interface PayVedengBankMapper {
    /**
     * delete by primary key
     * @param payVedengBankId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer payVedengBankId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(PayVedengBankEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(PayVedengBankEntity record);

    /**
     * select by primary key
     * @param payVedengBankId primary key
     * @return object by primary key
     */
    PayVedengBankEntity selectByPrimaryKey(Integer payVedengBankId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(PayVedengBankEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(PayVedengBankEntity record);

    int batchInsert(@Param("list") List<PayVedengBankEntity> list);

    /**
     * <AUTHOR>
     * @desc 根据id查询财务付款账号信息
     * @param payVedengBankId
     * @return
     */
    PayVedengBankDto queryInfoByPayVedengBankId(Integer payVedengBankId);

    /**
     * 根据银行账号查锡尼希
     * @param payBankNo 账号
     * @return PayVedengBankDto
     */
    PayVedengBankDto queryInfoByPayBankNo(String payBankNo);

    String getKingDeeBankCodeByBankNo(String payBankNo);

    /**
     * 查询付款银行
     * @param buyOrderNo
     * @return
     */
    List<PayApplyPayBankResultDto> queryPayBankByBuyOrder(String buyOrderNo);
}