package com.newtask;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.finance.dao.HxInvoiceMapper;
import com.vedeng.finance.dao.InvoiceMapper;
import com.vedeng.finance.enums.HxInvoiceStatusEnum;
import com.vedeng.finance.model.HxInvoice;
import com.vedeng.finance.model.Invoice;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: daniel
 * @Date: 2020/11/23 23 26
 * @Description:
 */
@Component
@JobHandler("hxInvoiceInfoCheck")
public class HxInvoiceInfoCheckHandler extends AbstractJobHandler {
    private final static Logger logger = LoggerFactory.getLogger(HxInvoiceInfoCheckHandler.class);

    @Autowired
    private HxInvoiceMapper hxInvoiceMapper;

    @Autowired
    private InvoiceMapper invoiceMapper;

    @Override
    public ReturnT<String> doExecute(String params) throws Exception {
        hxInvoiceMapper.getValidHxInvoice().parallelStream()
                .forEach(item -> {
                    //校准HxInvoice的状态，并补充Invoice表里的Hx_INVOICE_ID
                    List<Invoice> invoices = invoiceMapper.getValidInvoiceByCondition(item.getInvoiceNum(), item.getInvoiceCode())
                            .stream().filter(invoice -> !ErpConst.TWO.equals(invoice.getValidStatus())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(invoices)) {
                        return;
                    }

                    BigDecimal hasInputAmount = invoices.stream().map(Invoice::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

                    HxInvoice toUpdateHxInvoice = new HxInvoice();
                    toUpdateHxInvoice.setHxInvoiceId(item.getHxInvoiceId());
                    toUpdateHxInvoice.setInvoiceStatus(hasInputAmount.add(BigDecimal.ONE).compareTo(item.getAmount()) > -1 ?
                            HxInvoiceStatusEnum.RECORDED.getStatus() : HxInvoiceStatusEnum.WAIT_RECORD.getStatus());
                    logger.info("hxInvoiceInfoCheck updateInvoice invoice:{}", JSON.toJSONString(toUpdateHxInvoice));
                    hxInvoiceMapper.updateByPrimaryKeySelective(toUpdateHxInvoice);
                    invoices.forEach(var -> invoiceMapper.updateHxInvoiceIdByPrimaryKey(var.getInvoiceId(), item.getHxInvoiceId()));
                });
        return ReturnT.SUCCESS;
    }
}
