package com.vedeng.erp.express.service;

import com.vedeng.erp.express.dto.ExpressCommunicationBindDto;
import com.vedeng.erp.express.dto.SaleOrderCommunicationDto;
import com.vedeng.erp.express.dto.SaleOrderExpressDto;

import java.util.List;

public interface SaleOrderExpressApiService {

    List<SaleOrderExpressDto> querySaleDirectExpress(Integer saleOrderId);

    void bindCommunicationIdAndSignFor(List<ExpressCommunicationBindDto> list,Integer currentUserId);

    void bindCommunicationId(ExpressCommunicationBindDto expressCommunicationBindDto,Integer currentUserId) throws Exception;

    List<Integer> getExpressCommunicationId(Integer expressId);

    SaleOrderCommunicationDto getSaleOrderCommunication(Integer communicationRecordId);
}
