package com.vedeng.order.model.vo;

/**
 * <AUTHOR>
 * @description: 记录关联在途VB单窗口的检索值
 * @date 2021/4/13 9:54
 */
public class BuyorderVBVo {
    private Integer buyorderGoodsId;
    private String buyorderNo;
    private String createrName;
    private String addTime;
    private Integer goodSum;
    private Integer goodNumAble;

    public Integer getCanSelect() {
        return canSelect;
    }

    public void setCanSelect(Integer canSelect) {
        this.canSelect = canSelect;
    }

    //0不可被选择，1可以被选择
    private Integer canSelect;



    public Integer getBuyorderGoodsId() {
        return buyorderGoodsId;
    }

    public void setBuyorderGoodsId(Integer buyorderGoodsId) {
        this.buyorderGoodsId = buyorderGoodsId;
    }

    public String getBuyorderNo() {
        return buyorderNo;
    }

    public void setBuyorderNo(String buyorderNo) {
        this.buyorderNo = buyorderNo;
    }

    public String getCreaterName() {
        return createrName;
    }

    public void setCreaterName(String createrName) {
        this.createrName = createrName;
    }

    public Integer getGoodSum() {
        return goodSum;
    }

    public void setGoodSum(Integer goodSum) {
        this.goodSum = goodSum;
    }

    public Integer getGoodNumAble() {
        return goodNumAble;
    }

    public void setGoodNumAble(Integer goodNumAble) {
        this.goodNumAble = goodNumAble;
    }

    public String getAddTime() {
        return addTime;
    }

    public void setAddTime(String addTime) {
        this.addTime = addTime;
    }
}
