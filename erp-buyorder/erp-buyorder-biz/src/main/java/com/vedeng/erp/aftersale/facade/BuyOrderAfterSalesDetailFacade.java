package com.vedeng.erp.aftersale.facade;

import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.authorization.model.User;
import org.springframework.web.servlet.ModelAndView;

public interface BuyOrderAfterSalesDetailFacade {

    /**
     * 采购售后详情页
     * @param mav
     * @param currentUser
     * @param afterSalesVo
     */
    void getBuyOrderDetail(ModelAndView mav, User currentUser, AfterSalesVo afterSalesVo);
}
