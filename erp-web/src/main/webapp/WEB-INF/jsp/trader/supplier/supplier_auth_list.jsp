<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="授权书列表" scope="application" />
<%@ include file="../../common/common.jsp"%>
<%@ include file="supplier_tag.jsp"%>
<link rel="stylesheet"
      href="${pageContext.request.contextPath}/static/new/css/common/global.css">
<link rel="stylesheet" href="<%=basePath%>static/css/supplier/supplier_registration.css?rnd=${resourceVersionKey}" />
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/pages/goods/vgoods/spu/spu_view.css?rnd=${resourceVersionKey}">

<div class="main-container">
    <input id="traderId" type="hidden" value="${traderId}">
    <input id="traderSupplierId" type="hidden" value="${traderSupplierId}">
    <div style="font-size: 15px">
        <span>供应商名称：${traderSupplier.trader.traderName}</span>
        <div class="title-click nobor  pop-new-data"
             layerParams='{"width":"850px","height":"560px","title":"新增授权书","link":"/trader/supply/auth.do?traderSupplierId=${traderSupplierId}"}'>
            新增授权书
        </div>
    </div>
    <div class="fixdiv">
        <div class="superdiv">
            <table
                    class="table table-bordered table-striped table-condensed table-centered">
                <thead>
                <tr>
                    <th style="width: 50px">序号</th>
                    <th>授权书有效期</th>
                    <th style="width: 490px">授权书附件</th>
                    <th style="width: 100px">授权类型</th>
                    <th>授权品牌</th>
                    <th>关联商品</th>
                    <th>操作</th>
                    <th>审核状态</th>
                    <th>审核人</th>
                </tr>
                </thead>
                <tbody class="employeestate">
                <c:if test="${not empty skuSupplyAuthVoList}">
                    <c:forEach items="${skuSupplyAuthVoList}" var="item" varStatus="authstatus">
                        <tr class="p">
                            <input type="hidden" class="sku-supply-auth-id" id="skuSupplyAuth_${authstatus.count}" value="${item.skuSupplyAuthId}">
                            <%--序号--%>
                            <td>${authstatus.count}</td>
                            <%--授权书有效期--%>
                            <td>
                                <fmt:formatDate value="${item.validStartTime}" type="date"/>
                                &nbsp;&nbsp;-&nbsp;&nbsp;
                                <fmt:formatDate value="${item.validEndTime}" type="date"/>
                            </td>
                            <%--授权书附件--%>
                            <td>
                                <div>图上传支持上传不超过5M的JPG、PNG、JPEG、BMP格式。</div>
                                <c:forEach items="${item.pictureAttachmentJsonStrings}" var="pictureAttachmentJsonString">
                                    <div class="form-col">
                                        <div class="J-upload"></div>
                                        <input type="hidden" class="J-upload-data" value='${pictureAttachmentJsonString}'>
                                        <div class="form-fields-tip">-单行最多上传五张，换行上传需刷新页面</div>
                                        <div class="feedback-block" wrapfor="upload0"></div>
                                        <div class="feedback-block J-upload-error">
                                            <label class="error" for="" style="display: none;"></label>
                                        </div>
                                    </div>
                                </c:forEach>
                                <br>
                                <div style="text-align: left">
                                    <c:forEach items="${item.fileAttachment}" var="fileInfo" varStatus="fileStatus">
                                        <span class="addtitle" style="color: #2E8AE6"
                                              tabTitle='{"num":"supplier_auth_${fileStatus.count }",
                                            "link":"${oss_http}${fileInfo.domain}${fileInfo.uri}","title":"附件预览"}'>授权书文件${fileStatus.count}</span>
                                        <br>
                                    </c:forEach>
                                </div>
                            </td>
                            <%--授权类型--%>
                            <td>
                                <c:if test="${item.authType eq 1}">独家代理合作模式</c:if>
                                <c:if test="${item.authType eq 2}">授权经销合作模式</c:if>
                                <c:if test="${item.authType eq 3}">零星采购模式</c:if>
                                <c:if test="${item.authType eq 4}">授权经销商产品</c:if>
                                <c:if test="${item.authType eq 5}">非授权经销产品</c:if>
                                <c:if test="${item.authType eq 6}">自有产品加工模式</c:if>
                            </td>
                            <%--授权品牌--%>
                            <td>
                                ${item.brandNameString}
                            </td>
                            <%--关联商品--%>
                            <td>
                                <div class="status-list">
                                    <c:forEach items="${item.skuSupplyAuthDetails}" var="detail" varStatus="detailStatus">
                                        <c:if test="${detailStatus.count==6}">
                                            <div class="status-more J-optional-more" id="hideSkuInfo${authstatus.count}">
                                        </c:if>
                                        <div class="status-cnt">
                                            <div>
                                                ${detail.skuNo}${detail.skuName}
                                                <br>
                                            </div>
                                        </div>
                                        <c:if test="${item.skuSupplyAuthDetails.size() == detailStatus.count && detailStatus.count>5 }">
                                            </div>
                                        </c:if>
                                    </c:forEach>
                                </div>
                                <c:if test="${item.skuSupplyAuthDetails.size() > 5}">
                                    <div class="detail-optional J-toggle-show">
                                        <div id="showMoreSpan${authstatus.count}">
                                            <span class="toggle-txt J-more" onclick="show(${authstatus.count})">展开更多<i class="vd-icon icon-down" style="background: none"></i></span>
                                        </div>
                                        <div id="hideMoreSpan${authstatus.count}" style="display: none;">
                                            <span class="toggle-txt J-less" onclick="hide(${authstatus.count})">收起<i class="vd-icon icon-up" style="background: none"></i></span>
                                        </div>
                                    </div>
                                </c:if>
                            </td>
                            <td>
                                <div class="title-click nobor  pop-new-data"
                                     layerParams='{"width":"850px","height":"520px","title":"删除关联商品","link":"deleteRelatedSku.do?skuSupplyAuthId=${item.skuSupplyAuthId}"}'>
                                    删除关联商品
                                </div>
                                <div class="title-click nobor  pop-new-data"
                                     layerParams='{"width":"850px","height":"600px","title":"新增关联商品","link":"addRelatedSku.do?skuSupplyAuthId=${item.skuSupplyAuthId}"}'>
                                    新增关联商品
                                </div>
                                <div class="title-click"  onclick="delSkuSupplyAuth(${authstatus.count})">删除</div>
                            </td>
                            <td>
                                审核通过
                            </td>
                            <td>njadmin</td>
                        </tr>
                    </c:forEach>
                </c:if>
                </tbody>
            </table>
            <c:if test="${empty skuSupplyAuthVoList }">
                <!-- 查询无结果弹出 -->
                <div class="noresult">查询无结果！请尝试使用其他搜索条件。</div>
            </c:if>
        </div>
    </div>
    <tags:page page="${page}" />
</div>
<script src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/util.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/upload.js?rnd=${resourceVersionKey}"></script>

<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/supplier/supplier_auth_list.js?rnd=${resourceVersionKey}"></script>