<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.DwhTraderTagCpmErpMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.DwhTraderTagCpmErpEntity">
    <!--@mbg.generated-->
    <!--@Table DWH_TRADER_TAG_CPM_ERP-->
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_CUSTOMER_ID" jdbcType="INTEGER" property="traderCustomerId" />
    <result column="INSTITUTION_NATURE" jdbcType="VARCHAR" property="institutionNature" />
    <result column="TRADER_CUSTOMER_MARKETING_TYPE" jdbcType="VARCHAR" property="traderCustomerMarketingType" />
    <result column="INSTITUTION_LEVEL" jdbcType="VARCHAR" property="institutionLevel" />
    <result column="INSTITUTION_TYPE" jdbcType="VARCHAR" property="institutionType" />
    <result column="INSTITUTION_TYPE_CHILD" jdbcType="VARCHAR" property="institutionTypeChild" />
    <result column="MAIN_L3_CATEGORY_IDS" jdbcType="VARCHAR" property="mainL3CategoryIds" />
    <result column="ETL_DAY" jdbcType="DATE" property="etlDay" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    TRADER_ID, TRADER_CUSTOMER_ID, INSTITUTION_NATURE, TRADER_CUSTOMER_MARKETING_TYPE, 
    INSTITUTION_LEVEL, INSTITUTION_TYPE, INSTITUTION_TYPE_CHILD, MAIN_L3_CATEGORY_IDS, 
    ETL_DAY
  </sql>
  <insert id="insert" parameterType="com.vedeng.erp.trader.domain.entity.DwhTraderTagCpmErpEntity">
    <!--@mbg.generated-->
    insert into DWH_TRADER_TAG_CPM_ERP (TRADER_ID, TRADER_CUSTOMER_ID, INSTITUTION_NATURE, 
      TRADER_CUSTOMER_MARKETING_TYPE, INSTITUTION_LEVEL, 
      INSTITUTION_TYPE, INSTITUTION_TYPE_CHILD, 
      MAIN_L3_CATEGORY_IDS, ETL_DAY)
    values (#{traderId,jdbcType=INTEGER}, #{traderCustomerId,jdbcType=INTEGER}, #{institutionNature,jdbcType=VARCHAR}, 
      #{traderCustomerMarketingType,jdbcType=VARCHAR}, #{institutionLevel,jdbcType=VARCHAR}, 
      #{institutionType,jdbcType=VARCHAR}, #{institutionTypeChild,jdbcType=VARCHAR}, 
      #{mainL3CategoryIds,jdbcType=VARCHAR}, #{etlDay,jdbcType=DATE})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.erp.trader.domain.entity.DwhTraderTagCpmErpEntity">
    <!--@mbg.generated-->
    insert into DWH_TRADER_TAG_CPM_ERP
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderCustomerId != null">
        TRADER_CUSTOMER_ID,
      </if>
      <if test="institutionNature != null and institutionNature != ''">
        INSTITUTION_NATURE,
      </if>
      <if test="traderCustomerMarketingType != null and traderCustomerMarketingType != ''">
        TRADER_CUSTOMER_MARKETING_TYPE,
      </if>
      <if test="institutionLevel != null and institutionLevel != ''">
        INSTITUTION_LEVEL,
      </if>
      <if test="institutionType != null and institutionType != ''">
        INSTITUTION_TYPE,
      </if>
      <if test="institutionTypeChild != null and institutionTypeChild != ''">
        INSTITUTION_TYPE_CHILD,
      </if>
      <if test="mainL3CategoryIds != null and mainL3CategoryIds != ''">
        MAIN_L3_CATEGORY_IDS,
      </if>
      <if test="etlDay != null">
        ETL_DAY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderCustomerId != null">
        #{traderCustomerId,jdbcType=INTEGER},
      </if>
      <if test="institutionNature != null and institutionNature != ''">
        #{institutionNature,jdbcType=VARCHAR},
      </if>
      <if test="traderCustomerMarketingType != null and traderCustomerMarketingType != ''">
        #{traderCustomerMarketingType,jdbcType=VARCHAR},
      </if>
      <if test="institutionLevel != null and institutionLevel != ''">
        #{institutionLevel,jdbcType=VARCHAR},
      </if>
      <if test="institutionType != null and institutionType != ''">
        #{institutionType,jdbcType=VARCHAR},
      </if>
      <if test="institutionTypeChild != null and institutionTypeChild != ''">
        #{institutionTypeChild,jdbcType=VARCHAR},
      </if>
      <if test="mainL3CategoryIds != null and mainL3CategoryIds != ''">
        #{mainL3CategoryIds,jdbcType=VARCHAR},
      </if>
      <if test="etlDay != null">
        #{etlDay,jdbcType=DATE},
      </if>
    </trim>
  </insert>

<!--auto generated by MybatisCodeHelper on 2023-08-17-->
  <select id="findByAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from DWH_TRADER_TAG_CPM_ERP
        <where>
            <if test="traderId != null">
                and TRADER_ID=#{traderId,jdbcType=INTEGER}
            </if>
            <if test="traderCustomerId != null">
                and TRADER_CUSTOMER_ID=#{traderCustomerId,jdbcType=INTEGER}
            </if>
            <if test="institutionNature != null and institutionNature != ''">
                and INSTITUTION_NATURE=#{institutionNature,jdbcType=VARCHAR}
            </if>
            <if test="traderCustomerMarketingType != null and traderCustomerMarketingType != ''">
                and TRADER_CUSTOMER_MARKETING_TYPE=#{traderCustomerMarketingType,jdbcType=VARCHAR}
            </if>
            <if test="institutionLevel != null and institutionLevel != ''">
                and INSTITUTION_LEVEL=#{institutionLevel,jdbcType=VARCHAR}
            </if>
            <if test="institutionType != null and institutionType != ''">
                and INSTITUTION_TYPE=#{institutionType,jdbcType=VARCHAR}
            </if>
            <if test="institutionTypeChild != null and institutionTypeChild != ''">
                and INSTITUTION_TYPE_CHILD=#{institutionTypeChild,jdbcType=VARCHAR}
            </if>
            <if test="mainL3CategoryIds != null and mainL3CategoryIds != ''">
                and MAIN_L3_CATEGORY_IDS=#{mainL3CategoryIds,jdbcType=VARCHAR}
            </if>
            <if test="etlDay != null">
                and ETL_DAY=#{etlDay,jdbcType=DATE}
            </if>
        </where>
    </select>
</mapper>