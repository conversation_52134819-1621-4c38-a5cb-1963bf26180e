package com.wms.inventorytransfer.model.vo;


import com.wms.inventorytransfer.model.po.InventoryTransferDetailPO;

/**
 * @describe 库存转移单详情视图对象
 * <AUTHOR>
 * @date 2020/7/10 13:54:12
 */
public class InventoryTransferDetailVO extends InventoryTransferDetailPO {
    /**
     * 来源逻辑仓
     */
    private String fromWarehoseStr;

    /**
     * 目标逻辑仓
     */
    private String toWarehouseStr;

    public String getFromWarehoseStr() {
        return fromWarehoseStr;
    }

    public void setFromWarehoseStr(String fromWarehoseStr) {
        this.fromWarehoseStr = fromWarehoseStr;
    }

    public String getToWarehouseStr() {
        return toWarehouseStr;
    }

    public void setToWarehouseStr(String toWarehouseStr) {
        this.toWarehouseStr = toWarehouseStr;
    }
}
