package com.vedeng.erp.kingdee.batch.writer;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.batch.repository.BatchMaterialFinanceDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeeMaterialDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeMaterialCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeMaterialConvertor;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeMaterialMapper;
import com.vedeng.erp.kingdee.service.KingDeeMaterialApiService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/5 10:41
 */
@Service
@Slf4j
public class BatchMaterialWriter extends BaseWriter<KingDeeMaterialDto> {


    @Autowired
    private BatchMaterialFinanceDtoMapper batchMaterialFinanceDtoMapper;

    @Autowired
    private KingDeeMaterialApiService kingDeeMaterialApiService;

    @Override
    public void doWrite(KingDeeMaterialDto item, JobParameters params, ExecutionContext stepContext) throws Exception {

        log.info("审计物料信息推送：{}", JSON.toJSONString(item));
        item.setKingDeeBizEnums(KingDeeBizEnums.updateMaterial);
        kingDeeMaterialApiService.register(item, true);
        batchMaterialFinanceDtoMapper.updateKingDeePushStatus(item.getGoodsFinanceId());
    }
}