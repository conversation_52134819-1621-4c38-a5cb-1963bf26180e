function exportCustomerAccount(){
	checkLogin();

	//location.href = page_url + '/report/finance/exportCustomerAccountNew.do?' + $("#search").serialize();
	var traderName = $("#traderName").val();
	var traderUserId = $('#traderUserId option:selected').text();
	if (traderUserId=="全部"){
		traderUserId="";
	}
	var overdueState = $('#overdueState option:selected').text();
	if (overdueState=="全部"){
		overdueState="";
	}
	var saleorderNo = $("#saleorderNo").val();
	var startTime = $("#startTime").val();
	var endTime = $("#endTime").val();
	var billType = $('#billType option:selected').text();
	if (billType=="全部"){
		billType="";
	}

	var o=$('#departmentInfo option:selected');
	var departmentInfo="";
	for(var i=0;i<o.length;i++){
		if(o[i].selected){
			if (i==o.length-1){
				departmentInfo+=o[i].text;
			} else{
				departmentInfo+=o[i].text+',';
			}

		}
	}
	debugger
	//var departmentInfo = $('#departmentInfo option:selected').text();
	departmentInfo = encodeURIComponent(departmentInfo);
	var url= $("#ezDomain").val()+ "/ezlist/export/export.html?pageId="+$("#ezId").val()+"&exportType=2&pageName=客户账期记录导出&DEFAULT_PAGE_SIZE=10&CLICK_NUM_=0&TRADER_NAME="+traderName+"&USERNAME="+traderUserId+"&BILL_PERIOD_TYPE="+billType+"&SALEORDER_NO="+saleorderNo+"&VALID_TIME_START="+startTime+"&VALID_TIME_END="+endTime+"&OVER_STATUS="+overdueState;
	url = encodeURI(url) + "&ORG_NAME="+departmentInfo+"&more_less_flag=1&";
	location.href = url;
	//http://ez.ivedeng.com/ezlist/export/export.html?pageId=320&exportType=2&pageName=%E5%AE%A2%E6%88%B7%E8%B4%A6%E6%9C%9F%E8%AE%B0%E5%BD%95%E5%AF%BC%E5%87%BA&DEFAULT_PAGE_SIZE=10&CLICK_NUM_=0&TRADER_NAME=&USERNAME=%E6%B5%8B%E8%AF%95quinn&ORG_NAME=&BILL_PERIOD_TYPE=&SALEORDER_NO=&VALID_TIME_START=2021-08-10&VALID_TIME_END=2021-08-17&OVER_STATUS=&more_less_flag=1&
	//location.href ="http://ez.ivedeng.com/ezlist/list/list.html?pageId=319";
}

function exportSupplierAccount(){
	checkLogin();
	location.href = page_url + '/report/finance/exportSupplierAccount.do?' + $("#search").serialize();
}

function reset(){
	$("form").find("input[type='text']").val('');
	$("form").find(".hidden-reset").val('');
	$.each($("form select"),function(i,n){
		$(this).children("option:first").prop("selected",true);
	});
	var array = new Array();
	$("#departmentInfo").select2().val(array).trigger("change");
}

$(function () {
	$("#departmentInfo").select2();

	if ($("#departmentInfoList").val()){
		var arr = new Array();
		arr = $("#departmentInfoList").val().replace('[','').replace(']','').replace(/\s/g,'').split(',');
		$("#departmentInfo").select2().val(arr).trigger("change");
		$(".select2-container-multi").css("width","178px");
	}

})