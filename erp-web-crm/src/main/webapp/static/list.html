<!DOCTYPE html>

<html>

<head>
    <title>CRM-线索</title>
    <link rel="stylesheet" href="./css/common/common.css">
    <link rel="stylesheet" href="./ui/ui.css">
    <link rel="stylesheet" href="./css/pages/businessLeads.css">
</head>

<body>
    <input type="hidden" id="isHiddenLayout" value="0">
    <div class="page-wrap" id="page-container">
        <page-header></page-header>
        <div class="page-container">
            <page-side></page-side>
            <div class="page-main">
                <div class="list-title">线索</div>
                <div class="list-top-option">
                    <ui-button type="primary" icon="icon-add">新建</ui-button>
                </div>
                <ui-search-container ref="listContainer" list-name="01" :default-tab="defaultTab" :headers="tableHeaders" url="http://***********:7777/crm/businessLeads/profile/list" :search-params="searchParams">
                    <template v-slot:filter-list>
                        <ui-search-item label="线索编号" :lock="true">
                            <ui-input v-model="searchParams.leadsNo"></ui-input>
                        </ui-search-item>
                        <ui-search-item label="线索状态">
                            <ui-select :data="leadsStatusList"  multiple-type="fixed" placeholder="全部" v-model="searchParams.followStatusList" clearable></ui-select>
                        </ui-search-item>
                        <ui-search-item label="归属销售">
                            <ui-select :remote="true" :avatar="true" multiple-type="fixed" :default-multi="searchParams.channelMultiItems" placeholder="全部" v-model="searchParams.channelIdList" clearable :remote-info="belongerListRemoteInfo" @change="remoteSelectChange('channelMultiItems', $event)"></ui-select>
                        </ui-search-item>
                        <ui-search-item label="协作人">
                            <ui-select :remote="true" :avatar="true" multiple-type="fixed" :default-multi="searchParams.shareUserMultiItems" @change="remoteSelectChange('shareUserMultiItems', $event)" placeholder="全部" v-model="searchParams.shareUserIdList" clearable :remote-info="shareUserListRemoteInfo"></ui-select>
                        </ui-search-item>
                        <ui-search-item label="联系人">
                            <ui-input v-model="searchParams.contact"></ui-input>
                        </ui-search-item>
                        <ui-search-item label="手机号码">
                            <ui-input v-model="searchParams.phone"></ui-input>
                        </ui-search-item>
                        <ui-search-item label="电话">
                            <ui-input v-model="searchParams.telephone"></ui-input>
                        </ui-search-item>
                        <ui-search-item label="客户名称">
                            <ui-input v-model="searchParams.traderName"></ui-input>
                        </ui-search-item>
                        <ui-search-item label="创建时间">
                            <ui-date-picker
                                v-model="searchParams.startAddTime"
                                placeholder="请选择日期"
                            ></ui-date-picker>
                            <!-- <ui-input v-model="startAddTime"></ui-input> -->
                            <!-- <ui-input v-model="endAddTime"></ui-input> -->
                        </ui-search-item>
                        <ui-search-item label="分配时间">
                            <ui-date-picker
                                v-model="searchParams.startAssignTime"
                                placeholder="请选择日期"
                            ></ui-date-picker>
                            <!-- <ui-input v-model="startAssignTime"></ui-input> -->
                            <!-- <ui-input v-model="endAssignTime"></ui-input> -->
                        </ui-search-item>
                        <ui-search-item label="下次跟进日期">
                            <ui-date-picker
                                v-model="searchParams.nextCommunicationDateStart"
                                placeholder="请选择日期"
                            ></ui-date-picker>
                            <!-- nextCommunicationDateEnd -->
                        </ui-search-item>
                        <ui-search-item label="最近跟进日期">
                            <ui-date-picker
                                v-model="searchParams.startLastCommunicationTime"
                                type="daterange"
                                start-placeholder="起始时间"
                                end-placeholder="截止时间"
                            ></ui-date-picker>
                            <!-- endLastCommunicationTime -->
                        </ui-search-item>
                        <ui-search-item label="线索类型">
                            <ui-select :first-remote="true" :remote-info="clueTypeRemoteInfo"  multiple-type="fixed" placeholder="全部" v-model="searchParams.clueTypeList" clearable></ui-select>
                        </ui-search-item>
                        <ui-search-item label="线索渠道">
                            <ui-select :data="selectList1"></ui-select>
                        </ui-search-item>
                        <ui-search-item label="三级分类">
                            <ui-input v-model="searchParams.content"></ui-input>
                        </ui-search-item>
                        <ui-search-item label="产品信息">
                            <ui-input v-model="searchParams.goodsInfo"></ui-input>
                        </ui-search-item>
                        <ui-search-item label="地区">
                            <ui-select :data="selectList1"></ui-select>
                        </ui-search-item>
                        <ui-search-item label="询价行为">
                            <ui-select :first-remote="true" :remote-info="inquiryRemoteInfo"  multiple-type="fixed" placeholder="全部" v-model="searchParams.inquiryList" clearable></ui-select>
                        </ui-search-item>
                        <ui-search-item label="咨询入口">
                            <ui-select :first-remote="true" :remote-info="entrancesRemoteInfo"  multiple-type="fixed" placeholder="全部" v-model="searchParams.entrancesList" clearable></ui-select>
                        </ui-search-item>
                        <ui-search-item label="功能">
                            <ui-select :first-remote="true" :remote-info="functionsRemoteInfo"  multiple-type="fixed" placeholder="全部" v-model="searchParams.functionsList" clearable></ui-select>
                        </ui-search-item>
                        <ui-search-item label="创建人">
                            <ui-select :remote="true" :avatar="true" multiple-type="fixed" :default-multi="searchParams.creatorUserMultiItems" @change="remoteSelectChange('creatorUserMultiItems', $event)" placeholder="全部" v-model="searchParams.creatorUserIdList" clearable :remote-info="creatorUserIdListRemoteInfo"></ui-select>
                        </ui-search-item>
                    </template>
                    <template v-slot:list-button>
                        <ui-button @click="assignLeads">分配线索</ui-button>
                    </template>
                    <template v-slot:leadsNo="{ row }">
                        <div class="td-link leadsno" @click="GLOBAL.link(row.leadsNo)"><i class="vd-ui_icon icon-popup" title="线索合并" v-if="row.mergeStatus == 2"></i>{{row.leadsNo}}</div>
                    </template>
                    <template v-slot:status="{ row }">{{GLOBAL.BUSINESSLEADS_STATUS[row.status]}}</template>
                    <template v-slot:record="{ row }">
                        <div class="record-wrap" v-if="row.communicateRecordDto.length" :title="row.communicateRecordDto[0].contentSuffix">
                            <i class="vd-ui_icon icon-sms"></i>
                            <div class="record-txt text-line-1 " >{{ row.communicateRecordDto[0].contentSuffix || '-' }}</div>
                        </div>
                        <template v-else>-</template>
                    </template>
                    <template v-slot:address="{ row }">
                        <div class="text-line-1">{{ row.province }}{{ row.city ? '-' +  row.city : '' }}{{ row.county ? '-' + row.county : '' }}</div>
                    </template>
                    <template v-slot:option="{ row }">
                        <div class="option-wrap">
                            <a @click="assignLeadsItem(row)" class="table-edit">分配线索</a>
                            <a class="table-edit" @click="showShareList(row)">设置协作人</a>
                        </div>
                    </template>
                </ui-search-container>
            </div>
        </div>
        <ui-dialog
            :visible.sync="isShowBelongerDialog"
            title="分配线索"
            width="720px"
        >
            <div class="form-wrap label-width-2" v-if="isShowBelongerDialog">
                <ui-form-item label="新归属人" :must="true">
                    <div class="ui-col-4">
                        <ui-select :remote="true" :avatar="true" placeholder="请输入并选择归属人" v-model="belonger" clearable :remote-info="allUserRemoteInfo" valid="belongerForm_belonger"></ui-select>
                    </div>
                </ui-form-item>
            </div>
            <template slot="footer">
                <div class="dlg-form-footer">
                    <ui-button @click="setBelonger" type="primary">保存</ui-button>
                    <ui-button @click="isShowBelongerDialog=false" class="close">取消</ui-button>
                </div>
            </template>
        </ui-dialog>
        <ui-dialog
            :visible.sync="isShowShareAddDialog"
            title="添加协作人"
            width="720px"
        >
            <div class="form-wrap label-width-2" v-if="isShowShareAddDialog">
                <ui-form-item label="协作人" :must="true">
                    <div class="ui-col-4">
                        <ui-select :remote="true" :avatar="true" placeholder="请输入并选择协作人" v-model="shareUserId" clearable :remote-info="allUserRemoteInfo" valid="shareDlgForm_shareUserId" @change="handlerShareSelect"></ui-select>
                    </div>
                </ui-form-item>
            </div>
            <template slot="footer">
                <div class="dlg-form-footer">
                    <ui-button @click="addShareUser" type="primary">保存</ui-button>
                    <ui-button @click="isShowShareAddDialog=false" class="close">取消</ui-button>
                </div>
            </template>
        </ui-dialog>
        <ui-dialog
            :visible.sync="isShowShareDialog"
            title="协作人"
            width="720px"
        >
            <div class="dlg-share-wrap">
                <div class="dlg-share-btn">
                    <ui-button type="primary" @click="showShareAddDialog">添加协作人</ui-button>
                </div>
                <div class="dlg-share-list">
                    <ui-table v-if="!isShareLoading" container-height="470px" :width-border="true" :auto-scroll="false" :headers="shareListHeaders" :list="sharelist">
                        <template v-slot:option="{ row }">
                            <div class="option-wrap">
                                <a class="table-edit" @click="removeShareUser(row)" class="table-edit">移除</a>
                            </div>
                        </template>
                    </ui-table>
                </div>
            </div>
        </ui-dialog>
    </div>
    <script src="./js/common/vue.js"></script>
    <script src="./js/common/axios.js"></script>
    <script src="./js/common/lodash.min.js"></script>
    <script src="./ui/ui.js"></script>
    <script src="./js/common/layout.js"></script>
    <script src="./js/pages/businessleadsList.js"></script>
</body>

</html>