package com.vedeng.erp.kingdee.task.batch;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.kingdee.batch.job.WarehouseOtherInOutBatchJob;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 其他出入库定时任务
 * @date 2022/5/16 10:57
 */
@JobHandler(value = "OtherWarehouseInBatchTask")
@Component
public class OtherWarehouseInBatchTask extends AbstractJobHandler {

    @Autowired
    private WarehouseOtherInOutBatchJob batchJob;

    @Autowired
    private JobLauncher jobLauncher;

    /**
     * {"beginTime":"2022-11-01 00:00:00",
     * "endTime":"2022-12-01 00:00:00",
     * "timestamp":"1666687179395"}
     */
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("==================其他出入库batch开始====================");
        JobParameters jobParameters = new TaskBatchHandle().buildJobParameters(param);
        Job job = batchJob.otherWarehouseInFlowJob();
        jobLauncher.run(job, jobParameters);
        XxlJobLogger.log("==================其他出入库batch结束====================");
        return SUCCESS;
    }


}

