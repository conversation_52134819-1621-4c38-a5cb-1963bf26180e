package com.vedeng.crm.web.controller;


import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.servlet.error.ErrorController;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@Controller
public class CustomErrorController implements ErrorController {

    @RequestMapping("/error")
    public String handleError(HttpServletRequest request) {
        Integer statusCode = (Integer) request.getAttribute("javax.servlet.error.status_code");
        log.info("statusCode: {}", statusCode);
        if (statusCode != null) {
            if(statusCode.equals(403) || statusCode .equals(401) ){
                return "vue/view/crm/noPermission";
            }else if (statusCode .equals( 404)) {
                return "vue/view/crm/profile/common/404"; // 返回自定义的404页面
            } else if (statusCode .equals( 500)) {
                return "vue/view/crm/profile/common/500"; // 返回自定义的500页面
            }
        }
        return "vue/view/crm/profile/common/500"; //返回通用的错误页面
    }

    @RequestMapping("/notsupport")
    public String notsupport() {
        return "vue/view/crm/m/error"; //返回通用的错误页面
    }

    @Override
    public String getErrorPath() {
        return "vue/view/crm/profile/common/500";
    }
}