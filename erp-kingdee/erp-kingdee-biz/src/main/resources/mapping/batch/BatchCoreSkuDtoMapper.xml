<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchCoreSkuDtoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchCoreSkuDto">
    <!--@mbg.generated-->
    <!--@Table V_CORE_SKU-->
    <id column="SKU_ID" jdbcType="INTEGER" property="skuId" />
    <result column="SPU_ID" jdbcType="INTEGER" property="spuId" />
    <result column="CHECK_STATUS" jdbcType="BOOLEAN" property="checkStatus" />
    <result column="MODEL" jdbcType="VARCHAR" property="model" />
    <result column="SPEC" jdbcType="VARCHAR" property="spec" />
    <result column="SKU_NO" jdbcType="VARCHAR" property="skuNo" />
    <result column="SKU_NAME" jdbcType="VARCHAR" property="skuName" />
    <result column="SHOW_NAME" jdbcType="VARCHAR" property="showName" />
    <result column="MATERIAL_CODE" jdbcType="VARCHAR" property="materialCode" />
    <result column="SUPPLY_MODEL" jdbcType="VARCHAR" property="supplyModel" />
    <result column="IS_STOCKUP" jdbcType="VARCHAR" property="isStockup" />
    <result column="WIKI_HREF" jdbcType="VARCHAR" property="wikiHref" />
    <result column="TECHNICAL_PARAMETER" jdbcType="VARCHAR" property="technicalParameter" />
    <result column="PERFORMANCE_PARAMETER" jdbcType="VARCHAR" property="performanceParameter" />
    <result column="SPEC_PARAMETER" jdbcType="VARCHAR" property="specParameter" />
    <result column="BASE_UNIT_ID" jdbcType="INTEGER" property="baseUnitId" />
    <result column="MIN_ORDER" jdbcType="DECIMAL" property="minOrder" />
    <result column="GOODS_LENGTH" jdbcType="DECIMAL" property="goodsLength" />
    <result column="GOODS_WIDTH" jdbcType="DECIMAL" property="goodsWidth" />
    <result column="GOODS_HEIGHT" jdbcType="DECIMAL" property="goodsHeight" />
    <result column="PACKAGE_LENGTH" jdbcType="DECIMAL" property="packageLength" />
    <result column="PACKAGE_WIDTH" jdbcType="DECIMAL" property="packageWidth" />
    <result column="PACKAGE_HEIGHT" jdbcType="DECIMAL" property="packageHeight" />
    <result column="NET_WEIGHT" jdbcType="DECIMAL" property="netWeight" />
    <result column="GROSS_WEIGHT" jdbcType="DECIMAL" property="grossWeight" />
    <result column="UNIT_ID" jdbcType="INTEGER" property="unitId" />
    <result column="CHANGE_NUM" jdbcType="DECIMAL" property="changeNum" />
    <result column="PACKING_LIST" jdbcType="VARCHAR" property="packingList" />
    <result column="AFTER_SALE_CONTENT" jdbcType="VARCHAR" property="afterSaleContent" />
    <result column="QA_YEARS" jdbcType="VARCHAR" property="qaYears" />
    <result column="STORAGE_CONDITION_ONE" jdbcType="BOOLEAN" property="storageConditionOne" />
    <result column="STORAGE_CONDITION_ONE_LOWER_VALUE" jdbcType="FLOAT" property="storageConditionOneLowerValue" />
    <result column="STORAGE_CONDITION_ONE_UPPER_VALUE" jdbcType="FLOAT" property="storageConditionOneUpperValue" />
    <result column="STORAGE_CONDITION_HUMIDITY_LOWER_VALUE" jdbcType="FLOAT" property="storageConditionHumidityLowerValue" />
    <result column="STORAGE_CONDITION_HUMIDITY_UPPER_VALUE" jdbcType="FLOAT" property="storageConditionHumidityUpperValue" />
    <result column="STORAGE_CONDITION_TWO" jdbcType="VARCHAR" property="storageConditionTwo" />
    <result column="EFFECTIVE_DAY_UNIT" jdbcType="BOOLEAN" property="effectiveDayUnit" />
    <result column="EFFECTIVE_DAYS" jdbcType="VARCHAR" property="effectiveDays" />
    <result column="QA_RULE" jdbcType="VARCHAR" property="qaRule" />
    <result column="QA_OUT_PRICE" jdbcType="DECIMAL" property="qaOutPrice" />
    <result column="QA_RESPONSE_TIME" jdbcType="DECIMAL" property="qaResponseTime" />
    <result column="HAS_BACKUP_MACHINE" jdbcType="VARCHAR" property="hasBackupMachine" />
    <result column="SUPPLIER_EXTEND_GUARANTEE_PRICE" jdbcType="DECIMAL" property="supplierExtendGuaranteePrice" />
    <result column="CORE_PARTS_PRICE_FID" jdbcType="INTEGER" property="corePartsPriceFid" />
    <result column="RETURN_GOODS_CONDITIONS" jdbcType="BOOLEAN" property="returnGoodsConditions" />
    <result column="FREIGHT_INTRODUCTIONS" jdbcType="VARCHAR" property="freightIntroductions" />
    <result column="EXCHANGE_GOODS_CONDITIONS" jdbcType="VARCHAR" property="exchangeGoodsConditions" />
    <result column="EXCHANGE_GOODS_METHOD" jdbcType="VARCHAR" property="exchangeGoodsMethod" />
    <result column="GOODS_COMMENTS" jdbcType="VARCHAR" property="goodsComments" />
    <result column="STATUS" jdbcType="BOOLEAN" property="status" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CHECK_TIME" jdbcType="TIMESTAMP" property="checkTime" />
    <result column="CHECKER" jdbcType="INTEGER" property="checker" />
    <result column="OPERATE_INFO_ID" jdbcType="INTEGER" property="operateInfoId" />
    <result column="DELETE_REASON" jdbcType="VARCHAR" property="deleteReason" />
    <result column="LAST_CHECK_REASON" jdbcType="VARCHAR" property="lastCheckReason" />
    <result column="TAX_CATEGORY_NO" jdbcType="VARCHAR" property="taxCategoryNo" />
    <result column="JX_MARKET_PRICE" jdbcType="DECIMAL" property="jxMarketPrice" />
    <result column="JX_SALE_PRICE" jdbcType="DECIMAL" property="jxSalePrice" />
    <result column="JX_FLAG" jdbcType="INTEGER" property="jxFlag" />
    <result column="SOURCE" jdbcType="TINYINT" property="source" />
    <result column="PUSH_STATUS" jdbcType="INTEGER" property="pushStatus" />
    <result column="DECLARE_DELIVERY_RANGE" jdbcType="VARCHAR" property="declareDeliveryRange" />
    <result column="PRICE_VERIFY_STATUS" jdbcType="INTEGER" property="priceVerifyStatus" />
    <result column="AVGPRICE" jdbcType="DECIMAL" property="avgprice" />
    <result column="LATEST_VALID_ORDER_USER" jdbcType="INTEGER" property="latestValidOrderUser" />
    <result column="AVGPRICE_UPDATE_TIME" jdbcType="TIMESTAMP" property="avgpriceUpdateTime" />
    <result column="TERMINAL_PRICE" jdbcType="DECIMAL" property="terminalPrice" />
    <result column="DISTRIBUTION_PRICE" jdbcType="DECIMAL" property="distributionPrice" />
    <result column="COST_PRICE" jdbcType="DECIMAL" property="costPrice" />
    <result column="AVAILABLE_STOCK_NUM" jdbcType="INTEGER" property="availableStockNum" />
    <result column="STOCK_NUM" jdbcType="INTEGER" property="stockNum" />
    <result column="ON_SALE" jdbcType="INTEGER" property="onSale" />
    <result column="GOODS_BARCODE" jdbcType="VARCHAR" property="goodsBarcode" />
    <result column="CURING_TYPE" jdbcType="BOOLEAN" property="curingType" />
    <result column="CURING_REASON" jdbcType="VARCHAR" property="curingReason" />
    <result column="IS_NEED_TEST_REPROT" jdbcType="BOOLEAN" property="isNeedTestReprot" />
    <result column="IS_KIT" jdbcType="BOOLEAN" property="isKit" />
    <result column="KIT_DESC" jdbcType="VARCHAR" property="kitDesc" />
    <result column="IS_SAME_SN_CODE" jdbcType="BOOLEAN" property="isSameSnCode" />
    <result column="IS_FACTORY_SN_CODE" jdbcType="BOOLEAN" property="isFactorySnCode" />
    <result column="IS_MANAGE_VEDENG_CODE" jdbcType="BOOLEAN" property="isManageVedengCode" />
    <result column="IS_BAD_GOODS" jdbcType="BOOLEAN" property="isBadGoods" />
    <result column="IS_ENABLE_FACTORY_BATCHNUM" jdbcType="BOOLEAN" property="isEnableFactoryBatchnum" />
    <result column="IS_ENABLE_MULTISTAGE_PACKAGE" jdbcType="BOOLEAN" property="isEnableMultistagePackage" />
    <result column="MID_PACKAGE_NUM" jdbcType="INTEGER" property="midPackageNum" />
    <result column="BOX_PACKAGE_NUM" jdbcType="INTEGER" property="boxPackageNum" />
    <result column="IS_ENABLE_VALIDITY_PERIOD" jdbcType="TINYINT" property="isEnableValidityPeriod" />
    <result column="NEAR_TERM_WARN_DAYS" jdbcType="INTEGER" property="nearTermWarnDays" />
    <result column="OVER_NEAR_TERM_WARN_DAYS" jdbcType="INTEGER" property="overNearTermWarnDays" />
    <result column="INSTALL_TRAIN_TYPE" jdbcType="BOOLEAN" property="installTrainType" />
    <result column="LOGISTICS_DELIVERYTYPE" jdbcType="BOOLEAN" property="logisticsDeliverytype" />
    <result column="IS_NEED_REPORT" jdbcType="BOOLEAN" property="isNeedReport" />
    <result column="IS_AUTHORIZED" jdbcType="BOOLEAN" property="isAuthorized" />
    <result column="HISTORY_NAME" jdbcType="VARCHAR" property="historyName" />
    <result column="IS_NAME_CHANGE" jdbcType="TINYINT" property="isNameChange" />
    <result column="ONE_YEAR_SALE_NUM" jdbcType="INTEGER" property="oneYearSaleNum" />
    <result column="REGULAR_MAINTAIN_TYPE" jdbcType="BOOLEAN" property="regularMaintainType" />
    <result column="REGULAR_MAINTAIN_REASON" jdbcType="VARCHAR" property="regularMaintainReason" />
    <result column="SYNCHRONIZATION_STATUS" jdbcType="BOOLEAN" property="synchronizationStatus" />
    <result column="IS_INSTALLABLE" jdbcType="BOOLEAN" property="isInstallable" />
    <result column="GOODS_LEVEL_NO" jdbcType="INTEGER" property="goodsLevelNo" />
    <result column="GOODS_POSITION_NO" jdbcType="INTEGER" property="goodsPositionNo" />
    <result column="LAST_YEAR_RATIO_EIGHTY_SORT" jdbcType="INTEGER" property="lastYearRatioEightySort" />
    <result column="THREE_MONTH_RATIO_EIGHTY_SORT" jdbcType="INTEGER" property="threeMonthRatioEightySort" />
    <result column="ONE_MONTH_RATIO_EIGHTY_SORT" jdbcType="INTEGER" property="oneMonthRatioEightySort" />
    <result column="ORG_ID_LIST" jdbcType="VARCHAR" property="orgIdList" />
    <result column="IS_AVAILABLE_SALE" jdbcType="INTEGER" property="isAvailableSale" />
    <result column="PUSHED_ORG_ID_LIST" jdbcType="VARCHAR" property="pushedOrgIdList" />
    <result column="CONFIGURATION_LIST" jdbcType="VARCHAR" property="configurationList" />
    <result column="ACTION_LOCK_NUM" jdbcType="INTEGER" property="actionLockNum" />
    <result column="ORDER_OCCUPY_NUM" jdbcType="INTEGER" property="orderOccupyNum" />
    <result column="DISABLED_REASON" jdbcType="VARCHAR" property="disabledReason" />
    <result column="PURCHASE_TIME" jdbcType="INTEGER" property="purchaseTime" />
    <result column="PURCHASE_TIME_UPDATE_TIME" jdbcType="TIMESTAMP" property="purchaseTimeUpdateTime" />
    <result column="HAS_AFTER_SALE_SERVICE_LABEL" jdbcType="BOOLEAN" property="hasAfterSaleServiceLabel" />
    <result column="SPU_TYPE" jdbcType="INTEGER" property="spuType" />
    <result column="INSTITUTION_LEVEL_IDS" jdbcType="VARCHAR" property="institutionLevelIds" />
    <result column="IS_SEVEN" jdbcType="INTEGER" property="isSeven" />
    <result column="HAVE_STOCK_MANAGE" jdbcType="BOOLEAN" property="haveStockManage" />
    <result column="COST_CATEGORY_ID" jdbcType="INTEGER" property="costCategoryId" />
    <result column="IS_VIRTURE_SKU" jdbcType="BOOLEAN" property="isVirtureSku" />
    <result column="VIRTURE_TIME" jdbcType="TIMESTAMP" property="virtureTime" />
    <result column="VIRTURE_CREATOR" jdbcType="INTEGER" property="virtureCreator" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    SKU_ID, SPU_ID, CHECK_STATUS, MODEL, SPEC, SKU_NO, SKU_NAME, SHOW_NAME, MATERIAL_CODE, 
    SUPPLY_MODEL, IS_STOCKUP, WIKI_HREF, TECHNICAL_PARAMETER, PERFORMANCE_PARAMETER, 
    SPEC_PARAMETER, BASE_UNIT_ID, MIN_ORDER, GOODS_LENGTH, GOODS_WIDTH, GOODS_HEIGHT, 
    PACKAGE_LENGTH, PACKAGE_WIDTH, PACKAGE_HEIGHT, NET_WEIGHT, GROSS_WEIGHT, UNIT_ID, 
    CHANGE_NUM, PACKING_LIST, AFTER_SALE_CONTENT, QA_YEARS, STORAGE_CONDITION_ONE, STORAGE_CONDITION_ONE_LOWER_VALUE, 
    STORAGE_CONDITION_ONE_UPPER_VALUE, STORAGE_CONDITION_HUMIDITY_LOWER_VALUE, STORAGE_CONDITION_HUMIDITY_UPPER_VALUE, 
    STORAGE_CONDITION_TWO, EFFECTIVE_DAY_UNIT, EFFECTIVE_DAYS, QA_RULE, QA_OUT_PRICE, 
    QA_RESPONSE_TIME, HAS_BACKUP_MACHINE, SUPPLIER_EXTEND_GUARANTEE_PRICE, CORE_PARTS_PRICE_FID, 
    RETURN_GOODS_CONDITIONS, FREIGHT_INTRODUCTIONS, EXCHANGE_GOODS_CONDITIONS, EXCHANGE_GOODS_METHOD, 
    GOODS_COMMENTS, `STATUS`, ADD_TIME, CREATOR, MOD_TIME, UPDATER, CHECK_TIME, CHECKER, 
    OPERATE_INFO_ID, DELETE_REASON, LAST_CHECK_REASON, TAX_CATEGORY_NO, JX_MARKET_PRICE, 
    JX_SALE_PRICE, JX_FLAG, `SOURCE`, PUSH_STATUS, DECLARE_DELIVERY_RANGE, PRICE_VERIFY_STATUS, 
    AVGPRICE, LATEST_VALID_ORDER_USER, AVGPRICE_UPDATE_TIME, TERMINAL_PRICE, DISTRIBUTION_PRICE, 
    COST_PRICE, AVAILABLE_STOCK_NUM, STOCK_NUM, ON_SALE, GOODS_BARCODE, CURING_TYPE, 
    CURING_REASON, IS_NEED_TEST_REPROT, IS_KIT, KIT_DESC, IS_SAME_SN_CODE, IS_FACTORY_SN_CODE, 
    IS_MANAGE_VEDENG_CODE, IS_BAD_GOODS, IS_ENABLE_FACTORY_BATCHNUM, IS_ENABLE_MULTISTAGE_PACKAGE, 
    MID_PACKAGE_NUM, BOX_PACKAGE_NUM, IS_ENABLE_VALIDITY_PERIOD, NEAR_TERM_WARN_DAYS, 
    OVER_NEAR_TERM_WARN_DAYS, INSTALL_TRAIN_TYPE, LOGISTICS_DELIVERYTYPE, IS_NEED_REPORT, 
    IS_AUTHORIZED, HISTORY_NAME, IS_NAME_CHANGE, ONE_YEAR_SALE_NUM, REGULAR_MAINTAIN_TYPE, 
    REGULAR_MAINTAIN_REASON, SYNCHRONIZATION_STATUS, IS_INSTALLABLE, GOODS_LEVEL_NO, 
    GOODS_POSITION_NO, LAST_YEAR_RATIO_EIGHTY_SORT, THREE_MONTH_RATIO_EIGHTY_SORT, ONE_MONTH_RATIO_EIGHTY_SORT, 
    ORG_ID_LIST, IS_AVAILABLE_SALE, PUSHED_ORG_ID_LIST, CONFIGURATION_LIST, ACTION_LOCK_NUM, 
    ORDER_OCCUPY_NUM, DISABLED_REASON, PURCHASE_TIME, PURCHASE_TIME_UPDATE_TIME, HAS_AFTER_SALE_SERVICE_LABEL, 
    SPU_TYPE, INSTITUTION_LEVEL_IDS, IS_SEVEN, HAVE_STOCK_MANAGE, COST_CATEGORY_ID, IS_VIRTURE_SKU, 
    VIRTURE_TIME, VIRTURE_CREATOR
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from V_CORE_SKU
    where SKU_ID = #{skuId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from V_CORE_SKU
    where SKU_ID = #{skuId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="SKU_ID" keyProperty="skuId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchCoreSkuDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into V_CORE_SKU (SPU_ID, CHECK_STATUS, MODEL, 
      SPEC, SKU_NO, SKU_NAME, 
      SHOW_NAME, MATERIAL_CODE, SUPPLY_MODEL, 
      IS_STOCKUP, WIKI_HREF, TECHNICAL_PARAMETER, 
      PERFORMANCE_PARAMETER, SPEC_PARAMETER, BASE_UNIT_ID, 
      MIN_ORDER, GOODS_LENGTH, GOODS_WIDTH, 
      GOODS_HEIGHT, PACKAGE_LENGTH, PACKAGE_WIDTH, 
      PACKAGE_HEIGHT, NET_WEIGHT, GROSS_WEIGHT, 
      UNIT_ID, CHANGE_NUM, PACKING_LIST, 
      AFTER_SALE_CONTENT, QA_YEARS, STORAGE_CONDITION_ONE, 
      STORAGE_CONDITION_ONE_LOWER_VALUE, STORAGE_CONDITION_ONE_UPPER_VALUE, 
      STORAGE_CONDITION_HUMIDITY_LOWER_VALUE, STORAGE_CONDITION_HUMIDITY_UPPER_VALUE, 
      STORAGE_CONDITION_TWO, EFFECTIVE_DAY_UNIT, 
      EFFECTIVE_DAYS, QA_RULE, QA_OUT_PRICE, 
      QA_RESPONSE_TIME, HAS_BACKUP_MACHINE, SUPPLIER_EXTEND_GUARANTEE_PRICE, 
      CORE_PARTS_PRICE_FID, RETURN_GOODS_CONDITIONS, 
      FREIGHT_INTRODUCTIONS, EXCHANGE_GOODS_CONDITIONS, 
      EXCHANGE_GOODS_METHOD, GOODS_COMMENTS, `STATUS`, 
      ADD_TIME, CREATOR, MOD_TIME, 
      UPDATER, CHECK_TIME, CHECKER, 
      OPERATE_INFO_ID, DELETE_REASON, LAST_CHECK_REASON, 
      TAX_CATEGORY_NO, JX_MARKET_PRICE, JX_SALE_PRICE, 
      JX_FLAG, `SOURCE`, PUSH_STATUS, 
      DECLARE_DELIVERY_RANGE, PRICE_VERIFY_STATUS, 
      AVGPRICE, LATEST_VALID_ORDER_USER, AVGPRICE_UPDATE_TIME, 
      TERMINAL_PRICE, DISTRIBUTION_PRICE, COST_PRICE, 
      AVAILABLE_STOCK_NUM, STOCK_NUM, ON_SALE, 
      GOODS_BARCODE, CURING_TYPE, CURING_REASON, 
      IS_NEED_TEST_REPROT, IS_KIT, KIT_DESC, 
      IS_SAME_SN_CODE, IS_FACTORY_SN_CODE, IS_MANAGE_VEDENG_CODE, 
      IS_BAD_GOODS, IS_ENABLE_FACTORY_BATCHNUM, IS_ENABLE_MULTISTAGE_PACKAGE, 
      MID_PACKAGE_NUM, BOX_PACKAGE_NUM, IS_ENABLE_VALIDITY_PERIOD, 
      NEAR_TERM_WARN_DAYS, OVER_NEAR_TERM_WARN_DAYS, 
      INSTALL_TRAIN_TYPE, LOGISTICS_DELIVERYTYPE, 
      IS_NEED_REPORT, IS_AUTHORIZED, HISTORY_NAME, 
      IS_NAME_CHANGE, ONE_YEAR_SALE_NUM, REGULAR_MAINTAIN_TYPE, 
      REGULAR_MAINTAIN_REASON, SYNCHRONIZATION_STATUS, 
      IS_INSTALLABLE, GOODS_LEVEL_NO, GOODS_POSITION_NO, 
      LAST_YEAR_RATIO_EIGHTY_SORT, THREE_MONTH_RATIO_EIGHTY_SORT, 
      ONE_MONTH_RATIO_EIGHTY_SORT, ORG_ID_LIST, IS_AVAILABLE_SALE, 
      PUSHED_ORG_ID_LIST, CONFIGURATION_LIST, ACTION_LOCK_NUM, 
      ORDER_OCCUPY_NUM, DISABLED_REASON, PURCHASE_TIME, 
      PURCHASE_TIME_UPDATE_TIME, HAS_AFTER_SALE_SERVICE_LABEL, 
      SPU_TYPE, INSTITUTION_LEVEL_IDS, IS_SEVEN, 
      HAVE_STOCK_MANAGE, COST_CATEGORY_ID, IS_VIRTURE_SKU, 
      VIRTURE_TIME, VIRTURE_CREATOR)
    values (#{spuId,jdbcType=INTEGER}, #{checkStatus,jdbcType=BOOLEAN}, #{model,jdbcType=VARCHAR}, 
      #{spec,jdbcType=VARCHAR}, #{skuNo,jdbcType=VARCHAR}, #{skuName,jdbcType=VARCHAR}, 
      #{showName,jdbcType=VARCHAR}, #{materialCode,jdbcType=VARCHAR}, #{supplyModel,jdbcType=VARCHAR}, 
      #{isStockup,jdbcType=VARCHAR}, #{wikiHref,jdbcType=VARCHAR}, #{technicalParameter,jdbcType=VARCHAR}, 
      #{performanceParameter,jdbcType=VARCHAR}, #{specParameter,jdbcType=VARCHAR}, #{baseUnitId,jdbcType=INTEGER}, 
      #{minOrder,jdbcType=DECIMAL}, #{goodsLength,jdbcType=DECIMAL}, #{goodsWidth,jdbcType=DECIMAL}, 
      #{goodsHeight,jdbcType=DECIMAL}, #{packageLength,jdbcType=DECIMAL}, #{packageWidth,jdbcType=DECIMAL}, 
      #{packageHeight,jdbcType=DECIMAL}, #{netWeight,jdbcType=DECIMAL}, #{grossWeight,jdbcType=DECIMAL}, 
      #{unitId,jdbcType=INTEGER}, #{changeNum,jdbcType=DECIMAL}, #{packingList,jdbcType=VARCHAR}, 
      #{afterSaleContent,jdbcType=VARCHAR}, #{qaYears,jdbcType=VARCHAR}, #{storageConditionOne,jdbcType=BOOLEAN}, 
      #{storageConditionOneLowerValue,jdbcType=FLOAT}, #{storageConditionOneUpperValue,jdbcType=FLOAT}, 
      #{storageConditionHumidityLowerValue,jdbcType=FLOAT}, #{storageConditionHumidityUpperValue,jdbcType=FLOAT}, 
      #{storageConditionTwo,jdbcType=VARCHAR}, #{effectiveDayUnit,jdbcType=BOOLEAN}, 
      #{effectiveDays,jdbcType=VARCHAR}, #{qaRule,jdbcType=VARCHAR}, #{qaOutPrice,jdbcType=DECIMAL}, 
      #{qaResponseTime,jdbcType=DECIMAL}, #{hasBackupMachine,jdbcType=VARCHAR}, #{supplierExtendGuaranteePrice,jdbcType=DECIMAL}, 
      #{corePartsPriceFid,jdbcType=INTEGER}, #{returnGoodsConditions,jdbcType=BOOLEAN}, 
      #{freightIntroductions,jdbcType=VARCHAR}, #{exchangeGoodsConditions,jdbcType=VARCHAR}, 
      #{exchangeGoodsMethod,jdbcType=VARCHAR}, #{goodsComments,jdbcType=VARCHAR}, #{status,jdbcType=BOOLEAN}, 
      #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=TIMESTAMP}, 
      #{updater,jdbcType=INTEGER}, #{checkTime,jdbcType=TIMESTAMP}, #{checker,jdbcType=INTEGER}, 
      #{operateInfoId,jdbcType=INTEGER}, #{deleteReason,jdbcType=VARCHAR}, #{lastCheckReason,jdbcType=VARCHAR}, 
      #{taxCategoryNo,jdbcType=VARCHAR}, #{jxMarketPrice,jdbcType=DECIMAL}, #{jxSalePrice,jdbcType=DECIMAL}, 
      #{jxFlag,jdbcType=INTEGER}, #{source,jdbcType=TINYINT}, #{pushStatus,jdbcType=INTEGER}, 
      #{declareDeliveryRange,jdbcType=VARCHAR}, #{priceVerifyStatus,jdbcType=INTEGER}, 
      #{avgprice,jdbcType=DECIMAL}, #{latestValidOrderUser,jdbcType=INTEGER}, #{avgpriceUpdateTime,jdbcType=TIMESTAMP}, 
      #{terminalPrice,jdbcType=DECIMAL}, #{distributionPrice,jdbcType=DECIMAL}, #{costPrice,jdbcType=DECIMAL}, 
      #{availableStockNum,jdbcType=INTEGER}, #{stockNum,jdbcType=INTEGER}, #{onSale,jdbcType=INTEGER}, 
      #{goodsBarcode,jdbcType=VARCHAR}, #{curingType,jdbcType=BOOLEAN}, #{curingReason,jdbcType=VARCHAR}, 
      #{isNeedTestReprot,jdbcType=BOOLEAN}, #{isKit,jdbcType=BOOLEAN}, #{kitDesc,jdbcType=VARCHAR}, 
      #{isSameSnCode,jdbcType=BOOLEAN}, #{isFactorySnCode,jdbcType=BOOLEAN}, #{isManageVedengCode,jdbcType=BOOLEAN}, 
      #{isBadGoods,jdbcType=BOOLEAN}, #{isEnableFactoryBatchnum,jdbcType=BOOLEAN}, #{isEnableMultistagePackage,jdbcType=BOOLEAN}, 
      #{midPackageNum,jdbcType=INTEGER}, #{boxPackageNum,jdbcType=INTEGER}, #{isEnableValidityPeriod,jdbcType=TINYINT}, 
      #{nearTermWarnDays,jdbcType=INTEGER}, #{overNearTermWarnDays,jdbcType=INTEGER}, 
      #{installTrainType,jdbcType=BOOLEAN}, #{logisticsDeliverytype,jdbcType=BOOLEAN}, 
      #{isNeedReport,jdbcType=BOOLEAN}, #{isAuthorized,jdbcType=BOOLEAN}, #{historyName,jdbcType=VARCHAR}, 
      #{isNameChange,jdbcType=TINYINT}, #{oneYearSaleNum,jdbcType=INTEGER}, #{regularMaintainType,jdbcType=BOOLEAN}, 
      #{regularMaintainReason,jdbcType=VARCHAR}, #{synchronizationStatus,jdbcType=BOOLEAN}, 
      #{isInstallable,jdbcType=BOOLEAN}, #{goodsLevelNo,jdbcType=INTEGER}, #{goodsPositionNo,jdbcType=INTEGER}, 
      #{lastYearRatioEightySort,jdbcType=INTEGER}, #{threeMonthRatioEightySort,jdbcType=INTEGER}, 
      #{oneMonthRatioEightySort,jdbcType=INTEGER}, #{orgIdList,jdbcType=VARCHAR}, #{isAvailableSale,jdbcType=INTEGER}, 
      #{pushedOrgIdList,jdbcType=VARCHAR}, #{configurationList,jdbcType=VARCHAR}, #{actionLockNum,jdbcType=INTEGER}, 
      #{orderOccupyNum,jdbcType=INTEGER}, #{disabledReason,jdbcType=VARCHAR}, #{purchaseTime,jdbcType=INTEGER}, 
      #{purchaseTimeUpdateTime,jdbcType=TIMESTAMP}, #{hasAfterSaleServiceLabel,jdbcType=BOOLEAN}, 
      #{spuType,jdbcType=INTEGER}, #{institutionLevelIds,jdbcType=VARCHAR}, #{isSeven,jdbcType=INTEGER}, 
      #{haveStockManage,jdbcType=BOOLEAN}, #{costCategoryId,jdbcType=INTEGER}, #{isVirtureSku,jdbcType=BOOLEAN}, 
      #{virtureTime,jdbcType=TIMESTAMP}, #{virtureCreator,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="SKU_ID" keyProperty="skuId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchCoreSkuDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into V_CORE_SKU
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="spuId != null">
        SPU_ID,
      </if>
      <if test="checkStatus != null">
        CHECK_STATUS,
      </if>
      <if test="model != null and model != ''">
        MODEL,
      </if>
      <if test="spec != null and spec != ''">
        SPEC,
      </if>
      <if test="skuNo != null and skuNo != ''">
        SKU_NO,
      </if>
      <if test="skuName != null and skuName != ''">
        SKU_NAME,
      </if>
      <if test="showName != null and showName != ''">
        SHOW_NAME,
      </if>
      <if test="materialCode != null and materialCode != ''">
        MATERIAL_CODE,
      </if>
      <if test="supplyModel != null and supplyModel != ''">
        SUPPLY_MODEL,
      </if>
      <if test="isStockup != null and isStockup != ''">
        IS_STOCKUP,
      </if>
      <if test="wikiHref != null and wikiHref != ''">
        WIKI_HREF,
      </if>
      <if test="technicalParameter != null and technicalParameter != ''">
        TECHNICAL_PARAMETER,
      </if>
      <if test="performanceParameter != null and performanceParameter != ''">
        PERFORMANCE_PARAMETER,
      </if>
      <if test="specParameter != null and specParameter != ''">
        SPEC_PARAMETER,
      </if>
      <if test="baseUnitId != null">
        BASE_UNIT_ID,
      </if>
      <if test="minOrder != null">
        MIN_ORDER,
      </if>
      <if test="goodsLength != null">
        GOODS_LENGTH,
      </if>
      <if test="goodsWidth != null">
        GOODS_WIDTH,
      </if>
      <if test="goodsHeight != null">
        GOODS_HEIGHT,
      </if>
      <if test="packageLength != null">
        PACKAGE_LENGTH,
      </if>
      <if test="packageWidth != null">
        PACKAGE_WIDTH,
      </if>
      <if test="packageHeight != null">
        PACKAGE_HEIGHT,
      </if>
      <if test="netWeight != null">
        NET_WEIGHT,
      </if>
      <if test="grossWeight != null">
        GROSS_WEIGHT,
      </if>
      <if test="unitId != null">
        UNIT_ID,
      </if>
      <if test="changeNum != null">
        CHANGE_NUM,
      </if>
      <if test="packingList != null and packingList != ''">
        PACKING_LIST,
      </if>
      <if test="afterSaleContent != null and afterSaleContent != ''">
        AFTER_SALE_CONTENT,
      </if>
      <if test="qaYears != null and qaYears != ''">
        QA_YEARS,
      </if>
      <if test="storageConditionOne != null">
        STORAGE_CONDITION_ONE,
      </if>
      <if test="storageConditionOneLowerValue != null">
        STORAGE_CONDITION_ONE_LOWER_VALUE,
      </if>
      <if test="storageConditionOneUpperValue != null">
        STORAGE_CONDITION_ONE_UPPER_VALUE,
      </if>
      <if test="storageConditionHumidityLowerValue != null">
        STORAGE_CONDITION_HUMIDITY_LOWER_VALUE,
      </if>
      <if test="storageConditionHumidityUpperValue != null">
        STORAGE_CONDITION_HUMIDITY_UPPER_VALUE,
      </if>
      <if test="storageConditionTwo != null and storageConditionTwo != ''">
        STORAGE_CONDITION_TWO,
      </if>
      <if test="effectiveDayUnit != null">
        EFFECTIVE_DAY_UNIT,
      </if>
      <if test="effectiveDays != null and effectiveDays != ''">
        EFFECTIVE_DAYS,
      </if>
      <if test="qaRule != null and qaRule != ''">
        QA_RULE,
      </if>
      <if test="qaOutPrice != null">
        QA_OUT_PRICE,
      </if>
      <if test="qaResponseTime != null">
        QA_RESPONSE_TIME,
      </if>
      <if test="hasBackupMachine != null and hasBackupMachine != ''">
        HAS_BACKUP_MACHINE,
      </if>
      <if test="supplierExtendGuaranteePrice != null">
        SUPPLIER_EXTEND_GUARANTEE_PRICE,
      </if>
      <if test="corePartsPriceFid != null">
        CORE_PARTS_PRICE_FID,
      </if>
      <if test="returnGoodsConditions != null">
        RETURN_GOODS_CONDITIONS,
      </if>
      <if test="freightIntroductions != null and freightIntroductions != ''">
        FREIGHT_INTRODUCTIONS,
      </if>
      <if test="exchangeGoodsConditions != null and exchangeGoodsConditions != ''">
        EXCHANGE_GOODS_CONDITIONS,
      </if>
      <if test="exchangeGoodsMethod != null and exchangeGoodsMethod != ''">
        EXCHANGE_GOODS_METHOD,
      </if>
      <if test="goodsComments != null and goodsComments != ''">
        GOODS_COMMENTS,
      </if>
      <if test="status != null">
        `STATUS`,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="checkTime != null">
        CHECK_TIME,
      </if>
      <if test="checker != null">
        CHECKER,
      </if>
      <if test="operateInfoId != null">
        OPERATE_INFO_ID,
      </if>
      <if test="deleteReason != null and deleteReason != ''">
        DELETE_REASON,
      </if>
      <if test="lastCheckReason != null and lastCheckReason != ''">
        LAST_CHECK_REASON,
      </if>
      <if test="taxCategoryNo != null and taxCategoryNo != ''">
        TAX_CATEGORY_NO,
      </if>
      <if test="jxMarketPrice != null">
        JX_MARKET_PRICE,
      </if>
      <if test="jxSalePrice != null">
        JX_SALE_PRICE,
      </if>
      <if test="jxFlag != null">
        JX_FLAG,
      </if>
      <if test="source != null">
        `SOURCE`,
      </if>
      <if test="pushStatus != null">
        PUSH_STATUS,
      </if>
      <if test="declareDeliveryRange != null and declareDeliveryRange != ''">
        DECLARE_DELIVERY_RANGE,
      </if>
      <if test="priceVerifyStatus != null">
        PRICE_VERIFY_STATUS,
      </if>
      <if test="avgprice != null">
        AVGPRICE,
      </if>
      <if test="latestValidOrderUser != null">
        LATEST_VALID_ORDER_USER,
      </if>
      <if test="avgpriceUpdateTime != null">
        AVGPRICE_UPDATE_TIME,
      </if>
      <if test="terminalPrice != null">
        TERMINAL_PRICE,
      </if>
      <if test="distributionPrice != null">
        DISTRIBUTION_PRICE,
      </if>
      <if test="costPrice != null">
        COST_PRICE,
      </if>
      <if test="availableStockNum != null">
        AVAILABLE_STOCK_NUM,
      </if>
      <if test="stockNum != null">
        STOCK_NUM,
      </if>
      <if test="onSale != null">
        ON_SALE,
      </if>
      <if test="goodsBarcode != null and goodsBarcode != ''">
        GOODS_BARCODE,
      </if>
      <if test="curingType != null">
        CURING_TYPE,
      </if>
      <if test="curingReason != null and curingReason != ''">
        CURING_REASON,
      </if>
      <if test="isNeedTestReprot != null">
        IS_NEED_TEST_REPROT,
      </if>
      <if test="isKit != null">
        IS_KIT,
      </if>
      <if test="kitDesc != null and kitDesc != ''">
        KIT_DESC,
      </if>
      <if test="isSameSnCode != null">
        IS_SAME_SN_CODE,
      </if>
      <if test="isFactorySnCode != null">
        IS_FACTORY_SN_CODE,
      </if>
      <if test="isManageVedengCode != null">
        IS_MANAGE_VEDENG_CODE,
      </if>
      <if test="isBadGoods != null">
        IS_BAD_GOODS,
      </if>
      <if test="isEnableFactoryBatchnum != null">
        IS_ENABLE_FACTORY_BATCHNUM,
      </if>
      <if test="isEnableMultistagePackage != null">
        IS_ENABLE_MULTISTAGE_PACKAGE,
      </if>
      <if test="midPackageNum != null">
        MID_PACKAGE_NUM,
      </if>
      <if test="boxPackageNum != null">
        BOX_PACKAGE_NUM,
      </if>
      <if test="isEnableValidityPeriod != null">
        IS_ENABLE_VALIDITY_PERIOD,
      </if>
      <if test="nearTermWarnDays != null">
        NEAR_TERM_WARN_DAYS,
      </if>
      <if test="overNearTermWarnDays != null">
        OVER_NEAR_TERM_WARN_DAYS,
      </if>
      <if test="installTrainType != null">
        INSTALL_TRAIN_TYPE,
      </if>
      <if test="logisticsDeliverytype != null">
        LOGISTICS_DELIVERYTYPE,
      </if>
      <if test="isNeedReport != null">
        IS_NEED_REPORT,
      </if>
      <if test="isAuthorized != null">
        IS_AUTHORIZED,
      </if>
      <if test="historyName != null and historyName != ''">
        HISTORY_NAME,
      </if>
      <if test="isNameChange != null">
        IS_NAME_CHANGE,
      </if>
      <if test="oneYearSaleNum != null">
        ONE_YEAR_SALE_NUM,
      </if>
      <if test="regularMaintainType != null">
        REGULAR_MAINTAIN_TYPE,
      </if>
      <if test="regularMaintainReason != null and regularMaintainReason != ''">
        REGULAR_MAINTAIN_REASON,
      </if>
      <if test="synchronizationStatus != null">
        SYNCHRONIZATION_STATUS,
      </if>
      <if test="isInstallable != null">
        IS_INSTALLABLE,
      </if>
      <if test="goodsLevelNo != null">
        GOODS_LEVEL_NO,
      </if>
      <if test="goodsPositionNo != null">
        GOODS_POSITION_NO,
      </if>
      <if test="lastYearRatioEightySort != null">
        LAST_YEAR_RATIO_EIGHTY_SORT,
      </if>
      <if test="threeMonthRatioEightySort != null">
        THREE_MONTH_RATIO_EIGHTY_SORT,
      </if>
      <if test="oneMonthRatioEightySort != null">
        ONE_MONTH_RATIO_EIGHTY_SORT,
      </if>
      <if test="orgIdList != null and orgIdList != ''">
        ORG_ID_LIST,
      </if>
      <if test="isAvailableSale != null">
        IS_AVAILABLE_SALE,
      </if>
      <if test="pushedOrgIdList != null and pushedOrgIdList != ''">
        PUSHED_ORG_ID_LIST,
      </if>
      <if test="configurationList != null and configurationList != ''">
        CONFIGURATION_LIST,
      </if>
      <if test="actionLockNum != null">
        ACTION_LOCK_NUM,
      </if>
      <if test="orderOccupyNum != null">
        ORDER_OCCUPY_NUM,
      </if>
      <if test="disabledReason != null and disabledReason != ''">
        DISABLED_REASON,
      </if>
      <if test="purchaseTime != null">
        PURCHASE_TIME,
      </if>
      <if test="purchaseTimeUpdateTime != null">
        PURCHASE_TIME_UPDATE_TIME,
      </if>
      <if test="hasAfterSaleServiceLabel != null">
        HAS_AFTER_SALE_SERVICE_LABEL,
      </if>
      <if test="spuType != null">
        SPU_TYPE,
      </if>
      <if test="institutionLevelIds != null and institutionLevelIds != ''">
        INSTITUTION_LEVEL_IDS,
      </if>
      <if test="isSeven != null">
        IS_SEVEN,
      </if>
      <if test="haveStockManage != null">
        HAVE_STOCK_MANAGE,
      </if>
      <if test="costCategoryId != null">
        COST_CATEGORY_ID,
      </if>
      <if test="isVirtureSku != null">
        IS_VIRTURE_SKU,
      </if>
      <if test="virtureTime != null">
        VIRTURE_TIME,
      </if>
      <if test="virtureCreator != null">
        VIRTURE_CREATOR,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="spuId != null">
        #{spuId,jdbcType=INTEGER},
      </if>
      <if test="checkStatus != null">
        #{checkStatus,jdbcType=BOOLEAN},
      </if>
      <if test="model != null and model != ''">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="spec != null and spec != ''">
        #{spec,jdbcType=VARCHAR},
      </if>
      <if test="skuNo != null and skuNo != ''">
        #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null and skuName != ''">
        #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="showName != null and showName != ''">
        #{showName,jdbcType=VARCHAR},
      </if>
      <if test="materialCode != null and materialCode != ''">
        #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="supplyModel != null and supplyModel != ''">
        #{supplyModel,jdbcType=VARCHAR},
      </if>
      <if test="isStockup != null and isStockup != ''">
        #{isStockup,jdbcType=VARCHAR},
      </if>
      <if test="wikiHref != null and wikiHref != ''">
        #{wikiHref,jdbcType=VARCHAR},
      </if>
      <if test="technicalParameter != null and technicalParameter != ''">
        #{technicalParameter,jdbcType=VARCHAR},
      </if>
      <if test="performanceParameter != null and performanceParameter != ''">
        #{performanceParameter,jdbcType=VARCHAR},
      </if>
      <if test="specParameter != null and specParameter != ''">
        #{specParameter,jdbcType=VARCHAR},
      </if>
      <if test="baseUnitId != null">
        #{baseUnitId,jdbcType=INTEGER},
      </if>
      <if test="minOrder != null">
        #{minOrder,jdbcType=DECIMAL},
      </if>
      <if test="goodsLength != null">
        #{goodsLength,jdbcType=DECIMAL},
      </if>
      <if test="goodsWidth != null">
        #{goodsWidth,jdbcType=DECIMAL},
      </if>
      <if test="goodsHeight != null">
        #{goodsHeight,jdbcType=DECIMAL},
      </if>
      <if test="packageLength != null">
        #{packageLength,jdbcType=DECIMAL},
      </if>
      <if test="packageWidth != null">
        #{packageWidth,jdbcType=DECIMAL},
      </if>
      <if test="packageHeight != null">
        #{packageHeight,jdbcType=DECIMAL},
      </if>
      <if test="netWeight != null">
        #{netWeight,jdbcType=DECIMAL},
      </if>
      <if test="grossWeight != null">
        #{grossWeight,jdbcType=DECIMAL},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="changeNum != null">
        #{changeNum,jdbcType=DECIMAL},
      </if>
      <if test="packingList != null and packingList != ''">
        #{packingList,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleContent != null and afterSaleContent != ''">
        #{afterSaleContent,jdbcType=VARCHAR},
      </if>
      <if test="qaYears != null and qaYears != ''">
        #{qaYears,jdbcType=VARCHAR},
      </if>
      <if test="storageConditionOne != null">
        #{storageConditionOne,jdbcType=BOOLEAN},
      </if>
      <if test="storageConditionOneLowerValue != null">
        #{storageConditionOneLowerValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionOneUpperValue != null">
        #{storageConditionOneUpperValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionHumidityLowerValue != null">
        #{storageConditionHumidityLowerValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionHumidityUpperValue != null">
        #{storageConditionHumidityUpperValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionTwo != null and storageConditionTwo != ''">
        #{storageConditionTwo,jdbcType=VARCHAR},
      </if>
      <if test="effectiveDayUnit != null">
        #{effectiveDayUnit,jdbcType=BOOLEAN},
      </if>
      <if test="effectiveDays != null and effectiveDays != ''">
        #{effectiveDays,jdbcType=VARCHAR},
      </if>
      <if test="qaRule != null and qaRule != ''">
        #{qaRule,jdbcType=VARCHAR},
      </if>
      <if test="qaOutPrice != null">
        #{qaOutPrice,jdbcType=DECIMAL},
      </if>
      <if test="qaResponseTime != null">
        #{qaResponseTime,jdbcType=DECIMAL},
      </if>
      <if test="hasBackupMachine != null and hasBackupMachine != ''">
        #{hasBackupMachine,jdbcType=VARCHAR},
      </if>
      <if test="supplierExtendGuaranteePrice != null">
        #{supplierExtendGuaranteePrice,jdbcType=DECIMAL},
      </if>
      <if test="corePartsPriceFid != null">
        #{corePartsPriceFid,jdbcType=INTEGER},
      </if>
      <if test="returnGoodsConditions != null">
        #{returnGoodsConditions,jdbcType=BOOLEAN},
      </if>
      <if test="freightIntroductions != null and freightIntroductions != ''">
        #{freightIntroductions,jdbcType=VARCHAR},
      </if>
      <if test="exchangeGoodsConditions != null and exchangeGoodsConditions != ''">
        #{exchangeGoodsConditions,jdbcType=VARCHAR},
      </if>
      <if test="exchangeGoodsMethod != null and exchangeGoodsMethod != ''">
        #{exchangeGoodsMethod,jdbcType=VARCHAR},
      </if>
      <if test="goodsComments != null and goodsComments != ''">
        #{goodsComments,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="checkTime != null">
        #{checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checker != null">
        #{checker,jdbcType=INTEGER},
      </if>
      <if test="operateInfoId != null">
        #{operateInfoId,jdbcType=INTEGER},
      </if>
      <if test="deleteReason != null and deleteReason != ''">
        #{deleteReason,jdbcType=VARCHAR},
      </if>
      <if test="lastCheckReason != null and lastCheckReason != ''">
        #{lastCheckReason,jdbcType=VARCHAR},
      </if>
      <if test="taxCategoryNo != null and taxCategoryNo != ''">
        #{taxCategoryNo,jdbcType=VARCHAR},
      </if>
      <if test="jxMarketPrice != null">
        #{jxMarketPrice,jdbcType=DECIMAL},
      </if>
      <if test="jxSalePrice != null">
        #{jxSalePrice,jdbcType=DECIMAL},
      </if>
      <if test="jxFlag != null">
        #{jxFlag,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=TINYINT},
      </if>
      <if test="pushStatus != null">
        #{pushStatus,jdbcType=INTEGER},
      </if>
      <if test="declareDeliveryRange != null and declareDeliveryRange != ''">
        #{declareDeliveryRange,jdbcType=VARCHAR},
      </if>
      <if test="priceVerifyStatus != null">
        #{priceVerifyStatus,jdbcType=INTEGER},
      </if>
      <if test="avgprice != null">
        #{avgprice,jdbcType=DECIMAL},
      </if>
      <if test="latestValidOrderUser != null">
        #{latestValidOrderUser,jdbcType=INTEGER},
      </if>
      <if test="avgpriceUpdateTime != null">
        #{avgpriceUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="terminalPrice != null">
        #{terminalPrice,jdbcType=DECIMAL},
      </if>
      <if test="distributionPrice != null">
        #{distributionPrice,jdbcType=DECIMAL},
      </if>
      <if test="costPrice != null">
        #{costPrice,jdbcType=DECIMAL},
      </if>
      <if test="availableStockNum != null">
        #{availableStockNum,jdbcType=INTEGER},
      </if>
      <if test="stockNum != null">
        #{stockNum,jdbcType=INTEGER},
      </if>
      <if test="onSale != null">
        #{onSale,jdbcType=INTEGER},
      </if>
      <if test="goodsBarcode != null and goodsBarcode != ''">
        #{goodsBarcode,jdbcType=VARCHAR},
      </if>
      <if test="curingType != null">
        #{curingType,jdbcType=BOOLEAN},
      </if>
      <if test="curingReason != null and curingReason != ''">
        #{curingReason,jdbcType=VARCHAR},
      </if>
      <if test="isNeedTestReprot != null">
        #{isNeedTestReprot,jdbcType=BOOLEAN},
      </if>
      <if test="isKit != null">
        #{isKit,jdbcType=BOOLEAN},
      </if>
      <if test="kitDesc != null and kitDesc != ''">
        #{kitDesc,jdbcType=VARCHAR},
      </if>
      <if test="isSameSnCode != null">
        #{isSameSnCode,jdbcType=BOOLEAN},
      </if>
      <if test="isFactorySnCode != null">
        #{isFactorySnCode,jdbcType=BOOLEAN},
      </if>
      <if test="isManageVedengCode != null">
        #{isManageVedengCode,jdbcType=BOOLEAN},
      </if>
      <if test="isBadGoods != null">
        #{isBadGoods,jdbcType=BOOLEAN},
      </if>
      <if test="isEnableFactoryBatchnum != null">
        #{isEnableFactoryBatchnum,jdbcType=BOOLEAN},
      </if>
      <if test="isEnableMultistagePackage != null">
        #{isEnableMultistagePackage,jdbcType=BOOLEAN},
      </if>
      <if test="midPackageNum != null">
        #{midPackageNum,jdbcType=INTEGER},
      </if>
      <if test="boxPackageNum != null">
        #{boxPackageNum,jdbcType=INTEGER},
      </if>
      <if test="isEnableValidityPeriod != null">
        #{isEnableValidityPeriod,jdbcType=TINYINT},
      </if>
      <if test="nearTermWarnDays != null">
        #{nearTermWarnDays,jdbcType=INTEGER},
      </if>
      <if test="overNearTermWarnDays != null">
        #{overNearTermWarnDays,jdbcType=INTEGER},
      </if>
      <if test="installTrainType != null">
        #{installTrainType,jdbcType=BOOLEAN},
      </if>
      <if test="logisticsDeliverytype != null">
        #{logisticsDeliverytype,jdbcType=BOOLEAN},
      </if>
      <if test="isNeedReport != null">
        #{isNeedReport,jdbcType=BOOLEAN},
      </if>
      <if test="isAuthorized != null">
        #{isAuthorized,jdbcType=BOOLEAN},
      </if>
      <if test="historyName != null and historyName != ''">
        #{historyName,jdbcType=VARCHAR},
      </if>
      <if test="isNameChange != null">
        #{isNameChange,jdbcType=TINYINT},
      </if>
      <if test="oneYearSaleNum != null">
        #{oneYearSaleNum,jdbcType=INTEGER},
      </if>
      <if test="regularMaintainType != null">
        #{regularMaintainType,jdbcType=BOOLEAN},
      </if>
      <if test="regularMaintainReason != null and regularMaintainReason != ''">
        #{regularMaintainReason,jdbcType=VARCHAR},
      </if>
      <if test="synchronizationStatus != null">
        #{synchronizationStatus,jdbcType=BOOLEAN},
      </if>
      <if test="isInstallable != null">
        #{isInstallable,jdbcType=BOOLEAN},
      </if>
      <if test="goodsLevelNo != null">
        #{goodsLevelNo,jdbcType=INTEGER},
      </if>
      <if test="goodsPositionNo != null">
        #{goodsPositionNo,jdbcType=INTEGER},
      </if>
      <if test="lastYearRatioEightySort != null">
        #{lastYearRatioEightySort,jdbcType=INTEGER},
      </if>
      <if test="threeMonthRatioEightySort != null">
        #{threeMonthRatioEightySort,jdbcType=INTEGER},
      </if>
      <if test="oneMonthRatioEightySort != null">
        #{oneMonthRatioEightySort,jdbcType=INTEGER},
      </if>
      <if test="orgIdList != null and orgIdList != ''">
        #{orgIdList,jdbcType=VARCHAR},
      </if>
      <if test="isAvailableSale != null">
        #{isAvailableSale,jdbcType=INTEGER},
      </if>
      <if test="pushedOrgIdList != null and pushedOrgIdList != ''">
        #{pushedOrgIdList,jdbcType=VARCHAR},
      </if>
      <if test="configurationList != null and configurationList != ''">
        #{configurationList,jdbcType=VARCHAR},
      </if>
      <if test="actionLockNum != null">
        #{actionLockNum,jdbcType=INTEGER},
      </if>
      <if test="orderOccupyNum != null">
        #{orderOccupyNum,jdbcType=INTEGER},
      </if>
      <if test="disabledReason != null and disabledReason != ''">
        #{disabledReason,jdbcType=VARCHAR},
      </if>
      <if test="purchaseTime != null">
        #{purchaseTime,jdbcType=INTEGER},
      </if>
      <if test="purchaseTimeUpdateTime != null">
        #{purchaseTimeUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="hasAfterSaleServiceLabel != null">
        #{hasAfterSaleServiceLabel,jdbcType=BOOLEAN},
      </if>
      <if test="spuType != null">
        #{spuType,jdbcType=INTEGER},
      </if>
      <if test="institutionLevelIds != null and institutionLevelIds != ''">
        #{institutionLevelIds,jdbcType=VARCHAR},
      </if>
      <if test="isSeven != null">
        #{isSeven,jdbcType=INTEGER},
      </if>
      <if test="haveStockManage != null">
        #{haveStockManage,jdbcType=BOOLEAN},
      </if>
      <if test="costCategoryId != null">
        #{costCategoryId,jdbcType=INTEGER},
      </if>
      <if test="isVirtureSku != null">
        #{isVirtureSku,jdbcType=BOOLEAN},
      </if>
      <if test="virtureTime != null">
        #{virtureTime,jdbcType=TIMESTAMP},
      </if>
      <if test="virtureCreator != null">
        #{virtureCreator,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchCoreSkuDto">
    <!--@mbg.generated-->
    update V_CORE_SKU
    <set>
      <if test="spuId != null">
        SPU_ID = #{spuId,jdbcType=INTEGER},
      </if>
      <if test="checkStatus != null">
        CHECK_STATUS = #{checkStatus,jdbcType=BOOLEAN},
      </if>
      <if test="model != null and model != ''">
        MODEL = #{model,jdbcType=VARCHAR},
      </if>
      <if test="spec != null and spec != ''">
        SPEC = #{spec,jdbcType=VARCHAR},
      </if>
      <if test="skuNo != null and skuNo != ''">
        SKU_NO = #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null and skuName != ''">
        SKU_NAME = #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="showName != null and showName != ''">
        SHOW_NAME = #{showName,jdbcType=VARCHAR},
      </if>
      <if test="materialCode != null and materialCode != ''">
        MATERIAL_CODE = #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="supplyModel != null and supplyModel != ''">
        SUPPLY_MODEL = #{supplyModel,jdbcType=VARCHAR},
      </if>
      <if test="isStockup != null and isStockup != ''">
        IS_STOCKUP = #{isStockup,jdbcType=VARCHAR},
      </if>
      <if test="wikiHref != null and wikiHref != ''">
        WIKI_HREF = #{wikiHref,jdbcType=VARCHAR},
      </if>
      <if test="technicalParameter != null and technicalParameter != ''">
        TECHNICAL_PARAMETER = #{technicalParameter,jdbcType=VARCHAR},
      </if>
      <if test="performanceParameter != null and performanceParameter != ''">
        PERFORMANCE_PARAMETER = #{performanceParameter,jdbcType=VARCHAR},
      </if>
      <if test="specParameter != null and specParameter != ''">
        SPEC_PARAMETER = #{specParameter,jdbcType=VARCHAR},
      </if>
      <if test="baseUnitId != null">
        BASE_UNIT_ID = #{baseUnitId,jdbcType=INTEGER},
      </if>
      <if test="minOrder != null">
        MIN_ORDER = #{minOrder,jdbcType=DECIMAL},
      </if>
      <if test="goodsLength != null">
        GOODS_LENGTH = #{goodsLength,jdbcType=DECIMAL},
      </if>
      <if test="goodsWidth != null">
        GOODS_WIDTH = #{goodsWidth,jdbcType=DECIMAL},
      </if>
      <if test="goodsHeight != null">
        GOODS_HEIGHT = #{goodsHeight,jdbcType=DECIMAL},
      </if>
      <if test="packageLength != null">
        PACKAGE_LENGTH = #{packageLength,jdbcType=DECIMAL},
      </if>
      <if test="packageWidth != null">
        PACKAGE_WIDTH = #{packageWidth,jdbcType=DECIMAL},
      </if>
      <if test="packageHeight != null">
        PACKAGE_HEIGHT = #{packageHeight,jdbcType=DECIMAL},
      </if>
      <if test="netWeight != null">
        NET_WEIGHT = #{netWeight,jdbcType=DECIMAL},
      </if>
      <if test="grossWeight != null">
        GROSS_WEIGHT = #{grossWeight,jdbcType=DECIMAL},
      </if>
      <if test="unitId != null">
        UNIT_ID = #{unitId,jdbcType=INTEGER},
      </if>
      <if test="changeNum != null">
        CHANGE_NUM = #{changeNum,jdbcType=DECIMAL},
      </if>
      <if test="packingList != null and packingList != ''">
        PACKING_LIST = #{packingList,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleContent != null and afterSaleContent != ''">
        AFTER_SALE_CONTENT = #{afterSaleContent,jdbcType=VARCHAR},
      </if>
      <if test="qaYears != null and qaYears != ''">
        QA_YEARS = #{qaYears,jdbcType=VARCHAR},
      </if>
      <if test="storageConditionOne != null">
        STORAGE_CONDITION_ONE = #{storageConditionOne,jdbcType=BOOLEAN},
      </if>
      <if test="storageConditionOneLowerValue != null">
        STORAGE_CONDITION_ONE_LOWER_VALUE = #{storageConditionOneLowerValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionOneUpperValue != null">
        STORAGE_CONDITION_ONE_UPPER_VALUE = #{storageConditionOneUpperValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionHumidityLowerValue != null">
        STORAGE_CONDITION_HUMIDITY_LOWER_VALUE = #{storageConditionHumidityLowerValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionHumidityUpperValue != null">
        STORAGE_CONDITION_HUMIDITY_UPPER_VALUE = #{storageConditionHumidityUpperValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionTwo != null and storageConditionTwo != ''">
        STORAGE_CONDITION_TWO = #{storageConditionTwo,jdbcType=VARCHAR},
      </if>
      <if test="effectiveDayUnit != null">
        EFFECTIVE_DAY_UNIT = #{effectiveDayUnit,jdbcType=BOOLEAN},
      </if>
      <if test="effectiveDays != null and effectiveDays != ''">
        EFFECTIVE_DAYS = #{effectiveDays,jdbcType=VARCHAR},
      </if>
      <if test="qaRule != null and qaRule != ''">
        QA_RULE = #{qaRule,jdbcType=VARCHAR},
      </if>
      <if test="qaOutPrice != null">
        QA_OUT_PRICE = #{qaOutPrice,jdbcType=DECIMAL},
      </if>
      <if test="qaResponseTime != null">
        QA_RESPONSE_TIME = #{qaResponseTime,jdbcType=DECIMAL},
      </if>
      <if test="hasBackupMachine != null and hasBackupMachine != ''">
        HAS_BACKUP_MACHINE = #{hasBackupMachine,jdbcType=VARCHAR},
      </if>
      <if test="supplierExtendGuaranteePrice != null">
        SUPPLIER_EXTEND_GUARANTEE_PRICE = #{supplierExtendGuaranteePrice,jdbcType=DECIMAL},
      </if>
      <if test="corePartsPriceFid != null">
        CORE_PARTS_PRICE_FID = #{corePartsPriceFid,jdbcType=INTEGER},
      </if>
      <if test="returnGoodsConditions != null">
        RETURN_GOODS_CONDITIONS = #{returnGoodsConditions,jdbcType=BOOLEAN},
      </if>
      <if test="freightIntroductions != null and freightIntroductions != ''">
        FREIGHT_INTRODUCTIONS = #{freightIntroductions,jdbcType=VARCHAR},
      </if>
      <if test="exchangeGoodsConditions != null and exchangeGoodsConditions != ''">
        EXCHANGE_GOODS_CONDITIONS = #{exchangeGoodsConditions,jdbcType=VARCHAR},
      </if>
      <if test="exchangeGoodsMethod != null and exchangeGoodsMethod != ''">
        EXCHANGE_GOODS_METHOD = #{exchangeGoodsMethod,jdbcType=VARCHAR},
      </if>
      <if test="goodsComments != null and goodsComments != ''">
        GOODS_COMMENTS = #{goodsComments,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `STATUS` = #{status,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="checkTime != null">
        CHECK_TIME = #{checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checker != null">
        CHECKER = #{checker,jdbcType=INTEGER},
      </if>
      <if test="operateInfoId != null">
        OPERATE_INFO_ID = #{operateInfoId,jdbcType=INTEGER},
      </if>
      <if test="deleteReason != null and deleteReason != ''">
        DELETE_REASON = #{deleteReason,jdbcType=VARCHAR},
      </if>
      <if test="lastCheckReason != null and lastCheckReason != ''">
        LAST_CHECK_REASON = #{lastCheckReason,jdbcType=VARCHAR},
      </if>
      <if test="taxCategoryNo != null and taxCategoryNo != ''">
        TAX_CATEGORY_NO = #{taxCategoryNo,jdbcType=VARCHAR},
      </if>
      <if test="jxMarketPrice != null">
        JX_MARKET_PRICE = #{jxMarketPrice,jdbcType=DECIMAL},
      </if>
      <if test="jxSalePrice != null">
        JX_SALE_PRICE = #{jxSalePrice,jdbcType=DECIMAL},
      </if>
      <if test="jxFlag != null">
        JX_FLAG = #{jxFlag,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        `SOURCE` = #{source,jdbcType=TINYINT},
      </if>
      <if test="pushStatus != null">
        PUSH_STATUS = #{pushStatus,jdbcType=INTEGER},
      </if>
      <if test="declareDeliveryRange != null and declareDeliveryRange != ''">
        DECLARE_DELIVERY_RANGE = #{declareDeliveryRange,jdbcType=VARCHAR},
      </if>
      <if test="priceVerifyStatus != null">
        PRICE_VERIFY_STATUS = #{priceVerifyStatus,jdbcType=INTEGER},
      </if>
      <if test="avgprice != null">
        AVGPRICE = #{avgprice,jdbcType=DECIMAL},
      </if>
      <if test="latestValidOrderUser != null">
        LATEST_VALID_ORDER_USER = #{latestValidOrderUser,jdbcType=INTEGER},
      </if>
      <if test="avgpriceUpdateTime != null">
        AVGPRICE_UPDATE_TIME = #{avgpriceUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="terminalPrice != null">
        TERMINAL_PRICE = #{terminalPrice,jdbcType=DECIMAL},
      </if>
      <if test="distributionPrice != null">
        DISTRIBUTION_PRICE = #{distributionPrice,jdbcType=DECIMAL},
      </if>
      <if test="costPrice != null">
        COST_PRICE = #{costPrice,jdbcType=DECIMAL},
      </if>
      <if test="availableStockNum != null">
        AVAILABLE_STOCK_NUM = #{availableStockNum,jdbcType=INTEGER},
      </if>
      <if test="stockNum != null">
        STOCK_NUM = #{stockNum,jdbcType=INTEGER},
      </if>
      <if test="onSale != null">
        ON_SALE = #{onSale,jdbcType=INTEGER},
      </if>
      <if test="goodsBarcode != null and goodsBarcode != ''">
        GOODS_BARCODE = #{goodsBarcode,jdbcType=VARCHAR},
      </if>
      <if test="curingType != null">
        CURING_TYPE = #{curingType,jdbcType=BOOLEAN},
      </if>
      <if test="curingReason != null and curingReason != ''">
        CURING_REASON = #{curingReason,jdbcType=VARCHAR},
      </if>
      <if test="isNeedTestReprot != null">
        IS_NEED_TEST_REPROT = #{isNeedTestReprot,jdbcType=BOOLEAN},
      </if>
      <if test="isKit != null">
        IS_KIT = #{isKit,jdbcType=BOOLEAN},
      </if>
      <if test="kitDesc != null and kitDesc != ''">
        KIT_DESC = #{kitDesc,jdbcType=VARCHAR},
      </if>
      <if test="isSameSnCode != null">
        IS_SAME_SN_CODE = #{isSameSnCode,jdbcType=BOOLEAN},
      </if>
      <if test="isFactorySnCode != null">
        IS_FACTORY_SN_CODE = #{isFactorySnCode,jdbcType=BOOLEAN},
      </if>
      <if test="isManageVedengCode != null">
        IS_MANAGE_VEDENG_CODE = #{isManageVedengCode,jdbcType=BOOLEAN},
      </if>
      <if test="isBadGoods != null">
        IS_BAD_GOODS = #{isBadGoods,jdbcType=BOOLEAN},
      </if>
      <if test="isEnableFactoryBatchnum != null">
        IS_ENABLE_FACTORY_BATCHNUM = #{isEnableFactoryBatchnum,jdbcType=BOOLEAN},
      </if>
      <if test="isEnableMultistagePackage != null">
        IS_ENABLE_MULTISTAGE_PACKAGE = #{isEnableMultistagePackage,jdbcType=BOOLEAN},
      </if>
      <if test="midPackageNum != null">
        MID_PACKAGE_NUM = #{midPackageNum,jdbcType=INTEGER},
      </if>
      <if test="boxPackageNum != null">
        BOX_PACKAGE_NUM = #{boxPackageNum,jdbcType=INTEGER},
      </if>
      <if test="isEnableValidityPeriod != null">
        IS_ENABLE_VALIDITY_PERIOD = #{isEnableValidityPeriod,jdbcType=TINYINT},
      </if>
      <if test="nearTermWarnDays != null">
        NEAR_TERM_WARN_DAYS = #{nearTermWarnDays,jdbcType=INTEGER},
      </if>
      <if test="overNearTermWarnDays != null">
        OVER_NEAR_TERM_WARN_DAYS = #{overNearTermWarnDays,jdbcType=INTEGER},
      </if>
      <if test="installTrainType != null">
        INSTALL_TRAIN_TYPE = #{installTrainType,jdbcType=BOOLEAN},
      </if>
      <if test="logisticsDeliverytype != null">
        LOGISTICS_DELIVERYTYPE = #{logisticsDeliverytype,jdbcType=BOOLEAN},
      </if>
      <if test="isNeedReport != null">
        IS_NEED_REPORT = #{isNeedReport,jdbcType=BOOLEAN},
      </if>
      <if test="isAuthorized != null">
        IS_AUTHORIZED = #{isAuthorized,jdbcType=BOOLEAN},
      </if>
      <if test="historyName != null and historyName != ''">
        HISTORY_NAME = #{historyName,jdbcType=VARCHAR},
      </if>
      <if test="isNameChange != null">
        IS_NAME_CHANGE = #{isNameChange,jdbcType=TINYINT},
      </if>
      <if test="oneYearSaleNum != null">
        ONE_YEAR_SALE_NUM = #{oneYearSaleNum,jdbcType=INTEGER},
      </if>
      <if test="regularMaintainType != null">
        REGULAR_MAINTAIN_TYPE = #{regularMaintainType,jdbcType=BOOLEAN},
      </if>
      <if test="regularMaintainReason != null and regularMaintainReason != ''">
        REGULAR_MAINTAIN_REASON = #{regularMaintainReason,jdbcType=VARCHAR},
      </if>
      <if test="synchronizationStatus != null">
        SYNCHRONIZATION_STATUS = #{synchronizationStatus,jdbcType=BOOLEAN},
      </if>
      <if test="isInstallable != null">
        IS_INSTALLABLE = #{isInstallable,jdbcType=BOOLEAN},
      </if>
      <if test="goodsLevelNo != null">
        GOODS_LEVEL_NO = #{goodsLevelNo,jdbcType=INTEGER},
      </if>
      <if test="goodsPositionNo != null">
        GOODS_POSITION_NO = #{goodsPositionNo,jdbcType=INTEGER},
      </if>
      <if test="lastYearRatioEightySort != null">
        LAST_YEAR_RATIO_EIGHTY_SORT = #{lastYearRatioEightySort,jdbcType=INTEGER},
      </if>
      <if test="threeMonthRatioEightySort != null">
        THREE_MONTH_RATIO_EIGHTY_SORT = #{threeMonthRatioEightySort,jdbcType=INTEGER},
      </if>
      <if test="oneMonthRatioEightySort != null">
        ONE_MONTH_RATIO_EIGHTY_SORT = #{oneMonthRatioEightySort,jdbcType=INTEGER},
      </if>
      <if test="orgIdList != null and orgIdList != ''">
        ORG_ID_LIST = #{orgIdList,jdbcType=VARCHAR},
      </if>
      <if test="isAvailableSale != null">
        IS_AVAILABLE_SALE = #{isAvailableSale,jdbcType=INTEGER},
      </if>
      <if test="pushedOrgIdList != null and pushedOrgIdList != ''">
        PUSHED_ORG_ID_LIST = #{pushedOrgIdList,jdbcType=VARCHAR},
      </if>
      <if test="configurationList != null and configurationList != ''">
        CONFIGURATION_LIST = #{configurationList,jdbcType=VARCHAR},
      </if>
      <if test="actionLockNum != null">
        ACTION_LOCK_NUM = #{actionLockNum,jdbcType=INTEGER},
      </if>
      <if test="orderOccupyNum != null">
        ORDER_OCCUPY_NUM = #{orderOccupyNum,jdbcType=INTEGER},
      </if>
      <if test="disabledReason != null and disabledReason != ''">
        DISABLED_REASON = #{disabledReason,jdbcType=VARCHAR},
      </if>
      <if test="purchaseTime != null">
        PURCHASE_TIME = #{purchaseTime,jdbcType=INTEGER},
      </if>
      <if test="purchaseTimeUpdateTime != null">
        PURCHASE_TIME_UPDATE_TIME = #{purchaseTimeUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="hasAfterSaleServiceLabel != null">
        HAS_AFTER_SALE_SERVICE_LABEL = #{hasAfterSaleServiceLabel,jdbcType=BOOLEAN},
      </if>
      <if test="spuType != null">
        SPU_TYPE = #{spuType,jdbcType=INTEGER},
      </if>
      <if test="institutionLevelIds != null and institutionLevelIds != ''">
        INSTITUTION_LEVEL_IDS = #{institutionLevelIds,jdbcType=VARCHAR},
      </if>
      <if test="isSeven != null">
        IS_SEVEN = #{isSeven,jdbcType=INTEGER},
      </if>
      <if test="haveStockManage != null">
        HAVE_STOCK_MANAGE = #{haveStockManage,jdbcType=BOOLEAN},
      </if>
      <if test="costCategoryId != null">
        COST_CATEGORY_ID = #{costCategoryId,jdbcType=INTEGER},
      </if>
      <if test="isVirtureSku != null">
        IS_VIRTURE_SKU = #{isVirtureSku,jdbcType=BOOLEAN},
      </if>
      <if test="virtureTime != null">
        VIRTURE_TIME = #{virtureTime,jdbcType=TIMESTAMP},
      </if>
      <if test="virtureCreator != null">
        VIRTURE_CREATOR = #{virtureCreator,jdbcType=INTEGER},
      </if>
    </set>
    where SKU_ID = #{skuId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchCoreSkuDto">
    <!--@mbg.generated-->
    update V_CORE_SKU
    set SPU_ID = #{spuId,jdbcType=INTEGER},
      CHECK_STATUS = #{checkStatus,jdbcType=BOOLEAN},
      MODEL = #{model,jdbcType=VARCHAR},
      SPEC = #{spec,jdbcType=VARCHAR},
      SKU_NO = #{skuNo,jdbcType=VARCHAR},
      SKU_NAME = #{skuName,jdbcType=VARCHAR},
      SHOW_NAME = #{showName,jdbcType=VARCHAR},
      MATERIAL_CODE = #{materialCode,jdbcType=VARCHAR},
      SUPPLY_MODEL = #{supplyModel,jdbcType=VARCHAR},
      IS_STOCKUP = #{isStockup,jdbcType=VARCHAR},
      WIKI_HREF = #{wikiHref,jdbcType=VARCHAR},
      TECHNICAL_PARAMETER = #{technicalParameter,jdbcType=VARCHAR},
      PERFORMANCE_PARAMETER = #{performanceParameter,jdbcType=VARCHAR},
      SPEC_PARAMETER = #{specParameter,jdbcType=VARCHAR},
      BASE_UNIT_ID = #{baseUnitId,jdbcType=INTEGER},
      MIN_ORDER = #{minOrder,jdbcType=DECIMAL},
      GOODS_LENGTH = #{goodsLength,jdbcType=DECIMAL},
      GOODS_WIDTH = #{goodsWidth,jdbcType=DECIMAL},
      GOODS_HEIGHT = #{goodsHeight,jdbcType=DECIMAL},
      PACKAGE_LENGTH = #{packageLength,jdbcType=DECIMAL},
      PACKAGE_WIDTH = #{packageWidth,jdbcType=DECIMAL},
      PACKAGE_HEIGHT = #{packageHeight,jdbcType=DECIMAL},
      NET_WEIGHT = #{netWeight,jdbcType=DECIMAL},
      GROSS_WEIGHT = #{grossWeight,jdbcType=DECIMAL},
      UNIT_ID = #{unitId,jdbcType=INTEGER},
      CHANGE_NUM = #{changeNum,jdbcType=DECIMAL},
      PACKING_LIST = #{packingList,jdbcType=VARCHAR},
      AFTER_SALE_CONTENT = #{afterSaleContent,jdbcType=VARCHAR},
      QA_YEARS = #{qaYears,jdbcType=VARCHAR},
      STORAGE_CONDITION_ONE = #{storageConditionOne,jdbcType=BOOLEAN},
      STORAGE_CONDITION_ONE_LOWER_VALUE = #{storageConditionOneLowerValue,jdbcType=FLOAT},
      STORAGE_CONDITION_ONE_UPPER_VALUE = #{storageConditionOneUpperValue,jdbcType=FLOAT},
      STORAGE_CONDITION_HUMIDITY_LOWER_VALUE = #{storageConditionHumidityLowerValue,jdbcType=FLOAT},
      STORAGE_CONDITION_HUMIDITY_UPPER_VALUE = #{storageConditionHumidityUpperValue,jdbcType=FLOAT},
      STORAGE_CONDITION_TWO = #{storageConditionTwo,jdbcType=VARCHAR},
      EFFECTIVE_DAY_UNIT = #{effectiveDayUnit,jdbcType=BOOLEAN},
      EFFECTIVE_DAYS = #{effectiveDays,jdbcType=VARCHAR},
      QA_RULE = #{qaRule,jdbcType=VARCHAR},
      QA_OUT_PRICE = #{qaOutPrice,jdbcType=DECIMAL},
      QA_RESPONSE_TIME = #{qaResponseTime,jdbcType=DECIMAL},
      HAS_BACKUP_MACHINE = #{hasBackupMachine,jdbcType=VARCHAR},
      SUPPLIER_EXTEND_GUARANTEE_PRICE = #{supplierExtendGuaranteePrice,jdbcType=DECIMAL},
      CORE_PARTS_PRICE_FID = #{corePartsPriceFid,jdbcType=INTEGER},
      RETURN_GOODS_CONDITIONS = #{returnGoodsConditions,jdbcType=BOOLEAN},
      FREIGHT_INTRODUCTIONS = #{freightIntroductions,jdbcType=VARCHAR},
      EXCHANGE_GOODS_CONDITIONS = #{exchangeGoodsConditions,jdbcType=VARCHAR},
      EXCHANGE_GOODS_METHOD = #{exchangeGoodsMethod,jdbcType=VARCHAR},
      GOODS_COMMENTS = #{goodsComments,jdbcType=VARCHAR},
      `STATUS` = #{status,jdbcType=BOOLEAN},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      CHECK_TIME = #{checkTime,jdbcType=TIMESTAMP},
      CHECKER = #{checker,jdbcType=INTEGER},
      OPERATE_INFO_ID = #{operateInfoId,jdbcType=INTEGER},
      DELETE_REASON = #{deleteReason,jdbcType=VARCHAR},
      LAST_CHECK_REASON = #{lastCheckReason,jdbcType=VARCHAR},
      TAX_CATEGORY_NO = #{taxCategoryNo,jdbcType=VARCHAR},
      JX_MARKET_PRICE = #{jxMarketPrice,jdbcType=DECIMAL},
      JX_SALE_PRICE = #{jxSalePrice,jdbcType=DECIMAL},
      JX_FLAG = #{jxFlag,jdbcType=INTEGER},
      `SOURCE` = #{source,jdbcType=TINYINT},
      PUSH_STATUS = #{pushStatus,jdbcType=INTEGER},
      DECLARE_DELIVERY_RANGE = #{declareDeliveryRange,jdbcType=VARCHAR},
      PRICE_VERIFY_STATUS = #{priceVerifyStatus,jdbcType=INTEGER},
      AVGPRICE = #{avgprice,jdbcType=DECIMAL},
      LATEST_VALID_ORDER_USER = #{latestValidOrderUser,jdbcType=INTEGER},
      AVGPRICE_UPDATE_TIME = #{avgpriceUpdateTime,jdbcType=TIMESTAMP},
      TERMINAL_PRICE = #{terminalPrice,jdbcType=DECIMAL},
      DISTRIBUTION_PRICE = #{distributionPrice,jdbcType=DECIMAL},
      COST_PRICE = #{costPrice,jdbcType=DECIMAL},
      AVAILABLE_STOCK_NUM = #{availableStockNum,jdbcType=INTEGER},
      STOCK_NUM = #{stockNum,jdbcType=INTEGER},
      ON_SALE = #{onSale,jdbcType=INTEGER},
      GOODS_BARCODE = #{goodsBarcode,jdbcType=VARCHAR},
      CURING_TYPE = #{curingType,jdbcType=BOOLEAN},
      CURING_REASON = #{curingReason,jdbcType=VARCHAR},
      IS_NEED_TEST_REPROT = #{isNeedTestReprot,jdbcType=BOOLEAN},
      IS_KIT = #{isKit,jdbcType=BOOLEAN},
      KIT_DESC = #{kitDesc,jdbcType=VARCHAR},
      IS_SAME_SN_CODE = #{isSameSnCode,jdbcType=BOOLEAN},
      IS_FACTORY_SN_CODE = #{isFactorySnCode,jdbcType=BOOLEAN},
      IS_MANAGE_VEDENG_CODE = #{isManageVedengCode,jdbcType=BOOLEAN},
      IS_BAD_GOODS = #{isBadGoods,jdbcType=BOOLEAN},
      IS_ENABLE_FACTORY_BATCHNUM = #{isEnableFactoryBatchnum,jdbcType=BOOLEAN},
      IS_ENABLE_MULTISTAGE_PACKAGE = #{isEnableMultistagePackage,jdbcType=BOOLEAN},
      MID_PACKAGE_NUM = #{midPackageNum,jdbcType=INTEGER},
      BOX_PACKAGE_NUM = #{boxPackageNum,jdbcType=INTEGER},
      IS_ENABLE_VALIDITY_PERIOD = #{isEnableValidityPeriod,jdbcType=TINYINT},
      NEAR_TERM_WARN_DAYS = #{nearTermWarnDays,jdbcType=INTEGER},
      OVER_NEAR_TERM_WARN_DAYS = #{overNearTermWarnDays,jdbcType=INTEGER},
      INSTALL_TRAIN_TYPE = #{installTrainType,jdbcType=BOOLEAN},
      LOGISTICS_DELIVERYTYPE = #{logisticsDeliverytype,jdbcType=BOOLEAN},
      IS_NEED_REPORT = #{isNeedReport,jdbcType=BOOLEAN},
      IS_AUTHORIZED = #{isAuthorized,jdbcType=BOOLEAN},
      HISTORY_NAME = #{historyName,jdbcType=VARCHAR},
      IS_NAME_CHANGE = #{isNameChange,jdbcType=TINYINT},
      ONE_YEAR_SALE_NUM = #{oneYearSaleNum,jdbcType=INTEGER},
      REGULAR_MAINTAIN_TYPE = #{regularMaintainType,jdbcType=BOOLEAN},
      REGULAR_MAINTAIN_REASON = #{regularMaintainReason,jdbcType=VARCHAR},
      SYNCHRONIZATION_STATUS = #{synchronizationStatus,jdbcType=BOOLEAN},
      IS_INSTALLABLE = #{isInstallable,jdbcType=BOOLEAN},
      GOODS_LEVEL_NO = #{goodsLevelNo,jdbcType=INTEGER},
      GOODS_POSITION_NO = #{goodsPositionNo,jdbcType=INTEGER},
      LAST_YEAR_RATIO_EIGHTY_SORT = #{lastYearRatioEightySort,jdbcType=INTEGER},
      THREE_MONTH_RATIO_EIGHTY_SORT = #{threeMonthRatioEightySort,jdbcType=INTEGER},
      ONE_MONTH_RATIO_EIGHTY_SORT = #{oneMonthRatioEightySort,jdbcType=INTEGER},
      ORG_ID_LIST = #{orgIdList,jdbcType=VARCHAR},
      IS_AVAILABLE_SALE = #{isAvailableSale,jdbcType=INTEGER},
      PUSHED_ORG_ID_LIST = #{pushedOrgIdList,jdbcType=VARCHAR},
      CONFIGURATION_LIST = #{configurationList,jdbcType=VARCHAR},
      ACTION_LOCK_NUM = #{actionLockNum,jdbcType=INTEGER},
      ORDER_OCCUPY_NUM = #{orderOccupyNum,jdbcType=INTEGER},
      DISABLED_REASON = #{disabledReason,jdbcType=VARCHAR},
      PURCHASE_TIME = #{purchaseTime,jdbcType=INTEGER},
      PURCHASE_TIME_UPDATE_TIME = #{purchaseTimeUpdateTime,jdbcType=TIMESTAMP},
      HAS_AFTER_SALE_SERVICE_LABEL = #{hasAfterSaleServiceLabel,jdbcType=BOOLEAN},
      SPU_TYPE = #{spuType,jdbcType=INTEGER},
      INSTITUTION_LEVEL_IDS = #{institutionLevelIds,jdbcType=VARCHAR},
      IS_SEVEN = #{isSeven,jdbcType=INTEGER},
      HAVE_STOCK_MANAGE = #{haveStockManage,jdbcType=BOOLEAN},
      COST_CATEGORY_ID = #{costCategoryId,jdbcType=INTEGER},
      IS_VIRTURE_SKU = #{isVirtureSku,jdbcType=BOOLEAN},
      VIRTURE_TIME = #{virtureTime,jdbcType=TIMESTAMP},
      VIRTURE_CREATOR = #{virtureCreator,jdbcType=INTEGER}
    where SKU_ID = #{skuId,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update V_CORE_SKU
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="SPU_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.spuId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CHECK_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.checkStatus,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="MODEL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.model,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="SPEC = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.spec,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="SKU_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.skuNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="SKU_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.skuName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="SHOW_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.showName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="MATERIAL_CODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.materialCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="SUPPLY_MODEL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.supplyModel,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="IS_STOCKUP = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.isStockup,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="WIKI_HREF = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.wikiHref,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="TECHNICAL_PARAMETER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.technicalParameter,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="PERFORMANCE_PARAMETER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.performanceParameter,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="SPEC_PARAMETER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.specParameter,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="BASE_UNIT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.baseUnitId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="MIN_ORDER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.minOrder,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="GOODS_LENGTH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.goodsLength,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="GOODS_WIDTH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.goodsWidth,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="GOODS_HEIGHT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.goodsHeight,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="PACKAGE_LENGTH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.packageLength,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="PACKAGE_WIDTH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.packageWidth,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="PACKAGE_HEIGHT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.packageHeight,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="NET_WEIGHT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.netWeight,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="GROSS_WEIGHT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.grossWeight,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="UNIT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.unitId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CHANGE_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.changeNum,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="PACKING_LIST = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.packingList,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="AFTER_SALE_CONTENT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.afterSaleContent,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="QA_YEARS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.qaYears,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="STORAGE_CONDITION_ONE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.storageConditionOne,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="STORAGE_CONDITION_ONE_LOWER_VALUE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.storageConditionOneLowerValue,jdbcType=FLOAT}
        </foreach>
      </trim>
      <trim prefix="STORAGE_CONDITION_ONE_UPPER_VALUE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.storageConditionOneUpperValue,jdbcType=FLOAT}
        </foreach>
      </trim>
      <trim prefix="STORAGE_CONDITION_HUMIDITY_LOWER_VALUE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.storageConditionHumidityLowerValue,jdbcType=FLOAT}
        </foreach>
      </trim>
      <trim prefix="STORAGE_CONDITION_HUMIDITY_UPPER_VALUE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.storageConditionHumidityUpperValue,jdbcType=FLOAT}
        </foreach>
      </trim>
      <trim prefix="STORAGE_CONDITION_TWO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.storageConditionTwo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="EFFECTIVE_DAY_UNIT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.effectiveDayUnit,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="EFFECTIVE_DAYS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.effectiveDays,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="QA_RULE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.qaRule,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="QA_OUT_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.qaOutPrice,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="QA_RESPONSE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.qaResponseTime,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="HAS_BACKUP_MACHINE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.hasBackupMachine,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="SUPPLIER_EXTEND_GUARANTEE_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.supplierExtendGuaranteePrice,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="CORE_PARTS_PRICE_FID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.corePartsPriceFid,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="RETURN_GOODS_CONDITIONS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.returnGoodsConditions,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="FREIGHT_INTRODUCTIONS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.freightIntroductions,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="EXCHANGE_GOODS_CONDITIONS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.exchangeGoodsConditions,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="EXCHANGE_GOODS_METHOD = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.exchangeGoodsMethod,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="GOODS_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.goodsComments,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`STATUS` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.status,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CHECK_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.checkTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="CHECKER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.checker,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="OPERATE_INFO_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.operateInfoId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="DELETE_REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.deleteReason,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="LAST_CHECK_REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.lastCheckReason,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="TAX_CATEGORY_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.taxCategoryNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="JX_MARKET_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.jxMarketPrice,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="JX_SALE_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.jxSalePrice,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="JX_FLAG = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.jxFlag,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="`SOURCE` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.source,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="PUSH_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.pushStatus,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="DECLARE_DELIVERY_RANGE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.declareDeliveryRange,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="PRICE_VERIFY_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.priceVerifyStatus,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="AVGPRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.avgprice,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="LATEST_VALID_ORDER_USER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.latestValidOrderUser,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="AVGPRICE_UPDATE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.avgpriceUpdateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="TERMINAL_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.terminalPrice,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="DISTRIBUTION_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.distributionPrice,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="COST_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.costPrice,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="AVAILABLE_STOCK_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.availableStockNum,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="STOCK_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.stockNum,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ON_SALE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.onSale,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="GOODS_BARCODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.goodsBarcode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="CURING_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.curingType,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="CURING_REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.curingReason,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="IS_NEED_TEST_REPROT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.isNeedTestReprot,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="IS_KIT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.isKit,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="KIT_DESC = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.kitDesc,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="IS_SAME_SN_CODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.isSameSnCode,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="IS_FACTORY_SN_CODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.isFactorySnCode,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="IS_MANAGE_VEDENG_CODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.isManageVedengCode,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="IS_BAD_GOODS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.isBadGoods,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="IS_ENABLE_FACTORY_BATCHNUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.isEnableFactoryBatchnum,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="IS_ENABLE_MULTISTAGE_PACKAGE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.isEnableMultistagePackage,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="MID_PACKAGE_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.midPackageNum,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="BOX_PACKAGE_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.boxPackageNum,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="IS_ENABLE_VALIDITY_PERIOD = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.isEnableValidityPeriod,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="NEAR_TERM_WARN_DAYS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.nearTermWarnDays,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="OVER_NEAR_TERM_WARN_DAYS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.overNearTermWarnDays,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="INSTALL_TRAIN_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.installTrainType,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="LOGISTICS_DELIVERYTYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.logisticsDeliverytype,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="IS_NEED_REPORT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.isNeedReport,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="IS_AUTHORIZED = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.isAuthorized,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="HISTORY_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.historyName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="IS_NAME_CHANGE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.isNameChange,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="ONE_YEAR_SALE_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.oneYearSaleNum,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="REGULAR_MAINTAIN_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.regularMaintainType,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="REGULAR_MAINTAIN_REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.regularMaintainReason,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="SYNCHRONIZATION_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.synchronizationStatus,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="IS_INSTALLABLE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.isInstallable,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="GOODS_LEVEL_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.goodsLevelNo,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="GOODS_POSITION_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.goodsPositionNo,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="LAST_YEAR_RATIO_EIGHTY_SORT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.lastYearRatioEightySort,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="THREE_MONTH_RATIO_EIGHTY_SORT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.threeMonthRatioEightySort,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ONE_MONTH_RATIO_EIGHTY_SORT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.oneMonthRatioEightySort,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ORG_ID_LIST = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.orgIdList,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="IS_AVAILABLE_SALE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.isAvailableSale,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="PUSHED_ORG_ID_LIST = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.pushedOrgIdList,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="CONFIGURATION_LIST = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.configurationList,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ACTION_LOCK_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.actionLockNum,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ORDER_OCCUPY_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.orderOccupyNum,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="DISABLED_REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.disabledReason,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="PURCHASE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.purchaseTime,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="PURCHASE_TIME_UPDATE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.purchaseTimeUpdateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="HAS_AFTER_SALE_SERVICE_LABEL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.hasAfterSaleServiceLabel,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="SPU_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.spuType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="INSTITUTION_LEVEL_IDS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.institutionLevelIds,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="IS_SEVEN = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.isSeven,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="HAVE_STOCK_MANAGE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.haveStockManage,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="COST_CATEGORY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.costCategoryId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="IS_VIRTURE_SKU = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.isVirtureSku,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="VIRTURE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.virtureTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="VIRTURE_CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.virtureCreator,jdbcType=INTEGER}
        </foreach>
      </trim>
    </trim>
    where SKU_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.skuId,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update V_CORE_SKU
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="SPU_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.spuId != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.spuId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CHECK_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.checkStatus != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.checkStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="MODEL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.model != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.model,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SPEC = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.spec != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.spec,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SKU_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.skuNo != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.skuNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SKU_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.skuName != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.skuName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SHOW_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.showName != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.showName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="MATERIAL_CODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.materialCode != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.materialCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SUPPLY_MODEL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.supplyModel != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.supplyModel,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_STOCKUP = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isStockup != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.isStockup,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="WIKI_HREF = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.wikiHref != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.wikiHref,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TECHNICAL_PARAMETER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.technicalParameter != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.technicalParameter,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="PERFORMANCE_PARAMETER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.performanceParameter != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.performanceParameter,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SPEC_PARAMETER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.specParameter != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.specParameter,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="BASE_UNIT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.baseUnitId != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.baseUnitId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="MIN_ORDER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.minOrder != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.minOrder,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="GOODS_LENGTH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.goodsLength != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.goodsLength,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="GOODS_WIDTH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.goodsWidth != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.goodsWidth,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="GOODS_HEIGHT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.goodsHeight != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.goodsHeight,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="PACKAGE_LENGTH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.packageLength != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.packageLength,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="PACKAGE_WIDTH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.packageWidth != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.packageWidth,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="PACKAGE_HEIGHT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.packageHeight != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.packageHeight,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="NET_WEIGHT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.netWeight != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.netWeight,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="GROSS_WEIGHT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.grossWeight != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.grossWeight,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="UNIT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.unitId != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.unitId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CHANGE_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.changeNum != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.changeNum,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="PACKING_LIST = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.packingList != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.packingList,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="AFTER_SALE_CONTENT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.afterSaleContent != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.afterSaleContent,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="QA_YEARS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.qaYears != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.qaYears,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="STORAGE_CONDITION_ONE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.storageConditionOne != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.storageConditionOne,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="STORAGE_CONDITION_ONE_LOWER_VALUE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.storageConditionOneLowerValue != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.storageConditionOneLowerValue,jdbcType=FLOAT}
          </if>
        </foreach>
      </trim>
      <trim prefix="STORAGE_CONDITION_ONE_UPPER_VALUE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.storageConditionOneUpperValue != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.storageConditionOneUpperValue,jdbcType=FLOAT}
          </if>
        </foreach>
      </trim>
      <trim prefix="STORAGE_CONDITION_HUMIDITY_LOWER_VALUE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.storageConditionHumidityLowerValue != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.storageConditionHumidityLowerValue,jdbcType=FLOAT}
          </if>
        </foreach>
      </trim>
      <trim prefix="STORAGE_CONDITION_HUMIDITY_UPPER_VALUE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.storageConditionHumidityUpperValue != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.storageConditionHumidityUpperValue,jdbcType=FLOAT}
          </if>
        </foreach>
      </trim>
      <trim prefix="STORAGE_CONDITION_TWO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.storageConditionTwo != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.storageConditionTwo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="EFFECTIVE_DAY_UNIT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.effectiveDayUnit != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.effectiveDayUnit,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="EFFECTIVE_DAYS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.effectiveDays != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.effectiveDays,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="QA_RULE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.qaRule != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.qaRule,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="QA_OUT_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.qaOutPrice != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.qaOutPrice,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="QA_RESPONSE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.qaResponseTime != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.qaResponseTime,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="HAS_BACKUP_MACHINE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.hasBackupMachine != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.hasBackupMachine,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SUPPLIER_EXTEND_GUARANTEE_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.supplierExtendGuaranteePrice != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.supplierExtendGuaranteePrice,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="CORE_PARTS_PRICE_FID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.corePartsPriceFid != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.corePartsPriceFid,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="RETURN_GOODS_CONDITIONS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.returnGoodsConditions != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.returnGoodsConditions,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="FREIGHT_INTRODUCTIONS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.freightIntroductions != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.freightIntroductions,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="EXCHANGE_GOODS_CONDITIONS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.exchangeGoodsConditions != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.exchangeGoodsConditions,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="EXCHANGE_GOODS_METHOD = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.exchangeGoodsMethod != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.exchangeGoodsMethod,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="GOODS_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.goodsComments != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.goodsComments,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`STATUS` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.status != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.status,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CHECK_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.checkTime != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.checkTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CHECKER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.checker != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.checker,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="OPERATE_INFO_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.operateInfoId != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.operateInfoId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="DELETE_REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deleteReason != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.deleteReason,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="LAST_CHECK_REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.lastCheckReason != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.lastCheckReason,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TAX_CATEGORY_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.taxCategoryNo != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.taxCategoryNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="JX_MARKET_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.jxMarketPrice != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.jxMarketPrice,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="JX_SALE_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.jxSalePrice != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.jxSalePrice,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="JX_FLAG = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.jxFlag != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.jxFlag,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="`SOURCE` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.source != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.source,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="PUSH_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.pushStatus != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.pushStatus,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="DECLARE_DELIVERY_RANGE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.declareDeliveryRange != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.declareDeliveryRange,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="PRICE_VERIFY_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.priceVerifyStatus != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.priceVerifyStatus,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="AVGPRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.avgprice != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.avgprice,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="LATEST_VALID_ORDER_USER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.latestValidOrderUser != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.latestValidOrderUser,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="AVGPRICE_UPDATE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.avgpriceUpdateTime != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.avgpriceUpdateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="TERMINAL_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.terminalPrice != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.terminalPrice,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="DISTRIBUTION_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.distributionPrice != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.distributionPrice,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="COST_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.costPrice != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.costPrice,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="AVAILABLE_STOCK_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.availableStockNum != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.availableStockNum,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="STOCK_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.stockNum != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.stockNum,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ON_SALE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.onSale != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.onSale,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="GOODS_BARCODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.goodsBarcode != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.goodsBarcode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="CURING_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.curingType != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.curingType,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="CURING_REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.curingReason != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.curingReason,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_NEED_TEST_REPROT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isNeedTestReprot != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.isNeedTestReprot,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_KIT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isKit != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.isKit,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="KIT_DESC = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.kitDesc != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.kitDesc,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_SAME_SN_CODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isSameSnCode != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.isSameSnCode,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_FACTORY_SN_CODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isFactorySnCode != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.isFactorySnCode,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_MANAGE_VEDENG_CODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isManageVedengCode != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.isManageVedengCode,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_BAD_GOODS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isBadGoods != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.isBadGoods,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_ENABLE_FACTORY_BATCHNUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isEnableFactoryBatchnum != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.isEnableFactoryBatchnum,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_ENABLE_MULTISTAGE_PACKAGE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isEnableMultistagePackage != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.isEnableMultistagePackage,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="MID_PACKAGE_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.midPackageNum != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.midPackageNum,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="BOX_PACKAGE_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.boxPackageNum != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.boxPackageNum,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_ENABLE_VALIDITY_PERIOD = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isEnableValidityPeriod != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.isEnableValidityPeriod,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="NEAR_TERM_WARN_DAYS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.nearTermWarnDays != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.nearTermWarnDays,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="OVER_NEAR_TERM_WARN_DAYS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.overNearTermWarnDays != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.overNearTermWarnDays,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="INSTALL_TRAIN_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.installTrainType != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.installTrainType,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="LOGISTICS_DELIVERYTYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.logisticsDeliverytype != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.logisticsDeliverytype,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_NEED_REPORT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isNeedReport != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.isNeedReport,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_AUTHORIZED = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isAuthorized != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.isAuthorized,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="HISTORY_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.historyName != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.historyName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_NAME_CHANGE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isNameChange != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.isNameChange,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="ONE_YEAR_SALE_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.oneYearSaleNum != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.oneYearSaleNum,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="REGULAR_MAINTAIN_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.regularMaintainType != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.regularMaintainType,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="REGULAR_MAINTAIN_REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.regularMaintainReason != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.regularMaintainReason,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SYNCHRONIZATION_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.synchronizationStatus != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.synchronizationStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_INSTALLABLE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isInstallable != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.isInstallable,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="GOODS_LEVEL_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.goodsLevelNo != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.goodsLevelNo,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="GOODS_POSITION_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.goodsPositionNo != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.goodsPositionNo,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="LAST_YEAR_RATIO_EIGHTY_SORT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.lastYearRatioEightySort != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.lastYearRatioEightySort,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="THREE_MONTH_RATIO_EIGHTY_SORT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.threeMonthRatioEightySort != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.threeMonthRatioEightySort,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ONE_MONTH_RATIO_EIGHTY_SORT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.oneMonthRatioEightySort != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.oneMonthRatioEightySort,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ORG_ID_LIST = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orgIdList != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.orgIdList,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_AVAILABLE_SALE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isAvailableSale != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.isAvailableSale,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="PUSHED_ORG_ID_LIST = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.pushedOrgIdList != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.pushedOrgIdList,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="CONFIGURATION_LIST = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.configurationList != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.configurationList,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ACTION_LOCK_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.actionLockNum != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.actionLockNum,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ORDER_OCCUPY_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orderOccupyNum != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.orderOccupyNum,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="DISABLED_REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.disabledReason != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.disabledReason,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="PURCHASE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.purchaseTime != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.purchaseTime,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="PURCHASE_TIME_UPDATE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.purchaseTimeUpdateTime != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.purchaseTimeUpdateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="HAS_AFTER_SALE_SERVICE_LABEL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.hasAfterSaleServiceLabel != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.hasAfterSaleServiceLabel,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="SPU_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.spuType != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.spuType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="INSTITUTION_LEVEL_IDS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.institutionLevelIds != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.institutionLevelIds,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_SEVEN = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isSeven != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.isSeven,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="HAVE_STOCK_MANAGE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.haveStockManage != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.haveStockManage,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="COST_CATEGORY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.costCategoryId != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.costCategoryId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_VIRTURE_SKU = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isVirtureSku != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.isVirtureSku,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="VIRTURE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.virtureTime != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.virtureTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="VIRTURE_CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.virtureCreator != null">
            when SKU_ID = #{item.skuId,jdbcType=INTEGER} then #{item.virtureCreator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
    </trim>
    where SKU_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.skuId,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="SKU_ID" keyProperty="skuId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into V_CORE_SKU
    (SPU_ID, CHECK_STATUS, MODEL, SPEC, SKU_NO, SKU_NAME, SHOW_NAME, MATERIAL_CODE, SUPPLY_MODEL, 
      IS_STOCKUP, WIKI_HREF, TECHNICAL_PARAMETER, PERFORMANCE_PARAMETER, SPEC_PARAMETER, 
      BASE_UNIT_ID, MIN_ORDER, GOODS_LENGTH, GOODS_WIDTH, GOODS_HEIGHT, PACKAGE_LENGTH, 
      PACKAGE_WIDTH, PACKAGE_HEIGHT, NET_WEIGHT, GROSS_WEIGHT, UNIT_ID, CHANGE_NUM, PACKING_LIST, 
      AFTER_SALE_CONTENT, QA_YEARS, STORAGE_CONDITION_ONE, STORAGE_CONDITION_ONE_LOWER_VALUE, 
      STORAGE_CONDITION_ONE_UPPER_VALUE, STORAGE_CONDITION_HUMIDITY_LOWER_VALUE, STORAGE_CONDITION_HUMIDITY_UPPER_VALUE, 
      STORAGE_CONDITION_TWO, EFFECTIVE_DAY_UNIT, EFFECTIVE_DAYS, QA_RULE, QA_OUT_PRICE, 
      QA_RESPONSE_TIME, HAS_BACKUP_MACHINE, SUPPLIER_EXTEND_GUARANTEE_PRICE, CORE_PARTS_PRICE_FID, 
      RETURN_GOODS_CONDITIONS, FREIGHT_INTRODUCTIONS, EXCHANGE_GOODS_CONDITIONS, EXCHANGE_GOODS_METHOD, 
      GOODS_COMMENTS, `STATUS`, ADD_TIME, CREATOR, MOD_TIME, UPDATER, CHECK_TIME, CHECKER, 
      OPERATE_INFO_ID, DELETE_REASON, LAST_CHECK_REASON, TAX_CATEGORY_NO, JX_MARKET_PRICE, 
      JX_SALE_PRICE, JX_FLAG, `SOURCE`, PUSH_STATUS, DECLARE_DELIVERY_RANGE, PRICE_VERIFY_STATUS, 
      AVGPRICE, LATEST_VALID_ORDER_USER, AVGPRICE_UPDATE_TIME, TERMINAL_PRICE, DISTRIBUTION_PRICE, 
      COST_PRICE, AVAILABLE_STOCK_NUM, STOCK_NUM, ON_SALE, GOODS_BARCODE, CURING_TYPE, 
      CURING_REASON, IS_NEED_TEST_REPROT, IS_KIT, KIT_DESC, IS_SAME_SN_CODE, IS_FACTORY_SN_CODE, 
      IS_MANAGE_VEDENG_CODE, IS_BAD_GOODS, IS_ENABLE_FACTORY_BATCHNUM, IS_ENABLE_MULTISTAGE_PACKAGE, 
      MID_PACKAGE_NUM, BOX_PACKAGE_NUM, IS_ENABLE_VALIDITY_PERIOD, NEAR_TERM_WARN_DAYS, 
      OVER_NEAR_TERM_WARN_DAYS, INSTALL_TRAIN_TYPE, LOGISTICS_DELIVERYTYPE, IS_NEED_REPORT, 
      IS_AUTHORIZED, HISTORY_NAME, IS_NAME_CHANGE, ONE_YEAR_SALE_NUM, REGULAR_MAINTAIN_TYPE, 
      REGULAR_MAINTAIN_REASON, SYNCHRONIZATION_STATUS, IS_INSTALLABLE, GOODS_LEVEL_NO, 
      GOODS_POSITION_NO, LAST_YEAR_RATIO_EIGHTY_SORT, THREE_MONTH_RATIO_EIGHTY_SORT, 
      ONE_MONTH_RATIO_EIGHTY_SORT, ORG_ID_LIST, IS_AVAILABLE_SALE, PUSHED_ORG_ID_LIST, 
      CONFIGURATION_LIST, ACTION_LOCK_NUM, ORDER_OCCUPY_NUM, DISABLED_REASON, PURCHASE_TIME, 
      PURCHASE_TIME_UPDATE_TIME, HAS_AFTER_SALE_SERVICE_LABEL, SPU_TYPE, INSTITUTION_LEVEL_IDS, 
      IS_SEVEN, HAVE_STOCK_MANAGE, COST_CATEGORY_ID, IS_VIRTURE_SKU, VIRTURE_TIME, VIRTURE_CREATOR
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.spuId,jdbcType=INTEGER}, #{item.checkStatus,jdbcType=BOOLEAN}, #{item.model,jdbcType=VARCHAR}, 
        #{item.spec,jdbcType=VARCHAR}, #{item.skuNo,jdbcType=VARCHAR}, #{item.skuName,jdbcType=VARCHAR}, 
        #{item.showName,jdbcType=VARCHAR}, #{item.materialCode,jdbcType=VARCHAR}, #{item.supplyModel,jdbcType=VARCHAR}, 
        #{item.isStockup,jdbcType=VARCHAR}, #{item.wikiHref,jdbcType=VARCHAR}, #{item.technicalParameter,jdbcType=VARCHAR}, 
        #{item.performanceParameter,jdbcType=VARCHAR}, #{item.specParameter,jdbcType=VARCHAR}, 
        #{item.baseUnitId,jdbcType=INTEGER}, #{item.minOrder,jdbcType=DECIMAL}, #{item.goodsLength,jdbcType=DECIMAL}, 
        #{item.goodsWidth,jdbcType=DECIMAL}, #{item.goodsHeight,jdbcType=DECIMAL}, #{item.packageLength,jdbcType=DECIMAL}, 
        #{item.packageWidth,jdbcType=DECIMAL}, #{item.packageHeight,jdbcType=DECIMAL}, 
        #{item.netWeight,jdbcType=DECIMAL}, #{item.grossWeight,jdbcType=DECIMAL}, #{item.unitId,jdbcType=INTEGER}, 
        #{item.changeNum,jdbcType=DECIMAL}, #{item.packingList,jdbcType=VARCHAR}, #{item.afterSaleContent,jdbcType=VARCHAR}, 
        #{item.qaYears,jdbcType=VARCHAR}, #{item.storageConditionOne,jdbcType=BOOLEAN}, 
        #{item.storageConditionOneLowerValue,jdbcType=FLOAT}, #{item.storageConditionOneUpperValue,jdbcType=FLOAT}, 
        #{item.storageConditionHumidityLowerValue,jdbcType=FLOAT}, #{item.storageConditionHumidityUpperValue,jdbcType=FLOAT}, 
        #{item.storageConditionTwo,jdbcType=VARCHAR}, #{item.effectiveDayUnit,jdbcType=BOOLEAN}, 
        #{item.effectiveDays,jdbcType=VARCHAR}, #{item.qaRule,jdbcType=VARCHAR}, #{item.qaOutPrice,jdbcType=DECIMAL}, 
        #{item.qaResponseTime,jdbcType=DECIMAL}, #{item.hasBackupMachine,jdbcType=VARCHAR}, 
        #{item.supplierExtendGuaranteePrice,jdbcType=DECIMAL}, #{item.corePartsPriceFid,jdbcType=INTEGER}, 
        #{item.returnGoodsConditions,jdbcType=BOOLEAN}, #{item.freightIntroductions,jdbcType=VARCHAR}, 
        #{item.exchangeGoodsConditions,jdbcType=VARCHAR}, #{item.exchangeGoodsMethod,jdbcType=VARCHAR}, 
        #{item.goodsComments,jdbcType=VARCHAR}, #{item.status,jdbcType=BOOLEAN}, #{item.addTime,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=INTEGER}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.updater,jdbcType=INTEGER}, 
        #{item.checkTime,jdbcType=TIMESTAMP}, #{item.checker,jdbcType=INTEGER}, #{item.operateInfoId,jdbcType=INTEGER}, 
        #{item.deleteReason,jdbcType=VARCHAR}, #{item.lastCheckReason,jdbcType=VARCHAR}, 
        #{item.taxCategoryNo,jdbcType=VARCHAR}, #{item.jxMarketPrice,jdbcType=DECIMAL}, 
        #{item.jxSalePrice,jdbcType=DECIMAL}, #{item.jxFlag,jdbcType=INTEGER}, #{item.source,jdbcType=TINYINT}, 
        #{item.pushStatus,jdbcType=INTEGER}, #{item.declareDeliveryRange,jdbcType=VARCHAR}, 
        #{item.priceVerifyStatus,jdbcType=INTEGER}, #{item.avgprice,jdbcType=DECIMAL}, 
        #{item.latestValidOrderUser,jdbcType=INTEGER}, #{item.avgpriceUpdateTime,jdbcType=TIMESTAMP}, 
        #{item.terminalPrice,jdbcType=DECIMAL}, #{item.distributionPrice,jdbcType=DECIMAL}, 
        #{item.costPrice,jdbcType=DECIMAL}, #{item.availableStockNum,jdbcType=INTEGER}, 
        #{item.stockNum,jdbcType=INTEGER}, #{item.onSale,jdbcType=INTEGER}, #{item.goodsBarcode,jdbcType=VARCHAR}, 
        #{item.curingType,jdbcType=BOOLEAN}, #{item.curingReason,jdbcType=VARCHAR}, #{item.isNeedTestReprot,jdbcType=BOOLEAN}, 
        #{item.isKit,jdbcType=BOOLEAN}, #{item.kitDesc,jdbcType=VARCHAR}, #{item.isSameSnCode,jdbcType=BOOLEAN}, 
        #{item.isFactorySnCode,jdbcType=BOOLEAN}, #{item.isManageVedengCode,jdbcType=BOOLEAN}, 
        #{item.isBadGoods,jdbcType=BOOLEAN}, #{item.isEnableFactoryBatchnum,jdbcType=BOOLEAN}, 
        #{item.isEnableMultistagePackage,jdbcType=BOOLEAN}, #{item.midPackageNum,jdbcType=INTEGER}, 
        #{item.boxPackageNum,jdbcType=INTEGER}, #{item.isEnableValidityPeriod,jdbcType=TINYINT}, 
        #{item.nearTermWarnDays,jdbcType=INTEGER}, #{item.overNearTermWarnDays,jdbcType=INTEGER}, 
        #{item.installTrainType,jdbcType=BOOLEAN}, #{item.logisticsDeliverytype,jdbcType=BOOLEAN}, 
        #{item.isNeedReport,jdbcType=BOOLEAN}, #{item.isAuthorized,jdbcType=BOOLEAN}, #{item.historyName,jdbcType=VARCHAR}, 
        #{item.isNameChange,jdbcType=TINYINT}, #{item.oneYearSaleNum,jdbcType=INTEGER}, 
        #{item.regularMaintainType,jdbcType=BOOLEAN}, #{item.regularMaintainReason,jdbcType=VARCHAR}, 
        #{item.synchronizationStatus,jdbcType=BOOLEAN}, #{item.isInstallable,jdbcType=BOOLEAN}, 
        #{item.goodsLevelNo,jdbcType=INTEGER}, #{item.goodsPositionNo,jdbcType=INTEGER}, 
        #{item.lastYearRatioEightySort,jdbcType=INTEGER}, #{item.threeMonthRatioEightySort,jdbcType=INTEGER}, 
        #{item.oneMonthRatioEightySort,jdbcType=INTEGER}, #{item.orgIdList,jdbcType=VARCHAR}, 
        #{item.isAvailableSale,jdbcType=INTEGER}, #{item.pushedOrgIdList,jdbcType=VARCHAR}, 
        #{item.configurationList,jdbcType=VARCHAR}, #{item.actionLockNum,jdbcType=INTEGER}, 
        #{item.orderOccupyNum,jdbcType=INTEGER}, #{item.disabledReason,jdbcType=VARCHAR}, 
        #{item.purchaseTime,jdbcType=INTEGER}, #{item.purchaseTimeUpdateTime,jdbcType=TIMESTAMP}, 
        #{item.hasAfterSaleServiceLabel,jdbcType=BOOLEAN}, #{item.spuType,jdbcType=INTEGER}, 
        #{item.institutionLevelIds,jdbcType=VARCHAR}, #{item.isSeven,jdbcType=INTEGER}, 
        #{item.haveStockManage,jdbcType=BOOLEAN}, #{item.costCategoryId,jdbcType=INTEGER}, 
        #{item.isVirtureSku,jdbcType=BOOLEAN}, #{item.virtureTime,jdbcType=TIMESTAMP}, 
        #{item.virtureCreator,jdbcType=INTEGER})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="SKU_ID" keyProperty="skuId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchCoreSkuDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into V_CORE_SKU
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="skuId != null">
        SKU_ID,
      </if>
      SPU_ID,
      CHECK_STATUS,
      MODEL,
      SPEC,
      SKU_NO,
      SKU_NAME,
      SHOW_NAME,
      MATERIAL_CODE,
      SUPPLY_MODEL,
      IS_STOCKUP,
      WIKI_HREF,
      TECHNICAL_PARAMETER,
      PERFORMANCE_PARAMETER,
      SPEC_PARAMETER,
      BASE_UNIT_ID,
      MIN_ORDER,
      GOODS_LENGTH,
      GOODS_WIDTH,
      GOODS_HEIGHT,
      PACKAGE_LENGTH,
      PACKAGE_WIDTH,
      PACKAGE_HEIGHT,
      NET_WEIGHT,
      GROSS_WEIGHT,
      UNIT_ID,
      CHANGE_NUM,
      PACKING_LIST,
      AFTER_SALE_CONTENT,
      QA_YEARS,
      STORAGE_CONDITION_ONE,
      STORAGE_CONDITION_ONE_LOWER_VALUE,
      STORAGE_CONDITION_ONE_UPPER_VALUE,
      STORAGE_CONDITION_HUMIDITY_LOWER_VALUE,
      STORAGE_CONDITION_HUMIDITY_UPPER_VALUE,
      STORAGE_CONDITION_TWO,
      EFFECTIVE_DAY_UNIT,
      EFFECTIVE_DAYS,
      QA_RULE,
      QA_OUT_PRICE,
      QA_RESPONSE_TIME,
      HAS_BACKUP_MACHINE,
      SUPPLIER_EXTEND_GUARANTEE_PRICE,
      CORE_PARTS_PRICE_FID,
      RETURN_GOODS_CONDITIONS,
      FREIGHT_INTRODUCTIONS,
      EXCHANGE_GOODS_CONDITIONS,
      EXCHANGE_GOODS_METHOD,
      GOODS_COMMENTS,
      `STATUS`,
      ADD_TIME,
      CREATOR,
      MOD_TIME,
      UPDATER,
      CHECK_TIME,
      CHECKER,
      OPERATE_INFO_ID,
      DELETE_REASON,
      LAST_CHECK_REASON,
      TAX_CATEGORY_NO,
      JX_MARKET_PRICE,
      JX_SALE_PRICE,
      JX_FLAG,
      `SOURCE`,
      PUSH_STATUS,
      DECLARE_DELIVERY_RANGE,
      PRICE_VERIFY_STATUS,
      AVGPRICE,
      LATEST_VALID_ORDER_USER,
      AVGPRICE_UPDATE_TIME,
      TERMINAL_PRICE,
      DISTRIBUTION_PRICE,
      COST_PRICE,
      AVAILABLE_STOCK_NUM,
      STOCK_NUM,
      ON_SALE,
      GOODS_BARCODE,
      CURING_TYPE,
      CURING_REASON,
      IS_NEED_TEST_REPROT,
      IS_KIT,
      KIT_DESC,
      IS_SAME_SN_CODE,
      IS_FACTORY_SN_CODE,
      IS_MANAGE_VEDENG_CODE,
      IS_BAD_GOODS,
      IS_ENABLE_FACTORY_BATCHNUM,
      IS_ENABLE_MULTISTAGE_PACKAGE,
      MID_PACKAGE_NUM,
      BOX_PACKAGE_NUM,
      IS_ENABLE_VALIDITY_PERIOD,
      NEAR_TERM_WARN_DAYS,
      OVER_NEAR_TERM_WARN_DAYS,
      INSTALL_TRAIN_TYPE,
      LOGISTICS_DELIVERYTYPE,
      IS_NEED_REPORT,
      IS_AUTHORIZED,
      HISTORY_NAME,
      IS_NAME_CHANGE,
      ONE_YEAR_SALE_NUM,
      REGULAR_MAINTAIN_TYPE,
      REGULAR_MAINTAIN_REASON,
      SYNCHRONIZATION_STATUS,
      IS_INSTALLABLE,
      GOODS_LEVEL_NO,
      GOODS_POSITION_NO,
      LAST_YEAR_RATIO_EIGHTY_SORT,
      THREE_MONTH_RATIO_EIGHTY_SORT,
      ONE_MONTH_RATIO_EIGHTY_SORT,
      ORG_ID_LIST,
      IS_AVAILABLE_SALE,
      PUSHED_ORG_ID_LIST,
      CONFIGURATION_LIST,
      ACTION_LOCK_NUM,
      ORDER_OCCUPY_NUM,
      DISABLED_REASON,
      PURCHASE_TIME,
      PURCHASE_TIME_UPDATE_TIME,
      HAS_AFTER_SALE_SERVICE_LABEL,
      SPU_TYPE,
      INSTITUTION_LEVEL_IDS,
      IS_SEVEN,
      HAVE_STOCK_MANAGE,
      COST_CATEGORY_ID,
      IS_VIRTURE_SKU,
      VIRTURE_TIME,
      VIRTURE_CREATOR,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="skuId != null">
        #{skuId,jdbcType=INTEGER},
      </if>
      #{spuId,jdbcType=INTEGER},
      #{checkStatus,jdbcType=BOOLEAN},
      #{model,jdbcType=VARCHAR},
      #{spec,jdbcType=VARCHAR},
      #{skuNo,jdbcType=VARCHAR},
      #{skuName,jdbcType=VARCHAR},
      #{showName,jdbcType=VARCHAR},
      #{materialCode,jdbcType=VARCHAR},
      #{supplyModel,jdbcType=VARCHAR},
      #{isStockup,jdbcType=VARCHAR},
      #{wikiHref,jdbcType=VARCHAR},
      #{technicalParameter,jdbcType=VARCHAR},
      #{performanceParameter,jdbcType=VARCHAR},
      #{specParameter,jdbcType=VARCHAR},
      #{baseUnitId,jdbcType=INTEGER},
      #{minOrder,jdbcType=DECIMAL},
      #{goodsLength,jdbcType=DECIMAL},
      #{goodsWidth,jdbcType=DECIMAL},
      #{goodsHeight,jdbcType=DECIMAL},
      #{packageLength,jdbcType=DECIMAL},
      #{packageWidth,jdbcType=DECIMAL},
      #{packageHeight,jdbcType=DECIMAL},
      #{netWeight,jdbcType=DECIMAL},
      #{grossWeight,jdbcType=DECIMAL},
      #{unitId,jdbcType=INTEGER},
      #{changeNum,jdbcType=DECIMAL},
      #{packingList,jdbcType=VARCHAR},
      #{afterSaleContent,jdbcType=VARCHAR},
      #{qaYears,jdbcType=VARCHAR},
      #{storageConditionOne,jdbcType=BOOLEAN},
      #{storageConditionOneLowerValue,jdbcType=FLOAT},
      #{storageConditionOneUpperValue,jdbcType=FLOAT},
      #{storageConditionHumidityLowerValue,jdbcType=FLOAT},
      #{storageConditionHumidityUpperValue,jdbcType=FLOAT},
      #{storageConditionTwo,jdbcType=VARCHAR},
      #{effectiveDayUnit,jdbcType=BOOLEAN},
      #{effectiveDays,jdbcType=VARCHAR},
      #{qaRule,jdbcType=VARCHAR},
      #{qaOutPrice,jdbcType=DECIMAL},
      #{qaResponseTime,jdbcType=DECIMAL},
      #{hasBackupMachine,jdbcType=VARCHAR},
      #{supplierExtendGuaranteePrice,jdbcType=DECIMAL},
      #{corePartsPriceFid,jdbcType=INTEGER},
      #{returnGoodsConditions,jdbcType=BOOLEAN},
      #{freightIntroductions,jdbcType=VARCHAR},
      #{exchangeGoodsConditions,jdbcType=VARCHAR},
      #{exchangeGoodsMethod,jdbcType=VARCHAR},
      #{goodsComments,jdbcType=VARCHAR},
      #{status,jdbcType=BOOLEAN},
      #{addTime,jdbcType=TIMESTAMP},
      #{creator,jdbcType=INTEGER},
      #{modTime,jdbcType=TIMESTAMP},
      #{updater,jdbcType=INTEGER},
      #{checkTime,jdbcType=TIMESTAMP},
      #{checker,jdbcType=INTEGER},
      #{operateInfoId,jdbcType=INTEGER},
      #{deleteReason,jdbcType=VARCHAR},
      #{lastCheckReason,jdbcType=VARCHAR},
      #{taxCategoryNo,jdbcType=VARCHAR},
      #{jxMarketPrice,jdbcType=DECIMAL},
      #{jxSalePrice,jdbcType=DECIMAL},
      #{jxFlag,jdbcType=INTEGER},
      #{source,jdbcType=TINYINT},
      #{pushStatus,jdbcType=INTEGER},
      #{declareDeliveryRange,jdbcType=VARCHAR},
      #{priceVerifyStatus,jdbcType=INTEGER},
      #{avgprice,jdbcType=DECIMAL},
      #{latestValidOrderUser,jdbcType=INTEGER},
      #{avgpriceUpdateTime,jdbcType=TIMESTAMP},
      #{terminalPrice,jdbcType=DECIMAL},
      #{distributionPrice,jdbcType=DECIMAL},
      #{costPrice,jdbcType=DECIMAL},
      #{availableStockNum,jdbcType=INTEGER},
      #{stockNum,jdbcType=INTEGER},
      #{onSale,jdbcType=INTEGER},
      #{goodsBarcode,jdbcType=VARCHAR},
      #{curingType,jdbcType=BOOLEAN},
      #{curingReason,jdbcType=VARCHAR},
      #{isNeedTestReprot,jdbcType=BOOLEAN},
      #{isKit,jdbcType=BOOLEAN},
      #{kitDesc,jdbcType=VARCHAR},
      #{isSameSnCode,jdbcType=BOOLEAN},
      #{isFactorySnCode,jdbcType=BOOLEAN},
      #{isManageVedengCode,jdbcType=BOOLEAN},
      #{isBadGoods,jdbcType=BOOLEAN},
      #{isEnableFactoryBatchnum,jdbcType=BOOLEAN},
      #{isEnableMultistagePackage,jdbcType=BOOLEAN},
      #{midPackageNum,jdbcType=INTEGER},
      #{boxPackageNum,jdbcType=INTEGER},
      #{isEnableValidityPeriod,jdbcType=TINYINT},
      #{nearTermWarnDays,jdbcType=INTEGER},
      #{overNearTermWarnDays,jdbcType=INTEGER},
      #{installTrainType,jdbcType=BOOLEAN},
      #{logisticsDeliverytype,jdbcType=BOOLEAN},
      #{isNeedReport,jdbcType=BOOLEAN},
      #{isAuthorized,jdbcType=BOOLEAN},
      #{historyName,jdbcType=VARCHAR},
      #{isNameChange,jdbcType=TINYINT},
      #{oneYearSaleNum,jdbcType=INTEGER},
      #{regularMaintainType,jdbcType=BOOLEAN},
      #{regularMaintainReason,jdbcType=VARCHAR},
      #{synchronizationStatus,jdbcType=BOOLEAN},
      #{isInstallable,jdbcType=BOOLEAN},
      #{goodsLevelNo,jdbcType=INTEGER},
      #{goodsPositionNo,jdbcType=INTEGER},
      #{lastYearRatioEightySort,jdbcType=INTEGER},
      #{threeMonthRatioEightySort,jdbcType=INTEGER},
      #{oneMonthRatioEightySort,jdbcType=INTEGER},
      #{orgIdList,jdbcType=VARCHAR},
      #{isAvailableSale,jdbcType=INTEGER},
      #{pushedOrgIdList,jdbcType=VARCHAR},
      #{configurationList,jdbcType=VARCHAR},
      #{actionLockNum,jdbcType=INTEGER},
      #{orderOccupyNum,jdbcType=INTEGER},
      #{disabledReason,jdbcType=VARCHAR},
      #{purchaseTime,jdbcType=INTEGER},
      #{purchaseTimeUpdateTime,jdbcType=TIMESTAMP},
      #{hasAfterSaleServiceLabel,jdbcType=BOOLEAN},
      #{spuType,jdbcType=INTEGER},
      #{institutionLevelIds,jdbcType=VARCHAR},
      #{isSeven,jdbcType=INTEGER},
      #{haveStockManage,jdbcType=BOOLEAN},
      #{costCategoryId,jdbcType=INTEGER},
      #{isVirtureSku,jdbcType=BOOLEAN},
      #{virtureTime,jdbcType=TIMESTAMP},
      #{virtureCreator,jdbcType=INTEGER},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="skuId != null">
        SKU_ID = #{skuId,jdbcType=INTEGER},
      </if>
      SPU_ID = #{spuId,jdbcType=INTEGER},
      CHECK_STATUS = #{checkStatus,jdbcType=BOOLEAN},
      MODEL = #{model,jdbcType=VARCHAR},
      SPEC = #{spec,jdbcType=VARCHAR},
      SKU_NO = #{skuNo,jdbcType=VARCHAR},
      SKU_NAME = #{skuName,jdbcType=VARCHAR},
      SHOW_NAME = #{showName,jdbcType=VARCHAR},
      MATERIAL_CODE = #{materialCode,jdbcType=VARCHAR},
      SUPPLY_MODEL = #{supplyModel,jdbcType=VARCHAR},
      IS_STOCKUP = #{isStockup,jdbcType=VARCHAR},
      WIKI_HREF = #{wikiHref,jdbcType=VARCHAR},
      TECHNICAL_PARAMETER = #{technicalParameter,jdbcType=VARCHAR},
      PERFORMANCE_PARAMETER = #{performanceParameter,jdbcType=VARCHAR},
      SPEC_PARAMETER = #{specParameter,jdbcType=VARCHAR},
      BASE_UNIT_ID = #{baseUnitId,jdbcType=INTEGER},
      MIN_ORDER = #{minOrder,jdbcType=DECIMAL},
      GOODS_LENGTH = #{goodsLength,jdbcType=DECIMAL},
      GOODS_WIDTH = #{goodsWidth,jdbcType=DECIMAL},
      GOODS_HEIGHT = #{goodsHeight,jdbcType=DECIMAL},
      PACKAGE_LENGTH = #{packageLength,jdbcType=DECIMAL},
      PACKAGE_WIDTH = #{packageWidth,jdbcType=DECIMAL},
      PACKAGE_HEIGHT = #{packageHeight,jdbcType=DECIMAL},
      NET_WEIGHT = #{netWeight,jdbcType=DECIMAL},
      GROSS_WEIGHT = #{grossWeight,jdbcType=DECIMAL},
      UNIT_ID = #{unitId,jdbcType=INTEGER},
      CHANGE_NUM = #{changeNum,jdbcType=DECIMAL},
      PACKING_LIST = #{packingList,jdbcType=VARCHAR},
      AFTER_SALE_CONTENT = #{afterSaleContent,jdbcType=VARCHAR},
      QA_YEARS = #{qaYears,jdbcType=VARCHAR},
      STORAGE_CONDITION_ONE = #{storageConditionOne,jdbcType=BOOLEAN},
      STORAGE_CONDITION_ONE_LOWER_VALUE = #{storageConditionOneLowerValue,jdbcType=FLOAT},
      STORAGE_CONDITION_ONE_UPPER_VALUE = #{storageConditionOneUpperValue,jdbcType=FLOAT},
      STORAGE_CONDITION_HUMIDITY_LOWER_VALUE = #{storageConditionHumidityLowerValue,jdbcType=FLOAT},
      STORAGE_CONDITION_HUMIDITY_UPPER_VALUE = #{storageConditionHumidityUpperValue,jdbcType=FLOAT},
      STORAGE_CONDITION_TWO = #{storageConditionTwo,jdbcType=VARCHAR},
      EFFECTIVE_DAY_UNIT = #{effectiveDayUnit,jdbcType=BOOLEAN},
      EFFECTIVE_DAYS = #{effectiveDays,jdbcType=VARCHAR},
      QA_RULE = #{qaRule,jdbcType=VARCHAR},
      QA_OUT_PRICE = #{qaOutPrice,jdbcType=DECIMAL},
      QA_RESPONSE_TIME = #{qaResponseTime,jdbcType=DECIMAL},
      HAS_BACKUP_MACHINE = #{hasBackupMachine,jdbcType=VARCHAR},
      SUPPLIER_EXTEND_GUARANTEE_PRICE = #{supplierExtendGuaranteePrice,jdbcType=DECIMAL},
      CORE_PARTS_PRICE_FID = #{corePartsPriceFid,jdbcType=INTEGER},
      RETURN_GOODS_CONDITIONS = #{returnGoodsConditions,jdbcType=BOOLEAN},
      FREIGHT_INTRODUCTIONS = #{freightIntroductions,jdbcType=VARCHAR},
      EXCHANGE_GOODS_CONDITIONS = #{exchangeGoodsConditions,jdbcType=VARCHAR},
      EXCHANGE_GOODS_METHOD = #{exchangeGoodsMethod,jdbcType=VARCHAR},
      GOODS_COMMENTS = #{goodsComments,jdbcType=VARCHAR},
      `STATUS` = #{status,jdbcType=BOOLEAN},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      CHECK_TIME = #{checkTime,jdbcType=TIMESTAMP},
      CHECKER = #{checker,jdbcType=INTEGER},
      OPERATE_INFO_ID = #{operateInfoId,jdbcType=INTEGER},
      DELETE_REASON = #{deleteReason,jdbcType=VARCHAR},
      LAST_CHECK_REASON = #{lastCheckReason,jdbcType=VARCHAR},
      TAX_CATEGORY_NO = #{taxCategoryNo,jdbcType=VARCHAR},
      JX_MARKET_PRICE = #{jxMarketPrice,jdbcType=DECIMAL},
      JX_SALE_PRICE = #{jxSalePrice,jdbcType=DECIMAL},
      JX_FLAG = #{jxFlag,jdbcType=INTEGER},
      `SOURCE` = #{source,jdbcType=TINYINT},
      PUSH_STATUS = #{pushStatus,jdbcType=INTEGER},
      DECLARE_DELIVERY_RANGE = #{declareDeliveryRange,jdbcType=VARCHAR},
      PRICE_VERIFY_STATUS = #{priceVerifyStatus,jdbcType=INTEGER},
      AVGPRICE = #{avgprice,jdbcType=DECIMAL},
      LATEST_VALID_ORDER_USER = #{latestValidOrderUser,jdbcType=INTEGER},
      AVGPRICE_UPDATE_TIME = #{avgpriceUpdateTime,jdbcType=TIMESTAMP},
      TERMINAL_PRICE = #{terminalPrice,jdbcType=DECIMAL},
      DISTRIBUTION_PRICE = #{distributionPrice,jdbcType=DECIMAL},
      COST_PRICE = #{costPrice,jdbcType=DECIMAL},
      AVAILABLE_STOCK_NUM = #{availableStockNum,jdbcType=INTEGER},
      STOCK_NUM = #{stockNum,jdbcType=INTEGER},
      ON_SALE = #{onSale,jdbcType=INTEGER},
      GOODS_BARCODE = #{goodsBarcode,jdbcType=VARCHAR},
      CURING_TYPE = #{curingType,jdbcType=BOOLEAN},
      CURING_REASON = #{curingReason,jdbcType=VARCHAR},
      IS_NEED_TEST_REPROT = #{isNeedTestReprot,jdbcType=BOOLEAN},
      IS_KIT = #{isKit,jdbcType=BOOLEAN},
      KIT_DESC = #{kitDesc,jdbcType=VARCHAR},
      IS_SAME_SN_CODE = #{isSameSnCode,jdbcType=BOOLEAN},
      IS_FACTORY_SN_CODE = #{isFactorySnCode,jdbcType=BOOLEAN},
      IS_MANAGE_VEDENG_CODE = #{isManageVedengCode,jdbcType=BOOLEAN},
      IS_BAD_GOODS = #{isBadGoods,jdbcType=BOOLEAN},
      IS_ENABLE_FACTORY_BATCHNUM = #{isEnableFactoryBatchnum,jdbcType=BOOLEAN},
      IS_ENABLE_MULTISTAGE_PACKAGE = #{isEnableMultistagePackage,jdbcType=BOOLEAN},
      MID_PACKAGE_NUM = #{midPackageNum,jdbcType=INTEGER},
      BOX_PACKAGE_NUM = #{boxPackageNum,jdbcType=INTEGER},
      IS_ENABLE_VALIDITY_PERIOD = #{isEnableValidityPeriod,jdbcType=TINYINT},
      NEAR_TERM_WARN_DAYS = #{nearTermWarnDays,jdbcType=INTEGER},
      OVER_NEAR_TERM_WARN_DAYS = #{overNearTermWarnDays,jdbcType=INTEGER},
      INSTALL_TRAIN_TYPE = #{installTrainType,jdbcType=BOOLEAN},
      LOGISTICS_DELIVERYTYPE = #{logisticsDeliverytype,jdbcType=BOOLEAN},
      IS_NEED_REPORT = #{isNeedReport,jdbcType=BOOLEAN},
      IS_AUTHORIZED = #{isAuthorized,jdbcType=BOOLEAN},
      HISTORY_NAME = #{historyName,jdbcType=VARCHAR},
      IS_NAME_CHANGE = #{isNameChange,jdbcType=TINYINT},
      ONE_YEAR_SALE_NUM = #{oneYearSaleNum,jdbcType=INTEGER},
      REGULAR_MAINTAIN_TYPE = #{regularMaintainType,jdbcType=BOOLEAN},
      REGULAR_MAINTAIN_REASON = #{regularMaintainReason,jdbcType=VARCHAR},
      SYNCHRONIZATION_STATUS = #{synchronizationStatus,jdbcType=BOOLEAN},
      IS_INSTALLABLE = #{isInstallable,jdbcType=BOOLEAN},
      GOODS_LEVEL_NO = #{goodsLevelNo,jdbcType=INTEGER},
      GOODS_POSITION_NO = #{goodsPositionNo,jdbcType=INTEGER},
      LAST_YEAR_RATIO_EIGHTY_SORT = #{lastYearRatioEightySort,jdbcType=INTEGER},
      THREE_MONTH_RATIO_EIGHTY_SORT = #{threeMonthRatioEightySort,jdbcType=INTEGER},
      ONE_MONTH_RATIO_EIGHTY_SORT = #{oneMonthRatioEightySort,jdbcType=INTEGER},
      ORG_ID_LIST = #{orgIdList,jdbcType=VARCHAR},
      IS_AVAILABLE_SALE = #{isAvailableSale,jdbcType=INTEGER},
      PUSHED_ORG_ID_LIST = #{pushedOrgIdList,jdbcType=VARCHAR},
      CONFIGURATION_LIST = #{configurationList,jdbcType=VARCHAR},
      ACTION_LOCK_NUM = #{actionLockNum,jdbcType=INTEGER},
      ORDER_OCCUPY_NUM = #{orderOccupyNum,jdbcType=INTEGER},
      DISABLED_REASON = #{disabledReason,jdbcType=VARCHAR},
      PURCHASE_TIME = #{purchaseTime,jdbcType=INTEGER},
      PURCHASE_TIME_UPDATE_TIME = #{purchaseTimeUpdateTime,jdbcType=TIMESTAMP},
      HAS_AFTER_SALE_SERVICE_LABEL = #{hasAfterSaleServiceLabel,jdbcType=BOOLEAN},
      SPU_TYPE = #{spuType,jdbcType=INTEGER},
      INSTITUTION_LEVEL_IDS = #{institutionLevelIds,jdbcType=VARCHAR},
      IS_SEVEN = #{isSeven,jdbcType=INTEGER},
      HAVE_STOCK_MANAGE = #{haveStockManage,jdbcType=BOOLEAN},
      COST_CATEGORY_ID = #{costCategoryId,jdbcType=INTEGER},
      IS_VIRTURE_SKU = #{isVirtureSku,jdbcType=BOOLEAN},
      VIRTURE_TIME = #{virtureTime,jdbcType=TIMESTAMP},
      VIRTURE_CREATOR = #{virtureCreator,jdbcType=INTEGER},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="SKU_ID" keyProperty="skuId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchCoreSkuDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into V_CORE_SKU
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="skuId != null">
        SKU_ID,
      </if>
      <if test="spuId != null">
        SPU_ID,
      </if>
      <if test="checkStatus != null">
        CHECK_STATUS,
      </if>
      <if test="model != null and model != ''">
        MODEL,
      </if>
      <if test="spec != null and spec != ''">
        SPEC,
      </if>
      <if test="skuNo != null and skuNo != ''">
        SKU_NO,
      </if>
      <if test="skuName != null and skuName != ''">
        SKU_NAME,
      </if>
      <if test="showName != null and showName != ''">
        SHOW_NAME,
      </if>
      <if test="materialCode != null and materialCode != ''">
        MATERIAL_CODE,
      </if>
      <if test="supplyModel != null and supplyModel != ''">
        SUPPLY_MODEL,
      </if>
      <if test="isStockup != null and isStockup != ''">
        IS_STOCKUP,
      </if>
      <if test="wikiHref != null and wikiHref != ''">
        WIKI_HREF,
      </if>
      <if test="technicalParameter != null and technicalParameter != ''">
        TECHNICAL_PARAMETER,
      </if>
      <if test="performanceParameter != null and performanceParameter != ''">
        PERFORMANCE_PARAMETER,
      </if>
      <if test="specParameter != null and specParameter != ''">
        SPEC_PARAMETER,
      </if>
      <if test="baseUnitId != null">
        BASE_UNIT_ID,
      </if>
      <if test="minOrder != null">
        MIN_ORDER,
      </if>
      <if test="goodsLength != null">
        GOODS_LENGTH,
      </if>
      <if test="goodsWidth != null">
        GOODS_WIDTH,
      </if>
      <if test="goodsHeight != null">
        GOODS_HEIGHT,
      </if>
      <if test="packageLength != null">
        PACKAGE_LENGTH,
      </if>
      <if test="packageWidth != null">
        PACKAGE_WIDTH,
      </if>
      <if test="packageHeight != null">
        PACKAGE_HEIGHT,
      </if>
      <if test="netWeight != null">
        NET_WEIGHT,
      </if>
      <if test="grossWeight != null">
        GROSS_WEIGHT,
      </if>
      <if test="unitId != null">
        UNIT_ID,
      </if>
      <if test="changeNum != null">
        CHANGE_NUM,
      </if>
      <if test="packingList != null and packingList != ''">
        PACKING_LIST,
      </if>
      <if test="afterSaleContent != null and afterSaleContent != ''">
        AFTER_SALE_CONTENT,
      </if>
      <if test="qaYears != null and qaYears != ''">
        QA_YEARS,
      </if>
      <if test="storageConditionOne != null">
        STORAGE_CONDITION_ONE,
      </if>
      <if test="storageConditionOneLowerValue != null">
        STORAGE_CONDITION_ONE_LOWER_VALUE,
      </if>
      <if test="storageConditionOneUpperValue != null">
        STORAGE_CONDITION_ONE_UPPER_VALUE,
      </if>
      <if test="storageConditionHumidityLowerValue != null">
        STORAGE_CONDITION_HUMIDITY_LOWER_VALUE,
      </if>
      <if test="storageConditionHumidityUpperValue != null">
        STORAGE_CONDITION_HUMIDITY_UPPER_VALUE,
      </if>
      <if test="storageConditionTwo != null and storageConditionTwo != ''">
        STORAGE_CONDITION_TWO,
      </if>
      <if test="effectiveDayUnit != null">
        EFFECTIVE_DAY_UNIT,
      </if>
      <if test="effectiveDays != null and effectiveDays != ''">
        EFFECTIVE_DAYS,
      </if>
      <if test="qaRule != null and qaRule != ''">
        QA_RULE,
      </if>
      <if test="qaOutPrice != null">
        QA_OUT_PRICE,
      </if>
      <if test="qaResponseTime != null">
        QA_RESPONSE_TIME,
      </if>
      <if test="hasBackupMachine != null and hasBackupMachine != ''">
        HAS_BACKUP_MACHINE,
      </if>
      <if test="supplierExtendGuaranteePrice != null">
        SUPPLIER_EXTEND_GUARANTEE_PRICE,
      </if>
      <if test="corePartsPriceFid != null">
        CORE_PARTS_PRICE_FID,
      </if>
      <if test="returnGoodsConditions != null">
        RETURN_GOODS_CONDITIONS,
      </if>
      <if test="freightIntroductions != null and freightIntroductions != ''">
        FREIGHT_INTRODUCTIONS,
      </if>
      <if test="exchangeGoodsConditions != null and exchangeGoodsConditions != ''">
        EXCHANGE_GOODS_CONDITIONS,
      </if>
      <if test="exchangeGoodsMethod != null and exchangeGoodsMethod != ''">
        EXCHANGE_GOODS_METHOD,
      </if>
      <if test="goodsComments != null and goodsComments != ''">
        GOODS_COMMENTS,
      </if>
      <if test="status != null">
        `STATUS`,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="checkTime != null">
        CHECK_TIME,
      </if>
      <if test="checker != null">
        CHECKER,
      </if>
      <if test="operateInfoId != null">
        OPERATE_INFO_ID,
      </if>
      <if test="deleteReason != null and deleteReason != ''">
        DELETE_REASON,
      </if>
      <if test="lastCheckReason != null and lastCheckReason != ''">
        LAST_CHECK_REASON,
      </if>
      <if test="taxCategoryNo != null and taxCategoryNo != ''">
        TAX_CATEGORY_NO,
      </if>
      <if test="jxMarketPrice != null">
        JX_MARKET_PRICE,
      </if>
      <if test="jxSalePrice != null">
        JX_SALE_PRICE,
      </if>
      <if test="jxFlag != null">
        JX_FLAG,
      </if>
      <if test="source != null">
        `SOURCE`,
      </if>
      <if test="pushStatus != null">
        PUSH_STATUS,
      </if>
      <if test="declareDeliveryRange != null and declareDeliveryRange != ''">
        DECLARE_DELIVERY_RANGE,
      </if>
      <if test="priceVerifyStatus != null">
        PRICE_VERIFY_STATUS,
      </if>
      <if test="avgprice != null">
        AVGPRICE,
      </if>
      <if test="latestValidOrderUser != null">
        LATEST_VALID_ORDER_USER,
      </if>
      <if test="avgpriceUpdateTime != null">
        AVGPRICE_UPDATE_TIME,
      </if>
      <if test="terminalPrice != null">
        TERMINAL_PRICE,
      </if>
      <if test="distributionPrice != null">
        DISTRIBUTION_PRICE,
      </if>
      <if test="costPrice != null">
        COST_PRICE,
      </if>
      <if test="availableStockNum != null">
        AVAILABLE_STOCK_NUM,
      </if>
      <if test="stockNum != null">
        STOCK_NUM,
      </if>
      <if test="onSale != null">
        ON_SALE,
      </if>
      <if test="goodsBarcode != null and goodsBarcode != ''">
        GOODS_BARCODE,
      </if>
      <if test="curingType != null">
        CURING_TYPE,
      </if>
      <if test="curingReason != null and curingReason != ''">
        CURING_REASON,
      </if>
      <if test="isNeedTestReprot != null">
        IS_NEED_TEST_REPROT,
      </if>
      <if test="isKit != null">
        IS_KIT,
      </if>
      <if test="kitDesc != null and kitDesc != ''">
        KIT_DESC,
      </if>
      <if test="isSameSnCode != null">
        IS_SAME_SN_CODE,
      </if>
      <if test="isFactorySnCode != null">
        IS_FACTORY_SN_CODE,
      </if>
      <if test="isManageVedengCode != null">
        IS_MANAGE_VEDENG_CODE,
      </if>
      <if test="isBadGoods != null">
        IS_BAD_GOODS,
      </if>
      <if test="isEnableFactoryBatchnum != null">
        IS_ENABLE_FACTORY_BATCHNUM,
      </if>
      <if test="isEnableMultistagePackage != null">
        IS_ENABLE_MULTISTAGE_PACKAGE,
      </if>
      <if test="midPackageNum != null">
        MID_PACKAGE_NUM,
      </if>
      <if test="boxPackageNum != null">
        BOX_PACKAGE_NUM,
      </if>
      <if test="isEnableValidityPeriod != null">
        IS_ENABLE_VALIDITY_PERIOD,
      </if>
      <if test="nearTermWarnDays != null">
        NEAR_TERM_WARN_DAYS,
      </if>
      <if test="overNearTermWarnDays != null">
        OVER_NEAR_TERM_WARN_DAYS,
      </if>
      <if test="installTrainType != null">
        INSTALL_TRAIN_TYPE,
      </if>
      <if test="logisticsDeliverytype != null">
        LOGISTICS_DELIVERYTYPE,
      </if>
      <if test="isNeedReport != null">
        IS_NEED_REPORT,
      </if>
      <if test="isAuthorized != null">
        IS_AUTHORIZED,
      </if>
      <if test="historyName != null and historyName != ''">
        HISTORY_NAME,
      </if>
      <if test="isNameChange != null">
        IS_NAME_CHANGE,
      </if>
      <if test="oneYearSaleNum != null">
        ONE_YEAR_SALE_NUM,
      </if>
      <if test="regularMaintainType != null">
        REGULAR_MAINTAIN_TYPE,
      </if>
      <if test="regularMaintainReason != null and regularMaintainReason != ''">
        REGULAR_MAINTAIN_REASON,
      </if>
      <if test="synchronizationStatus != null">
        SYNCHRONIZATION_STATUS,
      </if>
      <if test="isInstallable != null">
        IS_INSTALLABLE,
      </if>
      <if test="goodsLevelNo != null">
        GOODS_LEVEL_NO,
      </if>
      <if test="goodsPositionNo != null">
        GOODS_POSITION_NO,
      </if>
      <if test="lastYearRatioEightySort != null">
        LAST_YEAR_RATIO_EIGHTY_SORT,
      </if>
      <if test="threeMonthRatioEightySort != null">
        THREE_MONTH_RATIO_EIGHTY_SORT,
      </if>
      <if test="oneMonthRatioEightySort != null">
        ONE_MONTH_RATIO_EIGHTY_SORT,
      </if>
      <if test="orgIdList != null and orgIdList != ''">
        ORG_ID_LIST,
      </if>
      <if test="isAvailableSale != null">
        IS_AVAILABLE_SALE,
      </if>
      <if test="pushedOrgIdList != null and pushedOrgIdList != ''">
        PUSHED_ORG_ID_LIST,
      </if>
      <if test="configurationList != null and configurationList != ''">
        CONFIGURATION_LIST,
      </if>
      <if test="actionLockNum != null">
        ACTION_LOCK_NUM,
      </if>
      <if test="orderOccupyNum != null">
        ORDER_OCCUPY_NUM,
      </if>
      <if test="disabledReason != null and disabledReason != ''">
        DISABLED_REASON,
      </if>
      <if test="purchaseTime != null">
        PURCHASE_TIME,
      </if>
      <if test="purchaseTimeUpdateTime != null">
        PURCHASE_TIME_UPDATE_TIME,
      </if>
      <if test="hasAfterSaleServiceLabel != null">
        HAS_AFTER_SALE_SERVICE_LABEL,
      </if>
      <if test="spuType != null">
        SPU_TYPE,
      </if>
      <if test="institutionLevelIds != null and institutionLevelIds != ''">
        INSTITUTION_LEVEL_IDS,
      </if>
      <if test="isSeven != null">
        IS_SEVEN,
      </if>
      <if test="haveStockManage != null">
        HAVE_STOCK_MANAGE,
      </if>
      <if test="costCategoryId != null">
        COST_CATEGORY_ID,
      </if>
      <if test="isVirtureSku != null">
        IS_VIRTURE_SKU,
      </if>
      <if test="virtureTime != null">
        VIRTURE_TIME,
      </if>
      <if test="virtureCreator != null">
        VIRTURE_CREATOR,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="skuId != null">
        #{skuId,jdbcType=INTEGER},
      </if>
      <if test="spuId != null">
        #{spuId,jdbcType=INTEGER},
      </if>
      <if test="checkStatus != null">
        #{checkStatus,jdbcType=BOOLEAN},
      </if>
      <if test="model != null and model != ''">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="spec != null and spec != ''">
        #{spec,jdbcType=VARCHAR},
      </if>
      <if test="skuNo != null and skuNo != ''">
        #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null and skuName != ''">
        #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="showName != null and showName != ''">
        #{showName,jdbcType=VARCHAR},
      </if>
      <if test="materialCode != null and materialCode != ''">
        #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="supplyModel != null and supplyModel != ''">
        #{supplyModel,jdbcType=VARCHAR},
      </if>
      <if test="isStockup != null and isStockup != ''">
        #{isStockup,jdbcType=VARCHAR},
      </if>
      <if test="wikiHref != null and wikiHref != ''">
        #{wikiHref,jdbcType=VARCHAR},
      </if>
      <if test="technicalParameter != null and technicalParameter != ''">
        #{technicalParameter,jdbcType=VARCHAR},
      </if>
      <if test="performanceParameter != null and performanceParameter != ''">
        #{performanceParameter,jdbcType=VARCHAR},
      </if>
      <if test="specParameter != null and specParameter != ''">
        #{specParameter,jdbcType=VARCHAR},
      </if>
      <if test="baseUnitId != null">
        #{baseUnitId,jdbcType=INTEGER},
      </if>
      <if test="minOrder != null">
        #{minOrder,jdbcType=DECIMAL},
      </if>
      <if test="goodsLength != null">
        #{goodsLength,jdbcType=DECIMAL},
      </if>
      <if test="goodsWidth != null">
        #{goodsWidth,jdbcType=DECIMAL},
      </if>
      <if test="goodsHeight != null">
        #{goodsHeight,jdbcType=DECIMAL},
      </if>
      <if test="packageLength != null">
        #{packageLength,jdbcType=DECIMAL},
      </if>
      <if test="packageWidth != null">
        #{packageWidth,jdbcType=DECIMAL},
      </if>
      <if test="packageHeight != null">
        #{packageHeight,jdbcType=DECIMAL},
      </if>
      <if test="netWeight != null">
        #{netWeight,jdbcType=DECIMAL},
      </if>
      <if test="grossWeight != null">
        #{grossWeight,jdbcType=DECIMAL},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="changeNum != null">
        #{changeNum,jdbcType=DECIMAL},
      </if>
      <if test="packingList != null and packingList != ''">
        #{packingList,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleContent != null and afterSaleContent != ''">
        #{afterSaleContent,jdbcType=VARCHAR},
      </if>
      <if test="qaYears != null and qaYears != ''">
        #{qaYears,jdbcType=VARCHAR},
      </if>
      <if test="storageConditionOne != null">
        #{storageConditionOne,jdbcType=BOOLEAN},
      </if>
      <if test="storageConditionOneLowerValue != null">
        #{storageConditionOneLowerValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionOneUpperValue != null">
        #{storageConditionOneUpperValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionHumidityLowerValue != null">
        #{storageConditionHumidityLowerValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionHumidityUpperValue != null">
        #{storageConditionHumidityUpperValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionTwo != null and storageConditionTwo != ''">
        #{storageConditionTwo,jdbcType=VARCHAR},
      </if>
      <if test="effectiveDayUnit != null">
        #{effectiveDayUnit,jdbcType=BOOLEAN},
      </if>
      <if test="effectiveDays != null and effectiveDays != ''">
        #{effectiveDays,jdbcType=VARCHAR},
      </if>
      <if test="qaRule != null and qaRule != ''">
        #{qaRule,jdbcType=VARCHAR},
      </if>
      <if test="qaOutPrice != null">
        #{qaOutPrice,jdbcType=DECIMAL},
      </if>
      <if test="qaResponseTime != null">
        #{qaResponseTime,jdbcType=DECIMAL},
      </if>
      <if test="hasBackupMachine != null and hasBackupMachine != ''">
        #{hasBackupMachine,jdbcType=VARCHAR},
      </if>
      <if test="supplierExtendGuaranteePrice != null">
        #{supplierExtendGuaranteePrice,jdbcType=DECIMAL},
      </if>
      <if test="corePartsPriceFid != null">
        #{corePartsPriceFid,jdbcType=INTEGER},
      </if>
      <if test="returnGoodsConditions != null">
        #{returnGoodsConditions,jdbcType=BOOLEAN},
      </if>
      <if test="freightIntroductions != null and freightIntroductions != ''">
        #{freightIntroductions,jdbcType=VARCHAR},
      </if>
      <if test="exchangeGoodsConditions != null and exchangeGoodsConditions != ''">
        #{exchangeGoodsConditions,jdbcType=VARCHAR},
      </if>
      <if test="exchangeGoodsMethod != null and exchangeGoodsMethod != ''">
        #{exchangeGoodsMethod,jdbcType=VARCHAR},
      </if>
      <if test="goodsComments != null and goodsComments != ''">
        #{goodsComments,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="checkTime != null">
        #{checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checker != null">
        #{checker,jdbcType=INTEGER},
      </if>
      <if test="operateInfoId != null">
        #{operateInfoId,jdbcType=INTEGER},
      </if>
      <if test="deleteReason != null and deleteReason != ''">
        #{deleteReason,jdbcType=VARCHAR},
      </if>
      <if test="lastCheckReason != null and lastCheckReason != ''">
        #{lastCheckReason,jdbcType=VARCHAR},
      </if>
      <if test="taxCategoryNo != null and taxCategoryNo != ''">
        #{taxCategoryNo,jdbcType=VARCHAR},
      </if>
      <if test="jxMarketPrice != null">
        #{jxMarketPrice,jdbcType=DECIMAL},
      </if>
      <if test="jxSalePrice != null">
        #{jxSalePrice,jdbcType=DECIMAL},
      </if>
      <if test="jxFlag != null">
        #{jxFlag,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=TINYINT},
      </if>
      <if test="pushStatus != null">
        #{pushStatus,jdbcType=INTEGER},
      </if>
      <if test="declareDeliveryRange != null and declareDeliveryRange != ''">
        #{declareDeliveryRange,jdbcType=VARCHAR},
      </if>
      <if test="priceVerifyStatus != null">
        #{priceVerifyStatus,jdbcType=INTEGER},
      </if>
      <if test="avgprice != null">
        #{avgprice,jdbcType=DECIMAL},
      </if>
      <if test="latestValidOrderUser != null">
        #{latestValidOrderUser,jdbcType=INTEGER},
      </if>
      <if test="avgpriceUpdateTime != null">
        #{avgpriceUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="terminalPrice != null">
        #{terminalPrice,jdbcType=DECIMAL},
      </if>
      <if test="distributionPrice != null">
        #{distributionPrice,jdbcType=DECIMAL},
      </if>
      <if test="costPrice != null">
        #{costPrice,jdbcType=DECIMAL},
      </if>
      <if test="availableStockNum != null">
        #{availableStockNum,jdbcType=INTEGER},
      </if>
      <if test="stockNum != null">
        #{stockNum,jdbcType=INTEGER},
      </if>
      <if test="onSale != null">
        #{onSale,jdbcType=INTEGER},
      </if>
      <if test="goodsBarcode != null and goodsBarcode != ''">
        #{goodsBarcode,jdbcType=VARCHAR},
      </if>
      <if test="curingType != null">
        #{curingType,jdbcType=BOOLEAN},
      </if>
      <if test="curingReason != null and curingReason != ''">
        #{curingReason,jdbcType=VARCHAR},
      </if>
      <if test="isNeedTestReprot != null">
        #{isNeedTestReprot,jdbcType=BOOLEAN},
      </if>
      <if test="isKit != null">
        #{isKit,jdbcType=BOOLEAN},
      </if>
      <if test="kitDesc != null and kitDesc != ''">
        #{kitDesc,jdbcType=VARCHAR},
      </if>
      <if test="isSameSnCode != null">
        #{isSameSnCode,jdbcType=BOOLEAN},
      </if>
      <if test="isFactorySnCode != null">
        #{isFactorySnCode,jdbcType=BOOLEAN},
      </if>
      <if test="isManageVedengCode != null">
        #{isManageVedengCode,jdbcType=BOOLEAN},
      </if>
      <if test="isBadGoods != null">
        #{isBadGoods,jdbcType=BOOLEAN},
      </if>
      <if test="isEnableFactoryBatchnum != null">
        #{isEnableFactoryBatchnum,jdbcType=BOOLEAN},
      </if>
      <if test="isEnableMultistagePackage != null">
        #{isEnableMultistagePackage,jdbcType=BOOLEAN},
      </if>
      <if test="midPackageNum != null">
        #{midPackageNum,jdbcType=INTEGER},
      </if>
      <if test="boxPackageNum != null">
        #{boxPackageNum,jdbcType=INTEGER},
      </if>
      <if test="isEnableValidityPeriod != null">
        #{isEnableValidityPeriod,jdbcType=TINYINT},
      </if>
      <if test="nearTermWarnDays != null">
        #{nearTermWarnDays,jdbcType=INTEGER},
      </if>
      <if test="overNearTermWarnDays != null">
        #{overNearTermWarnDays,jdbcType=INTEGER},
      </if>
      <if test="installTrainType != null">
        #{installTrainType,jdbcType=BOOLEAN},
      </if>
      <if test="logisticsDeliverytype != null">
        #{logisticsDeliverytype,jdbcType=BOOLEAN},
      </if>
      <if test="isNeedReport != null">
        #{isNeedReport,jdbcType=BOOLEAN},
      </if>
      <if test="isAuthorized != null">
        #{isAuthorized,jdbcType=BOOLEAN},
      </if>
      <if test="historyName != null and historyName != ''">
        #{historyName,jdbcType=VARCHAR},
      </if>
      <if test="isNameChange != null">
        #{isNameChange,jdbcType=TINYINT},
      </if>
      <if test="oneYearSaleNum != null">
        #{oneYearSaleNum,jdbcType=INTEGER},
      </if>
      <if test="regularMaintainType != null">
        #{regularMaintainType,jdbcType=BOOLEAN},
      </if>
      <if test="regularMaintainReason != null and regularMaintainReason != ''">
        #{regularMaintainReason,jdbcType=VARCHAR},
      </if>
      <if test="synchronizationStatus != null">
        #{synchronizationStatus,jdbcType=BOOLEAN},
      </if>
      <if test="isInstallable != null">
        #{isInstallable,jdbcType=BOOLEAN},
      </if>
      <if test="goodsLevelNo != null">
        #{goodsLevelNo,jdbcType=INTEGER},
      </if>
      <if test="goodsPositionNo != null">
        #{goodsPositionNo,jdbcType=INTEGER},
      </if>
      <if test="lastYearRatioEightySort != null">
        #{lastYearRatioEightySort,jdbcType=INTEGER},
      </if>
      <if test="threeMonthRatioEightySort != null">
        #{threeMonthRatioEightySort,jdbcType=INTEGER},
      </if>
      <if test="oneMonthRatioEightySort != null">
        #{oneMonthRatioEightySort,jdbcType=INTEGER},
      </if>
      <if test="orgIdList != null and orgIdList != ''">
        #{orgIdList,jdbcType=VARCHAR},
      </if>
      <if test="isAvailableSale != null">
        #{isAvailableSale,jdbcType=INTEGER},
      </if>
      <if test="pushedOrgIdList != null and pushedOrgIdList != ''">
        #{pushedOrgIdList,jdbcType=VARCHAR},
      </if>
      <if test="configurationList != null and configurationList != ''">
        #{configurationList,jdbcType=VARCHAR},
      </if>
      <if test="actionLockNum != null">
        #{actionLockNum,jdbcType=INTEGER},
      </if>
      <if test="orderOccupyNum != null">
        #{orderOccupyNum,jdbcType=INTEGER},
      </if>
      <if test="disabledReason != null and disabledReason != ''">
        #{disabledReason,jdbcType=VARCHAR},
      </if>
      <if test="purchaseTime != null">
        #{purchaseTime,jdbcType=INTEGER},
      </if>
      <if test="purchaseTimeUpdateTime != null">
        #{purchaseTimeUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="hasAfterSaleServiceLabel != null">
        #{hasAfterSaleServiceLabel,jdbcType=BOOLEAN},
      </if>
      <if test="spuType != null">
        #{spuType,jdbcType=INTEGER},
      </if>
      <if test="institutionLevelIds != null and institutionLevelIds != ''">
        #{institutionLevelIds,jdbcType=VARCHAR},
      </if>
      <if test="isSeven != null">
        #{isSeven,jdbcType=INTEGER},
      </if>
      <if test="haveStockManage != null">
        #{haveStockManage,jdbcType=BOOLEAN},
      </if>
      <if test="costCategoryId != null">
        #{costCategoryId,jdbcType=INTEGER},
      </if>
      <if test="isVirtureSku != null">
        #{isVirtureSku,jdbcType=BOOLEAN},
      </if>
      <if test="virtureTime != null">
        #{virtureTime,jdbcType=TIMESTAMP},
      </if>
      <if test="virtureCreator != null">
        #{virtureCreator,jdbcType=INTEGER},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="skuId != null">
        SKU_ID = #{skuId,jdbcType=INTEGER},
      </if>
      <if test="spuId != null">
        SPU_ID = #{spuId,jdbcType=INTEGER},
      </if>
      <if test="checkStatus != null">
        CHECK_STATUS = #{checkStatus,jdbcType=BOOLEAN},
      </if>
      <if test="model != null and model != ''">
        MODEL = #{model,jdbcType=VARCHAR},
      </if>
      <if test="spec != null and spec != ''">
        SPEC = #{spec,jdbcType=VARCHAR},
      </if>
      <if test="skuNo != null and skuNo != ''">
        SKU_NO = #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null and skuName != ''">
        SKU_NAME = #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="showName != null and showName != ''">
        SHOW_NAME = #{showName,jdbcType=VARCHAR},
      </if>
      <if test="materialCode != null and materialCode != ''">
        MATERIAL_CODE = #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="supplyModel != null and supplyModel != ''">
        SUPPLY_MODEL = #{supplyModel,jdbcType=VARCHAR},
      </if>
      <if test="isStockup != null and isStockup != ''">
        IS_STOCKUP = #{isStockup,jdbcType=VARCHAR},
      </if>
      <if test="wikiHref != null and wikiHref != ''">
        WIKI_HREF = #{wikiHref,jdbcType=VARCHAR},
      </if>
      <if test="technicalParameter != null and technicalParameter != ''">
        TECHNICAL_PARAMETER = #{technicalParameter,jdbcType=VARCHAR},
      </if>
      <if test="performanceParameter != null and performanceParameter != ''">
        PERFORMANCE_PARAMETER = #{performanceParameter,jdbcType=VARCHAR},
      </if>
      <if test="specParameter != null and specParameter != ''">
        SPEC_PARAMETER = #{specParameter,jdbcType=VARCHAR},
      </if>
      <if test="baseUnitId != null">
        BASE_UNIT_ID = #{baseUnitId,jdbcType=INTEGER},
      </if>
      <if test="minOrder != null">
        MIN_ORDER = #{minOrder,jdbcType=DECIMAL},
      </if>
      <if test="goodsLength != null">
        GOODS_LENGTH = #{goodsLength,jdbcType=DECIMAL},
      </if>
      <if test="goodsWidth != null">
        GOODS_WIDTH = #{goodsWidth,jdbcType=DECIMAL},
      </if>
      <if test="goodsHeight != null">
        GOODS_HEIGHT = #{goodsHeight,jdbcType=DECIMAL},
      </if>
      <if test="packageLength != null">
        PACKAGE_LENGTH = #{packageLength,jdbcType=DECIMAL},
      </if>
      <if test="packageWidth != null">
        PACKAGE_WIDTH = #{packageWidth,jdbcType=DECIMAL},
      </if>
      <if test="packageHeight != null">
        PACKAGE_HEIGHT = #{packageHeight,jdbcType=DECIMAL},
      </if>
      <if test="netWeight != null">
        NET_WEIGHT = #{netWeight,jdbcType=DECIMAL},
      </if>
      <if test="grossWeight != null">
        GROSS_WEIGHT = #{grossWeight,jdbcType=DECIMAL},
      </if>
      <if test="unitId != null">
        UNIT_ID = #{unitId,jdbcType=INTEGER},
      </if>
      <if test="changeNum != null">
        CHANGE_NUM = #{changeNum,jdbcType=DECIMAL},
      </if>
      <if test="packingList != null and packingList != ''">
        PACKING_LIST = #{packingList,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleContent != null and afterSaleContent != ''">
        AFTER_SALE_CONTENT = #{afterSaleContent,jdbcType=VARCHAR},
      </if>
      <if test="qaYears != null and qaYears != ''">
        QA_YEARS = #{qaYears,jdbcType=VARCHAR},
      </if>
      <if test="storageConditionOne != null">
        STORAGE_CONDITION_ONE = #{storageConditionOne,jdbcType=BOOLEAN},
      </if>
      <if test="storageConditionOneLowerValue != null">
        STORAGE_CONDITION_ONE_LOWER_VALUE = #{storageConditionOneLowerValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionOneUpperValue != null">
        STORAGE_CONDITION_ONE_UPPER_VALUE = #{storageConditionOneUpperValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionHumidityLowerValue != null">
        STORAGE_CONDITION_HUMIDITY_LOWER_VALUE = #{storageConditionHumidityLowerValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionHumidityUpperValue != null">
        STORAGE_CONDITION_HUMIDITY_UPPER_VALUE = #{storageConditionHumidityUpperValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionTwo != null and storageConditionTwo != ''">
        STORAGE_CONDITION_TWO = #{storageConditionTwo,jdbcType=VARCHAR},
      </if>
      <if test="effectiveDayUnit != null">
        EFFECTIVE_DAY_UNIT = #{effectiveDayUnit,jdbcType=BOOLEAN},
      </if>
      <if test="effectiveDays != null and effectiveDays != ''">
        EFFECTIVE_DAYS = #{effectiveDays,jdbcType=VARCHAR},
      </if>
      <if test="qaRule != null and qaRule != ''">
        QA_RULE = #{qaRule,jdbcType=VARCHAR},
      </if>
      <if test="qaOutPrice != null">
        QA_OUT_PRICE = #{qaOutPrice,jdbcType=DECIMAL},
      </if>
      <if test="qaResponseTime != null">
        QA_RESPONSE_TIME = #{qaResponseTime,jdbcType=DECIMAL},
      </if>
      <if test="hasBackupMachine != null and hasBackupMachine != ''">
        HAS_BACKUP_MACHINE = #{hasBackupMachine,jdbcType=VARCHAR},
      </if>
      <if test="supplierExtendGuaranteePrice != null">
        SUPPLIER_EXTEND_GUARANTEE_PRICE = #{supplierExtendGuaranteePrice,jdbcType=DECIMAL},
      </if>
      <if test="corePartsPriceFid != null">
        CORE_PARTS_PRICE_FID = #{corePartsPriceFid,jdbcType=INTEGER},
      </if>
      <if test="returnGoodsConditions != null">
        RETURN_GOODS_CONDITIONS = #{returnGoodsConditions,jdbcType=BOOLEAN},
      </if>
      <if test="freightIntroductions != null and freightIntroductions != ''">
        FREIGHT_INTRODUCTIONS = #{freightIntroductions,jdbcType=VARCHAR},
      </if>
      <if test="exchangeGoodsConditions != null and exchangeGoodsConditions != ''">
        EXCHANGE_GOODS_CONDITIONS = #{exchangeGoodsConditions,jdbcType=VARCHAR},
      </if>
      <if test="exchangeGoodsMethod != null and exchangeGoodsMethod != ''">
        EXCHANGE_GOODS_METHOD = #{exchangeGoodsMethod,jdbcType=VARCHAR},
      </if>
      <if test="goodsComments != null and goodsComments != ''">
        GOODS_COMMENTS = #{goodsComments,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `STATUS` = #{status,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="checkTime != null">
        CHECK_TIME = #{checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checker != null">
        CHECKER = #{checker,jdbcType=INTEGER},
      </if>
      <if test="operateInfoId != null">
        OPERATE_INFO_ID = #{operateInfoId,jdbcType=INTEGER},
      </if>
      <if test="deleteReason != null and deleteReason != ''">
        DELETE_REASON = #{deleteReason,jdbcType=VARCHAR},
      </if>
      <if test="lastCheckReason != null and lastCheckReason != ''">
        LAST_CHECK_REASON = #{lastCheckReason,jdbcType=VARCHAR},
      </if>
      <if test="taxCategoryNo != null and taxCategoryNo != ''">
        TAX_CATEGORY_NO = #{taxCategoryNo,jdbcType=VARCHAR},
      </if>
      <if test="jxMarketPrice != null">
        JX_MARKET_PRICE = #{jxMarketPrice,jdbcType=DECIMAL},
      </if>
      <if test="jxSalePrice != null">
        JX_SALE_PRICE = #{jxSalePrice,jdbcType=DECIMAL},
      </if>
      <if test="jxFlag != null">
        JX_FLAG = #{jxFlag,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        `SOURCE` = #{source,jdbcType=TINYINT},
      </if>
      <if test="pushStatus != null">
        PUSH_STATUS = #{pushStatus,jdbcType=INTEGER},
      </if>
      <if test="declareDeliveryRange != null and declareDeliveryRange != ''">
        DECLARE_DELIVERY_RANGE = #{declareDeliveryRange,jdbcType=VARCHAR},
      </if>
      <if test="priceVerifyStatus != null">
        PRICE_VERIFY_STATUS = #{priceVerifyStatus,jdbcType=INTEGER},
      </if>
      <if test="avgprice != null">
        AVGPRICE = #{avgprice,jdbcType=DECIMAL},
      </if>
      <if test="latestValidOrderUser != null">
        LATEST_VALID_ORDER_USER = #{latestValidOrderUser,jdbcType=INTEGER},
      </if>
      <if test="avgpriceUpdateTime != null">
        AVGPRICE_UPDATE_TIME = #{avgpriceUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="terminalPrice != null">
        TERMINAL_PRICE = #{terminalPrice,jdbcType=DECIMAL},
      </if>
      <if test="distributionPrice != null">
        DISTRIBUTION_PRICE = #{distributionPrice,jdbcType=DECIMAL},
      </if>
      <if test="costPrice != null">
        COST_PRICE = #{costPrice,jdbcType=DECIMAL},
      </if>
      <if test="availableStockNum != null">
        AVAILABLE_STOCK_NUM = #{availableStockNum,jdbcType=INTEGER},
      </if>
      <if test="stockNum != null">
        STOCK_NUM = #{stockNum,jdbcType=INTEGER},
      </if>
      <if test="onSale != null">
        ON_SALE = #{onSale,jdbcType=INTEGER},
      </if>
      <if test="goodsBarcode != null and goodsBarcode != ''">
        GOODS_BARCODE = #{goodsBarcode,jdbcType=VARCHAR},
      </if>
      <if test="curingType != null">
        CURING_TYPE = #{curingType,jdbcType=BOOLEAN},
      </if>
      <if test="curingReason != null and curingReason != ''">
        CURING_REASON = #{curingReason,jdbcType=VARCHAR},
      </if>
      <if test="isNeedTestReprot != null">
        IS_NEED_TEST_REPROT = #{isNeedTestReprot,jdbcType=BOOLEAN},
      </if>
      <if test="isKit != null">
        IS_KIT = #{isKit,jdbcType=BOOLEAN},
      </if>
      <if test="kitDesc != null and kitDesc != ''">
        KIT_DESC = #{kitDesc,jdbcType=VARCHAR},
      </if>
      <if test="isSameSnCode != null">
        IS_SAME_SN_CODE = #{isSameSnCode,jdbcType=BOOLEAN},
      </if>
      <if test="isFactorySnCode != null">
        IS_FACTORY_SN_CODE = #{isFactorySnCode,jdbcType=BOOLEAN},
      </if>
      <if test="isManageVedengCode != null">
        IS_MANAGE_VEDENG_CODE = #{isManageVedengCode,jdbcType=BOOLEAN},
      </if>
      <if test="isBadGoods != null">
        IS_BAD_GOODS = #{isBadGoods,jdbcType=BOOLEAN},
      </if>
      <if test="isEnableFactoryBatchnum != null">
        IS_ENABLE_FACTORY_BATCHNUM = #{isEnableFactoryBatchnum,jdbcType=BOOLEAN},
      </if>
      <if test="isEnableMultistagePackage != null">
        IS_ENABLE_MULTISTAGE_PACKAGE = #{isEnableMultistagePackage,jdbcType=BOOLEAN},
      </if>
      <if test="midPackageNum != null">
        MID_PACKAGE_NUM = #{midPackageNum,jdbcType=INTEGER},
      </if>
      <if test="boxPackageNum != null">
        BOX_PACKAGE_NUM = #{boxPackageNum,jdbcType=INTEGER},
      </if>
      <if test="isEnableValidityPeriod != null">
        IS_ENABLE_VALIDITY_PERIOD = #{isEnableValidityPeriod,jdbcType=TINYINT},
      </if>
      <if test="nearTermWarnDays != null">
        NEAR_TERM_WARN_DAYS = #{nearTermWarnDays,jdbcType=INTEGER},
      </if>
      <if test="overNearTermWarnDays != null">
        OVER_NEAR_TERM_WARN_DAYS = #{overNearTermWarnDays,jdbcType=INTEGER},
      </if>
      <if test="installTrainType != null">
        INSTALL_TRAIN_TYPE = #{installTrainType,jdbcType=BOOLEAN},
      </if>
      <if test="logisticsDeliverytype != null">
        LOGISTICS_DELIVERYTYPE = #{logisticsDeliverytype,jdbcType=BOOLEAN},
      </if>
      <if test="isNeedReport != null">
        IS_NEED_REPORT = #{isNeedReport,jdbcType=BOOLEAN},
      </if>
      <if test="isAuthorized != null">
        IS_AUTHORIZED = #{isAuthorized,jdbcType=BOOLEAN},
      </if>
      <if test="historyName != null and historyName != ''">
        HISTORY_NAME = #{historyName,jdbcType=VARCHAR},
      </if>
      <if test="isNameChange != null">
        IS_NAME_CHANGE = #{isNameChange,jdbcType=TINYINT},
      </if>
      <if test="oneYearSaleNum != null">
        ONE_YEAR_SALE_NUM = #{oneYearSaleNum,jdbcType=INTEGER},
      </if>
      <if test="regularMaintainType != null">
        REGULAR_MAINTAIN_TYPE = #{regularMaintainType,jdbcType=BOOLEAN},
      </if>
      <if test="regularMaintainReason != null and regularMaintainReason != ''">
        REGULAR_MAINTAIN_REASON = #{regularMaintainReason,jdbcType=VARCHAR},
      </if>
      <if test="synchronizationStatus != null">
        SYNCHRONIZATION_STATUS = #{synchronizationStatus,jdbcType=BOOLEAN},
      </if>
      <if test="isInstallable != null">
        IS_INSTALLABLE = #{isInstallable,jdbcType=BOOLEAN},
      </if>
      <if test="goodsLevelNo != null">
        GOODS_LEVEL_NO = #{goodsLevelNo,jdbcType=INTEGER},
      </if>
      <if test="goodsPositionNo != null">
        GOODS_POSITION_NO = #{goodsPositionNo,jdbcType=INTEGER},
      </if>
      <if test="lastYearRatioEightySort != null">
        LAST_YEAR_RATIO_EIGHTY_SORT = #{lastYearRatioEightySort,jdbcType=INTEGER},
      </if>
      <if test="threeMonthRatioEightySort != null">
        THREE_MONTH_RATIO_EIGHTY_SORT = #{threeMonthRatioEightySort,jdbcType=INTEGER},
      </if>
      <if test="oneMonthRatioEightySort != null">
        ONE_MONTH_RATIO_EIGHTY_SORT = #{oneMonthRatioEightySort,jdbcType=INTEGER},
      </if>
      <if test="orgIdList != null and orgIdList != ''">
        ORG_ID_LIST = #{orgIdList,jdbcType=VARCHAR},
      </if>
      <if test="isAvailableSale != null">
        IS_AVAILABLE_SALE = #{isAvailableSale,jdbcType=INTEGER},
      </if>
      <if test="pushedOrgIdList != null and pushedOrgIdList != ''">
        PUSHED_ORG_ID_LIST = #{pushedOrgIdList,jdbcType=VARCHAR},
      </if>
      <if test="configurationList != null and configurationList != ''">
        CONFIGURATION_LIST = #{configurationList,jdbcType=VARCHAR},
      </if>
      <if test="actionLockNum != null">
        ACTION_LOCK_NUM = #{actionLockNum,jdbcType=INTEGER},
      </if>
      <if test="orderOccupyNum != null">
        ORDER_OCCUPY_NUM = #{orderOccupyNum,jdbcType=INTEGER},
      </if>
      <if test="disabledReason != null and disabledReason != ''">
        DISABLED_REASON = #{disabledReason,jdbcType=VARCHAR},
      </if>
      <if test="purchaseTime != null">
        PURCHASE_TIME = #{purchaseTime,jdbcType=INTEGER},
      </if>
      <if test="purchaseTimeUpdateTime != null">
        PURCHASE_TIME_UPDATE_TIME = #{purchaseTimeUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="hasAfterSaleServiceLabel != null">
        HAS_AFTER_SALE_SERVICE_LABEL = #{hasAfterSaleServiceLabel,jdbcType=BOOLEAN},
      </if>
      <if test="spuType != null">
        SPU_TYPE = #{spuType,jdbcType=INTEGER},
      </if>
      <if test="institutionLevelIds != null and institutionLevelIds != ''">
        INSTITUTION_LEVEL_IDS = #{institutionLevelIds,jdbcType=VARCHAR},
      </if>
      <if test="isSeven != null">
        IS_SEVEN = #{isSeven,jdbcType=INTEGER},
      </if>
      <if test="haveStockManage != null">
        HAVE_STOCK_MANAGE = #{haveStockManage,jdbcType=BOOLEAN},
      </if>
      <if test="costCategoryId != null">
        COST_CATEGORY_ID = #{costCategoryId,jdbcType=INTEGER},
      </if>
      <if test="isVirtureSku != null">
        IS_VIRTURE_SKU = #{isVirtureSku,jdbcType=BOOLEAN},
      </if>
      <if test="virtureTime != null">
        VIRTURE_TIME = #{virtureTime,jdbcType=TIMESTAMP},
      </if>
      <if test="virtureCreator != null">
        VIRTURE_CREATOR = #{virtureCreator,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

  <select id="skuTipList" resultType="java.util.Map">
    select
    F.FIRST_ENGAGE_ID FIRST_ENGAGE_ID,F.STANDARD_CATEGORY_TYPE,
    F.NEW_STANDARD_CATEGORY_ID,F.OLD_STANDARD_CATEGORY_ID,
    P.SPU_ID,P.SPU_TYPE,
    K.MATERIAL_CODE,
    ifnull(NUM.REGISTRATION_NUMBER,'') REGISTRATION_NUMBER,
    CASE NUM.MANAGE_CATEGORY_LEVEL
    WHEN 968 THEN '一类医疗器械'
    WHEN  969 THEN '二类医疗器械'
    WHEN  970 THEN '三类医疗器械'
    ELSE '' END AS MANAGE_CATEGORY_LEVEL,
    M.USERNAME,
    U.USERNAME AS 'ASSIS',
    ifnull(K.PACKING_LIST,'') PACKING_LIST ,
    CASE K.CHECK_STATUS
    WHEN 0 THEN '待完善'
    WHEN 1 THEN '审核中'
    WHEN 2 THEN '审核不通过'
    WHEN 3 THEN '审核通过'
    else '' end as CHECK_STATUS,
    K.SKU_ID,
    K.SKU_NO,
    K.SHOW_NAME,
    ifnull(K.SPEC,'') SPEC,
    B.BRAND_NAME,
    ifnull(K.MODEL,'')  MODEL,
    ifnull(UN.UNIT_NAME,'') UNIT_NAME,
    CASE QA_YEARS
    WHEN NULL THEN ''
    WHEN '' THEN ''
    else CONCAT(QA_YEARS,"年") end as QA_YEARS,
    CASE P.SPU_LEVEL
    WHEN 0 THEN '其他产品'
    WHEN 1 THEN '核心产品'
    WHEN 2 THEN '临时产品'
    ELSE '' END AS GOODS_LEVEL_NAME,
    OPT.TITLE AS GOODS_TYPE_NAME,IFNULL(K.AVAILABLE_STOCK_NUM,0) AVAILABLESTOCKNUM,IFNULL(K.STOCK_NUM,0) STOCKNUM,IFNULL(K.ORDER_OCCUPY_NUM,0) OCCUPYNUM,P.FIRST_ENGAGE_ID, P.SPU_TYPE
    from V_CORE_SKU K LEFT JOIN V_CORE_SPU P
    ON K.SPU_ID=P.SPU_ID
    LEFT JOIN T_FIRST_ENGAGE F ON P.FIRST_ENGAGE_ID=F.FIRST_ENGAGE_ID
    LEFT JOIN T_REGISTRATION_NUMBER NUM ON NUM.REGISTRATION_NUMBER_ID=F.REGISTRATION_NUMBER_ID
    LEFT JOIN T_USER U ON U.USER_ID=P.ASSIGNMENT_ASSISTANT_ID
    LEFT JOIN T_USER M ON M.USER_ID=P.ASSIGNMENT_MANAGER_ID
    LEFT JOIN T_BRAND B ON B.BRAND_ID=P.BRAND_ID
    left join T_UNIT UN ON UN.UNIT_ID=K.BASE_UNIT_ID
    LEFT JOIN T_SYS_OPTION_DEFINITION OPT ON P.SPU_TYPE = OPT.SYS_OPTION_DEFINITION_ID
    where K.SKU_ID in
    <foreach collection="list" item="skuId" index="index"
             open="(" close=")" separator=",">
      #{skuId}
    </foreach>
  </select>

  <select id="queryMaterialCompensate" resultMap="BaseResultMap">
    select VCS.SKU_ID,
           VCS.SPU_ID,
           VCS.SKU_NO,
           VCS.SKU_NAME,
           VCS.SPEC,
           VCS.MODEL
    from V_CORE_SKU VCS
           left join KING_DEE_MATERIAL KDM on VCS.SKU_NO = KDM.F_NUMBER
    where VCS.CHECK_STATUS = 3
      and KDM.KING_DEE_MATERIAL_ENTITY_ID is null
      <if test="beginTime != null">
        and VCS.ADD_TIME <![CDATA[>=]]> #{beginTime,jdbcType=DATE}
      </if>
      <if test="endTime != null">
        and VCS.ADD_TIME <![CDATA[<=]]> #{endTime,jdbcType=DATE}
      </if>
      <if test="materialSaveExcludeSkuList != null and materialSaveExcludeSkuList.size() != 0">
        and VCS.SKU_NO not in
        <foreach collection="materialSaveExcludeSkuList" item="skuNo" index="index"
                 open="(" close=")" separator=",">
          #{skuNo}
        </foreach>
      </if>
    group by VCS.SKU_ID
    limit #{_pagesize} OFFSET #{_skiprows}
  </select>

  <select id="selectStandardCategoryByCategoryId" resultType="java.lang.String">
    SELECT
    IF(
    c.CATEGORY_NAME = '' OR c.CATEGORY_NAME IS NULL,
    IF(
    b.CATEGORY_NAME = '' OR b.CATEGORY_NAME IS NULL,
    IF(a.CATEGORY_NAME = '' OR a.CATEGORY_NAME IS NULL, '--', a.CATEGORY_NAME),
    CONCAT(a.CATEGORY_NAME, '>', b.CATEGORY_NAME)
    ),
    CONCAT(c.CATEGORY_NAME, '>', b.CATEGORY_NAME, '>', a.CATEGORY_NAME)
    ) AS categoryName
    FROM
    T_STANDARD_CATEGORY a
    LEFT JOIN T_STANDARD_CATEGORY b ON a.PARENT_ID = b.STANDARD_CATEGORY_ID AND b.`STATUS` = 1
    LEFT JOIN T_STANDARD_CATEGORY c ON b.PARENT_ID = c.STANDARD_CATEGORY_ID AND c.`STATUS` = 1
    WHERE
    a.STANDARD_CATEGORY_ID =
    #{first.newStandardCategoryId, jdbcType=INTEGER}
  </select>

  <select id="selectByOldStandardCategory" resultType="java.lang.String">
    select
    TITLE
    from T_SYS_OPTION_DEFINITION
    where SYS_OPTION_DEFINITION_ID = #{id,jdbcType=INTEGER}
  </select>
</mapper>