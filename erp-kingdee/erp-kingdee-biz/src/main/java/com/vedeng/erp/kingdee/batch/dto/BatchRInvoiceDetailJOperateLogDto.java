package com.vedeng.erp.kingdee.batch.dto;

import com.vedeng.erp.kingdee.batch.common.bean.BatchBaseDto;
import lombok.*;

import java.math.BigDecimal;

/**
 * 发票详情和出入库详情关系表
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchRInvoiceDetailJOperateLogDto extends BatchBaseDto {


    private Integer rInvoiceDetailJOperateLogId;

    /**
     * 发票详情ID
     */
    private Integer invoiceDetailId;

    /**
     * 出入库日志ID
     */
    private Integer operateLogId;

    /**
     * 发票ID
     */
    private Integer invoiceId;

    /**
     * 订单详情ID
     */
    private Integer detailGoodsId;

    /**
     * 商品ID
     */
    private Integer goodsId;

    /**
     * 唯一编码
     */
    private String sku;

    /**
     * 关联数量
     */
    private BigDecimal num;


    /**
     * 操作类型 '操作类型 1采购入库 2销售出库 3销售换货入库 4销售换货出库 5销售退货入库 6采购退货出库 7采购换货出库 8采购换货入库 9外借入库 10外借出库  12盘盈入库 13报废出库 14领用出库 16 盘亏出库,',
     */
    private Integer operateType;

    /**
     * 是否删除0否1是
     */
    private Integer isDelete;

    /**
     * 关联的出入库单号
     */
    private String outInNo;
    /**
     * 关系来源类型
     * 0：真实出入库与红蓝票关系
     * 1：虚拟出入库与红票关系(由红票生成，无法找到蓝票)
     * 2：虚拟出库与蓝票关系（有虚拟出库和红票关系生成）
     */
    private Integer sourceType;

    /**
     * 可关联数量
     */
    private BigDecimal canRelationNum;

    /**
     * 关联详情ID
     */
    private Integer relatedId;


    /**
     * @组合对象@ 判断是否再21-22 的采购费用内
     */
    private Boolean hasExpense = Boolean.FALSE;

    /**
     * @组合对象@ 出入库单据ID
     */
    private Integer warehouseGoodsOutInId;

}