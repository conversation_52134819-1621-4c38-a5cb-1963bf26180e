<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.system.mapper.VerifiesInfoEntityMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.system.domain.entity.VerifiesInfoEntity">
    <!--@mbg.generated-->
    <!--@Table T_VERIFIES_INFO-->
    <id column="VERIFIES_INFO_ID" jdbcType="INTEGER" property="verifiesInfoId" />
    <result column="RELATE_TABLE" jdbcType="VARCHAR" property="relateTable" />
    <result column="RELATE_TABLE_KEY" jdbcType="INTEGER" property="relateTableKey" />
    <result column="VERIFIES_TYPE" jdbcType="INTEGER" property="verifiesType" />
    <result column="LAST_VERIFY_USERNAME" jdbcType="VARCHAR" property="lastVerifyUsername" />
    <result column="VERIFY_USERNAME" jdbcType="VARCHAR" property="verifyUsername" />
    <result column="STATUS" jdbcType="INTEGER" property="status" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="VERIFYER_ID" jdbcType="INTEGER" property="verifyerId" />
    <result column="APPLYER_ID" jdbcType="INTEGER" property="applyerId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    VERIFIES_INFO_ID, RELATE_TABLE, RELATE_TABLE_KEY, VERIFIES_TYPE, LAST_VERIFY_USERNAME, 
    VERIFY_USERNAME, `STATUS`, ADD_TIME, MOD_TIME, VERIFYER_ID, APPLYER_ID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_VERIFIES_INFO
    where VERIFIES_INFO_ID = #{verifiesInfoId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_VERIFIES_INFO
    where VERIFIES_INFO_ID = #{verifiesInfoId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="VERIFIES_INFO_ID" keyProperty="verifiesInfoId" parameterType="com.vedeng.erp.system.domain.entity.VerifiesInfoEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_VERIFIES_INFO (RELATE_TABLE, RELATE_TABLE_KEY, VERIFIES_TYPE, 
      LAST_VERIFY_USERNAME, VERIFY_USERNAME, `STATUS`, 
      ADD_TIME, MOD_TIME, VERIFYER_ID, 
      APPLYER_ID)
    values (#{relateTable,jdbcType=VARCHAR}, #{relateTableKey,jdbcType=INTEGER}, #{verifiesType,jdbcType=INTEGER}, 
      #{lastVerifyUsername,jdbcType=VARCHAR}, #{verifyUsername,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{addTime,jdbcType=BIGINT}, #{modTime,jdbcType=BIGINT}, #{verifyerId,jdbcType=INTEGER}, 
      #{applyerId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="VERIFIES_INFO_ID" keyProperty="verifiesInfoId" parameterType="com.vedeng.erp.system.domain.entity.VerifiesInfoEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_VERIFIES_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="relateTable != null">
        RELATE_TABLE,
      </if>
      <if test="relateTableKey != null">
        RELATE_TABLE_KEY,
      </if>
      <if test="verifiesType != null">
        VERIFIES_TYPE,
      </if>
      <if test="lastVerifyUsername != null">
        LAST_VERIFY_USERNAME,
      </if>
      <if test="verifyUsername != null">
        VERIFY_USERNAME,
      </if>
      <if test="status != null">
        `STATUS`,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="verifyerId != null">
        VERIFYER_ID,
      </if>
      <if test="applyerId != null">
        APPLYER_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="relateTable != null">
        #{relateTable,jdbcType=VARCHAR},
      </if>
      <if test="relateTableKey != null">
        #{relateTableKey,jdbcType=INTEGER},
      </if>
      <if test="verifiesType != null">
        #{verifiesType,jdbcType=INTEGER},
      </if>
      <if test="lastVerifyUsername != null">
        #{lastVerifyUsername,jdbcType=VARCHAR},
      </if>
      <if test="verifyUsername != null">
        #{verifyUsername,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="verifyerId != null">
        #{verifyerId,jdbcType=INTEGER},
      </if>
      <if test="applyerId != null">
        #{applyerId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.system.domain.entity.VerifiesInfoEntity">
    <!--@mbg.generated-->
    update T_VERIFIES_INFO
    <set>
      <if test="relateTable != null">
        RELATE_TABLE = #{relateTable,jdbcType=VARCHAR},
      </if>
      <if test="relateTableKey != null">
        RELATE_TABLE_KEY = #{relateTableKey,jdbcType=INTEGER},
      </if>
      <if test="verifiesType != null">
        VERIFIES_TYPE = #{verifiesType,jdbcType=INTEGER},
      </if>
      <if test="lastVerifyUsername != null">
        LAST_VERIFY_USERNAME = #{lastVerifyUsername,jdbcType=VARCHAR},
      </if>
      <if test="verifyUsername != null">
        VERIFY_USERNAME = #{verifyUsername,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `STATUS` = #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="verifyerId != null">
        VERIFYER_ID = #{verifyerId,jdbcType=INTEGER},
      </if>
      <if test="applyerId != null">
        APPLYER_ID = #{applyerId,jdbcType=INTEGER},
      </if>
    </set>
    where VERIFIES_INFO_ID = #{verifiesInfoId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.system.domain.entity.VerifiesInfoEntity">
    <!--@mbg.generated-->
    update T_VERIFIES_INFO
    set RELATE_TABLE = #{relateTable,jdbcType=VARCHAR},
      RELATE_TABLE_KEY = #{relateTableKey,jdbcType=INTEGER},
      VERIFIES_TYPE = #{verifiesType,jdbcType=INTEGER},
      LAST_VERIFY_USERNAME = #{lastVerifyUsername,jdbcType=VARCHAR},
      VERIFY_USERNAME = #{verifyUsername,jdbcType=VARCHAR},
      `STATUS` = #{status,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      VERIFYER_ID = #{verifyerId,jdbcType=INTEGER},
      APPLYER_ID = #{applyerId,jdbcType=INTEGER}
    where VERIFIES_INFO_ID = #{verifiesInfoId,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2024-05-27-->
  <select id="findByAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_VERIFIES_INFO
    <where>
      <if test="verifiesInfoId != null">
        and VERIFIES_INFO_ID=#{verifiesInfoId,jdbcType=INTEGER}
      </if>
      <if test="relateTable != null">
        and RELATE_TABLE=#{relateTable,jdbcType=VARCHAR}
      </if>
      <if test="relateTableKey != null">
        and RELATE_TABLE_KEY=#{relateTableKey,jdbcType=INTEGER}
      </if>
      <if test="verifiesType != null">
        and VERIFIES_TYPE=#{verifiesType,jdbcType=INTEGER}
      </if>
      <if test="lastVerifyUsername != null">
        and LAST_VERIFY_USERNAME=#{lastVerifyUsername,jdbcType=VARCHAR}
      </if>
      <if test="verifyUsername != null">
        and VERIFY_USERNAME=#{verifyUsername,jdbcType=VARCHAR}
      </if>
      <if test="status != null">
        and `STATUS`=#{status,jdbcType=INTEGER}
      </if>
      <if test="addTime != null">
        and ADD_TIME=#{addTime,jdbcType=BIGINT}
      </if>
      <if test="modTime != null">
        and MOD_TIME=#{modTime,jdbcType=BIGINT}
      </if>
      <if test="verifyerId != null">
        and VERIFYER_ID=#{verifyerId,jdbcType=INTEGER}
      </if>
      <if test="applyerId != null">
        and APPLYER_ID=#{applyerId,jdbcType=INTEGER}
      </if>
    </where>
  </select>

<!--auto generated by MybatisCodeHelper on 2024-05-27-->
  <select id="findOneByAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_VERIFIES_INFO
    <where>
      <if test="verifiesInfoId != null">
        and VERIFIES_INFO_ID=#{verifiesInfoId,jdbcType=INTEGER}
      </if>
      <if test="relateTable != null">
        and RELATE_TABLE=#{relateTable,jdbcType=VARCHAR}
      </if>
      <if test="relateTableKey != null">
        and RELATE_TABLE_KEY=#{relateTableKey,jdbcType=INTEGER}
      </if>
      <if test="verifiesType != null">
        and VERIFIES_TYPE=#{verifiesType,jdbcType=INTEGER}
      </if>
      <if test="lastVerifyUsername != null">
        and LAST_VERIFY_USERNAME=#{lastVerifyUsername,jdbcType=VARCHAR}
      </if>
      <if test="verifyUsername != null">
        and VERIFY_USERNAME=#{verifyUsername,jdbcType=VARCHAR}
      </if>
      <if test="status != null">
        and `STATUS`=#{status,jdbcType=INTEGER}
      </if>
      <if test="addTime != null">
        and ADD_TIME=#{addTime,jdbcType=BIGINT}
      </if>
      <if test="modTime != null">
        and MOD_TIME=#{modTime,jdbcType=BIGINT}
      </if>
      <if test="verifyerId != null">
        and VERIFYER_ID=#{verifyerId,jdbcType=INTEGER}
      </if>
      <if test="applyerId != null">
        and APPLYER_ID=#{applyerId,jdbcType=INTEGER}
      </if>
    </where>
    order by VERIFIES_INFO_ID desc limit 1
  </select>
</mapper>