<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.confirmrecord.dao.ConfirmRecordDao">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.confirmrecord.model.ConfirmRecord">
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="BUSINESS_DESC" jdbcType="VARCHAR" property="businessDesc" />
    <result column="BUSINESS_NO" jdbcType="VARCHAR" property="businessNo" />
    <result column="BUSINESS_TYPE" jdbcType="INTEGER" property="businessType" />
    <result column="CONFIRM_TIME" jdbcType="TIMESTAMP" property="confirmTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="SEND_TYPE" jdbcType="INTEGER" property="sendType" />
    <result column="SEND_TIME" jdbcType="TIMESTAMP" property="sendTime" />
    <result column="SEND_METHOD_TYPE" jdbcType="VARCHAR" property="sendMethodType" />
    <result column="CONFIRM_STATUS" jdbcType="INTEGER" property="confirmStatus" />
    <result column="CONFIRM_NUMBER" jdbcType="INTEGER" property="confirmNumber" />

  </resultMap>

  <resultMap id="SignInRecordMap" type="com.vedeng.erp.confirmrecord.dto.SignInRecordDto">
    <result column="BUSINESS_DESC" jdbcType="VARCHAR" property="businessDesc" />
    <result column="CONFIRM_NUMBER" jdbcType="INTEGER" property="confirmNumber" />
    <result column="SEND_TIME" jdbcType="TIMESTAMP" property="sendTime" />
    <result column="GOODS_NAME" property="goodsName" jdbcType="VARCHAR" />
    <result column="SKU" property="sku" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="Base_Column_List">
    ID, BUSINESS_DESC, BUSINESS_NO, BUSINESS_TYPE, CONFIRM_TIME, CREATOR, ADD_TIME, UPDATER, 
    MOD_TIME, SEND_TYPE, SEND_TIME, SEND_METHOD_TYPE, CONFIRM_STATUS
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_CONFIRM_RECORD
    where ID = #{id,jdbcType=BIGINT}
  </select>
    <select id="queryByBusinessNo" resultMap="BaseResultMap">
      select * from T_CONFIRM_RECORD where BUSINESS_NO=#{saleorderNo}
      order by MOD_TIME asc
    </select>
  <select id="selectTodayNum" resultType="java.lang.Integer">
    select count(*) from T_CONFIRM_RECORD where BUSINESS_NO=#{saleorderNo} and SEND_TYPE=1 and DATE_FORMAT(ADD_TIME,'%Y-%m-%d')=#{nowTime}
  </select>
  <select id="selectSendCount" resultType="java.lang.Integer">
    select count(*) from T_CONFIRM_RECORD where BUSINESS_NO=#{saleorderNo} and SEND_TYPE=1
  </select>
    <select id="queryCustomerSignature" resultMap="SignInRecordMap">
      		 select c.CONFIRM_NUMBER,c.SEND_TIME,b.GOODS_NAME,b.SKU from T_SALEORDER a left join T_SALEORDER_GOODS b on a.SALEORDER_ID=b.SALEORDER_ID
		 left join T_CONFIRM_RECORD c on b.SALEORDER_GOODS_ID = c.BUSINESS_NO
		 where a.SALEORDER_ID=#{saleorderId}
		 and c.ID is not null
		  and c.CONFIRM_NUMBER>0
		 order by c.CONFIRM_TIME asc
    </select>
  <select id="queryConfirmRecordInfoByBusinessNo"
          resultType="com.vedeng.erp.confirmrecord.model.ConfirmRecord">
    select * from T_CONFIRM_RECORD where BUSINESS_NO = #{businessNo} and BUSINESS_TYPE = 1 and CONFIRM_STATUS = 0 order by SEND_TIME DESC limit 1
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from T_CONFIRM_RECORD
    where ID = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.vedeng.erp.confirmrecord.model.ConfirmRecord">
    insert into T_CONFIRM_RECORD (ID, BUSINESS_DESC, BUSINESS_NO, 
      BUSINESS_TYPE, CONFIRM_TIME, CREATOR, 
      ADD_TIME, UPDATER, MOD_TIME, 
      SEND_TYPE, SEND_TIME, SEND_METHOD_TYPE, 
      CONFIRM_STATUS)
    values (#{id,jdbcType=BIGINT}, #{businessDesc,jdbcType=VARCHAR}, #{businessNo,jdbcType=VARCHAR}, 
      #{businessType,jdbcType=INTEGER}, #{confirmTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER}, #{modTime,jdbcType=TIMESTAMP}, 
      #{sendType,jdbcType=INTEGER}, #{sendTime,jdbcType=TIMESTAMP}, #{sendMethodType,jdbcType=VARCHAR}, 
      #{confirmStatus,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.erp.confirmrecord.model.ConfirmRecord" useGeneratedKeys="true" keyProperty="id">
    insert into T_CONFIRM_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="businessDesc != null">
        BUSINESS_DESC,
      </if>
      <if test="businessNo != null">
        BUSINESS_NO,
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE,
      </if>
      <if test="confirmTime != null">
        CONFIRM_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="sendType != null">
        SEND_TYPE,
      </if>
      <if test="sendTime != null">
        SEND_TIME,
      </if>
      <if test="sendMethodType != null">
        SEND_METHOD_TYPE,
      </if>
      <if test="confirmNumber != null">
        CONFIRM_NUMBER,
      </if>
      <if test="confirmStatus != null">
        CONFIRM_STATUS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="businessDesc != null">
        #{businessDesc,jdbcType=VARCHAR},
      </if>
      <if test="businessNo != null">
        #{businessNo,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=INTEGER},
      </if>
      <if test="confirmTime != null">
        #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sendType != null">
        #{sendType,jdbcType=INTEGER},
      </if>
      <if test="sendTime != null">
        #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sendMethodType != null">
        #{sendMethodType,jdbcType=VARCHAR},
      </if>
      <if test="confirmNumber != null">
        #{confirmNumber,jdbcType=INTEGER},
      </if>
      <if test="confirmStatus != null">
        #{confirmStatus,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.confirmrecord.model.ConfirmRecord">
    update T_CONFIRM_RECORD
    <set>
      <if test="businessDesc != null">
        BUSINESS_DESC = #{businessDesc,jdbcType=VARCHAR},
      </if>
      <if test="businessNo != null">
        BUSINESS_NO = #{businessNo,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE = #{businessType,jdbcType=INTEGER},
      </if>
      <if test="confirmTime != null">
        CONFIRM_TIME = #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sendType != null">
        SEND_TYPE = #{sendType,jdbcType=INTEGER},
      </if>
      <if test="sendTime != null">
        SEND_TIME = #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sendMethodType != null">
        SEND_METHOD_TYPE = #{sendMethodType,jdbcType=VARCHAR},
      </if>
      <if test="confirmStatus != null">
        CONFIRM_STATUS = #{confirmStatus,jdbcType=INTEGER},
      </if>
    </set>
    where ID = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.confirmrecord.model.ConfirmRecord">
    update T_CONFIRM_RECORD
    set BUSINESS_DESC = #{businessDesc,jdbcType=VARCHAR},
      BUSINESS_NO = #{businessNo,jdbcType=VARCHAR},
      BUSINESS_TYPE = #{businessType,jdbcType=INTEGER},
      CONFIRM_TIME = #{confirmTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      SEND_TYPE = #{sendType,jdbcType=INTEGER},
      SEND_TIME = #{sendTime,jdbcType=TIMESTAMP},
      SEND_METHOD_TYPE = #{sendMethodType,jdbcType=VARCHAR},
      CONFIRM_STATUS = #{confirmStatus,jdbcType=INTEGER}
    where ID = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateConfirmTimeAndStatus">
    update T_CONFIRM_RECORD set
    CONFIRM_TIME =#{confirmTime},
    MOD_TIME =#{modTime},
    CONFIRM_STATUS =#{confirmStatus}
    where BUSINESS_NO=#{businessNo}
  </update>
  <update id="updateConfirmInfo">
    <foreach collection="list" index="index" item="item" separator=";">
      update T_CONFIRM_RECORD
      <set>
        <if test="item.businessDesc != null">
          BUSINESS_DESC = #{item.businessDesc,jdbcType=VARCHAR},
        </if>
        <if test="item.businessNo != null">
          BUSINESS_NO = #{item.businessNo,jdbcType=VARCHAR},
        </if>
        <if test="item.businessType != null">
          BUSINESS_TYPE = #{item.businessType,jdbcType=INTEGER},
        </if>
        <if test="item.confirmTime != null">
          CONFIRM_TIME = #{item.confirmTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.creator != null">
          CREATOR = #{item.creator,jdbcType=INTEGER},
        </if>
        <if test="item.addTime != null">
          ADD_TIME = #{item.addTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.updater != null">
          UPDATER = #{item.updater,jdbcType=INTEGER},
        </if>
        <if test="item.modTime != null">
          MOD_TIME = #{item.modTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.sendType != null">
          SEND_TYPE = #{item.sendType,jdbcType=INTEGER},
        </if>
        <if test="item.sendTime != null">
          SEND_TIME = #{list.sendTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.sendMethodType != null">
          SEND_METHOD_TYPE = #{item.sendMethodType,jdbcType=VARCHAR},
        </if>
        <if test="item.confirmNumber != null">
          CONFIRM_NUMBER = #{item.confirmNumber,jdbcType=INTEGER},
        </if>
        <if test="item.confirmStatus != null">
          CONFIRM_STATUS = #{item.confirmStatus,jdbcType=INTEGER},
        </if>
      </set>
      where
      BUSINESS_NO = #{item.businessNo}
      <if test="item.id != null">
         AND ID = #{item.id}
      </if>
      <if test="item.businessType != null">
        and BUSINESS_TYPE = #{item.businessType,jdbcType=INTEGER}
      </if>
    </foreach>
  </update>

</mapper>