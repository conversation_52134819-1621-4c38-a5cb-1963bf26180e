<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.TraderCustomerMarketingPrincipalMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.TraderCustomerMarketingPrincipalEntity">
    <!--@mbg.generated-->
    <!--@Table T_TRADER_CUSTOMER_MARKETING_PRINCIPAL-->
    <id column="TRADER_CUSTOMER_MARKETING_PRINCIPAL_ID" jdbcType="INTEGER" property="traderCustomerMarketingPrincipalId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_CUSTOMER_ID" jdbcType="INTEGER" property="traderCustomerId" />
    <result column="SKU_TYPE" jdbcType="VARCHAR" property="skuType" />
    <result column="SKU_SCOPE" jdbcType="VARCHAR" property="skuScope" />
    <result column="SKU_CATEGORY" jdbcType="VARCHAR" property="skuCategory" />
    <result column="SALES_TYPE" jdbcType="VARCHAR" property="salesType" />
    <result column="AGENCY_BRAND" jdbcType="VARCHAR" property="agencyBrand" />
    <result column="GOVERNMENT_RELATION" jdbcType="VARCHAR" property="governmentRelation" />
    <result column="OTHER_AGENCY_BRAND" jdbcType="VARCHAR" property="otherAgencyBrand" />
    <result column="EFFECTIVENESS" jdbcType="INTEGER" property="effectiveness" />
    <result column="OTHER_GOVERNMENT_RELATION" jdbcType="VARCHAR" property="otherGovernmentRelation" />
    <result column="AGENCY_SKU" jdbcType="VARCHAR" property="agencySku" />
    <result column="OTHER_AGENCY_SKU" jdbcType="VARCHAR" property="otherAgencySku" />
    <result column="IS_CHANNEL_CUSTOMER" jdbcType="INTEGER" property="isChannelCustomer" />
    <result column="IS_PRODUCT_CUSTOMER" jdbcType="INTEGER" property="isProductCustomer" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    TRADER_CUSTOMER_MARKETING_PRINCIPAL_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME,
    UPDATER_NAME, TRADER_ID, TRADER_CUSTOMER_ID, SKU_TYPE, SKU_SCOPE, SKU_CATEGORY, SALES_TYPE,
    AGENCY_BRAND, GOVERNMENT_RELATION, OTHER_AGENCY_BRAND, EFFECTIVENESS, OTHER_GOVERNMENT_RELATION,AGENCY_SKU,OTHER_AGENCY_SKU,IS_CHANNEL_CUSTOMER,IS_PRODUCT_CUSTOMER
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_TRADER_CUSTOMER_MARKETING_PRINCIPAL
    where TRADER_CUSTOMER_MARKETING_PRINCIPAL_ID = #{traderCustomerMarketingPrincipalId,jdbcType=INTEGER}
  </select>
  <select id="getByTraderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_TRADER_CUSTOMER_MARKETING_PRINCIPAL
    where TRADER_ID = #{traderId,jdbcType=INTEGER}
    limit 1
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_TRADER_CUSTOMER_MARKETING_PRINCIPAL
    where TRADER_CUSTOMER_MARKETING_PRINCIPAL_ID = #{traderId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="TRADER_CUSTOMER_MARKETING_PRINCIPAL_ID" keyProperty="traderCustomerMarketingPrincipalId" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerMarketingPrincipalEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_CUSTOMER_MARKETING_PRINCIPAL (ADD_TIME, MOD_TIME, CREATOR,
    UPDATER, CREATOR_NAME, UPDATER_NAME,
    TRADER_ID, TRADER_CUSTOMER_ID, SKU_TYPE,
    SKU_SCOPE, SKU_CATEGORY, SALES_TYPE,
    AGENCY_BRAND, GOVERNMENT_RELATION, OTHER_AGENCY_BRAND,
    EFFECTIVENESS, OTHER_GOVERNMENT_RELATION)
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER},
    #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR},
    #{traderId,jdbcType=INTEGER}, #{traderCustomerId,jdbcType=INTEGER}, #{skuType,jdbcType=VARCHAR},
    #{skuScope,jdbcType=VARCHAR}, #{skuCategory,jdbcType=VARCHAR}, #{salesType,jdbcType=VARCHAR},
    #{agencyBrand,jdbcType=VARCHAR}, #{governmentRelation,jdbcType=VARCHAR}, #{otherAgencyBrand,jdbcType=VARCHAR},
    #{effectiveness,jdbcType=INTEGER}, #{otherGovernmentRelation,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="TRADER_CUSTOMER_MARKETING_PRINCIPAL_ID" keyProperty="traderCustomerMarketingPrincipalId" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerMarketingPrincipalEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_CUSTOMER_MARKETING_PRINCIPAL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderCustomerId != null">
        TRADER_CUSTOMER_ID,
      </if>
      <if test="skuType != null">
        SKU_TYPE,
      </if>
      <if test="skuScope != null">
        SKU_SCOPE,
      </if>
      <if test="skuCategory != null">
        SKU_CATEGORY,
      </if>
      <if test="salesType != null">
        SALES_TYPE,
      </if>
      <if test="agencyBrand != null">
        AGENCY_BRAND,
      </if>
      <if test="governmentRelation != null">
        GOVERNMENT_RELATION,
      </if>
      <if test="otherAgencyBrand != null">
        OTHER_AGENCY_BRAND,
      </if>
      <if test="effectiveness != null">
        EFFECTIVENESS,
      </if>
      <if test="otherGovernmentRelation != null">
        OTHER_GOVERNMENT_RELATION,
      </if>
      <if test="agencySku != null">
        AGENCY_SKU,
      </if>
      <if test="otherAgencySku != null">
        OTHER_AGENCY_SKU,
      </if>
      <if test="isChannelCustomer != null">
        IS_CHANNEL_CUSTOMER,
      </if>
      <if test="isProductCustomer != null">
        IS_PRODUCT_CUSTOMER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderCustomerId != null">
        #{traderCustomerId,jdbcType=INTEGER},
      </if>
      <if test="skuType != null">
        #{skuType,jdbcType=VARCHAR},
      </if>
      <if test="skuScope != null">
        #{skuScope,jdbcType=VARCHAR},
      </if>
      <if test="skuCategory != null">
        #{skuCategory,jdbcType=VARCHAR},
      </if>
      <if test="salesType != null">
        #{salesType,jdbcType=VARCHAR},
      </if>
      <if test="agencyBrand != null">
        #{agencyBrand,jdbcType=VARCHAR},
      </if>
      <if test="governmentRelation != null">
        #{governmentRelation,jdbcType=VARCHAR},
      </if>
      <if test="otherAgencyBrand != null">
        #{otherAgencyBrand,jdbcType=VARCHAR},
      </if>
      <if test="effectiveness != null">
        #{effectiveness,jdbcType=INTEGER},
      </if>
      <if test="otherGovernmentRelation != null">
        #{otherGovernmentRelation,jdbcType=VARCHAR},
      </if>
      <if test="agencySku != null">
        #{agencySku,jdbcType=VARCHAR},
      </if>
      <if test="otherAgencySku != null">
        #{otherAgencySku,jdbcType=VARCHAR},
      </if>
      <if test="isChannelCustomer != null">
        #{isChannelCustomer,jdbcType=INTEGER},
      </if>
      <if test="isProductCustomer != null">
        #{isProductCustomer,jdbcType=INTEGER},
      </if>

    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerMarketingPrincipalEntity">
    <!--@mbg.generated-->
    update T_TRADER_CUSTOMER_MARKETING_PRINCIPAL
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderCustomerId != null">
        TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER},
      </if>
      <if test="skuType != null">
        SKU_TYPE = #{skuType,jdbcType=VARCHAR},
      </if>
      <if test="skuScope != null">
        SKU_SCOPE = #{skuScope,jdbcType=VARCHAR},
      </if>
      <if test="skuCategory != null">
        SKU_CATEGORY = #{skuCategory,jdbcType=VARCHAR},
      </if>
      <if test="salesType != null">
        SALES_TYPE = #{salesType,jdbcType=VARCHAR},
      </if>
      <if test="agencyBrand != null">
        AGENCY_BRAND = #{agencyBrand,jdbcType=VARCHAR},
      </if>
      <if test="governmentRelation != null">
        GOVERNMENT_RELATION = #{governmentRelation,jdbcType=VARCHAR},
      </if>
      <if test="otherAgencyBrand != null">
        OTHER_AGENCY_BRAND = #{otherAgencyBrand,jdbcType=VARCHAR},
      </if>
      <if test="effectiveness != null">
        EFFECTIVENESS = #{effectiveness,jdbcType=INTEGER},
      </if>
      <if test="otherGovernmentRelation != null">
        OTHER_GOVERNMENT_RELATION = #{otherGovernmentRelation,jdbcType=VARCHAR},
      </if>
      <if test="isProductCustomer != null">
        IS_PRODUCT_CUSTOMER = #{isProductCustomer,jdbcType=INTEGER},
      </if>
      <if test="isChannelCustomer != null">
        IS_CHANNEL_CUSTOMER = #{isChannelCustomer,jdbcType=INTEGER},
      </if>
    </set>
    where TRADER_CUSTOMER_MARKETING_PRINCIPAL_ID = #{traderCustomerMarketingPrincipalId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerMarketingPrincipalEntity">
    <!--@mbg.generated-->
    update T_TRADER_CUSTOMER_MARKETING_PRINCIPAL
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
    MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
    CREATOR = #{creator,jdbcType=INTEGER},
    UPDATER = #{updater,jdbcType=INTEGER},
    CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
    UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
    TRADER_ID = #{traderId,jdbcType=INTEGER},
    TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER},
    SKU_TYPE = #{skuType,jdbcType=VARCHAR},
    SKU_SCOPE = #{skuScope,jdbcType=VARCHAR},
    SKU_CATEGORY = #{skuCategory,jdbcType=VARCHAR},
    SALES_TYPE = #{salesType,jdbcType=VARCHAR},
    AGENCY_BRAND = #{agencyBrand,jdbcType=VARCHAR},
    GOVERNMENT_RELATION = #{governmentRelation,jdbcType=VARCHAR},
    OTHER_AGENCY_BRAND = #{otherAgencyBrand,jdbcType=VARCHAR},
    EFFECTIVENESS = #{effectiveness,jdbcType=INTEGER},
    OTHER_GOVERNMENT_RELATION = #{otherGovernmentRelation,jdbcType=VARCHAR}
    where TRADER_CUSTOMER_MARKETING_PRINCIPAL_ID = #{traderCustomerMarketingPrincipalId,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="TRADER_CUSTOMER_MARKETING_PRINCIPAL_ID" keyProperty="traderCustomerMarketingPrincipalId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_CUSTOMER_MARKETING_PRINCIPAL
    (ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, TRADER_ID, TRADER_CUSTOMER_ID,
    SKU_TYPE, SKU_SCOPE, SKU_CATEGORY, SALES_TYPE, AGENCY_BRAND, GOVERNMENT_RELATION,
    OTHER_AGENCY_BRAND, EFFECTIVENESS, OTHER_GOVERNMENT_RELATION)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER},
      #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, #{item.updaterName,jdbcType=VARCHAR},
      #{item.traderId,jdbcType=INTEGER}, #{item.traderCustomerId,jdbcType=INTEGER}, #{item.skuType,jdbcType=VARCHAR},
      #{item.skuScope,jdbcType=VARCHAR}, #{item.skuCategory,jdbcType=VARCHAR}, #{item.salesType,jdbcType=VARCHAR},
      #{item.agencyBrand,jdbcType=VARCHAR}, #{item.governmentRelation,jdbcType=VARCHAR},
      #{item.otherAgencyBrand,jdbcType=VARCHAR}, #{item.effectiveness,jdbcType=INTEGER},
      #{item.otherGovernmentRelation,jdbcType=VARCHAR})
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2023-08-17-->
  <select id="selectByTraderCustomerId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_TRADER_CUSTOMER_MARKETING_PRINCIPAL
        where TRADER_CUSTOMER_ID=#{traderCustomerId,jdbcType=INTEGER}
    </select>

<!--auto generated by MybatisCodeHelper on 2023-08-17-->
  <select id="findByTraderIdAndTraderCustomerId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_TRADER_CUSTOMER_MARKETING_PRINCIPAL
    where TRADER_ID=#{traderId,jdbcType=INTEGER} and TRADER_CUSTOMER_ID=#{traderCustomerId,jdbcType=INTEGER}
  </select>


  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_TRADER_CUSTOMER_MARKETING_PRINCIPAL
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when TRADER_CUSTOMER_MARKETING_PRINCIPAL_ID = #{item.traderCustomerMarketingPrincipalId,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when TRADER_CUSTOMER_MARKETING_PRINCIPAL_ID = #{item.traderCustomerMarketingPrincipalId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when TRADER_CUSTOMER_MARKETING_PRINCIPAL_ID = #{item.traderCustomerMarketingPrincipalId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when TRADER_CUSTOMER_MARKETING_PRINCIPAL_ID = #{item.traderCustomerMarketingPrincipalId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when TRADER_CUSTOMER_MARKETING_PRINCIPAL_ID = #{item.traderCustomerMarketingPrincipalId,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when TRADER_CUSTOMER_MARKETING_PRINCIPAL_ID = #{item.traderCustomerMarketingPrincipalId,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRADER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.traderId != null">
            when TRADER_CUSTOMER_MARKETING_PRINCIPAL_ID = #{item.traderCustomerMarketingPrincipalId,jdbcType=INTEGER} then #{item.traderId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRADER_CUSTOMER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.traderCustomerId != null">
            when TRADER_CUSTOMER_MARKETING_PRINCIPAL_ID = #{item.traderCustomerMarketingPrincipalId,jdbcType=INTEGER} then #{item.traderCustomerId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="SKU_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.skuType != null">
            when TRADER_CUSTOMER_MARKETING_PRINCIPAL_ID = #{item.traderCustomerMarketingPrincipalId,jdbcType=INTEGER} then #{item.skuType,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SKU_SCOPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.skuScope != null">
            when TRADER_CUSTOMER_MARKETING_PRINCIPAL_ID = #{item.traderCustomerMarketingPrincipalId,jdbcType=INTEGER} then #{item.skuScope,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SKU_CATEGORY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.skuCategory != null">
            when TRADER_CUSTOMER_MARKETING_PRINCIPAL_ID = #{item.traderCustomerMarketingPrincipalId,jdbcType=INTEGER} then #{item.skuCategory,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SALES_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.salesType != null">
            when TRADER_CUSTOMER_MARKETING_PRINCIPAL_ID = #{item.traderCustomerMarketingPrincipalId,jdbcType=INTEGER} then #{item.salesType,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="AGENCY_BRAND = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.agencyBrand != null">
            when TRADER_CUSTOMER_MARKETING_PRINCIPAL_ID = #{item.traderCustomerMarketingPrincipalId,jdbcType=INTEGER} then #{item.agencyBrand,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="GOVERNMENT_RELATION = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.governmentRelation != null">
            when TRADER_CUSTOMER_MARKETING_PRINCIPAL_ID = #{item.traderCustomerMarketingPrincipalId,jdbcType=INTEGER} then #{item.governmentRelation,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="OTHER_AGENCY_BRAND = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.otherAgencyBrand != null">
            when TRADER_CUSTOMER_MARKETING_PRINCIPAL_ID = #{item.traderCustomerMarketingPrincipalId,jdbcType=INTEGER} then #{item.otherAgencyBrand,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="EFFECTIVENESS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.effectiveness != null">
            when TRADER_CUSTOMER_MARKETING_PRINCIPAL_ID = #{item.traderCustomerMarketingPrincipalId,jdbcType=INTEGER} then #{item.effectiveness,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="OTHER_GOVERNMENT_RELATION = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.otherGovernmentRelation != null">
            when TRADER_CUSTOMER_MARKETING_PRINCIPAL_ID = #{item.traderCustomerMarketingPrincipalId,jdbcType=INTEGER} then #{item.otherGovernmentRelation,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where TRADER_CUSTOMER_MARKETING_PRINCIPAL_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.traderCustomerMarketingPrincipalId,jdbcType=INTEGER}
    </foreach>
  </update>

</mapper>