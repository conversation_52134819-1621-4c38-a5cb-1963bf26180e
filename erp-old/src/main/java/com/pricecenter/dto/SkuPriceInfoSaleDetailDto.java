package com.pricecenter.dto;

import java.math.BigDecimal;

public class SkuPriceInfoSaleDetailDto {

    private Long id;

    private Long skuId;

    private BigDecimal marketPrice;

    private BigDecimal terminalPrice;

    private BigDecimal distributionPrice;

    private BigDecimal groupPrice;

    private BigDecimal electronicCommercePrice;

    private BigDecimal researchTerminalPrice;

    private Integer purchaseContainsFee;

    private Integer saleContainsFee;

    private String modTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public BigDecimal getMarketPrice() {
        return marketPrice;
    }

    public void setMarketPrice(BigDecimal marketPrice) {
        this.marketPrice = marketPrice;
    }

    public BigDecimal getTerminalPrice() {
        return terminalPrice;
    }

    public void setTerminalPrice(BigDecimal terminalPrice) {
        this.terminalPrice = terminalPrice;
    }

    public BigDecimal getDistributionPrice() {
        return distributionPrice;
    }

    public void setDistributionPrice(BigDecimal distributionPrice) {
        this.distributionPrice = distributionPrice;
    }

    public String getModTime() {
        return modTime;
    }

    public void setModTime(String modTime) {
        this.modTime = modTime;
    }

    public BigDecimal getGroupPrice() {
        return groupPrice;
    }

    public void setGroupPrice(BigDecimal groupPrice) {
        this.groupPrice = groupPrice;
    }

    public BigDecimal getElectronicCommercePrice() {
        return electronicCommercePrice;
    }

    public void setElectronicCommercePrice(BigDecimal electronicCommercePrice) {
        this.electronicCommercePrice = electronicCommercePrice;
    }

    public BigDecimal getResearchTerminalPrice() {
        return researchTerminalPrice;
    }

    public void setResearchTerminalPrice(BigDecimal researchTerminalPrice) {
        this.researchTerminalPrice = researchTerminalPrice;
    }

    public Integer getPurchaseContainsFee() {
        return purchaseContainsFee;
    }

    public void setPurchaseContainsFee(Integer purchaseContainsFee) {
        this.purchaseContainsFee = purchaseContainsFee;
    }

    public Integer getSaleContainsFee() {
        return saleContainsFee;
    }

    public void setSaleContainsFee(Integer saleContainsFee) {
        this.saleContainsFee = saleContainsFee;
    }
}
