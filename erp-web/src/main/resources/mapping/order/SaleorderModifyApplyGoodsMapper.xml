<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.order.dao.SaleorderModifyApplyGoodsMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.order.model.SaleorderModifyApplyGoods" >
    <id column="SALEORDER_MODIFY_APPLY_GOODS_ID" property="saleorderModifyApplyGoodsId" jdbcType="INTEGER" />
    <result column="SALEORDER_MODIFY_APPLY_ID" property="saleorderModifyApplyId" jdbcType="INTEGER" />
    <result column="SALEORDER_GOODS_ID" property="saleorderGoodsId" jdbcType="INTEGER" />
    <result column="DELIVERY_DIRECT" property="deliveryDirect" jdbcType="BIT" />
    <result column="DELIVERY_DIRECT_COMMENTS" property="deliveryDirectComments" jdbcType="VARCHAR" />
    <result column="OLD_DELIVERY_DIRECT" property="oldDeliveryDirect" jdbcType="BIT" />
    <result column="OLD_DELIVERY_DIRECT_COMMENTS" property="oldDeliveryDirectComments" jdbcType="VARCHAR" />
    <result column="GOODS_COMMENTS" property="goodsComments" jdbcType="VARCHAR" />
    <result column="OLD_GOODS_COMMENTS" property="oldGoodsComments" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    SALEORDER_MODIFY_APPLY_GOODS_ID, SALEORDER_MODIFY_APPLY_ID, SALEORDER_GOODS_ID, DELIVERY_DIRECT, 
    DELIVERY_DIRECT_COMMENTS, OLD_DELIVERY_DIRECT, OLD_DELIVERY_DIRECT_COMMENTS, GOODS_COMMENTS, OLD_GOODS_COMMENTS
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from T_SALEORDER_MODIFY_APPLY_GOODS
    where SALEORDER_MODIFY_APPLY_GOODS_ID = #{saleorderModifyApplyGoodsId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from T_SALEORDER_MODIFY_APPLY_GOODS
    where SALEORDER_MODIFY_APPLY_GOODS_ID = #{saleorderModifyApplyGoodsId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.order.model.SaleorderModifyApplyGoods" >
    insert into T_SALEORDER_MODIFY_APPLY_GOODS (SALEORDER_MODIFY_APPLY_GOODS_ID, SALEORDER_MODIFY_APPLY_ID, 
      SALEORDER_GOODS_ID, DELIVERY_DIRECT, DELIVERY_DIRECT_COMMENTS, 
      OLD_DELIVERY_DIRECT, OLD_DELIVERY_DIRECT_COMMENTS, GOODS_COMMENTS, OLD_GOODS_COMMENTS
      )
    values (#{saleorderModifyApplyGoodsId,jdbcType=INTEGER}, #{saleorderModifyApplyId,jdbcType=INTEGER}, 
      #{saleorderGoodsId,jdbcType=INTEGER}, #{deliveryDirect,jdbcType=BIT}, #{deliveryDirectComments,jdbcType=VARCHAR}, 
      #{oldDeliveryDirect,jdbcType=BIT}, #{oldDeliveryDirectComments,jdbcType=VARCHAR}, #{goodsComments,jdbcType=VARCHAR}, #{oldGoodsComments,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.order.model.SaleorderModifyApplyGoods" >
    insert into T_SALEORDER_MODIFY_APPLY_GOODS
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="saleorderModifyApplyGoodsId != null" >
        SALEORDER_MODIFY_APPLY_GOODS_ID,
      </if>
      <if test="saleorderModifyApplyId != null" >
        SALEORDER_MODIFY_APPLY_ID,
      </if>
      <if test="saleorderGoodsId != null" >
        SALEORDER_GOODS_ID,
      </if>
      <if test="deliveryDirect != null" >
        DELIVERY_DIRECT,
      </if>
      <if test="deliveryDirectComments != null" >
        DELIVERY_DIRECT_COMMENTS,
      </if>
      <if test="oldDeliveryDirect != null" >
        OLD_DELIVERY_DIRECT,
      </if>
      <if test="oldDeliveryDirectComments != null" >
        OLD_DELIVERY_DIRECT_COMMENTS,
      </if>
      <if test="goodsComments != null" >
        GOODS_COMMENTS,
      </if>
      <if test="oldGoodsComments != null" >
        OLD_GOODS_COMMENTS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="saleorderModifyApplyGoodsId != null" >
        #{saleorderModifyApplyGoodsId,jdbcType=INTEGER},
      </if>
      <if test="saleorderModifyApplyId != null" >
        #{saleorderModifyApplyId,jdbcType=INTEGER},
      </if>
      <if test="saleorderGoodsId != null" >
        #{saleorderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="deliveryDirect != null" >
        #{deliveryDirect,jdbcType=BIT},
      </if>
      <if test="deliveryDirectComments != null" >
        #{deliveryDirectComments,jdbcType=VARCHAR},
      </if>
      <if test="oldDeliveryDirect != null" >
        #{oldDeliveryDirect,jdbcType=BIT},
      </if>
      <if test="oldDeliveryDirectComments != null" >
        #{oldDeliveryDirectComments,jdbcType=VARCHAR},
      </if>
      <if test="goodsComments != null" >
        #{goodsComments,jdbcType=VARCHAR},
      </if>
      <if test="oldGoodsComments != null" >
        #{oldGoodsComments,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.order.model.SaleorderModifyApplyGoods" >
    update T_SALEORDER_MODIFY_APPLY_GOODS
    <set >
      <if test="saleorderModifyApplyId != null" >
        SALEORDER_MODIFY_APPLY_ID = #{saleorderModifyApplyId,jdbcType=INTEGER},
      </if>
      <if test="saleorderGoodsId != null" >
        SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="deliveryDirect != null" >
        DELIVERY_DIRECT = #{deliveryDirect,jdbcType=BIT},
      </if>
      <if test="deliveryDirectComments != null" >
        DELIVERY_DIRECT_COMMENTS = #{deliveryDirectComments,jdbcType=VARCHAR},
      </if>
      <if test="oldDeliveryDirect != null" >
        OLD_DELIVERY_DIRECT = #{oldDeliveryDirect,jdbcType=BIT},
      </if>
      <if test="oldDeliveryDirectComments != null" >
        OLD_DELIVERY_DIRECT_COMMENTS = #{oldDeliveryDirectComments,jdbcType=VARCHAR},
      </if>
      <if test="goodsComments != null" >
        GOODS_COMMENTS = #{goodsComments,jdbcType=VARCHAR},
      </if>
      <if test="oldGoodsComments != null" >
        OLD_GOODS_COMMENTS = #{oldGoodsComments,jdbcType=VARCHAR},
      </if>
    </set>
    where SALEORDER_MODIFY_APPLY_GOODS_ID = #{saleorderModifyApplyGoodsId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.order.model.SaleorderModifyApplyGoods" >
    update T_SALEORDER_MODIFY_APPLY_GOODS
    set SALEORDER_MODIFY_APPLY_ID = #{saleorderModifyApplyId,jdbcType=INTEGER},
      SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER},
      DELIVERY_DIRECT = #{deliveryDirect,jdbcType=BIT},
      DELIVERY_DIRECT_COMMENTS = #{deliveryDirectComments,jdbcType=VARCHAR},
      OLD_DELIVERY_DIRECT = #{oldDeliveryDirect,jdbcType=BIT},
      OLD_DELIVERY_DIRECT_COMMENTS = #{oldDeliveryDirectComments,jdbcType=VARCHAR}
    where SALEORDER_MODIFY_APPLY_GOODS_ID = #{saleorderModifyApplyGoodsId,jdbcType=INTEGER}
  </update>
  
  <!-- 获取订单下的产品列表 -->
	<select id="getSaleorderModifyApplyGoodsById" parameterType="com.vedeng.order.model.SaleorderModifyApply" resultType="com.vedeng.order.model.SaleorderModifyApplyGoods">
			SELECT
				A.* 		
			FROM
				T_SALEORDER_MODIFY_APPLY_GOODS A
			WHERE A.SALEORDER_MODIFY_APPLY_ID = #{saleorderModifyApplyId,jdbcType=INTEGER}
	</select>

  <update id="updateModifyApplyGoodsByIdAndGoodsId" parameterType="com.vedeng.order.model.SaleorderModifyApplyGoods">
    update T_SALEORDER_MODIFY_APPLY_GOODS
    set DELIVERY_DIRECT = #{deliveryDirect,jdbcType=BIT},
        DELIVERY_DIRECT_COMMENTS = #{deliveryDirectComments,jdbcType=VARCHAR},
    where SALEORDER_MODIFY_APPLY_ID = #{saleorderModifyApplyId,jdbcType=INTEGER} and SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER},
  </update>
</mapper>