$(function () {
    $('.J-cate-toggle').click(function () {
        var $parent = $(this).parents('.J-item-wrap:first');
        var isHidden = $parent.hasClass('hidden');

        if (isHidden) {
            $parent.removeClass('hidden');
        } else {
            $parent.addClass('hidden');
        }
    });

    //删除
    $('.J-del').click(function () {
        var id = $(this).data("id");
        var lv = $(this).data('lv');

        var dialog = artDialog.confirm('您确认删除此分类吗？', '', {
            fn: function () {
                $.ajax({
                    url: page_url + '/category/base/deleteCategory.do',
                    data: {
                        baseCategoryId: id,
                        baseCategoryLevel: lv
                    },
                    dataType: 'json',
                    success: function (res) {
                        if (res.code == 0) {
                            window.localStorage.setItem('needShowCateTip', true);
                            window.location.reload();
                        } else {
                            var dia = artDialog.alert(res.message || '操作异常', null, {
                                fn: function () {
                                    dia.close();
                                }, text: '我知道了'
                            }, { type: "warn" });
                        }
                    }
                })
            }, text: '提交'
        }, {
                fn: function () {
                    dialog.close();
                }, text: '取消'
            });
    })

    //操作提示
    GLOBAL.showGlobalTip('删除成功', null, 'needShowCateTip');


    $('.J-choice').click(function () {
        var id = $(this).data("id");
        var index = layer.open({
            type: 2,
            shadeClose: false, //点击遮罩关闭
            //area: 'auto',
            area: ['500px', '505px'],
            title: "商品分类",
            content: "./getFirstCategoryList.do?baseCategoryId="+id,

        });

        }
    )



    $('.J-2').click(function () {
        var index = layer.open({
            type: 1,
            shadeClose: false, //点击遮罩关闭
            //area: 'auto',
            area: ['500px', '500px'],
            title: "sssss",
            content: "ssssss",

        });

        }
    )
});

function choice(searchUrl) {
    $("#popEngineer").attr('layerParams','{"width":"70%","height":"500px","title":"分类迁移确认","link":"'+searchUrl+'"}');
    $("#popEngineer").click();
}

function searchCategory2(searchUrl){
    $("#popEngineer2").attr('layerParams','{"width":"500px","height":"500px","title":"商品分类","link":"'+searchUrl+'"}');
    $("#popEngineer2").click();
}

function quxiao() {
    layer.closeAll();
}