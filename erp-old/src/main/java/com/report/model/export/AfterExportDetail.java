package com.report.model.export;

public class AfterExportDetail {
	
	private Integer companyId;
	
	private Integer afterSalesId;
	
	private Integer type,serviceUserId,orderId,traderId,goodsId,atferSalesStatus,num;
	
	private String afterSalesNo,orderNo,sku,goodsName,brandName,model,barcode,barcodeFactory;
	
	private String addTimeStr,modTimeStr,serviceUserNm,optUserNm,afterTypeStr;
	
	private Long startTime,endTime;

	
	
	public Integer getNum() {
		return num;
	}

	public void setNum(Integer num) {
		this.num = num;
	}

	public String getAfterTypeStr() {
		return afterTypeStr;
	}

	public void setAfterTypeStr(String afterTypeStr) {
		this.afterTypeStr = afterTypeStr;
	}

	public String getOptUserNm() {
		return optUserNm;
	}

	public void setOptUserNm(String optUserNm) {
		this.optUserNm = optUserNm;
	}

	public String getAfterSalesNo() {
		return afterSalesNo;
	}

	public void setAfterSalesNo(String afterSalesNo) {
		this.afterSalesNo = afterSalesNo;
	}

	public String getServiceUserNm() {
		return serviceUserNm;
	}

	public void setServiceUserNm(String serviceUserNm) {
		this.serviceUserNm = serviceUserNm;
	}

	public Integer getAfterSalesId() {
		return afterSalesId;
	}

	public void setAfterSalesId(Integer afterSalesId) {
		this.afterSalesId = afterSalesId;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getServiceUserId() {
		return serviceUserId;
	}

	public void setServiceUserId(Integer serviceUserId) {
		this.serviceUserId = serviceUserId;
	}

	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	public Integer getTraderId() {
		return traderId;
	}

	public void setTraderId(Integer traderId) {
		this.traderId = traderId;
	}

	public Integer getGoodsId() {
		return goodsId;
	}

	public void setGoodsId(Integer goodsId) {
		this.goodsId = goodsId;
	}

	public Integer getAtferSalesStatus() {
		return atferSalesStatus;
	}

	public void setAtferSalesStatus(Integer atferSalesStatus) {
		this.atferSalesStatus = atferSalesStatus;
	}

	public String getModTimeStr() {
		return modTimeStr;
	}

	public void setModTimeStr(String modTimeStr) {
		this.modTimeStr = modTimeStr;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getSku() {
		return sku;
	}

	public void setSku(String sku) {
		this.sku = sku;
	}

	public String getGoodsName() {
		return goodsName;
	}

	public void setGoodsName(String goodsName) {
		this.goodsName = goodsName;
	}

	public String getBrandName() {
		return brandName;
	}

	public void setBrandName(String brandName) {
		this.brandName = brandName;
	}

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public String getBarcode() {
		return barcode;
	}

	public void setBarcode(String barcode) {
		this.barcode = barcode;
	}

	public String getBarcodeFactory() {
		return barcodeFactory;
	}

	public void setBarcodeFactory(String barcodeFactory) {
		this.barcodeFactory = barcodeFactory;
	}

	public String getAddTimeStr() {
		return addTimeStr;
	}

	public void setAddTimeStr(String addTimeStr) {
		this.addTimeStr = addTimeStr;
	}

	public Integer getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Integer companyId) {
		this.companyId = companyId;
	}

	public Long getStartTime() {
		return startTime;
	}

	public void setStartTime(Long startTime) {
		this.startTime = startTime;
	}

	public Long getEndTime() {
		return endTime;
	}

	public void setEndTime(Long endTime) {
		this.endTime = endTime;
	}

}
