package com.vedeng.erp.trader.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 客户终端表
 * @date 2023/8/7 10:17
 */
@Getter
@Setter
public class TraderCustomerTerminalEntity extends BaseEntity {


    /**
     * 主键
     */
    private Integer traderCustomerTerminalId;

    /**
     * 交易者id
     */
    private Integer traderId;

    /**
     * 客户id
     */
    private Integer traderCustomerId;

    /**
     * 终端名称
     */
    private String terminalName;

    /**
     * 大数据终端id（通过大数据查询出对应的终端信息）
     */
    private String dwhTerminalId;

    /**
     * 审核状态
     * 0=审核中
     * 1=审核通过
     * 2=审核不通过
     */
    private Integer auditStatus;

    /**
     * 沟通记录ID
     */
    private Integer communicateRecordId;

    /**
     * 聊天记录截图
     */
    private String picUrl;

    /**
     * 是否删除 （0否1是）
     */
    private Integer isDeleted;
}