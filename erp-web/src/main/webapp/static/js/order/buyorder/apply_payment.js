$(function(){
	var totalAmountArr = new Array();
	var virtureTotalAmountArr = new Array();
	var totalAmountStr = 0;
	var virture_amount = 0;
	var buyorder_amount = 0;
	$("input[name='totalAmount']").each(function(i){
		if($(this).parent().parent().find("input[name='checkedOne']").is(':checked')){
			totalAmountArr[i] = $(this).val();
			totalAmountStr += $(this).val()*100;
			buyorder_amount += $(this).val()*100;
		}
	});
	$("input[name='virture_totalAmount']").each(function(i){
		if($(this).parent().parent().find("input[name='virture_checkedOne']").is(':checked')){
			virtureTotalAmountArr[i] = $(this).val();
			totalAmountStr += $(this).val()*100;
			virture_amount += $(this).val()*100;
		}
	});
	$("#amount").val((totalAmountStr/100).toFixed(2));
	$("#buyorder_amount").val((buyorder_amount / 100).toFixed(2));
	$("#virture_amount").val((virture_amount / 100).toFixed(2));

	$("input[name='checkedAll']").click(function(){
		checkLogin();
		if($(this).is(':checked')){
			$("input[name='checkedOne']").each(function(){
				$(this).prop("checked",true);
			});
		}else{
			$("input[name='checkedOne']").each(function(){
				$(this).prop("checked",false);
			});
		}
		getAmount();
	});

	$("input[name='checkedOne']").click(function(){
		checkLogin();
		var isCheckAll = 1;
		$("input[name='checkedOne']").each(function(){
			if(!$(this).is(':checked')){
				isCheckAll = 0;
			}
		});
		if (isCheckAll == 1) {
			$("input[name='checkedAll']").prop("checked",true);
		} else {
			$("input[name='checkedAll']").prop("checked",false);
		}
		getAmount();
	});

	$("input[name='virture_checkedOne']").click(function(){
		checkLogin();
		var isCheckAll = 1;
		$("input[name='virture_checkedOne']").each(function(){
			if(!$(this).is(':checked')){
				isCheckAll = 0;
			}
		});
		if (isCheckAll == 1) {
			$("input[name='virture_checkedAll']").prop("checked",true);
		} else {
			$("input[name='virture_checkedAll']").prop("checked",false);
		}
		getAmount();
	});

	$("input[name='virture_checkedAll']").click(function(){
		checkLogin();
		if($(this).is(':checked')){
			$("input[name='virture_checkedOne']").each(function(){
				$(this).prop("checked",true);
			});
		}else{
			$("input[name='virture_checkedOne']").each(function(){
				$(this).prop("checked",false);
			});
		}
		getAmount();
	});

})

function changeNum(e, buyorderGoodsId) {
	checkLogin();
	var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
	var price = $("#price_" + buyorderGoodsId).html();
	var allnum = $("#allnum_" + buyorderGoodsId).html();
	var applyPaymentAmount = $("#applyPaymentAmount_"+buyorderGoodsId).html();

	if($(e).val().length>0 && !reg.test($(e).val())){
		var num = 0.00;
	} else {
		var num = ($(e).val()*1).toFixed(2);
	}
	var thisValue = num*price > ((price * allnum) - applyPaymentAmount) ? ((price * allnum) - applyPaymentAmount) : num*price;
	$("#totalAmount_" + buyorderGoodsId).val(thisValue.toFixed(2));
	$("#num_" + buyorderGoodsId).val(isNaN(thisValue/price) ? '0.00' : (thisValue/price).toFixed(2));
	getAmount();
}
//采购费用单编辑数量
function expenseChangeNum(e, buyorderExpenseGoodsId){
	checkLogin();
	var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
	var virture_price = $("#virture_price_" + buyorderExpenseGoodsId).html();
	var virture_allnum = $("#virture_allnum_" + buyorderExpenseGoodsId).html();
	var virture_applyPaymentAmount = $("#virture_applyPaymentAmount_"+buyorderExpenseGoodsId).html();

	if($(e).val().length > 0 && !reg.test($(e).val())){
		var virture_num = 0.00;
	}else {
		var virture_num = ($(e).val()*1).toFixed(2);
	}
	var thisValue = virture_num * virture_price > ((virture_price * virture_allnum)
		- virture_applyPaymentAmount) ? ((virture_price * virture_allnum) - virture_applyPaymentAmount) : virture_num * virture_price;
	$("#virture_totalAmount_" + buyorderExpenseGoodsId).val(thisValue.toFixed(2));
	$("#virture_num_" + buyorderExpenseGoodsId).val(isNaN(thisValue / virture_price) ? '0.00' : (thisValue / virture_price).toFixed(2));
	getAmount();
}

function changePrice(e, buyorderGoodsId) {
	checkLogin();
	var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
	var price = $("#price_" + buyorderGoodsId).html();
	var allnum = $("#allnum_" + buyorderGoodsId).html();
	var applyPaymentAmount = $("#applyPaymentAmount_"+buyorderGoodsId).html();
	if($(e).val().length>0 && !reg.test($(e).val())){
		var amount = 0.00;
	} else {
		var amount = ($(e).val()*1).toFixed(2);
	}
	var thisValue = amount > ((price * allnum) - applyPaymentAmount) ? ((price * allnum) - applyPaymentAmount) : amount;
	$(e).val(thisValue);
	$("#num_" + buyorderGoodsId).val(isNaN(thisValue/price) ? '0.00' : (thisValue/price).toFixed(2));
	getAmount();
}
//采购费用单修改金额
function expenseChangePrice(e, buyorderExpenseGoodsId){
	checkLogin();
	var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
	var virture_price = $("#virture_price_" + buyorderExpenseGoodsId).html();
	var virture_allnum = $("#virture_allnum_" + buyorderExpenseGoodsId).html();
	var virture_applyPaymentAmount = $("#virture_applyPaymentAmount_"+buyorderExpenseGoodsId).html();
	if($(e).val().length>0 && !reg.test($(e).val())){
		var virture_amount = 0.00;
	} else {
		var virture_amount = ($(e).val()*1).toFixed(2);
	}
	var thisValue = virture_amount > ((virture_price * virture_allnum) - virture_applyPaymentAmount) ? ((virture_price * virture_allnum) - virture_applyPaymentAmount) : virture_amount;
	$(e).val(thisValue);
	$("#virture_num_" + buyorderExpenseGoodsId).val(isNaN(thisValue / virture_price) ? '0.00' : (thisValue / virture_price).toFixed(2));
	getAmount();
}

function getAmount() {
	checkLogin();
	var amount = 0;
	var virture_amount = 0;
	var buyorder_amount = 0;
	$("input[name='checkedOne']").each(function(){
		if($(this).is(':checked')){
			amount += $("#totalAmount_" + $(this).val()).val()*100;
			buyorder_amount += $("#totalAmount_" + $(this).val()).val()*100;
		}
	});
	$("input[name = 'virture_checkedOne']").each(function (){
		if($(this).is(':checked')){
			amount += $("#virture_totalAmount_" + $(this).val()).val() * 100;
			virture_amount += $("#virture_totalAmount_" + $(this).val()).val() * 100;
		}
	});
	$("#amount").val((amount/100).toFixed(2));
	$("#buyorder_amount").val((buyorder_amount / 100).toFixed(2));
	$("#virture_amount").val((virture_amount / 100).toFixed(2));
}

function isUseBalanceRadio(e) {
	checkLogin();
	if ($(e).val() == 1) {
		$("#traderMode").val(528);
		$(".notUseBalance").hide();
	} else {
		$("#traderMode").val(521);
		$(".notUseBalance").show();
	}
}


function checkForm() {
	checkLogin();
	$("#traderNameError").html("");
	$("#bankStrError").html("");
	var maxPrePaymentAmount=$("#maxPrePaymentAmount").val();
	var virtureMaxPrePaymentAmount = $("#maxVirturePrePaymentAmount").val();
	var traderMode = $("#traderMode").val();
	var traderName = $("#traderName").val();
	var traderId = $("#traderId").val();
	var supplyAmount = $("#supplyAmount").val();
	var occupyAmount = $("#occupyAmount").val();
	var comments = $("#comments").val();
	var bankRemark = $("#bankRemark").val();
	// 校验 comments 的最大长度
	if (String(comments).length > 100) {
		layer.alert("内部付款备注的最大长度不能超过100个字符");
		return false;
	}
	// 校验 bankRemark 的最大长度
	if (String(bankRemark).length > 100) {
		layer.alert("银行回单备注的最大长度不能超过100个字符");
		return false;
	}
	//校验采购单的首次付款
	if($("#isHavePayed").val()=='false'){
		if(Number(maxPrePaymentAmount) < Number($("#buyorder_amount").val())){
			layer.alert("采购单的首次申请付款的金额不得超过付款计划的预付金额");
			return false;
		}
	}
	//校验采购费用单的首次付款
	if($("#virtureIsHavePayed").val()=='false'){
		if(Number(virtureMaxPrePaymentAmount) < Number($("#virture_amount").val())){
			layer.alert("采购费用单首次申请付款的金额不得超过付款计划的预付金额");
			return false;
		}
	}
	if (traderMode == 521 || traderMode == 10001) {
		if (traderName == '' || traderName==undefined) {
			$("#traderNameError").html("交易名称不能为空");
			return false;
		}

		var bankValue = $("input[name='bank']:checked").val();
		if($("#bank_str_" + bankValue).html() == undefined){
			$("#bankStrError").html("开户银行及银行帐号不能为空");
			return false;
		}
		if($("#bankCode_" + bankValue).val() == undefined || $("#bankCode_" + bankValue).val() == '' || $("#bankCode_" + bankValue).val() == null){
			$("#bankStrError").html("该银行账号无对应联行号数据，请先去供应商基础信息页维护该银行账户联行号数据");
			return false;
		}

		var bankValueArr = $("#bank_str_" + bankValue).html().split(" / ");
		if (bankValueArr[0] == '' || bankValueArr[0]==undefined) {
			$("#bankStrError").html("开户银行不能为空");
			return false;
		}
		if (bankValueArr[1] == '' || bankValueArr[1]==undefined) {
			$("#bankStrError").html("银行帐号不能为空");
			return false;
		}
		$("#bank").val(bankValueArr[0]);
		$("#bankAccount").val(bankValueArr[1]);
		$("#bankCode").val(bankValueArr[2]);
	}

	var flag = true;
	//产品详情ID
	var buyorderGoodsIdArr = new Array();
	var buyorderExpenseGoodsIdArr = new Array();
	$("input[name='buyorderGoodsId']").each(function(i){
		if($(this).parent().find("input[name='checkedOne']").is(':checked')){
			buyorderGoodsIdArr[i] = $(this).val();
		}
	});
	$("input[name='buyorderExpenseGoodsId']").each(function(i){
		if($(this).parent().find("input[name='virture_checkedOne']").is(':checked')){
			buyorderExpenseGoodsIdArr[i] = $(this).val();
		}
	});
	if (buyorderGoodsIdArr.length == 0 && buyorderExpenseGoodsIdArr.length == 0) {
		layer.alert("请先选择产品或费用产品");
		return false;
	}

	//价格
	var priceArr = new Array();
	$("input[name='price']").each(function(i){
		if($(this).parent().find("input[name='checkedOne']").is(':checked')){
			priceArr[i] = $(this).val();
		}
	});
	var virturePriceArr = new Array();
	$("input[name='virture_price']").each(function(i){
		if($(this).parent().find("input[name='virture_checkedOne']").is(':checked')){
			virturePriceArr[i] = $(this).val();
		}
	});
	//申请数量
	var numArr = new Array();
	$("input[name='num']").each(function(i){
		if($(this).parent().parent().find("input[name='checkedOne']").is(':checked')){
			numArr[i] = $(this).val();
		}
	});
	var virtureNumArr = new Array();
	$("input[name='virture_num']").each(function(i){
		if($(this).parent().parent().find("input[name='virture_checkedOne']").is(':checked')){
			virtureNumArr[i] = $(this).val();
		}
	});

	//申请总额
	var totalAmountArr = new Array();
	var virtureTotalAmountArr = new Array();
	var totalAmountStr = 0;
	$("input[name='totalAmount']").each(function(i){
		if($(this).parent().parent().find("input[name='checkedOne']").is(':checked')){
			if ($(this).val() == '' || $(this).val() == '0.00' || $(this).val() == '0') {
				layer.alert("选择产品中申请总额不能为空");
				flag = false;
				return false;
			}
			totalAmountArr[i] = $(this).val();
			totalAmountStr += $(this).val()*10000;
		}
	});
	$("input[name='virture_totalAmount']").each(function(i){
		if($(this).parent().parent().find("input[name='virture_checkedOne']").is(':checked')){
			if ($(this).val() == '' || $(this).val() == '0.00' || $(this).val() == '0') {
				layer.alert("选择产品中申请总额不能为空");
				flag = false;
				return false;
			}
			virtureTotalAmountArr[i] = $(this).val();
			totalAmountStr += $(this).val()*10000;
		}
	});
	if(totalAmountStr * 1 > 0){
		totalAmountStr = totalAmountStr/10000
	}
	if (traderMode == 528 && totalAmountStr > (supplyAmount - occupyAmount)) {
		layer.alert("可申请余额不足");
		return false;
	}

	if(flag==false){
		return false;
	}
	var formToken = $("input[name='formToken']").val();
	$.ajax({
		async : false,
		url : './saveApplyPayment.do',
		data : {
			"amount":$("#amount").val(),
			"comments":$("#comments").val(),
			"bankRemark":$("#bankRemark").val(),
			"traderSubject":$("input[name='traderSubject']:checked").val(),
			"isUseBalance":$("input[name='isUseBalance']:checked").val(),
			"traderMode":$("#traderMode").val(),
			"bank":$("#bank").val(),
			"bankCode":$("#bankCode").val(),
			"bankAccount":$("#bankAccount").val(),
			"traderName":$("#traderName").val(),
			"traderId":$("#traderId").val(),
			"relatedId":$("#relatedId").val(),
			"buyorderExpenseRelatedId":$("#buyorderExpenseRelatedId").val(),
			"traderSupplierId":$("#traderSupplierId").val(),
			"priceArr":JSON.stringify(priceArr),
			"virturePriceArr":JSON.stringify(virturePriceArr),
			"numArr":JSON.stringify(numArr),
			"virtureNumArr":JSON.stringify(virtureNumArr),
			"totalAmountArr":JSON.stringify(totalAmountArr),
			"virtureTotalAmountArr":JSON.stringify(virtureTotalAmountArr),
			"buyorderGoodsIdArr":JSON.stringify(buyorderGoodsIdArr),
			"buyorderExpenseGoodsIdArr":JSON.stringify(buyorderExpenseGoodsIdArr),
			"formToken":formToken,
			"payApplyType":$("#payApplyType").val(),
			"virture_amount":$("#virture_amount").val(),
			"buyorder_amount":$("#buyorder_amount").val()
			},
		type : "POST",
		dataType : "json",
		success : function(data) {
			layer.alert(data.message, {	icon : (data.code == 0 ? 1 : 2)},
				function(index) {
					if(data.code == 0){
						//location.reload();
						// pagesContrlpages(true,false,true);由于采购费用申请付款页面无法兼容原公共方法，关闭当前也页面并刷新前一页，所以此处只能关闭当前页面
						closeNowPage(true);
					}else{
						layer.close(index);
					}
				}
			);
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	})
	return false;
}

