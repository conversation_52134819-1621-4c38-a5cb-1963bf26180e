package com.vedeng.erp.finance.domain.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 红字确认单详情返回
 */
@Data
public class SaleInvoiceRedConfirmDetailResponseDto extends TaxesReturnInfo {

    /**
     * 返回code
     */
    private String code;
    /**
     * 返回message
     */
    private String message;
    /**
     * 红字信息表id
     */
    private String uuid;
    /**
     * 红字信息表确认单编号
     */
    private String hzfpxxqrdbh;
    /**
     * 录入方身份
     */
    private String lrfsf;
    /**
     * 销售方纳税人识别号
     */
    private String xsfnsrsbh;
    /**
     * 销售方名称
     */
    private String xsfmc;
    /**
     * 购买方纳税人识别号
     */
    private String gmfnsrsbh;
    /**
     * 购买方名称
     */
    private String gmfmc;
    /**
     * 对方纳税人识别号
     */
    private String dfnsrsbh;
    /**
     * 购买方纳税人名称
     */
    private String dfnsrmc;
    /**
     * 蓝字发票代码
     */
    private String lzfpdm;
    /**
     * 蓝字发票号码
     */
    private String lzfphm;
    /**
     * 蓝字开票日期
     */
    private String lzkprq;
    /**
     * 蓝字合计金额
     */
    private BigDecimal lzhjje;
    /**
     * 蓝字合计税额
     */
    private BigDecimal lzhjse;
    /**
     * 发票票种
     */
    private String lzfppzDm;
    /**
     * 特殊类型
     */
    private String lzfpTdyslxDm;
    /**
     * 发票类型
     */
    private String lzfplxDm2;
    /**
     * 增值税用途
     */
    private String zzsytDm;
    /**
     * 销售方用途
     */
    private String xfsytDm;
    /**
     * 发票认证状态
     */
    private String fprzztDm;
    /**
     * 红字剩余金额
     */
    private BigDecimal hzcxje;
    /**
     * 红字剩余税额
     */
    private BigDecimal hzcxse;
    /**
     * 红字确认单项目数量
     */
    private Integer hzqrdmxsl;
    /**
     * 冲红原因
     */
    private String chyyDm;
    /**
     * 红字确认单状态
     * 01 无需确认
     *
     * 02 销方录入待购方确认
     *
     * 03 购方录入待销方确认
     *
     * 04 购销双方已确认
     *
     * 05 作废（销方录入购方否认）
     *
     * 06 作废（购方录入销方否认）
     *
     * 07 作废（超 72 小时未确认）
     *
     * 08 作废（发起方已撤销）
     *
     * 09 作废（确认后撤销）
     */
    private String hzqrxxztDm;

    /**
     * 开票方纳税人识别号
     */
    private String kpfnsrsbh;
    /**
     * 确认日期
     */
    private String qrrq;
    /**
     * 价税合计大写
     */
    private String ykjhzfpbz;
    /**
     * 红字发票号码
     */
    private String hzfphm;
    /**
     * 红字开票日期
     */
    private String hzkprq;
    /**
     * 有效标志
     */
    private String yxbz;
    /**
     * 录入人身份id
     */
    private String lrrsfid;
    /**
     * 修改人身份id
     */
    private String xgrsfid;
    /**
     * 录入日期
     */
    private String lrrq;
    /**
     * 修改日期
     */
    private String xgrq;
    /**
     * 数据传输地区
     */
    private String sjcsdq;
    /**
     * 数据归属地区
     */
    private String sjgsdq;
    /**
     * 数据同步时间
     */
    private String sjtbSj;
    /**
     * 业务渠道代码
     */
    private String ywqdDm;
    /**
     * 地区纳税人识别号
     */
    private String dqnsrsbh;
    /**
     * 按钮控制代码列表
     */
    private List<String> mxButtonCtrlDm;
    /**
     *“确认即开票”标识
     */
    private String qrjkpbz;

    /**
     * 确认方名称
     */
    private String qrfmc;
    /**
     * 计税合计
     */
    private BigDecimal jshj;
    /**
     * 是否增值发票标志
     */
    private String sfzzfpbz;
    /**
     * 销售方税务机关代码
     */
    private String xsfssjswjgDm;
    /**
     * 销售方地税机关代码
     */
    private String xsfdsjswjgDm;
    /**
     * 销售方总税务机关代码
     */
    private String xsfzgswjDm;
    /**
     * 购买方税务机关代码
     */
    private String gmfssjswjgDm;
    /**
     * 购买方地税机关代码
     */
    private String gmfdsjswjgDm;
    /**
     * 购买方总税务机关代码
     */
    private String gmfzgswjDm;
    /**
     * 冲红原因代码
     */
    private String cezslxDm;
    /**
     * 发票来源
     */
    private String fplyDm;

    /**
     * 销售方
     */
    private String gxsf;

    private String fpkjfsDm;
    /**
     * 明细列表
     */
    private List<Hzqrxxmx> hzqrxxmxList;

    /**
     * 描述红字信息明细的内部类
     */
    @Data
    public static class Hzqrxxmx {

        /**
         * 单位
         */
        private String dw;
        /**
         * 单价
         */
        private String fpspdj;
        /**
         * 数量
         */
        private String fpspsl;
        /**
         * 规格型号
         */
        private String ggxh;
        /**
         * 购买方地税机关代码
         */
        private String gmfdsjswjgDm;
        /**
         * 购买方总税务机关代码
         */
        private String gmfzgswjDm;
        /**
         * 购买方税务机关代码
         */
        private String gmfssjswjgDm;
        /**
         * 劳务或应税劳务、服务名称
         */
        private String hwhyslwfwmc;
        /**
         * 红字确认单明细uuid
         */
        private String hzqrxxuuid;
        /**
         * 金额
         */
        private String je;
        /**
         * 蓝字发票号码
         */
        private String lzfphm;
        /**
         * 蓝字明细序号
         */
        private Integer lzmxxh;
        /**
         * 税额
         */
        private String se;

        /**
         * 税率
         */
        private String sl1;

        /**
         * 税收分类编码简称
         */
        private String spfwjc;
        /**
         * 税收分类编码
         */
        private String sphfwssflhbbm;

        /**
         * uuid
         */
        private String uuid;

        /**
         * 序号 （和申请时不一样，返回 1 2 。。。 非自定义值）
         */
        private String xh;
        /**
         * 项目名称
         */
        private String xmmc;
        /**
         * 销售方地税机关代码
         */
        private String xsfdsjswjgDm;
        /**
         * 销售方总税务机关代码
         */
        private String xsfzgswjDm;
        /**
         * 销售方税务机关代码
         */
        private String xsfssjswjgDm;
    }
}
