package com.vedeng.erp.saleorder.enums;

/**
 * <AUTHOR>
 * @ClassName TraceTypeEnum.java
 * @Description TODO
 * @createTime 2022年09月05日 17:50:00
 */
public enum TraceTypeEnum {
    SALE("sale"),
    BUY("buy");

    private String type;

    TraceTypeEnum() {
    }


    public static TraceTypeEnum valueOfType(String type) {
        for (TraceTypeEnum obj : TraceTypeEnum.values()) {
            if (java.util.Objects.equals(obj.type, type)) {
                return obj;
            }
        }
        return null;
    }

    public String getType() {
        return type;
    }

    TraceTypeEnum(String type) {
        this.type = type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
