package com.vedeng.erp.kingdee.batch.writer;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveCommonDto;
import com.vedeng.erp.kingdee.service.KingDeeReceiveCommonApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 销售单标准应收单
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/11 9:47
 */
@Service
@Slf4j
public class BatchSaleOrderReceiveWriter extends BaseWriter<KingDeeReceiveCommonDto> {
    @Autowired
    private KingDeeReceiveCommonApiService kingDeeReceiveCommonApiService;

    @Override
    public void doWrite(KingDeeReceiveCommonDto item, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("销售实物商品标准应收单推送金蝶：{}", JSON.toJSONString(item));
        item.setKingDeeBizEnums(KingDeeBizEnums.saveReceiveCommon);
        kingDeeReceiveCommonApiService.register(item,true);
    }
}
