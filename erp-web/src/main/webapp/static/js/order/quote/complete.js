function complementTask(){
	checkLogin();
	var comment = $("input[name='comment']").val()
	var taskId = $("input[name='taskId']").val()
	var pass = $("input[name='pass']").val()
	var quoteorderId = $("input[name='quoteorderId']").val()
	var type = $("input[name='type']").val();
	if(pass =="false" && comment == ""){
		warnTips("comment","请填写备注");
		return false;
	}
	if(comment.length > 1024){
		warnTips("comment","备注内容不允许超过256个字符");
		return false;
	}
		if(type==1){
			$.ajax({
				type: "POST",
				url: "./complementTaskParallel.do",
				data: $('#complement').serialize(),
				dataType:'json',
				success: function(data){
					if (data.code == 0) {
						if(data.status == 1){
							layer.close(index);
							window.parent.location.href = page_url + '/order/quote/getQuoteDetail.do?viewType=3&quoteorderId='+data.data.quoteorderId
						}else{
							window.parent.location.reload();
						}
						
					} else {
						layer.alert(data.message);
					}
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
		}else{
			$.ajax({
				type: "POST",
				url: "./complementTask.do",
				data: $('#complement').serialize(),
				dataType:'json',
				success: function(data){
					if (data.code == 0) {
						if(data.status == 1){
							layer.close(index);
							window.parent.location.href = page_url + '/order/quote/getQuoteDetail.do?viewType=3&quoteorderId='+data.data.quoteorderId
						}else{
							window.parent.location.reload();
						}
						
					} else {
						layer.alert(data.message);
					}
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
		}
		

}

function closeQuoteVerify(){
	checkLogin();
	var quoteorderId = $("input[name='quoteorderId']").val()
	var reason= $("select[name='reason']").find("option:selected").val();
	var closeReasonId= $("select[name='closeReasonId']").find("option:selected").val();
	if(reason=="" || closeReasonId=="" || closeReasonId==undefined){
		alert("请选择关闭原因");
		return false;
	}
	if(closeReasonId == 1607 || closeReasonId==1612 ||closeReasonId==1617){
		var closeReasonComment= $("#closeReasonComment_1").val();
		if(closeReasonComment=='' || closeReasonComment==undefined){
			alert("请填写关闭原因");
			return false;
		}
	}
	$.ajax({
		type: "POST",
		url: "./closeQuoteVerify.do",
		data: $('#complement').serialize(),
		dataType:'json',
		success: function(data){
			if (data.code == 0) {
				if(data.status == 1){
					layer.close(index);
					window.parent.location.href = page_url + '/order/quote/getQuoteDetail.do?viewType=2&quoteorderId='+data.data.quoteorderId
				}else{
					window.parent.location.reload();
				}
				
			} else {
				layer.alert(data.message);
			}
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});

}
$(function () {
	$('.J-select').hide();
	$('#closeReasonComment').hide();
	$('#firstreason').change(function () {
		$('.J-select').empty();
		var parentId = $("#firstreason").val();
		if(parentId == '') {
			$('.J-select').hide();
			$('#closeReasonComment').hide();
			return false;
		}
			$.ajax({
				type:"GET",
				url:"./getColseSecondReason.do?parentId="+parentId,
				dataType:'json',
				success:function (data) {
					if(data.code==0){
						$('.J-select').show();
						$('#closeReasonComment').show();
						$('.J-select').append(`<option  value=''>请选择关闭原因</option>`)
						data.data.forEach(function (item) {
							$('.J-select').append(`<option name="closeReasonId" value='${item.sysOptionDefinitionId}'>${item.title}</option>`);
						})
					}
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			})
	})


})