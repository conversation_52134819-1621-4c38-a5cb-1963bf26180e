package com.vedeng.erp.system.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.system.domain.entity.CustomTagEntity;
import com.vedeng.erp.system.domain.entity.TagEntity;
import com.vedeng.erp.system.dto.CustomTagDto;
import com.vedeng.erp.system.dto.TagDto;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @description: dto entity转换类
 * @date 2022/7/21
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface TagConvertor extends BaseMapStruct<TagEntity, TagDto> {
}
