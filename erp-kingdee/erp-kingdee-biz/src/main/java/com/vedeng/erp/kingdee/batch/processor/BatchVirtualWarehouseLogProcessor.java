package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.utils.numgenerator.BillNumGenerator;
import com.vedeng.common.core.utils.numgenerator.bean.BillGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.enums.BillType;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.BatchAfterSalesDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceDetailDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchSaleorderDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInItemDtoMapper;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 红票生成虚拟出入库记录process
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class BatchVirtualWarehouseLogProcessor implements ItemProcessor<BatchInvoiceDto, BatchVirtualWarehouseLogDto> {

    @Autowired
    private BatchAfterSalesDtoMapper batchAfterSalesDtoMapper;

    @Autowired
    private BatchSaleorderDtoMapper batchSaleorderDtoMapper;

    @Autowired
    private BatchInvoiceDetailDtoMapper batchInvoiceDetailDtoMapper;

    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;

    @Override
    public BatchVirtualWarehouseLogDto process(BatchInvoiceDto batchInvoiceDto) throws Exception {
        log.info("红票生成虚拟出入库记录处理开始：金蝶处理红字发票{},", JSON.toJSONString(batchInvoiceDto));
        //红字发票的售后id
        Integer afterSalesId = batchInvoiceDto.getAfterSalesId();
        BatchAfterSalesDto batchAfterSalesDto = batchAfterSalesDtoMapper.findByAfterSalesId(afterSalesId);
        log.info("红字发票关联的售后单信息{},", JSON.toJSONString(batchAfterSalesDto));
        if (batchAfterSalesDto == null) {
            log.info("未查询到售后单信息，售后单id{},", batchInvoiceDto.getAfterSalesId());
            return null;
        }

        BatchSaleorderDto batchSaleorderDto = batchSaleorderDtoMapper.selectByPrimaryKey(batchAfterSalesDto.getOrderId());
        log.info("红字发票关联的销售单信息{},", JSON.toJSONString(batchSaleorderDto));
        if (batchSaleorderDto == null) {
            log.info("未查询到销售单信息，售后单id{},", batchInvoiceDto.getAfterSalesId());
            return null;
        }

        BatchAfterSalesDto saleByAfterSalesNoAndSubjectType = batchAfterSalesDtoMapper.findSaleByAfterSalesNoAndSubjectType(batchAfterSalesDto.getAfterSalesNo(), 535);
        List<BatchAfterSalesGoodsDto> batchAfterSalesGoodsDtoList = saleByAfterSalesNoAndSubjectType.getBatchAfterSalesGoodsDtoList();
        if (CollUtil.isEmpty(batchAfterSalesGoodsDtoList)) {
            log.info("未查询到售后明细，售后单id{},", (batchAfterSalesDto.getAfterSalesNo()));
            return null;
        }


        // 根据售后单获取所有红票
        List<Integer> invoiceIds = batchInvoiceDetailDtoMapper.findByAfterSalesIdGetInvoiceId(batchInvoiceDto.getAfterSalesId());
        List<BatchInvoiceDetailDto> list = batchInvoiceDetailDtoMapper.querySaleInvoiceByInvoiceIds(invoiceIds);
        if (CollUtil.isEmpty(list)) {
            log.info("未查询到发票明细，售后单id{},", batchInvoiceDto.getAfterSalesId());
            return null;
        }

        // 票总数map
        Map<Integer, BigDecimal> invoiceSumMap = list.stream()
                .collect(Collectors.groupingBy(BatchInvoiceDetailDto::getGoodsId, Collectors.mapping(BatchInvoiceDetailDto::getNum,
                        Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        List<BatchInvoiceDetailDto> distinctList = list.stream()
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(BatchInvoiceDetailDto::getGoodsId))), ArrayList::new));


        BatchVirtualWarehouseLogDto batchVirtualWarehouseLogDto = new BatchVirtualWarehouseLogDto();
        List<GoodsItem> goodsItems = new ArrayList<>();

        // 仅退票
        if (KingDeeConstant.ID_542.equals(batchAfterSalesDto.getType())) {
            // 仅退票使用销售订单详细id 获取虚拟入库单，当已生成虚拟出入库，则不在生成
            distinctList.forEach(batchInvoiceDetailDto -> {
                BigDecimal invoiceSum = invoiceSumMap.get(batchInvoiceDetailDto.getGoodsId());
                // 虚拟出库单明细数据库存数量
                List<BatchWarehouseGoodsOutInItemDto> virtualBatchWarehouseGoodsOutInItemDtoList = batchWarehouseGoodsOutInItemDtoMapper
                        .findByOperateTypeAndRelatedId(KingDeeConstant.FIVE, batchInvoiceDetailDto.getDetailgoodsId(), 1,
                                batchAfterSalesDto.getAfterSalesNo());
                // 总共出库数量
                BigDecimal realSum = new BigDecimal("0");
                if (CollUtil.isNotEmpty(virtualBatchWarehouseGoodsOutInItemDtoList)) {
                    realSum = virtualBatchWarehouseGoodsOutInItemDtoList.stream().map(w -> w.getNum().abs()).reduce(BigDecimal.ZERO, BigDecimal::add);
                }
                if (invoiceSum.compareTo(realSum) > 0) {
                    GoodsItem goodsItem = GoodsItem.builder()
                            .relatedId(batchInvoiceDetailDto.getDetailgoodsId())
                            .num(invoiceSum)
                            .goodsId(batchInvoiceDetailDto.getGoodsId())
                            .sku(batchInvoiceDetailDto.getSku())
                            .invoiceDetailId(batchInvoiceDetailDto.getInvoiceDetailId())
                            .build();
                    goodsItems.add(goodsItem);
                }
            });
            if (CollUtil.isEmpty(goodsItems)) {
                log.info("红票生成虚拟出入库记录处理无需处理发票 invoiceId:{}", invoiceIds);
                return null;
            }
            this.createVirtualWareHouse(batchAfterSalesDto, batchSaleorderDto, batchVirtualWarehouseLogDto, goodsItems, batchInvoiceDto);
        }

        //退货退票售后  判断实际入库的部分 红票录入数量减去 - 售后入库数量
        if (KingDeeConstant.ID_539.equals(batchAfterSalesDto.getType())) {
            // 真实售后单中的库存数量
            List<Integer> afterSalesGoodsIds =
                    batchAfterSalesGoodsDtoList.stream().filter(b -> b.getGoodsType().equals(0))
                            .map(BatchAfterSalesGoodsDto::getAfterSalesGoodsId).collect(Collectors.toList());
            // 过滤赠品
            afterSalesGoodsIds = batchAfterSalesDtoMapper.getNonGiftAfterOrders(afterSalesGoodsIds);
            List<BatchWarehouseGoodsOutInItemDto> batchWarehouseGoodsOutInItemDtoList = new ArrayList<>();
            afterSalesGoodsIds.forEach(id -> {
                List<BatchWarehouseGoodsOutInItemDto> afterSaleWarehouseGoodsOutInItemDtoList = batchWarehouseGoodsOutInItemDtoMapper
                        .findByOperateTypeAndRelatedId(KingDeeConstant.FIVE, id, 0, batchAfterSalesDto.getAfterSalesNo());
                batchWarehouseGoodsOutInItemDtoList.addAll(afterSaleWarehouseGoodsOutInItemDtoList);
            });

            distinctList.forEach(batchInvoiceDetailDto -> {
                // 票总数
                BigDecimal invoiceSum = invoiceSumMap.get(batchInvoiceDetailDto.getGoodsId());

                // 实际出库单明细数据库存数量
                List<BatchWarehouseGoodsOutInItemDto> actualBatchWarehouseGoodsOutInItemDtoList =
                        batchWarehouseGoodsOutInItemDtoList.stream().filter(b -> b.getGoodsId().equals(batchInvoiceDetailDto.getGoodsId())).collect(Collectors.toList());
                // 虚拟出库单明细数据库存数量
                List<BatchWarehouseGoodsOutInItemDto> virtualBatchWarehouseGoodsOutInItemDtoList = batchWarehouseGoodsOutInItemDtoMapper
                        .findByOperateTypeAndRelatedId(KingDeeConstant.FIVE, batchInvoiceDetailDto.getDetailgoodsId(), 1,
                                batchAfterSalesDto.getAfterSalesNo());

                // 总共出库数量
                BigDecimal realSum = new BigDecimal("0");
                if (CollUtil.isNotEmpty(actualBatchWarehouseGoodsOutInItemDtoList)) {
                    BigDecimal number = actualBatchWarehouseGoodsOutInItemDtoList.stream().map(w -> w.getNum().abs()).reduce(BigDecimal.ZERO, BigDecimal::add);
                    realSum = realSum.add(number);
                }
                if (CollUtil.isNotEmpty(virtualBatchWarehouseGoodsOutInItemDtoList)) {
                    BigDecimal number = virtualBatchWarehouseGoodsOutInItemDtoList.stream().map(w -> w.getNum().abs()).reduce(BigDecimal.ZERO, BigDecimal::add);
                    realSum = realSum.add(number);
                }

                if (CollUtil.isNotEmpty(actualBatchWarehouseGoodsOutInItemDtoList)) {
                    if (invoiceSum.compareTo(realSum) > 0) {
                        GoodsItem goodsItem = GoodsItem.builder()
                                .relatedId(batchInvoiceDetailDto.getDetailgoodsId())
                                .num(invoiceSum.subtract(realSum))
                                .goodsId(batchInvoiceDetailDto.getGoodsId())
                                .sku(batchInvoiceDetailDto.getSku())
                                .invoiceDetailId(batchInvoiceDetailDto.getInvoiceDetailId())
                                .build();
                        goodsItems.add(goodsItem);
                    }
                } else {
                    //未匹配到实际出入库记录的，直接生成虚拟出入库信息
                    GoodsItem goodsItem = GoodsItem.builder()
                            .relatedId(batchInvoiceDetailDto.getDetailgoodsId())
                            .num(batchInvoiceDetailDto.getNum())
                            .goodsId(batchInvoiceDetailDto.getGoodsId())
                            .sku(batchInvoiceDetailDto.getSku())
                            .invoiceDetailId(batchInvoiceDetailDto.getInvoiceDetailId())
                            .build();
                    goodsItems.add(goodsItem);
                }
            });

            if (CollUtil.isEmpty(goodsItems)) {
                log.info("无需生成虚拟出入库记录 invoiceId:{}", invoiceIds);
                return null;
            }

            this.createVirtualWareHouse(batchAfterSalesDto, batchSaleorderDto, batchVirtualWarehouseLogDto, goodsItems, batchInvoiceDto);
        }

        return batchVirtualWarehouseLogDto;
    }

    /**
     * 组装虚拟出入库信息
     *
     * @param batchAfterSalesDto
     * @param batchSaleorderDto
     * @param batchVirtualWarehouseLogDto
     * @param goodsItems
     */
    private void createVirtualWareHouse(BatchAfterSalesDto batchAfterSalesDto, BatchSaleorderDto batchSaleorderDto,
                                        BatchVirtualWarehouseLogDto batchVirtualWarehouseLogDto, List<GoodsItem> goodsItems,
                                        BatchInvoiceDto batchInvoiceDto) {
        if (CollUtil.isNotEmpty(goodsItems)) {
            String inNo = this.createWarehouseOutIn(batchVirtualWarehouseLogDto, batchAfterSalesDto, batchSaleorderDto, KingDeeConstant.FIVE, batchInvoiceDto);
            List<BatchWarehouseGoodsOutInItemDto> batchWarehouseGoodsInItemDtoList = this.createWarehouseOutInItem(goodsItems, inNo, KingDeeConstant.FIVE);
            batchVirtualWarehouseLogDto.setBatchWarehouseGoodsInItemDtoList(batchWarehouseGoodsInItemDtoList);

            String outNo = this.createWarehouseOutIn(batchVirtualWarehouseLogDto, batchAfterSalesDto, batchSaleorderDto, KingDeeConstant.TWO, batchInvoiceDto);
            List<BatchWarehouseGoodsOutInItemDto> batchWarehouseGoodsOutItemDtoList = this.createWarehouseOutInItem(goodsItems, outNo, KingDeeConstant.TWO);
            batchVirtualWarehouseLogDto.setBatchWarehouseGoodsOutItemDtoList(batchWarehouseGoodsOutItemDtoList);
        }
    }


    /**
     * 生成虚拟出入库单主表
     *
     * @param batchVirtualWarehouseLogDto batchVirtualWarehouseLogDto
     * @param batchAfterSalesDto          batchAfterSalesDto
     * @param batchSaleorderDto           batchSaleorderDto
     * @param outInType                   outInType
     * @return String
     */
    private String createWarehouseOutIn(BatchVirtualWarehouseLogDto batchVirtualWarehouseLogDto,
                                        BatchAfterSalesDto batchAfterSalesDto,
                                        BatchSaleorderDto batchSaleorderDto,
                                        Integer outInType, BatchInvoiceDto batchInvoiceDto) {
        //虚拟入库记录主表
        BatchWarehouseGoodsOutInDto batchWarehouseGoodsDto = new BatchWarehouseGoodsOutInDto();


        batchWarehouseGoodsDto.setOutInType(outInType);
        batchWarehouseGoodsDto.setOutInCompany(batchSaleorderDto.getTraderName());
        batchWarehouseGoodsDto.setSource(KingDeeConstant.SOURCE_ERP);
        batchWarehouseGoodsDto.setIsDelete(KingDeeConstant.ZERO);
        batchWarehouseGoodsDto.setIsVirtual(KingDeeConstant.ONE);
        batchWarehouseGoodsDto.setCreator(KingDeeConstant.TWO);
        batchWarehouseGoodsDto.setCreatorName("njadmin");
        batchWarehouseGoodsDto.setAddTime(new Date());
        batchWarehouseGoodsDto.setModTime(new Date());
        batchWarehouseGoodsDto.setOutInTime(DateUtil.date(batchInvoiceDto.getAddTime()));
        log.info("生成虚拟出入库单主表信息{},", JSON.toJSONString(batchWarehouseGoodsDto));
        BillGeneratorBean billGeneratorBean;
        if (outInType.equals(KingDeeConstant.TWO)) {
            billGeneratorBean = new BillGeneratorBean(BillType.WAREHOUSE_GOODS_OUT_IN_XNCK);
            batchWarehouseGoodsDto.setRelateNo(batchSaleorderDto.getSaleorderNo());
            batchVirtualWarehouseLogDto.setBatchWarehouseGoodsOutDto(batchWarehouseGoodsDto);
        } else {
            billGeneratorBean = new BillGeneratorBean(BillType.WAREHOUSE_GOODS_OUT_IN_XNRK);
            batchWarehouseGoodsDto.setRelateNo(batchAfterSalesDto.getAfterSalesNo());
            batchVirtualWarehouseLogDto.setBatchWarehouseGoodsInDto(batchWarehouseGoodsDto);
        }
        String outInNo = new BillNumGenerator().distribution(billGeneratorBean);
        batchWarehouseGoodsDto.setOutInNo(outInNo);
        return outInNo;
    }


    /**
     * 生成虚拟出入库单子表
     *
     * @param goodsItems goodsItems
     * @param outInNo    outInNo
     * @return List<BatchWarehouseGoodsOutInItemDto>
     */
    private List<BatchWarehouseGoodsOutInItemDto> createWarehouseOutInItem(List<GoodsItem> goodsItems, String outInNo, Integer outInType) {

        List<BatchWarehouseGoodsOutInItemDto> batchWarehouseGoodsItemDtoList = new ArrayList<>();

        for (GoodsItem item : goodsItems) {
            BatchWarehouseGoodsOutInItemDto batchWarehouseGoodsItemDto = new BatchWarehouseGoodsOutInItemDto();
            batchWarehouseGoodsItemDto.setOutInNo(outInNo);
            batchWarehouseGoodsItemDto.setCompanyId(KingDeeConstant.ONE);
            batchWarehouseGoodsItemDto.setOperateType(outInType);
            batchWarehouseGoodsItemDto.setRelatedId(item.getRelatedId());
            batchWarehouseGoodsItemDto.setGoodsId(item.getGoodsId());
            batchWarehouseGoodsItemDto.setNum(item.getNum());
            batchWarehouseGoodsItemDto.setInvoiceDetailId(item.getInvoiceDetailId());
            batchWarehouseGoodsItemDto.setSkuNo(item.getSku());
            batchWarehouseGoodsItemDto.setCheckStatus(KingDeeConstant.ONE);
            batchWarehouseGoodsItemDto.setIsDelete(KingDeeConstant.ZERO);
            batchWarehouseGoodsItemDto.setCreator(KingDeeConstant.TWO);
            batchWarehouseGoodsItemDto.setCreatorName("njadmin");
            batchWarehouseGoodsItemDto.setAddTime(new Date());
            batchWarehouseGoodsItemDto.setModTime(new Date());
            if (outInType.equals(KingDeeConstant.TWO)) {
                batchWarehouseGoodsItemDto.setLogType(KingDeeConstant.ONE);
                batchWarehouseGoodsItemDtoList.add(batchWarehouseGoodsItemDto);
            } else {
                batchWarehouseGoodsItemDto.setLogType(KingDeeConstant.ZERO);
                batchWarehouseGoodsItemDtoList.add(batchWarehouseGoodsItemDto);
            }
        }
        log.info("生成虚拟入库商品信息{},", JSON.toJSONString(batchWarehouseGoodsItemDtoList));
        return batchWarehouseGoodsItemDtoList;
    }


    @Getter
    @Setter
    @Builder
    public static class GoodsItem {
        private Integer relatedId;
        private Integer goodsId;
        private BigDecimal num;
        private Integer invoiceDetailId;
        private String sku;
    }

}
