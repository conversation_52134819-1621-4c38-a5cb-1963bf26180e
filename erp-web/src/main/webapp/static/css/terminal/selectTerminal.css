.form-wrap {
  margin: auto;
  padding: 20px;
  font-size: 14px;
  line-height: 1.5;
}
.form-wrap .form-item {
  display: flex;
  margin-bottom: 20px;
}
.form-wrap .form-item:last-child {
  margin-bottom: 0;
}
.form-wrap .form-item .form-label {
  width: 140px;
  text-align: right;
  color: #999;
  line-height: 33px;
  margin-right: 10px;
}
.form-wrap .form-item .form-label .must {
  color: #e64545;
}
.form-wrap .form-item .form-field {
  flex: 1;
}
.form-wrap .form-item .form-error {
  margin-top: 5px;
  color: #e64545;
  display: none;
}
.form-wrap .form-item .form-error .icon-error2 {
  margin-right: 5px;
  line-height: 1;
  vertical-align: -3px;
}
.form-wrap .form-item .form-btn-wrap {
  padding-left: 150px;
  display: flex;
  align-items: center;
}
.form-wrap .form-item .form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}
.form-wrap .form-radio-wrap {
  margin-bottom: -10px;
  display: flex;
  flex-wrap: wrap;
  padding-top: 5px;
}
.form-wrap .form-radio-wrap .radio-label-item {
  display: flex;
  align-items: center;
  margin-right: 20px;
  margin-bottom: 10px;
  cursor: pointer;
}
.form-wrap .form-radio-wrap .radio-label-item input {
  width: 14px;
  height: 14px;
  margin: 1px 5px 0 0;
}
.form-wrap .col-1 {
  width: calc(((100% - 110px) / 12 + 10px) * 1 - 10px);
}
.form-wrap .col-1 .input-text {
  width: 100%;
}
.form-wrap .col-2 {
  width: calc(((100% - 110px) / 12 + 10px) * 2 - 10px);
}
.form-wrap .col-2 .input-text {
  width: 100%;
}
.form-wrap .col-3 {
  width: calc(((100% - 110px) / 12 + 10px) * 3 - 10px);
}
.form-wrap .col-3 .input-text {
  width: 100%;
}
.form-wrap .col-4 {
  width: calc(((100% - 110px) / 12 + 10px) * 4 - 10px);
}
.form-wrap .col-4 .input-text {
  width: 100%;
}
.form-wrap .col-5 {
  width: calc(((100% - 110px) / 12 + 10px) * 5 - 10px);
}
.form-wrap .col-5 .input-text {
  width: 100%;
}
.form-wrap .col-6 {
  width: calc(((100% - 110px) / 12 + 10px) * 6 - 10px);
}
.form-wrap .col-6 .input-text {
  width: 100%;
}
.form-wrap .col-7 {
  width: calc(((100% - 110px) / 12 + 10px) * 7 - 10px);
}
.form-wrap .col-7 .input-text {
  width: 100%;
}
.form-wrap .col-8 {
  width: calc(((100% - 110px) / 12 + 10px) * 8 - 10px);
}
.form-wrap .col-8 .input-text {
  width: 100%;
}
.form-wrap .col-9 {
  width: calc(((100% - 110px) / 12 + 10px) * 9 - 10px);
}
.form-wrap .col-9 .input-text {
  width: 100%;
}
.form-wrap .col-10 {
  width: calc(((100% - 110px) / 12 + 10px) * 10 - 10px);
}
.form-wrap .col-10 .input-text {
  width: 100%;
}
.form-wrap .col-11 {
  width: calc(((100% - 110px) / 12 + 10px) * 11 - 10px);
}
.form-wrap .col-11 .input-text {
  width: 100%;
}
.form-wrap .col-12 {
  width: calc(((100% - 110px) / 12 + 10px) * 12 - 10px);
}
.form-wrap .col-12 .input-text {
  width: 100%;
}
.new-ui .input-text {
  border: solid 1px #BABFC2;
  border-radius: 3px;
  padding: 0 10px;
  line-height: 33px;
  height: 33px;
}
.new-ui .input-text:hover {
  border-color: #969B9E;
}
.new-ui .input-text::placeholder {
  color: #999;
}
.new-ui .input-text:focus {
  border-color: #0099FF;
}
.new-ui .btn {
  background: #F5F7FA;
  border: 1px solid #BABFC2;
  border-radius: 3px;
  padding: 5px 15px;
  cursor: pointer;
  margin-right: 10px;
  display: inline-block;
}
.new-ui .btn:hover {
  background: #EBEFF2;
}
.new-ui .btn:active {
  background: #E1E5E8;
}
.new-ui .btn.btn-primary {
  background: #0099FF;
  color: #fff;
  border-color: #0099FF;
}
.new-ui .btn.btn-primary:hover {
  background: #0087E0;
  border-color: #0087E0;
}
.new-ui .btn.btn-primary:active {
  background: #006CB3;
  border-color: #006CB3;
}
.tyc-dialog-wrap {
  font-size: 14px;
  line-height: 1.5;
  padding: 20px;
}
.tyc-dialog-wrap .tyc-search-wrap {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.tyc-dialog-wrap .tyc-search-wrap .input-text {
  margin-right: 10px;
  width: 360px;
}
.tyc-dialog-wrap .tyc-search-wrap .btn {
  margin-right: 20px;
}
.tyc-dialog-wrap .tyc-search-wrap .search-tip {
  color: #999;
  font-size: 12px;
}
.tyc-dialog-wrap .tyc-list-empty {
  padding-top: 130px;
  padding-bottom: 130px;
  text-align: center;
  display: none;
}
.tyc-dialog-wrap .tyc-list-empty .empty-img {
  width: 110px;
  height: 95px;
  background-image: url(../../images/order/search-empty.svg);
  background-size: 100% 100%;
  margin: 0 auto 40px auto;
}
.tyc-dialog-wrap .list-table-tr {
  display: flex;
  align-items: center;
  margin-top: -1px;
}
.tyc-dialog-wrap .list-table-tr .th-item,
.tyc-dialog-wrap .list-table-tr .td-item {
  border: 1px solid #E1E5E8;
  border-left: 0;
}
.tyc-dialog-wrap .list-table-tr .th-item:nth-child(1),
.tyc-dialog-wrap .list-table-tr .td-item:nth-child(1) {
  width: 500px;
  border-left: 1px solid #E1E5E8;
}
.tyc-dialog-wrap .list-table-tr .th-item:nth-child(2),
.tyc-dialog-wrap .list-table-tr .td-item:nth-child(2) {
  width: 160px;
}
.tyc-dialog-wrap .list-table-tr .th-item:nth-child(3),
.tyc-dialog-wrap .list-table-tr .td-item:nth-child(3) {
  width: 160px;
}
.tyc-dialog-wrap .list-table-tr .th-item:nth-child(4),
.tyc-dialog-wrap .list-table-tr .td-item:nth-child(4) {
  width: 100px;
}
.tyc-dialog-wrap .list-table-tr .th-item {
  background-color: #FAFBFC;
  padding: 7px 20px;
  color: #999;
}
.tyc-dialog-wrap .list-table-tr .td-item {
  border-left: 0;
  padding: 13px 20px;
}
.tyc-dialog-wrap .list-table-tr .text-line-1 {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.tyc-dialog-wrap .list-table-tr .td-option {
  display: inline-block;
  cursor: pointer;
  color: #09f;
}
.tyc-dialog-wrap .list-table-tr .td-option:hover {
  color: #f60;
}
.tyc-dialog-wrap .list-table-body {
  max-height: 478px;
  overflow: auto;
  overscroll-behavior: contain;
}
.tyc-dialog-wrap .list-table-body::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.tyc-dialog-wrap .list-table-body::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.tyc-dialog-wrap .list-table-body::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.tyc-dialog-wrap .list-table-body::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.tyc-dialog-wrap .list-table-body::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.tyc-dialog-wrap .list-table-body.on-scroll {
  margin-right: -6px;
}
.table.new-table tr td,
.table.new-table tr th {
  padding-left: 10px;
  padding-right: 10px;
  text-align: left;
}
.table.new-table tr td.td-empty,
.table.new-table tr th.td-empty {
  padding: 0;
  background: #fff;
}
.table.new-table tr td.td-empty:hover,
.table.new-table tr th.td-empty:hover {
  background: #fff;
}
.table.new-table .option-wrap {
  display: flex;
  align-items: center;
}
.table.new-table .option-wrap .option-item {
  margin-right: 21px;
  color: #3384EF;
  cursor: pointer;
  position: relative;
}
.table.new-table .option-wrap .option-item::before {
  content: "";
  width: 1px;
  height: 12px;
  background: #e1e5e8;
  position: absolute;
  right: -10px;
  top: 2px;
}
.table.new-table .option-wrap .option-item:last-child {
  margin-right: 0;
}
.table.new-table .option-wrap .option-item:last-child::before {
  display: none;
}
.table.new-table .option-wrap .option-item:hover {
  color: #f60;
}
.table.new-table .list-empty {
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.table.new-table .list-empty .icon-info1 {
  margin-bottom: -1px;
  margin-right: 5px;
  color: #3384EF;
}
.table.new-table .list-empty .empty-txt {
  display: flex;
  align-items: center;
}
.table.new-table .list-empty .empty-txt.error {
  color: #E64545;
}
.table.new-table .list-empty .empty-txt.error .icon-info1 {
  color: #e64545;
}
.table.new-table .list-empty .empty-option {
  color: #3384EF;
  cursor: pointer;
}
.table.new-table .list-empty .empty-option:hover {
  color: #f60;
}
