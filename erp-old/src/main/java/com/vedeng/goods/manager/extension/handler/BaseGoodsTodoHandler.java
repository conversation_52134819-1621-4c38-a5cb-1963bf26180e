package com.vedeng.goods.manager.extension.handler;

import com.vedeng.goods.manager.extension.GoodsToDoHandlerContext;
import com.vedeng.goods.utils.GoodsUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

/**
 * <AUTHOR> [<EMAIL>]
 */
public abstract class BaseGoodsTodoHandler implements GoodsTodoHandler {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    private final GoodsToDoHandlerContext context = GoodsToDoHandlerContext.getInstance();

    protected boolean preCondition() {
        return true;
    }


    protected Integer getGoodId() {
        Integer goodsId = context.getGoodsId();
        if (goodsId == null) {
            throw new IllegalStateException("goods Id is null");
        }

        return goodsId;
    }

    protected String getSkuNo() {
        return GoodsUtils.createGoodsNo(getGoodId());
    }

    @Override
    public void finish(Object param) {
        //no-op
    }

    /**
     * Adds a record.
     *
     * @param goodsId the primary key of SKU
     * @param goodsNo
     */
    protected void addGoodsTodoRecord(Integer goodsId, String goodsNo) {
        Objects.requireNonNull(goodsId, "goodsId is required");
        getITodoInstance().add(goodsId, goodsNo, null, null);
    }

    @Override
    public boolean hasTodoItems() {
        return getITodoInstance().hasTodoItem(getGoodId());
    }

}
