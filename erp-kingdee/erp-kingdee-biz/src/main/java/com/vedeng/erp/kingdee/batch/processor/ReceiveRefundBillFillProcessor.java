package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchBankBillDto;
import com.vedeng.erp.kingdee.dto.KingDeeFileDataDto;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveRefundBillDto;
import com.vedeng.erp.kingdee.service.KingDeeFileDataService;
import com.vedeng.erp.kingdee.service.impl.KingDeeReceiveRefundBillServiceImpl;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;

@Slf4j
@Service
public class ReceiveRefundBillFillProcessor extends BaseProcessor<BatchBankBillDto, KingDeeFileDataDto> {
    public static final String ZERO = "0";

    @Autowired
    private KingDeeReceiveRefundBillServiceImpl kingDeeReceiveRefundBillServiceImpl;

    @Autowired
    private KingDeeFileDataService kingDeeFileDataService;

    @Override
    public KingDeeFileDataDto doProcess(BatchBankBillDto batchBankBillDto, JobParameters params, ExecutionContext stepContext) throws Exception {
        KingDeeFileDataDto query = KingDeeFileDataDto.builder()
                .formId(KingDeeFormConstant.RECEIVE_REFUND)
                .erpId(batchBankBillDto.getBankBillId().toString())
                .url(batchBankBillDto.getReceiptUrl())
                .build();
        List<KingDeeFileDataDto> existFile = kingDeeFileDataService.getByBusinessIdAndUri(query);
        if (!CollectionUtils.isEmpty(existFile)) {
            log.info("当前附件已经推送过金蝶，{}", JSON.toJSONString(query));
            return null;
        }

        KingDeeReceiveRefundBillDto kingDeeReceiveRefundBillDto = new KingDeeReceiveRefundBillDto();
        if (KingDeeConstant.ZERO.equals(batchBankBillDto.getStatus()) && batchBankBillDto.getMatchedAmount().compareTo(BigDecimal.ZERO) != 0) {
            kingDeeReceiveRefundBillDto.setFBillNo("C_" + batchBankBillDto.getBankBillId());
        } else {
            kingDeeReceiveRefundBillDto.setFBillNo("B_" + batchBankBillDto.getBankBillId());
        }
        kingDeeReceiveRefundBillServiceImpl.query(kingDeeReceiveRefundBillDto);
        if (kingDeeReceiveRefundBillDto.getFID() == null || ZERO.equals(kingDeeReceiveRefundBillDto.getFID())) {
            log.info("上传出库单附件,金蝶收款退款单未推送金蝶：{}", JSON.toJSONString(batchBankBillDto));
            return null;
        }

        return KingDeeFileDataDto.builder()
                .fileName(batchBankBillDto.getBankBillId() + ".pdf")
                .aliasFileName(batchBankBillDto.getBankBillId() + ".pdf")
                .billNo(kingDeeReceiveRefundBillDto.getFBillNo())
                .formId(KingDeeFormConstant.RECEIVE_REFUND)
                .isLast(true)
                .fId(kingDeeReceiveRefundBillDto.getFID())
                .url(batchBankBillDto.getReceiptUrl())
                .erpId(batchBankBillDto.getBankBillId().toString())
                .businessId(kingDeeReceiveRefundBillDto.getFormId() + batchBankBillDto.getBankBillId().toString())
                .build();
    }
}
