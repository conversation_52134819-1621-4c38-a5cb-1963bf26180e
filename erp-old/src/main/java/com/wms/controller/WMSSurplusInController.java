package com.wms.controller;

import com.alibaba.fastjson.JSON;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.authorization.model.Organization;
import com.vedeng.authorization.model.Position;
import com.vedeng.authorization.model.Role;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.DateUtil;
import com.vedeng.logistics.eums.WarehouseGoodsInEnum;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.logistics.model.outIn.OutInDetail;
import com.vedeng.system.service.OrgService;
import com.vedeng.system.service.RoleService;
import com.vedeng.system.service.UserService;
import com.wms.constant.VerifyStatusEnum;
import com.wms.model.dto.WmsInputOrderGoodsDto;
import com.wms.model.dto.WmsSurplusInQueryDto;
import com.wms.model.po.WmsInputOrder;
import com.wms.model.po.WmsSurplusInOrder;
import com.wms.service.WmsSurplusinService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.task.IdentityLink;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName WMSSurplusInController.java
 * @Description TODO 盘盈入库单
 * @createTime 2020年09月17日 20:01:00
 */
@Controller
@RequestMapping("/wms/surplusIn")
public class WMSSurplusInController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(WMSSurplusInController.class);
    @Autowired
    private OrgService orgService;

    @Autowired
    private WmsSurplusinService wmsSurplusinService;

    @Autowired
    @Qualifier("actionProcdefService")
    protected ActionProcdefService actionProcdefService;

    @Autowired
    private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

    @Autowired
    private RoleService roleService;

    @Autowired
    @Qualifier("userService")
    private UserService userService;

    /**
     * @description: 盘盈入库列表页
     * @return: ModelAndView
    * @author: Strange
     * @date: 2020/9/21
     **/
    @ResponseBody
    @RequestMapping("/index")
    public ModelAndView index(HttpServletRequest request, WmsSurplusInQueryDto wmsSurplusInQueryDto,
                              @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                              @RequestParam(required = false) Integer pageSize,
                              HttpSession session){

        ModelAndView mv = new ModelAndView();
        User user =(User)session.getAttribute(ErpConst.CURR_USER);

        Page page = getPageTag(request,pageNo,pageSize);
        List<WmsSurplusInOrder> list =  wmsSurplusinService.querySurplusInlistPage(wmsSurplusInQueryDto,page);
        SimpleDateFormat ft = new SimpleDateFormat("yyyy-MM-dd");
        String applyIntimeEnd = "";
        if(wmsSurplusInQueryDto.getApplyIntimeEnd() != null){
            applyIntimeEnd = ft.format( wmsSurplusInQueryDto.getApplyIntimeEnd());
        }
        String applyIntimeStrat = "";
        if(wmsSurplusInQueryDto.getApplyIntimeStrat() != null){
            applyIntimeStrat = ft.format(wmsSurplusInQueryDto.getApplyIntimeStrat().getTime());
        }
        mv.addObject("applyIntimeEnd", applyIntimeEnd);
        mv.addObject("applyIntimeStrat",applyIntimeStrat);
        mv.addObject("wmsSurplusInQueryDto",wmsSurplusInQueryDto);
        mv.addObject("wmsSurplusInOrderlist", list);
        mv.addObject("page",page);
        mv.setViewName("wms/surplusIn/surplusInIndex");
        return mv;
    }
    /**
     * @description: 盘盈入库保存页
     * @return: ModelAndView
     * @author: Strange
     * @date: 2020/9/21
     **/
    @ResponseBody
    @RequestMapping("/surplusInAdd")
    public ModelAndView surplusInAdd(HttpServletRequest request,
                              HttpSession session){

        ModelAndView mv = new ModelAndView();
        User user =(User)session.getAttribute(ErpConst.CURR_USER);
        // 获取申请部门
        List<Organization> orgList = orgService.getOrgList(0, user.getCompanyId(), true);

        mv.addObject("orgList", orgList);
        mv.addObject("today", DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd"));
        mv.setViewName("wms/surplusIn/surplusInAdd");
        return mv;
    }

    /**
     * @description: 保存盘盈入库单
     * @return: ResultInfo
     * @author: Strange
     * @date: 2020/9/21
     **/
    @ResponseBody
    @RequestMapping("/saveSurplusInOrder")
    public ModelAndView saveSurplusInOrder(HttpServletRequest request, WmsInputOrder wmsInputOrder,
                                   HttpSession session){

        ModelAndView mv = new ModelAndView();
        try {
            User user =(User)session.getAttribute(ErpConst.CURR_USER);
            wmsInputOrder.setCreator(user.getUserId());
            wmsInputOrder.setUpdater(user.getUserId());
            ResultInfo resultInfo =  wmsSurplusinService.saveSurplusInOrder(wmsInputOrder);
            if(resultInfo.getCode().equals(0)){
                WmsInputOrder data = (WmsInputOrder) resultInfo.getData();
                startProcessInstance(request,data.getWmsInputOrderId());
                mv.addObject("url","/wms/surplusIn/applyIndex.do?wmsInputOrderId="+data.getWmsInputOrderId());
                return success(mv);
            }
        }catch (Exception e){
            logger.error("saveSurplusInOrder  error",e);
        }
        return fail(mv);
    }

    private void startProcessInstance(HttpServletRequest request,Integer wmsInputOrderId) throws Exception{

        //初始化工作流变量
        Map<String, Object> variableMap = new HashMap<>();

        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        Integer roleflag = 0;
        List<Position> positionList = userService.getPositionByUserId(user.getUserId());
        for (Position position : positionList) {
            if(position.getPositionName().contains("经理") || position.getPositionName().contains("主管")){
                roleflag = 1;
            }
            if(position.getPositionName().contains("总监")){
                roleflag = 2;
                variableMap.put("pass", true);
            }
            if (position.getPositionName().contains("财务总监")) {
                roleflag = 3;
                variableMap.put("pass", true);
                break;
            }
        }
        variableMap.put("processDefinitionKey", "surplusInOrderAudit");
        variableMap.put("businessKey", "surplusInOrderAudit_" + wmsInputOrderId);
        variableMap.put("currentAssinee", user.getUsername());
        variableMap.put("currentAssineeId", user.getOrgId());
        variableMap.put("wmsInputOrderId", wmsInputOrderId);
        variableMap.put("roleflag", roleflag);

        WmsInputOrder wmsInputOrder = this.wmsSurplusinService.getSurplusInOrderById(wmsInputOrderId);
        variableMap.put("surplusInOrderNo", wmsInputOrder.getOrderNo());

        actionProcdefService.createProcessInstance(request, variableMap.get("processDefinitionKey").toString(), variableMap.get("businessKey").toString(), variableMap);

        // 获取当前活动节点
        Task taskInfo = processEngine.getTaskService()
                .createTaskQuery()
                .processInstanceBusinessKey(variableMap.get("businessKey").toString())
                .singleResult();

        //完成申请的任务
        actionProcdefService.complementTask(request, taskInfo.getId(), "", user.getUsername(), variableMap);
    }
    /**
     * @description:
     * @return: 盘盈审核详情页
     * @author: Strange
     * @date: 2020/9/22
     **/
    @ResponseBody
    @RequestMapping("/applyIndex")
    public ModelAndView applyIndex(HttpServletRequest request , HttpSession session, WmsInputOrder searchOrder){
        User user =(User)session.getAttribute(ErpConst.CURR_USER);
        ModelAndView mv = new ModelAndView();
        WmsInputOrder wmsInputOrder = wmsSurplusinService.getSurplusInOrderById(searchOrder.getWmsInputOrderId());
        List<WmsInputOrderGoodsDto> wmsInputOrderGoodsList = wmsSurplusinService.getWmsInputOrderGoodsDto(searchOrder.getWmsInputOrderId());


        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, "surplusInOrderAudit_" + searchOrder.getWmsInputOrderId());
        mv.addObject("historicInfo", historicInfo);
        mv.addObject("historicActivityInstance", historicInfo.get("historicActivityInstance"));
        mv.addObject("commentMap", historicInfo.get("commentMap"));

        if (historicInfo.get("taskInfo") != null) {

            Task taskInfo = (Task) historicInfo.get("taskInfo");

            mv.addObject("taskId",taskInfo.getId());
            mv.addObject("taskInfo", taskInfo);

            //获取审核人候选组
            Map<String, Object> candidateUserMap = (Map<String, Object>) historicInfo.get("candidateUserMap");

            //获取审核人候选组
            List<IdentityLink> candidateUserList = (List<IdentityLink>) candidateUserMap.get(taskInfo.getId());

            if(CollectionUtils.isNotEmpty(candidateUserList)){

                List<String> userNameList = candidateUserList.stream().map(identityLink -> identityLink.getUserId()).collect(Collectors.toList());

                mv.addObject("verifyUsers", StringUtils.join(userNameList,","));
            }
        }
        Boolean cancelflag = cancelflag(wmsInputOrder,user);
        logger.info("查询盘盈入库详情:{}", JSON.toJSONString(wmsInputOrder));
        List<OutInDetail> outInDetailList = wmsSurplusinService.getOUtInDetailList(wmsInputOrder.getOrderNo(), WarehouseGoodsInEnum.RECEIVE_WAREHOUSE_OUT.getErpCode(),WarehouseGoodsInEnum.RECEIVE_WAREHOUSE_OUT.getErpCode());
        mv.addObject("outInDetailList",outInDetailList);
        mv.addObject("cancelflag",cancelflag);
        mv.addObject("curr_user",user);
        mv.addObject("wmsInputOrderGoodsList",wmsInputOrderGoodsList);
        mv.addObject("wmsInputOrder",wmsInputOrder);
        mv.setViewName("wms/surplusIn/surplusApplyIndex");
        return mv;
    }

    private Boolean cancelflag(WmsInputOrder wmsInputOrder, User user) {
        Boolean result = true;
        if(wmsInputOrder.getCreator() == null){
            result = false;
        }else if(user.getUserId() == null){
            result = false;
        }else if(!wmsInputOrder.getCreator().equals(user.getUserId())){
            result = false;
        }
        if(VerifyStatusEnum.COLSED.getValue() == wmsInputOrder.getVerifyStatus()){
            result = false;
        }else if(VerifyStatusEnum.Approved.getValue() == wmsInputOrder.getVerifyStatus()){
            result = false;
        }
        return result;
    }

    /**
     * 审核结果页面
     * @param taskId
     * @param pass
     * @param wmsInputOrderId
     * @return
     */
    @RequestMapping(value = "/auditResult")
    public ModelAndView auditResult(String taskId, Boolean pass,Integer wmsInputOrderId) {
        ModelAndView mv = new ModelAndView();
        mv.addObject("taskId", taskId);
        mv.addObject("pass", pass);
        mv.addObject("wmsInputOrderId", wmsInputOrderId);
        mv.setViewName("wms/surplusIn/auditResult");
        return mv;
    }
    /**
     * 取消订单
     * @return
     */
    @RequestMapping(value = "/closeSurplusInOrder",method = RequestMethod.POST)
    @ResponseBody
    public ResultInfo closeSurplusInOrder(Integer wmsInputOrderId) {
        try {

            WmsInputOrder wmsInputOrder = this.wmsSurplusinService.getSurplusInOrderById(wmsInputOrderId);

            if(wmsInputOrder.getVerifyStatus() == VerifyStatusEnum.Approved.getValue()){
                return new ResultInfo(-1, "盘盈单已经审核通过，不允许关闭");
            }

            this.wmsSurplusinService.updatesurplusInOrderAuditStatus(wmsInputOrderId,VerifyStatusEnum.COLSED.getValue());

            //如果是审核中,就删除现在的流程实例
            if(VerifyStatusEnum.Reviewing.getValue() == wmsInputOrder.getVerifyStatus()){

                // 获取当前活动节点
                Task taskInfo = processEngine.getTaskService()
                        .createTaskQuery()
                        .processInstanceBusinessKey("surplusInOrderAudit_"+wmsInputOrderId)
                        .singleResult();

                if(taskInfo != null){
                    //删除流程实例
                    actionProcdefService.deleteProcessInstance(taskInfo.getId());
                }
            }

            return new ResultInfo(0, "操作成功");

        } catch (Exception e) {
            logger.error("closeSurplusInOrder:", e);
            return new ResultInfo(-1, "操作失败");
        }
    }
    /**
     * 盘盈单审核操作
     */
    @ResponseBody
    @RequestMapping(value = "/complementTask")
    public ResultInfo<?> complementTask(HttpServletRequest request,Integer wmsInputOrderId,String taskId, String comment, Boolean pass,
                                        HttpSession session) {

        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);

        try {
            //完成当前任务，工作流往下走
            Map<String, Object> taskVaribles = new HashMap<String, Object>();
            taskVaribles.put("pass", pass);

            ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, comment,
                    user.getUsername(), taskVaribles);

            return new ResultInfo(0, "操作成功");

        } catch (Exception e) {
            logger.error("complementTask:", e);
            return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
        }

    }

    /**
     * @description: 盘盈入库详情页
     * @return: ModelAndView
     * @author: Strange
     * @date: 2020/9/21
     **/
    @ResponseBody
    @RequestMapping("/detailJump")
    public ModelAndView detailJump(HttpServletRequest request , HttpSession session, WmsInputOrder wmsInputOrder){
        ModelAndView mv = new ModelAndView();
        User user =(User)session.getAttribute(ErpConst.CURR_USER);

        WmsInputOrder wmsInputOrder1 = wmsSurplusinService.getSurplusInOrderById(wmsInputOrder.getWmsInputOrderId());
        List<WmsInputOrderGoodsDto> wmsInputOrderGoodsList = wmsSurplusinService.getWmsInputOrderGoodsDto(wmsInputOrder.getWmsInputOrderId());

        List<WarehouseGoodsOperateLog> wlog = wmsSurplusinService.getWarehouseLogListByOrderId(wmsInputOrder.getWmsInputOrderId(), StockOperateTypeConst.SURPLUS_WAREHOUSE_IN);

        mv.addObject("wlog",wlog);
        mv.addObject("wmsInputOrderGoodsList",wmsInputOrderGoodsList);
        mv.addObject("wmsInputOrder",wmsInputOrder1);
        mv.setViewName("wms/surplusIn/surplusInDetailJump");
        return mv;
    }

    /**
     * 判断当前用户是否有传入的角色
     * @param applyerUserid
     * @return
     */
    private boolean currentUserHasRoleName(Integer applyerUserid,String roleName) {

        AtomicBoolean flag = new AtomicBoolean(false);

        List<Role> roles = roleService.getUserRoles(applyerUserid);
        if(CollectionUtils.isEmpty(roles)){
            return false;
        }

        roles.stream().forEach(
                role -> {
                    if (role.getRoleName().contains(roleName)){
                        flag.set(true);
                    }
                }
        );

        return flag.get();
    }

    /**
     * 校验盘盈入库单审核人信息
     * @return
     */
    @RequestMapping(value = "/validateApplyerInfo",method = RequestMethod.POST)
    @ResponseBody
    public ResultInfo validateApplyerInfo(HttpServletRequest request) {
        try {
            User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);

            Integer applyerUserid = user.getUserId();
            Integer applyerDepartmentId = user.getOrgId();

            if(currentUserHasRoleName(applyerUserid,"总监")){
                return new ResultInfo(0, "校验成功");
            }

            if(currentUserHasRoleName(applyerUserid,"经理")){

                List<User> userList = userService.getUserByPositLevel(applyerDepartmentId, 442);

                if(userList == null){
                    return new ResultInfo(-1, "当前用户没有对应的总监");
                }else {
                    return new ResultInfo(0, "校验成功");
                }
            }

            List<User> userList = userService.getUserByPositLevel(applyerDepartmentId, 609);

            if(userList == null){
                return new ResultInfo(-1, "当前用户没有对应的经理");
            }

            return new ResultInfo(0, "校验成功");

        } catch (Exception e) {
            logger.error("validateApplyerInfo:", e);
            return new ResultInfo(-1, "校验失败");
        }
    }

}
