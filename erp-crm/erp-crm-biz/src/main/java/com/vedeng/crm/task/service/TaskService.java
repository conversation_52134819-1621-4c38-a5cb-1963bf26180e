package com.vedeng.crm.task.service;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.domain.DataDictionaryDto;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.crm.business.quote.domain.dto.SendMessageDto;
import com.vedeng.crm.common.domain.DeadlineRole;
import com.vedeng.crm.task.domain.dto.*;
import com.vedeng.crm.task.domain.entity.TaskEntity;
import com.vedeng.crm.task.domain.vo.MyTaskVo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TaskService {


    /**
     * 发送卡片消息
     * @param sendMessageDto
     * @param title
     */
    void sendCardMsg(SendMessageDto sendMessageDto, String title);

    /**
     * 新增任务
     *
     * @param taskDto taskDto
     */
    void save(TaskDto taskDto);

    void updateTaskForVisit(TaskDto taskDto);

    /**
     * 获取任务类型。
     *
     * @param businessId 业务ID。
     * @param type  1商机 2线索
     * @return 返回任务类型列表。
     */
    List<DataDictionaryDto> getTaskType(Integer businessId,Integer type);

    /**
     * 获取候选用户。
     *
     * @return 返回候选用户列表。
     */
    List<DeadlineRole.Department> getCandidateUser();


    /**
     * 查询任务列表。
     *
     * @param taskQueryDto 查询条件。
     * @return 返回任务列表。
     */
    PageInfo<MyTaskVo> taskPage(PageParam<TaskQueryDto> taskQueryDto);

    /**
     * 获取待办任务数量。
     *
     * @param todoUserId 待办人ID。
     * @return 返回待办任务数量。
     */
    int getTaskCount(Integer bizId, Integer todoUserId, Integer bizType);

    /**
     * 获取我的任务
     *
     * @param dto
     */
    List<MyTaskVo> getMyTask(TaskQueryDto dto);

    /**
     * 根据任务ID查询任务详细信息。
     *
     * @param taskId     任务ID。
     * @param taskItemId 子任务ID。
     * @return 返回任务的详细DTO对象。
     */
    MyTaskVo detail(Long taskId, Long taskItemId);

    /**
     * 处理任务。
     *
     * @param taskHandleDto 任务处理DTO对象。
     */
    void handle(TaskHandleDto taskHandleDto);

    /**
     * 获取指定业务ID的督导类型。
     *
     * @param businessId 业务ID。
     */
    List<DataDictionaryDto> getSupervisionType(Integer businessId);


    /**
     * 获取发起人,获取待办人。
     *
     * @return 返回创建者列表。
     */
    List<TaskUserDto> getCreator();

    /**
     * 临期提醒
     */
    void deadlineReminder();

    /**
     * 超时提醒
     */
    void timeoutReminder();

    /**
     * 我的待办超时统计
     */
    void myTodoTimeoutStatistic();

    /**
     * 我的发起超时统计
     */
    void myInitiativeTimeoutStatistic();

    /**
     * 商机督导
     */
    void alertBussinessChanceTask();


    /**
     * 商机跟进
     */
    void alertBussinessChanceProcessTask(String check);


    /**
     * 级联获取任务类型
     */
    List<DataDictionaryDto> cascadeGetTaskType();

    /**
     * 获取待办任务
     * 1.任务类型：再次跟进
     * 2.未处理
     * 3.截止时间 小于等于 当前日
     *
     * @param bizId 业务ID
     * @param bizType 业务
     */
    List<TaskItemDto> getAgainTodoTask(Integer bizId, Integer bizType);

    /**
     * 查询商机下是否有进行中的待办
     * @param bussinessChanceId
     * @return
     */
    Integer queryTaskForBussinessChance(Integer bussinessChanceId);


    /**
     * 处理之前的待办任务
     * @param taskDto
     */
    void handlePreviousTodoTasks(TaskDto taskDto);

    /**
     * 线索关闭时，自动关闭对应的任务-所有的任务
     */
    void handleTaskCompleteForLeadsClose(Integer bizId);
    /**
     * 商机关闭时，自动关闭对应的任务-所有的任务
     * 参见  商机赢单时，自动关闭对应的任务-所有的任务
     */
    void handleTaskCompleteForBusincessChance(Integer bizId);
    /**
     * 商机赢单时，自动关闭对应的任务-所有的任务
     */
    void handleTaskCompleteForBusincessChance(Integer bizId,Integer doneStatus,String doneRemark);
    /**
     * 订单关闭时，关闭对应的商机，从而将任务也给关闭
     * @param taskDto
     */
    void handleTaskCompleteForBusincessChance(TaskDto taskDto);

}

