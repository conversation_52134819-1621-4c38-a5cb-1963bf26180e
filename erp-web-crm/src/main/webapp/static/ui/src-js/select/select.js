
var Mixin_uiSelect_transition = {
    data() {
        return {}
    },
    mounted() {
        // window.addEventListener('scroll', this.vdListScroll, true) // 改为true是从外到里，事件捕获，false是从里到外，事件冒泡
        // window.addEventListener('resize', this.vdListScroll)
    },
    methods: {
        beforeLeave(el) {
            el.style.webkitTransform = 'scale(1,1)';
            el.style.opacity = 1;
        },
        leave(el) {
            if (el.scrollHeight !== 0) {
                // el.style.transition = '0.19s all ease-in';
                el.style.webkitTransform = 'scale(1,0)';
                el.style.opacity = 0;
            }
        },
        afterLeave(el) {
            el.style.transition = '';
            el.style.webkitTransform = '';
            el.style.opacity = '';
        },
        topScroll() {
            console.log('111')
            let client = this.$el.getBoundingClientRect();
            let height = this.$refs.vdList.clientHeight;
            let clientHeight = document.body.clientHeight;
            if (client.bottom + height + 7 > clientHeight && client.top >= height + 2) {
                this.animation = 'appear-up';
                this.$refs.vdList.style.top = `-${height + 2}px`;
                this.$refs.vdList.style.boxShadow = 'rgba(0, 0, 0, 0.1) 0px -5px 10px';
            } else {
                this.animation = 'appear-down';
                this.$refs.vdList.style.top = "";
                this.$refs.vdList.style.boxShadow = '';
            }
        },
        vdListScroll() {
            console.log('asd')
            if (this.$refs.vdList) {
                this.topScroll();
            }
        },
    }
};


var Mixin_uiSelect_multipleSelect = {
    data() {
        return {
            mulList: [],
            mulNumber: 0,
            mulSeleList: [],
            showTop: false,
        }
    },
    mounted() {
        if (this.defaultMulti && this.defaultMulti.length) {
            this.mulSeleList = this.defaultMulti;
            this.mulList = JSON.parse(JSON.stringify(this.mulSeleList));
        }

        if (this.multipleType == 'fixed') {
            this.tagLabelNum();
        }

        window.addEventListener('resize', () => {
            if (this.multipleType && this.multipleType == 'fixed' && this.mulSeleList && this.mulSeleList.length) {
                this.mulList = JSON.parse(JSON.stringify(this.mulSeleList));
                this.tagLabelNum();
            }
        })
    },
    computed: {
        moreTag() {
            if (this.mulNumber === 0) {
                return false
            } else {
                return true
            }
        }
    },
    methods: {
        arrayContra(arr, className) {
            let newArr = new Array();
            for (var i = 0, len = arr.length; i < len; i++) {
                if (arr[i]._prevClass && arr[i]._prevClass.indexOf(className) >= 0) {
                    newArr.push(arr[i]);
                }
            }
            return newArr;
        },
        tagLabelNum() {
            this.$nextTick(() => {
                if (!this.$refs.tagBox) {
                    return;
                }

                let all = this.$refs.tagBox.offsetWidth;
                let offsetWidth = 0;
                let arr = this.arrayContra(this.$refs.tagBox.children, "tag-label");
                this.mulNumber = 0;
                this.showTop = false;

                try {
                    arr.forEach((item, index) => {
                        offsetWidth += item.offsetWidth + 5;
                        let gap = 2;

                        if(this.multipleType == 'fixed' ) {
                            gap = 0;
                        }

                        if (offsetWidth > all - gap) { // 最大宽度不会超出，第一个对象肯定放得下
                            offsetWidth -= item.offsetWidth - 5;
                            this.mulNumber = this.mulList.length - index;
                            this.mulList = this.mulList.slice(0, index);
                            throw new Error("overNative");
                        }
                    })
                } catch (e) {
                    if (e.message != 'overNative') throw e
                }
                if (this.mulList.length == 1 && this.mulNumber != 0) {
                    this.$nextTick(() => {
                        if (offsetWidth + this.$refs.tagMore.offsetWidth + 5 > all) {
                            this.showTop = true
                        }
                    })
                }
                console.log(this.mulList.length, this.mulNumber)
                if (this.mulList.length > 1 && this.mulNumber != 0) {
                    this.$nextTick(() => {
                        let more = this.$refs.tagMore.offsetWidth + 5;
                        let arrNew = this.arrayContra(this.$refs.tagBox.children, "tag-label");
                        try {
                            arrNew.reverse().forEach((item, index) => {//数组反转
                                if (offsetWidth + more <= all) {//已经在一行
                                    throw new Error("kill");
                                }

                                if (arrNew.length === index + 1) { //到第一项不删除
                                    this.mulList = this.mulList.slice(0, 1);
                                    this.mulNumber += index;
                                    throw new Error("kill");
                                }

                                offsetWidth = offsetWidth - item.offsetWidth - 5;
                                if (offsetWidth + more <= all) {
                                    this.mulNumber += index + 1;
                                    this.mulList = this.mulList.slice(0, this.mulList.length - (index + 1));
                                    throw new Error("kill");
                                }
                            })
                        } catch (e) {
                            if (e.message != 'kill') throw e
                        }
                    })
                }
            })
        },
        removeTag(obj) {
            for (let i in this.mulSeleList) {
                if (this.mulSeleList[i].value == obj.value) {
                    this.mulSeleList.splice(i, 1);
                    this.changeMulList();
                    if (this.multipleType == 'fixed') {
                        this.tagLabelNum();
                    }
                    return;
                }
            }
        },
        changeMulList() {
            let arr = new Array();
            this.mulSeleList.forEach(item => {
                if(/^\d*$/.test(item.value)) {
                    arr.push(Number(item.value));
                } else {
                    arr.push(item.value);
                }
            })
            this.changeVal(this.showVal, arr); // change事件
            this.mulList = JSON.parse(JSON.stringify(this.mulSeleList));
            this.selectValue(arr);
        },
    },
}

Vue.component('ui-select', {
    template: `
        <div
            class="vd-ui-select"
            :style="{'width':width}"
            @click.stop="handleClick"
            @mouseenter="handleEnter"
            @mouseleave="handleLeave"
        >
            <div
                v-if="!multipleType"
                class="vd-ui-select-wrapper"
                :class="[disabled? 'vd-ui-select-wrapper__disabled': '', errorable? 'vd-ui-select-wrapper__error': '']"
            >
                <ui-input
                    :placeholder="holder"
                    v-bind="inputattr"
                    :disabled="disabled"
                    v-model="showLabel"
                    ref="vdInput"
                    @focus="focus"
                    @blur="blur"
                    @clear="clear"
                    @keydown.native.tab="rotate=false"
                    @input.native="handleInput"
                    @compositionend.native="commentPress"
                    :size="size"
                    width="100%"
                    :readonly="readonly"
                    autocomplete="off"
                    :clearable="clearValue"
                    :selectClear="selectClear"
                >
                    <i slot="suffix" class="vd-ui_icon icon-down icon" :class="[rotate? 'rotate': '', size? size: '']" v-if="iconDown"></i>
                    <i slot="suffix" class="vd-ui_icon icon-search icon" :class="[size? size: '']" v-if="iconSearch"></i>
                </ui-input>
            </div>
            <div 
                v-if="multipleType"
                class="vd-ui-select-wrapper vd-ui-select-multiple-wrapper"
                :class="[
                    disabled? 'vd-ui-select-multiple-wrapper__disabled': '',
                    size? 'vd-ui-select-multiple-wrapper-'+size: '',
                    rotate? 'is-focus': '',
                    errorable? 'vd-ui-select-multiple-wrapper__error': ''
                ]"
                
            >
                <div class="vd-ui-tag " :class="[ multipleType == 'auto'? 'vd-ui-tag-auto': '', {'has-more': moreTag}, readonly? 'vd-ui-readonly': '']" ref="tagBox">
                    <span class="placeholder" v-show="!mulList.length && readonly">{{placeholder || '请选择'}}</span>
                        <span class="vd-ui-select-tag tag-label" v-for="item in mulList" :key="item.value">
                        <span class="tag-text">{{item.label}}</span>
                        <span class="tag-icon" @click.stop v-if="!disabled"><i class="vd-ui_icon icon-delete" @click.stop="removeTag(item)"></i></span>
                    </span>

                    <span ref="tagMore" class="vd-ui-select-tag tag-more" :class="{'marginTop':showTop}" v-if="moreTag">
                        <span class="tag-text">+{{mulNumber}}</span>
                    </span>

                    <input
                        v-if="multipleType == 'auto' && !readonly"
                        type="text"
                        class="vd-ui-input-multiple"
                        ref="multipleInput" 
                        :placeholder="!disabled && !mulList.length ? (placeholder || '请输入'): ''"
                        :disabled="disabled"
                        v-model="showLabel"
                        @focus="focus"
                        @blur="blur"
                        @input="handleInput"
                    />
                </div>

                <input
                    v-if="multipleType == 'fixed' && !readonly"
                    type="text"
                    class="vd-ui-input-multiple"
                    ref="multipleInput" 
                    :placeholder="!disabled && !mulList.length ? placeholder || '请输入': ''"
                    :disabled="disabled"
                    v-model="showLabel"
                    @focus="focus"
                    @blur="blur"
                    @input="handleInput"
                />
                <i class="vd-ui_icon icon-down mul-icon" :class="[rotate? 'rotate': '',size? size: '']" v-if="mulDown"></i>
                <i class="vd-ui_icon icon-search mul-icon" v-if="mulSearch"></i>
                <i  v-if="mulClear"
                    class="vd-ui_icon icon-error2 mul-icon"
                    @click.prevent.stop="handleClear"
                ></i>
            </div>

            <ui-poper :show="rotate" :position="animation === 'appear-up' ? 'top' : ''" ref="dropwrap" :errorable="errorable">
            
                    <ul 
                        class="vd-ui-select-list"
                        :class="[animation? animation:'']"
                        ref="vdList"
                        :style="style"
                        @click.stop
                        v-show="rotate"
                        @mousedown.prevent
                    >
                        <template v-if="!noLoad">
                            <template v-if="group">
                                <ui-option-group
                                    v-for="(item,index) in list"
                                    :key="index" 
                                    :data="item.list"
                                    :group="item.group"
                                >
                                </ui-option-group>
                            </template>
                            <template v-else>
                                <ui-option
                                    v-for="(item,index) in list"
                                    :key="index"
                                    :value="item.value"
                                    :label="item.label"
                                    :showlabel="item.showLabel || ''"
                                    :name="item.name"
                                    :shadows="shadows"
                                    :disabled="item.disabled"
                                    :avatar="avatar"
                                    :nameWidth="nameWidth"
                                    :avatar-url="avatar ? item.avatar : ''"
                                >
                                </ui-option>
                                <li class="empty-li" v-if="showdow">
                                    <p>暂无数据</p>
                                </li>
                            </template>
                            <slot v-if="!remote"></slot>
                        </template>
                        
                        <template v-if="loading">
                            <li class="loading-li">
                                <p>
                                    <i class="vd-ui_icon icon-loading" ref="loading"></i>
                                    <span>加载中...</span>
                                </p>
                            </li>
                        </template>

                        <template v-if="loadingFail">
                            <li class="failed-li">
                                <p>
                                    <i class="vd-ui_icon icon-error2"></i>
                                    <span>加载失败，</span>
                                    <span class="reload" @click="handleReload">重新加载</span>
                                </p>
                            </li>
                        </template>

                        <template v-if="!list.length && !noLoad && !$slots.default">
                            <li class="empty-li" v-if="remote">
                                <p>无匹配数据</p>
                            </li>
                            <li class="empty-li" v-else>
                                <p>暂无数据</p>
                            </li>
                        </template>
                    </ul>
            </ui-poper>
            <div class="vd-ui-input-error" v-if="errorable && !disabled">
                <i class="vd-ui_icon icon-error2 vd-ui-input-error--icon"></i>
                <p class="vd-ui-input-error--errmsg">{{errorMsg}}</p>
            </div>
        </div>
    `,

    mixins: [Mixin_uiSelect_transition, Mixin_uiSelect_multipleSelect],
    props: {
        placeholder: {
            type: String,
            default: '请选择'
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        clearable: {
            type: Boolean,
            default: false,
        },
        width: String,
        listWidth: String,
        nameWidth: String,
        size: {
            type: String,
            default: 'middle'
        },
        data: {
            type: Array,
            default: () => {
                return []
            }
        },
        showVal: {
            default: '',
        },
        listAlign: {
            type: String,
            default: ''
        },
        group: {
            type: Boolean,
            default: false
        },
        loading: {
            type: Boolean,
            default: false
        },
        loadingFail: {
            type: Boolean,
            default: false
        },
        firstRemote: {
            type: Boolean,
            default: false
        },
        remote: {
            type: Boolean,
            default: false
        },
        remoteInfo: {
            type: Object,
            default: () => {
                return {}
            }
        },
        multipleType: { //auto  fixed
            type: String,
            default: ''
        },
        shadows: {
            type: Boolean,
            default: false
        },
        errorable: {
            type: Boolean,
            default: false
        },
        avatar: {
            type: Boolean,
            default: false
        },
        maxHeight: {
            type: String,
            default: '300px'
        },
        defaultMulti: {
            type: Array,
            default: () => {
                return [];
            }
        },
        defaultLabel: {
            type: String,
            default: ""
        },
        //是否需要从字典表取值，需要的话，统一传字典表id
        dictionary: {
            type: String,
            default: ''
        },
        nameKeys: {
            type: Array,
            default() {
                return [];
            }
        },
        remoteDataParse: {
            type: Function,
            default: () => { }
        },
        //多选的时候每次选择后是否清空当前搜索框的值
        multiSelectClear: {
            type: Boolean,
            default: true
        },
        cansearch: {
            type: Boolean,
            default: false
        }
    },
    provide() {
        return {
            'select': this
        }
    },
    computed: {
        list() {
            return this.data;
        },
        style() {
            let style = {};
            style.width = this.listWidth
            style.maxHeight = this.maxHeight
            this.listAlign ?
                this.listAlign === 'right' ?
                    style.right = 0 :
                    style.left = this.listAlign : ''
            return style;
        },
        noLoad() {
            return this.loading || this.loadingFail;
        },
        readonly() {
            return !this.remote && !this.cansearch;
        },
        clearValue() {
            return this.clearable && this.selectClear;
        },
        selectClear() {
            return this.showVal ? true : false;
        },
        iconDown() {
            return !((this.entered || this.clearFlag) && this.clearValue && !this.disabled) && !this.iconSearch;
        },
        iconSearch() {
            return (this.remote || this.cansearch) && this.rotate && !this.clearValue;
        },
        mulClear() {
            return (this.clearable && this.showVal.length ? true : false) && (this.entered || this.rotate) && !this.disabled;
        },
        mulDown() {
            return !this.mulClear && !this.mulSearch;
        },
        mulSearch() {
            return (this.remote || this.cansearch) && !this.showVal.length && this.rotate;
        }
    },
    model: {
        prop: 'showVal',
        event: 'value'
    },
    data() {
        return {
            rotate: false,//下拉框
            showLabel: '',
            clearFlag: false,//清空按钮,获焦状态
            animation: '',
            holder: this.placeholder,
            selectObj: '',//选中的对象
            entered: false,
            showdow: false,
            inputTimeout: null,
            inputattr: {},
            dropPosition: {},
            sourceList: []
        }
    },
    created() {
        this.$form.setValidEl(this);
        this.inputattr = this.$attrs;
        delete this.inputattr.valid;

        if (this.dictionary) {
            this.firstRemote = true;
            this.remoteInfo = {
                url: '/crm/sysOption/public/getByParentId?parentId=' + this.dictionary,
                parseLabel: 'title',
                parseValue: 'sysOptionDefinitionId'
            }
        }
    },
    mounted() {
        if(this.cansearch && this.data.length) {
            this.sourceList = JSON.parse(JSON.stringify(this.data));
        }

        document.addEventListener('click', (e) => {
            if (!this.$el.contains(e.target)) this.rotate = false;
        })

        this.$nextTick(() => {
            setTimeout(() => {
                if (!this.defaultLabel) {
                    this.$refs.dropwrap.$children.forEach(item => {
                        // console.log('item:', item.$options);
                        if (item.$options.name == 'ui-option') {
                            item.initLabel();
                        }
                    })
                } else {
                    this.showLabel = this.defaultLabel;
                    this.selectObj = {
                        label: this.defaultLabel,
                        value: this.showVal
                    }
                }
            }, 200)
        })


        if (!this.$refs.vdList.children.length) this.showdow = true;

        if (this.firstRemote && !this.data.length && ((!this.multipleType && this.showVal) || (this.multipleType && this.showVal && this.showVal.length))) {
            this.getFirstList();
        }


    },
    watch: {
        defaultLabel(newV) {
            this.showLabel = newV;
            this.selectObj = {
                label: newV,
                value: this.showVal
            }
        },
        rotate() {
            if (this.rotate) {
                this.$nextTick(() => {
                    this.topScroll();
                })
                if (this.remote) {
                    this.showLabel ? this.holder = this.showLabel : '';
                    this.showLabel = '';
                }
            } else {
                this.holder = this.placeholder;
                this.showLabel = this.selectObj.label ? this.selectObj.label : '';
            }
        }
    },
    methods: {
        focus(event) {
            this.clearFlag = this.clearValue ? true : false;
            this.getSearchList();
            this.$emit('focus', event);
        },
        triggerFocus() {
            if(this.$refs.multipleInput) {
                this.$refs.multipleInput.focus();
            } 
            
            setTimeout(() => {
                this.handleClick();
            }, 200)
        },
        handleClick() {
            if (this.disabled) return;

            if (this.firstRemote && !this.data.length) {
                this.getFirstList(1);
            } else {
                this.remote ? this.rotate = true : this.rotate = !this.rotate;
                if (this.multipleType) {
                    if (!this.readonly) this.$refs.multipleInput.focus();
                }
            }

        },
        blur(event) {
            this.rotate = false;
            this.clearFlag = false;
            this.$emit('blur', event);
        },
        clear() {
            this.clearFlag = false;
            if (this.multipleType) {
                this.selectValue([]);
            } else {
                this.selectValue('');
            }
            this.selectObj = "";
            this.holder = this.placeholder;

            setTimeout(() => {
                this.$refs.vdInput.blur();
            }, 100)
        },
        handleClear() {
            this.changeVal(this.showVal, []);
            this.mulSeleList = [];
            this.mulList = [];
            this.mulNumber = 0;

            this.selectValue([]);
        },
        handleInput(val) {
            console.log('inputType:', val.inputType);
            if (val.inputType != 'insertCompositionText') {
                this.inputTimeout && clearTimeout(this.inputTimeout);

                this.inputTimeout = setTimeout(() => {
                    this.getSearchList();
                }, 300)

                if (this.showLabel) {
                    this.$emit('search', this.showLabel);
                }
            }
        },
        commentPress(e) {
            if (this.showLabel) {
                this.inputTimeout && clearTimeout(this.inputTimeout);

                this.inputTimeout = setTimeout(() => {
                    this.getSearchList();
                }, 300)
                this.$emit('search', this.showLabel);
            }
        },
        handleClose() {
            // this.$refs.vdInput.focus();
        },
        handleEnter() {
            this.entered = true;
        },
        handleLeave() {
            this.entered = false;
        },
        clearData() {
            this.showLabel = '';
            this.clearFlag = false;

            if (this.multipleType) {
                this.selectValue([]);
            } else {
                this.selectValue('');
            }
        },
        //更改v-model绑定的值
        selectValue(val) {
            this.$emit('value', val);

            let resData = {
                value: val,
            };

            if (this.multipleType) {
                resData.list =  this.mulSeleList || [];

                if(this.multiSelectClear) {
                    if(this.showLabel) {
                        this.showLabel = '';
                        this.handleInput({})
                    }
                }
            } else {
                let choosed = this.data.filter(item => item.value == val)[0] || {};
                resData.selected = Object.assign({}, choosed)
            }
            this.$emit('change', resData);
            console.log('change1', resData);
            this.checkValid(val || '');
        },
        changeVal(oldValue, newValue) {
            setTimeout(() => {
                if (!this.$refs.vdList.children.length) this.showdow = true;
                else this.showdow = false;
            })

            let resData = {
                value: newValue,
            };

            if (this.multipleType) {
                resData.list = this.mulSeleList;
            } else {
                let choosed = this.data.filter(item => item.value == this.selectObj.value)[0] || {};
                resData.selected = Object.assign(this.selectObj, choosed)
            }
            this.$emit('change', resData);
            console.log('change2', resData)
        },
        handleReload() {
            this.$emit('reload');
        },
        getSearchList() {
            if (this.remote && this.remoteInfo.url) {
                let value = this.showLabel.trim();
                this.loading = true;

                this.$nextTick(() => {
                    this.$refs.dropwrap.calcPosition();
                })

                let params = {};
                let url = this.remoteInfo.url;

                if (this.remoteInfo.paramsType == 'url') {
                    if (this.remoteInfo.url.indexOf('?') !== -1) {
                        url += '&' + this.remoteInfo.paramsKey + '=' + value;
                    } else {
                        url += '?' + this.remoteInfo.paramsKey + '=' + value;
                    }
                } else {
                    params[this.remoteInfo.paramsKey] = value;
                }

                let reqMethod = this.remoteInfo.paramsMethod || 'post';

                let reqData = {
                    url,
                    method: reqMethod,
                }

                if (reqMethod == 'get') {
                    reqData.params = params
                } else if (reqMethod == 'post') {
                    reqData.data = params;
                }

                this.$axios(reqData).then(({ data }) => {
                    if (data.success) {
                        let list = [];
                        let resList = data.data;

                        let remoteParseData = this.remoteDataParse(resList)
                        if (remoteParseData) {
                            resList = remoteParseData;
                        }

                        resList.forEach(item => {
                            let reg = new RegExp('(' + value + ')', 'ig');

                            let itemData = {
                                label: item[this.remoteInfo.parseLabel],
                                value: item[this.remoteInfo.parseValue] + '',
                                avatar: this.avatar ? item[this.remoteInfo.parseAvatar] : '',
                                showLabel: value && item[this.remoteInfo.parseLabel] ? item[this.remoteInfo.parseLabel].replace(reg, '<span class="strong">$1</span>') : '',
                                ...item
                            }

                            if (this.nameKeys.length) {
                                let names = [];
                                this.nameKeys.forEach(key => {
                                    if (item[key]) {
                                        names.push(item[key])
                                    }
                                })

                                itemData.name = names.join(' / ');
                            } else {
                                delete itemData.name
                            }

                            list.push(itemData);
                        });
                        this.data = list;
                        this.loading = false;

                        this.$nextTick(() => {
                            this.$refs.dropwrap.calcPosition();
                        })
                    }
                })
            } else if(this.cansearch){
                let list = [];
                let value = this.showLabel.trim();

                if(value) {

                    this.sourceList.forEach(item => {
                        let reg = new RegExp('(' + value + ')', 'ig');
    
                        if(item.label.toUpperCase().indexOf(value.toUpperCase()) !== -1) {
                            let itemData = JSON.parse(JSON.stringify(item))
                            itemData.showLabel = item.label.replace(reg, '<span class="strong">$1</span>')
                            list.push(itemData);
                        }
                    });

                    this.data = list;
                } else {
                    this.data = JSON.parse(JSON.stringify(this.sourceList));
                }

                this.$nextTick(() => {
                    this.$refs.dropwrap.calcPosition();
                })
            }
        },
        getFirstList(trigger) {
            this.loading = true;

            this.$axios.post(this.remoteInfo.url, this.remoteInfo.params || {}).then(({ data }) => {
                if (data.success) {
                    let list = [];
                    let resList = data.data;

                    let remoteParseData = this.remoteDataParse(resList)
                    if (remoteParseData) {
                        resList = remoteParseData;
                    }

                    resList.forEach(item => {
                        
                        let itemData = {
                            label: item[this.remoteInfo.parseLabel],
                            value: item[this.remoteInfo.parseValue] + '',
                            ...item
                        }

                        if (this.nameKeys.length) {
                            let names = [];
                            this.nameKeys.forEach(key => {
                                if (item[key]) {
                                    names.push(item[key])
                                }
                            })

                            itemData.name = names.join(' / ');
                        } else {
                            delete itemData.name
                        }

                        list.push(itemData);
                    });
                    this.data = list;
                    this.loading = false;

                    this.$nextTick(() => {
                        if ((!this.multipleType && this.showVal) || (this.multipleType && this.showVal && this.showVal.length)) {
                            this.$refs.dropwrap.$children.forEach(item => {
                                if (item.$options.name == 'ui-option') {
                                    item.initLabel();
                                }
                            })
                        }
                    })

                    if (trigger) {
                        this.remote ? this.rotate = true : this.rotate = !this.rotate;
                        if (this.multipleType) {
                            if (!this.readonly) this.$refs.multipleInput.focus();
                        }
                    }

                }
            })
        },
        checkValid(newValue) {
            if (this.validKey && this.validValue) {
                if (this.$form.validList[this.validKey].indexOf(this.validValue) !== -1) {
                    let validData = this.$form.checkValid(this.validValue, newValue, this.validKey);

                    this.triggerError(validData);
                }
            }
        },
        triggerError(validData) {
            if (validData.result) {
                this.errorMsg = '';
                this.errorable = false;
            } else {
                this.errorMsg = validData.message;
                this.errorable = true;
            }

            this.$refs.dropwrap.calcPosition();
        }
    }
})

Vue.component('ui-custom-search-select', {
    template: `<div class="vd-ui-custom-select-wrap">
        <div class="vd-ui-custom-placeholder" :class="{open: isShowDrop}" @click.stop="showDrop" ref="placeholder">
            <slot name="placeholder" :selected="multi ? multiSelectedObj : selected"></slot>
        </div>
        <ui-poper :show="isShowDrop" ref="dropwrap" :auto-width="true">
            <div class="vd-ui-custom-select-drop" @click.stop :style="'width:' + width ">
                <div class="vd-ui-custom-select-search" v-if="!noSearch">
                    <ui-input suffixIcon="icon-search" v-model="searchVal" @input="handlerInput" placeholder="请输入关键词搜索"></ui-input>
                </div>
                <div class="vd-ui-custom-loading-wrap" v-if="isloading">
                    <i class="vd-ui_icon icon-loading"></i>
                    <span>加载中...</span>
                </div>
                <div class="vd-ui-custom-select-list" :style="'minHeight:' + minHeight" v-else-if="showList && showList.length">
                    <div class="vd-ui-custom-select-item" v-if="multi && needAll" @click="triggerSelectAll">
                        <div class="item-checkbox" v-if="multi">
                            <i class="vd-ui_icon icon-checkbox1" v-if="!isSelectedAll"></i>
                            <i class="vd-ui_icon icon-checkbox2" v-else></i>
                        </div>
                        <div class="item-label" v-if="!searchVal.trim()">全部</div>
                        <div class="item-label" v-else>选中全部搜索项</div>
                    </div>
                    <div class="vd-ui-custom-select-item" v-for="(item, index) in showList" :key="index" @click="selectItem(item)">
                        <div class="item-checkbox" v-if="multi">
                            <i class="vd-ui_icon icon-checkbox1" v-if="!tempSelectedObj[item.value]"></i>
                            <i class="vd-ui_icon icon-checkbox2" v-else></i>
                        </div>
                        <div class="item-avatar" v-if="item.avatar">
                            <img :src="item.avatar" />
                        </div>
                        <div class="item-label text-line-1" v-html="item.showLabel || item.label" :title="item.label"></div>
                    </div>
                </div>
                <div class="vd-ui-custom-empty" v-else>无匹配数据</div>
                <div class="vd-ui-custom-footer" v-if="multi">
                    <ui-button type="primary" @click="handlerMultiSelect">确定</ui-button>
                    <ui-button @click="isShowDrop=false">取消</ui-button>
                </div>
            </div>
        </ui-poper>
    </div>`,
    props: {
        remoteInfo: {
            type: Object,
            default() {
                return {}
            }
        },
        list: {
            type: Array,
            default() {
                return []
            }
        },
        searchKey: {
            type: String,
            default: 'name'
        },
        avatar: {
            type: String,
            default: ''
        },
        width: {
            type: String,
            default: '300px'
        },
        label: {
            type: String,
            default: ''
        },
        value: {
            type: String,
            default: ''
        },
        containHeight: {
            type: Boolean,
            default: false
        },
        multi: {
            type: Boolean,
            default: false
        },
        noSearch: {
            type: Boolean,
            default: false
        },
        //是否可以全选
        needAll: {
            type: Boolean,
            default: false
        },
        default: {
            type: String,
        }
    },
    data() {
        return {
            isloading: false,
            showList: [],
            reqt: 0,
            searchVal: '',
            timeout: null,
            minHeight: '',
            selected: null,
            multiSelectedObj: {},
            tempSelectedObj: {},
            isShowDrop: false,
            isSelectedAll: false
        }
    },
    mounted() {
        this.init();
        document.addEventListener('click', () => {
            this.isShowDrop = false;
        })

        this.showList = this.list;
    },
    watch: {
        default() {
            this.init();
        },
        isShowDrop() {
            console.log(this.isShowDrop)
        }
    },
    methods: {
        init() {
            if(this.default) {
                if(!this.multi) {
                    this.list.forEach(item => {
                        if(item.value == this.default) {
                            this.selected = item;
                        }
                    })
                }
            }
        },
        showDrop() {
            document.body.click();
            this.$emit('open')
            this.tempSelectedObj = JSON.parse(JSON.stringify(this.multiSelectedObj));
            this.$nextTick(() => {
                this.isShowDrop = true;
                this.searchVal = '';
                this.showList = this.list;
                this.checkSelectAll();
            })
        },
        hide() {
            this.isShowDrop = false;
        },
        handlerInput() {
            this.timeout && clearTimeout(this.timeout);

            this.timeout = setTimeout(() => {
                this.search()
            }, 300)
        },
        search(){
            if (this.remoteInfo.url) {
                this.isloading = true;
                let reqt = new Date().getTime();
                this.reqt = reqt;
                this.$axios.post(this.remoteInfo.url, {
                    headers: {
                        reqt
                    }
                }).then((res) => {
                    let reqt = res.config.headers.reqt;
                    console.log(reqt, this.reqt)
                    if(reqt >= this.reqt) {
                        console.log('first')
                        this.isloading = false;
                        let data = res.data;
                        if(data.success) {
                            let list = [];

                            data.data.forEach(item => {
                                list.push({
                                    label: item[this.label],
                                    value: item[this.value],
                                    avatar: this.avatar ? item[this.avatar] : ''
                                })
                            })
                            this.showList = list;

                            if(this.needAll) {
                                this.checkSelectAll();
                            }
                        }
                    }
                })
            } else {
                if(this.containHeight) {
                    this.minHeight = (this.list.length > 10 ? 330 : 33*this.list.length) + 'px';
                }
                let searchValue = this.searchVal.trim();

                if(!searchValue) {
                    this.showList = this.list;
                } else {
                    let reg = new RegExp('(' + searchValue + ')', 'ig');
                    let list = [];
                    this.list.forEach(item => {
                        if(item.label.toUpperCase().indexOf(searchValue.toUpperCase()) !== -1) {
                            let itemData = JSON.parse(JSON.stringify(item))
                            itemData.showLabel = itemData.label.replace(reg, '<span class="strong">$1</span>');
                            list.push(itemData);
                        }
                    })

                    this.showList = list;
                }

                if(this.needAll) {
                    this.checkSelectAll();
                }
            }
        },
        selectItem(item) {
            if(!this.multi) {
                this.selected = item;
                this.onselect(item);
                this.isShowDrop = false;
            } else {
                if(this.tempSelectedObj[item.value]) {
                    this.$set(this.tempSelectedObj, item.value, null)
                    delete this.tempSelectedObj[item.value];
                } else {
                    this.$set(this.tempSelectedObj, item.value, item)
                }
            }
        },
        handlerMultiSelect() {
            this.multiSelectedObj = JSON.parse(JSON.stringify(this.tempSelectedObj));
            this.isShowDrop = false;
            this.onselect(this.multiSelectedObj);
        },
        onselect(data) {
            this.$emit('select', data);
        },
        checkSelectAll() {
            let flag = true;
            this.showList.forEach(item => {
                if(!this.tempSelectedObj[item.value]) {
                    flag = false;
                }
            })

            this.isSelectedAll = flag;
        },
        triggerSelectAll() {
            this.showList.forEach(item => {
                if(this.isSelectedAll) {
                    this.$set(this.tempSelectedObj, item.value, null)
                    delete this.tempSelectedObj[item.value];
                } else {
                    this.$set(this.tempSelectedObj, item.value, item)
                }
            })

            this.isSelectedAll = !this.isSelectedAll;
        }
    }
})