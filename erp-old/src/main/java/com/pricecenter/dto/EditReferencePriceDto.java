package com.pricecenter.dto;

import java.math.BigDecimal;

public class EditReferencePriceDto {

    private String skuNo;

    private Long referencePriceId;

    private BigDecimal referenceTerminalPrice;

    private BigDecimal referenceDistributionPrice;

    private BigDecimal referenceGroupPrice;

    private String operator;

    public String getSkuNo() {
        return skuNo;
    }

    public void setSkuNo(String skuNo) {
        this.skuNo = skuNo;
    }

    public Long getReferencePriceId() {
        return referencePriceId;
    }

    public void setReferencePriceId(Long referencePriceId) {
        this.referencePriceId = referencePriceId;
    }

    public BigDecimal getReferenceTerminalPrice() {
        return referenceTerminalPrice;
    }

    public void setReferenceTerminalPrice(BigDecimal referenceTerminalPrice) {
        this.referenceTerminalPrice = referenceTerminalPrice;
    }

    public BigDecimal getReferenceDistributionPrice() {
        return referenceDistributionPrice;
    }

    public void setReferenceDistributionPrice(BigDecimal referenceDistributionPrice) {
        this.referenceDistributionPrice = referenceDistributionPrice;
    }

    public BigDecimal getReferenceGroupPrice() {
        return referenceGroupPrice;
    }

    public void setReferenceGroupPrice(BigDecimal referenceGroupPrice) {
        this.referenceGroupPrice = referenceGroupPrice;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

}
