package com.vedeng.goods.manager.validator.InvalidatorChain.sku.Invalid;

import com.vedeng.common.model.ResultInfo;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.dao.CoreSpuMapper;
import com.vedeng.goods.exception.InvalidatorChainException;
import com.vedeng.goods.manager.validator.GoodsInvalidatorChain;
import com.vedeng.goods.model.CoreSku;
import com.vedeng.goods.model.CoreSpu;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.vedeng.goods.manager.validator.enums.GoodsInvalidatorErrorEnum.SkuExistSpuInvalidatorError;

/**
 * .(10)	该SKU的SPU存在；
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2021/9/1 16:05.
 * @author: Randy<PERSON>.
 */
@Service
public class SkuExistSpuInvalidator extends GoodsInvalidatorChain {

    @Resource
    CoreSpuMapper coreSpuMapper;

    @Resource
    CoreSkuMapper coreSkuMapper;

    @Override
    public void doInvalid() {
        String SkuNo = getSkuNo();
        List<String> skuNos = Collections.singletonList(SkuNo);
        List<CoreSku> skuList = coreSkuMapper.selectSkuBySkuNoList(skuNos);
        Optional<CoreSku> first = skuList.stream().filter(this::checkSpu).findFirst();
        if(first.isPresent()){
            throw new InvalidatorChainException(SkuExistSpuInvalidatorError.getCode(),SkuExistSpuInvalidatorError.getMessage());
        }
    }

    private boolean checkSpu(CoreSku sku) {
        if(sku.getSpuId() != null && sku.getSpuId() != 0){
            CoreSpu spuinfoById = coreSpuMapper.getSpuinfoById(sku.getSpuId());
            if(spuinfoById == null){
                return true;
            } else {
                return false;
            }
        } else {
            return true;
        }
    }
}
