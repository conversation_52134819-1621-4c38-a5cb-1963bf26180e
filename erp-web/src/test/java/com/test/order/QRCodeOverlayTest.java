package com.test.order;

import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.itextpdf.text.BaseColor;
import com.itextpdf.text.Image;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import org.springframework.core.io.ClassPathResource;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.URL;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2024/12/12 15:54
 */
public class QRCodeOverlayTest {

    public static void main(String[] args) {
        try {
            QrConfig config = new QrConfig();
            config.setWidth(65);
            config.setHeight(65);
            config.setMargin(0);
            // 1. 生成二维码
            BufferedImage qrCodeImage = QrCodeUtil.generate("https://m.vedeng.com/authorization?SQ2024120031-SQ2024120031-SQ2024120031", config);

            // 2. 将二维码保存为临时文件
            File qrCodeFile = new File("qrcode.png");
            ImageIO.write(qrCodeImage, "png", qrCodeFile);

            // 3. 加载 PDF 文件
            String srcPdfPath = "https://file.vedeng.com/file/display?resourceId=1e0873cab9c1467e856b51f0c5431f35"; // 输入 PDF 文件路径
            String destPdfPath = "C:\\Users\\<USER>\\Desktop\\output.pdf"; // 输出 PDF 文件路径
            URL pdfUrl = new URL(srcPdfPath); // 网络 PDF 文件地址
            PdfReader reader = new PdfReader(pdfUrl);
            PdfStamper stamper = new PdfStamper(reader, new FileOutputStream(destPdfPath));

            // 4. 遍历每一页并在左下角添加二维码
            int numberOfPages = reader.getNumberOfPages();
            Image qrImage = Image.getInstance(qrCodeFile.getAbsolutePath());
            float x = 60;
            float y = 5;
            qrImage.setAbsolutePosition(x, y);
            //qrImage.scaleToFit(60, 60);

            ClassPathResource fontResource = new ClassPathResource("fonts/simhei.ttf");
            BaseFont bf = BaseFont.createFont(fontResource.getFile().getAbsolutePath(), BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            for (int i = 1; i <= numberOfPages; i++) {
                PdfContentByte content = stamper.getOverContent(i);
                content.addImage(qrImage);


                content.beginText();
                content.setFontAndSize(bf, 5);
                content.showTextAligned(PdfContentByte.ALIGN_LEFT, "SQ2024120251", x + 18, y + 55, 0);
                content.showTextAligned(PdfContentByte.ALIGN_LEFT, "贝登授权溯源,扫一扫查询真伪", x, y+5, 0);
                content.endText();
            }

            // 5. 关闭文档
            stamper.close();
            reader.close();

            // 删除临时文件
            qrCodeFile.delete();

            System.out.println("二维码已成功添加到 PDF 的左下角并保存到：" + destPdfPath);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
