<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.erp.saleorder.dao.OnlineInvoiceOpenMapper">


    <select id="getInvoiceOpenInfoByOrderNo"
            resultType="com.vedeng.erp.saleorder.model.dto.OrderInvoiceInfo">
        SELECT T1.INVOICE_TYPE,
               T1.INVOICE_METHOD,
               T2.AREA_ID INVOICE_TRADER_ADDRESS_ID,
               T1.INVOICE_TRADER_ADDRESS,
               T1.INVOICE_TRADER_CONTACT_MOBILE,
               T1.INVOICE_TRADER_CONTACT_NAME
        FROM T_SALEORDER T1
                 LEFT JOIN T_TRADER_ADDRESS T2 ON T1.INVOICE_TRADER_ADDRESS_ID = T2.TRADER_ADDRESS_ID
        WHERE T1.SALEORDER_NO = #{orderNojdbcType=VARCHAR}
        GROUP BY T1.SALEORDER_NO
    </select>

    <select id="getAfterSalesNumByOrderNum" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT T2.AFTER_SALES_ID) AFTER_SALES_NUM
        FROM T_SALEORDER T1
                 LEFT JOIN T_AFTER_SALES T2 ON T1.SALEORDER_ID = T2.ORDER_ID
            AND T2.SUBJECT_TYPE = 535
        WHERE T1.SALEORDER_NO = #{orderNo, jdbcType=VARCHAR}
    </select>

    <select id="getOrderInvoiceApplyNumByOrderNo" resultType="java.lang.Integer">
        SELECT
            COUNT( DISTINCT T2.INVOICE_APPLY_ID )
        FROM
            T_SALEORDER T1
                LEFT JOIN T_INVOICE_APPLY T2 ON T1.SALEORDER_ID = T2.RELATED_ID
                AND T2.TYPE = 505
        WHERE
            T1.SALEORDER_NO = #{orderNo, jdbcType=VARCHAR}
          AND ((T2.IS_ADVANCE = 1 AND T2.ADVANCE_VALID_STATUS = 0)
            OR (T2.IS_ADVANCE = 0 AND T2.VALID_STATUS = 0))
    </select>

    <select id="getOrderInvoiceStatusByOrderNo" resultType="java.lang.Integer">
        SELECT IFNULL( SUM( INVOICE_STATUS ), 0 )
        FROM T_SALEORDER
        WHERE SALEORDER_NO = #{orderNo, jdbcType=VARCHAR}
    </select>

    <select id="getOrderIsLockByOrderNo" resultType="java.lang.Integer">
        SELECT IFNULL( SUM( LOCKED_STATUS ), 0 )
        FROM T_SALEORDER
        WHERE SALEORDER_NO = #{orderNo, jdbcType=VARCHAR}
    </select>

    <select id="getOrderModifyNumByOrderNo" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT T2.SALEORDER_MODIFY_APPLY_ID)
        FROM T_SALEORDER T1
                 LEFT JOIN T_SALEORDER_MODIFY_APPLY T2 ON T1.SALEORDER_ID = T2.SALEORDER_ID
            AND T2.COMPANY_ID = 1
                 LEFT JOIN T_VERIFIES_INFO T3 ON T2.SALEORDER_MODIFY_APPLY_ID = T3.RELATE_TABLE_KEY
            AND T3.RELATE_TABLE = 'T_SALEORDER_MODIFY_APPLY'
        WHERE T1.SALEORDER_NO = #{orderNo, jdbcType=VARCHAR}
          AND IFNULL(T3.STATUS, - 1) <![CDATA[<]]> 1
    </select>

    <select id="getCustomerInvoiceInfoByOrderNo" resultType="com.vedeng.erp.saleorder.model.dto.CustomerInvoiceDto">
        SELECT
            T1.INVOICE_TRADER_CONTACT_NAME customerName,
            T2.TAX_NUM taxRegistrationNumber,
            T2.REG_ADDRESS companyAddress,
            T2.REG_TEL mobile,
            T2.BANK bankName,
            T2.BANK_CODE bankTaxNumber,
            T2.BANK_ACCOUNT bankAccount,
            T1.INVOICE_TRADER_ID invoiceTraderId,
            IF
                ( T2.AVERAGE_TAXPAYER_DOMAIN != NULL AND T2.AVERAGE_TAXPAYER_URI != NULL, 1, 0 ) isHaveQualifications
        FROM
            T_SALEORDER T1
                LEFT JOIN T_TRADER_FINANCE T2 ON T1.TRADER_ID = T2.TRADER_ID
                AND T2.TRADER_TYPE = 1
        WHERE
            T1.SALEORDER_NO = #{orderNo, jdbcType=VARCHAR}
        GROUP BY
            T1.SALEORDER_NO
    </select>
</mapper>