@import (less) '../common.css';


.vd-ui-date * {
    margin: 0;
    padding: 0;
    list-style: none;
    box-sizing: border-box;
}

.vd-ui-date {
    position: relative;
    display: inline-block;
    text-align: left;

    // 回显值 input框
    .vd-ui-date-editor {

        // input宽度
        &--year, &--month, &--date, &--datetime, &--datetimerange, &--daterange {
            width: 252px;
            // width: 252px!important;
        }

        &--time {
            width: 180px;
            // width: 180px!important;
        }

        /deep/ .vd-ui-input__inner {
            cursor: pointer;
        }
    }
}

// 弹层
.vd-ui-date-wrapper {
    position: absolute;
    box-sizing: border-box;
    border: solid 1px @Gray-7;
    border-radius: 3px;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
    z-index: 1500;
    background-color: @Text-7;
    list-style-type: none;
    margin: 0;
    
    // 弹层容器宽度
    &--year, &--month, &--date {
        width: 294px;
    }
    &--datetime {
        width: 474px;
    }
    &--time {
        width: 180px;
    }

    &.appear-up {
        transform-origin : center bottom;
        animation: appear 0.22s ease-out;
    }
    &.appear-down {
        transform-origin : center top;
        animation: appear 0.22s ease-out;
    }
}

.vd-ui-panel {
    // width: 292px;

    // panel 头部控制器
    .vd-ui-panel-header {
        background: #fff;
        color: #333;
        height: 46px;
        margin: 0 auto;
        padding: 0;
        border-bottom: solid 1px #ddd;

        > ul {
            margin: 0 auto;
            height: 46px;
            width: 90%;
            display: flex;
            align-items: center;

            > li {
                cursor: pointer;

                &.year-month {
                    flex: 1;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }

                &.arrow {
                    width: 30px;
                    text-align: center;
                    color: #666;
                    i {
                        display: block;
                        font-size: 16px;
                        color: #666;
                    }

                    &.arrow-left1 i {
                        transform: rotate(-90deg);
                    }
                    &.arrow-right1 i {
                        transform: rotate(90deg);
                    }
                }
            }
        }
    }

    // panel table部分
    .vd-ui-panel-body {
        position: relative;
        margin: 5px 15px 5px;

        table {
            table-layout: fixed;
            width: 100%;
        }
    }

    .date-shortcuts {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        align-items: center;
        border-top: solid 1px #E1E5E8;
        padding: 9px 0;

        .item-sc {
            position: relative;
            font-size: 12px;
            color: #09F;
            padding: 4px 9px;
            cursor: pointer;

            &::after {
                content: "";
                display: block;
                width: 0;
                height: 12px;
                border-right: solid 1px #e3e3e3;
                position: absolute;
                right: 0px;
                top: 50%;
                transform: translateY(-50%);
            }
            &:last-child::after {
                display: none;
            }
        }
    }
}

.vd-ui-date-table {
    font-size: 12px;
    user-select: none;
    width: 100%;

    td {
        width: 36px;
        height: 36px;
        box-sizing: border-box;
        text-align: center;
        cursor: pointer;
        position: relative;

        & > div {
            height: 100%;
            padding: 0;
            box-sizing: border-box;
        }

        & span {
            width: 36px;
            height: 36px;
            display: block;
            margin: 0 auto;
            line-height: 34px;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 3px;
        }

        &.next-month, &.prev-month {
            color: #C0C4CC
        }

        &.today {
            position: relative;
            span {
                color: #0099FF;
                font-weight: bold;
                border: solid 1px #0099FF;
            }
            &.start-date span,
            &.end-date span {
                color: #FFF;
            }
        }
        &.available:hover span {
            background-color: #F5F7FA;
        }
        &.available:active span {
            background-color: #EBEFF2;
        }

        &.in-range div {
            background-color: #e0f3ff;
            &:hover {
                background-color: #4e7a96;
                span {
                    background-color: #50bcff;
                }
            }
        }

        &.current:not(.disabled) span {
            color:#FFF;
            background-color:#0099FF;
        }
        &.start-date div,
        &.end-date div {
            color: #FFF;
        }

        &.start-date span,
        &.end-date span {
            background-color: #0099FF;
        }

        &.start-date div {
            margin-left: 5px;
            border-top-left-radius: 15px;
            border-bottom-left-radius: 15px;
        }

        &.end-date div {
            margin-right: 5px;
            border-top-right-radius: 15px;
            border-bottom-right-radius: 15px;
        }

        &.disabled div {
            background-color: #F5F7FA;
            opacity: 1;
            cursor: not-allowed;
            color: #C0C4CC;
        }

        &.selected div {
            margin-left: 5px;
            margin-right: 5px;
            background-color: #F2F6FC;
            border-radius: 15px;
            &:hover {
                background-color: #F2F6FC;
            }
        }

        &.selected span {
            background-color: #0099FF;
            color: #FFF;
            border-radius: 15px;
        }
    }

    th {
        position: relative;
        font-weight: 400;
        color: #606266;
        height: 36px;
        text-align: center;
    }
}

.vd-ui-year-table {
    font-size: 12px;
    border-collapse: collapse;

    .el-icon {
        color: #303133;
    }

    td {
        text-align: center;
        padding: 5px 3px;
        cursor: pointer;

        // 当天对应的年份
        &.today {
            .cell {
                color: #000;
                font-weight: bold;
                // color: #409EFF;
                // border: solid 1px #409eff;
                // border-radius: 3px;
            }
        }

        &.disabled .cell {
            background-color: #F5F7FA;
            cursor: not-allowed;
            color: #999;
        
            &:hover {
                color: #999;
            }
        }
    
        .cell {
            width: 80px;
            height: 36px;
            display: block;
            line-height: 36px;
            color: #333;
            margin: 0 auto;
        
            &:hover {
                background-color: #F5F7FA;
            }
        }
    
        &.current:not(.disabled) .cell {
            color: #fff;
            background: #409eff;
            border-radius: 3px;
        }
    }
    
}

.vd-ui-month-table {
    font-size: 12px;
    border-collapse: collapse;

    td {
        text-align: center;
        padding: 0px;
        cursor: pointer;

        & div {
            height: 48px;
            padding: 5px 0;
            box-sizing: border-box;
        }

        // 当天对应的月份
        &.today {
            .cell {
                color: #000;
                font-weight: bold;
            }
            
            &.start-date .cell,
            &.end-date .cell {
                color: #fff;
            }
        }
    
        &.disabled .cell {
            background-color: #F5F7FA;
            cursor: not-allowed;
            color: #999;
        
            &:hover {
                color: #999;
            }
        }
    
        .cell {
            width: 58px;
            height: 36px;
            display: block;
            line-height: 36px;
            color: #333;
            margin: 0 auto;
            border-radius: 3px;
            &:hover {
                background: #F5F7FA;
            }
        }
    
        &.in-range div {
            background-color: #F2F6FC;
            &:hover {
                background-color: #F2F6FC;
            }
        }
        &.start-date div,
        &.end-date div {
            color: #fff;
        }
    
        &.start-date .cell,
        &.end-date .cell {
            color:#FFF;
            background-color:#409EFF;
        }
    
        &.start-date div {
            border-top-left-radius: 24px;
            border-bottom-left-radius: 24px;
        }
    
        &.end-date div {
            border-top-right-radius: 24px;
            border-bottom-right-radius: 24px;
        }
    
        &.current:not(.disabled) .cell {
            background: #409EFF;
            border-radius: 3px;
            color: #fff;
        }
    }

}

.vd-ui-time-panel {
    width: 180px;

    // 头
    .vd-ui-time-panel__header {
        height: 46px;
        font-size: 14px;
        color: @Text-4;
        line-height: 46px;
        text-align: center;
        border-bottom: solid 1px #E1E5E8;
    }

    // 滚动区域
    .vd-ui-time-panel__content {
        border-bottom: solid 1px #E1E5E8;

        .spinner-list {
            font-size: 0;
            .spinner-item {
                display: inline-block;
                vertical-align: top;
                width: 33.32%;
                height: 264px;
                overflow: hidden;
                border-right: solid 1px #E1E5E8;
                &:last-child {
                    border-right: none;
                }

                &>ul {
                    list-style: none;
                    margin: 0;
                    padding: 0;
                    height: 100%;
                    overflow-y: auto;
                    padding-right: 6px;

                    &>li {
                        position: relative;
                        font-size: 14px;
                        text-align: center;
                        height: 33px;
                        line-height: 33px;
                        padding-left: 6px;
                        cursor: pointer;
                        &:hover {
                            background: #edf0f2;
                        }

                        &.active {
                            color: #09f;
                        }
                    }

                    &::-webkit-scrollbar {
                        width: 0px;
                        height: 0px;
                    }
                    &::-webkit-scrollbar-track {
                        background: transparent;
                        width: 0;
                        height: 0;
                    }
                    &::-webkit-scrollbar-thumb {
                        width: 0;
                        height: 0;
                    }
                }
                // 滚动条样式
                &>ul:hover {
                    padding-right: 0;
                    &::-webkit-scrollbar {
                        width: 6px;
                        height: 6px;
                    }
                    &::-webkit-scrollbar-track {
                        background: transparent;
                        width: 6px;
                        height: 6px;
                    }
                    &::-webkit-scrollbar-thumb {
                        background: #D7DADE;
                        width: 6px;
                        height: 6px;
                        border-radius: 3px;
                        &:hover {
                            background: #BABFC2;
                        }
                        &:active {
                            background: #969B9E;
                        }
                    }
                }
            }
        }
    }

    // 脚
    .vd-ui-time-panel__footer {
        height: 40px;
        padding: 0 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .time-cancel {
            border: none;
            background: none;
            font-size: 12px;
            color: @Blue-6;
            cursor: pointer;
        }
        .time-confirm {
            width: 44px;
            height: 26px;
            border: none;
            background: @Blue-6;
            border-radius: 3px;
            font-size: 12px;
            color: @Text-7;
            cursor: pointer;
        }
    }
}

.vd-ui-datetime-panel {
    width: 474px;
        box-sizing: border-box;

    * {
        box-sizing: border-box;
    }

    .datetime-content {
        display: flex;

        .datetime-date-panel {
            width: 292px;
            border-right: solid 1px #E1E5E8;
        }
    
        .datetime-time-panel {
            width: 180px;
        }
    }

    .datetime-btn {
        width: calc(100% - 2px);
        height: 40px;
        padding: 0 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-top: solid 1px #edf0f2;

        .time-cancel {
            border: none;
            background: none;
            font-size: 12px;
            color: @Blue-6;
            cursor: pointer;
        }
        .time-confirm {
            width: 44px;
            height: 26px;
            border: none;
            background: @Blue-6;
            border-radius: 3px;
            font-size: 12px;
            color: @Text-7;
            cursor: pointer;
        }
    }
}


// 范围
// 范围回显框
.vd-ui-range {
    width: 252px;
    height: 30px;
    border: solid 1px #BABFC2;
    border-radius: 3px;
    padding: 3px 10px;
    box-sizing: border-box;

    display: inline-flex;
    align-items: center;

    .icon-date {
        font-size: 16px;
        color: #666;
        margin-right: 4px;
    }
    .range-error2 {
        width: 16px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;

        .icon-error2 {
            font-size: 16px;
            color: #666;
            cursor: pointer;
            &:hover {
                color: #333;
            }
        }
    }
    .split {
        font-size: 12px;
        color: #333;
        margin: 0 5px;
    }
    .input-box {
        flex: 1;
        height: 100%;
        cursor: pointer;

        input {
            appearance: none;
            border: none;
            outline: none;
            display: inline-block;
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            font-size: 12px;
            color: #333;
            text-align: center;
            cursor: pointer;
            transition: border-color .1s linear;

            &::placeholder {
                color: #999;
                font-size: 12px;
            }

            &:hover {
                border-color: #969B9E;
            }
        }
    }
}
.vd-ui-daterange-panel {
    * {
        box-sizing: border-box;
    }

    .vd-ui-panel__body-wrapper {
        position: relative;

        // 快捷时间
        .vd-ui-panel__sidebar {
            position: absolute;
            top: 0;
            bottom: 0;
            width: 100px;
            border-right: 1px solid #e4e4e4;
            box-sizing: border-box;
            padding-top: 15px;
            background-color: #fff;
            overflow: auto;

            .vd-ui-panel__shortcut {
                display: block;
                padding-left: 12px;
                width: 100%;
                background-color: transparent;
                border: 0;
                font-size: 12px;
                color: #09F;
                text-align: left;
                margin-bottom: 15px;
                outline: none;
                cursor: pointer;
            }
        }

        // 日期面板
        .vd-ui-panel__body {

            .el-picker-panel__content {
                float: left;
                width: 50%;
                box-sizing: border-box;
                margin: 0;

                &.is-left {
                    border-right: solid 1px #edf0f2;
                }

                .el-date-range-picker__header {
                    position: relative;
                    background: #fff;
                    height: 46px;
                    color: #333;
                    margin: 0 auto;
                    padding: 0;
                    border-bottom: solid 1px #ddd;

                    .header-label {
                        margin: 0 50px;
                        text-align: center;
                        line-height: 46px;
                    }

                    &>.arrow {
                        font-size: 12px;
                        color: #333;
                        border: 0;
                        background: 0 0;
                        cursor: pointer;
                        outline: none;
                        position: absolute;
                        top: 50%;
                        transform: translateY(-50%);

                        &.arrow1 {
                            left: 10px;
                            transform: translateY(-50%) rotate(-90deg);
                            margin-top: -1px;
                        }
                        &.arrow2 {
                            left: 30px;
                        }
                        &.arrow3 {
                            right: 30px;
                        }
                        &.arrow4 {
                            right: 10px;
                            transform: translateY(-50%) rotate(90deg);
                            margin-top: -1px;
                        }
                        &.disable {
                            color: #ccc;
                            cursor: not-allowed;
                        }
                    }

                    ul {
                        margin: 0 auto;
                        width: 100%;
                        height: 46px;
                        list-style: none;
                        padding: 0;
                        margin: 0;
                        display: flex;
                        align-items: center;

                        li {
                            cursor: pointer;

                            &.year-month {
                                flex: 1;
                                display: flex;
                                justify-content: center;
                                align-items: center;
                            }

                            &.arrow {
                                width: 30px;
                                text-align: center;
                                color: #666;
                                i {
                                    display: block;
                                    font-size: 16px;
                                    color: #666;
                                }

                                &.arrow-left1 i {
                                    transform: rotate(-90deg);
                                }
                                &.arrow-right1 i {
                                    transform: rotate(90deg);
                                }
                            }
                        }
                    }
                }
            }

            &::after {
                content: "";
                display: block;
                clear: both;
            }
        }
    }
}

.vd-ui-date-range-wrap {
    display: flex;
    align-items: center;

    .vd-ui-date-range-gap {
        padding: 0 5px;
        display: flex;
        align-items: center;
    }
}
