function getCommunicateRecord(data) {
    return axios({
        url: '/communicateRecord/page.do',
        method: 'post',
        data: data,
        headers: {
            'Content-Type': 'application/json'  //如果写成contentType会报错,如果不写这条也报错
        }
    })

}

function addCommunicateRecord(data) {
    return axios({
        url: '/communicateRecord/add.do',
        method: 'post',
        data: data
    })
}

function getCommunicateRecordToday(data) {
    return axios({
        url: '/communicate/getCommunicateRecordToday.do',
        method: 'post',
        params: data
    })
}

function getAiInfo(data) {
    return axios({
        url: '/communicate/getAiInfo.do',
        method: 'post',
        params: data
    })
}

function getSummary(data) {
    return axios({
        url: '/communicate/summary.do',
        method: 'post',
        params: data
    })
}


function addAiCommunicateRecord(data) {
    return axios({
        url: '/communicate/addCommunicateRecord.do',
        method: 'post',
        data: data
    })
}

function createBusinessChange(data) {
    return axios({
        url: '/communicate/createBusinessChange.do',
        method: 'post',
        data: data
    })
}

function noCreateBusinessChange(data) {
    return axios({
        url: '/communicate/noCreateBusinessChange.do',
        method: 'post',
        data: data
    })
}

function updateTraderSign(data) {
    return axios({
        url: '/communicate/updateTraderSign.do',
        method: 'post',
        data: data
    })
}

function noUpdateTraderSign(data) {
    return axios({
        url: '/communicate/noUpdateTraderSign.do',
        method: 'post',
        data: data
    })
}

function syncContactPosition(data) {
    return axios({
        url: '/communicate/syncContactPosition.do',
        method: 'post',
        data: data
    })
}

