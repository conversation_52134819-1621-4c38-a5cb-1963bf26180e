package com.vedeng.infrastructure.constants;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: rabbitMQ队列, 交换机 常量
 * @date 2021/12/11 17:42
 */
public class RabbitMqConstant {


    /**
     * 电子签章rabbit交换机
     */
    public static final String ELECTRONIC_SIGN_CHANGE = "esignBaseCallbackDirectExchange";
    /**
     * 电子签章队列
     */
    public static final String ELECTRONIC_SIGN_QUEUE = "esignBaseCallbackQueue";

    /**
     * 订单确认交换机
     */
    public static final String ORDER_CONFIRM_TO_ERP_EXCHANGE = "orderConfirmToErpExchange";
    /**
     * 订单确认队列
     */
    public static final String ORDER_CONFIRM_TO_ERP_QUEUE = "confirmStatusQueue";


    /**
     * 签收单确认交换机
     */
    public static final String SING_IN_FORM_TO_ERP_EXCHANGE = "singInFormToErpExchange";
    /**
     * 签收单确认队列
     */
    public static final String SING_IN_FORM_TO_ERP_QUEUE = "singInFormToErpQueue";

}
