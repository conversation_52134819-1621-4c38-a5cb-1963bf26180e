<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>采购到货时长</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="bQQE7on9eww" datasource="erp-datasourcetarget" fixednumber="2" fixednumberright="0" append_column_url="" append_row_url="" empty_show="" firstcol="">
<div id="appendHead"></div>
<div class="layui-fluid">
    <div class="layui-tab">
        <ul class="layui-tab-title" id="tab">
        </ul>
    </div>
    <form class="layui-form" id="search">
        <div class=" layui-inline "><label class="layui-form-label">订货号</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="SKU_NO" placeholder="" style="" alias="s" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" validate_rules="" validate_messages=""></input>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">商品名称</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="SKU_NAME" placeholder="" style="" alias="s" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" validate_rules="" validate_messages=""></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">审核状态</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="CHECK_STATUS" placeholder="" style="" alias="s" jdbctype="" data="[{&quot;V&quot;:&quot;待完善&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;待提交审核&quot;,&quot;K&quot;:&quot;5&quot;}]" datatype="JSON" oper="" validate_rules="" validate_messages=""></select>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">归属助理</label>
            <div class="layui-input-inline"><select class=" layui-input list-search-item " type="search" name="ASSIGNMENT_ASSISTANT_ID" placeholder="" style="" alias="p" jdbctype="" data="select distinct U.USER_ID as `K`,U.USERNAME as `V` from T_USER U LEFT JOIN T_R_USER_ROLE RB ON U.USER_ID=RB.USER_ID 	LEFT JOIN T_ROLE R ON RB.ROLE_ID=R.ROLE_ID 	WHERE R.ROLE_NAME IN ('产品专员','产品主管','产品总监') 	AND U.IS_DISABLED='0' 	ORDER BY USERNAME" datatype="KVSQLCACHE" oper="" validate_rules="" validate_messages=""></select>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">归属经理</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="ASSIGNMENT_MANAGER_ID" placeholder="" style="" alias="p" jdbctype="" data="select distinct U.USER_ID as `K`,U.USERNAME as `V` from T_USER U LEFT JOIN T_R_USER_ROLE RB ON U.USER_ID=RB.USER_ID 	LEFT JOIN T_ROLE R ON RB.ROLE_ID=R.ROLE_ID 	WHERE R.ROLE_NAME IN ('产品专员','产品主管','产品总监') 	AND U.IS_DISABLED='0' 	ORDER BY USERNAME" datatype="KVSQLCACHE" oper="" validate_rules="" validate_messages=""></select>
            </div>
        </div>
    </form>
    <hr class="layui-border-blue">
    <div class="btn-group   bd-highlight" id="tableButton"><button item_name="批量修改" name="批量修改" url="/goods/vgoods/uploadSkuDeliveryRange.do" opentype="MODEL" windowname="批量修改" style type="table">批量修改</button><button item_name="导出所有" name="导出所有" url="/ezadmin/list/export-bQQE7on9eww" opentype="APPEND_PARAM" windowname="导出所有" style type="table">导出所有</button>
    </div>
    <table id="table" class="layui-table" style=" width:100%">
        <thead>
        <tr id="column">
            <th type="rowbutton" id="rowbutton"><button item_name="编辑" name="编辑" url="/ezadmin/form/form-oLNICEVGvuM?ID=${ID}" opentype="MODEL" windowname="编辑" style area type="single">编辑</button></th>
            <th item_name="ID" name="ID" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">ID</th>
            <th item_name="SKU_NO" name="SKU_NO" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">订货号</th>
            <th item_name="SKU_NAME" name="SKU_NAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">商品名称</th>
            <th item_name="PURCHASE_TIME" name="PURCHASE_TIME" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">采购到货时长（工作日）</th>
            <th item_name="PURCHASE_TIME_UPDATE_TIME" name="PURCHASE_TIME_UPDATE_TIME" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">采购到货时长最近更新时间</th>
            <th item_name="NAMES" name="NAMES" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">归属</th>
            <th item_name="CHECK_STATUS" name="CHECK_STATUS" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;待完善&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;待提交审核&quot;,&quot;K&quot;:&quot;5&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">审核状态</th>
            <th item_name="IS_DIRECT" name="CHECK_STATUS" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;普发&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;直发&quot;,&quot;K&quot;:&quot;1&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">发货方式</th>
            <th item_name="GOODSLEVEL" name="GOODSLEVEL" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">商品等级</th>
            <th item_name="STATUS" name="STATUS" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;已禁用&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;已启用&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">启用状态</th>
            <th item_name="POSITION_NAME" name="POSITION_NAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">商品档位</th>
            <th item_name="SPU_TYPE" name="SPU_TYPE" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;设备&quot;,&quot;K&quot;:&quot;316&quot;},{&quot;V&quot;:&quot;耗材&quot;,&quot;K&quot;:&quot;317&quot;},{&quot;VALUE&quot;:&quot;试剂&quot;,&quot;K&quot;:&quot;318&quot;},{&quot;V&quot;:&quot;其他&quot;,&quot;K&quot;:&quot;319&quot;},{&quot;V&quot;:&quot;高值耗材&quot;,&quot;K&quot;:&quot;653&quot;},{&quot;V&quot;:&quot;配件&quot;,&quot;K&quot;:&quot;1008&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">商品类型</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td> express:</td>
            <td colspan="100"><pre id="express" class="layui-code" orderby="" groupby="">SELECT
        s.SKU_ID ID,
	s.SKU_NO,
	s.SKU_NAME sku_name,
	s.PURCHASE_TIME,
	s.PURCHASE_TIME_UPDATE_TIME,
                s.IS_DIRECT,
	p.SPU_ID,
	 p.ASSIGNMENT_MANAGER_ID manager_name,
	p.ASSIGNMENT_ASSISTANT_ID assistant_name,
	concat( u1.username, '&amp;', u2.username ) AS NAMES,
	concat(gl.UNIQUE_IDENTIFIER,'-',gl.LEVEL_NAME) goodsLevel,
	gp.POSITION_NAME,
	p.SPU_TYPE,
	s.CHECK_STATUS*1 CHECK_STATUS ,
	s.STATUS*1  STATUS
FROM
	V_CORE_SKU s
	LEFT JOIN V_GOODS_POSITION gp ON gp.ID = s.GOODS_POSITION_NO
	LEFT JOIN V_GOODS_LEVEL gl ON gl.ID = s.GOODS_LEVEL_NO
	left  JOIN V_CORE_SPU p ON s.SPU_ID = p.SPU_ID
LEFT JOIN T_USER u1 ON u1.user_id = p.ASSIGNMENT_MANAGER_ID
	LEFT JOIN T_USER u2 ON u2.user_id = p.ASSIGNMENT_ASSISTANT_ID
WHERE 1 = 1</pre></td>
        </tr>
        <tr>
            <td> count:</td>
            <td colspan="100"><pre id="count" class="layui-code"></pre></td>
        </tr>
        </tbody>
    </table>
</div>
<div id="appendFoot"></div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    })
</script>
</body>
</html>