package com.vedeng.erp.saleorder.api;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.erp.saleorder.dto.QuoteLinkOrderDto;
import com.vedeng.erp.saleorder.dto.QuoteToOrderDto;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.erp.saleorder.dto.TerminalQueryDto;
import com.vedeng.erp.saleorder.service.OrderTerminalService;
import com.vedeng.erp.saleorder.service.QuoteToOrderApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@ExceptionController
@RestController
@RequestMapping("/api/quoteToOrder")
public class QuoteToOrderApi {

    @Autowired
    private QuoteToOrderApiService quotesToOrderApiService;

    /**
     * 报价转化为销售订单
     */
    @RequestMapping(value = "/convert", method = {RequestMethod.POST})
    @NoNeedAccessAuthorization
    public R<Integer> convert(@RequestBody QuoteToOrderDto quoteToOrderDto) {
        return R.success(quotesToOrderApiService.convert(quoteToOrderDto));
    }

    /**
     * 报价关联销售订单
     */
    @RequestMapping(value = "/link", method = {RequestMethod.POST})
    @NoNeedAccessAuthorization
    public R<?> link(@RequestBody QuoteLinkOrderDto quoteLinkOrderDto) {
        quotesToOrderApiService.link(quoteLinkOrderDto);
        return R.success();
    }

    /**
     * 查询商机关联的销售单号
     */
    @RequestMapping(value = "/getSaleOrder", method = {RequestMethod.POST})
    @NoNeedAccessAuthorization
    public R<SaleorderInfoDto> getSaleOrder(@RequestBody QuoteLinkOrderDto quoteLinkOrderDto) {
        return R.success(quotesToOrderApiService.getSaleOrder(quoteLinkOrderDto));
    }

    /**
     * 报价是否已生成销售订单
     */
    @RequestMapping(value = "/isExistSaleOrder", method = {RequestMethod.POST})
    @NoNeedAccessAuthorization
    public R<Integer> isExistSaleOrder(@RequestBody QuoteLinkOrderDto quoteLinkOrderDto) {
        return R.success(quotesToOrderApiService.isExistSaleOrder(quoteLinkOrderDto));
    }
    
}
