package com.vedeng.erp.saleorder.model.dto.ext;

import com.vedeng.erp.saleorder.model.dto.BaseDataInfoDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = false)
public class BusinessChanceDateInfo extends BaseDataInfoDto {

    /**
     * 当月五行信息
     */
    private  Integer sort;

    /**
     * 商机总额
     */
    private BigDecimal totalBussinessAmount;

    /**
     * 商机数量
     */
    private Integer totalBussinessNum;

    /**
     * 昨日新增商机金额
     */
    private BigDecimal yesterdayTotalBussinessAmount;

    /**
     * 昨日新增商机数量
     */
    private Integer yesterdayTotalBussinessNum;

    /**
     * 预计当月到款
     */
    private BigDecimal thisMouthTotalAmount;

    /**
     * 预计当月到款商机数
     */
    private Integer thisMouthBussinessNum;

}
