package com.vedeng.erp.system.web.api;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.system.dto.RoleUserCategoryConfigDto;
import com.vedeng.erp.system.service.RoleUserCategoryConfigApiService;
import com.vedeng.erp.system.vo.BusinessUserVO;
import com.vedeng.erp.system.vo.RoleUserCategoryConfigQueryVO;
import com.vedeng.erp.system.vo.RoleUserCategoryConfigVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 人员与商品分类配置Controller
 */
@ExceptionController
@RestController
@RequestMapping("/system/role-user-category-config")
@Slf4j
public class RoleUserCategoryConfigApi {

    @Autowired
    private RoleUserCategoryConfigApiService roleUserCategoryConfigApiService;

    /**
     * 分页查询人员与商品分类配置
     */
    @RequestMapping("/page")
    public R<PageInfo<RoleUserCategoryConfigVO>> page(@RequestBody PageParam<RoleUserCategoryConfigQueryVO> pageParam) {
        return R.success(roleUserCategoryConfigApiService.pageRoleUserCategoryConfig(pageParam));
    }

    /**
     * 获取人员与商品分类配置详情
     */
    @RequestMapping("/get")
    public R<RoleUserCategoryConfigDto> getInfo(@RequestParam("id") Long id) {
        return R.success(roleUserCategoryConfigApiService.getById(id));
    }

    /**
     * 新增人员与商品分类配置
     */
    @RequestMapping("/add")
    public R<Boolean> add(@RequestBody RoleUserCategoryConfigDto dto) {
        return R.success(roleUserCategoryConfigApiService.saveRoleUserCategoryConfig(dto));
    }

    /**
     * 修改人员与商品分类配置
     */
    @RequestMapping("/update")
    public R<Boolean> update(@RequestBody RoleUserCategoryConfigDto dto) {
        return R.success(roleUserCategoryConfigApiService.updateRoleUserCategoryConfig(dto));
    }

    /**
     * 删除人员与商品分类配置
     */
    @RequestMapping("/delete")
    public R<Boolean> remove(@RequestParam("id") Long id) {
        return R.success(roleUserCategoryConfigApiService.deleteRoleUserCategoryConfig(id));
    }

    /**
     * 批量删除人员与商品分类配置
     */
    @RequestMapping("/batch-delete")
    public R<Boolean> batchRemove(@RequestBody List<Long> ids) {
        return R.success(roleUserCategoryConfigApiService.batchDeleteRoleUserCategoryConfig(ids));
    }

    /**
     * 获取所有业务人员信息
     */
    @RequestMapping("/all-business-users")
    public R<List<BusinessUserVO>> getAllBusinessUsers(@RequestParam(value = "userName", required = false) String userName) {
        return R.success(roleUserCategoryConfigApiService.getAllBusinessUsers(userName));
    }
} 