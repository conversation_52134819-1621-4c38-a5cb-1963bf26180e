
// address组件
.vd-ui-address {

    .vd-ui-address-panel {
        height: calc(100% - 53px);

        .address-choose-wrap {
            height: 100%;
    
            .vd-ui-address-nav {
                padding: 0 10px;
                border-bottom: solid 1px #EBEFF2;
                display: flex;
    
                .nav-item {
                    position: relative;
                    height: 40px;
                    line-height: 40px;
                    font-size: 14px;
                    color: #999;
                    margin-right: 30px;
                    max-width: 90px;
    
                    &.active {
                        font-weight: 700;
                        color: #09f;
    
                        &::after {
                            content: "";
                            display: block;
                            border-bottom: solid 3px #09f;
                            position: absolute;
                            bottom: 0;
                            left: 0;
                            right: 0;
                        }
                    }
                }
            }
    
            .address-choose-panel {
                max-height: calc(100% - 41px);
                overflow-y: auto;

                .ap-list {
    
                    .ap-item {
                        padding: 10px;
                        font-size: 12px;
                        color: #000;
                        display: flex;
                        align-items: center;
                        border-bottom: solid 1px #F5F7FA;

                        &:last-child {
                            border: none;
                        }
    
                        .selected {
                            height: 16px;
                            line-height: 16px;
                            margin-right: 5px;

                            > i {
                                font-size: 16px;
                                color: #ccc;
                            }
                        }
    
                        &.active {
                            background: #E0F3FF;
    
                            .selected i {
                                color: #09f;
                            }
                        }
                    }
                }
            }
        }

        .address-search-wrap {
            height: 100%;

            .filter-list {
                // max-height: calc(100% - 41px);
                max-height: 100%;
                overflow-y: auto;
    
                .filter-item {
                    padding: 0 10px;
                    height: 33px;
                    line-height: 33px;
                    display: flex;
                    align-items: center;
                    cursor: pointer;

                    .filter-selected {
                        width: 16px;
                        height: 16px;
                        line-height: 16px;
                        margin-right: 5px;

                        > i {
                            height: 16px;
                            font-size: 16px;
                            color: #ccc;

                            &.icon-radio3 {
                                color: #09f;
                                display: none;
                            }
                        }
                    }

                    &.active {
                        background: #E0F3FF;
    
                        .filter-selected > i {
                            &.icon-radio3 {
                                display: block;
                            }
                            &.icon-radio1 {
                                display: none;
                            }
                        }
                    }
                }
            }

            .no-filter {
                padding: 5px 0;
                color: #999;
                text-align: center;
            }
        }
    }
}


// form-address组件
.ui-form-address-wrap {

    .form-address-view {
        padding-left: 10px;

        .form-address-show {
            display: flex;
            align-items: center;

            .value {
                flex: 1;
                min-width: 0;
            }

            .icon-error2 {
                width: 36px;
                height: 38px;
                flex-shrink: 0;
                font-size: 16px;
                color: #666;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }
    }
}



// 表单地址弹层
.form-address-panel {
    height: calc(100vh - 45px - 53px);
    height: calc(100vh - 98px - constant(safe-area-inset-bottom));
    height: calc(100vh - 98px - env(safe-area-inset-bottom));

    .panel-inner {
        height: calc(100% - 53px);
    }
}
