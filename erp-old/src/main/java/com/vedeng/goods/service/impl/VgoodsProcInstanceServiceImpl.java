package com.vedeng.goods.service.impl;

import com.vedeng.activiti.ProcessInstanceContext;
import com.vedeng.activiti.ProcessSupport;
import com.vedeng.activiti.constants.ProcessConstants;
import com.vedeng.activiti.model.HistoryVerfiyRecord;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.goods.manager.constants.GoodsValidConstants;
import com.vedeng.goods.service.VgoodsProcInstanceService;
import com.vedeng.system.service.UserService;
import com.vedeng.system.service.VerifiesRecordService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.task.IdentityLink;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @Description com.vedeng.goods.service.impl
 * @Date 2021/8/31 11:01
 */

@Service
public class VgoodsProcInstanceServiceImpl extends ProcessSupport implements VgoodsProcInstanceService {


    @Resource
    private UserService userService;


    @Autowired
    public VgoodsProcInstanceServiceImpl(ProcessEngine processEngine, VerifiesRecordService verifiesRecordService) {
        super(processEngine, verifiesRecordService);
    }

    @Override
    protected String getProcessDefinitionPrefixKey() {
        return ProcessConstants.APPLY_DISABLE_GOODS_PROC_KEY;
    }



    @Override
    public void disableGoodsApplyVerify(Integer relatedId, String disableReason,Integer userId,Integer goodsType) {
        User assignee = checkCurrentAssignee(userId);
        ProcessInstanceContext processInstanceContext = new ProcessInstanceContext();
        processInstanceContext.setProcessDefinitionKey(getProcessDefinitionPrefixKey());
        processInstanceContext.setAssignee(assignee.getUsername());
        processInstanceContext.setRelatedId(relatedId);
        processInstanceContext.setComment(disableReason);
        processInstanceContext.set("goodsType",goodsType);
        if (GoodsValidConstants.SKU_TYPE.equals(goodsType)){
            processInstanceContext.setRelateTable("V_CORE_SKU");
        }
        if (GoodsValidConstants.SPU_TYPE.equals(goodsType)){
            processInstanceContext.setRelateTable("V_CORE_SPU");
        }
        if (processInstanceContext.getBusinessKey() == null) {
            processInstanceContext.setBusinessKey(generateBusinessKey(relatedId,returnGoodsTypeStr(goodsType)));
        }
        Objects.requireNonNull(processInstanceContext.getRelatedId(), "relatedId is null");
        //开启流程
        Task task = getTaskByBusinessKey(processInstanceContext.getBusinessKey());
        if (task != null) {
            if (logger.isDebugEnabled()) {
                logger.debug("Business Key: {} is already started.", processInstanceContext.getBusinessKey());
            }

            throw new IllegalStateException("Process instance :" + processInstanceContext.getBusinessKey() + " is already started.");
        }
        startProcessInstance(processInstanceContext);

        //默认申请人通过
        Task taskInfo = getTaskByBusinessKey(processInstanceContext.getBusinessKey());
        Map<String, Object> attributes = Collections.singletonMap("verifiesType", ProcessConstants.VERIFY_TYPE_DISABLE_GOODS);

        doCompleteTask(taskInfo, processInstanceContext.getAssignee(), disableReason, attributes, ProcessConstants.CheckStatus.CHECKING.getStatus());


    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void completeTask(Task currentTask, User assignee, Map<String, Object> variables, String comment, Boolean pass) {
        int checkStatus = pass ? ProcessConstants.CheckStatus.CHECKING.getStatus() : ProcessConstants.CheckStatus.REJECTED.getStatus();
        doCompleteTask(currentTask, assignee.getUsername(), comment, variables, checkStatus);
    }

    @Override
    public User checkCurrentAssignee(Integer assigneeId) {
        Objects.requireNonNull(assigneeId, "assignId is null");

        User assignee = userService.getUserById(assigneeId);
        if (assignee == null || CommonConstants.ON.equals(assignee.getIsDisabled())) {

            logger.info("禁用商品时操作人不存在或已被禁用 -  userId: {}", Optional.ofNullable(assignee)
                    .orElseGet(User::new).getUserId());

            throw new IllegalArgumentException("操作人不存在或已被禁用");
        }

        return assignee;
    }

    @Override
    public Map<String, Object> getProvessVars(String taskId){
        return getTaskService().getVariables(taskId);

    }


    @Override
    public Task getDisableGoodsTask(Integer related, Integer goodsType) {
        return getTaskByBusinessKey(generateBusinessKey(related,returnGoodsTypeStr(goodsType)));
    }


    private String returnGoodsTypeStr(Integer goodsType){
        Objects.requireNonNull(goodsType, "goodsType is null");
        String goodsTypeStr="";
        if (GoodsValidConstants.SKU_TYPE.equals(goodsType)){
            goodsTypeStr = "sku";
        }
        if (GoodsValidConstants.SPU_TYPE.equals(goodsType)){
            goodsTypeStr = "spu";
        }
        return  goodsTypeStr;
    }

    @Override
    public List<HistoryVerfiyRecord> getDisableGoodsRecord(Integer related, Integer goodsType, User user) {
        try {
            return getHistoricByBusinessKey(generateBusinessKey(related, returnGoodsTypeStr(goodsType)), user);
        } catch (Exception e) {
            logger.error("获取商品审核记录失败！"+ goodsType + related,e);
        }
        return null;
    }


    @Override
    public Task getDisableGoodsTaskByTaskId(String taskId) {
        return getTaskByTaskId(taskId);
    }

    @Override
    public Boolean isTaskCandidate(String taskId,User user) {

        Boolean flag = Boolean.FALSE;

        if (StringUtils.isBlank(taskId) || Objects.isNull(user)){
            return flag;
        }

        List<IdentityLink> candidateUserList = getIdentityLinksForTaskByTaskId(taskId);

        if(CollectionUtils.isNotEmpty(candidateUserList)){
            for(IdentityLink identityLink:candidateUserList){
                if(identityLink.getUserId().equalsIgnoreCase(user.getUsername())){
                    flag = Boolean.TRUE;
                }

            }
        }
        return flag;
    }
}
