package com.vedeng.erp.system.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 操作日志表
 */
@Getter
@Setter
public class OperationLogEntity extends BaseEntity {
    /**
     * 主键
     */
    private Long operationLogId;

    /**
     * 业务表ID
     */
    private Integer bizId;

    /**
     * 业务名称-中文
     */
    private String bizName;

    /**
     * 业务类型 01线索池 02商机库 03 报价单
     */
    private String bizTypeEnum;

    /**
     * 操作动作-中文
     */
    private String actionTypeEnum;

    /**
     * 日志参数
     */
    private String logParam;

    /**
     * 日志内容
     */
    private String logContent;

    /**
     * 操作时间
     */
    private Date operationTime;

    /**
     * 是否删除 0否 1是
     */
    private Boolean isDelete;

    /**
     * 更新备注
     */
    private String updateRemark;
}