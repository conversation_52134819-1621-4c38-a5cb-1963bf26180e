$(document).ready(function(){
	$('#submit_edit').click(function (e) {
		e.preventDefault();
		if(beforeSubmit()){
			$("#myform").submit();
		}
	});

	var beforeSubmit = function() {
		checkLogin();
		$("input[name='name']").val($("#traderName").val());
		$(".warning").remove();
		$("select").removeClass("errorbor");
		$("input").removeClass("errorbor");
		if($("#bank").val().length>256){
			warnTips("bank","开户银行长度不能超过256字符");
			return false;
		}

		if($("#bankAccount").val()!=''&&$("#bankAccount").val().length>32){
			warnTips("bankAccount","银行帐号长度不能超过32字符");
			return false;
		}

		if($("#bankCode").val()!=''&&$("#bankCode").val().length>32){
			warnTips("bankCode","开户行支付联行号长度不能超过32字符");
			return false;
		}
		var traderSupplierId = $("#traderSupplierId").val();
		if (traderSupplierId == 0) {
			$("#searchTraderName").parent("div").siblings("div").removeClass("none").html("当前公司不存在，请重新选择！");
			return false;
		}
		return true;
	}
});
