package com.vedeng.config;

import org.springframework.batch.core.configuration.annotation.EnableBatchProcessing;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.retry.annotation.EnableRetry;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: Spring配置类
 * @date 2022/9/1 15:01
 */
@ComponentScan(basePackages={"cn.hutool.extra.spring"})
@Configuration
@EnableRetry
@EnableBatchProcessing
@Import({SimpleBatchConfiguration.class,cn.hutool.extra.spring.SpringUtil.class})
public class SpringConfig {
}
