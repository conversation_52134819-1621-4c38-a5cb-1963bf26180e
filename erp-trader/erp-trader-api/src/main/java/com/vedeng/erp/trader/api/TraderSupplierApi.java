package com.vedeng.erp.trader.api;

import com.vedeng.common.core.base.R;
import com.vedeng.erp.trader.command.TraderSupplierCommand;
import com.vedeng.erp.trader.dto.TraderSupplierDto;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * 远程调用供应商
 * <AUTHOR>
 */

@RequestMapping("/trader/supplier")
public interface TraderSupplierApi {


    /**
     * 新增【供应商生产厂商】
     *
     * traderSupplierCommand traderSupplierCommand
     * @return R
     */
    @RequestMapping(value = "/addTraderSupplier",method = RequestMethod.POST)
    R<TraderSupplierDto> addTraderSupplier(@RequestBody TraderSupplierCommand traderSupplierCommand);

}
