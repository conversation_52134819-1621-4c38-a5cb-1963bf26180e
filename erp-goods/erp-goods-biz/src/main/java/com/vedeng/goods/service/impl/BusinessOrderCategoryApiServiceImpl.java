package com.vedeng.goods.service.impl;

import com.vedeng.goods.domain.entity.BusinessOrderCategoryEntity;
import com.vedeng.goods.dto.BusinessOrderCategoryDto;
import com.vedeng.goods.mapper.BusinessOrderCategoryMapper;
import com.vedeng.goods.mapstruct.BusinessOrderCategoryConvertor;
import com.vedeng.goods.service.BusinessOrderCategoryApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 业务单据与商品分类关联服务实现类
 */
@Service
public class BusinessOrderCategoryApiServiceImpl implements BusinessOrderCategoryApiService {

    @Autowired
    private BusinessOrderCategoryMapper businessOrderCategoryMapper;

    @Autowired
    private BusinessOrderCategoryConvertor businessOrderCategoryConvertor;

    @Override
    public BusinessOrderCategoryDto getById(Long id) {
        BusinessOrderCategoryEntity entity = businessOrderCategoryMapper.selectByPrimaryKey(id);
        return businessOrderCategoryConvertor.toDto(entity);
    }

    @Override
    public List<BusinessOrderCategoryDto> getByBusinessIdAndType(Integer businessId, Integer businessType) {
        List<BusinessOrderCategoryEntity> entityList = businessOrderCategoryMapper.findByBusinessIdAndBusinessType(businessId, businessType);
        return businessOrderCategoryConvertor.toDto(entityList);
    }

    @Override
    public boolean save(BusinessOrderCategoryDto dto) {
        // CategoryId为0时不执行新增操作
        if (dto.getCategoryId() != null && dto.getCategoryId() == 0) {
            return false;
        }
        
        // 根据商机id和类型查询是否已存在
        List<BusinessOrderCategoryEntity> existingEntities = businessOrderCategoryMapper.findByBusinessIdAndBusinessType(
            dto.getBusinessId(), dto.getBusinessType());
        
        // 查找是否有相同的分类ID记录
        Optional<BusinessOrderCategoryEntity> existingEntity = existingEntities.stream()
            .filter(entity -> entity.getCategoryId().equals(dto.getCategoryId()))
            .findFirst();
        
        BusinessOrderCategoryEntity entity = businessOrderCategoryConvertor.toEntity(dto);
        
        if (existingEntity.isPresent()) {
            // 已存在记录，执行更新操作
            entity.setId(existingEntity.get().getId());
            return businessOrderCategoryMapper.updateByPrimaryKeySelective(entity) > 0;
        } else {
            // 不存在记录，执行新增操作
            return businessOrderCategoryMapper.insertSelective(entity) > 0;
        }
    }

    @Override
    public boolean batchSave(List<BusinessOrderCategoryDto> dtoList) {
        List<BusinessOrderCategoryEntity> entityList = businessOrderCategoryConvertor.toEntity(dtoList);
        return businessOrderCategoryMapper.batchInsert(entityList) > 0;
    }

    @Override
    public boolean update(BusinessOrderCategoryDto dto) {
        BusinessOrderCategoryEntity entity = businessOrderCategoryConvertor.toEntity(dto);
        return businessOrderCategoryMapper.updateByPrimaryKeySelective(entity) > 0;
    }

    @Override
    public boolean batchUpdate(List<BusinessOrderCategoryDto> dtoList) {
        List<BusinessOrderCategoryEntity> entityList = businessOrderCategoryConvertor.toEntity(dtoList);
        return businessOrderCategoryMapper.updateBatchSelective(entityList) > 0;
    }

    @Override
    public boolean deleteById(Long id) {
        return businessOrderCategoryMapper.deleteByPrimaryKey(id) > 0;
    }

    @Override
    public boolean deleteByBusinessIdAndType(Integer businessId, Integer businessType) {
        return businessOrderCategoryMapper.deleteByBusinessIdAndBusinessType(businessId, businessType) > 0;
    }
}