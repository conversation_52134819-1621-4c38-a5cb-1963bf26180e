<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
	   xmlns:jdbc="http://www.springframework.org/schema/jdbc" xmlns:jee="http://www.springframework.org/schema/jee"
	   xmlns:tx="http://www.springframework.org/schema/tx" xmlns:util="http://www.springframework.org/schema/util"
	   xmlns:task="http://www.springframework.org/schema/task"
	   xsi:schemaLocation="
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.2.xsd
		http://www.springframework.org/schema/jdbc http://www.springframework.org/schema/jdbc/spring-jdbc-3.2.xsd
		http://www.springframework.org/schema/jee http://www.springframework.org/schema/jee/spring-jee-3.2.xsd
		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.2.xsd
		http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-3.2.xsd
		http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.2.xsd">

	<bean id="reportSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
		<property name="dataSource" ref="reportDataSource" />
		<property name="mapperLocations">
			<list>
				<value>classpath:/report/*.xml</value>
			</list>
		</property>
		<property name="configLocation" value="classpath:configuration.xml"/>
	</bean>

	<bean id="reportDataSource" class="com.alibaba.druid.pool.DruidDataSource"  init-method="init" destroy-method="close">
		<property name="url" value="${report_url}" />
		<property name="username" value="${report_username}" />
		<property name="password" value="${report_password}" />
	
		<property name="initialSize" value="0" />		
		<property name="maxActive" value="20" />	

		<property name="minIdle" value="0" />		
		<property name="maxWait" value="60000" />
		
		<property name="validationQuery" value="${validationQuery}" />
		<property name="testOnBorrow" value="false" />
		<property name="testOnReturn" value="false" />
		<property name="testWhileIdle" value="true" />
		<property name="timeBetweenEvictionRunsMillis" value="60000" />
		<property name="minEvictableIdleTimeMillis" value="25200000" />
		<property name="removeAbandoned" value="true" />
		<property name="removeAbandonedTimeout" value="1800" />
		<property name="logAbandoned" value="true" />
		<property name="filters" value="mergeStat" />
	</bean>

	<bean id="reportMapperScannerConfigurer" class="org.mybatis.spring.mapper.MapperScannerConfigurer">
		<property name="sqlSessionFactoryBeanName" value="reportSqlSessionFactory" />
		<property name="basePackage" value="com.report.dao" />
		<property name="annotationClass" value="javax.inject.Named" />
	</bean>

	<bean id="reportTransactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
		<property name="dataSource" ref="reportDataSource" />
	</bean>

	<tx:annotation-driven transaction-manager="reportTransactionManager" proxy-target-class="true" />
</beans>
