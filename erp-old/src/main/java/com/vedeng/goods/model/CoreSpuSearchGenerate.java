package com.vedeng.goods.model;

import java.util.Date;

public class CoreSpuSearchGenerate {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU_SEARCH.SPU_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer spuId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU_SEARCH.CATEGORY_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer categoryId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU_SEARCH.BRAND_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer brandId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU_SEARCH.SPU_NO
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String spuNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU_SEARCH.SPU_NAME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String spuName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU_SEARCH.SHOW_NAME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String showName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU_SEARCH.SPU_LEVEL
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer spuLevel;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU_SEARCH.STATUS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU_SEARCH.SPU_TYPE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer spuType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU_SEARCH.FIRST_ENGAGE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer firstEngageId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU_SEARCH.REGISTRATION_ICON
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String registrationIcon;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU_SEARCH.WIKI_HREF
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String wikiHref;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU_SEARCH.OPERATE_INFO_FLAG
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer operateInfoFlag;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU_SEARCH.CHECK_STATUS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer checkStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU_SEARCH.OPERATE_INFO_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer operateInfoId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU_SEARCH.HOSPITAL_TAGS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String hospitalTags;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU_SEARCH.ADD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Date addTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU_SEARCH.CREATOR
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU_SEARCH.MOD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Date modTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU_SEARCH.UPDATER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer updater;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU_SEARCH.CHECK_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Date checkTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU_SEARCH.CHECKER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer checker;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU_SEARCH.DELETE_REASON
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String deleteReason;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU_SEARCH.LAST_CHECK_REASON
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String lastCheckReason;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU_SEARCH.ASSIGNMENT_MANAGER_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer assignmentManagerId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU_SEARCH.ASSIGNMENT_ASSISTANT_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer assignmentAssistantId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU_SEARCH.SPU_ID
     *
     * @return the value of V_CORE_SPU_SEARCH.SPU_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getSpuId() {
        return spuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU_SEARCH.SPU_ID
     *
     * @param spuId the value for V_CORE_SPU_SEARCH.SPU_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setSpuId(Integer spuId) {
        this.spuId = spuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU_SEARCH.CATEGORY_ID
     *
     * @return the value of V_CORE_SPU_SEARCH.CATEGORY_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getCategoryId() {
        return categoryId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU_SEARCH.CATEGORY_ID
     *
     * @param categoryId the value for V_CORE_SPU_SEARCH.CATEGORY_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU_SEARCH.BRAND_ID
     *
     * @return the value of V_CORE_SPU_SEARCH.BRAND_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getBrandId() {
        return brandId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU_SEARCH.BRAND_ID
     *
     * @param brandId the value for V_CORE_SPU_SEARCH.BRAND_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU_SEARCH.SPU_NO
     *
     * @return the value of V_CORE_SPU_SEARCH.SPU_NO
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getSpuNo() {
        return spuNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU_SEARCH.SPU_NO
     *
     * @param spuNo the value for V_CORE_SPU_SEARCH.SPU_NO
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setSpuNo(String spuNo) {
        this.spuNo = spuNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU_SEARCH.SPU_NAME
     *
     * @return the value of V_CORE_SPU_SEARCH.SPU_NAME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getSpuName() {
        return spuName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU_SEARCH.SPU_NAME
     *
     * @param spuName the value for V_CORE_SPU_SEARCH.SPU_NAME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setSpuName(String spuName) {
        this.spuName = spuName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU_SEARCH.SHOW_NAME
     *
     * @return the value of V_CORE_SPU_SEARCH.SHOW_NAME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getShowName() {
        return showName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU_SEARCH.SHOW_NAME
     *
     * @param showName the value for V_CORE_SPU_SEARCH.SHOW_NAME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setShowName(String showName) {
        this.showName = showName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU_SEARCH.SPU_LEVEL
     *
     * @return the value of V_CORE_SPU_SEARCH.SPU_LEVEL
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getSpuLevel() {
        return spuLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU_SEARCH.SPU_LEVEL
     *
     * @param spuLevel the value for V_CORE_SPU_SEARCH.SPU_LEVEL
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setSpuLevel(Integer spuLevel) {
        this.spuLevel = spuLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU_SEARCH.STATUS
     *
     * @return the value of V_CORE_SPU_SEARCH.STATUS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU_SEARCH.STATUS
     *
     * @param status the value for V_CORE_SPU_SEARCH.STATUS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU_SEARCH.SPU_TYPE
     *
     * @return the value of V_CORE_SPU_SEARCH.SPU_TYPE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getSpuType() {
        return spuType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU_SEARCH.SPU_TYPE
     *
     * @param spuType the value for V_CORE_SPU_SEARCH.SPU_TYPE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setSpuType(Integer spuType) {
        this.spuType = spuType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU_SEARCH.FIRST_ENGAGE_ID
     *
     * @return the value of V_CORE_SPU_SEARCH.FIRST_ENGAGE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getFirstEngageId() {
        return firstEngageId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU_SEARCH.FIRST_ENGAGE_ID
     *
     * @param firstEngageId the value for V_CORE_SPU_SEARCH.FIRST_ENGAGE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setFirstEngageId(Integer firstEngageId) {
        this.firstEngageId = firstEngageId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU_SEARCH.REGISTRATION_ICON
     *
     * @return the value of V_CORE_SPU_SEARCH.REGISTRATION_ICON
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getRegistrationIcon() {
        return registrationIcon;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU_SEARCH.REGISTRATION_ICON
     *
     * @param registrationIcon the value for V_CORE_SPU_SEARCH.REGISTRATION_ICON
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setRegistrationIcon(String registrationIcon) {
        this.registrationIcon = registrationIcon;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU_SEARCH.WIKI_HREF
     *
     * @return the value of V_CORE_SPU_SEARCH.WIKI_HREF
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getWikiHref() {
        return wikiHref;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU_SEARCH.WIKI_HREF
     *
     * @param wikiHref the value for V_CORE_SPU_SEARCH.WIKI_HREF
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setWikiHref(String wikiHref) {
        this.wikiHref = wikiHref;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU_SEARCH.OPERATE_INFO_FLAG
     *
     * @return the value of V_CORE_SPU_SEARCH.OPERATE_INFO_FLAG
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getOperateInfoFlag() {
        return operateInfoFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU_SEARCH.OPERATE_INFO_FLAG
     *
     * @param operateInfoFlag the value for V_CORE_SPU_SEARCH.OPERATE_INFO_FLAG
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setOperateInfoFlag(Integer operateInfoFlag) {
        this.operateInfoFlag = operateInfoFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU_SEARCH.CHECK_STATUS
     *
     * @return the value of V_CORE_SPU_SEARCH.CHECK_STATUS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getCheckStatus() {
        return checkStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU_SEARCH.CHECK_STATUS
     *
     * @param checkStatus the value for V_CORE_SPU_SEARCH.CHECK_STATUS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setCheckStatus(Integer checkStatus) {
        this.checkStatus = checkStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU_SEARCH.OPERATE_INFO_ID
     *
     * @return the value of V_CORE_SPU_SEARCH.OPERATE_INFO_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getOperateInfoId() {
        return operateInfoId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU_SEARCH.OPERATE_INFO_ID
     *
     * @param operateInfoId the value for V_CORE_SPU_SEARCH.OPERATE_INFO_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setOperateInfoId(Integer operateInfoId) {
        this.operateInfoId = operateInfoId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU_SEARCH.HOSPITAL_TAGS
     *
     * @return the value of V_CORE_SPU_SEARCH.HOSPITAL_TAGS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getHospitalTags() {
        return hospitalTags;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU_SEARCH.HOSPITAL_TAGS
     *
     * @param hospitalTags the value for V_CORE_SPU_SEARCH.HOSPITAL_TAGS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setHospitalTags(String hospitalTags) {
        this.hospitalTags = hospitalTags;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU_SEARCH.ADD_TIME
     *
     * @return the value of V_CORE_SPU_SEARCH.ADD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Date getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU_SEARCH.ADD_TIME
     *
     * @param addTime the value for V_CORE_SPU_SEARCH.ADD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU_SEARCH.CREATOR
     *
     * @return the value of V_CORE_SPU_SEARCH.CREATOR
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU_SEARCH.CREATOR
     *
     * @param creator the value for V_CORE_SPU_SEARCH.CREATOR
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU_SEARCH.MOD_TIME
     *
     * @return the value of V_CORE_SPU_SEARCH.MOD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Date getModTime() {
        return modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU_SEARCH.MOD_TIME
     *
     * @param modTime the value for V_CORE_SPU_SEARCH.MOD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setModTime(Date modTime) {
        this.modTime = modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU_SEARCH.UPDATER
     *
     * @return the value of V_CORE_SPU_SEARCH.UPDATER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getUpdater() {
        return updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU_SEARCH.UPDATER
     *
     * @param updater the value for V_CORE_SPU_SEARCH.UPDATER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU_SEARCH.CHECK_TIME
     *
     * @return the value of V_CORE_SPU_SEARCH.CHECK_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Date getCheckTime() {
        return checkTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU_SEARCH.CHECK_TIME
     *
     * @param checkTime the value for V_CORE_SPU_SEARCH.CHECK_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setCheckTime(Date checkTime) {
        this.checkTime = checkTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU_SEARCH.CHECKER
     *
     * @return the value of V_CORE_SPU_SEARCH.CHECKER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getChecker() {
        return checker;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU_SEARCH.CHECKER
     *
     * @param checker the value for V_CORE_SPU_SEARCH.CHECKER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setChecker(Integer checker) {
        this.checker = checker;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU_SEARCH.DELETE_REASON
     *
     * @return the value of V_CORE_SPU_SEARCH.DELETE_REASON
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getDeleteReason() {
        return deleteReason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU_SEARCH.DELETE_REASON
     *
     * @param deleteReason the value for V_CORE_SPU_SEARCH.DELETE_REASON
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setDeleteReason(String deleteReason) {
        this.deleteReason = deleteReason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU_SEARCH.LAST_CHECK_REASON
     *
     * @return the value of V_CORE_SPU_SEARCH.LAST_CHECK_REASON
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getLastCheckReason() {
        return lastCheckReason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU_SEARCH.LAST_CHECK_REASON
     *
     * @param lastCheckReason the value for V_CORE_SPU_SEARCH.LAST_CHECK_REASON
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setLastCheckReason(String lastCheckReason) {
        this.lastCheckReason = lastCheckReason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU_SEARCH.ASSIGNMENT_MANAGER_ID
     *
     * @return the value of V_CORE_SPU_SEARCH.ASSIGNMENT_MANAGER_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getAssignmentManagerId() {
        return assignmentManagerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU_SEARCH.ASSIGNMENT_MANAGER_ID
     *
     * @param assignmentManagerId the value for V_CORE_SPU_SEARCH.ASSIGNMENT_MANAGER_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setAssignmentManagerId(Integer assignmentManagerId) {
        this.assignmentManagerId = assignmentManagerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU_SEARCH.ASSIGNMENT_ASSISTANT_ID
     *
     * @return the value of V_CORE_SPU_SEARCH.ASSIGNMENT_ASSISTANT_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getAssignmentAssistantId() {
        return assignmentAssistantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU_SEARCH.ASSIGNMENT_ASSISTANT_ID
     *
     * @param assignmentAssistantId the value for V_CORE_SPU_SEARCH.ASSIGNMENT_ASSISTANT_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setAssignmentAssistantId(Integer assignmentAssistantId) {
        this.assignmentAssistantId = assignmentAssistantId;
    }
}