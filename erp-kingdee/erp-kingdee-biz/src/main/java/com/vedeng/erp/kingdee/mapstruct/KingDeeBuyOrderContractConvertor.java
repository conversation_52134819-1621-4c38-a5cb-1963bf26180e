package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.kingdee.domain.entity.KingDeeBuyOrderContractEntity;
import com.vedeng.erp.kingdee.dto.KingDeeBuyOrderContractDto;
import org.mapstruct.*;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, builder = @Builder(disableBuilder = true))
public interface KingDeeBuyOrderContractConvertor extends BaseMapStruct<KingDeeBuyOrderContractEntity, KingDeeBuyOrderContractDto> {
    @Mapping(target = "kingDeeBuyorderContractId", source = "kingDeeBuyOrderContractId")
    KingDeeBuyOrderContractEntity toEntity(KingDeeBuyOrderContractDto dto);
}
