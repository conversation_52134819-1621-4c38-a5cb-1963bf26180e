package com.vedeng.common.constant;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: daniel
 * @Date: 2021/2/24 16 02
 * @Description: 推送平台枚举类
 */
public enum  PushPlatformEnum {

    /**
     * 推送贝登平台
     */
    VD(1,"贝登"),

    YXG(2,"医械购"),

    KYG(4,"科研购"),

    J<PERSON>(8,"非公集采"),

    QYSC(16, "区域商城"),

    KYTMH(32, "科研特麦帮")
    ;

    private Integer code;

    private String name;

    PushPlatformEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getNameByCode(Integer code){
        for (PushPlatformEnum item : PushPlatformEnum.values()){
            if (item.getCode().equals(code)){
                return item.getName();
            }
        }
        return null;
    }

    public static List<String> getAllName(){
        List<String> nameList = new ArrayList<>();
        for (PushPlatformEnum item : PushPlatformEnum.values()){
            nameList.add(item.getName());
        }
        return nameList;
    }

    public static Integer getCount(){
        return PushPlatformEnum.values().length;
    }

    public static Integer getValueOfPushedAllPlatform(){
        return (int)Math.pow(2,getCount()) -1;
    }
}
