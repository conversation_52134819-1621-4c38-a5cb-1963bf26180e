package com.vedeng.erp.kingdee.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.finance.dto.BankBillDto;
import com.vedeng.erp.finance.service.BankBillApiService;
import com.vedeng.erp.kingdee.dto.RecBankTradeDetailQueryResult;
import com.vedeng.erp.kingdee.enums.KingDeePullBankBillEnums;
import com.vedeng.erp.kingdee.service.KingDeePullBankBillService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryExtParam;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryFilterDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2023/7/31 14:58
 **/
@Service
@Slf4j
public class KingDeePullBankBillServiceImpl implements KingDeePullBankBillService {

    @Autowired
    private BankBillApiService bankBillApiService;

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Override
    public void doPullKingDeeBankBill(String beginTime,String endTime) {

        for (KingDeePullBankBillEnums x : KingDeePullBankBillEnums.values()) {
            KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
            queryParam.setFormId(KingDeeFormConstant.WB_RecBankTradeDetail);
            List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
            queryFilterDtos.add(KingDeeQueryFilterDto.builder().fieldName("FBANKACNTNO").compare("=").left("(").right(")").value(x.getBankNo()).logic("AND").build());
            queryFilterDtos.add(KingDeeQueryFilterDto.builder().fieldName("FTRANSDATE").compare(">=").left("(").right(")").value(beginTime).logic("AND").build());
            queryFilterDtos.add(KingDeeQueryFilterDto.builder().fieldName("FTRANSDATE").compare("<=").left("(").right(")").value(endTime).logic("AND").build());

            queryParam.setFilterString(queryFilterDtos);

            List<RecBankTradeDetailQueryResult> query = kingDeeBaseApi.query(queryParam, RecBankTradeDetailQueryResult.class);

            if (CollUtil.isEmpty(query)) {
                continue;
            }

            for (RecBankTradeDetailQueryResult recBankTradeDetailQueryResult : query) {
                doAddBankBill(x, recBankTradeDetailQueryResult);
            }

        }


    }

    /**
     * 封装银行流水
     * @param kingDeePullBankBillEnums
     * @param recBankTradeDetailQueryResult
     */
    private void doAddBankBill(KingDeePullBankBillEnums kingDeePullBankBillEnums, RecBankTradeDetailQueryResult recBankTradeDetailQueryResult) {

        boolean in = recBankTradeDetailQueryResult.getFCREDITAMOUNT().compareTo(BigDecimal.ZERO)>0;

        BankBillDto build = BankBillDto.builder().companyId(1).bankTag(kingDeePullBankBillEnums.getBankType())
                .tranFlow(kingDeePullBankBillEnums.getBankType().equals(6) ? recBankTradeDetailQueryResult.getFBNKSEQNO() : recBankTradeDetailQueryResult.getFBUSIREFENO())
                .trandate(DateUtil.parse(recBankTradeDetailQueryResult.getFTOACCOUNTDATE(), DatePattern.UTC_SIMPLE_PATTERN))
                .trantime(DateUtil.parse(recBankTradeDetailQueryResult.getFTRANSDATE(), DatePattern.UTC_SIMPLE_PATTERN))
                .realTrandate(DateUtil.parse(recBankTradeDetailQueryResult.getFTOACCOUNTDATE(),DatePattern.UTC_SIMPLE_PATTERN))
                .realTrandatetime(DateUtil.parse(recBankTradeDetailQueryResult.getFTRANSDATE(),DatePattern.UTC_SIMPLE_PATTERN))
                .message(recBankTradeDetailQueryResult.getFEXPLANATION())
                .amt(in ?recBankTradeDetailQueryResult.getFCREDITAMOUNT() : recBankTradeDetailQueryResult.getFDEBITAMOUNT())
                .amt1(recBankTradeDetailQueryResult.getFBALANCEAMOUNT().toPlainString())
                .flag1(in ? 1 : 0)
                .accno2(recBankTradeDetailQueryResult.getFOppBankAcntNo())
                .accBankno("")
                .accName1(recBankTradeDetailQueryResult.getFOppBankAcntName())
                .cadbankNm(recBankTradeDetailQueryResult.getFOppOpenBankName())
                .status(0)
                .matchedAmount(BigDecimal.ZERO)
                .isConsistency(0)
                .matchedObject(0)
                .isFee(0)
                .havePushedKingdee(0).build();

        BankBillDto existBankBillDto = bankBillApiService.checkTranFlowForCharge(build.getTranFlow(), build.getBankTag());
        if (Objects.isNull(existBankBillDto)){
            log.info("1拉取金蝶银行流水：{}，封装对象：{}", JSON.toJSONString(recBankTradeDetailQueryResult),JSON.toJSONString(build));
            bankBillApiService.add(build);
            return;
        }
        if (KingDeeFormConstant.CORRECT_THE_SERIAL_NUMBER.equals(existBankBillDto.getTranFlow())){
            //流水号添加后缀"_流水号"写入，流水号长度为4
            log.info("2拉取金蝶银行流水：{}，封装对象：{}", JSON.toJSONString(recBankTradeDetailQueryResult),JSON.toJSONString(build));
            BankBillDto bankBillDto0000 = bankBillApiService.selectTranFlowForChargeLastOne(build.getBankTag());
            //判断 build的trandate和bankBillDto0000的trandate是否相等，如果相等，则表示是同一个流水号
            if (bankBillDto0000!=null && DateUtil.isSameDay(build.getTrandate(),bankBillDto0000.getTrandate())){
                 log.info("2.1 拉取金蝶银行流水时，当前日期的效果流水已存在。");
                 return ;
            }
            int sequenceNumber = getMaxSequenceNumber();
            sequenceNumber++;
            String serialNumber = KingDeeFormConstant.CORRECT_THE_SERIAL_NUMBER +"_" + String.format("%04d", sequenceNumber);
            build.setTranFlow(serialNumber);
            bankBillApiService.add(build);
            return;
        }

        if(existBankBillDto.getAmt().compareTo(build.getAmt()) > 0){//如果表里已有的流水金额，大于当前拉取到的金额，则认为非手续费这条已经有了。需要存手续费这条
            build.setTranFlow(build.getTranFlow()+"_1");//手续费的流水号规则
            //判断手续费这条是否有了，也有可能已经存过了
            BankBillDto existBankBillDtoForCharge = bankBillApiService.checkTranFlowForCharge(build.getTranFlow(), build.getBankTag());
            if(existBankBillDtoForCharge ==null){
                log.info("3拉取金蝶银行流水，手续费：{}，封装对象：{}", JSON.toJSONString(recBankTradeDetailQueryResult),JSON.toJSONString(build));
                bankBillApiService.add(build);
            }
        }else if(existBankBillDto.getAmt().compareTo(build.getAmt()) == 0){//如果金额相等，则说明非手续费
            //不需要处理。本地已存在
        }else{//如果表里已有的流水金额，小于当前拉取到的金额，则认为手续费这条已经有了。需要存正常的这条且将原来这条流水的流水号+[_1]上去
            log.info("4拉取金蝶银行流水，手续费：{}，封装对象：{}", JSON.toJSONString(recBankTradeDetailQueryResult),JSON.toJSONString(build));
            bankBillApiService.updateTranFlowForCharge(build.getTranFlow(),build.getBankTag(),build.getTranFlow()+"_1");
            bankBillApiService.add(build);
        }
    }

    private int getMaxSequenceNumber() {
        return bankBillApiService.countSpecialSerialNumbers();
    }
}
