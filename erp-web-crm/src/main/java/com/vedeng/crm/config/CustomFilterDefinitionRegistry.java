package com.vedeng.crm.config;

import com.vedeng.security.FilterDefinitionRegistry;

import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 自定义shion拦截注册器
 * @date 2024/7/20 13:36
 */
public class CustomFilterDefinitionRegistry implements FilterDefinitionRegistry {

    private final LinkedHashMap<String, String> filterDefinitions = new LinkedHashMap<>();

    @Override
    public void addFilterDefinition(String antPath, String definition) {
        filterDefinitions.put(antPath, definition);
    }

    @Override
    public LinkedHashMap<String, String> getAllDefinitions() {
        return filterDefinitions;
    }
}
