package com.vedeng.common.shiro;

import com.alibaba.fastjson.JSON;
import com.ezadmin.common.constants.SessionConstants;
import com.ezadmin.common.enums.DefaultParamEnum;
import com.ezadmin.common.utils.IpUtils;
import com.ezadmin.common.utils.StringUtils;
import com.ezadmin.common.utils.Utils;
import com.vedeng.apollo.ApolloConstant;
import com.vedeng.authorization.model.User;
import com.vedeng.common.controller.Consts;
import com.wms.service.context.ThreadLocalContext;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class CorsFilter implements Filter {

    private final Logger LOG = LoggerFactory.getLogger("visitor");

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;
        //如果没取到apollo配置里的值，则取默认值
        String resourceVersionKey = StringUtils.isBlank(ApolloConstant.getResourceVersionKey())?""+Math.random():ApolloConstant.getResourceVersionKey();
        httpRequest.setAttribute("resourceVersionKey",resourceVersionKey);
        httpRequest.setAttribute("lxcrmUrl",ApolloConstant.getLxcrmUrl());

        //给op用
        if (!httpRequest.getRequestURI().contains("doLogin")
                && !httpRequest.getRequestURI().contains("adkorder")
                && !httpRequest.getRequestURI().contains("newerp.vedeng.com")
                && !httpRequest.getRequestURI().contains("checkSession")
        ) {
            long start = System.currentTimeMillis();
            Map<String,Object> param = new HashMap<>();
            try {
                Utils.initRequestUrl(httpRequest.getRequestURL().toString());
                filterChain.doFilter(httpRequest, response);
                param =requestToMap(httpRequest);
            } finally {
                Utils.clearLog();
                LOG.info("erp:::end：{}ms \t{}\t{}-{}\t{}" ,(System.currentTimeMillis() - start),  httpRequest.getRequestURL(),
                        param.size(), param,IpUtils.getRealIp(httpRequest)
                );
                ThreadLocalContext.removeAll();
            }
            return;
        }
        String originHeader = httpRequest.getHeader("Origin");
        if (null != originHeader && (originHeader.contains(".ivedeng.com")
                || originHeader.contains(".vedeng.com") || originHeader.contains(".vedeng.com.cn"))) {
            response.setHeader("Access-Control-Allow-Origin", httpRequest.getHeader("Origin"));
            response.setHeader("Access-Control-Allow-Credentials", "true");
        } else {
            response.setHeader("Access-Control-Allow-Origin", "*");
        }
        response.setHeader("Access-Control-Allow-Methods", "GET, HEAD, POST, PUT, DELETE, TRACE, OPTIONS, PATCH");
        response.setHeader("Access-Control-Allow-Headers", "Authorization,Content-Type,X-Requested-With,version,gomanager,token,formtoken,source");
        response.setHeader("Access-Control-Max-Age", "3600");
        if ("OPTIONS".equalsIgnoreCase(httpRequest.getMethod())) {
            response.setStatus(HttpServletResponse.SC_OK);
        } else {
            filterChain.doFilter(httpRequest, response);
        }
    }
    protected Map<String,Object> requestToMap(HttpServletRequest request){
        Map<String, Object> searchParamsValues = new HashMap<>();
        try{
        request.getParameterMap().forEach((k, v) -> {
            if(v==null){
                return;
            }
            if(v!=null&&v.length==1){
                searchParamsValues.put(k, v[0]);
            }else if(v.length>1){
                searchParamsValues.put(k+"_ARRAY",request.getParameterValues(k));
            }
        });}catch (Exception e){
            log.error("【requestToMap】处理异常",e);
        }
        return searchParamsValues;
    }

    @Override
    public void destroy() {
       // ThreadLocalContext.removeAll();
    }
}
