<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.authorization.dao.PositionMapper">
	<resultMap id="BaseResultMap" type="com.vedeng.authorization.model.Position">
		<id column="POSITION_ID" property="positionId" jdbcType="INTEGER" />
		<result column="ORG_ID" property="orgId" jdbcType="INTEGER" />
		<result column="POSITION_NAME" property="positionName" jdbcType="VARCHAR" />
		<result column="TYPE" property="type" jdbcType="INTEGER" />
		<result column="LEVEL" property="level" jdbcType="INTEGER" />
		<result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
		<result column="CREATOR" property="creator" jdbcType="INTEGER" />
		<result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
		<result column="UPDATER" property="updater" jdbcType="INTEGER" />
	</resultMap>
	<sql id="Base_Column_List">
		POSITION_ID, ORG_ID, POSITION_NAME,TYPE, LEVEL, ADD_TIME, CREATOR,
		MOD_TIME, UPDATER
	</sql>
	<!-- 根据主键查询对应详细信息 -->
	<select id="getPositionByKey" resultMap="BaseResultMap"
		parameterType="com.vedeng.authorization.model.Position">
		select
		<include refid="Base_Column_List" />
		from T_POSITION
		where POSITION_ID = #{positionId,jdbcType=INTEGER}
	</select>
	<!-- delete -->
	<delete id="delete" parameterType="java.lang.Integer">
		delete from
		T_POSITION
		where POSITION_ID = #{positionId,jdbcType=INTEGER}
	</delete>
	<!-- 新增数据保存 -->
	<insert id="insert" parameterType="com.vedeng.authorization.model.Position">
		insert into T_POSITION
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="positionId != null">
				POSITION_ID,
			</if>
			<if test="orgId != null">
				ORG_ID,
			</if>
			<if test="positionName != null">
				POSITION_NAME,
			</if>
			<if test="type != null">
				TYPE,
			</if>
			<if test="level != null" >
		        LEVEL,
		      </if>
			<if test="addTime != null">
				ADD_TIME,
			</if>
			<if test="creator != null">
				CREATOR,
			</if>
			<if test="modTime != null">
				MOD_TIME,
			</if>
			<if test="updater != null">
				UPDATER,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="positionId != null">
				#{positionId,jdbcType=INTEGER},
			</if>
			<if test="orgId != null">
				#{orgId,jdbcType=INTEGER},
			</if>
			<if test="positionName != null">
				#{positionName,jdbcType=VARCHAR},
			</if>
			<if test="type != null">
				#{type,jdbcType=INTEGER},
			</if>
			<if test="level != null" >
		        #{level,jdbcType=INTEGER},
		      </if>
			<if test="addTime != null">
				#{addTime,jdbcType=BIGINT},
			</if>
			<if test="creator != null">
				#{creator,jdbcType=INTEGER},
			</if>
			<if test="modTime != null">
				#{modTime,jdbcType=BIGINT},
			</if>
			<if test="updater != null">
				#{updater,jdbcType=INTEGER},
			</if>
		</trim>
	</insert>
	<!-- 修改职位信息 -->
	<update id="update" parameterType="com.vedeng.authorization.model.Position">
		update T_POSITION
		<set>
			<if test="orgId != null">
				ORG_ID = #{orgId,jdbcType=INTEGER},
			</if>
			<if test="positionName != null">
				POSITION_NAME = #{positionName,jdbcType=VARCHAR},
			</if>
			<if test="type != null">
				TYPE = #{type,jdbcType=INTEGER},
			</if>
			<if test="level != null" >
		        LEVEL = #{level,jdbcType=INTEGER},
		      </if>
			<if test="addTime != null">
				ADD_TIME = #{addTime,jdbcType=BIGINT},
			</if>
			<if test="creator != null">
				CREATOR = #{creator,jdbcType=INTEGER},
			</if>
			<if test="modTime != null">
				MOD_TIME = #{modTime,jdbcType=BIGINT},
			</if>
			<if test="updater != null">
				UPDATER = #{updater,jdbcType=INTEGER},
			</if>
		</set>
		where POSITION_ID = #{positionId,jdbcType=INTEGER}
	</update>

	<!-- 根据部门获取职位 -->
	<select id="getPositByOrgId" resultMap="BaseResultMap"
		parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from T_POSITION
		where ORG_ID = #{orgId,jdbcType=INTEGER}
		order by
		POSITION_ID
	</select>
	<!-- 验证职位名称 -->
	<select id="vailPosition" parameterType="com.vedeng.authorization.model.Position" resultType="int">
		SELECT COUNT(*) FROM T_POSITION 
		<trim prefix="WHERE "  suffix=""> 
			POSITION_NAME = #{positionName} AND ORG_ID = #{orgId}
			<if test="positionId!=null and positionId!=''">
				AND POSITION_ID <![CDATA[ <> ]]> #{positionId}
			</if>
		</trim>
	</select>

	<!-- querylistPage start -->
	<resultMap type="com.vedeng.authorization.model.Position" id="querylistPageMap"
		extends="BaseResultMap">
		<result column="org_name" property="orgName" />
	</resultMap>
	<select id="querylistPage" parameterType="Map" resultMap="querylistPageMap">
		select
		a.POSITION_ID, a.POSITION_NAME, b.ORG_NAME,a.ORG_ID,a.TYPE, a.LEVEL
		from
		T_POSITION a
		left
		join
		T_ORGANIZATION b
		on
		a.ORG_ID = b.ORG_ID
		<where>
			1=1
			<if test="position.positionName != null and position.positionName != ''">
				and a.POSITION_NAME like
				CONCAT('%',#{position.positionName},'%' )
			</if>
			<if test="position.orgId != null and position.orgId != 0">
				and a.ORG_ID = #{position.orgId}
			</if>
			<if test="position.organization.companyId !=null ">
				and b.COMPANY_ID=#{position.organization.companyId}
			</if>
		</where>
		order by POSITION_ID DESC
	</select>
	<!-- querylistPage end -->
	<!-- getPositionByUserId -->
	<select id="getPositionByUserId" resultMap="BaseResultMap"
		parameterType="java.lang.Integer">
		select
		a.POSITION_ID, a.ORG_ID, a.POSITION_NAME,a.TYPE, a.LEVEL,
		c.ORG_NAME
		from T_POSITION a
		left join T_R_USER_POSIT b on a.POSITION_ID=b.POSITION_ID
		left join T_ORGANIZATION c on a.ORG_ID = c.ORG_ID
		where b.USER_ID = #{userId,jdbcType=INTEGER}
		order by a.POSITION_ID
	</select>
	
	<!-- getPositByOrgIds -->
	<select id="getPositByOrgIds" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from T_POSITION
		where ORG_ID in 
		<foreach item="orgId" index="index" collection="list" open="(" separator="," close=")">  
		  #{orgId}  
		</foreach>  
	</select>

	<select id="getPositByOrgIdsByType" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from T_POSITION
		where TYPE =#{type} and ORG_ID in
		<foreach item="orgId" index="index" collection="orgIds" open="(" separator="," close=")">
		  #{orgId}
		</foreach>
	</select>

	<select id="getPositionByUserIdAndCompanyId" resultMap="BaseResultMap"
			parameterType="java.lang.Integer">
		select
		a.POSITION_ID, a.ORG_ID, a.POSITION_NAME,a.TYPE, a.LEVEL,
		c.ORG_NAME
		from T_POSITION a
		left join T_R_USER_POSIT b on a.POSITION_ID=b.POSITION_ID
		left join T_ORGANIZATION c on a.ORG_ID = c.ORG_ID
		where b.USER_ID = #{userId,jdbcType=INTEGER} and c.COMPANY_ID = #{companyId,jdbcType=INTEGER}
		order by a.POSITION_ID
	</select>
    <select id="getOrgListOfUsers" resultType="com.vedeng.authorization.model.Position">
		SELECT
			USER_ID AS ORG_ID,
			GROUP_CONCAT( P.ORG_ID ) AS POSITION_NAME
		FROM
			T_R_USER_POSIT UP
				JOIN T_POSITION P ON UP.POSITION_ID = P.POSITION_ID
		WHERE
			UP.USER_ID IN
			<foreach collection="list" index="index" item="item" open="(" close=")" separator=",">
				#{item}
			</foreach>
		GROUP BY
			USER_ID;
	</select>

	<select id="getOrgListOfAllUsers" resultType="com.vedeng.authorization.model.Position">
		SELECT
		USER_ID AS ORG_ID,
		GROUP_CONCAT( P.ORG_ID ) AS POSITION_NAME
		FROM
		T_R_USER_POSIT UP
		JOIN T_POSITION P ON UP.POSITION_ID = P.POSITION_ID
		GROUP BY
		USER_ID;
	</select>
</mapper>