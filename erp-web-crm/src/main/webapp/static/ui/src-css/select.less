@import (less) '../common.css';

@keyframes loading {
    0% {
        transform : rotate(0deg);
    }
    100% {
        transform : rotate(360deg);
    }
}

.vd-ui-select {
    position: relative;
    width: 300px;
    height: 100%;
    .vd-ui-select-wrapper {
        position: relative;
        width: 100%;
        .icon {
            position: absolute;
            right: 5px;
            font-size: 16px;
            color: @Text-3;
            cursor: pointer;
            pointer-events: none;
            transition: 0.19s;
            top: 8px;
            line-height: 1;
            &.large {
                line-height: 42px;
            }  
            &.small{
                line-height: 26px;
            }  
            &.rotate{
                transform: rotate(180deg);
                transition: 0.22s;
            }
                
        }
        input::-webkit-input-placeholder {
            color: @Text-2;
        }
        .vd-ui-input input{
            cursor: pointer;
        }
    } 
    &-wrapper__disabled {
        .icon {
            color: @Text-2;
        }
    }
    .vd-ui-select-wrapper__error {
        .vd-ui-input input {
            border-color: @Crimson-6 !important;
        }
    }
    &-multiple-wrapper {
        position: relative;
        background-color: @Text-7;
        border: solid 1px @Gray-7;
        border-radius: 3px;
        min-height: 30px;
        box-sizing: border-box;
        transition: border-color .1s linear;
        cursor: pointer;
        padding: 0 10px;
        padding-right: 36px;
        display: flex;
        align-items: center;
        &:hover {
            border-color: @Gray-9;
        }
        &.is-focus{
            border-color: @Brand-6;
            &:hover {
                border-color: @Brand-6;
            }
        }
        .vd-ui-tag {
            max-width: 80%;
            font-size: 0;
            .placeholder {
                margin-top: 4px;
                color: @Text-2;
                font-size: 12px;
            }

            &.has-more {
                .vd-ui-select-tag {
                    max-width: calc( 100% - 38px );
                }
            }
            .vd-ui-select-tag {
                padding-left: 5px;
                padding-right: 24px;

                background-color: @Gray-2;
                font-size: 12px;
                border-radius: 2px;
                border: 1px solid @Gray-4;
                position: relative;
                margin-right: 5px;
                display: inline-block;
                height: 22px;
                box-sizing: border-box;
                max-width: calc( 100% - 5px);
                transform-origin : center center;
                animation: tagAppear 0.19s ease-in;

                &.tag-more {
                    padding-right: 5px;
                }
                &.marginTop {
                    // margin-top: 5px;
                }
                > span {
                    line-height: 20px;
                }
                .tag-text {
                    max-width: 100%;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    display: inline-block;
                    margin-bottom: -5px;
                }
                .tag-icon {
                    padding-right: 5px;
                    cursor: pointer;
                    position: absolute;
                    right: 0;
                    &:hover {
                        i {
                            color: @Blue-6;
                        }
                    }
                    i {
                        font-size: 14px;
                        position: relative;
                        top: 1px;
                        color: @Text-2;
                    }
                }
            }
        }

        .vd-ui-input-multiple {
            flex: 1;
            border: none;
            min-width: 20%;
            height: 26px;
            padding-left: 10px;
            margin-left: -10px;
            border-radius: 3px 0 0 3px;

            &::placeholder {
                font-size: 12px;
            }
        }
        .vd-ui-tag-auto {
            max-width: 100%;
            display: flex;
            flex-wrap: wrap;
            flex: 1;

            .vd-ui-select-tag {
                margin-top: 2px;
            }
            .vd-ui-input-multiple {
                line-height: 26px;
                font-size: 12px;
            }
        }
        
        .vd-ui-readonly {
            max-width: 100%;
            flex: 1;
        }
        .mul-icon {
            font-size: 16px;
            position: absolute;
            right: 10px;
            transition: 0.19s;
            &.icon-error2 {
                color: @Text-3;
            }
            &.rotate {
                transform: rotate(180deg);
                transition: 0.22s;
            }
        }
    }
    &-multiple-wrapper__error {
        border-color: @Crimson-6 !important;
    }
    &-multiple-wrapper__disabled {
        background-color: @Gray-2;
        border-color: @Gray-5;
        color: @Text-2;
        cursor: not-allowed;
        .vd-ui-tag .vd-ui-select-tag {
            padding-right: 5px;
        }
        &:hover {
            border-color: @Gray-5;
        }
        &.is-focus {
            border-color: @Gray-5;
            &:hover {
                border-color: @Gray-5;
            }
        }
    }
    &-multiple-wrapper-large {
        min-height: 42px;
    }

    &-list {
        width: 100%;
        box-sizing: border-box;
        overflow: auto;
        border-radius: 3px;
        border: solid 1px @Gray-7;
        border-radius: 3px;
        box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
        z-index: 15;
        background-color: @Text-7;
        padding: 5px 0px;
        list-style-type: none;
        margin: 0;
        overscroll-behavior: contain;
        .scrollbar;

        &.appear-up {
            transform-origin: center bottom;
            animation: appear 0.22s ease-out;
        }
        &.appear-down {
            transform-origin : center top;
            animation: appear 0.22s ease-out;
        }
        .loading-li {
            height: 29px;
            line-height: 29px;
            padding: 0px 10px;
            i {
                animation: loading 1.8s linear  infinite;
                display: inline-block;
                position: relative;
                top: 2px;
                font-size: 16px;
                margin-right: 5px;
                color: @Brand-6;
            }
        }
        .failed-li {
            height: 29px;
            line-height: 29px;
            padding: 0px 10px;
            i {
                position: relative;
                top: 2px;
                font-size: 16px;
                color: @Crimson-6;
                margin-right: 5px;
            }
            .reload {
                color: @Brand-6;
                cursor: pointer;
            }
        }
        .empty-li {
            height: 29px;
            line-height: 29px;
            padding: 0px 10px;
            text-align: center;
            color: @Text-2;
        }
    }

    .vd-ui-input-error{
        color:@Crimson-6;
        margin-top:5px;
        display: flex;
        align-items: center;

        .vd-ui-input-error--icon{
            font-size: 16px;
            margin-right: 5px;
            line-height: 1;
        }
        .vd-ui-input-error--errmsg{
            margin:0px;
        }
    }
}


@keyframes appear {
    0% {
        opacity: 0;
        -webkit-transform : scale(1, 0);
    }
    100% {
        -webkit-transform : scale(1, 1);
        opacity: 1;
    }
}
@keyframes tagAppear {
    0% {
        opacity: 0;
        -webkit-transform : scale(1, 0);
    }
    100% {
        -webkit-transform : scale(1, 1);
        opacity: 1;
    }
}
.slide-enter {
    opacity: 1;
    transform: scale(1, 1);
}
.slide-leave-active {
    // transition: 0.22s all ease-out;
}
.slide-leave-to {
    opacity: 0;
    -webkit-transform: scale(1, 0);
}

.vd-ui-group {
    margin-bottom: 5px;
    &:last-child {
        margin-bottom: 0;
    }
    .vd-ui-group-title {
        padding: 0px 10px;
        height: 33px;
        color: @Text-2;
        p {
            line-height: 33px;
        }
    }
}


.ui-select-option-li {
    height: 33px;
    width: 100%;
    cursor: pointer;
    position: relative;
    
    .icon-checkbox1,.icon-checkbox2 {
      position: absolute;
      left: 10px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 16px;
      color: @Gray-9;
    }
    .icon-checkbox2 {
      color: @Brand-6;
    }
    .li-p {
        padding: 0px 10px;
        display: flex;
        .before {
            width: 50%;
            overflow: hidden;/*超出部分隐藏*/
            white-space: nowrap;/*不换行*/
            text-overflow: ellipsis;/*超出部分文字以...显示*/
        } 
        .after {
            padding-left: 10px;
            width: 50%;
            overflow: hidden;/*超出部分隐藏*/
            white-space: nowrap;/*不换行*/
            text-overflow: ellipsis;/*超出部分文字以...显示*/
        }
    }
    .li-p-made {
        display: flex;
        align-items: center;

        .li-avatar {
            width: 20px;
            height: 20px;
            border-radius: 2px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 5px;
            position: relative;

            img {
                max-width: 100%;
                max-height: 100%;

                &.error {
                    width: 0;
                    height: 0;
    
                    &:before {
                        content: '';
                        position: absolute;
                        width: 20px;
                        height: 20px;
                        left: 0;
                        top: 0;
                        background-image: url(../image/crm-user-avatar.svg);
                        background-size: 100% 100%;
                    }
                }
            }
        }

        .li-label {
            flex: 1;

            .strong {
                color: #f60;
            }
        }
        .li-name {
            text-align: right;
            width: 50%;
            margin-left: 10px;
            color: #999;
        }
    }
    &.selected {
        .li-p {
            .li-label {
                color: @Brand-6;
            }
        }
    }
    &.multiple {
        .li-p {
            padding-left: 36px;
        }
    }
    p {
        line-height: 33px;
        margin: 0;
        font-size: 12px;
        overflow: hidden;/*超出部分隐藏*/
        white-space: nowrap;/*不换行*/
        text-overflow: ellipsis;/*超出部分文字以...显示*/
    }
    &:hover {
        background-color: @Gray-2;
        .icon-checkbox1 {
            color: @Brand-6;
        }
    }
    &.disabled {
        &:hover {
            background-color: @Text-7;
            .icon-checkbox1 {
                color: @Gray-5;
            }
            .icon-checkbox2 {
                color: @Gray-5;
            }
        }
        .icon-checkbox1 {
            color: @Gray-5;
        }
        .icon-checkbox2 {
            color: @Gray-5;
        }
        .li-p {
            cursor: not-allowed;
            p {
                color: @Text-2;
            }
        }
    }
    &:active {
        background-color: @Gray-3;
    }
}

.vd-ui-custom-select-wrap {
}

.vd-ui-custom-select-drop {
    background: #fff;
    padding: 10px 0;
    border: solid 1px #BABFC2;
    border-radius: 3px;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
    z-index: 11;

    .vd-ui-custom-select-search {
        padding: 0 10px;
        margin-bottom: 10px;
        
        .vd-ui-input .vd-ui-input__inner {
            border-radius: 0;
            border-top: 0;
            border-left: 0;
            border-right: 0;
        }

    }

    .vd-ui-custom-loading-wrap {
        display: flex;
        color: #666;
        padding: 6px 10px;
        align-items: center;

        .icon-loading {
            font-size: 16px;
            line-height: 1;
            color: #09f;
            margin-right: 5px;
            animation: loading 2s linear infinite;
        }
    }

    .vd-ui-custom-empty {
        color: #999;
        text-align: center;
        padding: 6px 10px;
    }

    .vd-ui-custom-select-list {
        max-height: 330px;
        overflow: auto;
        .scrollbar;
    }

    .vd-ui-custom-select-item {
        display: flex;
        align-items: center;
        cursor: pointer;
        height: 33px;
        padding: 0 10px;

        .item-checkbox {
            font-size: 16px;
            margin-right: 5px;
            display: flex;
            align-items: center;

            .vd-ui_icon {
                line-height: 1;

                &.icon-checkbox1 {
                    color: #969B9E;
                }

                &.icon-checkbox2 {
                    color: #09f;
                }
            }

            & + .item-avatar {
                margin-left: 5px;
            }
        }

        .item-avatar {
            width: 18px;
            height: 18px;
            border-radius: 2px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 5px;

            img {
                max-width: 100%;
                max-height: 100%;
            }
        }

        .item-label {
            flex: 1;

            .strong {
                color: #f60;
            }
        }

        &:hover {
            background: #f5f7fa;

            .item-checkbox {
                .vd-ui_icon {
                    &.icon-checkbox1 {
                        color: #09f;
                    }
                }
            }
        }
    }

    .vd-ui-custom-footer {
        margin-top: 5px;
        padding: 10px 10px 0 10px;
        display: flex;
        justify-content: flex-end;
        border-top: solid 1px #E1E5E8;

        .vd-ui-button {
            margin-left: 10px;
        }
    }
}