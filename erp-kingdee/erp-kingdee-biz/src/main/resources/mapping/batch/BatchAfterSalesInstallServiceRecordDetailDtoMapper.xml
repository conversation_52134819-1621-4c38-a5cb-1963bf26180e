<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchAfterSalesInstallServiceRecordDetailDtoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesInstallServiceRecordDetailDto">
    <!--@mbg.generated-->
    <!--@Table T_AFTER_SALES_INSTALL_SERVICE_RECORD_DETAIL-->
    <id column="AFTER_SALES_SERVICE_DETAIL_ID" jdbcType="INTEGER" property="afterSalesServiceDetailId" />
    <result column="AFTER_SALES_SERVICE_ID" jdbcType="INTEGER" property="afterSalesServiceId" />
    <result column="AFTER_SALES_GOODS_ID" jdbcType="INTEGER" property="afterSalesGoodsId" />
    <result column="SKU" jdbcType="VARCHAR" property="sku" />
    <result column="SKU_NAME" jdbcType="VARCHAR" property="skuName" />
    <result column="BRAND" jdbcType="VARCHAR" property="brand" />
    <result column="MODEL" jdbcType="VARCHAR" property="model" />
    <result column="NUM" jdbcType="INTEGER" property="num" />
    <result column="SERIAL_NUMBER" jdbcType="VARCHAR" property="serialNumber" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete" />
    <result column="SUPPL_CODE" jdbcType="VARCHAR" property="supplCode" />

    <result column="AFTER_SALES_ID" jdbcType="INTEGER" property="afterSalesId" />
    <result column="CHECK_DATE" jdbcType="DATE" property="checkDate" />
    <result column="CHECK_TYPE" jdbcType="INTEGER" property="checkType" />
    <result column="CHECK_TYPE_NAME" jdbcType="VARCHAR" property="checkTypeName" />
    <result column="RECORD_ID" jdbcType="VARCHAR" property="recordId" />
    <result column="CHECK_CONCLUSION" jdbcType="INTEGER" property="checkConclusion" />
    <result column="CHECK_CONCLUSION_NAME" jdbcType="VARCHAR" property="checkConclusionName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    AFTER_SALES_SERVICE_DETAIL_ID, AFTER_SALES_SERVICE_ID, AFTER_SALES_GOODS_ID, SKU,
    SKU_NAME, BRAND, MODEL, NUM, SERIAL_NUMBER, ADD_TIME, CREATOR, MOD_TIME, UPDATER,
    IS_DELETE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_AFTER_SALES_INSTALL_SERVICE_RECORD_DETAIL
    where AFTER_SALES_SERVICE_DETAIL_ID = #{afterSalesServiceDetailId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_AFTER_SALES_INSTALL_SERVICE_RECORD_DETAIL
    where AFTER_SALES_SERVICE_DETAIL_ID = #{afterSalesServiceDetailId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="AFTER_SALES_SERVICE_DETAIL_ID" keyProperty="afterSalesServiceDetailId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesInstallServiceRecordDetailDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALES_INSTALL_SERVICE_RECORD_DETAIL (AFTER_SALES_SERVICE_ID, AFTER_SALES_GOODS_ID,
      SKU, SKU_NAME, BRAND,
      MODEL, NUM, SERIAL_NUMBER,
      ADD_TIME, CREATOR, MOD_TIME,
      UPDATER, IS_DELETE)
    values (#{afterSalesServiceId,jdbcType=INTEGER}, #{afterSalesGoodsId,jdbcType=INTEGER},
      #{sku,jdbcType=VARCHAR}, #{skuName,jdbcType=VARCHAR}, #{brand,jdbcType=VARCHAR},
      #{model,jdbcType=VARCHAR}, #{num,jdbcType=INTEGER}, #{serialNumber,jdbcType=VARCHAR},
      #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=TIMESTAMP},
      #{updater,jdbcType=INTEGER}, #{isDelete,jdbcType=BOOLEAN})
  </insert>
  <insert id="insertSelective" keyColumn="AFTER_SALES_SERVICE_DETAIL_ID" keyProperty="afterSalesServiceDetailId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesInstallServiceRecordDetailDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALES_INSTALL_SERVICE_RECORD_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="afterSalesServiceId != null">
        AFTER_SALES_SERVICE_ID,
      </if>
      <if test="afterSalesGoodsId != null">
        AFTER_SALES_GOODS_ID,
      </if>
      <if test="sku != null and sku != ''">
        SKU,
      </if>
      <if test="skuName != null and skuName != ''">
        SKU_NAME,
      </if>
      <if test="brand != null and brand != ''">
        BRAND,
      </if>
      <if test="model != null and model != ''">
        MODEL,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="serialNumber != null and serialNumber != ''">
        SERIAL_NUMBER,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="afterSalesServiceId != null">
        #{afterSalesServiceId,jdbcType=INTEGER},
      </if>
      <if test="afterSalesGoodsId != null">
        #{afterSalesGoodsId,jdbcType=INTEGER},
      </if>
      <if test="sku != null and sku != ''">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null and skuName != ''">
        #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="brand != null and brand != ''">
        #{brand,jdbcType=VARCHAR},
      </if>
      <if test="model != null and model != ''">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="serialNumber != null and serialNumber != ''">
        #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesInstallServiceRecordDetailDto">
    <!--@mbg.generated-->
    update T_AFTER_SALES_INSTALL_SERVICE_RECORD_DETAIL
    <set>
      <if test="afterSalesServiceId != null">
        AFTER_SALES_SERVICE_ID = #{afterSalesServiceId,jdbcType=INTEGER},
      </if>
      <if test="afterSalesGoodsId != null">
        AFTER_SALES_GOODS_ID = #{afterSalesGoodsId,jdbcType=INTEGER},
      </if>
      <if test="sku != null and sku != ''">
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null and skuName != ''">
        SKU_NAME = #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="brand != null and brand != ''">
        BRAND = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="model != null and model != ''">
        MODEL = #{model,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="serialNumber != null and serialNumber != ''">
        SERIAL_NUMBER = #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
    </set>
    where AFTER_SALES_SERVICE_DETAIL_ID = #{afterSalesServiceDetailId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesInstallServiceRecordDetailDto">
    <!--@mbg.generated-->
    update T_AFTER_SALES_INSTALL_SERVICE_RECORD_DETAIL
    set AFTER_SALES_SERVICE_ID = #{afterSalesServiceId,jdbcType=INTEGER},
      AFTER_SALES_GOODS_ID = #{afterSalesGoodsId,jdbcType=INTEGER},
      SKU = #{sku,jdbcType=VARCHAR},
      SKU_NAME = #{skuName,jdbcType=VARCHAR},
      BRAND = #{brand,jdbcType=VARCHAR},
      MODEL = #{model,jdbcType=VARCHAR},
      NUM = #{num,jdbcType=INTEGER},
      SERIAL_NUMBER = #{serialNumber,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN}
    where AFTER_SALES_SERVICE_DETAIL_ID = #{afterSalesServiceDetailId,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_AFTER_SALES_INSTALL_SERVICE_RECORD_DETAIL
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="AFTER_SALES_SERVICE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_SERVICE_DETAIL_ID = #{item.afterSalesServiceDetailId,jdbcType=INTEGER} then #{item.afterSalesServiceId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="AFTER_SALES_GOODS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_SERVICE_DETAIL_ID = #{item.afterSalesServiceDetailId,jdbcType=INTEGER} then #{item.afterSalesGoodsId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="SKU = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_SERVICE_DETAIL_ID = #{item.afterSalesServiceDetailId,jdbcType=INTEGER} then #{item.sku,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="SKU_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_SERVICE_DETAIL_ID = #{item.afterSalesServiceDetailId,jdbcType=INTEGER} then #{item.skuName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="BRAND = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_SERVICE_DETAIL_ID = #{item.afterSalesServiceDetailId,jdbcType=INTEGER} then #{item.brand,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="MODEL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_SERVICE_DETAIL_ID = #{item.afterSalesServiceDetailId,jdbcType=INTEGER} then #{item.model,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_SERVICE_DETAIL_ID = #{item.afterSalesServiceDetailId,jdbcType=INTEGER} then #{item.num,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="SERIAL_NUMBER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_SERVICE_DETAIL_ID = #{item.afterSalesServiceDetailId,jdbcType=INTEGER} then #{item.serialNumber,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_SERVICE_DETAIL_ID = #{item.afterSalesServiceDetailId,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_SERVICE_DETAIL_ID = #{item.afterSalesServiceDetailId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_SERVICE_DETAIL_ID = #{item.afterSalesServiceDetailId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_SERVICE_DETAIL_ID = #{item.afterSalesServiceDetailId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_SERVICE_DETAIL_ID = #{item.afterSalesServiceDetailId,jdbcType=INTEGER} then #{item.isDelete,jdbcType=BOOLEAN}
        </foreach>
      </trim>
    </trim>
    where AFTER_SALES_SERVICE_DETAIL_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.afterSalesServiceDetailId,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_AFTER_SALES_INSTALL_SERVICE_RECORD_DETAIL
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="AFTER_SALES_SERVICE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.afterSalesServiceId != null">
            when AFTER_SALES_SERVICE_DETAIL_ID = #{item.afterSalesServiceDetailId,jdbcType=INTEGER} then #{item.afterSalesServiceId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="AFTER_SALES_GOODS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.afterSalesGoodsId != null">
            when AFTER_SALES_SERVICE_DETAIL_ID = #{item.afterSalesServiceDetailId,jdbcType=INTEGER} then #{item.afterSalesGoodsId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="SKU = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.sku != null">
            when AFTER_SALES_SERVICE_DETAIL_ID = #{item.afterSalesServiceDetailId,jdbcType=INTEGER} then #{item.sku,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SKU_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.skuName != null">
            when AFTER_SALES_SERVICE_DETAIL_ID = #{item.afterSalesServiceDetailId,jdbcType=INTEGER} then #{item.skuName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="BRAND = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.brand != null">
            when AFTER_SALES_SERVICE_DETAIL_ID = #{item.afterSalesServiceDetailId,jdbcType=INTEGER} then #{item.brand,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="MODEL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.model != null">
            when AFTER_SALES_SERVICE_DETAIL_ID = #{item.afterSalesServiceDetailId,jdbcType=INTEGER} then #{item.model,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.num != null">
            when AFTER_SALES_SERVICE_DETAIL_ID = #{item.afterSalesServiceDetailId,jdbcType=INTEGER} then #{item.num,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="SERIAL_NUMBER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.serialNumber != null">
            when AFTER_SALES_SERVICE_DETAIL_ID = #{item.afterSalesServiceDetailId,jdbcType=INTEGER} then #{item.serialNumber,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when AFTER_SALES_SERVICE_DETAIL_ID = #{item.afterSalesServiceDetailId,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when AFTER_SALES_SERVICE_DETAIL_ID = #{item.afterSalesServiceDetailId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when AFTER_SALES_SERVICE_DETAIL_ID = #{item.afterSalesServiceDetailId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when AFTER_SALES_SERVICE_DETAIL_ID = #{item.afterSalesServiceDetailId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDelete != null">
            when AFTER_SALES_SERVICE_DETAIL_ID = #{item.afterSalesServiceDetailId,jdbcType=INTEGER} then #{item.isDelete,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
    </trim>
    where AFTER_SALES_SERVICE_DETAIL_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.afterSalesServiceDetailId,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="AFTER_SALES_SERVICE_DETAIL_ID" keyProperty="afterSalesServiceDetailId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALES_INSTALL_SERVICE_RECORD_DETAIL
    (AFTER_SALES_SERVICE_ID, AFTER_SALES_GOODS_ID, SKU, SKU_NAME, BRAND, MODEL, NUM,
      SERIAL_NUMBER, ADD_TIME, CREATOR, MOD_TIME, UPDATER, IS_DELETE)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.afterSalesServiceId,jdbcType=INTEGER}, #{item.afterSalesGoodsId,jdbcType=INTEGER},
        #{item.sku,jdbcType=VARCHAR}, #{item.skuName,jdbcType=VARCHAR}, #{item.brand,jdbcType=VARCHAR},
        #{item.model,jdbcType=VARCHAR}, #{item.num,jdbcType=INTEGER}, #{item.serialNumber,jdbcType=VARCHAR},
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, #{item.modTime,jdbcType=TIMESTAMP},
        #{item.updater,jdbcType=INTEGER}, #{item.isDelete,jdbcType=BOOLEAN})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="AFTER_SALES_SERVICE_DETAIL_ID" keyProperty="afterSalesServiceDetailId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesInstallServiceRecordDetailDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALES_INSTALL_SERVICE_RECORD_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="afterSalesServiceDetailId != null">
        AFTER_SALES_SERVICE_DETAIL_ID,
      </if>
      AFTER_SALES_SERVICE_ID,
      AFTER_SALES_GOODS_ID,
      SKU,
      SKU_NAME,
      BRAND,
      MODEL,
      NUM,
      SERIAL_NUMBER,
      ADD_TIME,
      CREATOR,
      MOD_TIME,
      UPDATER,
      IS_DELETE,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="afterSalesServiceDetailId != null">
        #{afterSalesServiceDetailId,jdbcType=INTEGER},
      </if>
      #{afterSalesServiceId,jdbcType=INTEGER},
      #{afterSalesGoodsId,jdbcType=INTEGER},
      #{sku,jdbcType=VARCHAR},
      #{skuName,jdbcType=VARCHAR},
      #{brand,jdbcType=VARCHAR},
      #{model,jdbcType=VARCHAR},
      #{num,jdbcType=INTEGER},
      #{serialNumber,jdbcType=VARCHAR},
      #{addTime,jdbcType=TIMESTAMP},
      #{creator,jdbcType=INTEGER},
      #{modTime,jdbcType=TIMESTAMP},
      #{updater,jdbcType=INTEGER},
      #{isDelete,jdbcType=BOOLEAN},
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="afterSalesServiceDetailId != null">
        AFTER_SALES_SERVICE_DETAIL_ID = #{afterSalesServiceDetailId,jdbcType=INTEGER},
      </if>
      AFTER_SALES_SERVICE_ID = #{afterSalesServiceId,jdbcType=INTEGER},
      AFTER_SALES_GOODS_ID = #{afterSalesGoodsId,jdbcType=INTEGER},
      SKU = #{sku,jdbcType=VARCHAR},
      SKU_NAME = #{skuName,jdbcType=VARCHAR},
      BRAND = #{brand,jdbcType=VARCHAR},
      MODEL = #{model,jdbcType=VARCHAR},
      NUM = #{num,jdbcType=INTEGER},
      SERIAL_NUMBER = #{serialNumber,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="AFTER_SALES_SERVICE_DETAIL_ID" keyProperty="afterSalesServiceDetailId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesInstallServiceRecordDetailDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALES_INSTALL_SERVICE_RECORD_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="afterSalesServiceDetailId != null">
        AFTER_SALES_SERVICE_DETAIL_ID,
      </if>
      <if test="afterSalesServiceId != null">
        AFTER_SALES_SERVICE_ID,
      </if>
      <if test="afterSalesGoodsId != null">
        AFTER_SALES_GOODS_ID,
      </if>
      <if test="sku != null and sku != ''">
        SKU,
      </if>
      <if test="skuName != null and skuName != ''">
        SKU_NAME,
      </if>
      <if test="brand != null and brand != ''">
        BRAND,
      </if>
      <if test="model != null and model != ''">
        MODEL,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="serialNumber != null and serialNumber != ''">
        SERIAL_NUMBER,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="afterSalesServiceDetailId != null">
        #{afterSalesServiceDetailId,jdbcType=INTEGER},
      </if>
      <if test="afterSalesServiceId != null">
        #{afterSalesServiceId,jdbcType=INTEGER},
      </if>
      <if test="afterSalesGoodsId != null">
        #{afterSalesGoodsId,jdbcType=INTEGER},
      </if>
      <if test="sku != null and sku != ''">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null and skuName != ''">
        #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="brand != null and brand != ''">
        #{brand,jdbcType=VARCHAR},
      </if>
      <if test="model != null and model != ''">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="serialNumber != null and serialNumber != ''">
        #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="afterSalesServiceDetailId != null">
        AFTER_SALES_SERVICE_DETAIL_ID = #{afterSalesServiceDetailId,jdbcType=INTEGER},
      </if>
      <if test="afterSalesServiceId != null">
        AFTER_SALES_SERVICE_ID = #{afterSalesServiceId,jdbcType=INTEGER},
      </if>
      <if test="afterSalesGoodsId != null">
        AFTER_SALES_GOODS_ID = #{afterSalesGoodsId,jdbcType=INTEGER},
      </if>
      <if test="sku != null and sku != ''">
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null and skuName != ''">
        SKU_NAME = #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="brand != null and brand != ''">
        BRAND = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="model != null and model != ''">
        MODEL = #{model,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="serialNumber != null and serialNumber != ''">
        SERIAL_NUMBER = #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
    </trim>
  </insert>

<!--auto generated by MybatisCodeHelper on 2023-02-17-->
  <select id="findByAddTime" resultMap="BaseResultMap">
    select
    a.*,
    b.AFTER_SALES_ID,
    b.CHECK_DATE,
    b.CHECK_TYPE,
    case
    when b.CHECK_TYPE = 1 then '电话回访'
    when b.CHECK_TYPE = 2 then '纸单验收'
    when b.CHECK_TYPE = 3 then '短信通知'
    else ''
    end CHECK_TYPE_NAME,
    b.RECORD_ID,
    b.CHECK_CONCLUSION,
    c.TITLE CHECK_CONCLUSION_NAME
    from T_AFTER_SALES_INSTALL_SERVICE_RECORD_DETAIL a
    left join T_AFTER_SALES_INSTALL_SERVICE_RECORD b on a.AFTER_SALES_SERVICE_ID = b.AFTER_SALES_SERVICE_ID
    left join T_SYS_OPTION_DEFINITION c on b.CHECK_CONCLUSION = c.SYS_OPTION_DEFINITION_ID
    left join T_AFTER_SALES_GOODS d on a.AFTER_SALES_GOODS_ID = d.AFTER_SALES_GOODS_ID
    left join T_SALEORDER_GOODS e on d.ORDER_DETAIL_ID = e.SALEORDER_GOODS_ID
    where a.ADD_TIME <![CDATA[>=]]> #{beginTime,jdbcType=TIMESTAMP}
    and a.ADD_TIME <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
    and a.IS_DELETE = 0
    and b.IS_DELETE = 0
    -- 以下售后单中安调记录过多，由财务手工处理
    and b.AFTER_SALES_ID not in (94191, 74672, 95786, 78082, 102001, 81099, 94527, 96337, 98541, 121687)
    -- 财务要求安调结论是（客户设备未安调后续再跟进、客户设备未安调已协助报售后）两种类型的不推送金蝶
    and b.CHECK_CONCLUSION not in (4088, 4089)
    -- 财务需要以下sku暂不推送
    and a.SKU not in (
    'V525472', 'V500019', 'V533038', 'V255193', 'V277029', 'V508079', 'V527520', 'V255193', 'V527520', 'V508079',
    'V525200', 'V273297', 'V533213', 'V528590', 'V528590', 'V528895', 'V529780', 'V528590', 'V527520', 'V276529',
    'V501686', 'V507098', 'V527520', 'V527522', 'V528895', 'V521181', 'V258565', 'V255193', 'V527522', 'V527297',
    'V256730', 'V504919', 'V521775', 'V276479', 'V514248', 'V526157', 'V519200', 'V528643', 'V514248', 'V258349',
    'V277046', 'V273820', 'V257651', 'V528643', 'V527525', 'V529780', 'V501686', 'V519180', 'V532176', 'V528504',
    'V530698', 'V277346', 'V509721', 'V276951', 'V521774', 'V272673', 'V274872', 'V503039', 'V275350', 'V259228',
    'V527526', 'V525781', 'V530701', 'V256938', 'V255478', 'V528993', 'V516752', 'V527522', 'V527520', 'V504866',
    'V273232', 'V504326', 'V277344', 'V526570', 'V534217', 'V274999', 'V274860', 'V504588', 'V276990', 'V532643',
    'V530699', 'V253790', 'V255478', 'V258348', 'V502511', 'V527526', 'V253780', 'V528643', 'V272306', 'V533221',
    'V276237', 'V514162', 'V254055', 'V521860', 'V256980', 'V272602', 'V528621', 'V536156', 'V512006', 'V523215',
    'V500852', 'V504501', 'V277352', 'V527525', 'V527525', 'V512247', 'V273826', 'V532321', 'V254055', 'V514566',
    'V532145', 'V505387', 'V147397', 'V529514', 'V531322', 'V525988', 'V257507', 'V527526', 'V503971', 'V527526',
    'V514989', 'V258172', 'V277710', 'V529168', 'V529490', 'V517380', 'V272588', 'V501686', 'V527522', 'V277527',
    'V515335', 'V527946', 'V504344', 'V500031', 'V519288', 'V272429', 'V517194', 'V535836', 'V254577', 'V525780',
    'V515079', 'V503645', 'V273820', 'V255670', 'V527949', 'V525496', 'V276393', 'V529323', 'V528895', 'V513811',
    'V273223', 'V503645', 'V529780', 'V525000', 'V533171', 'V271747', 'V530126', 'V506605', 'V527946', 'V258650',
    'V529736', 'V529515', 'V503507', 'V121823', 'V276668', 'V520512', 'V276786', 'V520095', 'V517369', 'V514164',
    'V504336', 'V533454', 'V271796', 'V503468', 'V276419', 'V513998', 'V505247', 'V256012', 'V529227', 'V255478',
    'V250964', 'V535839', 'V535858', 'V257515', 'V272673', 'V529285', 'V534822', 'V527130', 'V527525', 'V251297',
    'V273276', 'V513770', 'V535857', 'V508412', 'V500970', 'V504589', 'V534979', 'V514253', 'V251224', 'V255196',
    'V533587', 'V512073', 'V505226', 'V521784', 'V251822', 'V503987', 'V250949', 'V532022', 'V257495', 'V272673',
    'V529284', 'V504313', 'V256012', 'V277531', 'V530442', 'V252151', 'V529737', 'V251818', 'V503991', 'V530582',
    'V505898', 'V277128', 'V257837', 'V514185', 'V277343', 'V256976', 'V256762', 'V515435', 'V272649', 'V529037',
    'V271670', 'V531374', 'V531375', 'V501845', 'V500163', 'V529778', 'V526830', 'V278199', 'V257686', 'V534980',
    'V529756', 'V274950', 'V529364', 'V533602', 'V251226', 'V121823', 'V534437', 'V529433', 'V523874', 'V273917',
    'V250949', 'V504336', 'V500263', 'V529768', 'V272303', 'V257374', 'V272750', 'V271744', 'V516914', 'V256970',
    'V502610', 'V500755', 'V500790', 'V530959', 'V514411', 'V258519', 'V504072', 'V530737', 'V529482', 'V529227',
    'V532060', 'V527102', 'V534888', 'V512992', 'V253410', 'V151166', 'V532181', 'V516474', 'V514481', 'V255128',
    'V505643', 'V254199', 'V254093', 'V251820', 'V254397', 'V505643', 'V515938', 'V506621', 'V151250', 'V517443',
    'V520392', 'V519127', 'V532258', 'V273904', 'V272301', 'V272301', 'V257509', 'V273872', 'V530043', 'V522434',
    'V534264', 'V505151', 'V520358', 'V520146', 'V257329', 'V531371', 'V531372', 'V534780', 'V514324', 'V525725',
    'V533185', 'V533184', 'V527788', 'V504326', 'V522103', 'V254325', 'V518128', 'V506799', 'V534191', 'V147397',
    'V147409', 'V274257', 'V272916', 'V529323', 'V533777', 'V533588', 'V527709', 'V533346', 'V524601', 'V529739',
    'V529738', 'V252151', 'V527883', 'V253993', 'V252441', 'V251226', 'V514248', 'V277630', 'V505303', 'V529418',
    'V256667', 'V505614', 'V505613', 'V121823', 'V504300', 'V501480', 'V273186', 'V535405', 'V531605', 'V531606',
    'V521336', 'V519822', 'V504889', 'V504886', 'V504890', 'V504887', 'V533833', 'V515732', 'V530574', 'V530587',
    'V250964', 'V250964', 'V529554', 'V500263', 'V530584', 'V530573', 'V529557', 'V532843', 'V255482', 'V515577',
    'V525531', 'V514182', 'V530951', 'V529434', 'V535852', 'V535853', 'V535851', 'V526975', 'V273508', 'V503465',
    'V273274', 'V273201', 'V258350', 'V504293', 'V258031', 'V271672', 'V273268', 'V525724', 'V272790', 'V504690',
    'V531191', 'V516322', 'V526454', 'V252791', 'V501143', 'V277357', 'V535098', 'V512755', 'V514867', 'V255631',
    'V532271', 'V533469', 'V251411', 'V252562', 'V514410', 'V515632', 'V272649', 'V508430', 'V258502', 'V512681',
    'V529323', 'V527526', 'V505228', 'V512064', 'V505246', 'V525518', 'V513305', 'V514724', 'V135440', 'V251474',
    'V253507', 'V257635', 'V533625', 'V533625', 'V533620', 'V521437', 'V526786', 'V508739', 'V522449', 'V532323',
    'V527542', 'V530493', 'V534325', 'V527579', 'V527578', 'V502606', 'V275457', 'V151250', 'V255193', 'V142619',
    'V524354', 'V515135', 'V515136', 'V534863', 'V521783', 'V502770', 'V534864', 'V274486', 'V520789', 'V520792',
    'V520791', 'V514129', 'V535479', 'V535093', 'V505034', 'V502739', 'V501717', 'V513189', 'V276888', 'V500226',
    'V276577', 'V501357', 'V500967', 'V276576', 'V277162', 'V273416', 'V274616', 'V274610', 'V502268', 'V274611',
    'V524682', 'V503660', 'V509908', 'V278226', 'V500966', 'V273427', 'V501734', 'V147397', 'V147409', 'V272306',
    'V519967', 'V515006', 'V515010', 'V524089', 'V274850', 'V511705', 'V511709', 'V251307', 'V256293', 'V252194',
    'V530920', 'V522384', 'V251835', 'V251297', 'V514593', 'V252297', 'V501113', 'V516846', 'V516847', 'V251831',
    'V253943', 'V514248', 'V514248', 'V515251', 'V255688', 'V254093', 'V251405', 'V253943', 'V505893', 'V534886',
    'V522436', 'V522437', 'V522438', 'V522440', 'V522439', 'V506386', 'V503988', 'V506408', 'V532397', 'V532398',
    'V532419', 'V532189', 'V502967', 'V504562', 'V514996', 'V530571', 'V529556', 'V529555', 'V254263', 'V500973',
    'V529563', 'V502284', 'V529559', 'V529593', 'V529595', 'V529594', 'V530586', 'V531161', 'V531192', 'V517201',
    'V531030', 'V274272', 'V535844', 'V536028', 'V535841', 'V535856', 'V533170', 'V525542', 'V531434', 'V524356',
    'V272301', 'V256970', 'V277119', 'V258031', 'V509726', 'V272269', 'V257998', 'V500128', 'V274681', 'V504680',
    'V500349', 'V275938', 'V522479', 'V516318', 'V532427', 'V529613'
    )
    and e.HAVE_INSTALLATION = 1
    group by a.AFTER_SALES_SERVICE_DETAIL_ID
    limit #{_pagesize} OFFSET #{_skiprows}
  </select>
</mapper>
