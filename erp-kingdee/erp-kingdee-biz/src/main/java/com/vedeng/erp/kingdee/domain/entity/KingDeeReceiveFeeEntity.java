package com.vedeng.erp.kingdee.domain.entity;

import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseEntity;
import java.util.Date;

import com.vedeng.common.mybatis.jbatis.annotation.Column;
import com.vedeng.common.mybatis.jbatis.annotation.GeneratedValue;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.common.mybatis.jbatis.annotation.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
    * 费用应收单
    */
@Getter
@Setter
@ToString
@Table(name = "KING_DEE_RECEIVE_FEE")
public class KingDeeReceiveFeeEntity extends BaseEntity {
    /**
    * id
    */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
    * 单据内码 0：表示新增，非0：云星空系统单据FID值，表示修改
    */
    private String fid;

    /**
    * 单据日期 格式yyyy-MM-dd
    */
    private String fdate;

    /**
    * 贝登单据头ID 贝登单据头ID号（预留）
    */
    private String fQzokBddjtid;

    /**
    * 业务类型 默认FY
    */
    private String fbusinesstype;

    /**
    * 立账类型  默认填写1
    */
    private String fSetAccountType;

    /**
    * 单据类型 填单据类型编码，默认"YSD01_SYS"
    */
    private String fBillTypeID;

    /**
    * 客户 填写客户编码
    */
    private String fcustomerid;

    /**
    * 结算组织 填写组织编码
    */
    private String fsettleorgid;

    /**
    * fEntityDetail
    */
    @Column(jdbcType = "VARCHAR")
    private JSONArray fEntityDetail;
}