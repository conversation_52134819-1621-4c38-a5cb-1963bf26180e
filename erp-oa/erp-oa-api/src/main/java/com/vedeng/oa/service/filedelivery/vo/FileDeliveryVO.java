package com.vedeng.oa.service.filedelivery.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
@Data
public class FileDeliveryVO implements Serializable{
	private static final long serialVersionUID = 1L;

	private Integer fileDeliveryId;

    private String fileDeliveryNo;

    private Integer applyUserId;


    private String applyOrgName;


    private Date applyTime;

    private Integer verifyStatus;

    private Integer deliveryStatus;


    private Date deliveryTime;

    private Integer traderId;


    private Integer traderType;


    private String traderName;

    private Integer logisticsName;

    private String expressNos;

    private Integer deliveryType;

    private Integer deliveryDept;

    private BigDecimal deliveryTotalWeight;

    private String deliveryProdName;


    private Integer deliveryExpressNum;


    private String content;


    private Integer isClosed;


    private String closedComments;

    private Integer isDeleted;

    private Date addTime;
    
    /**创建人*/
    private Integer creator;
    
    /**发件人类型【1：公司；2：申请人】*/
    private Integer sendUserType;

}
