<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchWmsInputOrderDtoMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchWmsInputOrderDto">
        <!--@mbg.generated-->
        <!--@Table T_WMS_INPUT_ORDER-->
        <id column="WMS_INPUT_ORDER_ID" jdbcType="INTEGER" property="wmsInputOrderId"/>
        <result column="ORDER_TYPE" jdbcType="INTEGER" property="orderType"/>
        <result column="ORDER_NO" jdbcType="VARCHAR" property="orderNo"/>
        <result column="VERIFY_STATUS" jdbcType="INTEGER" property="verifyStatus"/>
        <result column="ARRIVAL_STATUS" jdbcType="INTEGER" property="arrivalStatus"/>
        <result column="APPLY_INTIME" jdbcType="TIMESTAMP" property="applyIntime"/>
        <result column="REMARK" jdbcType="VARCHAR" property="remark"/>
        <result column="APPLYER" jdbcType="VARCHAR" property="applyer"/>
        <result column="APPLYER_USERID" jdbcType="INTEGER" property="applyerUserid"/>
        <result column="APPLYER_DEPARTMENT" jdbcType="VARCHAR" property="applyerDepartment"/>
        <result column="APPLYER_DEPARTMENT_ID" jdbcType="INTEGER" property="applyerDepartmentId"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="MODE_TIME" jdbcType="TIMESTAMP" property="modeTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete"/>
        <collection property="batchWmsInputOrderGoodsDtoList" ofType="com.vedeng.erp.kingdee.batch.dto.BatchWmsInputOrderGoodsDto">
            <result column="WMS_INPUT_ORDER_ID" jdbcType="INTEGER" property="wmsInputOrderId" />
            <result column="SKU_NO" jdbcType="VARCHAR" property="skuNo" />
            <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId" />
            <result column="INPUT_NUM" jdbcType="INTEGER" property="inputNum" />
            <result column="ARRIVAL_NUM" jdbcType="INTEGER" property="arrivalNum" />
            <result column="ARRIVAL_STATUS" jdbcType="INTEGER" property="arrivalStatus" />
            <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
            <result column="MODE_TIME" jdbcType="TIMESTAMP" property="modeTime" />
            <result column="CREATOR" jdbcType="INTEGER" property="creator" />
            <result column="UPDATER" jdbcType="INTEGER" property="updater" />
            <result column="IS_DELTET" jdbcType="INTEGER" property="isDeltet" />
        </collection>
    </resultMap>

    <select id="findByOrderNo" resultMap="BaseResultMap">
        select
        TWIO.*,
        TWIOG.*
        from T_WMS_INPUT_ORDER TWIO
        left join T_WMS_INPUT_ORDER_GOODS TWIOG on TWIO.WMS_INPUT_ORDER_ID = TWIOG.WMS_INPUT_ORDER_ID
        where ORDER_NO = #{orderNo,jdbcType=VARCHAR}
          and TWIO.IS_DELETE = 0
    </select>


</mapper>