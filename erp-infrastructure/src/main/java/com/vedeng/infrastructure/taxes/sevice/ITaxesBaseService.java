package com.vedeng.infrastructure.taxes.sevice;

import com.vedeng.infrastructure.taxes.base.ITaxesParam;
import com.vedeng.infrastructure.taxes.base.ITaxesResult;

public interface ITaxesBaseService {

    /**
     * 销项票
     * @param taxesParam
     * @return
     */
    ITaxesResult openapi(ITaxesParam taxesParam, String interfaceCode);

    /**
     * 销项票
     * @param taxesParamJsonStr
     * @return
     */
    ITaxesResult openapi(String taxesParamJsonStr, String interfaceCode);

    /**
     * 公共接口
     * @param taxesParam
     * @param interfaceCode
     * @return
     */
    ITaxesResult commonapi(ITaxesParam taxesParam, String interfaceCode);
}
