package com.vedeng.erp.finance.dto;

import cn.hutool.core.util.StrUtil;
import com.vedeng.erp.finance.enums.InvoiceTaxTypeEnum;
import com.vedeng.erp.trader.dto.TraderFinanceDto;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 开票申请
 */
@Data
public class InvoiceApplyDto implements Serializable {
    private Integer invoiceApplyId;

    /**
     * ERP公司ID(T_COMPANY)
     */
    private Integer companyId;

    /**
     * 1纸质发票2电子发票
     */
    private Integer invoiceProperty;

    /**
     * 申请方式，0为销售手动申请,1为系统自动推送, 2票货同行物流部申请 3客户在线申请开票
     */
    private Integer applyMethod;

    /**
     * 开票申请类型 字典库
     */
    private Integer type;

    /**
     * 关联表ID
     */
    private Integer relatedId;

    /**
     * 开票型式1手动2自动
     */
    private Integer isAuto;

    /**
     * 是否提前申请开票 0:否1：是
     */
    private Integer isAdvance;

    /**
     * 提前申请审核 0待审核 1通过 不通过
     */
    private Integer advanceValidStatus;

    /**
     * 提前申请审核人ID
     */
    private Integer advanceValidUserid;

    /**
     * 提前申请最后一次审核时间
     */
    private Long advanceValidTime;

    /**
     * 提前申请审核备注
     */
    private String advanceValidComments;

    /**
     * 审核 0待审核 1通过 2不通过
     */
    private Integer yyValidStatus;

    /**
     * 运营审核人ID
     */
    private Integer yyValidUserid;

    /**
     * 运营审核时间
     */
    private Long yyValidTime;

    /**
     * 运营审核备注
     */
    private String yyValidComments;

    /**
     * 审核 0待审核 1通过 不通过
     */
    private Integer validStatus;

    /**
     * 审核人ID
     */
    private Integer validUserid;

    /**
     * 最后一次审核时间
     */
    private Long validTime;

    /**
     * 审核备注
     */
    private String validComments;

    /**
     * 待开发票状态 1：待开 2：已经获取
     */
    private Integer invoiceStStatus;

    /**
     * 发票打印状态 0：未打印 1：已打印
     */
    private Integer invoicePrintStatus;

    /**
     * 备注(申请备注)
     */
    private String comments;

    /**
     * 是否标记开票0否 1是
     */
    private Integer isSign;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 添加人
     */
    private Integer creator;

    /**
     * 添加人名称
     */
    private String creatorName;

    /**
     * 最近一次编辑时间
     */
    private Long modTime;

    /**
     * 最近一次编辑人
     */
    private Integer updater;

    private Integer expressId;

    /**
     * 是否正在开票0否 1是
     */
    private Integer isOpening;

    /**
     * 开票失败原因
     */
    private String reasons;

    /**
     * 票货同行任务WMS订单号
     */
    private String wmsOrderNo;

    /**
     * 票货同行任务ERP订单号
     */
    private String erpOrderNo;

    /**
     * 是否推送到WMS 0否 1是
     */
    private Integer isSendWms;

    /**
     * 前台申请者ID
     */
    private Integer signerId;

    /**
     * 前台申请者名称
     */
    private String signerName;

    /**
     * 是否催办 0否 1是
     */
    private Integer urage;

    /**
     * 税率
     */
    private BigDecimal tax;

    /**
     * 发票类型
     */
    private Integer invoiceType;

    /**
     * "01"专票 "02"普票
     * 航信专用
     */
    private String invoiceTypeCode;

    /**
     * 客户id
     */
    private Integer traderId;

    /**
     * 客户名称
     */
    private String traderName;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 发票申请明细
     */
    private List<InvoiceApplyDetailDto> invoiceApplyDetailDtoList;

    /**
     * 销售单号
     */
    private String orderNo;


    /**
     * 购方信息
     */
    private TraderFinanceDto traderFinanceDto;

    /**
     * 开票方式(取开票申请的isAuto)
     */
    private Integer invoiceMethod;

    /**
     * 提前申请原因
     */
    private String advanceValidReason;

    /**
     * 开票留言
     */
    private String invoiceMessage;

    /**
     * 开票信息类型(0-标准开票信息,1-自定义开票信息)
     */
    private Integer invoiceInfoType;


    /**
     * 创建方式：SYSTEM、MANUAL
     */
    private String createType;

    private List<InvoiceApplyReasonSnapshotDto> snapshotDtoList;

    private static final long serialVersionUID = 1L;

    public void initInvoiceInfo() {
        // 是否专票
        boolean isSpecialInvoice = InvoiceTaxTypeEnum.getIsSpecialInvoice(invoiceType);
        this.invoiceTypeCode = isSpecialInvoice ? "01" : "02";
        // 税率
        this.tax = InvoiceTaxTypeEnum.getTax(invoiceType);
        // 补0到19位
        invoiceApplyDetailDtoList.forEach(i -> i.setTaxCategoryNo(StrUtil.padAfter(i.getTaxCategoryNo(), 19, "0")));
    }

    public void initInvoiceAmount() {
        this.totalAmount = invoiceApplyDetailDtoList
                .stream()
                .map(InvoiceApplyDetailDto::getTotalAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
    }


}