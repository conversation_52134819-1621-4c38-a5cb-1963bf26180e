package com.vedeng.common.trace.service.impl;

import java.util.Map;
import java.util.Objects;

import cn.hutool.core.util.StrUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.vedeng.common.trace.data.TrackParamsData;
import com.vedeng.common.trace.enums.EventTrackingEnum;
import com.vedeng.common.trace.track.TrackStrategy;
import com.vedeng.common.trace.util.TrackUtils;
import com.vedeng.common.trace.service.TrackBaseService;

/**
 * 新增线索
 * @ClassName:  BusinessLeadsAddStrategyImpl     
 * @author: <PERSON>.yang
 * @date:   2024年6月7日 下午5:46:43    
 * @Copyright:
 */
@Component
public class BusinessLeadsAddStrategyImpl extends TrackBaseService implements TrackStrategy {

	private static final Logger LOGGER = LoggerFactory.getLogger(BusinessLeadsAddStrategyImpl.class);
	
	@Override
	public EventTrackingEnum getTrackStrategy() {
		return EventTrackingEnum.PRE_SALE_NEW_BUSINESS_LEADS;
	}

	@Override
	public boolean checkParamsLegal(TrackParamsData trackParamsData) {
		//获取唯一标识符
		Map<String, Object> trackParams = trackParamsData.getTrackParams();
		String primaryKey = trackParamsData.getEventTrackingEnum().getPrimaryKey();
		String primaryValue = TrackUtils.getJsonValue(new JSONObject(trackParams),primaryKey);
		//唯一标识符获取不到，不能埋点
		if(StrUtil.isEmpty(primaryValue) || "0".equals(primaryValue)) {
			LOGGER.info("=======埋点档案：{}，必填字段：{}缺失...",trackParamsData.getEventTrackingEnum().getArchivedName(),primaryKey);
			return false;
		}
		if(Objects.isNull(trackParams.get("track_user"))) {
			LOGGER.info("=======埋点档案：{}，必填字段：{}缺失...",trackParamsData.getEventTrackingEnum().getArchivedName(),"track_user");
			return false;
		}
		return true;
	}

}
