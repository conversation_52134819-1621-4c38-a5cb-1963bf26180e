<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="tags" tagdir="/WEB-INF/tags"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
<%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>
<%--<!DOCTYPE html">--%>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>生产企业详情</title>
    <link rel="stylesheet"
          href="${pageContext.request.contextPath}/static/new/css/common/global.css">
    <link rel="stylesheet"
          href="${pageContext.request.contextPath}/static/new/css/pages/firstengage/first/view.css?rnd=${resourceVersionKey}">
</head>

<body>
<div class="detail-wrap">
    <div class="detail-title">
    <c:choose>
        <c:when test="${not empty param.weituo}">委托生产企业资质-${manufacturer.manufacturerName}</c:when>
        <c:otherwise>生产企业资质-${manufacturer.manufacturerName}</c:otherwise>
    </c:choose>

    </div>
    <c:if test="${notShowCheck ne 0}">
        <div class="detail-option-wrap">
            <div class="option-btns">

                <input type="hidden" id = "manufacturerId" value="${manufacturer.manufacturerId}" />
                <c:if test="${manufacturer.status ne 1 and manufacturer.hasEditAuth}">
                    <a href="./addProduct.do?manufacturerId=${manufacturer.manufacturerId}" class="btn btn-blue btn-small">编辑</a>
                    <a class="btn btn-small J-one-del" data-id="${manufacturer.manufacturerId}">删除</a>
<%--                    <c:if test="${manufacturer.status eq 3 and manufacturer.signature ne 2}">--%>
<%--                        <a  class="btn btn-red btn-small J-audit3"  data-type="${manufacturer.status}" data-t="0" data-id="${manufacturer.manufacturerId}">电子签章</a>--%>
<%--                    </c:if>--%>
                    <div>
                        <c:if test="${manufacturer.status eq 3 and manufacturer.signature eq 2}">
                            <a  class="btn btn-red btn-small J-audit3"  data-type="${manufacturer.status}" data-t="0" data-id="${manufacturer.manufacturerId}">电子签章</a>
                        </c:if>

                        <c:if test="${manufacturer.signature eq 2}">
                            <div class="tip-wrap">
                                <i class="vd-icon icon-info2">
                                </i>
                            </div>
                        </c:if>
                    </div>
                </c:if>

                <c:if test="${manufacturer.status eq 5 and manufacturer.hasEditAuth}">
                    <%--<a class="btn btn-small J-submitCheck" data-type="1" data-id = "${manufacturer.manufacturerId}">提交审核</a>--%>
                    <div class="option-select-wrap J-option-select-wrap" data-id="${firstEngage.firstEngageId }">
                        <div class="option-select-btn J-option-select-icon">提交审核</div>
                        <div class="option-select-icon J-option-select-icon">
                            <i class="vd-icon icon-down"></i>
                        </div>
                        <div class="option-select-list">
                            <div class="option-select-item J-submitCheck" data-type="1" data-signature="0" data-id = "${manufacturer.manufacturerId}">提交审核</div>
                            <div class="option-select-item J-submitCheck-e" data-type="1" data-signature="1" data-id = "${manufacturer.manufacturerId}">提交审核并电子签章</div>
                        </div>
                    </div>

                </c:if>

                <c:if test="${manufacturer.status eq 1}">
                    <c:choose>
                        <c:when test="${manufacturer.hasCheckAuth}">
                            <a class="btn btn-small J-audit" data-type="3"
                               data-id="${manufacturer.manufacturerId }">审核通过</a>
                            <a class="btn btn-small J-audit" data-type="2"
                               data-id="${manufacturer.manufacturerId }">审核不通过</a>
                        </c:when>
                        <c:otherwise>
                            <a class="btn btn-small" style="color: grey">已申请审核</a>
                        </c:otherwise>
                    </c:choose>
                </c:if>
            </div>
        </div>
    </c:if>

    <div class="detail-block" id ="detail_block_inner">
            <div class="detail-table">
                <div class="table-item" id="manufacturer_inner_name">

                    <div class="table-th">
                        <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">

                        <c:choose>
                            <c:when test="${not empty param.weituo}">委托生产企业名称：</c:when>
                            <c:otherwise>生产企业名称：</c:otherwise>
                        </c:choose>
                    </div>
                    <div class="table-td">
                        ${manufacturer.manufacturerName}
                    </div>

                </div>

                <div class="table-item">
                    <div class="table-th">
                        审核状态：
                    </div>
                    <div class="table-td">
                        <c:choose>
                            <c:when test="${manufacturer.status == 5}">
                                <span style="color: red; ">待提交审核</span>
                            </c:when>
                            <c:when test="${manufacturer.status == 1}">
                                <span style="color: red; ">审核中</span>
                            </c:when>
                            <c:when test="${manufacturer.status == 2}">
                                <span style="color: red; ">审核不通过</span>

                            </c:when>
                            <c:when test="${manufacturer.status == 3}">
                                审核通过
                            </c:when>
                        </c:choose>
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-th">
                        营业执照：
                    </div>
                    <div class="table-td">
                        <div class="info-pic">
                            <c:if test="${not empty manufacturer.yzAttachments}">
                                <c:forEach var="attachments" items="${manufacturer.yzAttachments }">
                                    <div class="info-pic-item J-show-big"
                                         data-src="${api_http}${attachments.domain}${attachments.uri}">
                                        <img style="width:100%;height:100%"
                                             src="${api_http}${attachments.domain}${attachments.uri}" alt="">
                                    </div>

                                    <a class="printAtta" href="javascript:;">打印</a>

                                </c:forEach>
                            </c:if>

                            <c:if test="${not empty manufacturer.yzBAttachments }">
                                <c:forEach var="attachments" items="${manufacturer.yzBAttachments }">
                                    <div class="info-pic-item J-show-big"
                                         data-src="${api_http}${attachments.domain }${attachments.uri }">
                                        <img style="width:100%;height:100%"
                                             src="${api_http}${attachments.domain}${attachments.uri }" alt="">
                                            <%--                                                <a style="margin-top: 30px;color: #0C0C0C"></a>--%>
                                    </div>

                                    <a class="printAtta" href="javascript:;">打印（贝）</a>

                                </c:forEach>
                            </c:if>
                        </div>
                    </div>
                </div>
                <div class="table-item">
                    <div class="table-th">
                        营业执照发证日期：
                    </div>
                    <div class="table-td">
                        <c:if test="${null ne manufacturer.bcIssueDate and '' ne manufacturer.bcIssueDate}">
                            <fmt:formatDate value="${manufacturer.bcIssueDate}" pattern="yyyy-MM-dd"/>
                        </c:if>
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-th">营业执照有效期：
                    </div>
                    <div class="table-td">
                        <fmt:formatDate value="${manufacturer.bcStartTime}" pattern="yyyy-MM-dd"/>——
                        <fmt:formatDate value="${manufacturer.bcEndTime}" pattern="yyyy-MM-dd"/>
                    </div>
                </div>
                <div class="table-item">

                </div>

                <div class="table-item">
                    <div class="table-th">
                        生产企业生产许可证：
                    </div>
                    <div class="table-td">
                        <div class="info-pic">
                            <c:if test="${not empty manufacturer.scAttachments }">
                                <c:forEach var="attachments" items="${manufacturer.scAttachments }">
                                    <div class="info-pic-item J-show-big"
                                         data-src="${api_http}${attachments.domain }${attachments.uri }">
                                        <img style="width:100%;height:100%"
                                             src="${api_http}${attachments.domain }${attachments.uri }" alt="">
                                    </div>
                                    <a class="printAtta" href="javascript:;">打印</a>
                                </c:forEach>
                            </c:if>

                            <c:if test="${not empty manufacturer.scBAttachments }">
                                <c:forEach var="attachments" items="${manufacturer.scBAttachments }">
                                    <div class="info-pic-item J-show-big"
                                         data-src="${api_http}${attachments.domain }${attachments.uri }">
                                        <img style="width:100%;height:100%"
                                             src="${api_http}${attachments.domain }${attachments.uri }" alt="">
                                    </div>
                                    <a class="printAtta" href="javascript:;">打印（贝）</a>

                                </c:forEach>
                            </c:if>
                        </div>
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-th">
                        <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                        生产企业生产许可证号：
                    </div>
                    <div class="table-td">
                        ${manufacturer.productCompanyLicence}
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-th">生产企业生产许可证有效期：
                    </div>
                    <div class="table-td">
                        <fmt:formatDate value="${manufacturer.peStartTime}" pattern="yyyy-MM-dd"/>-
                        <fmt:formatDate value="${manufacturer.peEndTime}" pattern="yyyy-MM-dd"/>
                    </div>
                </div>
                <div class="table-item"></div>

                <div class="table-item">
                    <div class="table-th">
                        第一类医疗器械生产备案凭证：
                    </div>
                    <div class="table-td">
                        <div class="info-pic">
                            <c:if test="${not empty manufacturer.rcAttachments }">
                                <c:forEach var="attachments" items="${manufacturer.rcAttachments }">
                                    <div class="info-pic-item J-show-big"
                                         data-src="${api_http}${attachments.domain }${attachments.uri }">
                                        <img style="width:100%;height:100%"
                                             src="${api_http}${attachments.domain }${attachments.uri }" alt="">
                                    </div>
                                    <a class="printAtta" href="javascript:;">打印</a>
                                </c:forEach>
                            </c:if>

                            <c:if test="${not empty manufacturer.rcBAttachments }">
                                <c:forEach var="attachments" items="${manufacturer.rcBAttachments }">
                                    <div class="info-pic-item J-show-big"
                                         data-src="${api_http}${attachments.domain }${attachments.uri }">
                                        <img style="width:100%;height:100%"
                                             src="${api_http}${attachments.domain }${attachments.uri }" alt="">
                                    </div>
                                    <a class="printAtta" href="javascript:;">打印（贝）</a>

                                </c:forEach>
                            </c:if>
                        </div>
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-th">
                        <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                        第一类医疗器械生产备案编号：
                    </div>
                    <div class="table-td">
                        ${manufacturer.recordCertificateLicence}
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-th">第一类医疗器械生产备案有效期：
                    </div>
                    <div class="table-td">
                        <fmt:formatDate value="${manufacturer.rcStartTime}" pattern="yyyy-MM-dd"/>-
                        <fmt:formatDate value="${manufacturer.rcEndTime}" pattern="yyyy-MM-dd"/>
                    </div>
                </div>
                <div class="table-item"></div>

                <div class="table-item">
                    <div class="table-th">生产企业生产产品登记表：
                    </div>
                    <div class="table-td">
                        <div class="info-pic">
                            <c:if test="${not empty manufacturer.djbAttachments}">
                                <c:forEach var="attachments"
                                           items="${manufacturer.djbAttachments}">

                                    <div class="info-pic-item J-show-big"
                                         data-src="${api_http}${attachments.domain }${attachments.uri }">
                                        <img style="width:100%;height:100%"
                                             src="${api_http}${attachments.domain }${attachments.uri }"
                                             alt="">
                                    </div>
                                    <a class="printAtta" href="javascript:;">打印</a>
                                </c:forEach>
                            </c:if>

                            <c:if test="${not empty manufacturer.djbBAttachments }">
                                <c:forEach var="attachments"
                                           items="${manufacturer.djbBAttachments }">

                                    <div class="info-pic-item J-show-big"
                                         data-src="${api_http}${attachments.domain }${attachments.uri }">
                                        <img style="width:100%;height:100%"
                                             src="${api_http}${attachments.domain }${attachments.uri }"
                                             alt="">
                                    </div>
                                    <a class="printAtta" href="javascript:;">打印（贝）</a>
                                </c:forEach>
                            </c:if>
                        </div>
                    </div>
                </div>
                <div class="table-item">
                    <div class="table-th">
                        生产企业生产产品登记表有效期:
                    </div>
                    <div class="table-td">
                        <fmt:formatDate value="${manufacturer.pepStartTime}" pattern="yyyy-MM-dd"/>——
                        <fmt:formatDate value="${manufacturer.pepEndTime}" pattern="yyyy-MM-dd"/>
                    </div>
                </div>

            </div>
     </div>

    <c:if test="${notShowCheck ne 0}">
        <div class="detail-block" id ="detail_block_inner11">
            <div class="block-title" id = "manufacturer_inner">生产企业审核记录</div>
            <table class="table table-base table-hover base-form J-table-wrap">
                <colgroup>
                    <col width="14.5%">
                    <col width="14.5%">
                    <col width="14.5%">
                    <col width="14.5%">
                    <col width="14.5%">
                </colgroup>
                <tbody>
                <tr>
                    <th>操作时间</th>
                    <th>操作人</th>
                    <th>操作事项</th>
                    <th>备注</th>
                    <th>审核状态</th>
                </tr>
                <c:if test="${empty checkList}">
                    <tr>
                        <td class="no-data" colspan='9'>
                            <div><i class="vd-icon icon-caution1"></i></div>
                            没有匹配的数据
                        </td>
                    </tr>
                </c:if>
                <c:if test="${not empty checkList}">
                    <c:forEach var="checkData" items="${checkList}">
                        <tr>
                            <td><fmt:formatDate value="${checkData.addTime}" pattern="yyyy-MM-dd HH:mm:ss"/></td>
                            <td>${checkData.creatorName}</td>
                            <td>${checkData.logStatusName}</td>
                            <td>${checkData.logMessage}</td>
                            <td>
                                <div class="stand-wrap">
                                    <c:if test="${checkData.logStatus eq 5}">
                                        待提交审核
                                    </c:if>
                                    <c:if test="${checkData.logStatus eq 1}">
                                        审核中
                                    </c:if>
                                    <c:if test="${checkData.logStatus eq 2}">
                                        审核不通过
                                    </c:if>
                                    <c:if test="${checkData.logStatus eq 3}">
                                        审核通过
                                    </c:if>
                                </div>
                            </td>
                        </tr>
                    </c:forEach>
                </c:if>
                </tbody>
            </table>
        </div>
    </c:if>


</div>


<!-- 判断是否被嵌入 -->
    <c:choose >
        <c:when test="${empty param.isFrame}">
    <script type="text/tmpl" class="J-audit-tmpl">
        <div class="del-wrap">
            <div class="del-tip">
                <i class="vd-icon icon-caution2"></i>确定{{=auditTip}}么？
            </div>
            {{ if(type != 3){ }}
            <form class="J-audit-form">
                <div class="del-cnt base-form">
                    <textarea name="content" id="" cols="30" rows="10" class="input-textarea" placeholder="必填：请填写{{=auditTip}}原因，最多64个字"></textarea>
                </div>
            </form>
            {{ } }}
        </div>

    </script>
    <script type="text/tmpl" class="J-del-tmpl">
        <div class="del-wrap">
            <div class="del-tip">
                <i class="vd-icon icon-caution2"></i>确认删除该生产企业？
            </div>
        </div>
    </script>
    <script src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/util.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/select.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/static/new/js/common/artDialog/2.0.0/artDialog.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/pages/modules/list.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/static/new/js/common/template.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/static/new/js/common/jquery.validate.js"></script>
    <script src="${pageContext.request.contextPath}/static/new/js/pages/manufacturer/product/view.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/libs/jquery/plugins/layer/layer.js"></script>
    <script type="text/javascript"
            src='${pageContext.request.contextPath}/static/js/jquery.PrintArea.js'></script>

<%@ include file="../../common/footer.jsp"%>
        </c:when>
        <c:otherwise>
            </body>
            </html>
        </c:otherwise>
    </c:choose>