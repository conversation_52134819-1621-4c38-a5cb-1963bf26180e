package com.vedeng.erp.broadcast.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 业绩目标Excel导入DTO
 */
@Data
public class BroadcastTargetImportDto {

    /**
     * 年度（必填）
     */
    @ExcelProperty("年度*")
    private Integer targetYear;

    /**
     * 员工姓名（个人目标Sheet使用）
     */
    @ExcelProperty("员工*")
    private String employeeName;

    /**
     * 小组名称（小组目标Sheet使用）
     */
    @ExcelProperty("小组*")
    private String groupName;

    /**
     * 部门名称（部门目标Sheet使用）
     */
    @ExcelProperty("部门*")
    private String deptName;

    /**
     * 1月目标
     */
    @ExcelProperty("1月")
    private BigDecimal month1;

    /**
     * 2月目标
     */
    @ExcelProperty("2月")
    private BigDecimal month2;

    /**
     * 3月目标
     */
    @ExcelProperty("3月")
    private BigDecimal month3;

    /**
     * 4月目标
     */
    @ExcelProperty("4月")
    private BigDecimal month4;

    /**
     * 5月目标
     */
    @ExcelProperty("5月")
    private BigDecimal month5;

    /**
     * 6月目标
     */
    @ExcelProperty("6月")
    private BigDecimal month6;

    /**
     * 7月目标
     */
    @ExcelProperty("7月")
    private BigDecimal month7;

    /**
     * 8月目标
     */
    @ExcelProperty("8月")
    private BigDecimal month8;

    /**
     * 9月目标
     */
    @ExcelProperty("9月")
    private BigDecimal month9;

    /**
     * 10月目标
     */
    @ExcelProperty("10月")
    private BigDecimal month10;

    /**
     * 11月目标
     */
    @ExcelProperty("11月")
    private BigDecimal month11;

    /**
     * 12月目标
     */
    @ExcelProperty("12月")
    private BigDecimal month12;

    /**
     * 获取指定月份的目标金额
     */
    public BigDecimal getMonthTarget(int month) {
        switch (month) {
            case 1: return month1;
            case 2: return month2;
            case 3: return month3;
            case 4: return month4;
            case 5: return month5;
            case 6: return month6;
            case 7: return month7;
            case 8: return month8;
            case 9: return month9;
            case 10: return month10;
            case 11: return month11;
            case 12: return month12;
            default: return null;
        }
    }

    /**
     * 设置指定月份的目标金额
     */
    public void setMonthTarget(int month, BigDecimal amount) {
        switch (month) {
            case 1: month1 = amount; break;
            case 2: month2 = amount; break;
            case 3: month3 = amount; break;
            case 4: month4 = amount; break;
            case 5: month5 = amount; break;
            case 6: month6 = amount; break;
            case 7: month7 = amount; break;
            case 8: month8 = amount; break;
            case 9: month9 = amount; break;
            case 10: month10 = amount; break;
            case 11: month11 = amount; break;
            case 12: month12 = amount; break;
        }
    }
}
