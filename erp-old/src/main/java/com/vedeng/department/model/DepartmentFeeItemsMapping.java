package com.vedeng.department.model;

public class DepartmentFeeItemsMapping {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_DEPARTMENT_FEE_ITEMS_MAPPING.DEPARTMENT_FEE_ITEMS_MAPPING_ID
     *
     * @mbg.generated Tue Apr 09 19:02:47 CST 2019
     */
    private Integer departmentFeeItemsMappingId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_DEPARTMENT_FEE_ITEMS_MAPPING.DEPARTMENT_ID
     *
     * @mbg.generated Tue Apr 09 19:02:47 CST 2019
     */
    private Integer departmentId;

    /**
     * 收费项目
     */
    private String feePro;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_DEPARTMENT_FEE_ITEMS_MAPPING.DEPARTMENT_FEE_ITEMS_ID
     *
     * @mbg.generated Tue Apr 09 19:02:47 CST 2019
     */
    private Integer departmentFeeItemsId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_DEPARTMENT_FEE_ITEMS_MAPPING.UPDATER
     *
     * @mbg.generated Tue Apr 09 19:02:47 CST 2019
     */
    private Integer updater;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_DEPARTMENT_FEE_ITEMS_MAPPING.MOD_TIME
     *
     * @mbg.generated Tue Apr 09 19:02:47 CST 2019
     */
    private Long modTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_DEPARTMENT_FEE_ITEMS_MAPPING.CREATOR
     *
     * @mbg.generated Tue Apr 09 19:02:47 CST 2019
     */
    private Integer creator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_DEPARTMENT_FEE_ITEMS_MAPPING.ADD_TIME
     *
     * @mbg.generated Tue Apr 09 19:02:47 CST 2019
     */
    private Long addTime;

    public String getFeePro() {
        return feePro;
    }

    public void setFeePro(String feePro) {
        this.feePro = feePro;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_DEPARTMENT_FEE_ITEMS_MAPPING.DEPARTMENT_FEE_ITEMS_MAPPING_ID
     *
     * @return the value of T_DEPARTMENT_FEE_ITEMS_MAPPING.DEPARTMENT_FEE_ITEMS_MAPPING_ID
     *
     * @mbg.generated Tue Apr 09 19:02:47 CST 2019
     */
    public Integer getDepartmentFeeItemsMappingId() {
        return departmentFeeItemsMappingId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_DEPARTMENT_FEE_ITEMS_MAPPING.DEPARTMENT_FEE_ITEMS_MAPPING_ID
     *
     * @param departmentFeeItemsMappingId the value for T_DEPARTMENT_FEE_ITEMS_MAPPING.DEPARTMENT_FEE_ITEMS_MAPPING_ID
     *
     * @mbg.generated Tue Apr 09 19:02:47 CST 2019
     */
    public void setDepartmentFeeItemsMappingId(Integer departmentFeeItemsMappingId) {
        this.departmentFeeItemsMappingId = departmentFeeItemsMappingId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_DEPARTMENT_FEE_ITEMS_MAPPING.DEPARTMENT_ID
     *
     * @return the value of T_DEPARTMENT_FEE_ITEMS_MAPPING.DEPARTMENT_ID
     *
     * @mbg.generated Tue Apr 09 19:02:47 CST 2019
     */
    public Integer getDepartmentId() {
        return departmentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_DEPARTMENT_FEE_ITEMS_MAPPING.DEPARTMENT_ID
     *
     * @param departmentId the value for T_DEPARTMENT_FEE_ITEMS_MAPPING.DEPARTMENT_ID
     *
     * @mbg.generated Tue Apr 09 19:02:47 CST 2019
     */
    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_DEPARTMENT_FEE_ITEMS_MAPPING.DEPARTMENT_FEE_ITEMS_ID
     *
     * @return the value of T_DEPARTMENT_FEE_ITEMS_MAPPING.DEPARTMENT_FEE_ITEMS_ID
     *
     * @mbg.generated Tue Apr 09 19:02:47 CST 2019
     */
    public Integer getDepartmentFeeItemsId() {
        return departmentFeeItemsId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_DEPARTMENT_FEE_ITEMS_MAPPING.DEPARTMENT_FEE_ITEMS_ID
     *
     * @param departmentFeeItemsId the value for T_DEPARTMENT_FEE_ITEMS_MAPPING.DEPARTMENT_FEE_ITEMS_ID
     *
     * @mbg.generated Tue Apr 09 19:02:47 CST 2019
     */
    public void setDepartmentFeeItemsId(Integer departmentFeeItemsId) {
        this.departmentFeeItemsId = departmentFeeItemsId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_DEPARTMENT_FEE_ITEMS_MAPPING.UPDATER
     *
     * @return the value of T_DEPARTMENT_FEE_ITEMS_MAPPING.UPDATER
     *
     * @mbg.generated Tue Apr 09 19:02:47 CST 2019
     */
    public Integer getUpdater() {
        return updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_DEPARTMENT_FEE_ITEMS_MAPPING.UPDATER
     *
     * @param updater the value for T_DEPARTMENT_FEE_ITEMS_MAPPING.UPDATER
     *
     * @mbg.generated Tue Apr 09 19:02:47 CST 2019
     */
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_DEPARTMENT_FEE_ITEMS_MAPPING.MOD_TIME
     *
     * @return the value of T_DEPARTMENT_FEE_ITEMS_MAPPING.MOD_TIME
     *
     * @mbg.generated Tue Apr 09 19:02:47 CST 2019
     */
    public Long getModTime() {
        return modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_DEPARTMENT_FEE_ITEMS_MAPPING.MOD_TIME
     *
     * @param modTime the value for T_DEPARTMENT_FEE_ITEMS_MAPPING.MOD_TIME
     *
     * @mbg.generated Tue Apr 09 19:02:47 CST 2019
     */
    public void setModTime(Long modTime) {
        this.modTime = modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_DEPARTMENT_FEE_ITEMS_MAPPING.CREATOR
     *
     * @return the value of T_DEPARTMENT_FEE_ITEMS_MAPPING.CREATOR
     *
     * @mbg.generated Tue Apr 09 19:02:47 CST 2019
     */
    public Integer getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_DEPARTMENT_FEE_ITEMS_MAPPING.CREATOR
     *
     * @param creator the value for T_DEPARTMENT_FEE_ITEMS_MAPPING.CREATOR
     *
     * @mbg.generated Tue Apr 09 19:02:47 CST 2019
     */
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_DEPARTMENT_FEE_ITEMS_MAPPING.ADD_TIME
     *
     * @return the value of T_DEPARTMENT_FEE_ITEMS_MAPPING.ADD_TIME
     *
     * @mbg.generated Tue Apr 09 19:02:47 CST 2019
     */
    public Long getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_DEPARTMENT_FEE_ITEMS_MAPPING.ADD_TIME
     *
     * @param addTime the value for T_DEPARTMENT_FEE_ITEMS_MAPPING.ADD_TIME
     *
     * @mbg.generated Tue Apr 09 19:02:47 CST 2019
     */
    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }
}