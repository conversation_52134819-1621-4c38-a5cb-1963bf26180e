package com.rabbitmq;

import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
public class ImMsgProducer  extends MsgProducer implements InitializingBean {

    @Autowired
    @Qualifier(value = "imRabbitTemplate")
    private RabbitTemplate imRabbitTemplate;

    @Override
    public void afterPropertiesSet() throws Exception {
//        imRabbitTemplate.setConfirmCallback(this);
        setRabbitTemplate(imRabbitTemplate);
    }
}
