package com.vedeng.erp.finance.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class PayApplyReqDto{
    private String buyorderNo; // 订单号
    private String buyorderTraderName; // 供应商名称
    private String traderName; // 收款名称
    private Integer traderSubject; // 交易主体
    private Integer validStatus; // 审核状态
    private Integer isBill; // 制单状态
    private BigDecimal searchBeginAmount; // 订单金额-左区间
    private BigDecimal searchEndAmount; // 订单金额-右区间
    private Long searchBegintime; // 申请时间-左区间
    private Long searchEndtime; // 申请时间-右区间
    private Long searchPayBegintime; // 流水交易时间-左区间
    private Long searchPayEndtime;// 流水交易时间-右区间
    private Integer comments; // 内部付款备注 -1全部 1有 0无
    private Integer accountType; // 往来单位类型 0客户 1供应商
    private Integer autoBill; // 满足自动制单 全部 1是 0否
    private Integer billMethod; // 制单方式 全部 0手动 1自动
}
