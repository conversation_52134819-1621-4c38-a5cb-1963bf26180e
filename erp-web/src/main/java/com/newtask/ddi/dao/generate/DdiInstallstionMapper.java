package com.newtask.ddi.dao.generate;


import com.wms.model.ddi.generate.DdiInstallstion;

public interface DdiInstallstionMapper {
    int deleteByPrimaryKey(Integer tDdiInstallstionId);

    int insert(DdiInstallstion record);

    int insertSelective(DdiInstallstion record);

    DdiInstallstion selectByPrimaryKey(Integer tDdiInstallstionId);

    int updateByPrimaryKeySelective(DdiInstallstion record);

    int updateByPrimaryKey(DdiInstallstion record);
}