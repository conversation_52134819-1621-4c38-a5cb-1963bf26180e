package com.wms.inventorytransfer.model.dto;

import java.io.Serializable;

/**
 * 库存转移详情数据传输对象
 * <AUTHOR>
 * @date 2020/7/20 10:15:20
 */
public class InventoryTransferDetailDto implements Serializable {
    /**
     * 库存转移单详情单ID
     */
    private Integer inventoryTransferDetailId;

    /**
     * 商品唯一编码
     */
    private String skuNo;

    /**
     * 转移数量
     */
    private Integer num;

    /**
     * WMS转移成功数量
     */
    private Integer transferNum;

    /**
     * 来源逻辑仓
     */
    private Integer fromWarehouseId;

    /**
     * 目标逻辑仓
     */
    private Integer toWarehouseId;

    public Integer getInventoryTransferDetailId() {
        return inventoryTransferDetailId;
    }

    public void setInventoryTransferDetailId(Integer inventoryTransferDetailId) {
        this.inventoryTransferDetailId = inventoryTransferDetailId;
    }


    public String getSkuNo() {
        return skuNo;
    }

    public void setSkuNo(String skuNo) {
        this.skuNo = skuNo;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public Integer getTransferNum() {
        return transferNum;
    }

    public void setTransferNum(Integer transferNum) {
        this.transferNum = transferNum;
    }

    public Integer getFromWarehouseId() {
        return fromWarehouseId;
    }

    public void setFromWarehouseId(Integer fromWarehouseId) {
        this.fromWarehouseId = fromWarehouseId;
    }

    public Integer getToWarehouseId() {
        return toWarehouseId;
    }

    public void setToWarehouseId(Integer toWarehouseId) {
        this.toWarehouseId = toWarehouseId;
    }
}
