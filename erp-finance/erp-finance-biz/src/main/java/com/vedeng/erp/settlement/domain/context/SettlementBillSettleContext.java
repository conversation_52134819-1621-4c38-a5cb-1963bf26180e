package com.vedeng.erp.settlement.domain.context;

import com.vedeng.common.core.enums.SettlementTypeEnum;
import com.vedeng.erp.finance.dto.CapitalBillDto;
import com.vedeng.erp.settlement.domain.dto.SettlementBillDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 结算单 结算参数上下文类
 * @date 2023/11/27 17:07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SettlementBillSettleContext{

    /**
     * 付款申请单id
     */
    private Integer payApplyId;

    /**
     * 需要结算的金额
     */
    private BigDecimal settleAmount;

    /**
     * 结算单
     */
    private SettlementBillDto settlementBillDto;

    /**
     * 资金流水
     */
    private CapitalBillDto capitalBillDto;

    /**
     * 结算单类型
     */
    SettlementTypeEnum settlementTypeEnum;
}
