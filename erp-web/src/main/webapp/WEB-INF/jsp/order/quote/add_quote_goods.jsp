<%@ page language="java" contentType="text/html; charset=UTF-8"	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="添加产品" scope="application" />
<%@ include file="../../common/common.jsp"%>
<%@ include file="../../component/remarkComponent.jsp"%>
<script type="text/javascript" src="<%= basePath %>static/js/jquery/ajaxfileupload.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src='<%=basePath%>/static/js/order/quote/add_quote_goods.js?rnd=${resourceVersionKey}'></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/select.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/pages/goods/vgoods/sku_edit.js?rnd=${resourceVersionKey}"></script>
<style>
	.lableCss{
				  float: left;
				  text-align: right;
				  white-space: nowrap;
				  text-overflow: ellipsis;
				  overflow: hidden;
				  margin: 3px 10px 0px 6px;
	}
</style>
</head>
<body>
	<div class="formpublic formpublic1">
		<div>
			<ul>
				<li>
					<div class="infor_name" style='margin-top:0px;'>
						<label>产品添加方式</label>
					</div>
					<div class="f_left inputfloat inputfloatmb0">
						<ul>
							<li class="mb0 controller"><input type="radio" name="abc" checked value="select"> <label>产品库搜索</label></li>
							<li class="mb0 controller"><input type="radio" name="abc" value="input"> <label>手动填写</label></li>
							<c:if test="${optType ne 1}">
							<li class="font-grey9">（手动填写的产品无法被添加到订单）</li>
							</c:if>
						</ul>
					</div>
				</li>
			</ul>
			<!-- ------------产品数据列表--------start------- -->
			<div class="controlled" id="goodsListDiv">
				<!-- 搜索表格出来 -->
				<ul class="searchTable">
					<li>
						<form method="post" id="search" action="<%=basePath%>order/quote/addQuoteGoods.do?quoteorderId=${quoteorderId}">
							<input type="hidden" name="optType" value="${optType}"/>
							<input type="hidden" name="traderId" value="${traderId}"/>
							<input type='hidden' name='dwhTerminalId' id='dwhTerminalId_search' value=''>
							<input type='hidden' name='unifiedSocialCreditIdentifier' id='unifiedSocialCreditIdentifier_search' value=''>
							<input type='hidden' name='organizationCode' id='organizationCode_search' value=''>
							<input type='hidden' name='provinceId' id='provinceId_search' value=''>
							<input type='hidden' name='cityId' id='cityId_search' value=''>
							<input type='hidden' name='areaId' id='areaId_search' value=''>
							<input type='hidden' name='provinceName' id='provinceName_search' value=''>
							<input type='hidden' name='cityName' id='cityName_search' value=''>
							<input type='hidden' name='areaName' id='areaName_search' value=''>

							<div class="lableCss ">
								<span style="color: red">*</span> <label>产品名称</label>
							</div>
							<div class="f_left ">
								<div class="mb10">
									<input type="text" class="input-middle mr5" placeholder="请输入产品名称/订货号/品牌/型号等关键词" id="searchContent" name="searchContent" value="${searchContent}">
									<input type="hidden" id="salesAreaId" name="salesAreaId" value="">
								</div>

							</div >

							<div class="lableCss ">
								<span style="color: red">*</span> <label>品牌</label>
							</div>

							<div class="f_left">
								<input type="text" class="input-smaller96 " placeholder="请输入品牌" id="searchBrandStr" name="searchBrandStr" value="${searchBrandStr}">
							</div>

							<div class="lableCss ">
								<span style="color: red">*</span> <label>规格/型号</label>
							</div>

							<div class="f_left">
								<input type="text" class="input-smaller96 " placeholder="请输入规格/型号" id="searchModelSpec" name="searchModelSpec" value="${searchModelSpec}">
							</div>

							<div class="lableCss ">
								<span style="color: red">*</span> <label>单位</label>
							</div>

							<div class="f_left">
								<div class="form-fields"  id="unitHidden">
									<select  class="J-select J-base-sku-unit J-sku-unit sku-unit-select" name="baseUnitId" id="baseUnitId" style= "width:100px;height: 28px">
										<option value="">请选择单位</option>
										<c:if test="${not empty unitList }">
											<c:forEach var="unit" items="${unitList}">
												<option value="${unit.unitId}" <c:if test="${ baseUnitId == unit.unitId  }"> selected
												</c:if> >${unit.unitName}</option>
											</c:forEach>
										</c:if>
									</select>

								</div>
							</div>

							<div class="f_left" style="margin-left: 10px">
								<span class="bt-bg-style bt-small bg-light-blue" onclick="search2();" id="errorMes">搜索</span>
							</div>
						</form>
						<div>
							<table
								class="table table-bordered table-striped table-condensed table-centered mb10">
								<thead>
									<th class="table-smallest12">订货号</th>
									<th>产品名称</th>
									<th class="table-smallest15">品牌</th>
									<th class="table-smallest12">型号</th>
									<th class="table-smallest5">单位</th>
									<!-- <th>物料编码</th> -->
									<th class="table-smallest">商品等级</th>
									<th class="table-smallest">商品档位</th>
									<th class="table-smallest5">库存</th>
									<th class="table-smallest8">审核状态</th>
									<th class="table-smallest6">选择</th>
								</thead>
								<tbody>
									<c:forEach var="list" items="${goodsList}" varStatus="status">
										<tr>
											<td>${list.sku}</td>
											<td><c:if test="${list.source == 1}"><span style="color: red">【医械购】</span></c:if>
												<a class="pop-new-data"  layerParams='{"width":"90%","height":"80%","title":"商品信息聚合页",
												"link":"${pageContext.request.contextPath}/goods/vgoods/viewSku.do?skuId=${list.goodsId}&spuId=${list.spuId}&pageType=1", "noEncodeURI": true}'>${list.goodsName}</a>

											</td>
											<td>${list.brandName}</td>
											<td>${list.model}</td>
											<td>${list.unitName}</td>
											<%-- <td>${list.materialCode}</td> --%>
											<td>${list.goodsLevelName}</td>
											<td>${list.goodsPositionName}</td>
											<td>${list.stockNum}</td>
											<td>
												<c:if test="${list.verifyStatus eq 0}">待完善</c:if>
												<c:if test="${list.verifyStatus eq 1}">审核中</c:if>
												<c:if test="${list.verifyStatus eq 2}">审核不通过</c:if>
												<c:if test="${list.verifyStatus eq 3}">审核通过</c:if>
												<c:if test="${list.verifyStatus eq 5}">待提交审核</c:if>
											</td>
											<td>
												<!-- oldVerifyStatus 代表老商品流审核通过-->
												<c:if test="${list.verifyStatus eq 3}">
													<c:if test="${optType ne 1}">
												<a href="javascript:void(0);"
													onclick="selectGoods('${list.goodsId}','${list.sku}','<c:out value='${list.goodsName}' escapeXml="true"></c:out>','${list.brandName}','${list.model}',
                                                            '${list.unitName}','${list.goodsLevelName}','${list.proUserName}','${list.verifyStatus}','${list.channelPrice}',
															'${list.checkPrice}','${list.checkPriceStr}',
                                                            '${list.directDeliveryTimeStart}','${list.directDeliveryTimeEnd}','${list.commonDeliveryTimeStart}','${list.commonDeliveryTimeEnd}');">选择</a>
													</c:if>
													<c:if test="${optType eq 1}">
														<a href="javascript:void(0);"
														   onclick="selectGoods2('${list.goodsId}','${traderId}');">选择报价产品</a>
													</c:if>
												</c:if>
											</td>
										</tr>
									</c:forEach>
									<c:if test="${empty goodsList}">
									<!-- 查询无结果弹出 -->
									<tr>
										<td colspan='10'>
											查询无结果！请尝试使用其他搜索条件。该商品可能不存在,可以尝试手动填写.
										</td>
									</tr>
									</c:if>
								</tbody>
							</table>
						</div>
					</li>
					<tags:page page="${page}"/>
					<div class="clear"></div>
				</ul>
			</div>
			<!-- ------------产品数据列表--------end------- -->

			<!-- ------------选择列表产品后操作--------start------- -->
			<div class="controlled none" id="confirmGoodsDiv">
				<!-- 搜索最后结果lastResult -->
				<form id="confirmForm">
					<input type="hidden" name="formToken" value="${formToken}"/>
					<input type="hidden" name="goodsId" id="goodsId"/>
					<input type="hidden" name="sku" id="sku"/>
					<input type="hidden" name="goodsName" id="goodsName"/>
					<input type="hidden" name="brandName" id="brandName"/>
					<input type="hidden" name="model" id="model"/>
					<input type="hidden" name="unitName" id="unitName"/>
					<input type='hidden' name='dwhTerminalId' id='dwhTerminalId_confirmForm' value=''>
					<input type='hidden' name='unifiedSocialCreditIdentifier' id='unifiedSocialCreditIdentifier_confirmForm' value=''>
					<input type='hidden' name='organizationCode' id='organizationCode_confirmForm' value=''>

					<input type='hidden' name='provinceId' id='provinceId_confirmForm' value=''>
					<input type='hidden' name='cityId' id='cityId_confirmForm' value=''>
					<input type='hidden' name='areaId' id='areaId_confirmForm' value=''>
					<input type='hidden' name='provinceName' id='provinceName_confirmForm' value=''>
					<input type='hidden' name='cityName' id='cityName_confirmForm' value=''>
					<input type='hidden' name='areaName' id='areaName_confirmForm' value=''>
					<ul class="lastResult">
						<!-- 终端客户属性和区域 -->
						<input type='hidden' name='terminalTraderName' id='terminalTraderName' value=''>
						<input type='hidden' name='terminalTraderId' id='terminalTraderId' value=''>
						<input type='hidden' name='terminalTraderType' id='terminalTraderType' value=''>
						<input type='hidden' name='salesArea' id='salesArea' value=''>
						<input type='hidden' name='salesAreaId' id='salesAreaId' value=''>
                        <input type="hidden" name="directDeliveryTimeStart" id="directDeliveryTimeStart" value="">
                        <input type="hidden" name="directDeliveryTimeEnd" id="directDeliveryTimeEnd" value="">
                        <input type="hidden" name="commonDeliveryTimeStart" id="commonDeliveryTimeStart" value="">
                        <input type="hidden" name="commonDeliveryTimeEnd" id="commonDeliveryTimeEnd" value="">
						<li>
							<div class="infor_name ">
								<span>*</span> <label>产品名称</label>
							</div>
							<div class="f_left table-largest content1">
								<div class="">
									<a class="font-blue mr10 productname2 addtitle2" id="confirmGoodsName"></a>
									<span class="bt-bg-style bt-small bg-light-blue searchAgain" onclick="againSearch();">重新搜索</span>
								</div>
							</div>
						</li>
						<li>
							<div class="infor_name mt0">
								<label>品牌/型号</label>
							</div>
							<div class="f_left" id="confirmGoodsBrandNameModel"></div>
						</li>
						<li>
							<div class="infor_name ">
								<label>产品信息</label>
							</div>
							<div class="f_left mr10" id="confirmGoodsContent"></div>
							<span class="font-grey9" >产品未通过审核时，不允许转化到订单中</span>
						</li>
						<li>
							<div class="infor_name ">
								<label>报价</label>
							</div>
							<div class="f_left">
								<input type="text" class="input-middle mr5" name="price" id="price" onkeyup="confirmTotalMoney('price');"> 
						
								<span class="font-grey9" id="priceError">
								<c:if test="${quoteGoods.checkPrice == null}">
									<c:if test="${quoteGoods.channelPrice==null}">
										<c:if test="${quoteGoods.avgPrice==null}">
											<span id="goodsChannelPrice">报价平均价（近12个月）：无平均价信息，请向产品部咨询</span>
										</c:if>
										<c:if test="${quoteGoods.avgPrice!=null}">
											<span id="goodsChannelPrice">报价平均价（近12个月）：<fmt:formatNumber type="number" value="${quoteGoods.avgPrice==null?0:quoteGoods.avgPrice}" pattern="0.00" maxFractionDigits="2"/></span>

										</c:if>
									</c:if>
									<c:if test="${quoteGoods.channelPrice!=null}"><span id="goodsChannelPrice">核价参考价格：<fmt:formatNumber type="number" value="${quoteGoods.channelPrice==null?0:quoteGoods.channelPrice}" pattern="0.00" maxFractionDigits="2"/></span></c:if>
								</c:if>
								<c:if test="${quoteGoods.checkPrice != null}">
									${quoteGoods.checkPriceStr}
								</c:if>
								</span>
							</div>
						</li>
						<li>
							<div class="infor_name ">
								<span>*</span> <label>数量</label>
							</div>
							<div class="f_left">
								<input type="text" class="input-middle" name="num" id="num" onkeyup="confirmTotalMoney('num');"><!-- 不使用onblur因为提交时有影响 -->
							</div>
						</li>
						<li>
							<div class="infor_name  mt0">
								<span>*</span> <label>单位</label>
							</div>
							<div class="f_left" id="confirmUnitName"></div>
						</li>
						<li>
							<div class="infor_name  mt0">
								<label>总额</label>
							</div>
							<div class="f_left" id="confirmTotalMoney"></div>
						</li>
						<li>
							<div class="infor_name mt0">
								<label>报价含安调</label>
							</div>
							<div class="f_left inputfloat inputfloatmb0">
								<ul>
									<li><input type="radio" name="installation" value="1" > <label>是</label></li>
									<li><input type="radio" name="installation" value="0" checked> <label>否</label></li>
								</ul>
								<input type="hidden" name="haveInstallation" id="haveInstallation"/>
							</div>
						</li>
						<li>
							<div class="infor_name ">
								<label>货期</label>
							</div>
							<div class="f_left">
								<input type="text" class="input-middle mr5" name="deliveryCycle" id="deliveryCycle">
                                <span class="font-grey9 mt4" id="deliveryCycleFlag"></span>
							</div>
						</li>
						<li>
							<div class="infor_name">
								<label>是否直发</label>
							</div>
							<div class="f_left inputfloat inputfloatmb0">
								<ul>
									<li class="mt4">
                                        <input type="radio" name="deliveryDirectRad" value="0" checked onclick="showDeliveryCycle(this, 0);">
                                        <label>否</label>
                                    </li>
									<li class="mt4">
                                        <input type="radio" name="deliveryDirectRad" value="1" onclick="showDeliveryCycle(this, 1);">
                                        <label>是</label>
                                    </li>
									<li><input type="text" placeholder="请填写直发原因，含有直发商品的订单不允许提前采购" class="input-larger" name="deliveryDirectComments" id="deliveryDirectComments"></li>
								</ul>
								<input type="hidden" name="deliveryDirect" id="deliveryDirect">
							</div>
						</li>
						<li>
							<div class="infor_name ">
								<label>内部备注</label>
							</div>
							<div class="customername pos_rel f_left">
								<textarea class="input-largest textarea-smallest" placeholder="内部备注不对外显示，最多支持512个字符。" name="insideComments"
										  id="insideComments" label_data=""></textarea>
								<div class="pos_abs customernameshow" label_left=50" style="width: 500px; top: 25px;background-color: #00CD66;">
								</div>
							</div>
						</li>
						<li>
							<div class="infor_name ">
								<label>产品备注</label>
							</div>
							<div class="f_left">
								<div>
									<input type="text" class="input-largest mr5" placeholder="产品备注对外显示" name="goodsComments" id="goodsComments">
								</div>
								<div class="font-grey9 mt5">友情提示<br/>1、如果您的操作导致报价单金额发生变化，可能需要重新编辑付款计划；</div>
							</div>
						</li>
					</ul>
					<input type="hidden" name="quoteorderId" id="quoteorderId" value="${quoteorderId}"/>
					<input type="hidden" name="isTemp" id="isTemp" value="0"/><!-- 是否临时产品 -->
					<div class="add-tijiao  tcenter">
						<button class="bt-bg-style bg-deep-green" type="button" onClick="confirmSubmit();">提交</button>
						<button id="close-layer" type="button" class="dele">取消</button>
					</div>
				</form>
			</div>
			<!-- ------------选择列表产品后操作--------end------- -->
			
			<!-- ------------手动输入产品信息----------start----- -->
			<div class="controlled none" id="inputGoodsDiv">
				<form id="inputForm">
					<input type="hidden" name="formToken" value="${formToken}"/>
					<!-- 终端客户属性和区域 -->
					<input type='hidden' name='terminalTraderName' id='terminalTraderName' value=''>
					<input type='hidden' name='terminalTraderId' id='terminalTraderId' value=''>
					<input type='hidden' name='terminalTraderType' id='terminalTraderType' value=''>
					<input type='hidden' name='salesArea' id='salesArea' value=''>
					<input type='hidden' name='dwhTerminalId' id='dwhTerminalId_inputForm' value=''>
					<input type='hidden' name='unifiedSocialCreditIdentifier' id='unifiedSocialCreditIdentifier_inputForm' value=''>
					<input type='hidden' name='organizationCode' id='organizationCode_inputForm' value=''>

					<input type='hidden' name='provinceId' id='provinceId_inputForm' value=''>
					<input type='hidden' name='cityId' id='cityId_inputForm' value=''>
					<input type='hidden' name='areaId' id='areaId_inputForm' value=''>
					<input type='hidden' name='provinceName' id='provinceName_inputForm' value=''>
					<input type='hidden' name='cityName' id='cityName_inputForm' value=''>
					<input type='hidden' name='areaName' id='areaName_inputForm' value=''>
					<input type='hidden' name='salesAreaId' id='salesAreaId' value=''>
					<ul>
						<li>
							<div class="infor_name">
								<span>*</span> <label>产品名称</label>
							</div>
							<div class="f_left table-largest content1">
								<div>
									<input type="text" class="input-largest" name="goodsName" id="goodsName">
								</div>
							</div>
						</li>
						<li>
							<div class="infor_name ">
								<span>*</span> <label>品牌</label>
							</div>
							<div class="f_left">
								<input type="text" class="input-middle mr5" name="brandName" id="brandName"> 
								<!-- <span class="font-grey9">核价参考价格：12000.00 或 无核价信息，请向产品部咨询</span> -->
							</div>
						</li>
						<li>
							<div class="infor_name ">
								<span>*</span> <label>型号</label>
							</div>
							<div class="f_left">
								<input type="text" class="input-middle" name="model" id="model">
							</div>
						</li>
						<li>
							<div class="infor_name ">
								<span>*</span> <label>单位</label>
							</div>
							<div class="f_left">
								<input type="text" class="input-smaller" name="unitName" id="unitName">
							</div>
						</li>
						<c:if test="${optType ne 1}">
						<li>
							<div class="infor_name ">
								<label>报价</label>
							</div>
							<div class="f_left">
								<input type="text" class="input-smaller mr5" name="price" id="price" onkeyup="inputTotalMoney('price');">
								<!-- <span class="font-grey9">核价参考价格：12000.00 或 无核价信息，请向产品部咨询</span> -->
							</div>
						</li>
						<li>
							<div class="infor_name ">
								<span>*</span> <label>数量</label>
							</div>
							<div class="f_left">
								<input type="text" class="input-smaller" name="num" id="num" onkeyup="inputTotalMoney('num');">
							</div>
						</li>
							<li>
								<div class="infor_name ">
									<label>总额</label>
								</div>
								<div class="f_left" id="inputTotalMoney"></div>
							</li>
							<li>
								<div class="infor_name mt0">
									<label>报价含安调</label>
								</div>
								<div class="f_left inputfloat inputfloatmb0">
									<ul>
										<li><input type="radio" name="installation" value="1" checked> <label>是</label></li>
										<li><input type="radio" name="installation" value="0"> <label>否</label></li>
									</ul>
									<input type="hidden" name="haveInstallation" id="haveInstallation" />
								</div>
							</li>
							<li>
								<div class="infor_name ">
									<label>货期</label>
								</div>
								<div class="f_left">
									<input type="text" class="input-smaller mr5" name="deliveryCycle" id="deliveryCycle"><!--  <span class="mt4 mr5">天</span>  -->
									<!-- <span class="font-grey9 mt4">核价参考货期：3-5天，近半年货期均值：6.5天</span> -->
								</div>
							</li>
						<li>
							<div class="infor_name">
								<label>是否直发</label>
							</div>
							<div class="f_left inputfloat inputfloatmb0">
								<ul>
									<li class="mt4"><input type="radio" name="deliveryDirectRad" value="0" checked> <label>否</label></li>
									<li class="mt4"><input type="radio" name="deliveryDirectRad" value="1"> <label>是</label></li>
									<li><input type="text" placeholder="请填写直发原因，含有直发商品的订单不允许提前采购" class="input-larger" name="deliveryDirectComments" id="deliveryDirectComments"></li>
								</ul>
								<input type="hidden" name="deliveryDirect" id="deliveryDirect">
							</div>
						</li>
						<li>
							<div class="infor_name ">
								<label>内部备注</label>
							</div>
							<div class="customername pos_rel f_left">
								<input type="text" class="input-largest mr5" placeholder="内部备注不对外显示" name="insideComments" id="insideComments">
<%--								<input type="text" class="input-largest mr5 J-lm-trigger"--%>
<%--									   placeholder="内部备注不对外显示" name="insideComments"--%>
<%--									   id="insideComments" onclick="insideRemark(this)" label_data="" readonly>--%>
<%--								<i class="iconbluemouth contorlIcon"></i>--%>
<%--								<div class="pos_abs customernameshow" label_left=50" style="width: 500px; top: 25px;background-color: #00CD66;">--%>
<%--								</div>--%>
							</div>
						</li>
						<li>
							<div class="infor_name ">
								<label>产品备注</label>
							</div>
							<div class="f_left">
								<input type="text" class="input-largest mr5" placeholder="产品备注对外显示" name="goodsComments" id="goodsComments">
							</div>
						</li>
						<li>
							<div class="infor_name ">
								<label>产品图片</label>
							</div>
							<div class="f_left">
								<div class="">
									<input type="file" class="upload_file" style="display: none;" name="lwfile" id="lwfile" onchange="uploadImgFtp(this);">
									<input type="text" class="input-middle" id="fileUrl" onclick="lwfile.click();" readonly="readonly">
									<label class="bt-bg-style bt-middle bg-light-blue ml4" onclick="lwfile.click();">浏览</label>
									
									<!-- <i class="iconsuccesss mt5" id="iconsuccess" style="display:none;"></i> -->
									
									<i class="iconsuccesss mt5 none" id="img_icon_wait"></i>
			                        <a href="" target="_blank" class="font-blue cursor-pointer mr5 ml10 mt4 none" id="img_opt_look">查看</a>
			                    	<span class="font-red cursor-pointer mt4 none" onclick="delProductImg()" id="img_opt_del">删除</span>
	                                <span class="font-grey9">使用jpg格式，2MB以内</span>
								</div>
								<!-- 附件信息表字段隐藏信息 -->
								<input type="hidden" name="name" id="imgName"/>
								<input type="hidden" name="domain" id="imgDomain"/>
								<input type="hidden" name="uri" id="imgUri"/>
								
								<div class="font-grey9 mt5">友情提示<br/>1、如果您的操作导致报价单金额发生变化，可能需要重新编辑付款计划；</div>
							</div>
						</li>
					</c:if>
					</ul>
					<input type="hidden" name="quoteorderId" id="quoteorderId" value="${quoteorderId}"/>
					<input type="hidden" name="isTemp" id="isTemp" value="1"/><!-- 是否临时产品 -->
					<div class="add-tijiao  tcenter">
						<c:if test="${optType eq 1}">
							<button class="bt-bg-style bg-deep-green" type="button" onclick="bussinessInput();">提交报价产品</button>
						</c:if>
						<c:if test="${optType ne 1}">
							<button class="bt-bg-style bg-deep-green" type="button" onclick="inputSubmit();">提交</button>
						</c:if>
						<button id="cancle" type="button" class="dele">取消</button>
					</div>
				</form>
			</div>
		</div>
	</div>
</body>
</html>
