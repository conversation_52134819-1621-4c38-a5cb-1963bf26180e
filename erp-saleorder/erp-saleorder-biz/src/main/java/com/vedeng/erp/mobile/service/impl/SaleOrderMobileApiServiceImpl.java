package com.vedeng.erp.mobile.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vedeng.base.api.dto.kuaidi.KuaiDiReqDTO;
import com.vedeng.base.api.dto.kuaidi.LogisticsDTO;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.erp.common.Constants;
import com.vedeng.erp.mobile.api.SaleOrderMobileApiService;
import com.vedeng.erp.mobile.domain.Position;
import com.vedeng.erp.mobile.domain.User;
import com.vedeng.erp.mobile.dto.*;
import com.vedeng.erp.mobile.mapper.ExpressMapper;
import com.vedeng.erp.mobile.mapper.InvoiceMapper;
import com.vedeng.erp.mobile.mapper.UserMapper;
import com.vedeng.erp.saleorder.dao.SaleOrderMapper;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.infrastructure.logistics.api.KuaiDiApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SaleOrderMobileApiServiceImpl implements SaleOrderMobileApiService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private SaleOrderMapper saleOrderMapper;

    @Autowired
    private ExpressMapper expressMapper;

    @Autowired
    private InvoiceMapper invoiceMapper;

    @Autowired
    private KuaiDiApi kuaiDiApi;

    @Value("${kuaidi.cutomer}")
    String customer;

    @Value("${kuaidi.key}")
    String key;

    @Value("${baseserver.kuaidi.erp.authorization}")
    String kuaidiAuthorization;

    @Override
    public SaleOrderAttributionInfoDto getSaleOrderAttributionInfo(Integer userId) {
        if (Objects.isNull(userId)) {
            return null;
        }

        SaleOrderAttributionInfoDto saleOrderAttributionInfoDto = new SaleOrderAttributionInfoDto();

        // 设置归属部门Id集合
        List<Integer> orgIds = userMapper.getOrgIdsByUsersOrgIds(userId);
        if (CollUtil.isNotEmpty(orgIds)) {
            saleOrderAttributionInfoDto.setCurrentOrgIdList(orgIds);
        }

        // 设置归属人员Id集合
        User user = userMapper.selectByPrimaryKey(userId);
        List<Position> positionList = userMapper.getPositionByUserId(userId);
        if (Objects.nonNull(user) && CollUtil.isNotEmpty(positionList)) {
            saleOrderAttributionInfoDto.setCurrentUserIdList(getCurrentUserIdList(user, positionList));
        }

        return saleOrderAttributionInfoDto;
    }

    @Override
    public PageInfo<SaleOrderListResultDto> getSaleOrderListPage(SaleOrderListQueryDto saleOrderListQueryDto) {
        PageHelper.startPage(saleOrderListQueryDto.getPageNum(), saleOrderListQueryDto.getPageSize());
        List<SaleOrderListResultDto> saleOrderListPage = saleOrderMapper.getSaleOrderListPage(saleOrderListQueryDto);
        return new PageInfo<>(saleOrderListPage);
    }

    @Override
    public List<SaleOrderExpressInfoDto> getExpressInfoOfSaleOrder(Integer saleOrderId, List<Integer> saleOrderGoodsIds) {
        if (Objects.isNull(saleOrderId) || CollUtil.isEmpty(saleOrderGoodsIds)) {
            return Collections.emptyList();
        }
        // 1.查询普发商品快递信息
        List<SaleOrderExpressInfoDto> expressInfoList = expressMapper.getExpressInfoBySaleOrder(saleOrderId, saleOrderGoodsIds);
        // 2.查询直发商品快递信息
        List<SaleOrderExpressInfoDto> directExpressInfoList = expressMapper.getDirectExpressInfoBySaleOrder(saleOrderId, saleOrderGoodsIds);

        List<SaleOrderExpressInfoDto> combinedList = new ArrayList<>();
        if (CollUtil.isNotEmpty(expressInfoList)) {
            combinedList.addAll(expressInfoList);
        }
        if (CollUtil.isNotEmpty(directExpressInfoList)) {
            combinedList.addAll(directExpressInfoList);
        }

        return combinedList;
    }

    @Override
    public List<SaleOrderInvoiceInfoDto> getInvoiceInfoOfSaleOrder(List<Integer> saleOrderIds) {
        if (CollUtil.isEmpty(saleOrderIds)) {
            return Collections.emptyList();
        }
        List<SaleOrderInvoiceInfoDto> saleOrderInvoiceInfoDtoList = invoiceMapper.getByRelatedIdAndType(saleOrderIds, Constants.SALES_INVOICE);

        if (CollUtil.isEmpty(saleOrderInvoiceInfoDtoList)) {
            return Collections.emptyList();
        }

        return saleOrderInvoiceInfoDtoList.stream().peek(saleOrderInvoiceInfoDto -> {
            if (StringUtils.isNotBlank(saleOrderInvoiceInfoDto.getInvoiceHref()) && saleOrderInvoiceInfoDto.getInvoiceHref().startsWith("<")) {
                String href = saleOrderInvoiceInfoDto.getInvoiceHref();
                href = StringUtils.replace(href, "<![CDATA[", "");
                href = StringUtils.replace(href, "]]>", "");
                saleOrderInvoiceInfoDto.setInvoiceHref(href);
            }
            if (StringUtils.isNotBlank(saleOrderInvoiceInfoDto.getOssFileUrl()) && saleOrderInvoiceInfoDto.getOssFileUrl().startsWith("<")) {
                String ossFileUrl = saleOrderInvoiceInfoDto.getOssFileUrl();
                ossFileUrl = StringUtils.replace(ossFileUrl, "<![CDATA[", "");
                ossFileUrl = StringUtils.replace(ossFileUrl, "]]>", "");
                saleOrderInvoiceInfoDto.setOssFileUrl(ossFileUrl);
            }
        }).collect(Collectors.toList());
    }

    @Override
    public SaleOrderDetailInfoDto getSaleOrderDetail(Integer saleOrderId) {
        return saleOrderMapper.getBySaleOrderId(saleOrderId);
    }

    @Override
    public BigDecimal getRealTotalAmountOfSaleOrder(Integer saleOrderId) {
        return saleOrderMapper.getRealTotalAmountOfSaleOrder(saleOrderId);
    }

    @Override
    public SaleOrderExpressInfoDto getExpressByExpressIdAndLogisticsNo(Integer expressId, String logisticsNo) {
        return expressMapper.getExpressByExpressIdAndLogisticsNo(expressId, logisticsNo);
    }

    @Override
    public List<String> getExpressPhoneByBusinessType(Integer businessType, Integer expressId) {
        switch (businessType) {
            case Constants.BUSINESS_TYPE_SALE_ORDER:
                return expressMapper.getSaleOrderExpressPhoneByExpressId(expressId);
            case Constants.BUSINESS_TYPE_BUY_ORDER:
                return expressMapper.getBuyOrderExpressPhoneByExpressId(expressId);
            case Constants.BUSINESS_TYPE_INVOICE:
                return expressMapper.getInvoiceExpressPhoneByExpressId(expressId);
            default:
                return Collections.emptyList();
        }
    }

    @Override
    public String getLogisticsDetail(String logisticsName, String logisticsNo, String phone) {
        log.info("获取快递信息：logisticsName:{},logisticsNo:{},phone:{}", logisticsName, logisticsNo, phone);
        String com = expressMapper.getLogisticsCode(logisticsName);
        if (StringUtils.isBlank(com)) {
            return null;
        }
        KuaiDiReqDTO kuaiDiReqDto = new KuaiDiReqDTO();
        kuaiDiReqDto.setAuthorization(kuaidiAuthorization);
        KuaiDiReqDTO.KuaiDiParam kuaiDiParam = new KuaiDiReqDTO.KuaiDiParam();
        kuaiDiParam.setCom(com);
        kuaiDiParam.setNum(logisticsNo);
        kuaiDiParam.setPhone(phone);
        kuaiDiReqDto.setParam(kuaiDiParam);
        try {
            RestfulResult<LogisticsDTO> waybillState = kuaiDiApi.getWaybillState(kuaiDiReqDto);
            if (waybillState.isSuccess() && Objects.nonNull(waybillState.getData())) {
                return JSON.toJSONString(waybillState.getData());
            }
        } catch (Exception e) {
            log.error("获取快递信息失败", e);
        }
        return null;
    }

    private List<Integer> getCurrentUserIdList(User user, List<Position> positionList) {
        List<Integer> currentUserIdList = new ArrayList<>();
        for (Position position : positionList) {
            user.setPositions(Collections.singletonList(position));
            user.setPositType(position.getType());
            user.setPositLevel(position.getLevel());
            user.setOrgId(position.getOrgId());
            user.setOrgName(position.getOrgName());

            List<User> myUserList = getMyUserList(user, Collections.singletonList(position.getType()), false);
            if(CollectionUtils.isNotEmpty(myUserList)){
                currentUserIdList.addAll(myUserList.stream().map(User::getUserId).collect(Collectors.toList()));
            }

        }
        return currentUserIdList;
    }

    private List<User> getMyUserList(User user, List<Integer> positionType, boolean haveDisabeldUser) {
        List<User> myUserList = new ArrayList<>();

        User subUser = new User();
        if (!Constants.TWO.equals(user.getIsAdmin())) {
            subUser.setCompanyId(user.getCompanyId());
        }

        if (!haveDisabeldUser) {
            subUser.setIsDisabled(0);
        }

        if (CollUtil.isNotEmpty(positionType)) {
            subUser.setPositionTypes(positionType);
        }

        List<User> userByPositTypes = userMapper.getUserByPositTypes(subUser);

        if (CollUtil.isNotEmpty(positionType)) {
            // 非超级管理员
            if (!Constants.TWO.equals(user.getIsAdmin())) {
                // 自己当前职位是否在职位类型内
                Boolean isInPosit = false;
                for (Integer p : positionType) {
                    if (p.equals(user.getPositType())) {
                        isInPosit = true;
                    }
                }
                // 查询自己下面的用户
                if (isInPosit) {
                    List<User> treeUser = null;
                    JSONArray jsonArray = (JSONArray) JSONArray.parseArray(JSONObject.toJSONString(userByPositTypes));

                    List<User> sellist = new ArrayList<>();

                    JSONArray jsonList = treeMenuList(jsonArray, user.getUserId(), "");
                    treeUser = resetList(jsonList, sellist, 0);
                    treeUser.add(user);
                    if (treeUser.size() > 1) {// 名称排序
                        List<Integer> userIds = new ArrayList<>();
                        for (User u : treeUser) {
                            userIds.add(u.getUserId());
                        }
                        myUserList = userMapper.getUserByUserIds(userIds);

                    } else {
                        myUserList = treeUser;
                    }
                } else {
                    myUserList = userByPositTypes;
                }
            } else {
                myUserList = userByPositTypes;
            }
        } else {
            myUserList = userByPositTypes;
        }

        if (CollUtil.isNotEmpty(myUserList)) {
            boolean isE = false;
            for (User u : myUserList) {
                if (u.getUserId().equals(user.getUserId())) {
                    isE = true;
                    break;
                }
            }
            if (!isE) {
                myUserList.add(user);
            }
        } else {
            myUserList.add(user);
        }
        return myUserList;
    }

    private com.alibaba.fastjson.JSONArray treeMenuList(com.alibaba.fastjson.JSONArray menuList, int parentId, String parentName) {
        JSONArray childMenu = new JSONArray();
        for (Object object : menuList) {
            JSONObject jsonMenu = (JSONObject) JSONArray.toJSON(object);
            int menuId = jsonMenu.getIntValue("userId");
            int pid = jsonMenu.getIntValue("parentId");
            if (parentName != "") {
                jsonMenu.put("nameArr", parentName + "--" + jsonMenu.getString("username"));
            } else {
                jsonMenu.put("nameArr", jsonMenu.getString("username"));
            }
            if (parentId == pid) {
                JSONArray c_node = treeMenuList(menuList, menuId, jsonMenu.getString("nameArr"));
                jsonMenu.put("childNode", c_node);
                childMenu.add(jsonMenu);
            }
        }
        return childMenu;
    }

    private List<User> resetList(JSONArray tasklist, List<User> sellist, int num) {
        String str = "";
        for (int i = 0; i < (num * 2); i++) {
            str += "-";
        }
        for (Object obj : tasklist) {
            JSONObject jsonMenu = (JSONObject) JSONArray.toJSON(obj);
            User sm = new User();
            sm.setUserId(Integer.valueOf(jsonMenu.getIntValue("userId")));
            sm.setUsername(str + "├" + jsonMenu.getString("username"));
            sm.setParentId(jsonMenu.getIntValue("parentId"));
            sm.setCcNumber(jsonMenu.getString("ccNumber"));
            sellist.add(sm);
            if (jsonMenu.get("childNode") != null) {
                if (JSONArray.parseArray(JSONObject.toJSONString(jsonMenu.get("childNode"))).size() > 0) {
                    num++;
                    resetList(JSONArray.parseArray(JSONObject.toJSONString(jsonMenu.get("childNode"))), sellist, num);
                    num--;
                }
            }
        }
        return sellist;
    }


    @Override
    public SaleorderInfoDto querySaleorderByCustomerName(String customerName) {
        return saleOrderMapper.selectLastSaleOrderByTraderName(customerName);
    }

}
