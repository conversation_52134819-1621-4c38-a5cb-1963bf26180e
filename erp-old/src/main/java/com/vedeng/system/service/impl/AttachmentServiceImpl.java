package com.vedeng.system.service.impl;

import com.common.constants.Contant;
import com.fasterxml.jackson.core.type.TypeReference;
import com.vedeng.authorization.model.User;
import com.vedeng.common.http.HttpClientUtils;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.system.dao.AttachmentMapper;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.service.AttachmentService;
import com.vedeng.system.service.ReadService;
import com.vedeng.system.service.UserService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
@Service("attachmentService")
public class AttachmentServiceImpl extends BaseServiceimpl implements AttachmentService {
	public static Logger logger = LoggerFactory.getLogger(AttachmentServiceImpl.class);

	@Value("${file_url}")
	protected String fileUrl;

	@Resource
	private ReadService readService;

	@Autowired
	private AttachmentMapper attachmentMapper;

	@Resource
	private UserService userService;

	private static Integer FIlE_AUTHORIZATION_STORAGE=1643;

	@Override
	public ResultInfo<?> saveOrUpdateAttachment(Attachment attachment) {
		String url = httpUrl + "attachment/saveorupdateattachment.htm";
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, attachment, clientId, clientKey, TypeRef2);
			return result;
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return new ResultInfo<>();
		}
	}

	@Override
	public void saveAuthorization(String[] fileName, String[] fileUri, String domain, Integer relatedId, Long time,Integer functionId) {
		if (fileName != null && fileUri != null) {
			for (int i = 0; i < fileName.length; i++) {
				String name = fileName[i];
				String uri = fileUri[i];
				if (!"".equals(name) && !"".equals(uri)) {
					Attachment attachment = new Attachment();
					attachment.setName(name);
					attachment.setUri(uri);
					attachment.setAttachmentFunction(functionId);
					if(uri.indexOf("resourceId") >= 0){
						attachment.setDomain(domain);
					}else{
						attachment.setDomain(fileUrl);
					}

					attachment.setRelatedId(relatedId);
					attachment.setAddTime(time);
					if (uri.contains("jpg") || uri.contains("png") || uri.contains("gif") || uri.contains("bmp")) {
						attachment.setAttachmentType(460);
					} else {
						attachment.setAttachmentType(461);
					}
					int res3 = attachmentMapper.insert(attachment);
				}
			}
		}
	}

	@Override
	public void delAuthorizationStorageInfo(Integer temporaryStorageId, Integer authorizationStorageFile) {
		attachmentMapper.delByKeyIdAndRelatedTableId(temporaryStorageId,authorizationStorageFile);
	}

	@Override
	public List<Attachment> getAttachmentInfoByRelatedIdAndFunctionId(Integer authorizationApplyId, Integer authorizationApplyFile) {
		return attachmentMapper.getAttachmentInfoByRelatedIdAndFunctionId(authorizationApplyId,authorizationApplyFile);
	}

	@Override
	public List<Attachment> queryAttachmentList(Attachment attachment) {
		return attachmentMapper.getAttachmentList(attachment);
	}

	@Override
	public void delAttachment(Attachment attachment) {
		if(Objects.nonNull(attachment) && Objects.nonNull(attachment.getAttachmentId())){
			attachmentMapper.delAttachmentId(attachment);
		}else if(Objects.nonNull(attachment) && Objects.nonNull(attachment.getAttachmentType()) && Objects.nonNull(attachment.getAttachmentFunction())&&Objects.nonNull(attachment.getRelatedId())){
			attachmentMapper.delAttachmentId(attachment);
		}
	}

	@Override
	public List<Attachment> queryOutInAttachmentList(Attachment attachment) {
		List<Attachment> attachmentList = attachmentMapper.queryOutInAttachmentList(attachment);
		if(!CollectionUtils.isEmpty(attachmentList)){
			List<Integer> userIdList = attachmentList.stream().map(Attachment::getCreator).collect(Collectors.toList());
			List<User> userList = userService.getUserByUserIds(userIdList);
			if(!CollectionUtils.isEmpty(userList)){
				Map<Integer, User> userMap = userList.stream().collect(Collectors.toMap(User::getUserId, Function.identity(), (x, y) -> y));
				attachmentList.stream().filter(o->Objects.nonNull(userMap.get(o.getCreator()))).forEach(o->{
					o.setCreatorName(userMap.get(o.getCreator()).getUsername());
				});
			}
		}
		return attachmentList;
	}

	@Override
	public void delWarehouseOutAttachment(Attachment attachment) {
		if(attachment != null && attachment.getAttachmentId() != null){
			attachmentMapper.delWarehouseOutAttachment(attachment);
		}
	}
}
