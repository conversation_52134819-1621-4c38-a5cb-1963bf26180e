.vd-ui-search-wrap {
    background: #fff;

    .vd-ui-search-container {
        padding: 20px 10px;

        &.hidden {
            opacity: 0;
        }
    }

    .vd-ui-search-list {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        margin-bottom: -10px;
        position: relative;

        &.show-line {
            max-height: none !important;
        }

        .search-item {
            align-items: center;
            margin-right: 20px;
            margin-bottom: 10px;
            width: calc(20% - 20px);
            display: flex;

            &.hidden {
                opacity: 0;
                z-index: -1;
            }

            .search-label {
                width: 100px;
                text-align: right;
                color: #999;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }

            .search-content {
                flex: 1;

                .vd-ui-select, .vd-ui-date, .vd-ui-cascader {
                    width: 100%;
                }

                .vd-ui-input-multiple {
                    width: 100%;
                }

                .vd-ui-date-editor, .vd-ui-range{
                    width: 100%;
                }
            }
        }
    }

    .vd-ui-search-btns {
        display: flex;
        justify-content: center;
        margin-top: 20px;
        position: relative;

        .search-btn-inner {
            display: flex;
            margin: 0 auto;
            align-items: center;

            .vd-ui-button {
                margin-left: 10px;

                &:first-child {
                    margin-left: 0;
                }
            }
        }

        .vd-ui-search-toggle {
            color: #09f;
            cursor: pointer;
            margin-left: 20px;

            .vd-ui_icon {
                font-size: 16px;
                margin-left: 5px;
                vertical-align: -2px;
            }

            &:hover {
                color: #f60;
            }
        }
    }

    .vd-ui-search-option {
        position: absolute;
        right: 20px;
        display: flex;
        align-items: center;

        .search-option-item {
            color: #09f;
            cursor: pointer;
            margin-left: 20px;
            position: relative;

            &::before {
                content: '';
                position: absolute;
                width: 1px;
                height: 14px;
                background: #e1e5e8;
                left: -10px;
                top: 5px;
            }

            &:first-child {
                margin-left: 0;

                &::before {
                    display: none;
                }
            }
            
            .vd-ui_icon {
                font-size: 16px;
                vertical-align: -2px;
                margin-right: 5px;
            }

            &.item-setting {
                color: #999;
            }

            &:hover {
                color: #f60;
            }
        }
    }

    .dlg-form-footer {
        padding-left: 150px;
        width: 100%;
        display: flex;
    }
}

.search-setting-dlg {
    overflow: hidden;

    .search-setting-top {
        display: flex;
        margin-bottom: 20px;

        .setting-select-all {
            flex: 1;
        }

        .setting-refresh {
            color: #09f;
            cursor: pointer;

            .vd-ui_icon {
                font-size: 16px;
                vertical-align: -2px;
                margin-right: 5px;
            }

            &:hover {
                color: #f60;
            }
        }
    }

    .dlg-setting-list {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: -5px;

        &.sortable {
            .dlg-setting-item {
                .vd-ui-checkbox-item {
                    cursor: pointer;
                }
            }
        }

        .dlg-setting-item {
            width: calc(25% - 3.75px);
            margin-right: 5px;
            border: solid 1px #E1E5E8;
            border-radius: 3px;
            margin-bottom: 5px;
            user-select: none;

            &:nth-child(4n) {
                margin-right: 0;
            }

            .vd-ui-checkbox-item {
                width: 100%;
                padding: 10px;
                cursor: move;
                
                .vd-ui-checkbox-inner {
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                }
            }

            &.placehodler {
                opacity: .1;
            }
        }
    }
}

@media screen and (max-width: 2500px) {
    .vd-ui-search-wrap .vd-ui-search-list .search-item {
        width: calc(25% - 20px);
    }
}

@media screen and (max-width: 1366px) {
    .vd-ui-search-wrap .vd-ui-search-list .search-item {
        width: calc(33.33% - 20px);
    }

    .vd-ui-search-wrap.no-left .vd-ui-search-list .search-item {
        width: calc(33.33% - 20px);
    }
}

@media screen and (max-width: 1680px) and (min-width: 1367px) {
    .vd-ui-search-wrap .vd-ui-search-list .search-item {
        width: calc(33.33% - 20px);
    }

    .vd-ui-search-wrap.no-left .vd-ui-search-list .search-item {
        width: calc(25% - 20px);
    }
}


// 列表主体部分
.vd-ui-list-container {
    padding: 20px;

    .vd-ui-list-inner {
        background: #fff;
        border: solid 1px #E1E5E8;

        &.no-border-top {
            border-top: 0;
        }

        .list-container-top {
            padding: 10px 20px;
            display: flex;
            align-items: center;

            .list-container-top-buttons {
                display: flex;
                align-items: center;
            }

            .vd-ui-button {
                margin-right: 10px;

                &:last-child {
                    margin-right: 0;
                }
            }

            .list-select-num {
                margin-left: 20px;
                color: #999;
            }
        }

        .list-container-content {
            position: relative;

            .list-container-setting {
                position: absolute;
                right: 0;
                top: 0;
                z-index: 11;
                width: 37px;
                height: 37px;
                text-align: center;
                line-height: 37px;
                font-size: 16px;
                color: #666;
                border-left: solid 1px #E1E5E8;
                cursor: pointer;

                &:hover {
                    color: #f60;
                }
            }
        }

        .list-container-table {
            // min-height: calc(100vh - 500px);
        }

        .list-container-pager {
            padding: 10px 20px;
            display: flex;
            align-items: center;

            .list-container-total {
                flex: 1;
                color: #999;
            }

            .vd-ui-select {
                margin-left: 20px;
            }
        }
    }
}