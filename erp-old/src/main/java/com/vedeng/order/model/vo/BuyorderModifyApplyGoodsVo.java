package com.vedeng.order.model.vo;

import com.vedeng.order.model.BuyorderModifyApplyGoods;

public class BuyorderModifyApplyGoodsVo extends BuyorderModifyApplyGoods {

	/**
	 * @Fields serialVersionUID : TODO
	 */
	private static final long serialVersionUID = 1L;
	
	private String [] oldInsideCommentsArray;
	
	private String [] insideCommentsArray;

	/**
	 * Start
	 */
	private String[] oldSendGoodsTimeStr;

	private String[] oldReceiveGoodsTimeStr;

	private String[] sendGoodsTimeStr;

	private String[] receiveGoodsTimeStr;

	public String[] getOldSendGoodsTimeStr() {
		return oldSendGoodsTimeStr;
	}

	public void setOldSendGoodsTimeStr(String[] oldSendGoodsTimeStr) {
		this.oldSendGoodsTimeStr = oldSendGoodsTimeStr;
	}

	public String[] getOldReceiveGoodsTimeStr() {
		return oldReceiveGoodsTimeStr;
	}

	public void setOldReceiveGoodsTimeStr(String[] oldReceiveGoodsTimeStr) {
		this.oldReceiveGoodsTimeStr = oldReceiveGoodsTimeStr;
	}

	public String[] getSendGoodsTimeStr() {
		return sendGoodsTimeStr;
	}

	public void setSendGoodsTimeStr(String[] sendGoodsTimeStr) {
		this.sendGoodsTimeStr = sendGoodsTimeStr;
	}

	public String[] getReceiveGoodsTimeStr() {
		return receiveGoodsTimeStr;
	}

	public void setReceiveGoodsTimeStr(String[] receiveGoodsTimeStr) {
		this.receiveGoodsTimeStr = receiveGoodsTimeStr;
	}

	/**
	 * End
	 */

	public String[] getOldInsideCommentsArray() {
		return oldInsideCommentsArray;
	}

	public void setOldInsideCommentsArray(String[] oldInsideCommentsArray) {
		this.oldInsideCommentsArray = oldInsideCommentsArray;
	}

	public String[] getInsideCommentsArray() {
		return insideCommentsArray;
	}

	public void setInsideCommentsArray(String[] insideCommentsArray) {
		this.insideCommentsArray = insideCommentsArray;
	}

}
