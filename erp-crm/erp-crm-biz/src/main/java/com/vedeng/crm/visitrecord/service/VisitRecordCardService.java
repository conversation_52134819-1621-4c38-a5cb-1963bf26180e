package com.vedeng.crm.visitrecord.service;

import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.crm.visitrecord.domain.dto.VisitRecordCardDto;
import com.vedeng.crm.visitrecord.domain.vo.VisitRecordCardVo;
import java.util.List;

public interface VisitRecordCardService {
    
    /**
     * 根据拜访记录ID查询打卡记录列表(每个人只取最新一条)
     */
    List<VisitRecordCardVo> getLatestCardList(Integer recordId);

    /**
     * 保存打卡记录
     * @param cardDto 打卡信息
     */
    void saveCard(VisitRecordCardDto cardDto, CurrentUser currentUser);
} 