package com.vedeng.customerbillperiod.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: daniel
 * @Date: 2021/7/26 10 54
 * @Description: 客户账期金额冻结类
 */
@Data
public class CustomerBillPeriodFreezingDto {

    private Integer companyId;

    private Long customerId;

    private Long orderId;

    /**
     * 冻结的金额
     */
    private BigDecimal freezingAmount;

    /**
     * 客户账期结算方式，1产品发货，2产品开票
     */
    private Integer settlementType;

    private Long addTime;
}
