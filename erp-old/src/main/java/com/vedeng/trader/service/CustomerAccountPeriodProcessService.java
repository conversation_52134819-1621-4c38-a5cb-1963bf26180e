package com.vedeng.trader.service;

import com.vedeng.authorization.model.User;
import com.vedeng.customerbillperiod.exception.CustomerBillPeriodException;
import com.vedeng.trader.model.model.vo.CustomerBillPeriodApplyVo;


/**
 * <AUTHOR> [<EMAIL>]
 */
public interface CustomerAccountPeriodProcessService {

    /**
     * 保存或着更新客户账期shenqing
     *
     * @param operator
     * @param customerBillPeriodApplyVo
     */
    void saveOrUpdateAccountPeriodApply(User operator, CustomerBillPeriodApplyVo customerBillPeriodApplyVo) throws CustomerBillPeriodException;


    /**
     * 完成当前流程任务
     *
     * @param taskId
     * @param operatorId
     * @param comment
     * @param pass
     */
    void completeTask(String taskId, Integer operatorId, String comment, Boolean pass);

}
