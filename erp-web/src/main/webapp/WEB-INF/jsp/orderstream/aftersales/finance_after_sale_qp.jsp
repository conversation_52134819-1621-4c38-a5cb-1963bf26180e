<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="丢票详情" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<script type="text/javascript"
        src='<%= basePath %>static/js/orderstream/aftersales/finance_after_sales.js?rnd=${resourceVersionKey}'></script>
<script src="<%= basePath %>static/js/orderstream/jquery.js"></script>
<script src="<%= basePath %>static/js/orderstream/afterSales_index.js?rnd=${resourceVersionKey}"></script>
<link rel="stylesheet" href="<%= basePath %>static/css/orderstream/style.css">
<div class="main-container">
    <div class="t-line-wrap J-line-wrap"
         data-json='[
     {"label":"确认","status":${topStatusList.get(0)}},
     {"label":"处理丢票","status":${topStatusList.get(1)}},
     {"label":""${lastLabelName}","status":${topStatusList.get(2)}}]'>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">基本信息</div>
        </div>
        <table class="table">
            <tbody>
            <tr>
                <td class="wid20">订单号</td>
                <td>${afterSalesVo.afterSalesNo}</td>

                <td>售后类型</td>
                <td>${afterSalesVo.typeName}</td>
            </tr>
            <tr>
                <td class="wid20">订单状态</td>
                <td>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 0}">待确认</c:if>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 1}">进行中</c:if>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 2}">已完结</c:if>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 3}">已关闭</c:if>
                </td>

                <td>审核状态</td>
                <td>
                    <c:if test="${afterSalesVo.status eq 0}">待确认</c:if>
                    <c:if test="${afterSalesVo.status eq 1}">审核中</c:if>
                    <c:if test="${afterSalesVo.status eq 2}">审核通过</c:if>
                    <c:if test="${afterSalesVo.status eq 3}">审核不通过</c:if>
                </td>
            </tr>
            <tr>
                <td>生效状态</td>
                <td><c:if test="${afterSalesVo.validStatus eq 0}">未生效</c:if>
                    <c:if test="${afterSalesVo.validStatus eq 1}">已生效</c:if>
                </td>

                <td>售后处理人</td>
                <td>${afterSalesVo.serviceUserName}</td>
            </tr>
            <tr>
                <td>申请人</td>
                <td>
                    <c:forEach var="user" items="${afterSalesVo.userList}">
                        <c:if test="${user.userId eq afterSalesVo.creator}">${user.username}</c:if>
                    </c:forEach>
                </td>
                <td>申请时间</td>
                <td><date:date value="${afterSalesVo.addTime}"/></td>
            </tr>
            <tr>
                <td>生效时间</td>
                <td><date:date value="${afterSalesVo.validTime}"/></td>

                <td>完结时间</td>
                <td><c:if test="${afterSalesVo.atferSalesStatus == 2}"><date:date
                        value="${afterSalesVo.modTime}"/></c:if></td>
            </tr>
            <tr>
                <td>完结/关闭原因</td>
                <td>${afterSalesVo.afterSalesStatusResonName}</td>
                <td>完结/关闭人员</td>
                <td>${afterSalesVo.afterSalesStatusUserName}</td>
            </tr>

            <tr>
                <td>完结/关闭备注</td>
                <td colspan="3" class="text-left">${afterSalesVo.afterSalesStatusComments}</td>
            </tr>
            </tbody>
        </table>
    </div>

    <input type="hidden" name="afterSalesId" value="${afterSalesVo.afterSalesId}"/>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">售后信息</div>
        </div>
        <table class="table">
            <tbody>
            <tr>
                <td class="wid20">售后原因</td>
                <td>${afterSalesVo.reasonName}</td>
                <td class="wid20">第一责任部门</td>
                <td>${afterSalesVo.firstResponsibleDepartmentStr}</td>
            </tr>

            <tr>
                <td class="wid20">售后联系人</td>
                <td>${afterSalesVo.afterConnectUserName}</td>

                <td>售后联系人电话</td>
                <td>
                    ${afterSalesVo.afterConnectPhone}
                    <c:if test="${not empty afterSalesVo.afterConnectPhone}">
                        <i class="icontel cursor-pointer" title="点击拨号"
                           onclick="callout('${afterSalesVo.afterConnectPhone}',${afterSalesVo.traderId},1,4,${afterSalesVo.afterSalesId},${afterSalesVo.traderContactId});"></i>
                    </c:if>
                </td>
            </tr>

            <tr>
                <td class="wid20">售后报单人</td>
                <td>${afterSalesVo.traderContactName}</td>

                <td>售后报单人电话</td>
                <td>
                    ${afterSalesVo.traderContactTelephone}
                    <c:if test="${not empty afterSalesVo.traderContactTelephone}">
                        <i class="icontel cursor-pointer" title="点击拨号"
                           onclick="callout('${afterSalesVo.traderContactTelephone}',${afterSalesVo.traderId},1,4,${afterSalesVo.afterSalesId},${afterSalesVo.traderContactId});"></i>
                    </c:if>
                </td>
            </tr>

            <tr>
                <td>售后报单人手机号</td>
                <td>
                    ${afterSalesVo.traderContactMobile}
                    <c:if test="${not empty afterSalesVo.traderContactMobile}">
                        <i class="icontel cursor-pointer" title="点击拨号"
                           onclick="callout('${afterSalesVo.traderContactMobile}',${afterSalesVo.traderId},1,4,${afterSalesVo.afterSalesId},${afterSalesVo.traderContactId});"></i>
                    </c:if>
                </td>
            </tr>

            <tr>
                <td>详情说明</td>
                <td colspan="3" class="text-left">${afterSalesVo.comments}</td>

            </tr>
            <tr>
                <td>附件</td>
                <td colspan="3" class="text-left">
                    <%@ include file="view_afterSales_files.jsp" %>
                </td>

            </tr>
            </tbody>
        </table>
    </div>


    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">丢票处理记录</div>
        </div>

        <div class="inputfloat limb0">
            <ul>
                <li>
                    <label>快递公司</label>
                    <select style='width:178px;' id="logisticsName">
                        <c:forEach var="list" items="${logisticsList}" varStatus="num">
                            <c:if test="${list.isEnable eq 1 && (list.name == '中通快递'||list.name=='顺丰速运')}">
                                <option
                                        <c:if test="${list.name == '中通快递'}">selected</c:if> value="${list.name}"
                                        id="${list.logisticsId}">${list.name}</option>
                            </c:if>
                        </c:forEach>
                    </select>
                </li>
                <li>
                    <c:choose>
                        <c:when test="${afterSalesVo.atferSalesStatus eq 1 &&
                        ((null==taskInfoOver and null==taskInfoOver.getProcessInstanceId())
                    or (null!=taskInfoOver and taskInfoOver.assignee==null and empty candidateUserMapOver[taskInfoOver.id]))}">
                            <button type="button" class="bt-bg-style bg-light-blue bt-small"
                                    onclick="printview('${afterSalesVo.afterSalesId}')">寄送发票并打印快递单
                            </button>
                            <button type="button" class="bt-bg-style bg-light-blue bt-small"
                                    onclick="initExpressSave('${afterSalesVo.afterSalesId}')">手动录入快递单
                            </button>
                        </c:when>
                        <c:otherwise>
                            <button type="button" class="bt-bg-style bg-light-grey bt-small">寄送发票并打印快递单
                            </button>
                            <button type="button" class="bt-bg-style bg-light-grey bt-small">手动录入快递单
                            </button>
                        </c:otherwise>
                    </c:choose>
                </li>
            </ul>
        </div>
        <!-- 根据退票状态，判断是否能开票 -->
        <c:set var="isOpenInvoice" value="0"></c:set>

        <table class="table" id="listInfo">
            <c:if test="${not empty afterSalesVo.afterSalesInvoiceVoList}">
                <c:forEach items="${afterSalesVo.afterSalesInvoiceVoList}" var="asi">
                    <thead>
                    <tr>
                        <td colspan="8">
                            <div style="float: left; margin-left: 100px;">处理状态：
                                <c:choose>
                                    <c:when test="${asi.handleStatus eq 0}">
                                        未处理
                                    </c:when>
                                    <c:when test="${asi.handleStatus eq 1}">
                                        已处理
                                    </c:when>
                                </c:choose>
                            </div>
                            <div style="float: left; margin-left: 100px;">快递单号：
                                    ${asi.logisticsNo}
                            </div>

                            <div style="float: left; margin-left: 100px;">快递公司：
                                    ${asi.logisticsName}
                            </div>

                            <div style="float: left; margin-left: 100px;">寄送状态：
                                <c:choose>
                                    <c:when test="${(empty asi.afterExpressId) or (asi.afterExpressId eq 0)}">未寄送</c:when>
                                    <c:when test="${(not empty asi.afterExpressId) and (asi.afterExpressId ne 0)}">已寄送</c:when>
                                    <c:otherwise>--</c:otherwise>
                                </c:choose>
                            </div>

                            <div style="float: left; margin-left: 100px;">签收状态：
                                <c:choose>
                                    <c:when test="${asi.arrivalStatus==0||(empty asi.arrivalStatus)}">未签收</c:when>
                                    <c:when test="${asi.arrivalStatus==1}">部分签收</c:when>
                                    <c:when test="${asi.arrivalStatus==2}">全部签收</c:when>
                                </c:choose>
                            </div>

                            <div style="float: left; margin-left: 100px;">签收时间：
                                <date:date value="${asi.arrivalTime}" format="yyyy.MM.dd hh:mm"/>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <th class="wid4">选择</th>
                        <th>发票号</th>
                        <th>发票代码</th>
                        <th>票种</th>
                        <th>红蓝字</th>
                        <th>发票金额</th>
                        <th>开票人</th>
                        <th>复印件开具日</th>
                    </tr>
                    </thead>
                    <tbody>

                    <tr>
                        <td>
                            <c:if test="${empty asi.afterExpressId || asi.afterExpressId==null ||asi.afterExpressId==0}">
                                <input type="checkbox" name="checkName" id="${asi.invoiceId}" class=""
                                       alt="${asi.invoiceNo}" placeholder="${asi.type}">
                            </c:if>
                            <input type="hidden" name="companyId" id="companyId" value="1">
                            <input type="hidden" name="qpType" id="qpType" value="${afterSalesVo.type}">
                            <input type="hidden" id="sInvoiceNo" name="sInvoiceNo">
                            <input type="hidden" id="logisticsNo">
                        </td>
                        <td>${asi.invoiceNo}</td>
                        <td>${asi.invoiceCode}</td>
                        <td>
                            <c:forEach var="list" items="${invoiceTypeList}">
                                <c:if test="${asi.invoiceType eq list.sysOptionDefinitionId}">${list.title}</c:if>
                            </c:forEach>
                        </td>
                        <td>
                            <c:choose>
                                <c:when test="${asi.colorType eq 1}">
                                    <c:choose>
                                        <c:when test="${asi.isEnable eq 0}">
                                            <span style="color: red">红字作废</span>
                                        </c:when>
                                        <c:otherwise>
                                            <span style="color: red">红字有效</span>
                                        </c:otherwise>
                                    </c:choose>
                                </c:when>
                                <c:otherwise>
                                    <c:choose>
                                        <c:when test="${asi.isEnable eq 0}">
                                            <span style="color: red">蓝字作废</span>
                                        </c:when>
                                        <c:otherwise>
                                            蓝字有效
                                        </c:otherwise>
                                    </c:choose>
                                </c:otherwise>
                            </c:choose>
                        </td>
                        <td>${asi.amount}</td>
                        <td>
                            <c:forEach var="user" items="${afterSalesVo.userList}">
                                <c:if test="${user.userId eq asi.creator}">${user.username}</c:if>
                            </c:forEach>
                        </td>
                        <td>
                            <fmt:formatDate pattern="yyyy-MM-dd HH:mm:ss" value="${asi.sendTime}"/>
                        </td>
                    </tr>
                    </tbody>

                </c:forEach>
            </c:if>
            <c:if test="${empty afterSalesVo.afterSalesInvoiceVoList}">
                <thead>
                <tr>
                    <th class="wid4">选择</th>
                    <th>发票号</th>
                    <th>发票代码</th>
                    <th>票种</th>
                    <th>红蓝字</th>
                    <th>发票金额</th>
                    <th>开票人</th>
                    <th>复印件开具日</th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td colspan='7'>查询无结果！</td>
                </tr>
                </tbody>
            </c:if>
        </table>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">所属订单信息</div>
        </div>
        <table class="table">
            <tbody>
            <tr>
                <td class="wid20">所属销售单号</td>
                <td>
                    <div class="customername pos_rel">
							<span style="float:none" class="brand-color1 addtitle"
                                  tabTitle='{"num":"viewfinancesaleorder${afterSalesVo.orderId}","title":"订单信息",
                               	"link":"./finance/invoice/viewSaleorder.do?saleorderId=${afterSalesVo.orderId}"}'>
                                ${afterSalesVo.orderNo}
                            </span>
                        <i class="iconbluemouth"></i>
                        <div class="pos_abs customernameshow" style="display: none;">
                            付款状态：
                            <c:if test="${afterSalesVo.paymentStatus eq 0}">未付款</c:if>
                            <c:if test="${afterSalesVo.paymentStatus eq 1}">部分付款</c:if>
                            <c:if test="${afterSalesVo.paymentStatus eq 2}">全部付款</c:if>
                            <br> 发货状态：
                            <c:if test="${afterSalesVo.deliveryStatus eq 0}">未发货</c:if>
                            <c:if test="${afterSalesVo.deliveryStatus eq 1}">部分发货</c:if>
                            <c:if test="${afterSalesVo.deliveryStatus eq 2}">全部发货</c:if>
                            <br> 开票状态：
                            <c:if test="${afterSalesVo.invoiceStatus eq 0}">未开票</c:if>
                            <c:if test="${afterSalesVo.invoiceStatus eq 1}">部分开票</c:if>
                            <c:if test="${afterSalesVo.invoiceStatus eq 2}">全部开票</c:if>
                            <br> 收货状态：
                            <c:if test="${afterSalesVo.arrivalStatus eq 0}">未收货</c:if>
                            <c:if test="${afterSalesVo.arrivalStatus eq 1}">部分收货</c:if>
                            <c:if test="${afterSalesVo.arrivalStatus eq 2}">全部收货</c:if>
                        </div>
                    </div>
                </td>
                <td class="wid20">销售单金额</td>
                <td><fmt:formatNumber type="number" value="${afterSalesVo.totalAmount}" pattern="0.00"
                                      maxFractionDigits="2"/></td>
            </tr>

            <tr>
                <td>归属销售</td>
                <td>${afterSalesVo.userName}</td>
                <td>归属部门</td>
                <td>${afterSalesVo.orgName}</td>
            </tr>

            <tr>
                <td>订单状态</td>
                <td>
                    <c:if test="${afterSalesVo.saleorderStatus eq 0}">待确认</c:if>
                    <c:if test="${afterSalesVo.saleorderStatus eq 1}">进行中</c:if>
                    <c:if test="${afterSalesVo.saleorderStatus eq 2}">已完结</c:if>
                    <c:if test="${afterSalesVo.saleorderStatus eq 3}">已关闭</c:if>
                </td>
                <td>生效时间</td>
                <td><date:date value="${afterSalesVo.saleorderValidTime}"/></td>
            </tr>

            <tr>
                <td>客户名称</td>
                <td>
                    <div class="customername pos_rel">
							<span style="float:none" class="brand-color1 addtitle"
                                  tabTitle='{"num":"viewcustomer${afterSalesVo.traderId}","title":"客户信息",
								"link":"./trader/customer/baseinfo.do?traderId=${afterSalesVo.traderId}"}'>${afterSalesVo.traderName}
                            </span>
                        <i class="iconbluemouth"></i>
                        <div class="pos_abs customernameshow" style="display: none;">
                            客户性质：
                            <c:if test="${afterSalesVo.customerNature eq 465}">分销</c:if>
                            <c:if test="${afterSalesVo.customerNature eq 466}">终端</c:if>
                            <br> 交易次数：${afterSalesVo.orderCount}<br>
                            交易金额：<fmt:formatNumber type="number" value="${afterSalesVo.orderTotalAmount}" pattern="0.00"
                                                   maxFractionDigits="2"/><br>
                            上次交易日期：<date:date value="${afterSalesVo.lastOrderTime}" format="yyyy.MM"/>
                        </div>
                    </div>
                </td>
                <td>客户等级</td>
                <td>${afterSalesVo.customerLevelStr}
                </td>
            </tr>
            </tbody>
        </table>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                跟进记录
            </div>
        </div>
        <%@ include file="add_followUp_common.jsp" %>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">发票材料</div>
        </div>
        <table class="table">
            <thead>
            <tr>
                <th>材料</th>
                <th>操作人</th>
                <th>时间</th>
            </tr>
            </thead>
            <tbody>
            <c:if test="${not empty afterSalesVo.afterInvoiceAttachmentList}">
                <c:forEach items="${afterSalesVo.afterInvoiceAttachmentList}" var="aia">
                    <tr>
                        <td class="font-blue"><a href="http://${aia.domain}${aia.uri}" target="_blank">${aia.name}</a>
                        </td>
                        <td>
                            <c:forEach var="user" items="${afterSalesVo.userList}">
                                <c:if test="${user.userId eq aia.creator}">${user.username}</c:if>
                            </c:forEach>
                        </td>
                        <td><date:date value="${aia.addTime}"/></td>
                    </tr>
                </c:forEach>
            </c:if>
            </tbody>
        </table>
    </div>


    <%--支出记录--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">支出记录</div>
        </div>
        <%@ include file="add_expenditure_common.jsp" %>
    </div>

    <%--收入记录--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">收入记录</div>
        </div>
        <%@ include file="add_revenue_common.jsp" %>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">沟通记录</div>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <th class="wid18">沟通时间</th>
                <th class="wid8">录音</th>
                <th class="wid8">联系人</th>
                <th class="wid10">联系方式</th>
                <th class="wid6">沟通方式</th>
                <th>沟通内容</th>
                <th>操作人</th>
                <th>下次联系日期</th>
                <th>下次沟通内容</th>
                <th>备注</th>
                <th>创建时间</th>
            </tr>
            </thead>
            <tbody>
            <c:if test="${not empty communicateList}">
                <c:forEach items="${communicateList}" var="communicateRecord" varStatus="">
                    <tr>
                        <td><date:date value="${communicateRecord.begintime} "/>~<date:date
                                value="${communicateRecord.endtime}" format="HH:mm:ss"/></td>
                        <td><c:if
                                test="${not empty communicateRecord.coidUri }">${communicateRecord.communicateRecordId }</c:if></td>
                        <td>${communicateRecord.contactName}</td>
                        <td>${communicateRecord.phone}</td>
                        <td>${communicateRecord.communicateModeName}</td>
                        <td>
                            <ul class="communicatecontent ml0">
                                <c:if test="${not empty communicateRecord.tag }">
                                    <c:forEach items="${communicateRecord.tag }" var="tag">
                                        <li class="bluetag" title="${tag.tagName}">${tag.tagName}</li>
                                    </c:forEach>
                                </c:if>
                            </ul>
                        </td>
                        <td>${communicateRecord.user.username}</td>
                        <c:choose>
                            <c:when test="${communicateRecord.isDone == 0 }">
                                <td class="font-red">${communicateRecord.nextContactDate }</td>
                            </c:when>
                            <c:otherwise>
                                <td>${communicateRecord.nextContactDate }</td>
                            </c:otherwise>
                        </c:choose>
                        <td>${communicateRecord.nextContactContent}</td>
                        <td>${communicateRecord.comments}</td>
                        <td><date:date value="${communicateRecord.addTime} "/></td>
                    </tr>
                </c:forEach>
            </c:if>
            <c:if test="${empty communicateList}">
                <!-- 查询无结果弹出 -->
                <tr>
                    <td colspan='11'>查询无结果！请尝试使用其他搜索条件。</td>
                </tr>
            </c:if>
            </tbody>
        </table>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                审核记录
            </div>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <th>操作人</th>
                <th>操作时间</th>
                <th>操作事项</th>
                <th>备注</th>
            </tr>
            </thead>
            <tbody>
            <c:if test="${null!=historicActivityInstance}">
                <c:forEach var="hi" items="${historicActivityInstance}" varStatus="status">
                    <c:if test="${not empty  hi.activityName}">
                        <tr>
                            <td>
                                <c:choose>
                                    <c:when test="${hi.activityType == 'startEvent'}">
                                        ${startUser}
                                    </c:when>
                                    <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                                    </c:when>
                                    <c:otherwise>
                                        <c:if test="${historicActivityInstance.size() == status.count}">
                                            ${verifyUsers}
                                        </c:if>
                                        <c:if test="${historicActivityInstance.size() != status.count}">
                                            <c:forEach items="${assigneeVos}" var="assigneeVo">
                                                <c:if test="${assigneeVo.assignee eq hi.assignee}">
                                                    ${assigneeVo.realName}
                                                </c:if>
                                            </c:forEach>
                                        </c:if>
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td><fmt:formatDate value="${hi.endTime}" pattern="yyyy-MM-dd HH:mm:ss" /></td>
                            <td>
                                <c:choose>
                                    <c:when test="${hi.activityType == 'startEvent'}">
                                        开始
                                    </c:when>
                                    <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                                        结束
                                    </c:when>
                                    <c:otherwise>
                                        ${hi.activityName}
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td class="font-red">${commentMap[hi.taskId]}</td>
                        </tr>
                    </c:if>
                </c:forEach>
            </c:if>
            <c:if test="${null==historicActivityInstance}">
                <!-- 查询无结果弹出 -->
                <tr>
                    <td colspan='4'>暂无记录！</td>
                </tr>
            </c:if>
            </tbody>
        </table>
    </div>
</div>
<%@ include file="../../common/footer.jsp" %>

