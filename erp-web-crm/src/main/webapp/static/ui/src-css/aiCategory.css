.ai-category-choose-wrap {
  display: flex;
  margin-bottom: 15px;
  font-size: 12px;
}
.ai-category-choose-wrap .ai-category-choose-label {
  margin-right: 10px;
  width: 240px;
  text-align: right;
  position: relative;
}
.ai-category-choose-wrap .ai-category-choose-label img {
  width: 30px;
  height: 30px;
}
.ai-category-choose-wrap .ai-category-choose-label .ai-category-logo-tip {
  position: absolute;
  top: -38px;
  right: 0;
  transform: translateX(calc(100% - 21px));
  height: 47px;
  width: 193px;
  background: url(/static/image/common/ai-tip-bg.svg);
  background-size: 100% 100%;
  opacity: 0;
  z-index: -1;
  transition: all 0.22s ease;
}
.ai-category-choose-wrap .ai-category-choose-label .ai-category-logo-tip .ai-category-logo-tip-txt {
  color: #fff;
  padding-top: 9px;
  padding-left: 16px;
  text-align: left;
}
.ai-category-choose-wrap .ai-category-choose-label:hover .ai-category-logo-tip {
  opacity: 1;
  z-index: 1;
}
.ai-category-choose-wrap .ai-category-choose-cnt {
  background: #E0F3FF;
  border-radius: 3px;
  padding: 0 10px;
  max-width: 640px;
}
.ai-category-choose-wrap .ai-category-choose-cnt .ai-category-detail {
  padding: 10px 0;
}
.ai-category-choose-wrap .ai-category-choose-cnt .ai-category-detail .ai-category-detail-txt {
  margin-bottom: 10px;
}
.ai-category-choose-wrap .ai-category-choose-cnt .ai-category-detail .ai-category-detail-txt:last-child {
  margin-bottom: 0;
}
.ai-category-choose-wrap .ai-category-choose-cnt .ai-category-detail .ai-category-tags {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10px;
}
.ai-category-choose-wrap .ai-category-choose-cnt .ai-category-detail .ai-category-tags .ai-category-tag-item {
  padding: 4px 10px;
  border-radius: 2px;
  background: #fff;
  margin-right: 5px;
  margin-bottom: 5px;
}
.ai-category-choose-wrap .ai-category-choose-cnt .ai-category-detail .ai-category-add {
  margin-top: 10px;
  display: flex;
}
.ai-category-choose-wrap .ai-category-choose-cnt .ai-category-detail .ai-category-add .ai-category-add-btn {
  display: flex;
  align-items: center;
  color: #09f;
  cursor: pointer;
}
.ai-category-choose-wrap .ai-category-choose-cnt .ai-category-detail .ai-category-add .ai-category-add-btn:hover {
  color: #f60;
}
.ai-category-choose-wrap .ai-category-choose-cnt .ai-category-detail .ai-category-add .ai-category-add-btn .icon-add {
  font-size: 16px;
  line-height: 1;
  margin-right: 5px;
}
.ai-category-choose-wrap .ai-category-choose-cnt .ai-category-loading {
  display: flex;
  align-items: center;
  padding: 6px 0;
}
@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.ai-category-choose-wrap .ai-category-choose-cnt .ai-category-loading .icon-loading {
  animation: loading 2s linear infinite;
  font-size: 16px;
  margin-left: 5px;
  line-height: 1;
}
.ai-category-choose-wrap .ai-category-choose-cnt .ai-category-empty {
  padding: 6px 0;
}
.ai-category-choose-wrap .ai-category-choose-cnt .ai-category-custom-item {
  display: flex;
  align-items: center;
  padding: 6px 0;
}
.ai-category-choose-wrap .ai-category-choose-cnt .ai-category-suggest-wrap {
  position: relative;
  width: 360px;
}
.ai-category-choose-wrap .ai-category-choose-cnt .ai-category-suggest-wrap .icon-delete {
  position: absolute;
  font-size: 16px;
  color: #666;
  width: 30px;
  height: 30px;
  top: 0;
  right: -30px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}
.ai-category-choose-wrap .ai-category-choose-cnt .ai-category-suggest-wrap .icon-delete:hover {
  color: #f60;
}
.ai-category-choose-wrap .ai-category-choose-cnt .ai-category-search-panel {
  position: absolute;
  top: 30px;
  left: 0;
  z-index: 10;
  width: 100%;
  box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.1);
}
.ai-category-choose-wrap .ai-category-choose-cnt .ai-category-search-panel .ai-category-search-panel-inner {
  background: #fff;
  border: solid 1px #ddd;
}
.ai-category-choose-wrap .ai-category-choose-cnt .ai-category-search-panel .ai-category-search-panel-inner .ai-category-search-panel-list .ai-category-search-panel-title {
  color: #999;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 10px;
}
.ai-category-choose-wrap .ai-category-choose-cnt .ai-category-search-panel .ai-category-search-panel-inner .ai-category-search-panel-list .ai-category-search-list-content {
  max-height: 352px;
  overflow-y: auto;
}
.ai-category-choose-wrap .ai-category-choose-cnt .ai-category-search-panel .ai-category-search-panel-inner .ai-category-search-panel-list .ai-category-search-list-content::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.ai-category-choose-wrap .ai-category-choose-cnt .ai-category-search-panel .ai-category-search-panel-inner .ai-category-search-panel-list .ai-category-search-list-content::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.ai-category-choose-wrap .ai-category-choose-cnt .ai-category-search-panel .ai-category-search-panel-inner .ai-category-search-panel-list .ai-category-search-list-content::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.ai-category-choose-wrap .ai-category-choose-cnt .ai-category-search-panel .ai-category-search-panel-inner .ai-category-search-panel-list .ai-category-search-list-content::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.ai-category-choose-wrap .ai-category-choose-cnt .ai-category-search-panel .ai-category-search-panel-inner .ai-category-search-panel-list .ai-category-search-list-content::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.ai-category-choose-wrap .ai-category-choose-cnt .ai-category-search-panel .ai-category-search-panel-inner .ai-category-search-panel-list .ai-category-search-list-content .ai-category-search-panel-item {
  padding: 6px 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}
.ai-category-choose-wrap .ai-category-choose-cnt .ai-category-search-panel .ai-category-search-panel-inner .ai-category-search-panel-list .ai-category-search-list-content .ai-category-search-panel-item .num {
  color: #999;
}
.ai-category-choose-wrap .ai-category-choose-cnt .ai-category-search-panel .ai-category-search-panel-inner .ai-category-search-panel-list .ai-category-search-list-content .ai-category-search-panel-item:hover {
  color: #f60;
}
.ai-category-choose-wrap .ai-category-choose-cnt .ai-category-search-panel .ai-category-search-panel-inner .ai-category-search-loading {
  padding: 10px;
  display: flex;
  align-items: center;
}
@keyframes loading {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
.ai-category-choose-wrap .ai-category-choose-cnt .ai-category-search-panel .ai-category-search-panel-inner .ai-category-search-loading > .icon-loading {
  font-size: 16px;
  color: #09f;
  animation: loading 2s linear infinite;
  z-index: 99;
  margin-right: 5px;
}
.ai-category-choose-wrap .ai-category-choose-cnt .ai-category-search-panel .ai-category-search-panel-inner .ai-category-null-data {
  text-align: center;
  padding: 10px;
}
.ai-category-detail-wrap .ai-category-detail-trigger {
  width: 30px;
  height: 30px;
  cursor: pointer;
}
.ai-category-detail-wrap .ai-category-detail-trigger img {
  width: 100%;
  height: 100%;
}
.ai-category-detail-wrap .ai-category-detail-trigger .ai-category-logo-tip {
  position: absolute;
  top: -38px;
  right: 9px;
  height: 47px;
  width: 208px;
  opacity: 0;
  z-index: -1;
  transition: all 0.22s ease;
}
.ai-category-detail-wrap .ai-category-detail-trigger .ai-category-logo-tip:after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background: url(/static/image/common/ai-tip-bg.svg);
  background-size: 100% 100%;
  left: 0;
  top: 0;
  transform: rotateY(180deg);
}
.ai-category-detail-wrap .ai-category-detail-trigger .ai-category-logo-tip .ai-category-logo-tip-txt {
  color: #fff;
  padding-top: 9px;
  padding-left: 16px;
  text-align: left;
  position: relative;
  z-index: 1;
}
.ai-category-detail-wrap .ai-category-detail-trigger:hover .ai-category-logo-tip {
  opacity: 1;
  z-index: 1;
}
.ai-category-detail-dialog .ai-category-detail-tip {
  margin-bottom: 10px;
}
.ai-category-detail-dialog .ai-category-detail-tip .strong {
  color: #f60;
}
.ai-category-detail-dialog .ai-category-detail-tags {
  display: flex;
  flex-wrap: wrap;
  margin-top: -5px;
  margin-bottom: 10px;
}
.ai-category-detail-dialog .ai-category-detail-tags .ai-category-detail-tag-item {
  padding: 4px 10px;
  background: #E0F3FF;
  border-radius: 2px;
  margin-right: 5px;
  margin-top: 5px;
  cursor: pointer;
}
.ai-category-detail-dialog .ai-category-detail-tags .ai-category-detail-tag-item:hover {
  background: #B3E1FF;
}
.ai-category-detail-dialog .ai-category-detail-tags .ai-category-detail-tag-item:active {
  background: #7FCCFF;
}
.ai-category-detail-dialog .ai-category-detail-item {
  height: 30px;
  display: flex;
  align-items: center;
  cursor: pointer;
  border-bottom: solid 1px #F5F7FA;
  padding-left: 10px;
}
.ai-category-detail-dialog .ai-category-detail-item .ai-category-detail-item-label {
  flex: 1;
}
.ai-category-detail-dialog .ai-category-detail-item .icon-search {
  width: 36px;
  height: 30px;
  font-size: 16px;
  align-items: center;
  justify-content: center;
  color: #999;
  display: none;
}
.ai-category-detail-dialog .ai-category-detail-item:hover {
  background: #F5F7FA;
}
.ai-category-detail-dialog .ai-category-detail-item:hover .icon-search {
  display: flex;
}
