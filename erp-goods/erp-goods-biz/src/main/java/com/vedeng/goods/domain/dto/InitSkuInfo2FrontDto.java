package com.vedeng.goods.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/8/9
 * @apiNote 未上架商品数据初始化推送到前台：导入excel封装实体
 */
@Data
public class InitSkuInfo2FrontDto {

    /**
     * 订货号
     */
    @ExcelProperty(value = {"订货号"}, index = 0)
    private String skuNo;

    @ExcelProperty(value = {"商品名称"}, index = 1)
    private String showName;

    @ExcelProperty(value = {"给研发数据", "商品定价"}, index = 2)
    private String pricing;

    /**
     * 采购到货时长(工作日）
     */
    @ExcelProperty(value = {"给研发数据", "货期"}, index = 3)
    private String purchaseTime;

    @ExcelProperty(value = {"给研发数据", "供应商售后政策"}, index = 4)
    private String afterSalesPolicy;

    @ExcelProperty(value = {"给研发数据", "贝登售后标准"}, index = 5)
    private String afterSalesStandard;

    /**
     * 是否需报备 0否 1是
     */
    @ExcelProperty(value = {"给研发数据", "报备信息"}, index = 6)
    private String isNeedReport;

    @ExcelProperty(value = {"给研发数据", "运营信息"}, index = 7)
    private String operationalInfo;

    @ExcelProperty(value = {"品牌"}, index = 8)
    private String brand;

    @ExcelProperty(value = {"一级分类"}, index = 9)
    private String firstGrade;

    @ExcelProperty(value = {"二级分类"}, index = 10)
    private String secondGrade;

    @ExcelProperty(value = {"三级分类"}, index = 11)
    private String thirdGrade;

    @ExcelProperty(value = {"产品经理"}, index = 12)
    private String productManager;

    /**
     * 审核状态 0待完善 1审核中 2审核不通过 3审核通过 4删除 5 待提交审核
     */
    @ExcelProperty(value = {"审核状态"}, index = 13)
    private String checkStatus;

    @ExcelProperty(value = {"启用状态"}, index = 14)
    private String status;

    @ExcelProperty(value = {"贝登上架"}, index = 15)
    private String onSale;

    /**
     * 商品等级
     */
    @ExcelProperty(value = {"商品等级"}, index = 16)
    private String goodsLevelNo;

    @ExcelProperty(value = {"商品档位"}, index = 17)
    private String goodsPositionNo;

}
