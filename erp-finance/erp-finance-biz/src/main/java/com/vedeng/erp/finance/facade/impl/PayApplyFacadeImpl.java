package com.vedeng.erp.finance.facade.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.eventbus.EventBus;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.enums.PatternEnum;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.utils.ErpDateUtils;
import com.vedeng.common.core.utils.ErpSpringBeanUtil;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.buyorder.dto.BuyOrderInfoDto;
import com.vedeng.erp.buyorder.service.BuyorderApiService;
import com.vedeng.erp.finance.domain.dto.DiscountNumberDto;
import com.vedeng.erp.finance.dto.*;
import com.vedeng.erp.finance.dto.event.CreateBillEvent;
import com.vedeng.erp.finance.enums.PayApplyRouteEnum;
import com.vedeng.erp.finance.facade.PayApplyFacade;
import com.vedeng.erp.finance.mapstruct.PayApplyCreateBillConvertor;
import com.vedeng.erp.finance.service.PayApplyInterface;
import com.vedeng.erp.finance.service.NewPayApplyService;
import com.vedeng.erp.settlement.domain.entity.PayApplyEntity;
import com.vedeng.erp.settlement.mapper.PayApplyMapper;
import com.vedeng.infrastructure.bank.api.common.exception.AcceptanceBillException;
import com.vedeng.infrastructure.bank.api.domain.B2eBasicDraftApplyReq;
import com.vedeng.infrastructure.bank.api.domain.B2eDraftApplyRes;
import com.vedeng.infrastructure.bank.api.domain.B2eQueryTaskNoReq;
import com.vedeng.infrastructure.bank.api.domain.B2eQueryTaskNoRes;
import com.vedeng.infrastructure.bank.api.domain.dto.AcceptanceBillCreateDto;
import com.vedeng.infrastructure.bank.api.service.AcceptanceBillApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PayApplyFacadeImpl implements PayApplyFacade {

    @Autowired
    private PayApplyMapper payApplyMapper;

    @Autowired
    private PayApplyCreateBillConvertor payApplyCreateBillConvertor;

    @Autowired
    private NewPayApplyService payApplyService;

    @Autowired
    @Qualifier(value = "eventBusCenter")
    private EventBus eventBusCenter;

    @Autowired
    private AcceptanceBillApiService acceptanceBillApiService;

    @Autowired
    private BuyorderApiService buyorderApiService;

    /**
     * 是否从扣款账号中扣划至保证金账户
     */
    @Value("${bank.api.isDeduct}")
    private String IS_DEDUCT;

    /**
     * 账号类型
     */
    @Value("${bank.api.accountType}")
    private String ACCOUNT_TYPE;

    /**
     * 自动制单
     * @param payApplyCreateBillDto
     */
    @Override
    public void autoCreateCreateBill(PayApplyCreateBillDto payApplyCreateBillDto) {

        // 获取执行器
        Class<? extends PayApplyInterface> clazz = PayApplyRouteEnum.getClazzByPayTypeEnum(payApplyCreateBillDto.getPayType());
        PayApplyInterface interfaceInstance = ErpSpringBeanUtil.getBean(clazz);
        interfaceInstance.autoCreateCreateBill(payApplyCreateBillDto);
    }

    /**
     * 支付申请单规则检查
     * @param payApplyCheckDto
     */
    @Override
    public void payApplyRuleCheck(PayApplyCheckDto payApplyCheckDto) {
        // 获取执行器
        Class<? extends PayApplyInterface> clazz = PayApplyRouteEnum.getClazzByPayTypeEnum(payApplyCheckDto.getPayType());
        PayApplyInterface interfaceInstance = ErpSpringBeanUtil.getBean(clazz);
        interfaceInstance.payApplyRuleCheck(payApplyCheckDto);
    }

    @Override
    public void filter(PayApplyCreateBillDto payApplyCreateBillDto) {
        String verifyInfoUserNames = payApplyMapper.getVerifyInfoUserNames(payApplyCreateBillDto.getPayApplyId());
        // 判断当前是否需要财务审核，如果没有则过滤掉
        if (!verifyInfoUserNames.toLowerCase().contains("robin")){
            throw new ServiceException("付款申请单当前非robin审核，及非财务制单阶段，自动制单拦截");
        }
    }

    /**
     * 创建支付申请单规则检查
     * @param payApplyCreateBillDto
     */
    @Override
    public void createBillRuleCheck(PayApplyCreateBillDto payApplyCreateBillDto) {
        // 获取执行器
        Class<? extends PayApplyInterface> clazz = PayApplyRouteEnum.getClazzByPayTypeEnum(payApplyCreateBillDto.getPayType());
        PayApplyInterface interfaceInstance = ErpSpringBeanUtil.getBean(clazz);
        interfaceInstance.createBillRuleCheck(payApplyCreateBillDto);
    }

    /**
     * 根据dto获取支付申请单
     * @param payApplyCreateBillDto
     * @return
     */
    @Override
    public List<PayApplyCreateBillDto> getPayApplyByDto(PayApplyCreateBillDto payApplyCreateBillDto) {
        // dto 转 entity
        List<PayApplyEntity> payApplyEntities = payApplyMapper.queryByAll(payApplyCreateBillDto);
        return payApplyCreateBillConvertor.toDto(payApplyEntities);
    }

    @Override
    public PageInfo<PayApplyRespDto> pageList( PageParam<PayApplyReqDto> payApplyReqDto) {
        PageInfo<PayApplyRespDto> listPageInfo = payApplyService.pageList(payApplyReqDto);
        return listPageInfo;
    }

    @Override
    public List<AcceptancePayRespDto> acceptancePay(AcceptancePayDto acceptancePayDto) {
        log.info("承兑信息制单：{}", JSONObject.toJSONString(acceptancePayDto));
        Integer payVedengBankId;
        if (Objects.equals(acceptancePayDto.getBank(),1)){
            payVedengBankId = ErpConstant.SEVEN;
        }else if (Objects.equals(acceptancePayDto.getBank(),2)){
            payVedengBankId = ErpConstant.THREE;
        }else {
            throw new ServiceException("暂不支持该银行承兑");
        }



        List<Integer> payApplyIds = acceptancePayDto.getPayApplyIds();
        List<PayApplyDto> payApplyDtoList = payApplyMapper.queryPayApplyByIds(payApplyIds);
        Map<Integer, PayApplyDto> payApplyDtoMap = new HashMap<>();
        if (CollUtil.isNotEmpty(payApplyDtoList)){
            payApplyDtoMap = payApplyDtoList.stream().collect(Collectors.toMap(PayApplyDto::getPayApplyId, Function.identity(), (v1, v2) -> v1));
        }
        if (MapUtil.isEmpty(payApplyDtoMap)){
            payApplyDtoMap = MapUtil.newHashMap();
        }

        List<AcceptancePayRespDto> respDtoList = new ArrayList<>();
        for (Integer payApplyId : payApplyIds) {
            payApplyService.updateOffline(payApplyId,acceptancePayDto.getOffline());
            AcceptancePayRespDto respDto = new AcceptancePayRespDto();
            respDto.setPayApplyId(payApplyId);
            try{
                PayApplyDto payApplyDto = payApplyDtoMap.get(payApplyId);
                Integer buyorderId = payApplyDto.getRelatedId();
                BuyOrderInfoDto buyOrderInfo = buyorderApiService.getBuyOrderInfo(buyorderId);
                if (Objects.isNull(buyOrderInfo)){
                    respDto.setMsg("未查询到相关采购单信息");
                    respDtoList.add(respDto);
                    continue;
                }
                respDto.setBusinessNo(buyOrderInfo.getBuyorderNo());
                AcceptanceBillCreateDto dto = new AcceptanceBillCreateDto();
                dto.setPayAppId(payApplyId);
                B2eBasicDraftApplyReq req = new B2eBasicDraftApplyReq();
                req.setTrnId(IdUtil.simpleUUID());
                req.setOutApplicationNo(String.valueOf(payApplyId));
                req.setIsAllowSplitBill("0");

                req.setFinancingAmount(payApplyDto.getAmount());
                req.setValidFrom(ErpDateUtils.format(new Date(),PatternEnum.YYYY_MM_DD));
                req.setValidTo(ErpDateUtils.format(DateUtil.offsetMonth(new Date(), 6),PatternEnum.YYYY_MM_DD));
                req.setCustPhone("***********");
                req.setBothAgreedMatter("");
                req.setTaskNo("");
                req.setDeductAccountNo("");
                req.setIssuanceFlag("1");
                req.setIsAutoDelPayeeInfo("0");
                req.setEndoFlag("EM00");
                req.setTaskNo(acceptancePayDto.getTaskNo());

                // setCounterPartyList
                B2eBasicDraftApplyReq.CounterParty counterParty = new B2eBasicDraftApplyReq.CounterParty();
                counterParty.setPartyName(payApplyDto.getTraderName());
                counterParty.setTradeContractNo(buyOrderInfo.getBuyorderNo());
                counterParty.setTradeContractAmount(payApplyDto.getAmount());
                counterParty.setTradeValidFrom(ErpDateUtils.format(new Date(buyOrderInfo.getValidTime()),PatternEnum.YYYY_MM_DD));
                counterParty.setTradeValidTo("");
                counterParty.setIsValid("1");
                counterParty.setIsSelfBank(payApplyDto.getBank().contains("民生银行") ? "1" : "0");
                counterParty.setDepositBankNo(payApplyDto.getBankCode());
                counterParty.setDepositBankName(payApplyDto.getBank());
                counterParty.setPayeeAccountNo(payApplyDto.getBankAccount());
                counterParty.setPaymentAmount(payApplyDto.getAmount());
                counterParty.setRemark("");
                req.getCounterPartyList().add(counterParty);

                String productCode;
                String creditType;
                if (Objects.equals(acceptancePayDto.getCreditType(),"1")){
                    // 综合授信
                    productCode = "********";
                    creditType = "01";
                    List<B2eBasicDraftApplyReq.BailInfo> bailInfoList = new ArrayList<>();
                    // setBailInfoList
                    B2eBasicDraftApplyReq.BailInfo bailInfo = new B2eBasicDraftApplyReq.BailInfo();
                    bailInfo.setAccountType(ACCOUNT_TYPE);
                    req.setAccountType(ACCOUNT_TYPE);
                    req.setIsDeduct(IS_DEDUCT);
                    bailInfo.setAddAmount(payApplyDto.getAmount());
                    bailInfoList.add(bailInfo);
                    req.setBailInfoList(bailInfoList);
                }else if (Objects.equals(acceptancePayDto.getCreditType(),"2")){
                    // 单笔授信
                    productCode = "********";
                    creditType = "02";
                }else {
                    throw new ServiceException("暂不支持该授信类型");
                }
                req.setProductCode(productCode);
                req.setCreditType(creditType);

                dto.setB2eBasicDraftApplyReq(req);
                List<String> auditBuyorderContract = buyorderApiService.getAuditBuyorderContract(buyorderId);
                if (CollUtil.isEmpty(auditBuyorderContract)){
                    respDto.setMsg("采购合同为空");
                    respDtoList.add(respDto);
                    continue;
                }
                dto.setFileUrl(auditBuyorderContract.get(0));// 采购单合同

                if(Objects.equals(acceptancePayDto.getOffline(), 0)){
                    log.info("线上开票");
                    // 线上开票
                    B2eDraftApplyRes b2eDraftApplyRes = acceptanceBillApiService.b2eNbsAcceptanceBillCreate(dto);
                    boolean success = b2eDraftApplyRes.isSuccess();
                    if (success){
                        // 审核
                        this.audit(payApplyId,payVedengBankId);
                        continue;
                    }
                    // 失败
                    String message = b2eDraftApplyRes.getResponseHeader().getStatus().getMessage();
                    respDto.setMsg(message);
                    respDtoList.add(respDto);
                    continue;
                }
                if(Objects.equals(acceptancePayDto.getOffline(), 1)){
                    log.info("线下开票");
                    // 审核
                    this.audit(payApplyId,payVedengBankId);
                }
            }catch (ServiceException | AcceptanceBillException e){
                log.info("已知异常：",e);
                respDto.setMsg(e.getMessage());
                respDtoList.add(respDto);
            }catch (Exception e){
                log.info("创建承兑汇票未知异常,：{}", JSONObject.toJSONString(respDtoList),e);
                respDto.setMsg("未知异常");
                respDtoList.add(respDto);
            }

        }
        return respDtoList;
    }

    @Override
    public List<DiscountNumberDto> getDiscountNumber(BigDecimal amount) {
        B2eQueryTaskNoReq b2eQueryTaskNoReq = new B2eQueryTaskNoReq();
        b2eQueryTaskNoReq.setFinancingAmt(amount.toString());
        B2eQueryTaskNoRes b2eQueryTaskNoRes = acceptanceBillApiService.b2eQueryTaskNo(b2eQueryTaskNoReq);
        List<DiscountNumberDto> discountNumberDtoList = new ArrayList<>();
        if(b2eQueryTaskNoRes.isSuccess()){
            List<B2eQueryTaskNoRes.B2eQueryTaskNo> taskNoList = b2eQueryTaskNoRes.getXDataBody().getList();
            if(CollUtil.isNotEmpty(taskNoList)){
                for (B2eQueryTaskNoRes.B2eQueryTaskNo b2eQueryTaskNo : taskNoList) {
                    DiscountNumberDto discountNumberDto = new DiscountNumberDto();
                    discountNumberDto.setTaskNo(b2eQueryTaskNo.getTaskNo());
                    discountNumberDto.setSerialCode(b2eQueryTaskNo.getSerialCode());
                    discountNumberDtoList.add(discountNumberDto);
                }
            }
        }
        return discountNumberDtoList;
    }

    public void audit(Integer payApplyId,Integer payVedengBankId) {
        CreateBillEvent createBillEvent = new CreateBillEvent();
        createBillEvent.setPayVedengBankId(payVedengBankId);
        createBillEvent.setPayApplyId(payApplyId);
        log.info("制单事件触发：{}", JSONObject.toJSONString(createBillEvent));
        eventBusCenter.post(createBillEvent);
    }
}
