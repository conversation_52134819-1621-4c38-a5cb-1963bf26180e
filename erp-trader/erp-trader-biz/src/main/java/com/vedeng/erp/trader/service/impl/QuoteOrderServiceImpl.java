package com.vedeng.erp.trader.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.utils.numgenerator.BillNumGenerator;
import com.vedeng.common.core.utils.numgenerator.bean.BillGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.bean.NoGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.enums.BillType;
import com.vedeng.erp.saleorder.dto.OrderTerminalDto;
import com.vedeng.erp.saleorder.service.OrderTerminalApiService;
import com.vedeng.erp.saleorder.strategy.OrderTerminalContext;
import com.vedeng.erp.trader.domain.dto.QuoteorderDto;
import com.vedeng.erp.trader.domain.entity.AuthorizationApiApply;
import com.vedeng.erp.trader.domain.entity.QuoteorderEntity;
import com.vedeng.erp.trader.domain.entity.QuoteorderGoodsEntity;
import com.vedeng.erp.trader.mapper.QuoteorderGoodsMapper;
import com.vedeng.erp.trader.mapper.QuoteorderMapper;
import com.vedeng.erp.trader.mapstruct.QuoteorderConvertor;
import com.vedeng.erp.trader.mapstruct.QuoteorderGoodsConvertor;
import com.vedeng.erp.trader.service.QuoteOrderService;
import com.vedeng.order.strategy.QuoteOrderTerminalStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description 报价单服务类
 * @date 2022/7/21 18:45
 **/
@Service
@Slf4j
public class QuoteOrderServiceImpl implements QuoteOrderService {

    @Autowired
    private QuoteorderMapper quoteorderMapper;

    @Autowired
    private QuoteorderGoodsMapper quoteorderGoodsMapper;

    @Autowired
    private QuoteorderConvertor quoteorderConvertor;

    @Autowired
    private QuoteorderGoodsConvertor quoteorderGoodsConvertor;

    @Autowired
    private QuoteOrderTerminalStrategy quoteOrderTerminalStrategy;

    @Autowired
    private OrderTerminalApiService orderTerminalApiService;

    @Override
    public Integer selectQuoteorderIdByBusinessChanceId(Integer bussinessChanceId) {
        return quoteorderMapper.selectQuoteorderIdByBusinessChanceId(bussinessChanceId);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public QuoteorderDto saveQuoteOrder(QuoteorderDto quoteorderDto) {

        log.info("saveQuoteOrder 入参：{}", JSON.toJSONString(quoteorderDto));

        QuoteorderEntity quoteorderEntity = quoteorderConvertor.toEntity(quoteorderDto);
        quoteorderMapper.insertSelective(quoteorderEntity);
        // 报价单
        quoteorderDto.setQuoteorderId(quoteorderEntity.getQuoteorderId());


        BillGeneratorBean billGeneratorBean = new BillGeneratorBean(BillType.QUOTE_ORDER, NoGeneratorBean.builder().id(quoteorderDto.getQuoteorderId()).numberOfDigits(9).build());
        String code = new BillNumGenerator().distribution(billGeneratorBean);
        QuoteorderEntity toNo = new QuoteorderEntity();
        toNo.setQuoteorderId(quoteorderDto.getQuoteorderId());
        toNo.setQuoteorderNo(code);
        quoteorderMapper.updateByPrimaryKeySelective(toNo);
        quoteorderDto.setQuoteorderNo(code);
        // 报价商品 TO DB
        if (!CollectionUtils.isEmpty(quoteorderDto.getQuoteorderGoodsDtos())) {
            List<QuoteorderGoodsEntity> goodsEntities = quoteorderGoodsConvertor.toEntity(quoteorderDto.getQuoteorderGoodsDtos());
            goodsEntities.forEach(c -> c.setQuoteorderId(quoteorderEntity.getQuoteorderId()));
            goodsEntities.forEach(c ->{
                quoteorderGoodsMapper.insertSelective(c);
            });
        }
        // VDERP-15595 商机转报价后，需要将原商机的关联终端信息复制过来
        // step1: 先查询原商机是否有关联终端
        Optional<OrderTerminalDto> businessChanceTerminalInfo = Optional.ofNullable(orderTerminalApiService.getTerminalInfoByBusinessIdAndBusinessType(quoteorderDto.getBussinessChanceId(), 1));
        // step2: 有的话，将商机的终端信息复制一份关联到报价下面
        if (businessChanceTerminalInfo.isPresent()) {
            OrderTerminalDto exist = businessChanceTerminalInfo.get();

            OrderTerminalDto orderTerminalDto = new OrderTerminalDto();
            orderTerminalDto.setBusinessId(quoteorderDto.getQuoteorderId());
            orderTerminalDto.setBusinessNo(code);
            orderTerminalDto.setBusinessType(2);
            orderTerminalDto.setTerminalName(exist.getTerminalName());
            orderTerminalDto.setDwhTerminalId(exist.getDwhTerminalId());
            orderTerminalDto.setUnifiedSocialCreditIdentifier(exist.getUnifiedSocialCreditIdentifier());
            orderTerminalDto.setOrganizationCode(exist.getOrganizationCode());
            orderTerminalDto.setProvinceId(exist.getProvinceId());
            orderTerminalDto.setProvinceName(exist.getProvinceName());
            orderTerminalDto.setCityId(exist.getCityId());
            orderTerminalDto.setCityName(exist.getCityName());
            orderTerminalDto.setAreaId(exist.getAreaId());
            orderTerminalDto.setAreaName(exist.getAreaName());
            OrderTerminalContext orderTerminalContext = new OrderTerminalContext();
            orderTerminalContext.setOrderTerminalStrategy(quoteOrderTerminalStrategy);
            orderTerminalContext.executeStrategy(orderTerminalDto);
        }
        return quoteorderDto;
    }

	@Override
	public QuoteorderEntity queryEntityByNo(String quotationNo) {
		return quoteorderMapper.getQuoteorderEntityByQuotationNo(quotationNo);
	}

	@Override
	public AuthorizationApiApply getAuthorizationApplyByNum(String authorizationNo) {
		return quoteorderMapper.getAuthorizationApplyByNum(authorizationNo);
	}

    @Override
    public     QuoteorderEntity selectByPrimaryKey(Integer quoteorderId){
        return quoteorderMapper.selectByPrimaryKey(quoteorderId);
    }

}
