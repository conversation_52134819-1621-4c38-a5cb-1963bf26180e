function checkedOnly(obj){
    //$(obj).attr("disabled",true)
    //setTimeout(function(){$(obj).attr("disabled",false)}, 1500);
	var checkNum = Number($("#checkNum").html());
    var checkAmount = parseFloat($("#checkAmount").html());
    if($(obj).is(":checked")){
        var n = 0;
        $("#invoice_apply_list_tab").find("input[type='checkbox'][name='checkName']").each(function(){
            if($(this).is(":checked")){
                n++;
            }else{
                return false;
            }
        });
        if($("#invoice_apply_list_tab").find("input[type='checkbox'][name='checkName']").length == n){
            $("#invoice_apply_list_tab").find("input[type='checkbox'][name='checkAllOpt']").prop("checked",true);
        }
        checkNum++;
        checkAmount = checkAmount + parseFloat($(obj).attr("amount"));
    }else{
        $("#invoice_apply_list_tab").find("input[type='checkbox'][name='checkAllOpt']").prop("checked",false);
        checkNum--;
        checkAmount = checkAmount - parseFloat($(obj).attr("amount"));
    }
    $("#checkNum").html(checkNum);
    $("#checkAmount").html(checkAmount.toFixed(2));
}

function checkAllOpt(obj){
    var checkNum = 0;
    var checkAmount = 0.00;
    if($(obj).is(":checked")){
        $("#invoice_apply_list_tab").find("input[type='checkbox'][name='checkName']").each(function(){
            $(this).prop("checked",true);
            checkNum++;
            checkAmount = checkAmount + parseFloat($(this).attr("amount"));
        });
    }else{
        $("#invoice_apply_list_tab").find("input[type='checkbox'][name='checkName']").each(function(){
            $(this).prop("checked",false);
        });
    }
    $("#checkNum").html(checkNum);
    $("#checkAmount").html(checkAmount.toFixed(2));
}
function resetPage(){
    reset();
    $("#validStatus").val(0);
}

function categoryMigrationpass() {
    var categoryMigrationIdArr = [];
    $("#invoice_apply_list_tab").find("input[type='checkbox'][name='checkName']:checked").each(function(){
        if($(this).is(":checked")){
            categoryMigrationIdArr.push($(this).val());
        }
    });

    $.ajax({
        type: "POST",
        url: "./categoryMigrationExamine.do",
        data: {"categoryMigrationIdArr": JSON.stringify(categoryMigrationIdArr)},
        dataType: 'json',
        success: function (data) {
            refreshPageList(data)
        },
        error: function (data) {
            if (data.status == 1001) {
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }

    });
    
}