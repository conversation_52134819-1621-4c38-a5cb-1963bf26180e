package com.vedeng.erp.kingdee.batch.writer;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * @description: 销售订单生成销售调整单
 * @author: Suqin
 * @date: 2023/2/22 15:04
 **/
@Slf4j
@Service
public class BatchSaleSettlementAdjustmentWriter extends BaseWriter<BatchSaleSettlementAdjustmentAndItemDto> {
    @Autowired
    BatchSaleSettlementAdjustmentDtoMapper batchSaleSettlementAdjustmentDtoMapper;

    @Autowired
    BatchSaleSettlementAdjustmentItemDtoMapper batchSaleSettlementAdjustmentItemDtoMapper;

    @Override
    public void doWrite(BatchSaleSettlementAdjustmentAndItemDto item, JobParameters params, ExecutionContext stepContext) throws Exception {
        if (Objects.isNull(item) || Objects.isNull(item.getBatchSaleSettlementAdjustmentDto()) || CollUtil.isEmpty(item.getBatchSaleSettlementAdjustmentItemDtoList())) {
            return;
        }
        BatchSaleSettlementAdjustmentDto adjustmentDto = item.getBatchSaleSettlementAdjustmentDto();
        List<BatchSaleSettlementAdjustmentItemDto> adjustmentItemDtoList = item.getBatchSaleSettlementAdjustmentItemDtoList();
        log.info("销售/售后 产生调整单落表：{}", JSON.toJSONString(adjustmentDto));
        batchSaleSettlementAdjustmentDtoMapper.insertSelective(adjustmentDto);
        // 反查主键
        List<BatchSaleSettlementAdjustmentDto> dtoList = batchSaleSettlementAdjustmentDtoMapper.findByAll(adjustmentDto);
        Integer adjustmentId = CollUtil.getFirst(dtoList).getSaleSettlementAdjustmentId();
        log.info("销售/售后 产生调整单明细落表：{}", JSON.toJSONString(adjustmentItemDtoList));
        for (BatchSaleSettlementAdjustmentItemDto itemDto : adjustmentItemDtoList) {
            itemDto.setSaleSettlementAdjustmentId(adjustmentId);
            batchSaleSettlementAdjustmentItemDtoMapper.insertSelective(itemDto);
        }
    }
}
