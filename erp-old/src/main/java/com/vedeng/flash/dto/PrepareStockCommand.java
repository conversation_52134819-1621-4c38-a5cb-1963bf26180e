package com.vedeng.flash.dto;

import com.vedeng.common.controller.BaseCommand;
import com.vedeng.system.model.SysOptionDefinition;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description:  备货计划查询条件
 * @Author:       davis
 * @Date:         2021/5/18 下午3:13
 * @Version:      1.0
 */
@Data
public class PrepareStockCommand extends BaseCommand{

	/**
	 * 定品ID
	 */
	private Integer regularId;

	/**
	 * 安全预警等级
	 * 0：安全
	 * 1：二级预警
	 * 2：一级预警
	 */
	private Integer warnLevel;

	/**
	 * 商品类型
	 */
	private List<SysOptionDefinition> spuTypeList;

	/**
	 * 处理类型
	 */
	private Integer dealType;

	/**
	 * skuId
	 */
	private Integer skuId;

	/**
	 * 订货号
	 */
	private String skuNo;

	/**
	 * 商品名称
	 */
	private String skuName;

	/**
	 * 品牌
	 */
	private String brandName;

	/**
	 * 商品类型
	 */
	private Integer skuType;

	/**
	 * 归属人 (产品经理)
	 */
	private Integer assignmentManagerId;

	/**
	 * 安全库存-开始
	 */
	private Integer startSafeStock;

	/**
	 * 安全库存-结束
	 */
	private Integer endSafeStock;

	/**
	 * 是否调整安全库存
	 */
	private Integer isAdjustStock;

	/**
	 * skuNo集合
	 */
	private List<String> skuNoList;

	/**
	 * 近三个月天数
	 */
	private int threeMonthDays;

	/**
	 * 查询开始时间
	 */
	private long startDate;

	/**
	 * 查询结束时间
	 */
	private long endDate;

	/**
	 * 采购订单ID
	 */
	private Integer buyorderId;

	/**
	 * 近三个月销量
	 */
	private Integer threeMonthDaysSaleNum;

	/**
	 * 库存数量
	 */
	private Integer stock;

	/**
	 * 建议补充库存数量
	 */
	private Integer supplementStock;

	/**
	 * 在途数量
	 */
	private Integer intransitStock;

	/**
	 * 订单占用数量
	 */
	private Integer orderStock;

	/**
	 * 平均到货时间
	 */
	private BigDecimal receiveTimes;

	/**
	 * 断货天数
	 */
	private Integer outStockTimes;

	/**
	 * 成本价
	 */
	private String  cost;

	/**
	 * 是否需要分页 0：需要，1：不需要
	 */
	private Integer needPage;

	/**
	 * 导出月份
	 */
	private String exportMonth;

	/**
	 * 操作类型 0：定品操作；1：备货操作
	 */
	private Integer logType;

	/**
	 * 是否管理员
	 */
	private Integer isAdmin;

	/**
	 * 归属人ID
	 */
	private List<Integer> userIdList;

	/**
	 * 全部预警等级
	 */
	private Integer warnAll;

	/**
	 * 任务处理人ID
	 */
	private Integer taskDealerId;

}
