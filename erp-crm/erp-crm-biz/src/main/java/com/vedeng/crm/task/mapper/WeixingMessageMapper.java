package com.vedeng.crm.task.mapper;

import com.vedeng.crm.task.domain.entity.WeixingMessageEntity;
import org.apache.ibatis.annotations.Param;

public interface WeixingMessageMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(WeixingMessageEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(WeixingMessageEntity record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    WeixingMessageEntity selectByPrimaryKey(Long id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(WeixingMessageEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(WeixingMessageEntity record);


    WeixingMessageEntity selectByUserIdAndType(@Param(value="userId")Integer userId,@Param(value="mainTaskType")Integer mainTaskType);
}