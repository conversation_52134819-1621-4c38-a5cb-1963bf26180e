package com.wms.dto;

import java.math.BigDecimal;

public class InputOrderGoodsDto {

    private String SKU;

    //实际收货数量
    private BigDecimal ReceivedQty;

    //生产日期
    private String LotAtt01;

    //有效期至
    private String LotAtt02;

    //入库日期
    private String LotAtt03;

    //厂家批号
    private String LotAtt04;

    //灭菌批号
    private String LotAtt05;

    //注册证号
    private String LotAtt06;

    //专项发货关联vp单
    private String LotAtt07;

    //质量状态
    private String LotAtt08;

    //入库单号
    private String LotAtt10;

    //贝登批次码
    private String LotAtt11;
    //SN码
    private String LotAtt12;

    //ERP订单商品ID
    private String UserDefine1;

    private String UserDefine4;

    public String getUserDefine1() {
        return UserDefine1;
    }

    public void setUserDefine1(String userDefine1) {
        UserDefine1 = userDefine1;
    }

    public String getSKU() {
        return SKU;
    }

    public void setSKU(String SKU) {
        this.SKU = SKU;
    }

    public BigDecimal getReceivedQty() {
        return ReceivedQty;
    }

    public void setReceivedQty(BigDecimal receivedQty) {
        ReceivedQty = receivedQty;
    }

    public String getLotAtt01() {
        return LotAtt01;
    }

    public void setLotAtt01(String lotAtt01) {
        LotAtt01 = lotAtt01;
    }

    public String getLotAtt02() {
        return LotAtt02;
    }

    public void setLotAtt02(String lotAtt02) {
        LotAtt02 = lotAtt02;
    }

    public String getLotAtt03() {
        return LotAtt03;
    }

    public void setLotAtt03(String lotAtt03) {
        LotAtt03 = lotAtt03;
    }

    public String getLotAtt04() {
        return LotAtt04;
    }

    public void setLotAtt04(String lotAtt04) {
        LotAtt04 = lotAtt04;
    }

    public String getLotAtt08() {
        return LotAtt08;
    }

    public void setLotAtt08(String lotAtt08) {
        LotAtt08 = lotAtt08;
    }

    public String getLotAtt10() {
        return LotAtt10;
    }

    public void setLotAtt10(String lotAtt10) {
        LotAtt10 = lotAtt10;
    }

    public String getLotAtt11() {
        return LotAtt11;
    }

    public void setLotAtt11(String lotAtt11) {
        LotAtt11 = lotAtt11;
    }

    public String getLotAtt05() {
        return LotAtt05;
    }

    public void setLotAtt05(String lotAtt05) {
        LotAtt05 = lotAtt05;
    }

    public String getLotAtt06() {
        return LotAtt06;
    }

    public void setLotAtt06(String lotAtt06) {
        LotAtt06 = lotAtt06;
    }

    public String getLotAtt07() {
        return LotAtt07;
    }

    public void setLotAtt07(String lotAtt07) {
        LotAtt07 = lotAtt07;
    }

    public String getUserDefine4() {
        return UserDefine4;
    }

    public void setUserDefine4(String userDefine4) {
        UserDefine4 = userDefine4;
    }

    public String getLotAtt12() {
        return LotAtt12;
    }

    public void setLotAtt12(String lotAtt12) {
        LotAtt12 = lotAtt12;
    }

    @Override
    public String toString() {
        return "InputOrderGoodsDto{" +
                "SKU='" + SKU + '\'' +
                ", ReceivedQty=" + ReceivedQty +
                ", LotAtt01='" + LotAtt01 + '\'' +
                ", LotAtt02='" + LotAtt02 + '\'' +
                ", LotAtt03='" + LotAtt03 + '\'' +
                ", LotAtt04='" + LotAtt04 + '\'' +
                ", LotAtt05='" + LotAtt05 + '\'' +
                ", LotAtt06='" + LotAtt06 + '\'' +
                ", LotAtt07='" + LotAtt07 + '\'' +
                ", LotAtt08='" + LotAtt08 + '\'' +
                ", LotAtt10='" + LotAtt10 + '\'' +
                ", LotAtt11='" + LotAtt11 + '\'' +
                ", LotAtt12='" + LotAtt12 + '\'' +
                ", UserDefine1='" + UserDefine1 + '\'' +
                ", UserDefine4='" + UserDefine4 + '\'' +
                '}';
    }
}
