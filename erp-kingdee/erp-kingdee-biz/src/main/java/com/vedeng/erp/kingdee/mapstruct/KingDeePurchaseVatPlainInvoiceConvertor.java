package com.vedeng.erp.kingdee.mapstruct;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.kingdee.domain.entity.KingDeePurchaseVatPlainInvoiceEntity;
import com.vedeng.erp.kingdee.dto.*;
import org.mapstruct.*;

import java.util.Collections;
import java.util.List;

/**
 * 金蝶 采购普票 dto 转 实体
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface KingDeePurchaseVatPlainInvoiceConvertor extends BaseMapStruct<KingDeePurchaseVatPlainInvoiceEntity,PurchaseVatPlainInvoiceDto> {
    /**
     * KingDeePurchaseVatPlainInvoiceEntity
     *
     * @param dto PurchaseVatPlainInvoiceDto
     * @return KingDeePurchaseVatPlainInvoiceEntity
     */
    @Mapping(target = "FBillTypeId", source = "FBillTypeID")
    @Mapping(target = "fpurchaseicentry", source = "FPURCHASEICENTRY", qualifiedByName = "listToString")
    @Override
    KingDeePurchaseVatPlainInvoiceEntity toEntity(PurchaseVatPlainInvoiceDto dto);

    /**
     * 转dto
     * @param entity
     * @return
     */
    @Mapping(target = "FBillTypeID", source = "FBillTypeId")
    @Mapping(target = "FPURCHASEICENTRY", source = "fpurchaseicentry", qualifiedByName = "stringToList")
    @Override
    PurchaseVatPlainInvoiceDto toDto(KingDeePurchaseVatPlainInvoiceEntity entity);

    /**
     * dto 原List 转 JSON
     *
     * @param source 对象
     * @return String String
     */
    @Named("listToString")
    default String listToString(List<PurchaseVatPlainInvoiceDetailDto> source) {
        if (CollUtil.isEmpty(source)) {
            return null;
        }
        return JSON.toJSONString(source);
    }

    /**
     * entity 中string 转 原对象
     *
     * @param data string
     * @return List<KingDeePayBillDto> dto中的对象
     */
    @Named("stringToList")
    default List<PurchaseVatPlainInvoiceDetailDto> entryJsonArrayToList(String data) {
        if (StrUtil.isEmpty(data)) {
            return Collections.emptyList();
        }
        return JSONArray.parseArray(data,PurchaseVatPlainInvoiceDetailDto.class);
    }
}
