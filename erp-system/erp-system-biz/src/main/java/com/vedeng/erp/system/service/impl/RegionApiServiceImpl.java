package com.vedeng.erp.system.service.impl;

import com.vedeng.erp.system.common.constant.RegionConstant;
import com.vedeng.erp.system.domain.entity.RegionEntity;
import com.vedeng.erp.system.dto.RegionDto;
import com.vedeng.erp.system.mapper.RegionMapper;
import com.vedeng.erp.system.service.RegionApiService;
import com.vedeng.erp.system.service.RegionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @description 地区服务实现类
 * @date 2022/7/13 16:17
 **/
@Slf4j
@Service
public class RegionApiServiceImpl implements RegionApiService {

    @Autowired
    @Qualifier("newRegionMapper")
    private RegionMapper regionMapper;

    @Autowired
    RegionService regionService;




    @Override
    public String getThisRegionToParentRegion(Integer regionId) {

        if (regionId == null) {
            return "";
        }

        RegionEntity regionEntity = regionMapper.selectByRegionId(Long.valueOf(regionId));
        StringBuilder address = new StringBuilder();
        if (regionEntity != null && regionEntity.getParentId() != null) {
            address.append(regionEntity.getRegionName());
            Long parentId = regionEntity.getParentId();
            for (int i = 0; i <= RegionConstant.REGION_CYCLE_TIME && parentId != null && parentId != 0; i++) {

                RegionEntity parent = regionMapper.selectByRegionId(parentId);
                if (parent != null) {
                    address.insert(0, !StringUtils.isEmpty(parent.getRegionName()) ? parent.getRegionName() + " " : "");
                    parentId = parent.getParentId();
                } else {
                    parentId = null;
                }
            }
        }

        return address.toString();
    }


    @Override
    public String getThisRegionToParentRegionP(Integer regionId) {

        if (regionId == null) {
            return "";
        }

        RegionEntity regionEntity = regionMapper.selectByRegionId(Long.valueOf(regionId));
        StringBuilder address = new StringBuilder();
        if (regionEntity != null && regionEntity.getParentId() != null) {
            address.append(regionEntity.getRegionName());
            Long parentId = regionEntity.getParentId();
            for (int i = 0; i <= RegionConstant.REGION_CYCLE_TIME_TWO && parentId != null && parentId != 100000; i++) {

                RegionEntity parent = regionMapper.selectByRegionId(parentId);
                if (parent != null) {
                    address.insert(0, !StringUtils.isEmpty(parent.getRegionName()) ? parent.getRegionName() + " " : "");
                    parentId = parent.getParentId();
                } else {
                    parentId = null;
                }
            }
        }

        return address.toString();
    }

    @Override
    public String getRegion(Integer regionId) {
        return regionService.getRegion(regionId);
    }

    @Override
    public List<RegionDto> getAllRegion() {
        return regionMapper.getAllRegion();
    }

    @Override
    public RegionDto getRegionDto(Integer regionId) {
        RegionDto regionDto = new RegionDto();
        RegionEntity regionEntity = regionService.getRegionById(Long.valueOf(regionId));
        if (regionEntity != null) {
            regionDto.setRegionId(regionEntity.getRegionId().intValue());
            regionDto.setRegionType(regionEntity.getRegionType());
            regionDto.setParentId(regionEntity.getParentId().intValue());
            return regionDto;

        }
        return null;

    }

    @Override
    public String getRegionProvinceByCityName(String name) {
        return regionMapper.getRegionProvinceByCityName(name);
    }
}
