package com.vedeng.erp.finance.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 开票申请明细表
 */
@Data
public class InvoiceApplyDetailEntity implements Serializable {
    /**
     * 主键
     */
    private Integer invoiceApplyDetailId;

    /**
     * 申请表关联主键
     */
    private Integer invoiceApplyId;

    /**
     * 订单商品ID
     */
    private Integer detailgoodsId;

    /**
     * 开票单价
     */
    private BigDecimal price;

    /**
     * 开票数量
     */
    private BigDecimal num;

    /**
     * 开票总额
     */
    private BigDecimal totalAmount;

    /**
     * 修改后的商品名称
     */
    private String changedGoodsName;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 规格型号
     */
    private String specModel;

    /**
     * 单位
     */
    private String unit;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 税额
     */
    private BigDecimal taxAmount;

    /**
     * 不含税单价
     */
    private BigDecimal taxExclusivePrice;

    /**
     * 不含税金额
     */
    private BigDecimal taxExclusiveAmount;

    private static final long serialVersionUID = 1L;
}