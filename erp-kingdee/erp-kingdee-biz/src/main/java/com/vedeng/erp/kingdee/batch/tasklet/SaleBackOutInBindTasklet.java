package com.vedeng.erp.kingdee.batch.tasklet;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.vedeng.erp.kingdee.batch.chain.context.SaleBackOutInBindContext;
import com.vedeng.erp.kingdee.batch.chain.impl.FirstSaleBackOutInBindChain;
import com.vedeng.erp.kingdee.batch.common.tasklet.BaseTasklet;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInDtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @description: 销售退货出入库关系绑定
 * @author: Suqin
 * @date: 2023/1/7 10:44
 **/
@Service
@Slf4j
public class SaleBackOutInBindTasklet extends BaseTasklet {

    @Autowired
    private BatchWarehouseGoodsOutInDtoMapper batchWarehouseGoodsOutInDtoMapper;

    @Autowired
    private FirstSaleBackOutInBindChain firstBindChain;

    @Value("${valid_saleorder_time}")
    private Long validTime;

    @Override
    public void doExec(Map<String, Object> jobParameters) {
        String beginTime = (String) jobParameters.get("beginTime");
        String endTime = (String) jobParameters.get("endTime");
        DateTime begin = StringUtils.isBlank(beginTime) ? DateUtil.beginOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(beginTime);
        DateTime end = StringUtils.isBlank(endTime) ? DateUtil.endOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(endTime);

        BatchWarehouseGoodsOutInDto queryInOrder = BatchWarehouseGoodsOutInDto
                .builder()
                .isDelete(0)
                .isVirtual(0)
                .beginTime(begin)
                .endTime(end)
                // 销售退货入库类型
                .outInType(5)
                .build();

        List<BatchWarehouseGoodsOutInDto> inOrders = batchWarehouseGoodsOutInDtoMapper.findByItem(queryInOrder);


        // 没有入库单直接退出
        if (CollUtil.isEmpty(inOrders)) {
            log.info("beginTime：{}，endTime：{}，未找到符合条件的入库单", begin, end);
            return;
        }

        SaleBackOutInBindContext context = SaleBackOutInBindContext
                .builder()
                .snList(CollUtil.newArrayList())
                .batchList(CollUtil.newArrayList())
                .snAndBatchList(CollUtil.newArrayList())
                .snList2(CollUtil.newArrayList())
                .batchList2(CollUtil.newArrayList())
                .snAndBatchList2(CollUtil.newArrayList())
                .snAndBatchList3(CollUtil.newArrayList())
                .remainSnOutGoods(CollUtil.newArrayList())
                .remainBatchOutGoods(CollUtil.newArrayList())
                .remainSnBatchOutGoods(CollUtil.newArrayList())
                .inOrders(inOrders)
                .validTime(validTime)
                .build();

        // 第一次绑定
        firstBindChain.handler(context);
    }
}