package com.vedeng.goods.manager.rule;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class GoodsValidatedRule {

    private Integer goodsLevel;

    /**
     * 商品维度，1.SPU维度, 2.SKU维度
     */
    private Integer goodsDimension;

    private String description;

    private List<GoodsBasicValidElement> basicItems;

    private List<GoodsExtValidElement> additionalModules;


    public void addBasicItem(String propertyName, boolean required, String message) {
        if (basicItems == null) {
            basicItems = new LinkedList<>();
        }

        GoodsBasicValidElement goodsBasicValidElement = new GoodsBasicValidElement();
        goodsBasicValidElement.setPropertyName(propertyName);
        goodsBasicValidElement.setMessage(message);
        goodsBasicValidElement.setRequired(required);
        basicItems.add(goodsBasicValidElement);
    }

    public void addAdditionalItem(Integer serviceId, String serviceName, String message, boolean required) {
        if (additionalModules == null) {
            additionalModules = new LinkedList<>();
        }
        GoodsExtValidElement goodsExtValidElement = new GoodsExtValidElement();
        goodsExtValidElement.setServiceId(serviceId);
        goodsExtValidElement.setServiceName(serviceName);
        goodsExtValidElement.setMessage(message);
        goodsExtValidElement.setRequired(required);
        additionalModules.add(goodsExtValidElement);
    }


    public Integer getGoodsLevel() {
        return goodsLevel;
    }

    public void setGoodsLevel(Integer goodsLevel) {
        this.goodsLevel = goodsLevel;
    }

    public Integer getGoodsDimension() {
        return goodsDimension;
    }

    public void setGoodsDimension(Integer goodsDimension) {
        this.goodsDimension = goodsDimension;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<GoodsBasicValidElement> getBasicItems() {
        return basicItems;
    }

    public void setBasicItems(List<GoodsBasicValidElement> basicItems) {
        this.basicItems = basicItems;
    }

    public List<GoodsExtValidElement> getAdditionalModules() {
        return additionalModules;
    }

    public void setAdditionalModules(List<GoodsExtValidElement> additionalModules) {
        this.additionalModules = additionalModules;
    }
}
