package com.vedeng.erp.saleorder.service;

import com.vedeng.erp.saleorder.dto.QuoteLinkOrderDto;
import com.vedeng.erp.saleorder.dto.QuoteToOrderDto;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;

public interface QuoteToOrderApiService {

    /**
     * 报价转订单
     * @param quoteToOrderDto
     * @return
     */
    Integer convert(QuoteToOrderDto quoteToOrderDto);

    /**
     * 报价关联订单
     * @param quoteLinkOrderDto
     */
    void link(QuoteLinkOrderDto quoteLinkOrderDto);

    /**
     * 获取订单信息
     * @param quoteLinkOrderDto
     * @return
     */
    SaleorderInfoDto getSaleOrder(QuoteLinkOrderDto quoteLinkOrderDto);

    /**
     * 判断订单是否存在
     * @param quoteLinkOrderDto
     * @return
     */
    Integer isExistSaleOrder(QuoteLinkOrderDto quoteLinkOrderDto);
}
