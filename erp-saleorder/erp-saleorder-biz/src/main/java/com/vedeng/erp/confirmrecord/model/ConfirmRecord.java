package com.vedeng.erp.confirmrecord.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 确认记录表
 * <AUTHOR>
 */
@Data
public class ConfirmRecord implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 业务详细描述（货物签收单存入商品名称）
     */
    private String businessDesc;

    /**
     * 业务编号
     */
    private String businessNo;

    /**
     * 业务类型（0 ：订单确认 1：货物签收确认）
     */
    private Integer businessType;

    /**
     * 确认时间
     */
    private Date confirmTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 更新人
     */
    private Integer updater;

    /**
     * 更新时间
     */
    private Date modTime;

    /**
     * 发送类型(0:系统发送 1 手动发送)
     */
    private Integer sendType;

    /**
     * 发送时间
     */
    private Date sendTime;

    /**
     * 发送方式类型（0：短信 1：微信 2：邮件）可多选
     */
    private String sendMethodType;

    /**
     * 确认数量
     */
    private Integer confirmNumber;

    /**
     * 是否确认
     */
    private Integer confirmStatus;

    private static final long serialVersionUID = 1L;

    private String userName;
}