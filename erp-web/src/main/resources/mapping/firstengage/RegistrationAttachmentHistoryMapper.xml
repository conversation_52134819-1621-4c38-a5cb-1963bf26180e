<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.firstengage.dao.RegistrationAttachmentHistoryMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.firstengage.model.RegistrationAttachmentHistory" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 24 19:24:12 CST 2020.
    -->
    <id column="REIGISTRATION_ATTACHMENT_HISTORY_ID" property="reigistrationAttachmentHistoryId" jdbcType="INTEGER" />
    <result column="REGISTRATION_NUMBER_ID" property="registrationNumberId" jdbcType="INTEGER" />
    <result column="ATTACHMENT_TYPE" property="attachmentType" jdbcType="INTEGER" />
    <result column="NAME" property="name" jdbcType="VARCHAR" />
    <result column="DOMAIN" property="domain" jdbcType="VARCHAR" />
    <result column="URI" property="uri" jdbcType="VARCHAR" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="UPDATOR" property="updator" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 24 19:24:12 CST 2020.
    -->
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 24 19:24:12 CST 2020.
    -->
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 24 19:24:12 CST 2020.
    -->
    REIGISTRATION_ATTACHMENT_HISTORY_ID, REGISTRATION_NUMBER_ID, ATTACHMENT_TYPE, `NAME`, 
    `DOMAIN`, URI, ADD_TIME, MOD_TIME, CREATOR, UPDATOR
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.vedeng.firstengage.model.RegistrationAttachmentHistoryExample" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 24 19:24:12 CST 2020.
    -->
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from T_REIGISTRATION_ATTACHMENT_HISTORY
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 24 19:24:12 CST 2020.
    -->
    select 
    <include refid="Base_Column_List" />
    from T_REIGISTRATION_ATTACHMENT_HISTORY
    where REIGISTRATION_ATTACHMENT_HISTORY_ID = #{reigistrationAttachmentHistoryId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 24 19:24:12 CST 2020.
    -->
    delete from T_REIGISTRATION_ATTACHMENT_HISTORY
    where REIGISTRATION_ATTACHMENT_HISTORY_ID = #{reigistrationAttachmentHistoryId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.firstengage.model.RegistrationAttachmentHistoryExample" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 24 19:24:12 CST 2020.
    -->
    delete from T_REIGISTRATION_ATTACHMENT_HISTORY
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.firstengage.model.RegistrationAttachmentHistory" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 24 19:24:12 CST 2020.
    -->
    <selectKey resultType="java.lang.Integer" keyProperty="reigistrationAttachmentHistoryId" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_REIGISTRATION_ATTACHMENT_HISTORY (REGISTRATION_NUMBER_ID, ATTACHMENT_TYPE, 
      `NAME`, `DOMAIN`, URI, 
      ADD_TIME, MOD_TIME, CREATOR, 
      UPDATOR)
    values (#{registrationNumberId,jdbcType=INTEGER}, #{attachmentType,jdbcType=INTEGER}, 
      #{name,jdbcType=VARCHAR}, #{domain,jdbcType=VARCHAR}, #{uri,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=BIGINT}, #{modTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, 
      #{updator,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.firstengage.model.RegistrationAttachmentHistory" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 24 19:24:12 CST 2020.
    -->
    <selectKey resultType="java.lang.Integer" keyProperty="reigistrationAttachmentHistoryId" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_REIGISTRATION_ATTACHMENT_HISTORY
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="registrationNumberId != null" >
        REGISTRATION_NUMBER_ID,
      </if>
      <if test="attachmentType != null" >
        ATTACHMENT_TYPE,
      </if>
      <if test="name != null" >
        `NAME`,
      </if>
      <if test="domain != null" >
        `DOMAIN`,
      </if>
      <if test="uri != null" >
        URI,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="updator != null" >
        UPDATOR,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="registrationNumberId != null" >
        #{registrationNumberId,jdbcType=INTEGER},
      </if>
      <if test="attachmentType != null" >
        #{attachmentType,jdbcType=INTEGER},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="domain != null" >
        #{domain,jdbcType=VARCHAR},
      </if>
      <if test="uri != null" >
        #{uri,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updator != null" >
        #{updator,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.firstengage.model.RegistrationAttachmentHistoryExample" resultType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 24 19:24:12 CST 2020.
    -->
    select count(*) from T_REIGISTRATION_ATTACHMENT_HISTORY
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 24 19:24:12 CST 2020.
    -->
    update T_REIGISTRATION_ATTACHMENT_HISTORY
    <set >
      <if test="record.reigistrationAttachmentHistoryId != null" >
        REIGISTRATION_ATTACHMENT_HISTORY_ID = #{record.reigistrationAttachmentHistoryId,jdbcType=INTEGER},
      </if>
      <if test="record.registrationNumberId != null" >
        REGISTRATION_NUMBER_ID = #{record.registrationNumberId,jdbcType=INTEGER},
      </if>
      <if test="record.attachmentType != null" >
        ATTACHMENT_TYPE = #{record.attachmentType,jdbcType=INTEGER},
      </if>
      <if test="record.name != null" >
        `NAME` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.domain != null" >
        `DOMAIN` = #{record.domain,jdbcType=VARCHAR},
      </if>
      <if test="record.uri != null" >
        URI = #{record.uri,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null" >
        ADD_TIME = #{record.addTime,jdbcType=BIGINT},
      </if>
      <if test="record.modTime != null" >
        MOD_TIME = #{record.modTime,jdbcType=BIGINT},
      </if>
      <if test="record.creator != null" >
        CREATOR = #{record.creator,jdbcType=INTEGER},
      </if>
      <if test="record.updator != null" >
        UPDATOR = #{record.updator,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 24 19:24:13 CST 2020.
    -->
    update T_REIGISTRATION_ATTACHMENT_HISTORY
    set REIGISTRATION_ATTACHMENT_HISTORY_ID = #{record.reigistrationAttachmentHistoryId,jdbcType=INTEGER},
      REGISTRATION_NUMBER_ID = #{record.registrationNumberId,jdbcType=INTEGER},
      ATTACHMENT_TYPE = #{record.attachmentType,jdbcType=INTEGER},
      `NAME` = #{record.name,jdbcType=VARCHAR},
      `DOMAIN` = #{record.domain,jdbcType=VARCHAR},
      URI = #{record.uri,jdbcType=VARCHAR},
      ADD_TIME = #{record.addTime,jdbcType=BIGINT},
      MOD_TIME = #{record.modTime,jdbcType=BIGINT},
      CREATOR = #{record.creator,jdbcType=INTEGER},
      UPDATOR = #{record.updator,jdbcType=INTEGER}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.firstengage.model.RegistrationAttachmentHistory" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 24 19:24:13 CST 2020.
    -->
    update T_REIGISTRATION_ATTACHMENT_HISTORY
    <set >
      <if test="registrationNumberId != null" >
        REGISTRATION_NUMBER_ID = #{registrationNumberId,jdbcType=INTEGER},
      </if>
      <if test="attachmentType != null" >
        ATTACHMENT_TYPE = #{attachmentType,jdbcType=INTEGER},
      </if>
      <if test="name != null" >
        `NAME` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="domain != null" >
        `DOMAIN` = #{domain,jdbcType=VARCHAR},
      </if>
      <if test="uri != null" >
        URI = #{uri,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updator != null" >
        UPDATOR = #{updator,jdbcType=INTEGER},
      </if>
    </set>
    where REIGISTRATION_ATTACHMENT_HISTORY_ID = #{reigistrationAttachmentHistoryId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.firstengage.model.RegistrationAttachmentHistory" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 24 19:24:13 CST 2020.
    -->
    update T_REIGISTRATION_ATTACHMENT_HISTORY
    set REGISTRATION_NUMBER_ID = #{registrationNumberId,jdbcType=INTEGER},
      ATTACHMENT_TYPE = #{attachmentType,jdbcType=INTEGER},
      `NAME` = #{name,jdbcType=VARCHAR},
      `DOMAIN` = #{domain,jdbcType=VARCHAR},
      URI = #{uri,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATOR = #{updator,jdbcType=INTEGER}
    where REIGISTRATION_ATTACHMENT_HISTORY_ID = #{reigistrationAttachmentHistoryId,jdbcType=INTEGER}
  </update>

  <select id="getListByRegistrationNumberId" resultMap="BaseResultMap">
    SELECT * FROM T_REIGISTRATION_ATTACHMENT_HISTORY WHERE REGISTRATION_NUMBER_ID=#{numberId}
    AND ADD_TIME>UNIX_TIMESTAMP(date_sub(curdate(),interval 5 YEAR))*1000
    ORDER BY REIGISTRATION_ATTACHMENT_HISTORY_ID DESC
  </select>
</mapper>