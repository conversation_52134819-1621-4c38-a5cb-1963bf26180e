package com.vedeng.erp.finance.service;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.finance.dto.TaxClassificationDto;
import com.vedeng.erp.finance.dto.TaxcodeClassificationDto;


/**
 * <AUTHOR>
 * @version 1.0
 * @description:  税收分类编码
 * @date 2023/9/20 13:03
 */
public interface TaxClassificationService {
    /**
     * 根据编码获取税收分类
     */
    TaxcodeClassificationDto findByCode(String code);

    /**
     * 根据查询条件获取编码分页列表
     */
    PageInfo<TaxcodeClassificationDto> findPageList(PageParam<TaxcodeClassificationDto> pageParam);
}
