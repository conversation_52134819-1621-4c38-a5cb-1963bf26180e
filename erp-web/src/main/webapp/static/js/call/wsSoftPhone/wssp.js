var wssp;
var click = 1;
var alertType = 0;

var startTime = new Date().getTime();

var settings={
    agentId:$('#agentID').val(),
    agentName:$('#agentName').val(),
    ins:$('#devNum').val(),
    serverUri: callCenterServerUrl,
    serverUri2 : '',
    delegatorId:'',
    workModeSettings:'3',
    autoReconnect : true,
    customInitParams:'',
    saveLocalLog:true,
    isAutoAnswer:false,
    isConnectIpt:false,
    softPhoneCode:''
};

var reconnectCount=0;
function window_onload_btn(){
    callRecord('window_onload_btn');
    window_onload();
}

/**
 * 来电手机号格式为8618651669603 并且前后存在有格式的情况。如果是手机号，将手机号提取，便于后端保存聊天记录
 * @param input
 * @returns {*}
 */
function extractOrClean(input) {
    // 去除所有空格和前导 +
    const cleaned = input.replace(/\s+/g, '').replace(/^\+/, '');

    // 匹配以86开头，后跟合法手机号（11位）
    const match = cleaned.match(/^86(1[3-9]\d{9})$/);

    return match ? match[1] : cleaned;
    // 测试
    // console.log(extractOrClean('+8618735378408'));   // 输出: 18735378408
    // console.log(extractOrClean('8618735378408'));    // 输出: 18735378408
    // console.log(extractOrClean('86 18735378408'));   // 输出: 18735378408
    // console.log(extractOrClean('862566851234'));     // 输出: 862566851234
    // console.log(extractOrClean(' +861865 166 9603')); // 输出: 18651669603
    // console.log(extractOrClean(' random string '));  // 输出: randomstring
}

// 初始化软链接
function window_onload() {
    if (!wssp) {
        wssp = new WSSoftPhone({
            agentId : settings.agentId, //工号
            agentName : settings.agentName,
            ins : settings.ins,
            serverUri : settings.serverUri,
            serverUri2 : settings.serverUri2,
            delegatorId : settings.delegatorId,
            workModeSettings : settings.workModeSettings,
            customInitParams : settings.customInitParams,
            saveLocalLog : settings.saveLocalLog,
            isAutoAnswer :settings.isAutoAnswer,
            isConnectIpt: settings.isConnectIpt,
            softPhoneCode:settings.softPhoneCode,

        }, {
            onInited : function(delegatorId) {
                // self.onInited(delegatorId);
                console.log('onInited')
                wssp.delegatorId = delegatorId
                $('#delegateId').val(delegatorId)
                //wssp.logon();
            },
            onNotifySettings : function() {
                //self.onNotifySettings();
                console.log('onNotifySettings')
            },
            onLogonSucc : function(ins) {
                Trace('登录成功')
                $("#btn_logon").hide();
                $("#btn_logoff").show();
                // $("#model").show();
            },
            onLogonFailed : function(ins, errNum, errDesc) {
                console.log('onLogonFailed：' + ins)
                window_onload();
                $("#btn_logon").show();//登录
                $("#btn_logoff").hide();//登出
                // $("#model").hide();//模式
                // $("#status").hide();//状态
                TraceModel("签入失败");
                Trace("签入失败:" + errNum + ", " + errDesc);
            },
            onLogoffSucc : function(ins) {
               // console.log('onLogoffSucc')
                Trace('登出成功' )
                $("#btn_logon").show();
                $("#btn_logoff").hide();
            },
            onTrace : function(strmsg) {
              //  console.log(strmsg)
                // Trace(strmsg )
            },
            onWSOpen : function() {
                //console.log('onWSOpen')
                wssp.logon()
            },
            onWSClose : function(e) {
               // console.log('onWSClose')
             //   $.get("/static/js/call/test.js?"+$('#agentID').val()+Math.random());
                // if(wssp&&!wssp.wsConnected&&settings.autoReconnect&&reconnectCount<3){
                //     reconnectCount++;
                //      wssp.connectServer();
                // }
            },
            onWSConnectFailed:function(){
               // console.log('onWSConnectFailed')

                // setTimeout(function(){
                //     if(wssp&&!wssp.wsConnected&&settings.autoReconnect&&reconnectCount<5){
                //         reconnectCount++;
                //         wssp.connectServer();
                //     }else{
                //         alert("连接通话中心网络异常，重试次数超过5次，请重新登录。")
                //     }
                // } , 3000);


            },
            onStateChange : function(state) {
              //  console.log('onStateChange'+state)
                onStateChange(state);
            },
            onWorkModeChange : function(workmode) {
                var model_name = '普通模式';
                switch(workmode){
                    case "0":
                        model_name = '普通模式';
                        //关闭监听
                        $("#monitor").hide();
                        break;
                    case "1":
                        model_name = '下班模式';
                        //关闭监听
                        $("#monitor").hide();
                        break;
                    case "3":
                        model_name = '外拨模式';
                        //关闭监听
                        $("#monitor").hide();
                        break;
                    case "4":
                        model_name = '班长模式';
                        //打开监听
                        $("#monitor").show();
                        break;
                }
                TraceModelName(model_name);
               // console.log('onWorkModeChange'+workmode)
            },
            onQueueChange : function(queueList) {
              //  console.log('onQueueChange')
                queueChangeHandler(queueList);
            },
            onShowTip : function(info) {
               // console.log(info)
                Trace(info)
            },
            onCallArrive : function(coInfos) {
                // 来电
               // console.log('onCallArrive')
                var ANI="";
                var SK="";
                var COID = "";
                var DNIS = "";
                ANI=extractOrClean(wssp.coInfos.ANI);
                DNIS=wssp.coInfos.DNIS;
                SK= wssp.coInfos.SK;
                COID= wssp.coInfos.COID;
                if(ANI.length != 4 ){ //内呼不弹屏
                    callin(ANI);
                }
                Trace ("收到从" + ANI+">>>"+DNIS + "来的电话");
                $("#phone_span").html(ANI+">>>"+DNIS);
                let type = coInfos[17];
                switch(type){
                    case '0':
                        $("#phone_type").html('呼入电话');
                        TraceModel("通话准备(振铃)");
                        break;
                    case '1':
                        $("#phone_type").html('呼出电话');

                        break;
                    case '2':
                        $("#phone_type").html('内部通话');
                        TraceModel("内呼中(振铃)");
                        break;
                }
            },
            onPrepareDialSucc : function() {
                // 进行号码输入界面或者通讯录显示
              //  console.log('onPrepareDialSucc')
            },
            onPrepareCallOutSucc : function() {
                // 显示号码输入界面或者通讯录显示
            //    console.log('onPrepareCallOutSucc')
            },
            onDialBegin : function(coInfos) {
                // 进行外拨信息显示
             //   console.log('onDialBegin')
                dialBeginHandler(coInfos);
            },
            onDialSucc : function(coInfos) {
                //外拨成功
               // console.log('onDialSucc')
                startTime = new Date().getTime()
                timedCount();
                let type = coInfos[17];
                switch(type){
                    case '0':
                        $("#phone_type").html('呼入电话');
                        TraceModel("通话");
                        Trace("呼入成功，开始通话");
                        break;
                    case '1':
                        $("#phone_type").html('呼出电话');
                        TraceModel("通话");
                        Trace("外拨成功，开始通话");
                        break;
                    case '2':
                        $("#phone_type").html('内部通话');
                        TraceModel("内部通话");
                        Trace("内呼成功，开始内部通话");
                        break;
                }
            },
            onDialFailed : function(errorCode) {
              //  console.log('onDialFailed')
            },
            onWebSocketReplaced : function() {
                // 屏蔽软电话界面，仅显示重新创建的按钮
             //   console.log('onWebSocketReplaced')
                var r=confirm("软电话在其他地方登录，确定登录当前软电话吗？")
                if (r&&settings.autoReconnect) {
                    wssp.init();
                }
                else{
                    settings.autoReconnect=false;
                    Trace("软电话在其他地方登录,且选择不自动登录");
                }
            },
            onConfirmReplaceWebSocket : function() {
                // 存在已有连接，确认是否需要顶替
              //  console.log('onConfirmReplaceWebSocket')
                var r=confirm("软电话在其他地方登录，确定登录当前软电话吗？")
                if (r&&settings.autoReconnect) {
                    wssp.init();
                }
                else{
                    settings.autoReconnect=false;
                    Trace("软电话在其他地方登录,且选择不自动登录");
                }
            },
            onRefreshAgentListSucc : function(agentList) {
                // 显示坐席列表供选择
              //  console.log('onRefreshAgentListSucc')
                refreshAgentListSuccHandler(agentList);
            },
            onDevConnected : function() {
               // console.log('onDevConnected');
                startTime = new Date().getTime();
                timedCount();
                let type = wssp.coInfos[17];
                switch(type){
                    case '0':
                        $("#phone_type").html('呼入电话');
                        TraceModel("通话");
                        Trace("呼入成功，开始通话");
                        break;
                    case '1':
                        $("#phone_type").html('呼出电话');
                        TraceModel("通话");
                        Trace("外拨成功，开始通话");
                        break;
                    case '2':
                        $("#phone_type").html('内部通话');
                        TraceModel("内部通话");
                        Trace("内呼成功，开始内部通话");
                        break;
                }
            },
            onDevDisconnected : function() {
            //    console.log('onDevDisconnected')
                //TODO CC
                if(c*1>0){//接通电话
                    stopCount();
                    timeEnd();
                }
                addComrecord()
            },
            onConsultStart : function(coInfos) {
               // console.log('onConsultStart')
            },
            onConsultSucc : function(coInfos) {
             //   console.log('onConsultSucc')
            },
            onConsultFailed : function(coInfos) {
              //  console.log('onConsultFailed')

            },
            onConsultOver : function(coInfos) {
               // console.log('onConsultOver')
                wssp.fetchHold();
            },
            onFunctionSwitch : function(functionCode, isSupport, isOn) {
               // console.log('onFunctionSwitch')
            },
            onWSHello : function() {
               // console.log('onWSHello')
            },
            onIptWSOpen : function() {
                //console.log('onIptWSOpen')
            },
            onIptWSClose : function() {
               // console.log('onIptWSClose')
            },
            onIptWSConnectFailed:function(){
              //  console.log('onIptWSConnectFailed')
            },
            onIptStateChange:function(st,isMute){
               // console.log('onIptStateChange')
            },
            onOneStepTransferSucc : function(coInfos) {
                this.trace("onOneStepTransferSucc:" + coInfos.CCIVR_TRANSFERTYPE + ":" + coInfos.CCIVR_TRANSFERDEST);
                stopCount();
                timeEnd();
            },
            onConsultTransferSucc: function(coInfos) {
                this.trace("onConsultTransferSucc:" + coInfos.CCIVR_TRANSFERTYPE + ":" + coInfos.CCIVR_TRANSFERDEST);
                stopCount();
                timeEnd();
            },
            onTransferSucc: function(coInfos) {
                this.trace("onTransferSucc:" + coInfos.CCIVR_TRANSFERTYPE + ":" + coInfos.CCIVR_TRANSFERDEST);
            },
            onFetchHoldSucc: function (coInfos){
                TraceModel("通话");
                Trace("取回成功，开始通话");
            },
            onCallInnerSucc: function (coInfos){
                startTime = new Date().getTime()
                timedCount();
                TraceModel("内部通话");
                Trace("内呼成功，开始内部通话");
            },
            onFinishWrapupSucc: function (){
                clearTimeEnd();
            },
            onFinishMonitorSucc: function (){
                timeEnd();
            },
            onCallInnerBegin: function (){
                let DNIS = wssp.coInfos.DNIS;
                $("#phone_type").html('内呼电话');
                $("#phone_span").html(DNIS);
                TraceModel("内部通话");
                Trace("开始内呼，等待对方坐席接通");
            },
            onDropCallSucc: function (){
                if(c*1>0){//接通电话
                    stopCount();
                    timeEnd();
                }
                addComrecord();
            }

        });
        //  wssp.connectServer();
        ////判断是否启用ipt服务
        //if( self.settings.isConnectIpt){
        //	this.wssp.connectIpt();
        //}
        //启动定时器 TODO
        startTimer();
        Trace("正在初始化...");
       // console.log('初始化软电话成功');

    } else {
        alertWhenStateChangeToOne('onload');
       // Trace("初始化软电话成功...");
    }
    connectWsServer();
}


let isWorking = false;
let timerSource;
let tryConnectIpt;

function startTimer ()  {
    //console.log('startTimer 定时器启动');
    // 先确认停止Timer
    stopTimer();
    // 设置工作标识，启动Timer
    isWorking = true;
    timerSource = setInterval( onTimer , 3000);

}

function stopTimer() {
    // 复位工作标识
    this.isWorking = false;
    // 尝试停止并释放Timer
    if ( timerSource) {
        clearTimeout( timerSource);
        timerSource = null;
    }
}

function onTimer() {
    if ( isWorking) {
        wssp.wsHello("");
    }
    if( isWorking &&  settings.isConnectIpt &&  wssp.isLogon){
        this.wssp.iptHeartBeat("");
    }
    if( tryConnectIpt){
        wssp.connectIpt();
    }
}


var c=0
var t

const interval = 1000;

/**
 * 计时器
 */
function timedCount() {
    c = parseInt(((new Date().getTime() - startTime)/1000).toString());
   // console.log('通话时间:' + c);

    hour = parseInt(c / 3600);// 小时数
    min = parseInt(c / 60);// 分钟数
    if(min>=60){
        min=min%60
    }
    lastsecs = c % 60;
    if(hour<10){
        hour = '0'+hour;
    }
    if(min<10){
        min = '0'+min;
    }
    if(lastsecs<10){
        lastsecs = '0'+lastsecs;
    }

    $("#time_span").html(hour+":"+min+":"+lastsecs);
    // c=c+1
    t=setTimeout("timedCount()",1000);
}


function stopCount()
{
    c=0;
    clearTimeout(t);
}


let e = 60;
let et;

function timeEnd(){
    e--;
    TraceModel("话后");
    Trace("通话结束-自动完成时间:"+e);
    let newCallCenterFlag = $('#newCallCenterFlag').val();
   // console.log('话后清除显示 newCallCenterFlag:' + newCallCenterFlag);
    if (newCallCenterFlag == 1){
        $('#callLine').html('');
        $('#phonepool').html('');
    }
    if(e == 0){
        finishWrapup();
    }else{
        et = setTimeout("timeEnd()",1000);
    }
}

function clearTimeEnd(){
    e=60;
    clearTimeout(et);
}

/**
 * 外拨计时
 */
function timeEndOut(msg){
    e--;
    Trace(msg+"-自动完成时间:"+e);
    if(e == 0){
        finishWrapup();
    }else{
        et = setTimeout("timeEndOut('"+msg+"')",1000);
    }
}


function connectWsServer(){
    callRecord("connectWsServer");
    if (wssp.connectServer() == false){
        Trace("连接CallCenter失败！");
    } else {
        TraceModel("CallCenter登录中");
    }
}


//登录cc
function callCenterLogon(){
    // if (!wssp.isKeepLogon){
    //     wssp.logon()
    // }
    // wssp.checkWebSocket();
    connectWsServer()
}

//登录
function cmdSetParam_onclick() {
    //是否监听
    $.ajax({
        type:'get',
        url:page_url+'/system/call/getisinit.do',
        dataType:'json',
        async:false,
        success:function(data){
            if(data.code == -1){
                if(wssp.connectServer() == false)
                    Trace("连接Server失败！");
                else
                    TraceModel("已连接");
                ////***连接后自动登录
                // wssp.logon();
            }else{
                click++;
                if(click == 5){
                    if(wssp.connectServer() == false)
                        Trace("连接Server失败！");
                    else
                        TraceModel("已连接");
                    ////***连接后自动登录
                    // wssp.logon();
                }else{
                    $("#btn_logon").show();//登录
                    Trace("坐席已在其它页面登录！");
                }
            }
        }
    });
}

//登出
function cmdSetParam_offClick(){
    wssp.logoff();
}


/**
 * 模式后面的提示语句
 */
function TraceModel(msg)
{
    $("#trace_model").html(msg);
}
/**
 * 模式类型
 */
function TraceModelName(msg)
{
    $("#model_span").html(msg);
}
/**
 * 提示信息
 */
function Trace(msg)
{
    $("#trace_msg").html(msg);
}

/**
 * 呼入呼出类型
 */
function TracePhoneType(msg){
    $("#phone_type").html(msg);
}


/**
 * 更改模式
 */
function changeModel(){
    wssp.setWorkMode($("select[name='model']").val());
    callRecord("changeModel");
}


/**
 * 离席复席
 */
function changeStatus(){
    let status = wssp.agentState;
    let changeWorkStatus = $("select[name='status']").val();
    if(changeWorkStatus != 4){
        if(status == 3){
            wssp.leave(changeWorkStatus);
            console.log("变更工作状态：" + changeWorkStatus);
        }else{
            if(status == 23){
                Trace("仅在正常工作状态下，允许更改离席原因");
            }else{
                Trace("仅在空闲状态下，允许离席");
            }
        }
    }else{
        if(status == 23){
            wssp.resumeWork();
        }else{
            Trace("仅在非正常状态下，允许复席");
        }
    }
    callRecord("changeStatus");
}

/**
 * 取消外拨
 */
function cancelDial(){
    wssp.cancelDial();
    callRecord("cancelDial");
}
/**
 * 挂机
 */
function dropCall(){
    wssp.dropCall();
    callRecord("dropCall");
}
/**
 * 完成
 */
function finishWrapup(){
    wssp.finishWrapup();
    callRecord("finishWrapup");
}

/**
 * 准备监听
 */
function prepareMonitor(){
    wssp.prepareMonitor();
    callRecord("prepareMonitor");
}
/**
 * 结束监听
 */
function finishMonitor(){
    wssp.finishMonitor();
    callRecord("finishMonitor");
}

/**
 * 准备内呼
 */
function prepareCallInner(){
    wssp.prepareCallInner();
    callRecord("prepareCallInner");
}
/**
 * 取消内呼
 */
function cancelCallInner(){
    wssp.cancelCallInner();
    callRecord("cancelCallInner");
}

function changeMycall(){
    $.get("/system/call/static/changeMyCall.do?type="+$("select[name='mycall']").val(),function(data){
        if($("select[name='mycall']").val() == 0){
            Trace("主叫号码：025-66857200");
        }
        if($("select[name='mycall']").val() == 1){
            Trace("主叫号码：号码池内号码");
        }
        if($("select[name='mycall']").val() == 2){
            cal();
            Trace("专属主叫号码"+$("#ttNumber").val()+'今日剩余次数：' + $("#ttNumberCount").val());
        }
    });
    callRecord("changeMycall");
}

function  cal(){
    $.ajax({
        url: '/system/call/static/getTtNumber.do',
        data:{"traderId":1,"traderType":1},
        type:"POST",
        dataType : "json",
        async: false,
        success:function(data)
        {
            if(data.code == 0){
             //   console.log("getTtNumber "+data.data.ttNumber);
                $("#ttNumberCount").val(data.data.leftCount);
                $("#ttNumber").val(data.data.ttNumber);
                $("select[name=mycall]").val(data.data.typevalue);
            }else{
                $("#ttNumber").val('');
                $("#ttNumberCount").val(0);
            }
        },
        error:function(data){
            if(data.status == 1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
            $("#ttNumber").val('');
            $("#ttNumberCount").val(0);
            alert("网络错误")
        }
    });
}


function onStateChange(state){
    switch(state){
        case '0':
            $("#btn_logon").show();//登录

            $("#btn_logoff").hide();//登出
            // $("#model").hide();//模式
            // $("#status").hide();//状态

            $("#transfer").hide();//转接

            $("#consult").hide();//内部咨询
            $("#cancelPrepareConsult").hide();//取消内部咨询准备
            $("#cancelConsult").hide();//取消内部咨询

            $("#consultTransfer").hide();//咨询转接

            $("#cancelDial").hide();//取消外拨

            $("#hold").hide();//保持
            $("#fetchHold").hide();//取回保持

            $("#dropCall").hide();//结束
            $("#finishWrapup").hide();//完成

            $("#callInner").hide();//内呼
            $("#cancelPrepareCallInner").hide();//取消内呼准备
            $("#cancelCallInner").hide();//取消内呼

            $("#monitor").hide();//监听
            $("#cancelPrepareMonitor").hide();//取消监听
            $("#finishMonitor").hide();//结束监听

            $("#call").hide();
            $("#time").hide();

            TraceModelName("普通模式");
            TraceModel("未登录");
            Trace("签出成功");
            break;
        case '1':
            $("#btn_logon").hide();

            // $("#model").show();

            //模式初始化
            $("select[name='model']").val(0);
            wssp.setWorkMode(0);


            //状态初始化
            $("select[name='status']").val(4);

            //计时器初始化
            stopCount();
            clearTimeEnd();

            TraceModelName("普通模式");
            TraceModel("已登录");
            Trace("签入成功");

            //添加监控
            alertWhenStateChangeToOne('1');
            break;
        case '3'://空闲
            console.log('空闲 onStateChange'+state);
            $("#cancelCallInner").hide();
            $("#cancelPrepareCallInner").hide();
            $("#finishWrapup").hide();
            $("#cancelPrepareMonitor").hide();
            $("#finishMonitor").hide();
            $("#call").hide();
            $("#time").hide();
            $("#cancelPrepareConsult").hide();
            $("#callInner").show();
            // $("#status").show();
            // $("#btn_logoff").show();
            // $("#model").show();
            // $("#btn_logon").hide();//登录
            // $("#btn_logoff").show();
            //班长监听
            if($("select[name='model']").val() == 4){
                $("#monitor").show();
            }else{
                $("#monitor").hide();
            }

            TraceModel("空闲");

            try{
                // changeMycall()
                //Trace("");
            }catch(e){

            }
            break;
        case '5'://通话准备(震铃)
            $("#callInner").hide();
            //班长监听
            $("#call").show();
            break;
        case '6'://通话
            $("#cancelDial").hide();
            $("#fetchHold").hide();//取回保持


            $("#transfer").hide();//转接

            $("#cancelPrepareConsult").hide();//取消内部咨询准备
            $("#cancelConsult").hide();//取消内部咨询

            $("#consultTransfer").hide();//咨询转接

            $("#consult").show();//内部咨询
            $("#transfer").show();//转接
            $("#hold").show();//保持
            $("#dropCall").show();//结束

            $("#time").show();//结束
            $("#call").show();//结束
            break;
        case '7'://保持
            $("#hold").hide();//保持
            $("#fetchHold").show();//取回保持
            TraceModel('保持');
            Trace('保持成功');
            break;
        case '8'://外拨准备(输入号码)
            $("#callInner").hide();

            break;
        case '9'://外拨中
            $("#callInner").hide();

            $("#cancelDial").show();
            $("#call").show();
            TraceModel('外拨中');
            Trace('开始外拨');
            break;
        case '10'://咨询请求中（震铃）
            $("#transfer").hide();//转接
            $("#consult").hide();

            $("#hold").hide();//保持

            $("#cancelPrepareConsult").show();
            var agent = window.parent.document.getElementById('monitorAgent').value;
            window.parent.document.getElementById('monitorAgent').value = '';

            TraceModel('内部咨询请求中（震铃）');
            Trace('正在呼叫座席('+agent+')');
            break;
        case '11'://咨询接通
            $("#cancelPrepareConsult").hide();

            $("#cancelConsult").show();
            $("#consultTransfer").show();
            TraceModel('内部咨询');
            Trace('咨询成功，开始通话');
            break;
        case 12://
            break;
        case 13://
            break;
        case 14://
            break;
        case 15://
            break;
        case '16'://监控准备(选择座席)
            $("#callInner").hide();//内呼
            $("#monitor").hide();//监听
            $("#consult").hide();//内部咨询
            $("#transfer").hide();//内部转接
            $("#hold").hide();//保持
            $("#dropCall").hide();//结束
            $("#cancelPrepareMonitor").show();//取消监听
            TraceModel('监控准备(选择座席)');
            Trace("请选择监控的座席");
            break;
        case '17'://监控
            $("#cancelPrepareMonitor").hide();//取消监听
            $("#finishMonitor").show();//取消监听
            break;
        case '18'://
            break;
        case '19'://话后
            if(c<=0){
                timeEnd();
            }
            $("#cancelDial").hide();
            $("#consult").hide();//内部咨询
            $("#transfer").hide();//内部转接
            $("#hold").hide();//保持
            $("#fetchHold").hide();//取回保持
            $("#dropCall").hide();//结束
            $("#finishMonitor").hide();
            $("#cancelCallInner").hide();
            $("#cancelPrepareMonitor").hide();
            $("#cancelConsult").hide();//取消内部咨询
            $("#consultTransfer").hide();//咨询转接
            $("#cancelPrepareConsult").hide();
            $("#finishWrapup").show();
            break;
        case 20://
            break;
        case 21://
            break;
        case 22://
            break;
        case '23'://离席
            $("#callInner").hide();//内呼
            $("#monitor").hide();//监听
            var status = wssp.leaveReason;
            var status_name = '';
            switch(status){
                case "0":
                    status_name = '小休';
                    break;
                case "1":
                    status_name = '开会';
                    break;
                case "2":
                    status_name = '就餐';
                    break;
                case "3":
                    status_name = '其它工作';
                    break;
            }
            TraceModel('离席('+status_name+')');
            Trace("离席成功");
            break;
        case '24'://内呼准备(选择座席)
            $("#monitor").hide();//监听
            $("#callInner").hide();//内呼
            $("#cancelPrepareCallInner").show();//取消内呼
            TraceModel('内呼准备(选择座席)');
            Trace("请选择内呼的座席");
            break;
        case '25'://内呼中(震铃)
            $("#cancelPrepareCallInner").hide();
            $("#cancelCallInner").show();
            $("#call").show();
            break;
        case '26'://内部通话
            $("#time").show();//结束
            break;
        case 27://
            break;
        case 28://
            break;
        case 29://
            break;
        case 30://
            break;
        case 31://
            break;
        case 32://
            break;
        case 33://
            break;
        case 34://
            break;
        case 35://
            break;
        case 36://
            break;
        case 37://
            break;
        case 38://
            break;
        case 39://
            break;
        case 40://
            break;
        case 41://
            break;
        case 42://
            break;
        case 43://
            break;
        case 44://
            break;
        case 45://
            break;
        case 46://
            break;
        case 47://
            break;
        case 48://
            break;
        case 49://
            break;
    }

}


/**
 * 咨询准备
 */
function consult(){
    alertType = 2;
    wssp.prepareConsult();
    callRecord("consult");
}

/**
 * 取消咨询准备
 */
function cancelConsult(){
    TraceModel("通话");
    Trace("取消咨询，开始通话");
    wssp.cancelConsult();
    callRecord("cancelConsult");
}
/**
 * 取消咨询
 */
function finishConsult(){
    wssp.finishConsult();
    callRecord("finishConsult");
}
/**
 * 咨询转接
 */
function consultTransfer(){
    wssp.consultTransfer();
    callRecord("consultTransfer");
}

/**
 * 转接准备
 */
function transfer(){
    alertType = 1;
    wssp.refreshTransferAgentList();
    callRecord("transfer");
}

/**
 * 保存
 */
function hold(){
    wssp.hold();
    callRecord("hold");
}

/**
 * 取回保存
 */
function fetchHold(){
    wssp.fetchHold();
    callRecord("fetchHold");
}



/**
 * 坐席刷新
 */
function refreshAgentListSuccHandler(agentList){
    var agentstate = wssp.agentState;//模式

    var type = alertType;
    alertType = 0;

    var customer_on = {};//坐席（在线）
    var customer_leave = {};//分机（离线）

    for (var i = 0; i < agentList.items.length; i++){
        var item = agentList.items[i];
        var agentObj = {};
        //坐席
        //TODO 坐席问题
        if (item.itemType === '1'){
            agentObj['INS'] = item.ins;
            agentObj['AGENTNAME'] = item.agentName;
            agentObj['ST'] = item.state;
            agentObj['WKMD'] = item.workMode;
            customer_on[i] = agentObj;
        } else {
            //分机
            agentObj['INS'] = item.ins;
            agentObj['AGENTNAME'] = item.devOwner;
            agentObj['ST'] = item.calST;
            // if (item.calST === 23){
            customer_leave[i] = agentObj;
            // }
        }
    }

    //json字符串
    customer_on = JSON.stringify(customer_on);
    customer_leave = JSON.stringify(customer_leave);
    // //url编码
    // customer_leave = encodeURI(customer_leave);
    // customer_on = encodeURI(customer_on);

    $.post({
        url: '/system/call/saveAgentList.do',
        data: {
            agentOn: customer_on,
            agentLeave: customer_leave
        },
        success: function (){
            self.parent.layer.config({
                extend: 'vedeng.com/style.css', //加载您的扩展样式
                skin: 'vedeng.com'
            });
            self.parent.callAgent = self.parent.layer.myopen({
                type: 2,
                shadeClose: false, //点击遮罩关闭
                closeBtn: false,
                area: ['600px','434px'],
                title: false,
                content: ['/system/call/getagentlist.do?type='+type+'&agentState='+agentstate,'no'],
                success: function(layero, index) {
                    //layer.iframeAuto(index);
                }
            });
        }
    })


}


function queueChangeHandler(queueList){
    let str = '';
    for (let i = 0; i < queueList.length; i++) {
        let title = '';
        let customer = '';

        const ANI = queueList[i].phoneNum;
        $.ajax({
            type:'get',
            url:page_url+'/system/call/gettraderinfo.do',
            data:{phone:ANI},
            dataType:'json',
            async:false,
            success:function(data){
                if(data.code == 0){
                    title = data.data.traderName;
                    customer = ' '+data.data.traderName;
                }
            }
        });
        str += "<li title="+title+">"+ANI+customer+"</li>";
    }
    $(".call-queue-list").html(str);
}



/**
 * 去电弹屏
 */
function dialBeginHandler(coInfos) {
    var phone = coInfos.DNIS;
    console.log("start phone："+phone)
    phone = phone.replace("#", "");
    phone = phone.replace(/\b(0+)/gi, "");

    //电信线路出局码处理
    var telecomThisPlateReg = /^11[34578]{1}\d{9}$/;
    var telecomOtherPlateReg = /^101[34578]{1}\d{9}$/;
    if (telecomThisPlateReg.test(phone)){
        phone = phone.substring(1);
    }
    if (telecomOtherPlateReg.test(phone)){
        phone = phone.substring(2);
    }


    var reg = /^1[345789]{1}\d{9}$/;
    console.log("end phone："+phone)
    if (reg.test(phone)) {
        phone = phone;
    } else {
        if (phone.length > 8) {
            phone = '0' + phone;
        }
    }
    console.log('csSoftPhone_evtDialBegin' + phone)
    var coid = coInfos.COID;
    var pp=$("#currentTtNumber").val();

    // if(orginPhone.startsWith('3') || orginPhone.startsWith('5') || orginPhone.startsWith('9')){
    //获取主叫号码
    //先判断 是否有 1对1 号码
    console.log("start with [3|5|9]")
    cal();

    // }
    try {

        //回拨更新记录信息
        if(self.parent.document.getElementById("callFailedId").val != undefined && self.parent.document.getElementById("callFailedId").val > 0){
            console.log("csSoftPhone_evtDialBegin faile")
            var callFailedId = self.parent.document.getElementById("callFailedId").val;
            if(callFailedId){
                $.ajax({
                    type:'get',
                    url:page_url+'/system/call/editcallfailedcoid.do',
                    data:{'callFailedId':callFailedId,'coid':coid},
                    dataType:'json',
                    async:false,
                    success:function(data){
                        if(data.code==0){
                            self.parent.document.getElementById("callFailedId").val = '0';
                        }
                    }
                });
            }
        }
        console.log('去电弹屏 wssp.js phone:' + phone);
        calloutScreen(phone,coid);
        $("#phone_type").html('呼出电话');
        // if($("select[name='mycall']").val()==2){
        //     if( $("#ttNumber").val()!='') {
        //         $("#phone_span").html('被叫号码：' + phone + '>>当前主叫号码：' + pp + '>>今日剩余次数：' + $("#ttNumberCount").val());
        //     }else{
        //         $("#phone_span").html( '被叫号码：'+ phone+'>>当前主叫号码：'+pp);
        //     }
        // }else{
        $("#phone_span").html( '被叫号码：'+ phone );
        //}
    }catch(e){
        // alert('请安装最新插件来支持号码池功能');
        console.log('请安装最新插件来支持号码池功能')
    }

}


function alertWhenStateChangeToOne(from){
    var agentStateInfo = {
        agentId: wssp.agentId,
        ins: wssp.ins,
        workMode: wssp.agentWorkMode,
        state: wssp.agentState,
        isLogon: wssp.isLogon
    }
    $.ajax({
        type:'get',
        url:page_url+'/system/call/stateChangePoint.do?from='+from+'&agentId=' + agentStateInfo.agentId + "&ins=" + agentStateInfo.ins + "&workMode=" + agentStateInfo.workMode
        + "&state=" + agentStateInfo.state + "&isLogon=" + agentStateInfo.isLogon,
        dataType:'json',
        async:true
    })
}