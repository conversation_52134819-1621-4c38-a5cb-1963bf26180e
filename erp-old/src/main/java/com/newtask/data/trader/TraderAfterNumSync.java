package com.newtask.data.trader;

import com.newtask.data.AbstractDataSync;
import com.newtask.data.dao.TraderDateMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 **/
@Component
public class TraderAfterNumSync extends AbstractDataSync {

    @Resource
    private TraderDateMapper traderDateMapper;

    /**
     * 查询某一时间段需要更新的客户
     * @param startTime endTime
     * @return
     */
    @Override
    public List<Integer> loadBizId(Long startTime,Long endTime) {
        List<Integer> list=   traderDateMapper.getTraderId(startTime,endTime);
        return list;
    }

    /**
     * 查询客户 退货次数和售后总金额
     * @param bizIds
     * @return
     */
    @Override
    public List<Map<String, Object>> loadBizData(List<Integer> bizIds) {
        // 根据 traderId查 退货次数和售后总金额
        if(CollectionUtils.isNotEmpty(bizIds)){
            List<Map<String, Object>> list = traderDateMapper.findAfterSaleCountAndAmount(bizIds);
            return list;
        }
       return null;
    }

    @Override
    public void process(String startTime) {
        super.process(startTime);
    }

    /**
     * 更新T_TRADER_DATA表 退货次数和售后总金额
     */
    @Override
    public void updateData(List<Map<String, Object>> dataList) {
        if(CollectionUtils.isNotEmpty(dataList)){
            dataList.forEach(item->{
                Long nowDate = new Date().getTime();
                Integer traderId= NumberUtils.toInt(item.get("traderId")+"");
                //退货次数
                Integer afterSaleCount=NumberUtils.toInt(item.get("afterSaleCount")+"");
                //退款金额
                Double afterSaleTotalAmount = NumberUtils.toDouble(item.get("afterSaleTotalAmount")+"");
                //判断宽表中是否存在这条记录
                makeExist(traderId,nowDate);
                traderDateMapper.updateAfterSaleCountAndAmount(traderId,afterSaleCount,afterSaleTotalAmount,nowDate);
            });
        }

    }

    /**
     * 表中是否有该客户 没有直接插入
     * @param traderId
     */
    @Override
    public void makeExist(Integer traderId,Long nowDate) {
        Long count = traderDateMapper.makeExist(traderId);
      if(count<=0){
          //往宽表里添加数据
          traderDateMapper.insertTraderId(traderId,nowDate);
      }
    }
}
