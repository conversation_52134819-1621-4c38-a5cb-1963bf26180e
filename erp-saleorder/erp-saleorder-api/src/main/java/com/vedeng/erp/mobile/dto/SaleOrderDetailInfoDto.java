package com.vedeng.erp.mobile.dto;

import lombok.Data;

@Data
public class SaleOrderDetailInfoDto {

    /**
     * 销售订单id
     */
    private Integer saleOrderId;

    /**
     * 销售订单号
     */
    private String saleOrderNo;

    /**
     * 销售订单状态
     */
    private Integer orderStatus;

    /**
     * 客户名称
     */
    private String traderName;

    /**
     * 归属销售Id
     */
    private Integer currentUserId;

    /**
     * 归属销售名称
     */
    private String currentUserName;

    /**
     * 内部备注
     */
    private String comments;

    /**
     * 创建时间
     */
    private String addTime;

    /**
     * 合同详情地址
     */
    private String contractUrl;

    /**
     * 收货联系人
     */
    private String takeTraderContactName;

    /**
     * 收货联系人电话
     */
    private String takeTraderContactMobile;

    /**
     * 收货地区
     */
    private String takeTraderArea;

    /**
     * 收货地址
     */
    private String takeTraderAddress;

    /**
     * 发票抬头(发票客户名称)
     */
    private String invoiceTraderName;

    /**
     * 发票类型
     */
    private String invoiceType;

    /**
     * 收票联系人
     */
    private String invoiceTraderContactName;

    /**
     * 收票联系人电话
     */
    private String invoiceTraderContactMobile;

    /**
     * 收票联系人邮箱
     */
    private String invoiceTraderContactEmail;

    /**
     * 收票地区
     */
    private String invoiceTraderArea;

    /**
     * 收票地址
     */
    private String invoiceTraderAddress;

    /**
     * 开票方式:1手动纸质开票、2自动纸质开票、3自动电子发票
     */
    private Integer invoiceMethod;

    /**
     * 是否寄送发票 0否 1是
     */
    private Integer isSendInvoice;


}
