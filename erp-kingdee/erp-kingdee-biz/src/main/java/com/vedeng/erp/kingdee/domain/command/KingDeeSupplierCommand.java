package com.vedeng.erp.kingdee.domain.command;

import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶实际接受参数类
 * @date 2022/8/29 13:59
 */
@Getter
@Setter
public class KingDeeSupplierCommand {


    /**
     * 供应商内码
     */
    private String FSupplierId;
    /**
     * 创建组织
     */
    private KingDeeNumberCommand FCreateOrgId = new KingDeeNumberCommand();
    /**
     * 供应商编号
     */
    private String FNumber;
    /**
     * 使用组织
     */
    private KingDeeNumberCommand FUseOrgId = new KingDeeNumberCommand();
    /**
     * 供应商名称
     */
    private String FName;
    /**
     * 供应类别
     */
    private FBaseInfo FBaseInfo = new FBaseInfo();

    //2022-11-11新增 start
    /**
     * 供应商类别
     */
    private KingDeeNumberCommand FSupplierClassify = new KingDeeNumberCommand();


    /**
     * 供应商账户信息
     */
    private List<KingDeeSupplierBankCommand> FBankInfo = new ArrayList<>();

    //2022-11-11新增 end

    @Getter
    @Setter
    public static class FBaseInfo {
        /**
         * 供应类别
         */
        private String FSupplyClassify;
    }

    @Getter
    @Setter
    public static class KingDeeSupplierBankCommand{
        //帐号
        private String FBankCode;
        //帐户名称
        private String FBankHolder;
        //开户银行
        private String FOpenBankName;
        //联行号
        private String FCNAPS;
    }
}
