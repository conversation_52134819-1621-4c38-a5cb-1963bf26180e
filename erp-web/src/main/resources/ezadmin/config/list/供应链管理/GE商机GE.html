<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>GE商机-GE</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="uypKhP4HQqk" datasource="erp-reportdatasource" fixednumber="0" append_column_url="" append_row_url=""
      empty_show="" firstcol="numbers">
<div id="appendHead"></div>
<div class="layui-fluid">
    <div class="layui-tab">
        <ul class="layui-tab-title" id="tab">
        </ul>
    </div>
    <form class="layui-form" id="search">
        <div class=" layui-inline "><label class="layui-form-label">商机状态</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="21" name="BUSINESS_CHANCE_STATUS" placeholder=""
                        style="" alias="c" jdbctype="VARCHAR"
                        data="[{&quot;V&quot;:&quot;跟进中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;赢单&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;失单&quot;,&quot;K&quot;:&quot;3&quot;}]"
                        datatype="JSON" oper=""></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">报价单号</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="19" name="QUOTEORDER_NO" placeholder="" style=""
                        alias="a" jdbctype="VARCHAR" data="" datatype="" oper="LIKE"></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">是否维护GE商机</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="21" name="IS_DEFEND" placeholder="" style=""
                        alias="a" jdbctype=""
                        data="[{&quot;V&quot;:&quot;否&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;是&quot;,&quot;K&quot;:&quot;1&quot;}]"
                        datatype="JSON" oper=""></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">报单状态</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="21" name="STATUS" placeholder="" style="" alias="a"
                        jdbctype="VARCHAR"
                        data="[{&quot;V&quot;:&quot;待审核&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;可跟进&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;不可跟进&quot;,&quot;K&quot;:&quot;2&quot;}]"
                        datatype="JSON" oper="EQ"></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">意向型号</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="19" name="GOODS_NAME" placeholder="" style=""
                        alias="a" jdbctype="VARCHAR" data="" datatype="" oper="LIKE"></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">经销商名称</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="19" name="TRADER_NAME" placeholder="" style=""
                        alias="a" jdbctype="VARCHAR" data="" datatype="" oper="LIKE"></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">终端医院名称</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="19" name="TERMINAL_TRADER_NAME" placeholder=""
                        style="" alias="a" jdbctype="VARCHAR" data="" datatype="" oper="LIKE"></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">报单日期</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" name="ADD_TIME" placeholder="" style=""
                        alias="a" jdbctype="DATE" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>
    </form>
    <hr class="layui-border-blue">
    <div class="btn-group   bd-highlight" id="tableButton">
        <button item_name="导出搜索结果" name="导出搜索结果" url="/ezadmin/list/export-64optiYdPSQ" opentype="_BLANK_PARAM"
                windowname="导出搜索结果" style type="table">导出搜索结果
        </button>
    </div>
    <table id="table" class="layui-table" style=" width:100%">
        <thead>
        <tr id="column">
            <th type="rowbutton" id="rowbutton">
                <button item_name="a" name="a"
                        url="/businesschance/ge/geuserviewgebusinesschance.do?geBussinessChanceId=${GE_BUSSINESS_CHANCE_ID}"
                        opentype="PARENT" windowname="" style type="group">查看
                </button>
                <button item_name="b" name="b"
                        url="/businesschance/ge/geExamine.do?geBussinessChanceId=${GE_BUSSINESS_CHANCE_ID}"
                        opentype="PARENT" windowname="" style type="group">审核
                </button>
                <button item_name="c" name="c" url="" opentype="PARENT" windowname="" style type="group">已审核
                </button>
            </th>
            <th item_name="ADD_TIME" name="ADD_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname=""
                datatype="" data="" style="" head="18">报单日期
            </th>
            <th item_name="QUOTEORDER_NO" name="QUOTEORDER_NO" body="td-link" order="0"
                url="/businesschance/ge/geuserviewgebusinesschance.do?geBussinessChanceId=${GE_BUSSINESS_CHANCE_ID}"
                opentype="PARENT" windowname="" datatype="" data="" style="" head="18">报价单号
            </th>
            <th item_name="TERMINAL_TRADER_NAME" name="TERMINAL_TRADER_NAME" body="td-text" order="0" url="" opentype="MODEL"
                windowname="" datatype="" data="" style="" head="18">终端医院名称
            </th>
            <th item_name="HOSTPITAL_TYPE" name="HOSTPITAL_TYPE" body="td-text" order="0" url="" opentype="MODEL"
                windowname="" datatype="" data="" style="" head="18">医院性质
            </th>
            <th item_name="TITLE" name="TITLE" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype=""
                data="" style="" head="18">商机来源
            </th>
            <th item_name="TRADER_NAME" name="TRADER_NAME" body="td-text" order="0" url="" opentype="MODEL" windowname=""
                datatype="" data="" style="" head="18">经销商名称
            </th>
            <th item_name="GOODS_NAME" name="GOODS_NAME" body="td-text" order="0" url="" opentype="MODEL" windowname=""
                datatype="" data="" style="" head="18">意向型号
            </th>
            <th item_name="SALES_AREA" name="SALES_AREA" body="td-text" order="0" url="" opentype="MODEL" windowname=""
                datatype="" data="" style="" head="18">所属地区
            </th>
            <th item_name="ADDRESS" name="ADDRESS" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype=""
                data="" style="" head="18">具体地址
            </th>
            <th item_name="STATUS" name="STATUS" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype=""
                data="" style="" head="18">报单状态
            </th>
            <th item_name="IS_DEFEND" name="IS_DEFEND" body="td-text" order="0" url="" opentype="MODEL" windowname=""
                datatype="" data="" style="" head="18">是否维护GE商机
            </th>
            <th item_name="BUSINESS_CHANCE_STATUS" name="BUSINESS_CHANCE_STATUS" body="td-text" order="0" url=""
                opentype="MODEL" windowname="" datatype="" data="" style="" head="18">商机状态
            </th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td colspan="100"> express:
                <pre id="express" class="layui-code" orderby="order by a.ADD_TIME desc" groupby="">select
a.GE_BUSSINESS_CHANCE_ID,
DATE_FORMAT(a.ADD_TIME,'%Y-%m-%d') ADD_TIME,
a.QUOTEORDER_ID,
a.QUOTEORDER_NO,
a.TERMINAL_TRADER_NAME,
case when a.HOSPITAL_TYPE=1 then '公立'
when a.HOSPITAL_TYPE=2 then '非公' end HOSTPITAL_TYPE,
b.TITLE, -- 商机来源
a.TRADER_NAME, 
a.GOODS_NAME, -- 意向型号
a.SALES_AREA,
a.ADDRESS,
case when   a.STATUS=0  then '待审核'
when  a.STATUS=1 then '可跟进'
when a.STATUS=2 then '不可跟进' end STATUS,
case when a.IS_DEFEND =0 then '否'
when a.IS_DEFEND =1 then '是' end IS_DEFEND ,

case when c.BUSINESS_CHANCE_STATUS =1 then '跟进中'
when c.BUSINESS_CHANCE_STATUS =2 then '赢单'
when c.BUSINESS_CHANCE_STATUS =3 then '失单' end BUSINESS_CHANCE_STATUS  -- 商机状态 1跟进中 2赢单 3失单'

 from T_GE_BUSINESS_CHANCE a left join T_SYS_OPTION_DEFINITION b on a.GE_BUSINESS_CHANCE_SOURCE=b.SYS_OPTION_DEFINITION_ID
left join T_GE_BUSINESS_CHANCE_DETAIL c on a.GE_BUSSINESS_CHANCE_ID =c.GE_BUSSINESS_CHANCE_ID
and c.IS_DELETE=0

where 1=1 and a.IS_DELETE=0</pre>
                count:
                <pre id="count" class="layui-code"></pre>
            </td>
        </tr>
        </tbody>
    </table>
</div>
<div id="appendFoot">
    <script>
        $(".dropdown-menu").each(function () {
            var $tr = $(this).parents("tr:first");
            var bdStatus = $tr.find('td').eq(10);
            if ($.trim(bdStatus.html()) == "待审核") {
                $(this).find('.dropdown-item').eq(0).show();
                $(this).find('.dropdown-item').eq(1).show();
                // 已审核隐藏
                $(this).find('.dropdown-item').eq(2).hide();
            } else if ($.trim(bdStatus.html()) == "可跟进" || $.trim(bdStatus.html()) == "不可跟进") {

                //审核按钮隐藏 已审核 显示且按钮置灰
                $(this).find('.dropdown-item').eq(1).hide();
                $(this).find('.dropdown-item').eq(2).show();
                $(this).find('.dropdown-item').eq(2).addClass('disabled');
            }
        })


    </script>
</div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    })
</script>
</body>
</html>