<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.logistics.dao.WarehouseGoodsOutInItemMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.logistics.model.WarehouseGoodsOutInItem">
    <!--@mbg.generated-->
    <!--@Table T_WAREHOUSE_GOODS_OUT_IN_ITEM-->
    <id column="WAREHOUSE_GOODS_OUT_IN_DETAIL_ID" jdbcType="BIGINT" property="warehouseGoodsOutInDetailId" />
    <result column="OUT_IN_NO" jdbcType="VARCHAR" property="outInNo" />
    <result column="WMS_NO" jdbcType="VARCHAR" property="wmsNo" />
    <result column="BARCODE_ID" jdbcType="INTEGER" property="barcodeId" />
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
    <result column="LOG_TYPE" jdbcType="BOOLEAN" property="logType" />
    <result column="OPERATE_TYPE" jdbcType="TINYINT" property="operateType" />
    <result column="RELATED_ID" jdbcType="INTEGER" property="relatedId" />
    <result column="WAREHOUSE_PICKING_DETAIL_ID" jdbcType="INTEGER" property="warehousePickingDetailId" />
    <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId" />
    <result column="BARCODE_FACTORY" jdbcType="VARCHAR" property="barcodeFactory" />
    <result column="NUM" jdbcType="INTEGER" property="num" />
    <result column="WAREHOUSE_ID" jdbcType="INTEGER" property="warehouseId" />
    <result column="STORAGE_ROOM_ID" jdbcType="INTEGER" property="storageRoomId" />
    <result column="STORAGE_AREA_ID" jdbcType="INTEGER" property="storageAreaId" />
    <result column="STORAGE_LOCATION_ID" jdbcType="INTEGER" property="storageLocationId" />
    <result column="STORAGE_RACK_ID" jdbcType="INTEGER" property="storageRackId" />
    <result column="BATCH_NUMBER" jdbcType="VARCHAR" property="batchNumber" />
    <result column="EXPIRATION_DATE" jdbcType="VARCHAR" property="expirationDate" />
    <result column="CHECK_STATUS" jdbcType="BOOLEAN" property="checkStatus" />
    <result column="CHECK_STATUS_USER" jdbcType="INTEGER" property="checkStatusUser" />
    <result column="CHECK_STATUS_TIME" jdbcType="VARCHAR" property="checkStatusTime" />
    <result column="RECHECK_STATUS" jdbcType="BOOLEAN" property="recheckStatus" />
    <result column="RECHECK_STATUS_USER" jdbcType="INTEGER" property="recheckStatusUser" />
    <result column="RECHECK_STATUS_TIME" jdbcType="VARCHAR" property="recheckStatusTime" />
    <result column="IS_EXPRESS" jdbcType="BOOLEAN" property="isExpress" />
    <result column="IS_PROBLEM" jdbcType="BOOLEAN" property="isProblem" />
    <result column="PROBLEM_REMARK" jdbcType="VARCHAR" property="problemRemark" />
    <result column="PRODUCT_DATE" jdbcType="VARCHAR" property="productDate" />
    <result column="COST_PRICE" jdbcType="DECIMAL" property="costPrice" />
    <result column="IS_USE" jdbcType="BOOLEAN" property="isUse" />
    <result column="LOGICAL_WAREHOUSE_ID" jdbcType="INTEGER" property="logicalWarehouseId" />
    <result column="VEDENG_BATCH_NUMBER" jdbcType="VARCHAR" property="vedengBatchNumber" />
    <result column="LAST_STOCK_NUM" jdbcType="INTEGER" property="lastStockNum" />
    <result column="STERILIZATION_BATCH_NUMBER" jdbcType="VARCHAR" property="sterilizationBatchNumber" />
    <result column="NEW_COST_PRICE" jdbcType="DECIMAL" property="newCostPrice" />
    <result column="DEDICATED_BUYORDER_NO" jdbcType="VARCHAR" property="dedicatedBuyorderNo" />
    <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    WAREHOUSE_GOODS_OUT_IN_DETAIL_ID, OUT_IN_NO, WMS_NO, BARCODE_ID, COMPANY_ID, LOG_TYPE, 
    OPERATE_TYPE, RELATED_ID, WAREHOUSE_PICKING_DETAIL_ID, GOODS_ID, BARCODE_FACTORY, 
    NUM, WAREHOUSE_ID, STORAGE_ROOM_ID, STORAGE_AREA_ID, STORAGE_LOCATION_ID, STORAGE_RACK_ID, 
    BATCH_NUMBER, EXPIRATION_DATE, CHECK_STATUS, CHECK_STATUS_USER, CHECK_STATUS_TIME, 
    RECHECK_STATUS, RECHECK_STATUS_USER, RECHECK_STATUS_TIME, IS_EXPRESS, IS_PROBLEM, 
    PROBLEM_REMARK, PRODUCT_DATE, COST_PRICE, IS_USE, LOGICAL_WAREHOUSE_ID, VEDENG_BATCH_NUMBER, 
    LAST_STOCK_NUM, STERILIZATION_BATCH_NUMBER, NEW_COST_PRICE, DEDICATED_BUYORDER_NO, 
    IS_DELETE, ADD_TIME, CREATOR, CREATOR_NAME, MOD_TIME, UPDATER, UPDATER_NAME, REMARK, 
    UPDATE_REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_WAREHOUSE_GOODS_OUT_IN_ITEM
    where WAREHOUSE_GOODS_OUT_IN_DETAIL_ID = #{warehouseGoodsOutInDetailId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_WAREHOUSE_GOODS_OUT_IN_ITEM
    where WAREHOUSE_GOODS_OUT_IN_DETAIL_ID = #{warehouseGoodsOutInDetailId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="WAREHOUSE_GOODS_OUT_IN_DETAIL_ID" keyProperty="warehouseGoodsOutInDetailId" parameterType="com.vedeng.logistics.model.WarehouseGoodsOutInItem" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_WAREHOUSE_GOODS_OUT_IN_ITEM (OUT_IN_NO, WMS_NO, BARCODE_ID, 
      COMPANY_ID, LOG_TYPE, OPERATE_TYPE, 
      RELATED_ID, WAREHOUSE_PICKING_DETAIL_ID, GOODS_ID, 
      BARCODE_FACTORY, NUM, WAREHOUSE_ID, 
      STORAGE_ROOM_ID, STORAGE_AREA_ID, STORAGE_LOCATION_ID, 
      STORAGE_RACK_ID, BATCH_NUMBER, EXPIRATION_DATE, 
      CHECK_STATUS, CHECK_STATUS_USER, CHECK_STATUS_TIME, 
      RECHECK_STATUS, RECHECK_STATUS_USER, RECHECK_STATUS_TIME, 
      IS_EXPRESS, IS_PROBLEM, PROBLEM_REMARK, 
      PRODUCT_DATE, COST_PRICE, IS_USE, 
      LOGICAL_WAREHOUSE_ID, VEDENG_BATCH_NUMBER, 
      LAST_STOCK_NUM, STERILIZATION_BATCH_NUMBER, 
      NEW_COST_PRICE, DEDICATED_BUYORDER_NO, IS_DELETE, 
      ADD_TIME, CREATOR, CREATOR_NAME, 
      MOD_TIME, UPDATER, UPDATER_NAME, 
      REMARK, UPDATE_REMARK)
    values (#{outInNo,jdbcType=VARCHAR}, #{wmsNo,jdbcType=VARCHAR}, #{barcodeId,jdbcType=INTEGER}, 
      #{companyId,jdbcType=INTEGER}, #{logType,jdbcType=BOOLEAN}, #{operateType,jdbcType=TINYINT}, 
      #{relatedId,jdbcType=INTEGER}, #{warehousePickingDetailId,jdbcType=INTEGER}, #{goodsId,jdbcType=INTEGER}, 
      #{barcodeFactory,jdbcType=VARCHAR}, #{num,jdbcType=INTEGER}, #{warehouseId,jdbcType=INTEGER}, 
      #{storageRoomId,jdbcType=INTEGER}, #{storageAreaId,jdbcType=INTEGER}, #{storageLocationId,jdbcType=INTEGER}, 
      #{storageRackId,jdbcType=INTEGER}, #{batchNumber,jdbcType=VARCHAR}, #{expirationDate,jdbcType=VARCHAR}, 
      #{checkStatus,jdbcType=BOOLEAN}, #{checkStatusUser,jdbcType=INTEGER}, #{checkStatusTime,jdbcType=VARCHAR}, 
      #{recheckStatus,jdbcType=BOOLEAN}, #{recheckStatusUser,jdbcType=INTEGER}, #{recheckStatusTime,jdbcType=VARCHAR}, 
      #{isExpress,jdbcType=BOOLEAN}, #{isProblem,jdbcType=BOOLEAN}, #{problemRemark,jdbcType=VARCHAR}, 
      #{productDate,jdbcType=VARCHAR}, #{costPrice,jdbcType=DECIMAL}, #{isUse,jdbcType=BOOLEAN}, 
      #{logicalWarehouseId,jdbcType=INTEGER}, #{vedengBatchNumber,jdbcType=VARCHAR}, 
      #{lastStockNum,jdbcType=INTEGER}, #{sterilizationBatchNumber,jdbcType=VARCHAR}, 
      #{newCostPrice,jdbcType=DECIMAL}, #{dedicatedBuyorderNo,jdbcType=VARCHAR}, #{isDelete,jdbcType=BOOLEAN}, 
      #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, 
      #{modTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{updateRemark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="WAREHOUSE_GOODS_OUT_IN_DETAIL_ID" keyProperty="warehouseGoodsOutInDetailId" parameterType="com.vedeng.logistics.model.WarehouseGoodsOutInItem" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_WAREHOUSE_GOODS_OUT_IN_ITEM
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="outInNo != null">
        OUT_IN_NO,
      </if>
      <if test="wmsNo != null">
        WMS_NO,
      </if>
      <if test="barcodeId != null">
        BARCODE_ID,
      </if>
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="logType != null">
        LOG_TYPE,
      </if>
      <if test="operateType != null">
        OPERATE_TYPE,
      </if>
      <if test="relatedId != null">
        RELATED_ID,
      </if>
      <if test="warehousePickingDetailId != null">
        WAREHOUSE_PICKING_DETAIL_ID,
      </if>
      <if test="goodsId != null">
        GOODS_ID,
      </if>
      <if test="barcodeFactory != null">
        BARCODE_FACTORY,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="warehouseId != null">
        WAREHOUSE_ID,
      </if>
      <if test="storageRoomId != null">
        STORAGE_ROOM_ID,
      </if>
      <if test="storageAreaId != null">
        STORAGE_AREA_ID,
      </if>
      <if test="storageLocationId != null">
        STORAGE_LOCATION_ID,
      </if>
      <if test="storageRackId != null">
        STORAGE_RACK_ID,
      </if>
      <if test="batchNumber != null">
        BATCH_NUMBER,
      </if>
      <if test="expirationDate != null">
        EXPIRATION_DATE,
      </if>
      <if test="checkStatus != null">
        CHECK_STATUS,
      </if>
      <if test="checkStatusUser != null">
        CHECK_STATUS_USER,
      </if>
      <if test="checkStatusTime != null">
        CHECK_STATUS_TIME,
      </if>
      <if test="recheckStatus != null">
        RECHECK_STATUS,
      </if>
      <if test="recheckStatusUser != null">
        RECHECK_STATUS_USER,
      </if>
      <if test="recheckStatusTime != null">
        RECHECK_STATUS_TIME,
      </if>
      <if test="isExpress != null">
        IS_EXPRESS,
      </if>
      <if test="isProblem != null">
        IS_PROBLEM,
      </if>
      <if test="problemRemark != null">
        PROBLEM_REMARK,
      </if>
      <if test="productDate != null">
        PRODUCT_DATE,
      </if>
      <if test="costPrice != null">
        COST_PRICE,
      </if>
      <if test="isUse != null">
        IS_USE,
      </if>
      <if test="logicalWarehouseId != null">
        LOGICAL_WAREHOUSE_ID,
      </if>
      <if test="vedengBatchNumber != null">
        VEDENG_BATCH_NUMBER,
      </if>
      <if test="lastStockNum != null">
        LAST_STOCK_NUM,
      </if>
      <if test="sterilizationBatchNumber != null">
        STERILIZATION_BATCH_NUMBER,
      </if>
      <if test="newCostPrice != null">
        NEW_COST_PRICE,
      </if>
      <if test="dedicatedBuyorderNo != null">
        DEDICATED_BUYORDER_NO,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="outInNo != null">
        #{outInNo,jdbcType=VARCHAR},
      </if>
      <if test="wmsNo != null">
        #{wmsNo,jdbcType=VARCHAR},
      </if>
      <if test="barcodeId != null">
        #{barcodeId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="logType != null">
        #{logType,jdbcType=BOOLEAN},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=TINYINT},
      </if>
      <if test="relatedId != null">
        #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="warehousePickingDetailId != null">
        #{warehousePickingDetailId,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null">
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="barcodeFactory != null">
        #{barcodeFactory,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="warehouseId != null">
        #{warehouseId,jdbcType=INTEGER},
      </if>
      <if test="storageRoomId != null">
        #{storageRoomId,jdbcType=INTEGER},
      </if>
      <if test="storageAreaId != null">
        #{storageAreaId,jdbcType=INTEGER},
      </if>
      <if test="storageLocationId != null">
        #{storageLocationId,jdbcType=INTEGER},
      </if>
      <if test="storageRackId != null">
        #{storageRackId,jdbcType=INTEGER},
      </if>
      <if test="batchNumber != null">
        #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="expirationDate != null">
        #{expirationDate,jdbcType=VARCHAR},
      </if>
      <if test="checkStatus != null">
        #{checkStatus,jdbcType=BOOLEAN},
      </if>
      <if test="checkStatusUser != null">
        #{checkStatusUser,jdbcType=INTEGER},
      </if>
      <if test="checkStatusTime != null">
        #{checkStatusTime,jdbcType=VARCHAR},
      </if>
      <if test="recheckStatus != null">
        #{recheckStatus,jdbcType=BOOLEAN},
      </if>
      <if test="recheckStatusUser != null">
        #{recheckStatusUser,jdbcType=INTEGER},
      </if>
      <if test="recheckStatusTime != null">
        #{recheckStatusTime,jdbcType=VARCHAR},
      </if>
      <if test="isExpress != null">
        #{isExpress,jdbcType=BOOLEAN},
      </if>
      <if test="isProblem != null">
        #{isProblem,jdbcType=BOOLEAN},
      </if>
      <if test="problemRemark != null">
        #{problemRemark,jdbcType=VARCHAR},
      </if>
      <if test="productDate != null">
        #{productDate,jdbcType=VARCHAR},
      </if>
      <if test="costPrice != null">
        #{costPrice,jdbcType=DECIMAL},
      </if>
      <if test="isUse != null">
        #{isUse,jdbcType=BOOLEAN},
      </if>
      <if test="logicalWarehouseId != null">
        #{logicalWarehouseId,jdbcType=INTEGER},
      </if>
      <if test="vedengBatchNumber != null">
        #{vedengBatchNumber,jdbcType=VARCHAR},
      </if>
      <if test="lastStockNum != null">
        #{lastStockNum,jdbcType=INTEGER},
      </if>
      <if test="sterilizationBatchNumber != null">
        #{sterilizationBatchNumber,jdbcType=VARCHAR},
      </if>
      <if test="newCostPrice != null">
        #{newCostPrice,jdbcType=DECIMAL},
      </if>
      <if test="dedicatedBuyorderNo != null">
        #{dedicatedBuyorderNo,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.logistics.model.WarehouseGoodsOutInItem">
    <!--@mbg.generated-->
    update T_WAREHOUSE_GOODS_OUT_IN_ITEM
    <set>
      <if test="outInNo != null">
        OUT_IN_NO = #{outInNo,jdbcType=VARCHAR},
      </if>
      <if test="wmsNo != null">
        WMS_NO = #{wmsNo,jdbcType=VARCHAR},
      </if>
      <if test="barcodeId != null">
        BARCODE_ID = #{barcodeId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="logType != null">
        LOG_TYPE = #{logType,jdbcType=BOOLEAN},
      </if>
      <if test="operateType != null">
        OPERATE_TYPE = #{operateType,jdbcType=TINYINT},
      </if>
      <if test="relatedId != null">
        RELATED_ID = #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="warehousePickingDetailId != null">
        WAREHOUSE_PICKING_DETAIL_ID = #{warehousePickingDetailId,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null">
        GOODS_ID = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="barcodeFactory != null">
        BARCODE_FACTORY = #{barcodeFactory,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="warehouseId != null">
        WAREHOUSE_ID = #{warehouseId,jdbcType=INTEGER},
      </if>
      <if test="storageRoomId != null">
        STORAGE_ROOM_ID = #{storageRoomId,jdbcType=INTEGER},
      </if>
      <if test="storageAreaId != null">
        STORAGE_AREA_ID = #{storageAreaId,jdbcType=INTEGER},
      </if>
      <if test="storageLocationId != null">
        STORAGE_LOCATION_ID = #{storageLocationId,jdbcType=INTEGER},
      </if>
      <if test="storageRackId != null">
        STORAGE_RACK_ID = #{storageRackId,jdbcType=INTEGER},
      </if>
      <if test="batchNumber != null">
        BATCH_NUMBER = #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="expirationDate != null">
        EXPIRATION_DATE = #{expirationDate,jdbcType=VARCHAR},
      </if>
      <if test="checkStatus != null">
        CHECK_STATUS = #{checkStatus,jdbcType=BOOLEAN},
      </if>
      <if test="checkStatusUser != null">
        CHECK_STATUS_USER = #{checkStatusUser,jdbcType=INTEGER},
      </if>
      <if test="checkStatusTime != null">
        CHECK_STATUS_TIME = #{checkStatusTime,jdbcType=VARCHAR},
      </if>
      <if test="recheckStatus != null">
        RECHECK_STATUS = #{recheckStatus,jdbcType=BOOLEAN},
      </if>
      <if test="recheckStatusUser != null">
        RECHECK_STATUS_USER = #{recheckStatusUser,jdbcType=INTEGER},
      </if>
      <if test="recheckStatusTime != null">
        RECHECK_STATUS_TIME = #{recheckStatusTime,jdbcType=VARCHAR},
      </if>
      <if test="isExpress != null">
        IS_EXPRESS = #{isExpress,jdbcType=BOOLEAN},
      </if>
      <if test="isProblem != null">
        IS_PROBLEM = #{isProblem,jdbcType=BOOLEAN},
      </if>
      <if test="problemRemark != null">
        PROBLEM_REMARK = #{problemRemark,jdbcType=VARCHAR},
      </if>
      <if test="productDate != null">
        PRODUCT_DATE = #{productDate,jdbcType=VARCHAR},
      </if>
      <if test="costPrice != null">
        COST_PRICE = #{costPrice,jdbcType=DECIMAL},
      </if>
      <if test="isUse != null">
        IS_USE = #{isUse,jdbcType=BOOLEAN},
      </if>
      <if test="logicalWarehouseId != null">
        LOGICAL_WAREHOUSE_ID = #{logicalWarehouseId,jdbcType=INTEGER},
      </if>
      <if test="vedengBatchNumber != null">
        VEDENG_BATCH_NUMBER = #{vedengBatchNumber,jdbcType=VARCHAR},
      </if>
      <if test="lastStockNum != null">
        LAST_STOCK_NUM = #{lastStockNum,jdbcType=INTEGER},
      </if>
      <if test="sterilizationBatchNumber != null">
        STERILIZATION_BATCH_NUMBER = #{sterilizationBatchNumber,jdbcType=VARCHAR},
      </if>
      <if test="newCostPrice != null">
        NEW_COST_PRICE = #{newCostPrice,jdbcType=DECIMAL},
      </if>
      <if test="dedicatedBuyorderNo != null">
        DEDICATED_BUYORDER_NO = #{dedicatedBuyorderNo,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where WAREHOUSE_GOODS_OUT_IN_DETAIL_ID = #{warehouseGoodsOutInDetailId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.logistics.model.WarehouseGoodsOutInItem">
    <!--@mbg.generated-->
    update T_WAREHOUSE_GOODS_OUT_IN_ITEM
    set OUT_IN_NO = #{outInNo,jdbcType=VARCHAR},
      WMS_NO = #{wmsNo,jdbcType=VARCHAR},
      BARCODE_ID = #{barcodeId,jdbcType=INTEGER},
      COMPANY_ID = #{companyId,jdbcType=INTEGER},
      LOG_TYPE = #{logType,jdbcType=BOOLEAN},
      OPERATE_TYPE = #{operateType,jdbcType=TINYINT},
      RELATED_ID = #{relatedId,jdbcType=INTEGER},
      WAREHOUSE_PICKING_DETAIL_ID = #{warehousePickingDetailId,jdbcType=INTEGER},
      GOODS_ID = #{goodsId,jdbcType=INTEGER},
      BARCODE_FACTORY = #{barcodeFactory,jdbcType=VARCHAR},
      NUM = #{num,jdbcType=INTEGER},
      WAREHOUSE_ID = #{warehouseId,jdbcType=INTEGER},
      STORAGE_ROOM_ID = #{storageRoomId,jdbcType=INTEGER},
      STORAGE_AREA_ID = #{storageAreaId,jdbcType=INTEGER},
      STORAGE_LOCATION_ID = #{storageLocationId,jdbcType=INTEGER},
      STORAGE_RACK_ID = #{storageRackId,jdbcType=INTEGER},
      BATCH_NUMBER = #{batchNumber,jdbcType=VARCHAR},
      EXPIRATION_DATE = #{expirationDate,jdbcType=VARCHAR},
      CHECK_STATUS = #{checkStatus,jdbcType=BOOLEAN},
      CHECK_STATUS_USER = #{checkStatusUser,jdbcType=INTEGER},
      CHECK_STATUS_TIME = #{checkStatusTime,jdbcType=VARCHAR},
      RECHECK_STATUS = #{recheckStatus,jdbcType=BOOLEAN},
      RECHECK_STATUS_USER = #{recheckStatusUser,jdbcType=INTEGER},
      RECHECK_STATUS_TIME = #{recheckStatusTime,jdbcType=VARCHAR},
      IS_EXPRESS = #{isExpress,jdbcType=BOOLEAN},
      IS_PROBLEM = #{isProblem,jdbcType=BOOLEAN},
      PROBLEM_REMARK = #{problemRemark,jdbcType=VARCHAR},
      PRODUCT_DATE = #{productDate,jdbcType=VARCHAR},
      COST_PRICE = #{costPrice,jdbcType=DECIMAL},
      IS_USE = #{isUse,jdbcType=BOOLEAN},
      LOGICAL_WAREHOUSE_ID = #{logicalWarehouseId,jdbcType=INTEGER},
      VEDENG_BATCH_NUMBER = #{vedengBatchNumber,jdbcType=VARCHAR},
      LAST_STOCK_NUM = #{lastStockNum,jdbcType=INTEGER},
      STERILIZATION_BATCH_NUMBER = #{sterilizationBatchNumber,jdbcType=VARCHAR},
      NEW_COST_PRICE = #{newCostPrice,jdbcType=DECIMAL},
      DEDICATED_BUYORDER_NO = #{dedicatedBuyorderNo,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where WAREHOUSE_GOODS_OUT_IN_DETAIL_ID = #{warehouseGoodsOutInDetailId,jdbcType=BIGINT}
  </update>

<!--auto generated by MybatisCodeHelper on 2022-11-25-->
  <select id="findByAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_WAREHOUSE_GOODS_OUT_IN_ITEM
        <where>
            <if test="warehouseGoodsOutInDetailId != null">
                and WAREHOUSE_GOODS_OUT_IN_DETAIL_ID=#{warehouseGoodsOutInDetailId,jdbcType=BIGINT}
            </if>
            <if test="outInNo != null">
                and OUT_IN_NO=#{outInNo,jdbcType=VARCHAR}
            </if>
            <if test="wmsNo != null">
                and WMS_NO=#{wmsNo,jdbcType=VARCHAR}
            </if>
            <if test="barcodeId != null">
                and BARCODE_ID=#{barcodeId,jdbcType=INTEGER}
            </if>
            <if test="companyId != null">
                and COMPANY_ID=#{companyId,jdbcType=INTEGER}
            </if>
            <if test="logType != null">
                and LOG_TYPE=#{logType,jdbcType=BOOLEAN}
            </if>
            <if test="operateType != null">
                and OPERATE_TYPE=#{operateType,jdbcType=TINYINT}
            </if>
            <if test="relatedId != null">
                and RELATED_ID=#{relatedId,jdbcType=INTEGER}
            </if>
            <if test="warehousePickingDetailId != null">
                and WAREHOUSE_PICKING_DETAIL_ID=#{warehousePickingDetailId,jdbcType=INTEGER}
            </if>
            <if test="goodsId != null">
                and GOODS_ID=#{goodsId,jdbcType=INTEGER}
            </if>
            <if test="barcodeFactory != null">
                and BARCODE_FACTORY=#{barcodeFactory,jdbcType=VARCHAR}
            </if>
            <if test="num != null">
                and NUM=#{num,jdbcType=INTEGER}
            </if>
            <if test="warehouseId != null">
                and WAREHOUSE_ID=#{warehouseId,jdbcType=INTEGER}
            </if>
            <if test="storageRoomId != null">
                and STORAGE_ROOM_ID=#{storageRoomId,jdbcType=INTEGER}
            </if>
            <if test="storageAreaId != null">
                and STORAGE_AREA_ID=#{storageAreaId,jdbcType=INTEGER}
            </if>
            <if test="storageLocationId != null">
                and STORAGE_LOCATION_ID=#{storageLocationId,jdbcType=INTEGER}
            </if>
            <if test="storageRackId != null">
                and STORAGE_RACK_ID=#{storageRackId,jdbcType=INTEGER}
            </if>
            <if test="batchNumber != null">
                and BATCH_NUMBER=#{batchNumber,jdbcType=VARCHAR}
            </if>
            <if test="expirationDate != null">
                and EXPIRATION_DATE=#{expirationDate,jdbcType=VARCHAR}
            </if>
            <if test="checkStatus != null">
                and CHECK_STATUS=#{checkStatus,jdbcType=BOOLEAN}
            </if>
            <if test="checkStatusUser != null">
                and CHECK_STATUS_USER=#{checkStatusUser,jdbcType=INTEGER}
            </if>
            <if test="checkStatusTime != null">
                and CHECK_STATUS_TIME=#{checkStatusTime,jdbcType=VARCHAR}
            </if>
            <if test="recheckStatus != null">
                and RECHECK_STATUS=#{recheckStatus,jdbcType=BOOLEAN}
            </if>
            <if test="recheckStatusUser != null">
                and RECHECK_STATUS_USER=#{recheckStatusUser,jdbcType=INTEGER}
            </if>
            <if test="recheckStatusTime != null">
                and RECHECK_STATUS_TIME=#{recheckStatusTime,jdbcType=VARCHAR}
            </if>
            <if test="isExpress != null">
                and IS_EXPRESS=#{isExpress,jdbcType=BOOLEAN}
            </if>
            <if test="isProblem != null">
                and IS_PROBLEM=#{isProblem,jdbcType=BOOLEAN}
            </if>
            <if test="problemRemark != null">
                and PROBLEM_REMARK=#{problemRemark,jdbcType=VARCHAR}
            </if>
            <if test="productDate != null">
                and PRODUCT_DATE=#{productDate,jdbcType=VARCHAR}
            </if>
            <if test="costPrice != null">
                and COST_PRICE=#{costPrice,jdbcType=DECIMAL}
            </if>
            <if test="isUse != null">
                and IS_USE=#{isUse,jdbcType=BOOLEAN}
            </if>
            <if test="logicalWarehouseId != null">
                and LOGICAL_WAREHOUSE_ID=#{logicalWarehouseId,jdbcType=INTEGER}
            </if>
            <if test="vedengBatchNumber != null">
                and VEDENG_BATCH_NUMBER=#{vedengBatchNumber,jdbcType=VARCHAR}
            </if>
            <if test="lastStockNum != null">
                and LAST_STOCK_NUM=#{lastStockNum,jdbcType=INTEGER}
            </if>
            <if test="sterilizationBatchNumber != null">
                and STERILIZATION_BATCH_NUMBER=#{sterilizationBatchNumber,jdbcType=VARCHAR}
            </if>
            <if test="newCostPrice != null">
                and NEW_COST_PRICE=#{newCostPrice,jdbcType=DECIMAL}
            </if>
            <if test="dedicatedBuyorderNo != null">
                and DEDICATED_BUYORDER_NO=#{dedicatedBuyorderNo,jdbcType=VARCHAR}
            </if>
            <if test="isDelete != null">
                and IS_DELETE=#{isDelete,jdbcType=BOOLEAN}
            </if>
            <if test="remark != null">
                and REMARK=#{remark,jdbcType=VARCHAR}
            </if>
            <if test="updateRemark != null">
                and UPDATE_REMARK=#{updateRemark,jdbcType=VARCHAR}
            </if>
            <if test="addTime != null">
                and ADD_TIME=#{addTime,jdbcType=TIMESTAMP}
            </if>
            <if test="modTime != null">
                and MOD_TIME=#{modTime,jdbcType=TIMESTAMP}
            </if>
            <if test="creator != null">
                and CREATOR=#{creator,jdbcType=INTEGER}
            </if>
            <if test="updater != null">
                and UPDATER=#{updater,jdbcType=INTEGER}
            </if>
            <if test="creatorName != null">
                and CREATOR_NAME=#{creatorName,jdbcType=VARCHAR}
            </if>
            <if test="updaterName != null">
                and UPDATER_NAME=#{updaterName,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    
   	<select id="getAvailableLogicalGoods" resultType="com.vedeng.logistics.model.WarehouseGoodsOutInItem">
		SELECT
			*
		FROM
			T_WAREHOUSE_GOODS_OUT_IN_ITEM
			<where>
				1 = 1
				<if test="logicalWarehouseId != null">
					AND LOGICAL_WAREHOUSE_ID = #{logicalWarehouseId,jdbcType=INTEGER}
				</if>
				<if test="relatedId != null">
					AND RELATED_ID = #{relatedId,jdbcType=INTEGER}
				</if>
				AND VEDENG_BATCH_NUMBER = #{vedengBatchNumber,jdbcType=VARCHAR}
				AND GOODS_ID = #{goodsId,jdbcType=INTEGER}
				AND LOG_TYPE = 0
				AND IS_USE = 0
				AND LAST_STOCK_NUM>0
			</where>
	</select>
	
	<select id="getRetrunOutInItem" resultType="com.vedeng.logistics.model.WarehouseGoodsOutInItem">
		select
			  *
			from
			  T_WAREHOUSE_GOODS_OUT_IN_ITEM
			where
			  OUT_IN_NO in (
			    select
			      OUT_IN_NO
			    from
			      T_WAREHOUSE_GOODS_OUT_IN
			    where
			      OUT_IN_TYPE = 8
				  and RELATE_NO = #{afterSaleOrderNo,jdbcType=VARCHAR}
			  )
	</select>


  <select id="getOutInItemByRelateNo" resultType="com.vedeng.logistics.model.WarehouseGoodsOutInItemSn">
    select a.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID
    , b.OUT_IN_NO
    , b.RELATE_NO
    , b.OUT_IN_TYPE
    , CONCAT('V', a.GOODS_ID) as SKU
    , a.BARCODE_FACTORY
    from T_WAREHOUSE_GOODS_OUT_IN_ITEM a
    left join T_WAREHOUSE_GOODS_OUT_IN b on a.OUT_IN_NO = b.OUT_IN_NO
    where a.IS_DELETE = 0
    and b.IS_DELETE = 0
    and b.RELATE_NO = #{relateNo,jdbcType=VARCHAR}
    and b.OUT_IN_TYPE =  #{outInType,jdbcType=INTEGER}
  </select>

  <select id="getGoodsOperateNumByGoodsIds" resultType="com.vedeng.logistics.model.dto.GoodsOperateNumDto">
    SELECT RELATED_ID,
          SUM(
            ABS(
            IFNULL( NUM, 0 ))) TOTAL_NUM
    FROM T_WAREHOUSE_GOODS_OUT_IN_ITEM
    WHERE IS_DELETE = 0
      AND LOG_TYPE = 1
      AND OPERATE_TYPE = 6
      AND RELATED_ID IN
    <foreach collection="goodsIds" index="index" item="goodsId" open="(" separator="," close=")">
      #{goodsId,jdbcType=INTEGER}
    </foreach>
    GROUP BY RELATED_ID
    HAVING TOTAL_NUM > 0
  </select>

<!--auto generated by MybatisCodeHelper on 2023-04-03-->
  <update id="logicalDeleteByOutInNo">
        update  T_WAREHOUSE_GOODS_OUT_IN_ITEM set IS_DELETE = 1
        where OUT_IN_NO=#{outInNo,jdbcType=VARCHAR}
    </update>

<!--auto generated by MybatisCodeHelper on 2023-04-25-->
  <select id="findByBarcodeFactoryAndOperateTypeIn" resultType="java.lang.Boolean">
    SELECT EXISTS(select 1
                  from T_WAREHOUSE_GOODS_OUT_IN_ITEM
    where BARCODE_FACTORY = #{barcodeFactory,jdbcType=VARCHAR}
      and OPERATE_TYPE in
    <foreach item="item" index="index" collection="operateTypeCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=TINYINT}
    </foreach>
    );

  </select>

<!--auto generated by MybatisCodeHelper on 2023-05-08-->
  <select id="findByBarcodeFactory" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_WAREHOUSE_GOODS_OUT_IN_ITEM
        where BARCODE_FACTORY=#{barcodeFactory,jdbcType=VARCHAR}
        limit 1
    </select>

<!--auto generated by MybatisCodeHelper on 2023-05-10-->
  <select id="findByRelatedId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_WAREHOUSE_GOODS_OUT_IN_ITEM
    where RELATED_ID=#{relatedId,jdbcType=INTEGER}
    order by WAREHOUSE_GOODS_OUT_IN_DETAIL_ID ASC
  </select>
</mapper>