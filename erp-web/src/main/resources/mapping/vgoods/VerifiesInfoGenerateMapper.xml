<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.dao.VerifiesInfoGenerateMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.goods.model.VerifiesInfoGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <id column="VERIFIES_INFO_ID" jdbcType="INTEGER" property="verifiesInfoId" />
    <result column="RELATE_TABLE" jdbcType="VARCHAR" property="relateTable" />
    <result column="RELATE_TABLE_KEY" jdbcType="INTEGER" property="relateTableKey" />
    <result column="VERIFIES_TYPE" jdbcType="INTEGER" property="verifiesType" />
    <result column="LAST_VERIFY_USERNAME" jdbcType="VARCHAR" property="lastVerifyUsername" />
    <result column="VERIFY_USERNAME" jdbcType="VARCHAR" property="verifyUsername" />
    <result column="STATUS" jdbcType="TINYINT" property="status" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    VERIFIES_INFO_ID, RELATE_TABLE, RELATE_TABLE_KEY, VERIFIES_TYPE, LAST_VERIFY_USERNAME, 
    VERIFY_USERNAME, `STATUS`, ADD_TIME, MOD_TIME
  </sql>
  <select id="selectByExample" parameterType="com.vedeng.goods.model.VerifiesInfoGenerateExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from T_VERIFIES_INFO
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    from T_VERIFIES_INFO
    where VERIFIES_INFO_ID = #{verifiesInfoId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    delete from T_VERIFIES_INFO
    where VERIFIES_INFO_ID = #{verifiesInfoId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.goods.model.VerifiesInfoGenerateExample">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    delete from T_VERIFIES_INFO
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.goods.model.VerifiesInfoGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <selectKey keyProperty="verifiesInfoId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_VERIFIES_INFO (RELATE_TABLE, RELATE_TABLE_KEY, VERIFIES_TYPE, 
      LAST_VERIFY_USERNAME, VERIFY_USERNAME, `STATUS`, 
      ADD_TIME, MOD_TIME)
    values (#{relateTable,jdbcType=VARCHAR}, #{relateTableKey,jdbcType=INTEGER}, #{verifiesType,jdbcType=INTEGER}, 
      #{lastVerifyUsername,jdbcType=VARCHAR}, #{verifyUsername,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{addTime,jdbcType=BIGINT}, #{modTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.goods.model.VerifiesInfoGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <selectKey keyProperty="verifiesInfoId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_VERIFIES_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="relateTable != null">
        RELATE_TABLE,
      </if>
      <if test="relateTableKey != null">
        RELATE_TABLE_KEY,
      </if>
      <if test="verifiesType != null">
        VERIFIES_TYPE,
      </if>
      <if test="lastVerifyUsername != null">
        LAST_VERIFY_USERNAME,
      </if>
      <if test="verifyUsername != null">
        VERIFY_USERNAME,
      </if>
      <if test="status != null">
        `STATUS`,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="relateTable != null">
        #{relateTable,jdbcType=VARCHAR},
      </if>
      <if test="relateTableKey != null">
        #{relateTableKey,jdbcType=INTEGER},
      </if>
      <if test="verifiesType != null">
        #{verifiesType,jdbcType=INTEGER},
      </if>
      <if test="lastVerifyUsername != null">
        #{lastVerifyUsername,jdbcType=VARCHAR},
      </if>
      <if test="verifyUsername != null">
        #{verifyUsername,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.goods.model.VerifiesInfoGenerateExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    select count(*) from T_VERIFIES_INFO
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update T_VERIFIES_INFO
    <set>
      <if test="record.verifiesInfoId != null">
        VERIFIES_INFO_ID = #{record.verifiesInfoId,jdbcType=INTEGER},
      </if>
      <if test="record.relateTable != null">
        RELATE_TABLE = #{record.relateTable,jdbcType=VARCHAR},
      </if>
      <if test="record.relateTableKey != null">
        RELATE_TABLE_KEY = #{record.relateTableKey,jdbcType=INTEGER},
      </if>
      <if test="record.verifiesType != null">
        VERIFIES_TYPE = #{record.verifiesType,jdbcType=INTEGER},
      </if>
      <if test="record.lastVerifyUsername != null">
        LAST_VERIFY_USERNAME = #{record.lastVerifyUsername,jdbcType=VARCHAR},
      </if>
      <if test="record.verifyUsername != null">
        VERIFY_USERNAME = #{record.verifyUsername,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `STATUS` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.addTime != null">
        ADD_TIME = #{record.addTime,jdbcType=BIGINT},
      </if>
      <if test="record.modTime != null">
        MOD_TIME = #{record.modTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update T_VERIFIES_INFO
    set VERIFIES_INFO_ID = #{record.verifiesInfoId,jdbcType=INTEGER},
      RELATE_TABLE = #{record.relateTable,jdbcType=VARCHAR},
      RELATE_TABLE_KEY = #{record.relateTableKey,jdbcType=INTEGER},
      VERIFIES_TYPE = #{record.verifiesType,jdbcType=INTEGER},
      LAST_VERIFY_USERNAME = #{record.lastVerifyUsername,jdbcType=VARCHAR},
      VERIFY_USERNAME = #{record.verifyUsername,jdbcType=VARCHAR},
      `STATUS` = #{record.status,jdbcType=TINYINT},
      ADD_TIME = #{record.addTime,jdbcType=BIGINT},
      MOD_TIME = #{record.modTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.model.VerifiesInfoGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update T_VERIFIES_INFO
    <set>
      <if test="relateTable != null">
        RELATE_TABLE = #{relateTable,jdbcType=VARCHAR},
      </if>
      <if test="relateTableKey != null">
        RELATE_TABLE_KEY = #{relateTableKey,jdbcType=INTEGER},
      </if>
      <if test="verifiesType != null">
        VERIFIES_TYPE = #{verifiesType,jdbcType=INTEGER},
      </if>
      <if test="lastVerifyUsername != null">
        LAST_VERIFY_USERNAME = #{lastVerifyUsername,jdbcType=VARCHAR},
      </if>
      <if test="verifyUsername != null">
        VERIFY_USERNAME = #{verifyUsername,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `STATUS` = #{status,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
    </set>
    where VERIFIES_INFO_ID = #{verifiesInfoId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.model.VerifiesInfoGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update T_VERIFIES_INFO
    set RELATE_TABLE = #{relateTable,jdbcType=VARCHAR},
      RELATE_TABLE_KEY = #{relateTableKey,jdbcType=INTEGER},
      VERIFIES_TYPE = #{verifiesType,jdbcType=INTEGER},
      LAST_VERIFY_USERNAME = #{lastVerifyUsername,jdbcType=VARCHAR},
      VERIFY_USERNAME = #{verifyUsername,jdbcType=VARCHAR},
      `STATUS` = #{status,jdbcType=TINYINT},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      MOD_TIME = #{modTime,jdbcType=BIGINT}
    where VERIFIES_INFO_ID = #{verifiesInfoId,jdbcType=INTEGER}
  </update>
</mapper>