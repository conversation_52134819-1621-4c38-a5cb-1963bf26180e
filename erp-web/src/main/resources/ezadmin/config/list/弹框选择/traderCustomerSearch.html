<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>快捷客户列表</title> 
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="traderCustomerSearch" datasource="erp-datasourcetarget" fixednumber="3" fixednumberright="0" append_column_url="" append_row_url="" empty_show="" firstcol="radio">
  <div id="appendHead"></div> 
  <div class="layui-fluid"> 
   <div class="layui-tab"> 
    <ul class="layui-tab-title" id="tab"> 
    </ul> 
   </div> 
   <form class="layui-form" id="search"> 
    <div class=" layui-inline ">
     <label class="layui-form-label">ID</label>
     <div class="layui-input-inline"><input class=" layui-input list-search-item " type="text" name="TRADER_ID" placeholder="" style="" alias="" jdbctype="" data="" datatype="" oper=""  >
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">客户名称</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="TRADER_NAME" placeholder="" style="" alias="" jdbctype="" data="" datatype="" oper="LIKE"  >
     </div>
    </div>
   </form>
   <hr class="layui-border-blue"> 
   <div class="btn-group   bd-highlight" id="tableButton">
       <button item_name="TRADER_ID,TRADER_NAME" item_id="TRADER_ID-TRADER_NAME"  url="traderSearchCallBack" opentype="" windowname="" style type="tableselectradio">选择</button>
   </div>
   <table id="table" class="layui-table" style=" width:100%"> 
    <thead> 
     <tr id="column"> 
      <th type="rowbutton" id="rowbutton"></th> 
      <th item_name="TRADER_ID" name="TRADER_ID" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">ID</th>
      <th item_name="TRADER_NAME" name="TRADER_NAME" body="td-text" order="0"   opentype="PARENT" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">客户名称</th>
      <th item_name="USERNAME" name="USERNAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">归属人</th>
       <th item_name="ORG_NAME" name="ORG_NAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">归属部门</th>
      <th item_name="REGION" name="REGION" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">地区</th>
         <th item_name="CUSTOMER_TYPE" name="CUSTOMER_TYPE" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;科研医疗&quot;,&quot;K&quot;:&quot;426&quot;},{&quot;V&quot;:&quot;临床医疗&quot;,&quot;K&quot;:&quot;427&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">客户类型</th>
      <th item_name="CUSTOMER_LEVEL" name="CUSTOMER_LEVEL" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="KVSQLCACHE" data="select SYS_OPTION_DEFINITION_ID AS `K`,TITLE as `V` from T_SYS_OPTION_DEFINITION  a WHERE a.PARENT_ID=11" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">客户等级</th>
      <th item_name="CUSTOMER_NATURE" name="CUSTOMER_NATURE" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;分销&quot;,&quot;K&quot;:&quot;465&quot;},{&quot;V&quot;:&quot;终端&quot;,&quot;K&quot;:&quot;466&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">客户性质</th>
       <th item_name="ADD_TIME" name="ADD_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">创建时间</th>
       </tr>
    </thead> 
    <tbody> 
     <tr> 
      <td> express:</td> 
      <td colspan="100"> <pre id="express" class="layui-code" orderby=" order by trader_id desc " groupby="">StringBuilder sql=new StringBuilder("SELECT A.TRADER_ID,
      A.TRADER_ID , A.TRADER_ID                                                        ID,A.IS_ENABLE*1 IS_ENABLE,
       A.TRADER_NAME,
       C.USERNAME,
       F.ORG_NAME                                                    AS           ORG_NAME,
       CC.CUSTOMER_TYPE,
       CC.CUSTOMER_NATURE,
       CC.CUSTOMER_LEVEL,
       FROM_UNIXTIME(A.ADD_TIME / 1000, '%Y-%m-%d %H:%i:%s') AS           ADD_TIME,
       CC.CUSTOMER_SCORE,
       CC.BASIC_MEDICAL_DEALER,

       FROM_UNIXTIME(A.MOD_TIME / 1000, '%Y-%m-%d %H:%i:%s'),
       A.TRADER_STATUS*1 TRADER_STATUS,

       concat(R3.REGION_NAME,'/',R2.REGION_NAME,'/',R1.REGION_NAME
              ) REGION
FROM T_TRADER_CUSTOMER CC
         LEFT JOIN T_TRADER A ON CC.TRADER_ID = A.TRADER_ID
         LEFT JOIN T_R_TRADER_J_USER B ON A.TRADER_ID = B.TRADER_ID AND B.TRADER_TYPE = 1
         LEFT JOIN T_USER C ON B.USER_ID = C.USER_ID
         left join T_R_USER_POSIT D on D.USER_ID = C.USER_ID
         LEFT JOIN T_POSITION E ON D.POSITION_ID = E.POSITION_ID
         LEFT JOIN T_ORGANIZATION F ON E.ORG_ID = F.ORG_ID

         LEFT JOIN T_REGION R1 ON R1.REGION_ID = A.AREA_ID AND R1.REGION_ID &gt; 100000
         LEFT JOIN T_REGION R2 ON R1.PARENT_ID = R2.REGION_ID AND R2.REGION_ID &gt; 100000
         LEFT JOIN T_REGION R3 ON R2.PARENT_ID = R3.REGION_ID AND R3.REGION_ID &gt; 100000

WHERE A.COMPANY_ID = 1
");


sql.append ( " and (  C.USER_ID=-1   " );

if ( isNotBlank("EZ_SESSION_ORG_IDS_LIST_KEY","session")) {
sql.append ( " or  F.ORG_ID  in (" );
sql.append ( $$("EZ_SESSION_ORG_IDS_LIST_KEY") );
sql.append (  ") " );
}

if ( isNotBlank("EZ_SESSION_MY_USER_KEY","session")) {
sql.append ( " or C.USER_ID in (" );
sql.append ( $$("EZ_SESSION_MY_USER_KEY") );
sql.append (  ") " );
}

if (isBlank("EZ_SESSION_MY_USER_KEY","session") &amp;&amp; isBlank("EZ_SESSION_ORG_IDS_LIST_KEY","session")) {
sql.append ( " or 1=1" );
}
sql.append (  ") " );


list=search(sql);
return list;</pre> </td> 
     </tr> 
     <tr> 
      <td> count:</td> 
      <td colspan="100"> <pre id="count" class="layui-code"></pre> </td> 
     </tr> 
    </tbody> 
   </table> 
  </div> 
  <div id="appendFoot"></div> 
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script> 
  <script>
    layui.use(function () {

    })
</script> 
 </body>
</html>