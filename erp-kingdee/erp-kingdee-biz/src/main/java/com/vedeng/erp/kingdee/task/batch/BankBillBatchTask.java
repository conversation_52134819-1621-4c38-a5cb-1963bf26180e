package com.vedeng.erp.kingdee.task.batch;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.kingdee.batch.job.PayBankBillJob;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 银行流水推送金蝶
 * <AUTHOR>
 */

@JobHandler("BankBillBatchTask")
@Component
@Slf4j
public class BankBillBatchTask extends AbstractJobHandler {

    @Autowired
    private PayBankBillJob payBankBillJob;

    @Autowired
    private JobLauncher jobLauncher;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        JobParameters jobParameters = new TaskBatchHandle().buildJobParameters(param);
        Job job = payBankBillJob.pullBankBIllJob();
        jobLauncher.run(job, jobParameters);
        return SUCCESS;
    }
}
