package com.vedeng.erp.buyorder.service;


import com.github.pagehelper.PageInfo;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.buyorder.dto.*;

import java.util.List;

/**
 * 采购单
 *
 * <AUTHOR>
 */
public interface BuyorderApiService {


    /**
     * 根据采购单id获取采购单信息
     *
     * @param buyorderId 采购单id
     * @return 采购单信息
     */
    BuyOrderApiDto getBuyorderByBuyorderId(Integer buyorderId);

    BuyOrderInfoDto getBuyOrderInfo(Integer buyOrderId);

    /**
     * 根据采购单编号获取采购单信息
     *
     * @param buyorderNo 采购单id
     * @return 采购单信息
     */
    BuyOrderApiDto getBuyorderByBuyorderNo(String buyorderNo);

    /**
     * 直发采购快递确认收货
     * @param expressIdList 快递id集合
     */
    void deliveryDirectConfirmArrival(List<Integer> expressIdList);

    /**
     * 是否为直发采购快递
     * @param expressId 快递id
     * @return true 是 false 否
     */
    boolean isDeliveryDirectBuyOrderExpress(Integer expressId);

    /**
     * 获取采购单合同审核状态
     * @param relatedId
     * @return
     */
    List<Integer> getContractReviewStatus(Integer relatedId);

    List<String> getAuditBuyorderContract(Integer relateId);


    /**
     * 分页查询采购订单列表
     */
    PageInfo<BuyOrderKingDeeDto> pageList(PageParam<BuyOrderKingDeeRequestDto> pageParam);

    /**
     * 保存或更新采购订单实际供应商
     * @param buyorderActualSupplierDto
     */
    void saveOrUpdateBuyorderActualSupplier(BuyorderActualSupplierDto buyorderActualSupplierDto);

    /**
     * 根据采购单id获取采购订单实际供应商
     * @param buyorderId
     * @return
     */
    BuyorderActualSupplierDto getActualSupplierByBuyorderId(Integer buyorderId);

    /**
     * 查询订单支付方式详情
     */
    OrderPaymentDetailsDto queryOrderPaymentDetails(Integer orderId, Integer orderType, Integer paymentMethodType);

    /**
     * 新增订单支付方式详情
     */
    void insertOrderPaymentDetails(Integer orderId, Integer orderType, Integer paymentMethodType);

    /**
     * 删除订单支付方式详情
     */
    void deleteOrderPaymentDetails(Long tOrderPaymentDetailsId);

    /**
     * 更新采购单商品价格
     * @param buyorderId 采购单ID
     * @param goodsList 需要更新价格的商品列表
     */
    void updateBuyorderGoodsPrices(Integer buyorderId, List<BuyorderGoodsApiDto> goodsList);
}
