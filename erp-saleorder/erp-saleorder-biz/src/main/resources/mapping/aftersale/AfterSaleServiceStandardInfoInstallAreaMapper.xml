<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.erp.aftersale.mapper.AfterSaleServiceStandardInfoInstallAreaMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.erp.aftersale.domain.entity.AfterSaleServiceStandardInfoInstallArea">
    <id column="INSTALL_AREA_ID" property="installAreaId" jdbcType="BIGINT" />
    <result column="SERVICE_STANDARD_INFO_ID" property="serviceStandardInfoId" jdbcType="BIGINT" />
    <result column="PROVINCE_CITY_JSONVALUE" property="provinceCityJsonvalue" jdbcType="VARCHAR" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="UPDATOR" property="updator" jdbcType="INTEGER" />
    <result column="ADD_TIME" property="addTime" jdbcType="VARCHAR" />
    <result column="MOD_TIME" property="modTime" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    INSTALL_AREA_ID, SERVICE_STANDARD_INFO_ID, PROVINCE_CITY_JSONVALUE, CREATOR, UPDATOR,
    ADD_TIME, MOD_TIME
  </sql>


  <select id="selectByServiceStandardInfoId" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from T_AFTER_SALE_SERVICE_STANDARD_INFO_INSTALL_AREA
    where SERVICE_STANDARD_INFO_ID = #{serviceStandardInfoId,jdbcType=BIGINT}
  </select>


</mapper>