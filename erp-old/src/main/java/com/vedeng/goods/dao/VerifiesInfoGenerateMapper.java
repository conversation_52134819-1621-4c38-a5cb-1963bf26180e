package com.vedeng.goods.dao;

import com.vedeng.goods.model.VerifiesInfoGenerate;
import com.vedeng.goods.model.VerifiesInfoGenerateExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface VerifiesInfoGenerateMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_VERIFIES_INFO
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int countByExample(VerifiesInfoGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_VERIFIES_INFO
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int deleteByExample(VerifiesInfoGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_VERIFIES_INFO
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int deleteByPrimaryKey(Integer verifiesInfoId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_VERIFIES_INFO
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int insert(VerifiesInfoGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_VERIFIES_INFO
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int insertSelective(VerifiesInfoGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_VERIFIES_INFO
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    List<VerifiesInfoGenerate> selectByExample(VerifiesInfoGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_VERIFIES_INFO
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    VerifiesInfoGenerate selectByPrimaryKey(Integer verifiesInfoId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_VERIFIES_INFO
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByExampleSelective(@Param("record") VerifiesInfoGenerate record, @Param("example") VerifiesInfoGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_VERIFIES_INFO
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByExample(@Param("record") VerifiesInfoGenerate record, @Param("example") VerifiesInfoGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_VERIFIES_INFO
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByPrimaryKeySelective(VerifiesInfoGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_VERIFIES_INFO
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByPrimaryKey(VerifiesInfoGenerate record);
}