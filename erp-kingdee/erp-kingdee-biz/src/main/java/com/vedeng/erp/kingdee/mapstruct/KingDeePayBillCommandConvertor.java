package com.vedeng.erp.kingdee.mapstruct;
/**
 * Dto转command
 */

import com.vedeng.erp.kingdee.domain.command.KingDeePayBillCommand;
import com.vedeng.erp.kingdee.dto.KingDeePayBillDto;
import com.vedeng.erp.kingdee.dto.KingDeePayBillEntryDto;
import com.vedeng.erp.kingdee.dto.KingDeePayBillSrcEntryDto;
import com.vedeng.erp.kingdee.dto.KingDeePayBillSrcEntryLinkDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface KingDeePayBillCommandConvertor {

    /**
     * 转换
     * <AUTHOR>
     * @param dto KingDeeSupplierDto
     * @return KingDeeSupplierCommand
     */
    @Mapping(target = "FBillTypeId.FNumber", source = "FBillTypeId")
    @Mapping(target = "FSettleOrgId.FNumber", source = "FSettleOrgId")
    @Mapping(target = "FPayOrgId.FNumber", source = "FPayOrgId")
    @Mapping(target = "FContactUnit.FNumber", source = "FContactUnit")
    @Mapping(target = "FRectUnit.FNumber", source = "FRectUnit")
    @Mapping(target = "FCurrencyId.FNumber", source = "FCurrencyId")
    @Mapping(target = "FSettleCur.FNumber", source = "FSettleCur")
    @Mapping(target = "FSettleMainBookId.FNumber", source = "FSettleMainBookId")
    @Mapping(target = "f_Qzok_dr", source = "FQzokDr")
    @Mapping(target = "f_Qzok_Jylx", source = "FQzokJylx")
    @Mapping(target = "f_Qzok_Jyzt", source = "FQzokJyzt")
    @Mapping(target = "f_Qzok_Cgddh", source = "FQzokCgddh")
    @Mapping(target = "f_qzok_lsh", source = "FQzokLsh")
    @Mapping(target = "f_qzok_pzgsywdh", source = "FQzokPzgsywdh")
    KingDeePayBillCommand toCommand(KingDeePayBillDto dto);

    @Mapping(target = "fsettletypeid.FNumber", source = "fsettletypeid")
    @Mapping(target = "fpurposeid.FNumber", source = "fpurposeid")
    @Mapping(target = "fcostid.FNumber", source = "fcostid")
    @Mapping(target = "fexpensedeptidE.FNumber", source = "fexpensedeptidE")
    @Mapping(target = "faccountid.FNumber", source = "faccountid")
    @Mapping(target = "f_qzok_ysddh", source = "FQzokYsddh")
    @Mapping(target = "f_qzok_gsywdh", source = "FQzokGsywdh")
    @Mapping(target = "f_qzok_ywlx", source = "FQzokYwlx")
    KingDeePayBillCommand.KingDeePayBillEntryCommand toCommand(KingDeePayBillEntryDto dto);

    @Mapping(target = "fsrccurrencyid.FNumber", source = "fsrccurrencyid")
    @Mapping(target = "fpaybillsrcentryLink", source = "fpaybillsrcentryLink")
    KingDeePayBillCommand.KingDeePayBillSrcEntryCommand toCommand(KingDeePayBillSrcEntryDto dto);


    KingDeePayBillCommand.KingDeePayBillSrcEntryLinkCommand toCommand(KingDeePayBillSrcEntryLinkDto dto);
}
