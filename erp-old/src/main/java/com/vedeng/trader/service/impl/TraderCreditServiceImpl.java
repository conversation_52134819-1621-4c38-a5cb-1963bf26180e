package com.vedeng.trader.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.page.Page;
import com.vedeng.trader.dao.TraderCreditMapper;
import com.vedeng.trader.model.dto.*;
import com.vedeng.trader.service.TraderCreditService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class TraderCreditServiceImpl implements TraderCreditService {

    Logger logger = LoggerFactory.getLogger(TraderCreditServiceImpl.class);

    @Resource
    private TraderCreditMapper traderCreditMapper;

    @Override
    public CustomerBillPeriodListViewDto getCustomerBillPeriodListView(CustomerBillPeriodListQueryDto queryDto, Page page) {
        logger.info("getCustomerBillPeriodListView queryDto:{}", JSON.toJSONString(queryDto));

        HashMap<String, Object> requestMap = dealCustomerBillPeriodListParamData(queryDto, page);

        CustomerBillPeriodListViewDto customerBillPeriodListViewDto = new CustomerBillPeriodListViewDto();
        customerBillPeriodListViewDto.setPage(page);
        customerBillPeriodListViewDto.setPeriodRecordStatementDto(traderCreditMapper.getPeriodRecordStatement(requestMap));
        customerBillPeriodListViewDto.setPeriodOrderStatementDto(traderCreditMapper.getPeriodOrderStatement(requestMap));
        List<CustomerBillPeriodItemDto> creditViewList = traderCreditMapper.getTraderCreditViewListPage(requestMap);

        if (CollectionUtils.isEmpty(creditViewList)) {
            return customerBillPeriodListViewDto;
        }
        customerBillPeriodListViewDto.setCustomerBillPeriodItemDtoList(creditViewList);

        //客户账期可用额度
        Map<Long, BigDecimal> periodIdCreditUsableAmountMap = traderCreditMapper.getCreditUsableAmountByPeriodIds(
                creditViewList.stream().map(CustomerBillPeriodItemDto::getBillPeriodId).distinct().collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(CustomerBillPeriodUsableAmountDto::getBillPeriodId, CustomerBillPeriodUsableAmountDto::getCreditUsableAmount));

        List<Long> periodUseDetailIds = creditViewList.stream().map(CustomerBillPeriodItemDto::getBillPeriodUseDetailId).distinct().collect(Collectors.toList());

        //客户使用明细没有被监管到的金额
        HashMap<Long, BigDecimal> useDetailIdUnSuperViseAmountMap = new HashMap<>(16);

        Map<Long, BigDecimal> periodUseDetailRiskMap = traderCreditMapper.getRiskAmountByDetailIds(periodUseDetailIds)
                .stream().collect(Collectors.toMap(CustomerBillPeriodRiskAmountDto::getBillPeriodUseDetailId,
                        CustomerBillPeriodRiskAmountDto::getRiskAmount));
        if (MapUtils.isNotEmpty(periodUseDetailRiskMap)) {
            Map<Long, BigDecimal> periodUseIdSuperviseMap = traderCreditMapper.getSuperViseAmountByDetailIds(periodUseDetailIds)
                    .stream().collect(Collectors.toMap(CustomerBillPeriodSuperViseAmountDto::getBillPeriodUseDetailId,
                            CustomerBillPeriodSuperViseAmountDto::getSuperViseAmount));
            periodUseDetailRiskMap.forEach((key, value) -> useDetailIdUnSuperViseAmountMap.put(key, value.subtract(periodUseIdSuperviseMap.getOrDefault(key, BigDecimal.ZERO))));
        }

        creditViewList.forEach(item -> {
            item.setCreditUsableAmount(periodIdCreditUsableAmountMap.getOrDefault(item.getBillPeriodId(), BigDecimal.ZERO));
            BigDecimal unSuperViseAmount = useDetailIdUnSuperViseAmountMap.getOrDefault(item.getBillPeriodUseDetailId(), BigDecimal.ZERO);
            item.setUnSuperViseAmount(unSuperViseAmount.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : unSuperViseAmount);
        });
        return customerBillPeriodListViewDto;
    }

    /**
     * 处理客户账期信息参数
     *
     * @param queryDto
     * @param page
     * @return
     */
    HashMap<String, Object> dealCustomerBillPeriodListParamData(CustomerBillPeriodListQueryDto queryDto, Page page) {
        HashMap<String, Object> paramMap = new HashMap<>(8);
        paramMap.put("page", page);
        if (queryDto == null) {
            return paramMap;
        }
        paramMap.put("overdueState", queryDto.getOverdueState());
        paramMap.put("orderIds", queryDto.getOrderIds());
        paramMap.put("billPeriodType", queryDto.getBillPeriodType());
        paramMap.put("isReturn", queryDto.getIsReturn());
        return paramMap;
    }

    @Override
    public CustomerBillPeriodApplyListDto getCustomerBillPeriodApplyList(CustomerBillPeriodApplyListQueryDto queryDto, Page page) {
        logger.info("getCustomerBillPeriodApplyList queryDto:{}", JSON.toJSONString(queryDto));
        CustomerBillPeriodApplyListDto customerBillPeriodApplyListDto = new CustomerBillPeriodApplyListDto();
        customerBillPeriodApplyListDto.setPage(page);

        List<CustomerBillPeriodApplyItemDto> customerBillPeriodApplyList = traderCreditMapper.getCustomerBillPeriodApplyListPage(
                dealCustomerBillPeriodApplyListParamData(queryDto, page)
        );
        if (CollectionUtils.isEmpty(customerBillPeriodApplyList)) {
            return customerBillPeriodApplyListDto;
        }

        customerBillPeriodApplyListDto.setCustomerBillPeriodApplyItemDtoList(customerBillPeriodApplyList);

        List<Long> billPeriodIds = customerBillPeriodApplyList.stream().filter(item -> item.getBillPeriodId() != null && item.getBillPeriodId() != 0)
                .map(CustomerBillPeriodApplyItemDto::getBillPeriodId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(billPeriodIds)) {
            return customerBillPeriodApplyListDto;
        }

        Map<Long, BigDecimal> periodIdCreditUsableAmountMap = traderCreditMapper.getCreditUsableAmountByPeriodIds(billPeriodIds).stream()
                .collect(Collectors.toMap(CustomerBillPeriodUsableAmountDto::getBillPeriodId, CustomerBillPeriodUsableAmountDto::getCreditUsableAmount));

        customerBillPeriodApplyList.stream().filter(item -> item.getBillPeriodId() != null && item.getBillPeriodId() != 0).forEach(item -> {
            item.setCreditUsableAmount(periodIdCreditUsableAmountMap.getOrDefault(item.getBillPeriodId(), BigDecimal.ZERO));
        });
        return customerBillPeriodApplyListDto;
    }

    @Override
    public List<Long> getCustomerBIllPeriodApplyByCreator(Integer status, Integer userId) {
        if(status != null && status == 0){
            List<Long> customerIdList = traderCreditMapper.getCustomerBIllPeriodApplyByCreator(status,userId);
            return customerIdList;
        }
        return null;
    }

    /**
     * 处理客户账期申请信息参数
     *
     * @param queryDto
     * @param page
     * @return
     */
    HashMap<String, Object> dealCustomerBillPeriodApplyListParamData(CustomerBillPeriodApplyListQueryDto queryDto, Page page) {
        HashMap<String, Object> paramMap = new HashMap<>(8);
        paramMap.put("page", page);
        if (queryDto == null) {
            return paramMap;
        }
        paramMap.put("customerIdList", queryDto.getCustomerIdList());
        paramMap.put("checkStatus", queryDto.getCheckStatus());
        paramMap.put("billPeriodStart", queryDto.getBillPeriodStart());
        paramMap.put("billPeriodEnd", queryDto.getBillPeriodEnd());
        paramMap.put("billPeriodType", queryDto.getBillPeriodType());
        paramMap.put("creator", queryDto.getCreatorId());
        paramMap.put("customerBillPeriodApplyIdList",queryDto.getCustomerBillPeriodApplyIdList());
        return paramMap;
    }
}
