package com.vedeng.erp.saleorder.manager.esign;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.eventbus.EventBus;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.base.api.dto.reqParam.BusinessInfo;
import com.vedeng.base.api.dto.reqParam.FileInfo;
import com.vedeng.base.api.dto.reqParam.SignCallbackDto;
import com.vedeng.base.api.dto.resParam.SignCompanyInfo;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.OrderConstant;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.listenerEvent.SaleOrderContractReturnEvent;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.common.util.ObjectUtils;
import com.vedeng.common.util.UrlUtils;
import com.vedeng.erp.system.dto.BaseCompanyInfoDto;
import com.vedeng.erp.system.service.BaseCompanyInfoApiService;
import com.vedeng.infrastructure.esign.domain.ElectronicSignParam;
import com.vedeng.infrastructure.esign.enums.ElectronicSignBusinessEnums;
import com.vedeng.infrastructure.esign.handle.AbstractElectronicSignHandle;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.system.dao.AttachmentMapper;
import com.vedeng.system.dao.ProcinstMapper;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.model.vo.UrlToPdfParam;
import com.vedeng.system.service.OssUtilsService;
import com.vedeng.system.service.VerifiesRecordService;
import com.vedeng.trader.dao.RTraderJUserMapper;
import com.vedeng.trader.model.RTraderJUser;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.task.Task;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 销售单e签宝实现类
 * @date 2021/12/7 9:26
 */
@Component
@Slf4j
public class SaleOrderElectronicSignHandle extends AbstractElectronicSignHandle {

    @Autowired
    private SaleorderMapper saleorderMapper;
    @Autowired
    private OssUtilsService ossUtilsService;
    @Autowired
    private AttachmentMapper attachmentMapper;

    @Autowired
    private ActionProcdefService actionProcdefService;

    @Autowired
    private ProcessEngine processEngine;

    @Autowired
    private SaleorderService saleorderService;

    @Autowired
    protected VerifiesRecordService verifiesRecordService;

    @Autowired
    private ProcinstMapper procinstMapper;

    @Value("${erp.domain}")
    private String erpDomain;

    @Value("${html2Pdf.domain}")
    private String html2PdfDomain;

    @Value("${ht_remove_blank_page}")
    private Boolean isRemoveBlankPage;

    private static final String CONTRACT_URL = "/orderstream/saleorder/contract_template/print.do?saleorderId={}&noStamp={}";

    private static final String RENDER_URL = "/api/render";

    private static final String FILENAME_PREFIX = "订单合同";

    @Autowired
    private RTraderJUserMapper rTraderJUserMapper;

    @Autowired
    @Qualifier(value = "eventBusCenter")
    private EventBus eventBusCenter;

    @Override
    protected void electronicSignFailCompensate(String error, ElectronicSignParam electronicSignParam) {
        log.error("订单异步签章任务失败日志[{}],[{}]" , error, JSON.toJSONString(electronicSignParam));
        if (electronicSignParam.getFlowType() == 2 || electronicSignParam.getFlowType() == 3) {
            // 将链接更新到合同未盖章字段
            saleorderMapper.updateContractNoStampUrlOfSaleorder(electronicSignParam.getSaleOrderId(), null);
            // 将链接更新到合同半程盖章字段
            saleorderMapper.updateContractUrlOfSaleorder(electronicSignParam.getSaleOrderId(), null);
        }
    }

    @Override
    protected void preMqProcessors(SignCallbackDto signCallbackDto) {
        log.info("进入销售订单实际消费业务类前置处理器,编号{}" , signCallbackDto.getOrderNo());
        AtomicInteger retryCount = new AtomicInteger(0);
        Saleorder saleorder = saleorderMapper.getSaleorderByOrderNo(signCallbackDto.getOrderNo());
        String fileName;
        if (saleorder != null) {
            //查询到销售单信息的情况下，根据新文件命名规则，订单号+客户名
            fileName = saleorder.getSaleorderNo() + saleorder.getTraderName();
        } else {
            //未查询到信息的情况下，维持原逻辑
            fileName = FILENAME_PREFIX + signCallbackDto.getOrderNo();
        }
        log.info("消费-》oss保存文件名：{}" , fileName);
        this.saveFile2Oss(signCallbackDto, retryCount, fileName);
    }

    @Override
    protected FileInfo toPdfGetFile(ElectronicSignParam electronicSignParam) {
        if (electronicSignParam.getSaleOrderId() == null) {
            log.error("必要参数SaleOrderId为空！");
            throw new ServiceException("电子签章:订单id不能为空");
        }

        Saleorder saleorder = saleorderMapper.getSaleorderBySaleorderId(electronicSignParam.getSaleOrderId());
        electronicSignParam.getBusinessInfo().setOrderNo(saleorder.getSaleorderNo());

        String contractTemplateUrl = erpDomain + CONTRACT_URL;
        contractTemplateUrl = StrUtil.format(contractTemplateUrl, electronicSignParam.getSaleOrderId(), false);
        log.info("电子签章合同模板请求url[{}]" , contractTemplateUrl);
        String html2PdfUrl = html2PdfDomain + RENDER_URL;
        UrlToPdfParam urlToPdfParam = UrlToPdfParam.defaultUrlToPdfParam(contractTemplateUrl);
        // 上传未盖章空白合同链接
        String ossUrl = ossUtilsService.migrateFile2Oss(html2PdfUrl, "pdf" , saleorder.getSaleorderNo() + saleorder.getTraderName(), urlToPdfParam);
        log.info("原电子签章:自动生成订单模板合同,订单ID[{}]，订单编号[{}]，合同url[{}]" , saleorder.getSaleorderId(), saleorder.getSaleorderNo(), ossUrl);
        if (isRemoveBlankPage) {
            AtomicInteger retryCount = new AtomicInteger(0);
            ossUrl = removeBlankPdfPagesAndSaveFile2Oss(ossUrl, retryCount, saleorder.getSaleorderNo() + saleorder.getTraderName());
        }
        if (StringUtils.isBlank(ossUrl)) {
            log.error("电子签章:自动生成订单模板合同失败，订单ID[{}]" , electronicSignParam.getSaleOrderId());
            throw new ServiceException("电子签章:自动生成订单合同失败");
        }
        log.info("电子签章:自动生成订单模板合同,订单ID[{}]，订单编号[{}]，合同url[{}]" , saleorder.getSaleorderId(), saleorder.getSaleorderNo(), ossUrl);
        // 仅发起半程签署合同进行更新url
        if (electronicSignParam.getFlowType() == 2) {
            // 将链接更新到合同未盖章字段
            saleorderMapper.updateContractNoStampUrlOfSaleorder(saleorder.getSaleorderId(), ossUrl);
            // 将链接更新到合同半程盖章字段
            saleorderMapper.updateContractUrlOfSaleorder(saleorder.getSaleorderId(), ossUrl);
        }

        FileInfo fileInfo = new FileInfo();
        fileInfo.setFileName(CharSequenceUtil.subAfter(ossUrl, "=" , true) + ".pdf");
        fileInfo.setFilePath(ossUrl);
        return fileInfo;
    }

    @Override
    public ElectronicSignParam buildElectronicSignParam(Integer businessId, BusinessInfo businessInfo) {
        if(checkSignCompanyInfo(businessId)){//销售订单的客户，存在于分子公司配置表中
            return ElectronicSignParam
                    .builder()
                    .flowType(3)
                    .saleOrderId(businessId)
                    .businessInfo(businessInfo)
                    .electronicSignBusinessEnums(ElectronicSignBusinessEnums.MULT_SALEORDER_SEAL)
                    .build();

        }else{
            return ElectronicSignParam
                    .builder()
                    .flowType(2)
                    .saleOrderId(businessId)
                    .businessInfo(businessInfo)
                    .electronicSignBusinessEnums(ElectronicSignBusinessEnums.SALE_ORDER)
                    .build();
         }
    }

    @Value("${erpCompanyName:南京贝登医疗股份有限公司}")
    private String erpCompanyName;

    @Override
    protected void getSignCompanyInfoList(ElectronicSignParam electronicSignParam, List<SignCompanyInfo> signCompanyInfoList) {
        Integer saleOrderId = electronicSignParam.getSaleOrderId();
        Saleorder saleorder = saleorderMapper.getSaleOrderById(saleOrderId);
        if (saleorder == null) {
            log.error("发起电子签单时，多主体，该订单未找到");
            return;
        }

        //甲方
        SignCompanyInfo signCompanyInfo = new SignCompanyInfo();
        signCompanyInfo.setSignCompanyName(saleorder.getTraderName());
        signCompanyInfo.setHideCompanyName(STATIC_HIDE_NAME_JIA);
        signCompanyInfo.setSignAllSeal(true);
        signCompanyInfo.setSignSealName(STATIC_SEAL_NAME_HETONG);
        signCompanyInfoList.add(signCompanyInfo);

        //已方
        SignCompanyInfo signCompanyInfoYi = new SignCompanyInfo();
        signCompanyInfoYi.setSignCompanyName(erpCompanyName);
        signCompanyInfoYi.setHideCompanyName(STATIC_HIDE_NAME_YI);
        signCompanyInfoYi.setSignAllSeal(true);
        signCompanyInfoYi.setSignSealName(STATIC_SEAL_NAME_HETONG);
        signCompanyInfoList.add(signCompanyInfoYi);


    }

    @Override
    protected void electronicSignSuccessCompensate(ElectronicSignParam electronicSignParam) {
    }


    /**
     * 1. 全程签章 新增合同回传记录
     * 2. 半程签贝登章 更新合同url
     *
     * @param signCallbackDto 电子签章MQ回传消息
     */
    @Override
    protected void mqProcessors(SignCallbackDto signCallbackDto) {
        log.info("进入销售单实际消费业务类,订单编号{}" , signCallbackDto.getOrderNo());
        Saleorder saleorder = saleorderMapper.getSaleorderByOrderNo(signCallbackDto.getOrderNo());
        if (saleorder == null) {
            log.error("销售单不存在,订单编号{}" , signCallbackDto.getOrderNo());
            throw new ServiceException("销售单不存在");
        }

        // 新增合同回传记录
        Integer saleorderId = saleorder.getSaleorderId();
        if (signCallbackDto.getBusinessType() == 1  ) {
            try {
                Saleorder baseSaleorderInfo = saleorderMapper.getBaseSaleorderInfo(saleorderId);
                log.info("电子签章新增合同回传记录:{}",baseSaleorderInfo.getSaleorderNo()+" : "+baseSaleorderInfo.getContractStatus());
                if(baseSaleorderInfo.getContractStatus() != null && baseSaleorderInfo.getContractStatus() == 2){
                    delContract(saleorderId);
                }
                if (baseSaleorderInfo.getContractStatus() == null || baseSaleorderInfo.getContractStatus() == 2){
                    int result = this.updateRecordInfo(signCallbackDto, saleorder);
                    if (ErpConst.ZERO.equals(result)) {
                        log.error("新增合同回传记录失败,订单编号{}" , signCallbackDto.getOrderNo());
                        throw new ServiceException("新增合同回传记录失败");
                    }
                }

            } catch (Exception e) {
                log.error("新增合同回传记录失败,订单编号{}" , signCallbackDto.getOrderNo(), e);
                throw new ServiceException("新增合同回传记录失败");
            }
        }

        // 更新合同url
        if (signCallbackDto.getBusinessType() == 2 || signCallbackDto.getBusinessType() == 3) {//半程章或者与子公司的双章
            log.info("更新合同url,订单编号{}" , signCallbackDto.getOrderNo());
            saleorderMapper.updateContractUrlOfSaleorder(saleorderId, signCallbackDto.getFileUrl());
        }

        if(signCallbackDto.getBusinessType() == 3){//20250627新增双章回传兼容
            // 发布合同回传事件
            try {
                URI uri = new URI(signCallbackDto.getFileUrl());
                String host = uri.getHost();
                String path = uri.getPath();
                String query = uri.getQuery();
                String pathAndQuery = path + (query != null ? "?" + query : "");
                String resourceId = null;
                if (query != null) {
                    String[] params = query.split("&"); // 按 "&" 分割多个参数
                    for (String param : params) {
                        String[] keyValue = param.split("="); // 按 "=" 分割键值对
                        if (keyValue.length == 2 && "resourceId".equals(keyValue[0])) {
                            resourceId = keyValue[1];
                            break;
                        }
                    }
                }
                // 创建销售订单合同回传事件
                SaleOrderContractReturnEvent contractReturnEvent = SaleOrderContractReturnEvent.builder()
                    .saleorderId(saleorderId)
                    .saleorderNo(saleorder.getSaleorderNo())
                    .fileName("销售订单" + saleorder.getSaleorderNo()+ "-合同-双边章.pdf")
                    .filePath(pathAndQuery)
                    .domain(host)
                    .ossResourceId(resourceId)
                    .signTime(signCallbackDto.getSignTime())
                    .fileUrl(signCallbackDto.getFileUrl())
                    .flowOrderId(null)
                    .flowOrderNo(null)
                    .businessType(1) // 设置为1表示全程签章，触发合同回传记录逻辑
                    .build();

                eventBusCenter.post(contractReturnEvent);
                log.info("发布销售订单合同回传事件成功，订单编号: {}, 流转单ID: {}");

            } catch (Exception eventException) {
                log.error("发布销售订单合同回传事件失败，订单编号: {}, 流转单ID: {}",
                        null, null, eventException);
            }

        }



    }

    private void delContract(Integer saleorderId) {
        Attachment update = new Attachment();
        update.setRelatedId(saleorderId);
        update.setAttachmentFunction(SysOptionConstant.ID_492);
        attachmentMapper.updateByAttachment(update);
        if(saleorderId != null){
            // 将T_VERIFIES_INFO表的数据删除
            log.info("电子签章新增合同回传记录，删除T_VERIFIES_INFO表的数据，saleorderId:{}", saleorderId);
            attachmentMapper.delVerifiesInfo(update);
        }
    }

    @Override
    protected void postMqProcessors(SignCallbackDto signCallbackDto) {

    }


    /**
     * 通过订单号,保存电子签章 的记录及审核记录
     *
     * @param signCallbackDto 回调参数
     * @param saleOrder       订单
     * @return 新增结果
     */
    private int updateRecordInfo(SignCallbackDto signCallbackDto, Saleorder saleOrder) {
        boolean sendMessage;
        log.info("signCallbackDto->{},saleOrder->{}" , JSON.toJSONString(signCallbackDto), JSON.toJSONString(saleOrder));
        String[] domainAndUriFromUrl = UrlUtils.getDomainAndUriFromUrl(signCallbackDto.getFileUrl());
        Attachment attachment = new Attachment();
        attachment.setRelatedId(saleOrder.getSaleorderId());
        //电子签章类型
        attachment.setAttachmentFunction(SysOptionConstant.ID_492);
        //文件类型
        attachment.setAttachmentType(SysOptionConstant.ID_461);
        attachment.setSuffix("pdf");
        if (ArrayUtil.isNotEmpty(domainAndUriFromUrl)) {
            attachment.setDomain(domainAndUriFromUrl[0]);
            attachment.setUri(domainAndUriFromUrl[1]);
            attachment.setName(CharSequenceUtil.subAfter(signCallbackDto.getFileUrl(), "=" , true));
        }
        //1. 如果ERP中该订单没有上传合同，则将合同记录在“销售订单详情-合同回传”中。
        attachment.setAddTime(DateUtil.parseDateTime(signCallbackDto.getSignTime()).getTime());
        //不覆盖合同,并发站内信
        attachment.setIsDeleted(0);
        attachment.setAlt("电子签章");
        int result = attachmentMapper.insertSelective(attachment);
        log.info("新增合同回传记录成功,relatedId:[{}],附件id[{}]", attachment.getRelatedId(), attachment.getAttachmentId());
        //修改回传状态
        saleorderMapper.uptContactStatus(OrderConstant.ORDER_CONTRACT_ISRETURN, attachment.getRelatedId());
        Saleorder saleOrderReturn = saleorderMapper.getSaleOrderById(saleOrder.getSaleorderId());
        if (null != saleOrderReturn) {
            RTraderJUser rTraderUser = rTraderJUserMapper.getUserByTraderId(saleOrderReturn.getTraderId());
            //发送站内信给订单归属销售
            if (null != rTraderUser) {
                Map<String, String> map = new HashMap<>(1);
                String url = "./order/saleorder/view.do?saleorderId=";
                if (ObjectUtils.isEmpty(saleOrderReturn.getIsNew())) {
                    url = "./orderstream/saleorder/detail.do?saleOrderId=";
                }
                map.put("saleorderNo", saleOrderReturn.getSaleorderNo());
                log.info("站内信发送,saleOrderId:[{}],saleOrderReturn:[{}]", saleOrder.getSaleorderId(), JSON.toJSONString(saleOrderReturn));
                MessageUtil.sendMessage(144, Collections.singletonList(rTraderUser.getUserId()), map, url + saleOrder.getSaleorderId());
            }
        }
        log.info("新增合同回传记录成功,saleOrderId:[{}]", saleOrder.getSaleorderId());
        try {
            this.addApplyValidContractReturn(signCallbackDto, saleOrder);
            log.info("新增合同回传审核记录成功,saleOrderId:[{}]", saleOrder.getSaleorderId());
        } catch (Exception e) {
            log.error("新增合同回传审核记录失败:", e);
        }
        return result;
    }


    /**
     * 新增审核记录
     *
     * @param signCallbackDto 回调参数
     * @param saleOrder       订单
     */
    private void addApplyValidContractReturn(SignCallbackDto signCallbackDto, Saleorder saleOrder) throws Exception {
        Map<String, Object> variableMap = new HashMap<>();
        saleOrder.setOptType("orderDetail");
        Saleorder saleOrderInfo = saleorderService.getBaseSaleorderInfoNew(saleOrder);
        // 查询当前订单的一些状态
        // 开始生成流程
        variableMap.put("saleorderInfo" , saleOrderInfo);
        variableMap.put("currentAssinee" , "njadmin");
        variableMap.put("processDefinitionKey" , "contractReturnVerify");
        variableMap.put("businessKey" , "contractReturnVerify_" + saleOrder.getSaleorderId());
        variableMap.put("relateTableKey" , saleOrder.getSaleorderId());
        variableMap.put("relateTable" , "T_SALEORDER");
        actionProcdefService.createProcessInstance(null, "contractReturnVerify" ,
                "contractReturnVerify_" + saleOrder.getSaleorderId(), variableMap);
        // 默认申请人通过
        // 根据BusinessKey获取生成的审核实例
        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
                "contractReturnVerify_" + saleOrder.getSaleorderId());
        if (historicInfo.get("endStatus") != "审核完成") {
            Task taskInfo = (Task) historicInfo.get("taskInfo");
            Authentication.setAuthenticatedUserId("njadmin");
            Map<String, Object> variables = new HashMap<>();
            // 默认审批通过
            ResultInfo<?> complementStatus = actionProcdefService.complementTask(null, taskInfo.getId(), "" ,
                    "njadmin" , variables);
            // 如果未结束添加审核对应主表的审核状态
            if (!"endEvent".equals(complementStatus.getData())) {
                verifiesRecordService.saveVerifiesInfo(taskInfo.getId(), ErpConst.ZERO);
            }
            //此处增加电子签章回传的,修改sql
            Map<String, Object> map = new HashMap<>();
            map.put("taskId" , taskInfo.getId());
            log.info("新增合同回传记录成功,taskInfo:[{}]" , taskInfo.getId());
            List<Map<String, Object>> resultMap = procinstMapper.getActHiInfoByTaskId(map);
            if (null != resultMap) {
                Map<String, Object> mapAdd = resultMap.get(0);
                mapAdd.put("ACT_NAME_" , "电子签章回传");
                mapAdd.put("ASSIGNEE_" , "客户");
                mapAdd.put("NAME_" , "电子签章回传");
                mapAdd.put("END_TIME_" , signCallbackDto.getSignTime());
                log.info("新增合同回传记录成功,END_TIME_:[{}]" , signCallbackDto.getSignTime());
                procinstMapper.updateActHiActinstInfoByTaskId(mapAdd);
                procinstMapper.updateActHiTaskinstInfoById(mapAdd);
            }
        }
    }

    @Override
    protected void dealWithByMqException(String errorMsg, SignCallbackDto signCallbackDto) {
        log.error("订单Mq消费失败，补偿任务触发[{}]" , JSON.toJSONString(signCallbackDto));
        if (signCallbackDto.getBusinessType() == 2) {
            // 将链接更新到合同未盖章字段
            Saleorder saleorder = saleorderMapper.getSaleOrderId(signCallbackDto.getOrderNo());
            saleorderMapper.updateContractNoStampUrlOfSaleorder(saleorder.getOrderId(), null);
            // 将链接更新到合同半程盖章字段
            saleorderMapper.updateContractUrlOfSaleorder(saleorder.getOrderId(), null);
        }
    }


    @Autowired
    private BaseCompanyInfoApiService baseCompanyInfoApiService;


    private boolean checkSignCompanyInfo(Integer saleOrderId) {
        Saleorder saleorder = saleorderMapper.getSaleOrderById(saleOrderId);
        if (saleorder == null) {
            log.error("发起电子签单时，多主体，该订单未找到");
            return false;
        }

        BaseCompanyInfoDto baseCompanyInfoDto = baseCompanyInfoApiService.selectBaseCompanyByCompanyName(saleorder.getTraderName());
        if (baseCompanyInfoDto == null) {
            return false;
        }
        return true;

    }
}
