package com.vedeng.erp.trader.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.trader.domain.dto.QuoteorderGoodsDto;
import com.vedeng.erp.trader.domain.entity.QuoteorderGoodsEntity;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/13 12:44
 **/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface QuoteorderGoodsConvertor extends BaseMapStruct<QuoteorderGoodsEntity, QuoteorderGoodsDto> {
}
