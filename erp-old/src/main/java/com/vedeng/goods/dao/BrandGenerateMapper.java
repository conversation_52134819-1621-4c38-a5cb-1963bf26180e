package com.vedeng.goods.dao;

import com.vedeng.goods.model.BrandGenerate;
import com.vedeng.goods.model.BrandGenerateExample;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

public interface BrandGenerateMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BRAND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int countByExample(BrandGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BRAND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int deleteByExample(BrandGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BRAND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int deleteByPrimaryKey(Integer brandId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BRAND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int insert(BrandGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BRAND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int insertSelective(BrandGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BRAND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    List<BrandGenerate> selectByExampleWithBLOBs(BrandGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BRAND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    List<BrandGenerate> selectByExample(BrandGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BRAND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    BrandGenerate selectByPrimaryKey(Integer brandId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BRAND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByExampleSelective(@Param("record") BrandGenerate record, @Param("example") BrandGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BRAND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByExampleWithBLOBs(@Param("record") BrandGenerate record, @Param("example") BrandGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BRAND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByExample(@Param("record") BrandGenerate record, @Param("example") BrandGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BRAND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByPrimaryKeySelective(BrandGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BRAND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByPrimaryKeyWithBLOBs(BrandGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BRAND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByPrimaryKey(BrandGenerate record);

    List<BrandGenerate> getBrandInfoByParam(BrandGenerate brandGenerate);
}