package com.vedeng.todolist.controller;

import com.google.inject.internal.cglib.core.$CollectionUtils;
import com.vedeng.authorization.model.Region;
import com.vedeng.authorization.model.User;
import com.vedeng.common.annotation.SystemControllerLog;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.service.BaseService;
import com.vedeng.common.shiro.JedisUtils;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.firstengage.service.FirstEngageService;
import com.vedeng.order.service.SupplierCommonService;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.service.RegionService;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.model.TraderSupplier;
import com.vedeng.trader.model.vo.TraderCertificateVo;
import com.vedeng.trader.model.vo.TraderMedicalCategoryVo;
import com.vedeng.trader.model.vo.TraderSupplierVo;
import com.vedeng.trader.model.vo.TraderVo;
import com.vedeng.trader.service.TraderCustomerService;
import com.vedeng.trader.service.TraderSupplierService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.*;

/**
 * 供应商信息页面展示接口.
 * @jira: VDERP-4324【分级分档】供应链工作台(结合分级分档).
 * @notes: .
 * @version: 1.0.
 * @date: 2020/12/23 上午11:29.
 * @author: Tomcat.Hui.
 */
@Controller
@RequestMapping("/todolist")
public class SupplierInfoController extends BaseController {

    @Autowired
    private SupplierCommonService supplierCommonService;

    @Autowired
    private TraderSupplierService traderSupplierService;

    @Autowired
    @Qualifier("regionService")
    private RegionService regionService;

    @Autowired
    @Qualifier("traderCustomerService")
    private TraderCustomerService traderCustomerService;

    @Autowired
    private FirstEngageService firstEngageService;

    @Autowired
    private BaseService baseService;

    /**
     * 国标分类，一级
     */
    private static final Integer STANDARD_PARRNT_ID_0 = 0;


    @ResponseBody
    @RequestMapping(value = "risk_check/editsupplierbaseinfo")
    public ModelAndView editBaseInfo(TraderSupplier traderSupplier) throws IOException {
        ModelAndView mv = new ModelAndView();
        TraderSupplierVo traderBaseInfo = traderSupplierService.getTraderSupplierBaseInfo(traderSupplier);
        //地区
        List<Region> provinceList = regionService.getRegionByParentId(1);
        if (null != traderBaseInfo.getTrader().getAreaId() && traderBaseInfo.getTrader().getAreaId() > 0
                && org.apache.commons.lang3.StringUtils.isNotBlank(traderBaseInfo.getTrader().getAreaIds())) {

            setRegionsInfo(mv, traderBaseInfo.getTrader().getAreaId(), "provinceRegion",
                    "cityList", "cityRegion", "zoneList",
                    "zoneRegion", "countryRegion");
        }

        if (traderBaseInfo.getWarehouseAreaId() != null && traderBaseInfo.getWarehouseAreaId() > 0){
            setRegionsInfo(mv, traderBaseInfo.getWarehouseAreaId(), "warehouseProvinceRegion",
                    "warehouseCityList", "warehouseCityRegion",
                    "warehouseZoneList", "warehouseZoneRegion", "warehouseCountryRegion");
        }
        setNewStandCategoryList(mv);

        mv.addObject("traderSupplier", traderBaseInfo);
        mv.addObject("provinceList", provinceList);
        mv.addObject("method", "baseinfo");
        traderSupplier.setTraderId(traderBaseInfo.getTraderId());

        TraderCertificateVo traderCertificate = new TraderCertificateVo();
        traderCertificate.setTraderId(traderSupplier.getTraderId());
        traderCertificate.setTraderType(ErpConst.TWO);
        mv.addObject("traderSupplier1", traderSupplier);

        // 营业执照信息
        traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_25);
        List<TraderCertificateVo> bus = supplierCommonService.getTraderCertificateVos(traderCertificate);
        if (!CollectionUtils.isEmpty(bus)) {
            mv.addObject("business", bus.get(0));
        }

        // 二类医疗资质 多证合一辅助证明
        traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_28);
        List<TraderCertificateVo> tList = supplierCommonService.getTraderCertificateVos(traderCertificate);
        if (!CollectionUtils.isEmpty(tList)) {
            mv.addObject("twoMedicalList", tList);
        }
        mv.addObject("twoMedical", CollectionUtils.isEmpty(tList) ? null: tList.get(0));

        // 销售授权书 与 销售人信息
        traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_1100);
        List<TraderCertificateVo> saleList = supplierCommonService.getTraderCertificateVos(traderCertificate);
        if (!CollectionUtils.isEmpty(saleList)) {
            mv.addObject("saleAuth", saleList.get(0));
        }

        //随货同行模板
        traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_896);
        List<TraderCertificateVo> goodWithTemList = supplierCommonService.getTraderCertificateVos(traderCertificate);
        if (!CollectionUtils.isEmpty(goodWithTemList)) {
            mv.addObject("goodWithTemList", goodWithTemList);
        }

        //质量保证协议
        traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_897);
        List<TraderCertificateVo> qualityAssuranceList = supplierCommonService.getTraderCertificateVos(traderCertificate);
        if (!CollectionUtils.isEmpty(qualityAssuranceList)) {
            mv.addObject("qualityAssuranceList", qualityAssuranceList);
            mv.addObject("qualityAssurance", CollectionUtils.isEmpty(qualityAssuranceList) ? null: qualityAssuranceList.get(0));
        }

        //售后服务承诺书
        traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_898);
        List<TraderCertificateVo> afterSalesBookList = supplierCommonService.getTraderCertificateVos(traderCertificate);
        if (!CollectionUtils.isEmpty(afterSalesBookList)) {
            mv.addObject("afterSalesBookList", afterSalesBookList);
            mv.addObject("afterSalesBook", CollectionUtils.isEmpty(afterSalesBookList) ? null: afterSalesBookList.get(0));
        }


//        initEditAptitudePart(mv, traderSupplier);
        mv.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(traderBaseInfo)));

        mv.setViewName("todolist/supplier_base_info");
        return mv;
    }

    /**
     * 设置地区信息
     *
     * @param mv
     * @param areaId
     * @param warehouseProvinceRegion
     * @param warehouseCityList
     * @param warehouseCityRegion
     * @param warehouseZoneList
     * @param warehouseZoneRegion
     * @param warehouseCountryRegion
     */
    private void setRegionsInfo(ModelAndView mv, Integer areaId, String warehouseProvinceRegion,
                                String warehouseCityList, String warehouseCityRegion, String warehouseZoneList,
                                String warehouseZoneRegion, String warehouseCountryRegion) {
        List<Region> regionList = (List<Region>) regionService.getRegion(areaId, 1);
        if (!StringUtils.isEmpty(regionList)) {
            for (Region r : regionList) {
                switch (r.getRegionType()) {
                    case 1:
                        List<Region> cityList = regionService.getRegionByParentId(r.getRegionId());
                        mv.addObject(warehouseProvinceRegion, r);
                        mv.addObject(warehouseCityList, cityList);
                        break;
                    case 2:
                        List<Region> zoneList = regionService.getRegionByParentId(r.getRegionId());
                        mv.addObject(warehouseCityRegion, r);
                        mv.addObject(warehouseZoneList, zoneList);
                        break;
                    case 3:
                        mv.addObject(warehouseZoneRegion, r);
                        break;
                    default:
                        mv.addObject(warehouseCountryRegion, r);
                        break;
                }
            }
        }
    }

    /**
     * 新国标一级分类
     *
     * @param mv
     */
    private void setNewStandCategoryList(ModelAndView mv) {
        // 参数
        Map<String, Object> paramMap = new HashMap<>(4);
        // 启用状态
        paramMap.put("status", CommonConstants.STATUS_1);
        // 一级类别
        paramMap.put("parentId", STANDARD_PARRNT_ID_0);

        // 新国标分类信息
        List<Map<String, Object>> newStandCategoryList = firstEngageService.getNewStandardCategory(paramMap);
        mv.addObject("newStandCategoryList", newStandCategoryList);
    }


    /**
     * <b>Description:</b>初始化编辑供应商资质部分<br>
     *
     * @param
     * @return
     * @Note <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/4/2
     */
    private void initEditAptitudePart(ModelAndView mav, TraderSupplier traderSupplier) {
        TraderCertificateVo traderCertificate = new TraderCertificateVo();
        traderCertificate.setTraderId(traderSupplier.getTraderId());
        traderCertificate.setTraderType(ErpConst.TWO);
        mav.addObject("traderSupplier1", traderSupplier);
        //Page page = Page.newBuilder(null, null, null);

        if(traderSupplier.getTraderId()==null){
            log.error("checkInvoiceParams::traderId{}",traderSupplier.getTraderId());
        }
        Map<String, Object> map = traderCustomerService.getFinanceAndAptitudeByTraderId(traderCertificate, "zz");
        TraderCertificateVo tc = null;
        StringBuffer sb = new StringBuffer();

        // 营业执照信息
        List<TraderCertificateVo> bus = null;
        // 营业执照信息
        if (map.containsKey("business")) {
//			JSONObject json=JSONObject.fromObject(map.get("business"));
//			bus=(TraderCertificateVo) JSONObject.toBean(json, TraderCertificateVo.class);
//			mav.addObject("business", bus);
            bus = (List<TraderCertificateVo>) map.get("business");
            if (!CollectionUtils.isEmpty(bus)) {
                mav.addObject("business", bus.get(0));
            }
        }
        // 税务登记信息
        if (map.containsKey("tax")) {
            JSONObject json = JSONObject.fromObject(map.get("tax"));
            tc = (TraderCertificateVo) JSONObject.toBean(json, TraderCertificateVo.class);
            mav.addObject("tax", tc);
        }
        // 组织机构信息
        if (map.containsKey("orga")) {
            JSONObject json = JSONObject.fromObject(map.get("orga"));
            tc = (TraderCertificateVo) JSONObject.toBean(json, TraderCertificateVo.class);
            mav.addObject("orga", tc);

        }
        // 二类医疗资质
        List<TraderCertificateVo> twoMedicalList = null;
        if (map.containsKey("twoMedical")) {
            twoMedicalList = (List<TraderCertificateVo>) map.get("twoMedical");
            mav.addObject("twoMedicalList", twoMedicalList);
            mav.addObject("twoMedical", CollectionUtils.isEmpty(twoMedicalList) ? null: twoMedicalList.get(0));
        }
        // 三类医疗资质
        if (map.containsKey("threeMedical")) {
//			JSONObject json=JSONObject.fromObject(map.get("threeMedical"));
//			tc=(TraderCertificateVo) JSONObject.toBean(json, TraderCertificateVo.class);
//			mav.addObject("threeMedical", tc);
//			sb=sb.append(JsonUtils.translateToJson(tc));

            List<TraderCertificateVo> threeMedical = (List<TraderCertificateVo>) map.get("threeMedical");
            if (!CollectionUtils.isEmpty(threeMedical)) {
                mav.addObject("threeMedical", threeMedical.get(0));
            }
        }
        List<TraderMedicalCategoryVo> two = null;
        if (map.containsKey("two")) {
            JSONArray jsonArray = JSONArray.fromObject(map.get("two"));
            two = (List<TraderMedicalCategoryVo>) JSONArray.toCollection(jsonArray, TraderMedicalCategoryVo.class);
            mav.addObject("two", two);
        }
        List<TraderMedicalCategoryVo> three = null;
        if (map.containsKey("three")) {
            JSONArray jsonArray = JSONArray.fromObject(map.get("three"));
            three = (List<TraderMedicalCategoryVo>) JSONArray.toCollection(jsonArray, TraderMedicalCategoryVo.class);
            mav.addObject("three", three);
        }
        List<TraderMedicalCategoryVo> newTwo = null;
        if (map.containsKey("newTwo")) {
            JSONArray jsonArray = JSONArray.fromObject(map.get("newTwo"));
            newTwo = (List<TraderMedicalCategoryVo>) JSONArray.toCollection(jsonArray, TraderMedicalCategoryVo.class);
            mav.addObject("newTwo", newTwo);
        }
        List<TraderMedicalCategoryVo> newThree = null;
        if (map.containsKey("newThree")) {
            JSONArray jsonArray = JSONArray.fromObject(map.get("newThree"));
            newThree = (List<TraderMedicalCategoryVo>) JSONArray.toCollection(jsonArray, TraderMedicalCategoryVo.class);
            mav.addObject("newThree", newThree);
        }
        // 医疗器械生产许可证
        TraderCertificateVo product = null;
        if (map.containsKey("product")) {
            JSONObject json = JSONObject.fromObject(map.get("product"));
            product = (TraderCertificateVo) JSONObject.toBean(json, TraderCertificateVo.class);
            mav.addObject("product", product);
        }

        // 医疗器械经营许可证
        TraderCertificateVo operate = null;
        if (map.containsKey("operate")) {
            JSONObject json = JSONObject.fromObject(map.get("operate"));
            operate = (TraderCertificateVo) JSONObject.toBean(json, TraderCertificateVo.class);
            mav.addObject("operate", operate);
        }
        // 销售授权书 与 授权销售人
        TraderCertificateVo saleAuth = null;
        if (map.containsKey("saleAuth")) {
            JSONObject json = JSONObject.fromObject(map.get("saleAuth"));
            saleAuth = (TraderCertificateVo) JSONObject.toBean(json, TraderCertificateVo.class);
            mav.addObject("saleAuth", saleAuth);
        }

        // 第一类医疗器械生产备案凭证
        TraderCertificateVo firstCategoryCertificate = null;
        if (map.containsKey("firstCategoryCertificate")) {
            JSONObject json = JSONObject.fromObject(map.get("firstCategoryCertificate"));
            firstCategoryCertificate = (TraderCertificateVo) JSONObject.toBean(json, TraderCertificateVo.class);
            mav.addObject("firstCategoryCertificate", firstCategoryCertificate);
        }

        // 生产企业生产产品登记表
        TraderCertificateVo productRegistration = null;
        if (map.containsKey("productRegistration")) {
            JSONObject json = JSONObject.fromObject(map.get("productRegistration"));
            productRegistration = (TraderCertificateVo) JSONObject.toBean(json, TraderCertificateVo.class);
            mav.addObject("productRegistration", productRegistration);
        }

        // 品牌授权书
        List<TraderCertificateVo> brandBookList = null;
        if (map.containsKey("brandBookList")) {
            brandBookList = (List<TraderCertificateVo>) map.get("brandBookList");
            mav.addObject("brandBookList", brandBookList);
        }

        // 随货同行模板
        List<TraderCertificateVo> goodWithTemList = null;
        if (map.containsKey("goodWithTemList")) {
            goodWithTemList = (List<TraderCertificateVo>) map.get("goodWithTemList");
            mav.addObject("goodWithTemList", goodWithTemList);
        }

        // 质量保证协议
        List<TraderCertificateVo> qualityAssuranceList = null;
        if (map.containsKey("qualityAssuranceList")) {
            qualityAssuranceList = (List<TraderCertificateVo>) map.get("qualityAssuranceList");
            mav.addObject("qualityAssuranceList", qualityAssuranceList);
        }

        // 售后服务承诺书
        List<TraderCertificateVo> afterSalesBookList = null;
        if (map.containsKey("afterSalesBookList")) {
            afterSalesBookList = (List<TraderCertificateVo>) map.get("afterSalesBookList");
            mav.addObject("afterSalesBookList", afterSalesBookList);
        }

        // 质量体系调查表或合格供应商档案
        List<TraderCertificateVo> qualityAndTraderList = null;
        if (map.containsKey("qualityAndTraderList")) {
            qualityAndTraderList = (List<TraderCertificateVo>) map.get("qualityAndTraderList");
            mav.addObject("qualityAndTraderList", qualityAndTraderList);
        }

        //其他证书
        List<TraderCertificateVo> otherList = null;
        if (map.containsKey("otherList")) {
            otherList = (List<TraderCertificateVo>) map.get("otherList");
            mav.addObject("otherList", otherList);
        }
        // 医疗类别
        List<SysOptionDefinition> medicalTypes = getSysOptionDefinitionList(SysOptionConstant.ID_20);

        mav.addObject("medicalTypes", medicalTypes);
        // 医疗类别级别
        List<SysOptionDefinition> medicalTypLevels = getSysOptionDefinitionList(SysOptionConstant.ID_21);

        mav.addObject("medicalTypLevels", medicalTypLevels);
        mav.addObject("domain", domain);
    }

    /**
     * <b>Description:</b><br>
     * 将修改前的数据保存进redis返回key
     *
     * @param beforeParams
     * @return
     * @Note <b>Author:</b> east <br>
     *       <b>Date:</b> 2017年9月25日 下午1:49:23
     */
    protected String saveBeforeParamToRedis(String beforeParams) {
        if (beforeParams == null) {
            beforeParams = "";
        }
        String redisKey = dbType + "beforeParams:" + UUID.randomUUID().toString();
        JedisUtils.set(redisKey, beforeParams, ErpConst.REDIS_USERID_SESSIONID_TIMEOUT);
        return redisKey;
    }

    /**
     * <b>Description:</b><br>
     * 获取字典表中的集合
     *

     * @return
     * @Note <b>Author:</b> east <br>
     *       <b>Date:</b> 2017年7月27日 下午4:32:06
     */
    @SuppressWarnings("unchecked")
    protected List<SysOptionDefinition> getSysOptionDefinitionList(Integer parentId) {
        // modify by franlin.wu
        // for[针对只判断redis缓存的key是否存在来获取数字字典值,可能存在从redis缓存获取为null的场景,做以下修改] at
        // 2018-11-23 begin
        // 数字字典list
        List<SysOptionDefinition> resultList = null;
        if (JedisUtils.exists(dbType + ErpConst.KEY_PREFIX_DATA_DICTIONARY_LIST + parentId)) {
            String jsonStr = JedisUtils.get(dbType + ErpConst.KEY_PREFIX_DATA_DICTIONARY_LIST + parentId);
            // 避免json为空或null的字符串或[null]的字符串
            if (org.apache.commons.lang3.StringUtils.isNotBlank(jsonStr) && !"null".equalsIgnoreCase(jsonStr)
                    && !"[null]".equalsIgnoreCase(jsonStr)) {
                JSONArray json = JSONArray.fromObject(jsonStr);
                resultList = (List<SysOptionDefinition>) JSONArray.toCollection(json, SysOptionDefinition.class);
            }
        }
        // 从redis中获取为null，则从库中查询
        if (org.apache.commons.collections.CollectionUtils.isEmpty(resultList)) {
            // 调用根据parendId获取数字字典子list
            resultList = baseService.getSysOptionDefinitionListByParentId(parentId);
        }
        return resultList;
        // modify by franlin.wu
        // for[针对只判断redis缓存的key是否存在来获取数字字典值,可能存在从redis缓存获取为null的场景,做以下修改] at
        // 2018-11-23 end

    }


}
