package com.vedeng.erp.kingdee.batch.common.reader;

import cn.hutool.core.bean.BeanUtil;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.batch.MyBatisPagingItemReader;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 自定义金蝶查询器
 * @date 2022/11/17 10:01
 */
public class CommonMybatisItemReader<T> extends MyBatisPagingItemReader<T> {

    private final static int PAGE_SIZE =1000;//由100调整为1000


    public CommonMybatisItemReader(SqlSessionFactory sqlSessionFactory, String name) {
        setSqlSessionFactory(sqlSessionFactory);
        setQueryId("com.vedeng.erp.kingdee.batch.repository." + name + "Mapper.findByAll");
        setPageSize(PAGE_SIZE);
    }

    public CommonMybatisItemReader(SqlSessionFactory sqlSessionFactory, String name, T t) {
        setSqlSessionFactory(sqlSessionFactory);
        setQueryId("com.vedeng.erp.kingdee.batch.repository." + name + "Mapper.findByAll");
        setPageSize(PAGE_SIZE);
        setParameterValues(BeanUtil.beanToMap(t, false, true));
    }

    public CommonMybatisItemReader(SqlSessionFactory sqlSessionFactory, String name, String method,T t) {
        setSqlSessionFactory(sqlSessionFactory);
        setQueryId("com.vedeng.erp.kingdee.batch.repository." + name + "Mapper." + method);
        setPageSize(PAGE_SIZE);
        setParameterValues(BeanUtil.beanToMap(t, false, true));
    }
}
