<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.erp.system.mapper.ChangeLogMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.erp.system.domain.dto.ChangeLog" >
    <!--          -->
    <id column="CHANGE_LOG_ID" property="changeLogId" jdbcType="INTEGER" />
    <result column="RELATED_TABLE" property="relatedTable" jdbcType="VARCHAR" />
    <result column="RELATED_ID" property="relatedId" jdbcType="INTEGER" />
    <result column="LOG_MESSAGE" property="logMessage" jdbcType="VARCHAR" />
    <result column="ADD_TIME" property="addTime" jdbcType="TIMESTAMP" />
    <result column="MODE_TIME" property="modeTime" jdbcType="TIMESTAMP" />
    <result column="IS_DELETE" property="isDelete" jdbcType="BIT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--          -->
    CHANGE_LOG_ID, RELATED_TABLE, RELATED_ID, LOG_MESSAGE, ADD_TIME, MODE_TIME, IS_DELETE,
    CREATOR, UPDATER
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--          -->
    select
    <include refid="Base_Column_List" />
    from V_CHANGE_LOG
    where CHANGE_LOG_ID = #{changeLogId,jdbcType=INTEGER}
  </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--          -->
    delete from V_CHANGE_LOG
    where CHANGE_LOG_ID = #{changeLogId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.erp.system.domain.dto.ChangeLog" >
    <!--          -->
    insert into V_CHANGE_LOG (CHANGE_LOG_ID, RELATED_TABLE, RELATED_ID,
      LOG_MESSAGE, ADD_TIME, MODE_TIME,
      IS_DELETE, CREATOR, UPDATER
      )
    values (#{changeLogId,jdbcType=INTEGER}, #{relatedTable,jdbcType=VARCHAR}, #{relatedId,jdbcType=INTEGER},
      #{logMessage,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, #{modeTime,jdbcType=TIMESTAMP},
      #{isDelete,jdbcType=BIT}, #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.erp.system.domain.dto.ChangeLog" >
    <!--          -->
    insert into V_CHANGE_LOG
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="changeLogId != null" >
        CHANGE_LOG_ID,
      </if>
      <if test="relatedTable != null" >
        RELATED_TABLE,
      </if>
      <if test="relatedId != null" >
        RELATED_ID,
      </if>
      <if test="logMessage != null" >
        LOG_MESSAGE,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="modeTime != null" >
        MODE_TIME,
      </if>
      <if test="isDelete != null" >
        IS_DELETE,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="changeLogId != null" >
        #{changeLogId,jdbcType=INTEGER},
      </if>
      <if test="relatedTable != null" >
        #{relatedTable,jdbcType=VARCHAR},
      </if>
      <if test="relatedId != null" >
        #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="logMessage != null" >
        #{logMessage,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modeTime != null" >
        #{modeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null" >
        #{isDelete,jdbcType=BIT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.system.domain.dto.ChangeLog" >
    <!--          -->
    update V_CHANGE_LOG
    <set >
      <if test="relatedTable != null" >
        RELATED_TABLE = #{relatedTable,jdbcType=VARCHAR},
      </if>
      <if test="relatedId != null" >
        RELATED_ID = #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="logMessage != null" >
        LOG_MESSAGE = #{logMessage,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modeTime != null" >
        MODE_TIME = #{modeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null" >
        IS_DELETE = #{isDelete,jdbcType=BIT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where CHANGE_LOG_ID = #{changeLogId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.system.domain.dto.ChangeLog" >
    <!--          -->
    update V_CHANGE_LOG
    set RELATED_TABLE = #{relatedTable,jdbcType=VARCHAR},
      RELATED_ID = #{relatedId,jdbcType=INTEGER},
      LOG_MESSAGE = #{logMessage,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MODE_TIME = #{modeTime,jdbcType=TIMESTAMP},
      IS_DELETE = #{isDelete,jdbcType=BIT},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER}
    where CHANGE_LOG_ID = #{changeLogId,jdbcType=INTEGER}
  </update>

  <select id="getInfo" resultType="com.vedeng.erp.system.dto.ChangeLogDto">
    SELECT
      A.*,
      B.USERNAME creatorName
    FROM
      V_CHANGE_LOG A
        LEFT JOIN T_USER B ON A.CREATOR = B.USER_ID
    WHERE
      A.IS_DELETE = 0
      AND A.RELATED_ID = #{relatedId}
      AND A.RELATED_TABLE = #{realtedTable}
  </select>

</mapper>
