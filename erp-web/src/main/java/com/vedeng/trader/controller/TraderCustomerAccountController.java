package com.vedeng.trader.controller;

import com.vedeng.common.controller.BaseController;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.erp.finance.dto.CustomerAccountReqDto;
import com.vedeng.erp.finance.facade.CustomerAccountFacade;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

@Controller
@RequestMapping(value = "/trader/account")
public class TraderCustomerAccountController extends BaseController {

    @Resource
    private CustomerAccountFacade customerAccountFacade;
    @ResponseBody
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public ResultInfo create(@RequestBody CustomerAccountReqDto customerAccountReqDto) throws Exception {
        customerAccountFacade.tryCreateCustomerAccount(customerAccountReqDto.getAfterSalesId());
        return ResultInfo.success();
    }
}
