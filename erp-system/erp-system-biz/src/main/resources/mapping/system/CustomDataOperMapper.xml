<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.system.mapper.CustomDataOperMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.system.domain.entity.CustomDataOperEntity">
        <!--@mbg.generated-->
        <!--@Table T_CUSTOM_DATA_OPER-->
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="RELATED_ID" jdbcType="INTEGER" property="relatedId"/>
        <result column="BIZ_TYPE" jdbcType="INTEGER" property="bizType"/>
        <result column="OPER_TYPE" jdbcType="INTEGER" property="operType"/>
        <result column="OPER_TIME" jdbcType="TIMESTAMP" property="operTime"/>
        <result column="BELONGER_ID" jdbcType="INTEGER" property="belongerId"/>
        <result column="BELONGER" jdbcType="VARCHAR" property="belonger"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, RELATED_ID, BIZ_TYPE, OPER_TYPE, OPER_TIME,
        BELONGER_ID, BELONGER
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from T_CUSTOM_DATA_OPER
        where ID = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete
        from T_CUSTOM_DATA_OPER
        where ID = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="ID" keyProperty="id"
            parameterType="com.vedeng.erp.system.domain.entity.CustomDataOperEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_CUSTOM_DATA_OPER (ADD_TIME, MOD_TIME, CREATOR,
                                        UPDATER, RELATED_ID, BIZ_TYPE,
                                        OPER_TYPE, OPER_TIME, BELONGER_ID,
                                        BELONGER)
        values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER},
                #{updater,jdbcType=INTEGER}, #{relatedId,jdbcType=INTEGER}, #{bizType,jdbcType=INTEGER},
                #{operType,jdbcType=INTEGER}, #{operTime,jdbcType=TIMESTAMP}, #{belongerId,jdbcType=INTEGER},
                #{belonger,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="ID" keyProperty="id"
            parameterType="com.vedeng.erp.system.domain.entity.CustomDataOperEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_CUSTOM_DATA_OPER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="relatedId != null">
                RELATED_ID,
            </if>
            <if test="bizType != null">
                BIZ_TYPE,
            </if>
            <if test="operType != null">
                OPER_TYPE,
            </if>
            <if test="operTime != null">
                OPER_TIME,
            </if>
            <if test="belongerId != null">
                BELONGER_ID,
            </if>
            <if test="belonger != null">
                BELONGER,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="relatedId != null">
                #{relatedId,jdbcType=INTEGER},
            </if>
            <if test="bizType != null">
                #{bizType,jdbcType=INTEGER},
            </if>
            <if test="operType != null">
                #{operType,jdbcType=INTEGER},
            </if>
            <if test="operTime != null">
                #{operTime,jdbcType=TIMESTAMP},
            </if>
            <if test="belongerId != null">
                #{belongerId,jdbcType=INTEGER},
            </if>
            <if test="belonger != null">
                #{belonger,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.system.domain.entity.CustomDataOperEntity">
        <!--@mbg.generated-->
        update T_CUSTOM_DATA_OPER
        <set>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="relatedId != null">
                RELATED_ID = #{relatedId,jdbcType=INTEGER},
            </if>
            <if test="bizType != null">
                BIZ_TYPE = #{bizType,jdbcType=INTEGER},
            </if>
            <if test="operType != null">
                OPER_TYPE = #{operType,jdbcType=INTEGER},
            </if>
            <if test="operTime != null">
                OPER_TIME = #{operTime,jdbcType=TIMESTAMP},
            </if>
            <if test="belongerId != null">
                BELONGER_ID = #{belongerId,jdbcType=INTEGER},
            </if>
            <if test="belonger != null">
                BELONGER = #{belonger,jdbcType=VARCHAR},
            </if>
        </set>
        where ID = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.system.domain.entity.CustomDataOperEntity">
        <!--@mbg.generated-->
        update T_CUSTOM_DATA_OPER
        set ADD_TIME    = #{addTime,jdbcType=TIMESTAMP},
            MOD_TIME    = #{modTime,jdbcType=TIMESTAMP},
            CREATOR     = #{creator,jdbcType=INTEGER},
            UPDATER     = #{updater,jdbcType=INTEGER},
            RELATED_ID  = #{relatedId,jdbcType=INTEGER},
            BIZ_TYPE    = #{bizType,jdbcType=INTEGER},
            OPER_TYPE   = #{operType,jdbcType=INTEGER},
            OPER_TIME   = #{operTime,jdbcType=TIMESTAMP},
            BELONGER_ID = #{belongerId,jdbcType=INTEGER},
            BELONGER    = #{belonger,jdbcType=VARCHAR}
        where ID = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update T_CUSTOM_DATA_OPER
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="ADD_TIME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.addTime != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="MOD_TIME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.modTime != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CREATOR = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.creator != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="UPDATER = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updater != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="RELATED_ID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.relatedId != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.relatedId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="BIZ_TYPE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.bizType != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.bizType,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="OPER_TYPE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.operType != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.operType,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="OPER_TIME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.operTime != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.operTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="BELONGER_ID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.belongerId != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.belongerId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="BELONGER = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.belonger != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.belonger,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
        </trim>
        where ID in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=INTEGER}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="ID" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_CUSTOM_DATA_OPER
        (ADD_TIME, MOD_TIME, CREATOR, UPDATER, RELATED_ID, BIZ_TYPE, OPER_TYPE, OPER_TIME,
         BELONGER_ID, BELONGER)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER},
             #{item.updater,jdbcType=INTEGER}, #{item.relatedId,jdbcType=INTEGER}, #{item.bizType,jdbcType=INTEGER},
             #{item.operType,jdbcType=INTEGER}, #{item.operTime,jdbcType=TIMESTAMP},
             #{item.belongerId,jdbcType=INTEGER},
             #{item.belonger,jdbcType=VARCHAR})
        </foreach>
    </insert>

<!--auto generated by MybatisCodeHelper on 2022-07-21-->
    <select id="findByAll" resultType="com.vedeng.erp.system.dto.CustomDataOperDto">
        select
        <include refid="Base_Column_List"/>
        from T_CUSTOM_DATA_OPER
        <where>
            <if test="relatedId != null">
                and RELATED_ID=#{relatedId,jdbcType=INTEGER}
            </if>
            <if test="bizType != null">
                and BIZ_TYPE=#{bizType,jdbcType=INTEGER}
            </if>
            <if test="operType != null">
                and OPER_TYPE=#{operType,jdbcType=INTEGER}
            </if>
            <if test="operTime != null">
                and OPER_TIME=#{operTime,jdbcType=TIMESTAMP}
            </if>
            <if test="belongerId != null">
                and BELONGER_ID=#{belongerId,jdbcType=INTEGER}
            </if>
            <if test="belonger != null">
                and BELONGER=#{belonger,jdbcType=VARCHAR}
            </if>
            <if test="id != null">
                and ID=#{id,jdbcType=INTEGER}
            </if>
            <if test="addTime != null">
                and ADD_TIME=#{addTime,jdbcType=TIMESTAMP}
            </if>
            <if test="modTime != null">
                and MOD_TIME=#{modTime,jdbcType=TIMESTAMP}
            </if>
            <if test="creator != null">
                and CREATOR=#{creator,jdbcType=INTEGER}
            </if>
            <if test="updater != null">
                and UPDATER=#{updater,jdbcType=INTEGER}
            </if>
        </where>
    </select>
</mapper>