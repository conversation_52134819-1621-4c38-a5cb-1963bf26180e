package com.wms.service.other;

import com.alibaba.fastjson.JSON;
import com.ezadmin.common.utils.Utils;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.model.OssInfo;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.common.util.StringUtil;
import com.vedeng.ftpclient.client.FTPClientHelper;
import com.vedeng.ftpclient.config.FTPAbandonedConfig;
import com.vedeng.ftpclient.config.FtpPoolConfig;
import com.vedeng.ftpclient.core.FTPClientFactory;
import com.vedeng.ftpclient.core.FTPClientPool;
import com.vedeng.ftpclient.util.ByteUtil;
import com.vedeng.ftpclient.util.FtpConstant;
import com.vedeng.logistics.dao.ext.WmsWarehouseGoodsOperateLogDataExtMapper;
import com.vedeng.system.dao.AttachmentMapper;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.service.impl.OssUtilsServiceImpl;
import com.wms.dto.OutputDto;
import com.wms.dto.OutputGoodDto;
import com.wms.service.WmsImagesService;
import com.wms.service.util.WMSFTPUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import top.ezadmin.common.utils.StringUtils;
import top.ezadmin.dao.Dao;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName WmsImagesServiceImpl.java
 * @Description TODO Wms图片
 * @createTime 2021年01月11日 13:11:00
 */
@Service
public class WmsImagesServiceImpl implements WmsImagesService {

    Logger logger= LoggerFactory.getLogger(WmsImagesServiceImpl.class);
    @Autowired
    private OssUtilsServiceImpl ossUtilsService;

    @Resource
    private AttachmentMapper attachmentMapper;

    @Autowired
    private WMSFTPUtil wmsftpUtil;

    /**
     * @description: 保存质检报告
     * @return:
     * @author: Strange
     * @date: 2021/1/11
     **/
    @Override
    public void dealReportImage(OutputDto requestBean, Integer attachmentFuntion) {
        logger.info("保存质检报告 start requestBean:{}", JSON.toJSONString(requestBean));

        long currentTimeMillis = System.currentTimeMillis();
        Attachment search = new Attachment();
        List<OutputGoodDto> details = requestBean.getDetails();
        for (OutputGoodDto detail : details) {
            if(StringUtil.isBlank(detail.getUserDefine2())){
                continue;
            }
            Integer saleorderGoodsId = Integer.valueOf(detail.getUserDefine1());
            String[] paths = detail.getUserDefine2().split(";");
            for (String path : paths) {

                int index = path.lastIndexOf("/", path.length())+1;
                String filePath = path.substring(0, index);
                String fileName = path.substring(index, path.length());

                Attachment insert = new Attachment();
                insert.setCreator(1);
                insert.setRelatedId(saleorderGoodsId);
                insert.setAddTime(currentTimeMillis);
                //批号
                insert.setAlt(detail.getLotAtt04());
                insert.setName(fileName);
                insert.setAttachmentType(SysOptionConstant.QUALITY_REPORT);
                insert.setAttachmentFunction(attachmentFuntion);

                search.setOriginalFilepath(path);
                search.setAttachmentType(SysOptionConstant.QUALITY_REPORT);
                //是否以保存了这个路径的图片
                Attachment attachment = attachmentMapper.getAttachmentsByOriginalFilepath(search);
                logger.info("检索质检报告 出库单:{},attachment:{}", requestBean.getSOReference1(),  JSON.toJSONString(attachment));

                if(attachment != null){
                    insert.setUri(attachment.getUri());
                    insert.setDomain(attachment.getDomain());
                    insert.setOriginalFilepath(path);
                    insert.setOssResourceId(attachment.getOssResourceId());
                    attachmentMapper.insertSelective(insert);
                }else{
                    FTPClient ftpClient = wmsftpUtil.getFTPClient();
                    InputStream inputStream = wmsftpUtil.downLoadFile(ftpClient,filePath,fileName);
                    OssInfo ossInfo = ossUtilsService.sendFile2Oss(path, inputStream,true);
                    wmsftpUtil.closeFTP(ftpClient);
                    if(ossInfo != null && ossInfo.getCode().equals(0)){
                        insert.setDomain(ossInfo.getDomain());
                        insert.setUri(ossInfo.getUri());
                        insert.setOriginalFilepath(path);
                        insert.setOssResourceId(ossInfo.getResourceId());
                        attachmentMapper.insertSelective(insert);
                    } else {
                        logger.info("文件发送发到oss失败 requestBean:{}", JSON.toJSONString(requestBean));
                    }
                }
            }
        }
    }
    @Autowired
    @Qualifier("wmsDataSourceTarget")
    DataSource wmsDatasource;

    @Autowired
    @Qualifier("wmsFtpClientHelper")
    private FTPClientHelper wmsFtpClientHelper;

    @Value("${wms.oracle.username:WMS_USER_TEST}")
    String wmsOracleUser;
    public String  doReportBySkuAndBatchNum(String skuNo,String batchNum,String lastestReport,String lastestReportOss){
        List<Map<String,Object>> obj = null;
        try {
            logger.info(" skuNo:{} batchNum:{}  ",skuNo,batchNum );
            obj = Dao.getInstance().executeQuery(wmsDatasource,StringUtils.repaceAll(ORACLE_SQL,"#USER_NAME#",wmsOracleUser),
                    new Object[]{skuNo,batchNum});
            List<Map<String, String>> result = new ArrayList<>();
            for (Map<String, Object> originalMap : obj) {
                // 获取添加时间和文件名
                String addTimeStr = String.valueOf(originalMap.get("ADDTIMESTR"));
                String fileName = String.valueOf(originalMap.get("QCREPORTFILENAME"));
                // 跳过空值
                if (addTimeStr == null || "null".equals(addTimeStr)|| "".equals(addTimeStr) ||
                        fileName == null || "null".equals(fileName)||"".equals(fileName)) {
                    continue;
                }
                // 创建新的Map并添加到结果集
                Map<String, String> newMap = new HashMap<>();
                newMap.put(addTimeStr, fileName);
                result.add(newMap);
            }
            String finalFiles=ReportSelector.selectReport(result);
            logger.info("找到检测报告 skuNo:{} batchNum:{}  finalFiles:{}",skuNo,batchNum,finalFiles );

            //判断 库里面是否已经有这个检测报告了
            if(StringUtils.isNotBlank(lastestReport)
                    &&StringUtils.isNotBlank(finalFiles)
                    &&StringUtils.isNotBlank(lastestReportOss)
                    &&StringUtils.equals(finalFiles,lastestReport)){
                return lastestReportOss+"@@"+lastestReport;
            }
            String[] picUrlArray=StringUtils.split(finalFiles,";");
            if(ArrayUtils.isEmpty(picUrlArray)){
                return "";
            }
            //生成pdf的inputstream  并上传到oss
            List<byte[]> blist=new ArrayList<>();
            for (int j = 0; j < picUrlArray.length; j++) {
                String picUrl=picUrlArray[j];
                if(StringUtils.isNotBlank(picUrl)){
                    try {
                        String encodedFileName = URLEncoder.encode(picUrl, StandardCharsets.UTF_8.toString());
                        if(wmsFtpClientHelper.verifyIfExist(picUrl)){
                            byte[] b =  wmsFtpClientHelper.downloadFileIntoByte(picUrl);
                            blist.add(b);
                        }
                    }catch (Exception e){
                        logger.warn("检测报告找不到文件 {} {}",batchNum,picUrl,e);
                    }
                }
            }
            if(CollectionUtils.isNotEmpty(blist)){
                try {
                    InputStream pdf = PdfGenerator.generatePdfFromImages(blist);
//                    if(pdf==null){
//                        logger.info("生成检测报告PDF失败 {} from {} 尝试使用图片 ",batchNum, picUrlArray);
//                    }
                    OssInfo ossInfo = ossUtilsService.sendFile2Oss(skuNo + batchNum + System.currentTimeMillis() + ".pdf", pdf, true);
                    if (ossInfo != null && ossInfo.getCode().equals(0)) {
                        logger.info("生成检测报告PDF成功 {} from {} to {}",batchNum, picUrlArray,ossInfo.getUri());
                        return ossInfo.getUri()+"@@"+finalFiles;
                    }
                }catch (Exception e){
                    logger.info("生成检测报告PDF失败 {} from {}",batchNum, picUrlArray);
                    return " "+"@@"+finalFiles;
                }
            }
        } catch (Exception e) {
            logger.error("",e);
            return "";
        }
        return "";
    }






    private String ORACLE_SQL="select  distinct  rtrim('CUSTOMERID=' || cast( a.CUSTOMERID as varchar2(100) )\n" +
            "  )  || ',' ||  rtrim('SKU=' || cast(  a.SKU as varchar2(100) ))  || ',' ||\n" +
            "  rtrim('LOTATT04=' || cast(  a.LOTATT04 as varchar2(100) ))  || ',' ||\n" +
            "  rtrim('LOTATT05=' || cast(  a.LOTATT05 as varchar2(100) ))  PKEY ,\n" +
            "  a.CUSTOMERID, a.SKU, b.DESCR_C, b.DESCR_E, b.ALTERNATE_SKU1, a.LOTATT04,\n" +
            "  a.LOTATT05, a.QCREPORTFILENAME,   b.RESERVEDFIELD01, b.MEDICALTYPE,\n" +
            "  b.APPROVALNO, a.LOTNUM, c.Descr_C as SUPPLIERNAME  ,b.sku_group6 as\n" +
            "  CONFIGLIST01,b.sku_group7 as CONFIGLIST02,a.LOTATT06 as CONFIGLIST03,\n" +
            "  a.LOTATT01 as CONFIGLIST04,a.LOTATT02 as CONFIGLIST05,a.LOTATT03 as\n" +
            "  CONFIGLIST06,b.sku_group3 as CONFIGLIST07,b.Alternate_SKU3 as CONFIGLIST08,\n" +
            "  b.PrintMedicineQcReport as CONFIGLIST09\n,a.ADDTIME,TO_CHAR(a.ADDTIME, 'YYYYMMDDHH24MISS') ADDTIMESTR " +
            " from\n" +
            " #USER_NAME#.INV_LOT_ATT a left join  #USER_NAME#.BAS_SKU b on a.CustomerID = b.CustomerID and a.SKU =\n" +
            "   b.SKU left join  #USER_NAME#.BAS_Customer c on a.LotATT05=c.CustomerID and\n" +
            "  c.Customer_Type='VE' where   a.SKU =  ?   and a.LOTATT04 =\n" +
            "   ?   and a.QCREPORTFILENAME is not null  and\n" +
            "  length(a.QCREPORTFILENAME) > 0  ORDER BY a.ADDTIME DESC ";
}
