package com.vedeng.erp.wms.mapper;

import com.vedeng.erp.wms.domain.entity.BuyOrderAfterSaleDirectOutLogEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BuyOrderAfterSaleDirectOutLogMapper {
    /**
     * delete by primary key
     * @param afterSaleBuyorderDirectOutLogId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer afterSaleBuyorderDirectOutLogId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(BuyOrderAfterSaleDirectOutLogEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(BuyOrderAfterSaleDirectOutLogEntity record);

    /**
     * select by primary key
     * @param afterSaleBuyorderDirectOutLogId primary key
     * @return object by primary key
     */
    BuyOrderAfterSaleDirectOutLogEntity selectByPrimaryKey(Integer afterSaleBuyorderDirectOutLogId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(BuyOrderAfterSaleDirectOutLogEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(BuyOrderAfterSaleDirectOutLogEntity record);

    int updateBatch(List<BuyOrderAfterSaleDirectOutLogEntity> list);

    int batchInsert(@Param("list") List<BuyOrderAfterSaleDirectOutLogEntity> list);

    /**
     * 自定义查询
     * @param afterSaleBuyorderDirectOutLogEntity 实体
     * @return 对象列表
     */
    List<BuyOrderAfterSaleDirectOutLogEntity> findByAll(BuyOrderAfterSaleDirectOutLogEntity buyOrderAfterSaleDirectOutLogEntity);


}