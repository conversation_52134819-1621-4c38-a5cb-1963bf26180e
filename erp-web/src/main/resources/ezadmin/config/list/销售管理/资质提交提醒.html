<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>资质提交提醒</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="FhB3hcmqKHs" datasource="erp-reportdatasource" fixednumber="2" fixednumberright="0" append_column_url="" append_row_url="" empty_show="" firstcol="">
<div id="appendHead"></div>
<div class="layui-fluid">
    <div class="layui-tab">
        <ul class="layui-tab-title" id="tab">
        </ul>
    </div>
    <form class="layui-form" id="search">
        <div class=" layui-inline "><label class="layui-form-label">是否已读</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="21" name="IS_VIEW" placeholder="" style="" alias="b" jdbctype="NUMBER" data="[ {&quot;K&quot;:&quot;1&quot;,&quot;V&quot;:&quot;已读&quot;},{&quot;K&quot;:&quot;0&quot;,&quot;V&quot;:&quot;未读&quot;}]" datatype="JSON" oper="EQ" ></object>
            </div>
        </div>
    </form>
    <hr class="layui-border-blue">
    <div class="btn-group   bd-highlight" id="tableButton">
    </div>
    <table id="table" class="layui-table" style=" width:100%">
        <thead>
        <tr id="column">
            <th type="rowbutton" id="rowbutton"></th>
            <th item_name="MESSAGE_ID" name="MESSAGE_ID" body="td-text" order="" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">ID</th>
            <th item_name="TITLE" name="TITLE" body="td-text" order="" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">标题</th>
            <th item_name="CONTENT" name="CONTENT" body="td-link" order="0" url="${URL}" opentype="PARENT" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">内容</th>
            <th item_name="ADD_TIME" name="ADD_TIME" body="td-text" order="" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">添加时间</th>
            <th item_name="IS_VIEW" name="IS_VIEW" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[ {&quot;K&quot;:&quot;1&quot;,&quot;V&quot;:&quot;已读&quot;},{&quot;K&quot;:&quot;0&quot;,&quot;V&quot;:&quot;未读&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">是否已读</th>
            <th item_name="VIEW_TIME" name="VIEW_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">查看时间</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td> express:</td>
            <td colspan="100"><pre id="express" class="layui-code" orderby="order by a.message_id desc" groupby="">select  a.message_id ,a.title,a.content,url,FROM_UNIXTIME(a.add_time/1000)  add_time,
b.IS_VIEW*1 IS_VIEW ,a.CATEGORY,CASE WHEN (b.VIEW_TIME IS NULL OR b.VIEW_TIME=0) THEN ''
ELSE FROM_UNIXTIME(b.VIEW_TIME/1000) END
   VIEW_TIME
 from T_MESSAGE a left join T_MESSAGE_USER b on a.MESSAGE_ID=b.MESSAGE_ID
 WHERE a.MESSAGE_ID&gt;1087403  and    a.CATEGORY=596  AND  ( title = '客户已在前台提交资质认证，请查看。'
    or title='客户已在前台更新资质信息，请查看并维护。' )</pre></td>
        </tr>
        <tr>
            <td> count:</td>
            <td colspan="100"><pre id="count" class="layui-code"></pre></td>
        </tr>
        </tbody>
    </table>
</div>
<div id="appendFoot"></div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    })
</script>
</body>
</html>