package com.vedeng.goods.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.StringJoiner;

/**
 * SKU推送状态
 *
 * <AUTHOR> [<EMAIL>]
 */
@Getter
public enum SKUPushedStatusEnum {

    /**
     * 未推送
     */
    OFF(0, "未推送"),

    /**
     * 贝登推送
     */
    VD_PUSHED(1, "贝登"),

    /**
     * 医械购推送
     */
    GO_PUSHED(2, "医械购"),

    /**
     * 科研购
     */
    KY_PUSHED(4, "科研购"),

    KYTMH_PUSHED(32, "科研特麦帮");

    private Integer status;
    private String platformName;

    private final static List<SKUPushedStatusEnum> PLATFORMS = Arrays.asList(VD_PUSHED, GO_PUSHED, KY_PUSHED,KYTMH_PUSHED);

    private final static String SLASH = "/";

    SKUPushedStatusEnum(Integer status, String platformName) {
        this.status = status;
        this.platformName = platformName;
    }

    /**
     * 通过推送状态获取推送平台的名称
     *
     * @param pushedStatus
     * @return
     */
    public static String getPlatformNames(Integer pushedStatus) {
        if (pushedStatus == null || pushedStatus.equals(OFF.getStatus())) {
            return OFF.getPlatformName();
        }

        StringJoiner joiner = new StringJoiner(SLASH);
        PLATFORMS.forEach(e -> {
            if ((pushedStatus & e.getStatus()) > 0) {
                joiner.add(e.getPlatformName());
            }
        });

        return joiner.toString();
    }
}
