package com.vedeng.erp.finance.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.aftersale.dto.AfterBuyorderInvoiceDto;
import com.vedeng.erp.aftersale.dto.AfterSaleGoodsIdQueryReqDto;
import com.vedeng.erp.aftersale.dto.AfterSalesDto;
import com.vedeng.erp.aftersale.service.AfterSalesApiService;
import com.vedeng.erp.aftersale.service.BuyorderAfterSalesApiService;
import com.vedeng.erp.buyorder.dto.BuyOrderApiDto;
import com.vedeng.erp.buyorder.service.BuyorderApiService;
import com.vedeng.erp.finance.constants.FinanceConstant;
import com.vedeng.erp.finance.domain.entity.*;
import com.vedeng.erp.finance.dto.*;
import com.vedeng.erp.finance.enums.InvoiceApplyTypeEnum;
import com.vedeng.erp.finance.enums.InvoiceTypeEnum;
import com.vedeng.erp.finance.enums.YNEnum;
import com.vedeng.erp.finance.mapper.*;
import com.vedeng.erp.finance.mapstruct.InvoiceConvertor;
import com.vedeng.erp.finance.mapstruct.InvoiceDetailConvertor;
import com.vedeng.erp.finance.mapstruct.RInvoiceJInvoiceConvertor;
import com.vedeng.erp.finance.service.HxInvoiceApiService;
import com.vedeng.erp.finance.service.InvoiceApiService;
import com.vedeng.erp.finance.service.InvoiceApplyService;
import com.vedeng.erp.finance.service.InvoiceService;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import com.vedeng.erp.system.dto.SysOptionDefinitionDto;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.SysOptionDefinitionApiService;
import com.vedeng.erp.system.service.UserApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 开票信息接口服务
 * @date 2022/8/27 16:08
 **/
@Service
@Slf4j
public class InvoiceServiceImpl implements InvoiceService,InvoiceApiService {

    @Autowired
    private InvoiceMapper invoiceMapper;

    @Autowired
    private SysOptionDefinitionApiService sysOptionDefinitionApiService;

    @Autowired
    private UserApiService userApiService;

    @Autowired
    private InvoiceDetailMapper invoiceDetailMapper;

    @Autowired
    private InvoiceConvertor invoiceConvertor;

    @Autowired
    private InvoiceDetailConvertor invoiceDetailConvertor;

    @Autowired
    private RInvoiceJInvoiceMapper rInvoiceJInvoiceMapper;

    @Autowired
    private RInvoiceJInvoiceConvertor rInvoiceJInvoiceConvertor;

    @Autowired
    private HxInvoiceApiService hxInvoiceApiService;

    @Resource
    private RInvoiceDetailJOperateLogMapper rInvoiceDetailJOperateLogMapper;

    @Autowired
    private BuyorderAfterSalesApiService buyorderAfterSalesApiService;

    @Autowired
    private ROldInvoiceJNewInvoiceMapper rOldInvoiceJNewInvoiceMapper;
    @Autowired
    private SaleOrderApiService saleOrderApiService;
    @Autowired
    private InvoiceApplyMapper invoiceApplyMapper;
    @Autowired
    private AfterSalesApiService afterSalesApiService;

    @Override
    public List<InvoiceDto> getInvoiceListByRelatedIdGroupByInvoiceNoAndColor(InvoiceDto invoiceDto) {

        List<InvoiceDto> invoiceListByRelatedId = invoiceMapper.getInvoiceListByRelatedIdGroupByInvoiceNoAndColor(invoiceDto);
        bindInvoiceTypeAndCreator(invoiceListByRelatedId);

        return invoiceListByRelatedId;
    }

    private void bindInvoiceTypeAndCreator(List<InvoiceDto> invoiceListByRelatedId) {
        if (CollectionUtil.isNotEmpty(invoiceListByRelatedId)) {

            // 类型
            List<Integer> collect = invoiceListByRelatedId.stream().map(InvoiceDto::getInvoiceType).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(collect)) {
                List<SysOptionDefinitionDto> byIds = sysOptionDefinitionApiService.getByIds(collect);
                if (CollectionUtil.isNotEmpty(byIds)) {
                    Map<Integer, String> stringMap = byIds.stream().collect(Collectors.toMap(SysOptionDefinitionDto::getSysOptionDefinitionId, SysOptionDefinitionDto::getTitle));
                    for (InvoiceDto invoice : invoiceListByRelatedId) {
                        invoice.setInvoiceTypeStr(stringMap.get(invoice.getInvoiceType()));
                    }
                }
            }

            // 创建人
            Set<Integer> users = new HashSet<>();
            invoiceListByRelatedId.forEach(c -> {
                if (Objects.nonNull(c.getCreator())) {
                    users.add(c.getCreator());
                }
            });

            if (users.size() > 0) {
                List<UserDto> userInfoByUserIds = userApiService.getUserInfoByUserIds(new ArrayList<>(users));
                if (CollectionUtil.isNotEmpty(userInfoByUserIds)) {
                    Map<Integer, String> userDtoMap = userInfoByUserIds.stream().collect(Collectors.toMap(UserDto::getUserId, UserDto::getUsername));

                    for (InvoiceDto data : invoiceListByRelatedId) {
                        data.setCreatorName(userDtoMap.get(data.getCreator()));
                    }
                }
            }
        }
    }

    private void bindInvoiceGoodsTypeAndCreator(List<InvoiceByGoodsDto> invoiceListByRelatedId) {
        if (CollectionUtil.isNotEmpty(invoiceListByRelatedId)) {

            // 类型
            List<Integer> collect = invoiceListByRelatedId.stream().map(InvoiceByGoodsDto::getInvoiceType).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(collect)) {
                List<SysOptionDefinitionDto> byIds = sysOptionDefinitionApiService.getByIds(collect);
                if (CollectionUtil.isNotEmpty(byIds)) {
                    Map<Integer, String> stringMap = byIds.stream().collect(Collectors.toMap(SysOptionDefinitionDto::getSysOptionDefinitionId, SysOptionDefinitionDto::getTitle));
                    for (InvoiceByGoodsDto invoice : invoiceListByRelatedId) {
                        invoice.setInvoiceTypeStr(stringMap.get(invoice.getInvoiceType()));
                    }
                }
            }

        }
    }

    @Override
    public Map<Integer, List<InvoiceByGoodsDto>> getInvoiceListByRelatedIdGroupByInvoiceNoAndColorAndOrderGoodsItemId(InvoiceDto invoiceDto) {

        List<InvoiceByGoodsDto> invoiceListByRelatedId = invoiceMapper.getInvoiceListByRelatedIdGroupByInvoiceNoAndColorAndOrderGoodsItemId(invoiceDto);
        bindInvoiceGoodsTypeAndCreator(invoiceListByRelatedId);

        return invoiceListByRelatedId.stream().filter(c -> Objects.nonNull(c.getDetailgoodsId())).collect(Collectors.groupingBy(InvoiceByGoodsDto::getDetailgoodsId));
    }

    @Override
    public Integer coverInvoiceInfoSave(InvoiceReversalDto invoiceReversalInfo, String afterSalesNo, Integer coverType, Integer userId) {
        //兼容采购费用售后申请付款
        //获取采购录入的发票信息
        InvoiceEntity invoice = new InvoiceEntity();
        InvoiceEntity coverInvoice = new InvoiceEntity();
        if (ErpConstant.ONE.equals(coverType)) {
            coverInvoice.setAfterSalesId(invoiceReversalInfo.getReversalBillId());
            invoice = invoiceMapper.queryInvoiceByInvoiceNoAndRelatedId(invoiceReversalInfo.getInvoiceNo(), invoiceReversalInfo.getOrderId(), InvoiceTypeEnum.BUYORDER_ORDER.getCode());
        } else if (ErpConstant.TWO.equals(coverType)) {
            //采购费用单售后
            coverInvoice.setAfterSalesId(Integer.parseInt((invoiceReversalInfo.getReversalBillId().toString())));
            invoice = invoiceMapper.queryInvoiceByInvoiceNoAndRelatedId(invoiceReversalInfo.getInvoiceNo(), Integer.parseInt(invoiceReversalInfo.getOrderId().toString()), InvoiceTypeEnum.BUY_ORDER_EXPENSE.getCode());
        } else {
            log.info("冲销审核通过，售后单类型错误，售后单号{},类型为{},", afterSalesNo, coverType);
        }
        //发票冲销类型保存-1冲销
        coverInvoice.setColorComplementType(ErpConstant.ONE);
        //冲销发票作为红字有效处理
        //1红2蓝
        coverInvoice.setColorType(ErpConstant.ONE);
        //是否有效0否1是
        coverInvoice.setIsEnable(ErpConstant.ONE);
        coverInvoice.setCompanyId(ErpConstant.ONE);
        coverInvoice.setInvoiceCode(invoice.getInvoiceCode());
        coverInvoice.setInvoiceProperty(invoice.getInvoiceProperty());
        coverInvoice.setType(invoice.getType());
        coverInvoice.setTag(invoice.getTag());
        coverInvoice.setRelatedId(invoice.getRelatedId());
        coverInvoice.setValidTime(DateUtil.current());
        coverInvoice.setValidUserid(userId);
        coverInvoice.setCreator(userId);
        coverInvoice.setAddTime(DateUtil.current());

        //发票号
        coverInvoice.setInvoiceNo(setCoverInvoiceNo(invoice.getInvoiceNo()));
        coverInvoice.setInvoiceType(invoice.getInvoiceType());
        coverInvoice.setRatio(invoice.getRatio());
        coverInvoice.setValidStatus(ErpConstant.ONE);
        coverInvoice.setValidComments(invoiceReversalInfo.getAuditComments());
        coverInvoice.setHxInvoiceId(invoice.getHxInvoiceId());
        coverInvoice.setInvoiceFrom(invoice.getInvoiceFrom());
        BigDecimal invoiceAmount = invoiceMapper.querySumAmountByInvoiceNo(invoice.getInvoiceNo(), invoice.getRelatedId(), invoice.getType());
        //查询所有满足需要冲销的蓝字票
        List<Integer> invoiceIdList = invoiceMapper.queryAllInvoiceIdByInvoiceNo(invoice.getInvoiceNo(), invoice.getRelatedId(), invoice.getType());
        coverInvoice.setAmount(invoiceAmount.negate());
        log.info("保存冲销发票主表信息{},", JSON.toJSONString(coverInvoice));
        invoiceMapper.insertSelective(coverInvoice);
        //保存冲销发票明细信息
        List<InvoiceDetailEntity> coverInvoiceDetailEntityList = new ArrayList<>();
        List<InvoiceDetailDto> invoiceDetailAmountList = invoiceDetailMapper.querySumDetailByInvoiceIds(invoiceIdList);
        for (InvoiceDetailDto invoiceDetailDto : invoiceDetailAmountList) {
            InvoiceDetailEntity coverInvoiceDetailEntity = new InvoiceDetailEntity();
            coverInvoiceDetailEntity.setInvoiceId(coverInvoice.getInvoiceId());
            coverInvoiceDetailEntity.setDetailgoodsId(invoiceDetailDto.getDetailgoodsId());
            InvoiceDetailDto oldInvoiceDetail = invoiceDetailMapper.queryDetailByInvoiceInfo(invoiceDetailDto.getDetailgoodsId(), invoice.getInvoiceNo());
            coverInvoiceDetailEntity.setChangedGoodsName(oldInvoiceDetail.getChangedGoodsName());
            coverInvoiceDetailEntity.setNum(invoiceDetailDto.getNum());
            coverInvoiceDetailEntity.setPrice(oldInvoiceDetail.getPrice().negate());
            coverInvoiceDetailEntity.setTotalAmount(oldInvoiceDetail.getTotalAmount().negate());
            coverInvoiceDetailEntityList.add(coverInvoiceDetailEntity);
        }
        if (CollectionUtil.isNotEmpty(coverInvoiceDetailEntityList)) {
            log.info("保存冲销发票明细信息{},", JSON.toJSONString(coverInvoiceDetailEntityList));
            invoiceDetailMapper.batchInsert(coverInvoiceDetailEntityList);
        }
        //保存红蓝票之间的关系
        List<RInvoiceJInvoiceDto> rInvoiceJInvoiceDtoList = new ArrayList<>();
        for (Integer invoiceId : invoiceIdList) {
            RInvoiceJInvoiceDto rInvoiceJInvoiceDto = new RInvoiceJInvoiceDto();
            rInvoiceJInvoiceDto.setInvoiceId(invoiceId);
            rInvoiceJInvoiceDto.setRelateInvoiceId(coverInvoice.getInvoiceId());
            rInvoiceJInvoiceDtoList.add(rInvoiceJInvoiceDto);
        }
        log.info("saveInvoice 关联的票的关系：{}", JSON.toJSONString(rInvoiceJInvoiceDtoList));
        rInvoiceJInvoiceMapper.batchInsert(rInvoiceJInvoiceConvertor.toEntity(rInvoiceJInvoiceDtoList));
        if (!ErpConstant.ZERO.equals(invoice.getHxInvoiceId())) {
            hxInvoiceApiService.refreshHxInvoiceStatus(invoice.getHxInvoiceId());
        }
        return coverInvoice.getInvoiceId();
    }

    /**
     * 设置冲销的发票号
     *
     * @param originInvoiceNo
     * @return
     */
    private String setCoverInvoiceNo(String originInvoiceNo) {
        String latestInvoiceNo = invoiceMapper.queryLatestInvoiceNo(originInvoiceNo);
        log.info("冲销设置发票号，查询到数据库的最新发票号为:{},", latestInvoiceNo);
        if (originInvoiceNo.equals(latestInvoiceNo)) {
            return originInvoiceNo + "-1";
        } else {
            //当前发票已有冲销号，编号+1
            String[] strs = latestInvoiceNo.split("-");
            Integer num = Integer.parseInt(strs[1]) + 1;
            return originInvoiceNo + '-' + num.toString();
        }
    }

    @Override
    public List<InvoiceDto> getInvoicesByRelatedId(Integer relatedId) {
        return invoiceMapper.findByRelatedIdGroupByInvoiceNo(relatedId);
    }

    @Override
    public List<InvoiceDto> getInvoiceListByAfterSalesId(Integer afterSalesId, Integer type) {
        return invoiceMapper.getInvoiceListByAfterSalesId(afterSalesId, type);
    }

    @Override
    public BigDecimal getAlreadyInvoiceNumByGoodsId(Integer goodsId) {
        return invoiceMapper.calculateAlreadyInvoiceNum(goodsId);
    }

    @Override
    public List<InvoiceDto> getOneInvoiceByNoCodeType(InvoiceDto param) {

        List<InvoiceDto> oneInvoiceByNoCodeType = invoiceMapper.getOneInvoiceByNoCodeType(param);
        if (CollUtil.isNotEmpty(oneInvoiceByNoCodeType)) {
            bindInvoiceTypeAndCreator(oneInvoiceByNoCodeType);
        }
        return oneInvoiceByNoCodeType;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Integer saveRedInvoice(InvoiceDto invoiceDto) {
        log.info("saveInvoice 入参：{}", JSON.toJSONString(invoiceDto));
        InvoiceEntity invoiceEntity = invoiceConvertor.toEntity(invoiceDto);
        invoiceMapper.insertSelective(invoiceEntity);
        List<InvoiceDetailEntity> invoiceDetailEntities = invoiceDetailConvertor.toEntity(invoiceDto.getInvoiceDetailDtos());
        if (CollUtil.isNotEmpty(invoiceDetailEntities)) {
            invoiceDetailEntities.forEach(c -> c.setInvoiceId(invoiceEntity.getInvoiceId()));
            invoiceDetailMapper.batchInsert(invoiceDetailEntities);
        }
        if (CollUtil.isNotEmpty(invoiceDto.getInvoiceIds())) {
            List<RInvoiceJInvoiceDto> collect = invoiceDto.getInvoiceIds().stream().map(c -> RInvoiceJInvoiceDto.builder()
                            .invoiceId(invoiceEntity.getInvoiceId())
                            .relateInvoiceId(c)
                            .build())
                    .collect(Collectors.toList());
            log.info("saveInvoice 关联的票的关系：{}", JSON.toJSONString(collect));
            rInvoiceJInvoiceMapper.batchInsert(rInvoiceJInvoiceConvertor.toEntity(collect));
        }
        return invoiceEntity.getInvoiceId();
    }

    @Override
    public BigDecimal getAlreadyRedInvoiceNum(List<Integer> invoiceIdList, Integer goodsId) {
        return invoiceMapper.getAlreadyRedInvoiceNum(invoiceIdList, goodsId);
    }

    @Override
    public BigDecimal getInvoiceTotalAmountByRelatedId(Integer relatedId, Integer type) {
        return Optional.ofNullable(invoiceMapper.getInvoiceTotalAmountByRelatedId(relatedId, type)).orElse(new BigDecimal("0.00"));
    }

    @Override
    public Integer queryInvoiceIdByInfo(Integer buyorderId, String invoiceNo) {
        return invoiceMapper.queryInvoiceIdByInfo(buyorderId, invoiceNo);
    }

    @Autowired
    private BuyorderApiService buyorderApiService;
    
    @Override
    public InvoiceDto queryReceiptInvoiceRecord(String buyorderNo, String invoiceNo) {
        BuyOrderApiDto buyOrderApiDto = buyorderApiService.getBuyorderByBuyorderNo(buyorderNo);
        InvoiceEntity invoiceEntity = invoiceMapper.queryReceiptInvoiceRecord(buyOrderApiDto.getBuyorderId(), invoiceNo);
        if (invoiceEntity == null){
            return null;
        }
        InvoiceDto invoiceDto = new InvoiceDto();
        BeanUtils.copyProperties(invoiceEntity, invoiceDto);
        return invoiceDto;
    }

    @Override
    public boolean saveRelatedWarehousingLogId(List<Integer> invoiceIdList) throws Exception {
        log.info("发票保存关联入库记录 invoiceIdList:{}", JSON.toJSONString(invoiceIdList));
        if (CollectionUtil.isEmpty(invoiceIdList)) {
            throw new RuntimeException("发票保存关联入库记录参数不能为空");
        }

        List<InvoiceDetailEntity> allValidInvoiceDetailList = invoiceDetailMapper.getAllValidInvoiceDetailByInvoiceIds(invoiceIdList);

        if (CollectionUtil.isEmpty(allValidInvoiceDetailList)) {
            throw new Exception("发票保存关联入库记录发票详情内容异常");
        }

        List<Integer> detailGoodsIdList = allValidInvoiceDetailList.stream().map(InvoiceDetailEntity::getDetailgoodsId).distinct().collect(Collectors.toList());

        if (CollectionUtil.isEmpty(detailGoodsIdList)) {
            throw new Exception("发票保存关联入库记录发票详情商品内容异常");
        }

        for (InvoiceDetailEntity detail : allValidInvoiceDetailList) {
            Integer detailgoodsId = detail.getDetailgoodsId();
            // 根据关联关系id查出所有出库单中对应数量小于发票则报错
            BigDecimal sum = rInvoiceDetailJOperateLogMapper.sumWarehouseGoodsOutInItem(detailgoodsId);
            if (sum.compareTo(detail.getNum()) < 0) {
                log.error("当前发票商品无可匹配入库记录 invoiceIdList:{}", JSON.toJSONString(invoiceIdList));
                return false;
            }
        }

        //List<InvoiceRelationOperateLogDto> validWarehousingLogList = rInvoiceDetailJOperateLogMapper.getValidWarehousingLogByRelatedIds(detailGoodsIdList, ErpConstant.ONE);
        //if (CollectionUtil.isEmpty(validWarehousingLogList)) {
        //    log.error("当前发票商品无可匹配入库记录 invoiceIdList:{}", JSON.toJSONString(invoiceIdList));
        //    return false;
        //}

        //Map<Integer, List<InvoiceRelationOperateLogDto>> validDetailGoodsItemMap = validWarehousingLogList.stream().collect(Collectors.groupingBy(InvoiceRelationOperateLogDto::getRelatedId));
        //
        //allValidInvoiceDetailList.forEach(invoiceDetailDto -> {
        //    List<InvoiceRelationOperateLogDto> invoiceRelationOperateLogList = validDetailGoodsItemMap.get(invoiceDetailDto.getDetailgoodsId());
        //    if (CollectionUtil.isEmpty(invoiceRelationOperateLogList)) {
        //        return;
        //    }
        //    relationInvoiceDetailOperateLog(invoiceRelationOperateLogList, invoiceDetailDto, ErpConstant.ONE);
        //});

        return true;
    }


    @Override
    public void rollbackRelatedWarehousingLog(String invoiceNo, String invoiceCode, Integer orderId) throws Exception {
        log.info("回滚入库关联关系 invoiceNo:{}, invoiceCode:{}, orderId:{}", invoiceNo, invoiceCode, orderId);
        if (StringUtils.isBlank(invoiceNo) || StringUtils.isBlank(invoiceCode)) {
            throw new Exception("回滚入库关联关系参数异常");
        }

        List<Integer> validBuyOrderInvoiceIds = invoiceMapper.getValidBuyOrderInvoiceIdsByInvoiceNoAndCode(invoiceNo, invoiceCode, orderId);
        if (CollectionUtil.isEmpty(validBuyOrderInvoiceIds)) {
            log.info("回滚发票无有效采购票信息 invoiceNo:{}, invoiceCode:{}", invoiceNo, invoiceCode);
            return;
        }

        validBuyOrderInvoiceIds.forEach(invoiceId -> {
            List<RInvoiceDetailJOperateLogEntity> rInvoiceDetailJOperateLogEntityList = rInvoiceDetailJOperateLogMapper.findAllByInvoiceId(invoiceId);
            if (CollectionUtil.isEmpty(rInvoiceDetailJOperateLogEntityList)) {
                return;
            }
            rInvoiceDetailJOperateLogEntityList.forEach(item -> {
                RInvoiceDetailJOperateLogEntity rInvoiceDetailJOperateLogEntity = new RInvoiceDetailJOperateLogEntity();
                BeanUtils.copyProperties(item, rInvoiceDetailJOperateLogEntity);
                rInvoiceDetailJOperateLogEntity.setNum(item.getNum().negate().stripTrailingZeros());
                log.info("回滚入库关联关系最终生成 rInvoiceDetailJOperateLogEntity:{}", JSON.toJSONString(rInvoiceDetailJOperateLogEntity));
                rInvoiceDetailJOperateLogMapper.insertSelective(rInvoiceDetailJOperateLogEntity);
            });
        });
    }

    @Override
    public boolean saveRelatedIssueLogId(Integer invoiceId) throws Exception {
        log.info("发票保存关联出库记录 invoiceId:{}", invoiceId);
        if (!Objects.nonNull(invoiceId)) {
            throw new RuntimeException("发票保存关联出库记录参数异常");
        }

        List<InvoiceDetailEntity> invoiceDetailEntities = invoiceDetailMapper.queryAllByInvoiceId(invoiceId);
        if (CollectionUtil.isEmpty(invoiceDetailEntities)) {
            throw new Exception("发票保存关联出库记录发票详情异常");
        }

        List<Integer> buyOrderGoodsIdList = invoiceDetailEntities.stream().map(InvoiceDetailEntity::getDetailgoodsId).distinct().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(buyOrderGoodsIdList)) {
            log.error("发票保存关联出库记录发票商品ID集合为空 invoiceId:{}", invoiceId);
            throw new RuntimeException("发票保存关联出库记录发票商品ID集合为空");
        }

        InvoiceEntity invoiceEntity = invoiceMapper.selectByPrimaryKey(invoiceId);
        if (!Objects.nonNull(invoiceEntity)) {
            log.error("发票保存关联出库记录发票主体信息为空 invoiceId:{}", invoiceId);
            throw new RuntimeException("发票保存关联出库记录发票主体信息为空");
        }

        invoiceDetailEntities.forEach(invoiceDetailEntity -> {
            AfterSaleGoodsIdQueryReqDto afterSaleGoodsIdQueryReq = AfterSaleGoodsIdQueryReqDto.builder()
                    .buyOrderGoodsId(invoiceDetailEntity.getDetailgoodsId()).afterSalesId(invoiceEntity.getAfterSalesId()).build();

            Integer afterGoodsIdByCondition = buyorderAfterSalesApiService.getAfterGoodsIdByCondition(afterSaleGoodsIdQueryReq);

            if (!Objects.nonNull(afterGoodsIdByCondition)) {
                log.error("采购商品找售后商品数据异常 afterSalesId:{}, detailGoodsId:{}", invoiceEntity.getAfterSalesId(), invoiceDetailEntity.getDetailgoodsId());
                throw new RuntimeException("采购商品找售后商品数据异常");
            }

            invoiceDetailEntity.setDetailgoodsId(afterGoodsIdByCondition);
        });


        List<InvoiceRelationOperateLogDto> validWarehousingLogList = rInvoiceDetailJOperateLogMapper.getValidWarehousingLogByRelatedIds(
                invoiceDetailEntities.stream().map(InvoiceDetailEntity::getDetailgoodsId).distinct().collect(Collectors.toList()), ErpConstant.SIX);

        if (CollectionUtil.isEmpty(validWarehousingLogList)) {
            log.error("发票保存关联出库记录无有效出库记录 invoiceId:{}", invoiceId);
            return false;
        }

        Map<Integer, BigDecimal> validDetailGoodsNumMap = validWarehousingLogList.stream()
                .collect(Collectors.groupingBy(InvoiceRelationOperateLogDto::getRelatedId,
                        Collectors.reducing(BigDecimal.ZERO,
                                t -> {
                                    if (t.getCanRelationNum() == null) {
                                        return BigDecimal.ZERO;
                                    }
                                    return t.getCanRelationNum();
                                }, BigDecimal::add)));

        for (InvoiceDetailEntity invoiceDetailDto : invoiceDetailEntities) {
            BigDecimal validRelatedNum = validDetailGoodsNumMap.containsKey(invoiceDetailDto.getDetailgoodsId()) ? BigDecimal.ZERO : validDetailGoodsNumMap.get(invoiceDetailDto.getDetailgoodsId());
            if (invoiceDetailDto.getNum().compareTo(validRelatedNum) < 0) {
                log.error("当前发票商品无可匹配出库记录数量不全 invoiceDetailDto:{}, validRelatedNum:{}", JSON.toJSONString(invoiceDetailDto), validRelatedNum);
                return false;
            }
        }

        Map<Integer, List<InvoiceRelationOperateLogDto>> validDetailGoodsItemMap = validWarehousingLogList.stream().collect(Collectors.groupingBy(InvoiceRelationOperateLogDto::getRelatedId));

        invoiceDetailEntities.forEach(invoiceDetailDto -> {
            List<InvoiceRelationOperateLogDto> invoiceRelationOperateLogList = validDetailGoodsItemMap.get(invoiceDetailDto.getDetailgoodsId());
            if (CollectionUtil.isEmpty(invoiceRelationOperateLogList)) {
                return;
            }
            relationInvoiceDetailOperateLog(invoiceRelationOperateLogList, invoiceDetailDto, ErpConstant.SIX);
        });

        return true;
    }

    /**
     * 发票详情关联有效出入库记录
     *
     * @param validWarehousingLogList
     * @param invoiceDetailDto
     * @param operateLog
     */
    private void relationInvoiceDetailOperateLog(List<InvoiceRelationOperateLogDto> validWarehousingLogList,
                                                 InvoiceDetailEntity invoiceDetailDto,
                                                 Integer operateLog) {
        for (InvoiceRelationOperateLogDto validDetailGoodsItem : validWarehousingLogList) {
            if (validDetailGoodsItem.getCanRelationNum().compareTo(BigDecimal.ZERO) < 1) {
                continue;
            }
            BigDecimal thisRelationNum = validDetailGoodsItem.getCanRelationNum().compareTo(invoiceDetailDto.getNum()) > -1 ?
                    invoiceDetailDto.getNum() : validDetailGoodsItem.getCanRelationNum();

            RInvoiceDetailJOperateLogEntity rInvoiceDetailJOperateLogEntity = RInvoiceDetailJOperateLogEntity.builder()
                    .invoiceDetailId(invoiceDetailDto.getInvoiceDetailId())
                    .operateLogId(validDetailGoodsItem.getLogId())
                    .invoiceId(invoiceDetailDto.getInvoiceId())
                    .detailGoodsId(invoiceDetailDto.getDetailgoodsId())
                    .goodsId(validDetailGoodsItem.getGoodsId())
                    .sku("V" + validDetailGoodsItem.getGoodsId())
                    .num(thisRelationNum)
                    .operateType(operateLog)
                    .isDelete(ErpConstant.DEFAULT_ID)
                    .build();

            log.info("发票与出入库关联关系生成 rInvoiceDetailJOperateLogEntity:{}", JSON.toJSONString(rInvoiceDetailJOperateLogEntity));
            rInvoiceDetailJOperateLogMapper.insertSelective(rInvoiceDetailJOperateLogEntity);

            validDetailGoodsItem.setCanRelationNum(validDetailGoodsItem.getCanRelationNum().subtract(thisRelationNum).stripTrailingZeros());

            invoiceDetailDto.setNum(invoiceDetailDto.getNum().subtract(thisRelationNum).stripTrailingZeros());
            if (invoiceDetailDto.getNum().compareTo(BigDecimal.ZERO) < 1) {
                break;
            }
        }
    }

    @Override
    public InvoiceDto queryInvoiceByInvoiceNoAndRelatedId(String invoiceNo, Integer relatedId, Integer type) {
        InvoiceEntity invoice = invoiceMapper.queryInvoiceByInvoiceNoAndRelatedId(invoiceNo, relatedId, type);
        //查询该订单下该发票录票金额
        BigDecimal amount = invoiceMapper.querySumAmountByInvoiceNo(invoiceNo, relatedId, type);
        invoice.setAmount(amount);
        return invoiceConvertor.toDto(invoice);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer saveBuyorderTpRedInfo(InvoiceDto invoiceDto, List<Integer> detailGoodsIdList, List<BigDecimal> invoiceAmountList, List<BigDecimal> invoicePriceList, List<BigDecimal> invoiceNumList) {
        CurrentUser user = CurrentUser.getCurrentUser();
        //保存仅退票的红票主表信息
        InvoiceEntity invoiceEntity = invoiceConvertor.toEntity(invoiceDto);
        //冲销发票作为红字有效处理
        //1红2蓝
        invoiceEntity.setColorType(ErpConstant.ONE);
        //是否有效0否1是
        invoiceEntity.setIsEnable(ErpConstant.ONE);
        invoiceEntity.setCompanyId(ErpConstant.ONE);
        invoiceEntity.setValidTime(DateUtil.current());
        invoiceEntity.setValidComments("退票售后单退票");
        invoiceEntity.setValidUserid(user.getId());
        invoiceEntity.setCreator(user.getId());
        invoiceEntity.setAddTime(DateUtil.current());
        invoiceEntity.setIsAfterBuyorderOnly(ErpConstant.ONE);
        BigDecimal totalAmount = invoiceAmountList.stream().reduce(BigDecimal::add).orElse(new BigDecimal(0));
        invoiceEntity.setAmount(totalAmount.negate());
        log.info("保存仅退票发票主表信息{},", JSON.toJSONString(invoiceEntity));
        invoiceMapper.insertSelective(invoiceEntity);
        List<InvoiceDetailEntity> invoiceDetailEntityList = new ArrayList<>();
        //保存仅退票的红票明细表信息
        for (int i = 0; i < detailGoodsIdList.size(); i++) {
            InvoiceDetailEntity invoiceDetailEntity = new InvoiceDetailEntity();
            invoiceDetailEntity.setInvoiceId(invoiceEntity.getInvoiceId());
            invoiceDetailEntity.setDetailgoodsId(detailGoodsIdList.get(i));
            invoiceDetailEntity.setNum(invoiceNumList.get(i));
            invoiceDetailEntity.setPrice(invoicePriceList.get(i).negate());
            invoiceDetailEntity.setTotalAmount(invoiceAmountList.get(i).negate());
            invoiceDetailEntityList.add(invoiceDetailEntity);
        }
        if (CollectionUtil.isNotEmpty(invoiceDetailEntityList)) {
            log.info("保存冲销发票明细信息{},", JSON.toJSONString(invoiceDetailEntityList));
            invoiceDetailMapper.batchInsert(invoiceDetailEntityList);
        }
        //根据发票号查询所有涉及的蓝字发票
        List<Integer> invoiceIdList = invoiceMapper.queryValidInvoiceIdByGoodsAndInvoiceNo(invoiceDto.getOldInvoiceNo(), detailGoodsIdList, invoiceEntity.getRelatedId());
        //保存红蓝字发票关系
        List<RInvoiceJInvoiceDto> rInvoiceJInvoiceDtoList = new ArrayList<>();
        for (Integer invoiceId : invoiceIdList) {
            RInvoiceJInvoiceDto rInvoiceJInvoiceDto = new RInvoiceJInvoiceDto();
            rInvoiceJInvoiceDto.setInvoiceId(invoiceId);
            rInvoiceJInvoiceDto.setRelateInvoiceId(invoiceEntity.getInvoiceId());
            rInvoiceJInvoiceDtoList.add(rInvoiceJInvoiceDto);
        }
        log.info("saveInvoice 关联的票的关系：{}", JSON.toJSONString(rInvoiceJInvoiceDtoList));
        rInvoiceJInvoiceMapper.batchInsert(rInvoiceJInvoiceConvertor.toEntity(rInvoiceJInvoiceDtoList));
        return invoiceEntity.getInvoiceId();
    }

    @Override
    public void saveTpNewInvoiceInfo(InvoiceDto invoiceDto, Integer afterSalesId, List<Integer> detailGoodsIdList, List<BigDecimal> invoiceAmountList,
                                     List<BigDecimal> invoicePriceList, List<BigDecimal> invoiceNumList) {
        CurrentUser user = CurrentUser.getCurrentUser();
        //清除红票的hxinvoiceid
        invoiceDto.setHxInvoiceId(ErpConstant.ZERO);
        invoiceDto.setInvoiceFrom(ErpConstant.ZERO);
        //保存仅退票的新蓝票主表信息
        InvoiceEntity invoiceEntity = invoiceConvertor.toEntity(invoiceDto);
        //查询售后暂存表发票信息
        AfterBuyorderInvoiceDto afterBuyorderInvoiceDto = buyorderAfterSalesApiService.queryInfoByAfterSalesId(afterSalesId);
        invoiceEntity.setInvoiceNo(afterBuyorderInvoiceDto.getInvoiceNo());
        invoiceEntity.setInvoiceCode(afterBuyorderInvoiceDto.getInvoiceCode());
        invoiceEntity.setValidStatus(ErpConstant.ONE);
        //1红2蓝
        invoiceEntity.setColorType(ErpConstant.TWO);
        //是否有效0否1是
        invoiceEntity.setIsEnable(ErpConstant.ONE);
        invoiceEntity.setCompanyId(ErpConstant.ONE);
        invoiceEntity.setValidTime(DateUtil.current());
        invoiceEntity.setValidUserid(user.getId());
        invoiceEntity.setValidComments("仅退票录票自动审核");
        invoiceEntity.setCreator(user.getId());
        invoiceEntity.setAddTime(DateUtil.current());
        invoiceEntity.setRatio(afterBuyorderInvoiceDto.getRatio());
        invoiceEntity.setIsAfterBuyorderOnly(ErpConstant.ONE);
        invoiceEntity.setInvoiceType(afterBuyorderInvoiceDto.getInvoiceType());
        HxInvoiceDto hxInvoiceDto = hxInvoiceApiService.queryHxInvoiceInfoByNo(invoiceEntity.getInvoiceNo(), invoiceEntity.getInvoiceCode());
        if (hxInvoiceDto != null) {
            invoiceEntity.setHxInvoiceId(hxInvoiceDto.getHxInvoiceId());
            invoiceEntity.setInvoiceFrom(ErpConstant.ONE);
        }
        BigDecimal allTotalAmount = invoiceAmountList.stream().reduce(BigDecimal::add).orElse(new BigDecimal(0));
        invoiceEntity.setAmount(allTotalAmount);
        log.info("保存仅退票新蓝字发票主表信息{},", JSON.toJSONString(invoiceEntity));
        invoiceMapper.insertSelective(invoiceEntity);
        List<InvoiceDetailEntity> invoiceDetailEntityList = new ArrayList<>();
        //保存仅退票的红票明细表信息
        for (int i = 0; i < detailGoodsIdList.size(); i++) {
            InvoiceDetailEntity invoiceDetailEntity = new InvoiceDetailEntity();
            invoiceDetailEntity.setInvoiceId(invoiceEntity.getInvoiceId());
            invoiceDetailEntity.setDetailgoodsId(detailGoodsIdList.get(i));
            invoiceDetailEntity.setNum(invoiceNumList.get(i));
            invoiceDetailEntity.setPrice(invoicePriceList.get(i));
            invoiceDetailEntity.setTotalAmount(invoiceAmountList.get(i));
            invoiceDetailEntityList.add(invoiceDetailEntity);
        }
        if (CollectionUtil.isNotEmpty(invoiceDetailEntityList)) {
            log.info("保存仅退票新蓝字发票明细信息{},", JSON.toJSONString(invoiceDetailEntityList));
            invoiceDetailMapper.batchInsert(invoiceDetailEntityList);
        }
        //保存新老蓝字发票关联关系
        ROldInvoiceJNewInvoiceEntity rOldInvoiceJNewInvoiceEntity = new ROldInvoiceJNewInvoiceEntity();
        //根据发票号查询所有涉及的蓝字发票
        List<Integer> invoiceIdList = invoiceMapper.queryValidInvoiceIdByGoodsAndInvoiceNo(invoiceDto.getOldInvoiceNo(), detailGoodsIdList, invoiceEntity.getRelatedId());
        for (Integer invoiceId : invoiceIdList) {
            rOldInvoiceJNewInvoiceEntity.setOldInvoiceId(invoiceId);
            rOldInvoiceJNewInvoiceEntity.setNewInvoiceId(invoiceEntity.getInvoiceId());
            rOldInvoiceJNewInvoiceEntity.setInvoiceAmount(allTotalAmount);
            rOldInvoiceJNewInvoiceMapper.insertSelective(rOldInvoiceJNewInvoiceEntity);
        }
    }

    @Override
    public List<String> getRedValidInvoiceListByExpenseId(Integer buyorderExpenseId) {
        return invoiceMapper.getRedValidInvoiceListByExpenseId(buyorderExpenseId);
    }

    @Override
    public List<InvoiceDto> getBlueInvoiceByExpenseId(Integer buyorderExpenseId) {
        return invoiceMapper.getBlueInvoiceByExpenseId(buyorderExpenseId);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void saveBlueInvoice(List<InvoiceDto> invoiceDtos) {

        log.info("蓝票信息保存：{}", JSON.toJSONString(invoiceDtos));

        if (CollUtil.isEmpty(invoiceDtos)) {
            return;
        }

        invoiceDtos.forEach(this::createInvoice);
    }

    @Override
    public List<InvoiceDto> getSaleOrderInvoice(Integer saleOrderId) {
        List<InvoiceEntity> invoiceEntities = invoiceMapper.findBySaleOrderRelatedId(saleOrderId);
        return invoiceConvertor.toDto(invoiceEntities);
    }


    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void createInvoice(InvoiceDto invoiceDto) {
        InvoiceEntity invoiceEntity = invoiceConvertor.toEntity(invoiceDto);
        log.info("创建发票：{}",JSON.toJSON(invoiceEntity));
        invoiceMapper.insertSelective(invoiceEntity);
        invoiceDto.setInvoiceId(invoiceEntity.getInvoiceId());
        List<InvoiceDetailDto> invoiceDetailDtos = invoiceDto.getInvoiceDetailDtos();
        if (CollUtil.isNotEmpty(invoiceDetailDtos)) {
            invoiceDetailDtos.forEach(x -> x.setInvoiceId(invoiceEntity.getInvoiceId()));
        }
        invoiceDetailMapper.batchInsert(invoiceDetailConvertor.toEntity(invoiceDetailDtos));
    }

    @Override
    public List<InvoiceDto> findByInvoiceApply(Integer invoiceApplyId) {
        return invoiceConvertor.toDto(invoiceMapper.findByInvoiceApplyId(invoiceApplyId));
    }

    @Override
    public InvoiceDto getRedInvoice(String invoiceNo) {
        List<InvoiceEntity> byInvoiceNoAndColorTypeAndInvoiceProperty = invoiceMapper.findByInvoiceNoAndColorTypeAndInvoiceProperty(invoiceNo);
        InvoiceEntity invoiceEntity = null;
        if (CollUtil.isNotEmpty(byInvoiceNoAndColorTypeAndInvoiceProperty)) {
            invoiceEntity = CollUtil.getFirst(byInvoiceNoAndColorTypeAndInvoiceProperty);
        }
        return invoiceConvertor.toDto(invoiceEntity);
    }

    @Override
    public void updateDownloadData(InvoiceDto invoiceDto) {
        List<InvoiceEntity> data = invoiceMapper.findByColorTypeAndEnableAndInvoiceApplyId(invoiceDto.getInvoiceNo());
        if (CollUtil.isEmpty(data)) {
            log.info("未查询到开票申请:{},的票信息",invoiceDto.getInvoiceApplyId());
            throw new ServiceException("未查询到票:" + invoiceDto.getInvoiceNo() + ",的信息");
        }
        if (data.size()>1) {
            log.info("updateDownloadData 查询到多张票：{}",JSON.toJSONString(data));
        }
        data.forEach(x->{
            invoiceDto.setInvoiceId(x.getInvoiceId());
            invoiceDto.setModTime(System.currentTimeMillis());
            invoiceMapper.updateByPrimaryKeySelective(invoiceConvertor.toEntity(invoiceDto));
        });

    }

    @Override
    public BigDecimal getSaleOpenInvoiceAmount(Integer saleOrderId) {
        return invoiceMapper.getSaleOpenInvoiceAmount(saleOrderId);
    }

    @Override
    public InvoiceDto getSaleOrderInvoiceByApply(Integer invoiceApplyId,String invoiceNo) {
        List<InvoiceDto> data = invoiceMapper.findSaleorderByInvoiceApplyId(invoiceApplyId,invoiceNo);
        if (CollUtil.isNotEmpty(data)) {
            return data.get(0);
        }
        log.info("查询邮箱交付信息时未查到数据，invoiceApplyId:{},invoiceNo:{}",invoiceApplyId,invoiceNo);
        return null;
    }

    /**
     * 邮箱交付信息
     * @param invoiceApplyId
     * @param invoiceNo
     * @return
     */
    @Override
    public InvoiceDto getAtInvoiceByApply(Integer invoiceApplyId,String invoiceNo) {
        List<InvoiceDto> data = invoiceMapper.findAtByInvoiceApplyId(invoiceApplyId,invoiceNo);
        if (CollUtil.isNotEmpty(data)) {
            return data.get(0);
        }
        log.info("查询邮箱交付信息时未查到数据，invoiceApplyId:{},invoiceNo:{}",invoiceApplyId,invoiceNo);
        return null;
    }

    @Override
    public InvoiceDto findbyInvoiceId(Integer invoiceId) {
        InvoiceEntity invoiceEntity = invoiceMapper.selectByPrimaryKey(invoiceId);
        return invoiceConvertor.toDto(invoiceEntity);
    }

    @Override
    public InvoiceDetailDto findByInvoiceIdAndDetailGoodsId(Integer invoiceId, Integer detailGoodsId) {
        return invoiceDetailMapper.findByInvoiceIdAndDetailGoodsId(invoiceId, detailGoodsId);
    }

    @Override
    public List<InvoiceDetailDto> findByInvoiceId(List<Integer> invoiceIdList) {
        return invoiceDetailConvertor.toDto(invoiceDetailMapper.findByInvoiceIdIn(invoiceIdList));
    }

    @Override
    public InvoiceGoodsResultDto queryInvoiceGoods(String invoiceNo) {
        List<InvoiceGoodsDto> list = invoiceDetailMapper.queryInvoiceGoods(invoiceNo);
        InvoiceGoodsResultDto invoiceGoodsResultDto = new InvoiceGoodsResultDto();
        invoiceGoodsResultDto.setInvoiceGoods(list);
        return invoiceGoodsResultDto;
    }

    @Override
    public InvoiceGoodsResultDto queryInvoiceGoodsBySaleorderNo(String saleorderNo) {
        // 先根据销售订单号查询发票号
        String invoiceNo = invoiceDetailMapper.queryInvoiceNo(saleorderNo);
        if (invoiceNo == null) {
            // 如果没有找到发票号，返回空结果
            InvoiceGoodsResultDto invoiceGoodsResultDto = new InvoiceGoodsResultDto();
            invoiceGoodsResultDto.setInvoiceGoods(new ArrayList<>());
            invoiceGoodsResultDto.setOpenInvoiceStatus(0);
            invoiceGoodsResultDto.setInvoiceNo("");
            return invoiceGoodsResultDto;
        }

        // 根据发票号查询发票商品信息
        List<InvoiceGoodsDto> list = invoiceDetailMapper.queryInvoiceGoods(invoiceNo);
        InvoiceGoodsResultDto invoiceGoodsResultDto = new InvoiceGoodsResultDto();
        invoiceGoodsResultDto.setInvoiceGoods(list);
        invoiceGoodsResultDto.setOpenInvoiceStatus(1);
        invoiceGoodsResultDto.setInvoiceNo(invoiceNo);
        return invoiceGoodsResultDto;
    }

    /**
     * 计算发票已开票金额
     *
     * @param afterSalesId 发票信息
     * @return 发票金额
     */
    @Override
    public BigDecimal calAmount(Integer afterSalesId) {
        InvoiceDto invoiceDto = new InvoiceDto();
        invoiceDto.setType(504);
        invoiceDto.setTag(1);
        invoiceDto.setRelatedId(afterSalesId);
        BigDecimal calAmount = invoiceMapper.calAmount(invoiceDto);
        return Objects.isNull(calAmount) ? BigDecimal.ZERO : calAmount;

    }

    @Override
    public InvoiceDto findByInvoiceNo(String invoiceNo) {
        InvoiceEntity byInvoiceNoOne = invoiceMapper.findByInvoiceNoOne(invoiceNo);
        if (Objects.isNull(byInvoiceNoOne)) {
            return null;
        }
        InvoiceDto invoiceDto = invoiceMapper.selectOrderAndAfterSale(byInvoiceNoOne.getInvoiceId());
        log.info("查询发票信息：{}",JSON.toJSONString(invoiceDto));
        return invoiceDto;
    }

    @Override
    public EnableOpenInvoiceDto enableOpenInvoice(Integer orderId, Integer orderType) {
        EnableOpenInvoiceDto enableOpenInvoiceDto = new EnableOpenInvoiceDto();
        enableOpenInvoiceDto.setPass(Boolean.FALSE);
        if (ErpConstant.ONE.equals(orderType)){
            SaleorderInfoDto saleOrderDto = saleOrderApiService.getBySaleOrderId(orderId);
            if (ErpConstant.ZERO.equals(saleOrderDto.getValidStatus())){
                enableOpenInvoiceDto.setErrorMsg("生效状态为未生效，不可申请开票");
                return enableOpenInvoiceDto;
            }
            if (null != saleOrderDto.getStatus() && Arrays.asList(0,3,4).contains(saleOrderDto.getStatus())){
                enableOpenInvoiceDto.setErrorMsg("单据状态为"+
                        (0 == saleOrderDto.getStatus() ? "待确认" : 3 == saleOrderDto.getStatus() ? "已关闭" : "待用户确认") +
                        "，不可申请开票");
                return enableOpenInvoiceDto;
            }
            if (ErpConstant.TWO.equals(saleOrderDto.getInvoiceType())){
                enableOpenInvoiceDto.setErrorMsg("开票状态为全部开票，不可申请开票");
                return enableOpenInvoiceDto;
            }
            if (ErpConstant.ONE.equals(saleOrderDto.getLockedStatus())){
                enableOpenInvoiceDto.setErrorMsg("订单已锁定，请订单解除锁定后重试");
                return enableOpenInvoiceDto;
            }
            if (BigDecimal.ZERO.compareTo(saleOrderDto.getRealTotalAmount()) >= 0){
                enableOpenInvoiceDto.setErrorMsg("订单实际金额为0，不可申请开票");
                return enableOpenInvoiceDto;
            }
            InvoiceApplyEntity invoiceApplyEntity = new InvoiceApplyEntity();
            invoiceApplyEntity.setRelatedId(orderId);
            // 销售开票
            invoiceApplyEntity.setType(InvoiceApplyTypeEnum.SALE_ORDER.getCode());
            // 开票中，注意底层的SQL判断
            invoiceApplyEntity.setIsOpening(YNEnum.Y.getCode());
            List<InvoiceApplyEntity> invoiceApplyEntities = invoiceApplyMapper.selectInvoiceApplyByEntity(invoiceApplyEntity);
            if (CollUtil.isNotEmpty(invoiceApplyEntities)){
                enableOpenInvoiceDto.setErrorMsg("已存在进行中开票申请");
                return enableOpenInvoiceDto;
            }
        }
        if (ErpConstant.TWO.equals(orderType)) {
            AfterSalesDto afterSalesDto = afterSalesApiService.queryInfoById(orderId);
            if (ErpConstant.ZERO.equals(afterSalesDto.getValidStatus())) {
                enableOpenInvoiceDto.setErrorMsg("生效状态为未生效，不可申请开票");
                return enableOpenInvoiceDto;
            }
            if (null != afterSalesDto.getAtferSalesStatus() && Arrays.asList(0, 3).contains(afterSalesDto.getAtferSalesStatus())) {
                enableOpenInvoiceDto.setErrorMsg("单据状态为" + (ErpConstant.ZERO.equals(afterSalesDto.getAtferSalesStatus()) ? "待确认" : "已关闭") + "，不可申请开票");
                return enableOpenInvoiceDto;
            }
            if (ErpConstant.TWO.equals(afterSalesDto.getInvoiceStatus())) {
                enableOpenInvoiceDto.setErrorMsg("开票状态为全部开票,不可申请开票");
                return enableOpenInvoiceDto;
            }
            //if (null != afterSalesDto.getAmountCollectionStatus() && Arrays.asList(0, 1, 2).contains(afterSalesDto.getAmountCollectionStatus())) {
            //    enableOpenInvoiceDto.setErrorMsg("收款状态为" + (0 == afterSalesDto.getAmountCollectionStatus() ? "无收款状态" : 1 == afterSalesDto.getAmountCollectionStatus() ? "未收款" : "部分收款") + "，不可申请开票");
            //    return enableOpenInvoiceDto;
            //}
            InvoiceApplyDto invoiceApply = invoiceApplyMapper.getAftersaleInvoiceApplyByRelatedIdLast(orderId);
            if (Objects.nonNull(invoiceApply) &&
                    (!FinanceConstant.ONE.equals(invoiceApply.getValidStatus()) && !FinanceConstant.TWO.equals(invoiceApply.getValidStatus())) &&
                    !FinanceConstant.TWO.equals(invoiceApply.getAdvanceValidStatus())) {
                throw new ServiceException("已存在进行中开票申请");
            }
        }
        enableOpenInvoiceDto.setPass(Boolean.TRUE);
        return enableOpenInvoiceDto;
    }

    @Override
    public List<Map<String, Object>> getInvoicedTaxNum(Integer saleorderId, String saleorderNo) {
        return invoiceDetailMapper.getInvoicedTaxNum(saleorderId, saleorderNo);
    }

    @Override
    public List<Map<String, Object>> getInvoicedDataOld(Integer saleorderId, String saleorderNo) {
        return invoiceDetailMapper.getInvoicedDataOld(saleorderId, saleorderNo);
    }

    @Override
    public List<Map<String, Object>> getAppliedTaxNum(Integer saleorderId, String saleorderNo) {
        return invoiceDetailMapper.getAppliedTaxNum(saleorderId, saleorderNo);
    }

    @Override
    public InvoiceDto queryHref(String invoiceNo) {
        log.info("查询发票链接信息: invoiceNo={}", invoiceNo);
        try {
            if (StringUtils.isBlank(invoiceNo)) {
                throw new ServiceException("发票号不能为空");
            }
            
            // 根据发票号查询发票信息
            InvoiceEntity invoice = invoiceMapper.findByInvoiceNoOne(invoiceNo);
            if (invoice == null) {
                log.warn("未找到对应的发票信息: invoiceNo={}", invoiceNo);
                return null;
            }

            InvoiceDto result = new InvoiceDto();
            result.setInvoiceHref(invoice.getInvoiceHref());
            log.info("查询发票链接成功: result={}", JSON.toJSONString(result));
            return result;
        } catch (Exception e) {
            log.error("查询发票链接失败: invoiceNo={}", invoiceNo, e);
            throw new ServiceException("查询发票链接失败: " + e.getMessage());
        }
    }

    @Override
    public void updateHref(String invoiceNo, String invoiceHref) {
        log.info("更新发票链接信息: invoiceNo={}, invoiceHref={}", invoiceNo, invoiceHref);
        try {
            if (StringUtils.isBlank(invoiceNo)) {
                throw new ServiceException("发票号不能为空");
            }
            
            // 根据发票号查询发票信息
            InvoiceEntity invoice = invoiceMapper.findByInvoiceNoOne(invoiceNo);
            if (invoice == null) {
                throw new ServiceException("未找到对应的发票记录");
            }
            if (StrUtil.isNotBlank(invoice.getInvoiceHref())){
                log.info("无需更新");
                return ;
            }
            
            // 更新发票的修改时间和链接相关信息
            InvoiceEntity updateEntity = new InvoiceEntity();
            updateEntity.setInvoiceId(invoice.getInvoiceId());
            updateEntity.setInvoiceHref(invoiceHref);
            updateEntity.setOssFileUrl(invoiceHref);
            invoiceMapper.updateByPrimaryKeySelective(updateEntity);
        } catch (Exception e) {
            log.error("更新发票链接失败: invoiceNo={}, invoiceHref={}", invoiceNo, invoiceHref, e);
            throw new ServiceException("更新发票链接失败: " + e.getMessage());
        }
    }
}
