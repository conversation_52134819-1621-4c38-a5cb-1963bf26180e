package com.test.jbatis;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.domain.entity.KingDeeSupplierEntity;
import com.vedeng.erp.kingdee.repository.KingDeeSupplierRepository;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2023/2/27 12:52
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring.xml", "classpath:kingdee-spring-mybatis.xml"})
public class KingDeeSupplierRepositoryTest {

    @Autowired
    private KingDeeSupplierRepository kingDeeSupplierRepository;



    @Test
    public void testQueryByObject() {
        KingDeeSupplierEntity entity = new KingDeeSupplierEntity();
        entity.setFNumber(5480);
        List<KingDeeSupplierEntity> list = kingDeeSupplierRepository.queryByObject(entity);
        System.out.println(JSON.toJSON(list));
    }

}
