package com.newtask.broadcast;

import java.util.Calendar;
import java.util.Date;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.broadcast.statistics.project.BroadcastDay;
import com.vedeng.erp.common.broadcast.param.TimePeriod;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;


/**
 * 日到款最终数据落表，每天0点执行，执行前一天的整点数据
 * @ClassName:  BroadcastDayFinallyTask   
 * @author: Neil.yang
 * @date:   2025年6月18日 上午10:30:29    
 * @Copyright:
 */
@JobHandler(value = "broadcastDayFinallyTask")
@Component
public class BroadcastDayFinallyTask extends AbstractJobHandler {

    Logger logger = LoggerFactory.getLogger(BroadcastDayFinallyTask.class);
    
    @Autowired
    private BroadcastDay broadcastDay;
    
    @Override
    public ReturnT<String> doExecute(String params) throws Exception {
        XxlJobLogger.log("日到款最终数据落表 start-----------");
        logger.info("日到款最终数据落表  start-----------");
        try {
        	// 获取Calendar实例
            Calendar calendar = Calendar.getInstance();
            
            // 设置为前一天
            calendar.add(Calendar.DAY_OF_MONTH, -1);
            
            // 设置时间为00:00:00
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            Date startOfDay = calendar.getTime();
            
            // 设置时间为23:59:59
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            calendar.set(Calendar.MILLISECOND, 999);
            Date endOfDay = calendar.getTime();
        	
        	
        	TimePeriod timePeriod = new TimePeriod(startOfDay,endOfDay);
        	broadcastDay.invocation(timePeriod,false);

        }catch(Exception e) {
        	XxlJobLogger.log("日到款最终数据落表失败");
        	logger.error("日到款最终数据落表失败");
        	return FAIL;
        }
        logger.info("日到款最终数据落表 end-----------");
        XxlJobLogger.log("日到款最终数据落表 end-----------");
        return SUCCESS;
    }
    
    
    
    public static void main(String[] args) {
    	
    	// 获取Calendar实例
        Calendar calendar = Calendar.getInstance();
        
        // 设置为前一天
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        
        // 设置时间为00:00:00
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date startOfDay = calendar.getTime();
        
        // 设置时间为23:59:59
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        Date endOfDay = calendar.getTime();
        System.out.println(startOfDay);
        System.out.println(endOfDay);
	}
}
