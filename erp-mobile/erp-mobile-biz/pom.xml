<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>erp-mobile</artifactId>
        <groupId>com.vedeng.erp</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>erp-mobile-biz</artifactId>


    <dependencies>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>5.1.7.RELEASE</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-mobile-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-system-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.vedeng.framework</groupId>
                    <artifactId>vedeng-core-sqlmonitor</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-trader-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-saleorder-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>


        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-trader-biz</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.vedeng.framework</groupId>
                    <artifactId>vedeng-core-sqlmonitor</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.vedeng.erp</groupId>
                    <artifactId>erp-common-redis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.vedeng.erp</groupId>
                    <artifactId>erp-old</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.vedeng.market</groupId>
            <artifactId>market-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-system-biz</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.vedeng.erp</groupId>
                    <artifactId>erp-common-redis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.vedeng.framework</groupId>
                    <artifactId>vedeng-core-sqlmonitor</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Doc -->
        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-doc-biz</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.vedeng.erp</groupId>
                    <artifactId>erp-common-redis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.vedeng.framework</groupId>
                    <artifactId>vedeng-core-sqlmonitor</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Goods -->
        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-goods-biz</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.vedeng.erp</groupId>
                    <artifactId>erp-common-redis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.vedeng.framework</groupId>
                    <artifactId>vedeng-core-sqlmonitor</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.vedeng.search</groupId>
                    <artifactId>search-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- saleorder-->
        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-saleorder-biz</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.vedeng.erp</groupId>
                    <artifactId>erp-common-redis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.vedeng.framework</groupId>
                    <artifactId>vedeng-core-sqlmonitor</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.vedeng.erp</groupId>
                    <artifactId>erp-old</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.vedeng.crm.admin</groupId>
                    <artifactId>crm-admin-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <!-- buyorder-->
        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-buyorder-biz</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.vedeng.erp</groupId>
                    <artifactId>erp-common-redis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.vedeng.framework</groupId>
                    <artifactId>vedeng-core-sqlmonitor</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.vedeng.erp</groupId>
                    <artifactId>erp-old</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <!-- UAC -->
        <dependency>
            <groupId>com.vedeng.uac</groupId>
            <artifactId>vedeng-uac-api</artifactId>
            <version>1.1.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-openfeign-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>




    </dependencies>

</project>