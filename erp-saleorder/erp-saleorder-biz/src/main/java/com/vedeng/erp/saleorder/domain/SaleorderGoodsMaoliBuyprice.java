package com.vedeng.erp.saleorder.domain;

import com.vedeng.common.mybatis.domain.BaseEntity;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/11/8
 */

/**
 * 销售订单对应的近期采购价格表
 */
public class SaleorderGoodsMaoliBuyprice extends BaseEntity {
    private Integer saleorderGoodsMaoliBuypriceId;

    /**
     * 销售订单ID
     */
    private Integer saleorderId;

    /**
     * SKU
     */
    private String skuNo;

    /**
     * 近期采购价-来自onedata
     */
    private BigDecimal buyPrice;

    /**
     * 近期采购价说明-来自onedata
     */
    private String buyPriceDesc;

    /**
     * 价格状态-0待查询 1成功 2失败
     */
    private Integer priceStatus;

    public Integer getSaleorderGoodsMaoliBuypriceId() {
        return saleorderGoodsMaoliBuypriceId;
    }

    public void setSaleorderGoodsMaoliBuypriceId(Integer saleorderGoodsMaoliBuypriceId) {
        this.saleorderGoodsMaoliBuypriceId = saleorderGoodsMaoliBuypriceId;
    }

    public Integer getSaleorderId() {
        return saleorderId;
    }

    public void setSaleorderId(Integer saleorderId) {
        this.saleorderId = saleorderId;
    }

    public String getSkuNo() {
        return skuNo;
    }

    public void setSkuNo(String skuNo) {
        this.skuNo = skuNo;
    }

    public BigDecimal getBuyPrice() {
        return buyPrice;
    }

    public void setBuyPrice(BigDecimal buyPrice) {
        this.buyPrice = buyPrice;
    }

    public String getBuyPriceDesc() {
        return buyPriceDesc;
    }

    public void setBuyPriceDesc(String buyPriceDesc) {
        this.buyPriceDesc = buyPriceDesc;
    }

    public Integer getPriceStatus() {
        return priceStatus;
    }

    public void setPriceStatus(Integer priceStatus) {
        this.priceStatus = priceStatus;
    }
}