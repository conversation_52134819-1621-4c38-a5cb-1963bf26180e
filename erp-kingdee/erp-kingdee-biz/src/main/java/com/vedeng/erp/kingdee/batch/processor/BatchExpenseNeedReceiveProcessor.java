package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDetailDto;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceDetailDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveFeeDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveFeeDto;
import com.vedeng.erp.kingdee.dto.result.KingDeeReceiveQueryResultDto;
import com.vedeng.erp.kingdee.service.KingDeeReceiveFeeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 销售费用应收单
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/12 13:58
 */
@Service
@Slf4j
public class BatchExpenseNeedReceiveProcessor implements ItemProcessor<BatchInvoiceDto, KingDeeReceiveFeeDto> {

    @Autowired
    private BatchInvoiceDetailDtoMapper batchInvoiceDetailDtoMapper;

    @Autowired
    private KingDeeReceiveFeeService kingDeeReceiveFeeService;

    @Autowired
    private BatchInvoiceDtoMapper batchInvoiceDtoMapper;

    @Override
    public KingDeeReceiveFeeDto process(BatchInvoiceDto batchInvoiceDto) throws Exception {
        if (StrUtil.isBlank(batchInvoiceDto.getInvoiceNo()) || StrUtil.isBlank(batchInvoiceDto.getInvoiceCode())) {
            log.warn("销售单费用商品蓝票无发票号或者发票代码{}", JSON.toJSONString(batchInvoiceDto));
            return null;
        }

        // 排除蓝字作废
        List<BatchInvoiceDto> blueDisEnableByInvoiceCodeAndInvoiceNo = batchInvoiceDtoMapper.findBlueDisEnableByInvoiceCodeAndInvoiceNo(batchInvoiceDto.getInvoiceCode(), batchInvoiceDto.getInvoiceNo(), 505);
        if (CollUtil.isNotEmpty(blueDisEnableByInvoiceCodeAndInvoiceNo)) {
            log.info("当前蓝票存在蓝字作废{}", JSON.toJSONString(batchInvoiceDto));
            return null;
        }

        // 先筛选出全部/部分包含费用商品的发票，再去查对应的商品信息（全部是普通商品的话，直接过滤掉）
        log.info("销售单费用商品应收单信息构造开始：{}", JSON.toJSONString(batchInvoiceDto));
        List<BatchInvoiceDetailDto> invoiceDetailList;
        List<BatchInvoiceDetailDto> expenseInvoiceDetailList;
        if (CollectionUtils.isEmpty(batchInvoiceDto.getBatchInvoiceDetailDtoList())) {
            // 发票明细为空，则表示是非历史数据，直接取数据库查询发票明细
            log.info("开始处理销售费用应收单蓝票明细：{}", JSON.toJSONString(batchInvoiceDto));
            invoiceDetailList = batchInvoiceDetailDtoMapper.getSaleOrderInvoiceDetailList(batchInvoiceDto.getInvoiceId());
        } else {
            // 否则，则证明是从Excel中读取的发票明细
            log.info("开始处理21-22历史销售费用应收单蓝票明细：{}", JSON.toJSONString(batchInvoiceDto));
            invoiceDetailList = batchInvoiceDto.getBatchInvoiceDetailDtoList();
        }

        expenseInvoiceDetailList = invoiceDetailList.stream().filter(coreSkuVo -> coreSkuVo.getIsVirtureSku() != null && coreSkuVo.getIsVirtureSku() == 1)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(expenseInvoiceDetailList)) {
            // 当前发票关联的销售单商品全是实物商品，不符合费用票推送条件，直接返回
            return null;
        }

        // 判断是否数据已存在
        List<KingDeeReceiveQueryResultDto> kingDeeSaleReceivable = kingDeeReceiveFeeService.getKingDeeReceiveFee(batchInvoiceDto.getInvoiceId().toString());
        if (CollUtil.isNotEmpty(kingDeeSaleReceivable)) {
            log.info("销售费用商品费用应收单,金蝶数据已存在:{}", JSON.toJSONString(batchInvoiceDto));
            return null;
        }

        KingDeeReceiveFeeDto kingDeeReceiveFeeDto = new KingDeeReceiveFeeDto();
        kingDeeReceiveFeeDto.setFid("0");
        kingDeeReceiveFeeDto.setFdate(DateUtil.formatDate(new Date(batchInvoiceDto.getAddTime())));
        kingDeeReceiveFeeDto.setFcustomerid(batchInvoiceDto.getTraderCustomerId().toString());
        kingDeeReceiveFeeDto.setFQzokBddjtid(batchInvoiceDto.getInvoiceId().toString());

        // 费用发票明细信息
        List<KingDeeReceiveFeeDetailDto> fEntityDetail = new ArrayList<>();
        // 费用编码相同、价格相同 合并
        expenseInvoiceDetailList.forEach(item -> {
            KingDeeReceiveFeeDetailDto temp = KingDeeReceiveFeeDetailDto.builder()
                    .fcostid(item.getUnitKingDeeNo())
                    .fPriceQty(item.getNum().toString())
                    .fTaxPrice(item.getTotalAmount().divide(item.getNum(), 2, RoundingMode.HALF_UP).toString())
                    .fEntryTaxRate(batchInvoiceDto.getRatio().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP).toString())
                    .fQzokBddjhid(item.getInvoiceDetailId().toString())
                    .build();
            fEntityDetail.add(temp);
        });
        kingDeeReceiveFeeDto.setFEntityDetail(fEntityDetail);
        log.info("销售单费用商品应收单信息构造成功：{}", JSON.toJSONString(kingDeeReceiveFeeDto));
        return kingDeeReceiveFeeDto;
    }

}