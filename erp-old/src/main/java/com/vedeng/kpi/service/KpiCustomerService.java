package com.vedeng.kpi.service;

import com.vedeng.kpi.model.DTO.KpiDataQueryDto;
import com.vedeng.kpi.model.DTO.KpiUserInfoDto;

import java.util.List;

/**
 * 五行客户统计service.
 * @jira: VDERP-2376【五行剑法】规则修改.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/6/5 10:19 上午.
 * @author: Tomcat.Hui.
 */
public interface KpiCustomerService {

    /**
     * 获取客户是否流失(团队).
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/6/27 7:53 下午.
     * @author: Tomcat.Hui.
     * @param query: .
     * @return: java.util.List<com.vedeng.kpi.model.DTO.KpiDataQueryDto>.
     * @throws: .
     */
    List<KpiDataQueryDto> getGroupCoCustomers(KpiDataQueryDto query);

    /**
     * 获取客户是否流失(用户).
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/6/27 7:57 下午.
     * @author: Tomcat.Hui.
     * @param query: .
     * @return: java.util.List<com.vedeng.kpi.model.DTO.KpiDataQueryDto>.
     * @throws: .
     */
    List<KpiDataQueryDto> getUserCoCustomers(KpiDataQueryDto query);

    /**
     * 获取客户详情(页面展示).
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/6 11:41 上午.
     * @author: Tomcat.Hui.
     * @param query: .
     * @return: java.util.List<com.vedeng.kpi.model.DTO.KpiDataQueryDto>.
     * @throws: .
     */
    List<KpiDataQueryDto> getUserCoCustomersDetail(KpiDataQueryDto query);

    /**
     * 获取客户最后沟通/下单时间.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/6 11:40 上午.
     * @author: Tomcat.Hui.
     * @param query: .
     * @return: java.util.List<com.vedeng.kpi.model.DTO.KpiDataQueryDto>.
     * @throws: .
     */
    List<KpiDataQueryDto> getTraderLastTimes(KpiDataQueryDto query);
}
