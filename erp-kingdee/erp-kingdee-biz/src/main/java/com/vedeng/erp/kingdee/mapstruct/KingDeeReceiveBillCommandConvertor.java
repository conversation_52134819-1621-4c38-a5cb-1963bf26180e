package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.domain.command.KingDeeReceiveBillCommand;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveBillDto;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveBillEntryDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface KingDeeReceiveBillCommandConvertor extends BaseCommandMapStruct<KingDeeReceiveBillCommand,KingDeeReceiveBillDto> {
    /**
     * dto转为金蝶command
     * @param dto
     * @return
     */
    @Override
    @Mapping(target = "FBillTypeId.FNumber", source = "FBillTypeId")
    @Mapping(target = "FPayOrgId.FNumber", source = "FPayOrgId")
    @Mapping(target = "FContactUnit.FNumber", source = "FContactUnit")
    @Mapping(target = "f_qzok_jyzt", source = "FQzokJyzt")
    @Mapping(target = "f_qzok_jylx", source = "FQzokJylx")
    @Mapping(target = "f_qzok_jyfs", source = "FQzokJyfs")
    @Mapping(target = "f_qzok_lsh", source = "FQzokLsh")
    @Mapping(target = "f_QZOK_PZGSYWDH",source = "FQzokPzgsywdh")
    KingDeeReceiveBillCommand toCommand(KingDeeReceiveBillDto dto);


    @Mapping(target = "FSettleTypeId.FNumber", source = "FSettleTypeId")
    @Mapping(target = "FPurposeId.FNumber", source = "FPurposeId")
    @Mapping(target = "FAccountId.FNumber", source = "FAccountId")
    @Mapping(target = "f_qzok_skyw.FNumber", source = "FQzokSkyw")
    @Mapping(target = "f_qzok_bddjhid", source = "FQzokBddjhid")
    @Mapping(target = "f_qzok_ysddh", source = "FQzokYsddh")
    @Mapping(target = "f_qzok_gsywdh", source = "FQzokGsywdh")
    @Mapping(target = "f_qzok_ywlx", source = "FQzokYwlx")
    @Mapping(target = "FHANDLINGCHARGEFOR", source = "FHandlingChargeFor")
    KingDeeReceiveBillCommand.KingDeeReceiveBillEntryCommand toCommand(KingDeeReceiveBillEntryDto dto);
}
