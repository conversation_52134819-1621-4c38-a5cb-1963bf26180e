package com.vedeng.goods.enums;

/**
 * SKU信息同步状态
 */
public enum SkuSynchronizationStatusEnum {


    OFF(0, "未推送"),

    PUSHED(1, "已推送"),

    RE_PUSH(2, "需重推");

    private Integer status;
    private String describe;


    SkuSynchronizationStatusEnum(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }
}
