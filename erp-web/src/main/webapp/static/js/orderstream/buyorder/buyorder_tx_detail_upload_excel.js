// /*
// var keyValue=1000
//
//
// function savePurchaseData(list,buyOrderId){
//     $.ajax({
//         async:false,
//         url: '/order/newBuyorderPeerList/saveDetails.do',
//         data:JSON.stringify({
//             "list":list,
//             "buyorderId":buyOrderId
//         }),
//         type:"POST",
//         dataType : "json",
//         contentType:'application/json',
//         success:function(data){
//             console.log(data);
//             // layer.close(load);
//             if (data.data.code == 200) {
//                 layer.alert("保存成功");
//             } else {
//                 if (data.data.code == 101) {
//                     layer.alert("生产批次号/序列号存在空值");
//                 }  else
//                 if (data.data.code == 102) {
//                     layer.alert("生产批次号/序列号存在重复："+data.data.msg);
//                 }else
//                 if (data.data.code == 1) {
//                     layer.alert(data.data.msg);
//                 }else
//                 if (data.data.code == 2) {
//                     layer.alert(data.data.msg+"的收货数量超出可填写的收货数量");
//                 }else
//                 if (data.data.code == 90) {
//                     layer.alert(data.data.msg+"的生产批号/序列号不可为空");
//                 }else
//                 if (data.data.code == 91) {
//                     layer.alert(data.data.msg+"的收货数量必填");
//                 }else if (data.data.code == 92) {
//                     layer.alert(data.data.msg + "的生效日期、失效日期必填");
//                 } else {
//                     layer.alert("服务器错误，请联系研发部");
//                 }
//
//             }
//
//             // window.parent.search();
//         },
//         error:function(data){
//             layer.alert("500")
//             if(data.status == 1001){
//                 layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
//             }
//             if(data.status == 400){
//                 layer.alert("操作失败");
//             }
//             if(data.status == 500){
//                 layer.alert("操作失败");
//             }
//         }
//     });
// }
//
// function pcChange(obj){
//     var pc = $(obj).val();
//     var s = pc.trim();
//     if (pc.length > s.length) {
//         layer.msg("生产批次号/序列号不可带空格",{icon: 2})
//     }
//     if (s.length>50) {
//         layer.msg("生产批次号/序列号 长度不可超过50",{icon: 2})
//     }
// }
//
// function arrivalNum(obj){
//     var re = /^\+?[1-9][0-9]*$/;
//     var arrivalNum = $(obj).val();
//     if (!re.test(arrivalNum)) {
//         $(obj).get(0).value = "";
//         layer.msg("收货数量必须为正整数", {icon: 2});
//     } else {
//         if (arrivalNum>$(obj).get(0).parentNode.children[0].value) {
//             $(obj).get(0).value = "";
//             layer.msg("收货数量超出",{icon: 2})
//         }
//     }
//
// }
//
// function checkPurchaseDetail(obj){
//     if (obj.batchNumber.length>50) {
//         layer.alert(obj.sku+"的生产批次号/序列号 长度不可超过50",{icon: 2})
//         return false;
//     }
//     if (obj.batchNumber==""||obj.batchNumber==undefined) {
//         layer.alert(obj.sku+"的生产批次号/序列号 必填",{icon: 2})
//         return false;
//     }
//
//     if (obj.arrivalCount>obj.oldNum) {
//         layer.alert(obj.sku+"收货数量超出可收获数量",{icon: 2});
//         return false;
//     }
//
//     if (obj.arrivalCount==0||obj.arrivalCount==undefined||obj.arrivalCount=="") {
//         layer.alert(obj.sku+"收货数量必填",{icon: 2});
//         return false;
//     }
//
//     if (obj.spuType==317||obj.spuType==318) {
//         if (obj.manufactureDateTime==""||obj.manufactureDateTime==undefined||obj.invalidDateTime==""||obj.invalidDateTime==undefined) {
//             layer.alert(obj.sku+"的生效日期、失效日期必填",{icon: 2});
//             return false;
//         }
//     }
//
//     return true;
// }
// function checkData(obj){
//     var flag = false;
//     $.ajax({
//         async:false,
//         url: '/order/newBuyorderPeerList/checkDetails.do',
//         data:JSON.stringify(obj),
//         type:"POST",
//         dataType : "json",
//         contentType:'application/json',
//         success:function(data){
//             console.log(data);
//             // layer.close(load);
//             if (data.data.code === 200) {
//                 flag = true;
//             } else {
//                 if (data.data.code === 101) {
//                     layer.alert("生产批次号/序列号存在空值");
//                 }
//                 if (data.data.code === 102) {
//                     layer.alert("生产批次号/序列号存在重复："+data.data.msg);
//                 }
//
//                 if (data.data.code === 1) {
//                     layer.alert(data.data.msg);
//                 }
//                 if (data.data.code === 2) {
//                     layer.alert(data.data.msg+"的收货数量超出可填写的收货数量");
//                 }
//
//                 if (data.data.code === 90) {
//                     layer.alert(data.data.msg+"的生产批号/序列号不可为空");
//                 }
//
//                 if (data.data.code === 91) {
//                     layer.alert(data.data.msg+"的收货数量必填");
//                 }
//
//                 if (data.data.code === 92) {
//                     layer.alert(data.data.msg+"的生效日期、失效日期必填");
//                 }
//
//             }
//
//             // window.parent.search();
//         },
//         error:function(data){
//             if(data.status == 1001){
//                 layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
//             }
//             if(data.status == 400){
//                 layer.alert("操作失败");
//             }
//             if(data.status == 500){
//                 layer.alert("操作失败");
//             }
//         }
//     });
//
//     return flag;
// }
// function PurchaseDetail(obj){
//     // 物流明细id
//     this.expressDetailId=obj.children[11].children[0].innerText;
//     this.spuType=obj.children[12].children[0].innerText;
//     debugger
//     if (obj.children[12].children[0].innerText == 316) {
//         this.unionSequence = 1;
//     } else {
//         this.unionSequence = 0;
//     }
//
//     // 商品sku
//     this.sku=obj.children[1].children[0].innerText;
//     // 商品名
//     this.skuName=obj.children[0].children[0].innerText;
//     // 型号
//     this.model=obj.children[2].children[0].innerText;
//     // 单位
//     this.unit=obj.children[3].children[0].innerText;
//     // 生产厂家
//     this.productCompany=obj.children[4].children[0].innerText;
//     // 生产许可证号
//     this.productionLicence=obj.children[5].children[0].innerText;
//     // 注册证号
//     this.registerNumber=obj.children[6].children[0].innerText;
//     // 生产批次号
//     this.batchNumber=obj.children[7].children[0].children[0].value.trim();
//     // 收货数量
//     this.arrivalCount=obj.children[8].children[0].children[1].value;
//     // 此物流单下还需要的
//     this.oldNum=obj.children[8].children[0].children[0].value;
//     // 生产日期
//     this.manufactureDateTime=obj.children[9].children[0].children[0].value;
//     // 失效日期
//     debugger
//     this.invalidDateTime=obj.children[10].children[0].children[0].value;
// }
// function copyFun(obj) {
//     layer.confirm('真的复制当前行吗', function(index){
//         debugger
//         var parentNode = obj.parentNode.parentNode.parentNode;
//         console.log(parentNode)
//         var tr = $(parentNode);
//         var str = $(parentNode).clone();
//         $(str).find(".takeDate").each(function (){
//             keyValue =keyValue+1;
//             $(this).attr("lay-key",keyValue)
//             console.log(this)
//         })
//         tr.after(str);
//         // $(obj.parentNode.parentNode.parentNode.).find(".")
//         /!* var parentNode = obj.parentNode.parentNode.parentNode;
//          debugger
//          var find = $(parentNode).find("tr:last");
//          console.log(parentNode)
//          var arr = Array.prototype.slice.call(parentNode);
//          $(parentNode).find("tr:last")
//          $(parentNode).find("tr:last").after(str);*!/
//         setTimeout(function(){
//             $(str).find(".takeDate").each(function (){
//                 console.log(11221);
//                 layui.laydate.render({
//                     elem: this
//                     ,type: 'datetime'
//                     ,closeStop: this
//                     ,zIndex: 99999999
//                 });
//             })
//         },2000)
//         layer.close(index);
//     })
//
//
//
// }
// function delFun(obj) {
//     layer.confirm('真的删除当前行吗', function(index){
//         debugger
//         $(obj.parentNode.parentNode.parentNode).remove();
//         layer.close(index);
//     })
//     /!* var parentNode = obj.parentNode.parentNode.parentNode;
//      debugger
//      var find = $(parentNode).find("tr:last");
//      console.log(parentNode)
//      var arr = Array.prototype.slice.call(parentNode);
//      $(parentNode).find("tr:last")
//      $(parentNode).find("tr:last").after(str);*!/
//
//
// }
//
//
//
//
//
// /!*$(document).ready(function () {
//     debugger
//     var val = $("input[name='expressIds']");
//     if (val == null || val == undefined || val == "") {
//         return;
//     }
//     $("input[name='expressIds']").each(function (){
//         var value = $(this).val();
//         $.ajax({
//             type: "get",
//             url: "/order/newBuyorderPeerList/getNeedEditBuyGoods.do?expressId="+value,
//             async: true,
//             dataType: 'json',
//             success: function (data) {
//                 //物流管理
//                 var wlhtml = "";
//                 //入库记录
//                 var rkhtml = "";
//                 var myDate = new Date();
//                 var voList = data.data;
//                 for (var i = 0; i < voList.length; i++) {
//                     var bgv = voList[i];
//                     wlhtml = wlhtml+"<tr><td>"+bgv.goodsName +"</td><td>"+bgv.sku+"</td><td>"+bgv.model+"</td><td>"+bgv.unitName+"</td><td>"+bgv.manufacturerName+
//                         "</td><td>"+bgv.productCompanyLicence+"</td><td>"+bgv.registrationNumber+"</td><td>"+"<input type=\"text\" style=\"width: 150px;\" name = \"pc\" >"+
//                         "</td><td>"+'<input type="text" style="width: 45px;" name = "canReceive" class="layui-inline">'+'/'+bgv.dnum+"</td><td>"+
//                         '<input type="text" style="width: 160px;height: 28px;" placeholder="请选择日期" className="layui-input layui-input-date takeDate"/>'+"</td><td>"+
//                         '<input type="text" style="width: 160px;height: 28px;" placeholder="请选择日期" className="layui-input layui-input-date takeDate"/>'+"</td><td>"+
//                         '<a class="layui-btn layui-btn-xs copyRow">复制</a> \n' +
//                         '<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a> '
//                         +"</td>"+"</tr>"
//
//                 }
//
//                 // 物流信息
//                 if (wlhtml != "") {
//                     $("#"+"tbody-"+value).find("tr:last").parent().append(wlhtml);
//                     $("#"+"tbody-"+value).find("tr:first").remove();
//                 }
//                 /!*if (rkhtml != "") {
//                     $("#rk").find("tr:last").after(rkhtml);
//                 }*!/
//
//                 loadMoreAddTitle();
//                 loadMoreBlueKuang();
//                 dnyPopNewdata();
//
//             },
//             error: function (data) {
//                 loadMoreClose();
//                 if (data.status == 1001) {
//                     layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
//                 }
//             }
//         });
//     });
//
//     $('.takeDate').each(function() {
//         console.log(111);
//         layui.laydate.render({
//             elem: this
//             ,type: 'datetime'
//             ,closeStop: this
//         });
//     });
// })*!/
// */
