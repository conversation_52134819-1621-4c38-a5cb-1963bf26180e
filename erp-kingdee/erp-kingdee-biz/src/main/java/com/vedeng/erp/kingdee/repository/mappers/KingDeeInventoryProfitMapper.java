package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.KingDeeInventoryProfitEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * @description: 盘盈单
 * @author: yana.jiang
 * @date: 2022/11/11
 */
public interface KingDeeInventoryProfitMapper {
    /**
     * delete by primary key
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(KingDeeInventoryProfitEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(KingDeeInventoryProfitEntity record);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    KingDeeInventoryProfitEntity selectByPrimaryKey(Integer id);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(KingDeeInventoryProfitEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(KingDeeInventoryProfitEntity record);

    int updateBatchSelective(List<KingDeeInventoryProfitEntity> list);

    int batchInsert(@Param("list") List<KingDeeInventoryProfitEntity> list);
}