package com.wms.model.po;

import java.util.Date;
import java.util.List;

public class WmsInputOrder {
    /** 主键  WMS_INPUT_ORDER_ID **/
    private Integer wmsInputOrderId;

    /** 入库单类型:1-盘盈入库单,  ORDER_TYPE **/
    private Integer orderType;

    /** 入库单号  ORDER_NO **/
    private String orderNo;

    /** 审核状态:0:待审核,1:审核中,2:审核通过,3:审核不通过  VERIFY_STATUS **/
    private Integer verifyStatus;

    /** 入库状态:0-未入库 1-部分入库 2 全部入库  ARRIVAL_STATUS **/
    private Integer arrivalStatus;

    /** 申请入库时间  APPLY_INTIME **/
    private Date applyIntime;

    /** 备注  REMARK **/
    private String remark;

    /** 申请人  APPLYER **/
    private String applyer;

    /** 申请人id  APPLYER_USERID **/
    private Integer applyerUserid;

    /** 申请人部门  APPLYER_DEPARTMENT **/
    private String applyerDepartment;

    /** 申请人部门id  APPLYER_DEPARTMENT_ID **/
    private Integer applyerDepartmentId;

    /** 创建时间  ADD_TIME **/
    private Date addTime;

    /** 更新时间  MODE_TIME **/
    private Date modeTime;

    /** 创建人  CREATOR **/
    private Integer creator;

    /** 更新人  UPDATER **/
    private Integer updater;

    /** 是否删除 0否 1是  IS_DELETE **/
    private Integer isDelete;

    private List<WmsInputOrderGoods> wmsInputOrderGoods;

    /**   主键  WMS_INPUT_ORDER_ID   **/
    public Integer getWmsInputOrderId() {
        return wmsInputOrderId;
    }

    /**   主键  WMS_INPUT_ORDER_ID   **/
    public void setWmsInputOrderId(Integer wmsInputOrderId) {
        this.wmsInputOrderId = wmsInputOrderId;
    }

    /**   入库单类型:1-盘盈入库单,  ORDER_TYPE   **/
    public Integer getOrderType() {
        return orderType;
    }

    /**   入库单类型:1-盘盈入库单,  ORDER_TYPE   **/
    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    /**   入库单号  ORDER_NO   **/
    public String getOrderNo() {
        return orderNo;
    }

    /**   入库单号  ORDER_NO   **/
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    /**   审核状态:0:待审核,1:审核中,2:审核通过,3:审核不通过  VERIFY_STATUS   **/
    public Integer getVerifyStatus() {
        return verifyStatus;
    }

    /**   审核状态:0:待审核,1:审核中,2:审核通过,3:审核不通过  VERIFY_STATUS   **/
    public void setVerifyStatus(Integer verifyStatus) {
        this.verifyStatus = verifyStatus;
    }

    /**   入库状态:0-未入库 1-部分入库 2 全部入库  ARRIVAL_STATUS   **/
    public Integer getArrivalStatus() {
        return arrivalStatus;
    }

    /**   入库状态:0-未入库 1-部分入库 2 全部入库  ARRIVAL_STATUS   **/
    public void setArrivalStatus(Integer arrivalStatus) {
        this.arrivalStatus = arrivalStatus;
    }

    /**   申请入库时间  APPLY_INTIME   **/
    public Date getApplyIntime() {
        return applyIntime;
    }

    /**   申请入库时间  APPLY_INTIME   **/
    public void setApplyIntime(Date applyIntime) {
        this.applyIntime = applyIntime;
    }

    /**   备注  REMARK   **/
    public String getRemark() {
        return remark;
    }

    /**   备注  REMARK   **/
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    /**   申请人  APPLYER   **/
    public String getApplyer() {
        return applyer;
    }

    /**   申请人  APPLYER   **/
    public void setApplyer(String applyer) {
        this.applyer = applyer == null ? null : applyer.trim();
    }

    /**   申请人id  APPLYER_USERID   **/
    public Integer getApplyerUserid() {
        return applyerUserid;
    }

    /**   申请人id  APPLYER_USERID   **/
    public void setApplyerUserid(Integer applyerUserid) {
        this.applyerUserid = applyerUserid;
    }

    /**   申请人部门  APPLYER_DEPARTMENT   **/
    public String getApplyerDepartment() {
        return applyerDepartment;
    }

    /**   申请人部门  APPLYER_DEPARTMENT   **/
    public void setApplyerDepartment(String applyerDepartment) {
        this.applyerDepartment = applyerDepartment == null ? null : applyerDepartment.trim();
    }

    /**   申请人部门id  APPLYER_DEPARTMENT_ID   **/
    public Integer getApplyerDepartmentId() {
        return applyerDepartmentId;
    }

    /**   申请人部门id  APPLYER_DEPARTMENT_ID   **/
    public void setApplyerDepartmentId(Integer applyerDepartmentId) {
        this.applyerDepartmentId = applyerDepartmentId;
    }

    /**   创建时间  ADD_TIME   **/
    public Date getAddTime() {
        return addTime;
    }

    /**   创建时间  ADD_TIME   **/
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**   更新时间  MODE_TIME   **/
    public Date getModeTime() {
        return modeTime;
    }

    /**   更新时间  MODE_TIME   **/
    public void setModeTime(Date modeTime) {
        this.modeTime = modeTime;
    }

    /**   创建人  CREATOR   **/
    public Integer getCreator() {
        return creator;
    }

    /**   创建人  CREATOR   **/
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**   更新人  UPDATER   **/
    public Integer getUpdater() {
        return updater;
    }

    /**   更新人  UPDATER   **/
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    /**   是否删除 0否 1是  IS_DELETE   **/
    public Integer getIsDelete() {
        return isDelete;
    }

    /**   是否删除 0否 1是  IS_DELETE   **/
    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public List<WmsInputOrderGoods> getWmsInputOrderGoods() {
        return wmsInputOrderGoods;
    }

    public void setWmsInputOrderGoods(List<WmsInputOrderGoods> wmsInputOrderGoods) {
        this.wmsInputOrderGoods = wmsInputOrderGoods;
    }
}