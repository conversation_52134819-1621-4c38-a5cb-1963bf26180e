package com.vedeng.infrastructure.kingdee.domain.dto;

import com.vedeng.common.core.base.BaseDto;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶消息baseDto ，定义消息类型
 * @date 2022/8/26 10:18
 */
@Getter
@Setter
@NoArgsConstructor
public class KingDeeMqBaseDto extends BaseDto {

    /**
     * 消息业务类型
     */
    private KingDeeBizEnums kingDeeBizEnums;

    /**
     * 业务类型，根据不同的业务类型进行消费
     * （由KingDeeBizEnums中 business+operation 组成 ： business_operation）
     */
    private String eventType;

    public KingDeeMqBaseDto(KingDeeBizEnums kingDeeBizEnums) {
        this.kingDeeBizEnums = kingDeeBizEnums;
        this.eventType = KingDeeBizEnums.getEventType(kingDeeBizEnums);
    }

    public String getFormId(){
        return kingDeeBizEnums.getFormId();
    }

    public void setKingDeeBizEnums(KingDeeBizEnums kingDeeBizEnums) {
        this.kingDeeBizEnums = kingDeeBizEnums;
        this.eventType = KingDeeBizEnums.getEventType(kingDeeBizEnums);
    }
}
