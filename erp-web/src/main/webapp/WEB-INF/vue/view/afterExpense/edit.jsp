<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>


<div id="app" style="display: none;">

    <el-form v-if="formChangeFlag" :rules="rules" ref="form" :model="buyorderExpenseAfter" :label-position="labelPosition" label-width="80px">

        <el-row style="font-weight: 700">
            <el-form-item label="售后类型">
                <template>
                    <el-radio v-model="buyorderExpenseAfter.expenseAfterSalesType" :label="4121"
                              :disabled="buyorderExpenseAfterId != null && buyorderExpenseAfterId !== ''" @change="AfterSalesTypeChange()">退货
                    </el-radio>
                    <el-radio v-model="buyorderExpenseAfter.expenseAfterSalesType" :label="4122" v-if="buyorderExpenseAfter.expenseAfterSalesReason !== 4284"
                              :disabled="buyorderExpenseAfterId != null && buyorderExpenseAfterId !== ''" @change="AfterSalesTypeChange()">仅退票
                    </el-radio>
                </template>
            </el-form-item>

        </el-row>

        <el-row style="font-weight: 700">
            <el-form-item label="售后原因" prop="expenseAfterSalesReason">
                <template>
                    <el-radio v-if="buyorderExpenseAfter.expenseAfterSalesType == 4121 && buyorderExpenseAfter.expenseAfterSalesReason !== 4284"
                              v-model="buyorderExpenseAfter.expenseAfterSalesReason" :label="4200">退货
                    </el-radio>
                    <el-radio v-if="buyorderExpenseAfter.expenseAfterSalesType == 4121 && buyorderExpenseAfter.expenseAfterSalesReason !== 4284"
                              v-model="buyorderExpenseAfter.expenseAfterSalesReason" :label="4201">退款
                    </el-radio>
                    <el-radio v-if="buyorderExpenseAfter.expenseAfterSalesType == 4121 && buyorderExpenseAfter.expenseAfterSalesReason !== 4284"
                              v-model="buyorderExpenseAfter.expenseAfterSalesReason" :label="4202">重做
                    </el-radio>
                    <el-radio v-if="buyorderExpenseAfter.expenseAfterSalesType == 4121 && buyorderExpenseAfter.expenseAfterSalesReason !== 4284"
                              v-model="buyorderExpenseAfter.expenseAfterSalesReason" :label="4203">其他
                    </el-radio>
                    <el-radio v-if="buyorderExpenseAfter.expenseAfterSalesType == 4121 && buyorderExpenseAfter.expenseAfterSalesReason == 4284"
                              v-model="buyorderExpenseAfter.expenseAfterSalesReason" :label="4284" :disabled="true">销售联动退货
                    </el-radio>


                    <el-radio v-if="buyorderExpenseAfter.expenseAfterSalesType == 4122"
                              v-model="buyorderExpenseAfter.expenseAfterSalesReason" :label="4204">供应商开错且已跨月
                    </el-radio>
                    <el-radio v-if="buyorderExpenseAfter.expenseAfterSalesType == 4122"
                              v-model="buyorderExpenseAfter.expenseAfterSalesReason" :label="4205">发票录错
                    </el-radio>
                    <el-radio v-if="buyorderExpenseAfter.expenseAfterSalesType == 4122"
                              v-model="buyorderExpenseAfter.expenseAfterSalesReason" :label="4206">其他
                    </el-radio>

                </template>
            </el-form-item>
        </el-row>

        <el-card class="box-card" v-if="buyorderExpenseAfter.expenseAfterSalesType == 4121">
            <div slot="header" class="clearfix">
                <span>选择商品</span>
            </div>
            <template>
                <el-table :data="buyorderExpenseAfter.expenseItemList" border
                          key="expenseItemList"
                          ref="expenseItem"
                          :row-key="getItemRowKey"
                          :cell-style="{'text-align':'center'}"
                          :header-cell-style="{'text-align':'center'}"
                          @selection-change="handleItemSelect">
                    <el-table-column
                            type="selection"
                            :reserve-selection="true"
                            :selectable="selectExpenseItem"
                            min-width="5%">
                    </el-table-column>
                    <el-table-column
                            prop="buyorderExpenseItemDetailDto.sku"
                            label="订货号"
                            min-width="10%">
                    </el-table-column>
                    <el-table-column
                            label="产品名称"
                            min-width="30%">
                            <template slot-scope="scope">
                                <el-link type="primary" :underline="false" @click="viewSkuInfo(scope.row.goodsId)">{{ scope.row.buyorderExpenseItemDetailDto.goodsName }}</el-link>
                            </template>
                    </el-table-column>
                    <el-table-column
                            prop="buyorderExpenseItemDetailDto.expenseCategoryName"
                            label="费用类别"
                            min-width="10%">
                    </el-table-column>
                    <el-table-column
                            prop="num"
                            label="采购数量"
                            min-width="10%">
                    </el-table-column>
                    <el-table-column
                            prop="buyorderExpenseItemDetailDto.price"
                            label="单价"
                            min-width="10%">
                    </el-table-column>
                    <el-table-column label="总额" min-width="10%">
                        <template slot-scope="scope">
                            <span>{{scope.row.num * scope.row.buyorderExpenseItemDetailDto.price}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="售后数量/可售后数量"
                            min-width="20%">
                            <template slot-scope="scope">
                                <el-form-item label-width="0" :prop="'expenseItemList.' + scope.$index"
                                              :rules="rules.returnNum">
                                    <el-input v-model="scope.row.returnNum"
                                              @input="handleReturnNumValue(scope.row)"
                                              :disabled="buyorderExpenseAfter.expenseAfterSalesReason == 4284"
                                              style="width: 150px;">
                                    </el-input>
                                    <span>/{{scope.row.afterSalesQuantity}}</span>
                                </el-form-item>
                            </template>
                    </el-table-column>
                    <el-table-column
                            prop="invoicedNum"
                            label="已收票数量"
                            min-width="10%">
                    </el-table-column>
                </el-table>
            </template>
        </el-card>


        <el-card class="box-card" v-if="buyorderExpenseAfter.expenseAfterSalesType == 4122">
            <div slot="header" class="clearfix">
                <span>请选择退票商品及发票</span>
            </div>
            <template>
                <el-table :data="buyorderExpenseAfter.itemAndInvoiceDtoList" border
                          key="itemAndInvoiceDtoList"
                          ref="invoiceItemList"
                          :row-key="getInvoiceRowKey"
                          :cell-style="{'text-align':'center'}"
                          :header-cell-style="{'text-align':'center'}"
                          @selection-change="handleInvoiceSelect">
                    <el-table-column
                            label="选择"
                            type="selection"
                            min-width="5%">
                    </el-table-column>
                    <el-table-column
                            label="产品名称"
                            min-width="30%">
                            <template slot-scope="scope">
                                <el-link type="primary" :underline="false" @click="viewSkuInfo(scope.row.goodsId)">{{ scope.row.goodsName}}</el-link><br>
                                <span>{{ scope.row.sku}}</span>
                            </template>
                    </el-table-column>
                    <el-table-column
                            prop="brandName"
                            label="品牌"
                            min-width="10%">
                    </el-table-column>
                    <el-table-column
                            prop="model"
                            label="型号"
                            min-width="10%">
                    </el-table-column>
                    <el-table-column
                            prop="price"
                            label="采购价"
                            min-width="5%">
                    </el-table-column>
                    <el-table-column
                            prop="num"
                            label="数量"
                            min-width="5%">
                    </el-table-column>
                    <el-table-column
                            prop="unitName"
                            label="单位"
                            min-width="10%">
                    </el-table-column>
                    <el-table-column
                            prop="invoiceNum"
                            label="收票数"
                            min-width="5%">
                    </el-table-column>
                    <el-table-column
                            prop="invoiceNo"
                            label="发票号"
                            min-width="10%">
                    </el-table-column>
                    <el-table-column
                            label="本次退票数（不可大于收票数）"
                            min-width="10%">
                            <template slot-scope="scope">
                                <el-form-item label-width="0" :prop="'itemAndInvoiceDtoList.' + scope.$index"
                                              :rules="rules.returnNumInvoice">
                                    <el-input v-model="scope.row.returnNum"
                                              @input="handleReturnInvoiceNum(scope.row)"
                                              style="width: 80%;">
                                    </el-input>
                                </el-form-item>
                            </template>
                    </el-table-column>
                </el-table>
            </template>
        </el-card>


        <el-row style="font-weight: 700">
            <el-form-item label="详情说明" prop="expenseAfterSalesComments">
                <template>
                    <el-input
                            type="textarea"
                            :rows="2"
                            maxlength="255"
                            style="width: 92%"
                            :placeholder="placeholder"
                            v-model="buyorderExpenseAfter.expenseAfterSalesComments">
                    </el-input>
                </template>
            </el-form-item>
        </el-row>

        <el-row style="font-weight: 700" v-if="buyorderExpenseAfter.expenseAfterSalesType == 4121">
            <el-form-item label="联系人" prop="contact">
                <template>
                    <el-select v-model="buyorderExpenseAfter.traderContactId" placeholder="请选择">
                        <el-option
                                v-for="item in traderContacts"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                        </el-option>
                    </el-select>
                </template>
            </el-form-item>
        </el-row>

        <el-row style="font-weight: 700" v-if="buyorderExpenseAfter.expenseAfterSalesType == 4121">
            <el-form-item label="款项退还" prop="refundMethod">
                <template>
                    <el-radio v-model="buyorderExpenseAfter.refundMethod" :label="1">退至公司账户（由财务结算）</el-radio>
                    <el-radio v-model="buyorderExpenseAfter.refundMethod" :label="2">退至供应商余额（可用于支付该供应商其他订单）</el-radio>
                </template>
            </el-form-item>
        </el-row>

        <el-row>
            <el-form-item label="上传附件">
                <template>
                    <el-upload
                            limit="50"
                            action="/fileUpload/ajaxFileUpload.do"
                            :on-success="handleAvatarSuccess"
                            :on-remove="handleRemove"
                            :before-upload="beforeAvatarUpload"
                            accept=".jpg, .png, .doc, .docx, .pdf"
                            :file-list="fileList"
                            name="lwfile">

                        <el-button size="small" type="primary">浏览</el-button>
                    </el-upload>
                </template>
            </el-form-item>
        </el-row>

        <el-form-item v-if="buyorderExpenseAfter.expenseAfterSalesType == 4121">
            <span>退货须知：</span><br>
            <span>1、上传附件不得超过2M，可以上传jpg、png、doc、docx、pdf等格式</span><br>
            <span>2、实际退款金额根据商品价值决定</span><br>
            <el-button type="primary" size="small" @click="onSubmit('form')">提交</el-button>
        </el-form-item>

        <el-form-item v-if="buyorderExpenseAfter.expenseAfterSalesType == 4122">
            <span>退票须知：</span><br>
            <span>1、如果是供应商开错且是当月，则无需创建售后单，只需尽快通知供应商重新开票，将原蓝字有效票作废。系统自动抓取到作废票后会自动处理（供应商开票后1-2天，系统会拉取到，若供应商已开出，紧急情况可联系财务进行人工拉取作废票），之后即可重新录票。</span><br>
            <span>2、选择此售后，只能基于当前订单做所选发票进行红冲，不影响其他订单。若其他订单亦需退票，请自行给对应订单创建退票售后单。</span><br>
            <el-button type="primary" size="small" @click="onSubmit('form')">提交</el-button>
        </el-form-item>

    </el-form>
</div>

<script src="${pageContext.request.contextPath}/static/api/expenseAftersale/expenseAfterSales.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/api/trader/traderSupplier.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript">

    const buyorderExpenseAfterId = '${expenseAfterSalesId}';
    const buyorderExpenseId = '${buyorderExpenseId}';
    const traderId = '${traderId}';

    new Vue({
        el: '#app',
        data() {

            let checkReturnNum = (rule, value, callback) => {
                let flag = false;
                this.itemReturnList.forEach((item) => {
                    if(item.buyorderExpenseItemId === value.buyorderExpenseItemId){
                        flag = true;
                    }
                });
                if(flag){
                    if (value.returnNum === undefined || value.returnNum === '' || value.returnNum === null) {
                        callback(new Error("退货数量不可为空"));
                    }
                    if (Number(value.returnNum) === 0) {
                        callback(new Error("退货数量不可为0"));
                    }
                    if (Number(value.returnNum) - Number(value.afterSalesQuantity) > 0) {
                        callback(new Error("退货数量不能超过可退货数量"));
                    }
                    callback();
                }else{
                    callback();
                }

            };

            let checkReturnInvoiceNum = (rule, value, callback) => {
                let flag = false;
                this.invoiceReturnList.forEach(item => {
                    if (item.buyorderExpenseItemId === value.buyorderExpenseItemId && item.invoiceNo === value.invoiceNo && item.invoiceCode === value.invoiceCode) {
                        flag = true;
                    }
                });

                if (flag) {
                    if (value.returnNum === undefined || value.returnNum === '' || value.returnNum === null) {
                        callback(new Error("退票数量不可为空"));
                    }
                    if (Number(value.returnNum) === 0) {
                        callback(new Error("退票数量不可为0"));
                    }
                    if (Number(value.returnNum) - Number(value.invoiceNum) > 0) {
                        // callback(new Error("商品" + value.sku + "当前实际可退票数为" +  Number(value.invoiceNum) + ",提交有误，请核实！"));
                        callback(new Error("该商品当前可退票数为" +  Number(value.invoiceNum)));
                    }
                    callback();
                } else{
                    callback();
                }

            };



            return {
                tempFileName: '',

                buyorderExpenseAfter: {
                    expenseItemList: [],
                    itemAndInvoiceDtoList: [],
                    refundMethod: '',
                    attachmentList: [
                        {
                            attachmentType: 0,
                            attachmentFunction: 0,
                            relatedId: 0,
                            name: "",
                            domain: "",
                            uri: "",
                            suffix: "",
                            ossResourceId: ""
                        }
                    ]
                },
                rules: {
                    expenseAfterSalesReason: [{required: true}],
                    refundMethod: [{required: true}],
                    expenseAfterSalesComments: [{required: true, message: '请填写详情说明', trigger: 'blur'}],
                    returnNum: [
                        {validator: checkReturnNum, trigger: 'blur'}

                    ],
                    returnNumInvoice: [
                        {validator: checkReturnInvoiceNum, trigger: 'blur'}
                    ]
                },
                labelPosition: 'right',

                // 客户联系人信息集合
                traderContacts: [],
                // 选中的退货商品集合
                itemReturnList: [],
                // 选中的退票信息集合
                invoiceReturnList: [],

                fileList: [],

                placeholder: "请详述客户需求及退货要求，以便售后同事联系",

                formChangeFlag: true
            }
        },

        mounted() {
            loadingApp();
        },

        created() {

            // 查询退货商品列表信息
            getAddEditInfo({
                "buyorderExpenseId": buyorderExpenseId,
                "expenseAfterSalesId": buyorderExpenseAfterId
            }).then(res => {
                this.buyorderExpenseAfter = res.data.data;
                this.buyorderExpenseAfter.itemAndInvoiceDtoList = res.data.data.itemAndInvoiceDtoList;

                if (buyorderExpenseAfterId) {
                    if (this.buyorderExpenseAfter.attachmentList.length > 0) {
                        this.buyorderExpenseAfter.attachmentList.forEach(item => {
                            let image = {
                                "name": "",
                                "url": ""
                            };
                            image.url = item.uri;
                            image.name = item.name;
                            this.fileList.push(image);
                        })
                    }
                } else {
                    this.buyorderExpenseAfter.attachmentList = [];
                }

                if (this.buyorderExpenseAfter.expenseAfterSalesType === 4121) {
                    res.data.data.expenseItemList.forEach(item => {
                        if (item.expenseAfterSalesItemId) { //判断条件，根据自己的项目进行添加
                            this.$nextTick(() => {
                                this.$refs.expenseItem.toggleRowSelection(item);
                            })
                        }
                    });
                }

                if (this.buyorderExpenseAfter.expenseAfterSalesType === 4122) {
                    this.placeholder = '请详细描述退票原因';
                    res.data.data.itemAndInvoiceDtoList.forEach(item => {
                        if (item.expenseAfterSalesInvoiceId) { //判断条件，根据自己的项目进行添加
                            this.$nextTick(() => {
                                this.$refs.invoiceItemList.toggleRowSelection(item);
                            })
                        } else {
                            // id为空，证明是新增或者 编辑时没被选中的商品，此时退票数量给默认值收票数量
                            item.returnNum = item.invoiceNum;
                        }
                    });
                }

                if (buyorderExpenseAfterId == 0) {
                    this.buyorderExpenseAfter.expenseAfterSalesType = 4121;
                    this.buyorderExpenseAfter.expenseAfterSalesReason = 4200;
                    this.buyorderExpenseAfter.refundMethod = 1;
                }
            });


            // 查询供应商联系人集合
            getTraderConcatData(traderId).then(res => {
                res.data.data.list.forEach(item => {
                    let contract = {
                        label: item.name + '/' + item.mobile + '/' + item.telephone,
                        value: item.traderContactId
                    };
                    this.traderContacts.push(contract)
                })
            })
        },
        methods: {
            // 售后原因 联动
            AfterSalesTypeChange() {
                this.buyorderExpenseAfter.attachmentList = [];
                this.buyorderExpenseAfter.expenseAfterSalesReason = this.buyorderExpenseAfter.expenseAfterSalesType === '4121' ? '4200' : '4204';
                if (this.buyorderExpenseAfter.expenseAfterSalesType == '4121') {
                    this.buyorderExpenseAfter.expenseAfterSalesReason = 4200;
                    getExpenseItemInfoList({"buyorderExpenseId": buyorderExpenseId}).then(res => {
                        this.buyorderExpenseAfter.expenseItemList = res.data.data;
                    })
                }
                if (this.buyorderExpenseAfter.expenseAfterSalesType == '4122') {
                    this.placeholder = '请详细描述退票原因';
                    this.buyorderExpenseAfter.expenseAfterSalesReason = 4204;
                    getExpenseItemAndInvoiceList({"buyorderExpenseId": buyorderExpenseId}).then(res => {
                        this.buyorderExpenseAfter.itemAndInvoiceDtoList = res.data.data;
                        if (this.buyorderExpenseAfter.itemAndInvoiceDtoList.length > 0) {
                            this.buyorderExpenseAfter.itemAndInvoiceDtoList.forEach(item => {
                                if (!item.expenseAfterSalesInvoiceId) {
                                    item.returnNum = item.invoiceNum;
                                }
                            })
                        }
                    });
                }

                this.formChangeFlag = false;

                setTimeout(() => {
                    this.formChangeFlag = true;
                })
            },

            // 退货商品列表选中事件
            handleItemSelect(val) {
                this.itemReturnList = val;
            },

            // 退票列表选中事件
            handleInvoiceSelect(val) {
                this.invoiceReturnList = val;
            },

            // 提交表单
            onSubmit(form) {
                this.$refs[form].validate(valid => {
                    if (valid) {
                        if (this.itemReturnList.length === 0 && this.invoiceReturnList.length === 0) {
                            this.$message({
                                message: "请选择售后商品",
                                type: 'error',
                            });
                            return;
                        }

                        if (this.invoiceReturnList.length > 0) {
                            let tempInvoiceNoList = [];
                            this.invoiceReturnList.forEach(item => {
                                if (tempInvoiceNoList.indexOf(item.invoiceNo) == -1) {
                                    tempInvoiceNoList.push(item.invoiceNo);
                                }
                            });

                            if(tempInvoiceNoList.length > 1){
                                this.$message({
                                    message: "仅退票只支持相同票号的商品!",
                                    type: 'error',
                                });
                                return;
                            }
                        }

                        this.buyorderExpenseAfter.expenseItemList = this.itemReturnList;
                        this.buyorderExpenseAfter.itemAndInvoiceDtoList = this.invoiceReturnList;
                        this.buyorderExpenseAfter.buyorderExpenseId = buyorderExpenseId;
                        this.buyorderExpenseAfter.buyorderExpenseAfterId = buyorderExpenseAfterId;
                        this.buyorderExpenseAfter.traderId = traderId;

                        // 退货时计算totalAmount
                        let totalAmount = 0;
                        if (this.itemReturnList.length > 0) {
                            this.itemReturnList.forEach(item => {
                                totalAmount = totalAmount + item.buyorderExpenseItemDetailDto.price * item.returnNum
                            })
                        }
                        this.buyorderExpenseAfter.totalAmount = totalAmount;

                        if (!buyorderExpenseAfterId) {
                            saveAddExpenseAfterSale(this.buyorderExpenseAfter).then(res => {
                                if (res.data.success) {
                                    openTab("费用售后详情", '/buyorderExpense/aftersale/detail.do?expenseAfterSalesId=' + res.data.data);
                                    this.closeThis()
                                } else {
                                    this.$message({
                                        message: res.data.message,
                                        type: 'error',
                                    });
                                }
                            })

                        } else {
                            saveEditExpenseAfterSale(this.buyorderExpenseAfter).then(res => {
                                if (res.data.success) {
                                    openTab("费用售后详情", '/buyorderExpense/aftersale/detail.do?expenseAfterSalesId=' + res.data.data);
                                    this.closeThis()
                                } else {
                                    this.$message({
                                        message: res.data.message,
                                        type: 'error',
                                    });
                                }
                            })
                        }

                    }
                })
            },

            closeThis() {
                window.parent.closableTab.close($(window.parent.document).find('[role=presentation].active .glyphicon.small'), window.parent.closableTab.resizeMove);
            },

            getItemRowKey(row) {
                return row.buyorderExpenseItemId;
            },

            getInvoiceRowKey(row) {
                return row.buyorderExpenseItemId;
            },

            // 校验退货单的输入数量
            handleReturnNumValue(row) {
                row.returnNum = this.limitReturnNum(row.returnNum);
            },

            // 校验退票单的输入数量
            handleReturnInvoiceNum (row) {
                row.returnNum = this.limitReturnInvoiceNum(row.returnNum);
            },

            limitReturnNum(returnNum) {
                if (returnNum != undefined) {
                    returnNum = returnNum.replace(/[^0-9]/g, '');// 只能输入数字
                    returnNum = returnNum.replace(/^(0+)|[^\d]+/g, '');// 第一位0开头，0后面为数字，则过滤掉，取后面的数字
                }
                return returnNum;
            },

            limitReturnInvoiceNum(returnNum) {
                let str = returnNum;
                let len1 = str.substr(0, 1);
                let len2 = str.substr(1, 1);
                //如果第一位是0，第二位不是点，就用数字把点替换掉
                if (str.length > 1 && len1 == 0 && len2 != '.') {
                    str = str.substr(1, 1);
                }
                //第一位不能是.
                if (len1 == '.') {
                    str = '';
                }
                //限制只能输入一个小数点
                if (str.indexOf('.') != -1) {
                    let str_ = str.substr(str.indexOf('.') + 1);
                    if (str_.indexOf('.') != -1) {
                        str = str.substr(0, str.indexOf('.') + str_.indexOf('.') + 1);
                    }
                }
                //正则替换
                str = str.replace(/[^\d\.]+/g, ''); // 保留数字和小数点
                str = str.replace(/(\.\d{2})\d*/, '$1'); // 小数点后只能输两位
                return str;
        },

            handleRemove(file) {
                let nameList = [];
                this.buyorderExpenseAfter.attachmentList.forEach(item => {
                    nameList.push(item.name)
                });
                let  index = nameList.indexOf(file.name);
                this.buyorderExpenseAfter.attachmentList.splice(index, 1);
            },

            handleAvatarSuccess(file) {
                if (file.code == -1) {
                    this.$message({
                        message: file.message,
                        type: 'error'
                    });
                    this.buyorderExpenseAfter.attachmentList.splice(-1 ,1);
                    this.fileList.splice(-1 ,1);
                    return;
                }
                let tempFile = {
                    attachmentType: 4134,
                    attachmentFunction: 4134,
                    // 新增时取不到售后单id，因此这边统一设置为0，到接口内取id
                    relatedId: 0,
                    name: this.tempFileName,
                    domain: file.httpUrl,
                    uri: file.filePath,
                    suffix: file.prefix,
                    ossResourceId: file.ossResourceId
                };
                this.buyorderExpenseAfter.attachmentList.push(tempFile);

                this.tempFileName = '';
            },

            beforeAvatarUpload(file) {
                this.tempFileName = file.name;

                let img = file.name.substring(file.name.lastIndexOf('.') + 1);
                img = img.toLocaleLowerCase();
                const suffix = img === 'jpg';
                const suffix2 = img === 'png';
                const suffix3 = img === 'doc';
                const suffix4 = img === 'docx';
                const suffix5 = img === 'pdf';
                if (!suffix && !suffix2 && !suffix3 && !suffix4 && !suffix5) {
                    this.$message.error("只能上传jpg、png、doc、docx、pdf等格式！");
                    return false
                }

                const isLt2M = file.size / 1024 / 1024 <= 2;
                if (!isLt2M) {
                    this.$message.error('上传附件大小不能超过 2MB!');
                    return false
                }
                return true;
            },

            // 查看商品信息
            viewSkuInfo(id) {
                openTab("商品信息", '/goods/goods/viewbaseinfo.do?goodsId=' + id);
            },
            selectExpenseItem(row, index){
                if(this.buyorderExpenseAfter.expenseAfterSalesReason == 4284){
                    // setTimeout(() => {
                    //     document.querySelector(".el-table-column--selection").querySelector(".el-checkbox__input").classList.add("is-disabled");
                    //     document.querySelector(".el-table-column--selection").querySelector(".el-checkbox").classList.add("is-disabled");
                    //     document.querySelector(".el-table-column--selection").querySelector(".el-checkbox__original").setAttribute("disabled", "disabled");
                    // }, 100);
                    return false;
                }else{
                    return true;
                }

            },
            renderHeader(h, {column}){
                debugger
                console.log(column)
            }

        }

    })
</script>

<style>
    .el-card__header {
        height: 34px;
        background-color: #d7d7d7;
        box-sizing: border-box;
        padding-left: 15px;
        font-weight: 700;
        padding-top: 9px;
        padding-bottom: 9px;
        color: #303133;
    }

    .el-card {
        margin-bottom: 20px;
    }

    .el-card__body {
        padding: 0;
    }

    .el-row {
        font-weight: 700;
    }

    .el-upload-list__item-status-label {
        right: 60%;
    }

    .el-upload-list__item .el-icon-close {
        right: 60%;
    }

    .el-upload-list__item .el-icon-close-tip {
        right: 60%;
    }


    .el-form-item__error {
        z-index: 99999 !important;
    }


</style>