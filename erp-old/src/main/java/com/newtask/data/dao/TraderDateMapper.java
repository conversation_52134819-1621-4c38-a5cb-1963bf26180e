package com.newtask.data.dao;


import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


public interface TraderDateMapper{


    List<Integer> getTraderId(@Param("startTime") Long startTime,@Param("endTime") Long endTime);

    List<Map<String, Object>> findAfterSaleCountAndAmount(@Param("bizIds") List<Integer> bizIds);

    Long makeExist(Integer traderId);

    void insertTraderId(@Param("traderId") Integer traderId,@Param("nowDate") Long nowDate);

    void updateAfterSaleCountAndAmount(@Param("traderId")Integer traderId,@Param("afterSaleCount") Integer afterSaleCount,
                                       @Param("afterSaleTotalAmount") Double afterSaleTotalAmount, @Param("nowDate") Long nowDate);

    List<Integer> getSaleOrderTraderId(@Param("startTime") String startTime,@Param("endTime") String endTime);

    List<Map<String, Object>> findSaleOrderInfo(@Param("bizIds") List<Integer> bizIds);

    void updateSaleOrderInfo(@Param("traderId")Integer traderId,@Param("orderCount") Integer orderCount,
               @Param("orderTotalAmount")Double orderTotalAmount,@Param("firstTraderTime") Long firstTraderTime,
                             @Param("lastTraderTime") Long lastTraderTime,@Param("nowDate") Long nowDate);

    List<Map<String, Object>> findCheckStatus(@Param("startTime") Long startTime,@Param("endTime") Long endTime);

    void updateCheckStatus(@Param("traderId") Integer traderId, @Param("traderAttrCheckStatus") Integer traderAttrCheckStatus,@Param("nowDate")Long nowDate);

    List<Map<String, Object>> findAttrCheckStatus(@Param("startTime") Long startTime,@Param("endTime") Long endTime);

    void updateAttrCheckStatus(@Param("traderId") Integer traderId,@Param("traderAttrCheckStatus") Integer traderAttrCheckStatus,@Param("nowDate") Long nowDate);

    List<Integer> getQuteNumTraderId(@Param("startTime")Long startTime,@Param("endTime") Long endTime);

    List<Map<String, Object>> findQuteCount(@Param("bizIds") List<Integer> bizIds);

    void updateQuteCount(@Param("traderId") Integer traderId,@Param("quoteNum") Integer quoteNum,@Param("nowDate") Long nowDate);

    List<Integer> getReceiveTotalAmoountTraderId(@Param("startTime") Long startTime,@Param("endTime") Long endTime);

    List<Map<String, Object>> findReceiveTotalAmoount(@Param("bizIds") List<Integer> bizIds);

    void updateReceiveTotalAmoount(@Param("traderId") Integer traderId,@Param("receiveTotalAmount") Double receiveTotalAmount,@Param("nowDate") Long nowDate);

    List<Integer> getSkuAndBrandNumTraderId(@Param("startTime") String startTime,@Param("endTime") String endTime
    ,@Param("startTimeLong") Long startTimeLong,@Param("endTimeLong") Long endTimeLong);

    List<Map<String, Object>> findSkuNumAndBrandNum(@Param("bizIds") List<Integer> bizIds);

    void updateSkuNumAndBrandNum(@Param("traderId") Integer traderId, @Param("orderSkuCount") Integer orderSkuCount,
                                 @Param("orderBrandCount") Integer orderBrandCount,@Param("nowDate") Long nowDate);

    List<Integer> getCommunicateNumTraderId(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    List<Map<String, Object>> findCommunicateNum(@Param("bizIds") List<Integer> bizIds);

    void updateCommunicateNum(@Param("traderId") Integer traderId,@Param("communcateCount") Integer communcateCount,
                              @Param("lastCommuncateTime") Long lastCommuncateTime,@Param("nowDate") Long nowDate);

    List<Integer> getPhoneNumTraderId(@Param("startTime") Long startTime,@Param("endTime") Long endTime);

    List<Map<String, Object>> findPhoneNum(@Param("bizIds")List<Integer> bizIds);

    void updatePhoneNum(@Param("traderId") Integer traderId,@Param("mobileCount") Integer mobileCount,@Param("nowDate") Long nowDate);

    List<Integer> getBussinessChanceNumTraderId(@Param("startTime") Long startTime,@Param("endTime") Long endTime);

    List<Map<String, Object>> findBussinessChanceNum(@Param("bizIds")List<Integer> bizIds);

    void updateBussinessChanceNum(@Param("traderId") Integer traderId, @Param("bussinessChanceNum") Integer bussinessChanceNum,@Param("nowDate") Long nowDate);
}
