<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.newtask.ddi.dao.DdiGoodsStockExtMapper">
    <resultMap id="BaseResultMap" type="com.wms.model.ddi.DdiGoodsStockExtDto">
        <id column="T_DDI_GOODS_STOCK_ID" jdbcType="INTEGER" property="tDdiGoodsStockId"/>
        <result column="DISTRIBUTOR" jdbcType="VARCHAR" property="distributor"/>
        <result column="QUOTE_ID" jdbcType="VARCHAR" property="quoteId"/>
        <result column="STORAGE_TIME" jdbcType="TIMESTAMP" property="storageTime"/>
        <result column="STOCK_DURATION" jdbcType="BIGINT" property="stockDuration"/>
        <result column="GOODS_TYPE" jdbcType="VARCHAR" property="goodsType"/>
        <result column="GOODS_NAME" jdbcType="VARCHAR" property="goodsName"/>
        <result column="GOODS_MODEL" jdbcType="VARCHAR" property="goodsModel"/>
        <result column="REGISTRATION_NUMBER" jdbcType="VARCHAR" property="registrationNumber"/>
        <result column="SERIAL_NUMBER" jdbcType="VARCHAR" property="serialNumber"/>
        <result column="EFFECTIVE_DAYS" jdbcType="TIMESTAMP" property="effectiveDays"/>
        <result column="NUM" jdbcType="INTEGER" property="num"/>
        <result column="UNIT" jdbcType="VARCHAR" property="unit"/>
        <result column="SUPPLIER_NAME" jdbcType="VARCHAR" property="supplierName"/>
        <result column="SUPPLIER_ID" jdbcType="INTEGER" property="supplierId"/>
        <result column="MANUFACTURER" jdbcType="VARCHAR" property="manufacturer"/>
        <result column="STOCK_STATUS" jdbcType="VARCHAR" property="stockStatus"/>
        <result column="STOCK_TIME" jdbcType="TIMESTAMP" property="stockTime"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="MODE_TIME" jdbcType="TIMESTAMP" property="modeTime"/>
        <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
    </resultMap>
    <sql id="Base_Column_List">
    T_DDI_GOODS_STOCK_ID, DISTRIBUTOR, QUOTE_ID, STORAGE_TIME, STOCK_DURATION, GOODS_TYPE, 
    GOODS_NAME, GOODS_MODEL, REGISTRATION_NUMBER, SERIAL_NUMBER, EFFECTIVE_DAYS, NUM, 
    UNIT, SUPPLIER_NAME, SUPPLIER_ID, MANUFACTURER, STOCK_STATUS, STOCK_TIME, ADD_TIME, 
    MODE_TIME, IS_DELETE, CREATOR, UPDATER
  </sql>
    <insert id="insertSingleOrder" keyColumn="T_DDI_GOODS_STOCK_ID" keyProperty="tDdiGoodsStockId"
            parameterType="com.wms.model.ddi.DdiGoodsStockExtDto" useGeneratedKeys="true">
        insert into T_DDI_GOODS_STOCK
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="distributor != null">
                DISTRIBUTOR,
            </if>
            <if test="quoteId != null">
                QUOTE_ID,
            </if>
            <if test="saleorderNo != null">
                SALEORDER_NO,
            </if>
            <if test="storageTime != null">
                STORAGE_TIME,
            </if>
            <if test="stockDuration != null">
                STOCK_DURATION,
            </if>
            <if test="goodsType != null">
                GOODS_TYPE,
            </if>
            <if test="goodsName != null">
                GOODS_NAME,
            </if>
            <if test="goodsModel != null">
                GOODS_MODEL,
            </if>
            <if test="registrationNumber != null">
                REGISTRATION_NUMBER,
            </if>
            <if test="serialNumber != null">
                SERIAL_NUMBER,
            </if>
            <if test="effectiveDays != null">
                EFFECTIVE_DAYS,
            </if>
            <if test="num != null">
                NUM,
            </if>
            <if test="unit != null">
                UNIT,
            </if>
            <if test="supplierName != null">
                SUPPLIER_NAME,
            </if>
            <if test="supplierId != null">
                SUPPLIER_ID,
            </if>
            <if test="manufacturer != null">
                MANUFACTURER,
            </if>
            <if test="stockStatus != null">
                STOCK_STATUS,
            </if>
            <if test="stockTime != null">
                STOCK_TIME,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="modeTime != null">
                MODE_TIME,
            </if>
            <if test="isDelete != null">
                IS_DELETE,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="distributor != null">
                #{distributor,jdbcType=VARCHAR},
            </if>
            <if test="quoteId != null">
                #{quoteId,jdbcType=VARCHAR},
            </if>
            <if test="saleorderNo != null">
                #{saleorderNo,jdbcType=VARCHAR},
            </if>
            <if test="storageTime != null">
                #{storageTime,jdbcType=TIMESTAMP},
            </if>
            <if test="stockDuration != null">
                #{stockDuration,jdbcType=BIGINT},
            </if>
            <if test="goodsType != null">
                #{goodsType,jdbcType=VARCHAR},
            </if>
            <if test="goodsName != null">
                #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="goodsModel != null">
                #{goodsModel,jdbcType=VARCHAR},
            </if>
            <if test="registrationNumber != null">
                #{registrationNumber,jdbcType=VARCHAR},
            </if>
            <if test="serialNumber != null">
                #{serialNumber,jdbcType=VARCHAR},
            </if>
            <if test="effectiveDays != null">
                #{effectiveDays,jdbcType=TIMESTAMP},
            </if>
            <if test="num != null">
                #{num,jdbcType=INTEGER},
            </if>
            <if test="unit != null">
                #{unit,jdbcType=VARCHAR},
            </if>
            <if test="supplierName != null">
                #{supplierName,jdbcType=VARCHAR},
            </if>
            <if test="supplierId != null">
                #{supplierId,jdbcType=INTEGER},
            </if>
            <if test="manufacturer != null">
                #{manufacturer,jdbcType=VARCHAR},
            </if>
            <if test="stockStatus != null">
                #{stockStatus,jdbcType=VARCHAR},
            </if>
            <if test="stockTime != null">
                #{stockTime,jdbcType=TIMESTAMP},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modeTime != null">
                #{modeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=BOOLEAN},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>


</mapper>