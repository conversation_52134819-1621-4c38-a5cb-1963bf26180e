package com.vedeng.erp.kingdee.domain.command;

import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@Data
@Getter
@Setter
public class KingDeeSaleorderContractCommand {
    /**
     * 云星空系统单据FID值
     */
    private Integer FID;

    /**
     * 组织
     */
    private KingDeeNumberCommand F_QZOK_OrgId = new KingDeeNumberCommand();

    /**
     * 合同号
     */
    private String F_QZOK_HTH;

    /**
     * 合同日期
     */
    private String F_QZOK_HTRQ;

    /**
     * 合同金额
     */
    private String F_QZOK_HTJE;

    /**
     * 税率
     */
    private String F_QZOK_SLL;

    /**
     * 销售订单
     */
    private String F_QZOK_DDH;

    private String FBillNo;
}
