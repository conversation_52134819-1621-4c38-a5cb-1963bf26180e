.vd-input-number-wrap {
    display: flex;
    position: relative;
    width: 90px;
}

.vd-input-number-wrap .vd-number-btn {
    height: 30px;
    line-height: 30px;
    font-size: 12px;
    display: inline-block;
    vertical-align: middle;
    text-align: center;
    color: #333;
    border: solid 1px #ced2d9;
    background-color: #f5f7fa;
    border-radius: 3px;
    cursor: pointer;
    box-sizing: border-box;
    width: 26px;
}

.vd-input-number-wrap .vd-number-btn:first-child {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.vd-input-number-wrap .vd-number-btn:last-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.vd-input-number-wrap .vd-number-btn:hover {
    background-color: #e6ecf2;
    color: #333;
}

.vd-input-number-wrap .vd-number-btn.disabled {
    color: #999;
    border: solid 1px #e6ebf2;
    background-color: #f5f7fa;
    cursor: not-allowed;
}

.vd-input-number-wrap .vd-number-input .vd-input-text {
    border: 1px solid #ced3d9;
    vertical-align: middle;
    line-height: 30px;
    font-size: 12px;
    height: 30px;
    width: 40px;
    box-sizing: border-box;
    position: relative;
    text-align: center;
    margin-left: -1px;
    margin-right: -1px;
}

.vd-input-number-wrap .vd-number-input .vd-input-text:hover {
    border-color: #B6BABF;
    outline: 0;
}

.vd-input-number-wrap .vd-number-input .vd-input-text:focus {
    border-color: #2E8AE6;
    outline: 0;
}

.vd-input-number-wrap .vd-number-tip {
    position: absolute;
    width: 100%;
    line-height: 26px;
    border: 1px solid #ced2d9;
    margin-top: -1px;
    font-size: 12px;
    color: #f60;
    text-align: center;
    background: #fff;
    box-sizing: border-box;
    opacity: 0;
    top: 30px;
    z-index: 99;
}

.vd-input-number-wrap .vd-number-tip.tip-show {
    opacity: 1;
}

.vd-input-number-wrap .vd-number-tip.tip-hide {
    opacity: 0;
    transition: all .2s linear;
    z-index: -1;
}