package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.annotation.BusinessID;
import com.vedeng.infrastructure.kingdee.annotation.KingDeeID;
import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 应付单：费用应付单   https://www.yuque.com/manhuo/gf1570/qyn801
 *
 * <AUTHOR>
 * @version 1.0
 * @description: 预处理金蝶费用应付单 dto  由erp实际业务转换
 * @date
 */
@Getter
@Setter
@WriteBackField(needBackField = {"FID","FEntityDetail.FEntryID","FEntityDetail.F_QZOK_BDDJHID"})
public class KingDeePayExpensesDto extends KingDeeMqBaseDto {

    /**
     * 单据内码
     */
    @WriteBackField
    @KingDeeID
    private Integer fId;
    /**
     * fBillNo
     */
    private String FBillNo;
    /**
     * 单据日期
     */
    private String fDate;
    /**
     * 贝登单据头ID
     */
    @BusinessID("F_QZOK_BDDJTID")
    private String fQzokBddjtId;
    /**
     * 是否价外税
     */
    private boolean FIsPriceExcludeTax;
    /**
     * 业务类型
     */
    private String fBusinessType;
    /**
     * 是否含税单价录入
     */
    private boolean fIsTax;
    /**
     * 立账类型
     */
    private String FSetAccountType;

    /**
     * 单据类型
     */
    private String fBillTypeId;
    /**
     * 供应商 报字段“供应商”是必填项 考虑传值的供应商字段不存在
     */
    private String fSupplierId;
    /**
     * 币别
     */
    private String fCurrencyId;
    /**
     * 结算组织
     */
    private String fSettleOrgId;
    /**
     * 付款组织
     */
    private String fPayOrgId;

    /**
     * 应付单明细
     */
    private List<KingDeePayExpensesDetailDto> fEntityDetail = new ArrayList<>();

    public KingDeePayExpensesDto() {
        this.fBusinessType = "FY";
        this.FIsPriceExcludeTax = true;
        this.fIsTax = true;
        this.FSetAccountType = "1";
        this.fBillTypeId = "YFD02_SYS";
        this.fCurrencyId = "PRE001";
        this.fSettleOrgId = KingDeeConstant.ORG_ID.toString();
        this.fPayOrgId = KingDeeConstant.ORG_ID.toString();
    }

    @Override
    public String getFormId() {
        return KingDeeFormConstant.PAY_EXPENSES;
    }
}
