package com.vedeng.finance.model;

import java.io.Serializable;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

public class BuyorderExpense implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * Column: BUYORDER_EXPENSE_ID
     * Type: INT UNSIGNED
     * Remark: 主键
     */
    private Integer buyorderExpenseId;

    /**
     * Column: BUYORDER_EXPENSE_NO
     * Type: VARCHAR(255)
     * Remark: 采购费用单号
     */
    private String buyorderExpenseNo;

    /**
     * Column: BUYORDER_NO
     * Type: VARCHAR(255)
     * Remark: 采购单号
     */
    private String buyorderNo;

    /**
     * Column: BUYORDER_ID
     * Type: INT UNSIGNED
     * Default value: 0
     * Remark: 采购ID
     */
    private Integer buyorderId;

    /**
     * Column: ORDER_TYPE
     * Type: INT UNSIGNED
     * Default value: 0
     * Remark: 费用单类型0直属采购费用单1非直属采购费用单
     */
    private Integer orderType;

    /**
     * Column: VALID_STATUS
     * Type: INT UNSIGNED
     * Default value: 0
     * Remark: 生效状态 0否 1是
     */
    private Integer validStatus;

    /**
     * Column: VALID_TIME
     * Type: DATETIME
     * Remark: 生效时间
     */
    private Date validTime;


    private String validTimeStr;


    private String addTimeStr;

    public String getValidTimeStr() {
        //把validTime转换成yyyy-mm-dd hh:mm:ss 时间格式返回
        if (validTime != null) {
            DateFormat dateformat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
            validTimeStr = dateformat.format(validTime);
        }
        return validTimeStr;

    }


    public String getAddTimeStr() {
        //把addTime转换成yyyy-mm-dd hh:mm:ss 时间格式返回
        if (addTime != null) {
            DateFormat dateformat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
            addTimeStr = dateformat.format(addTime);
        }
        return addTimeStr;
    }

    /**
     * Column: STATUS
     * Type: INT UNSIGNED
     * Default value: 0
     * Remark: 订单状态：0待确认（默认）、1进行中、2已完结、3已关闭
     */
    private Integer status;

    /**
     * Column: LOCKED_STATUS
     * Type: INT UNSIGNED
     * Default value: 0
     * Remark: 锁定状态0未锁定 1已锁定
     */
    private Integer lockedStatus;

    /**
     * Column: INVOICE_STATUS
     * Type: INT UNSIGNED
     * Default value: 0
     * Remark: 收票状态0未收票 1部分收票 2全部收票
     */
    private Integer invoiceStatus;

    /**
     * Column: PAYMENT_STATUS
     * Type: INT UNSIGNED
     * Default value: 0
     * Remark: 付款状态 0未付款 1部分付款 2全部付款
     */
    private Integer paymentStatus;

    /**
     * Column: DELIVERY_STATUS
     * Type: INT UNSIGNED
     * Default value: 0
     * Remark: 发货状态0未发货 1部分发货 2全部发货
     */
    private Integer deliveryStatus;

    /**
     * Column: ARRIVAL_STATUS
     * Type: INT UNSIGNED
     * Default value: 0
     * Remark: 收货状态0未收货 1部分收货 2全部收货
     */
    private Integer arrivalStatus;

    /**
     * Column: SERVICE_STATUS
     * Type: INT UNSIGNED
     * Default value: 0
     * Remark: 售后状态 0未售后 1售后中 2售后完成 3售后关闭
     */
    private Integer serviceStatus;

    /**
     * Column: AUDIT_STATUS
     * Type: INT UNSIGNED
     * Default value: 0
     * Remark: 订单审核状态：0待审核、1审核中、2审核通过、3审核不通过
     */
    private Integer auditStatus;

    /**
     * Column: INVOICE_TIME
     * Type: DATETIME
     * Remark: 收票时间
     */
    private Date invoiceTime;

    /**
     * Column: PAYMENT_TIME
     * Type: DATETIME
     * Remark: 付款时间
     */
    private Date paymentTime;

    /**
     * Column: AUDIT_TIME
     * Type: DATETIME
     * Remark: 审核时间
     */
    private Date auditTime;

    /**
     * Column: ADD_TIME
     * Type: DATETIME
     * Remark: 添加时间
     */
    private Date addTime;

    /**
     * Column: CREATOR
     * Type: INT UNSIGNED
     * Default value: 0
     * Remark: 添加人ID
     */
    private Integer creator;

    /**
     * Column: CREATOR_NAME
     * Type: VARCHAR(255)
     * Remark: 添加人名称
     */
    private String creatorName;

    /**
     * Column: MOD_TIME
     * Type: DATETIME
     * Remark: 最近一次编辑时间
     */
    private Date modTime;

    /**
     * Column: UPDATER
     * Type: INT UNSIGNED
     * Default value: 0
     * Remark: 最近一次编辑人ID
     */
    private Integer updater;

    /**
     * Column: UPDATER_NAME
     * Type: VARCHAR(255)
     * Remark: 最近一次编辑人名称
     */
    private String updaterName;

    /**
     * Column: IS_DELETE
     * Type: INT UNSIGNED
     * Default value: 0
     * Remark: 是否删除0否1是
     */
    private Integer isDelete;

    /**
     * Column: BUSINESS_TYPE
     * Type: INT UNSIGNED
     * Default value: 1
     * Remark: 业务类型：1.采购 2.采购售后
     */
    private Integer businessType;

    /**
     * Column: AFTER_SALES_NO
     * Type: VARCHAR(255)
     * Remark: 售后单号
     */
    private String afterSalesNo;

    /**
     * Column: AFTER_SALES_ID
     * Type: INT UNSIGNED
     * Default value: 0
     * Remark: 售后ID
     */
    private Integer afterSalesId;

    /**
     * Column: IS_AUTO
     * Type: INT
     * Default value: 0
     * Remark: 是否是自动转单创建0 否 1 是
     */
    private Integer isAuto;

    /**
     * Column: IS_RETURN_EARLY_WARN
     * Type: INT
     * Default value: 0
     * Remark: 是否退货预警 0否1是
     */
    private Integer isReturnEarlyWarn;

    /**
     * 供应商名称
     */
    private String traderName;

    public String getTraderName() {
        return traderName;
    }

    public void setTraderName(String traderName) {
        this.traderName = traderName;
    }

    public Integer getBuyorderExpenseId() {
        return buyorderExpenseId;
    }

    public void setBuyorderExpenseId(Integer buyorderExpenseId) {
        this.buyorderExpenseId = buyorderExpenseId;
    }

    public String getBuyorderExpenseNo() {
        return buyorderExpenseNo;
    }

    public void setBuyorderExpenseNo(String buyorderExpenseNo) {
        this.buyorderExpenseNo = buyorderExpenseNo == null ? null : buyorderExpenseNo.trim();
    }

    public String getBuyorderNo() {
        return buyorderNo;
    }

    public void setBuyorderNo(String buyorderNo) {
        this.buyorderNo = buyorderNo == null ? null : buyorderNo.trim();
    }

    public Integer getBuyorderId() {
        return buyorderId;
    }

    public void setBuyorderId(Integer buyorderId) {
        this.buyorderId = buyorderId;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Integer getValidStatus() {
        return validStatus;
    }

    public void setValidStatus(Integer validStatus) {
        this.validStatus = validStatus;
    }

    public Date getValidTime() {
        return validTime;
    }

    public void setValidTime(Date validTime) {
        this.validTime = validTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getLockedStatus() {
        return lockedStatus;
    }

    public void setLockedStatus(Integer lockedStatus) {
        this.lockedStatus = lockedStatus;
    }

    public Integer getInvoiceStatus() {
        return invoiceStatus;
    }

    public void setInvoiceStatus(Integer invoiceStatus) {
        this.invoiceStatus = invoiceStatus;
    }

    public Integer getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(Integer paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public Integer getDeliveryStatus() {
        return deliveryStatus;
    }

    public void setDeliveryStatus(Integer deliveryStatus) {
        this.deliveryStatus = deliveryStatus;
    }

    public Integer getArrivalStatus() {
        return arrivalStatus;
    }

    public void setArrivalStatus(Integer arrivalStatus) {
        this.arrivalStatus = arrivalStatus;
    }

    public Integer getServiceStatus() {
        return serviceStatus;
    }

    public void setServiceStatus(Integer serviceStatus) {
        this.serviceStatus = serviceStatus;
    }

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    public Date getInvoiceTime() {
        return invoiceTime;
    }

    public void setInvoiceTime(Date invoiceTime) {
        this.invoiceTime = invoiceTime;
    }

    public Date getPaymentTime() {
        return paymentTime;
    }

    public void setPaymentTime(Date paymentTime) {
        this.paymentTime = paymentTime;
    }

    public Date getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName == null ? null : creatorName.trim();
    }

    public Date getModTime() {
        return modTime;
    }

    public void setModTime(Date modTime) {
        this.modTime = modTime;
    }

    public Integer getUpdater() {
        return updater;
    }

    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    public String getUpdaterName() {
        return updaterName;
    }

    public void setUpdaterName(String updaterName) {
        this.updaterName = updaterName == null ? null : updaterName.trim();
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public String getAfterSalesNo() {
        return afterSalesNo;
    }

    public void setAfterSalesNo(String afterSalesNo) {
        this.afterSalesNo = afterSalesNo == null ? null : afterSalesNo.trim();
    }

    public Integer getAfterSalesId() {
        return afterSalesId;
    }

    public void setAfterSalesId(Integer afterSalesId) {
        this.afterSalesId = afterSalesId;
    }

    public Integer getIsAuto() {
        return isAuto;
    }

    public void setIsAuto(Integer isAuto) {
        this.isAuto = isAuto;
    }

    public Integer getIsReturnEarlyWarn() {
        return isReturnEarlyWarn;
    }

    public void setIsReturnEarlyWarn(Integer isReturnEarlyWarn) {
        this.isReturnEarlyWarn = isReturnEarlyWarn;
    }
}
