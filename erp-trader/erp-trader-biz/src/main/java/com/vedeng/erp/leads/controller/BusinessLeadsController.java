package com.vedeng.erp.leads.controller;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.BaseController;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.erp.leads.dto.LeadsToEditAndAddDto;
import com.vedeng.erp.leads.facade.LeadsFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 线索路由
 * @date 2022/7/9 14:46
 */
@Controller
@RequestMapping("/businessLeads")
@Slf4j
public class BusinessLeadsController extends BaseController {

    @Autowired
    private LeadsFacade leadsFacade;


    @RequestMapping(value = "/index")
    public ModelAndView index() {
//        return view(); //重定向到灵犀CRM的线索/商机列表中去
        return new ModelAndView("redirect:"+lxcrmUrl +"/crm/businessChance/profile/index");
    }

    /**
     * 线索编辑接口
     * @param id
     * @param traderId
     * @return
     */
    @RequestMapping(value = "/edit")
    public ModelAndView edit(@RequestParam(required = false) Integer id,
                             @RequestParam(required = false) Integer traderId) {
//        CurrentUser currentUser = CurrentUser.getCurrentUser();
//        LeadsToEditAndAddDto dto = new LeadsToEditAndAddDto();
//        dto.setLeadsId(id);
//        dto.setTraderId(traderId);
//        ModelAndView mv = leadsFacade.toEditAndAdd(dto, currentUser);
//        return mv;
        return new ModelAndView("redirect:"+lxcrmUrl +"/crm/businessChance/profile/add"); //重定稿到销售的线索/商机新增页去

    }

    /**
     * 线索新增接口
     * @param id
     * @param traderId
     * @return
     */
    @RequestMapping(value = "/edit/add")
    public ModelAndView edit1(@RequestParam(required = false) Integer id,
                              @RequestParam(required = false) Integer traderId) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        LeadsToEditAndAddDto dto = new LeadsToEditAndAddDto();
        dto.setLeadsId(id);
        dto.setTraderId(traderId);
//        ModelAndView mv = leadsFacade.toEditAndAdd(dto, currentUser);
//        return mv;

        return new ModelAndView("redirect:"+lxcrmUrl +"/crm/businessChance/profile/add"); //重定向到销售的线索/商机新增页去
    }

    @RequestMapping(value = "/details")
    @NoNeedAccessAuthorization
    public ModelAndView details(@RequestParam Integer id) {
//        ModelAndView mv = new ModelAndView("vue/view/businessleads/details");
//        mv.addObject("leadsId", id);
//        return mv;
        return new ModelAndView("redirect:"+lxcrmUrl +"/crm/businessLeads/profile/detail?id="+id); //重定向到销售的线索/商机新增页去
    }

}
