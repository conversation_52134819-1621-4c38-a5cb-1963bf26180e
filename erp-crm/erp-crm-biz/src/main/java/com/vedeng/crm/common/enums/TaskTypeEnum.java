package com.vedeng.crm.common.enums;

import com.vedeng.common.core.exception.ServiceException;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
public enum TaskTypeEnum {
    // 初步产品方案
    INITIAL_PRODUCT_PLAN("1", "商机支持"),

    // 单品询价
    SINGLE_ITEM_INQUIRY("2", "单品询价"),

    // 综合询价
    COMPREHENSIVE_INQUIRY("3", "综合询价"),

    // 商机督导
    BUSINESS_SUPERVISION("4", "商机督导"),

    // 再次跟进
    FOLLOW_UP_AGAIN("5", "再次跟进"),

    // 协同报价
    COLLABORATIVE_QUOTATION("6", "协同报价"),

    VISIT_FOLLOW("7","拜访任务");

    private final String value;
    private final String label;

    TaskTypeEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }

    /**
     * 获取任务类型
     *
     * @param code 业务类型
     * @return KingDeeBizEnums
     */
    public static TaskTypeEnum getTaskTypeEnum(String code) {
        return Arrays.stream(TaskTypeEnum.values())
                .filter(enums -> enums.getValue().equals(code))
                .findFirst().orElseThrow(() -> new ServiceException("无法获取任务类型枚举"));
    }
    
    public static String getTaskTypeLabel(String code) {
        return getTaskTypeEnum(code).getLabel();
    }



}
