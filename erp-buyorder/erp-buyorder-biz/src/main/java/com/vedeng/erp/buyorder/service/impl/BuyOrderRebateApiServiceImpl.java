package com.vedeng.erp.buyorder.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.enums.*;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.buyorder.domain.entity.Buyorder;
import com.vedeng.erp.buyorder.domain.entity.BuyorderGoods;
import com.vedeng.erp.buyorder.dto.BuyOrderRebateDto;
import com.vedeng.erp.buyorder.dto.SettlementBillCheckResult;
import com.vedeng.erp.buyorder.mapper.BuyorderGoodsMapper;
import com.vedeng.erp.buyorder.mapper.BuyorderMapper;
import com.vedeng.erp.buyorder.service.BuyOrderRebateApiService;
import com.vedeng.erp.finance.cmd.SettlementBillCreateCmd;
import com.vedeng.erp.finance.cmd.SettlementBillDeleteCmd;
import com.vedeng.erp.finance.cmd.SettlementBillQueryCmd;
import com.vedeng.erp.finance.service.SettlementBillApiService;
import com.vedeng.erp.trader.dto.SupplierAssetChangeDto;
import com.vedeng.erp.trader.dto.TraderSupplierDto;
import com.vedeng.erp.trader.service.SupplierAssetApiService;
import com.vedeng.erp.trader.service.TraderSupplierApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class BuyOrderRebateApiServiceImpl implements BuyOrderRebateApiService {

    @Autowired
    SupplierAssetApiService supplierAssetApiService;

    @Autowired
    SettlementBillApiService settlementBillApiService;

    @Autowired
    TraderSupplierApiService traderSupplierApiService;

    @Autowired
    @Qualifier(value = "newBuyorderMapper")
    private BuyorderMapper buyorderMapper;

    @Autowired
    @Qualifier(value = "newBuyorderGoodsMapper")
    private BuyorderGoodsMapper buyorderGoodsMapper;

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void addOrUpdateBuyOrderRebateProcess(BuyOrderRebateDto buyOrderRebateDto, Boolean isExistSettlement, Boolean isUsedRebate) {
        if (Objects.isNull(buyOrderRebateDto)) {
            log.info("新增或修改采购订单处理返利业务, buyOrderRebateDto为空");
            throw new ServiceException("采购订单返利信息为空");
        }
        if (Boolean.FALSE.equals(isExistSettlement) && Boolean.FALSE.equals(isUsedRebate)) {
            log.info("新增或修改采购订单处理返利业务, 原先未使用返利，现在也未使用返利，无需处理: buyOrderRebateDto:{}", JSON.toJSONString(buyOrderRebateDto));
        } else if (Boolean.FALSE.equals(isExistSettlement) && Boolean.TRUE.equals(isUsedRebate)) {
            log.info("新增或修改采购订单处理返利业务, 原先未使用返利，现在使用返利, buyOrderRebateDto:{}", JSON.toJSONString(buyOrderRebateDto));
            // 占用供应商返利余额
            occupyRebate(buyOrderRebateDto);
        } else if (Boolean.TRUE.equals(isExistSettlement) && Boolean.FALSE.equals(isUsedRebate)) {
            log.info("新增或修改采购订单处理返利业务, 原先使用返利，现在未使用返利, buyOrderRebateDto:{}", JSON.toJSONString(buyOrderRebateDto));
            // 释放供应商返利余额
            releaseRebate(buyOrderRebateDto);
        } else if (Boolean.TRUE.equals(isExistSettlement) && Boolean.TRUE.equals(isUsedRebate)) {
            log.info("新增或修改采购订单处理返利业务, 原先使用返利，现在也使用返利, buyOrderRebateDto:{}", JSON.toJSONString(buyOrderRebateDto));
            // 释放原先的供应商返利余额
            releaseRebate(buyOrderRebateDto);
            // 占用新的供应商返利余额
            occupyRebate(buyOrderRebateDto);
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void passAuditBuyOrderRebateProcess(Integer buyOrderId) {
        log.info("采购订单审核通过后返利业务处理，审核采购订单通过，buyOrderId:{}", buyOrderId);
        // 0.前置校验
        SettlementBillCheckResult result = getSettlementBillCheckResult(buyOrderId);
        if (Objects.isNull(result)) {
            log.info("该采购订单未使用返利余额，无需处理，buyOrderId:{}", buyOrderId);
            return;
        }
        SupplierAssetChangeDto supplierAssetChangeDto = packSupplierAssetChangeDto(result.getBuyorder().getBuyorderNo(),
                result.getTotalRebateAmount(), result.getBuyorder().getTraderId(), result.getTraderSupplierDto().getTraderSupplierId());
        log.info("采购订单审核通过后返利业务处理，释放供应商返利余额，supplierAssetChangeDto:{}", JSON.toJSONString(supplierAssetChangeDto));
        supplierAssetApiService.relieveOccupy(supplierAssetChangeDto);
        log.info("采购订单审核通过后返利业务处理，根据返利结算单进行结算，buyOrderId:{}", buyOrderId);
        settlementBillApiService.createAlsoSettlement(
                SettlementBillCreateCmd
                        .builder()
                        .sourceTypeEnum(BusinessSourceTypeEnum.buyOrder)
                        .settlementTypeEnum(Collections.singletonList(SettlementTypeEnum.REBATE_PAY))
                        .businessSourceId(buyOrderId)
                        .build());
        int paymentStatus = ErpConstant.ONE;
        int invoiceStatus = ErpConstant.ONE;
        BigDecimal totalAmount = result.getBuyorder().getTotalAmount();
        // 返利金额小于订单总金额，付款状态为部分付款
        if (result.getTotalRebateAmount().compareTo(totalAmount) < 0) {
            // 付款方式为全部账期，付款状态为全部付款
            if (ErpConstant.ID_423.equals(result.getBuyorder().getPaymentType())) {
                paymentStatus = ErpConstant.TWO;
            }
        } else {
            paymentStatus = ErpConstant.TWO;
            invoiceStatus = ErpConstant.TWO;
        }
        log.info("采购订单审核通过后返利业务处理，更新采购订单的付款状态和开票状态，paymentStatus:{}, invoiceStatus:{}, buyOrderId:{}", paymentStatus, invoiceStatus, buyOrderId);
        buyorderMapper.updatePaymentStatusAndInvoiceStatusByBuyorderId(paymentStatus, invoiceStatus, buyOrderId);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void closeBuyOrderRebateProcess(Integer buyOrderId) {
        log.info("关闭采购订单后返利业务处理，关闭采购订单，buyOrderId:{}", buyOrderId);
        SettlementBillCheckResult result = getSettlementBillCheckResult(buyOrderId);
        if (Objects.isNull(result)) {
            log.info("该采购订单未使用返利余额，无需处理，buyOrderId:{}", buyOrderId);
            return;
        }
        if (ErpConstant.ZERO.equals(result.getBuyorder().getValidStatus())) {
            log.info("关闭采购订单后返利业务处理，该采购单未生效且使用了返利，释放供应商返利余额，buyOrderId:{}", buyOrderId);
            SupplierAssetChangeDto supplierAssetChangeDto = packSupplierAssetChangeDto(result.getBuyorder().getBuyorderNo(),
                    result.getTotalRebateAmount(), result.getBuyorder().getTraderId(), result.getTraderSupplierDto().getTraderSupplierId());
            supplierAssetApiService.relieveOccupy(supplierAssetChangeDto);
        } else {
            log.info("关闭采购订单后返利业务处理，该采购单已生效且使用了返利，删除返利结算单，buyOrderId:{}", buyOrderId);
            SettlementBillQueryCmd cmd = new SettlementBillQueryCmd(buyOrderId, BusinessSourceTypeEnum.buyOrder);
            Integer settleBillId = settlementBillApiService.getSettlementIdByBusiness(cmd);
            settlementBillApiService.delete(new SettlementBillDeleteCmd(settleBillId));
        }
    }

    private SupplierAssetChangeDto packSupplierAssetChangeDto(String buyorderNo, BigDecimal settleAmount, Integer traderId, Integer traderSupplierId) {
        SupplierAssetChangeDto supplierAssetChangeDto = new SupplierAssetChangeDto();
        supplierAssetChangeDto.setQuantity(settleAmount);
        supplierAssetChangeDto.setTraderId(traderId);
        supplierAssetChangeDto.setTraderSupplierId(traderSupplierId);
        supplierAssetChangeDto.setBusinessNumber(buyorderNo);
        supplierAssetChangeDto.setBusinessSourceType(BusinessSourceTypeEnum.buyOrder);
        supplierAssetChangeDto.setSupplierAsset(SupplierAssetEnum.rebate);
        log.info("新增或修改采购订单处理返利业务，封装供应商资产变动信息, supplierAssetChangeDto:{}", JSON.toJSONString(supplierAssetChangeDto));
        return supplierAssetChangeDto;
    }

    private void releaseRebate(BuyOrderRebateDto buyOrderRebateDto) {
        BigDecimal totalRebateAmountOld = buyOrderRebateDto.getOldBuyOrderGoodsRebateList().stream()
                .filter(Objects::nonNull)
                .map(BuyOrderRebateDto.BuyOrderGoodsRebateDto::getRebateAmount)
                .filter(amount -> Objects.nonNull(amount) && amount.compareTo(BigDecimal.ZERO) > 0)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        TraderSupplierDto traderSupplierDtoOld = traderSupplierApiService.getTraderSupplierByTraderId(buyOrderRebateDto.getOldTraderId());
        // 调用释放老的的供应商返利余额
        SupplierAssetChangeDto oldSupplierAssetChangeDto = packSupplierAssetChangeDto(buyOrderRebateDto.getBuyorderNo(),
                totalRebateAmountOld, buyOrderRebateDto.getOldTraderId(), traderSupplierDtoOld.getTraderSupplierId());
        supplierAssetApiService.relieveOccupy(oldSupplierAssetChangeDto);
    }

    private void occupyRebate(BuyOrderRebateDto buyOrderRebateDto) {
        BigDecimal totalRebateAmountNew = buyOrderRebateDto.getNewbuyOrderGoodsRebateList().stream()
                .filter(Objects::nonNull)
                .map(BuyOrderRebateDto.BuyOrderGoodsRebateDto::getRebateAmount)
                .filter(amount -> Objects.nonNull(amount) && amount.compareTo(BigDecimal.ZERO) > 0)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        TraderSupplierDto traderSupplierDtoNew = traderSupplierApiService.getTraderSupplierByTraderId(buyOrderRebateDto.getTraderId());
        // 调用占用新的的供应商返利余额
        SupplierAssetChangeDto newSupplierAssetChangeDto = packSupplierAssetChangeDto(buyOrderRebateDto.getBuyorderNo(),
                totalRebateAmountNew, buyOrderRebateDto.getTraderId(), traderSupplierDtoNew.getTraderSupplierId());
        supplierAssetApiService.occupy(newSupplierAssetChangeDto);
    }

    private SettlementBillCheckResult getSettlementBillCheckResult(Integer buyOrderId) {
        Buyorder buyorder = buyorderMapper.selectByPrimaryKey(buyOrderId);
        List<BuyorderGoods> buyorderGoodsList = buyorderGoodsMapper.findByBuyorderId(buyOrderId);
        // 1.判断该采购订单是否使用了返利余额
        boolean isExistSettlement = CollUtil.isNotEmpty(buyorderGoodsList) && buyorderGoodsList.stream()
                .anyMatch(goods -> Objects.nonNull(goods.getRebateAmount()) && goods.getRebateAmount().compareTo(BigDecimal.ZERO) > 0);
        if (!isExistSettlement) {
            return null;
        }
        TraderSupplierDto traderSupplierDto = traderSupplierApiService.getTraderSupplierByTraderId(buyorder.getTraderId());
        if (Objects.isNull(traderSupplierDto) || Objects.isNull(traderSupplierDto.getTraderSupplierId())) {
            log.info("供应商不存在, traderId:{}", buyorder.getTraderId());
            throw new ServiceException("供应商不存在");
        }
        BigDecimal totalRebateAmount = buyorderGoodsList.stream()
                .filter(goods -> Objects.nonNull(goods.getRebateAmount()) && goods.getRebateAmount().compareTo(BigDecimal.ZERO) > 0)
                .map(BuyorderGoods::getRebateAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return SettlementBillCheckResult
                .builder()
                .buyorder(buyorder)
                .traderSupplierDto(traderSupplierDto)
                .totalRebateAmount(totalRebateAmount)
                .build();
    }

}
