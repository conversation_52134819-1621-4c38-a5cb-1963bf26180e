package com.vedeng.common.mybatis.jbatis.exception;


import com.vedeng.common.mybatis.jbatis.constant.ExceptionConstant;

/**
 * <p>Description: List参数为空 异常</p> 
 * <AUTHOR>
 * @version V1.0
 */
public class ListIsNullException extends RuntimeException {

	private static final long serialVersionUID = 1L;
	
    public ListIsNullException(){
        super(ExceptionConstant.LIST_IS_NULL_MSG);
    }

    public ListIsNullException(String msg){
        super(msg);
    }

    public ListIsNullException(Throwable cause){
        super(cause);
    }

    public ListIsNullException(String msg, Throwable cause){
        super(msg, cause);
    }
}
