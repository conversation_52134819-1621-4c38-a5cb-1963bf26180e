package com.vedeng.erp.kingdee.batch.tasklet;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.kingdee.batch.common.tasklet.BaseTasklet;
import com.vedeng.erp.kingdee.batch.dto.BatchFlowNodeDto;
import com.vedeng.erp.kingdee.batch.dto.BatchFlowOrderDto;
import com.vedeng.erp.kingdee.batch.dto.BatchFlowOrderInfoDto;
import com.vedeng.erp.kingdee.batch.repository.BatchFlowOrderDtoMapper;
import com.vedeng.erp.system.service.ContractApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 流转单合同电子签章 Tasklet
 * 当流转单成功回传到 ERP 系统后，检查流转单的审核状态
 * 同时验证该流转单的所有节点单据是否已全部回传完成
 * 如果流转单已审核通过且所有节点单据都已回传，则自动调用电子签章服务生成对应的合同
 *
 * <AUTHOR>
 * @date 2023-07-02
 */
@Service
@Slf4j
public class FlowOrderContractSignTasklet extends BaseTasklet {

    @Autowired
    private BatchFlowOrderDtoMapper batchFlowOrderDtoMapper;

    @Autowired
    private ContractApiService contractApiService;

    @Override
    public void doExec(Map<String, Object> jobParameters) throws Exception {
        log.info("流转单合同电子签章任务开始");

        // 1. 查询已审核通过的流转单
        List<BatchFlowOrderDto> flowOrderList = batchFlowOrderDtoMapper.findNoPushInternalProcurement();
        if (CollUtil.isEmpty(flowOrderList)) {
            log.info("没有需要处理的流转单");
            return;
        }

        for (BatchFlowOrderDto flowOrderDto : flowOrderList) {
            try {
                // 3. 获取流转单节点
                List<BatchFlowNodeDto> nodeList = batchFlowOrderDtoMapper.findByFlowOrderIdGetFlowNodeDto(flowOrderDto.getFlowOrderId());
                if (CollUtil.isEmpty(nodeList)) {
                    log.info("流转单[{}]没有节点信息，跳过合同签章", flowOrderDto.getFlowOrderNo());
                    continue;
                }

                // 4. 检查所有节点的单据是否已全部回传
                int totalNodes = nodeList.size();
                int totalInfoWithBusinessNo = 0;

                // 遍历所有节点，统计有业务编号的单据数量
                for (BatchFlowNodeDto nodeDto : nodeList) {
                    List<BatchFlowOrderInfoDto> infoList = batchFlowOrderDtoMapper.findByflowNodeId(nodeDto.getFlowNodeId());
                    if (CollUtil.isNotEmpty(infoList)) {
                        for (BatchFlowOrderInfoDto infoDto : infoList) {
                            // 统计有业务编号的单据数量
                            if (infoDto.getFlowOrderInfoNo() != null && !infoDto.getFlowOrderInfoNo().isEmpty()) {
                                totalInfoWithBusinessNo++;
                            }
                        }
                    }
                }

                log.info("流转单[{}]的节点单据,当前节点数量：{}，有业务编号的单据数量：{}", flowOrderDto.getFlowOrderNo(), totalNodes, totalInfoWithBusinessNo);

                contractApiService.signContract(flowOrderDto.getFlowOrderId(), flowOrderDto.getBaseBusinessType());

            } catch (Exception e) {
                log.error("处理流转单[{}]合同签章异常", flowOrderDto.getFlowOrderNo(), e);
            }
        }

        log.info("流转单合同电子签章任务结束");
    }
}
