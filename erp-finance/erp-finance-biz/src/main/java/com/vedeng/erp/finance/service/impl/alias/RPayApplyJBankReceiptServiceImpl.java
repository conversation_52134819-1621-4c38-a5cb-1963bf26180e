package com.vedeng.erp.finance.service.impl.alias;

import com.vedeng.erp.finance.domain.entity.RPayApplyJBankReceiptEntity;
import com.vedeng.erp.finance.dto.RPayApplyJBankReceiptDto;
import com.vedeng.erp.finance.mapper.RPayApplyJBankReceiptMapper;
import com.vedeng.erp.finance.service.RPayApplyJBankReceiptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RPayApplyJBankReceiptServiceImpl implements RPayApplyJBankReceiptService {

    @Autowired
    private RPayApplyJBankReceiptMapper rPayApplyJBankReceiptMapper;
    @Override
    public List<RPayApplyJBankReceiptEntity> findByPayApplyId(Integer payApplyId) {
        return rPayApplyJBankReceiptMapper.findByPayApplyId(payApplyId);
    }

    @Override
    public int insertSelective(RPayApplyJBankReceiptDto rPayApplyJBankReceiptDto) {

        RPayApplyJBankReceiptEntity insert = new RPayApplyJBankReceiptEntity();
        insert.setPayApplyId(rPayApplyJBankReceiptDto.getPayApplyId());
        insert.setBankId(rPayApplyJBankReceiptDto.getBankId());
        insert.setBankReceiptAliasId(rPayApplyJBankReceiptDto.getBankReceiptAliasId());
        return rPayApplyJBankReceiptMapper.insertSelective(insert);
    }
}
