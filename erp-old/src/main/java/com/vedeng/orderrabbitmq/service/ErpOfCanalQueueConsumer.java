package com.vedeng.orderrabbitmq.service;

import com.alibaba.fastjson.JSON;
import com.common.dto.CanalMqBodyDTO;
import com.common.enums.CanalMqTypeEnum;
import com.rabbitmq.ErpMsgProducer;
import com.rabbitmq.RabbitConfig;
import com.rabbitmq.client.Channel;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.trace.mq.AbstractMessageListener;
import com.vedeng.docSync.service.SyncGoodsService;
import com.vedeng.erp.buyorder.service.BuyorderDataSyncService;
import com.vedeng.goods.dao.BaseCategoryMapper;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.dao.SyncGoodsInfoMapper;
import com.vedeng.goods.model.CoreSku;
import com.vedeng.goods.model.vo.BaseCategoryVo;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.dto.SaleorderBillDTO;
import com.vedeng.order.service.SaleorderDataSyncService;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.todolist.service.impl.MaintainDataDeliveryTime;
import com.vedeng.trader.dao.TraderMapper;
import net.sf.json.JSONObject;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: daniel
 * @Date: 2020/11/2 14 21
 * @Description:
 */
@Component
public class ErpOfCanalQueueConsumer extends AbstractMessageListener {

    public static final Logger LOGGER = LoggerFactory.getLogger(ErpOfCanalQueueConsumer.class);

    @Autowired
    private TraderMapper traderMapper;

    @Value("${erp.database}")
    private String erpDatabaseName;

    @Autowired
    private MaintainDataDeliveryTime maintainDataDeliveryTime;

    @Autowired
    private SaleorderService saleorderService;

    @Autowired
    private ErpMsgProducer erpMsgProducer;

    @Autowired
    private SaleorderMapper saleorderMapper;

    @Autowired
    private SaleorderDataSyncService saleorderDataSyncService;

    @Autowired
    private BuyorderDataSyncService buyorderDataSyncService;

    @Autowired
    private SyncGoodsService syncGoodsService;

    @Resource
    private CoreSkuMapper coreSkuMapper;

    @Resource
    private SyncGoodsInfoMapper syncGoodsInfoMapper;

    @Resource
    private BaseCategoryMapper baseCategoryMapper;

    @Override
    public void doBusiness(Message message, Channel channel) throws Exception {

        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);

        try {
            CanalMqBodyDTO mqBodyDTO = JSON.parseObject(messageBody,CanalMqBodyDTO.class);
            LOGGER.info("canal监听的erp表变动，推送的数据：{}", JSON.toJSONString(mqBodyDTO));
            if ((mqBodyDTO.getType().equals(CanalMqTypeEnum.INSERT.getOperateType()) || mqBodyDTO.getType().equals(CanalMqTypeEnum.UPDATE.getOperateType()))
                    && !mqBodyDTO.getIsDdl() && mqBodyDTO.getData().length > 0){

                if (erpDatabaseName.equals(mqBodyDTO.getDatabase())){
                    switch (mqBodyDTO.getTable()){
                        case "T_COMMUNICATE_RECORD":
                            listenCommunicationData(mqBodyDTO.getData(),mqBodyDTO.getType());
                            saleorderDataSyncService.syncDataByCommunicationRecordTable(mqBodyDTO);
                            break;

                        case "V_CORE_SKU":
                            listenDeliveryRangeOfCoreSkuTable(mqBodyDTO);
                            listenGoodsSearchOfCoreSkuTable(mqBodyDTO);
                            break;

                        case "V_CORE_SPU":
                            listenGoodsSearchOfCoreSpuTable(mqBodyDTO);
                            break;

                        case "V_BASE_CATEGORY":
                            listenGoodsSearchOfBaseCategoryTable(mqBodyDTO);
                            break;

                        case "T_SALEORDER":
                            listenSaleorderData(mqBodyDTO);
                            saleorderDataSyncService.syncDataBySaleorderTable(mqBodyDTO);
                            break;

                        case "T_SALEORDER_GOODS":
                            listenSaleorderGoodsChange(mqBodyDTO.getData());
                            break;
                        case "T_CAPITAL_BILL_DETAIL":
                            listenCapitalBillDetail(mqBodyDTO);
                            saleorderDataSyncService.syncDataByCapitalBillDetailTable(mqBodyDTO);
                            break;
                        case "T_VERIFIES_INFO":
                            saleorderDataSyncService.syncDataByVerifiesInfoTable(mqBodyDTO);
                            break;
                        case "T_INVOICE_APPLY":
                            saleorderDataSyncService.syncDataByInvoiceApplyTable(mqBodyDTO);
                            break;
                        case "T_AFTER_SALES":
                            saleorderDataSyncService.syncDataByAfterSalesTable(mqBodyDTO);
                            break;
                        case "T_ATTACHMENT":
                            buyorderDataSyncService.syncDataByAttachmentTable(mqBodyDTO);
                            break;
                        default:
                            break;
                    }
                }
            }
            channel.basicAck(message.getMessageProperties().getDeliveryTag(),false);
        }catch (Exception e){
            LOGGER.error("canal监听的erp表变动，推送的数据：{}，错误信息：",messageBody,e);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(),false,false);
            } catch (IOException ex) {
                LOGGER.error("canal监听的erp表变动，消息消费失败，将消息返回给rabbitmq错误：",ex);
            }
        }
    }


    private void listenCommunicationData(Object[] dataList, String operateType){

        if (CanalMqTypeEnum.INSERT.getOperateType().equals(operateType) ||  CanalMqTypeEnum.UPDATE.getOperateType().equals(operateType)){
            for (Object data : dataList){
                JSONObject item = JSONObject.fromObject(data);
                Integer traderId = item.getInt("TRADER_ID");
                Long communicateTime = item.getLong("ADD_TIME");
                traderMapper.updateLastCommunicateTimeOfTrader(traderId,communicateTime);
            }
        }
    }


    /**
     * 监听coreSku表的预计可发货字段
     * @param mqBodyDTO 消息体
     */
    private void listenDeliveryRangeOfCoreSkuTable(CanalMqBodyDTO mqBodyDTO){
        if (CanalMqTypeEnum.INSERT.getOperateType().equals(mqBodyDTO.getType()) || CanalMqTypeEnum.UPDATE.getOperateType().equals(mqBodyDTO.getType())){
            for (Object data : mqBodyDTO.getData()){
                JSONObject item = JSONObject.fromObject(data);
                Integer skuId = item.getInt("SKU_ID");
                if (!"null".equals(item.getString("DECLARE_DELIVERY_RANGE")) && StringUtils.isNotBlank(item.getString("DECLARE_DELIVERY_RANGE"))){
                    maintainDataDeliveryTime.finish(skuId);
                }
            }
        }
    }

    private void listenSaleorderData(CanalMqBodyDTO mqBodyDTO){
        LOGGER.info("canal监听的Saleorder表变动，推送的数据：{}", JSON.toJSONString(mqBodyDTO));
        if (CanalMqTypeEnum.UPDATE.getOperateType().equals(mqBodyDTO.getType())){
            for (int i = 0; i < mqBodyDTO.getData().length; i++) {
                listenArriveStatusOfHcOrder(mqBodyDTO.getData()[i],mqBodyDTO.getOld()[i],mqBodyDTO.getTs());
                JSONObject dataJson = JSONObject.fromObject(mqBodyDTO.getData()[i]);
                int orderType = dataJson.getInt("ORDER_TYPE");
                if (orderType == 7 || orderType == 8){
                    sendMsgWhenJcOrderChange(dataJson.getString("SALEORDER_NO"));
                }
            }
        }
    }





    //合伙人需求
    private void listenArriveStatusOfHcOrder(Object data, Object old, Long timestamp){
        JSONObject dataJson = JSONObject.fromObject(data);
        JSONObject oldJson = JSONObject.fromObject(old);
        int arriveStatus = dataJson.getInt("ARRIVAL_STATUS");
        String orderNo = dataJson.getString("SALEORDER_NO");
        Saleorder saleorder = new Saleorder();
        saleorder.setSaleorderNo(orderNo);
        saleorder = saleorderMapper.getSaleorderBySaleorderNo(saleorder);
        if (saleorder == null) {
            return;
        }
        if (ErpConst.FIVE.equals(saleorder.getOrderType()) && oldJson.containsKey("ARRIVAL_STATUS") && arriveStatus == 2){
            LOGGER.info("耗材订单全部收货，订单号：{}",orderNo);
            sendMqWhenHcOrderArrive(orderNo,timestamp);
        }
    }


    private void listenCapitalBillDetail(CanalMqBodyDTO mqBodyDTO){
         if (CanalMqTypeEnum.INSERT.getOperateType().equals(mqBodyDTO.getType())){
             for (Object data : mqBodyDTO.getData()){
                 JSONObject dataJson = JSONObject.fromObject(data);
                 Integer capitalBillDetailId = dataJson.getInt("CAPITAL_BILL_DETAIL_ID");

                 //VDERP-5714 如果是耗材订单的售后退款流水，则发送MQ。
                 Optional.ofNullable(saleorderService.checkAfterCapitalBillAmountOfHcOrder(capitalBillDetailId))
                         .ifPresent(capitalBillDetail -> {
                             LOGGER.info("耗材订单：{}，售后订单发生退款，退款金额：{}，流水ID：{}",capitalBillDetail.getOrderNo(),capitalBillDetail.getAmount(),
                                     capitalBillDetailId);
                             sendMqWhenHcOrderAfterBill(capitalBillDetail.getOrderNo(),capitalBillDetail.getAmount(),mqBodyDTO.getTs());
                         });
             }
         }
    }

    private void sendMqWhenHcOrderArrive(String orderNo, Long timestamp){
        //VDERP-5714 耗材订单收货状态为全部收货时，发送MQ。
        SaleorderBillDTO billDTO = saleorderService.getPayAmountInfoWhenOrderArrived(orderNo,timestamp);
        LOGGER.info("耗材订单：{}全部收货后，发送MQ消息，消息体：{}",orderNo,JSON.toJSONString(billDTO));
        erpMsgProducer.sendMsg(RabbitConfig.HC_ORDER_ARRIVE_EXCHANGE,null,JSON.toJSONString(billDTO));
    }

    private void sendMqWhenHcOrderAfterBill(String orderNo, BigDecimal refundAmount, Long timestamp){
        SaleorderBillDTO billDTO = new SaleorderBillDTO();
        billDTO.setOrderNo(orderNo);
        billDTO.setRefundAmount(refundAmount);
        billDTO.setRefundTime(timestamp);
        LOGGER.info("耗材订单：{}产生售后退款流水，发送MQ消息，消息体：{}",orderNo,JSON.toJSONString(billDTO));
        erpMsgProducer.sendMsg(RabbitConfig.HC_ORDER_ARRIVE_EXCHANGE,null,JSON.toJSONString(billDTO));
    }

    private void listenSaleorderGoodsChange(Object[] dataList){
        LOGGER.info("canal监听的SaleorderGoods表变动，推送的数据：{}", JSON.toJSONString(dataList));
        for (Object data : dataList){
            JSONObject item = JSONObject.fromObject(data);
            if (item.containsKey("SALEORDER_ID") && item.containsKey("ORDER_TYPE")){
                int saleorderId = item.getInt("SALEORDER_ID");
                int orderType = item.getInt("ORDER_TYPE");
                if (orderType == 7 || orderType == 8){
                    Optional.ofNullable(saleorderMapper.getSaleorderBySaleorderId(saleorderId))
                            .ifPresent(saleorder -> sendMsgWhenJcOrderChange(saleorder.getSaleorderNo()));
                }

            }
        }
    }


    /**
     * 当集采订单状态发生变化时，发送MQ
     * @param orderNo 订单号
     */
    private void sendMsgWhenJcOrderChange(String orderNo){
        JSONObject json = new JSONObject();
        json.put("orderNo",orderNo);
        LOGGER.info("集采订单：{}的状态发生变化，推送MQ消息",orderNo);
        erpMsgProducer.sendMsg(RabbitConfig.JC_ORDER_STATUS_CHANGE_EXCHANGE,RabbitConfig.JC_ORDER_STATUS_CHANGE_ROUTING_KEY,json.toString());
    }


    /**
     * sku信息变化时，推送信息到前台
     * @param mqBodyDTO
     */
    private void listenGoodsSearchOfCoreSkuTable(CanalMqBodyDTO mqBodyDTO){
        switch (CanalMqTypeEnum.getInstance(mqBodyDTO.getType())) {
            case INSERT:
            case UPDATE: {
                ArrayList<Integer> skuIds = new ArrayList<>();
                for (Object data : mqBodyDTO.getData()) {
                    JSONObject item = JSONObject.fromObject(data);
                    Integer skuId = item.getInt("SKU_ID");
                    skuIds.add(skuId);
                }
                syncGoodsService.syncSkuInfo2EsBySkuIds(skuIds);
                break;
            }
            default:
        }
    }

    /**
     * spu信息变化时  推送相关sku信息
     * @param mqBodyDTO
     */
    private void listenGoodsSearchOfCoreSpuTable(CanalMqBodyDTO mqBodyDTO){
        switch (CanalMqTypeEnum.getInstance(mqBodyDTO.getType())) {
            case INSERT:
            case UPDATE: {
                ArrayList<Integer> skuIds = new ArrayList<>();
                for (Object data : mqBodyDTO.getData()) {
                    JSONObject item = JSONObject.fromObject(data);
                    Integer spuId = item.getInt("SPU_ID");
                    List<CoreSku> skuBySpuId = coreSkuMapper.getSkuBySpuId(spuId);
                    if (CollectionUtils.isEmpty(skuBySpuId)) {
                        continue;
                    }
                    skuIds.addAll(skuBySpuId.stream().map(CoreSku::getSkuId).collect(Collectors.toList()));
                }
                syncGoodsService.syncSkuInfo2EsBySkuIds(skuIds);
                break;
            }
            default:
        }
    }

    /**
     * 分类信息变化时，推送推送相关sku信息
     * @param mqBodyDTO
     */
    private void listenGoodsSearchOfBaseCategoryTable(CanalMqBodyDTO mqBodyDTO){
        switch (CanalMqTypeEnum.getInstance(mqBodyDTO.getType())) {
            case INSERT:
            case UPDATE: {
                ArrayList<Integer> baseCategoryIdList = new ArrayList<>();
                for (Object data : mqBodyDTO.getData()) {
                    JSONObject item = JSONObject.fromObject(data);
                    Integer baseCategoryId = item.getInt("BASE_CATEGORY_ID");
                    baseCategoryIdList.add(baseCategoryId);
                }
                if (CollectionUtils.isEmpty(baseCategoryIdList)) {
                    return;
                }
                HashMap<Integer, Integer> categoryLevelMap = new HashMap<>(16);

                baseCategoryIdList.forEach(categoryInfo -> {
                    BaseCategoryVo baseCategoryVo = baseCategoryMapper.selectByPrimaryKey(categoryInfo);
                    if (baseCategoryVo == null || baseCategoryVo.getBaseCategoryId() == null) {
                        return;
                    }
                    categoryLevelMap.put(baseCategoryVo.getBaseCategoryId(), baseCategoryVo.getBaseCategoryLevel());
                });
                if (MapUtils.isEmpty(categoryLevelMap)) {
                    return;
                }
                ArrayList<Integer> thirdCategoryIdList = new ArrayList<>();
                categoryLevelMap.forEach((k, v) -> {
                    List<Integer> thirdBaseCategoryIdsByIdAndLevel = syncGoodsInfoMapper.getThirdBaseCategoryIdsByIdAndLevel(k, v);
                    if (CollectionUtils.isEmpty(thirdBaseCategoryIdsByIdAndLevel)) {
                        return;
                    }
                    thirdCategoryIdList.addAll(thirdBaseCategoryIdsByIdAndLevel);
                });
                syncGoodsService.syncSkuInfo2EsBySkuIds(syncGoodsInfoMapper.getSkuIdsByBaseCategoryIds(thirdCategoryIdList));
                break;
            }
            default:
        }
    }

}
