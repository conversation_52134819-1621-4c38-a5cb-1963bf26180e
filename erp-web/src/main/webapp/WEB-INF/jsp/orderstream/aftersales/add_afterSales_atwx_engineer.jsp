<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="新增售后安调公司" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='<%=basePath%>static/js/orderstream/aftersales/add_afterSales_atWxEngineer.js?rnd=${resourceVersionKey}'></script>

<div class="form-list  form-tips5">
    <form method="post" action="<%= basePath %>/order/afterSalesCommon/saveAddAfterSalesEngineer.do" >
        <input type="hidden" value="${isAtwx}" name="isAtwx">
        <ul>
            <li>
                <div class="form-tips">
                    <span>*</span>
                    <lable>公司名称</lable>
                </div>
                <div class="f_left f_left_wid90">
                    <div class="form-blanks mb10">
                        <span class="none" id="selname"></span>
                        <input type="text" placeholder="请输入售后安调公司名称" class="input-small" name="searchName" id="searchName" >
                        <label class="bt-bg-style bg-light-blue bt-small" onclick="search();" id="search1" style='margin-top:-3px;'>搜索</label>
                        <label class="bt-bg-style bg-light-blue bt-small none" onclick="research();" id="search2" style='margin-top:-3px;'>重新搜索</label>
                        <span style="display:none;">
							<!-- 弹框 -->
							<div class="title-click nobor  pop-new-data" id="popEngineer"></div>
						</span>
                        <input type="hidden" name="name" id="name">
                        <input type="hidden" name="engineerId" id="engineerId">
                        <input type="hidden" name="type"   value="541">
                    </div>
                    <div id="searchNameError"></div>
                </div>
            </li>
            <li style="margin-top:-10px;">
                <div class="form-tips">
                    <lable>维修商品</lable>
                </div>
                <div class="f_left f_left_wid90">

                    <table class="table">
                        <thead>
                        <tr>
                            <th class="wid3">选择</th>
                            <th class="wid16">产品名称</th>
                            <th class="wid10">品牌</th>
                            <th class="wid10">型号</th>
                            <th class="wid8">物料编码</th>
                            <th class="wid8">销售价</th>
                            <th class="wid8">数量</th>
                            <th class="wid7">单位</th>
                            <th class="wid8">售后数量</th>
                        </tr>
                        </thead>
                        <tbody>
                        <c:forEach items="${list}" var="sgv">
                            <tr>
                                <td>
                                    <input type="checkbox" name="oneSelect" alt="${sgv.afterSalesGoodsId}">
                                    <input type="hidden" name="afterSaleNums" >
                                </td>
                                <td class="text-left">
	                                    <span class="font-blue cursor-pointer addtitle"
                                              tabTitle='{"num":"viewgoods<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
		                    				"link":"/order/afterSalesCommon/viewbaseinfo.do?goodsId=${sgv.goodsId}","title":"产品信息"}'>${sgv.goodsName}</span>
                                    <div>${sgv.sku}</div>
                                </td>
                                <td>${sgv.model}</td>
                                <td>${sgv.brandName}</td>
                                <td>${sgv.materialCode}</td>
                                <td><fmt:formatNumber type="number" value="${sgv.saleorderPrice}" pattern="0.00" maxFractionDigits="2" /></td>
                                <td>${sgv.saleorderNum}</td>
                                <td>${sgv.unitName}</td>
                                <td>
                                    <input type="text" style="width:50px;" alt1="${sgv.afterSalesGoodsId}" value="" onkeyup="this.value=this.value.replace(/\D/g, '')">
                                    <input type="hidden" value="${sgv.num}">
                                </td>
                            </tr>
                        </c:forEach>
                        </tbody>
                    </table>
                </div>
            </li>
            <li>
                <div class="form-tips">
                    <span>*</span>
                    <lable>酬金</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <input class="input-small" type="text" name="engineerAmount" id="engineerAmount" value="" placeholder="请输入本次售后服务费">
                        <input type="hidden" name="afterSalesId" value="${afterSalesInstallstionVo.afterSalesId}">
                        <input type="hidden" name="areaId" id="areaId" value="${afterSalesInstallstionVo.areaId}">
                        <input type="hidden" name="formToken" value="${formToken}"/>
                    </div>
                    <div id="engineerAmountError"></div>
                </div>
            </li>
            <li>
                <div class="form-tips">
                    <span>*</span>
                    <lable>服务时间</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <div class="form-blanks">
                            <input class="Wdate input-small input-smaller96 mr5" type="text" placeholder="请选择日期"
                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'end\')}'})" autocomplete="off"
                                   name="start" id="serviceTime" value="<date:date value ='${start}' format="yyyy-MM-dd"/>"/>
                            <input type="hidden" name="end" id="end" onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'serviceTime\')}'})" autocomplete="off"
                                   value="<date:date value ='${end}' format="yyyy-MM-dd"/>">
                        </div>
                        <div id="serviceTimeError"></div>
                    </div>
                    <div class="pop-friend-tips mt5">
                        友情提示：
                        <br/> 1、酬金指我方支付给售后安调公司的费用；
                        <br/> 2、售后服务费指我方向客户收取的费用；
                    </div>
                </div>
            </li>
        </ul>
        <div class="add-tijiao tcenter">
            <input type="hidden" name="formToken" value="${formToken}"/>
            <%--<input type="hidden" name="afterSalesId" value="${afterSalesFollowUpRecord.afterSalesId}">--%>
            <button class="dele" id="close-layer" type="button"  style="background-color: #f5f7fa;border-color: #ced2d9;color: black;" onclick="closeGoBack();">取消</button>
            <button type="submit" id="submit" style="background-color: #3384ef;border-color: #fff;">确定</button>
        </div>
    </form>
</div>
<%@ include file="../../common/footer.jsp"%>