<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.kpi.replica.dao.SaleorderReplicaMapper" >

    <resultMap id="BaseResultMap" type="com.vedeng.order.model.Saleorder">
        <id column="SALEORDER_ID" property="saleorderId" jdbcType="INTEGER"/>
        <result column="QUOTEORDER_ID" property="quoteorderId" jdbcType="INTEGER"/>
        <result column="PARENT_ID" property="parentId" jdbcType="INTEGER"/>
        <result column="SALEORDER_NO" property="saleorderNo" jdbcType="VARCHAR"/>
        <result column="ORDER_TYPE" property="orderType" jdbcType="BIT"/>
        <result column="COMPANY_ID" property="companyId" jdbcType="INTEGER"/>
        <result column="ORG_ID" property="orgId" jdbcType="INTEGER"/>
        <result column="USER_ID" property="userId" jdbcType="INTEGER"/>
        <result column="VALID_STATUS" property="validStatus" jdbcType="BIT"/>
        <result column="VALID_TIME" property="validTime" jdbcType="BIGINT"/>
        <result column="STATUS" property="status" jdbcType="BIT"/>
        <result column="PURCHASE_STATUS" property="purchaseStatus" jdbcType="BIT"/>
        <result column="LOCKED_STATUS" property="lockedStatus" jdbcType="BIT"/>
        <result column="INVOICE_STATUS" property="invoiceStatus" jdbcType="BIT"/>
        <result column="INVOICE_TIME" property="invoiceTime" jdbcType="BIGINT"/>
        <result column="PAYMENT_STATUS" property="paymentStatus" jdbcType="BIT"/>
        <result column="PAYMENT_TIME" property="paymentTime" jdbcType="BIGINT"/>
        <result column="DELIVERY_STATUS" property="deliveryStatus" jdbcType="BIT"/>
        <result column="DELIVERY_TIME" property="deliveryTime" jdbcType="BIGINT"/>
        <result column="IS_CUSTOMER_ARRIVAL" property="isCustomerArrival" jdbcType="BIT"/>
        <result column="ARRIVAL_STATUS" property="arrivalStatus" jdbcType="BIT"/>
        <result column="ARRIVAL_TIME" property="arrivalTime" jdbcType="BIGINT"/>
        <result column="SERVICE_STATUS" property="serviceStatus" jdbcType="BIT"/>
        <result column="HAVE_ACCOUNT_PERIOD" property="haveAccountPeriod" jdbcType="BIT"/>
        <result column="TOTAL_AMOUNT" property="totalAmount" jdbcType="DECIMAL"/>
        <result column="TRADER_ID" property="traderId" jdbcType="INTEGER"/>
        <result column="CUSTOMER_TYPE" property="customerType" jdbcType="INTEGER"/>
        <result column="CUSTOMER_NATURE" property="customerNature" jdbcType="INTEGER"/>
        <result column="TRADER_NAME" property="traderName" jdbcType="VARCHAR"/>
        <result column="TRADER_CONTACT_ID" property="traderContactId" jdbcType="INTEGER"/>
        <result column="TRADER_CONTACT_NAME" property="traderContactName" jdbcType="VARCHAR"/>
        <result column="TRADER_CONTACT_MOBILE" property="traderContactMobile" jdbcType="VARCHAR"/>
        <result column="TRADER_CONTACT_TELEPHONE" property="traderContactTelephone" jdbcType="VARCHAR"/>
        <result column="TRADER_ADDRESS_ID" property="traderAddressId" jdbcType="INTEGER"/>
        <result column="TRADER_ADDRESS" property="traderAddress" jdbcType="VARCHAR"/>
        <result column="TRADER_COMMENTS" property="traderComments" jdbcType="VARCHAR"/>
        <result column="TAKE_TRADER_ID" property="takeTraderId" jdbcType="INTEGER"/>
        <result column="TAKE_TRADER_NAME" property="takeTraderName" jdbcType="VARCHAR"/>
        <result column="TAKE_TRADER_CONTACT_ID" property="takeTraderContactId" jdbcType="INTEGER"/>
        <result column="TAKE_TRADER_CONTACT_NAME" property="takeTraderContactName" jdbcType="VARCHAR"/>
        <result column="TAKE_TRADER_CONTACT_MOBILE" property="takeTraderContactMobile" jdbcType="VARCHAR"/>
        <result column="TAKE_TRADER_CONTACT_TELEPHONE" property="takeTraderContactTelephone" jdbcType="VARCHAR"/>
        <result column="TAKE_TRADER_ADDRESS_ID" property="takeTraderAddressId" jdbcType="INTEGER"/>
        <result column="TAKE_TRADER_ADDRESS" property="takeTraderAddress" jdbcType="VARCHAR"/>
        <result column="IS_SEND_INVOICE" property="isSendInvoice" jdbcType="BIT"/>
        <result column="INVOICE_TRADER_ID" property="invoiceTraderId" jdbcType="INTEGER"/>
        <result column="INVOICE_TRADER_NAME" property="invoiceTraderName" jdbcType="VARCHAR"/>
        <result column="INVOICE_TRADER_CONTACT_ID" property="invoiceTraderContactId" jdbcType="INTEGER"/>
        <result column="INVOICE_TRADER_CONTACT_NAME" property="invoiceTraderContactName" jdbcType="VARCHAR"/>
        <result column="INVOICE_TRADER_CONTACT_MOBILE" property="invoiceTraderContactMobile" jdbcType="VARCHAR"/>
        <result column="INVOICE_TRADER_CONTACT_TELEPHONE" property="invoiceTraderContactTelephone" jdbcType="VARCHAR"/>
        <result column="INVOICE_TRADER_ADDRESS_ID" property="invoiceTraderAddressId" jdbcType="INTEGER"/>
        <result column="INVOICE_TRADER_ADDRESS" property="invoiceTraderAddress" jdbcType="VARCHAR"/>
        <result column="SALES_AREA_ID" property="salesAreaId" jdbcType="INTEGER"/>
        <result column="SALES_AREA" property="salesArea" jdbcType="VARCHAR"/>
        <result column="TERMINAL_TRADER_ID" property="terminalTraderId" jdbcType="INTEGER"/>
        <result column="TERMINAL_TRADER_NAME" property="terminalTraderName" jdbcType="VARCHAR"/>
        <result column="TERMINAL_TRADER_TYPE" property="terminalTraderType" jdbcType="INTEGER"/>
        <result column="INVOICE_TYPE" property="invoiceType" jdbcType="INTEGER"/>
        <result column="INVOICE_METHOD" property="invoiceMethod" jdbcType="INTEGER"/>
        <result column="FREIGHT_DESCRIPTION" property="freightDescription" jdbcType="INTEGER"/>
        <result column="DELIVERY_TYPE" property="deliveryType" jdbcType="INTEGER"/>
        <result column="LOGISTICS_ID" property="logisticsId" jdbcType="INTEGER"/>
        <result column="PAYMENT_TYPE" property="paymentType" jdbcType="INTEGER"/>
        <result column="PREPAID_AMOUNT" property="prepaidAmount" jdbcType="DECIMAL"/>
        <result column="ACCOUNT_PERIOD_AMOUNT" property="accountPeriodAmount" jdbcType="DECIMAL"/>
        <result column="LOGISTICS_COLLECTION" property="logisticsCollection" jdbcType="BIT"/>
        <result column="RETAINAGE_AMOUNT" property="retainageAmount" jdbcType="DECIMAL"/>
        <result column="RETAINAGE_AMOUNT_MONTH" property="retainageAmountMonth" jdbcType="INTEGER"/>
        <result column="PAYMENT_COMMENTS" property="paymentComments" jdbcType="VARCHAR"/>
        <result column="ADDITIONAL_CLAUSE" property="additionalClause" jdbcType="VARCHAR"/>
        <result column="LOGISTICS_COMMENTS" property="logisticsComments" jdbcType="VARCHAR"/>
        <result column="FINANCE_COMMENTS" property="financeComments" jdbcType="VARCHAR"/>
        <result column="COMMENTS" property="comments" jdbcType="VARCHAR"/>
        <result column="INVOICE_COMMENTS" property="invoiceComments" jdbcType="VARCHAR"/>
        <result column="DELIVERY_DIRECT" property="deliveryDirect" jdbcType="BIT"/>
        <result column="SUPPLIER_CLAUSE" property="supplierClause" jdbcType="VARCHAR"/>
        <result column="HAVE_ADVANCE_PURCHASE" property="haveAdvancePurchase" jdbcType="BIT"/>
        <result column="IS_URGENT" property="isUrgent" jdbcType="BIT"/>
        <result column="URGENT_AMOUNT" property="urgentAmount" jdbcType="DECIMAL"/>
        <result column="HAVE_COMMUNICATE" property="haveCommunicate" jdbcType="BIT"/>
        <result column="PREPARE_COMMENTS" property="prepareComments" jdbcType="VARCHAR"/>
        <result column="MARKETING_PLAN" property="marketingPlan" jdbcType="VARCHAR"/>
        <result column="STATUS_COMMENTS" property="statusComments" jdbcType="INTEGER"/>
        <result column="SYNC_STATUS" property="syncStatus" jdbcType="BIT"/>
        <result column="ADD_TIME" property="addTime" jdbcType="BIGINT"/>
        <result column="CREATOR" property="creator" jdbcType="INTEGER"/>
        <result column="MOD_TIME" property="modTime" jdbcType="BIGINT"/>
        <result column="UPDATER" property="updater" jdbcType="INTEGER"/>
        <result column="TRADER_AREA" property="traderArea" jdbcType="VARCHAR"/>
        <result column="TAKE_TRADER_AREA" property="takeTraderArea" jdbcType="VARCHAR"/>
        <result column="INVOICE_TRADER_AREA" property="invoiceTraderArea" jdbcType="VARCHAR"/>
        <result column="IS_PAYMENT" property="isPayment" jdbcType="BIT"/>
        <result column="VERIFIES_TYPE" property="verifiesType" jdbcType="INTEGER"/>
        <result column="VERIFY_USERNAME" property="verifyUsername" jdbcType="VARCHAR"/>
        <result column="VERIFY_STATUS" property="verifyStatus" jdbcType="INTEGER"/>
        <result column="CONTRACT_STATUS" property="contractStatus" jdbcType="INTEGER"/>
        <result column="ADVANCE_PURCHASE_STATUS" property="advancePurchaseStatus" jdbcType="INTEGER"/>
        <result column="ADVANCE_PURCHASE_COMMENTS" property="advancePurchaseComments" jdbcType="VARCHAR"/>
        <result column="ADVANCE_PURCHASE_TIME" property="advancePurchaseTime" jdbcType="BIGINT"/>

        <result column="SATISFY_INVOICE_TIME" property="satisfyInvoiceTime" jdbcType="BIGINT"/>
        <result column="SATISFY_DELIVERY_TIME" property="satisfyDeliveryTime" jdbcType="BIGINT"/>

        <result column="IS_DELAY_INVOICE" property="isDelayInvoice" jdbcType="BIT"/>
        <result column="LOCKED_REASON" property="lockedReason" jdbcType="VARCHAR"/>
        <result column="COST_USER_IDS" property="costUserIds" jdbcType="VARCHAR"/>
        <!-- 收票邮箱 -->
        <result column="INVOICE_EMAIL" property="invoiceEmail" jdbcType="VARCHAR" />
        <!-- 订单归属  userId -->
        <result column="OWNER_USER_ID" property="ownerUserId" jdbcType="INTEGER" />

        <result column="PAY_TYPE" property="payType" jdbcType="BIT"/>
        <result column="PAYMENT_MODE" property="paymentMode" jdbcType="BIT"/>

        <result column="IS_SALES_PERFORMANCE" property="isSalesPerformance" jdbcType="INTEGER" />
        <result column="SALES_PERFORMANCE_TIME" property="salesPerformanceTime" jdbcType="BIGINT" />

        <result column="TRADER_AREA_ID" property="traderAreaId" jdbcType="INTEGER"/>
        <result column="TAKE_TRADER_AREA_ID" property="takeTraderAreaId" jdbcType="INTEGER"/>
        <result column="INVOICE_TRADER_AREA_ID" property="invoiceTraderAreaId" jdbcType="INTEGER"/>
        <result column="CREATE_MOBILE" property="createMobile" jdbcType="VARCHAR"/>
        <result column="BD_MOBILE_TIME" property="bdMobileTime" jdbcType="BIT"/>
        <result column="ACTION_ID" property="actionId" jdbcType="INTEGER"/>
        <result column="IS_PRINTOUT" property="isPrintout" jdbcType="INTEGER"/>

        <collection property="goodsList" ofType="com.vedeng.order.model.SaleorderGoods">
            <id column="SALEORDER_GOODS_ID" property="saleorderGoodsId" jdbcType="INTEGER" />
            <result column="SALEORDER_ID" property="saleorderId" jdbcType="INTEGER" />
            <result column="GOODS_ID" property="goodsId" jdbcType="INTEGER" />
            <result column="SKU" property="sku" jdbcType="VARCHAR" />
            <result column="GOODS_NAME" property="goodsName" jdbcType="VARCHAR" />
            <result column="BRAND_NAME" property="brandName" jdbcType="VARCHAR" />
            <result column="MODEL" property="model" jdbcType="VARCHAR" />
            <result column="UNIT_NAME" property="unitName" jdbcType="VARCHAR" />
            <!-- 单价 在耗材商城订单中为优惠后的实际单价  erp的单价 -->
            <result column="PRICE" property="price" jdbcType="DECIMAL" />
            <!-- 实际单价, 耗材商城订单优惠前原来的单价 -->
            <result column="REAL_PRICE" property="realPrice" jdbcType="DECIMAL" />
            <result column="CURRENCY_UNIT_ID" property="currencyUnitId" jdbcType="INTEGER" />
            <result column="NUM" property="num" jdbcType="INTEGER" />
            <result column="BUY_NUM" property="dbBuyNum" jdbcType="INTEGER" />
            <result column="DELIVERY_NUM" property="deliveryNum" jdbcType="INTEGER" />
            <result column="DELIVERY_STATUS" property="deliveryStatus" jdbcType="BIT" />
            <result column="DELIVERY_TIME" property="deliveryTime" jdbcType="BIGINT" />
            <result column="IS_IGNORE" property="isIgnore" jdbcType="BIT" />
            <result column="IGNORE_TIME" property="ignoreTime" jdbcType="BIGINT" />
            <result column="IGNORE_USER_ID" property="ignoreUserId" jdbcType="INTEGER" />
            <result column="PURCHASING_PRICE" property="purchasingPrice" jdbcType="VARCHAR" />
            <result column="DELIVERY_CYCLE" property="deliveryCycle" jdbcType="VARCHAR" />
            <result column="DELIVERY_DIRECT" property="deliveryDirect" jdbcType="BIT" />
            <result column="DELIVERY_DIRECT_COMMENTS" property="deliveryDirectComments" jdbcType="VARCHAR" />
            <result column="REGISTRATION_NUMBER" property="registrationNumber" jdbcType="VARCHAR" />
            <result column="SUPPLIER_NAME" property="supplierName" jdbcType="VARCHAR" />
            <result column="REFERENCE_COST_PRICE" property="referenceCostPrice" jdbcType="DECIMAL" />
            <result column="REFERENCE_PRICE" property="referencePrice" jdbcType="DECIMAL" />
            <result column="REFERENCE_DELIVERY_CYCLE" property="referenceDeliveryCycle" jdbcType="VARCHAR" />
            <result column="REPORT_STATUS" property="reportStatus" jdbcType="BIT" />
            <result column="REPORT_COMMENTS" property="reportComments" jdbcType="VARCHAR" />
            <result column="HAVE_INSTALLATION" property="haveInstallation" jdbcType="BIT" />
            <result column="GOODS_COMMENTS" property="goodsComments" jdbcType="VARCHAR" />
            <result column="INSIDE_COMMENTS" property="insideComments" jdbcType="VARCHAR" />
            <result column="ARRIVAL_USER_ID" property="arrivalUserId" jdbcType="INTEGER" />
            <result column="ARRIVAL_STATUS" property="arrivalStatus" jdbcType="BIT" />
            <result column="ARRIVAL_TIME" property="arrivalTime" jdbcType="BIGINT" />
            <result column="IS_DELETE" property="isDelete" jdbcType="BIT" />
            <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
            <result column="CREATOR" property="creator" jdbcType="INTEGER" />
            <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
            <result column="UPDATER" property="updater" jdbcType="INTEGER" />
            <result column="LOCKED_STATUS" property="lockedStatus" jdbcType="INTEGER" />
            <!-- 当前sku最大退款金额即当前订单商品实际金额(含退货) -->
            <result column="MAX_SKU_REFUND_AMOUNT" property="maxSkuRefundAmount" jdbcType="DECIMAL" />
            <result column="JX_MARKET_PRICE" property="jxMarketPrice" jdbcType="DECIMAL" />
            <result column="JX_SALE_PRICE" property="jxSalePrice" jdbcType="DECIMAL" />
            <result column="OCCUPY_NUM" property="occupyNum" jdbcType="INTEGER" />
            <result column="IS_ACTION_GOODS" property="isActionGoods" jdbcType="INTEGER" />
            <result column="ACTION_OCCUPY_NUM" property="actionOccupyNum" jdbcType="INTEGER" />
        </collection>
    </resultMap>

    <select id="getSaleorderDetails" resultMap="BaseResultMap">
        SELECT * FROM T_SALEORDER S JOIN T_SALEORDER_GOODS SG on S.SALEORDER_ID = SG.SALEORDER_ID WHERE S.SALEORDER_NO = #{saleorderNo} AND SG.IS_DELETE = 0
    </select>
    <select id="getTotalAmountOfSaleorder" resultType="java.math.BigDecimal">
        SELECT TOTAL_AMOUNT FROM T_SALEORDER WHERE SALEORDER_NO = #{saleorderNo}
    </select>
    <select id="getSaleorderBySaleorderGoodsId" resultMap="BaseResultMap">
        SELECT * FROM T_SALEORDER S JOIN T_SALEORDER_GOODS SG on S.SALEORDER_ID = SG.SALEORDER_ID WHERE SG.SALEORDER_GOODS_ID = #{saleorderGoodsId} AND SG.IS_DELETE = 0
    </select>
    <select id="getSaleorderByAfterSalesId" resultMap="BaseResultMap">
        SELECT
               TS.*, TSG.*
        FROM T_AFTER_SALES A
            LEFT JOIN T_SALEORDER TS
                on A.ORDER_NO = TS.SALEORDER_NO
            LEFT JOIN T_SALEORDER_GOODS TSG on TS.SALEORDER_ID = TSG.SALEORDER_ID
        WHERE A.AFTER_SALES_ID = #{afterSalesId} AND TSG.IS_DELETE = 0
    </select>
    <select id="getSaleorderIdByAddTime" resultType="java.lang.Integer">
        SELECT SALEORDER_ID FROM T_SALEORDER S WHERE
            S.ADD_TIME &gt; #{startTime}
            AND S.SALEORDER_ID >= #{startSaleorderId}
            <if test="endTime > 0">
                AND S.ADD_TIME &lt;= #{endTime}
            </if>
            LIMIT #{offSet},#{limit}
    </select>
    <select id="getCountOfSaleorderInSalePerformanceByTraderId" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM T_SALEORDER WHERE TRADER_ID = #{traderId} AND IS_SALES_PERFORMANCE = 1 AND SALES_PERFORMANCE_TIME &lt; #{salePerformanceTime} AND SALEORDER_NO != #{saleorderNo}
    </select>
    <select id="getSaleorderBySaleorderIdList" resultMap="BaseResultMap">
        SELECT * FROM T_SALEORDER S JOIN T_SALEORDER_GOODS SG ON S.SALEORDER_ID = SG.SALEORDER_ID WHERE S.SALEORDER_ID IN
        <foreach collection="list" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND SG.IS_DELETE = 0
    </select>
    <select id="getSaleorderIdByValidTimeOrModTime" resultType="java.lang.Integer">
        SELECT SALEORDER_ID FROM T_SALEORDER WHERE ORDER_TYPE IN (0,1,5) AND (UPDATE_DATA_TIME &gt; #{startTime})
        AND SALEORDER_ID &gt;= #{startSaleorderId}
        <if test="endTime > 0">
            AND (UPDATE_DATA_TIME &lt;= #{endTime})
        </if>
        AND IS_SALES_PERFORMANCE = 0 AND PAYMENT_STATUS = 2
        LIMIT #{offSet},#{limit}
    </select>
    <select id="getSaleorderNoBySaleorderGoodsId" resultType="java.lang.String">
        SELECT S.SALEORDER_NO FROM T_SALEORDER S JOIN T_SALEORDER_GOODS SG on S.SALEORDER_ID = SG.SALEORDER_ID WHERE SG.SALEORDER_GOODS_ID = #{saleorderGoodsId}
    </select>
    <select id="getSaleorderDetailsBySaleorderId" resultMap="BaseResultMap">
        SELECT * FROM T_SALEORDER S JOIN T_SALEORDER_GOODS SG on S.SALEORDER_ID = SG.SALEORDER_ID WHERE S.SALEORDER_ID = #{saleorderId} AND SG.IS_DELETE = 0
    </select>


</mapper>