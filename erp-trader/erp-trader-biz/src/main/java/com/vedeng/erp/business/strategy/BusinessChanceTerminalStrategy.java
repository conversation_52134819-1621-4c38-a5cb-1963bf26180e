package com.vedeng.erp.business.strategy;

import com.vedeng.erp.business.domain.entity.BussinessChanceEntity;
import com.vedeng.erp.business.mapper.BussinessChanceMapper;
import com.vedeng.erp.saleorder.dto.OrderTerminalDto;
import com.vedeng.erp.saleorder.enums.OrderTerminalConstant;
import com.vedeng.erp.saleorder.service.OrderTerminalApiService;
import com.vedeng.erp.saleorder.strategy.OrderTerminalStrategy;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 商机终端信息处理类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/5 13:21
 */
@Service
public class BusinessChanceTerminalStrategy implements OrderTerminalStrategy {

    @Autowired
    private OrderTerminalApiService orderTerminalApiService;

    @Autowired
    private BussinessChanceMapper bussinessChanceMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void execute(OrderTerminalDto orderTerminalDto) {
        // 商机 终端名称和省市区 都是非必填项，因此校验只有维护了才存。
        if (StringUtils.isNotBlank(orderTerminalDto.getTerminalName())) {
            BussinessChanceEntity update = new BussinessChanceEntity();
            update.setBussinessChanceId(orderTerminalDto.getBusinessId());
            orderTerminalDto.setBusinessType(1);
            // 新增/编辑终端信息
            // 同步修改商机表的终端信息
            update.setTerminalTraderName(orderTerminalDto.getTerminalName());
            update.setTerminalTraderType(0);
            bussinessChanceMapper.updateByPrimaryKeySelective(update);
            orderTerminalApiService.save(orderTerminalDto);
        }
    }
}