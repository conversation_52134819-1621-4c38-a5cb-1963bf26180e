<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>选择采购订单</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="validBuyorderList" datasource="erp-datasourcetarget" DEFAULT_EMPTY=1 empty_show="" fixednumber="0" firstcol="radio">
<div id="appendHead">
</div>
<div class="layui-fluid">
    <form class="layui-form" id="search">
        <div class="layui-inline ">
            <label>采购单号</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder="请输入" oper="like" name="BUYORDER_NO" empty_show="-" style="" alias="">
            </div>
        </div>
         <div class="layui-inline ">
                    <label>供应商</label>
                    <div class="layui-input-inline">
                        <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder="请输入" oper="like" name="TRADER_NAME" empty_show="-" style="" alias="">
                    </div>
                </div>
          <div class=" layui-inline "><label class="layui-form-label">创建人</label>
           <div class="layui-input-inline">
            <object class=" layui-input list-search-item " type="21" name="CREATOR" placeholder="" style="" alias="bo" jdbctype="" data="SELECT DISTINCT u.USER_ID K, lower(u.USERNAME) V
      FROM T_USER u
               LEFT JOIN T_R_USER_POSIT up ON u.USER_ID = up.USER_ID
               LEFT JOIN T_POSITION p on up.POSITION_ID = p.POSITION_ID
               LEFT JOIN T_ORGANIZATION o ON p.ORG_ID = o.ORG_ID
      WHERE o.TYPE = 311   order by u.USERNAME" datatype="KVSQLCACHE" oper=""></object>
           </div>
          </div>
          <div class=" layui-inline ">
           <label class="layui-form-label">创建部门</label>
           <div class="layui-input-inline"><object class=" layui-input list-search-item " type="search-orggyl" name="ORG_ID" placeholder="" style="" alias="bo" jdbctype=""   oper="IN"></object>
           </div>
          </div>


    </form>
    <hr class="layui-border-blue">
    <div class="btn-group   bd-highlight" id="tableButton">
<button  class="layui-btn list-row-button"  url="ez_callback_chooseBuyorder" name="
BUYORDER_ID,BUYORDER_NO,TRADER_NAME,CREATOR_NAME,ORG_NAME,ADD_TIME,TRADER_ID,TRADER_NAME,TRADER_CONTACT_ID,TRADER_CONTACT_NAME,TRADER_CONTACT_MOBILE,TRADER_CONTACT_TELEPHONE,TRADER_ADDRESS_ID,TRADER_AREA,TRADER_ADDRESS,TRADER_COMMENTS
 " windowname="" style type="tableselectradio">选择</button>
    </div>
    <table id="table" class="layui-table" style=" width:100%">
        <thead>
        <tr id="column">
            <th name="BUYORDER_NO" >采购单号</th>
            <th name="TRADER_NAME" >供应商</th>
           <th name="CREATOR_NAME" >创建人</th>
           <th name="ORG_NAME" >创建人部门</th>
            <th item_name="ADD_TIME" name="ADD_TIME" body="td-text"  url="" opentype="MODEL" windowname="" datatype="" data="" style="" >创建时间</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td colspan="100">
                express:
                <pre id="express" class="layui-code" orderby="order by BUYORDER_ID desc" groupby="">
     		SELECT
                BUYORDER_ID,
            	BUYORDER_NO,
            	TRADER_NAME,
            	u.USERNAME CREATOR_NAME,
            	o.ORG_NAME ORG_NAME,
            	FROM_UNIXTIME( IF ( bo.ADD_TIME = 0, NULL, bo.ADD_TIME ) / 1000, '%Y-%m-%d %H:%i:%s' ) AS ADD_TIME,
                TRADER_ID,
                TRADER_CONTACT_ID,
                TRADER_CONTACT_NAME,
                TRADER_CONTACT_MOBILE,
                TRADER_CONTACT_TELEPHONE,
                TRADER_ADDRESS_ID,
                TRADER_AREA,
                TRADER_ADDRESS,
                TRADER_COMMENTS
            FROM
            	T_BUYORDER bo
            	LEFT join T_USER u on bo.CREATOR = u.USER_ID
            	left join T_ORGANIZATION o on bo.ORG_ID = o.ORG_ID
            	WHERE
                    VALID_STATUS = 1
     </pre>
                <pre id="count" class="layui-code">

     </pre>
            </td>
        </tr>
        </tbody>
    </table>
</div>
<div id="appendFoot">
    <script>

        console.log('hello world!')
    </script>
</div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    })
</script>
</body>
</html>