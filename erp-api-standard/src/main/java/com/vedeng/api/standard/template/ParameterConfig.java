package com.vedeng.api.standard.template;

/**
 * 参数配置类
 * 用于配置Controller方法的参数类型和值
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-27
 */
public class ParameterConfig {
    
    /**
     * 参数类型
     */
    private Class<?> type;
    
    /**
     * 参数值（可以是实际值或占位符）
     */
    private Object value;
    
    /**
     * 是否为业务对象占位符
     */
    private boolean isBusinessObjectPlaceholder;
    
    /**
     * 是否从callParams中获取
     */
    private String paramKey;

    /**
     * 是否为请求参数（用于request.getParameter()）
     */
    private boolean isRequestParameter;

    /**
     * 请求参数名称（当isRequestParameter为true时使用）
     */
    private String requestParameterName;

    /**
     * 是否从验证上下文中获取
     */
    private boolean isFromValidationContext;

    /**
     * 验证上下文中的键名（当isFromValidationContext为true时使用）
     */
    private String validationContextKey;

    /**
     * 私有构造函数
     */
    private ParameterConfig(Class<?> type, Object value) {
        this.type = type;
        this.value = value;
        this.isBusinessObjectPlaceholder = false;
        this.isRequestParameter = false;
        this.isFromValidationContext = false;
    }
    
    /**
     * 私有构造函数（业务对象占位符）
     */
    private ParameterConfig(Class<?> type) {
        this.type = type;
        this.value = null;
        this.isBusinessObjectPlaceholder = true;
        this.isRequestParameter = false;
        this.isFromValidationContext = false;
    }

    /**
     * 私有构造函数（从参数中获取）
     */
    private ParameterConfig(Class<?> type, String paramKey) {
        this.type = type;
        this.value = null;
        this.paramKey = paramKey;
        this.isBusinessObjectPlaceholder = false;
        this.isRequestParameter = false;
        this.isFromValidationContext = false;
    }

    /**
     * 私有构造函数（请求参数）
     */
    private ParameterConfig(String requestParameterName, Object value) {
        this.type = String.class; // 请求参数统一为String类型
        this.value = value;
        this.requestParameterName = requestParameterName;
        this.isBusinessObjectPlaceholder = false;
        this.isRequestParameter = true;
        this.isFromValidationContext = false;
    }

    /**
     * 私有构造函数（从验证上下文获取）
     */
    private ParameterConfig(Class<?> type, String validationContextKey, boolean isFromValidationContext) {
        this.type = type;
        this.value = null;
        this.validationContextKey = validationContextKey;
        this.isBusinessObjectPlaceholder = false;
        this.isRequestParameter = false;
        this.isFromValidationContext = isFromValidationContext;
    }
    
    /**
     * 创建固定值参数配置
     * 
     * @param type 参数类型
     * @param value 参数值
     * @return 参数配置
     */
    public static ParameterConfig of(Class<?> type, Object value) {
        return new ParameterConfig(type, value);
    }
    
    /**
     * 创建业务对象占位符
     * 业务对象将在运行时自动注入
     * 
     * @param type 业务对象类型
     * @return 参数配置
     */
    public static ParameterConfig businessObject(Class<?> type) {
        return new ParameterConfig(type);
    }
    
    /**
     * 创建从callParams中获取的参数配置
     *
     * @param type 参数类型
     * @param paramKey 参数键名
     * @return 参数配置
     */
    public static ParameterConfig fromParams(Class<?> type, String paramKey) {
        return new ParameterConfig(type, paramKey);
    }

    /**
     * 创建请求参数配置（用于request.getParameter()）
     *
     * 适用于Controller方法中通过request.getParameter()获取参数的场景：
     * - request.getParameter("buyorderGoodsIdArr")
     * - request.getParameter("priceArr")
     *
     * @param paramName 请求参数名
     * @param paramValue 请求参数值
     * @return 参数配置
     */
    public static ParameterConfig requestParam(String paramName, Object paramValue) {
        return new ParameterConfig(paramName, paramValue);
    }

    /**
     * 创建从验证上下文获取的参数配置
     *
     * 适用于需要使用验证规则中查询到的数据作为参数的场景：
     * - 验证规则中查询到的销售单信息
     * - 验证规则中查询到的客户信息
     * - 验证规则中查询到的其他业务数据
     *
     * @param validationContextKey 验证上下文中的键名
     * @param type 参数类型
     * @return 参数配置
     */
    public static ParameterConfig fromValidationContext(String validationContextKey, Class<?> type) {
        return new ParameterConfig(type, validationContextKey, true);
    }
    
    /**
     * 便捷方法：创建String类型参数
     */
    public static ParameterConfig string(String value) {
        return of(String.class, value);
    }
    
    /**
     * 便捷方法：创建Integer类型参数
     */
    public static ParameterConfig integer(Integer value) {
        return of(Integer.class, value);
    }
    
    /**
     * 便捷方法：创建Boolean类型参数
     */
    public static ParameterConfig bool(Boolean value) {
        return of(Boolean.class, value);
    }
    
    /**
     * 便捷方法：从参数中获取String
     */
    public static ParameterConfig stringParam(String paramKey) {
        return fromParams(String.class, paramKey);
    }
    
    /**
     * 便捷方法：从参数中获取Integer
     */
    public static ParameterConfig integerParam(String paramKey) {
        return fromParams(Integer.class, paramKey);
    }
    
    // Getter methods
    public Class<?> getType() {
        return type;
    }
    
    public Object getValue() {
        return value;
    }
    
    public boolean isBusinessObjectPlaceholder() {
        return isBusinessObjectPlaceholder;
    }
    
    public String getParamKey() {
        return paramKey;
    }

    public boolean isRequestParameter() {
        return isRequestParameter;
    }

    public String getRequestParameterName() {
        return requestParameterName;
    }

    public boolean isFromValidationContext() {
        return isFromValidationContext;
    }

    public String getValidationContextKey() {
        return validationContextKey;
    }

    @Override
    public String toString() {
        if (isBusinessObjectPlaceholder) {
            return "ParameterConfig{type=" + type.getSimpleName() + ", businessObjectPlaceholder=true}";
        } else if (isRequestParameter) {
            return "ParameterConfig{requestParam='" + requestParameterName + "', value=" + value + "}";
        } else if (isFromValidationContext) {
            return "ParameterConfig{type=" + type.getSimpleName() + ", validationContextKey='" + validationContextKey + "'}";
        } else if (paramKey != null) {
            return "ParameterConfig{type=" + type.getSimpleName() + ", paramKey='" + paramKey + "'}";
        } else {
            return "ParameterConfig{type=" + type.getSimpleName() + ", value=" + value + "}";
        }
    }
}
