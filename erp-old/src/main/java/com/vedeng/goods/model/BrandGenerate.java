package com.vedeng.goods.model;

public class BrandGenerate {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_BRAND.BRAND_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer brandId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_BRAND.COMPANY_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer companyId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_BRAND.BRAND_NATURE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer brandNature;

    /**
     * 品牌名全称
     *
     * @since ERP_LV_2020_86
     */
    private String brandName;

    /**
     * 品牌中文名
     *
     * @since ERP_LV_2020_86
     */
    private String brandNameCn  ;

    /**
     * 品牌英文名
     */
    private String brandNameEn;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_BRAND.MANUFACTURER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String manufacturer;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_BRAND.BRAND_WEBSITE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String brandWebsite;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_BRAND.OWNER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String owner;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_BRAND.LOGO_DOMAIN
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String logoDomain;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_BRAND.LOGO_URI
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String logoUri;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_BRAND.SORT
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer sort;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_BRAND.INITIAL_CN
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String initialCn;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_BRAND.INITIAL_EN
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String initialEn;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_BRAND.SOURCE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer source;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_BRAND.ADD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Long addTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_BRAND.CREATOR
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_BRAND.MOD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Long modTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_BRAND.UPDATER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer updater;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_BRAND.IS_DELETE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer isDelete;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_BRAND.COMMENTS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String comments;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_BRAND.DESCRIPTION
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String description;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_BRAND.BRAND_ID
     *
     * @return the value of T_BRAND.BRAND_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getBrandId() {
        return brandId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_BRAND.BRAND_ID
     *
     * @param brandId the value for T_BRAND.BRAND_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_BRAND.COMPANY_ID
     *
     * @return the value of T_BRAND.COMPANY_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_BRAND.COMPANY_ID
     *
     * @param companyId the value for T_BRAND.COMPANY_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_BRAND.BRAND_NATURE
     *
     * @return the value of T_BRAND.BRAND_NATURE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getBrandNature() {
        return brandNature;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_BRAND.BRAND_NATURE
     *
     * @param brandNature the value for T_BRAND.BRAND_NATURE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setBrandNature(Integer brandNature) {
        this.brandNature = brandNature;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_BRAND.BRAND_NAME
     *
     * @return the value of T_BRAND.BRAND_NAME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getBrandName() {
        return brandName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_BRAND.BRAND_NAME
     *
     * @param brandName the value for T_BRAND.BRAND_NAME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getBrandNameCn() {
        return brandNameCn;
    }

    public void setBrandNameCn(String brandNameCn) {
        this.brandNameCn = brandNameCn;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_BRAND.BRAND_NAME_EN
     *
     * @return the value of T_BRAND.BRAND_NAME_EN
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getBrandNameEn() {
        return brandNameEn;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_BRAND.BRAND_NAME_EN
     *
     * @param brandNameEn the value for T_BRAND.BRAND_NAME_EN
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setBrandNameEn(String brandNameEn) {
        this.brandNameEn = brandNameEn;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_BRAND.MANUFACTURER
     *
     * @return the value of T_BRAND.MANUFACTURER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getManufacturer() {
        return manufacturer;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_BRAND.MANUFACTURER
     *
     * @param manufacturer the value for T_BRAND.MANUFACTURER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_BRAND.BRAND_WEBSITE
     *
     * @return the value of T_BRAND.BRAND_WEBSITE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getBrandWebsite() {
        return brandWebsite;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_BRAND.BRAND_WEBSITE
     *
     * @param brandWebsite the value for T_BRAND.BRAND_WEBSITE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setBrandWebsite(String brandWebsite) {
        this.brandWebsite = brandWebsite;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_BRAND.OWNER
     *
     * @return the value of T_BRAND.OWNER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getOwner() {
        return owner;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_BRAND.OWNER
     *
     * @param owner the value for T_BRAND.OWNER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setOwner(String owner) {
        this.owner = owner;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_BRAND.LOGO_DOMAIN
     *
     * @return the value of T_BRAND.LOGO_DOMAIN
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getLogoDomain() {
        return logoDomain;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_BRAND.LOGO_DOMAIN
     *
     * @param logoDomain the value for T_BRAND.LOGO_DOMAIN
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setLogoDomain(String logoDomain) {
        this.logoDomain = logoDomain;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_BRAND.LOGO_URI
     *
     * @return the value of T_BRAND.LOGO_URI
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getLogoUri() {
        return logoUri;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_BRAND.LOGO_URI
     *
     * @param logoUri the value for T_BRAND.LOGO_URI
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setLogoUri(String logoUri) {
        this.logoUri = logoUri;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_BRAND.SORT
     *
     * @return the value of T_BRAND.SORT
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getSort() {
        return sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_BRAND.SORT
     *
     * @param sort the value for T_BRAND.SORT
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setSort(Integer sort) {
        this.sort = sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_BRAND.INITIAL_CN
     *
     * @return the value of T_BRAND.INITIAL_CN
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getInitialCn() {
        return initialCn;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_BRAND.INITIAL_CN
     *
     * @param initialCn the value for T_BRAND.INITIAL_CN
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setInitialCn(String initialCn) {
        this.initialCn = initialCn;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_BRAND.INITIAL_EN
     *
     * @return the value of T_BRAND.INITIAL_EN
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getInitialEn() {
        return initialEn;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_BRAND.INITIAL_EN
     *
     * @param initialEn the value for T_BRAND.INITIAL_EN
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setInitialEn(String initialEn) {
        this.initialEn = initialEn;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_BRAND.SOURCE
     *
     * @return the value of T_BRAND.SOURCE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getSource() {
        return source;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_BRAND.SOURCE
     *
     * @param source the value for T_BRAND.SOURCE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setSource(Integer source) {
        this.source = source;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_BRAND.ADD_TIME
     *
     * @return the value of T_BRAND.ADD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Long getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_BRAND.ADD_TIME
     *
     * @param addTime the value for T_BRAND.ADD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_BRAND.CREATOR
     *
     * @return the value of T_BRAND.CREATOR
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_BRAND.CREATOR
     *
     * @param creator the value for T_BRAND.CREATOR
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_BRAND.MOD_TIME
     *
     * @return the value of T_BRAND.MOD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Long getModTime() {
        return modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_BRAND.MOD_TIME
     *
     * @param modTime the value for T_BRAND.MOD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setModTime(Long modTime) {
        this.modTime = modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_BRAND.UPDATER
     *
     * @return the value of T_BRAND.UPDATER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getUpdater() {
        return updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_BRAND.UPDATER
     *
     * @param updater the value for T_BRAND.UPDATER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_BRAND.IS_DELETE
     *
     * @return the value of T_BRAND.IS_DELETE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getIsDelete() {
        return isDelete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_BRAND.IS_DELETE
     *
     * @param isDelete the value for T_BRAND.IS_DELETE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_BRAND.COMMENTS
     *
     * @return the value of T_BRAND.COMMENTS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getComments() {
        return comments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_BRAND.COMMENTS
     *
     * @param comments the value for T_BRAND.COMMENTS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setComments(String comments) {
        this.comments = comments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_BRAND.DESCRIPTION
     *
     * @return the value of T_BRAND.DESCRIPTION
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_BRAND.DESCRIPTION
     *
     * @param description the value for T_BRAND.DESCRIPTION
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setDescription(String description) {
        this.description = description;
    }
}