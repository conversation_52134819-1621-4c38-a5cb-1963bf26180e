package com.vedeng.todolist.service.impl;

import com.vedeng.common.util.DateUtil;
import com.vedeng.todolist.constant.TodoListBuzSceneEnum;
import com.vedeng.todolist.dao.TodoListMapper;
import com.vedeng.todolist.model.TodoList;
import com.vedeng.todolist.service.ITodoInstance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Author: daniel
 * @Date: 2020/12/19 11 22
 * @Description:
 */
@Service
public class MaintainDataSupplyAfterSalePolicy implements ITodoInstance {

    private static final Logger logger = LoggerFactory.getLogger(MaintainDataSupplyAfterSalePolicy.class);

    @Resource
    private TodoListMapper todoListMapper;

    @Override
    public synchronized void add(Integer buzId, String buzExtra, String comment, String buzProperty) {
        logger.info("待办事项---商品供应商售后政策，buzId:{}，buzExtra：{}生成待办事项",buzId,buzExtra);
        if (todoListMapper.selectUnHandledByBuzTypeAndBuzId(TodoListBuzSceneEnum.MAINTAIN_DATA_SUPPLY_AFTER_SALE_POLICY.getBuzSceneId(), buzId).size() > 0) {
            todoListMapper.deleteByBuzTypeAndBuzId(TodoListBuzSceneEnum.MAINTAIN_DATA_SUPPLY_AFTER_SALE_POLICY.getBuzSceneId(),buzId);
        }
        TodoList instance = new TodoList(TodoListBuzSceneEnum.MAINTAIN_DATA_SUPPLY_AFTER_SALE_POLICY,buzId,buzExtra,buzProperty,
                comment, DateUtil.sysTimeMillis(),2);
        todoListMapper.insertSelective(instance);
    }

    @Override
    public void finish(Integer buzId) {
        todoListMapper.updateStatusByBuzTypeAndBuzId(TodoListBuzSceneEnum.MAINTAIN_DATA_SUPPLY_AFTER_SALE_POLICY.getBuzSceneId(),buzId,1,
                                                    getCurrentRequestUser().getUserId(),
                                                    System.currentTimeMillis());
        logger.info("待办事项---商品供应商售后政策，buzId;{}更新为已完成",buzId);
    }

    @Override
    public boolean hasTodoItem(Integer bizId) {
        Objects.requireNonNull(bizId, "业务id不能为空");
        return todoListMapper.countUnHandledItem(TodoListBuzSceneEnum.MAINTAIN_DATA_SUPPLY_AFTER_SALE_POLICY.getBuzSceneId(), bizId) > 0;
    }

    public List<TodoList> getUnHandledListByBuzType() {
        return todoListMapper.getUnHandledListByBuzType(TodoListBuzSceneEnum.MAINTAIN_DATA_SUPPLY_AFTER_SALE_POLICY.getBuzSceneId());
    }
}
