package com.newtask;

import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.qualityReport.service.TmpTraderValidtimeExtService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@JobHandler(value = "syncTmpTraderSupplierValidDataTask")
public class syncTmpTraderSupplierValidDataTask extends AbstractJobHandler {

    @Autowired
    private TmpTraderValidtimeExtService tmpTraderValidtimeService;

    private static final Logger LOGGER = LoggerFactory.getLogger(syncTmpTraderSupplierValidDataTask.class);

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("开始执行trader审核时间同步任务");
        LOGGER.info("开始执行trader审核时间同步任务");

        ResultInfo resultInfo = tmpTraderValidtimeService.saveTraderValidTime();

        if(resultInfo.getCode() != 0){
            LOGGER.info("trader审核时间同步任务失败");
            return FAIL;
        }

        return SUCCESS;
    }
}
