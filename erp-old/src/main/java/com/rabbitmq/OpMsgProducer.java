package com.rabbitmq;

import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
public class OpMsgProducer extends MsgProducer implements InitializingBean {
    @Autowired
    @Qualifier(value = "opRabbitTemplate")
    private RabbitTemplate opRabbitTemplate;

    @Override
    public void afterPropertiesSet() throws Exception {
        opRabbitTemplate.setConfirmCallback(this);
        setRabbitTemplate(opRabbitTemplate);
    }
}
