function conadd(){
	var num = Number($("#conadd").siblings(".form-blanks").length)+Number(1);
	var div = '<div class="form-blanks mt10">'+
		'<div class="pos_rel f_left mb8 ">'+
		'<input type="file" class="upload_file" id="file_'+num+'" name="lwfile" style="display: none;" onchange="uploadFile(this,'+num+');">'+
		'<input type="text" class="input-middle" style="margin-right:10px;" id="name_'+num+'" readonly="readonly" placeholder="请上传附件" name="fileName" onclick="file_'+num+'.click();" value ="">'+
		'<input type="hidden" id="uri_'+num+'" name="fileUri" >'+
		'<label class="bt-bg-style bt-middle bg-light-blue ml4" type="file" onclick="return $(\'#file_'+ num +'\').click();">浏览</label>'+
		'</div>'+
		'<div class="f_left ">'+
		'<i class="iconsuccesss mt5 none" id="img_icon_'+num+'"></i>'+
		'<a href="" target="_blank" class="font-blue cursor-pointer mr5 ml10 mt4 none" id="img_view_'+num+'" style="margin:0px 8px 0 13px;">查看</a>'+
		'<span class="font-red cursor-pointer mt4" onclick="del('+num+')" id="img_del_'+num+'">删除</span>'+
		'</div>'+
		'<div class="clear"></div></div>';
	$("#conadd").before(div);
}

function clickNotBiaoZhun(){
	if($('#img_del_non') && !$('#img_del_non').is(':hidden')){
		var num = 'non';
		var sum = Number($("#conadd").siblings(".form-blanks").length);
		var uri = $("#uri_"+num).val();
		if(uri == '' && sum > 1){
			$("#img_del_"+num).parent().parent(".form-blanks").remove();
		}else{
			$("#img_icon_" + num).hide();
			$("#img_view_" + num).hide();
			if (num == 1 || num == "non") {
				$("#img_del_" + num).hide();
			}
			$("#whether_sign_tip").hide();
			$("#name_"+ num).val("");
			$("#uri_"+ num).val("");
			$("#file_"+ num).val("");
		}
	}
	$("#notBiaoZhunDiv").show();

}

function clickBiaoZhun(){
	if($('#img_del_non') && !$('#img_del_non').is(':hidden')){
		var num = 'non';
		var sum = Number($("#conadd").siblings(".form-blanks").length);
		var uri = $("#uri_"+num).val();
		if(uri == '' && sum > 1){
			$("#img_del_"+num).parent().parent(".form-blanks").remove();
		}else{
			$("#img_icon_" + num).hide();
			$("#img_view_" + num).hide();
			if (num == 1 || num == "non") {
				$("#img_del_" + num).hide();
			}
			$("#whether_sign_tip").hide();
			$("#name_"+ num).val("");
			$("#uri_"+ num).val("");
			$("#file_"+ num).val("");
		}
	}
	$("#notBiaoZhunDiv").hide();
}

function del(num){
	var sum = Number($("#conadd").siblings(".form-blanks").length);
	var uri = $("#uri_"+num).val();
	if(uri == '' && sum > 1){
		$("#img_del_"+num).parent().parent(".form-blanks").remove();
	}else{
		index = layer.confirm("您是否确认该操作？", {
			btn: ['确定','取消'] //按钮
		}, function(){
			$("#img_icon_" + num).hide();
			$("#img_view_" + num).hide();
			if (num == 1 || num == "non") {
				$("#img_del_" + num).hide();
			}
			$("#whether_sign_tip").hide();
			$("#name_"+ num).val("");
			$("#uri_"+ num).val("");
			$("#file_"+ num).val("");
			layer.close(index);
		}, function(){
		});
	}
}

function uploadFile(obj,num){
	checkLogin();
	var imgPath = $(obj).val();
	if(imgPath == '' || imgPath == undefined){
		return false;
	}
	/*alert(imgPath)*/
	var oldName=imgPath.substr(imgPath.lastIndexOf('\\')+1);
	//var domain = $("#domain").val();
	//判断上传文件的后缀名
	var strExtension = imgPath.substr(imgPath.lastIndexOf('.') + 1);
	if (strExtension != 'jpg' && strExtension != 'JPG' && strExtension != 'png' && strExtension != 'PNG'
		&& strExtension != 'pdf' && strExtension != 'PDF' && strExtension != 'doc' && strExtension != 'DOC' && strExtension != 'docx' && strExtension != 'DOCX') {
		layer.alert("文件格式不正确,仅支持PDF、WORD、JPG、PNG");
		// 清空url
		$(obj).val("");
		return false;
	}
	var fileSize = 0;
	var isIE = /msie/i.test(navigator.userAgent) && !window.opera;
	if (isIE && !obj.files) {
		var filePath = obj.value;
		var fileSystem = new ActiveXObject("Scripting.FileSystemObject");
		var file = fileSystem.GetFile (filePath);
		fileSize = file.Size;
	}else {
		fileSize = obj.files[0].size;
	}
	fileSize=Math.round(fileSize/1024*100)/100; //单位为KB
	if(fileSize>20480){
		layer.alert("上传附件不得超过20M");
		return false;
	}
	$.ajaxFileUpload({
		url : page_url + '/fileUpload/ajaxFileUploadAuthorization.do', //用于文件上传的服务器端请求地址
		secureuri : false, //一般设置为false
		fileElementId : $(obj).attr("id"), //文件上传控件的id属性
		dataType : 'json',//返回值类型 一般设置为json
		complete : function() {//只要完成即执行，最后执行
		},
		//服务器成功响应处理函数
		success : function(data) {
			if (data.code == 0) {
				debugger;
				$("#name_"+num ).val(oldName);
				$("#uri_"+num ).val(data.filePath);
				$("#img_icon_" + num).attr("class", "iconsuccesss ml7").show();
				$("#img_view_" + num).attr("href", data.httpUrl + data.filePath).show();
				$("#img_del_" + num).show();
			} else {
				layer.alert(data.message);
			}
		},
		//服务器响应失败处理函数
		error : function(data, status, e) {
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}else{
				layer.alert(data.responseText);
			}

		}
	});
}

/**
 * 上传文件-非标授权书附件
 * @param obj
 * @param num
 * @returns {boolean}
 */
function uploadFileNonStandardAuthorization(obj, num) {
	debugger
	checkLogin();
	var imgPath = $(obj).val();
	if (imgPath == '' || imgPath == undefined) {
		return false;
	}
	var oldName = imgPath.substr(imgPath.lastIndexOf('\\') + 1);
	var domain = $("#domain").val();
	//判断上传文件的后缀名
	var strExtension = imgPath.substr(imgPath.lastIndexOf('.') + 1);
	if (strExtension != 'pdf' && strExtension != 'PDF') {
		layer.alert("仅限上传PDF");
		// 清空url
		$(obj).val("");
		return false;
	}

	var fileSize = 0;
	var isIE = /msie/i.test(navigator.userAgent) && !window.opera;
	if (isIE && !obj.files) {
		var filePath = obj.value;
		var fileSystem = new ActiveXObject("Scripting.FileSystemObject");
		var file = fileSystem.GetFile(filePath);
		fileSize = file.Size;
	} else {
		fileSize = obj.files[0].size;
	}
	fileSize = Math.round(fileSize / 1024 * 100) / 100; //单位为KB
	if (fileSize > 20480) {
		layer.alert("上传附件不得超过20M");
		return false;
	}

	$.ajaxFileUpload({
		url: page_url + '/fileUpload/ajaxFileUploadAuthorization.do', //用于文件上传的服务器端请求地址
		secureuri: false, //一般设置为false
		fileElementId: $(obj).attr("id"),
		dataType: 'json',//返回值类型 一般设置为json
		complete: function () {//只要完成即执行，最后执行
		},
		//服务器成功响应处理函数
		success: function (data) {
			if (data.code == 0) {
				$("#name_" + num).val(oldName);
				$("#uri_" + num).val(data.filePath);
				$("#img_icon_" + num).attr("class", "iconsuccesss ml7").show();
				$("#img_view_" + num).attr("href", 'http://' + domain + data.filePath).show();
				$("#img_del_" + num).show();
				$.ajax({
					url: page_url + '/order/quote/whetherIncludeText.do?url=' + data.filePath + '&sealType=' + $("#sealType").val(),
					type: "POST",
					dataType: "json",
					success: function (req) {
						if(req.code == 0){
							$("#whether_sign").val(1);
							$("#whether_sign_tip").hide();
						}
						if(req.code == -1){
							$("#whether_sign").val(0);
							$("#whether_sign_tip").show();
						}
					},
					error: function (req) {
					}
				});
			} else {
				layer.alert(data.message);
			}
		},
		//服务器响应失败处理函数
		error: function (data, status, e) {
			if (data.status == 1001) {
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。请求的接口地址是：" + ((this.url && this.url.startsWith("http")) == true ? (new URL(this.url).pathname) : this.url));
			} else {
				layer.alert(data.responseText);
			}

		}
	});
}


function bc(){
	checkLogin();
	var su=$("#sUser").val();
	var suserNamer=$("#sUserName").val()
	if (su!=suserNamer){
		layer.confirm("你无权限操作", {
			btn : [ '确定' ]
			//按钮
		}, function() {
			layer.closeAll();
		}, function() {
		});
		return;
	}
	$.ajax({
		url:page_url + '/order/quote/authorizationStorage.do',
		data:$('#tjform').serialize(),
		type:"POST",
		dataType : "json",
		success:function(data){
			location.reload();
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});
}


function tj(){
	checkLogin();
	var su=$("#sUser").val();
	var suserNamer=$("#sUserName").val()
	if (su!=suserNamer){
		layer.confirm("你无权限操作", {
			btn : [ '确定' ]
			//按钮
		}, function() {
			layer.closeAll();
		}, function() {
		});
		return;
	}

	var s=$('#purchaseOrBidding').val();
	var purchaseOrBiddingNum=$('#purchaseOrBidding').val().length;
	if (purchaseOrBiddingNum<4 || purchaseOrBiddingNum>200){
		layer.confirm("采购单位/招标公司请务必输入4-200的字符", {
			btn : [ '确定' ]
			//按钮
		}, function() {
			layer.closeAll();
		}, function() {
		});
		return;
	}
	var productCompanyNum=$('#productCompany').val().length;
	if (productCompanyNum<4 || productCompanyNum>200){
		layer.confirm("生产厂家请务必输入4-200的字符", {
			btn : [ '确定' ]
			//按钮
		}, function() {
			layer.closeAll();
		}, function() {
		});
		return;
	}
    var purchaseProjectNameNum=$('#purchaseProjectName').val().length;


	var aftersalesCompanyNum=$('#aftersalesCompany').val().length;
	if (aftersalesCompanyNum<4 || aftersalesCompanyNum>200){
		layer.confirm("售后支持公司全称请务必输入4-200的字符", {
			btn : [ '确定' ]
			//按钮
		}, function() {
			layer.closeAll();
		}, function() {
		});
		return;
	}

	var skuNameNum=$('#skuName').val().length;
	var brandNameNum=$('#brandName').val().length;
	//var modelNum=$('#model').val().length;
	if (skuNameNum==0 || brandNameNum==0 ){
		layer.confirm("产品品牌/型号/名称不得为空", {
			btn : [ '确定' ]
			//按钮
		}, function() {
			layer.closeAll();
		}, function() {
		});
		return;
	}

	var beginTimeNum=$('#beginTime').val().length;
	var endTimeNum=$('#endTime').val().length;
	if (beginTimeNum==0 || endTimeNum==0){
		layer.confirm("开始时间/结束时间不能为空", {
			btn : [ '确定' ]
			//按钮
		}, function() {
			layer.closeAll();
		}, function() {
		});
		return;
	}

	var startDate=$('#beginTime').val()
	var endDate=$('#endTime').val()
	var startTime = new Date(Date.parse(startDate.replace(/-/g, "/"))).getTime();
	var endTime = new Date(Date.parse(endDate.replace(/-/g, "/"))).getTime();
	var dates =(endTime - startTime)/(1000*60*60*24);
	var authType=$("input[name='authType']:checked").val();
	if(authType == undefined){
		layer.confirm("请选择授权类型", {
			btn : [ '确定' ]
			//按钮
		}, function() {
			layer.closeAll();
		}, function() {
		});
		return;
	}


	if (dates>90 && authType == 0){
		layer.confirm("授权日期不能超过90天", {
			btn : [ '确定' ]
			//按钮
		}, function() {
			layer.closeAll();
		}, function() {
		});
		return;
	}

	if (dates>365 && authType == 1){
		layer.confirm("授权日期不能超过90天", {
			btn : [ '确定' ]
			//按钮
		}, function() {
			layer.closeAll();
		}, function() {
		});
		return;
	}

	if (dates<0){
		layer.confirm("开始日期不得大于结束日期", {
			btn : [ '确定' ]
			//按钮
		}, function() {
			layer.closeAll();
		}, function() {
		});
		return;
	}

	var auNumNum=$('#auNum').val().length;
	if (auNumNum==0){
		layer.confirm("请输入正整数的份数", {
			btn : [ '确定' ]
			//按钮
		}, function() {
			layer.closeAll();
		}, function() {
		});
		return;
	}

    var file1 = $("input[name='fileName']").val().length;
    var purchaseProjectNumNum1=$('#purchaseProjectNum').val().length;

    var describedNum=$('#described').val().length;

	var standardTemplateStr=$("input[name='standardTemplate']:checked").val();
    if(standardTemplateStr==1 && describedNum==0){
        layer.confirm("非标准模板,请填写事项描述", {
            btn : [ '确定' ]
            //按钮
        }, function() {
            layer.closeAll();
        }, function() {
        });
        return;
    }
	var nonStandardAuthorizationUrl = $('[name="nonStandardAuthorizationUrl"]').val().length;
	if(standardTemplateStr==1 && nonStandardAuthorizationUrl==0){
		layer.close(loadingIndex);
		layer.confirm("非标授权,须上传“非标授权附件”", {
			btn : [ '确定' ]
			//按钮
		}, function() {
			layer.closeAll();
		}, function() {
		});
		return;
	}

	// if ((purchaseProjectNameNum==0 || purchaseProjectNumNum1==0) && standardTemplateStr==0){
	// 	layer.confirm("标准模板，请填写项目名称和项目编号", {
	// 		btn : [ '确定' ]
	// 		//按钮
	// 	}, function() {
	// 		layer.closeAll();
	// 	}, function() {
	// 	});
	// 	return
	// }
	var loadingIndex = layer.open({
		type: 3,
		content: '提交中...',
		shade: [0.5, '#000']
	});

	$.ajax({
		async:false,
		url:page_url + '/order/quote/checkAuthorizationApply.do',
		data:$('#tjform').serialize(),
		type:"POST",
		dataType : "json",
		success:function(data){
			if (data.data){
					$.ajax({
						async:false,
						url:page_url + '/order/quote/authorizationApply.do',
						data:$('#tjform').serialize(),
						type:"POST",
						dataType : "json",
						success:function(data2){
							if (data2.data){
								layer.close(loadingIndex);
								pagesContrlpages(true,false,true);
							} else {
								layer.close(loadingIndex);
								layer.confirm(data2.message, {
									btn : [ '确定']
									//按钮
								}, function() {
									window.location.reload();
								});
							}
						},
						error:function(data2){
							layer.close(loadingIndex);
							if(data2.status ==1001){
								layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
							}
						}
					});


			} else {
				layer.close(loadingIndex);
				layer.confirm(data.message, {
					btn : [ '确定']
					//按钮
				}, function() {
					layer.closeAll();
				});

			}
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));

			}
		}
	});


}

$(function () {
	var authType=$("input[name='authType']:checked").val();
	if(authType == undefined || authType==0){//默认是项目授权
		initDateForApply(90,true);
	}else{//经销授权
		initDateForApply(365,true);
	}
});

function initDateForApply(day,first){
	if(day>90){
		$("#pikadayNum").text("经销授权，时间不得超过365天");
	}else{
		$("#pikadayNum").text("项目授权，时间不得超过90天");
	}
	var strTimeToNum = function (str) {
		var num = new Date(str).valueOf();

		return num - 8 * 60 * 60 * 1000;
	};
	if(!first){//非初始进页面，则在切换类型时，需要将原值清空
		$('.J-date-range input').val('');
	}
	$('.J-date-range').each(function () {
		var $input1 = $(this).find('input').eq(0);
		var $input2 = $(this).find('input').eq(1);

		if ($input1.data('pikaday')) {
			$input1.data('pikaday').destroy();
		}
		if ($input2.data('pikaday')) {
			$input2.data('pikaday').destroy();
		}
	});
	//日期空间初始化
	$('.J-date-range').each(function () {
		var year = new Date().getFullYear();
		var $input1 = $(this).find('input').eq(0);
		var $input2 = $(this).find('input').eq(1);
		var pikadayStart = new Pikaday({
			format: 'yyyy-mm-dd',
			field: $(this).find('input').eq(0)[0],
			firstDay: 1,
			yearRange: [year, year + 10],
			onSelect: function (val) {
				console.log("start"+val1);
				pikadayEnd.setMinDate(val);
				pikadayEnd.setMaxDate(new Date(val).valueOf() + day * 1000 * 60 * 60 * 24);
			}
		});
		$input1.data('pikaday', pikadayStart);

		var pikadayEnd = new Pikaday({
			format: 'yyyy-mm-dd',
			field: $(this).find('input').eq(1)[0],
			firstDay: 1,
			yearRange: [year, year + 10],
			onSelect: function (val) {
				console.log("end"+val);
			}
		});
		$input2.data('pikaday', pikadayEnd);
		var val1 = $(this).find('input').eq(0).val();
		var val2 = $(this).find('input').eq(1).val();
		if (val1) {
			var times = strTimeToNum($.trim(val1))
			pikadayEnd.setMinDate(new Date(times));
			pikadayEnd.setMaxDate(new Date(times + day * 1000 * 60 * 60 * 24));
		}

		// 重置 Pikaday 的日期范围
		pikadayStart.setMinDate(null);
		pikadayStart.setMaxDate(null);
		pikadayEnd.setMinDate(null);
		pikadayEnd.setMaxDate(null);
	})
}


function findSku(quoteorderId){
	layer.open({
		type:2,
		shadeClose: false, //点击遮罩关闭
		//area: 'auto',
		area: ['800px', '500px'],
		title: '选择sku',
		btn: null,
		content: page_url + '/order/quote/selSkuByQuote.do?quoteorderId='+quoteorderId,
	});
}


function delAttachment(obj) {
	var uri = $(obj).parent().find("a").attr("href");
	if (uri == '') {
		$(obj).parent().parent().remove();
	} else {
		index = layer.confirm("您是否确认该操作？", {
			btn: ['确定','取消'] //按钮
		}, function(){
			var length = $(obj).parent().parent().parent().find("input[type='file']").length;
			if (length == 1) {
				$(obj).parent().find("i").hide();
				$(obj).parent().find("a").hide();
				$(obj).parent().find("span").hide();
				/*$(obj).parent().parent().parent().find("input[type='text']").val('');*/
			} else {
				$(obj).parent().parent().remove();
			}
			layer.close(index);
		}, function(){
		});
	}
}

function reLook(skuName,brandName,skuModel,skuId) {
	$('#skuName').val(skuName);
	$('#brandName').val(brandName);
	$('#model').val(skuModel);
	$('#skuId').val(skuId);
	layer.closeAll()
}

// 公章类型选择事件处理
function changeSealType() {
	var sealType = $("#sealType").val();
	// 获取隐藏域中的公司信息列表
	var companyInfoListStr = $("#companyInfoListWithSeq").val();
	var companyInfoList = JSON.parse(companyInfoListStr);
	var companyNameNow = "";
	
	// 根据sealType获取对应的公司名称
	if(companyInfoList && companyInfoList.length > 0) {
		for(var i = 0; i < companyInfoList.length; i++) {
			if(companyInfoList[i].frontEndSeq == sealType) {
				companyNameNow = companyInfoList[i].companyName;
				break;
			}
		}
	}
	updateCompanyName(companyNameNow);

	// 根据公章类型动态更新页面上的公司名称
	// if (sealType == "2") {
	// 	// 医购优选公章
	// 	updateCompanyName("南京医购优选供应链管理有限公司");
	// } else {
	// 	// 贝登公章（默认）
	// 	updateCompanyName("南京贝登医疗股份有限公司");
	// }
}

// 更新页面上所有公司名称文本的辅助函数
function updateCompanyName(companyName) {
	var companyInfoListStr = $("#companyInfoListWithSeq").val();
	var companyInfoList = JSON.parse(companyInfoListStr);
	var companyInfoNameArray =[];

	// 根据sealType获取对应的公司名称
	if(companyInfoList && companyInfoList.length > 0) {
		for(var i = 0; i < companyInfoList.length; i++) {
			companyInfoNameArray.push(companyInfoList[i].companyName);
		}
	}
	var companyNameLastStr = companyInfoNameArray.join("|");
	
	// 更新授权书正文中的公司名称（开头部分）
	var contentParagraphs = $(".content p");
	if (contentParagraphs.length > 0) {
		var firstParagraph = contentParagraphs.eq(1);
		var text = firstParagraph.html();
		// 使用正则表达式替换公司名称
		// text = text.replace(/(南京贝登医疗股份有限公司|南京医购优选供应链管理有限公司)/, companyName);
		text = text.replace(new RegExp(companyNameLastStr, "g"), companyName);
		firstParagraph.html(text);
	}

	// 更新授权书正文中的公司名称（未经...书面同意部分）
	if (contentParagraphs.length > 3) {
		var fourthParagraph = contentParagraphs.eq(3);
		var text = fourthParagraph.html();
		// 使用正则表达式替换公司名称
		//text = text.replace(/(南京贝登医疗股份有限公司|南京医购优选供应链管理有限公司)/, companyName);
		text = text.replace(new RegExp(companyNameLastStr, "g"), companyName);
		fourthParagraph.html(text);
	}

	// 更新授权书落款处的公司名称
	var rightParagraph = $(".content p.top.right");
	if (rightParagraph.length > 0) {
		rightParagraph.text(companyName);
	}

	// 更新页面顶部的logo
	if (!(companyName === "南京贝登医疗股份有限公司")) {
		// 医购优选公章类型不显示logo
		$("#company-logo").hide();
	} else {
		$("#company-logo").show();
		$("#company-logo").attr("src", page_url + "/static/images/logonew.jpg");
	}
}

function authorizationPre() {
	var purchaseOrBidding=$("#purchaseOrBidding").val();
	var productCompany=$("#productCompany").val();
	var natureOfOperation=$("#natureOfOperation").val();
	var brandName=$("#brandName").val();
	var skuName=$("#skuName").val();
	var distributionsType=$("#distributionsType").val();
	var purchaseProjectName=$("#purchaseProjectName").val();
	var purchaseProjectNum=$("#purchaseProjectNum").val();
	var fileType=$("#fileType").val();
	var authorizedCompany=$("#authorizedCompany").val();
	var aftersalesCompany=$("#aftersalesCompany").val();
	var beginTime=$("#beginTime").val();
	var endTime=$("#endTime").val();
	var applyYear=$("#applyYear").val();
	var applyMonth=$("#applyMonth").val();
	var applyDay=$("#applyDay").val();
	var sealType=$("#sealType").val();

	url = page_url + "/order/quote/authorizationPreview.do?purchaseOrBidding="+purchaseOrBidding+"&productCompany="+productCompany+"&natureOfOperation="+natureOfOperation
		+"&brandName="+brandName
		+"&skuName="+skuName
		+"&distributionsType="+distributionsType
		+"&authorizedCompany="+authorizedCompany
		+"&purchaseProjectName="+purchaseProjectName
		+"&purchaseProjectNum="+purchaseProjectNum
		+"&fileType="+fileType
		+"&sealType="+sealType
		+"&aftersalesCompany="+aftersalesCompany
		+"&beginTime="+beginTime
		+"&endTime="+endTime
		+"&applyYear="+applyYear
		+"&applyMonth="+applyMonth
		+"&applyDay="+applyDay
	;


	layer.open({
		title: '授权书预览',
		content: url,
		type: 2,
		area: ['95%', '95%']
		,btn: ['关闭预览']
		,btnAlign: 'c'
		,btn1: function(index, layero){
			layer.close(index);
		}
	})
}