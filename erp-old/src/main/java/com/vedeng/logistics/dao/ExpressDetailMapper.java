package com.vedeng.logistics.dao;

import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.model.ExpressDetail;
import com.vedeng.logistics.model.vo.ExpressArrivalDetailVo;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ExpressDetailMapper {
    int deleteByPrimaryKey(Integer expressDetailId);
    
    int deleteSelective(ExpressDetail record);

    int insert(ExpressDetail record);

    int insertSelective(ExpressDetail record);

    ExpressDetail selectByPrimaryKey(Integer expressDetailId);

    int updateByPrimaryKeySelective(ExpressDetail record);

    int updateByPrimaryKey(ExpressDetail record);

	int insertSelectiveBatch(List<ExpressDetail> eList);

    /**
     * 根据关联id集合和业务类型查询相关物流的到货详情
     * @param relatedIdList 相关商品id
     * @param businessType 业务类型
     * @return 物流到货详情
     */
	List<ExpressArrivalDetailVo> getExpressArrivalDetailByRelatedIdListAndBusinessType(@Param("relatedIdList") List<Integer> relatedIdList,
                                                                                   @Param("businessType") Integer businessType);

    /**
     * <b>Description:</b><br> 批量查询采购订单所有发货总数量
     * @param bgList
     * @return
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2018年5月30日 下午7:12:51
     */
    List<BuyorderGoodsVo> batchBuyorderAllDeliveryNum(@Param("bgList")List<BuyorderGoodsVo> bgList);

    /**
     * 查询采购SKU对应物流
     * @param bgv
     * @return
     */
    List<Express> selectAllByRelatedKey(@Param("bgv") BuyorderGoodsVo bgv);

    /**
     * 更新采购物流详情数量
     * @param expressDetail
     */
    void updateExpressDetailNum(@Param("expressDetail") ExpressDetail expressDetail);

    /**
     * 查询多个采购SKU对应物流
     * @param expressIdList
     * @return
     */
    List<Express> selectAllInRelatedKey(@Param("expressIdList")  List<Integer> expressIdList);

    /**
     * 根据快递id和采购SKU查询唯一快递详情数量
     * @param tempBuyOrderGoodsVo
     * @return
     */
    ExpressDetail selectOneByRelatedKey(@Param("paramsVariable") BuyorderGoodsVo tempBuyOrderGoodsVo);

    /**
     * 根据快递id查询所有sku详情
     * @param expressId
     * @return
     */
    List<ExpressDetail> selectAllByExpressId(@Param("expressId") Integer expressId);

    /**
     * <AUTHOR>
     * @desc 根据类型和关联id查询信息
     * @param relatedId
     * @param businessType
     * @return
     */
    List<ExpressDetail> queryByRelateAndType(@Param("relatedId")Integer relatedId,@Param("businessType") Integer businessType);

    /**
     * 包裹详情获取快递信息
     *
     * @param detailId
     * @return
     */
    Express getExpressByDetailId(@Param("detailId") Integer detailId);

    /**
     * 根据expressId 和relateId更新detail
     * @param record
     * @return
     */
    int updateByPrimaryKeySelectiveByExpressId(ExpressDetail record);
}