package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.KingDeePurchaseBackEntity;
import java.util.List;

import com.vedeng.erp.kingdee.dto.KingDeePurchaseBackQueryDto;
import org.apache.ibatis.annotations.Param;

public interface KingDeePurchaseBackMapper {
    /**
     * delete by primary key
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(String id);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(KingDeePurchaseBackEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(KingDeePurchaseBackEntity record);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    KingDeePurchaseBackEntity selectByPrimaryKey(String id);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(KingDeePurchaseBackEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(KingDeePurchaseBackEntity record);

    int updateBatchSelective(List<KingDeePurchaseBackEntity> list);

    int batchInsert(@Param("list") List<KingDeePurchaseBackEntity> list);


    List<KingDeePurchaseBackEntity> findByAll(KingDeePurchaseBackQueryDto kingDeePurchaseBackQueryDto);


}