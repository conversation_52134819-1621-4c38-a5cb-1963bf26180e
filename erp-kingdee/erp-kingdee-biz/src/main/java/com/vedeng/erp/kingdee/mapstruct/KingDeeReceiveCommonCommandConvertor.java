package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.domain.command.KingDeeReceiveCommonCommand;
import com.vedeng.erp.kingdee.dto.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 标准应付单关联的源单是采购入库单和采购退料单，同一个接口，两种场景入参，请注意入参参数。未防止错误，下面分两个场景示例。
 * @date
 */
@Mapper(componentModel = "spring")
public interface KingDeeReceiveCommonCommandConvertor extends BaseCommandMapStruct<KingDeeReceiveCommonCommand,KingDeeReceiveCommonDto> {

    @Mapping(target = "FId", source = "FId")
    @Mapping(target = "FDate", source = "FDate")
    @Mapping(target = "f_QZOK_BDDJTID", source = "FQzokBddjtId")
    @Mapping(target = "FBusinessType", source = "FBusinessType")
    @Mapping(target = "FSetAccountType", source = "FSetAccountType")
    @Mapping(target = "FBillTypeID.FNumber", source = "FBillTypeId")
    @Mapping(target = "FCustomerId.FNumber", source = "FCustomerId")
    @Mapping(target = "FSettleOrgId.FNumber", source = "FSettleOrgId")
    @Mapping(target = "FPayOrgId.FNumber", source = "FPayOrgId")
    KingDeeReceiveCommonCommand toCommand(KingDeeReceiveCommonDto dto);

    @Mapping(target = "FPriceQty", source = "FPriceQty")
    @Mapping(target = "FTaxPrice", source = "FTaxPrice")
    @Mapping(target = "FEntryTaxRate", source = "FEntryTaxRate")
    @Mapping(target = "FNoTaxAmountFor_D", source = "FNoTaxAmountForD")
    @Mapping(target = "FTaxAmountFor_D", source = "FTaxAmountForD")
    @Mapping(target = "FAllAmountFor_D", source = "FAllAmountForD")
    @Mapping(target = "FIsFree", source = "FIsFree")
    @Mapping(target = "f_QZOK_BDDJHID", source = "FQzokBddjhId")
    @Mapping(target = "FSourceType", source = "FSourceType")
    @Mapping(target = "FMaterialId.FNumber", source = "FMaterialId")
    KingDeeReceiveCommonCommand.FEntityDetail toCommand(KingDeeReceiveCommonDetailDto dto);

    @Mapping(target = "FLinkId", source = "FLinkId")
    @Mapping(target = "FEntityDetail_link_fruleId", source = "FEntityDetailLinkFruleId")
    @Mapping(target = "FEntityDetail_link_fflowlineId", source = "FEntityDetailLinkFflowlineId")
    @Mapping(target = "FEntityDetail_link_fstableId", source = "FEntityDetailLinkFstableId")
    @Mapping(target = "FEntityDetail_link_fstableName", source = "FEntityDetailLinkFstableName")
    @Mapping(target = "FEntityDetail_link_fsbillId", source = "FEntityDetailLinkFsbillId")
    @Mapping(target = "FEntityDetail_link_fsId", source = "FEntityDetailLinkFsId")
    @Mapping(target = "FEntityDetail_link_fbasicunitqtyold", source = "FEntityDetailLinkFbasicunitqtyold")
    @Mapping(target = "FEntityDetail_link_fbasicunitqty", source = "FEntityDetailLinkFbasicunitqty")
    @Mapping(target = "FEntityDetail_link_fsalbaseqtyold", source = "FEntityDetailLinkFsalbaseqtyold")
    @Mapping(target = "FEntityDetail_link_fsalbaseqty", source = "FEntityDetailLinkFsalbaseqty")
    @Mapping(target = "FEntityDetail_link_fstockbaseqtyold", source = "FEntityDetailLinkFstockbaseqtyold")
    @Mapping(target = "FEntityDetail_link_fstockbaseqty", source = "FEntityDetailLinkFstockbaseqty")
    @Mapping(target = "FEntityDetail_link_fallamountfor_Dold", source = "FEntityDetailLinkFallamountforDold")
    @Mapping(target = "FEntityDetail_link_fallamountfor_D", source = "FEntityDetailLinkFallamountforD")
    KingDeeReceiveCommonCommand.FEntityDetail.FEntityDetailLink toCommand(KingDeeReceiveCommonDetailLinkDto dto);

}
