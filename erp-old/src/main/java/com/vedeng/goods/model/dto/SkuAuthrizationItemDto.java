package com.vedeng.goods.model.dto;

/**
 * SKU报备信息明细运输类
 *
 * <AUTHOR>
 * @date 2020/9/24 14:29:18
 */
public class SkuAuthrizationItemDto {
    /**
     * 授权省份ID
     */
    private Integer skuAuthorizationRegionId;

    /**
     * 授权类型ID
     */
    private Integer skuAuthorizationTerminaltypeId;

    /**
     * SKU
     */
    private Integer skuId;

    /**
     * 省份ID
     */
    private Integer regionId;

    /**
     * 授权类型ID
     */
    private Integer terminalTypeId;

    /**
     * 雪花ID
     */
    private Long snowFlakeId;

    /**
     * 是否启用
     */
    private Integer isEnable;

    public Integer getSkuAuthorizationRegionId() {
        return skuAuthorizationRegionId;
    }

    public void setSkuAuthorizationRegionId(Integer skuAuthorizationRegionId) {
        this.skuAuthorizationRegionId = skuAuthorizationRegionId;
    }

    public Integer getSkuAuthorizationTerminaltypeId() {
        return skuAuthorizationTerminaltypeId;
    }

    public void setSkuAuthorizationTerminaltypeId(Integer skuAuthorizationTerminaltypeId) {
        this.skuAuthorizationTerminaltypeId = skuAuthorizationTerminaltypeId;
    }

    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    public Integer getRegionId() {
        return regionId;
    }

    public void setRegionId(Integer regionId) {
        this.regionId = regionId;
    }

    public Integer getTerminalTypeId() {
        return terminalTypeId;
    }

    public void setTerminalTypeId(Integer terminalTypeId) {
        this.terminalTypeId = terminalTypeId;
    }

    public Long getSnowFlakeId() {
        return snowFlakeId;
    }

    public void setSnowFlakeId(Long snowFlakeId) {
        this.snowFlakeId = snowFlakeId;
    }

    public Integer getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(Integer isEnable) {
        this.isEnable = isEnable;
    }
}
