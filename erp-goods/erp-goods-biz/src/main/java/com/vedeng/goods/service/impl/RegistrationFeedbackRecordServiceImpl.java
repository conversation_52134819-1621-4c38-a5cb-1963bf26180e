package com.vedeng.goods.service.impl;

import com.vedeng.goods.domain.dto.FeedbackCountDto;
import com.vedeng.goods.domain.entity.RegistrationFeedbackRecordEntity;
import com.vedeng.goods.dto.RegistrationFeedbackDto;
import com.vedeng.goods.mapper.RegistrationFeedbackRecordMapper;
import com.vedeng.goods.service.RegistrationFeedbackApiService;
import com.vedeng.goods.service.RegistrationFeedbackRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @create 2022−10-31 上午9:16
 * @description 注册证问题反馈
 */
@Service
@Slf4j
public class RegistrationFeedbackRecordServiceImpl implements RegistrationFeedbackRecordService, RegistrationFeedbackApiService {
    @Resource
    RegistrationFeedbackRecordMapper feedbackRecordMapper;

    @Override
    public void addFeedbackRecord(RegistrationFeedbackRecordEntity registrationFeedbackRecord, Integer userId) {
        registrationFeedbackRecord.setState(0)
                .setAddTime(System.currentTimeMillis())
                .setModTime(System.currentTimeMillis())
                .setCreator(userId)
                .setUpdater(userId);
        feedbackRecordMapper.insert(registrationFeedbackRecord);
    }

    @Override
    public Map<String,Object> handleFeedbackNum(Integer registrationNumberId) {
        Map<String,Object> map = new HashMap<>();

        String sb = "已有%d条问题反馈：印章缺失/不清晰(%d),打印内容模糊(%d),水印内容不当(%d),已过期(%d),其他(%d).";
        List<FeedbackCountDto> feedbackCountList = feedbackRecordMapper.feedbackCount(registrationNumberId);

        map.put("flag", !CollectionUtils.isEmpty(feedbackCountList));

        AtomicInteger one = new AtomicInteger();
        AtomicInteger two = new AtomicInteger();
        AtomicInteger three = new AtomicInteger();
        AtomicInteger four = new AtomicInteger();
        AtomicInteger five = new AtomicInteger();
        AtomicInteger total = new AtomicInteger();
        feedbackCountList.forEach(f ->{
            if ("1".equals(f.getProblemType())){
                one.set(f.getCountNum());
            }
            if ("2".equals(f.getProblemType())){
                two.set(f.getCountNum());
            }
            if ("3".equals(f.getProblemType())){
                three.set(f.getCountNum());
            }
            if ("4".equals(f.getProblemType())){
                four.set(f.getCountNum());
            }
            if ("5".equals(f.getProblemType())){
                five.set(f.getCountNum());
            }
            total.getAndAdd(f.getCountNum()) ;
        });
            String format = String.format(sb, total.get(), one.get(), two.get(), three.get(), four.get(), five.get());
            map.put("sb",format);

        return map;
    }

    @Override
    public List<RegistrationFeedbackDto> listByFirstEngageId(Integer firstEngageId) {
        List<RegistrationFeedbackDto> list = feedbackRecordMapper.listByFirstEngageId(firstEngageId);
        return list;
    }
}
