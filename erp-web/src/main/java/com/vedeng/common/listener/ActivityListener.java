package com.vedeng.common.listener;

import com.google.common.eventbus.Subscribe;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.common.core.config.IObserver;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.listenerEvent.*;
import com.vedeng.common.core.listenerEvent.eventresult.AuditRecordEventResult;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.StringUtil;
import com.vedeng.system.service.UserService;
import com.vedeng.system.service.VerifiesRecordService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.task.IdentityLink;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 审核流监听器
 * @date 2023/11/27 11:09
 */
@Component
@Slf4j
public class ActivityListener implements IObserver {

    @Autowired
    private ActionProcdefService actionProcdefService;

    @Autowired
    private ProcessEngine processEngine;

    @Autowired
    private VerifiesRecordService verifiesRecordService;

    @Autowired
    private UserService userService;

    /**
     * 开启审核流
     *
     * @param createActivityEvent
     */
    @Subscribe
    @Transactional(rollbackFor = Throwable.class)
    public void createProcessInstance(CreateActivityEvent createActivityEvent) {
        try {
            actionProcdefService.createProcessInstance(null, createActivityEvent.getProcessDefinitionKey(), createActivityEvent.getBusinessKey(), createActivityEvent.getVariables());
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    /**
     * 根据BusinessKey获取生成的审核实例
     */
    @Subscribe
    @Transactional(rollbackFor = Throwable.class)
    public void getHistoric(HistoricActivityEvent historicActivityEvent) {
        Map<String, Object> historic = actionProcdefService.getHistoric(processEngine, historicActivityEvent.getBusinessKey());
        historicActivityEvent.setHistoricInfo(historic);
    }

    /**
     * 完成审核节点
     */
    @Subscribe
    @Transactional(rollbackFor = Throwable.class)
    public void complementTask(ComplementTaskEvent complementTaskEvent) {
        ResultInfo<?> resultInfo = actionProcdefService.complementTask(complementTaskEvent.getRequest(), complementTaskEvent.getTaskId(), complementTaskEvent.getComment(), complementTaskEvent.getAssignee(), complementTaskEvent.getVariables());
        complementTaskEvent.setData(resultInfo.getData());
    }

    /**
     * 更新T_VERIFIES_INFO信息
     */
    @Subscribe
    @Transactional(rollbackFor = Throwable.class)
    public void saveVerifiesInfo(SaveVerifiesInfoEvent saveVerifiesInfoEvent) {
        verifiesRecordService.saveVerifiesInfo(saveVerifiesInfoEvent.getTaskId(), saveVerifiesInfoEvent.getStatus());
    }

    /**
     * 根据businessKey查询所有的审核记录
     *
     * @param auditRecordEvent businessKey
     */
    @Subscribe
    public void getHistoryAuditRecord(AuditRecordEvent auditRecordEvent) {
        CurrentUser user = CurrentUser.getCurrentUser();
        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, auditRecordEvent.getBusinessKey());
        Map commentMap = (Map) historicInfo.get("commentMap");
        List<HistoricActivityInstance> historicActivityInstance = ((List<HistoricActivityInstance>) historicInfo.get("historicActivityInstance")).stream()
                .filter(v -> StringUtil.isNotBlank(v.getActivityName())).collect(Collectors.toList());
        List<AuditRecordEventResult> auditRecordList = historicActivityInstance.stream().map(item -> {
            AuditRecordEventResult temp = new AuditRecordEventResult();
            temp.setOperator(userService.getRealNameByUserName(item.getAssignee()));
            temp.setOperation(item.getActivityName());
            temp.setOperationTime(item.getEndTime());

            String remark = (String) commentMap.get(item.getTaskId());
            temp.setRemark(remark == null ? "" : remark);
            return temp;
        }).collect(Collectors.toList());
        // 获取当前进行的流程的节点的候选人组
        Task taskInfoPay = (Task) historicInfo.get("taskInfo");
        String verifyUsersPay = null;
        if (taskInfoPay != null) {
            Map candidateUserMap = (Map) historicInfo.get("candidateUserMap");
            //获取审核人候选组
            List<IdentityLink> candidateUserList = (List<IdentityLink>) candidateUserMap.get(taskInfoPay.getId());
            if (CollectionUtils.isNotEmpty(candidateUserList)) {
                List<String> userNameList = candidateUserList.stream().map(IdentityLink::getUserId).collect(Collectors.toList());
                verifyUsersPay = userNameList.stream().map(userName -> userService.getRealNameByUserName(userName)).collect(Collectors.joining(","));
            }
        }
        auditRecordList.get(historicActivityInstance.size() - 1).setOperator(verifyUsersPay);
        auditRecordEvent.setAuditRecordEventResults(auditRecordList);
        auditRecordEvent.setIsAuditUser(verifyUsersPay != null && verifyUsersPay.contains(user.getUsername()));
        auditRecordEvent.setTaskId(historicActivityInstance.get(historicActivityInstance.size() - 1).getTaskId());
    }

    /**
     * 判断当前用户是不是审核人（用于判断是否展示审核按钮）
     *
     * @param checkAuditUserEvent CheckAuditUserEvent
     */
    @Subscribe
    public void checkIsAuditUser(CheckAuditUserEvent checkAuditUserEvent) {
        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, checkAuditUserEvent.getBusinessKey());
        List<HistoricActivityInstance> historicActivityInstance = ((List<HistoricActivityInstance>) historicInfo.get("historicActivityInstance")).stream()
                .filter(v -> StringUtil.isNotBlank(v.getActivityName())).collect(Collectors.toList());
        checkAuditUserEvent.setTaskId(historicActivityInstance.get(historicActivityInstance.size() - 1).getTaskId());
        Map candidateUserMap = (Map) historicInfo.get("candidateUserMap");
        Task taskInfoPay = (Task) historicInfo.get("taskInfo");
        if (Objects.nonNull(candidateUserMap) && Objects.nonNull(taskInfoPay)) {
            CurrentUser user = CurrentUser.getCurrentUser();
            List<IdentityLink> candidateUserList = Optional.ofNullable((List<IdentityLink>) candidateUserMap.get(taskInfoPay.getId())).orElse(new ArrayList<>());
            List<String> userNameList = candidateUserList.stream().map(IdentityLink::getUserId).collect(Collectors.toList());
            checkAuditUserEvent.setIsAuditUser(userNameList.contains(user.getUsername()));
        }
    }

}
