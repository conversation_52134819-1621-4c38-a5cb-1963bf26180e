package com.vedeng.erp.saleorder.service.impl;

import com.vedeng.authorization.model.Region;
import com.vedeng.erp.saleorder.dao.RegionJdMapper;
import com.vedeng.erp.saleorder.model.po.RegionJd;
import com.vedeng.erp.saleorder.model.po.VJdSaleorder;
import com.vedeng.erp.saleorder.service.JdRegionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2022/12/7
 */
@Service
public class JdRegionServiceImpl implements JdRegionService {

    @Resource
    private RegionJdMapper regionJdMapper;
    public static final String SPLIT_CHAR = "-";

    @Override
    public List<VJdSaleorder> selectJdToVdRegion(List<VJdSaleorder> orderList) {
        if(CollectionUtils.isEmpty(orderList)){
            return orderList;
        }
        List<String> inputList = new ArrayList<>();

        for(VJdSaleorder order :orderList){
            StringBuffer sbr = new StringBuffer();
            sbr.append(order.getProvince().trim());
            sbr.append(SPLIT_CHAR);
            sbr.append(order.getCity().trim());
            sbr.append(SPLIT_CHAR);
            sbr.append(order.getCounty().trim());
            inputList.add(sbr.toString());
        }
        //取省市区拼接用in查询得到所有京东地址对应的贝登REGION_ID
        List<RegionJd> jdRegionList = regionJdMapper.selectJdToVdRegion(inputList);
        if(!CollectionUtils.isEmpty(jdRegionList)){
            for(RegionJd jd:jdRegionList){
                orderList.forEach(order->{
                    StringBuffer sbr = new StringBuffer();
                    sbr.append(order.getProvince().trim());
                    sbr.append(SPLIT_CHAR);
                    sbr.append(order.getCity().trim());
                    sbr.append(SPLIT_CHAR);
                    sbr.append(order.getCounty().trim());
                        if(sbr.toString().equals(jd.getJdPCAreaName())){
                            order.setRegionId(jd.getVdRegionId());
                        }
                    }
                );
            }
        }
        return orderList;

    }
}
