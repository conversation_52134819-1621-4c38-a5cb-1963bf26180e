void function () {
    new Vue({
        el: '#page-container',
        data: {
            flowOrderId: '',
            flowOrderNo: '',
            baseOrderId: '',
            baseOrderNo: '',
            isAudit: '',
            supplierData: [],
            nodeInfoDate:[],
            goodsData: [],
            baseBusinessType: 1,
            invoiceTypeList: [],
            payTypeList: [{
                label: '先款后货，预付100%',
                value: 0,
            }, {
                label: '自定义',
                value: 1,
            }],
            totalAddRate: {},
            totalProfitRate: {},
            statusMappings :{
                '付款状态': {0: '未付款', 1: '部分付款', 2: '全部付款'},
                '收款状态': {0: '未收款', 1: '部分收款', 2: '全部收款'},
                '入库状态': {0: '未入库', 1: '部分入库', 2: '全部入库'},
                '出库状态': {0: '未出库', 1: '部分出库', 2: '全部出库'},
                '收票状态': {0: '未收票', 1: '部分收票', 2: '全部收票'},
                '开票状态': {0: '未开票', 1: '部分开票', 2: '全部开票'},
            },
            dialogVisible: false, // 控制 dialog 显示
            currentInvoiceData:[],
            isShowLeft: true,
            // 合同上传相关
            uploadDialogVisible: false, // 控制上传对话框显示
            currentUploadRow: null, // 当前上传的行数据
            uploadUrl: '/flowOrder/uploadContract.do', // 上传接口地址
            uploadData: {}, // 上传参数
            fileList: [], // 文件列表
            uploadProgress: 0, // 上传进度
            uploadStatus: '', // 上传状态
            uploading: false, // 是否正在上传
            sourceErpName: '',
        },
        computed: {
            supplierShowList() {
                debugger
                return JSON.parse(JSON.stringify(this.supplierData)).reverse();
            }
        },
        created() {
            this.flowOrderId = this.getQuery('flowOrderId');
            this.initPageData();
        },
        methods: {
            // 打开 dialog
            openDialog(invoiceData) {
                this.currentInvoiceData = invoiceData || [];
                if (invoiceData && invoiceData.length > 0){
                    this.dialogVisible = true;
                }else {
                    // this.$message.error('暂无发票文件');
                    this.$alert('暂无发票文件', '', {
                        confirmButtonText: '我知道了'
                    });
                }
            },
            // 关闭 dialog
            handleClose(done) {
                this.dialogVisible = false;
                done();
            },
            // 在新标签页打开发票文件
            openInvoice(url) {
                if (url){
                    window.open(url, "_blank");
                }else{
                    this.$alert('暂无发票文件', '', {
                        confirmButtonText: '我知道了'
                    });
                }
            },
            // 在新标签页打开合同文件
            openContract(url) {
                if (url){
                    window.open(url, "_blank");
                }else{
                    this.$alert('暂无合同文件', '', {
                        confirmButtonText: '我知道了'
                    });
                }
            },
            // 显示上传对话框
            showUploadDialog(row) {
                if (row.contractUrl) {
                    this.$confirm('当前已有合同文件，确定要替换现有合同吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.openUploadDialog(row);
                    });
                } else {
                    this.openUploadDialog(row);
                }
            },

            // 打开上传对话框
            openUploadDialog(row) {
                this.currentUploadRow = row;
                this.uploadDialogVisible = true;
                this.fileList = [];
                this.uploadProgress = 0;
                this.uploadStatus = '';
                this.uploading = false;

                // 设置上传参数 - 采购流转单固定为采购类型
                this.uploadData = {
                    flowOrderId: this.flowOrderId,
                    nodeLevel: row.nodeLevel,
                    flowOrderInfoType: 0 // 采购流转单固定为0:采购
                };
            },

            // 文件选择变化事件
            onChange(file, fileList) {
                // 更新文件列表
                this.fileList = fileList;
                console.log('文件选择变化:', file, fileList);
            },

            // 文件移除事件
            onRemove(file, fileList) {
                // 更新文件列表
                this.fileList = fileList;
                console.log('文件移除:', file, fileList);
            },

            // 上传前验证
            beforeUpload(file) {
                // 文件格式验证
                const isPDF = file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf');
                if (!isPDF) {
                    this.$message.error('只能上传PDF格式的文件！');
                    return false;
                }

                // 文件大小验证（10MB）
                const isLt10M = file.size / 1024 / 1024 < 10;
                if (!isLt10M) {
                    this.$message.error('文件大小不能超过10MB！');
                    return false;
                }

                return true;
            },

            // 确认上传
            confirmUpload() {
                if (this.fileList.length === 0) {
                    this.$message.warning('请选择要上传的文件');
                    return;
                }

                this.uploading = true;
                this.$refs.contractUpload.submit();
            },

            // 上传进度
            onUploadProgress(event, file, fileList) {
                this.uploadProgress = Math.round(event.percent);
                this.uploadStatus = this.uploadProgress === 100 ? 'success' : '';
            },

            // 上传成功
            onUploadSuccess(response, file, fileList) {
                this.uploading = false;
                if (response.success) {
                    this.$message.success('合同上传成功！');
                    this.uploadDialogVisible = false;
                    // 刷新页面数据
                    this.initPageData();
                } else {
                    this.$message.error(response.message || '上传失败');
                    this.uploadStatus = 'exception';
                }
            },

            // 上传失败
            onUploadError(err, file, fileList) {
                this.uploading = false;
                this.uploadStatus = 'exception';
                this.$message.error('上传失败，请重试');
                console.error('上传错误：', err);
            },

            // 关闭上传对话框
            handleUploadClose() {
                this.uploadDialogVisible = false;
                this.currentUploadRow = null;
                this.fileList = [];
                this.uploadProgress = 0;
                this.uploadStatus = '';
                this.uploading = false;
            },
            getQuery(key) {
                let params = new URLSearchParams(window.location.search);
                return params.get(key) || '';
            },
            initPageData() {
                let loading = this.$loading({
                    lock: true,
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.6)',
                    customClass: 'global-loading'
                });

                axios.get('/flowOrder/get.do?flowOrderId=' + this.flowOrderId).then(({ data }) => {
                    loading.close();

                    if (data.code === 0) {
                        this.flowOrderNo = data.data.flowOrderNo;
                        this.baseOrderId = data.data.baseOrderId;
                        this.baseOrderNo = data.data.baseOrderNo;
                        this.isAudit = data.data.auditStatus === 1; // 0:未审核，1：已审核
                        this.sourceErpName = data.data.sourceErpName || '南京贝登医疗股份有限公司';

                        this.supplierData = data.data.flowNodeDto || [];
                        this.supplierData.forEach(item => {
                            item.isInvoice = !!item.openInvoice;
                            item.prevTraderId = item.traderId;
                        })

                        this.goodsData = data.data.flowOrderDetailList || [];

                        this.goodsData.forEach(item => {
                            if(!item.flowNodeOrderDetailPriceDtoList) {
                                item.flowNodeOrderDetailPriceDtoList = [{
                                    flowNodeOrderPriceId: '',
                                    flowOrderDetailId: item.flowOrderDetailId,
                                    nodeLevel: '',
                                    price: undefined,
                                    markupRate: undefined,
                                    grossProfitRate: '',
                                }];
                            }
                        })

                        if(this.isAudit){
                            axios.get('/flowOrder/getBuySaleOrderInfo.do?baseBusinessType=1&flowOrderId=' + this.flowOrderId).then(({ data }) => {
                                if (data.code === 0) {
                                    this.nodeInfoDate = data.data || [];
                                } else {
                                    this.$alert(data.message, '', {
                                        confirmButtonText: '我知道了'
                                    });
                                }
                            });
                        }

                    } else {
                        this.$alert(data.message, '', {
                            confirmButtonText: '我知道了'
                        });
                    }
                })

                axios.post('/flowOrder/getInvoiceType.do').then(({ data }) => {
                    if (data.code === 0) {
                        this.invoiceTypeList = data.data;
                    }
                })
            },

            getPayTypeLabel(invoiceType) {
                var item = this.payTypeList.find(item => item.value === invoiceType);
                return item ? item.label : '-';
            },


            getInvoiceTypeLabel(invoiceType) {
                var item = this.invoiceTypeList.find(item => item.value === invoiceType);
                return item ? item.label : '-';
            },

            // 获取状态描述的函数
            getStatusDescription(statusType, statusCode) {
                if (this.statusMappings[statusType] && this.statusMappings[statusType].hasOwnProperty(statusCode)) {
                    return this.statusMappings[statusType][statusCode];
                } else {
                    return '-';
                }
            },

            handleEdit() {
                // 跳转到编辑页面
                window.location.href = '/flowOrder/editBuyOrder.do' + '?flowOrderId=' + this.flowOrderId;
            },
            handleAudit() {
                // 审核操作
                this.$confirm('确定要审核该单吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    // 调用审核接口
                    axios.get('/flowOrder/audit.do?flowOrderId=' + this.flowOrderId).then(({data}) => {
                        if (data.code === 0) {
                            this.$message({
                                type: 'success',
                                message: '审核成功'
                            });
                        } else {
                            this.$alert(data.message, '', {
                                confirmButtonText: '我知道了'
                            });
                        }
                        // 关闭当前页
                        this.closeThis()
                    })
                }).catch(() => {});
            },
            handleDelete() {
                // 删除操作
                this.$confirm('确定要删除该采购单吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    // 调用删除接口
                    axios.get('/flowOrder/delete.do?flowOrderId=' + this.flowOrderId).then(({data}) => {
                        if (data.code === 0) {
                            this.$message({
                                type: 'success',
                                message: '删除成功'
                            });
                            // 关闭当前页
                            this.closeThis()
                        } else {
                            this.$alert(data.message, '', {
                                confirmButtonText: '我知道了'
                            });
                        }
                    })
                }).catch(() => {});
            },
            getGoodsSummary(params) {
                console.log(params)


                let sums = [];

                params.columns.forEach((item, index) => {
                    if (index == 0) {
                        sums[index] = '合计';
                        return;
                    }

                    if (item.label === '数量') {
                        let sum = 0;
                        params.data.forEach(dataItem => {
                            sum += dataItem.quantity;
                        })
                        sums[index] = sum;
                    } else if (item.label === '贝登发货数量' || item.label === '贝登收货数量') {
                        let sum = 0;
                        params.data.forEach(dataItem => {
                            sum += dataItem.deliveryQuantity;
                        })
                        sums[index] = sum;
                    }
                    // else if (item.label && (item.label.indexOf('采购价') !== -1 || item.label.indexOf('销售价') !== -1)) {
                    //     let sum = 0;
                    //     let hasEmpty = false;
                    //     console.log('columns:', item)
                    //     params.data.forEach(dataItem => {
                    //         let sindex = item.property - 1;
                    //         let price = 0;

                    //         if(this.baseBusinessType == 1) {
                    //             if(dataItem.flowNodeOrderDetailPriceDtoList[this.supplierData.length - 1 - sindex]) {
                    //                 price = dataItem.flowNodeOrderDetailPriceDtoList[this.supplierData.length - 1 - sindex].price;
                    //             }
                    //         } else {
                    //             price = dataItem.flowNodeOrderDetailPriceDtoList[sindex].price;
                    //         }

                    //         if (price && price != 0) {
                    //             console.log(price)
                    //             sum += price;
                    //         } else {
                    //             hasEmpty = true;
                    //         }
                    //     })

                    //     if (hasEmpty) {
                    //         sum = '-'
                    //     } else {
                    //         sum = this.toFixed2(sum);
                    //     }

                    //     sums[index] = sum;
                    // }
                    else if (item.label && (item.label.indexOf('采购金额') !== -1 || item.label.indexOf('销售金额') !== -1)) {
                        let sum = 0;
                        let hasEmpty = false;

                        params.data.forEach(dataItem => {
                            let sindex = item.property - 1;
                            let price = 0;

                            if(this.baseBusinessType == 1) {
                                if(dataItem.flowNodeOrderDetailPriceDtoList[this.supplierData.length - 1 - sindex]) {
                                    price = dataItem.flowNodeOrderDetailPriceDtoList[this.supplierData.length - 1 - sindex].price;
                                }
                            } else {
                                price = dataItem.flowNodeOrderDetailPriceDtoList[sindex].price;
                            }

                            if (price && price != 0) {
                                sum += dataItem.quantity * price;
                            } else {
                                hasEmpty = true;
                            }
                        })

                        if (hasEmpty) {
                            sum = '-'
                        } else {
                            sum = this.toFixed2(sum);
                        }

                        sums[index] = sum;
                    } else if (item.label && item.label.indexOf('加价率') !== -1) {
                        let sum = 0;
                        let hasEmpty = false;
                        let totalPrice = 0;
                        let nextTotalPrice = 0;

                        let sindex = item.property - 1;
                        params.data.forEach(dataItem => {
                            let price = 0;
                            let nextPrice = 0;

                            if(this.baseBusinessType == 1) {
                                if(dataItem.flowNodeOrderDetailPriceDtoList[this.supplierData.length - sindex] && dataItem.flowNodeOrderDetailPriceDtoList[this.supplierData.length - 1 - sindex]) {
                                    price = dataItem.flowNodeOrderDetailPriceDtoList[this.supplierData.length - sindex].price;
                                    nextPrice = dataItem.flowNodeOrderDetailPriceDtoList[this.supplierData.length - 1 - sindex].price;
                                }
                            } else {
                                if(sindex === 0) {
                                    hasEmpty = true;
                                    price = '';
                                } else {
                                    price = dataItem.flowNodeOrderDetailPriceDtoList[sindex - 1].price;
                                    nextPrice = dataItem.flowNodeOrderDetailPriceDtoList[sindex].price;
                                }
                            }

                            if (price && price != 0 && nextPrice && nextPrice != 0) {
                                totalPrice += parseFloat(price) * parseFloat(dataItem.quantity);
                                nextTotalPrice += parseFloat(nextPrice) * parseFloat(dataItem.quantity);
                            } else {
                                hasEmpty = true;
                            }
                        })

                        if (hasEmpty) {
                            sum = '-'
                        } else {
                            if(totalPrice) {
                                sum = this.toFixed2((nextTotalPrice - totalPrice) / totalPrice * 100) + '%';
                            } else {
                                sum = '-';
                            }
                        }

                        this.$set(this.totalAddRate, sindex, sum)
                        sums[index] = sum;
                    } else if (item.label && item.label.indexOf('毛利率') !== -1) {
                        let sindex = item.property - 1;
                        let sum = 0;
                        console.log('maolilv', sindex)
                        if(sindex < 1) {
                            sum = '-'
                        } else {
                            let hasEmpty = false;
                            let totalPrice = 0;
                            let nextTotalPrice = 0;

                            params.data.forEach(dataItem => {
                                price = 0;
                                nextPrice = 0;

                                if(this.baseBusinessType == 1) {
                                    if(dataItem.flowNodeOrderDetailPriceDtoList[this.supplierData.length - sindex] && dataItem.flowNodeOrderDetailPriceDtoList[this.supplierData.length - 1 - sindex]) {
                                        price = dataItem.flowNodeOrderDetailPriceDtoList[this.supplierData.length - sindex].price;
                                        nextPrice = dataItem.flowNodeOrderDetailPriceDtoList[this.supplierData.length - 1 - sindex].price;
                                    }
                                } else {
                                    price = dataItem.flowNodeOrderDetailPriceDtoList[sindex - 1].price;
                                    nextPrice = dataItem.flowNodeOrderDetailPriceDtoList[sindex].price;
                                }

                                if (price) {
                                    totalPrice += parseFloat(price) * parseFloat(dataItem.quantity);
                                    nextTotalPrice += parseFloat(nextPrice) * parseFloat(dataItem.quantity);
                                } else {
                                    hasEmpty = true;
                                }
                            })
                            console.log(hasEmpty, nextTotalPrice)
                            if (hasEmpty) {
                                sum = '-'
                            } else {
                                if(nextTotalPrice) {
                                    sum = this.toFixed2((nextTotalPrice - totalPrice) / nextTotalPrice * 100) + '%';
                                } else {
                                    sum = '-';
                                }
                            }

                        }
                        console.log(sum)
                        this.$set(this.totalProfitRate, sindex, sum)
                        sums[index] = sum;
                    } else {
                        sums[index] = '';
                    }

                })

                return sums;
            },
            gotoOrderDetail(data) {
                let link = ''
                link = `/order/newBuyorder/newViewBuyOrderDetail.do?buyorderId=${data}`;
                this.openLink(link, {
                    name: '采购详情',
                    id: 'buy_order_detail_' + data
                })
            },
            //计算采购金额
            calcGoodsItemLevelTotal(index, item) {
                let price = item.flowNodeOrderDetailPriceDtoList[index].price;
                if(item.quantity && (price || price === 0)) {
                    return this.toFixed2(item.quantity * price);
                } else {
                    return '-'
                }
            },
            toFixed2(num){
                return Math.round(num * 100) / 100;
            },
            openLink(link, params) {
                if (window.parent != window) {
                    if (window.parent.closableTab) {
                        var item = {
                            'id': params.id ? params.id : new Date().getTime(),
                            'name': params.name,
                            'url': link,
                            'closable': params.noclose ? false : true
                        };

                        window.parent.closableTab.addTab(item);
                        window.parent.closableTab.resizeMove();
                    }
                } else {
                    window.open(link);
                }
            },

            closeThis() {
                window.parent.closableTab.close($(window.parent.document).find('[role=presentation].active .glyphicon.small'), window.parent.closableTab.resizeMove);
            }
        }
    })
}.call(this);
