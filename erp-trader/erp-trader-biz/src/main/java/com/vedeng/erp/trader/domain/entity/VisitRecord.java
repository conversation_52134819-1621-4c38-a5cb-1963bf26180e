package com.vedeng.erp.trader.domain.entity;

import lombok.Data;

import java.util.Date;

/**
 * 拜访记录表
 * <AUTHOR>
 */
@Data
public class VisitRecord {
    /**
     * 序号
     */
    private Integer id;

    /**
     * 计划拜访时间
     */
    private Date planVisitDate;

    /**
     * 拜访人
     */
    private Integer visitorId;

    /**
     * 拜访目标 (A新客开发B商机跟进C老客客情维护D签约会员E产品推广，以逗号隔开)
     */
    private String visitTarget;

    /**
     * 客户所在地区-省CODE
     */
    private Integer provinceCode;

    /**
     * 客户所在地区-省名称
     */
    private String provinceName;

    /**
     * 客户所在地区-市CODE
     */
    private Integer cityCode;

    /**
     * 客户所在地区-市名称
     */
    private String cityName;

    /**
     * 客户所在地区-区CODE-20240407 ERP_SV_2024_17版本 拜访计划优化需求新增
     */
    private Integer areaCode;

    /**
     * 客户所在地区-区名称-20240407 ERP_SV_2024_17版本 拜访计划优化需求新增
     */
    private String areaName;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户来源方式 (1erp2终端库3天眼查)
     */
    private Integer customerFrom;

    /**
     * 客户类型 (ERP客户时自动带入且不能修改，其他必填。465分销466终端)
     */
    private Integer customerNature;

    /**
     * 交易者ID
     */
    private Integer traderId;

    /**
     * 客户ID
     */
    private Integer traderCustomerId;

    /**
     * 实际拜访时间
     */
    private Date actualVisitDate;

    /**
     * 是否打卡 (Y是N否)
     */
    private String cardOff;

    /**
     * 打卡照片
     */
    private String pictureList;

    /**
     * 拜访内容-联系人信息-姓名
     */
    private String contactName;

    /**
     * 拜访内容-联系人信息-手机
     */
    private String contactMobile;

    /**
     * 拜访内容-联系人信息-电话
     */
    private String contactTele;

    /**
     * 拜访内容-联系人信息-职位
     */
    private String contactPosition;

    /**
     * 沟通事项-讲解PPT (默认否，Y是N否)
     */
    private String showPpt;

    /**
     * 沟通事项-邀请客户注册 (默认否，Y是N否)
     */
    private String inviteReg;

    /**
     * 沟通事项-邀请客户注册-手机号
     */
    private String regMobile;

    /**
     * 沟通事项-邀请客户注册-联系人ID
     */
    private Integer traderContractId;

    /**
     * 沟通事项-沟通情况
     */
    private String commucateContent;

    /**
     * 沟通事项-预计下次拜访时间
     */
    private Date nextVisitDate;

    /**
     * 沟通事项-是否产生商机 (Y是N否)
     */
    private String createBusinessChange;

    /**
     * 拜访结果，拜访成功Y拜访事项缺失N
     */
    private String visitSuccess;

    /**
     * 添加时间
     */
    private Date addTime;

    /**
     * 添加人
     */
    private Integer addUserId;

    /**
     * 更新时间
     */
    private Date modTime;

    /**
     * 更新人
     */
    private Integer modUserId;

    /**
     * 是否删除0否1是
     */
    private Integer isDelete;

    /**
     * 商机表ID
     */
    private Integer bussinessChanceId;

    /**
     * 商机编号
     */
    private String bussinessChanceNo;

    /**
     * 拜访人用户名
     */
    private String visitorName;

    /**
     * 打卡时间
     */
    private String cardTime;


}