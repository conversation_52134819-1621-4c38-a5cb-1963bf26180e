<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>(订单流)采购订单售后列表</title> 
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="uwTrWkUDzfA" datasource="erp-reportdatasource" fixednumber="2" append_column_url="" append_row_url="" empty_show="" firstcol="numbers">
  <div id="appendHead"></div> 
  <div class="layui-fluid"> 
   <div class="layui-tab">
       <ul class="layui-tab-title" id="tab">
           <li class="layui-this"><a href="/ezadmin/list/list-uwTrWkUDzfA">采购售后单</a></li>
           <li><a href="/ezadmin/list/list-purchaseexpenseaftersales">费用售后单</a></li>
       </ul>
   </div> 
   <form class="layui-form" id="search">

       <div class=" layui-inline ">
           <label class="layui-form-label">售后单号</label>
           <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="AFTER_SALES_NO" placeholder="请输入" style="" alias="a" jdbctype="" data="" datatype="" oper="LIKE"></object>
           </div>
       </div>
       <div class=" layui-inline "><label class="layui-form-label">采购单号</label>
           <div class="layui-input-inline">
               <object class=" layui-input list-search-item " type="19" name="ORDER_NO" placeholder="请输入" style="" alias="a" jdbctype="" data="" datatype="" oper="LIKE"></object>
           </div>
       </div>
       <div class=" layui-inline ">
           <label class="layui-form-label">审核状态</label>
           <div class="layui-input-inline"><object class=" layui-input list-search-item " type="21" name="VERIFY_STATUS" placeholder="" style="" alias="COALESCE ( v.STATUS, 3 )" jdbctype="" data="[{&quot;V&quot;:&quot;待审核&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;1&quot;}]" datatype="JSON" oper=""></object>
           </div>
       </div>
       <div class=" layui-inline "><label class="layui-form-label">售后状态</label>
           <div class="layui-input-inline">
               <object class=" layui-input list-search-item " type="21" name="ATFER_SALES_STATUS" placeholder="" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;待确认&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;进行中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;已完结&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;已关闭&quot;,&quot;K&quot;:&quot;3&quot;}]" datatype="JSON" oper=""></object>
           </div>
       </div>
       <div class=" layui-inline ">
           <label class="layui-form-label">售后类型</label>
           <div class="layui-input-inline"><object class=" layui-input list-search-item " type="21" name="TYPE" placeholder="" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;采购订单退货&quot;,&quot;K&quot;:&quot;546&quot;},{&quot;V&quot;:&quot;采购订单换货&quot;,&quot;K&quot;:&quot;547&quot;},{&quot;V&quot;:&quot;采购订单退票&quot;,&quot;K&quot;:&quot;548&quot;},{&quot;V&quot;:&quot;采购订单退款&quot;,&quot;K&quot;:&quot;549&quot;}]" datatype="JSON" oper=""></object>
           </div>
       </div>
       <div class=" layui-inline "><label class="layui-form-label">供应商名称</label>
           <div class="layui-input-inline">
               <object class=" layui-input list-search-item " type="19" name="TRADER_NAME" placeholder="请输入" style="" alias="b" jdbctype="" data="" datatype="" oper="LIKE"></object>
           </div>
       </div>
       <div class=" layui-inline ">
           <label class="layui-form-label">创建方式</label>
           <div class="layui-input-inline"><object class=" layui-input list-search-item " type="20" name="CREATE_TYPE" placeholder="" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;手动&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;自动&quot;,&quot;K&quot;:&quot;1&quot;}]" datatype="JSON" oper=""></object>
           </div>
       </div>
       <div class=" layui-inline "><label class="layui-form-label">创建人</label>
           <div class="layui-input-inline">
               <object class=" layui-input list-search-item " type="20" name="CREATOR" placeholder="" style="" alias="a" jdbctype="" data="SELECT DISTINCT a.CREATOR K, lower(u.USERNAME) V
FROM T_AFTER_SALES a
         LEFT JOIN T_USER u ON a.CREATOR = u.USER_ID
WHERE a.TYPE IN (546, 547, 548, 549)
  AND a.COMPANY_ID = 1 order by u.USERNAME" datatype="KVSQLCACHE" oper=""></object>
           </div>
       </div>
    <div class=" layui-inline "><label class="layui-form-label">生效时间</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="daterange" name="VALIDTIMESTR" placeholder="" style="" alias="FROM_UNIXTIME(IF(a.VALID_TIME= 0, NULL, a.VALID_TIME) / 1000, '%Y-%m-%d %H:%i:%s')" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">创建时间</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="daterange" name="ADDTIMESTR" placeholder="" style="" alias="FROM_UNIXTIME(IF(a.ADD_TIME= 0, NULL, a.ADD_TIME) / 1000, '%Y-%m-%d %H:%i:%s')" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
     </div>
    </div>
   </form>
   <hr class="layui-border-blue"> 
   <div class="btn-group   bd-highlight" id="tableButton"> 
   </div> 
   <table id="table" class="layui-table" style=" width:100%"> 
    <thead> 
     <tr id="column">
      <th type="rowbutton" id="rowbutton"></th>
      <th item_name="AFTER_SALES_NO" name="AFTER_SALES_NO" body="td-link-color" order="1" url="/order/newBuyorder/viewAfterSalesDetail.do?traderType=2&amp;afterSalesId=${AFTER_SALES_ID}" opentype="PARENT" windowname="${AFTER_SALES_NO}" datatype="" data="" style="" head="18">售后单号</th>
      <th item_name="TRADER_NAME" name="TRADER_NAME" body="td-link" order="1" url="/trader/supplier/baseinfo.do?traderId=${TRADER_ID}" opentype="PARENT" windowname="" datatype="" data="" style="" head="18">供应商名称</th>
      <th item_name="ORDER_NO" name="ORDER_NO" body="td-link" order="1" url="/order/newBuyorder/newViewBuyOrderDetail.do?buyorderId=${BUYORDER_ID}" opentype="PARENT" windowname="" datatype="" data="" style="" head="18">采购单号</th>
      <th item_name="TYPE" name="TYPE" body="td-select" order="1" url="" opentype="MODEL" windowname="" datatype="KVSQLCACHE" data="select
   SYS_OPTION_DEFINITION_ID K,TITLE V
    from T_SYS_OPTION_DEFINITION
    where STATUS = 1 AND PARENT_ID = 536" style="" head="18">售后类型</th>
      <th item_name="ATFER_SALES_STATUS" name="ATFER_SALES_STATUS" body="td-select" order="1" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;已关闭&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;待确认&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;已完结&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;进行中&quot;,&quot;K&quot;:&quot;1&quot;}]" style="" head="18">售后状态</th>
      <th item_name="VERIFY_STATUS" name="VERIFY_STATUS" body="td-select" order="1" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;待审核&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;1&quot;}]" style="" head="18">审核状态</th>
      <th item_name="CREATOR" name="CREATOR" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="KVSQLCACHE" data="select USER_ID K,USERNAME V FROM T_USER" style="" head="18">创建人</th>
      <th item_name="ADD_TIME" name="ADD_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">创建时间</th>
      <th item_name="VALID_TIME" name="VALID_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">生效时间</th>
     </tr> 
    </thead> 
    <tbody> 
     <tr> 
      <td colspan="100"> express: <pre id="express" class="layui-code" orderby="order by a.ADD_TIME DESC" groupby="">SELECT
        CASE	
	WHEN
		a.ATFER_SALES_STATUS= 0 THEN
			'orangecircle' 
			WHEN a.ATFER_SALES_STATUS= 1   THEN
			'greencircle' 
			WHEN a.ATFER_SALES_STATUS= 2 THEN
			'bluecircle' 
			WHEN a.ATFER_SALES_STATUS= 3 THEN
			'greycircle' 
		END COLOR_CIRCLE,
	a.AFTER_SALES_ID,
        b.BUYORDER_ID,
	a.AFTER_SALES_NO,
	a.SUBJECT_TYPE,
	a.TYPE,
        a.CREATE_TYPE,
	a.ORDER_ID,
	a.ORDER_NO,
	a.SERVICE_USER_ID,
	a.VALID_STATUS,
	FROM_UNIXTIME(IF(a.VALID_TIME= 0, NULL, a.VALID_TIME) / 1000, '%Y-%m-%d %H:%i:%s') AS  VALID_TIME,
	a.STATUS,
	a.ATFER_SALES_STATUS*1 ATFER_SALES_STATUS,
	FROM_UNIXTIME(IF(a.ADD_TIME= 0, NULL, a.ADD_TIME) / 1000, '%Y-%m-%d %H:%i:%s') AS  ADD_TIME,
	a.CREATOR,
	a.MOD_TIME,
	a.UPDATER,
	s.TITLE AS TYPE_NAME,
	v.VERIFIES_TYPE,
        CONCAT(COALESCE ( v.STATUS, 0 ),',',v.VERIFY_USERNAME ) VERIFY_USERNAME,
	COALESCE ( v.STATUS, 3 )  AS VERIFY_STATUS,
	b.TRADER_ID,
	b.TRADER_NAME 
FROM
	T_AFTER_SALES a
	LEFT JOIN T_SYS_OPTION_DEFINITION s ON s.SYS_OPTION_DEFINITION_ID = a.TYPE
	LEFT JOIN T_VERIFIES_INFO v ON a.AFTER_SALES_ID = v.RELATE_TABLE_KEY 
	AND v.RELATE_TABLE = 'T_AFTER_SALES' 
	AND v.VERIFIES_TYPE = 614
	LEFT JOIN T_BUYORDER b ON a.ORDER_ID = b.BUYORDER_ID 
WHERE
	a.TYPE IN ( 546, 547, 548, 549 ) 
	AND a.COMPANY_ID = 1</pre> count: <pre id="count" class="layui-code"></pre></td> 
     </tr> 
    </tbody> 
   </table> 
  </div> 
  <div id="appendFoot">
   <script>

 $("input[name='row_data_hidden_VERIFY_USERNAME']").each(function(e){
        var curUser = $("#EZ_SESSION_USER_NAME_KEY").val();
        var verifyUser = $(this).val();
        debugger
        var varifyUserChar = verifyUser.split(",");
        var flag = false;
        if (varifyUserChar[0] == '0'){
            for (var i = 1; i < varifyUserChar.length; i++) {
                if (varifyUserChar[i] == curUser) {
                    flag = true;
                    break;
                }
            }
        }
        if (flag){
            var shen = "<font color='red'>[审]</font>";
            $(this).parent().next().children("a").after(shen);
        }
    })
</script>
  </div> 
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script> 
  <script>
    layui.use(function () {

    })
</script> 
 </body>
</html>