<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.crm.visitrecord.mapper.VisitRecordCardMapper">

    <select id="selectLatestCardByRecordId" resultType="com.vedeng.crm.visitrecord.domain.vo.VisitRecordCardVo">
        SELECT
            c.ID as id,
            c.RECORD_ID as recordId,
            c.VISIT_USER_ID as visitUserId,
            u.USERNAME as userName,
            DETAIL.ALIAS_HEAD_PICTURE AS aliasHeadPicture,
            c.CARD_PHOTO_URLS as cardPhotoUrls,
            c.CARD_LOCATION as cardLocation,
            c.CARD_TIME as cardTime,
            c.ADD_TIME as addTime,
            c.ADD_USER_ID as addUserId,
            c.MOD_TIME as modTime,
            c.MOD_USER_ID as modUserId
        FROM
            T_VISIT_RECORD_CARD c
        INNER JOIN (
            SELECT
                VISIT_USER_ID,
                MAX(CARD_TIME) as latest_card_time
            FROM
            T_VISIT_RECORD_CARD
            WHERE
                RECORD_ID = #{recordId}
            GROUP BY
                VISIT_USER_ID
        ) latest ON c.VISIT_USER_ID = latest.VISIT_USER_ID
            AND c.CARD_TIME = latest.latest_card_time
        LEFT JOIN T_USER u ON c.VISIT_USER_ID = u.USER_ID
        LEFT JOIN T_USER_DETAIL DETAIL ON DETAIL.USER_ID = c.VISIT_USER_ID
        WHERE
            c.RECORD_ID = #{recordId}
        ORDER BY
          c.CARD_TIME DESC
    </select>

    <resultMap id="BaseResultMap" type="com.vedeng.crm.visitrecord.domain.vo.VisitRecordCardVo">
        <id column="ID" property="id" jdbcType="INTEGER"/>
        <result column="RECORD_ID" property="recordId" jdbcType="INTEGER"/>
        <result column="VISIT_USER_ID" property="visitUserId" jdbcType="INTEGER"/>
        <result column="CARD_PHOTO_URLS" property="cardPhotoUrls" jdbcType="VARCHAR"/>
        <result column="CARD_LOCATION" property="cardLocation" jdbcType="VARCHAR"/>
        <result column="CARD_TIME" property="cardTime" jdbcType="TIMESTAMP"/>
        <result column="ADD_TIME" property="addTime" jdbcType="TIMESTAMP"/>
        <result column="ADD_USER_ID" property="addUserId" jdbcType="INTEGER"/>
        <result column="MOD_TIME" property="modTime" jdbcType="TIMESTAMP"/>
        <result column="MOD_USER_ID" property="modUserId" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID, RECORD_ID, VISIT_USER_ID, CARD_PHOTO_URLS, CARD_LOCATION, CARD_TIME,
        ADD_TIME, ADD_USER_ID, MOD_TIME, MOD_USER_ID
    </sql>

    <insert id="insert" parameterType="com.vedeng.crm.visitrecord.domain.vo.VisitRecordCardVo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO T_VISIT_RECORD_CARD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="recordId != null">
                RECORD_ID,
            </if>
            <if test="visitUserId != null">
                VISIT_USER_ID,
            </if>
            <if test="cardPhotoUrls != null">
                CARD_PHOTO_URLS,
            </if>
            <if test="cardLocation != null">
                CARD_LOCATION,
            </if>
            <if test="cardTime != null">
                CARD_TIME,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="addUserId != null">
                ADD_USER_ID,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="modUserId != null">
                MOD_USER_ID,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="recordId != null">
                #{recordId,jdbcType=INTEGER},
            </if>
            <if test="visitUserId != null">
                #{visitUserId,jdbcType=INTEGER},
            </if>
            <if test="cardPhotoUrls != null">
                #{cardPhotoUrls,jdbcType=VARCHAR},
            </if>
            <if test="cardLocation != null">
                #{cardLocation,jdbcType=VARCHAR},
            </if>
            <if test="cardTime != null">
                #{cardTime,jdbcType=TIMESTAMP},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="addUserId != null">
                #{addUserId,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modUserId != null">
                #{modUserId,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>


    <select id="selectLastestCheckInTime" resultType="java.lang.String">
        SELECT
            DATE_FORMAT(CARD_TIME, '%Y-%m-%d %H:%i:%s') AS CARD_TIME
        FROM
            T_VISIT_RECORD_CARD
        WHERE
            RECORD_ID = #{recordId,jdbcType=INTEGER}
            AND VISIT_USER_ID = #{userId,jdbcType=INTEGER}
        ORDER BY
            CARD_TIME DESC
        LIMIT 1
    </select>

</mapper> 