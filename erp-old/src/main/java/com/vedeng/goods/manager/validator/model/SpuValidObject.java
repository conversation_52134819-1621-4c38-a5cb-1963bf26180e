package com.vedeng.goods.manager.validator.model;

import lombok.Data;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Data
public class SpuValidObject extends GoodsValidObject {

    private Integer spuId;

    /**
     * 分类
     */
    private Integer categoryId;

    /**
     * 品牌
     */
    private Integer brandId;

    /**
     * SPU名称
     */
    private String showName;

    /**
     * 商品类型
     */
    private Integer spuType;

    private String spuName;

    private String specsModel;

    /**
     * 是否有注册证
     */
    private Boolean hasRegistrationCert;

    /**
     * 是否在《医疗器械目录》
     */
    private Integer medicalInstrumentCatalogIncluded;

    private Integer storageConditionTemperature;

    private Float storageConditionTemperatureLowerValue;

    private Float storageConditionTemperatureUpperValue;

    private Float storageConditionHumidityLowerValue;

    private Float storageConditionHumidityUpperValue;

    private Integer[] storageConditionOthersArray;

    /**
     * 属性Ids
     */
    private Integer[] baseAttributeIds;

    private Integer assignmentManagerId;

    private Integer assignmentAssistantId;


    @Override
    public Integer getId() {
        return getSpuId();
    }

    @Override
    public String getName() {
        return showName;
    }

    @Override
    public void setName(String name) {
        showName = name;
    }

}
