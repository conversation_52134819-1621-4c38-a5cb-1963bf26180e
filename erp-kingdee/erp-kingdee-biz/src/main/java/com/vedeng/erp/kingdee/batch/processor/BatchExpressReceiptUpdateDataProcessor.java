package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.kingdee.batch.dto.BatchExpressReceiptBasicDataDto;
import com.vedeng.erp.kingdee.dto.KingDeeExpressReceiptDto;
import com.vedeng.erp.kingdee.service.KingDeeExpressReceiptApiService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 快递签收新增数据 处理类
 * @date 2023/04/14 15:00
 */

@Service
@Slf4j
public class BatchExpressReceiptUpdateDataProcessor implements ItemProcessor<BatchExpressReceiptBasicDataDto, KingDeeExpressReceiptDto> {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private KingDeeExpressReceiptApiService kingDeeExpressReceiptApiService;


    @Override
    public KingDeeExpressReceiptDto process(BatchExpressReceiptBasicDataDto dto) throws Exception {

        log.info("BatchExpressReceiptUpdateDataProcessor process dto:{}", JSON.toJSONString(dto));

        log.info("查询修改快递签收数据是否已推送到金蝶,FQzokBddjbh:{}", dto.getUniqueKey());
        KingDeeExpressReceiptDto queryDto = KingDeeExpressReceiptDto.builder().FQzokBddjbh(dto.getUniqueKey()).build();
        if (!kingDeeBaseApi.isExist(queryDto)) {
            log.info("修改快递签收数据未推送到金蝶:{}", JSON.toJSONString(dto));
            return null;
        }

        kingDeeExpressReceiptApiService.query(queryDto);
        String fid = queryDto.getFid();
        if (StringUtils.isBlank(fid)) {
            log.info("根据uniqueKey未查询到金蝶数据的fid:{}", JSON.toJSONString(dto));
            throw new ServiceException("根据uniqueKey未查询到金蝶数据的fid");
        }

        if (KingDeeConstant.TWO.equals(dto.getArrivalActFlag())) {
            return KingDeeExpressReceiptDto.builder()
                    .fid(fid)
                    .FQzokOrgid(KingDeeConstant.ORG_ID.toString())
                    .FQzokYsddh(dto.getSaleorderNo())
                    .FQzokGsywdh(dto.getSaleorderNo())
                    .FQzokCrkdh(dto.getOutInNo())
                    .FQzokKdh(dto.getLogisticsNo())
                    .FQzokYwlx("销售订单")
                    .FQzokQssj(DateUtil.formatDate(dto.getArrivalTime()))
                    .FQzokWlgs(dto.getLogistics())
                    .FQzokWlbm(dto.getSku())
                    .FQzokXlh(dto.getBarcodeFactory())
                    .FQzokPch(dto.getBatchNumber())
                    .FQzokFhsl(Objects.isNull(dto.getNum()) ? BigDecimal.ZERO : new BigDecimal(dto.getNum().toString()))
                    .FQzokSjr(dto.getTakeTraderContactName())
                    .FQzokDh(dto.getTakeTraderContactTelephone())
                    .FQzokDz(dto.getTakeTraderAddress())
                    .FQzokBddjbh(dto.getUniqueKey())
                    .FQzokSfsc(Boolean.FALSE.toString())
                    .FQzokSfjrcb(Objects.nonNull(dto.getIsExpense()) && KingDeeConstant.ONE.equals(dto.getIsExpense()) ? "N" : "Y")
                    .FQzokSfzp(Objects.nonNull(dto.getIsGift()) && KingDeeConstant.ONE.equals(dto.getIsGift()) ? 1 : 0)
                    .build();
        }

        if (KingDeeConstant.FOUR.equals(dto.getArrivalActFlag())) {
            return KingDeeExpressReceiptDto.builder()
                    .fid(fid)
                    .FQzokBddjbh(dto.getUniqueKey())
                    .FQzokSfsc(Boolean.TRUE.toString())
                    .FQzokSfzp(Objects.nonNull(dto.getIsGift()) && KingDeeConstant.ONE.equals(dto.getIsGift()) ? 1 : 0)
                    .build();
        }

        return null;
    }

}
