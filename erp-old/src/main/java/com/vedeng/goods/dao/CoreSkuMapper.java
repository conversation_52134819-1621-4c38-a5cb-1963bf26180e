package com.vedeng.goods.dao;

import com.newtask.model.SkuSaleNum;
import com.vedeng.firstengage.model.vo.RegisterSkuVo;
import com.vedeng.flash.dto.temp.CalculatedSaleSumTemp;
import com.vedeng.flash.dto.temp.JoinOrderingPoolOperateTemp;
import com.vedeng.flash.dto.temp.OrderingPoolSortTemp;
import com.vedeng.goods.model.CoreSku;
import com.vedeng.goods.model.CoreSpu;
import com.vedeng.goods.model.dto.*;
import com.vedeng.goods.model.dto.SyncSkuInfo2EsDto;
import com.vedeng.goods.model.vo.GoodsVo;
import com.vedeng.order.model.dto.ProductManageAndAsistDto;
import com.vedeng.order.model.vo.SkuDto;
import com.vedeng.system.model.SysOptionDefinition;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface CoreSkuMapper {
    int deleteByPrimaryKey(Integer skuId);

    int insert(CoreSku record);

    int insertSelective(CoreSku record);

    CoreSku selectByPrimaryKey(Integer skuId);

    int updateByPrimaryKeySelective(CoreSku record);

    @Deprecated
    int updateByPrimaryKey(CoreSku record);

    void updatePriceOfSku(CoreSku coreSku);

    List<CoreSku> getSkuListOfUninitPriceAndStock(@Param("startId") Integer startId, @Param("restart") Integer restart, @Param("limit") Integer limit);

    void initOneYearSaleNum();

    void updateOneYearSaleNum(SkuSaleNum sku);

    List<RegisterSkuVo.Sku> getSkuListByNoList(List<String> noList);

    CoreSku getSkuInfoByNo(String skuNo);

    List<CoreSku> getSkuBySpuId(Integer spuId);

    List<CoreSku> getSkuByFirstEnageId(Integer firstEnageId);

    List<CoreSkuDto> batchFindBySkuNos(List<String> skuNos);

    List<ProductManageAndAsistDto> batchQueryProductManageAndAsist(List<String> skuNos);

    ProductManageAndAsistDto queryProductManageAndAsist(String sku);

    List<CoreSku> getSkuList();

    List<CalculatedSaleSumTemp> getSkuListOrderBySaleSumDesc(@Param("goodType") SysOptionDefinition goodType,@Param("timeFrame") Integer timeFrame);

    BigDecimal getSkuSum80Percent(@Param("goodType") SysOptionDefinition goodType,@Param("timeFrame") Integer timeFrame);

    void update80PercentFlagBySkuIdList(@Param("toChangeSkuIdSortList") List<OrderingPoolSortTemp> toChangeSkuIdSortList, @Param("timeFrame") Integer timeFrame);

    void update80PercentFlag();

    CoreSku selectBySkuNo(String skuNo);

    CoreSku getSkuinfoById(Integer logBizId);

    List<JoinOrderingPoolOperateTemp> selectBySkuNoList(List<JoinOrderingPoolOperateTemp> joinOrderingPoolOperateTemps);

    List<CoreSku> selectSkuBySkuNoList(@Param("skuNoList")List<String> skuNoList);

    List<String> selectNotDeleteBySkuNoList(@Param("skuNoList")List<String> skuNoList);

    List<CoreSku> listSku();

    SkuDto getSkuInfoById(@Param("skuId") Integer skuId);

    List<Integer> getGoodsIdBySkuNo(@Param("skuNo") String skuNo);

    List<CoreSku> getValidingSkuInfoListByNo(@Param("skuNos") List<String> skuNos);

    List<String> getSkuNoBySpuId(@Param("spuId") Integer spuId);

    List<GoodsVo> queryGoodsNewListPage(Map<String, Object> map);

    void updateSpuTypeBySpuId(@Param("coreSpuList") List<CoreSpu> coreSpuList, @Param("nowDate") String nowDate);

    /**
     * 获取产品信息
     * @param sku
     * @return
     */
    GoodsVo queryGoodsBySku(String sku);


    /**
     * 分页获取sku集合（排除特殊商品）
     * @param skuId sku
     * @return sku集合
     */
    List<Integer> getSkuIdListExceptSpecial(@Param("skuId") Integer skuId, @Param("specialSkuIdList") List<Integer> specialSkuIdList,
                                            @Param("startSkuId") Integer startSkuId, @Param("limit") Integer limit);

    List<CoreSku> getAllVirtureSkuList();

    /**
     * 获取费用类别，是否可库存管理
     * @param skuNo
     * @return
     */
    Map<String, Object> getFeeCategory(@Param("skuNo") String skuNo);


    List<CoreSku> getInvisibleSkuList();

    String getTaxCategoryNo(Integer goodsId);

    List<CoreSku> searchSku(@Param("keyword")String keyword);
}