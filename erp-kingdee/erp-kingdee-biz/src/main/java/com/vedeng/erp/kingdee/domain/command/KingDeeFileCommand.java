package com.vedeng.erp.kingdee.domain.command;

import lombok.Data;

/**
 * <AUTHOR>
 * 金蝶附件command
 */
@Data
public class KingDeeFileCommand {

    private String FormId;
    /**
     * 文件名
     */
    private String fileName;

    /**
     * 是否最后一次上传
     */
    private Boolean isLast;

    /**
     * 单据内码，对应金蝶付款单ID
     */
    private String interId;
    /**
     * 单据体标识
     */
    private String entryKey;
    /**
     * 分录内码
     */
    private String entryInterId;
    /**
     * 单据编号
     */
    private String billNo;
    /**
     * 附件别名
     */
    private String aliasFileName;
    /**
     * 多次上传文件需填
     */
    private String fileId;
    /**
     * Base64后的文件字节流
     */
    private String sendByte;

    public KingDeeFileCommand() {
        this.isLast = true;
        this.entryKey = "";
        this.entryInterId = "-1";
        this.fileId = "";
    }
}
