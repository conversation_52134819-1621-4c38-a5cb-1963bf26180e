package com.newtask.data.saleorderdata;

import com.newtask.data.AbstractDataSync;
import com.newtask.data.dao.SaleorderDataMapper;
import com.report.dao.SaleorderDataSelectMapper;
import com.newtask.data.dto.SaleoderDataDto;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/26 10:00
 * @describe
 */
@Component
public class SaleorderCommunicateNumSync extends AbstractDataSync {

    @Resource
    SaleorderDataMapper saleorderDataMapper;
    @Resource
    SaleorderDataSelectMapper saleorderDataSelectMapper;
    @Override
    public List<Integer> loadBizId(Long startTime, Long endTime) {
        List<Integer> communicateNumIdsList = saleorderDataSelectMapper.SaleorderCommunicateNumIdsList(startTime, endTime);

        return communicateNumIdsList;
    }

    @Override
    public List<Map<String, Object>> loadBizData(List<Integer> bizIds) {
        List<Map<String, Object>> communicateNum = null;

        if (CollectionUtils.isNotEmpty(bizIds)) {
            communicateNum = saleorderDataSelectMapper.findCommunicateNumBySaleorderId(bizIds);
        }

        return communicateNum;
    }

    @Override
    public void updateData(List<Map<String, Object>> dataList) {
        if (CollectionUtils.isNotEmpty(dataList)) {
            long timeMillis = System.currentTimeMillis();
            List<SaleoderDataDto> saleoderDataDtos = new ArrayList<>();
            dataList.forEach(item -> {
                Integer saleorderId = NumberUtils.toInt(item.get("saleorderId").toString());

                SaleoderDataDto saleoderDataDto = new SaleoderDataDto();
                saleoderDataDto.setSaleorderId(saleorderId);
                saleoderDataDto.setCommunicateNum(NumberUtils.toLong(item.get("communicateNum")+""));
                saleoderDataDtos.add(saleoderDataDto);
                // 判断saleorderId是否存在
                makeExist(saleorderId, timeMillis);
            });
            // 根据id修改communicateNum
            saleorderDataMapper.updateCommunicateNumCheck(saleoderDataDtos, timeMillis);
        }
    }

    @Override
    public void makeExist(Integer id, Long nowTime) {
        Long count = saleorderDataMapper.getSaleorderById(id);
        if (count <= 0) {
            saleorderDataMapper.insertSaleOrder(id, nowTime);
        }
    }
}
