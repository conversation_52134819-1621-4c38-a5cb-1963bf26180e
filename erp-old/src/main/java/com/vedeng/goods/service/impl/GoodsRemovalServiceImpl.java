package com.vedeng.goods.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.PropertyPreFilter;
import com.alibaba.fastjson.serializer.SimplePropertyPreFilter;
import com.google.common.base.Preconditions;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.DateUtil;
import com.vedeng.goods.dao.*;
import com.vedeng.goods.enums.SKUPushedStatusEnum;
import com.vedeng.goods.model.*;
import com.vedeng.goods.model.dto.AttributeExcelDataDto;
import com.vedeng.goods.model.dto.GoodsRemovalRecordQueryDto;
import com.vedeng.goods.model.dto.SkuExcelDataDto;
import com.vedeng.goods.model.dto.SpuRemovalLogDto;
import com.vedeng.goods.model.vo.*;
import com.vedeng.goods.service.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.shiro.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Service
public class GoodsRemovalServiceImpl implements GoodsRemovalService {

    private final static Logger LOGGER = LoggerFactory.getLogger(GoodsRemovalServiceImpl.class);

    private final static Integer THIRD_CATEGORY_LEVEL = 3;

    private final static int DEFAULT_COUNT = 0;

    private final PropertyPreFilter attributePropertyFilter = new SimplePropertyPreFilter(BaseAttribute.class, "baseAttributeId", "baseAttributeName");

    private final PropertyPreFilter attributeValuePropertyFilter = new SimplePropertyPreFilter(BaseAttributeValue.class, "baseAttributeValueId", "baseAttributeId", "attrValue");

    private final PropertyPreFilter categoryAttributeValueMapingPropertyFilter = new SimplePropertyPreFilter(CategoryAttrValueMapping.class, "categoryAttrValueMappingId", "baseCategoryId", "baseAttributeId", "baseAttributeValueId");

    @Resource
    private SpuMigratedLogMapper spuMigratedLogMapper;
    @Resource
    private BaseCategoryService baseCategoryService;
    @Resource
    private BaseAttributeService baseAttributeService;
    @Resource
    private BaseAttributeValueService baseAttributeValueService;
    @Resource
    private CategoryAttrValueMappingMapper categoryAttrValueMappingMapper;
    @Resource
    private CoreSkuGenerateMapper coreSkuGenerateMapper;
    @Resource
    private CoreSpuGenerateMapper coreSpuGenerateMapper;
    @Resource
    private BaseCategoryMapper baseCategoryMapper;
    @Resource
    private BaseAttributeMapper baseAttributeMapper;
    @Resource
    private VgoodsService vgoodsService;


    @Transactional(readOnly = true, rollbackFor = Exception.class)
    @Override
    public List<GoodsRemovalRecordVo> listGoodsRemovalRecordWithPage(GoodsRemovalRecordQueryDto goodsRemovalRecordQueryDto, Page page) {
        //设置查询条件
        if (StringUtils.isNotEmpty(goodsRemovalRecordQueryDto.getOperateBeginTime())) {
            goodsRemovalRecordQueryDto.setOperateBeginTimeMills(DateUtil.convertLong(goodsRemovalRecordQueryDto.getOperateBeginTime() + " 00:00:00", ""));
        }

        if (StringUtils.isNotEmpty(goodsRemovalRecordQueryDto.getOperateEndTime())) {
            goodsRemovalRecordQueryDto.setOperateEndTimeMills(DateUtil.convertLong(goodsRemovalRecordQueryDto.getOperateEndTime() + " 23:59:59", ""));
        }
        List<SpuRemovalLogDto> spuRemovalLogDtoList = spuMigratedLogMapper.listSpuMigratedlistpage(goodsRemovalRecordQueryDto, page);

        List<GoodsRemovalRecordVo> resultList = new ArrayList<>();
        for (SpuRemovalLogDto spuRemovalLogDto : spuRemovalLogDtoList) {
            resultList.add(convert2GoodsRemovalRecordVo(spuRemovalLogDto));
        }

        return resultList;
    }


    @SuppressWarnings("all")
    @Transactional(readOnly = true, rollbackFor = Exception.class)
    @Override
    public List<SpuRemovalDetailVo> listCategoryAttributeDetailsBeforeChange(Integer targetCategoryId, Integer spuId) {
        Preconditions.checkArgument(Objects.nonNull(targetCategoryId), "商品分类id不能为空");
        Preconditions.checkArgument(spuId != null, "spuId不能为空");

        //获取分类下的属性值及属性值
        List<BaseAttributeVo> targetCategoryAttributeList = vgoodsService.getAttributeInfoByCategoryId(targetCategoryId);
        BaseCategoryVo categoryAttrCondition = new BaseCategoryVo();
        categoryAttrCondition.setBaseCategoryId(targetCategoryId);
        List<BaseAttributeValueVo> categoryAttributeValueList = baseAttributeValueService.getAttrValueListByCategoryId(Collections.singletonList(categoryAttrCondition));

        List<CoreSkuGenerate> ownSkuList = listAllSkuFor(spuId);

        final List<SpuRemovalDetailVo> resultList = new ArrayList<>();
        //获取spu关联属性
        List<? extends BaseAttribute> spuAttrList = baseAttributeService.listSpuOwnAttributeBySpuId(spuId);
        if (CollectionUtils.isNotEmpty(ownSkuList)) {
            List<Integer> skuIdList = ownSkuList.stream().mapToInt(CoreSkuGenerate::getSkuId).boxed().collect(Collectors.toList());

            //获取需要添加的目标需要添加的属性
            final List<BaseAttributeValue> skuAttributeValueList = this.listSkuAttributeValueForSpu(spuAttrList, skuIdList);
            Set<BaseAttributeValue> attributeValueSetToAdd = getAttributeValueSetWithoutCategory(categoryAttributeValueList, skuAttributeValueList);
            Set<BaseAttribute> categoryAttributeToChange = getCategoryAttributeToChange(attributeValueSetToAdd, targetCategoryAttributeList, spuAttrList);

            //获取需要添加的目标需要添加的属性
            categoryAttributeToChange.forEach(attr -> {
                int attrValueCount = 0;
                SpuRemovalDetailVo spuRemovalDetailVo = new SpuRemovalDetailVo();

                //过滤属性
                Set<BaseAttributeValue> currentAttrValuesToAdd = filterAttributeValueFrom(attributeValueSetToAdd, attr.getBaseAttributeId());
                if (CollectionUtils.isNotEmpty(currentAttrValuesToAdd)) {
                    StringJoiner joiner = new StringJoiner(ErpConst.Symbol.COMMA);
                    for (BaseAttributeValue baseAttributeValue : currentAttrValuesToAdd) {
                        attrValueCount++;
                        joiner.add(baseAttributeValue.getAttrValue());
                    }
                    spuRemovalDetailVo.setOwnAttributeValueNames(joiner.toString());
                }

                spuRemovalDetailVo.setAttributeId(attr.getBaseAttributeId());
                spuRemovalDetailVo.setAttrValueCount(attrValueCount);
                spuRemovalDetailVo.setAttributeName(attr.getBaseAttributeName());
                resultList.add(spuRemovalDetailVo);
            });
        }

        return resultList;
    }

    @Override
    public List<SpuRemovalDetailVo> listCategoryAttributeDetailsAfterChange(Integer targetCategoryId, Integer spuRemovalLogId) {
        final List<SpuRemovalDetailVo> resultList = new ArrayList<>();
        SpuMigratedLogDomain spuMigratedLogQuery = spuMigratedLogMapper.selectByPrimaryKey(spuRemovalLogId);
        if (spuMigratedLogQuery != null) {

            if (StringUtils.isNotEmpty(spuMigratedLogQuery.getAddAttrs())) {
                String[] attributeIds = spuMigratedLogQuery.getAddAttrs().split(ErpConst.Symbol.COMMA);
                for (String attributeIdStr : attributeIds) {
                    final Integer attributeId;
                    try {
                        attributeId = Integer.valueOf(attributeIdStr);
                    } catch (NumberFormatException e) {
                        LOGGER.error("spu迁移记录保存的属性id不为整型 - attrIds: {}, attrIdStr: {}", attributeIds, attributeIdStr);
                        continue;
                    }

                    SpuRemovalDetailVo spuRemovalDetailVo = new SpuRemovalDetailVo();
                    BaseAttribute attributeQuery = baseAttributeService.selectByPrimaryKey(attributeId);
                    if (attributeQuery == null) {
                        LOGGER.error("获取spu迁移属性时未找到属性信息 - attrId: {}", attributeId);
                        continue;
                    }

                    if (StringUtils.isNotEmpty(spuMigratedLogQuery.getAddAttrValues())) {
                        String[] attributeValueIds = spuMigratedLogQuery.getAddAttrValues().split(ErpConst.Symbol.COMMA);
                        List<Integer> attributeValueIdList = Arrays.stream(attributeValueIds).mapToInt(Integer::valueOf).boxed().collect(Collectors.toList());
                        List<BaseAttributeValue> attributeValues = baseAttributeValueService.listAttributeValueByIds(attributeValueIdList);
                        Set<BaseAttributeValue> attributeValuesIfMatch = filterAttributeValueFrom(attributeValues, attributeId);
                        StringJoiner joiner = new StringJoiner(ErpConst.Symbol.COMMA);
                        for (BaseAttributeValue baseAttributeValue : attributeValuesIfMatch) {
                            joiner.add(baseAttributeValue.getAttrValue());
                        }
                        spuRemovalDetailVo.setOwnAttributeValueNames(joiner.toString());
                    }

                    spuRemovalDetailVo.setAttributeId(attributeQuery.getBaseAttributeId());
                    spuRemovalDetailVo.setAttributeName(attributeQuery.getBaseAttributeName());
                    resultList.add(spuRemovalDetailVo);
                }
            }
        }

        return resultList;
    }

    @Override
    public List<CoreSkuBaseVO> listSkuInfoAfterSpuRemoval(Integer spuId) {
        Preconditions.checkArgument(spuId != null, "spuId is null");

        List<CoreSkuBaseVO> returnedList = new ArrayList<>();
        List<CoreSkuGenerate> coreSkuGenerates = listAllSkuFor(spuId);
        if (CollectionUtils.isNotEmpty(coreSkuGenerates)) {
            Consumer<CoreSkuGenerate> coreSkuGenerateConsumer = skuDo -> {
                CoreSkuBaseVO skuBaseVO = new CoreSkuBaseVO();
                skuBaseVO.setShowName(skuDo.getShowName());
                skuBaseVO.setSkuNo(skuDo.getSkuNo());
                skuBaseVO.setPushStatus(skuDo.getPushStatus());
                skuBaseVO.setSellingStatusStr(getSaleStatusString(skuDo.getPushStatus(), skuDo.getOnSale()));
                skuBaseVO.setPushedPlatformNames(SKUPushedStatusEnum.getPlatformNames(skuDo.getPushStatus()));
                returnedList.add(skuBaseVO);
            };
            coreSkuGenerates.forEach(coreSkuGenerateConsumer);
        }
        return returnedList;

    }


    @SuppressWarnings("all")
    @Transactional(readOnly = true, rollbackFor = Exception.class)
    @Override
    public List<SpuRemovalPreparedVo> prepareSpuRemoval(Integer targetCategoryId, Integer[] spuIds) {
        Preconditions.checkArgument(Objects.nonNull(targetCategoryId), "商品分类id不能为空");
        Preconditions.checkArgument(ArrayUtils.isNotEmpty(spuIds), "spuId列表不能为空");

        final BaseCategoryVo targetCategoryQuery = baseCategoryService.getBaseCategoryByParam(targetCategoryId);
        if (targetCategoryQuery == null || Objects.equals(targetCategoryQuery.getIsDeleted(), CommonConstants.IS_DELETE_1)) {
            throw new IllegalArgumentException("目标商品分类信息已被删除或者不存在");
        }
        if (!Objects.equals(targetCategoryQuery.getBaseCategoryLevel(), THIRD_CATEGORY_LEVEL)) {
            throw new IllegalArgumentException("目前SPU移动只支持三级商品分类");
        }

        //获取分类下的属性值及属性值信息
        final List<BaseAttributeVo> targetCategoryAttrList = vgoodsService.getAttributeInfoByCategoryId(targetCategoryId);
        BaseCategoryVo categoryAttrCondition = new BaseCategoryVo();
        categoryAttrCondition.setBaseCategoryId(targetCategoryId);
        List<BaseAttributeValueVo> categoryAttributeValueList = baseAttributeValueService.getAttrValueListByCategoryId(Collections.singletonList(categoryAttrCondition));
        LOGGER.info("【SPU迁移准备阶段】获取到阶段目标分类用户的属性值, 目标分类: {},属性个数: {}, 属性明细: {}, 属性值个数:{}, 属性值明细: {}", targetCategoryId,
                targetCategoryAttrList.size(), JSON.toJSONString(targetCategoryAttrList, attributePropertyFilter), categoryAttributeValueList.size(), JSON.toJSONString(categoryAttributeValueList, attributeValuePropertyFilter));

        final List<SpuRemovalPreparedVo> resultList = new ArrayList<>();
        for (Integer currentSpuId : spuIds) {
            //获取待移动spu信息
            CoreSpuGenerate currentSpuInfo = getSpuInfoBySPUId(currentSpuId);

            if (targetCategoryId.equals(currentSpuInfo.getCategoryId())) {
                LOGGER.info("【SPU迁移准备阶段】选择的spu所属分类id与目标分类id相同 - spuId:{}, targetCategoryId: {}, originalCategoryId: {}", currentSpuInfo.getSpuId(), targetCategoryId, currentSpuInfo.getCategoryId());
                continue;
            }

            //获取sku关联属性
            List<CoreSkuGenerate> ownSkuList = listAllSkuFor(currentSpuId);

            //获取spu关联属性及属性值
            int categoryAttributeCountToChange = 0;

            List<? extends BaseAttribute> spuAttrList = baseAttributeService.listSpuOwnAttributeBySpuId(currentSpuInfo.getSpuId());
            if (CollectionUtils.isNotEmpty(ownSkuList)) {
                List<Integer> skuIdList = ownSkuList.stream().mapToInt(CoreSkuGenerate::getSkuId).boxed().collect(Collectors.toList());
                //获取spu下的属性值
                final List<BaseAttributeValue> skuAttributeValueList = this.listSkuAttributeValueForSpu(spuAttrList, skuIdList);


                LOGGER.info("【SPU迁移准备阶段】spu属性信息明细 - spuId:{}, spu关联拥有的属性: {}, spu关联的skuId: {}, 从spu关联过滤后的属性值信息: {}", currentSpuInfo.getSpuId(),
                        JSON.toJSONString(spuAttrList, attributePropertyFilter), skuIdList, JSON.toJSONString(spuAttrList, attributePropertyFilter), JSON.toJSONString(skuAttributeValueList, attributeValuePropertyFilter));

                //获取需要添加的目标需要添加的属性
                Set<BaseAttributeValue> attributeValueSetToAdd = getAttributeValueSetWithoutCategory(categoryAttributeValueList, skuAttributeValueList);
                Set<BaseAttribute> categoryAttributeToChange = getCategoryAttributeToChange(attributeValueSetToAdd, targetCategoryAttrList, spuAttrList);

                LOGGER.info("【SPU迁移准备阶段】需要增补属性情况 - 目标分类ID:{}, spuId: {}, 增补的属性: {}, 增补的属性值: {}", targetCategoryId, currentSpuInfo.getSpuId(),
                        JSON.toJSONString(categoryAttributeToChange, attributePropertyFilter), JSON.toJSONString(attributeValueSetToAdd, attributeValuePropertyFilter));

                categoryAttributeCountToChange = categoryAttributeToChange.size();
            }

            BaseCategoryVo originalCategoryQuery = null;
            if (currentSpuInfo.getCategoryId() != null) {
                originalCategoryQuery = baseCategoryService.getBaseCategoryByParam(currentSpuInfo.getCategoryId());
                if (originalCategoryQuery == null || Objects.equals(originalCategoryQuery.getIsDeleted(), CommonConstants.IS_DELETE_1)) {
                    LOGGER.error("【SPU迁移准备阶段】当前SPU关联得商品分类已被删除或不存在 - spuId:{}, spuCategoryId: {}", currentSpuInfo.getSpuNo(), currentSpuInfo.getCategoryId());
                }
            }

            //记录spu关联属性与属性值与目标属性的变化情况
            SpuRemovalPreparedVo spuRemovalRecordVo = new SpuRemovalPreparedVo();
            spuRemovalRecordVo.setSpuId(currentSpuInfo.getSpuId());
            spuRemovalRecordVo.setSpuShowName(currentSpuInfo.getShowName());
            if (originalCategoryQuery != null) {
                spuRemovalRecordVo.setOriginalCategoryId(originalCategoryQuery.getBaseCategoryId());
                spuRemovalRecordVo.setOriginalCategoryName(this.getAllHierarchicalNames(originalCategoryQuery.getBaseCategoryId()));
            }
            spuRemovalRecordVo.setTargetCategoryId(targetCategoryQuery.getBaseCategoryId());
            spuRemovalRecordVo.setTargetCategoryName(this.getAllHierarchicalNames(targetCategoryQuery.getBaseCategoryId()));
            spuRemovalRecordVo.setOwnSkuCount(ownSkuList.size());
            spuRemovalRecordVo.setChangedAttrCount(categoryAttributeCountToChange);

            resultList.add(spuRemovalRecordVo);
        }

        return resultList;
    }


    @SuppressWarnings("all")
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void confirmSpuRemoval(final Integer targetCategoryId, Integer[] spuIds, String reason) {
        Preconditions.checkArgument(Objects.nonNull(targetCategoryId), "商品分类id不能为空");
        Preconditions.checkArgument(ArrayUtils.isNotEmpty(spuIds), "spuId列表不能为空");

        User currentUser;
        try {
            currentUser = (User) SecurityUtils.getSubject().getSession().getAttribute(Consts.SESSION_USER);
        } catch (Exception e) {
            throw new IllegalStateException("获取当前登录用户信息失败");
        }

        //TODO prod env需验证迁移原因

        //获取目标分类相关信息
        final BaseCategoryVo targetCategoryQuery = baseCategoryService.getBaseCategoryByParam(targetCategoryId);
        if (targetCategoryQuery == null || Objects.equals(targetCategoryQuery.getIsDeleted(), CommonConstants.IS_DELETE_1)) {
            throw new IllegalArgumentException("目标商品分类信息[categoryId=" + targetCategoryId + "]已被删除或者不存在");
        }
        if (!Objects.equals(targetCategoryQuery.getBaseCategoryLevel(), THIRD_CATEGORY_LEVEL)) {
            throw new IllegalArgumentException("目前SPU移动只支持三级商品分类");
        }

        //获取目标分类下的属性值及属性值
        final List<BaseAttributeVo> targetCategoryAttrList = vgoodsService.getAttributeInfoByCategoryId(targetCategoryId);
        BaseCategoryVo categoryAttrCondition = new BaseCategoryVo();
        categoryAttrCondition.setBaseCategoryId(targetCategoryId);
        List<BaseAttributeValueVo> targetCategoryAttributeValueList = baseAttributeValueService.getAttrValueListByCategoryId(Collections.singletonList(categoryAttrCondition));
        LOGGER.info("【SPU迁确认阶段】获取到阶段目标分类用户的属性值, 目标分类: {},属性个数: {}, 属性明细: {}, 属性值个数:{}, 属性值明细: {}", targetCategoryId,
                targetCategoryAttrList.size(), JSON.toJSONString(targetCategoryAttrList, attributePropertyFilter), targetCategoryAttributeValueList.size(), JSON.toJSONString(targetCategoryAttributeValueList, attributeValuePropertyFilter));

        //todo spu迁移审核 prod env触发

        //去除重复属性值
        final Set<BaseAttributeValue> allCategoryAttrValueToAdd = new HashSet<>();

        List<Integer> originCategoryIdList = new ArrayList<>();

        for (Integer currentSpuId : spuIds) {
            //获取待移动spu信息
            CoreSpuGenerate currentSpuInfo = getSpuInfoBySPUId(currentSpuId);

            if (targetCategoryId.equals(currentSpuInfo.getCategoryId())) {
                LOGGER.error("【SPU迁移】确认阶段spu的当前归属分类与目标分类相同，不需要进行属性迁移 - spuId:{}, targetCategoryId:{}", currentSpuInfo.getSpuId(), targetCategoryId);
                continue;
            }

            Integer originalCategoryId = currentSpuInfo.getCategoryId();
            String originalCategoryName = null;
            //获取原始分类相关信息
            boolean hasOriginalCategory = true;
            if (originalCategoryId == null) {
                hasOriginalCategory = false;
            } else {
                BaseCategoryVo originalCategoryQuery = baseCategoryService.getBaseCategoryByParam(originalCategoryId);
                if (originalCategoryQuery == null || Objects.equals(originalCategoryQuery.getIsDeleted(), CommonConstants.IS_DELETE_1)) {
                    LOGGER.error("【SPU迁移确认阶段】spu当前归属分类不存在或者已被删除 - spuId: {}", currentSpuInfo.getSpuId());
                    hasOriginalCategory = false;
                } else {
                    originalCategoryName = originalCategoryQuery.getBaseCategoryName();
                }
            }

            originCategoryIdList.add(originalCategoryId);

            //获取spu关联属性及属性值
            List<? extends BaseAttribute> spuAttrList = baseAttributeService.listSpuOwnAttributeBySpuId(currentSpuInfo.getSpuId());

            //获取sku关联属性
            List<CoreSkuGenerate> ownSkuList = listAllSkuFor(currentSpuId);

            List<Integer> skuIdList = ownSkuList.stream().mapToInt(CoreSkuGenerate::getSkuId).boxed().collect(Collectors.toList());

            //获取spu下的属性值
            final List<BaseAttributeValue> skuAttributeValueList = this.listSkuAttributeValueForSpu(spuAttrList, skuIdList);

            //获取需要添加的目标需要添加的属性
            Set<BaseAttributeValue> attributeValueSetToAdd = getAttributeValueSetWithoutCategory(targetCategoryAttributeValueList, skuAttributeValueList);

            Set<BaseAttribute> categoryAttributeToChange = getCategoryAttributeToChange(attributeValueSetToAdd, targetCategoryAttrList, spuAttrList);

            LOGGER.info("【SPU迁移确认阶段】需要增补属性情况 - 目标分类ID:{}, spuId: {}, 增补的属性: {}, 增补的属性值: {}", targetCategoryId, currentSpuInfo.getSpuId(),
                    JSON.toJSONString(categoryAttributeToChange, attributePropertyFilter), JSON.toJSONString(attributeValueSetToAdd, attributeValuePropertyFilter));

            //放入目标属性需要补充的属性值合计
            allCategoryAttrValueToAdd.addAll(attributeValueSetToAdd);

            currentSpuInfo.setCategoryId(targetCategoryId);
            currentSpuInfo.setModTime(new Date());
            currentSpuInfo.setUpdater(currentUser.getUserId());
            coreSpuGenerateMapper.updateByPrimaryKey(currentSpuInfo);

            //保存spu迁移日志
            SpuMigratedLogDomain spuMigratedLogDomain = new SpuMigratedLogDomain();
            spuMigratedLogDomain.setSpuId(currentSpuInfo.getSpuId());
            spuMigratedLogDomain.setSpuNo(currentSpuInfo.getSpuNo());
            spuMigratedLogDomain.setCategoryIdPre(originalCategoryId);
            spuMigratedLogDomain.setCategoryNamePre(originalCategoryName);
            if (hasOriginalCategory) {
                spuMigratedLogDomain.setOriginPath(baseCategoryMapper.getAllHierarchyFor(originalCategoryId));
            }
            spuMigratedLogDomain.setTargetPath(baseCategoryMapper.getAllHierarchyFor(targetCategoryId));
            spuMigratedLogDomain.setCategoryIdAfter(targetCategoryId);
            spuMigratedLogDomain.setCategoryNameAfter(targetCategoryQuery.getBaseCategoryName());
            //增补的属性
            if (!categoryAttributeToChange.isEmpty()) {
                StringJoiner attIdJoiner = new StringJoiner(ErpConst.Symbol.COMMA);
                categoryAttributeToChange.forEach(attribute -> attIdJoiner.add(String.valueOf(attribute.getBaseAttributeId())));
                spuMigratedLogDomain.setAddAttrs(attIdJoiner.toString());
            }

            //增补的属性值
            if (!attributeValueSetToAdd.isEmpty()) {
                StringJoiner attrValueIdJoiner = new StringJoiner(ErpConst.Symbol.COMMA);
                attributeValueSetToAdd.forEach(attrValue -> attrValueIdJoiner.add(String.valueOf(attrValue.getBaseAttributeValueId())));
                spuMigratedLogDomain.setAddAttrValues(attrValueIdJoiner.toString());
            }

            spuMigratedLogDomain.setMigrationReason(reason);
            spuMigratedLogDomain.setDeleted(CommonConstants.IS_DELETE_0);
            spuMigratedLogDomain.setUpdater(currentUser.getUserId());
            spuMigratedLogDomain.setUpdateTime(System.currentTimeMillis());
            spuMigratedLogDomain.setAddTime(System.currentTimeMillis());
            spuMigratedLogDomain.setCreator(currentUser.getUserId());
            spuMigratedLogMapper.insertSelective(spuMigratedLogDomain);
        }

        //添加新增属性子
        if (!allCategoryAttrValueToAdd.isEmpty()) {
            final List<CategoryAttrValueMapping> categoryAttrValueToBatchSave = new LinkedList<>();
            allCategoryAttrValueToAdd.forEach(attrValueToAdd -> {
                CategoryAttrValueMapping categoryAttrValueMapping = new CategoryAttrValueMapping();
                categoryAttrValueMapping.setBaseCategoryId(targetCategoryId);
                categoryAttrValueMapping.setIsDeleted(CommonConstants.IS_DELETE_0);
                categoryAttrValueMapping.setBaseAttributeId(attrValueToAdd.getBaseAttributeId());
                categoryAttrValueMapping.setBaseAttributeValueId(attrValueToAdd.getBaseAttributeValueId());
                categoryAttrValueMapping.setCreator(currentUser.getUserId());
                categoryAttrValueMapping.setAddTime(new Date());
                categoryAttrValueMapping.setUpdater(currentUser.getUserId());
                categoryAttrValueMapping.setModTime(new Date());
                categoryAttrValueToBatchSave.add(categoryAttrValueMapping);
            });
            categoryAttrValueMappingMapper.insertCategoryAttrValueMappingBatch(categoryAttrValueToBatchSave);

            LOGGER.info("【SPU迁移确认阶段】目标分类新增数属性值信息 - 目标分类ID:{}, 新增属性值数量:{} ,新增属性数量: {}, 新增属性明细：{}", targetCategoryId, categoryAttrValueToBatchSave.size(),
                    categoryAttrValueToBatchSave.size(), JSON.toJSONString(categoryAttrValueToBatchSave, categoryAttributeValueMapingPropertyFilter));
        }

        //Last step - 同步删除原分类、新分类中未被任何SPU、SKU引用的属性及属性值
        if (!originCategoryIdList.isEmpty()) {
            final Set<Integer> categoryIdListToDelete = new HashSet<>(originCategoryIdList);
            categoryIdListToDelete.add(targetCategoryId);
            deleteCategoryValueAfterSpuRemove(categoryIdListToDelete, currentUser);
        }
    }


    /**
     * 获取上下家状态
     *
     * @return
     */
    private String getSaleStatusString(Integer pushStatus, Integer onSale) {
        VgoodsServiceImpl vgoodsService = (VgoodsServiceImpl) this.vgoodsService;
        return vgoodsService.getSaleStr(pushStatus, onSale);
    }


    @Override
    public void exportGoodsRemovalRecords(Integer[] spuRemovalLogIds, OutputStream outputStream) throws IOException {
        if (ArrayUtils.isEmpty(spuRemovalLogIds)) {
            return;
        }
        HSSFWorkbook workbook = new HSSFWorkbook();

        List<SpuRemovalLogDto> spuRemovalLogDtoList = spuMigratedLogMapper.listSpuRemovalRecordsBySpuIdList(Arrays.asList(spuRemovalLogIds));
        List<Integer> spuIdList = spuRemovalLogDtoList.stream().map(SpuRemovalLogDto::getSpuId).collect(Collectors.toList());

        int currentSheetNo = 1;
        for (Map.Entry<String, List<String>> entry : SHEET_HEADER_NAME_MAP.entrySet()) {
            HSSFSheet currentSheet = workbook.createSheet(entry.getKey());
            int currRowIdx = 0;
            HSSFRow firstRow = currentSheet.createRow(currRowIdx++);
            List<String> tableHeaderNames = entry.getValue();
            for (int i = 0; i < tableHeaderNames.size(); i++) {
                HSSFCell cell = firstRow.createCell(i);
                cell.setCellValue(tableHeaderNames.get(i));
            }

            if (currentSheetNo == 1) {
                //SPU迁移表数据信息
                for (SpuRemovalLogDto spuRemovalLogDto : spuRemovalLogDtoList) {
                    GoodsRemovalRecordVo goodsRemovalRecordVo = convert2GoodsRemovalRecordVo(spuRemovalLogDto);
                    HSSFRow currentRow = currentSheet.createRow(currRowIdx++);
                    convertSpuDataExportToRow(goodsRemovalRecordVo, currentRow);
                }

            } else if (currentSheetNo == 2) {
                //SKU迁移表数据信息
                for (Integer spuId : spuIdList) {
                    List<SkuExcelDataDto> skuExcelDataDtoList = coreSpuGenerateMapper.listOwnSkuForDataExport(spuId);
                    for (SkuExcelDataDto skuExcelDataDto : skuExcelDataDtoList) {
                        HSSFRow currentRow = currentSheet.createRow(currRowIdx++);
                        convertSkuDataExportToRow(skuExcelDataDto, currentRow);
                    }
                }

            } else if (currentSheetNo == 3) {
                //属性增补表数据信息
                for (SpuRemovalLogDto spuRemovalLogDto : spuRemovalLogDtoList) {
                    AttributeExcelDataDto attributeExcelDataDto = convert2AttributeExcelDataDto(spuRemovalLogDto);
                    HSSFRow currentRow = currentSheet.createRow(currRowIdx++);
                    convertAttributeExcelDataDtoToRow(attributeExcelDataDto, currentRow);
                }
            }

            currentSheetNo++;
        }

        workbook.write(outputStream);
    }


    //～==================================================================================Private methods


    private String getAllHierarchicalNames(Integer thirdCategoryId) {
        if (thirdCategoryId == null) {
            return null;
        }
        return baseCategoryMapper.getOrganizedCategoryNameById(thirdCategoryId, ErpConst.Symbol.SLASH);
    }

    private List<CoreSkuGenerate> listAllSkuFor(Integer spuId) {
        CoreSkuGenerateExample skuCondition = new CoreSkuGenerateExample();
        skuCondition.createCriteria().andSpuIdEqualTo(spuId).andStatusEqualTo(CommonConstants.STATUS_1);
        return coreSkuGenerateMapper.selectByExample(skuCondition);
    }


    private CoreSpuGenerate getSpuInfoBySPUId(Integer spuId) {
        //获取待移动spu信息
        CoreSpuGenerateExample spuExample = new CoreSpuGenerateExample();
        spuExample.createCriteria().andSpuIdEqualTo(spuId).andStatusEqualTo(CommonConstants.STATUS_1);
        List<CoreSpuGenerate> spuList = coreSpuGenerateMapper.selectByExample(spuExample);
        if (CollectionUtils.isEmpty(spuList)) {
            throw new IllegalStateException("商品spu列表信息[spuId=" + spuId + "]不存在或已被警用");
        } else if (spuList.size() > 1) {
            throw new IllegalStateException("商品spu列表信息[spuId=" + spuId + "]返记录大于一条");
        }

        return spuList.get(0);
    }


    /**
     * 获取目标分类需要增加的属性值集合
     *
     * @param categoryAttributeValueList
     * @param spuAttributeValueList
     * @return
     */
    private Set<BaseAttributeValue> getAttributeValueSetWithoutCategory(final List<? extends BaseAttributeValue> categoryAttributeValueList, final List<BaseAttributeValue> spuAttributeValueList) {
        //获取目标分类不存的属性值
        Set<BaseAttributeValue> categoryAttrValueSet = new HashSet<>(categoryAttributeValueList);
        Set<BaseAttributeValue> spuAttrValueSet = new HashSet<>(spuAttributeValueList);

        //目标分类需要增加属性数
        Set<BaseAttributeValue> resultSetIfNotFound = new HashSet<>();
        for (BaseAttributeValue spuAttributeValue : spuAttrValueSet) {
            if (!categoryAttrValueSet.contains(spuAttributeValue)) {
                resultSetIfNotFound.add(spuAttributeValue);
            }
        }

        return resultSetIfNotFound;
    }


    private Set<BaseAttributeValue> filterAttributeValueFrom(Collection<? extends BaseAttributeValue> attributeValuesList, Integer attributeIdToMath) {
        return attributeValuesList.stream().filter(attrValue -> {
            if (attributeIdToMath == null || attrValue.getBaseAttributeId() == null) {
                return false;
            }
            return attributeIdToMath.equals(attrValue.getBaseAttributeId());
        }).collect(Collectors.toSet());
    }


    /**
     * 获取spu下sku所关联的属性值
     *
     * @param spuOwnAttrList
     * @param skuIdList
     * @return
     */
    private List<BaseAttributeValue> listSkuAttributeValueForSpu(List<? extends BaseAttribute> spuOwnAttrList, List<Integer> skuIdList) {
        if (CollectionUtils.isEmpty(spuOwnAttrList)) {
            return Collections.emptyList();
        }

        final List<BaseAttributeValue> skuAttrValueList = baseAttributeValueService.listAttributeValueBySkuIds(skuIdList);
        if (CollectionUtils.isEmpty(skuAttrValueList)) {
            return Collections.emptyList();
        }

        List<BaseAttributeValue> attributeValuesIfMatch = new ArrayList<>();
        for (BaseAttribute spuAttribute : spuOwnAttrList) {
            Set<BaseAttributeValue> baseAttributeValuesAfterFilter = filterAttributeValueFrom(skuAttrValueList, spuAttribute.getBaseAttributeId());
            attributeValuesIfMatch.addAll(baseAttributeValuesAfterFilter);
        }
        return attributeValuesIfMatch;
    }

    /**
     * 获取目标中不存在的属性列表
     *
     * @param categoryAttrList
     * @param spuAttrList
     * @return
     */
    private Set<BaseAttribute> getCategoryAttributeToChange(final Set<BaseAttributeValue> attributeValueSetToAdd, List<? extends BaseAttribute> categoryAttrList, List<? extends BaseAttribute> spuAttrList) {
        //fast fail
        if (CollectionUtils.isEmpty(attributeValueSetToAdd)) {
            return Collections.emptySet();
        }

        Set<BaseAttribute> categoryAttrSet = new HashSet<>(categoryAttrList);
        Set<BaseAttribute> spuAttrSet = new HashSet<>(spuAttrList);

        //Step1:添加原有分类没有的属性
        Set<BaseAttribute> attributeSetIfChange = new HashSet<>();
        for (BaseAttribute currentSpuAttribute : spuAttrSet) {
            if (!categoryAttrSet.contains(currentSpuAttribute)) {
                attributeSetIfChange.add(currentSpuAttribute);
            }
        }

        //Step2:添加原有分类属性需要增补属性值的分类
        for (BaseAttribute currentCategoryAttribute : categoryAttrList) {
            Set<BaseAttributeValue> categoryAttributeAftFilter = filterAttributeValueFrom(attributeValueSetToAdd, currentCategoryAttribute.getBaseAttributeId());
            if (categoryAttributeAftFilter.size() > 0) {
                attributeSetIfChange.add(currentCategoryAttribute);
            }
        }

        return attributeSetIfChange;
    }


    /**
     * 同步删除原分类、新分类中未被任何SPU、SKU引用的属性及属性值
     *
     * @param categoryIdListToDelete
     */
    private void deleteCategoryValueAfterSpuRemove(final Set<Integer> categoryIdListToDelete, User currentUser) {
        final List<CategoryAttrValueMapping> categoryAttrValueMappingsToDelete = new LinkedList<>();
        for (Integer currentCategoryId : categoryIdListToDelete) {
            List<Integer> spuIdList = baseCategoryMapper.listSpuIdiUnderThirdCategory(currentCategoryId);
            List<CategoryAttrValueMapping> categoryAttrValueMappingList = baseCategoryService.getCategoryAttrValueMappingVoList(currentCategoryId);
            if (CollectionUtils.isEmpty(spuIdList)) {
                //当前分类下已没有关联的spu需要删除所有属性
                categoryAttrValueMappingsToDelete.addAll(categoryAttrValueMappingList);
            } else {
                //删除当前分类与现在关联的spu、sku没有关联的属性值型信息
                CoreSkuGenerateExample skuCondition = new CoreSkuGenerateExample();
                skuCondition.createCriteria().andSpuIdIn(spuIdList).andStatusEqualTo(CommonConstants.STATUS_1);
                List<CoreSkuGenerate> skuList = coreSkuGenerateMapper.selectByExample(skuCondition);
                List<Integer> allSkuIdList = skuList.stream().mapToInt(CoreSkuGenerate::getSkuId).boxed().collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(allSkuIdList)) {
                    //获取所有sku关联的属性子值
                    final List<BaseAttributeValue> skuAttrValueList = baseAttributeValueService.listAttributeValueBySkuIds(allSkuIdList);
                    next:
                    for (CategoryAttrValueMapping categoryAttrValueMapping : categoryAttrValueMappingList) {
                        for (BaseAttributeValue skuAttributeValue : skuAttrValueList) {
                            if (categoryAttrValueMapping.getBaseAttributeValueId() == null) {
                                LOGGER.error("【数据异常】分类属性值的关联id为空 - data:{}", JSON.toJSONString(categoryAttrValueMapping));
                                continue;
                            }
                            if (skuAttributeValue.getBaseAttributeValueId() == null) {
                                LOGGER.error("【数据异常】sku与属性值的关联id为空 - data:{}", JSON.toJSONString(skuAttributeValue));
                                continue;
                            }
                            if (categoryAttrValueMapping.getBaseAttributeValueId().equals(skuAttributeValue.getBaseAttributeValueId())) {
                                continue next;
                            }
                        }
                        //not match
                        categoryAttrValueMappingsToDelete.add(categoryAttrValueMapping);
                    }
                }
            }
        }

        if (!categoryAttrValueMappingsToDelete.isEmpty()) {
            //设置删除操作时间及操作人
            categoryAttrValueMappingsToDelete.forEach(e -> {
                e.setIsDeleted(CommonConstants.IS_DELETE_1);
                e.setUpdater(currentUser.getUserId());
                e.setModTime(new Date());
            });
            categoryAttrValueMappingMapper.deleteCategoryAttrMapping(categoryAttrValueMappingsToDelete);
            LOGGER.info("【SPU迁移确认阶段】同步删除原分类、新分类中未被任何SPU、SKU引用的属性及属性值 - dataToDelete: {}", JSON.toJSONString(categoryAttrValueMappingsToDelete, categoryAttributeValueMapingPropertyFilter));
        }
    }


    private final static List<String> FIRST_SHEET_NAMES = new ArrayList<>();

    static {
        FIRST_SHEET_NAMES.add("SPU_ID");
        FIRST_SHEET_NAMES.add("SPU名称");
        FIRST_SHEET_NAMES.add("原始路径");
        FIRST_SHEET_NAMES.add("目标路径");
        FIRST_SHEET_NAMES.add("原始父ID");
        FIRST_SHEET_NAMES.add("目标父ID");
        FIRST_SHEET_NAMES.add("SKU数");
        FIRST_SHEET_NAMES.add("增补属性数");
        FIRST_SHEET_NAMES.add("迁移原因");
        FIRST_SHEET_NAMES.add("操作时间");
        FIRST_SHEET_NAMES.add("操作人");
    }


    private final static List<String> SECOND_SHEET_NAMES = new ArrayList<>();

    static {
        SECOND_SHEET_NAMES.add("SPU_ID");
        SECOND_SHEET_NAMES.add("SPU名称");
        SECOND_SHEET_NAMES.add("SKU名称");
        SECOND_SHEET_NAMES.add("订货号");
        SECOND_SHEET_NAMES.add("推送状态");
        SECOND_SHEET_NAMES.add("上下架状态");
    }


    private final static List<String> THIRD_SHEET_NAMES = new ArrayList<>();

    static {
        THIRD_SHEET_NAMES.add("SPU_ID");
        THIRD_SHEET_NAMES.add("SPU名称");
        THIRD_SHEET_NAMES.add("增补属性ID");
        THIRD_SHEET_NAMES.add("增补属性名");
        THIRD_SHEET_NAMES.add("增补属性值");
        THIRD_SHEET_NAMES.add("原始分类ID");
        THIRD_SHEET_NAMES.add("原始分类路径");
        THIRD_SHEET_NAMES.add("目标分类ID");
        THIRD_SHEET_NAMES.add("目标分类路径");
    }

    private static final Map<String, List<String>> SHEET_HEADER_NAME_MAP = new LinkedHashMap<>(3);

    static {
        SHEET_HEADER_NAME_MAP.put("SPU迁移表", FIRST_SHEET_NAMES);
        SHEET_HEADER_NAME_MAP.put("SKU迁移表", SECOND_SHEET_NAMES);
        SHEET_HEADER_NAME_MAP.put("属性增补表", THIRD_SHEET_NAMES);
    }


    private Integer getCategoryIdPFrom(String categoryIdStr) {
        if (StringUtils.isNotEmpty(categoryIdStr)) {
            String[] categoryIds = categoryIdStr.split(ErpConst.Symbol.COMMA);
            if (categoryIds.length == 3) {
                try {
                    return Integer.valueOf(categoryIds[1]);
                } catch (NumberFormatException e) {
                    //no-op
                }
            }
        }
        return null;
    }


    private GoodsRemovalRecordVo convert2GoodsRemovalRecordVo(SpuRemovalLogDto spuRemovalLogDto) {
        GoodsRemovalRecordVo goodsRemovalRecordVo = new GoodsRemovalRecordVo();

        goodsRemovalRecordVo.setSpuRemovalRecordId(spuRemovalLogDto.getSpuMigtationLogId());
        goodsRemovalRecordVo.setSpuId(spuRemovalLogDto.getSpuId());
        goodsRemovalRecordVo.setSpuShowName(spuRemovalLogDto.getSpuShowName());
        goodsRemovalRecordVo.setLastOperatorName(spuRemovalLogDto.getLastOperatorName());
        goodsRemovalRecordVo.setReason(spuRemovalLogDto.getReason());

        if (spuRemovalLogDto.getCategoryIdPre() != null) {
            goodsRemovalRecordVo.setOriginalPath(this.getAllHierarchicalNames(spuRemovalLogDto.getCategoryIdPre()));
        }
        if (spuRemovalLogDto.getCategoryIdAfter() != null) {
            goodsRemovalRecordVo.setTargetPath(this.getAllHierarchicalNames(spuRemovalLogDto.getCategoryIdAfter()));
            goodsRemovalRecordVo.setTargetCategoryId(spuRemovalLogDto.getCategoryIdAfter());
        }

        goodsRemovalRecordVo.setOriginalParentId(getCategoryIdPFrom(spuRemovalLogDto.getOriginPath()));
        goodsRemovalRecordVo.setTargetParentId(getCategoryIdPFrom(spuRemovalLogDto.getTargetPath()));

        if (StringUtils.isNotEmpty(spuRemovalLogDto.getAddAttrs())) {
            int additionalAttCount = spuRemovalLogDto.getAddAttrs().split(ErpConst.Symbol.COMMA).length;
            goodsRemovalRecordVo.setAddedAttributeCount(additionalAttCount);
        } else {
            goodsRemovalRecordVo.setAddedAttributeCount(DEFAULT_COUNT);
        }
        if (spuRemovalLogDto.getSpuId() != null) {
            CoreSkuGenerateExample skuCondition = new CoreSkuGenerateExample();
            skuCondition.createCriteria().andSpuIdEqualTo(spuRemovalLogDto.getSpuId()).andStatusEqualTo(CommonConstants.STATUS_1);
            int ownSkuCount = coreSkuGenerateMapper.countByExample(skuCondition);
            goodsRemovalRecordVo.setSkuCount(ownSkuCount);
        } else {
            goodsRemovalRecordVo.setSkuCount(DEFAULT_COUNT);
        }
        if (spuRemovalLogDto.getLastUpdateTime() != null) {
            goodsRemovalRecordVo.setLastModTime(DateUtil.convertString(spuRemovalLogDto.getLastUpdateTime(), DateUtil.TIME_FORMAT));
        }

        return goodsRemovalRecordVo;
    }


    private AttributeExcelDataDto convert2AttributeExcelDataDto(SpuRemovalLogDto spuRemovalLogDto) {
        AttributeExcelDataDto attributeExcelDataDto = new AttributeExcelDataDto();

        attributeExcelDataDto.setSpuId(spuRemovalLogDto.getSpuId());
        attributeExcelDataDto.setSpuShowName(spuRemovalLogDto.getSpuShowName());

        if (spuRemovalLogDto.getCategoryIdPre() != null) {
            attributeExcelDataDto.setOriginalCategoryId(spuRemovalLogDto.getCategoryIdPre());
            attributeExcelDataDto.setOriginalCategoryPath(this.getAllHierarchicalNames(spuRemovalLogDto.getCategoryIdPre()));
        }
        if (spuRemovalLogDto.getCategoryIdAfter() != null) {
            attributeExcelDataDto.setTargetCategoryId(spuRemovalLogDto.getCategoryIdAfter());
            attributeExcelDataDto.setTargetCategoryPath(this.getAllHierarchicalNames(spuRemovalLogDto.getCategoryIdAfter()));
        }


        String attrNames = null;
        if (StringUtils.isNotEmpty(spuRemovalLogDto.getAddAttrs())) {
            String addAttrs = spuRemovalLogDto.getAddAttrs();
            String[] attrIdArray = addAttrs.split(ErpConst.Symbol.COMMA);
            List<Integer> attrIdList = Arrays.stream(attrIdArray).map(Integer::valueOf).distinct().collect(Collectors.toList());
            List<BaseAttribute> attributesList = baseAttributeMapper.listAttributeByAttrIdList(attrIdList);
            if (CollectionUtils.isNotEmpty(attributesList)) {
                StringJoiner attrNameJoiner = new StringJoiner(ErpConst.Symbol.COMMA);
                for (BaseAttribute attribute : attributesList) {
                    attrNameJoiner.add(attribute.getBaseAttributeName());
                }
                attrNames = attrNameJoiner.toString();
            }

            String attrValueNames = null;
            if (StringUtils.isNotEmpty(spuRemovalLogDto.getAddAttrValues())) {
                StringJoiner attrValueNameJoiner = new StringJoiner(ErpConst.Symbol.COMMA);
                String[] attrValueIdArray = spuRemovalLogDto.getAddAttrValues().split(ErpConst.Symbol.COMMA);
                List<Integer> attrValueIdList = Arrays.stream(attrValueIdArray).map(Integer::valueOf).distinct().collect(Collectors.toList());
                List<BaseAttributeValue> attributeValueVoList = baseAttributeValueService.listAttributeValueByIds(attrValueIdList);
                attributeValueVoList.forEach(e -> attrValueNameJoiner.add(e.getAttrValue()));
                attrValueNames = attrValueNameJoiner.toString();
            }

            attributeExcelDataDto.setAddedAttIds(spuRemovalLogDto.getAddAttrs());
            attributeExcelDataDto.setAddedAttrNames(attrNames);
            attributeExcelDataDto.setAddedAttrValueNames(attrValueNames);
        }

        return attributeExcelDataDto;
    }


    private void convertSpuDataExportToRow(GoodsRemovalRecordVo goodsRemovalRecordVo, Row row) {
        int currCellIdx = 0;
        Cell currCell;

        currCell = row.createCell(currCellIdx++);
        currCell.setCellValue(goodsRemovalRecordVo.getSpuId());

        currCell = row.createCell(currCellIdx++);
        currCell.setCellValue(goodsRemovalRecordVo.getSpuShowName());

        currCell = row.createCell(currCellIdx++);
        currCell.setCellValue(goodsRemovalRecordVo.getOriginalPath());


        currCell = row.createCell(currCellIdx++);
        currCell.setCellValue(goodsRemovalRecordVo.getTargetPath());


        currCell = row.createCell(currCellIdx++);
        currCell.setCellValue(goodsRemovalRecordVo.getOriginalParentId());


        currCell = row.createCell(currCellIdx++);
        currCell.setCellValue(goodsRemovalRecordVo.getTargetParentId());


        currCell = row.createCell(currCellIdx++);
        currCell.setCellValue(goodsRemovalRecordVo.getSkuCount());

        currCell = row.createCell(currCellIdx++);
        currCell.setCellValue(goodsRemovalRecordVo.getAddedAttributeCount());

        currCell = row.createCell(currCellIdx++);
        currCell.setCellValue(goodsRemovalRecordVo.getReason());

        currCell = row.createCell(currCellIdx++);
        currCell.setCellValue(goodsRemovalRecordVo.getLastModTime());

        currCell = row.createCell(currCellIdx);
        currCell.setCellValue(goodsRemovalRecordVo.getLastOperatorName());
    }

    private void convertSkuDataExportToRow(SkuExcelDataDto skuExcelDataDto, Row row) {
        int currCellIdx = 0;
        Cell currCell;

        currCell = row.createCell(currCellIdx++);
        currCell.setCellValue(skuExcelDataDto.getSpuId());

        currCell = row.createCell(currCellIdx++);
        currCell.setCellValue(skuExcelDataDto.getSpuShowName());

        currCell = row.createCell(currCellIdx++);
        currCell.setCellValue(skuExcelDataDto.getSkuShowName());

        currCell = row.createCell(currCellIdx++);
        currCell.setCellValue(skuExcelDataDto.getSkuNo());

        currCell = row.createCell(currCellIdx++);
        currCell.setCellValue(SKUPushedStatusEnum.getPlatformNames(skuExcelDataDto.getPushStatus()));

        currCell = row.createCell(currCellIdx);
        currCell.setCellValue(getSaleStatusString(skuExcelDataDto.getPushStatus(), skuExcelDataDto.getOnSale()));

    }

    private void convertAttributeExcelDataDtoToRow(AttributeExcelDataDto attributeExcelDataDto, Row row) {
        int currCellIdx = 0;
        Cell currCell;

        currCell = row.createCell(currCellIdx++);
        currCell.setCellValue(attributeExcelDataDto.getSpuId());

        currCell = row.createCell(currCellIdx++);
        currCell.setCellValue(attributeExcelDataDto.getSpuShowName());

        currCell = row.createCell(currCellIdx++);
        currCell.setCellValue(attributeExcelDataDto.getAddedAttIds());

        currCell = row.createCell(currCellIdx++);
        currCell.setCellValue(attributeExcelDataDto.getAddedAttrNames());

        //增补属性值
        currCell = row.createCell(currCellIdx++);
        currCell.setCellValue(attributeExcelDataDto.getAddedAttrValueNames());

        currCell = row.createCell(currCellIdx++);
        currCell.setCellValue(attributeExcelDataDto.getOriginalCategoryId());

        currCell = row.createCell(currCellIdx++);
        currCell.setCellValue(attributeExcelDataDto.getOriginalCategoryPath());

        currCell = row.createCell(currCellIdx++);
        currCell.setCellValue(attributeExcelDataDto.getTargetCategoryId());

        currCell = row.createCell(currCellIdx);
        currCell.setCellValue(attributeExcelDataDto.getTargetCategoryPath());
    }


}
