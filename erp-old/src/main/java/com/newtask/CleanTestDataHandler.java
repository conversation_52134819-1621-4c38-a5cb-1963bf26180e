package com.newtask;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.order.dao.SaleorderMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * 任务Handler示例（Bean模式）
 * 
 * 开发步骤：
 * 1、继承"IJobHandler"：“com.xxl.job.core.handler.IJobHandler”；
 * 2、注册到Spring容器：添加“@Component”注解，被Spring容器扫描为Bean实例；
 * 3、注册到执行器工厂：添加“@JobHandler(value="自定义jobhandler名称")”注解，注解value值对应的是调度中心新建任务的JobHandler属性的值。
 * 4、执行日志：需要通过 "XxlJobLogger.log" 打印执行日志；
 * 
 * <AUTHOR> 2015-12-19 19:43:36
 */
@JobHandler(value="cleanTestDataHandler")
@Component
public class CleanTestDataHandler extends AbstractJobHandler {
	@Resource
	private SaleorderMapper saleorderMapper;

	@Value("${test_userNames}")
	private String testUserNames;


	@Override
	public ReturnT<String> doExecute(String param) throws Exception {

		XxlJobLogger.log("cleanTestDataHandler start.");
		int orderCount2=saleorderMapper.cleanSaleOrder2(testUserNames);
		XxlJobLogger.log("cleanTestDataHandler end.order2:"+orderCount2  );
		int orderCount=saleorderMapper.cleanSaleOrder(testUserNames);
		XxlJobLogger.log("cleanTestDataHandler end.order:"+orderCount  );
		int afterOrderCount=saleorderMapper.cleanAfterSale(testUserNames);

		XxlJobLogger.log("cleanTestDataHandler end.afterOrderCount:"+afterOrderCount );

        int clearBussiness=saleorderMapper.clearBussiness(testUserNames);
		XxlJobLogger.log("cleanTestDataHandler end.clearBussiness:"+clearBussiness );

        int clearBuyorderInvoice=saleorderMapper.clearBuyorderInvoice(testUserNames);
		XxlJobLogger.log("cleanTestDataHandler end.clearBuyorderInvoice:"+clearBuyorderInvoice );

        int clearSaleorderInvoice=saleorderMapper.clearSaleorderInvoice(testUserNames);
		XxlJobLogger.log("cleanTestDataHandler end.clearSaleorderInvoice:"+clearSaleorderInvoice );
        int clearBuyorder=saleorderMapper.clearBuyorder(testUserNames);
		XxlJobLogger.log("cleanTestDataHandler end.clearBuyorder:"+clearBuyorder );
        int clearInvoice=saleorderMapper.clearInvoice(testUserNames);
		XxlJobLogger.log("cleanTestDataHandler end.clearInvoice:"+clearInvoice );

		//清除测试账期update  T_CUSTOMER_BILL_PERIOD_APPLY set COMPANY_ID = 6  where COMPANY_ID = 1 and
		//CREATOR in (select user_id from T_USER where USERNAME in ('Alina.huang','test','yolanda','Yolanda.guo'));
		int clearPERIOD_APPLY=saleorderMapper.clearPeriod(testUserNames);
		XxlJobLogger.log("cleanTestDataHandler end.clearPERIOD_APPLY:"+clearPERIOD_APPLY );
		//清除测试供应商余额update  T_CUSTOMER_BILL_PERIOD_APPLY set COMPANY_ID = 6  where COMPANY_ID = 1 and
		//CREATOR in (select user_id from T_USER where USERNAME in ('Alina.huang','test','yolanda','Yolanda.guo'));



		int clearReceiveInvoice=saleorderMapper.clearReceiveInvoice(testUserNames);
		XxlJobLogger.log("cleanTestDataHandler end.clearReceiveInvoice:"+clearReceiveInvoice );
		return SUCCESS;
	}
	
}
