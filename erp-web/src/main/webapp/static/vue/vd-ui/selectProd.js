Vue.component('ui-select-prod', {
    template: `<div class="ui-select-prod-wrap" ref="container" v-show="isShow">
        <div class="ui-select-prod-main">
            <div class="ui-select-prod-title">选择产品</div>
            <div class="ui-select-prod-close" @click="hide">
                <i class="vd-ui_icon icon-delete"></i>
            </div>
            <div class="ui-select-prod-cnt">
                <div ref="prodListTop">
                    <div class="ui-select-prod-options">
                        <div class="option-item" @click="resetSearch">
                            <i class="vd-ui_icon icon-rotate"></i>
                            <div class="item-txt">重置</div>
                        </div>
                    </div>
                    <div class="ui-select-prod-search">
                        <div class="search-input-wrap">
                            <ui-input width="240px" maxlength="200" placeholder="输入产品/品类/品牌" ref="searchInput" @blur="handlerSearchInputBlur" @focus="showSearchHistory" @enter="search" v-model="searchValue"></ui-input>
                            <div class="search-history-wrap" v-show="isShowSearchHistory">
                                <div class="history-list">
                                    <div class="history-item" @click="historySearch(item)" :title="item" v-for="(item, index) in searchHistoryList" :key="index">{{ item }}</div>
                                </div>
                                <div class="history-clear">
                                    <div class="clear-btn" @click="clearSearchHistory">删除历史</div>
                                </div>
                            </div>
                        </div>
                        <ui-button type="primary" @click="search">搜索</ui-button>
                        <ui-cascader 
                            class="margin" 
                            :data="sceneList" 
                            clearable 
                            width="240px"
                            ref="casePicker"
                            placeholder="科室方案选择"
                            v-if="sceneList && sceneList.length"
                            @change="handlerSceneChange"
                        ></ui-cascader>
                        <template v-if="searchKeyword">
                            <ui-checkbox :label="item.label" :checked.sync="item.checked" v-for="(item, index) in types" :key="index" @change="typeChange"><ui-checkbox>
                        </template>
                    </div>
                    <template v-if="searchKeyword || sceneNames">
                        <div class="ui-select-prod-filter">
                            <div class="ui-select-prod-filter-list" v-if="filterList && filterList.length">
                                <div class="filter-item" :class="{active: index === filterActiveIndex}" v-for="(item, index) in filterList" :key="index" @click="openFilter(index)">
                                    <div class="item-txt">{{ item.filterData.filterLabel }}</div>
                                    <i class="vd-ui_icon icon-down"></i>
                                </div>
                            </div>
                            <div class="ui-select-prod-filter-selected">
                                <div class="selected-keyword text-line-1" @click="filterReset('keyword')" v-if="searchKeyword" title="searchKeyword">搜索"{{ searchKeyword }}"</div>
                                <template v-if="sceneNames">
                                    <i class="vd-ui_icon icon-app-right" v-if="searchKeyword"></i>
                                    <div class="selected-keyword text-line-1" @click="filterReset('scene')" :title="sceneNames">{{ sceneNames }}</div>
                                </template>
                                <template v-if="searchFilterList && searchFilterList.length">
                                    <i class="vd-ui_icon icon-app-right"></i>
                                    <template v-for="(item, index) in searchFilterList">
                                        <div class="selected-item" @click="clearFilterItem(index)">
                                            <div class="item-txt text-line-1" :title="item.label">{{ item.filterData.filterLabel }}：{{ item.label }}</div>
                                            <i class="vd-ui_icon icon-delete"></i>
                                        </div>
                                    </template>
                                </template>
                            </div>
                        </div>
                        <div class="ui-select-prod-filter-drop" @click="hideFilterDrop" v-show="filterItemList && filterItemList.length">
                            <div class="filter-drop-cnt" @click.stop>
                                <div class="filter-drop-list">
                                    <div class="filter-drop-list-inner">
                                        <template v-for="(item, index) in filterItemList">
                                            <ui-checkbox :label="item.label" :checked.sync="item.checked" :key="index" v-if="filterItemMulti"></ui-checkbox> 
                                            <div class="filer-item-link" :key="index" v-else @click="filterSearch([item])">{{ item.label }}</div> 
                                        </template>
                                    </div>
                                </div>
                                <div class="filter-drop-btn" v-if="filterItemMulti">
                                    <ui-button size="small" type="primary" @click="filterSearchConfirm">确定</ui-button>
                                    <ui-button size="small" @click="hideFilterDrop">取消</ui-button>
                                </div>
                            </div>
                        </div>
                    </template>
                </div>
                <template v-if="searchKeyword || sceneNames">
                    <div class="ui-select-prod-list">
                        <table class="ui-select-prod-table">
                            <colgroup>
                                <col :width="item" v-for="(item, index) in colWidth" />
                            </colgroup>
                            <thead>
                                <tr class="ui-select-prod-tr">
                                    <th class="ui-select-prod-th" v-if="isMulti">
                                        <ui-checkbox :checked.sync="isSelectAll" @change="triggerSelectAll"></ui-checkbox> 
                                    </th>
                                    <th class="ui-select-prod-th">订货号</th>
                                    <th class="ui-select-prod-th">产品名称</th>
                                    <th class="ui-select-prod-th">产品等级</th>
                                    <th class="ui-select-prod-th">产品档位</th>
                                    <th class="ui-select-prod-th">
                                        <div class="th-inner" @click="sort('distributionPrice')">
                                            <div class="th-inner-txt">经销价</div>
                                            <div class="th-inner-sort" :class="[{'sort-up': sortColoum === 'distributionPrice' && sortAsc === 0}, {'sort-down': sortColoum === 'distributionPrice' && sortAsc === 1}]"></div>
                                        </div>
                                    </th>
                                    <th class="ui-select-prod-th">
                                        <div class="th-inner" @click="sort('terminalPrice')">
                                            <div class="th-inner-txt">终端价</div>
                                            <div class="th-inner-sort" :class="[{'sort-up': sortColoum === 'terminalPrice' && sortAsc === 0}, {'sort-down': sortColoum === 'terminalPrice' && sortAsc === 1}]"></div>
                                        </div>
                                    </th>
                                    <th class="ui-select-prod-th right">可用库存</th>
                                    <th class="ui-select-prod-th right">采购到货时长</th>
                                    <th class="ui-select-prod-th" v-if="!isMulti">操作</th>
                                </tr>
                            </thead>
                        </table>
                        <div class="ui-select-prod-body" ref="prodListBody" :class="{scrollY: isBodyScrollY}" :style="'max-height:' + bodyHeight + 'px;'">
                            <table class="ui-select-prod-table">
                                <colgroup>
                                    <col :width="item" v-for="(item, index) in colWidth" />
                                </colgroup>
                                <tbody>
                                    <template v-if="list.length">
                                        <tr class="ui-select-prod-tr" :class="{'on-select': index === trSelectedIndex}" v-for="(item, index) in list" :key="index" @click="trSelected(index)">
                                            <td class="ui-select-prod-td" v-if="isMulti">
                                                <template v-if="item.disabled">
                                                    <div @mouseenter="showDisabledTip" @mouseleave="isShowDisabledTip=false;">
                                                        <ui-checkbox :checked="false" :disabled="true"></ui-checkbox>
                                                    </div>
                                                </template>
                                                <template v-else>
                                                    <ui-checkbox :checked.sync="item.checked" @change="checkSelectedAll"></ui-checkbox>
                                                </template>
                                            </td>
                                            <td class="ui-select-prod-td">{{ item.skuNo }}</td>
                                            <td class="ui-select-prod-td">
                                                <a :href="'/goods/vgoods/viewSku.do?skuId=' + item.skuId" class="td-link text-line-1" :title="item.skuName" target="_blank">
                                                <template v-if="item.prodTags">
                                                    <span class="prod-tag" :class="'tag-' + tag.icon" :title="tag.name" v-for="(tag, tagIndex) in item.prodTags"></span>
                                                </template><span class="td-link-txt">{{ item.skuName }}</span></a>
                                            </td>
                                            <td class="ui-select-prod-td">{{ item.goodsLevel || '-' }}</td>
                                            <td class="ui-select-prod-td">{{ item.goodsPosition || '-' }}</td>
                                            <td class="ui-select-prod-td right red">
                                                <template v-if="item.distributionPrice || item.distributionPrice === 0">
                                                    <span class="fee-icon" title="不含运费" v-if="item.saleContainsFee == 2"></span>{{item.distributionPrice.toFixed(2)}}
                                                </template>
                                                <template v-else>-</template>
                                            </td>
                                            <td class="ui-select-prod-td right">{{ (item.terminalPrice || item.terminalPrice === 0) ? item.terminalPrice.toFixed(2) : '-'}}</td>
                                            <td class="ui-select-prod-td right">{{ (item.availableStock || item.availableStock === 0) ? item.availableStock : '-'}}</td>
                                            <td class="ui-select-prod-td right">{{ item.purchaseTime || item.purchaseTime === 0 ? item.purchaseTime + '工作日' : '-'}}</td>
                                            <td class="ui-select-prod-td" v-if="!isMulti">
                                                <div class="ui-select-td-option disabled" title="该产品已存在" v-if="item.disabled">选择</div>
                                                <div class="ui-select-td-option" @click="selectProd(item)" v-else>选择</div>
                                            </td>
                                        </tr>
                                    </template>
                                    <tr class="ui-select-prod-tr" v-else v-show="!isloading">
                                        <td colspan="9" class="prod-list-empty" :style="'padding-top:' + ((bodyHeight - 89) * 0.4) + 'px;'">
                                            <i class="vd-ui_icon icon-info2"></i>
                                            <div class="empty-txt">查询无结果！请尝试使用其他搜索条件。</div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="ui-page-wrap" v-if="!isloading">
                            <ui-pagination
                                :total="total"
                                :pageSize="pageSize"
                                :currentPage="pageNo"
                                @change="handlerPageChange"
                            ></ui-pagination>
                            <div class="ui-prod-footer-btns">
                                <ui-button type="primary" @click="multiSelectConfirm">确定</ui-button>
                                <ui-button @click="hide">取消</ui-button>
                            </div>
                        </div>
                    </div>
                </template>
                <div class="ui-prod-loading" v-show="isloading">
                    <i class="vd-ui_icon icon-loading"></i>
                </div>
            </div>
        </div>
        <div class="td-tip-wrap" v-show="isShowDisabledTip" :style="titlePosition">
            <div class="tip-cnt">该产品已存在</div>
        </div>
    </div>`,
    data() {
        return {
            isShow: false,
            types: [{
                label: '经销价不含运费',
                key: 'saleNotContainsFee',
                checked: false
            }, {
                label: '已核价',
                key: 'hasVerifiedPrice',
                checked: false
            }, {
                label: '已授权',
                key: 'hasAuthorized',
                checked: false
            }, {
                label: '有可用库存',
                key: 'hasStock',
                checked: false
            }, {
                label: '支持安装',
                key: 'isSupportInstallation',
                checked: false
            }],
            filterActiveIndex: -1,
            colWidth: ['71px', '', '68px', '80px', '90px', '90px', '68px', '92px', '50px'],
            isBodyScrollY: false,
            total: 0,
            bodyHeight: '',
            filterList: [],
            filterItemList: [],
            filterItemMulti: false,
            isloading: false,
            searchValue: '',
            list: [],
            prodIconParse: {
                '销量之王': 1,
                '新品': 2,
                '热销': 3,
                '同行热推': 4,
                '价格透明': 5,
                '现货供应': 6
            },
            //搜索筛选项的字段信息
            filterConfig: [{
                listKey: 'brandList', //接口返回的字段名
                filterLabel: '品牌', //渲染时的标签名
                filterId: 'brandId', //选择时用到的数据key
                filterName: 'brandName', //展示时用到的数据key
                filterSubmitKey: '',  //筛选提交时的字段key，为空时默认取filterId;该字段主要为了兼容渲染和提交时接口字段名不一致
                isMulti: true
            },
            {
                listKey: 'regionPriceList',
                filterLabel: '价位段',
                filterId: 'priceId',
                filterName: 'priceName',
            },
            {
                listKey: 'categoryList',
                filterLabel: '分类',
                filterId: 'categoryId',
                filterName: 'categoryName',
            },
            {
                listKey: 'attributeList', //属性/参数
                filterId: '',
                filterName: '',
                isMulti: true
            },
            {
                listKey: 'spuType',
                filterLabel: '类型',
                filterId: 'spuTypeId',
                filterName: 'spuTypeName',
                filterSubmitKey: 'spuType'
            },
            {
                listKey: 'institutionList',
                filterLabel: '机构',
                filterId: 'institutionId',
                filterName: 'institutionName',
                isMulti: true
            },
            {
                listKey: 'departmentList',
                filterLabel: '科室',
                filterId: 'departmentId',
                filterName: 'departmentName',
                isMulti: true
            },
            {
                listKey: 'brandNature',
                filterLabel: '进口/国产',
                filterId: 'brandNatureId',
                filterName: 'brandNatureName',
            },
            {
                listKey: 'goodsLevelList',
                filterLabel: '等级',
                filterId: 'goodsLevelId',
                filterName: 'goodsLevelName',
                isMulti: true
            },
            {
                listKey: 'goodsPositionList',
                filterLabel: '档位',
                filterId: 'goodsPositionId',
                filterName: 'goodsPositionName',
                isMulti: true
            }],
            trSelectedIndex: -1,
            //搜索参数
            searchKeyword: '',
            pageSize: 30,
            pageNo: 1,
            searchFilterList: [], //筛选的搜索参数
            sortColoum: '', //排序字段
            sortAsc: -1, //排序方式 0正序 1倒序
            searchHistoryList: [], //搜索历史
            isShowSearchHistory: false, //是否展示搜索历史
            isMulti: true, //是否支持多选
            isSelectAll: false,
            skuSelected: [],
            isShowDisabledTip: false,
            titlePosition: "",
            sceneList: [], //场景方案
            sceneIds: '',
            sceneNames: ''
        }
    },
    props: {
        searchUrl: {
            type: String,
            default: '/goodsInfo/search.do'
        }
    },
    computed: {

    },
    mounted() {
        let timeout = null;
        window.addEventListener('resize', () => {
            timeout && clearTimeout(timeout);

            setTimeout(() => {
                this.checkBodyHeight();
            }, 200)
        })
    },
    methods: {
        getSceneList() {
            axios.post('/sku/scene/getSceneAndCategory.do').then(({data}) => {
                if(data.code === 0) {
                    let sceneList = [];

                    data.data.forEach(item => {
                        let sceneItem = {
                            label: item.name,
                            value: item.id,
                            children: []
                        }

                        if(item.skuSceneCategoryDtoList && item.skuSceneCategoryDtoList.length) {
                            item.skuSceneCategoryDtoList.forEach(categoryItem => {
                                sceneItem.children.push({
                                    label: categoryItem.name,
                                    value: categoryItem.id,
                                })
                            })

                            sceneList.push(sceneItem);
                        }
                    })

                    this.sceneList = sceneList;
                }
            })
        },
        getList() {
            if(!this.searchKeyword && !this.sceneIds) {
                return;
            }

            this.isloading = true;

            let reqData = {
                pageSize: this.pageSize,
                pageNo: this.pageNo || 1,
                keywords: this.searchKeyword,
                skuSceneCategoryId: this.sceneIds
            }

            //固定筛选项 核价、授权、库存、安装
            this.types.forEach(item => {
                if (item.checked) {
                    reqData[item.key] = 1;
                }
            })

            //动态筛选项
            if (this.searchFilterList.length) {
                let filterReqData = this.getFilterReqData();
                reqData = Object.assign({}, reqData, filterReqData);
            }

            //排序
            if (this.sortColoum) {
                reqData.sortColoum = this.sortColoum;
                reqData.sortAsc = this.sortAsc;
            }

            axios.get(this.searchUrl, {
                params: reqData
            }).then(({ data }) => {
                this.isloading = false;

                if (data.code === 0) {
                    this.total = data.page.totalRecord;
                    let filterData = data.data || {};
                    let filterList = [];
                    this.isSelectAll = false;

                    this.filterConfig.forEach((item, i) => {
                        if (filterData[item.listKey] && filterData[item.listKey].length > 1) {
                            if (item.listKey === 'regionPriceList') {
                                filterData[item.listKey].forEach((dataItem, ii) => {
                                    filterData[item.listKey][ii].priceId = dataItem.minPrice + ',' + dataItem.maxPrice;
                                    if (dataItem.minPrice == -1) {
                                        filterData[item.listKey][ii].priceName = '未核价';
                                    } else if (dataItem.minPrice === dataItem.maxPrice) {
                                        filterData[item.listKey][ii].priceName = this.parsePrice(dataItem.minPrice);
                                    } else {
                                        filterData[item.listKey][ii].priceName = this.parsePrice(dataItem.minPrice) + ' - ' + this.parsePrice(dataItem.maxPrice);
                                    }
                                })
                            }

                            if (item.listKey !== 'attributeList') {
                                let flag = true;
                                this.searchFilterList.forEach(filterItem => {
                                    if (filterItem.filterData.filterLabel === item.filterLabel) {
                                        flag = false;
                                    }
                                })

                                if (flag) {
                                    filterList.push({
                                        data: filterData[item.listKey],
                                        filterData: item
                                    })
                                }
                            } else {
                                filterData[item.listKey].forEach((data, ii) => {
                                    let flag = true;

                                    this.searchFilterList.forEach(filterItem => {
                                        if (filterItem.filterData.filterLabel === data.attributeName) {
                                            flag = false;
                                        }
                                    })

                                    if (flag) {
                                        filterList.push({
                                            data: data.attributeValueList,
                                            filterData: {
                                                listKey: 'attributeList',
                                                filterLabel: data.attributeName,
                                                filterLabelId: data.attributeId,
                                                filterId: 'attributeValueId',
                                                filterName: 'attributeValueName',
                                                isMulti: true
                                            }
                                        })
                                    }
                                })
                            }
                        }
                    })

                    this.filterList = filterList;

                    console.log(this.filterList)

                    let list = data.listData || []
                    list.forEach(item => {
                        if (item.labelList && item.labelList.length) {
                            let tags = [];

                            item.labelList.forEach(label => {
                                if (this.prodIconParse[label]) {
                                    tags.push({
                                        name: label,
                                        icon: this.prodIconParse[label]
                                    });
                                }
                            })

                            item.prodTags = tags;
                        }

                        if (this.skuSelected.indexOf(item.skuNo) !== -1) {
                            item.disabled = true;
                        }

                        item.checked = false;
                    });

                    this.list = list;

                    this.checkBodyScroll();
                    this.checkBodyHeight();

                    this.trSelectedIndex = -1;
                } else {
                    this.$messgae.warn('系统繁忙，请稍后再试！');
                }
            })
        },
        getFilterReqData() {
            let filterReqData = {};

            let attrIds = [];
            let attrValues = [];

            this.searchFilterList.forEach(item => {
                let filterData = item.filterData;

                if (filterData.filterId === 'attributeValueId') {
                    attrIds.push(filterData.filterLabelId);
                    attrValues = attrValues.concat(item.value.split(','))
                } else if (filterData.filterId == 'priceId') {
                    let values = item.value.split(',');
                    filterReqData.minSalePrice = values[0];
                    filterReqData.maxSalePrice = values[1];
                } else {
                    filterReqData[filterData.filterId] = item.value;
                }
            })

            if (attrIds.length) {
                filterReqData.attributeId = attrIds.join(',');
                filterReqData.attributeValueId = attrValues.join(',');
            }

            return filterReqData;
        },
        parsePrice(price) {
            if (price >= 10000) {
                return (Math.round(price / 100) / 100).toFixed(2) + '万'
            } else {
                return price;
            }
        },
        typeChange() {
            this.pageNo = 1;
            this.getList();
        },
        openFilter(index) {
            if (this.filterActiveIndex == index) {
                this.hideFilterDrop();
            } else {
                this.filterActiveIndex = index;
                let filterItem = this.filterList[index];

                let filterData = filterItem.filterData;
                let filterItemList = [];

                filterItem.data.forEach(item => {
                    filterItemList.push({
                        label: item[filterData.filterName],
                        value: item[filterData.filterId],
                        checked: false
                    })
                })

                this.filterItemList = filterItemList;
                this.filterItemMulti = filterData.isMulti || false;
            }
        },
        hideFilterDrop() {
            this.filterActiveIndex = -1;
            this.filterItemList = [];
        },
        //如果列表超出滚动，需要加另外的样式让头和内容对齐
        checkBodyScroll() {
            this.$nextTick(() => {
                let scrollHeight = this.$refs.prodListBody.scrollHeight;
                let offsetHeight = this.$refs.prodListBody.offsetHeight;

                if (scrollHeight > offsetHeight + 10) {
                    this.isBodyScrollY = true;
                } else {
                    this.isBodyScrollY = false;
                }
            })
        },
        //重新计算列表的高度（resize或者页面结果变化）
        checkBodyHeight() {
            this.$nextTick(() => {
                let bottomHeight = 0;

                if (this.total > this.pageSize || this.isMulti) {
                    bottomHeight = 45;
                }

                this.bodyHeight = this.$refs.container.offsetHeight - this.$refs.prodListTop.offsetHeight - bottomHeight - 170;

            })
        },
        search() {
            let value = this.searchValue.trim();
            if (!value) {
                this.$message.warn('请输入关键词');
                return;
            }

            this.clearSearchData();

            this.searchKeyword = value;
            this.$refs.searchInput.blur();
            this.isShowSearchHistory = false;
            this.setHistory(value);
            this.getList();
        },
        historySearch(word) {
            this.searchValue = word;
            this.search();
        },
        filterSearchConfirm() {
            let filterSelected = [];

            this.filterItemList.forEach(item => {
                if (item.checked) {
                    filterSelected.push(item);
                }
            })

            if (!filterSelected.length) {
                this.hideFilterDrop();
            } else {
                this.filterSearch(filterSelected);
            }
        },
        filterSearch(selectedItems) {
            let filterItem = this.filterList[this.filterActiveIndex];
            let filterData = filterItem.filterData;

            let labels = [];
            let values = [];

            selectedItems.forEach(item => {
                labels.push(item.label);
                values.push(item.value);
            })

            this.searchFilterList.push({
                filterData,
                label: labels.join(','),
                value: values.join(',')
            })

            this.pageNo = 1;
            this.getList();

            this.hideFilterDrop();
        },
        clearFilterItem(index) {
            this.searchFilterList.splice(index, 1);
            this.pageNo = 1;
            this.getList();
        },
        //排序
        sort(key) {
            if (key === this.sortColoum) {
                if (this.sortAsc === -1) {
                    this.sortAsc = 1;
                } else if (this.sortAsc === 1) {
                    this.sortAsc = 0;
                } else {
                    this.sortColoum = "";
                    this.sortAsc = -1;
                }
            } else {
                this.sortColoum = key;
                this.sortAsc = 1;
            }

            this.pageNo = 1;
            this.getList();
        },
        handlerPageChange(num) {
            this.pageNo = num;
            this.getList();
        },
        filterReset(type) {
            if(type === 'keyword') {
                this.$refs.casePicker && this.$refs.casePicker.clearData();
            } else if(type === 'scene') {
                this.searchKeyword = "";
                this.searchValue = "";
            }

            this.clearSearchData();

            this.getList();
        },
        clearSearchData() {
            this.pageNo = 1;
            this.list = [];
            this.hideFilterDrop();
            this.filterList = [];
            this.sortColoum = '';
            this.sortAsc = -1;
            this.searchFilterList = [];
            this.types.forEach(item => {
                item.checked = false;
            })
        },
        resetSearch() {
            this.searchValue = "";
            this.searchKeyword = "";
            this.$refs.casePicker && this.$refs.casePicker.clearData();
            this.clearSearchData();

        },
        //列表单行点击选中
        trSelected(index) {
            this.trSelectedIndex = index;
        },
        selectProd(item) {
            this.$emit('select', [item.skuNo])
            this.hide();
        },
        show(params) {
            let config = params || {};
            this.isMulti = !!config.isMulti;
            this.colWidth = this.isMulti ? ['36px', '71px', '', '68px', '80px', '111px', '90px', '68px', '92px'] : ['71px', '', '68px', '80px', '111px', '90px', '68px', '92px', '50px'];

            this.skuSelected = config.skuNos || [];

            this.isShow = true;

            this.getSceneList();
        },
        hide() {
            this.resetSearch();
            this.isShow = false;
        },
        setHistory(word) {
            let historyList = JSON.parse(localStorage.getItem('select_prod_dialog_search') || '[]');

            historyList.forEach((item, index) => {
                if (item.toLowerCase() == word.toLowerCase()) {
                    historyList.splice(index, 1);
                }
            })

            historyList = [word].concat(historyList);

            historyList = historyList.splice(0, 10);

            localStorage.setItem('select_prod_dialog_search', JSON.stringify(historyList))
        },
        showSearchHistory() {
            let historyList = JSON.parse(localStorage.getItem('select_prod_dialog_search') || '[]');

            if (historyList.length) {
                this.searchHistoryList = historyList;
                this.isShowSearchHistory = true;
            }
        },
        handlerSearchInputBlur() {
            setTimeout(() => {
                this.isShowSearchHistory = false;
            }, 200)
        },
        clearSearchHistory() {
            this.searchHistoryList = [];
            this.isShowSearchHistory = false;
            localStorage.removeItem('select_prod_dialog_search');
        },
        checkSelectedAll() {
            let flag = true;
            this.list.forEach(item => {
                if (!item.checked) {
                    flag = false;
                }
            })

            this.isSelectAll = flag;
        },
        triggerSelectAll() {
            this.list.forEach(item => {
                if (!item.disabled) {
                    item.checked = this.isSelectAll;
                }
            })
        },
        multiSelectConfirm() {
            let skuNos = [];
            this.list.forEach(item => {
                if (item.checked) {
                    skuNos.push(item.skuNo);
                }
            })

            if (!skuNos.length) {
                this.$message.warn('请选择产品')
                return;
            }

            this.$emit('select', skuNos);
            this.hide();
        },
        showDisabledTip(e) {
            console.log(e.target.getBoundingClientRect())
            let position = e.target.getBoundingClientRect();

            this.titlePosition = `left:${position.left - 13}px;top:${position.top - 44}px;`;
            this.isShowDisabledTip = true;
        },
        handlerSceneChange(val) {
            if(val && val.length) {
                this.sceneIds = val[1].value;
                let sceneNames = [];

                val.forEach(item => {
                    sceneNames.push(item.label);
                })

                this.sceneNames = sceneNames.join('/');
            } else {
                this.sceneIds = '';
                this.sceneNames = '';
            }

            this.searchValue = this.searchKeyword;

            this.clearSearchData();
            this.getList();
        },
    }
})