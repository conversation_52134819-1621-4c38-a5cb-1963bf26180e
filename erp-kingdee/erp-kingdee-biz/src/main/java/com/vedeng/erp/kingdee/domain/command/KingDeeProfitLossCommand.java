package com.vedeng.erp.kingdee.domain.command;

import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @description: 盘亏单
 * @author: yana.jiang
 * @date: 2022/11/10
 */
@Data
@Getter
@Setter

public class KingDeeProfitLossCommand {

    /**
     * 单据内码0：表示新增 非0：云星空系统单据FID值，表示修改
     */
    private String fId;
    /**
     * 单据类型填单据类型编码，默认：PK01_SYS
     */
    private KingDeeNumberCommand fBillTypeId = new KingDeeNumberCommand();
    /**
     * fBillNo
     */
    private String fBillNo;
    /**
     * 库存组织写组织编码
     */
    private KingDeeNumberCommand fStockOrgId = new KingDeeNumberCommand();
    /**
     * 贝登单据头ID
     */
    private String F_QZOK_BDDJTID;
    /**
     * 货主类型
     */
    private String fOwnerTypeIdHead;
    /**
     * 单据日期
     */
    private String fDate;
    /**
     * fDeptId
     */
    private KingDeeNumberCommand fDeptId = new KingDeeNumberCommand();
    /**
     * fBillEntry
     */
    private List<KingDeeProfitLossDetailCommand> fBillEntry;

    @Data
    public static class KingDeeProfitLossDetailCommand {
        /**
         * fMaterialId
         */
        private KingDeeNumberCommand fMaterialId = new KingDeeNumberCommand();
        /**
         * fStockId
         */
        private KingDeeNumberCommand fStockId = new KingDeeNumberCommand();
        /**
         * fStockStatusId
         */
        private KingDeeNumberCommand fStockStatusId = new KingDeeNumberCommand();
        /**
         * 盘亏数量
         */
        private String fBaseLossQty;
        /**
         * 原始订单号
         */
        private String F_QZOK_YSDDH;
        /**
         * 归属业务单号
         */
        private String F_QZOK_GSYWDH;
        /**
         * 业务类型
         */
        private String F_QZOK_YWLX;
        /**
         * 批次号
         */
        private String F_QZOK_PCH;
        /**
         * 序列号
         */
        private String F_QZOK_XLH;
        /**
         * 授权类型
         */
        private String F_QZOK_SQLX;
        /**
         * 是否直发
         */
        private String F_QZOK_SFZF;
    }
}
