.page-main-header-wrap {
  position: fixed;
  top: 50px;
  left: 0;
  width: 100%;
  z-index: 11;
}
.page-main-header {
  height: 60px;
  display: flex;
  align-items: center;
  background: #fff;
  padding-left: 20px;
  min-width: 1300px;
}
.page-main-header .header-title {
  font-size: 20px;
  font-weight: 700;
  margin-right: 20px;
}
.page-main-header .order-status-tag {
  line-height: 27px;
  padding: 0 10px;
  border-radius: 3px;
  background: rgba(255, 237, 224, 0.97);
  color: #f60;
  font-size: 14px;
  margin-right: 20px;
}
.page-main-header .order-status-tag.tag-green {
  background: #E3F7E3;
  color: #13BF13;
}
.page-main-header .order-business-info {
  display: flex;
  align-items: center;
  flex: 1;
}
.page-main-header .order-business-info .business-info-item {
  display: flex;
  align-items: center;
  position: relative;
}
.page-main-header .order-business-info .business-info-item .info-label {
  color: #999;
}
.page-main-header .order-business-info .business-info-item .info-txt {
  color: #09f;
  cursor: pointer;
}
.page-main-header .order-business-info .business-info-item .info-txt:hover {
  color: #f60;
}
.page-main-header .header-link {
  color: #09f;
  cursor: pointer;
  margin-right: 20px;
  white-space: nowrap;
}
.page-main-header .header-link:hover {
  color: #f60;
}
.page-main-header .header-link .vd-ui_icon {
  font-size: 16px;
  vertical-align: -2px;
}
.page-main-header .header-options {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-left: 20px;
}
.page-main-header .header-options .options-btns {
  display: flex;
  align-items: center;
  white-space: nowrap;
}
.page-main-header .header-options .options-btns .vd-ui-button {
  margin-right: 10px;
}
.page-main-header .header-options .options-btns .vd-ui-button:last-child {
  margin-right: 20px;
}
.page-main-header .header-options .options-btns .vd-ui-button .icon-selected2 {
  color: #13BF13;
}
.page-main-header .header-options .options-icons {
  display: flex;
  align-items: center;
}
.page-main-header .header-options .options-icons .btn-icon {
  width: 64px;
  height: 60px;
  position: relative;
  cursor: pointer;
}
.page-main-header .header-options .options-icons .btn-icon::before,
.page-main-header .header-options .options-icons .btn-icon::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: 24px 24px;
  background-position: center;
  background-repeat: no-repeat;
}
.page-main-header .header-options .options-icons .btn-icon::after {
  opacity: 0;
}
.page-main-header .header-options .options-icons .btn-icon:hover::before {
  opacity: 0;
}
.page-main-header .header-options .options-icons .btn-icon:hover::after {
  opacity: 1;
}
.page-main-header .header-options .options-icons .btn-icon.btn-message::before {
  background-image: url(../../image/icon/msg.svg);
}
.page-main-header .header-options .options-icons .btn-icon.btn-message::after {
  background-image: url(../../image/icon/msg-active.svg);
}
.page-main-header .header-options .options-icons .btn-icon.btn-record::before {
  background-image: url(../../image/icon/record.svg);
}
.page-main-header .header-options .options-icons .btn-icon.btn-record::after {
  background-image: url(../../image/icon/record-active.svg);
}
.page-main-header .header-options .options-icons .btn-icon.btn-time::before {
  background-image: url(../../image/icon/time.svg);
}
.page-main-header .header-options .options-icons .btn-icon.btn-time::after {
  background-image: url(../../image/icon/time-active.svg);
}
.page-main-header .header-options .options-icons .btn-icon.btn-user::before {
  background-image: url(../../image/icon/user.svg);
}
.page-main-header .header-options .options-icons .btn-icon.btn-user::after {
  background-image: url(../../image/icon/user-active.svg);
}
.page-main-header .header-options .h-a-item-num {
  position: absolute;
  top: 8px;
  right: 20px;
  background: #e64545;
  border-radius: 10px;
  color: #fff;
  padding: 0 6px;
  z-index: 5;
  line-height: 20px;
  transform: translateX(50%);
  min-width: 20px;
  text-align: center;
}
.page-top-tip {
  background: #E0F3FF;
  display: flex;
  align-items: center;
  padding: 10px 15px;
}
.page-top-tip .vd-ui_icon {
  font-size: 16px;
  line-height: 1;
}
.page-top-tip .icon-info2 {
  margin-right: 10px;
  color: #09f;
}
.page-top-tip .icon-m-message {
  color: #f60;
}
.page-top-tip .tip-option {
  color: #09f;
  cursor: pointer;
}
.page-top-tip .tip-option:hover {
  color: #f60;
}
.quote-wrap {
  padding-top: 122px;
  padding: 121px 20px 60px 20px;
  margin: auto;
  min-width: 1260px;
}
.quote-wrap.no-tip {
  padding-top: 80px;
}
.quote-wrap .quote-content {
  background: #fff;
}
.quote-wrap .quote-content .options-wrap {
  display: flex;
  align-items: center;
  padding: 10px 20px 10px 10px;
  border: solid 1px #E1E5E8;
  border-bottom: 0;
}
.quote-wrap .quote-content .options-wrap.fixed {
  position: fixed;
  width: calc(100% - 40px);
  background: #fff;
  z-index: 50;
}
.quote-wrap .quote-content .options-wrap .options-l {
  flex: 1;
  display: flex;
  align-items: center;
}
.quote-wrap .quote-content .options-wrap .options-btns {
  display: flex;
  align-items: center;
}
.quote-wrap .quote-content .options-wrap .options-btns .vd-ui-button {
  margin-right: 10px;
}
.quote-wrap .quote-content .options-wrap .options-links {
  display: flex;
  align-items: center;
  margin-left: 10px;
}
.quote-wrap .quote-content .options-wrap .options-links .link-item {
  color: #09f;
  cursor: pointer;
  margin-right: 20px;
  position: relative;
}
.quote-wrap .quote-content .options-wrap .options-links .link-item::before {
  content: "";
  height: 14px;
  width: 1px;
  background: #E1E5E8;
  position: absolute;
  top: 3.5px;
  right: -10px;
}
.quote-wrap .quote-content .options-wrap .options-links .link-item:hover {
  color: #f60;
}
.quote-wrap .quote-content .options-wrap .options-links .link-item .icon-sms {
  font-size: 16px;
  vertical-align: -2px;
  margin-right: 5px;
  line-height: 1;
}
.quote-wrap .quote-content .options-wrap .options-links .link-item:last-child::before {
  display: none;
}
.quote-wrap .quote-content .options-wrap .selected-num {
  color: #999;
  position: relative;
}
.quote-wrap .quote-content .options-wrap .selected-num::before {
  content: "";
  height: 14px;
  width: 1px;
  background: #E1E5E8;
  position: absolute;
  top: 3.5px;
  left: -10px;
}
.quote-wrap .quote-content .options-wrap .filter-wrap {
  display: flex;
  align-items: center;
  margin-right: 40px;
  position: relative;
}
.quote-wrap .quote-content .options-wrap .filter-wrap::before {
  content: "";
  width: 1px;
  height: 14px;
  background: #e1e5e8;
  position: absolute;
  top: 4px;
  right: -20px;
}
.quote-wrap .quote-content .options-wrap .filter-wrap .vd-ui-custom-select-wrap {
  margin-right: 20px;
}
.quote-wrap .quote-content .options-wrap .filter-wrap .vd-ui-custom-select-wrap:last-child {
  margin-right: 0;
}
.quote-wrap .quote-content .options-wrap .filter-wrap .filter-checkbox-item {
  margin-right: 20px;
}
.quote-wrap .quote-content .options-wrap .filter-wrap .filter-checkbox-item .vd-ui-checkbox-item {
  color: #999;
}
.quote-wrap .quote-content .options-wrap .filter-wrap .filter-item {
  display: flex;
  align-items: center;
  color: #999;
  cursor: pointer;
}
.quote-wrap .quote-content .options-wrap .filter-wrap .filter-item:last-child {
  margin-right: 0;
}
.quote-wrap .quote-content .options-wrap .filter-wrap .filter-item:hover {
  color: #f60;
}
.quote-wrap .quote-content .options-wrap .filter-wrap .filter-item .vd-ui_icon {
  font-size: 16px;
  line-height: 1;
  margin-left: 5px;
}
.quote-wrap .quote-content .options-wrap .filter-wrap .filter-item .vd-ui_icon.active {
  color: #f60;
}
.quote-wrap .quote-content .options-wrap .price-total {
  line-height: 33px;
}
.quote-wrap .quote-content .options-wrap .price-total .price-label {
  color: #999;
}
.quote-wrap .quote-content .options-wrap .price-total .price-num {
  color: #e64545;
  font-weight: 700;
  margin-right: 5px;
}
.quote-wrap .quote-content .options-wrap-placeholder {
  height: 54px;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table-header {
  top: 1px;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-th {
  border: 1px solid #E1E5E8;
  padding: 8px 10px;
  font-size: 12px;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-th:first-child {
  background: #F5F7FA;
  padding-left: 10px;
  padding-right: 10px;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td {
  border: 1px solid #E1E5E8;
  padding: 10px 3px 10px 10px;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td:first-child {
  background: #F5F7FA;
  padding-left: 10px;
  padding-right: 10px;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td.num {
  padding-left: 0;
  padding-right: 0;
  text-align: center;
  background: #F5F7FA;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .td-remark-btn {
  height: 108px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  padding-right: 7px;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .td-remark-btn .btn-link {
  color: #09f;
  cursor: pointer;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .td-remark-btn .btn-link:hover {
  color: #f60;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .option-item {
  color: #09f;
  cursor: pointer;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .option-item:hover {
  color: #f60;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td.td-need-info {
  position: relative;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td.td-need-info .option-item-delete {
  position: absolute;
  bottom: 0;
  right: 0;
  font-size: 16px;
  line-height: 1;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  background: transparent;
  cursor: pointer;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td.td-need-info .option-item-delete:hover {
  background: #EBEFF2;
  color: #f60;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .td-remark-info {
  font-size: 12px;
  padding-right: 7px;
  position: relative;
  height: 108px;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .td-remark-info .remark-title {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .td-remark-info .remark-title .title-name {
  flex: 1;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .td-remark-info .remark-cnt {
  color: #999;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .td-remark-info .remark-options {
  display: flex;
  justify-content: space-between;
  position: absolute;
  bottom: 3px;
  left: 0;
  padding-right: 7px;
  width: 100%;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .td-remark-info .remark-options .remark-option-item {
  color: #09f;
  cursor: pointer;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .td-remark-info .remark-options .remark-option-item:hover {
  color: #f60;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td.prod-cnt {
  position: relative;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td.prod-cnt .prod-status-tag {
  position: absolute;
  top: 0;
  right: 0;
  background: rgba(255, 102, 0, 0.1);
  color: #f60;
  padding: 2px 4px;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt {
  padding-right: 7px;
  height: 108px;
  overflow: auto;
  font-size: 12px;
  word-break: break-all;
  word-wrap: break-word;
  line-break: anywhere;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .label-item {
  display: flex;
  margin-bottom: 4px;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .label-item.label-sku .link,
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .label-item.label-sku .txt {
  flex: none;
  margin-right: 5px;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .label-item:last-child {
  margin-bottom: 0;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .label-item .label {
  color: #999;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .label-item .label.highlight {
  color: #f60;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .label-item .label.label-tip {
  display: flex;
  align-items: center;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .label-item .label.label-tip .txt {
  margin-right: 5px;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .label-item .txt {
  flex: 1;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .label-item .txt.red {
  color: #e64545;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .label-item .txt.green {
  color: #13BF13;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .label-item .txt.orange {
  color: #FF6600;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .label-item .txt.blue {
  color: #09f;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .label-item .txt.report-txt {
  display: flex;
  align-items: center;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .label-item .txt.report-txt .txt-option {
  cursor: pointer;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .label-item .txt.report-txt:hover .txt-option {
  color: #f60;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .label-item .txt.report-txt .vd-ui_icon {
  line-height: 1;
  font-size: 16px;
  vertical-align: text-bottom;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .label-item .txt.report-txt .vd-ui_icon.icon-edit {
  margin-left: 5px;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .label-item .txt.report-txt .vd-ui_icon.icon-edit.disabled {
  color: #999;
  cursor: not-allowed;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .label-item .txt.report-txt .vd-ui_icon.icon-edit.disabled:hover {
  color: #999;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .label-item .txt.report-txt .txt-tip {
  margin-left: 10px;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .label-item .link {
  flex: 1;
  color: #09f;
  cursor: pointer;
  font-size: 12px;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .label-item .link:hover {
  color: #f60;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .label-item .user-list {
  flex: 1;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .label-item .user-item {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .label-item .user-item:last-child {
  margin-bottom: 0;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .label-item .user-item .user-avatar {
  width: 18px;
  height: 18px;
  border-radius: 2px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 5px;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .label-item .user-item .user-avatar img {
  max-width: 100%;
  max-height: 100%;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .label-item .user-item .user-name {
  flex: 1;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .text-item {
  display: flex;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .text-item .txt {
  flex: 1;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .edit-item {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .edit-item.can-edit {
  margin-bottom: 10px;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .edit-item:last-child {
  margin-bottom: 0;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .edit-item .label {
  color: #999;
  width: 65px;
  white-space: nowrap;
  text-align: right;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .edit-item .label.highlight {
  color: #f60;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .edit-item .content {
  flex: 1;
  display: flex;
  align-items: center;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .edit-item .content .unit {
  margin: 0 5px;
  flex: 1;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .edit-item .content .vd-ui-number-input {
  width: 92px;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .edit-item .icon-caution2 {
  font-size: 16px;
  color: #f60;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt.center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt.option {
  padding-right: 5px;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt.option .option-item {
  margin-right: 0;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt.option .option-item::before {
  display: none;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt.visible {
  overflow: visible;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .goods-info-wrap {
  display: flex;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .goods-info-wrap .goods-img {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  position: relative;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .goods-info-wrap .goods-img img {
  max-width: 100%;
  max-height: 100%;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .goods-info-wrap .goods-img .goods-tags {
  display: flex;
  align-items: center;
  position: absolute;
  top: 0;
  left: 0;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .goods-info-wrap .goods-img .goods-tags .tag-item {
  width: 16px;
  height: 16px;
  background-size: 100% 100%;
  margin-right: 3px;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .goods-info-wrap .goods-img .goods-tags .tag-item.tag-zeng {
  background-image: url(../../image/common/prod-tag-zeng.svg);
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .goods-info-wrap .goods-img .goods-tags .tag-item:last-child {
  margin-right: 0;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .goods-info-wrap .goods-info {
  flex: 1;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .vd-ui-select-link {
  margin-bottom: 5px;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .option-item {
  color: #09f;
  cursor: pointer;
  margin-bottom: 5px;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .option-item:last-child {
  margin-bottom: 0;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .option-item:hover {
  color: #f60;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .input-strong .vd-ui-input__inner {
  font-weight: 700;
  color: #e64545;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .value-change .vd-ui-input__inner {
  background: #FBEDB3;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .cnt-input-wrap {
  position: relative;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .cnt-input-wrap.focus .vd-ui-input__inner {
  border-color: #12B2B2;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .cnt-input-wrap .focus-tip {
  position: absolute;
  bottom: 26px;
  min-width: 100%;
  white-space: nowrap;
  background: #12B2B2;
  color: #fff;
  left: 0;
  font-size: 12px;
  line-height: 16px;
  padding: 0 2px;
  z-index: 3;
  border-radius: 2px;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .vd-ui-custom-placeholder.open .user-select-placeholder .icon-down {
  transform: rotate(180deg);
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .user-select-placeholder {
  display: flex;
  color: #09f;
  align-items: center;
  cursor: pointer;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .user-select-placeholder:hover {
  color: #f60;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .user-select-placeholder .icon-down {
  font-size: 16px;
  line-height: 1;
  margin-left: 5px;
  transition: transform 0.22s ease;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .user-select-placeholder .user-info {
  display: flex;
  align-items: center;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .user-select-placeholder .user-info .user-avatar {
  width: 18px;
  height: 18px;
  border-radius: 2px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 5px;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .quote-td-cnt .user-select-placeholder .user-info .user-avatar img {
  max-width: 100%;
  max-height: 100%;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr:hover .vd-ui-td {
  background: #F5F7FA;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr.on-select .vd-ui-td {
  background: #F0F9FF;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr.on-select .vd-ui-td:last-child {
  z-index: 4;
}
.quote-wrap .quote-content .vd-ui-table-wrap .vd-ui-table .vd-ui-tr:first-child .quote-td-cnt .cnt-input-wrap .focus-tip {
  bottom: auto;
  top: 26px;
}
.multi-add-wrap {
  display: flex;
}
.multi-add-wrap .multi-add-l {
  width: 180px;
  margin-right: 20px;
}
.multi-add-wrap .multi-add-r {
  flex: 1;
}
.multi-add-wrap .multi-add-r .multi-add-tips {
  color: #999;
  margin-bottom: 15px;
  line-height: 24px;
}
.multi-add-wrap .multi-add-r .multi-error-wrap {
  display: flex;
  color: #e64545;
  word-break: break-all;
}
.multi-add-wrap .multi-add-r .multi-error-wrap .icon-error2 {
  font-size: 16px;
  line-height: 1;
  margin-right: 5px;
  margin-top: 3px;
}
.tmpl-download-wrap {
  display: flex;
  margin-top: 20px;
  align-items: center;
}
.tmpl-download-wrap .tmpl-icon {
  width: 13px;
  height: 16px;
  margin-right: 5px;
}
.tmpl-download-wrap .tmpl-icon img {
  vertical-align: -1px;
  width: 100%;
  height: 100%;
}
.tmpl-download-wrap .tmpl-link {
  color: #09f;
  margin-left: 10px;
  cursor: pointer;
}
.tmpl-download-wrap .tmpl-link:hover {
  color: #f60;
}
.import-dialog .vd-ui-dialog--in .vd-ui-dialog_content {
  overflow: visible;
}
.dlg-consultation-users {
  overflow: hidden;
}
.dlg-consultation-users .user-list {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: -5px;
  margin-right: -5px;
}
.dlg-consultation-users .user-list .user-item {
  display: flex;
  margin-right: 5px;
  align-items: center;
  padding: 0 10px 0 5px;
  height: 33px;
  background: #F5F7FA;
  border-radius: 3px;
  margin-bottom: 5px;
}
.dlg-consultation-users .user-list .user-item .item-avatar {
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
  overflow: hidden;
  margin-right: 5px;
}
.dlg-consultation-users .user-list .user-item .item-avatar img {
  max-width: 100%;
  max-height: 100%;
}
.ui-popup-message-box .msg-button-choice .vd-button.vd-button-link {
  background: none;
  color: #09f;
  padding: 0;
  border: 0;
  height: auto;
  line-height: 1.5;
  margin-right: 10px;
  display: flex;
  align-items: center;
}
.ui-popup-message-box .msg-button-choice .vd-button.vd-button-link:hover {
  color: #f60;
}
.share-dlg-wrap .share-info .info-item {
  display: flex;
  margin-bottom: 15px;
}
.share-dlg-wrap .share-info .info-item .info-label {
  color: #999;
  width: 140px;
  text-align: right;
  margin-right: 10px;
}
.share-dlg-wrap .share-info .info-item .price {
  color: #e64545;
  font-weight: 700;
}
.share-dlg-wrap .share-info .info-item:last-child {
  margin-bottom: 0;
}
.share-dlg-wrap .share-msg {
  padding: 10px;
  background: #F5F7FA;
  border-radius: 3px;
  margin-top: 20px;
  margin-bottom: 20px;
}
.share-dlg-wrap .share-msg .msg-link {
  color: #09f;
}
.share-dlg-wrap .share-msg .msg-link:hover {
  color: #f60;
}
.share-dlg-wrap .share-footer {
  display: flex;
  align-items: center;
}
.share-dlg-wrap .share-footer .footer-tip {
  color: #f60;
  flex: 1;
}
.share-dlg-wrap .share-footer .footer-tip .icon-info2 {
  font-size: 16px;
  line-height: 1;
  margin-right: 5px;
  vertical-align: -2px;
}
.share-dlg-wrap .share-footer .footer-options {
  display: flex;
  align-items: center;
}
.share-dlg-wrap .share-footer .footer-options .footer-link {
  color: #09f;
  margin-right: 20px;
}
.share-dlg-wrap .share-footer .footer-options .footer-link:hover {
  color: #f60;
}
.biz-dlg-info-wrap .dlg-info-block {
  padding-bottom: 20px;
  border-bottom: solid 1px #E1E5E8;
  margin-bottom: 20px;
}
.biz-dlg-info-wrap .dlg-info-block:last-child {
  border-bottom: 0;
  margin-bottom: 0;
  padding-bottom: 0;
}
.biz-dlg-info-wrap .dlg-info-block .dlg-info-title {
  font-weight: 700;
  margin-bottom: 20px;
}
.biz-dlg-info-wrap .dlg-info-block .dlg-info-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}
.biz-dlg-info-wrap .dlg-info-block .dlg-info-item:last-child {
  margin-bottom: 10px;
}
.biz-dlg-info-wrap .dlg-info-block .dlg-info-item .info-label {
  font-size: 14px;
  color: #999;
  width: 140px;
  margin-right: 10px;
  text-align: right;
}
.biz-dlg-info-wrap .dlg-info-block .dlg-info-item .info-txt {
  flex: 1;
}
.req-des-wrap .vd-ui-textarea-place {
  vertical-align: top;
}
.req-des-wrap .dlg-form-footer {
  margin-top: 19px;
}
.req-des-wrap .dlg-form-footer .vd-ui-button {
  margin-right: 10px;
}
.custom-add-dlg-wrap .vd-ui-textarea-place {
  vertical-align: top;
}
.custom-add-dlg-wrap .custom-add-form {
  max-height: 471px;
  overflow-y: auto;
  overscroll-behavior: contain;
}
.custom-add-dlg-wrap .custom-add-form::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.custom-add-dlg-wrap .custom-add-form::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.custom-add-dlg-wrap .custom-add-form::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.custom-add-dlg-wrap .custom-add-form::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.custom-add-dlg-wrap .custom-add-form::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.custom-add-dlg-wrap .dlg-form-footer {
  padding-left: 210px;
  margin-top: 20px;
}
.custom-add-dlg-wrap .dlg-form-footer .vd-ui-button {
  margin-right: 10px;
}
.remark-dialog-info-wrap .remark-info-options {
  margin-bottom: 20px;
}
.remark-dialog-info-wrap .remark-info-list {
  max-height: 512px;
  overflow-y: auto;
  padding-right: 11px;
  padding-left: 21px;
}
.remark-dialog-info-wrap .remark-info-list::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.remark-dialog-info-wrap .remark-info-list::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.remark-dialog-info-wrap .remark-info-list::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.remark-dialog-info-wrap .remark-info-list::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.remark-dialog-info-wrap .remark-info-list::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.remark-dialog-info-wrap .remark-info-list .remark-info-item {
  padding-bottom: 20px;
  margin-bottom: 20px;
  border-bottom: 1px solid #e1e5e8;
  position: relative;
}
.remark-dialog-info-wrap .remark-info-list .remark-info-item:before {
  content: "";
  width: 11px;
  height: 11px;
  border-radius: 50%;
  background: #09f;
  position: absolute;
  top: 5px;
  left: -21px;
}
.remark-dialog-info-wrap .remark-info-list .remark-info-item:after {
  content: "";
  width: 1px;
  height: calc(100% + 9px);
  border-left: 1px dashed #e1e5e8;
  position: absolute;
  top: 16px;
  left: -16px;
}
.remark-dialog-info-wrap .remark-info-list .remark-info-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: 0;
}
.remark-dialog-info-wrap .remark-info-list .remark-info-item:last-child::after {
  display: none;
}
.remark-dialog-info-wrap .remark-info-list .remark-info-item .remark-item-t {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}
.remark-dialog-info-wrap .remark-info-list .remark-info-item .remark-item-t .remark-user {
  display: flex;
  align-items: center;
}
.remark-dialog-info-wrap .remark-info-list .remark-info-item .remark-item-t .remark-user .user-avatar {
  width: 20px;
  height: 20px;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 5px;
}
.remark-dialog-info-wrap .remark-info-list .remark-info-item .remark-item-t .remark-user .user-avatar img {
  max-width: 100%;
  max-height: 100%;
}
.remark-dialog-info-wrap .remark-info-list .remark-info-item .remark-item-t .remark-time {
  color: #999;
}
.form-last-radio .form-item:last-child .vd-ui-radio-group {
  margin-bottom: 0;
}
.form-last-radio .form-item:last-child .vd-ui-radio-group .vd-ui-radio-item {
  margin-bottom: 0;
}
.price-history-wrap {
  background: #fff;
  border-radius: 3px;
  box-shadow: rgba(0, 0, 0, 0.12) 0px 3px 6px;
  padding: 10px;
  width: 350px;
  display: none;
  z-index: 8888;
}
.price-history-wrap.show {
  display: block;
}
.price-history-wrap::before {
  content: "";
  width: 0;
  height: 0;
  border: 5px solid transparent;
  border-right-color: #fff;
  left: -10px;
  top: 10px;
  position: absolute;
}
.price-history-wrap .price-history-table .price-history-tbody {
  max-height: 320px;
  overflow: auto;
}
.price-history-wrap .price-history-table .price-history-tbody::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.price-history-wrap .price-history-table .price-history-tbody::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.price-history-wrap .price-history-table .price-history-tbody::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.price-history-wrap .price-history-table .price-history-tbody::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.price-history-wrap .price-history-table .price-history-tbody::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.price-history-wrap .price-history-table .price-history-tbody.scroll {
  margin-right: -6px;
}
.price-history-wrap .price-history-table .price-history-tr {
  display: flex;
}
.price-history-wrap .price-history-table .price-history-tr .price-history-th,
.price-history-wrap .price-history-table .price-history-tr .price-history-td {
  margin-right: -1px;
}
.price-history-wrap .price-history-table .price-history-tr .price-history-th:nth-child(1),
.price-history-wrap .price-history-table .price-history-tr .price-history-td:nth-child(1) {
  width: 130px;
}
.price-history-wrap .price-history-table .price-history-tr .price-history-th:nth-child(2),
.price-history-wrap .price-history-table .price-history-tr .price-history-td:nth-child(2) {
  width: 100px;
}
.price-history-wrap .price-history-table .price-history-tr .price-history-th:nth-child(3),
.price-history-wrap .price-history-table .price-history-tr .price-history-td:nth-child(3) {
  flex: 1;
  margin-right: 0;
}
.price-history-wrap .price-history-table .price-history-tr .price-history-th {
  background: #F5F7FA;
  padding: 5px 10px;
  border: 1px solid #E1E5E8;
  color: #999;
}
.price-history-wrap .price-history-table .price-history-tr .price-history-td {
  padding: 5px 10px;
  border: 1px solid transparent;
  border-bottom: 1px solid #e1e5e8;
}
.price-history-wrap .price-history-table .price-history-tr .price-history-td:nth-child(1) {
  border-left: 1px solid #e1e5e8;
}
.price-history-wrap .price-history-table .price-history-tr .price-history-td:nth-child(3) {
  border-right: 1px solid #e1e5e8;
}
.price-history-wrap .price-history-table .price-history-tr .option-user-info {
  display: flex;
  align-items: center;
}
.price-history-wrap .price-history-table .price-history-tr .option-user-info .user-avatar {
  width: 20px;
  height: 20px;
  border-radius: 3px;
  overflow: hidden;
  margin-right: 5px;
}
.price-history-wrap .price-history-table .price-history-tr .option-user-info .user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  vertical-align: top;
}
.price-history-wrap .price-history-table .price-history-tr .option-user-info .user-name {
  flex: 1;
}
.price-history-wrap .price-history-table .price-history-tr .txt-right {
  text-align: right;
}
.price-history-wrap .price-history-table .price-history-tr .price-txt {
  color: #E64545;
}
.price-history-wrap .price-history-table .price-history-tr .txt-grey {
  color: #999;
}
.price-history-wrap .price-history-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 0;
  border: solid 1px #E1E5E8;
  margin-top: -1px;
  color: #666;
}
@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.price-history-wrap .price-history-loading .icon-loading {
  font-size: 16px;
  color: #09f;
  margin-right: 5px;
  line-height: 1;
  animation: loading 2s linear infinite;
}
.price-history-wrap .price-history-empty {
  text-align: center;
  padding: 10px;
  border: solid 1px #E1E5E8;
  margin-top: -1px;
  color: #999;
}
.option-refresh-tip-wrap {
  position: fixed;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99999;
}
.option-refresh-tip-wrap .option-refresh-tip-cnt {
  background: #fff;
  border-radius: 5px;
  box-shadow: rgba(0, 0, 0, 0.08) 0px 7px 14px;
  padding: 60px 40px 40px 40px;
  text-align: center;
}
.option-refresh-tip-wrap .option-refresh-tip-cnt .option-tip-icon {
  width: 48px;
  height: 48px;
  background-image: url(../../image/option-tip-icon.svg);
  background-size: 100% 100%;
  display: inline-block;
  vertical-align: top;
  margin-bottom: 30px;
}
.option-refresh-tip-wrap .option-refresh-tip-cnt .option-tip-txt {
  margin-bottom: 20px;
}
.option-refresh-tip-wrap .option-refresh-tip-cnt .vd-ui-button {
  border-radius: 18px;
  padding: 6px 50px;
}
.group-user-list .group-user-item-wrap {
  display: flex;
  align-items: center;
}
.group-user-list .group-user-item-wrap .user-avatar {
  width: 20px;
  height: 20px;
  border-radius: 3px;
  overflow: hidden;
  margin-right: 5px;
}
.group-user-list .group-user-item-wrap .user-avatar img {
  width: 20px;
  height: 20px;
  object-fit: cover;
  vertical-align: top;
}
.report-radio-wrap .vd-ui-radio-group {
  display: flex;
  align-items: center;
}
.report-radio-wrap .vd-ui-radio-group .vd-ui-radio-item {
  margin-right: 9px;
}
.report-radio-wrap .vd-ui-radio-group .vd-ui-radio-item:last-child {
  margin-right: 0;
}
.report-radio-wrap .vd-ui-radio-group .vd-ui-radio-item .vd-ui-radio-icon {
  margin-top: 3px;
}
.report-reason-wrap .vd-ui-textarea .vd-ui-textarea__inner {
  font-size: 12px;
}
.report-reason-wrap .vd-ui-textarea .vd-ui-textarea__inner::placeholder {
  font-size: 12px;
}
.goods-tip-info {
  min-width: 200px;
}
.goods-tip-info .goods-tip-item {
  display: flex;
  margin-bottom: 5px;
}
.goods-tip-info .goods-tip-item .item-label {
  color: #999;
}
.goods-tip-info .goods-tip-item .item-txt {
  flex: 1;
}
.goods-tip-info .goods-tip-item:last-child {
  margin-bottom: 0;
}
.goods-tip-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100px;
}
@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.goods-tip-loading .icon-loading {
  font-size: 16px;
  color: #09f;
  margin-right: 5px;
  line-height: 1;
  animation: loading 2s linear infinite;
}
.order-type .red {
  color: #e64545;
}
.create-chat-tip {
  line-height: 30px;
  margin-right: 10px;
  color: #e64545;
}
.create-user-status {
  margin-right: 10px;
  display: flex;
  align-items: center;
}
