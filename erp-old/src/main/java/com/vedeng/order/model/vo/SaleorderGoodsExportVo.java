package com.vedeng.order.model.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class SaleorderGoodsExportVo {

    //销售单号
    @ExcelProperty("订单号")
    private String saleorderNo;

    //申请人名称
    @ExcelProperty("申请人")
    private String applicantName;

    //销售部门
    @ExcelProperty("销售部门")
    private String salesDeptName;

    //可发货时间
    @ExcelProperty("可采购时间")
    private String satisfyDeliveryTimeStr;

    //订货号
    @ExcelProperty("订货号")
    private String sku;

    //产品名称
    @ExcelProperty("产品名称")
    private String skuName;

    //产品经理
    @ExcelProperty("归属产品经理")
    private String assignmentManagerId;

    //产品助理
    @ExcelProperty("归属产品助理")
    private  String assignmentAssistantId;

    //库存数量
    @ExcelIgnore
    private Integer goodsStock;

    //可用库存数量
    @ExcelIgnore
    private Integer canUseGoodsStock;

    //在途数量
    @ExcelProperty("在途数量")
    private Integer onWayNum;

    //是否直发
    @ExcelProperty("是否直发")
    private String deliveryDirect;

    @ExcelProperty("可用库存/库存量")
    private String stockValue;

    //商品总数
    @ExcelProperty("商品总数")
    private Integer num;

    //订单助理
    @ExcelIgnore
    private String orderAssitId;

    //内部备注
    @ExcelProperty("内部备注")
    private String insideComments;

    //物流备注
    @ExcelProperty("物流备注")
    private String logisticsComments;

    //采购类型
    @ExcelProperty("采购类型")
    private String purchaseType;

    //采购原因
    @ExcelProperty("原因")
    private String buyProcessModReson;

    //采购时间
    @ExcelProperty("原因填写时间")
    private String buyProcessModTime;

}
