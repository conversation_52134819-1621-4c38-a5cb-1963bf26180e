<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
            + path + "/";
%>
<c:set var="path" value="<%=basePath%>" scope="application"/>
<link rel="stylesheet" href="<%=basePath%>static/css/general.css?rnd=${resourceVersionKey}"/>
<link rel="stylesheet" href="<%=basePath%>static/css/manage.css"/>

<div id="app">
    <el-form label-position="right" label-width="80px" :rules="topRules" class="topForm">
        <el-form-item label="客户名称">
            <span>{{this.communicateAiSummaryDto.traderName}}</span>
        </el-form-item>
        <el-form-item label="归属销售">
            <span>{{this.communicateAiSummaryDto.saleUserName}}</span>
        </el-form-item>
        <el-form-item label="联系人" prop="traderContactName">
            <span>{{this.communicateAiSummaryDto.traderContactName}}</span>
        </el-form-item>
        <el-form-item label="沟通时间" prop="communicateTimeRange">
            <span>{{this.communicateAiSummaryDto.communicateTimeRange}}</span>
        </el-form-item>
    </el-form>

    <el-row class="customer-title">沟通内容摘要 <span style="color: red">（AI提取）</span></el-row>

    <el-form label-position="right" label-width="120px" :model="communicateAiSummaryDto" class="form-container">
        <el-form-item label="客户意图：">
            <el-input v-model="communicateAiSummaryDto.customerIntentions" :maxlength="500" style="width: 50%"></el-input>
        </el-form-item>
        <el-form-item label="客户类型：">
            <el-select v-model="communicateAiSummaryDto.customerTypes" placeholder="">
                <el-option label="终端" value="终端"></el-option>
                <el-option label="经销商" value="经销商"></el-option>
                <el-option label="不明确" value="不明确"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="是否有意向：">
            <el-select v-model="communicateAiSummaryDto.isIntention" placeholder="">
                <el-option label="有意向" value="是"></el-option>
                <el-option label="无意向" value="否"></el-option>
                <el-option label="不明确" value="不明确"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="是否加微信：">
            <el-select v-model="communicateAiSummaryDto.isAddWechat" placeholder="">
                <el-option label="客户提供了微信号" value="是"></el-option>
                <el-option label="客户未提供微信号" value="否"></el-option>
                <el-option label="不明确" value="不明确"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="沟通是否有效：">
            <el-select v-model="communicateAiSummaryDto.isEffectiveCommunication" placeholder="">
                <el-option label="有效沟通" value="是"></el-option>
                <el-option label="无效沟通" value="否"></el-option>
                <el-option label="不明确" value="不明确"></el-option>
            </el-select>
        </el-form-item>

        <el-row class="form-item-container" v-for="(item, index) in communicateAiSummaryDto.goodsList">
            <el-form-item :label="'意向商品' + Number(index + 1)" class="inline-form-item">
                <el-input v-model="item.intentionGoods" style="width: 220px" :maxlength="200"></el-input>
            </el-form-item>
            <el-form-item :label="'品牌' + Number(index + 1)" class="inline-form-item">
                <el-input v-model="item.brands" style="width: 220px" :maxlength="200"></el-input>
            </el-form-item>
            <el-form-item :label="'型号' + Number(index + 1)" class="inline-form-item">
                <el-input v-model="item.models" style="width: 220px" :maxlength="200"></el-input>
            </el-form-item>
            <el-button size="mini" icon="el-icon-remove-outline" @click="deleteLine(index)"
                       style="height: 44px;width: 44px; font-size: 30px; padding-left: 6px; padding-top: 4px; border: none"></el-button>
            <el-button v-if="index === communicateAiSummaryDto.goodsList.length - 1" size="mini" icon="el-icon-circle-plus-outline"
                       style="height: 44px;width: 44px; font-size: 30px; padding-left: 6px; padding-top: 4px; border: none" @click="addLine()"></el-button>
        </el-row>
        <el-form-item style="margin-left: 35%">
            <el-button type="primary" @click="submitForm()">提交</el-button>
        </el-form-item>
    </el-form>
</div>
<script src="${pageContext.request.contextPath}/static/api/trader/traderCustomer.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript">
    const communicateSummaryId = '${communicateSummaryId}';

    new Vue({
        el: '#app',
        data() {
            return {
                topRules: {
                    traderContactName: [
                        { required: true}
                    ],
                    communicateTimeRange: [
                        { required: true}
                    ]
                },

                communicateAiSummaryDto: {
                    customerIntentions: '',
                    intentionGoods: '',
                    brands: '',
                    models: '',
                    customerTypes: '',
                    isIntention: '',
                    isAddWechat: '',
                    isEffectiveCommunication: '',
                    traderName: '',
                    saleUserName: '',
                    traderContactName: '',
                    communicateTimeRange: '',
                    goodsList: []
                }
            }
        },

        mounted() {
            loadingApp()
        },

        created() {
            this.initPageData();
        },

        methods: {
            initPageData() {
                getByCommunicateSummaryId({"communicateSummaryId" : communicateSummaryId}).then(res => {
                    this.communicateAiSummaryDto = res.data.data;
                    if (this.communicateAiSummaryDto.goodsList.length == 0) {
                        let good = {
                            "intentionGoods" : this.communicateAiSummaryDto.intentionGoods,
                            "brands" : this.communicateAiSummaryDto.brands,
                            "models" : this.communicateAiSummaryDto.models
                        };
                        this.communicateAiSummaryDto.goodsList.push(good);
                    }

                })
            },

            addLine() {
                let index = Number(this.communicateAiSummaryDto.goodsList.length);
                if (index < 10) {
                    let good = {
                        "intentionGoods" : "",
                        "brands" : "",
                        "models" : ""
                    };
                    this.communicateAiSummaryDto.goodsList.push(good);
                } else {
                    this.$message.error('最多添加10组商品数据');
                }
            },

            deleteLine(index) {
                let lineNum = Number(this.communicateAiSummaryDto.goodsList.length);
                if (lineNum == 1) {
                    this.$message.error('至少保留一组商品数据');
                } else {
                    this.communicateAiSummaryDto.goodsList.splice(index, 1);
                }
            },

            // 提交保存
            submitForm() {
                saveModifySummary(this.communicateAiSummaryDto).then(res => {
                    this.$message({
                        message: '修正成功',
                        type: 'success',
                        duration: 2000,
                        onClose: () => {
                            var index = parent.layer.getFrameIndex(window.name);
                            parent.layer.close(index);
                        }
                    });
                })
            }
        }
    })
</script>

<style>
    .customer-title {
        margin-left: 40px;
        margin-top: 30px;
        font-size: 16px;
        font-weight: 700;
    }

    .topForm .el-form-item {
        margin-bottom: 0;
        margin-left: 100px;
    }

    .el-select i {
        background: none !important;
    }

    .el-button i {
        background: none !important;
    }

    .form-container {
        margin-top: 10px;
        margin-left: 100px;
    }

    .inline-form-item {
        margin-left: 0;
        margin-right: 20px;
    }

    .form-item-container {
        display: flex;
        flex-wrap: wrap;
    }
</style>
