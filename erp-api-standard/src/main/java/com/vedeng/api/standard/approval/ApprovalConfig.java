package com.vedeng.api.standard.approval;

import com.vedeng.api.standard.converter.ResponseMappingConfig;
import com.vedeng.api.standard.template.ParameterConfig;
import java.util.function.Function;

/**
 * 审核配置类
 * 封装审核执行所需的所有配置信息
 *
 * @param <T> 请求类型
 * @param <R> 响应类型
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-26
 */
public class ApprovalConfig<T extends ApprovalRequest, R> {

    private Class<T> requestType;
    private Class<R> responseType;
    private String controllerBeanName;
    private String controllerMethodName;
    private Class<?>[] validationRules;
    private ResponseMappingConfig responseConfig;
    private Function<T, ParameterConfig[]> parameterConfigFunction;
    private boolean useHttpParameters = true; // 默认使用HTTP参数（包含HttpServletRequest）
    
    private ApprovalConfig(Builder<T, R> builder) {
        this.requestType = builder.requestType;
        this.responseType = builder.responseType;
        this.controllerBeanName = builder.controllerBeanName;
        this.controllerMethodName = builder.controllerMethodName;
        this.validationRules = builder.validationRules;
        this.responseConfig = builder.responseConfig;
        this.parameterConfigFunction = builder.parameterConfigFunction;
        this.useHttpParameters = builder.useHttpParameters;
    }
    
    public static <T extends ApprovalRequest, R> Builder<T, R> builder() {
        return new Builder<>();
    }
    
    // Getters
    public Class<T> getRequestType() { return requestType; }
    public Class<R> getResponseType() { return responseType; }
    public String getControllerBeanName() { return controllerBeanName; }
    public String getControllerMethodName() { return controllerMethodName; }
    public Class<?>[] getValidationRules() { return validationRules; }
    public ResponseMappingConfig getResponseConfig() { return responseConfig; }
    public Function<T, ParameterConfig[]> getParameterConfigFunction() { return parameterConfigFunction; }
    public boolean isUseHttpParameters() { return useHttpParameters; }
    
    /**
     * 构建器类
     */
    public static class Builder<T extends ApprovalRequest, R> {
        private Class<T> requestType;
        private Class<R> responseType;
        private String controllerBeanName;
        private String controllerMethodName;
        private Class<?>[] validationRules = new Class<?>[0];
        private ResponseMappingConfig responseConfig;
        private Function<T, ParameterConfig[]> parameterConfigFunction;
        private boolean useHttpParameters = true; // 默认使用HTTP参数
        
        public Builder<T, R> requestType(Class<T> requestType) {
            this.requestType = requestType;
            return this;
        }
        
        public Builder<T, R> responseType(Class<R> responseType) {
            this.responseType = responseType;
            return this;
        }
        
        public Builder<T, R> controller(String controllerBeanName, String controllerMethodName) {
            this.controllerBeanName = controllerBeanName;
            this.controllerMethodName = controllerMethodName;
            return this;
        }
        
        public Builder<T, R> validationRules(Class<?>... validationRules) {
            this.validationRules = validationRules;
            return this;
        }
        
        public Builder<T, R> responseConfig(ResponseMappingConfig responseConfig) {
            this.responseConfig = responseConfig;
            return this;
        }

        /**
         * 配置Controller方法参数（包含HttpServletRequest）
         *
         * @param parameterConfigFunction 参数配置函数，将审核请求对象转换为Controller方法参数
         * @return 当前构建器
         */
        public Builder<T, R> withHttpParameters(Function<T, ParameterConfig[]> parameterConfigFunction) {
            this.parameterConfigFunction = parameterConfigFunction;
            this.useHttpParameters = true;
            return this;
        }

        /**
         * 配置Controller方法参数（不包含HttpServletRequest）
         *
         * @param parameterConfigFunction 参数配置函数，将审核请求对象转换为Controller方法参数
         * @return 当前构建器
         */
        public Builder<T, R> withoutHttpParameters(Function<T, ParameterConfig[]> parameterConfigFunction) {
            this.parameterConfigFunction = parameterConfigFunction;
            this.useHttpParameters = false;
            return this;
        }

        /**
         * 配置Controller方法参数（向后兼容方法，默认使用HTTP参数）
         *
         * @param parameterConfigFunction 参数配置函数，将审核请求对象转换为Controller方法参数
         * @return 当前构建器
         * @deprecated 请使用 withHttpParameters() 或 withoutHttpParameters() 明确指定参数类型
         */
        @Deprecated
        public Builder<T, R> parameterConfig(Function<T, ParameterConfig[]> parameterConfigFunction) {
            return withHttpParameters(parameterConfigFunction);
        }

        public ApprovalConfig<T, R> build() {
            if (requestType == null || responseType == null) {
                throw new IllegalArgumentException("requestType and responseType are required");
            }
            if (controllerBeanName == null || controllerMethodName == null) {
                throw new IllegalArgumentException("controller bean name and method name are required");
            }
            return new ApprovalConfig<>(this);
        }
    }
}
