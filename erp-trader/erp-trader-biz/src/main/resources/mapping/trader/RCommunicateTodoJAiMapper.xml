<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.RCommunicateTodoJAiMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.RCommunicateTodoJAiEntity">
    <!--@mbg.generated-->
    <!--@Table T_R_COMMUNICATE_TODO_J_AI-->
    <id column="COMMUNICATE_INFO_ID" jdbcType="INTEGER" property="communicateInfoId" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="COMMUNICATE_RECORD_ID" jdbcType="INTEGER" property="communicateRecordId" />
    <result column="BUSINESS_ID" jdbcType="INTEGER" property="businessId" />
    <result column="BUSINESS_NO" jdbcType="VARCHAR" property="businessNo" />
    <result column="CREATE_BUSINESS_CHANGE" jdbcType="INTEGER" property="createBusinessChange" />
    <result column="UPDATE_TRADER_SIGN" jdbcType="INTEGER" property="updateTraderSign" />
    <result column="TRADER_SIGN" jdbcType="VARCHAR" property="traderSign" />
    <result column="SYNC_CONTACT_POSITION" jdbcType="INTEGER" property="syncContactPosition" />
    <result column="CUSTOMER_CONTACT_ID" jdbcType="INTEGER" property="customerContactId" />
    <result column="CUSTOMER_CONTACT_USERNAME" jdbcType="VARCHAR" property="customerContactUsername" />
    <result column="CUSTOMER_CONTACT_PHONE" jdbcType="VARCHAR" property="customerContactPhone" />
    <result column="CUSTOMER_CONTACT_POSITION" jdbcType="VARCHAR" property="customerContactPosition" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    COMMUNICATE_INFO_ID, TRADER_ID, COMMUNICATE_RECORD_ID, BUSINESS_ID, BUSINESS_NO, 
    CREATE_BUSINESS_CHANGE, UPDATE_TRADER_SIGN, TRADER_SIGN, SYNC_CONTACT_POSITION, CUSTOMER_CONTACT_ID, 
    CUSTOMER_CONTACT_USERNAME, CUSTOMER_CONTACT_PHONE, CUSTOMER_CONTACT_POSITION, ADD_TIME, 
    CREATOR, CREATOR_NAME, MOD_TIME, UPDATER, UPDATER_NAME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_R_COMMUNICATE_TODO_J_AI
    where COMMUNICATE_INFO_ID = #{communicateInfoId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_R_COMMUNICATE_TODO_J_AI
    where COMMUNICATE_INFO_ID = #{communicateInfoId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="COMMUNICATE_INFO_ID" keyProperty="communicateInfoId" parameterType="com.vedeng.erp.trader.domain.entity.RCommunicateTodoJAiEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_COMMUNICATE_TODO_J_AI (TRADER_ID, COMMUNICATE_RECORD_ID, BUSINESS_ID, 
      BUSINESS_NO, CREATE_BUSINESS_CHANGE, UPDATE_TRADER_SIGN, 
      TRADER_SIGN, SYNC_CONTACT_POSITION, CUSTOMER_CONTACT_ID, 
      CUSTOMER_CONTACT_USERNAME, CUSTOMER_CONTACT_PHONE, 
      CUSTOMER_CONTACT_POSITION, ADD_TIME, CREATOR, 
      CREATOR_NAME, MOD_TIME, UPDATER, 
      UPDATER_NAME)
    values (#{traderId,jdbcType=INTEGER}, #{communicateRecordId,jdbcType=INTEGER}, #{businessId,jdbcType=INTEGER}, 
      #{businessNo,jdbcType=VARCHAR}, #{createBusinessChange,jdbcType=INTEGER}, #{updateTraderSign,jdbcType=INTEGER}, 
      #{traderSign,jdbcType=VARCHAR}, #{syncContactPosition,jdbcType=INTEGER}, #{customerContactId,jdbcType=INTEGER}, 
      #{customerContactUsername,jdbcType=VARCHAR}, #{customerContactPhone,jdbcType=VARCHAR}, 
      #{customerContactPosition,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{creatorName,jdbcType=VARCHAR}, #{modTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER}, 
      #{updaterName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="COMMUNICATE_INFO_ID" keyProperty="communicateInfoId" parameterType="com.vedeng.erp.trader.domain.entity.RCommunicateTodoJAiEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_COMMUNICATE_TODO_J_AI
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="communicateRecordId != null">
        COMMUNICATE_RECORD_ID,
      </if>
      <if test="businessId != null">
        BUSINESS_ID,
      </if>
      <if test="businessNo != null and businessNo != ''">
        BUSINESS_NO,
      </if>
      <if test="createBusinessChange != null">
        CREATE_BUSINESS_CHANGE,
      </if>
      <if test="updateTraderSign != null">
        UPDATE_TRADER_SIGN,
      </if>
      <if test="traderSign != null and traderSign != ''">
        TRADER_SIGN,
      </if>
      <if test="syncContactPosition != null">
        SYNC_CONTACT_POSITION,
      </if>
      <if test="customerContactId != null">
        CUSTOMER_CONTACT_ID,
      </if>
      <if test="customerContactUsername != null and customerContactUsername != ''">
        CUSTOMER_CONTACT_USERNAME,
      </if>
      <if test="customerContactPhone != null and customerContactPhone != ''">
        CUSTOMER_CONTACT_PHONE,
      </if>
      <if test="customerContactPosition != null and customerContactPosition != ''">
        CUSTOMER_CONTACT_POSITION,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="communicateRecordId != null">
        #{communicateRecordId,jdbcType=INTEGER},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="businessNo != null and businessNo != ''">
        #{businessNo,jdbcType=VARCHAR},
      </if>
      <if test="createBusinessChange != null">
        #{createBusinessChange,jdbcType=INTEGER},
      </if>
      <if test="updateTraderSign != null">
        #{updateTraderSign,jdbcType=INTEGER},
      </if>
      <if test="traderSign != null and traderSign != ''">
        #{traderSign,jdbcType=VARCHAR},
      </if>
      <if test="syncContactPosition != null">
        #{syncContactPosition,jdbcType=INTEGER},
      </if>
      <if test="customerContactId != null">
        #{customerContactId,jdbcType=INTEGER},
      </if>
      <if test="customerContactUsername != null and customerContactUsername != ''">
        #{customerContactUsername,jdbcType=VARCHAR},
      </if>
      <if test="customerContactPhone != null and customerContactPhone != ''">
        #{customerContactPhone,jdbcType=VARCHAR},
      </if>
      <if test="customerContactPosition != null and customerContactPosition != ''">
        #{customerContactPosition,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.RCommunicateTodoJAiEntity">
    <!--@mbg.generated-->
    update T_R_COMMUNICATE_TODO_J_AI
    <set>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="communicateRecordId != null">
        COMMUNICATE_RECORD_ID = #{communicateRecordId,jdbcType=INTEGER},
      </if>
      <if test="businessId != null">
        BUSINESS_ID = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="businessNo != null and businessNo != ''">
        BUSINESS_NO = #{businessNo,jdbcType=VARCHAR},
      </if>
      <if test="createBusinessChange != null">
        CREATE_BUSINESS_CHANGE = #{createBusinessChange,jdbcType=INTEGER},
      </if>
      <if test="updateTraderSign != null">
        UPDATE_TRADER_SIGN = #{updateTraderSign,jdbcType=INTEGER},
      </if>
      <if test="traderSign != null and traderSign != ''">
        TRADER_SIGN = #{traderSign,jdbcType=VARCHAR},
      </if>
      <if test="syncContactPosition != null">
        SYNC_CONTACT_POSITION = #{syncContactPosition,jdbcType=INTEGER},
      </if>
      <if test="customerContactId != null">
        CUSTOMER_CONTACT_ID = #{customerContactId,jdbcType=INTEGER},
      </if>
      <if test="customerContactUsername != null and customerContactUsername != ''">
        CUSTOMER_CONTACT_USERNAME = #{customerContactUsername,jdbcType=VARCHAR},
      </if>
      <if test="customerContactPhone != null and customerContactPhone != ''">
        CUSTOMER_CONTACT_PHONE = #{customerContactPhone,jdbcType=VARCHAR},
      </if>
      <if test="customerContactPosition != null and customerContactPosition != ''">
        CUSTOMER_CONTACT_POSITION = #{customerContactPosition,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
    </set>
    where COMMUNICATE_INFO_ID = #{communicateInfoId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.RCommunicateTodoJAiEntity">
    <!--@mbg.generated-->
    update T_R_COMMUNICATE_TODO_J_AI
    set TRADER_ID = #{traderId,jdbcType=INTEGER},
      COMMUNICATE_RECORD_ID = #{communicateRecordId,jdbcType=INTEGER},
      BUSINESS_ID = #{businessId,jdbcType=INTEGER},
      BUSINESS_NO = #{businessNo,jdbcType=VARCHAR},
      CREATE_BUSINESS_CHANGE = #{createBusinessChange,jdbcType=INTEGER},
      UPDATE_TRADER_SIGN = #{updateTraderSign,jdbcType=INTEGER},
      TRADER_SIGN = #{traderSign,jdbcType=VARCHAR},
      SYNC_CONTACT_POSITION = #{syncContactPosition,jdbcType=INTEGER},
      CUSTOMER_CONTACT_ID = #{customerContactId,jdbcType=INTEGER},
      CUSTOMER_CONTACT_USERNAME = #{customerContactUsername,jdbcType=VARCHAR},
      CUSTOMER_CONTACT_PHONE = #{customerContactPhone,jdbcType=VARCHAR},
      CUSTOMER_CONTACT_POSITION = #{customerContactPosition,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR}
    where COMMUNICATE_INFO_ID = #{communicateInfoId,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2024-05-14-->
  <select id="findByCommunicateRecordId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_R_COMMUNICATE_TODO_J_AI
    where COMMUNICATE_RECORD_ID=#{communicateRecordId,jdbcType=INTEGER}
  </select>

<!--auto generated by MybatisCodeHelper on 2024-05-17-->
  <select id="findByCommunicateRecordIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_R_COMMUNICATE_TODO_J_AI
        where COMMUNICATE_RECORD_ID in
        <foreach item="item" index="index" collection="communicateRecordIdCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>
</mapper>