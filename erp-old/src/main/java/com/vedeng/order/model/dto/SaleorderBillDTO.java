package com.vedeng.order.model.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: daniel
 * @Date: 2021/3/16 17 40
 * @Description: 订单支付信息
 */
@Data
public class SaleorderBillDTO {

    private String orderNo;

    /**
     * 订单全部收货时间
     */
    private Long arriveTime;

    /**
     * 支付总金额
     */
    private BigDecimal payAmount;

    /**
     * 最后的支付时间
     */
    private Long payTime;

    /**
     * 退款总金额
     */
    private BigDecimal refundAmount;

    /**
     * 最后退款时间
     */
    private Long refundTime;
}
