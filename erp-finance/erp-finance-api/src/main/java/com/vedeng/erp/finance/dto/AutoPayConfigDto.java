package com.vedeng.erp.finance.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AutoPayConfigDto {

    /**
     * 主键
     */
    private Integer autoPayConfigId;

    /**
     * 启用付款申请自动制单
     */
    private Boolean enableAutoPay;

    /**
     * 启用采购合同审核状态
     */
    private Boolean enableContractAudit;

    /**
     * 启用供应商白名单
     */
    private Boolean enableSupplierWhitelist;

    /**
     * 供应商白名单
     */
    private List<Integer> supplierWhitelist;

    /**
     * 启用推送金蝶付款单自动审核
     */
    private Boolean enableKingDeeAutoAudit;

    /**
     * 付款申请规则：开始时间
     */
    private String payApplyTimeStart;

    /**
     * 付款申请规则：结束时间
     */
    private String payApplyTimeEnd;

    /**
     *  付款限额
     */
    private String payLimit;

    /**
     * 自动制单规则：开始时间
     */
    private String payBankTimeStart;

    /**
     * 自动制单规则：结束时间
     */
    private String payBankTimeEnd;

    /**
     * 付款银行
     */
    private String payBank;

}
