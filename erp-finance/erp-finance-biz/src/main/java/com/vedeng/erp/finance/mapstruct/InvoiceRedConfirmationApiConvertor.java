package com.vedeng.erp.finance.mapstruct;

import com.vedeng.erp.finance.domain.dto.InvoiceRedConfirmationDto;
import com.vedeng.erp.finance.dto.InvoiceRedConfirmationApiDto;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface InvoiceRedConfirmationApiConvertor  {

    InvoiceRedConfirmationDto to(InvoiceRedConfirmationApiDto dto);

    InvoiceRedConfirmationApiDto toReverse(InvoiceRedConfirmationDto dto);
}
