package com.vedeng.workbench.model.vo.summary;

import com.vedeng.workbench.model.vo.base.WorkbenchSummaryObject;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 沟通信息汇总.
 * @jira: VDERP-3073【商机管理中心】ERP销售工作台.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/10/25 8:49 下午.
 * @author: Tomcat.Hui.
 */
@Data
public class CommunicateSummary {

    /**
     * 统计对象信息
     */
    private WorkbenchSummaryObject objectInfo;

    /**
     * 计划内沟通商机数
     */
    private Integer planingCommunicateNum;

    /**
     * 计划内数量
     */
    private Integer inPlaningNum;

    /**
     * 计划外数量
     */
    private Integer outPlaningNum;

    /**
     * 完成率
     */
    private BigDecimal completionRate;

    /**
     * 今日待沟通商机数量
     */
    private Integer todayToCommunicateNum;
}
