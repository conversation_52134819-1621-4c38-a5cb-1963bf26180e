package com.vedeng.erp.kingdee.batch.writer;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.kingdee.batch.common.enums.WarehouseGoodsInEnum;
import com.vedeng.erp.kingdee.batch.common.enums.WarehouseGoodsOutEnum;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.*;
import com.vedeng.erp.kingdee.service.KingDeeWarehouseGoodsInService;
import com.vedeng.erp.kingdee.service.KingDeeWarehouseGoodsOutService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class BatchGenerateDirectPurchaseOutAndInOrdersWriter extends BaseWriter<BatchGenerateDirectPurchaseOutAndInOrdersDto> {

    @Autowired
    private KingDeeWarehouseGoodsInService kingDeeWarehouseGoodsInService;

    @Autowired
    private KingDeeWarehouseGoodsOutService kingDeeWarehouseGoodsOutService;

    @Autowired
    private BatchRExpressWarehouseGoodsOutInDtoMapper batchRExpressWarehouseGoodsOutInDtoMapper;

    @Override
    public void doWrite(BatchGenerateDirectPurchaseOutAndInOrdersDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("批量生成直发采购出库单和入库单writer开始，参数：{}", JSON.toJSONString(dto));
        BatchBuyorderDto batchBuyorderDto = dto.getBatchBuyorderDto();
        BatchExpressDto batchExpressDto = dto.getBatchExpressDto();
        Map<Integer, Boolean> splitOrNotMap = new HashMap<>();
        // 采购直发入库单
        if (CollUtil.isNotEmpty(dto.getGoodsIdAndSendNum())) {
            log.info("采购直发入库单,BuyOrder:{},goodsIdAndSendNum:{}", JSON.toJSONString(batchBuyorderDto), JSON.toJSONString(dto.getGoodsIdAndSendNum()));
            splitOrNotMap = kingDeeWarehouseGoodsInService.insertWarehouseGoodsPurchaseDirectOutInDirect(null, batchBuyorderDto.getBuyorderId(), dto.getGoodsIdAndSendNum(), WarehouseGoodsInEnum.PURCHASE_IN, batchExpressDto);
        }
        // 采购直发对应的销售出库单
        if (KingDeeConstant.ZERO.equals(batchBuyorderDto.getOrderType())) {
            log.info("采购直发对应的销售出库单,BuyOrder:{},id_sendN_sendedN_sumN:{}", JSON.toJSONString(batchBuyorderDto), dto.getId_sendN_sendedN_sumN());
            kingDeeWarehouseGoodsOutService.insertSaleorderDirectOut(null, batchBuyorderDto, dto.getId_sendN_sendedN_sumN(), batchExpressDto, splitOrNotMap);
        }
    }
}
