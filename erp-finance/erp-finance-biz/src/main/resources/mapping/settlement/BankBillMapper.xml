<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.settlement.mapper.BankBillMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.settlement.domain.entity.BankBillEntity">
        <!--@mbg.generated-->
        <!--@Table T_BANK_BILL-->
        <id column="BANK_BILL_ID" jdbcType="INTEGER" property="bankBillId"/>
        <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId"/>
        <result column="BANK_TAG" jdbcType="INTEGER" property="bankTag"/>
        <result column="TRAN_FLOW" jdbcType="VARCHAR" property="tranFlow"/>
        <result column="TRANDATE" jdbcType="DATE" property="trandate"/>
        <result column="TRANTIME" jdbcType="TIME" property="trantime"/>
        <result column="REAL_TRANDATE" jdbcType="DATE" property="realTrandate"/>
        <result column="REAL_TRANDATETIME" jdbcType="TIMESTAMP" property="realTrandatetime"/>
        <result column="CRE_TYP" jdbcType="VARCHAR" property="creTyp"/>
        <result column="CRE_NO" jdbcType="VARCHAR" property="creNo"/>
        <result column="MESSAGE" jdbcType="VARCHAR" property="message"/>
        <result column="AMT" jdbcType="DECIMAL" property="amt"/>
        <result column="AMT1" jdbcType="VARCHAR" property="amt1"/>
        <result column="FLAG1" jdbcType="INTEGER" property="flag1"/>
        <result column="ACCNO2" jdbcType="VARCHAR" property="accno2"/>
        <result column="ACC_BANKNO" jdbcType="VARCHAR" property="accBankno"/>
        <result column="ACC_NAME1" jdbcType="VARCHAR" property="accName1"/>
        <result column="FLAG2" jdbcType="INTEGER" property="flag2"/>
        <result column="BFLOW" jdbcType="VARCHAR" property="bflow"/>
        <result column="DET_NO" jdbcType="VARCHAR" property="detNo"/>
        <result column="DET" jdbcType="VARCHAR" property="det"/>
        <result column="RLTV_ACCNO" jdbcType="VARCHAR" property="rltvAccno"/>
        <result column="CADBANK_NM" jdbcType="VARCHAR" property="cadbankNm"/>
        <result column="STATUS" jdbcType="INTEGER" property="status"/>
        <result column="COMMENTS" jdbcType="VARCHAR" property="comments"/>
        <result column="MATCHED_AMOUNT" jdbcType="DECIMAL" property="matchedAmount"/>
        <result column="OVRLSTTN_TRCK_NO" jdbcType="VARCHAR" property="ovrlsttnTrckNo"/>
        <result column="IS_CONSISTENCY" jdbcType="INTEGER" property="isConsistency"/>
        <result column="MATCHED_OBJECT" jdbcType="INTEGER" property="matchedObject"/>
        <result column="ORDER_NO" jdbcType="VARCHAR" property="orderNo"/>
        <result column="RELATED_BILL" jdbcType="VARCHAR" property="relatedBill"/>
        <result column="IS_FEE" jdbcType="INTEGER" property="isFee"/>
        <result column="SYNC_DATE" jdbcType="DATE" property="syncDate"/>
        <result column="CAPITAL_SEARCH_FLOW" jdbcType="VARCHAR" property="capitalSearchFlow"/>
        <result column="RECEIPT_URL" jdbcType="VARCHAR" property="receiptUrl"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        BANK_BILL_ID,
        COMPANY_ID,
        BANK_TAG,
        TRAN_FLOW,
        TRANDATE,
        TRANTIME,
        REAL_TRANDATE,
        REAL_TRANDATETIME,
        CRE_TYP,
        CRE_NO,
        MESSAGE,
        AMT,
        AMT1,
        FLAG1,
        ACCNO2,
        ACC_BANKNO,
        ACC_NAME1,
        FLAG2,
        BFLOW,
        DET_NO,
        DET,
        RLTV_ACCNO,
        CADBANK_NM,
        `STATUS`,
        COMMENTS,
        MATCHED_AMOUNT,
        OVRLSTTN_TRCK_NO,
        IS_CONSISTENCY,
        MATCHED_OBJECT,
        ORDER_NO,
        RELATED_BILL,
        IS_FEE,
        SYNC_DATE,
        CAPITAL_SEARCH_FLOW,
        RECEIPT_URL
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from T_BANK_BILL
        where BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete
        from T_BANK_BILL
        where BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="BANK_BILL_ID" keyProperty="bankBillId"
            parameterType="com.vedeng.erp.settlement.domain.entity.BankBillEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_BANK_BILL (COMPANY_ID, BANK_TAG, TRAN_FLOW,
                                 TRANDATE, TRANTIME, REAL_TRANDATE,
                                 REAL_TRANDATETIME, CRE_TYP, CRE_NO,
                                 MESSAGE, AMT, AMT1,
                                 FLAG1, ACCNO2, ACC_BANKNO,
                                 ACC_NAME1, FLAG2, BFLOW,
                                 DET_NO, DET, RLTV_ACCNO,
                                 CADBANK_NM, `STATUS`, COMMENTS,
                                 MATCHED_AMOUNT, OVRLSTTN_TRCK_NO, IS_CONSISTENCY,
                                 MATCHED_OBJECT, ORDER_NO, RELATED_BILL,
                                 IS_FEE, SYNC_DATE, CAPITAL_SEARCH_FLOW,
                                 RECEIPT_URL)
        values (#{companyId,jdbcType=INTEGER}, #{bankTag,jdbcType=INTEGER}, #{tranFlow,jdbcType=VARCHAR},
                #{trandate,jdbcType=DATE}, #{trantime,jdbcType=TIME}, #{realTrandate,jdbcType=DATE},
                #{realTrandatetime,jdbcType=TIMESTAMP}, #{creTyp,jdbcType=VARCHAR}, #{creNo,jdbcType=VARCHAR},
                #{message,jdbcType=VARCHAR}, #{amt,jdbcType=DECIMAL}, #{amt1,jdbcType=VARCHAR},
                #{flag1,jdbcType=INTEGER}, #{accno2,jdbcType=VARCHAR}, #{accBankno,jdbcType=VARCHAR},
                #{accName1,jdbcType=VARCHAR}, #{flag2,jdbcType=INTEGER}, #{bflow,jdbcType=VARCHAR},
                #{detNo,jdbcType=VARCHAR}, #{det,jdbcType=VARCHAR}, #{rltvAccno,jdbcType=VARCHAR},
                #{cadbankNm,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{comments,jdbcType=VARCHAR},
                #{matchedAmount,jdbcType=DECIMAL}, #{ovrlsttnTrckNo,jdbcType=VARCHAR},
                #{isConsistency,jdbcType=INTEGER},
                #{matchedObject,jdbcType=INTEGER}, #{orderNo,jdbcType=VARCHAR}, #{relatedBill,jdbcType=VARCHAR},
                #{isFee,jdbcType=INTEGER}, #{syncDate,jdbcType=DATE}, #{capitalSearchFlow,jdbcType=VARCHAR},
                #{receiptUrl,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="BANK_BILL_ID" keyProperty="bankBillId"
            parameterType="com.vedeng.erp.settlement.domain.entity.BankBillEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_BANK_BILL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                COMPANY_ID,
            </if>
            <if test="bankTag != null">
                BANK_TAG,
            </if>
            <if test="tranFlow != null">
                TRAN_FLOW,
            </if>
            <if test="trandate != null">
                TRANDATE,
            </if>
            <if test="trantime != null">
                TRANTIME,
            </if>
            <if test="realTrandate != null">
                REAL_TRANDATE,
            </if>
            <if test="realTrandatetime != null">
                REAL_TRANDATETIME,
            </if>
            <if test="creTyp != null">
                CRE_TYP,
            </if>
            <if test="creNo != null">
                CRE_NO,
            </if>
            <if test="message != null">
                MESSAGE,
            </if>
            <if test="amt != null">
                AMT,
            </if>
            <if test="amt1 != null">
                AMT1,
            </if>
            <if test="flag1 != null">
                FLAG1,
            </if>
            <if test="accno2 != null">
                ACCNO2,
            </if>
            <if test="accBankno != null">
                ACC_BANKNO,
            </if>
            <if test="accName1 != null">
                ACC_NAME1,
            </if>
            <if test="flag2 != null">
                FLAG2,
            </if>
            <if test="bflow != null">
                BFLOW,
            </if>
            <if test="detNo != null">
                DET_NO,
            </if>
            <if test="det != null">
                DET,
            </if>
            <if test="rltvAccno != null">
                RLTV_ACCNO,
            </if>
            <if test="cadbankNm != null">
                CADBANK_NM,
            </if>
            <if test="status != null">
                `STATUS`,
            </if>
            <if test="comments != null">
                COMMENTS,
            </if>
            <if test="matchedAmount != null">
                MATCHED_AMOUNT,
            </if>
            <if test="ovrlsttnTrckNo != null">
                OVRLSTTN_TRCK_NO,
            </if>
            <if test="isConsistency != null">
                IS_CONSISTENCY,
            </if>
            <if test="matchedObject != null">
                MATCHED_OBJECT,
            </if>
            <if test="orderNo != null">
                ORDER_NO,
            </if>
            <if test="relatedBill != null">
                RELATED_BILL,
            </if>
            <if test="isFee != null">
                IS_FEE,
            </if>
            <if test="syncDate != null">
                SYNC_DATE,
            </if>
            <if test="capitalSearchFlow != null">
                CAPITAL_SEARCH_FLOW,
            </if>
            <if test="receiptUrl != null">
                RECEIPT_URL,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                #{companyId,jdbcType=INTEGER},
            </if>
            <if test="bankTag != null">
                #{bankTag,jdbcType=INTEGER},
            </if>
            <if test="tranFlow != null">
                #{tranFlow,jdbcType=VARCHAR},
            </if>
            <if test="trandate != null">
                #{trandate,jdbcType=DATE},
            </if>
            <if test="trantime != null">
                #{trantime,jdbcType=TIME},
            </if>
            <if test="realTrandate != null">
                #{realTrandate,jdbcType=DATE},
            </if>
            <if test="realTrandatetime != null">
                #{realTrandatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="creTyp != null">
                #{creTyp,jdbcType=VARCHAR},
            </if>
            <if test="creNo != null">
                #{creNo,jdbcType=VARCHAR},
            </if>
            <if test="message != null">
                #{message,jdbcType=VARCHAR},
            </if>
            <if test="amt != null">
                #{amt,jdbcType=DECIMAL},
            </if>
            <if test="amt1 != null">
                #{amt1,jdbcType=VARCHAR},
            </if>
            <if test="flag1 != null">
                #{flag1,jdbcType=INTEGER},
            </if>
            <if test="accno2 != null">
                #{accno2,jdbcType=VARCHAR},
            </if>
            <if test="accBankno != null">
                #{accBankno,jdbcType=VARCHAR},
            </if>
            <if test="accName1 != null">
                #{accName1,jdbcType=VARCHAR},
            </if>
            <if test="flag2 != null">
                #{flag2,jdbcType=INTEGER},
            </if>
            <if test="bflow != null">
                #{bflow,jdbcType=VARCHAR},
            </if>
            <if test="detNo != null">
                #{detNo,jdbcType=VARCHAR},
            </if>
            <if test="det != null">
                #{det,jdbcType=VARCHAR},
            </if>
            <if test="rltvAccno != null">
                #{rltvAccno,jdbcType=VARCHAR},
            </if>
            <if test="cadbankNm != null">
                #{cadbankNm,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="comments != null">
                #{comments,jdbcType=VARCHAR},
            </if>
            <if test="matchedAmount != null">
                #{matchedAmount,jdbcType=DECIMAL},
            </if>
            <if test="ovrlsttnTrckNo != null">
                #{ovrlsttnTrckNo,jdbcType=VARCHAR},
            </if>
            <if test="isConsistency != null">
                #{isConsistency,jdbcType=INTEGER},
            </if>
            <if test="matchedObject != null">
                #{matchedObject,jdbcType=INTEGER},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="relatedBill != null">
                #{relatedBill,jdbcType=VARCHAR},
            </if>
            <if test="isFee != null">
                #{isFee,jdbcType=INTEGER},
            </if>
            <if test="syncDate != null">
                #{syncDate,jdbcType=DATE},
            </if>
            <if test="capitalSearchFlow != null">
                #{capitalSearchFlow,jdbcType=VARCHAR},
            </if>
            <if test="receiptUrl != null">
                #{receiptUrl,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.settlement.domain.entity.BankBillEntity">
        <!--@mbg.generated-->
        update T_BANK_BILL
        <set>
            <if test="companyId != null">
                COMPANY_ID = #{companyId,jdbcType=INTEGER},
            </if>
            <if test="bankTag != null">
                BANK_TAG = #{bankTag,jdbcType=INTEGER},
            </if>
            <if test="tranFlow != null">
                TRAN_FLOW = #{tranFlow,jdbcType=VARCHAR},
            </if>
            <if test="trandate != null">
                TRANDATE = #{trandate,jdbcType=DATE},
            </if>
            <if test="trantime != null">
                TRANTIME = #{trantime,jdbcType=TIME},
            </if>
            <if test="realTrandate != null">
                REAL_TRANDATE = #{realTrandate,jdbcType=DATE},
            </if>
            <if test="realTrandatetime != null">
                REAL_TRANDATETIME = #{realTrandatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="creTyp != null">
                CRE_TYP = #{creTyp,jdbcType=VARCHAR},
            </if>
            <if test="creNo != null">
                CRE_NO = #{creNo,jdbcType=VARCHAR},
            </if>
            <if test="message != null">
                MESSAGE = #{message,jdbcType=VARCHAR},
            </if>
            <if test="amt != null">
                AMT = #{amt,jdbcType=DECIMAL},
            </if>
            <if test="amt1 != null">
                AMT1 = #{amt1,jdbcType=VARCHAR},
            </if>
            <if test="flag1 != null">
                FLAG1 = #{flag1,jdbcType=INTEGER},
            </if>
            <if test="accno2 != null">
                ACCNO2 = #{accno2,jdbcType=VARCHAR},
            </if>
            <if test="accBankno != null">
                ACC_BANKNO = #{accBankno,jdbcType=VARCHAR},
            </if>
            <if test="accName1 != null">
                ACC_NAME1 = #{accName1,jdbcType=VARCHAR},
            </if>
            <if test="flag2 != null">
                FLAG2 = #{flag2,jdbcType=INTEGER},
            </if>
            <if test="bflow != null">
                BFLOW = #{bflow,jdbcType=VARCHAR},
            </if>
            <if test="detNo != null">
                DET_NO = #{detNo,jdbcType=VARCHAR},
            </if>
            <if test="det != null">
                DET = #{det,jdbcType=VARCHAR},
            </if>
            <if test="rltvAccno != null">
                RLTV_ACCNO = #{rltvAccno,jdbcType=VARCHAR},
            </if>
            <if test="cadbankNm != null">
                CADBANK_NM = #{cadbankNm,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                `STATUS` = #{status,jdbcType=INTEGER},
            </if>
            <if test="comments != null">
                COMMENTS = #{comments,jdbcType=VARCHAR},
            </if>
            <if test="matchedAmount != null">
                MATCHED_AMOUNT = #{matchedAmount,jdbcType=DECIMAL},
            </if>
            <if test="ovrlsttnTrckNo != null">
                OVRLSTTN_TRCK_NO = #{ovrlsttnTrckNo,jdbcType=VARCHAR},
            </if>
            <if test="isConsistency != null">
                IS_CONSISTENCY = #{isConsistency,jdbcType=INTEGER},
            </if>
            <if test="matchedObject != null">
                MATCHED_OBJECT = #{matchedObject,jdbcType=INTEGER},
            </if>
            <if test="orderNo != null">
                ORDER_NO = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="relatedBill != null">
                RELATED_BILL = #{relatedBill,jdbcType=VARCHAR},
            </if>
            <if test="isFee != null">
                IS_FEE = #{isFee,jdbcType=INTEGER},
            </if>
            <if test="syncDate != null">
                SYNC_DATE = #{syncDate,jdbcType=DATE},
            </if>
            <if test="capitalSearchFlow != null">
                CAPITAL_SEARCH_FLOW = #{capitalSearchFlow,jdbcType=VARCHAR},
            </if>
            <if test="receiptUrl != null">
                RECEIPT_URL = #{receiptUrl,jdbcType=VARCHAR},
            </if>
            <if test="havePushedKingdee != null">
                HAVE_PUSHED_KINGDEE = #{havePushedKingdee,jdbcType=INTEGER},
            </if>
        </set>
        where BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.settlement.domain.entity.BankBillEntity">
        <!--@mbg.generated-->
        update T_BANK_BILL
        set COMPANY_ID          = #{companyId,jdbcType=INTEGER},
            BANK_TAG            = #{bankTag,jdbcType=INTEGER},
            TRAN_FLOW           = #{tranFlow,jdbcType=VARCHAR},
            TRANDATE            = #{trandate,jdbcType=DATE},
            TRANTIME            = #{trantime,jdbcType=TIME},
            REAL_TRANDATE       = #{realTrandate,jdbcType=DATE},
            REAL_TRANDATETIME   = #{realTrandatetime,jdbcType=TIMESTAMP},
            CRE_TYP             = #{creTyp,jdbcType=VARCHAR},
            CRE_NO              = #{creNo,jdbcType=VARCHAR},
            MESSAGE             = #{message,jdbcType=VARCHAR},
            AMT                 = #{amt,jdbcType=DECIMAL},
            AMT1                = #{amt1,jdbcType=VARCHAR},
            FLAG1               = #{flag1,jdbcType=INTEGER},
            ACCNO2              = #{accno2,jdbcType=VARCHAR},
            ACC_BANKNO          = #{accBankno,jdbcType=VARCHAR},
            ACC_NAME1           = #{accName1,jdbcType=VARCHAR},
            FLAG2               = #{flag2,jdbcType=INTEGER},
            BFLOW               = #{bflow,jdbcType=VARCHAR},
            DET_NO              = #{detNo,jdbcType=VARCHAR},
            DET                 = #{det,jdbcType=VARCHAR},
            RLTV_ACCNO          = #{rltvAccno,jdbcType=VARCHAR},
            CADBANK_NM          = #{cadbankNm,jdbcType=VARCHAR},
            `STATUS`            = #{status,jdbcType=INTEGER},
            COMMENTS            = #{comments,jdbcType=VARCHAR},
            MATCHED_AMOUNT      = #{matchedAmount,jdbcType=DECIMAL},
            OVRLSTTN_TRCK_NO    = #{ovrlsttnTrckNo,jdbcType=VARCHAR},
            IS_CONSISTENCY      = #{isConsistency,jdbcType=INTEGER},
            MATCHED_OBJECT      = #{matchedObject,jdbcType=INTEGER},
            ORDER_NO            = #{orderNo,jdbcType=VARCHAR},
            RELATED_BILL        = #{relatedBill,jdbcType=VARCHAR},
            IS_FEE              = #{isFee,jdbcType=INTEGER},
            SYNC_DATE           = #{syncDate,jdbcType=DATE},
            CAPITAL_SEARCH_FLOW = #{capitalSearchFlow,jdbcType=VARCHAR},
            RECEIPT_URL         = #{receiptUrl,jdbcType=VARCHAR}
        where BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
    </update>
    <insert id="batchInsert" keyColumn="BANK_BILL_ID" keyProperty="bankBillId" parameterType="map"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_BANK_BILL
        (COMPANY_ID, BANK_TAG, TRAN_FLOW, TRANDATE, TRANTIME, REAL_TRANDATE, REAL_TRANDATETIME,
         CRE_TYP, CRE_NO, MESSAGE, AMT, AMT1, FLAG1, ACCNO2, ACC_BANKNO, ACC_NAME1, FLAG2,
         BFLOW, DET_NO, DET, RLTV_ACCNO, CADBANK_NM, `STATUS`, COMMENTS, MATCHED_AMOUNT,
         OVRLSTTN_TRCK_NO, IS_CONSISTENCY, MATCHED_OBJECT, ORDER_NO, RELATED_BILL, IS_FEE,
         SYNC_DATE, CAPITAL_SEARCH_FLOW, RECEIPT_URL)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.companyId,jdbcType=INTEGER}, #{item.bankTag,jdbcType=INTEGER}, #{item.tranFlow,jdbcType=VARCHAR},
             #{item.trandate,jdbcType=DATE}, #{item.trantime,jdbcType=TIME}, #{item.realTrandate,jdbcType=DATE},
             #{item.realTrandatetime,jdbcType=TIMESTAMP}, #{item.creTyp,jdbcType=VARCHAR},
             #{item.creNo,jdbcType=VARCHAR},
             #{item.message,jdbcType=VARCHAR}, #{item.amt,jdbcType=DECIMAL}, #{item.amt1,jdbcType=VARCHAR},
             #{item.flag1,jdbcType=INTEGER}, #{item.accno2,jdbcType=VARCHAR}, #{item.accBankno,jdbcType=VARCHAR},
             #{item.accName1,jdbcType=VARCHAR}, #{item.flag2,jdbcType=INTEGER}, #{item.bflow,jdbcType=VARCHAR},
             #{item.detNo,jdbcType=VARCHAR}, #{item.det,jdbcType=VARCHAR}, #{item.rltvAccno,jdbcType=VARCHAR},
             #{item.cadbankNm,jdbcType=VARCHAR}, #{item.status,jdbcType=INTEGER}, #{item.comments,jdbcType=VARCHAR},
             #{item.matchedAmount,jdbcType=DECIMAL}, #{item.ovrlsttnTrckNo,jdbcType=VARCHAR},
             #{item.isConsistency,jdbcType=INTEGER}, #{item.matchedObject,jdbcType=INTEGER},
             #{item.orderNo,jdbcType=VARCHAR}, #{item.relatedBill,jdbcType=VARCHAR}, #{item.isFee,jdbcType=INTEGER},
             #{item.syncDate,jdbcType=DATE}, #{item.capitalSearchFlow,jdbcType=VARCHAR},
             #{item.receiptUrl,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <!--auto generated by MybatisCodeHelper on 2022-09-06-->
    <select id="selectByTranFlow" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_BANK_BILL
        where TRAN_FLOW = #{tranFlow,jdbcType=VARCHAR}
          and BANK_TAG = #{bankTag,jdbcType=INTEGER}
    </select>
    <select id="getAliPayBillNotPushedToKingDee" resultType="com.vedeng.erp.finance.dto.BankBillDto">
        SELECT BB.BANK_BILL_ID,
               BB.TRAN_FLOW,
               BB.TRANTIME,
               BB.TRANDATE,
               BB.AMT,
               BB.ACCNO2,
               BB.ACC_NAME1,
               TC.TRADER_CUSTOMER_ID,
               CB.TRADER_SUBJECT,
               CB.TRADER_TYPE,
               CBD.ORDER_NO,
               BB.RECEIPT_URL,
               PAY_BANK_NO AS vedengAccountName
        FROM T_BANK_BILL BB
                 LEFT JOIN T_CAPITAL_BILL CB ON CB.BANK_BILL_ID = BB.BANK_BILL_ID
                 LEFT JOIN T_CAPITAL_BILL_DETAIL CBD ON CBD.CAPITAL_BILL_ID = CB.CAPITAL_BILL_ID
                 LEFT JOIN T_TRADER_CUSTOMER TC ON TC.TRADER_ID = CBD.TRADER_ID
                 LEFT JOIN T_PAY_VEDENG_BANK ON PAY_VEDENG_BANK_ID = 4
        WHERE BB.BANK_TAG = 4
          AND BB.HAVE_PUSHED_KINGDEE = 0
          AND BB.RECEIPT_URL IS NOT NULL
          AND CB.CAPITAL_BILL_ID IS NOT NULL
          AND BB.REAL_TRANDATE >= '2022-01-01'
          AND CBD.ORDER_TYPE = 3
          and (BB.IS_FEE = 0 or BB.IS_FEE is null)
        GROUP BY BB.BANK_BILL_ID
    </select>

    <!--auto generated by MybatisCodeHelper on 2022-11-07-->
    <select id="getByTranFlowLike" resultType="com.vedeng.erp.finance.dto.BankBillDto">
        select
        <include refid="Base_Column_List"/>
        from T_BANK_BILL
        where TRAN_FLOW like concat('%', #{tranFlow,jdbcType=VARCHAR}, '%')
          and AMT > 0
        limit 10
    </select>

    <select id="matchBankAmount" parameterType="java.util.Map" resultType="java.math.BigDecimal">
        SELECT if(sum(ABS(TCB.AMOUNT)) = null, 0, sum(ABS(TCB.AMOUNT))) as match_amount
        from T_BANK_BILL TBB
                 left join T_CAPITAL_BILL TCB on TBB.BANK_BILL_ID = TCB.BANK_BILL_ID
        where TBB.BANK_TAG = #{bankTag,jdbcType=INTEGER}
          and TBB.COMPANY_ID = 1
    </select>

    <select id="findByCompanyId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_BANK_BILL
        where COMPANY_ID = #{companyId,jdbcType=INTEGER}
    </select>

    <select id="findByAccName1AndTrandateBetween" resultType="com.vedeng.erp.finance.dto.BankBillDto">
        select (AMT - MATCHED_AMOUNT) as surplusAmount
        from T_BANK_BILL
        where ACC_NAME1 = #{accName1,jdbcType=VARCHAR}
          and TRANDATE <![CDATA[>]]> #{minTrandate,jdbcType=DATE}
          and TRANDATE <![CDATA[<]]> #{maxTrandate,jdbcType=DATE}
          and AMT - MATCHED_AMOUNT > 0
    </select>

    <select id="queryRemainBankBill" resultType="com.vedeng.erp.finance.dto.BankBillDto">
        SELECT
        <include refid="Base_Column_List"/>
        FROM T_BANK_BILL
        WHERE TRAN_FLOW LIKE CONCAT('%', #{tranFlow,jdbcType=VARCHAR}, '%')
          AND (AMT - MATCHED_AMOUNT) > 0
          AND TRANDATE > '2020-01-01'
    </select>
    <select id="querySKBankBillInfo" resultType="com.vedeng.erp.finance.dto.BankBillDto">
        SELECT
        <include refid="Base_Column_List"/>
        FROM T_BANK_BILL
        where FLAG1 = 1
          AND (IS_FEE = 0 OR IS_FEE IS NULL)
          AND STATUS = 0
          AND (AMT - MATCHED_AMOUNT) > 0
          AND BANK_TAG not in (4, 5)
          AND TRAN_FLOW like CONCAT('%', #{tranFlow,jdbcType=VARCHAR}, '%')
    </select>

    <!--auto generated by MybatisCodeHelper on 2023-08-01-->
    <select id="findByTranFlowAndBankTag" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_BANK_BILL
        where TRAN_FLOW = #{tranFlow,jdbcType=VARCHAR}
          and BANK_TAG = #{bankTag,jdbcType=INTEGER}
    </select>

    <select id="queryConstructionBankBill" resultType="com.vedeng.erp.finance.dto.BankBillDto"
            parameterType="com.vedeng.erp.finance.dto.BankBillQueryDto">
        SELECT
        <include refid="Base_Column_List"/>
        FROM T_BANK_BILL
        where (IS_FEE = 0 OR IS_FEE IS NULL)
          AND `STATUS` = #{status,jdbcType=INTEGER}
          AND BANK_TAG in (1,2,3,6,7)
          AND FLAG1 = #{flag1,jdbcType=INTEGER}
          AND MATCHED_AMOUNT = #{matchedAmount,jdbcType=DECIMAL}
          AND REAL_TRANDATETIME <![CDATA[>=]]> #{beginTime,jdbcType=TIMESTAMP}
          AND REAL_TRANDATETIME <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
    </select>

    <select id="queryIgnoreBankBill" resultType="com.vedeng.erp.finance.dto.BankBillDto">
        SELECT
        <include refid="Base_Column_List"/>
        FROM T_BANK_BILL
        where (IS_FEE = 0 OR IS_FEE IS NULL)
          AND `STATUS` = 0
          AND MATCHED_AMOUNT = 0
          AND REAL_TRANDATETIME <![CDATA[>=]]> #{beginTime,jdbcType=TIMESTAMP}
          AND REAL_TRANDATETIME <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
          and (MESSAGE like CONCAT('%', #{message,jdbcType=VARCHAR}, '%')
            OR DET like CONCAT('%', #{message,jdbcType=VARCHAR}, '%')
            )
    </select>

    <select id="checkIgnoreBankBill" resultType="com.vedeng.erp.finance.dto.BankBillDto">
        SELECT
        <include refid="Base_Column_List"/>
        FROM T_BANK_BILL
        where (IS_FEE = 0 OR IS_FEE IS NULL)
          AND `STATUS` = 0
          AND MATCHED_AMOUNT = 0
          and (MESSAGE like CONCAT('%', 'A资金划转#', '%')
            OR DET like CONCAT('%', 'A资金划转#', '%')
            )
          AND BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
    </select>

    <select id="checkConstructionBankBill" resultType="com.vedeng.erp.finance.dto.BankBillDto">
        SELECT
        <include refid="Base_Column_List"/>
        FROM T_BANK_BILL
        where (IS_FEE = 0 OR IS_FEE IS NULL)
          AND `STATUS` = #{status,jdbcType=INTEGER}
          AND BANK_TAG = #{bankTag,jdbcType=INTEGER}
          AND FLAG1 = #{flag1,jdbcType=INTEGER}
          AND MATCHED_AMOUNT = #{matchedAmount,jdbcType=DECIMAL}
          AND BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-01-11-->
    <select id="findByTranFlow" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_BANK_BILL
        where TRAN_FLOW = #{tranFlow,jdbcType=VARCHAR}
    </select>

    <select id="getByTranFlowAndBankTag" resultType="com.vedeng.erp.finance.dto.BankBillDto">
        select
        <include refid="Base_Column_List"/>
        from T_BANK_BILL
        where TRAN_FLOW = #{tranFlow,jdbcType=VARCHAR}
          and BANK_TAG = #{bankTag,jdbcType=INTEGER}
    </select>

    <select id="findAcceptanceBill" resultType="com.vedeng.erp.finance.dto.BankBillDto">
        select
            TBB.*
        from T_BANK_BILL TBB
                 left join T_BANK_BILL_SETTLEMENT TBBS on TBB.BANK_BILL_ID = TBBS.BANK_BILL_ID
        where TBBS.SETTLEMENT_METHOD = 3
          and TBB.FLAG1 = 0
          and TBB.BANK_TAG = 7
          and TBB.STATUS = 0
          and TBB.MATCHED_AMOUNT <![CDATA[<>]]> TBB.AMT
    </select>

    <select id="queryBankBillBySaleOrderId"
            resultType="com.vedeng.erp.finance.domain.dto.BankBillCustomerAccountDto">
        select b.ORDER_NO, c.BANK_BILL_ID,
        c.ACCNO2 as accountNo,
        c.ACC_NAME1 as accountName,
        c.BANK_TAG, c.RECEIPT_URL,c.TRAN_FLOW
        from T_CAPITAL_BILL a
        left join T_CAPITAL_BILL_DETAIL b on a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
        left join T_BANK_BILL c on a.BANK_BILL_ID = c.BANK_BILL_ID
        where a.TRADER_TYPE = 1
        and b.ORDER_TYPE = 1
        and c.BANK_BILL_ID is not null
        and b.BUSSINESS_TYPE != 679
        and c.BANK_TAG not in (4,5)
        and (a.PAYER IS NULL OR a.PAYER NOT IN ('支付宝（中国）网络技术有限公司', '财付通支付科技有限公司'))
        and b.RELATED_ID = #{saleOrderId,jdbcType=INTEGER};
    </select>

    <update id="updateTranFlowForCharge" >
        <!--@mbg.generated-->
        update T_BANK_BILL
        set
        TRAN_FLOW  = #{newTranFlow,jdbcType=VARCHAR}
        where  TRAN_FLOW = #{tranFlow,jdbcType=VARCHAR} and BANK_TAG = #{bankTag,jdbcType=INTEGER}
    </update>

    <select id="countSpecialSerialNumbers" resultType="int">
        select COALESCE(count(*), 0) AS total_sum
        from T_BANK_BILL
        where TRAN_FLOW LIKE '00000000000000000000000000000000%';
    </select>

    <select id="queryBankBill" resultType="com.vedeng.erp.finance.dto.BankBillDto">
        select
        <include refid="Base_Column_List"/>
        from T_BANK_BILL
        where BANK_BILL_ID IN
        <foreach collection="bankBillIds" item="bankBillId" index="index"
                 open="(" close=")" separator=",">
            #{bankBillId,jdbcType=INTEGER}
        </foreach>
    </select>


    <select id="selectTranFlowForChargeLastOne" resultType="com.vedeng.erp.finance.dto.BankBillDto">
        select <include refid="Base_Column_List"/>
        from T_BANK_BILL
        where TRAN_FLOW LIKE '00000000000000000000000000000000%' AND BANK_TAG = #{bankTag,jdbcType=INTEGER}
        ORDER BY BANK_BILL_ID DESC LIMIT 1
    </select>
    
    <select id="querySettlementBankBillList" resultType="com.vedeng.erp.finance.dto.BankBillDto">
        SELECT *
        from T_BANK_BILL a
        where a.AMT != if(a.MATCHED_AMOUNT is NULL, 0, a.MATCHED_AMOUNT)
          and a.STATUS != 1
          and (a.IS_FEE IS NULL OR a.IS_FEE = 0)
          and a.BANK_TAG = 1
          and a.FLAG1 = 1
          and a.REAL_TRANDATETIME >= #{startTime,jdbcType=VARCHAR}
          and a.REAL_TRANDATETIME <![CDATA[ <= ]]> #{endTime,jdbcType=VARCHAR}
    </select>
    
    <select id="getMatchInfo" resultType="com.vedeng.erp.finance.dto.MatchSaleOrderDto">
        select a.*
        from (
        SELECT s.*,sinfo.RECEIVED_AMOUNT FROM (SELECT
        if(COALESCE(e.TOTAL_AMOUNT - IFNULL(abs(b.tk_amount),0) - IFNULL(abs(f.gtk_amount),0),0) <![CDATA[<]]> 0
        ,0,COALESCE(e.TOTAL_AMOUNT - IFNULL(abs(b.tk_amount),0) - IFNULL(abs(f.gtk_amount),0),0)) as TOTAL_AMOUNT
        ,e.SALEORDER_ID,
        e.SALEORDER_NO,
        e.PREPAID_AMOUNT,
        e.HAVE_ACCOUNT_PERIOD,
        e.PAYMENT_STATUS,
        e.ACCOUNT_PERIOD_AMOUNT,
        e.TRADER_ID,

        e.TRADER_NAME,
        e.RETAINAGE_AMOUNT,
        e.TRADER_CONTACT_NAME,
        e.VALID_STATUS,
        e.IS_PAYMENT,
        e.VALID_TIME,
        e.STATUS
        FROM
        T_SALEORDER e
        left JOIN
        (
        SELECT
        sum(bb.NUM*cc.PRICE) as tk_amount,aa.ORDER_ID
        FROM
        T_AFTER_SALES aa
        left JOIN
        T_AFTER_SALES_GOODS bb
        ON
        aa.AFTER_SALES_ID = bb.AFTER_SALES_ID
        left JOIN
        T_SALEORDER_GOODS cc
        ON
        bb.ORDER_DETAIL_ID = cc.SALEORDER_GOODS_ID
        WHERE
        aa.TYPE = 539
        AND
        aa.SUBJECT_TYPE = 535
        AND
        aa.VALID_STATUS = 1
        AND
        aa.ATFER_SALES_STATUS in (1,2)
        GROUP BY
        aa.ORDER_ID
        ) as b
        ON
        e.SALEORDER_ID = b.ORDER_ID

        LEFT JOIN
        (
        select
        sum(ac.REAL_REFUND_AMOUNT) as gtk_amount,ab.ORDER_ID
        from
        T_AFTER_SALES ab
        left join
        T_AFTER_SALES_DETAIL ac
        on
        ab.AFTER_SALES_ID = ac.AFTER_SALES_ID
        where
        ab.SUBJECT_TYPE = 535
        and
        ab.TYPE = 543
        and
        ab.ATFER_SALES_STATUS = 2
        group by
        ab.ORDER_ID

        ) as f
        on
        e.SALEORDER_ID = f.ORDER_ID
        WHERE
        (e.TRADER_NAME =#{accName1,jdbcType=VARCHAR}
        OR
        e.TRADER_CONTACT_NAME =#{accName1,jdbcType=VARCHAR})
        ) AS s
        LEFT JOIN
        (SELECT
        c.SALEORDER_ID,
        if(sum(IF(d.TRADER_TYPE = 1 or d.TRADER_TYPE=4,ABS(b.AMOUNT),if(d.TRADER_TYPE = 3,0,- (ABS(b.AMOUNT))))) is NULL,0,sum(IF(d.TRADER_TYPE = 1 or d.TRADER_TYPE=4,ABS(b.AMOUNT),if(d.TRADER_TYPE = 3,0,- (ABS(b.AMOUNT))))))AS RECEIVED_AMOUNT
        FROM
        T_SALEORDER c
        LEFT JOIN T_CAPITAL_BILL_DETAIL b ON b.RELATED_ID = c.SALEORDER_ID and	 b.ORDER_TYPE = 1
        LEFT JOIN T_CAPITAL_BILL d ON d.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
        WHERE(c.TRADER_NAME =#{accName1,jdbcType=VARCHAR}
        OR
        c.TRADER_CONTACT_NAME =#{accName1,jdbcType=VARCHAR})
        GROUP BY c.SALEORDER_ID)
        sinfo on s.SALEORDER_ID = sinfo.SALEORDER_ID
        ) a
        where ((a.TOTAL_AMOUNT-a.RECEIVED_AMOUNT = #{amt,jdbcType=DECIMAL} and a.TRADER_NAME = #{accName1,jdbcType=VARCHAR})
        or (a.TOTAL_AMOUNT-a.RECEIVED_AMOUNT-a.RETAINAGE_AMOUNT = #{amt,jdbcType=DECIMAL} and a.TRADER_NAME = #{accName1,jdbcType=VARCHAR})
        or (a.TOTAL_AMOUNT-a.RECEIVED_AMOUNT = #{amt,jdbcType=DECIMAL} and a.TRADER_CONTACT_NAME = #{accName1,jdbcType=VARCHAR})
        or (a.TOTAL_AMOUNT-a.RECEIVED_AMOUNT-a.RETAINAGE_AMOUNT = #{amt,jdbcType=DECIMAL} and a.TRADER_CONTACT_NAME = #{accName1,jdbcType=VARCHAR})
        or (a.TOTAL_AMOUNT-a.RECEIVED_AMOUNT != #{amt,jdbcType=DECIMAL} and a.TRADER_NAME = #{accName1,jdbcType=VARCHAR})
        or (a.TOTAL_AMOUNT-a.RECEIVED_AMOUNT-a.RETAINAGE_AMOUNT != #{amt,jdbcType=DECIMAL} and a.TRADER_NAME = #{accName1,jdbcType=VARCHAR})
        or (a.TOTAL_AMOUNT-a.RECEIVED_AMOUNT != #{amt,jdbcType=DECIMAL} and a.TRADER_CONTACT_NAME = #{accName1,jdbcType=VARCHAR})
        or (a.TOTAL_AMOUNT-a.RECEIVED_AMOUNT-a.RETAINAGE_AMOUNT != #{amt,jdbcType=DECIMAL} and a.TRADER_CONTACT_NAME = #{accName1,jdbcType=VARCHAR}))
        and a.VALID_STATUS=1 and a.IS_PAYMENT!=1 and a.STATUS!=3 and a.TOTAL_AMOUNT>0
        order by a.VALID_TIME desc
    </select>
</mapper>
