<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchRExpressWarehouseGoodsOutInDtoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchRExpressWarehouseGoodsOutInDto">
    <!--@mbg.generated-->
    <!--@Table T_R_EXPRESS_WAREHOUSE_GOODS_OUT_IN-->
    <id column="R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID" jdbcType="INTEGER" property="rExpressWarehouseGoodsOutInId" />
    <result column="EXPRESS_ID" jdbcType="INTEGER" property="expressId" />
    <result column="WAREHOUSE_GOODS_OUT_IN_ID" jdbcType="INTEGER" property="warehouseGoodsOutInId" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID, EXPRESS_ID, WAREHOUSE_GOODS_OUT_IN_ID, IS_DELETE, 
    ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_R_EXPRESS_WAREHOUSE_GOODS_OUT_IN
    where R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID = #{rExpressWarehouseGoodsOutInId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_R_EXPRESS_WAREHOUSE_GOODS_OUT_IN
    where R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID = #{rExpressWarehouseGoodsOutInId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID" keyProperty="rExpressWarehouseGoodsOutInId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchRExpressWarehouseGoodsOutInDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_EXPRESS_WAREHOUSE_GOODS_OUT_IN (EXPRESS_ID, WAREHOUSE_GOODS_OUT_IN_ID, 
      IS_DELETE, ADD_TIME, MOD_TIME, 
      CREATOR, UPDATER, CREATOR_NAME, 
      UPDATER_NAME)
    values (#{expressId,jdbcType=INTEGER}, #{warehouseGoodsOutInId,jdbcType=INTEGER}, 
      #{isDelete,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, 
      #{updaterName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID" keyProperty="rExpressWarehouseGoodsOutInId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchRExpressWarehouseGoodsOutInDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_EXPRESS_WAREHOUSE_GOODS_OUT_IN
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="expressId != null">
        EXPRESS_ID,
      </if>
      <if test="warehouseGoodsOutInId != null">
        WAREHOUSE_GOODS_OUT_IN_ID,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="expressId != null">
        #{expressId,jdbcType=INTEGER},
      </if>
      <if test="warehouseGoodsOutInId != null">
        #{warehouseGoodsOutInId,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchRExpressWarehouseGoodsOutInDto">
    <!--@mbg.generated-->
    update T_R_EXPRESS_WAREHOUSE_GOODS_OUT_IN
    <set>
      <if test="expressId != null">
        EXPRESS_ID = #{expressId,jdbcType=INTEGER},
      </if>
      <if test="warehouseGoodsOutInId != null">
        WAREHOUSE_GOODS_OUT_IN_ID = #{warehouseGoodsOutInId,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
    </set>
    where R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID = #{rExpressWarehouseGoodsOutInId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchRExpressWarehouseGoodsOutInDto">
    <!--@mbg.generated-->
    update T_R_EXPRESS_WAREHOUSE_GOODS_OUT_IN
    set EXPRESS_ID = #{expressId,jdbcType=INTEGER},
      WAREHOUSE_GOODS_OUT_IN_ID = #{warehouseGoodsOutInId,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR}
    where R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID = #{rExpressWarehouseGoodsOutInId,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_R_EXPRESS_WAREHOUSE_GOODS_OUT_IN
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="EXPRESS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID = #{item.rExpressWarehouseGoodsOutInId,jdbcType=INTEGER} then #{item.expressId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="WAREHOUSE_GOODS_OUT_IN_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID = #{item.rExpressWarehouseGoodsOutInId,jdbcType=INTEGER} then #{item.warehouseGoodsOutInId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID = #{item.rExpressWarehouseGoodsOutInId,jdbcType=INTEGER} then #{item.isDelete,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID = #{item.rExpressWarehouseGoodsOutInId,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID = #{item.rExpressWarehouseGoodsOutInId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID = #{item.rExpressWarehouseGoodsOutInId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID = #{item.rExpressWarehouseGoodsOutInId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID = #{item.rExpressWarehouseGoodsOutInId,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID = #{item.rExpressWarehouseGoodsOutInId,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.rExpressWarehouseGoodsOutInId,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_R_EXPRESS_WAREHOUSE_GOODS_OUT_IN
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="EXPRESS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.expressId != null">
            when R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID = #{item.rExpressWarehouseGoodsOutInId,jdbcType=INTEGER} then #{item.expressId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="WAREHOUSE_GOODS_OUT_IN_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.warehouseGoodsOutInId != null">
            when R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID = #{item.rExpressWarehouseGoodsOutInId,jdbcType=INTEGER} then #{item.warehouseGoodsOutInId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDelete != null">
            when R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID = #{item.rExpressWarehouseGoodsOutInId,jdbcType=INTEGER} then #{item.isDelete,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID = #{item.rExpressWarehouseGoodsOutInId,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID = #{item.rExpressWarehouseGoodsOutInId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID = #{item.rExpressWarehouseGoodsOutInId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID = #{item.rExpressWarehouseGoodsOutInId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID = #{item.rExpressWarehouseGoodsOutInId,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID = #{item.rExpressWarehouseGoodsOutInId,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.rExpressWarehouseGoodsOutInId,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID" keyProperty="rExpressWarehouseGoodsOutInId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_EXPRESS_WAREHOUSE_GOODS_OUT_IN
    (EXPRESS_ID, WAREHOUSE_GOODS_OUT_IN_ID, IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, UPDATER, 
      CREATOR_NAME, UPDATER_NAME)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.expressId,jdbcType=INTEGER}, #{item.warehouseGoodsOutInId,jdbcType=INTEGER}, 
        #{item.isDelete,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=INTEGER}, #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, 
        #{item.updaterName,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID" keyProperty="rExpressWarehouseGoodsOutInId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchRExpressWarehouseGoodsOutInDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_EXPRESS_WAREHOUSE_GOODS_OUT_IN
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="rExpressWarehouseGoodsOutInId != null">
        R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID,
      </if>
      EXPRESS_ID,
      WAREHOUSE_GOODS_OUT_IN_ID,
      IS_DELETE,
      ADD_TIME,
      MOD_TIME,
      CREATOR,
      UPDATER,
      CREATOR_NAME,
      UPDATER_NAME,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="rExpressWarehouseGoodsOutInId != null">
        #{rExpressWarehouseGoodsOutInId,jdbcType=INTEGER},
      </if>
      #{expressId,jdbcType=INTEGER},
      #{warehouseGoodsOutInId,jdbcType=INTEGER},
      #{isDelete,jdbcType=INTEGER},
      #{addTime,jdbcType=TIMESTAMP},
      #{modTime,jdbcType=TIMESTAMP},
      #{creator,jdbcType=INTEGER},
      #{updater,jdbcType=INTEGER},
      #{creatorName,jdbcType=VARCHAR},
      #{updaterName,jdbcType=VARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="rExpressWarehouseGoodsOutInId != null">
        R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID = #{rExpressWarehouseGoodsOutInId,jdbcType=INTEGER},
      </if>
      EXPRESS_ID = #{expressId,jdbcType=INTEGER},
      WAREHOUSE_GOODS_OUT_IN_ID = #{warehouseGoodsOutInId,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID" keyProperty="rExpressWarehouseGoodsOutInId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchRExpressWarehouseGoodsOutInDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_EXPRESS_WAREHOUSE_GOODS_OUT_IN
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="rExpressWarehouseGoodsOutInId != null">
        R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID,
      </if>
      <if test="expressId != null">
        EXPRESS_ID,
      </if>
      <if test="warehouseGoodsOutInId != null">
        WAREHOUSE_GOODS_OUT_IN_ID,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="rExpressWarehouseGoodsOutInId != null">
        #{rExpressWarehouseGoodsOutInId,jdbcType=INTEGER},
      </if>
      <if test="expressId != null">
        #{expressId,jdbcType=INTEGER},
      </if>
      <if test="warehouseGoodsOutInId != null">
        #{warehouseGoodsOutInId,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="rExpressWarehouseGoodsOutInId != null">
        R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID = #{rExpressWarehouseGoodsOutInId,jdbcType=INTEGER},
      </if>
      <if test="expressId != null">
        EXPRESS_ID = #{expressId,jdbcType=INTEGER},
      </if>
      <if test="warehouseGoodsOutInId != null">
        WAREHOUSE_GOODS_OUT_IN_ID = #{warehouseGoodsOutInId,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

<!--auto generated by MybatisCodeHelper on 2023-05-08-->
  <select id="findByExpressIdAndIsDelete" resultMap="BaseResultMap">
    select
    TREWGOI.*
    from T_R_EXPRESS_WAREHOUSE_GOODS_OUT_IN TREWGOI
    left join T_WAREHOUSE_GOODS_OUT_IN TWGOI on TREWGOI.WAREHOUSE_GOODS_OUT_IN_ID = TWGOI.WAREHOUSE_GOODS_OUT_IN_ID
    where
    TREWGOI.EXPRESS_ID=#{expressId,jdbcType=INTEGER}
    and TREWGOI.IS_DELETE=#{isDelete,jdbcType=INTEGER}
    and TWGOI.IS_DELETE=#{isDelete,jdbcType=INTEGER}
  </select>
</mapper>