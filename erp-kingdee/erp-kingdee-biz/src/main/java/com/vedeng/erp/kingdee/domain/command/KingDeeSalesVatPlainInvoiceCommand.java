package com.vedeng.erp.kingdee.domain.command;

import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/9 16:15
 */
@NoArgsConstructor
@Data
public class KingDeeSalesVatPlainInvoiceCommand {

    /**
     * 单据内码
     */
    private Integer fid;
    /**
     * 单据号
     */
    private String fBillNo;
    /**
     * 业务日期
     */
    private String fdate;
    /**
     * 贝登单据头ID
     */
    private String F_QZOK_BDDJTID;
    /**
     * 发票号
     */
    private String finvoiceno;
    /**
     * 发票日期
     */
    private String finvoicedate;
    /**
     * 发票代码
     */
    private String F_QZOK_FPDM;
    /**
     * 开票方式
     */
    private String fbillingway;
    /**
     * 客户
     */
    private KingDeeNumberCommand fcustomerid = new KingDeeNumberCommand();
    /**
     * 单据状态
     */
    private String fdocumentstatus;
    /**
     * 发票类型
     */
    private KingDeeNumberCommand fBillTypeID = new KingDeeNumberCommand();
    /**
     * 结算组织
     */
    private KingDeeNumberCommand fsettleorgid = new KingDeeNumberCommand();
    /**
     * 作废状态 固定值 A （正常）
     */
    private String fCancelStatus;
    /**
     * 红蓝字标识 0 蓝字  1 红字
     */
    private Integer fRedBlue;

    private String f_QZOK_PZGSYWDH;
    /**
     * 发票的明细
     */
    private List<FSALESICENTRY> fsalesicentry;

    /**
     * 发票的明细
     */
    @NoArgsConstructor
    @Data
    public static class FSALESICENTRY {
        /**
         * 星空物料的编码
         */
        private KingDeeNumberCommand fmaterialid = new KingDeeNumberCommand();
        /**
         * 计价数量
         */
        private BigDecimal fpriceqty;
        /**
         * 含税单价
         */
        private BigDecimal fauxtaxprice;

        /**
         * 税率
         */
        private BigDecimal ftaxrate;

        /**
         * 贝登单据行ID
         */
        private String F_QZOK_BDDJHID;
        /**
         * 原始订单号
         */
        private String F_QZOK_YSDDH;
        /**
         * 归属业务单号
         */
        private String F_QZOK_GSYWDH;
        /**
         * 业务类型
         */
        private String F_QZOK_YWLX;
        /**
         * 源单类型
         */
        private String fsrcbilltypeid;
        /**
         * 源单
         */
        private List<FSALESICENTRYLink> FSALESICENTRY_Link;

        /**
         * FSALESICENTRYLink
         */
        @NoArgsConstructor
        @Data
        public static class FSALESICENTRYLink {
            /**
             * 实体主键
             */
            private Integer fLinkId;
            /**
             * 业务流程图
             */
            private String FSALESICENTRY_Link_FFlowId;
            /**
             * 推进线路ID
             */
            private Integer FSALESICENTRY_Link_FFlowLineId;
            /**
             * 源单据id
             */
            private String FSALESICENTRY_Link_FRuleId;
            /**
             * 源单表内码
             */
            private Integer FSALESICENTRY_Link_FSTableId;
            /**
             * 转换规则
             */
            private String FSALESICENTRY_Link_FSTableName;
            /**
             * 源单内码
             */
            private Integer FSALESICENTRY_Link_FSBillId;
            /**
             * 源单分录内码
             */
            private Integer FSALESICENTRY_Link_FSId;
            /**
             * 原单的数量
             */
            private BigDecimal FSALESICENTRY_Link_FBASICUNITQTYOld;
            /**
             * 修改携带量（实开数量）
             */
            private BigDecimal FSALESICENTRY_Link_FBASICUNITQTY;
            /**
             * 原单的金额
             */
            private BigDecimal FSALESICENTRY_Link_FALLAMOUNTFOROld;
            /**
             * 开票的金额（实开金额）
             */
            private BigDecimal FSALESICENTRY_Link_FALLAMOUNTFOR;
        }
    }
}