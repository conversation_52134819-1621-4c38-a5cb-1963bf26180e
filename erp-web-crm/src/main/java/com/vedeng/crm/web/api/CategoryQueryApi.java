package com.vedeng.crm.web.api;

import com.github.pagehelper.PageInfo;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.crm.feign.category.CategoryThreeApiService;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.goods.dto.CategoryFrontDto;
import com.vedeng.goods.dto.CategoryQueryDto;
import com.vedeng.goods.dto.CategoryResultDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/7/18
 */
@ExceptionController
@RestController
@RequestMapping("/crm/category/public")
@Slf4j
public class CategoryQueryApi {

    @Autowired
    private CategoryThreeApiService categoryThreeApiService;

    /**
     * 返回全部用户的信息
     * @return
     */
    @RequestMapping("/findThreeCategory")
    public R<?> findThreeCategory(@RequestBody CategoryQueryDto categoryQueryDto){
        RestfulResult<PageInfo<CategoryResultDto>> result =  categoryThreeApiService.findThreeCategory(categoryQueryDto);
        if(result.isSuccess()){
            return R.success(result.getData());
        }
        return R.success(new PageInfo<>());
    }

    @RequestMapping("/getAllCategory")
    public R<List<CategoryFrontDto>> getAllCategory(){
        return categoryThreeApiService.findLevelCategory();
    }

}
