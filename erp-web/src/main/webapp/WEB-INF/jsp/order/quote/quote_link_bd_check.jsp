<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="商机关联订单审核页" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<script type="text/javascript">
    $(function()
    {
        var saleorderId = $("input[name='saleorderId']").val();



        //补订单产品详情相关数据
        $.ajax({
            async:true,
            url:page_url+'/order/saleorder/getsaleordergoodsextrainfo.do',
            data:{"saleorderId":saleorderId, "extraType":"order_saleorder"},//销售订单详情（占用，库存，采购状态，到库状态，发货状态，收货状态）
            type:"POST",
            dataType : "json",
            success:function(data){
                if(data.code==0){
                    /*layer.alert(data.message,
                            { icon: 1 },
                            function () {
                                location.reload();
                            }
                    );*/
                    for (var i = 0; i < data.data.length; i++) {
                        $("#orderOccupy_stockNum_"+data.data[i].saleorderGoodsId).html(data.data[i].goods.availableStockNum+"/"+data.data[i].goods.stockNum);
                        $("#kc_"+data.data[i].saleorderGoodsId).html(data.data[i].goods.stockNum);
                        $("#kykc_"+data.data[i].saleorderGoodsId).html((data.data[i].goods.stockNum-data.data[i].goods.orderOccupy) < 0 ? 0 : (data.data[i].goods.stockNum-data.data[i].goods.orderOccupy));
                        $("#dzzy_"+data.data[i].saleorderGoodsId).html(data.data[i].goods.orderOccupy);
                        $("#ktj_"+data.data[i].saleorderGoodsId).html(data.data[i].goods.adjustableNum);
                        //采购状态
                        var cgztStr = '';
                        if (data.data[i].buyNum == undefined || data.data[i].buyNum == 0) {
                            cgztStr = "未采购";
                        } else if (data.data[i].buyNum < data.data[i].num) {
                            cgztStr = "部分采购";
                        } else if (data.data[i].buyNum == data.data[i].num) {
                            cgztStr = "已采购";
                        }
                        $("#cgztStr_"+data.data[i].saleorderGoodsId).html(cgztStr);
                        //到库状态
                        var dkztStr = '';
                        if (data.data[i].warehouseNum == undefined || data.data[i].warehouseNum == 0) {
                            dkztStr = "未到库";
                        } else if (data.data[i].warehouseNum < data.data[i].num) {
                            dkztStr = "部分到库";
                        } else if (data.data[i].warehouseNum == data.data[i].num) {
                            dkztStr = "已到库";
                        }
                        $("#dkztStr_"+data.data[i].saleorderGoodsId).html(dkztStr);
                    }
                }else{
                    //layer.alert(data.message);
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        })
    })
</script>
<div class="content mt10 ">
    <div class="parts" style="width: 49%;display: inline-block;float: left">
        <div class="title-container title-container-yellow">
            <div class="table-title nobor">商机-基本信息</div>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <tbody>
            <tr>
                <td class="table-smaller">商机编号</td>
                <td>
                    <a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"view<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
												"link":"./order/bussinesschance/toSalesDetailPage.do?bussinessChanceId=${bussinessChance.bussinessChanceId}&traderId=${bussinessChance.traderId }",
												"title":"销售商机详情"}'>${bussinessChance.bussinessChanceNo }</a>
                </td>
            </tr>
            <tr>
                <td>创建时间</td>
                <td><date:date value="${bussinessChance.addTime}" format="yyyy-MM-dd HH:mm:ss"/></td>
            </tr>
            <tr>
                <td>归属人员</td>
                <td>${bussinessChance.salerName}</td>
            </tr>
            <tr>
                <td class="table-smaller">商机类型</td>
                <td>${bussinessChance.typeName}</td>
            </tr>
            <tr>
                <td>联系人</td>
                <td>${bussinessChance.traderContactName}</td>
            </tr>
            <tr>
                <td>手机</td>
                <td>
                    ${bussinessChance.mobile}
                </td>
            </tr>
            <tr>
                <td>询价产品</td>
                <td>
                    ${bussinessChance.content}<br/>
                </td>
            </tr>
            <tr>
                <td>产品备注（总机）</td>
                <td >${bussinessChance.productComments}</td>
            </tr>
            </tbody>
        </table>
    </div>
    <div class="parts" style="margin-left:20px;width: 49%;display: inline-block;float: left">
        <div class="title-container title-container-blue">
            <input type="hidden" name="saleorderId" value="${saleorder.saleorderId}">
            <div class="table-title nobor">订单-基本信息</div>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <tbody>
            <tr>
                <td class="table-smaller">订单号</td>
                <td>
                    <a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewsaleorder${saleorder.saleorderId}","link":"./order/saleorder/view.do?saleorderId=${saleorder.saleorderId}","title":"订单信息"}'>
                    ${saleorder.saleorderNo}
                   </a>
                </td>
            </tr>
            <tr>
                <td>创建时间</td>
                <td><date:date value ="${saleorder.addTime}"/></td>
            </tr>
            <tr>
                <td>归属人员</td>
                <td>${saleorder.optUserName}</td>
            </tr>
            <tr>
                <td>客户名称</td>
                <td> <a class="addtitle" href="javascript:void(0);"
                        tabTitle='{"num":"viewcustomer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											"link":"./trader/customer/baseinfo.do?traderId=${saleorder.traderId}",
											"title":"客户信息"}'>${saleorder.traderName}</a>
                </td>
            </tr>
            <tr>
                <td>联系人</td>
                <td>${saleorder.traderContactName}</td>
            </tr>
            <tr>
                <td>手机</td>
                <td>
                    ${saleorder.traderContactMobile}
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <div class="clear"></div>
    <div class="parts">
        <div class="title-container title-container-blue">
            <div class="table-title nobor">订单-产品信息</div>
            <c:set var="referenceCostPrice" value="0"></c:set>
            <shiro:hasPermission name="/order/saleorder/referenceCostPrice.do">
                <c:set var="referenceCostPrice" value="1"></c:set>
            </shiro:hasPermission>
            <c:set var="settlementPrice" value="0"></c:set>
            <shiro:hasPermission name="/order/quote/settlementPrice.do">
                <c:set var="settlementPrice" value="1"></c:set>
            </shiro:hasPermission>
        </div>
        <table class="table  table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <th class="wid4">序号</th>
                <th class="wid15">产品名称</th>
                <th >品牌</th>
                <th class="wid8">规格/型号</th>
                <th class="wid6">单价</th>
                <c:if test="${saleorder.orderType eq 1}">
                    <th class="wid6">原单价</th>
                </c:if>
                <c:if test="${referenceCostPrice eq 1 }">
                    <th class="wid6">参考成本</th>
                </c:if>
                <th class="wid4">数量</th>
                <th class="wid4">单位</th>
                <th>总额</th>
                <th>货期</th>
                <th class="wid5">直发</th>
                <th class="wid10">核价参考</th>
                <th class="wid15">供应链回复</th>
                <th>可用/库存量</th>
                <th>含安调</th>
                <th>产品备注</th>
                <th>内部备注</th>
                <th>锁定状态</th>
                <th>采购状态</th>
                <th>到库状态</th>
                <td>发货数量</td>
                <td>收货数量</td>
                <!-- add by Tomcat.Hui 2019/11/28 15:34 .Desc: VDERP-1325 分批开票 已开票数量/开票中数量/该订单该sku的数量. start -->
                <th class="wid5">已开票/开票中数量</th>
                <!-- add by Tomcat.Hui 2019/11/28 15:34 .Desc: VDERP-1325 分批开票 已开票数量/开票中数量/该订单该sku的数量. end -->
            </tr>
            </thead>
            <tbody>
            <c:set var="num" value="0"></c:set>
            <c:set var="totleMoney" value="0.00"></c:set>
            <c:set var="isNotDelPriceZero" value="0"></c:set>
            <c:set var="isUrgent" value="0"></c:set>
            <c:set var="isCold" value="0"></c:set>
            <c:forEach var="list" items="${saleorderGoodsList}" varStatus="staut">
                <c:if test="${list.isDelete eq 0}">
                    <c:set var="num" value="${num + list.num}"></c:set>
                    <!-- 总额计算方式变更 2018-12-27 产品Ada 总额 = 单价*(订单商品数量-订单商品售后数量) -->
                    <c:set var="totleMoney" value="${totleMoney + (list.price * (list.num - list.afterReturnNum))}"></c:set>
                    <c:if test="${list.price == '0.00'}">
                        <c:set var="isNotDelPriceZero" value="1"></c:set>
                    </c:if>
                </c:if>
                <c:if test="${list.goodsId == '251526'}">
                    <c:set var="isUrgent" value="1"></c:set>
                </c:if>
                <c:if test="${list.goodsId == '256675'}">
                    <c:set var="isCold" value="1"></c:set>
                </c:if>
                <!-- 判断该商品是不是归属于当前登陆人 -->
                <c:if test="${(null!=taskInfo and null!=taskInfo.getProcessInstanceId() and null!=taskInfo.assignee) or !empty candidateUserMap[taskInfo.id]}">
                    <c:set var="shenhe" value="0"></c:set>
                    <c:forEach items="${verifyUserList}" var="verifyUsernameInfo">
                        <c:if test="${verifyUsernameInfo == curr_user.username}">
                            <c:set var="shenhe" value="1"></c:set>
                        </c:if>
                    </c:forEach>
                    <c:if test="${(taskInfo.assignee == curr_user.username or candidateUserMap['belong']) and shenhe!=1 and taskInfo.name == '产品线归属人审核'}">
                        <c:choose>
                            <c:when test="${fn:contains(list.goodsUserIdStr, loginUserId)}">
                                <c:set var="goodsCategoryUser" value="y"></c:set>
                            </c:when>
                            <c:otherwise>
                                <c:set var="goodsCategoryUser" value="n"></c:set>
                            </c:otherwise>
                        </c:choose>
                    </c:if>
                </c:if>
                <c:if test="${saleorder.verifyStatus != null and curr_user.positType == 311 and isCrossMonth == 0}">
                    <c:choose>
                        <c:when test="${fn:contains(list.goodsUserIdStr, loginUserId)}">
                            <c:set var="goodsCategoryUser" value="y"></c:set>
                        </c:when>
                        <c:otherwise>
                            <c:set var="goodsCategoryUser" value="n"></c:set>
                        </c:otherwise>
                    </c:choose>
                </c:if>
                <tr <c:if test="${list.isDelete eq 1 or goodsCategoryUser eq 'n'}">class="caozuo-grey"</c:if>>
                    <td>${staut.count}</td>
                    <td class="text-left">
                        <div class="customername pos_rel">
                            <c:if test="${list.goods.source == 1}"><span style="color: red">【医械购】</span></c:if>
                            <c:choose>
                                <c:when test="${list.isDelete eq 1}">
                                    <span>${list.goodsName}<br/></span>
                                    <span>${list.sku} <br>${list.goods.materialCode}</span>
                                </c:when>
                                <c:otherwise>
                                    <span class="font-blue"><a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewgoods${list.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${list.goodsId}","title":""}'>${list.goodsName}</a>&nbsp;<i class="iconbluemouth contorlIcon"></i><br/></span>
                                    <span>${list.sku} <br>${list.goods.materialCode}</span>

                                    <c:set var="skuNo" value="${list.sku}"></c:set>
                                    <%@ include file="../../common/new_sku_common_tip.jsp" %>

                                </c:otherwise>
                            </c:choose>
                        </div>
                    </td>
                    <td>${list.brandName}</td>
                    <td>${list.model}</td>
                    <td>${list.price}</td>
                    <c:if test="${saleorder.orderType eq 1}">
                        <td>
                            <fmt:formatNumber type="number" value="${null == list.realPrice ? 0 : list.realPrice}" pattern="0.00" maxFractionDigits="2" />
                        </td>
                    </c:if>
                    <c:if test="${referenceCostPrice eq 1 }">
                        <!-- 如果是采购部并且非待审核的 -->
                        <c:choose>
                            <c:when test="${saleorder.validStatus != null and curr_user.positType == 311 and isCrossMonth == 0}">
                                <td>
                                    <input type="text" name="referenceCostPrice_${staut.count}" value="${list.referenceCostPrice == null || list.referenceCostPrice == '0.00' ?(list.costPrice == null ?'0.00':list.costPrice):list.referenceCostPrice}" alt="${list.referenceCostPrice == null || list.referenceCostPrice == '0.00'?'0.00':list.referenceCostPrice}" goodsCategoryUser="${goodsCategoryUser}">
                                    <input type="hidden" name="saleorderGoodsId" value="${list.saleorderGoodsId}" />
                                </td>
                            </c:when>
                            <c:otherwise>
                                <td>${list.referenceCostPrice}</td>
                            </c:otherwise>
                        </c:choose>
                    </c:if>
                    <!-- 数量 -->
                    <td>
                        <c:choose>
                            <c:when test="${((list.afterReturnNum==''||list.afterReturnNum==null) ? 0 : list.afterReturnNum) eq 0}">
                                ${list.num}
                            </c:when>
                            <c:otherwise>
                                <div class="customername pos_rel">
				                        	<span>
				                        		${list.num - list.afterReturnNum}
				                        		<i class="iconredsigh ml4 contorlIcon"></i>
				                        	</span>
                                    <div class="pos_abs customernameshow">原值：${list.num}</div>
                                </div>
                            </c:otherwise>
                        </c:choose>
                    </td>
                    <!- 单位 ->
                    <td>${list.unitName}</td>
                    <td>
                            <%--<!- 总额计算方式变更 2018-12-27 产品Ada 总额 = 单价*(订单商品数量-订单商品售后数量)->
                            <fmt:formatNumber type="number" value="${list.price * (list.num - list.afterReturnNum)}" pattern="0.00" maxFractionDigits="2" />--%>
                        <c:choose>
                            <c:when test="${saleorder.orderType eq 1}">
                                <fmt:formatNumber type="number" value="${list.maxSkuRefundAmount - list.afterReturnAmount}" pattern="0.00" maxFractionDigits="2" />
                            </c:when>
                            <c:otherwise>
                                <fmt:formatNumber type="number" value="${list.price * (list.num - list.afterReturnNum)}" pattern="0.00" maxFractionDigits="2" />
                            </c:otherwise>
                        </c:choose>

                    </td>
                    <td>${list.deliveryCycle}</td>
                    <td>
                        <div class="customername pos_rel">
	                                <span>
	                                <c:choose>
										<c:when test="${list.deliveryDirect eq 0}">否</c:when>
										<c:when test="${list.deliveryDirect eq 3}"></c:when>
										<c:otherwise>
										是
										<i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">直发原因：${list.deliveryDirectComments}</div>
                            </c:otherwise>
                            </c:choose>
                        </div>
                    </td>
                    <td>
                        <div class="customername pos_rel" style="text-align: left">
                            <tags:order_goods_consult_hejia_info item="${list}" userList="${userList}" skuNoAndPriceMap="${skuNoAndPriceMap}" />
                        </div>
                    </td>
                    <td>
                        <div class="customername pos_rel" style="text-align: left">
                            <tags:order_goods_consult_info item="${list}" quote="${quote}" userList="${userList}"/>
                        </div>
                    </td>
                    <td><span id="orderOccupy_stockNum_${list.saleorderGoodsId}"></span></td>
                    <td>
                        <c:choose>
                            <c:when test="${list.haveInstallation eq 0}">否</c:when>
                            <c:otherwise>是</c:otherwise>
                        </c:choose>
                    </td>
                    <td>${list.goodsComments}</td>
                    <td>${list.insideComments}</td>
                    <td>
                        <c:choose>
                            <c:when test="${list.lockedStatus eq 0}"><span>-</span></c:when>
                            <c:otherwise><span style="color: red;">锁</span></c:otherwise>
                        </c:choose>
                    </td>

                    <td><span id="cgztStr_${list.saleorderGoodsId}"></span></td>
                    <td><span id="dkztStr_${list.saleorderGoodsId}"></span></td>
                    <td>
                        <c:forEach var="express" items="${expresseList}" varStatus="staut">
                            <c:if test="${list.saleorderGoodsId eq express.saleOrderGoodsId}">${express.sendNum}/
                                <c:choose>
                                    <c:when test="${((list.afterReturnNum==''||list.afterReturnNum==null) ? 0 : list.afterReturnNum) eq 0}">
                                        ${list.num}
                                    </c:when>
                                    <c:otherwise>
                                        ${list.num - list.afterReturnNum}
                                    </c:otherwise>
                                </c:choose>


                            </c:if>
                        </c:forEach>
                    </td>
                    <td>
                        <c:forEach var="express" items="${expresseList}" varStatus="staut">
                            <c:if test="${list.saleorderGoodsId eq express.saleOrderGoodsId}">${express.arriveNum}/
                                <c:choose>
                                    <c:when test="${((list.afterReturnNum==''||list.afterReturnNum==null) ? 0 : list.afterReturnNum) eq 0}">
                                        ${list.num}
                                    </c:when>
                                    <c:otherwise>
                                        ${list.num - list.afterReturnNum}
                                    </c:otherwise>
                                </c:choose>
                            </c:if>
                        </c:forEach>
                    </td>
                    <!-- add by Tomcat.Hui 2019/11/28 15:34 .Desc: VDERP-1325 分批开票 已开票数量/开票中数量/该订单该sku的数量. start -->
                    <td>${list.invoicedNum}/${list.appliedNum }</td>
                    <!-- add by Tomcat.Hui 2019/11/28 15:34 .Desc: VDERP-1325 分批开票 已开票数量/开票中数量/该订单该sku的数量. end -->
                </tr>
            </c:forEach>
            <tr style="background: #eaf2fd;">
                <c:choose>
                <c:when test="${referenceCostPrice eq 1 and saleorder.orderType eq 1}">
                <td colspan="24" class="text-left">
                    </c:when>
                    <c:when test="${referenceCostPrice eq 1 and saleorder.orderType ne 1}">
                <td colspan="23" class="text-left">
                    </c:when>
                    <c:when test="${referenceCostPrice ne 1 and saleorder.orderType eq 1}">
                <td colspan="23" class="text-left">
                    </c:when>
                    <c:otherwise>
                <td colspan="22" class="text-left">
                    </c:otherwise>
                    </c:choose>
                    <input type="hidden" value="${isNotDelPriceZero}" id="isNotDelPriceZero">
                    总件数<span class="font-red"> ${num}</span>

                    <c:if test="${saleorder.orderType ne 1}">
                        ， 订单原金额&nbsp;&nbsp;<span class="font-red"><fmt:formatNumber type="number" value="${saleorder.totalAmount}" pattern="0.00" maxFractionDigits="2" /></span>&nbsp;&nbsp;
                    </c:if>

                    <c:if test="${saleorder.orderType eq 1}">
                        ， 优惠前金额 <span class="font-red"><fmt:formatNumber type="number" value="${saleorder.totalAmount + (saleorderCoupon.denomination == null? 0 : saleorderCoupon.denomination)}" pattern="0.00" maxFractionDigits="2" /></span>
                        ， 优惠金额 <span class="font-red"><fmt:formatNumber type="number" value="${saleorderCoupon.denomination == null? 0 : saleorderCoupon.denomination}" pattern="0.00" maxFractionDigits="2" /></span>
                        ， 优惠后金额 <span class="font-red"><fmt:formatNumber type="number" value="${saleorder.totalAmount}" pattern="0.00" maxFractionDigits="2" /></span>
                    </c:if>

                    ， 订单实际金额&nbsp;&nbsp;<span class="font-red"><fmt:formatNumber type="number" value="${realAmount}" pattern="0.00" maxFractionDigits="2" /></span>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <div class="parts content1">
        <div class="title-container">
            <div class="table-title nobor">报价单-产品信息</div>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <th class="wid4">序号</th>
                <th class="wid16">产品名称</th>
                <th class="wid12">品牌</th>
                <th class="wid9">型号</th>
                <th class="wid8">报价</th>
                <th class="wid6">数量</th>
                <th class="wid6">单位</th>
                <th class="wid8">总额</th>
                <th class="wid6">货期</th>
                <th class="wid5">直发</th>
                <th class="wid8">可用/库存量</th>
                <th class="wid15">核价参考</th>
                <th class="wid15">供应链回复</th>
                <th class="wid6">含安调</th>
                <th>产品备注</th>
                <th>内部备注</th>
            </tr>
            </thead>
            <tbody>
            <c:set var="num" value="0"></c:set>
            <c:set var="totleMoney" value="0.00"></c:set>
            <c:forEach var="list" items="${quoteGoodsList}" varStatus="staut">
                <tr <c:if test="${list.isDelete eq 1}">class="caozuo-grey"</c:if>>
                    <c:if test="${list.isDelete eq 0}">
                        <!-- 未删除 -->
                        <c:set var="num" value="${num + list.num}"></c:set>
                        <c:set var="totleMoney"
                               value="${totleMoney + (list.price * list.num)}"></c:set>
                    </c:if>
                    <td>${staut.count}</td>
                    <td class="text-left">
                        <div class="customername pos_rel">
                            <c:choose>
                                <c:when test="${list.isDelete eq 1}">
                                    ${qnewSkuInfosMap[list.sku].SHOW_NAME == null ? list.goodsName : qnewSkuInfosMap[list.sku].SHOW_NAME}
                                    <i class="iconbluemouth"></i>
                                    <br>
                                </c:when>
                                <c:otherwise>
                                    <!-- 未删除 -->
                                    <c:if test="${list.isTemp eq 1}">${list.goodsName}</c:if>
                                    <c:if test="${list.isTemp eq 0}"><!-- 非临时产品 -->
                                        <span class="font-blue">
												<a class="addtitle" href="javascript:void(0);"
                                                   tabtitle='{"num":"viewgoods${list.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${list.goodsId}","title":"产品信息"}'>
													${qnewSkuInfosMap[list.sku].SHOW_NAME}
												<i class="iconbluemouth"></i><br>
												</a>
											</span>
                                    </c:if>
                                </c:otherwise>
                            </c:choose>
                                ${list.sku}
                            <c:set var="skuNo" value="${list.sku}"></c:set>
                            <%@ include file="../../common/new_sku_common_tip.jsp" %>
                        </div>
                    </td>
                    <td>
                        <c:choose>
                            <c:when test="${list.isTemp eq 1}">
                                ${list.brandName}
                            </c:when>
                            <c:when test="${list.isTemp eq 0}">
                                ${qnewSkuInfosMap[list.sku].BRAND_NAME}
                            </c:when>
                        </c:choose>
                    </td>
                    <td>
                        <c:choose>
                            <c:when test="${list.isTemp eq 1}">
                                ${list.model}
                            </c:when>
                            <c:when test="${list.isTemp eq 0}">
                                ${qnewSkuInfosMap[list.sku].MODEL}
                            </c:when>
                        </c:choose>
                    </td>
                    <td>${list.price}</td>
                    <td>${list.num}</td>
                    <td>
                        <c:choose>
                            <c:when test="${list.isTemp eq 1}">
                                ${list.unitName}
                            </c:when>
                            <c:when test="${list.isTemp eq 0}">
                                ${qnewSkuInfosMap[list.sku].UNIT_NAME}
                            </c:when>
                        </c:choose>
                    </td>
                    <td><fmt:formatNumber type="number"
                                          value="${list.price * list.num}" pattern="0.00"
                                          maxFractionDigits="2" /></td>
                    <td>${list.deliveryCycle}</td>
                    <td>
                        <div class="customername pos_rel">
								<span> <c:choose>
                                    <c:when test="${list.deliveryDirect eq 0}">否</c:when>
                                    <c:otherwise>
                                        是
                                        <i class="iconbluesigh ml4"></i>
                                        <div class="pos_abs customernameshow">直发原因：${list.deliveryDirectComments}</div>
                                    </c:otherwise>
                                </c:choose>
								</span>
                        </div>
                    </td>
                    <td>${list.goods.availableStockNum}/${list.goods.stockNum}</td>
                    <td>
                        <div class="customername pos_rel" style="text-align: left">
                            <tags:order_goods_consult_hejia_info item="${list}" userList="${userList}" skuNoAndPriceMap="${qskuNoAndPriceMap}" />
                        </div>
                    </td>
                    <td>
                        <div class="customername pos_rel" style="text-align: left">
                            <tags:order_goods_consult_info item="${list}"  quote="${quote}" userList="${userList}"/>
                        </div>
                    </td>
                    <td><c:choose>
                        <c:when test="${list.haveInstallation eq 0}">否</c:when>
                        <c:otherwise>是</c:otherwise>
                    </c:choose></td>
                    <td>${list.goodsComments}</td>
                    <td>${list.insideComments}</td>
                </tr>
            </c:forEach>
            <tr style="background: #eaf2fd;">
                <td colspan="16" class="text-left">总件数<span class="font-red">${num}</span>，
                    总金额 <span class="font-red"> <fmt:formatNumber type="number"
                                                                  value="${totleMoney}" pattern="0.00" maxFractionDigits="2" />
					</span>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <div class="parts content1">
        <div  class="table-buttons">
            <c:if test="${1 eq quote.linkBdStatus && candidateUserMap['belong']}">
                <button type="button" class="bt-bg-style bg-light-green bt-small mr10 pop-new-data"
                        layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./complement.do?taskId=${taskInfo.id}&quoteorderId=${quote.quoteorderId}&pass=true&type=1"}'>审核通过</button>
                <button type="button" class="bt-bg-style bg-light-orange bt-small mr10 pop-new-data"
                        layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./complement.do?taskId=${taskInfo.id}&quoteorderId=${quote.quoteorderId}&pass=false&type=1"}'>审核不通过</button>
            </c:if>
        </div>
    </div>
</div>