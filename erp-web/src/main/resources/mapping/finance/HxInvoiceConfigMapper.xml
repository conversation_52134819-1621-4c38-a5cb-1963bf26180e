<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.finance.dao.HxInvoiceConfigMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.finance.model.HxInvoiceConfig">
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="CONFIG_TYPE" jdbcType="INTEGER" property="configType" />
    <result column="CONTENT" jdbcType="VARCHAR" property="content" />
    <result column="UNIQUE_ID" jdbcType="BIGINT" property="uniqueId" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, CONFIG_TYPE, CONTENT,UNIQUE_ID, CREATOR, ADD_TIME, UPDATER, MOD_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_HX_INVOICE_CONFIG
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_HX_INVOICE_CONFIG
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.finance.model.HxInvoiceConfig" useGeneratedKeys="true">
    insert into T_HX_INVOICE_CONFIG (CONFIG_TYPE, CONTENT,UNIQUE_ID， CREATOR,
      ADD_TIME, UPDATER, MOD_TIME, 
      IS_DEL)
    values (#{configType,jdbcType=INTEGER}, #{content,jdbcType=VARCHAR},#{uniqueId,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER},
      #{addTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT},
      #{isDel,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.finance.model.HxInvoiceConfig" useGeneratedKeys="true">
    insert into T_HX_INVOICE_CONFIG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="configType != null">
        CONFIG_TYPE,
      </if>
      <if test="content != null">
        CONTENT,
      </if>
      <if test="uniqueId != null">
        UNIQUE_ID,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="configType != null">
        #{configType,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="uniqueId != null">
        #{uniqueId,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>

    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.finance.model.HxInvoiceConfig">
    update T_HX_INVOICE_CONFIG
    <set>
      <if test="configType != null">
        CONFIG_TYPE = #{configType,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        CONTENT = #{content,jdbcType=VARCHAR},
      </if>
      <if test="uniqueId != null">
        UNIQUE_ID = #{uniqueId,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.finance.model.HxInvoiceConfig">
    update T_HX_INVOICE_CONFIG
    set CONFIG_TYPE = #{configType,jdbcType=INTEGER},
      CONTENT = #{content,jdbcType=VARCHAR},
      UNIQUE_ID = #{uniqueId,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT}
    where ID = #{id,jdbcType=INTEGER}
  </update>

  <update id="updateByUniqueIdSelective" parameterType="com.vedeng.finance.model.HxInvoiceConfig">
    update T_HX_INVOICE_CONFIG
    <set>
      <if test="configType != null">
        CONFIG_TYPE = #{configType,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        CONTENT = #{content,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
    </set>
    where UNIQUE_ID = #{uniqueId,jdbcType=BIGINT}
  </update>

  <delete id="deleteByConfigType" parameterType="java.lang.Integer">
    delete from T_HX_INVOICE_CONFIG
    where CONFIG_TYPE = #{configType,jdbcType=INTEGER}
  </delete>

  <delete id="deleteByUniqueId" parameterType="java.lang.Long">
    delete from T_HX_INVOICE_CONFIG
    where UNIQUE_ID = #{uniqueId,jdbcType=BIGINT}
  </delete>

  <delete id="deleteInvoiceConfig">
    delete from T_HX_INVOICE_CONFIG
  </delete>


  <select id="getHxInvoiceConfig" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_HX_INVOICE_CONFIG
  </select>

  <select id="getInvoiceConfigByType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_HX_INVOICE_CONFIG
    where CONFIG_TYPE IN
    <foreach item="configType" index="index" collection="configTypes" open="(" separator="," close=")">
      #{configType}
    </foreach>
  </select>

  <select id="getHxInvoiceConfigByUniqueId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_HX_INVOICE_CONFIG
    where UNIQUE_ID = #{uniqueId,jdbcType=BIGINT}
  </select>

  <select id="getInvoiceConfigByTypeAndContent" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_HX_INVOICE_CONFIG
    where CONFIG_TYPE = #{configType,jdbcType=INTEGER} and CONTENT = #{content,jdbcType=VARCHAR}
  </select>
</mapper>