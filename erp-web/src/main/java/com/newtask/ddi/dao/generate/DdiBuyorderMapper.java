package com.newtask.ddi.dao.generate;

import com.wms.model.ddi.DdiBuyorderExtDto;

public interface DdiBuyorderMapper {
    int deleteByPrimaryKey(Integer tDdiBuyorderId);

    int insert(DdiBuyorderExtDto record);

    int insertSelective(DdiBuyorderExtDto record);

    DdiBuyorderExtDto selectByPrimaryKey(Integer tDdiBuyorderId);

    int updateByPrimaryKeySelective(DdiBuyorderExtDto record);

    int updateByPrimaryKey(DdiBuyorderExtDto record);
}