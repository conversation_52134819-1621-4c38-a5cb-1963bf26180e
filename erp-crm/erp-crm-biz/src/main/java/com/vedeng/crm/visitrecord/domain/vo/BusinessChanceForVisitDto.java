package com.vedeng.crm.visitrecord.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class BusinessChanceForVisitDto {

    /**
     * 商机ID
     */
    private Integer bussinessChanceId;


    /**
     * 商机编号
     */
    private String bussinessChanceNo;

    /**
     * 客户ID
     */
    private Integer traderId;

    /**
     * 客户名称
     */
     private String traderName;

    /**
     * 天眼查标识Y是N否
     */
    private String tycFlag;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 预计成单金额
     */
    private BigDecimal amount;

    /**
     * 预计成单日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderTime;


    /**
     * 归属人id
     */
    private Integer belongerId;

    /**
     * 归属人
     */
    private String belonger;

    /**
     * 归属销售头像
     */
    private String belongPic;

    /**
     * （业务类型）商机类型-字典值(小产品、大单品、综合项目、AED、应急)
     */
    private Integer businessType;
    /**
     * （业务类型）商机类型-名称
     */
    private String businessTypeName;
    /**
     * 产品需求（销售）
     */
    private String productCommentsSale;

    /**
     * 商机阶段(1初步洽谈，2商机验证，3初步方案，4最终方案，5赢单，6关闭)
     */
    private Integer stage;

    /**
     * 商机阶段-名称展示
     */
    private String stageStr;

    /**
     * 返回给crm的前端erp的超链接
     */
    private String traderNameLink;

    /**
     * ERP内部链接
     */
    private String traderNameInnerLink;



}
