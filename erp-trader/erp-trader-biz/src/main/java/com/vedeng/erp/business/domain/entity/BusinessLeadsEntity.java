package com.vedeng.erp.business.domain.entity;

import com.vedeng.common.core.utils.validator.group.AddGroup;
import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商机线索
 * @date 2022/7/8 14:35
 */
@Getter
@Setter
public class BusinessLeadsEntity extends BaseEntity {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 线索编号
     */
    private String leadsNo;

    /**
     * 客户id
     */
    private Integer traderId;

    /**
     * 客户名称
     */
    private String traderName;


    /**
     * 联系人Id
     */
    private Integer traderContactId;

    /**
     * 联系人
     */
    @NotNull(message = "联系人不可为空",groups = {AddGroup.class})
    private String contact;

    /**
     * 手机号
     */
    @NotNull(message = "手机不可为空",groups = {AddGroup.class})
    private String phone;

    /**
     * 电话
     */
    private String telephone;

    /**
     * 产品信息
     */
    private String goodsInfo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 线索跟进状态(1.未处理、2.跟进中、3.已关闭、4.已商机)
     */
    private Integer followStatus;

    /**
     * 首次跟进时间
     */
    private Date firstFollowTime;

    /**
     * 跟进图片地址：英文逗号分割的地址
     */
    private String followPic;

    /**
     * 线索类型（1.销售自拓线索）
     */
    private Integer type;

    /**
     * 线索分级（0无效 1有效）
     */
    private Integer status;

    /**
     * 无效原因
     */
    private String invalidReason;

    /**
     * 线索标签id：英文逗号分割的id
     */
    private String tagIds;

    /**
     * 归属人id
     */
    private Integer belongerId;

    /**
     * 归属人
     */
    private String belonger;

    /**
     * 省id
     */
    private Integer provinceId;

    /**
     * 市id
     */
    private Integer cityId;

    /**
     * 县id
     */
    private Integer countyId;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 县
     */
    private String county;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 转商机时间
     */
    private Date turnBusinessChanceTime;

    /**
     * 关闭时间
     */
    private Date closeTime;

    /**
     * 关闭理由
     */
    private String closeReason;

    /**
     * 关闭原因类型
     * 1无法取得联系/2招投标无授权/3产品不在经营范围/4产品不在经营区域/5仅咨询技术问题/6客户没有购买意愿/7没有竞争力、价格没优势/8其他
     */
    private Integer closeReasonType;

    /**
     * 关闭原因类型对应的描述
     */
    private String closeReasonTypeName;


    /**
     * 分配时间
     */
    private Date assignTime;

    /**
     * 商机id
     */
    private Integer businessChanceId;

    /**
     * 线索类型字典库
     */
    private Integer clueType;

    /**
     * 询价行为字典库
     */
    private Integer inquiry;

    /**
     * 渠道类型字典库
     */
    private Integer source;

    /**
     * 渠道名称字典库
     */
    private Integer communication;

    /**
     * 三级分类
     */
    private String content;

    /**
     * 其他联系方式
     */
    private String otherContactInfo;

    /**
     * 咨询入口
     */
    private Integer entrances;

    /**
     * 功能
     */
    private Integer functions;

    /**
     * 线索合并状态，0未合并，1被合并，2合并其他的
     */
    private Integer mergeStatus;

    /**
     * 父线索编号
     */
    private String parentLeadsNo;

    private List<Integer> ids;

    /**
     * 是否发送微信标识
     */
    private  String sendVx;

    /**
     * 天眼查标识
     */
    private String tycFlag;

    private BigDecimal amount;

    private Long orderTime;

    private Integer purchasingType;

    /**
     * 终端名称-搜索用
     */
    private String terminalTraderName;

    /**
     * 终端性质-字典值(公立等级、公立基层、非公等级、非公基层、非公集团、应急、院外)
     */
    private Integer terminalTraderNature;

    /**
     * 终端区域(省市区id，逗号分隔)
     */
    private String terminalTraderRegion;


    /**
     * （业务类型）商机类型-字典值(小产品、大单品、综合项目、AED、应急)
     */
    private Integer businessType;

    /**
     * 客情关系(1熟悉终端决策人，2熟悉使用人，多选逗号分隔)
     */
    private String customerRelationship;

    /**
     * 招投标阶段-字典值(提案咨询、立项论证、意向公示、公开招标、合同签署)
     */
    private Integer biddingPhase;

    /**
     * 招标参数(1可调整，2不可调整)-搜索用
     */
    private Integer biddingParameter;

}
