package com.vedeng.erp.saleorder.api;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.erp.broadcast.StatisticDataDto;
import com.vedeng.erp.broadcast.service.BroadCastStatisticService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 到款播报统计
 * @ClassName:  BroadCastApi   
 * @author: <PERSON>.yang
 * @date:   2025年7月18日 上午11:16:09    
 * @Copyright:
 */
@ExceptionController
@RestController
@RequestMapping("/api/broadCast")
public class BroadCastApi {
	
	private static final Logger log = LoggerFactory.getLogger(BroadCastApi.class);
	
	@Autowired
	private BroadCastStatisticService broadCastStatisticService;

	/**
     * 排行榜信息查询
     */
    @RequestMapping(value = "/getStatisticData")
    @NoNeedAccessAuthorization
    public R<StatisticDataDto> statisticData() {
    	try {
    		StatisticDataDto dto = broadCastStatisticService.getBraodCastStatisticData();
            return R.success(dto);
    	}catch(Exception e) {
    		log.error("查询排行榜信息失败",e);
    		return R.error("查询排行榜信息失败");
    	}
    	
    }
	
}
