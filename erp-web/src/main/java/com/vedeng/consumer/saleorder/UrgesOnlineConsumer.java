package com.vedeng.consumer.saleorder;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.newtask.data.dao.SaleorderDataMapper;
import com.rabbitmq.client.Channel;
import com.vedeng.common.constant.InvoiceApplyUrageEnum;
import com.vedeng.common.trace.mq.AbstractMessageListener;
import com.vedeng.finance.dao.InvoiceApplyMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * @Description:  发票催单消费者
 * @Author:       jyd
 * @Date:         2022/9/21 下午17:00
 * @Version:      1.0
 */
public class UrgesOnlineConsumer extends AbstractMessageListener {

        private final Logger log = LoggerFactory.getLogger(this.getClass());

        @Resource
        private InvoiceApplyMapper applyMapper;

        @Resource
        private SaleorderMapper saleorderMapper;

        @Resource
        private SaleorderDataMapper saleorderDataMapper;

        @Override
        public void doBusiness(Message message, Channel channel) throws Exception {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                String body = new String(message.getBody(), StandardCharsets.UTF_8);
                JSONObject object = JSONObject.parseObject(body);
                log.info("发票催单消费者:{}",body );
                String orderNo = object.getString("orderNo");
                log.info("订单号:{}",orderNo);
                if (ObjectUtil.isEmpty(orderNo)){
                        return;
                }

                Saleorder saleorder = saleorderMapper.getSaleorderByOrderNo(orderNo);

                if (ObjectUtil.isEmpty(saleorder)){
                        log.info("订单号:{}查出信息为空;",orderNo);
                        return;
                }

                log.info("根据订单号:{},查出订单信息:{}",orderNo,JSONObject.toJSONString(saleorder));
                // 修改订单状态 为 已催单
                applyMapper.updateUrageByOrderNumber(saleorder.getSaleorderId(), InvoiceApplyUrageEnum.YES.getKey());
                log.info("发票状态修改完成...");
                /*saleorderMapper.updateUrageById(saleorder.getSaleorderId());*/
                saleorderDataMapper.updateUrageById(saleorder.getSaleorderId());


        }
}
