<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchSettlementBillDtoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchSettlementBillDto">
    <!--@mbg.generated-->
    <!--@Table T_SETTLEMENT_BILL-->
    <id column="SETTLE_BILL_ID" jdbcType="INTEGER" property="settleBillId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="SETTLE_BILL_NO" jdbcType="VARCHAR" property="settleBillNo" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_SUBJECT_TYPE" jdbcType="INTEGER" property="traderSubjectType" />
    <result column="TRADER_SUBJECT_ID" jdbcType="INTEGER" property="traderSubjectId" />
    <result column="SOURCE_TYPE" jdbcType="VARCHAR" property="sourceType" />
    <result column="BUSINESS_SOURCE_TYPE_NO" jdbcType="VARCHAR" property="businessSourceTypeNo" />
    <result column="BUSINESS_SOURCE_TYPE_ID" jdbcType="INTEGER" property="businessSourceTypeId" />
    <result column="SETTLE_AMOUNT" jdbcType="DECIMAL" property="settleAmount" />
    <result column="ALREADY_SETTLE_AMOUNT" jdbcType="DECIMAL" property="alreadySettleAmount" />
    <result column="ACCOUNT_PERIOD" jdbcType="INTEGER" property="accountPeriod" />
    <result column="ACCOUNT_PERIOD_AMOUNT" jdbcType="DECIMAL" property="accountPeriodAmount" />
    <result column="ALREADY_REPAID_AMOUNT" jdbcType="DECIMAL" property="alreadyRepaidAmount" />
    <result column="TRANSACTION_COUNT" jdbcType="INTEGER" property="transactionCount" />
    <result column="SETTLEMENT_STATUS" jdbcType="INTEGER" property="settlementStatus" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    SETTLE_BILL_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, 
    SETTLE_BILL_NO, TRADER_ID, TRADER_SUBJECT_TYPE, TRADER_SUBJECT_ID, SOURCE_TYPE, BUSINESS_SOURCE_TYPE_NO, 
    BUSINESS_SOURCE_TYPE_ID, SETTLE_AMOUNT, ALREADY_SETTLE_AMOUNT, ACCOUNT_PERIOD, ACCOUNT_PERIOD_AMOUNT, 
    ALREADY_REPAID_AMOUNT, TRANSACTION_COUNT, SETTLEMENT_STATUS, IS_DELETE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_SETTLEMENT_BILL
    where SETTLE_BILL_ID = #{settleBillId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_SETTLEMENT_BILL
    where SETTLE_BILL_ID = #{settleBillId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="SETTLE_BILL_ID" keyProperty="settleBillId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchSettlementBillDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SETTLEMENT_BILL (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      SETTLE_BILL_NO, TRADER_ID, TRADER_SUBJECT_TYPE, 
      TRADER_SUBJECT_ID, SOURCE_TYPE, BUSINESS_SOURCE_TYPE_NO, 
      BUSINESS_SOURCE_TYPE_ID, SETTLE_AMOUNT, ALREADY_SETTLE_AMOUNT, 
      ACCOUNT_PERIOD, ACCOUNT_PERIOD_AMOUNT, ALREADY_REPAID_AMOUNT, 
      TRANSACTION_COUNT, SETTLEMENT_STATUS, IS_DELETE
      )
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{settleBillNo,jdbcType=VARCHAR}, #{traderId,jdbcType=INTEGER}, #{traderSubjectType,jdbcType=INTEGER}, 
      #{traderSubjectId,jdbcType=INTEGER}, #{sourceType,jdbcType=VARCHAR}, #{businessSourceTypeNo,jdbcType=VARCHAR}, 
      #{businessSourceTypeId,jdbcType=INTEGER}, #{settleAmount,jdbcType=DECIMAL}, #{alreadySettleAmount,jdbcType=DECIMAL}, 
      #{accountPeriod,jdbcType=INTEGER}, #{accountPeriodAmount,jdbcType=DECIMAL}, #{alreadyRepaidAmount,jdbcType=DECIMAL}, 
      #{transactionCount,jdbcType=INTEGER}, #{settlementStatus,jdbcType=INTEGER}, #{isDelete,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="SETTLE_BILL_ID" keyProperty="settleBillId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchSettlementBillDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SETTLEMENT_BILL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="settleBillNo != null">
        SETTLE_BILL_NO,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderSubjectType != null">
        TRADER_SUBJECT_TYPE,
      </if>
      <if test="traderSubjectId != null">
        TRADER_SUBJECT_ID,
      </if>
      <if test="sourceType != null">
        SOURCE_TYPE,
      </if>
      <if test="businessSourceTypeNo != null">
        BUSINESS_SOURCE_TYPE_NO,
      </if>
      <if test="businessSourceTypeId != null">
        BUSINESS_SOURCE_TYPE_ID,
      </if>
      <if test="settleAmount != null">
        SETTLE_AMOUNT,
      </if>
      <if test="alreadySettleAmount != null">
        ALREADY_SETTLE_AMOUNT,
      </if>
      <if test="accountPeriod != null">
        ACCOUNT_PERIOD,
      </if>
      <if test="accountPeriodAmount != null">
        ACCOUNT_PERIOD_AMOUNT,
      </if>
      <if test="alreadyRepaidAmount != null">
        ALREADY_REPAID_AMOUNT,
      </if>
      <if test="transactionCount != null">
        TRANSACTION_COUNT,
      </if>
      <if test="settlementStatus != null">
        SETTLEMENT_STATUS,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="settleBillNo != null">
        #{settleBillNo,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderSubjectType != null">
        #{traderSubjectType,jdbcType=INTEGER},
      </if>
      <if test="traderSubjectId != null">
        #{traderSubjectId,jdbcType=INTEGER},
      </if>
      <if test="sourceType != null">
        #{sourceType,jdbcType=VARCHAR},
      </if>
      <if test="businessSourceTypeNo != null">
        #{businessSourceTypeNo,jdbcType=VARCHAR},
      </if>
      <if test="businessSourceTypeId != null">
        #{businessSourceTypeId,jdbcType=INTEGER},
      </if>
      <if test="settleAmount != null">
        #{settleAmount,jdbcType=DECIMAL},
      </if>
      <if test="alreadySettleAmount != null">
        #{alreadySettleAmount,jdbcType=DECIMAL},
      </if>
      <if test="accountPeriod != null">
        #{accountPeriod,jdbcType=INTEGER},
      </if>
      <if test="accountPeriodAmount != null">
        #{accountPeriodAmount,jdbcType=DECIMAL},
      </if>
      <if test="alreadyRepaidAmount != null">
        #{alreadyRepaidAmount,jdbcType=DECIMAL},
      </if>
      <if test="transactionCount != null">
        #{transactionCount,jdbcType=INTEGER},
      </if>
      <if test="settlementStatus != null">
        #{settlementStatus,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchSettlementBillDto">
    <!--@mbg.generated-->
    update T_SETTLEMENT_BILL
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="settleBillNo != null">
        SETTLE_BILL_NO = #{settleBillNo,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderSubjectType != null">
        TRADER_SUBJECT_TYPE = #{traderSubjectType,jdbcType=INTEGER},
      </if>
      <if test="traderSubjectId != null">
        TRADER_SUBJECT_ID = #{traderSubjectId,jdbcType=INTEGER},
      </if>
      <if test="sourceType != null">
        SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR},
      </if>
      <if test="businessSourceTypeNo != null">
        BUSINESS_SOURCE_TYPE_NO = #{businessSourceTypeNo,jdbcType=VARCHAR},
      </if>
      <if test="businessSourceTypeId != null">
        BUSINESS_SOURCE_TYPE_ID = #{businessSourceTypeId,jdbcType=INTEGER},
      </if>
      <if test="settleAmount != null">
        SETTLE_AMOUNT = #{settleAmount,jdbcType=DECIMAL},
      </if>
      <if test="alreadySettleAmount != null">
        ALREADY_SETTLE_AMOUNT = #{alreadySettleAmount,jdbcType=DECIMAL},
      </if>
      <if test="accountPeriod != null">
        ACCOUNT_PERIOD = #{accountPeriod,jdbcType=INTEGER},
      </if>
      <if test="accountPeriodAmount != null">
        ACCOUNT_PERIOD_AMOUNT = #{accountPeriodAmount,jdbcType=DECIMAL},
      </if>
      <if test="alreadyRepaidAmount != null">
        ALREADY_REPAID_AMOUNT = #{alreadyRepaidAmount,jdbcType=DECIMAL},
      </if>
      <if test="transactionCount != null">
        TRANSACTION_COUNT = #{transactionCount,jdbcType=INTEGER},
      </if>
      <if test="settlementStatus != null">
        SETTLEMENT_STATUS = #{settlementStatus,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
    </set>
    where SETTLE_BILL_ID = #{settleBillId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchSettlementBillDto">
    <!--@mbg.generated-->
    update T_SETTLEMENT_BILL
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      SETTLE_BILL_NO = #{settleBillNo,jdbcType=VARCHAR},
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      TRADER_SUBJECT_TYPE = #{traderSubjectType,jdbcType=INTEGER},
      TRADER_SUBJECT_ID = #{traderSubjectId,jdbcType=INTEGER},
      SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR},
      BUSINESS_SOURCE_TYPE_NO = #{businessSourceTypeNo,jdbcType=VARCHAR},
      BUSINESS_SOURCE_TYPE_ID = #{businessSourceTypeId,jdbcType=INTEGER},
      SETTLE_AMOUNT = #{settleAmount,jdbcType=DECIMAL},
      ALREADY_SETTLE_AMOUNT = #{alreadySettleAmount,jdbcType=DECIMAL},
      ACCOUNT_PERIOD = #{accountPeriod,jdbcType=INTEGER},
      ACCOUNT_PERIOD_AMOUNT = #{accountPeriodAmount,jdbcType=DECIMAL},
      ALREADY_REPAID_AMOUNT = #{alreadyRepaidAmount,jdbcType=DECIMAL},
      TRANSACTION_COUNT = #{transactionCount,jdbcType=INTEGER},
      SETTLEMENT_STATUS = #{settlementStatus,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=INTEGER}
    where SETTLE_BILL_ID = #{settleBillId,jdbcType=INTEGER}
  </update>

  <select id="queryBuyOrderRebate" resultMap="BaseResultMap">
    select
    TSB.SETTLE_BILL_ID, TSB.ADD_TIME, TSB.MOD_TIME,
    TSB.SETTLE_BILL_NO, TRADER_ID, TRADER_SUBJECT_TYPE, TRADER_SUBJECT_ID, SOURCE_TYPE, BUSINESS_SOURCE_TYPE_NO,
    BUSINESS_SOURCE_TYPE_ID, SETTLE_AMOUNT, ALREADY_SETTLE_AMOUNT, ACCOUNT_PERIOD, ACCOUNT_PERIOD_AMOUNT,
    ALREADY_REPAID_AMOUNT, TRANSACTION_COUNT, TSB.SETTLEMENT_STATUS, TSB.IS_DELETE
    from T_SETTLEMENT_BILL TSB
           LEFT JOIN T_SETTLEMENT_BILL_ITEM TSBI on TSB.SETTLE_BILL_ID = TSBI.SETTLE_BILL_ID
    where TSB.SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR}
      and TSB.IS_DELETE = #{isDelete,jdbcType=INTEGER}
    and TSBI.INVOICE_STATUS != 2
      and TSBI.SETTLEMENT_TYPE = 1
    and TSBI.SETTLEMENT_STATUS = 2
    and TSBI.IS_DELETE =0
    <!--&lt;if test="beginTime != null"&gt;-->
    <!--  and TSB.ADD_TIME &lt;![CDATA[&gt;=]]&gt; #{beginTime,jdbcType=TIMESTAMP}-->
    <!--&lt;/if&gt;-->
    <!--&lt;if test="endTime != null"&gt;-->
    <!--  and TSB.ADD_TIME &lt;![CDATA[&lt;=]]&gt; #{endTime,jdbcType=TIMESTAMP}-->
    <!--&lt;/if&gt;-->
    group by TSB.SETTLE_BILL_ID
    limit #{_pagesize} OFFSET #{_skiprows}
  </select>

  <select id="queryBuyOrderAfterRebate" resultMap="BaseResultMap">
    select
    TSB.SETTLE_BILL_ID, TSB.ADD_TIME, TSB.MOD_TIME,
    TSB.SETTLE_BILL_NO, TRADER_ID, TRADER_SUBJECT_TYPE, TRADER_SUBJECT_ID, SOURCE_TYPE, BUSINESS_SOURCE_TYPE_NO,
    BUSINESS_SOURCE_TYPE_ID, SETTLE_AMOUNT, ALREADY_SETTLE_AMOUNT, ACCOUNT_PERIOD, ACCOUNT_PERIOD_AMOUNT,
    ALREADY_REPAID_AMOUNT, TRANSACTION_COUNT, TSB.SETTLEMENT_STATUS, TSB.IS_DELETE
    from T_SETTLEMENT_BILL TSB
    LEFT JOIN T_SETTLEMENT_BILL_ITEM TSBI on TSB.SETTLE_BILL_ID = TSBI.SETTLE_BILL_ID
    where TSB.SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR}
    and TSB.IS_DELETE = #{isDelete,jdbcType=INTEGER}
    and TSBI.INVOICE_STATUS != 2
    and TSBI.SETTLEMENT_TYPE = 1
    and TSBI.SETTLEMENT_STATUS = 2
    and TSBI.IS_DELETE =0
    <!--&lt;if test="beginTime != null"&gt;-->
    <!--  and TSB.MOD_TIME &lt;![CDATA[&gt;=]]&gt; #{beginTime,jdbcType=TIMESTAMP}-->
    <!--&lt;/if&gt;-->
    <!--&lt;if test="endTime != null"&gt;-->
    <!--  and TSB.MOD_TIME &lt;![CDATA[&lt;=]]&gt; #{endTime,jdbcType=TIMESTAMP}-->
    <!--&lt;/if&gt;-->
    group by TSB.SETTLE_BILL_ID
    limit #{_pagesize} OFFSET #{_skiprows}
  </select>

<!--auto generated by MybatisCodeHelper on 2023-12-12-->
  <select id="selectByBusinessSourceTypeIdAndSourceType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_SETTLEMENT_BILL
    where BUSINESS_SOURCE_TYPE_ID=#{businessSourceTypeId,jdbcType=INTEGER} and
    SOURCE_TYPE=#{sourceType,jdbcType=VARCHAR}
  </select>
</mapper>