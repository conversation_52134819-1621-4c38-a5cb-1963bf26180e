<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="售后详情-其他" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<script type="text/javascript"
        src='<%= basePath %>static/js/orderstream/aftersales/view_afterSales.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript"
        src='<%= basePath %>static/js/orderstream/aftersales/view_afterSales_qt.js?rnd=${resourceVersionKey}'></script>
<script src="<%= basePath %>static/js/orderstream/jquery.js"></script>
<%--<script src="<%= basePath %>static/js/orderstream/index.js"></script>--%>
<link rel="stylesheet" href="<%= basePath %>static/css/orderstream/style.css">

<div class="main-container">

    <%--用户已点击过关闭-》完结字段改为关闭--%>
    <c:choose>
        <c:when test="${afterSalesVo.atferSalesStatus eq 3}">
            <div class="t-line-wrap J-line-wrap"
                 data-json='[
                         {"label":"确认","status":1},
                         {"label":"处理中","status": 4},
                         {"label":"关闭","status":1}]'>
            </div>
        </c:when>
        <c:otherwise>
            <div class="t-line-wrap J-line-wrap"
                 data-json='[
                     {"label":"确认","status":${topStatusList.get(0)}},
                     {"label":"处理中","status": 4},
                     {"label":"完结","status":${topStatusList.get(1)}}]'>
            </div>
        </c:otherwise>
    </c:choose>

    <%--编辑订单-提交审核-申请完结-关闭订单 按钮--%>
    <div class="parts">
        <div class="table-buttons">
            <form action="" method="post" id="myform">
                <input type="hidden" name="afterSalesId" id="afterSalesId" value="${afterSalesVo.afterSalesId}"/>
                <input type="hidden" name="orderId" value="${afterSalesVo.orderId}"/>
                <input type="hidden" name="subjectType" value="${afterSalesVo.subjectType}"/>
                <input type="hidden" name="type" value="${afterSalesVo.type}"/>
                <input type="hidden" name="formToken" value="${formToken}"/>
                <input type="hidden" name="taskId" value="${taskInfo.id == null ?0: taskInfo.id}"/>


                <%--      售后单在待确认状态，申请人与售后处理人可点击，否则置灰--%>
                <c:choose>
                    <%--<c:when test="${afterSalesVo.atferSalesStatus eq 0 &&
                    (user.positType eq 310 || user.positType eq 312 || user.positType eq 589) &&
                     ((null==taskInfo and null==taskInfo.processInstanceId and endStatus != '审核完成') or (null!=taskInfo and taskInfo.assignee==null))}">--%>
                    <c:when test="${afterSalesVo.atferSalesStatus eq 0 && afterSalesVo.status ne 1
                   && (user.positType eq 310 || user.positType eq 312 || user.positType eq 589)}">
                    <button type="button" class="bt-bg-style bg-light-blue bt-small" onclick="editAfterSales(1);">
                            编辑订单
                        </button>
                    </c:when>

                    <c:otherwise>
                        <button type="button" class="bt-bg-style bg-light-grey bt-small">
                            编辑订单
                        </button>
                    </c:otherwise>
                </c:choose>

                <%--售后单在待确认状态，售后处理人可点击，否则置灰--%>
                <c:choose>
                    <c:when test="${afterSalesVo.atferSalesStatus eq 0 && afterSalesVo.status ne 1
                   && (user.positType eq 310 || user.positType eq 312 || user.positType eq 589)}">
                        <button type="button" class="bt-bg-style bg-light-blue bt-small" onclick="applyAudit();">
                            提交审核
                        </button>
                    </c:when>
                    <c:otherwise>
                        <button type="button" class="bt-bg-style bg-light-grey bt-small">
                            提交审核
                        </button>
                    </c:otherwise>
                </c:choose>

                <c:choose>
                    <c:when test="${isSupply ne 1 && ((null!=taskInfo and null!=taskInfo.getProcessInstanceId() and null!=taskInfo.assignee) or !empty candidateUserMap[taskInfo.id]) &&
                            (taskInfo.assignee == user.username or candidateUserMap['belong'])}">
                        <%--售后单在待确认、审核中状态下，审核节点对应审核账号可点击，否则置灰--%>
                        <button type="button" class="bt-bg-style bg-light-blue bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./complement.do?taskId=${taskInfo.id}&pass=true&type=2&saleorderId=${afterSalesVo.orderId}&afterSalesId=${afterSalesVo.afterSalesId}"}'>审核通过</button>
                        <button type="button" class="bt-bg-style bg-light-blue bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./complement.do?taskId=${taskInfo.id}&pass=false&type=2&saleorderId=${afterSalesVo.orderId}&afterSalesId=${afterSalesVo.afterSalesId}"}'>审核不通过</button>
                    </c:when>
                    <c:otherwise>
                        <button type="button" class="bt-bg-style bg-light-grey bt-small mr10">
                            审核通过
                        </button>
                        <button type="button" class="bt-bg-style bg-light-grey bt-small mr10">
                            审核不通过
                        </button>
                    </c:otherwise>
                </c:choose>

                <%--   售后单入库状态为“无入库/全部入库”、退票状态为“无退票/全部退票”退款状态为“无退款/全部退款”开票状态为“全部开票/无开票”，
                   且售后单未在完结审核流程中，此时售后处理人可点击，否则置灰--%>
                <c:choose>
                    <c:when test="${afterSalesVo.atferSalesStatus eq 1 && (user.positType eq 312)
                    && ((null==taskInfoOver and null==taskInfoOver.getProcessInstanceId())
                    or (null!=taskInfoOver and taskInfoOver.assignee==null and empty candidateUserMapOver[taskInfoOver.id]))}">
                        <button type="button" class="bt-bg-style bg-light-green bt-small" onclick="confirmComplete();">申请完结</button>
                    </c:when>
                    <c:otherwise>
                        <button type="button" class="bt-bg-style bg-light-grey bt-small">申请完结</button>
                    </c:otherwise>
                </c:choose>

                <c:choose>
                    <c:when test="${afterSalesVo.status ne 1 && afterSalesVo.closeStatus eq 1
                && afterSalesVo.atferSalesStatus ne 3 && afterSalesVo.atferSalesStatus ne 2
                && ((null==taskInfoOver and null==taskInfoOver.getProcessInstanceId())
                or (null!=taskInfoOver and taskInfoOver.assignee==null and empty candidateUserMapOver[taskInfoOver.id]))
                && (user.positType eq 312 || (afterSalesVo.atferSalesStatus eq 0 && user.userId eq afterSalesVo.creator))}">
                        <button type="button" class="bt-bg-style bg-light-blue bt-small" onclick="colse();">
                            关闭订单
                        </button>
                    </c:when>
                    <c:otherwise>
                        <button type="button" class="bt-bg-style bg-light-grey bt-small">
                            关闭订单
                        </button>
                    </c:otherwise>
                </c:choose>


                <c:choose>
                    <c:when test="${((null!=taskInfoOver and null!=taskInfoOver.getProcessInstanceId() and null!=taskInfoOver.assignee) or !empty candidateUserMapOver[taskInfoOver.id])
                    && (taskInfoOver.assignee == user.username or candidateUserMapOver['belong'])}">
                        <button type="button" class="bt-bg-style bg-light-blue bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./complement.do?taskId=${taskInfoOver.id}&pass=true&type=2&sku=${sku}&orderId=${afterSalesVo.orderId}&afterSalesId=${afterSalesVo.afterSalesId}"}'>完结/关闭审核通过</button>
                        <button type="button" class="bt-bg-style bg-light-blue bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./complement.do?taskId=${taskInfoOver.id}&pass=false&type=2&afterSalesId=${afterSalesVo.afterSalesId}"}'>完结/关闭审核不通过</button>
                    </c:when>
                    <c:otherwise>
                        <button type="button" class="bt-bg-style bg-light-grey bt-small">
                            完结/关闭审核通过
                        </button>
                        <button type="button" class="bt-bg-style bg-light-grey bt-small">
                            完结/关闭审核不通过
                        </button>
                    </c:otherwise>
                </c:choose>
            </form>

        </div>
    </div>

    <%--基本信息模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                基本信息
            </div>
        </div>
        <table class="table">
            <tbody>
            <tr>
                <td class="wid20">订单号</td>
                <td>${afterSalesVo.afterSalesNo}</td>
                <td class="wid20">售后类型</td>
                <td>${afterSalesVo.typeName}</td>
            </tr>

            <tr>
                <td class="wid20">订单状态</td>
                <td>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 0}">待确认</c:if>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 1}">进行中</c:if>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 2}">已完结</c:if>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 3}">已关闭</c:if>
                </td>

                <td>审核状态</td>
                <td>
                    <c:if test="${afterSalesVo.status eq 0}">待审核</c:if>
                    <c:if test="${afterSalesVo.status eq 1}">审核中</c:if>
                    <c:if test="${afterSalesVo.status eq 2}">审核通过</c:if>
                    <c:if test="${afterSalesVo.status eq 3}">审核不通过</c:if>
                </td>
            </tr>


            <tr>
                <td>生效状态</td>
                <td>
                    <c:if test="${afterSalesVo.validStatus eq 0}">未生效</c:if>
                    <c:if test="${afterSalesVo.validStatus eq 1}">已生效</c:if>
                </td>

                <td>售后处理人</td>
                <td>${afterSalesVo.serviceUserName}</td>
            </tr>


            <tr>
                <td>申请人</td>
                <td>${afterSalesVo.creatorName}</td>

                <td>申请时间</td>
                <td><date:date value="${afterSalesVo.addTime}"/></td>
            </tr>

            <tr>
                <td>生效时间</td>
                <td><date:date value="${afterSalesVo.validTime}"/></td>

                <td>完结时间</td>
                <td><c:if test="${afterSalesVo.atferSalesStatus == 2}"><date:date
                        value="${afterSalesVo.modTime}"/></c:if></td>
            </tr>

            <tr>
                <td>完结/关闭原因</td>
                <td>${afterSalesVo.afterSalesStatusResonName}</td>
                <td>完结/关闭人员</td>
                <td>${afterSalesVo.afterSalesStatusUserName}</td>
            </tr>

            <tr>
                <td>完结/关闭备注</td>
                <td colspan="3" class="text-left">${afterSalesVo.afterSalesStatusComments}</td>
            </tr>
            </tbody>
        </table>
    </div>

    <%--售后信息模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                售后信息
            </div>
            <c:choose>
                <c:when test="${(afterSalesVo.atferSalesStatus eq 0 || afterSalesVo.atferSalesStatus eq 1) && afterSalesVo.status ne 1 && (user.positType eq 310 || user.positType eq 312 || user.positType eq 589)}">
               <%-- <c:when test="${afterSalesVo.status eq 0 && afterSalesVo.atferSalesStatus eq 1 && user.positType eq 312
                && ((null==taskInfoOver and null==taskInfoOver.getProcessInstanceId())or (null!=taskInfoOver and taskInfoOver.assignee==null and empty candidateUserMapOver[taskInfoOver.id]))}">--%>
                    <div class="title-click  pop-new-data" layerParams='{"width":"700px","height":"480px","title":"编辑售后信息","link":"/order/afterSalesCommon/editAfterSalesInfo.do?afterSalesId=${afterSalesVo.afterSalesId}&&areaId=${afterSalesVo.areaId}&&type=${afterSalesVo.type}"}'>编辑售后信息</div>
                </c:when>
            </c:choose>
        </div>
        <table class="table">
            <tbody>
            <tr>
                <td class="wid20">售后原因</td>
                <td>${afterSalesVo.reasonName}</td>
                <td class="wid20">第一责任部门</td>
                <td>${afterSalesVo.firstResponsibleDepartmentStr}</td>
            </tr>

            <tr>
                <td class="wid20">售后联系人</td>
                <td>
                    ${afterSalesVo.afterConnectUserName}
                </td>

                <td>售后联系人电话</td>
                <td>
                    ${afterSalesVo.afterConnectPhone}
                    <c:if test="${not empty afterSalesVo.afterConnectPhone}">
                        <i class="icontel cursor-pointer" title="点击拨号"
                           onclick="callout('${afterSalesVo.afterConnectPhone}',${afterSalesVo.traderId},1,4,${afterSalesVo.afterSalesId},${afterSalesVo.traderContactId});"></i>
                    </c:if>
                </td>
            </tr>

            <tr>
                <td class="wid20">售后报单人</td>
                <td>${afterSalesVo.traderContactName}</td>

                <td>售后报单人电话</td>
                <td>
                    ${afterSalesVo.traderContactTelephone}
                    <c:if test="${not empty afterSalesVo.traderContactTelephone}">
                        <i class="icontel cursor-pointer" title="点击拨号"
                           onclick="callout('${afterSalesVo.traderContactTelephone}',${afterSalesVo.traderId},1,4,${afterSalesVo.afterSalesId},${afterSalesVo.traderContactId});"></i>
                    </c:if>
                </td>
            </tr>

            <tr>
                <td>售后报单人手机</td>
                <td>
                    ${afterSalesVo.traderContactMobile}
                    <c:if test="${not empty afterSalesVo.traderContactMobile}">
                        <i class="icontel cursor-pointer" title="点击拨号"
                           onclick="callout('${afterSalesVo.traderContactMobile}',${afterSalesVo.traderId},1,4,${afterSalesVo.afterSalesId},${afterSalesVo.traderContactId});"></i>
                    </c:if>
                </td>
            </tr>
            <tr>
                <td>详情说明</td>
                <td colspan="3" class="text-left">${afterSalesVo.comments}</td>

            </tr>
            <tr>
                <td>附件</td>
                <td colspan="3" class="text-left">
                    <%@ include file="view_afterSales_files.jsp" %>
                </td>

            </tr>
            </tbody>
        </table>
    </div>

    <%--所属订单信息模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                所属订单信息
            </div>
        </div>
        <table class="table">
            <tbody>
            <tr>
                <td class="wid20">所属销售单号</td>
                <td>
                    <div class="customername pos_rel">
                       <span class="brand-color1 addtitle" style="float:none;"
                             tabTitle='{"num":"viewsaleorder${afterSalesVo.orderNo}","title":"订单信息",
                            "link":"./orderstream/saleorder/detail.do?saleOrderId=${afterSalesVo.orderId}"}'>${afterSalesVo.orderNo}</span><i
                            class="iconbluemouth"></i>
                        <div class="pos_abs customernameshow" style="display: none;">
                            付款状态：<c:if test="${empty afterSalesVo.amountPayStatus || afterSalesVo.amountPayStatus eq 0}">未付款</c:if>
                            <c:if test="${afterSalesVo.amountPayStatus eq 1}">部分付款</c:if>
                            <c:if test="${afterSalesVo.amountPayStatus eq 2}">全部付款</c:if><br>
                            发货状态：<c:if test="${afterSalesVo.deliveryStatus eq 0}">未发货</c:if>
                            <c:if test="${afterSalesVo.deliveryStatus eq 1}">部分发货</c:if>
                            <c:if test="${afterSalesVo.deliveryStatus eq 2}">全部发货</c:if><br>
                            开票状态：<%--<c:if test="${afterSalesVo.invoiceStatus eq 0}">未开票</c:if>
                            <c:if test="${afterSalesVo.invoiceStatus eq 1}">部分开票</c:if>
                            <c:if test="${afterSalesVo.invoiceStatus eq 2}">全部开票</c:if>--%>
                            <c:if test="${empty afterSalesVo.invoiceMakeoutStatus ||afterSalesVo.invoiceMakeoutStatus eq 0}">
                                无开票
                            </c:if>
                            <c:if test="${afterSalesVo.invoiceMakeoutStatus eq 1}">未开票</c:if>
                            <%--<c:if test="${afterSalesVo.invoiceMakeoutStatus eq 2}">部分开票</c:if>--%>
                            <c:if test="${afterSalesVo.invoiceMakeoutStatus eq 2}">全部开票</c:if>
                            <br>
                            收货状态：<c:if test="${afterSalesVo.arrivalStatus eq 0}">未收货</c:if>
                            <c:if test="${afterSalesVo.arrivalStatus eq 1}">部分收货</c:if>
                            <c:if test="${afterSalesVo.arrivalStatus eq 2}">全部收货</c:if>
                        </div>
                    </div>
                </td>
                <td class="wid20">销售单金额</td>
                <td><fmt:formatNumber type="number" value="${afterSalesVo.totalAmount}" pattern="0.00"
                                      maxFractionDigits="2"/></td>
            </tr>
            <tr>
                <td>归属销售</td>
                <td>${afterSalesVo.userName}</td>
                <td>归属部门</td>
                <td>${afterSalesVo.orgName}</td>
            </tr>
            <tr>
                <td>订单状态</td>
                <td><c:if test="${afterSalesVo.saleorderStatus eq 0}">未生效</c:if>
                    <c:if test="${afterSalesVo.saleorderStatus eq 1}">已生效</c:if></td>
                <td>生效时间</td>
                <td><date:date value="${afterSalesVo.saleorderValidTime}" /></td>
            </tr>
            <tr>
                <td>客户名称</td>
                <td>
                    <div class="customername pos_rel">
                          <span class="brand-color1 addtitle" style="float:none;"
                                tabTitle='{"num":"viewcustomer${afterSalesVo.traderId}","title":"客户信息",
                                "link":"./trader/customer/baseinfo.do?traderId=${afterSalesVo.traderId}"}'>${afterSalesVo.traderName}</span><i
                            class="iconbluemouth"></i>
                        <div class="pos_abs customernameshow" style="display: none;">
                            客户性质：<c:if test="${afterSalesVo.customerNature eq 465}">分销</c:if>
                            <c:if test="${afterSalesVo.customerNature eq 466}">终端</c:if><br>
                            交易次数：${afterSalesVo.orderCount}<br>
                            交易金额：<fmt:formatNumber type="number" value="${afterSalesVo.orderTotalAmount}" pattern="0.00"
                                                   maxFractionDigits="2"/><br>
                            上次交易日期：<date:date value="${afterSalesVo.lastOrderTime}"/>
                        </div>
                    </div>
                </td>
                <td>客户等级</td>
                <td>
                    ${afterSalesVo.customerLevelStr}
                </td>
            </tr>
            </tbody>
        </table>
    </div>

    <%--商品信息模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">商品信息</div>
        </div>
        <table class="table  table-style10">
            <thead>
            <tr>
                <th class="wid4">序号</th>
                <th class="wid18">产品信息</th>
                <th class="wid8">物料编码</th>
                <th class="wid8">所属采购订单</th>
                <th class="wid5">单价</th>
                <th class="wid8">原预定总数</th>
                <th class="wid8">总额</th>
                <th class="wid8">售后数量</th>
            </tr>
            </thead>
            <tbody>
            <c:if test="${not empty afterSalesVo.afterSalesGoodsList}">
                <c:forEach items="${afterSalesVo.afterSalesGoodsList}" var="asg" varStatus="sttaus">
                    <tr>
                        <td>${sttaus.count }</td>
                        <td class="text-left">
                            <div class="customername pos_rel">
                                <c:if test="${asg.isActionGoods==1}"><span style="color:red;">【活动】</span></c:if>
                                <c:if test="${asg.isGift == 1}">
                                    <img src="<%= basePath %>static/images/gift_icon.svg" style="width: 15px" />
                                </c:if>
                                <c:if test="${asg.isDirectPurchase == 1}">
                                    <img id="isDirectPurchase" src="<%= basePath %>static/images/promote_icon.svg" style="width: 15px" title="商城&quot;现货现价&quot;促销"/>
                                </c:if>
                                <span class="brand-color1 addtitle" style="float:none;"
                                      tabTitle='{"num":"viewgoods${list.goodsId}","link":"<%= basePath %>/goods/goods/viewbaseinfo.do?goodsId=${asg.goodsId}",
                                                "title":"产品信息"}'>${asg.goodsName}</span>
                                <br>${asg.sku}
                            </div>
                            <br>
                            <span style="font-weight: bold">品牌：</span><span>${asg.brandName}</span>
                            <br>
                            <span style="font-weight: bold">规格/型号：</span><span>${asg.materialCode}</span>
                            <br>
                            <span style="font-weight: bold">单位：</span><span>${asg.unitName}</span>
                        </td>
                        <td>${asg.model}</td>
                        <td>
                            <c:if test="${not empty asg.buyorderNos}">
                                <c:forEach items="${asg.buyorderNos}" var="buyorder">
                                    <a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewbuyorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                        "link":"./order/buyorder/viewBuyordersh.do?buyorderId=${buyorder.buyorderId}","title":"订单信息"}'>${buyorder.buyorderNo}</a><br>
                                </c:forEach>
                            </c:if>
                        </td>
                        <td><fmt:formatNumber type="number" value="${asg.saleorderPrice}" pattern="0.00" maxFractionDigits="2" /></td>
                        <td>${asg.saleorderNum}</td>
                        <td><fmt:formatNumber type="number" value="${asg.saleorderNum * asg.saleorderPrice}" pattern="0.00" maxFractionDigits="2" /></td>
                        <td>${asg.num}</td>
                    </tr>
                </c:forEach>
            </c:if>
            <c:if test="${empty afterSalesVo.afterSalesGoodsList}">
                <tr>
                    <td colspan="8">暂无记录</td>
                </tr>
            </c:if>
            </tbody>
        </table>
    </div>
    <%--<div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                商品信息
            </div>
        </div>
        <c:if test="${not empty afterSalesVo.afterSalesInstallstionVoList}">
            <c:forEach items="${afterSalesVo.afterSalesInstallstionVoList}" var="asg" varStatus="sttaus">
                <c:set var="afterSalesGoodsVoListPage" value="${asg.afterSalesGoodsVoList}"></c:set>
                &lt;%&ndash;产品信息&ndash;%&gt;
                <%@ include file="add_product_engineer_goods_common.jsp"%>
            </c:forEach>
        </c:if>
        <c:if test="${ empty afterSalesVo.afterSalesInstallstionVoList}">
        <table class="table  table-style10">
            <thead>
            <tr>
                <th class="wid5">序号</th>
                <th class="wid8">SKU</th>
                <th >产品名称</th>
                <th class="wid10">品牌</th>
                <th class="wid8">型号</th>
                <th class="wid8">物料编号</th>
                <th class="wid8">采购订单</th>
                <th class="wid5">销售价</th>
                <th class="wid8">数量</th>
                <th class="wid5">单位</th>
                <th class="wid6">售后数量</th>
            </tr>
            </thead>
            <tbody>
                <tr>
                    <td colspan="11">暂无记录!</td>
                </tr>
            </tbody>
        </table>
        </c:if>
    </div>--%>

    <%--跟进记录模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                跟进记录
            </div>
            <c:choose>
                <c:when test="${afterSalesVo.status eq 2 && afterSalesVo.atferSalesStatus eq 1 && user.positType eq 312}">
                    <div class="title-click  pop-new-data" layerParams='{"width":"600px","height":"380px","title":"新增售后跟进记录","link":"/order/afterSalesCommon/addFollowUpRecord.do?afterSalesId=${afterSalesVo.afterSalesId}"}'>新增跟进记录</div>
                </c:when>
                <c:otherwise>
                    <button type="button" class="bt-bg-style bg-light-grey bt-small" style="float: right;">新增售后跟进记录</button>
                </c:otherwise>
            </c:choose>

        </div>
        <%@ include file="add_followUp_common.jsp"%>
    </div>

    <%--支出记录模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                支出记录
            </div>
            <c:choose>
                <c:when test="${afterSalesVo.atferSalesStatus eq 1 && user.positType eq 312}">
                    <div class="title-click  pop-new-data" layerParams='{"width":"600px","height":"380px","title":"新增售后支出记录","link":"/order/afterSalesCommon/addExpenditureRecord.do?afterSalesId=${afterSalesVo.afterSalesId}"}'>新增支出记录</div>
                </c:when>
                <c:otherwise>
                    <button type="button" class="bt-bg-style bg-light-grey bt-small" style="float: right;">新增售后支出记录</button>
                </c:otherwise>
            </c:choose>
        </div>
        <%@ include file="add_expenditure_common.jsp"%>
    </div>

    <%--收入记录模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                收入记录
            </div>
            <c:choose>
                <c:when test="${afterSalesVo.atferSalesStatus eq 1 && user.positType eq 312}">
                    <div class="title-click  pop-new-data" layerParams='{"width":"600px","height":"380px","title":"新增售后收入记录","link":"/order/afterSalesCommon/addRevenueRecord.do?afterSalesId=${afterSalesVo.afterSalesId}"}'>新增收入记录</div>
                </c:when>
                <c:otherwise>
                    <button type="button" class="bt-bg-style bg-light-grey bt-small" style="float: right;">新增售后收入记录</button>
                </c:otherwise>
            </c:choose>
        </div>
        <%@ include file="add_revenue_common.jsp"%>
    </div>

    <!-- 沟通记录只能让售后看 -->
    <c:if test="${sessionScope.curr_user.positType eq 312}">
        <div class="parts">
            <div class="title-container">
                <div class="table-title nobor">
                    沟通记录
                </div>
                <c:if test="${afterSalesVo.status eq 2}">
                    <div class="title-click nobor  pop-new-data" layerParams='{"width":"850px","height":"460px","title":"新增沟通记录",
                        "link":"<%= basePath %>/aftersales/order/addCommunicatePage.do?afterSalesId=${afterSalesVo.afterSalesId}&&traderId=${afterSalesVo.traderId }&&traderType=1"}'>
                        新增
                    </div>
                </c:if>
            </div>
            <table class="table table-bordered table-striped table-condensed table-centered">
                <thead>
                <tr>
                    <th class="wid10">沟通时间</th>
                    <th class="">录音</th>
                    <th class="">联系人</th>
                    <th class="">联系方式</th>
                    <th class="">沟通方式</th>
                    <th class="wid30">沟通内容（AI分析整理）</th>
                    <th class="">操作人</th>
                    <th class="wid8">下次联系日期</th>
                    <th class="wid15">下次沟通内容</th>
                    <th class="">备注</th>
                    <th class="wid10">创建时间</th>
                    <th class="wid6">操作</th>
                </tr>
                </thead>
                <tbody>
                <c:if test="${not empty communicateList}">
                    <c:forEach items="${communicateList}" var="communicateRecord" varStatus="">
                        <tr>
                            <td><date:date value="${communicateRecord.begintime} "/>~<date:date
                                    value="${communicateRecord.endtime}" format="HH:mm:ss"/></td>
                            <td><c:if
                                    test="${not empty communicateRecord.coidUri }">${communicateRecord.communicateRecordId }</c:if></td>
                            <td>${communicateRecord.contactName}</td>
                            <td>${communicateRecord.phone}</td>
                            <td>${communicateRecord.communicateModeName}</td>
                            <td>
                                <ul class="communicatecontent ml0">
                                    <c:choose>
                                        <c:when test="${not empty communicateRecord.tag }">
                                            <c:forEach items="${communicateRecord.tag }" var="tag">
                                                <li class="bluetag" title="${tag.tagName}">${tag.tagName}</li>
                                            </c:forEach>
                                        </c:when>
                                        <c:otherwise>
                                            <li>${communicateRecord.contactContent }</li>
                                        </c:otherwise>
                                    </c:choose>
                                </ul>
                            </td>
                            <td>${communicateRecord.user.username}</td>
                            <c:choose>
                                <c:when test="${communicateRecord.isDone == 0 }">
                                    <td class="font-red">${communicateRecord.nextContactDate }</td>
                                </c:when>
                                <c:otherwise>
                                    <td>${communicateRecord.nextContactDate }</td>
                                </c:otherwise>
                            </c:choose>
                            <td>${communicateRecord.nextContactContent}</td>
                            <td>${communicateRecord.comments}</td>
                            <td><date:date value="${communicateRecord.addTime} "/></td>

                            <td class="caozuo">
                                <c:if test="${isSupply ne 1}">
                                    <c:if test="${afterSalesVo.atferSalesStatus ne 3 && afterSalesVo.atferSalesStatus ne 2 && afterSalesVo.verifyStatus ne 0}">
                                <span class="border-blue pop-new-data" layerParams='{"width":"60%","height":"63%","title":"编辑沟通记录",
                                    "link":"<%= basePath %>/aftersales/order/editcommunicate.do?orderFlag=${afterSalesVo.atferSalesStatus }&flag=${afterSalesVo.status }&communicateRecordId=${communicateRecord.communicateRecordId}&afterSalesId=${afterSalesVo.afterSalesId}&&traderId=${afterSalesVo.traderId }&&traderType=1"}'>编辑</span>
                                    </c:if>
                                </c:if>
                            </td>
                        </tr>
                    </c:forEach>
                </c:if>
                <c:if test="${empty communicateList}">
                    <!-- 查询无结果弹出 -->
                    <tr>
                        <td colspan='12'>暂无记录！</td>
                    </tr>
                </c:if>
                </tbody>
            </table>
        </div>
    </c:if>

    <%--回访记录模块--%>
    <div class="parts">
        <%@ include file="afterSales_return_visit.jsp"%>
    </div>


    <%--各项审核记录模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                各项审核记录
            </div>
        </div>
        <%@ include file="add_auditRecords_common.jsp"%>
    </div>
</div>
