package com.vedeng.goods.mapper;
import org.apache.ibatis.annotations.Param;

import com.vedeng.goods.domain.entity.DepartmentsHospital;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository("newDepartmentsHospitalMapper")
public interface DepartmentsHospitalMapper {
    /**
     * select by primary key
     *
     * @param departmentId primary key
     * @return object by primary key
     */
    DepartmentsHospital selectByPrimaryKey(Integer departmentId);

    /**
     * 获取全部科室
     *
     * @return List<DepartmentsHospital>
     */
    List<DepartmentsHospital> findAll();

    /**
     * 批量更新
     *
     * @param list 科室list
     * @return 影响数量
     */
    int updateBatchSelective(List<DepartmentsHospital> list);

    /**
     * 根据科室名称和是否删除查询
     *
     * @param departmentName 科室名称
     * @param isDelete 是否删除
     * @return List<DepartmentsHospital>
     */
    List<DepartmentsHospital> findAllByDepartmentNameAndIsDelete(@Param("departmentName")String departmentName,@Param("isDelete")Integer isDelete);


    /**
     * 查询所有未删除科室
     * @return List<DepartmentsHospital>
     */
    List<DepartmentsHospital> findAllNotDelete();
}