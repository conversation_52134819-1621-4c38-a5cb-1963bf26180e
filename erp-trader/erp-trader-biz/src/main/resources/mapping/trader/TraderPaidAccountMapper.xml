<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.TraderPaidAccountMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.TraderPaidAccountEntity">
    <!--@mbg.generated-->
    <!--@Table T_TRADER_PAID_ACCOUNT-->
    <id column="TRADER_PAID_ACCOUNT_ID" jdbcType="INTEGER" property="traderPaidAccountId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName" />
    <result column="TRADER_PAID_ACCOUNT" jdbcType="VARCHAR" property="traderPaidAccount" />
    <result column="IS_DEL" jdbcType="TINYINT" property="isDel" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    TRADER_PAID_ACCOUNT_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, 
    TRADER_NAME, TRADER_PAID_ACCOUNT, IS_DEL
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_TRADER_PAID_ACCOUNT
    where TRADER_PAID_ACCOUNT_ID = #{traderPaidAccountId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_TRADER_PAID_ACCOUNT
    where TRADER_PAID_ACCOUNT_ID = #{traderPaidAccountId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.erp.trader.domain.entity.TraderPaidAccountEntity">
    <!--@mbg.generated-->
    insert into T_TRADER_PAID_ACCOUNT (TRADER_PAID_ACCOUNT_ID, ADD_TIME, MOD_TIME, 
      CREATOR, UPDATER, CREATOR_NAME, 
      UPDATER_NAME, TRADER_NAME, TRADER_PAID_ACCOUNT, 
      IS_DEL)
    values (#{traderPaidAccountId,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, 
      #{updaterName,jdbcType=VARCHAR}, #{traderName,jdbcType=VARCHAR}, #{traderPaidAccount,jdbcType=VARCHAR}, 
      #{isDel,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.erp.trader.domain.entity.TraderPaidAccountEntity">
    <!--@mbg.generated-->
    insert into T_TRADER_PAID_ACCOUNT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="traderPaidAccountId != null">
        TRADER_PAID_ACCOUNT_ID,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="traderName != null">
        TRADER_NAME,
      </if>
      <if test="traderPaidAccount != null">
        TRADER_PAID_ACCOUNT,
      </if>
      <if test="isDel != null">
        IS_DEL,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="traderPaidAccountId != null">
        #{traderPaidAccountId,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="traderName != null">
        #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="traderPaidAccount != null">
        #{traderPaidAccount,jdbcType=VARCHAR},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.TraderPaidAccountEntity">
    <!--@mbg.generated-->
    update T_TRADER_PAID_ACCOUNT
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="traderName != null">
        TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="traderPaidAccount != null">
        TRADER_PAID_ACCOUNT = #{traderPaidAccount,jdbcType=VARCHAR},
      </if>
      <if test="isDel != null">
        IS_DEL = #{isDel,jdbcType=TINYINT},
      </if>
    </set>
    where TRADER_PAID_ACCOUNT_ID = #{traderPaidAccountId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.TraderPaidAccountEntity">
    <!--@mbg.generated-->
    update T_TRADER_PAID_ACCOUNT
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      TRADER_PAID_ACCOUNT = #{traderPaidAccount,jdbcType=VARCHAR},
      IS_DEL = #{isDel,jdbcType=TINYINT}
    where TRADER_PAID_ACCOUNT_ID = #{traderPaidAccountId,jdbcType=INTEGER}
  </update>
  <select id="findByAll" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from T_TRADER_PAID_ACCOUNT
        <where>
            <if test="traderPaidAccountId != null">
                and TRADER_PAID_ACCOUNT_ID=#{traderPaidAccountId,jdbcType=INTEGER}
            </if>
            <if test="addTime != null">
                and ADD_TIME=#{addTime,jdbcType=TIMESTAMP}
            </if>
            <if test="modTime != null">
                and MOD_TIME=#{modTime,jdbcType=TIMESTAMP}
            </if>
            <if test="creator != null">
                and CREATOR=#{creator,jdbcType=INTEGER}
            </if>
            <if test="updater != null">
                and UPDATER=#{updater,jdbcType=INTEGER}
            </if>
            <if test="creatorName != null">
                and CREATOR_NAME=#{creatorName,jdbcType=VARCHAR}
            </if>
            <if test="updaterName != null">
                and UPDATER_NAME=#{updaterName,jdbcType=VARCHAR}
            </if>
            <if test="traderName != null">
                and TRADER_NAME=#{traderName,jdbcType=VARCHAR}
            </if>
            <if test="traderPaidAccount != null">
                and TRADER_PAID_ACCOUNT=#{traderPaidAccount,jdbcType=VARCHAR}
            </if>
            <if test="isDel != null">
                and IS_DEL=#{isDel,jdbcType=TINYINT}
            </if>
        </where>
    </select>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_TRADER_PAID_ACCOUNT
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when TRADER_PAID_ACCOUNT_ID = #{item.traderPaidAccountId,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when TRADER_PAID_ACCOUNT_ID = #{item.traderPaidAccountId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when TRADER_PAID_ACCOUNT_ID = #{item.traderPaidAccountId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when TRADER_PAID_ACCOUNT_ID = #{item.traderPaidAccountId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when TRADER_PAID_ACCOUNT_ID = #{item.traderPaidAccountId,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when TRADER_PAID_ACCOUNT_ID = #{item.traderPaidAccountId,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRADER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.traderName != null">
            when TRADER_PAID_ACCOUNT_ID = #{item.traderPaidAccountId,jdbcType=INTEGER} then #{item.traderName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRADER_PAID_ACCOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.traderPaidAccount != null">
            when TRADER_PAID_ACCOUNT_ID = #{item.traderPaidAccountId,jdbcType=INTEGER} then #{item.traderPaidAccount,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DEL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDel != null">
            when TRADER_PAID_ACCOUNT_ID = #{item.traderPaidAccountId,jdbcType=INTEGER} then #{item.isDel,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
    </trim>
    where TRADER_PAID_ACCOUNT_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.traderPaidAccountId,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into T_TRADER_PAID_ACCOUNT
    (TRADER_PAID_ACCOUNT_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, 
      TRADER_NAME, TRADER_PAID_ACCOUNT, IS_DEL)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.traderPaidAccountId,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP}, 
        #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, #{item.updater,jdbcType=INTEGER}, 
        #{item.creatorName,jdbcType=VARCHAR}, #{item.updaterName,jdbcType=VARCHAR}, #{item.traderName,jdbcType=VARCHAR}, 
        #{item.traderPaidAccount,jdbcType=VARCHAR}, #{item.isDel,jdbcType=TINYINT})
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2022-08-16-->
  <select id="findByTraderNameAndTraderPaidAccountAndIsDel" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_TRADER_PAID_ACCOUNT
    where TRADER_NAME=#{traderName,jdbcType=VARCHAR} and TRADER_PAID_ACCOUNT=#{traderPaidAccount,jdbcType=VARCHAR} and
    IS_DEL=#{isDel,jdbcType=TINYINT}
  </select>

<!--auto generated by MybatisCodeHelper on 2022-08-17-->
  <select id="findByTraderPaidAccountAndIsDel" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_TRADER_PAID_ACCOUNT
        where TRADER_PAID_ACCOUNT=#{traderPaidAccount,jdbcType=VARCHAR} and IS_DEL=#{isDel,jdbcType=TINYINT}
    </select>
</mapper>