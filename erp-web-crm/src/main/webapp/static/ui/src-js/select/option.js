Vue.component('ui-option', {
    template: `
        <div v-if="!shadow">
            <li
                :class="['ui-select-option-li', disabled?'disabled':'',selected?'selected':'',select.multipleType?'multiple':'']"
                @click="selectFT"
                @mouseup="mouseUp"
            >
                <template>
                    <i class="vd-ui_icon icon-checkbox1" v-if="select.multipleType && !selected"></i>
                    <i class="vd-ui_icon icon-checkbox2" v-if="select.multipleType && selected"></i>
                    <div class="li-p li-p-made">
                        <div class="li-avatar" v-if="avatar"><img :src="avatarUrl || '/static/image/crm-user-avatar.svg'" onerror="this.classList.add('error')"/></div>
                        <p class="li-label" :title="label" v-html="showlabel || label"></p>
                        <p class="li-name" v-if="name" v-html="name" :style="'width:' + (nameWidth || '50%')"></p>
                    </div>
                </template>
            </li>
        </div>
    `,
    props: {
        value: String,
        label: String,
        name: String,
        avatar: {
            type: Boolean,
            defalut: false
        },
        avatarUrl: String,
        showlabel: String,
        nameWidth: String,
        disabled: {
            type: Boolean,
            default: false
        },
        shadows: {
            type: Boolean,
            default: false
        }
    },
    inject: {
        select: {
            default: ''
        }
    },
    computed: {
        selected() {
            if (this.select.multipleType) {
                for (let i of this.select.showVal) {
                    if (i == this.value) {
                        return true;
                    }
                }
            } else {
                if (this.select.showVal == this.value) {
                    return true;
                }
            }
        },
        shadow() {
            if (this.select.multipleType) {
                for(let i of this.select.showVal) {
                    if (i == this.value) {
                        return this.shadows;
                    }
                }
            } else {
                if (this.select.showVal == this.value) {
                    return this.shadows;
                }
            }
            return false;
        }
    },
    methods: {
        selectFT() {
            if (this.disabled) return
            if (this.select.multipleType) {
                if (this.selected) {
                    for(let i in this.select.mulSeleList) {
                        if (this.select.mulSeleList[i].value == this.value) {
                            this.select.mulSeleList.splice(i,1)
                            break
                        }
                    }
                } else {
                    this.select.mulSeleList.push({
                        label:this.label,
                        value:this.value
                    })
                }
                this.select.changeMulList();
                if (this.select.multipleType == 'fixed') {
                    this.select.tagLabelNum()
                }
            } else {
                this.select.showLabel = this.label;
                this.select.selectValue(this.value);
                this.select.selectObj = {
                    label:this.label,
                    value:this.value
                }
                this.select.changeVal(this.select.selectObj.value,this.value); // change事件
            }
        },
        mouseUp(event) {
            if (this.disabled) return;
            if (!this.select.multipleType) {
                setTimeout(() => {
                    this.select.rotate = false;
                    try {
                        this.$parent.$parent.$refs.vdInput.blur();
                    } catch (error) {
                        
                    }
                }, 100)
            };
            if ( this.select.clearable == true ) this.select.clearFlag = true;
        },
        initLabel() {
            if (this.select.multipleType) {
                let arr = new Array();
                this.select.showVal.forEach((val)=>{
                    if (val == this.value) {
                        this.select.mulSeleList.push({
                            label:this.label,
                            value:this.value
                        })
                    }
                })
                this.select.mulList = JSON.parse(JSON.stringify(this.select.mulSeleList));
            } else {
                if (this.select.showVal == this.value) {
                    this.select.showLabel = this.label;
                    this.select.selectObj = {
                        label:this.label,
                        value:this.value
                    }
                }
            }
        },
    }
})