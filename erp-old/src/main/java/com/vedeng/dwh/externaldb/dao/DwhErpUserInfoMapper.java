package com.vedeng.dwh.externaldb.dao;

import com.vedeng.dwh.externaldb.dao.generate.DwmNjUserOrgDfMapper;
import com.vedeng.dwh.model.dto.DwhErpUserDto;
import com.vedeng.dwh.model.dto.DwhQueryParamsDto;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DwhErpUserInfoMapper extends DwmNjUserOrgDfMapper {

    /**
     * 查询指定时间用户快照信息.
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/2 5:54 下午.
     * @author: Tomcat.Hui.
     * @param params: .
     * @return: com.vedeng.dwh.model.dto.DwhErpOrganizationDto.
     * @throw: .
     */
    DwhErpUserDto getErpUserSnapshotInfo(DwhQueryParamsDto params);

    /**
     * 获取全量/指定部门下所有员工.
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/3 10:38 上午.
     * @author: Tomcat.Hui.
     * @param params: .
     * @return: java.util.List<com.vedeng.dwh.model.dto.DwhErpUserDto>.
     * @throw: .
     */
    List<DwhErpUserDto> getErpUserSnapshotInfoList(DwhQueryParamsDto params);

}