package com.vedeng.goods.model;

import com.vedeng.erp.finance.dto.TaxcodeClassificationDto;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class CoreSkuGenerate {

    /**
     * DI_CODE 商品UDI-DI码 VDERP-17675 商品详情页：增加商品DI字段
     */
    private String diCode;

    public String getDiCode() {
        return diCode;
    }

    public void setDiCode(String diCode) {
        this.diCode = diCode;
    }

    private Integer serviceLife;//使用年限/单位：年

    public Integer getServiceLife() {
        return serviceLife;
    }

    public void setServiceLife(Integer serviceLife) {
        this.serviceLife = serviceLife;
    }

    // 是否可售
    private Integer isAvailableSale;

    /**
     * 采购到货时长（工作日）
     */
    private Integer perchaseTime;

    public Integer getPerchaseTime() {
        return perchaseTime;
    }

    public void setPerchaseTime(Integer perchaseTime) {
        this.perchaseTime = perchaseTime;
    }

    public Integer getIsAvailableSale() {
        return isAvailableSale;
    }

    public void setIsAvailableSale(Integer isAvailableSale) {
        this.isAvailableSale = isAvailableSale;
    }

    // add by Tomcat.Hui 2020/5/14 5:01 下午 .Desc: VDERP-2217 提供预计发货时间给前台. start

    private String deliveryRange;

    public String getDeliveryRange() {
        return deliveryRange;
    }

    public void setDeliveryRange(String deliveryRange) {
        this.deliveryRange = deliveryRange;
    }

    private String declareDeliveryRange;

    public String getDeclareDeliveryRange() {
        return declareDeliveryRange;
    }

    public void setDeclareDeliveryRange(String declareDeliveryRange) {
        this.declareDeliveryRange = declareDeliveryRange;
    }

    // add by Tomcat.Hui 2020/5/14 5:01 下午 .Desc: VDERP-2217 提供预计发货时间给前台. end

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.SKU_ID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Integer skuId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.SPU_ID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Integer spuId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.CHECK_STATUS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Integer checkStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.MODEL
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String model;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.SPEC
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String spec;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.SKU_NO
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String skuNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.SKU_NAME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String skuName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.SHOW_NAME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String showName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.MATERIAL_CODE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String materialCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.SUPPLY_MODEL
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String supplyModel;


    /**
     * 是否备货
     *
     * @deprecated 字段已废弃
     * @since ERP_LV_2020_86
     */
    @Deprecated
    private String isStockup;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.WIKI_HREF
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String wikiHref;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.TECHNICAL_PARAMETER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String technicalParameter;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.PERFORMANCE_PARAMETER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String performanceParameter;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.SPEC_PARAMETER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String specParameter;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.BASE_UNIT_ID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Integer baseUnitId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.MIN_ORDER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private BigDecimal minOrder;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.GOODS_LENGTH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private BigDecimal goodsLength;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.GOODS_WIDTH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private BigDecimal goodsWidth;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.GOODS_HEIGHT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private BigDecimal goodsHeight;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.PACKAGE_LENGTH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private BigDecimal packageLength;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.PACKAGE_WIDTH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private BigDecimal packageWidth;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.PACKAGE_HEIGHT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private BigDecimal packageHeight;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.NET_WEIGHT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private BigDecimal netWeight;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.GROSS_WEIGHT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private BigDecimal grossWeight;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.UNIT_ID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Integer unitId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.CHANGE_NUM
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Long changeNum;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.PACKING_LIST
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String packingList;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.AFTER_SALE_CONTENT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String afterSaleContent;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.QA_YEARS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String qaYears;

    /**
     * 存储条件（温度)
     *
     * @since ERP_LV_2020_67
     * @see com.vedeng.goods.enums.GoodsStorageConditionTemperatureEnum
     */
    private Integer storageConditionOne;

    /**
     * 存储条件（温度,单位：摄氏度），其他温度范围值存储较小值
     *
     * @since ERP_LV_2020_67
     */
    private Float storageConditionOneLowerValue;

    /**
     * 存储条件（温度，单位：摄氏度），其他温度范围值存储较大值
     *
     * @since ERP_LV_2020_67
     */
    private Float storageConditionOneUpperValue;

    /**
     * 存储条件（湿度，单位：%）：范围值存储较小的值
     *
     * @since ERP_LV_2020_67
     */
    private Float storageConditionHumidityLowerValue;

    /**
     * 存储条件（湿度，单位：%）：范围值存储较大的
     *
     * @since ERP_SV_2020_61
     */
    private Float storageConditionHumidityUpperValue;

    /**
     * 存储条件（其他）
     *
     * @since ERP_SV_2020_61
     * @see com.vedeng.goods.enums.GoodsStorageConditionOthersEnum
     */
    private String storageConditionTwo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.EFFECTIVE_DAY_UNIT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Integer effectiveDayUnit;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.EFFECTIVE_DAYS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
        private String effectiveDays;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.QA_RULE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String qaRule;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.QA_OUT_PRICE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private BigDecimal qaOutPrice;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.QA_RESPONSE_TIME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Long qaResponseTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.HAS_BACKUP_MACHINE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String hasBackupMachine;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.SUPPLIER_EXTEND_GUARANTEE_PRICE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private BigDecimal supplierExtendGuaranteePrice;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.CORE_PARTS_PRICE_FID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Integer corePartsPriceFid;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.RETURN_GOODS_CONDITIONS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Integer returnGoodsConditions;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.FREIGHT_INTRODUCTIONS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String freightIntroductions;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.EXCHANGE_GOODS_CONDITIONS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String exchangeGoodsConditions;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.EXCHANGE_GOODS_METHOD
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String exchangeGoodsMethod;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.GOODS_COMMENTS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String goodsComments;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.STATUS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.ADD_TIME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Date addTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.CREATOR
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Integer creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.MOD_TIME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Date modTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.UPDATER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Integer updater;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.CHECK_TIME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Date checkTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.CHECKER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Integer checker;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.OPERATE_INFO_ID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Integer operateInfoId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.DELETE_REASON
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String deleteReason;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.LAST_CHECK_REASON
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String lastCheckReason;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.TAX_CATEGORY_NO
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String taxCategoryNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.JX_MARKET_PRICE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private BigDecimal jxMarketPrice;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.JX_SALE_PRICE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private BigDecimal jxSalePrice;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.JX_FLAG
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Integer jxFlag;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU.SOURCE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Integer source;

    //推送的状态
    private Integer pushStatus;


    /**
     * 上下架状态
     *
     * @since ERP_LV_2020_56
     */
    private Integer onSale;


    /**
     * 商品级别
     *
     * @since ERP_LV_2020_105
     */
    private Integer goodsLevelNo;

    /**
     * 商品档位
     *
     * @since  ERP_LV_2020_105
     */
    private Integer goodsPositionNo;

    private String configurationList;

    private Integer hasAfterSaleServiceLabel;



    private Integer costCategoryId;

    public Integer getCostCategoryId() {
        return costCategoryId;
    }

    public void setCostCategoryId(Integer costCategoryId) {
        this.costCategoryId = costCategoryId;
    }

    /**
     * 虚拟商品归属人
     */
    private String assignments;

    /**
     * 主销售部门
     */
    private String mainDept;

    public String getMainDept() {
        return mainDept;
    }

    public void setMainDept(String mainDept) {
        this.mainDept = mainDept;
    }

    public String getAssignments() {
        return assignments;
    }

    public void setAssignments(String assignments) {
        this.assignments = assignments;
    }

    public Integer getHasAfterSaleServiceLabel() {
        return hasAfterSaleServiceLabel;
    }

    public void setHasAfterSaleServiceLabel(Integer hasAfterSaleServiceLabel) {
        this.hasAfterSaleServiceLabel = hasAfterSaleServiceLabel;
    }

    public String getConfigurationList() {
        return configurationList;
    }

    public void setConfigurationList(String configurationList) {
        this.configurationList = configurationList;
    }

    public Integer getOnSale() {
        return onSale;
    }

    public void setOnSale(Integer onSale) {
        this.onSale = onSale;
    }

    public Integer getPushStatus() {
        return pushStatus;
    }

    public void setPushStatus(Integer pushStatus) {
        this.pushStatus = pushStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.SKU_ID
     *
     * @return the value of V_CORE_SKU.SKU_ID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Integer getSkuId() {
        return skuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.SKU_ID
     *
     * @param skuId the value for V_CORE_SKU.SKU_ID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.SPU_ID
     *
     * @return the value of V_CORE_SKU.SPU_ID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Integer getSpuId() {
        return spuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.SPU_ID
     *
     * @param spuId the value for V_CORE_SKU.SPU_ID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setSpuId(Integer spuId) {
        this.spuId = spuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.CHECK_STATUS
     *
     * @return the value of V_CORE_SKU.CHECK_STATUS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Integer getCheckStatus() {
        return checkStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.CHECK_STATUS
     *
     * @param checkStatus the value for V_CORE_SKU.CHECK_STATUS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setCheckStatus(Integer checkStatus) {
        this.checkStatus = checkStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.MODEL
     *
     * @return the value of V_CORE_SKU.MODEL
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getModel() {
        return model;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.MODEL
     *
     * @param model the value for V_CORE_SKU.MODEL
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setModel(String model) {
        this.model = model;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.SPEC
     *
     * @return the value of V_CORE_SKU.SPEC
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getSpec() {
        return spec;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.SPEC
     *
     * @param spec the value for V_CORE_SKU.SPEC
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setSpec(String spec) {
        this.spec = spec;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.SKU_NO
     *
     * @return the value of V_CORE_SKU.SKU_NO
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getSkuNo() {
        return skuNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.SKU_NO
     *
     * @param skuNo the value for V_CORE_SKU.SKU_NO
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setSkuNo(String skuNo) {
        this.skuNo = skuNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.SKU_NAME
     *
     * @return the value of V_CORE_SKU.SKU_NAME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getSkuName() {
        return skuName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.SKU_NAME
     *
     * @param skuName the value for V_CORE_SKU.SKU_NAME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.SHOW_NAME
     *
     * @return the value of V_CORE_SKU.SHOW_NAME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getShowName() {
        return showName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.SHOW_NAME
     *
     * @param showName the value for V_CORE_SKU.SHOW_NAME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setShowName(String showName) {
        this.showName = showName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.MATERIAL_CODE
     *
     * @return the value of V_CORE_SKU.MATERIAL_CODE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getMaterialCode() {
        return materialCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.MATERIAL_CODE
     *
     * @param materialCode the value for V_CORE_SKU.MATERIAL_CODE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.SUPPLY_MODEL
     *
     * @return the value of V_CORE_SKU.SUPPLY_MODEL
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getSupplyModel() {
        return supplyModel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.SUPPLY_MODEL
     *
     * @param supplyModel the value for V_CORE_SKU.SUPPLY_MODEL
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setSupplyModel(String supplyModel) {
        this.supplyModel = supplyModel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.IS_STOCKUP
     *
     * @return the value of V_CORE_SKU.IS_STOCKUP
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getIsStockup() {
        return isStockup;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.IS_STOCKUP
     *
     * @param isStockup the value for V_CORE_SKU.IS_STOCKUP
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setIsStockup(String isStockup) {
        this.isStockup = isStockup;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.WIKI_HREF
     *
     * @return the value of V_CORE_SKU.WIKI_HREF
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getWikiHref() {
        return wikiHref;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.WIKI_HREF
     *
     * @param wikiHref the value for V_CORE_SKU.WIKI_HREF
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setWikiHref(String wikiHref) {
        this.wikiHref = wikiHref;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.TECHNICAL_PARAMETER
     *
     * @return the value of V_CORE_SKU.TECHNICAL_PARAMETER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getTechnicalParameter() {
        return technicalParameter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.TECHNICAL_PARAMETER
     *
     * @param technicalParameter the value for V_CORE_SKU.TECHNICAL_PARAMETER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setTechnicalParameter(String technicalParameter) {
        this.technicalParameter = technicalParameter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.PERFORMANCE_PARAMETER
     *
     * @return the value of V_CORE_SKU.PERFORMANCE_PARAMETER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getPerformanceParameter() {
        return performanceParameter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.PERFORMANCE_PARAMETER
     *
     * @param performanceParameter the value for V_CORE_SKU.PERFORMANCE_PARAMETER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setPerformanceParameter(String performanceParameter) {
        this.performanceParameter = performanceParameter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.SPEC_PARAMETER
     *
     * @return the value of V_CORE_SKU.SPEC_PARAMETER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getSpecParameter() {
        return specParameter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.SPEC_PARAMETER
     *
     * @param specParameter the value for V_CORE_SKU.SPEC_PARAMETER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setSpecParameter(String specParameter) {
        this.specParameter = specParameter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.BASE_UNIT_ID
     *
     * @return the value of V_CORE_SKU.BASE_UNIT_ID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Integer getBaseUnitId() {
        return baseUnitId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.BASE_UNIT_ID
     *
     * @param baseUnitId the value for V_CORE_SKU.BASE_UNIT_ID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setBaseUnitId(Integer baseUnitId) {
        this.baseUnitId = baseUnitId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.MIN_ORDER
     *
     * @return the value of V_CORE_SKU.MIN_ORDER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public BigDecimal getMinOrder() {
        return minOrder;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.MIN_ORDER
     *
     * @param minOrder the value for V_CORE_SKU.MIN_ORDER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setMinOrder(BigDecimal minOrder) {
        this.minOrder = minOrder;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.GOODS_LENGTH
     *
     * @return the value of V_CORE_SKU.GOODS_LENGTH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public BigDecimal getGoodsLength() {
        return goodsLength;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.GOODS_LENGTH
     *
     * @param goodsLength the value for V_CORE_SKU.GOODS_LENGTH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setGoodsLength(BigDecimal goodsLength) {
        this.goodsLength = goodsLength;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.GOODS_WIDTH
     *
     * @return the value of V_CORE_SKU.GOODS_WIDTH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public BigDecimal getGoodsWidth() {
        return goodsWidth;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.GOODS_WIDTH
     *
     * @param goodsWidth the value for V_CORE_SKU.GOODS_WIDTH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setGoodsWidth(BigDecimal goodsWidth) {
        this.goodsWidth = goodsWidth;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.GOODS_HEIGHT
     *
     * @return the value of V_CORE_SKU.GOODS_HEIGHT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public BigDecimal getGoodsHeight() {
        return goodsHeight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.GOODS_HEIGHT
     *
     * @param goodsHeight the value for V_CORE_SKU.GOODS_HEIGHT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setGoodsHeight(BigDecimal goodsHeight) {
        this.goodsHeight = goodsHeight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.PACKAGE_LENGTH
     *
     * @return the value of V_CORE_SKU.PACKAGE_LENGTH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public BigDecimal getPackageLength() {
        return packageLength;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.PACKAGE_LENGTH
     *
     * @param packageLength the value for V_CORE_SKU.PACKAGE_LENGTH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setPackageLength(BigDecimal packageLength) {
        this.packageLength = packageLength;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.PACKAGE_WIDTH
     *
     * @return the value of V_CORE_SKU.PACKAGE_WIDTH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public BigDecimal getPackageWidth() {
        return packageWidth;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.PACKAGE_WIDTH
     *
     * @param packageWidth the value for V_CORE_SKU.PACKAGE_WIDTH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setPackageWidth(BigDecimal packageWidth) {
        this.packageWidth = packageWidth;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.PACKAGE_HEIGHT
     *
     * @return the value of V_CORE_SKU.PACKAGE_HEIGHT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public BigDecimal getPackageHeight() {
        return packageHeight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.PACKAGE_HEIGHT
     *
     * @param packageHeight the value for V_CORE_SKU.PACKAGE_HEIGHT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setPackageHeight(BigDecimal packageHeight) {
        this.packageHeight = packageHeight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.NET_WEIGHT
     *
     * @return the value of V_CORE_SKU.NET_WEIGHT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public BigDecimal getNetWeight() {
        return netWeight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.NET_WEIGHT
     *
     * @param netWeight the value for V_CORE_SKU.NET_WEIGHT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setNetWeight(BigDecimal netWeight) {
        this.netWeight = netWeight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.GROSS_WEIGHT
     *
     * @return the value of V_CORE_SKU.GROSS_WEIGHT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public BigDecimal getGrossWeight() {
        return grossWeight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.GROSS_WEIGHT
     *
     * @param grossWeight the value for V_CORE_SKU.GROSS_WEIGHT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setGrossWeight(BigDecimal grossWeight) {
        this.grossWeight = grossWeight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.UNIT_ID
     *
     * @return the value of V_CORE_SKU.UNIT_ID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Integer getUnitId() {
        return unitId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.UNIT_ID
     *
     * @param unitId the value for V_CORE_SKU.UNIT_ID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.CHANGE_NUM
     *
     * @return the value of V_CORE_SKU.CHANGE_NUM
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Long getChangeNum() {
        return changeNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.CHANGE_NUM
     *
     * @param changeNum the value for V_CORE_SKU.CHANGE_NUM
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setChangeNum(Long changeNum) {
        this.changeNum = changeNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.PACKING_LIST
     *
     * @return the value of V_CORE_SKU.PACKING_LIST
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getPackingList() {
        return packingList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.PACKING_LIST
     *
     * @param packingList the value for V_CORE_SKU.PACKING_LIST
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setPackingList(String packingList) {
        this.packingList = packingList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.AFTER_SALE_CONTENT
     *
     * @return the value of V_CORE_SKU.AFTER_SALE_CONTENT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getAfterSaleContent() {
        return afterSaleContent;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.AFTER_SALE_CONTENT
     *
     * @param afterSaleContent the value for V_CORE_SKU.AFTER_SALE_CONTENT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setAfterSaleContent(String afterSaleContent) {
        this.afterSaleContent = afterSaleContent;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.QA_YEARS
     *
     * @return the value of V_CORE_SKU.QA_YEARS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getQaYears() {
        return qaYears;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.QA_YEARS
     *
     * @param qaYears the value for V_CORE_SKU.QA_YEARS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setQaYears(String qaYears) {
        this.qaYears = qaYears;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.STORAGE_CONDITION_ONE
     *
     * @return the value of V_CORE_SKU.STORAGE_CONDITION_ONE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Integer getStorageConditionOne() {
        return storageConditionOne;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.STORAGE_CONDITION_ONE
     *
     * @param storageConditionOne the value for V_CORE_SKU.STORAGE_CONDITION_ONE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setStorageConditionOne(Integer storageConditionOne) {
        this.storageConditionOne = storageConditionOne;
    }

    public Float getStorageConditionOneLowerValue() {
        return storageConditionOneLowerValue;
    }

    public void setStorageConditionOneLowerValue(Float storageConditionOneLowerValue) {
        this.storageConditionOneLowerValue = storageConditionOneLowerValue;
    }

    public Float getStorageConditionOneUpperValue() {
        return storageConditionOneUpperValue;
    }

    public void setStorageConditionOneUpperValue(Float storageConditionOneUpperValue) {
        this.storageConditionOneUpperValue = storageConditionOneUpperValue;
    }

    public Float getStorageConditionHumidityLowerValue() {
        return storageConditionHumidityLowerValue;
    }

    public void setStorageConditionHumidityLowerValue(Float storageConditionHumidityLowerValue) {
        this.storageConditionHumidityLowerValue = storageConditionHumidityLowerValue;
    }

    public Float getStorageConditionHumidityUpperValue() {
        return storageConditionHumidityUpperValue;
    }

    public void setStorageConditionHumidityUpperValue(Float storageConditionHumidityUpperValue) {
        this.storageConditionHumidityUpperValue = storageConditionHumidityUpperValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.STORAGE_CONDITION_TWO
     *
     * @return the value of V_CORE_SKU.STORAGE_CONDITION_TWO
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getStorageConditionTwo() {
        return storageConditionTwo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.STORAGE_CONDITION_TWO
     *
     * @param storageConditionTwo the value for V_CORE_SKU.STORAGE_CONDITION_TWO
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setStorageConditionTwo(String storageConditionTwo) {
        this.storageConditionTwo = storageConditionTwo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.EFFECTIVE_DAY_UNIT
     *
     * @return the value of V_CORE_SKU.EFFECTIVE_DAY_UNIT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Integer getEffectiveDayUnit() {
        return effectiveDayUnit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.EFFECTIVE_DAY_UNIT
     *
     * @param effectiveDayUnit the value for V_CORE_SKU.EFFECTIVE_DAY_UNIT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setEffectiveDayUnit(Integer effectiveDayUnit) {
        this.effectiveDayUnit = effectiveDayUnit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.EFFECTIVE_DAYS
     *
     * @return the value of V_CORE_SKU.EFFECTIVE_DAYS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getEffectiveDays() {
        return effectiveDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.EFFECTIVE_DAYS
     *
     * @param effectiveDays the value for V_CORE_SKU.EFFECTIVE_DAYS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setEffectiveDays(String effectiveDays) {
        this.effectiveDays = effectiveDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.QA_RULE
     *
     * @return the value of V_CORE_SKU.QA_RULE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getQaRule() {
        return qaRule;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.QA_RULE
     *
     * @param qaRule the value for V_CORE_SKU.QA_RULE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setQaRule(String qaRule) {
        this.qaRule = qaRule;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.QA_OUT_PRICE
     *
     * @return the value of V_CORE_SKU.QA_OUT_PRICE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public BigDecimal getQaOutPrice() {
        return qaOutPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.QA_OUT_PRICE
     *
     * @param qaOutPrice the value for V_CORE_SKU.QA_OUT_PRICE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setQaOutPrice(BigDecimal qaOutPrice) {
        this.qaOutPrice = qaOutPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.QA_RESPONSE_TIME
     *
     * @return the value of V_CORE_SKU.QA_RESPONSE_TIME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Long getQaResponseTime() {
        return qaResponseTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.QA_RESPONSE_TIME
     *
     * @param qaResponseTime the value for V_CORE_SKU.QA_RESPONSE_TIME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setQaResponseTime(Long qaResponseTime) {
        this.qaResponseTime = qaResponseTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.HAS_BACKUP_MACHINE
     *
     * @return the value of V_CORE_SKU.HAS_BACKUP_MACHINE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getHasBackupMachine() {
        return hasBackupMachine;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.HAS_BACKUP_MACHINE
     *
     * @param hasBackupMachine the value for V_CORE_SKU.HAS_BACKUP_MACHINE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setHasBackupMachine(String hasBackupMachine) {
        this.hasBackupMachine = hasBackupMachine;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.SUPPLIER_EXTEND_GUARANTEE_PRICE
     *
     * @return the value of V_CORE_SKU.SUPPLIER_EXTEND_GUARANTEE_PRICE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public BigDecimal getSupplierExtendGuaranteePrice() {
        return supplierExtendGuaranteePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.SUPPLIER_EXTEND_GUARANTEE_PRICE
     *
     * @param supplierExtendGuaranteePrice the value for V_CORE_SKU.SUPPLIER_EXTEND_GUARANTEE_PRICE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setSupplierExtendGuaranteePrice(BigDecimal supplierExtendGuaranteePrice) {
        this.supplierExtendGuaranteePrice = supplierExtendGuaranteePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.CORE_PARTS_PRICE_FID
     *
     * @return the value of V_CORE_SKU.CORE_PARTS_PRICE_FID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Integer getCorePartsPriceFid() {
        return corePartsPriceFid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.CORE_PARTS_PRICE_FID
     *
     * @param corePartsPriceFid the value for V_CORE_SKU.CORE_PARTS_PRICE_FID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setCorePartsPriceFid(Integer corePartsPriceFid) {
        this.corePartsPriceFid = corePartsPriceFid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.RETURN_GOODS_CONDITIONS
     *
     * @return the value of V_CORE_SKU.RETURN_GOODS_CONDITIONS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Integer getReturnGoodsConditions() {
        return returnGoodsConditions;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.RETURN_GOODS_CONDITIONS
     *
     * @param returnGoodsConditions the value for V_CORE_SKU.RETURN_GOODS_CONDITIONS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setReturnGoodsConditions(Integer returnGoodsConditions) {
        this.returnGoodsConditions = returnGoodsConditions;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.FREIGHT_INTRODUCTIONS
     *
     * @return the value of V_CORE_SKU.FREIGHT_INTRODUCTIONS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getFreightIntroductions() {
        return freightIntroductions;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.FREIGHT_INTRODUCTIONS
     *
     * @param freightIntroductions the value for V_CORE_SKU.FREIGHT_INTRODUCTIONS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setFreightIntroductions(String freightIntroductions) {
        this.freightIntroductions = freightIntroductions;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.EXCHANGE_GOODS_CONDITIONS
     *
     * @return the value of V_CORE_SKU.EXCHANGE_GOODS_CONDITIONS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getExchangeGoodsConditions() {
        return exchangeGoodsConditions;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.EXCHANGE_GOODS_CONDITIONS
     *
     * @param exchangeGoodsConditions the value for V_CORE_SKU.EXCHANGE_GOODS_CONDITIONS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setExchangeGoodsConditions(String exchangeGoodsConditions) {
        this.exchangeGoodsConditions = exchangeGoodsConditions;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.EXCHANGE_GOODS_METHOD
     *
     * @return the value of V_CORE_SKU.EXCHANGE_GOODS_METHOD
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getExchangeGoodsMethod() {
        return exchangeGoodsMethod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.EXCHANGE_GOODS_METHOD
     *
     * @param exchangeGoodsMethod the value for V_CORE_SKU.EXCHANGE_GOODS_METHOD
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setExchangeGoodsMethod(String exchangeGoodsMethod) {
        this.exchangeGoodsMethod = exchangeGoodsMethod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.GOODS_COMMENTS
     *
     * @return the value of V_CORE_SKU.GOODS_COMMENTS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getGoodsComments() {
        return goodsComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.GOODS_COMMENTS
     *
     * @param goodsComments the value for V_CORE_SKU.GOODS_COMMENTS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setGoodsComments(String goodsComments) {
        this.goodsComments = goodsComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.STATUS
     *
     * @return the value of V_CORE_SKU.STATUS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.STATUS
     *
     * @param status the value for V_CORE_SKU.STATUS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.ADD_TIME
     *
     * @return the value of V_CORE_SKU.ADD_TIME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Date getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.ADD_TIME
     *
     * @param addTime the value for V_CORE_SKU.ADD_TIME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.CREATOR
     *
     * @return the value of V_CORE_SKU.CREATOR
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Integer getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.CREATOR
     *
     * @param creator the value for V_CORE_SKU.CREATOR
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.MOD_TIME
     *
     * @return the value of V_CORE_SKU.MOD_TIME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Date getModTime() {
        return modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.MOD_TIME
     *
     * @param modTime the value for V_CORE_SKU.MOD_TIME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setModTime(Date modTime) {
        this.modTime = modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.UPDATER
     *
     * @return the value of V_CORE_SKU.UPDATER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Integer getUpdater() {
        return updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.UPDATER
     *
     * @param updater the value for V_CORE_SKU.UPDATER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.CHECK_TIME
     *
     * @return the value of V_CORE_SKU.CHECK_TIME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Date getCheckTime() {
        return checkTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.CHECK_TIME
     *
     * @param checkTime the value for V_CORE_SKU.CHECK_TIME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setCheckTime(Date checkTime) {
        this.checkTime = checkTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.CHECKER
     *
     * @return the value of V_CORE_SKU.CHECKER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Integer getChecker() {
        return checker;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.CHECKER
     *
     * @param checker the value for V_CORE_SKU.CHECKER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setChecker(Integer checker) {
        this.checker = checker;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.OPERATE_INFO_ID
     *
     * @return the value of V_CORE_SKU.OPERATE_INFO_ID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Integer getOperateInfoId() {
        return operateInfoId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.OPERATE_INFO_ID
     *
     * @param operateInfoId the value for V_CORE_SKU.OPERATE_INFO_ID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setOperateInfoId(Integer operateInfoId) {
        this.operateInfoId = operateInfoId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.DELETE_REASON
     *
     * @return the value of V_CORE_SKU.DELETE_REASON
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getDeleteReason() {
        return deleteReason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.DELETE_REASON
     *
     * @param deleteReason the value for V_CORE_SKU.DELETE_REASON
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setDeleteReason(String deleteReason) {
        this.deleteReason = deleteReason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.LAST_CHECK_REASON
     *
     * @return the value of V_CORE_SKU.LAST_CHECK_REASON
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getLastCheckReason() {
        return lastCheckReason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.LAST_CHECK_REASON
     *
     * @param lastCheckReason the value for V_CORE_SKU.LAST_CHECK_REASON
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setLastCheckReason(String lastCheckReason) {
        this.lastCheckReason = lastCheckReason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.TAX_CATEGORY_NO
     *
     * @return the value of V_CORE_SKU.TAX_CATEGORY_NO
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getTaxCategoryNo() {
        return taxCategoryNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.TAX_CATEGORY_NO
     *
     * @param taxCategoryNo the value for V_CORE_SKU.TAX_CATEGORY_NO
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setTaxCategoryNo(String taxCategoryNo) {
        this.taxCategoryNo = taxCategoryNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.JX_MARKET_PRICE
     *
     * @return the value of V_CORE_SKU.JX_MARKET_PRICE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public BigDecimal getJxMarketPrice() {
        return jxMarketPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.JX_MARKET_PRICE
     *
     * @param jxMarketPrice the value for V_CORE_SKU.JX_MARKET_PRICE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setJxMarketPrice(BigDecimal jxMarketPrice) {
        this.jxMarketPrice = jxMarketPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.JX_SALE_PRICE
     *
     * @return the value of V_CORE_SKU.JX_SALE_PRICE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public BigDecimal getJxSalePrice() {
        return jxSalePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.JX_SALE_PRICE
     *
     * @param jxSalePrice the value for V_CORE_SKU.JX_SALE_PRICE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setJxSalePrice(BigDecimal jxSalePrice) {
        this.jxSalePrice = jxSalePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.JX_FLAG
     *
     * @return the value of V_CORE_SKU.JX_FLAG
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Integer getJxFlag() {
        return jxFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.JX_FLAG
     *
     * @param jxFlag the value for V_CORE_SKU.JX_FLAG
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setJxFlag(Integer jxFlag) {
        this.jxFlag = jxFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU.SOURCE
     *
     * @return the value of V_CORE_SKU.SOURCE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Integer getSource() {
        return source;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU.SOURCE
     *
     * @param source the value for V_CORE_SKU.SOURCE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setSource(Integer source) {
        this.source = source;
    }

    /*
    商品条形码
     */
    private String goodsBarcode;

    /*
    养护类型
     */
    private Integer curingType;
    /*
    养护原因
     */
    @Deprecated
    private String curingReason;

    /**
     * 定期维护类型：0不维护，1一般维护，2定期维护
     *
     *  @since ERP_LV_2020_98
     */
    private Integer regularMaintainType;

    /**
     * 定期维护原因
     *
     * @since ERP_LV_2020_98
     */
    private String regularMaintainReason;

    /*
    是否必须检测报告
     */
    private Integer isNeedTestReprot;
    /*
    是否套件
     */
    private Integer isKit;
    /*
    有哪几个套件
     */
    private String kitDesc;
    /*
    套件中子件的SN码是否必须一致
     */
    private Integer isSameSnCode;
    /*
    是否厂家赋SN码
     */
    private Integer isFactorySnCode;
    /*
    是否管理贝登码
     */
    private Integer isManageVedengCode;
    /*
    是否异形品
     */
    private Integer isBadGoods;
    /*
    是否启用厂家批号
     */
    private Integer isEnableFactoryBatchnum;

    /*
    是否启用多级包装
     */
    private Integer isEnableMultistagePackage;
    /*
    中包装数量
     */
    private Integer midPackageNum;
    /*
    箱包装数量
     */
    private Integer boxPackageNum;
    /**
     * 是否启用效期
     */
    private Integer isEnableValidityPeriod;
    /*
    近效期预警天数
     */
    private Integer nearTermWarnDays;
    /*
    超近效期预警天数
     */
    private Integer overNearTermWarnDays;
    /*
    安装培训
   */
    private Integer installTrainType;

    /*
    物流安装
     */
    private Integer logisticsDeliveryType;

    /**
     * '是否需报备 0否 1是'
     */
    private Integer isNeedReport;

    /**
     * 是否获得授权 0否 1是
     */
    private  Integer isAuthorized;

    /**
     * 历史名称
     */
    private String historyName;


    private Integer isNameChange;

    /**
     * 产品是否可安装
     */
    private Integer isInstallable;


    /**
     * 同步状态 0 未推送  1 已推送 2 需重推
     */
    private Integer synchronizationStatus;

    /**
     * 检查机构等级ids，字典表查出，使用,分隔
     */
    private String institutionLevelIds;

    /**
     * 检查机构等级，字典表查出
     */
    private String institutionLevel;

    /**
     * 禁用原因
     */
    private String disabledReason;


    public String getDisabledReason() {
        return disabledReason;
    }

    public void setDisabledReason(String disabledReason) {
        this.disabledReason = disabledReason;
    }

    /**
     *  已经推送的区域商城id集合
     */
    private String pushedOrgIdList;

    /**
     * 采购到货时长(工作日）
     */
    private String purchaseTime ;

    /**
     * 采购到货时长最近更新时间
     */
    private Date  purchaseTimeUpdateTime;

    private Integer priceVerifyStatus;

    /**
     * 是否直发
     */
    private Integer isDirect;

    /**
     * 税收编码操作记录
     */
    private String taxCategoryNoRecord;

    /**
     * 税收编码简称
     */
    private String taxCodeSimpleName;

    /**
     * 税收编码信息
     */
    private TaxcodeClassificationDto taxcodeClassificationDto;

    public TaxcodeClassificationDto getTaxcodeClassificationDto() {
        return taxcodeClassificationDto;
    }

    public void setTaxcodeClassificationDto(TaxcodeClassificationDto taxcodeClassificationDto) {
        this.taxcodeClassificationDto = taxcodeClassificationDto;
    }

    public String getTaxCodeSimpleName() {
        return taxCodeSimpleName;
    }

    public void setTaxCodeSimpleName(String taxCodeSimpleName) {
        this.taxCodeSimpleName = taxCodeSimpleName;
    }

    public String getTaxCategoryNoRecord() {
        return taxCategoryNoRecord;
    }

    public void setTaxCategoryNoRecord(String taxCategoryNoRecord) {
        this.taxCategoryNoRecord = taxCategoryNoRecord;
    }

    public Integer getIsDirect() {
        return isDirect;
    }

    public void setIsDirect(Integer isDirect) {
        this.isDirect = isDirect;
    }

    public Integer getPriceVerifyStatus() {
        return priceVerifyStatus;
    }

    public void setPriceVerifyStatus(Integer priceVerifyStatus) {
        this.priceVerifyStatus = priceVerifyStatus;
    }

    private Integer logisticsDeliverytype;

    public Integer getLogisticsDeliverytype() {
        return logisticsDeliverytype;
    }

    public void setLogisticsDeliverytype(Integer logisticsDeliverytype) {
        this.logisticsDeliverytype = logisticsDeliverytype;
    }

    private BigDecimal avgprice;

    public BigDecimal getAvgprice() {
        return avgprice;
    }

    public void setAvgprice(BigDecimal avgprice) {
        this.avgprice = avgprice;
    }

    private Integer latestValidOrderUser;

    public Integer getLatestValidOrderUser() {
        return latestValidOrderUser;
    }

    public void setLatestValidOrderUser(Integer latestValidOrderUser) {
        this.latestValidOrderUser = latestValidOrderUser;
    }

    private Date avgpriceUpdateTime;

    public Date getAvgpriceUpdateTime() {
        return avgpriceUpdateTime;
    }

    public void setAvgpriceUpdateTime(Date avgpriceUpdateTime) {
        this.avgpriceUpdateTime = avgpriceUpdateTime;
    }

    private BigDecimal terminalPrice;

    public BigDecimal getTerminalPrice() {
        return terminalPrice;
    }

    public void setTerminalPrice(BigDecimal terminalPrice) {
        this.terminalPrice = terminalPrice;
    }

    private BigDecimal distributionPrice;

    public BigDecimal getDistributionPrice() {
        return distributionPrice;
    }

    public void setDistributionPrice(BigDecimal distributionPrice) {
        this.distributionPrice = distributionPrice;
    }

    private Integer availableStockNum;

    public Integer getAvailableStockNum() {
        return availableStockNum;
    }

    public void setAvailableStockNum(Integer availableStockNum) {
        this.availableStockNum = availableStockNum;
    }

    private Integer stockNum;

    public Integer getStockNum() {
        return stockNum;
    }

    public void setStockNum(Integer stockNum) {
        this.stockNum = stockNum;
    }

    public Integer getOneYearSaleNum() {
        return oneYearSaleNum;
    }

    public void setOneYearSaleNum(Integer oneYearSaleNum) {
        this.oneYearSaleNum = oneYearSaleNum;
    }

    private Integer oneYearSaleNum;

    public BigDecimal getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

    private BigDecimal costPrice;

    public Integer getLastYearRatioEightySort() {
        return lastYearRatioEightySort;
    }

    public void setLastYearRatioEightySort(Integer lastYearRatioEightySort) {
        this.lastYearRatioEightySort = lastYearRatioEightySort;
    }

    private Integer lastYearRatioEightySort;

    private Integer threeMonthRatioEightySort;

    public Integer getThreeMonthRatioEightySort() {
        return threeMonthRatioEightySort;
    }

    public void setThreeMonthRatioEightySort(Integer threeMonthRatioEightySort) {
        this.threeMonthRatioEightySort = threeMonthRatioEightySort;
    }

    private Integer oneMonthRatioEightySort;

    public Integer getOneMonthRatioEightySort() {
        return oneMonthRatioEightySort;
    }

    public void setOneMonthRatioEightySort(Integer oneMonthRatioEightySort) {
        this.oneMonthRatioEightySort = oneMonthRatioEightySort;
    }

    private Integer actionLockNum;

    public Integer getActionLockNum() {
        return actionLockNum;
    }

    public void setActionLockNum(Integer actionLockNum) {
        this.actionLockNum = actionLockNum;
    }

    private Integer orderOccupyNum;

    public Integer getOrderOccupyNum() {
        return orderOccupyNum;
    }

    public void setOrderOccupyNum(Integer orderOccupyNum) {
        this.orderOccupyNum = orderOccupyNum;
    }

    private Integer spuType;

    public Integer getSpuType() {
        return spuType;
    }

    public void setSpuType(Integer spuType) {
        this.spuType = spuType;
    }

    private Integer occupyNum;

    public Integer getOccupyNum() {
        return occupyNum;
    }

    public void setOccupyNum(Integer occupyNum) {
        this.occupyNum = occupyNum;
    }

    private Integer isSeven;

    public Integer getIsSeven() {
        return isSeven;
    }

    public void setIsSeven(Integer isSeven) {
        this.isSeven = isSeven;
    }

    public String getPurchaseTime() {
        return purchaseTime;
    }

    public void setPurchaseTime(String purchaseTime) {
        this.purchaseTime = purchaseTime;
    }

    public Date getPurchaseTimeUpdateTime() {
        return purchaseTimeUpdateTime;
    }

    public void setPurchaseTimeUpdateTime(Date purchaseTimeUpdateTime) {
        this.purchaseTimeUpdateTime = purchaseTimeUpdateTime;
    }

    // 区域商城Id集合（用于和前端交互）
    private List<Integer> orgIdArray;

    public List<Integer> getOrgIdArray() {
        return orgIdArray;
    }

    public void setOrgIdArray(List<Integer> orgIdArray) {
        this.orgIdArray = orgIdArray;
    }

    // 区域商城Id串（用逗号隔开，用于存库）
    private String orgIdList;

    public String getOrgIdList() {
        return orgIdList;
    }

    public void setOrgIdList(String orgIdList) {
        this.orgIdList = orgIdList;
    }

    // 区域商城名称集合（用于详情/审核页面回显）
    private String orgNameList;

    public String getOrgNameList() {
        return orgNameList;
    }

    public void setOrgNameList(String orgNameList) {
        this.orgNameList = orgNameList;
    }

    public Integer getSynchronizationStatus() {
        return synchronizationStatus;
    }

    public void setSynchronizationStatus(Integer synchronizationStatus) {
        this.synchronizationStatus = synchronizationStatus;
    }

    public Integer getIsNameChange() {
        return isNameChange;
    }

    public void setIsNameChange(Integer isNameChange) {
        this.isNameChange = isNameChange;
    }

    public String getHistoryName() {
        return historyName;
    }

    public void setHistoryName(String historyName) {
        this.historyName = historyName;
    }

    public Integer getIsNeedReport() {
        return isNeedReport;
    }

    public void setIsNeedReport(Integer isNeedReport) {
        this.isNeedReport = isNeedReport;
    }

    public Integer getIsAuthorized() {
        return isAuthorized;
    }

    public void setIsAuthorized(Integer isAuthorized) {
        this.isAuthorized = isAuthorized;
    }

    public Integer getIsEnableValidityPeriod() {
        return isEnableValidityPeriod;
    }

    public void setIsEnableValidityPeriod(Integer isEnableValidityPeriod) {
        this.isEnableValidityPeriod = isEnableValidityPeriod;
    }

    public Integer getInstallTrainType() {
        return installTrainType;
    }

    public void setInstallTrainType(Integer installTrainType) {
        this.installTrainType = installTrainType;
    }

    public Integer getLogisticsDeliveryType() {
        return logisticsDeliveryType;
    }

    public void setLogisticsDeliveryType(Integer logisticsDeliveryType) {
        this.logisticsDeliveryType = logisticsDeliveryType;
    }

    public Integer getIsEnableMultistagePackage() {
        return isEnableMultistagePackage;
    }

    public void setIsEnableMultistagePackage(Integer isEnableMultistagePackage) {
        this.isEnableMultistagePackage = isEnableMultistagePackage;
    }

    public Integer getMidPackageNum() {
        return midPackageNum;
    }

    public void setMidPackageNum(Integer midPackageNum) {
        this.midPackageNum = midPackageNum;
    }

    public Integer getBoxPackageNum() {
        return boxPackageNum;
    }

    public void setBoxPackageNum(Integer boxPackageNum) {
        this.boxPackageNum = boxPackageNum;
    }

    public Integer getNearTermWarnDays() {
        return nearTermWarnDays;
    }

    public void setNearTermWarnDays(Integer nearTermWarnDays) {
        this.nearTermWarnDays = nearTermWarnDays;
    }

    public Integer getOverNearTermWarnDays() {
        return overNearTermWarnDays;
    }

    public void setOverNearTermWarnDays(Integer overNearTermWarnDays) {
        this.overNearTermWarnDays = overNearTermWarnDays;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public Integer getCuringType() {
        return curingType;
    }

    public void setCuringType(Integer curingType) {
        this.curingType = curingType;
    }

    public String getCuringReason() {
        return curingReason;
    }

    public void setCuringReason(String curingReason) {
        this.curingReason = curingReason;
    }

    public Integer getIsNeedTestReprot() {
        return isNeedTestReprot;
    }

    public void setIsNeedTestReprot(Integer isNeedTestReprot) {
        this.isNeedTestReprot = isNeedTestReprot;
    }

    public Integer getIsKit() {
        return isKit;
    }

    public void setIsKit(Integer isKit) {
        this.isKit = isKit;
    }

    public String getKitDesc() {
        return kitDesc;
    }

    public void setKitDesc(String kitDesc) {
        this.kitDesc = kitDesc;
    }

    public Integer getIsSameSnCode() {
        return isSameSnCode;
    }

    public void setIsSameSnCode(Integer isSameSnCode) {
        this.isSameSnCode = isSameSnCode;
    }

    public Integer getIsFactorySnCode() {
        return isFactorySnCode;
    }

    public void setIsFactorySnCode(Integer isFactorySnCode) {
        this.isFactorySnCode = isFactorySnCode;
    }

    public Integer getIsManageVedengCode() {
        return isManageVedengCode;
    }

    public void setIsManageVedengCode(Integer isManageVedengCode) {
        this.isManageVedengCode = isManageVedengCode;
    }

    public Integer getIsBadGoods() {
        return isBadGoods;
    }

    public void setIsBadGoods(Integer isBadGoods) {
        this.isBadGoods = isBadGoods;
    }

    public Integer getIsEnableFactoryBatchnum() {
        return isEnableFactoryBatchnum;
    }

    public void setIsEnableFactoryBatchnum(Integer isEnableFactoryBatchnum) {
        this.isEnableFactoryBatchnum = isEnableFactoryBatchnum;
    }

    public Integer getRegularMaintainType() {
        return regularMaintainType;
    }

    public void setRegularMaintainType(Integer regularMaintainType) {
        this.regularMaintainType = regularMaintainType;
    }

    public String getRegularMaintainReason() {
        return regularMaintainReason;
    }

    public void setRegularMaintainReason(String regularMaintainReason) {
        this.regularMaintainReason = regularMaintainReason;
    }

    public Integer getGoodsLevelNo() {
        return goodsLevelNo;
    }

    public void setGoodsLevelNo(Integer goodsLevelNo) {
        this.goodsLevelNo = goodsLevelNo;
    }

    public Integer getGoodsPositionNo() {
        return goodsPositionNo;
    }

    public void setGoodsPositionNo(Integer goodsPositionNo) {
        this.goodsPositionNo = goodsPositionNo;
    }

    public Integer getIsInstallable() {
        return isInstallable;
    }

    public void setIsInstallable(Integer isInstallable) {
        this.isInstallable = isInstallable;
    }

    public String getPushedOrgIdList() {
        return pushedOrgIdList;
    }

    public void setPushedOrgIdList(String pushedOrgIdList) {
        this.pushedOrgIdList = pushedOrgIdList;
    }

    public String getInstitutionLevelIds() {
        return institutionLevelIds;
    }

    public void setInstitutionLevelIds(String institutionLevelIds) {
        this.institutionLevelIds = institutionLevelIds;
    }

    public String getInstitutionLevel() {
        return institutionLevel;
    }

    public void setInstitutionLevel(String institutionLevel) {
        this.institutionLevel = institutionLevel;
    }
}