package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.CapitalBillKingDeeCreate;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CapitalBillKingDeeCreateMapper {
    int deleteByPrimaryKey(Integer capitalBillKingDeeCreateId);

    int insert(CapitalBillKingDeeCreate record);

    int insertSelective(CapitalBillKingDeeCreate record);

    CapitalBillKingDeeCreate selectByPrimaryKey(Integer capitalBillKingDeeCreateId);

    List<CapitalBillKingDeeCreate> selectByOrderNoAndType(@Param("orderNo") String orderNo,@Param("orderType") Integer orderType);

    int updateByPrimaryKeySelective(CapitalBillKingDeeCreate record);

    int updateByPrimaryKey(CapitalBillKingDeeCreate record);

    int batchInsert(@Param("list") List<CapitalBillKingDeeCreate> list);
}