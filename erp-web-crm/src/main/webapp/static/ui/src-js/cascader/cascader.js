var Minxin_cascader_transition = {
    mounted() {
        window.addEventListener('scroll',this.cascaderListScroll,true) //改为true是从外到里，事件捕获，false是从里到外，事件冒泡
        window.addEventListener('resize',this.cascaderListScroll)
    },
    methods: {
        beforeLeave(el) {
            el.style.webkitTransform = 'scale(1,1)';
            el.style.opacity = 1;
        },
        leave(el) {
            if (el.scrollHeight !== 0) {
                el.style.transition = '0.19s all ease-in';
                el.style.webkitTransform = 'scale(1,0)';
                el.style.opacity = 0;
            }
        },
        afterLeave(el) {
            el.style.transition = '';
            el.style.webkitTransform = '';
            el.style.opacity = '';
        },
        topScroll() {
            let client = this.$el.getBoundingClientRect();
            let height = this.$refs.cascaderList && this.$refs.cascaderList.clientHeight;
            let clientHeight = document.body.clientHeight;
            if (client.bottom + height + 7 > clientHeight && client.top >= height+2) {
                this.animation = 'appear-up';
                if (this.$refs.cascaderList) {
                    this.$refs.cascaderList.style.top = `-${height+2}px`;
                    this.$refs.cascaderList.style.boxShadow = 'rgba(0, 0, 0, 0.1) 0px -5px 10px';
                }
            } else {
                this.animation = 'appear-down';
                if (this.$refs.cascaderList) {
                    this.$refs.cascaderList.style.top = "";
                    this.$refs.cascaderList.style.boxShadow = '';
                }
            }
        },
        cascaderListScroll() {
            if (this.$refs.cascaderList) {
                this.topScroll()
            }
        },
    }
}

Vue.component('ui-cascader', {
    template: `
        <div 
            class="vd-ui-cascader" 
            :style="{'width':width}" 
            @mouseenter="handleEnter" 
            @mouseleave="handleLeave" 
        >
            <div 
                class="vd-ui-cascader-wrapper" 
                @click="handleClick" 
            >
                <div v-if="multiple" class="multiple-wrap">
                    <div v-if="multipleTags.length" class="multiple-tags">
                        <span class="tag text-line-1">{{oneTagShow.show}}
                            <span class="tag-del" @click="clearSelect(oneTagShow.value)"><i class="vd-ui_icon icon-delete"></i></span>
                        </span>
                        <span class="tag-add" v-if="multipleTags.length > 1">+{{multipleTags.length-1}}</span>
                    </div>
                    <div class="input">
                        <input 
                            v-model="multipleInput"
                            :placeholder="multipleTags.length? '': holder"
                            :disabled="disabled"
                            @focus="focus" 
                            @blur="blur" 
                            @input="nativeOninput"
                            @keydown.tab="rotate=false" 
                            :readonly="!filterable" 
                            autocomplete="off" 
                        />
                    </div>
                    <div class="vd-ui-input__suffix" :class="{clear: clearable && multipleTags.length}">
                        <span class="vd-ui-input__suffix-inner">
                            <i v-if="clearable && multipleTags.length" class="vd-ui_icon icon-error2" @click.prevent.stop="clearAllSelect"></i>
                            <i v-else-if="filterable" class="vd-ui_icon icon-search icon"></i>
                            <i v-else class="j2 vd-ui_icon icon-down icon" :class="[rotate? 'rotate': '']"></i>
                        </span>
                    </div>
                </div>
                <template v-else>
                    <ui-input 
                        v-model="showLabel" 
                        :placeholder="holder" 
                        :disabled="disabled" 
                        @focus="focus" 
                        @blur="blur" 
                        @input="oninput"
                        @clear="clearData" 
                        @keydown.native.tab="rotate=false" 
                        width="100%"
                        :readonly="!filterable" 
                        autocomplete="off" 
                        :clearable="clearable" 
                        :select-clear="selectClear" 
                        ref="searchInput"
                    >
                        <template v-if="filterable">
                            <i slot="suffix" class="vd-ui_icon icon-search icon" v-if="iconClear"></i>
                        </template>
                        <template v-else>
                            <i slot="suffix" class="vd-ui_icon icon-down icon" v-if="iconClear" :class="[rotate? 'rotate': '']"></i>
                        </template>
                    </ui-input>
                </template>
            </div>
            <div class="vd-ui-input-error" v-if="errorable && !disabled">
                <i class="vd-ui_icon icon-error2 vd-ui-input-error--icon"></i>
                <p class="vd-ui-input-error--errmsg">{{errorMsg}}</p>
            </div>
            <transition 
                @before-leave="beforeLeave"
                @leave="leave"
                @after-leave="afterLeave"
            >
                <div v-if="rotate" class="vd-ui-cascader-menu" ref="cascaderList" :class="[animation ? animation:'', {'width': filtering}]" :style="isRight?'right:0;':''" @click.stop>
                    <div v-if="filtering" class="suggestion-list">
                        <template v-if="loading">
                            <div class="loading-li" :style="{'width':width}">
                                <p>
                                    <i class="vd-ui_icon icon-loading" ref="loading"></i>
                                    <span>加载中...</span>
                                </p>
                            </div>
                        </template>
                        <div v-if="filterResult.length" class="filter-list">
                            <div 
                                class="filter-item" 
                                v-for="(item, index) in filterResult" :key="index"
                                @click.stop="handleSuggestionClick(item)"
                            >
                                <div v-if="multiple" class="ui-cascader-checkbox-wrap" :class="{'active': isCheck(item)}">
                                    <i class="vd-ui_icon icon-selected2" v-if="isCheck(item)"></i>
                                </div>
                                <div v-html="suggestShow(item)"></div>
                            </div>
                        </div>
                        <div class="no-filter" v-else>无匹配数据</div>
                    </div>
                    <div v-else-if="multiple">
                        <ui-cascader-multiple
                            ref="multiplPanel"
                            :data="list"
                            :selectObj="selectObj"
                            :styles="style"
                            :multiple="multiple"
                            @open="checkPosition"
                            @pick="handleMultipleChange"
                        ></ui-cascader-multiple>
                    </div>
                    <div v-else>
                        <ui-cascader-panel
                            :data="list"
                            :selectObj="selectObj"
                            :styles="style"
                            :each="each"
                            @pick="handleChange"
                        ></ui-cascader-panel>
                    </div>
                </div>
            </transition>
        </div>
    `,

    mixins: [Minxin_cascader_transition],
    props: {
        placeholder: {
            type: String,
            default: '请选择'
        },
        data: {
            type:Array,
            default:()=> {
                return []
            }
        },
        showVal: {
            default: '',
        },
        disabled: {
            type: Boolean,
            default: false
        },
        width: {
            type: String,
            default: '300px'
        },
        maxHeight: {
            type: String,
            default: '300px'
        },
        listWidth: {
            type: String,
            default: '150px'
        },
        clearable: {
            type: Boolean,
            default: false
        },
        // 可搜索
        filterable: {
            type: Boolean,
            default: false
        },
        // 多选
        multiple: {
            type: Boolean,
            default: false
        },
        errorMsg: {
            type: String,
            default: ''
        },
        errorable: {
            type: Boolean,
            default: false
        },
        valid: {
            type: String,
            default: ''
        },
        needTrigger: {
            type: Boolean,
            default: false, //值改变后是否重新触发change
        },
        //是否每一级都可选
        each: {
            type: Boolean,
            default: false
        }
    },
    model: {
        prop:'showVal',
        event:'value'
    },
    provide() {
        return {
            casca: this
        }
    },
    data() {
        return {
            rotate: false,     // 下拉框
            showLabel: '',
            animation: '',
            holder: this.placeholder,
            selectObj: [],    // 选中的对象
            entered: false,   // 鼠标进入选择框为true
            clearFlag: false,
            liList: [],

            filterResult: [],
            filtering: false,
            loading: false, // 正在搜索
            multipleTags: [], // 多选结果
            multipleInput: '',
            isRight: false,
        }
    },
    computed: {
        // 多选时 首个tag展示
        oneTagShow () {
            let tag = this.multipleTags[0] || [];
            if (!tag.length) return {show: ''};

            let l1 = this.list.filter(item => item.value == tag[0])[0] || {};
            let name = l1.label || '';
            let tagValue = [l1.value];
            if (tag[1]) {
                let l2 = l1.children.filter(item => item.value == tag[1])[0] || {};
                name += ` / ${l2.label}`;
                tagValue.push(l2.value);

                if (tag[2]) {
                    let l3 = l2.children.filter(item => item.value == tag[2])[0] || {};
                    name += ` / ${l3.label}`;
                    tagValue.push(l3.value);
                }
            }

            return {
                show: name,
                value: tagValue
            }
        },
        list() {
            return this.saveIn(JSON.parse(JSON.stringify(this.data)), 1);
        },
        style() {
            let style = {};
            style.maxHeight = this.maxHeight;
            style.width = this.listWidth;
            return style;
        },
        iconClear() {
            return !this.clearable || !(this.selectObj && this.selectObj.length) || !(this.entered || this.clearFlag);
        },
        selectClear() {
            return this.selectObj && this.selectObj.length ? true : false;
        },
        selectStr () {
            let str = '';
            this.selectObj.forEach(item=> {
                str += str ? ' / '+ item.label : item.label;
            })
            return str || '';
        }
    },
    watch: {
        rotate() {
            if (this.rotate) {
                this.holder = this.showLabel || this.holder;
                if (!this.filterable) {
                    this.showLabel = '';
                }
                this.$nextTick(()=>{
                    this.topScroll(); // 下拉框动效
                })
            } else {
                this.multipleInput = ''; // 多选input清空
                this.filtering = false;  // 关闭搜索面板
                this.holder = this.placeholder;
                this.showLabel = '';
                this.selectObj.forEach(item=> {
                    this.showLabel += this.showLabel ? ' / '+ item.label : item.label;
                })
            }
        },
        data: {
            handler () {
                if (this.showVal && this.showVal.length) {
                    this.showLabel = '';
                    this.selectObj = [];
    
                    if (this.multiple) { // 多选
                        if (this.showVal && Object.keys(this.showVal)) {
                            this.echoMultiple(this.list, this.showVal);
                        }
                    } else if (this.showVal && this.showVal.length) {
                        this.liList = [];
                        this.echoObj(this.list, this.showVal, 0);
                    }
                }
            },
            deep: true,
            immediate: true,
        },
        showVal: {
            handler (newV, oldV) {
                this.showLabel = '';
                this.selectObj = [];

                if (this.multiple) { // 多选
                    if (newV && Object.keys(newV)) {
                        this.echoMultiple(this.list, newV);
                    }
                } else if (newV && newV.length) {
                    this.liList = [];
                    console.log(newV, oldV)
                    let valchange = !oldV || newV.join(',') !== oldV.join(',');
                    
                    this.echoObj(this.list, this.showVal, 0, valchange);
                }
            },
            immediate: true,
            deep: true,
        }
    },
    mounted() {
        this.$form.setValidEl(this);
        document.addEventListener('click',(e)=>{
            if (!this.$el.contains(e.target)) {
                this.rotate = false;
                this.filtering = false;
            }
        })
    },
    methods: {
        saveIn(arr, level) { // 数组增加是否打开字段
            for (let i in arr) {
                this.$set(arr[i], 'level', level);
                // 多选初始化
                // if (this.multiple) {
                //     需判断子集是否全部选中，以及子集是否需要全部选中等???
                //     this.$set(arr[i], 'checked', falses);
                //     this.$set(arr[i], 'indeterminate', false);
                // }

                if (arr[i].children && arr[i].children.length) {
                    this.saveIn(arr[i].children, level + 1);
                }
            }
            return arr;
        },
        echoObj(list,arr,j, valchange) { // 框的初始值回显
            // 根据数组， 计算出初始selectObj ???
            for ( let i in list) {
                if (list[i].value == arr[j]) {
                    if ( list[i].children && list[i].children.length && arr[j+1]) {
                        this.liList.push(JSON.parse(JSON.stringify(list[i])))
                        this.echoObj(list[i].children,arr,j+1, valchange)
                    } else {
                        this.liList.push(JSON.parse(JSON.stringify(list[i])))

                        this.liList.forEach(item=>{ // 将选择的层级名称保存
                            this.selectObj.push({
                                label:item.label,
                                value:item.value,
                                level:item.level
                            })
                        })

                        console.log('echo', this.selectObj)

                        this.selectObj.forEach(item=>{
                            this.showLabel += this.showLabel ? ' / '+ item.label : item.label;
                        })

                        if(this.needTrigger && valchange) {
                            this.$emit('change', this.selectObj);
                        }
                    }
                }
            }
        },
        // 回显多选值
        echoMultiple (list, data) {
            let class1 = data.level1 || [];
            let class2 = data.level2 || [];
            let class3 = data.level3 || [];
            

            list.forEach(item1 => {
                let childs1 = item1.children || [];
                if (class1.includes(String(item1.value)) || class1.includes(Number(item1.value))) { // 一级选中
                    this.$set(item1, 'checked', true);
                    this.$set(item1, 'indeterminate', false);
                    this.changeChildCheckbox(childs1, true);
                } else {
                    // 循环一级的chlld，
                    childs1.forEach(item2=> {
                        let childs2 = item2.children || [];
                        if (class2.includes(String(item2.value)) || class2.includes(Number(item2.value))) { // 二级选中
                            this.$set(item2, 'checked', true);
                            this.$set(item2, 'indeterminate', false);
                            this.changeChildCheckbox(childs2, true);
                        } else {
                            // 循环二级下的child
                            childs2.forEach(item3 => {
                                if (class3.includes(String(item3.value)) || class3.includes(Number(item3.value))) { // 三级选中
                                    this.$set(item3, 'checked', true);
                                }
                            })
                        }
                    })
                }
            })
            this.checkAllStatus();
            this.dealChooseTag();
        },
        focus(event) {
            this.clearFlag = this.clearable;
            this.$emit('focus', event);

            setTimeout(() => {
                this.checkPosition();
            }, 200)

            // if (this.filterable) {
            //     this.$nextTick(()=>{
            //         this.topScroll(); // 下拉框动效
            //     })
            // } ???
        },
        triggerFocus() {
            if(this.$refs.searchInput) {
                this.$refs.searchInput.focus();
            }

            this.handleClick();
        },
        handleClick() {
            if (this.disabled) return;
            if (this.filterable) {
                this.rotate = true;
                return;
            }
            this.rotate = !this.rotate;
        },
        blur(event) {
            this.clearFlag = false;
            this.$emit('blur', event);
            this.checkValid(this.selectObj);
        },
        clearData() {
            console.log('clear')
            this.showLabel = '';
            this.holder = this.placeholder;
            this.clearFlag = false;
            this.selectObj = [];
            this.selectValue([]);
            this.changeVal([]);
            this.rotate = false;
        },
        handleEnter() {
            this.entered = true;
        },
        handleLeave() {
            this.entered = false;
        },
        // 更改v-model绑定的值
        selectValue(val) {
            this.$emit('value',val);
        },
        changeVal(newValue) {
            this.$emit('change', newValue);
            this.checkValid(this.selectObj);
        },
        handleReload() {
            this.$emit('reload');
        },
        handleChange (val) {
            this.selectObj = val;
            console.log('change', this.selectObj)
            if(!this.each) {
                this.rotate = false;
            } else {
                this.showLabel = '';
                this.selectObj.forEach(item=> {
                    this.showLabel += this.showLabel ? ' / '+ item.label : item.label;
                })
            }
            this.$emit('change', val);
            this.checkValid(this.selectObj);
        },
        /* 搜索面板 */
        nativeOninput (event) {
            let val = event.target.value;
            this.oninput(val);
        },
        oninput (event) {
            let val = event;
            if (!val) {
                this.filterResult = [];
                this.filtering = false;
                return;
            };
            if (val == this.selectStr) return;

            this.filterResult = this.getSuggestions(val);
            this.filtering = true;
            this.$nextTick(() => {
                this.cascaderListScroll();
            })
        },
        getSuggestionWord (val) {
            let splitArr = val.split('/');
            let words = [];
            if (splitArr.length > 1) {
                splitArr.forEach(item=> {
                    if (item.trim()) {
                        words.push(item.trim());
                    }
                })
            } else {
                words.push(splitArr[0].trim());
            }
            return words;
        },
        getSuggestions (val) {
            let words = this.getSuggestionWord(val);
            let searchRes = []; // 匹配结果

            if (words.length > 1) {
                this.list.forEach(L1 => {
                    // 匹配到第一级， 注: words第一级 不一定是 list第一级别
                    let level = words.length;
                    if (L1.label.includes(words[0])) {
                        // 继续匹配下一级
                        level--;
                        if (level && L1.children && L1.children.length) {
                            L1.children.forEach(L2 => {
                                if (L2.label.includes(words[words.length - level])) {
                                    level--;

                                    if (L2.children && L2.children.length) {
                                        if (level) {
                                            L2.children.forEach(L3=> {
                                                if (L3.label.includes(words[words.length - level])) {
                                                    searchRes.push({
                                                        l1: L1,
                                                        l2: L2,
                                                        l3: L3
                                                    })
                                                }
                                            })
                                        } else {
                                            if(this.each) {
                                                searchRes.push({
                                                    l1: L1,
                                                    l2: L2,
                                                })
                                            }

                                            L2.children.forEach(L3=> {
                                                searchRes.push({
                                                    l1: L1,
                                                    l2: L2,
                                                    l3: L3
                                                })
                                            })
                                        }
                                    } else {
                                        searchRes.push({
                                            l1: L1,
                                            l2: L2,
                                        })
                                    }
                                }
                            })
                        }
                    } else {
                        // 一级没匹配到, 继续从第二级比较
                        L1.children && L1.children.length && L1.children.forEach(L2 => {
                            if (L2.label.includes(words[0])) {
                                level--;
                                if (level && L2.children && L2.children.length) {
                                    L2.children.forEach(L3 => {
                                        if (L3.label.includes(words[words.length - level])) {
                                            searchRes.push({
                                                l1: L1,
                                                l2: L2,
                                                l3: L3
                                            })
                                        }
                                    })
                                }
                            }
                        })
                    }
                })
            } else if (words.length == 1) {
                let word = words[0].trim();

                this.list.forEach(L1 => {
                    // 一级匹配, 则匹配结果包含所有子集
                    if (L1.label.includes(word)) {
                        if(this.each) {
                            searchRes.push({
                                l1: L1
                            })
                        }
                        if (L1.children) {
                            if (L1.children && L1.children.length) {
                                L1.children.forEach(L2 => {
                                    if (L2.children && L2.children.length) {
                                        if(this.each) {
                                            searchRes.push({
                                                l1: L1,
                                                l2: L2
                                            })
                                        }

                                        L2.children.forEach(L3 => {
                                            searchRes.push({
                                                l1: L1,
                                                l2: L2,
                                                l3: L3
                                            })
                                        })
                                    } else {
                                        searchRes.push({
                                            l1: L1,
                                            l2: L2
                                        })
                                    }
                                })
                            } else {
                                searchRes.push({
                                    l1: L1,
                                })
                            }
                        }
                    }
                    // 一级不匹配, 继续轮循下面二级
                    else {
                        L1.children && L1.children.length && L1.children.forEach(L2 => {
                            if (L2.label.includes(word)) {
                                if (L2.children && L2.children.length) {
                                    if(this.each) {
                                        searchRes.push({
                                            l1: L1,
                                            l2: L2
                                        })
                                    }
                                    L2.children.forEach(L3 => {
                                        searchRes.push({
                                            l1: L1,
                                            l2: L2,
                                            l3: L3
                                        })
                                    })
                                } else {
                                    searchRes.push({
                                        l1: L1,
                                        l2: L2
                                    })
                                }
                            } 
                            // 二级不匹配, 继续轮循下面三级
                            else {
                                L2.children && L2.children.length && L2.children.forEach(L3 => {
                                    if (L3.label.includes(word)) {
                                        searchRes.push({
                                            l1: L1,
                                            l2: L2,
                                            l3: L3
                                        })
                                    }
                                })
                            }
                        })
                    }
                })
            }

            if (searchRes.length > 100) {
                searchRes = searchRes.slice(0, 100);
            }

            console.log(searchRes)
            return searchRes;
        },
        suggestShow(item) {
            let str = '';
            if (item.l1) {
                str += item.l1.label;
            }
            if (item.l2) {
                str += ` / ${item.l2.label}`;
            }
            if (item.l3) {
                str += ` / ${item.l3.label}`;
            }

            const keywords = this.getSuggestionWord(this.showLabel);
            keywords.sort((a, b) => b.length - a.length);
            for (const keyword of keywords) {
                const regExp = new RegExp(keyword, 'g');
                str = str.replace(regExp, `<font color='#FF6600'">${keyword}</font>`);
            }
            return str;
        },
        // 搜索面板选中
        handleSuggestionClick (item) {
            if (this.multiple) { // 多选
                this.handlerMultipleClick(item);
                return;
            }

            // 单选
            let arr = [];
            if (item.l1) {
                arr.push({ label: item.l1.label, level: 1, value: item.l1.value });
            }
            if (item.l2) {
                arr.push({ label: item.l2.label, level: 2, value: item.l2.value });
            }
            if (item.l3) {
                arr.push({ label: item.l3.label, level: 3, value: item.l3.value });
            }
            
            this.selectObj = arr;
            this.rotate = false;
            this.handleChange(arr);
        },


        /* 以下多选逻辑 **/
        // 子集: 全选/全不选
        changeChildCheckbox (data, bool) {
            if (!(data && data.length)) return;
            data.forEach(item=> {
                this.$set(item, 'checked', bool);
                this.$set(item, 'indeterminate', false);

                if (item.children && item.children.length) {
                    this.changeChildCheckbox(item.children, bool);
                }
            })
        },
        // 验证搜索面板中选中状态
        isCheck(item) {
            arr = [];
            if (item.l1) {
                arr.push(item.l1.value);
            }
            if (item.l2) {
                arr.push(item.l2.value);
            }
            if (item.l3) {
                arr.push(item.l3.value);
            }

            let list = this.multipleTags.map(item=> {
                return item.join('_');
            })
            return list.includes(arr.join('_'));
        },
        // 搜索面板 选中某项
        handlerMultipleClick (item) {
            if (item.l3) {
                // 初始化set后 此处无需再判断，用注释语句即可???
                if (!item.l3.checked) {
                    this.$set(item.l3, 'checked', true);
                } else {
                    this.$set(item.l3, 'checked', false);
                }
            } else if (item.l2) {
                if (!item.l2.checked) {
                    this.$set(item.l2, 'checked', true);
                } else {
                    this.$set(item.l2, 'checked', false);
                }
            } else {
                if (!item.l1.checked) {
                    this.$set(item.l1, 'checked', true);
                } else {
                    this.$set(item.l1, 'checked', false);
                }
            }
            this.checkAllStatus();
            this.handleMultipleChange();
        },
        // 清除选项
        clearSelect() {
            let a = this.multipleTags.shift(); // 改成查询当前项
            let l1Info = this.list.filter(IETM1 => IETM1.value == a[0])[0] || {};

            if (a[1] && l1Info.children) {
                let l2Info = l1Info.children.filter(ITEM2=>ITEM2.value == a[1])[0] || {};

                if (a[2] && l2Info.children) {
                    let l3Info = l2Info.children.filter(IETM3 => IETM3.value == a[2])[0] || {};
                    l3Info.checked = false;
                    l3Info.indeterminate = false;
                } else {
                    l2Info.checked = false;
                    l2Info.indeterminate = false;
                }
            } else {
                l1Info.checked = false;
                l1Info.indeterminate = false;
            }
            this.checkAllStatus();
            this.handleMultipleChange();
        },
        // 清除所有选项
        clearAllSelect() {
            this.multipleTags.forEach(item => {
                let a = item;
                let l1Info = this.list.filter(IETM1 => IETM1.value == a[0])[0] || {};

                if (a[1] && l1Info.children) {
                    let l2Info = l1Info.children.filter(ITEM2=>ITEM2.value == a[1])[0] || {};

                    if (a[2] && l2Info.children) {
                        let l3Info = l2Info.children.filter(IETM3 => IETM3.value == a[2])[0] || {};
                        l3Info.checked = false;
                        l3Info.indeterminate = false;
                    } else {
                        l2Info.checked = false;
                        l2Info.indeterminate = false;
                    }
                } else {
                    l1Info.checked = false;
                    l1Info.indeterminate = false;
                }
            })

            this.checkAllStatus();
            this.handleMultipleChange();
        },
        // 计算所有选中项【回显到框中】  //有时间dealChooseTag可改递归 ???
        dealChooseTag () {
            let arr = [];

            let filter1 = this.list.filter(f1 => f1.checked || f1.indeterminate);
            filter1.forEach(item => {  // 选中/未全部选中的一级列表
                let childs1 = item.children || []; // childs1 二级列表
                if (item.checked) {// 一级选中 轮循取下面所有的
                    childs1.length && childs1.forEach(item2 => {
                        let child2 = item2.children || [];
                        child2.length && child2.forEach(item3 => {
                            arr.push([item.value, item2.value, item3.value]);
                        })
                        !child2.length && arr.push([item.value, item2.value]);
                    })
                    !childs1.length && arr.push([item.value]);
                } else if (item.indeterminate) { // 一级未全部选中
                    if (childs1.length) {
                        let filter2 = childs1.filter(f2 => f2.checked || f2.indeterminate); // 选中/未全部选中的二级列表
                        filter2.forEach(item2 => {
                            if (item2.checked) { // 二级选中 轮循取以下所有三级
                                let childs2 = item2.children || []; // 三级列表
                                childs2.length && childs2.forEach(item3 => {
                                    arr.push([item.value, item2.value, item3.value]);
                                })
                                !childs2.length && (arr.push([item.value, item2.value]));
                            } else if (item2.indeterminate) {
                                let child2 = item2.children || [];
                                child2.length && item2.children.forEach(item3 => {
                                    if (item3.checked) {
                                        arr.push([item.value, item2.value, item3.value])
                                    }
                                })
                            }
                        });
                    }
                }
            })
            this.multipleTags = arr;
        },
        // 组件内传出来的数据
        handleMultipleChange () {
            this.checkAllStatus();
            this.dealChooseTag();
            let final = this.handlerQuery();
            this.$emit('change', final);
        },

        // 验证所有层级 全选/部分选状态 递归???
        checkAllStatus() {
            this.list.forEach(item1 => {
                if (item1.children && item1.children.length) {
                    // 先判断二级的状态
                    item1.children.forEach(item2 => {
                        if (item2.children && item2.children.length) {
                            let arr = item2.children.filter(f => f.checked);
                            if (arr.length == item2.children.length) {
                                item2.checked = true;
                                item2.indeterminate = false;
                            } else if (arr.length >= 1) {
                                item2.checked = false;
                                item2.indeterminate = true;
                            } else {
                                item2.checked = false;
                                item2.indeterminate = false;
                            }
                        }
                    })

                    // 判断一级的状态                            
                    let item1_check = item1.children.filter(f => f.checked);
                    let item1_inde = item1.children.filter(f => f.checked || f.indeterminate);

                    if (item1_check.length == item1.children.length) {
                        item1.checked = true;
                        item1.indeterminate = false;
                    } else if (item1_inde.length) {
                        item1.checked = false;
                        item1.indeterminate = true;
                    } else {
                        item1.checked = false;
                        item1.indeterminate = false;
                    }
                }
            })
        },

        // 入参格式
        handlerQuery() {
            class1 = [];
            class2 = [];
            class3 = [];

            this.list.forEach(item1 => {
                if (item1.checked) {
                    class1.push(item1.value);
                } else if (item1.indeterminate) {
                    let item1Child = item1.children || [];
                    item1Child.forEach(item2 => {
                        if (item2.checked) {
                            class2.push(item2.value);
                        } else if (item2.indeterminate) {
                            let item2Child = item2.children || [];
                            item2Child.forEach(item3=> {
                                if (item3.checked) {
                                    class3.push(item3.value);
                                }
                            })
                        }
                    })
                }
            })
            return {
                level1: class1,
                level2: class2,
                level3: class3,
            };
        },
        checkValid(newValue) {
            if (this.validKey && this.validValue) {
                if (this.$form.validList[this.validKey].indexOf(this.validValue) !== -1) {
                    let validData = this.$form.checkValid(this.validValue, newValue, this.validKey);

                    this.triggerError(validData);
                }
            }
        },
        triggerError(validData) {
            if (validData.result) {
                this.errorMsg = '';
                this.errorable = false;
            } else {
                this.errorMsg = validData.message;
                this.errorable = true;
            }
        },
        checkPosition() {
            this.isRight = false;
            this.$nextTick(() => {
                if(this.$refs.cascaderList) {
                    let position = this.$refs.cascaderList.getBoundingClientRect();
    
                    if(position.right > window.innerWidth) {
                        this.isRight = true;
                    }
                }
            })
        },
    }
})