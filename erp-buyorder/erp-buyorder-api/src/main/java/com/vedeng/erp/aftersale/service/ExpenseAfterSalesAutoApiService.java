package com.vedeng.erp.aftersale.service;


import com.vedeng.erp.aftersale.dto.ExpenseAfterSalesDto;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseDto;

import java.util.List;

/**
 * <AUTHOR>
 * @description 采购费用售后 销售线采销联动 转单 以及 一键成单
 * @date 2023/01/05 9:24
 **/
public interface ExpenseAfterSalesAutoApiService {

    List<BuyorderExpenseDto> autoExpenseAfterSales(Integer afterSaleOrderId,List<Integer> warnIds);

    /**
     * 根据销售售后单的id进行计算
     * @param afterSaleOrderId
     */
    List<ExpenseAfterSalesDto> bindingAndAllocationAutoExpenseAfterSales(Integer afterSaleOrderId,List<Integer> warnIds);

    /**
     * 满足采销联动创建售后单的时候自动创建费用售后单
     * @param expenseAfterSalesDtos 前置方法封装好的费用售后单数据
     */
    List<Long> autoAddExpenseAfterSales(List<ExpenseAfterSalesDto> expenseAfterSalesDtos);


    /**
     * 自动转单全部可售后数据封装
     * @param afterSalesId 售后单id
     * @param warnIds 已经预警的
     * @return 费用售后单
     */
    List<ExpenseAfterSalesDto> bindingAutoExpenseAfterSales(Integer afterSalesId,List<Integer> warnIds);

    /**
     * 自动转单创建的售后单 执行退款运算
     * @param afterSalesIds 售后单id
     */
    void autoExecuteRefundOperation(List<Long> afterSalesIds);

    /**
     * 自动转单创建的售后单 执行票的冲销
     * @param afterSalesIds 售后单id
     */
    void autoReversalInvoices(List<Long> afterSalesIds);
}
