package com.vedeng.erp.trader.mapper;

import com.vedeng.erp.trader.domain.entity.CommunicateSummaryEntity;

public interface TraderCommunicateSummaryMapper {
    int deleteByPrimaryKey(Long communicateSummaryId);

    int insert(CommunicateSummaryEntity record);

    int insertSelective(CommunicateSummaryEntity record);

    CommunicateSummaryEntity selectByPrimaryKey(Long communicateSummaryId);

    int updateByPrimaryKeySelective(CommunicateSummaryEntity record);

    int updateByPrimaryKey(CommunicateSummaryEntity record);

    /**
     * 根据客户id查询最近的一条沟通记录的转写结果
     *
     * @param traderId 客户id
     * @return CommunicateSummaryEntity
     */
    CommunicateSummaryEntity getLatestSummaryByTraderId(Integer traderId);
}