.dlg-share-wrap .dlg-share-btn {
  margin-bottom: 20px;
}
.record-wrap {
  display: flex;
  align-items: center;
}
.record-wrap .icon-sms {
  font-size: 16px;
  margin-right: 5px;
  color: #09f;
  cursor: pointer;
  line-height: 1;
}
.record-wrap .icon-sms:hover {
  color: #f60;
}
.td-tyc-wrap {
  display: flex;
  align-items: center;
}
.td-tyc-wrap .text-line-1 {
  flex: 1;
}
.td-tyc-wrap .tyc-icon {
  width: 16px;
  height: 16px;
  background-image: url(../../image/tyc.png);
  background-size: 100% 100%;
  margin-left: 5px;
  cursor: pointer;
}
.leadsno {
  display: flex;
  align-items: center;
}
.tag-icon-hebing {
  width: 16px;
  height: 16px;
  background-image: url(../../image/common/tag-hebing.svg);
  background-size: 100% 100%;
  margin-top: -1px;
  display: inline-block;
  margin-right: 5px;
}
.ai-wrap {
  width: 100%;
  margin-top: 10px;
}
.ai-wrap .ai-title {
  color: #999;
  border-top: dashed 1px #E1E5E8;
  padding: 10px 0 3px;
}
.ai-wrap .ai-content {
  margin-top: 7px;
}
.card-title-ai {
  position: absolute;
  top: 15px;
  right: 20px;
}
.highlight {
  color: #09f;
  cursor: pointer;
}
.highlight .vd-ui_icon {
  font-size: 16px;
  margin-right: 1px;
  cursor: pointer;
  line-height: 1;
}
.highlight:hover {
  color: #f60;
}
.detail-tyc {
  word-break: break-all;
  position: relative;
  padding-right: 21px;
  display: inline-block;
}
.detail-tyc .company {
  font-size: 12px;
  color: #333;
}
.detail-tyc .company.blue {
  color: #09f;
  cursor: pointer;
}
.detail-tyc .company.blue:hover {
  color: #f60;
}
.detail-tyc .icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url('../../image/tyc.png') no-repeat;
  background-size: 100%;
  margin-left: 5px;
  cursor: pointer;
  position: absolute;
  top: 1px;
  right: 0;
}
.businessLeads-add-container {
  padding-top: 60px;
}
.businessLeads-add-container .top-featurn {
  width: 100%;
  height: 60px;
  position: fixed;
  top: 50px;
  left: 0;
  z-index: 40;
}
.businessLeads-add-container .top-featurn .inner-header {
  padding: 0 15px;
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #FFFFFF;
  border-bottom: solid 1px #E1E5E8;
}
.businessLeads-add-container .top-featurn .inner-header .inner-header-title {
  font-size: 20px;
  font-weight: 700;
}
.businessLeads-detail-container {
  position: relative;
  padding-top: 60px;
  padding-right: 360px;
}
.businessLeads-detail-container .visible-xs {
  display: none;
}
.businessLeads-detail-container .leadsDetail-header-wrap {
  width: 100%;
  min-width: 760px;
  height: 60px;
  background: #fff;
  border-bottom: solid 1px #E1E5E8;
  position: fixed;
  top: 50px;
  z-index: 9;
}
.businessLeads-detail-container .leadsDetail-header-wrap .header-main {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
}
.businessLeads-detail-container .leadsDetail-header-wrap .header-main .header-content {
  flex: 1;
  min-width: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.businessLeads-detail-container .leadsDetail-header-wrap .header-main .header-content .header-left {
  display: flex;
  align-items: center;
  padding: 0 15px;
}
.businessLeads-detail-container .leadsDetail-header-wrap .header-main .header-content .header-left .title {
  font-size: 20px;
  font-weight: 700;
  color: #333333;
}
.businessLeads-detail-container .leadsDetail-header-wrap .header-main .header-content .header-left .status {
  height: 27px;
  line-height: 27px;
  padding: 0 10px;
  border-radius: 3px;
  color: #FF6600;
  background-color: rgba(255, 102, 0, 0.1);
  margin-left: 15px;
}
.businessLeads-detail-container .leadsDetail-header-wrap .header-main .header-content .header-left .status.status0 {
  background: #ffede1;
  color: #F60;
}
.businessLeads-detail-container .leadsDetail-header-wrap .header-main .header-content .header-left .status.status1 {
  background: #ffede1;
  color: #F60;
}
.businessLeads-detail-container .leadsDetail-header-wrap .header-main .header-content .header-left .status.status2 {
  background: #e1f3ff;
  color: #09F;
}
.businessLeads-detail-container .leadsDetail-header-wrap .header-main .header-content .header-left .status.status3 {
  background: #fce9e9;
  color: #E64545;
}
.businessLeads-detail-container .leadsDetail-header-wrap .header-main .header-content .header-left .status.status4 {
  background: #E3F7E3;
  color: #13BF13;
}
.businessLeads-detail-container .leadsDetail-header-wrap .header-main .header-content .header-right {
  display: flex;
  align-items: center;
  padding-right: 10px;
}
.businessLeads-detail-container .leadsDetail-header-wrap .header-main .header-content .header-right .vd-ui-button {
  margin-right: 10px;
}
.businessLeads-detail-container .leadsDetail-header-wrap .header-main .header-aside-wrap .header-md-aside {
  position: relative;
  width: 360px;
  display: flex;
}
.businessLeads-detail-container .leadsDetail-header-wrap .header-main .header-aside-wrap .header-md-aside::before {
  content: "";
  height: 60px;
  width: 1px;
  background: #E1E5E8;
  position: absolute;
  left: 0;
}
.businessLeads-detail-container .leadsDetail-header-wrap .header-main .header-aside-wrap .vd-ui-title-tip-wrap {
  flex: 1;
}
.businessLeads-detail-container .leadsDetail-header-wrap .header-main .header-aside-wrap .header-xs-aside {
  display: none;
}
.businessLeads-detail-container .leadsDetail-header-wrap .header-main .header-aside-wrap .h-a-item {
  height: 60px;
  position: relative;
  flex: 1;
  background-size: 24px 24px;
  background-position: center;
  background-repeat: no-repeat;
  cursor: pointer;
}
.businessLeads-detail-container .leadsDetail-header-wrap .header-main .header-aside-wrap .h-a-item::before {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  content: "";
  z-index: 1;
  background-size: 24px 24px;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0;
}
.businessLeads-detail-container .leadsDetail-header-wrap .header-main .header-aside-wrap .h-a-item.msg {
  background-image: url('../../image/icon/msg.svg');
}
.businessLeads-detail-container .leadsDetail-header-wrap .header-main .header-aside-wrap .h-a-item.msg::before {
  background-image: url('../../image/icon/msg-active.svg');
}
.businessLeads-detail-container .leadsDetail-header-wrap .header-main .header-aside-wrap .h-a-item.time {
  background-image: url('../../image/icon/time.svg');
}
.businessLeads-detail-container .leadsDetail-header-wrap .header-main .header-aside-wrap .h-a-item.time::before {
  background-image: url('../../image/icon/time-active.svg');
}
.businessLeads-detail-container .leadsDetail-header-wrap .header-main .header-aside-wrap .h-a-item.record {
  background-image: url('../../image/icon/record.svg');
}
.businessLeads-detail-container .leadsDetail-header-wrap .header-main .header-aside-wrap .h-a-item.record::before {
  background-image: url('../../image/icon/record-active.svg');
}
.businessLeads-detail-container .leadsDetail-header-wrap .header-main .header-aside-wrap .h-a-item.user {
  background-image: url('../../image/icon/user.svg');
}
.businessLeads-detail-container .leadsDetail-header-wrap .header-main .header-aside-wrap .h-a-item.user::before {
  background-image: url('../../image/icon/user-active.svg');
}
.businessLeads-detail-container .leadsDetail-header-wrap .header-main .header-aside-wrap .h-a-item.active,
.businessLeads-detail-container .leadsDetail-header-wrap .header-main .header-aside-wrap .h-a-item:hover {
  background-image: none;
}
.businessLeads-detail-container .leadsDetail-header-wrap .header-main .header-aside-wrap .h-a-item.active::before,
.businessLeads-detail-container .leadsDetail-header-wrap .header-main .header-aside-wrap .h-a-item:hover::before {
  opacity: 1;
}
.businessLeads-detail-container .leadsDetail-header-wrap .header-main .header-aside-wrap .h-a-item.active::after {
  content: "";
  display: block;
  width: 30px;
  height: 2px;
  background: #09f;
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}
.businessLeads-detail-container .leadsDetail-page-wrap {
  padding: 10px 20px 20px 20px;
}
.businessLeads-detail-container .leadsDetail-page-wrap .main .show-font {
  margin-top: 10px;
}
.businessLeads-detail-container .leadsDetail-page-wrap .main .show-font:first-child {
  margin-top: 0;
}
.businessLeads-detail-container .leadsDetail-page-wrap .right-aside {
  width: 360px;
  background: #fff;
  position: fixed;
  right: 0;
  top: 50px;
  z-index: 3;
  min-height: 500px;
  bottom: 0;
  padding-top: 60px;
}
.businessLeads-detail-container .leadsDetail-page-wrap .right-aside .right-aside-inner {
  height: 100%;
  border-left: solid 1px #E1E5E8;
}
.businessLeads-detail-container .remark-wrap {
  position: relative;
  max-width: 1240px;
  margin: 0 auto;
  background: #fff;
  padding: 20px;
  margin-bottom: 20px;
}
.businessLeads-detail-container .card {
  min-width: 0;
}
.call-span .icon-call2 {
  line-height: 1;
  vertical-align: -2px;
  font-size: 16px;
}
.call-span.highlight {
  color: #09f;
}
.call-span.highlight:hover {
  color: #f60;
}
@media screen and (max-width: 1366px) {
  .businessLeads-detail-container {
    position: relative;
    padding-top: 60px;
    padding-right: 0;
  }
  .businessLeads-detail-container .hidden-xs {
    display: none;
  }
  .businessLeads-detail-container .leadsDetail-header-wrap .header-main .header-aside-wrap .header-md-aside {
    display: none;
  }
  .businessLeads-detail-container .leadsDetail-header-wrap .header-main .header-aside-wrap .header-xs-aside {
    position: relative;
    width: 192px;
    display: flex;
  }
  .card {
    min-width: 1160px;
  }
}
.only-form-wrap {
  padding-top: 0 !important;
}
.only-form-wrap .page-container {
  padding-left: 0 !important;
}
.only-form-wrap .businessLeads-add-container {
  padding-top: 0 !important;
}
.only-form-wrap .businessLeads-add-container .top-featurn {
  display: none !important;
}
