package com.vedeng.common.shiro.cas.listener;

import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import com.vedeng.common.shiro.cas.session.EvictAccountIdRegistry;
import com.vedeng.common.trace.mq.AbstractMessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.Map;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Component
@Slf4j
public class AccountInfoChangedListener extends AbstractMessageListener {

    private final static Logger LOGGER = LoggerFactory.getLogger(AccountInfoChangedListener.class);

    private EvictAccountIdRegistry evictAccountIdQueue = EvictAccountIdRegistry.getInstance();

    @Override
    public void doBusiness(Message message, Channel channel) throws Exception {
        String paramsInJson = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("收到清除用户session消息 - params: {}", paramsInJson);

        Map<String, Object> resultMap = null;
        try {
            resultMap = Collections.checkedMap(JSONObject.parseObject(paramsInJson, Map.class), String.class, Object.class);
            if (resultMap != null) {
                Integer accountId = MapUtils.getInteger(resultMap, "accountId");
                if (accountId != null) {
                    evictAccountIdQueue.add(accountId);
                }
            }
        } catch (Exception e) {
            log.error("【onMessage】处理异常",e);
        } finally {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        }
    }
}
