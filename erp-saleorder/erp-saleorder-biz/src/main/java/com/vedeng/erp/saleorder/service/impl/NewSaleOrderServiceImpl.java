package com.vedeng.erp.saleorder.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.erp.saleorder.dao.SaleOrderMapper;
import com.vedeng.erp.saleorder.service.NewSaleOrderService;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.price.dto.ContractPriceInfoDetailResponseDto;
import com.vedeng.price.dto.SkuPriceInfoDetailResponseDto;
import com.vedeng.price.service.BasePriceService;
import com.vedeng.trader.model.TraderCustomer;
import com.vedeng.trader.service.TraderCustomerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class NewSaleOrderServiceImpl implements NewSaleOrderService {

    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private BasePriceService basePriceService;

    @Resource
    private TraderCustomerService traderCustomerService;

    @Resource
    private SaleOrderMapper saleOrderMapper;

    /**
     * 获取产品价格信息
     *
     * 说明
     * 原方法：com.vedeng.erp.saleorder.controller.NewSaleorderController#getSaleOrderGoodsPrice(java.lang.Integer, java.lang.String, java.math.BigDecimal)
     * 抽取成service 共saleorder-biz调用
     * @param saleorderId
     * @param skuNo
     * @param price
     * @return
     */
    @Override
    public Map<String, String> getSaleOrderGoodsPrice(Integer saleorderId, String skuNo, BigDecimal price) {
        log.info("getSaleOrderGoodsPrice start ,saleorderId:{}-{}-{}",saleorderId,skuNo,price);
        Saleorder saleorder = saleorderMapper.getSaleOrderById(saleorderId);

        Integer traderId = saleorder.getTraderId();
        Map<String, String> skuPrice = this.getPriceCenterBySKU(traderId, skuNo, price);

        return skuPrice;
    }


    /**
     * 获取价格中心核算价
     * @param traderId
     * @param skuNo
     * @param price
     * @return
     */
    @Override
    public Map<String, String> getPriceCenterBySKU(Integer traderId, String skuNo, BigDecimal price){
        log.info("getPriceCenterBySKU,traderId:{},skuNo:{},price:{}",traderId,skuNo,price);
        ContractPriceInfoDetailResponseDto contractPriceInfoDetail = basePriceService.findSkuContractInfoByCon(traderId,skuNo);
        TraderCustomer traderCustomer = traderCustomerService.getTraderCustomerId(traderId);
        String salePrice = "";
        String salePriceShow = "";
        //有合约价 就设置合约价
        if(contractPriceInfoDetail != null && contractPriceInfoDetail.getContractPrice() != null){
            salePrice = contractPriceInfoDetail.getContractPrice().toPlainString();
            salePriceShow += (salePrice + "(合约价)");
            //没有合约价,查基础价
        }else{
            SkuPriceInfoDetailResponseDto skuPriceInfoDetailResponseDto = basePriceService.findSkuPriceInfoBySkuNo(skuNo);
            //已经核价就设置成本价
            if (skuPriceInfoDetailResponseDto != null && traderCustomer != null) {
                //分销商
                if(ErpConst.CUSTOME_RNATURE.equals(traderCustomer.getCustomerNature())){
                    salePrice = skuPriceInfoDetailResponseDto.getDistributionPrice().toPlainString();
                    salePriceShow += (salePrice + "(经销价)");
                }
                //终端
                if(ErpConst.CUSTOME_INTERMIAL.equals(traderCustomer.getCustomerNature())){
                    //客户类型 = 科研医疗 终端 取科研终端价(新增的)
                    if (ErpConst.CUSTOME_TYPE_RESEARCH_MEDICAL.equals(traderCustomer.getCustomerType())){
                        if (ObjectUtil.isNotNull(skuPriceInfoDetailResponseDto.getResearchTerminalPrice())){
                            salePrice = skuPriceInfoDetailResponseDto.getResearchTerminalPrice().toPlainString();
                            salePriceShow += (salePrice + "(科研终端价)");
                        }
                    }else {
                        salePrice = skuPriceInfoDetailResponseDto.getTerminalPrice().toPlainString();
                        salePriceShow += (salePrice + "(终端价)");
                    }
                }
            }
        }
        Map<String,String> returnMap = new HashMap<>(8);
        if (price == null) {
            returnMap.put("salePrice",salePrice);
        } else {
            returnMap.put("salePrice", price.toString());
        }
        returnMap.put("limitPrice","false");
        returnMap.put("salePriceShow",salePriceShow);
        log.info("getOrderGoodsPrice end ,returnMap:{}", JSONObject.toJSONString(returnMap));
        return returnMap;
    }

}
