<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.trader.dao.TraderCustomerBidingInfoMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.trader.model.TraderCustomerBidingInfo">
        <id column="ID" jdbcType="INTEGER" property="id" />
        <result column="TRADER_CUSTOMER_ID" jdbcType="INTEGER" property="traderCustomerId" />
        <result column="TYPE" jdbcType="INTEGER" property="type" />
        <result column="BIDING_DATA" jdbcType="VARCHAR" property="bidingData" />
    </resultMap>
    <sql id="Base_Column_List">
    ID, TRADER_CUSTOMER_ID, `TYPE`, BIDING_DATA
  </sql>
    <select id="getBidingInfoByCustomerId" resultType="com.vedeng.trader.model.TraderCustomerBidingInfo">
        SELECT * FROM T_TRADER_CUSTOMER_BIDING_INFO WHERE TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER}
    </select>

</mapper>