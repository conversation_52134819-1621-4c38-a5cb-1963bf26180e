package com.vedeng.crm.business.quote.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.vedeng.crm.business.quote.service.impl.QuoteLogoImageConverter;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 报价单商品表头导出vo
 */
@Data
@ContentRowHeight(40)
public class QuoteOrderGoodsHeadExportVo {

    /**
     * 图片 设置宽高
     */
    @ExcelProperty(value = "图片", converter = QuoteLogoImageConverter.class)
    @ColumnWidth(10)
    private String logo;

    /**
     * 联系人
     */
    @ExcelProperty(value = "联系人")
    @ColumnWidth(20)
    private String concatName;

    /**
     * 手机
     */
    @ExcelProperty(value = "手机")
    @ColumnWidth(20)
    private String concatPhone;

    /**
     * 邮箱
     */
    @ExcelProperty(value = "邮箱")
    @ColumnWidth(40)
    private String concatEmail;

    /**
     * 报价号
     */
    @ExcelProperty(value = "报价号")
    @ColumnWidth(20)
    private String quoteNo;

    /**
     * 报价日期
     */
    @ExcelProperty(value = "报价日期")
    @ColumnWidth(20)
    private String quoteDate;

    @ExcelProperty(value = "总价")
    @ColumnWidth(10)
    private BigDecimal totalAmount;

    @ExcelProperty(value = "sku种类数量")
    @ColumnWidth(20)
    private Integer skuCount;

    @ExcelProperty(value = "产品件数")
    @ColumnWidth(20)
    private Integer productCount;
}


