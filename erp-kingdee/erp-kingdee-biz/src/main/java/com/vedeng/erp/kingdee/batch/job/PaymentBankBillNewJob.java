package com.vedeng.erp.kingdee.batch.job;

import cn.hutool.core.date.DateUtil;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.dto.BatchBankBillDto;
import com.vedeng.erp.kingdee.batch.processor.IgnoreBankPaymentBillProcessor;
import com.vedeng.erp.kingdee.batch.processor.PaymentBankBillNewProcessor;
import com.vedeng.erp.kingdee.batch.writer.PaymentWriter;
import com.vedeng.erp.kingdee.dto.KingDeePayBillDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 已结算付款银行流水推送金蝶
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class PaymentBankBillNewJob extends BaseJob {


    @Autowired
    private PaymentWriter paymentWriter;

    @Autowired
    private PaymentBankBillNewProcessor paymentBankBillNewProcessor;

    @Autowired
    private IgnoreBankPaymentBillProcessor ignoreBankPaymentBillProcessor;

    @Value("${ignore_trade_name}")
    private String ignoreTradeName;


    /**
     * 推送已结算付款银行流水
     * @return
     */
    public Job pushPaymentBankBillNewJob(){
        return jobBuilderFactory.get("pushPaymentBankBillNewJob")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(paymentBankBillNew())
                .next(paymentWeChatAndAlipayNew())
                .next(allIgnorePaymentBill())
                .build();
    }

    public Step paymentBankBillNew(){
        return stepBuilderFactory.get("已结算付款银行流水")
                .<BatchBankBillDto, KingDeePayBillDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(paymentBankBillNewReader(null,null))
                .processor(paymentBankBillNewProcessor)
                .writer(paymentWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }

    public Step paymentWeChatAndAlipayNew(){
        return stepBuilderFactory.get("已结算付款微信和支付宝流水")
                .<BatchBankBillDto, KingDeePayBillDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(paymentWeChatAndAlipayNewReader(null,null))
                .processor(paymentBankBillNewProcessor)
                .writer(paymentWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 全量的忽略付款单流水推送包括银行、支付宝、微信
     * @return
     */
    public Step allIgnorePaymentBill(){
        return stepBuilderFactory.get("全类型的忽略付款单流水推送")
                .<BatchBankBillDto, KingDeePayBillDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(allIgnorePaymentBillReader(null,null))
                .processor(ignoreBankPaymentBillProcessor)
                .writer(paymentWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * (付款单)查询所有流水推送金蝶
     * @param beginTime
     * @param endTime
     * @return
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchBankBillDto> allIgnorePaymentBillReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                @Value("#{jobParameters['endTime']}") String endTime) {
        BatchBankBillDto batchBankBillDto = BatchBankBillDto
                .builder()
                // -0：支出，1：收入
                .flag1(ErpConstant.ZERO)
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(beginTime))
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()): DateUtil.parseDateTime(endTime))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchBankBillDto.class.getSimpleName(), "queryNeedPullIgnoreBankBill", batchBankBillDto);

    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchBankBillDto> paymentBankBillNewReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                              @Value("#{jobParameters['endTime']}") String endTime){

        List<String> needTraderName = new ArrayList<>();

        if (StringUtils.isNotBlank(ignoreTradeName)) {
            String[] split = ignoreTradeName.split(",");
            needTraderName = Arrays.stream(split).collect(Collectors.toList());
        }

        BatchBankBillDto batchBankBillDto = BatchBankBillDto
                .builder()
                .needPushTraderNameList(needTraderName)
                .capitalBillBeginTime(beginTime == null ? null : DateUtil.parseDateTime(beginTime).getTime())
                .capitalBillEndTime(endTime == null ? null: DateUtil.parseDateTime(endTime).getTime())
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchBankBillDto.class.getSimpleName(), "queryNeedPushPaymentBankBillNew", batchBankBillDto);
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchBankBillDto> paymentWeChatAndAlipayNewReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                     @Value("#{jobParameters['endTime']}") String endTime) {

        BatchBankBillDto batchBankBillDto = BatchBankBillDto
                .builder()
                .capitalBillBeginTime(beginTime == null ? null : DateUtil.parseDateTime(beginTime).getTime())
                .capitalBillEndTime(endTime == null ? null : DateUtil.parseDateTime(endTime).getTime())
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchBankBillDto.class.getSimpleName(), "queryNeedPushPaymentWeChatAndAlipayNew", batchBankBillDto);
    }

}
