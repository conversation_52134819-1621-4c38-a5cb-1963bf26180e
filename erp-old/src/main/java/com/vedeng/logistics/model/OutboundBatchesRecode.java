package com.vedeng.logistics.model;

import com.vedeng.logistics.model.vo.BatchExpressVo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 出库批次记录表
 * @TableName T_OUTBOUND_BATCHES_RECODE
 */
public class OutboundBatchesRecode implements Serializable {
    /**
     * 出库批次ID
     */
    private Integer id;

    /**
     * 批次编码
     */
    private String batchNo;

    /**
     * 备注
     */
    private String comments;

    /**
     * 审核状态 0：驳回；1：通过 2:审核中
     */
    private Integer auditStatus;

    /**
     * 是否删除,0未删除,1已删除
     */
    private Integer isEnable;

    /**
     * 操作人
     */
    private Integer creator;

    /**
     * 批次类型，1.普发；2.直发
     */
    private Integer batchType;

    /**
     * 最近一次编辑时间
     */
    private Long modTime;

    /**
     * 最近一次编辑人
     */
    private Integer updater;

    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 是否上传,0：未上传；1：已上传
     */
    private Integer uploadStatus;

    /**
     * 是否在线确认，0:否，1：是
     */
    private Integer onlineConfirm;


    /**
     * 发货时间
     */
    private Long deliveryTime;

    /**
     * 批次时间
     */
    private String batchTime;//签收时间

    /**
     * 批次下商品详情信息
     * @return
     */
    private List<BatchExpressVo> batchExpressVos;

    public Integer getOnlineConfirm() {
        return onlineConfirm;
    }

    public void setOnlineConfirm(Integer onlineConfirm) {
        this.onlineConfirm = onlineConfirm;
    }

    public List<BatchExpressVo> getBatchExpressVos() {
        return batchExpressVos;
    }

    public void setBatchExpressVos(List<BatchExpressVo> batchExpressVos) {
        this.batchExpressVos = batchExpressVos;
    }


    public String getBatchTime() {
        return batchTime;
    }

    public void setBatchTime(String batchTime) {
        this.batchTime = batchTime;
    }

    public Long getDeliveryTime() {
        return deliveryTime;
    }

    public void setDeliveryTime(Long deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    /**
     * 该批次下物流快递集合
     */
    List<Express> expressList;

    private static final long serialVersionUID = 1L;


    public List<Express> getExpressList() {
        return expressList;
    }

    public void setExpressList(List<Express> expressList) {
        this.expressList = expressList;
    }

    public Integer getUploadStatus() {
        return uploadStatus;
    }

    public void setUploadStatus(Integer uploadStatus) {
        this.uploadStatus = uploadStatus;
    }

    public Integer getBatchType() {
        return batchType;
    }

    public void setBatchType(Integer batchType) {
        this.batchType = batchType;
    }


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    public Integer getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(Integer isEnable) {
        this.isEnable = isEnable;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Long getModTime() {
        return modTime;
    }

    public void setModTime(Long modTime) {
        this.modTime = modTime;
    }

    public Integer getUpdater() {
        return updater;
    }

    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    public Long getAddTime() {
        return addTime;
    }

    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }
}