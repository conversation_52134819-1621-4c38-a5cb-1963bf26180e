package com.wms.constant;

/**
 * WMS单据类型Bean
 */
public class WmsInterfaceOrderType {

    /**
     * 入库单类型-采购单
     */
    public static final String INPUT_PURCHASE  = "PO";

    /**
     * 入库单类型-采购换货入库
     */
    public static final String INPUT_PURCHASE_EXG  = "PS";

    /**
     * 入库单类型-赠品
     */
    public static final String INPUT_GIFT  = "PA";

    /**
     * 入库单类型-盘盈入库单
     */
    public static final String INPUT_CHECK_MORE  = "RC";

    /**
     * 入库单类型-调拨入库单
     */
    public static final String INPUT_TRANSFER  = "TT";


    /**
     * 入库单类型-销售售后退货
     */
    public static final String INPUT_SALE_RETURN  = "RA";

    /**
     * 入库单类型- 平滑移仓入库
     */
    public static final String INPUT_CS_INIT  = "CS";

    /**
     * 入库单类型- 商品外借入库
     */
    public static final String INPUT_LEND_IN  = "JS";

    /**
     * 入库单类型-直发采购单
     */
    public static final String DELIVERY_DIRECT_IN  = "ZP";





    /**
     * 出库单类型-销售出库
     */
    public static final String OUT_SALE_OUT  = "SO";


    /**
     * 出库单类型-采购售后退货
     */
    public static final String OUT_PURCHASE_RETURN  = "PT";

    /**
     * 出库单类型-领用出库
     */
    public static final String OUT_RECEIVER  = "SA";
    /**
     * 出库单类型-报废出库单
     */
    public static final String OUT_SCRAPPED  = "SB";

    /**
     * 出库单类型-采购换货出库
     */
    public static final String OUT_PURCHASE_EXG  = "PS";

    /**
     * ERP移仓单
     */
    public static final String OUT_TRANSFER  = "TR";

    /**
     * 出库单类型 -> 商品外借出库
     */
    public static final String OUT_LENDOUT  = "JS";

    /**
     * 报废出库单
     */
    public static final String OUT_CRASH = "SB";

    /**
     *调拔出库单
     */
    public static final String TRANSFER_OUT = "TT";

    /**
     *样品出库单
     */
    public static final String OUT_SAMPLE = "SD";

    /**
     * 直发销售出库单
     */
    public static final String OUT_DIRECT_SALE_OUT = "ZS";

    /**
     * todo hollis 盘亏出库单
     */
    public static final String INVENTORY_OUT = "PK";


    /**
     * todo hollis 转换单出库
     */
    public static final String UNIT_CONVERSION_OUT = "UO";

    /**
     * todo hollis 转换单入库
     */
    public static final String UNIT_CONVERSION_IN = "UI";

    public static final String SAMPLE_ORDER_OUT = "YPO";

    /**
     * 换货单类型-借货单
     */
    public static final String EXG_JH = "JS";

    /**
     * 换货单类型- 采购换货单
     */
    public static final String EXG_PURCHASE  = "PS";

    /**
     * 换货单类型- 销售换货单
     */
    public static final String EXCHANG_SALEORDER = "SS";



    /**
     * 修改商品的长宽高
     */
    public static final String EXCHANG_PUT_LWHDATA = "LWH";

}
