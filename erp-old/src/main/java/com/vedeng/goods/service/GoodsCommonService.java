package com.vedeng.goods.service;

import com.vedeng.common.model.ResultInfo;
import com.vedeng.goods.manager.extension.GoodsTodoResultVo;
import com.vedeng.goods.manager.extension.GoodsValidResultVo;
import com.vedeng.goods.manager.rule.GoodsValidatedRule;
import com.vedeng.goods.model.entity.GoodsLevelDo;
import com.vedeng.goods.model.entity.GoodsPositionDo;
import com.vedeng.goods.model.vo.GoodsLevelVo;
import com.vedeng.goods.model.vo.GoodsPositionVo;
import com.vedeng.goods.model.vo.GoodsTodoItemVo;
import com.vedeng.goods.model.vo.GoodsTodoSummaryVo;

import java.util.List;

/**
 * <AUTHOR> [<EMAIL>]
 */
public interface GoodsCommonService {

    String DEFAULT_GOODS_LEVEL_NAME = "无";
    String DEFAULT_GOODS_POSITION_NAME = "无档位";

    /**
     * Lists all of goods levels.
     *
     * @param additional
     * @return
     */
    List<GoodsLevelVo> listAllGoodsLevel(boolean additional);

    /**
     * List all of goods position.
     *
     * @return
     */
    List<GoodsPositionVo> listAllGoodsPosition();


    String getGoodsLevelName(Integer goodsLevelNo);


    String getGoodsPositionName(Integer goodsPositionNo);

    GoodsPositionDo getGoodsPosition(Integer goodsPositionNo);

    GoodsLevelDo getGoodsLevel(Integer goodsLevelNo);


    GoodsPositionVo getGoodsPositionVo(Integer goodsPositionNo);

    GoodsLevelVo getGoodsLevelVo(Integer goodsLevelNo);

    /**
     * 根据类型和商品等级获取校验规则
     *
     * @param goodsValidType
     * @param goodsLevelNo
     * @return
     */
    GoodsValidatedRule getValidatedRule(Integer goodsValidType, Integer goodsLevelNo);


    int countTodoItems(Integer skuId);

    /**
     * 推送环节校验商品待办事项
     *
     * @param skuId
     * @return
     */
    GoodsValidResultVo checkGoodsWhileSync(Integer skuId);

    /**
     * 获取待办事项列表
     *
     * @param skuId
     * @param notDoneOnly 是否返回仅返回未完成事项
     * @return
     */
    List<GoodsTodoItemVo> listSkuTodoItems(Integer skuId, boolean notDoneOnly);

    GoodsTodoSummaryVo statGoodsLevelAndPosition();

    /**
     * 推送商品时检查信息
     *
     * @param skuId
     * @return
     */
    ResultInfo checkGoodsInfo4Push(Integer skuId);

    /**
     * 更新SKU的信息同步状态
     *
     * @param skuIds
     * @param status
     * @return
     */
    int updateSynchronizationStatusBySkuIds(List<Integer> skuIds, Integer status);

    List<GoodsValidatedRule>  getAllValidatedRule(Integer goodsValidType );

}
