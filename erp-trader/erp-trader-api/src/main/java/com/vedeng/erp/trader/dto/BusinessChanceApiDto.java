package com.vedeng.erp.trader.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * 商机dto
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/7 14:22
 */
@Getter
@Setter
public class BusinessChanceApiDto {

    /**
     * id
     */
    private Integer bussinessChanceId;

    /**
     * 终端名称
     */
    private String terminalTraderName;

    /**
     * 终端类型 字典
     */
    private Integer terminalTraderType;


    /**
     * 商机单号
     */
    private String bussinessChanceNo;


    /**
     * 商机阶段(1初步洽谈，2商机验证，3初步方案，4最终方案，5赢单，6关闭)
     */
    private Integer stage;

    /**
     * 商机归属销售
     */
    private Integer saleId;
}