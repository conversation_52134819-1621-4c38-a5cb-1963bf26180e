package com.vedeng.authorization.model;

import lombok.Data;

import java.io.Serializable;
@Data
public class ImUserInfoDto implements Serializable {
    private Integer userId;

    private String userName;

    private String realName;//中文名

    private String companyName;// 公司名称

    private String number;//工号

    private String mobile;//手机号

    private String email;//邮箱

    /**
     * 是否贝登员工 1是 0否
     */
    private Integer staff;

    private String positionName; //职位名称

    private String orgName;//部门，组织

    private String roleName; //角色

    private String orgPositionName;//组织职位名称

    private String token;

}
