package com.vedeng.infrastructure.kingdee.common.sdk.entity;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Slf4j
public class SuccessEntity {

    private String Id;

    private String Number;

    private int DIndex;

    private String BillNo;

    ArrayList<JsonObject> NeedReturnData;


    /**
     * @param fields 需要返回的字段
     * @return 返回数据
     */
    public List<Map<String, Object>> returnData(ArrayList<String> fields) {
        log.info("金蝶调用新增或者修改接口需要返回的字段:{}", JSON.toJSONString(fields));
        List<Map<String, Object>> returnDataList = new ArrayList<>();
        // 构建返回map对象
        if (CollUtil.isNotEmpty(NeedReturnData)) {
            NeedReturnData.forEach(data -> {
                Map<String, Object> returnDataMap = new HashMap<>(16);
                fields.forEach(f -> {
                    // 解析字段层级
                    List<String> fieldsLayer = StrUtil.split(f, StrUtil.DOT);
                    if (fieldsLayer.size() == 1) {
                        JsonElement jsonElement = data.get(CollUtil.getFirst(fieldsLayer));
                        JsonObject jsonObject = jsonElement.getAsJsonObject();
                        String returnFieldData = jsonObject.get(f).getAsString();
                        returnDataMap.put(f, returnFieldData);
                    }
                    if (fieldsLayer.size() > 1) {
                        // 当返回层级大于1，表示返回是一个对象
                        String firstLayerField = CollUtil.getFirst(fieldsLayer);
                        // 值为数组对象
                        JsonArray jsonArray = data.getAsJsonArray(CollUtil.getFirst(fieldsLayer));
                        String json = new Gson().toJson(jsonArray);
                        JSONArray objects = JSONUtil.parseArray(json);
                        List<Map> layerData = JSONUtil.toList(objects, Map.class);
                        returnDataMap.put(firstLayerField, layerData);
                    }
                });
                returnDataList.add(returnDataMap);
            });
        }
        log.info("金蝶调用新增或者修改接口需要返回的数据:{}", JSON.toJSONString(returnDataList));
        return returnDataList;
    }

}