package com.smallhospital.dao;


import com.smallhospital.model.ElContract;
import com.smallhospital.model.vo.ELContractVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ELContractModifyMapper {

    /**
     * <b>Description:</b>根据合同id删除合同修改记录<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/4/20
     */
    int deleteByContractId(@Param("contractId")Integer contractId);

    int insertSelective(ElContract record);

    /**
     * <b>Description:</b>根据合同Id查询修改记录<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/4/20
     */
    ELContractVO selectByContractId(@Param("contractId") Integer elContractId);

    int updateByPrimaryKeySelective(ELContractVO record);

}
