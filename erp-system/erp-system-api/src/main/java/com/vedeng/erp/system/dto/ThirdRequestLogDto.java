package com.vedeng.erp.system.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/16 9:15
 */
@Data
public class ThirdRequestLogDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long thirdRequestLogId;

    /**
     * 调用服务名称
     */
    private String serverName;

    /**
     * ERP方法名称
     */
    private String erpMethodName;

    /**
     * URL
     */
    private String url;

    /**
     * 请求方式：0:Post, 1:Get, 2:Put, 3:Delete
     */
    private Integer requestType;

    /**
     * 请求入参
     */
    private String requestParam;

    /**
     * 接口返回值
     */
    private String response;

    /**
     * 重试状态 0.待重试 1.重试完成
     */
    private Integer retryStatus;

    /**
     * 重试次数
     */
    private Integer retryTimes;

    /**
     * 最近一次重试的返回值
     */
    private String latestResponse;

    /**
     * 最近一次重试时间
     */
    private Date latestRetryTime;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 业务单据号
     */
    private String businessNo;
}