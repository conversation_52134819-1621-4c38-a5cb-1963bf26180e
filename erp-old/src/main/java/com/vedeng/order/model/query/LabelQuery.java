package com.vedeng.order.model.query;

import com.vedeng.order.model.vo.SkuVo;
import lombok.Data;

import java.util.List;

/**
 * @Description:  内部备注组件接口参数
 * @Author:       davis
 * @Date:         2021/4/14 下午3:51
 * @Version:      1.0
 */
@Data
public class LabelQuery {

    /**
     * 关联场景ID
     */
    private Integer relationId;

    /**
     * 场景
     */
    private Integer scene;

    /**
     * 人工备注
     */
    private String remark;

    /**
     * 产品集合
     */
    private List<SkuVo> skuList;

    /**
     * 订货号
     */
    private String skuNo;

    /**
     * 产品名称
     */
    private String skuName;

    /**
     * 组件ID
     */
    private Integer componentId;

    /**
     * 备注标签HTML
     */
    private String componentHtml;

    /**
     * 订货号集合
     */
    private List<String> skuNoList;

    /**
     * 批量修改还是单个修改
     */
    private Integer isAll;
}
