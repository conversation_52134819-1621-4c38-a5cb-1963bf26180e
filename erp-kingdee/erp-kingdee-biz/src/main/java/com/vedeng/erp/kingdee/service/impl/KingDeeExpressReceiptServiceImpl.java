package com.vedeng.erp.kingdee.service.impl;

import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeeExpressReceiptCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeExpressReceiptEntity;
import com.vedeng.erp.kingdee.dto.KingDeeExpressReceiptDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeExpressReceiptCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeExpressReceiptConvertor;
import com.vedeng.erp.kingdee.repository.KingDeeExpressReceiptRepository;
import com.vedeng.erp.kingdee.service.KingDeeExpressReceiptApiService;
import com.vedeng.erp.kingdee.service.KingDeeExpressReceiptService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 快递签收推送金蝶
 * @date 2023/4/17 16:00
 **/
@Service
@Slf4j
public class KingDeeExpressReceiptServiceImpl extends KingDeeBaseServiceImpl<
        KingDeeExpressReceiptEntity,
        KingDeeExpressReceiptDto,
        KingDeeExpressReceiptCommand,
        KingDeeExpressReceiptRepository,
        KingDeeExpressReceiptConvertor,
        KingDeeExpressReceiptCommandConvertor>
        implements KingDeeExpressReceiptService, KingDeeExpressReceiptApiService {

    @Override
    public boolean getIsAutoSubmitAndAudit(KingDeeExpressReceiptDto kingDeeExpressReceiptDto){
        return Boolean.FALSE;
    }
}
