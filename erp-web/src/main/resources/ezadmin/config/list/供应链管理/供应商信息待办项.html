<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>供应商信息待办项</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="supplierTodo" datasource="erp-datasourcetarget" fixednumber append_column_url append_row_url empty_show firstcol="numbers">
<div id="appendHead"></div>
<div class="layui-fluid">
    <div class="layui-tab">
        <ul class="layui-tab-title" id="tab">

        </ul>
    </div>
    <form class="layui-form" id="search">

        <div class=" layui-inline ">
            <label class="layui-form-label">ID</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="TRADER_ID" placeholder style alias="T" jdbctype data datatype oper>
            </div>
        </div><div class=" layui-inline ">
        <label class="layui-form-label">供应商名称</label>
        <div class="layui-input-inline">
            <input class=" layui-input list-search-item " type="text" name="TRADER_NAME" placeholder style alias="T" jdbctype data datatype oper>
        </div>
    </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">hidden</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="hidden" name="USER_LIST" placeholder style   jdbctype="BODY" data datatype oper>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">归属采购</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="USER_ID" placeholder style alias="U" jdbctype data="select USER_ID K,USERNAME V FROM T_USER " DATATYPE="KVSQLCACHE" oper></select>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">审核状态</label>
            <div class="layui-input-inline">
                <SELECT class=" layui-input list-search-item " type="SEARCH" name="" placeholder style alias="IFNULL(A.`STATUS`, 3)" jdbctype data='[{"V":"审核中","K":"0"},{"V":"审核通过","K":"1"},{"V":"审核不通过","K":"2"},{"V":"待审核","K":"3"}]' datatype="JSON" oper> </SELECT>
            </div>
        </div>

    </form>
    <hr class="layui-border-blue">
    <div class="btn-group   bd-highlight" id="tableButton">

    </div>
    <table id="table" class="layui-table" style=" width:100%">
        <thead>
        <tr id="column">

            <th item_name="ID" name="ID" body="td-text" head="th">ID</th>
            <th item_name="TRADER_NAME" name="TRADER_NAME" body="td-link" url="/trader/supplier/editbaseinfo.do?traderId=${ID}" opentype="LOCATION" head="th">供应商名称</th>
            <th item_name="ORDER_NO" name="ORDER_NO" body="td-text" head="th" style="max-width: 300px">关联订单</th>
            <th item_name="USER_ID" name="USER_ID" body="td-select" data="select USER_ID K,USERNAME V FROM T_USER " DATATYPE="KVSQLCACHE" head="th">归属采购</th>

            <th item_name="MOD_TIME" name="MOD_TIME" body="td-text" head="th">更新时间</th>
            <th item_name="RISK_TIME" name="RISK_TIME" body="td-text" head="th">首次风控时间</th>
            <th item_name="CHECK_STATUS" name="CHECK_STATUS" body="td-select" head="th" data='[{"V":"审核中","K":"0"},{"V":"审核通过","K":"1"},{"V":"审核不通过","K":"2"},{"V":"待审核","K":"3"}]' datatype="JSON">审核状态</th>
            <th type="rowbutton" id="rowbutton">
                <button type="single" url="/trader/supplier/editbaseinfo.do?traderId=${ID}" opentype="PARENT" windowname="${TRADER_NAME}">编辑</button>
            </th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td> express:</td>
            <td colspan="100">
                <pre id="express" class="layui-code" orderby="" groupby>StringBuilder sql=new StringBuilder();

sql.append("   SELECT
	T.TRADER_ID ID,
	T.TRADER_NAME,
	L.ORDER_NO,
        U.USER_ID,
	U.USERNAME,
	FROM_UNIXTIME(TS.MOD_TIME/1000) MOD_TIME,
                    FROM_UNIXTIME(L.ADD_TIME/1000) RISK_TIME,
	IFNULL(A.`STATUS`,3) CHECK_STATUS
FROM
	(

	SELECT
	      BUZ_ID, GROUP_CONCAT( DISTINCT BUZ_EXTRA) ORDER_NO,T.ADD_TIME
	    FROM T_TODO_LIST T
	    JOIN T_BUYORDER B ON T.BUZ_EXTRA = B.BUYORDER_NO
	    JOIN T_BUYORDER_GOODS BG ON B.BUYORDER_ID = BG.BUYORDER_ID AND BG.IS_DELETE = 0
	    JOIN V_CORE_SKU K ON BG.SKU = K.SKU_NO
	    JOIN V_CORE_SPU P ON K.SPU_ID = P.SPU_ID
	    WHERE T.BUZ_TYPE = 6 AND T.STATUS = 0
                    ");
userId=$("USER_LIST");
if(isNotBlank("USER_LIST")){
                    sql.append("AND ( P.ASSIGNMENT_MANAGER_ID IN ( "+userId+"  ) OR P.ASSIGNMENT_ASSISTANT_ID IN ("+userId+" ))");
                    }

sql.append("
                    GROUP BY BUZ_ID

)L
JOIN T_TRADER T ON L.BUZ_ID = T.TRADER_ID
JOIN T_TRADER_SUPPLIER TS ON T.TRADER_ID = TS.TRADER_ID
LEFT JOIN T_R_TRADER_J_USER TU ON T.TRADER_ID = TU.TRADER_ID AND TU.TRADER_TYPE = 2
LEFT JOIN T_USER U ON TU.USER_ID = U.USER_ID
LEFT JOIN T_VERIFIES_INFO A ON A.RELATE_TABLE_KEY = TS.TRADER_SUPPLIER_ID AND A.RELATE_TABLE = 'T_TRADER_SUPPLIER' AND A.VERIFIES_TYPE = 619
   where 1=1
                    ");


return search(sql);</pre>

            </td>
        </tr>
        <tr>
            <td> count:</td>
            <td colspan="100">
                <pre id="count" class="layui-code"></pre>
            </td>
        </tr>
        </tbody>
    </table>
</div>
<div id="appendFoot"></div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    })
</script>

</body></html>