<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>分类迁移记录</title> 
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="EJh3pbuGVsU" datasource="erp-reportdatasource" fixednumber="2" append_column_url="" append_row_url="" empty_show="" firstcol="numbers">
  <div id="appendHead"></div> 
  <div class="layui-fluid"> 
   <div class="layui-tab"> 
    <ul class="layui-tab-title" id="tab"> 
    </ul> 
   </div> 
   <form class="layui-form" id="search">
    <div class=" layui-inline "><label class="layui-form-label">操作人</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="USERNAME" placeholder="" style="" alias="" jdbctype="" data="" datatype="" oper=""></object> 
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">操作时间</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="daterange" name="UPDATE_TIME_STR" placeholder="" style="" alias="" jdbctype="" data="" datatype="" oper=""></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">目标路径</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="TARGET_PATH" placeholder="" style="" alias="" jdbctype="" data="" datatype="" oper=""></object> 
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">原始路径</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="ORIGIN_PATH" placeholder="" style="" alias="" jdbctype="" data="" datatype="" oper=""></object> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">分类名称</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="CATEGORY_NAME" placeholder="" style="" alias="" jdbctype="" data="" datatype="" oper=""></object> 
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">分类ID</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="CATEGORY_ID" placeholder="" style="" alias="" jdbctype="" data="" datatype="" oper=""></object> 
     </div>
    </div> 
   </form> 
   <hr class="layui-border-blue"> 
   <div class="btn-group   bd-highlight" id="tableButton">
    <button type="table" item_name="导出所有" name="导出所有" url="/ezadmin/list/export-EJh3pbuGVsU" opentype="_BLANK_PARAM" windowname=""  >导出所有</button>
   </div> 
   <table id="table" class="layui-table" style=" width:100%"> 
    <thead> 
     <tr id="column"> 
      <th type="rowbutton" id="rowbutton"></th> 
      <th item_name="CATEGORY_ID" name="CATEGORY_ID" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">分类ID</th>
      <th item_name="CATEGORY_NAME" name="CATEGORY_NAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">分类名称</th>
      <th item_name="ORIGIN_PATH" name="ORIGIN_PATH" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">原始路径</th>
      <th item_name="TARGET_PATH" name="TARGET_PATH" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">目标路径</th>
      <th item_name="ORIGIN_PARENT_ID" name="ORIGIN_PARENT_ID" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">原始父级ID</th>
      <th item_name="TARGET_PARENT_ID" name="TARGET_PARENT_ID" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">目标父级ID</th>
      <th item_name="MIGRATION_REASON" name="MIGRATION_REASON" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">迁移原因</th>
      <th item_name="UPDATE_TIME_STR" name="UPDATE_TIME_STR" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">操作时间</th>
      <th item_name="USERNAME" name="USERNAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">操作人</th>
     </tr> 
    </thead> 
    <tbody> 
     <tr> 
      <td colspan="100"> express: <pre id="express" class="layui-code" orderby="" groupby="">SELECT
        A.CATEGORY_MIGTATION_LOG_ID ID,
	A.CATEGORY_ID,
	A.CATEGORY_NAME,
	A.ORIGIN_PATH,
	A.TARGET_PATH,
	A.ORIGIN_PARENT_ID,
	A.TARGET_PARENT_ID,
	A.MIGRATION_REASON,
	FROM_UNIXTIME(
		A.UPDATE_TIME / 1000, '%Y-%m-%d %H:%i:%s'
	) AS UPDATE_TIME_STR,
	B.USERNAME
FROM
	T_CATEGORY_MIGRATION_LOG A
LEFT JOIN T_USER B ON A.UPDATER = B.USER_ID
where 1=1</pre> count: <pre id="count" class="layui-code"></pre></td> 
     </tr> 
    </tbody> 
   </table> 
  </div> 
  <div id="appendFoot"></div> 
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script> 
  <script>
    layui.use(function () {

    })
</script> 
 </body>
</html>