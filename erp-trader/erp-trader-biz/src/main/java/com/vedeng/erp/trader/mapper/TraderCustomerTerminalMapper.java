package com.vedeng.erp.trader.mapper;

import com.vedeng.erp.trader.domain.entity.TraderCustomerTerminalEntity;
import java.util.List;

import com.vedeng.erp.trader.dto.TraderCustomerTerminalDto;
import com.vedeng.erp.trader.dto.TraderCustomerTerminalQuery;
import com.vedeng.erp.trader.dto.TraderCustomerTerminalResult;
import org.apache.ibatis.annotations.Param;


/**
 * @description ${end}
 * <AUTHOR>
 * @date 2023/9/4 19:07
 **/
public interface TraderCustomerTerminalMapper {
    /**
     * delete by primary key
     * @param traderCustomerTerminalId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer traderCustomerTerminalId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(TraderCustomerTerminalEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(TraderCustomerTerminalEntity record);

    /**
     * select by primary key
     * @param traderCustomerTerminalId primary key
     * @return object by primary key
     */
    TraderCustomerTerminalEntity selectByPrimaryKey(Integer traderCustomerTerminalId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(TraderCustomerTerminalEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(TraderCustomerTerminalEntity record);

    int batchInsert(@Param("list") List<TraderCustomerTerminalEntity> list);

    /**
     * 根据客户id查询 排除删除的
     * @param traderId 客户id
     * @return List<TraderCustomerTerminalEntity>
     */
    List<TraderCustomerTerminalEntity> selectByTraderId(@Param("traderId")Integer traderId);


    /**
     * 更具条件查询
     * @param param
     * @return
     */
    List<TraderCustomerTerminalResult> findByAll(TraderCustomerTerminalQuery param);

    /**
     * 根据客户id 和大数据唯一id查询
     * @param traderId
     * @param dwhTerminalId
     * @return
     */
    List<TraderCustomerTerminalEntity> selectByTraderIdAndDwhTerminalId(@Param("traderId")Integer traderId,@Param("dwhTerminalId")String dwhTerminalId);

    /**
     *
     * @param traderId 客户id
     * @param auditStatus 审核状态
     * @return List<TraderCustomerTerminalEntity>
     */
    List<TraderCustomerTerminalDto> selectByTraderIdAndTraderCustomerIdAndAuditStatus(@Param("traderId")Integer traderId,@Param("auditStatus")Integer auditStatus);


}