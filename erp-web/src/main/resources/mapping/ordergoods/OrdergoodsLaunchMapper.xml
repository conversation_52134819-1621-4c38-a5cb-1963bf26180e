<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.ordergoods.dao.OrdergoodsLaunchMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.ordergoods.model.OrdergoodsLaunch" >
    <id column="ORDERGOODS_LAUNCH_ID" property="ordergoodsLaunchId" jdbcType="INTEGER" />
    <result column="R_ORDERGOODS_LAUNCH_GOODS_J_CATEGORY_ID" property="rOrdergoodsLaunchGoodsJCategoryId" jdbcType="INTEGER" />
    <result column="TRADER_CUSTOMER_ID" property="traderCustomerId" jdbcType="INTEGER" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    ORDERGOODS_LAUNCH_ID, R_ORDERGOODS_LAUNCH_GOODS_J_CATEGORY_ID, TRADER_CUSTOMER_ID, 
    ADD_TIME, CREATOR, MOD_TIME, UPDATER
  </sql>
  <select id="selectByPrimaryKey" resultType="com.vedeng.ordergoods.model.vo.OrdergoodsLaunchVo" parameterType="java.lang.Integer" >
  	select
  		a.ORDERGOODS_LAUNCH_ID, a.TRADER_CUSTOMER_ID, a.R_ORDERGOODS_LAUNCH_GOODS_J_CATEGORY_ID,
  		b.startTime,b.endTime,b.goalAmount,
    	c.GOODS_ID,c.ORDERGOODS_CATEGORY_IDS,c.ORDERGOODS_STORE_ID
  	from
  		T_ORDERGOODS_LAUNCH a
  	left join
  	(
  	select
  	min(aa.START_TIME) as startTime,max(aa.END_TIME) as endTime,sum(GOAL_AMOUNT) as goalAmount,aa.ORDERGOODS_LAUNCH_ID
  	from
  	T_ORDERGOODS_LAUNCH_GOAL aa
  	group by aa.ORDERGOODS_LAUNCH_ID
  	) b on a.ORDERGOODS_LAUNCH_ID = b.ORDERGOODS_LAUNCH_ID
  	left join
  		T_R_ORDERGOODS_LAUNCH_GOODS_J_CATEGORY c
  	on
  		c.R_ORDERGOODS_LAUNCH_GOODS_J_CATEGORY_ID = a.R_ORDERGOODS_LAUNCH_GOODS_J_CATEGORY_ID
    where a.ORDERGOODS_LAUNCH_ID = #{ordergoodsLaunchId,jdbcType=INTEGER}
  </select>
  <insert id="insert" parameterType="com.vedeng.ordergoods.model.OrdergoodsLaunch" >
    insert into T_ORDERGOODS_LAUNCH
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="rOrdergoodsLaunchGoodsJCategoryId != null" >
        R_ORDERGOODS_LAUNCH_GOODS_J_CATEGORY_ID,
      </if>
      <if test="traderCustomerId != null" >
        TRADER_CUSTOMER_ID,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="rOrdergoodsLaunchGoodsJCategoryId != null" >
        #{rOrdergoodsLaunchGoodsJCategoryId,jdbcType=INTEGER},
      </if>
      <if test="traderCustomerId != null" >
        #{traderCustomerId,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
    
    <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="ordergoodsLaunchId">
		SELECT LAST_INSERT_ID() AS ordergoodsLaunchId
    </selectKey>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.ordergoods.model.OrdergoodsLaunch" >
    update T_ORDERGOODS_LAUNCH
    <set >
      <if test="rOrdergoodsLaunchGoodsJCategoryId != null" >
        R_ORDERGOODS_LAUNCH_GOODS_J_CATEGORY_ID = #{rOrdergoodsLaunchGoodsJCategoryId,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where ORDERGOODS_LAUNCH_ID = #{ordergoodsLaunchId,jdbcType=INTEGER}
  </update>
  
  <select id="getOrdergoodsLaunchListPage" parameterType="Map" resultType="com.vedeng.ordergoods.model.vo.OrdergoodsLaunchVo">
  	select
  	a.ORDERGOODS_LAUNCH_ID, a.TRADER_CUSTOMER_ID, a.R_ORDERGOODS_LAUNCH_GOODS_J_CATEGORY_ID,
    b.startTime,b.endTime,b.goalAmount,
    c.GOODS_ID,c.ORDERGOODS_CATEGORY_IDS,c.ORDERGOODS_STORE_ID
  	from
  	T_ORDERGOODS_LAUNCH a
  	left join
  	(
  	select
  	min(aa.START_TIME) as startTime,max(aa.END_TIME) as endTime,sum(GOAL_AMOUNT) as goalAmount,aa.ORDERGOODS_LAUNCH_ID
  	from
  	T_ORDERGOODS_LAUNCH_GOAL aa
  	group by aa.ORDERGOODS_LAUNCH_ID
  	) b on a.ORDERGOODS_LAUNCH_ID = b.ORDERGOODS_LAUNCH_ID
  	left join
  	T_R_ORDERGOODS_LAUNCH_GOODS_J_CATEGORY c
  	on
  	c.R_ORDERGOODS_LAUNCH_GOODS_J_CATEGORY_ID = a.R_ORDERGOODS_LAUNCH_GOODS_J_CATEGORY_ID
  	where
  	 c.ORDERGOODS_STORE_ID = #{ordergoodsLaunchVo.ordergoodsStoreId}
  	 <if test="ordergoodsLaunchVo.goodsIds!= null and ordergoodsLaunchVo.goodsIds.size > 0">
  	 	and c.GOODS_ID in 
  	 	<foreach collection="ordergoodsLaunchVo.goodsIds" item="goodsId" open="(" close=")" separator=",">
			#{goodsId,jdbcType=INTEGER}
		</foreach>
  	 </if>
  	 <if test="ordergoodsLaunchVo.traderCustomerIds!= null and ordergoodsLaunchVo.traderCustomerIds.size > 0">
  	 	and a.TRADER_CUSTOMER_ID in 
  	 	<foreach collection="ordergoodsLaunchVo.traderCustomerIds" item="traderCustomerId" open="(" close=")" separator=",">
			#{traderCustomerId,jdbcType=INTEGER}
		</foreach>
  	 </if>
  </select>
</mapper>