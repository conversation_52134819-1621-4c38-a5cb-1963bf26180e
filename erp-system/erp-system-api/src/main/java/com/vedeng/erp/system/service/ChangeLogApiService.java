package com.vedeng.erp.system.service;

import com.vedeng.erp.system.common.enums.ChangeRelatedTableEnums;
import com.vedeng.erp.system.dto.ChangeLogDto;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName ChangeLogApiService.java
 * @Description TODO
 * @createTime 2023年02月07日 13:46:00
 */
public interface ChangeLogApiService {
    /**
     * @param enums
     * @param newData 新数据
     * @param oldData 旧数据
     * @param creator
     */
    void saveLog(ChangeRelatedTableEnums enums, List<?> newData, List<?> oldData,Integer creator);

    List<ChangeLogDto> getInfo(ChangeRelatedTableEnums tQuoteorderGoods);

    void insertSelective(ChangeRelatedTableEnums tQuoteorderGoods, String logMessage, Integer creator);
}
