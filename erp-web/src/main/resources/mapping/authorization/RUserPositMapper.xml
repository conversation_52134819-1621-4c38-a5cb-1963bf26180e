<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.authorization.dao.RUserPositMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.authorization.model.RUserPosit" >
    <id column="USER_POSIT_ID" property="userPositId" jdbcType="INTEGER" />
    <result column="USER_ID" property="userId" jdbcType="INTEGER" />
    <result column="POSITION_ID" property="positionId" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    USER_POSIT_ID, USER_ID, POSITION_ID
  </sql>
  
  <!-- deleteByUserId -->
  <delete id="deleteByUserId" parameterType="java.lang.Integer" >
    delete from T_R_USER_POSIT
    where USER_ID = #{userId,jdbcType=INTEGER}
  </delete>
  
  <!-- 添加员工职位 -->
  <insert id="insert" parameterType="com.vedeng.authorization.model.RUserPosit" >
    insert into T_R_USER_POSIT
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="userPositId != null" >
        USER_POSIT_ID,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="positionId != null" >
        POSITION_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="userPositId != null" >
        #{userPositId,jdbcType=INTEGER},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="positionId != null" >
        #{positionId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
</mapper>