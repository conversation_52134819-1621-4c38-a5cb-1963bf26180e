package com.vedeng.erp.trader.domain.vo;

import com.vedeng.erp.trader.domain.entity.TraderCustomerCategoryEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 客户信息 非数据库entity
 * @date 2022/7/13 15:15
 **/
@Data
public class TraderCustomerInfoVo {

    private static final long serialVersionUID = 1L;

    /**
     * 客户 customerID
     */
    private Integer traderCustomerId;

    /**
     * 客户id
     */
    private Integer traderId;

    /**
     * 客户类型分类ID
     */
    private Integer traderCustomerCategoryId;

    /**
     * 金额余额
     */
    private BigDecimal amount;

    /**
     * 所有制公立私立字典表
     */
    private Integer ownership;

    /**
     * 注册资金
     */
    private String registeredCapital;

    /**
     * 注册年份
     */
    private Date registeredDate;

    /**
     * 注册年份
     */
    private String registeredDateStr;

    /**
     * 客户等级 str
     */
    private String customerLevelStr;

    /**
     * 客户等级字典表
     */
    private Integer customerLevel;


	/**
     * 客户类型
     */
    private Integer customerType; 

	/**
     * 客户性质
     */
    private Integer customerNature;

	/**
     *地区ids
     */
    private String areaIds;

	/** 所属区域id*/
    private Integer areaId;

    /**
     * 地址
     */
    private String address;

	/** 客户类型*/
    private String customerTypeStr;

	/** 客户性质*/
    private String customerNatureStr;

	/**客户名称*/
    private String traderName;

    /**
     * 归属销售 名
     */
    private String saleName;

    /**
     * 归属销售id
     */
    private Integer saleId;

    /**
     * 是否归属当前用户
     */
    private boolean belong;

    /**
     * 是否分享给当前查询人了
     */
    private boolean share;

    private List<TraderCustomerCategoryEntity> customerCategories;

    /**
     * 是否天眼查标识，此字段为Y时，表示曾经erp查过天眼查并缓存过此数据
     */
    private String tycFlag;

    /**
     * 是否启用
     */
    private Integer isEnable;
    /**
     * 是否是下属
     */
    private boolean subUser;
}
