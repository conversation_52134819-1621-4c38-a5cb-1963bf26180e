package com.vedeng.infrastructure.kingdee.domain.dto;

import com.vedeng.infrastructure.kingdee.common.sdk.entity.JsonBase;
import lombok.Data;

import java.util.List;

@Data
public class KingDeeSysReportResultDto extends JsonBase {

    private List<SysReportBaseDto> result;

    @Data
    public static class SysReportBaseDto {
        private Boolean isSuccess;

        private Integer rowCount;

        private List<List<String>> rows;
    }
}
