package com.vedeng.logistics.rabbitmq;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.rabbitmq.client.Channel;
import com.vedeng.common.trace.mq.AbstractMessageListener;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.logistics.dao.ExpressMapper;
import com.vedeng.logistics.model.Express;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.model.Buyorder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 采购直发订单能否确认收货-死信队列消费者
 */
@Component
@Slf4j
public class BuyOrderEnableReceiveUnlockConsumer extends AbstractMessageListener {
    @Resource
    BuyorderMapper buyorderMapper;
    @Resource
    ExpressMapper expressMapper;

    @Override
    public void doBusiness(Message message, Channel channel) throws Exception {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("采购直发订单能否确认收货24H后解锁-回传消息：[{}]", message);
        try {
            handleMessage(messageBody);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), Boolean.FALSE);
        } catch (Exception e) {
            log.error("采购直发订单能否确认收货24H后解锁，express信息：{}，错误信息：", messageBody, e);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), Boolean.FALSE, Boolean.FALSE);
            } catch (IOException ex) {
                log.error("采购直发订单能否确认收货24H后解锁-消费失败，将消息返回给rabbitmq错误：", ex);
            }
        }
    }

    private void handleMessage(String messageBody) {
        Express expressMessage = JSONUtil.toBean(messageBody, Express.class);
        Express express = expressMapper.getExpressInfoById(expressMessage.getExpressId());
        if (ObjectUtil.isNotNull(express)
                && null != express.getArrivalStatus()
                && 0 == express.getArrivalStatus()
                && 1 == express.getEnableReceive()
                && 1==express.getIsEnable()){
            //快递关联的直发采购订单
            List<Buyorder> buyorderList = buyorderMapper.selectBuyorderByExpressId(express.getExpressId());
            if (CollUtil.isEmpty(buyorderList)){
                log.info("采购直发订单能否确认收货24H后解锁,快递关联的直发采购订单为空,快递信息:{}",JSONUtil.toJsonStr(express));
                return;
            }
            buyorderList.forEach(b -> {
                //发送站内信给直发采购单创建人
                Map<String, String> mapParams = new HashMap<>(4);
                mapParams.put("buyOrderNo", b.getBuyorderNo());
                mapParams.put("logisticsNo", express.getLogisticsNo());
                MessageUtil.sendMessage2(263, CollUtil.toList(b.getCreator()), mapParams, "./order/newBuyorder/newViewBuyOrderDetail.do?buyorderId=" + b.getBuyorderId(), "njadmin");
            });
        }
    }
}
