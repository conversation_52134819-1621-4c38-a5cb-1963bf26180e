package com.vedeng.erp.trader.domain.entity;

import lombok.Data;

import java.util.Date;

/**
 * 操作记录表
 * <AUTHOR>
 */
@Data
public class VisitRecordLog {
    /**
     * 记录ID
     */
    private Integer id;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 操作时间
     */
    private Date operationTime;

    /**
     * 操作类型A创建B修改C打卡D添加拜访记录E删除
     */
    private String operationType;

    /**
     * 描述
     */
    private String description;

    /**
     * 关联表的ID
     */
    private Integer recordId;

}