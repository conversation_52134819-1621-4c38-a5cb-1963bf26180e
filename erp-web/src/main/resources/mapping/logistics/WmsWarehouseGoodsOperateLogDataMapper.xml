<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.logistics.dao.WmsWarehouseGoodsOperateLogDataMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.logistics.model.WmsWarehouseGoodsOperateLogData">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Feb 21 14:37:02 CST 2025.
    -->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="WAREHOUSE_GOODS_OPERATE_LOG_ID" jdbcType="INTEGER" property="warehouseGoodsOperateLogId" />
    <result column="OPERATE_TYPE" jdbcType="INTEGER" property="operateType" />
    <result column="ORDER_ID" jdbcType="INTEGER" property="orderId" />
    <result column="RELATED_ID" jdbcType="INTEGER" property="relatedId" />
    <result column="BUSINESS_NO" jdbcType="VARCHAR" property="businessNo" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName" />
    <result column="USER_NAME" jdbcType="VARCHAR" property="userName" />
    <result column="DEPT_NAME" jdbcType="VARCHAR" property="deptName" />
    <result column="SKU_NO" jdbcType="VARCHAR" property="skuNo" />
    <result column="SKU_NAME" jdbcType="VARCHAR" property="skuName" />
    <result column="SKU_MODEL" jdbcType="VARCHAR" property="skuModel" />
    <result column="SKU_SPEC" jdbcType="VARCHAR" property="skuSpec" />
    <result column="SKU_NUM" jdbcType="INTEGER" property="skuNum" />
    <result column="REGISTER_NUMBER" jdbcType="VARCHAR" property="registerNumber" />
    <result column="BRAND_NAME" jdbcType="VARCHAR" property="brandName" />
    <result column="BARCODE_FACTORY" jdbcType="VARCHAR" property="barcodeFactory" />
    <result column="BATCH_NUMBER" jdbcType="VARCHAR" property="batchNumber" />
    <result column="VEDENG_BATCH_NUMER" jdbcType="VARCHAR" property="vedengBatchNumer" />
    <result column="PRODUCT_DATE" jdbcType="TIMESTAMP" property="productDate" />
    <result column="EXPIRATION_DATE" jdbcType="TIMESTAMP" property="expirationDate" />
    <result column="EXPRESS_NOS" jdbcType="VARCHAR" property="expressNos" />
    <result column="QUALITY_REPORT" jdbcType="VARCHAR" property="qualityReport" />
    <result column="QUALITY_REPORT_OSS" jdbcType="VARCHAR" property="qualityReportOss" />
    <result column="QUALITY_REPORT_SIZE" jdbcType="INTEGER" property="qualityReportSize" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MODE_TIME" jdbcType="TIMESTAMP" property="modeTime" />
    <result column="IS_DELETE" jdbcType="TINYINT" property="isDelete" />
    <result column="OUT_TIME" jdbcType="TIMESTAMP" property="outTime" />
    <result column="STERILZATION_BATCH_NUMBER" jdbcType="VARCHAR" property="sterilzationBatchNumber" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Feb 21 14:37:02 CST 2025.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Feb 21 14:37:02 CST 2025.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Feb 21 14:37:02 CST 2025.
    -->
    ID, WAREHOUSE_GOODS_OPERATE_LOG_ID, OPERATE_TYPE, ORDER_ID, RELATED_ID, BUSINESS_NO, 
    TRADER_ID, TRADER_NAME, USER_NAME, DEPT_NAME, SKU_NO, SKU_NAME, SKU_MODEL, SKU_SPEC, 
    SKU_NUM, REGISTER_NUMBER, BRAND_NAME, BARCODE_FACTORY, BATCH_NUMBER, VEDENG_BATCH_NUMER, 
    PRODUCT_DATE, EXPIRATION_DATE, EXPRESS_NOS, QUALITY_REPORT, QUALITY_REPORT_OSS, QUALITY_REPORT_SIZE, 
    CREATOR, UPDATER, ADD_TIME, MODE_TIME, IS_DELETE, OUT_TIME, STERILZATION_BATCH_NUMBER
  </sql>
  <select id="selectByExample" parameterType="com.vedeng.logistics.model.WmsWarehouseGoodsOperateLogDataExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Feb 21 14:37:02 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Feb 21 14:37:02 CST 2025.
    -->
    select 
    <include refid="Base_Column_List" />
    from T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
    where ID = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Feb 21 14:37:02 CST 2025.
    -->
    delete from T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
    where ID = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.logistics.model.WmsWarehouseGoodsOperateLogDataExample">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Feb 21 14:37:02 CST 2025.
    -->
    delete from T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.logistics.model.WmsWarehouseGoodsOperateLogData">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Feb 21 14:37:02 CST 2025.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_WAREHOUSE_GOODS_OPERATE_LOG_DATA (WAREHOUSE_GOODS_OPERATE_LOG_ID, OPERATE_TYPE, 
      ORDER_ID, RELATED_ID, BUSINESS_NO, 
      TRADER_ID, TRADER_NAME, USER_NAME, 
      DEPT_NAME, SKU_NO, SKU_NAME, 
      SKU_MODEL, SKU_SPEC, SKU_NUM, 
      REGISTER_NUMBER, BRAND_NAME, BARCODE_FACTORY, 
      BATCH_NUMBER, VEDENG_BATCH_NUMER, PRODUCT_DATE, 
      EXPIRATION_DATE, EXPRESS_NOS, QUALITY_REPORT, 
      QUALITY_REPORT_OSS, QUALITY_REPORT_SIZE, CREATOR, 
      UPDATER, ADD_TIME, MODE_TIME, 
      IS_DELETE, OUT_TIME, STERILZATION_BATCH_NUMBER
      )
    values (#{warehouseGoodsOperateLogId,jdbcType=INTEGER}, #{operateType,jdbcType=INTEGER}, 
      #{orderId,jdbcType=INTEGER}, #{relatedId,jdbcType=INTEGER}, #{businessNo,jdbcType=VARCHAR}, 
      #{traderId,jdbcType=INTEGER}, #{traderName,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, 
      #{deptName,jdbcType=VARCHAR}, #{skuNo,jdbcType=VARCHAR}, #{skuName,jdbcType=VARCHAR}, 
      #{skuModel,jdbcType=VARCHAR}, #{skuSpec,jdbcType=VARCHAR}, #{skuNum,jdbcType=INTEGER}, 
      #{registerNumber,jdbcType=VARCHAR}, #{brandName,jdbcType=VARCHAR}, #{barcodeFactory,jdbcType=VARCHAR}, 
      #{batchNumber,jdbcType=VARCHAR}, #{vedengBatchNumer,jdbcType=VARCHAR}, #{productDate,jdbcType=TIMESTAMP}, 
      #{expirationDate,jdbcType=TIMESTAMP}, #{expressNos,jdbcType=VARCHAR}, #{qualityReport,jdbcType=VARCHAR}, 
      #{qualityReportOss,jdbcType=VARCHAR}, #{qualityReportSize,jdbcType=INTEGER}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{modeTime,jdbcType=TIMESTAMP}, 
      #{isDelete,jdbcType=TINYINT}, #{outTime,jdbcType=TIMESTAMP}, #{sterilzationBatchNumber,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.logistics.model.WmsWarehouseGoodsOperateLogData">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Feb 21 14:37:02 CST 2025.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="warehouseGoodsOperateLogId != null">
        WAREHOUSE_GOODS_OPERATE_LOG_ID,
      </if>
      <if test="operateType != null">
        OPERATE_TYPE,
      </if>
      <if test="orderId != null">
        ORDER_ID,
      </if>
      <if test="relatedId != null">
        RELATED_ID,
      </if>
      <if test="businessNo != null">
        BUSINESS_NO,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderName != null">
        TRADER_NAME,
      </if>
      <if test="userName != null">
        USER_NAME,
      </if>
      <if test="deptName != null">
        DEPT_NAME,
      </if>
      <if test="skuNo != null">
        SKU_NO,
      </if>
      <if test="skuName != null">
        SKU_NAME,
      </if>
      <if test="skuModel != null">
        SKU_MODEL,
      </if>
      <if test="skuSpec != null">
        SKU_SPEC,
      </if>
      <if test="skuNum != null">
        SKU_NUM,
      </if>
      <if test="registerNumber != null">
        REGISTER_NUMBER,
      </if>
      <if test="brandName != null">
        BRAND_NAME,
      </if>
      <if test="barcodeFactory != null">
        BARCODE_FACTORY,
      </if>
      <if test="batchNumber != null">
        BATCH_NUMBER,
      </if>
      <if test="vedengBatchNumer != null">
        VEDENG_BATCH_NUMER,
      </if>
      <if test="productDate != null">
        PRODUCT_DATE,
      </if>
      <if test="expirationDate != null">
        EXPIRATION_DATE,
      </if>
      <if test="expressNos != null">
        EXPRESS_NOS,
      </if>
      <if test="qualityReport != null">
        QUALITY_REPORT,
      </if>
      <if test="qualityReportOss != null">
        QUALITY_REPORT_OSS,
      </if>
      <if test="qualityReportSize != null">
        QUALITY_REPORT_SIZE,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modeTime != null">
        MODE_TIME,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="outTime != null">
        OUT_TIME,
      </if>
      <if test="sterilzationBatchNumber != null">
        STERILZATION_BATCH_NUMBER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="warehouseGoodsOperateLogId != null">
        #{warehouseGoodsOperateLogId,jdbcType=INTEGER},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="relatedId != null">
        #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="businessNo != null">
        #{businessNo,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderName != null">
        #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="deptName != null">
        #{deptName,jdbcType=VARCHAR},
      </if>
      <if test="skuNo != null">
        #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null">
        #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="skuModel != null">
        #{skuModel,jdbcType=VARCHAR},
      </if>
      <if test="skuSpec != null">
        #{skuSpec,jdbcType=VARCHAR},
      </if>
      <if test="skuNum != null">
        #{skuNum,jdbcType=INTEGER},
      </if>
      <if test="registerNumber != null">
        #{registerNumber,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null">
        #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="barcodeFactory != null">
        #{barcodeFactory,jdbcType=VARCHAR},
      </if>
      <if test="batchNumber != null">
        #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="vedengBatchNumer != null">
        #{vedengBatchNumer,jdbcType=VARCHAR},
      </if>
      <if test="productDate != null">
        #{productDate,jdbcType=TIMESTAMP},
      </if>
      <if test="expirationDate != null">
        #{expirationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="expressNos != null">
        #{expressNos,jdbcType=VARCHAR},
      </if>
      <if test="qualityReport != null">
        #{qualityReport,jdbcType=VARCHAR},
      </if>
      <if test="qualityReportOss != null">
        #{qualityReportOss,jdbcType=VARCHAR},
      </if>
      <if test="qualityReportSize != null">
        #{qualityReportSize,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modeTime != null">
        #{modeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="outTime != null">
        #{outTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sterilzationBatchNumber != null">
        #{sterilzationBatchNumber,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.logistics.model.WmsWarehouseGoodsOperateLogDataExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Feb 21 14:37:02 CST 2025.
    -->
    select count(*) from T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Feb 21 14:37:02 CST 2025.
    -->
    update T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
    <set>
      <if test="record.id != null">
        ID = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.warehouseGoodsOperateLogId != null">
        WAREHOUSE_GOODS_OPERATE_LOG_ID = #{record.warehouseGoodsOperateLogId,jdbcType=INTEGER},
      </if>
      <if test="record.operateType != null">
        OPERATE_TYPE = #{record.operateType,jdbcType=INTEGER},
      </if>
      <if test="record.orderId != null">
        ORDER_ID = #{record.orderId,jdbcType=INTEGER},
      </if>
      <if test="record.relatedId != null">
        RELATED_ID = #{record.relatedId,jdbcType=INTEGER},
      </if>
      <if test="record.businessNo != null">
        BUSINESS_NO = #{record.businessNo,jdbcType=VARCHAR},
      </if>
      <if test="record.traderId != null">
        TRADER_ID = #{record.traderId,jdbcType=INTEGER},
      </if>
      <if test="record.traderName != null">
        TRADER_NAME = #{record.traderName,jdbcType=VARCHAR},
      </if>
      <if test="record.userName != null">
        USER_NAME = #{record.userName,jdbcType=VARCHAR},
      </if>
      <if test="record.deptName != null">
        DEPT_NAME = #{record.deptName,jdbcType=VARCHAR},
      </if>
      <if test="record.skuNo != null">
        SKU_NO = #{record.skuNo,jdbcType=VARCHAR},
      </if>
      <if test="record.skuName != null">
        SKU_NAME = #{record.skuName,jdbcType=VARCHAR},
      </if>
      <if test="record.skuModel != null">
        SKU_MODEL = #{record.skuModel,jdbcType=VARCHAR},
      </if>
      <if test="record.skuSpec != null">
        SKU_SPEC = #{record.skuSpec,jdbcType=VARCHAR},
      </if>
      <if test="record.skuNum != null">
        SKU_NUM = #{record.skuNum,jdbcType=INTEGER},
      </if>
      <if test="record.registerNumber != null">
        REGISTER_NUMBER = #{record.registerNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.brandName != null">
        BRAND_NAME = #{record.brandName,jdbcType=VARCHAR},
      </if>
      <if test="record.barcodeFactory != null">
        BARCODE_FACTORY = #{record.barcodeFactory,jdbcType=VARCHAR},
      </if>
      <if test="record.batchNumber != null">
        BATCH_NUMBER = #{record.batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.vedengBatchNumer != null">
        VEDENG_BATCH_NUMER = #{record.vedengBatchNumer,jdbcType=VARCHAR},
      </if>
      <if test="record.productDate != null">
        PRODUCT_DATE = #{record.productDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.expirationDate != null">
        EXPIRATION_DATE = #{record.expirationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.expressNos != null">
        EXPRESS_NOS = #{record.expressNos,jdbcType=VARCHAR},
      </if>
      <if test="record.qualityReport != null">
        QUALITY_REPORT = #{record.qualityReport,jdbcType=VARCHAR},
      </if>
      <if test="record.qualityReportOss != null">
        QUALITY_REPORT_OSS = #{record.qualityReportOss,jdbcType=VARCHAR},
      </if>
      <if test="record.qualityReportSize != null">
        QUALITY_REPORT_SIZE = #{record.qualityReportSize,jdbcType=INTEGER},
      </if>
      <if test="record.creator != null">
        CREATOR = #{record.creator,jdbcType=INTEGER},
      </if>
      <if test="record.updater != null">
        UPDATER = #{record.updater,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        ADD_TIME = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modeTime != null">
        MODE_TIME = #{record.modeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDelete != null">
        IS_DELETE = #{record.isDelete,jdbcType=TINYINT},
      </if>
      <if test="record.outTime != null">
        OUT_TIME = #{record.outTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.sterilzationBatchNumber != null">
        STERILZATION_BATCH_NUMBER = #{record.sterilzationBatchNumber,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Feb 21 14:37:02 CST 2025.
    -->
    update T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
    set ID = #{record.id,jdbcType=BIGINT},
      WAREHOUSE_GOODS_OPERATE_LOG_ID = #{record.warehouseGoodsOperateLogId,jdbcType=INTEGER},
      OPERATE_TYPE = #{record.operateType,jdbcType=INTEGER},
      ORDER_ID = #{record.orderId,jdbcType=INTEGER},
      RELATED_ID = #{record.relatedId,jdbcType=INTEGER},
      BUSINESS_NO = #{record.businessNo,jdbcType=VARCHAR},
      TRADER_ID = #{record.traderId,jdbcType=INTEGER},
      TRADER_NAME = #{record.traderName,jdbcType=VARCHAR},
      USER_NAME = #{record.userName,jdbcType=VARCHAR},
      DEPT_NAME = #{record.deptName,jdbcType=VARCHAR},
      SKU_NO = #{record.skuNo,jdbcType=VARCHAR},
      SKU_NAME = #{record.skuName,jdbcType=VARCHAR},
      SKU_MODEL = #{record.skuModel,jdbcType=VARCHAR},
      SKU_SPEC = #{record.skuSpec,jdbcType=VARCHAR},
      SKU_NUM = #{record.skuNum,jdbcType=INTEGER},
      REGISTER_NUMBER = #{record.registerNumber,jdbcType=VARCHAR},
      BRAND_NAME = #{record.brandName,jdbcType=VARCHAR},
      BARCODE_FACTORY = #{record.barcodeFactory,jdbcType=VARCHAR},
      BATCH_NUMBER = #{record.batchNumber,jdbcType=VARCHAR},
      VEDENG_BATCH_NUMER = #{record.vedengBatchNumer,jdbcType=VARCHAR},
      PRODUCT_DATE = #{record.productDate,jdbcType=TIMESTAMP},
      EXPIRATION_DATE = #{record.expirationDate,jdbcType=TIMESTAMP},
      EXPRESS_NOS = #{record.expressNos,jdbcType=VARCHAR},
      QUALITY_REPORT = #{record.qualityReport,jdbcType=VARCHAR},
      QUALITY_REPORT_OSS = #{record.qualityReportOss,jdbcType=VARCHAR},
      QUALITY_REPORT_SIZE = #{record.qualityReportSize,jdbcType=INTEGER},
      CREATOR = #{record.creator,jdbcType=INTEGER},
      UPDATER = #{record.updater,jdbcType=INTEGER},
      ADD_TIME = #{record.addTime,jdbcType=TIMESTAMP},
      MODE_TIME = #{record.modeTime,jdbcType=TIMESTAMP},
      IS_DELETE = #{record.isDelete,jdbcType=TINYINT},
      OUT_TIME = #{record.outTime,jdbcType=TIMESTAMP},
      STERILZATION_BATCH_NUMBER = #{record.sterilzationBatchNumber,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.logistics.model.WmsWarehouseGoodsOperateLogData">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Feb 21 14:37:02 CST 2025.
    -->
    update T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
    <set>
      <if test="warehouseGoodsOperateLogId != null">
        WAREHOUSE_GOODS_OPERATE_LOG_ID = #{warehouseGoodsOperateLogId,jdbcType=INTEGER},
      </if>
      <if test="operateType != null">
        OPERATE_TYPE = #{operateType,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        ORDER_ID = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="relatedId != null">
        RELATED_ID = #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="businessNo != null">
        BUSINESS_NO = #{businessNo,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderName != null">
        TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        USER_NAME = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="deptName != null">
        DEPT_NAME = #{deptName,jdbcType=VARCHAR},
      </if>
      <if test="skuNo != null">
        SKU_NO = #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null">
        SKU_NAME = #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="skuModel != null">
        SKU_MODEL = #{skuModel,jdbcType=VARCHAR},
      </if>
      <if test="skuSpec != null">
        SKU_SPEC = #{skuSpec,jdbcType=VARCHAR},
      </if>
      <if test="skuNum != null">
        SKU_NUM = #{skuNum,jdbcType=INTEGER},
      </if>
      <if test="registerNumber != null">
        REGISTER_NUMBER = #{registerNumber,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null">
        BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="barcodeFactory != null">
        BARCODE_FACTORY = #{barcodeFactory,jdbcType=VARCHAR},
      </if>
      <if test="batchNumber != null">
        BATCH_NUMBER = #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="vedengBatchNumer != null">
        VEDENG_BATCH_NUMER = #{vedengBatchNumer,jdbcType=VARCHAR},
      </if>
      <if test="productDate != null">
        PRODUCT_DATE = #{productDate,jdbcType=TIMESTAMP},
      </if>
      <if test="expirationDate != null">
        EXPIRATION_DATE = #{expirationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="expressNos != null">
        EXPRESS_NOS = #{expressNos,jdbcType=VARCHAR},
      </if>
      <if test="qualityReport != null">
        QUALITY_REPORT = #{qualityReport,jdbcType=VARCHAR},
      </if>
      <if test="qualityReportOss != null">
        QUALITY_REPORT_OSS = #{qualityReportOss,jdbcType=VARCHAR},
      </if>
      <if test="qualityReportSize != null">
        QUALITY_REPORT_SIZE = #{qualityReportSize,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modeTime != null">
        MODE_TIME = #{modeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="outTime != null">
        OUT_TIME = #{outTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sterilzationBatchNumber != null">
        STERILZATION_BATCH_NUMBER = #{sterilzationBatchNumber,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.logistics.model.WmsWarehouseGoodsOperateLogData">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Feb 21 14:37:02 CST 2025.
    -->
    update T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
    set WAREHOUSE_GOODS_OPERATE_LOG_ID = #{warehouseGoodsOperateLogId,jdbcType=INTEGER},
      OPERATE_TYPE = #{operateType,jdbcType=INTEGER},
      ORDER_ID = #{orderId,jdbcType=INTEGER},
      RELATED_ID = #{relatedId,jdbcType=INTEGER},
      BUSINESS_NO = #{businessNo,jdbcType=VARCHAR},
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      USER_NAME = #{userName,jdbcType=VARCHAR},
      DEPT_NAME = #{deptName,jdbcType=VARCHAR},
      SKU_NO = #{skuNo,jdbcType=VARCHAR},
      SKU_NAME = #{skuName,jdbcType=VARCHAR},
      SKU_MODEL = #{skuModel,jdbcType=VARCHAR},
      SKU_SPEC = #{skuSpec,jdbcType=VARCHAR},
      SKU_NUM = #{skuNum,jdbcType=INTEGER},
      REGISTER_NUMBER = #{registerNumber,jdbcType=VARCHAR},
      BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      BARCODE_FACTORY = #{barcodeFactory,jdbcType=VARCHAR},
      BATCH_NUMBER = #{batchNumber,jdbcType=VARCHAR},
      VEDENG_BATCH_NUMER = #{vedengBatchNumer,jdbcType=VARCHAR},
      PRODUCT_DATE = #{productDate,jdbcType=TIMESTAMP},
      EXPIRATION_DATE = #{expirationDate,jdbcType=TIMESTAMP},
      EXPRESS_NOS = #{expressNos,jdbcType=VARCHAR},
      QUALITY_REPORT = #{qualityReport,jdbcType=VARCHAR},
      QUALITY_REPORT_OSS = #{qualityReportOss,jdbcType=VARCHAR},
      QUALITY_REPORT_SIZE = #{qualityReportSize,jdbcType=INTEGER},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MODE_TIME = #{modeTime,jdbcType=TIMESTAMP},
      IS_DELETE = #{isDelete,jdbcType=TINYINT},
      OUT_TIME = #{outTime,jdbcType=TIMESTAMP},
      STERILZATION_BATCH_NUMBER = #{sterilzationBatchNumber,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=BIGINT}
  </update>
</mapper>