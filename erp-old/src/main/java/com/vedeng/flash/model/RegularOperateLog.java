package com.vedeng.flash.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Description:  定品备货操作
 * @Author:       davis
 * @Date:         2021/5/18 下午7:40
 * @Version:      1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RegularOperateLog {
    /**
     * 主键ID
     */
    private Integer logId;

    /**
     * 操作日志父ID
     */
    private Integer parentId;

    /**
     * 定品/备货主键ID
     */
    private Integer regularId;

    /**
     * 操作类型 0：定品操作；1：备货操作
     */
    private Integer logType;

    /**
     * SKUID
     */
    private Integer skuId;

    /**
     * 订货号
     */
    private String skuNo;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 品牌
     */
    private String brandName;

    /**
     * 商品类型
     */
    private String spuType;

    /**
     * 二级分类
     */
    private String categoryName;

    /**
     * 规格
     */
    private String spec;

    /**
     * 商品单位
     */
    private String unit;

    /**
     * 成本价
     */
    private String cost;

    /**
     * 去年销售量-总
     */
    private BigDecimal amount;

    /**
     * 去年销售量-医械购
     */
    private BigDecimal yxgAmount;

    /**
     * 近三个月销售量-总
     */
    private BigDecimal threeMonthAmount;

    /**
     * 近三个月销售量-医械购
     */
    private BigDecimal yxgThreeMonthAmount;

    /**
     * 近一个月销售量-总
     */
    private BigDecimal oneMonthAmount;

    /**
     * 近一个月销售量-医械购
     */
    private BigDecimal yxgOneMonthAmount;

    /**
     * 归属人ID
     */
    private Integer owerUserId;

    /**
     * 归属人
     */
    private String owerUser;

    /**
     * 库存数量
     */
    private Integer stock;

    /**
     * 在途数量
     */
    private Integer intransitStock;

    /**
     * 订单占用数量
     */
    private Integer orderStock;

    /**
     * 预测安全库存
     */
    private Integer forecastSafeStock;

    /**
     * 安全库存
     */
    private Integer safeStock;

    /**
     * 安全库存预警
     */
    private String stockWarn;

    /**
     * 建议补充库存数量
     */
    private Integer supplementStock;

    /**
     * 平均到货时间
     */
    private BigDecimal receiveTimes;

    /**
     * 断货天数
     */
    private Integer outStockTimes;

    /**
     * 操作类型 0：删除，1、替换，2：转换，3：暂不备货, 4：备货
     */
    private Integer operateType;

    private String operateTypeStr;

    /**
     * 操作人ID
     */
    private Integer operateUserId;

    /**
     * 操作人
     */
    private String operateUserName;

    /**
     * 操作时间
     */
    private Long operateTime;

    private String operateTimeStr;

    /**
     * 操作原因
     */
    private String operateReason;

    /**
     * 备货单ID
     */
    private Integer saleorderId;

    /**
     * 最新加入定品时间
     */
    private Long lastAddTime;

    /**
     * 最新加入定品时间
     */
    private String lastAddTimeStr;

}
