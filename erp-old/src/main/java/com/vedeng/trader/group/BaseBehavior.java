package com.vedeng.trader.group;



import com.vedeng.common.service.impl.BaseServiceimpl;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import java.util.BitSet;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * <b>Description:</b>分群，标签基础行为<br>
 * <b>Author:calvin</b>
 * <br><b>Date:</b> 2020/5/25
 */
public abstract class BaseBehavior {

    private Logger logger= LoggerFactory.getLogger(BaseBehavior.class);

    protected String crmUrl;

    protected String trackUrl;

    protected String operateUrl;
    // 运行时注入Mapper
    protected WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
    private GroupConfig groupConfig=context.getBean(GroupConfig.class);
    public BaseBehavior(){
        this.crmUrl=groupConfig.crmUrl;
        this.trackUrl=groupConfig.trackUrl;
        this.operateUrl=groupConfig.operateUrl;
    }
    /**
     * 时间长度
     */
    private Integer period;


    private BitSet baseTraderSet;

    private CountDownLatch countDownLatch;

    public BitSet getValidateTrader(){
        try {
            BitSet traderIds = calculate();
            if (getCountDownLatch() != null) {
                getCountDownLatch().countDown();
            }
            System.out.println("结束计算");
            return traderIds;
        }catch (Exception ex){
            logger.error("行为计算出错：",ex);
            if (getCountDownLatch() != null) {
                getCountDownLatch().countDown();
            }
            return null;
        }
    }

    public BitSet addBitSet(BitSet set, List<Integer> traderIds){
        if(CollectionUtils.isNotEmpty(traderIds)){
            for(Integer id:traderIds){
                if(id==null){
                    continue;
                }
                set.set(id);
            }
        }
        return set;
    }
    /**
     * <b>Description:</b>计算得出符合条件的客户集合<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/5/25
     */
    public abstract BitSet calculate();

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public BitSet getBaseTraderSet() {
        return baseTraderSet;
    }

    public void setBaseTraderSet(BitSet baseTraderSet) {
        this.baseTraderSet = baseTraderSet;
    }

    public CountDownLatch getCountDownLatch() {
        return countDownLatch;
    }

    public void setCountDownLatch(CountDownLatch countDownLatch) {
        this.countDownLatch = countDownLatch;
    }
}
