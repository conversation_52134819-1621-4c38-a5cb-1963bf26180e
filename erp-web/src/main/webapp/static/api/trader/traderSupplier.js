
function getTraderSupplierInfoById(param) {
    return axios({
        url: '/traderTraderSupplier/getTraderSupplierInfo.do',
        method: 'post',
        params: param
    })
}

// 供应商的联系人
function getTraderConcatData(traderId) {
    var data = {"param": {"traderId": traderId,"traderType":2}, "orderBy": 'TRADER_CONTACT_ID desc', "pageSize": 1000};
    return axios({
        url: '/traderContact/page.do',
        method: 'post',
        data: data,
        headers: {
            'Content-Type': 'application/json'  //如果写成contentType会报错,如果不写这条也报错
        }
    })
}

// 供应商的地址
function getTraderAddressData(traderId) {
    var data =  {"traderId": traderId,"traderType":2}
    return axios({
        url: '/traderAddress/getTraderAddress.do',
        method: 'post',
        data: data,
        headers: {
            'Content-Type': 'application/json'  //如果写成contentType会报错,如果不写这条也报错
        }
    })
}