<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!DOCTYPE html>
<html>
<head>
    <title>CRM-拜访计划详情</title>
    <link rel="stylesheet" href="/static/css/common/common.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/ui/ui.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/css/pages/records.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/css/pages/visitRecord.css?staticResourceVersion=${requestScope.staticResourceVersion}">
</head>

<body>
    <input type="hidden" id="isHiddenLayout" value="0">
    <jsp:include page="../common/head_import.jsp"></jsp:include>

    <div class="page-wrap" id="page-container">
        <page-header></page-header>
        <div class="page-container">
            <div class="page-main">
                <div class="visit-detail-wrap" v-if="!pageLoading">
                    <div class="chanceDetail-header-wrap" :style="{'top': fixedTop+'px'}">
                        <div class="header-main">
                            <div class="header-content">
                                <div class="header-left">
                                    <div class="title">拜访计划详情</div>
                                    <div class="status" :class="'status'+detail.visitRecordStatus">{{ {1: '待拜访', 2: '拜访中', 3: '已拜访', 4: '已关闭'}[detail.visitRecordStatus] }}</div>
                                </div>
                                <div class="header-right">
                                    <!-- <ui-button @click="cardTest">测试打卡</ui-button> -->
                                    <ui-button v-if="detail.visitDetailButtonDto.addVisitRecordBtn" type="primary" @click="showVisitDialog">添加拜访记录</ui-button>
                                    <ui-button v-if="detail.visitDetailButtonDto.editPlanBtn" @click="editPlan">编辑计划</ui-button>
                                    <ui-button v-if="detail.visitDetailButtonDto.createNextVisitBtn" @click="addNextPlan">创建下次拜访计划</ui-button>
                                    <ui-button v-if="detail.visitDetailButtonDto.createBusinessChanceBtn" @click="addBusinessChance">创建商机/线索</ui-button>
                                    <ui-button v-if="detail.visitDetailButtonDto.addCommuncateRecordBtn" @click="showConnectDialog">添加沟通记录</ui-button>
                                    <ui-button v-if="detail.visitDetailButtonDto.closeVisitBtn" type="danger" @click="showCloseDialog">关闭</ui-button>
                                    <ui-card-switch name="visitRecord_detail" v-model="cardLineNum"></ui-card-switch>
                                </div>
                            </div>
                            <div class="header-aside-wrap">
                                <!-- 大屏幕 -->
                                <div class="header-md-aside">
                                    <ui-title-tip :title="item.name" :position="layout_hidden_value ? 'bottom' : 'top'" :y="-23" v-for="item in asideList" :key="item.name">
                                        <div 
                                            class="h-a-item" 
                                            :class="[item.icon, {'active': item.id == asideIndex}]"
                                            @click="asideIndex = item.id"
                                        ></div>
                                        <!-- <div class="btn-icon btn-message" @click="openRecord"></div> -->
                                    </ui-title-tip>
                                </div>
                                <!-- 小屏头 -->
                                <div class="header-xs-aside">
                                    <ui-title-tip :title="item.name" :y="-23" :position="layout_hidden_value ? 'bottom' : 'top'" v-for="item in asideList" :key="item.name">
                                        <div 
                                            :class="['h-a-item', item.icon]"
                                            @click="openDialog(item.id)"
                                        ></div>
                                    </ui-title-tip>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="deatil-page-top-bg" :style="{'top': fixedTop + 60 +'px'}" :class="stepStatus"></div>
                    <div class="detail-page-wrap" :class="{'switch-line-2': cardLineNum == 2}">
                        <div class="chance-step" >
                            <ui-step :list="stepList" :active="stepActive" :status="stepStatus"></ui-step>
                        </div>
                        
                        <div class="main">
                            <div class="top-warn-tip" v-if="detail.visitRecordStatus==4">
                                <i class="vd-ui_icon icon-caution2"></i>
                                <div class="tip-cnt">
                                    关闭原因：{{ CloseReasonTypes[detail.closeReasonType - 1].label }}<template v-if="detail.closeReasonContent">（{{ detail.closeReasonContent }}）</template>
                                </div>
                            </div>
                            <div class="detail-top-card">
                                <div class="detail-top-item">
                                    <div class="item-label">计划编号</div>
                                    <div class="item-txt">{{ detail.visitRecordNo }}</div>
                                </div>
                                <div class="detail-top-item">
                                    <div class="item-label">计划拜访时间</div>
                                    <div class="item-txt">{{ detail.planVisitDate }}</div>
                                </div>
                                <div class="detail-top-item">
                                    <template v-if="detail.visitRecordStatus != 4">
                                        <div class="item-label">拜访完成时间</div>
                                        <div class="item-txt">{{ detail.completeDatetime || '-' }}</div>
                                    </template>
                                    <template v-else>
                                        <div class="item-label">关闭时间</div>
                                        <div class="item-txt">{{ detail.closeDatetime || '-' }}</div>
                                    </template>
                                </div>
                                <div class="detail-top-item">
                                    <div class="item-label">拜访人</div>
                                    <div class="item-txt">
                                        <ui-user :name="detail.visitorName" :avatar="detail.visitorPic"></ui-user>
                                    </div>
                                </div>
                                <div class="detail-top-item">
                                    <div class="item-label">创建时间</div>
                                    <div class="item-txt">{{ detail.addTime || '-' }}</div>
                                </div>
                            </div>
                            <div class="card detail-card">
                                <div class="card-title">拜访客户信息</div>
                                <div class="info-wrap">
                                    <div class="info-item">
                                        <div class="label">客户名称：</div>
                                        <div class="content">
                                            <ui-trader-name :info="{
                                                traderName: detail.customerName,
                                                traderNameLink: detail.visitCustomerVo && detail.visitCustomerVo.traderNameLink,
                                                traderNameInnerLink: detail.visitCustomerVo && detail.visitCustomerVo.traderNameInnerLink,
                                                tycFlag: detail.visitCustomerVo && detail.visitCustomerVo.tycFlag,
                                                baidu: true,
                                                traderId: detail.traderId,
                                                needValid: true
                                            }"></ui-trader-name>
                                            <div class="trader-detail" v-if="detail.visitCustomerVo">
                                                <div class="detail-item">总交易额：{{ detail.visitCustomerVo.historyTransactionAmount || '-' }}</div>
                                                <div class="detail-item">交易次数：{{ detail.visitCustomerVo.historyTransactionNum || '-' }}</div>
                                                <div class="detail-item">最近下单：{{ detail.visitCustomerVo.lastOrderTime || '-' }}</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">客户等级：</div>
                                        <div class="content">
                                            <template v-if="detail.visitCustomerVo && detail.visitCustomerVo.customerGrade">
                                                <ui-trader-grade :grade="detail.visitCustomerVo.customerGrade"></ui-trader-grade>
                                            </template>
                                            <template v-else>-</template>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">客户类型：</div>
                                        <div class="content">
                                           {{ {465: '渠道商', 466: '终端'}[detail.customerNature] }}
                                        </div>
                                    </div>
                                    <div class="info-item" v-if="detail.contactName">
                                        <div class="label">联系人：</div>
                                        <div class="content">{{ detail.contactName }}</div>
                                    </div>
                                    <div class="info-item" v-if="detail.contactPosition">
                                        <div class="label">职位：</div>
                                        <div class="content">{{ detail.contactPosition }}</div>
                                    </div>
                                    <div class="info-item" v-if="detail.contactMobile">
                                        <div class="label">手机：</div>
                                        <div class="content">
                                            <ui-call 
                                                :tel="detail.contactMobile" 
                                                :call-type="11" 
                                                :trader-id="detail.traderId" 
                                                :order-id="id"
                                                :need-status="true"
                                                :status="contactMobileStatus"
                                            >
                                            </ui-call>
                                        </div>
                                    </div>
                                    <div class="info-item" v-if="detail.contactTele">
                                        <div class="label">固话：</div>
                                        <div class="content">
                                            <ui-call 
                                                :tel="detail.contactTele" 
                                                :call-type="11" 
                                                :trader-id="detail.traderId" 
                                                :order-id="id"
                                            >
                                            </ui-call>
                                        </div>
                                    </div>
                                    <div class="info-item" v-if="detail.otherContact">
                                        <div class="label">其他联系方式：</div>
                                        <div class="content" v-html="detail.otherContact.replace(/##/g, '<div class=\'other-contant-gap\'></div>')"></div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">拜访地区：</div>
                                        <div class="content">{{ [detail.provinceName, detail.cityName, detail.areaName].join('-') }}</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">详细地址：</div>
                                        <div class="content">
                                            <div class="map-address-wrap">
                                                <div class="address-txt">{{ detail.visitAddress || '-' }}</div>
                                                <ui-view-map :address="[detail.provinceName, detail.cityName, detail.areaName].join('') + detail.visitAddress" v-if="detail.visitAddress"></ui-view-map>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="card detail-card">
                                <div class="card-title">计划信息</div>
                                <div class="info-wrap">
                                    <div class="info-item info-avatar">
                                        <div class="label">拜访人：</div>
                                        <div class="content">
                                            <div class="user-label-wrap">
                                                <ui-user :name="detail.visitorName" :avatar="detail.visitorPic"></ui-user>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="info-item info-avatar">
                                        <div class="label">同行拜访人：</div>
                                        <div class="content">
                                            <div v-if="detail.tongxingUserList.length" class="user-label-list">
                                                <template v-for="(item, index) in detail.tongxingUserList">
                                                    <div class="user-label-wrap">
                                                        <ui-user :name="item.userName" :avatar="item.aliasHeadPicture"></ui-user>
                                                    </div>
                                                </template>
                                            </div>
                                            <template v-else>-</template>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">计划拜访时间：</div>
                                        <div class="content">{{ detail.planVisitDate }}</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">拜访目标：</div>
                                        <div class="content">{{ detail.visitTargetStr }}</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">备注：</div>
                                        <div class="content">
                                            <div class="ui-col-10">{{ detail.remark || '-' }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="card detail-card line-1" v-if="relatedOrderDtoList.length">
                                <div class="card-title">相关单据</div>
                                <div class="card-top-tip" v-if="relatedOrderDtoList[0].traderName !== detail.customerName">
                                    <i class="vd-ui_icon icon-caution1"></i>
                                    {{relatedOrderDtoList[0].type==1 ? '线索' : '商机'}}客户与当前拜访客户不一致
                                </div>
                                <div class="info-wrap no-padding">
                                    <ui-table 
                                        border
                                        :oneline="true"
                                        :width-border="true" 
                                        :auto-scroll="false" 
                                        :headers="relatedOrderHeaders" 
                                        :list="relatedOrderDtoList"
                                    >
                                        <template v-slot:bizNo="{ row }">
                                            <div class="td-link" v-if="row.type==1" @click="GLOBAL.link({name:'查看线索', url: '/crm/businessLeads/profile/detail?id=' + row.bizId})">{{row.bizNo}}</div>
                                            <div class="td-link" v-else @click="GLOBAL.link({name:'查看商机', url: '/crm/businessChance/profile/detail?id=' + row.bizId})">{{row.bizNo}}</div>
                                        </template>
                                        <template v-slot:traderName="{ row }">
                                            <ui-trader-name :info="{
                                                traderName: row.traderName,
                                                traderNameLink: row.traderNameLink,
                                                traderNameInnerLink: row.traderNameInnerLink,
                                                tycFlag: row.tycFlag,
                                                nameFlex: true,
                                                traderId: row.traderId,
                                                needValid: true
                                            }"></ui-trader-name>
                                        </template>
                                    </ui-table>
                                </div>
                            </div>

                            <div class="card detail-card line-1">
                                <div class="card-title">打卡信息</div>
                                <div class="card-top-tip">
                                    <i class="vd-ui_icon icon-caution1"></i>
                                    打卡仅支持在企微CRM中操作
                                </div>
                                <div class="info-wrap">
                                    <template v-if="detail.cardList && detail.cardList.length">
                                        <div class="info-item info-table">
                                            <div class="label">打卡信息：</div>
                                            <div class="content">
                                                <div class="ui-col-10">
                                                    <ui-table 
                                                        border
                                                        :oneline="true"
                                                        :width-border="true" 
                                                        :auto-scroll="false" 
                                                        :headers="clockInHeader" 
                                                        :list="detail.cardList"
                                                    >
                                                        <template v-slot:cardLocation="{ row }">
                                                            <template v-if="row.id">
                                                                <ui-location-map :location="row.cardLocation"></ui-location-map>
                                                            </template>
                                                            <template v-else>未打卡</template>
                                                        </template>
                                                    </ui-table>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="info-item">
                                            <div class="label">拍照图片：</div>
                                            <div class="content">
                                                <template v-if="cardPicList && cardPicList.length">
                                                    <ui-img-preview-list :img-list="cardPicList"></ui-img-preview-list>
                                                </template>
                                                <template v-else>-</template>
                                                <div class="pic-list-tip">图片由{{ detail.visitorName }}上传</div>
                                            </div>
                                        </div>
                                    </template>
                                    <div class="info-empty" v-else>
                                        <i class="info-empty-icon vd-ui_icon icon-info1"></i>
                                        <div class="info-empty-txt">拜访人未打卡</div>
                                    </div>
                                </div>
                            </div>
                            <div class="card detail-card line-1">
                                <div class="card-title">拜访记录</div>
                                <div class="card-top-option">
                                    <div class="option-item" v-if="detail.visitDetailButtonDto.addVisitRecordBtn" @click="showVisitDialog">
                                        <i class="vd-ui_icon icon-add"></i>添加拜访记录
                                    </div>
                                    <div class="option-item" v-if="detail.visitDetailButtonDto.addCommuncateRecordBtn" @click="showConnectDialog">
                                        <i class="vd-ui_icon icon-add"></i>添加沟通记录
                                    </div>
                                </div>
                                <div class="info-wrap">
                                    <template v-if="detail.communicateList && detail.communicateList.length">
                                        <div class="info-item">
                                            <div class="label">联系人：</div>
                                            <div class="content">{{ detail.recordContactName || '-' }}</div>
                                        </div>
                                        <div class="info-item">
                                            <div class="label">手机：</div>
                                            <div class="content">
                                                <ui-call 
                                                    :tel="detail.recordContactMobile" 
                                                    :call-type="11" 
                                                    :trader-id="detail.traderId" 
                                                    :order-id="id"
                                                    :need-status="true"
                                                    :status="recordContactMobileStatus"
                                                >
                                                </ui-call>
                                            </div>
                                        </div>
                                        <div class="info-item" v-if="detail.recordContactTele">
                                            <div class="label">固话：</div>
                                            <div class="content">
                                                <ui-call 
                                                    :tel="detail.recordContactTele" 
                                                    :call-type="11" 
                                                    :trader-id="detail.traderId" 
                                                    :order-id="id"
                                                >
                                                </ui-call>
                                            </div>
                                        </div>
                                        <div class="info-item" v-if="detail.recordOtherContact">
                                            <div class="label">其他联系方式：</div>
                                            <div class="content" v-html="detail.recordOtherContact.replace(/##/g, '<div class=\'other-contant-gap\'></div>')"></div>
                                        </div>
                                        <div class="info-item">
                                            <div class="label">职位：</div>
                                            <div class="content">{{ detail.recordContactPosition || '-' }}</div>
                                        </div>
                                        <div class="info-item">
                                            <div class="label">讲解PPT：</div>
                                            <div class="content">{{ detail.showPpt ? {Y: '已讲解', N: '未讲解'}[detail.showPpt] : '-' }}</div>
                                        </div>
                                        <div class="info-item info-table">
                                            <div class="label">沟通记录：</div>
                                            <div class="content">
                                                <div class="ui-col-10">
                                                    <ui-table 
                                                        border
                                                        :oneline="true"
                                                        :width-border="true" 
                                                        :auto-scroll="false" 
                                                        :headers="feedbackHeader" 
                                                        :list="detail.communicateList"
                                                    >
                                                        <template v-slot:contentSuffix="{ row }">
                                                           {{ row.contentSuffix }}
                                                        </template>
                                                    </ui-table>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                    <div class="info-empty" v-else>
                                        <i class="info-empty-icon vd-ui_icon icon-info1"></i>
                                        <div class="info-empty-txt">未添加拜访记录</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="right-aside hidden-xs" :style="'top:' + (layout_hidden_value ? '0' : '50px')">
                            <div class="right-aside-inner">
                                <!-- 操作记录面板 -->
                                <operation-log 
                                    ref="operationLog" 
                                    v-if="asideIndex == 1"
                                    url="/crm/visitrecord/profile/logs"
                                    :related-id="id"
                                ></operation-log>
                            </div>
                        </div>
                    </div>
                </div>
                
                <ui-dialog
                    :visible.sync="isShowVisitDialog"
                    title="添加拜访记录"
                    width="960px"
                >
                    <div class="form-wrap visit-form-wrap label-width-5" v-if="isShowVisitDialog">
                        <ui-form-item label="客户名称" :text="true">
                            <div class="visit-form-trader">
                                <div class="trader-name">
                                    <ui-trader-name :info="{
                                        traderName: detail.customerName,
                                        traderNameLink: detail.visitCustomerVo && detail.visitCustomerVo.traderNameLink,
                                        traderNameInnerLink: detail.visitCustomerVo && detail.visitCustomerVo.traderNameInnerLink,
                                        tycFlag: detail.visitCustomerVo && detail.visitCustomerVo.tycFlag,
                                        traderId: detail.traderId,
                                        needValid: true
                                    }"></ui-trader-name>
                                </div>
                                <div class="trader-detail" v-if="detail.visitCustomerVo">
                                    <div class="detail-item">总交易额：{{ detail.visitCustomerVo.historyTransactionAmount || '-' }}</div>
                                    <div class="detail-item">交易次数：{{ detail.visitCustomerVo.historyTransactionNum || '-' }}</div>
                                    <div class="detail-item">最近下单：{{ detail.visitCustomerVo.lastOrderTime || '-' }}</div>
                                </div>
                            </div>
                        </ui-form-item>
                        <ui-form-item label="客户等级" :text="true">
                            <template v-if="detail.visitCustomerVo && detail.visitCustomerVo.customerGrade">
                                <ui-trader-grade :grade="detail.visitCustomerVo.customerGrade"></ui-trader-grade>
                            </template>
                            <template v-else>-</template>
                        </ui-form-item>
                        <ui-form-item label="客户类型" :text="true">{{ {465: '渠道商', 466: '终端'}[detail.customerNature] }}</ui-form-item>
                        <template v-if="!isNoVisitContactInfo">
                            <ui-form-item label="手机">
                                <div class="ui-col-4">
                                    <ui-phone-related
                                        placeholder="已建档客户，可输入手机号搜索"
                                        v-model="visitFormMobile"
                                        :trader-id="detail.traderId"
                                        :accurate-match="false"
                                        valid="visitForm_visitFormMobile" 
                                        @change="handlerPhone"
                                        :novalid="true"
                                    ></ui-phone-related>
                                    <div class="form-status-tip tip-warn" v-if="recordContactMobileStatus == 2">
                                        <i class="vd-ui_icon icon-caution1"></i>
                                        <div class="tip-txt">该手机号未注册贝登商城</div>
                                    </div>
                                    <div class="form-status-tip tip-success" v-if="recordContactMobileStatus == 1">
                                        <i class="vd-ui_icon icon-yes1"></i>
                                        <div class="tip-txt">该手机号已注册贝登商城</div>
                                    </div>
                                </div>
                            </ui-form-item>
                            <ui-form-item label="固话">
                                <div class="ui-col-4">
                                    <ui-input 
                                        type="number" 
                                        placeholder="需加上区号，可支持20位数字"
                                        v-model="visitFormTelephone"
                                        maxlength="20"
                                        @blur="validPhone"
                                    ></ui-input>
                                </div>
                            </ui-form-item>
                            <ui-more-contact
                                label="其他联系方式"
                                width="323px"
                                placeholder="请输入联系方式名称"
                                v-model="visitFormOtherContact"
                                @change="validPhone"
                            ></ui-more-contact>
                            <ui-form-item label="联系人" :must="true">
                                <div class="ui-col-4">
                                    <ui-input 
                                        placeholder="最多支持20个字"
                                        v-model="visitFormContact"
                                        maxlength="20"
                                        valid="visitForm_visitFormContact" 
                                        @change="handlerContact"
                                    ></ui-input>
                                </div>
                            </ui-form-item>
                            <ui-form-item label="职位" :must="true">
                                <div class="ui-col-4">
                                    <ui-select
                                        placeholder="请选择"
                                        :data="positionList"
                                        v-model="visitFormDepart"
                                        valid="visitForm_visitFormDepart" 
                                        clearable
                                    ></ui-select>
                                </div>
                            </ui-form-item>
                        </template>
                        <ui-form-item label="">
                            <ui-checkbox label="未获得联系人信息" :checked="isNoVisitContactInfo" @change="toggleVisitContactInfo"></ui-checkbox>
                        </ui-form-item>
                        <ui-form-item label="讲解PPT" :text="true">
                            <ui-radio-group
                                :list="[{label: '是', value: 'Y'}, {label: '否', value: 'N'}]"
                                :value.sync="visitFormPPT"
                            ></ui-radio-group>
                        </ui-form-item>
                        <ui-form-item label="沟通记录" :must="true">
                            <ui-input
                                type="textarea"
                                width="590px"
                                height="30px"
                                show-word-limit
                                placeholder="可详细总结下拜访的结果或客户的沟通记录，最多不超过1000字"
                                maxlength="1000"
                                v-model="visitFormConnect"
                                valid="visitForm_visitFormConnect" 
                                height-auto
                            ></ui-input>
                        </ui-form-item>
                    </div>
                    <template slot="footer">
                        <div class="dlg-form-footer long-form-footer">
                            <ui-button @click="addVisitRecord" type="primary">提交</ui-button>
                            <ui-button @click="isShowVisitDialog=false" class="close">取消</ui-button>
                        </div>
                    </template>
                </ui-dialog>

                <ui-dialog
                    :visible.sync="isShowConnectDialog"
                    title="添加沟通记录"
                    width="960px"
                >
                    <div class="form-wrap label-width-2" v-if="isShowConnectDialog">
                        <ui-form-item label="沟通记录" :must="true">
                            <ui-input
                                type="textarea"
                                width="590px"
                                height="30px"
                                show-word-limit
                                placeholder="请补充拜访中的内容，最多不超过1000字"
                                maxlength="1000"
                                v-model="connectValue"
                                height-auto
                                valid="connectForm_connectValue"
                            ></ui-input>
                        </ui-form-item>
                    </div>
                    <template slot="footer">
                        <div class="dlg-form-footer">
                            <ui-button @click="addConnectRecord" type="primary">提交</ui-button>
                            <ui-button @click="isShowConnectDialog=false" class="close">取消</ui-button>
                        </div>
                    </template>
                </ui-dialog>

                <ui-dialog
                    :visible.sync="isShowCloseDialog"
                    title="关闭"
                    width="720px"
                >
                    <div class="form-wrap label-width-2" v-if="isShowCloseDialog">
                        <ui-form-item label="关闭原因" :must="true">
                            <div class="ui-col-4">
                                <ui-select
                                    width="323px"
                                    placeholder="请选择关闭原因"
                                    :data="CloseReasonTypes"
                                    v-model="closeReasonType"
                                    clearable
                                    valid="closeForm_closeReasonType"
                                ></ui-select>
                            </div>
                        </ui-form-item>

                        <ui-form-item label="说明">
                            <div class="ui-col-4">
                                <ui-input
                                    type="textarea"
                                    width="440px"
                                    height="30px"
                                    show-word-limit
                                    maxlength="200"
                                    height-auto
                                    v-model="closeReason"
                                ></ui-input>
                            </div>
                        </ui-form-item>
                    </div>
                    <template slot="footer">
                        <div class="dlg-form-footer">
                            <ui-button @click="submitClose" type="primary">提交</ui-button>
                            <ui-button @click="isShowCloseDialog=false" class="close">取消</ui-button>
                        </div>
                    </template>
                </ui-dialog>

                <!-- 操作记录 dialog -->
                <operation-log-dialog ref="operationLogDialog"></operation-log-dialog>
            </div>
        </div>
    </div>

    <script src="/static/js/common/vue.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/axios.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/lodash.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/moment.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/ui/ui.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/layout.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <!-- 操作记录 -->
    <script src="/static/js/common/components/business/operationLog.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/components/business/operationLog-dialog.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <!-- 高德地图经纬度查看位置 -->
    <script src="/static/js/common/map.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <!-- 线索详情 -->
    <script src="/static/js/pages/visitRecord/detail.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
</body>
</html>
