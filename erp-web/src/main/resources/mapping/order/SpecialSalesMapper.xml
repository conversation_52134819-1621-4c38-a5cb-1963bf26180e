<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.SpecialSalesMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.SpecialSalesEntity">
    <!--@mbg.generated-->
    <!--@Table T_SPECIAL_SALES-->
    <id column="SPECIAL_SALES_ID" jdbcType="INTEGER" property="specialSalesId" />
    <result column="RELATE_ID" jdbcType="INTEGER" property="relateId" />
    <result column="RELATE_NO" jdbcType="VARCHAR" property="relateNo" />
    <result column="RELATE_TYPE" jdbcType="INTEGER" property="relateType" />
    <result column="IS_DELETE" jdbcType="TINYINT" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    SPECIAL_SALES_ID, RELATE_ID, RELATE_NO, RELATE_TYPE, IS_DELETE, ADD_TIME, CREATOR, 
    MOD_TIME, UPDATER, CREATOR_NAME, UPDATER_NAME, UPDATE_REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_SPECIAL_SALES
    where SPECIAL_SALES_ID = #{specialSalesId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_SPECIAL_SALES
    where SPECIAL_SALES_ID = #{specialSalesId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="SPECIAL_SALES_ID" keyProperty="specialSalesId" parameterType="com.vedeng.erp.trader.domain.entity.SpecialSalesEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SPECIAL_SALES (RELATE_ID, RELATE_NO, RELATE_TYPE, 
      IS_DELETE, ADD_TIME, CREATOR, 
      MOD_TIME, UPDATER, CREATOR_NAME, 
      UPDATER_NAME, UPDATE_REMARK)
    values (#{relateId,jdbcType=INTEGER}, #{relateNo,jdbcType=VARCHAR}, #{relateType,jdbcType=INTEGER}, 
      #{isDelete,jdbcType=TINYINT}, #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{modTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, 
      #{updaterName,jdbcType=VARCHAR}, #{updateRemark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="SPECIAL_SALES_ID" keyProperty="specialSalesId" parameterType="com.vedeng.erp.trader.domain.entity.SpecialSalesEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SPECIAL_SALES
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="relateId != null">
        RELATE_ID,
      </if>
      <if test="relateNo != null and relateNo != ''">
        RELATE_NO,
      </if>
      <if test="relateType != null">
        RELATE_TYPE,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="updateRemark != null and updateRemark != ''">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="relateId != null">
        #{relateId,jdbcType=INTEGER},
      </if>
      <if test="relateNo != null and relateNo != ''">
        #{relateNo,jdbcType=VARCHAR},
      </if>
      <if test="relateType != null">
        #{relateType,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null and updateRemark != ''">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.SpecialSalesEntity">
    <!--@mbg.generated-->
    update T_SPECIAL_SALES
    <set>
      <if test="relateId != null">
        RELATE_ID = #{relateId,jdbcType=INTEGER},
      </if>
      <if test="relateNo != null and relateNo != ''">
        RELATE_NO = #{relateNo,jdbcType=VARCHAR},
      </if>
      <if test="relateType != null">
        RELATE_TYPE = #{relateType,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null and updateRemark != ''">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where SPECIAL_SALES_ID = #{specialSalesId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.SpecialSalesEntity">
    <!--@mbg.generated-->
    update T_SPECIAL_SALES
    set RELATE_ID = #{relateId,jdbcType=INTEGER},
      RELATE_NO = #{relateNo,jdbcType=VARCHAR},
      RELATE_TYPE = #{relateType,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=TINYINT},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where SPECIAL_SALES_ID = #{specialSalesId,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_SPECIAL_SALES
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="RELATE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPECIAL_SALES_ID = #{item.specialSalesId,jdbcType=INTEGER} then #{item.relateId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="RELATE_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPECIAL_SALES_ID = #{item.specialSalesId,jdbcType=INTEGER} then #{item.relateNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="RELATE_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPECIAL_SALES_ID = #{item.specialSalesId,jdbcType=INTEGER} then #{item.relateType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPECIAL_SALES_ID = #{item.specialSalesId,jdbcType=INTEGER} then #{item.isDelete,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPECIAL_SALES_ID = #{item.specialSalesId,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPECIAL_SALES_ID = #{item.specialSalesId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPECIAL_SALES_ID = #{item.specialSalesId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPECIAL_SALES_ID = #{item.specialSalesId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPECIAL_SALES_ID = #{item.specialSalesId,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPECIAL_SALES_ID = #{item.specialSalesId,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATE_REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPECIAL_SALES_ID = #{item.specialSalesId,jdbcType=INTEGER} then #{item.updateRemark,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where SPECIAL_SALES_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.specialSalesId,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_SPECIAL_SALES
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="RELATE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.relateId != null">
            when SPECIAL_SALES_ID = #{item.specialSalesId,jdbcType=INTEGER} then #{item.relateId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="RELATE_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.relateNo != null">
            when SPECIAL_SALES_ID = #{item.specialSalesId,jdbcType=INTEGER} then #{item.relateNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="RELATE_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.relateType != null">
            when SPECIAL_SALES_ID = #{item.specialSalesId,jdbcType=INTEGER} then #{item.relateType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDelete != null">
            when SPECIAL_SALES_ID = #{item.specialSalesId,jdbcType=INTEGER} then #{item.isDelete,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when SPECIAL_SALES_ID = #{item.specialSalesId,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when SPECIAL_SALES_ID = #{item.specialSalesId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when SPECIAL_SALES_ID = #{item.specialSalesId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when SPECIAL_SALES_ID = #{item.specialSalesId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when SPECIAL_SALES_ID = #{item.specialSalesId,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when SPECIAL_SALES_ID = #{item.specialSalesId,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATE_REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateRemark != null">
            when SPECIAL_SALES_ID = #{item.specialSalesId,jdbcType=INTEGER} then #{item.updateRemark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where SPECIAL_SALES_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.specialSalesId,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="SPECIAL_SALES_ID" keyProperty="specialSalesId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SPECIAL_SALES
    (RELATE_ID, RELATE_NO, RELATE_TYPE, IS_DELETE, ADD_TIME, CREATOR, MOD_TIME, UPDATER, 
      CREATOR_NAME, UPDATER_NAME, UPDATE_REMARK)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.relateId,jdbcType=INTEGER}, #{item.relateNo,jdbcType=VARCHAR}, #{item.relateType,jdbcType=INTEGER}, 
        #{item.isDelete,jdbcType=TINYINT}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, 
        #{item.modTime,jdbcType=TIMESTAMP}, #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, 
        #{item.updaterName,jdbcType=VARCHAR}, #{item.updateRemark,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="SPECIAL_SALES_ID" keyProperty="specialSalesId" parameterType="com.vedeng.erp.trader.domain.entity.SpecialSalesEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SPECIAL_SALES
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="specialSalesId != null">
        SPECIAL_SALES_ID,
      </if>
      RELATE_ID,
      RELATE_NO,
      RELATE_TYPE,
      IS_DELETE,
      ADD_TIME,
      CREATOR,
      MOD_TIME,
      UPDATER,
      CREATOR_NAME,
      UPDATER_NAME,
      UPDATE_REMARK,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="specialSalesId != null">
        #{specialSalesId,jdbcType=INTEGER},
      </if>
      #{relateId,jdbcType=INTEGER},
      #{relateNo,jdbcType=VARCHAR},
      #{relateType,jdbcType=INTEGER},
      #{isDelete,jdbcType=TINYINT},
      #{addTime,jdbcType=TIMESTAMP},
      #{creator,jdbcType=INTEGER},
      #{modTime,jdbcType=TIMESTAMP},
      #{updater,jdbcType=INTEGER},
      #{creatorName,jdbcType=VARCHAR},
      #{updaterName,jdbcType=VARCHAR},
      #{updateRemark,jdbcType=VARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="specialSalesId != null">
        SPECIAL_SALES_ID = #{specialSalesId,jdbcType=INTEGER},
      </if>
      RELATE_ID = #{relateId,jdbcType=INTEGER},
      RELATE_NO = #{relateNo,jdbcType=VARCHAR},
      RELATE_TYPE = #{relateType,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=TINYINT},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="SPECIAL_SALES_ID" keyProperty="specialSalesId" parameterType="com.vedeng.erp.trader.domain.entity.SpecialSalesEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SPECIAL_SALES
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="specialSalesId != null">
        SPECIAL_SALES_ID,
      </if>
      <if test="relateId != null">
        RELATE_ID,
      </if>
      <if test="relateNo != null and relateNo != ''">
        RELATE_NO,
      </if>
      <if test="relateType != null">
        RELATE_TYPE,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="updateRemark != null and updateRemark != ''">
        UPDATE_REMARK,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="specialSalesId != null">
        #{specialSalesId,jdbcType=INTEGER},
      </if>
      <if test="relateId != null">
        #{relateId,jdbcType=INTEGER},
      </if>
      <if test="relateNo != null and relateNo != ''">
        #{relateNo,jdbcType=VARCHAR},
      </if>
      <if test="relateType != null">
        #{relateType,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null and updateRemark != ''">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="specialSalesId != null">
        SPECIAL_SALES_ID = #{specialSalesId,jdbcType=INTEGER},
      </if>
      <if test="relateId != null">
        RELATE_ID = #{relateId,jdbcType=INTEGER},
      </if>
      <if test="relateNo != null and relateNo != ''">
        RELATE_NO = #{relateNo,jdbcType=VARCHAR},
      </if>
      <if test="relateType != null">
        RELATE_TYPE = #{relateType,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null and updateRemark != ''">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

<!--auto generated by MybatisCodeHelper on 2023-08-14-->
  <select id="findByRelateIdInAndRelateTypeAndIsDelete" resultType="com.vedeng.erp.trader.dto.SpecialSalesDto">
    select
    <include refid="Base_Column_List"/>
    from T_SPECIAL_SALES
    where RELATE_ID in
    <foreach item="item" index="index" collection="relateIdCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=INTEGER}
    </foreach>
    and RELATE_TYPE=#{relateType,jdbcType=INTEGER} and IS_DELETE=#{isDelete,jdbcType=TINYINT}
  </select>

  <select id="selectByRelatedIdAndType" resultMap="BaseResultMap">
    select *
    from T_SPECIAL_SALES
    where RELATE_ID = #{relateId,jdbcType=INTEGER}
      and RELATE_TYPE = #{type,jdbcType=INTEGER}
      and IS_DELETE = 0
    </select>
</mapper>