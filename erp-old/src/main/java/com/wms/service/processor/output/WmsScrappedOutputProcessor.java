package com.wms.service.processor.output;

import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.common.util.DateUtil;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.constant.WmsLogicalOperateTypeEnum;
import com.wms.dao.WmsOutputOrderGoodsMapper;
import com.wms.dao.WmsOutputOrderMapper;
import com.wms.dto.OutputDto;
import com.wms.dto.OutputGoodDto;
import com.wms.dto.StockCalculateDto;
import com.wms.model.dto.WmsOutputOrderGoodsDto;
import com.wms.model.po.WmsOutputOrder;
import com.wms.model.po.WmsOutputOrderGoods;
import com.wms.service.WMSScrappedOutService;
import com.wms.service.util.WmsCommonUtil;
import com.wms.service.context.ThreadLocalContext;
import com.wms.service.processor.AbstractOutputOrderProcessor;
import com.wms.service.stockcalculate.SaleorderOutCaculateImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName WmsScrappedOutputProcessor.java
 * @Description TODO
 * @createTime 2020年11月25日 19:30:00
 */
@Service
public class WmsScrappedOutputProcessor extends AbstractOutputOrderProcessor {

    Logger logger= LoggerFactory.getLogger(WmsScrappedOutputProcessor.class);
    @Autowired
    @Qualifier("saleorderOutCaculateImpl")
    private SaleorderOutCaculateImpl stockinfoCaculateInterface;

    @Autowired
    private WMSScrappedOutService wmsScrappedOutService;

    @Autowired
    private WmsOutputOrderMapper outputOrderMapper;

    @Autowired
    private WmsOutputOrderGoodsMapper outputOrderGoodsMapper;


    @Override
    protected void commonValidator(OutputDto requestBean) throws Exception {
        if(!WmsInterfaceOrderType.OUT_RECEIVER.equals(requestBean.getOrderType()) &&
                !WmsInterfaceOrderType.OUT_SCRAPPED.equals(requestBean.getOrderType())){
            throw new Exception("报废出库单:"+requestBean.getSOReference1()+"的类型错误!");
        }
    }

    @Override
    protected void updateOrderData(OutputDto requestBean) throws Exception {
        String scrappedNo = WmsCommonUtil.getOriginalOrderNo(requestBean.getSOReference1());
        WmsOutputOrder wmsOutputOrder = wmsScrappedOutService.getScrappedOrderByNo(scrappedNo);

        Map<Integer, WmsOutputOrderGoods> wmsOutputOrderGoodsMap = new HashMap<>();
        List<WmsOutputOrderGoodsDto> wmsOutputOrderGoodsList = wmsScrappedOutService.queryOutputGoodsByScrappedOutId(wmsOutputOrder.getId());
        for (WmsOutputOrderGoods wmsOutputOrderGood : wmsOutputOrderGoodsList) {
            wmsOutputOrderGoodsMap.put(Integer.valueOf(wmsOutputOrderGood.getId().intValue()),wmsOutputOrderGood);
        }
        List<OutputGoodDto> details = requestBean.getDetails();
        String currentTime = DateUtil.convertString(System.currentTimeMillis(),DateUtil.TIME_FORMAT);
        Map<Integer,Integer> outNumMap = new HashMap<>();
        for (OutputGoodDto detail : details) {
            int detailId = getRelateId(detail);
            Integer outNum = outNumMap.get(detailId) == null ? 0 : outNumMap.get(detailId);
            outNumMap.put(detailId,outNum + detail.getQtyShipped().intValue());
        }

        for (Integer detailId : outNumMap.keySet()) {
            WmsOutputOrderGoods wmsOutputOrderGoods = wmsOutputOrderGoodsMap.get(detailId);
            Integer outNum = outNumMap.get(detailId);
            WmsOutputOrderGoods update = new WmsOutputOrderGoods();
            update.setId(wmsOutputOrderGoods.getId());
            update.setAlreadyOutputNum(wmsOutputOrderGoods.getAlreadyOutputNum() + outNum);
            update.setLastOutputTime(currentTime);
            update.setOutStatus(wmsOutputOrderGoods.getOutputNum() <= update.getAlreadyOutputNum() ? 2 : 1);
            update.setUpdateTime(currentTime);
            outputOrderGoodsMapper.updateByPrimaryKeySelective(update);
        }
        List<WmsOutputOrderGoodsDto> wmsOutputOrderGoodsList1 = wmsScrappedOutService.queryOutputGoodsByScrappedOutId(wmsOutputOrder.getId());
        WmsOutputOrder updateOrder = new WmsOutputOrder();
        updateOrder.setOutStatus(2);
        updateOrder.setId(wmsOutputOrder.getId());
        updateOrder.setRealOutputTime(currentTime);
        updateOrder.setUpdateTime(currentTime);
        for (WmsOutputOrderGoods wmsOutputOrderGoods : wmsOutputOrderGoodsList1) {
            if(!wmsOutputOrderGoods.getOutStatus().equals(2)){
                updateOrder.setOutStatus(1);
                break;
            }
        }
        outputOrderMapper.updateByPrimaryKeySelective(updateOrder);
    }

    @Override
    protected int getOperateType(OutputDto requestBean) {
        if(WmsInterfaceOrderType.OUT_RECEIVER.equals(requestBean.getOrderType())){
            return StockOperateTypeConst.SCRAPED_WAREHOUSE_OUT_L;
        }else{
            return StockOperateTypeConst.SCRAPED_WAREHOUSE_OUT;
        }
    }

    @Override
    protected int getWmsLogicalOperateType() {
        return WmsLogicalOperateTypeEnum.SCRAPPED_OUT.getOperateTypeCode();
    }

    @Override
    protected int getRelateId(OutputGoodDto outputGoodDto) {
        return Integer.valueOf(outputGoodDto.getUserDefine1());
    }

    @Override
    protected List<WarehouseDto> getWarehouseStockList(List<StockCalculateDto> stockCalculateList) {
        return stockinfoCaculateInterface.calculateStockInfo(stockCalculateList);
    }
}
