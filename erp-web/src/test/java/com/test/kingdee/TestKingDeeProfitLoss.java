package com.test.kingdee;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.domain.command.KingDeeProfitLossCommand;
import com.vedeng.erp.kingdee.dto.KingDeeProfitLossDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeProfitLossDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeProfitLossMapper;
import com.vedeng.erp.kingdee.mapstruct.KingDeeProfitLossCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeProfitLossConvertor;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.RepoStatus;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 盘亏单
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring.xml", "classpath:spring-mybatis.xml"})
public class TestKingDeeProfitLoss {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Resource
    private KingDeeProfitLossCommandConvertor commandConvertor;

    @Resource
    private KingDeeProfitLossConvertor profitLossConvertor;

    @Autowired
    private KingDeeProfitLossMapper kingDeeProfitLossMapper;


    @Test
    public void kingDeeProfitLoss() {
    	KingDeeProfitLossDto dto = new KingDeeProfitLossDto();
        dto.setFId("0");
        dto.setFBillTypeId("PK01_SYS");
        dto.setFBillNo("PYD0002");
        dto.setFStockOrgId("101");
        dto.setFQzokBddjtId("贝登erp对应的单据头ID");
        dto.setFOwnerTypeIdHead("BD_OwnerOrg");
        dto.setFDate("2022-09-17 00:00:00");
        dto.setFDeptId("BM9999");

        List<KingDeeProfitLossDetailDto>  FEntityDetail = new ArrayList<>();
        KingDeeProfitLossDetailDto detailDto = new KingDeeProfitLossDetailDto();
        detailDto.setFMaterialId("sku0001");
        detailDto.setFStockId("ck9999");
        detailDto.setFStockStatusId("KCZT01_SYS");
        detailDto.setFBaseLossQty("2.0");
        detailDto.setFQzokYsddh("原始订单号");
        detailDto.setFQzokGsywdh("归属业务单号");
        detailDto.setFQzokYwlx("业务类型");
        detailDto.setFQzokPch("批次号");
        detailDto.setFQzokXlh("序列号");
        detailDto.setFQzokSqlx("授权类型");
        detailDto.setFQzokSfzf("是否直发");
        FEntityDetail.add(detailDto);
        dto.setFBillEntry(FEntityDetail);

        KingDeeProfitLossCommand command = commandConvertor.toCommand(dto);


        System.out.println("入参："+JSON.toJSONString(command));
        RepoStatus stk_stockCountLoss = kingDeeBaseApi.save(new SaveExtCommand<>(command, "STK_StockCountLoss"));
        ArrayList<SuccessEntity> successEntities = stk_stockCountLoss.getSuccessEntitys();
//        System.out.println("金蝶返回："+JSON.toJSONString(successEntities));
        if (CollUtil.isNotEmpty(successEntities)) {
            SuccessEntity successEntity = CollUtil.getFirst(successEntities);
            System.out.println("数据库入参："+JSON.toJSONString(profitLossConvertor.toEntity(dto)));
            kingDeeProfitLossMapper.insertSelective(profitLossConvertor.toEntity(dto));
        }
    }
}
