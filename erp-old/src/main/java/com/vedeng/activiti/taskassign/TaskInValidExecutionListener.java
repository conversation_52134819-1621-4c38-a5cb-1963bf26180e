package com.vedeng.activiti.taskassign;

import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.system.service.VerifiesRecordService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

/**
 * <AUTHOR>
 * @date created in 2020/8/31 20:05
 */
public class TaskInValidExecutionListener implements ExecutionListener {

    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
    private ActionProcdefService actionProcdefService = (ActionProcdefService) context.getBean("actionProcdefService");

    private VerifiesRecordService verifiesRecordService = (VerifiesRecordService)context.getBean("verifiesRecordService");

    @Override
    public void notify(DelegateExecution execution) throws Exception {
        if(execution.getVariable("tableName") != null){
            String tableName = (String) execution.getVariable("tableName");
            String id = (String) execution.getVariable("id");
            Integer idValue = (Integer) execution.getVariable("idValue");
            String key = (String) execution.getVariable("key1");
            Integer value = (Integer) execution.getVariable("value1");
            Integer db = (Integer) execution.getVariable("db");
            actionProcdefService.updateInfo(tableName, id, idValue, key, value,db);
        }
        //添加审核对应主表的审核状态
        if(execution.getVariable("verifyStatus") != null){
            verifiesRecordService.saveVerifiesInfo(execution.getId(),(Integer) execution.getVariable("verifyStatus"));
        }else{
            verifiesRecordService.saveVerifiesInfo(execution.getId(),1);
        }
    }
}
