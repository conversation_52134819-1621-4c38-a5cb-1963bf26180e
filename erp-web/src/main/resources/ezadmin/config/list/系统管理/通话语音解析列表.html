<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>通话语音解析列表</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="commucationVoice" datasource="erp-datasourcetarget" empty_show="" fixednumber="0" firstcol="numbers">
<div id="appendHead">

    <style>
        .rowButtons{
            min-width: 160px;
        }
        li{
            position: relative;
            float: left;
            padding: 10px 0;
            display: list-item;
        }
        li>a{    color: #333;
            border-right: 1px solid #ddd;
            padding: 0 10px;}
        ul{    overflow: hidden;
            list-style: none outside none;}
        /*.layui-elip{*/
        /*    white-space: normal !important;*/
        /*}*/
        .ezcall{
            color: #01AAED;
            font-size: 16px !important;
            cursor: pointer;
        }
        .utext i{
            font-size: 16px !important;
        }

    </style>

</div>

<div class="layui-fluid">
    <div class="layui-tab">
        <ul class="layui-tab-title" id="tab">
        </ul>
    </div>
    <form class="layui-form" id="search">
        <div class=" layui-inline ">
            <label class="layui-form-label">录音ID</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="COMMUNICATE_RECORD_ID" style="" alias="A" jdbctype="NUMBER" data="" datatype="" oper="=">
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">场景名称</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="SENCE_CODE" alias="A.SENCE_CODE" placeholder="全部" jdbctype=""
                        data='[ {"K":"SENCE_BUSINESS_FOLLOW","V":"商机跟进"},  {"K":"SENCE_PRODUCT_PROMOT","V":"产品推广"},  {"K":"SENCE_CUSTOMER_DEVELOP","V":"客户开发"},  {"K":"SENCE_QUOTAT_RESP","V":"报价响应"},  {"K":"SENCE_BUSINESS_PROCESS","V":"商务处理"},  {"K":"SENCE_AFTER_SALE","V":"售后"},  {"K":"SENCE_OTHER","V":"其他"}]'
                        datatype="json"></select>
            </div>
        </div>

        <div class=" layui-inline "><label class="layui-form-label">转写状态</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="" name="VOICE_STATUS" placeholder="" style="" alias="" jdbctype="" data="[
                {&quot;V&quot;:&quot;忽略&quot;,&quot;K&quot;:&quot;-1&quot;},
                {&quot;V&quot;:&quot;初始&quot;,&quot;K&quot;:&quot;0&quot;},
                {&quot;V&quot;:&quot;上传完成（转写中）&quot;,&quot;K&quot;:&quot;1&quot;},
                {&quot;V&quot;:&quot;上传失败（需重新上传）&quot;,&quot;K&quot;:&quot;2&quot;},
                {&quot;V&quot;:&quot;讯飞转写失败&quot;,&quot;K&quot;:&quot;3&quot;},
                {&quot;V&quot;:&quot;讯飞转写成功&quot;,&quot;K&quot;:&quot;4&quot;},
                {&quot;V&quot;:&quot;提取可读文本失败&quot;,&quot;K&quot;:&quot;5&quot;},
                {&quot;V&quot;:&quot;提取可读文本成功&quot;,&quot;K&quot;:&quot;6&quot;},
                {&quot;V&quot;:&quot;GPT获取标签中&quot;,&quot;K&quot;:&quot;7&quot;},
                {&quot;V&quot;:&quot;GPT获取标签失败&quot;,&quot;K&quot;:&quot;8&quot;},
                {&quot;V&quot;:&quot;GPT获取标签成功&quot;,&quot;K&quot;:&quot;9&quot;},
                {&quot;V&quot;:&quot;查询转写结果报错&quot;,&quot;K&quot;:&quot;10&quot;}
                ]" datatype="JSON" oper=""></select>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">创建时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" name="ADD_TIME" placeholder style=""
                        alias="A" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>
    </form>
    <hr class="layui-border-blue">

    <table id="table" class="layui-table" style=" width:100%">
        <thead>

        <tr id="column">
            <th type="rowbutton" id="rowbutton">
                <button  type="single" url="${pageContext.request.contextPath}/system/call/getrecordplayForAi.do?communicateRecordId=${COMMUNICATE_RECORD_ID}" area="840px,540px"
                         windowname="贝壳助理语音识别，录音ID：${COMMUNICATE_RECORD_ID}"  >语音播放及转写
                </button>
            </th>
            <th name="VOICE_STATUS">转写状态</th>
            <th name="GPT_VERSION">GPT版本</th>
            <th name="COMMUNICATE_RECORD_ID" style="width:80px;max-width:80px;min-width: 80px;" >录音ID</th>
            <th name="SENCE_NAME" style="width:80px;max-width:120px;min-width: 80px;"  >场景</th>
            <th name="COID_LENGTH" style="width:80px;max-width:80px;min-width: 80px;"  >录音时长</th>
            <th name="PHONE" style="width:80px;max-width:120px;min-width: 80px;"  >电话号码</th>
            <th name="ADD_TIME" style="width:140px;max-width:140px;min-width: 140px;"  >创建时间</th>
            <th name="COID_URI"   >录音地址</th>
            <th name="CUSTOMER_NATURE">客户类型（主数据）</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td colspan="100">
                express:
                <pre id="express" class="layui-code" orderby="ORDER BY A.MOD_TIME DESC" groupby="">
       <![CDATA[         select
    A.ID,
    A.COMMUNICATE_RECORD_ID,
    A.COID_LENGTH,
    A.COID_URI,
    A.SENCE_CODE,
    CASE A.SENCE_CODE
    WHEN 'SENCE_BUSINESS_FOLLOW' THEN '商机跟进'
    WHEN 'SENCE_PRODUCT_PROMOT' THEN '产品推广'
    WHEN 'SENCE_CUSTOMER_DEVELOP' THEN '客户开发'
    WHEN 'SENCE_QUOTAT_RESP' THEN '报价响应'
    WHEN 'SENCE_BUSINESS_PROCESS' THEN '商务处理'
    WHEN 'SENCE_AFTER_SALE' THEN '售后'
    WHEN 'SENCE_OTHER' THEN '其他'
    ELSE '未知'
END AS SENCE_NAME,
    A.GPT_VERSION,
    CASE
      WHEN VOICE_STATUS = '-1' THEN '忽略'
      WHEN VOICE_STATUS = '0' THEN '初始'
      WHEN VOICE_STATUS = '1' THEN '上传完成（转写中）'
      WHEN VOICE_STATUS = '2' THEN '上传失败（需重新上传）'
      WHEN VOICE_STATUS = '3' THEN '讯飞转写失败'
      WHEN VOICE_STATUS = '4' THEN '讯飞转写成功'
      WHEN VOICE_STATUS = '5' THEN '提取可读文本失败'
      WHEN VOICE_STATUS = '6' THEN '提取可读文本成功'
      WHEN VOICE_STATUS = '7' THEN 'GPT获取标签中'
      WHEN VOICE_STATUS = '8' THEN 'GPT获取标签失败'
      WHEN VOICE_STATUS = '9' THEN 'GPT获取标签成功'
      WHEN VOICE_STATUS = '10' THEN '查询转写结果报错'
      ELSE '未知状态'
      END AS VOICE_STATUS,
    A.VOICE_STATUS_REQUEST_URI,
    A.UPLOAD_TIMESTAMP,
    A.VOICE_TIMESTAMP,
    A.GPT_TIMESTAMP,
    A.ADD_TIME,
    A.MOD_TIME,
    A.REMARK,
    A.VOICE_TEXT_ORDER,
    A.VOICE_TEXT_PARSE,
    A.ORDER_ID,
    B.CUSTOMER_INTENTIONS,
   B.INTENTION_GOODS,
        B.BRANDS,
        B.MODELS,
        B.CUSTOMER_TYPES,
                    case
                    TR.CUSTOMER_NATURE when 465 then '经销商'
                    WHEN 466 then '终端'
                    else TR.CUSTOMER_NATURE
                    end as CUSTOMER_NATURE,
        B.IS_INTENTION,
        B.IS_ADD_WECHAT,
        B.IS_EFFECTIVE_COMMUNICATION,
        C.PHONE
  from T_COMMUNICATE_VOICE_TASK A
  LEFT JOIN T_COMMUNICATE_SUMMARY B ON A.COMMUNICATE_RECORD_ID = B.COMMUNICATE_RECORD_ID
  LEFT JOIN T_COMMUNICATE_RECORD C ON A.COMMUNICATE_RECORD_ID = C.COMMUNICATE_RECORD_ID
                    LEFT JOIN T_TRADER_CUSTOMER TR ON C.TRADER_ID=TR.TRADER_ID
  where 1=1
                    ]]>
 </pre>
            </td>
        </tr>
        </tbody>
    </table>
</div>
</body>
<div id="appendFoot">
    <!--<script type="text/javascript" src="${pageContext.request.contextPath}/static/libs/jquery/plugins/layer/mylayer.js"></script>-->
    <script>

        $(function(){
            $('button[name="playrecordBtn"]').on('click', function(e) {
                // 在这里添加你的 onClick 事件处理代码
                // 可以使用 event.preventDefault() 阻止按钮的默认行为
                e.preventDefault();e.stopPropagation();
                playrecord($(this).attr("item_open_title"))
                console.log($(this).attr("item_open_title"));
            });
            // $('button[name="voiceTextParse"]').on('click', function(e) {
            // 在这里添加你的 onClick 事件处理代码
            // 可以使用 event.preventDefault() 阻止按钮的默认行为
            // e.preventDefault();e.stopPropagation();
            // var content = $(this).attr("item_open_title");
            // if(content == undefined ){ //acvd
            //     content = "";
            // }
            // //var modifiedText = content.replace(/(。对话人)/g, "<br/>$1");
            // var modifiedText = content;
            // if(content.length > 0){
            //     modifiedText = content.replace(/对话人/g, "<br/>对话人");
            //     if(modifiedText.startsWith("<br/>")){
            //         modifiedText = modifiedText.substring(5,modifiedText.length);
            //     }
            // }
            // var communicateRecordId = $(this).parents("tr").find("[name='row_data_hidden_COMMUNICATE_RECORD_ID']").val();//112
            // window.top.showAiHelpme(communicateRecordId);
            // console.log(url);
            // modifiedText = "<iframe src='/system/call/getrecordpaly.do?url="+encodeURIComponent(url)+"' width='500px' height='80px' style='border: 0;' ></iframe><div style='max-height: 270px;overflow: auto;background-color: aliceblue;padding-left: 11px;padding-top: 0px;'>"+modifiedText+"</div>";
            // layer.open({
            //     title: '录音播放及转写结果查看',
            //     area: ['600px','500px'],
            //     content: modifiedText
            // });
            // console.log(modifiedText);
            // });
        })
        function  playrecord(url) {
            //url = this.coidUri;
            url = encodeURIComponent(url);
            if(url != ''){
                layer.open({
                    type: 2,
                    shadeClose: false, //点击遮罩关闭
                    area: ['360px','80px'],
                    title: false,
                    content: ['/system/call/getrecordpaly.do?url='+url],
                    success: function(layero, index) {
                        //layer.iframeAuto(index);
                    },
                    error:function(data){
                        if(data.status ==1001){
                            layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                        }
                    }
                });
            }
        }
    </script>
</div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
</html>