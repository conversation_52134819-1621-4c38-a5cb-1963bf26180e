package com.vedeng.mobile.system.service.Impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.core.base.Page;
import com.vedeng.erp.saleorder.constant.TerminalCustomerEnum;
import com.vedeng.erp.saleorder.dto.OrderTerminalDto;
import com.vedeng.erp.saleorder.enums.OrderTerminalConstant;
import com.vedeng.erp.saleorder.feign.OneDataTerminalFeignApi;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.infrastructure.tyc.domain.dto.TycResultDto;
import com.vedeng.infrastructure.tyc.service.TycSearchService;
import com.vedeng.mobile.system.dto.TraderCustomerInputDto;
import com.vedeng.mobile.system.dto.TraderCustomerSearchApiDto;
import com.vedeng.mobile.system.dto.TraderCustomerSearchResultDto;
import com.vedeng.mobile.system.mapper.TraderCustomerForMobileMapper;
import com.vedeng.mobile.system.mapper.WebAccountMapper;
import com.vedeng.mobile.system.model.WebAccount;
import com.vedeng.mobile.system.service.TraderCustomerSearchService;
import com.vedeng.onedataapi.api.terminal.req.TerminalReqDto;
import com.vedeng.onedataapi.api.terminal.res.TerminalDataRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2023/12/6
 */
@Service
@Slf4j
public class TraderCustomerSearchServiceImpl implements TraderCustomerSearchService {

    @Autowired
    private WebAccountMapper webAccountMapper;

    @Autowired
    @Qualifier("traderCustomerForMobileMapper")
    private TraderCustomerForMobileMapper traderCustomerForMobileMapper;

    @Autowired
    private OneDataTerminalFeignApi oneDataTerminalFeignApi;

    @Autowired
    private TycSearchService tycSearchService;


    @Autowired
    private UserApiService userService;

    @Override
    public List<TraderCustomerSearchResultDto> searchTraderCustomerByName(String customerName){
        return traderCustomerForMobileMapper.searchTraderCustomerByName(customerName);
    }

    @Override
    public PageInfo<TraderCustomerSearchResultDto> searchTraderCustomerListPage(TraderCustomerSearchApiDto traderCustomerVo, Page page) {

        PageHelper.startPage(page.getPageNo(), page.getPageSize());
        List<TraderCustomerSearchResultDto> searchCustomerList = traderCustomerForMobileMapper.searchTraderCustomerListPage(traderCustomerVo,page.getPageNo(),page.getPageSize());
        return new PageInfo<>(searchCustomerList);
    }

    @Override
    public PageInfo<TraderCustomerSearchResultDto> searchTraderCustomerListPageForYingji(TraderCustomerSearchApiDto traderCustomerVo, Page page){
        PageHelper.startPage(page.getPageNo(), page.getPageSize());
        List<TraderCustomerSearchResultDto> searchCustomerList = traderCustomerForMobileMapper.searchTraderCustomerListPage(traderCustomerVo,page.getPageNo(),page.getPageSize());
        return new PageInfo<>(searchCustomerList);
    }


    @Override
    public PageInfo<TraderCustomerSearchResultDto> searchTraderCustomerListPageForFeigong(TraderCustomerSearchApiDto traderCustomerVo, Page page){
        PageHelper.startPage(page.getPageNo(), page.getPageSize());
        List<TraderCustomerSearchResultDto> searchCustomerList = traderCustomerForMobileMapper.searchTraderCustomerListPageForYingji(traderCustomerVo,page.getPageNo(),page.getPageSize());
        return new PageInfo<>(searchCustomerList);
    }

    /**
     * 见设计文档：
     * http://wiki.ivedeng.com/pages/viewpage.action?pageId=232882513#ERP_SV_2024_17%E7%89%88%E6%9C%AC%E6%8B%9C%E8%AE%BF%E8%AE%A1%E5%88%92%E4%BC%98%E5%8C%96%E9%9C%80%E6%B1%82-%E7%B3%BB%E7%BB%9F%E8%AE%BE%E8%AE%A1-6%E4%B8%AA%E5%A4%A7%E5%8C%BA%E5%8F%AF%E8%A7%81%E8%8C%83%E5%9B%B4%E5%88%86%E6%9E%90%EF%BC%9A
     * @param inputDto
     * @return
     */
    @Override
    public boolean checkTraderCanSelected(TraderCustomerInputDto inputDto){
        List<Integer> userIds = userService.getAllSubordinateByUserIdForVisit(inputDto.getUserId());
        if(inputDto.getTraderId() !=null && inputDto.getTraderId()>0){//如果是erp的客户，先根据下属的关系判断是否为归属人
            //获取该客户的归属人和被分享给哪些人了
            //场景1，归属销售是本人及下属（含线上和线下销售，不对人进行区分）：
            //场景2，分享给本地及下属（含线上和线下销售，不对人进行区分）：
            List<Integer> belongUserIds = traderCustomerForMobileMapper.selectTraderCustomerId(inputDto.getTraderId());
            if(CollectionUtils.isNotEmpty(belongUserIds)){
                boolean containsAny = false;
                for (Integer belongUserId : belongUserIds) {
                    if (userIds.contains(belongUserId)) {
                        containsAny = true;
                        break;
                    }
                }
                if (containsAny) {
                    return true;
                }
            }
        }

        Integer areaCode = inputDto.getAreaCode();
        if(areaCode !=null && areaCode >0 && CollectionUtils.isNotEmpty(userIds)){//场景3本人及下属作为线下销售，在其所负责的区域内均可见（仅线下销售）：
            List<Integer> list = traderCustomerForMobileMapper.selectSaleDownArea(userIds,areaCode);
            if(CollectionUtils.isNotEmpty(list)){
                return true;
            }
        }

        if(inputDto.getTraderId() !=null && inputDto.getTraderId()>0 && CollectionUtils.isNotEmpty(userIds)) {//场景4，线上销售，在本人所负责的区域，但归属销售不是自己，但是A2的场景（仅线上销售场景）：
            List<Integer> list = traderCustomerForMobileMapper.selectSaleDownBelongA2(userIds,areaCode,inputDto.getTraderId());
            if(CollectionUtils.isNotEmpty(list)){
                return true;
            }

        }
        return false;
    }





    @Override
    public PageInfo<TraderCustomerSearchResultDto> getOneDataTerminalInfo(String terminalName, Integer pageSize, Integer pageNum) {
        PageInfo<TraderCustomerSearchResultDto> pageResult = new PageInfo<>();
        pageResult.setPageSize(pageSize);
        pageResult.setPageNum(pageNum);
        TerminalReqDto query = new TerminalReqDto();
        query.setPageSize(pageSize);
        query.setPageNo(pageNum);
        query.setSearchName(terminalName);
        try {
            RestfulResult<TerminalDataRes> searchDetail = oneDataTerminalFeignApi.getTerminalSearchDetail(query);
            TerminalDataRes terminalDataRes = Optional.ofNullable(searchDetail.getData()).orElse(new TerminalDataRes());
            if (CollectionUtils.isNotEmpty(terminalDataRes.getList())) {
                List<TraderCustomerSearchResultDto> result = terminalDataRes.getList().stream().map(item ->  TraderCustomerSearchResultDto.builder()
                        .traderCustomerId(null)
                        .traderId(null)
                        .provinceName(item.getProvince())
                        .cityName(item.getCity())
                        .areaName(item.getArea())
                        .areaId(item.getAreaId())
                        .cityId(item.getCityId())
                        .provinceId(item.getProvinceId())
                        .traderName(item.getHosName())
                        .userId(null).build()).collect(Collectors.toList());
                pageResult.setTotal(terminalDataRes.getTotalRecord());
                pageResult.setList(result);
                return pageResult;
            }
        } catch (Exception e) {
            log.error("查询终端库接口异常，terminalName：{},", terminalName, e);
        }
        return pageResult;
    }

    @Override
    public PageInfo<TraderCustomerSearchResultDto> getTycTerminalInfo(String terminalName, Integer pageSize, Integer pageNum) {
        PageInfo<TraderCustomerSearchResultDto> pageResult = new PageInfo<>();
        PageInfo<TycResultDto> pageInfo = tycSearchService.searchByTerminalName(terminalName);
        List<TycResultDto> tycResultDtoList = Optional.ofNullable(pageInfo.getList()).orElse(new ArrayList<>());
        List<TraderCustomerSearchResultDto> orderTerminalDtoList = tycResultDtoList.stream().map(item -> TraderCustomerSearchResultDto.builder()
                .traderCustomerId(null)
                .traderId(null)
                .traderName(item.getName())
                .userId(null).build()).collect(Collectors.toList());
        pageResult.setTotal(pageInfo.getTotal());
        pageResult.setList(orderTerminalDtoList);
        pageResult.setPageSize(pageSize);
        pageResult.setPageNum(pageNum);
        return pageResult;
    }

    @Override
    public List<WebAccount> queryWebAccountByTraderId(Integer traderId) {
        return webAccountMapper.selectByTraderId(traderId);
    }


    @Override
    public Integer getTraderWorkAreaByCustomerName(String customerName){

        return null;
    }





}

