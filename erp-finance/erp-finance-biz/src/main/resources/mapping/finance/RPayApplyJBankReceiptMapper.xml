<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.finance.mapper.RPayApplyJBankReceiptMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.finance.domain.entity.RPayApplyJBankReceiptEntity">
    <!--@mbg.generated-->
    <!--@Table T_R_PAY_APPLY_J_BANK_RECEIPT-->
    <id column="R_PAY_APPLY_J_BANK_RECEIPT_ID" jdbcType="BIGINT" property="rPayApplyJBankReceiptId" />
    <result column="BANK_RECEIPT_ALIAS_ID" jdbcType="BIGINT" property="bankReceiptAliasId" />
    <result column="BANK_ID" jdbcType="INTEGER" property="bankId" />
    <result column="PAY_APPLY_ID" jdbcType="INTEGER" property="payApplyId" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    R_PAY_APPLY_J_BANK_RECEIPT_ID, BANK_RECEIPT_ALIAS_ID, BANK_ID, PAY_APPLY_ID, IS_DELETE,
    ADD_TIME, MOD_TIME, CREATOR, CREATOR_NAME, UPDATER, UPDATER_NAME, UPDATE_REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_R_PAY_APPLY_J_BANK_RECEIPT
    where R_PAY_APPLY_J_BANK_RECEIPT_ID = #{rPayApplyJBankReceiptId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_R_PAY_APPLY_J_BANK_RECEIPT
    where R_PAY_APPLY_J_BANK_RECEIPT_ID = #{rPayApplyJBankReceiptId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="R_PAY_APPLY_J_BANK_RECEIPT_ID" keyProperty="rPayApplyJBankReceiptId" parameterType="com.vedeng.erp.finance.domain.entity.RPayApplyJBankReceiptEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_PAY_APPLY_J_BANK_RECEIPT (BANK_RECEIPT_ALIAS_ID, BANK_ID, PAY_APPLY_ID,
      IS_DELETE, ADD_TIME, MOD_TIME,
      CREATOR, CREATOR_NAME, UPDATER,
      UPDATER_NAME, UPDATE_REMARK)
    values (#{bankReceiptAliasId,jdbcType=BIGINT}, #{bankId,jdbcType=INTEGER}, #{payApplyId,jdbcType=INTEGER},
      #{isDelete,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP},
      #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updater,jdbcType=INTEGER},
      #{updaterName,jdbcType=VARCHAR}, #{updateRemark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="R_PAY_APPLY_J_BANK_RECEIPT_ID" keyProperty="rPayApplyJBankReceiptId" parameterType="com.vedeng.erp.finance.domain.entity.RPayApplyJBankReceiptEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_PAY_APPLY_J_BANK_RECEIPT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bankReceiptAliasId != null">
        BANK_RECEIPT_ALIAS_ID,
      </if>
      <if test="bankId != null">
        BANK_ID,
      </if>
      <if test="payApplyId != null">
        PAY_APPLY_ID,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bankReceiptAliasId != null">
        #{bankReceiptAliasId,jdbcType=BIGINT},
      </if>
      <if test="bankId != null">
        #{bankId,jdbcType=INTEGER},
      </if>
      <if test="payApplyId != null">
        #{payApplyId,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.finance.domain.entity.RPayApplyJBankReceiptEntity">
    <!--@mbg.generated-->
    update T_R_PAY_APPLY_J_BANK_RECEIPT
    <set>
      <if test="bankReceiptAliasId != null">
        BANK_RECEIPT_ALIAS_ID = #{bankReceiptAliasId,jdbcType=BIGINT},
      </if>
      <if test="bankId != null">
        BANK_ID = #{bankId,jdbcType=INTEGER},
      </if>
      <if test="payApplyId != null">
        PAY_APPLY_ID = #{payApplyId,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where R_PAY_APPLY_J_BANK_RECEIPT_ID = #{rPayApplyJBankReceiptId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.finance.domain.entity.RPayApplyJBankReceiptEntity">
    <!--@mbg.generated-->
    update T_R_PAY_APPLY_J_BANK_RECEIPT
    set BANK_RECEIPT_ALIAS_ID = #{bankReceiptAliasId,jdbcType=BIGINT},
      BANK_ID = #{bankId,jdbcType=INTEGER},
      PAY_APPLY_ID = #{payApplyId,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where R_PAY_APPLY_J_BANK_RECEIPT_ID = #{rPayApplyJBankReceiptId,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_R_PAY_APPLY_J_BANK_RECEIPT
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="BANK_RECEIPT_ALIAS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when R_PAY_APPLY_J_BANK_RECEIPT_ID = #{item.rPayApplyJBankReceiptId,jdbcType=BIGINT} then #{item.bankReceiptAliasId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="BANK_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when R_PAY_APPLY_J_BANK_RECEIPT_ID = #{item.rPayApplyJBankReceiptId,jdbcType=BIGINT} then #{item.bankId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="PAY_APPLY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when R_PAY_APPLY_J_BANK_RECEIPT_ID = #{item.rPayApplyJBankReceiptId,jdbcType=BIGINT} then #{item.payApplyId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when R_PAY_APPLY_J_BANK_RECEIPT_ID = #{item.rPayApplyJBankReceiptId,jdbcType=BIGINT} then #{item.isDelete,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when R_PAY_APPLY_J_BANK_RECEIPT_ID = #{item.rPayApplyJBankReceiptId,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when R_PAY_APPLY_J_BANK_RECEIPT_ID = #{item.rPayApplyJBankReceiptId,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when R_PAY_APPLY_J_BANK_RECEIPT_ID = #{item.rPayApplyJBankReceiptId,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when R_PAY_APPLY_J_BANK_RECEIPT_ID = #{item.rPayApplyJBankReceiptId,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when R_PAY_APPLY_J_BANK_RECEIPT_ID = #{item.rPayApplyJBankReceiptId,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when R_PAY_APPLY_J_BANK_RECEIPT_ID = #{item.rPayApplyJBankReceiptId,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATE_REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when R_PAY_APPLY_J_BANK_RECEIPT_ID = #{item.rPayApplyJBankReceiptId,jdbcType=BIGINT} then #{item.updateRemark,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where R_PAY_APPLY_J_BANK_RECEIPT_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.rPayApplyJBankReceiptId,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_R_PAY_APPLY_J_BANK_RECEIPT
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="BANK_RECEIPT_ALIAS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.bankReceiptAliasId != null">
            when R_PAY_APPLY_J_BANK_RECEIPT_ID = #{item.rPayApplyJBankReceiptId,jdbcType=BIGINT} then #{item.bankReceiptAliasId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="BANK_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.bankId != null">
            when R_PAY_APPLY_J_BANK_RECEIPT_ID = #{item.rPayApplyJBankReceiptId,jdbcType=BIGINT} then #{item.bankId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="PAY_APPLY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.payApplyId != null">
            when R_PAY_APPLY_J_BANK_RECEIPT_ID = #{item.rPayApplyJBankReceiptId,jdbcType=BIGINT} then #{item.payApplyId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDelete != null">
            when R_PAY_APPLY_J_BANK_RECEIPT_ID = #{item.rPayApplyJBankReceiptId,jdbcType=BIGINT} then #{item.isDelete,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when R_PAY_APPLY_J_BANK_RECEIPT_ID = #{item.rPayApplyJBankReceiptId,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when R_PAY_APPLY_J_BANK_RECEIPT_ID = #{item.rPayApplyJBankReceiptId,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when R_PAY_APPLY_J_BANK_RECEIPT_ID = #{item.rPayApplyJBankReceiptId,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when R_PAY_APPLY_J_BANK_RECEIPT_ID = #{item.rPayApplyJBankReceiptId,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when R_PAY_APPLY_J_BANK_RECEIPT_ID = #{item.rPayApplyJBankReceiptId,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when R_PAY_APPLY_J_BANK_RECEIPT_ID = #{item.rPayApplyJBankReceiptId,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATE_REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateRemark != null">
            when R_PAY_APPLY_J_BANK_RECEIPT_ID = #{item.rPayApplyJBankReceiptId,jdbcType=BIGINT} then #{item.updateRemark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where R_PAY_APPLY_J_BANK_RECEIPT_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.rPayApplyJBankReceiptId,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="R_PAY_APPLY_J_BANK_RECEIPT_ID" keyProperty="rPayApplyJBankReceiptId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_PAY_APPLY_J_BANK_RECEIPT
    (BANK_RECEIPT_ALIAS_ID, BANK_ID, PAY_APPLY_ID, IS_DELETE, ADD_TIME, MOD_TIME, CREATOR,
      CREATOR_NAME, UPDATER, UPDATER_NAME, UPDATE_REMARK)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.bankReceiptAliasId,jdbcType=BIGINT}, #{item.bankId,jdbcType=INTEGER}, #{item.payApplyId,jdbcType=INTEGER},
        #{item.isDelete,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP},
        #{item.creator,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, #{item.updater,jdbcType=INTEGER},
        #{item.updaterName,jdbcType=VARCHAR}, #{item.updateRemark,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="findByPayApplyId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_R_PAY_APPLY_J_BANK_RECEIPT
    where PAY_APPLY_ID = #{payApplyId,jdbcType=INTEGER} and IS_DELETE = 0
  </select>
</mapper>
