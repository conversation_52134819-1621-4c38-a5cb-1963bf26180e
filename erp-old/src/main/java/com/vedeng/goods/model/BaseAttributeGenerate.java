package com.vedeng.goods.model;

import java.util.Date;

public class BaseAttributeGenerate {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_BASE_ATTRIBUTE.BASE_ATTRIBUTE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer baseAttributeId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_BASE_ATTRIBUTE.BASE_ATTRIBUTE_NAME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String baseAttributeName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_BASE_ATTRIBUTE.IS_UNIT
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer isUnit;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_BASE_ATTRIBUTE.IS_DELETED
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer isDeleted;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_BASE_ATTRIBUTE.CREATOR
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_BASE_ATTRIBUTE.UPDATER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer updater;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_BASE_ATTRIBUTE.ADD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Date addTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_BASE_ATTRIBUTE.MOD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Date modTime;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_BASE_ATTRIBUTE.BASE_ATTRIBUTE_ID
     *
     * @return the value of V_BASE_ATTRIBUTE.BASE_ATTRIBUTE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getBaseAttributeId() {
        return baseAttributeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_BASE_ATTRIBUTE.BASE_ATTRIBUTE_ID
     *
     * @param baseAttributeId the value for V_BASE_ATTRIBUTE.BASE_ATTRIBUTE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setBaseAttributeId(Integer baseAttributeId) {
        this.baseAttributeId = baseAttributeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_BASE_ATTRIBUTE.BASE_ATTRIBUTE_NAME
     *
     * @return the value of V_BASE_ATTRIBUTE.BASE_ATTRIBUTE_NAME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getBaseAttributeName() {
        return baseAttributeName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_BASE_ATTRIBUTE.BASE_ATTRIBUTE_NAME
     *
     * @param baseAttributeName the value for V_BASE_ATTRIBUTE.BASE_ATTRIBUTE_NAME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setBaseAttributeName(String baseAttributeName) {
        this.baseAttributeName = baseAttributeName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_BASE_ATTRIBUTE.IS_UNIT
     *
     * @return the value of V_BASE_ATTRIBUTE.IS_UNIT
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getIsUnit() {
        return isUnit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_BASE_ATTRIBUTE.IS_UNIT
     *
     * @param isUnit the value for V_BASE_ATTRIBUTE.IS_UNIT
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setIsUnit(Integer isUnit) {
        this.isUnit = isUnit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_BASE_ATTRIBUTE.IS_DELETED
     *
     * @return the value of V_BASE_ATTRIBUTE.IS_DELETED
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_BASE_ATTRIBUTE.IS_DELETED
     *
     * @param isDeleted the value for V_BASE_ATTRIBUTE.IS_DELETED
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_BASE_ATTRIBUTE.CREATOR
     *
     * @return the value of V_BASE_ATTRIBUTE.CREATOR
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_BASE_ATTRIBUTE.CREATOR
     *
     * @param creator the value for V_BASE_ATTRIBUTE.CREATOR
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_BASE_ATTRIBUTE.UPDATER
     *
     * @return the value of V_BASE_ATTRIBUTE.UPDATER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getUpdater() {
        return updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_BASE_ATTRIBUTE.UPDATER
     *
     * @param updater the value for V_BASE_ATTRIBUTE.UPDATER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_BASE_ATTRIBUTE.ADD_TIME
     *
     * @return the value of V_BASE_ATTRIBUTE.ADD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Date getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_BASE_ATTRIBUTE.ADD_TIME
     *
     * @param addTime the value for V_BASE_ATTRIBUTE.ADD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_BASE_ATTRIBUTE.MOD_TIME
     *
     * @return the value of V_BASE_ATTRIBUTE.MOD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Date getModTime() {
        return modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_BASE_ATTRIBUTE.MOD_TIME
     *
     * @param modTime the value for V_BASE_ATTRIBUTE.MOD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setModTime(Date modTime) {
        this.modTime = modTime;
    }
}