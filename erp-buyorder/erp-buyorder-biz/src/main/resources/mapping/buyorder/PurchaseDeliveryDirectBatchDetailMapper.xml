<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.buyorder.mapper.PurchaseDeliveryDirectBatchDetailMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.buyorder.domain.entity.PurchaseDeliveryDirectBatchDetail">
    <id column="PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL_ID" jdbcType="INTEGER" property="purchaseDeliveryDirectBatchDetailId" />
    <result column="PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID" jdbcType="INTEGER" property="purchaseDeliveryDirectBatchInfoId" />
    <result column="EXPRESS_DETAIL_ID" jdbcType="INTEGER" property="expressDetailId" />
    <result column="LOGISTICS_NO" jdbcType="VARCHAR" property="logisticsNo" />
    <result column="SKU" jdbcType="VARCHAR" property="sku" />
    <result column="SKU_NAME" jdbcType="VARCHAR" property="skuName" />
    <result column="MODEL" jdbcType="VARCHAR" property="model" />
    <result column="UNIT" jdbcType="VARCHAR" property="unit" />
    <result column="PRODUCT_COMPANY" jdbcType="VARCHAR" property="productCompany" />
    <result column="PRODUCTION_LICENCE" jdbcType="VARCHAR" property="productionLicence" />
    <result column="REGISTER_NUMBER" jdbcType="VARCHAR" property="registerNumber" />
    <result column="BATCH_NUMBER" jdbcType="VARCHAR" property="batchNumber" />
    <result column="UNION_SEQUENCE" jdbcType="INTEGER" property="unionSequence" />
    <result column="ARRIVAL_COUNT" jdbcType="INTEGER" property="arrivalCount" />
    <result column="WMS_HANDLED_ARRIVAL_COUNT" jdbcType="INTEGER" property="wmsHandledArrivalCount" />
    <result column="WMS_HANDLED_DELIVERY_COUNT" jdbcType="INTEGER" property="wmsHandledDeliveryCount" />
    <result column="MANUFACTURE_DATE_TIME" jdbcType="TIMESTAMP" property="manufactureDateTime" />
    <result column="INVALID_DATE_TIME" jdbcType="TIMESTAMP" property="invalidDateTime" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL_ID, PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID, 
    EXPRESS_DETAIL_ID, SKU, SKU_NAME, MODEL, UNIT, PRODUCT_COMPANY, PRODUCTION_LICENCE, 
    REGISTER_NUMBER, BATCH_NUMBER, UNION_SEQUENCE, ARRIVAL_COUNT, WMS_HANDLED_ARRIVAL_COUNT, 
    WMS_HANDLED_DELIVERY_COUNT, MANUFACTURE_DATE_TIME, INVALID_DATE_TIME, ADD_TIME, CREATOR, 
    MOD_TIME, UPDATER
  </sql>
  <select id="selectByExample" parameterType="com.vedeng.erp.buyorder.domain.entity.TPurchaseDeliveryDirectBatchDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from T_PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL
    where PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL_ID = #{purchaseDeliveryDirectBatchDetailId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL
    where PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL_ID = #{purchaseDeliveryDirectBatchDetailId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.erp.buyorder.domain.entity.TPurchaseDeliveryDirectBatchDetailExample">
    delete from T_PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL_ID" keyProperty="purchaseDeliveryDirectBatchDetailId" parameterType="com.vedeng.erp.buyorder.domain.entity.PurchaseDeliveryDirectBatchDetail" useGeneratedKeys="true">
    insert into T_PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL (PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID, EXPRESS_DETAIL_ID, 
      SKU, SKU_NAME, MODEL, 
      UNIT, PRODUCT_COMPANY, PRODUCTION_LICENCE, 
      REGISTER_NUMBER, BATCH_NUMBER, UNION_SEQUENCE, 
      ARRIVAL_COUNT, WMS_HANDLED_ARRIVAL_COUNT, WMS_HANDLED_DELIVERY_COUNT, 
      MANUFACTURE_DATE_TIME, INVALID_DATE_TIME, 
      ADD_TIME, CREATOR, MOD_TIME, 
      UPDATER)
    values (#{purchaseDeliveryDirectBatchInfoId,jdbcType=INTEGER}, #{expressDetailId,jdbcType=INTEGER}, 
      #{sku,jdbcType=VARCHAR}, #{skuName,jdbcType=VARCHAR}, #{model,jdbcType=VARCHAR}, 
      #{unit,jdbcType=VARCHAR}, #{productCompany,jdbcType=VARCHAR}, #{productionLicence,jdbcType=VARCHAR}, 
      #{registerNumber,jdbcType=VARCHAR}, #{batchNumber,jdbcType=VARCHAR}, #{unionSequence,jdbcType=INTEGER}, 
      #{arrivalCount,jdbcType=INTEGER}, #{wmsHandledArrivalCount,jdbcType=INTEGER}, #{wmsHandledDeliveryCount,jdbcType=INTEGER}, 
      #{manufactureDateTime,jdbcType=TIMESTAMP}, #{invalidDateTime,jdbcType=TIMESTAMP}, 
      #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=TIMESTAMP}, 
      #{updater,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL_ID" keyProperty="purchaseDeliveryDirectBatchDetailId" parameterType="com.vedeng.erp.buyorder.domain.entity.PurchaseDeliveryDirectBatchDetail" useGeneratedKeys="true">
    insert into T_PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="purchaseDeliveryDirectBatchInfoId != null">
        PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID,
      </if>
      <if test="expressDetailId != null">
        EXPRESS_DETAIL_ID,
      </if>
      <if test="sku != null">
        SKU,
      </if>
      <if test="skuName != null">
        SKU_NAME,
      </if>
      <if test="model != null">
        MODEL,
      </if>
      <if test="unit != null">
        UNIT,
      </if>
      <if test="productCompany != null">
        PRODUCT_COMPANY,
      </if>
      <if test="productionLicence != null">
        PRODUCTION_LICENCE,
      </if>
      <if test="registerNumber != null">
        REGISTER_NUMBER,
      </if>
      <if test="batchNumber != null">
        BATCH_NUMBER,
      </if>
      <if test="unionSequence != null">
        UNION_SEQUENCE,
      </if>
      <if test="arrivalCount != null">
        ARRIVAL_COUNT,
      </if>
      <if test="wmsHandledArrivalCount != null">
        WMS_HANDLED_ARRIVAL_COUNT,
      </if>
      <if test="wmsHandledDeliveryCount != null">
        WMS_HANDLED_DELIVERY_COUNT,
      </if>
      <if test="manufactureDateTime != null">
        MANUFACTURE_DATE_TIME,
      </if>
      <if test="invalidDateTime != null">
        INVALID_DATE_TIME,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="purchaseDeliveryDirectBatchInfoId != null">
        #{purchaseDeliveryDirectBatchInfoId,jdbcType=INTEGER},
      </if>
      <if test="expressDetailId != null">
        #{expressDetailId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null">
        #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="productCompany != null">
        #{productCompany,jdbcType=VARCHAR},
      </if>
      <if test="productionLicence != null">
        #{productionLicence,jdbcType=VARCHAR},
      </if>
      <if test="registerNumber != null">
        #{registerNumber,jdbcType=VARCHAR},
      </if>
      <if test="batchNumber != null">
        #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="unionSequence != null">
        #{unionSequence,jdbcType=INTEGER},
      </if>
      <if test="arrivalCount != null">
        #{arrivalCount,jdbcType=INTEGER},
      </if>
      <if test="wmsHandledArrivalCount != null">
        #{wmsHandledArrivalCount,jdbcType=INTEGER},
      </if>
      <if test="wmsHandledDeliveryCount != null">
        #{wmsHandledDeliveryCount,jdbcType=INTEGER},
      </if>
      <if test="manufactureDateTime != null">
        #{manufactureDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="invalidDateTime != null">
        #{invalidDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.erp.buyorder.domain.entity.TPurchaseDeliveryDirectBatchDetailExample" resultType="java.lang.Long">
    select count(*) from T_PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update T_PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL
    <set>
      <if test="record.purchaseDeliveryDirectBatchDetailId != null">
        PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL_ID = #{record.purchaseDeliveryDirectBatchDetailId,jdbcType=INTEGER},
      </if>
      <if test="record.purchaseDeliveryDirectBatchInfoId != null">
        PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID = #{record.purchaseDeliveryDirectBatchInfoId,jdbcType=INTEGER},
      </if>
      <if test="record.expressDetailId != null">
        EXPRESS_DETAIL_ID = #{record.expressDetailId,jdbcType=INTEGER},
      </if>
      <if test="record.sku != null">
        SKU = #{record.sku,jdbcType=VARCHAR},
      </if>
      <if test="record.skuName != null">
        SKU_NAME = #{record.skuName,jdbcType=VARCHAR},
      </if>
      <if test="record.model != null">
        MODEL = #{record.model,jdbcType=VARCHAR},
      </if>
      <if test="record.unit != null">
        UNIT = #{record.unit,jdbcType=VARCHAR},
      </if>
      <if test="record.productCompany != null">
        PRODUCT_COMPANY = #{record.productCompany,jdbcType=VARCHAR},
      </if>
      <if test="record.productionLicence != null">
        PRODUCTION_LICENCE = #{record.productionLicence,jdbcType=VARCHAR},
      </if>
      <if test="record.registerNumber != null">
        REGISTER_NUMBER = #{record.registerNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.batchNumber != null">
        BATCH_NUMBER = #{record.batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.unionSequence != null">
        UNION_SEQUENCE = #{record.unionSequence,jdbcType=INTEGER},
      </if>
      <if test="record.arrivalCount != null">
        ARRIVAL_COUNT = #{record.arrivalCount,jdbcType=INTEGER},
      </if>
      <if test="record.wmsHandledArrivalCount != null">
        WMS_HANDLED_ARRIVAL_COUNT = #{record.wmsHandledArrivalCount,jdbcType=INTEGER},
      </if>
      <if test="record.wmsHandledDeliveryCount != null">
        WMS_HANDLED_DELIVERY_COUNT = #{record.wmsHandledDeliveryCount,jdbcType=INTEGER},
      </if>
      <if test="record.manufactureDateTime != null">
        MANUFACTURE_DATE_TIME = #{record.manufactureDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.invalidDateTime != null">
        INVALID_DATE_TIME = #{record.invalidDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.addTime != null">
        ADD_TIME = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creator != null">
        CREATOR = #{record.creator,jdbcType=INTEGER},
      </if>
      <if test="record.modTime != null">
        MOD_TIME = #{record.modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updater != null">
        UPDATER = #{record.updater,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update T_PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL
    set PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL_ID = #{record.purchaseDeliveryDirectBatchDetailId,jdbcType=INTEGER},
      PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID = #{record.purchaseDeliveryDirectBatchInfoId,jdbcType=INTEGER},
      EXPRESS_DETAIL_ID = #{record.expressDetailId,jdbcType=INTEGER},
      SKU = #{record.sku,jdbcType=VARCHAR},
      SKU_NAME = #{record.skuName,jdbcType=VARCHAR},
      MODEL = #{record.model,jdbcType=VARCHAR},
      UNIT = #{record.unit,jdbcType=VARCHAR},
      PRODUCT_COMPANY = #{record.productCompany,jdbcType=VARCHAR},
      PRODUCTION_LICENCE = #{record.productionLicence,jdbcType=VARCHAR},
      REGISTER_NUMBER = #{record.registerNumber,jdbcType=VARCHAR},
      BATCH_NUMBER = #{record.batchNumber,jdbcType=VARCHAR},
      UNION_SEQUENCE = #{record.unionSequence,jdbcType=INTEGER},
      ARRIVAL_COUNT = #{record.arrivalCount,jdbcType=INTEGER},
      WMS_HANDLED_ARRIVAL_COUNT = #{record.wmsHandledArrivalCount,jdbcType=INTEGER},
      WMS_HANDLED_DELIVERY_COUNT = #{record.wmsHandledDeliveryCount,jdbcType=INTEGER},
      MANUFACTURE_DATE_TIME = #{record.manufactureDateTime,jdbcType=TIMESTAMP},
      INVALID_DATE_TIME = #{record.invalidDateTime,jdbcType=TIMESTAMP},
      ADD_TIME = #{record.addTime,jdbcType=TIMESTAMP},
      CREATOR = #{record.creator,jdbcType=INTEGER},
      MOD_TIME = #{record.modTime,jdbcType=TIMESTAMP},
      UPDATER = #{record.updater,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.buyorder.domain.entity.PurchaseDeliveryDirectBatchDetail">
    update T_PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL
    <set>
      <if test="purchaseDeliveryDirectBatchInfoId != null">
        PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID = #{purchaseDeliveryDirectBatchInfoId,jdbcType=INTEGER},
      </if>
      <if test="expressDetailId != null">
        EXPRESS_DETAIL_ID = #{expressDetailId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null">
        SKU_NAME = #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        MODEL = #{model,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        UNIT = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="productCompany != null">
        PRODUCT_COMPANY = #{productCompany,jdbcType=VARCHAR},
      </if>
      <if test="productionLicence != null">
        PRODUCTION_LICENCE = #{productionLicence,jdbcType=VARCHAR},
      </if>
      <if test="registerNumber != null">
        REGISTER_NUMBER = #{registerNumber,jdbcType=VARCHAR},
      </if>
      <if test="batchNumber != null">
        BATCH_NUMBER = #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="unionSequence != null">
        UNION_SEQUENCE = #{unionSequence,jdbcType=INTEGER},
      </if>
      <if test="arrivalCount != null">
        ARRIVAL_COUNT = #{arrivalCount,jdbcType=INTEGER},
      </if>
      <if test="wmsHandledArrivalCount != null">
        WMS_HANDLED_ARRIVAL_COUNT = #{wmsHandledArrivalCount,jdbcType=INTEGER},
      </if>
      <if test="wmsHandledDeliveryCount != null">
        WMS_HANDLED_DELIVERY_COUNT = #{wmsHandledDeliveryCount,jdbcType=INTEGER},
      </if>
      <if test="manufactureDateTime != null">
        MANUFACTURE_DATE_TIME = #{manufactureDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="invalidDateTime != null">
        INVALID_DATE_TIME = #{invalidDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL_ID = #{purchaseDeliveryDirectBatchDetailId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.buyorder.domain.entity.PurchaseDeliveryDirectBatchDetail">
    update T_PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL
    set PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID = #{purchaseDeliveryDirectBatchInfoId,jdbcType=INTEGER},
      EXPRESS_DETAIL_ID = #{expressDetailId,jdbcType=INTEGER},
      SKU = #{sku,jdbcType=VARCHAR},
      SKU_NAME = #{skuName,jdbcType=VARCHAR},
      MODEL = #{model,jdbcType=VARCHAR},
      UNIT = #{unit,jdbcType=VARCHAR},
      PRODUCT_COMPANY = #{productCompany,jdbcType=VARCHAR},
      PRODUCTION_LICENCE = #{productionLicence,jdbcType=VARCHAR},
      REGISTER_NUMBER = #{registerNumber,jdbcType=VARCHAR},
      BATCH_NUMBER = #{batchNumber,jdbcType=VARCHAR},
      UNION_SEQUENCE = #{unionSequence,jdbcType=INTEGER},
      ARRIVAL_COUNT = #{arrivalCount,jdbcType=INTEGER},
      WMS_HANDLED_ARRIVAL_COUNT = #{wmsHandledArrivalCount,jdbcType=INTEGER},
      WMS_HANDLED_DELIVERY_COUNT = #{wmsHandledDeliveryCount,jdbcType=INTEGER},
      MANUFACTURE_DATE_TIME = #{manufactureDateTime,jdbcType=TIMESTAMP},
      INVALID_DATE_TIME = #{invalidDateTime,jdbcType=TIMESTAMP},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER}
    where PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL_ID = #{purchaseDeliveryDirectBatchDetailId,jdbcType=INTEGER}
  </update>
  <update id="updateOutNumById">
    UPDATE T_PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL
    SET WMS_HANDLED_DELIVERY_COUNT = WMS_HANDLED_DELIVERY_COUNT + #{num,jdbcType=INTEGER}
    WHERE PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL_ID = #{purchaseDeliveryDirectBatchDetailId,jdbcType=INTEGER}
  </update>
  <insert id="batchInsertSelective" parameterType="com.vedeng.erp.buyorder.domain.entity.PurchaseDeliveryDirectBatchDetail">
    insert into T_PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
        PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID,
        EXPRESS_DETAIL_ID,
        SKU,
        SKU_NAME,
        MODEL,
        UNIT,
        PRODUCT_COMPANY,
        PRODUCTION_LICENCE,
        REGISTER_NUMBER,
        BATCH_NUMBER,
        UNION_SEQUENCE,
        ARRIVAL_COUNT,
        MANUFACTURE_DATE_TIME,
        INVALID_DATE_TIME,
        ADD_TIME,
        CREATOR,
        MOD_TIME,
        UPDATER,
    </trim>
    values
        <foreach collection="list" item="item" separator=",">
          <trim prefix="(" suffix=")" suffixOverrides=",">
              #{item.purchaseDeliveryDirectBatchInfoId,jdbcType=INTEGER},
              #{item.expressDetailId,jdbcType=INTEGER},
              #{item.sku,jdbcType=VARCHAR},
              #{item.skuName,jdbcType=VARCHAR},
              #{item.model,jdbcType=VARCHAR},
              #{item.unit,jdbcType=VARCHAR},
              #{item.productCompany,jdbcType=VARCHAR},
              #{item.productionLicence,jdbcType=VARCHAR},
              #{item.registerNumber,jdbcType=VARCHAR},
              #{item.batchNumber,jdbcType=VARCHAR},
              #{item.unionSequence,jdbcType=INTEGER},
              #{item.arrivalCount,jdbcType=INTEGER},
              #{item.manufactureDateTime,jdbcType=TIMESTAMP},
              #{item.invalidDateTime,jdbcType=TIMESTAMP},
              #{item.addTime,jdbcType=TIMESTAMP},
              #{item.creator,jdbcType=INTEGER},
              #{item.modTime,jdbcType=TIMESTAMP},
              #{item.updater,jdbcType=INTEGER},
          </trim>
        </foreach>


  </insert>


  <update id="updateArrivalCountByDetailId">
    UPDATE T_PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL
    SET WMS_HANDLED_ARRIVAL_COUNT = WMS_HANDLED_ARRIVAL_COUNT + #{num,jdbcType=INTEGER}
    WHERE PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL_ID = #{purchaseDeliveryDirectBatchDetailId,jdbcType=INTEGER}
  </update>

  <select id="queryInfoByExpressDetailId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM T_PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL
    WHERE EXPRESS_DETAIL_ID = #{expressDetailId,jdbcType=INTEGER}
  </select>

  <select id="queryMainIdByDetailId" resultType="java.lang.Integer">
    SELECT PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID
    FROM T_PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL
    WHERE PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL_ID = #{purchaseDeliveryDirectBatchDetailId,jdbcType=INTEGER}
  </select>

  <select id="queryInfoByMainId" resultMap="BaseResultMap">
  SELECT
  <include refid="Base_Column_List" />
  FROM T_PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL
  WHERE PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID = #{purchaseDeliveryDirectBatchInfoId,jdbcType=INTEGER}
  </select>
  
  <select id="queryInfoByBuyOrderId" resultMap="BaseResultMap">
    select a.*, b.BUYORDER_ID,c.LOGISTICS_NO
    from T_PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL a
           left join T_PURCHASE_DELIVERY_DIRECT_BATCH_INFO b
                     on a.PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID = b.PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID
           left join (select a.EXPRESS_DETAIL_ID,b.LOGISTICS_NO from T_EXPRESS_DETAIL a left join T_EXPRESS b on a.EXPRESS_ID = b.EXPRESS_ID) c 
                     on c.EXPRESS_DETAIL_ID = a.EXPRESS_DETAIL_ID
    where b.BUYORDER_ID =  #{buyOrderId,jdbcType=INTEGER}
  </select>
</mapper>
