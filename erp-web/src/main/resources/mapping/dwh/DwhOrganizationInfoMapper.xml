<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.dwh.externaldb.dao.DwhOrganizationInfoMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.dwh.model.dto.DwhErpOrganizationDto">
        <id column="VD_ODS_PK" jdbcType="BIGINT" property="vdOdsPk"/>
        <result column="START_TIME" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="END_TIME" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="ORG_ID" jdbcType="INTEGER" property="orgId"/>
        <result column="L1_ID" jdbcType="INTEGER" property="l1Id"/>
        <result column="L1_NAME" jdbcType="VARCHAR" property="l1Name"/>
        <result column="L1_TYPE" jdbcType="INTEGER" property="l1Type"/>
        <result column="L1_LEVEL" jdbcType="INTEGER" property="l1Level"/>
        <result column="L1_CREATOR" jdbcType="INTEGER" property="l1Creator"/>
        <result column="L1_COMPANY_ID" jdbcType="INTEGER" property="l1CompanyId"/>
        <result column="L1_ADD_TIME" jdbcType="TIMESTAMP" property="l1AddTime"/>
        <result column="L2_ID" jdbcType="INTEGER" property="l2Id"/>
        <result column="L2_NAME" jdbcType="VARCHAR" property="l2Name"/>
        <result column="L2_TYPE" jdbcType="INTEGER" property="l2Type"/>
        <result column="L2_LEVEL" jdbcType="INTEGER" property="l2Level"/>
        <result column="L2_CREATOR" jdbcType="INTEGER" property="l2Creator"/>
        <result column="L2_COMPANY_ID" jdbcType="INTEGER" property="l2CompanyId"/>
        <result column="L2_ADD_TIME" jdbcType="TIMESTAMP" property="l2AddTime"/>
        <result column="L3_ID" jdbcType="INTEGER" property="l3Id"/>
        <result column="L3_NAME" jdbcType="VARCHAR" property="l3Name"/>
        <result column="L3_TYPE" jdbcType="INTEGER" property="l3Type"/>
        <result column="L3_LEVEL" jdbcType="INTEGER" property="l3Level"/>
        <result column="L3_CREATOR" jdbcType="INTEGER" property="l3Creator"/>
        <result column="L3_COMPANY_ID" jdbcType="INTEGER" property="l3CompanyId"/>
        <result column="L3_ADD_TIME" jdbcType="TIMESTAMP" property="l3AddTime"/>
        <collection property="subUsers" ofType="com.vedeng.dwh.model.dto.DwhErpUserDto">
            <result column="USER_ID" jdbcType="INTEGER" property="userId"/>
            <result column="USERNAME" jdbcType="VARCHAR" property="username"/>
            <result column="POSITION_ID" jdbcType="INTEGER" property="positionId"/>
            <result column="POSITION_NAME" jdbcType="VARCHAR" property="positionName"/>
        </collection>
    </resultMap>
    <sql id="Base_Column_List">
    VD_ODS_PK, START_TIME, END_TIME, ORG_ID, L1_ID, L1_NAME, L1_TYPE, L1_LEVEL, L1_CREATOR,
    L1_COMPANY_ID, L1_ADD_TIME, L2_ID, L2_NAME, L2_TYPE, L2_LEVEL, L2_CREATOR, L2_COMPANY_ID,
    L2_ADD_TIME, L3_ID, L3_NAME, L3_TYPE, L3_LEVEL, L3_CREATOR, L3_COMPANY_ID, L3_ADD_TIME
  </sql>


    <!--  -->
    <select id="getAllErpDepartments" resultMap="BaseResultMap">
        select
        o.VD_ODS_PK,
        o.L1_ID,
        o.L1_NAME,
        o.L2_ID,
        o.L2_NAME,
        o.L3_ID,
        o.L3_NAME,
        u.USER_ID,
        u.USERNAME,
        u.POSITION_ID,
        u.POSITION_NAME
        from (select *
        from DWD_ERP_ORGANIZATION_NJ_DF
        where START_TIME &lt;= #{startTime,jdbcType=TIMESTAMP}
        and END_TIME &gt; #{endTime,jdbcType=TIMESTAMP}
        ) o
        left join (select *
        from DWM_NJ_USER_ORG_DF
        where START_TIME &lt;= #{startTime,jdbcType=TIMESTAMP}
        and END_TIME &gt; #{endTime,jdbcType=TIMESTAMP}
        and COMPANY_ID = 1
        and IS_DISABLED = 0) u
        on u.ORG_ID = o.ORG_ID
    </select>


    <select id="getOrgBaseInfo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />,coalesce (L3_NAME,L2_NAME,L1_NAME) as orgName
        from DWD_ERP_ORGANIZATION_NJ_DF
        where START_TIME &lt;= #{startTime,jdbcType=TIMESTAMP}
        and END_TIME &gt; #{endTime,jdbcType=TIMESTAMP}
    </select>

</mapper>