package com.wms.service.other;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.aftersales.dao.AfterSalesDetailMapper;
import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesDetail;
import com.vedeng.aftersales.model.AfterSalesGoods;
import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.Region;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.OrderConstant;
import com.vedeng.common.constant.goods.GoodsConstants;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.logistics.dao.WarehouseGoodsOperateLogMapper;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.logistics.model.WarehouseStock;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.order.model.vo.SaleorderGoodsVo;
import com.vedeng.stock.api.stock.dto.StockInfoDto;
import com.vedeng.system.service.RegionService;
import com.wms.constant.LogicalEnum;
import com.wms.constant.WMSContant;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.constant.WmsLogicalOperateTypeEnum;
import com.wms.dao.WmsLogicalOrdergoodsMapper;
import com.wms.dao.WmsSendOrderMapper;
import com.wms.dto.ExgOrderDto;
import com.wms.dto.ExgOrderGoodDto;
import com.wms.dto.StockCalculateDto;
import com.wms.dto.WmsResponse;
import com.wms.model.po.WmsLogicalOrdergoods;
import com.wms.model.po.WmsSendOrder;
import com.wms.service.LogicalAfterorderChooseService;
import com.wms.service.LogicalSaleorderChooseService;
import com.wms.service.WMSInterfaceFactory;
import com.wms.service.WmsInterface;
import com.wms.service.stockcalculate.OutputOrderAuditPassCaculateImpl;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName LogicalAfterorderChooseServiceImpl.java
 * @Description TODO  售后换货选择逻辑仓处理器
 * @createTime 2020年07月28日 14:56:00
 */
@Service("logicalAfterorderChooseService")
public class LogicalAfterorderChooseServiceImpl implements LogicalAfterorderChooseService {

    Logger logger= LoggerFactory.getLogger(LogicalAfterorderChooseServiceImpl.class);

    @Resource
    private AfterSalesMapper afterSalesMapper;

    @Resource
    private AfterSalesDetailMapper  afterSalesDetailMapper;

    @Resource
    private AfterSalesGoodsMapper afterSalesGoodsMapper;

    @Resource
    private WarehouseGoodsOperateLogMapper warehouseGoodsOperateLogMapper;

    @Resource
    private WmsLogicalOrdergoodsMapper wmsLogicalOrdergoodsMapper;

    @Resource
    private BuyorderGoodsMapper buyorderGoodsMapper;

    @Autowired
    private WarehouseStockService warehouseStockService;

    @Autowired
    private LogicalSaleorderChooseService logicalSaleorderChooseServiceImpl;

    @Value("${buyorder_after_logicalPre}")
    private String buyorderAfterChooseLogicalPre;

    @Autowired
    private OutputOrderAuditPassCaculateImpl outputOrderAuditPassCaculateImpl;

    @Autowired
    private WMSInterfaceFactory wmsInterfaceFactory;

    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private UserMapper userMapper;

    @Autowired
    private RegionService regionService;

    @Autowired
    private LogicalSaleorderChooseService logicalSaleorderChooseService;

    /**
     * @description: 售后单审核通过指定逻辑仓
     * @return:
     * @author: Strange
     * @date: 2020/7/28
     **/
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void chooseLogicalAfterorder(AfterSales order, User user) throws Exception {
             if(order == null || order.getAfterSalesId() == null || !OrderConstant.ORDER_STATUS_VALID.equals(order.getAtferSalesStatus())){
                 return;
             }
             AfterSales afterSales = afterSalesMapper.getAfterSalesById(order.getAfterSalesId());

             Integer type = afterSales.getType();
                 //销售换货
             if(StockOperateTypeConst.AFTERORDER_CHANGE_FINSH.equals(type)){
                chooseLogicalAfterSaleorder(afterSales,user);
            }/*else if(StockOperateTypeConst.AFTERORDER_BACK_FINSH.equals(type)){
                 //销售退货下发WMS
                putAfterReturnWMS(afterSales,user);
             }
             else if(StockOperateTypeConst.AFTERBUYORDER_BACK_FINSH.equals(type)){
                 //采购退货
                 chooseLogicalAfterBuyorder(afterSales,user);
             }else if(StockOperateTypeConst.AFTERBUYORDER_CHANGE_FINSH.equals(type)){
                 //采购换货
                 chooseLogicalAfterBuyorder(afterSales,user);
             }*/
    }


    /**
     * @description: 销售换货单选择逻辑仓
     * @return:
     * @author: Strange
     * @date: 2020/7/28
     **/
    private void chooseLogicalAfterSaleorder(AfterSales afterSales, User user) throws Exception {
        AfterSalesGoods afterGoods = new AfterSalesGoods();
        afterGoods.setAfterSalesId(afterSales.getAfterSalesId());
        List<AfterSalesGoodsVo> afterSalesGoodsVosList = afterSalesGoodsMapper.getAftersalesGoodsList(afterGoods);
        if(CollectionUtils.isEmpty(afterSalesGoodsVosList)){
            return;
        }
        Saleorder saleOrder = saleorderMapper.getSaleOrderById(afterSales.getOrderId());
        if(!OrderConstant.ORDER_ALL_PAYMENT.equals(saleOrder.getPaymentStatus())){
            return;
        }
        List<String> skuList = afterSalesGoodsVosList.stream().map(item -> item.getSku()).collect(Collectors.toList());
        //逻辑库存数据
        Map<String, WarehouseStock> logicalStockInfo = warehouseStockService.getLogicalStockMapInfo(skuList);
        //逻辑仓订单商品表信息
        List<WmsLogicalOrdergoods> wmsLogicalOrdergoodsList = new ArrayList<>();

        Map<Integer, WmsLogicalOrdergoods> oldLogicalMap = getHistoryWmsLogicalOrdergoodsMap(afterSalesGoodsVosList);
        for (AfterSalesGoodsVo afterSalesGoodsVo : afterSalesGoodsVosList) {
            if (isExist(oldLogicalMap, afterSalesGoodsVo)) {continue;}
            SaleorderGoodsVo saleorderGoodsVo = new SaleorderGoodsVo();
            saleorderGoodsVo.setSaleorderGoodsId(afterSalesGoodsVo.getAfterSalesGoodsId());
            saleorderGoodsVo.setSku(afterSalesGoodsVo.getSku());
            saleorderGoodsVo.setGoodsId(afterSalesGoodsVo.getGoodsId());
            saleorderGoodsVo.setNum(afterSalesGoodsVo.getNum()-afterSalesGoodsVo.getDeliveryNum());
            saleorderGoodsVo.setIsActionGoods(0);
            logicalSaleorderChooseServiceImpl.chooseLogicalOrder(logicalStockInfo,wmsLogicalOrdergoodsList,saleorderGoodsVo);
        }
        logger.info("chooseLogicalAfterSaleorder  aftersaleNo:{},insertInfo:{}",afterSales.getAfterSalesNo(),wmsLogicalOrdergoodsList.toString());
        insertWmsLogicalOrderGodos(wmsLogicalOrdergoodsList);
        //同步库存服务
        sysnchStock(afterSales,wmsLogicalOrdergoodsList);

        //换货单下传wms
        putExchangeWMS(afterSales,wmsLogicalOrdergoodsList,user);

    }

    @Override
    public void putExchangeWMS(AfterSales afterSales, List<WmsLogicalOrdergoods> wmsLogicalOrdergoodsList, User user) throws Exception {
        if(CollectionUtils.isEmpty(wmsLogicalOrdergoodsList)){
            logger.info("下发wms 销售换货单无明细 no:{}",afterSales.getAfterSalesNo());
            return;
        }
        //同步销售换货单->WMS
        AfterSalesDetail afterSalesDetailVo = afterSalesDetailMapper.selectadtbyid(afterSales);
        ExgOrderDto exgOrderDto = new ExgOrderDto();
        exgOrderDto.setDOCNO(afterSales.getAfterSalesNo());
        exgOrderDto.setDOC_TYPE(WmsInterfaceOrderType.EXCHANG_SALEORDER);
        exgOrderDto.setIs_exchange(afterSales.getIsLightning() != null && afterSales.getIsLightning().equals(0) ? "Y": "N");
        String updateTime = DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss");
        exgOrderDto.setApproveTime(updateTime);
        if(afterSales.getServiceUserId() != null && afterSales.getServiceUserId() != 0){
            User userByUserId = userMapper.getUserByUserId(afterSales.getServiceUserId());
            exgOrderDto.setDepartment(StringUtil.isEmpty(userByUserId.getOrgName()) ? "/" : userByUserId.getOrgName() );
            exgOrderDto.setStaffName(userByUserId.getUsername());
        }else{
            exgOrderDto.setDepartment(StringUtil.isEmpty(user.getOrgName()) ? "/" : user.getOrgName() );
            exgOrderDto.setStaffName(user.getUsername());
        }

        exgOrderDto.setConsigneeID(afterSalesDetailVo.getTraderId().toString());
        exgOrderDto.setConsigneeName(afterSalesDetailVo.getTraderName());

        if(afterSalesDetailVo.getAreaId() == null || afterSalesDetailVo.getAreaId().equals(0)){
            throw new Exception("销售换货单收货地址id为空下发WMS失败单号:"+afterSales.getAfterSalesNo());
        }
        List<Region> regionList = regionService.getRegionInfoByMinRegionId(afterSalesDetailVo.getAreaId());
        if(CollectionUtils.isEmpty(regionList)){
            throw new Exception("销售换货单收货地址为空下发WMS失败单号:"+afterSales.getAfterSalesNo());
        }
        exgOrderDto.setC_Province(regionList.size() > 0 ? regionList.get(0).getRegionName(): "");
        exgOrderDto.setC_City(regionList.size() > 1 ? regionList.get(1).getRegionName(): "");
        exgOrderDto.setC_district(regionList.size() > 2 ? regionList.get(2).getRegionName(): "");

        exgOrderDto.setC_Address1(afterSalesDetailVo.getAddress());
        exgOrderDto.setC_Contact(afterSalesDetailVo.getTraderContactName());
        exgOrderDto.setC_Tel1(afterSalesDetailVo.getTraderContactTelephone());
        exgOrderDto.setC_Tel2(afterSalesDetailVo.getTraderContactMobile());

        List<ExgOrderGoodDto> exgOrderGoodDtoList = new ArrayList<>();
        for (WmsLogicalOrdergoods wmsLogicalOrdergoods : wmsLogicalOrdergoodsList) {
            ExgOrderGoodDto exgOrderGoodDto = new ExgOrderGoodDto();
            exgOrderGoodDto.setSKU(wmsLogicalOrdergoods.getSku());
            exgOrderGoodDto.setQty(new BigDecimal(wmsLogicalOrdergoods.getNum()));
            exgOrderGoodDto.setLotAtt08(LogicalEnum.getLogicalWarehouseCode(wmsLogicalOrdergoods.getLogicalWarehouseId()));
            exgOrderGoodDto.setD_EDI_07(wmsLogicalOrdergoods.getRelatedId().toString());
            //销售售后换货单不考虑专项发货的情况
            exgOrderGoodDto.setLotAtt07("*");
            exgOrderGoodDtoList.add(exgOrderGoodDto);
        }
        exgOrderDto.setDetails(exgOrderGoodDtoList);

        logger.info("ERP销售换货单下发至WMS的请求: exgOrderDto:{}" + JSON.toJSONString(exgOrderDto));

        //下发存表，V_WMS_SEND_ORDER
        WmsSendOrder wmsSendOrder = logicalSaleorderChooseService.getWmsSendOrder(afterSales.getAfterSalesNo(),afterSales.getAfterSalesId(), ErpConst.FIVE,user);
        try {
            WmsInterface wmsInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_EXG_DATA);
            WmsResponse response = wmsInterface.request(exgOrderDto);
            if( response.getReturnFlag().equals(ErpConst.ZERO)){
                //throw new Exception("ERP销售换货单下传失败 单号"+afterSales.getAfterSalesNo()+",失败原因:"+response.getReturnDesc());
                logger.info("ERP销售换货单下传失败 单号"+afterSales.getAfterSalesNo()+",失败原因:"+response.getReturnDesc());
            }else {
                //下发成功，更新V_WMS_SEND_ORDER状态
                logicalSaleorderChooseService.saveWmsSendOrder(wmsSendOrder);
            }
            logger.info("ERP销售换货单下发WMS的响应:" + JSON.toJSONString(response));
        }catch (Exception e){
            logger.error("ERP销售换货单下传失败,{}",e);
        }
    }

    private User getUser(){
        User user = null;
        ServletRequestAttributes ra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (ra != null) {
            HttpServletRequest request = ra.getRequest();
            if (request != null && request.getSession() != null) {
                user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
            }
        }
        if(user==null){
            user= new User();
            user.setCompanyId(1);
            user.setUserId(2);
            user.setCompanyName("南京贝登医疗有限公司");
            user.setUsername("njadmin");
        }
        return user;
    }

    /**
     * 同步库存服务占用
     * @param afterSales
     * @param wmsLogicalOrdergoodsList
     * @throws Exception
     */
    private void sysnchStock(AfterSales afterSales, List<WmsLogicalOrdergoods> wmsLogicalOrdergoodsList) throws Exception {
        if(CollectionUtils.isEmpty(wmsLogicalOrdergoodsList)){return;}

        List<StockCalculateDto> stockCalculateList  = new ArrayList<>();
        wmsLogicalOrdergoodsList.stream().forEach( wmsLogicalOrdergoods -> {
            StockCalculateDto stockCalculateDto = new StockCalculateDto();
            stockCalculateDto.setSku(wmsLogicalOrdergoods.getSku());
            stockCalculateDto.setOccupyNum(wmsLogicalOrdergoods.getOccupyNum());
            stockCalculateDto.setStockNum(0);
            stockCalculateDto.setLogicalWarehouseId(wmsLogicalOrdergoods.getLogicalWarehouseId());
            stockCalculateList.add(stockCalculateDto);
        });

        //库存策略计算
        StockInfoDto stockInfoDto  = new StockInfoDto();
        stockInfoDto.setRelatedNo(afterSales.getAfterSalesNo());
        stockInfoDto.setWarehouseStockList(outputOrderAuditPassCaculateImpl.calculateStockInfo(stockCalculateList));

        //更新库存服务
        warehouseStockService.updateStockInfo(stockInfoDto);

    }
    /**
     * 获取以指定逻辑仓的订单商品
     * @param afterSalesGoodsVosList
     * @return
     */
    private Map<Integer, WmsLogicalOrdergoods> getHistoryWmsLogicalOrdergoodsMap(List<AfterSalesGoodsVo> afterSalesGoodsVosList) {
        List<Integer> relateIdList = afterSalesGoodsVosList
                .stream().map(AfterSalesGoodsVo::getAfterSalesGoodsId)
                .collect(Collectors.toList());
        //如果之前保存过则不保存
        return wmsLogicalOrdergoodsMapper.getLogicalOrderInfoGroupByRelateId(relateIdList, WmsLogicalOperateTypeEnum.AFTER_SALEORDER_TYPE.getOperateTypeCode())
                .stream()
                .collect(Collectors.toMap(WmsLogicalOrdergoods::getRelatedId,item->item));
    }

    private WmsLogicalOrdergoods getWmsLogicalOrdergoods(AfterSalesGoodsVo afterSalesGoodsVo) {
        WmsLogicalOrdergoods wmsLogicalOrdergoods = new WmsLogicalOrdergoods();
        wmsLogicalOrdergoods.setOperateType(WmsLogicalOperateTypeEnum.AFTER_SALEORDER_TYPE.getOperateTypeCode());
        wmsLogicalOrdergoods.setSku(afterSalesGoodsVo.getSku());
        wmsLogicalOrdergoods.setGoodsId(afterSalesGoodsVo.getGoodsId());
        wmsLogicalOrdergoods.setRelatedId(afterSalesGoodsVo.getAfterSalesGoodsId());
        return wmsLogicalOrdergoods;
    }

    private void insertWmsLogicalOrderGodos(List<WmsLogicalOrdergoods> wmsLogicalOrdergoodsList) {
        if(CollectionUtils.isEmpty(wmsLogicalOrdergoodsList)){
            return;
        }
        for (WmsLogicalOrdergoods wmsLogicalOrdergoods : wmsLogicalOrdergoodsList) {
            wmsLogicalOrdergoods.setOperateType(WmsLogicalOperateTypeEnum.AFTER_SALEORDER_TYPE.getOperateTypeCode());
            wmsLogicalOrdergoodsMapper.insertSelective(wmsLogicalOrdergoods);
        }
        logger.info("afterchooselogical insertinfo:{}",wmsLogicalOrdergoodsList.toString());
    }
    private boolean isExist(Map<Integer, WmsLogicalOrdergoods> oldLogicalMap, AfterSalesGoodsVo afterSalesGoodsVo) {
        WmsLogicalOrdergoods wmsLogicalOrdergoods = oldLogicalMap.get(afterSalesGoodsVo.getAfterSalesGoodsId());
        if(wmsLogicalOrdergoods != null || afterSalesGoodsVo.getDeliveryDirect().equals(1) || afterSalesGoodsVo.getGoodsId().equals(GoodsConstants.FREIGHT)){
            return true;
        }
        return false;
    }
    /**
     * @description: 采购售后单选择逻辑仓
     * @return:
     * @author: Strange
     * @date: 2020/7/28
     **/
    private void chooseLogicalAfterBuyorder(AfterSales afterSales, User user) {
        AfterSalesGoods afterGoods = new AfterSalesGoods();
        afterGoods.setAfterSalesId(afterSales.getAfterSalesId());
        List<AfterSalesGoodsVo> afterSalesGoodsVosList = afterSalesGoodsMapper.getBuyorderAfterSalesGoodsVosList(afterGoods);
        if(CollectionUtils.isEmpty(afterSalesGoodsVosList)){
            return;
        }
        //选择逻辑仓优先级队列
        List<Integer> chooseLogicalPre = JSONObject.parseArray(buyorderAfterChooseLogicalPre, Integer.class);
        LinkedList<Integer> chooseLogicalPreLink = new LinkedList();
        chooseLogicalPreLink.addAll(chooseLogicalPre);
        //逻辑仓订单商品表信息
        List<WmsLogicalOrdergoods> wmsLogicalOrdergoodsList = new ArrayList<>();

        Map<Integer, WmsLogicalOrdergoods> oldLogicalMap = getHistoryWmsLogicalOrdergoodsMap(afterSalesGoodsVosList);
        Map<Integer,Integer> buyorderGoodsNumMap = new HashMap<>();
        if(StockOperateTypeConst.AFTERBUYORDER_BACK_FINSH.equals(afterSales.getType())){
            List<Integer> buyorderGoodsIdList = afterSalesGoodsVosList.stream().map(AfterSalesGoodsVo::getOrderDetailId).collect(Collectors.toList());
            List<BuyorderGoodsVo> buyorderGoodsList = buyorderGoodsMapper.getBuyorderGoodsVoListByBuyorderGoodsIdListNoSpecial(buyorderGoodsIdList);
            buyorderGoodsNumMap = buyorderGoodsList.stream().collect(Collectors.toMap(BuyorderGoodsVo::getBuyorderGoodsId,item->item.getNum()));
        }

        for (AfterSalesGoodsVo afterSalesGoodsVo : afterSalesGoodsVosList){
            if (isExist(oldLogicalMap, afterSalesGoodsVo)) {continue;}
            //本次售后数量
            Integer nowAfterNum = afterSalesGoodsVo.getNum();
            if(StockOperateTypeConst.AFTERBUYORDER_BACK_FINSH.equals(afterSales.getType())){
                // 查询当前的采购单入库商品在库数量
                WarehouseGoodsOperateLog record = new WarehouseGoodsOperateLog();
                record.setOperateType(1);
                record.setRelatedId(afterSalesGoodsVo.getOrderDetailId());
                record.setCompanyId(1);
                //采购单实际在库数量
                Integer instockNum = warehouseGoodsOperateLogMapper.getInstockNumByRelatedId(record);
                AfterSalesGoods searchGoods = new AfterSalesGoods();
                searchGoods.setOrderDetailId(afterSalesGoodsVo.getOrderDetailId());
                searchGoods.setOperateType(StockOperateTypeConst.AFTERBUYORDER_BACK_FINSH);
                //已完结售后数量
                Integer afterNum = afterSalesGoodsMapper.getAfterbuyorderNumByBuyorderGoodsId(searchGoods);
                Integer buyorderGoodsNum = buyorderGoodsNumMap.get(afterSalesGoodsVo.getOrderDetailId());
                //需退货出库数量 = 采购售后退货单中的数量 - (采购单原数量-已完结售后数量-采购单在库数量)
                nowAfterNum = nowAfterNum - (buyorderGoodsNum - afterNum -instockNum );
                //不需要出库
                if(nowAfterNum <= 0){
                    continue;
                }
            }
            Map<Integer,WarehouseGoodsOperateLog> logicalNumMap = warehouseGoodsOperateLogMapper.getBuyorderInlogicalByBuyorderGoodsId(afterSalesGoodsVo.getOrderDetailId())
                    .stream()
                    .collect(Collectors.toMap(WarehouseGoodsOperateLog::getLogicalWarehouseId,item ->item));
            chooseBuyorderLogicalNum(chooseLogicalPreLink, wmsLogicalOrdergoodsList, afterSalesGoodsVo, nowAfterNum, logicalNumMap);
        }
        insertWmsLogicalOrderGodos(wmsLogicalOrdergoodsList);
    }

    /**
     * @description: 采购售后单优先级选择逻辑仓
     *
     * @return:
     * @author: Strange
     * @date: 2020/7/28
     **/
    private void chooseBuyorderLogicalNum(LinkedList<Integer> chooseLogicalPreLink, List<WmsLogicalOrdergoods> wmsLogicalOrdergoodsList,
                                          AfterSalesGoodsVo afterSalesGoodsVo, Integer goodsVoNum, Map<Integer, WarehouseGoodsOperateLog> logicalNumMap) {
        //采购单选择优先级队列
        for (Integer logicalWarehouseId : chooseLogicalPreLink) {
            WarehouseGoodsOperateLog warehouseGoodsOperateLog = logicalNumMap.get(logicalWarehouseId);
            if(warehouseGoodsOperateLog != null){
                Integer lastStockNum = warehouseGoodsOperateLog.getLastStockNum();
                WmsLogicalOrdergoods wmsLogicalOrdergoods = getWmsLogicalOrdergoods(afterSalesGoodsVo);
                wmsLogicalOrdergoods.setLogicalWarehouseId(logicalWarehouseId);
                if(lastStockNum >= goodsVoNum){
                    wmsLogicalOrdergoods.setNum(goodsVoNum);
                    goodsVoNum = 0;
                }else if(lastStockNum < goodsVoNum){
                    wmsLogicalOrdergoods.setNum(lastStockNum);
                    goodsVoNum = goodsVoNum - lastStockNum;
                }
                wmsLogicalOrdergoodsList.add(wmsLogicalOrdergoods);
                if(goodsVoNum == 0){
                    break;
                }
            }
        }
        if(goodsVoNum != 0){
            WmsLogicalOrdergoods wmsLogicalOrdergoods = getWmsLogicalOrdergoods(afterSalesGoodsVo);
            wmsLogicalOrdergoods.setLogicalWarehouseId(LogicalEnum.HG.getLogicalWarehouseId());
            wmsLogicalOrdergoods.setNum(goodsVoNum);
            wmsLogicalOrdergoodsList.add(wmsLogicalOrdergoods);
        }
    }

    @Override
    public void closeExchangeAfterOrder(AfterSalesVo afterorder){
        try {
            if(afterorder == null || afterorder.getAfterSalesId() == null){
                return;
            }
            AfterSales afterSales = afterSalesMapper.getAfterSalesById(afterorder.getAfterSalesId());
            if(!StockOperateTypeConst.AFTERORDER_CHANGE_FINSH.equals(afterSales.getType()) &&
                    !StockOperateTypeConst.AFTERBUYORDER_CHANGE_FINSH.equals(afterSales.getType()) ){
                return;
            }
            //已关闭并且生效
            if(!OrderConstant.ORDER_STATUS_CLOSE.equals(afterorder.getAtferSalesStatus()) ||
                    !OrderConstant.ORDER_STATUS_VALID.equals(afterSales.getValidStatus())){
                return;
            }

            Integer operaType = WmsLogicalOperateTypeEnum.AFTER_SALEORDER_TYPE.getOperateTypeCode();
            if(StockOperateTypeConst.AFTERBUYORDER_CHANGE_FINSH.equals(afterSales.getType())){
                operaType = WmsLogicalOperateTypeEnum.PURCHASE_EXG.getOperateTypeCode();
            }

            updateStock(afterSales, operaType);

        } catch (Exception e) {
            logger.error("closeExchangeAfterOrder error",e);
        }
    }


    @Override
    public void confirmExchangeAfterOrder(Integer afterSalesId) {
        try {
            if(afterSalesId == null ){
                return;
            }
            AfterSales afterSales = afterSalesMapper.getAfterSalesById(afterSalesId);
            //已完成并且生效
            if(!OrderConstant.ATFER_SALES_STATUS_CONFIRM.equals(afterSales.getAtferSalesStatus()) ||
                    !OrderConstant.ORDER_STATUS_VALID.equals(afterSales.getValidStatus())){
                return;
            }

            Integer operaType = WmsLogicalOperateTypeEnum.PURCHASE_EXG.getOperateTypeCode();

            updateStock(afterSales, operaType);

        } catch (Exception e) {
            logger.error("confirmExchangeAfterOrder error",e);
        }
    }


    /**
     * 更新库存服务
     * @param afterSales 售后单
     * @param operaType 单类型
     * @throws Exception
     */
    private void updateStock(AfterSales afterSales, Integer operaType) throws Exception {

        List<WmsLogicalOrdergoods> afterorderLogicalList = wmsLogicalOrdergoodsMapper.getAfterorderLogicalChooseInfo(afterSales.getAfterSalesId(), operaType);

        WmsLogicalOrdergoods update = new WmsLogicalOrdergoods();

        for (WmsLogicalOrdergoods wmsLogicalOrdergoods : afterorderLogicalList) {
            Integer occupyNum = wmsLogicalOrdergoods.getOccupyNum();
            update.setOccupyNum(-occupyNum);
            update.setRelatedId(wmsLogicalOrdergoods.getRelatedId());
            update.setOperateType(wmsLogicalOrdergoods.getOperateType());
            if (!OrderConstant.ATFER_SALES_STATUS_CONFIRM.equals(afterSales.getAtferSalesStatus())) {
                update.setIsDelete(1);
            }
            wmsLogicalOrdergoodsMapper.updateOccupyNum(update);

            wmsLogicalOrdergoods.setOccupyNum(-occupyNum);
        }

        sysnchStock(afterSales,afterorderLogicalList);
    }
}
