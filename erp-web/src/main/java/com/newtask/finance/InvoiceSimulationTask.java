package com.newtask.finance;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.finance.domain.dto.SaleInvoiceOpenResponseDto;
import com.vedeng.erp.finance.dto.InvoiceApplyDetailDto;
import com.vedeng.erp.finance.dto.InvoiceApplyDto;
import com.vedeng.erp.finance.service.InvoiceApplyApiService;
import com.vedeng.erp.finance.service.InvoiceApplyService;
import com.vedeng.erp.finance.service.impl.SalesOpenInvoiceImpl;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 发票开票成功模拟定时任务
 * 
 * 功能说明：
 * - 模拟开票成功，调用successOpenInvoice方法
 * - 支持通过销售订单号触发开票成功流程
 * - 用于测试和开发环境的开票流程验证
 * 
 * <AUTHOR>
 */
@JobHandler(value = "InvoiceSimulationTask")
@Component
@Slf4j
public class InvoiceSimulationTask extends AbstractJobHandler {

    @Autowired
    private InvoiceApplyService invoiceApplyService;
    @Autowired
    private InvoiceApplyApiService invoiceApplyApiService;
    
    @Autowired
    private SaleOrderApiService saleOrderApiService;
    
    @Autowired
    private SalesOpenInvoiceImpl salesOpenInvoice;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        log.info("开始执行发票开票成功模拟任务，时间: {}, 参数: {}", 
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), param);

        try {
            // 参数可以是单个销售订单号，或者多个用逗号分隔的销售订单号
            if (StrUtil.isBlank(param)) {
                log.info("未提供销售订单号参数，跳过执行");
                return ReturnT.SUCCESS;
            }

            // 支持多个销售订单号，用逗号分隔
            String[] saleOrderNos = param.split(",");
            int successCount = 0;
            int failCount = 0;

            for (String saleOrderNo : saleOrderNos) {
                saleOrderNo = saleOrderNo.trim();
                if (StrUtil.isBlank(saleOrderNo)) {
                    continue;
                }
                
                try {
                    simulateInvoiceSuccessBySaleOrderNo(saleOrderNo);
                    successCount++;
                    log.info("模拟开票成功完成，销售订单号: {}", saleOrderNo);
                } catch (Exception e) {
                    failCount++;
                    log.error("模拟开票成功失败，销售订单号: {}", saleOrderNo, e);
                }
            }

            log.info("发票开票成功模拟任务执行完成，成功: {}, 失败: {}", successCount, failCount);
            
            if (failCount > 0) {
                return new ReturnT<>(ReturnT.FAIL_CODE, String.format("部分执行失败，成功: %d, 失败: %d", successCount, failCount));
            }
            
            return ReturnT.SUCCESS;

        } catch (Exception e) {
            log.error("发票开票成功模拟任务执行异常", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "任务执行异常: " + e.getMessage());
        }
    }

    /**
     * 根据销售订单号模拟开票成功
     * 
     * @param saleOrderNo 销售订单号
     */
    private void simulateInvoiceSuccessBySaleOrderNo(String saleOrderNo) {
        log.info("开始模拟开票成功，销售订单号: {}", saleOrderNo);

        try {
            // 1. 根据销售订单号查询销售订单信息
            SaleorderInfoDto saleOrder = saleOrderApiService.getBySaleOrderNo(saleOrderNo);
            if (saleOrder == null) {
                log.warn("未找到销售订单，销售订单号: {}", saleOrderNo);
                throw new RuntimeException("未找到销售订单: " + saleOrderNo);
            }

            Integer saleOrderId = saleOrder.getSaleorderId();
            log.info("查询到销售订单，销售订单ID: {}, 销售订单号: {}", saleOrderId, saleOrderNo);

            // 2. 查询该销售订单的开票申请
            Page page = new Page(1, 100);
            PageInfo<InvoiceApplyDto> waitInvoiceApply = invoiceApplyApiService.getWaitInvoiceApply(page, saleOrderNo);
            List<InvoiceApplyDto> invoiceApplies = waitInvoiceApply.getList();
            if (CollUtil.isEmpty(invoiceApplies)) {
                log.warn("未找到开票申请，销售订单号: {}, 销售订单ID: {}", saleOrderNo, saleOrderId);
                throw new RuntimeException("未找到开票申请，销售订单号: " + saleOrderNo);
            }

            // 3. 处理每个开票申请
            for (InvoiceApplyDto invoiceApply : invoiceApplies) {
                processInvoiceApply(invoiceApply, saleOrderNo);
            }

        } catch (Exception e) {
            log.error("模拟开票成功异常，销售订单号: {}", saleOrderNo, e);
            throw new RuntimeException("模拟开票成功失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理单个开票申请
     * 
     * @param invoiceApply 开票申请
     * @param saleOrderNo 销售订单号
     */
    private void processInvoiceApply(InvoiceApplyDto invoiceApply, String saleOrderNo) {
        Integer invoiceApplyId = invoiceApply.getInvoiceApplyId();
        log.info("开始处理开票申请，开票申请ID: {}, 销售订单号: {}", invoiceApplyId, saleOrderNo);

        try {
            // 检查开票申请状态
            if (invoiceApply.getValidStatus() == null || invoiceApply.getValidStatus() != 0) {
                log.info("开票申请状态不是待审核状态，跳过处理，开票申请ID: {}, 状态: {}", 
                        invoiceApplyId, invoiceApply.getValidStatus());
                return;
            }

            // 构建模拟的开票成功响应
            SaleInvoiceOpenResponseDto responseDto = buildMockInvoiceResponse(invoiceApply);

            // 调用开票成功处理方法
            salesOpenInvoice.successOpenInvoice(invoiceApply, responseDto);

            log.info("模拟开票成功完成，开票申请ID: {}, 销售订单号: {}", invoiceApplyId, saleOrderNo);

        } catch (Exception e) {
            log.error("处理开票申请异常，开票申请ID: {}, 销售订单号: {}", invoiceApplyId, saleOrderNo, e);
            throw new RuntimeException("处理开票申请失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建模拟的开票成功响应
     * 
     * @param invoiceApply 开票申请
     * @return 模拟的开票响应
     */
    private SaleInvoiceOpenResponseDto buildMockInvoiceResponse(InvoiceApplyDto invoiceApply) {
        SaleInvoiceOpenResponseDto responseDto = new SaleInvoiceOpenResponseDto();
        
        // 设置成功状态
        responseDto.setIsSuccess(true);
        responseDto.setReturnCode("0000");
        responseDto.setReturnMessage("开票成功");
        
        // 生成模拟的发票信息 - 20位数字发票号码
        String timestamp = String.valueOf(System.currentTimeMillis());
        // 确保生成20位数字发票号码，如果时间戳超过20位则截取后20位，不足20位则往前补0
        String invoiceNumber;
        if (timestamp.length() >= 20) {
            invoiceNumber = timestamp.substring(timestamp.length() - 20);
        } else {
            invoiceNumber = String.format("%020d", Long.parseLong(timestamp));
        }
        responseDto.setFphm(invoiceNumber);
        responseDto.setKprq(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))); // 开票日期

        // sum totalAmount 求和Bigdecimal InvoiceApplyDetailDto::getTotalAmount
        BigDecimal totalAmount = invoiceApply.getInvoiceApplyDetailDtoList().stream().map(InvoiceApplyDetailDto::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        invoiceApply.setTotalAmount(totalAmount);
        log.info("构建模拟开票响应，开票申请ID: {}, 20位发票号码: {}, 开票日期: {}",
                invoiceApply.getInvoiceApplyId(), responseDto.getFphm(), responseDto.getKprq());
        
        return responseDto;
    }
}
