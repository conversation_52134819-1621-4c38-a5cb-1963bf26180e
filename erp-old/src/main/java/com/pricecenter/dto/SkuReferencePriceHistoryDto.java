package com.pricecenter.dto;

import java.math.BigDecimal;

public class SkuReferencePriceHistoryDto {

    private Long referencePriceId;

    private String operator;

    private BigDecimal oldReferenceTerminalPrice;

    private BigDecimal oldReferenceDistributionPrice;

    private BigDecimal oldReferenceGroupPrice;

    private BigDecimal newReferenceTerminalPrice;

    private BigDecimal newReferenceDistributionPrice;

    private BigDecimal newReferenceGroupPrice;

    private String addTime;

    public Long getReferencePriceId() {
        return referencePriceId;
    }

    public void setReferencePriceId(Long referencePriceId) {
        this.referencePriceId = referencePriceId;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator == null ? null : operator.trim();
    }

    public BigDecimal getOldReferenceTerminalPrice() {
        return oldReferenceTerminalPrice;
    }

    public void setOldReferenceTerminalPrice(BigDecimal oldReferenceTerminalPrice) {
        this.oldReferenceTerminalPrice = oldReferenceTerminalPrice;
    }

    public BigDecimal getOldReferenceDistributionPrice() {
        return oldReferenceDistributionPrice;
    }

    public void setOldReferenceDistributionPrice(BigDecimal oldReferenceDistributionPrice) {
        this.oldReferenceDistributionPrice = oldReferenceDistributionPrice;
    }

    public BigDecimal getOldReferenceGroupPrice() {
        return oldReferenceGroupPrice;
    }

    public void setOldReferenceGroupPrice(BigDecimal oldReferenceGroupPrice) {
        this.oldReferenceGroupPrice = oldReferenceGroupPrice;
    }

    public BigDecimal getNewReferenceTerminalPrice() {
        return newReferenceTerminalPrice;
    }

    public void setNewReferenceTerminalPrice(BigDecimal newReferenceTerminalPrice) {
        this.newReferenceTerminalPrice = newReferenceTerminalPrice;
    }

    public BigDecimal getNewReferenceDistributionPrice() {
        return newReferenceDistributionPrice;
    }

    public void setNewReferenceDistributionPrice(BigDecimal newReferenceDistributionPrice) {
        this.newReferenceDistributionPrice = newReferenceDistributionPrice;
    }

    public BigDecimal getNewReferenceGroupPrice() {
        return newReferenceGroupPrice;
    }

    public void setNewReferenceGroupPrice(BigDecimal newReferenceGroupPrice) {
        this.newReferenceGroupPrice = newReferenceGroupPrice;
    }

    public String getAddTime() {
        return addTime;
    }

    public void setAddTime(String addTime) {
        this.addTime = addTime == null ? null : addTime.trim();
    }
}