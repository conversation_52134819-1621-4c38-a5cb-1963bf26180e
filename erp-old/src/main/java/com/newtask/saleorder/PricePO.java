package com.newtask.saleorder;

public class PricePO {

    private String       SALEORDER_GOODS_ID;
    private String BUYORDER_ID;
    private String        BUYORDER_GOODS_ID;



    private String BUYORDER_NO;


    public String getBUYORDER_NUM_LOG() {
        return BUYORDER_NUM_LOG;
    }

    public void setBUYORDER_NUM_LOG(String BUYORDER_NUM_LOG) {
        this.BUYORDER_NUM_LOG = BUYORDER_NUM_LOG;
    }

    private String BUYORDER_NUM_LOG;
    private String        SALEORDER_ID;
    private String SALEORDER_NO;

    private String SALEORDER_NUM;
    private String        BUYORDER_NUM;
    private String BUYORDER_PRICE;
    private String       SALEORDER_PRICE;


    @Override
    public String toString() {
        return "PricePO{" +
                "SALEORDER_GOODS_ID='" + SALEORDER_GOODS_ID + '\'' +
                ", BUYORDER_ID='" + BUYORDER_ID + '\'' +
                ", BUYORDER_GOODS_ID='" + BUYORDER_GOODS_ID + '\'' +
                ", BUYORDER_NO='" + BUYORDER_NO + '\'' +
                ", BUYORDER_NUM_LOG='" + BUYORDER_NUM_LOG + '\'' +
                ", SALEORDER_ID='" + SALEORDER_ID + '\'' +
                ", SALEORDER_NO='" + SALEORDER_NO + '\'' +
                ", SALEORDER_NUM='" + SALEORDER_NUM + '\'' +
                ", BUYORDER_NUM='" + BUYORDER_NUM + '\'' +
                ", BUYORDER_PRICE='" + BUYORDER_PRICE + '\'' +
                ", SALEORDER_PRICE='" + SALEORDER_PRICE + '\'' +
                '}';
    }

    public String getSALEORDER_GOODS_ID() {
        return SALEORDER_GOODS_ID;
    }

    public void setSALEORDER_GOODS_ID(String SALEORDER_GOODS_ID) {
        this.SALEORDER_GOODS_ID = SALEORDER_GOODS_ID;
    }

    public String getBUYORDER_GOODS_ID() {
        return BUYORDER_GOODS_ID;
    }

    public void setBUYORDER_GOODS_ID(String BUYORDER_GOODS_ID) {
        this.BUYORDER_GOODS_ID = BUYORDER_GOODS_ID;
    }



    public String getBUYORDER_ID() {
        return BUYORDER_ID;
    }

    public void setBUYORDER_ID(String BUYORDER_ID) {
        this.BUYORDER_ID = BUYORDER_ID;
    }



    public String getBUYORDER_NO() {
        return BUYORDER_NO;
    }

    public void setBUYORDER_NO(String BUYORDER_NO) {
        this.BUYORDER_NO = BUYORDER_NO;
    }

    public String getSALEORDER_ID() {
        return SALEORDER_ID;
    }

    public void setSALEORDER_ID(String SALEORDER_ID) {
        this.SALEORDER_ID = SALEORDER_ID;
    }

    public String getSALEORDER_NO() {
        return SALEORDER_NO;
    }

    public void setSALEORDER_NO(String SALEORDER_NO) {
        this.SALEORDER_NO = SALEORDER_NO;
    }



    public String getSALEORDER_NUM() {
        return SALEORDER_NUM;
    }

    public void setSALEORDER_NUM(String SALEORDER_NUM) {
        this.SALEORDER_NUM = SALEORDER_NUM;
    }

    public String getBUYORDER_NUM() {
        return BUYORDER_NUM;
    }

    public void setBUYORDER_NUM(String BUYORDER_NUM) {
        this.BUYORDER_NUM = BUYORDER_NUM;
    }

    public String getBUYORDER_PRICE() {
        return BUYORDER_PRICE;
    }

    public void setBUYORDER_PRICE(String BUYORDER_PRICE) {
        this.BUYORDER_PRICE = BUYORDER_PRICE;
    }

    public String getSALEORDER_PRICE() {
        return SALEORDER_PRICE;
    }

    public void setSALEORDER_PRICE(String SALEORDER_PRICE) {
        this.SALEORDER_PRICE = SALEORDER_PRICE;
    }
}
