package com.test.task;

import com.newtask.finance.AcceptanceBillCreateTask;
import com.newtask.finance.AcceptanceBillPaymentTask;
import com.test.BaseTestWithSpring;
import com.vedeng.erp.buyorder.task.buyorder.BuyorderDataSyncTask;
import com.vedeng.erp.kingdee.task.batch.BatchFlowOrderBatchTask;
import com.vedeng.erp.kingdee.task.batch.OtherWarehouseInBatchTask;
import com.vedeng.erp.kingdee.task.batch.SaleOrderAfterSaleExchangeBatchTask;
import com.xxl.job.core.biz.model.ReturnT;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶任务test
 * @date 2023/6/6 10:50
 */
public class SaleOrderAfterSaleExchangeBatchJobTest extends BaseTestWithSpring {


    @Autowired
    SaleOrderAfterSaleExchangeBatchTask saleOrderAfterSaleExchangeBatchTask;
    @Autowired
    OtherWarehouseInBatchTask otherWarehouseInBatchTask;
    @Autowired
    BuyorderDataSyncTask buyorderDataSyncTask;
    @Autowired
    AcceptanceBillCreateTask acceptanceBillCreateTask;
    @Autowired
    AcceptanceBillPaymentTask acceptanceBillPaymentTask;
    @Autowired
    BatchFlowOrderBatchTask batchFlowOrderBatchTask;

    @Test
    public void testJob() throws Exception {
        // 执行任务
        ReturnT<String> result = saleOrderAfterSaleExchangeBatchTask.execute("{\"beginTime\": \"2023-01-01 00:00:00\",  \"endTime\": \"2023-05-25 00:00:00\"}");
        // 判断任务执行结果
        assert result.getCode() == ReturnT.SUCCESS_CODE;
    }

    @Test
    public void testOtherWarehouseInBatchTask() throws Exception {
        // 执行任务
        ReturnT<String> result = otherWarehouseInBatchTask.execute("{\"beginTime\": \"2023-03-06 17:36:05\",  \"endTime\": \"2023-03-06 17:36:05\"}");
        // 判断任务执行结果
        assert result.getCode() == ReturnT.SUCCESS_CODE;
    }
    @Test
    public void testBuyorderDataSyncTask() throws Exception {
        // 执行任务
        ReturnT<String> result = buyorderDataSyncTask.execute("yesterday");
        // 判断任务执行结果
        assert result.getCode() == ReturnT.SUCCESS_CODE;
    }

    @Test
    public void testAcceptanceBillCreateTask() throws Exception {
        // 执行任务
        ReturnT<String> result = acceptanceBillCreateTask.execute("");
        // 判断任务执行结果
        assert result.getCode() == ReturnT.SUCCESS_CODE;
    }

    @Test
    public void testAcceptanceBillPaymentTask() throws Exception {
        // 执行任务
        ReturnT<String> result = acceptanceBillPaymentTask.execute("");
        // 判断任务执行结果
        assert result.getCode() == ReturnT.SUCCESS_CODE;
    }

    @Test
    public void testBatchFlowOrderBatchTask() throws Exception {
        // 执行任务
        ReturnT<String> result = batchFlowOrderBatchTask.execute("{\"beginTime\":\"2025-02-21 07:00:00\",\n" +
                "    \"endTime\":\"2025-02-23 23:00:00\"}");
        // 判断任务执行结果
        assert result.getCode() == ReturnT.SUCCESS_CODE;
    }
}
