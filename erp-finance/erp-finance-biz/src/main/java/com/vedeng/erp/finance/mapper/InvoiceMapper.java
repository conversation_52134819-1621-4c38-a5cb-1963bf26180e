package com.vedeng.erp.finance.mapper;

import com.vedeng.erp.finance.domain.entity.InvoiceEntity;
import com.vedeng.erp.finance.dto.InvoiceByGoodsDto;
import com.vedeng.erp.finance.dto.InvoiceDto;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description ${end}
 * @date 2022/8/27 16:01
 **/
@Named("newInvoiceMapper")
public interface InvoiceMapper {
    /**
     * delete by primary key
     *
     * @param invoiceId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer invoiceId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(InvoiceEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(InvoiceEntity record);

    /**
     * select by primary key
     *
     * @param invoiceId primary key
     * @return object by primary key
     */
    InvoiceEntity selectByPrimaryKey(Integer invoiceId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(InvoiceEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(InvoiceEntity record);


    /**
     * 发票查询
     *
     * @param invoiceDto relatedId 必要
     * @return List<InvoiceDto>
     */
    List<InvoiceDto> getInvoiceListByRelatedIdGroupByInvoiceNoAndColor(InvoiceDto invoiceDto);

    /**
     * 发票查询 (根据商品维度聚合)
     *
     * @param invoiceDto 查询对象 relatedId type
     * @return List<InvoiceByGoodsDto>
     */
    List<InvoiceByGoodsDto> getInvoiceListByRelatedIdGroupByInvoiceNoAndColorAndOrderGoodsItemId(InvoiceDto invoiceDto);

    /**
     * 查询最新冲销发票号
     *
     * @param invoiceNo
     * @return
     * <AUTHOR>
     */
    String queryLatestInvoiceNo(String invoiceNo);


    /**
     * 根据业务 relatedId 获取采购费用单的所有的蓝字有效和蓝字作废票，并聚合金额
     *
     * @param relatedId 业务关联主键
     * @return List<InvoiceDto>
     */
    List<InvoiceDto> findByRelatedIdGroupByInvoiceNo(@Param("relatedId") Integer relatedId);

    /**
     * 根据售后单id和开票类型查询发票信息
     *
     * @param afterSalesId 售后订单id
     * @param type         开票类型
     * @return List<InvoiceDto>
     */
    List<InvoiceDto> getInvoiceListByAfterSalesId(@Param("afterSalesId") Integer afterSalesId, @Param("type") Integer type);

    /**
     * 更具数据查询一张票信息
     * @param param
     * @return
     */
    List<InvoiceDto> getOneInvoiceByNoCodeType(InvoiceDto param);
    /**
     * 根据商品id查询该商品已录票的数量之和
     * @param goodsId 商品id
     * @return 该商品已录票的数量之和
     */
    BigDecimal calculateAlreadyInvoiceNum(@Param("goodsId") Integer goodsId);

    /**
     * 查询某个商品在所有红字有效票中的  数量之和
     *
     * @param invoiceIdList invoiceIdList
     * @param goodsId       detailgoodsId
     * @return InvoiceDetailDto
     */
    BigDecimal getAlreadyRedInvoiceNum(@Param("invoiceIdList") List<Integer> invoiceIdList, @Param("goodsId") Integer goodsId);

    /**
     * 根据发票号，订单id，录入发票业务类型查询发票总额
     * <AUTHOR>
     * @param invoiceNo 发票号
     * @param relatedId 关联订单id
     * @param type 503 采购开票 504 售后开票 505 销售开票 4126 采购费用开票
     * @return
     */
    BigDecimal querySumAmountByInvoiceNo(@Param("invoiceNo")String invoiceNo,@Param("relatedId")Integer relatedId, @Param("type")Integer type);

    /**
     * 根据发票号，订单id，录入发票业务类型查询所有满足冲销的蓝字票ID
     * <AUTHOR>
     * @param invoiceNo
     * @param relatedId
     * @param type
     * @return
     */
    List<Integer> queryAllInvoiceIdByInvoiceNo(@Param("invoiceNo")String invoiceNo,@Param("relatedId")Integer relatedId, @Param("type")Integer type);

    /**
     * 根据订单id查询收票金额之和（减去蓝字作废和红字票的金额）
     *
     * @param relatedId 订单id
     * @param type      开票申请类型
     * @return 收票金额之和
     */
    BigDecimal getInvoiceTotalAmountByRelatedId(@Param("relatedId") Integer relatedId, @Param("type") Integer type);

    InvoiceEntity queryInvoiceByInvoiceNoAndRelatedId(@Param("invoiceNo")String invoiceNo,@Param("relatedId")Integer relatedId, @Param("type")Integer type);

    /**
     * 根据采购单id，发票号查询invoiceId
     * <AUTHOR>
     * @param buyorderId
     * @param invoiceNo
     * @return
     */
    Integer queryInvoiceIdByInfo(@Param("buyorderId") Integer buyorderId,@Param("invoiceNo") String invoiceNo);

    InvoiceEntity queryReceiptInvoiceRecord(@Param("buyorderId") Integer buyorderId,@Param("invoiceNo") String invoiceNo);

    /**
     * 获取采购发票有效发票信息
     *
     * @param invoiceNo
     * @param invoiceCode
     * @return
     */
    List<Integer> getValidBuyOrderInvoiceIdsByInvoiceNoAndCode(@Param("invoiceNo") String invoiceNo,
                                                               @Param("invoiceCode") String invoiceCode,
                                                               @Param("orderId") Integer orderId);

    /**
     * 根据发票号，商品明细id，订单号查询仅退票蓝票id
     * <AUTHOR> @param invoiceNo
     * @param detailGoodsList
     * @param orderId
     * @return
     */
    List<Integer> queryValidInvoiceIdByGoodsAndInvoiceNo(@Param("invoiceNo")String invoiceNo,
                                                         @Param("detailGoodsList")List<Integer> detailGoodsList,
                                                         @Param("orderId")Integer orderId);

    List<String> getRedValidInvoiceListByExpenseId(@Param("buyorderExpenseId")Integer buyorderExpenseId);

    /**
     * 根据费用单id 查询
     * @param buyorderExpenseId
     * @return
     */
    List<InvoiceDto> getBlueInvoiceByExpenseId(Integer buyorderExpenseId);

    /**
     * 获取销售订单发发票信息
     *
     * @param relatedId 销售单id
     * @return List<InvoiceEntity>
     */
    List<InvoiceEntity> findBySaleOrderRelatedId(@Param("relatedId") Integer relatedId);


    /**
     * 根据付款申请查有效票
     * @param invoiceNo 发票号码
     * @return
     */
    List<InvoiceEntity> findByColorTypeAndEnableAndInvoiceApplyId(@Param("invoiceNo")String invoiceNo);


    BigDecimal getSaleOpenInvoiceAmount(@Param(value="relatedId")Integer relatedId);

    List<InvoiceEntity> findByInvoiceApplyId(@Param("invoiceApplyId")Integer invoiceApplyId);

    List<InvoiceDto> findSaleorderByInvoiceApplyId(@Param("invoiceApplyId") Integer invoiceApplyId,@Param("invoiceNo") String invocieNo);

    /**
     * 根据发票申请id查询发票信息
     * @param invoiceApplyId 发票申请id
     * @param invocieNo 发票号
     * @return
     */
    List<InvoiceDto> findAtByInvoiceApplyId(@Param("invoiceApplyId") Integer invoiceApplyId,@Param("invoiceNo") String invocieNo);

    /**
     * 根据发票号查询发票信息
     *
     * @param invoiceNo
     * @return
     */
    List<InvoiceEntity> findSaleBlueInvoiceByInvoiceNo(@Param("invoiceNo") String invoiceNo);

    /**
     * 根据发票号查询发票信息
     *
     * @param invoiceNo 发票号
     * @param relatedId 关联订单id
     * @param type      开票申请类型
     * @param colorType 红蓝字 1红2蓝
     * @return
     */
    List<InvoiceEntity> findByInvoiceNoAndRelatedIdAndTypeAndColorType(@Param("invoiceNo") String invoiceNo, @Param("relatedId") Integer relatedId, @Param("type") Integer type, @Param("colorType") Integer colorType);

    List<InvoiceEntity> findByInvoiceNoAndColorTypeAndInvoiceProperty(@Param("invoiceNo")String invoiceNo);


    /**
     * 根据发票号查询1条发票信息
     * @param invoiceNo 发票号
     * @return  InvoiceEntity 发票
     */
    InvoiceEntity findByInvoiceNoOne(String invoiceNo);


    /**
     * 根据发票号查询1条发票信息
     * @param invoiceNo 发票号
     * @return  InvoiceEntity 发票
     */
    InvoiceEntity findByInvoiceNoAndTag(@Param(value = "invoiceNo") String invoiceNo, @Param(value = "tag") Integer tag);

    /**
     * 根据发票号查询是否会存在多条发票信息且TAG不同
     * @param invoiceNo
     * @return
     */
    List<Integer> getInvoiceTagByInvoiceNo(String invoiceNo);

    /**
     * 根据发票号查询原始单和售后单号
     * @param invoiceId
     * @return InvoiceDto
     */
    InvoiceDto selectOrderAndAfterSale(Integer invoiceId);

    /**
     * 根据发票号更新发票表的OSS_FILE_URL和RESOURCE_ID字段
     */
    void updateInvoiceOssFileUrlAndResourceId(@Param("invoiceNo") String invoiceNo, @Param("ossFileUrl") String ossFileUrl, @Param("resourceId") String resourceId);

    /**
     * 计算发票金额
     * @param invoiceDto 发票信息
     * @return BigDecimal 发票金额
     */
    BigDecimal calAmount(InvoiceDto invoiceDto);

    /**
     * 财务-售后-安调-开票信息
     * @param afterSalesId
     * @param type
     * @param tag
     * @return
     */
    List<InvoiceDto> getAfterInvoiceList(@Param(value = "afterSalesId") Integer afterSalesId,
                                                  @Param(value = "type") Integer type,
                                                  @Param(value = "tag") Integer tag);

    int updateInvoiceOssUrlByOcr(@Param("invoiceNo") String invoiceNo, @Param("ossUrl") String ossUrl,@Param("invoiceHref") String invoiceHref
            ,@Param("resourceId") String resourceId,@Param("modTime") long modTime,@Param("tag") Integer tag);

    /**
     * 销售订单关联蓝票金额
     * @param relatedId
     * @return
     */
    BigDecimal getSalesOrderBlueInvoiceAmount(@Param("relatedId") Integer relatedId);

    /**
     * 销售订单关联红票金额
     * @param relatedId
     * @return
     */
    BigDecimal getSalesOrderRedInvoiceAmount(@Param("relatedId") Integer relatedId);
}
