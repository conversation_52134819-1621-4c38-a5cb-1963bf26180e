Vue.component('crm-slide-dialog', {
    template: `<div class="crm-slide-dialog-wrap" ref="wrap" :class="{show: isShow}" :style="'z-index:' + (isShow ? zindex : '-1')">
        <div class="crm-slide-dialog-mask" @click="handlerMaskClick"></div>
        <div class="crm-slide-dialog-cnt" :class="{'drop-down': type==='down'}">
            <div class="crm-slide-dialog-header" v-if="title">
                <div class="slide-dialog-header-option" @click="handlerTopOption">{{ topOptionTxt }}</div>
                <div class="slide-dialog-header-title">{{ title }}</div>
                <div class="slide-dialog-header-close" v-if="closeable" @click="hide">
                    <i class="vd-ui_icon icon-delete"></i>
                </div>
            </div>
            <div ref="dialogContent">
                <template v-if="refresh">
                    <div v-if="isShow">
                        <slot></slot>
                    </div>
                </template>
                <template v-else>
                    <slot></slot>
                </template>
            </div>
        </div>
    </div>`,
    props: {
        type: {
            type: String,
            default: 'up', //'up' or 'down' 向上或者向下弹出，默认向上
        },
        zindex: {
            type: String,
            default: '999'
        },
        //点击遮罩隐藏弹层
        maskHide: {
            type: Boolean,
            default: true
        },
        isShow: {
            type: Boolean,
            default: false
        },
        refresh: {
            type: Boolean,
            default: false
        },
        title: {
            type: String,
            default: '',
        },
        closeable: {
            type: Boolean,
            default: true,
        },
        topOptionTxt: {
            type: String,
            default: '',
        }
    },
    watch: {
        isShow() {
            if(this.isShow) {
                document.body.style.overflow = 'hidden'
            } else {
                document.body.style.overflow = ''
            }
        }
    },
    data() {
        return {};
    },
    computed: {

    },
    mounted() {
        
    },
    methods: {
        show() {
            document.body.append(this.$refs.wrap);
            setTimeout(() => {
                this.isShow = true;
            }, 100)
        },
        hide() {
            this.isShow = false;
        },
        handlerMaskClick() {
            if(this.maskHide) {
                this.isShow = false;
                this.$emit('update:isShow', false);
                this.$emit('hidn');
            }
        },
        handlerTopOption() {
            this.$emit('topOption')
        }
    }
})