<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.flash.dao.PrepareStockLogMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.flash.model.PrepareStockLog" >
    <id column="SAFE_STOCK_ID" property="safeStockId" jdbcType="INTEGER" />
    <result column="PREPARE_ID" property="prepareId" jdbcType="INTEGER" />
    <result column="SKU_ID" property="skuId" jdbcType="INTEGER" />
    <result column="SAFE_STOCK" property="safeStock" jdbcType="INTEGER" />
    <result column="ORIGIN_SAFE_STOCK" property="originSafeStock" jdbcType="INTEGER" />
    <result column="OPERATE_TYPE" property="operateType" jdbcType="TINYINT" />
    <result column="OPERATE_USER_ID" property="operateUserId" jdbcType="INTEGER" />
    <result column="OPERATE_USER_NAME" property="operateUserName" jdbcType="VARCHAR" />
    <result column="OPERATE_TIME" property="operateTime" jdbcType="BIGINT" />
    <result column="OPERATE_REASON" property="operateReason" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    SAFE_STOCK_ID, PREPARE_ID, SKU_ID, SAFE_STOCK, ORIGIN_SAFE_STOCK, OPERATE_TYPE, OPERATE_USER_ID,
    OPERATE_USER_NAME, OPERATE_TIME, OPERATE_REASON
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from T_PREPARE_STOCK_LOG
    where SAFE_STOCK_ID = #{safeStockId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from T_PREPARE_STOCK_LOG
    where SAFE_STOCK_ID = #{safeStockId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.flash.model.PrepareStockLog" >
    insert into T_PREPARE_STOCK_LOG (SAFE_STOCK_ID, PREPARE_ID, SKU_ID,
      SAFE_STOCK, ORIGIN_SAFE_STOCK, OPERATE_TYPE,
      OPERATE_USER_ID, OPERATE_USER_NAME, OPERATE_TIME,
      OPERATE_REASON)
    values (#{safeStockId,jdbcType=INTEGER}, #{prepareId,jdbcType=INTEGER}, #{skuId,jdbcType=INTEGER},
      #{safeStock,jdbcType=INTEGER}, #{originSafeStock,jdbcType=INTEGER}, #{operateType,jdbcType=TINYINT},
      #{operateUserId,jdbcType=INTEGER}, #{operateUserName,jdbcType=VARCHAR}, #{operateTime,jdbcType=BIGINT},
      #{operateReason,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.flash.model.PrepareStockLog" >
    insert into T_PREPARE_STOCK_LOG
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="safeStockId != null" >
        SAFE_STOCK_ID,
      </if>
      <if test="prepareId != null" >
        PREPARE_ID,
      </if>
      <if test="skuId != null" >
        SKU_ID,
      </if>
      <if test="safeStock != null" >
        SAFE_STOCK,
      </if>
      <if test="originSafeStock != null" >
        ORIGIN_SAFE_STOCK,
      </if>
      <if test="operateType != null" >
        OPERATE_TYPE,
      </if>
      <if test="operateUserId != null" >
        OPERATE_USER_ID,
      </if>
      <if test="operateUserName != null" >
        OPERATE_USER_NAME,
      </if>
      <if test="operateTime != null" >
        OPERATE_TIME,
      </if>
      <if test="operateReason != null" >
        OPERATE_REASON,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="safeStockId != null" >
        #{safeStockId,jdbcType=INTEGER},
      </if>
      <if test="prepareId != null" >
        #{prepareId,jdbcType=INTEGER},
      </if>
      <if test="skuId != null" >
        #{skuId,jdbcType=INTEGER},
      </if>
      <if test="safeStock != null" >
        #{safeStock,jdbcType=INTEGER},
      </if>
      <if test="originSafeStock != null" >
        #{originSafeStock,jdbcType=INTEGER},
      </if>
      <if test="operateType != null" >
        #{operateType,jdbcType=TINYINT},
      </if>
      <if test="operateUserId != null" >
        #{operateUserId,jdbcType=INTEGER},
      </if>
      <if test="operateUserName != null" >
        #{operateUserName,jdbcType=VARCHAR},
      </if>
      <if test="operateTime != null" >
        #{operateTime,jdbcType=BIGINT},
      </if>
      <if test="operateReason != null" >
        #{operateReason,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.flash.model.PrepareStockLog" >
    update T_PREPARE_STOCK_LOG
    <set >
      <if test="prepareId != null" >
        PREPARE_ID = #{prepareId,jdbcType=INTEGER},
      </if>
      <if test="skuId != null" >
        SKU_ID = #{skuId,jdbcType=INTEGER},
      </if>
      <if test="safeStock != null" >
        SAFE_STOCK = #{safeStock,jdbcType=INTEGER},
      </if>
      <if test="originSafeStock != null" >
        ORIGIN_SAFE_STOCK = #{originSafeStock,jdbcType=INTEGER},
      </if>
      <if test="operateType != null" >
        OPERATE_TYPE = #{operateType,jdbcType=TINYINT},
      </if>
      <if test="operateUserId != null" >
        OPERATE_USER_ID = #{operateUserId,jdbcType=INTEGER},
      </if>
      <if test="operateUserName != null" >
        OPERATE_USER_NAME = #{operateUserName,jdbcType=VARCHAR},
      </if>
      <if test="operateTime != null" >
        OPERATE_TIME = #{operateTime,jdbcType=BIGINT},
      </if>
      <if test="operateReason != null" >
        OPERATE_REASON = #{operateReason,jdbcType=VARCHAR},
      </if>
    </set>
    where SAFE_STOCK_ID = #{safeStockId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.flash.model.PrepareStockLog" >
    update T_PREPARE_STOCK_LOG
    set PREPARE_ID = #{prepareId,jdbcType=INTEGER},
      SKU_ID = #{skuId,jdbcType=INTEGER},
      SAFE_STOCK = #{safeStock,jdbcType=INTEGER},
      ORIGIN_SAFE_STOCK = #{originSafeStock,jdbcType=INTEGER},
      OPERATE_TYPE = #{operateType,jdbcType=TINYINT},
      OPERATE_USER_ID = #{operateUserId,jdbcType=INTEGER},
      OPERATE_USER_NAME = #{operateUserName,jdbcType=VARCHAR},
      OPERATE_TIME = #{operateTime,jdbcType=BIGINT},
      OPERATE_REASON = #{operateReason,jdbcType=VARCHAR}
    where SAFE_STOCK_ID = #{safeStockId,jdbcType=INTEGER}
  </update>

  <select id="getStockLogListByRegularId" parameterType="java.lang.Integer" resultType="com.vedeng.flash.dto.PrepareStockLogDto">
    select
    FROM_UNIXTIME(CONVERT(OPERATE_TIME/1000,SIGNED),'%Y-%m-%d %H:%i:%s') AS operateTimeStr,
    <include refid="Base_Column_List" />
    from T_PREPARE_STOCK_LOG
    where PREPARE_ID = #{regularId,jdbcType=INTEGER}
    order by OPERATE_TIME desc
  </select>
  <select id="getPrepareLogListByRegularIdListPage" resultType="com.vedeng.flash.dto.RegularOperateLogDto" parameterType="Map">
    SELECT
        A.LOG_ID,
        B.SALEORDER_NO,
        FROM_UNIXTIME(A.OPERATE_TIME/1000,'%Y-%m-%d %H:%i:%s') AS OPERATE_TIME_STRING,
        C.NUM AS BUY_NUM,
		A.SALEORDER_ID
    FROM
        T_REGULAR_OPERATE_LOG AS A
        LEFT JOIN T_SALEORDER AS B ON A.SALEORDER_ID=B.SALEORDER_ID
				LEFT JOIN T_SALEORDER_GOODS AS C ON C.SALEORDER_ID =A.SALEORDER_ID AND C.SKU=A.SKU_NO
    WHERE
        OPERATE_TYPE = 4
        AND REGULAR_ID = #{regularId,jdbcType=INTEGER}
   </select>
  <select id="getDeleteLogListByRegularId" resultType="com.vedeng.flash.dto.RegularOperateLogDto">
    SELECT
        LOG_ID,
        FROM_UNIXTIME(OPERATE_TIME/1000,
        '%Y-%m-%d %H:%i:%s') AS OPERATE_TIME_STRING,
        OPERATE_REASON
    FROM
        T_REGULAR_OPERATE_LOG
    WHERE
        OPERATE_TYPE = 0
        AND REGULAR_ID = #{regularId,jdbcType=INTEGER}
  </select>
  <select id="getDoNotPrepareLogListByRegularId" resultType="com.vedeng.flash.dto.RegularOperateLogDto">
    SELECT
        LOG_ID,
        FROM_UNIXTIME(OPERATE_TIME/1000,
        '%Y-%m-%d %H:%i:%s') AS OPERATE_TIME_STRING,
        OPERATE_REASON
    FROM
        T_REGULAR_OPERATE_LOG
    WHERE
        OPERATE_TYPE = 3
        AND REGULAR_ID = #{regularId,jdbcType=INTEGER}
  </select>
  <select id="getRepalceLogListByRegularId" resultType="com.vedeng.flash.dto.RegularOperateLogDto">
   SELECT
        A.LOG_ID,
        FROM_UNIXTIME(B.OPERATE_TIME/1000,
        '%Y-%m-%d %H:%i:%s') AS OPERATE_TIME_STRING,
        B.OPERATE_REASON,
        B.SKU_NO,
        B.SKU_NAME,
        B.STOCK_WARN AS STOCK_WARN,
        CONCAT(B.SAFE_STOCK,
        B.UNIT) AS SAFE_STOCK_STRING,
        CONCAT(B.STOCK,
        B.UNIT) AS STOCK_STRING,
        CONCAT(B.SUPPLEMENT_STOCK,
        B.UNIT) AS PROPOSE_PREPARE_STOCK_STRING
    FROM
        T_REGULAR_OPERATE_LOG AS B
    LEFT JOIN
        T_REGULAR_OPERATE_LOG AS A
            ON A.PARENT_ID = B.LOG_ID
    WHERE
        B.OPERATE_TYPE = 1
				AND B.PARENT_ID IS NULL
        AND B.REGULAR_ID =  #{regularId,jdbcType=INTEGER}
  </select>
  <select id="getConversionLogListByRegularId" resultType="com.vedeng.flash.dto.RegularOperateLogDto">
    SELECT
        A.LOG_ID,
        FROM_UNIXTIME(B.OPERATE_TIME/1000,
        '%Y-%m-%d %H:%i:%s') AS OPERATE_TIME_STRING,
        B.OPERATE_REASON,
        B.SKU_NO,
        B.SKU_NAME,
        B.STOCK_WARN AS STOCK_WARN,
        CONCAT(B.SAFE_STOCK,
        B.UNIT) AS SAFE_STOCK_STRING,
        CONCAT(B.STOCK,
        B.UNIT) AS STOCK_STRING,
        CONCAT(B.SUPPLEMENT_STOCK,
        B.UNIT) AS PROPOSE_PREPARE_STOCK_STRING
    FROM
        T_REGULAR_OPERATE_LOG AS B
    LEFT JOIN
        T_REGULAR_OPERATE_LOG AS A
            ON A.PARENT_ID = B.LOG_ID
    WHERE
        B.OPERATE_TYPE = 2
				AND B.PARENT_ID IS NULL
        AND B.REGULAR_ID =  #{regularId,jdbcType=INTEGER}
  </select>
</mapper>
