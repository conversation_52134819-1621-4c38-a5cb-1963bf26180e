package com.test.kingdee;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.domain.command.KingDeeExpressCostCommand;
import com.vedeng.erp.kingdee.dto.KingDeeExpressCostDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeExpressCostCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeExpressCostConvertor;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeExpressCostMapper;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.domain.command.UpdateExtCommand;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * 盘盈单
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring.xml", "classpath:spring-mybatis.xml"})
public class TestKingDeeExpressUpdateCost {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Resource
    private KingDeeExpressCostCommandConvertor commandConvertor;

    @Resource
    private KingDeeExpressCostConvertor expressCostConvertor;

    @Autowired
    private KingDeeExpressCostMapper expressCostMapper;


    @Test
    public void kingDeeExpressCost() {
        String sa = "YSD001";
        KingDeeExpressCostDto kingDeeExpressCostDto = new KingDeeExpressCostDto();
        kingDeeExpressCostDto.setFid("100043");
        kingDeeExpressCostDto.setFQzokOrgId("101");
        kingDeeExpressCostDto.setFQzokYsddh(sa);
        kingDeeExpressCostDto.setFQzokGsywdh(sa);
        kingDeeExpressCostDto.setFQzokYwlx("销售订单");
        kingDeeExpressCostDto.setFQzokCrkdh("CK0944443");
        kingDeeExpressCostDto.setFQzokKddh("SF001");
        kingDeeExpressCostDto.setFQzokWlbm("V111508");
        kingDeeExpressCostDto.setFQzokXlh("BT2233");
        kingDeeExpressCostDto.setFQzokPch("P11");
        kingDeeExpressCostDto.setFQzokFhsl("3");
        kingDeeExpressCostDto.setFQzokCb("20.0");
        kingDeeExpressCostDto.setFQzokSjr("网文");
        kingDeeExpressCostDto.setFQzokDh("19029837632");
        kingDeeExpressCostDto.setFQzokDz("江苏省南京市");
        kingDeeExpressCostDto.setFQzokBddjbh("UYE09133");
        kingDeeExpressCostDto.setFQzokSfsc("TRUE");
        kingDeeExpressCostDto.setFQzokSfjrcb("Y");

        KingDeeExpressCostCommand command = commandConvertor.toCommand(kingDeeExpressCostDto);


        System.out.println("入参："+JSON.toJSONString(command));
        String formId = "QZOK_KDCB";
        String ids = kingDeeExpressCostDto.getFid();
        kingDeeBaseApi.update(new UpdateExtCommand<>(command, kingDeeExpressCostDto.getFormId()));
//        kingDeeBaseApi.unAudit(new OperateExtCommand(formId,ids,KingDeeConstant.ORG_ID.toString(), null));
//        kingDeeBaseApi.delete(new OperateExtCommand(formId,ids,KingDeeConstant.ORG_ID.toString(), null));
//        if (CollUtil.isNotEmpty(successEntities)) {
//            SuccessEntity successEntity = CollUtil.getFirst(successEntities);
//            System.out.println("数据库入参："+JSON.toJSONString(inventoryProfitConvertor.toEntity(dto)));
//            kingDeeInventoryProfitMapper.insertSelective(inventoryProfitConvertor.toEntity(dto));
//        }
    }
}
