package com.vedeng.crm.business.quote.mapper;

import com.vedeng.crm.business.quote.domain.dto.CleanConsultationContentDto;
import com.vedeng.crm.business.quote.domain.dto.ConsultationReportUpdateEntity;
import com.vedeng.crm.business.quote.domain.dto.QuoteGoodsUpdateLockDto;
import com.vedeng.crm.business.quote.domain.entity.CrmQuoteorderGoods;
import com.vedeng.crm.business.quote.domain.entity.CrmQuoteorderGoodsNoSku;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CrmQuoteorderGoodsMapper {
    int deleteByPrimaryKey(Integer quoteorderGoodsId);

    int insertSelective(CrmQuoteorderGoods record);

    int updatePriceByPrimaryKey(CrmQuoteorderGoods record);

    int insertQuoteOrderGoodsNoSku(CrmQuoteorderGoodsNoSku record);

    /**
     * 更新自定义商品明细
     * @param record
     * @return
     */
    int updateQuoteOrderGoodsNoSku(CrmQuoteorderGoodsNoSku record);

    CrmQuoteorderGoods selectByPrimaryKey(Integer quoteorderGoodsId);

    List<CrmQuoteorderGoods> selectByQuoteorderId(Integer quoteorderId);

    List<CrmQuoteorderGoods> selectByQuoteNeedsIdList(@Param(value="quoteorderNeedsIdList") List<Long> quoteorderNeedsIdList);



    int updateByPrimaryKeySelective(CrmQuoteorderGoods record);

    int updateDeliveryCycle(QuoteGoodsUpdateLockDto updateRequestDto);
    int updatePrice(QuoteGoodsUpdateLockDto updateRequestDto);
    int updateReportStatus(QuoteGoodsUpdateLockDto updateRequestDto);

    int updateNum(QuoteGoodsUpdateLockDto updateRequestDto);

    int updateByPrimaryKey(CrmQuoteorderGoods record);

    void batchUpdateConsultationReport(ConsultationReportUpdateEntity update);

    void cleanConsultationContent(CleanConsultationContentDto cleanConsultationContentDto);
}
