package com.vedeng.order.model.vo;

import com.vedeng.order.model.SkuServiceTag;

import java.math.BigDecimal;
import java.util.List;

public class OrderGoodsData {
    private Integer saleorderGoodsId;
    private String skuNo;
    private Integer productNum;//商品数量
    private BigDecimal jxSalePrice;//销售价
    private BigDecimal marketMomey;//市场价
    private String storeRemarks;//商家备注
    private String deliveryCycle;//货期


    private String orderNo;
    private Integer goodsNum;
    private BigDecimal salesMoney;
    private String goodsName;
    private String goodsSku;
    private String goodsModel;
    private String specs;
    private String goodsUri;

    /**
     * 是否直发 0否 1是
     */
    private Integer deliveryDirect;
    // 赠品标识
    private String giftFlag;

    //0-未使用 1-已使用
    private Integer isCoupons;

    //商品总额
    private BigDecimal skuAmount;

    //优惠后的价格
    private BigDecimal couponPrice;

    private List<SkuServiceTag> tagList;

    private String jdSaleorderNo;


    /**
     * 秒杀标识
     */
    private String limitFlag;
    /**
     * 秒杀价格
     */
    private BigDecimal limitPrice;

    /**
     * 是否现货现价直购(0否，1是)
     */
    private Integer isDirectPurchase;

    /**
     * 是否包含安调(0否，1是)
     */
    private Integer haveInstallation;

    /**
     * 现货现价直购价格
     */
    private BigDecimal directPurchasePrice;
    
    /**
     * 商品备注
     */
    private String goodsComments;

    /**
     * 内部备注
     */
    private String insideComments;
    
    /**
     * 直发备注
     */
    private String delilveryDirectComments;
    
    /**贝登销售明细ID*/
    private Integer vdSaleorderGoodsId;
    
    

    public String getGoodsComments() {
		return goodsComments;
	}

	public void setGoodsComments(String goodsComments) {
		this.goodsComments = goodsComments;
	}

	public String getInsideComments() {
		return insideComments;
	}

	public void setInsideComments(String insideComments) {
		this.insideComments = insideComments;
	}

	public String getDelilveryDirectComments() {
		return delilveryDirectComments;
	}

	public void setDelilveryDirectComments(String delilveryDirectComments) {
		this.delilveryDirectComments = delilveryDirectComments;
	}

	public Integer getVdSaleorderGoodsId() {
		return vdSaleorderGoodsId;
	}

	public void setVdSaleorderGoodsId(Integer vdSaleorderGoodsId) {
		this.vdSaleorderGoodsId = vdSaleorderGoodsId;
	}

	public Integer getIsDirectPurchase() {
        return isDirectPurchase;
    }

    public void setIsDirectPurchase(Integer isDirectPurchase) {
        this.isDirectPurchase = isDirectPurchase;
    }

    public Integer getHaveInstallation() {
        return haveInstallation;
    }

    public void setHaveInstallation(Integer haveInstallation) {
        this.haveInstallation = haveInstallation;
    }

    public BigDecimal getDirectPurchasePrice() {
        return directPurchasePrice;
    }

    public void setDirectPurchasePrice(BigDecimal directPurchasePrice) {
        this.directPurchasePrice = directPurchasePrice;
    }

    public String getLimitFlag() {
        return limitFlag;
    }

    public void setLimitFlag(String limitFlag) {
        this.limitFlag = limitFlag;
    }

    public BigDecimal getLimitPrice() {
        return limitPrice;
    }

    public void setLimitPrice(BigDecimal limitPrice) {
        this.limitPrice = limitPrice;
    }

    public String getJdSaleorderNo() {
        return jdSaleorderNo;
    }

    public void setJdSaleorderNo(String jdSaleorderNo) {
        this.jdSaleorderNo = jdSaleorderNo;
    }

    public String getGiftFlag() {
        return giftFlag;
    }

    public void setGiftFlag(String giftFlag) {
        this.giftFlag = giftFlag;
    }

    public List<SkuServiceTag> getTagList() {
        return tagList;
    }

    public void setTagList(List<SkuServiceTag> tagList) {
        this.tagList = tagList;
    }


    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getGoodsNum() {
        return goodsNum;
    }

    public void setGoodsNum(Integer goodsNum) {
        this.goodsNum = goodsNum;
    }

    public BigDecimal getSalesMoney() {
        return salesMoney;
    }

    public void setSalesMoney(BigDecimal salesMoney) {
        this.salesMoney = salesMoney;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsSku() {
        return goodsSku;
    }

    public void setGoodsSku(String goodsSku) {
        this.goodsSku = goodsSku;
    }

    public String getGoodsModel() {
        return goodsModel;
    }

    public void setGoodsModel(String goodsModel) {
        this.goodsModel = goodsModel;
    }

    public String getSpecs() {
        return specs;
    }

    public void setSpecs(String specs) {
        this.specs = specs;
    }

    public String getGoodsUri() {
        return goodsUri;
    }

    public void setGoodsUri(String goodsUri) {
        this.goodsUri = goodsUri;
    }

    public String getStoreRemarks() {
		return storeRemarks;
	}

	public void setStoreRemarks(String storeRemarks) {
		this.storeRemarks = storeRemarks;
	}

	public BigDecimal getMarketMomey() {
		return marketMomey;
	}

	public void setMarketMomey(BigDecimal marketMomey) {
		this.marketMomey = marketMomey;
	}

	public String getSkuNo() {
        return skuNo;
    }

    public void setSkuNo(String skuNo) {
        this.skuNo = skuNo;
    }

    public Integer getProductNum() {
        return productNum;
    }

    public void setProductNum(Integer productNum) {
        this.productNum = productNum;
    }

    public BigDecimal getJxSalePrice() {
        return jxSalePrice;
    }

    public void setJxSalePrice(BigDecimal jxSalePrice) {
        this.jxSalePrice = jxSalePrice;
    }

    public Integer getIsCoupons() {
        return isCoupons;
    }

    public void setIsCoupons(Integer isCoupons) {
        this.isCoupons = isCoupons;
    }

    @Override
	public String toString() {
		return "OrderGoodsData [skuNo=" + skuNo + ", productNum=" + productNum + "]";
	}

    public Integer getSaleorderGoodsId() {
        return saleorderGoodsId;
    }

    public void setSaleorderGoodsId(Integer saleorderGoodsId) {
        this.saleorderGoodsId = saleorderGoodsId;
    }

    public BigDecimal getSkuAmount() {
        return skuAmount;
    }

    public void setSkuAmount(BigDecimal skuAmount) {
        this.skuAmount = skuAmount;
    }

    public BigDecimal getCouponPrice() {
        return couponPrice;
    }

    public void setCouponPrice(BigDecimal couponPrice) {
        this.couponPrice = couponPrice;
    }

    public String getDeliveryCycle() {
        return deliveryCycle;
    }

    public void setDeliveryCycle(String deliveryCycle) {
        this.deliveryCycle = deliveryCycle;
    }

    public Integer getDeliveryDirect() {
        return deliveryDirect;
    }

    public void setDeliveryDirect(Integer deliveryDirect) {
        this.deliveryDirect = deliveryDirect;
    }
}
