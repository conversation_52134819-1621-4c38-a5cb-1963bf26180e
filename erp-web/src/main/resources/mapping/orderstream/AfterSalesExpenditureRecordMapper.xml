<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.orderstream.aftersales.dao.AfterSalesExpenditureRecordMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.orderstream.aftersales.model.AfterSalesExpenditureRecord">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Oct 21 16:35:45 CST 2021.
    -->
    <id column="RECORD_ID" jdbcType="INTEGER" property="recordId" />
    <result column="AFTER_SALES_ID" jdbcType="INTEGER" property="afterSalesId" />
    <result column="TYPE" jdbcType="INTEGER" property="type" />
    <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
    <result column="ORG_ID" jdbcType="INTEGER" property="orgId" />
    <result column="PAYER" jdbcType="VARCHAR" property="payer" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="MODE_TIME" jdbcType="BIGINT" property="modeTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="IS_DELETE" jdbcType="BIT" property="isDelete" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Oct 21 16:35:45 CST 2021.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Oct 21 16:35:45 CST 2021.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Oct 21 16:35:45 CST 2021.
    -->
    RECORD_ID, AFTER_SALES_ID, `TYPE`, AMOUNT, ORG_ID, PAYER, ADD_TIME, MODE_TIME, CREATOR, 
    UPDATER, IS_DELETE, REMARK
  </sql>
  <select id="selectByExample" parameterType="com.vedeng.orderstream.aftersales.model.AfterSalesExpenditureRecordExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Oct 21 16:35:45 CST 2021.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from T_AFTER_SALES_EXPENDITURE_RECORD
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Oct 21 16:35:45 CST 2021.
    -->
    select 
    <include refid="Base_Column_List" />
    from T_AFTER_SALES_EXPENDITURE_RECORD
    where RECORD_ID = #{recordId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Oct 21 16:35:45 CST 2021.
    -->
    delete from T_AFTER_SALES_EXPENDITURE_RECORD
    where RECORD_ID = #{recordId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.orderstream.aftersales.model.AfterSalesExpenditureRecordExample">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Oct 21 16:35:45 CST 2021.
    -->
    delete from T_AFTER_SALES_EXPENDITURE_RECORD
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.orderstream.aftersales.model.AfterSalesExpenditureRecord">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Oct 21 16:35:45 CST 2021.
    -->
    <selectKey keyProperty="recordId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_AFTER_SALES_EXPENDITURE_RECORD (AFTER_SALES_ID, `TYPE`, AMOUNT, 
      ORG_ID, PAYER, ADD_TIME, 
      MODE_TIME, CREATOR, UPDATER, 
      IS_DELETE, REMARK)
    values (#{afterSalesId,jdbcType=INTEGER}, #{type,jdbcType=INTEGER}, #{amount,jdbcType=DECIMAL}, 
      #{orgId,jdbcType=INTEGER}, #{payer,jdbcType=VARCHAR}, #{addTime,jdbcType=BIGINT}, 
      #{modeTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, 
      #{isDelete,jdbcType=BIT}, #{remark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.orderstream.aftersales.model.AfterSalesExpenditureRecord">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Oct 21 16:35:45 CST 2021.
    -->
    <selectKey keyProperty="recordId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_AFTER_SALES_EXPENDITURE_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="afterSalesId != null">
        AFTER_SALES_ID,
      </if>
      <if test="type != null">
        `TYPE`,
      </if>
      <if test="amount != null">
        AMOUNT,
      </if>
      <if test="orgId != null">
        ORG_ID,
      </if>
      <if test="payer != null">
        PAYER,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modeTime != null">
        MODE_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="afterSalesId != null">
        #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="payer != null">
        #{payer,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="modeTime != null">
        #{modeTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BIT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.orderstream.aftersales.model.AfterSalesExpenditureRecordExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Oct 21 16:35:45 CST 2021.
    -->
    select count(*) from T_AFTER_SALES_EXPENDITURE_RECORD
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Oct 21 16:35:45 CST 2021.
    -->
    update T_AFTER_SALES_EXPENDITURE_RECORD
    <set>
      <if test="record.recordId != null">
        RECORD_ID = #{record.recordId,jdbcType=INTEGER},
      </if>
      <if test="record.afterSalesId != null">
        AFTER_SALES_ID = #{record.afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="record.type != null">
        `TYPE` = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.amount != null">
        AMOUNT = #{record.amount,jdbcType=DECIMAL},
      </if>
      <if test="record.orgId != null">
        ORG_ID = #{record.orgId,jdbcType=INTEGER},
      </if>
      <if test="record.payer != null">
        PAYER = #{record.payer,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        ADD_TIME = #{record.addTime,jdbcType=BIGINT},
      </if>
      <if test="record.modeTime != null">
        MODE_TIME = #{record.modeTime,jdbcType=BIGINT},
      </if>
      <if test="record.creator != null">
        CREATOR = #{record.creator,jdbcType=INTEGER},
      </if>
      <if test="record.updater != null">
        UPDATER = #{record.updater,jdbcType=INTEGER},
      </if>
      <if test="record.isDelete != null">
        IS_DELETE = #{record.isDelete,jdbcType=BIT},
      </if>
      <if test="record.remark != null">
        REMARK = #{record.remark,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Oct 21 16:35:45 CST 2021.
    -->
    update T_AFTER_SALES_EXPENDITURE_RECORD
    set RECORD_ID = #{record.recordId,jdbcType=INTEGER},
      AFTER_SALES_ID = #{record.afterSalesId,jdbcType=INTEGER},
      `TYPE` = #{record.type,jdbcType=INTEGER},
      AMOUNT = #{record.amount,jdbcType=DECIMAL},
      ORG_ID = #{record.orgId,jdbcType=INTEGER},
      PAYER = #{record.payer,jdbcType=VARCHAR},
      ADD_TIME = #{record.addTime,jdbcType=BIGINT},
      MODE_TIME = #{record.modeTime,jdbcType=BIGINT},
      CREATOR = #{record.creator,jdbcType=INTEGER},
      UPDATER = #{record.updater,jdbcType=INTEGER},
      IS_DELETE = #{record.isDelete,jdbcType=BIT},
      REMARK = #{record.remark,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.orderstream.aftersales.model.AfterSalesExpenditureRecord">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Oct 21 16:35:45 CST 2021.
    -->
    update T_AFTER_SALES_EXPENDITURE_RECORD
    <set>
      <if test="afterSalesId != null">
        AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        `TYPE` = #{type,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="orgId != null">
        ORG_ID = #{orgId,jdbcType=INTEGER},
      </if>
      <if test="payer != null">
        PAYER = #{payer,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="modeTime != null">
        MODE_TIME = #{modeTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BIT},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where RECORD_ID = #{recordId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.orderstream.aftersales.model.AfterSalesExpenditureRecord">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Oct 21 16:35:45 CST 2021.
    -->
    update T_AFTER_SALES_EXPENDITURE_RECORD
    set AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      `TYPE` = #{type,jdbcType=INTEGER},
      AMOUNT = #{amount,jdbcType=DECIMAL},
      ORG_ID = #{orgId,jdbcType=INTEGER},
      PAYER = #{payer,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      MODE_TIME = #{modeTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=BIT},
      REMARK = #{remark,jdbcType=VARCHAR}
    where RECORD_ID = #{recordId,jdbcType=INTEGER}
  </update>
  <select id="selectByAfterSalesId" resultType="com.vedeng.orderstream.aftersales.model.AfterSalesExpenditureRecord">
    select
    <include refid="Base_Column_List" />
    from T_AFTER_SALES_EXPENDITURE_RECORD
    where AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
  </select>
</mapper>