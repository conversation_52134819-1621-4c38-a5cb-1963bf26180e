<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.dao.CoreOperateInfoGenerateExtendMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.goods.model.CoreOperateInfoGenerate">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed Jun 05 11:49:39 CST 2019.
        -->
        <id column="OPERATE_INFO_ID" jdbcType="INTEGER" property="operateInfoId" />
        <result column="SPU_ID" jdbcType="INTEGER" property="spuId" />
        <result column="SKU_ID" jdbcType="INTEGER" property="skuId" />
        <result column="OPERATE_INFO_TYPE" jdbcType="TINYINT" property="operateInfoType" />
        <result column="GOODS_NAME" jdbcType="VARCHAR" property="goodsName" />
        <result column="SEO_DESCRIPT" jdbcType="VARCHAR" property="seoDescript" />
        <result column="SEO_KEYWORDS" jdbcType="VARCHAR" property="seoKeywords" />
        <result column="SEO_TITLE" jdbcType="VARCHAR" property="seoTitle" />
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.vedeng.goods.model.CoreOperateInfoGenerate">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed Jun 05 11:49:39 CST 2019.
        -->
        <result column="OPRATE_INFO_HTML" jdbcType="LONGVARCHAR" property="oprateInfoHtml" />
    </resultMap>
    <sql id="Example_Where_Clause">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed Jun 05 11:49:39 CST 2019.
        -->
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed Jun 05 11:49:39 CST 2019.
        -->
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed Jun 05 11:49:39 CST 2019.
        -->
        OPERATE_INFO_ID, SPU_ID, SKU_ID, OPERATE_INFO_TYPE, GOODS_NAME, SEO_DESCRIPT, SEO_KEYWORDS,
        SEO_TITLE
    </sql>
    <sql id="Blob_Column_List">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed Jun 05 11:49:39 CST 2019.
        -->
        OPRATE_INFO_HTML
    </sql>

    <sql id="All_Column_List">
        a.OPERATE_INFO_ID, a.SPU_ID, a.SKU_ID, a.OPERATE_INFO_TYPE,
        a.SEO_DESCRIPT, a.SEO_KEYWORDS, a.SEO_TITLE, a.OPRATE_INFO_HTML,
        a.GOODS_NAME, a.OPERATE_INFO_SOURCE
    </sql>

    <select id="getCoreOperateInfoById" parameterType="com.vedeng.goods.model.vo.CoreOperateInfoGenerateVo" resultType="com.vedeng.goods.model.vo.CoreOperateInfoGenerateVo">
        SELECT
            <include refid="All_Column_List"/>
        FROM
            V_CORE_OPERATE_INFO a
        WHERE a.OPERATE_INFO_ID = #{operateInfoId,jdbcType=INTEGER}
    </select>
    <select id="getProductNameBySkuId" parameterType="java.lang.Integer" resultType="java.lang.String">
        SELECT
            SKU_NAME
        FROM
            V_CORE_SKU
        WHERE SKU_ID = #{skuId,jdbcType=INTEGER}
    </select>
    <select id="getProductNameBySpuId" parameterType="java.lang.Integer" resultType="java.lang.String">
        SELECT
            SHOW_NAME
        FROM
            V_CORE_SPU
        WHERE SPU_ID = #{spuId,jdbcType=INTEGER}
    </select>
    <select id="getCoreOperateInfoBySkuId" parameterType="java.lang.Integer" resultType="com.vedeng.goods.model.vo.CoreOperateInfoGenerateVo">
        SELECT
            <include refid="All_Column_List"/>
        FROM
            V_CORE_OPERATE_INFO a
        WHERE a.SKU_ID = #{skuId,jdbcType=INTEGER}
    </select>
    <select id="getCoreOperateInfoBySpuId" parameterType="java.lang.Integer" resultType="com.vedeng.goods.model.vo.CoreOperateInfoGenerateVo">
        SELECT
            <include refid="All_Column_List"/>
        FROM
            V_CORE_OPERATE_INFO a
        WHERE a.SPU_ID = #{spuId,jdbcType=INTEGER}
    </select>
    <select id="selectSpuOperateBySpuId" parameterType="java.lang.Integer" resultType="com.vedeng.goods.model.vo.OperateSpuVo">
        SELECT A.SPU_ID,
               A.SPU_NO,
               A.SHOW_NAME as spuName,
               A.CHECK_STATUS,
               A.BRAND_ID,
               A.SPU_TYPE,
               A.SPU_LEVEL,
               A.CATEGORY_ID,
               CONCAT(IF(D.BASE_CATEGORY_NAME IS NULL, '', CONCAT(D.BASE_CATEGORY_NAME, '@')),
                      IF(C.BASE_CATEGORY_NAME IS NULL, '', CONCAT(C.BASE_CATEGORY_NAME, '@')),
                      IF(B.BASE_CATEGORY_NAME IS NULL, '', B.BASE_CATEGORY_NAME))
                                                                                               AS CATEGORY_NAME_ERP,
               A.STATUS,
               G.MANAGE_CATEGORY_LEVEL,
               G.REGISTRATION_NUMBER,
               E.BRAND_NAME,
               FROM_UNIXTIME(IF(A.ADD_TIME = 0, NULL, A.ADD_TIME) / 1000, '%Y-%m-%d %H:%i:%s') AS ADD_TIME,
               A.CREATOR
        FROM V_CORE_SPU A
                 LEFT JOIN V_BASE_CATEGORY B ON A.CATEGORY_ID = B.BASE_CATEGORY_ID
                 LEFT JOIN V_BASE_CATEGORY C ON B.PARENT_ID = C.BASE_CATEGORY_ID
                 LEFT JOIN V_BASE_CATEGORY D ON C.PARENT_ID = D.BASE_CATEGORY_ID
                 LEFT JOIN T_FIRST_ENGAGE F ON A.FIRST_ENGAGE_ID = F.FIRST_ENGAGE_ID
                 LEFT JOIN T_REGISTRATION_NUMBER G
                           ON F.REGISTRATION_NUMBER_ID = G.REGISTRATION_NUMBER_ID
                 LEFT JOIN T_BRAND E ON A.BRAND_ID = E.BRAND_ID
        WHERE A.SPU_ID = #{spuId,jdbcType=INTEGER}
    </select>
    <select id="selectSkuOperateBySpuId" parameterType="java.lang.Integer" resultType="com.vedeng.goods.model.vo.OperateSkuVo">
        SELECT A.SKU_ID,
               A.SKU_NO,
               A.SPU_ID,
               IFNULL(A.SKU_NAME, '') AS SKU_NAME,
               IFNULL(A.SHOW_NAME, '') AS SHOW_NAME,
               IFNULL(B.GOODS_NAME, '') AS SKU_SUBTITLE,
               IFNULL(B.OPRATE_INFO_HTML, '') AS OP_DETAILS,
               B.OPERATE_INFO_SOURCE,
               B.SEO_KEYWORDS,
               A.IS_AVAILABLE_SALE,
               A.ORG_ID_LIST,
               A.CHECK_STATUS,
               A.MODEL,
               A.SPEC,
               A.MATERIAL_CODE,
               A.STATUS,
               C.UNIT_NAME AS BASE_UNIT_NAME,
               FROM_UNIXTIME(IF(A.ADD_TIME = 0, NULL, A.ADD_TIME) / 1000, '%Y-%m-%d %H:%i:%s') AS ADD_TIME,
               A.CREATOR
        FROM V_CORE_SKU A
                 LEFT JOIN V_CORE_OPERATE_INFO B
                           ON     A.SKU_ID = B.SKU_ID
                               AND B.OPERATE_INFO_TYPE = 2
                 LEFT JOIN T_UNIT C ON A.BASE_UNIT_ID = C.UNIT_ID
        WHERE A.SKU_ID = #{skuId,jdbcType=INTEGER}
    </select>

    <select id="selectSkuOperateAttachmentList" parameterType="java.lang.Integer" resultType="com.vedeng.goods.model.GoodsAttachment">
        SELECT A.GOODS_ATTACHMENT_ID,
               A.GOODS_ID,
               A.ATTACHMENT_TYPE,
               A.DOMAIN,
               A.URI,
               A.ALT,
               A.SORT,
               A.IS_DEFAULT,
               A.STATUS
        FROM T_GOODS_ATTACHMENT A
        WHERE A.GOODS_ID = #{goodsId,jdbcType=INTEGER} AND A.ATTACHMENT_TYPE = #{attachmentType,jdbcType=INTEGER} AND A.STATUS = 1
    </select>

    <select id="getCoreSkuInfoBySkuNo" parameterType="java.util.List" resultType="java.lang.Integer">
        SELECT
        A.SKU_ID
        FROM
        V_CORE_SKU A
        WHERE
        A.SKU_NO IN
        <foreach collection="skuNoList" item="skuNo" open="(" close=")" separator=",">
            #{skuNo,jdbcType=VARCHAR}
        </foreach>
    </select>
</mapper>