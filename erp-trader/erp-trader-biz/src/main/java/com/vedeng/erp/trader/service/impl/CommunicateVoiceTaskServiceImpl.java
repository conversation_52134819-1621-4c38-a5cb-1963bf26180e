package com.vedeng.erp.trader.service.impl;

import org.springframework.stereotype.Service;

import org.springframework.beans.factory.annotation.Autowired;

import com.vedeng.erp.trader.mapper.TraderCommunicateVoiceTaskMapper;
import com.vedeng.erp.trader.domain.entity.CommunicateVoiceTaskEntity;
import com.vedeng.erp.trader.service.CommunicateVoiceTaskService;

@Service
public class CommunicateVoiceTaskServiceImpl implements CommunicateVoiceTaskService {

    @Autowired
    private TraderCommunicateVoiceTaskMapper traderCommunicateVoiceTaskMapper;

    @Override
    public int insertSelective(CommunicateVoiceTaskEntity record) {
        return traderCommunicateVoiceTaskMapper.insertSelective(record);
    }

    @Override
    public int deleteByPrimaryKey(Long id) {
        return traderCommunicateVoiceTaskMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(CommunicateVoiceTaskEntity record) {
        return traderCommunicateVoiceTaskMapper.insert(record);
    }

    @Override
    public CommunicateVoiceTaskEntity selectByPrimaryKey(Long id) {
        return traderCommunicateVoiceTaskMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(CommunicateVoiceTaskEntity record) {
        return traderCommunicateVoiceTaskMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(CommunicateVoiceTaskEntity record) {
        return traderCommunicateVoiceTaskMapper.updateByPrimaryKey(record);
    }

    @Override
    public CommunicateVoiceTaskEntity selectByCommunicateRecordId(Integer communicateRecordId) {
        return traderCommunicateVoiceTaskMapper.getCommunicateVoiceTaskByTaskId(communicateRecordId);
    }

    @Override
    public CommunicateVoiceTaskEntity selectByCommunicateRecordIdAndSence(Integer communicateRecordId, String sence) {
        return traderCommunicateVoiceTaskMapper.getCommunicateVoiceTaskByTaskIdAndSence(communicateRecordId, sence);
    }


}

