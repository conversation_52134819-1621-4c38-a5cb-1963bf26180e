package com.wms.task.Ddi;

import com.alibaba.fastjson.JSON;
import com.rabbitmq.MsgProducer;
import com.rabbitmq.RabbitConfig;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.StringUtil;
import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.model.LogisticsOrderData;
import com.vedeng.logistics.model.LogisticsOrderGoodsData;
import com.wms.inventoryadjustment.dao.InventoryAdjustmentMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * JSON补偿起包裹信息数据（临时）
 *
 * <AUTHOR>
 */

@Component
@JobHandler(value = "IssueExpress2WebByJsonTask")
public class IssueExpress2WebByJsonTask extends AbstractJobHandler {
    public static Logger logger = LoggerFactory.getLogger(IssueExpress2WebByJsonTask.class);
    @Autowired
    @Qualifier("msgProducer")
    MsgProducer msgProducer;

    @Resource
    private InventoryAdjustmentMapper inventoryAdjustmentMapper;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        XxlJobLogger.log("IssueExpress2WebByJsonTask-JOB, START");

        if (StringUtil.isBlank(s)){
            XxlJobLogger.log("JSON补偿包裹信息文件名称不能为空");
            return ReturnT.FAIL;
        }
        String fileInfo = getFileInfo(s);
        List<LogisticsOrderData> logisticsOrderDataList = JSON.parseArray(fileInfo, LogisticsOrderData.class);
        if (CollectionUtils.isEmpty(logisticsOrderDataList)) {
            XxlJobLogger.log("JSON补偿包裹信息文件信息错误");
            return ReturnT.FAIL;
        }
        logisticsOrderDataList.forEach(item -> {
            for (LogisticsOrderGoodsData logisticsOrderGoodsData : item.getOrderGoodsLogisticsDataList()) {
                if (logisticsOrderGoodsData == null || StringUtil.isBlank(item.getOrderNo())) {
                    continue;
                }
                List<Express> expressByDetailInfo = inventoryAdjustmentMapper.getExpressByDetailInfo(logisticsOrderGoodsData, item.getOrderNo());
                if (expressByDetailInfo == null || expressByDetailInfo.size() > 1) {
                    continue;
                }

                item.setAddLogisticsNo(expressByDetailInfo.get(0).getLogisticsNo());

                if (item.getType() != null && (item.getType().equals(1) || item.getType().equals(2))){
                    item.setDelLogisticsNo(expressByDetailInfo.get(0).getLogisticsNo());
                }
                logger.info("补偿发送mq消息至前台 item:{}", JSON.toJSONString(item));

                msgProducer.sendMsg(RabbitConfig.MJX_ADDLOGISTICS_EXCHANGE, RabbitConfig.MJX_ADDLOGISTICS_ROUTINGKEY, JSON.toJSONString(item));
            }
        });
        return SUCCESS;
    }

    public String getFileInfo(String s) throws IOException {
        ClassPathResource resource = new ClassPathResource(s);
        InputStream inputStream = resource.getInputStream();
        StringBuilder out = new StringBuilder();
        byte[] b = new byte[4096];
        // 读取流
        for (int n; (n = inputStream.read(b)) != -1; ) {
            out.append(new String(b, 0, n));
        }
        return out.toString();
    }
}
