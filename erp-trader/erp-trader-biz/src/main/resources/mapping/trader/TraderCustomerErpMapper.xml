<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.TraderCustomerErpMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.TraderCustomerErpEntity">
    <!--@mbg.generated-->
    <!--@Table T_TRADER_CUSTOMER-->
    <id column="TRADER_CUSTOMER_ID" jdbcType="INTEGER" property="traderCustomerId" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_CUSTOMER_CATEGORY_ID" jdbcType="INTEGER" property="traderCustomerCategoryId" />
    <result column="CUSTOMER_TYPE" jdbcType="INTEGER" property="customerType" />
    <result column="CUSTOMER_NATURE" jdbcType="INTEGER" property="customerNature" />
    <result column="IS_ENABLE" jdbcType="INTEGER" property="isEnable" />
    <result column="IS_TOP" jdbcType="INTEGER" property="isTop" />
    <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
    <result column="PERIOD_AMOUNT" jdbcType="DECIMAL" property="periodAmount" />
    <result column="PERIOD_DAY" jdbcType="INTEGER" property="periodDay" />
    <result column="DISABLE_TIME" jdbcType="BIGINT" property="disableTime" />
    <result column="CUSTOMER_FROM" jdbcType="INTEGER" property="customerFrom" />
    <result column="FIRST_ENQUIRY_TYPE" jdbcType="INTEGER" property="firstEnquiryType" />
    <result column="CUSTOMER_LEVEL" jdbcType="INTEGER" property="customerLevel" />
    <result column="CUSTOMER_CATEGORY" jdbcType="INTEGER" property="customerCategory" />
    <result column="TRADER_LEVEL" jdbcType="INTEGER" property="traderLevel" />
    <result column="USER_EVALUATE" jdbcType="INTEGER" property="userEvaluate" />
    <result column="CUSTOMER_SCORE" jdbcType="INTEGER" property="customerScore" />
    <result column="BASIC_MEDICAL_DEALER" jdbcType="INTEGER" property="basicMedicalDealer" />
    <result column="IS_VIP" jdbcType="INTEGER" property="isVip" />
    <result column="OWNERSHIP" jdbcType="INTEGER" property="ownership" />
    <result column="MEDICAL_TYPE" jdbcType="INTEGER" property="medicalType" />
    <result column="HOSPITAL_LEVEL" jdbcType="INTEGER" property="hospitalLevel" />
    <result column="EMPLOYEES" jdbcType="INTEGER" property="employees" />
    <result column="ANNUAL_SALES" jdbcType="INTEGER" property="annualSales" />
    <result column="SALES_MODEL" jdbcType="INTEGER" property="salesModel" />
    <result column="REGISTERED_CAPITAL" jdbcType="VARCHAR" property="registeredCapital" />
    <result column="REGISTERED_DATE" jdbcType="DATE" property="registeredDate" />
    <result column="DIRECT_SELLING" jdbcType="DECIMAL" property="directSelling" />
    <result column="WHOLESALE" jdbcType="DECIMAL" property="wholesale" />
    <result column="ORGANIZATION_CODE" jdbcType="VARCHAR" property="organizationCode" />
    <result column="UNIFIED_SOCIAL_CREDIT_IDENTIFIER" jdbcType="VARCHAR" property="unifiedSocialCreditIdentifier" />
    <result column="BUSINESS_SCOPE" jdbcType="VARCHAR" property="businessScope" />
    <result column="HISTORY_NAME" jdbcType="VARCHAR" property="historyName" />
    <result column="DISABLE_REASON" jdbcType="VARCHAR" property="disableReason" />
    <result column="COMMENTS" jdbcType="VARCHAR" property="comments" />
    <result column="FINANCE_COMMENTS" jdbcType="VARCHAR" property="financeComments" />
    <result column="LOGISTICS_COMMENTS" jdbcType="VARCHAR" property="logisticsComments" />
    <result column="BRIEF" jdbcType="VARCHAR" property="brief" />
    <result column="HISTORY" jdbcType="VARCHAR" property="history" />
    <result column="BUSINESS_CONDITION" jdbcType="VARCHAR" property="businessCondition" />
    <result column="BUSINESS_PLAN" jdbcType="VARCHAR" property="businessPlan" />
    <result column="ADVANTAGE" jdbcType="VARCHAR" property="advantage" />
    <result column="PRIMAL_PROBLEM" jdbcType="VARCHAR" property="primalProblem" />
    <result column="MN_CODE" jdbcType="VARCHAR" property="mnCode" />
    <result column="IS_COLLECT" jdbcType="INTEGER" property="isCollect" />
    <result column="IS_LIMITPRICE" jdbcType="INTEGER" property="isLimitprice" />
    <result column="COLLECT_TIME" jdbcType="BIGINT" property="collectTime" />
    <result column="IS_PROFIT" jdbcType="INTEGER" property="isProfit" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="IS_VEDENG_MEMBER" jdbcType="INTEGER" property="isVedengMember" />
    <result column="IS_SHOW_VIP_PRICE" jdbcType="INTEGER" property="isShowVipPrice" />
    <result column="HAS_QUOTED" jdbcType="INTEGER" property="hasQuoted" />
    <result column="IS_COOPERATED" jdbcType="INTEGER" property="isCooperated" />
    <result column="ASSOCIATED_CUSTOMER_GROUP" jdbcType="BIGINT" property="associatedCustomerGroup" />
    <result column="PUBLIC_CUSTOMER_EARLY_WARNING_COUNT" jdbcType="INTEGER" property="publicCustomerEarlyWarningCount" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    TRADER_CUSTOMER_ID, TRADER_ID, TRADER_CUSTOMER_CATEGORY_ID, CUSTOMER_TYPE, CUSTOMER_NATURE, 
    IS_ENABLE, IS_TOP, AMOUNT, PERIOD_AMOUNT, PERIOD_DAY, DISABLE_TIME, CUSTOMER_FROM, 
    FIRST_ENQUIRY_TYPE, CUSTOMER_LEVEL, CUSTOMER_CATEGORY, TRADER_LEVEL, USER_EVALUATE, 
    CUSTOMER_SCORE, BASIC_MEDICAL_DEALER, IS_VIP, OWNERSHIP, MEDICAL_TYPE, HOSPITAL_LEVEL, 
    EMPLOYEES, ANNUAL_SALES, SALES_MODEL, REGISTERED_CAPITAL, REGISTERED_DATE, DIRECT_SELLING, 
    WHOLESALE, ORGANIZATION_CODE, UNIFIED_SOCIAL_CREDIT_IDENTIFIER, BUSINESS_SCOPE, HISTORY_NAME, 
    DISABLE_REASON, COMMENTS, FINANCE_COMMENTS, LOGISTICS_COMMENTS, BRIEF, HISTORY, BUSINESS_CONDITION, 
    BUSINESS_PLAN, ADVANTAGE, PRIMAL_PROBLEM, MN_CODE, IS_COLLECT, IS_LIMITPRICE, COLLECT_TIME, 
    IS_PROFIT, ADD_TIME, CREATOR, MOD_TIME, UPDATER, IS_VEDENG_MEMBER, IS_SHOW_VIP_PRICE, 
    HAS_QUOTED, IS_COOPERATED, ASSOCIATED_CUSTOMER_GROUP, PUBLIC_CUSTOMER_EARLY_WARNING_COUNT
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_TRADER_CUSTOMER
    where TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_TRADER_CUSTOMER
    where TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="TRADER_CUSTOMER_ID" keyProperty="traderCustomerId" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerErpEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_CUSTOMER (TRADER_ID, TRADER_CUSTOMER_CATEGORY_ID, 
      CUSTOMER_TYPE, CUSTOMER_NATURE, IS_ENABLE, 
      IS_TOP, AMOUNT, PERIOD_AMOUNT, 
      PERIOD_DAY, DISABLE_TIME, CUSTOMER_FROM, 
      FIRST_ENQUIRY_TYPE, CUSTOMER_LEVEL, CUSTOMER_CATEGORY, 
      TRADER_LEVEL, USER_EVALUATE, CUSTOMER_SCORE, 
      BASIC_MEDICAL_DEALER, IS_VIP, OWNERSHIP, 
      MEDICAL_TYPE, HOSPITAL_LEVEL, EMPLOYEES, 
      ANNUAL_SALES, SALES_MODEL, REGISTERED_CAPITAL, 
      REGISTERED_DATE, DIRECT_SELLING, WHOLESALE, 
      ORGANIZATION_CODE, UNIFIED_SOCIAL_CREDIT_IDENTIFIER, 
      BUSINESS_SCOPE, HISTORY_NAME, DISABLE_REASON, 
      COMMENTS, FINANCE_COMMENTS, LOGISTICS_COMMENTS, 
      BRIEF, HISTORY, BUSINESS_CONDITION, 
      BUSINESS_PLAN, ADVANTAGE, PRIMAL_PROBLEM, 
      MN_CODE, IS_COLLECT, IS_LIMITPRICE, 
      COLLECT_TIME, IS_PROFIT, ADD_TIME, 
      CREATOR, MOD_TIME, UPDATER, 
      IS_VEDENG_MEMBER, IS_SHOW_VIP_PRICE, HAS_QUOTED, 
      IS_COOPERATED, ASSOCIATED_CUSTOMER_GROUP, PUBLIC_CUSTOMER_EARLY_WARNING_COUNT
      )
    values (#{traderId,jdbcType=INTEGER}, #{traderCustomerCategoryId,jdbcType=INTEGER}, 
      #{customerType,jdbcType=INTEGER}, #{customerNature,jdbcType=INTEGER}, #{isEnable,jdbcType=INTEGER}, 
      #{isTop,jdbcType=INTEGER}, #{amount,jdbcType=DECIMAL}, #{periodAmount,jdbcType=DECIMAL}, 
      #{periodDay,jdbcType=INTEGER}, #{disableTime,jdbcType=BIGINT}, #{customerFrom,jdbcType=INTEGER}, 
      #{firstEnquiryType,jdbcType=INTEGER}, #{customerLevel,jdbcType=INTEGER}, #{customerCategory,jdbcType=INTEGER}, 
      #{traderLevel,jdbcType=INTEGER}, #{userEvaluate,jdbcType=INTEGER}, #{customerScore,jdbcType=INTEGER}, 
      #{basicMedicalDealer,jdbcType=INTEGER}, #{isVip,jdbcType=INTEGER}, #{ownership,jdbcType=INTEGER}, 
      #{medicalType,jdbcType=INTEGER}, #{hospitalLevel,jdbcType=INTEGER}, #{employees,jdbcType=INTEGER}, 
      #{annualSales,jdbcType=INTEGER}, #{salesModel,jdbcType=INTEGER}, #{registeredCapital,jdbcType=VARCHAR}, 
      #{registeredDate,jdbcType=DATE}, #{directSelling,jdbcType=DECIMAL}, #{wholesale,jdbcType=DECIMAL}, 
      #{organizationCode,jdbcType=VARCHAR}, #{unifiedSocialCreditIdentifier,jdbcType=VARCHAR}, 
      #{businessScope,jdbcType=VARCHAR}, #{historyName,jdbcType=VARCHAR}, #{disableReason,jdbcType=VARCHAR}, 
      #{comments,jdbcType=VARCHAR}, #{financeComments,jdbcType=VARCHAR}, #{logisticsComments,jdbcType=VARCHAR}, 
      #{brief,jdbcType=VARCHAR}, #{history,jdbcType=VARCHAR}, #{businessCondition,jdbcType=VARCHAR}, 
      #{businessPlan,jdbcType=VARCHAR}, #{advantage,jdbcType=VARCHAR}, #{primalProblem,jdbcType=VARCHAR}, 
      #{mnCode,jdbcType=VARCHAR}, #{isCollect,jdbcType=INTEGER}, #{isLimitprice,jdbcType=INTEGER}, 
      #{collectTime,jdbcType=BIGINT}, #{isProfit,jdbcType=INTEGER}, #{addTime,jdbcType=BIGINT}, 
      #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}, 
      #{isVedengMember,jdbcType=INTEGER}, #{isShowVipPrice,jdbcType=INTEGER}, #{hasQuoted,jdbcType=INTEGER}, 
      #{isCooperated,jdbcType=INTEGER}, #{associatedCustomerGroup,jdbcType=BIGINT}, #{publicCustomerEarlyWarningCount,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="TRADER_CUSTOMER_ID" keyProperty="traderCustomerId" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerErpEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_CUSTOMER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderCustomerCategoryId != null">
        TRADER_CUSTOMER_CATEGORY_ID,
      </if>
      <if test="customerType != null">
        CUSTOMER_TYPE,
      </if>
      <if test="customerNature != null">
        CUSTOMER_NATURE,
      </if>
      <if test="isEnable != null">
        IS_ENABLE,
      </if>
      <if test="isTop != null">
        IS_TOP,
      </if>
      <if test="amount != null">
        AMOUNT,
      </if>
      <if test="periodAmount != null">
        PERIOD_AMOUNT,
      </if>
      <if test="periodDay != null">
        PERIOD_DAY,
      </if>
      <if test="disableTime != null">
        DISABLE_TIME,
      </if>
      <if test="customerFrom != null">
        CUSTOMER_FROM,
      </if>
      <if test="firstEnquiryType != null">
        FIRST_ENQUIRY_TYPE,
      </if>
      <if test="customerLevel != null">
        CUSTOMER_LEVEL,
      </if>
      <if test="customerCategory != null">
        CUSTOMER_CATEGORY,
      </if>
      <if test="traderLevel != null">
        TRADER_LEVEL,
      </if>
      <if test="userEvaluate != null">
        USER_EVALUATE,
      </if>
      <if test="customerScore != null">
        CUSTOMER_SCORE,
      </if>
      <if test="basicMedicalDealer != null">
        BASIC_MEDICAL_DEALER,
      </if>
      <if test="isVip != null">
        IS_VIP,
      </if>
      <if test="ownership != null">
        OWNERSHIP,
      </if>
      <if test="medicalType != null">
        MEDICAL_TYPE,
      </if>
      <if test="hospitalLevel != null">
        HOSPITAL_LEVEL,
      </if>
      <if test="employees != null">
        EMPLOYEES,
      </if>
      <if test="annualSales != null">
        ANNUAL_SALES,
      </if>
      <if test="salesModel != null">
        SALES_MODEL,
      </if>
      <if test="registeredCapital != null">
        REGISTERED_CAPITAL,
      </if>
      <if test="registeredDate != null">
        REGISTERED_DATE,
      </if>
      <if test="directSelling != null">
        DIRECT_SELLING,
      </if>
      <if test="wholesale != null">
        WHOLESALE,
      </if>
      <if test="organizationCode != null">
        ORGANIZATION_CODE,
      </if>
      <if test="unifiedSocialCreditIdentifier != null">
        UNIFIED_SOCIAL_CREDIT_IDENTIFIER,
      </if>
      <if test="businessScope != null">
        BUSINESS_SCOPE,
      </if>
      <if test="historyName != null">
        HISTORY_NAME,
      </if>
      <if test="disableReason != null">
        DISABLE_REASON,
      </if>
      <if test="comments != null">
        COMMENTS,
      </if>
      <if test="financeComments != null">
        FINANCE_COMMENTS,
      </if>
      <if test="logisticsComments != null">
        LOGISTICS_COMMENTS,
      </if>
      <if test="brief != null">
        BRIEF,
      </if>
      <if test="history != null">
        HISTORY,
      </if>
      <if test="businessCondition != null">
        BUSINESS_CONDITION,
      </if>
      <if test="businessPlan != null">
        BUSINESS_PLAN,
      </if>
      <if test="advantage != null">
        ADVANTAGE,
      </if>
      <if test="primalProblem != null">
        PRIMAL_PROBLEM,
      </if>
      <if test="mnCode != null">
        MN_CODE,
      </if>
      <if test="isCollect != null">
        IS_COLLECT,
      </if>
      <if test="isLimitprice != null">
        IS_LIMITPRICE,
      </if>
      <if test="collectTime != null">
        COLLECT_TIME,
      </if>
      <if test="isProfit != null">
        IS_PROFIT,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="isVedengMember != null">
        IS_VEDENG_MEMBER,
      </if>
      <if test="isShowVipPrice != null">
        IS_SHOW_VIP_PRICE,
      </if>
      <if test="hasQuoted != null">
        HAS_QUOTED,
      </if>
      <if test="isCooperated != null">
        IS_COOPERATED,
      </if>
      <if test="associatedCustomerGroup != null">
        ASSOCIATED_CUSTOMER_GROUP,
      </if>
      <if test="publicCustomerEarlyWarningCount != null">
        PUBLIC_CUSTOMER_EARLY_WARNING_COUNT,
      </if>
      <if test="invalidReason != null">
        INVALID_REASON,
      </if>
      <if test="otherReason != null">
        OTHER_REASON,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderCustomerCategoryId != null">
        #{traderCustomerCategoryId,jdbcType=INTEGER},
      </if>
      <if test="customerType != null">
        #{customerType,jdbcType=INTEGER},
      </if>
      <if test="customerNature != null">
        #{customerNature,jdbcType=INTEGER},
      </if>
      <if test="isEnable != null">
        #{isEnable,jdbcType=INTEGER},
      </if>
      <if test="isTop != null">
        #{isTop,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="periodAmount != null">
        #{periodAmount,jdbcType=DECIMAL},
      </if>
      <if test="periodDay != null">
        #{periodDay,jdbcType=INTEGER},
      </if>
      <if test="disableTime != null">
        #{disableTime,jdbcType=BIGINT},
      </if>
      <if test="customerFrom != null">
        #{customerFrom,jdbcType=INTEGER},
      </if>
      <if test="firstEnquiryType != null">
        #{firstEnquiryType,jdbcType=INTEGER},
      </if>
      <if test="customerLevel != null">
        #{customerLevel,jdbcType=INTEGER},
      </if>
      <if test="customerCategory != null">
        #{customerCategory,jdbcType=INTEGER},
      </if>
      <if test="traderLevel != null">
        #{traderLevel,jdbcType=INTEGER},
      </if>
      <if test="userEvaluate != null">
        #{userEvaluate,jdbcType=INTEGER},
      </if>
      <if test="customerScore != null">
        #{customerScore,jdbcType=INTEGER},
      </if>
      <if test="basicMedicalDealer != null">
        #{basicMedicalDealer,jdbcType=INTEGER},
      </if>
      <if test="isVip != null">
        #{isVip,jdbcType=INTEGER},
      </if>
      <if test="ownership != null">
        #{ownership,jdbcType=INTEGER},
      </if>
      <if test="medicalType != null">
        #{medicalType,jdbcType=INTEGER},
      </if>
      <if test="hospitalLevel != null">
        #{hospitalLevel,jdbcType=INTEGER},
      </if>
      <if test="employees != null">
        #{employees,jdbcType=INTEGER},
      </if>
      <if test="annualSales != null">
        #{annualSales,jdbcType=INTEGER},
      </if>
      <if test="salesModel != null">
        #{salesModel,jdbcType=INTEGER},
      </if>
      <if test="registeredCapital != null">
        #{registeredCapital,jdbcType=VARCHAR},
      </if>
      <if test="registeredDate != null">
        #{registeredDate,jdbcType=DATE},
      </if>
      <if test="directSelling != null">
        #{directSelling,jdbcType=DECIMAL},
      </if>
      <if test="wholesale != null">
        #{wholesale,jdbcType=DECIMAL},
      </if>
      <if test="organizationCode != null">
        #{organizationCode,jdbcType=VARCHAR},
      </if>
      <if test="unifiedSocialCreditIdentifier != null">
        #{unifiedSocialCreditIdentifier,jdbcType=VARCHAR},
      </if>
      <if test="businessScope != null">
        #{businessScope,jdbcType=VARCHAR},
      </if>
      <if test="historyName != null">
        #{historyName,jdbcType=VARCHAR},
      </if>
      <if test="disableReason != null">
        #{disableReason,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="financeComments != null">
        #{financeComments,jdbcType=VARCHAR},
      </if>
      <if test="logisticsComments != null">
        #{logisticsComments,jdbcType=VARCHAR},
      </if>
      <if test="brief != null">
        #{brief,jdbcType=VARCHAR},
      </if>
      <if test="history != null">
        #{history,jdbcType=VARCHAR},
      </if>
      <if test="businessCondition != null">
        #{businessCondition,jdbcType=VARCHAR},
      </if>
      <if test="businessPlan != null">
        #{businessPlan,jdbcType=VARCHAR},
      </if>
      <if test="advantage != null">
        #{advantage,jdbcType=VARCHAR},
      </if>
      <if test="primalProblem != null">
        #{primalProblem,jdbcType=VARCHAR},
      </if>
      <if test="mnCode != null">
        #{mnCode,jdbcType=VARCHAR},
      </if>
      <if test="isCollect != null">
        #{isCollect,jdbcType=INTEGER},
      </if>
      <if test="isLimitprice != null">
        #{isLimitprice,jdbcType=INTEGER},
      </if>
      <if test="collectTime != null">
        #{collectTime,jdbcType=BIGINT},
      </if>
      <if test="isProfit != null">
        #{isProfit,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="isVedengMember != null">
        #{isVedengMember,jdbcType=INTEGER},
      </if>
      <if test="isShowVipPrice != null">
        #{isShowVipPrice,jdbcType=INTEGER},
      </if>
      <if test="hasQuoted != null">
        #{hasQuoted,jdbcType=INTEGER},
      </if>
      <if test="isCooperated != null">
        #{isCooperated,jdbcType=INTEGER},
      </if>
      <if test="associatedCustomerGroup != null">
        #{associatedCustomerGroup,jdbcType=BIGINT},
      </if>
      <if test="publicCustomerEarlyWarningCount != null">
        #{publicCustomerEarlyWarningCount,jdbcType=INTEGER},
      </if>
      <if test="invalidReason != null">
        #{invalidReason,jdbcType=VARCHAR},
      </if> 
      <if test="otherReason != null">
        #{otherReason,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerErpEntity">
    <!--@mbg.generated-->
    update T_TRADER_CUSTOMER
    <set>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderCustomerCategoryId != null">
        TRADER_CUSTOMER_CATEGORY_ID = #{traderCustomerCategoryId,jdbcType=INTEGER},
      </if>
      <if test="customerType != null">
        CUSTOMER_TYPE = #{customerType,jdbcType=INTEGER},
      </if>
      <if test="customerNature != null">
        CUSTOMER_NATURE = #{customerNature,jdbcType=INTEGER},
      </if>
      <if test="isEnable != null">
        IS_ENABLE = #{isEnable,jdbcType=INTEGER},
      </if>
      <if test="isTop != null">
        IS_TOP = #{isTop,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="periodAmount != null">
        PERIOD_AMOUNT = #{periodAmount,jdbcType=DECIMAL},
      </if>
      <if test="periodDay != null">
        PERIOD_DAY = #{periodDay,jdbcType=INTEGER},
      </if>
      <if test="disableTime != null">
        DISABLE_TIME = #{disableTime,jdbcType=BIGINT},
      </if>
      <if test="customerFrom != null">
        CUSTOMER_FROM = #{customerFrom,jdbcType=INTEGER},
      </if>
      <if test="firstEnquiryType != null">
        FIRST_ENQUIRY_TYPE = #{firstEnquiryType,jdbcType=INTEGER},
      </if>
      <if test="customerLevel != null">
        CUSTOMER_LEVEL = #{customerLevel,jdbcType=INTEGER},
      </if>
      <if test="customerCategory != null">
        CUSTOMER_CATEGORY = #{customerCategory,jdbcType=INTEGER},
      </if>
      <if test="traderLevel != null">
        TRADER_LEVEL = #{traderLevel,jdbcType=INTEGER},
      </if>
      <if test="userEvaluate != null">
        USER_EVALUATE = #{userEvaluate,jdbcType=INTEGER},
      </if>
      <if test="customerScore != null">
        CUSTOMER_SCORE = #{customerScore,jdbcType=INTEGER},
      </if>
      <if test="basicMedicalDealer != null">
        BASIC_MEDICAL_DEALER = #{basicMedicalDealer,jdbcType=INTEGER},
      </if>
      <if test="isVip != null">
        IS_VIP = #{isVip,jdbcType=INTEGER},
      </if>
      <if test="ownership != null">
        OWNERSHIP = #{ownership,jdbcType=INTEGER},
      </if>
      <if test="medicalType != null">
        MEDICAL_TYPE = #{medicalType,jdbcType=INTEGER},
      </if>
      <if test="hospitalLevel != null">
        HOSPITAL_LEVEL = #{hospitalLevel,jdbcType=INTEGER},
      </if>
      <if test="employees != null">
        EMPLOYEES = #{employees,jdbcType=INTEGER},
      </if>
      <if test="annualSales != null">
        ANNUAL_SALES = #{annualSales,jdbcType=INTEGER},
      </if>
      <if test="salesModel != null">
        SALES_MODEL = #{salesModel,jdbcType=INTEGER},
      </if>
      <if test="registeredCapital != null">
        REGISTERED_CAPITAL = #{registeredCapital,jdbcType=VARCHAR},
      </if>
      <if test="registeredDate != null">
        REGISTERED_DATE = #{registeredDate,jdbcType=DATE},
      </if>
      <if test="directSelling != null">
        DIRECT_SELLING = #{directSelling,jdbcType=DECIMAL},
      </if>
      <if test="wholesale != null">
        WHOLESALE = #{wholesale,jdbcType=DECIMAL},
      </if>
      <if test="organizationCode != null">
        ORGANIZATION_CODE = #{organizationCode,jdbcType=VARCHAR},
      </if>
      <if test="unifiedSocialCreditIdentifier != null">
        UNIFIED_SOCIAL_CREDIT_IDENTIFIER = #{unifiedSocialCreditIdentifier,jdbcType=VARCHAR},
      </if>
      <if test="businessScope != null">
        BUSINESS_SCOPE = #{businessScope,jdbcType=VARCHAR},
      </if>
      <if test="historyName != null">
        HISTORY_NAME = #{historyName,jdbcType=VARCHAR},
      </if>
      <if test="disableReason != null">
        DISABLE_REASON = #{disableReason,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="financeComments != null">
        FINANCE_COMMENTS = #{financeComments,jdbcType=VARCHAR},
      </if>
      <if test="logisticsComments != null">
        LOGISTICS_COMMENTS = #{logisticsComments,jdbcType=VARCHAR},
      </if>
      <if test="brief != null">
        BRIEF = #{brief,jdbcType=VARCHAR},
      </if>
      <if test="history != null">
        HISTORY = #{history,jdbcType=VARCHAR},
      </if>
      <if test="businessCondition != null">
        BUSINESS_CONDITION = #{businessCondition,jdbcType=VARCHAR},
      </if>
      <if test="businessPlan != null">
        BUSINESS_PLAN = #{businessPlan,jdbcType=VARCHAR},
      </if>
      <if test="advantage != null">
        ADVANTAGE = #{advantage,jdbcType=VARCHAR},
      </if>
      <if test="primalProblem != null">
        PRIMAL_PROBLEM = #{primalProblem,jdbcType=VARCHAR},
      </if>
      <if test="mnCode != null">
        MN_CODE = #{mnCode,jdbcType=VARCHAR},
      </if>
      <if test="isCollect != null">
        IS_COLLECT = #{isCollect,jdbcType=INTEGER},
      </if>
      <if test="isLimitprice != null">
        IS_LIMITPRICE = #{isLimitprice,jdbcType=INTEGER},
      </if>
      <if test="collectTime != null">
        COLLECT_TIME = #{collectTime,jdbcType=BIGINT},
      </if>
      <if test="isProfit != null">
        IS_PROFIT = #{isProfit,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="isVedengMember != null">
        IS_VEDENG_MEMBER = #{isVedengMember,jdbcType=INTEGER},
      </if>
      <if test="isShowVipPrice != null">
        IS_SHOW_VIP_PRICE = #{isShowVipPrice,jdbcType=INTEGER},
      </if>
      <if test="hasQuoted != null">
        HAS_QUOTED = #{hasQuoted,jdbcType=INTEGER},
      </if>
      <if test="isCooperated != null">
        IS_COOPERATED = #{isCooperated,jdbcType=INTEGER},
      </if>
      <if test="associatedCustomerGroup != null">
        ASSOCIATED_CUSTOMER_GROUP = #{associatedCustomerGroup,jdbcType=BIGINT},
      </if>
      <if test="publicCustomerEarlyWarningCount != null">
        PUBLIC_CUSTOMER_EARLY_WARNING_COUNT = #{publicCustomerEarlyWarningCount,jdbcType=INTEGER},
      </if>
      <if test="invalidReason != null">
        INVALID_REASON = #{invalidReason,jdbcType=VARCHAR},
      </if>
      <if test="otherReason != null">
        OTHER_REASON = #{otherReason,jdbcType=VARCHAR},
      </if>
    </set>
    where TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerErpEntity">
    <!--@mbg.generated-->
    update T_TRADER_CUSTOMER
    set TRADER_ID = #{traderId,jdbcType=INTEGER},
      TRADER_CUSTOMER_CATEGORY_ID = #{traderCustomerCategoryId,jdbcType=INTEGER},
      CUSTOMER_TYPE = #{customerType,jdbcType=INTEGER},
      CUSTOMER_NATURE = #{customerNature,jdbcType=INTEGER},
      IS_ENABLE = #{isEnable,jdbcType=INTEGER},
      IS_TOP = #{isTop,jdbcType=INTEGER},
      AMOUNT = #{amount,jdbcType=DECIMAL},
      PERIOD_AMOUNT = #{periodAmount,jdbcType=DECIMAL},
      PERIOD_DAY = #{periodDay,jdbcType=INTEGER},
      DISABLE_TIME = #{disableTime,jdbcType=BIGINT},
      CUSTOMER_FROM = #{customerFrom,jdbcType=INTEGER},
      FIRST_ENQUIRY_TYPE = #{firstEnquiryType,jdbcType=INTEGER},
      CUSTOMER_LEVEL = #{customerLevel,jdbcType=INTEGER},
      CUSTOMER_CATEGORY = #{customerCategory,jdbcType=INTEGER},
      TRADER_LEVEL = #{traderLevel,jdbcType=INTEGER},
      USER_EVALUATE = #{userEvaluate,jdbcType=INTEGER},
      CUSTOMER_SCORE = #{customerScore,jdbcType=INTEGER},
      BASIC_MEDICAL_DEALER = #{basicMedicalDealer,jdbcType=INTEGER},
      IS_VIP = #{isVip,jdbcType=INTEGER},
      OWNERSHIP = #{ownership,jdbcType=INTEGER},
      MEDICAL_TYPE = #{medicalType,jdbcType=INTEGER},
      HOSPITAL_LEVEL = #{hospitalLevel,jdbcType=INTEGER},
      EMPLOYEES = #{employees,jdbcType=INTEGER},
      ANNUAL_SALES = #{annualSales,jdbcType=INTEGER},
      SALES_MODEL = #{salesModel,jdbcType=INTEGER},
      REGISTERED_CAPITAL = #{registeredCapital,jdbcType=VARCHAR},
      REGISTERED_DATE = #{registeredDate,jdbcType=DATE},
      DIRECT_SELLING = #{directSelling,jdbcType=DECIMAL},
      WHOLESALE = #{wholesale,jdbcType=DECIMAL},
      ORGANIZATION_CODE = #{organizationCode,jdbcType=VARCHAR},
      UNIFIED_SOCIAL_CREDIT_IDENTIFIER = #{unifiedSocialCreditIdentifier,jdbcType=VARCHAR},
      BUSINESS_SCOPE = #{businessScope,jdbcType=VARCHAR},
      HISTORY_NAME = #{historyName,jdbcType=VARCHAR},
      DISABLE_REASON = #{disableReason,jdbcType=VARCHAR},
      COMMENTS = #{comments,jdbcType=VARCHAR},
      FINANCE_COMMENTS = #{financeComments,jdbcType=VARCHAR},
      LOGISTICS_COMMENTS = #{logisticsComments,jdbcType=VARCHAR},
      BRIEF = #{brief,jdbcType=VARCHAR},
      HISTORY = #{history,jdbcType=VARCHAR},
      BUSINESS_CONDITION = #{businessCondition,jdbcType=VARCHAR},
      BUSINESS_PLAN = #{businessPlan,jdbcType=VARCHAR},
      ADVANTAGE = #{advantage,jdbcType=VARCHAR},
      PRIMAL_PROBLEM = #{primalProblem,jdbcType=VARCHAR},
      MN_CODE = #{mnCode,jdbcType=VARCHAR},
      IS_COLLECT = #{isCollect,jdbcType=INTEGER},
      IS_LIMITPRICE = #{isLimitprice,jdbcType=INTEGER},
      COLLECT_TIME = #{collectTime,jdbcType=BIGINT},
      IS_PROFIT = #{isProfit,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      IS_VEDENG_MEMBER = #{isVedengMember,jdbcType=INTEGER},
      IS_SHOW_VIP_PRICE = #{isShowVipPrice,jdbcType=INTEGER},
      HAS_QUOTED = #{hasQuoted,jdbcType=INTEGER},
      IS_COOPERATED = #{isCooperated,jdbcType=INTEGER},
      ASSOCIATED_CUSTOMER_GROUP = #{associatedCustomerGroup,jdbcType=BIGINT},
      PUBLIC_CUSTOMER_EARLY_WARNING_COUNT = #{publicCustomerEarlyWarningCount,jdbcType=INTEGER}
    where TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="TRADER_CUSTOMER_ID" keyProperty="traderCustomerId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_CUSTOMER
    (TRADER_ID, TRADER_CUSTOMER_CATEGORY_ID, CUSTOMER_TYPE, CUSTOMER_NATURE, IS_ENABLE, 
      IS_TOP, AMOUNT, PERIOD_AMOUNT, PERIOD_DAY, DISABLE_TIME, CUSTOMER_FROM, FIRST_ENQUIRY_TYPE, 
      CUSTOMER_LEVEL, CUSTOMER_CATEGORY, TRADER_LEVEL, USER_EVALUATE, CUSTOMER_SCORE, 
      BASIC_MEDICAL_DEALER, IS_VIP, OWNERSHIP, MEDICAL_TYPE, HOSPITAL_LEVEL, EMPLOYEES, 
      ANNUAL_SALES, SALES_MODEL, REGISTERED_CAPITAL, REGISTERED_DATE, DIRECT_SELLING, 
      WHOLESALE, ORGANIZATION_CODE, UNIFIED_SOCIAL_CREDIT_IDENTIFIER, BUSINESS_SCOPE, 
      HISTORY_NAME, DISABLE_REASON, COMMENTS, FINANCE_COMMENTS, LOGISTICS_COMMENTS, BRIEF, 
      HISTORY, BUSINESS_CONDITION, BUSINESS_PLAN, ADVANTAGE, PRIMAL_PROBLEM, MN_CODE, 
      IS_COLLECT, IS_LIMITPRICE, COLLECT_TIME, IS_PROFIT, ADD_TIME, CREATOR, MOD_TIME, 
      UPDATER, IS_VEDENG_MEMBER, IS_SHOW_VIP_PRICE, HAS_QUOTED, IS_COOPERATED, ASSOCIATED_CUSTOMER_GROUP, 
      PUBLIC_CUSTOMER_EARLY_WARNING_COUNT)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.traderId,jdbcType=INTEGER}, #{item.traderCustomerCategoryId,jdbcType=INTEGER}, 
        #{item.customerType,jdbcType=INTEGER}, #{item.customerNature,jdbcType=INTEGER}, 
        #{item.isEnable,jdbcType=INTEGER}, #{item.isTop,jdbcType=INTEGER}, #{item.amount,jdbcType=DECIMAL}, 
        #{item.periodAmount,jdbcType=DECIMAL}, #{item.periodDay,jdbcType=INTEGER}, #{item.disableTime,jdbcType=BIGINT}, 
        #{item.customerFrom,jdbcType=INTEGER}, #{item.firstEnquiryType,jdbcType=INTEGER}, 
        #{item.customerLevel,jdbcType=INTEGER}, #{item.customerCategory,jdbcType=INTEGER}, 
        #{item.traderLevel,jdbcType=INTEGER}, #{item.userEvaluate,jdbcType=INTEGER}, #{item.customerScore,jdbcType=INTEGER}, 
        #{item.basicMedicalDealer,jdbcType=INTEGER}, #{item.isVip,jdbcType=INTEGER}, #{item.ownership,jdbcType=INTEGER}, 
        #{item.medicalType,jdbcType=INTEGER}, #{item.hospitalLevel,jdbcType=INTEGER}, #{item.employees,jdbcType=INTEGER}, 
        #{item.annualSales,jdbcType=INTEGER}, #{item.salesModel,jdbcType=INTEGER}, #{item.registeredCapital,jdbcType=VARCHAR}, 
        #{item.registeredDate,jdbcType=DATE}, #{item.directSelling,jdbcType=DECIMAL}, #{item.wholesale,jdbcType=DECIMAL}, 
        #{item.organizationCode,jdbcType=VARCHAR}, #{item.unifiedSocialCreditIdentifier,jdbcType=VARCHAR}, 
        #{item.businessScope,jdbcType=VARCHAR}, #{item.historyName,jdbcType=VARCHAR}, #{item.disableReason,jdbcType=VARCHAR}, 
        #{item.comments,jdbcType=VARCHAR}, #{item.financeComments,jdbcType=VARCHAR}, #{item.logisticsComments,jdbcType=VARCHAR}, 
        #{item.brief,jdbcType=VARCHAR}, #{item.history,jdbcType=VARCHAR}, #{item.businessCondition,jdbcType=VARCHAR}, 
        #{item.businessPlan,jdbcType=VARCHAR}, #{item.advantage,jdbcType=VARCHAR}, #{item.primalProblem,jdbcType=VARCHAR}, 
        #{item.mnCode,jdbcType=VARCHAR}, #{item.isCollect,jdbcType=INTEGER}, #{item.isLimitprice,jdbcType=INTEGER}, 
        #{item.collectTime,jdbcType=BIGINT}, #{item.isProfit,jdbcType=INTEGER}, #{item.addTime,jdbcType=BIGINT}, 
        #{item.creator,jdbcType=INTEGER}, #{item.modTime,jdbcType=BIGINT}, #{item.updater,jdbcType=INTEGER}, 
        #{item.isVedengMember,jdbcType=INTEGER}, #{item.isShowVipPrice,jdbcType=INTEGER}, 
        #{item.hasQuoted,jdbcType=INTEGER}, #{item.isCooperated,jdbcType=INTEGER}, #{item.associatedCustomerGroup,jdbcType=BIGINT}, 
        #{item.publicCustomerEarlyWarningCount,jdbcType=INTEGER})
    </foreach>
  </insert>

  <select id="selectByTraderName" resultMap="BaseResultMap">
    select TTC.TRADER_CUSTOMER_ID, TT.TRADER_ID
    from T_TRADER TT left join T_TRADER_CUSTOMER TTC on TTC.TRADER_ID = TT.TRADER_ID
    where TT.TRADER_NAME = #{traderName} and TRADER_CUSTOMER_ID>0 limit 1
  </select>



<!--auto generated by MybatisCodeHelper on 2024-02-03-->
  <select id="findByAll" resultType="com.vedeng.erp.trader.dto.TraderCustomerErpDto">
    select
    TRADER_ID,
    TRADER_NAME,
    LAST_COMMUNICATE_TIME,
    TRADER_CUSTOMER_ID,
    IS_ENABLE,
    IS_TOP,
    CUSTOMER_LEVEL,
    traderCustomerLevelGrade,
    traderCustomerLifeCycle,
    ADD_TIME,
    MOD_TIME,
    CUSTOMER_TYPE,
    CUSTOMER_NATURE,
    USER_ID,
    USERNAME
    from
    (
    SELECT
    ttc.TRADER_ID,
    t.TRADER_NAME,
    t.LAST_COMMUNICATE_TIME,
    ttc.TRADER_CUSTOMER_ID,
    ttc.IS_ENABLE,
    ttc.IS_TOP,
    ttc.CUSTOMER_LEVEL,
    tlfe.TRADER_LEVEL as traderCustomerLevelGrade,
    tlfe.LIFE_CYCLE as traderCustomerLifeCycle,
    ttc.ADD_TIME,
    ttc.MOD_TIME,
    ttc.CUSTOMER_TYPE,
    ttc.CUSTOMER_NATURE,
    tu.USER_ID,
    u.USERNAME
    FROM T_TRADER_CUSTOMER ttc
    LEFT JOIN T_R_TRADER_J_USER tu on  ttc.TRADER_ID =tu.TRADER_ID and tu.TRADER_TYPE = 1
    left JOIN T_TRADER t ON t.TRADER_ID = ttc.TRADER_ID
    <if test="listCustomerQuery.effectiveness != null||listCustomerQuery.skuType != null||listCustomerQuery.traderCustomerMainCategoryType != null||listCustomerQuery.traderCustomerOwnership != null||listCustomerQuery.traderCustomerDevelopLevel!=null">
      LEFT JOIN T_TRADER_CUSTOMER_MARKETING_PRINCIPAL tcmp ON tcmp.TRADER_ID=ttc.TRADER_ID and tcmp.TRADER_CUSTOMER_ID=ttc.TRADER_CUSTOMER_ID
    </if>
    LEFT JOIN DWH_TRADER_LIST_FILTER_ERP tlfe ON tlfe.TRADER_ID=ttc.TRADER_ID and tlfe.TRADER_CUSTOMER_ID = ttc.TRADER_CUSTOMER_ID
    left join T_USER u on u.USER_ID = tu.USER_ID
    <!-- 终端机构性质 -->
    <if test="listCustomerQuery.institutionNature != null ">
      join (
      select distinct TRADER_CUSTOMER_ID
      from T_TRADER_CUSTOMER_MARKETING_TERMINAL
      where
      FIND_IN_SET(#{listCustomerQuery.institutionNature, jdbcType=VARCHAR}, INSTITUTION_NATURE)
      ) TTCMTA on TTCMTA.TRADER_CUSTOMER_ID = ttc.TRADER_CUSTOMER_ID
    </if>
    <!-- 终端机构评级 -->
    <if test="listCustomerQuery.institutionLevel !=null and listCustomerQuery.institutionLevel.size() > 0 and !listCustomerQuery.institutionLevel.contains(-1) ">
      join (
      select distinct TRADER_CUSTOMER_ID
      from T_TRADER_CUSTOMER_MARKETING_TERMINAL
      where 1=1
      and(
      <foreach collection="listCustomerQuery.institutionLevel" item="institutionLevelId" separator="OR"
               index="index" open="(" close=")">
        FIND_IN_SET(#{institutionLevelId}, INSTITUTION_LEVEL)
      </foreach>
      )
      ) TTCMTB on TTCMTB.TRADER_CUSTOMER_ID = ttc.TRADER_CUSTOMER_ID
    </if>
    <if test="listCustomerQuery.traderCustomerMarketingTypeList != null and listCustomerQuery.traderCustomerMarketingTypeList.size() > 0">
      join (
      select distinct TRADER_CUSTOMER_ID
      from T_TRADER_CUSTOMER_MARKETING_TERMINAL
      where TRADER_CUSTOMER_MARKETING_TYPE in

      <foreach collection="listCustomerQuery.traderCustomerMarketingTypeList" item="traderCustomerMarketingType"
               index="index"
               open="(" close=")" separator=",">
        #{traderCustomerMarketingType}
      </foreach>
      <!--终端类型-->
      <if test="listCustomerQuery.institutionTypeList != null and listCustomerQuery.institutionTypeList.size() > 0">
        and (
        <foreach collection="listCustomerQuery.institutionTypeList" item="institutionType" separator="OR"
                 index="index" open="(" close=")">
          FIND_IN_SET(#{institutionType}, INSTITUTION_TYPE)
        </foreach>
        )
      </if>

      ) TTCMT on TTCMT.TRADER_CUSTOMER_ID = ttc.TRADER_CUSTOMER_ID
    </if>
    <where>
      <!--客户类型-->
      <if test="listCustomerQuery.customerTypeQuery!=null and listCustomerQuery.customerTypeQuery == 0">
        AND ttc.CUSTOMER_TYPE= 427 AND CUSTOMER_NATURE = 466
      </if>

      <if test="listCustomerQuery.customerTypeQuery!=null and listCustomerQuery.customerTypeQuery == 1">
        AND ttc.CUSTOMER_TYPE= 427 AND CUSTOMER_NATURE = 465
      </if>
      <if test="listCustomerQuery.customerTypeQuery!=null and listCustomerQuery.customerTypeQuery == 2">
        AND ttc.CUSTOMER_TYPE= 426 AND CUSTOMER_NATURE = 466
      </if>

      <if test="listCustomerQuery.customerTypeQuery!=null and listCustomerQuery.customerTypeQuery == 3">
        AND ttc.CUSTOMER_TYPE= 426 AND CUSTOMER_NATURE = 465
      </if>
      <!-- 经销商有效性 -->
      <if test="listCustomerQuery.effectiveness != null ">
        AND tcmp.EFFECTIVENESS = #{listCustomerQuery.effectiveness, jdbcType=INTEGER}
      </if>
      <!--筛选归属销售-->
      <if test="listCustomerQuery.attributableUserId !=null">
        and tu.USER_ID =#{listCustomerQuery.attributableUserId,jdbcType=INTEGER}
      </if>
      <!-- 主营商品类型 -->
      <if test="listCustomerQuery.skuType != null ">
        AND FIND_IN_SET(#{listCustomerQuery.skuType}, tcmp.SKU_TYPE)
      </if>
      <!-- 主营商品范畴类别 -->
      <if test="listCustomerQuery.traderCustomerMainCategoryType != null ">
        AND tcmp.SKU_SCOPE = #{listCustomerQuery.traderCustomerMainCategoryType, jdbcType=VARCHAR}
      </if>
      <!-- 销售类别 -->
      <if test="listCustomerQuery.traderCustomerOwnership != null ">
        AND tcmp.SALES_TYPE = #{listCustomerQuery.traderCustomerOwnership, jdbcType=VARCHAR}
      </if>
      <!-- 核心资源-代理品牌 -->
      <if test="listCustomerQuery.traderCustomerDevelopLevel == 1 ">
        AND tcmp.AGENCY_BRAND IS NOT NULL AND tcmp.AGENCY_BRAND <![CDATA[<> '']]>
      </if>
      <!-- 核心资源-代理商品 -->
      <if test="listCustomerQuery.traderCustomerDevelopLevel == 3 ">
        AND tcmp.AGENCY_SKU IS NOT NULL AND tcmp.AGENCY_SKU <![CDATA[<> '']]>
      </if>
      <!-- 核心资源-政府关系 -->
      <if test="listCustomerQuery.traderCustomerDevelopLevel == 2 ">
        AND tcmp.GOVERNMENT_RELATION IS NOT NULL AND tcmp.GOVERNMENT_RELATION <![CDATA[<> '']]>
      </if>
      <!-- 客户等级 -->
      <if test="listCustomerQuery.traderCustomerLevelGrade != null ">
        AND tlfe.TRADER_LEVEL = #{listCustomerQuery.traderCustomerLevelGrade, jdbcType=VARCHAR}
      </if>
      <if test="listCustomerQuery.userIdList !=null">
        and (tu.USER_ID in
        <foreach collection="listCustomerQuery.userIdList" item="userId" index="index" open="(" close=")" separator=",">
          #{userId}
        </foreach>
        OR ttc.TRADER_ID in
                (
        select DISTINCT TW.TRADER_ID
        FROM T_CUSTOMER_REGION_SALE TS
        JOIN T_TRADER_WORKAREA TW ON TS.REGION_ID = TW.AREA_ID
        JOIN T_R_TRADER_J_USER TRJ
        ON TRJ.TRADER_ID = TW.TRADER_ID AND TRJ.USER_ID = TS.USER_ID and TRJ.TRADER_TYPE = 1
        WHERE TS.USER_ID_DOWN IN
                      <foreach collection="listCustomerQuery.userIdList" item="userId" index="index" open="(" close=")" separator=",">
                        #{userId}
                      </foreach>
                )
        )
      </if>
      <if test="listCustomerQuery.customerName !=null and listCustomerQuery.customerName !='' ">
        and t.TRADER_NAME like CONCAT('%',#{listCustomerQuery.customerName},'%' )
      </if>
    </where>


    <if test="listCustomerQuery.attributableUserId ==null">
      union all

      SELECT
      ttc.TRADER_ID,
      t.TRADER_NAME,
      t.LAST_COMMUNICATE_TIME,
      ttc.TRADER_CUSTOMER_ID,
      ttc.IS_ENABLE,
      ttc.IS_TOP,
      ttc.CUSTOMER_LEVEL,
      tlfe.TRADER_LEVEL as traderCustomerLevelGrade,
      tlfe.LIFE_CYCLE as traderCustomerLifeCycle,
      ttc.ADD_TIME,
      ttc.MOD_TIME,
      ttc.CUSTOMER_TYPE,
      ttc.CUSTOMER_NATURE,
      tu.USER_ID,
      u.USERNAME
      FROM T_TRADER_CUSTOMER ttc
      LEFT JOIN T_R_TRADER_J_USER tu on  ttc.TRADER_ID =tu.TRADER_ID and tu.TRADER_TYPE = 1
      LEFT JOIN T_R_SALES_J_TRADER TRSJT ON ttc.TRADER_ID = TRSJT.TRADER_ID AND TRSJT.IS_DELETED = 0
      left JOIN T_TRADER t ON t.TRADER_ID = ttc.TRADER_ID
      <if test="listCustomerQuery.effectiveness != null||listCustomerQuery.skuType != null||listCustomerQuery.traderCustomerMainCategoryType != null||listCustomerQuery.traderCustomerOwnership != null||listCustomerQuery.traderCustomerDevelopLevel!=null">
        LEFT JOIN T_TRADER_CUSTOMER_MARKETING_PRINCIPAL tcmp ON tcmp.TRADER_ID=ttc.TRADER_ID and tcmp.TRADER_CUSTOMER_ID=ttc.TRADER_CUSTOMER_ID
      </if>
      LEFT JOIN DWH_TRADER_LIST_FILTER_ERP tlfe ON tlfe.TRADER_ID=ttc.TRADER_ID and tlfe.TRADER_CUSTOMER_ID = ttc.TRADER_CUSTOMER_ID
      left join T_USER u on u.USER_ID = tu.USER_ID
      <!-- 终端机构性质 -->
      <if test="listCustomerQuery.institutionNature != null ">
        join (
        select distinct TRADER_CUSTOMER_ID
        from T_TRADER_CUSTOMER_MARKETING_TERMINAL
        where
        FIND_IN_SET(#{listCustomerQuery.institutionNature, jdbcType=VARCHAR}, INSTITUTION_NATURE)
        ) TTCMTA on TTCMTA.TRADER_CUSTOMER_ID = ttc.TRADER_CUSTOMER_ID
      </if>
      <!-- 终端机构评级 -->
      <if test="listCustomerQuery.institutionLevel !=null and listCustomerQuery.institutionLevel.size() > 0 and !listCustomerQuery.institutionLevel.contains(-1) ">
        join (
        select distinct TRADER_CUSTOMER_ID
        from T_TRADER_CUSTOMER_MARKETING_TERMINAL
        where 1=1
        and(
        <foreach collection="listCustomerQuery.institutionLevel" item="institutionLevelId" separator="OR"
                 index="index" open="(" close=")">
          FIND_IN_SET(#{institutionLevelId}, INSTITUTION_LEVEL)
        </foreach>
        )
        ) TTCMTB on TTCMTB.TRADER_CUSTOMER_ID = ttc.TRADER_CUSTOMER_ID
      </if>
      <if test="listCustomerQuery.traderCustomerMarketingTypeList != null and listCustomerQuery.traderCustomerMarketingTypeList.size() > 0">
        join (
        select distinct TRADER_CUSTOMER_ID
        from T_TRADER_CUSTOMER_MARKETING_TERMINAL
        where TRADER_CUSTOMER_MARKETING_TYPE in

        <foreach collection="listCustomerQuery.traderCustomerMarketingTypeList" item="traderCustomerMarketingType"
                 index="index"
                 open="(" close=")" separator=",">
          #{traderCustomerMarketingType}
        </foreach>
        <!--终端类型-->
        <if test="listCustomerQuery.institutionTypeList != null and listCustomerQuery.institutionTypeList.size() > 0">
          and (
          <foreach collection="listCustomerQuery.institutionTypeList" item="institutionType" separator="OR"
                   index="index" open="(" close=")">
            FIND_IN_SET(#{institutionType}, INSTITUTION_TYPE)
          </foreach>
          )
        </if>

        ) TTCMT on TTCMT.TRADER_CUSTOMER_ID = ttc.TRADER_CUSTOMER_ID
      </if>
      <where>
        <!--客户类型-->
        <if test="listCustomerQuery.customerTypeQuery!=null and listCustomerQuery.customerTypeQuery == 0">
          AND ttc.CUSTOMER_TYPE= 427 AND CUSTOMER_NATURE = 466
        </if>

        <if test="listCustomerQuery.customerTypeQuery!=null and listCustomerQuery.customerTypeQuery == 1">
          AND ttc.CUSTOMER_TYPE= 427 AND CUSTOMER_NATURE = 465
        </if>

        <if test="listCustomerQuery.customerTypeQuery!=null and listCustomerQuery.customerTypeQuery == 2">
          AND ttc.CUSTOMER_TYPE= 426 AND CUSTOMER_NATURE = 466
        </if>

        <if test="listCustomerQuery.customerTypeQuery!=null and listCustomerQuery.customerTypeQuery == 3">
          AND ttc.CUSTOMER_TYPE= 426 AND CUSTOMER_NATURE = 465
        </if>
        <!-- 经销商有效性 -->
        <if test="listCustomerQuery.effectiveness != null ">
          AND tcmp.EFFECTIVENESS = #{listCustomerQuery.effectiveness, jdbcType=INTEGER}
        </if>
        <!--筛选归属销售-->
        <if test="listCustomerQuery.attributableUserId !=null">
          and tu.USER_ID =#{listCustomerQuery.attributableUserId,jdbcType=INTEGER}
        </if>
        <!-- 主营商品类型 -->
        <if test="listCustomerQuery.skuType != null ">
          AND FIND_IN_SET(#{listCustomerQuery.skuType}, tcmp.SKU_TYPE)
        </if>
        <!-- 主营商品范畴类别 -->
        <if test="listCustomerQuery.traderCustomerMainCategoryType != null ">
          AND tcmp.SKU_SCOPE = #{listCustomerQuery.traderCustomerMainCategoryType, jdbcType=VARCHAR}
        </if>
        <!-- 销售类别 -->
        <if test="listCustomerQuery.traderCustomerOwnership != null ">
          AND tcmp.SALES_TYPE = #{listCustomerQuery.traderCustomerOwnership, jdbcType=VARCHAR}
        </if>
        <!-- 核心资源-代理品牌 -->
        <if test="listCustomerQuery.traderCustomerDevelopLevel == 1 ">
          AND tcmp.AGENCY_BRAND IS NOT NULL AND tcmp.AGENCY_BRAND <![CDATA[<> '']]>
        </if>
        <!-- 核心资源-代理商品 -->
        <if test="listCustomerQuery.traderCustomerDevelopLevel == 3 ">
          AND tcmp.AGENCY_SKU IS NOT NULL AND tcmp.AGENCY_SKU <![CDATA[<> '']]>
        </if>
        <!-- 核心资源-政府关系 -->
        <if test="listCustomerQuery.traderCustomerDevelopLevel == 2 ">
          AND tcmp.GOVERNMENT_RELATION IS NOT NULL AND tcmp.GOVERNMENT_RELATION <![CDATA[<> '']]>
        </if>
        <!-- 客户等级 -->
        <if test="listCustomerQuery.traderCustomerLevelGrade != null ">
          AND tlfe.TRADER_LEVEL = #{listCustomerQuery.traderCustomerLevelGrade, jdbcType=VARCHAR}
        </if>
        <if test="listCustomerQuery.userIdList !=null">
          and ( TRSJT.SALE_USER_ID in
          <foreach collection="listCustomerQuery.userIdList" item="sharedSaleId" index="index" open="(" close=")" separator=",">
            #{sharedSaleId}
          </foreach>
          )
        </if>
        <if test="listCustomerQuery.customerName !=null and listCustomerQuery.customerName !='' ">
          and t.TRADER_NAME like CONCAT('%',#{listCustomerQuery.customerName},'%' )
        </if>
      </where>
    </if>
    ) as A

    group by TRADER_CUSTOMER_ID
    <choose>
      <when test="listCustomerQuery.addTimeOrderBy ==null  and  listCustomerQuery.modTimeOrderBy ==null ">
        ORDER BY IS_TOP DESC, IS_ENABLE DESC,ADD_TIME DESC,MOD_TIME DESC
      </when>
      <when test="listCustomerQuery.addTimeOrderBy !=null and listCustomerQuery.addTimeOrderBy eq 1 ">
        ORDER BY ADD_TIME DESC
      </when>
      <when test=" listCustomerQuery.addTimeOrderBy !=null and listCustomerQuery.addTimeOrderBy eq 0">
        ORDER BY ADD_TIME ASC
      </when>
      <when test="listCustomerQuery.modTimeOrderBy != null and listCustomerQuery.modTimeOrderBy eq 0 ">
        ORDER BY MOD_TIME ASC
      </when>
      <when test="listCustomerQuery.modTimeOrderBy != null and listCustomerQuery.modTimeOrderBy eq 1 ">
        ORDER BY MOD_TIME DESC
      </when>
    </choose>


  </select>
</mapper>
