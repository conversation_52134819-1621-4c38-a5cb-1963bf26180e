package com.vedeng.erp.kingdee.batch.writer;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.batch.dto.BatchPayExpensesDto;
import com.vedeng.erp.kingdee.dto.KingDeePayExpensesDto;
import com.vedeng.erp.kingdee.service.KingDeePayExpensesApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购负向应付单以及票推送
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchExpenseAfterSalePayableOrderWriterService extends BaseWriter<BatchPayExpensesDto> {

    @Autowired
    private KingDeePayExpensesApiService kingDeePayExpensesApiService;

    @Override
    public void doWrite(BatchPayExpensesDto item, JobParameters params, ExecutionContext stepContext) throws Exception {
        KingDeePayExpensesDto payExpensesDto = item.getKingDeePayExpensesDto();
        payExpensesDto.setKingDeeBizEnums(KingDeeBizEnums.savePayExpenses);
        log.info("采购费用负向应付单推送{}", JSON.toJSONString(payExpensesDto));
        kingDeePayExpensesApiService.register(payExpensesDto,true);
    }

}
