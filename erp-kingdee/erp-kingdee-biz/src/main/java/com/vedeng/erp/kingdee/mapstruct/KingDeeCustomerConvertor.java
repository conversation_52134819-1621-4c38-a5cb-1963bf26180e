package com.vedeng.erp.kingdee.mapstruct;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.kingdee.domain.entity.KingDeeCustomerEntity;
import com.vedeng.erp.kingdee.domain.entity.KingDeeSupplierEntity;
import com.vedeng.erp.kingdee.dto.KingDeeCustomerDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeCustomerDto;
import com.vedeng.erp.kingdee.dto.KingDeeMaterialSubHeadEntityDto;
import com.vedeng.erp.kingdee.dto.KingDeeSupplierDto;
import org.mapstruct.*;

import java.util.List;

/**
 * dto 转 entity
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface KingDeeCustomerConvertor extends BaseMapStruct<KingDeeCustomerEntity, KingDeeCustomerDto> {


    @Override
    @Mapping(target = "FShortName",defaultValue = "")
    @Mapping(target = "FCustTypeId",defaultValue = "")
    @Mapping(target = "FAddress",defaultValue = "")
    @Mapping(target = "FInvoiceTitle",defaultValue = "")
    @Mapping(target = "FTaxRegisterCode",defaultValue = "")
    @Mapping(target = "FInvoiceBankName",defaultValue = "")
    @Mapping(target = "FInvoiceTel",defaultValue = "")
    @Mapping(target = "FInvoiceBankAccount",defaultValue = "")
    @Mapping(target = "FInvoiceAddress",defaultValue = "")
    @Mapping(target = "FGroup",defaultValue = "")
    @Mapping(target = "FTradingCurrid",defaultValue = "")
    @Mapping(target = "FSettleTypeId",defaultValue = "")
    @Mapping(target = "FSaldeptId",defaultValue = "")
    @Mapping(target = "FTaxRate",defaultValue = "")
    @Mapping(target = "FQZOKTradeTerms",defaultValue = "")
    @Mapping(target = "fqzoksyyygt", source = "FQzokSyyygt")
    @Mapping(target = "fqzokssjt", source = "FQzokSsjt")
    @Mapping(target = "fqzokxym", source = "FQzokXym")
    @Mapping(target = "fqzokkhxztext", source = "FQzokKhxztext")
    @Mapping(target = "fqzokzdkhfltext", source = "FQzokZdkhfltext")
    @Mapping(target = "fqzokyljgfltext", source = "FQzokYljgfltext")
    @Mapping(target = "fqzokkhxfltext", source = "FQzokKhxfltext")
    @Mapping(target = "fqzokyydjtext", source = "FQzokYydjtext")
    @Mapping(target = "fqzokkhdj", source = "FQzokKhdj")
    @Mapping(target = "ftBdCustbank", source = "FT_BD_CUSTBANK", qualifiedByName = "entryListToStr")
    KingDeeCustomerEntity toEntity(KingDeeCustomerDto dto);

    /**
     * dto 原对象中 转 Str
     *
     * @param source 对象
     * @return Str
     */
    @Named("entryListToStr")
    default String entryListToStr(List<KingDeeCustomerDetailDto> source) {
        if (source == null) {
            return null;
        }
        return JSON.toJSONString(source);
    }
}
