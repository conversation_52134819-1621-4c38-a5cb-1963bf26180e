package com.vedeng.trader.dto;

import com.vedeng.customerbillperiod.dto.CustomerBillPeriodCreditHistoryDto;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: daniel
 * @Date: 2022/5/11 16 41
 * @Description: 客户历史使用账期记录
 */
@Data
public class CustomerBillPeriodCreditHistoryVo extends CustomerBillPeriodCreditHistoryDto {

    /**
     * 使用了账期支付，但是未完全归还的订单的总额
     */
    private BigDecimal orderListAmount;

    /**
     * 历史使用账期支付的订单的总额
     */
    private BigDecimal historyOrderListAmount;

}
