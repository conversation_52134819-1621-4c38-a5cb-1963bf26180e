package com.vedeng.goods.utils;

import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.validator.ValidateConstants;

import java.util.StringJoiner;
import java.util.regex.Pattern;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class NamingUtils {

    private static final Pattern KEY_VALUE_DELIMITER_PATTERN = Pattern.compile("[:：]");

    private static final String DEFAULT_DELIMITER = ErpConst.Symbol.SPACE;

    private static final String EMPTY_VALUE = "";

    public static final String DEFAULT_NAME_PLACEHOLDER = EMPTY_VALUE;

    public static final int MAX_GOODS_NAME_LENGTH = ValidateConstants.MAX_SPU_NAME_LENGTH;

    public static String normalize(String input, String replacement) {
        if (checkEmpty(input)) {
            return input;
        }

        if (checkEmpty(replacement)) {
            return input;
        }

        String inputAftTrim = input.trim();
        if (inputAftTrim.length() == 0) {
            return inputAftTrim;
        }

        return KEY_VALUE_DELIMITER_PATTERN.matcher(inputAftTrim).replaceAll(replacement);
    }

    public static String joinWithSpace(String former, String back) {
        return join(DEFAULT_DELIMITER, former, back);
    }


    public static String join(String delimiter, String... elements) {
        StringJoiner joiner = new StringJoiner(delimiter);
        for (String element : elements) {
            element = trim(element);
            if (element.length() == 0) {
                continue;
            }
            joiner.add(element);
        }
        return joiner.toString();
    }


    private static boolean checkEmpty(String element) {
        return element == null || element.length() == 0;
    }

    public static String trim(String input) {
        if (checkEmpty(input)) {
            return EMPTY_VALUE;
        }
        input = input.trim();
        return checkEmpty(input) ? EMPTY_VALUE : input;
    }
}

