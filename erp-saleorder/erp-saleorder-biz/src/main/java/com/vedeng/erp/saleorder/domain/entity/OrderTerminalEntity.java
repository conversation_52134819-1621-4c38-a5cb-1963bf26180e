package com.vedeng.erp.saleorder.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 订单终端信息 (0:销售订单 1:商机单 2:报价单)
 * @date 2022/12/22 13:48
 */
@Getter
@Setter
public class OrderTerminalEntity extends BaseEntity {
    /**
     * 主键
     */
    private Integer orderTerminalId;

    /**
     * 业务id
     */
    private Integer businessId;

    /**
     * 业务类型
     * 0:销售订单
     * 1:商机单
     * 2:报价单
     */
    private Integer businessType;

    /**
     * 编号
     */
    private String businessNo;

    /**
     * 终端名称
     */
    private String terminalName;

    /**
     * 大数据终端id（通过大数据查询出对应的终端信息）
     */
    private String dwhTerminalId;

    /**
     * 统一社会信用代码
     */
    private String unifiedSocialCreditIdentifier;

    /**
     * 组织机构代码
     */
    private String organizationCode;

    /**
     * 是否删除 （0否1是）
     */
    private Integer isDeleted;

    /**
     * 省
     */
    private Integer provinceId;

    /**
     * 市
     */
    private Integer cityId;

    /**
     * 区
     */
    private Integer areaId;

    /**
     * 省名
     */
    private String provinceName;

    /**
     * 市名
     */
    private String cityName;

    /**
     * 区名
     */
    private String areaName;

    /**
     * 终端性质-字典值(公立等级、公立基层、非公等级、非公基层、非公集团、应急、院外)
     */
    private Integer terminalTraderNature;

    /**
     * 详细地址
     */
    private String detailAddress;

    /**
     * 终端性质描述
     */
    private String natureTypeName;

    /**
     * 终端联系人
     */
    private String contractUser;

    /**
     * 联系方式
     */
    private String contractMobile;

    /**
     * 修改前的终端名称
     */
    private String beforeTerminalName;


}