<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.finance.mapper.AcceptanceBillMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.finance.domain.entity.AcceptanceBillEntity">
    <!--@mbg.generated-->
    <!--@Table T_ACCEPTANCE_BILL-->
    <id column="ACCEPTANCE_BIL_ID" jdbcType="BIGINT" property="acceptanceBilId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="BANK_BILL_ID" jdbcType="INTEGER" property="bankBillId" />
    <result column="ACCEPTANCE_BANK" jdbcType="INTEGER" property="acceptanceBank" />
    <result column="BILL_NUMBER" jdbcType="VARCHAR" property="billNumber" />
    <result column="SUB_BILL_INTERVAL_START" jdbcType="VARCHAR" property="subBillIntervalStart" />
    <result column="SUB_BILL_INTERVAL_END" jdbcType="VARCHAR" property="subBillIntervalEnd" />
    <result column="BILL_TYPE" jdbcType="VARCHAR" property="billType" />
    <result column="ISSUE_DATE" jdbcType="TIMESTAMP" property="issueDate" />
    <result column="MATURITY_DATE" jdbcType="TIMESTAMP" property="maturityDate" />
    <result column="BILL_AMOUNT" jdbcType="DECIMAL" property="billAmount" />
    <result column="ACCEPTANCE_DATE" jdbcType="TIMESTAMP" property="acceptanceDate" />
    <result column="TRANSACTION_CONTRACT_NO" jdbcType="VARCHAR" property="transactionContractNo" />
    <result column="DRAWER_NAME" jdbcType="VARCHAR" property="drawerName" />
    <result column="DRAWER_ACCOUNT" jdbcType="VARCHAR" property="drawerAccount" />
    <result column="DRAWER_BANK_NAME" jdbcType="VARCHAR" property="drawerBankName" />
    <result column="DRAWER_BANK_CODE" jdbcType="VARCHAR" property="drawerBankCode" />
    <result column="PAYEE_NAME" jdbcType="VARCHAR" property="payeeName" />
    <result column="PAYEE_ACCOUNT" jdbcType="VARCHAR" property="payeeAccount" />
    <result column="PAYEE_BANK_NAME" jdbcType="VARCHAR" property="payeeBankName" />
    <result column="PAYEE_BANK_CODE" jdbcType="VARCHAR" property="payeeBankCode" />
    <result column="ACCEPTOR_NAME" jdbcType="VARCHAR" property="acceptorName" />
    <result column="ACCEPTOR_ACCOUNT" jdbcType="VARCHAR" property="acceptorAccount" />
    <result column="ACCEPTOR_BANK_NAME" jdbcType="VARCHAR" property="acceptorBankName" />
    <result column="ACCEPTOR_BANK_CODE" jdbcType="VARCHAR" property="acceptorBankCode" />
    <result column="BILL_STATUS" jdbcType="INTEGER" property="billStatus" />
    <result column="DISCOUNT_STATUS" jdbcType="INTEGER" property="discountStatus" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ACCEPTANCE_BIL_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, 
    BANK_BILL_ID, ACCEPTANCE_BANK, BILL_NUMBER, SUB_BILL_INTERVAL_START, SUB_BILL_INTERVAL_END, 
    BILL_TYPE, ISSUE_DATE, MATURITY_DATE, BILL_AMOUNT, ACCEPTANCE_DATE, TRANSACTION_CONTRACT_NO, 
    DRAWER_NAME, DRAWER_ACCOUNT, DRAWER_BANK_NAME, DRAWER_BANK_CODE, PAYEE_NAME, PAYEE_ACCOUNT, 
    PAYEE_BANK_NAME, PAYEE_BANK_CODE, ACCEPTOR_NAME, ACCEPTOR_ACCOUNT, ACCEPTOR_BANK_NAME, 
    ACCEPTOR_BANK_CODE, BILL_STATUS, DISCOUNT_STATUS
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_ACCEPTANCE_BILL
    where ACCEPTANCE_BIL_ID = #{acceptanceBilId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_ACCEPTANCE_BILL
    where ACCEPTANCE_BIL_ID = #{acceptanceBilId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="ACCEPTANCE_BIL_ID" keyProperty="acceptanceBilId" parameterType="com.vedeng.erp.finance.domain.entity.AcceptanceBillEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_ACCEPTANCE_BILL (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      BANK_BILL_ID, ACCEPTANCE_BANK, BILL_NUMBER, 
      SUB_BILL_INTERVAL_START, SUB_BILL_INTERVAL_END, 
      BILL_TYPE, ISSUE_DATE, MATURITY_DATE, 
      BILL_AMOUNT, ACCEPTANCE_DATE, TRANSACTION_CONTRACT_NO, 
      DRAWER_NAME, DRAWER_ACCOUNT, DRAWER_BANK_NAME, 
      DRAWER_BANK_CODE, PAYEE_NAME, PAYEE_ACCOUNT, 
      PAYEE_BANK_NAME, PAYEE_BANK_CODE, ACCEPTOR_NAME, 
      ACCEPTOR_ACCOUNT, ACCEPTOR_BANK_NAME, ACCEPTOR_BANK_CODE, 
      BILL_STATUS, DISCOUNT_STATUS)
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{bankBillId,jdbcType=INTEGER}, #{acceptanceBank,jdbcType=INTEGER}, #{billNumber,jdbcType=VARCHAR}, 
      #{subBillIntervalStart,jdbcType=VARCHAR}, #{subBillIntervalEnd,jdbcType=VARCHAR}, 
      #{billType,jdbcType=VARCHAR}, #{issueDate,jdbcType=TIMESTAMP}, #{maturityDate,jdbcType=TIMESTAMP}, 
      #{billAmount,jdbcType=DECIMAL}, #{acceptanceDate,jdbcType=TIMESTAMP}, #{transactionContractNo,jdbcType=VARCHAR}, 
      #{drawerName,jdbcType=VARCHAR}, #{drawerAccount,jdbcType=VARCHAR}, #{drawerBankName,jdbcType=VARCHAR}, 
      #{drawerBankCode,jdbcType=VARCHAR}, #{payeeName,jdbcType=VARCHAR}, #{payeeAccount,jdbcType=VARCHAR}, 
      #{payeeBankName,jdbcType=VARCHAR}, #{payeeBankCode,jdbcType=VARCHAR}, #{acceptorName,jdbcType=VARCHAR}, 
      #{acceptorAccount,jdbcType=VARCHAR}, #{acceptorBankName,jdbcType=VARCHAR}, #{acceptorBankCode,jdbcType=VARCHAR}, 
      #{billStatus,jdbcType=INTEGER}, #{discountStatus,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="ACCEPTANCE_BIL_ID" keyProperty="acceptanceBilId" parameterType="com.vedeng.erp.finance.domain.entity.AcceptanceBillEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_ACCEPTANCE_BILL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="bankBillId != null">
        BANK_BILL_ID,
      </if>
      <if test="acceptanceBank != null">
        ACCEPTANCE_BANK,
      </if>
      <if test="billNumber != null and billNumber != ''">
        BILL_NUMBER,
      </if>
      <if test="subBillIntervalStart != null and subBillIntervalStart != ''">
        SUB_BILL_INTERVAL_START,
      </if>
      <if test="subBillIntervalEnd != null and subBillIntervalEnd != ''">
        SUB_BILL_INTERVAL_END,
      </if>
      <if test="billType != null and billType != ''">
        BILL_TYPE,
      </if>
      <if test="issueDate != null">
        ISSUE_DATE,
      </if>
      <if test="maturityDate != null">
        MATURITY_DATE,
      </if>
      <if test="billAmount != null">
        BILL_AMOUNT,
      </if>
      <if test="acceptanceDate != null">
        ACCEPTANCE_DATE,
      </if>
      <if test="transactionContractNo != null and transactionContractNo != ''">
        TRANSACTION_CONTRACT_NO,
      </if>
      <if test="drawerName != null and drawerName != ''">
        DRAWER_NAME,
      </if>
      <if test="drawerAccount != null and drawerAccount != ''">
        DRAWER_ACCOUNT,
      </if>
      <if test="drawerBankName != null and drawerBankName != ''">
        DRAWER_BANK_NAME,
      </if>
      <if test="drawerBankCode != null and drawerBankCode != ''">
        DRAWER_BANK_CODE,
      </if>
      <if test="payeeName != null and payeeName != ''">
        PAYEE_NAME,
      </if>
      <if test="payeeAccount != null and payeeAccount != ''">
        PAYEE_ACCOUNT,
      </if>
      <if test="payeeBankName != null and payeeBankName != ''">
        PAYEE_BANK_NAME,
      </if>
      <if test="payeeBankCode != null and payeeBankCode != ''">
        PAYEE_BANK_CODE,
      </if>
      <if test="acceptorName != null and acceptorName != ''">
        ACCEPTOR_NAME,
      </if>
      <if test="acceptorAccount != null and acceptorAccount != ''">
        ACCEPTOR_ACCOUNT,
      </if>
      <if test="acceptorBankName != null and acceptorBankName != ''">
        ACCEPTOR_BANK_NAME,
      </if>
      <if test="acceptorBankCode != null and acceptorBankCode != ''">
        ACCEPTOR_BANK_CODE,
      </if>
      <if test="billStatus != null">
        BILL_STATUS,
      </if>
      <if test="discountStatus != null">
        DISCOUNT_STATUS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="bankBillId != null">
        #{bankBillId,jdbcType=INTEGER},
      </if>
      <if test="acceptanceBank != null">
        #{acceptanceBank,jdbcType=INTEGER},
      </if>
      <if test="billNumber != null and billNumber != ''">
        #{billNumber,jdbcType=VARCHAR},
      </if>
      <if test="subBillIntervalStart != null and subBillIntervalStart != ''">
        #{subBillIntervalStart,jdbcType=VARCHAR},
      </if>
      <if test="subBillIntervalEnd != null and subBillIntervalEnd != ''">
        #{subBillIntervalEnd,jdbcType=VARCHAR},
      </if>
      <if test="billType != null and billType != ''">
        #{billType,jdbcType=VARCHAR},
      </if>
      <if test="issueDate != null">
        #{issueDate,jdbcType=TIMESTAMP},
      </if>
      <if test="maturityDate != null">
        #{maturityDate,jdbcType=TIMESTAMP},
      </if>
      <if test="billAmount != null">
        #{billAmount,jdbcType=DECIMAL},
      </if>
      <if test="acceptanceDate != null">
        #{acceptanceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="transactionContractNo != null and transactionContractNo != ''">
        #{transactionContractNo,jdbcType=VARCHAR},
      </if>
      <if test="drawerName != null and drawerName != ''">
        #{drawerName,jdbcType=VARCHAR},
      </if>
      <if test="drawerAccount != null and drawerAccount != ''">
        #{drawerAccount,jdbcType=VARCHAR},
      </if>
      <if test="drawerBankName != null and drawerBankName != ''">
        #{drawerBankName,jdbcType=VARCHAR},
      </if>
      <if test="drawerBankCode != null and drawerBankCode != ''">
        #{drawerBankCode,jdbcType=VARCHAR},
      </if>
      <if test="payeeName != null and payeeName != ''">
        #{payeeName,jdbcType=VARCHAR},
      </if>
      <if test="payeeAccount != null and payeeAccount != ''">
        #{payeeAccount,jdbcType=VARCHAR},
      </if>
      <if test="payeeBankName != null and payeeBankName != ''">
        #{payeeBankName,jdbcType=VARCHAR},
      </if>
      <if test="payeeBankCode != null and payeeBankCode != ''">
        #{payeeBankCode,jdbcType=VARCHAR},
      </if>
      <if test="acceptorName != null and acceptorName != ''">
        #{acceptorName,jdbcType=VARCHAR},
      </if>
      <if test="acceptorAccount != null and acceptorAccount != ''">
        #{acceptorAccount,jdbcType=VARCHAR},
      </if>
      <if test="acceptorBankName != null and acceptorBankName != ''">
        #{acceptorBankName,jdbcType=VARCHAR},
      </if>
      <if test="acceptorBankCode != null and acceptorBankCode != ''">
        #{acceptorBankCode,jdbcType=VARCHAR},
      </if>
      <if test="billStatus != null">
        #{billStatus,jdbcType=INTEGER},
      </if>
      <if test="discountStatus != null">
        #{discountStatus,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.finance.domain.entity.AcceptanceBillEntity">
    <!--@mbg.generated-->
    update T_ACCEPTANCE_BILL
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="bankBillId != null">
        BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER},
      </if>
      <if test="acceptanceBank != null">
        ACCEPTANCE_BANK = #{acceptanceBank,jdbcType=INTEGER},
      </if>
      <if test="billNumber != null and billNumber != ''">
        BILL_NUMBER = #{billNumber,jdbcType=VARCHAR},
      </if>
      <if test="subBillIntervalStart != null and subBillIntervalStart != ''">
        SUB_BILL_INTERVAL_START = #{subBillIntervalStart,jdbcType=VARCHAR},
      </if>
      <if test="subBillIntervalEnd != null and subBillIntervalEnd != ''">
        SUB_BILL_INTERVAL_END = #{subBillIntervalEnd,jdbcType=VARCHAR},
      </if>
      <if test="billType != null and billType != ''">
        BILL_TYPE = #{billType,jdbcType=VARCHAR},
      </if>
      <if test="issueDate != null">
        ISSUE_DATE = #{issueDate,jdbcType=TIMESTAMP},
      </if>
      <if test="maturityDate != null">
        MATURITY_DATE = #{maturityDate,jdbcType=TIMESTAMP},
      </if>
      <if test="billAmount != null">
        BILL_AMOUNT = #{billAmount,jdbcType=DECIMAL},
      </if>
      <if test="acceptanceDate != null">
        ACCEPTANCE_DATE = #{acceptanceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="transactionContractNo != null and transactionContractNo != ''">
        TRANSACTION_CONTRACT_NO = #{transactionContractNo,jdbcType=VARCHAR},
      </if>
      <if test="drawerName != null and drawerName != ''">
        DRAWER_NAME = #{drawerName,jdbcType=VARCHAR},
      </if>
      <if test="drawerAccount != null and drawerAccount != ''">
        DRAWER_ACCOUNT = #{drawerAccount,jdbcType=VARCHAR},
      </if>
      <if test="drawerBankName != null and drawerBankName != ''">
        DRAWER_BANK_NAME = #{drawerBankName,jdbcType=VARCHAR},
      </if>
      <if test="drawerBankCode != null and drawerBankCode != ''">
        DRAWER_BANK_CODE = #{drawerBankCode,jdbcType=VARCHAR},
      </if>
      <if test="payeeName != null and payeeName != ''">
        PAYEE_NAME = #{payeeName,jdbcType=VARCHAR},
      </if>
      <if test="payeeAccount != null and payeeAccount != ''">
        PAYEE_ACCOUNT = #{payeeAccount,jdbcType=VARCHAR},
      </if>
      <if test="payeeBankName != null and payeeBankName != ''">
        PAYEE_BANK_NAME = #{payeeBankName,jdbcType=VARCHAR},
      </if>
      <if test="payeeBankCode != null and payeeBankCode != ''">
        PAYEE_BANK_CODE = #{payeeBankCode,jdbcType=VARCHAR},
      </if>
      <if test="acceptorName != null and acceptorName != ''">
        ACCEPTOR_NAME = #{acceptorName,jdbcType=VARCHAR},
      </if>
      <if test="acceptorAccount != null and acceptorAccount != ''">
        ACCEPTOR_ACCOUNT = #{acceptorAccount,jdbcType=VARCHAR},
      </if>
      <if test="acceptorBankName != null and acceptorBankName != ''">
        ACCEPTOR_BANK_NAME = #{acceptorBankName,jdbcType=VARCHAR},
      </if>
      <if test="acceptorBankCode != null and acceptorBankCode != ''">
        ACCEPTOR_BANK_CODE = #{acceptorBankCode,jdbcType=VARCHAR},
      </if>
      <if test="billStatus != null">
        BILL_STATUS = #{billStatus,jdbcType=INTEGER},
      </if>
      <if test="discountStatus != null">
        DISCOUNT_STATUS = #{discountStatus,jdbcType=INTEGER},
      </if>
    </set>
    where ACCEPTANCE_BIL_ID = #{acceptanceBilId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.finance.domain.entity.AcceptanceBillEntity">
    <!--@mbg.generated-->
    update T_ACCEPTANCE_BILL
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER},
      ACCEPTANCE_BANK = #{acceptanceBank,jdbcType=INTEGER},
      BILL_NUMBER = #{billNumber,jdbcType=VARCHAR},
      SUB_BILL_INTERVAL_START = #{subBillIntervalStart,jdbcType=VARCHAR},
      SUB_BILL_INTERVAL_END = #{subBillIntervalEnd,jdbcType=VARCHAR},
      BILL_TYPE = #{billType,jdbcType=VARCHAR},
      ISSUE_DATE = #{issueDate,jdbcType=TIMESTAMP},
      MATURITY_DATE = #{maturityDate,jdbcType=TIMESTAMP},
      BILL_AMOUNT = #{billAmount,jdbcType=DECIMAL},
      ACCEPTANCE_DATE = #{acceptanceDate,jdbcType=TIMESTAMP},
      TRANSACTION_CONTRACT_NO = #{transactionContractNo,jdbcType=VARCHAR},
      DRAWER_NAME = #{drawerName,jdbcType=VARCHAR},
      DRAWER_ACCOUNT = #{drawerAccount,jdbcType=VARCHAR},
      DRAWER_BANK_NAME = #{drawerBankName,jdbcType=VARCHAR},
      DRAWER_BANK_CODE = #{drawerBankCode,jdbcType=VARCHAR},
      PAYEE_NAME = #{payeeName,jdbcType=VARCHAR},
      PAYEE_ACCOUNT = #{payeeAccount,jdbcType=VARCHAR},
      PAYEE_BANK_NAME = #{payeeBankName,jdbcType=VARCHAR},
      PAYEE_BANK_CODE = #{payeeBankCode,jdbcType=VARCHAR},
      ACCEPTOR_NAME = #{acceptorName,jdbcType=VARCHAR},
      ACCEPTOR_ACCOUNT = #{acceptorAccount,jdbcType=VARCHAR},
      ACCEPTOR_BANK_NAME = #{acceptorBankName,jdbcType=VARCHAR},
      ACCEPTOR_BANK_CODE = #{acceptorBankCode,jdbcType=VARCHAR},
      BILL_STATUS = #{billStatus,jdbcType=INTEGER},
      DISCOUNT_STATUS = #{discountStatus,jdbcType=INTEGER}
    where ACCEPTANCE_BIL_ID = #{acceptanceBilId,jdbcType=BIGINT}
  </update>

    <!--auto generated by MybatisCodeHelper on 2024-09-09-->
    <select id="findByAll" resultType="com.vedeng.erp.finance.domain.dto.AcceptanceBillDto">
        select
        <include refid="Base_Column_List" />
        from T_ACCEPTANCE_BILL
        <where>
            <if test="acceptanceBilId != null">
                and ACCEPTANCE_BIL_ID = #{acceptanceBilId,jdbcType=BIGINT}
            </if>
            <if test="bankBillId != null">
                and BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
            </if>
            <if test="acceptanceBank != null">
                and ACCEPTANCE_BANK = #{acceptanceBank,jdbcType=INTEGER}
            </if>
            <if test="billNumber != null and billNumber != ''">
                and BILL_NUMBER like CONCAT('%',#{billNumber,jdbcType=VARCHAR},'%')
            </if>
            <if test="subBillIntervalStart != null">
                and SUB_BILL_INTERVAL_START = #{subBillIntervalStart,jdbcType=TIMESTAMP}
            </if>
            <if test="subBillIntervalEnd != null">
                and SUB_BILL_INTERVAL_END = #{subBillIntervalEnd,jdbcType=TIMESTAMP}
            </if>
            <if test="billType != null and billType != ''">
                and BILL_TYPE = #{billType,jdbcType=VARCHAR}
            </if>
            <if test="issueDate != null">
                and ISSUE_DATE = #{issueDate,jdbcType=TIMESTAMP}
            </if>
            <if test="maturityDate != null">
                and MATURITY_DATE = #{maturityDate,jdbcType=TIMESTAMP}
            </if>
            <if test="billAmount != null">
                and BILL_AMOUNT = #{billAmount,jdbcType=DECIMAL}
            </if>
            <if test="acceptanceDate != null">
                and ACCEPTANCE_DATE = #{acceptanceDate,jdbcType=TIMESTAMP}
            </if>
            <if test="transactionContractNo != null and transactionContractNo != ''">
                and TRANSACTION_CONTRACT_NO like CONCAT('%',#{transactionContractNo,jdbcType=VARCHAR},'%')
            </if>
            <if test="drawerName != null and drawerName != ''">
                and DRAWER_NAME = #{drawerName,jdbcType=VARCHAR}
            </if>
            <if test="drawerAccount != null and drawerAccount != ''">
                and DRAWER_ACCOUNT = #{drawerAccount,jdbcType=VARCHAR}
            </if>
            <if test="drawerBankName != null and drawerBankName != ''">
                and DRAWER_BANK_NAME = #{drawerBankName,jdbcType=VARCHAR}
            </if>
            <if test="drawerBankCode != null and drawerBankCode != ''">
                and DRAWER_BANK_CODE = #{drawerBankCode,jdbcType=VARCHAR}
            </if>
            <if test="payeeName != null and payeeName != ''">
                and PAYEE_NAME like CONCAT('%',#{payeeName,jdbcType=VARCHAR},'%')
            </if>
            <if test="payeeAccount != null and payeeAccount != ''">
                and PAYEE_ACCOUNT = #{payeeAccount,jdbcType=VARCHAR}
            </if>
            <if test="payeeBankName != null and payeeBankName != ''">
                and PAYEE_BANK_NAME = #{payeeBankName,jdbcType=VARCHAR}
            </if>
            <if test="payeeBankCode != null and payeeBankCode != ''">
                and PAYEE_BANK_CODE = #{payeeBankCode,jdbcType=VARCHAR}
            </if>
            <if test="acceptorName != null and acceptorName != ''">
                and ACCEPTOR_NAME = #{acceptorName,jdbcType=VARCHAR}
            </if>
            <if test="acceptorAccount != null and acceptorAccount != ''">
                and ACCEPTOR_ACCOUNT = #{acceptorAccount,jdbcType=VARCHAR}
            </if>
            <if test="acceptorBankName != null and acceptorBankName != ''">
                and ACCEPTOR_BANK_NAME = #{acceptorBankName,jdbcType=VARCHAR}
            </if>
            <if test="acceptorBankCode != null and acceptorBankCode != ''">
                and ACCEPTOR_BANK_CODE = #{acceptorBankCode,jdbcType=VARCHAR}
            </if>
            <if test="billStatus != null and billStatus != ''">
                and BILL_STATUS = #{billStatus,jdbcType=VARCHAR}
            </if>
            <if test="addTime != null">
                and ADD_TIME = #{addTime,jdbcType=TIMESTAMP}
            </if>
            <if test="modTime != null">
                and MOD_TIME = #{modTime,jdbcType=TIMESTAMP}
            </if>
            <if test="creator != null">
                and CREATOR = #{creator,jdbcType=INTEGER}
            </if>
            <if test="updater != null">
                and UPDATER = #{updater,jdbcType=INTEGER}
            </if>
            <if test="creatorName != null and creatorName != ''">
                and CREATOR_NAME = #{creatorName,jdbcType=VARCHAR}
            </if>
            <if test="updaterName != null and updaterName != ''">
                and UPDATER_NAME = #{updaterName,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-09-10-->
    <select id="findByBillNumber" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from T_ACCEPTANCE_BILL
        where BILL_NUMBER = #{billNumber,jdbcType=VARCHAR}
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-09-10-->
    <select id="findByBankBillIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from T_ACCEPTANCE_BILL
        where BANK_BILL_ID in
        <foreach close=")" collection="bankBillIdCollection" index="index" item="item" open="(" separator=",">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-09-11-->
    <select id="findAcceptanceBillNonTerminatedState" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM T_ACCEPTANCE_BILL tab
        WHERE NOT EXISTS(
                SELECT 1
                FROM (SELECT 5 AS BILL_STATUS UNION ALL SELECT 6) sub
                WHERE tab.BILL_STATUS = sub.BILL_STATUS
            );
    </select>
    <select id="findDraftDiscountProcessing" resultMap="BaseResultMap">
      SELECT
      <include refid="Base_Column_List" />
      FROM T_ACCEPTANCE_BILL tab
      WHERE DISCOUNT_STATUS = 2
      LIMIT 1000
    </select>

  <select id="findSignupList" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM T_ACCEPTANCE_BILL tab
    WHERE BILL_STATUS = 2
    LIMIT 1000
  </select>






</mapper>