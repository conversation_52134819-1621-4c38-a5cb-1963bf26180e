package com.vedeng.logistics.dao;
import java.util.List;
import org.apache.ibatis.annotations.Param;

import com.vedeng.logistics.model.dto.RExpressWarehouseGoodsOutInDto;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2023/3/31 10:02
 **/
public interface RExpressWarehouseGoodsOutInMapper {
    int deleteByPrimaryKey(Integer rExpressWarehouseGoodsOutInId);

    int insert(RExpressWarehouseGoodsOutInDto record);

    int insertSelective(RExpressWarehouseGoodsOutInDto record);

    RExpressWarehouseGoodsOutInDto selectByPrimaryKey(Integer rExpressWarehouseGoodsOutInId);

    int updateByPrimaryKeySelective(RExpressWarehouseGoodsOutInDto record);

    int updateByPrimaryKey(RExpressWarehouseGoodsOutInDto record);

    int logicaldeleteByExpressId(@Param("expressId")Integer expressId);

    List<RExpressWarehouseGoodsOutInDto> selectByExpressId(@Param("expressId")Integer expressId);




}