<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchAfterSalesInstallServiceRecordDtoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesInstallServiceRecordDto">
    <!--@mbg.generated-->
    <!--@Table T_AFTER_SALES_INSTALL_SERVICE_RECORD-->
    <id column="AFTER_SALES_SERVICE_ID" jdbcType="INTEGER" property="afterSalesServiceId" />
    <result column="AFTER_SALES_ID" jdbcType="INTEGER" property="afterSalesId" />
    <result column="CHECK_DATE" jdbcType="DATE" property="checkDate" />
    <result column="CHECK_TYPE" jdbcType="INTEGER" property="checkType" />
    <result column="RECORD_ID" jdbcType="VARCHAR" property="recordId" />
    <result column="CHECK_CONCLUSION" jdbcType="INTEGER" property="checkConclusion" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    AFTER_SALES_SERVICE_ID, AFTER_SALES_ID, CHECK_DATE, CHECK_TYPE, RECORD_ID, CHECK_CONCLUSION,
    ADD_TIME, CREATOR, MOD_TIME, UPDATER, IS_DELETE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_AFTER_SALES_INSTALL_SERVICE_RECORD
    where AFTER_SALES_SERVICE_ID = #{afterSalesServiceId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_AFTER_SALES_INSTALL_SERVICE_RECORD
    where AFTER_SALES_SERVICE_ID = #{afterSalesServiceId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="AFTER_SALES_SERVICE_ID" keyProperty="afterSalesServiceId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesInstallServiceRecordDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALES_INSTALL_SERVICE_RECORD (AFTER_SALES_ID, CHECK_DATE, CHECK_TYPE,
      RECORD_ID, CHECK_CONCLUSION, ADD_TIME,
      CREATOR, MOD_TIME, UPDATER,
      IS_DELETE)
    values (#{afterSalesId,jdbcType=INTEGER}, #{checkDate,jdbcType=DATE}, #{checkType,jdbcType=INTEGER},
      #{recordId,jdbcType=VARCHAR}, #{checkConclusion,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP},
      #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER},
      #{isDelete,jdbcType=BOOLEAN})
  </insert>
  <insert id="insertSelective" keyColumn="AFTER_SALES_SERVICE_ID" keyProperty="afterSalesServiceId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesInstallServiceRecordDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALES_INSTALL_SERVICE_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="afterSalesId != null">
        AFTER_SALES_ID,
      </if>
      <if test="checkDate != null">
        CHECK_DATE,
      </if>
      <if test="checkType != null">
        CHECK_TYPE,
      </if>
      <if test="recordId != null and recordId != ''">
        RECORD_ID,
      </if>
      <if test="checkConclusion != null">
        CHECK_CONCLUSION,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="afterSalesId != null">
        #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="checkDate != null">
        #{checkDate,jdbcType=DATE},
      </if>
      <if test="checkType != null">
        #{checkType,jdbcType=INTEGER},
      </if>
      <if test="recordId != null and recordId != ''">
        #{recordId,jdbcType=VARCHAR},
      </if>
      <if test="checkConclusion != null">
        #{checkConclusion,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesInstallServiceRecordDto">
    <!--@mbg.generated-->
    update T_AFTER_SALES_INSTALL_SERVICE_RECORD
    <set>
      <if test="afterSalesId != null">
        AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="checkDate != null">
        CHECK_DATE = #{checkDate,jdbcType=DATE},
      </if>
      <if test="checkType != null">
        CHECK_TYPE = #{checkType,jdbcType=INTEGER},
      </if>
      <if test="recordId != null and recordId != ''">
        RECORD_ID = #{recordId,jdbcType=VARCHAR},
      </if>
      <if test="checkConclusion != null">
        CHECK_CONCLUSION = #{checkConclusion,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
    </set>
    where AFTER_SALES_SERVICE_ID = #{afterSalesServiceId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesInstallServiceRecordDto">
    <!--@mbg.generated-->
    update T_AFTER_SALES_INSTALL_SERVICE_RECORD
    set AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      CHECK_DATE = #{checkDate,jdbcType=DATE},
      CHECK_TYPE = #{checkType,jdbcType=INTEGER},
      RECORD_ID = #{recordId,jdbcType=VARCHAR},
      CHECK_CONCLUSION = #{checkConclusion,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN}
    where AFTER_SALES_SERVICE_ID = #{afterSalesServiceId,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_AFTER_SALES_INSTALL_SERVICE_RECORD
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="AFTER_SALES_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_SERVICE_ID = #{item.afterSalesServiceId,jdbcType=INTEGER} then #{item.afterSalesId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CHECK_DATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_SERVICE_ID = #{item.afterSalesServiceId,jdbcType=INTEGER} then #{item.checkDate,jdbcType=DATE}
        </foreach>
      </trim>
      <trim prefix="CHECK_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_SERVICE_ID = #{item.afterSalesServiceId,jdbcType=INTEGER} then #{item.checkType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="RECORD_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_SERVICE_ID = #{item.afterSalesServiceId,jdbcType=INTEGER} then #{item.recordId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="CHECK_CONCLUSION = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_SERVICE_ID = #{item.afterSalesServiceId,jdbcType=INTEGER} then #{item.checkConclusion,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_SERVICE_ID = #{item.afterSalesServiceId,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_SERVICE_ID = #{item.afterSalesServiceId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_SERVICE_ID = #{item.afterSalesServiceId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_SERVICE_ID = #{item.afterSalesServiceId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_SERVICE_ID = #{item.afterSalesServiceId,jdbcType=INTEGER} then #{item.isDelete,jdbcType=BOOLEAN}
        </foreach>
      </trim>
    </trim>
    where AFTER_SALES_SERVICE_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.afterSalesServiceId,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_AFTER_SALES_INSTALL_SERVICE_RECORD
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="AFTER_SALES_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.afterSalesId != null">
            when AFTER_SALES_SERVICE_ID = #{item.afterSalesServiceId,jdbcType=INTEGER} then #{item.afterSalesId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CHECK_DATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.checkDate != null">
            when AFTER_SALES_SERVICE_ID = #{item.afterSalesServiceId,jdbcType=INTEGER} then #{item.checkDate,jdbcType=DATE}
          </if>
        </foreach>
      </trim>
      <trim prefix="CHECK_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.checkType != null">
            when AFTER_SALES_SERVICE_ID = #{item.afterSalesServiceId,jdbcType=INTEGER} then #{item.checkType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="RECORD_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.recordId != null">
            when AFTER_SALES_SERVICE_ID = #{item.afterSalesServiceId,jdbcType=INTEGER} then #{item.recordId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="CHECK_CONCLUSION = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.checkConclusion != null">
            when AFTER_SALES_SERVICE_ID = #{item.afterSalesServiceId,jdbcType=INTEGER} then #{item.checkConclusion,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when AFTER_SALES_SERVICE_ID = #{item.afterSalesServiceId,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when AFTER_SALES_SERVICE_ID = #{item.afterSalesServiceId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when AFTER_SALES_SERVICE_ID = #{item.afterSalesServiceId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when AFTER_SALES_SERVICE_ID = #{item.afterSalesServiceId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDelete != null">
            when AFTER_SALES_SERVICE_ID = #{item.afterSalesServiceId,jdbcType=INTEGER} then #{item.isDelete,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
    </trim>
    where AFTER_SALES_SERVICE_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.afterSalesServiceId,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="AFTER_SALES_SERVICE_ID" keyProperty="afterSalesServiceId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALES_INSTALL_SERVICE_RECORD
    (AFTER_SALES_ID, CHECK_DATE, CHECK_TYPE, RECORD_ID, CHECK_CONCLUSION, ADD_TIME, CREATOR,
      MOD_TIME, UPDATER, IS_DELETE)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.afterSalesId,jdbcType=INTEGER}, #{item.checkDate,jdbcType=DATE}, #{item.checkType,jdbcType=INTEGER},
        #{item.recordId,jdbcType=VARCHAR}, #{item.checkConclusion,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP},
        #{item.creator,jdbcType=INTEGER}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.updater,jdbcType=INTEGER},
        #{item.isDelete,jdbcType=BOOLEAN})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="AFTER_SALES_SERVICE_ID" keyProperty="afterSalesServiceId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesInstallServiceRecordDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALES_INSTALL_SERVICE_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="afterSalesServiceId != null">
        AFTER_SALES_SERVICE_ID,
      </if>
      AFTER_SALES_ID,
      CHECK_DATE,
      CHECK_TYPE,
      RECORD_ID,
      CHECK_CONCLUSION,
      ADD_TIME,
      CREATOR,
      MOD_TIME,
      UPDATER,
      IS_DELETE,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="afterSalesServiceId != null">
        #{afterSalesServiceId,jdbcType=INTEGER},
      </if>
      #{afterSalesId,jdbcType=INTEGER},
      #{checkDate,jdbcType=DATE},
      #{checkType,jdbcType=INTEGER},
      #{recordId,jdbcType=VARCHAR},
      #{checkConclusion,jdbcType=INTEGER},
      #{addTime,jdbcType=TIMESTAMP},
      #{creator,jdbcType=INTEGER},
      #{modTime,jdbcType=TIMESTAMP},
      #{updater,jdbcType=INTEGER},
      #{isDelete,jdbcType=BOOLEAN},
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="afterSalesServiceId != null">
        AFTER_SALES_SERVICE_ID = #{afterSalesServiceId,jdbcType=INTEGER},
      </if>
      AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      CHECK_DATE = #{checkDate,jdbcType=DATE},
      CHECK_TYPE = #{checkType,jdbcType=INTEGER},
      RECORD_ID = #{recordId,jdbcType=VARCHAR},
      CHECK_CONCLUSION = #{checkConclusion,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="AFTER_SALES_SERVICE_ID" keyProperty="afterSalesServiceId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesInstallServiceRecordDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALES_INSTALL_SERVICE_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="afterSalesServiceId != null">
        AFTER_SALES_SERVICE_ID,
      </if>
      <if test="afterSalesId != null">
        AFTER_SALES_ID,
      </if>
      <if test="checkDate != null">
        CHECK_DATE,
      </if>
      <if test="checkType != null">
        CHECK_TYPE,
      </if>
      <if test="recordId != null and recordId != ''">
        RECORD_ID,
      </if>
      <if test="checkConclusion != null">
        CHECK_CONCLUSION,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="afterSalesServiceId != null">
        #{afterSalesServiceId,jdbcType=INTEGER},
      </if>
      <if test="afterSalesId != null">
        #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="checkDate != null">
        #{checkDate,jdbcType=DATE},
      </if>
      <if test="checkType != null">
        #{checkType,jdbcType=INTEGER},
      </if>
      <if test="recordId != null and recordId != ''">
        #{recordId,jdbcType=VARCHAR},
      </if>
      <if test="checkConclusion != null">
        #{checkConclusion,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="afterSalesServiceId != null">
        AFTER_SALES_SERVICE_ID = #{afterSalesServiceId,jdbcType=INTEGER},
      </if>
      <if test="afterSalesId != null">
        AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="checkDate != null">
        CHECK_DATE = #{checkDate,jdbcType=DATE},
      </if>
      <if test="checkType != null">
        CHECK_TYPE = #{checkType,jdbcType=INTEGER},
      </if>
      <if test="recordId != null and recordId != ''">
        RECORD_ID = #{recordId,jdbcType=VARCHAR},
      </if>
      <if test="checkConclusion != null">
        CHECK_CONCLUSION = #{checkConclusion,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
    </trim>
  </insert>
</mapper>