package com.vedeng.erp.kingdee.batch.job;

import cn.hutool.core.date.DateUtil;
import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto;
import com.vedeng.erp.kingdee.batch.dto.BatchPayExpensesDto;
import com.vedeng.erp.kingdee.batch.processor.SaleOrderAfterSaleInputFeeProcessor;
import com.vedeng.erp.kingdee.batch.processor.SaleOrderAfterSalePayExpenseProcessor;
import com.vedeng.erp.kingdee.batch.writer.SaleOrderAfterSaleInputFeeWriter;
import com.vedeng.erp.kingdee.batch.writer.SaleOrderAfterSalePayExpenseWriter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: ckt
 * @Date: 2023/6/2
 * @Description: 销售售后录票（蓝字有效）-- 费用应付单、进项票
 *
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class SaleOrderAfterSalePayExpenseBatchJob extends BaseJob {

    @Autowired
    SaleOrderAfterSalePayExpenseProcessor saleOrderAfterSalePayExpenseProcessor;
    @Autowired
    SaleOrderAfterSalePayExpenseWriter saleOrderAfterSalePayExpenseWriter;
    @Autowired
    SaleOrderAfterSaleInputFeeProcessor saleOrderAfterSaleInputFeeProcessor;
    @Autowired
    SaleOrderAfterSaleInputFeeWriter saleOrderAfterSaleInputFeeWriter;

    public Job saleOrderAfterSalePayExpenseJob() {
        return jobBuilderFactory.get("saleOrderAfterSalePayExpenseJob")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(saleOrderAfterSalePayExpense())
                .next(saleOrderAfterSaleInputFee())
                .build();
    }

    /**
     * 销售售后录票费用应付单
     */
    private Step saleOrderAfterSalePayExpense() {
        return stepBuilderFactory.get("销售售后录票费用应付单")
                // <输入对象, 输出对象> chunk通俗的讲类似于SQL的commit; 这里表示处理(processor)100条后写入(writer)一次
                .<BatchInvoiceDto, BatchPayExpensesDto>chunk(1)
                .faultTolerant()
                .retryLimit(1)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(saleorderAfterSalePayExpenseReader(null,null))
                .processor(saleOrderAfterSalePayExpenseProcessor)
                .writer(saleOrderAfterSalePayExpenseWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 销售售后录票进项票(专票、普票)
     */
    private Step saleOrderAfterSaleInputFee(){
        return stepBuilderFactory.get("销售售后录票蓝票")
                // <输入对象, 输出对象> chunk通俗的讲类似于SQL的commit; 这里表示处理(processor)100条后写入(writer)一次
                .<BatchInvoiceDto, BatchPayExpensesDto>chunk(1)
                .faultTolerant()
                .retryLimit(1)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(saleorderAfterSaleInputFeeReader(null,null))
                .processor(saleOrderAfterSaleInputFeeProcessor)
                .writer(saleOrderAfterSaleInputFeeWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> saleorderAfterSalePayExpenseReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                           @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                // 蓝字有效
                .colorType(2)
                .isEnable(1)
                // 审核通过
                .validStatus(1)
                .companyId(1)
                //录票
                .tag(2)
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()).getTime() :
                        DateUtil.parseDateTime(beginTime).getTime())
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()).getTime() :
                        DateUtil.parseDateTime(endTime).getTime())
                //订单类型为'销售售后' SUBJECT_TYPE = 535
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(), "saleOrderAfterSalePayExpenseReader", batchInvoiceDto);
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> saleorderAfterSaleInputFeeReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                           @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                // 蓝字有效
                .colorType(2)
                .isEnable(1)
                // 审核通过
                .validStatus(1)
                .companyId(1)
                //录票
                .tag(2)
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()).getTime() :
                        DateUtil.parseDateTime(beginTime).getTime())
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()).getTime() :
                        DateUtil.parseDateTime(endTime).getTime())
                //订单类型为'销售售后' SUBJECT_TYPE = 535
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(), "saleOrderAfterSaleInputFeeReader", batchInvoiceDto);
    }



}
