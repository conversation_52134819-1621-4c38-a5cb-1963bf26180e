package com.vedeng.erp.kingdee.domain.command;

import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class KingDeeAliReceiveBillCommand {
    /**
     * 单据内码
     */
    private Integer fId;
    /**
     * 单据编号
     */
    private String fBillNo;
    /**
     * 单据类型
     */
    private KingDeeNumberCommand fBillTypeId = new KingDeeNumberCommand();
    /**
     * 收款组织
     */
    private KingDeeNumberCommand fPayOrgId = new KingDeeNumberCommand();
    /**
     * 单据日期
     */
    private String FDATE;
    /**
     * 银行流水号
     */
    private String f_qzok_lsh;
    /**
     * 往来单位类型
     */
    private String fContactUnitType;
    /**
     * 往来单位
     */
    private KingDeeNumberCommand fContactUnit = new KingDeeNumberCommand();
    /**
     * 交易主体
     */
    private String F_QZOK_JYZT;
    /**
     * 交易类型
     */
    private String F_QZOK_JYLX;
    /**
     * 交易方式
     */
    private String F_QZOK_JYFS;

    private List<KingDeeAliReceiveBillCommand.FRECEIVEBILLENTRY> FRECEIVEBILLENTRY = new ArrayList<>();

    @NoArgsConstructor
    @Data
    public static class FRECEIVEBILLENTRY{
        /**
         * 结算方式
         */

        private KingDeeNumberCommand FSETTLETYPEID = new KingDeeNumberCommand();
        /**
         * 用途
         */
        private KingDeeNumberCommand FPURPOSEID = new KingDeeNumberCommand();
        /**
         * 我方银行账号
         */
        private KingDeeNumberCommand FACCOUNTID = new KingDeeNumberCommand();
        /**
         * 收款业务分类
         */
        private KingDeeNumberCommand F_QZOK_SKYW = new KingDeeNumberCommand();
        /**
         * 收款金额
         */
        private BigDecimal FRECTOTALAMOUNTFOR;
        /**
         * 贝登单据行id
         */
        private String F_QZOK_BDDJHID;
        /**
         * 原始订单号
         */
        private String F_QZOK_YSDDH;
        /**
         * 归属业务单号
         */
        private String F_QZOK_GSYWDH;
        /**
         * 业务类型
         */
        private String F_QZOK_YWLX;

        /**
         * 手续费
         */
        private String fHANDLINGCHARGEFOR;
    }
}
