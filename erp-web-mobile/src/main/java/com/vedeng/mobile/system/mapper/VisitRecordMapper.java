package com.vedeng.mobile.system.mapper;

import com.vedeng.erp.trader.dto.VisitInputDto;
import com.vedeng.erp.trader.dto.VisitSearchDto;
import com.vedeng.mobile.system.model.VisitRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2023/12/6
 */
public interface VisitRecordMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(VisitRecord record);

    int insertSelective(VisitRecord record);

    VisitRecord selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(VisitRecord record);

    int updateByPrimaryKey(VisitRecord record);

    VisitInputDto selectVisitInputDtoByPrimaryKey(Integer id);

    List<VisitInputDto> searchVisitRecordListPage(@Param("visitSearchDto") VisitSearchDto visitSearchDto, @Param("pageNum") int pageNum, @Param("pageSize") int pageSize);

    List<VisitInputDto> searchVisitRecordListPageByCustomerName(@Param("visitSearchDto") VisitSearchDto visitSearchDto, @Param("pageNum") int pageNum, @Param("pageSize") int pageSize);

    List<VisitRecord> selectByCustomerNameForAreaCode(String customerName);

    List<VisitRecord> selectByCustomerName(String customerName);

//    List<VisitRecord> selectByCustomerNameAndAddUserId(@Param("customerName")String customerName,
//                                                       @Param("addUserId")Integer addUserId,);

//    List<Integer>

}