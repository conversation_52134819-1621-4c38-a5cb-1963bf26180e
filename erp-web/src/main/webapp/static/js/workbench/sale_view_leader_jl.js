$(function () {
    var userPostionName = $("#position").val();
    var userPostionId = $("#id").val();
    var pathUrl = $("#pathUrl").val();
    var thisWeekStartTimeStr = $("#thisWeekStartTimeStr").val();
    var thisWeekEndTimeStr = $("#thisWeekEndTimeStr").val();
    var bussinessChanceStatusString = $("#bussinessChanceStatusString").val();
    var yesterdayEndtimeStr = $("#yesterdayEndtimeStr").val();


    // 管理数据总览
    $.ajax({
        type: "POST",
        data: {userId: userPostionId, positionName: userPostionName},
        url: pathUrl + "/workbench/bussinesschance/leaderDataOverView.do",
        dataType: "json",
        success: function (result) {
            if (result.code == 0) {
                var dataOverviewList = result.data;
                var OverviewhtmlStr = "";
                if (dataOverviewList == null || dataOverviewList.length == 0) {
                    OverviewhtmlStr += "<tr><td colspan='8'>暂无数据</td></tr>";
                    $("#overviewTable").html(OverviewhtmlStr);
                } else {
                    for (var i = 0; i < dataOverviewList.length; i++) {
                        OverviewhtmlStr += "<tr>";

                        if (dataOverviewList[i].name == null) {
                            OverviewhtmlStr += "<td>-</td>"
                        } else {
                            OverviewhtmlStr += "<td>" + dataOverviewList[i].name + "</td>";
                        }

                        if (dataOverviewList[i].totalBussinessAmount == null) {
                            OverviewhtmlStr += "<td>-</td>"
                        } else {
                            OverviewhtmlStr += "<td>" + dataOverviewList[i].totalBussinessAmount.toFixed(2) + "</td>";
                        }

                        if (dataOverviewList[i].totalBussinessNum == null) {
                            OverviewhtmlStr += "<td>-</td>"
                        } else {
                            OverviewhtmlStr += "<td>" + dataOverviewList[i].totalBussinessNum + "</td>";
                        }

                        if (dataOverviewList[i].avrBussinessAmount == null) {
                            OverviewhtmlStr += "<td>-</td>"
                        } else {
                            OverviewhtmlStr += "<td>" + dataOverviewList[i].avrBussinessAmount.toFixed(2) + "</td>";
                        }

                        if (dataOverviewList[i].yesterdayBussinessAmount == null) {
                            OverviewhtmlStr += "<td>-</td>"
                        } else {
                            OverviewhtmlStr += "<td>" + dataOverviewList[i].yesterdayBussinessAmount.toFixed(2) + "</td>";
                        }

                        if (dataOverviewList[i].yesterdayBussinessNum == null) {
                            OverviewhtmlStr += "<td>-</td>"
                        } else {
                            OverviewhtmlStr += "<td>" + dataOverviewList[i].yesterdayBussinessNum + "</td>";
                        }

                        if (dataOverviewList[i].thisWeekBussinessAmount == null) {
                            OverviewhtmlStr += "<td>-</td>"
                        } else {
                            OverviewhtmlStr += "<td>" + dataOverviewList[i].thisWeekBussinessAmount.toFixed(2) + "</td>";
                        }

                        if (dataOverviewList[i].thisWeekBussinessNum == null) {
                            OverviewhtmlStr += "<td>-</td>"
                        } else {
                            OverviewhtmlStr += "<td>" + dataOverviewList[i].thisWeekBussinessNum + "</td>";
                        }

                        OverviewhtmlStr += "</tr>";
                    }
                    $("#overviewTable").html(OverviewhtmlStr);
                }
            }
        }

    });

    $.ajax({
        //管理待沟通商机
        type: "POST",
        data: {userId: userPostionId, positionName: userPostionName},
        url: pathUrl + "/workbench/bussinesschance/leaderCommunicate.do",
        dataType: "json",
        success: function (result) {
            if (result.code == 0) {
                var commnunicateList = result.data;
                var communicateHtmlStr = "";
                if (commnunicateList == null || commnunicateList.length == 0) {
                    communicateHtmlStr += "<tr><td colspan='6'>暂无数据</td></tr>";
                    $("#communicateTable").html(communicateHtmlStr);
                } else {
                    for (var i = 0; i < commnunicateList.length; i++) {
                        communicateHtmlStr += "<tr>";

                        if (commnunicateList[i].name == null) {
                            communicateHtmlStr += "<td>-</td>"
                        } else {
                            communicateHtmlStr += "<td>" + commnunicateList[i].name + "</td>";
                        }

                        if (commnunicateList[i].planingCommunicateNum == null) {
                            communicateHtmlStr += "<td>-</td>"
                        } else {
                            communicateHtmlStr += "<td>" + commnunicateList[i].planingCommunicateNum + "</td>";
                        }

                        if (commnunicateList[i].inPlaningNum == null) {
                            communicateHtmlStr += "<td>-</td>"
                        } else {
                            communicateHtmlStr += "<td>" + commnunicateList[i].inPlaningNum + "</td>";
                        }

                        if (commnunicateList[i].completionRate == null) {
                            communicateHtmlStr += "<td>-</td>"
                        } else {
                            communicateHtmlStr += "<td>" + commnunicateList[i].completionRate + "%</td>";
                        }

                        if (commnunicateList[i].outPlaningNum == null) {
                            communicateHtmlStr += "<td>-</td>"
                        } else {
                            communicateHtmlStr += "<td>" + commnunicateList[i].outPlaningNum + "</td>";
                        }

                        if (commnunicateList[i].todayToCommunicateNum == null) {
                            communicateHtmlStr += "<td>-</td>"
                        } else {
                            communicateHtmlStr += "<td>" + commnunicateList[i].todayToCommunicateNum + "</td>";
                        }

                        communicateHtmlStr += "</tr>";
                    }
                    $("#communicateTable").html(communicateHtmlStr);
                }
            }

        }
    });


    $.ajax({
        //预计本周成单
        type: "POST",
        data: {userId: userPostionId, positionName: userPostionName},
        url: pathUrl + "/workbench/bussinesschance/leaderExpect.do",
        dataType: "json",
        success: function (result) {
            if (result.code == 0) {
                var expectList = result.data;
                var expectHtmlStr = "";
                if (expectList == null || expectList.length == 0) {
                    expectHtmlStr += "<tr><td colspan='6'>暂无数据</td></tr>";
                    $("#expectTable").html(expectHtmlStr);
                } else {
                    for (var i = 0; i < expectList.length; i++) {
                        expectHtmlStr += "<tr>";

                        if (expectList[i].name == null) {
                            expectHtmlStr += "<td>-</td>"
                        } else {
                            expectHtmlStr += "<td>" + expectList[i].name + "</td>";
                        }

                        if (expectList[i].lastWeekSuccessNum == null) {
                            expectHtmlStr += "<td>-</td>"
                        } else {
                            expectHtmlStr += "<td>" + expectList[i].lastWeekSuccessNum + "</td>";
                        }

                        if (expectList[i].lastWeekFailNum == null) {
                            expectHtmlStr += "<td>-</td>"
                        } else {
                            expectHtmlStr += "<td>" + expectList[i].lastWeekFailNum + "</td>";
                        }

                        if (expectList[i].hitRate == null) {
                            expectHtmlStr += "<td>-</td>"
                        } else {
                            expectHtmlStr += "<td>" + expectList[i].hitRate + "%</td>";
                        }

                        if (expectList[i].thisWeekSuccessNum == null) {
                            expectHtmlStr += "<td>-</td>"
                        } else {
                            if (userPostionName == "销售主管") {
                                if (i != expectList.length - 1) {
                                    expectHtmlStr += "<td><div class =\"jump\"><a class=\"addtitle_1\" href=\"javascript:void(0); \"tabTitle=\'{\"num\":\"view" + Math.floor(Math.random() * 10000 + 1) + "\", " +
                                        "\"link\":\"" + pathUrl + "/order/bussinesschance/expectChance.do?jumpType=1&searchType=0&searchId=" + expectList[i].id + "&cdstarttime=" + thisWeekStartTimeStr + "&cdendtime=" + thisWeekEndTimeStr + "\", " +
                                        "\"title\":\"销售商机详情\"}\'>" + expectList[i].thisWeekSuccessNum + "</a></div></td>";
                                } else {
                                    expectHtmlStr += "<td><div class =\"jump\"><a class=\"addtitle_1\" href=\"javascript:void(0); \"tabTitle=\'{\"num\":\"view" + Math.floor(Math.random() * 10000 + 1) + "\", " +
                                        "\"link\":\"" + pathUrl + "/order/bussinesschance/expectChance.do?jumpType=1&searchType=0&cdstarttime=" + thisWeekStartTimeStr + "&cdendtime=" + thisWeekEndTimeStr + "\", " +
                                        "\"title\":\"销售商机详情\"}\'>" + expectList[i].thisWeekSuccessNum + "</a></div></td>";

                                }

                            } else {
                                if (i != expectList.length - 1) {
                                    expectHtmlStr += "<td><div class =\"jump\"><a class=\"addtitle_1\" href=\"javascript:void(0); \"tabTitle=\'{\"num\":\"view" + Math.floor(Math.random() * 10000 + 1) + "\", " +
                                        "\"link\":\"" + pathUrl + "/order/bussinesschance/expectChance.do?jumpType=1&searchType=1&searchId=" + expectList[i].id + "&cdstarttime=" + thisWeekStartTimeStr + "&cdendtime=" + thisWeekEndTimeStr + "\", " +
                                        "\"title\":\"销售商机详情\"}\'>" + expectList[i].thisWeekSuccessNum + "</a></div></td>";

                                } else {
                                    expectHtmlStr += "<td><div class =\"jump\"><a class=\"addtitle_1\" href=\"javascript:void(0); \"tabTitle=\'{\"num\":\"view" + Math.floor(Math.random() * 10000 + 1) + "\", " +
                                        "\"link\":\"" + pathUrl + "/order/bussinesschance/expectChance.do?jumpType=1&searchType=1&cdstarttime=" + thisWeekStartTimeStr + "&cdendtime=" + thisWeekEndTimeStr + "\", " +
                                        "\"title\":\"销售商机详情\"}\'>" + expectList[i].thisWeekSuccessNum + "</a></div></td>";
                                }

                            }
                        }

                        if (expectList[i].thisWeekAmount == null) {
                            expectHtmlStr += "<td>-</td>"
                        } else {
                            expectHtmlStr += "<td>" + expectList[i].thisWeekAmount.toFixed(2) + "</td>";
                        }
                        expectHtmlStr += "</tr>";


                    }
                    $("#expectTable").html(expectHtmlStr);
                    $("#expectTable .addtitle_1").css("color", "#3384ef");
                    $("#expectTable .addtitle_1").css("font-size", "12px");
                }
            }
        }
    });


    //经理和总监重点商机
    $.ajax({
        type: "POST",
        data: {userId: userPostionId, positionName: userPostionName},
        url: pathUrl + "/workbench/bussinesschance/leaderImportant/leader.do",
        dataType: "json",
        success: function (result) {
            if (result.code == 0) {
                var leaderImportantList = result.data;
                var leaderImportantHtmlStr = "";
                if (leaderImportantList == null || leaderImportantList.length == 0) {
                    leaderImportantHtmlStr += "<tr><td colspan='5'>暂无数据</td></tr>";
                    $("#importantTable").html(leaderImportantHtmlStr);
                } else {
                    for (var i = 0; i < leaderImportantList.length; i++) {
                        leaderImportantHtmlStr += "<tr>";

                        if (leaderImportantList[i].orgName == null) {
                            leaderImportantHtmlStr += "<td>-</td>"
                        } else {
                            leaderImportantHtmlStr += "<td>" + leaderImportantList[i].orgName + "</td>";
                        }

                        if (leaderImportantList[i].ssTotalNum == null) {
                            leaderImportantHtmlStr += "<td>-</td>"
                        } else {
                            leaderImportantHtmlStr += "<td>" + leaderImportantList[i].ssTotalNum + "</td>";
                        }

                        if (leaderImportantList[i].ssTotalAmount == null) {
                            leaderImportantHtmlStr += "<td>-</td>"
                        } else {
                            leaderImportantHtmlStr += "<td>" + leaderImportantList[i].ssTotalAmount.toFixed(2) + "</td>";
                        }

                        if (leaderImportantList[i].aaTotalNum == null) {
                            leaderImportantHtmlStr += "<td>-</td>"
                        } else {
                            leaderImportantHtmlStr += "<td>" + leaderImportantList[i].aaTotalNum + "</td>";
                        }

                        if (leaderImportantList[i].aaTotalAmount == null) {
                            leaderImportantHtmlStr += "<td>-</td>"
                        } else {
                            leaderImportantHtmlStr += "<td>" + leaderImportantList[i].aaTotalAmount.toFixed(2) + "</td>";
                        }

                        leaderImportantHtmlStr += "</tr>";
                    }
                    $("#importantTable").html(leaderImportantHtmlStr);
                }
            }

        }
    });

    //经理和总监预警商机
    $.ajax({
        type: "POST",
        data: {userId: userPostionId, positionName: userPostionName},
        url: pathUrl + "/workbench/bussinesschance/leaderWarning/leader.do",
        dataType: "json",
        success: function (result) {
            if (result.code == 0) {
                var leaderWarningList = result.data;
                var leaderWarningHtmlStr = "";
                if (leaderWarningList == null || leaderWarningList.length == 0) {
                    leaderWarningHtmlStr += "<tr><td colspan='5'>暂无数据</td></tr>";
                    $("#leaderWaringTable").html(leaderImportantHtmlStr);
                } else {
                    for (var i = 0; i < leaderWarningList.length; i++) {
                        leaderWarningHtmlStr += "<tr>";

                        if (leaderWarningList[i].name == null) {
                            leaderWarningHtmlStr += "<td>-</td>"
                        } else {
                            leaderWarningHtmlStr += "<td>" + leaderWarningList[i].name + "</td>";
                        }

                        if (leaderWarningList[i].thisWeekNewBigProjectNum == null) {
                            leaderWarningHtmlStr += "<td>-</td>"
                        } else {
                            leaderWarningHtmlStr += "<td>" + leaderWarningList[i].thisWeekNewBigProjectNum + "</td>";
                        }

                        if (leaderWarningList[i].unCommunicateNum == null) {
                            leaderWarningHtmlStr += "<td>-</td>"
                        } else {
                            leaderWarningHtmlStr += "<td>" + leaderWarningList[i].unCommunicateNum + "</td>";
                        }

                        if (leaderWarningList[i].thisWeekLostCoreNum == null) {
                            leaderWarningHtmlStr += "<td>-</td>"
                        } else {
                            leaderWarningHtmlStr += "<td>" + leaderWarningList[i].thisWeekLostCoreNum + "</td>";
                        }

                        if (leaderWarningList[i].thisWeekArrearsNum == null) {
                            leaderWarningHtmlStr += "<td>-</td>"
                        } else {
                            leaderWarningHtmlStr += "<td>" + leaderWarningList[i].thisWeekArrearsNum + "</td>";
                        }

                        leaderWarningHtmlStr += "</tr>";
                    }
                }
                $("#leaderWaringTable").html(leaderWarningHtmlStr);
            }
        }
    });
});