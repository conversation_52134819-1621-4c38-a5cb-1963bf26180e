<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="选择录票商品" scope="application"/>
<%@ include file="../../../common/common.jsp" %>
<script src="${pageContext.request.contextPath}/static/js/finance/invoice/hx_invoice/supply_hx_invoice_goods_choice.js?rnd=${resourceVersionKey}"
        charset="utf-8"></script>
<div class="main-container">
    <div class='list-pages-search'>
        <form method="post" id="search" action="<%=basePath%>supplyChain/invoice/supply_hx_invoice_goods_choice.do">
            <ul>
              <%--  <li>
                    <label class="infor_name">产品型号</label>
                    <input type="text" class="input-middle" name="specification" id="specification"
                           value="${buyorderSearchDTO.specification}"/>
                </li>--%>
                <li>
                    <label class="infor_name">供应商</label>
                    <input type="text" class="input-middle" name="salerName" id="salerName"
                           value="${buyorderSearchDTO.salerName}"/>
                </li>
                <li>
                    <label class="infor_name">产品名称</label>
                    <input type="text" class="input-middle" name="goodsName" id="goodsName"
                           value="${buyorderSearchDTO.goodsName}"/>
                </li>
                <li>
                    <label class="infor_name">产品品牌</label>
                    <input type="text" class="input-middle" name="brandName" id="brandName"
                           value="${buyorderSearchDTO.brandName}"/>
                </li>
                <li>
                    <label class="infor_name">订单号</label>
                    <input type="text" class="input-middle" name="buyorderNo" id="buyorderNo"
                           value="${buyorderSearchDTO.buyorderNo}"/>
                </li>
                <li>
                    <label class="infor_name">票种</label>
                    <select class="input-middle" name="taxRate" id="taxRate">
                        <option value="0"
                                <c:if test="${buyorderSearchDTO.taxRate == 0}">selected</c:if> >全部
                        </option>
                        <option value="971"
                                <c:if test="${buyorderSearchDTO.taxRate eq 971}">selected="selected"</c:if>>
                            13%增值税普通发票
                        </option>
                        <option value="972"
                                <c:if test="${buyorderSearchDTO.taxRate eq 972}">selected="selected"</c:if>>
                            13%增值税专用发票
                        </option>
                        <option value="681"
                                <c:if test="${buyorderSearchDTO.taxRate eq 681}">selected="selected"</c:if>>
                            16%增值税普通发票
                        </option>
                        <option value="682"
                                <c:if test="${buyorderSearchDTO.taxRate eq 682}">selected="selected"</c:if>>
                            16%增值税专用发票
                        </option>
                        <option value="683"
                                <c:if test="${buyorderSearchDTO.taxRate eq 683}">selected="selected"</c:if>>6%增值税普通发票
                        </option>
                        <option value="684"
                                <c:if test="${buyorderSearchDTO.taxRate eq 684}">selected="selected"</c:if>>6%增值税专用发票
                        </option>
                        <option value="685"
                                <c:if test="${buyorderSearchDTO.taxRate eq 685}">selected="selected"</c:if>>3%增值税普通发票
                        </option>
                        <option value="686"
                                <c:if test="${buyorderSearchDTO.taxRate eq 686}">selected="selected"</c:if>>3%增值税专用发票
                        </option>
                        <option value="687"
                                <c:if test="${buyorderSearchDTO.taxRate eq 687}">selected="selected"</c:if>>0%增值税普通发票
                        </option>
                    </select>
                </li>

              <%--  <li>
                    <label class="infor_name">采购价</label>
                    <input type="text" class="input-middle" name="price" id="price" value="<fmt:formatNumber type="number" value="${buyorderSearchDTO.price}" pattern="0.00"
                                              maxFractionDigits="2"/>"/>
                    <input id="hxInvoiceId" type="hidden" name="hxInvoiceId" value="${buyorderSearchDTO.hxInvoiceId}">
                </li>--%>

                <li>
                    <div class="infor_name">
                        生效时间
                    </div>
                    <input class="Wdate f_left input-smaller96 mr5" type="text" placeholder="请选择日期"
                           onClick="WdatePicker()" autocomplete="off"
                           onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endAddDateStr\')}'})"
                           name="startAddDateStr" id="startAddDateStr"
                           value='<date:date value ="${startAddDateStr}" format="yyyy-MM-dd"/>'>
                    <div class="gang">-</div>
                    <input class="Wdate f_left input-smaller96" type="text" placeholder="请选择日期" onClick="WdatePicker()" autocomplete="off"
                           onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'startAddDateStr\')}'})"
                           name="endAddDateStr" id="endAddDateStr"
                           value='<date:date value ="${endAddDateStr}" format="yyyy-MM-dd"/>'>
                    <input type="hidden" name="invoiceDetailId" value="${buyorderSearchDTO.invoiceDetailId}">
                    <input id="hxInvoiceId" type="hidden" name="hxInvoiceId" value="${buyorderSearchDTO.hxInvoiceId}">
                </li>
            </ul>
            <div class="tcenter">
                <span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();"
                      id="searchSpan">搜索</span>
                <span class="bt-small bt-bg-style mr20 bg-light-blue" onclick="reset();">重置</span>
            </div>
        </form>
    </div>

    <div class="parts" style="font-weight: bolder; margin: 10px">
        <span>发票总额：</span>
        <span>${hxInvoiceVo.amount}</span>
        &nbsp;&nbsp;&nbsp;
        <span>已录票金额：</span>
        <span id="choiceAmount" style="color: #438DEF">
				0.00
			</span>
        &nbsp;&nbsp;&nbsp;
        <span>剩余可录票金额：</span>
        <span id="canChoiceAmmount" style="color: #438DEF">
				<fmt:formatNumber type="number" pattern="0.00" maxFractionDigits="2"
                                  value="${hxInvoiceVo.amount - hxInvoiceVo.recordedAmount}"/>
			</span>
        <input id="canRecordAmount" type="hidden" value="${hxInvoiceVo.amount - hxInvoiceVo.recordedAmount}">
    </div>
    <div class="parts table-style10-1" id="buyorderInfo">
        <div class="title-container" style='margin-bottom: 10px;'>
            <div class="table-title nobor">产品及订单信息</div>
        </div>
        <c:forEach items="${buyorderList}" var="list" varStatus="buyNum">
            <table class="table table-style10">
                <thead>
                <tr>
                    <th class="wid15">订单号</th>
                    <th class="wid20">生效时间</th>
                    <th>供应商</th>
                    <th class="wid10">订单总额</th>
                    <th>票种</th>
                    <th class="wid20">付款时间</th>
                    <th>开票备注</th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td>
                        <a class="addtitle" href="javascript:void(0);"
                           tabtitle='{"num":"viewbuyorder${list.buyorderId}","link":"./order/buyorder/viewBuyordersh.do?buyorderId=${list.buyorderId}","title":"订单信息"}'>${list.buyorderNo}</a>
                    </td>
                    <td><date:date value="${list.validTime}"/></td>
                    <td>
                        <div class="brand-color">
                            <a class="addtitle" href="javascript:void(0);"
                               tabTitle='{"num":"viewsupplier${list.traderId}","link":"./trader/supplier/baseinfo.do?traderId=${list.traderId}","title":"供应商信息"}'>${list.traderName}</a>
                        </div>
                    </td>
                    <td>${list.totalAmount}</td>
                    <td>
                        <c:choose>
                            <c:when test="${hxInvoiceVo.taxRate == 971}">
                                13%增值税普通发票
                            </c:when>
                            <c:when test="${hxInvoiceVo.taxRate == 972}">
                                13%增值税专用发票
                            </c:when>
                            <c:when test="${hxInvoiceVo.taxRate == 681}">
                                16%增值税普通发票
                            </c:when>
                            <c:when test="${hxInvoiceVo.taxRate == 682}">
                                16%增值税专用发票
                            </c:when>
                            <c:when test="${hxInvoiceVo.taxRate == 683}">
                                6%增值税普通发票
                            </c:when>
                            <c:when test="${hxInvoiceVo.taxRate == 684}">
                                6%增值税专用发票
                            </c:when>
                            <c:when test="${hxInvoiceVo.taxRate == 685}">
                                3%增值税普通发票
                            </c:when>
                            <c:when test="${hxInvoiceVo.taxRate == 686}">
                                3%增值税专用发票
                            </c:when>
                            <c:when test="${hxInvoiceVo.taxRate == 687}">
                                0%增值税普通发票
                            </c:when>
                        </c:choose>
                    </td>
                    <td><date:date value="${list.paymentTime}"/></td>
                    <td>${list.invoiceComments}</td>
                </tr>
                <tr>
                    <td colspan="7" class="table-container">
                        <table class="table">
                            <thead class="order_info_thead">
                            <tr>
                                <th>产品名称</th>
                                <th>品牌</th>
                                <th>型号</th>
                                <th>采购价</th>
                                <th class="wid6">单位</th>
                                <th class="wid6">总数</th>
                                <th class="wid8">已入库数量</th>
                                <th>已录票数量</th>
                                <th>已录票总额</th>
                                <th class="wid10">录票单价</th>
                                <th class="wid10">本次录票数量</th>
                                <th class="wid10">本次录票总额</th>
                                <th class="wid8">不含税金额</th>
                                <th class="wid6">税额</th>
                                <th class="wid6">
                                    <span>全选</span><br/><input id="allcheck${list.buyorderId}" type="checkbox"
                                                               name="allcheck"
                                                               onclick="selectAll(this,${list.buyorderId})">
                                </th>
                            </tr>
                            </thead>
                            <tbody class="order_info_body">
                            <c:forEach var="goods" items="${list.buyorderGoodsVoList}" varStatus="goodsNum">
                                <tr>
                                    <td class="text-left">
                                        <div class="brand-color1">
                                            <a class="addtitle" href="javascript:void(0);"
                                               tabtitle='{"num":"viewgoods${goods.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${goods.goodsId}","title":"产品信息"}'>${goods.goodsName}</a>
                                        </div>
                                        <div>${goods.sku}</div>
                                        <div>${goods.materialCode}</div>
                                        <input type="hidden" name="relatedId" id="relatedId${goods.buyorderGoodsId}"
                                               value="${list.buyorderId}">
                                        <input type="hidden" name="buyorderGoodsId"
                                               id="buyorderGoodsId${goods.buyorderGoodsId}"
                                               value="${goods.buyorderGoodsId}">
                                    </td>
                                    <td>${goods.brandName}</td>
                                    <td>${goods.model}</td>
                                    <td>${goods.price}</td>
                                    <td>${goods.unitName}</td>
                                    <td>${goods.num}</td>
                                    <td>${goods.arrivalNum}</td>
                                    <td>${goods.buyorderGoodsRecordDTO.recordedNum == null ? 0 : goods.buyorderGoodsRecordDTO.recordedNum}</td>
                                    <td>${goods.buyorderGoodsRecordDTO.recordedAmount == null ? 0 : goods.buyorderGoodsRecordDTO.recordedAmount}</td>

                                    <input type="hidden" name="update_num_pice"
                                           onclick="updateInvoice(${goods.price},${goods.arrivalNum},${inNum},${buyNum.index},${goodsNum.index});">
                                    <input type="hidden" id="inNum${goods.buyorderGoodsId}" value="${inNum}"/>
                                    <fmt:parseNumber value="${goods.price}" type="number" var="goods_price"/>
                                    <td id="invoice_price${goods.buyorderGoodsId}">${goods.price}</td>
                                    <td>
                                        <input type="text" id="invoice_num${goods.buyorderGoodsId}" name="invoice_num"
                                               alt="${goods.buyorderGoodsId}"
                                               value="<fmt:formatNumber type="number" groupingUsed="false" value="${goods.arrivalNum - goods.buyorderGoodsRecordDTO.recordedNum}" maxFractionDigits="10" />"
                                               onBlur="invoiceNumChange(${goods.buyorderGoodsId},${goods.price},${list.buyorderId});">
                                        <input type="hidden" id="max_num${goods.buyorderGoodsId}"
                                               value="${goods.arrivalNum - goods.buyorderGoodsRecordDTO.recordedNum}"/>
                                    </td>
                                    <td>
                                        <input type="text" id="invoice_totle_amount${goods.buyorderGoodsId}"
                                               name="invoice_totle_amount" alt="${goods.buyorderGoodsId}"
                                               value="<fmt:formatNumber type="number" groupingUsed="false" value="${goods.price * (goods.arrivalNum-goods.buyorderGoodsRecordDTO.recordedNum)}" pattern="0.00" maxFractionDigits="2" />"
                                               onBlur="invoiceTotleAmountChange(this,<fmt:formatNumber type="number"
                                                                                                       value="${(goods.price * goods.arrivalNum)-totalAmount}"
                                                                                                       pattern="0.00"
                                                                                                       maxFractionDigits="2"/>,${goods.price});">
                                        <input type="hidden" id="max_price${goods.buyorderGoodsId}"
                                               value="<fmt:formatNumber type="number" value="${goods.price * (goods.arrivalNum-goods.buyorderGoodsRecordDTO.recordedNum)}" pattern="0.00" maxFractionDigits="2" />"/>
                                    </td>
                                        <%--已录票数据--%>
                                    <c:set var="recordNum"
                                           value="${goods.buyorderGoodsRecordDTO.recordedNum == null ? 0 : goods.buyorderGoodsRecordDTO.recordedNum}"></c:set>
                                    <c:set var="recordAmount"
                                           value="${goods.buyorderGoodsRecordDTO.recordedAmount == null ? 0 : goods.buyorderGoodsRecordDTO.recordedAmount}"></c:set>
                                    <fmt:parseNumber value="${(goods.price * goods.num)-recordAmount}" type="number"
                                                     pattern="0.*********" var="goods_amount"/>
                                    <td id="invoice_no_tax${goods.buyorderGoodsId}">
                                        <fmt:formatNumber type="number"
                                                          value="${goods_amount/(1+0.13)}"
                                                          pattern="0.*********" maxFractionDigits="9"/>
                                    </td>
                                    <td id="invoice_tax${goods.buyorderGoodsId}">
                                        <fmt:formatNumber type="number"
                                                          value="${goods_amount-(goods_amount/(1+0.13))}"
                                                          pattern="0.*********" maxFractionDigits="9"/>
                                    </td>
                                    <td class="single_check_box">
                                        <!-- 订单锁定不允许录票 -->
                                        <c:if test="${list.lockedStatus eq 0}">
                                            <input type="checkbox"
                                                   id="check_box${list.buyorderId}_${goods.buyorderGoodsId}"
                                                   idFlag="${goods.buyorderGoodsId}" value="${list.invoiceType}"
                                                   name="selectInvoiceName${list.buyorderId}"
                                                   goodsPrice="${goods.price}"
                                                   maxNum="${goods.arrivalNum - goods.buyorderGoodsRecordDTO.recordedNum}"
                                                   maxPrice=
                                                       <fmt:formatNumber type="number"
                                                                         value="${goods.price * (goods.arrivalNum - goods.buyorderGoodsRecordDTO.recordedNum)}"
                                                                         pattern="0.00"
                                                                         maxFractionDigits="2"/> inNum="${goods.buyorderGoodsRecordDTO.recordedNum}"
                                                   class="${list.traderId}"
                                                   onchange="selectBuyOrder(this,${goods.arrivalNum - goods.buyorderGoodsRecordDTO.recordedNum},'${goods.buyorderGoodsId}',${list.buyorderId})"
                                                   hxInvoiceDetailIdFlag="${buyorderSearchDTO.invoiceDetailId}">
                                        </c:if>
                                    </td>
                                </tr>
                            </c:forEach>
                            </tbody>
                        </table>
                    </td>
                </tr>
                </tbody>
            </table>
        </c:forEach>
        <c:if test="${empty buyorderList}">
            <div class='noresult' style='margin-top:-11px;'>
                查询无结果！请尝试使用其他搜索条件。
            </div>
        </c:if>
        <div class="table-buttons tcenter">
            <button type="button" class="bt-bg-style bg-light-green bt-small" onclick="addInvoiceEntryStash();">提交
            </button>
        </div>
    </div>
    <%@ include file="../../../common/footer.jsp" %>
