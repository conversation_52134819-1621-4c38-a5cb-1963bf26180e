package com.vedeng.goods.manager.validator;

import com.baidu.unbiz.fluentvalidator.FluentValidator;
import com.baidu.unbiz.fluentvalidator.ValidatorHandler;
import com.vedeng.common.validator.NullValidate;
import com.vedeng.common.validator.NumberValidatorHandler;
import com.vedeng.common.validator.StringValidatorHandler;
import com.vedeng.goods.manager.rule.GoodsBasicValidElement;
import com.vedeng.goods.manager.rule.GoodsExtValidElement;
import com.vedeng.goods.manager.rule.GoodsValidatedRule;
import com.vedeng.goods.manager.validator.model.GoodsValidObject;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.*;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class GoodsValidContext {

    private final Map<Integer, List<GoodsBasicValidElement>> basicRuleMap;
    Logger logger= LoggerFactory.getLogger(getClass());
    private final FluentValidator validator;

    public GoodsValidContext(List<GoodsValidatedRule> rules) {
        Map<Integer, List<GoodsBasicValidElement>> rulesMap = new LinkedHashMap<>();
        for (GoodsValidatedRule rule : rules) {
            if (rule != null && rule.getGoodsLevel() != null) {
                rulesMap.put(rule.getGoodsLevel(), CollectionUtils.isNotEmpty(rule.getBasicItems())
                        ? rule.getBasicItems() : Collections.emptyList());
            }
        }

        this.basicRuleMap = Collections.unmodifiableMap(rulesMap);
        this.validator = FluentValidator.checkAll();
        this.validator.failFast();
    }

    public boolean hasBasicItem(String propertyName, Integer goodLevelNo) {
        try {
            List<GoodsBasicValidElement> basicItems = basicRuleMap.get(goodLevelNo);
            return !basicItems.isEmpty() && basicItems.stream().anyMatch(item -> item.getPropertyName().equals(propertyName));
        }catch (Exception e){
            logger.warn("{} {}",propertyName,goodLevelNo,e);
        }
        return false;
    }

    public GoodsBasicValidElement getBasicItem(String propertyName, Integer goodLevelNo) {
        List<GoodsBasicValidElement> basicItems = basicRuleMap.get(goodLevelNo);
        return basicItems.stream().filter(item -> item.getPropertyName().equals(propertyName)).findFirst().orElse(null);
    }

    public void addProperties(PropertyDescriptor[] propertyDescriptors, GoodsValidObject requestData, List<String> ignorePropNames) {
        for (PropertyDescriptor propertyDescriptor : propertyDescriptors) {
            if (ignorePropNames != null && ignorePropNames.contains(propertyDescriptor.getName())) {
                continue;
            }

            String propertyName = propertyDescriptor.getName();
            if (hasBasicItem(propertyName, requestData.getGoodsLevelNo())) {
                GoodsBasicValidElement basicItem = getBasicItem(propertyName, requestData.getGoodsLevelNo());
                if (basicItem.getRequired()) {
                    Method readMethod = propertyDescriptor.getReadMethod();
                    int modifiers = readMethod.getModifiers();
                    if (Modifier.isAbstract(modifiers) || Modifier.isStatic(modifiers)) {
                        continue;
                    }
                    if (!Modifier.isPublic(modifiers)) {
                        readMethod.setAccessible(true);
                    }

                    Object readValue;
                    try {
                        readValue = readMethod.invoke(requestData);
                    } catch (Exception e) {
                        throw new IllegalStateException(e);
                    }

                    Class<?> propertyType = propertyDescriptor.getPropertyType();
                    ValidatorHandler validatorHandler;
                    if (propertyType.isArray()) {
                        Object[] valueArray = (Object[]) readValue;
                        if (propertyType.isAssignableFrom(Number[].class)) {
                            validatorHandler = new NumberValidatorHandler(basicItem.getMessage());
                        } else if (propertyType.isAssignableFrom(String[].class)) {
                            validatorHandler = new StringValidatorHandler(basicItem.getMessage());
                        } else {
                            validatorHandler = new NullValidate(basicItem.getMessage());
                        }

                        validator.onEach(valueArray, validatorHandler);
                    } else {
                        if (propertyType.isAssignableFrom(String.class)) {
                            validatorHandler = new StringValidatorHandler(basicItem.getMessage());
                        } else if (propertyType.isAssignableFrom(Number.class)) {
                            validatorHandler = new NumberValidatorHandler(basicItem.getMessage());
                        } else {
                            validatorHandler = new NullValidate(basicItem.getMessage());
                        }

                        validator.on(readValue, validatorHandler);
                    }

                }
            }
        }

    }

    public FluentValidator getValidator() {
        return validator;
    }
}
