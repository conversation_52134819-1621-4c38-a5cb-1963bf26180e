package com.vedeng.activiti.taskassign;

import com.vedeng.activiti.service.ActivitiAssigneeService;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.system.service.UserService;
import org.activiti.engine.HistoryService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.history.HistoricProcessInstance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ReceiveOutSendMessageListener implements ExecutionListener {
    private static Logger logger = LoggerFactory.getLogger(ReceiveOutSendMessageListener.class);

    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();

    private UserService userService = (UserService) context.getBean("userService");

    private ActivitiAssigneeService activitiAssigneeService = (ActivitiAssigneeService) context.getBean("activitiAssigneeService");

    private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();



    @Override
    public void notify(DelegateExecution execution) throws Exception {
        User user = null;

        if(RequestContextHolder.getRequestAttributes() == null){
            user = userService.getByUsername("njadmin",1);
        }else{
            ServletRequestAttributes ra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = ra.getRequest();
            user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
            if(user == null){
                user = userService.getByUsername("njadmin",1);
            }
        }

        // 申请人名称
        String preAssignee = execution.getVariable("currentAssinee").toString();
        User assigneeInfo = userService.getByUsernameEnable(preAssignee, user.getCompanyId());
        HistoryService historyService = processEngine.getHistoryService(); // 任务相关service
        String processInstanceId = execution.getProcessInstanceId();
        HistoricProcessInstance historicProcessInstance =
                historyService.createHistoricProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        String processDefinitionKey = historicProcessInstance.getProcessDefinitionKey();
        // 把list转为Map
        Map<String, Object> variables = execution.getVariables();

        List<Integer> varifyUserList = new ArrayList<>();

        // 准备发送参数
        // 模板ID
        Integer messageTemplateId = null;
        Map<String, String> map = new HashMap<>();
        String url = null;
        if (processDefinitionKey.equals("receiveOutAudit")) {
            //获取质量管理机构负责人
            try{
                List<Integer> userIdList = activitiAssigneeService.getUserIdListByPosition("质量机构负责人");

                varifyUserList.addAll(userIdList);

                if (execution.getCurrentActivityName().equals("审核完成")){
                    messageTemplateId = 248;
                }
                map.put("assigneeName",assigneeInfo.getUsername());
                map.put("receiveOutOrderNo",variables.get("receiveOutOrderNo").toString());
                url = "./wms/receiveOut/receiveDetail.do?receiveOutId="+variables.get("receiveOutOrderId").toString();
                MessageUtil.sendMessage(messageTemplateId, varifyUserList, map, url, preAssignee);
            }catch (Exception e){
                logger.error("发送站内信给质量机构负责人失败 单号:"+variables.get("receiveOutOrderNo").toString(),e);
            }

        }

    }
}
