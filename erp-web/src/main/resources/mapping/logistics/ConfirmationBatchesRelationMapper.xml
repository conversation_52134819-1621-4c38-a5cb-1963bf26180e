<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.logistics.dao.ConfirmationBatchesRelationMapper">

    <resultMap id="BaseResultMap" type="com.vedeng.logistics.model.ConfirmationBatchesRelation">
        <id property="id" column="ID" jdbcType="INTEGER"/>
        <result property="saleorderId" column="SALEORDER_ID" jdbcType="INTEGER"/>
        <result property="confirmationId" column="CONFIRMATION_ID" jdbcType="BIGINT"/>
        <result property="batchNo" column="BATCH_NO" jdbcType="VARCHAR"/>
        <result property="isEnable" column="IS_ENABLE" jdbcType="TINYINT"/>
        <result property="creator" column="CREATOR" jdbcType="INTEGER"/>
        <result property="modTime" column="MOD_TIME" jdbcType="BIGINT"/>
        <result property="updater" column="UPDATER" jdbcType="INTEGER"/>
        <result property="addTime" column="ADD_TIME" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,SALEORDER_ID,CONFIRMATION_ID,
        BATCH_NO,IS_ENABLE,
        CREATOR,MOD_TIME,UPDATER,
        ADD_TIME
    </sql>


    <update id="deleteByConfirmationIdBatch" parameterType="java.util.List">
        update T_CONFIRMATION_BATCHES_RELATION a
        <set>
            IS_ENABLE = 1
        </set>
        where
        a.CONFIRMATION_ID in
        <foreach item="item" index="index" collection="list" open="("
                 separator="," close=")">
            #{item}
        </foreach>
    </update>


    <select id="selectAllByRelation"
            parameterType="com.vedeng.logistics.model.ConfirmationBatchesRelation" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM T_CONFIRMATION_BATCHES_RELATION a
        WHERE 1=1
        AND a.IS_ENABLE = 0
        <if test="id!=null and id!='-1'">
            and a.ID=#{id,jdbcType=INTEGER}
        </if>
        <if test="saleorderId!=null and saleorderId!='-1'">
            and a.SALEORDER_ID=#{saleorderId,jdbcType=INTEGER}
        </if>
        <if test="confirmationId!=null and confirmationId!='-1'">
            and a.CONFIRMATION_ID=#{confirmationId,jdbcType=INTEGER}
        </if>
        <if test="batchNo!=null and batchNo!=''">
            and a.BATCH_NO like CONCAT('%',#{batchNo,jdbcType=VARCHAR},'%' )
        </if>

    </select>
    <select id="selectAllBySaleorderId" resultType="com.vedeng.logistics.model.ConfirmationBatchesRelation" parameterType="java.lang.Integer" >
        SELECT
            a.ID,a.SALEORDER_ID,a.CONFIRMATION_ID,
            a.BATCH_NO,a.IS_ENABLE,
            a.CREATOR,a.MOD_TIME,a.UPDATER,
            a.ADD_TIME,
            b.EXPRESS_ID as expressId,
            b.BATCH_NO as bno,
            c.UPLOAD_STATUS as uploadStatus ,
            c.AUDIT_STATUS as auditStatus
        FROM T_CONFIRMATION_BATCHES_RELATION a LEFT JOIN T_EXPRESS b ON a.BATCH_NO = b.BATCH_NO
                                               LEFT JOIN T_OUTBOUND_BATCHES_RECODE c on b.BATCH_NO = c.BATCH_NO
        WHERE 1=1
          AND a.IS_ENABLE = 0
          AND b.IS_ENABLE = 1
          AND c.IS_ENABLE = 0
          AND a.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
    </select>



    <insert id="insert" parameterType="com.vedeng.logistics.model.ConfirmationBatchesRelation">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into T_CONFIRMATION_BATCHES_RELATION (SALEORDER_ID, CONFIRMATION_ID, BATCH_NO,
        IS_ENABLE, CREATOR, MOD_TIME,
        UPDATER, ADD_TIME)
        values (#{saleorderId,jdbcType=INTEGER}, #{confirmationId,jdbcType=BIGINT}, #{batchNo,jdbcType=VARCHAR},
        #{isEnable,jdbcType=BIT}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT},
        #{updater,jdbcType=INTEGER}, #{addTime,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective" parameterType="com.vedeng.logistics.model.ConfirmationBatchesRelation">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into T_CONFIRMATION_BATCHES_RELATION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="saleorderId != null">
                SALEORDER_ID,
            </if>
            <if test="confirmationId != null">
                CONFIRMATION_ID,
            </if>
            <if test="batchNo != null">
                BATCH_NO,
            </if>
            <if test="isEnable != null">
                IS_ENABLE,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="saleorderId != null">
                #{saleorderId,jdbcType=INTEGER},
            </if>
            <if test="confirmationId != null">
                #{confirmationId,jdbcType=BIGINT},
            </if>
            <if test="batchNo != null">
                #{batchNo,jdbcType=VARCHAR},
            </if>
            <if test="isEnable != null">
                #{isEnable,jdbcType=BIT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
</mapper>
