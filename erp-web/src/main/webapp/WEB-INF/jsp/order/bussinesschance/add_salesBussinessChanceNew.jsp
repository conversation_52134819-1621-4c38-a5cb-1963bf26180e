<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="新增销售商机" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<%@ include file="../../component/remarkComponent.jsp"%>

<link rel="stylesheet"
      href="${pageContext.request.contextPath}/static/new/css/common/font/font.css">
<link rel="stylesheet"
      href="${pageContext.request.contextPath}/static/new/css/common/lib/suggestSelect.css">
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/suggestSelect.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/order/bussinesschance/add_salesBussinessChanceNew.js?rnd=${resourceVersionKey}"></script>

<style>
    .total-price{
        color: #ff3300;
    }
    .select-suggest {
        width: 450px;
    }

    .select-suggest .select-selected {
        padding-bottom: 24px;
    }

    .select-suggest .select-list-wrap {
        width: 450px;
    }

    .select-suggest .select-list-wrap li {
        float: none;
    }

    .select-suggest .select-list {
        width: 100%;
        max-width: none;
    }

    .select-suggest i {
        background: none;
    }

    .select-suggest .search-no-result {
        padding: 50px 0;
        text-align: center;
    }

    .select-suggest .search-no-result .icon-caution2 {
        color: #f60;
        float: none;
        vertical-align: -3px;
        margin-right: 5px;
        margin-bottom: 20px;
    }

    .select-suggest .select-opt-tip {
        text-align: left!important;
        padding-right: 120px;
        position: relative;
    }

    .select-suggest .select-opt-tip .select-opt-tip-txt {
        position: absolute;
        right: 0;
    }

    .select-suggest .sucsess-ok {
        float: none;
    }

    .list-no-prod {
        text-align: center;
        padding: 20px 0;
    }

    .list-no-prod .sucsess-ok {
        margin-top: 10px;
    }
    .btn.btn-blue {
        color: #fff;
        border: solid 1px #2e8ae6;
        background-color: #2e8ae6;
    }
</style>

<div class="formpublic formpublic1 pt0">
    <form method="post" id="myform"
          action="">
        <input type="hidden" name="formToken" class="sss" value="${formToken}"/>
        <input type="hidden" name="optType" id="optType" value="${optType}"/>
        <input type="hidden" name="applyType" value="${applyType}"/>
        <input type="hidden" name="isClues" value="${isClues}"/>
        <input type="hidden" name="businessCluesId" value="${businessCluesId}"/>
        <input type="hidden" name="type" value="392">
        <input type="hidden" name="bussinessParentId" id="bussinessParentId" value=""/>
        <input type="hidden" name="bussinessChanceId" value="${bussinessChance.bussinessChanceId}"/>
        <input type="hidden" id="linkStr" name="bncLink" value='${bussinessChance.bncLink}'/>
        <input type="hidden" id="TMH" name="TMH" value=''/>
        <div class="traderinfo">
            <div class="formtitle">客户信息</div>
            <ul class="payplan">
                <li>
                    <div class="infor_name infor_name72">
                        <span>*</span>
                        <label>客户名称</label>
                    </div>

                    <div class="f_left inputfloat">
                        <span class=" mr10 mt3" id="trader_name_span_1"></span>
                        <input  type="hidden" name="traderId" id="traderId_1" value="${traderCustomerVo.traderId}">
                        <input type="hidden" name="checktraderName" id="trader_name_1"
                               value="${traderCustomerVo.traderName}">
                        <%-- <span class="bt-bg-style bt-small bg-light-blue pop-new-data dropdown-menu"
                               layerParams='{"width":"800px","height":"300px","title":"搜索客户",
                               "link":"${pageContext.request.contextPath}/trader/customer/searchCustomer.do?indexId=1&searchTraderName="}'>搜索</span>--%>
                        <input type="hidden" name="traderName" value="${traderCustomerVo.traderName }">
                        <div class="J-trader-select-wrap"></div>
                        <input type="hidden" name="traderId" value="${traderCustomerVo.traderId}">
                        <div id="e-traderId" class="font-red " style="display: none">客户名称不允许为空</div>
                    </div>
                </li>
                <li>
                    <div class="infor_name infor_name72 ">
                        <span>*</span>
                        <label>地区</label>
                    </div>
                    <div class="f_left">
                        <input type="hidden" class="input-large" name="areaId" id="areaId_1"
                               value="${traderCustomerVo.areaId}">
                        <input type="hidden" class="input-large" name="areaIds" id="areaIds_1" value="${traderCustomerVo.areaIds}">
                        <input type="hidden" class="input-large" name="checkTraderArea"
                               value="${bussinessChance.checkTraderArea}">
                        <span id="traderAddress">${traderCustomerVo.address}</span>
                        <div id="e-areaId" class="font-red " style="display: none">地区不允许为空</div>
                    </div>
                </li>
                <li>
                    <div class="infor_name infor_name72 ">
                        <span>*</span>
                        <label>客户类型</label>
                    </div>
                    <div class="f_left inputfloat" id="customer_type_nature_div"></div>
                    <input  type="hidden" name="customerType" id="customerType_1"
                           value="${traderCustomerVo.customerType}">
                    <span id="customerTypeStr">${traderCustomerVo.customerTypeStr}</span>
                    <div id="e-customerType" class="font-red " style="display: none">客户类型不允许为空</div>
                </li>
                <li>
                    <div class="infor_name infor_name72 ">
                        <span>*</span>
                        <label>客户联系人</label>
                    </div>
                    <div class="f_left inputfloat">
                        <input type="hidden" name="traderContactName">
                        <input type="hidden" name="checkTraderContactName">
                        <input type="hidden" name="checkTraderContactTelephone">
                        <input type="hidden" name="checkTraderContactMobile">
                        <input type="hidden" name="telephone">
                        <input type="hidden" name="mobile">
                        <div id="traderContactIdMsg" style="clear:both"></div>
                        <select class="input-xx" name="traderContactId" id="traderContactId_1">
                            <c:if test="${contactCount ne 1}">
                            <option value=""  <c:if test="${traderContactList eq null}"> selected </c:if>>请选择</option>
                            </c:if>
                            <c:forEach var="item" items="${traderContactList}">
                                <option value="${item.traderContactId}"
                                        <c:if test="${bussinessChance.traderContactId eq item.traderContactId}">selected</c:if>>${ item.name}/${item.telephone}/${item.mobile}</option>
                            </c:forEach>
                        </select>
                        <div class="title-click nobor  pop-new-data J-add-contact" layerParams='{"width":"700px","height":"510px","title":"新增联系人","link":"/trader/customer/toAddContactPage.do?pageType=1&traderId=${traderCustomerVo.traderId}&traderCustomerId=${traderCustomerVo.traderCustomerId}"}'>
                            +添加联系人
                        </div>
                        <div id="e-traderContactId" class="font-red " style="display: none">客户联系人不允许为空</div>
                    </div>
                </li>
                <li>
                    <div class="infor_name infor_name72 ">
                        <span>*</span>
                        <label>联系人情况</label>
                    </div>
                    <div>
                        <input type="radio" name="isPolicymaker" value="1"><label>采购关键人</label>
                        <input type="radio" name="isPolicymaker" value="2"><label>非采购关键人</label>
                        <div id="e-isPolicymaker" class="font-red " style="display: none">联系人情况不允许为空</div>
                    </div>
                </li>
            </ul>
        </div>
        <div class="line"></div>
        <div class="chanceinfo">
            <div>
            <div style="display: inline-block" class="formtitle">商机信息</div>
                <div class="f_right  ">
                    <a id="link-href" class="pop-new-data title-click nobor" layerparams='{"width":"80%","height":"90%","title":"","link":"/order/bussinesschance/linkBncChoosePage.do?first=1&exceptId=${bussinessChance.bussinessChanceId}"}'>
                        关联商机
                    </a>
                </div>
            </div>
            <ul class="payplan">
                <li>
                    <div id="sourceName">
                        <div class="infor_name mt0">
                            <span>*</span>
                            <label>渠道名称</label>
                        </div>
                        <select name="communication" id="communication_1"
                            <c:if test="${bussinessChance.communication != null && bussinessChance.communication > 0  && 1 ne isLink}">disabled="disabled"</c:if>
                        >
                            <option value=""  <c:if test="${bussinessChance.communication eq null || bussinessChance.communication eq 0}"> selected </c:if>>
                                请选择
                            </option>
                            <c:forEach var="il" items="${inquiryList}">
                                <option value="${il.sysOptionDefinitionId}" <c:if
                                        test="${il.sysOptionDefinitionId eq bussinessChance.communication}"> selected </c:if>>${il.title}</option>
                            </c:forEach>
                        </select>
                        <div id="e-communication" class="font-red " style="display: none">渠道名称不允许为空</div>
                    </div>
                </li>
                <li <c:if test="${bussinessChance.communication != null && bussinessChance.communication > 0  && 1 ne isLink}">style="display: none" </c:if>>
                    <div class="infor_name">
                        <span>*</span>
                        <label>是否科研特麦帮</label>
                    </div>
                    <input type="radio" name="isTMH" id="isTMH" value="1" onclick="hideSourceName()"/><label>是</label>
                    <input type="radio" name="isTMH" id="isTMHN" value="0" onclick="showSourceName()"/><label>否</label>
                    <div id="e-isTMH" class="font-red " style="display: none">是否科研特麦帮不允许为空</div>
                </li>
                <li>
                    <div class="infor_name">
                        <span>*</span>
                        <label>产品分类</label>
                    </div>
                    <div class="f_left">
                        <c:if test="${not empty goodsTypeList }">
                            <c:forEach items="${goodsTypeList}" var="gyl">
                                <input type="radio" name="goodsCategory" value="${gyl.sysOptionDefinitionId}"
                                       <c:if test="${gyl.sysOptionDefinitionId eq bussinessChance.goodsCategory || (isClues eq 1 and gyl.sysOptionDefinitionId eq 388)}">checked="checked"</c:if>><label>${gyl.title}</label>
                            </c:forEach>
                        </c:if>
                        </select>
                        <div id="e-goodsCategory" class="font-red " style="display: none">产品分类不允许为空</div>
                    </div>
                </li>
                <li>
                    <div class="infor_name">
                        <span>*</span>
                        <label>商机等级</label>
                    </div>
                    <div class="f_left">
                        <c:if test="${not empty bussinessLevelList }">
                            <c:forEach items="${bussinessLevelList}" var="leve">
                                <input type="radio" name="bussinessLevel" value="${leve.sysOptionDefinitionId}"
                                       <c:if test="${leve.sysOptionDefinitionId eq bussinessChance.bussinessLevel}">checked="checked"</c:if>><label>${leve.title}</label>
                            </c:forEach>
                        </c:if>
                        </select>
                        <div id="e-bussinessLevel" class="font-red " style="display: none">商机等级不允许为空</div>
                    </div>
                </li>
                <li>
                    <div class="infor_name">
                        <span>*</span>
                        <label>商机阶段</label>
                    </div>
                    <div class="f_left">
                        <c:if test="${not empty bussinessStageList }">
                            <c:forEach items="${bussinessStageList}" var="stage">
                                <input type="radio" name="bussinessStage" value="${stage.sysOptionDefinitionId}"
                                       <c:if test="${stage.sysOptionDefinitionId eq bussinessChance.bussinessStage}">checked="checked"</c:if>><label>${stage.title}</label>
                            </c:forEach>
                        </c:if>
                        </select>
                        <div id="e-bussinessStage" class="font-red " style="display: none">商机阶段不允许为空</div>
                    </div>
                </li>
                <li>
                    <div class="infor_name">
                        <span>*</span>
                        <label>询价类型</label>
                    </div>
                    <div class="f_left">
                        <c:if test="${not empty enquiryTypeList }">
                            <c:forEach items="${enquiryTypeList}" var="enq">
                                <input type="radio" name="enquiryType" value="${enq.sysOptionDefinitionId}"
                                       <c:if test="${enq.sysOptionDefinitionId eq bussinessChance.enquiryType}">checked="checked"</c:if>><label>${enq.title}</label>
                            </c:forEach>
                        </c:if>
                        </select>
                        <div id="e-enquiryType" class="font-red " style="display: none">询价类型不允许为空</div>
                    </div>
                </li>
                <li>
                    <div class="infor_name">
                        <span>*</span>
                        <label>成单几率</label>
                    </div>
                    <div class="f_left">
                        <c:if test="${not empty orderRateList }">
                            <c:forEach items="${orderRateList}" var="rate">
                                <input type="radio" name="orderRate" value="${rate.sysOptionDefinitionId}"
                                       <c:if test="${rate.sysOptionDefinitionId eq bussinessChance.orderRate}">checked="checked"</c:if>><label>${rate.title}</label>
                            </c:forEach>
                        </c:if>
                        </select>
                        <div id="e-orderRate" class="font-red " style="display: none">成单几率不允许为空</div>
                    </div>
                </li>
                <li>
                    <div class="infor_name">
                        <span>*</span>
                        <label>预计金额</label>
                    </div>
                    <div>
                        <input type="text" name="amount" id="amount_1" value="${bussinessChance.amount}"
                               placeholder="请输入金额">
                        <div id="e-amount" class="font-red " style="display: none">预计金额不允许为空</div>
                    </div>
                </li>
                <li>
                    <div class="infor_name">
                        <span>*</span>
                        <label>预计成单时间</label>
                    </div>
                    <div class="f_left">
                        <input class="Wdate m0 input-middle Wdate2" name="orderTimeStr" id="orderTimeStr" type="text"
                               placeholder="请选择日期"
                               onFocus="WdatePicker({dateFmt:'yyyy-MM-dd'})" autocomplete="off"
                               value='<date:date value ="${bussinessChance.orderTime} " format="yyyy-MM-dd"/>'>
                        <div id="e-orderTimeStr" class="font-red " style="display: none">预计成单时间不允许为空</div>
                    </div>
                </li>
                <c:if test="${optType eq 'edit'}">
                    <li>
                        <div class="infor_name">
                            <label>产品备注（总机）</label>
                        </div>
                        <div>
                            <span>${bussinessChance.productComments}</span>
                        </div>
                    </li>
                </c:if>
                <li>
                    <div class="infor_name">
                        <label>产品备注（销售）</label>
                    </div>
                    <div class="f_left">
                            <textarea class="askprice" id="productCommentsSale" name="productCommentsSale"
                                      placeholder="请输入商机产品备注" >${bussinessChance.productCommentsSale}
                            </textarea>
                    </div>
                </li>
                <li>
                    <div class="infor_name">关联商机编号</div>
                    <div>
                        <c:if test="${not empty linkBnc}">
                            <c:forEach items="${linkBnc}" var="b">
                               <div style="background-color: #f3f3f3;display: inline-block;float: left;padding: 0 18px 0 5px;margin-left: 5px;position: relative">
                                    <div  style="display: inline-block;padding: 0px 5px" class="title-click addtitle"
                                         tabTitle='{"num":"view${b.id}","link":"./order/bussinesschance/toSalesDetailPage.do?bussinessChanceId=${b.id}","title":"商机详情"}'>${b.no}</div>
                                   <span style="position: absolute;right: 3px;top:9px" class="vd-icon icon-del-after" style="color: #333" onclick="deleteLinkBnc(${b.id},this)"></span>
                               </div>
                            </c:forEach>
                        </c:if>
                    </div>
                </li>
                <c:if test="${optType eq 'edit'}">
                <li>
                    <div class="infor_name">
                        <label>询价商品</label>
                    </div>
                    <div>
                        <span>${bussinessChance.content}</span>
                    </div>
                </li>
                </c:if>
            </ul>
        </div>
        <div class="line"></div>
        <div class="goodsinfo">
            <div class="formtitle">
                产品信息
                <span class="title-click   bg-light-bule pop-new-data bt-small mr10 bt-bg-style J-add-prod"
                      layerParams='{"width":"80%","height":"75%","title":"添加产品","link":"/order/quote/addQuoteGoods.do?optType=1&traderId=${bussinessChance.traderId}&quoteorderId="}'>添加</span>
            </div>
            <div class="prod-list J-prod-wrap">
                <table class="table table-bordered table-centered">
                    <colgroup>
                        <col width="150px">
                        <col width="80px">
                        <col width="80px">
                        <col width="80px">
                        <col width="50px">
                        <col width="80px">
                        <col width="80px">
                        <col width="80px">
                        <col width="80px">
                        <col width="80px">
                        <col width="80px">
                        <col width="80px">
                        <col width="150px">
                        <col width="80px">
                        <col width="80px">
                        <col width="60px">
                    </colgroup>
                    <thead>
                        <tr>
                            <th>产品名称</th>
                            <th>订货号</th>
                            <th>品牌</th>
                            <th>型号</th>
                            <th>单位</th>
                            <th>报价（元）</th>
                            <th>数量</th>
                            <th>含安调</th>
                            <th>货期（天）</th>
                            <th>直发</th>
                            <th>总额</th>
                            <th>库存</th>
                            <th>核价参考</th>
                            <th>产品备注</th>
                            <th>内部备注</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody class="J-prod-list">

                    </tbody>
                </table>
                <div class="total-price J-total-wrap" style="float: right;">
                    总<span class="J-total-num">0</span>件,总金额<span class="J-total-price" >0.00</span>元
                </div>
            </div>
            <div class="list-no-prod J-list-no-data">
                <div>据说,认真完善报价产品信息会增加成单概率哦!</div>
                <span class="sucsess-ok pop-new-data J-add-prod"
                      layerParams='{"width":"80%","height":"75%","title":"添加产品","link":"/order/quote/addQuoteGoods.do?optType=1&traderId=${bussinessChance.traderId}&quoteorderId="}'>添加报价产品信息</span>
            </div>
            <div class="J-table-error" id="J-table-error"></div>
        </div>
        <div class="line"></div>
        <div class="quoteinfo">
            <div class="formtitle">报价信息</div>
            <ul class="payplan">
                <li>
                    <div class="infor_name">
                        <span>*</span>
                        <label>采购方式</label>
                    </div>
                    <div class="f_left">
                        <c:if test="${not empty purchasingTypeList }">
                            <c:forEach items="${purchasingTypeList}" var="buytype">
                                <input type="radio" name="purchasingType"
                                       value="${buytype.sysOptionDefinitionId}"><label>${buytype.title}</label>
                            </c:forEach>
                        </c:if>
                        </select>
                        <div id="e-purchasingType" class="font-red " style="display: none">采购方式不允许为空</div>
                    </div>
                </li>
                <li>
                    <div class="infor_name">
                        <span>*</span>
                        <label>付款条件</label>
                    </div>
                    <div class="f_left">
                        <c:if test="${not empty payList }">
                            <c:forEach items="${payList}" var="item">
                                <input type="radio" name="paymentTerm" id="paymentTerm_1"
                                       value="${item.sysOptionDefinitionId}"
                                       <c:if test="${item.sysOptionDefinitionId == 408}">checked="checked"</c:if>
                                ><label>${item.title}</label>
                            </c:forEach>
                        </c:if>
                        </select>
                        <div id="e-paymentTerm" class="font-red " style="display: none">付款条件不允许为空</div>
                    </div>
                </li>
                <li>
                    <div class="infor_name">
                        <label>付款方式</label>
                    </div>
                    <div class="f_left">
                        <select name="paymentType" id="paymentTypeId">
                            <c:forEach var="item" items="${payTypeList}">
                                <option name="paymentType" value="${item.sysOptionDefinitionId}">${item.title}</option>
                            </c:forEach>
                        </select>
                        <input type="hidden" id="accountPeriodLeft" value="<fmt:formatNumber type="number" value="${traderCustomerVo.accountPeriodLeft}" pattern="0.00" maxFractionDigits="2" />">
                    </div>
                </li>
                <li class="J-payment-wrap1" style="display: none;">
                    <div class="infor_name">
                        <label>预付金额</label>
                    </div>
                    <div class="f_left">
                        <input type="text" class="input-large J-prepaidAmount-input" readonly name="prepaidAmount" id="prepaidAmount" value="">
                    </div>
                    <div id="prepaidAmountError"></div>
                </li>
                <li class="J-payment-wrap2" style="display: none;">
                    <div class="infor_name">
                        <label>账期支付</label>
                    </div>
                    <div class="f_left">
                        <input type="text" class="input-large J-accountPeriodAmount-input" readonly name="accountPeriodAmount" id="accountPeriodAmount" value="">
                        <label><input type="checkbox" name="logisticsCollection"
                               value="1">物流代收帐期款</label>
                    </div>
                    <div id="accountPeriodAmountError"></div>
                </li>
                <li class="J-last-pay" style="display: none;">
                    <div class="infor_name">
                        <label>尾款</label>
                    </div>
                    <div class="f_left">
                        <input type="text" class="input-large J-last-pay-input" name="retainageAmount" id="retainageAmount" value="">
                        <label>尾款期限</label>
                        <input type="text" class="input-large" name="retainageAmountMonth" id="retainageAmountMonth" value="">
                        <label>个月</label>
                    </div>
                    <div id="retainageAmountError"></div>
                </li>
                <li>
                    <div class="infor_name">
                        <label>项目进展情况</label>
                    </div>
                    <div>
                        <input type="text" class="input-large" name="projectProgress" id="projectProgress" value=""
                               placeholder="请填写客户项目信息,预算和进展情况等">
                    </div>
                </li>
                <li>
                    <div class="infor_name">
                        <span>*</span>
                        <label>采购时间</label>
                    </div>
                    <div class="f_left">
                        <c:if test="${not empty purchasingTimeList }">
                            <c:forEach items="${purchasingTimeList}" var="item">
                                <input type="radio" name="purchasingTime"
                                       value="${item.sysOptionDefinitionId}"><label>${item.title}</label>
                            </c:forEach>
                        </c:if>
                        </select>
                        <div id="e-purchasingTime" class="font-red " style="display: none">采购方式不允许为空</div>
                    </div>
                </li>
                <li>
                    <div class="infor_name">
                        <span>*</span>
                        <label>报价有效期</label>
                    </div>
                    <div>
                        <input type="text" name="period" id="period_1"  value="14">天
                    </div>
                    <label style="color: #999999"> -不允许超过30天 </label>
                </li>
                <li>
                    <div class="infor_name">
                        <label>发票类型</label>
                    </div>
                    <div class="f_left">
                        <select name="invoiceType" id="invoiceType">
                            <option value="">请选择</option>
                            <c:forEach var="item" items="${invoiceTypeList}">
                                <option name="paymentType" value="${item.sysOptionDefinitionId}">${item.title}</option>
                            </c:forEach>
                        </select>
                    </div>
                </li>
                <li>
                    <div class="infor_name">
                        <label>运费说明</label>
                    </div>
                    <div class="f_left">
                        <select name="freightDescription" id="freightDescription">
                            <option value="">请选择</option>
                            <c:forEach var="item" items="${freightDescriptionList}">
                                <option name="paymentType" value="${item.sysOptionDefinitionId}">${item.title}</option>
                            </c:forEach>
                        </select>
                    </div>
                </li>
                <li>
                    <div class="infor_name">
                        <label>附加条款</label>
                    </div>
                    <div>
                        <input type="text" class="input-large" name="additionalClause" id="additionalClause" value="">
                    </div>
                    <label style="color: #999999"> -补充报价相关条款,客户可见 </label>
                </li>
                <li>
                    <div class="infor_name">
                        <label>内部备注</label>

                    </div>
                    <div>
                        <input type="text" class="input-large" name="quoteComments" id="quoteComments" value="">
                    </div>
                    <label style="color: #999999">-帮助其他岗位同事了解报价情况,客户不可见,仅内容人员可见 </label>
                </li>
            </ul>
        </div>
        <div class="add-tijiao">
            <button class="btn btn-blue btn-large" name="submit" type="button" onclick="subForm()" data-type="save">保存</button>
<%--            <button class="btn btn-blue btn-large" type="button" onclick="subForm()">保存</button>--%>
           <%-- <button name="submit" type="submit" value="apply" data-type="saveAndapply">保存并审核</button>
            <button type="button" class="dele J-close-tab">取消</button>--%>
        </div>
    </form>

    <%--//历史未处理弹窗按钮--%>
    <div class="pop-new-data J-history-business-layer" style="display: none;"></div>

 <%--   //天眼查按钮--%>
    <div class="pop-new-data J-eye-layer" style="display: none;" layerParams='{"width":"800px","height":"600px","title":"天眼查",
                               "link":"${pageContext.request.contextPath}/trader/customer/add.do?optType=1&traderName", "noEncodeURI": true}'></div>
    <div class="pop-new-data J-eye-layer1" style="display: none;" layerParams='{"width":"800px","height":"600px","title":"手工创建",
                               "link":"${pageContext.request.contextPath}/trader/customer/add.do?optType=1"}'></div>
    <script class="J-empty" type="text/tmpl">
        <div class="search-no-result">
            <i class="vd-icon icon-caution2"></i>公司库未能匹配到该公司信息。
            <div class="btn-wrap">
                <span  class="sucsess-ok J-eye-look">天眼查</span>
                <span class="sucsess-ok J-eye-look1">手工创建</span>
            </div>
        </div>
    </script>

    <script type="text/tmpl" class="J-prod-tmpl">
        <tr class="J-prod-item">
            <td > {{=goodsName}}
                <input type="hidden" name="quoteorderGoods[i].goodsName" value="{{=goodsName}}">
            </td>
            <td> {{=sku}}
                <input type="hidden" name="quoteorderGoods[i].goodsId" class="J-sku" value="{{=goodsId}}">
                <input type="hidden" name="quoteorderGoods[i].sku" value="{{=sku}}">
            </td>
            <td > {{=brandName}}
                <input type="hidden" name="quoteorderGoods[i].brandName" value="{{=brandName}}">
            </td>
            <td > {{=model}}
              <input type="hidden" name="quoteorderGoods[i].model" value="{{=model}}">
            </td>
            <td>{{=unitName}}
                <input type="hidden" name="quoteorderGoods[i].unitName" value="{{=unitName}}"></td>
            <td><input type="text" name="quoteorderGoods[i].price" value="{{=channelPrice || ''}}" class="J-item-price J-num-parse"></td>
            <td><input type="text" name="quoteorderGoods[i].num" value="" class="J-item-num J-num-parse"></td>
            <td>
                <label><input type="radio" name="quoteorderGoods[i].haveInstallation" class="J-item-haveInstallation" value="1">是</label>
                <label><input type="radio" name="quoteorderGoods[i].haveInstallation" class="J-item-haveInstallation" checked="checked" value="0">否</label>
            </td>
            <td><input type="text" name="quoteorderGoods[i].deliveryCycle" class="J-item-deliveryCycle" value=""></td>
            <td>
            {{  if(isDirect === 0){ }}
            <label><input type="radio" name="quoteorderGoods[i].deliveryDirect" class="J-item-deliveryDirect" value="1">是</label>
                <label><input type="radio" name="quoteorderGoods[i].deliveryDirect" class="J-item-deliveryDirect" checked="checked" value="0">否</label>
            {{  } else { }}
            <label><input type="radio" name="quoteorderGoods[i].deliveryDirect" class="J-item-deliveryDirect" checked="checked" value="1">是</label>
                <label><input type="radio" name="quoteorderGoods[i].deliveryDirect" class="J-item-deliveryDirect"  value="0">否</label>
            {{  } }}

            </td>
            <<td><span class="J-item-total"></span></td></td>
            <td>{{=stockNum || ''}}</td>
            <td>
                核价参考价： <span class="J-item-price1">{{=channelPrice || ''}}</span><br>
                参考价格：  <span class="J-item-price2">{{=channelPrice || ''}}</span><br>
                参考货期：  <span class="J-item-price3">{{=referenceDeliveryCycle || ''}}</span><br>
                结算价：  <span class="J-item-price4">{{=settlementPrice || ''}}</span>

                <input type="hidden" name="quoteorderGoods[i].referenceDeliveryCycle" value="{{=referenceDeliveryCycle || ''}}">
                <input type="hidden" name="quoteorderGoods[i].referencePrice" value="{{=channelPrice || ''}}">
            </td>
            <td><input type="text" name="quoteorderGoods[i].goodsComments" value=""></td>
            <td class="c-comments" skuId="{{=goodsId}}">
                <div class="customername pos_rel f_left">
                    <input type="text" name="quoteorderGoods[i].insideComments" onclick="insideRemark(this,i)" label_data="" readOnly=true value="">
                    <i class="iconbluemouth contorlIcon" style="display: none;"></i>
                    <div class="pos_abs customernameshow" label_left="-330" style="width: 500px; top: 45px;background-color: #00CD66;"></div>
                </div>
                <div class="no_remark_error" style="display: none;">
                    <span style="color:red;">请设置订单要求，点击编辑进行内部备注修改</span>
                </div>
            </td>
            <td>
                <a class="J-prod-del">删除</a>
            </td>
        </tr>
    </script>


</div>
