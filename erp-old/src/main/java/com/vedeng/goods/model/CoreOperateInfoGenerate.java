package com.vedeng.goods.model;

public class CoreOperateInfoGenerate {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_OPERATE_INFO.OPERATE_INFO_ID
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    private Integer operateInfoId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_OPERATE_INFO.SPU_ID
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    private Integer spuId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_OPERATE_INFO.SKU_ID
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    private Integer skuId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_OPERATE_INFO.OPERATE_INFO_TYPE
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    private Integer operateInfoType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_OPERATE_INFO.GOODS_NAME
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    private String goodsName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_OPERATE_INFO.SEO_DESCRIPT
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    private String seoDescript;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_OPERATE_INFO.SEO_KEYWORDS
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    private String seoKeywords;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_OPERATE_INFO.SEO_TITLE
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    private String seoTitle;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_OPERATE_INFO.OPRATE_INFO_HTML
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    private String oprateInfoHtml;

    /**
     * V_CORE_OPERATE_INFO.OPERATE_INFO_SOURCE
     *
     * 商品描述来源 1:ERP富文本 2:马良
     * @see com.vedeng.goods.enums.OperateInfoSourceEnum
     */
    private Integer operateInfoSource;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_OPERATE_INFO.OPERATE_INFO_ID
     *
     * @return the value of V_CORE_OPERATE_INFO.OPERATE_INFO_ID
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    public Integer getOperateInfoId() {
        return operateInfoId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_OPERATE_INFO.OPERATE_INFO_ID
     *
     * @param operateInfoId the value for V_CORE_OPERATE_INFO.OPERATE_INFO_ID
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    public void setOperateInfoId(Integer operateInfoId) {
        this.operateInfoId = operateInfoId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_OPERATE_INFO.SPU_ID
     *
     * @return the value of V_CORE_OPERATE_INFO.SPU_ID
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    public Integer getSpuId() {
        return spuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_OPERATE_INFO.SPU_ID
     *
     * @param spuId the value for V_CORE_OPERATE_INFO.SPU_ID
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    public void setSpuId(Integer spuId) {
        this.spuId = spuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_OPERATE_INFO.SKU_ID
     *
     * @return the value of V_CORE_OPERATE_INFO.SKU_ID
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    public Integer getSkuId() {
        return skuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_OPERATE_INFO.SKU_ID
     *
     * @param skuId the value for V_CORE_OPERATE_INFO.SKU_ID
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_OPERATE_INFO.OPERATE_INFO_TYPE
     *
     * @return the value of V_CORE_OPERATE_INFO.OPERATE_INFO_TYPE
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    public Integer getOperateInfoType() {
        return operateInfoType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_OPERATE_INFO.OPERATE_INFO_TYPE
     *
     * @param operateInfoType the value for V_CORE_OPERATE_INFO.OPERATE_INFO_TYPE
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    public void setOperateInfoType(Integer operateInfoType) {
        this.operateInfoType = operateInfoType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_OPERATE_INFO.GOODS_NAME
     *
     * @return the value of V_CORE_OPERATE_INFO.GOODS_NAME
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    public String getGoodsName() {
        return goodsName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_OPERATE_INFO.GOODS_NAME
     *
     * @param goodsName the value for V_CORE_OPERATE_INFO.GOODS_NAME
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_OPERATE_INFO.SEO_DESCRIPT
     *
     * @return the value of V_CORE_OPERATE_INFO.SEO_DESCRIPT
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    public String getSeoDescript() {
        return seoDescript;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_OPERATE_INFO.SEO_DESCRIPT
     *
     * @param seoDescript the value for V_CORE_OPERATE_INFO.SEO_DESCRIPT
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    public void setSeoDescript(String seoDescript) {
        this.seoDescript = seoDescript;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_OPERATE_INFO.SEO_KEYWORDS
     *
     * @return the value of V_CORE_OPERATE_INFO.SEO_KEYWORDS
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    public String getSeoKeywords() {
        return seoKeywords;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_OPERATE_INFO.SEO_KEYWORDS
     *
     * @param seoKeywords the value for V_CORE_OPERATE_INFO.SEO_KEYWORDS
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    public void setSeoKeywords(String seoKeywords) {
        this.seoKeywords = seoKeywords;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_OPERATE_INFO.SEO_TITLE
     *
     * @return the value of V_CORE_OPERATE_INFO.SEO_TITLE
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    public String getSeoTitle() {
        return seoTitle;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_OPERATE_INFO.SEO_TITLE
     *
     * @param seoTitle the value for V_CORE_OPERATE_INFO.SEO_TITLE
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    public void setSeoTitle(String seoTitle) {
        this.seoTitle = seoTitle;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_OPERATE_INFO.OPRATE_INFO_HTML
     *
     * @return the value of V_CORE_OPERATE_INFO.OPRATE_INFO_HTML
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    public String getOprateInfoHtml() {
        return oprateInfoHtml;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_OPERATE_INFO.OPRATE_INFO_HTML
     *
     * @param oprateInfoHtml the value for V_CORE_OPERATE_INFO.OPRATE_INFO_HTML
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    public void setOprateInfoHtml(String oprateInfoHtml) {
        this.oprateInfoHtml = oprateInfoHtml;
    }

    public Integer getOperateInfoSource() {
        return operateInfoSource;
    }

    public void setOperateInfoSource(Integer operateInfoSource) {
        this.operateInfoSource = operateInfoSource;
    }
}