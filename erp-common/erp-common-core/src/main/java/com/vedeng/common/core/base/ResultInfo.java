package com.vedeng.common.core.base;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <b>Description:</b><br> json 数据模型
 * <AUTHOR>
 * @Note
 * <b>ProjectName:</b> erp
 * <br><b>PackageName:</b> com.vedeng.common.model
 * <br><b>ClassName:</b> ResultInfo
 * <br><b>Date:</b> 2017年4月25日 上午11:13:17
 */
@Data
public class ResultInfo<T> {


	private Integer code = -1;//0成功，-1失败

	private String message = "操作失败";

	private Object param;

	private T data;

	private Page page;

	private List<T> listData;//备用字段

	private Integer status = 0;

	private Long count;

	public ResultInfo() {
		super();
	}

	public ResultInfo(Integer code) {
		this.code = code;
	}

	public ResultInfo(Integer code, String message) {
		super();
		this.code = code;
		this.message = message;
	}

	public ResultInfo(Integer code, String message, Integer status, T data) {
		super();
		this.code = code;
		this.message = message;
		this.status = status;
		this.data = data;
	}

	public ResultInfo(Integer code, String message, T data) {
		super();
		this.code = code;
		this.message = message;
		this.data = data;
	}

	public ResultInfo(Integer code, String message, List<T> listData) {
		super();
		this.code = code;
		this.message = message;
		this.listData = listData;
	}


	public static ResultInfo success(){
		return new ResultInfo(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage());
	}

	public static<T> ResultInfo<T> success(T data){
		return new ResultInfo<>(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), data);
	}

	public static<T> ResultInfo<T> success(String message, T data){
		return new ResultInfo<>(ResultCode.SUCCESS.getCode(), message, data);
	}

	public static ResultInfo error(){
		return new ResultInfo(ResultCode.ERROR.getCode(), ResultCode.ERROR.getMessage());
	}

	public static ResultInfo error(String message){
		return new ResultInfo(ResultCode.ERROR.getCode(), StringUtils.isNotEmpty(message) ? message : ResultCode.ERROR.getMessage());
	}

	public static<T> ResultInfo<T> error(T data){
		return new ResultInfo<>(ResultCode.ERROR.getCode(), ResultCode.ERROR.getMessage(), data);
	}


	private enum ResultCode{
		SUCCESS(0,"操作成功"),
		ERROR(-1,"操作失败")
		;
		private Integer code;
		private String message;

		ResultCode(Integer code, String message) {
			this.code = code;
			this.message = message;
		}


		public Integer getCode() {
			return code;
		}

		public String getMessage() {
			return message;
		}
	}

}
