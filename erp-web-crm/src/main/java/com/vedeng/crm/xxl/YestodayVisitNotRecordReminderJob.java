package com.vedeng.crm.xxl;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.crm.visitrecord.service.CrmVisitRecordService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 前一天的拜访记录未填写的情况-第二天早上9点提示
 * - 查询前一天未填写拜访记录的人员，打卡时间是昨天的，拜访计划的状态为拜访中，不关心是否拜访计划时间是否是昨天。
 */
@Component
@JobHandler(value="YestodayVisitNotRecordReminderJob")
@Slf4j
public class YestodayVisitNotRecordReminderJob extends AbstractJobHandler {

    @Autowired
    private CrmVisitRecordService crmVisitRecordService;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("==================当天待拜访提醒job开始====================");
        crmVisitRecordService.sendYestodayNotRecordReminder();
        XxlJobLogger.log("==================当天待拜访提醒job结束====================");
        return SUCCESS;
    }
} 
