package com.vedeng.erp.finance.dto;

import lombok.Data;

import java.util.List;

/**
 * 发票校验结果Dto
 */
@Data
public class InvoiceCheckResultDto {

    private Boolean success = true;

    private List<InvoiceCheckResultDetailDto> invoiceCheckResultDetailDtoList;

    @Data
    public static class InvoiceCheckResultDetailDto {

        /**
         * 规则编码
         */
        private String ruleCode;

        /**
         * 规则名称
         */
        private String ruleName;

        /**
         * 规则内容
         */
        private String ruleContent;

        /**
         * 提示文本
         */
        private String promptText;
        /**
         * 填写的备注
         */
        private String applyReason;
    }

    public void setFail(){
        this.success = false;
    }

    public void addDetail(InvoiceCheckResultDto.InvoiceCheckResultDetailDto invoiceCheckResultDetailDto) {
        this.invoiceCheckResultDetailDtoList.add(invoiceCheckResultDetailDto);
    }
}
