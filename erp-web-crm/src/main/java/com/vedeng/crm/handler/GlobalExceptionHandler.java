package com.vedeng.crm.handler;

import cn.hutool.core.date.DatePattern;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.base.BaseResponseCode;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.exception.ClientMustBeWeixinException;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.exception.UserLimitException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.beans.propertyeditors.CustomNumberEditor;
import org.springframework.beans.propertyeditors.StringTrimmerEditor;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.support.ByteArrayMultipartFileEditor;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.TimeZone;

/**
 * 全局异常处理
 *
 * <AUTHOR>
 * @date 2020-09-10 上午 10:42
 */
@Slf4j
@RestControllerAdvice(annotations = ExceptionController.class)
@ControllerAdvice(annotations = ExceptionController.class)
public class GlobalExceptionHandler {

    @Value(value = "${noPowerContract:Wyatt}")
    private String noPowerContract;


    @InitBinder
    protected void initBinder(WebDataBinder binder) {
        // 去除前端传入的前后空格
        binder.registerCustomEditor(String.class, new StringTrimmerEditor(false));
        SimpleDateFormat dateTimeFormat = new SimpleDateFormat(DatePattern.NORM_DATETIME_PATTERN);
        // 格式化日期格式
        dateTimeFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
        binder.registerCustomEditor(java.util.Date.class, new CustomDateEditor(dateTimeFormat, true));
        // 格式化double类型
        binder.registerCustomEditor(byte[].class, new ByteArrayMultipartFileEditor());
        binder.registerCustomEditor(Short.class, new CustomNumberEditor(Short.class, true));
        binder.registerCustomEditor(Integer.class, new CustomNumberEditor(Integer.class, true));
        binder.registerCustomEditor(Long.class, new CustomNumberEditor(Long.class, true));
        binder.registerCustomEditor(Float.class, new CustomNumberEditor(Float.class, true));
        binder.registerCustomEditor(Double.class, new CustomNumberEditor(Double.class, true));
        binder.registerCustomEditor(BigDecimal.class, new CustomNumberEditor(BigDecimal.class, true));
        binder.registerCustomEditor(BigInteger.class, new CustomNumberEditor(BigInteger.class, true));
    }


    /**
     * 捕捉系统异常
     *
     * <AUTHOR>
     * @date 2020-09-10 上午 10:41
     */
    @ExceptionHandler(Exception.class)
    public void handleIllegalArgumentException(HttpServletRequest request, HttpServletResponse response,Exception e) {
         log.error("系统异常 {}", e.getMessage(), e);
        // 判断是否是AJAX请求
        boolean isAjax = "XMLHttpRequest".equals(request.getHeader("X-Requested-With"));
        String contentType = request.getHeader("Content-Type");
        isAjax = isAjax||   (contentType != null && contentType.contains("application/json"));

        if (isAjax) {
            String responseText = JSON.toJSONString( R.error(BaseResponseCode.SYSTEM_BUSY));
            try {
                response.setContentType("application/json;charset=utf-8");
                response.getWriter().write(responseText);
                response.getWriter().flush();
                response.getWriter().close();
            } catch (Exception e1) {
                log.error("响应系统异常失败", e1);
            }
        }else{
            try {
                response.sendRedirect("/crm/500");
            } catch (Exception e2) {
                log.error("响应系统异常失败", e2);
            }
        }
    }

    /**
     * 捕捉自定义业务异常
     *
     * <AUTHOR>
     * @date 2020-09-10 上午 10:41
     */
    @ExceptionHandler(ServiceException.class)
    public R serviceException(HttpServletRequest request, ServiceException ex) {
        log.warn("业务异常 {}", ex.getMessage(), ex);
        return R.error(getStatus(request).value(), ex.getMessage());
    }


    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(UserLimitException.class)
    public void userLimitException(HttpServletRequest request, HttpServletResponse response, UserLimitException ex) {
        log.warn("权限异常 {}", ex.getMessage(), ex);
        // 判断是否是AJAX请求
        boolean isAjax = "XMLHttpRequest".equals(request.getHeader("X-Requested-With"));
        if (isAjax) {
            String responseText = JSON.toJSONString(R.error(BaseResponseCode.UNAUTHORIZED_ACCESS.getCode(),"您暂时还未开通此权限，请联系研发部"+noPowerContract+"开通权限。"));
            try {
                response.setContentType("application/json;charset=utf-8");
                response.getWriter().write(responseText);
                response.getWriter().flush();
                response.getWriter().close();
            } catch (Exception e) {
                log.error("响应权限异常失败", e);
            }
        }else{
            try {
                response.sendRedirect("/crm/noPermission");
            } catch (Exception e) {
                log.error("响应权限异常失败", e);
            }
        }
    }

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(ClientMustBeWeixinException.class)
    public void clientMustBeWeixinException(HttpServletRequest request, HttpServletResponse response, ClientMustBeWeixinException ex) {
        log.warn("客户端异常 {}", ex.getMessage(), ex);
        // 判断是否是AJAX请求
        boolean isAjax = "XMLHttpRequest".equals(request.getHeader("X-Requested-With"));
        if (isAjax) {
            String responseText = JSON.toJSONString(R.error(BaseResponseCode.UNAUTHORIZED_ACCESS.getCode(),"请使用微信客户端访问，请联系研发部"+noPowerContract+"开通权限。"));
            try {
                response.setContentType("application/json;charset=utf-8");
                response.getWriter().write(responseText);
                response.getWriter().flush();
                response.getWriter().close();
            } catch (Exception e) {
                log.error("响应权限异常失败", e);
            }
        }else{
            //注意，ajax请求时，不执行此cookie写入流程
            if(StringUtils.isNotBlank(ex.getMessage())){
                //将loginToken写入到Cookie返回前端
                Cookie cookie = new Cookie("lxcrmTargetUrl", ex.getMessage());
                cookie.setPath("/");
                cookie.setHttpOnly(true);
                cookie.setMaxAge(60);//有效期仅一分钟，仅做登录重定向使用
                response.addCookie(cookie);
            }
            try {
                response.sendRedirect("/crm/clientMustBeWeixin");
            } catch (Exception e) {
                log.error("响应权限异常失败", e);
            }
        }
    }



    /**
     * 捕捉请求异常
     *
     * @param e :
     * <AUTHOR>
     * @date 2020-09-10 上午 10:41
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public R handleIllegalArgumentException(HttpRequestMethodNotSupportedException e) {
        log.error("【全局异常拦截】HttpRequestMethodNotSupportedException: 当前请求方式 {}, 支持请求方式 {}",
                e.getMethod(), JSONUtil.toJsonStr(e.getSupportedHttpMethods()));
        return R.error(BaseResponseCode.HTTP_BAD_METHOD);
    }


    /**
     * validation Exception
     *
     * @param exception
     * @return R
     */
    @ExceptionHandler({ MethodArgumentNotValidException.class })
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R handleBodyValidException(MethodArgumentNotValidException exception) {
        List<FieldError> fieldErrors = exception.getBindingResult().getFieldErrors();
        log.error("参数绑定异常,ex = {}", fieldErrors.get(0).getDefaultMessage());
        return R.error(fieldErrors.get(0).getDefaultMessage());
    }

    /**
     * validation Exception (以form-data形式传参)
     *
     * @param exception
     * @return R
     */
    @ExceptionHandler({ BindException.class })
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R bindExceptionHandler(BindException exception) {
        List<FieldError> fieldErrors = exception.getBindingResult().getFieldErrors();
        log.error("参数绑定异常,ex = {}", fieldErrors.get(0).getDefaultMessage());
        return R.error(fieldErrors.get(0).getDefaultMessage());
    }


    private HttpStatus getStatus(HttpServletRequest request) {
        Integer statusCode = (Integer) request.getAttribute("javax.servlet.error.status_code");
        if (statusCode == null) {
            return HttpStatus.INTERNAL_SERVER_ERROR;
        }
        return HttpStatus.valueOf(statusCode);
    }

}
