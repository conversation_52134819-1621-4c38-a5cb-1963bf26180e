package com.vedeng.erp.kingdee.batch.writer;

import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeeFileDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeFileCommandConvertor;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购出入库
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class PurchaseInAcceptanceFormWriter extends BaseWriter<KingDeeFileDto> {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;
    @Autowired
    private KingDeeFileCommandConvertor kingDeeFileCommandConvertor;


    @Override
    public void doWrite(KingDeeFileDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {

//        log.info("入库单验收附件上传{}",JSON.toJSONString(dto));
//        RepoRet fileResult = kingDeeBaseApi.attachmentUpload(kingDeeFileCommandConvertor.toCommand(dto));
//
//        if (fileResult == null) {
//            log.info("入库验收单上传金蝶失败");
//            throw new ServiceException("采购入库验收单上传金蝶失败");
//        } else {
//            if (fileResult.getResult().getResponseStatus().isIsSuccess()) {
//                //收到成功保存附件信息
//                log.info("入库验收单上传金蝶成功{},", JSON.toJSONString(fileResult));
//            } else {
//                log.info("入库验收单上传金蝶失败,金蝶反馈{},", JSON.toJSONString(fileResult));
//            }
//        }

    }


}
