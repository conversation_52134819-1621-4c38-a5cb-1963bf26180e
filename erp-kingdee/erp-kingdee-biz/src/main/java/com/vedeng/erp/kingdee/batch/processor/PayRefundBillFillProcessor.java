package com.vedeng.erp.kingdee.batch.processor;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchBankBillDto;
import com.vedeng.erp.kingdee.dto.KingDeeFileDataDto;
import com.vedeng.erp.kingdee.dto.KingDeePayRefundBillDto;
import com.vedeng.erp.kingdee.service.KingDeeFileDataService;
import com.vedeng.erp.kingdee.service.KingDeePayRefundBillService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;

@Slf4j
@Service
public class PayRefundBillFillProcessor extends BaseProcessor<BatchBankBillDto, KingDeeFileDataDto> {
    public static final String ZERO = "0";

    @Autowired
    private KingDeePayRefundBillService kingDeePayRefundBillService;

    @Autowired
    private KingDeeFileDataService kingDeeFileDataService;

    @Override
    public KingDeeFileDataDto doProcess(BatchBankBillDto batchBankBillDto, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("金蝶付款退款单未推送回单对应的erp流水信息为{},", JSON.toJSON(batchBankBillDto));
        if (StringUtils.isBlank(batchBankBillDto.getReceiptUrl())) {
            log.info("该银行流水不存在回单信息");
            return null;
        }

        KingDeeFileDataDto query = KingDeeFileDataDto.builder()
                .formId(KingDeeFormConstant.PAY_REFUND)
                .erpId(batchBankBillDto.getBankBillId().toString())
                .url(batchBankBillDto.getReceiptUrl())
                .build();
        List<KingDeeFileDataDto> existFile = kingDeeFileDataService.getByBusinessIdAndUri(query);
        if (!CollectionUtils.isEmpty(existFile)) {
            log.info("当前附件已经推送过金蝶，{}", JSON.toJSONString(query));
            return null;
        }

        KingDeePayRefundBillDto kingDeePayRefundBillDto = new KingDeePayRefundBillDto();
        if (KingDeeConstant.ZERO.equals(batchBankBillDto.getStatus()) && batchBankBillDto.getMatchedAmount().compareTo(BigDecimal.ZERO) != 0) {
            kingDeePayRefundBillDto.setFBillNo("C_" + batchBankBillDto.getBankBillId());
        } else {
            kingDeePayRefundBillDto.setFBillNo("B_" + batchBankBillDto.getBankBillId());
        }
        kingDeePayRefundBillService.query(kingDeePayRefundBillDto);
        if (kingDeePayRefundBillDto.getFId() == null || ZERO.equals(kingDeePayRefundBillDto.getFId())) {
            log.info("上传出库单附件,金蝶付款退款单未推送金蝶：{}", JSON.toJSONString(batchBankBillDto));
            return null;
        }
        return KingDeeFileDataDto.builder()
                .fileName(batchBankBillDto.getBankBillId() + ".pdf")
                .aliasFileName(batchBankBillDto.getBankBillId() + ".pdf")
                .billNo(kingDeePayRefundBillDto.getFBillNo())
                .formId(KingDeeFormConstant.PAY_REFUND)
                .isLast(true)
                .fId(kingDeePayRefundBillDto.getFId())
                .url(batchBankBillDto.getReceiptUrl())
                .erpId(batchBankBillDto.getBankBillId().toString())
                .businessId(kingDeePayRefundBillDto.getFormId() + batchBankBillDto.getBankBillId().toString())
                .build();
    }
}
