package com.newtask;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;

/**
 * 拉取航信进项发票定时任务
 * <AUTHOR>
 * @date created in 2020/5/18 13:52
 */
@JobHandler(value = "pullHxIncomeInvoiceHandler")
public class PullHxIncomeInvoiceHandler extends AbstractJobHandler {


    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        return null;
    }
}
