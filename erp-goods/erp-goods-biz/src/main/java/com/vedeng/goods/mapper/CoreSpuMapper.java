package com.vedeng.goods.mapper;
import java.util.Collection;

import com.vedeng.goods.domain.entity.CoreSpu;
import com.vedeng.goods.dto.RegistrationNumberSpuDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Entity com.vedeng.erp.goods.model.CoreSpu
 */
@Repository("newCoreSpuMapper")
public interface CoreSpuMapper {

    /**
     * 根据spuId查询注册证信息
     *
     * @param spuId
     * @return
     */
    RegistrationNumberSpuDto queryRegistrationNumberBySpuId(@Param("spuId") Integer spuId);

    /**
     * 根据spuId查询注册证和生产厂家信息
     *
     * @param spuId
     * @return
     */
    RegistrationNumberSpuDto queryRegistraAndManufactureBySpuId(@Param("spuId") Integer spuId);

    /**
     * @Description 根据spuIds批量更新spu的启用状态和审核状态
     * @Param list
     * @Return {@link int}
     */
    int updateStatusAndCheckStatusBySpuIds(List<CoreSpu> list);

    /**
     * 根据分类id集合更新spu的税收分类编码
     */
    int updateTaxClassificationCodeByCategoryIdIn(@Param("updatedTaxClassificationCode") String updatedTaxClassificationCode, @Param("categoryIdCollection") Collection<Integer> categoryIdCollection, @Param("userId") Integer userId);

}




