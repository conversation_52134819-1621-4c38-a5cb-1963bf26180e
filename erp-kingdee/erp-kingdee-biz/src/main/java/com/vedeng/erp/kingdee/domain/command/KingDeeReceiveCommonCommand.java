package com.vedeng.erp.kingdee.domain.command;

import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 预处理金蝶费用应收单 dto  金蝶入参
 * @date
 */
@NoArgsConstructor
@Data
@Getter
@Setter
public class KingDeeReceiveCommonCommand {

    /**
     * 0：表示新增，非0：云星空系统单据FID值，表示修改
     */
    private int fId;

    /**
     * 格式yyyy-MM-dd
     */
    private String fDate;
    /**
     * 贝登单据头ID号（预留）
     */
    private String F_QZOK_BDDJTID;

    /**
     * 业务类型 默认BZ
     */
    private String fBusinessType;

    /**
     * 立账类型 默认填写1
     */
    private String fSetAccountType;


    /**
     * 填单据类型编码，默认"YSD01_SYS"
     */
    private KingDeeNumberCommand fBillTypeID = new KingDeeNumberCommand();

    /**
     * 填写客户编码
     */
    private KingDeeNumberCommand fCustomerId = new KingDeeNumberCommand();

    /**
     * 结算组织 填写组织编码
     */
    private KingDeeNumberCommand fSettleOrgId = new KingDeeNumberCommand();

    /**
     * 付款组织 填写组织编码
     */
    private KingDeeNumberCommand  fPayOrgId = new KingDeeNumberCommand();

    /**
     * 明细
     */
    private List<FEntityDetail> fEntityDetail = new ArrayList<>();

    /**
     * FEntityDetail
     */
    @NoArgsConstructor
    @Data
    public static class FEntityDetail {
        /**
         * 计价数量 填写数量（退货负数）
         */
        private String fPriceQty;
        /**
         * 含税单价
         */
        private String fTaxPrice;
        /**
         * 税率%
         */
        private String fEntryTaxRate;
        /**
         * 不含税金额
         */
        private String fNoTaxAmountFor_D;
        /**
         * 税额
         */
        private String fTaxAmountFor_D;
        /**
         * 价税合计
         */
        private String fAllAmountFor_D;

        /**
         * 是否赠品 默认：false
         */
        private Boolean fIsFree;

        /**
         * 贝登单据行ID
         */
        private String F_QZOK_BDDJHID;
        /**
         * 源单类型 关联单据类型，入库单默认：SAL_OUTSTOCK   退货单：SAL_RETURNSTOCK
         */
        private String fSourceType;
        /**
         * 明细
         */
        private List<FEntityDetailLink> FEntityDetail_Link;

        //Fnumber
        /**
         * 填写物料编码
         */
        private KingDeeNumberCommand  fMaterialId = new KingDeeNumberCommand();


        /**
         * FEntityDetail
         */
        @NoArgsConstructor
        @Data
        public static class FEntityDetailLink {
            /**
             * 关联入库必填，默认0
             */
            private String fLinkId;
            /**
             * 关联入库必填默认：AR_OutStockToReceivableMap 关联退货必填默认：AR_ReturnToReceivableMap
             */
            private String fEntityDetail_link_fruleId;
            /**
             * 关联入库必填，默认0
             */
            private String fEntityDetail_link_fflowlineId;
            /**
             * 关联入库必填，默认0
             */
            private String fEntityDetail_link_fstableId;
            /**
             * 关联入库必填默认：T_SAL_OUTSTOCKENTRY
             * 关联退货必填：T_SAL_RETURNSTOCKENTRY
             */
            private String fEntityDetail_link_fstableName;
            /**
             * 关联入库必填，关联入库内码，填写金蝶入库单内码FID
             */
            private String fEntityDetail_link_fsbillId;
            /**
             * 关联入库必填，关联入库单行内码,填写金蝶入库单行内码FEntryID
             */
            private String fEntityDetail_link_fsId;
            /**
             * 取源单总数量（退货负数）
             */
            private String fEntityDetail_link_fbasicunitqtyold;
            /**
             * 取实际数量（退货负数）
             */
            private String fEntityDetail_link_fbasicunitqty;
            /**
             * 取源单总数量（退货负数）
             */
            private String fEntityDetail_link_fsalbaseqtyold;
            /**
             * 取实际数量（退货负数）
             */
            private String fEntityDetail_link_fsalbaseqty;
            /**
             * 取源单总数量（退货负数）
             */
            private String fEntityDetail_link_fstockbaseqtyold;
            /**
             * 取实际数量（退货负数）
             */
            private String fEntityDetail_link_fstockbaseqty;
            /**
             * 取出库关联金额 （出库数量*出库含税单价）（退货负数）
             */
            private String fEntityDetail_link_fallamountfor_Dold;
            /**
             * 取出库关联金额 （出库数量*出库含税单价）（退货负数）
             */
            private String fEntityDetail_link_fallamountfor_D;

        }
    }

}
