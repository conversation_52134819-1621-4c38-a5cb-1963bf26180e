package com.vedeng.erp.aftersale.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 原始可退票蓝票聚合信息
 * @date 2022/11/1 15:21
 **/
@Data
public class ReturnInvoiceDto {

    /**
     * 售后单id
     */
    private Long expenseAfterSalesId;

    /**
     * 蓝发票代码
     */
    private String invoiceCode;

    /**
     * 蓝发票号码
     */
    private String invoiceNo;

    /**
     * 退票发票代码
     */
    private String invoiceCodeRed;

    /**
     * 退票发票号码
     */
    private String invoiceNoRed;

    /**
     * 发票类型str
     */
    private String invoiceTypeStr;

    /**
     * 发票类型
     */
    private Integer invoiceType;

    /**
     * 发票税率
     */
    private BigDecimal ratio;

    /**
     * 售后单类型
     */
    private Integer expenseAfterSalesType;

    /**
     * 当前票的总额
     */
    private BigDecimal amount;

    /**
     * 1纸质发票2电子发票 3 数电发票
     */
    private Integer invoiceProperty;

    /**
     * 商品
     */
    private List<ReturnInvoiceGoodsDto> returnInvoiceGoodsDtos;
}
