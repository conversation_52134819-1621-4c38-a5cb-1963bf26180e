package com.vedeng.erp.trader.domain.dto;

import com.vedeng.onedataapi.api.trader.req.TraderAreaSearchDto;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 终端数据
 */
@Getter
@Setter
public class TerminalDistributionLinkRequestDto {

    /**
     * 终端名称
     */
    private String terminalName;

    /**
     * 合作时间
     */
    private Integer cooperationTime;

    /**
     * 地区
     */
    private List<TraderAreaSearchDto> traderAreaSearchList;

    /**
     * 最新中标时间开始
     */
    private String lastBiddingTimeStart;

    /**
     * 最新中标时间结束
     */
    private String lastBiddingTimeEnd;

    /**
     * 最新交易时间开始
     */
    private String lastSaleTimeStart;

    /**
     * 最新交易时间结束
     */
    private String lastSaleTimeEnd;

    /**
     * 合作情况 0未合作 1已合作  其他值视为全部
     */
    private Integer cooperationStatus;
    /**
     * 排排序的字段
     *
     * 5:最近中标时间
     *
     * 6:最近交易时间
     *
     */
    private Integer sortColumn;

    /**
     * 0 升序 1降序 (默认升序)
     */
    private Integer sortType;

}