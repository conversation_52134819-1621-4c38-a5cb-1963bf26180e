$(function() {
	var $form = $("#addUnitKingDee");
	$form.submit(function (){
		checkLogin();
		let inputs = $("input");
		var i = 0;
		var flag = false;
		inputs.each(function () {
			i = i + 1;
			if($(this).val().trim() == ''){
				layer.alert($(this).attr('text')+'未填写');
				flag = true;
				return;
			}
		});
		if(flag){
			return false;
		}
		$.ajax({
			url:page_url+'/goods/unit/addUnitKingDee.do',
			data:$('#addUnitKingDee').serialize(),
			type:"POST",
			dataType : "json",
			async: false,
			success:function(data)
			{
				if(data.code==0){
					window.parent.location.reload();
				}else{
					layer.alert(data.message);
				}

			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}else {
					layer.alert(data.message);
				}
			}
		});
		return false;
	});
});



function delOne(obj){
	obj.parentElement.parentElement.remove();
}
function add() {
	$("#kingDeeTbody").prepend("<tr class='tr'> <td> <input type='text' class='input-small' name='unitKingDeeNo' maxlength='10' text='金蝶单位编号'/></td> <td> <input type='text' class='input-small' name='unitKingDeeName' maxlength='10' text='金蝶单位名称'/></td> <td><input  class='bt-small bt-bg-style bg-light-blue' type='button' value='删行' onclick='delOne(this)'/></td> </tr>");
};