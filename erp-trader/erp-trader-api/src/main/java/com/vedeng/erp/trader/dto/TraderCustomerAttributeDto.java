package com.vedeng.erp.trader.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/11 13:40
 */
@Getter
@Setter
public class TraderCustomerAttributeDto {

    /**
     * 主键id
     */
    private Integer traderCustomerAttributeId;

    /**
     * 客户ID
     */
    private Integer traderCustomerId;

    /**
     * 属性分类ID
     */
    private Integer attributeCategoryId;

    /**
     * 属性ID SYS_OPTION_DEFINITION_ID
     */
    private Integer attributeId;

    /**
     * 其它属性
     */
    private String attributeOther;

    /**
     * 子分类控制
     */
    private String subCategoryIds;

    /**
     * 属性名称
     */
    private String attributeName;

    /**
     * 属性值
     */
    private String attributeValue;
}