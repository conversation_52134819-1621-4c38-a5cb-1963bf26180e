<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="修改线索价值" scope="application" />
<%@ include file="../common/common.jsp"%>
<div class="form-list">
    <form >
        <input type="hidden" name="businessCluesId" value="${businessCluesVo.businessCluesId}">
        <ul>
            <li>
                <div class="form-tips">
                    <span class="must">*</span><lable>线索价值</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <input type="radio" name="worth" value="1" <c:if test="${businessCluesVo.worth eq 1}">checked="checked"</c:if>> <label>有效</label>
                        <input type="radio" name="worth" value="0" <c:if test="${businessCluesVo.worth eq 0}">checked="checked"</c:if>> <label>无效</label>
                    </div>
                    <div id="worthError"></div>
                </div>
            </li>
            <li>
                <div class="form-tips">
                    <lable>备注</lable>
                </div>
                <div class="f_left ">
                    <div>
                        <textarea type="input-textarea" class="input-larger" name="comment" id="comment" style="height: 100px">${businessCluesVo.comment}</textarea>
                    </div>
                    <div id="commentError"></div>
                </div>
            </li>
        </ul>
        <div class="add-tijiao tcenter">
            <input class="bg-light-blue bt-bg-style bt-middle" type="button" id="saveCluesWorth" value="提交">
            <button class="dele" type="button" id="close-layer">取消</button>
        </div>
    </form>
</div>
<%@ include file="../common/footer.jsp"%>
<script>
    $("#saveCluesWorth").click(function () {
        let worth = $("input[type='radio'][name='worth']:checked").val();
        if (worth == undefined) {
            warnTips("worthError","请选择线索价值");
            return false;
        }
        let comment = $("#comment").val();
        if (comment.length > 200) {
            warnTips("commentError","备注最多不超过200字");
            return false;
        }

        $.ajax({
            type: "POST",
            url: '/business/clues/saveCluesWorth.do',
            data: $('form').serialize(),
            dataType: 'json',
            success: function (data) {
                if (data.code == 0) {
                    parent.layer.close(index);
                    window.parent.location.reload();
                } else {
                    layer.alert(data.message);
                }
            },
            error: function (data) {
                if (data.status == 1001) {
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    });
</script>