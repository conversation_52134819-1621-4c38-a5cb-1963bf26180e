package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.KingDeeOutPutFeePlainInvoiceEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface KingDeeOutPutFeePlainInvoiceMapper {
    /**
     * delete by primary key
     * @param plainInvoicId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer plainInvoicId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(KingDeeOutPutFeePlainInvoiceEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(KingDeeOutPutFeePlainInvoiceEntity record);

    /**
     * select by primary key
     * @param plainInvoicId primary key
     * @return object by primary key
     */
    KingDeeOutPutFeePlainInvoiceEntity selectByPrimaryKey(Integer plainInvoicId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(KingDeeOutPutFeePlainInvoiceEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(KingDeeOutPutFeePlainInvoiceEntity record);

    int updateBatchSelective(List<KingDeeOutPutFeePlainInvoiceEntity> list);

    int batchInsert(@Param("list") List<KingDeeOutPutFeePlainInvoiceEntity> list);
}