package com.vedeng.order.model.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @Title: ${file_name}
 * @Package ${package_name}
 * @Description: ${todo}
 * @date 2021/9/316:54
 */
@Data
public class SmsSaleorderDTO {

    /**
     * 客户名称
     */
    private String traderName;

    /**
     * 下单人名称
     */
    private String traderContactName;

    /**
     * 下单人手机号
     */
    private String traderContactMobile;

    /**
     * 收货人名称
     */
    private String takeTraderName;

    /**
     * 收货人手机号
     */
    private String takeTraderContactMobile;

    /**
     * 订单号
     */
    private String saleorderNo;

    /**
     * 物流公司名称
     */
    private String expressName;

    /**
     * 物流信息Id
     */
    private Integer expressId;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 销售电话
     */
    private String saleMobile;

    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 归属销售Id
     */
    private Integer saleId;

}
