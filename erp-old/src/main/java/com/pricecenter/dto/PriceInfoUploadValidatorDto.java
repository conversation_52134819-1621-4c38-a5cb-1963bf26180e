package com.pricecenter.dto;

import com.vedeng.authorization.model.User;
import org.apache.poi.ss.usermodel.Row;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PriceInfoUploadValidatorDto {

    private List<Row> rows;

    private User user;

    private Map<String,Long> traderNameAndIdMap = new HashMap<>();

    private Map<String,Integer> traderNameAndPlatFormMap = new HashMap<>();

    private Map<String,String> skuNoAndSkuNameMap = new HashMap<>();

    public List<Row> getRows() {
        return rows;
    }

    public void setRows(List<Row> rows) {
        this.rows = rows;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Map<String, Long> getTraderNameAndIdMap() {
        return traderNameAndIdMap;
    }

    public void setTraderNameAndIdMap(Map<String, Long> traderNameAndIdMap) {
        this.traderNameAndIdMap = traderNameAndIdMap;
    }

    public Map<String, Integer> getTraderNameAndPlatFormMap() {
        return traderNameAndPlatFormMap;
    }

    public void setTraderNameAndPlatFormMap(Map<String, Integer> traderNameAndPlatFormMap) {
        this.traderNameAndPlatFormMap = traderNameAndPlatFormMap;
    }

    public Map<String, String> getSkuNoAndSkuNameMap() {
        return skuNoAndSkuNameMap;
    }

    public void setSkuNoAndSkuNameMap(Map<String, String> skuNoAndSkuNameMap) {
        this.skuNoAndSkuNameMap = skuNoAndSkuNameMap;
    }
}
