package com.vedeng.goods.manager;

import com.vedeng.common.util.NumberUtil;
import com.vedeng.todolist.service.impl.MaintainDataSku;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Component
public class GoodsInfoTodoListener implements GoodsInfoChangeListener {
    @Resource
    private MaintainDataSku maintainDataSku;

    @Override
    public void onGoodsInfoChange(Integer goodsId) {
        if (NumberUtil.isPositive(goodsId)) {
            maintainDataSku.finish(goodsId);
        }
    }
}
