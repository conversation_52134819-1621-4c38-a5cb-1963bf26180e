package com.vedeng.erp.saleorder.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/11/8
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class SaleOrderGoodsMaoLiBuyPriceDto {

    /**
     * 销售订单ID
     */
    private Integer saleorderId;

    /**
     * SKU
     */
    private String skuNo;

    /**
     * 近期采购价-来自onedata
     */
    private BigDecimal buyPrice;

    private String buyPriceDesc;

    /**
     * 价格状态-0待查询 1成功 2失败
     */
    private Integer priceStatus;

    public SaleOrderGoodsMaoLiBuyPriceDto(Integer saleorderId, String skuNo) {
        this.saleorderId = saleorderId;
        this.skuNo = skuNo;
    }
}
