package com.vedeng.goods.service.api;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.vedeng.common.http.NewHttpClientUtils;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.goods.model.dto.OrgDTO;
import com.vedeng.invi.api.dto.OrgListDTO;
import com.vedeng.price.dto.SkuPriceInfoDetailResponseDto;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class VgoodsApiServiceImpl implements VgoodsApiService {

    @Value("${invi_url}")
    private String inviUrl;

    @Value("${price.url}")
    private String priceUrl;

    private static final String SUCCESS_SIGN = "success";

    private static final String FIND_SKUPRICEINFO_BYSKUNO = "sku_price_info/findSkuPriceInfoBySkuNo";

    /**
     * 成功code
     */
    private static final int SUCCESS_CODE = 200;

    /**
     * 查询前台区域商城接口
     */
    private static final String QUERY_ORG = "/org/queryOrg";

    @Override
    public List<OrgDTO> getOrgDTOList() {
        List<OrgDTO> orgDTOList = new ArrayList<>();
        try {
            ResultInfo<?> orgResult = NewHttpClientUtils.doPost(inviUrl + QUERY_ORG);
            if (orgResult.getCode() == SUCCESS_CODE) {
                //1、使用JSONObject
                OrgListDTO orgListDTO = JSON.parseObject((String) orgResult.getData(), OrgListDTO.class);
                orgListDTO.getOrgList().forEach(item -> {
                    OrgDTO orgDTO = new OrgDTO();
                    orgDTO.setOrgId(Integer.parseInt(item.getOrgId()));
                    orgDTO.setOrgName(item.getOrgName());
                    orgDTOList.add(orgDTO);
                });
            } else {
                log.error("区域商城前台接口异常{}", orgResult);
            }
        } catch (Exception e) {
            log.error("区域商城前台接口异常", e);
        }
        return orgDTOList;
    }


    @Override
    public SkuPriceInfoDetailResponseDto findSkuPriceInfoBySkuNo(String skuNo) {
        SkuPriceInfoDetailResponseDto skuPriceInfoDetailResponseDto = null;
        try {
            //封装请求参数
            Map<String, Object> requestMap = new HashMap<>(2);
            requestMap.put("skuNo", skuNo);
            String requestJson = JsonUtils.translateToJson(requestMap);
            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + FIND_SKUPRICEINFO_BYSKUNO, requestJson);
            if (null == resultJsonObj || !SUCCESS_SIGN.equals(resultJsonObj.get("code")) || null == resultJsonObj.getJSONObject("data")) {
                return null;
            }
            Gson gson = new Gson();
            skuPriceInfoDetailResponseDto = gson.fromJson(resultJsonObj.getJSONObject("data").toString(),
                    new TypeToken<SkuPriceInfoDetailResponseDto>() {
                    }.getType());
        } catch (Exception e) {
            log.error("获取skuNo核价基本信息失败:skuNo{}", skuNo, e);
        }
        return skuPriceInfoDetailResponseDto;
    }


}
