package com.vedeng.erp.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.erp.system.domain.entity.FlowOrderEntity;
import com.vedeng.erp.system.mapper.FlowOrderMapper;
import com.vedeng.erp.system.service.FlowOrderApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class FlowOrderApiServiceImpl implements FlowOrderApiService {
    
    @Autowired
    private FlowOrderMapper flowOrderMapper;
    
    @Override
    public Integer queryAuditStatus(String orderNo) {
        List<FlowOrderEntity> flowOrderEntities = flowOrderMapper.findbyBaseOrderNo(orderNo);
        if (CollUtil.isEmpty(flowOrderEntities)){
            return null;
        }
        FlowOrderEntity flowOrderEntity = flowOrderEntities.get(0);
        return flowOrderEntity.getAuditStatus();
    }
}
