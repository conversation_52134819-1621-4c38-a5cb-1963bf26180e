package com.vedeng.erp.aftersale.service.impl;

import com.vedeng.orderstream.aftersales.dao.AfterSalesRevenueRecordMapper;
import com.vedeng.orderstream.aftersales.model.AfterSalesRevenueRecord;
import com.vedeng.erp.aftersale.service.AfterSalesRevenueRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> simgo.wang
 * @date : 2021/10/13 20:29
 * @desc :
 */
@Service
public class AfterSalesRevenueRecordServiceImpl implements AfterSalesRevenueRecordService {

    @Qualifier("afterSalesRevenueRecordMapper")
    @Autowired
    private AfterSalesRevenueRecordMapper afterSalesRevenueRecordMapper;

    @Override
    public int insert(AfterSalesRevenueRecord afterSalesRevenueRecord) {

        return afterSalesRevenueRecordMapper.insert(afterSalesRevenueRecord);

    }

    @Override
    public List<AfterSalesRevenueRecord> selectByAfterSalesId(Integer afterSalesId) {

        return afterSalesRevenueRecordMapper.selectByAfterSalesId(afterSalesId);

    }
}
