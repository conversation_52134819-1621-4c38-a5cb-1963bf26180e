package com.vedeng.authorization.dao;

import com.vedeng.authorization.model.Region;
import com.vedeng.authorization.model.vo.RegionVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <b>Description:</b><br> 地区Mapper
 * <AUTHOR>
 * @Note
 * <b>ProjectName:</b> erp
 * <br><b>PackageName:</b> com.vedeng.authorization.dao
 * <br><b>ClassName:</b> RegionMapper
 * <br><b>Date:</b> 2017年4月25日 上午9:55:22
 */
public interface RegionMapper {
	
	/**
	 * <b>Description:</b><br> 根据父级地区ID查询子集地区
	 * @param parentId 父级地区ID
	 * @return List<Region>
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年4月25日 上午9:55:44
	 */
	List<Region> getRegionByParentId(Integer parentId);
	
	/**
	 * <b>Description:</b><br> 查询城市
	 * @param parentId 父级地区ID
	 * @return List<Region>
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年4月25日 上午9:55:44
	 */
	List<Region> getRegionByParam(Region region);
	
	/**
	 * <b>Description:</b><br> 查询地区 
	 * @param regionId 地区ID
	 * @return Region
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年4月25日 上午9:56:33
	 */
	Region getRegionById(Integer regionId);

	/**
	 * <b>Description:</b><br> 获取地区信息(名称+上级ID)
	 * @param region
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年11月28日 下午2:24:26
	 */
	Region getRegion(Region region);
	
	/**
	 * <b>Description:</b><br> 查询分页
	 * @param map
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2018年3月16日 上午11:03:04
	 */
	List<Region> getRegionListPage(Map<String, Object> map);
	
	/**
	 * <b>Description:</b><br> 新增
	 * @param region
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2018年3月16日 下午1:52:50
	 */
	int insert(Region region);
	
	/**
	 * <b>Description:</b><br> 修改
	 * @param region
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2018年3月16日 下午1:53:31
	 */
	int update(Region region);

	/**
	 * <b>Description:</b><br> 根据区域获取地区信息
	 * @param regionVo
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2018年8月20日 上午10:50:38
	 */
	RegionVo getRegionByArea(RegionVo regionVo);
	
	/**
	 * 
	 * <b>Description: 根据当前地区ID查询省市区的ID以,分割</b><br> 
	 * @param minRegionId
	 * @return
	 * <b>Author: Franlin.wu</b>  
	 * <br><b>Date: 2018年11月28日 下午6:21:14 </b>
	 */
	String getRegionIdStringByMinRegionId(@Param("minRegionId")Integer minRegionId);


	/**
	 * 根据当前地区ID查询省市区名称以空格分割
	 *
	 * @param minRegionId
	 * @return
	 */
	String getRegionNameStringByMinRegionId(@Param("minRegionId")Integer minRegionId);
	
	/**
	 * <b>Description:</b><br>
	 * 获取所有的城市或者县级市或者区
	 * @param :a
	 *@return :a
	 *@Note <b>Author:</b> Bert <br>
	 * <b>Date:</b> 2019/1/21 10:08
	 */
	List<RegionVo> getCityList(RegionVo regionVo);

	/**
	 * @description: 获取地址全称
	 * @return: Region
	 * @author: Strange
	 * @date: 2020/8/31
	 **/
	Region getRegionFullNameById(@Param("areaId") Integer areaId);

	/**
	 * 根据地区名称和等级检索地区信息
	 *
	 * @param regionName
	 * @param regionType
	 * @return
	 */
	Region getRegionByRegionNameAndType(@Param("regionName") String regionName, @Param("regionType") Integer regionType);

	List<String> getRegionIdStringByMinRegionName(@Param("regionName")String regionName);

	Region getRegionByReginFullName(@Param("regionName") String regionName,@Param("regionType") Integer regionType,@Param("parentId") Integer parentId);

	List<Region> getRegionCityAll();
	/**
	 * VDERP-7929 根据省、市的NAME查询市的id、type（防止同名的行政区，因此需要省市一起校验）
	 */
	Region getRegionByNameParentName(@Param("name") String name, @Param("parentName") String parentName);

    List<Integer> selectTypeEqThreeRegionIdListByRegionIdList(@Param("regionIdList") List<Integer> regionIdListByUserIdList);
}