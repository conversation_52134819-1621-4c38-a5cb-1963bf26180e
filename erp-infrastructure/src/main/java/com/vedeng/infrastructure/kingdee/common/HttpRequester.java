package com.vedeng.infrastructure.kingdee.common;


import com.alibaba.fastjson.JSON;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.ConstDefine;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.Cookie;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.RequestBodyObject;
import com.vedeng.infrastructure.kingdee.common.sdk.utils.HttpUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.Proxy;
import java.net.URL;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Slf4j
public class HttpRequester {
    int connectTimeout;
    RequestBodyObject reqJson;
    int requestTimeout;
    String url;
    Map<String, String> header;
    int statusCode;
    Set<Cookie> repoCookies;

    public HttpRequester(String url, Map<String, String> header, RequestBodyObject reqJson) {
        this.connectTimeout = 120;
        this.requestTimeout = 120;
        this.url = url;
        this.header = header;
        this.reqJson = reqJson;
    }

    public HttpRequester(String url, Map<String, String> header, RequestBodyObject reqJson, int connectTimeout, int requestTimeout) {
        this.connectTimeout = 120;
        this.requestTimeout = 120;
        this.url = url;
        this.header = header;
        this.reqJson = reqJson;
        this.connectTimeout = connectTimeout;
        this.requestTimeout = requestTimeout;
    }

    public String post() throws Exception {
        HttpURLConnection conn;
        PrintWriter out = null;
        URL realUrl = new URL(this.url);
        Proxy proxy = HttpUtils.getProxy();
        if (proxy != null) {
            conn = (HttpURLConnection) realUrl.openConnection(proxy);
        } else {
            conn = (HttpURLConnection) realUrl.openConnection();
        }
        conn.setConnectTimeout(getConnectTimeout() * 1000);
        conn.setReadTimeout(getRequestTimeout() * 1000);
        if (this.header != null) {
            for (Map.Entry<String, String> entry : this.header.entrySet()) {
                conn.setRequestProperty(entry.getKey(), entry.getValue());
            }
        }
        try {
            conn.setRequestProperty("Content-type", "applicatin/json");
            conn.setRequestProperty("User-Agent", "Kingdee/Java WebApi SDK 7.3 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            conn.setDoOutput(true);
            conn.setDoInput(true);
            try {
                if (this.reqJson != null) {
                    out = new PrintWriter(new OutputStreamWriter(conn.getOutputStream(), "utf8"));
                    out.print(this.reqJson.toJson());
                    out.flush();
                }
                int statusCode = conn.getResponseCode();
                if (statusCode == 200 || statusCode == 206) {
                    for (Map.Entry<String, List<String>> cookie : conn.getHeaderFields().entrySet()) {
                        if (cookie.getKey() != null) {
                            if (cookie.getKey().equals(ConstDefine.Cookie_Set)) {
                                this.repoCookies = new HashSet();
                                for (String c : cookie.getValue()) {
                                    if (Cookie.parse(c) != null) {
                                        this.repoCookies.add(new Cookie(c));
                                    }
                                }
                            }
                        }
                    }
                    return readRepoBody(conn);
                }
                throw new Exception(String.format("StatusCode:%s,\tDesc:%s", statusCode, readRepoBody(conn)));
            } catch (Exception er) {
                throw er;
            }
        } finally {
            if (0 != 0) {
                out.close();
            }
        }
    }

    String readRepoBody(HttpURLConnection conn) throws UnsupportedEncodingException, IOException {
        BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream(), "utf8"));
        StringBuilder sb = new StringBuilder();
        while (true) {
            String line = in.readLine();
            if (line != null) {
                sb.append(line);
            } else {
                in.close();
                return sb.toString();
            }
        }
    }

    public int getConnectTimeout() {
        return this.connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getRequestTimeout() {
        return this.requestTimeout;
    }

    public void setRequestTimeout(int requestTimeout) {
        this.requestTimeout = requestTimeout;
    }

    public String getUrl() {
        return this.url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Map<String, String> getHeader() {
        return this.header;
    }

    public void setHeader(Map<String, String> header) {
        this.header = header;
    }

    public RequestBodyObject getReqJson() {
        return this.reqJson;
    }

    public void setReqJson(RequestBodyObject reqJson) {
        this.reqJson = reqJson;
    }

    public int getStatusCode() {
        return this.statusCode;
    }

    public void setStatusCode(int statusCode) {
        this.statusCode = statusCode;
    }

    public Set<Cookie> getRepoCookies() {
        return this.repoCookies;
    }

    public void setRepoCookies(Set<Cookie> repoCookies) {
        this.repoCookies = repoCookies;
    }
}
