package com.vedeng.finance.controller;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.redis.redission.RedissonLockUtils;
import com.vedeng.common.redis.utils.RedisUtil;
import com.vedeng.finance.service.InvoiceApplyAuditService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/15 16:09
 **/
@Controller
@RequestMapping("/finance/invoiceApplyAutoAudit")
@Slf4j
public class InvoiceApplyAutoAuditController {



    private static final int CORE_POOL_SIZE = 1;
    private static final int MAX_POOL_SIZE = CORE_POOL_SIZE * 2;
    private static final long KEEP_ALIVE_TIME = 60L;

    private final ThreadPoolExecutor executor = new ThreadPoolExecutor(CORE_POOL_SIZE,
            MAX_POOL_SIZE, KEEP_ALIVE_TIME, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(MAX_POOL_SIZE * 100, true),
            new ThreadFactoryBuilder().setNameFormat("invoiceApplyAutoAudit-pool-%d").build(),
            new ThreadPoolExecutor.AbortPolicy());


    @Autowired
    private InvoiceApplyAuditService invoiceApplyAuditService;


    @ResponseBody
    @RequestMapping(value = "/advance")
    @NoNeedAccessAuthorization
    public ResultInfo<?> advanceAudit() throws InterruptedException {

        boolean hasKey = RedisUtil.KeyOps.hasKey(ErpConstant.REVIEW_OF_ADVANCE_INVOICING_TASK);
        if (hasKey) {
            log.info("加锁失败，手动触发提前开票审核,存在处理中的提前开票审核业务");
            return ResultInfo.error("存在处理中的提前开票审核业务,请稍后再试");
        }

        log.info("提前开票审核加锁成功, key = [{}]", ErpConstant.REVIEW_OF_ADVANCE_INVOICING_TASK);

        executor.submit(() -> {

            try {
                boolean lock = RedissonLockUtils.tryLock(ErpConstant.REVIEW_OF_ADVANCE_INVOICING_TASK,0,2, TimeUnit.HOURS);

                if (!lock) {
                    log.info("加锁失败，手动触发提前开票审核,存在处理中的提前开票审核业务");
                    return;
                }
                invoiceApplyAuditService.doAdvancePageAudit( true);
            } catch (Exception e) {
                log.error("触发提前开票审核,异常",e);
            }finally {
                // 解锁手动触发
                RedissonLockUtils.unlock(ErpConstant.REVIEW_OF_ADVANCE_INVOICING_TASK);
                log.info("自动提前开票审核解锁成功, key = [{}]", ErpConstant.REVIEW_OF_ADVANCE_INVOICING_TASK);
            }

        });

        return ResultInfo.success();
    }


    @ResponseBody
    @RequestMapping(value = "/advanceLoading")
    @NoNeedAccessAuthorization
    public ResultInfo advanceLoading() {

        long total = RedisUtil.StringOps.incrBy(ErpConstant.REVIEW_OF_ADVANCE_INVOICING_TOTAL, 0);
        long done = RedisUtil.StringOps.incrBy(ErpConstant.REVIEW_OF_ADVANCE_INVOICING_LOAD, 0);
        if (total == 0) {
            return ResultInfo.success(100);
        }
        if (total == done) {
            return ResultInfo.success(100);
        }
        long result = done * 100 / total;
        return ResultInfo.success(result);
    }

}
