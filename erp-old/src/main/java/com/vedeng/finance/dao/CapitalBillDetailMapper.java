package com.vedeng.finance.dao;

import com.vedeng.finance.model.CapitalBill;
import com.vedeng.finance.model.CapitalBillDetail;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;
import java.util.List;

@Named("capitalBillDetailMapper")
public interface CapitalBillDetailMapper {
    int deleteByPrimaryKey(Integer capitalBillDetailId);

    int insert(CapitalBillDetail record);

    int insertSelective(CapitalBillDetail record);

    CapitalBillDetail selectByPrimaryKey(Integer capitalBillDetailId);

    int updateByPrimaryKeySelective(CapitalBillDetail record);

    int updateByPrimaryKey(CapitalBillDetail record);
    /**
     * 
     * <b>Description:</b><br> 根据银行流水ID查询资金流水详情（销售单信息）
     * @param bankBillId
     * @return
     * @Note
     * <b>Author:</b> selectByBankBillId
     * <br><b>Date:</b> 2017年9月14日 上午10:30:06
     */
    List<CapitalBillDetail> selectByBankBillId(Integer bankBillId);
    
    /**
     * 根据银行流水ID和订单号查询资金流水详情（销售单信息）
     * <b>Description:</b><br> 
     * @param bankBillId
     * @param orderNo
     * @return
     * @Note
     * <b>Author:</b> Bill
     * <br><b>Date:</b> 2018年8月29日 上午11:20:19
     */
    List<CapitalBillDetail> selectByBankBillIdAndNo(@Param("bankBillId") Integer bankBillId, @Param("orderNo") String orderNo);
    
    /**
     * 
     * <b>Description:</b><br> 根据银行流水ID查询资金流水详情（采购单信息）
     * @param bankBillId
     * @return
     * @Note
     * <b>Author:</b> Michael
     * <br><b>Date:</b> 2018年7月2日 下午2:56:51
     */
    List<CapitalBillDetail> selectBuyorderByBankBillId(Integer bankBillId);
    
    /**
     * 根据银行流水ID和订单号查询资金流水详情（采购单信息）
     * <b>Description:</b><br> 
     * @param bankBillId
     * @param orderNo
     * @return
     * @Note
     * <b>Author:</b> Bill
     * <br><b>Date:</b> 2018年8月29日 上午11:25:09
     */
    List<CapitalBillDetail> selectBuyorderByBankBillIdAndNo(@Param("bankBillId") Integer bankBillId, @Param("orderNo") String orderNo);
    
    /**
     * 
     * <b>Description:</b><br>  根据银行流水ID查询资金流水详情（售后单信息）
     * @param bankBillId
     * @return
     * @Note
     * <b>Author:</b> Michael
     * <br><b>Date:</b> 2018年7月2日 下午2:57:53
     */
    List<CapitalBillDetail> selectAfterSaleByBankBillId(Integer bankBillId);
    
    /**
     * 根据银行流水ID和订单号查询资金流水详情（售后单信息）
     * <b>Description:</b><br> 
     * @param bankBillId
     * @return
     * @Note
     * <b>Author:</b> Bill
     * <br><b>Date:</b> 2018年8月29日 上午11:27:39
     */
    List<CapitalBillDetail> selectAfterSaleByBankBillIdAndNo(@Param("bankBillId") Integer bankBillId, @Param("orderNo") String orderNo);
    
    /**
     * 
     * <b>Description:</b><br> 根据银行流水ID查询资金流水详情
     * @param bankBillId
     * @return
     * @Note
     * <b>Author:</b> Michael
     * <br><b>Date:</b> 2018年7月2日 下午2:26:19
     */
    List<CapitalBillDetail> selectListByBankBillId(@Param("bankBillId") Integer bankBillId);

	/** 
	 * <b>Description:</b>获取流水归属人信息
	 * @param capitalBillDetail
	 * @return CapitalBillDetail
	 * @Note
	 * <b>Author：</b> lijie
	 * <b>Date:</b> 2018年11月5日 下午4:15:55
	 */
	CapitalBillDetail getCapitalUser(CapitalBillDetail capitalBillDetail);


    /**
     * 查询对应售后的退款记录
     *
     * @param capitalBill
     * @return
     */
    List<CapitalBill> getAfterReturnCapitalBillList(CapitalBill capitalBill);

    /**
     * 获取资金流水列表（不分页）
     *
     * @param capitalBill
     * @return
     */
    List<CapitalBill> getCapitalBillList(CapitalBill capitalBill);

    /**
     * 判断该财务流水是否是耗材订单的售后退款流水，如果是则返回订单号，否则返回null
     * @param capitalBillDetailId 流水详情id
     * @return  结果
     */
    CapitalBillDetail checkAfterCapitalBillAmountOfHcOrder(Integer capitalBillDetailId);

    List<CapitalBillDetail> getMatchAliPayInfo(String tranFlow);

    List<CapitalBillDetail> selectByCapitalBillId(Integer capitalBillId);
}