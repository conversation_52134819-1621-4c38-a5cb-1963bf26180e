package com.vedeng.finance.model;

import java.math.BigDecimal;

/**
 * 航信发票详情类
 * <AUTHOR>
 * @date created in 2020/5/18 14:57
 */
public class HxInvoiceDetail {

    private Integer hxInvoiceDetailId;

    private Integer hxInvoiceId;

    /**
     * 发票明细编号
     */
    private String number;

    /**
     * 货物名称
     */
    private String goodsName;

    /**
     * 规格型号
     */
    private String specification;

    /**
     * 单位
     */
    private String unit;

    /**
     * 数量
     */
    private Double quantity;

    private Integer taxRate;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 税额
     */
    private BigDecimal taxAmount;

    public Integer getHxInvoiceDetailId() {
        return hxInvoiceDetailId;
    }

    public void setHxInvoiceDetailId(Integer hxInvoiceDetailId) {
        this.hxInvoiceDetailId = hxInvoiceDetailId;
    }

    public Integer getHxInvoiceId() {
        return hxInvoiceId;
    }

    public void setHxInvoiceId(Integer hxInvoiceId) {
        this.hxInvoiceId = hxInvoiceId;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getSpecification() {
        return specification;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

    public Double getQuantity() {
        return quantity;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Integer getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(Integer taxRate) {
        this.taxRate = taxRate;
    }

    @Override
    public String toString() {
        return "HxInvoiceDetail{" +
                "hxInvoiceDetailId=" + hxInvoiceDetailId +
                ", hxInvoiceId=" + hxInvoiceId +
                ", number=" + number +
                ", goodsName='" + goodsName + '\'' +
                ", specification='" + specification + '\'' +
                ", unit='" + unit + '\'' +
                ", quantity=" + quantity +
                ", taxRate=" + taxRate +
                ", price=" + price +
                ", amount=" + amount +
                ", taxAmount=" + taxAmount +
                '}';
    }
}
