package com.vedeng.erp.saleorder.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class SaleOrderGoodsDetailDto {
    /**
     * 销售订单ID
     */
    private Integer saleorderId;

    /**
     * 销售详情ID
     */
    private Integer saleorderGoodsId;
    /**
     * 订单号
     */
    private String saleorderNo;

    /**
     * 订单归属人
     */
    private String applicantName;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 采购数量
     */
    private Integer buyNum;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 货期
     */
    private Integer deliveryCycle;

    /**
     * 商品id
     */
    private Integer goodsId;

    /**
     * 商品sku
     */
    private String skuNo;

    /**
     * 品牌
     */
    private String brandName;

    /**
     * 内部备注
     */
    private String insideComments;

    /**
     * 产品备注
     */
    private String goodsComments;

    /**
     * 归属人ID
     */
    private Integer userId;

    /**
     * 终端名称
     */
    private String terminalTraderName;

    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 发货数量
     */
    private Integer deliveryNum;

    /**
     * 发货状态
     */
    private Integer deliveryStatus;

    /**
     * 收货状态
     */
    private Integer arrivalStatus;

    /**
     * 虚拟商品是否有库存管理
     */
    private Integer haveStockManage;

    /**
     * 虚拟商品费用类别
     */
    private String categoryName;

    /**
     * sku
     */
    private String sku;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 是否为虚拟商品
     */
    private Integer isVirtureSku;

    /**
     *是否启用
     */
    private Integer status;

    /**
     * 是否需要采购
     */
    private Integer isNeedPurchase;

    /**
     * 是否直发
     */
    private Integer deliveryDirect;

    /**
     * 发货时间
     */
    private Long deliveryTime;

    /**
     * 收货时间
     */
    private Long arrivalTime;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * 规格
     */
    private String spec;

    private String model;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 税收分类编码
     */
    private String taxCategoryNo;

    /**
     * spu类型
     */
    private Integer spuType;

    private String afterSaleServiceLabels;

    /**
     * 即当前订单商品实际金额(含退货)
     */
    private BigDecimal maxSkuRefundAmount;
}
