package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.annotation.BusinessID;
import com.vedeng.infrastructure.kingdee.annotation.KingDeeID;
import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 盘盈单 dto  https://www.yuque.com/manhuo/gf1570/vpqnld
 *
 * <AUTHOR>
 * @version 1.0
 * @description: 预处理金蝶采购盘盈单dto  由erp实际业务转换
 * @date
 */
@Getter
@Setter
@WriteBackField(needBackField = {"FID"})
public class KingDeeInventoryProfitDto extends KingDeeMqBaseDto {

    /**
     * id
     */
    private Integer id;

    /**
     * 单据内码
     */
    @KingDeeID
    @WriteBackField
    private String fId;
    /**
     * 单据类型
     */
    private String fBillTypeId;
    /**
     * 单据编号
     */
    @BusinessID
    private String fBillNo;
    /**
     * 贝登单据头ID
     */
    private String fQzokBddjtId;
    /**
     * 库存组织
     */
    private String fStockOrgId;

    /**
     * 库存方向
     */
    private String fStockDirect;

    /**
     * 货主类型
     */
    private String fOwnerTypeIdHead;
    /**
     * 单据日期
     */
    private String fDate;
    /**
     * 部门
     */
    private String fDeptId;

    /**
     * 客户
     */
    private String fCustId;

    /**
     * fBillEntry
     */
    private List<KingDeeInventoryProfitDetailDto> fBillEntry;

    public KingDeeInventoryProfitDto() {
        this.fBillTypeId = "PY01_SYS";
        this.fStockOrgId = "101";
        this.fOwnerTypeIdHead = "BD_OwnerOrg";
        this.fDeptId = "BM9999";
        this.fStockDirect = "GENERAL";
    }

    @Override
    public String getFormId() {
        return KingDeeFormConstant.STK_STOCK_COUNT_GAIN;
    }
}
