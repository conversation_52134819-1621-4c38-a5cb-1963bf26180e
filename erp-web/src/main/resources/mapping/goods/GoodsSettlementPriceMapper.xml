<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.goods.dao.GoodsSettlementPriceMapper">
	<resultMap id="BaseResultMap" type="com.vedeng.goods.model.GoodsSettlementPrice">
		<id column="GOODS_SETTLEMENT_PRICE_ID" property="goodsSettlementPriceId"
			jdbcType="INTEGER" />
		<result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
		<result column="GOODS_ID" property="goodsId" jdbcType="INTEGER" />
		<result column="SETTLEMENT_PRICE" property="settlementPrice"
			jdbcType="DECIMAL" />
		<result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
		<result column="CREATOR" property="creator" jdbcType="INTEGER" />
		<result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
		<result column="UPDATER" property="updater" jdbcType="INTEGER" />
	</resultMap>
	<sql id="Base_Column_List">
		GOODS_SETTLEMENT_PRICE_ID, COMPANY_ID, GOODS_ID, SETTLEMENT_PRICE, ADD_TIME, CREATOR,
		MOD_TIME, UPDATER
	</sql>
	<select id="selectByPrimaryKey" resultMap="BaseResultMap"
		parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from T_GOODS_SETTLEMENT_PRICE
		where GOODS_SETTLEMENT_PRICE_ID =
		#{goodsSettlementPriceId,jdbcType=INTEGER}
	</select>
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		delete from T_GOODS_SETTLEMENT_PRICE
		where GOODS_SETTLEMENT_PRICE_ID =
		#{goodsSettlementPriceId,jdbcType=INTEGER}
	</delete>
	<insert id="insert" parameterType="com.vedeng.goods.model.GoodsSettlementPrice">
		insert into T_GOODS_SETTLEMENT_PRICE (GOODS_SETTLEMENT_PRICE_ID,
		COMPANY_ID,
		GOODS_ID, SETTLEMENT_PRICE, ADD_TIME,
		CREATOR, MOD_TIME, UPDATER
		)
		values (#{goodsSettlementPriceId,jdbcType=INTEGER},
		#{companyId,jdbcType=INTEGER},
		#{goodsId,jdbcType=INTEGER}, #{settlementPrice,jdbcType=DECIMAL}, #{addTime,jdbcType=BIGINT},
		#{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT},
		#{updater,jdbcType=INTEGER}
		)
	</insert>
	<insert id="insertSelective" parameterType="com.vedeng.goods.model.GoodsSettlementPrice">
		insert into T_GOODS_SETTLEMENT_PRICE
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="goodsSettlementPriceId != null">
				GOODS_SETTLEMENT_PRICE_ID,
			</if>
			<if test="companyId != null">
				COMPANY_ID,
			</if>
			<if test="goodsId != null">
				GOODS_ID,
			</if>
			<if test="settlementPrice != null">
				SETTLEMENT_PRICE,
			</if>
			<if test="addTime != null">
				ADD_TIME,
			</if>
			<if test="creator != null">
				CREATOR,
			</if>
			<if test="modTime != null">
				MOD_TIME,
			</if>
			<if test="updater != null">
				UPDATER,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="goodsSettlementPriceId != null">
				#{goodsSettlementPriceId,jdbcType=INTEGER},
			</if>
			<if test="companyId != null">
				#{companyId,jdbcType=INTEGER},
			</if>
			<if test="goodsId != null">
				#{goodsId,jdbcType=INTEGER},
			</if>
			<if test="settlementPrice != null">
				#{settlementPrice,jdbcType=DECIMAL},
			</if>
			<if test="addTime != null">
				#{addTime,jdbcType=BIGINT},
			</if>
			<if test="creator != null">
				#{creator,jdbcType=INTEGER},
			</if>
			<if test="modTime != null">
				#{modTime,jdbcType=BIGINT},
			</if>
			<if test="updater != null">
				#{updater,jdbcType=INTEGER},
			</if>
		</trim>
	</insert>
	<update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.model.GoodsSettlementPrice">
		update T_GOODS_SETTLEMENT_PRICE
		<set>
			<if test="companyId != null">
				COMPANY_ID = #{companyId,jdbcType=INTEGER},
			</if>
			<if test="goodsId != null">
				GOODS_ID = #{goodsId,jdbcType=INTEGER},
			</if>
			<if test="settlementPrice != null">
				SETTLEMENT_PRICE = #{settlementPrice,jdbcType=DECIMAL},
			</if>
			<if test="addTime != null">
				ADD_TIME = #{addTime,jdbcType=BIGINT},
			</if>
			<if test="creator != null">
				CREATOR = #{creator,jdbcType=INTEGER},
			</if>
			<if test="modTime != null">
				MOD_TIME = #{modTime,jdbcType=BIGINT},
			</if>
			<if test="updater != null">
				UPDATER = #{updater,jdbcType=INTEGER},
			</if>
		</set>
		where GOODS_SETTLEMENT_PRICE_ID =
		#{goodsSettlementPriceId,jdbcType=INTEGER}
	</update>
	<update id="updateByPrimaryKey" parameterType="com.vedeng.goods.model.GoodsSettlementPrice">
		update T_GOODS_SETTLEMENT_PRICE
		set COMPANY_ID = #{companyId,jdbcType=INTEGER},
		GOODS_ID = #{goodsId,jdbcType=INTEGER},
		SETTLEMENT_PRICE = #{settlementPrice,jdbcType=DECIMAL},
		ADD_TIME = #{addTime,jdbcType=BIGINT},
		CREATOR = #{creator,jdbcType=INTEGER},
		MOD_TIME = #{modTime,jdbcType=BIGINT},
		UPDATER = #{updater,jdbcType=INTEGER}
		where GOODS_SETTLEMENT_PRICE_ID =
		#{goodsSettlementPriceId,jdbcType=INTEGER}
	</update>
	
	<update id="batchGoodsSettelmentSave" parameterType="java.util.List">
		<foreach collection="goodsList" item="list" separator=";">
			DELETE FROM T_GOODS_SETTLEMENT_PRICE WHERE GOODS_ID = #{list.goodsId,jdbcType=INTEGER} AND COMPANY_ID = #{list.companyId,jdbcType=INTEGER};
			INSERT INTO T_GOODS_SETTLEMENT_PRICE (
			  COMPANY_ID
			  ,GOODS_ID
			  ,SETTLEMENT_PRICE
			  ,ADD_TIME
			  ,CREATOR
			  ,MOD_TIME
			  ,UPDATER
			) VALUES (
			  #{list.companyId,jdbcType=INTEGER}
			  ,#{list.goodsId,jdbcType=INTEGER}
			  ,#{list.settlementPrice,jdbcType=DECIMAL}
			  ,#{list.addTime,jdbcType=BIGINT}
			  ,#{list.creator,jdbcType=INTEGER}
			  ,#{list.modTime,jdbcType=BIGINT}
			  ,#{list.updater,jdbcType=INTEGER}
			)
		</foreach>
	</update>
	
	<select id="getSaleorderGoodsSettlementPrice" parameterType="java.lang.Integer" resultType="java.math.BigDecimal">
		select
			SETTLEMENT_PRICE
		from T_GOODS_SETTLEMENT_PRICE
		where GOODS_ID = #{goodsId,jdbcType=INTEGER}
		and COMPANY_ID = #{companyId,jdbcType=INTEGER}
	</select>
	
	<select id="getGoodsSettlePriceByGoodsList" parameterType="java.lang.Integer" resultType="com.vedeng.goods.model.GoodsSettlementPrice">
		SELECT *
		FROM T_GOODS_SETTLEMENT_PRICE A
		WHERE A.COMPANY_ID = 1 
			AND A.GOODS_ID IN 
			<foreach collection="quoteGoodsList" item="goodsList" open="(" close=")" separator=",">
				#{goodsList.goods.goodsId,jdbcType=INTEGER}
			</foreach>
	</select>
	
	<select id="getGoodsSettlePriceBySaleorderGoodsList" parameterType="java.lang.Integer" resultType="com.vedeng.goods.model.GoodsSettlementPrice">
		SELECT *
		FROM T_GOODS_SETTLEMENT_PRICE A
		WHERE A.COMPANY_ID = 1 
			AND A.GOODS_ID IN 
			<foreach collection="saleorderGoodsList" item="goodsList" open="(" close=")" separator=",">
				#{goodsList.goods.goodsId,jdbcType=INTEGER}
			</foreach>
	</select>
	
	<select id="getGoodsSettlePriceByGoods" parameterType="com.vedeng.goods.model.GoodsSettlementPrice" resultType="com.vedeng.goods.model.GoodsSettlementPrice">
		SELECT A.*, B.USERNAME
		FROM T_GOODS_SETTLEMENT_PRICE A 
		JOIN T_USER B ON A.CREATOR = B.USER_ID 
		<where>
			1=1 
			<if test="companyId != null and companyId != 0">
	   			and A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
	   		</if>
	   		<if test="goodsId != null and goodsId != 0">
	   			and A.GOODS_ID = #{goodsId,jdbcType=INTEGER}
	   		</if>
		</where>
	</select>
	
	<select id="getSaleorderGoodsSettlementPriceByGoodsIds" resultType="com.vedeng.goods.model.GoodsSettlementPrice">
		SELECT A.GOODS_ID,A.SETTLEMENT_PRICE
		FROM T_GOODS_SETTLEMENT_PRICE A
		WHERE 1=1 
			<if test="companyId != null and companyId != 0">
	   			and A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
	   		</if>
			AND A.GOODS_ID IN 
			<foreach collection="goodsIds" item="goodsId" open="(" close=")" separator=",">
				#{goodsId,jdbcType=INTEGER}
			</foreach>
	</select>
</mapper>