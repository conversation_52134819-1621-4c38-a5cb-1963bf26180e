<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>同步数据ERP列表</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="syncDataErp" datasource="erp-datasourcetarget" empty_show="无数据" fixednumber="0" firstcol="checkbox">
<div id="appendHead">
    <style>
        .rowButtons{
            min-width: 160px;
        }
        li{
            position: relative;
            float: left;
            padding: 10px 0;
            display: list-item;
        }
        li>a{ color: #333; border-right: 1px solid #ddd; padding: 0 10px;}
        ul{ overflow: hidden; list-style: none outside none;}
        .ezcall{ color: #01AAED; font-size: 16px !important; cursor: pointer;}
        .utext i{ font-size: 16px !important;}
    </style>
</div>

<div class="layui-fluid">
    <form class="layui-form" id="search">
        <div class="layui-inline">
            <label class="layui-form-label">业务单号</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" type="text" name="BUSINESS_NO"  alias="BUSINESS_NO" data=""  oper="like">
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label">业务类型</label>
            <div class="layui-input-inline">
                <select class="layui-input list-search-item" name="BUSINESS_TYPE" alias="BUSINESS_TYPE"
                        data='[
                                {"K":"BUYORDER_TO_SALEORDER","V":"采购单转销售单"},
                                {"K":"BUYORDERCONTACT_TO_SALECONTACT","V":"采购订单合同同步销售订单合同"},
                                {"K":"SALEORDER_LOGISTICS_TO_BUYORDER","V":"销售反向同步物流信息"},
                                {"K":"SALEORDER_AUTO_COMFIRM_ORDER","V":"自动单生成确认单"}
                            ]'
                        datatype="json" oper="IN" placeholder="全部">
                </select>
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label">目标ERP</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" type="text" name="TARGET_ERP"  alias="" data=""   oper="like">
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label">处理状态</label>
            <div class="layui-input-inline">
                <select class="layui-input list-search-item" name="PROCESS_STATUS" alias="PROCESS_STATUS"
                        data='[
                                {"K":"0","V":"待处理"},
                                {"K":"1","V":"处理中"},
                                {"K":"2","V":"处理成功"},
                                {"K":"3","V":"处理失败"}
                            ]'
                        datatype="json" oper="IN" placeholder="全部">
                </select>
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label">创建时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" name="CREATE_TIME" placeholder="" style="" alias="CREATE_TIME" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>
    </form>
    <hr class="layui-border-blue">
    <div class="btn-group   bd-highlight" id="tableButton">
        <button type="table" class="layui-btn" url="/ezadmin/form/form-modifySyncDataErp?ID=" opentype="SELF" windowname="新建">新建</button>
    </div>
    <table id="table" class="layui-table" style="width:100%">
        <thead>
        <tr id="column">
            <th type="rowbutton" id="rowbutton">
                <button type="single" url="/ezadmin/form/form-modifySyncDataErp?ID=${ID}" area="800px,500px"
                        windowname="编辑" name="playrecordBtn">编辑
                </button>
                <button type="table" url="/ezadmin/form/doDelete-modifySyncDataErp?ID=${ID}"
                        opentype="CONFIRM_AJAX" windowname="删除">删除
                </button>
            </th>
            <th name="ID" style="max-width:80px;width: 50px;">主键</th>
            <th name="BUSINESS_NO">业务单号</th>
            <th name="BUSINESS_TYPE">业务类型</th>
            <th name="TARGET_ERP">目标ERP简称</th>
            <th name="PROCESS_STATUS">处理状态</th>
            <th name="CREATE_TIME">创建时间</th>
            <th name="UPDATE_TIME">更新时间</th>
            <th name="CREATE_USER">创建人</th>
            <th name="UPDATE_USER">更新人</th>
            <th name="IS_DELETED">是否删除</th>
            <th name="REMARK">备注</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td colspan="100">
                express:
                <pre id="express" class="layui-code" orderby="" groupby="">
StringBuilder sql=new StringBuilder("
select ID,
       BUSINESS_NO,
       BUSINESS_TYPE,
       TARGET_ERP,
       PROCESS_STATUS,
       CREATE_TIME,
       UPDATE_TIME,
       CREATE_USER,
       UPDATE_USER,
       IS_DELETED,
       REMARK
from T_SYNC_DATA_ERP
where IS_DELETED = 0");
sql.append(" order by ID desc");
list= search(sql);
return list;
                </pre> count:
                <pre id="count" class="layui-code">

                </pre>
            </td>
        </tr>
        </tbody>
    </table>
</div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    })
</script>
</body>
</html> 