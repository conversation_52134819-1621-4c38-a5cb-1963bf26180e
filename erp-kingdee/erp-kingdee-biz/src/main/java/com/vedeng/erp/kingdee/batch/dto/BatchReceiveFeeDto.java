package com.vedeng.erp.kingdee.batch.dto;

import com.vedeng.erp.kingdee.dto.KingDeeReceiveFeeDto;
import com.vedeng.erp.kingdee.dto.OutPutFeePlainInvoiceDto;
import com.vedeng.erp.kingdee.dto.OutPutFeeSpecialInvoiceDto;
import lombok.*;

/**
 * <AUTHOR>
 * @create 2023−01-09 下午5:14
 * @description 应收单 发票 组合对象
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BatchReceiveFeeDto {
    /**
     * 金蝶应收单
     */
    private KingDeeReceiveFeeDto kingDeeReceiveFeeDto;

    /**
     * 销项费用增值税专用发票
     */
    private OutPutFeeSpecialInvoiceDto outPutFeeSpecialInvoiceDto;

    /**
     * 销项费用增值税普通发票
     */
    private OutPutFeePlainInvoiceDto outPutFeePlainInvoiceDto;
}
