package com.vedeng.erp.kingdee.domain.command;

import com.vedeng.erp.kingdee.dto.KingDeeStorageInDto;
import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * 其他入库   https://www.yuque.com/manhuo/gf1570/uilh7d
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date
 */
@NoArgsConstructor
@Data
@Getter
@Setter
public class KingDeeStorageInCommand {

    /**
     * 单据内码  0：表示新增
     * 非0：云星空系统单据FID值，表示修改
     */
    private String fId;

    /**
     * 单据编号 填写单据编号，若为空则调用系统的编码规则生成
     */
    private String fBillNo;
    /**
     * 贝登单据头ID 贝登单据头ID号（预留）
     */
    private String F_QZOK_BDDJTID;

    /**
     * 库存方向 如果是普通入库，默认值 ：GENERAL
     * 如果是退货，则默认值 ： RETURN
     */
    private String fStockDirect;
    /**
     * 单据日期 格式yyyy-MM-dd
     */
    private String fDate;

    /**
     * 明细
     */
    private List<FEntity> fEntity;

    //Fnumber
    /**
     * 单据类型 填单据类型编码，默认QTRKD01_SYS
     */
    private KingDeeNumberCommand fBillTypeId = new KingDeeNumberCommand();

    /**
     * 库存组织 填写组织编码
     */
    private KingDeeNumberCommand fStockOrgId = new KingDeeNumberCommand();

    /**
     * 供应商 填写供应商编码
     */
    private KingDeeNumberCommand fSupplierId = new KingDeeNumberCommand();

    /**
     * 客户编码
     */
    private KingDeeNumberCommand F_QZOK_KH = new KingDeeNumberCommand();

    /**
     * 部门 填写部门编码，默认值 ：BM9999
     */
    private KingDeeNumberCommand fDeptId = new KingDeeNumberCommand();


    /**
     * 明细
     */
    @NoArgsConstructor
    @Data
    public static class FEntity {

        private String FENTRYID;

        /**
         * 实收数量
         */
        private String fQty;
        /**
         * 成本单价 填写成本价（不含税）
         */
        private String fPrice;
        /**
         * 成本金额 填写成本（不含税）
         */
        private String fAmount;
        /**
         * 原始订单号
         */
        private String F_QZOK_YSDDH;
        /**
         * 归属业务单号
         */
        private String F_QZOK_GSYWDH;
        /**
         * 业务类型
         */
        private String F_QZOK_YWLX;
        /**
         * 批次号
         */
        private String F_QZOK_PCH;
        /**
         * 序列号
         */
        private String F_QZOK_XLH;
        /**
         * 授权类型
         */
        private String F_QZOK_SQLX;
        /**
         * 是否直发
         */
        private String F_QZOK_SFZF;

        /**
         * 贝登订单行id
         */
        private String F_QZOK_BDDJHID;

        /**
         * ftaxprice
         */
        private Integer FTAXPRICE;
        /**
         * fentrytaxrate
         */
        private BigDecimal FENTRYTAXRATE;

        //Fnumber
        /**
         * 填写物料编码 sku
         */
        private KingDeeNumberCommand fMaterialId = new KingDeeNumberCommand();
        /**
         * 填写仓库编码，默认值 ：CK9999
         */
        private KingDeeNumberCommand fStockId = new KingDeeNumberCommand();
    }

}
