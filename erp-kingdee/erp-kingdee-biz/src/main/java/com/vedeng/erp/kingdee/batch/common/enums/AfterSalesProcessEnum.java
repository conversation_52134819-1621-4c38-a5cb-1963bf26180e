package com.vedeng.erp.kingdee.batch.common.enums;

import java.util.Arrays;

/**
 * <b>Description:</b><br>
 * 订单流升级-售后单据类型
 *
 * <AUTHOR>
 * @Note <b>ProjectName:</b> erp <br>
 * <b>PackageName:</b> com.vedeng.orderstream.aftersales.constant <br>
 * <b>ClassName:</b> AfterSalesConstant <br>
 * <b>Date:</b> 2021/10/9 14:08 <br>
 *
 * 不要随便加枚举 或者要加考虑一下 DEFAULT_CONSTANT
 */
public enum AfterSalesProcessEnum {

    /**
     * 销售
     */
    SALES_ORDER("so", 535, "销售"),

    /**
     * 采购
     */
    BUY_ORDER("bo", 536, "采购"),

    /**
     * 第三方
     */
    THIRD_ORDER("to", 537, "第三方"),

    /**
     * 订单流升级-销售售后退货
     */

    AFTERSALES_TH("th", 539, "销售售后退货"),

    /**
     * 订单流升级-销售售后换货
     */
    AFTERSALES_HH("hh", 540, "销售售后换货"),

    /**
     * 订单流升级-销售售后安调
     */
    AFTERSALES_AT("at", 541, "销售售后安调"),

    /**
     * 订单流升级-销售安调（合同安调）
     */
    AFTERSALES_ATY("aty", 4090, "销售安调（合同安调）"),

    /**
     * 订单流升级-销售安调（附加服务）
     */
    AFTERSALES_ATN("atn", 4091, "销售安调（附加服务）"),


    /**
     * 订单流升级-销售售后维修
     */
    AFTERSALES_WX("wx", 584, "销售售后维修"),

    /**
     * 订单流升级-销售售后退票
     */
    AFTERSALES_TP("tp", 542, "销售售后退票"),

    /**
     * 订单流升级-销售售后退款
     */
    AFTERSALES_TK("tk", 543, "销售售后退款"),

    /**
     * 销售订单技术咨询
     */
    AFTERSALES_JZ("jz", 544, "销售订单技术咨询"),

    /**
     * 销售订单其他
     */
    OTHER("qt", 545, "销售订单其他"),

    /**
     * 第三方退款
     */
    THIRD_AMOUNT_RETURN("ttk", 551, "第三方退款"),

    /**
     * 第三方安调
     */
    AFTERASALES_THIRD_AT("tat", 550, "第三方安调"),

    /**
     * 第三方维修
     */
    AFTERASALES_THIRD_WX("tat", 585, "第三方维修"),

    /**
     * 第三方技术咨询
     */
    THIRD_CONSULTATION("dsfzx", 552, "第三方技术咨询"),

    /**
     * 第三方其他
     */
    THIRD_OTHER("dsfqt", 553, "第三方其他"),

    /**
     * 销售订单丢票
     */
    LOST_TICKET("qp", 1135, "销售订单丢票"),

    /**
     * 销售订单未履约赔付
     */
    NONPERFORMENCE_PAYMENT("pf",3321,"销售订单未履约赔付"),

    DELIVERY_COMPLAINT("",3322,"销售订单送货投诉"),

    INSTALL_COMPLAINT("",3323,"销售订单安装投诉"),

    MAINTAIN_COMPLAINT("",3324,"销售订单维修投诉"),
    /**
     * 默认枚举
     */
    DEFAULT_CONSTANT("default", 0, "默认");

    private String type;

    private Integer code;

    private String desc;

    AfterSalesProcessEnum(String type, int code, String desc) {
        this.type = type;
        this.code = code;
        this.desc = desc;
    }

    public static AfterSalesProcessEnum getInstance(String type) {
        for (AfterSalesProcessEnum v : values()) {
            if (v.type.equalsIgnoreCase(type)) {
                return v;
            }
        }
        return DEFAULT_CONSTANT;
    }

    public static AfterSalesProcessEnum getInstance(Integer code) {
        for (AfterSalesProcessEnum v : values()) {
            if (v.code.equals(code)) {
                return v;
            }
        }
        return DEFAULT_CONSTANT;
    }

    /**
     * 是否应该跳转老的售后类型(售后类型代码)
     *
     * @param code
     * @return
     */
    public static boolean isBelongNewProcess(Integer code) {
        return Arrays.asList(AFTERSALES_TH, AFTERSALES_HH, AFTERSALES_AT, AFTERSALES_WX, AFTERSALES_ATY,AFTERSALES_ATN,
                AFTERSALES_TP, AFTERSALES_TK, AFTERSALES_JZ, OTHER, THIRD_AMOUNT_RETURN, AFTERASALES_THIRD_AT,
                AFTERASALES_THIRD_WX, THIRD_CONSULTATION, THIRD_OTHER, LOST_TICKET).contains(getInstance(code));
    }

    public String getType() {
        return type;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }}
