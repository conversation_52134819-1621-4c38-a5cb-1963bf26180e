package com.vedeng.crm.presales.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.vedeng.common.core.base.BaseDto;
import com.vedeng.common.core.utils.validator.group.AddGroup;
import com.vedeng.erp.business.domain.dto.RelatedOrderDto;
import com.vedeng.erp.saleorder.dto.OrderTerminalDto;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.erp.system.dto.CustomTagDto;
import com.vedeng.erp.trader.domain.dto.QuoteorderDto;
import com.vedeng.erp.trader.dto.CommunicateRecordDto;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/12/9
 */
@Setter
@Getter
@NoArgsConstructor
public class PreSalesInfoDto extends BaseDto {



    /**
     * 如果是更新场景，走以下逻辑
     */
    private PreSalesInfoType preSalesInfoType;

    /**
     * 归属销售头像-列表字段
     */
    private String belongPic;

    /**
     * 线索编号
     */
    private String leadsNo;

    /**
     * 商机编号
     */
    private String bussinessChanceNo;

    //模块1 - 线索来源
    //    线索类型：总机询价
    //    企微提醒： - 提醒 不提醒
    //    询价行为： 即时通讯 电话  留言
    //    渠道类型： 搜索引擎 官网及APP 第三方平台 整合营销
    //    渠道名称： 百度SEM-贝登生物 百度SEM-贝登电子商务 百度SEM-贝登电子电工 百度SEM-AED
    /**
     * 线索类型字典库 391 总机线索
     * 391	总机询价
     * 392	销售新增询价
     * 394	自主询价
     * 1933	贝登微信
     * 4110	线索转商机
     */
    private Integer clueType;

    /**
     * 企微提醒-是否发送微信标识(Y通知，N不通知)
     */
    private  String sendVx;

    /**
     * 询价行为字典库
     */
    private Integer inquiry;

    /**
     * 渠道类型字典库
     */
    private Integer source;

    /**
     * 渠道名称字典库
     */
    private Integer communication;

    /**
     * 三级分类-多个以&&拼接；例：临床检验/尿液分析设备/测试三级分类&&呼麻急救/急救设备/AED训练机
     */
    private String content;


    //模块2 - 客户信息
//    客户名称
//    手机
//    电话
//    其它联系方式
//    联系人：
    /**
     * 客户名称
     */
    private String traderName;

    /**
     * 客户ID
     */
    private Integer traderId;

    /**
     * 天眼查标识Y是N否
     */
    private String tycFlag;

    /**
     * 手机号
     */
    private String phone;
    /**
     * 电话
     */
    private String telephone;
    /**
     * 联系人
     */
    private String contact;
    /**
     * 联系人Id
     */
    private Integer traderContactId;

    /**
     * 其他联系方式
     */
    private String otherContactInfo;

    //模块3 - 详细信息
//    业务类型：小产品大单品综合项目AED应急
//    产品信息：
//    预计成单金额：
//    预计成单日期：
//    标签： 重要商机 以旧换新 产品推广 新客户开发
//    客情关系： 熟悉终端决策人 熟悉使用人【多选】
//    采购方式：直接采购 招标采购
//                      招标采购：【单选】
//                          招投标阶段：提案咨询 立项论证 意向公示 公开招标 合同签署
//                          招标参数：可调整 不可调整
//    备注：
    /**
     * （业务类型）商机类型-字典值(小产品、大单品、综合项目、AED、应急)
     */
    private Integer businessType;

    /**
     * 产品信息-商机里是productInfo
     */
    private String goodsInfo;
    /**
     * 预计成单金额-列表字段
     */
    private BigDecimal amount;
    /**
     * 预计成单日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderTime;

    /**
     * 线索标签id：英文逗号分割的id
     */
    private String tagIds;
    /**
     * 客情关系(1熟悉终端决策人，2熟悉使用人，多选逗号分隔)【多选】
     */
    private String customerRelationship;
    /**
     * 采购方式-字典值-（直接采购 招投标采购）-搜索用
     */
    private Integer purchasingType;
    /**
     * 招投标阶段-字典值(提案咨询、立项论证、意向公示、公开招标、合同签署)
     */
    private Integer biddingPhase;
    /**
     * 招标参数(1可调整，2不可调整)-搜索用
     */
    private Integer biddingParameter;
    /**
     * 备注
     */
    private String remark;

    //模块4 - 终端信息
//    终端名称：
//    终端性质：公立等级 公立基层 非公等级 非公基层 非公集团 应急院外
//    终端地区：
    /**
     * 终端名称
     */
    private String terminalTraderName;

    /**
     * 终端性质-字典值(公立等级、公立基层、非公等级、非公基层、非公集团、应急、院外)
     */
    private Integer terminalTraderNature;

    /**
     * 终端区域(省市区id，逗号分隔，省市区三级ID，从高向低拼接)-210000,210400,210403
     */
    private String terminalTraderRegion;

    /**
     * 关联终端信息--这个对象要保留单独存
     */
    private OrderTerminalDto orderTerminalDto;

    /**
     * 沟通记录
     *
     * 线索只会有下次跟进时间和下次跟进事项
     */
    private CommunicateRecordDto communicateRecordDto;


    /**
     * 咨询入口
     */
    private Integer entrances;

    /**
     * 渠道类型-展示用
     */
    private String entrancesName;


    /**
     * 功能
     */
    private Integer functions;

    /**
     * 渠道类型-展示用
     */
    private String functionsName;


    // 归属人逻辑
    /**
     * 归属人id
     */
    private Integer belongerId;

    /**
     * 归属人
     */
    private String belonger;


    /**
     * 省id
     */
    private Integer provinceId;

    /**
     * 市id
     */
    private Integer cityId;

    /**
     * 县id
     */
    private Integer countyId;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 县
     */
    private String county;

    /**
     * 报价单实体-用于商机模块信息的返回
     */
    private QuoteorderDto quoteorderDto = new QuoteorderDto();

    /**
     * 销售订单信息-用于商机模块信息的返回
     */
    private SaleorderInfoDto saleorderInfoDto = new SaleorderInfoDto();

    //线索新增字段
    /**
     * 线索跟进状态(0未分配，1.未处理、2.跟进中、3.已关闭、4.已商机)
     */
    private Integer followStatus;
    /**
     * 询价行为-名称
     */
    private String inquiryName;
    /**
     * 渠道类型-名称
     */
    private String sourceName;
    /**
     * 渠道名称-名称
     */
    private String communicationName;
    /**
     * 商机类型-名称-对应businessType
     */
    private String businessTypeStr;





    //以下字段均为商机模块返回
    /**
     * 我的关注-搜索用-（1关注，0未关注）
     */
    private Integer attentionState;
    /**
     * 商机状态0未处理、1报价中、2已报价、3已订单、4已关闭、5未分配、6处理中、7已成单
     */
    private Integer status;

    /**
     * 商机阶段(1初步洽谈，2商机验证，3初步方案，4最终方案，5赢单，6关闭)
     */
    private Integer stage;
    /**
     * 初步洽谈时间
     */
    private Date preliminaryNegotiationTime;

    /**
     * 商机验证时间
     */
    private Date opportunityVerificationTime;

    /**
     * 初步方案时间
     */
    private Date preliminarySchemeTime;

    /**
     * 最终方案时间
     */
    private Date finalSchemeTime;

    /**
     * 赢单时间
     */
    private Date winningOrderTime;

    /**
     * 关闭时间
     */
    private Date loseOrderTime;
    /**
     * 商机等级
     */
    private Integer systemBusinessLevel;


    /**
     * 商机等级-列表字段
     */
    private String systemBusinessLevelStr;

    /**
     * 返回给crm的前端erp的超链接
     */
    private String traderNameLink;

    /**
     * ERP内部链接
     */
    private String traderNameInnerLink;
    /**
     * 终端性质-名称
     */
    private String terminalTraderNatureStr;

    /**
     * 终端区域名称
     */
    private String terminalTraderRegionStr;
    /**
     * 下单日期-格式化
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date orderTimeDate;
    /**
     * 产品需求-搜索用
     */
    @ExcelProperty("*产品需求（销售）")
    private String productCommentsSale;

    /**
     * 标签
     */
    List<CustomTagDto> tags;

    /**
     * 客情关系-详情展示用
     */
    private List<String> customerRelationshipStr;

    /**
     * 采购方式-名称
     */
    private String purchasingTypeStr;
    /**
     * 招投标阶段-名称
     */
    private String biddingPhaseStr;

    /**
     * 备注-搜索用
     */
    private String comments;
    /**
     * 关联单据
     */
    private List<RelatedOrderDto> relatedOrderDtoList;

    private Integer  customerNature;

    /**
     * 关闭时间
     */
    private Date closeTime;

    /**
     * 关闭理由
     */
    private String closeReason;

    /**
     * 关闭原因类型
     * 1无法取得联系/2招投标无授权/3产品不在经营范围/4产品不在经营区域/5仅咨询技术问题/6客户没有购买意愿/7没有竞争力、价格没优势/8其他
     */
    private Integer closeReasonType;

    /**
     * 关闭原因类型对应的描述
     */
    private String closeReasonTypeName;

    /**
     * 三级分类ids
     */
    private String categoryIds;
    /**
     * 关键词
     */
    private String keywords;

}
