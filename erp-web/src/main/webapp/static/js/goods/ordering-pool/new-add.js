
$(function () {

    //搜索建议词
    $('.J-suggest-input').each(function () {
        let _this = this;
        new Suggest({
            el: this,
            url: page_url + $(this).data('url'),
            params: $(this).attr('name'),
            parseData: function (data) {
                return parseData[$(_this).attr('name')](data);
            }
        });
    })

    let parseData = {
        brandName: function (data) {
            let list = data.listData || [];
            let reslist = [];
            $.each(list, function (i, item) {
                reslist.push({
                    word: item.brandName,
                })
            })
            return reslist;
        }
    };

    let checkSelectAll = function () {
        let selectFlag = true;

        $('.J-select-sku').each(function () {
            if (!$(this)[0].checked) {
                selectFlag = false;
            }
        })

        $('.J-select-list-all')[0].checked = selectFlag;
    };

    //表格全选复选框
    $('.J-select-list-all').on("click",function () {

        let $this = $(this);

        $('.J-select-sku').each(function () {
            $(this)[0].checked = $this[0].checked;
        })

    });

    $('.J-select-sku').on("click",function () {
        checkSelectAll();
    })

    // 获取所有销量
    let $input = $('input:checkbox[name="skuId"]');
    let skuIdArray = new Array($input.length);
    for(let i=0;i<$input.length;i++){
        skuIdArray[i]=$input[i].value;
    }
    console.log(skuIdArray);
    $.ajax({
        async:true,
        url:'./getOneThreeMonthLastYearSaleNum.do',
        data: JSON.stringify(skuIdArray),
        type:"POST",
        dataType : "json",
        contentType: "application/json",
        success:function(data){
            console.log(data);
            for(let j=0;j<data.length;j++) {
                // console.log($("#lastYearSum"+data[j].skuId));
                // console.log(data[j].skuId);
                $('#lastYearSum'+data[j].skuId).text(data[j].lastYearSum);
                $('#lastYearPart'+data[j].skuId).text(data[j].lastYearPart);
                $('#threeMonthSum'+data[j].skuId).text(data[j].threeMonthSum);
                $('#threeMonthPart'+data[j].skuId).text(data[j].threeMonthPart);
                $('#oneMonthSum'+data[j].skuId).text(data[j].oneMonthSum);
                $('#oneMonthPart'+data[j].skuId).text(data[j].oneMonthPart);
            }
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    })

});


var new_parentId= "";
function getParentCategoryList(categoryId){
    checkLogin();
    $.ajax({
        async:false,
        url:page_url + '/goods/category/getParentCateList.do',
        data:{"categoryId":categoryId},
        type:"POST",
        dataType : "json",
        success:function(data){
            if(data.code==0){
                var list = data.listData;
                if(list!=null && list.length>0){
                    new_parentId = "";
                    var level = Number(list[0].level);
                    var ht = '<select id="categoryOpt" name="categoryOpt" style="width: 100px;" onchange="updateCategory(this,\'search\');">';
                    ht = ht + '<option value="-1" id="'+list[0].level+'">请选择</option>';
                    for(var i=0;i<list.length;i++){
                        if(list[i].categoryId==categoryId){
                            ht = ht + '<option value="'+list[i].categoryId+'" id="'+list[i].level+'" selected >'+list[i].categoryName+'</option>';
                        }else{
                            ht = ht + '<option value="'+list[i].categoryId+'" id="'+list[i].level+'" >'+list[i].categoryName+'</option>';
                        }
                        if(list[i].parentId!=0 && list[i].parentId!="0"){
                            new_parentId = list[i].parentId;
                        }
                    }
                    ht = ht + '</select>';
                    $("#category_div").prepend(ht);
                    if(new_parentId!=""){
                        getParentCategoryList(new_parentId);
                    }
                }
            }else{
                layer.alert("获取对应分类信息失败，请稍后重试或联系管理员！");
                return false;
            }
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    })
}

function joinPool(obj) {

    // obj -- [skuId]
    console.log(obj);
    if(obj.length == 0){
        console.log("结束了..");
        layer.alert("请选择需要加入定品池的商品")
        return;
    }

    let array = new Array();
    for(let i=0;i<obj.length;i++){
        array[i] = {skuId:obj[i]};
    }

    let load = layer.msg('处理中', {
        icon: 16
        ,shade: 0.1
    });

    $.ajax({
        async:false,
        url:'./joinOrderingPool.do',
        data: JSON.stringify({
            idFlag:1,
            joinOrderingPoolOperateTempList:array
        }),
        type:"POST",
        dataType : "json",
        contentType: "application/json",
        success:function(data){
            layer.close(load);
            console.log(data);
            search();
        },
        error:function(data){
            layer.close(load);
            console.log(data);
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
            if(data.status ==400){
                layer.alert("当前操作异常")
            }
        }
    })
}

function getChildrenCategory(obj) {

    let optId = $(obj).attr("id");
    let val = $(obj).val();
    if(optId == "categoryOpt0"){
        if(val != "-1" && val != -1) {
            $.ajax({
                async: false,
                url: page_url + '/goods/category/getCategoryList.do',
                data: {"parentId": val},
                type: "POST",
                dataType: "json",
                success: function (data) {
                    if (data.code == 0) {
                        let list = data.listData;
                        if (list != null && list.length > 0) {
                            let ht = "<option value='-1'>请选择</option>";
                            for (let i = 0; i < list.length; i++) {
                                ht = ht + "<option value='" + list[i].categoryId + "'>" + list[i].categoryName + "</option>";
                            }
                            $("#categoryOpt1").empty();
                            $("#categoryOpt1").append(ht);
                            let ht1 = "<option value='-1'>请选择</option>";
                            $("#categoryOpt2").empty();
                            $("#categoryOpt2").append(ht1);
                        }else {
                            let ht = "<option value='-1'>请选择</option>";
                            $("#categoryOpt1").empty();
                            $("#categoryOpt1").append(ht);
                            let ht1 = "<option value='-1'>请选择</option>";
                            $("#categoryOpt2").empty();
                            $("#categoryOpt2").append(ht1);
                        }
                    }
                },
                error: function (data) {
                    if (data.status == 1001) {
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            })
        }else{
            let ht1 = "<option value='-1'>请选择</option>";
            $("categoryOpt1").empty();
            $("categoryOpt1").append(ht1);
            let ht2 = "<option value='-1'>请选择</option>";
            $("categoryOpt2").empty();
            $("categoryOpt2").append(ht2);
        }
        $("#categoryId").val(0);

    }else if(optId == "categoryOpt1"){
        if(val != "-1" && val != -1) {
            $.ajax({
                async: false,
                url: page_url + '/goods/category/getCategoryList.do',
                data: {"parentId": val},
                type: "POST",
                dataType: "json",
                success: function (data) {
                    if (data.code == 0) {
                        let list = data.listData;
                        if (list != null && list.length > 0) {
                            let ht111 = "<option value='-1'>请选择</option>";
                            for (let i = 0; i < list.length; i++) {
                                ht111 = ht111 + "<option value='" + list[i].categoryId + "'>" + list[i].categoryName + "</option>";
                            }
                            $("#categoryOpt2").empty();
                            $("#categoryOpt2").append(ht111);
                        }
                    }
                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            })
        }else{
            let ht2 = "<option value='-1'>请选择</option>";
            $("#categoryOpt2").empty();
            $("#categoryOpt2").append(ht2);
        }
        $("#categoryId").val(0);
    }else{
        $("#categoryId").val(val);
    }
}

/**
 * layui函数
 */
var callbackdata1 = function() {

    // 获取所有销量
    let $input = $('input:checkbox:checked[name="skuId"]');
    let skuIdArray = new Array($input.length);
    for(let i=0;i<$input.length;i++){
        skuIdArray[i]=$input[i].value;
    }
    // 加入定品池
    joinPool(skuIdArray);
    // console.log(skuIdArray);
    return skuIdArray;
}
