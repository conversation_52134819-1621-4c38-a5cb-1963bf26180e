package com.vedeng.erp.system.web.controller;

import com.vedeng.common.core.base.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 银行信息路由接口
 * @date 2022/8/15 17:01
 */
@Controller
@RequestMapping("/bank")
@Slf4j
public class BankController extends BaseController {


    @RequestMapping(value = "/edit")
    public ModelAndView edit(@RequestParam(required = false) Integer bankId) {
        ModelAndView mv = new ModelAndView("vue/view/bank/edit");
        mv.addObject("bankId", bankId);
        return mv;
    }


}
