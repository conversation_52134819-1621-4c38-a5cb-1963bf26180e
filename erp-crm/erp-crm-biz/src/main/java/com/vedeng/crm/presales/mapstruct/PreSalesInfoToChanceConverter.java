package com.vedeng.crm.presales.mapstruct;

import com.ctrip.framework.apollo.ConfigService;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.crm.presales.dto.PreSalesInfoDto;
import com.vedeng.erp.business.domain.dto.BusinessChanceDto;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/12/9
 */
@Mapper(componentModel = "spring",builder = @Builder(disableBuilder = true))
public interface PreSalesInfoToChanceConverter{

    /**
     * Entity转DTO
     *
     * @param entity BusinessLeadsEntity
     * @return BusinessLeadsDto
     */
    @Mapping(source="bussinessChanceNo",target = "bussinessChanceNo")
    @Mapping(source="belongPic",target = "belongPic")
    @Mapping(source="clueType",target = "type")
    @Mapping(source="sendVx",target = "sendVx")
    @Mapping(source="inquiry",target = "inquiry")
    @Mapping(source="source",target = "source")
    @Mapping(source="communication",target = "communication")
    @Mapping(source="content",target = "content")
    @Mapping(source="traderName",target = "traderName")
    @Mapping(source="traderId",target = "traderId")
    @Mapping(source="tycFlag",target = "tycFlag")
    @Mapping(source="phone",target = "mobile")
    @Mapping(source="telephone",target = "telephone")
    @Mapping(source="contact",target = "traderContactName")
    @Mapping(source="traderContactId",target = "traderContactId")
    @Mapping(source="otherContactInfo",target = "otherContactInfo")
    @Mapping(source="tagIds",target = "tagIds")
    @Mapping(source="businessType",target = "businessType")//（业务类型）商机类型-字典值(小产品、大单品、综合项目、AED、应急)
    @Mapping(source = "goodsInfo",target = "productCommentsSale")//产品信息-商机里是productInfo
    @Mapping(target = "amount", source = "amount")//预计成单金额-列表字段
//    @Mapping(target = "orderTime", source = "orderTime")//预计成单日期
//    @Mapping(target = "tagIdList", source = "tagIdList")//线索标签id list
    @Mapping(target = "customerRelationship", source = "customerRelationship")//客情关系(1熟悉终端决策人，2熟悉使用人，多选逗号分隔)【多选】
    @Mapping(target = "purchasingType", source = "purchasingType")//采购方式-字典值-（直接采购 招投标采购）
    @Mapping(target = "biddingPhase", source = "biddingPhase")//招投标阶段-字典值(提案咨询、立项论证、意向公示、公开招标、合同签署)
    @Mapping(target = "biddingParameter", source = "biddingParameter")//招标参数(1可调整，2不可调整)-搜索用
    @Mapping(target = "terminalTraderName", source = "terminalTraderName")//终端名称
    @Mapping(target = "terminalTraderNature", source = "terminalTraderNature")//终端性质-字典值(公立等级、公立基层、非公等级、非公基层、非公集团、应急、院外)
    @Mapping(target = "terminalTraderRegion", source = "terminalTraderRegion")//终端区域(省市区id，逗号分隔，省市区三级ID，从高向低拼接)-210000,210400,210403
    @Mapping(target = "comments", source = "remark")//
    @Mapping(target = "orderTerminalDto", source = "orderTerminalDto")//
    @Mapping(target = "communicateRecordDto", source = "communicateRecordDto")//
    @Mapping(target = "userId", source = "belongerId")//
    @Mapping(target = "username", source = "belonger")
    @Mapping(target = "addTime", source = "addTime",qualifiedByName = "date2Long")//
    @Mapping(target = "modTime", source = "modTime",qualifiedByName = "date2Long")
    @Mapping(target = "orderTime", source = "orderTime",qualifiedByName = "date2Long")
    @Mapping(source = "entrances", target = "entrances")
    @Mapping(source = "functions", target = "functions")
    @Mapping(source = "provinceId", target = "provinceId")
    @Mapping(source = "cityId", target = "cityId")
    @Mapping(source = "countyId", target = "countyId")
    @Mapping(source = "province", target = "province")
    @Mapping(source = "city", target = "city")
    @Mapping(source = "county", target = "county")
    @Mapping(source = "clueType",target = "clueType")
    BusinessChanceDto toDto(PreSalesInfoDto entity);


    /**
     * DTO转Entity
     *
     * @param dto BusinessChanceDto
     * @return PreSalesInfoDto
     */
    @Mapping(source="bussinessChanceNo",target = "bussinessChanceNo")
    @Mapping(source="belongPic",target = "belongPic")
    @Mapping(source = "type", target = "clueType")
    @Mapping(source = "sendVx", target = "sendVx")
    @Mapping(source = "inquiry", target = "inquiry")
    @Mapping(source = "source", target = "source")
    @Mapping(source = "communication", target = "communication")
    @Mapping(source = "content", target = "content")
    @Mapping(source = "traderName", target = "traderName")
    @Mapping(source = "traderId", target = "traderId")
    @Mapping(source = "tycFlag", target = "tycFlag")
    @Mapping(source = "mobile", target = "phone")
    @Mapping(source = "telephone", target = "telephone")
    @Mapping(source = "traderContactName", target = "contact")
    @Mapping(source = "traderContactId", target = "traderContactId")
    @Mapping(source = "otherContactInfo", target = "otherContactInfo")
    @Mapping(source="tagIds",target = "tagIds")
    @Mapping(source = "businessType", target = "businessType")
    @Mapping(source = "productCommentsSale", target = "goodsInfo")
    @Mapping(source = "amount", target = "amount")
    @Mapping(source = "customerRelationship", target = "customerRelationship")
    @Mapping(source = "purchasingType", target = "purchasingType")
    @Mapping(source = "biddingPhase", target = "biddingPhase")
    @Mapping(source = "biddingParameter", target = "biddingParameter")
    @Mapping(source = "terminalTraderName", target = "terminalTraderName")
    @Mapping(source = "terminalTraderNature", target = "terminalTraderNature")
    @Mapping(source = "terminalTraderRegion", target = "terminalTraderRegion")
    @Mapping(source = "comments", target = "remark")
    @Mapping(source = "orderTerminalDto", target = "orderTerminalDto")
    @Mapping(source = "communicateRecordDto", target = "communicateRecordDto")
    @Mapping(source = "userId", target = "belongerId")
    @Mapping(source = "username", target = "belonger")
    @Mapping(source = "addTime", target = "addTime", qualifiedByName = "long2Date")
    @Mapping(source = "modTime", target = "modTime", qualifiedByName = "long2Date")
    @Mapping(source = "orderTime", target = "orderTime", qualifiedByName = "long2Date")
    @Mapping(source = "systemBusinessLevel", target = "systemBusinessLevel")
    @Mapping(source = "systemBusinessLevelStr", target = "systemBusinessLevelStr")
    @Mapping(source = "traderNameLink", target = "traderNameLink")
    @Mapping(source = "traderNameInnerLink", target = "traderNameInnerLink")
    @Mapping(source = "terminalTraderNatureStr", target = "terminalTraderNatureStr")
    @Mapping(source = "terminalTraderRegionStr", target = "terminalTraderRegionStr")
    @Mapping(source = "orderTimeDate", target = "orderTimeDate")
    @Mapping(source = "productCommentsSale", target = "productCommentsSale")
    @Mapping(source = "tags", target = "tags")
    @Mapping(source = "customerRelationshipStr", target = "customerRelationshipStr")
    @Mapping(source = "purchasingTypeStr", target = "purchasingTypeStr")
    @Mapping(source = "biddingPhaseStr", target = "biddingPhaseStr")
    @Mapping(source = "entrances", target = "entrances")
    @Mapping(source = "functions", target = "functions")
    @Mapping(source = "entrancesName", target = "entrancesName")
    @Mapping(source = "functionsName", target = "functionsName")
    @Mapping(source = "status", target = "status")
    @Mapping(source = "stage", target = "stage")
    @Mapping(source = "preliminaryNegotiationTime", target = "preliminaryNegotiationTime")
    @Mapping(source = "opportunityVerificationTime", target = "opportunityVerificationTime")
    @Mapping(source = "preliminarySchemeTime", target = "preliminarySchemeTime")
    @Mapping(source = "finalSchemeTime", target = "finalSchemeTime")
    @Mapping(source = "winningOrderTime", target = "winningOrderTime")
    @Mapping(source = "loseOrderTime", target = "loseOrderTime")
    @Mapping(source = "attentionState", target = "attentionState")
    @Mapping(source = "relatedOrderDtoList", target = "relatedOrderDtoList")
    @Mapping(source = "customerNature", target = "customerNature")
    @Mapping(source = "provinceId", target = "provinceId")
    @Mapping(source = "cityId", target = "cityId")
    @Mapping(source = "countyId", target = "countyId")
    @Mapping(source = "province", target = "province")
    @Mapping(source = "city", target = "city")
    @Mapping(source = "county", target = "county")
    @Mapping(source = "loseOrderTime", target = "closeTime")
    @Mapping(source = "closedComments", target = "closeReason")//关闭原因说明
    @Mapping(source = "statusComments", target = "closeReasonType")//关闭原因类型
    @Mapping(source = "statusCommentsStr",target = "closeReasonTypeName")//关闭原因类型对应的名称
    PreSalesInfoDto toResponseBody(BusinessChanceDto dto);

    /**
     * str转strList
     *
     * @param date
     * @return long
     */
    @Named("date2Long")
    default Long date2Long(Date date) {
        if(date == null)
        {
            return null;
        }
        return date.getTime();
    }

    @Named("long2Date")
    default Date long2Date(Long time) {
        if (time == null) {
            return null;
        }
        return new Date(time);
    }

}
