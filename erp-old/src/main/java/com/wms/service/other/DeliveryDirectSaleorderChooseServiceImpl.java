package com.wms.service.other;

import com.alibaba.fastjson.JSON;
import com.vedeng.authorization.model.Region;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.flash.enums.WmsSendOrderTypeEnum;
import com.vedeng.logistics.dao.LogisticsMapper;
import com.vedeng.logistics.model.Logistics;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.dao.RBuyorderSaleorderMapper;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.*;
import com.vedeng.system.service.OrgService;
import com.vedeng.system.service.RegionService;
import com.vedeng.trader.dao.TraderAddressMapper;
import com.vedeng.trader.model.TraderAddress;
import com.wms.constant.CancelReasonConstant;
import com.wms.constant.LogicalEnum;
import com.wms.constant.WMSContant;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.dao.WmsSendOrderMapper;
import com.wms.dto.PurchaseDeliveryDirectBatchDetailDto;
import com.wms.dto.PutSaleOrderDto;
import com.wms.dto.PutSaleOrderGoodsDto;
import com.wms.dto.WmsResponse;
import com.wms.model.po.WmsSendOrder;
import com.wms.service.CancelTypeService;
import com.wms.service.WMSInterfaceFactory;
import com.wms.service.WmsInterface;
import com.wms.service.util.WmsCommonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: daniel
 * @Date: 2022/4/14 15 26
 * @Description:
 */
@Service("deliveryDirectSaleorderChooseService")
public class DeliveryDirectSaleorderChooseServiceImpl {

    private static final Logger logger = LoggerFactory.getLogger(DeliveryDirectSaleorderChooseServiceImpl.class);

    @Autowired
    private OrgService orgService;

    @Autowired
    private SaleorderMapper saleorderMapper;

    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Resource
    private TraderAddressMapper traderAddressMapper;

    @Autowired
    private RegionService regionService;

    @Autowired
    private LogisticsMapper logisticsMapper;

    @Autowired
    private BuyorderMapper buyorderMapper;

    @Autowired
    private RBuyorderSaleorderMapper rBuyorderSaleorderMapper;

    @Autowired
    private WMSInterfaceFactory wmsInterfaceFactory;

    @Autowired
    private CancelTypeService cancelTypeService;

    @Autowired
    private WmsSendOrderMapper wmsSendOrderMapper;


    /**
     * 直发销售单出库下发WMS
     * @param purchaseDeliveryDirectBatchDetails 直发采购单维护的同行单集合
     * 如果之前维护的同行单数据中 wmsHandledDeliveryCount 小于 arrivalCount，那么之前维护的同行单数据还需再次重新下发，下发数量为 arrivalCount - wmsHandledDeliveryCount。
     * @throws Exception 下发异常
     */
    public void putSaleOrderOutput(List<PurchaseDeliveryDirectBatchDetailDto> purchaseDeliveryDirectBatchDetails, User user) throws Exception {
        if(CollectionUtils.isEmpty(purchaseDeliveryDirectBatchDetails)){
            return;
        }

        List<Integer> saleorderIdList = buyorderMapper.getSaleorderIdListByBuyorderIdNoBH(purchaseDeliveryDirectBatchDetails.get(0).getBuyorderId());

        if (CollectionUtils.isEmpty(saleorderIdList)){
            return;
        }

        Saleorder saleorder = saleorderMapper.getSaleOrderById(saleorderIdList.get(0));
        Buyorder buyorder = buyorderMapper.selectByPrimaryKey(purchaseDeliveryDirectBatchDetails.get(0).getBuyorderId());

        try {
            Integer wmsSendOrderId = saveWmsSendOrder(buyorderMapper.selectByPrimaryKey(purchaseDeliveryDirectBatchDetails.get(0).getBuyorderId()), user,
                    WmsSendOrderTypeEnum.PUT_DELIVERY_DIRECT_SALEORDER.getCode());

            //存在正在进行中的订单修改并且此修改申请需要重新下发wms,拦截下传WMS订单
            List<SaleorderModifyApply> saleorderModifyApplyList = saleorderMapper.getSaleorderModifyApplyList(saleorder.getSaleorderId());
            if(CollectionUtils.isNotEmpty(saleorderModifyApplyList)){
                Optional<SaleorderModifyApply> optional = saleorderModifyApplyList.stream().filter(item -> item.getVerifyStatus().equals(0)).findFirst();
                if(optional.isPresent() && optional.get().getIsWmsCancel().equals(1)){
                    logger.info("销售单存在物流信息订单修改申请不下发  单号:{}",saleorder.getSaleorderNo());
                    return;
                }
            }

            PutSaleOrderDto putSaleOrderDto = new PutSaleOrderDto();
            putSaleOrderDto.setWarehouseId("NJ02");
            putSaleOrderDto.setDocNo(WmsCommonUtil.addTimestampForOrderNo(saleorder.getSaleorderNo()));
            //账期客户或金额大于1w需要提供签回单
            if(new BigDecimal("10000").compareTo(saleorder.getTotalAmount()) <= 0 || saleorder.getHaveAccountPeriod() == 1){
                putSaleOrderDto.setHedi01("Y");
            }
            if (saleorder.getHaveAccountPeriod() == 1) {
                putSaleOrderDto.setHedi08("Y");
            } else {
                putSaleOrderDto.setHedi08("N");
            }
            putSaleOrderDto.setOrderType(WmsInterfaceOrderType.OUT_DIRECT_SALE_OUT);
            putSaleOrderDto.setOrderTime(DateUtil.convertString(saleorder.getSatisfyDeliveryTime(),"yyyy-MM-dd HH:mm:ss"));

            // 预期到货时间，快递时间中的最小 最大值 因为传递过来的就是yyyy-MM-dd 的直接字符比较
            Optional<PurchaseDeliveryDirectBatchDetailDto> min = purchaseDeliveryDirectBatchDetails.stream().filter(item-> StringUtils.isNotEmpty(item.getExpressDeliveryTime())).min(Comparator.comparing(PurchaseDeliveryDirectBatchDetailDto::getExpressDeliveryTime));
            if (min.isPresent()) {
                putSaleOrderDto.setExpectedShipmentTime1(min.get().getExpressDeliveryTime() + " 00:00:00");
            } else {
                putSaleOrderDto.setExpectedShipmentTime1(DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss"));
            }
            putSaleOrderDto.setExpectedShipmentTime2("9999-12-21 23:59:59");

            User user1 = orgService.getTraderUserAndOrgByTraderId(saleorder.getTraderId(), 1);
            putSaleOrderDto.setSoReferenceA(StringUtil.isEmpty(user1.getOrgName()) ? "/" : user1.getOrgName());
            putSaleOrderDto.setSoReferenceB(user1.getUsername());
            //客户ID
            putSaleOrderDto.setConsigneeId(saleorder.getTraderId().toString());
            putSaleOrderDto.setConsigneeName(saleorder.getTraderName());
            int areaId;
            if(saleorder.getTakeTraderAreaId() != null && !saleorder.getTakeTraderAreaId().equals(0)){
                areaId = saleorder.getTakeTraderAreaId();
            }else{
                if(saleorder.getTakeTraderAddressId() == null || saleorder.getTakeTraderAddressId().equals(0)){
                    throw new Exception("销售单收货地址id为空下发WMS失败单号:"+saleorder.getSaleorderNo());
                }
                TraderAddress addressInfo = traderAddressMapper.getAddressInfoById(saleorder.getTakeTraderAddressId(), null);
                if(addressInfo == null || addressInfo.getAreaId().equals(0)){
                    throw new Exception("销售单收货地址为空下发WMS失败单号:"+saleorder.getSaleorderNo());
                }
                areaId = addressInfo.getAreaId();
            }
            if(areaId == 0){
                throw new Exception("销售单收货地址id为0下发WMS失败单号:"+saleorder.getSaleorderNo());
            }
            List<Region> regionList = regionService.getRegionInfoByMinRegionId(areaId);
            if(CollectionUtils.isEmpty(regionList)){
                throw new Exception("销售单收货地区为空下发WMS失败单号:"+saleorder.getSaleorderNo());
            }
            putSaleOrderDto.setConsigneeProvince(regionList.size() > 0 ? regionList.get(0).getRegionName(): "");
            putSaleOrderDto.setConsigneeCity(regionList.size() > 1 ? regionList.get(1).getRegionName(): "");
            putSaleOrderDto.setConsigneeDistrict(regionList.size() > 2 ? regionList.get(2).getRegionName(): "");

            putSaleOrderDto.setConsigneeAddress1(saleorder.getTakeTraderAddress());
            putSaleOrderDto.setConsigneeContact(saleorder.getTakeTraderContactName());
            putSaleOrderDto.setConsigneeTel1(saleorder.getTakeTraderContactMobile());
            putSaleOrderDto.setConsigneeTel2(saleorder.getTakeTraderContactTelephone());
            //发货类型默认正常发货
            putSaleOrderDto.setHedi07("A");

            putSaleOrderDto.setNotes(saleorder.getLogisticsComments());
            //是否打印4联随货同行单
            putSaleOrderDto.setHedi03("Y");

            //发货方式
            putSaleOrderDto.setHedi04("N");

            //VDERP-3633 【WMS】物流随货发送资质文件判断条件变更为B2B和科研购的订单通通不要资质文件
            //是否是医械购客户
            User orderUser = orgService.getTraderUserAndOrgByTraderId(saleorder.getTraderId(), 1);
            boolean hcOrgFlag = false;
            if(orderUser != null && null != orderUser.getOrgName() && orderUser.getOrgName().contains("医械购")){
                hcOrgFlag = true;
            }

            Map<String, Integer> goodsisExistmap = new HashMap<>();
            if(hcOrgFlag) {
                List<Saleorder> hcOrgorderList = saleorderMapper.getHcOrgValidStatus(saleorder);

                if (CollectionUtils.isEmpty(hcOrgorderList)) {
                    putSaleOrderDto.setHedi05("Y");
                } else {
                    //已存在发货商品
                    for (Saleorder orderIsExist : hcOrgorderList) {
                        List<SaleorderGoods> isExitGoods = saleorderGoodsMapper.getGoodsSkuByOrderId(orderIsExist.getSaleorderId());
                        for (SaleorderGoods isExitGood : isExitGoods) {
                            if (isExitGood.getNum() - isExitGood.getAfterReturnNum() > 0) {
                                goodsisExistmap.put(isExitGood.getSku(), isExitGood.getGoodsId());
                            }
                        }
                    }
                    putSaleOrderDto.setHedi05("N");
                }
            }else{
                putSaleOrderDto.setHedi05("N");
            }
            //VDERP-3633 【WMS】物流随货发送资质文件判断条件变更为B2B和科研购的订单通通不要资质文件

            //VDERP-7428 物流公司
            if (saleorder.getLogisticsId() != null && !saleorder.getLogisticsId().equals(0)){
                Logistics logistics = logisticsMapper.getLogisticsById(saleorder.getLogisticsId());
                putSaleOrderDto.setCarrierId(logistics.getCarrierId());
                putSaleOrderDto.setCarrierName(logistics.getName());
            }

            List<PutSaleOrderGoodsDto> details = new ArrayList<>();
            List<Integer> collect = purchaseDeliveryDirectBatchDetails.stream().map(PurchaseDeliveryDirectBatchDetailDto::getBuyorderId).distinct().collect(Collectors.toList());
            Map<Integer, Integer> buyorderSaleorderMap =
                    rBuyorderSaleorderMapper.getRBuyorderSaleorderInfoListByBuyorderIds(collect).stream()
                            .collect(Collectors.toMap(RBuyorderSaleorder::getBuyorderGoodsId,RBuyorderSaleorder::getSaleorderGoodsId));

            logger.info("下发直发出库任务，查询到采购销售关联信息{},",JSON.toJSONString(buyorderSaleorderMap));
            for (PurchaseDeliveryDirectBatchDetailDto item : purchaseDeliveryDirectBatchDetails){
                PutSaleOrderGoodsDto detail = new PutSaleOrderGoodsDto();
                detail.setSku(item.getSku());
                detail.setLineNo(String.valueOf(item.getPurchaseDeliveryDirectBatchDetailId()));
                detail.setQtyOrdered(item.getArrivalCount());
                Buyorder buyorderdata = buyorderMapper.selectByPrimaryKey(item.getBuyorderId());
                detail.setLotAtt07(buyorderdata.getBuyorderNo());
                detail.setLotAtt08(LogicalEnum.HG.getLogicalWarehouseCode());
                Integer isExistGodosId = goodsisExistmap.get(item.getSku());
                if(isExistGodosId == null && hcOrgFlag){
                    detail.setDedi04("Y");
                }else{
                    detail.setDedi04("N");
                }
                //订单单价
                SaleorderGoods saleorderGoods = saleorderGoodsMapper.selectByPrimaryKey(buyorderSaleorderMap.get(item.getBuyorderGoodsId()));
                logger.info("下发直发出库任务，查询条件采购单商品id{},到销售单商品信息为{},",item.getBuyorderGoodsId(),JSON.toJSONString(saleorderGoods));
                if (Objects.isNull(saleorderGoods)){
                    throw new Exception("下发直发出库任务，根据采购单商品id未查询到销售单商品信息");
                }
                detail.setDedi07(saleorderGoods.getSaleorderGoodsId() + "");
                detail.setDedi09(saleorderGoods.getRealPrice());
                // 加上同行单的快递时间
                detail.setDedi13(item.getExpressDeliveryTime());
                details.add(detail);
            }
            putSaleOrderDto.setDetails(details);

            if(CollectionUtils.isEmpty(details)){
                logger.info("销售单中没有商品明细 无法下发WMS:{}",saleorder.getSaleorderNo());
                return;
            }

            if(!cancelTypeService.cancelInputDeliveryDirectSaleReturnMethod(saleorder.getSaleorderNo(), CancelReasonConstant.SEND_ORDER)){
                logger.info("wms不得取消下发失败 单号:{}",saleorder.getSaleorderNo());
                return;
            }

            logger.info("WMS销售出库单下传的 单号:{},请求:{}",saleorder.getSaleorderNo(),JSON.toJSONString(putSaleOrderDto));
            WmsInterface putSaleOrderOutputInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_ORIGINAL_SALESORDER);

            WmsResponse response = putSaleOrderOutputInterface.request(putSaleOrderDto);

            logger.info("WMS销售出库单下传的 单号:{},响应:{}",saleorder.getSaleorderNo(),JSON.toJSONString(response));
            if("1".equals(response.getReturnFlag())){
                WmsSendOrder updateWmsSendorder = new WmsSendOrder();
                updateWmsSendorder.setWmsSendOrderId(wmsSendOrderId);
                updateWmsSendorder.setSendStatus(ErpConst.ONE);
                wmsSendOrderMapper.updateByPrimaryKeySelective(updateWmsSendorder);
            }
        } catch (Exception e){
            logger.error("下发销售单失败error 单号：{}",saleorder.getSaleorderNo(),e);
        }

    }


    //保存下传的单号
    private Integer saveWmsSendOrder(Buyorder buyorder, User user, int orderType) {
        WmsSendOrder wmsSendOrder = new WmsSendOrder();
        try {
            wmsSendOrder.setOrderType(orderType);
            wmsSendOrder.setOrderId(buyorder.getBuyorderId());
            wmsSendOrder.setOrderNo(buyorder.getBuyorderNo());
            WmsSendOrder oldInfo = wmsSendOrderMapper.getWmsSendOrderInfo(wmsSendOrder);
            if(oldInfo != null){
                WmsSendOrder update = new WmsSendOrder();
                update.setSendStatus(0);
                update.setWmsSendOrderId(oldInfo.getWmsSendOrderId());
                wmsSendOrderMapper.updateByPrimaryKeySelective(update);
                return oldInfo.getWmsSendOrderId();
            }
            Integer userId = 0;
            if(user != null && user.getUserId() != null){
                userId = user.getUserId();
            }
            wmsSendOrder.setCreator(userId);
            wmsSendOrder.setUpdater(userId);
            wmsSendOrder.setSendStatus(0);
            wmsSendOrderMapper.insertSelective(wmsSendOrder);
        } catch (Exception e) {
            logger.error("saveWmsSendOrder error",e);
        }
        return wmsSendOrder.getWmsSendOrderId();
    }


}
