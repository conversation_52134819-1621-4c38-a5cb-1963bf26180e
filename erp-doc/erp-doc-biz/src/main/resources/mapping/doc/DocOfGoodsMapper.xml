<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.doc.mapper.DocOfGoodsMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.doc.domain.entity.DocOfGoods">
    <!--@mbg.generated-->
    <!--@Table T_DOC_OF_GOODS-->
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="SPU_ID" jdbcType="INTEGER" property="spuId" />
    <result column="SKU_ID" jdbcType="INTEGER" property="skuId" />
    <result column="GOODS_NAME" jdbcType="VARCHAR" property="goodsName" />
    <result column="MODEL" jdbcType="VARCHAR" property="model" />
    <result column="DOC_TITLE" jdbcType="VARCHAR" property="docTitle" />
    <result column="THIRD_CATEGORY" jdbcType="INTEGER" property="thirdCategory" />
    <result column="BRAND_ID" jdbcType="INTEGER" property="brandId" />
    <result column="DOC_SUPPLIER_ID" jdbcType="INTEGER" property="docSupplierId" />
    <result column="DOC_TEXT" jdbcType="LONGVARCHAR" property="docText" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="DISABLE" jdbcType="BOOLEAN" property="disable" />
    <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete" />
    <result column="SORT_RULES" jdbcType="INTEGER" property="sortRules" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, SPU_ID, SKU_ID, GOODS_NAME, MODEL, DOC_TITLE, THIRD_CATEGORY, BRAND_ID, DOC_SUPPLIER_ID, 
    DOC_TEXT, ADD_TIME, CREATOR, MOD_TIME, UPDATER, `DISABLE`, IS_DELETE, SORT_RULES
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_DOC_OF_GOODS
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_DOC_OF_GOODS
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.doc.domain.entity.DocOfGoods" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_DOC_OF_GOODS (SPU_ID, SKU_ID, GOODS_NAME, 
      MODEL, DOC_TITLE, THIRD_CATEGORY, 
      BRAND_ID, DOC_SUPPLIER_ID, DOC_TEXT, 
      ADD_TIME, CREATOR, MOD_TIME, 
      UPDATER, `DISABLE`, IS_DELETE, 
      SORT_RULES)
    values (#{spuId,jdbcType=INTEGER}, #{skuId,jdbcType=INTEGER}, #{goodsName,jdbcType=VARCHAR}, 
      #{model,jdbcType=VARCHAR}, #{docTitle,jdbcType=VARCHAR}, #{thirdCategory,jdbcType=INTEGER}, 
      #{brandId,jdbcType=INTEGER}, #{docSupplierId,jdbcType=INTEGER}, #{docText,jdbcType=LONGVARCHAR}, 
      #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, 
      #{updater,jdbcType=INTEGER}, #{disable,jdbcType=BOOLEAN}, #{isDelete,jdbcType=BOOLEAN}, 
      #{sortRules,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.doc.domain.entity.DocOfGoods" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_DOC_OF_GOODS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="spuId != null">
        SPU_ID,
      </if>
      <if test="skuId != null">
        SKU_ID,
      </if>
      <if test="goodsName != null">
        GOODS_NAME,
      </if>
      <if test="model != null">
        MODEL,
      </if>
      <if test="docTitle != null">
        DOC_TITLE,
      </if>
      <if test="thirdCategory != null">
        THIRD_CATEGORY,
      </if>
      <if test="brandId != null">
        BRAND_ID,
      </if>
      <if test="docSupplierId != null">
        DOC_SUPPLIER_ID,
      </if>
      <if test="docText != null">
        DOC_TEXT,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="disable != null">
        `DISABLE`,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="sortRules != null">
        SORT_RULES,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="spuId != null">
        #{spuId,jdbcType=INTEGER},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=INTEGER},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="docTitle != null">
        #{docTitle,jdbcType=VARCHAR},
      </if>
      <if test="thirdCategory != null">
        #{thirdCategory,jdbcType=INTEGER},
      </if>
      <if test="brandId != null">
        #{brandId,jdbcType=INTEGER},
      </if>
      <if test="docSupplierId != null">
        #{docSupplierId,jdbcType=INTEGER},
      </if>
      <if test="docText != null">
        #{docText,jdbcType=LONGVARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="disable != null">
        #{disable,jdbcType=BOOLEAN},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="sortRules != null">
        #{sortRules,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.doc.domain.entity.DocOfGoods">
    <!--@mbg.generated-->
    update T_DOC_OF_GOODS
    <set>
      <if test="spuId != null">
        SPU_ID = #{spuId,jdbcType=INTEGER},
      </if>
      <if test="skuId != null">
        SKU_ID = #{skuId,jdbcType=INTEGER},
      </if>
      <if test="goodsName != null">
        GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        MODEL = #{model,jdbcType=VARCHAR},
      </if>
      <if test="docTitle != null">
        DOC_TITLE = #{docTitle,jdbcType=VARCHAR},
      </if>
      <if test="thirdCategory != null">
        THIRD_CATEGORY = #{thirdCategory,jdbcType=INTEGER},
      </if>
      <if test="brandId != null">
        BRAND_ID = #{brandId,jdbcType=INTEGER},
      </if>
      <if test="docSupplierId != null">
        DOC_SUPPLIER_ID = #{docSupplierId,jdbcType=INTEGER},
      </if>
      <if test="docText != null">
        DOC_TEXT = #{docText,jdbcType=LONGVARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="disable != null">
        `DISABLE` = #{disable,jdbcType=BOOLEAN},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="sortRules != null">
        SORT_RULES = #{sortRules,jdbcType=INTEGER},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.doc.domain.entity.DocOfGoods">
    <!--@mbg.generated-->
    update T_DOC_OF_GOODS
    set SPU_ID = #{spuId,jdbcType=INTEGER},
      SKU_ID = #{skuId,jdbcType=INTEGER},
      GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      MODEL = #{model,jdbcType=VARCHAR},
      DOC_TITLE = #{docTitle,jdbcType=VARCHAR},
      THIRD_CATEGORY = #{thirdCategory,jdbcType=INTEGER},
      BRAND_ID = #{brandId,jdbcType=INTEGER},
      DOC_SUPPLIER_ID = #{docSupplierId,jdbcType=INTEGER},
      DOC_TEXT = #{docText,jdbcType=LONGVARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      `DISABLE` = #{disable,jdbcType=BOOLEAN},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      SORT_RULES = #{sortRules,jdbcType=INTEGER}
    where ID = #{id,jdbcType=INTEGER}
  </update>


    <select id="selectCoreSkuByPrimaryKey" resultType="java.lang.String">
        select SKU_NAME
        from V_CORE_SKU
        where STATUS = 1
          and SKU_ID = #{skuId,jdbcType=INTEGER}
        limit 1
    </select>

<!--auto generated by MybatisCodeHelper on 2022-02-15-->
  <select id="findBySkuIdAndDisableAndIsDelete" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_DOC_OF_GOODS
    where SKU_ID=#{skuId,jdbcType=INTEGER} and `DISABLE`=#{disable,jdbcType=BOOLEAN} and
    IS_DELETE=#{isDelete,jdbcType=BOOLEAN}
  </select>
</mapper>