$(function () {
    var contentLeftHtml = `<div class="left-wrap">
        <div class="cnt-title">选择商品</div>
        <div class="cnt-textarea-wrap">
            <textarea class="cnt-textarea input-textarea J-sku-push-textarea" placeholder="多个skuNo用‘,’隔开；excel可直接拷贝一列skuNo；"></textarea>
            <div class="search-wrap"><div class="btn btn-small J-sku-add">添加</div></div>
            <div class="nouse-tip J-nouse-tip" style="display:none;"><div class="nouse-txt"></div><i class="vd-icon icon-delete J-nouse-tip-del"></i></div>
        </div>
        <div class="cnt-select-label"><span>已选择：</span><span class="J-sku-selected-num">0</span>（最多1000条）</div>
        <div class="cnt-sku-list J-cnt-sku-list"></div>
    </div>`;

    var contentRightHtml = `<div class="right-wrap">
        <div class="cnt-title">选择区域商城</div>
        <div class="cnt-input-wrap">
            <input class="input-text J-sku-push-input"/>
            <div class="cnt-input-list J-sku-input-suggest" style="">
                <div class="cnt-list-item checked">
                    <div class="item-checkbox">
                        <i class="vd-icon icon-checkbox1"></i>
                        <i class="vd-icon icon-checkbox2"></i>
                    </div>
                    <div class="item-name">商城1</div>
                </div>
            </div>
        </div>
        <div class="cnt-select-label"><span>已选择：</span><span class="J-mall-selected-num">0</span></div>
        <div class="cnt-mall-list J-cnt-mall-list"></div>
    </div>`

    var contentHtml = `<div class="dlg-sku-push-wrap base-form J-sku-push-wrap">${contentLeftHtml}${contentRightHtml}<div>`;

    var checkTextareaVal = function () {
        var skuNosStr = $('.J-sku-push-textarea').val();
        
        $('.J-nouse-tip').hide();
        if (skuNosStr) {
            var valskuNos = skuNosStr.split(',');
            var skuNos = [];

            $.each(valskuNos, function(i, item){
                if(skuNos.indexOf(item) === -1){
                    skuNos.push(item);
                }
            })

            var skuUse = [];
            var skuNouse = [];

            $.each(skuNos, function (i, item) {
                if (!/^V\d{6}$/.test(item)) {
                    skuNouse.push(item);
                } else {
                    skuUse.push(item);
                }
            })

            if (skuNouse.length) {
                var nouseStr = `输入项中${skuNouse.join(',')}为无效的skuNo，已为您过滤。`
                $('.J-nouse-tip').show().find('.nouse-txt').html(nouseStr);
            }
            
            $('.J-sku-push-textarea').val(skuUse.join(','));
        }
    };

    $(document).on('click', '.J-nouse-tip-del', function () {
        $('.J-nouse-tip').hide();
    })

    $(document).on('blur', '.J-sku-push-textarea', function () {
        $(this).val($(this).val().trim().replace(/[\r\n\t\s]/g, ','));

        checkTextareaVal();
    })

    var malls = [];

    var getMallList = function () {
        $.ajax({
            url: page_url + '/vgoods/operate/getAllOrgList.do',
            dataType: 'json',
            success: function (res) {
                if (res.code == 0) {
                    malls = res.data;
                }
            }
        })
    }

    var mallsSelected = {};

    var initMallList = function () {
        var $list = $('.J-sku-push-wrap .J-sku-input-suggest');

        $list.empty();

        $.each(malls, function (i, item) {
            $list.append(`
                <div class="cnt-list-item J-cnt-list-item ${mallsSelected[item.orgId] ? 'checked' : ''}" data-item='${JSON.stringify(item)}'>
                    <div class="item-checkbox">
                        <i class="vd-icon icon-checkbox1"></i>
                        <i class="vd-icon icon-checkbox2"></i>
                    </div>
                    <div class="item-name" title="${item.orgName}">${item.orgName}</div>
                </div>
            `);
        })

        $list.show();
    };

    var searchMallList = function (val) {
        var $list = $('.J-sku-push-wrap .J-sku-input-suggest');

        $list.empty();

        var searchList = [];

        $.each(malls, function (i, item) {
            if (item.orgName.indexOf(val) !== -1) {
                searchList.push(item);
            }
        })

        if (searchList.length) {
            $.each(searchList, function (i, item) {
                $list.append(`
                <div class="cnt-list-item J-cnt-list-item ${mallsSelected[item.orgId] ? 'checked' : ''}" data-item='${JSON.stringify(item)}'>
                    <div class="item-checkbox">
                        <i class="vd-icon icon-checkbox1"></i>
                        <i class="vd-icon icon-checkbox2"></i>
                    </div>
                    <div class="item-name" title="${item.orgName}">${item.orgName}</div>
                </div>
            `);
            })
        } else {
            $list.append(`<div class="empty">无结果</div>`)
        }

        $list.show();
    };

    $(document).on('click', function () {
        $('.J-sku-push-wrap .J-sku-input-suggest').hide();
    })

    $(document).on('click', '.J-mall-item-del', function () {
        var id = $(this).data('id');
        mallsSelected[id] && delete mallsSelected[id];
        $(this).parents('.cnt-mall-item:first').remove();
        $('.J-mall-selected-num').html(Object.keys(mallsSelected).length);
    })

    var prodSelected = {};

    $(document).on('click', '.J-sku-add', function () {
        setTimeout(function () {
            var val = $('.J-sku-push-textarea').val();

            if (!val) {
                $('.J-sku-push-textarea').focus();
                return;
            }

            var skuNos = val.split(',');
            var flag = false;

            $.each(skuNos, function (i, item) {
                if(flag){
                    return;
                }

                if(Object.keys(prodSelected).length >= 1000){
                    GLOBAL.showGlobalTip('最多添加1000条', 'warn');
                    flag = true;
                    return;
                }

                if (!prodSelected[item]) {
                    $('.J-cnt-sku-list').append(`<div class="cnt-sku-item">
                    <div class="item-no">${item}</div>
                    <i class="vd-icon icon-delete J-sku-item-del" data-id="${item}"></i>
                </div>`);

                    prodSelected[item] = item;
                }
            })

            $('.J-sku-push-textarea').val('');
            $('.J-sku-selected-num').html(Object.keys(prodSelected).length);
        }, 100)
    })

    $(document).on('click', '.J-sku-item-del', function () {
        var id = $(this).data('id');
        prodSelected[id] && delete prodSelected[id];
        $(this).parents('.cnt-sku-item:first').remove();
        $('.J-sku-selected-num').html(Object.keys(prodSelected).length);
    })

    var checkMallSelected = function () {
        $('.J-cnt-mall-list').empty();

        for (var item in mallsSelected) {
            $('.J-cnt-mall-list').append(`<div class="cnt-mall-item">
                <div class="item-txt">${mallsSelected[item].orgName}</div>
                <i class="vd-icon icon-delete item-del J-mall-item-del" data-id="${item}"></i>
            </div>`)
        }

        $('.J-mall-selected-num').html(Object.keys(mallsSelected).length);
    };

    var addPushError = function (skuNo, txt) {
        $('.J-push-error-list').append(`<div class="push-error-item">
            <div class="error-skuNo">${skuNo}</div>
            <div class="error-reason">${txt}</div>
        </div>`);
    }

    var startPushSkus = function (skuNos, orgIds, index) {
        var pushUrl = page_url + `/vgoods/operate/pushGoodsInfo.do?spuId=&skuId=${skuNos[index].replace('V', '')}&platfromIds=16&`;
        var orgIdsStrs = [];

        $.each(orgIds, function (i, item) {
            orgIdsStrs.push('pushOrgIdList=' + item);
        })

        pushUrl += orgIdsStrs.join('&');

        $.ajax({
            url: pushUrl,
            dataType: 'json',
            success: function (res) {
                if (res.code == 0) {
                    $('.J-push-finished-num').html(parseInt($('.J-push-finished-num').html()) + 1);
                } else {
                    $('.J-push-error-num').html(parseInt($('.J-push-error-num').html()) + 1);
                    
                    var errorTxt = res.message || '推送失败';

                    if (res.code == -2 && res.listData && res.listData.length){
                        errorTxtLabels = [];
                        $.each(res.listData, function(i, item){
                            errorTxtLabels.push(item.serviceName);
                        })
                        errorTxt = `当前商品信息不满足推送条件！<br>待办事项如下:<br> ${errorTxtLabels.join('，')}<br>对应处理人：${res.data}<br>`
                    }
                    addPushError(skuNos[index], errorTxt);
                }
            },
            error: function () {
                $('.J-push-error-num').html(parseInt($('.J-push-error-num').html()) + 1);
                addPushError(skuNos[index], '接口异常')
            },
            complete: function () {
                if (index < skuNos.length - 1) {
                    startPushSkus(skuNos, orgIds, index + 1)
                } else {
                    $('.J-push-loading-txt').html('<div class="finish-txt">推送完成！</div>')
                    $('.J-push-loading-status').hide();
                    $('.J-push-loading-close').show();
                }
            }
        })
    };

    $('.J-push-loading-close').click(function () {
        $('.J-push-loading-wrap').hide();
    })

    var initloadingDialog = function (skuNos, orgIds) {
        $('.J-push-loading-wrap').css('display', 'flex');
        $('.J-push-loading-txt').html('推送中，请耐心等待。。。')
        $('.J-push-loading-status').show();
        $('.J-push-error-list').empty();
        $('.J-push-loading-close').hide();
        $('.J-push-finished-num, .J-push-error-num').html(0)

        $('.J-push-total-num').html(skuNos.length);

        startPushSkus(skuNos, orgIds, 0);
    };

    $('.J-multi-push-btn').click(function () {
        var dialog = new artDialog({
            title: '批量推送区域商城',
            content: contentHtml,
            init: function () {
                prodSelected = {};
                mallsSelected = {};
                
                $('.J-sku-push-wrap .cnt-input-wrap').on('focus', '.J-sku-push-input', function () {
                    var val = $(this).val().trim();

                    if (val) {
                        searchMallList(val);
                    } else {
                        initMallList();
                    }
                })

                $('.J-sku-push-wrap .cnt-input-wrap').on('input', '.J-sku-push-input', function () {
                    var val = $(this).val().trim();

                    if (val) {
                        searchMallList(val);
                    } else {
                        initMallList();
                    }
                })

                $('.J-sku-push-wrap .cnt-input-wrap').on('click', '.J-cnt-list-item', function (e) {
                    var item = $(this).data('item');

                    if ($(this).hasClass('checked')) {
                        $(this).removeClass('checked');
                        mallsSelected[item.orgId] && delete mallsSelected[item.orgId];
                    } else {
                        $(this).addClass('checked');
                        mallsSelected[item.orgId] = item;
                    }

                    checkMallSelected();
                })

                $('.J-sku-push-wrap .cnt-input-wrap').on('click', function (e) {
                    e.stopPropagation();
                })

                getMallList();

                // $('.J-sku-push-wrap .J-sku-push-input').on('click', function(e){
                //     e.stopPropagation();
                // })
            },
            width: 800,
            height: 500,
            button: [{
                name: '确认',
                highlight: true,
                callback: function () {
                    var skuNos = [];
                    for (var item in prodSelected) {
                        skuNos.push(item);
                    }

                    if (!skuNos.length) {
                        artDialog.alert('订货号不能为空');
                        return false;
                    }

                    var orgIds = [];
                    for (var item in mallsSelected) {
                        orgIds.push(item);
                    }

                    if (!orgIds.length) {
                        artDialog.alert('区域商城不能为空');
                        return false;
                    }

                    var tipDlg = artDialog.confirm('确定开始批量推送？', '', {
                        fn: function () {
                            initloadingDialog(skuNos, orgIds);
                            dialog.close();
                            prodSelected = {};
                            mallsSelected = {};
                        }, text: '提交'
                    }, {
                        fn: function () {
                            tipDlg.close();
                        }, text: '取消'
                    });

                    return false;
                }
            }, {
                name: '取消'
            }],
        })
    })



})