package com.vedeng.goods.service.impl;

import com.vedeng.goods.domain.entity.DepartmentsHospital;
import com.vedeng.goods.dto.DepartmentsHospitalDto;
import com.vedeng.goods.mapper.DepartmentsHospitalMapper;
import com.vedeng.goods.service.DepartmentsHospitalApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @description 对外提供科室信息服务
 * <AUTHOR>
 * @date 2022/7/11 17:00
 **/
@Service
@Slf4j
public class DepartmentsHospitalApiServiceImpl implements DepartmentsHospitalApiService {

    @Resource
    private DepartmentsHospitalMapper newDepartmentsHospitalMapper;

    @Override
    public List<DepartmentsHospitalDto> findAll() {

        List<DepartmentsHospitalDto> result = new ArrayList<>();
        List<DepartmentsHospital> all = newDepartmentsHospitalMapper.findAllNotDelete();
        if (!CollectionUtils.isEmpty(all)) {
            all.forEach(c->{
                DepartmentsHospitalDto data = new DepartmentsHospitalDto();
                BeanUtils.copyProperties(c, data);
                result.add(data);
            });
        }
        return result;
    }

    @Override
    public DepartmentsHospitalDto selectByPrimaryKey(Integer departmentId) {

        DepartmentsHospital departmentsHospital = newDepartmentsHospitalMapper.selectByPrimaryKey(departmentId);

        if (departmentsHospital == null) {
            return null;
        } else {
            DepartmentsHospitalDto data = new DepartmentsHospitalDto();
            BeanUtils.copyProperties(departmentsHospital, data);
            return data;
        }

    }
}
