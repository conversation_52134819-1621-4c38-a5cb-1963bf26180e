package com.vedeng.goods.manager.rule;

import com.vedeng.goods.manager.constants.GoodsValidConstants;
import com.vedeng.goods.model.CoreSkuGenerate;
import com.vedeng.goods.model.CoreSpuGenerate;
import lombok.Getter;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Getter
public enum GoodsValidTypeEnum {

    /**
     * SPU维度
     */
    SPU(1, "SPU信息录入校验规则", CoreSpuGenerate.class, false, GoodsValidConstants.SPU_VALID_RULES_KEY),

    /**
     * SKU维度
     */
    SKU(2, "SKU信息录入校验规则", CoreSkuGenerate.class, true, GoodsValidConstants.SKU_VALID_RULES_KEY);


    private Integer type;
    private String name;
    private Class<?> reflectClass;
    private Boolean hasAdditionalModule;
    private String apolloConfigKey;


    GoodsValidTypeEnum(Integer type, String name, Class<?> reflectClass, Boolean hasAdditionalModule, String apolloConfigKey) {
        this.type = type;
        this.name = name;
        this.reflectClass = reflectClass;
        this.hasAdditionalModule = hasAdditionalModule;
        this.apolloConfigKey = apolloConfigKey;
    }

    public static GoodsValidTypeEnum getByType(Integer type) {
        if (type == null) {
            return null;
        }

        for (GoodsValidTypeEnum value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }

        return null;
    }
}