package com.vedeng.erp.buyorder.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2023/1/10 13:12
 **/
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class OrderRemarkDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 类型 正向费用单 <br>
     * 1 预警 <br>
     * 2 自动转单原单 (e.g.该订单由销售单A中B订货号退货，联动采购费用单退货，已自动转单至C订单) <br>
     * 3 自动转单新单 (e.g.该订单由销售单A中B订货号退货，联动采购费用单C退货，自动转单生成）<br>
     * <br>
     * 类型 逆向费用售后单<br>
     * 1 自动创建售后单 (e.g.该订单由销售单A中B订货号退货，联动采购费用单C退货自动生成)<br>
     * 2 自动转单售后单 (e.g.该订单由销售单A中B订货号退货，联动采购费用单C退货，自动转单生成)<br>
     */
    private Integer type;

    private OriginalOrderDto  originalOrderDto;

    private ResultOrderDto resultOrderDto;
}