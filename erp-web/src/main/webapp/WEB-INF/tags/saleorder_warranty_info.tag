<%@ tag language="java" pageEncoding="UTF-8"%>
<%@attribute name="saleorder" type="com.vedeng.order.model.Saleorder" required="true" %>
<%@attribute name="goodsWarrantys" type="java.util.List" required="true" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>


<div class="parts content1" liname="录保卡" id="录保卡">
    <div class="title-container">
        <div class="table-title nobor">
            录保卡
        </div>
        <c:if test="${saleorder.status != 3}">
            <div class="title-click nobor addtitle" tabTitle='{"num":"allgoodswarranty<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
						"link":"./order/saleorder/allgoodswarranty.do?saleorderId=${saleorder.saleorderId}","title":"全部"}'>全部</div>
        </c:if>

    </div>
    <table class="table table-bordered table-striped table-condensed table-centered">
        <thead>
        <tr>
            <th class="table-small">产品名称</th>
            <th class="table-small">品牌</th>
            <th class="table-small">型号</th>
            <th class="table-small">贝登条码</th>
            <th class="table-small">厂商条码</th>
            <th class="table-small">录保卡时间</th>
            <th class="table-smallest10">操作</th>
        </tr>
        </thead>
        <tbody>
        <c:choose>
            <c:when test="${not empty goodsWarrantys }">
                <c:forEach items="${goodsWarrantys }" var="goodsWarranty">
                    <tr>
                        <td class="text-left">
                            <div class="brand-color1"><a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewgoods${goodsWarranty.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${goodsWarranty.goodsId}","title":"产品信息"}'>${goodsWarranty.goodsName }</a></div>
                            <div>${goodsWarranty.sku }</div>
                            <div>${goodsWarranty.materialCode }</div>
                        </td>
                        <td>${goodsWarranty.brandName }</td>
                        <td>${goodsWarranty.model }</td>
                        <td>${goodsWarranty.barcode }</td>
                        <td>${goodsWarranty.barcodeFactory }</td>
                        <td><date:date value ="${goodsWarranty.addTime}"/></td>
                        <td>
                					<span class="edit-user addtitle"
                                          tabTitle='{"num":"viewwarranty${goodsWarranty.saleorderGoodsWarrantyId }","link":"./order/saleorder/viewgoodswarrantyNew.do?saleorderGoodsWarrantyId=${goodsWarranty.saleorderGoodsWarrantyId}","title":"查看保卡"}'>查看</span>
                        </td>
                    </tr>
                </c:forEach>
            </c:when>
            <c:otherwise>
                <tr>
                    <td colspan="7">查询无结果！</td>
                </tr>
            </c:otherwise>
        </c:choose>
        </tbody>
    </table>
</div>
