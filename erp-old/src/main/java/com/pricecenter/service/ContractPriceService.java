package com.pricecenter.service;

import com.pricecenter.dto.*;

import java.util.List;

public interface ContractPriceService {

    //合约商品详情页
    PageResultDto<ContractPrice> findByPage(ContractPriceListPageQueryDto queryDto);
    //批量更改商品
    Boolean batchUpdateContract(List<BatchUpdateContractPriceDto> batchUpdateContractPriceDtos);
    //单个更该商品
    Boolean updateContractPrice(BatchUpdateContractPriceDto batchUpdateContractPriceDto);
    //查询所有商品
    PageResultDto<ContractPrice> findByPageAll(ContractPriceListPageQueryDto contractPriceListPageQueryDto);

    PageResultDto findCustomerIdByPage(CusomerInfoPageDto cusomerInfoPageDto);

    void batchUpdateTraderinfo(List<ContractTraderDto> traders);

    void batchAddContractPriceDtoList(List<BatchAddContractPriceDto> contractPriceDtoList);

    List<BatchContractPriceValidatorDto> validatorContractExsit(List<BatchContractPriceValidatorDto> validatorDtoList) throws Exception;

    List<BatchContractPriceValidatorDto> validatorContractNotExsit(List<BatchContractPriceValidatorDto> validatorDtoList) throws Exception;

    Boolean deleteContractPrice(DeleteContractPriceDto deleteContractPriceDto);
}
