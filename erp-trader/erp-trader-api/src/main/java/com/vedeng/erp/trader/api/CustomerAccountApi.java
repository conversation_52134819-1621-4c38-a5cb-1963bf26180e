package com.vedeng.erp.trader.api;

import com.vedeng.erp.trader.dto.CustomerAccountAssembleDto;
import com.vedeng.erp.trader.dto.CustomerAccountCreateDto;
import com.vedeng.erp.trader.dto.CustomerBankAccountApiDto;
import com.vedeng.erp.trader.dto.CustomerBankAccountQueryReqDto;
import com.vedeng.erp.trader.dto.CustomerBankAccountReqDto;

import java.util.List;

public interface CustomerAccountApi {

    /**
     * 售后提交根据回单创建客户账户
     */
    boolean create(CustomerAccountAssembleDto customerAccountAssembleDto);

    void insertOrUpdateCustomerAccount(CustomerBankAccountReqDto customerBankAccountApiDto);

    List<CustomerBankAccountApiDto> query(CustomerBankAccountQueryReqDto queryReqDto);

    /**
     * 根据客户id获取客户账户信息
     */
    List<CustomerBankAccountApiDto> findByTraderIdAndAccountType(Integer traderId,Integer accountType);

    boolean tryUpdateBankId(Integer traderId, String accountNo, Integer accountType, Integer bankId );

    void updateLastUseTime(Long customerBankAccountId);

}
