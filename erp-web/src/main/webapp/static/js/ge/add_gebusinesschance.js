//搜索报价单信息
function searchVdInfo() {
    var url =  './gesearchquote.do'
    $.ajax({
        type: 'POST',
        url: url,
        data: {showType: 1},
        dataType : "html",
        success: function(result) {
            var htmlCont = result;
            var open = layer.open({
                type: 1,
                title: '报价单号',
                shadeClose: false,
                area : ['1000px', '400px'],
                content: htmlCont,
                success: function(layer, index){

                }
            });
            $('#layerIndex').val(open);
        }
    });
}

//提交保存校验信息
function editSubmit(){
    $(".warning").remove();
    checkLogin();
    var $form = $("#geBusinessChanceForm");
    if($("#quoteorderNo").val() == ''){
        warnTips("quoteorderNo"," 请填写报价单");
        return false;
    }
    if($("#terminalTraderName").val() == ''){
        warnTips("terminalTraderName"," 请填写终端医院");
        return false;
    }
    if($("#hospitalType").val() == 0){
        warnTips("hospitalType"," 请填写医院性质");
        return false;
    }
    if($("#geBusinessChanceSource").val() == 0){
        warnTips("geBusinessChanceSource"," 请填写商机来源");
        return false;
    }
    if($("#goodsName").val() == ''){
        warnTips("goodsName"," 请填写意向型号");
        return false;
    }
    var province = $("#province :selected").val()=="0"?"":$("#province :selected").val();
    var provinceName = $("#province :selected").val()=="0"?"":$("#province :selected").text();
    var city = $("#city :selected").val();
    var cityName = $("#city :selected").text();
    var zone = $("#zone :selected").val();
    var zoneName = $("#zone :selected").text();
    $form.find("#salesAreaId").val(zone=="0"?(city=="0"?province:city):zone);
    $form.find("#salesArea").val(zoneName=="请选择"?(cityName=="请选择"?provinceName:provinceName + cityName):provinceName + cityName + zoneName);
    if(province == '' || province == 0){
        warnTips("zone"," 请填写所属地区");
        return false;
    }
    if(city == '' || city == 0){
        warnTips("zone"," 请填写所属市");
        return false;
    }
    if(zone == '' || zone == 0){
        warnTips("zone"," 请填写所属区");
        return false;
    }
    if($("#address").val() == ''){
        warnTips("address"," 请填写具体地址");
        return false;
    }
    $form.submit();
}

function closeTab(){
    window.parent.closableTab.close($(window.parent.document).find('[role=presentation].active .glyphicon.small'),window.parent.closableTab.resizeMove);
}