<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchROldInvoiceJNewInvoiceDtoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchROldInvoiceJNewInvoiceDto">
    <!--@mbg.generated-->
    <!--@Table T_R_OLD_INVOICE_J_NEW_INVOICE-->
    <id column="R_OLD_INVOICE_J_NEW_INVOICE_ID" jdbcType="INTEGER" property="rOldInvoiceJNewInvoiceId" />
    <result column="OLD_INVOICE_ID" jdbcType="INTEGER" property="oldInvoiceId" />
    <result column="NEW_INVOICE_ID" jdbcType="INTEGER" property="newInvoiceId" />
    <result column="INVOICE_AMOUNT" jdbcType="DECIMAL" property="invoiceAmount" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    R_OLD_INVOICE_J_NEW_INVOICE_ID, OLD_INVOICE_ID, NEW_INVOICE_ID, INVOICE_AMOUNT, IS_DELETE, 
    ADD_TIME, CREATOR, MOD_TIME, UPDATER
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_R_OLD_INVOICE_J_NEW_INVOICE
    where R_OLD_INVOICE_J_NEW_INVOICE_ID = #{rOldInvoiceJNewInvoiceId,jdbcType=INTEGER}
  </select>

    <select id="findByNewInvoiceId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_R_OLD_INVOICE_J_NEW_INVOICE
        where NEW_INVOICE_ID = #{newInvoiceId}
    </select>

<!--auto generated by MybatisCodeHelper on 2022-12-07-->
  <select id="findByNewInvoiceIds" resultType="java.lang.Integer">
        select
        OLD_INVOICE_ID
        from T_R_OLD_INVOICE_J_NEW_INVOICE
        where NEW_INVOICE_ID in
        <foreach collection="list" item="item" close=")" open="(" separator=",">
          #{item}
        </foreach>
        and IS_DELETE = 0
    </select>


    <!--auto generated by MybatisCodeHelper on 2022-12-07-->
</mapper>