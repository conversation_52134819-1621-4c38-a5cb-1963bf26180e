<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.trader.dao.TraderSupplierMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.trader.model.TraderSupplier">
    <id column="TRADER_SUPPLIER_ID" jdbcType="INTEGER" property="traderSupplierId" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
    <result column="PERIOD_AMOUNT" jdbcType="DECIMAL" property="periodAmount" />
    <result column="PERIOD_DAY" jdbcType="INTEGER" property="periodDay" />
    <result column="IS_ENABLE" jdbcType="BIT" property="isEnable" />
    <result column="IS_TOP" jdbcType="BIT" property="isTop" />
    <result column="SUPPLY_BRAND" jdbcType="VARCHAR" property="supplyBrand" />
    <result column="SUPPLY_PRODUCT" jdbcType="VARCHAR" property="supplyProduct" />
    <result column="GRADE" jdbcType="INTEGER" property="grade" />
    <result column="DISABLE_TIME" jdbcType="BIGINT" property="disableTime" />
    <result column="DISABLE_REASON" jdbcType="VARCHAR" property="disableReason" />
    <result column="COMMENTS" jdbcType="VARCHAR" property="comments" />
    <result column="BRIEF" jdbcType="VARCHAR" property="brief" />
    <result column="HOT_TELEPHONE" jdbcType="VARCHAR" property="hotTelephone" />
    <result column="SERVICE_TELEPHONE" jdbcType="VARCHAR" property="serviceTelephone" />
    <result column="LOGISTICS_NAME" jdbcType="VARCHAR" property="logisticsName" />
    <result column="WEBSITE" jdbcType="VARCHAR" property="website" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="AFTER_SALE_MANAGER" property="afterSaleManager" jdbcType="VARCHAR" />
    <result column="INSTALLSERVICE_CONTACTNAME" property="installServiceContactName" jdbcType="VARCHAR" />
    <result column="INSTALLSERVICE_CONTACTWAY" property="installServiceContactWay" jdbcType="VARCHAR" />
    <result column="TECHNICALDIRECT_CONTACTNAME" property="technicalDirectContactName" jdbcType="VARCHAR" />
    <result column="TECHNICALDIRECT_CONTACTWAY" property="technicalDirectContactWay" jdbcType="VARCHAR" />
    <result column="MAINTENANCE_CONTACTNAME" property="maintenanceContactName" jdbcType="VARCHAR" />
    <result column="MAINTENANCE_CONTACTWAY" property="maintenanceContactWay" jdbcType="VARCHAR" />
    <result column="EXCHANGE_CONTACTNAME" property="exchangeContactName" jdbcType="VARCHAR" />
    <result column="EXCHANGE_CONTACTWAY" property="exchangeContactWay" jdbcType="VARCHAR" />
    <result column="OTHER_CONTACTNAME" property="otherContactName" jdbcType="VARCHAR" />
    <result column="OTHER_CONTACTWAY" property="otherContactWay" jdbcType="VARCHAR" />
    <result column="TAX_PAYER_TYPE" property="taxPayerType" jdbcType="INTEGER" />
  </resultMap>

  <resultMap id="VoResultMap" type="com.vedeng.trader.model.vo.TraderSupplierVo" extends="BaseResultMap">
    <result column="TRADER_SUPPLIER_NAME" property="traderSupplierName" jdbcType="VARCHAR" />
    <result column="DISABLE_REASON" property="disableReason" jdbcType="VARCHAR" />
    <result column="TRADER_SUPPLIER_ADDRESS" property="traderSupplierAddress" jdbcType="VARCHAR" />
    <result column="TRADER_SUPPLIER_STATUS" property="traderSupplierStatus" jdbcType="INTEGER" />
    <result column="TRADER_SUPPLIER_BUY_COUNT" property="buyCount" jdbcType="INTEGER" />
    <result column="TRADER_SUPPLIER_BUY_MONEY" property="buyMoney" jdbcType="DECIMAL" />
    <result column="START_TIME" property="startTime" jdbcType="BIGINT" />
    <result column="END_TIME" property="endTime" jdbcType="BIGINT" />
    <result column="CONTACT_WAY" property="contactWay" jdbcType="VARCHAR" />
    <result column="AREA_ID" property="areaId" jdbcType="INTEGER"/>
    <result column="LAST_BUSSINESS_TIME" property="lastBussinessTime" jdbcType="BIGINT" />
    <result column="FIRST_BUSSINESS_TIME" property="firstBussinessTime" jdbcType="BIGINT" />

    <result column="USED_PERIOD_AMOUNT" property="usedPeriodAmount" jdbcType="DECIMAL" />
    <result column="USED_TIMES" property="usedTimes" jdbcType="INTEGER"/>
    <result column="CAPITAL_BILL_ID" property="capitalBillId" jdbcType="INTEGER" />
    <result column="TRADER_MODE" property="traderMode" jdbcType="INTEGER" />
    <result column="ORDER_PERIOD_AMOUNT" property="orderPeriodAmount" jdbcType="DECIMAL" />
    <result column="ORDER_PAYMENT_STATUS" property="orderPaymentStatus" jdbcType="INTEGER" />
    <result column="BUYORDER_ID" property="buyorderId" jdbcType="INTEGER" />
    <result column="LAST_VERIFY_USERNAME" property="lastVerifyUsername" jdbcType="VARCHAR" />
    <result column="VERIFY_USERNAME" property="verifyUsername" jdbcType="VARCHAR" />
    <result column="VERIFY_STATUS" property="verifyStatus" jdbcType="INTEGER" />
    <result column="ASSET" property="rebateAmount"  jdbcType="DECIMAL" />
  </resultMap>

  <resultMap type="com.vedeng.trader.model.vo.TraderSupplierVo" id="getTraderSupplierBaseInfoResult" extends="VoResultMap">
    <result column="supplierIsEnaale" property="isEnable" jdbcType="INTEGER" />
    <result column="WAREHOUSE_AREA_ID" property="warehouseAreaId" jdbcType="BIGINT" />
    <result column="WAREHOUSE_AREA_IDS" property="warehouseAreaIds" jdbcType="VARCHAR" />
    <result column="WAREHOUSE_ADDRESS" property="warehouseAddress" jdbcType="VARCHAR" />
    <result column="TRADER_TYPE" property="traderType" jdbcType="TINYINT" />
    <association property="trader" javaType="com.vedeng.trader.model.Trader">
      <id column="TRADER_ID" property="traderId" jdbcType="INTEGER" />
      <result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
      <result column="IS_ENABLE" property="isEnable" jdbcType="BIT" />
      <result column="TRADER_NAME" property="traderName" jdbcType="VARCHAR" />
      <result column="AREA_ID" property="areaId" jdbcType="INTEGER" />
      <result column="AREA_IDS" property="areaIds" jdbcType="VARCHAR" />
      <result column="ADDRESS" property="address" jdbcType="VARCHAR" />
      <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
      <result column="CREATOR" property="creator" jdbcType="INTEGER" />
      <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
      <result column="UPDATER" property="updater" jdbcType="INTEGER" />
      <result column="LAST_VALID_TIME" property="lastValidTime" jdbcType="TIMESTAMP" />
    </association>

    <collection property="traderSupplierSupplyBrands" ofType="com.vedeng.trader.model.TraderSupplierSupplyBrand" >
      <result column="BRAND_ID" property="brandId" jdbcType="INTEGER" />
      <association property="brand" javaType="com.vedeng.goods.model.Brand">
        <result column="BRAND_NAME" property="brandName" jdbcType="VARCHAR" />
      </association>
    </collection>
  </resultMap>

  <sql id="Base_Column_List">
    TRADER_SUPPLIER_ID, TRADER_ID, AMOUNT, PERIOD_AMOUNT, PERIOD_DAY, IS_ENABLE, IS_TOP, 
    SUPPLY_BRAND, SUPPLY_PRODUCT, GRADE, DISABLE_TIME, DISABLE_REASON, COMMENTS, BRIEF, 
    HOT_TELEPHONE, SERVICE_TELEPHONE, LOGISTICS_NAME, WEBSITE, ADD_TIME, CREATOR, MOD_TIME, 
    UPDATER,TRADER_TYPE,WAREHOUSE_AREA_ID,WAREHOUSE_AREA_IDS,WAREHOUSE_ADDRESS
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_TRADER_SUPPLIER
    where TRADER_SUPPLIER_ID = #{traderSupplierId,jdbcType=INTEGER}
  </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_TRADER_SUPPLIER
    where TRADER_SUPPLIER_ID = #{traderSupplierId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="TRADER_SUPPLIER_ID" keyProperty="traderSupplierId" parameterType="com.vedeng.trader.model.TraderSupplier" useGeneratedKeys="true">
    insert into T_TRADER_SUPPLIER (TRADER_ID, AMOUNT, PERIOD_AMOUNT, 
      PERIOD_DAY, IS_ENABLE, IS_TOP, 
      SUPPLY_BRAND, SUPPLY_PRODUCT, GRADE, 
      DISABLE_TIME, DISABLE_REASON, COMMENTS, 
      BRIEF, HOT_TELEPHONE, SERVICE_TELEPHONE, 
      LOGISTICS_NAME, WEBSITE, ADD_TIME, 
      CREATOR, MOD_TIME, UPDATER
      )
    values (#{traderId,jdbcType=INTEGER}, #{amount,jdbcType=DECIMAL}, #{periodAmount,jdbcType=DECIMAL}, 
      #{periodDay,jdbcType=INTEGER}, #{isEnable,jdbcType=BIT}, #{isTop,jdbcType=BIT}, 
      #{supplyBrand,jdbcType=VARCHAR}, #{supplyProduct,jdbcType=VARCHAR}, #{grade,jdbcType=INTEGER}, 
      #{disableTime,jdbcType=BIGINT}, #{disableReason,jdbcType=VARCHAR}, #{comments,jdbcType=VARCHAR}, 
      #{brief,jdbcType=VARCHAR}, #{hotTelephone,jdbcType=VARCHAR}, #{serviceTelephone,jdbcType=VARCHAR}, 
      #{logisticsName,jdbcType=VARCHAR}, #{website,jdbcType=VARCHAR}, #{addTime,jdbcType=BIGINT}, 
      #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="TRADER_SUPPLIER_ID" keyProperty="traderSupplierId" parameterType="com.vedeng.trader.model.TraderSupplier" useGeneratedKeys="true">
    insert into T_TRADER_SUPPLIER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="amount != null">
        AMOUNT,
      </if>
      <if test="periodAmount != null">
        PERIOD_AMOUNT,
      </if>
      <if test="periodDay != null">
        PERIOD_DAY,
      </if>
      <if test="isEnable != null">
        IS_ENABLE,
      </if>
      <if test="traderType != null">
        TRADER_TYPE,
      </if>
      <if test="isTop != null">
        IS_TOP,
      </if>
      <if test="supplyBrand != null">
        SUPPLY_BRAND,
      </if>
      <if test="supplyProduct != null">
        SUPPLY_PRODUCT,
      </if>
      <if test="grade != null">
        GRADE,
      </if>
      <if test="disableTime != null">
        DISABLE_TIME,
      </if>
      <if test="disableReason != null">
        DISABLE_REASON,
      </if>
      <if test="comments != null">
        COMMENTS,
      </if>
      <if test="brief != null">
        BRIEF,
      </if>
      <if test="hotTelephone != null">
        HOT_TELEPHONE,
      </if>
      <if test="serviceTelephone != null">
        SERVICE_TELEPHONE,
      </if>
      <if test="logisticsName != null">
        LOGISTICS_NAME,
      </if>
      <if test="website != null">
        WEBSITE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="periodAmount != null">
        #{periodAmount,jdbcType=DECIMAL},
      </if>
      <if test="periodDay != null">
        #{periodDay,jdbcType=INTEGER},
      </if>
      <if test="isEnable != null">
        #{isEnable,jdbcType=BIT},
      </if>
      <if test="traderType != null">
        #{traderType,jdbcType=BIT},
      </if>
      <if test="isTop != null">
        #{isTop,jdbcType=BIT},
      </if>
      <if test="supplyBrand != null">
        #{supplyBrand,jdbcType=VARCHAR},
      </if>
      <if test="supplyProduct != null">
        #{supplyProduct,jdbcType=VARCHAR},
      </if>
      <if test="grade != null">
        #{grade,jdbcType=INTEGER},
      </if>
      <if test="disableTime != null">
        #{disableTime,jdbcType=BIGINT},
      </if>
      <if test="disableReason != null">
        #{disableReason,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="brief != null">
        #{brief,jdbcType=VARCHAR},
      </if>
      <if test="hotTelephone != null">
        #{hotTelephone,jdbcType=VARCHAR},
      </if>
      <if test="serviceTelephone != null">
        #{serviceTelephone,jdbcType=VARCHAR},
      </if>
      <if test="logisticsName != null">
        #{logisticsName,jdbcType=VARCHAR},
      </if>
      <if test="website != null">
        #{website,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.trader.model.TraderSupplier">
    update T_TRADER_SUPPLIER
    <set>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="periodAmount != null">
        PERIOD_AMOUNT = #{periodAmount,jdbcType=DECIMAL},
      </if>
      <if test="periodDay != null">
        PERIOD_DAY = #{periodDay,jdbcType=INTEGER},
      </if>
      <if test="isEnable != null">
        IS_ENABLE = #{isEnable,jdbcType=BIT},
      </if>
      <if test="isTop != null">
        IS_TOP = #{isTop,jdbcType=BIT},
      </if>
      <if test="supplyBrand != null">
        SUPPLY_BRAND = #{supplyBrand,jdbcType=VARCHAR},
      </if>
      <if test="supplyProduct != null">
        SUPPLY_PRODUCT = #{supplyProduct,jdbcType=VARCHAR},
      </if>
      <if test="grade != null">
        GRADE = #{grade,jdbcType=INTEGER},
      </if>
      <if test="disableTime != null">
        DISABLE_TIME = #{disableTime,jdbcType=BIGINT},
      </if>
      <if test="disableReason != null">
        DISABLE_REASON = #{disableReason,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="brief != null">
        BRIEF = #{brief,jdbcType=VARCHAR},
      </if>
      <if test="hotTelephone != null">
        HOT_TELEPHONE = #{hotTelephone,jdbcType=VARCHAR},
      </if>
      <if test="serviceTelephone != null">
        SERVICE_TELEPHONE = #{serviceTelephone,jdbcType=VARCHAR},
      </if>
      <if test="logisticsName != null">
        LOGISTICS_NAME = #{logisticsName,jdbcType=VARCHAR},
      </if>
      <if test="website != null">
        WEBSITE = #{website,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where TRADER_SUPPLIER_ID = #{traderSupplierId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.trader.model.TraderSupplier">
    update T_TRADER_SUPPLIER
    set TRADER_ID = #{traderId,jdbcType=INTEGER},
      AMOUNT = #{amount,jdbcType=DECIMAL},
      PERIOD_AMOUNT = #{periodAmount,jdbcType=DECIMAL},
      PERIOD_DAY = #{periodDay,jdbcType=INTEGER},
      IS_ENABLE = #{isEnable,jdbcType=BIT},
      IS_TOP = #{isTop,jdbcType=BIT},
      SUPPLY_BRAND = #{supplyBrand,jdbcType=VARCHAR},
      SUPPLY_PRODUCT = #{supplyProduct,jdbcType=VARCHAR},
      GRADE = #{grade,jdbcType=INTEGER},
      DISABLE_TIME = #{disableTime,jdbcType=BIGINT},
      DISABLE_REASON = #{disableReason,jdbcType=VARCHAR},
      COMMENTS = #{comments,jdbcType=VARCHAR},
      BRIEF = #{brief,jdbcType=VARCHAR},
      HOT_TELEPHONE = #{hotTelephone,jdbcType=VARCHAR},
      SERVICE_TELEPHONE = #{serviceTelephone,jdbcType=VARCHAR},
      LOGISTICS_NAME = #{logisticsName,jdbcType=VARCHAR},
      WEBSITE = #{website,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER}
    where TRADER_SUPPLIER_ID = #{traderSupplierId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeySelectiveDB" parameterType="com.vedeng.trader.model.TraderSupplier" >
    update T_TRADER_SUPPLIER
    <set >
      <if test="traderId != null" >
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="amount != null" >
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="periodAmount != null" >
        PERIOD_AMOUNT = #{periodAmount,jdbcType=DECIMAL},
      </if>
      <if test="periodDay != null" >
        PERIOD_DAY = #{periodDay,jdbcType=INTEGER},
      </if>
      <if test="isEnable != null" >
        IS_ENABLE = #{isEnable,jdbcType=BIT},
      </if>
      <if test="isTop != null" >
        IS_TOP = #{isTop,jdbcType=BIT},
      </if>
      <if test="supplyBrand != null and supplyBrand != ''" >
        SUPPLY_BRAND = #{supplyBrand,jdbcType=VARCHAR},
      </if>
      <if test="supplyProduct != null and supplyProduct != ''" >
        SUPPLY_PRODUCT = #{supplyProduct,jdbcType=VARCHAR},
      </if>
      <if test="grade != null" >
        GRADE = #{grade,jdbcType=INTEGER},
      </if>
      <if test="disableTime != null" >
        DISABLE_TIME = #{disableTime,jdbcType=BIGINT},
      </if>
      <if test="disableReason != null and disableReason != ''" >
        DISABLE_REASON = #{disableReason,jdbcType=VARCHAR},
      </if>
      <if test="comments != null and comments != ''" >
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="brief != null and brief != ''" >
        BRIEF = #{brief,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="hotTelephone != null " >
        HOT_TELEPHONE = #{hotTelephone,jdbcType=VARCHAR},
      </if>
      <if test="serviceTelephone != null " >
        SERVICE_TELEPHONE = #{serviceTelephone,jdbcType=VARCHAR},
      </if>
      <if test="logisticsName != null " >
        LOGISTICS_NAME = #{logisticsName,jdbcType=VARCHAR},
      </if>
      <if test="website != null " >
        WEBSITE = #{website,jdbcType=VARCHAR},
      </if>

      <if test="afterSaleManager != null " >
        AFTER_SALE_MANAGER = #{afterSaleManager,jdbcType=VARCHAR},
      </if>
      <if test="installServiceContactName != null " >
        INSTALLSERVICE_CONTACTNAME = #{installServiceContactName,jdbcType=VARCHAR},
      </if>
      <if test="installServiceContactWay != null " >
        INSTALLSERVICE_CONTACTWAY = #{installServiceContactWay,jdbcType=VARCHAR},
      </if>
      <if test="technicalDirectContactName != null " >
        TECHNICALDIRECT_CONTACTNAME = #{technicalDirectContactName,jdbcType=VARCHAR},
      </if>
      <if test="technicalDirectContactWay != null " >
        TECHNICALDIRECT_CONTACTWAY = #{technicalDirectContactWay,jdbcType=VARCHAR},
      </if>
      <if test="maintenanceContactName != null " >
        MAINTENANCE_CONTACTNAME = #{maintenanceContactName,jdbcType=VARCHAR},
      </if>
      <if test="maintenanceContactWay != null " >
        MAINTENANCE_CONTACTWAY = #{maintenanceContactWay,jdbcType=VARCHAR},
      </if>
      <if test="exchangeContactName != null " >
        EXCHANGE_CONTACTNAME = #{exchangeContactName,jdbcType=VARCHAR},
      </if>
      <if test="exchangeContactWay != null " >
        EXCHANGE_CONTACTWAY = #{exchangeContactWay,jdbcType=VARCHAR},
      </if>
      <if test="otherContactName != null " >
        OTHER_CONTACTNAME = #{otherContactName,jdbcType=VARCHAR},
      </if>
      <if test="otherContactWay != null " >
        OTHER_CONTACTWAY = #{otherContactWay,jdbcType=VARCHAR},
      </if>
      <if test="warehouseAreaId != null " >
        WAREHOUSE_AREA_ID = #{warehouseAreaId,jdbcType=INTEGER},
      </if>
      <if test="warehouseAreaIds != null " >
        WAREHOUSE_AREA_IDS = #{warehouseAreaIds,jdbcType=VARCHAR},
      </if>
      <if test="warehouseAddress != null " >
        WAREHOUSE_ADDRESS = #{warehouseAddress,jdbcType=VARCHAR},
      </if>
      <if test="traderType != null " >
        TRADER_TYPE = #{traderType,jdbcType=TINYINT},
      </if>
      <!--
      <result column="HOT_TELEPHONE" property="hotTelephone" jdbcType="VARCHAR" />
	    <result column="SERVICE_TELEPHONE" property="serviceTelephone" jdbcType="VARCHAR" />
	    <result column="LOGISTICS_NAME" property="logisticsName" jdbcType="VARCHAR" />
	    <result column="WEBSITE" property="website" jdbcType="VARCHAR" />
       -->

    </set>
    where TRADER_SUPPLIER_ID = #{traderSupplierId,jdbcType=INTEGER}
  </update>
  <select id="getSuplierInfoByTraderId" resultType="com.vedeng.trader.model.TraderSupplier">
    	SELECT B.*
    	FROM T_TRADER A
    	LEFT JOIN T_TRADER_SUPPLIER B ON A.TRADER_ID=B.TRADER_ID
    	WHERE A.TRADER_ID=#{traderId,jdbcType=INTEGER}
  </select>

  <select id="getSupplierInfoByTraderName" resultType="com.vedeng.trader.model.TraderSupplier">
    	SELECT B.*,A.*
    	FROM T_TRADER A
    	INNER JOIN T_TRADER_SUPPLIER B ON A.TRADER_ID= B.TRADER_ID
    	WHERE A.TRADER_NAME = #{traderName,jdbcType=VARCHAR} AND B.TRADER_TYPE = 1 and A.COMPANY_ID = 1
  </select>

  <select id="getTraderSupplierByTraderId" resultType="com.vedeng.trader.model.vo.TraderVo">
    SELECT
	U.USERNAME,U.USER_ID,T.*
    FROM
	T_TRADER T
	LEFT JOIN T_R_TRADER_J_USER TU ON TU.TRADER_ID=T.TRADER_ID AND TU.TRADER_TYPE=2
	LEFT JOIN T_USER U ON U.USER_ID=TU.USER_ID
	WHERE T.TRADER_ID=#{traderId,jdbcType=INTEGER}
  </select>

  <select id="getTraderIdListByContactWay" parameterType="com.vedeng.trader.model.TraderContact" resultType="java.lang.Integer">
    select
    TRADER_ID
    from
    T_TRADER_CONTACT
    where
    1=1
    <if test="traderType != null and traderType != 0">
      and TRADER_TYPE = #{traderType,jdbcType=BIT}
    </if>
    <if test="mobile != null and mobile != ''">
      and (TELEPHONE like CONCAT('%',#{mobile},'%' )
      or MOBILE like CONCAT('%',#{mobile},'%' )
      or MOBILE2 like CONCAT('%',#{mobile},'%' )
      or QQ like CONCAT('%',#{mobile},'%' )
      or WEIXIN like CONCAT('%',#{mobile},'%' )
      or EMAIL like CONCAT('%',#{mobile},'%' )
      )
    </if>
limit 1000
  </select>
  <select id="getTraderSupplierListQualificationExpirationListPage"
          parameterType="Map" resultMap="VoResultMap">
    SELECT ts.TRADER_SUPPLIER_ID,ts.TRADER_ID ,t.TRADER_NAME as TRADER_SUPPLIER_NAME,ts.COMMENTS,ts.PERIOD_AMOUNT,ts.PERIOD_DAY,
    ts.IS_ENABLE,ts.IS_TOP,ts.GRADE,ts.ADD_TIME,ts.MOD_TIME,t.AREA_ID, t.COMPANY_ID,a.VERIFY_USERNAME,a.STATUS as VERIFY_STATUS,ts.TRADER_TYPE, ts.TAX_PAYER_TYPE
    from  T_TRADER_SUPPLIER ts
    LEFT JOIN  T_TRADER t ON t.TRADER_ID = ts.TRADER_ID
    LEFT JOIN T_TRADER_CERTIFICATE ttc ON ts.TRADER_ID = ttc.TRADER_ID
    LEFT JOIN  T_VERIFIES_INFO a ON a.RELATE_TABLE_KEY = ts.TRADER_SUPPLIER_ID AND a.RELATE_TABLE = 'T_TRADER_SUPPLIER' and a.VERIFIES_TYPE = 619
    where 1=1
    and ttc.SYS_OPTION_DEFINITION_ID in (25, 28, 29, 439, 1101, 1102, 897, 898, 1100)
    and (ttc.ENDTIME >0)
    and ttc.IS_DELETE = 0
    and ttc.TRADER_TYPE = 2
    and ttc.ENDTIME <![CDATA[<]]> UNIX_TIMESTAMP() * 1000
    and ttc.TRADER_ID is not null
    <if test="traderSupplierVo.traderType !=null and traderSupplierVo.traderType !=0 ">
      and ts.TRADER_TYPE = #{traderSupplierVo.traderType}
    </if>
      and ts.IS_ENABLE = 1
    <if test="traderSupplierVo.traderSupplierStatus !=null and traderSupplierVo.traderSupplierStatus ==0">
      and a.STATUS = 0
    </if>
    <if test="traderSupplierVo.traderSupplierStatus !=null and traderSupplierVo.traderSupplierStatus ==1">
      and a.STATUS = 1
    </if>
    <if test="traderSupplierVo.traderSupplierStatus !=null and traderSupplierVo.traderSupplierStatus ==2">
      and a.STATUS = 2
    </if>
    <if test="traderSupplierVo.traderSupplierStatus !=null and traderSupplierVo.traderSupplierStatus ==3">
      and (ISNULL(a.STATUS) or a.STATUS = 3)
    </if>
    <if test="traderSupplierVo.companyId !=null">
      and t.COMPANY_ID = #{traderSupplierVo.companyId}
    </if>
    <if test="traderSupplierVo.traderSupplierName !=null and traderSupplierVo.traderSupplierName !='' ">
      and t.TRADER_NAME like CONCAT('%',#{traderSupplierVo.traderSupplierName},'%' )
    </if>
    <if test="traderSupplierVo.userIdList !=null ">
      and t.TRADER_ID in (
      select TRADER_ID from T_R_TRADER_J_USER  where TRADER_TYPE =2
      and USER_ID in
      <foreach collection="traderSupplierVo.userIdList" item="userId" index="index"
               open="(" close=")" separator=",">
        #{userId}
      </foreach>
      )
    </if>
    <if test="traderSupplierVo.searchTraderIds !=null ">
      and t.TRADER_ID  in
      <foreach collection="traderSupplierVo.searchTraderIds" item="traderId" index="index"
               open="(" close=")" separator=",">
        #{traderId}
      </foreach>
    </if>
    <if test="traderSupplierVo.traderList !=null ">
      and t.TRADER_ID  in
      <foreach collection="traderSupplierVo.traderList" item="traderId" index="index"
               open="(" close=")" separator=",">
        #{traderId}
      </foreach>
    </if>
    <if test="traderSupplierVo.timeType ==1">
      <if test="traderSupplierVo.startTime != null">
        and ts.ADD_TIME <![CDATA[>=]]> #{traderSupplierVo.startTime}
      </if>
      <if test="traderSupplierVo.endTime != null">
        and ts.ADD_TIME <![CDATA[<=]]> #{traderSupplierVo.endTime}
      </if>
    </if>
    group by ts.TRADER_ID
    ORDER BY ts.IS_TOP DESC,ts.IS_ENABLE DESC,ts.MOD_TIME DESC
  </select>
  <!-- 获取供应商分页信息     联系方式查询使用union  -->
  <select id="getTraderSupplierVolistpage" parameterType="Map" resultMap="VoResultMap">
    SELECT ts.TRADER_SUPPLIER_ID,ts.TRADER_ID ,t.TRADER_NAME as TRADER_SUPPLIER_NAME,ts.COMMENTS,ts.PERIOD_AMOUNT,ts.PERIOD_DAY,ts.DISABLE_REASON,
    ts.IS_ENABLE,ts.IS_TOP,ts.GRADE,ts.ADD_TIME,ts.MOD_TIME,t.AREA_ID, t.COMPANY_ID,a.VERIFY_USERNAME,a.STATUS as VERIFY_STATUS,ts.TRADER_TYPE, ts.TAX_PAYER_TYPE
      ,CNT.TRADER_SUPPLIER_BUY_COUNT, CNT.LAST_BUSSINESS_TIME, CNT.FIRST_BUSSINESS_TIME, CNT.TRADER_SUPPLIER_BUY_MONEY
    ,ts.AMOUNT,tsa.ASSET
    from  T_TRADER_SUPPLIER ts
    LEFT JOIN T_SUPPLIER_ASSET tsa ON ts.TRADER_SUPPLIER_ID = tsa.TRADER_SUPPLIER_ID AND tsa.ASSET_TYPE = 1
    LEFT JOIN  T_TRADER t ON t.TRADER_ID = ts.TRADER_ID
    LEFT JOIN  T_VERIFIES_INFO a ON a.RELATE_TABLE_KEY = ts.TRADER_SUPPLIER_ID AND a.RELATE_TABLE = 'T_TRADER_SUPPLIER' and a.VERIFIES_TYPE = 619
    LEFT JOIN (
      select
      count(*) as TRADER_SUPPLIER_BUY_COUNT,max(VALID_TIME) as LAST_BUSSINESS_TIME,
      min(VALID_TIME) as FIRST_BUSSINESS_TIME,sum(TOTAL_AMOUNT) as TRADER_SUPPLIER_BUY_MONEY,a.TRADER_ID
      from
      T_BUYORDER a
      where
      a.VALID_STATUS = 1
      AND a.PAYMENT_STATUS = 2	<!-- 付款状态 0未付款 1部分付款 2全部付款 -->
      group by a.TRADER_ID
    )  CNT ON t.TRADER_ID = CNT.TRADER_ID
    where 1=1
      <if test="traderSupplierVo.cooperate !=null and traderSupplierVo.cooperate == 1 ">
          and CNT.TRADER_SUPPLIER_BUY_COUNT >0
      </if>
      <if test="traderSupplierVo.cooperate !=null and traderSupplierVo.cooperate !='' and traderSupplierVo.cooperate == 0 ">
          and CNT.TRADER_SUPPLIER_BUY_COUNT IS NULL
      </if>
      <if test="traderSupplierVo.traderType !=null and traderSupplierVo.traderType !=0 ">
      and ts.TRADER_TYPE = #{traderSupplierVo.traderType}
    </if>
    <if test="traderSupplierVo.isEnable !=null">
      and ts.IS_ENABLE = #{traderSupplierVo.isEnable}
    </if>
    <if test="traderSupplierVo.traderSupplierStatus !=null and traderSupplierVo.traderSupplierStatus ==0">
      and a.STATUS = 0
    </if>
    <if test="traderSupplierVo.traderSupplierStatus !=null and traderSupplierVo.traderSupplierStatus ==1">
      and a.STATUS = 1
    </if>
    <if test="traderSupplierVo.traderSupplierStatus !=null and traderSupplierVo.traderSupplierStatus ==2">
      and a.STATUS = 2
    </if>
    <if test="traderSupplierVo.traderSupplierStatus !=null and traderSupplierVo.traderSupplierStatus ==3">
      and (ISNULL(a.STATUS) or a.STATUS = 3)
    </if>
    <if test="traderSupplierVo.companyId !=null">
      and t.COMPANY_ID = #{traderSupplierVo.companyId}
    </if>
    <if test="traderSupplierVo.traderSupplierName !=null and traderSupplierVo.traderSupplierName !='' ">
      and t.TRADER_NAME like CONCAT('%',#{traderSupplierVo.traderSupplierName},'%' )
    </if>
    <if test="traderSupplierVo.areaId != null">
      and FIND_IN_SET(#{traderSupplierVo.areaId},t.AREA_IDS)
    </if>
    <if test="traderSupplierVo.userIdList !=null ">
      and t.TRADER_ID in (
      select TRADER_ID from T_R_TRADER_J_USER  where TRADER_TYPE =2
      and USER_ID in
      <foreach collection="traderSupplierVo.userIdList" item="userId" index="index"
               open="(" close=")" separator=",">
        #{userId}
      </foreach>
      )

    </if>
    <if test="traderSupplierVo.searchTraderIds !=null ">
      and t.TRADER_ID  in
      <foreach collection="traderSupplierVo.searchTraderIds" item="traderId" index="index"
               open="(" close=")" separator=",">
        #{traderId}
      </foreach>
    </if>
    <if test="traderSupplierVo.traderTimeList !=null ">
      and t.TRADER_ID  in
      <foreach collection="traderSupplierVo.traderTimeList" item="traderId" index="index"
               open="(" close=")" separator=",">
        #{traderId}
      </foreach>
    </if>
    <if test="traderSupplierVo.traderList !=null ">
      and t.TRADER_ID  in
      <foreach collection="traderSupplierVo.traderList" item="traderId" index="index"
               open="(" close=")" separator=",">
        #{traderId}
      </foreach>
    </if>
    <if test="traderSupplierVo.communicateTraderIds != null">
      and t.TRADER_ID  in
      <foreach collection="traderSupplierVo.communicateTraderIds" item="traderId" index="index"
               open="(" close=")" separator=",">
        #{traderId}
      </foreach>
    </if>
    <if test="traderSupplierVo.contactTraderIds != null">
      and t.TRADER_ID  in
      <foreach collection="traderSupplierVo.contactTraderIds" item="traderId" index="index"
               open="(" close=")" separator=",">
        #{traderId}
      </foreach>
    </if>
    <if test="traderSupplierVo.cooperateTraderIds != null">
      and t.TRADER_ID  in
      <foreach collection="traderSupplierVo.cooperateTraderIds" item="traderId" index="index"
               open="(" close=")" separator=",">
        #{traderId}
      </foreach>
    </if>
    <if test="traderSupplierVo.noCooperateTraderIds != null">
      and t.TRADER_ID not in
      <foreach collection="traderSupplierVo.noCooperateTraderIds" item="traderId" index="index"
               open="(" close=")" separator=",">
        #{traderId}
      </foreach>
    </if>
    <if test="traderSupplierVo.grade !=null and traderSupplierVo.grade != -1">
      and ts.GRADE = #{traderSupplierVo.grade}
    </if>
    <if test="traderSupplierVo.timeType ==1">
      <if test="traderSupplierVo.startTime != null">
        and ts.ADD_TIME <![CDATA[>=]]> #{traderSupplierVo.startTime}
      </if>
      <if test="traderSupplierVo.endTime != null">
        and ts.ADD_TIME <![CDATA[<=]]> #{traderSupplierVo.endTime}
      </if>
    </if>
    <if test="traderSupplierVo.timeType ==2">
      <if test="traderSupplierVo.startTime != null">

      </if>
      <if test="traderSupplierVo.endTime != null">

      </if>
    </if>
    <if test="traderSupplierVo.timeType ==3">
      <if test="traderSupplierVo.startTime != null">
        and ts.MOD_TIME <![CDATA[>=]]> #{traderSupplierVo.startTime}
      </if>
      <if test="traderSupplierVo.endTime != null">
        and ts.MOD_TIME <![CDATA[<=]]> #{traderSupplierVo.endTime}
      </if>
    </if>
    <if test="traderSupplierVo.taxPayerType != null">
      and ts.TAX_PAYER_TYPE = #{traderSupplierVo.taxPayerType}
    </if>
    <if test="traderSupplierVo.amount == 0">
      and (ts.AMOUNT = 0 or ts.AMOUNT is null)
    </if>
    <if test="traderSupplierVo.amount == 1">
      and ts.AMOUNT != 0
    </if>
    <if test="traderSupplierVo.rebateAmount == 0">
      and (tsa.ASSET = 0 or tsa.ASSET is null)
    </if>
    <if test="traderSupplierVo.rebateAmount == 1">
      and tsa.ASSET != 0
    </if>
    ORDER BY ts.IS_TOP DESC,ts.IS_ENABLE DESC,ts.MOD_TIME DESC
  </select>


  <select id="batchTraderBussinessData" parameterType="com.vedeng.trader.model.vo.TraderSupplierVo" resultMap="VoResultMap">
    select
      count(*) as TRADER_SUPPLIER_BUY_COUNT,max(VALID_TIME) as LAST_BUSSINESS_TIME,
      min(VALID_TIME) as FIRST_BUSSINESS_TIME,sum(TOTAL_AMOUNT) as TRADER_SUPPLIER_BUY_MONEY,a.TRADER_ID
      from
      T_BUYORDER a
      where
      a.VALID_STATUS = 1
      AND a.PAYMENT_STATUS = 2	<!-- 付款状态 0未付款 1部分付款 2全部付款 -->
    AND a.TRADER_ID in
    <foreach collection="traderSupplierVos" item="tsv" index="index" open="(" close=")" separator=",">
      #{tsv.traderId}
    </foreach>
    group by a.TRADER_ID
  </select>

  <select id="getTraderSupplierAccountPeriodInfo" parameterType="java.lang.Integer" resultMap="VoResultMap">
  	select
		a.PERIOD_AMOUNT,a.PERIOD_DAY,a.TRADER_ID,
		b.BUYORDER_ID
	from
		T_TRADER_SUPPLIER a
	left join
		T_BUYORDER b
	on
		a.TRADER_ID = b.TRADER_ID
	and
		b.HAVE_ACCOUNT_PERIOD = 1
	and
		b.STATUS != 3
	left join
		T_CAPITAL_BILL_DETAIL c
	on
		c.RELATED_ID = b.BUYORDER_ID
	and
		c.ORDER_TYPE = 2
	left join
		T_CAPITAL_BILL d
	on
		c.CAPITAL_BILL_ID = d.CAPITAL_BILL_ID
	and
		d.TRADER_MODE = 533
	where
		a.TRADER_ID = #{traderId,jdbcType=INTEGER}
	group by
		b.BUYORDER_ID
  </select>


  <select id="getSupplierBuyorderInfo" parameterType="java.lang.Integer" resultMap="VoResultMap">
    select
    count(*) as TRADER_SUPPLIER_BUY_COUNT,max(VALID_TIME) as LAST_BUSSINESS_TIME,min(VALID_TIME) as FIRST_BUSSINESS_TIME,sum(TOTAL_AMOUNT) as TRADER_SUPPLIER_BUY_MONEY
    from
    T_BUYORDER a
    where
    a.VALID_STATUS = 1
    AND a.PAYMENT_STATUS = 2 <!-- 付款状态 0未付款 1部分付款 2全部付款 -->
    AND a.TRADER_ID = #{traderId,jdbcType=INTEGER}
  </select>

  <select id="getTraderSupplierUsedAccountPeriodAmount" parameterType="java.lang.Integer" resultType="java.math.BigDecimal">
  	SELECT
		COALESCE (
			sum(ABS(a.AMOUNT)) - IFNULL(c.hk_amount, 0) - IFNULL(d.txy_amount, 0),
			0
		)
	FROM
		T_CAPITAL_BILL a
	LEFT JOIN T_CAPITAL_BILL_DETAIL b ON a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
	LEFT JOIN (
		SELECT
			COALESCE (sum(ABS(b1.AMOUNT)), 0) AS hk_amount,
			b1.TRADER_ID
		FROM
			T_CAPITAL_BILL a1
		LEFT JOIN T_CAPITAL_BILL_DETAIL b1 ON a1.CAPITAL_BILL_ID = b1.CAPITAL_BILL_ID
		WHERE
			a1.TRADER_TYPE = 2
		AND b1.ORDER_TYPE = 2
		AND b1.BUSSINESS_TYPE = 533
		AND b1.TRADER_TYPE = 2
		GROUP BY
			b1.TRADER_ID
	) AS c ON b.TRADER_ID = c.TRADER_ID
	LEFT JOIN (
		SELECT
			COALESCE (sum(ABS(b1.AMOUNT)), 0) AS txy_amount,
			b1.TRADER_ID
		FROM
			T_CAPITAL_BILL a1
		LEFT JOIN T_CAPITAL_BILL_DETAIL b1 ON a1.CAPITAL_BILL_ID = b1.CAPITAL_BILL_ID
		WHERE
			a1.TRADER_TYPE = 3
		AND b1.ORDER_TYPE = 3
		AND b1.BUSSINESS_TYPE = 533
		AND b1.TRADER_TYPE = 2
		GROUP BY
			b1.TRADER_ID
	) AS d ON d.TRADER_ID = b.TRADER_ID
	WHERE
		a.TRADER_TYPE = 3
	AND a.TRADER_MODE = 527
	AND b.ORDER_TYPE = 2
	AND b.TRADER_TYPE = 2
	AND b.TRADER_ID =#{traderId,jdbcType=INTEGER}
  </select>

  <select id="getTraderSupplierZYAccountPeriodAmount" parameterType="java.lang.Integer" resultType="java.math.BigDecimal">
  	select
			COALESCE(sum(ABS(ACCOUNT_PERIOD_AMOUNT)),0)
		from
			T_BUYORDER
		where
			TRADER_ID = #{traderId,jdbcType=INTEGER}
		and
			HAVE_ACCOUNT_PERIOD = 1
		and
			PAYMENT_STATUS in (0,1)
		and
			STATUS != 3
  </select>

  <select id="getTraderSupplierCGFYZYAccountPeriodAmount" resultType="java.math.BigDecimal">
        select COALESCE(sum(ABS(TBED.ACCOUNT_PERIOD_AMOUNT)),0)
    from T_BUYORDER_EXPENSE TBE left join T_BUYORDER_EXPENSE_DETAIL TBED on TBE.BUYORDER_ID = TBED.BUYORDER_EXPENSE_ID
    where
        TBED.TRADER_ID = #{traderId,jdbcType=INTEGER}
    and TBE.STATUS  != 3

  </select>

  <select id="getTraderSupplierAccountPeriodTimes" parameterType="java.lang.Integer" resultType="java.lang.Integer">
  	select
	  		count(*)
	  	from
	  		T_CAPITAL_BILL a
	  	left join
			T_CAPITAL_BILL_DETAIL b
		on
			a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
		left join
			T_BUYORDER c
		on
			c.BUYORDER_ID = b.RELATED_ID
	  	where
	  		a.TRADER_TYPE = 3
	  	and
	  		a.TRADER_MODE = 527
	  	and
	  		b.ORDER_TYPE = 2
	  	and
	  		c.TRADER_ID = #{traderId,jdbcType=INTEGER}
  </select>

  <select id="getTraderSupplierBaseInfo" parameterType="com.vedeng.trader.model.TraderSupplier" resultMap="getTraderSupplierBaseInfoResult">
    select
    a.TRADER_SUPPLIER_ID,a.IS_ENABLE as supplierIsEnaale,a.SUPPLY_PRODUCT, a.GRADE, a.HOT_TELEPHONE, a.SERVICE_TELEPHONE, a.LOGISTICS_NAME, a.WEBSITE,
    a.COMMENTS,a.BRIEF,
    a.AFTER_SALE_MANAGER,a.INSTALLSERVICE_CONTACTNAME,a.INSTALLSERVICE_CONTACTWAY,a.TECHNICALDIRECT_CONTACTNAME,a.TECHNICALDIRECT_CONTACTWAY,
    a.MAINTENANCE_CONTACTNAME,a.MAINTENANCE_CONTACTWAY,a.EXCHANGE_CONTACTNAME,a.EXCHANGE_CONTACTWAY,a.OTHER_CONTACTNAME,a.OTHER_CONTACTWAY,
    a.COMMENTS,a.BRIEF,a.WAREHOUSE_AREA_ID,a.WAREHOUSE_AREA_IDS,a.WAREHOUSE_ADDRESS,a.TRADER_TYPE,
    b.TRADER_ID,b.IS_ENABLE,b.TRADER_NAME,b.AREA_ID,b.AREA_IDS,b.ADDRESS,b.LAST_VALID_TIME,
    c.BRAND_ID,
    d.BRAND_NAME,
    j.LAST_VERIFY_USERNAME,
    j.VERIFY_USERNAME,
    ifnull(j.STATUS,-1) as VERIFY_STATUS,
    a.TAX_PAYER_TYPE
    from
    T_TRADER_SUPPLIER a
    left join
    T_TRADER b on a.TRADER_ID=b.TRADER_ID
    left join
    T_TRADER_SUPPLIER_SUPPLY_BRAND c on a.TRADER_SUPPLIER_ID=c.TRADER_SUPPLIER_ID
    left join
    T_BRAND d on d.BRAND_ID = c.BRAND_ID
    left join
    T_VERIFIES_INFO j on j.RELATE_TABLE_KEY = a.TRADER_SUPPLIER_ID and j.RELATE_TABLE = "T_TRADER_SUPPLIER"
    where
    1=1
    <if test="traderSupplierId != null and traderSupplierId > 0">
      and a.TRADER_SUPPLIER_ID = #{traderSupplierId,jdbcType=INTEGER}
    </if>
    <if test="traderId != null and traderId > 0">
      and a.TRADER_ID = #{traderId,jdbcType=INTEGER}
    </if>
    limit 100
  </select>

  <select id="getSupplierByTraderId" parameterType="java.lang.Integer" resultMap="VoResultMap">
  	select
    	a.*,
  		j.LAST_VERIFY_USERNAME,
  		j.VERIFY_USERNAME,
  		ifnull(j.STATUS,-1) as VERIFY_STATUS
    from T_TRADER_SUPPLIER a
    left join
  		T_VERIFIES_INFO j on j.RELATE_TABLE_KEY = a.TRADER_SUPPLIER_ID and j.RELATE_TABLE = 'T_TRADER_SUPPLIER'
    where a.TRADER_ID = #{traderId,jdbcType=INTEGER}
  </select>

  <select id="getTraderSupplier" parameterType="com.vedeng.trader.model.TraderSupplier" resultMap="VoResultMap">
    select a.*,
           j.LAST_VERIFY_USERNAME,
           j.MOD_TIME,
           j.VERIFY_USERNAME,
           ifnull(j.STATUS, -1) as VERIFY_STATUS
    from T_TRADER_SUPPLIER a
           left join
         T_VERIFIES_INFO j on j.RELATE_TABLE_KEY = a.TRADER_SUPPLIER_ID and j.RELATE_TABLE = 'T_TRADER_SUPPLIER'
    <where>
      <if test="traderId != null and traderId != 0">
        and TRADER_ID = #{traderId,jdbcType=INTEGER}
      </if>
      <if test="traderSupplierId != null and traderSupplierId != 0">
        and TRADER_SUPPLIER_ID = #{traderSupplierId,jdbcType=INTEGER}
      </if>
      limit 100
    </where>
  </select>

  <select id="getTraderSupplierByTraderName" resultType="com.vedeng.trader.model.TraderSupplier">
    SELECT B.*
    FROM T_TRADER A
           INNER JOIN T_TRADER_SUPPLIER B ON A.TRADER_ID= B.TRADER_ID
    WHERE A.TRADER_NAME = #{traderName,jdbcType=VARCHAR}
  </select>

  <select id="getOneByName" resultType="com.vedeng.trader.model.TraderSupplier">
    select a.TRADER_SUPPLIER_ID,
       a.TRADER_ID,
       a.AMOUNT,
       a.PERIOD_AMOUNT,
       a.PERIOD_DAY,
       a.IS_ENABLE,
       a.IS_TOP,
       a.SUPPLY_BRAND,
       a.SUPPLY_PRODUCT,
       a.GRADE,
       a.DISABLE_TIME,
       a.DISABLE_REASON,
       a.COMMENTS,
       a.BRIEF,
       a.HOT_TELEPHONE,
       a.SERVICE_TELEPHONE,
       a.LOGISTICS_NAME,
       a.WEBSITE,
       a.ADD_TIME,
       a.CREATOR,
       a.MOD_TIME,
       a.UPDATER,
       a.WAREHOUSE_AREA_ID,
       a.WAREHOUSE_AREA_IDS,
       a.WAREHOUSE_ADDRESS,
       a.TRADER_TYPE,
       a.AFTER_SALE_MANAGER,
       a.INSTALLSERVICE_CONTACTNAME,
       a.INSTALLSERVICE_CONTACTWAY,
       a.TECHNICALDIRECT_CONTACTNAME,
       a.TECHNICALDIRECT_CONTACTWAY,
       a.MAINTENANCE_CONTACTNAME,
       a.MAINTENANCE_CONTACTWAY,
       a.EXCHANGE_CONTACTNAME,
       a.EXCHANGE_CONTACTWAY,
       a.OTHER_CONTACTNAME,
       a.OTHER_CONTACTWAY
    from T_TRADER_SUPPLIER a
             left join T_TRADER b on a.TRADER_ID = b.TRADER_ID
    where b.TRADER_NAME = #{productNameToUse,jdbcType=VARCHAR}
    limit 1
  </select>
  <select id="getSupplierIdByProductCompanyId" resultType="java.lang.Integer">
    select c.TRADER_SUPPLIER_ID
    from T_PRODUCT_COMPANY a
             left join T_TRADER b on a.PRODUCT_COMPANY_CHINESE_NAME = b.TRADER_NAME
             left join T_TRADER_SUPPLIER c on b.TRADER_ID = c.TRADER_ID
    where a.PRODUCT_COMPANY_ID = #{productCompanyId,jdbcType=INTEGER}
    order by c.TRADER_SUPPLIER_ID desc
    limit 1
  </select>

  <select id="getTraderSupplierByTraderNameAndTraderType" resultMap="BaseResultMap">
    SELECT TTS.*,TT.LAST_VALID_TIME
    FROM T_TRADER TT
    INNER JOIN T_TRADER_SUPPLIER TTS ON TT.TRADER_ID= TTS.TRADER_ID
    WHERE TT.TRADER_NAME = #{traderName,jdbcType=VARCHAR} AND TTS.TRADER_TYPE = #{traderType}
  </select>

  <select id="getAllByTraderType" resultMap="BaseResultMap">
    SELECT TTS.*, TT.TRADER_NAME traderSupplierName
    FROM T_TRADER TT
           INNER JOIN T_TRADER_SUPPLIER TTS ON TT.TRADER_ID = TTS.TRADER_ID
    WHERE TTS.TRADER_TYPE = #{traderType,javaType=INTEGER}
    AND TT.IS_ENABLE = 1
    AND TTS.IS_ENABLE = 1
  </select>

  <select id="getCGFYReturnPeriodAmount" resultType="java.math.BigDecimal">
    select COALESCE(sum(ABS(TCBD.AMOUNT)),0)
    from T_CAPITAL_BILL TCB left join T_CAPITAL_BILL_DETAIL TCBD on TCB.CAPITAL_BILL_ID = TCBD.CAPITAL_BILL_ID
    where
    TCBD.BUSSINESS_TYPE = 533
    and TCBD.TRADER_ID = #{traderId,jdbcType=INTEGER}
    and TCBD.ORDER_TYPE in (4,5)
    and TCBD.TRADER_TYPE = 2
  </select>

  <select id="traderCertificateOverdue" resultType="java.lang.Long">
    SELECT ttc.ENDTIME
    from  T_TRADER_SUPPLIER ts
            LEFT JOIN  T_TRADER t ON t.TRADER_ID = ts.TRADER_ID
            LEFT JOIN T_TRADER_CERTIFICATE ttc ON ts.TRADER_ID = ttc.TRADER_ID
    where ttc.SYS_OPTION_DEFINITION_ID in (25, 28, 29, 439, 1101, 1102, 897, 898, 1100)
    and ttc.ENDTIME > 0
    and ttc.IS_DELETE = 0
      and ttc.TRADER_TYPE = 2
      and ttc.TRADER_ID = #{traderId,jdbcType=INTEGER}
  </select>
</mapper>