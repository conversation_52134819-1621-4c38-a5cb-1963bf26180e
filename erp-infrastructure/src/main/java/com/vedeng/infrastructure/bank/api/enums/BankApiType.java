package com.vedeng.infrastructure.bank.api.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 银行接口状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum BankApiType {
    XFER(1, "Xfer","单笔转账"),
    QRY_XFER(2, "qryXfer","单笔转账结果查询"),
    TRS_DTL_DOWNLOAD(3, "trsDtlDownLoad","转账明细查询"),
    ELECTNOTE_LIST_QRY(4, "ElectnoteListQry","回单列表查询"),
    B2E_ELECTNOTE_DOWNLOAD_NEW(5, "b2eElectNoteDownLoadNew","回单下载"),
    B2ENBS_DRAFT_ISSUE_ADD(6, "B2eNbsDraftIssueAdd","创建承兑汇票"),

    B2E_BASIC_DRAFT_APPLY(11, "B2eBasicDraftApply","融资预申请"),
    CMBC_SIGN_HASH(12, "CMBC_SIGN_HASH","文件签名"),
    B2E_DRAFT_APPLY(13, "B2eDraftApply","融资申请提交"),
    B2E_GET_TOKEN(14, "B2eGetToken","获取文件token"),
    B2E_UPLOAD(15, "b2eUpload","文件上传"),
    B2E_QUERY_BASIC_DRAFT_RESULT(16, "B2eQueryBasicDraftResult","融资结果查询"),
    B2ENBS_DRAFT_HOLDING_BILLS_QRY(17, "B2eNbsDraftHoldingBillsQry","持有票据查询"),
    B2ENBS_DRAFT_DETAIL(18, "B2eNbsDraftDetail","票据详情"),
    B2ENBS_QRY_STAY_SIGNUP_DRAFTS(19,"B2eNbsQryStaySignUpDrafts","签收列表"),/*签收使用*/
    B2ENBS_DRAFT_SIGNUP(20,"B2eNbsDraftSignUp","通用签收"),/*签收使用*/
    B2ENBS_DRAFT_DISCOUNT(21,"B2eNbsDraftDiscount","贴现申请"), /*贴现使用*/
    B2ENBS_QRY_PDRAFT_TRANSSTATUS(22,"B2eNbsQryDraftTransStatus","票据交易状态查询") ,/*贴现使用*/
    B2E_QUERY_TASK_NO(22,"B2eQueryTaskNo","银承中收减免审批单号查询") ;/*贴现使用*/


    private final Integer code;

    private final String trnCode;

    private final String name;



    public static Integer getCode(String trnCode) {
        for (BankApiType bankApiType : BankApiType.values()) {
            if (bankApiType.getTrnCode().equals(trnCode)) {
                return bankApiType.getCode();
            }
        }
        return null;
    }

    public static String getName(String trnCode) {
        for (BankApiType bankApiType : BankApiType.values()) {
            if (bankApiType.getTrnCode().equals(trnCode)) {
                return bankApiType.getName();
            }
        }
        return null;
    }
}
