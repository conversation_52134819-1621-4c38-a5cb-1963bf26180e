package com.vedeng.erp.kingdee.batch.writer;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.batch.dto.BatchKingDeeAliReceiptDto;
import com.vedeng.erp.kingdee.batch.repository.BatchBankBillDtoMapper;
import com.vedeng.erp.kingdee.domain.entity.KingDeeReceiveBillEntity;
import com.vedeng.erp.kingdee.dto.KingDeeAliReceiveBillDto;
import com.vedeng.erp.kingdee.repository.KingDeeAliReceiveBillRepository;
import com.vedeng.erp.kingdee.service.KingDeeAliReceiveBillApiService;
import com.vedeng.erp.kingdee.service.KingDeeAliReceiveBillService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class AliReceiptBillWriter extends BaseWriter<KingDeeAliReceiveBillDto> {

    @Autowired
    private BatchBankBillDtoMapper batchBankBillDtoMapper;

    @Autowired
    private KingDeeAliReceiveBillRepository kingDeeAliReceiveBillRepository;


    @Autowired
    private KingDeeAliReceiveBillService kingDeeAliReceiveBillService;


    @Autowired
    private KingDeeAliReceiveBillApiService kingDeeAliReceiveBillApiService;

    @Override
    public void doWrite(KingDeeAliReceiveBillDto item, JobParameters params, ExecutionContext stepContext) throws Exception {
        KingDeeReceiveBillEntity kingDeeReceiveBillEntity = new KingDeeReceiveBillEntity();
        kingDeeReceiveBillEntity.setFBillNo(item.getFBillNo());
        List<KingDeeReceiveBillEntity> entityListFirst = kingDeeAliReceiveBillRepository.queryByObject(kingDeeReceiveBillEntity);
        if (CollUtil.isEmpty(entityListFirst)) {
            item.setKingDeeBizEnums(KingDeeBizEnums.aliSaveReceiveBill);
            kingDeeAliReceiveBillApiService.register(item,true);
            batchBankBillDtoMapper.updateKingdeePushedStatus(item.getBankBillId());
        }





//
//        kingDeeAliReceiveBillService.query(item);
//        //entityList判空
//        if (item.getFId()<=0) {
//            log.info("调用金蝶接口推送支付宝收款流水失败，实体信息{},", JSON.toJSONString(item));
//            return;
//        }
//        BatchKingDeeAliReceiptDto build = BatchKingDeeAliReceiptDto.builder()
//                .fId(Integer.valueOf(item.getFId()))
//                .tranFlow(item.getFBillNo())
//                .receiptUrl(item.getReceiptUrl())
//                .build();
//        List<BatchKingDeeAliReceiptDto> batchKingDeeAliReceiptDtos = (List<BatchKingDeeAliReceiptDto>) getStepParameter("aliReceiptData");
//        List<BatchKingDeeAliReceiptDto> batchKingDeeAliReceiptDtos1 = new ArrayList<>();
//        if (CollUtil.isEmpty(batchKingDeeAliReceiptDtos)) {
//            batchKingDeeAliReceiptDtos1.add(build);
//            saveStepParameter("aliReceiptData", batchKingDeeAliReceiptDtos1);
//            saveStepParameter("formId", KingDeeFormConstant.RECEIVE_BILL);
//        } else {
//            batchKingDeeAliReceiptDtos.add(build);
//        }
    }

}
