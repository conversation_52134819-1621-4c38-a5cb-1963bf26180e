package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BatchFlowOrderDtoMapper {


    List<BatchFlowOrderDto> findByAll(BatchFlowOrderDto batchFlowOrderDto);


    List<BatchFlowOrderDetailDto> findByFlowOrderIdGetFlowOrderDetailDto(@Param("flowOrderId") Long flowOrderId);


    List<BatchFlowNodeDto> findByFlowOrderIdGetFlowNodeDto(@Param("flowOrderId") Long flowOrderId);

    List<BatchFlowNodeOrderDetailPriceDto> findByFlowOrderIdGetFlowNodeOrderDetailPriceDto(@Param("flowOrderDetailId") Long flowOrderDetailId);


    BatchCompanyDataDto findCustomerTraderIdGetCompanyData(@Param("customerTraderId") Integer customerTraderId);


    BatchCompanyDataDto findSupplierTraderIdGetCompanyData(@Param("supplierTraderId") Integer supplierTraderId);


    List<BatchFlowOrderDto> findNoPushInternalProcurement();

    void insertSelectiveFlowOrderInfo(BatchFlowOrderInfoDto batchFlowOrderInfoDto);

    void updateByPrimaryKeySelectiveFlowOrderInfo(BatchFlowOrderInfoDto batchFlowOrderInfoDto);

    List<BatchFlowOrderInfoDto> findByflowNodeId(Long flowNodeId);


    List<Integer> findSupplierTraderId(@Param("traderId") Integer traderId);


    List<Integer> findCustomerTraderId(@Param("traderId") Integer traderId);


}
