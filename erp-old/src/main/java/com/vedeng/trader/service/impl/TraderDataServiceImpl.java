package com.vedeng.trader.service.impl;

import com.vedeng.common.util.DateUtil;
import com.vedeng.finance.dao.CapitalBillMapper;
import com.vedeng.finance.model.CapitalBill;
import com.vedeng.logistics.dao.WarehouseGoodsOperateLogMapper;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.Saleorder;
import com.vedeng.trader.dao.TraderCustomerMapper;
import com.vedeng.trader.dao.TraderSupplierMapper;
import com.vedeng.trader.model.TraderBussinessData;
import com.vedeng.trader.model.TraderPeriodData;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import com.vedeng.trader.model.vo.TraderSupplierVo;
import com.vedeng.trader.service.TraderDataService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @Author: Cherny.chen
 * @Create: 2021/6/10 19:00
 */
@Service("traderDataService")
public class TraderDataServiceImpl implements TraderDataService {

    @Resource
    private TraderCustomerMapper traderCustomerMapper;

    @Resource
    private TraderSupplierMapper traderSupplierMapper;

    @Resource
    private WarehouseGoodsOperateLogMapper warehouseGoodsOperateLogMapper;

    @Resource
    private CapitalBillMapper capitalBillMapper;

    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private BuyorderMapper buyorderMapper;

    public final static Integer ONE = 1;

    @Override
    public TraderPeriodData getTraderPeriodData(Integer traderId, Integer traderType) {
        TraderPeriodData traderPeriodData = new TraderPeriodData();
        Integer periodAmountUsedTimes = 0;
        BigDecimal periodAmountUsed = new BigDecimal(0);
        BigDecimal periodAmount = new BigDecimal(0);
        BigDecimal periodAmountOccupy = new BigDecimal(0);// 订单占用额度
        Integer periodDay = 0;
        BigDecimal periodAmountOverdue = new BigDecimal(0);
        Integer periodAmountOverdueTimes = 0;

        switch (traderType) {
            case 1:// 客户
                Set<Integer> saleorderIds = new HashSet<>();

                periodAmountUsedTimes = this.getPeriodAmountUsedTimes(traderId, 1);
                periodAmountUsed = this.getPeriodAmountUsed(traderId, 1);
                periodAmountOccupy = this.getPeriodAmountOccupy(traderId, 1);

                List<TraderCustomerVo> traderCustomerAccountPeriodInfo = traderCustomerMapper
                        .getTraderCustomerAccountPeriodInfo(traderId);
                if (null != traderCustomerAccountPeriodInfo && traderCustomerAccountPeriodInfo.size() > 0) {
                    for (TraderCustomerVo periodInfo : traderCustomerAccountPeriodInfo) {
                        periodAmount = periodInfo.getPeriodAmount();
                        periodDay = periodInfo.getPeriodDay();

                        if (null != periodInfo.getSaleorderId() && periodInfo.getSaleorderId() > 0) {
                            saleorderIds.add(periodInfo.getSaleorderId());
                        }
                    }
                }

                if (saleorderIds.size() > 0) {// 判断订单是否逾期（（帐期开始时间+帐期天数）、帐期开始时间按生效订单开始发货的时间计算）
                    for (Integer id : saleorderIds) {
                        Saleorder saleorder = new Saleorder();
                        saleorder.setSaleorderId(id);
                        WarehouseGoodsOperateLog warehouseGoodsOperateLog = warehouseGoodsOperateLogMapper
                                .getWarehouseGoodsOperateOutLog(saleorder);
                        if (warehouseGoodsOperateLog != null) {
                            Long periodTime = warehouseGoodsOperateLog.getAddTime() + (long) periodDay * 24 * 60 * 60 * 1000;// 账期结算期限
                            // 获取结算期限内的订单账期金额
                            List<CapitalBill> periodCapitalBill = capitalBillMapper.getPeriodCapitalBill(id, ONE);
                            Boolean noOverDuo = true;
                            if (periodCapitalBill.size() > 0) {
                                for (CapitalBill bill : periodCapitalBill) {
                                    if (bill.getAddTime() < periodTime) {
                                        continue;
                                    }
                                    noOverDuo = false;
                                    periodAmountOverdue.add(bill.getAmount());
                                }
                            } else if (DateUtil.sysTimeMillis() >= periodTime) {// 未结束
                                // 则逾期金额为订单账期金额
                                noOverDuo = false;
                                Saleorder saleorderInfo = saleorderMapper.getSaleOrderById(id);

                                periodAmountOverdue.add(saleorderInfo.getAccountPeriodAmount());
                            }
                            if (!noOverDuo) {
                                periodAmountOverdueTimes++;// 逾期次数加一
                            }
                        }
                    }
                }

                break;
            case 2:// 供应商
                Set<Integer> buyorderIds = new HashSet<>();

                periodAmountUsedTimes = this.getPeriodAmountUsedTimes(traderId, 2);
                periodAmountUsed = this.getPeriodAmountUsed(traderId, 2);
                periodAmountOccupy = this.getPeriodAmountOccupy(traderId, 2);


                List<TraderSupplierVo> traderSupplierAccountPeriodInfo = traderSupplierMapper
                        .getTraderSupplierAccountPeriodInfo(traderId);
                if (null != traderSupplierAccountPeriodInfo && traderSupplierAccountPeriodInfo.size() > 0) {
                    for (TraderSupplierVo periodInfo : traderSupplierAccountPeriodInfo) {
                        periodAmount = periodInfo.getPeriodAmount();
                        periodDay = periodInfo.getPeriodDay();

                        if (null != periodInfo.getBuyorderId() && periodInfo.getBuyorderId() > 0) {
                            buyorderIds.add(periodInfo.getBuyorderId());
                        }
                    }
                }

                if (buyorderIds.size() > 0) {// 判断订单是否逾期（（帐期开始时间+帐期天数）、帐期开始时间按生效订单开始发货的时间计算）
                    for (Integer id : buyorderIds) {
                        Buyorder buyorder = new Buyorder();
                        buyorder.setBuyorderId(id);
                        WarehouseGoodsOperateLog warehouseGoodsOperateLog = warehouseGoodsOperateLogMapper
                                .getWarehouseGoodsOperateInLog(buyorder);
                        if (warehouseGoodsOperateLog != null) {
                            Long periodTime = warehouseGoodsOperateLog.getAddTime() + (long) periodDay * 24 * 60 * 60 * 1000;// 账期结算期限
                            // 获取结算期限内的订单账期金额
                            List<CapitalBill> periodCapitalBill = capitalBillMapper.getPeriodCapitalBill(id, ONE);
                            Boolean noOverDuo = true;
                            if (periodCapitalBill.size() > 0) {
                                for (CapitalBill bill : periodCapitalBill) {
                                    if (bill.getAddTime() < periodTime) {
                                        continue;
                                    }
                                    noOverDuo = false;
                                    periodAmountOverdue.add(bill.getAmount());
                                }
                            } else if (DateUtil.sysTimeMillis() >= periodTime) {
                                noOverDuo = false;
                                Buyorder buyorderInfo = buyorderMapper.selectByPrimaryKey(id);
                                periodAmountOverdue.add(buyorderInfo.getAccountPeriodAmount());
                            }
                            if (!noOverDuo) {
                                periodAmountOverdueTimes++;// 逾期次数加一
                            }
                        }
                    }
                }
                break;
        }

        traderPeriodData.setPeriodAmountUsedTimes(periodAmountUsedTimes);
        traderPeriodData.setPeriodAmountUsed(periodAmountUsed);
        traderPeriodData.setPeriodAmount(periodAmount);
        traderPeriodData.setPeriodAmountOccupy(periodAmountOccupy);
        traderPeriodData.setPeriodDay(periodDay);
        traderPeriodData.setPeriodAmountOverdue(periodAmountOverdue);
        traderPeriodData.setPeriodAmountOverdueTimes(periodAmountOverdueTimes);

        return traderPeriodData;
    }


    @Override
    public BigDecimal getPeriodAmountUsed(Integer traderId, Integer traderType) {
        BigDecimal periodAmountUsed = new BigDecimal(0);
        switch (traderType) {
            case 1:// 客户
                periodAmountUsed = traderCustomerMapper.getTraderCustomerUsedAccountPeriodAmount(traderId);

                break;
            case 2:// 供应商
                periodAmountUsed = traderSupplierMapper.getTraderSupplierUsedAccountPeriodAmount(traderId);

                break;
        }
        return periodAmountUsed;
    }

    @Override
    public Integer getPeriodAmountUsedTimes(Integer traderId, Integer traderType) {
        Integer periodAmountUsedTimes = 0;
        switch (traderType) {
            case 1:// 客户
                periodAmountUsedTimes = traderCustomerMapper.getTraderCustomerAccountPeriodTimes(traderId);

                break;
            case 2:// 供应商
                periodAmountUsedTimes = traderSupplierMapper.getTraderSupplierAccountPeriodTimes(traderId);
                break;
        }
        return periodAmountUsedTimes;
    }


    @Override
    public BigDecimal getPeriodAmountOccupy(Integer traderId, Integer traderType) {
        BigDecimal periodAmountOccupy = new BigDecimal(0);
        BigDecimal orderPreiodAmount = new BigDecimal(0);
        switch (traderType) {
            case 1:// 客户
                periodAmountOccupy = traderCustomerMapper.getTraderCustomerZYAccountPeriodAmount(traderId);
                //有尾款的账期订单
                orderPreiodAmount = saleorderMapper.getPeriodOrderAmount(traderId);

                break;
            case 2:// 供应商
                periodAmountOccupy = traderSupplierMapper.getTraderSupplierZYAccountPeriodAmount(traderId);
                // 采购费用单使用以及偿还
                BigDecimal cgfyzyAccountPeriodAmount = traderSupplierMapper.getTraderSupplierCGFYZYAccountPeriodAmount(traderId);
                BigDecimal cgfyReturnPeriodAmount = traderSupplierMapper.getCGFYReturnPeriodAmount(traderId);
                periodAmountOccupy = periodAmountOccupy.add(cgfyzyAccountPeriodAmount).subtract(cgfyReturnPeriodAmount);
                //有尾款的账期订单
                orderPreiodAmount = buyorderMapper.getPeriodOrderAmount(traderId);
                break;
        }
        periodAmountOccupy = periodAmountOccupy.subtract(orderPreiodAmount);
        return periodAmountOccupy;
    }

    @Override
    public TraderBussinessData getTraderBussinessData(Integer traderId, Integer traderType) {
        TraderBussinessData traderBussinessData = new TraderBussinessData();
        Integer quoteTimes = 0;// 客户报价次数
        Integer orderTimes = 0;// 客户交易次数
        BigDecimal orderTotalAmount = new BigDecimal(0);// 客户交易金额
        Long lastOrderTime = new Long(0);// 上次交易时间

        switch (traderType) {
            case 1:// 客户
                // 报价信息
                TraderCustomerVo customerQuoteorderInfo = traderCustomerMapper.getCustomerQuoteorderInfo(traderId);
                if (null != customerQuoteorderInfo) {
                    quoteTimes = customerQuoteorderInfo.getQuoteCount();
                }
                TraderCustomerVo customerSaleorderInfo = traderCustomerMapper.getCustomerSaleorderInfo(traderId);
                if (null != customerSaleorderInfo) {
                    orderTimes = customerSaleorderInfo.getBuyCount();
                    orderTotalAmount = customerSaleorderInfo.getBuyMoney();
                    lastOrderTime = customerSaleorderInfo.getLastBussinessTime();
                }
                break;

            case 2:// 供应商
                TraderSupplierVo supplierBuyorderInfo = traderSupplierMapper.getSupplierBuyorderInfo(traderId);
                if (null != supplierBuyorderInfo) {
                    orderTimes = supplierBuyorderInfo.getBuyCount();
                    orderTotalAmount = supplierBuyorderInfo.getBuyMoney();
                    lastOrderTime = supplierBuyorderInfo.getLastBussinessTime();
                }
                break;
        }

        traderBussinessData.setQuoteTimes(quoteTimes);
        traderBussinessData.setOrderTimes(orderTimes);
        traderBussinessData.setOrderTotalAmount(orderTotalAmount);
        traderBussinessData.setLastOrderTime(lastOrderTime);
        return traderBussinessData;
    }

}
