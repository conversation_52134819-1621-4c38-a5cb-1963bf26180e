<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.flash.dao.RegularSnapshotMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.flash.model.RegularSnapshot" >
    <id column="SNAPSHOT_ID" property="snapshotId" jdbcType="INTEGER" />
    <result column="SKU_STATUS" property="skuStatus" jdbcType="TINYINT" />
    <result column="LAST_ADD_TIME" property="lastAddTime" jdbcType="BIGINT" />
    <result column="SKU_ID" property="skuId" jdbcType="INTEGER" />
    <result column="SKU_NO" property="skuNo" jdbcType="VARCHAR" />
    <result column="SKU_NAME" property="skuName" jdbcType="VARCHAR" />
    <result column="BRAND_NAME" property="brandName" jdbcType="VARCHAR" />
    <result column="SPU_TYPE" property="spuType" jdbcType="VARCHAR" />
    <result column="CATEGORY_NAME" property="categoryName" jdbcType="VARCHAR" />
    <result column="SPEC" property="spec" jdbcType="VARCHAR" />
    <result column="UNIT" property="unit" jdbcType="VARCHAR" />
    <result column="COST" property="cost" jdbcType="VARCHAR" />
    <result column="AMOUNT" property="amount" jdbcType="DECIMAL" />
    <result column="YXG_AMOUNT" property="yxgAmount" jdbcType="DECIMAL" />
    <result column="THREE_MONTH_AMOUNT" property="threeMonthAmount" jdbcType="DECIMAL" />
    <result column="YXG_THREE_MONTH_AMOUNT" property="yxgThreeMonthAmount" jdbcType="DECIMAL" />
    <result column="ONE_MONTH_AMOUNT" property="oneMonthAmount" jdbcType="DECIMAL" />
    <result column="YXG_ONE_MONTH_AMOUNT" property="yxgOneMonthAmount" jdbcType="DECIMAL" />
    <result column="OWER_USER_ID" property="owerUserId" jdbcType="INTEGER" />
    <result column="OWER_USER" property="owerUser" jdbcType="VARCHAR" />
    <result column="STOCK" property="stock" jdbcType="INTEGER" />
    <result column="INTRANSIT_STOCK" property="intransitStock" jdbcType="INTEGER" />
    <result column="ORDER_STOCK" property="orderStock" jdbcType="INTEGER" />
    <result column="FORECAST_SAFE_STOCK" property="forecastSafeStock" jdbcType="INTEGER" />
    <result column="SAFE_STOCK" property="safeStock" jdbcType="INTEGER" />
    <result column="STOCK_WARN" property="stockWarn" jdbcType="VARCHAR" />
    <result column="SUPPLEMENT_STOCK" property="supplementStock" jdbcType="INTEGER" />
    <result column="RECEIVE_TIMES" property="receiveTimes" jdbcType="INTEGER" />
    <result column="OUT_STOCK_TIMES" property="outStockTimes" jdbcType="INTEGER" />
    <result column="SNAPSHOT_TIME" property="snapshotTime" jdbcType="VARCHAR" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    SNAPSHOT_ID, SKU_STATUS, LAST_ADD_TIME, SKU_ID, SKU_NO, SKU_NAME, BRAND_NAME, SPU_TYPE,
    CATEGORY_NAME, SPEC, UNIT, COST, AMOUNT, YXG_AMOUNT, THREE_MONTH_AMOUNT, YXG_THREE_MONTH_AMOUNT,
    ONE_MONTH_AMOUNT, YXG_ONE_MONTH_AMOUNT, OWER_USER_ID, OWER_USER, STOCK, INTRANSIT_STOCK,
    ORDER_STOCK, FORECAST_SAFE_STOCK, SAFE_STOCK, STOCK_WARN, SUPPLEMENT_STOCK, RECEIVE_TIMES,
    OUT_STOCK_TIMES, SNAPSHOT_TIME, ADD_TIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from T_REGULAR_SNAPSHOT
    where SNAPSHOT_ID = #{snapshotId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from T_REGULAR_SNAPSHOT
    where SNAPSHOT_ID = #{snapshotId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.flash.model.RegularSnapshot" >
    insert into T_REGULAR_SNAPSHOT (SNAPSHOT_ID, SKU_STATUS, LAST_ADD_TIME,
      SKU_ID, SKU_NO, SKU_NAME,
      BRAND_NAME, SPU_TYPE, CATEGORY_NAME,
      SPEC, UNIT, COST, AMOUNT,
      YXG_AMOUNT, THREE_MONTH_AMOUNT, YXG_THREE_MONTH_AMOUNT,
      ONE_MONTH_AMOUNT, YXG_ONE_MONTH_AMOUNT, OWER_USER_ID,
      OWER_USER, STOCK, INTRANSIT_STOCK,
      ORDER_STOCK, FORECAST_SAFE_STOCK, SAFE_STOCK,
      STOCK_WARN, SUPPLEMENT_STOCK, RECEIVE_TIMES,
      OUT_STOCK_TIMES, SNAPSHOT_TIME, ADD_TIME
      )
    values (#{snapshotId,jdbcType=INTEGER}, #{skuStatus,jdbcType=TINYINT}, #{lastAddTime,jdbcType=BIGINT},
      #{skuId,jdbcType=INTEGER}, #{skuNo,jdbcType=VARCHAR}, #{skuName,jdbcType=VARCHAR},
      #{brandName,jdbcType=VARCHAR}, #{spuType,jdbcType=VARCHAR}, #{categoryName,jdbcType=VARCHAR},
      #{spec,jdbcType=VARCHAR}, #{unit,jdbcType=VARCHAR}, #{cost,jdbcType=VARCHAR}, #{amount,jdbcType=DECIMAL},
      #{yxgAmount,jdbcType=DECIMAL}, #{threeMonthAmount,jdbcType=DECIMAL}, #{yxgThreeMonthAmount,jdbcType=DECIMAL},
      #{oneMonthAmount,jdbcType=DECIMAL}, #{yxgOneMonthAmount,jdbcType=DECIMAL}, #{owerUserId,jdbcType=INTEGER},
      #{owerUser,jdbcType=VARCHAR}, #{stock,jdbcType=INTEGER}, #{intransitStock,jdbcType=INTEGER},
      #{orderStock,jdbcType=INTEGER}, #{forecastSafeStock,jdbcType=INTEGER}, #{safeStock,jdbcType=INTEGER},
      #{stockWarn,jdbcType=VARCHAR}, #{supplementStock,jdbcType=INTEGER}, #{receiveTimes,jdbcType=INTEGER},
      #{outStockTimes,jdbcType=INTEGER}, #{snapshotTime,jdbcType=VARCHAR}, #{addTime,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.flash.model.RegularSnapshot" >
    insert into T_REGULAR_SNAPSHOT
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="snapshotId != null" >
        SNAPSHOT_ID,
      </if>
      <if test="skuStatus != null" >
        SKU_STATUS,
      </if>
      <if test="lastAddTime != null" >
        LAST_ADD_TIME,
      </if>
      <if test="skuId != null" >
        SKU_ID,
      </if>
      <if test="skuNo != null" >
        SKU_NO,
      </if>
      <if test="skuName != null" >
        SKU_NAME,
      </if>
      <if test="brandName != null" >
        BRAND_NAME,
      </if>
      <if test="spuType != null" >
        SPU_TYPE,
      </if>
      <if test="categoryName != null" >
        CATEGORY_NAME,
      </if>
      <if test="spec != null" >
        SPEC,
      </if>
      <if test="unit != null" >
        UNIT,
      </if>
      <if test="cost != null" >
        COST,
      </if>
      <if test="amount != null" >
        AMOUNT,
      </if>
      <if test="yxgAmount != null" >
        YXG_AMOUNT,
      </if>
      <if test="threeMonthAmount != null" >
        THREE_MONTH_AMOUNT,
      </if>
      <if test="yxgThreeMonthAmount != null" >
        YXG_THREE_MONTH_AMOUNT,
      </if>
      <if test="oneMonthAmount != null" >
        ONE_MONTH_AMOUNT,
      </if>
      <if test="yxgOneMonthAmount != null" >
        YXG_ONE_MONTH_AMOUNT,
      </if>
      <if test="owerUserId != null" >
        OWER_USER_ID,
      </if>
      <if test="owerUser != null" >
        OWER_USER,
      </if>
      <if test="stock != null" >
        STOCK,
      </if>
      <if test="intransitStock != null" >
        INTRANSIT_STOCK,
      </if>
      <if test="orderStock != null" >
        ORDER_STOCK,
      </if>
      <if test="forecastSafeStock != null" >
        FORECAST_SAFE_STOCK,
      </if>
      <if test="safeStock != null" >
        SAFE_STOCK,
      </if>
      <if test="stockWarn != null" >
        STOCK_WARN,
      </if>
      <if test="supplementStock != null" >
        SUPPLEMENT_STOCK,
      </if>
      <if test="receiveTimes != null" >
        RECEIVE_TIMES,
      </if>
      <if test="outStockTimes != null" >
        OUT_STOCK_TIMES,
      </if>
      <if test="snapshotTime != null" >
        SNAPSHOT_TIME,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="snapshotId != null" >
        #{snapshotId,jdbcType=INTEGER},
      </if>
      <if test="skuStatus != null" >
        #{skuStatus,jdbcType=TINYINT},
      </if>
      <if test="lastAddTime != null" >
        #{lastAddTime,jdbcType=BIGINT},
      </if>
      <if test="skuId != null" >
        #{skuId,jdbcType=INTEGER},
      </if>
      <if test="skuNo != null" >
        #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null" >
        #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null" >
        #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="spuType != null" >
        #{spuType,jdbcType=VARCHAR},
      </if>
      <if test="categoryName != null" >
        #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="spec != null" >
        #{spec,jdbcType=VARCHAR},
      </if>
      <if test="unit != null" >
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="cost != null" >
        #{cost,jdbcType=VARCHAR},
      </if>
      <if test="amount != null" >
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="yxgAmount != null" >
        #{yxgAmount,jdbcType=DECIMAL},
      </if>
      <if test="threeMonthAmount != null" >
        #{threeMonthAmount,jdbcType=DECIMAL},
      </if>
      <if test="yxgThreeMonthAmount != null" >
        #{yxgThreeMonthAmount,jdbcType=DECIMAL},
      </if>
      <if test="oneMonthAmount != null" >
        #{oneMonthAmount,jdbcType=DECIMAL},
      </if>
      <if test="yxgOneMonthAmount != null" >
        #{yxgOneMonthAmount,jdbcType=DECIMAL},
      </if>
      <if test="owerUserId != null" >
        #{owerUserId,jdbcType=INTEGER},
      </if>
      <if test="owerUser != null" >
        #{owerUser,jdbcType=VARCHAR},
      </if>
      <if test="stock != null" >
        #{stock,jdbcType=INTEGER},
      </if>
      <if test="intransitStock != null" >
        #{intransitStock,jdbcType=INTEGER},
      </if>
      <if test="orderStock != null" >
        #{orderStock,jdbcType=INTEGER},
      </if>
      <if test="forecastSafeStock != null" >
        #{forecastSafeStock,jdbcType=INTEGER},
      </if>
      <if test="safeStock != null" >
        #{safeStock,jdbcType=INTEGER},
      </if>
      <if test="stockWarn != null" >
        #{stockWarn,jdbcType=VARCHAR},
      </if>
      <if test="supplementStock != null" >
        #{supplementStock,jdbcType=INTEGER},
      </if>
      <if test="receiveTimes != null" >
        #{receiveTimes,jdbcType=INTEGER},
      </if>
      <if test="outStockTimes != null" >
        #{outStockTimes,jdbcType=INTEGER},
      </if>
      <if test="snapshotTime != null" >
        #{snapshotTime,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.flash.model.RegularSnapshot" >
    update T_REGULAR_SNAPSHOT
    <set >
      <if test="skuStatus != null" >
        SKU_STATUS = #{skuStatus,jdbcType=TINYINT},
      </if>
      <if test="lastAddTime != null" >
        LAST_ADD_TIME = #{lastAddTime,jdbcType=BIGINT},
      </if>
      <if test="skuId != null" >
        SKU_ID = #{skuId,jdbcType=INTEGER},
      </if>
      <if test="skuNo != null" >
        SKU_NO = #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null" >
        SKU_NAME = #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null" >
        BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="spuType != null" >
        SPU_TYPE = #{spuType,jdbcType=VARCHAR},
      </if>
      <if test="categoryName != null" >
        CATEGORY_NAME = #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="spec != null" >
        SPEC = #{spec,jdbcType=VARCHAR},
      </if>
      <if test="unit != null" >
        UNIT = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="cost != null" >
        COST = #{cost,jdbcType=VARCHAR},
      </if>
      <if test="amount != null" >
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="yxgAmount != null" >
        YXG_AMOUNT = #{yxgAmount,jdbcType=DECIMAL},
      </if>
      <if test="threeMonthAmount != null" >
        THREE_MONTH_AMOUNT = #{threeMonthAmount,jdbcType=DECIMAL},
      </if>
      <if test="yxgThreeMonthAmount != null" >
        YXG_THREE_MONTH_AMOUNT = #{yxgThreeMonthAmount,jdbcType=DECIMAL},
      </if>
      <if test="oneMonthAmount != null" >
        ONE_MONTH_AMOUNT = #{oneMonthAmount,jdbcType=DECIMAL},
      </if>
      <if test="yxgOneMonthAmount != null" >
        YXG_ONE_MONTH_AMOUNT = #{yxgOneMonthAmount,jdbcType=DECIMAL},
      </if>
      <if test="owerUserId != null" >
        OWER_USER_ID = #{owerUserId,jdbcType=INTEGER},
      </if>
      <if test="owerUser != null" >
        OWER_USER = #{owerUser,jdbcType=VARCHAR},
      </if>
      <if test="stock != null" >
        STOCK = #{stock,jdbcType=INTEGER},
      </if>
      <if test="intransitStock != null" >
        INTRANSIT_STOCK = #{intransitStock,jdbcType=INTEGER},
      </if>
      <if test="orderStock != null" >
        ORDER_STOCK = #{orderStock,jdbcType=INTEGER},
      </if>
      <if test="forecastSafeStock != null" >
        FORECAST_SAFE_STOCK = #{forecastSafeStock,jdbcType=INTEGER},
      </if>
      <if test="safeStock != null" >
        SAFE_STOCK = #{safeStock,jdbcType=INTEGER},
      </if>
      <if test="stockWarn != null" >
        STOCK_WARN = #{stockWarn,jdbcType=VARCHAR},
      </if>
      <if test="supplementStock != null" >
        SUPPLEMENT_STOCK = #{supplementStock,jdbcType=INTEGER},
      </if>
      <if test="receiveTimes != null" >
        RECEIVE_TIMES = #{receiveTimes,jdbcType=INTEGER},
      </if>
      <if test="outStockTimes != null" >
        OUT_STOCK_TIMES = #{outStockTimes,jdbcType=INTEGER},
      </if>
      <if test="snapshotTime != null" >
        SNAPSHOT_TIME = #{snapshotTime,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
    </set>
    where SNAPSHOT_ID = #{snapshotId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.flash.model.RegularSnapshot" >
    update T_REGULAR_SNAPSHOT
    set SKU_STATUS = #{skuStatus,jdbcType=TINYINT},
      LAST_ADD_TIME = #{lastAddTime,jdbcType=BIGINT},
      SKU_ID = #{skuId,jdbcType=INTEGER},
      SKU_NO = #{skuNo,jdbcType=VARCHAR},
      SKU_NAME = #{skuName,jdbcType=VARCHAR},
      BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      SPU_TYPE = #{spuType,jdbcType=VARCHAR},
      CATEGORY_NAME = #{categoryName,jdbcType=VARCHAR},
      SPEC = #{spec,jdbcType=VARCHAR},
      UNIT = #{unit,jdbcType=VARCHAR},
      COST = #{cost,jdbcType=VARCHAR},
      AMOUNT = #{amount,jdbcType=DECIMAL},
      YXG_AMOUNT = #{yxgAmount,jdbcType=DECIMAL},
      THREE_MONTH_AMOUNT = #{threeMonthAmount,jdbcType=DECIMAL},
      YXG_THREE_MONTH_AMOUNT = #{yxgThreeMonthAmount,jdbcType=DECIMAL},
      ONE_MONTH_AMOUNT = #{oneMonthAmount,jdbcType=DECIMAL},
      YXG_ONE_MONTH_AMOUNT = #{yxgOneMonthAmount,jdbcType=DECIMAL},
      OWER_USER_ID = #{owerUserId,jdbcType=INTEGER},
      OWER_USER = #{owerUser,jdbcType=VARCHAR},
      STOCK = #{stock,jdbcType=INTEGER},
      INTRANSIT_STOCK = #{intransitStock,jdbcType=INTEGER},
      ORDER_STOCK = #{orderStock,jdbcType=INTEGER},
      FORECAST_SAFE_STOCK = #{forecastSafeStock,jdbcType=INTEGER},
      SAFE_STOCK = #{safeStock,jdbcType=INTEGER},
      STOCK_WARN = #{stockWarn,jdbcType=VARCHAR},
      SUPPLEMENT_STOCK = #{supplementStock,jdbcType=INTEGER},
      RECEIVE_TIMES = #{receiveTimes,jdbcType=INTEGER},
      OUT_STOCK_TIMES = #{outStockTimes,jdbcType=INTEGER},
      SNAPSHOT_TIME = #{snapshotTime,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT}
    where SNAPSHOT_ID = #{snapshotId,jdbcType=INTEGER}
  </update>

  <!--批量新增-->
  <insert id="insertBatch" parameterType="java.util.List">
    INSERT INTO T_REGULAR_SNAPSHOT (SKU_STATUS, LAST_ADD_TIME,
    SKU_ID, SKU_NO, SKU_NAME,
    BRAND_NAME, SPU_TYPE, CATEGORY_NAME,
    SPEC, UNIT, COST, AMOUNT,
    YXG_AMOUNT, THREE_MONTH_AMOUNT, YXG_THREE_MONTH_AMOUNT,
    ONE_MONTH_AMOUNT, YXG_ONE_MONTH_AMOUNT, OWER_USER_ID,
    OWER_USER, STOCK, INTRANSIT_STOCK,
    ORDER_STOCK, FORECAST_SAFE_STOCK, SAFE_STOCK,
    STOCK_WARN, SUPPLEMENT_STOCK, RECEIVE_TIMES,
    OUT_STOCK_TIMES, SNAPSHOT_TIME, ADD_TIME
    )
    VALUES
    <foreach collection="list" item="item" separator=",">
      (#{item.skuStatus,jdbcType=TINYINT}, #{item.lastAddTime,jdbcType=BIGINT},
      #{item.skuId,jdbcType=INTEGER}, #{item.skuNo,jdbcType=VARCHAR}, #{item.skuName,jdbcType=VARCHAR},
      #{item.brandName,jdbcType=VARCHAR}, #{item.spuType,jdbcType=VARCHAR}, #{item.categoryName,jdbcType=VARCHAR},
      #{item.spec,jdbcType=VARCHAR}, #{item.unit,jdbcType=VARCHAR}, #{item.cost,jdbcType=VARCHAR}, #{item.amount,jdbcType=DECIMAL},
      #{item.yxgAmount,jdbcType=DECIMAL}, #{item.threeMonthAmount,jdbcType=DECIMAL}, #{item.yxgThreeMonthAmount,jdbcType=DECIMAL},
      #{item.oneMonthAmount,jdbcType=DECIMAL}, #{item.yxgOneMonthAmount,jdbcType=DECIMAL}, #{item.owerUserId,jdbcType=INTEGER},
      #{item.owerUser,jdbcType=VARCHAR}, #{item.stock,jdbcType=INTEGER}, #{item.intransitStock,jdbcType=INTEGER},
      #{item.orderStock,jdbcType=INTEGER}, #{item.forecastSafeStock,jdbcType=INTEGER}, #{item.safeStock,jdbcType=INTEGER},
      #{item.stockWarn,jdbcType=VARCHAR}, #{item.supplementStock,jdbcType=INTEGER}, #{item.receiveTimes,jdbcType=INTEGER},
      #{item.outStockTimes,jdbcType=INTEGER}, #{item.snapshotTime,jdbcType=VARCHAR}, #{item.addTime,jdbcType=BIGINT}
      )
    </foreach>
  </insert>

  <select id="selectSnapshotListByMonth" parameterType="com.vedeng.flash.dto.PrepareStockCommand" resultType="com.vedeng.flash.model.RegularSnapshot">
    select
    <include refid="Base_Column_List" />,FROM_UNIXTIME(CONVERT(LAST_ADD_TIME/1000,SIGNED),'%Y-%m-%d') AS lastAddTimeStr
    from T_REGULAR_SNAPSHOT
    where SNAPSHOT_TIME = #{exportMonth,jdbcType=VARCHAR}
    ORDER BY LAST_ADD_TIME DESC
  </select>

</mapper>
