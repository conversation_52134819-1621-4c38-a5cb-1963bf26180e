package com.vedeng.erp.kingdee.dto;

import lombok.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶  销项费用普通发票  FSALEEXINVENTRY_Link 实体类
 * @date 2022/11/09 09:13
 */
@Builder
@Getter
@Setter
@AllArgsConstructor
public class OutPutFeePlainInvoiceDetailLinkDto {
    /**
     * 实体主键
     */
    private String fLinkId;
    /**
     *
     */
    private String fsaleexinventryLinkFflowid;
    /**
     * 转换规则 源单是费用应收单：IV_ReceivableToSaleExInv_Entry
     */
    private String fsaleexinventryLinkFflowlineid;
    /**
     * 转换规则 源单是费用应收单：IV_ReceivableToSaleExInv_Entry
     */
    private String fsaleexinventryLinkFruleid;
    /**
     * 源单表内码
     */
    private String fsaleexinventryLinkFstableid;
    /**
     * 源单表 源单费用应收单：t_AR_receivableEntry
     */
    private String fsaleexinventryLinkFstablename;
    /**
     *  源单内码 源单的表头表单的内码
     */
    private String fsaleexinventryLinkFsbillid;
    /**
     * 源单分录内码 源单的表体行内码
     */
    private String fsaleexinventryLinkFsid;
    /**
     * 原始携带金额
     */
    private BigDecimal fsaleexinventryLinkFallamountforold;
    /**
     * 修改携带量（实开金额）
     */
    private BigDecimal fsaleexinventryLinkFallamountfor;

    public OutPutFeePlainInvoiceDetailLinkDto() {
        this.fLinkId = "0";
        this.fsaleexinventryLinkFflowid = "";
        this.fsaleexinventryLinkFflowlineid = "0";
        this.fsaleexinventryLinkFruleid = "IV_ReceivableToSaleExInv_Entry";
        this.fsaleexinventryLinkFstablename = "t_AR_receivableEntry";
        this.fsaleexinventryLinkFstableid = "0";
    }
}
