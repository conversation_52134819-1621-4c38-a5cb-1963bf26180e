<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>客户联系人</title> 
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="traderContactSearch" datasource="erp-datasourcetarget" fixednumber="" fixednumberright="" append_column_url="" append_row_url="" empty_show="" firstcol="radio">
  <div id="appendHead"></div> 
  <div class="layui-fluid"> 
   <div class="layui-tab"> 
    <ul class="layui-tab-title" id="tab"> 
    </ul> 
   </div> 
   <form class="layui-form" id="search">
       <div class=" layui-inline ">
           <label class="layui-form-label">trader_id</label>
           <div class="layui-input-inline">
               <object class=" layui-input list-search-item " type="hidden" name="TRADER_ID" placeholder style alias="" jdbctype data datatype oper validate_rules validate_messages>
               </object> </div>
       </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">联系人名称</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="NAME" placeholder style alias="" jdbctype data datatype oper validate_rules validate_messages> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">手机</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="MOBILE" placeholder style alias="" jdbctype data datatype oper validate_rules validate_messages> 
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">备用手机</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="MOBILE2" placeholder style alias="" jdbctype data datatype oper validate_rules validate_messages> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">电话</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="TELEPHONE" placeholder style alias="" jdbctype data datatype oper validate_rules validate_messages> 
     </div>
    </div>
   </form> 
   <hr class="layui-border-blue"> 
   <div class="btn-group   bd-highlight" id="tableButton">
       <button item_name="ID,NAME,MOBILE" item_id="ID-NAME-MOBILE"  url="traderContactCallBack" opentype="" windowname="" style type="tableselectradio">选择</button>

   </div> 
   <table id="table" class="layui-table" style=" width:100%"> 
    <thead> 
     <tr id="column"> 
      <th type="rowbutton" id="rowbutton"></th> 
      <th item_name="ID" name="ID" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">ID</th>
      <th item_name="NAME" name="NAME" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">联系人名称</th>
      <th item_name="MOBILE" name="MOBILE" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">手机</th>
      <th item_name="MOBILE2" name="MOBILE2" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">备用手机</th>
      <th item_name="TELEPHONE" name="TELEPHONE" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">电话</th>
     </tr> 
    </thead> 
    <tbody> 
     <tr> 
      <td> express:</td> 
      <td colspan="100"> <pre id="express" class="layui-code" orderby=" ORDER BY IS_DEFAULT DESC,ADD_TIME DESC " groupby="">StringBuilder sql=new StringBuilder();
 
sql.append("
    SELECT
        TRADER_CONTACT_ID ID,TRADER_ID,
        NAME,
        MOBILE,
        MOBILE2,
        TELEPHONE 
    FROM
        T_TRADER_CONTACT 
    WHERE
        TRADER_TYPE = 1 
        AND IS_ENABLE = 1 
    ");
              if(isBlank("TRADER_ID")){
              sql.append(" AND 1=2 " );
              }
return search(sql);</pre> </td> 
     </tr> 
     <tr> 
      <td> count:</td> 
      <td colspan="100"> <pre id="count" class="layui-code"></pre> </td> 
     </tr> 
    </tbody> 
   </table> 
  </div> 
  <div id="appendFoot"></div> 
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script> 
  <script>
    layui.use(function () {

    })
</script> 
 </body>
</html>