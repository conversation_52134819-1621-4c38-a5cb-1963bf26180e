package com.vedeng.activiti.taskassign;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.ConfigService;
import com.vedeng.activiti.ProcessInstanceContext;
import com.vedeng.activiti.constants.ProcessConstants;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.erp.aftersale.dto.ExpenseAfterSalesDto;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseDto;
import com.vedeng.finance.model.InvoiceApply;
import com.vedeng.finance.model.PayApply;
import com.vedeng.finance.model.TraderAccountPeriodApply;
import com.vedeng.goods.model.Goods;
import com.vedeng.logistics.model.FileDelivery;
import com.vedeng.oa.service.filedelivery.vo.FileDeliveryVO;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.model.*;
import com.vedeng.order.model.vo.BuyorderModifyApplyVo;
import com.vedeng.order.model.vo.BuyorderVo;
import com.vedeng.system.service.UserService;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import com.vedeng.trader.model.vo.TraderSupplierVo;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.HistoryService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.TaskService;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.impl.el.FixedValue;
import org.activiti.engine.task.IdentityLink;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.vedeng.goods.manager.constants.GoodsValidConstants.SKU_TYPE;
import static com.vedeng.goods.manager.constants.GoodsValidConstants.SPU_TYPE;

/**
 * 发送任务信息的触发器  工作流中需要通知某人审核
 */
@Slf4j
public class TaskMessageListener implements TaskListener {
    private static final long serialVersionUID = 1L;


    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
    private UserService userService = (UserService) context.getBean("userService");
    private AfterSalesMapper afterSalesMapper = context.getBean("afterSalesMapper", AfterSalesMapper.class);
    private BuyorderMapper buyorderMapper = context.getBean("buyorderMapper", BuyorderMapper.class);

    @Autowired
    private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

    public void notify(DelegateTask delegateTask) {

        User user = null;

        TaskService taskService = processEngine.getTaskService();

        if(RequestContextHolder.getRequestAttributes() == null){
            user = userService.getByUsername("njadmin",1);
        }else{
            ServletRequestAttributes ra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = ra.getRequest();
            user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
            if(user == null){
                user = userService.getByUsername("njadmin",1);
            }
        }

        // 申请人名称
        String preAssignee = delegateTask.getVariable("currentAssinee").toString();
        User assigneeInfo = userService.getByUsernameEnable(preAssignee, user.getCompanyId());
        HistoryService historyService = processEngine.getHistoryService(); // 任务相关service
        String processInstanceId = delegateTask.getProcessInstanceId();
        HistoricProcessInstance historicProcessInstance =
                historyService.createHistoricProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        String processDefinitionKey = historicProcessInstance.getProcessDefinitionKey();

        Map<String, Object> variables = delegateTask.getVariables();
        // 获取当前审核候选人
        List<IdentityLink> candidateUserList = taskService.getIdentityLinksForTask(delegateTask.getId());

        List<Integer> varifyUserList = new ArrayList<>();
        // 如果审核人候选组不为空的时候
        if (!candidateUserList.isEmpty()) {
            for (IdentityLink il : candidateUserList) {
                User userInfo = userService.getByUsernameEnable(il.getUserId(), user.getCompanyId());
                if (null != userInfo && null != userInfo.getUserId()&&userInfo.getIsDisabled()!=null && userInfo.getIsDisabled()!=1 ) {
                    varifyUserList.add(userInfo.getUserId());
                }
            }
        } else {
            if (null != delegateTask && null != delegateTask.getAssignee() && delegateTask.getAssignee() != "") {
                User userInfo = userService.getByUsernameEnable(delegateTask.getAssignee(), user.getCompanyId());
                if (null != userInfo && null != userInfo.getUserId() &&userInfo.getIsDisabled()!=null&& userInfo.getIsDisabled()!=1) {
                    varifyUserList.add(userInfo.getUserId());
                }
            }
        }

        // 准备发送参数
        // 模板ID
        Integer messageTemplateId = null;
        Map<String, String> map = new HashMap<>();
        String url = null;
        if (processDefinitionKey.equals("traderCustomerVerify")) {
            // 消息模板编号N001
            messageTemplateId = 1;
            if (null != variables.get("traderCustomerVo")) {
                TraderCustomerVo traderCustomerVo = (TraderCustomerVo) variables.get("traderCustomerVo");
                map.put("traderName", traderCustomerVo.getTrader().getTraderName());
                url = "./trader/customer/baseinfo.do?traderId=" + traderCustomerVo.getTraderId();
            }

        } else if (processDefinitionKey.equals("traderSupplierVerify")) {
            // 消息模板编号N061
            messageTemplateId = 61;
            if (null != variables.get("traderSupplierVo")) {
                TraderSupplierVo traderSupplierVo = (TraderSupplierVo) variables.get("traderSupplierVo");
                map.put("traderName", traderSupplierVo.getTrader().getTraderName());
                url = "./trader/supplier/baseinfo.do?traderId=" + traderSupplierVo.getTrader().getTraderId();
            }

        } else if (processDefinitionKey.equals("quoteVerify")) {
            Integer type = null;
            // 消息模板编号N003
            messageTemplateId = 3;
            type = 2;

            if (null != variables.get("quoteorder")) {
                Quoteorder quoteorder = (Quoteorder) variables.get("quoteorder");
                map.put("quoteorderNo", quoteorder.getQuoteorderNo());
                url = "./order/quote/getQuoteDetail.do?quoteorderId=" + quoteorder.getQuoteorderId() + "&viewType="
                        + type;
            }
        } else if (processDefinitionKey.equals("saleorderVerify")
                || processDefinitionKey.equals("hc_order_auto_verify")
                || processDefinitionKey.equals("bd_order_auto_verify")) {
            // 消息模板编号N006
            messageTemplateId = 6;
            if (null != variables.get("saleorderInfo")) {
                Saleorder saleorder = (Saleorder) variables.get("saleorderInfo");
                map.put("saleorderNo", saleorder.getSaleorderNo());
                url = "./order/saleorder/view.do?saleorderId=" + saleorder.getSaleorderId();
                // 2019-02-25 耗材订单跳转不同duke
                if(saleorder.getOrderType() != null && "5".equals(saleorder.getOrderType().toString())) {
                	url = "./order/hc/hcOrderDetailsPage.do?saleorderId=" + saleorder.getSaleorderId();
                }
                log.info("销售订单审核发送消息,messageTemplateId:{},varifyUserList:{},map:{},url:{},preAssignee:{}", messageTemplateId, JSON.toJSONString(varifyUserList), JSON.toJSONString(map), url, preAssignee);
            }
        } else if (processDefinitionKey.equals("goodsVerify")) {
            // 消息模板编号N012
            messageTemplateId = 12;
            if (null != variables.get("goods")) {
                Goods goods = (Goods) variables.get("goods");
                map.put("sku", goods.getSku());
                url = "./goods/goods/viewbaseinfo.do?goodsId=" + goods.getGoodsId();
            }
        } else if (processDefinitionKey.equals("customerPeriodVerify") ) {
            if (null != variables.get("traderType")) {
                TraderAccountPeriodApply traderAccountPeriodApply =
                        (TraderAccountPeriodApply) variables.get("traderAccountPeriodApply");
                map.put("traderName", traderAccountPeriodApply.getTraderName());
                if (variables.get("traderType").equals(1)) {
                    // 消息模板编号N044
                    messageTemplateId = 44;
                    url = "./finance/accountperiod/getAccountPeriodApply.do?traderAccountPeriodApplyId="
                            + traderAccountPeriodApply.getTraderAccountPeriodApplyId();

                } else {
                    // 消息模板编号N044
                    messageTemplateId = 44;
                    url = "./finance/accountperiod/getAccountPeriodApply.do?traderAccountPeriodApplyId="
                            + traderAccountPeriodApply.getTraderAccountPeriodApplyId();
                }

            }

        } else if(ProcessConstants.APPLY_ACCOUNT_PERIOD_PROC_KEY.endsWith(processDefinitionKey)) {
            map.put("traderName", MapUtils.getString(variables, ProcessInstanceContext.ATTR_NAME_CUSTOMER_NAME));
            messageTemplateId = 44;
            url = new StringBuilder("./finance/accountperiod/getAccountPeriodApply.do")
                    .append("?billPeriodApplyId=" + MapUtils.getString(variables,ProcessInstanceContext.ATTR_NAME_RELATED_TABLE_KEY))
                    .append("&traderAccountPeriodApplyId=" + MapUtils.getString(variables,ProcessInstanceContext.ATTR_NAME_RELATED_TABLE_KEY))
                    .append("&billPeriodType=" + MapUtils.getString(variables,ProcessInstanceContext.ATTR_NAME_ACCOUNT_PERIOD_APPLY_TYPE))
                    .append("&traderCustomerId=" + MapUtils.getString(variables,ProcessInstanceContext.ATTR_NAME_CUSTOMER_ID))
                    .append("&traderType=1")
                    .toString();

        }else if (processDefinitionKey.equals("afterSalesVerify")) {
            // 消息模板编号N027
            messageTemplateId = 27;
            if (null != variables.get("afterSalesInfo")) {
                AfterSalesVo afterSalesVo = (AfterSalesVo) variables.get("afterSalesInfo");
                map.put("afterSalesNo", afterSalesVo.getAfterSalesNo());
                //修复一个场景  traderType类型不是按照当前发送人的类型来的,
                // 而是看候选组审核人的部门类型来的,会导致虽然是销售售后但是下个节点的审核人是采购人员导致traderType错误.
//                List<User> userList = userService.getUserByUserIds(varifyUserList);
//                Integer type = 0;
//                for (User u : userList) {
//                    for (Position p : u.getPositions()) {
//                        type = p.getType();
//                    }
//                }
//                // 发送人是销售
//                if (type == 310) {
//                    url = "./order/saleorder/viewAfterSalesDetail.do?afterSalesId=" + afterSalesVo.getAfterSalesId();
//                    // 发送人是采购
//                } else if (type == 311) {
//                    url = "./aftersales/order/viewAfterSalesDetail.do?afterSalesId=" + afterSalesVo.getAfterSalesId()
//                            + "&traderType=2";
//                } else {
                    // 类型是销售
                    if (afterSalesVo.getSubjectType() == 535) {
                        url = "./order/aftersalesUpgrade/viewAfterSalesDetail.do?afterSalesId="
                                + afterSalesVo.getAfterSalesId() + "&traderType=1";
                        // 类型是采购
                    } else if (afterSalesVo.getSubjectType() == 536) {
                        if(ErpConst.ONE.equals(afterSalesVo.getIsNew())){
                            url = "./order/newBuyorder/viewAfterSalesDetail.do?afterSalesId="
                                    + afterSalesVo.getAfterSalesId() + "&traderType=2";
                        }else {
                            url = "./aftersales/order/viewAfterSalesDetail.do?afterSalesId="
                                    + afterSalesVo.getAfterSalesId() + "&traderType=2";
                        }
                    } else {
                        url = "./order/aftersalesUpgrade/viewAfterSalesDetail.do?afterSalesId="
                                + afterSalesVo.getAfterSalesId();
                    }
//                }
            }
        } else if (processDefinitionKey.equals("expenseAfterSalesVerify")) {
            // 消息模板编号N027
            messageTemplateId = 27;
            if (null != variables.get("expenseAfterSales")) {
                ExpenseAfterSalesDto afterSalesVo = (ExpenseAfterSalesDto) variables.get("expenseAfterSales");
                map.put("afterSalesNo", afterSalesVo.getExpenseAfterSalesNo());
                url = "/buyorderExpense/aftersale/detail.do?expenseAfterSalesId=" + afterSalesVo.getExpenseAfterSalesId();

            }
        }else if (processDefinitionKey.equals("bhSaleorderVerify")) {
            // 消息模板编号N035
            messageTemplateId = 35;
            if (null != variables.get("saleorderInfo")) {
                Saleorder saleorder = (Saleorder) variables.get("saleorderInfo");
                map.put("saleorderNo", saleorder.getSaleorderNo());
                url = "./order/saleorder/viewBhSaleorder.do?saleorderId=" + saleorder.getSaleorderId();
            }
        } else if (processDefinitionKey.equals("closeQuoteorderVerify")) {
            // 消息模板编号N045
            messageTemplateId = 45;
            if (null != variables.get("quoteorder")) {
                Quoteorder quoteorder = (Quoteorder) variables.get("quoteorder");
                map.put("quoteorderNo", quoteorder.getQuoteorderNo());
                url = "./order/quote/getQuoteDetail.do?quoteorderId=" + quoteorder.getQuoteorderId() + "&viewType=2";
            }
        } else if (processDefinitionKey.equals("closeBussinesschanceVerify")) {
            // 消息模板编号N048
            messageTemplateId = 48;
            if (null != variables.get("bussinessChance")) {
                BussinessChance bussinessChance = (BussinessChance) variables.get("bussinessChance");
                map.put("bussinessChanceNo", bussinessChance.getBussinessChanceNo());
//                url = "./order/bussinesschance/toSalesDetailPage.do?bussinessChanceId="
//                        + bussinessChance.getBussinessChanceId() + "&traderId=" + bussinessChance.getTraderId();
                url = "/businessChance/details.do?id=" + bussinessChance.getBussinessChanceId();
            }
        } else if (processDefinitionKey.equals("buyorderVerify") || processDefinitionKey.equals("buyorderVerify_HC")) {
            // 消息模板编号N014
            messageTemplateId = 14;
            if (null != variables.get("buyorderInfo")) {
                BuyorderVo buyorderInfo = (BuyorderVo) variables.get("buyorderInfo");
                map.put("buyorderNo", buyorderInfo.getBuyorderNo());
                url = "./order/buyorder/viewBuyorder.do?buyorderId=" + buyorderInfo.getBuyorderId();
            }
        } else if ("buyorderExpenseVerify".equals(processDefinitionKey)) {
            // 消息模板编号N014
            messageTemplateId = 236;

            if (null != variables.get("buyorderExpenseInfo")) {
                BuyorderExpenseDto buyorderInfo = (BuyorderExpenseDto) delegateTask.getVariable("buyorderExpenseInfo");
                map.put("orderNo", buyorderInfo.getBuyorderExpenseNo());
                url = "./buyorderExpense/details.do?buyorderExpenseId=" +  buyorderInfo.getBuyorderExpenseId();
            }
        }else if (processDefinitionKey.equals("fileDeliveryVerify") || processDefinitionKey.equals("fileDeliveryNew")) {
            // 消息模板编号N051
            messageTemplateId = 51;
            if (null != variables.get("fileDelivery")) {
                FileDelivery fileDelivery = (FileDelivery) variables.get("fileDelivery");
                map.put("fileDeliveryNo", fileDelivery.getFileDeliveryNo());
                url = "./logistics/filedelivery/getFileDeliveryDetail.do?fileDeliveryId="
                        + fileDelivery.getFileDeliveryId();
            }
            if (null != variables.get("fileDeliveryNew")) {
            	FileDeliveryVO fileDelivery = (FileDeliveryVO) variables.get("fileDeliveryNew");
                map.put("fileDeliveryNo", fileDelivery.getFileDeliveryNo());
                url = "./logistics/filedeliveryNew/getFileDeliveryDetail.do?fileDeliveryId="
                        + fileDelivery.getFileDeliveryId();
            }
        } else if (processDefinitionKey.equals("paymentVerify")) {
            // 消息模板编号N054
            messageTemplateId = 54;
            if (null != variables.get("payApply")) {
                PayApply payApply = (PayApply) variables.get("payApply");
                map.put("buyorderNo", payApply.getOrderNo());
//                String no = payApply.getOrderNo().substring(0, 2);
                AfterSales afterSales = afterSalesMapper.getAfterSalesByNo(payApply.getOrderNo());
                Buyorder buyorder = buyorderMapper.getBuyOrderByOrderNo(payApply.getOrderNo());
                if (afterSales != null) {
                    url = "./finance/after/getFinanceAfterSaleDetail.do?afterSalesId=" + payApply.getRelatedId();
                } else if(buyorder != null){
                    if (delegateTask.getName().equals("财务审核") || delegateTask.getName().equals("财务制单")) {
                        url = "./finance/buyorder/viewBuyorder.do?buyorderId=" + payApply.getRelatedId();
                    } else {
                        url = "./order/buyorder/viewBuyordersh.do?buyorderId=" + payApply.getRelatedId();
                    }
                }else {
                    //采购费用单
                    url = "/buyorderExpense/details.do?buyorderExpenseId=" + payApply.getRelatedId();
                }
            }
        } else if (processDefinitionKey.equals("editSaleorderVerify") || processDefinitionKey.equals("saleorderModifyAudit")) {
            // 消息模板编号N064
            messageTemplateId = 64;
            if (null != variables.get("saleorderModifyApplyInfo")) {
                SaleorderModifyApply saleorderModifyApply =
                        (SaleorderModifyApply) variables.get("saleorderModifyApplyInfo");
                map.put("saleorderNo", saleorderModifyApply.getSaleorderModifyApplyNo());
                url = "./order/saleorder/viewModifyApply.do?saleorderModifyApplyId="
                        + saleorderModifyApply.getSaleorderModifyApplyId() + "&saleorderId="
                        + saleorderModifyApply.getSaleorderId();
            }
        } else if (processDefinitionKey.equals("earlyBuyorderVerify")) {
            // 消息模板编号N057
            messageTemplateId = 57;
            if (null != variables.get("saleorderInfo")) {
                Saleorder saleorder = (Saleorder) variables.get("saleorderInfo");
                map.put("saleorderNo", saleorder.getSaleorderNo());
                url = "./order/saleorder/view.do?saleorderId=" + saleorder.getSaleorderId();
            }
        } else if (processDefinitionKey.equals("invoiceVerify")) {
            // 消息模板编号N071
            messageTemplateId = 71;
            if (null != variables.get("invoiceApply")) {
                InvoiceApply invoiceApply = (InvoiceApply) variables.get("invoiceApply");
                if (invoiceApply.getType().equals(504)) {
                    // 消息模板编号N071
                    messageTemplateId = 71;
                    map.put("orderNo", invoiceApply.getAfterSalesNo());
                    url = "./finance/after/getFinanceAfterSaleDetail.do?afterSalesId=" + invoiceApply.getRelatedId()
                            + "&eFlag=cw";

                } else {
                    // 消息模板编号N071
                    messageTemplateId = 71;
                    map.put("orderNo", invoiceApply.getSaleorderNo());
                    url = "./finance/invoice/viewSaleorder.do?saleorderId=" + invoiceApply.getRelatedId();
                }

            }
        } else if (processDefinitionKey.equals("editTraderCustomerName")) {
            // 消息模板编号N072
            messageTemplateId = 72;
            if (null != variables.get("trader")) {
                Trader trader = (Trader) variables.get("trader");
                map.put("traderName", trader.getTraderNameBefore());
                url = "./trader/customer/baseinfo.do?traderId=" + trader.getTraderId();
            }
        } else if (processDefinitionKey.equals("editBuyorderVerify")) {
            // 消息模板编号N076
            messageTemplateId = 76;
            if (null != variables.get("buyorderModifyApplyInfo")) {
                BuyorderModifyApplyVo buyorderModifyApply =
                        (BuyorderModifyApplyVo) variables.get("buyorderModifyApplyInfo");
                map.put("buyorderNo", buyorderModifyApply.getBuyorderModifyApplyNo());
                url = "./order/buyorder/viewModifyApply.do?buyorderModifyApplyId="
                        + buyorderModifyApply.getBuyorderModifyApplyId();
            }
        } else if (processDefinitionKey.equals("overAfterSalesVerify")) {
            if (null != variables.get("afterSalesVo")) {
                AfterSalesVo afterSalesInfo = (AfterSalesVo) variables.get("afterSalesVo");
                if (afterSalesInfo != null && afterSalesInfo.getVerifiesType().equals(0)) {
                    // 关闭
                    messageTemplateId = 80;
                } else if (afterSalesInfo != null && afterSalesInfo.getVerifiesType().equals(1)) {
                    // 完成
                    messageTemplateId = 83;
                }
                map.put("afterSalesNo", afterSalesInfo.getAfterSalesNo());
                // 类型是销售
                if (afterSalesInfo.getSubjectType() == 535 || afterSalesInfo.getSubjectType() == 537) {
                    url = "./order/aftersalesUpgrade/viewAfterSalesDetail.do?afterSalesId=" + afterSalesInfo.getAfterSalesId()
                            + "&traderType=1";
                    // 类型是采购
                } else if (afterSalesInfo.getSubjectType() == 536) {
                    url = "./aftersales/order/viewAfterSalesDetail.do?afterSalesId=" + afterSalesInfo.getAfterSalesId()
                            + "&traderType=2";
                } else {
                    url = "./aftersales/order/viewAfterSalesDetail.do?afterSalesId=" + afterSalesInfo.getAfterSalesId();
                }
            }
            /* 合同回传审核 */
        } else if (processDefinitionKey.equals("contractReturnVerify")) {
            // 消息模板编号 N087
            messageTemplateId = 87;
            if (null != variables.get("saleorderInfo")) {
                Saleorder saleorder = (Saleorder) variables.get("saleorderInfo");
                map.put("saleorderNo", saleorder.getSaleorderNo());
                url = "./order/saleorder/view.do?saleorderId=" + saleorder.getSaleorderId();
            }
        } else if (processDefinitionKey.equals("purchaseContractVerify")){
            // 消息模板编号 264
            messageTemplateId = 264;
            if (null != variables.get("buyorderInfo")){
                BuyorderVo buyorderVo = (BuyorderVo) variables.get("buyorderInfo");
                map.put("buyorderNo",buyorderVo.getBuyorderNo());
                url="./order/newBuyorder/newViewBuyOrderDetail.do?buyorderId="+buyorderVo.getBuyorderId();
            }
        } else if (processDefinitionKey.equals("priceChangeApply")) {
            // 消息模板编号106
            String firstVerifyPirce = variables.get("firstVerifyPirce").toString();

            messageTemplateId = "1".equals(firstVerifyPirce) ? 106 : 145;

            map.put("skuNo", variables.get("skuNo").toString());
            url = "./price/basePriceMaintain/detail.do?skuPriceChangeApplyId=" + variables.get("skuPriceChangeApplyId").toString();
        }else if (processDefinitionKey.equals("testshouquan")){
            messageTemplateId=114;
            map.put("AuthorizationNum",variables.get("authorizationNum").toString());
            url="./order/quote/authorizationExamine.do?authorizationApplyId=" + variables.get("authorizationApplyId").toString();
        }else if(processDefinitionKey.equals("deliveryNoticeVerify")){
            //发货通知 审核消息推 送给归属销售的直接上级
            messageTemplateId=134;
            //填充信息模板中的参数
            map.put("deliveryNoticeNo",variables.get("deliveryNoticeNo").toString());
            url = "./order/saleorder/deliveryNoticeDetailPage.do?saleorderId=" + variables.get("saleorderId").toString()+"&deliveryNoticeId="+variables.get("relateTableKey").toString();
        }else if (processDefinitionKey.equals("lendOutAudit")) {
            // 消息模板编号120
            messageTemplateId = 120;
            map.put("lendOutOrderNo", variables.get("lendOutOrderNo").toString());
            url = "./wms/commodityLendOut/detail.do?lendOutId=" + variables.get("lendOutOrderId").toString();

        }else if (processDefinitionKey.equals("surplusInOrderAudit")) {

            String evn = ConfigService.getAppConfig().getProperty("surplusin_order_audit", "PROD");

            if ("fat".equals(evn)) {
                // 消息模板编号137
                messageTemplateId = 137;
            }else{
                // 消息模板编号134
                messageTemplateId = 134;
            }

            map.put("surplusInOrderNo", variables.get("surplusInOrderNo").toString());
            url = "./wms/surplusIn/applyIndex.do?wmsInputOrderId=" + variables.get("wmsInputOrderId").toString();

        }else if (processDefinitionKey.equals("inventoryOutAudit")) {

            messageTemplateId=235;
            map.put("orderNo", variables.get("orderNo").toString());
            url = "./wms/inventoryOut/details.do?inventoryOutOrderId=" + variables.get("wmsOutPutOrderId").toString();

        } else if ("wmsUnitConversionVerify".equals(processDefinitionKey)) {
            // 消息模板编号120
            messageTemplateId = 250;
            map.put("wmsUnitConversionOrderNo", variables.get("conversionOrderNo").toString());
            url = "/wmsUnitConversion/detail.do?wmsUnitConversionOrderId=" + variables.get("conversionOrderId").toString();
        } else if (processDefinitionKey.equals("afterSaleStandardVerify")) {

            messageTemplateId = 157;

            map.put("skuNo", variables.get("skuNo").toString());
            url = "./aftersale/serviceStandard/detail.do?skuNo=" + variables.get("skuNo").toString();
        }else if (processDefinitionKey.equals("spuOrSkuDisabledStatusVerify")) {

            messageTemplateId = 201;

            Integer goodsType = (Integer)variables.get("goodsType");
            String relatedId = variables.get(ProcessInstanceContext.ATTR_NAME_RELATED_TABLE_KEY).toString();
            if (SPU_TYPE.equals(goodsType)){
                url = "./goods/vgoods/viewSpu.do?spuId=" + relatedId;
                map.put("goodsType", "SPU");
                map.put("relatedId", "V"+relatedId);
            }
            if (SKU_TYPE.equals(goodsType)){
                url = "./goods/vgoods/viewSku.do?pageType=0&skuId=" + relatedId;
                map.put("goodsType", "SKU");
                map.put("relatedId", "V"+relatedId);
            }
        }else if (processDefinitionKey.equals("receiveOutAudit")){

            messageTemplateId = 245;

            map.put("receiveOutOrderNo",variables.get("receiveOutOrderNo").toString());
            url = "./wms/receiveOut/receiveDetail.do?receiveOutId="+variables.get("receiveOutOrderId").toString();
        } else if (processDefinitionKey.equals("sampleOutAudit")) {
            messageTemplateId = 260;

            map.put("sampleOrderNo",variables.get("sampleOutOrderNo").toString());
            url = "./wms/sampleOut/detail.do?sampleOrderId="+variables.get("sampleOutOrderId").toString();
            log.info("创建样品单时，增加抄送功能"); //创建样品单时，增加抄送功能
//            delegateTask.addCandidateUser("Kerwin.wang(抄送)");
//            varifyUserList.add(1411);
        }
        else if ((processDefinitionKey.equals("confirmationOrderVerify"))){
            log.info("TaskMessageExecutionListener,确认单审核流不需要发送站内信");
            return;
        }
        else if(processDefinitionKey.equals("rebateChargeVerify")){
            messageTemplateId = 265;
            map.put("buyOrderRebateChargeNo",variables.get("buyOrderRebateChargeNo").toString());
            url = "./buyOrder/rebateChargeApply/detail.do?buyOrderRebateChargeId="+variables.get("buyOrderRebateChargeId").toString();
        }

        MessageUtil.sendMessage(messageTemplateId, varifyUserList, map, url, preAssignee);//

    }

}
