<%@ tag language="java" pageEncoding="UTF-8"%>
<%@attribute name="saleorder" type="com.vedeng.order.model.Saleorder" required="true" %>
<%@attribute name="expressList" type="java.util.List" required="true" %>
<%@attribute name="saleorderGoodsList" type="java.util.List" required="true" %>
<%@attribute name="specialSkuIdList" type="java.util.List" required="true" %>
<%@attribute name="displayPrUpFs" type="java.lang.Integer" required="true" %>
<%@attribute name="qualityFlagByUserId" type="java.lang.Integer" required="true"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
            + path + "/";
%>
<style>
    .warn22 {
        float: right;
        color: #3384ef;
        padding-right: 10px;
        cursor: pointer;
        height: 34px;
        line-height: 34px;
    }
</style>
<script>
    function warn22() {
    layer.alert("审核中状态不可以提交确认单.")
}

    function warn33() {
        layer.alert("审核中状态不可以删除确认单.")
    }

    function toRecord(recordId){
        console.log(recordId)
        window.location.href = page_url+'/system/call/getrecord.do';
    }

function toWarehouseDetail(){
        $.ajax({
            async:true,
            url:page_url+'/order/saleorder/toWarehouseDetail.do',
            data:{"saleorderId":${saleorder.saleorderId}},
            type:"POST",
            dataType : "json",
            success:function(data){
                if(data.code === 0){
                    var frontPageId = $(window.parent.document).find('.active').eq(1).attr('id');
                    var id = 0;
                    var uri = page_url+'/warehouse/warehousesout/detailJump.do?saleorderId=${saleorder.saleorderId}';
                    var item = { 'id': id, 'name': "查看出库记录", 'url': uri, 'closable': true };
                    self.parent.closableTab.addTab(item);
                    self.parent.closableTab.resizeMove();
                    $(window.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
                }else{
                    layer.alert("出库记录为空，直发商品需要出库记录可联系物流部处理。")
                }
            },
            error:function(data){
                if(data.status ===1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        })
    }

    function urgeDeliveryOrder(){
        $.ajax({
            async:true,
            url:page_url+'/order/saleorder/urgeDeliveryOrder.do',
            data:{"saleorderId":${saleorder.saleorderId}},
            type:"POST",
            dataType : "json",
            success:function(data){
                layer.alert(data.message)
            },
            error:function(data){
                if(data.status ===1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        })
    }
</script>
<form id="getRecord" method="post" action="${pageContext.request.contextPath}/system/call/getrecord.do">
    <input type="hidden" name="communicateRecordId" value="">
    <input type="hidden" name="begindate" value="">
    <input type="hidden" name="enddate" value="">

</form>
<script>
    function getRecord(id) {
        $.ajax({
            async:true,
            url:page_url+'/saleOrderExpress/getCommunicationRecord.do',
            data:{"communicateRecordId":id},
            type:"POST",
            dataType : "json",
            success:function(data){
                $("#getRecord").find("input[name='communicateRecordId']").val(id);
                $("#getRecord").find("input[name='begindate']").val(data.data.begindate);
                $("#getRecord").find("input[name='enddate']").val(data.data.enddate);
                $('#getRecord').submit()
            },
            error:function(data){
                if(data.status ===1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        })
    }
</script>
<div class="parts" liname="物流信息" id="物流信息">
    <div class="title-container title-container-green">
        <div class="table-title nobor">
            物流信息
        </div>
        <a class="title-click" href="javascript:void(0);"  onclick="toWarehouseDetail()">查看出库记录</a>
        <a class="title-click" href="javascript:void(0);"  onclick="urgeDeliveryOrder()">催办出库单</a>

        <c:if test="${!saleorder.needShowConfirm}">
            <c:choose>
                <c:when test="${saleorder.confirmationFormAudit == 1}">
                    <div onclick="warn22()" class="warn22">
                        上传收货确认单
                    </div>
                </c:when>
                <c:when test="${saleorder.deliveryStatus == 0}">
                </c:when>
                <c:otherwise>
                    <div class="title-click nobor  pop-new-data" layerParams='{"width":"52%","height":"90%","title":"上传收货确认单","link":"/orderstream/saleorder/uploadConfirmation.do?saleOrderId=${saleorder.saleorderId}"}'>
                        上传收货确认单
                    </div>
                </c:otherwise>
            </c:choose>
        </c:if>

    </div>
    <table class="table  table-style6" id="t1">
        <thead>
        <tr>
            <td colspan="6" style="background-color: #c1e2d7">确认单状态：&#8195;

                <c:if test="${saleorder.confirmationFormUpload == 0}">
                    未上传
                </c:if>
                <c:if test="${saleorder.confirmationFormUpload == 1}">
                    部分上传
                </c:if>
                <c:if test="${saleorder.confirmationFormUpload == 2}">
                    全部上传
                </c:if>
            </td>
            <td colspan="8" style="background-color: #c1e2d7">确认单审核状态：&#8195;
                <c:if test="${saleorder.confirmationFormAudit == 0}">
                    待提交审核
                </c:if>
                <c:if test="${saleorder.confirmationFormAudit == 1}">
                    审核中
                </c:if>
                <c:if test="${saleorder.confirmationFormAudit == 2}">
                    审核通过
                </c:if>
                <c:if test="${saleorder.confirmationFormAudit == 3}">
                    审核不通过
                </c:if>
            </td>
        </tr>
        <tr>
            <th class="">快递公司</th>
            <th class="">快递单号</th>
            <th class="wid10">发货时间</th>
            <th>商品</th>
            <th class="wid10">快递状态</th>
            <th>备注</th>
            <th>确认单类型</th>
            <th>确认单</th>
            <th>操作人</th>
            <th>操作时间</th>
            <th>确认单审核状态</th>
            <th>确认单驳回原因</th>
            <th>录音ID</th>
            <th>操作</th>
        </tr>
        </thead>
        <tbody id="wl">
        <c:forEach var="express" items="${expressList}">
            <tr>
                <c:if test="${express.firstRoll == 1}">
                    <td   rowspan="${express.rollSize}" >${express.logisticsName}</td>
                    <td   rowspan="${express.rollSize}" >${express.logisticsNo}</td>
                    <td   rowspan="${express.rollSize}" ><date:date value ="${express.deliveryTime}" format="yyyy-MM-dd"/></td>
                    <td   rowspan="${express.rollSize}" class="text-left">
                        <c:forEach var="expressDetails" items="${express.expressDetail}">
                            <div>${expressDetails.goodName}&nbsp;&nbsp;&nbsp;${expressDetails.num} ${expressDetails.unitName} </div>
                        </c:forEach>
                    </td>
                    <td  rowspan="${express.rollSize}" >
                        <c:if test="${empty express.arrivalStatus}">
                            -
                        </c:if>
                        <c:if test="${express.arrivalStatus == 0}">
                            未签收
                        </c:if>
                        <c:if test="${express.arrivalStatus == 1}">
                            部分签收
                        </c:if>
                        <c:if test="${express.arrivalStatus == 2}">
                            已签收
                        </c:if>
                    </td>
                    <td  rowspan="${express.rollSize}" >${express.logisticsComments}</td>
                </c:if>

                <td>
                    <c:if test="${empty express.confirmationType}">
                        -
                    </c:if>
                    <c:if test="${express.confirmationType == 1}">
                        销售上传确认单
                    </c:if>
                    <c:if test="${express.confirmationType == 2}">
                        物流上传确认单
                    </c:if>
                    <c:if test="${express.confirmationType == 3}">
                        客户在线确认
                    </c:if>
                    <c:if test="${express.confirmationType == 4}">
                        其他确认
                    </c:if>
                </td>
                <td>
                    <c:if test="${empty express.confirmationName}">
                        -
                    </c:if>
                    <c:if test="${not empty express.confirmationName}">
                    <span style="float: inherit" class="title-click nobor  pop-new-data" layerParams='{"width":"80%","height":"70%","title":"收货确认单","link":"/orderstream/saleorder/confirmationDetails.do?confirmationId=${express.confirmationId}&saleorderId=${saleorder.saleorderId}"}'>
                        ${express.confirmationName}
                    </span>
                    </c:if>
                </td>
                <td>
                    <c:if test="${empty express.confirmationCreator}">
                        -
                    </c:if>
                        ${express.confirmationCreator}
                </td>
                <td>
                    <c:if test="${empty express.confirmationModTime}">
                        -
                    </c:if>
                    <date:date value ="${express.confirmationModTime}" format="yyyy-MM-dd HH:mm:ss"/>
                </td>
                <td>
                    <c:if test="${empty express.isConfirmationPass}">
                        -
                    </c:if>
                    <c:if test="${express.isConfirmationPass == 0}">
                        待审核
                    </c:if>
                    <c:if test="${express.isConfirmationPass == 1}">
                        审核通过
                    </c:if>
                    <c:if test="${express.isConfirmationPass == 2}">
                        审核未通过
                    </c:if>
                    <c:if test="${express.isConfirmationPass == 3}">
                        审核中
                    </c:if>
                </td>

                <td>
                    <c:choose>
                        <c:when test="${empty express.confirmationRefuseReason}">
                            -
                        </c:when>
                        <c:otherwise>
                            ${express.confirmationRefuseReason}
                        </c:otherwise>
                    </c:choose>
                </td>
                <td>
                    <c:if test="${not empty express.recordIdList}">
                        <c:forEach items="${express.recordIdList}" var="id">
                            <a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewcommunication${id}","link":"./system/call/getrecord.do?communicateRecordId=${id}&begindate=2020-01-01","title":"通话记录"}'>
                                    ${id}
                            </a>
                        </c:forEach>
                    </c:if>
                </td>
                <td>
                    <div class="print-record">
                        <form method="post" id="searchSh"
                              action="<%= basePath %>warehouse/warehousesout/printShOutOrder.do">
                            <input type="hidden" name="orderId" id="orderId" value="${saleorder.saleorderId }"/>
                            <input type="hidden" name="bussinessNo" id="bussinessNo" value="${saleorder.saleorderNo }"/>
                            <input type="hidden" name="btype_sh" id="btype_sh" value=""/>
                            <input type="hidden" name="expressId_xs" id="expressId_xs" value=""/>
                            <c:if test="${express.business_Type == 496}">
                                <div style="width: 100%">
                                    <span class="bt-smaller bt-border-style border-blue" style="width: 80px"
                                          onclick="sendMeinianExpress('${express.expressId}','${express.logisticsNo}');">供货单同步</span>
                                </div>
                                <div style="width: 100%">
                                    <span class="bt-smaller bt-border-style border-blue" style="width: 80px;margin-top: 2px"
                                          onclick="printSHOutOrder('${express.expressId}',496,${saleorder.saleorderId});">打印送货单</span>
                                </div>
                            </c:if>
                            <c:if test="${qualityFlagByUserId == 1}">
                                <c:choose>
                                    <c:when test="${express.isConfirmationPass == 1}">

                                    </c:when>
                                    <c:when test="${express.isConfirmationPass == 3}">

                                    </c:when>
                                    <c:when test="${empty express.isConfirmationPass}">

                                    </c:when>
                                    <c:otherwise>
                                        <div style="width: 100%">
                                            <span class="bt-smaller bt-border-style border-red" style="width: 80px;margin-top: 2px"
                                                  onclick="deleteConfirmationById('${express.confirmationId}');">删除确认单</span>
                                        </div>
                                    </c:otherwise>
                                </c:choose>
                            </c:if>
                        </form>
                    </div>
                    <div class="customername pos_rel">
                        <div style="width: 100%">
                            <i class="bt-smaller bt-border-style border-blue pop-new-data" style="width: 80px;margin-top: 2px"
                               layerParams='{"width":"50%","height":"600px","title":"查看物流","link":"<%=basePath%>warehouse/warehousesout/queryExpressInfo.do?logisticsNo=${express.logisticsNo}&logisticsId=${express.logisticsId}"}'>查看物流</i>
                        </div>
                        <div class="pos_abs customernameshow mouthControlPos">
                            最新信息：${express.contentNew}
                        </div>
                    </div>
                    <div style="width: 100%">
                        <span class="bt-smaller bt-border-style border-blue" style="width: 80px;margin-top: 4px"
                              onclick="associativeRecording('${express.expressId}');">关联录音</span>
                    </div>
                </td>
            </tr>
        </c:forEach>
        <c:if test="${!empty expressList}">
            <tr>
                <td colspan="14" class="allchosetr text-left">
                    <!-- 总运费 -->
                    <c:set var="allamount" value="0.00" />
                    <!-- 总数量 -->
                    <c:set var="allarrivalnum" value="0" />
                    <c:forEach var="express" items="${expressList}">
                        <c:set var="amount" value="0.00" />
                        <c:set var="arrivalnum" value="0" />
                        <c:forEach var="expressDetails" items="${express.expressDetail}">
                            <c:set var="amount" value="${amount + expressDetails.amount}" />
                            <c:set var="arrivalnum" value="${arrivalnum + expressDetails.num}" />
                        </c:forEach>
                        <c:set var="allamount" value="${allamount + amount}" />
                        <c:set var="allarrivalnum" value="${allarrivalnum + arrivalnum}" />
                    </c:forEach>
                    <c:set var="allnum" value="0" />
                    <c:set var="allDeliveryNum" value="0" />
                    <c:set var="waittingDeliveryNum" value="0" />
                    <c:if test="${displayPrUpFs ==1 }">
                        <c:forEach var="item" items="${saleorderGoodsList}" varStatus="num">
                            <c:if test="${!specialSkuIdList.contains(item.getGoodsId())}">
                                <c:set var="allnum" value="${allnum + item.num - item.afterReturnNum}" />
                                <c:set var="allDeliveryNum" value="${allDeliveryNum + item.deliveryNum}" />
                                <c:set var="waittingDeliveryNum"
                                       value="${waittingDeliveryNum + ((item.num - item.afterReturnNum) <= item.deliveryNum ? 0 : (item.num - item.afterReturnNum - item.deliveryNum))}" />
                            </c:if>
                        </c:forEach>
                        运费总额：<span class="mr10">${allamount}</span>商品总数：<span class="">${allnum}</span>
                        已发货总数：<span class="mr10">${allDeliveryNum}</span><span class="warning-color1">待发货数量：${waittingDeliveryNum}</span>
                    </c:if>

                    <c:if test="${displayPrUpFs ==0 }">
                        <c:forEach var="bgv" items="${saleorderGoodsList}" varStatus="num">
                            <c:set var="allnum" value="${allnum + bgv.num}"></c:set>
                            <c:set var="allDeliveryNum" value="${allDeliveryNum + bgv.deliveryNum}"></c:set>
                        </c:forEach>
                        运费总额：<span class="mr10">${allamount}</span>商品总数：<span class="">${allnum}</span>
                        已发货总数：<span class="mr10">${allDeliveryNum}</span><span class="warning-color1">待发货数量：${allnum-allDeliveryNum}</span>
                    </c:if>


                </td>
            </tr>
            <c:if test="${!saleorder.needShowConfirm}">
                <tr>
                    <td colspan="14"><a  href="javascript:void(0);"  onclick="printOutOrderConformation(${saleorder.saleorderId})">打印收货确认单(未确认的批次)</a></td>
                </tr>
            </c:if>

            <form method="post" id="searchc" action="<%= basePath %>warehouse/warehousesout/prinConformationtOutOrder.do">
                <input type="hidden"  name="orderId" id="orderId1" value="${saleorder.saleorderId }"/>
                <input type="hidden"  name="bussinessNo" id="bussinessNo1" value="${saleorder.saleorderNo }"/>
                <input type="hidden"  name="bussinessType" id="bussinessType" value="496"/>
            </form>

        </c:if>
        <c:if test="${empty expressList}">
            <tr>
                <td colspan="13">无任何快递信息，请补充</td>
            </tr>
        </c:if>
        </tbody>
    </table>
</div>