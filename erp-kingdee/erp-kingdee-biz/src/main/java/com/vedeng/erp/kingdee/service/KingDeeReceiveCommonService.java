package com.vedeng.erp.kingdee.service;

import com.vedeng.infrastructure.kingdee.service.KingDeeBaseService;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveCommonDto;
import com.vedeng.erp.kingdee.dto.result.KingDeeReceiveQueryResultDto;

import java.util.List;

/**
 * @description: 销售标准应收单
 * @author: Jez
 * @date: 2022/12/2 15:46
 **/
public interface KingDeeReceiveCommonService extends KingDeeBaseService<KingDeeReceiveCommonDto> {


    /**
     * 根据ERP发票id获取金蝶销售标准应收单
     *
     * @param invoiceId 发票对象
     * @return List<KingDeeReceiveQueryResultDto>
     */
    List<KingDeeReceiveQueryResultDto> getKingDeeReceiveCommon(String invoiceId);

}
