<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchRExpenseAfterSalesJSaleorderMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchRExpenseAfterSalesJSaleorderDto">
    <!--@mbg.generated-->
    <!--@Table T_R_EXPENSE_AFTER_SALES_J_SALEORDER-->
    <id column="T_R_EXPENSE_AFTER_SALES_J_SALEORDER_ID" jdbcType="INTEGER" property="tRExpenseAfterSalesJSaleorderId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="SALEORDER_ID" jdbcType="INTEGER" property="saleorderId" />
    <result column="SALEORDER_GOODS_ID" jdbcType="INTEGER" property="saleorderGoodsId" />
    <result column="EXPENSE_AFTER_SALES_ID" jdbcType="BIGINT" property="expenseAfterSalesId" />
    <result column="EXPENSE_AFTER_SALES_ITEM_ID" jdbcType="BIGINT" property="expenseAfterSalesItemId" />
    <result column="SKU_ID" jdbcType="INTEGER" property="skuId" />
    <result column="SKU_NO" jdbcType="VARCHAR" property="skuNo" />
    <result column="AFTER_SALES_NUM" jdbcType="INTEGER" property="afterSalesNum" />
  </resultMap>

  <resultMap id="BaseResultMapAndExpenseItemId" type="com.vedeng.erp.kingdee.batch.dto.BatchRExpenseAfterSalesJSaleorderDto">
    <result column="EXPENSE_AFTER_SALES_ID" jdbcType="BIGINT" property="expenseAfterSalesId" />
    <result column="EXPENSE_AFTER_SALES_ITEM_ID" jdbcType="BIGINT" property="expenseAfterSalesItemId" />
    <result column="SKU_ID" jdbcType="INTEGER" property="skuId" />
    <result column="SKU_NO" jdbcType="VARCHAR" property="skuNo" />
    <result column="AFTER_SALES_NUM" jdbcType="INTEGER" property="afterSalesNum" />
    <result column="preciseAfterSalesNum" jdbcType="DECIMAL" property="preciseAfterSalesNum" />
    <result column="BUYORDER_EXPENSE_ITEM_ID" jdbcType="INTEGER" property="buyorderExpenseItemId"/>
    <result column="SALEORDER_NO" jdbcType="VARCHAR" property="saleorderNo"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    T_R_EXPENSE_AFTER_SALES_J_SALEORDER_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME,
    UPDATER_NAME, SALEORDER_ID, SALEORDER_GOODS_ID, EXPENSE_AFTER_SALES_ID, EXPENSE_AFTER_SALES_ITEM_ID,
    SKU_ID, SKU_NO, AFTER_SALES_NUM
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_R_EXPENSE_AFTER_SALES_J_SALEORDER
    where T_R_EXPENSE_AFTER_SALES_J_SALEORDER_ID = #{tRExpenseAfterSalesJSaleorderId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_R_EXPENSE_AFTER_SALES_J_SALEORDER
    where T_R_EXPENSE_AFTER_SALES_J_SALEORDER_ID = #{tRExpenseAfterSalesJSaleorderId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="T_R_EXPENSE_AFTER_SALES_J_SALEORDER_ID" keyProperty="tRExpenseAfterSalesJSaleorderId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchRExpenseAfterSalesJSaleorderDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_EXPENSE_AFTER_SALES_J_SALEORDER (ADD_TIME, MOD_TIME, CREATOR,
    UPDATER, CREATOR_NAME, UPDATER_NAME,
    SALEORDER_ID, SALEORDER_GOODS_ID, EXPENSE_AFTER_SALES_ID,
    EXPENSE_AFTER_SALES_ITEM_ID, SKU_ID, SKU_NO,
    AFTER_SALES_NUM)
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER},
    #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR},
    #{saleorderId,jdbcType=INTEGER}, #{saleorderGoodsId,jdbcType=INTEGER}, #{expenseAfterSalesId,jdbcType=BIGINT},
    #{expenseAfterSalesItemId,jdbcType=BIGINT}, #{skuId,jdbcType=INTEGER}, #{skuNo,jdbcType=VARCHAR},
    #{afterSalesNum,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="T_R_EXPENSE_AFTER_SALES_J_SALEORDER_ID" keyProperty="tRExpenseAfterSalesJSaleorderId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchRExpenseAfterSalesJSaleorderDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_EXPENSE_AFTER_SALES_J_SALEORDER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="saleorderId != null">
        SALEORDER_ID,
      </if>
      <if test="saleorderGoodsId != null">
        SALEORDER_GOODS_ID,
      </if>
      <if test="expenseAfterSalesId != null">
        EXPENSE_AFTER_SALES_ID,
      </if>
      <if test="expenseAfterSalesItemId != null">
        EXPENSE_AFTER_SALES_ITEM_ID,
      </if>
      <if test="skuId != null">
        SKU_ID,
      </if>
      <if test="skuNo != null">
        SKU_NO,
      </if>
      <if test="afterSalesNum != null">
        AFTER_SALES_NUM,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="saleorderId != null">
        #{saleorderId,jdbcType=INTEGER},
      </if>
      <if test="saleorderGoodsId != null">
        #{saleorderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="expenseAfterSalesId != null">
        #{expenseAfterSalesId,jdbcType=BIGINT},
      </if>
      <if test="expenseAfterSalesItemId != null">
        #{expenseAfterSalesItemId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=INTEGER},
      </if>
      <if test="skuNo != null">
        #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="afterSalesNum != null">
        #{afterSalesNum,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchRExpenseAfterSalesJSaleorderDto">
    <!--@mbg.generated-->
    update T_R_EXPENSE_AFTER_SALES_J_SALEORDER
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="saleorderId != null">
        SALEORDER_ID = #{saleorderId,jdbcType=INTEGER},
      </if>
      <if test="saleorderGoodsId != null">
        SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="expenseAfterSalesId != null">
        EXPENSE_AFTER_SALES_ID = #{expenseAfterSalesId,jdbcType=BIGINT},
      </if>
      <if test="expenseAfterSalesItemId != null">
        EXPENSE_AFTER_SALES_ITEM_ID = #{expenseAfterSalesItemId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null">
        SKU_ID = #{skuId,jdbcType=INTEGER},
      </if>
      <if test="skuNo != null">
        SKU_NO = #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="afterSalesNum != null">
        AFTER_SALES_NUM = #{afterSalesNum,jdbcType=INTEGER},
      </if>
    </set>
    where T_R_EXPENSE_AFTER_SALES_J_SALEORDER_ID = #{tRExpenseAfterSalesJSaleorderId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchRExpenseAfterSalesJSaleorderDto">
    <!--@mbg.generated-->
    update T_R_EXPENSE_AFTER_SALES_J_SALEORDER
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
    MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
    CREATOR = #{creator,jdbcType=INTEGER},
    UPDATER = #{updater,jdbcType=INTEGER},
    CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
    UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
    SALEORDER_ID = #{saleorderId,jdbcType=INTEGER},
    SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER},
    EXPENSE_AFTER_SALES_ID = #{expenseAfterSalesId,jdbcType=BIGINT},
    EXPENSE_AFTER_SALES_ITEM_ID = #{expenseAfterSalesItemId,jdbcType=BIGINT},
    SKU_ID = #{skuId,jdbcType=INTEGER},
    SKU_NO = #{skuNo,jdbcType=VARCHAR},
    AFTER_SALES_NUM = #{afterSalesNum,jdbcType=INTEGER}
    where T_R_EXPENSE_AFTER_SALES_J_SALEORDER_ID = #{tRExpenseAfterSalesJSaleorderId,jdbcType=INTEGER}
  </update>

  <!--auto generated by MybatisCodeHelper on 2023-03-21-->
  <select id="findByBuyorderExpenseId" resultMap="BaseResultMapAndExpenseItemId">
    select
    TREASJS.SALEORDER_ID,  TREASJS.EXPENSE_AFTER_SALES_ID, TREASJS.EXPENSE_AFTER_SALES_ITEM_ID, SKU_ID, SKU_NO,
    SUM(TREASJS.AFTER_SALES_NUM) as AFTER_SALES_NUM,SUM(TREASJS.AFTER_SALES_NUM) as preciseAfterSalesNum ,TS.SALEORDER_NO
    ,TEASI.BUYORDER_EXPENSE_ITEM_ID
    from T_R_EXPENSE_AFTER_SALES_J_SALEORDER TREASJS
    left join T_EXPENSE_AFTER_SALES_STATUS TEASS
    on TREASJS.EXPENSE_AFTER_SALES_ID = TEASS.EXPENSE_AFTER_SALES_ID
    left join T_EXPENSE_AFTER_SALES TEAS on TEAS.EXPENSE_AFTER_SALES_ID = TREASJS.EXPENSE_AFTER_SALES_ID
    left join T_EXPENSE_AFTER_SALES_ITEM TEASI on TREASJS.EXPENSE_AFTER_SALES_ITEM_ID =TEASI.EXPENSE_AFTER_SALES_ITEM_ID
    left join T_SALEORDER TS on TS.SALEORDER_ID = TREASJS.SALEORDER_ID
    where TEAS.BUYORDER_EXPENSE_ID=#{buyorderExpenseId,jdbcType=INTEGER}
    and TEASS.AFTER_SALES_STATUS in (1,2)
    group by TREASJS.SALEORDER_ID,TEASI.BUYORDER_EXPENSE_ITEM_ID
  </select>

  <!--auto generated by MybatisCodeHelper on 2023-03-22-->
  <select id="findByExpenseAfterSalesId" resultMap="BaseResultMapAndExpenseItemId">
    select
    TREASJS.SALEORDER_ID, SALEORDER_GOODS_ID,TS.SALEORDER_NO,
    SKU_ID, SKU_NO,  SUM(TREASJS.AFTER_SALES_NUM) as AFTER_SALES_NUM,SUM(TREASJS.AFTER_SALES_NUM) as preciseAfterSalesNum,TEASI.BUYORDER_EXPENSE_ITEM_ID
    from T_R_EXPENSE_AFTER_SALES_J_SALEORDER TREASJS
    left join T_EXPENSE_AFTER_SALES_ITEM TEASI on TEASI.EXPENSE_AFTER_SALES_ITEM_ID =TREASJS.EXPENSE_AFTER_SALES_ITEM_ID
    left join T_SALEORDER TS on TS.SALEORDER_ID = TREASJS.SALEORDER_ID
    where TREASJS.EXPENSE_AFTER_SALES_ID=#{expenseAfterSalesId,jdbcType=BIGINT}
    group by TREASJS.SALEORDER_ID,TEASI.BUYORDER_EXPENSE_ITEM_ID
  </select>
</mapper>