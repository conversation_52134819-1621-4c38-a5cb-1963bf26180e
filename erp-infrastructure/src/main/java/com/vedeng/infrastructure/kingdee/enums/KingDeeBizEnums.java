package com.vedeng.infrastructure.kingdee.enums;

import cn.hutool.core.util.StrUtil;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 对接金蝶业务枚举类
 * 如 新增供应商 addSupplier
 * 新增付款单 addPayBill
 * @date 2022/8/29 11:32
 */
public enum KingDeeBizEnums {

    /**
     * 保存供应商
     */
    saveSupplier(1, "Supplier", "save", KingDeeFormConstant.BD_SUPPLIER),

    /**
     * 供应商批量保存
     */
    batchSaveSupplier(2, "Supplier", "batchSave", KingDeeFormConstant.BD_SUPPLIER),

    /**
     * 保存付款单
     */
    savePayBill(3, "PayBill", "savePayBillInfo", KingDeeFormConstant.AP_PAYBILL),

    /**
     * 保存支付宝付款单
     */
    savePayBillAli(4, "PayBill", "savePayBillAli", KingDeeFormConstant.AP_PAYBILL),

    /**
     * 物料
     */
    saveMaterial(5, "Material", "save", KingDeeFormConstant.BD_MATERIAL),

    /**
     * 新增客户
     */
    saveCustomer(6, "Customer", "save", KingDeeFormConstant.BD_CUSTOMER),

    /**
     * 更新供应商
     */
    updateSupplier(7, "Supplier", "update", KingDeeFormConstant.BD_SUPPLIER),

    /**
     * 更新物料
     */
    updateMaterial(8, "Material", "update", KingDeeFormConstant.BD_MATERIAL),

    /**
     * 更新客户
     */
    updateCustomer(9, "Customer", "update", KingDeeFormConstant.BD_CUSTOMER),

    /**
     * 更新进项费用普票
     */
    updateIFPInvoice(10, "IFPInvoice", "update", KingDeeFormConstant.INPUT_FEE_PLAIN_INVOICE),

    /**
     * 更新进项费用专票
     */
    updateIFSInvoice(11, "IFSInvoice", "update", KingDeeFormConstant.INPUT_FEE_SPECIAL_INVOICE),

    /**
     * 更新采购费用普票
     */
    updatePVPInvoice(12, "PVPInvoice", "update", KingDeeFormConstant.PURCHASE_VAT_PLAIN_INVOICE),

    /**
     * 更新采购费用专票
     */
    updatePVSInvoice(13, "PVSInvoice", "update", KingDeeFormConstant.PURCHASE_VAT_SPECIAL_INVOICE),

    /**
     * 保存普通商品专票
     */
    saveCommonSpecialInvoice(14, "SalesVatSpecialInvoice", "save", KingDeeFormConstant.SALE_VAT_SPECIAL_INVOICE),

    /**
     * 保存普通商品普票
     */
    saveSalesVatPlainInvoice(15, "SalesVatPlainInvoice", "save", KingDeeFormConstant.SALE_VAT_PLAIN_INVOICE),

    /**
     * 保存费用商品专票
     */
    saveExpenseSpecialInvoice(16, "OutPutFeeSpecialInvoice", "save", KingDeeFormConstant.OUTPUT_FEE_SPECIAL_INVOICE),

    /**
     * 保存费用商品普票
     */
    saveExpensePlainInvoice(17, "OutPutFeePlainInvoice", "save", KingDeeFormConstant.OUTPUT_FEE_PLAIN_INVOICE),
    /**
     * 保存收款流水
     */
    saveReceiveBill(18, "ReceiveBill", "save", KingDeeFormConstant.RECEIVE_BILL),
    /**
     * 保存应收单调整单
     */
    saveNeedReceiveAdjust(19, "NeedReceiveAdjust", "save", KingDeeFormConstant.BD_NEEDREVEIVE),

    /**
     * 保存其他出库
     */
    saveStorageOut(20, "StorageOut", "save", KingDeeFormConstant.STK_MIS_DELIVERY),

    /**
     * 保存其他入库单
     */
    saveStorageIn(21, "StorageIn", "save", KingDeeFormConstant.STK_MISCELLANEOUS),

    /**
     * 保存安调单
     */
    saveInstallServiceRecord(22, "InstallServiceRecord", "save", KingDeeFormConstant.QZOK_ATHD),

    /**
     * 修改安调单
     */
    updateInstallServiceRecord(100, "InstallServiceRecord", "update", KingDeeFormConstant.QZOK_ATHD),

    /**
     * 销售出库单
     */
    saveSaleOutStock(23, "SaleOutStock", "save", KingDeeFormConstant.SAL_OUT_STOCK),

    /**
     * 标准(费用)应收单
     */
    saveReceiveCommon(24, "ReceiveCommon", "save", KingDeeFormConstant.RECEIVE_EXPENSES),

    /**
     * 保存采购入库单
     */
    savePurchaseReceipt(25, "PurchaseReceipt", "save", KingDeeFormConstant.STK_INSTOCK),

    /**
     * 保存销售结算调整单
     */
    saveSaleSettlementAdjustment(26, "SaleSettlementAdjustment", "save", KingDeeFormConstant.QZOK_XSJSTZD),

    /**
     * 保存采购退货出库单
     */
    savePurchaseBack(27, "PurchaseBack", "save", KingDeeFormConstant.PURCHASE_BACK),

    /**
     * 费用应收单
     */
    saveReceiveFee(28, "ReceiveFee", "save", KingDeeFormConstant.RECEIVE_EXPENSES),


    /**
     * 保存支付宝收款流水
     */
    aliSaveReceiveBill(29, "AliReceiveBill", "save", KingDeeFormConstant.RECEIVE_BILL),

    /**
     * 保存销售退货入库单
     */
    saveSaleReturn(30, "SaleReturnStock", "save", KingDeeFormConstant.SAL_RETURN_STOCK),

    /**
     * 保存采购应付单
     */
    savePurchasePayCommon(31, "PayCommon", "save", KingDeeFormConstant.PAY_EXPENSES),

    /**
     * 采购专票
     */
    savePurchaseInvoiceSpecial(32, "PurchaseInvoiceSpecial", "save", KingDeeFormConstant.PURCHASE_VAT_SPECIAL_INVOICE),

    /**
     * 采购普票
     */
    savePurchaseInvoicePlain(33, "PurchaseInvoicePlain", "save", KingDeeFormConstant.PURCHASE_VAT_PLAIN_INVOICE),
    /**
     * 采购费用应付单
     */
    savePayExpenses(34, "PayExpenses", "save", KingDeeFormConstant.PAY_EXPENSES),

    /**
     * 采购费用专票
     */
    saveInPutFeeSpecialInvoice(35, "InPutFeeSpecialInvoice", "save", KingDeeFormConstant.INPUT_FEE_SPECIAL_INVOICE),

    /**
     * 采购费用普票
     */
    saveInPutFeePlainInvoice(36, "InPutFeePlainInvoice", "save", KingDeeFormConstant.INPUT_FEE_PLAIN_INVOICE),

    /**
     * 应付余额调整单
     */
    saveKingDeeNeedPayAdjust(37, "NeedPay", "save", KingDeeFormConstant.BD_NEEDPAY),

    /**
     * 保存快递签收
     */
    saveExpressReceipt(38, "ExpressReceipt", "save", KingDeeFormConstant.QZOK_KDQSD),

    /**
     * 保存快递成本
     */
    saveExpressCost(39, "ExpressCost", "save", KingDeeFormConstant.QZOK_KDCB),

    /**
     * 更新快递成本
     */
    updateExpressCost(40, "ExpressCost", "update", KingDeeFormConstant.QZOK_KDCB),

    /**
     * 更新快递签收
     */
    updateExpressReceipt(41, "ExpressReceipt", "update", KingDeeFormConstant.QZOK_KDQSD),

    /**
     * 附件上传
     */
    uploadFileData(42, "FileData", "upload", ""),

    /**
     * 保存收款退款单（定时任务）
     */
    saveReceiveRefundBill(43, "ReceiveRefundBill", "save", KingDeeFormConstant.RECEIVE_REFUND),

    /**
     * 保存付款退款单
     */
    savePayRefundBill(44, "PayRefundBill", "save", KingDeeFormConstant.PAY_REFUND),

    /**
     * 保存收款退款单（付款申请）
     */
    saveReceiveRefundBill2(45, "ReceiveRefundBill2", "save", KingDeeFormConstant.RECEIVE_REFUND),

    /**
     * 调拨单
     */
    saveAllocation(46, "Allocation", "save", KingDeeFormConstant.STK_TRANSFER_DIRECT),

    /**
     * 盘盈单
     */
    saveInventoryProfit(47, "InventoryProfit", "save", KingDeeFormConstant.STK_STOCK_COUNT_GAIN),

    /**
     * 盘亏单
     */
    saveProfitLoss(48, "ProfitLoss", "save", KingDeeFormConstant.STK_STOCK_COUNT_LOSS),


    /**
     * 保存销售合同
     */
    saveSaleorderContract(49,"SaleorderContract","save",KingDeeFormConstant.SALEORDER_CONTRACT),

    /**
     * 保存采购合同
     */
    saveBuyOrderContract(50,"BuyOrderContract","save",KingDeeFormConstant.BUYORDER_CONTRACT),

    /**
     * 付款单工作流提交
     * formId存在两种情况，付款和收款退款，由不同业务传入
     */
    workFlowSubmit(51,"Workflow","submit",""),

    /**
     * 付款单工作流自动审核
     * formId存在两种情况，付款和收款退款，由不同业务传入
     */
    workFlowAudit(52,"Workflow","audit",""),

    /**
     * 内部采销
     */
    saveInternalProcurement(53, "InternalProcurement", "save", KingDeeFormConstant.INTERNAL_PROCUREMENT),
    ;


    /**
     * 业务类型
     */
    private final Integer type;

    /**
     * 业务类型
     */
    private final String business;

    /**
     * 方法
     */
    private final String method;

    /**
     * 金蝶表单id
     */
    private final String formId;


    public String getBusiness() {
        return business;
    }

    public String getMethod() {
        return method;
    }

    public Integer getType() {
        return type;
    }

    public String getFormId() {
        return formId;
    }

    KingDeeBizEnums(Integer type, String business, String method, String formId) {
        this.type = type;
        this.business = business;
        this.method = method;
        this.formId = formId;
    }


    /**
     * 获取金蝶业务枚举
     *
     * @param type 业务类型
     * @return KingDeeBizEnums
     */
    public static KingDeeBizEnums getEnum(Integer type) {
        return Arrays.stream(KingDeeBizEnums.values())
                .filter(enums -> enums.getType().equals(type))
                .findFirst().orElseThrow(() -> new ServiceException("无法获取对应的金蝶业务枚举"));
    }

    /**
     * 获取金蝶业务枚举
     *
     * @param eventType 业务类型
     * @return KingDeeBizEnums
     */
    public static KingDeeBizEnums getEnum(String eventType) {
        if (StrUtil.isBlank(eventType)) {
            throw new ServiceException("无法获取对应的金蝶业务枚举");
        }
        List<String> list = StrUtil.split(eventType, StrUtil.UNDERLINE);
        if (list.size() != 2) {
            throw new ServiceException("无法获取对应的金蝶业务枚举");
        }

        return Arrays.stream(KingDeeBizEnums.values())
                .filter(enums -> enums.getBusiness().equals(list.get(0))
                        && enums.getMethod().equals(list.get(1)))
                .findFirst().orElseThrow(() -> new ServiceException("无法获取对应的金蝶业务枚举"));
    }

    /**
     * 获取金蝶业务枚举
     *
     * @param kingDeeBizEnums 业务类型
     * @return String
     */
    public static String getEventType(KingDeeBizEnums kingDeeBizEnums) {
        return kingDeeBizEnums.getBusiness() + StrUtil.UNDERLINE + kingDeeBizEnums.getMethod();
    }
}
