package com.vedeng.common.constant.goods;

import java.util.Arrays;

public enum SpuLevelEnum {
	TEMP(2, "临时商品"), CORE(1, "核心商品"), OTHER(0, "其他商品");
	private Integer spuLevel;
	private String levelName;

	private SpuLevelEnum(int spuLevel, String levelName) {
		this.spuLevel = spuLevel;
		this.levelName = levelName;
	}

	public Integer spuLevel() {
		return spuLevel;
	}

	public static String levelName(Integer level) {
		for (SpuLevelEnum e : values()) {
			if (e.spuLevel.equals(level)) {
				return e.levelName;
			}
		}
		return "";
	}
	public static boolean isTempSpu(Integer spuLevel){
		return TEMP.spuLevel.equals(spuLevel);
	}


	/**
	 * 获取SPU等级ID，为空默认置为其他商品
	 *
	 * @param code
	 * @return
	 */
	public static Integer getOrDefault(Integer code) {
		return Arrays.stream(values()).filter(level->level.spuLevel().equals(code)).findFirst().orElse(OTHER).spuLevel();
	}


}
