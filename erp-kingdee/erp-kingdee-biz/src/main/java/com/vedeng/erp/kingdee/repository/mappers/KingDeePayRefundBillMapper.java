package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.KingDeePayRefundBillEntity;

public interface KingDeePayRefundBillMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(KingDeePayRefundBillEntity record);

    int insertSelective(KingDeePayRefundBillEntity record);

    KingDeePayRefundBillEntity selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(KingDeePayRefundBillEntity record);

    int updateByPrimaryKey(KingDeePayRefundBillEntity record);
}