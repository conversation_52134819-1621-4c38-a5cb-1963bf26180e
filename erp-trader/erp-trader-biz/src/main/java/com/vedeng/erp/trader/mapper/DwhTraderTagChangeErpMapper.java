package com.vedeng.erp.trader.mapper;

import com.vedeng.erp.trader.domain.entity.DwhTraderTagChangeErpEntity;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

public interface DwhTraderTagChangeErpMapper {
    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int insert(DwhTraderTagChangeErpEntity row);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    List<DwhTraderTagChangeErpEntity> selectAll();

    List<DwhTraderTagChangeErpEntity> selectByOperateTime(@Param("operateTimeBefore")LocalDateTime operateTimeBefore, @Param("operateTimeAfter")LocalDateTime operateTimeAfter);
}