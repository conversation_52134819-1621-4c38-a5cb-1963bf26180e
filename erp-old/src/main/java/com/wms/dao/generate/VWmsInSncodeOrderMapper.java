package com.wms.dao.generate;

import com.wms.model.ddi.VWmsInSncodeOrderExtDto;

public interface VWmsInSncodeOrderMapper {
    int deleteByPrimaryKey(Integer inSncodeOrderId);

    int insert(VWmsInSncodeOrderExtDto record);

    int insertSelective(VWmsInSncodeOrderExtDto record);

    VWmsInSncodeOrderExtDto selectByPrimaryKey(Integer inSncodeOrderId);

    int updateByPrimaryKeySelective(VWmsInSncodeOrderExtDto record);

    int updateByPrimaryKey(VWmsInSncodeOrderExtDto record);
}