<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.system.mapper.TagMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.system.domain.entity.TagEntity">
    <!--@mbg.generated-->
    <!--@Table T_TAG-->
    <id column="TAG_ID" jdbcType="INTEGER" property="tagId" />
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
    <result column="TAG_TYPE" jdbcType="INTEGER" property="tagType" />
    <result column="IS_RECOMMEND" jdbcType="BOOLEAN" property="isRecommend" />
    <result column="TAG_NAME" jdbcType="VARCHAR" property="tagName" />
    <result column="COMMENTS" jdbcType="VARCHAR" property="comments" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    TAG_ID, COMPANY_ID, TAG_TYPE, IS_RECOMMEND, TAG_NAME, COMMENTS
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_TAG
    where TAG_ID = #{tagId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_TAG
    where TAG_ID = #{tagId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="TAG_ID" keyProperty="tagId" parameterType="com.vedeng.erp.system.domain.entity.TagEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TAG (COMPANY_ID, TAG_TYPE, IS_RECOMMEND, 
      TAG_NAME, COMMENTS)
    values (#{companyId,jdbcType=INTEGER}, #{tagType,jdbcType=INTEGER}, #{isRecommend,jdbcType=BOOLEAN}, 
      #{tagName,jdbcType=VARCHAR}, #{comments,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="TAG_ID" keyProperty="tagId" parameterType="com.vedeng.erp.system.domain.entity.TagEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TAG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="tagType != null">
        TAG_TYPE,
      </if>
      <if test="isRecommend != null">
        IS_RECOMMEND,
      </if>
      <if test="tagName != null">
        TAG_NAME,
      </if>
      <if test="comments != null">
        COMMENTS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="tagType != null">
        #{tagType,jdbcType=INTEGER},
      </if>
      <if test="isRecommend != null">
        #{isRecommend,jdbcType=BOOLEAN},
      </if>
      <if test="tagName != null">
        #{tagName,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.system.domain.entity.TagEntity">
    <!--@mbg.generated-->
    update T_TAG
    <set>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="tagType != null">
        TAG_TYPE = #{tagType,jdbcType=INTEGER},
      </if>
      <if test="isRecommend != null">
        IS_RECOMMEND = #{isRecommend,jdbcType=BOOLEAN},
      </if>
      <if test="tagName != null">
        TAG_NAME = #{tagName,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
    </set>
    where TAG_ID = #{tagId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.system.domain.entity.TagEntity">
    <!--@mbg.generated-->
    update T_TAG
    set COMPANY_ID = #{companyId,jdbcType=INTEGER},
      TAG_TYPE = #{tagType,jdbcType=INTEGER},
      IS_RECOMMEND = #{isRecommend,jdbcType=BOOLEAN},
      TAG_NAME = #{tagName,jdbcType=VARCHAR},
      COMMENTS = #{comments,jdbcType=VARCHAR}
    where TAG_ID = #{tagId,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="TAG_ID" keyProperty="tagId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TAG
    (COMPANY_ID, TAG_TYPE, IS_RECOMMEND, TAG_NAME, COMMENTS)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.companyId,jdbcType=INTEGER}, #{item.tagType,jdbcType=INTEGER}, #{item.isRecommend,jdbcType=BOOLEAN}, 
        #{item.tagName,jdbcType=VARCHAR}, #{item.comments,jdbcType=VARCHAR})
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2022-07-21-->
  <select id="selectByTagTypeAndCompanyIdAndIsRecommend" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_TAG
    <where>
      <if test="tagType != null">
        and TAG_TYPE=#{tagType,jdbcType=INTEGER}
      </if>
      <if test="companyId != null">
        and COMPANY_ID=#{companyId,jdbcType=INTEGER}
      </if>
      <if test="isRecommend != null">
        and IS_RECOMMEND=#{isRecommend,jdbcType=BOOLEAN}
      </if>
    </where>
  </select>
</mapper>