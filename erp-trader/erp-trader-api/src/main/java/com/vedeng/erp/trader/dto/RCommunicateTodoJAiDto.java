
package com.vedeng.erp.trader.dto;

import com.vedeng.common.core.base.BaseDto;
import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 通话待办关联AI结果表
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class RCommunicateTodoJAiDto extends BaseDto {
    /**
     * 主键
     */
    private Integer communicateInfoId;

    /**
     * 交易者ID
     */
    private Integer traderId;

    /**
     * 客户ID
     */
    private Integer traderCustomerId;

    /**
     * 客户名称
     */
    private String traderCustomerName;

    /**
     * 沟通记录ID
     */
    private Integer communicateRecordId;

    /**
     * 商机id
     */
    private Integer businessId;

    /**
     * 商机编号
     */
    private String businessNo;

    /**
     * 是否生成商机
     */
    private Integer createBusinessChange;

    /**
     * 是否更新客户标签
     */
    private Integer updateTraderSign;


    /**
     * 是否同步联系人职位信息
     */
    private Integer syncContactPosition;

    /**
     * 客户联系人ID
     */
    private Integer customerContactId;

    /**
     * 联系人职位
     */
    private String customerContactPosition;


    /**
     * 选中的摘要
     */
    private List<String> checkDigest;

    /**
     * 选中的摘要
     */
    private List<Integer> checkDigestId;


}