package com.vedeng.crm.config;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/8/13
 */
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.model.ConfigChange;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.vedeng.crm.constant.CrmConstants;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
public class ApolloConfigListener {

    @ApolloConfig
    private Config config;

    @ApolloConfigChangeListener
    public void onChange(ConfigChangeEvent changeEvent) {
        for (String key : changeEvent.changedKeys()) {
            ConfigChange change = changeEvent.getChange(key);
            System.out.printf("Found change - key: %s, oldValue: %s, newValue: %s, changeType: %s%n",
                    change.getPropertyName(), change.getOldValue(), change.getNewValue(), change.getChangeType());
            if("staticResourceVersion".equals(key)){
                CrmConstants.setStaticResourceVersion(change.getNewValue());
            }

            if("checkCrmIsInner".equals(key)){
                CrmConstants.setCheckCrmIsInner(change.getNewValue());
            }

        }
    }

    @PostConstruct
    public void init() {
        String configValue = config.getProperty("staticResourceVersion", "1.0.0");
        CrmConstants.setStaticResourceVersion(configValue);

        String checkCrmIsInner = config.getProperty("checkCrmIsInner", "");
        CrmConstants.setCheckCrmIsInner(checkCrmIsInner);
    }
}