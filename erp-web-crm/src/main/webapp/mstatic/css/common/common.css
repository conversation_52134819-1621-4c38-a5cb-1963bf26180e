@import 'font/style.css';
html,
body {
  height: 100%;
  width: 100%;
  color: #333;
  font: 12px/1.5 'Microsoft YaHei', 'arial', 'sans-serif';
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
  background-color: #f5f7fa;
}
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: normal;
}
a {
  text-decoration: none;
  color: #333;
}
a:active,
a:visited {
  color: #333;
}
ul {
  list-style: none;
}
button,
input,
optgroup,
select,
textarea {
  border: 0;
  outline: 0;
  font: inherit;
  color: inherit;
}
img {
  border: 0;
  vertical-align: middle;
}
input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  color: #333;
  /*输入框提示语的字体样式*/
  font-size: 14px;
}
input[type=search] {
  -webkit-appearance: textfield;
  -webkit-box-sizing: content-box;
  font-family: inherit;
  font-size: 100%;
}
input::-webkit-search-decoration,
input::-webkit-search-cancel-button {
  display: none;
}
.text-line-1 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  word-break: break-all;
}
.text-line-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-all;
}
.text-line-3 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  word-break: break-all;
}
.text-line-7 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 7;
  -webkit-box-orient: vertical;
  word-break: break-all;
}
.page-wrap {
  max-width: 768px;
  margin: 0 auto;
  min-width: 360px;
}
#page-container {
  opacity: 0;
}
#page-container.show {
  opacity: 1;
}
.global__loading__wrap {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
@keyframes loading {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
.global__loading__wrap .icon-loading {
  font-size: 64px;
  color: #fff;
  animation: loading 2s linear infinite;
  z-index: 9999;
}
.global__loading__wrap .global__loading__p {
  font-size: 14px;
  color: #fff;
  margin-top: 10px;
}
