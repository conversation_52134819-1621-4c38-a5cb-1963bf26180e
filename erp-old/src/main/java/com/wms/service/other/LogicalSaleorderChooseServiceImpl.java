package com.wms.service.other;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.newtask.data.dao.SaleorderDataMapper;
import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesGoods;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.authorization.model.Region;
import com.vedeng.authorization.model.User;
import com.vedeng.common.annotation.MethodLock;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.OrderConstant;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.constant.goods.GoodsConstants;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.common.util.StringUtil;
import com.vedeng.erp.saleorder.api.SaleorderSyncService;
import com.vedeng.erp.saleorder.enums.PCOrderStatusEnum;
import com.vedeng.erp.saleorder.enums.SaleorderSyncEnum;
import com.vedeng.finance.model.SaleorderData;
import com.vedeng.flash.enums.WmsSendOrderTypeEnum;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.model.CoreSku;
import com.vedeng.logistics.dao.LogisticsMapper;
import com.vedeng.logistics.dao.WarehouseGoodsOperateLogMapper;
import com.vedeng.logistics.model.Logistics;
import com.vedeng.logistics.model.WarehouseStock;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.BuyorderGoods;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.model.SaleorderModifyApply;
import com.vedeng.order.model.dto.RBuyorderSaleorderDto;
import com.vedeng.order.model.vo.SaleorderGoodsVo;
import com.vedeng.stock.api.stock.dto.StockInfoDto;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.vedeng.system.service.OrgService;
import com.vedeng.system.service.RegionService;
import com.vedeng.trader.dao.TraderAddressMapper;
import com.vedeng.trader.model.TraderAddress;
import com.wms.constant.*;
import com.wms.dao.WmsLogicalOrdergoodsMapper;
import com.wms.dao.WmsSendOrderMapper;
import com.wms.dto.*;
import com.wms.model.po.TraderLogical;
import com.wms.model.po.WmsLogicalOrdergoods;
import com.wms.model.po.WmsSendOrder;
import com.wms.service.CancelTypeService;
import com.wms.service.LogicalSaleorderChooseService;
import com.wms.service.WMSInterfaceFactory;
import com.wms.service.WmsInterface;
import com.wms.service.stockcalculate.OutputOrderAuditPassCaculateImpl;
import com.wms.service.util.WmsCommonUtil;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName LogicalSaleorderServiceImpl.java
 * @Description TODO
 * @createTime 2020年07月23日 16:31:00
 */
@Service("logicalSaleorderChooseService")
public class LogicalSaleorderChooseServiceImpl implements LogicalSaleorderChooseService {
    Logger logger= LoggerFactory.getLogger(LogicalSaleorderChooseService.class);
    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Autowired
    private WarehouseStockService warehouseStockService;

    @Resource
    private WmsLogicalOrdergoodsMapper wmsLogicalOrdergoodsMapper;

    @Autowired
    private WMSInterfaceFactory wmsInterfaceFactory;

    @Autowired
    private OutputOrderAuditPassCaculateImpl outputOrderAuditPassCaculateImpl;

    @Resource
    private AfterSalesGoodsMapper afterSalesGoodsMapper;

    @Resource
    private AfterSalesMapper  afterSalesMapper;

    @Autowired
    private OrgService orgService;

    @Resource
    private TraderAddressMapper traderAddressMapper;

    @Autowired
    private RegionService regionService;

    @Autowired
    private WmsSendOrderMapper wmsSendOrderMapper;

    @Resource
    private BuyorderGoodsMapper buyorderGoodsMapper;

    @Resource
    private WarehouseGoodsOperateLogMapper warehouseGoodsOperateLogMapper;

    @Resource
    private LogisticsMapper logisticsMapper;

    @Autowired
    private CancelTypeService cancelTypeService;

    @Autowired
    private SaleorderDataMapper saleorderDataMapper;

    @Autowired
    private CoreSkuMapper coreSkuMapper;

    @Autowired
    private SaleorderSyncService saleorderSyncService;

    /**
     * 销售订单(账期类型)付款计划
     */
    public static final List PAYMENT_TYPE_FOR_PAYMENT_DAYS = Collections.unmodifiableList(Lists.newArrayList(
            OrderConstant.PREPAY_80_PERCENT,
            OrderConstant.PREPAY_50_PERCENT ,
            OrderConstant.PREPAY_30_PERCENT,
            OrderConstant.PREPAY_0_PERCENT));

    @Value("${TRADER_LOGICAL_JSON_STR}")
    private String traderLogicalJsonStr;

    @Value("${not_sell_trader_ids}")
    private String notSellTraderIds;

    @Value("${wms_tongxingdan_amount}")
    private String wmsTongxingdanAmount;

    @Transactional(rollbackFor = Exception.class)
    @Override
    @MethodLock(field = "saleorderId",className = Saleorder.class)
    public void chooseLogicalSaleorder(Saleorder saleorder,User user) throws Exception {
            if(saleorder == null || saleorder.getSaleorderId() == null){
                logger.info("chooseLogicalSaleorder saleorderId is null");
                return;
            }
            Saleorder order = saleorderMapper.getSaleOrderById(saleorder.getSaleorderId());
            logger.info("chooseLogicalSaleorder no:{},orderTYpe:{},status:{},payment:{}",
                    order.getSaleorderNo(),order.getOrderType(),order.getStatus(),order.getPaymentStatus());

            // 账期销售订单校验拦截
            if (!accountPeriodOrderVerificationInterception(order)) {
                logger.info("订单{}：被账期销售订单校验拦截，不下发wms",order.getSaleorderNo());
                return;
            }

            if (!OrderConstant.ORDER_TYPE_HC.equals(order.getOrderType())) {
                if (!OrderConstant.ORDER_STATUS_VALID.equals(order.getStatus()) ||
                        !OrderConstant.ORDER_ALL_PAYMENT.equals(order.getPaymentStatus())) {
                    // 如果是部分付款，订单中的虚拟商品自动全部发货
                    if (OrderConstant.ORDER_SOME_PAYMENT.equals(order.getPaymentStatus())) {
                        List<SaleorderGoodsVo> saleOrderGoodsVoList = saleorderGoodsMapper.getSaleorderGoodsVoListBySaleorderId(order);
                        handleVirtualGoods(saleOrderGoodsVoList);
                    }
                    logger.info("订单{}：非待审核状态，不下发wms",order.getSaleorderNo());
                    return;
                }
            }
            if(OrderConstant.ORDER_STATUS_CLOSE.equals(order.getStatus())){
                logger.info("订单{}：已关闭，不下发wms",order.getSaleorderNo());
                return;
            }

            List<SaleorderGoodsVo> saleorderGoodsVoList = saleorderGoodsMapper.getSaleorderGoodsVoListBySaleorderId(order);

            List<String> skuList = saleorderGoodsVoList.stream().map(item -> item.getSku()).collect(Collectors.toList());
            List<Integer> relateIdList = saleorderGoodsVoList
                    .stream().map(SaleorderGoodsVo::getSaleorderGoodsId)
                    .collect(Collectors.toList());
            //如果之前保存过则不保存
            Map<Integer,WmsLogicalOrdergoods> oldLogicalMap = wmsLogicalOrdergoodsMapper.getLogicalOrderInfoGroupByRelateId(relateIdList,WmsLogicalOperateTypeEnum.SALEORDER_TYPE.getOperateTypeCode())
                    .stream()
                    .collect(Collectors.toMap(WmsLogicalOrdergoods::getRelatedId,item->item));
            logger.info("start查询aliyun库存数据,order:{}",order.getSaleorderNo());
            //逻辑库存数据
            Map<String,WarehouseStock> logicalStockInfo = warehouseStockService.getLogicalStockMapInfo(skuList);
            logger.info("end查询aliyun库存数据,order:{}",order.getSaleorderNo());
            //逻辑库商品信息
            List<WmsLogicalOrdergoods> insertLogicalGoodsList = new ArrayList<>();
            List<WmsLogicalOrdergoods> dealStockLogicalGoodsList = new ArrayList<>();
            //虚拟商品订单
            List<SaleorderGoodsVo> saleVirtualGoodList = new ArrayList<>();

            // VDERP-13290 【金蝶】费用商品不进订单物流信息模块
            // 由于之前 虚拟商品 是写死在代码中的  GoodsConstants.VIRTUAL_GOODS：V127063，V251526，V256675，V253620，V251462，V140633
            // V127063——运费   V251526——加急运费   V256675——冷链运费   V253620——售后退换货手续费   V251462——国产品牌 安调费   V140633——售后维修费迈瑞
            // 现在增加 过滤 从所有的虚拟商品 中过滤  ; 虚拟商品数据: V_CORE_SKU  中 IS_VIRTURE_SKU = 1 并且 STATUS = 1
            List<CoreSku> allVirtureSkuList = coreSkuMapper.getAllVirtureSkuList();
            List<Integer> virtureSkuIdList = new ArrayList<>();
            for(CoreSku coreSku : allVirtureSkuList){
                virtureSkuIdList.add(coreSku.getSkuId());
            }

            for (SaleorderGoodsVo saleorderGoodsVo : saleorderGoodsVoList) {
                // VDERP-13290 【金蝶】费用商品不进订单物流信息模块 添加 判断条件  virtureSkuIdList.contains(saleorderGoodsVo.getGoodsId())
                if(GoodsConstants.VIRTUAL_GOODS.contains(saleorderGoodsVo.getGoodsId()) || virtureSkuIdList.contains(saleorderGoodsVo.getGoodsId())) {
                    // 订单中的虚拟商品不下传WMS
                    saleVirtualGoodList.add(saleorderGoodsVo);
                    logger.info("订单{}：虚拟商品{}，不下发wms",order.getSaleorderNo(),saleorderGoodsVo.getGoodsName());
                    continue;
                }
                //可出库数量
                int resultNum = saleorderGoodsVo.getNum() - getHistoryReturnNum(saleorderGoodsVo.getSaleorderGoodsId()) - saleorderGoodsVo.getDeliveryNum();
                saleorderGoodsVo.setNum(resultNum);
                //VDERP-7259 【医械购】部分客户的销售单下传WMS的默认逻辑变更
                saleorderGoodsVo.setTraderId(order.getTraderId());
//                this.chooseLogicalOrder(logicalStockInfo, wmsLogicalOrdergoodsList, saleorderGoodsVo);
                WmsLogicalOrdergoods wmsLogicalOrdergoods = oldLogicalMap.get(saleorderGoodsVo.getSaleorderGoodsId());
                //直变普重新选择
                if (wmsLogicalOrdergoods == null && saleorderGoodsVo.getDeliveryDirect().equals(0)){
                    this.chooseLogicalOrder(logicalStockInfo, insertLogicalGoodsList, saleorderGoodsVo);
                    logger.info("chooseLogicalSaleorder no:{},info:{}",order.getSaleorderNo(),JSON.toJSONString(insertLogicalGoodsList));
                }else if(wmsLogicalOrdergoods != null && saleorderGoodsVo.getDeliveryDirect().equals(1)){
                    logger.info("chooseLogicalSaleorder no:{},deleterelated:{}",order.getSaleorderNo(),saleorderGoodsVo.getSaleorderGoodsId());
                    //普变直逻辑删
                    WmsLogicalOrdergoods search = new WmsLogicalOrdergoods();
                    search.setOperateType(WmsLogicalOperateTypeEnum.SALEORDER_TYPE.getOperateTypeCode());
                    search.setRelatedId(saleorderGoodsVo.getSaleorderGoodsId());

                    List<WmsLogicalOrdergoods> deletGoodsList = wmsLogicalOrdergoodsMapper.getLogicalInfoByTypeAndRelate(search);
                    for (WmsLogicalOrdergoods logicalOrdergoods : deletGoodsList) {

                        WmsLogicalOrdergoods stock = new WmsLogicalOrdergoods();
                        stock.setOccupyNum(-logicalOrdergoods.getOccupyNum());
                        stock.setSku(logicalOrdergoods.getSku());
                        stock.setLogicalWarehouseId(logicalOrdergoods.getLogicalWarehouseId());
                        dealStockLogicalGoodsList.add(stock);

                        WmsLogicalOrdergoods updateGoods = new WmsLogicalOrdergoods();
                        updateGoods.setLogicalOrderGoodsId(logicalOrdergoods.getLogicalOrderGoodsId());
                        updateGoods.setIsDelete(1);
                        updateGoods.setOccupyNum(0);
                        wmsLogicalOrdergoodsMapper.updateByPrimaryKeySelective(updateGoods);
                    }

                }
            }
            //todo 订单中的虚拟商品自动全部发货
            handleVirtualGoods(saleorderGoodsVoList);
            logger.info("chooseLogicalSaleorder no:{},insertinfo:{}",order.getSaleorderNo(),JSON.toJSON(insertLogicalGoodsList));

            insertWmsLogicalOrderGodos(insertLogicalGoodsList,user);

            if(OrderConstant.ORDER_ALL_PAYMENT.equals(order.getPaymentStatus()) &&
                    OrderConstant.ORDER_STATUS_VALID.equals(order.getStatus())
                    // 若销售订单中只包含特殊商品，则整个订单不下传WMS
                    && (saleVirtualGoodList.size() == 0 || saleVirtualGoodList.size()!= saleorderGoodsVoList.size()) ){
                List<WmsLogicalOrdergoods> saleorderLogicalChooseInfoByNo = getWmsLogicalOrdergoodsByOrderNo(order);
                putSaleOrderOutput(order,saleorderLogicalChooseInfoByNo,user);

            }
            dealStockLogicalGoodsList.addAll(insertLogicalGoodsList);
            //同步库存服务
            sysnchStock(order, dealStockLogicalGoodsList);

    }

    @Override
    public List<WmsLogicalOrdergoods> getWmsLogicalOrdergoodsByOrderNo(Saleorder order) {
        List<WmsLogicalOrdergoods> saleorderLogicalChooseInfoByNo = wmsLogicalOrdergoodsMapper.getSaleorderLogicalChooseInfoByNo(order.getSaleorderNo());
        //重新下发未出库完成的商品
        Iterator<WmsLogicalOrdergoods> iterator = saleorderLogicalChooseInfoByNo.iterator();
        while (iterator.hasNext()) {
            WmsLogicalOrdergoods next = iterator.next();
            next.setNum(next.getOccupyNum());
            if (next.getNum().equals(0)) {
                iterator.remove();
            }
        }
        return saleorderLogicalChooseInfoByNo;
    }


    /**
     *  销售订单中，只要订单全部付款或部分付款，则订单中的虚拟商品自动全部发货销售订单中，只要订单全部付款或部分付款，则订单中的虚拟商品自动全部发货
     * @param saleOrderGoodsVoList
     * @return 销售订单中包含特殊商品
     */
    private void handleVirtualGoods(List<SaleorderGoodsVo> saleOrderGoodsVoList) {
        try {
            if (saleOrderGoodsVoList.size() <= 0) {
                return ;
            }
            long logTime = System.currentTimeMillis();

            int countVirtual = 0;
            int countInvisibleVirtual = 0;
            List<CoreSku> invisibleSkuList = coreSkuMapper.getInvisibleSkuList();
            List<Integer> invisibleList = invisibleSkuList.stream().map(CoreSku::getSkuId).collect(Collectors.toList());
            List<CoreSku> allVirtureSkuList = coreSkuMapper.getAllVirtureSkuList();
            List<Integer> allvirSku = allVirtureSkuList.stream().map(CoreSku::getSkuId).collect(Collectors.toList());
            for (SaleorderGoodsVo saleorderGoodsVo : saleOrderGoodsVoList) {
                if (invisibleList.contains(saleorderGoodsVo.getGoodsId())) {
                    Integer saleOrderGoodsId = saleorderGoodsVo.getSaleorderGoodsId();
                    // 虚拟商品自动全部发货
                    SaleorderGoods updateSaleOrderGood = new SaleorderGoods();
                    updateSaleOrderGood.setSaleorderGoodsId(saleOrderGoodsId);
                    // 发货状态 全部发货:2
                    updateSaleOrderGood.setDeliveryStatus(2);
                    // 发货时间
                    updateSaleOrderGood.setDeliveryTime(logTime);
                    // 已发货数量
                    updateSaleOrderGood.setDeliveryNum(saleorderGoodsVo.getNum());
                    if(!OrderConstant.ORDER_ALL_DELIVERY.equals(saleorderGoodsVo.getDeliveryStatus())){
                        logger.info("更新不可见虚拟商品的发货状态，订单号：{}，商品明细id:{}，sku:{}",saleorderGoodsVo.getSaleorderNo(),saleorderGoodsVo.getSaleorderGoodsId(),saleorderGoodsVo.getSku());
                        saleorderGoodsMapper.updateByPrimaryKeySelective(updateSaleOrderGood);
                    }

                    countInvisibleVirtual ++;
                }
                if (allvirSku.contains(saleorderGoodsVo.getGoodsId())|| invisibleList.contains(saleorderGoodsVo.getGoodsId())){
                    countVirtual ++;
                }
            }

            if (countVirtual != 0 && countVirtual == saleOrderGoodsVoList.size()) {
                Saleorder saleOrderById = saleorderMapper.getSaleOrderById(saleOrderGoodsVoList.get(0).getSaleorderId());
                if(OrderConstant.ORDER_ALL_DELIVERY.equals(saleOrderById.getDeliveryStatus())){
                    if (countInvisibleVirtual != 0 && countInvisibleVirtual == saleOrderGoodsVoList.size()) {
                        Saleorder updateSaleOrder = new Saleorder();
                        updateSaleOrder.setSaleorderId(saleOrderGoodsVoList.get(0).getSaleorderId());
                        updateSaleOrder.setConfirmationFormAudit(2);
                        saleorderMapper.updateByPrimaryKeySelective(updateSaleOrder);
                        logger.info("销售单下只有虚拟商品且无需采购,下发WMS时订单更新为全部收发货,确认单审核状态更新为审核通过:{}", JSON.toJSONString(updateSaleOrder));
                    }
                    return;
                }
                for (SaleorderGoodsVo saleorderGoodsVo : saleOrderGoodsVoList) {
                    if (invisibleList.contains(saleorderGoodsVo.getGoodsId())) {
                        Integer saleOrderGoodsId = saleorderGoodsVo.getSaleorderGoodsId();
                        // 虚拟商品自动全部收货
                        SaleorderGoods updateSaleOrderGood = new SaleorderGoods();
                        updateSaleOrderGood.setSaleorderGoodsId(saleOrderGoodsId);
                        // 收货状态 全部收货:2
                        updateSaleOrderGood.setArrivalStatus(2);
                        // 收货时间
                        updateSaleOrderGood.setArrivalTime(logTime);
                        //更新收货状态
                        logger.info("更新不可见虚拟商品的收货状态，订单号：{}，商品明细id:{}，sku:{}",saleorderGoodsVo.getSaleorderNo(),saleorderGoodsVo.getSaleorderGoodsId(),saleorderGoodsVo.getSku());
                        saleorderGoodsMapper.updateByPrimaryKeySelective(updateSaleOrderGood);

                    }
                }
                if (countInvisibleVirtual != 0 && countInvisibleVirtual == saleOrderGoodsVoList.size()){
                    Saleorder updateSaleOrder = new Saleorder();
                    updateSaleOrder.setSaleorderId(saleOrderGoodsVoList.get(0).getSaleorderId());
                    updateSaleOrder.setDeliveryStatus(2);
                    updateSaleOrder.setArrivalStatus(2);
                    updateSaleOrder.setArrivalTime(logTime);
                    updateSaleOrder.setDeliveryTime(logTime);
                    updateSaleOrder.setConfirmationFormAudit(2);
                    logger.info("销售单下只有虚拟商品且无需采购,下发WMS时订单更新为全部收发货,确认单审核状态更新为审核通过:{}", JSON.toJSONString(updateSaleOrder));
                    saleorderMapper.updateByPrimaryKeySelective(updateSaleOrder);
                    saleorderSyncService.syncSaleorderStatus2Mjx(saleOrderGoodsVoList.get(0).getSaleorderId(), PCOrderStatusEnum.FINISH, SaleorderSyncEnum.PUSH_VS);
                }

            }

        } catch (Exception e) {
            logger.error("handleVirtualGoods error", e);
        }

    }

    private void sysnchStock(Saleorder order, List<WmsLogicalOrdergoods> wmsLogicalOrdergoodsList) throws Exception {
        if(CollectionUtils.isEmpty(wmsLogicalOrdergoodsList)){return;}
        List<StockCalculateDto> stockCalculateList  = new ArrayList<>();
        wmsLogicalOrdergoodsList.stream().forEach( wmsLogicalOrdergoods -> {
            StockCalculateDto stockCalculateDto = new StockCalculateDto();
            if(wmsLogicalOrdergoods.getOccupyNum().equals(0)){
                return;
            }
            stockCalculateDto.setSku(wmsLogicalOrdergoods.getSku());
            stockCalculateDto.setOccupyNum(wmsLogicalOrdergoods.getOccupyNum());
            stockCalculateDto.setStockNum(0);
            stockCalculateDto.setLogicalWarehouseId(wmsLogicalOrdergoods.getLogicalWarehouseId());
            stockCalculateList.add(stockCalculateDto);
        });

        Integer actionId = order.getActionId();
        //库存策略计算
        StockInfoDto stockInfoDto  = new StockInfoDto();
        stockInfoDto.setRelatedNo(order.getSaleorderNo());
        stockInfoDto.setOccupyType(order.getOperateType());
        List<WarehouseDto> warehouseDtos = outputOrderAuditPassCaculateImpl.calculateStockInfo(stockCalculateList);
        for (WarehouseDto warehouseDto : warehouseDtos) {
            if(LogicalEnum.HDC.getLogicalWarehouseId().equals(warehouseDto.getLogicalWarehouseId())){
                warehouseDto.setActionId(actionId);
            }
        }
        stockInfoDto.setWarehouseStockList(warehouseDtos);

        //更新库存服务
        warehouseStockService.updateStockInfo(stockInfoDto);

    }
    //获取历史完结退货数量
    private Integer getHistoryReturnNum(Integer saleorderGoodsId) {
        return   afterSalesGoodsMapper.getSaleorderAftersaleReturnGoods(saleorderGoodsId);
    }
    //获取当前进行中的退货数量
    private Integer getNowReturnNum(Integer saleorderGoodsId) {
        return afterSalesGoodsMapper.getSaleorderAfterNowReturnNum(saleorderGoodsId);
    }

    /**
     * @Description 账期销售订单校验拦截
     * @Param saleorder
     * @Return {@link boolean} true代表校验通过，false代表校验不通过
     */
    private boolean accountPeriodOrderVerificationInterception(Saleorder saleorder) {
        logger.info("订单{}：进入账期销售订单校验拦截", saleorder);
        // 订单类型为销售订单&&付款方式为先货后款 才进行拦截
        if (PAYMENT_TYPE_FOR_PAYMENT_DAYS.contains(saleorder.getPaymentType())) {
            logger.info("订单{}：开始被账期销售订单校验拦截", saleorder.getSaleorderNo());
            try {
                // 1.客户是否在白名单
                int traderPeriodWhiteResult = saleorderMapper.getTraderPeriodWhiteByTraderId(saleorder.getTraderId());
                logger.info("订单{}：客户是否在白名单的查询结果为:{}", saleorder.getSaleorderNo(), traderPeriodWhiteResult);
                if (traderPeriodWhiteResult > 0) {
                    logger.info("订单{}：账期销售订单校验通过，此订单的客户在白名单中", saleorder.getSaleorderNo());
                    return true;
                }
                // 2.合同是否回传且审核通过
                SaleorderData saleorderData = saleorderDataMapper.getSaleOrderDataBySaleOrderId(saleorder.getSaleorderId());
                if (Objects.isNull(saleorderData)) {
                    logger.info("订单{}：查询saleOrderData信息为null", saleorder.getSaleorderNo());
                    return false;
                }
                Integer contractVerifyStatus = saleorderData.getContractVerifyStatus();
                logger.info("订单{}：合同审核状态的查询结果为：{}", saleorder.getSaleorderNo(), contractVerifyStatus);
                if (OrderConstant.CONTRACT_VERIFY_PASS.equals(contractVerifyStatus)) {
                    logger.info("订单{}：账期销售订单校验通过，此订单的合同回传且审核通过", saleorder.getSaleorderNo());
                    return true;
                }
                // 3.订单未收金额<=0 && 客户实付金额>0
                // 销售订单的实际金额
                BigDecimal realAmount = saleorderMapper.getRealTotalAmountExceptAfterSalesFinished(saleorder.getSaleorderId());
                logger.info("订单{}：实际金额为：{}", saleorder.getSaleorderNo(), realAmount);
                // 订单已收款金额（不含账期）
                BigDecimal paymentAmount = saleorderMapper.getSaleorderPaymentAmount(saleorder.getSaleorderId());
                logger.info("订单{}：已收款金额（不含账期）为：{}", saleorder.getSaleorderNo(), paymentAmount);
                // 订单账期金额（已收款）
                BigDecimal periodAmount = saleorderMapper.getPeriodAmount(saleorder.getSaleorderId());
                logger.info("订单{}：账期金额（已收款）为：{}", saleorder.getSaleorderNo(), periodAmount);
                // 剩余账期未还金额
                BigDecimal lackAccountPeriodAmount = saleorderMapper.getSaleorderLackAccountPeriodAmount(saleorder.getSaleorderId());
                logger.info("订单{}：剩余账期未还金额为：{}", saleorder.getSaleorderNo(), lackAccountPeriodAmount);
                // 销售订单退还余额的金额
                BigDecimal refundBalanceAmount = afterSalesMapper.getRefundBalanceAmountBySaleorderId(saleorder.getSaleorderId());
                logger.info("订单{}：退还余额的金额为：{}", saleorder.getSaleorderNo(), refundBalanceAmount);
                // 客户实付金额
                BigDecimal realPayAmount = paymentAmount.add(periodAmount).subtract(lackAccountPeriodAmount).subtract(refundBalanceAmount);
                logger.info("订单{}：客户实付金额为：{}", saleorder.getSaleorderNo(), realPayAmount);
                // 未收金额
                BigDecimal outstandingAmount = realAmount.subtract(realPayAmount);
                logger.info("订单{}：未收金额为：{}", saleorder.getSaleorderNo(), outstandingAmount);
                BigDecimal zero = BigDecimal.ZERO;
                if (realPayAmount.compareTo(zero) > 0 && outstandingAmount.compareTo(zero) <= 0) {
                    logger.info("订单{}：账期销售订单校验通过，此订单未收金额<=0且客户实付金额>0", saleorder.getSaleorderNo());
                    return true;
                }
                logger.info("订单{}：被账期销售订单校验拦截,校验条件都不满足（白名单、合同回传审核通过、订单未收金额<=0 && 客户实付金额>0）", saleorder.getSaleorderNo());
                return false;
            } catch (Exception e) {
                logger.error("订单{}：账期销售订单校验时发生异常={}", saleorder.getSaleorderNo(), e.getStackTrace());
                return false;
            }
        }
        return true;
    }


    private void putSaleOrderOutput(Saleorder saleorder, List<WmsLogicalOrdergoods> wmsLogicalOrdergoodsList, User user) throws Exception {
        logger.info("销售单下发WMS 单号:{},paymentStatus:{},list:{}",saleorder.getSaleorderNo(),saleorder.getPaymentStatus(),JSON.toJSON(wmsLogicalOrdergoodsList));
        //订单状态不为已生效的情况下不下发wms
        if(!OrderConstant.ORDER_STATUS_PROCESSING.equals(saleorder.getStatus())){
            logger.info("订单{}生效状态为{}，不下发wms",saleorder.getSaleorderNo(),saleorder.getStatus());
            return;
        }
        if(CollectionUtils.isEmpty(wmsLogicalOrdergoodsList) ||
                !OrderConstant.ORDER_ALL_PAYMENT.equals(saleorder.getPaymentStatus()) ||
                saleorder.getDeliveryStatus().equals(2)){
            return;
        }

        List<SaleorderGoodsVo> saleorderGoodsVoList = saleorderGoodsMapper.getSaleorderGoodsVoListBySaleorderId(saleorder);

        //货齐发货 需要所有专项发货关联的采购单都审核通过才可以下发
        if(SysOptionConstant.NO_PART_DELIVER.equals(saleorder.getDeliveryType())){
            if(CollectionUtils.isNotEmpty(saleorderGoodsVoList)){
                for(SaleorderGoodsVo saleorderGoodsVo : saleorderGoodsVoList){

                    //跳过非专项发货
                    if(saleorderGoodsVo.getSpecialDelivery() == 0){
                        continue;
                    }

                    //专项发货 判断关联的采购单是否已经审核通过
                    if(!saleOrderRelateBuyOrderAuditPass(saleorderGoodsVo)){
                        logger.info("货齐发后销售单关联的采购单未完全审核通过不下发WMS 单号:{}",saleorder.getSaleorderNo());
                        return;
                    }
                }
            }
        }

        //存在正在进行中的订单修改并且此修改申请需要重新下发wms,拦截下传WMS订单
        List<SaleorderModifyApply> saleorderModifyApplyList = saleorderMapper.getSaleorderModifyApplyList(saleorder.getSaleorderId());
        if(CollectionUtils.isNotEmpty(saleorderModifyApplyList)){
            Optional<SaleorderModifyApply> optional = saleorderModifyApplyList.stream().filter(item -> item.getVerifyStatus().equals(0)).findFirst();
            if(optional.isPresent() && optional.get().getIsWmsCancel().equals(1)){
                logger.info("销售单存在物流信息订单修改申请不下发  单号:{}",saleorder.getSaleorderNo());
                return;
            }
        }

        PutSaleOrderDto putSaleOrderDto = new PutSaleOrderDto();

        putSaleOrderDto.setDocNo(WmsCommonUtil.addTimestampForOrderNo(saleorder.getSaleorderNo()));
        //账期客户或金额大于1w需要提供签回单
        //VDERP-12272  2022-10-10 剔除非真实（EQ、样品赠送)
        boolean moreThan10000= new BigDecimal(wmsTongxingdanAmount).compareTo(saleorder.getTotalAmount()) <= 0;
        boolean periodOrder= saleorder.getHaveAccountPeriod() == 1;
        boolean eqTrader= ArrayUtils.contains(notSellTraderIds.split(","),saleorder.getTraderId()+"");
        if((moreThan10000||periodOrder) &&!eqTrader ){
            putSaleOrderDto.setHedi01("Y");
        }
        if (periodOrder) {
            putSaleOrderDto.setHedi08("Y");
        } else {
            putSaleOrderDto.setHedi08("N");
        }
        putSaleOrderDto.setOrderType(WmsInterfaceOrderType.OUT_SALE_OUT);
        putSaleOrderDto.setOrderTime(DateUtil.convertString(saleorder.getSatisfyDeliveryTime(),"yyyy-MM-dd HH:mm:ss"));
        putSaleOrderDto.setExpectedShipmentTime1(DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss"));
        if(null!= saleorder.getDeliveryDelayTime()){
            putSaleOrderDto.setExpectedShipmentTime2(saleorder.getDeliveryClaim() == null || ErpConst.ZERO.equals(saleorder.getDeliveryClaim())  ? "9999-12-21 23:59:59" : DateUtil.convertString(saleorder.getDeliveryDelayTime(),"yyyy-MM-dd HH:mm:ss"));
        }else{
            putSaleOrderDto.setExpectedShipmentTime2("9999-12-21 23:59:59");
        }

        User user1 = orgService.getTraderUserAndOrgByTraderId(saleorder.getTraderId(), 1);// 1客户，2供应商
        putSaleOrderDto.setSoReferenceA(StringUtil.isEmpty(user1.getOrgName()) ? "/" : user1.getOrgName());
        putSaleOrderDto.setSoReferenceB(user1.getUsername());
        //客户ID
        putSaleOrderDto.setConsigneeId(saleorder.getTraderId().toString());
        putSaleOrderDto.setConsigneeName(saleorder.getTraderName());
        int areaId;
        if(saleorder.getTakeTraderAreaId() != null && !saleorder.getTakeTraderAreaId().equals(0)){
            areaId = saleorder.getTakeTraderAreaId();
            if(saleorder.getTakeTraderAddressId() != null && !saleorder.getTakeTraderAddressId().equals(0)) {
                TraderAddress addressInfo = traderAddressMapper.getAddressInfoById(saleorder.getTakeTraderAddressId(), null);
                if (addressInfo != null && (addressInfo.getAreaId() != null && !addressInfo.getAreaId().equals(0))) {
                    int areaId2 = addressInfo.getAreaId();
                    if (areaId2 != areaId) {
                        areaId = areaId2;
                        logger.warn(saleorder.getSaleorderNo() + "下发wms地址时，地址id和地区id不一致，地址id：" + saleorder.getTakeTraderAddressId() + "，地区id：" + saleorder.getTakeTraderAreaId());
                    }
                }
            }
        }else{
            if(saleorder.getTakeTraderAddressId() == null || saleorder.getTakeTraderAddressId().equals(0)){
                throw new Exception("销售单收货地址id为空下发WMS失败单号:"+saleorder.getSaleorderNo());
            }
            TraderAddress addressInfo = traderAddressMapper.getAddressInfoById(saleorder.getTakeTraderAddressId(), null);
            if(addressInfo == null || addressInfo.getAreaId().equals(0)){
                throw new Exception("销售单收货地址为空下发WMS失败单号:"+saleorder.getSaleorderNo());
            }
            areaId = addressInfo.getAreaId();
        }
        if(areaId == 0){
            throw new Exception("销售单收货地址id为0下发WMS失败单号:"+saleorder.getSaleorderNo());
        }
        List<Region> regionList = regionService.getRegionInfoByMinRegionId(areaId);
        if(CollectionUtils.isEmpty(regionList)){
            throw new Exception("销售单收货地区为空下发WMS失败单号:"+saleorder.getSaleorderNo());
        }
        putSaleOrderDto.setConsigneeProvince(regionList.size() > 0 ? regionList.get(0).getRegionName(): "");
        putSaleOrderDto.setConsigneeCity(regionList.size() > 1 ? regionList.get(1).getRegionName(): "");
        putSaleOrderDto.setConsigneeDistrict(regionList.size() > 2 ? regionList.get(2).getRegionName(): "");

        putSaleOrderDto.setConsigneeAddress1(saleorder.getTakeTraderAddress());
        putSaleOrderDto.setConsigneeContact(saleorder.getTakeTraderContactName());
        putSaleOrderDto.setConsigneeTel1(saleorder.getTakeTraderContactMobile());
        putSaleOrderDto.setConsigneeTel2(saleorder.getTakeTraderContactTelephone());
        //发货类型默认正常发货
        putSaleOrderDto.setHedi07(ErpConst.ZERO.equals(saleorder.getDeliveryClaim()) ? "A" : "B");

        putSaleOrderDto.setNotes(saleorder.getLogisticsComments());
        //是否打印4联随货同行单
        putSaleOrderDto.setHedi03("Y");
        String printOutType = warehouseStockService.getPrintOutType(saleorder);
        if(StringUtil.isBlank(printOutType)){
            putSaleOrderDto.setHedi03("N");
        }
        //发货方式
        putSaleOrderDto.setHedi04(SysOptionConstant.NO_PART_DELIVER.equals(saleorder.getDeliveryType()) ? "Y" :"N");

        //VDERP-3633 【WMS】物流随货发送资质文件判断条件变更为B2B和科研购的订单通通不要资质文件
        //是否是医械购客户
        User orderUser = orgService.getTraderUserAndOrgByTraderId(saleorder.getTraderId(), 1);
        boolean hcOrgFlag = false;
        if(orderUser != null && null != orderUser.getOrgName() && orderUser.getOrgName().contains("医械购")){
            hcOrgFlag = true;
        }
        //医械购客户是否首次交易
//        List<Saleorder> orderPayList = saleorderMapper.getSaleorderRealAmountByTraderIdAndSaleorderId(saleorder);
        Map<String, Integer> goodsisExistmap = new HashMap<>();
        if(hcOrgFlag) {
            List<Saleorder> hcOrgorderList = saleorderMapper.getHcOrgValidStatus(saleorder);

            if (CollectionUtils.isEmpty(hcOrgorderList)) {
                putSaleOrderDto.setHedi05("Y");
            } else {
                //已存在发货商品
                for (Saleorder orderIsExist : hcOrgorderList) {
                    List<SaleorderGoods> isExitGoods = saleorderGoodsMapper.getGoodsSkuByOrderId(orderIsExist.getSaleorderId());
                    for (SaleorderGoods isExitGood : isExitGoods) {
                        if (isExitGood.getNum() - isExitGood.getAfterReturnNum() > 0) {
                            goodsisExistmap.put(isExitGood.getSku(), isExitGood.getGoodsId());
                        }
                    }
                }
                putSaleOrderDto.setHedi05("N");
            }
        }else{
            putSaleOrderDto.setHedi05("N");
        }
        //VDERP-3633 【WMS】物流随货发送资质文件判断条件变更为B2B和科研购的订单通通不要资质文件

        //VDERP-7428 物流公司
        if (saleorder.getLogisticsId() != null && !saleorder.getLogisticsId().equals(0)){
            Logistics logistics = logisticsMapper.getLogisticsById(saleorder.getLogisticsId());
            putSaleOrderDto.setCarrierId(logistics.getCarrierId());
            putSaleOrderDto.setCarrierName(logistics.getName());
        }



        List<PutSaleOrderGoodsDto> details = new ArrayList<>();
        for (WmsLogicalOrdergoods wmsLogicalOrdergoods : wmsLogicalOrdergoodsList) {
            PutSaleOrderGoodsDto putSaleOrderGoodsDto = new PutSaleOrderGoodsDto();
            putSaleOrderGoodsDto.setSku(wmsLogicalOrdergoods.getSku());
            putSaleOrderGoodsDto.setLotAtt08(LogicalEnum.getLogicalWarehouseCode(wmsLogicalOrdergoods.getLogicalWarehouseId()));
            putSaleOrderGoodsDto.setDedi07(wmsLogicalOrdergoods.getRelatedId().toString());

            Integer isExistGodosId = goodsisExistmap.get(wmsLogicalOrdergoods.getSku());
            if(isExistGodosId == null && hcOrgFlag){
                putSaleOrderGoodsDto.setDedi04("Y");
            }else{
                putSaleOrderGoodsDto.setDedi04("N");
            }
            //订单单价
            SaleorderGoods saleorderGoods = saleorderGoodsMapper.selectByPrimaryKey(wmsLogicalOrdergoods.getRelatedId());
            putSaleOrderGoodsDto.setDedi09(saleorderGoods.getRealPrice());


            //普通商品
            if (wmsLogicalOrdergoods.getSpecialDelivery() == null || wmsLogicalOrdergoods.getSpecialDelivery() == 0) {
                putSaleOrderGoodsDto.setQtyOrdered(wmsLogicalOrdergoods.getNum());
                putSaleOrderGoodsDto.setLotAtt07("*");
                if(putSaleOrderGoodsDto.getQtyOrdered() != null && putSaleOrderGoodsDto.getQtyOrdered() > 0){
                    details.add(putSaleOrderGoodsDto);
                }
            }else{
                //专项商品下发
                if (specialPutOut(details, wmsLogicalOrdergoods, putSaleOrderGoodsDto, saleorderGoods)){return;}
                //VDERP-8560销售订单专向发货sku创建采购单与下发数量逻辑强化end
            }
        }
        putSaleOrderDto.setDetails(details);

        if(CollectionUtils.isEmpty(details)){
            logger.info("销售单中没有商品明细 无法下发WMS:{}",saleorder.getSaleorderNo());
            return;
        }

        Integer wmsSendOrderId = saveWmsSendOrder(saleorder, user, WmsSendOrderTypeEnum.PUTSALEORDER.getCode());
        try {

             if(!cancelTypeService.cancelOutSaleOutMethod(saleorder.getSaleorderNo(),CancelReasonConstant.SEND_ORDER)){
                 logger.info("wms不得取消下发失败 单号:{}",saleorder.getSaleorderNo());
                 return;
             }

             logger.info("WMS销售出库单下传的 单号:{},请求:{}",saleorder.getSaleorderNo(),JSON.toJSONString(putSaleOrderDto));
             //XxlJobLogger.log("WMS销售出库单下传的 单号:{},请求:{}",saleorder.getSaleorderNo(),JSON.toJSONString(putSaleOrderDto));
             WmsInterface putSaleOrderOutputInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_ORIGINAL_SALESORDER);

             WmsResponse response = putSaleOrderOutputInterface.request(putSaleOrderDto);

             logger.info("WMS销售出库单下传的 单号:{},响应:{}",saleorder.getSaleorderNo(),JSON.toJSONString(response));
             //XxlJobLogger.log("WMS销售出库单下传的单号:{},响应:{}",saleorder.getSaleorderNo(),JSON.toJSONString(response));
             if("1".equals(response.getReturnFlag())){
                 WmsSendOrder updateWmsSendorder = new WmsSendOrder();
                 updateWmsSendorder.setWmsSendOrderId(wmsSendOrderId);
                 updateWmsSendorder.setSendStatus(ErpConst.ONE);
                 wmsSendOrderMapper.updateByPrimaryKeySelective(updateWmsSendorder);
             }
            } catch (Exception e) {
                logger.error("下发销售单失败error 单号:"+saleorder.getSaleorderNo(),e);
         }

    }

    private boolean specialPutOut(List<PutSaleOrderGoodsDto> details, WmsLogicalOrdergoods wmsLogicalOrdergoods, PutSaleOrderGoodsDto putSaleOrderGoodsDto, SaleorderGoods saleorderGoods) {
        //VDERP-8560销售订单专向发货sku创建采购单与下发数量逻辑强化start
        //销售商品共需下发数量
        int salesNeedNum = saleorderGoods.getNum() - (saleorderGoods.getAfterReturnNum() == null ? 0 : saleorderGoods.getAfterReturnNum());
        //获取专项发货关联的所有采购单信息
        List<RBuyorderSaleorderDto> allRelatedList = saleorderMapper.getRBuyorderSaleorderBySaleOrderGoodsId(wmsLogicalOrdergoods.getRelatedId());
        logger.info("专项发货商品{},关联采购单信息{},", wmsLogicalOrdergoods.getRelatedId(),JSON.toJSONString(allRelatedList));
        for(RBuyorderSaleorderDto rBuyorderSaleorderDto : allRelatedList){
            //获取采购单商品信息
            BuyorderGoods buyorderGoods = this.buyorderGoodsMapper.selectByPrimaryKey(rBuyorderSaleorderDto.getBuyorderGoodsId());
            //获取采购单对应进行中的售后信息
            List<AfterSalesGoods> buyOrderAfterSalesGoodsIngList = afterSalesGoodsMapper.getSpecialAftersalesGoodsList(rBuyorderSaleorderDto.getBuyorderId(),
                    rBuyorderSaleorderDto.getBuyorderGoodsId(),rBuyorderSaleorderDto.getBuyorderNo(),ErpConst.ONE);
            //获取采购单对应已完结的售后信息
            List<AfterSalesGoods> buyOrderAfterSalesGoodsComList = afterSalesGoodsMapper.getSpecialAftersalesGoodsList(rBuyorderSaleorderDto.getBuyorderId(),
                    rBuyorderSaleorderDto.getBuyorderGoodsId(),rBuyorderSaleorderDto.getBuyorderNo(),ErpConst.TWO);
            //查询已出库数量
            int outputNum = this.warehouseGoodsOperateLogMapper.getAlreadyOutputNum(buyorderGoods.getGoodsId(),rBuyorderSaleorderDto.getSaleorderGoodsId(),rBuyorderSaleorderDto.getBuyorderNo());
            logger.info("专项发货商品{},关联采购单{},进行中售后{},完结售后{},出库数量{},", wmsLogicalOrdergoods.getRelatedId(),rBuyorderSaleorderDto.getBuyorderNo()
                    ,JSON.toJSONString(buyOrderAfterSalesGoodsIngList)
                    ,JSON.toJSONString(buyOrderAfterSalesGoodsComList),outputNum);
            if(CollectionUtils.isEmpty(buyOrderAfterSalesGoodsIngList)){
                //不存在进行中的采购售后的情况--采购单需下发
                if(CollectionUtils.isEmpty(buyOrderAfterSalesGoodsComList)){
                    //已完结采购售后单为空--下发数量=采购单采购数量
                    //大前提 --下发数量比采购销售关联表数量小
                    if(rBuyorderSaleorderDto.getNum() < buyorderGoods.getNum()){
                        logger.info("销售明细,{}需下发数量超出采购销售关联表数量,采购单号{},",rBuyorderSaleorderDto.getSaleorderGoodsId(),rBuyorderSaleorderDto.getBuyorderNo());
                        return true;
                    }
                    //专项发货--本次下发+已出库需小于总需出库数量
                    if((salesNeedNum - outputNum) < buyorderGoods.getNum()){
                        logger.info("销售明细,{}本次下发数量+已发货数量,{},超出销售单共需下发数量,采购单号{},",rBuyorderSaleorderDto.getSaleorderGoodsId(),buyorderGoods.getNum() + outputNum
                                ,rBuyorderSaleorderDto.getBuyorderNo());
                        return true;
                    }
                    if(outputNum == buyorderGoods.getNum()){
                        //此采购单已全部出库，无需重复下发
                        continue;
                    }else {
                        //未全部出库，需出库数量=采购数量-已出库数量
                        PutSaleOrderGoodsDto targetPutSaleOrderGoodsDto = new PutSaleOrderGoodsDto();
                        BeanUtils.copyProperties(putSaleOrderGoodsDto,targetPutSaleOrderGoodsDto);
                        targetPutSaleOrderGoodsDto.setLotAtt07(rBuyorderSaleorderDto.getBuyorderNo());
                        targetPutSaleOrderGoodsDto.setQtyOrdered(buyorderGoods.getNum() - outputNum);
                        if(targetPutSaleOrderGoodsDto.getQtyOrdered() != null && targetPutSaleOrderGoodsDto.getQtyOrdered() > 0){
                            details.add(targetPutSaleOrderGoodsDto);
                        }
                    }
                }else {
                    //存在已完结的采购售后 下发数量=采购单采购数量-已完结售后单售后数量
                    Integer afterSum = 0;
                    for(AfterSalesGoods afterSalesGoodCom : buyOrderAfterSalesGoodsComList){
                        //计算当前采购单所有已完结的售后的数量
                        afterSum = afterSum + afterSalesGoodCom.getNum();
                    }
                    //大前提 --下发数量比采购销售关联表数量小
                    if(rBuyorderSaleorderDto.getNum() < (buyorderGoods.getNum() - afterSum)){
                        logger.info("销售明细,{}需下发数量超出采购销售关联表数量,采购单号{},售后数量和{},",rBuyorderSaleorderDto.getSaleorderGoodsId(),rBuyorderSaleorderDto.getBuyorderNo(),afterSum);
                        return true;
                    }
                    if((salesNeedNum - outputNum) < (buyorderGoods.getNum() - afterSum)){
                        logger.info("销售明细,{}本次下发数量+已发货数量,{},超出销售单共需下发数量,采购单号{},",rBuyorderSaleorderDto.getSaleorderGoodsId(),buyorderGoods.getNum() + outputNum
                                ,rBuyorderSaleorderDto.getBuyorderNo());
                        return true;
                    }
                    if(outputNum == (buyorderGoods.getNum() - afterSum)){
                        //已全部出库无需重复下发
                        continue;
                    }else {
                        //未全部出库，需出库数量=采购数量-已出库数量-完结售后数量和
                        PutSaleOrderGoodsDto targetPutSaleOrderGoodsDto = new PutSaleOrderGoodsDto();
                        BeanUtils.copyProperties(putSaleOrderGoodsDto,targetPutSaleOrderGoodsDto);
                        targetPutSaleOrderGoodsDto.setLotAtt07(rBuyorderSaleorderDto.getBuyorderNo());
                        targetPutSaleOrderGoodsDto.setQtyOrdered(buyorderGoods.getNum() - outputNum - afterSum);
                        if(targetPutSaleOrderGoodsDto.getQtyOrdered() != null && targetPutSaleOrderGoodsDto.getQtyOrdered() > 0){
                            details.add(targetPutSaleOrderGoodsDto);
                        }
                    }
                }
            }else {
                //存在进行中的采购售后单的情况--采购单不下发
                continue;
            }
        }
        return false;
    }

    //保存下传的单号
    private Integer saveWmsSendOrder(Saleorder saleorder, User user, int orderType) {
        WmsSendOrder wmsSendOrder = new WmsSendOrder();
        try {
            wmsSendOrder.setOrderType(orderType);
            wmsSendOrder.setOrderId(saleorder.getSaleorderId());
            wmsSendOrder.setOrderNo(saleorder.getSaleorderNo());
            WmsSendOrder oldInfo = wmsSendOrderMapper.getWmsSendOrderInfo(wmsSendOrder);
            if(oldInfo != null){
                WmsSendOrder update = new WmsSendOrder();
                update.setSendStatus(0);
                update.setWmsSendOrderId(oldInfo.getWmsSendOrderId());
                wmsSendOrderMapper.updateByPrimaryKeySelective(update);
                return oldInfo.getWmsSendOrderId();
            }
            Integer userId = 0;
            if(user != null && user.getUserId() != null){
                userId = user.getUserId();
            }
            wmsSendOrder.setCreator(userId);
            wmsSendOrder.setUpdater(userId);
            wmsSendOrder.setSendStatus(0);
            wmsSendOrderMapper.insertSelective(wmsSendOrder);
        } catch (Exception e) {
            logger.error("saveWmsSendOrder error",e);
        }
        return wmsSendOrder.getWmsSendOrderId();
    }

    private void insertWmsLogicalOrderGodos(List<WmsLogicalOrdergoods> wmsLogicalOrdergoodsList, User user) {

        if(CollectionUtils.isEmpty(wmsLogicalOrdergoodsList)){
            logger.info("insertWmsLogicalOrderGodos wmsLogicalOrdergoodsList is empty");
            return;
        }
        for (WmsLogicalOrdergoods wmsLogicalOrdergoods : wmsLogicalOrdergoodsList) {
            // 禁止打印user对象,java.lang.StackOverflowError
//            logger.info("当前用户User -- {}",JSON.toJSON(user));
//            logger.info("当前即插入wmsLogicalOrdergoods -- {}",JSON.toJSON(wmsLogicalOrdergoods));
            wmsLogicalOrdergoods.setOperateType(WmsLogicalOperateTypeEnum.SALEORDER_TYPE.getOperateTypeCode());
            Integer creatorId = Integer.valueOf(0);
            if(user != null){
                creatorId = user.getUserId();
            }
            wmsLogicalOrdergoods.setCreator(creatorId);
            wmsLogicalOrdergoodsMapper.insertSelective(wmsLogicalOrdergoods);
        }

    }

    @Override
    public void chooseLogicalOrder(Map<String, WarehouseStock> logicalStockInfo, List<WmsLogicalOrdergoods> wmsLogicalOrdergoodsList, SaleorderGoodsVo saleorderGoodsVo) {
        WarehouseStock jxqStock = logicalStockInfo.get(saleorderGoodsVo.getSku() + LogicalEnum.JXQ.getLogicalWarehouseId());
        Integer goodsVoNum = saleorderGoodsVo.getNum();
        if(goodsVoNum == 0){
            return;
        }
        WmsLogicalOrdergoods wmsLogicalOrdergoods = new WmsLogicalOrdergoods();
        wmsLogicalOrdergoods.setGoodsId(saleorderGoodsVo.getGoodsId());
        wmsLogicalOrdergoods.setRelatedId(saleorderGoodsVo.getSaleorderGoodsId());
        wmsLogicalOrdergoods.setSku(saleorderGoodsVo.getSku());

        //优先级 活动商品  > 定向发货 > 指定客户 > 默认规则

        HashMap<Integer, String> traderIdLogicalMap = getTraderLogical().getTraderIdLogicalMap();

        if(saleorderGoodsVo.getIsActionGoods() > 0){
            //活动仓
            wmsLogicalOrdergoods.setLogicalWarehouseId(LogicalEnum.HDC.getLogicalWarehouseId());
            wmsLogicalOrdergoods.setNum(goodsVoNum);
            logger.info("chooseLogicalOrder 活动仓 saleorderId:{},saleorderGoodsId:{}",saleorderGoodsVo.getSaleorderId(),saleorderGoodsVo.getSaleorderGoodsId());
        }else if(saleorderGoodsVo.getSpecialDelivery() != null && saleorderGoodsVo.getSpecialDelivery() == 1){
            //定向发货只指定HG仓
            wmsLogicalOrdergoods.setLogicalWarehouseId(LogicalEnum.HG.getLogicalWarehouseId());
            wmsLogicalOrdergoods.setNum(goodsVoNum);
            logger.info("chooseLogicalOrder 定向发货 saleorderId:{},saleorderGoodsId:{}",saleorderGoodsVo.getSaleorderId(),saleorderGoodsVo.getSaleorderGoodsId());
        }else if(saleorderGoodsVo.getTraderId() != null && traderIdLogicalMap.containsKey(saleorderGoodsVo.getTraderId())){
            //VDERP-7257 【质量管理】部分虚拟客户的销售单下传WMS的默认逻辑变更

            String logicalCode = traderIdLogicalMap.get(saleorderGoodsVo.getTraderId());

            Integer logicalWarehouseId = LogicalEnum.getLogicalWarehouseIdByCode(logicalCode);

            if(logicalWarehouseId != null && logicalWarehouseId > 0){
                wmsLogicalOrdergoods.setLogicalWarehouseId(logicalWarehouseId);
                logger.info("chooseLogicalOrder 指定客户 saleorderId:{},saleorderGoodsId:{},logicalWarehouseId:{}",saleorderGoodsVo.getSaleorderId(),saleorderGoodsVo.getSaleorderGoodsId(),logicalWarehouseId);
            }else{
                wmsLogicalOrdergoods.setLogicalWarehouseId(LogicalEnum.HG.getLogicalWarehouseId());
                logger.info("chooseLogicalOrder 指定客户未获取默认HG saleorderId:{},saleorderGoodsId:{}",saleorderGoodsVo.getSaleorderId(),saleorderGoodsVo.getSaleorderGoodsId());
            }

            wmsLogicalOrdergoods.setNum(goodsVoNum);

        }else{
            //近效期有可用库存优先分配,剩余商品数量全部分配至合格仓
            Integer jxqAvaNum = jxqStock.getAvailableStockNum();
            if(jxqAvaNum >= goodsVoNum && jxqAvaNum != 0){
                wmsLogicalOrdergoods.setLogicalWarehouseId(LogicalEnum.JXQ.getLogicalWarehouseId());
                wmsLogicalOrdergoods.setNum(goodsVoNum);
                jxqStock.setAvailableStockNum( jxqStock.getAvailableStockNum() - goodsVoNum);
            }else {
                if(jxqAvaNum == 0){
                    wmsLogicalOrdergoods.setNum(goodsVoNum);
                    wmsLogicalOrdergoods.setLogicalWarehouseId(LogicalEnum.HG.getLogicalWarehouseId());
                }else{
                    wmsLogicalOrdergoods.setNum(jxqAvaNum);
                    wmsLogicalOrdergoods.setLogicalWarehouseId(LogicalEnum.JXQ.getLogicalWarehouseId());
                    WmsLogicalOrdergoods hgorderGoods = new WmsLogicalOrdergoods();
                    hgorderGoods.setSku(saleorderGoodsVo.getSku());
                    hgorderGoods.setGoodsId(saleorderGoodsVo.getGoodsId());
                    hgorderGoods.setRelatedId(saleorderGoodsVo.getSaleorderGoodsId());
                    hgorderGoods.setNum(goodsVoNum-jxqAvaNum);
                    hgorderGoods.setOccupyNum(hgorderGoods.getNum());
                    hgorderGoods.setLogicalWarehouseId(LogicalEnum.HG.getLogicalWarehouseId());
                    addWmsLogicalOrderGoodsList(wmsLogicalOrdergoodsList, hgorderGoods);

                    jxqStock.setAvailableStockNum(0);
                }
            }
            logger.info("chooseLogicalOrder 默认逻辑 saleorderId:{},saleorderGoodsId:{}",saleorderGoodsVo.getSaleorderId(),saleorderGoodsVo.getSaleorderGoodsId());
        }
        wmsLogicalOrdergoods.setOccupyNum(wmsLogicalOrdergoods.getNum());
        addWmsLogicalOrderGoodsList(wmsLogicalOrdergoodsList, wmsLogicalOrdergoods);
    }

    private void addWmsLogicalOrderGoodsList(List<WmsLogicalOrdergoods> wmsLogicalOrdergoodsList, WmsLogicalOrdergoods wmsLogicalOrdergoods) {
        if(wmsLogicalOrdergoods.getNum() > 0) {
            wmsLogicalOrdergoodsList.add(wmsLogicalOrdergoods);
        }
    }

    @Override
    public void closeOrdersynchronizeStockData(Saleorder saleorder) {
        try {
            Saleorder saleOrderById = saleorderMapper.getSaleOrderById(saleorder.getSaleorderId());

            cancelTypeService.cancelOutSaleOutMethod(saleOrderById.getSaleorderNo(),CancelReasonConstant.HC_CLOSE_ORDER);
            if(!saleOrderById.getStatus().equals(OrderConstant.ORDER_STATUS_CLOSE) || !OrderConstant.ORDER_TYPE_HC.equals(saleOrderById.getOrderType())){
                return;
            }
            List<WmsLogicalOrdergoods> saleorderLogicalChooseList = wmsLogicalOrdergoodsMapper.getSaleorderLogicalChooseInfoByNo(saleOrderById.getSaleorderNo());
            if(CollectionUtils.isEmpty(saleorderLogicalChooseList)){
                return;
            }
            List<WmsLogicalOrdergoods> updateStockList = new ArrayList<>();
            for (WmsLogicalOrdergoods wmsLogicalOrdergoods : saleorderLogicalChooseList) {
                if(wmsLogicalOrdergoods.getOccupyNum().equals(0)){
                    continue;
                }
                WmsLogicalOrdergoods update = new WmsLogicalOrdergoods();
                update.setLogicalOrderGoodsId(wmsLogicalOrdergoods.getLogicalOrderGoodsId());
                update.setOccupyNum(0);
                update.setIsDelete(1);
                wmsLogicalOrdergoodsMapper.updateByPrimaryKeySelective(update);
                wmsLogicalOrdergoods.setOccupyNum(-wmsLogicalOrdergoods.getOccupyNum());
                updateStockList.add(wmsLogicalOrdergoods);
            }
            if(CollectionUtils.isEmpty(updateStockList)){
                return;
            }
            logger.info("closeOrdersynchronizeStockData 单号:{},info:{}",saleOrderById.getSaleorderNo(),JSON.toJSONString(updateStockList));
            //关闭订单
            saleOrderById.setOperateType(StockOperateTypeConst.COLES_ORDER);
            //同步到库存服务
            sysnchStock(saleOrderById, updateStockList);
        } catch (Exception e) {
            logger.error("closeOrdersynchronizeStockData error:",e);
        }

    }

    @Override
    public void closeAfterPutSaleorder(AfterSalesVo afterSalesInfo, User user) throws Exception {
        if(afterSalesInfo.getAfterSalesId() == null){
            return;
        }
        AfterSales afterSales = afterSalesMapper.getAfterSalesById(afterSalesInfo.getAfterSalesId());
        if(!StockOperateTypeConst.AFTERORDER_BACK_FINSH.equals(afterSales.getType())){
            return;
        }
        Saleorder saleorder = saleorderMapper.getSaleOrderById(afterSales.getOrderId());
        if(!OrderConstant.ORDER_ALL_PAYMENT.equals(saleorder.getPaymentStatus())){
            return;
        }
//        this.cancelAfterOrder(afterSales);
        // INPUT_SALE_RETURN
        cancelTypeService.cancelInputSaleReturnMethod(afterSales.getAfterSalesNo(), "售后单关闭");
//        this.cancelOutByNo(afterSales.getOrderNo(),CancelReasonConstant.SEND_ORDER);
        cancelTypeService.cancelOutSaleOutMethod(afterSales.getOrderNo(), CancelReasonConstant.SEND_ORDER);
        //查询已选择逻辑仓数据重新下发可出库部分
        //已选择逻辑仓
        List<WmsLogicalOrdergoods> saleorderLogicalChooseInfo = wmsLogicalOrdergoodsMapper.getSaleorderLogicalChooseInfoByNo(saleorder.getSaleorderNo());

        List<WmsLogicalOrdergoods> putSaleorderGoods = new ArrayList<>();
        this.putSaleorderLogicalGoods(saleorderLogicalChooseInfo, putSaleorderGoods);
        //销售出库单重新下传wms
        this.putSaleOrderOutput(saleorder,putSaleorderGoods,user);
    }

    private void putSaleorderLogicalGoods(List<WmsLogicalOrdergoods> saleorderLogicalChooseInfo, List<WmsLogicalOrdergoods> putSaleorderGoods) {
        //重新封装销售单选择逻辑仓数据
        saleorderLogicalChooseInfo.stream().forEach(wmsLogicalOrdergoods -> {
            if(wmsLogicalOrdergoods.getOccupyNum() > 0){
                wmsLogicalOrdergoods.setNum(wmsLogicalOrdergoods.getOccupyNum());
                putSaleorderGoods.add(wmsLogicalOrdergoods);
            }
        });
    }

//    @Override
//    public void cancelAfterOrder(AfterSales afterSales) {
//        try{
//            //取消订单参数对象
//            CancelPoDto cancelPoDto = new CancelPoDto();
//            cancelPoDto.setDocNo(afterSales.getAfterSalesNo());
//            cancelPoDto.setPoType(WmsInterfaceOrderType.INPUT_SALE_RETURN);
//            cancelPoDto.setErpCancelReason("售后单关闭");
//            logger.info("ERP取消入库单至WMS的请求: cancelPoDto:{}" + JSON.toJSONString(cancelPoDto));
//            //撤销入库单 TODO Holiis
//            WmsInterface wmsInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.CANCEL_PO);
//            WmsResponse wmsResponse = wmsInterface.request(cancelPoDto);
//            logger.info("ERP取消入库单至WMS的响应:" + JSON.toJSONString(wmsResponse));
//        }catch (Exception e){
//            logger.error("poc error",e);
//        }
//    }
    /*@Override
    public WmsResponse cancelOutByNo(String orderNo,String CancelReason) {
        if(StringUtil.isBlank(orderNo)){
            return null;
        }
        // TODO HOLLIS
        WmsResponse response = new WmsResponse();
        CancelPoDto cancelPoDto = new CancelPoDto();
        cancelPoDto.setDocNo(orderNo);
        cancelPoDto.setOrderType(WmsInterfaceOrderType.OUT_SALE_OUT);
        cancelPoDto.setErpCancelReason(CancelReason);
        try {
            XxlJobLogger.log("ERP取消出库单至WMS的 单号:{},请求cancelPoDto:{}",orderNo, JSON.toJSONString(cancelPoDto));
            WmsInterface cancelSaleorder = wmsInterfaceFactory.getWmsInterface(WMSContant.CANCEL_ORGGINCAL_SALESORDER);
            response = cancelSaleorder.request(cancelPoDto);
            XxlJobLogger.log("ERP取消出库单至WMS的单号:{}, 响应:{}",orderNo,JSON.toJSONString(response));
        } catch (Exception e) {
            XxlJobLogger.log("ERP取消出库单至WMS的请求接口报错", e);
        }
        return response;
    }*/

    @Override
    public void putSaleAfterReturnStartGoods(String orderNo, User user) {
        logger.info("销售售后退货下发WMS 剩余部分 order:{}",orderNo);
        Saleorder order = saleorderMapper.getSaleorderByOrderNo(orderNo);

        List<WmsLogicalOrdergoods> saleorderLogicalChooseInfoByNo = getWmsLogicalOrdergoodsByOrderNo(order);
        try {
            putSaleOrderOutput(order,saleorderLogicalChooseInfoByNo,user);

        }catch (Exception e){
            logger.error("putSaleAfterReturnStartGoods error",e);
        }

    }

    @Override
    public boolean modifyOrderOutput(Integer orderId,SaleorderModifyApply saleorderModifyApply,User user) {
        if (orderId == null && (saleorderModifyApply == null||saleorderModifyApply.getSaleorderId() == null)) {
            return false;
        }
        Saleorder order = saleorderMapper.getSaleOrderById(orderId);
        if (order == null) {
            logger.info("ERP出库单修改发货方式、发货要求、等待截止日期下发WMS，订单不存在，订单ID:{}",orderId);
            return false;
        }
        if(saleorderModifyApply != null){
            order.setDeliveryClaim(saleorderModifyApply.getDeliveryClaim());
            order.setDeliveryType(saleorderModifyApply.getDeliveryType());
            order.setDeliveryDelayTime(saleorderModifyApply.getDeliveryDelayTime());
        }
        //出库单重新下发wms
        if(!(OrderConstant.ORDER_ALL_PAYMENT.equals(order.getPaymentStatus()) &&
                OrderConstant.ORDER_STATUS_VALID.equals(order.getStatus()))){
            logger.info("ERP出库单修改发货方式、发货要求、等待截止日期不下发WMS，因为订单未全部付款或未生效,单号:{}",order.getSaleorderNo());
            //logger.info("ERP出库单修改发货方式、发货要求、等待截止日期不下发WMS，因为订单未全部付款或未生效,单号:{}",order.getSaleorderNo());
            return true;
        }
        ModifyOrderDto modifyOrderDto = new ModifyOrderDto();
        modifyOrderDto.setOrderNo(order.getSaleorderNo());
        modifyOrderDto.setOrderType(WmsInterfaceOrderType.OUT_SALE_OUT);
        modifyOrderDto.setExpectedShipmentTime2(ErpConst.ZERO.equals(order.getDeliveryClaim())  ? "9999-12-21 23:59:59" : DateUtil.convertString(order.getDeliveryDelayTime(),"yyyy-MM-dd HH:mm:ss"));
        modifyOrderDto.setHedi04(SysOptionConstant.NO_PART_DELIVER.equals(order.getDeliveryType()) ? "Y" :"N");
        modifyOrderDto.setHedi07(ErpConst.ZERO.equals(order.getDeliveryClaim())  ? "A" : "B");
        //是否打印4联随货同行单
        modifyOrderDto.setHedi03("Y");
        String printOutType = warehouseStockService.getPrintOutType(order);
        if(StringUtil.isBlank(printOutType)){
            modifyOrderDto.setHedi03("N");
        }
        if (saleorderModifyApply != null && saleorderModifyApply.getLogisticsId() != null && saleorderModifyApply.getLogisticsId() > 0){
            Logistics logistics = logisticsMapper.getLogisticsById(saleorderModifyApply.getLogisticsId());
            modifyOrderDto.setUserDefine1(logistics.getCarrierId());
            modifyOrderDto.setUserDefine2(logistics.getName());
        }
        Integer wmsSendOrderId = saveWmsSendOrder(order, user, WmsSendOrderTypeEnum.PUTSALEORDERMODIFY.getCode());
        try {
            logger.info("ERP出库单修改发货方式、发货要求、等待截止日期至WMS的单号:{},modifyOrderDto:{}",order.getSaleorderNo(), JSON.toJSONString(modifyOrderDto));
            WmsInterface modifySaleOrder = wmsInterfaceFactory.getWmsInterface(WMSContant.MODIFY_SALEORDER);
            WmsResponse response = modifySaleOrder.request(modifyOrderDto);
            logger.info("ERP出库单修改发货方式、发货要求、等待截止日期至WMS的单号:{}, 响应:{}",order.getSaleorderNo(),JSON.toJSONString(response));
            //logger.info("ERP出库单修改发货方式、发货要求、等待截止日期至WMS的单号:{}, 响应:{}",order.getSaleorderNo(),JSON.toJSONString(response));

            if("0".equals(response.getReturnFlag())){
                logger.info("ERP出库单修改发货方式、发货要求、等待截止日期下发失败 单号:{}",order.getSaleorderNo());
                // 如果失败原因是订单不存在，重新下发
                if (CancelCodeConstant.INNO_EXIST_CODE.equals(response.getReturnCode())) {
                    logger.info("ERP出库单修改发货方式、发货要求、等待截止日期下发,WMS修改订单不存在，触发重新下发");
                    if(OrderConstant.ORDER_ALL_PAYMENT.equals(order.getPaymentStatus()) &&
                            OrderConstant.ORDER_STATUS_VALID.equals(order.getStatus())) {
                        // 允许修改ERP销售单，先更新订单字段，销售出库下发需要使用
                        saleorderMapper.updateByPrimaryKeySelective(order);
                        LogicalSaleorderChooseServiceImpl bean =  SpringUtil.getBean(LogicalSaleorderChooseServiceImpl.class);
                        bean.chooseLogicalSaleorder(order, user);
                    }
                    updateWmsSendOrderStatus(wmsSendOrderId);
                    return true;
                } else if (CancelCodeConstant.WORKING_CODE.equals(response.getReturnCode()) || CancelCodeConstant.REAL_ARR_CODE.equals(response.getReturnCode())) {
                    logger.info("ERP出库单修改发货方式、发货要求、等待截止日期下发,订单已开始作业或已收货，无法修改驳回修改申请！");
                    updateWmsSendOrderStatus(wmsSendOrderId);
                    return false;
                }
            }
            if("1".equals(response.getReturnFlag())){
                updateWmsSendOrderStatus(wmsSendOrderId);
            }
        } catch (Exception e) {
            logger.error("ERP出库单修改发货方式、发货要求、等待截止日期至WMS的请求接口报错", e);
        }
        return true;
    }

    private void updateWmsSendOrderStatus(Integer wmsSendOrderId) {
        WmsSendOrder updateWmsSendorder = new WmsSendOrder();
        updateWmsSendorder.setWmsSendOrderId(wmsSendOrderId);
        updateWmsSendorder.setSendStatus(1);
        wmsSendOrderMapper.updateByPrimaryKeySelective(updateWmsSendorder);
    }

    protected boolean saleOrderRelateBuyOrderAuditPass(SaleorderGoodsVo saleorderGoodsVo) {

        List<RBuyorderSaleorderDto> relationList = saleorderMapper.getRBuyorderSaleorderBySaleOrderGoodsId(saleorderGoodsVo.getSaleorderGoodsId());
        if(CollectionUtils.isEmpty(relationList)){
            return false;
        }

        for(RBuyorderSaleorderDto rBuyorderSaleorderDto : relationList){
            //采购单的状态为待确认
            if (rBuyorderSaleorderDto.getStatus() == 0) {
                logger.info("采购单:"+rBuyorderSaleorderDto.getBuyorderNo() + "的状态为待确认，无法下发销售单");
                return false;
            }
        }

        int saleOrdreGoodsNum = saleorderGoodsMapper.selectByPrimaryKey(saleorderGoodsVo.getSaleorderGoodsId()).getNum();

        int relateSumTotal = relationList.stream().mapToInt(r->r.getNum()).sum();
        if (relateSumTotal != saleOrdreGoodsNum) {
            logger.info("采购单关联的总数量:" + relateSumTotal + "与销售单的数量:"+saleOrdreGoodsNum+"不相等，无法下发销售单");
            return false;
        }

        return true;
    }


    private TraderLogical getTraderLogical(){
        TraderLogical traderLogical;
        HashMap<Integer,String> traderIdLogicalMap = new HashMap<>();

        try {
            traderLogical = JsonUtils.readValue(traderLogicalJsonStr, TraderLogical.class);

            HashMap<String, Set<Integer>> logcailTraderMap = traderLogical.getLogcailTraderMap();

            for (String logcaiCode : logcailTraderMap.keySet()) {
                Set<Integer> traderIdSet = logcailTraderMap.get(logcaiCode);
                for (Integer traderId : traderIdSet) {
                    traderIdLogicalMap.put(traderId,logcaiCode);
                }
            }
            traderLogical.setTraderIdLogicalMap(traderIdLogicalMap);
        } catch (Exception e) {
            traderLogical = new TraderLogical();
            traderLogical.setTraderIdLogicalMap(traderIdLogicalMap);
        }
        return traderLogical;
    }

    /**
     * <b>Description:</b><br>
     * 保存下发信息公用方法
     *
     * @param orderNo, orderId, user
     * @return com.wms.model.po.WmsSendOrder
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/10/21 11:09
     */
    @Override
    public WmsSendOrder getWmsSendOrder(String orderNo,Integer orderId,Integer orderType,User user){
        WmsSendOrder wmsSendOrder = new WmsSendOrder();
        try {
            wmsSendOrder.setOrderType(orderType);
            wmsSendOrder.setOrderId(orderId);
            wmsSendOrder.setOrderNo(orderNo);
            WmsSendOrder oldInfo = wmsSendOrderMapper.getWmsSendOrderInfo(wmsSendOrder);
            if(oldInfo != null){
                return oldInfo;
            }
            Integer userId = 0;
            if(user != null && user.getUserId() != null){
                userId = user.getUserId();
            }
            wmsSendOrder.setCreator(userId);
            wmsSendOrder.setUpdater(userId);
            wmsSendOrder.setSendStatus(0);
            wmsSendOrderMapper.insertSelective(wmsSendOrder);
        } catch (Exception e) {
            logger.error("saveWmsSendOrder error",e);
        }
        return wmsSendOrder;
    }

    @Override
    public void saveWmsSendOrder(WmsSendOrder wmsSendOrder) {

        User user = getUser();
        wmsSendOrder.setSendStatus(ErpConst.ONE);
        wmsSendOrder.setUpdater(user.getUserId());
        wmsSendOrder.setModeTime(new Date());
        wmsSendOrderMapper.updateByPrimaryKeySelective(wmsSendOrder);

    }

    @Override
    public void saveWmsSendOrderEvent(Saleorder saleorder, User user, WmsSendOrderTypeEnum code) {

    }

    private User getUser(){
        User user = null;
        ServletRequestAttributes ra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (ra != null) {
            HttpServletRequest request = ra.getRequest();
            if (request != null && request.getSession() != null) {
                user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
            }
        }
        if(user==null){
            user= new User();
            user.setCompanyId(ErpConst.ONE);
            user.setUserId(ErpConst.TWO);
            user.setCompanyName("南京贝登医疗有限公司");
            user.setUsername("njadmin");
        }
        return user;
    }

}
