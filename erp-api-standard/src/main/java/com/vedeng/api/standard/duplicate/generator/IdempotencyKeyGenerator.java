package com.vedeng.api.standard.duplicate.generator;

import com.vedeng.api.standard.duplicate.constants.IdempotencyHeaders;
import com.vedeng.api.standard.duplicate.exception.IdempotencyException;
import com.vedeng.api.standard.core.ApiRequest;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 幂等性键生成器
 * 统一从请求头获取参数并生成幂等性键
 * 格式：{businessType}_{companyCode}_{flowOrderId}
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-15
 */
@Component
public class IdempotencyKeyGenerator {
    
    private static final Logger logger = LoggerFactory.getLogger(IdempotencyKeyGenerator.class);
    
    /**
     * 键分隔符（使用|避免与业务类型如PURCHASE_ORDER中的下划线冲突）
     */
    private static final String KEY_SEPARATOR = "|";
    
    /**
     * 生成幂等性键（从ApiRequest直接生成）
     * 只从请求头获取参数，格式：{businessType}|{companyCode}|{flowOrderId}
     * 
     * @param apiRequest API请求对象
     * @param businessType 业务类型
     * @return 生成的幂等性键
     * @throws IdempotencyException 如果生成失败
     */
    public String generateIdempotencyKey(ApiRequest apiRequest, String businessType) {
        try {
            // 1. 参数验证
            if (apiRequest == null) {
                throw new IllegalArgumentException("API请求不能为空");
            }
            if (StringUtils.isEmpty(businessType)) {
                throw new IllegalArgumentException("业务类型不能为空");
            }
            
            // 2. 从请求头提取参数（只从请求头获取）
            String companyCode = extractFromHeader(apiRequest, IdempotencyHeaders.COMPANY_CODE, null);
            String flowOrderId = extractFromHeader(apiRequest, IdempotencyHeaders.FLOW_ORDER_ID, null);
            
            if (StringUtils.isEmpty(companyCode)) {
                throw new IllegalArgumentException("公司代码不能为空，请在请求头" + IdempotencyHeaders.COMPANY_CODE + "中提供");
            }
            if (StringUtils.isEmpty(flowOrderId)) {
                throw new IllegalArgumentException("流程订单ID不能为空，请在请求头" + IdempotencyHeaders.FLOW_ORDER_ID + "中提供");
            }
            
            // 3. 先验证键组件字符合法性
            validateKeyComponents(businessType, companyCode, flowOrderId);
            
            // 4. 构建幂等性键
            StringBuilder keyBuilder = new StringBuilder();
            keyBuilder.append(businessType)
                      .append(KEY_SEPARATOR)
                      .append(companyCode)
                      .append(KEY_SEPARATOR)
                      .append(flowOrderId);
            
            String idempotencyKey = keyBuilder.toString();
            
            // 5. 处理键长度（用hash而非截断确保唯一性）
            if (idempotencyKey.length() > 120) {
                logger.warn("幂等性键长度超过120字符，使用hash后缀确保唯一性: length={}, originalKey={}", 
                    idempotencyKey.length(), idempotencyKey);
                // 保留前缀，后缀用hash确保唯一性
                String prefix = idempotencyKey.substring(0, 88);
                String hash = DigestUtils.md5Hex(idempotencyKey).substring(0, 32);
                idempotencyKey = prefix + "|" + hash;
            }
            
            // 6. 最终长度检查
            if (idempotencyKey.length() > 128) {
                logger.warn("生成的幂等性键长度仍超过128字符，将被截断: length={}, key={}", 
                    idempotencyKey.length(), idempotencyKey);
                idempotencyKey = idempotencyKey.substring(0, 128);
            }
            
            logger.debug("生成幂等性键: key={}, businessType={}, companyCode={}, flowOrderId={}", 
                idempotencyKey, businessType, companyCode, flowOrderId);
                
            return idempotencyKey;
            
        } catch (Exception e) {
            logger.error("生成幂等性键失败: businessType={}, requestId={}", businessType, 
                apiRequest != null ? apiRequest.getRequestId() : "null", e);
            throw IdempotencyException.keyGenerationError("生成幂等性键失败", e);
        }
    }
    
    /**
     * 从请求头提取参数
     * 
     * @param apiRequest API请求
     * @param headerName 请求头名称
     * @param defaultValue 默认值
     * @return 参数值
     */
    private String extractFromHeader(ApiRequest apiRequest, String headerName, String defaultValue) {
        if (apiRequest.getHeaders() != null) {
            String headerValue = apiRequest.getHeaders().get(headerName);
            if (!StringUtils.isEmpty(headerValue)) {
                return headerValue.trim();
            }
        }
        return defaultValue;
    }
    
    /**
     * 验证键组件字符合法性
     * 
     * @param businessType 业务类型
     * @param companyCode 公司代码
     * @param flowOrderId 流程订单ID
     * @throws IllegalArgumentException 如果包含非法字符
     */
    private void validateKeyComponents(String businessType, String companyCode, String flowOrderId) {
        validateKeyComponent("businessType", businessType);
        validateKeyComponent("companyCode", companyCode);
        validateKeyComponent("flowOrderId", flowOrderId);
    }
    
    /**
     * 验证键组件字符合法性
     * 
     * @param componentName 组件名称
     * @param componentValue 组件值
     * @throws IllegalArgumentException 如果包含非法字符
     */
    private void validateKeyComponent(String componentName, String componentValue) {
        if (StringUtils.isEmpty(componentValue)) {
            throw new IllegalArgumentException(componentName + "不能为空");
        }
        
        if (componentValue.contains(KEY_SEPARATOR)) {
            throw new IllegalArgumentException(
                String.format("%s不能包含分隔符'%s': %s", componentName, KEY_SEPARATOR, componentValue));
        }
        
        // 检查其他可能有问题的字符
        if (componentValue.contains(" ") || componentValue.contains("\t") || 
            componentValue.contains("\n") || componentValue.contains("\r")) {
            throw new IllegalArgumentException(
                String.format("%s不能包含空白字符: %s", componentName, componentValue));
        }
    }
}
