package com.pricecenter.dto;

import java.math.BigDecimal;

public class PriceInfoUploadDto {

    private String skuNo;

    private String traderName;

    private Long traderId;

    private BigDecimal marketPrice;

    private BigDecimal terminalPrice;

    private BigDecimal distributionPrice;

    private BigDecimal purchasePrice;

    private BigDecimal groupPrice;

    private BigDecimal electronicCommercePrice;

    private BigDecimal researchTerminalPrice;

    public String getSkuNo() {
        return skuNo;
    }

    public void setSkuNo(String skuNo) {
        this.skuNo = skuNo;
    }

    public String getTraderName() {
        return traderName;
    }

    public void setTraderName(String traderName) {
        this.traderName = traderName;
    }

    public Long getTraderId() {
        return traderId;
    }

    public void setTraderId(Long traderId) {
        this.traderId = traderId;
    }

    public BigDecimal getMarketPrice() {
        return marketPrice;
    }

    public void setMarketPrice(BigDecimal marketPrice) {
        this.marketPrice = marketPrice;
    }

    public BigDecimal getTerminalPrice() {
        return terminalPrice;
    }

    public void setTerminalPrice(BigDecimal terminalPrice) {
        this.terminalPrice = terminalPrice;
    }

    public BigDecimal getDistributionPrice() {
        return distributionPrice;
    }

    public void setDistributionPrice(BigDecimal distributionPrice) {
        this.distributionPrice = distributionPrice;
    }

    public BigDecimal getPurchasePrice() {
        return purchasePrice;
    }

    public void setPurchasePrice(BigDecimal purchasePrice) {
        this.purchasePrice = purchasePrice;
    }

    public BigDecimal getGroupPrice() {
        return groupPrice;
    }

    public void setGroupPrice(BigDecimal groupPrice) {
        this.groupPrice = groupPrice;
    }

    public BigDecimal getElectronicCommercePrice() {
        return electronicCommercePrice;
    }

    public void setElectronicCommercePrice(BigDecimal electronicCommercePrice) {
        this.electronicCommercePrice = electronicCommercePrice;
    }

    public BigDecimal getResearchTerminalPrice() {
        return researchTerminalPrice;
    }

    public void setResearchTerminalPrice(BigDecimal researchTerminalPrice) {
        this.researchTerminalPrice = researchTerminalPrice;
    }
}
