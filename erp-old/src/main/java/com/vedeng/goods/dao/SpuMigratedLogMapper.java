package com.vedeng.goods.dao;

import com.vedeng.common.page.Page;
import com.vedeng.goods.model.SpuMigratedLogDomain;
import com.vedeng.goods.model.SpuMigratedLogDomainExample;
import com.vedeng.goods.model.dto.GoodsRemovalRecordQueryDto;
import com.vedeng.goods.model.dto.SpuRemovalLogDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SpuMigratedLogMapper {

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SPU_MIGRATION_LOG
     *
     * @mbggenerated Thu Jul 30 17:48:00 CST 2020
     */
    int deleteByPrimaryKey(Integer spuMigtationLogId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SPU_MIGRATION_LOG
     *
     * @mbggenerated Thu Jul 30 17:48:00 CST 2020
     */
    int insert(SpuMigratedLogDomain record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SPU_MIGRATION_LOG
     *
     * @mbggenerated Thu Jul 30 17:48:00 CST 2020
     */
    int insertSelective(SpuMigratedLogDomain record);


    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SPU_MIGRATION_LOG
     *
     * @mbggenerated Thu Jul 30 17:48:00 CST 2020
     */
    SpuMigratedLogDomain selectByPrimaryKey(Integer spuMigtationLogId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SPU_MIGRATION_LOG
     *
     * @mbggenerated Thu Jul 30 17:48:00 CST 2020
     */
    int updateByPrimaryKeySelective(SpuMigratedLogDomain record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SPU_MIGRATION_LOG
     *
     * @mbggenerated Thu Jul 30 17:48:00 CST 2020
     */
    int updateByPrimaryKey(SpuMigratedLogDomain record);


    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SPU_MIGRATION_LOG
     *
     * @mbggenerated Mon Aug 03 16:48:32 CST 2020
     */
    List<SpuMigratedLogDomain> selectByExample(SpuMigratedLogDomainExample example);

    /**
     * 根据条件查询商品spu迁移记录
     *
     * @return
     */
    List<SpuRemovalLogDto> listSpuMigratedlistpage(@Param("goodsRemovalRecordQueryDto") GoodsRemovalRecordQueryDto goodsRemovalRecordQueryDto, @Param("page") Page page);


    List<SpuRemovalLogDto> listSpuRemovalRecordsBySpuIdList(List<Integer> spuRemovalLogIdList);
}