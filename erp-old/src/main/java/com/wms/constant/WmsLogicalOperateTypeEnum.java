package com.wms.constant;

/**
 * 业务类型
 * <AUTHOR>
 * @date 2020/7/25 15:46:20
 */
public enum WmsLogicalOperateTypeEnum {
    SALEORDER_TYPE(0, "销售单"),
    AFTER_SALEORDER_TYPE(1, "售后单"),
    INVENTORY_TRANSFER_TYPE(2, "库存转移单"),
    BUYORDER_TYPE(3, "采购单入库"),
    INVENTORY_ADJUSTMENT(4, "库存调整单"),
    PURCHASE_RETURN(5, "采购退货单"),
    PURCHASE_EXG(6, "采购换货单"),
    LENDOUT_EXG(7, "商品外借单"),
    SURPLUS_IN(8,"盘盈入库单"),
    SCRAPPED_OUT(9,"报废出库单"),
    INVENTORY_OUT(10,"盘亏出库单"),
    UNIT_CONVERSION_OUT(11,"单位转换出库单"),
    UNIT_CONVERSION_IN(12,"单位转换入库单"),
    SAMPLE_OUT(13, "样品出库单"),
    ;

    WmsLogicalOperateTypeEnum(Integer operateTypeCode, String operateTypeStr) {
        this.operateTypeCode = operateTypeCode;
        this.operateTypeStr = operateTypeStr;
    }

    private Integer operateTypeCode;

    private String operateTypeStr;

    public static String getOperateTypeStr(byte operateTypeCode) {
        String operateTypeStr = "";

        for (WmsLogicalOperateTypeEnum enumItem : WmsLogicalOperateTypeEnum.values()) {
            if (enumItem.getOperateTypeCode().equals(operateTypeCode)) {
                operateTypeStr = enumItem.getOperateTypeStr();
                break;
            }
        }

        return operateTypeStr;
    }

    public Integer getOperateTypeCode() {
        return operateTypeCode;
    }

    public String getOperateTypeStr() {
        return operateTypeStr;
    }}
