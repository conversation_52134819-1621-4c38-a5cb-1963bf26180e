package com.smallhospital.dao;

import com.smallhospital.model.ElContractSku;
import com.smallhospital.model.vo.ElContractSkuVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ELContractSkuModifyMapper {

    int deleteByPrimaryKey(@Param("modifySkuId") Integer modifySkuId);
    /**
     * <b>Description:</b>根据合同修改id删除sku<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/4/20
     */
    int deleteByContractModifyId(@Param("contractModifyId") Integer contractModifyId);

    int insert(ElContractSku record);

    int insertSelective(ElContractSku record);

    List<ElContractSkuVO> findByModifyContractId(@Param("contractModifyId") Integer contractModifyId);

    void batchAddContractSkus(List<ElContractSku> skuLists);
}
