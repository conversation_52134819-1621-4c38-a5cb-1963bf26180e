$(function(){
    //后台报错显示
    var errorTip = function (tip) {
        var dia = artDialog.alert(tip, null, {
            fn: function () {
                dia.close();
            }, text: '我知道了'
        }, { type: "warn" });
    };
    var isChange = false;
    //设置
    $('.J-set').click(function(){
        if(isChange){
            return;
        }
        var dialog = new artDialog({
            title: '选择推送的平台弹框',
            content: $('.J-dlg-tmpl').html(),
            init: function () {
                $('.J-dlg-form').validate({
                    rules: {
                        platfromIds: {
                            required: true
                        }
                    },
                    messages: {
                        platfromIds: {
                            required: '请选择平台'
                        }
                    }
                })
            },
            width: 600,
            button: [{
                name: '提交',
                highlight: true,
                callback: function () {
                    if ($('.J-dlg-form').valid()) {
                        $.ajax({
                            url: page_url + '/vgoods/operate/pushGoodsInfo.do',
                            data: $('.J-dlg-form').serialize(),
                            dataType: 'json',
                            success: function (res) {
                                console.log(res);
                                dialog.close();
                                if (res.code === 0) {
                                    window.localStorage.setItem('operateSetSuccess', 'true');
                                    window.location.reload();
                                } else if (res.code == -2){
                                    console.log(res.listData);
                                    var items = res.listData;
                                    var failItems = '';
                                    for(var i = 0; i < items.length; i++){
                                        failItems += items[i].serviceName;
                                        failItems += '，';
                                    };
                                    failItems = failItems.substring(0, failItems.length - 1);
                                    layer.alert('当前商品信息不满足推送条件！<br/>待办事项如下:<br/>' + failItems +
                                        '<br/>对应处理人：' + res.data + '<br/>系统已通知相关责任人!', { area: ['580px', '260px']});
                                } else {
                                    errorTip(res.message || '操作异常');
                                }
                            }
                        })
                    }
                    return false;
                }
            }, {
                name: '取消'
            }],
        })
    });

    $('.J-set2').click(function(){
        if(isChange){
            return;
        }
        var dialog = new artDialog({
            title: '选择推送的平台弹框',
            content: $('.J-dlg-tmpl2').html(),
            init: function () {
                $('.J-dlg-form').validate({
                    rules: {
                        pushOrgIdList: {
                            required: true
                        }
                    },
                    messages: {
                        pushOrgIdList: {
                            required: '请选择推送的平台'
                        }
                    }
                })
            },
            width: 600,
            button: [{
                name: '提交',
                highlight: true,
                callback: function () {
                    if ($('.J-dlg-form').valid()) {
                        $.ajax({
                            url: page_url + '/vgoods/operate/pushGoodsInfo.do',
                            data: $('.J-dlg-form').serialize(),
                            dataType: 'json',
                            success: function (res) {
                                dialog.close();
                                if (res.code === 0) {
                                    window.localStorage.setItem('operateSetSuccess', 'true');
                                    window.location.reload();
                                } else if (res.code == -2){
                                    console.log(res.listData);
                                    var items = res.listData;
                                    var failItems = '';
                                    for(var i = 0; i < items.length; i++){
                                        failItems += items[i].serviceName;
                                        failItems += '，';
                                    };
                                    failItems = failItems.substring(0, failItems.length - 1);
                                    layer.alert('当前商品信息不满足推送条件！<br/>待办事项如下:<br/>' + failItems +
                                        '<br/>对应处理人：' + res.data + '<br/>系统已通知相关责任人!', { area: ['580px', '260px']});
                                } else {
                                    errorTip(res.message || '操作异常');
                                }
                            }
                        })
                    }
                    return false;
                }
            }, {
                name: '取消'
            }],
        })
    });



    var checkPicLen = function(){
        var imgs = $('.J-upload .J-upload-item').length;
        if(imgs > 0){
            $('.J-upload-error').hide();
        }else{
            $('.J-upload-error').show().find('label').html('请上传1-5张商品图片。').show();
        }

        return imgs > 0;
    };

    var submitData = function(data, noRefresh){
        isChange = true;
        var sendData = $.extend({}, {
                oprateInfoHtml: '',
                goodsImageArray: [],
                operateInfoType: $('[name=operateInfoType]').val(),
                skuId: $('[name=skuId]').val()
            }
        , data);

        $.ajax({
            url: page_url + '/vgoods/operate/static/saveSkuOperate.do',
            type: 'post',
            data: sendData,
            dataType: 'json',
            success: function (res) {
                if(res.success){
                    if(!noRefresh){
                        window.location.reload();
                    }
                }else{
                    GLOBAL.showGlobalTip(res.message || '操作失败', 'warn');
                }
            },
            complete: function(){
                isChange = false
            }
        })
    };

    //点击弹出编辑图片
    $('.J-edit-pic').click(function () {
        var dialog = new artDialog({
            title: '编辑商品主图',
            content: $('.J-dlg-pic-tmpl').html(),
            init: function () {
                var timeout = null;
                new Upload({
                    limit: 5,
                    wrapper: $('.J-upload'),
                    uploadName: 'upload',
                    url: page_url + '/vgoods/operate/fileUploadImg.do?scene=goods_main&side=pc',
                    dragable: true,
                    list: JSON.parse($('.J-upload-data').val() || '[]'),
                    onchange: function (data) {
                        $('.J-upload').find('.J-upload-item').each(function (ii) {
                            var data = $(this).data('item');
                            $(this).find('.J-item-name').remove();
                            $(this).append('<input type="hidden" class="J-item-name" name="goodsImage" value="' + data.httpUrl + data.filePath+ '">');
                        });
                        checkPicLen();
                    },
                    filters: {
                        mime_types: [
                            { title: "Image files", extensions: "jpg,jpeg" }
                        ],
                        max_file_size: '300KB'
                    },
                    onError: function (error) {
                        var errorMsg = {
                            TYPE: '上传图片格式为：JPG、JEPG格式',
                            SIZE: '上传图片大小不能超过300KB'
                        };
                        if (error) {
                            var $error = $('.J-upload-error');
                            $error.show().find('label').html(errorMsg[error]).show();
                            timeout && clearTimeout(timeout);
                            timeout = setTimeout(function () {
                                $error.hide();
                            }, 3000)
                        }
                    },
                    handlerError: function (res){
                        var $error = $('.J-upload-error');
                        $error.show().find('label').html(res.message).show();
                        timeout && clearTimeout(timeout);
                        timeout = setTimeout(function () {
                            $error.hide();
                        }, 3000)
                    }
                });
            },
            width: 800,
            button: [{
                name: '提交',
                highlight: true,
                callback: function () {
                    if(checkPicLen()){
                        var list = [];
                        $('.J-upload-item').each(function (i, item) {
                            list.push($(this).find('[name=goodsImage]').val());
                        });
                        submitData({
                            goodsImageArray: list
                        });
                    }
                    return false;
                }
            }, {
                name: '取消'
            }]
        })
    });

    //图片查看
    $('.J-prod-pic-item').click(function () {
        var src = $(this).data('large');

        $('.J-prod-pic-large-wrap').css('display', 'flex').find('img').attr('src', src);
    });

    $('.J-prod-pic-large-wrap').click(function () {
        $(this).hide();
    });

    //切换类型
    var checkDetailType = function(){
        var val = $('[name=detailType]:checked').val();

        if(val == 1){
            $('.J-rich-html-wrap').show();
            $('.J-ml-html-wrap').hide();
        }else if(val == 2){
            $('.J-rich-html-wrap').hide();
            $('.J-ml-html-wrap').show();
        }
    };

    checkDetailType();

    $('[name=detailType]').change(function(){
        var val = $('[name=detailType]:checked').val();
        submitData({
            detailType: val
        });

        checkDetailType();
    });


    var editor = null;

    var checkRichTxt = function(){
        var hasVal = $('[name=oprateInfoHtml]').length && $('[name=oprateInfoHtml]').val();
        if(hasVal){
            $('.J-rich-error').hide();
        }else{
            $('.J-rich-error').show().find('label').html('请输入内容。').show();
        }

        return hasVal;
    };

    UEDITOR_CONFIG.maximumWords = 2000;
    UEDITOR_CONFIG.initialFrameWidth = 900;
    UEDITOR_CONFIG.toolbars = [[
        'source', '|', 'undo', 'redo', '|',
        'bold', 'italic', 'underline', 'removeformat', 'formatmatch', '|', 'forecolor', 'backcolor', '|',
        'customstyle', '|',
        'justifyleft', 'justifycenter', 'justifyright', 'justifyjustify', '|',
        'link', 'simpleupload', 'insertimage', /*'insertvideo', 'music', 'attachment', 'map', 'insertframe',*/ '|',
        'horizontal',/* 'snapscreen',*/
        'inserttable', 'deletetable',
        'preview', 'searchreplace'/*, 'drafts'*/
    ]];

    UE.Editor.prototype._bkGetActionUrl = UE.Editor.prototype.getActionUrl;
    UE.Editor.prototype.getActionUrl = function (action) {
        if (action == 'uploadimage' || action == 'getConfigFilePathuploadscrawl'
            || action == 'uploadimage' || action == 'uploadfile'
            || action == 'uploadvideo') {//上传附件类型
            return page_url + '/fileUpload/ueditFileUpload.do?uploadType=uedit&scene=goods_detail&side=pc';//自定义编辑器中附件上传路径
        } else {
            return this._bkGetActionUrl.call(this, action);//插件默认上传
        }
    };
    editor = UE.getEditor('content');

    editor.addListener('blur', function () {
        setTimeout(function () {
            checkRichTxt();
        }, 100)
    });

    editor.addListener('contentchange', function () {
        setTimeout(function () {
            editor.enableAutoHeight();
        }, 1000)
    });

    //编辑富文本
    $('.J-edit-rich').click(function () {
        $('.J-dlg-rich-wrap').css('display', 'flex');
        $('body').css('overflow', 'hidden');
        setTimeout(function () {
            editor.setContent($('.J-rich-tmpl').html());
            editor.enableAutoHeight();
        }, 300)
    });

    $('.J-hide-rich-dlg').click(function () {
        $('.J-dlg-rich-wrap').hide();
        $('body').css('overflow', 'auto');
    });

    $('.J-rich-submit').click(function(){
        setTimeout(function () {
            if(checkRichTxt()){
                submitData({
                    oprateInfoHtml: $('[name=oprateInfoHtml]').val()
                })
            }
        }, 300);
    });

    //编辑马良
    $('.J-edit-maliang').click(function () {
        var dia = artDialog.alert('在使用马良编辑，并提交后，刷新本页面。', '温馨提示', {
            fn: function () {
                window.location.reload();
            }, text: '我知道了'
        }, { type: "warn" });
    });

    //操作提示
    GLOBAL.showGlobalTip('操作成功', null, 'operateSetSuccess');

    //查看大图
    GLOBAL.showLargePic('.J-show-big');



    //新增\删除SEO
    var attrTmpl = $('.J-seo-tmpl').html();
    var $add = $('.J-seo-add');
    var $wrap = $('.J-seo-wrap');

    var changeVal = function () {
        $('.J-item-wrap').each(function (i) {


            var $attrvalue = $(this).find('.J-attr-value');
            var $errorwrap = $attrvalue.siblings('.feedback-block');
            var $error = $errorwrap.find('label');

            $attrvalue.attr('id', 'seoKeyWordsArray' + i);
            $errorwrap.length && $errorwrap.attr('wrapfor', 'seoKeyWordsArray' + i);
            $error.length && $error.attr('for', 'seoKeyWordsArray' + i);


        });
    };

    changeVal();

    var checkAttrLen = function () {
        var attrLength = $('.J-item-wrap').length;

        if (attrLength <= 1) {
            $('.J-seo-del').hide();
        } else {
            $('.J-seo-del').show();
        }

        $('.J-attr-num').html(attrLength);
    };

    checkAttrLen();

    $add.click(function () {
        var seoKeyWords = document.getElementsByName("seoKeyWordsArray");
        if (seoKeyWords.length >= 10) {
            layer.alert("最多输入10条SEO关键词");
            return false;
        }
        $wrap.append(attrTmpl);
        checkAttrLen();
        changeVal();
    });

    $wrap.on('click', '.J-seo-del', function () {
        $(this).parents('.J-item-wrap:first').remove();
        checkAttrLen();
        changeVal();
    });

    $('[valid-max]').each(function () {
        $(this).rules('add', {
            maxlength: $(this).attr('valid-max')
        })
    })

})