package com.vedeng.erp.kingdee.batch.dto;

import com.vedeng.erp.kingdee.dto.KingDeeSalesVatPlainInvoiceDto;
import com.vedeng.erp.kingdee.dto.KingDeeSalesVatSpecialInvoiceDto;
import com.vedeng.erp.kingdee.dto.OutPutFeePlainInvoiceDto;
import com.vedeng.erp.kingdee.dto.OutPutFeeSpecialInvoiceDto;
import lombok.Data;

/**
 * 金蝶推送运输对象
 *
 * <AUTHOR>
 */
@Data
public class BatchKingDeePurchaseInvoiceDto {

    /**
     * 普通商品普票
     */
    private KingDeeSalesVatPlainInvoiceDto kingDeeSalesVatPlainInvoiceDto;

    /**
     * 普通商品专票
     */
    private KingDeeSalesVatSpecialInvoiceDto kingDeeSalesVatSpecialInvoiceDto;


    /**
     * 销项费用普通发票
     */
    private OutPutFeePlainInvoiceDto outPutFeePlainInvoiceDto;

    /**
     * 销项费用专用发票
     */
    private OutPutFeeSpecialInvoiceDto outPutFeeSpecialInvoiceDto;

}
