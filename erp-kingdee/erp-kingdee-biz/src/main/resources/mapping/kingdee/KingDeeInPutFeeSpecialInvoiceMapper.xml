<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.repository.mappers.KingDeeInPutFeeSpecialInvoiceMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.domain.entity.KingDeeInPutFeeSpecialInvoiceEntity">
    <!--@mbg.generated-->
    <!--@Table KING_DEE_IN_PUT_FEE_SPECIAL_INVOICE_ENTITY-->
    <id column="IN_PUT_FEE_SPECIAL_INVOICE_ID" jdbcType="INTEGER" property="inPutFeeSpecialInvoiceId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="FID" jdbcType="VARCHAR" property="fid" />
    <result column="F_QZOK_BDDJTID" jdbcType="VARCHAR" property="fQzokBddjtid" />
    <result column="FINVOICENO" jdbcType="VARCHAR" property="finvoiceno" />
    <result column="F_QZOK_FPDM" jdbcType="VARCHAR" property="fQzokFpdm" />
    <result column="FINVOICEDATE" jdbcType="VARCHAR" property="finvoicedate" />
    <result column="FDATE" jdbcType="VARCHAR" property="fdate" />
    <result column="FCONTACTUNITTYPE" jdbcType="VARCHAR" property="fcontactunittype" />
    <result column="F_BILL_TYPE_ID" jdbcType="VARCHAR" property="fBillTypeId" />
    <result column="FSUPPLIERID" jdbcType="VARCHAR" property="fsupplierid" />
    <result column="FSETTLEORGID" jdbcType="VARCHAR" property="fsettleorgid" />
    <result column="FPURCHASEORGID" jdbcType="VARCHAR" property="fpurchaseorgid" />
    <result column="FDOCUMENTSTATUS" jdbcType="VARCHAR" property="fdocumentstatus" />
    <result column="F_RED_BLUE" jdbcType="VARCHAR" property="fRedBlue" />
    <result column="FPUREXPINVENTRY" jdbcType="VARCHAR" property="fpurexpinventry" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    IN_PUT_FEE_SPECIAL_INVOICE_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, 
    UPDATER_NAME, FID, F_QZOK_BDDJTID, FINVOICENO, F_QZOK_FPDM, FINVOICEDATE, FDATE, 
    FCONTACTUNITTYPE, F_BILL_TYPE_ID, FSUPPLIERID, FSETTLEORGID, FPURCHASEORGID, FDOCUMENTSTATUS, 
    F_RED_BLUE, FPUREXPINVENTRY
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from KING_DEE_IN_PUT_FEE_SPECIAL_INVOICE_ENTITY
    where IN_PUT_FEE_SPECIAL_INVOICE_ID = #{inPutFeeSpecialInvoiceId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from KING_DEE_IN_PUT_FEE_SPECIAL_INVOICE_ENTITY
    where IN_PUT_FEE_SPECIAL_INVOICE_ID = #{inPutFeeSpecialInvoiceId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="IN_PUT_FEE_SPECIAL_INVOICE_ID" keyProperty="inPutFeeSpecialInvoiceId" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeInPutFeeSpecialInvoiceEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_IN_PUT_FEE_SPECIAL_INVOICE_ENTITY (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      FID, F_QZOK_BDDJTID, FINVOICENO, 
      F_QZOK_FPDM, FINVOICEDATE, FDATE, 
      FCONTACTUNITTYPE, F_BILL_TYPE_ID, FSUPPLIERID, 
      FSETTLEORGID, FPURCHASEORGID, FDOCUMENTSTATUS, 
      F_RED_BLUE, FPUREXPINVENTRY)
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{fid,jdbcType=VARCHAR}, #{fQzokBddjtid,jdbcType=VARCHAR}, #{finvoiceno,jdbcType=VARCHAR}, 
      #{fQzokFpdm,jdbcType=VARCHAR}, #{finvoicedate,jdbcType=VARCHAR}, #{fdate,jdbcType=VARCHAR}, 
      #{fcontactunittype,jdbcType=VARCHAR}, #{fBillTypeId,jdbcType=VARCHAR}, #{fsupplierid,jdbcType=VARCHAR}, 
      #{fsettleorgid,jdbcType=VARCHAR}, #{fpurchaseorgid,jdbcType=VARCHAR}, #{fdocumentstatus,jdbcType=VARCHAR}, 
      #{fRedBlue,jdbcType=VARCHAR}, #{fpurexpinventry,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="IN_PUT_FEE_SPECIAL_INVOICE_ID" keyProperty="inPutFeeSpecialInvoiceId" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeInPutFeeSpecialInvoiceEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_IN_PUT_FEE_SPECIAL_INVOICE_ENTITY
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="fid != null">
        FID,
      </if>
      <if test="fQzokBddjtid != null">
        F_QZOK_BDDJTID,
      </if>
      <if test="finvoiceno != null">
        FINVOICENO,
      </if>
      <if test="fQzokFpdm != null">
        F_QZOK_FPDM,
      </if>
      <if test="finvoicedate != null">
        FINVOICEDATE,
      </if>
      <if test="fdate != null">
        FDATE,
      </if>
      <if test="fcontactunittype != null">
        FCONTACTUNITTYPE,
      </if>
      <if test="fBillTypeId != null">
        F_BILL_TYPE_ID,
      </if>
      <if test="fsupplierid != null">
        FSUPPLIERID,
      </if>
      <if test="fsettleorgid != null">
        FSETTLEORGID,
      </if>
      <if test="fpurchaseorgid != null">
        FPURCHASEORGID,
      </if>
      <if test="fdocumentstatus != null">
        FDOCUMENTSTATUS,
      </if>
      <if test="fRedBlue != null">
        F_RED_BLUE,
      </if>
      <if test="fpurexpinventry != null">
        FPUREXPINVENTRY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fid != null">
        #{fid,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBddjtid != null">
        #{fQzokBddjtid,jdbcType=VARCHAR},
      </if>
      <if test="finvoiceno != null">
        #{finvoiceno,jdbcType=VARCHAR},
      </if>
      <if test="fQzokFpdm != null">
        #{fQzokFpdm,jdbcType=VARCHAR},
      </if>
      <if test="finvoicedate != null">
        #{finvoicedate,jdbcType=VARCHAR},
      </if>
      <if test="fdate != null">
        #{fdate,jdbcType=VARCHAR},
      </if>
      <if test="fcontactunittype != null">
        #{fcontactunittype,jdbcType=VARCHAR},
      </if>
      <if test="fBillTypeId != null">
        #{fBillTypeId,jdbcType=VARCHAR},
      </if>
      <if test="fsupplierid != null">
        #{fsupplierid,jdbcType=VARCHAR},
      </if>
      <if test="fsettleorgid != null">
        #{fsettleorgid,jdbcType=VARCHAR},
      </if>
      <if test="fpurchaseorgid != null">
        #{fpurchaseorgid,jdbcType=VARCHAR},
      </if>
      <if test="fdocumentstatus != null">
        #{fdocumentstatus,jdbcType=VARCHAR},
      </if>
      <if test="fRedBlue != null">
        #{fRedBlue,jdbcType=VARCHAR},
      </if>
      <if test="fpurexpinventry != null">
        #{fpurexpinventry,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeInPutFeeSpecialInvoiceEntity">
    <!--@mbg.generated-->
    update KING_DEE_IN_PUT_FEE_SPECIAL_INVOICE_ENTITY
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fid != null">
        FID = #{fid,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBddjtid != null">
        F_QZOK_BDDJTID = #{fQzokBddjtid,jdbcType=VARCHAR},
      </if>
      <if test="finvoiceno != null">
        FINVOICENO = #{finvoiceno,jdbcType=VARCHAR},
      </if>
      <if test="fQzokFpdm != null">
        F_QZOK_FPDM = #{fQzokFpdm,jdbcType=VARCHAR},
      </if>
      <if test="finvoicedate != null">
        FINVOICEDATE = #{finvoicedate,jdbcType=VARCHAR},
      </if>
      <if test="fdate != null">
        FDATE = #{fdate,jdbcType=VARCHAR},
      </if>
      <if test="fcontactunittype != null">
        FCONTACTUNITTYPE = #{fcontactunittype,jdbcType=VARCHAR},
      </if>
      <if test="fBillTypeId != null">
        F_BILL_TYPE_ID = #{fBillTypeId,jdbcType=VARCHAR},
      </if>
      <if test="fsupplierid != null">
        FSUPPLIERID = #{fsupplierid,jdbcType=VARCHAR},
      </if>
      <if test="fsettleorgid != null">
        FSETTLEORGID = #{fsettleorgid,jdbcType=VARCHAR},
      </if>
      <if test="fpurchaseorgid != null">
        FPURCHASEORGID = #{fpurchaseorgid,jdbcType=VARCHAR},
      </if>
      <if test="fdocumentstatus != null">
        FDOCUMENTSTATUS = #{fdocumentstatus,jdbcType=VARCHAR},
      </if>
      <if test="fRedBlue != null">
        F_RED_BLUE = #{fRedBlue,jdbcType=VARCHAR},
      </if>
      <if test="fpurexpinventry != null">
        FPUREXPINVENTRY = #{fpurexpinventry,jdbcType=VARCHAR},
      </if>
    </set>
    where IN_PUT_FEE_SPECIAL_INVOICE_ID = #{inPutFeeSpecialInvoiceId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeInPutFeeSpecialInvoiceEntity">
    <!--@mbg.generated-->
    update KING_DEE_IN_PUT_FEE_SPECIAL_INVOICE_ENTITY
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      FID = #{fid,jdbcType=VARCHAR},
      F_QZOK_BDDJTID = #{fQzokBddjtid,jdbcType=VARCHAR},
      FINVOICENO = #{finvoiceno,jdbcType=VARCHAR},
      F_QZOK_FPDM = #{fQzokFpdm,jdbcType=VARCHAR},
      FINVOICEDATE = #{finvoicedate,jdbcType=VARCHAR},
      FDATE = #{fdate,jdbcType=VARCHAR},
      FCONTACTUNITTYPE = #{fcontactunittype,jdbcType=VARCHAR},
      F_BILL_TYPE_ID = #{fBillTypeId,jdbcType=VARCHAR},
      FSUPPLIERID = #{fsupplierid,jdbcType=VARCHAR},
      FSETTLEORGID = #{fsettleorgid,jdbcType=VARCHAR},
      FPURCHASEORGID = #{fpurchaseorgid,jdbcType=VARCHAR},
      FDOCUMENTSTATUS = #{fdocumentstatus,jdbcType=VARCHAR},
      F_RED_BLUE = #{fRedBlue,jdbcType=VARCHAR},
      FPUREXPINVENTRY = #{fpurexpinventry,jdbcType=VARCHAR}
    where IN_PUT_FEE_SPECIAL_INVOICE_ID = #{inPutFeeSpecialInvoiceId,jdbcType=INTEGER}
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update KING_DEE_IN_PUT_FEE_SPECIAL_INVOICE_ENTITY
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when IN_PUT_FEE_SPECIAL_INVOICE_ID = #{item.inPutFeeSpecialInvoiceId,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when IN_PUT_FEE_SPECIAL_INVOICE_ID = #{item.inPutFeeSpecialInvoiceId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when IN_PUT_FEE_SPECIAL_INVOICE_ID = #{item.inPutFeeSpecialInvoiceId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when IN_PUT_FEE_SPECIAL_INVOICE_ID = #{item.inPutFeeSpecialInvoiceId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when IN_PUT_FEE_SPECIAL_INVOICE_ID = #{item.inPutFeeSpecialInvoiceId,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when IN_PUT_FEE_SPECIAL_INVOICE_ID = #{item.inPutFeeSpecialInvoiceId,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="FID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fid != null">
            when IN_PUT_FEE_SPECIAL_INVOICE_ID = #{item.inPutFeeSpecialInvoiceId,jdbcType=INTEGER} then #{item.fid,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_BDDJTID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokBddjtid != null">
            when IN_PUT_FEE_SPECIAL_INVOICE_ID = #{item.inPutFeeSpecialInvoiceId,jdbcType=INTEGER} then #{item.fQzokBddjtid,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="FINVOICENO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.finvoiceno != null">
            when IN_PUT_FEE_SPECIAL_INVOICE_ID = #{item.inPutFeeSpecialInvoiceId,jdbcType=INTEGER} then #{item.finvoiceno,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_FPDM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokFpdm != null">
            when IN_PUT_FEE_SPECIAL_INVOICE_ID = #{item.inPutFeeSpecialInvoiceId,jdbcType=INTEGER} then #{item.fQzokFpdm,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="FINVOICEDATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.finvoicedate != null">
            when IN_PUT_FEE_SPECIAL_INVOICE_ID = #{item.inPutFeeSpecialInvoiceId,jdbcType=INTEGER} then #{item.finvoicedate,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="FDATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fdate != null">
            when IN_PUT_FEE_SPECIAL_INVOICE_ID = #{item.inPutFeeSpecialInvoiceId,jdbcType=INTEGER} then #{item.fdate,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="FCONTACTUNITTYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fcontactunittype != null">
            when IN_PUT_FEE_SPECIAL_INVOICE_ID = #{item.inPutFeeSpecialInvoiceId,jdbcType=INTEGER} then #{item.fcontactunittype,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_BILL_TYPE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fBillTypeId != null">
            when IN_PUT_FEE_SPECIAL_INVOICE_ID = #{item.inPutFeeSpecialInvoiceId,jdbcType=INTEGER} then #{item.fBillTypeId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="FSUPPLIERID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fsupplierid != null">
            when IN_PUT_FEE_SPECIAL_INVOICE_ID = #{item.inPutFeeSpecialInvoiceId,jdbcType=INTEGER} then #{item.fsupplierid,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="FSETTLEORGID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fsettleorgid != null">
            when IN_PUT_FEE_SPECIAL_INVOICE_ID = #{item.inPutFeeSpecialInvoiceId,jdbcType=INTEGER} then #{item.fsettleorgid,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="FPURCHASEORGID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fpurchaseorgid != null">
            when IN_PUT_FEE_SPECIAL_INVOICE_ID = #{item.inPutFeeSpecialInvoiceId,jdbcType=INTEGER} then #{item.fpurchaseorgid,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="FDOCUMENTSTATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fdocumentstatus != null">
            when IN_PUT_FEE_SPECIAL_INVOICE_ID = #{item.inPutFeeSpecialInvoiceId,jdbcType=INTEGER} then #{item.fdocumentstatus,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_RED_BLUE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fRedBlue != null">
            when IN_PUT_FEE_SPECIAL_INVOICE_ID = #{item.inPutFeeSpecialInvoiceId,jdbcType=INTEGER} then #{item.fRedBlue,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="FPUREXPINVENTRY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fpurexpinventry != null">
            when IN_PUT_FEE_SPECIAL_INVOICE_ID = #{item.inPutFeeSpecialInvoiceId,jdbcType=INTEGER} then #{item.fpurexpinventry,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where IN_PUT_FEE_SPECIAL_INVOICE_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.inPutFeeSpecialInvoiceId,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="IN_PUT_FEE_SPECIAL_INVOICE_ID" keyProperty="inPutFeeSpecialInvoiceId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_IN_PUT_FEE_SPECIAL_INVOICE_ENTITY
    (ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, FID, F_QZOK_BDDJTID, 
      FINVOICENO, F_QZOK_FPDM, FINVOICEDATE, FDATE, FCONTACTUNITTYPE, F_BILL_TYPE_ID, 
      FSUPPLIERID, FSETTLEORGID, FPURCHASEORGID, FDOCUMENTSTATUS, F_RED_BLUE, FPUREXPINVENTRY
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, 
        #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, #{item.updaterName,jdbcType=VARCHAR}, 
        #{item.fid,jdbcType=VARCHAR}, #{item.fQzokBddjtid,jdbcType=VARCHAR}, #{item.finvoiceno,jdbcType=VARCHAR}, 
        #{item.fQzokFpdm,jdbcType=VARCHAR}, #{item.finvoicedate,jdbcType=VARCHAR}, #{item.fdate,jdbcType=VARCHAR}, 
        #{item.fcontactunittype,jdbcType=VARCHAR}, #{item.fBillTypeId,jdbcType=VARCHAR}, 
        #{item.fsupplierid,jdbcType=VARCHAR}, #{item.fsettleorgid,jdbcType=VARCHAR}, #{item.fpurchaseorgid,jdbcType=VARCHAR}, 
        #{item.fdocumentstatus,jdbcType=VARCHAR}, #{item.fRedBlue,jdbcType=VARCHAR}, #{item.fpurexpinventry,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>

  <select id="selectByFQzokBddjtid" resultMap="BaseResultMap" parameterType="string">
    select
    <include refid="Base_Column_List" />
    from KING_DEE_IN_PUT_FEE_SPECIAL_INVOICE_ENTITY
    where F_QZOK_BDDJTID = #{FQzokBddjtid,jdbcType=VARCHAR}
  </select>
</mapper>