<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="订单打印" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<script type="text/javascript"
        src='<%=basePath%>static/js/jquery.PrintArea.js'></script>
<script type="text/javascript"
        src='<%=basePath%>static/js/order/saleorder/order_print.js?rnd=${resourceVersionKey}'></script>
<link type="text/css" rel="stylesheet" href="<%=basePath%>static/css/general.css?rnd=${resourceVersionKey}">
<link type="text/css" rel="stylesheet" href="<%=basePath%>static/css/manage.css?rnd=${resourceVersionKey}"/>
<style>
    .contract-print-title2 {
        line-height: 1.5;
    }

    .contract-details {
        line-height: 1.5;
    }
</style>
<body>
<input type="hidden" name="saleorderId" id="saleorderId" value="${saleorderVo.saleorderId}">
<c:choose>
    <c:when test="${saleorderVo.contractUrl ne null and saleorderVo.contractUrl ne '' and noStamp}">
        <div  >
            <iframe style="width: 1400px;height: 1300px"  src="${saleorderVo.contractUrl}" frameborder="0"  scrolling="no"></iframe>
        </div>
    </c:when>

<c:otherwise>
<div id="print-contract">
        <%-- 更换字体 --%>
    <style>
        @font-face {
            font-family: 'SourceHanSansCN-Regular';
            src: url('<%=basePath%>static/hansFont/SourceHanSansCN-Regular.otf');
        }

        html, body, #print-contract {
            font-family: "SourceHanSansCN-Regular", sans-serif;
        }
        .keyWord{
            color: rgba(255,255,255,0);
            font-size: 1px;
        }
    </style>
    <table cellpadding="0" cellspacing="0" border="0" width="100%" style="font-size: 1em;">
        <tr style="display:table-header-group;">
            <td>
                <div class="contract-head">
                    <c:if test="${ saleorderVo.customerType == 426}">
                        <img src="<%=basePath%>static/images/logonew.jpg"/>
                    </c:if>
                    <c:if test="${ saleorderVo.customerType == 427 or saleorderVo.customerType==0}">
                        <img src="<%=basePath%>static/images/logonew.jpg" class=""/>
                    </c:if>
                    <div class="contract-number">
                        <div>合同号码: ${saleorderVo.saleorderNo }</div>
                        <div>制单日期:
                            <c:if test="${saleorderVo.validTime ==0 }">
                                ${currTime}
                            </c:if>
                            <c:if test="${saleorderVo.validTime !=0}">
                                <date:date value="${saleorderVo.validTime }" format="yyyy-MM-dd"/>
                            </c:if>
                        </div>
                    </div>
                </div>
            </td>
        </tr>
        <tr style="display:table-row-group;">
            <td>
                <div class="contract-head-title">销售合同</div>
                <div class="contract-print-table">
                    <table>
                        <tbody>
                        <tr class="jiayi">
                            <c:choose>
                                <c:when test="${saleorderVo.orderType eq 7 || saleorderVo.orderType eq 8 }">
                                    <td><span>甲方：</span><span>${saleorderVo.invoiceTraderName}</span></td>
                                </c:when>
                                <c:otherwise>
                                    <td><span>甲方：</span><span>${saleorderVo.traderName }</span></td>
                                </c:otherwise>
                            </c:choose>
                            <td><span>乙方：</span><span>${company.companyName }</span></td>
                        </tr>
                        <tr>
                            <td>
                                <div>
                                    <div style="display: inline-block">
                                        采购人员：${saleorderVo.name }
                                    </div>
                                    <div class="keyWord" style="position:absolute;margin-left: 220px;margin-top: 10px;">$jia$</div>
                                </div>
                                <div style="display: inline-block;">
                                    <span>联系方式：</span>
                                    <span>${saleorderVo.mobile }</span>
                                </div>
                                <div>
                                    <span>开票信息：</span>
                                    <span></span>
                                </div>
                                <div class="overflow-hidden">
                                    <span style="width:71px;float:left;">注册地址/电话：</span>

                                    <span style="float:left;display:inline-block;max-width:-moz-calc(100% - 91px);max-width:-webkit-calc(100% - 91px);max-width:calc(100% - 91px);">${fn:trim(saleorderVo.regAddress) }/${ saleorderVo.regTel}</span>
                                </div>
                                <div>
                                    <span>开户行：</span>
                                    <span>${ saleorderVo.bank}</span>
                                </div>
                                <div>
                                    <span>税号：</span>
                                    <span>${ saleorderVo.taxNum}</span>
                                </div>
                                <div>
                                    <span>账号：</span>
                                    <span>${ saleorderVo.bankAccount}</span>
                                </div>
                            </td>
                            <td>

                                <div>
                                    <div style="display: inline-block">
<%--                                        <c:if test="${saleorderVo.validStatus ==1}">--%>
<%--                                        <img src="<%=basePath%>static/images/order_sign_b.png"--%>
<%--                                             style="position:absolute; margin-left:170px;margin-top: -30px; z-index: 123;">--%>
<%--                                    </c:if>--%>
                                        <span>销售人员：</span>
                                        <span>${detail.realName }<%--<c:if test="${detail.sex eq 0}">&nbsp;女士</c:if><c:if test="${detail.sex eq 1}">&nbsp;先生</c:if>--%>
                                        </span>
                                        <div class="keyWord" style="position:absolute;margin-left: 220px;margin-top: 10px;">$yi$</div>
                                    </div>
                                </div>
                                <div style="display: inline-block;">
                                    <span>联系方式：</span>
                                    <span>${detail.telephone }
                                <c:if test="${detail.telephone!=null && detail.telephone!=''}">/</c:if>
                                            ${detail.mobile }</span>
                                </div>
                                <div>
                                    <span>收款信息：</span>
                                    <span></span>
                                </div>
                                <div>
                                    <span>开户行：</span>
                                    <span>中国建设银行股份有限公司南京中山南路支行</span>
                                </div>
                                <div>
                                    <span>账号：</span>
                                    <span>32001881236052503686 </span>
                                </div>
                                <div>
                                    <span>税号：</span>
                                    <span>91320100589439066H</span>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <div class="contract-print-title2 pl10">发票及货物接收信息</div>
                    <table>
                        <tbody>
                        <tr>
                            <td>
                                <div>
                                    <span> 收票单位：</span>
                                    <span>${saleorderVo.invoiceTraderName }</span>
                                </div>
                                <div>
                                    <span>收票人：</span>
                                    <span>${saleorderVo.invoiceTraderContactName }/${saleorderVo.invoiceTraderContactMobile }/${saleorderVo.invoiceTraderContactTelephone }</span>
                                </div>
                                <div>
                                </div>
                                <div>
                                    <span>地址：</span>
                                    <span>${saleorderVo.invoiceTraderArea} ${saleorderVo.invoiceTraderAddress }</span>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <span>收货单位：</span>
                                    <span>${saleorderVo.takeTraderName }</span>
                                </div>
                                <div>
                                    <span>收货人：</span>
                                    <span>${saleorderVo.takeTraderContactName }/${saleorderVo.takeTraderContactMobile }/${saleorderVo.takeTraderContactTelephone }</span>
                                    <div>
                                        <span>地址：</span>
                                        <span>${saleorderVo.takeTraderArea } ${saleorderVo.takeTraderAddress } </span>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <div class="contract-tips">就乙方向甲方销售产品事宜，经友好协商，由双方于${contractDateList.get(0)}年${contractDateList.get(1)}月${contractDateList.get(2)}日在南京市秦淮区签订本合同，以资遵守。</div>
                    <div class="contract-print-title2 pl10">一、产品信息：</div>
                    <table class="print-product-table">
                        <tbody>
                        <tr class="font-bold tdsmall">
                            <td style="width:4%;">序号</td>
                            <td style="width:8%;">订货号</td>
                            <td style=" ">产品名称</td>
                            <td style="width:6%;">品牌</td>
                            <td style="width:8%;">型号/规格</td>
                            <c:if test="${apparatusType == 1}">
                                <td style="width:11%;">注册证编号/备案编号</td>
                                <td style="width:11%;">注册人/备案人名称</td>
                            </c:if>
                            <td style="width:4%;">数量</td>
                            <td style="width:4%;">单位</td>
                            <td style="width:6%">单价(元）</td>
                            <td style="width:6%;">金额(元）</td>
                            <td style="width:5%;">货期<br/><span style="font-size:0.75em;">(工作日)</span></td>
                            <td style="width:5%;">含安调</td>
                            <td style="width:5%;">备注</td>
                        </tr>
                        <c:set var="count" value="1"></c:set>
                        <c:forEach var="list" items="${saleorderGoodsList}" varStatus="num">
                            <c:if test="${list.isDelete eq 0}">
                                <tr>
                                    <td class=""> ${count}</td>
                                    <c:set var="count" value="${count+1}"></c:set>
                                    <td>${list.sku }</td>
                                    <td>${ list.goodsName}</td>
                                    <td style="">${list.brandName }</td>
                                    <td>
                                        <c:choose>
                                            <c:when test="${list.spuType == 316 || list.spuType == 1008}">
                                                ${list.model}
                                            </c:when>
                                            <c:when test="${list.spuType == 317 || list.spuType == 318}">
                                                ${list.spec}
                                            </c:when>
                                            <c:otherwise>
                                                <c:choose>
                                                    <c:when test="${list.model != null && list.model != ''}">
                                                        ${list.model}
                                                    </c:when>
                                                    <c:otherwise>${list.spec}</c:otherwise>
                                                </c:choose>
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                    <c:if test="${apparatusType == 1}">
                                        <td style="">
                                            <c:choose>
                                                <c:when test="${list.registrationNumber != null && list.registrationNumber != '' }">
                                                    ${list.registrationNumber}
                                                </c:when>
                                                <c:otherwise>
                                                    -
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                        <td style="">
                                            <c:choose>
                                                <c:when test="${list.manufacturerName != null && list.manufacturerName != '' }">
                                                    ${list.manufacturerName}
                                                </c:when>
                                                <c:otherwise>
                                                    -
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                    </c:if>
                                    <td>${list.num }</td>
                                    <td>${list.unitName }</td>
                                    <td>${list.prices }</td>
                                    <td>${list.allPrice}</td>
                                    <td>${list.deliveryCycle }</td>
                                    <td>${list.haveInstallation == 1 ? "是" : "否"}</td>
                                    <td>${list.goodsComments }</td>
                                </tr>
                            </c:if>
                        </c:forEach>
                        <tr>
                            <td>合计</td>
                            <td colspan="${apparatusType == 1? 11 : 9}" style="overflow: hidden;">
                                <div class="total-count f_left">（大写）${chineseNumberTotalPrice }</div>
                                <div class="total-count-num f_right">￥${totalAmount}</div>
                            </td>
                            <td colspan="2"></td>
                        </tr>
                        </tbody>
                    </table>
                    <div class="contract-print-title2">二、付款方式&发票类型：</div>
                    <div class="contract-details">
                        1、 付款方式：
                        <c:choose>
                            <%--兼容存在尾款订单的付款方式(质保金上线之前原逻辑展示)--%>
                            <c:when test="${saleorderVo.validStatus eq 1 and saleorderVo.validTime lt retentionMoneyUptime}">
                                <c:if test="${saleorderVo.paymentType == 419}">
                                    先款后货，预付100% ，预付金额${saleorderVo.prepaidAmount }元
                                </c:if>
                                <c:if test="${saleorderVo.paymentType == 420}">
                                    先货后款，预付80%，预付金额${saleorderVo.prepaidAmount }元，账期支付${saleorderVo.accountPeriodAmount }元
                                </c:if>
                                <c:if test="${saleorderVo.paymentType == 421}">
                                    先货后款，预付50%，预付金额${saleorderVo.prepaidAmount }元，账期支付${saleorderVo.accountPeriodAmount }元
                                </c:if>
                                <c:if test="${saleorderVo.paymentType == 422}">
                                    先货后款，预付30%，预付金额${saleorderVo.prepaidAmount }元，账期支付${saleorderVo.accountPeriodAmount }元
                                </c:if>
                                <c:if test="${saleorderVo.paymentType == 423}">
                                    先货后款，预付0%，预付金额${saleorderVo.prepaidAmount }元，账期支付${saleorderVo.accountPeriodAmount }元
                                </c:if>
                                <c:if test="${saleorderVo.paymentType == 424}">
                                    自定义，预付金额${saleorderVo.prepaidAmount }元，账期支付${saleorderVo.accountPeriodAmount }元，尾款${saleorderVo.retainageAmount }元
                                    ，尾款期限${saleorderVo.retainageAmountMonth }月
                                </c:if>
                            </c:when>
                            <c:otherwise>
                                <%--   质保金上线之后使用最新合同的付款方式--%>
                                <c:choose>
                                    <%--预付100%--%>
                                    <c:when test="${saleorderVo.paymentType == 419}">
                                        先款后货，100%支付，支付金额${saleorderVo.prepaidAmount }元后发货。
                                    </c:when>

                                    <%--预付80%无质保金--%>
                                    <c:when test="${saleorderVo.paymentType == 420 && saleorderVo.retentionMoney.unscaledValue() == 0}">
                                        预付合同总金额的80%后发货（即预付金额为${saleorderVo.prepaidAmount}元），合同余款20% （即${saleorderVo.accountPeriodAmount}元）于发货/开票后${saleorderVo.periodDay}天内付清。
                                    </c:when>
                                    <%--有质保金并且质保金小于账期支付金额--%>
                                    <c:when test="${saleorderVo.paymentType == 420 && saleorderVo.retentionMoney.unscaledValue() != 0 && saleorderVo.retentionMoney lt saleorderVo.accountPeriodAmount}">
                                        预付合同总金额的80%后发货（即预付金额为${saleorderVo.prepaidAmount}元），合同余款<fmt:formatNumber
                                            type="number"
                                            value="${prepaidPercent * 100}"
                                            pattern="0.00"
                                            maxFractionDigits="2"/>%
                                        (即${saleorderVo.accountPeriodAmount - saleorderVo.retentionMoney}元）于发货/开票后${saleorderVo.periodDay}天内付清。合同余款<fmt:formatNumber
                                            type="number" value="${(0.2 - prepaidPercent) * 100}" pattern="0.00"
                                            maxFractionDigits="2"/>%
                                        (即${saleorderVo.retentionMoney}元）作为质保金于发货/开票后${saleorderVo.retentionMoneyDay}天内付清。
                                    </c:when>
                                    <%--有质保金并且质保金等于账期支付金额--%>
                                    <c:when test="${saleorderVo.paymentType == 420 && saleorderVo.retentionMoney.unscaledValue() != 0 && saleorderVo.retentionMoney eq saleorderVo.accountPeriodAmount}">
                                        预付合同总金额的80%后发货（即预付金额为${saleorderVo.prepaidAmount}元），合同余款20%(即${saleorderVo.retentionMoney}元）作为质保金于发货/开票后${saleorderVo.retentionMoneyDay}天内付清。
                                    </c:when>

                                    <%--预付50%无质保金--%>
                                    <c:when test="${saleorderVo.paymentType == 421 && saleorderVo.retentionMoney.unscaledValue() == 0}">
                                        预付合同总金额的50%后发货（即预付金额为${saleorderVo.prepaidAmount}元），合同余款50% （即${saleorderVo.accountPeriodAmount}元）于发货/开票后${saleorderVo.periodDay}天内付清。
                                    </c:when>
                                    <%--有质保金并且质保金小于账期支付金额--%>
                                    <c:when test="${saleorderVo.paymentType == 421 && saleorderVo.retentionMoney.unscaledValue() != 0 && saleorderVo.retentionMoney lt saleorderVo.accountPeriodAmount}">
                                        预付合同总金额的50%后发货（即预付金额为${saleorderVo.prepaidAmount}元），合同余款<fmt:formatNumber
                                            type="number"
                                            value="${prepaidPercent * 100}"
                                            pattern="0.00"
                                            maxFractionDigits="2"/>%
                                        (即${saleorderVo.accountPeriodAmount - saleorderVo.retentionMoney}元）于发货/开票后${saleorderVo.periodDay}天内付清。合同余款<fmt:formatNumber
                                            type="number" value="${(0.5 - prepaidPercent)* 100}" pattern="0.00"
                                            maxFractionDigits="2"/>%
                                        (即${saleorderVo.retentionMoney}元）作为质保金于发货/开票后${saleorderVo.retentionMoneyDay}天内付清。
                                    </c:when>
                                    <%--有质保金并且质保金等于账期支付金额--%>
                                    <c:when test="${saleorderVo.paymentType == 421 && saleorderVo.retentionMoney.unscaledValue() != 0 && saleorderVo.retentionMoney eq saleorderVo.accountPeriodAmount}">
                                        预付合同总金额的50%后发货（即预付金额为${saleorderVo.prepaidAmount}元），合同余款50%(即${saleorderVo.retentionMoney}元）作为质保金于发货/开票后${saleorderVo.retentionMoneyDay}天内付清。
                                    </c:when>

                                    <%--预付30%无质保金--%>
                                    <c:when test="${saleorderVo.paymentType == 422 && saleorderVo.retentionMoney.unscaledValue() == 0}">
                                        预付合同总金额的30%后发货（即预付金额为${saleorderVo.prepaidAmount}元），合同余款70% （即${saleorderVo.accountPeriodAmount}元）于发货/开票后${saleorderVo.periodDay}天内付清。
                                    </c:when>
                                    <%--有质保金并且质保金小于账期支付金额--%>
                                    <c:when test="${saleorderVo.paymentType == 422 && saleorderVo.retentionMoney.unscaledValue() != 0 && saleorderVo.retentionMoney lt saleorderVo.accountPeriodAmount}">
                                        预付合同总金额的30%后发货（即预付金额为${saleorderVo.prepaidAmount}元），合同余款<fmt:formatNumber
                                            type="number"
                                            value="${prepaidPercent * 100}"
                                            pattern="0.00"
                                            maxFractionDigits="2"/>%
                                        (即${saleorderVo.accountPeriodAmount - saleorderVo.retentionMoney}元）于发货/开票后${saleorderVo.periodDay}天内付清。合同余款<fmt:formatNumber
                                            type="number" value="${(0.7 - prepaidPercent) * 100}" pattern="0.00"
                                            maxFractionDigits="2"/>%
                                        (即${saleorderVo.retentionMoney}元）作为质保金于发货/开票后${saleorderVo.retentionMoneyDay}天内付清。
                                    </c:when>
                                    <%--有质保金并且质保金等于账期支付金额--%>
                                    <c:when test="${saleorderVo.paymentType == 422 && saleorderVo.retentionMoney.unscaledValue() != 0 && saleorderVo.retentionMoney eq saleorderVo.accountPeriodAmount}">
                                        预付合同总金额的30%后发货（即预付金额为${saleorderVo.prepaidAmount}元），合同余款70%(即${saleorderVo.retentionMoney}元）作为质保金于发货/开票后${saleorderVo.retentionMoneyDay}天内付清。
                                    </c:when>

                                    <%--无预付款 无质保金情况--%>
                                    <c:when test="${saleorderVo.paymentType == 423 && saleorderVo.retentionMoney.unscaledValue() == 0}">
                                        先货后款，合同总金额（即${saleorderVo.accountPeriodAmount}元）于收发货/开票后${saleorderVo.periodDay}天内付清。
                                    </c:when>
                                    <%--无预付款 有质保金情况--%>
                                    <c:when test="${saleorderVo.paymentType == 423 && saleorderVo.retentionMoney.unscaledValue() != 0}">
                                        先货后款，合同金额<fmt:formatNumber type="number" value="${prepaidPercent * 100}"
                                                                   pattern="0.00"
                                                                   maxFractionDigits="2"/>%
                                        (即${saleorderVo.accountPeriodAmount - saleorderVo.retentionMoney}元）于发货/开票后${saleorderVo.periodDay}天内付清，合同余款
                                        <fmt:formatNumber type="number" value="${(1 - prepaidPercent) * 100}"
                                                          pattern="0.00"
                                                          maxFractionDigits="2"/>%
                                        (即${saleorderVo.retentionMoney}元）作为质保金于发货/开票后${saleorderVo.retentionMoneyDay}天内付清。
                                    </c:when>

                                    <%--自定义付款情况--%>
                                    <%-- 先款后货--%>
                                    <c:when test="${saleorderVo.paymentType == 424 && saleorderVo.prepaidAmount eq totalAmountBigDecimal}">
                                        先款后货，100%支付，支付金额${saleorderVo.prepaidAmount}元后发货。
                                    </c:when>
                                    <%--预付+账期支付（无质保金）--%>
                                    <c:when test="${saleorderVo.paymentType == 424 && saleorderVo.prepaidAmount .unscaledValue() != 0 && saleorderVo.accountPeriodAmount.unscaledValue() != 0 && saleorderVo.retentionMoney.unscaledValue() == 0}">
                                        预付合同总金额的<fmt:formatNumber type="number" value="${prepaidPercent* 100}"
                                                                  pattern="0.00"
                                                                  maxFractionDigits="2"/>%
                                        后发货（即预付金额为${saleorderVo.prepaidAmount}元），合同余款<fmt:formatNumber type="number"
                                                                                                       value="${accountPrepaidPercent* 100}"
                                                                                                       pattern="0.00"
                                                                                                       maxFractionDigits="2"/>%
                                        （即${saleorderVo.accountPeriodAmount}元）于发货/开票后${saleorderVo.periodDay}天内付清。
                                    </c:when>
                                    <%-- 预付+账期支付（有质保金）, 且质保金<账期支付金额。--%>
                                    <c:when test="${saleorderVo.paymentType == 424 && saleorderVo.prepaidAmount.unscaledValue() != 0 && saleorderVo.accountPeriodAmount.unscaledValue() != 0 && saleorderVo.retentionMoney.unscaledValue() != 0 && saleorderVo.retentionMoney lt saleorderVo.accountPeriodAmount}">
                                        预付合同总金额的<fmt:formatNumber type="number" value="${prepaidPercent* 100}"
                                                                  pattern="0.00"
                                                                  maxFractionDigits="2"/>%
                                        后发货（即预付金额为${saleorderVo.prepaidAmount}元），合同余款<fmt:formatNumber type="number"
                                                                                                       value="${accountPrepaidPercent * 100}"
                                                                                                       pattern="0.00"
                                                                                                       maxFractionDigits="2"/>%
                                        (即${saleorderVo.accountPeriodAmount - saleorderVo.retentionMoney}元）于发货/开票后${saleorderVo.periodDay}天内付清。合同余款
                                        <fmt:formatNumber type="number"
                                                          value="${(1 - prepaidPercent - accountPrepaidPercent) * 100}"
                                                          pattern="0.00" maxFractionDigits="2"/>%
                                        (即${saleorderVo.retentionMoney}元）作为质保金于发货/开票后${saleorderVo.retentionMoneyDay}天内付清。
                                    </c:when>
                                    <%--预付+账期支付（有质保金）, 且质保金=账期支付金额--%>
                                    <c:when test="${saleorderVo.paymentType == 424 && saleorderVo.prepaidAmount.unscaledValue() != 0 && saleorderVo.accountPeriodAmount.unscaledValue() != 0 && saleorderVo.retentionMoney.unscaledValue() != 0 && saleorderVo.retentionMoney eq saleorderVo.accountPeriodAmount}">
                                        预付合同总金额的<fmt:formatNumber type="number" value="${prepaidPercent * 100}"
                                                                  pattern="0.00"
                                                                  maxFractionDigits="2"/>%
                                        后发货（即预付金额为${saleorderVo.prepaidAmount}元），合同余款<fmt:formatNumber type="number"
                                                                                                       value="${(1 - prepaidPercent) * 100}"
                                                                                                       pattern="0.00"
                                                                                                       maxFractionDigits="2"/>%
                                        (即${saleorderVo.retentionMoney}元）作为质保金于发货/开票后${saleorderVo.retentionMoneyDay}天内付清。
                                    </c:when>
                                    <%--    无预付款，100%账期支付（无质保金）。--%>
                                    <c:when test="${saleorderVo.paymentType == 424 && saleorderVo.prepaidAmount.unscaledValue() == 0 && saleorderVo.accountPeriodAmount eq totalAmountBigDecimal && saleorderVo.retentionMoney.unscaledValue() == 0}">
                                        先货后款，合同总金额（即${saleorderVo.accountPeriodAmount}元）于发货/开票后${saleorderVo.periodDay}天内付清。
                                    </c:when>
                                    <%--无预付款，100%账期支付（有质保金）--%>
                                    <c:when test="${saleorderVo.paymentType == 424 && saleorderVo.prepaidAmount.unscaledValue() == 0 && saleorderVo.accountPeriodAmount eq totalAmountBigDecimal && saleorderVo.retentionMoney.unscaledValue() != 0}">
                                        先货后款，合同金额预付合同总金额的<fmt:formatNumber type="number"
                                                                           value="${accountPrepaidPercent * 100}"
                                                                           pattern="0.00" maxFractionDigits="2"/>%
                                        （即${saleorderVo.accountPeriodAmount}元）于发货/开票后${saleorderVo.periodDay}天内付清，合同余款<fmt:formatNumber
                                            type="number" value="${(1 - accountPrepaidPercent)* 100}" pattern="0.00"
                                            maxFractionDigits="2"/>%
                                        (即${saleorderVo.retentionMoney}元）作为质保金于发货/开票后 ${saleorderVo.retentionMoneyDay}天内付清。
                                    </c:when>
                                </c:choose>
                            </c:otherwise>
                        </c:choose>
                        <br/>
                        2、 甲方应将货款支付至乙方指定银行账户，勿将货款以现金形式支付给乙方任何人员，否则乙方有权视甲方未支付该款项。<br/>
                        3、 发票类型：
                        <c:forEach var="list" items="${invoiceTypes}">
                            <c:if test="${saleorderVo.invoiceType == list.sysOptionDefinitionId}">${list.title}</c:if>
                        </c:forEach>
                         ，乙方将按照本合同第一条产品信息开具发票。
                    </div>
                    <div class="contract-print-title2">三、产品交付及安调说明：</div>
                    <div class="contract-details">
                        1、 关于运费：<c:forEach var="list" items="${yfTypes}">
                        <c:if test="${saleorderVo.freightDescription == list.sysOptionDefinitionId}">${list.title}</c:if>
                    </c:forEach><br/>
                        2、 关于安调：<c:if test="${haveInstallationFlag==2}">含安调</c:if><c:if
                            test="${haveInstallationFlag==1}">部分产品含安调（详见"产品信息"）</c:if><c:if
                            test="${haveInstallationFlag==0}">不含安调</c:if><br/>
                        3、 货期自乙方收到甲方预付款后第二个工作日开始计算。国家法定周末和节假日不计算在货期内。如果因为甲方未及时付款导致货期变动的，双方需重新约定交货期。<br/>
                        4、
                        产品送达甲⽅指定收货地点后，甲⽅承担收货义务之指定⼈员、甲方公司员⼯或者甲方关联⽅员⼯需对物流公司配送的产品当场开箱签收，签收范围包括但不限于：产品外部包装是否破损或存有瑕疵；产品的种类、型号、颜⾊、数量等外部形态是否与订单相符。除指定收货⼈以外，甲⽅公司员⼯、甲方关联⽅员⼯对产品的签收亦视为指定收货人的签收。甲⽅的签收⼈员有义务根据⼄⽅的要求将上述已签收送货单或扫描回传或原件寄回给⼄⽅，甲方签收人员也可以通过乙方指定线上平台对乙方所交付产品的信息进行线上确认。甲⽅承担收货义务之指定⼈员、甲方公司员⼯或者甲方关联⽅员⼯在送货单上签字确认之后，将视为甲⽅接受所配送产品，⼄⽅将只在产品质量性能⽅⾯承担责任。甲⽅如发现产品外包装破损或者存有瑕疵，应当场向物流公司提出并在一天之内以书⾯形式通知⼄⽅，如产品的种类、型号、颜⾊、数量等外部形态与订单不符，甲⽅需在三（3）个⼯作⽇内以书⾯形式通知⼄⽅。逾期⼄⽅将不再承担免费调换义务。甲⽅无正当理由拒绝签收产品所带来的损失由甲⽅⾃⼰承担。
                    </div>
                    <div class="contract-print-title2">四、 质量保证：</div>
                    <div class="contract-details">
                        1、 乙方保证供应的产品符合国家标准或相关行业标准，且为全新、原装产品。<br/>
                        2、 乙方按照产品说明书内的质保条件及质保期在中国大陆区域范围内（不包括香港特别行政区、澳门特别行政区和台湾地区）对产品提供质保承诺。质保时间自甲方收到货物时开始计算。<br/>
                        3、
                        在质量保证期内出现的非甲方人为因素损坏的产品质量问题，乙方按照国家或行业标准提供质保服务，提供维修或更换服务。如非质保期内或甲方人为因素导致的货品的损坏，在可修复前提下，乙方提供有偿维修服务。
                    </div>
                    <div class="contract-print-title2">五、退换货：</div>
                    <div class="contract-details">
                        1、
                        甲方在收到货物十（10）个工作日内，1）由于产品质量原因导致无法使用，且经乙方确认不具有可修复性和可更换性，甲方可以要求退货。2）定制类产品出现以上质量问题，乙方仅提供尽可能的维修服务，不接受退货要求。<br/>
                        2、 如甲方订购的货品属于一次性使用产品、易耗品、或产品技术性能决定无法更换的，乙方不承担换货义务。<br/>
                        3、 甲⽅因经验不⾜、决策错误等单⽅⾯因素导致的选型、购买数量等失误，要求退还货物的，⼄⽅不予接受。<br/>
                    </div>
                    <div class="contract-print-title2">六、知识产权：</div>
                    <div class="contract-details">
                        1、 未经乙方事先书面同意，甲方不得在任何国家或地区，并通过任何形式，使用乙方及其关联方的商标、商号或将与乙方商标、商号相同或类似的内容注册为甲方的商标、商号、公司域名等。<br/>
                        2、
                        未经乙方事先书面同意，甲方不得对乙方及其关联方的企业名称、商标、商号、服务标志或标识进行任何商业使用和擅自作出任何变更，包括但不限于在广告、宣传资料、办公地点等使用。<br/>
                        3、
                        对于所售产品的商标、专利、包装设计以及品牌方的商标、商号、域名等均属于品牌方的知识产权，未经品牌方授权许可，甲方不得擅自使用品牌方的商标、商号、域名，也不得侵犯产品相关的知识产权，否则，责任由甲方自行承担，且由此给乙方造成损失的，甲方还应向乙方承担相应的赔偿责任。<br/>
                    </div>
                    <div class="contract-print-title2">七、反商业贿赂：</div>
                    <div class="contract-details">
                        双方应按照所有适用的法律法规，包括但不限于所有适用的反贿赂和反不正当法律法规，履行其在本合同项下的各项义务。甲方确认知晓国家法律、法规和乙方相关制度关于反商业贿赂事项的各项规定。甲方已获乙方正式告知，乙方对于任何形式商业贿赂均持坚决的反对态度，亦不会授权任何员工要求、指示、暗示甲方实施、参与任何形式商业贿赂行为，以获得交易机会或其他经济利益。乙方员工如有实施商业贿赂行为，甲方将第一时间告知乙方。双方在开展业务发展活动过程中，将严格遵守国家法律、法规，不从事、参与任何形式的商业贿赂及不正当竞争行为，以自身行动维护双方良好的合作关系。
                    </div>
                    <div class="contract-print-title2"> 八、合同生效：</div>
                    <div class="contract-details">

                        <c:choose>
                            <c:when test="${saleorderVo.orderType eq 1}">
                                若在合同⽣效后5个⼯作⽇内，甲⽅未付款或未执⾏任何实质性合同内容，合同将⾃动失效。
                            </c:when>
                            <c:otherwise>
                                若在合同⽣效后5个⼯作⽇内，甲⽅未付款或未执⾏任何实质性合同内容，合同将⾃动失效。
                            </c:otherwise>
                        </c:choose>

                    </div>
                    <div class="contract-print-title2">九、合同执行：</div>
                    <div class="contract-details">
                        1、
                        交货期以甲⼄双⽅约定为准，交付时间以⼄⽅交付货物给物流配送⽅时间为准。如果⼄⽅或物流配送⽅遭遇不可抗拒的事件，如地震、疫情、战争、台⻛、游⾏、政府管制、厂家停产、国家政策等不可抗⼒事件，乙方应及时通知甲方，并在⼗（10）个⼯作⽇内书⾯提供不可抗力事件的说明，乙方不承担由于不可抗力事件造成的任何违约责任。在发生不可抗力事件后，双方应当就合同的继续履行或终止履行重新进行协商。物流配送⽅遗失产品，不属于乙方延迟交付责任范畴。如果遇到⼚家停产、国家政策调整等导致⼄⽅不能按时供货，⼄⽅应当在⼗（10）个⼯作⽇内书⾯通知甲⽅，经双⽅协商⼀致，可以变更或终⽌合同。
                        <c:if test="${contractContainSpecialSku eq false}">
                            除以上不可抗拒的因素，⼄⽅延迟交货给甲⽅造成损失的，从延迟交付的第⼗（10）个⼯作⽇起，承担延迟交付的违约责任，每⼯作⽇违约⾦额为⼄⽅延迟交付部分且该部分甲方已付款⾦额的0.3%，违约⾦上限为⼄⽅延迟交付部分且该部分甲方已付款⾦额的3%。
                        </c:if>
                        <br/>
                        2、 本合同中产品，甲⽅承诺只销售或安装使用于____${saleOrderSalesArea}_____（地理区域）_____
                        <c:if test="${ saleorderVo.customerType == 426}">
                            科研医疗
                        </c:if>
                        <c:if test="${ saleorderVo.customerType == 427 or saleorderVo.customerType==0}">
                            临床医疗
                        </c:if>
                        _____（客户类型）。如有违反本条规定，则本合同中的产品不在售后保修范围之内。甲方违反本条约定给乙方造成的直接或间接损失由甲方承担，且甲⽅公司在⻉登信⽤评级下调，直接影响双⽅接下来的合作。
                    </div>
                    <div class="contract-print-title2"> 十、 合同撤销：</div>
                    <div class="contract-details">
                        合同生效后，除因产品停产、不可抗力原因外，甲方撤销合同的，甲方需向乙方支付合同总金额的百分之三（3%）作为违约金，同时甲方应当承担乙方为履行本合同已发生的成本及费用（包括但不限于产品采购成本等）。
                    </div>
                    <div class="contract-print-title2"> 十一、 合同争议处理：</div>
                    <div class="contract-details">
                        对于合同争议，双方需本着友好精神协商解决，协商不成，可将争议提请乙方所在地人民法院诉讼解决。
                    </div>
                    <div style="page-break-inside: avoid; ">
                        <div class="contract-print-title2">十二、合同效力：</div>
                        <div class="contract-details">
                            本合同自双方盖章之日起生效，传真件、扫描件与原件具有同等法律效力。
                        </div>
                        <div class="contract-print-title2">十三、其他约定：</div>
                        <div class="contract-details">
                            若发生因临床紧急等需乙方上游供货商直接将货物发送至甲方指定地点的情形，甲方承诺遵循相关监管法规要求对产品进行收货验收，并在验收完成当日将进货查验记录相关信息传递给乙方。
                        </div>
                        <div class="contract-print-title2">十四、附加条款：</div>
                        <div class="contract-details">${saleorderVo.additionalClause}</div>
                        <div class="contract-print-sign" style="page-break-inside: auto">
                            <ul>
                                <li style="margin-left: 50px;padding:32px 0px 0px 0px;">
                                    <c:choose>
                                        <c:when test="${saleorderVo.orderType eq 7 || saleorderVo.orderType eq 8 }">
                                            <div class="sign-name">甲方：${saleorderVo.invoiceTraderName} <span class="keyWord">$jia$</span> </div>
                                        </c:when>
                                        <c:otherwise>
                                            <div class="sign-name">甲方：${saleorderVo.traderName } <span class="keyWord">$jia$</span> </div>
                                        </c:otherwise>
                                    </c:choose>

<%--                                    <div class="sign-name">授权人签字：</div>--%>
                                    <%--<div class="sign-name">日期：</div>--%>
                                </li>
                                <li style="float: right; margin-right: 100px;overflow:visible;position: relative; padding:32px 0 28px 0;">
<%--                                    <c:if test="${saleorderVo.validStatus ==1}">--%>
<%--                                        <img src="<%=basePath%>static/images/order_sign_b.png"--%>
<%--                                             style="position:absolute; margin-top: -32px;margin-left: 50px; z-index: 123;">--%>
<%--                                    </c:if>--%>
                                    <div class="sign-name">乙方：南京贝登<div class="keyWord" style="position: absolute;margin-left: 66px;margin-top: -19px;">$yi$</div>医疗股份有限公司  </div>

<%--                                    <div class="sign-name">授权人签字：${detail.realName }</div>--%>

                                    <%--<div class="sign-name">日期：<c:if test="${saleorderVo.validTime ==0 }">
                                        ${currTime}
                                    </c:if>
                                        <c:if test="${saleorderVo.validTime !=0}">
                                            <date:date value="${saleorderVo.validTime }" format="yyyy-MM-dd"/>
                                        </c:if></div>--%>
                                </li>
                                <li style="clear: both;"></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </td>
        </tr>
    </table>
    <c:if test="${autoGenerate != null and !autoGenerate}">
        <div class='tcenter mb15'>
            <span class=" bt-small bt-bg-style bg-light-blue" id="btnPrint">打印</span>
            <c:if test="${saleorderVo.validStatus eq 1}">
                <span class=" bt-small bt-bg-style bg-light-blue"  id="signature">电子签章</span>
            </c:if>

        </div>
    </c:if>
</div>
</c:otherwise>
   </c:choose>

<script>
    $("#signature").click(function() {
        let saleorderId = $("#saleorderId").val();
        $.ajax({
            url:page_url+'/orderstream/saleorder/signature.do',
            data:{"saleorderId":saleorderId},
            type:"POST",
            dataType : "json",
            async: false,
            success:function(data)
            {
                if(data.code==0){
                    layer.alert("已发起电子签章流程。可稍后查看，无需频繁发起");
                }else{
                    layer.alert(data.message);
                }

            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });

    });

    $("#btnPrint").click(function () {
        $("#btnPrint").hide();
        if (window.ActiveXObject || "ActiveXObject" in window) {
            $("#print-contract").printArea({
                mode: 'popup'
            });
        } else {
            $("#print-contract").printArea({
                popTitle: "${saleorderVo.saleorderNo}" + " " +
                    <c:choose>
                    <c:when test="${saleorderVo.orderType eq 7 || saleorderVo.orderType eq 8 }">
                    "${saleorderVo.invoiceTraderName}"
                </c:when>
                <c:otherwise>
                "${saleorderVo.traderName }"
                </c:otherwise>
                </c:choose>
            });
        }
        $("#btnPrint").show();
    });
</script>
<%@ include file="../../common/footer.jsp" %>
