package com.vedeng.crm.visitrecord.domain.vo;

import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class VisitDetailButtonDto {

    /**
     * 编辑计划
     */
    private boolean editPlanBtn =false;

    /**
     * 添加拜访记录
     */
    private boolean addVisitRecordBtn =false;

    /**
     * 添加沟通记录
     */
    private boolean addCommuncateRecordBtn=false;

    /**
     * 创建商机
     */
    private boolean createBusinessChanceBtn = false;

    /**
     * 创建下次拜访
     */
    private boolean createNextVisitBtn = false;

    /**
     * 打卡
     */
    private boolean addCardBtn = false;

    /**
     * 编辑打卡
     */
    private boolean editCardBtn = false;

    /**
     * 关闭
     */
    private boolean closeVisitBtn = false;

}
