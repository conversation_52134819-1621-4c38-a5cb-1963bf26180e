package com.vedeng.finance.constant;

/**
 * <AUTHOR>
 */

public enum CapitalBillOrderTypeEnum {
    SALE_ORDER(1, "销售订单"),
    BUY_ORDER(2, "采购订单"),
    AFTER_SALE_ORDER(3, "售后订单"),
    BUY_ORDER_EXPENSE_ORDER(4,"采购费用单"),
    EXPENSE_AFTER_SALE_ORDER(5,"采购费用售后单");

    private Integer code;

    private String desc;

    CapitalBillOrderTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
