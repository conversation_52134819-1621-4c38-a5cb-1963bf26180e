package com.newtask;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;

/**
 * <b>Description:</b><br>
 * 定时任务抓取即将超时和已超时数据
 *
 * <AUTHOR>
 * @Note <b>ProjectName:</b> erp <br>
 * <b>PackageName:</b> com.newtask <br>
 * <b>ClassName:</b> CheckSalesTimeOutTask <br>
 * <b>Date:</b> 2021/6/11 9:13 <br>
 */
@Component
@JobHandler(value="CheckSalesTimeOutTask")
public class CheckSalesTimeOutTask extends AbstractJobHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(CheckSalesTimeOutTask.class);

    @Resource
    private SaleorderMapper saleorderMapper;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        List<Saleorder> threeTimeOutList = new ArrayList<>();
        List<Saleorder> toTimeOutList = new ArrayList<>();
        List<Saleorder> isTimeOutList = new ArrayList<>();
        String nowDate = DateUtil.getNowDate("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(DateUtil.StringToDate(nowDate));
        calendar.add(Calendar.DATE,3);
        String toEndDate = DateUtil.DateToString(calendar.getTime(),"yyyy-MM-dd");
        try{
            //即将超等通知的订单
            threeTimeOutList = getSaleorderList(toEndDate,toEndDate);
            sendTimeoutMessage(threeTimeOutList,182);
            //不足一天超时订单
            toTimeOutList = getSaleorderList(nowDate,nowDate);
            sendTimeoutMessage(toTimeOutList,183);
            calendar.setTime(DateUtil.StringToDate(nowDate));
            calendar.add(Calendar.DATE,-1);
            String isTimeOutDate = DateUtil.DateToString(calendar.getTime(),"yyyy-MM-dd");
            //前一天天已超时的订单
            isTimeOutList = getSaleorderList(isTimeOutDate,isTimeOutDate);
            sendTimeoutMessage(isTimeOutList,184);
            return SUCCESS;
        }catch (Exception e){
            LOGGER.error("推送等通知发货到期任务失败",e.getMessage());
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }
    }
    /**
     * <b>Description:</b><br>
     * 查询订单信息
     *
     * @param startTime, endTime
     * @return java.util.List<com.vedeng.order.model.Saleorder>
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/6/11 15:29
     */
    List<Saleorder> getSaleorderList(String startTime,String endTime){
        String timeOutStartTime = new StringBuffer(startTime).append(" 00:00:00.000").toString();
        String timeOutEndTime = new StringBuffer(endTime).append(" 23:59:59.999").toString();
        return saleorderMapper.selectTimeOutSales(DateUtil.convertLong(timeOutStartTime,"yyyy-MM-dd HH:mm:ss.SSS"),
                DateUtil.convertLong(timeOutEndTime,"yyyy-MM-dd HH:mm:ss.SSS"));
    }

    void sendTimeoutMessage(List<Saleorder> saleorders,int messageTemplate){
        for(Saleorder saleorder : saleorders){
            HashMap<String,String> map = new HashMap();
            List<Integer> users = new ArrayList<>();
            users.add(saleorder.getCreator());
            map.put("saleorderNo",saleorder.getSaleorderNo());
            map.put("timeOutTime",DateUtil.convertString(saleorder.getDeliveryDelayTime(),"yyyy-MM-dd"));
            MessageUtil.sendMessage2(messageTemplate,users,map,"./order/saleorder/view.do?saleorderId="+saleorder.getSaleorderId(),"njadmin");
        }
    }
}
