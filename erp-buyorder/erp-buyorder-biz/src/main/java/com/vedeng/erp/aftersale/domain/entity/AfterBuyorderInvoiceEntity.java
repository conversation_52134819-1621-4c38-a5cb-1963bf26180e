package com.vedeng.erp.aftersale.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 采购仅退票售后发票暂存
 */
@Data
public class AfterBuyorderInvoiceEntity extends BaseEntity {
    /**   AFTER_BUYORDER_INVOICE_ID **/
    private Integer afterBuyorderInvoiceId;

    /** 售后主表ID  AFTER_SALES_ID **/
    private Integer afterSalesId;

    /** 新录入发票号  INVOICE_NO **/
    private String invoiceNo;

    /** 新录入发票代码  INVOICE_CODE **/
    private String invoiceCode;

    /** 是否删除0否1是  IS_DELETE **/
    private Integer isDelete;
    /**
     * 新录入发票类型
     */
    private Integer invoiceType;
    /**
     * 新录入发票税率
     */
    private BigDecimal ratio;
}