package com.vedeng.erp.kingdee.batch.job;

import cn.hutool.core.date.DateUtil;
import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.listener.BaseProcessListener;
import com.vedeng.erp.kingdee.batch.common.listener.BaseReadListener;
import com.vedeng.erp.kingdee.batch.common.listener.BaseWriteListener;
import com.vedeng.erp.kingdee.batch.common.listener.JobListener;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.processor.*;
import com.vedeng.erp.kingdee.batch.writer.*;
import com.vedeng.erp.kingdee.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 其他出入库21-22历史数据推送流程job
 * @date 2023/05/25 16:00
 * 9外借入库 10外借出库 12盘盈入库 13报废出库 14领用出库  16 盘亏出库 18 样品出库 19 单位转换出库 20 单位转换入库
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class WarehouseOtherInOutHistoryBatchJob extends BaseJob {
    @Autowired
    private BatchUnitConversionInHistoryProcessor batchUnitConversionInHistoryProcessor;
    @Autowired
    private BatchUnitConversionOutHistoryProcessor batchUnitConversionOutHistoryProcessor;


    @Autowired
    private BatchAllocationInHistoryProcessor batchAllocationInHistoryProcessor;
    @Autowired
    private BatchAllocationInWriter batchAllocationInWriter;

    @Autowired
    private BatchAllocationOutHistoryProcessor batchAllocationOutHistoryProcessor;
    @Autowired
    private BatchAllocationOutWriter batchAllocationOutWriter;

    @Autowired
    private BatchScrapProcessor batchScrapProcessor;
    @Autowired
    private BatchScrapHistoryProcessor batchScrapHistoryProcessor;
    @Autowired
    private BatchScrapWriter batchScrapWriter;

    @Autowired
    private BatchReceiveHistoryProcessor batchReceiveHistoryProcessor;
    @Autowired
    private BatchReceiveWriter batchReceiveWriter;

    @Autowired
    private BatchProfitLossProcessor batchProfitLossProcessor;
    @Autowired
    private BatchProfitLossHistoryProcessor batchProfitLossHistoryProcessor;
    @Autowired
    private BatchProfitLossWriter batchProfitLossWriter;

    @Autowired
    private BatchInventoryProfitHistoryProcessor batchInventoryProfitHistoryProcessor;
    @Autowired
    private BatchInventoryProfitWriter batchInventoryProfitWriter;

    @Autowired
    private BatchUnitConversionOutProcessor batchUnitConversionOutProcessor;
    @Autowired
    private BatchUnitConversionOutWriter batchUnitConversionOutWriter;

    @Autowired
    private BatchUnitConversionInProcessor batchUnitConversionInProcessor;
    @Autowired
    private BatchUnitConversionInWriter batchUnitConversionInWriter;

    @Autowired
    private BatchSampleOutHistoryProcessor batchSampleOutHistoryProcessor;

    @Autowired
    private BatchSampleInHistoryProcessor batchSampleInHistoryProcessor;

    private int chunkSize = 1;

    public Job otherWarehouseInOutFlowJob() {
        return jobBuilderFactory.get("otherWarehouseInFlowHistoryJob")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(allocationIn())
                .next(inventoryProfit())
                .next(allocationOut())
                .next(scrap())
                .next(receive())
                .next(profitLoss())
                .next(unitConversionOut())
                .next(unitConversionIn())
                .next(sampleIn())
                .next(sampleOut())
                .build();
    }

    private Step sampleIn() {
        return stepBuilderFactory.get("sampleInHistory")
                .<BatchWarehouseGoodsOutInDto, KingDeeStorageInDto>chunk(chunkSize)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchSampleConversionInDtoItemReaderHistory(null, null))
                .processor(batchSampleInHistoryProcessor)
                .writer(batchUnitConversionInWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    private Step sampleOut() {
        return stepBuilderFactory.get("sampleOutHistory")
                .<BatchWarehouseGoodsOutInDto, KingDeeStorageOutDto>chunk(chunkSize)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchSampleConversionOutDtoItemReaderHistory(null, null))
                .processor(batchSampleOutHistoryProcessor)
                .writer(batchScrapWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 库存转换出
     */
    private Step unitConversionOut() {
        return stepBuilderFactory.get("unitConversionOutHistory")
                .<BatchWarehouseGoodsOutInDto, KingDeeStorageInDto>chunk(chunkSize)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchUnitConversionOutDtoItemReaderHistory(null, null))
                .processor(batchUnitConversionOutHistoryProcessor)
                .writer(batchUnitConversionOutWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }




    /**
     * 库存转换入
     */
    private Step unitConversionIn() {
        return stepBuilderFactory.get("unitConversionOutHistory")
                .<BatchWarehouseGoodsOutInDto, KingDeeStorageInDto>chunk(chunkSize)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchUnitConversionInDtoItemReaderHistory(null, null))
                .processor(batchUnitConversionInHistoryProcessor)
                .writer(batchUnitConversionInWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 外借入库
     */
    private Step allocationIn() {
        return stepBuilderFactory.get("allocationInHistory")
                .<BatchWarehouseGoodsOutInDto, KingDeeAllocationDto>chunk(chunkSize)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchAllocationInDtoItemReaderHistory(null, null))
                .processor(batchAllocationInHistoryProcessor)
                .writer(batchAllocationInWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 盘盈入库
     */
    private Step inventoryProfit() {
        return stepBuilderFactory.get("inventoryProfitHistory")
                .<BatchWarehouseGoodsOutInDto, KingDeeInventoryProfitDto>chunk(chunkSize)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchInventoryProfitDtoItemReaderHistory(null, null))
                .processor(batchInventoryProfitHistoryProcessor)
                .writer(batchInventoryProfitWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }


    /**
     * 外借出库
     */
    private Step allocationOut() {
        return stepBuilderFactory.get("allocationOutHistory")
                .<BatchWarehouseGoodsOutInDto, KingDeeAllocationDto>chunk(chunkSize)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchAllocationOutDtoItemReaderHistory(null, null))
                .processor(batchAllocationOutHistoryProcessor)
                .writer(batchAllocationOutWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 报废出库
     */
    private Step scrap() {
        return stepBuilderFactory.get("scrapHistory")
                .<BatchWarehouseGoodsOutInDto, KingDeeStorageOutDto>chunk(chunkSize)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchScrapDtoItemReaderHistory(null, null))
                .processor(batchScrapHistoryProcessor)
                .writer(batchScrapWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }


    /**
     * 领用出库
     */
    private Step receive() {
        return stepBuilderFactory.get("receiveHistory")
                .<BatchWarehouseGoodsOutInDto, KingDeeStorageOutDto>chunk(chunkSize)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchReceiveDtoItemReaderHistory(null, null))
                .processor(batchReceiveHistoryProcessor)
                .writer(batchReceiveWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 盘亏出库
     */
    private Step profitLoss() {
        return stepBuilderFactory.get("profitLossHistory")
                .<BatchWarehouseGoodsOutInDto, KingDeeProfitLossDto>chunk(chunkSize)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchProfitLossDtoItemReaderHistory(null, null))
                .processor(batchProfitLossHistoryProcessor)
                .writer(batchProfitLossWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> batchSampleConversionOutDtoItemReaderHistory(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        return getBatchWarehouseGoodsOutInDtoCommonMybatisItemReader(beginTime, endTime,18);
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> batchSampleConversionInDtoItemReaderHistory(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        return getBatchWarehouseGoodsOutInDtoCommonMybatisItemReader(beginTime, endTime,8);
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> batchUnitConversionOutDtoItemReaderHistory(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        return getBatchWarehouseGoodsOutInDtoCommonMybatisItemReader(beginTime, endTime,19);
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> batchUnitConversionInDtoItemReaderHistory(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        return getBatchWarehouseGoodsOutInDtoCommonMybatisItemReader(beginTime, endTime,20);
    }


    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> batchAllocationInDtoItemReaderHistory(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        return getBatchWarehouseGoodsOutInDtoCommonMybatisItemReader(beginTime, endTime,9);
    }


    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> batchInventoryProfitDtoItemReaderHistory(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        return getBatchWarehouseGoodsOutInDtoCommonMybatisItemReader(beginTime, endTime,12);
    }



    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> batchAllocationOutDtoItemReaderHistory(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        return getBatchWarehouseGoodsOutInDtoCommonMybatisItemReader(beginTime, endTime,10);
    }



    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> batchScrapDtoItemReaderHistory(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        return getBatchWarehouseGoodsOutInDtoCommonMybatisItemReader(beginTime, endTime,13);
    }



    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> batchReceiveDtoItemReaderHistory(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        return getBatchWarehouseGoodsOutInDtoCommonMybatisItemReader(beginTime, endTime,14);
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> batchProfitLossDtoItemReaderHistory(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        return getBatchWarehouseGoodsOutInDtoCommonMybatisItemReader(beginTime, endTime,16);
    }


    private CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> getBatchWarehouseGoodsOutInDtoCommonMybatisItemReader(
            String beginTime, String endTime, Integer outInType) {
        BatchWarehouseGoodsOutInDto warehouseGoodsOutInDto = BatchWarehouseGoodsOutInDto
                .builder()
                .outInType(outInType)
                .isDelete(0)
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(beginTime))
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(endTime))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchWarehouseGoodsOutInDto.class.getSimpleName(), warehouseGoodsOutInDto);
    }


}

