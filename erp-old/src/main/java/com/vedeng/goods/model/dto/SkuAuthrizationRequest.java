package com.vedeng.goods.model.dto;

import com.vedeng.goods.model.vo.SkuAuthorizationItemVo;

import java.io.Serializable;
import java.util.List;

/**
 * SKU报备信息请求
 *
 * <AUTHOR>
 * @date 2020/9/25 9:06:25
 */
public class SkuAuthrizationRequest implements Serializable {

    /**
     * SKU
     */
    private List<Integer> skuIds;

    /**
     * 报备信息
     */
    private List<SkuAuthorizationItemVo> skuAuthrizationItems;

    public List<Integer> getSkuIds() {
        return skuIds;
    }

    public void setSkuIds(List<Integer> skuIds) {
        this.skuIds = skuIds;
    }

    public List<SkuAuthorizationItemVo> getSkuAuthrizationItems() {
        return skuAuthrizationItems;
    }

    public void setSkuAuthrizationItems(List<SkuAuthorizationItemVo> skuAuthrizationItems) {
        this.skuAuthrizationItems = skuAuthrizationItems;
    }
}
