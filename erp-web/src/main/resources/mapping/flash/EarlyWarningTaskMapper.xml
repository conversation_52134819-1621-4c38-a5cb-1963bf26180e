<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.flash.dao.EarlyWarningTaskMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.flash.model.EarlyWarningTask" >
    <id column="EARLY_WARNING_TASK_ID" property="earlyWarningTaskId" jdbcType="BIGINT" />
    <result column="EARLY_WARNING_TYPE" property="earlyWarningType" jdbcType="INTEGER" />
    <result column="RELATE_BUSINESS_ID" property="relateBusinessId" jdbcType="BIGINT" />
    <result column="BUSINESS_EXTRA1" property="businessExtra1" jdbcType="VARCHAR" />
    <result column="BUSINESS_EXTRA2" property="businessExtra2" jdbcType="BIGINT" />
    <result column="URGING_TICKET_NUM" property="urgingTicketNum" jdbcType="DECIMAL" />
    <result column="URGING_TICKET_AMOUNT" property="urgingTicketAmount" jdbcType="DECIMAL" />
    <result column="ALREADY_INPUT_NUM" property="alreadyInputNum" jdbcType="DECIMAL" />
    <result column="ALREADY_INPUT_AMOUT" property="alreadyInputAmout" jdbcType="DECIMAL" />
    <result column="WANING_LEVEL" property="waningLevel" jdbcType="BIT" />
    <result column="TASK_STATUS" property="taskStatus" jdbcType="INTEGER" />
    <result column="TASK_DEALER" property="taskDealer" jdbcType="VARCHAR" />
    <result column="FOLLOW_UP_PERSON" property="followUpPerson" jdbcType="VARCHAR" />
    <result column="FOLLOW_UP_NUM" property="followUpNum" jdbcType="INTEGER" />
    <result column="FOLLOW_UP_TIME" property="followUpTime" jdbcType="VARCHAR" />
    <result column="SEND_INIT_MESSAGE" property="sendInitMessage" jdbcType="BIT" />
    <result column="SEND_IMPENDING_MESSAGE" property="sendImpendingMessage" jdbcType="BIT" />
    <result column="SEND_OVERDUE_MESSAGE" property="sendOverdueMessage" jdbcType="BIT" />
    <result column="SEND_OVER12HOURS_MESSAGE" property="sendOver12hoursMessage" jdbcType="BIT" />
    <result column="SEND_OVER48HOURS_MESSAGE" property="sendOver48hoursMessage" jdbcType="BIT" />
    <result column="IS_DELETED" property="isDeleted" jdbcType="INTEGER" />
    <result column="ADD_TIME" property="addTime" jdbcType="VARCHAR" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="VARCHAR" />
    <result column="UPDATOR" property="updator" jdbcType="INTEGER" />
    <result column="FOLLOW_UP_COMMENT" property="followUpComment" jdbcType="LONGVARCHAR" />
    <result column="DELIVERY_TIME" property="deliveryTime" jdbcType="BIGINT" />
    <result column="GOODS_DAY" property="goodsDay" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    EARLY_WARNING_TASK_ID, EARLY_WARNING_TYPE, RELATE_BUSINESS_ID, BUSINESS_EXTRA1, BUSINESS_EXTRA2,
    URGING_TICKET_NUM, URGING_TICKET_AMOUNT, ALREADY_INPUT_NUM, ALREADY_INPUT_AMOUT,
    WANING_LEVEL, TASK_STATUS, TASK_DEALER, FOLLOW_UP_PERSON, FOLLOW_UP_NUM, FOLLOW_UP_TIME,
    SEND_INIT_MESSAGE, SEND_IMPENDING_MESSAGE, SEND_OVERDUE_MESSAGE, SEND_OVER12HOURS_MESSAGE,
    SEND_OVER48HOURS_MESSAGE, IS_DELETED, ADD_TIME, CREATOR, UPDATE_TIME, UPDATOR,FOLLOW_UP_COMMENT,DELIVERY_TIME,GOODS_DAY
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from T_EARLY_WARNING_TASK
    where EARLY_WARNING_TASK_ID = #{earlyWarningTaskId,jdbcType=BIGINT}
  </select>
  <select id="getEarlyWarningTicksTaskTodoDtoByuserIds" resultType="com.vedeng.todolist.dto.EarlyWarningTicksTaskTodoDto">
    SELECT
        ROUND(SUM( URGING_TICKET_NUM - IFNULL(ALREADY_INPUT_NUM,0)),2) AS TASK_NUM,
        SUM( URGING_TICKET_AMOUNT-IFNULL(ALREADY_INPUT_AMOUT,0) ) AS TOTAL_TICKS_AMOUNT,
        COUNT( DISTINCT BUSINESS_EXTRA2 ) AS TRADER_NUM
    FROM
        T_EARLY_WARNING_TASK
    WHERE
        EARLY_WARNING_TYPE = 2
        AND IS_DELETED = 0
      <if test="userIds != null">
        AND TASK_DEALER IN
        <foreach collection="userIds" item="userId" open="(" close=")" index="index" separator=",">
          #{userId,jdbcType=INTEGER}
        </foreach>
      </if>
  </select>
  <select id="getEarlyWarningTicksDtoByUserIdsListPage" resultType="com.vedeng.flash.dto.EarlyWarningTicksDto" parameterType="Map">

    select *
    from (


    # 采购单  催票列表sql
    SELECT A.EARLY_WARNING_TASK_ID,
    B.TRADER_NAME,
    A.BUSINESS_EXTRA1                        AS SALEORDER_NO,
    D.SKU_NAME,
    D.SKU_NO,
    E.UNIT_NAME                              AS SKU_UNIT,
    C.PRICE,
    C.NUM,
    A.ADD_TIME,
    F.USERNAME                               AS TASK_DEALER_NAME,
    IFNULL(A.URGING_TICKET_NUM, 0)           AS URGING_TICKET_NUM,
    IFNULL(A.URGING_TICKET_AMOUNT, 0)        AS URGING_TICKET_AMOUNT,
    IFNULL(ROUND(A.ALREADY_INPUT_NUM, 2), 0) AS ALREADY_INPUT_NUM,
    IFNULL(A.ALREADY_INPUT_AMOUT, 0)         AS ALREADY_INPUT_AMOUNT,
    CASE
    WHEN A.FOLLOW_UP_NUM > 0 THEN 2
    ELSE 1
    END                                  AS FOLLOW_UP_STATUS
    FROM T_EARLY_WARNING_TASK AS A
    LEFT JOIN T_TRADER AS B ON A.BUSINESS_EXTRA2 = B.TRADER_ID
    LEFT JOIN T_BUYORDER_GOODS AS C ON A.RELATE_BUSINESS_ID = C.BUYORDER_GOODS_ID
    LEFT JOIN V_CORE_SKU AS D ON C.SKU = D.SKU_NO
    LEFT JOIN T_UNIT AS E ON E.UNIT_ID = D.BASE_UNIT_ID
    LEFT JOIN T_USER AS F ON F.USER_ID = A.TASK_DEALER
    WHERE A.EARLY_WARNING_TYPE = 2
    AND IS_DELETED = 0
    AND A.BUSINESS_EXTRA1 not IN (
    select A.BUSINESS_EXTRA1
    from T_EARLY_WARNING_TASK A
    INNER JOIN T_BUYORDER_EXPENSE B ON A.BUSINESS_EXTRA1 = B.BUYORDER_EXPENSE_NO
    WHERE A.EARLY_WARNING_TYPE = 2
    AND A.IS_DELETED = 0
    )
    <if test="userIds != null">
      AND TASK_DEALER IN
      <foreach collection="userIds" item="userId" open="(" close=")" index="index" separator=",">
        #{userId,jdbcType=INTEGER}
      </foreach>
    </if>
    <if test="earlyWarningTicksSearchDto.sku != null and earlyWarningTicksSearchDto.sku != ''" >
      AND (D.SKU_NAME LIKE CONCAT('%',#{earlyWarningTicksSearchDto.sku,jdbcType=VARCHAR},'%' ) OR D.SKU_NO LIKE CONCAT('%',#{earlyWarningTicksSearchDto.sku,jdbcType=VARCHAR},'%' ))
    </if>

    <if test="earlyWarningTicksSearchDto.traderName != null and earlyWarningTicksSearchDto.traderName != ''" >
      AND  B.TRADER_NAME LIKE CONCAT('%',#{earlyWarningTicksSearchDto.traderName,jdbcType=VARCHAR},'%' )
    </if>
    <if test="earlyWarningTicksSearchDto.saleorderNo != null and earlyWarningTicksSearchDto.saleorderNo != ''" >
      AND A.BUSINESS_EXTRA1 LIKE CONCAT('%',#{earlyWarningTicksSearchDto.saleorderNo,jdbcType=VARCHAR},'%' )
    </if>
    <if test="earlyWarningTicksSearchDto.delaer != null and earlyWarningTicksSearchDto.delaer != ''" >
      AND A.TASK_DEALER = #{earlyWarningTicksSearchDto.delaer,jdbcType=INTEGER}
    </if>
    <if test="earlyWarningTicksSearchDto.followUpStatus == 1">
      AND A.FOLLOW_UP_NUM > 0
    </if>
    <if test="earlyWarningTicksSearchDto.followUpStatus == 2">
      AND (A.FOLLOW_UP_NUM = 0 OR ISNULL(A.FOLLOW_UP_NUM)=1 )
    </if>


    union

    # 采购费用单  催票列表查询sql
    SELECT A.EARLY_WARNING_TASK_ID,
    B.TRADER_NAME,
    A.BUSINESS_EXTRA1                        AS SALEORDER_NO,
    D.SKU_NAME,
    D.SKU_NO,
    E.UNIT_NAME                              AS SKU_UNIT,
    G.PRICE,
    C.NUM,
    A.ADD_TIME,
    F.USERNAME                               AS TASK_DEALER_NAME,
    IFNULL(A.URGING_TICKET_NUM, 0)           AS URGING_TICKET_NUM,
    IFNULL(A.URGING_TICKET_AMOUNT, 0)        AS URGING_TICKET_AMOUNT,
    IFNULL(ROUND(A.ALREADY_INPUT_NUM, 2), 0) AS ALREADY_INPUT_NUM,
    IFNULL(A.ALREADY_INPUT_AMOUT, 0)         AS ALREADY_INPUT_AMOUNT,
    CASE
    WHEN A.FOLLOW_UP_NUM > 0 THEN 2
    ELSE 1
    END                                  AS FOLLOW_UP_STATUS
    FROM T_EARLY_WARNING_TASK AS A
    LEFT JOIN T_TRADER AS B ON A.BUSINESS_EXTRA2 = B.TRADER_ID
    LEFT JOIN T_BUYORDER_EXPENSE_ITEM AS C ON A.RELATE_BUSINESS_ID = C.BUYORDER_EXPENSE_ITEM_ID
    LEFT JOIN T_BUYORDER_EXPENSE_ITEM_DETAIL AS G
    ON G.BUYORDER_EXPENSE_ITEM_ID = C.BUYORDER_EXPENSE_ITEM_ID
    LEFT JOIN V_CORE_SKU AS D ON C.GOODS_ID = D.SKU_ID
    LEFT JOIN T_UNIT AS E ON E.UNIT_ID = D.BASE_UNIT_ID
    LEFT JOIN T_USER AS F ON F.USER_ID = A.TASK_DEALER
    WHERE A.EARLY_WARNING_TYPE = 2
    AND IS_DELETED = 0
    AND A.BUSINESS_EXTRA1 IN (
    select A.BUSINESS_EXTRA1
    from T_EARLY_WARNING_TASK A
    INNER JOIN T_BUYORDER_EXPENSE B ON A.BUSINESS_EXTRA1 = B.BUYORDER_EXPENSE_NO
    WHERE A.EARLY_WARNING_TYPE = 2
    AND A.IS_DELETED = 0
    )
    <if test="userIds != null">
      AND TASK_DEALER IN
      <foreach collection="userIds" item="userId" open="(" close=")" index="index" separator=",">
        #{userId,jdbcType=INTEGER}
      </foreach>
    </if>
    <if test="earlyWarningTicksSearchDto.sku != null and earlyWarningTicksSearchDto.sku != ''" >
      AND (D.SKU_NAME LIKE CONCAT('%',#{earlyWarningTicksSearchDto.sku,jdbcType=VARCHAR},'%' ) OR D.SKU_NO LIKE CONCAT('%',#{earlyWarningTicksSearchDto.sku,jdbcType=VARCHAR},'%' ))
    </if>

    <if test="earlyWarningTicksSearchDto.traderName != null and earlyWarningTicksSearchDto.traderName != ''" >
      AND  B.TRADER_NAME LIKE CONCAT('%',#{earlyWarningTicksSearchDto.traderName,jdbcType=VARCHAR},'%' )
    </if>
    <if test="earlyWarningTicksSearchDto.saleorderNo != null and earlyWarningTicksSearchDto.saleorderNo != ''" >
      AND A.BUSINESS_EXTRA1 LIKE CONCAT('%',#{earlyWarningTicksSearchDto.saleorderNo,jdbcType=VARCHAR},'%' )
    </if>
    <if test="earlyWarningTicksSearchDto.delaer != null and earlyWarningTicksSearchDto.delaer != ''" >
      AND A.TASK_DEALER = #{earlyWarningTicksSearchDto.delaer,jdbcType=INTEGER}
    </if>
    <if test="earlyWarningTicksSearchDto.followUpStatus == 1">
      AND A.FOLLOW_UP_NUM > 0
    </if>
    <if test="earlyWarningTicksSearchDto.followUpStatus == 2">
      AND (A.FOLLOW_UP_NUM = 0 OR ISNULL(A.FOLLOW_UP_NUM)=1 )
    </if>
    ) as FF
    ORDER BY FF.ADD_TIME DESC
  </select>
  <select id="getTicksFollowUpRecoredByUserIdListPage" resultType="com.vedeng.flash.dto.EarlyWarningTicksDto" parameterType="Map">


    select * from (

    SELECT
    A.EARLY_WARNING_TASK_ID,
    B.TRADER_NAME,
    A.BUSINESS_EXTRA1 AS SALEORDER_NO,
    D.SKU_NAME,
    D.SKU_NO,
    E.UNIT_NAME AS SKU_UNIT,
    C.PRICE,
    IFNULL(ROUND(A.URGING_TICKET_NUM_FOLLOW_UP,2),0) AS URGING_TICKET_NUM_FOLLOW_UP,
    IFNULL(A.URGING_TICKET_AMOUNT_FOLLOW_UP,0) AS URGING_TICKET_AMOUNT_FOLLOW_UP,
    A.ADD_TIME,
    A.FOLLOW_UP_TIME,
    CASE
    WHEN A.FOLLOW_UP_RESULT = 0 THEN '不可开'
    WHEN A.FOLLOW_UP_RESULT = 1 THEN '可开'
    ELSE '--'
    END AS FOLLOW_UP_RESULT,
    CASE
    WHEN A.FOLLOW_UP_RESULT = 1 THEN  IFNULL(A.CAN_TICKET_AMOUNT,0)
    ELSE 0
    END AS CAN_TICKET_AMOUNT,
    CASE
    WHEN A.FOLLOW_UP_RESULT = 1 THEN  A.CAN_TICKET_TIME
    ELSE '--'
    END AS CAN_TICKET_TIME,
    CASE
    WHEN A.FOLLOW_UP_RESULT = 0 THEN A.FOLLOW_UP_COMMENT
    ELSE '--'
    END AS FOLLOW_UP_COMMENT,
    F.USERNAME AS TASK_DEALER_NAME
    FROM
    T_EARLY_WARNING_TASK AS A
    LEFT JOIN T_TRADER AS B ON A.BUSINESS_EXTRA2=B.TRADER_ID
    LEFT JOIN T_BUYORDER_GOODS AS C ON A.RELATE_BUSINESS_ID = C.BUYORDER_GOODS_ID
    LEFT JOIN V_CORE_SKU AS D ON C.SKU=D.SKU_NO
    LEFT JOIN T_UNIT AS E ON E.UNIT_ID=D.BASE_UNIT_ID
    LEFT JOIN T_USER AS F ON F.USER_ID = A.TASK_DEALER
    WHERE
    A.EARLY_WARNING_TYPE=2
    AND A.IS_DELETED = 0
    AND A.FOLLOW_UP_NUM > 0
    AND A.BUSINESS_EXTRA1 NOT IN (
    select A.BUSINESS_EXTRA1
    from T_EARLY_WARNING_TASK A
    INNER JOIN T_BUYORDER_EXPENSE B ON A.BUSINESS_EXTRA1 = B.BUYORDER_EXPENSE_NO
    WHERE A.EARLY_WARNING_TYPE = 2
    AND A.IS_DELETED = 0
    )
    <if test="userIds != null">
      AND TASK_DEALER IN
      <foreach collection="userIds" item="userId" open="(" close=")" index="index" separator=",">
        #{userId,jdbcType=INTEGER}
      </foreach>
    </if>
    <if test="earlyWarningTicksSearchDto.sku != null and earlyWarningTicksSearchDto.sku != ''" >
      AND (D.SKU_NAME LIKE CONCAT('%',#{earlyWarningTicksSearchDto.sku,jdbcType=VARCHAR},'%' ) OR D.SKU_NO LIKE CONCAT('%',#{earlyWarningTicksSearchDto.sku,jdbcType=VARCHAR},'%' ))
    </if>
    <if test="earlyWarningTicksSearchDto.traderName != null and earlyWarningTicksSearchDto.traderName != ''" >
      AND  B.TRADER_NAME LIKE CONCAT('%',#{earlyWarningTicksSearchDto.traderName,jdbcType=VARCHAR},'%' )
    </if>
    <if test="earlyWarningTicksSearchDto.saleorderNo != null and earlyWarningTicksSearchDto.saleorderNo != ''" >
      AND A.BUSINESS_EXTRA1 LIKE CONCAT('%',#{earlyWarningTicksSearchDto.saleorderNo,jdbcType=VARCHAR},'%' )
    </if>
    <if test="earlyWarningTicksSearchDto.delaer != null and earlyWarningTicksSearchDto.delaer != ''" >
      AND A.TASK_DEALER = #{earlyWarningTicksSearchDto.delaer,jdbcType=INTEGER}
    </if>
    <if test="earlyWarningTicksSearchDto.startTimeStr != null and earlyWarningTicksSearchDto.startTimeStr != ''">
      AND A.CAN_TICKET_TIME &gt;= #{earlyWarningTicksSearchDto.startTimeStr,jdbcType=VARCHAR}
      AND A.FOLLOW_UP_RESULT = 1
    </if>
    <if test="earlyWarningTicksSearchDto.endTimeStr != null and earlyWarningTicksSearchDto.endTimeStr != ''">
      AND A.CAN_TICKET_TIME &lt;= #{earlyWarningTicksSearchDto.endTimeStr,jdbcType=VARCHAR}
      AND A.FOLLOW_UP_RESULT = 1
    </if>
    UNION
    SELECT
    A.EARLY_WARNING_TASK_ID,
    B.TRADER_NAME,
    A.BUSINESS_EXTRA1 AS SALEORDER_NO,
    D.SKU_NAME,
    D.SKU_NO,
    E.UNIT_NAME AS SKU_UNIT,
    G.PRICE,
    IFNULL(ROUND(A.URGING_TICKET_NUM_FOLLOW_UP,2),0) AS URGING_TICKET_NUM_FOLLOW_UP,
    IFNULL(A.URGING_TICKET_AMOUNT_FOLLOW_UP,0) AS URGING_TICKET_AMOUNT_FOLLOW_UP,
    A.ADD_TIME,
    A.FOLLOW_UP_TIME,
    CASE
    WHEN A.FOLLOW_UP_RESULT = 0 THEN '不可开'
    WHEN A.FOLLOW_UP_RESULT = 1 THEN '可开'
    ELSE '--'
    END AS FOLLOW_UP_RESULT,
    CASE
    WHEN A.FOLLOW_UP_RESULT = 1 THEN  IFNULL(A.CAN_TICKET_AMOUNT,0)
    ELSE 0
    END AS CAN_TICKET_AMOUNT,
    CASE
    WHEN A.FOLLOW_UP_RESULT = 1 THEN  A.CAN_TICKET_TIME
    ELSE '--'
    END AS CAN_TICKET_TIME,
    CASE
    WHEN A.FOLLOW_UP_RESULT = 0 THEN A.FOLLOW_UP_COMMENT
    ELSE '--'
    END AS FOLLOW_UP_COMMENT,
    F.USERNAME AS TASK_DEALER_NAME
    FROM
    T_EARLY_WARNING_TASK AS A
    LEFT JOIN T_TRADER AS B ON A.BUSINESS_EXTRA2=B.TRADER_ID
    LEFT JOIN T_BUYORDER_EXPENSE_ITEM AS C ON A.RELATE_BUSINESS_ID = C.BUYORDER_EXPENSE_ITEM_ID
    LEFT JOIN T_BUYORDER_EXPENSE_ITEM_DETAIL AS G ON G.BUYORDER_EXPENSE_ITEM_ID = C.BUYORDER_EXPENSE_ITEM_ID
    LEFT JOIN V_CORE_SKU AS D ON C.GOODS_ID=D.SKU_ID
    LEFT JOIN T_UNIT AS E ON E.UNIT_ID=D.BASE_UNIT_ID
    LEFT JOIN T_USER AS F ON F.USER_ID = A.TASK_DEALER
    WHERE
    A.EARLY_WARNING_TYPE=2
    AND A.IS_DELETED = 0
    AND A.FOLLOW_UP_NUM > 0
    AND A.BUSINESS_EXTRA1 IN (
    select A.BUSINESS_EXTRA1
    from T_EARLY_WARNING_TASK A
    INNER JOIN T_BUYORDER_EXPENSE B ON A.BUSINESS_EXTRA1 = B.BUYORDER_EXPENSE_NO
    WHERE A.EARLY_WARNING_TYPE = 2
    AND A.IS_DELETED = 0
    )
    <if test="userIds != null">
      AND TASK_DEALER IN
      <foreach collection="userIds" item="userId" open="(" close=")" index="index" separator=",">
        #{userId,jdbcType=INTEGER}
      </foreach>
    </if>
    <if test="earlyWarningTicksSearchDto.sku != null and earlyWarningTicksSearchDto.sku != ''" >
      AND (D.SKU_NAME LIKE CONCAT('%',#{earlyWarningTicksSearchDto.sku,jdbcType=VARCHAR},'%' ) OR D.SKU_NO LIKE CONCAT('%',#{earlyWarningTicksSearchDto.sku,jdbcType=VARCHAR},'%' ))
    </if>
    <if test="earlyWarningTicksSearchDto.traderName != null and earlyWarningTicksSearchDto.traderName != ''" >
      AND  B.TRADER_NAME LIKE CONCAT('%',#{earlyWarningTicksSearchDto.traderName,jdbcType=VARCHAR},'%' )
    </if>
    <if test="earlyWarningTicksSearchDto.saleorderNo != null and earlyWarningTicksSearchDto.saleorderNo != ''" >
      AND A.BUSINESS_EXTRA1 LIKE CONCAT('%',#{earlyWarningTicksSearchDto.saleorderNo,jdbcType=VARCHAR},'%' )
    </if>
    <if test="earlyWarningTicksSearchDto.delaer != null and earlyWarningTicksSearchDto.delaer != ''" >
      AND A.TASK_DEALER = #{earlyWarningTicksSearchDto.delaer,jdbcType=INTEGER}
    </if>
    <if test="earlyWarningTicksSearchDto.startTimeStr != null and earlyWarningTicksSearchDto.startTimeStr != ''">
      AND A.CAN_TICKET_TIME &gt;= #{earlyWarningTicksSearchDto.startTimeStr,jdbcType=VARCHAR}
      AND A.FOLLOW_UP_RESULT = 1
    </if>
    <if test="earlyWarningTicksSearchDto.endTimeStr != null and earlyWarningTicksSearchDto.endTimeStr != ''">
      AND A.CAN_TICKET_TIME &lt;= #{earlyWarningTicksSearchDto.endTimeStr,jdbcType=VARCHAR}
      AND A.FOLLOW_UP_RESULT = 1
    </if>
    ) AS FF
    ORDER BY FF.ADD_TIME DESC
  </select>
  <select id="getEarlyWarningTicksDtoByIds" resultType="com.vedeng.flash.dto.EarlyWarningTicksDto">

    select * from (
    SELECT A.EARLY_WARNING_TASK_ID,
    B.TRADER_NAME,
    A.BUSINESS_EXTRA1                AS SALEORDER_NO,
    D.SKU_NAME,
    E.UNIT_NAME                      AS SKU_UNIT,
    C.PRICE,
    C.NUM,
    A.ADD_TIME,
    A.URGING_TICKET_NUM,
    A.URGING_TICKET_AMOUNT,
    IFNULL(A.ALREADY_INPUT_NUM, 0)   AS ALREADY_INPUT_NUM,
    IFNULL(A.ALREADY_INPUT_AMOUT, 0) AS ALREADY_INPUT_AMOUNT
    FROM T_EARLY_WARNING_TASK AS A
    LEFT JOIN T_TRADER AS B ON A.BUSINESS_EXTRA2 = B.TRADER_ID
    LEFT JOIN T_BUYORDER_GOODS AS C ON A.RELATE_BUSINESS_ID = C.BUYORDER_GOODS_ID
    LEFT JOIN V_CORE_SKU AS D ON C.SKU = D.SKU_NO
    LEFT JOIN T_UNIT AS E ON E.UNIT_ID = D.BASE_UNIT_ID
    WHERE A.EARLY_WARNING_TYPE = 2
    AND IS_DELETED = 0
    <if test="earlyWarningTaskIdsList != null">
      AND EARLY_WARNING_TASK_ID IN
      <foreach collection="earlyWarningTaskIdsList" item="earlyWarningTaskId" open="(" close=")" index="index" separator=",">
        #{earlyWarningTaskId,jdbcType=INTEGER}
      </foreach>
    </if>
    AND A.BUSINESS_EXTRA1 NOT IN (
    select A.BUSINESS_EXTRA1
    from T_EARLY_WARNING_TASK A
    INNER JOIN T_BUYORDER_EXPENSE B ON A.BUSINESS_EXTRA1 = B.BUYORDER_EXPENSE_NO
    WHERE A.EARLY_WARNING_TYPE = 2
    AND A.IS_DELETED = 0
    )
    union
    SELECT A.EARLY_WARNING_TASK_ID,
    B.TRADER_NAME,
    A.BUSINESS_EXTRA1                AS SALEORDER_NO,
    D.SKU_NAME,
    E.UNIT_NAME                      AS SKU_UNIT,
    G.PRICE,
    C.NUM,
    A.ADD_TIME,
    A.URGING_TICKET_NUM,
    A.URGING_TICKET_AMOUNT,
    IFNULL(A.ALREADY_INPUT_NUM, 0)   AS ALREADY_INPUT_NUM,
    IFNULL(A.ALREADY_INPUT_AMOUT, 0) AS ALREADY_INPUT_AMOUNT
    FROM T_EARLY_WARNING_TASK AS A
    LEFT JOIN T_TRADER AS B ON A.BUSINESS_EXTRA2 = B.TRADER_ID
    LEFT JOIN T_BUYORDER_EXPENSE_ITEM AS C ON A.RELATE_BUSINESS_ID = C.BUYORDER_EXPENSE_ITEM_ID
    LEFT JOIN T_BUYORDER_EXPENSE_ITEM_DETAIL AS G ON G.BUYORDER_EXPENSE_ITEM_ID = C.BUYORDER_EXPENSE_ITEM_ID
    LEFT JOIN V_CORE_SKU AS D ON C.GOODS_ID = D.SKU_ID
    LEFT JOIN T_UNIT AS E ON E.UNIT_ID = D.BASE_UNIT_ID
    WHERE A.EARLY_WARNING_TYPE = 2
    AND IS_DELETED = 0
    <if test="earlyWarningTaskIdsList != null">
      AND EARLY_WARNING_TASK_ID IN
      <foreach collection="earlyWarningTaskIdsList" item="earlyWarningTaskId" open="(" close=")" index="index" separator=",">
        #{earlyWarningTaskId,jdbcType=INTEGER}
      </foreach>
    </if>
    AND A.BUSINESS_EXTRA1  IN (
    select A.BUSINESS_EXTRA1
    from T_EARLY_WARNING_TASK A
    INNER JOIN T_BUYORDER_EXPENSE B ON A.BUSINESS_EXTRA1 = B.BUYORDER_EXPENSE_NO
    WHERE A.EARLY_WARNING_TYPE = 2
    AND A.IS_DELETED = 0
    )
    ) AS FF

    ORDER BY FF.ADD_TIME
    DESC
  </select>
  <select id="getFollowNumByPrimaryKey" resultType="java.lang.Integer">
    SELECT
	IFNULL(FOLLOW_UP_NUM,0)
FROM
	T_EARLY_WARNING_TASK
WHERE
	EARLY_WARNING_TASK_ID = #{earlyWarningTaskId,jdbcType=BIGINT};
  </select>
  <select id="getAllDealerByUserIds" resultType="com.vedeng.flash.dto.TaskDealerDto">
    SELECT
      USER_ID,
      USERNAME AS USER_NAME
  FROM
      T_USER
  WHERE
      USER_ID IN
    <foreach collection="userIds" item="userId" open="(" close=")" index="index" separator=",">
      #{userId,jdbcType=INTEGER}
    </foreach>
  </select>
  <select id="getEarlyWarningTicketTaskByBuyorderGoodId" resultType="com.vedeng.flash.model.EarlyWarningTask">
    SELECT
        EARLY_WARNING_TASK_ID,
        IFNULL(URGING_TICKET_NUM,0) AS URGING_TICKET_NUM,
        IFNULL(URGING_TICKET_AMOUNT,0) AS URGING_TICKET_AMOUNT,
        IFNULL(ALREADY_INPUT_NUM,0) AS ALREADY_INPUT_NUM,
        BUSINESS_EXTRA1
    FROM
        T_EARLY_WARNING_TASK
    WHERE
        RELATE_BUSINESS_ID = #{buyorderGoodsId,jdbcType=INTEGER}
        AND IS_DELETED = 0
        AND EARLY_WARNING_TYPE = 2
    ORDER BY ADD_TIME
  </select>
  <select id="getAllNotDeleteAndNotSendOver48HoursMessageTask" resultType="com.vedeng.flash.model.EarlyWarningTask">
    SELECT
       *
    FROM
        T_EARLY_WARNING_TASK
    WHERE
        IS_DELETED = 0
        AND EARLY_WARNING_TYPE = 2
        AND SEND_OVER48HOURS_MESSAGE = 0
        AND (ALREADY_INPUT_NUM IS NULL OR ALREADY_INPUT_NUM=0)
        AND (FOLLOW_UP_NUM IS NULL OR FOLLOW_UP_NUM=0)
  </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from T_EARLY_WARNING_TASK
    where EARLY_WARNING_TASK_ID = #{earlyWarningTaskId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.vedeng.flash.model.EarlyWarningTask" useGeneratedKeys="true" keyProperty="earlyWarningTaskId" >
    insert into T_EARLY_WARNING_TASK (EARLY_WARNING_TASK_ID, EARLY_WARNING_TYPE,
      RELATE_BUSINESS_ID, BUSINESS_EXTRA1, BUSINESS_EXTRA2,
      URGING_TICKET_NUM, URGING_TICKET_AMOUNT, ALREADY_INPUT_NUM,
      ALREADY_INPUT_AMOUT, WANING_LEVEL, TASK_STATUS,
      TASK_DEALER, FOLLOW_UP_PERSON, FOLLOW_UP_NUM,
      FOLLOW_UP_TIME, SEND_INIT_MESSAGE, SEND_IMPENDING_MESSAGE,
      SEND_OVERDUE_MESSAGE, SEND_OVER12HOURS_MESSAGE, SEND_OVER48HOURS_MESSAGE,
      IS_DELETED, ADD_TIME, CREATOR,
      UPDATE_TIME, UPDATOR,DELIVERY_TIME,GOODS_DAY)
    values (#{earlyWarningTaskId,jdbcType=BIGINT}, #{earlyWarningType,jdbcType=INTEGER},
      #{relateBusinessId,jdbcType=BIGINT}, #{businessExtra1,jdbcType=VARCHAR}, #{businessExtra2,jdbcType=BIGINT},
      #{urgingTicketNum,jdbcType=DECIMAL}, #{urgingTicketAmount,jdbcType=DECIMAL}, #{alreadyInputNum,jdbcType=DECIMAL},
      #{alreadyInputAmout,jdbcType=DECIMAL}, #{waningLevel,jdbcType=BIT}, #{taskStatus,jdbcType=INTEGER},
      #{taskDealer,jdbcType=VARCHAR}, #{followUpPerson,jdbcType=VARCHAR}, #{followUpNum,jdbcType=INTEGER},
      #{followUpTime,jdbcType=VARCHAR}, #{sendInitMessage,jdbcType=BIT}, #{sendImpendingMessage,jdbcType=BIT},
      #{sendOverdueMessage,jdbcType=BIT}, #{sendOver12hoursMessage,jdbcType=BIT}, #{sendOver48hoursMessage,jdbcType=BIT},
      #{isDeleted,jdbcType=INTEGER}, #{addTime,jdbcType=VARCHAR}, #{creator,jdbcType=INTEGER},
      #{updateTime,jdbcType=VARCHAR}, #{updator,jdbcType=INTEGER},#{deliveryTime,jdbcType=BIGINT},#{goodsDay,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.flash.model.EarlyWarningTask" >
    insert into T_EARLY_WARNING_TASK
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="earlyWarningTaskId != null" >
        EARLY_WARNING_TASK_ID,
      </if>
      <if test="earlyWarningType != null" >
        EARLY_WARNING_TYPE,
      </if>
      <if test="relateBusinessId != null" >
        RELATE_BUSINESS_ID,
      </if>
      <if test="businessExtra1 != null" >
        BUSINESS_EXTRA1,
      </if>
      <if test="businessExtra2 != null" >
        BUSINESS_EXTRA2,
      </if>
      <if test="urgingTicketNum != null" >
        URGING_TICKET_NUM,
      </if>
      <if test="urgingTicketAmount != null" >
        URGING_TICKET_AMOUNT,
      </if>
      <if test="alreadyInputNum != null" >
        ALREADY_INPUT_NUM,
      </if>
      <if test="alreadyInputAmout != null" >
        ALREADY_INPUT_AMOUT,
      </if>
      <if test="waningLevel != null" >
        WANING_LEVEL,
      </if>
      <if test="taskStatus != null" >
        TASK_STATUS,
      </if>
      <if test="taskDealer != null" >
        TASK_DEALER,
      </if>
      <if test="followUpPerson != null" >
        FOLLOW_UP_PERSON,
      </if>
      <if test="followUpNum != null" >
        FOLLOW_UP_NUM,
      </if>
      <if test="followUpTime != null" >
        FOLLOW_UP_TIME,
      </if>
      <if test="sendInitMessage != null" >
        SEND_INIT_MESSAGE,
      </if>
      <if test="sendImpendingMessage != null" >
        SEND_IMPENDING_MESSAGE,
      </if>
      <if test="sendOverdueMessage != null" >
        SEND_OVERDUE_MESSAGE,
      </if>
      <if test="sendOver12hoursMessage != null" >
        SEND_OVER12HOURS_MESSAGE,
      </if>
      <if test="sendOver48hoursMessage != null" >
        SEND_OVER48HOURS_MESSAGE,
      </if>
      <if test="isDeleted != null" >
        IS_DELETED,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>
      <if test="updator != null" >
        UPDATOR,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="earlyWarningTaskId != null" >
        #{earlyWarningTaskId,jdbcType=BIGINT},
      </if>
      <if test="earlyWarningType != null" >
        #{earlyWarningType,jdbcType=INTEGER},
      </if>
      <if test="relateBusinessId != null" >
        #{relateBusinessId,jdbcType=BIGINT},
      </if>
      <if test="businessExtra1 != null" >
        #{businessExtra1,jdbcType=VARCHAR},
      </if>
      <if test="businessExtra2 != null" >
        #{businessExtra2,jdbcType=BIGINT},
      </if>
      <if test="urgingTicketNum != null" >
        #{urgingTicketNum,jdbcType=DECIMAL},
      </if>
      <if test="urgingTicketAmount != null" >
        #{urgingTicketAmount,jdbcType=DECIMAL},
      </if>
      <if test="alreadyInputNum != null" >
        #{alreadyInputNum,jdbcType=DECIMAL},
      </if>
      <if test="alreadyInputAmout != null" >
        #{alreadyInputAmout,jdbcType=DECIMAL},
      </if>
      <if test="waningLevel != null" >
        #{waningLevel,jdbcType=BIT},
      </if>
      <if test="taskStatus != null" >
        #{taskStatus,jdbcType=INTEGER},
      </if>
      <if test="taskDealer != null" >
        #{taskDealer,jdbcType=VARCHAR},
      </if>
      <if test="followUpPerson != null" >
        #{followUpPerson,jdbcType=VARCHAR},
      </if>
      <if test="followUpNum != null" >
        #{followUpNum,jdbcType=INTEGER},
      </if>
      <if test="followUpTime != null" >
        #{followUpTime,jdbcType=VARCHAR},
      </if>
      <if test="sendInitMessage != null" >
        #{sendInitMessage,jdbcType=BIT},
      </if>
      <if test="sendImpendingMessage != null" >
        #{sendImpendingMessage,jdbcType=BIT},
      </if>
      <if test="sendOverdueMessage != null" >
        #{sendOverdueMessage,jdbcType=BIT},
      </if>
      <if test="sendOver12hoursMessage != null" >
        #{sendOver12hoursMessage,jdbcType=BIT},
      </if>
      <if test="sendOver48hoursMessage != null" >
        #{sendOver48hoursMessage,jdbcType=BIT},
      </if>
      <if test="isDeleted != null" >
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=VARCHAR},
      </if>
      <if test="updator != null" >
        #{updator,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.flash.model.EarlyWarningTask" >
    update T_EARLY_WARNING_TASK
    <set >
      <if test="earlyWarningType != null" >
        EARLY_WARNING_TYPE = #{earlyWarningType,jdbcType=INTEGER},
      </if>
      <if test="relateBusinessId != null" >
        RELATE_BUSINESS_ID = #{relateBusinessId,jdbcType=BIGINT},
      </if>
      <if test="businessExtra1 != null" >
        BUSINESS_EXTRA1 = #{businessExtra1,jdbcType=VARCHAR},
      </if>
      <if test="businessExtra2 != null" >
        BUSINESS_EXTRA2 = #{businessExtra2,jdbcType=BIGINT},
      </if>
      <if test="urgingTicketNum != null" >
        URGING_TICKET_NUM = #{urgingTicketNum,jdbcType=INTEGER},
      </if>
      <if test="urgingTicketAmount != null" >
        URGING_TICKET_AMOUNT = #{urgingTicketAmount,jdbcType=DECIMAL},
      </if>
      <if test="alreadyInputNum != null" >
        ALREADY_INPUT_NUM = #{alreadyInputNum,jdbcType=DECIMAL},
      </if>
      <if test="alreadyInputAmout != null" >
        ALREADY_INPUT_AMOUT = #{alreadyInputAmout,jdbcType=DECIMAL},
      </if>
      <if test="waningLevel != null" >
        WANING_LEVEL = #{waningLevel,jdbcType=BIT},
      </if>
      <if test="taskStatus != null" >
        TASK_STATUS = #{taskStatus,jdbcType=INTEGER},
      </if>
      <if test="taskDealer != null" >
        TASK_DEALER = #{taskDealer,jdbcType=VARCHAR},
      </if>
      <if test="followUpPerson != null" >
        FOLLOW_UP_PERSON = #{followUpPerson,jdbcType=VARCHAR},
      </if>
      <if test="followUpNum != null" >
        FOLLOW_UP_NUM = #{followUpNum,jdbcType=INTEGER},
      </if>
      <if test="followUpTime != null" >
        FOLLOW_UP_TIME = #{followUpTime,jdbcType=VARCHAR},
      </if>
      <if test="sendInitMessage != null" >
        SEND_INIT_MESSAGE = #{sendInitMessage,jdbcType=BIT},
      </if>
      <if test="sendImpendingMessage != null" >
        SEND_IMPENDING_MESSAGE = #{sendImpendingMessage,jdbcType=BIT},
      </if>
      <if test="sendOverdueMessage != null" >
        SEND_OVERDUE_MESSAGE = #{sendOverdueMessage,jdbcType=BIT},
      </if>
      <if test="sendOver12hoursMessage != null" >
        SEND_OVER12HOURS_MESSAGE = #{sendOver12hoursMessage,jdbcType=BIT},
      </if>
      <if test="sendOver48hoursMessage != null" >
        SEND_OVER48HOURS_MESSAGE = #{sendOver48hoursMessage,jdbcType=BIT},
      </if>
      <if test="isDeleted != null" >
        IS_DELETED = #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=VARCHAR},
      </if>
      <if test="updator != null" >
        UPDATOR = #{updator,jdbcType=INTEGER},
      </if>
      <if test="followUpComment != null" >
        FOLLOW_UP_COMMENT = #{followUpComment,jdbcType=VARCHAR},
      </if>
      <if test="followUpResult != null" >
        FOLLOW_UP_RESULT = #{followUpResult,jdbcType=INTEGER},
      </if>
      <if test="canTicketTime != null" >
        CAN_TICKET_TIME = #{canTicketTime,jdbcType=VARCHAR},
      </if>
      <if test="canTicketAmount != null" >
        CAN_TICKET_AMOUNT = #{canTicketAmount,jdbcType=DECIMAL},
      </if>
      <if test="urgingTicketNumFollowUp != null" >
        URGING_TICKET_NUM_FOLLOW_UP = #{urgingTicketNumFollowUp,jdbcType=DECIMAL},
      </if>
      <if test="urgingTicketAmountFollowUp != null" >
        URGING_TICKET_AMOUNT_FOLLOW_UP = #{urgingTicketAmountFollowUp,jdbcType=DECIMAL},
      </if>
    </set>
    where EARLY_WARNING_TASK_ID = #{earlyWarningTaskId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.flash.model.EarlyWarningTask" >
    update T_EARLY_WARNING_TASK
    set EARLY_WARNING_TYPE = #{earlyWarningType,jdbcType=INTEGER},
      RELATE_BUSINESS_ID = #{relateBusinessId,jdbcType=BIGINT},
      BUSINESS_EXTRA1 = #{businessExtra1,jdbcType=VARCHAR},
      BUSINESS_EXTRA2 = #{businessExtra2,jdbcType=BIGINT},
      URGING_TICKET_NUM = #{urgingTicketNum,jdbcType=DECIMAL},
      URGING_TICKET_AMOUNT = #{urgingTicketAmount,jdbcType=DECIMAL},
      ALREADY_INPUT_NUM = #{alreadyInputNum,jdbcType=DECIMAL},
      ALREADY_INPUT_AMOUT = #{alreadyInputAmout,jdbcType=DECIMAL},
      WANING_LEVEL = #{waningLevel,jdbcType=BIT},
      TASK_STATUS = #{taskStatus,jdbcType=INTEGER},
      TASK_DEALER = #{taskDealer,jdbcType=VARCHAR},
      FOLLOW_UP_PERSON = #{followUpPerson,jdbcType=VARCHAR},
      FOLLOW_UP_NUM = #{followUpNum,jdbcType=INTEGER},
      FOLLOW_UP_TIME = #{followUpTime,jdbcType=VARCHAR},
      SEND_INIT_MESSAGE = #{sendInitMessage,jdbcType=BIT},
      SEND_IMPENDING_MESSAGE = #{sendImpendingMessage,jdbcType=BIT},
      SEND_OVERDUE_MESSAGE = #{sendOverdueMessage,jdbcType=BIT},
      SEND_OVER12HOURS_MESSAGE = #{sendOver12hoursMessage,jdbcType=BIT},
      SEND_OVER48HOURS_MESSAGE = #{sendOver48hoursMessage,jdbcType=BIT},
      IS_DELETED = #{isDeleted,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATE_TIME = #{updateTime,jdbcType=VARCHAR},
      UPDATOR = #{updator,jdbcType=INTEGER}
    where EARLY_WARNING_TASK_ID = #{earlyWarningTaskId,jdbcType=BIGINT}
  </update>

  <insert id="insertBatch" parameterType="list">
    insert into T_EARLY_WARNING_TASK (EARLY_WARNING_TYPE,
                                      RELATE_BUSINESS_ID, BUSINESS_EXTRA1, BUSINESS_EXTRA2,
                                      URGING_TICKET_NUM, URGING_TICKET_AMOUNT, ALREADY_INPUT_NUM,
                                      ALREADY_INPUT_AMOUT, WANING_LEVEL, TASK_STATUS,
                                      TASK_DEALER, FOLLOW_UP_PERSON, FOLLOW_UP_NUM,
                                      FOLLOW_UP_TIME, SEND_INIT_MESSAGE, SEND_IMPENDING_MESSAGE,
                                      SEND_OVERDUE_MESSAGE, SEND_OVER12HOURS_MESSAGE, SEND_OVER48HOURS_MESSAGE,
                                      IS_DELETED, ADD_TIME, CREATOR,
                                      UPDATE_TIME, UPDATOR)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.earlyWarningType,jdbcType=INTEGER},
      #{item.relateBusinessId,jdbcType=BIGINT}, #{item.businessExtra1,jdbcType=VARCHAR}, #{item.businessExtra2,jdbcType=BIGINT},
      #{item.urgingTicketNum,jdbcType=DECIMAL}, #{item.urgingTicketAmount,jdbcType=DECIMAL}, #{item.alreadyInputNum,jdbcType=DECIMAL},
      #{item.alreadyInputAmout,jdbcType=DECIMAL}, #{item.waningLevel,jdbcType=BIT}, #{item.taskStatus,jdbcType=INTEGER},
      #{item.taskDealer,jdbcType=VARCHAR}, #{item.followUpPerson,jdbcType=VARCHAR}, #{item.followUpNum,jdbcType=INTEGER},
      #{item.followUpTime,jdbcType=VARCHAR}, #{item.sendInitMessage,jdbcType=BIT}, #{item.sendImpendingMessage,jdbcType=BIT},
      #{item.sendOverdueMessage,jdbcType=BIT}, #{item.sendOver12hoursMessage,jdbcType=BIT}, #{item.sendOver48hoursMessage,jdbcType=BIT},
      #{item.isDeleted,jdbcType=INTEGER}, #{item.addTime,jdbcType=VARCHAR}, #{item.creator,jdbcType=INTEGER},
      #{item.updateTime,jdbcType=VARCHAR}, #{item.updator,jdbcType=INTEGER})
    </foreach>
  </insert>

  <update id="updateBatch" parameterType="list">
    <foreach collection="list" item="item" index="index" open="" close="" separator=";">
      update T_EARLY_WARNING_TASK
      <set>
        EARLY_WARNING_TYPE = #{item.earlyWarningType,jdbcType=INTEGER},
        RELATE_BUSINESS_ID = #{item.relateBusinessId,jdbcType=BIGINT},
        BUSINESS_EXTRA1 = #{item.businessExtra1,jdbcType=VARCHAR},
        BUSINESS_EXTRA2 = #{item.businessExtra2,jdbcType=BIGINT},
        URGING_TICKET_NUM = #{item.urgingTicketNum,jdbcType=DECIMAL},
        URGING_TICKET_AMOUNT = #{item.urgingTicketAmount,jdbcType=DECIMAL},
        ALREADY_INPUT_NUM = #{item.alreadyInputNum,jdbcType=DECIMAL},
        ALREADY_INPUT_AMOUT = #{item.alreadyInputAmout,jdbcType=DECIMAL},
        WANING_LEVEL = #{item.waningLevel,jdbcType=BIT},
        TASK_STATUS = #{item.taskStatus,jdbcType=INTEGER},
        TASK_DEALER = #{item.taskDealer,jdbcType=VARCHAR},
        FOLLOW_UP_PERSON = #{item.followUpPerson,jdbcType=VARCHAR},
        FOLLOW_UP_NUM = #{item.followUpNum,jdbcType=INTEGER},
        FOLLOW_UP_TIME = #{item.followUpTime,jdbcType=VARCHAR},
        SEND_INIT_MESSAGE = #{item.sendInitMessage,jdbcType=BIT},
        SEND_IMPENDING_MESSAGE = #{item.sendImpendingMessage,jdbcType=BIT},
        SEND_OVERDUE_MESSAGE = #{item.sendOverdueMessage,jdbcType=BIT},
        SEND_OVER12HOURS_MESSAGE = #{item.sendOver12hoursMessage,jdbcType=BIT},
        SEND_OVER48HOURS_MESSAGE = #{item.sendOver48hoursMessage,jdbcType=BIT},
        IS_DELETED = #{item.isDeleted,jdbcType=INTEGER},
        ADD_TIME = #{item.addTime,jdbcType=VARCHAR},
        CREATOR = #{item.creator,jdbcType=INTEGER},
        UPDATE_TIME = #{item.updateTime,jdbcType=VARCHAR},
        UPDATOR = #{item.updator,jdbcType=INTEGER}
      </set>
      where EARLY_WARNING_TASK_ID = #{item.earlyWarningTaskId,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateDelteStatusById">
    update T_EARLY_WARNING_TASK
    set IS_DELETED = 1
    where EARLY_WARNING_TASK_ID = #{earlyWarningTaskId,jdbcType = INTEGER}
  </update>

  <update id="batchCloseOrderGoodsTask">
    UPDATE T_EARLY_WARNING_TASK
    SET IS_DELETED = 1
    WHERE
      EARLY_WARNING_TYPE = 1
      AND RELATE_BUSINESS_ID IN
    <foreach collection="orderGoodsIdList" item="goodsId" separator="," open="(" close=")" >
      #{goodsId,jdbcType = INTEGER}
    </foreach>
  </update>
  <update id="closeEarlyTicketTaskList">
    update T_EARLY_WARNING_TASK set IS_DELETED = 1
    where EARLY_WARNING_TASK_ID IN
    <foreach collection="earlyWarningTicketTaskList" item="task" separator="," open="(" close=")" >
      #{task.earlyWarningTaskId,jdbcType = INTEGER}
    </foreach>
  </update>

  <update id="batchCloseUrgeTicketByOrderNo">
    UPDATE T_EARLY_WARNING_TASK
    SET IS_DELETED = 1
    WHERE
      EARLY_WARNING_TYPE = 2
      AND BUSINESS_EXTRA1 = #{orderNo,jdbcType=VARCHAR}
  </update>

  <select id="getExpeditionTaskInfoByBuyorderGoodsId" resultType="com.vedeng.flash.dto.EarlyWarningTaskDto">
    select EARLY_WARNING_TASK_ID earlyWarningTaskId,TASK_STATUS taskStatus, FOLLOW_UP_NUM followUpNum,FOLLOW_UP_PERSON followUpPeason,
           FOLLOW_UP_TIME followUpTime,FOLLOW_UP_COMMENT followUpComment
    from T_EARLY_WARNING_TASK
    where RELATE_BUSINESS_ID = #{buyorderGoodsId,jdbcType = INTEGER}
    and EARLY_WARNING_TYPE = 1
    and IS_DELETED = 0
  </select>
  <select id="getFollowInfoDetailById" resultType="com.vedeng.flash.dto.EarlyWarningTaskDto">
     select EARLY_WARNING_TASK_ID earlyWarningTaskId,TASK_STATUS taskStatus, FOLLOW_UP_NUM followUpNum,FOLLOW_UP_PERSON followUpPerson,
           FOLLOW_UP_TIME followUpTime,FOLLOW_UP_COMMENT followUpComment
      from T_EARLY_WARNING_TASK
      where EARLY_WARNING_TASK_ID = #{earlyWarningTaskId,jdbcType = INTEGER}
      and EARLY_WARNING_TYPE = 1
      and IS_DELETED = 0
  </select>
  <select id="getEarlyWarningGoodsTaskListByuserIds" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List"/>
      from  T_EARLY_WARNING_TASK
      where EARLY_WARNING_TYPE = 1
      and IS_DELETED = 0
      and TASK_DEALER in
      <foreach collection="userIdList" item="userId" separator="," open="(" close=")" >
        #{userId,jdbcType = INTEGER}
      </foreach>
  </select>
  <select id="getPrepareStockTask" parameterType="com.vedeng.flash.dto.PrepareStockDto" resultType="com.vedeng.flash.dto.EarlyWarningTaskDto">
      SELECT
      <include refid="Base_Column_List"/>
      FROM  T_EARLY_WARNING_TASK
      WHERE EARLY_WARNING_TYPE = 3
      AND IS_DELETED = 0
      AND RELATE_BUSINESS_ID = #{skuId,jdbcType=INTEGER}
  </select>
  <select id="getPrepareStockTaskByUserId" parameterType="list" resultType="com.vedeng.todolist.dto.PrepareStockTaskToDoDto">
      SELECT (
          SELECT
            count(*) AS totalTaskNum
          FROM
            T_EARLY_WARNING_TASK
          WHERE
          EARLY_WARNING_TYPE = 3
          AND IS_DELETED = 0
          AND
          <foreach collection="list" item="item" open="(" close=")" separator="OR">
            FIND_IN_SET( #{item}, TASK_DEALER )
          </foreach>
      ) 'totalTaskNum',(
          SELECT
            count(*) AS firstLevelTaskNum
          FROM
            T_EARLY_WARNING_TASK
          WHERE
          EARLY_WARNING_TYPE = 3
          AND IS_DELETED = 0
          AND WANING_LEVEL = 2
          AND
          <foreach collection="list" item="item" open="(" close=")" separator="OR">
            FIND_IN_SET( #{item}, TASK_DEALER )
          </foreach>
      ) 'firstLevelTaskNum',(
          SELECT
            count(*) AS secondLevelTaskNum
          FROM
            T_EARLY_WARNING_TASK
          WHERE
          EARLY_WARNING_TYPE = 3
          AND IS_DELETED = 0
          AND WANING_LEVEL = 1
          AND
          <foreach collection="list" item="item" open="(" close=")" separator="OR">
            FIND_IN_SET( #{item}, TASK_DEALER )
          </foreach>
       ) 'secondLevelTaskNum'
  </select>
  <select id="getAllExpedingTask" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_EARLY_WARNING_TASK
    where EARLY_WARNING_TYPE = 1
    and IS_DELETED = 0

  </select>
    <select id="getFollowInfoDetail" resultType="com.vedeng.flash.model.EarlyWarningTask">
      select
      <include refid="Base_Column_List"/>
      from T_EARLY_WARNING_TASK
      where EARLY_WARNING_TYPE = 1
      and IS_DELETED = 0
      and EARLY_WARNING_TASK_ID = #{earlyWarningTaskId,jdbcType = INTEGER}
    </select>
    <select id="deletePrepareStockBySkuId" parameterType="com.vedeng.flash.dto.PrepareStockDto">
      UPDATE T_EARLY_WARNING_TASK SET IS_DELETED = 1
      WHERE EARLY_WARNING_TYPE = 3
      AND RELATE_BUSINESS_ID = #{skuId,jdbcType=INTEGER}
    </select>
    <select id="getExistEarlyWarningGoodsTaskListByBuyorderGoodsIdList"
            resultType="com.vedeng.flash.model.EarlyWarningTask">
        select
        <include refid="Base_Column_List"/>
        from T_EARLY_WARNING_TASK
        where EARLY_WARNING_TYPE = 1
        and IS_DELETED = 0
        and RELATE_BUSINESS_ID IN
        <foreach collection="buyorderGoodsIdList" separator="," open="(" close=")" item="buyorderGoodsId">
            #{buyorderGoodsId,jdbcType=INTEGER}
        </foreach>
    </select>
</mapper>
