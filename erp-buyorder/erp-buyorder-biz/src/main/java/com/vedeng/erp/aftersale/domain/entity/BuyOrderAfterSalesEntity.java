package com.vedeng.erp.aftersale.domain.entity;

import lombok.Data;

import java.util.Date;

@Data
public class BuyOrderAfterSalesEntity {
    private Integer afterSalesId;

    /**
     * 售后订单号
     */
    private String afterSalesNo;

    /**
     * ERP公司ID(T_COMPANY)
     */
    private Integer companyId;

    /**
     * 售后主体类型（字典库:销售、采购、第三方）
     */
    private Integer subjectType;

    /**
     * 售后类型（字典库）
     */
    private Integer type;

    /**
     * 对应订单ID
     */
    private Integer orderId;

    /**
     * 对应订单单号
     */
    private String orderNo;

    /**
     * 归属售后人员
     */
    private Integer serviceUserId;

    /**
     * 是否生效 0否 1是
     */
    private Integer validStatus;

    /**
     * 生效时间
     */
    private Long validTime;

    /**
     * 售后订单审核状态：0待确认（默认）、1审核中、2审核通过、3审核不通过
     */
    private Integer status;

    /**
     * 售后订单状态：0待确认（默认）、1进行中、2已完结、3已关闭
     */
    private Integer atferSalesStatus;

    /**
     * 关闭/完结原因（字典库）
     */
    private Integer atferSalesStatusReson;

    /**
     * 关闭/完结人员
     */
    private Integer atferSalesStatusUser;

    /**
     * 关闭/完结备注
     */
    private String atferSalesStatusComments;

    /**
     * 首次审核状态0待审核1审核通过2审核不通过
     */
    private Integer firstValidStatus;

    /**
     * 首次审核时间
     */
    private Long firstValidTime;

    /**
     * 首次审核人ID
     */
    private Integer firstValidUser;

    /**
     * 首次审核原因
     */
    private String firstValidComments;

    /**
     * 售后来源0ERP 1耗材 2贝登前台
     */
    private Integer source;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 添加人
     */
    private Integer creator;

    /**
     * 最近一次编辑时间
     */
    private Long modTime;

    /**
     * 最近一次编辑人
     */
    private Integer updater;

    /**
     * 是否可以出库(1、可以 2、不可以)
     */
    private Integer isOutAfter;

    /**
     * 发票寄送状态（0未寄送 1 部分寄送 2 全部寄送）
     */
    private Integer invoiceSendStatus;

    /**
     * 发票收货状态（0未收货 1 部分收货 2 全部收货）
     */
    private Integer invoiceArrivalStatus;

    /**
     * 售后单关联业务更新时间
     */
    private Date updateDataTime;

    /**
     * 是否闪电发货0为是1为否
     */
    private Integer isLightning;

    /**
     * 售后单创建方式0手动1自动
     */
    private Integer createType;

    /**
     * 创建售后单的前台用户名称
     */
    private String createFrontEndUser;

    /**
     * 关闭售后单的前台用户名称
     */
    private String closeFrontEndMobile;

    /**
     * 审核不通过原因
     */
    private String verifiesNotPassReason;

    /**
     * 处理状态 0无处理状态 1未处理 2部分处理 3全部处理
     */
    private Integer handleStatus;

    /**
     * 退票状态 0无退票状态 1未退票 2部分退票 3全部退票
     */
    private Integer invoiceRefundStatus;

    /**
     * 退款状态 0无退款状态 1未退款 2部分退款 3全部退款 (用于筛选)
     */
    private Integer amountRefundStatus;

    /**
     * 收款状态: 0无收款状态 1未收款 2部分收款 3全部收款
     */
    private Integer amountCollectionStatus;

    /**
     * 付款状态：0无付款状态 1未付款 2部分付款 3全部付款
     */
    private Integer amountPayStatus;

    /**
     * 开票状态：0无开票 1未开票 2全部开票
     */
    private Integer invoiceMakeoutStatus;

    /**
     * 是否是订单流新订单
     */
    private Integer isNew;

    /**
     * 直发采购售后关联销售售后ID
     */
    private Integer deliveryDirectAfterSalesId;
}
