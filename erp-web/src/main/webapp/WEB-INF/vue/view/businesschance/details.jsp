<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>

<div id="app" style="display: none;">
    <el-steps :active="this.activeNode" align-center>
        <el-step title="未处理"></el-step>
        <el-step title="处理中"></el-step>
        <el-step title="报价中"></el-step>
        <el-step title="已报价"></el-step>
        <el-step title="已订单"></el-step>
        <el-step title="已成单"></el-step>
        <el-step title="已关闭"></el-step>
    </el-steps>

    <el-row type="flex" justify="center" style="margin: 20px 0">
        <el-button type="primary" v-if="businessChanceDetail.status == 0 || businessChanceDetail.status == 6"
                   @click="editChance()">编辑商机
        </el-button>
        <el-button type="primary" @click="addCommunicateRecord">新增沟通</el-button>
        <el-button type="info" v-if="businessChanceDetail.userId == businessChanceDetail.currentUserId && (businessChanceDetail.status == 0 || businessChanceDetail.status == 6) && endStatusFlag == true"
                   @click="closeChance(businessChanceDetail.bussinessChanceId)">关闭商机
        </el-button>
        <el-button type="warning" @click="editChance()" v-if="businessChanceDetail.status == 0 || businessChanceDetail.status == 6">转报价</el-button>
        <el-button type="primary" @click="shareDialogVisible = true" v-if="isBelongSale">分享商机</el-button>
        <el-button type="primary" @click="closeAuditVisible = true" v-if="currentUserAudit">关闭审核</el-button>
        <el-popover
                v-if="!isBelongSale"
                placement="top-start"
                width="200"
                trigger="hover"
                :content="tipMessageShare">
            <el-button style="margin-left: 10px;" type="primary" slot="reference" disabled>分享商机</el-button>
        </el-popover>

    </el-row>

    <el-dialog
            title="关闭审核"
            :visible.sync="closeAuditVisible"
            width="500px"
    >
        <el-form :model="autoAuditForm" :rules="rules" ref="autoAuditForm" label-width="110px">
            <el-form-item label="商机关闭申请" prop="status" >
                <el-radio-group v-model="autoAuditForm.status" >
                    <el-radio label="true">通过</el-radio>
                    <el-radio label="false">不通过</el-radio>
                </el-radio-group>
            </el-form-item>

            <el-form-item label="审核备注">
                <el-input v-model="autoAuditForm.remark"></el-input>
            </el-form-item>

            <el-form-item style="margin-left: -100px;display: flex;justify-content: center;">
                <el-button type="primary" :loading="closeLoading" @click="submitForm(autoAuditForm)">确定</el-button>
                <el-button @click="cancelAuditForm">取消</el-button>
            </el-form-item>
        </el-form>

    </el-dialog>

    <el-dialog
            :title="commTitle"
            :visible.sync="dialogVisible"
            width="900px"
    >
        <el-form :rules="rules" ref="communicateRecordForm" :model="communicateRecordDto">
            <div v-show="businessChanceDetail.traderId!=null&&businessChanceDetail.traderId!=0">
                <el-form-item label="联系人:" label-width="150px" prop="traderContactNameView">
                <span slot="label" style="color: #00a0e9;font-weight: bold">
                    联系人:
                </span>

                    <el-select
                            v-model="communicateRecordDto.traderContactNameView"
                            filterable
                            clearable
                            reserve-keyword
                            placeholder="请选择联系人"
                            style="width: 300px;"
                            @clear="traderContactClearComm"
                    >
                        <el-option
                                v-for="item in traderConcatDatas"
                                :key="item.traderContactId"
                                :label="item.name"
                                :value="item.traderContactId"
                                @click.native="traderConcatSelectComm(item)"
                        >
                            <span style="float: left">{{ item.name + '/' + item.mobile }}</span>
                        </el-option>

                    </el-select>
                    <span class="title-click   bg-light-bule pop-new-data bt-small mr10 bt-bg-style J-add-prod"
                          id="concat"
                          style="margin-left:24px;color: #3384ef;padding-right: 10px;cursor: pointer;height: 34px;line-height: 34px;"
                          :layerParams="'{&quot;width&quot;:&quot;50%&quot;,&quot;height&quot;:&quot;75%&quot;,&quot;title&quot;:&quot;添加联系人&quot;,&quot;link&quot;:&quot;/orderstream/saleorder/addContact.do?indexId=3&amp;traderId='+businessChanceDetail.traderId+'&quot;}'"><span
                            v-show="businessChanceDetail.traderId!=null"
                            onclick="changeNewConcatFlag(2);addTraderContract()">添加联系人</span></span>

                </el-form-item>

            </div>

            <div v-show="businessChanceDetail.traderId==null||businessChanceDetail.traderId==''||businessChanceDetail.traderId==0">
                <el-form-item label="联系人:" label-width="150px" prop="traderContactNameNew">
                <span slot="label" style="color: #00a0e9;font-weight: bold">
                    联系人:
                </span>
                    <el-input type="text" v-model="communicateRecordDto.traderContactNameNew" placeholder="联系人姓名"></el-input>
                </el-form-item>

                <el-form-item label="联系电话:" label-width="150px" prop="traderContactMobile">
                    <span slot="label" style="color: #F56C6C;font-weight: bold">
                    *
                </span>
                <span slot="label" style="color: #00a0e9;font-weight: bold">
                    联系电话:
                </span>
                    <el-input type="tel" v-model="communicateRecordDto.traderContactMobile" placeholder="联系电话"></el-input>
                </el-form-item>
            </div>

            <el-form-item label="沟通时间:" label-width="150px" prop="time">
                <el-date-picker
                        v-model="communicateRecordDto.time"
                        type="datetimerange"
                        value-format="timestamp"
                        range-separator="至"
                        start-placeholder="沟通开始时间"
                        end-placeholder="沟通结束时间">

                </el-date-picker>
            </el-form-item>

            <el-form-item label="沟通内容:" label-width="150px" prop="">
                <el-input
                        v-model="communicateRecordDto.contentSuffix"
                        type="textarea"
                        placeholder="沟通内容最多输入200个汉字"
                        style="width: 500px"
                        maxlength="200"
                        rows="6"
                >
                </el-input>
                <br>
                <div style="width: 500px">
                    <el-tag
                            v-for="item in sysTagUserCheck"
                            closable
                            :key="item.tagId"
                            style="margin-right: 8px;cursor : pointer"
                    <%--@click.native="sysTagClick(item)"--%>
                            @close="handlesysTagUserClose(item)"
                            effect="light">
                        {{ item.tagName }}
                    </el-tag>
                </div>
                <div style="width: 500px">
                    <el-tag
                            v-for="item in communicateRecordDto.userTag"
                            :key="item"
                            closable
                            type="success"
                            style="margin-right: 8px;cursor : pointer"
                            @close="handleUserTagClose(item)"
                            effect="light">
                        {{ item }}
                    </el-tag>
                </div>
                <span @click="showTag=!showTag"><i class="el-icon-edit-outline" style="color: #00a0e9">标签</i></span>
                <div v-if="showTag">
                    <div style="width: 500px">
                        <el-tag
                                v-for="item in sysTag"
                                :key="item.tagId"
                                style="margin-right: 8px;cursor : pointer"
                                @click.native="sysTagClick(item)"
                                effect="light">
                            {{ item.tagName }}
                        </el-tag>
                        <br>
                        <el-input
                                class="input-new-tag"
                                v-if="inputVisible_tag"
                                v-model="inputTagValue"
                                ref="saveTagInput"
                                size="small"
                                @keyup.enter.native="handleInputConfirm"
                                @blur="handleInputConfirm"
                        >
                        </el-input>
                        <el-button v-else class="button-new-tag" size="small" @click="showInput">+ 如果标签中没有您所需要的，请在此处自行填写
                        </el-button>
                    </div>
                </div>


            </el-form-item>

            <div v-show="businessChanceDetail.type==391||businessChanceDetail.type== 394">
                <el-form-item label="商机精准度:" label-width="150px" prop="businessChanceAccuracy">
                    <span slot="label" style="color: #F56C6C;font-weight: bold">
                    *
                </span>
                <span slot="label" style="color: #00a0e9;font-weight: bold">
                    商机精准度:
                </span>

                    <el-select
                            v-model="communicateRecordDto.businessChanceAccuracy"
                            reserve-keyword
                            placeholder="请选择商机精准度"
                            style="width: 300px;"
                    >
                        <el-option
                                v-for="item in businessChanceAccuracys"
                                :key="item.businessChanceAccuracy"
                                :label="item.businessChanceAccuracyShow"
                                :value="item.businessChanceAccuracy"
                                @click.native="businessChanceAccuracySelectComm(item)"
                        >
                        </el-option>

                    </el-select>
                    <el-tooltip class="item" v-if="belongPlatfromByOrgAndUser == 0" placement="bottom" effect="light"  placement="top">
                        <div slot="content">高精准：客户属于本部门经营对象，购买意向为公司签约商品或者一年内有成交过的商品；
                            <br/>一般精准：客户属于本部门经营对象，但当前没有明确购买意向或者需求商品不是公司签约商品和一年内有成交过的商品
                            <br/>不精准：客户不属于本部门经营对象，且目前没有明确购买意向或者需求商品不是公司签约商品和一年内有成交过的商品
                            <br/>无法判断：未接通（连续三天联系不上）</div>
                        <i class="el-icon-warning" style="margin-left: 5px; color: rgba(7,174,241,0.95)"></i>
                    </el-tooltip>
                    <el-tooltip class="item" v-if="belongPlatfromByOrgAndUser == 1" placement="bottom" effect="light"  placement="top">
                        <div slot="content">高精准：客户有明确购买意向，且需求与我司匹配
                            <br/>一般精准：客户有明确购买意向，但需求与我司不匹配
                            <br/>不精准：客户目前没有明确购买意向
                            <br/>无法判断：未接通（连续三天联系不上）
                        </div>
                        <i class="el-icon-warning" style="margin-left: 5px; color: rgba(7,174,241,0.95)"></i>
                    </el-tooltip>
                </el-form-item>
            </div>
            <el-form-item label="下次沟通时间:" label-width="150px" prop="nextContactDate">
                <el-date-picker
                        v-model="communicateRecordDto.nextContactDate"
                        type="date"
                        placeholder="选择日期时间"
                        :disabled="communicateRecordDto.noneNextDate"
                        default-time="00:00:00">
                </el-date-picker>
                <el-checkbox v-model="communicateRecordDto.noneNextDate"
                             @change="communicateRecordNextBind">暂无下次沟通记录
                </el-checkbox>
            </el-form-item>

            <el-form-item label="下次沟通内容:" label-width="150px" prop="">
                <el-input
                        v-model="communicateRecordDto.nextContactContent"
                        type="textarea"
                        placeholder=""
                        style="width: 500px"
                        maxlength="200"
                        rows="6"
                >
                </el-input>
            </el-form-item>

            <el-form-item label="备注:" label-width="150px" prop="">
                <el-input
                        v-model="communicateRecordDto.comments"
                        type="textarea"
                        placeholder=""
                        style="width: 500px"
                        maxlength="200"
                        rows="3"
                >
                </el-input>
            </el-form-item>
        </el-form>
        <span style="display: block;text-align: center;">
            <el-button @click="reInitCommun()">取 消</el-button>
            <el-button type="primary" @click="saveCommunication" :disabled="disabledForm">确 定</el-button>
        </span>
    </el-dialog>

    <!-- 添加支持记录-->
    <el-dialog
            :title="addSupportTitle"
            :visible.sync="addSupportDialogVisible"
            width="800px"
    >
        <el-form :inline="true" :rules="rules" ref="addSupportForm" :model="addSupportDto">
            <el-form-item label="评估等级:" label-width="120px" prop="businessLevel">
                <el-select
                        size="mini"
                        v-model="addSupportDto.businessLevel"
                        style="width: 200px"
                        placeholder="请选择">
                    <el-option
                            v-for="item in levelOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>
            <!-- 评估成交金额：元-->
            <el-form-item label="评估成交金额:" label-width="120px" prop="amount">
                <div style="display:inline-flex">
                    <el-input
                            size="mini"
                            v-model="addSupportDto.amount"
                            style="width: 200px"
                            placeholder="请输入金额，支持两位小数">
                    </el-input>
                    <div style="margin-left:5px">元</div>
                </div>

            </el-form-item>
            <el-form-item label="评估成交时间:" label-width="120px" prop="orderDate">
                <el-date-picker
                        size="mini"
                        v-model="addSupportDto.orderDate"
                        type="date"
                        placeholder="选择日期时间"
                        style="width: 200px"
                        default-time="00:00:00">
                </el-date-picker>
            </el-form-item>

            <el-form-item label="下次支持时间:" label-width="120px" prop="nextSupportDate">
                <el-date-picker
                        size="mini"
                        v-model="addSupportDto.nextSupportDate"
                        type="date"
                        placeholder="选择日期时间"
                        style="width: 200px"
                        :picker-options="pickerOptions"
                        default-time="00:00:00">
                </el-date-picker>
            </el-form-item>
            <el-form-item label="支持内容:" label-width="120px" prop="content">
                <el-input
                        v-model="addSupportDto.content"
                        type="textarea"
                        placeholder="请输入支持内容必填项，不超过500字"
                        style="width: 535px"
                        maxlength="500"
                        rows="3"
                        show-word-limit
                >
                </el-input>
            </el-form-item>

        </el-form>
        <span style="display: block;text-align: center;">
            <el-button @click="cancelAddSupport()">取消</el-button>
            <el-button type="primary" @click="saveAddSupport" :disabled="disabledForm" :loading="saveAddSupportLoading">保存</el-button>
        </span>
    </el-dialog>

    <!-- 咨询方案-->
    <el-dialog
            :title="addRequestTitle"
            :visible.sync="addRequestDialogVisible"
            width="750px"
    >
        <el-form :rules="rules" ref="addRequestForm" :model="addRequestDto">
            <el-form-item label="咨询方案" label-width="150px">
                <el-input
                        size="mini"
                        placeholder="输入关键字进行过滤"
                        style="width: 500px"
                        v-model="filterText">
                </el-input>
                <div class="tree-container">
                    <el-tree
                            :data="data"
                            show-checkbox
                            :props="defaultProps"
                            default-expand-all
                            :filter-node-method="filterNode"
                            ref="addRequestTree">
                    </el-tree>
                </div>
            </el-form-item>
            <el-form-item label="咨询内容:" label-width="150px" prop="requestContent">
                <el-input
                        v-model="addRequestDto.requestContent"
                        type="textarea"
                        placeholder="请输入咨询内容，不超过100字"
                        style="width: 500px"
                        maxlength="100"
                        rows="3"
                        show-word-limit
                >
                </el-input>
            </el-form-item>

        </el-form>
        <span style="display: block;text-align: center;">
            <el-button @click="cancelAddRequest()">取 消</el-button>
            <el-button type="primary" @click="saveAddRequest" :disabled="disabledForm" :loading="saveAddRequestLoading">立即咨询</el-button>
        </span>
    </el-dialog>

    <el-descriptions title="基本信息" border column="2">
        <el-descriptions-item label="商机编号" content-class-name="my-content">{{businessChanceDetail.bussinessChanceNo}}
        </el-descriptions-item>
        <el-descriptions-item label="商机类型">{{businessChanceDetail.typeName}}</el-descriptions-item>
        <el-descriptions-item label="商机等级">
            {{businessChanceDetail.systemBusinessLevelStr == null ? '' : '系统值：' + businessChanceDetail.systemBusinessLevelStr}}
            <span v-if="businessChanceDetail.systemBusinessLevelStr != null && businessChanceDetail.bussinessLevelStr != null">
                <br>
            </span>
            {{businessChanceDetail.bussinessLevelStr == null ? '' : '个人校准值：'+ businessChanceDetail.bussinessLevelStr}}
        </el-descriptions-item>
        <el-descriptions-item label="商机成单率">
            {{businessChanceDetail.systemOrderRateStr == null ? '' : '系统值：'+ businessChanceDetail.systemOrderRateStr}}
            <span v-if="businessChanceDetail.systemOrderRateStr != null && businessChanceDetail.orderRateStr != null">
                <br>
            </span>
            {{businessChanceDetail.orderRateStr == null ? '' : '个人校准值：' + businessChanceDetail.orderRateStr}}
        </el-descriptions-item>
        <el-descriptions-item label="归属销售">{{businessChanceDetail.username}}</el-descriptions-item>
        <el-descriptions-item label="创建者">{{businessChanceDetail.creatorName}}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{parseTime(businessChanceDetail.addTime)}}</el-descriptions-item>
        <el-descriptions-item label="分配时间">{{parseTime(businessChanceDetail.assignTime)}}</el-descriptions-item>

        <el-descriptions-item label="商机时间" v-if="businessChanceDetail.type != 392">{{parseTime(businessChanceDetail.receiveTime)}}</el-descriptions-item>

        <el-descriptions-item label="商机完整度">{{businessChanceDetail.completion}}%</el-descriptions-item>

        <el-descriptions-item label="客户名称" v-if="businessChanceDetail.type != 392">{{businessChanceDetail.traderName}}</el-descriptions-item>
        <el-descriptions-item label="联系人" v-if="businessChanceDetail.type != 392">{{businessChanceDetail.traderContactName}}</el-descriptions-item>
        <el-descriptions-item label="手机号" v-if="businessChanceDetail.type != 392">{{businessChanceDetail.mobile}}</el-descriptions-item>
        <el-descriptions-item label="电话" v-if="businessChanceDetail.type != 392">{{businessChanceDetail.telephone}}</el-descriptions-item>
        <el-descriptions-item label="询价行为" v-if="businessChanceDetail.type != 392">{{businessChanceDetail.inquiryName}}</el-descriptions-item>
        <el-descriptions-item label="渠道类型" v-if="businessChanceDetail.type != 392">{{businessChanceDetail.sourceName}}</el-descriptions-item>

        <el-descriptions-item label="渠道名称">{{businessChanceDetail.communicationName}}</el-descriptions-item>

        <el-descriptions-item label="咨询入口" v-if="businessChanceDetail.type != 392">{{businessChanceDetail.entranceName}}</el-descriptions-item>
        <el-descriptions-item label="功能" v-if="businessChanceDetail.type != 392">{{businessChanceDetail.functionName}}</el-descriptions-item>
        <el-descriptions-item label="商机精准度" >
            {{businessChanceDetail.businessChanceAccuracyShow}}
            <el-tooltip class="item" v-if="belongPlatfromByOrgAndUser == 0" placement="bottom" effect="light"  placement="top">
                <div slot="content">高精准：客户属于本部门经营对象，购买意向为公司签约商品或者一年内有成交过的商品；
                    <br/>一般精准：客户属于本部门经营对象，但当前没有明确购买意向或者需求商品不是公司签约商品和一年内有成交过的商品
                    <br/>不精准：客户不属于本部门经营对象，且目前没有明确购买意向或者需求商品不是公司签约商品和一年内有成交过的商品
                    <br/>无法判断：未接通（连续三天联系不上）</div>
                <i class="el-icon-warning" style="margin-left: 5px; color: rgba(7,174,241,0.95)"></i>
            </el-tooltip>
            <el-tooltip class="item" v-if="belongPlatfromByOrgAndUser == 1" placement="bottom" effect="light"  placement="top">
                <div slot="content">高精准：客户有明确购买意向，且需求与我司匹配
                    <br/>一般精准：客户有明确购买意向，但需求与我司不匹配
                    <br/>不精准：客户目前没有明确购买意向
                    <br/>无法判断：未接通（连续三天联系不上）
                </div>
                <i class="el-icon-warning" style="margin-left: 5px; color: rgba(7,174,241,0.95)"></i>
            </el-tooltip>
        </el-descriptions-item>
    </el-descriptions>

    <el-descriptions title="产品信息" border column="1">
            <el-descriptions-item label="产品备注（总机）"> <span style="float: left">{{ this.productComments}}</span></el-descriptions-item>
            <el-descriptions-item label="产品备注（销售）"> <span style="float: left">{{ this.productCommentsSale}}</span></el-descriptions-item>
        <el-descriptions-item label="询价产品">
            <span style="float: left">{{businessChanceDetail.content}}</span>
        </el-descriptions-item>

    </el-descriptions>
            <template v-if="businessChanceDto.businessChanceGoodsDtos.length!=0">
                <el-table
                        :data="businessChanceDto.businessChanceGoodsDtos"
                        :header-cell-style="{textAlign: 'center'}"
                        border
                        style="width: 100%">
                    <el-table-column
                            label="产品名称"
                            align="center"
                    >
                        <template slot-scope="scope">
                            <el-link type="primary" @click="viewSkuDetail(scope.row.goodsId)">
                                {{ scope.row.goodsName }}
                            </el-link>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="订货号"
                            width="100"
                            align="center"
                    >
                        <template slot-scope="scope">
                            <span size="medium">{{ scope.row.sku }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="品牌"
                            width="150"
                            align="center"
                    >
                        <template slot-scope="scope">
                            <span style="margin-left: 10px">{{ scope.row.brandName }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="型号"
                            width="150"
                            align="center"
                    >
                        <template slot-scope="scope">
                            <span style="margin-left: 10px">{{ scope.row.model }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="单位"
                            align="center"
                            width="80">
                        <template slot-scope="scope">
                            <span style="margin-left: 10px">{{ scope.row.unitName }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="报价"
                            align="center"
                            width="150"
                    >
                        <template slot-scope="scope">
                            <span style="margin-left: 10px">{{ scope.row.price }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="数量"
                            align="center"
                            width="100">
                        <template slot-scope="scope">
                            <span style="margin-left: 10px">{{ scope.row.num }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="含安调"
                            align="center"
                            width="160">
                        <template slot-scope="scope">
                            <%--                    <el-radio v-model="scope.row.haveInstallation" :label="true">是</el-radio>--%>
                            <%--                    <el-radio v-model="scope.row.haveInstallation" :label="false">否</el-radio>--%>
                            <span v-if="scope.row.haveInstallation" style="margin-left: 10px">
                        {{ "是" }}
                    </span>
                            <span v-else style="margin-left: 10px">
                        {{ "否" }}
                    </span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="货期（天）"
                            align="center"
                            width="100">
                        <template slot-scope="scope">
                            <span style="margin-left: 10px">{{ scope.row.deliveryCycle }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="直发"
                            align="center"
                            width="160">
                        <template slot-scope="scope">
                            <%--                    <el-radio v-model="scope.row.deliveryDirect" :label="true">是</el-radio>--%>
                            <%--                    <el-radio v-model="scope.row.deliveryDirect" :label="false">否</el-radio>--%>
                            <span v-if="scope.row.deliveryDirect" style="margin-left: 10px">
                        {{ "是" }}
                    </span>
                            <span v-else style="margin-left: 10px">
                        {{ "否" }}
                    </span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="总额"
                            align="center"
                            width="150">
                        <template slot-scope="scope">
                            <%--<span >{{scope.row.total}}</span><br>--%>
                            <span>{{calcTotal(scope.row)}}</span><br>
                        </template>
                    </el-table-column>
                </el-table>
                <span style="color: red;float: right">总{{tableNum}}件,总金额{{tableAmount}}元</span>
            </template>
            <template v-else></template>
            <br>

    <template v-if="businessChanceDetail.type == 391 || businessChanceDetail.type == 394 || businessChanceDetail.type == 1780">
        <el-row class="title-style" style="padding-top: 10px;padding-bottom: 10px;">最新询价记录</el-row>
        <el-table :data="oldChanceList" style="width: 100%; margin-bottom: 20px" border>
            <el-table-column label="序号" width="100" align="center">
                <template slot-scope="scope">{{scope.$index + 1}}</template>
            </el-table-column>
            <el-table-column prop="content" label="询价产品" width="500"></el-table-column>
            <el-table-column prop="goodsName" label="产品名称" width="400"></el-table-column>
            <el-table-column prop="addTime" label="询价时间" width="250">
                <template slot-scope="scope">
                    <span>{{parseTime(scope.row.addTime)}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="goodsCategoryName" label="产品分类" width="200"></el-table-column>
            <el-table-column prop="goodsBrand" label="产品品牌" width="200"></el-table-column>
            <el-table-column label="详情">
                <template slot-scope="scope">
                    <i class="el-icon-document" @click="historyVisible = true; getHistoryChanceInfo(scope.row)"></i>
                </template>
            </el-table-column>
        </el-table>

    </template>


    <el-dialog title="历史商机详情" :visible.sync="historyVisible" width="900px">
        <el-descriptions title="基本信息" border column="2">
            <el-descriptions-item label="商机编号" content-class-name="my-content">
                {{historyChanceInfo.oldChanceNo}}
            </el-descriptions-item>
            <el-descriptions-item label="商机状态">
                <span>{{handleStatus(historyChanceInfo.status).value}}</span>
            </el-descriptions-item>
            <el-descriptions-item label="商机类型">{{historyChanceInfo.typeName}}</el-descriptions-item>
            <el-descriptions-item label="归属销售">{{historyChanceInfo.username}}</el-descriptions-item>

            <el-descriptions-item label="创建者">{{historyChanceInfo.creatorName}}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{parseTime(historyChanceInfo.addTime)}}</el-descriptions-item>
            <el-descriptions-item label="商机时间">{{parseTime(historyChanceInfo.receiveTime)}}</el-descriptions-item>
            <el-descriptions-item label="分配时间">{{parseTime(historyChanceInfo.assignTime)}}</el-descriptions-item>

            <el-descriptions-item label="商机来源">{{historyChanceInfo.sourceName}}</el-descriptions-item>
            <el-descriptions-item label="询价方式">{{historyChanceInfo.communicationName}}</el-descriptions-item>
            <el-descriptions-item label="咨询入口">{{historyChanceInfo.entranceName}}</el-descriptions-item>
            <el-descriptions-item label="功能">{{historyChanceInfo.functionName}}</el-descriptions-item>

            <el-descriptions-item label="客户名称">{{historyChanceInfo.traderName}}</el-descriptions-item>
            <el-descriptions-item label="地区">{{historyChanceInfo.areaStr}}</el-descriptions-item>
            <el-descriptions-item label="联系人">{{historyChanceInfo.traderContactName}}</el-descriptions-item>
            <el-descriptions-item label="手机号">
                <i class="el-icon-phone" style="color: #409eff;" v-if="historyChanceInfo.mobile != null && historyChanceInfo.mobile != ''"
                   @click="call(historyChanceInfo.mobile)"></i>
                {{historyChanceInfo.mobile}}
            </el-descriptions-item>

            <el-descriptions-item label="电话">
                <i class="el-icon-phone" style="color: #409eff;" v-if="historyChanceInfo.telephone != null && historyChanceInfo.telephone != ''"
                   @click="call(historyChanceInfo.telephone)"></i>
                {{historyChanceInfo.telephone}}
            </el-descriptions-item>
            <el-descriptions-item label="其他联系方式">{{historyChanceInfo.otherContact}}</el-descriptions-item>
            <el-descriptions-item label="产品分类">{{historyChanceInfo.goodsCategoryName}}</el-descriptions-item>
            <el-descriptions-item label="产品品牌">{{historyChanceInfo.goodsBrand}}</el-descriptions-item>

            <el-descriptions-item label="产品名称">{{historyChanceInfo.goodsName}}</el-descriptions-item>
            <el-descriptions-item label="附件">
                <a v-if="historyChanceInfo.attachmentUri != null && historyChanceInfo.attachmentUri != ''" :href=" 'http://' + historyChanceInfo.attachmentDomain + historyChanceInfo.attachmentUri" target="_blank">查看</a>
            </el-descriptions-item>
            <el-descriptions-item label="询价产品">{{historyChanceInfo.content}}</el-descriptions-item>
        </el-descriptions>

    </el-dialog>


    <el-descriptions title="客户信息" border column="2">
        <template slot="extra" v-if="businessChanceDetail.traderId == 0">
            <el-button type="text" style="padding: 0; margin-top: 10px; margin-right: 10px"
                       @click="confirmTrader(businessChanceDetail.bussinessChanceId, 0)">确认客户
            </el-button>
        </template>

        <el-descriptions-item label="客户名称" content-class-name="my-content" v-if="businessChanceDetail.traderId != 0">
            <el-link type="primary" @click="viewTraderDetail(businessChanceDetail.traderId)">
                {{businessChanceDetail.checkTraderName}}
            </el-link>
            <i class="el-icon-s-custom" style="color: #00a0e9; margin-left: 20px"
               @click="traderPropertyVisible = true; getTraderInfoFromCrm(businessChanceDetail.traderId)"></i>
        </el-descriptions-item>
        <el-descriptions-item label="客户名称" content-class-name="my-content" v-if="businessChanceDetail.traderId == 0">
            {{businessChanceDetail.traderName}}
        </el-descriptions-item>
        <el-descriptions-item label="客户类型&客户性质">{{businessChanceDetail.customerTypeName}}&nbsp;{{businessChanceDetail.customerNatureName}}</el-descriptions-item>
        <el-descriptions-item label="联系人">{{businessChanceDetail.traderContactName}}</el-descriptions-item>
        <el-descriptions-item label="联系人情况">{{businessChanceDetail.traderContactInfo}}</el-descriptions-item>
        <el-descriptions-item label="手机"> <i class="el-icon-phone" style="color: #409eff;" v-if="businessChanceDetail.mobile != null && businessChanceDetail.mobile != ''"
                                             @click="call(businessChanceDetail.mobile)"></i>{{businessChanceDetail.mobile}}</el-descriptions-item>
        <el-descriptions-item label="电话"> <i class="el-icon-phone" style="color: #409eff;" v-if="businessChanceDetail.telephone != null && businessChanceDetail.telephone != ''"
                                             @click="call(businessChanceDetail.telephone)"></i>{{businessChanceDetail.telephone}}</el-descriptions-item>
        <el-descriptions-item label="地区">{{businessChanceDetail.areaStr}}</el-descriptions-item>
        <el-descriptions-item label="联系地址">{{businessChanceDetail.address}}</el-descriptions-item>
        <el-descriptions-item label="终端名称">{{businessChanceDetail.terminalTraderName}}</el-descriptions-item>
        <el-descriptions-item label=""></el-descriptions-item>
    </el-descriptions>


    <el-dialog title="客户属性" :visible.sync="traderPropertyVisible" width="900px">

        <el-row>
            <div style="border: 1px solid #d7dae2;border-radius: 0px">
                <el-row>
                    <div style="margin-top: 20px;padding-left: 20px">交易信息</div>
                    <el-divider></el-divider>
                    <el-descriptions>
                        <el-descriptions-item label="历史交易总额">{{crmTraderInfo.totalAmount}}</el-descriptions-item>
                        <el-descriptions-item label="历史交易次数">{{crmTraderInfo.totalBuyNum}}</el-descriptions-item>
                        <el-descriptions-item label="订单平均金额">{{crmTraderInfo.averageAmount}}</el-descriptions-item>
                        <el-descriptions-item label="线上订单数量">{{crmTraderInfo.onlineOrderNum}}</el-descriptions-item>
                        <el-descriptions-item label="线下订单数量">{{crmTraderInfo.offlineOrderNum}}</el-descriptions-item>
                        <el-descriptions-item label="线上订单比例">{{crmTraderInfo.onlineOrderRate}}</el-descriptions-item>
                        <el-descriptions-item label="初次交易时间">{{crmTraderInfo.firstBusinessTime}}</el-descriptions-item>
                        <el-descriptions-item label="最近交易时间">{{crmTraderInfo.lastBusinessTime}}</el-descriptions-item>
                        <el-descriptions-item label="消费频次">{{crmTraderInfo.consumptionFrequency}}</el-descriptions-item>
                    </el-descriptions>
                    <el-descriptions>
                        <el-descriptions-item label="经营品牌">{{crmTraderInfo.brands}}</el-descriptions-item>
                    </el-descriptions>
                    <el-descriptions>
                        <el-descriptions-item label="经营分类">{{crmTraderInfo.classification}}</el-descriptions-item>
                    </el-descriptions>
                    <el-descriptions>
                        <el-descriptions-item label="经营科室">{{crmTraderInfo.department}}</el-descriptions-item>
                    </el-descriptions>
                </el-row>
            </div>
        </el-row>

        <el-row>
            <div style="border: 1px solid #d7dae2;border-radius: 0">
                <el-row>
                    <div style="margin-top: 20px;padding-left: 20px">中标信息</div>
                    <el-divider></el-divider>

                    <el-table
                            :data="bidData"
                            style="width: 100%"
                            row-key="infoId"
                            border
                            lazy
                            :tree-props="{children: 'otherGoodsInfo', hasChildren: 'hasChildren'}">
                        <el-table-column label="招标信息" width="120">
                            <el-table-column
                                    label="招标单位"
                                    prop="zhaobiaoUnit">
                            </el-table-column>
                            <el-table-column
                                    label="地区"
                                    prop="zhaobiaoProvinceName">
                            </el-table-column>
                            <el-table-column
                                    label="单位性质"
                                    prop="zhaobiaoXingzhi">
                            </el-table-column>
                            <el-table-column
                                    label="单位等级"
                                    prop="zhaobiaoLevel">
                            </el-table-column>
                            <el-table-column
                                    label="单位类型"
                                    prop="zhaobiaoType">
                            </el-table-column>
                        </el-table-column>

                        <el-table-column
                                label="中标商品"
                                width="120">
                            <el-table-column
                                    label="商品"
                                    prop="zhongbiaoGoods">
                            </el-table-column>
                            <el-table-column
                                    label="品牌"
                                    prop="zhongbiaoBrand">
                            </el-table-column>
                            <el-table-column
                                    label="型号"
                                    prop="zhongbiaoModel">
                            </el-table-column>
                        </el-table-column>

                        <el-table-column
                                prop="name"
                                label="操作"
                                width="120">
                            <template slot-scope="scope">
                                <el-button v-if="scope.row.zhaobiaoUnit" type="text" size="small" @click="viewZhaoBiao(scope.row.infoId)">查看招投标</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-row>
            </div>
        </el-row>


    </el-dialog>


    <el-descriptions title="商机信息" border column="2">
        <el-descriptions-item label="商机阶段" content-class-name="my-content">{{businessChanceDetail.businessStageName}}</el-descriptions-item>
        <el-descriptions-item label="成单几率">{{businessChanceDetail.systemOrderRateStr}}</el-descriptions-item>
        <el-descriptions-item label="询价类型">{{businessChanceDetail.enquiryTypeName}}</el-descriptions-item>
        <el-descriptions-item label="采购方式">{{businessChanceDetail.purchasingTypeName}}</el-descriptions-item>
        <el-descriptions-item label="采购时间">{{businessChanceDetail.purchasingTimeName}}</el-descriptions-item>
        <el-descriptions-item label="预计金额">{{businessChanceDetail.amount}}</el-descriptions-item>
        <el-descriptions-item label="竞争对手信息">{{businessChanceDetail.competitorInformation}}</el-descriptions-item>
        <el-descriptions-item label="商机标签">
            <el-tag v-for="item in businessChanceDetail.tags"
                    :key="item.id"
                    :style="{color: item.cssClass,borderColor:item.cssClass}"
                    style="margin-right: 5px"
                    effect="plain">
                {{ item.name }}
            </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="预计成单时间">{{businessChanceDetail.orderTimeDate}}</el-descriptions-item>
        <el-descriptions-item label=""></el-descriptions-item>
    </el-descriptions>

    <el-descriptions title="报价信息" border column="2"
                     v-if="businessChanceDetail.status == 1 || businessChanceDetail.status == 2">
        <el-descriptions-item label="报价单号" content-class-name="my-content">
            <el-link type="primary" @click="viewQuoteOrder(quoteorderDto.quoteorderId, quoteorderDto.validStatus)">
                {{quoteorderDto.quoteorderNo}}
            </el-link>
        </el-descriptions-item>
        <el-descriptions-item label="报价时间">{{parseTime(quoteorderDto.addTime)}}</el-descriptions-item>
        <el-descriptions-item label="报价金额">{{quoteorderDto.totalAmount}}</el-descriptions-item>
        <el-descriptions-item label="报价单状态">
            {{getQuoteOrderStatus(quoteorderDto.followOrderStatus)}}
        </el-descriptions-item>
    </el-descriptions>

    <el-descriptions title="订单信息" border column="2"
                     v-if="businessChanceDetail.status == 3 || businessChanceDetail.status == 7">
        <el-descriptions-item label="订单号">
            <el-link type="primary" @click="viewSaleOrder(saleorderInfoDto.saleorderId)">
                {{saleorderInfoDto.saleorderNo}}
            </el-link>
        </el-descriptions-item>
        <el-descriptions-item label="订单时间">{{parseTime(saleorderInfoDto.addTime)}}</el-descriptions-item>
        <el-descriptions-item label="订单金额">{{saleorderInfoDto.totalAmount}}</el-descriptions-item>
        <el-descriptions-item label="订单状态">
            {{getSaleOrderStatus(saleorderInfoDto.status)}}
        </el-descriptions-item>
    </el-descriptions>

    <template>
        <el-row class="title-style">沟通记录<span style="margin-left: 94%"><el-button type="text" @click="addCommunicateRecord">新增</el-button></span>
        </el-row>
        <el-table :data="communicationRecords" style="width: 100%; margin-bottom: 20px" border>
            <el-table-column prop="begintime" label="沟通时间" width="180">
                <template slot-scope="scope">
                    <span>{{parseTime(scope.row.begintime)}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="communicateRecordId" label="录音" width="130">
                <template slot-scope="scope">
                    <span v-if="scope.row.coidUri != null && scope.row.coidUri != ''">
                        {{scope.row.communicateRecordId}}
                        <img @click="playrecord(scope.row.coidUri)" src="${pageContext.request.contextPath}/static/vue/images/trumpet.png" style="height: 20px">
                    </span>
                </template>
            </el-table-column>
            <el-table-column label="联系人" width="180">
                <template slot-scope="scope">
                    <span v-if="scope.row.traderContactId == null || scope.row.traderContactId == 0">{{scope.row.contact}}</span>
                    <span v-else>{{scope.row.contactName}}</span>
                </template>
            </el-table-column>
            <el-table-column label="联系方式" width="180" >
                <template slot-scope="scope">
                    <span v-if="scope.row.traderContactId == null || scope.row.traderContactId == 0">{{scope.row.contactMob}}</span>
                    <span v-else>{{scope.row.phone}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="contentSuffix" label="沟通内容（AI分析整理）" width="250">
                <template slot-scope="scope">
                    <el-tag
                            v-for="item in scope.row.allTag"
                            :key="item.tagId"
                            style="margin-right: 8px;cursor : pointer"
                            effect="light">
                        {{ item.tagName }}
                    </el-tag>
                    <br>
                    <div style="white-space: pre-wrap;text-align: left;">{{ scope.row.contentSuffix }}</div>
                </template>

            </el-table-column>
<%--            <el-table-column prop="communicateAiSummaryApiDto" label="沟通内容摘要（AI分析整理）"  width="250" >--%>
<%--                <template slot-scope="scope">--%>
<%--                    <div v-if="scope.row.communicateAiSummaryApiDto" class="container multiline-ellipsis" :title=--%>
<%--                            "'客户意图:'+scope.row.communicateAiSummaryApiDto.customerIntentions--%>
<%--                            +'\n意向商品:'+scope.row.communicateAiSummaryApiDto.intentionGoods--%>
<%--                            +'\n品牌:'+scope.row.communicateAiSummaryApiDto.brands--%>
<%--                            +'\n型号:'+scope.row.communicateAiSummaryApiDto.models--%>
<%--                            +'\n客户类型:'+scope.row.communicateAiSummaryApiDto.customerTypes--%>
<%--                            +'\n是否有意向:'+scope.row.communicateAiSummaryApiDto.isIntention--%>
<%--                            +'\n是否加微信:'+scope.row.communicateAiSummaryApiDto.isAddWechat--%>
<%--                            ">--%>
<%--                            <el-tag size="mini">客户意图:</el-tag>--%>
<%--                            {{scope.row.communicateAiSummaryApiDto.customerIntentions}}<br/>--%>
<%--                            <el-tag size="mini">意向商品:</el-tag>--%>
<%--                            {{scope.row.communicateAiSummaryApiDto.intentionGoods}}<br/>--%>
<%--                            <el-tag size="mini">品牌:</el-tag>--%>
<%--                            {{scope.row.communicateAiSummaryApiDto.brands}}<br/>--%>
<%--                            <el-tag size="mini">型号:</el-tag>--%>
<%--                            {{scope.row.communicateAiSummaryApiDto.models}}<br/>--%>
<%--                            <el-tag size="mini">客户类型:</el-tag>--%>
<%--                            {{scope.row.communicateAiSummaryApiDto.customerTypes}}<br/>--%>
<%--                            <el-tag size="mini">是否有意向:</el-tag>--%>
<%--                            {{scope.row.communicateAiSummaryApiDto.isIntention}}<br/>--%>
<%--                            <el-tag size="mini">是否加微信:</el-tag>--%>
<%--                            {{scope.row.communicateAiSummaryApiDto.isAddWechat}}--%>
<%--                </template>--%>
<%--            </el-table-column>--%>

            <el-table-column prop="creatorName" label="操作人" width="180"></el-table-column>
            <el-table-column prop="nextContactDate" label="下次沟通时间" width="180">
                <template slot-scope="scope">
                    <span>{{parseTime(scope.row.nextContactDate, '{y}-{m}-{d}')}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="nextContactContent" label="下次沟通内容"></el-table-column>
            <el-table-column prop="comments" label="备注" width="180"></el-table-column>
            <el-table-column prop="addTime" label="创建时间">
                <template slot-scope="scope">
                    <span>{{parseTime(scope.row.addTime)}}</span>
                </template>
            </el-table-column>
            <el-table-column label="操作">
                <template slot-scope="scope">
                     <el-button  v-if="scope.row.coidUri != null && scope.row.coidUri != ''" type="text" @click="window.top.showAiHelpme(scope.row.communicateRecordId)" >查看</el-button>
                     <el-button type="text" @click="editCommunicationData(scope.row)" disabled>编辑</el-button>
                </template>
            </el-table-column>
        </el-table>
    </template>

    <template>
        <el-row class="title-style" style="padding-top: 10px;padding-bottom: 10px;">审核记录</el-row>
        <el-table :data="historicInfo" style="width: 100%; margin-bottom: 20px" border>
            <el-table-column prop="operator" label="操作人" width="300"></el-table-column>
            <el-table-column prop="operateTime" label="操作时间" width="300"></el-table-column>
            <el-table-column prop="operationalMatters" label="操作事项" width="400"></el-table-column>
            <el-table-column prop="comments" label="备注"></el-table-column>
        </el-table>
    </template>

    <!--支持记录楼层-->
    <template>
        <el-row type="flex" class="row-bg" justify="space-between">
            <el-col :span="8" class="row-l"><span class="text-a">支持记录</span><span class="text-b">(添加支持后会自动关注)</span></el-col>
            <el-col :span="2" class="row-r">
                <el-tooltip v-if="addSupportDisabled" class="item" effect="dark" content="已成单、已关闭商机，无法咨询方案" placement="top-end">
                    <el-button type="text" v-if="isBelongSale" :disabled="addSupportDisabled" @click="addRequestClick">咨询方案</el-button>
                </el-tooltip>
                <el-button type="text" v-if="isBelongSale && !addSupportDisabled" @click="addRequestClick">咨询方案</el-button>

                <el-tooltip v-if="addSupportDisabled" class="item" effect="dark" content="已成单、已关闭商机，无法添加支持记录" placement="top-end">
                    <el-button type="text" v-if="!isBelongSale" :disabled="addSupportDisabled" @click="addSupportClick">添加支持记录</el-button>
                </el-tooltip>
                <el-button type="text" v-if="!isBelongSale && !addSupportDisabled" @click="addSupportClick">添加支持记录</el-button>
            </el-col>
        </el-row>

        <el-table :data="supportRecords" style="width: 100%; margin-bottom: 20px" border>
            <el-table-column
                    prop="supportTime"
                    label="支持时间"
                    width="180">
            </el-table-column>
            <el-table-column
                    prop="supportUserName"
                    label="支持人"
                    width="130">
            </el-table-column>
            <el-table-column
                    prop="department"
                    label="所属部门"
                    width="180">
            </el-table-column>
            <el-table-column
                    prop="businessLevel"
                    label="评估等级"
                    width="90" >
            </el-table-column>
            <el-table-column
                    prop="amount"
                    label="评估成交金额（元）"
                    width="180" >
            </el-table-column>
            <el-table-column
                    prop="orderDate"
                    label="评估成交时间"
                    width="110" >
            </el-table-column>
            <el-table-column
                    prop="nextSupportDate"
                    label="下次支持时间"
                    width="110" >
            </el-table-column>
            <el-table-column
                    prop="content"
                    label="支持内容"
                    show-overflow-tooltip="true"
                     >
            </el-table-column>
        </el-table>
    </template>

    <el-descriptions title="关闭商机" border column="2" v-if="businessChanceDetail.status == 4">
        <el-descriptions-item label="关闭原因" content-class-name="my-content">{{businessChanceDetail.closeReason}}&nbsp;{{businessChanceDetail.cancelReasonStr}}&nbsp;{{businessChanceDetail.otherReason}}</el-descriptions-item>
        <el-descriptions-item label="关闭时间">{{parseTime(businessChanceDetail.modTime)}}
        </el-descriptions-item>
        <el-descriptions-item label="备注">{{businessChanceDetail.comments}}</el-descriptions-item>
        <el-descriptions-item label="申请关闭备注">{{businessChanceDetail.closedComments}}</el-descriptions-item>
    </el-descriptions>

    <template>
            <el-row class="title-style" style="padding-top: 10px;padding-bottom: 10px;">分享记录</el-row>
            <el-table
                    :data="salesShareList"
                    border
                    :cell-style="{'text-align':'center'}"
                    :header-cell-style="{'text-align':'center'}"
                    style="width: 100%">
                <el-table-column
                        prop="saleUserName"
                        label="接收人英文"
                        width="180">
                </el-table-column>
                <el-table-column
                        prop="saleUserCnName"
                        label="接收人中文"
                        width="180">
                </el-table-column>
                <el-table-column
                        prop="addTime"
                        label="分享时间">
                    <template slot-scope="scope">
                        <span>{{parseTime(scope.row.addTime)}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="creatorName"
                        label="操作人英文"
                        width="180">
                </el-table-column>
                <el-table-column
                        prop="creatorCnName"
                        label="操作人中文"
                        width="180">
                </el-table-column>
                <el-table-column
                        label="操作">
                    <template slot-scope="scope">
                        <el-button v-if="isBelongSale" type="text" @click="cancelShare(scope.row)">取消分享</el-button>
                        <el-popover
                                v-if="!isBelongSale"
                                placement="top-start"
                                width="200"
                                trigger="hover"
                                :content="tipMessageShare">
                            <el-button type="text" slot="reference" disabled>取消分享</el-button>
                        </el-popover>

                    </template>
                </el-table-column>
            </el-table>
        </template>

    <el-dialog title="分享商机" :visible.sync="shareDialogVisible" width="30%" :show-close="false"
               :close-on-click-modal="false" :close-on-press-escape="false" @close="cancelSaveShare()">
        <el-select
                v-model="shareData.saleUserId"
                @change="changeSaleUserId()"
                filterable
                remote
                reserve-keyword
                placeholder="输入销售英文/中文全名搜索"
                :remote-method="getSales"
                style="width: 100%"
                :loading="getSalesLoading">
            <el-option
                    v-for="item in salesOptions"
                    :key="item.userId"
                    :label="item.username + '/' + item.realName"
                    :value="item.userId">
            </el-option>
        </el-select>
        <el-row v-if="showError" style="color: red">
            <span>该商机已分享过了</span>
        </el-row>
        <div slot="footer" class="dialog-footer" style="text-align: center">
            <el-button type="primary" @click="shareBusinessChance()">确 认</el-button>
            <el-button @click="shareDialogVisible = false">取 消</el-button>
        </div>
    </el-dialog>
</div>

<script src="${pageContext.request.contextPath}/static/api/trader/traderCustomer.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/api/trader/businessChance.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript">

    const viewInfo = {
        businessChanceId: '${bussinessChanceId}',
        belongPlatfromByOrgAndUser: '${belongPlatfromByOrgAndUser}'
    };

    let vm0 = null
    const sendThis0 = (_this) => {
        vm0 = _this;
    };
    const validatorBigDecimal = (rule, value, callback, source, options) => {
        if (value === "") {
            callback(new Error("仅数字，最多10位"));
            return;
        }
        if (value !== "" && !(/^\d{1,10}(\.\d{1,2})?$/.test(value) || /^\d$/.test(value))) {
            debugger
            callback(new Error("仅数字，最多10位"));
        } else {
            callback();
        }
    };

    new Vue({
        el: '#app',
        data() {
            var dateTime = new Date().getTime();
            var dateTimeEnd = new Date(dateTime + 1000 * 60 * 2).getTime();
            return {
                pickerOptions:{
                    disabledDate(time) {
                        if (time.getTime() < Date.now() - 8.64e7) {
                            return true;
                        }
                    }
                },
                autoAuditForm: {
                    status: '',
                    remark: ''
                },

                rules: {
                    'nextContactDate': [
                        {required: true, message: '请输入下次沟通时间', trigger: 'change'}
                    ],
                    'time': [
                        {type: 'array', required: true, message: '请输入沟通时间', trigger: 'change'}
                    ],
                    'businessChanceAccuracy': [
                        { validator: this.validatebusinessChanceAccuracy, trigger: 'blur'},
                    ],
                    'traderContactNameView': [
                        {required: true, validator: this.validateNameView,trigger: 'blur'},
                    ],
                    'traderContactNameNew': [
                        {required: true, validator: this.validateName, trigger: 'blur'},
                    ],
                    traderContactMobile: [
                        { validator: this.validatePhone, trigger: 'blur' }
                    ],
                    orderDate: [
                        { type:'date',required:true, message:'请输入评估成交时间', trigger: 'blur'}
                    ],
                    businessLevel:[
                        { required: true, message: '请选择评估等级', trigger: 'blur' }
                    ],
                    amount: [
                        { required: true, message: '仅数字，最多10位', trigger: 'blur',validator:validatorBigDecimal }
                    ],
                    content: [
                        { required: true, message: '请输入支持内容', trigger: 'blur' }
                    ],
                    requestContent: [
                        { required: true, message: '请输入咨询内容', trigger: 'blur' }
                    ],
                    status:[
                        { required: true, message: '请选择审核状态', trigger: 'blur' }
                    ]
                },

                commTitle: '新增沟通记录',
                addSupportTitle:'添加支持记录',
                addRequestTitle:'咨询方案',
                addSupportDialogVisible: false,
                addRequestDialogVisible: false,
                addSupportDisabled: false,
                saveAddSupportLoading: false,
                saveAddRequestLoading: false,
                disabledForm: false,
                // 输入的值暂存区
                inputTagValue: '',
                // 新增沟通记录 begin
                dialogVisible: false,
                historyVisible: false,
                traderPropertyVisible: false,
                closeLoading: false,
                oldChanceList: [],

                // 进度条当前高亮的节点
                activeNode: 0,

                traderContactNameView: '',
                traderContactNameNew: '',
                traderContactMobile: '',
                traderConcatDatas: [],
                businessChanceAccuracys: [],
                bindCommunicationData: [],
                // 选中
                sysTagUserCheck: [],
                // 新增沟通记录 end
                sysTag: [],
                // 显示隐藏
                showTag: false,
                // 是否输入框
                inputVisible_tag: false,

                businessChanceId: '',
                belongPlatfromByOrgAndUser: '',
                communicateRecordDto: {
                    relatedId: '',
                    time: [dateTime, dateTimeEnd],
                    noneNextDate: false,
                    nextContactDate: null,
                    contact: null,
                    businessChanceAccuracy: '',
                    contactMob: null,
                    contentSuffix: null,
                    sysTag: [],
                    userTag: [],
                    traderContactMobile: '',
                    traderContactNameNew: '',
                    traderContactNameView: ''
                },
                addRequestDto: {
                    requestContent: ''
                },
                addSupportDto: {
                    businessLevel: '',
                    amount: '',
                    orderDate: null,
                    nextSupportDate: null,
                    content: ''
                },
                addSupportEntity: {
                    businessLevel: '',
                    amount: '',
                    orderDate: null,
                    nextSupportDate: null,
                    content: ''
                },

                communicateRecordEntity: {
                    time: [dateTime, dateTimeEnd],
                    noneNextDate: false,
                    nextContactDate: null,
                    contact: null,
                    contactMob: null,
                    contentSuffix: null,
                    sysTag: [],
                    userTag: [],
                    traderContactNameView: ''
                },

                bidData: [],
                crmTraderInfo: {},

                businessChanceDetail: {},
                businessChanceDto: {
                    isPolicymaker: null,
                    other: null,
                    traderId: null,
                    checkTraderArea: null,
                    traderName: null,
                    traderNameShow: '',
                    completion: null,
                    systemOrderRate: null,
                    systemBusinessLevel: null,
                    bussinessLevel: null,
                    traderContactId: null,
                    traderContactName: null,
                    terminalTraderName: null,
                    terminalTraderType: null,
                    mobile: null,
                    telephone: null,
                    type: null,
                    resource: null,
                    checkTraderArea: '-',
                    businessChanceGoodsDtos: [],
                    tenderTime: '',
                    enquiryType: null,
                    purchasingType: null,
                    checkedCities: [],
                    communicateRecordDto: {
                        traderContactNameView: null,
                        time: [dateTime, dateTimeEnd],
                        noneNextDate: false,
                        contact: null,
                        contactMob: null,
                        contentSuffix: null,
                        sysTag: [],
                        userTag: [],
                        nextContactDate: null
                    }
                },
                tableAmount: 0,
                tableNum: 0,
                historyChanceInfo: {},
                quoteorderDto: {},
                saleorderInfoDto: {},

                // 沟通记录查询参数
                recordParam: {
                    param: {
                        companyId: 1,
                        relatedId: viewInfo.businessChanceId,
                        communicateType: 244,
                    }, pageSize: 1000
                    ,orderBy: 'COMMUNICATE_RECORD_ID desc'
                },
                supportParam:{},
                supportRecords:[],
                // 沟通记录列表值
                communicationRecords: [],

                // 审核记录
                historicInfo: [],
                taskId: 0,
                endStatusFlag: true,
                currentUserAudit: false,
                productComments: '',
                productCommentsSale: '',
                shareDialogVisible: false,
                closeAuditVisible: false,
                salesOptions:[],
                getSalesLoading: false,
                salesShareList: [],
                isBelongSale: false,
                tipMessageShare: '',
                showError: false,
                shareData : {
                    "businessId": '',
                    "businessNo": '',
                    "saleUserId": '',
                    "saleUserName": ''
                },
                isPopoverVisible: false,
                // 支持记录相关
                filterText : '',
                data: [],
                defaultProps: {
                    children: 'children',
                    label: 'label'
                },
                levelOptions: [{
                    value: 'S',
                    label: 'S'
                }, {
                    value: 'A',
                    label: 'A'
                }, {
                    value: 'B',
                    label: 'B'
                }, {
                    value: 'C',
                    label: 'C'
                }],
                levelValue: ''
            }
        },

        watch: {
            filterText(val) {
                this.$refs.addRequestTree.filter(val);
            }
        },

        created() {
            this.businessChanceId = viewInfo.businessChanceId;
            this.belongPlatfromByOrgAndUser = viewInfo.belongPlatfromByOrgAndUser;
            getBusinessChanceDetail({'businessChanceId': this.businessChanceId}).then((result) => {
                this.businessChanceDetail = result.data.data;
                this.businessChanceDto = result.data.data;
                this.saleorderInfoDto = this.businessChanceDetail.saleorderInfoDto;
                this.quoteorderDto = this.businessChanceDetail.quoteorderDto;
                this.productComments = this.businessChanceDetail.productComments;
                this.productCommentsSale = this.businessChanceDetail.productCommentsSale;
                this.type = this.businessChanceDetail.type;
                this.traderId = this.businessChanceDetail.traderId;
                this.addSupportDisabled = [4,7].includes(this.businessChanceDetail.status);
                switch (this.businessChanceDetail.status) {
                    case 0 :
                        this.activeNode = 0;
                        break;
                    case 1:
                        this.activeNode = 2;
                        break;
                    case 2:
                        this.activeNode = 3;
                        break;
                    case 3:
                        this.activeNode = 4;
                        break;
                    case 4:
                        this.activeNode = 6;
                        break;
                    case 5:
                        this.activeNode = 0;
                        break;
                    case 6:
                        this.activeNode = 1;
                        break;
                    case 7:
                        this.activeNode = 5;
                        break;
                }


                let param = {'businessChanceNo': this.businessChanceDetail.bussinessChanceNo};
                getMergedChances(param).then((result) => {
                    this.oldChanceList = result.data.data;
                });

                if (this.businessChanceDetail.traderId == null || this.businessChanceDetail.traderId == 0) {
                    var array = [];
                    if (this.businessChanceDetail.traderContactName != null
                        && this.businessChanceDetail.traderContactName !== ''
                        && this.businessChanceDetail.mobile != null
                        && this.businessChanceDetail.mobile !== '') {
                        let mob = {
                            id: 1,
                            traderContactName: this.businessChanceDetail.traderContactName,
                            type: this.businessChanceDetail.mobile,

                        };
                        array.push(mob)
                    }
                    if (this.businessChanceDetail.traderContactName != null
                        && this.businessChanceDetail.traderContactName !== ''
                        && this.businessChanceDetail.telephone != null
                        && this.businessChanceDetail.telephone !== '') {
                        let data = {
                            id: 2,
                            traderContactName: this.businessChanceDetail.traderContactName,
                            type: this.businessChanceDetail.telephone

                        };
                        array.push(data)
                    }
                    this.bindCommunicationData = array;
                }

                this.isBelongSale = this.businessChanceDetail.userId == this.businessChanceDetail.currentUserId
                this.tipMessageShare = '该商机归属不是您，请联系' + this.businessChanceDetail.username + '进行操作';
            });

            // 沟通记录
            getCommunicationRecords(this.recordParam).then((result) => {
                this.communicationRecords = result.data.data.list;
            });

            // 支持记录
            this.supportParam.businessChanceId = this.businessChanceId;
            getSupportRecordList(this.supportParam).then((result) => {
                this.supportRecords = result.data.data;
            });

            getCloseVerifyInfo({'businessChanceId': this.businessChanceId}).then((result) => {
                this.historicInfo = result.data.data;
                if (this.historicInfo.length > 0) {
                    this.taskId = this.historicInfo[0].taskId;
                    this.endStatusFlag = this.historicInfo[0].endStatusFlag;
                    this.currentUserAudit = this.historicInfo[0].currentUserAudit;
                }
            });

            getCommunicationTag().then(result => {
                this.sysTag = result.data.data;
            });

            this.getShareBusinessChance()
            sendThis0(this);

        },

        mounted() {
            loadingApp()
            sendThis(this);
        },

        methods: {
            submitForm( autoAuditForm ){
                this.$refs['autoAuditForm'].validate((valid) => {
                    if (valid) {
                        let data = {
                            'remark': this.autoAuditForm.remark,
                            'pass': this.autoAuditForm.status,
                            'taskId': this.taskId
                        };
                        if(this.autoAuditForm.status == 'false' && this.autoAuditForm.remark == ''){
                            this.$message({
                                type: 'error',
                                message: '审核不通过时，备注为必填项'
                            });
                            return;
                        }
                        this.closeLoading = true;
                        bussinessChanceCloseComplementTask(data).then(res => {
                            if (res.data.code == 0) {
                                this.$message({
                                    message: res.data.message,
                                    type: 'success'
                                });
                            }else{
                                this.$message.error(res.data.message);
                            }

                            this.closeAuditVisible = false;
                            this.autoAuditForm.status = '';
                            this.autoAuditForm.remark = '';
                            this.closeLoading = false;
                            window.location.reload();

                        });
                    }
                });

            },
            cancelAuditForm(){
                this.closeAuditVisible = false;
                this.autoAuditForm.status = '';
                this.autoAuditForm.remark = '';
            },
            calcTotal(row) {
                if (row.num == null || row.price == null) {
                    row.total = '';
                } else {
                    row.total = Number(row.num * row.price).toFixed(2);
                }
                this.tableNum = this.calcTableNum();
                this.tableAmount = this.calcTableAmount();
                return row.total;
            },
            calcTableNum() {

                let total = 0;
                if (this.businessChanceDto.businessChanceGoodsDtos.length > 0) {
                    this.businessChanceDto.businessChanceGoodsDtos.map(item => {
                        if (item.num != null) {
                            total += Number(item.num);
                        }
                    })
                } else {
                    return total;
                }
                return total;

            },
            validateNameView(rule, value, callback) {
                if (this.traderId === 0) {
                    callback();
                    return;
                }
                 if( value ==="" || value === undefined || value === null || value.length === 0)  {
                    callback(new Error('请选择联系人'));
                }else {
                    callback();
                }
            },
            validateName(rule, value, callback) {
                if (this.traderId !== 0) {
                    callback();
                    return;
                }
                if (value ==="" || value === undefined || value === null || value.length === 0) {
                    callback(new Error('联系人不能为空'));
                }else if(value.length > 20)  {
                    callback(new Error('联系人姓名不能超过20个字符'));
                }else {
                    callback();
                }
            },
            validatePhone(rule, value, callback) {
                if (this.traderId !== 0) {
                    callback();
                    return;
                }
                var phoneReg = /(^1[3|4|5|6|7|8|9]\d{9}$)|(^09\d{8}$)/;
                if (!phoneReg.test(value)) {
                    callback(new Error('请输入正确的电话号码'));
                } else {
                    callback();
                }
            },
            validatebusinessChanceAccuracy(rule, value, callback) {
                if ( !(this.type  == 394 || this.type == 391)) {
                    callback();
                    return;
                }
                if(value === null || value === '' || value === undefined ) {
                    callback(new Error('商机精准度不能为空'));
                } else {
                    callback();
                }
            },
            calcTableAmount() {

                let total = 0;
                if (this.businessChanceDto.businessChanceGoodsDtos.length > 0) {
                    this.businessChanceDto.businessChanceGoodsDtos.map(item => {
                        total += Number(item.total);

                    })
                } else {
                    return Number(total).toFixed(2);
                }
                return Number(total).toFixed(2);

            },
            handleStatus(status) {
                let chanceStatus = {
                    'value': '',
                };
                switch (status) {
                    case 0:
                        chanceStatus.value = '未处理';
                        break;
                    case 1:
                        chanceStatus.value = '报价中';
                        break;
                    case 2:
                        chanceStatus.value = '已报价';
                        break;
                    case 3:
                        chanceStatus.value = '已订单';
                        break;
                    case 4:
                        chanceStatus.value = '已关闭';
                        break;
                    case 5:
                        chanceStatus.value = '未分配';
                        break;
                    case 6:
                        chanceStatus.value = '处理中';
                        break;
                    case 7:
                        chanceStatus.value = '已成单';
                        break;
                    default:
                }
                return chanceStatus
            },

            viewZhaoBiao(infoId){
                openTab("查看招投标", 'https://www.vedeng.com/zhaobiao/detail-' + infoId + '.html')
            },
            editCommunicationData(data) {
                this.commTitle = '编辑沟通记录';
                let comm = {...data};
                comm.time = this.communicateRecordDto.time;
                comm.time[0] = new Date(comm.begintime).getTime();
                comm.time[1] = new Date(comm.begintime).getTime();

                if (comm.noneNextDate == 1) {
                    comm.noneNextDate = true;
                } else {
                    comm.noneNextDate = false;
                }
                if (data.traderContactId == null || data.traderContactId == 0) {
                    comm.traderContactNameView = data.contact + ' ' + data.contactMob;
                } else {
                    comm.traderContactNameView = data.contactName + ' ' + data.phone;
                    comm.contactName = null;
                    comm.phone = null;
                }
                if (comm.sysTagUserCheck == null) {
                    this.sysTagUserCheck = [];
                } else {
                    this.sysTagUserCheck = JSON.parse(JSON.stringify(comm.sysTagUserCheck));
                }

                if (comm.sysTag == null) {
                    comm.sysTag = [];
                }
                if (comm.userTag == null) {
                    comm.userTag = [];
                }
                this.communicateRecordDto = comm;
                this.communicateRecordNextBind();

                getAccuracyEnum().then(res => {
                    this.businessChanceAccuracys = res.data.data;
                });
                this.communicateRecordDto.businessChanceAccuracy = this.businessChanceDetail.businessChanceAccuracy;



                // 查询联系人下拉框的值
                this.communicateRecordDto.relatedId = viewInfo.businessChanceId;
                if (this.businessChanceDetail.traderId !== 0) {
                    this.communicateRecordDto.traderId = this.businessChanceDetail.traderId;
                    getTraderContactDat(
                        {
                            "param": {"traderId": this.businessChanceDetail.traderId},
                            "orderBy": 'TRADER_CONTACT_ID desc',
                            "pageSize": 1000
                        }
                    ).then(res => {
                        this.traderConcatDatas = res.data.data.list;
                    });
                } else {
                    var array = [];
                    if (this.businessChanceDetail.traderContactName != null
                        && this.businessChanceDetail.traderContactName !== ''
                        && this.businessChanceDetail.phone != null
                        && this.businessChanceDetail.phone !== '') {
                        let mob = {
                            traderContactId: 1,
                            name: this.businessChanceDetail.traderContactName,
                            mobile: this.businessChanceDetail.phone,
                        };
                        array.push(mob)
                    }
                    if (this.businessChanceDetail.traderContactName != null
                        && this.businessChanceDetail.traderContactName !== ''
                        && this.businessChanceDetail.telephone != null
                        && this.businessChanceDetail.telephone !== '') {
                        let data = {
                            traderContactId: 2,
                            name: this.businessChanceDetail.traderContactName,
                            mobile: this.businessChanceDetail.telephone
                        };
                        array.push(data)
                    }
                    this.traderConcatDatas = array;
                }

                this.dialogVisible = true;

            },
            businessChanceAccuracySelectComm(item) {
                this.communicateRecordDto.businessChanceAccuracy = item.businessChanceAccuracy;
                this.$forceUpdate();
            },
            traderConcatSelectComm(item) {
                this.communicateRecordDto.traderContactNameView = item.name + " " + item.mobile + " " + item.telephone
                this.communicateRecordDto.traderContactId = item.traderContactId
            },
            traderConcatSelectCommNoTraderId(item) {
                this.communicateRecordDto.traderContactNameView = item.traderContactName + " " + item.type
                this.communicateRecordDto.contact = item.traderContactName
                this.communicateRecordDto.contactMob = item.type
            },
            saveCommunication() {
                this.$refs['communicateRecordForm'].validate((valid) => {
                    if (valid) {
                        this.disabledForm = true;
                        let data = JSON.parse(JSON.stringify(this.communicateRecordDto));
                        data.relatedId = this.businessChanceDetail.bussinessChanceId;
                        if (data.noneNextDate) {
                            data.noneNextDate = 1;
                        } else {
                            data.noneNextDate = 0;
                        }
                        if (data.time.length > 1) {
                            data.begintime = this.communicateRecordDto.time[0];
                            data.endtime = this.communicateRecordDto.time[1];
                        }
                        data.companyId = 1;
                        data.isLfasr = 0;
                        data.traderId = this.businessChanceDetail.traderId;
                        data.communicateType = 244;
                        if (data.communicateRecordId == null) {
                            pageAddCommunication(data).then(res => {
                                if (res.data.code == 0) {
                                    this.$message({
                                        message: '保存成功',
                                        type: 'success',
                                        onClose: () => {
                                            this.dialogFormVisible = false;
                                            window.location.reload();
                                        }
                                    });
                                    updateChanceStatus({'id': this.businessChanceDetail.bussinessChanceId});

                                } else {
                                    this.$message({
                                        message: res.data.message,
                                        type: 'error',
                                        onClose: () => {
                                            this.dialogFormVisible = false;
                                        }
                                    });
                                }
                                this.reInitCommun();
                                this.disabledForm = true;
                            });
                        } else {
                            pageUpdateCommunication(data).then(res => {
                                if (res.data.code == 0) {
                                    this.$message({
                                        message: '修改成功',
                                        type: 'success',
                                        onClose: () => {
                                            this.dialogFormVisible = false;
                                            window.location.reload();
                                        }
                                    });

                                } else {
                                    this.$message({
                                        message: res.data.message,
                                        type: 'error',
                                        onClose: () => {
                                            this.dialogFormVisible = false;
                                        }
                                    });
                                }
                                this.reInitCommun();
                                this.disabledForm = true;
                            });
                        }

                    }
                })

            },
            saveAddSupport() {
                this.$refs['addSupportForm'].validate((valid) => {
                    if (valid) {
                        this.disabledForm = true;
                        let data = JSON.parse(JSON.stringify(this.addSupportDto));
                        data.businessChanceId = this.businessChanceDetail.bussinessChanceId;
                        console.log(data)
                        this.saveAddSupportLoading = true;
                        addSupport(data).then(res => {
                            if (res.data.code == 0) {
                                this.$message({
                                    message: '保存成功',
                                    type: 'success',
                                    onClose: () => {
                                        this.addSupportDialogVisible = false;
                                        this.saveAddSupportLoading = false;
                                        window.location.reload();
                                    }
                                });

                            } else {
                                this.$message({
                                    message: res.data.message,
                                    type: 'error',
                                    onClose: () => {
                                        this.addSupportDialogVisible = false;
                                        this.saveAddSupportLoading = false;
                                    }
                                });
                            }
                            this.cancelAddSupport();
                            this.disabledForm = true;
                        });
                    }
                })
            },
            cancelAddSupport(){
                this.addSupportDialogVisible = false;
                this.addSupportDto = JSON.parse(JSON.stringify(this.addSupportEntity));
            },
            saveAddRequest() {
                let userIdList = this.$refs.addRequestTree.getCheckedNodes(true,false).map(node => node.id);
                if (userIdList.length == 0) {
                    this.$message.error('请选择咨询方案');
                    return;
                }
                this.$refs['addRequestForm'].validate((valid) => {
                    if (valid) {
                        this.disabledForm = true;
                        let data = JSON.parse(JSON.stringify(this.addRequestDto));
                        data.businessChanceId = this.businessChanceDetail.bussinessChanceId;
                        data.userIdList = userIdList
                        console.log(data)
                        this.saveAddRequestLoading = true;
                        addRequest(data).then(res => {
                            if (res.data.code == 0) {
                                this.$message({
                                    message: '保存成功',
                                    type: 'success',
                                    onClose: () => {
                                        this.addSupportDialogVisible = false;
                                        this.saveAddRequestLoading = false;
                                        window.location.reload();
                                    }
                                });

                            } else {
                                this.$message({
                                    message: res.data.message,
                                    type: 'error',
                                    onClose: () => {
                                        this.addSupportDialogVisible = false;
                                        this.saveAddRequestLoading = false;
                                    }
                                });
                            }
                            this.cancelAddSupport();
                            this.disabledForm = true;
                        });
                    }
                })
            },
            cancelAddRequest(){
                this.addRequestDialogVisible = false;
            },
            reInitCommun() {
                this.dialogVisible = false;
                this.communicateRecordDto = JSON.parse(JSON.stringify(this.communicateRecordEntity));
            },
            // 改
            buildCommunicationData() {
                var array = []
                if (this.businessChanceDetail.traderContactName != null
                    && this.businessChanceDetail.traderContactName !== ''
                    && this.businessChanceDetail.mobile != null
                    && this.businessChanceDetail.mobile !== '') {
                    let mob = {
                        id: 1,
                        traderContactName: this.businessChanceDetail.traderContactName,
                        type: this.businessChanceDetail.mobile,

                    };
                    array.push(mob)
                }
                if (this.businessChanceDetail.traderContactName != null
                    && this.businessChanceDetail.traderContactName !== ''
                    && this.businessChanceDetail.telephone != null
                    && this.businessChanceDetail.telephone !== '') {
                    let data = {
                        id: 2,
                        traderContactName: this.businessChanceDetail.traderContactName,
                        type: this.businessChanceDetail.telephone

                    };
                    array.push(data)
                }
                this.bindCommunicationData = array;

            },
            // 点击触发输入
            showInput() {
                this.inputVisible_tag = true;
                this.$nextTick(_ => {
                    this.$refs.saveTagInput.$refs.input.focus();
                });
            },
            handleInputConfirm() {
                let inputValue = this.inputTagValue;
                if (inputValue) {
                    if (this.communicateRecordDto.userTag.find(i => i == inputValue)) {
                        this.$message.warning("选择的标签值已存在");
                    } else {
                        this.communicateRecordDto.userTag.push(inputValue);
                    }

                }
                this.inputVisible_tag = false;
                this.inputTagValue = '';
            },
            //删除系统标签
            handlesysTagUserClose(item) {
                this.sysTagUserCheck.splice(this.sysTagUserCheck.indexOf(item), 1);
                this.communicateRecordDto.sysTag.splice(this.communicateRecordDto.sysTag.indexOf(item.tagId), 1);
            },
            // 删除用户标签
            handleUserTagClose(item) {
                this.communicateRecordDto.userTag.splice(this.communicateRecordDto.userTag.indexOf(item), 1);
            },

            // 标签点击事件
            sysTagClick(item) {
                if (this.communicateRecordDto.sysTag.find(i => i == item.tagId)) {
                    this.$message.warning("选择的标签值已存在");
                } else {
                    this.communicateRecordDto.sysTag.push(item.tagId)
                    this.sysTagUserCheck.push(item)
                }

            },
            handleClose(done) {
                this.$confirm('确认关闭？')
                    .then(_ => {
                        done();
                    })
                    .catch(_ => {
                    });
            },


            // 清除联系人信息
            traderContactClearComm() {
                this.traderContactNameView = null;
                this.communicateRecordDto.traderContactId = null;
                this.communicateRecordDto.contact = null;
                this.communicateRecordDto.contactMob = null;
            },

            communicateRecordNextBind() {
                if (this.communicateRecordDto.noneNextDate) {
                    this.communicateRecordDto.nextContactDate = null;
                    this.rules = {
                        ...this.rules,
                        'nextContactDate': [{
                            // type: 'date',
                            required: false,
                            message: '',
                            trigger: 'change'
                        }]
                    };
                } else {
                    this.communicateRecordDto.nextContactDate =null;
                    this.rules = {
                        ...this.rules,
                        'nextContactDate': [{
                            // type: 'date',
                            required: true,
                            message: '请输入下次沟通时间',
                            trigger: 'change'
                        }]
                    };
                }
            },

            // 编辑商机
            editChance() {
                openTab("编辑商机", '/businessChance/updateView.do?businessChanceId=' + this.businessChanceDetail.bussinessChanceId);
            },

            closeChance(bussinessChanceId) {
                layer.open({
                    title: '关闭商机',
                    type: 2,
                    shade: 0.2,
                    maxmin: true,
                    shadeClose: true,
                    area: ['40%', '35%'],
                    content: '/order/bussinesschance/toCloseSalesPage.do?bussinessChanceId=' + bussinessChanceId + '&taskId=' + this.taskId,
                    moveOut: true
                });
            },

            confirmTrader(bussinessChanceId, traderId) {

                layer.open({
                    title: '确认客户',
                    type: 2,
                    shade: 0.2,
                    maxmin: true,
                    shadeClose: true,
                    area: ['50%', '90%'],
                    content: '/order/bussinesschance/confirmCustomer.do?bussinessChanceId=' + bussinessChanceId + '&traderId=0',
                    moveOut: true
                });
            },

            addSku(traderId) {
                layer.open({
                    title: '添加sku',
                    type: 2,
                    shade: 0.2,
                    maxmin: true,
                    shadeClose: true,
                    area: ['50%', '90%'],
                    content: '/order/quote/addQuoteGoods.do?optType=1&traderId=' + traderId + '&quoteorderId=',
                    moveOut: true
                });
            },

            viewSkuDetail(skuId) {
                if (skuId) {
                    this.$message.success(skuId);
                    openTab("商品整合查询页", 'goods/goods/viewbaseinfo.do?goodsId=' + skuId);
                }else {
                    this.$message.error("非本系统商品无法跳转");
                }
            },

            viewTraderDetail(traderId) {
                openTab("客户详情", 'trader/customer/baseinfo.do?traderId=' + traderId);
            },

            viewQuoteOrder(QuoteOrderId, status) {
                //if (status === 0) {
                //    openTab("报价详情", '/order/quote/getQuoteDetail.do?quoteorderId=' + QuoteOrderId + '&viewType=2');
                // } else {
                //     openTab("报价详情", '/order/quote/getQuoteDetail.do?quoteorderId=' + QuoteOrderId + '&viewType=3');
                // }
            },

            viewSaleOrder(saleOrderId) {
                openTab("订单详情", '/orderstream/saleorder/detail.do?saleOrderId=' + saleOrderId);
            },

            getSaleOrderStatus(status) {
                switch (status) {
                    case 0:
                        return '待确认';
                    case 1:
                        return '进行中';
                    case 2:
                        return '已完结';
                    case 3:
                        return '已关闭';
                    case 4:
                        return '待用户确认';
                }
            },

            getQuoteOrderStatus(status) {
                switch (status) {
                    case 0:
                        return '跟单中';
                    case 1:
                        return '成单';
                    case 2:
                        return '失单';
                }
            },

            getHistoryChanceInfo(row) {
                getBusinessChanceDetail({'businessChanceId': row.bussinessChanceId}).then((result) => {
                    this.historyChanceInfo = result.data.data;
                });
            },

            getTraderInfoFromCrm(traderId) {
                getBidInfoByTraderId({'traderId': traderId}).then((result) => {
                    this.bidData = result.data.data.data;
                });
                getTraderInfoByTraderId({'traderId': traderId}).then((result) => {
                    this.crmTraderInfo = result.data.data.data;
                });
            },

            call(phone) {
                callout(
                    phone,
                    this.businessChanceDetail.traderId,
                    this.businessChanceDetail.traderId !== 0 ? 1 : 0,
                    1,
                    this.businessChanceId,
                    0);
                updateChanceStatus({'id': viewInfo.businessChanceId});
            },

            //播放录音
            playrecord(url) {
                if (url != '') {
                    layer.open({
                        type: 2,
                        shadeClose: false, //点击遮罩关闭
                        area: ['360px', '80px'],
                        title: false,
                        content: ['/system/call/getrecordpaly.do?url=' + url],
                        success: function (layero, index) {
                            //layer.iframeAuto(index);
                        },
                        error: function (data) {
                            if (data.status == 1001) {
                                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                            }
                        }
                    });
                }
            },
            addRequestClick(){
                this.addRequestDialogVisible = true;
                this.disabledForm = false;
                getSupportListApi().then(res => {
                    if (res.data.code == 0) {
                        this.data = res.data.data;
                    } else {
                        this.$message({
                            message: res.data.msg,
                            type: 'error'
                        });
                    }
                })
            },
            addSupportClick(){
                this.addSupportDialogVisible = true;
                this.disabledForm = false;
            },


            //新增沟通记录页面====begin
            addCommunicateRecord() {
                this.dialogVisible = true;
                this.communicateRecordDto = JSON.parse(JSON.stringify(this.communicateRecordEntity));
                this.communicateRecordDto.relatedId = viewInfo.businessChanceId;
                    getAccuracyEnum().then(res => {
                        this.businessChanceAccuracys = res.data.data;
                    });
                    if (this.businessChanceDetail.businessChanceAccuracy != null){
                    this.communicateRecordDto.businessChanceAccuracy = this.businessChanceDetail.businessChanceAccuracy;
                    }
                if (this.businessChanceDetail.traderId !== 0) {
                    this.communicateRecordDto.traderId = this.businessChanceDetail.traderId;
                    getTraderContactDat(
                        {
                            "param": {"traderId": this.businessChanceDetail.traderId},
                            "orderBy": 'TRADER_CONTACT_ID desc',
                            "pageSize": 1000
                        }
                    ).then(res => {
                        this.traderConcatDatas = res.data.data.list;
                    });
                } else {
                    var array = [];
                    if (this.businessChanceDetail.traderContactName != null
                        && this.businessChanceDetail.traderContactName !== ''
                        && this.businessChanceDetail.phone != null
                        && this.businessChanceDetail.phone !== '') {
                        let mob = {
                            traderContactId: 1,
                            name: this.businessChanceDetail.traderContactName,
                            mobile: this.businessChanceDetail.phone,
                        };
                        array.push(mob)
                    }
                    if (this.businessChanceDetail.traderContactName != null
                        && this.businessChanceDetail.traderContactName !== ''
                        && this.businessChanceDetail.telephone != null
                        && this.businessChanceDetail.telephone !== '') {
                        let data = {
                            traderContactId: 2,
                            name: this.businessChanceDetail.traderContactName,
                            mobile: this.businessChanceDetail.telephone
                        };
                        array.push(data)
                    }
                    this.traderConcatDatas = array;
                }
            },


            getSales(query) {
                if (query !== '') {
                    this.getSalesLoading = true;
                    searchUser({"username" : query}).then(res => {
                        this.salesOptions = res.data.data;
                        this.getSalesLoading = false;
                    })
                } else {
                    this.salesOptions = [];
                }
            },


            changeSaleUserId() {
                let filter = this.salesOptions.filter((item)=> { return item.userId == this.shareData.saleUserId });
                if (filter.length > 0) {
                    this.shareData.saleUserName = filter[0].username;
                } else {
                    this.shareData.saleUserName = '';
                }
                console.log(this.salesShareList)
                this.showError = this.salesShareList.filter((item)=> { return item.saleUserId == this.shareData.saleUserId }).length > 0
            },

            shareBusinessChance(){
                if (!this.showError) {
                    if (this.shareData.saleUserId.length === 0) {
                        this.$message({
                            message: '请选择分享人员',
                            type: 'warning'
                        });
                        return;
                    }

                    this.shareData.businessId = this.businessChanceId,
                    this.shareData.businessNo = this.businessChanceDetail.bussinessChanceNo
                    shareBusinessChance(this.shareData).then(res => {
                        if (res.data.code === 0) {
                            this.$message({
                                message: '分享成功',
                                type: 'success'
                            });
                            this.getShareBusinessChance();
                            this.shareDialogVisible = false;
                        } else {
                            this.$message({
                                message: res.data.msg,
                                type: 'error'
                            });
                        }
                        this.shareData = {}
                        this.salesOptions = [];
                        this.showError = false;
                    });
                }
            },

            getShareBusinessChance() {
                getShareBusinessChance({'bussinessChanceId': this.businessChanceId}).then(res => {
                    this.salesShareList = res.data.data;
                });
            },

            // 弹窗关闭事件
            cancelSaveShare() {
                this.shareData = {};
                this.salesOptions = [];
                this.showError = false;
            },

            cancelShare(row) {
                let content = '确认取消"' + this.businessChanceDetail.bussinessChanceNo + '"分享给"' + row.saleUserName + '"，取消后该用户将无法查看到相关的数据？';
                this.$confirm(content, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                        cancelShareBusinessChance({'id': row.id}).then(res => {
                            if (res.data.code === 0) {
                                this.$message({
                                    message: '取消分享成功',
                                    type: 'success'
                                });
                                this.getShareBusinessChance();
                            } else {
                                this.$message({
                                    message: res.data.msg,
                                    type: 'error'
                                });
                            }
                        });
                    }
                ).catch(() => {

                });
            },
            // 支持记录
            filterNode(value, data) {
                if (!value){
                    return true;
                }
                return data.label.indexOf(value) !== -1;
            }
        }
    });

    // 空方法 用于判断是跳转新详情页还是旧的详情页
    function isVue() {
    }

    function updateTrader() {

        let data = {
            "param": {"traderId": vm0.businessChanceDetail.traderId},
            "orderBy": 'TRADER_CONTACT_ID desc',
            "pageSize": 1000
        };
        getTraderContactDat(data).then(res => {
            vm0.traderConcatDatas = res.data.data.list;
            if (vm0.traderConcatDatas.length > 0) {
                vm0.communicateRecordDto.traderContactNameView = vm0.traderConcatDatas[0].name + " " + vm0.traderConcatDatas[0].mobile + " " + vm0.traderConcatDatas[0].telephone;
                vm0.communicateRecordDto.traderContactId = vm0.traderConcatDatas[0].traderContactId
            }
        })

    }


</script>

<style>
    .el-row {
        margin-bottom: 10px;
    }

    .el-descriptions {
        background-color: #e5f1ff;
        margin-bottom: 20px;
    }

    .el-descriptions__title {
        margin-left: 10px;
        margin-top: 10px;
    }

    .el-descriptions__header {
        margin-bottom: 10px;
    }

    .title-style {
        background-color: #e5f1ff;
        box-sizing: border-box;
        padding-left: 10px;
        font-size: 16px;
        font-weight: 700;
        color: #303133;
    }

    .el-descriptions-item__cell.el-descriptions-item__label.is-bordered-label {
        text-align: center;
        width: 10%;
    }

    .cell {
        text-align: center;
    }


    .el-descriptions-item__cell.el-descriptions-item__content {
        text-align: center !important;
    }

    .my-content {
        width: 40%;
    }

    .el-tag{
        white-space: normal;
        height:auto;
    }
    .bottom {
        clear: both;
        width: 200px;
        text-align: center;
    }

    .container {
        text-align: left;
        max-height: 200px;
    }
    .multiline-ellipsis {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 8; /* 限制显示的行数 */
    }
    .row-bg {
        background-color: #e5f1ff;
    }
    .el-row {
        margin-bottom: 0px;
        &:last-child {
            margin-bottom: 0;
        }
    }
    .el-col {
        border-radius: 4px;
    }
    .row-l{
        text-align: left;
        align-items:center;
        padding-left: 10px;
        display: flex;
        .text-a{
            font-size: 16px;
            font-weight: 700;
            color: #303133;
        }
        .text-b{
            margin-left:10px;
            font-weight: 300;
            font-size: 15px;
            color: rgb(144, 147, 153);
        }
    }
    .row-r{
        text-align: right;
        padding-right: 10px;
        color: #409EFF;
        font-weight: 500;
        font-size: 14px;
    }

    .el-tooltip__popper {
        max-width: 35% !important;
    }
    .tree-container {
        height: 300px;
        overflow-y: auto;
    }

</style>