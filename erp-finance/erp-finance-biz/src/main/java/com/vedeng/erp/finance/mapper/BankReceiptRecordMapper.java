package com.vedeng.erp.finance.mapper;

import com.vedeng.erp.finance.domain.entity.BankReceiptRecordEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BankReceiptRecordMapper {
    /**
     * delete by primary key
     * @param bankReceiptAliasId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long bankReceiptAliasId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(BankReceiptRecordEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(BankReceiptRecordEntity record);

    /**
     * select by primary key
     * @param bankReceiptAliasId primary key
     * @return object by primary key
     */
    BankReceiptRecordEntity selectByPrimaryKey(Long bankReceiptAliasId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(BankReceiptRecordEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(BankReceiptRecordEntity record);

    int updateBatch(List<BankReceiptRecordEntity> list);

    int updateBatchSelective(List<BankReceiptRecordEntity> list);

    int batchInsert(@Param("list") List<BankReceiptRecordEntity> list);


    List<BankReceiptRecordEntity> findByAccountNoAndAccountType(@Param("accountNo")String accountNo,@Param("accountType")Integer accountType);

}
