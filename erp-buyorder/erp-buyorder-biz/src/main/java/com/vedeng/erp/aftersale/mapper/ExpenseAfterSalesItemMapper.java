package com.vedeng.erp.aftersale.mapper;

import com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesItemEntity;
import com.vedeng.erp.aftersale.dto.ExpenseAfterSalesItemDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Repository
public interface ExpenseAfterSalesItemMapper {
    /**
     * delete by primary key
     *
     * @param expenseAfterSalesItemId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long expenseAfterSalesItemId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(ExpenseAfterSalesItemEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(ExpenseAfterSalesItemEntity record);

    /**
     * select by primary key
     *
     * @param expenseAfterSalesItemId primary key
     * @return object by primary key
     */
    ExpenseAfterSalesItemEntity selectByPrimaryKey(Long expenseAfterSalesItemId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(ExpenseAfterSalesItemEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(ExpenseAfterSalesItemEntity record);

    /**
     * 根据费用单商品id查询该商品对应的已完结的售后数量之和
     *
     * @param expenseItemIdList 费用单商品id集合
     * @return 费用单商品和其对应的售后数量之和
     */
    List<ExpenseAfterSalesItemEntity> getItemReturnNum(@Param("expenseItemIdList") List<Integer> expenseItemIdList);

    /**
     * 查询费用单商品 在当前费用售后单中的退货数量
     *
     * @param expenseAfterSalesId   费用售后单id
     * @param buyorderExpenseItemId 费用单商品id
     * @return 退货数量
     */
    ExpenseAfterSalesItemEntity getReturnNum(@Param("expenseAfterSalesId") Long expenseAfterSalesId, @Param("buyorderExpenseItemId") Integer buyorderExpenseItemId);


    /**
     * 批量插入
     *
     * @param list 插入的itemList
     */
    void batchInsertSelective(List<ExpenseAfterSalesItemEntity> list);

    /**
     * 根据费用售后单id查询费用售后商品信息集合
     *
     * @param expenseAfterSalesId 费用售后单id
     * @return 退货商品集合
     */
    List<ExpenseAfterSalesItemDto> getExpenseAfterSalesItemListByAfterSalesId(@Param("expenseAfterSalesId") Long expenseAfterSalesId);

    /**
     * 查询采购费用单 所有已完结的退货商品
     *
     * @param buyorderExpenseId 采购费用单id
     * @return 退货商品信息
     */
    List<ExpenseAfterSalesItemDto> getExpenseAfterSalesReturnGoodsBybuyorderExpenseId(Integer buyorderExpenseId);

    /**
     * 查询采购费用单 售后 关联的售后单 所有已完结的退货商品
     * @param buyorderExpenseId 采购费用单id
     * @param saleOrderId 销售单id
     * @return 售后单明细id 关联到的售后的数量
     */
    List<ExpenseAfterSalesItemDto> getExpenseAfterSalesReturnGoodsBybuyorderExpenseIdAndSaleOrderId(@Param("buyorderExpenseId") Integer buyorderExpenseId, @Param("saleOrderId") Integer saleOrderId);
}