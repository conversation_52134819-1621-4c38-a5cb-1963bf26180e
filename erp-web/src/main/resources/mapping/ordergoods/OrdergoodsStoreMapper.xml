<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.ordergoods.dao.OrdergoodsStoreMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.ordergoods.model.OrdergoodsStore" >
    <id column="ORDERGOODS_STORE_ID" property="ordergoodsStoreId" jdbcType="INTEGER" />
    <result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
    <result column="NAME" property="name" jdbcType="VARCHAR" />
    <result column="IS_ENABLE" property="isEnable" jdbcType="BIT" />
    <result column="DESCRIPTION" property="description" jdbcType="VARCHAR" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    ORDERGOODS_STORE_ID, COMPANY_ID, NAME, IS_ENABLE, DESCRIPTION, ADD_TIME, CREATOR, 
    MOD_TIME, UPDATER
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from T_ORDERGOODS_STORE
    where ORDERGOODS_STORE_ID = #{ordergoodsStoreId,jdbcType=INTEGER}
  </select>
  <insert id="insert" parameterType="com.vedeng.ordergoods.model.OrdergoodsStore" >
    insert into T_ORDERGOODS_STORE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ordergoodsStoreId != null" >
        ORDERGOODS_STORE_ID,
      </if>
      <if test="companyId != null" >
        COMPANY_ID,
      </if>
      <if test="name != null" >
        NAME,
      </if>
      <if test="isEnable != null" >
        IS_ENABLE,
      </if>
      <if test="description != null" >
        DESCRIPTION,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ordergoodsStoreId != null" >
        #{ordergoodsStoreId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null" >
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null" >
        #{isEnable,jdbcType=BIT},
      </if>
      <if test="description != null" >
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
    <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="ordergoodsStoreId">
		SELECT LAST_INSERT_ID() AS ordergoodsStoreId
    </selectKey>
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.ordergoods.model.OrdergoodsStore" >
    update T_ORDERGOODS_STORE
    <set >
      <if test="companyId != null" >
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="name != null" >
        NAME = #{name,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null" >
        IS_ENABLE = #{isEnable,jdbcType=BIT},
      </if>
      <if test="description != null" >
        DESCRIPTION = #{description,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where ORDERGOODS_STORE_ID = #{ordergoodsStoreId,jdbcType=INTEGER}
  </update>
  
  <select id="getStore" resultMap="BaseResultMap" parameterType="java.lang.Integer">
  	select
  	<include refid="Base_Column_List" />
    from T_ORDERGOODS_STORE
    where
    	COMPANY_ID = #{companyId,jdbcType=INTEGER}
  	order by 
  		IS_ENABLE desc,ADD_TIME desc
  	
  </select>
  
  <select id="getStoreByName" resultMap="BaseResultMap" parameterType="com.vedeng.ordergoods.model.OrdergoodsStore">
  	select
  	<include refid="Base_Column_List" />
    from T_ORDERGOODS_STORE
    where
    	COMPANY_ID = #{companyId,jdbcType=INTEGER}
    and
    	NAME = #{name,jdbcType=VARCHAR}
  </select>
</mapper>