package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 预处理金蝶客户dto  由erp实际业务转换
 * @date 2022/8/26 10:18
 */
@Getter
@Setter
@NoArgsConstructor
public class KingDeeCustomerDetailDto{
    /**
     * 银行帐号
     */
    private String fbankcode;
    /**
     * 帐户名称
     */
    private String faccountname;
    /**
     * 开户行
     */
    private String fopenbankname;
    /**
     * 联行号
     */
    private String fcnaps;
}
