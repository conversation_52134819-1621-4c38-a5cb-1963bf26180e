package com.vedeng.erp.buyorder.web.controller;

import cn.hutool.json.JSONUtil;
import com.vedeng.common.core.annotation.ExcludeAuthorization;
import com.vedeng.erp.buyorder.service.NewBuyOrderService;
import com.vedeng.erp.buyorder.vo.AddRebateItemVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import java.math.BigDecimal;

/**
 * 采购订单-返利支付
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/27 17:00
 */
@Slf4j
@Controller
@RequestMapping("/buyOrder/rebatePayment")
public class BuyOrderRebatePaymentController {


    @Autowired
    NewBuyOrderService newBuyOrderService;

    /**
     * 采购订单返利支付弹框页面
     *
     * @return ModelAndView
     */
    @RequestMapping("/add")
    @ExcludeAuthorization
    public ModelAndView index(AddRebateItemVo addRebateItemVo) {
        log.info("采购订单返利支付弹框页面,addRebateItemVo:{}", JSONUtil.toJsonStr(addRebateItemVo));
        ModelAndView modelAndView = new ModelAndView("vue/view/buyorderrebatepayment/add");
        // 根据 采购明细id 获取实际入库的返利使用总额，使用可用返利加上已使用的返利
        BigDecimal usedRebateCharge = newBuyOrderService.getUsedRebateCharge(addRebateItemVo.getBuyorderId(),addRebateItemVo.getTraderId());
        addRebateItemVo.setUsedRebateCharge(usedRebateCharge);
        modelAndView.addObject("addRebateItem", JSONUtil.parse(addRebateItemVo));
        return modelAndView;
    }

}