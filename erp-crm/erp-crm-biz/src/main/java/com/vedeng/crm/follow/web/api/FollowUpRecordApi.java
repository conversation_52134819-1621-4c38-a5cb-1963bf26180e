package com.vedeng.crm.follow.web.api;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.crm.follow.domain.dto.FollowUpRecordPageResponseDto;
import com.vedeng.crm.follow.service.FollowUpRecordService;
import com.vedeng.crm.visitrecord.domain.vo.VisitRecordVo;
import com.vedeng.crm.visitrecord.service.CrmVisitRecordService;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.trader.common.enums.CommunicateRecordTypeEnum;
import com.vedeng.erp.trader.dto.CommunicateRecordDto;
import com.vedeng.erp.trader.dto.CommunicateTelRecord;
import com.vedeng.erp.trader.dto.CommunicateTelRecordApiDto;
import com.vedeng.erp.trader.dto.CommunicateTelRecordParams;
import com.vedeng.erp.trader.dto.FollowBindingTelParams;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 跟进记录
 */
@ExceptionController
@RestController
@RequestMapping("/crm/followUpRecord/profile")
@Slf4j
public class FollowUpRecordApi {
    
    @Autowired
    private FollowUpRecordService followUpRecordService;
    
    @Autowired
    private CrmVisitRecordService crmVisitRecordService;

    /**
     * 添加跟进记录
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> add(@RequestBody CommunicateRecordDto communicateRecordDto) {
        followUpRecordService.add(communicateRecordDto);
        return R.success();
    }

    /**
     * 更新跟进记录
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> update(@RequestBody CommunicateRecordDto communicateRecordDto) {
        followUpRecordService.update(communicateRecordDto);
        return R.success();
    }

    /**
     * 跟进记录列表分页接口
     */
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<FollowUpRecordPageResponseDto> page(@RequestBody CommunicateRecordDto communicateRecordQueryDto) {
        return R.success(followUpRecordService.page(communicateRecordQueryDto));
    }

    /**
     * 跟进记录详情
     */
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    @NoNeedAccessAuthorization
    public R<CommunicateRecordDto> detail(@RequestParam Integer communicateRecordId) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        CommunicateRecordDto communicateRecordDto =followUpRecordService.detail(communicateRecordId);
        if(communicateRecordDto != null && CommunicateRecordTypeEnum.VISIT_RECORD.getCode().equals(communicateRecordDto.getCommunicateType())){
            Integer id = communicateRecordDto.getRelatedId();//拜访计划的ID
            //根据ID查询拜访计划
            //将拜访计划中的信息填充到communicateRecordDto中返回
            VisitRecordVo visitRecordVo =  crmVisitRecordService.detail(id,currentUser,false);
            if(visitRecordVo != null){
                communicateRecordDto.setOtherData(visitRecordVo);
            }

        }
        return R.success(communicateRecordDto);
    }

    /**
     * 查询通话记录
     */
    @RequestMapping(value = "/getTelList", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> getTelList(@RequestBody PageParam<CommunicateTelRecordParams> communicateTelRecordParams) {
    	CurrentUser currentUser = CurrentUser.getCurrentUser();
        CommunicateTelRecordApiDto communicateTelRecordApiDto = followUpRecordService.getTelList(communicateTelRecordParams,currentUser);
        return R.success(communicateTelRecordApiDto);
    }
    
    /**
     * 跟进记录绑定通话
     */
    @RequestMapping(value = "/followBindingTel", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> followBindingTel(@RequestBody FollowBindingTelParams followBindingTelParams) {
    	log.info("跟进记录绑定通话,入参：{}",JSON.toJSONString(followBindingTelParams));
    	try {
    		CurrentUser currentUser = CurrentUser.getCurrentUser();
    		if(Objects.isNull(followBindingTelParams)) {
    			return R.error("网络错误，请稍后重试...");
    		}
    		if(CollectionUtil.isEmpty(followBindingTelParams.getCommunicateRecordIdList()) || Objects.isNull(followBindingTelParams.getCommunicateType()) || Objects.isNull(followBindingTelParams.getRelatedId())) {
    			return R.error("网络错误，请稍后重试...");
    		}
    		List<Integer> alreadyBindingTelIdList = followUpRecordService.followBindingTel(followBindingTelParams,currentUser);
    		//如果有已绑定的沟通记录ID,返回，提示：录音id：xxxxx、xxxxx已绑定业务单据无法再次绑定该商机。
    		return R.success(alreadyBindingTelIdList);
    	}catch(Exception e) {
    		log.error("跟进记录绑定通话失败",e);
    		return R.error("网络错误，请稍后重试...");
    	}
    }
    
    /**
     * 返回当前用户自身以及下属所有启用在岗未删除用户信息集合
     */
    @RequestMapping(value = "/getUacSubUserInfoByCurrent")
    @NoNeedAccessAuthorization
    public R<?> getUacSubUserInfoByCurrent() {
    	try {
    		CurrentUser currentUser = CurrentUser.getCurrentUser();
    		List<UserDto> userDtoList = followUpRecordService.getUacSubUserInfoByCurrent(currentUser.getId());
    		return R.success(userDtoList);
    	}catch(Exception e) {
    		log.error("返回当前用户自身以及下属所有启用在岗未删除用户信息集合失败",e);
    		return R.error("网络错误，请稍后重试...");
    	}
    }
    
}
