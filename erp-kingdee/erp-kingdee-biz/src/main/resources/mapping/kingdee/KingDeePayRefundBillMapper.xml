<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.repository.mappers.KingDeePayRefundBillMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.domain.entity.KingDeePayRefundBillEntity">
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="F_ID" jdbcType="VARCHAR" property="fId" />
    <result column="F_BILL_NO" jdbcType="VARCHAR" property="fBillNo" />
    <result column="F_BILL_TYPE_ID" jdbcType="VARCHAR" property="fBillTypeId" />
    <result column="F_DATE" jdbcType="VARCHAR" property="fDate" />
    <result column="F_CONTACT_UNIT_TYPE" jdbcType="VARCHAR" property="fContactUnitType" />
    <result column="F_CONTACT_UNIT" jdbcType="VARCHAR" property="fContactUnit" />
    <result column="F_BUSINESS_TYPE" jdbcType="VARCHAR" property="fBusinessType" />
    <result column="F_PAY_UNIT_TYPE" jdbcType="VARCHAR" property="fPayUnitType" />
    <result column="F_SETTLE_ORG_ID" jdbcType="VARCHAR" property="fSettleOrgId" />
    <result column="F_PURCHASE_ORG_ID" jdbcType="VARCHAR" property="fPurchaseOrgId" />
    <result column="F_PAY_ORG_ID" jdbcType="VARCHAR" property="fPayOrgId" />
    <result column="F_QZOK_SFZK" jdbcType="VARCHAR" property="fQzokSfzk" />
    <result column="F_QZOK_LSH" jdbcType="VARCHAR" property="fQzokLsh" />
    <result column="F_QZOK_BDDJTID" jdbcType="VARCHAR" property="fQzokBddjtid" />
    <result column="F_REFUND_BILL_ENTRY" javaType="com.vedeng.erp.kingdee.dto.KingDeePayRefundEntryDto"
            property="fRefundBillEntry"
            typeHandler="com.vedeng.common.mybatis.handler.CustomSqlJsonArrayHandler" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, F_ID, F_BILL_NO, F_BILL_TYPE_ID, F_DATE, F_CONTACT_UNIT_TYPE, F_CONTACT_UNIT, 
    F_BUSINESS_TYPE, F_PAY_UNIT_TYPE, F_SETTLE_ORG_ID, F_PURCHASE_ORG_ID, F_PAY_ORG_ID, 
    F_QZOK_SFZK, F_QZOK_LSH, F_QZOK_BDDJTID, F_REFUND_BILL_ENTRY, ADD_TIME, MOD_TIME, 
    CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from KING_DEE_PAY_REFUND_BILL
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from KING_DEE_PAY_REFUND_BILL
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeePayRefundBillEntity" useGeneratedKeys="true">
    insert into KING_DEE_PAY_REFUND_BILL (F_ID, F_BILL_NO, F_BILL_TYPE_ID, 
      F_DATE, F_CONTACT_UNIT_TYPE, F_CONTACT_UNIT, 
      F_BUSINESS_TYPE, F_PAY_UNIT_TYPE, F_SETTLE_ORG_ID, 
      F_PURCHASE_ORG_ID, F_PAY_ORG_ID, F_QZOK_SFZK, 
      F_QZOK_LSH, F_QZOK_BDDJTID, F_REFUND_BILL_ENTRY, 
      ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME
      )
    values (#{fId,jdbcType=VARCHAR}, #{fBillNo,jdbcType=VARCHAR}, #{fBillTypeId,jdbcType=VARCHAR}, 
      #{fDate,jdbcType=VARCHAR}, #{fContactUnitType,jdbcType=VARCHAR}, #{fContactUnit,jdbcType=VARCHAR}, 
      #{fBusinessType,jdbcType=VARCHAR}, #{fPayUnitType,jdbcType=VARCHAR}, #{fSettleOrgId,jdbcType=VARCHAR}, 
      #{fPurchaseOrgId,jdbcType=VARCHAR}, #{fPayOrgId,jdbcType=VARCHAR}, #{fQzokSfzk,jdbcType=VARCHAR}, 
      #{fQzokLsh,jdbcType=VARCHAR}, #{fQzokBddjtid,jdbcType=VARCHAR}, #{fRefundBillEntry,jdbcType=VARCHAR},
      #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeePayRefundBillEntity" useGeneratedKeys="true">
    insert into KING_DEE_PAY_REFUND_BILL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="fId != null">
        F_ID,
      </if>
      <if test="fBillNo != null">
        F_BILL_NO,
      </if>
      <if test="fBillTypeId != null">
        F_BILL_TYPE_ID,
      </if>
      <if test="fDate != null">
        F_DATE,
      </if>
      <if test="fContactUnitType != null">
        F_CONTACT_UNIT_TYPE,
      </if>
      <if test="fContactUnit != null">
        F_CONTACT_UNIT,
      </if>
      <if test="fBusinessType != null">
        F_BUSINESS_TYPE,
      </if>
      <if test="fPayUnitType != null">
        F_PAY_UNIT_TYPE,
      </if>
      <if test="fSettleOrgId != null">
        F_SETTLE_ORG_ID,
      </if>
      <if test="fPurchaseOrgId != null">
        F_PURCHASE_ORG_ID,
      </if>
      <if test="fPayOrgId != null">
        F_PAY_ORG_ID,
      </if>
      <if test="fQzokSfzk != null">
        F_QZOK_SFZK,
      </if>
      <if test="fQzokLsh != null">
        F_QZOK_LSH,
      </if>
      <if test="fQzokBddjtid != null">
        F_QZOK_BDDJTID,
      </if>
      <if test="fRefundBillEntry != null">
        F_REFUND_BILL_ENTRY,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="fId != null">
        #{fId,jdbcType=VARCHAR},
      </if>
      <if test="fBillNo != null">
        #{fBillNo,jdbcType=VARCHAR},
      </if>
      <if test="fBillTypeId != null">
        #{fBillTypeId,jdbcType=VARCHAR},
      </if>
      <if test="fDate != null">
        #{fDate,jdbcType=VARCHAR},
      </if>
      <if test="fContactUnitType != null">
        #{fContactUnitType,jdbcType=VARCHAR},
      </if>
      <if test="fContactUnit != null">
        #{fContactUnit,jdbcType=VARCHAR},
      </if>
      <if test="fBusinessType != null">
        #{fBusinessType,jdbcType=VARCHAR},
      </if>
      <if test="fPayUnitType != null">
        #{fPayUnitType,jdbcType=VARCHAR},
      </if>
      <if test="fSettleOrgId != null">
        #{fSettleOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fPurchaseOrgId != null">
        #{fPurchaseOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fPayOrgId != null">
        #{fPayOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fQzokSfzk != null">
        #{fQzokSfzk,jdbcType=VARCHAR},
      </if>
      <if test="fQzokLsh != null">
        #{fQzokLsh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBddjtid != null">
        #{fQzokBddjtid,jdbcType=VARCHAR},
      </if>
      <if test="fRefundBillEntry != null">
        #{fRefundBillEntry,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeePayRefundBillEntity">
    update KING_DEE_PAY_REFUND_BILL
    <set>
      <if test="fId != null">
        F_ID = #{fId,jdbcType=VARCHAR},
      </if>
      <if test="fBillNo != null">
        F_BILL_NO = #{fBillNo,jdbcType=VARCHAR},
      </if>
      <if test="fBillTypeId != null">
        F_BILL_TYPE_ID = #{fBillTypeId,jdbcType=VARCHAR},
      </if>
      <if test="fDate != null">
        F_DATE = #{fDate,jdbcType=VARCHAR},
      </if>
      <if test="fContactUnitType != null">
        F_CONTACT_UNIT_TYPE = #{fContactUnitType,jdbcType=VARCHAR},
      </if>
      <if test="fContactUnit != null">
        F_CONTACT_UNIT = #{fContactUnit,jdbcType=VARCHAR},
      </if>
      <if test="fBusinessType != null">
        F_BUSINESS_TYPE = #{fBusinessType,jdbcType=VARCHAR},
      </if>
      <if test="fPayUnitType != null">
        F_PAY_UNIT_TYPE = #{fPayUnitType,jdbcType=VARCHAR},
      </if>
      <if test="fSettleOrgId != null">
        F_SETTLE_ORG_ID = #{fSettleOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fPurchaseOrgId != null">
        F_PURCHASE_ORG_ID = #{fPurchaseOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fPayOrgId != null">
        F_PAY_ORG_ID = #{fPayOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fQzokSfzk != null">
        F_QZOK_SFZK = #{fQzokSfzk,jdbcType=VARCHAR},
      </if>
      <if test="fQzokLsh != null">
        F_QZOK_LSH = #{fQzokLsh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBddjtid != null">
        F_QZOK_BDDJTID = #{fQzokBddjtid,jdbcType=VARCHAR},
      </if>
      <if test="fRefundBillEntry != null">
        F_REFUND_BILL_ENTRY = #{fRefundBillEntry,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeePayRefundBillEntity">
    update KING_DEE_PAY_REFUND_BILL
    set F_ID = #{fId,jdbcType=VARCHAR},
      F_BILL_NO = #{fBillNo,jdbcType=VARCHAR},
      F_BILL_TYPE_ID = #{fBillTypeId,jdbcType=VARCHAR},
      F_DATE = #{fDate,jdbcType=VARCHAR},
      F_CONTACT_UNIT_TYPE = #{fContactUnitType,jdbcType=VARCHAR},
      F_CONTACT_UNIT = #{fContactUnit,jdbcType=VARCHAR},
      F_BUSINESS_TYPE = #{fBusinessType,jdbcType=VARCHAR},
      F_PAY_UNIT_TYPE = #{fPayUnitType,jdbcType=VARCHAR},
      F_SETTLE_ORG_ID = #{fSettleOrgId,jdbcType=VARCHAR},
      F_PURCHASE_ORG_ID = #{fPurchaseOrgId,jdbcType=VARCHAR},
      F_PAY_ORG_ID = #{fPayOrgId,jdbcType=VARCHAR},
      F_QZOK_SFZK = #{fQzokSfzk,jdbcType=VARCHAR},
      F_QZOK_LSH = #{fQzokLsh,jdbcType=VARCHAR},
      F_QZOK_BDDJTID = #{fQzokBddjtid,jdbcType=VARCHAR},
      F_REFUND_BILL_ENTRY = #{fRefundBillEntry,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=INTEGER}
  </update>
</mapper>