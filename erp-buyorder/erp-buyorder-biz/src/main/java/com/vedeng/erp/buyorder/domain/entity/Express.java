package com.vedeng.erp.buyorder.domain.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * T_EXPRESS
 * <AUTHOR>
@Data
public class Express implements Serializable {
    private Integer expressId;

    /**
     * 物流公司dbcenter 物流表
     */
    private Integer logisticsId;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * ERP公司ID(T_COMPANY)
     */
    private Integer companyId;

    /**
     * 寄送时间
     */
    private Long deliveryTime;

    /**
     * 客户收货状态0未收货 1部分收货 2全部收货
     */
    private Boolean arrivalStatus;

    /**
     * 客户收货时间
     */
    private Long arrivalTime;

    /**
     * 始发地地区选择最小级ID
     */
    private Integer deliveryFrom;

    /**
     * 物流备注
     */
    private String logisticsComments;

    /**
     * 是否有效 0否 1是
     */
    private Boolean isEnable;

    /**
     * 付款方式1寄付月结 2寄付现结 3到付现结 4第三方付
     */
    private Boolean paymentType;

    /**
     * 月结卡号
     */
    private String cardNumber;

    /**
     * 业务类型1顺丰特惠 2物流普运 3顺丰标快
     */
    private Boolean businessType;

    /**
     * 实际重量
     */
    private BigDecimal realWeight;

    /**
     * 件数
     */
    private Integer num;

    /**
     * 计费重量
     */
    private BigDecimal amountWeight;

    /**
     * 托寄物
     */
    private String mailGoods;

    /**
     * 托寄物数量
     */
    private Integer mailGoodsNum;

    /**
     * 是否保价 0否 1是
     */
    private Boolean isProtectPrice;

    /**
     * 保价金额
     */
    private BigDecimal protectPrice;

    /**
     * 是否签回单0否 1是
     */
    private Boolean isReceipt;

    /**
     * 寄方备注（默认:节假日正常派送，提前联系）
     */
    private String mailCommtents;

    /**
     * 是否已发送短信0否1是
     */
    private Boolean sentSms;

    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private Long modTime;

    /**
     * 更新人
     */
    private Integer updater;

    private Boolean travelingByTicket;

    private Boolean isInvoicing;

    /**
     * 包裹回传WMS订单号
     */
    private String wmsOrderNo;

    /**
     * 下传wms需要
     */
    private Integer buyorderGoodsId;

    /**
     * 物流明细id
     */
    private Integer expressDetailId;

    private static final long serialVersionUID = 1L;


}