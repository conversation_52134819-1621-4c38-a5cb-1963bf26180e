<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.repository.mappers.CapitalBillKingDeeCreateMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.domain.entity.CapitalBillKingDeeCreate">
    <!--@mbg.generated-->
    <!--@Table T_CAPITAL_BILL_KING_DEE_CREATE-->
    <id column="CAPITAL_BILL_KING_DEE_CREATE_ID" jdbcType="INTEGER" property="capitalBillKingDeeCreateId" />
    <result column="FROM_CAPITAL_BILL_ID" jdbcType="INTEGER" property="fromCapitalBillId" />
    <result column="CAPITAL_BILL_NO" jdbcType="VARCHAR" property="capitalBillNo" />
    <result column="TRADER_TIME" jdbcType="BIGINT" property="traderTime" />
    <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
    <result column="ORDER_TYPE" jdbcType="INTEGER" property="orderType" />
    <result column="ORDER_NO" jdbcType="VARCHAR" property="orderNo" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    CAPITAL_BILL_KING_DEE_CREATE_ID, FROM_CAPITAL_BILL_ID, CAPITAL_BILL_NO, TRADER_TIME, 
    AMOUNT, ORDER_TYPE, ORDER_NO, IS_DELETE, ADD_TIME, CREATOR, CREATOR_NAME, MOD_TIME, 
    UPDATER, UPDATER_NAME, REMARK, UPDATE_REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_CAPITAL_BILL_KING_DEE_CREATE
    where CAPITAL_BILL_KING_DEE_CREATE_ID = #{capitalBillKingDeeCreateId,jdbcType=INTEGER}
  </select>

  <select id="selectByOrderNoAndType" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_CAPITAL_BILL_KING_DEE_CREATE
    where IS_DELETE = 0 and ORDER_NO = #{orderNo,jdbcType=VARCHAR} and ORDER_TYPE =  #{orderType,jdbcType=INTEGER}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_CAPITAL_BILL_KING_DEE_CREATE
    where CAPITAL_BILL_KING_DEE_CREATE_ID = #{capitalBillKingDeeCreateId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="CAPITAL_BILL_KING_DEE_CREATE_ID" keyProperty="capitalBillKingDeeCreateId" parameterType="com.vedeng.erp.kingdee.domain.entity.CapitalBillKingDeeCreate" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_CAPITAL_BILL_KING_DEE_CREATE (FROM_CAPITAL_BILL_ID, CAPITAL_BILL_NO, 
      TRADER_TIME, AMOUNT, ORDER_TYPE, 
      ORDER_NO, IS_DELETE, ADD_TIME, 
      CREATOR, CREATOR_NAME, MOD_TIME, 
      UPDATER, UPDATER_NAME, REMARK, 
      UPDATE_REMARK)
    values (#{fromCapitalBillId,jdbcType=INTEGER}, #{capitalBillNo,jdbcType=VARCHAR}, 
      #{traderTime,jdbcType=BIGINT}, #{amount,jdbcType=DECIMAL}, #{orderType,jdbcType=INTEGER},
      #{orderNo,jdbcType=VARCHAR}, #{isDelete,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP},
      #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{modTime,jdbcType=TIMESTAMP}, 
      #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{updateRemark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="CAPITAL_BILL_KING_DEE_CREATE_ID" keyProperty="capitalBillKingDeeCreateId" parameterType="com.vedeng.erp.kingdee.domain.entity.CapitalBillKingDeeCreate" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_CAPITAL_BILL_KING_DEE_CREATE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="fromCapitalBillId != null">
        FROM_CAPITAL_BILL_ID,
      </if>
      <if test="capitalBillNo != null">
        CAPITAL_BILL_NO,
      </if>
      <if test="traderTime != null">
        TRADER_TIME,
      </if>
      <if test="amount != null">
        AMOUNT,
      </if>
      <if test="orderType != null">
        ORDER_TYPE,
      </if>
      <if test="orderNo != null">
        ORDER_NO,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="fromCapitalBillId != null">
        #{fromCapitalBillId,jdbcType=INTEGER},
      </if>
      <if test="capitalBillNo != null">
        #{capitalBillNo,jdbcType=VARCHAR},
      </if>
      <if test="traderTime != null">
        #{traderTime,jdbcType=BIGINT},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.domain.entity.CapitalBillKingDeeCreate">
    <!--@mbg.generated-->
    update T_CAPITAL_BILL_KING_DEE_CREATE
    <set>
      <if test="fromCapitalBillId != null">
        FROM_CAPITAL_BILL_ID = #{fromCapitalBillId,jdbcType=INTEGER},
      </if>
      <if test="capitalBillNo != null">
        CAPITAL_BILL_NO = #{capitalBillNo,jdbcType=VARCHAR},
      </if>
      <if test="traderTime != null">
        TRADER_TIME = #{traderTime,jdbcType=BIGINT},
      </if>
      <if test="amount != null">
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="orderType != null">
        ORDER_TYPE = #{orderType,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where CAPITAL_BILL_KING_DEE_CREATE_ID = #{capitalBillKingDeeCreateId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.domain.entity.CapitalBillKingDeeCreate">
    <!--@mbg.generated-->
    update T_CAPITAL_BILL_KING_DEE_CREATE
    set FROM_CAPITAL_BILL_ID = #{fromCapitalBillId,jdbcType=INTEGER},
      CAPITAL_BILL_NO = #{capitalBillNo,jdbcType=VARCHAR},
      TRADER_TIME = #{traderTime,jdbcType=BIGINT},
      AMOUNT = #{amount,jdbcType=DECIMAL},
      ORDER_TYPE = #{orderType,jdbcType=INTEGER},
      ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where CAPITAL_BILL_KING_DEE_CREATE_ID = #{capitalBillKingDeeCreateId,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="CAPITAL_BILL_KING_DEE_CREATE_ID" keyProperty="capitalBillKingDeeCreateId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_CAPITAL_BILL_KING_DEE_CREATE
    (FROM_CAPITAL_BILL_ID, CAPITAL_BILL_NO, TRADER_TIME, AMOUNT, ORDER_TYPE, ORDER_NO, 
      IS_DELETE, ADD_TIME, CREATOR, CREATOR_NAME, MOD_TIME, UPDATER, UPDATER_NAME, REMARK, 
      UPDATE_REMARK)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.fromCapitalBillId,jdbcType=INTEGER}, #{item.capitalBillNo,jdbcType=VARCHAR}, 
        #{item.traderTime,jdbcType=BIGINT}, #{item.amount,jdbcType=DECIMAL}, #{item.orderType,jdbcType=INTEGER},
        #{item.orderNo,jdbcType=VARCHAR}, #{item.isDelete,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP},
        #{item.creator,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, #{item.modTime,jdbcType=TIMESTAMP}, 
        #{item.updater,jdbcType=INTEGER}, #{item.updaterName,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, 
        #{item.updateRemark,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>