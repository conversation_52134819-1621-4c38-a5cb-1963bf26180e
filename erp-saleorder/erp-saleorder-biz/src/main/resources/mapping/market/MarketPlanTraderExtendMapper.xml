<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.market.dao.MarketPlanTraderExtendMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.market.domain.vo.MarketPlanTraderResponseVo">
        <!--@mbg.generated-->
        <!--@Table T_MARKETING_PLAN_TRADER-->
        <result column="PLAN_ID" jdbcType="INTEGER" property="planId" />
        <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
        <result column="TRADER_CUSTOMER_ID" jdbcType="INTEGER" property="traderCustomerId" />
        <result column="PROMOTION_PRIORITY" jdbcType="VARCHAR" property="promotionPriority" />
        <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName" />
        <result column="COMMUNICATE_RECORD_ID" jdbcType="INTEGER" property="lastCommId" />
        <result column="CONTACT" jdbcType="VARCHAR" property="contact" />
        <result column="CONTACT_MOB" jdbcType="VARCHAR" property="contactMob" />
        <result column="TRADER_CONTACT_ID" jdbcType="INTEGER" property="traderContactId" />

        <result column="LAST_COMM_TIME" jdbcType="VARCHAR" property="lastCommTime" />
        <result column="LAST_COMM_CONTENT" jdbcType="VARCHAR" property="lastCommContent" />
        <result column="SEND_MSG" jdbcType="INTEGER" property="sendMsg" />
    </resultMap>


    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID, PLAN_ID, TRADER_ID, TRADER_CUSTOMER_ID, PROMOTION_PRIORITY, CREATOR, ADD_TIME,TRADER_NAME,LAST_COMM_TIME,LAST_COMM_CONTENT,
        UPDATOR, MOD_TIME, SEND_MSG, IS_DEL
    </sql>
    <select id="findMarketPlanTrader" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from T_MARKETING_PLAN_TRADER
        where PLAN_ID = #{planId}
        AND TRADER_ID=#{traderId}
        AND TRADER_CUSTOMER_ID=#{traderCustomerId}
    </select>

    <select id="queryMarketPlanTraderListPage" parameterType="Map" resultMap="BaseResultMap">
        select
        tmpt.ID,
        tmpt.PLAN_ID,
        tmpt.TRADER_ID,
        tmpt.TRADER_CUSTOMER_ID,
        tmpt.PROMOTION_PRIORITY,
        tmpt.CREATOR,
        tmpt.ADD_TIME,tt.TRADER_NAME,
        DATE_FORMAT(FROM_UNIXTIME(record.BEGINTIME/1000),'%Y/%m/%d %H:%i:%s')         LAST_COMM_TIME,
        record.CONTENT_SUFFIX AS LAST_COMM_CONTENT,
        lastrecord.COMMUNICATE_RECORD_ID,
        contact.NAME AS CONTACT,
        CASE WHEN record.TRADER_CONTACT_ID IS NULL
        THEN record.PHONE
        ELSE
        CONCAT(contact.MOBILE,"|",contact.TELEPHONE ) END AS CONTACT_MOB,
        record.TRADER_CONTACT_ID,
        tmpt.UPDATOR, tmpt.MOD_TIME, tmpt.SEND_MSG, tmpt.IS_DEL
        from T_MARKETING_PLAN_TRADER tmpt
        JOIN T_TRADER_CUSTOMER ttc  ON tmpt.TRADER_ID =ttc.TRADER_ID     and   tmpt.TRADER_CUSTOMER_ID = ttc.TRADER_CUSTOMER_ID
        JOIN T_TRADER tt ON tt.TRADER_ID =ttc.TRADER_ID
        JOIN T_R_TRADER_J_USER tu ON tmpt.TRADER_ID=tu.TRADER_ID
        LEFT JOIN (
        select
        TRADER_ID,MAX(COMMUNICATE_RECORD_ID) AS COMMUNICATE_RECORD_ID
        from T_COMMUNICATE_RECORD a
        where 1=1 and a.COMPANY_ID = 1 AND   a.TRADER_TYPE = 1
        AND TRADER_ID IN (SELECT DISTINCT TRADER_ID  FROM T_MARKETING_PLAN_TRADER WHERE  PLAN_ID =#{planId}
        )
        GROUP BY TRADER_ID
        )  lastrecord ON lastrecord.TRADER_ID=tmpt.TRADER_ID
        LEFT JOIN T_COMMUNICATE_RECORD record on lastrecord.COMMUNICATE_RECORD_ID =record.COMMUNICATE_RECORD_ID
        LEFT JOIN T_TRADER_CONTACT contact ON record.TRADER_CONTACT_ID =contact .TRADER_CONTACT_ID and record.TRADER_CONTACT_ID is NOT NULL
        where PLAN_ID =#{planId}
        <if test="sendMsg != null">
            AND tmpt.SEND_MSG = #{sendMsg,jdbcType=INTEGER}
        </if>
        AND tu.USER_ID=#{userId}
        order by tmpt.ID ASC
    </select>

    <!-- 添加沟通记录时，根据sqlID查询该客户有多少个任务需要更新，由近及远最多取100个 -->
    <select id="checkMarketPlanTraderSendMsg" resultType="java.lang.Integer">
        select
          tmpt.ID
        from T_MARKETING_PLAN_TRADER tmpt
        join T_MARKETING_PLAN plan ON tmpt.PLAN_ID=plan.ID
        WHERE
            plan.PLAN_STATUS=1
            AND plan.PLAN_START_TIME <![CDATA[ <= ]]> now()
            and plan.PLAN_END_TIME <![CDATA[ >= ]]> NOW()
            and tmpt.TRADER_ID=#{traderId}
            and tmpt.SEND_MSG=0
        order  by tmpt.ID desc
        LIMIT 100
    </select>

    <update id="updateMarketPlanTraderSendMsg" parameterType="java.util.List">
        <!--@mbg.generated-->
        update T_MARKETING_PLAN_TRADER
        set
        MOD_TIME = now(),
        SEND_MSG = 1
        where ID in
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>


    <select id="queryUserMarketPlanTraderList" resultType="java.util.Map">
        select
            count(tmpt.ID) as marketPlanNum
        from T_MARKETING_PLAN_TRADER tmpt
        join T_MARKETING_PLAN plan ON tmpt.PLAN_ID=plan.ID
        JOIN T_R_TRADER_J_USER tu ON tmpt.TRADER_ID=tu.TRADER_ID

        WHERE
            plan.PLAN_STATUS=1
            AND plan.PLAN_START_TIME <![CDATA[ <= ]]> now()
            and plan.PLAN_END_TIME <![CDATA[ >= ]]> NOW()
            and tu.USER_ID=#{userId}
            and tmpt.SEND_MSG=0
    </select>





</mapper>