package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.KingDeeCustomerEntity;
import com.vedeng.erp.kingdee.dto.KingDeeCustomerDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface KingDeeCustomerMapper {
    /**
     * delete by primary key
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(KingDeeCustomerEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(KingDeeCustomerEntity record);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    KingDeeCustomerEntity selectByPrimaryKey(Integer id);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(KingDeeCustomerEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(KingDeeCustomerEntity record);

    int updateBatchSelective(List<KingDeeCustomerEntity> list);

    int batchInsert(@Param("list") List<KingDeeCustomerEntity> list);

    KingDeeCustomerEntity selectByFNumber(@Param("fNumber") Integer fNumber);

    /**
     * <AUTHOR>
     * @desc 根据客户id查询金蝶信息
     * @param traderCustomerId
     * @return
     */
    KingDeeCustomerDto queryInfoByCustomerId(Integer traderCustomerId);

    /**
     * 根据客户名称查询编码
     * <AUTHOR>
     * @param traderCustomerName
     * @return
     */
    KingDeeCustomerEntity queryInfoByCustomerName(String traderCustomerName);

    String  queryFCUSTIDByCustomerName(String traderCustomerName);


    String  queryFNUMBERByCustomerName(String traderCustomerName);

}