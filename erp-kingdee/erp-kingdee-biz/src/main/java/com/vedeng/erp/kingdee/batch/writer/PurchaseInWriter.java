package com.vedeng.erp.kingdee.batch.writer;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeePurchaseReceiptDto;
import com.vedeng.erp.kingdee.service.KingDeePurchaseReceiptApiService;
import com.vedeng.erp.kingdee.service.KingDeePurchaseReceiptService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购入库单
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class PurchaseInWriter extends BaseWriter<KingDeePurchaseReceiptDto> {

    @Autowired
    private KingDeePurchaseReceiptApiService kingDeePurchaseReceiptApiService;

    @Override
    public void doWrite(KingDeePurchaseReceiptDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("采购入库单 write:{}",JSON.toJSONString(dto));
        dto.setKingDeeBizEnums(KingDeeBizEnums.savePurchaseReceipt);
        kingDeePurchaseReceiptApiService.register(dto,true);
    }


}
