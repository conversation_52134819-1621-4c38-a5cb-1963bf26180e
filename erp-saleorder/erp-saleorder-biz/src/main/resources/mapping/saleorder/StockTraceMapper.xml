<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.erp.saleorder.dao.StockTraceMapper">

    <resultMap id="BaseResultMap" type="com.vedeng.erp.saleorder.model.dto.WarehouseGoodsOperateLog">
        <id column="WAREHOUSE_GOODS_OPERATE_LOG_ID"
            property="warehouseGoodsOperateLogId" jdbcType="INTEGER" />
        <result column="BARCODE_ID" property="barcodeId"
                jdbcType="INTEGER" />
        <result column="COMPANY_ID" property="companyId"
                jdbcType="INTEGER" />
        <result column="OPERATE_TYPE" property="operateType"
                jdbcType="BIT" />
        <result column="RELATED_ID" property="relatedId"
                jdbcType="INTEGER" />
        <result column="GOODS_ID" property="goodsId" jdbcType="INTEGER" />
        <result column="BARCODE_FACTORY" property="barcodeFactory"
                jdbcType="VARCHAR" />
        <result column="NUM" property="num" jdbcType="INTEGER" />
        <result column="WAREHOUSE_ID" property="warehouseId"
                jdbcType="INTEGER" />
        <result column="BATCH_NUMBER" property="batchNumber"
                jdbcType="VARCHAR" />
        <result column="EXPIRATION_DATE" property="expirationDate"
                jdbcType="BIGINT" />
        <result column="COMMENTS" property="comments"
                jdbcType="VARCHAR" />
        <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
        <result column="CREATOR" property="creator" jdbcType="INTEGER" />
        <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
        <result column="UPDATER" property="updater" jdbcType="INTEGER" />
        <result column="IS_ENABLE" property="isEnable" jdbcType="BIT" />
        <result column="SKU" property="sku" jdbcType="VARCHAR" />
        <result column="COST_PRICE" property="costPrice" jdbcType="DECIMAL"/>
        <result column="LOGICAL_WAREHOUSE_ID" property="logicalWarehouseId" jdbcType="INTEGER"/>
        <result column="LAST_STOCK_NUM" property="lastStockNum" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        WAREHOUSE_GOODS_OPERATE_LOG_ID, BARCODE_ID, COMPANY_ID, OPERATE_TYPE, RELATED_ID,
		GOODS_ID, BARCODE_FACTORY, NUM, WAREHOUSE_ID, STORAGE_ROOM_ID, STORAGE_AREA_ID,
		STORAGE_LOCATION_ID,
		STORAGE_RACK_ID, BATCH_NUMBER, EXPIRATION_DATE, CHECK_STATUS, CHECK_STATUS_USER,
		CHECK_STATUS_TIME, RECHECK_STATUS, RECHECK_STATUS_USER,
		RECHECK_STATUS_TIME, COMMENTS,
		ADD_TIME, CREATOR, MOD_TIME, UPDATER,IS_ENABLE, DEDICATED_BUYORDER_NO
    </sql>
    <select id="getSaleByBuorderSku" resultType="com.vedeng.erp.saleorder.dto.StockTraceResult">
        SELECT
            A.BUYORDER_NO originalOrderNo,
            B.SKU,
            FROM_UNIXTIME( B.ARRIVAL_TIME / 1000, '%Y-%m-%d' ) originalTime,
            SA.SALEORDER_NO tagerOrderNo,
            SG.SKU,
            FROM_UNIXTIME( SG.DELIVERY_TIME / 1000, '%Y-%m-%d' ) tagerTime
        FROM
            T_BUYORDER A
                LEFT JOIN T_BUYORDER_GOODS B ON A.BUYORDER_ID = B.BUYORDER_ID
                LEFT JOIN T_R_BUYORDER_J_SALEORDER C ON B.BUYORDER_GOODS_ID=C.BUYORDER_GOODS_ID
                LEFT JOIN T_SALEORDER_GOODS SG ON C.SALEORDER_GOODS_ID=SG.SALEORDER_GOODS_ID
                LEFT JOIN T_SALEORDER SA ON SA.SALEORDER_ID=SG.SALEORDER_ID
        WHERE 1=1
          <choose>
              <when test="type == sale">
                  AND SA.SALEORDER_NO = #{orderNo}
              </when>
              <when test="type == buy">
                  AND A.BUYORDER_NO = #{orderNo}
              </when>
              <otherwise>
                  0 > 1
              </otherwise>
          </choose>
          AND B.SKU = #{sku}
          AND SG.IS_DELETE = 0
    </select>
    <select id="getSaleSkuDeliverDirect" resultType="java.lang.Integer">
        SELECT
            SG.DELIVERY_DIRECT
        FROM
            T_SALEORDER SA
                LEFT JOIN T_SALEORDER_GOODS SG ON SA.SALEORDER_ID = SG.SALEORDER_ID
        WHERE 1=1
          AND SG.SKU=#{sku}
          AND SA.SALEORDER_NO=#{orderNo}
          AND SG.IS_DELETE = 0
    </select>
    <select id="getInWarehouseList" resultType="com.vedeng.erp.saleorder.dto.WarehouseLog">
    SELECT
        A.WAREHOUSE_GOODS_OPERATE_LOG_ID logId,
        A.BARCODE_ID barcodeId,
        (CASE A.OPERATE_TYPE
             WHEN 1 THEN
                 C.BUYORDER_NO
             WHEN 3 THEN
                 ASS.AFTER_SALES_NO
             WHEN 5 THEN
                 ASS.AFTER_SALES_NO
             WHEN 8 THEN
                 ASS.AFTER_SALES_NO
             WHEN 9 THEN
                 IF(L.LEND_OUT_NO IS NOT NULL,L.LEND_OUT_NO,OO.order_no)
             WHEN 12 THEN
                 IO.ORDER_NO
             WHEN 11 THEN
                 IA.INVENTORY_ADJUSTMENT_NO
             ELSE '' END) AS orderNo,
            CASE A.OPERATE_TYPE
                WHEN 1 THEN '采购入库'
                WHEN 2 THEN '销售出库'
                WHEN 3 THEN '销售换货入库'
                WHEN 4 THEN '销售换货出库'
                WHEN 5 THEN '销售退货入库'
                WHEN 6 THEN '采购退货出库'
                WHEN 7 THEN '采购换货出库'
                WHEN 8 THEN '采购换货入库'
                WHEN 9 THEN '外借入库'
                WHEN 10 THEN '外借出库'
                WHEN 11 THEN '调整盘盈入库'
                WHEN 12 THEN '盘盈入库'
                WHEN 13 THEN '报废出库'
                WHEN 14 THEN '领用出库'
                ELSE ''
                END  AS  orderType,
            AB.SKU_NO SKU,
            A.BARCODE_FACTORY ,
            ABS(A.NUM) NUM,
            A.NEW_COST_PRICE costPrice,
            ABS(A.NUM) * A.NEW_COST_PRICE amount,
            A.BATCH_NUMBER ,
            FROM_UNIXTIME( A.ADD_TIME / 1000, '%Y-%m-%d' ) AS addTime
        FROM
            T_WAREHOUSE_GOODS_OPERATE_LOG A
            LEFT JOIN V_CORE_SKU AB ON AB.SKU_ID = A.GOODS_ID
            LEFT JOIN V_CORE_SPU BB ON AB.SPU_ID = BB.SPU_ID
            LEFT JOIN T_BUYORDER_GOODS B ON B.BUYORDER_GOODS_ID = A.RELATED_ID AND B.IS_DELETE = 0
            LEFT JOIN T_BUYORDER C ON B.BUYORDER_ID = C.BUYORDER_ID AND C.VALID_STATUS = 1 AND A.OPERATE_TYPE=1
            LEFT JOIN T_AFTER_SALES_GOODS ASG ON ASG.AFTER_SALES_GOODS_ID=A.RELATED_ID AND A.OPERATE_TYPE IN (3,5,8)
            LEFT JOIN T_AFTER_SALES ASS ON ASG.AFTER_SALES_ID=ASS.AFTER_SALES_ID
            LEFT JOIN T_AFTER_SALES_DETAIL ASD ON ASD.AFTER_SALES_ID = ASS.AFTER_SALES_ID
            LEFT JOIN T_TRADER AST ON AST.TRADER_ID= ASD.TRADER_ID
            LEFT JOIN T_TRADER T ON C.TRADER_ID=T.TRADER_ID
            LEFT JOIN T_WMS_OUTPUT_ORDER_GOODS OOG ON OOG.id=A.RELATED_ID AND A.OPERATE_TYPE = 9 AND OOG.SKU_NO=AB.SKU_NO
            LEFT JOIN T_WMS_OUTPUT_ORDER OO ON OOG.wms_output_order_id=OO.id
            LEFT JOIN T_LEND_OUT L ON A.RELATED_ID = L.LEND_OUT_ID  AND A.OPERATE_TYPE = 9 AND A.GOODS_ID = L.GOODS_ID
            LEFT JOIN T_WMS_INPUT_ORDER_GOODS IOG ON IOG.WMS_INPUT_ORDER_GOODS_ID = A.RELATED_ID AND A.OPERATE_TYPE=12
            LEFT JOIN T_WMS_INPUT_ORDER IO ON IOG.WMS_INPUT_ORDER_ID=IO.WMS_INPUT_ORDER_ID
            LEFT JOIN T_WMS_INVENTORY_ADJUSTMENT_DETAIL IAD ON IAD.INVENTORY_ADJUSTMENT_DETAIL_ID = A.RELATED_ID AND A.OPERATE_TYPE = 11
            LEFT JOIN T_WMS_INVENTORY_ADJUSTMENT IA ON IA.INVENTORY_ADJUSTMENT_ID = IAD.INVENTORY_ADJUSTMENT_ID
            LEFT JOIN T_BARCODE BR ON BR.BARCODE_ID = A.BARCODE_ID
        WHERE
            A.LOG_TYPE = 0
          AND A.IS_ENABLE=1
          AND A.NUM > 0
          AND (ASS.AFTER_SALES_NO= #{orderNo} OR ASS.AFTER_SALES_NO=#{orderNo} OR OO.order_no=#{orderNo}
                   OR IO.ORDER_NO=#{orderNo} OR IA.INVENTORY_ADJUSTMENT_NO=#{orderNo} OR L.LEND_OUT_NO=#{orderNo}
                   OR C.BUYORDER_NO=#{orderNo} )
          AND AB.SKU_NO = #{sku}
        GROUP BY A.WAREHOUSE_GOODS_OPERATE_LOG_ID
    </select>
    <select id="getOutWarehouseListByBarcodeId" resultType="com.vedeng.erp.saleorder.dto.WarehouseLog">
        
    </select>

</mapper>
