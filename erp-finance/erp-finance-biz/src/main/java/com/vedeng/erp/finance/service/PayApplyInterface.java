package com.vedeng.erp.finance.service;

import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.finance.dto.PayApplyCheckDto;
import com.vedeng.erp.finance.dto.PayApplyCreateBillDto;

/**
 * 付款申请接口
 */
public interface PayApplyInterface {

    /**
     * 自动创建付款申请单
     * @param payApplyCreateBillDto
     */
    void autoCreateCreateBill(PayApplyCreateBillDto payApplyCreateBillDto) throws ServiceException;

    /**
     * 付款申请规则校验
     * @param payApplyCheckDto
     */
    default void payApplyRuleCheck(PayApplyCheckDto payApplyCheckDto)throws ServiceException{

    }

    void createBillRuleCheck(PayApplyCreateBillDto payApplyCreateBillDto) throws ServiceException;
}
