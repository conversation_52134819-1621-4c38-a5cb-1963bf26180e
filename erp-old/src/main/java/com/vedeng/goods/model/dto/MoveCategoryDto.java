package com.vedeng.goods.model.dto;

public class MoveCategoryDto {
    private Integer spuId;
    private String spuName;
    private Integer skuCount;
    private Integer oldFirstCategoryId;
    private Integer oldSecondCategoryId;
    private Integer oldThirdCategoryId;
    private String oldFirstCategoryName;
    private String oldSecondCategoryName;
    private String oldThirdCategoryName;
    private Integer newFirstCategoryId;
    private Integer newSecondCategoryId;
    private Integer newThirdCategoryId;
    private String newFirstCategoryName;
    private String newSecondCategoryName;
    private String newThirdCategoryName;

    public Integer getSpuId() {
        return spuId;
    }

    public void setSpuId(Integer spuId) {
        this.spuId = spuId;
    }

    public String getSpuName() {
        return spuName;
    }

    public void setSpuName(String spuName) {
        this.spuName = spuName;
    }

    public Integer getSkuCount() {
        return skuCount;
    }

    public void setSkuCount(Integer skuCount) {
        this.skuCount = skuCount;
    }

    public Integer getOldFirstCategoryId() {
        return oldFirstCategoryId;
    }

    public void setOldFirstCategoryId(Integer oldFirstCategoryId) {
        this.oldFirstCategoryId = oldFirstCategoryId;
    }

    public Integer getOldSecondCategoryId() {
        return oldSecondCategoryId;
    }

    public void setOldSecondCategoryId(Integer oldSecondCategoryId) {
        this.oldSecondCategoryId = oldSecondCategoryId;
    }

    public Integer getOldThirdCategoryId() {
        return oldThirdCategoryId;
    }

    public void setOldThirdCategoryId(Integer oldThirdCategoryId) {
        this.oldThirdCategoryId = oldThirdCategoryId;
    }


    public Integer getNewFirstCategoryId() {
        return newFirstCategoryId;
    }

    public void setNewFirstCategoryId(Integer newFirstCategoryId) {
        this.newFirstCategoryId = newFirstCategoryId;
    }

    public Integer getNewSecondCategoryId() {
        return newSecondCategoryId;
    }

    public void setNewSecondCategoryId(Integer newSecondCategoryId) {
        this.newSecondCategoryId = newSecondCategoryId;
    }

    public Integer getNewThirdCategoryId() {
        return newThirdCategoryId;
    }

    public void setNewThirdCategoryId(Integer newThirdCategoryId) {
        this.newThirdCategoryId = newThirdCategoryId;
    }

    public String getOldFirstCategoryName() {
        return oldFirstCategoryName;
    }

    public void setOldFirstCategoryName(String oldFirstCategoryName) {
        this.oldFirstCategoryName = oldFirstCategoryName;
    }

    public String getOldSecondCategoryName() {
        return oldSecondCategoryName;
    }

    public void setOldSecondCategoryName(String oldSecondCategoryName) {
        this.oldSecondCategoryName = oldSecondCategoryName;
    }

    public String getOldThirdCategoryName() {
        return oldThirdCategoryName;
    }

    public void setOldThirdCategoryName(String oldThirdCategoryName) {
        this.oldThirdCategoryName = oldThirdCategoryName;
    }

    public String getNewFirstCategoryName() {
        return newFirstCategoryName;
    }

    public void setNewFirstCategoryName(String newFirstCategoryName) {
        this.newFirstCategoryName = newFirstCategoryName;
    }

    public String getNewSecondCategoryName() {
        return newSecondCategoryName;
    }

    public void setNewSecondCategoryName(String newSecondCategoryName) {
        this.newSecondCategoryName = newSecondCategoryName;
    }

    public String getNewThirdCategoryName() {
        return newThirdCategoryName;
    }

    public void setNewThirdCategoryName(String newThirdCategoryName) {
        this.newThirdCategoryName = newThirdCategoryName;
    }

    @Override
    public String toString() {
        return "MoveCategoryDto{" +
                "spuId=" + spuId +
                ", spuName='" + spuName + '\'' +
                ", skuCount=" + skuCount +
                ", oldFirstCategoryId=" + oldFirstCategoryId +
                ", oldSecondCategoryId=" + oldSecondCategoryId +
                ", oldThirdCategoryId=" + oldThirdCategoryId +
                ", oldFirstCategoryName='" + oldFirstCategoryName + '\'' +
                ", oldSecondCategoryName='" + oldSecondCategoryName + '\'' +
                ", oldThirdCategoryName='" + oldThirdCategoryName + '\'' +
                ", newFirstCategoryId=" + newFirstCategoryId +
                ", newSecondCategoryId=" + newSecondCategoryId +
                ", newThirdCategoryId=" + newThirdCategoryId +
                ", newFirstCategoryName='" + newFirstCategoryName + '\'' +
                ", newSecondCategoryName='" + newSecondCategoryName + '\'' +
                ", newThirdCategoryName='" + newThirdCategoryName + '\'' +
                '}';
    }
}
