package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶  采购增值税 专票 发票明细
 * @date 2022/11/10 09:13
 */
@NoArgsConstructor
@Data
public class PurchaseVatSpecialInvoiceDetailDto{

    /**
     * 金蝶回写id
     */
    @WriteBackField(value = "FEntryID",backWriteByErpField = "fQzokBddjhid",backWriteToKingDeeField = "F_QZOK_BDDJHID")
    private String fEntryId;

    /**
     * fmaterialid
     */
    private String fmaterialid;
    /**
     * fpriceqty
     */
    private String fpriceqty;
    /**
     * fauxtaxprice
     */
    private BigDecimal fauxtaxprice;
    /**
     * ftaxrate
     */
    private String ftaxrate;
    /**
     * 不含税金额
     */
    private BigDecimal famountfor;
    /**
     * 税额
     */
    private BigDecimal fdetailtaxamountfor;
    /**
     * 价税合计
     */
    private BigDecimal fallamountfor;
    /**
     * 贝登单据行ID
     */
    private String fQzokBddjhid;
    /**
     * 原始订单号
     */
    private String fQzokYsddh;
    /**
     * 归属业务单号
     */
    private String fQzokGsywdh;
    /**
     * 业务类型
     */
    private String fQzokYwlx;
    /**
     * 源单类型
     * 应付单关联  ：AP_Payable
     * 蓝字发票关联：IV_PURCHASEIC
     */
    private String fsourcetype;
    /**
     * fpurchaseicentryLink
     */
    private List<PurchaseVatSpecialInvoiceDetailLinkDto> FPURCHASEICENTRY_Link;
}
