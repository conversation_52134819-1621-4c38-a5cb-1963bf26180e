package com.vedeng.erp.trader.domain.dto;

import com.vedeng.erp.trader.domain.PublicCustomerCalculateRules;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.trader.domain.dto
 * @Date 2022/2/16 14:04
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PublicCustomerCalculateRulesChangeDto extends PublicCustomerCalculateRules {

    /**
     * 创建距今的天数 原值
     */
    private Integer oldCustomerCreatedDays;

    /**
     * 生效订单的天数  原值
     */
    private Integer oldValidOrderDays;

    /**
     * 生效订单的个数 原值
     */
    private Integer oldValidOrderCount;

    /**
     * 生效订单的计算符，1：小于，2：等于 原值
     */
    private Integer oldValidOrderCalculator;

    /**
     * 沟通的天数 原值
     */
    private Integer oldCommunicationDays;

    /**
     * 解锁保护期 原值
     */
    private Integer oldLockProtectDays;

    /**
     * 沟通的次数 原值
     */
    private Integer oldCommunicationCount;

    /**
     * 沟通次数的计算符，1：小于，2：等于 原值
     */
    private Integer oldCommunicationCalculator;

    /**
     * 修改人名称
     */
    private String creatorName;

}
