<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.erp.trader.mapper.PublicTraderMapper">
    <update id="updatePrivatizedAndTimeInIdList">
        update T_PUBLIC_CUSTOMER_RECORD
        set IS_PRIVATIZED = 1,LOCK_USER_NAME = #{lockUserName,jdbcType=VARCHAR},
        PRIVATIZED_TIME = unix_timestamp() * 1000
        where IS_PRIVATIZED = 0
        and TRADER_CUSTOMER_ID in
        <foreach collection="traderCustomerIdList" open="(" close=")" separator="," index="index"
                 item="traderCustomerId">#{traderCustomerId,jdbcType=INTEGER}
        </foreach>
    </update>
    <update id="cancelLockById">
        update T_PUBLIC_CUSTOMER_RECORD
        set IS_PRIVATIZED = 0,
        PRIVATIZED_TIME = unix_timestamp() * 1000
        where IS_PRIVATIZED = 1 and PUBLIC_CUSTOMER_RECORD_ID = #{publicTraderRecordId,jdbcType=INTEGER}
    </update>
    <update id="updatePublicCustomerRecordByPrimaryKey">
        UPDATE T_PUBLIC_CUSTOMER_RECORD
        SET IS_UNLOCK         = #{isUnlock,jdbcType=INTEGER},
            UNLOCK_TIME       = #{unlockTime},
            UNLOCK_RELATED_ID = #{unlockRelatedId,jdbcType=INTEGER}
        WHERE PUBLIC_CUSTOMER_RECORD_ID = #{publicTraderRecordId,jdbcType=INTEGER}
    </update>


    <select id="getWaitingForEnterPublicCustomerRecords"
            resultType="com.vedeng.erp.trader.domain.dto.TraderCustomerDto">
        SELECT
            T1.TRADER_CUSTOMER_ID,
            T1.TRADER_ID,
            T3.USER_ID
        FROM
            T_TRADER_CUSTOMER T1
            JOIN T_TRADER T2 ON T1.TRADER_ID = T2.TRADER_ID
            LEFT JOIN T_R_TRADER_J_USER T3 ON T1.TRADER_ID = T3.TRADER_ID AND T3.TRADER_TYPE = 1
        WHERE
            T2.BELONG_PLATFORM = 1
            AND T1.PUBLIC_CUSTOMER_EARLY_WARNING_COUNT >= 10
        <if test="traderCustomerIdList != null and traderCustomerIdList.size > 0">
            AND T1.TRADER_CUSTOMER_ID IN
            <foreach collection="traderCustomerIdList" item="item" index="index" open="(" close=")" separator=",">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
    </select>

    <select id="selectRegionIdListByUserIdList" resultType="java.lang.Integer">
        select
        distinct REGION_ID
        from T_PUBLIC_CUSTOMER_REGION_RULES
        <if test="userIdList != null and userIdList.size > 0">
            where
            USER_ID in
            <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                #{userId,jdbcType=INTEGER}
            </foreach>
        </if>
    </select>
    <select id="selectSalesByRegionIdList" resultType="java.lang.Integer">
        select
        distinct USER_ID
        from T_PUBLIC_CUSTOMER_REGION_RULES
        <if test="typeEqThreeRegionIdListByRegionIdList != null and typeEqThreeRegionIdListByRegionIdList.size > 0">
            where
            REGION_ID in
            <foreach collection="typeEqThreeRegionIdListByRegionIdList" item="regionId" index="index" open="(" close=")"
                     separator=",">
                #{regionId,jdbcType=INTEGER}
            </foreach>
        </if>
    </select>
    <select id="selectTraderIdListBySalesUserIdList" resultType="java.lang.Integer">
        select
        TRADER_CUSTOMER_ID
        from T_PUBLIC_CUSTOMER_RECORD
        where IS_PRIVATIZED = 0
        and ORIGIN_USER_ID in
        <foreach collection="salesByRegionIdList" index="index" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>
    <select id="selectByPrimaryKey" resultType="com.vedeng.erp.trader.domain.PublicCustomerRecord">
        select PUBLIC_CUSTOMER_RECORD_ID,
               TRADER_CUSTOMER_ID,
               ORIGIN_USER_ID,
               IS_PRIVATIZED,
               PRIVATIZED_TIME,
               REVOCATION_PROTECTION_DEADLINE,
               IS_UNLOCK,
               UNLOCK_TIME,
               UNLOCK_RELATED_ID,
               ADD_TIME,
               MOD_TIME,
               UPDATER
        from T_PUBLIC_CUSTOMER_RECORD
        where
            PUBLIC_CUSTOMER_RECORD_ID = #{publicCustomerRecordId,jdbcType=INTEGER}
    </select>
    <select id="selectByTraderCustomerIdAndStates" resultType="java.lang.Integer">
        select PUBLIC_CUSTOMER_RECORD_ID
        from T_PUBLIC_CUSTOMER_RECORD
        where TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER}
        and IS_PRIVATIZED = 1
        and IS_UNLOCK = 0
        and unix_timestamp() * 1000 >= PRIVATIZED_TIME
        order by ADD_TIME desc
        limit 1
    </select>

    <select id="getTraderCustomerByTraderCustomerId" resultType="com.vedeng.erp.trader.domain.dto.TraderCustomerDto">
        select TRADER_CUSTOMER_ID,TRADER_ID,ASSOCIATED_CUSTOMER_GROUP from T_TRADER_CUSTOMER where TRADER_CUSTOMER_ID IN
        <foreach collection="traderCustomerIdList" item="item" index="index" open="(" close=")" separator=",">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>
    <select id="getTraderCustomerIdListByAssociateGroup" resultType="java.lang.Integer">
        select TRADER_CUSTOMER_ID from T_TRADER_CUSTOMER where ASSOCIATED_CUSTOMER_GROUP IN
        <foreach collection="associateGroupList" item="item" index="index" open="(" close=")" separator=",">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>
    <update id="cancelPublicTrader">
        update T_PUBLIC_CUSTOMER_RECORD
        set IS_PRIVATIZED = 2,
        PRIVATIZED_TIME = #{privatizedTime,jdbcType=BIGINT},
        REVOCATION_PROTECTION_DEADLINE = #{revocationProtectionDeadline,jdbcType=BIGINT}
        where IS_PRIVATIZED = 0
        and TRADER_CUSTOMER_ID in
        <foreach collection="traderCustomerIdList" open="(" close=")" separator="," index="index"
                 item="traderCustomerId">#{traderCustomerId,jdbcType=INTEGER}
        </foreach>
    </update>
</mapper>
