package com.vedeng.erp.kingdee.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import com.vedeng.common.mybatis.jbatis.annotation.Column;
import com.vedeng.common.mybatis.jbatis.annotation.GeneratedValue;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.common.mybatis.jbatis.annotation.Table;
import com.vedeng.infrastructure.kingdee.annotation.BusinessID;
import lombok.*;

/**
    * 收款退款单
    */
@Getter
@Setter
@ToString
@Table(name = "KING_DEE_RECEIVE_REFUND_BILL")
public class KingDeeReceiveRefundBill extends BaseEntity {
    /**
    * 主键
    */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer kingDeeReceiveRefundBillId;

    /**
    * FID值
    */
    private String fId;

    /**
    * 单据编号
    */
    @BusinessID
    private String fBillNo;

    /**
    * 单据类型id
    */
    private String fBillTypeId;

    /**
    * 单据日期
    */
    private String fDate;

    /**
    * 往来单位类型
    */
    private String fContactUnitType;

    /**
    * 往来单位
    */
    private String fContactUnit;

    /**
    * 收款单位类型
    */
    private String fRectUnitType;

    /**
    * 收款单位
    */
    private String fRectUnit;

    /**
    * 结算组织
    */
    private String fSettleOrgId;

    /**
    * 销售组织
    */
    private String fSaleOrgId;

    /**
    * 收款组织
    */
    private String fPayOrgId;

    /**
    * 业务类型
    */
    private String fBusinessType;

    /**
    * 流水号
    */
    private String fQzokLsh;

    /**
    * 流水号
    */
    private String fQzokBddjtid;

    /**
    * 明细
    */
    @Column(jdbcType = "VARCHAR")
    private String fRefundBillEntry;

    /**
     * 自动提交银行
     */
    private String fQzokZdtjyh;
}