package com.vedeng.erp.trader.mapper;

import com.vedeng.erp.trader.domain.entity.RegistrationProductionModeEntity;
import com.vedeng.erp.trader.dto.RegistrationProductionModeDto;

import java.util.List;

public interface RegistrationProductionModeMapper {
    /**
     * delete by primary key
     *
     * @param productionModeId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer productionModeId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(RegistrationProductionModeEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(RegistrationProductionModeEntity record);

    /**
     * select by primary key
     *
     * @param productionModeId primary key
     * @return object by primary key
     */
    RegistrationProductionModeEntity selectByPrimaryKey(Integer productionModeId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(RegistrationProductionModeEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(RegistrationProductionModeEntity record);

    List<RegistrationProductionModeDto> queryRegistrationProductionMode(Integer registrationNumberId);

    int deleteByRegistrationNumberId(Integer registrationNumberId);
}