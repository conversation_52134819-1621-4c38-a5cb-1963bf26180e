package com.vedeng.kpi.canal.sync;

import org.apache.ibatis.jdbc.SQL;
import org.apache.ibatis.jdbc.SqlBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @date created in 2020/6/11 17:41
 */
public class DynamicSql {


    public static String insertSql(String tableName, List<String> column, List<String> values){
        SQL sql = new SQL()
                .INSERT_INTO(tableName)
                .VALUES(String.join(",",column),String.join(",",values));
        return sql.toString();
    }



    public static String updateSql(String tableName, String[] array , String condition){
        SQL sql = new SQL().UPDATE(tableName);
        for (String s : array) {
            sql.SET(s);
        }
        sql.WHERE(condition);
        return sql.toString();

    }


    public static String deleteSql(String tableName, String condition){
        return new SQL()
                .DELETE_FROM(tableName)
                .WHERE(condition)
                .toString();
    }

    public static String selectByIdSql(String tableName, String primaryKey, String primaryKeyValue){
        return "SELECT COUNT(*) FROM " +
                tableName +
                " WHERE " +
                primaryKey +
                "=" +
                primaryKeyValue;
    }

    public static String selectColumnsOfTable(String tableName){
        return "SELECT DISTINCT COLUMN_NAME from information_schema.COLUMNS " +
                "where TABLE_NAME = '"
                + tableName + "'";
    }
}
