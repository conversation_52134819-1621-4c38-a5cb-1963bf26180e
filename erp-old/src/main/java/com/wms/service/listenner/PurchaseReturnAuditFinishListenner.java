package com.wms.service.listenner;

import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SpecialDeliveryEnum;
import com.vedeng.common.util.DateUtil;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.order.model.vo.SaleorderGoodsVo;
import com.wms.constant.CancelReasonConstant;
import com.wms.constant.LogicalEnum;
import com.wms.constant.WMSContant;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.dto.*;
import com.wms.service.*;
import com.wms.service.chain.Build.StepBuildFactory;
import com.wms.service.chain.HandlerStep;
import com.wms.service.chain.HandlerStepContext;
import com.wms.service.stockallocation.PurchaseStockAllocationStrategy;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * ERP采购退货单审核完成监听器
 */
@Service
public class PurchaseReturnAuditFinishListenner extends AbstractErpListenner {

    public static Logger logger = LoggerFactory.getLogger(PurchaseReturnAuditFinishListenner.class);

    @Autowired
    private WMSInterfaceFactory wmsInterfaceFactory;

    @Resource
    private BuyorderMapper buyorderMapper;

    @Resource
    private BuyorderGoodsMapper buyorderGoodsMapper;

    @Resource
    private AfterSalesGoodsMapper afterSalesGoodsMapper;

    @Resource
    private PurchaseStockAllocationStrategy stockAllocationStrategy;

    @Resource
    private AfterSalesService afterSalesService;

    @Resource
    private UserMapper userMapper;

    @Autowired
    private StepBuildFactory stepBuildFactory;

    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Resource
    private LogicalSaleorderChooseService logicalSaleorderChooseService;

    @Autowired
    private CancelTypeService cancelTypeService;

    @Autowired
    private DoPutService doPutService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    protected void doOnActionHappen(Object[] params) throws Exception{

        AfterSalesVo afterSalesInfo = (AfterSalesVo)params[0];

        boolean auditPass = (boolean)params[1];

        User user = (User)params[2];

        Buyorder buyorder = buyorderMapper.selectByPrimaryKey(afterSalesInfo.getOrderId());

        logger.info("采购单:"+buyorder.getBuyorderNo()+",的退货单:"+afterSalesInfo.getAfterSalesNo()+"审核完成,start======================");

        //审核不通过，重新下发采购单
        if(!auditPass){

            logger.info("采购单:"+buyorder.getBuyorderNo()+",的退货单:"+afterSalesInfo.getAfterSalesNo()+"审核不通过,重新下发采购单,start======================");


            //做个校验

            /*List<PutPurchaseOrderGoodsDto> details = new ArrayList<>();

            buyorderGoodsMapper.getBuyorderGoodsVoListByBuyorderIdNoSpecial(buyorder.getBuyorderId()).stream().forEach(buyOrderGood -> {

                PutPurchaseOrderGoodsDto detailItem = new PutPurchaseOrderGoodsDto();
                detailItem.setSku(buyOrderGood.getSku());
                detailItem.setDedi04(buyOrderGood.getBuyorderGoodsId() + StringUtils.EMPTY);

                //采购单数量 - 采购单到货数量 - 已完结售后数量
                detailItem.setOrderedQty(buyOrderGood.getNum() -
                                         buyOrderGood.getArrivalNum() -
                                         afterSalesService.getFinishAfterSaleNum(buyOrderGood.getBuyorderGoodsId()));

                if (SpecialDeliveryEnum.NEED_SPECIAL_DELIVERY.getCode().equals(
                        buyorderGoodsMapper.getSpecialDeliveryByRelatedId(buyOrderGood.getBuyorderGoodsId()))){
                    detailItem.setLotAtt07(buyorder.getBuyorderNo());
                }

                if(detailItem.getOrderedQty() > 0){
                    details.add(detailItem);
                }
            });*/

//            sendPurchaseOrder(buyorder,details);
            doPutService.doPutPurchaseOrderMethod(buyorder.getBuyorderId(),buyorder.getLogisticsComments());
        //审核通过
        }else{

            logger.info("采购单:"+buyorder.getBuyorderNo()+",的退货单:"+afterSalesInfo.getAfterSalesNo()+"审核通过,下发出库单start======================");

            List<InputOrderGoodsDto> inputOrderGoodsList = new ArrayList<>();

            List<PutSaleOrderGoodsDto> outputOrderGoodsList = new ArrayList<>();

            List<AfterSalesGoodsVo> afterSalesGoodList =
                    this.afterSalesGoodsMapper.getAfterSalesGoodList(afterSalesInfo.getAfterSalesId());

            //处理采购单中每个商品
            List<BuyorderGoodsVo> buyorderGoodsVoList = buyorderGoodsMapper.getBuyorderGoodsVoListByBuyorderIdNoSpecial(buyorder.getBuyorderId());
            for(BuyorderGoodsVo buyorderGood : buyorderGoodsVoList){
                dealWithBuyorderGoods(inputOrderGoodsList,outputOrderGoodsList,buyorderGood,afterSalesGoodList,buyorder);
            }

            //新流程 采购单审核通过不下发
            /*if(CollectionUtils.isNotEmpty(inputOrderGoodsList)){

                List<PutPurchaseOrderGoodsDto> details = new ArrayList<>();

                inputOrderGoodsList.stream().forEach(inputOrderGoodsDto -> {
                    PutPurchaseOrderGoodsDto detailItem = new PutPurchaseOrderGoodsDto();
                    detailItem.setSku(inputOrderGoodsDto.getSKU());
                    detailItem.setOrderedQty(inputOrderGoodsDto.getReceivedQty().intValue());
                    details.add(detailItem);
                });

                //下发wms入库单请求
                sendPurchaseOrder(buyorder,details,user);
            }*/

            //出库单请求
            if(CollectionUtils.isNotEmpty(outputOrderGoodsList)){

                logger.info("采购单商品:"+buyorder.getBuyorderNo()+"下发WMS出库任务请求,start======================");

                HandlerStepContext handlerStepContext = new HandlerStepContext();
                handlerStepContext.put("user",user);
                handlerStepContext.put("afterSalesInfo",afterSalesInfo);
                handlerStepContext.put("outputOrderGoodsList",outputOrderGoodsList);
                handlerStepContext.put("buyOrder",buyorder);

                HandlerStep handlerStep = stepBuildFactory.buildPurchaseReturnOutStep();
                handlerStep.dealWith(handlerStepContext);
            }

            //查看VP采购单 关联的销售单是否是专项发货单  如果是触发对应的销售单下传
            if(ErpConst.ZERO.equals(buyorder.getOrderType())) {

                //查询采购单关联的销售单
                List<Integer> saleOrderIdList = saleorderMapper.getRelateSaleOrderId(buyorder.getBuyorderId());

                Buyorder specialBuyorder = buyorderMapper.selectByPrimaryKey(buyorder.getBuyorderId());
                logger.info("专项发货采购单售后退货审核通过，取消销售出库wms，{},",specialBuyorder);
                //如果销售单是包含专项发货的 就再次下发销售单
                for (Integer saleOrderId : saleOrderIdList) {
                    try {
                        if (hasSpecialDeliveryGoodByOrderId(saleOrderId)) {
                            //包含专项发货
                            //先取消出库任务
                            Saleorder saleorder = saleorderMapper.selectBySaleOrderId(saleOrderId);
//                            logicalSaleorderChooseService.cancelOutByNo(saleorder.getSaleorderNo(), CancelReasonConstant.SEND_ORDER);TODO HOLLIS ok
                            cancelTypeService.cancelOutSaleOutMethod(saleorder.getSaleorderNo(), CancelReasonConstant.SEND_ORDER);
                            //重新下传出库任务
                            Saleorder order = saleorderMapper.getSaleOrderById(saleOrderId);
                            logicalSaleorderChooseService.chooseLogicalSaleorder(order, getUser());
                        }
                    } catch (Exception e) {
                        logger.error("专项发货单:"+saleOrderId+",下发WMS失败:",e);
                    }
                }
            }
        }

    }

    private void dealWithBuyorderGoods(List<InputOrderGoodsDto> inputOrderGoodsList,
                                       List<PutSaleOrderGoodsDto> outputOrderGoodsList,
                                       BuyorderGoodsVo buyOrderGood,
                                       List<AfterSalesGoodsVo> afterSalesGoodList, Buyorder buyorder) throws Exception{

        //售后商品
        AfterSalesGoodsVo afterSalesGoods = afterSalesGoodList.stream()
                                                              .filter(afterSalesGood -> afterSalesGood.getSku().equals(buyOrderGood.getSku()))
                                                              .findFirst()
                                                              .orElse(null);
        //该采购单中的商品没有发生售后,重新下发入库单
        if(afterSalesGoods == null){

            InputOrderGoodsDto inputOrderGoodsDto = new InputOrderGoodsDto();

            int receivedNum = buyOrderGood.getNum() -
                              buyOrderGood.getArrivalNum() -
                              afterSalesService.getFinishAfterSaleNum(buyOrderGood.getBuyorderGoodsId());

            inputOrderGoodsDto.setReceivedQty(BigDecimal.valueOf(receivedNum));
            inputOrderGoodsDto.setSKU(buyOrderGood.getSku());

            inputOrderGoodsList.add(inputOrderGoodsDto);
            return;
        }
        if(ErpConst.ONE.equals(afterSalesGoods.getDeliveryDirect())){
            //商品直发不进行下发处理
            return;
        }

        //本次退货数量 = 原数量 - 售后单出库数量
        int returnNum = afterSalesGoods.getNum() - afterSalesGoods.getDeliveryNum();
        logger.info("采购单商品:"+buyOrderGood.getBuyorderGoodsId()+",的申请退货数量: "+ returnNum);

        //未入库数量 = 采购单商品数量 - 采购单已经到货数量 - 已完成售后单数量
        int unInputStockNum = buyOrderGood.getNum() -
                              buyOrderGood.getArrivalNum() -
                              afterSalesService.getFinishAfterSaleNum(buyOrderGood.getBuyorderGoodsId());
        //未入库数量
        if(unInputStockNum < 0){
            unInputStockNum = 0;
        }

        logger.info("采购单商品:"+buyOrderGood.getBuyorderGoodsId()+",的未入库数量: "+ unInputStockNum);

        //如果退货数量和未入库数量相等 就不下发
        if(returnNum == unInputStockNum){
            logger.info("采购单商品:"+buyOrderGood.getBuyorderGoodsId()+",的数量相等，不下发");
            return;
        }

        //如果退货数量 < 未入库数量  重新下发采购单
        if(returnNum < unInputStockNum){
            InputOrderGoodsDto inputOrderGoodsDto = new InputOrderGoodsDto();
            inputOrderGoodsDto.setReceivedQty(BigDecimal.valueOf(unInputStockNum - returnNum));
            inputOrderGoodsDto.setSKU(buyOrderGood.getSku());
            inputOrderGoodsList.add(inputOrderGoodsDto);
            return;
        }

        //如果退货数量 > 未入库数量  下发出库单
        if(returnNum > unInputStockNum){

            logger.info("采购单商品:"+buyOrderGood.getBuyorderGoodsId()+"的退货数量 - 未入库数量=" + (returnNum - unInputStockNum) + "，下发出库单");

            StockAllocationRequest stockAllocationRequest = new StockAllocationRequest();
            stockAllocationRequest.setSku(buyOrderGood.getSku());
            stockAllocationRequest.setNum(returnNum - unInputStockNum);
            stockAllocationRequest.setBuyOrderGoodId(buyOrderGood.getBuyorderGoodsId());

            List<StockAllocationResult> allocationResult = stockAllocationStrategy.stockAllocation(stockAllocationRequest);

            //转换成出库单dto
            List<PutSaleOrderGoodsDto> putSaleOrderGoodsList = convertAllocationResult(afterSalesGoods.getAfterSalesGoodsId(),allocationResult);
            outputOrderGoodsList.addAll(putSaleOrderGoodsList);

        }
    }

    private List<PutSaleOrderGoodsDto> convertAllocationResult(Integer afterSalesGoodsId,List<StockAllocationResult> allocationResultList) {

        if(CollectionUtils.isEmpty(allocationResultList)){
            return null;
        }

        return allocationResultList
                .stream()
                .map(allocationResult -> {
                    PutSaleOrderGoodsDto putSaleOrderGoodsDto = new PutSaleOrderGoodsDto();
                    putSaleOrderGoodsDto.setSku(allocationResult.getSku());
                    putSaleOrderGoodsDto.setQtyOrdered(allocationResult.getNum());
                    putSaleOrderGoodsDto.setLotAtt08(LogicalEnum.getLogicalWarehouseCode(allocationResult.getLogicalWarehouseId()));
                    putSaleOrderGoodsDto.setDedi07(afterSalesGoodsId + StringUtils.EMPTY);
                    putSaleOrderGoodsDto.setDedi04("N");
                    return putSaleOrderGoodsDto;
                })
                .collect(Collectors.toList());
    }


    /**
     * 下发采购入库单
     * @param buyorder
     * @return
     */
    /*private void sendPurchaseOrder(Buyorder buyorder,List<PutPurchaseOrderGoodsDto> details) throws Exception{


        //入库单详情为空 无需下发
        if(CollectionUtils.isEmpty(details)){
            return;
        }

        PutPurchaseOrderDto putPurchaseOrderDto = new PutPurchaseOrderDto();

        putPurchaseOrderDto.setPoType(WmsInterfaceOrderType.INPUT_PURCHASE);
        putPurchaseOrderDto.setDocNo(WmsCommonUtil.addTimestampForOrderNo(buyorder.getBuyorderNo()));
        putPurchaseOrderDto.setPoCreationTime(DateUtil.convertString(buyorder.getAddTime(),"yyyy-MM-dd HH:mm:ss"));
        putPurchaseOrderDto.setExpectedArriveTime1(DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss"));
        putPurchaseOrderDto.setExpectedArriveTime2(DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss"));

        putPurchaseOrderDto.setSupplierId(buyorder.getTraderId());
        putPurchaseOrderDto.setSupplierName(buyorder.getTraderName());

        User user = userMapper.getUserByUserId(buyorder.getCreator());
        putPurchaseOrderDto.setPoReferenceA(user.getPositionName());
        putPurchaseOrderDto.setPoReferenceB(user.getUsername());
        putPurchaseOrderDto.setNotes(buyorder.getLogisticsComments());

        //入库单的详情
        putPurchaseOrderDto.setDetails(details);

        //wms采购入库单
        WmsInterface wmsInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_PURCHASE_ORDER);
        wmsInterface.request(putPurchaseOrderDto);

    }*/

    private boolean hasSpecialDeliveryGoodByOrderId(Integer saleOrderId){

        Saleorder saleorder = new Saleorder();
        saleorder.setSaleorderId(saleOrderId);

        List<SaleorderGoodsVo> saleorderGoodsVoList = saleorderGoodsMapper.getSaleorderGoodsVoListBySaleorderId(saleorder);

        //存在专项发货的SKU
        List<String> specialDeliverySkuList = saleorderGoodsVoList.stream()
                .filter(saleorderGoodsVo -> saleorderGoodsVo.getSpecialDelivery() == 1)
                .map(saleorderGoodsVo -> saleorderGoodsVo.getSku())
                .collect(Collectors.toList());

        return CollectionUtils.isNotEmpty(specialDeliverySkuList) ? true : false;
    }

    private User getUser(){
        User user = null;
        ServletRequestAttributes ra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (ra != null) {
            HttpServletRequest request = ra.getRequest();
            if (request != null && request.getSession() != null) {
                user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
            }
        }
        if(user==null){
            user= new User();
            user.setCompanyId(1);
            user.setUserId(2);
            user.setCompanyName("南京贝登医疗有限公司");
            user.setUsername("njadmin");
        }
        return user;
    }
}
