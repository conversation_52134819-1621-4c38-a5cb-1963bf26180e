<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!DOCTYPE html>
<html>
<head>
    <title>销售关系配置</title>
    <link rel="stylesheet" href="/static/css/common/common.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/ui/ui.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/css/pages/userConfig.css?staticResourceVersion=${requestScope.staticResourceVersion}">
</head>

<body>
    <!--隐藏的登录用户信息 -->
    <jsp:include page="../../common/head_import.jsp"></jsp:include>

    <div class="page-wrap" id="page-container">
        <page-header :side="true"></page-header>
        <div class="page-container">
            <div class="page-main" v-if="!loading">
                <div class="list-title">销售关系配置</div>
                <div class="list-top-option">
                  <ui-button type="primary" icon="icon-add" @click="showAddConfig">新建配置</ui-button>
                  <ui-button @click="showImport">导入</ui-button>
                  <ui-button @click="exportConfig">导出</ui-button>
                </div>
                <ui-search-container 
                    ref="listContainer" 
                    :headers="tableHeaders" 
                    url="/crm/role/profile/role-user-region-config/page" 
                    :search-params="searchParams" 
                    :can-choose="true" 
                    :need-custom="false" 
                    :need-setting="false"
                >
                    <template v-slot:filter-list>
                        <ui-search-item label="参考地区">
                            <ui-cascader 
                                :data="addressLv2Data"
                                width="100%"
                                clearable 
                                multiple
                                filterable
                                placeholder="全部"
                                @change="handleFilterAddressChange"
                                v-if="addressLv2Data && addressLv2Data.length"
                            ></ui-cascader>
                        </ui-search-item>
                        <ui-search-item label="业务人员" :lock="true">
                            <ui-select :remote="true" :avatar="true" placeholder="全部" multiple-type="fixed" clearable :remote-info="filterUserRemoteInfo" v-model="searchParams.businessUserIds"></ui-select>
                        </ui-search-item>
                        <ui-search-item label="在职状态">
                            <ui-select :data="userStatusList" placeholder="全部" v-model="searchParams.employmentStatus" clearable></ui-select>
                        </ui-search-item>
                        <ui-search-item label="线上销售部门">
                            <ui-cascader 
                                :data="departmentData"
                                width="100%"
                                clearable 
                                multiple
                                filterable
                                placeholder="全部"
                                @change="handleFilterDepartChange"
                                v-if="departmentData && departmentData.length"
                            ></ui-cascader>
                        </ui-search-item>
                    </template>
                    <template v-slot:list-button>
                      <ui-button @click="multiDelete">删除</ui-button>
                    </template>
                    <template v-slot:onlineSalesName="{ row }">
                        <ui-user :name="row.onlineSalesName" :avatar="row.onlineSalesAvatar" :status="row.onlineSalesStatus"></ui-user>
                    </template>
                    <template v-slot:onlineSalesDepartment="{ row }">
                        <ui-department :departments="row.onlineSalesDepartments"></ui-department>
                    </template>
                    <template v-slot:offlineSalesName="{ row }">
                        <ui-user :name="row.offlineSalesName" :avatar="row.offlineSalesAvatar" :status="row.offlineSalesStatus"></ui-user>
                    </template>
                    <template v-slot:productionUserName="{ row }">
                        <ui-user :name="row.productionUserName" :avatar="row.productionUserAvatar" :status="row.productionUserStatus"></ui-user>
                    </template>
                    <template v-slot:option="{ row }">
                        <div class="option-wrap">
                            <div class="table-edit" @click="deleteItem(row)">删除</div>
                        </div>
                    </template>
                </ui-search-container>
            </div>
        </div>

        <ui-dialog
            :visible.sync="isShowEditDialog"
            title="添加关系配置"
            width="960px"
            class="config-add-dialog"
        >
            <div class="form-wrap dialog-form-wrap label-width-2" v-if="isShowEditDialog" @click="triggerDocumentClick">
                <ui-form-item label="线上销售人员" :must="true">
                    <div class="ui-col-6">
                        <ui-select :remote="true" :avatar="true" v-model="formSaler" clearable :remote-info="allUserListRemoteInfo" valid="AddSalerRegion_formSaler"></ui-select>
                    </div>
                </ui-form-item>
                <ui-form-item label="线下销售人员">
                    <div class="ui-col-6">
                        <ui-select :remote="true" :avatar="true" v-model="formOfflineSaler" clearable :remote-info="allUserListRemoteInfo"></ui-select>
                    </div>
                </ui-form-item>
                <ui-form-item label="产线人员">
                    <div class="ui-col-6">
                        <ui-select :remote="true" :avatar="true" v-model="formProductor" clearable :remote-info="allUserListRemoteInfo"></ui-select>
                    </div>
                </ui-form-item>
                <ui-form-item label="参考地区">
                    <div class="ui-col-6" @click.stop>
                        <ui-cascader 
                            :data="addressLv2Data"
                            width="100%"
                            clearable 
                            multiple
                            filterable
                            @change="handleFormAddressChange"
                            v-if="addressLv2Data && addressLv2Data.length"
                        ></ui-cascader>
                    </div>
                </ui-form-item>
            </div>
            <template slot="footer">
                <div class="dlg-form-footer" @click="triggerDocumentClick">
                    <ui-button type="primary" @click="addSalerRegion">提交</ui-button>
                    <ui-button @click="isShowEditDialog=false" class="close">取消</ui-button>
                </div>
            </template>
        </ui-dialog>

        <ui-dialog
            :visible.sync="isShowImportDialog"
            title="添加关系配置"
            width="720px"
        >
          <div class="import-content-wrap form-wrap label-width-2" v-if="isShowImportDialog">
              <ui-form-item label="客户需求" :must="true">
                  <ui-import limit-type="xlsx,xls" @change="handlerImportChange" :error-msg="importErrorMsg" :limit-size="20" limit-type-error="仅支持xlsx,xls的EXCEL格式">
                      <template v-slot:tips>
                          - 支持格式：xlsx,xls格式；<br>
                          - 大小不超过20MB；<br>
                          - 导入后进行差量处理，自动识别进行 新增、删除。<br>
                          - 线上销售、线下销售、产线人员、参考地区完全一样则略过不变。
                      </template>
                  </ui-import>
                  <div class="tmpl-download-wrap">
                      <div class="tmpl-icon">
                          <img src="/static/image/upload-icon/excel.svg" alt="">
                      </div>
                      <div class="tmpl-name">区域配置导入模板</div>
                      <a class="tmpl-link" href="/crm/role/profile/role-user-region-config/download-template" target="_blank">下载</a>
                  </div>
              </ui-form-item>
          </div>
          <template slot="footer">
              <div class="dlg-form-footer">
                  <ui-button type="primary" @click="submitImportFile">确定</ui-button>
                  <ui-button @click="isShowImportDialog = false">取消</ui-button>
              </div>
          </template>
        </ui-dialog>

    </div>
    <script src="/static/js/common/vue.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/axios.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/lodash.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/moment.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/ui/ui.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/layout.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/pages/setting/userRegionConfig.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
</body>
</html>
