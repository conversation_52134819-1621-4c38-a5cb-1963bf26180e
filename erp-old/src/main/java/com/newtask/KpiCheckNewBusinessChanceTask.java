package com.newtask;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.kpi.dao.KpiOrderLogMapper;
import com.vedeng.kpi.model.DO.KpiOrderLogDo;
import com.vedeng.kpi.service.KpiCalculateService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date created in 2020/7/7 10:23
 */
@Component
@JobHandler("kpiCheckNewBusinessChanceTask")
public class KpiCheckNewBusinessChanceTask extends AbstractJobHandler {

    @Autowired
    private KpiCalculateService kpiCalculateService;

    @Autowired
    private KpiOrderLogMapper kpiOrderLogMapper;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        if (StringUtils.isNotBlank(s)){
            kpiCalculateService.checkNewBusinessChance(kpiOrderLogMapper.getKpiLogByOrderNoAndOperation(s,1).get(0));
        } else {
            int offset = 0;
            int limit = 100;
            int leftCountOfKpi;
            do {
                List<KpiOrderLogDo> kpiOrderLogDoList = kpiOrderLogMapper.getKpiLogByPage(offset*limit,limit);
                leftCountOfKpi = kpiOrderLogDoList.size();
                kpiOrderLogDoList.parallelStream()
                        .forEach(item -> kpiCalculateService.checkNewBusinessChance(item));
                XxlJobLogger.log("offset：{}",offset);
                offset++;
            } while (leftCountOfKpi > 0);
        }

        XxlJobLogger.log("校准新商机属性结束");
        return null;
    }
}
