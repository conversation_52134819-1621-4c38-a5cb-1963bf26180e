package com.vedeng.erp.trader.web.api;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.annotation.NoRepeatSubmit;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.erp.trader.domain.dto.CommunicateAiSummaryDto;
import com.vedeng.erp.trader.service.CommunicateSummaryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/18 15:26
 */
@Controller
@ExceptionController
@RequestMapping("/communicateSummary")
public class CommunicateSummaryApi {

    @Autowired
    private CommunicateSummaryService communicateSummaryService;

    /**
     * 根据客户id查询最近的一条沟通记录的转写结果
     *
     * @param traderId 客户id
     * @return CommunicateSummaryDto
     */
    @ResponseBody
    @RequestMapping("/getByTraderId")
    @NoNeedAccessAuthorization
    public R<CommunicateAiSummaryDto> getLatestSummaryByTraderId(@RequestParam("traderId") Integer traderId) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        return R.success(communicateSummaryService.getByTraderId(traderId, currentUser.getId()));
    }

    /**
     * 根据沟通内容概要id查询信息以及关联的沟通记录、客户信息
     *
     * @param communicateSummaryId communicateSummaryId
     * @return CommunicateAiSummaryDto
     */
    @ResponseBody
    @RequestMapping("/getByCommunicateSummaryId")
    @NoNeedAccessAuthorization
    public R<CommunicateAiSummaryDto> getByCommunicateSummaryId(@RequestParam("communicateSummaryId") Long communicateSummaryId) {
        return R.success(communicateSummaryService.getByCommunicateSummaryId(communicateSummaryId));
    }

    /**
     * 保存更新AI语音提取结果
     *
     * @param communicateAiSummaryDto CommunicateAiSummaryDto
     * @return R<?>
     */
    @ResponseBody
    @NoRepeatSubmit
    @RequestMapping("/update")
    @NoNeedAccessAuthorization
    public R<?> saveModifySummary(@RequestBody CommunicateAiSummaryDto communicateAiSummaryDto) {
        communicateSummaryService.saveUpdate(communicateAiSummaryDto);
        return R.success();
    }

}