package com.vedeng.goods.manufacturer.constants;

import com.google.common.collect.Lists;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.constant.SysOptionConstant;

import java.util.*;

/**
 * <AUTHOR>
 */
public class ManufacturerConstants {

    /**
     * 贝登公章（贝） // TODO V36 是否添加备案
     */
    public static final List<Integer> ATTACHMENT_FUN_WITH_B = Arrays.asList(
            // 营业执照(贝)
            CommonConstants.ATTACHMENT_FUNCTION_1302,
            // 生产企业生产许可证（贝）
            CommonConstants.ATTACHMENT_FUNCTION_1303,
            // 生产企业备案凭证（贝）
            CommonConstants.ATTACHMENT_FUNCTION_1309,
            // 生产企业生产产品登记表(即登记表附近)（贝）
            CommonConstants.ATTACHMENT_FUNCTION_1304);

    /**
     * 贝登公章（新）替换以前的 1305-1307 1308
     */
    public static final List<Integer> ATTACHMENT_FUN = Arrays.asList(
            CommonConstants.ATTACHMENT_FUNCTION_1305,
            CommonConstants.ATTACHMENT_FUNCTION_1306,
            CommonConstants.ATTACHMENT_FUNCTION_1307,
            CommonConstants.ATTACHMENT_FUNCTION_1308);

    /**
     * all
     */
    public static final List<Integer> MANUFACTURER_ALL_FUN = Lists.newArrayListWithExpectedSize(8);

    /**
     * 生产企业附件 -》 供应商附件 对应索引
     */
    public static final Map<Integer, Integer> ATTACHMENT_TO_SUPPLIER_INDEX = new HashMap<>(6);

    /**
     * 供应商附件 -》 生产企业附件 对应索引
     */
    public static final Map<Integer, Integer> SUPPLIER_TO_ATTACHMENT_INDEX = new HashMap<>(6);


    /**
     *  A -> S 同步状态 不处理
     */
    public static final int NOT_SYNC = 0;

    /**
     *  A -> S 同步状态 仅已经存在的base
     */
    public static final int SYNC_BASE_NOT_FILE = 1;

    /**
     *  A -> S 同步状态 同步base
     */
    public static final int SYNC_FILE = 2;


    static {
        MANUFACTURER_ALL_FUN.addAll(ATTACHMENT_FUN_WITH_B);
        MANUFACTURER_ALL_FUN.addAll(ATTACHMENT_FUN);

        // 营业执照
        ATTACHMENT_TO_SUPPLIER_INDEX.put(CommonConstants.ATTACHMENT_FUNCTION_1307, SysOptionConstant.ID_25);
        SUPPLIER_TO_ATTACHMENT_INDEX.put(SysOptionConstant.ID_25, CommonConstants.ATTACHMENT_FUNCTION_1307);

        //许可证
        ATTACHMENT_TO_SUPPLIER_INDEX.put(CommonConstants.ATTACHMENT_FUNCTION_1306, SysOptionConstant.ID_439);
        SUPPLIER_TO_ATTACHMENT_INDEX.put(SysOptionConstant.ID_439, CommonConstants.ATTACHMENT_FUNCTION_1306);

        //生产产品登记表
        ATTACHMENT_TO_SUPPLIER_INDEX.put(CommonConstants.ATTACHMENT_FUNCTION_1305, SysOptionConstant.ID_1102);
        SUPPLIER_TO_ATTACHMENT_INDEX.put(SysOptionConstant.ID_1102, CommonConstants.ATTACHMENT_FUNCTION_1305);

        //备案凭证编号
        ATTACHMENT_TO_SUPPLIER_INDEX.put(CommonConstants.ATTACHMENT_FUNCTION_1308, SysOptionConstant.ID_1101);
        SUPPLIER_TO_ATTACHMENT_INDEX.put(SysOptionConstant.ID_1101, CommonConstants.ATTACHMENT_FUNCTION_1308);

    }

}
