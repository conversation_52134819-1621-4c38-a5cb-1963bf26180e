package com.vedeng.erp.kingdee.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeeReceiveCommonCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeReceiveCommonEntity;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveCommonDto;
import com.vedeng.erp.kingdee.dto.result.KingDeeReceiveQueryResultDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeReceiveCommonCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeReceiveCommonConvertor;
import com.vedeng.erp.kingdee.repository.KingDeeReceiveCommonRepository;
import com.vedeng.erp.kingdee.service.KingDeeReceiveCommonApiService;
import com.vedeng.erp.kingdee.service.KingDeeReceiveCommonService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryExtParam;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryFilterDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 标准应收单
 * @author: Jez
 * @date: 2022/12/2 15:46
 **/
@Service
@Slf4j
public class KingDeeReceiveCommonServiceImpl extends KingDeeBaseServiceImpl<
        KingDeeReceiveCommonEntity,
        KingDeeReceiveCommonDto,
        KingDeeReceiveCommonCommand,
        KingDeeReceiveCommonRepository,
        KingDeeReceiveCommonConvertor,
        KingDeeReceiveCommonCommandConvertor>
        implements KingDeeReceiveCommonService, KingDeeReceiveCommonApiService {


    @Override
    public List<KingDeeReceiveQueryResultDto> getKingDeeReceiveCommon(String invoiceId) {
        KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
        queryParam.setFormId(KingDeeFormConstant.RECEIVE_EXPENSES);
        List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
        queryFilterDtos.add(KingDeeQueryFilterDto.builder().fieldName("F_QZOK_BDDJTID")
                .value(invoiceId).build());
        queryFilterDtos.add(KingDeeQueryFilterDto.builder().fieldName("fBusinessType")
                .value("BZ").build());
        queryParam.setFilterString(queryFilterDtos);
        return kingDeeBaseApi.query(queryParam, KingDeeReceiveQueryResultDto.class);
    }

    @Override
    public Boolean kingDeeIsExist(KingDeeReceiveCommonDto d) {
        List<KingDeeReceiveQueryResultDto> kingDeeReceiveCommon = this.getKingDeeReceiveCommon(d.getFQzokBddjtId());
        return CollectionUtil.isNotEmpty(kingDeeReceiveCommon);
    }

}
