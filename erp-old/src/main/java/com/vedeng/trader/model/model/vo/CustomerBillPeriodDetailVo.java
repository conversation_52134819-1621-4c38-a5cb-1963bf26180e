package com.vedeng.trader.model.model.vo;

import com.vedeng.trader.model.dto.CustomerBillPeriodItemDto;

import java.math.BigDecimal;

/**
 * <AUTHOR> simgo.wang
 * @date : 2021/8/5 17:11
 * @desc :
 */
public class CustomerBillPeriodDetailVo extends CustomerBillPeriodItemDto {

    /**
     * 是否直发 0否1是
     */
    private Integer deliveryDirect;
    /**
     * 客户名称
     */
    private String traderName;

    /**
     * 交易者ID
     */
    private Integer traderId;

    /**
     * 归属部门ID
     */
    private Integer orgId;

    /**
     * 归属部门
     */
    private String orgName;

    /**
     * 订单号
     */
    private String saleorderNo;

    /**
     * 订单生效日期
     */
    private Long validTime;

    /**
     * 订单原总金额
     */
    private BigDecimal totalAmount;

    /**
     * 订单实际总金额
     */
    private BigDecimal realTotalAmount;

    /**
     * 质保金
     */
    private BigDecimal retentionMoney;

    /**
     * 结算标准
     */
    private Integer billPeriodSettlementType;

    /**
     * 账期占用额度
     */
    private BigDecimal periodAmountOccupy;

    /**
     * 账期冻结额度
     */
    private BigDecimal periodAmountFreeze;

    /**
     * 账期使用额度
     */
    private BigDecimal periodAmountUsed;

    /**
     * 顶级账期类型，按正式账期>临时账期>订单账期
     */
    private Integer billPeriodTypeFirst;

    public Integer getBillPeriodTypeFirst() {
        return billPeriodTypeFirst;
    }

    public void setBillPeriodTypeFirst(Integer billPeriodTypeFirst) {
        this.billPeriodTypeFirst = billPeriodTypeFirst;
    }

    public Integer getDeliveryDirect() {
        return deliveryDirect;
    }

    public void setDeliveryDirect(Integer deliveryDirect) {
        this.deliveryDirect = deliveryDirect;
    }

    public String getTraderName() {
        return traderName;
    }

    public void setTraderName(String traderName) {
        this.traderName = traderName;
    }

    public Integer getTraderId() {
        return traderId;
    }

    public void setTraderId(Integer traderId) {
        this.traderId = traderId;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getSaleorderNo() {
        return saleorderNo;
    }

    public void setSaleorderNo(String saleorderNo) {
        this.saleorderNo = saleorderNo;
    }

    public Long getValidTime() {
        return validTime;
    }

    public void setValidTime(Long validTime) {
        this.validTime = validTime;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getRealTotalAmount() {
        return realTotalAmount;
    }

    public void setRealTotalAmount(BigDecimal realTotalAmount) {
        this.realTotalAmount = realTotalAmount;
    }

    public BigDecimal getRetentionMoney() {
        return retentionMoney;
    }

    public void setRetentionMoney(BigDecimal retentionMoney) {
        this.retentionMoney = retentionMoney;
    }

    public Integer getBillPeriodSettlementType() {
        return billPeriodSettlementType;
    }

    public void setBillPeriodSettlementType(Integer billPeriodSettlementType) {
        this.billPeriodSettlementType = billPeriodSettlementType;
    }

    public BigDecimal getPeriodAmountOccupy() {
        return periodAmountOccupy;
    }

    public void setPeriodAmountOccupy(BigDecimal periodAmountOccupy) {
        this.periodAmountOccupy = periodAmountOccupy;
    }

    public BigDecimal getPeriodAmountFreeze() {
        return periodAmountFreeze;
    }

    public void setPeriodAmountFreeze(BigDecimal periodAmountFreeze) {
        this.periodAmountFreeze = periodAmountFreeze;
    }

    public BigDecimal getPeriodAmountUsed() {
        return periodAmountUsed;
    }

    public void setPeriodAmountUsed(BigDecimal periodAmountUsed) {
        this.periodAmountUsed = periodAmountUsed;
    }
}
