package com.vedeng.erp.kingdee.domain.entity;

import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseEntity;
import com.vedeng.common.mybatis.jbatis.annotation.Column;
import com.vedeng.common.mybatis.jbatis.annotation.GeneratedValue;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.common.mybatis.jbatis.annotation.Table;
import lombok.Getter;
import lombok.Setter;

/**
    * 应收余额调整单表
    */
@Getter
@Setter
@Table(name = "KING_DEE_NEED_RECEIVE_ADJUST")
public class KingDeeNeedReceiveAdjustEntity extends BaseEntity{
    /**
    * 主键
    */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer kingDeeNeedReceiveId;

    /**
    * 单据内码  0：表示新增 * 非0：云星空系统单据FID值，表示修改
    */
    private String fid;

    /**
    * 单据号
    */
    private String fBillNo;

    /**
    * 单据日期 2022-10-10
    */
    private String fVpfnDate;

    /**
    * 组织代码
    */
    private String fVpfnJg;

    /**
    * 供应商
    */
    private String fVpfnKh;

    /**
    * fEntity json
    */
    @Column(jdbcType = "VARCHAR")
    private JSONArray fEntity;

    /**
    * 更新备注
    */
    private String updateRemark;

}