<!doctype html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>入库记录</title>
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
 </head>
 <body id="veWqSMQe5Bk" datasource="erp-reportdatasource" fixednumber="0" fixednumberright="0" append_column_url="" append_row_url="" empty_show="/" firstcol="">
  <div id="appendHead"></div>
  <div class="layui-fluid">
   <div class="layui-tab">
    <ul class="layui-tab-title" id="tab">
    </ul>
   </div>
   <form class="layui-form" id="search">
    <div class=" layui-inline ">
     <label class="layui-form-label">产品名称</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="SKU_NAME" placeholder="" style="" alias="AB" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" ></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">品牌</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="BRAND_NAME" placeholder="" style="" alias="BRAND" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" ></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">型号</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="MODEL" placeholder="" style="" alias="AB" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" ></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">订货号</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="SKU_NO" placeholder="" style="" alias="AB" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" ></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">合并搜索</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="union" name="C.BUYORDER_NO, ASS.AFTER_SALES_NO, L.LEND_OUT_NO, OO.ORDER_NO, IO.ORDER_NO, IA.INVENTORY_ADJUSTMENT_NO, WUCO.WMS_UNIT_CONVERSION_ORDER_NO" placeholder style="" alias="" jdbctype="VARCHAR" data="" datatype="" oper="" validate_rules="" validate_messages=""></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">发货方</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="TRADER_NAME" placeholder="" style="" alias="C" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" ></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">入库时间</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="daterange" name="ADD_TIME" placeholder="" style="" alias="A" jdbctype="DATE_TO_NUMBER" data="" datatype="" oper="BETWEEN" ></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">采购单号</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="30" name="BUYORDER_NO" placeholder="" style="" alias="C" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" ></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">售后单号</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="30" name="AFTER_SALES_NO" placeholder="" style="" alias="ASS" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" ></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">外借单号</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="30" name="LEND_OUT_NO" placeholder="" style="" alias="L" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" ></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">wms入库单号</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="30" name="ORDER_NO" placeholder="" style="" alias="IO" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" ></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">库存调整单单号</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="30" name="INVENTORY_ADJUSTMENT_NO" placeholder="" style="" alias="IA" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" ></object>
     </div>
    </div>
	   <div class=" layui-inline ">
		   <label class="layui-form-label">单位转换单号</label>
		   <div class="layui-input-inline"><object class=" layui-input list-search-item " type="30" name="WMS_UNIT_CONVERSION_ORDER_NO" placeholder="" style="" alias="WUCO" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" ></object>
		   </div>
	   </div>
   </form>
   <hr class="layui-border-blue">
   <div class="btn-group   bd-highlight" id="tableButton">
   </div>
   <table id="table" class="layui-table" style=" width:100%">
    <thead>
     <tr id="column">
      <th type="rowbutton" id="rowbutton"></th>
      <th item_name="WAREGOUSING_TIME" name="WAREGOUSING_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">入库时间</th>
      <th item_name="ORDER_NO" name="ORDER_NO" body="td-link" order="0" url="/logistics/warehousein/viewWarehouseDetail.do?operateType=${OPERATE_TYPE}&amp;orderId=${ORDER_ID}&amp;orderNo=${ORDER_NO}" opentype="PARENT" windowname="${ORDER_NO}" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">入库单据</th>
      <th item_name="SKU_NO" name="SKU_NO" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">订货号</th>
      <th item_name="SKU_NAME" name="SKU_NAME" body="td-link" order="0" url="/goods/goods/viewbaseinfo.do?goodsId=${SKU_ID}" opentype="PARENT" windowname="${SKU_NAME}" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">产品名称</th>
      <th item_name="MATERIAL_CODE" name="MATERIAL_CODE" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">物料编码</th>
      <th item_name="BRAND_NAME" name="BRAND_NAME" body="td-text" order="0" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">品牌</th>
      <th item_name="MODEL" name="MODEL" body="td-text" order="0" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">型号</th>
      <th item_name="NUM" name="NUM" body="td-text" order="0" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">数量</th>
      <th item_name="UNIT_NAME" name="UNIT_NAME" body="td-text" order="0" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">单位</th>
      <th item_name="GOODS_PRICE" name="GOODS_PRICE" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">采购价</th>
      <th item_name="TOTAL_PRICE" name="TOTAL_PRICE" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">总价</th>
      <th item_name="SALE_TRADER_NAME" name="SALE_TRADER_NAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">发货方</th>
     </tr>
    </thead>
    <tbody>
     <tr>
      <td> express:</td>
      <td colspan="100"><pre id="express" class="layui-code" orderby="ORDER BY WAREGOUSING_TIME DESC" groupby="">StringBuilder sql1=new StringBuilder("SELECT
	A.WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL_ID,
	FROM_UNIXTIME( A.ADD_TIME / 1000, '%Y-%m-%d %H:%i:%S' ) AS WAREGOUSING_TIME,
	A.BARCODE_ID,
	(
	CASE
			A.OPERATE_TYPE
			WHEN 1 THEN
			C.BUYORDER_NO
			WHEN 3 THEN
			ASS.AFTER_SALES_NO
			WHEN 5 THEN
			ASS.AFTER_SALES_NO
			WHEN 8 THEN
			ASS.AFTER_SALES_NO
			WHEN 9 THEN
		IF
			( L.LEND_OUT_NO IS NOT NULL, L.LEND_OUT_NO, OO.order_no )
			WHEN 12 THEN
			IO.ORDER_NO
			WHEN 11 THEN
			IA.INVENTORY_ADJUSTMENT_NO
		  	WHEN 20 THEN
		    WUCO.WMS_UNIT_CONVERSION_ORDER_NO
		  	ELSE ''
		END
		) AS ORDER_NO,
		(
		CASE
				A.OPERATE_TYPE
				WHEN 1 THEN
				C.BUYORDER_ID
				WHEN 3 THEN
				ASS.AFTER_SALES_ID
				WHEN 5 THEN
				ASS.AFTER_SALES_ID
				WHEN 8 THEN
				ASS.AFTER_SALES_ID
				WHEN 9 THEN
			IF
				( L.LEND_OUT_ID IS NOT NULL, L.LEND_OUT_ID, OO.id )
				WHEN 12 THEN
				IO.ORDER_NO
				WHEN 11 THEN
				IA.INVENTORY_ADJUSTMENT_NO
		  		WHEN 20 THEN
		  		WUCO.WMS_UNIT_CONVERSION_ORDER_ID
		  		ELSE ''
			END
			) AS ORDER_ID,
			A.OPERATE_TYPE,
			AB.SKU_NO,
			AB.SKU_NAME,
			AB.MATERIAL_CODE,
			AB.SKU_ID,
			BRAND.BRAND_NAME,
			ABS( A.NUM ) AS NUM,
			U.UNIT_NAME,
			AB.MODEL,
			(
			CASE
					A.OPERATE_TYPE
					WHEN 1 THEN
					B.PRICE
					WHEN 3 THEN
					ASG.PRICE
					WHEN 5 THEN
					ASG.PRICE
					WHEN 8 THEN
					ASG.PRICE ELSE 0
				END
				) AS GOODS_PRICE,
				(
				CASE
						A.OPERATE_TYPE
						WHEN 1 THEN
						B.PRICE * ABS( A.NUM )
						WHEN 3 THEN
						ASG.PRICE * ABS( A.NUM )
						WHEN 5 THEN
						ASG.PRICE * ABS( A.NUM )
						WHEN 8 THEN
						ASG.PRICE * ABS( A.NUM ) ELSE 0
					END
					) AS TOTAL_PRICE,
					( CASE A.OPERATE_TYPE WHEN 1 THEN C.TRADER_NAME END ) AS SALE_TRADER_NAME
				FROM
					T_WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL A
					LEFT JOIN V_CORE_SKU AB ON AB.SKU_ID = A.GOODS_ID
					LEFT JOIN V_CORE_SPU BB ON AB.SPU_ID = BB.SPU_ID
					LEFT JOIN T_BRAND BRAND ON BRAND.BRAND_ID = BB.BRAND_ID
					LEFT JOIN T_UNIT U ON U.UNIT_ID = AB.BASE_UNIT_ID
					LEFT JOIN T_BUYORDER_GOODS B ON B.BUYORDER_GOODS_ID = A.RELATED_ID
					AND B.IS_DELETE = 0
					LEFT JOIN T_BUYORDER C ON B.BUYORDER_ID = C.BUYORDER_ID
					AND C.VALID_STATUS = 1
					AND A.OPERATE_TYPE = 1
					LEFT JOIN T_AFTER_SALES_GOODS ASG ON ASG.AFTER_SALES_GOODS_ID = A.RELATED_ID
					AND A.OPERATE_TYPE IN ( 3, 5, 8 )
					LEFT JOIN T_AFTER_SALES ASS ON ASG.AFTER_SALES_ID = ASS.AFTER_SALES_ID
					LEFT JOIN T_AFTER_SALES_DETAIL ASD ON ASD.AFTER_SALES_ID = ASS.AFTER_SALES_ID
					LEFT JOIN T_TRADER AST ON AST.TRADER_ID = ASD.TRADER_ID
					LEFT JOIN T_TRADER T ON C.TRADER_ID = T.TRADER_ID
					LEFT JOIN T_WMS_OUTPUT_ORDER_GOODS OOG ON OOG.id = A.RELATED_ID
					AND A.OPERATE_TYPE = 9
					AND OOG.SKU_NO = AB.SKU_NO
					LEFT JOIN T_WMS_OUTPUT_ORDER OO ON OOG.wms_output_order_id = OO.id
					LEFT JOIN T_LEND_OUT L ON A.RELATED_ID = L.LEND_OUT_ID
					AND A.OPERATE_TYPE = 9
					AND A.GOODS_ID = L.GOODS_ID
					LEFT JOIN T_WMS_INPUT_ORDER_GOODS IOG ON IOG.WMS_INPUT_ORDER_GOODS_ID = A.RELATED_ID
					AND A.OPERATE_TYPE = 12
					LEFT JOIN T_WMS_INPUT_ORDER IO ON IOG.WMS_INPUT_ORDER_ID = IO.WMS_INPUT_ORDER_ID
					LEFT JOIN T_WMS_INVENTORY_ADJUSTMENT_DETAIL IAD ON IAD.INVENTORY_ADJUSTMENT_DETAIL_ID = A.RELATED_ID
					AND A.OPERATE_TYPE = 11
					LEFT JOIN T_WMS_INVENTORY_ADJUSTMENT IA ON IA.INVENTORY_ADJUSTMENT_ID = IAD.INVENTORY_ADJUSTMENT_ID
		  			LEFT JOIN T_WMS_UNIT_CONVERSION_ORDER_ITEM WUCOI ON WUCOI.WMS_UNIT_CONVERSION_ORDER_ITEM_ID = A.RELATED_ID
		  			AND A.OPERATE_TYPE = 20
		  			LEFT JOIN T_WMS_UNIT_CONVERSION_ORDER WUCO ON WUCO.WMS_UNIT_CONVERSION_ORDER_ID = WUCOI.WMS_UNIT_CONVERSION_ORDER_ID
					LEFT JOIN T_BARCODE BR ON BR.BARCODE_ID = A.BARCODE_ID
				WHERE
					A.LOG_TYPE = 0
					AND A.IS_ENABLE = 1
					AND A.NUM &gt; 0
                                       ");

String group1="GROUP BY A.WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL_ID";

StringBuilder sql2=new StringBuilder("SELECT
					A.WAREHOUSE_GOODS_OPERATE_LOG_ID,
					FROM_UNIXTIME( A.ADD_TIME / 1000, '%Y-%m-%d %H:%i:%S' ) AS WAREGOUSING_TIME,
					A.BARCODE_ID,
					(
					CASE
							A.OPERATE_TYPE
							WHEN 1 THEN
							C.BUYORDER_NO
							WHEN 3 THEN
							ASS.AFTER_SALES_NO
							WHEN 5 THEN
							ASS.AFTER_SALES_NO
							WHEN 8 THEN
							ASS.AFTER_SALES_NO
							WHEN 9 THEN
						IF
							( L.LEND_OUT_NO IS NOT NULL, L.LEND_OUT_NO, OO.order_no )
							WHEN 12 THEN
							IO.ORDER_NO
							WHEN 11 THEN
							IA.INVENTORY_ADJUSTMENT_NO
		  					WHEN 20 THEN
		  					WUCO.WMS_UNIT_CONVERSION_ORDER_NO
		  					ELSE ''
						END
						) AS ORDER_NO,
						(
						CASE
								A.OPERATE_TYPE
								WHEN 1 THEN
								C.BUYORDER_ID
								WHEN 3 THEN
								ASS.AFTER_SALES_ID
								WHEN 5 THEN
								ASS.AFTER_SALES_ID
								WHEN 8 THEN
								ASS.AFTER_SALES_ID
								WHEN 9 THEN
							IF
								( L.LEND_OUT_ID IS NOT NULL, L.LEND_OUT_ID, OO.id )
								WHEN 12 THEN
								IO.ORDER_NO
								WHEN 11 THEN
								IA.INVENTORY_ADJUSTMENT_NO
								WHEN 20 THEN
								WUCO.WMS_UNIT_CONVERSION_ORDER_ID
		  						ELSE ''
							END
							) AS ORDER_ID,
							A.OPERATE_TYPE,
							AB.SKU_NO,
							AB.SKU_NAME,
							AB.MATERIAL_CODE,
							AB.SKU_ID,
							BRAND.BRAND_NAME,
							ABS( A.NUM ) AS NUM,
							U.UNIT_NAME,
							AB.MODEL,
							(
							CASE
									A.OPERATE_TYPE
									WHEN 1 THEN
									B.PRICE
									WHEN 3 THEN
									ASG.PRICE
									WHEN 5 THEN
									ASG.PRICE
									WHEN 8 THEN
									ASG.PRICE ELSE 0
								END
								) AS GOODS_PRICE,
								(
								CASE
										A.OPERATE_TYPE
										WHEN 1 THEN
										B.PRICE * ABS( A.NUM )
										WHEN 3 THEN
										ASG.PRICE * ABS( A.NUM )
										WHEN 5 THEN
										ASG.PRICE * ABS( A.NUM )
										WHEN 8 THEN
										ASG.PRICE * ABS( A.NUM ) ELSE 0
									END
									) AS TOTAL_PRICE,
									( CASE A.OPERATE_TYPE WHEN 1 THEN C.TRADER_NAME END ) AS SALE_TRADER_NAME
								FROM
									T_WAREHOUSE_GOODS_OPERATE_LOG A
									LEFT JOIN V_CORE_SKU AB ON AB.SKU_ID = A.GOODS_ID
									LEFT JOIN V_CORE_SPU BB ON AB.SPU_ID = BB.SPU_ID
									LEFT JOIN T_BRAND BRAND ON BRAND.BRAND_ID = BB.BRAND_ID
									LEFT JOIN T_UNIT U ON U.UNIT_ID = AB.BASE_UNIT_ID
									LEFT JOIN T_BUYORDER_GOODS B ON B.BUYORDER_GOODS_ID = A.RELATED_ID
									AND B.IS_DELETE = 0
									LEFT JOIN T_BUYORDER C ON B.BUYORDER_ID = C.BUYORDER_ID
									AND C.VALID_STATUS = 1
									AND A.OPERATE_TYPE = 1
									LEFT JOIN T_AFTER_SALES_GOODS ASG ON ASG.AFTER_SALES_GOODS_ID = A.RELATED_ID
									AND A.OPERATE_TYPE IN ( 3, 5, 8 )
									LEFT JOIN T_AFTER_SALES ASS ON ASG.AFTER_SALES_ID = ASS.AFTER_SALES_ID
									LEFT JOIN T_AFTER_SALES_DETAIL ASD ON ASD.AFTER_SALES_ID = ASS.AFTER_SALES_ID
									LEFT JOIN T_TRADER AST ON AST.TRADER_ID = ASD.TRADER_ID
									LEFT JOIN T_TRADER T ON C.TRADER_ID = T.TRADER_ID
									LEFT JOIN T_WMS_OUTPUT_ORDER_GOODS OOG ON OOG.id = A.RELATED_ID
									AND A.OPERATE_TYPE = 9
									AND OOG.SKU_NO = AB.SKU_NO
									LEFT JOIN T_WMS_OUTPUT_ORDER OO ON OOG.wms_output_order_id = OO.id
									LEFT JOIN T_LEND_OUT L ON A.RELATED_ID = L.LEND_OUT_ID
									AND A.OPERATE_TYPE = 9
									AND A.GOODS_ID = L.GOODS_ID
									LEFT JOIN T_WMS_INPUT_ORDER_GOODS IOG ON IOG.WMS_INPUT_ORDER_GOODS_ID = A.RELATED_ID
									AND A.OPERATE_TYPE = 12
									LEFT JOIN T_WMS_INPUT_ORDER IO ON IOG.WMS_INPUT_ORDER_ID = IO.WMS_INPUT_ORDER_ID
									LEFT JOIN T_WMS_INVENTORY_ADJUSTMENT_DETAIL IAD ON IAD.INVENTORY_ADJUSTMENT_DETAIL_ID = A.RELATED_ID
									AND A.OPERATE_TYPE = 11
									LEFT JOIN T_WMS_INVENTORY_ADJUSTMENT IA ON IA.INVENTORY_ADJUSTMENT_ID = IAD.INVENTORY_ADJUSTMENT_ID
		  							LEFT JOIN T_WMS_UNIT_CONVERSION_ORDER_ITEM WUCOI ON WUCOI.WMS_UNIT_CONVERSION_ORDER_ITEM_ID = A.RELATED_ID
									AND A.OPERATE_TYPE = 20
									LEFT JOIN T_WMS_UNIT_CONVERSION_ORDER WUCO ON WUCO.WMS_UNIT_CONVERSION_ORDER_ID = WUCOI.WMS_UNIT_CONVERSION_ORDER_ID
									LEFT JOIN T_BARCODE BR ON BR.BARCODE_ID = A.BARCODE_ID
								WHERE
									A.LOG_TYPE = 0
									AND A.IS_ENABLE = 1
									AND A.NUM &gt; 0
                                                                        ");

String group2="GROUP BY A.WAREHOUSE_GOODS_OPERATE_LOG_ID ";

result=unionall(sql1,group1,"",sql2,group2,"");
return result;</pre></td>
     </tr>
     <tr>
      <td> count:</td>
      <td colspan="100"><pre id="count" class="layui-code"></pre></td>
     </tr>
    </tbody>
   </table>
  </div>
  <div id="appendFoot">
   <script>
  function submitHandle(){
   if($("#ADD_TIME_START_ID").val()==""){
    layer.alert("入库开始时间不能为空")
    return false;
   }
   return true;
  }

 </script>
  </div>
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
  <script>
 layui.use(function () {

 })
</script>
 </body>
</html>