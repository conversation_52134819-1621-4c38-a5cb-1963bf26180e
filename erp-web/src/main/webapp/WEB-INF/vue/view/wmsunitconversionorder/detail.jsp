<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>


<div id="app" style="display: none;">
    <el-card class="box-card">
        <el-descriptions title="基本信息" border column="2">
            <el-descriptions-item label="库存转换单号" content-class-name="my-content">{{wmsUnitConversionOrderDto.wmsUnitConversionOrderNo}}</el-descriptions-item>
            <el-descriptions-item label="审核状态">
                <span v-if="wmsUnitConversionOrderDto.verifyStatus == 0">待审核</span>
                <span v-else-if="wmsUnitConversionOrderDto.verifyStatus == 1">审核中</span>
                <span v-else-if="wmsUnitConversionOrderDto.verifyStatus == 2">审核通过</span>
                <span v-else-if="wmsUnitConversionOrderDto.verifyStatus == 3">审核不通过</span>
                <span v-else>-</span>
            </el-descriptions-item>
            <el-descriptions-item label="创建人">{{wmsUnitConversionOrderDto.creatorName}}</el-descriptions-item>
            <el-descriptions-item label="创建部门">{{wmsUnitConversionOrderDto.orgName}}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{parseTime(wmsUnitConversionOrderDto.addTime)}}</el-descriptions-item>
            <el-descriptions-item label="申请类型">
                <span v-if="wmsUnitConversionOrderDto.orderType == 1">单位转换</span>
                <span v-else-if="wmsUnitConversionOrderDto.orderType == 2">主机配件</span>
                <span v-else-if="wmsUnitConversionOrderDto.orderType == 3">组合产品</span>
                <span v-else>-</span>
            </el-descriptions-item>
            <el-descriptions-item label="申请原因">{{wmsUnitConversionOrderDto.reason}}</el-descriptions-item>
            <el-descriptions-item label="备注">{{wmsUnitConversionOrderDto.comments}}</el-descriptions-item>
        </el-descriptions>
    </el-card>


    <el-card class="box-card">
        <div slot="header" class="clearfix">
            <span>库存转换产品信息</span>
        </div>
        <template>
            <el-table
                    :data="wmsUnitConversionOrderDto.wmsUnitConversionOrderItemDtoList"
                    border
                    key="orderItem"
                    style="width: 100%;"
                    :cell-style="{'text-align':'center'}"
                    :header-cell-style="{'text-align':'center'}">
                <el-table-column
                        type="index"
                        label="序号"
                        min-width="50">
                </el-table-column>
                <el-table-column
                        prop="sourceSkuNo"
                        label="需转换订货号"
                        min-width="10%">
                </el-table-column>
                <el-table-column
                        label="需转换商品名称"
                        min-width="30%">
                        <template slot-scope="scope">
                            <el-link type="primary" :underline="false" @click="viewSkuInfo(scope.row.sourceSkuId)">{{scope.row.sourceGoodsName }}
                            </el-link>
                        </template>
                </el-table-column>
                <el-table-column
                        prop="sourceUnitName"
                        label="单位"
                        min-width="15%">
                </el-table-column>
                <el-table-column
                        label="成本单价"
                        min-width="10%">
                        <template slot-scope="scope">
                            <span>{{ (scope.row.sourcePrice).toFixed(2)}}</span>
                        </template>
                </el-table-column>
                <el-table-column
                        prop="stockNum"
                        label="库存数量"
                        min-width="10%">
                </el-table-column>
                <el-table-column
                        label="需转换数量"
                        min-width="10%">
                        <template slot-scope="scope">
                            <span>{{ (scope.row.sourceNum).toFixed(2)}}</span>
                        </template>
                </el-table-column>
                <el-table-column
                        prop="targetSkuNo"
                        label="目标订货号"
                        min-width="10%">
                </el-table-column>
                <el-table-column
                        label="目标商品名称"
                        min-width="10%">
                        <template slot-scope="scope">
                            <el-link type="primary" :underline="false" @click="viewSkuInfo(scope.row.targetSkuId)">{{scope.row.targetGoodsName }}
                            </el-link>
                        </template>
                </el-table-column>
                <el-table-column
                        prop="targetUnitName"
                        label="目标单位"
                        min-width="10%">
                </el-table-column>
                <el-table-column
                        label="转换后成本单价"
                        min-width="10%">
                        <template slot-scope="scope">
                            <span>{{ (scope.row.targetPrice).toFixed(2)}}</span>
                        </template>
                </el-table-column>
                <el-table-column
                        label="转换后数量"
                        min-width="10%">
                        <template slot-scope="scope">
                            <span>{{ (scope.row.targetNum).toFixed(2)}}</span>
                        </template>
                </el-table-column>
                <el-table-column
                        label="税率"
                        min-width="10%">
                        <template slot-scope="scope">
                            <span v-if="scope.row.taxRate == 1">0%</span>
                            <span v-else-if="scope.row.taxRate == 2">1%</span>
                            <span v-else-if="scope.row.taxRate == 3">3%</span>
                            <span v-else-if="scope.row.taxRate == 4">5%</span>
                            <span v-else-if="scope.row.taxRate == 5">6%</span>
                            <span v-else-if="scope.row.taxRate == 6">9%</span>
                            <span v-else-if="scope.row.taxRate == 7">13%</span>
                            <span v-else>-</span>
                        </template>
                </el-table-column>
            </el-table>
        </template>
    </el-card>

    <el-card class="box-card" >
        <div slot="header" class="clearfix">
            <span>出库记录</span>
        </div>
        <template>
            <el-table
                    :data="warehouseOutRecordList"
                    border
                    style="width: 100%; "
                    key="warehouseOutRecord"
                    :cell-style="{'text-align':'center'}"
                    :header-cell-style="{'text-align':'center'}">
                <el-table-column
                        type="index"
                        label="序号"
                        min-width="50">
                </el-table-column>
                <el-table-column
                        label="产品名称"
                        min-width="30%">
                        <template slot-scope="scope">
                            <el-link type="primary" :underline="false" @click="viewSkuInfo(scope.row.goodsId)">{{scope.row.goodsName }}</el-link>
                        </template>
                </el-table-column>
                <el-table-column
                        prop="sku"
                        label="订货号"
                        min-width="10%">
                </el-table-column>
                <el-table-column
                        prop="brandName"
                        label="品牌"
                        min-width="10%">
                </el-table-column>
                <el-table-column
                        label="型号"
                        prop="model"
                        min-width="10%">
                </el-table-column>
                <el-table-column
                        prop="realGoodsNum"
                        label="实际出库数量"
                        min-width="10%">
                </el-table-column>
                <el-table-column
                        prop="unitName"
                        label="单位"
                        min-width="10%">
                </el-table-column>
                <el-table-column
                        prop="vedengBatchNumer"
                        label="贝登批次码"
                        min-width="10%">
                </el-table-column>
                <el-table-column
                        label="生产日期"
                        min-width="10%">
                    <template slot-scope="scope">
                        <span>{{parseTime(scope.row.productDate)}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="有效期至"
                        min-width="10%">
                    <template slot-scope="scope">
                        <span>{{parseTime(scope.row.expirationDate)}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="出库时间"
                        min-width="10%">
                    <template slot-scope="scope">
                        <span>{{parseTime(scope.row.addTime)}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="batchNumber"
                        label="批次号"
                        min-width="10%">
                </el-table-column>
                <el-table-column
                        prop="barcodeFactory"
                        label="SN码"
                        min-width="10%">
                </el-table-column>
                <el-table-column
                        prop="sterilizationBatchNo"
                        label="灭菌编码"
                        min-width="10%">
                </el-table-column>
                <el-table-column
                        prop="registrationNumber"
                        label="注册证号"
                        min-width="10%">
                </el-table-column>
            </el-table>
        </template>
    </el-card>

    <el-card class="box-card">
        <div slot="header" class="clearfix">
            <span>入库记录</span>
        </div>
        <template>
            <el-table
                    :data="warehouseInRecordList"
                    border
                    key="warehouseInRecord"
                    style="width: 100%; "
                    :cell-style="{'text-align':'center'}"
                    :header-cell-style="{'text-align':'center'}">
                <el-table-column
                        type="index"
                        label="序号"
                        min-width="50">
                </el-table-column>
                <el-table-column
                        label="产品名称"
                        min-width="30%">
                    <template slot-scope="scope">
                        <el-link type="primary" :underline="false" @click="viewSkuInfo(scope.row.goodsId)">{{scope.row.goodsName }}</el-link>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="sku"
                        label="订货号"
                        min-width="10%">
                </el-table-column>
                <el-table-column
                        prop="brandName"
                        label="品牌"
                        min-width="10%">
                </el-table-column>
                <el-table-column
                        label="型号"
                        prop="model"
                        min-width="10%">
                </el-table-column>
                <el-table-column
                        prop="realGoodsNum"
                        label="实际入库数量"
                        min-width="10%">
                </el-table-column>
                <el-table-column
                        prop="unitName"
                        label="单位"
                        min-width="10%">
                </el-table-column>
                <el-table-column
                        prop="vedengBatchNumer"
                        label="贝登批次码"
                        min-width="10%">
                </el-table-column>
                <el-table-column
                        label="生产日期"
                        min-width="10%">
                    <template slot-scope="scope">
                        <span>{{parseTime(scope.row.productDate)}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="有效期至"
                        min-width="10%">
                    <template slot-scope="scope">
                        <span>{{parseTime(scope.row.expirationDate)}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="入库时间"
                        min-width="10%">
                    <template slot-scope="scope">
                        <span>{{parseTime(scope.row.addTime)}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="batchNumber"
                        label="批次号"
                        min-width="10%">
                </el-table-column>
                <el-table-column
                        prop="barcodeFactory"
                        label="SN码"
                        min-width="10%">
                </el-table-column>
                <el-table-column
                        prop="sterilizationBatchNo"
                        label="灭菌编码"
                        min-width="10%">
                </el-table-column>
                <el-table-column
                        prop="registrationNumber"
                        label="注册证号"
                        min-width="10%">
                </el-table-column>
            </el-table>
        </template>
    </el-card>

    <el-card class="box-card">
        <div slot="header" class="clearfix">
            <span>审核记录</span>
        </div>
        <template>
            <el-table
                    :data="verifyInfo"
                    border
                    style="width: 100%; "
                    :cell-style="{'text-align':'center'}"
                    :header-cell-style="{'text-align':'center'}">
                <el-table-column
                        prop="operator"
                        label="操作人"
                        min-width="20%">
                </el-table-column>
                <el-table-column
                        label="操作时间"
                        min-width="10%">
                        <template slot-scope="scope">
                            <span>{{parseTime(scope.row.operationTime)}}</span>
                        </template>
                </el-table-column>
                <el-table-column
                        prop="operation"
                        label="操作事项"
                        min-width="10%">
                </el-table-column>
                <el-table-column
                        prop="remark"
                        label="备注"
                        min-width="10%">
                </el-table-column>

            </el-table>
        </template>
    </el-card>

    <el-row style="text-align: center; margin-bottom: 20px">
        <el-button type="primary" size="small" v-if="wmsUnitConversionOrderDto.verifyStatus == 1 && isAuditUser" @click="auditOperation(true)">审核通过</el-button>
        <el-button type="primary" size="small" v-if="wmsUnitConversionOrderDto.verifyStatus == 1 && isAuditUser" @click="auditOperation(false)">审核不通过</el-button>
        <el-button type="primary" size="small" v-if="(wmsUnitConversionOrderDto.verifyStatus == 0 || wmsUnitConversionOrderDto.verifyStatus == 3) && isOperator"
                   @click="edit()">编辑</el-button>
    </el-row>

    <el-dialog title="操作确认" :visible.sync="dialogFormVisible" width="500px">
        <el-form :model="auditForm" :rules="rules" ref="form">
            <el-form-item label="备注" prop="comments">
                <el-input v-model="auditForm.comments" autocomplete="off" style="width: 85%; height: 26px"></el-input>
            </el-form-item>

            <el-form-item style="text-align: center; margin-top: 10px">
                <el-button style="background-color: #72BB72; color: #FFFFFF" size="mini" @click="submitAudit('form')">提交</el-button>
                <el-button style="background-color: #FFAA02; color: #FFFFFF" size="mini" @click="dialogFormVisible = false">取消</el-button>
            </el-form-item>
        </el-form>
    </el-dialog>


</div>

<script src="${pageContext.request.contextPath}/static/api/wmsunitconversionorder/wmsUnitConversionOrder.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript">
    const wmsUnitConversionOrderId = '${wmsUnitConversionOrderId}';

    new Vue({
        el: '#app',
        data() {
            return {
                wmsUnitConversionOrderDto: {
                    wmsUnitConversionOrderItemDtoList: [],
                },
                // 出库记录
                warehouseOutRecordList: [],
                //入库记录
                warehouseInRecordList:[],
                // 审核记录
                verifyInfo: [],
                // 当前审核流程id，默认为‘0’
                taskId: '0',
                isAuditUser : false,
                isOperator : false,

                // 审核弹窗显隐
                dialogFormVisible: false,
                auditForm: {
                    comments: ''
                },
                globalPassFlag: false,

                rules: {
                    comments: [
                        {required: true, message: '请填写备注！', trigger: 'blur'},
                        {max: 256, message: '备注内容不允许超过256个字符', trigger: 'blur' }
                        ]
                },
            }
        },

        mounted() {
            loadingApp();
            this.initData()
        },

        methods: {
            // 页面初始化数据加载
            initData() {
                // 基础信息
                viewWmsUnitConversionOrderDetail({"wmsUnitConversionOrderId": wmsUnitConversionOrderId}).then(res => {
                    this.wmsUnitConversionOrderDto = res.data.data;
                });

                // 出库记录
                getOutLog({"wmsUnitConversionOrderId": wmsUnitConversionOrderId}).then(res => {
                    this.warehouseOutRecordList = res.data.data;
                });

                // 入库记录
                getInLog({"wmsUnitConversionOrderId": wmsUnitConversionOrderId}).then(res => {
                    this.warehouseInRecordList = res.data.data;
                });

                // 审核记录模块
                getVerifyInfo({"wmsUnitConversionOrderId": wmsUnitConversionOrderId}).then(res => {
                    this.taskId = res.data.data.taskId;
                    this.verifyInfo = res.data.data.auditRecordList;
                    this.isAuditUser = res.data.data.isAuditUser;
                    this.isOperator = res.data.data.isOperator;
                })
            },

            // 查看商品信息
            viewSkuInfo(id) {
                openTab("商品信息", '/goods/goods/viewbaseinfo.do?goodsId=' + id);
            },

            closeThis() {
                window.parent.closableTab.close($(window.parent.document).find('[role=presentation].active .glyphicon.small'), window.parent.closableTab.resizeMove);
            },

            // 点击审核按钮
            auditOperation(pass) {
                if (pass) {
                    this.rules.comments = [
                        {required: false, trigger: 'blur'}
                    ]
                } else {
                    this.rules.comments = [
                        {required: true, message: '请填写备注！', trigger: 'blur'},
                        {max: 256, message: '备注内容不允许超过256个字符', trigger: 'blur' }
                    ]
                }

                this.globalPassFlag = pass;
                this.auditForm.comments = '';
                this.dialogFormVisible = true;
            },

            // 审核弹窗提交
            submitAudit(form) {
                this.$refs[form].validate((valid) => {
                    if (valid) {
                        let auditVariables = {
                            taskId: this.taskId,
                            comment: this.auditForm.comments,
                            pass: this.globalPassFlag,
                            wmsUnitConversionOrderId: wmsUnitConversionOrderId
                        };
                        doAudit(auditVariables).then(res => {
                            if (res.code == 0) {
                                this.$alert('库存转换单提交成功', '提交成功', {
                                    confirmButtonText: '确定'
                                });
                            } else {
                                this.$alert('库存转换单提交失败', '提交失败', {
                                    confirmButtonText: '确定'
                                });
                            }
                            this.dialogFormVisible = false;
                            openTab("库存转换详情", '/wmsUnitConversion/detail.do?wmsUnitConversionOrderId=' + wmsUnitConversionOrderId);
                            this.closeThis()
                        });
                    } else {
                        return false;
                    }
                });
            },

            // 编辑
            edit() {
                openTab("编辑", '/wmsUnitConversion/edit.do?wmsUnitConversionOrderId=' + wmsUnitConversionOrderId);
                this.closeThis();
            }

        }
    })
</script>

<style>
    .el-descriptions__title {
        margin-left: 15px;
    }

    .el-descriptions__header {
        margin-bottom: 9px;
        padding-top: 9px;
    }

    .el-descriptions {
        background-color: #c5ddfb;
        margin-top: 20px;
    }

    .el-descriptions-item__cell.el-descriptions-item__content {
        text-align: center !important;
    }

    .el-descriptions-item__cell.el-descriptions-item__label.is-bordered-label {
        text-align: center;
        width: 10%;
    }

    .el-card__header {
        height: 34px;
        background-color: #c5ddfb;
        box-sizing: border-box;
        padding-left: 15px;
        font-weight: 700;
        padding-top: 9px;
        padding-bottom: 30px;
        color: #303133;
    }

    .el-card {
        margin-bottom: 20px;
    }

    .el-card__body {
        padding: 0;
    }

    .el-dialog__header {
        background-color: #f3f3f3;
        border-bottom: 1px solid #ddd;
        border-radius: 2px 2px 0 0;
        height: 35px;
        padding: 7px 15px 0;
    }

    .el-dialog__title {
        font-size: 14px;
        margin-left: 0;
    }

    .el-dialog__headerbtn {
        margin-top: -5px;
    }

    .el-dialog__body {
        padding: 20px 20px;
    }

    .my-content {
        width: 40%;
    }

</style>