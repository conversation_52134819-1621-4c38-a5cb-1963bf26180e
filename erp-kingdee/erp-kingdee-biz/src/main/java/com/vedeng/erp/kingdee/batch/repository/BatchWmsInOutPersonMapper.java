package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchWmsInOutPersonDto;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2023/12/11 10:40
 **/
public interface BatchWmsInOutPersonMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(BatchWmsInOutPersonDto record);

    int insertSelective(BatchWmsInOutPersonDto record);

    BatchWmsInOutPersonDto selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(BatchWmsInOutPersonDto record);

    int updateByPrimaryKey(BatchWmsInOutPersonDto record);

    List<BatchWmsInOutPersonDto> selectByTypeAndDate(@Param("type")Integer type,@Param("date")String date);

    List<BatchWmsInOutPersonDto> selectByType(@Param("type")Integer type);




}