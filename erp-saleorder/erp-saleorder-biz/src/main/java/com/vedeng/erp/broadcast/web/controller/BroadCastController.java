package com.vedeng.erp.broadcast.web.controller;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 * 到款通知前端路由
 */
@Controller()
@RequestMapping("/broadcast/index")
public class BroadCastController {
    /**
     * 人员管理
     * @return ModelAndView
     */
    @NoNeedAccessAuthorization
    @RequestMapping("/user")
    public ModelAndView staff() {
        return new ModelAndView("vue/view/broadcast/user");
    }

    /**
     * 部门管理
     * @return ModelAndView
     */
    @NoNeedAccessAuthorization
    @RequestMapping("/department")
    public ModelAndView department() {
        return new ModelAndView("vue/view/broadcast/department");
    }

    /**
     * 部门配置
     * @return ModelAndView
     */
    @NoNeedAccessAuthorization
    @RequestMapping("/departmentEdit")
    public ModelAndView departmentEdit() {
        return new ModelAndView("vue/view/broadcast/departmentEdit");
    }

    /**
     * 播报管理
     * @return ModelAndView
     */
    @NoNeedAccessAuthorization
    @RequestMapping("/broadcast")
    public ModelAndView broadcast() {
        return new ModelAndView("vue/view/broadcast/broadcast");
    }

    /**
     * 播报配置
     * @return ModelAndView
     */
    @NoNeedAccessAuthorization
    @RequestMapping("/broadcastEdit")
    public ModelAndView broadcastEdit() {
        return new ModelAndView("vue/view/broadcast/broadcastEdit");
    }

    /**
     * 目标管理
     * @return ModelAndView
     */
    @NoNeedAccessAuthorization
    @RequestMapping("/target")
    public ModelAndView target() {
        return new ModelAndView("vue/view/broadcast/target");
    }
}
