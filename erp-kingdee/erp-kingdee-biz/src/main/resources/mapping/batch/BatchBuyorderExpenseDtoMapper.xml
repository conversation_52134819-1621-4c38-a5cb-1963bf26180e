<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchBuyorderExpenseDtoMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchBuyorderExpenseDto">
        <id column="BUYORDER_EXPENSE_ID" jdbcType="INTEGER" property="buyorderExpenseId"/>
        <result column="BUYORDER_EXPENSE_NO" jdbcType="VARCHAR" property="buyorderExpenseNo"/>
        <result column="BUYORDER_NO" jdbcType="VARCHAR" property="buyorderNo"/>
        <result column="BUYORDER_ID" jdbcType="INTEGER" property="buyorderId"/>
        <result column="ORDER_TYPE" jdbcType="INTEGER" property="orderType"/>
        <result column="VALID_STATUS" jdbcType="INTEGER" property="validStatus"/>
        <result column="VALID_TIME" jdbcType="TIMESTAMP" property="validTime"/>
        <result column="STATUS" jdbcType="INTEGER" property="status"/>
        <result column="LOCKED_STATUS" jdbcType="INTEGER" property="lockedStatus"/>
        <result column="INVOICE_STATUS" jdbcType="INTEGER" property="invoiceStatus"/>
        <result column="PAYMENT_STATUS" jdbcType="INTEGER" property="paymentStatus"/>
        <result column="DELIVERY_STATUS" jdbcType="INTEGER" property="deliveryStatus"/>
        <result column="ARRIVAL_STATUS" jdbcType="INTEGER" property="arrivalStatus"/>
        <result column="SERVICE_STATUS" jdbcType="INTEGER" property="serviceStatus"/>
        <result column="AUDIT_STATUS" jdbcType="INTEGER" property="auditStatus"/>
        <result column="INVOICE_TIME" jdbcType="TIMESTAMP" property="invoiceTime"/>
        <result column="PAYMENT_TIME" jdbcType="TIMESTAMP" property="paymentTime"/>
        <result column="AUDIT_TIME" jdbcType="TIMESTAMP" property="auditTime"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName"/>
        <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete"/>
        <result column="BUSINESS_TYPE" jdbcType="INTEGER" property="businessType"/>
        <result column="AFTER_SALES_NO" jdbcType="VARCHAR" property="afterSalesNo"/>
        <result column="AFTER_SALES_ID" jdbcType="INTEGER" property="afterSalesId"/>
        <association property="buyorderExpenseDetailDto"
                     javaType="com.vedeng.erp.kingdee.batch.dto.BatchBuyorderExpenseDetailDto">
            <id column="BUYORDER_EXPENSE_DETAIL_ID" jdbcType="INTEGER" property="buyorderExpenseDetailId"/>
            <result column="BUYORDER_EXPENSE_ID" jdbcType="INTEGER" property="buyorderExpenseId"/>
            <result column="PAYMENT_TYPE" jdbcType="INTEGER" property="paymentType"/>
            <result column="PAYMENT_COMMENTS" jdbcType="VARCHAR" property="paymentComments"/>
            <result column="INVOICE_TYPE" jdbcType="INTEGER" property="invoiceType"/>
            <result column="INVOICE_COMMENTS" jdbcType="VARCHAR" property="invoiceComments"/>
            <result column="TOTAL_AMOUNT" jdbcType="DECIMAL" property="totalAmount"/>
            <result column="TRADER_ID" jdbcType="INTEGER" property="traderId"/>
            <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName"/>
            <result column="TRADER_CONTACT_ID" jdbcType="INTEGER" property="traderContactId"/>
            <result column="TRADER_CONTACT_NAME" jdbcType="VARCHAR" property="traderContactName"/>
            <result column="TRADER_CONTACT_MOBILE" jdbcType="VARCHAR" property="traderContactMobile"/>
            <result column="TRADER_CONTACT_TELEPHONE" jdbcType="VARCHAR" property="traderContactTelephone"/>
            <result column="TRADER_ADDRESS_ID" jdbcType="INTEGER" property="traderAddressId"/>
            <result column="TRADER_AREA" jdbcType="VARCHAR" property="traderArea"/>
            <result column="TRADER_ADDRESS" jdbcType="VARCHAR" property="traderAddress"/>
            <result column="TRADER_COMMENTS" jdbcType="VARCHAR" property="traderComments"/>
            <result column="PREPAID_AMOUNT" jdbcType="DECIMAL" property="prepaidAmount"/>
            <result column="ACCOUNT_PERIOD_AMOUNT" jdbcType="DECIMAL" property="accountPeriodAmount"/>
            <result column="PERIOD_DAY" jdbcType="INTEGER" property="periodDay"/>
            <result column="RETAINAGE_AMOUNT" jdbcType="DECIMAL" property="retainageAmount"/>
            <result column="RETAINAGE_AMOUNT_MONTH" jdbcType="INTEGER" property="retainageAmountMonth"/>
            <result column="EXPENSE_SOURCE" jdbcType="VARCHAR" property="expenseSource"/>
            <result column="ORG_ID" jdbcType="INTEGER" property="orgId"/>
            <result column="CONTRACT_URL" jdbcType="VARCHAR" property="contractUrl"/>
            <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete"/>
            <result column="TRADER_SUPPLIER_ID" jdbcType="INTEGER" property="traderSupplierId"/>
        </association>
    </resultMap>


    <!--auto generated by MybatisCodeHelper on 2022-12-05-->
    <select id="findByAll" resultMap="BaseResultMap">
        select TBE.*,
               TBED.*,
               TTS.TRADER_SUPPLIER_ID
        from T_BUYORDER_EXPENSE TBE
                 left join T_BUYORDER_EXPENSE_DETAIL TBED on TBE.BUYORDER_EXPENSE_ID = TBED.BUYORDER_EXPENSE_ID
                 left join T_TRADER_SUPPLIER TTS on TBED.TRADER_ID = TTS.TRADER_ID
        <where>
            <if test="buyorderExpenseId != null">
                and TBE.BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER}
            </if>
            <if test="buyorderExpenseNo != null and buyorderExpenseNo != ''">
                and BUYORDER_EXPENSE_NO = #{buyorderExpenseNo,jdbcType=VARCHAR}
            </if>
            <if test="buyorderNo != null and buyorderNo != ''">
                and BUYORDER_NO = #{buyorderNo,jdbcType=VARCHAR}
            </if>
            <if test="buyorderId != null">
                and BUYORDER_ID = #{buyorderId,jdbcType=INTEGER}
            </if>
            <if test="orderType != null">
                and ORDER_TYPE = #{orderType,jdbcType=INTEGER}
            </if>
            <if test="validStatus != null">
                and VALID_STATUS = #{validStatus,jdbcType=INTEGER}
            </if>
            <if test="validTime != null">
                and VALID_TIME = #{validTime,jdbcType=TIMESTAMP}
            </if>
            <if test="status != null">
                and `STATUS` = #{status,jdbcType=INTEGER}
            </if>
            <if test="lockedStatus != null">
                and LOCKED_STATUS = #{lockedStatus,jdbcType=INTEGER}
            </if>
            <if test="invoiceStatus != null">
                and INVOICE_STATUS = #{invoiceStatus,jdbcType=INTEGER}
            </if>
            <if test="paymentStatus != null">
                and PAYMENT_STATUS = #{paymentStatus,jdbcType=INTEGER}
            </if>
            <if test="deliveryStatus != null">
                and DELIVERY_STATUS = #{deliveryStatus,jdbcType=INTEGER}
            </if>
            <if test="arrivalStatus != null">
                and ARRIVAL_STATUS = #{arrivalStatus,jdbcType=INTEGER}
            </if>
            <if test="serviceStatus != null">
                and SERVICE_STATUS = #{serviceStatus,jdbcType=INTEGER}
            </if>
            <if test="auditStatus != null">
                and AUDIT_STATUS = #{auditStatus,jdbcType=INTEGER}
            </if>
            <if test="invoiceTime != null">
                and INVOICE_TIME = #{invoiceTime,jdbcType=TIMESTAMP}
            </if>
            <if test="paymentTime != null">
                and PAYMENT_TIME = #{paymentTime,jdbcType=TIMESTAMP}
            </if>
            <if test="auditTime != null">
                and AUDIT_TIME = #{auditTime,jdbcType=TIMESTAMP}
            </if>
            <if test="businessType != null">
                and BUSINESS_TYPE = #{businessType,jdbcType=INTEGER}
            </if>
            <if test="afterSalesNo != null and afterSalesNo != ''">
                and AFTER_SALES_NO = #{afterSalesNo,jdbcType=VARCHAR}
            </if>
            <if test="afterSalesId != null">
                and AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <select id="getAfterSalesNo" parameterType="java.lang.Integer" resultType="java.lang.String">
        select EXPENSE_AFTER_SALES_NO
        from T_EXPENSE_AFTER_SALES
        where  EXPENSE_AFTER_SALES_ID = #{expenseAfterSalesId,jdbcType=INTEGER}
    </select>



    <select id="getAfterSalesNoByBuyorderExpenseId" parameterType="java.lang.Integer" resultType="java.lang.String">
        select EXPENSE_AFTER_SALES_NO
        from T_EXPENSE_AFTER_SALES TEAS
        left join T_BUYORDER_EXPENSE TBE on TBE.BUYORDER_EXPENSE_ID = TEAS.BUYORDER_EXPENSE_ID
        where TBE.BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER}
    </select>

    <select id="getAfterSalesTime" parameterType="java.lang.Integer" resultType="java.util.Date">
        select ADD_TIME
        from T_EXPENSE_AFTER_SALES
        where  EXPENSE_AFTER_SALES_ID = #{expenseAfterSalesId,jdbcType=INTEGER}
    </select>

    <select id="findTraderSupplierIdByBuyorderExpenseNo" resultType="java.lang.Integer">
        select
               TTS.TRADER_SUPPLIER_ID
        from T_BUYORDER_EXPENSE TBE
                 left join T_BUYORDER_EXPENSE_DETAIL TBED on TBE.BUYORDER_EXPENSE_ID = TBED.BUYORDER_EXPENSE_ID
                 left join T_TRADER_SUPPLIER TTS on TBED.TRADER_ID = TTS.TRADER_ID
        where
            TBE.BUYORDER_EXPENSE_NO = #{buyorderExpenseNo,jdbcType=VARCHAR}
        group by TBE.BUYORDER_EXPENSE_NO
    </select>

    <select id="findTraderSupplierIdByExpenseAfterSalesNo" resultType="java.lang.Integer">
        select
            TTS.TRADER_SUPPLIER_ID
        from T_EXPENSE_AFTER_SALES TEAS
                 left join T_TRADER_SUPPLIER TTS on TTS.TRADER_ID = TEAS.TRADER_ID
        where
            TEAS.EXPENSE_AFTER_SALES_NO = #{expenseAfterSalesNo,jdbcType=VARCHAR}
        group by TEAS.EXPENSE_AFTER_SALES_NO
    </select>
</mapper>