<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="暂不备货" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='<%= basePath %>static/js/flash/prepare/deal_stock.js?rnd=${resourceVersionKey}'></script>
<div class="content">
    <div class="sku-info">
        <ul>
            <li style="width: 200px;">
                <div class="infor_name">
                    <label class="info">订货号：</label>
                </div>
                <div class="info-name">
                    <label>${prepareStock.skuNo}</label>
                </div>
            </li>
            <li>
                <div class="infor_name">
                    <label class="info">商品名称：</label>
                </div>
                <div class="info-name">
                    <label>${prepareStock.skuName}</label>
                </div>
            </li>
            <br/>
            <br/>
            <li class="li-wid" style="width: 250px;">
                <div class="infor_name">
                    <label class="info">预警级别：</label>
                </div>
                <div class="info-name">
                    <label>
                        <c:choose>
                            <c:when test="${empty prepareStock.warnLevel || prepareStock.warnLevel == 0}">
                                <span style="color:green;">安全</span>
                            </c:when>
                            <c:when test="${prepareStock.warnLevel == 1}">
                                <span style="color:orange;">二级预警-需补货</span>
                            </c:when>
                            <c:when test="${prepareStock.warnLevel == 2}">
                                <span style="color:red;">一级预警-缺货</span>
                            </c:when>
                        </c:choose>
                    </label>
                </div>
            </li>
            <li class="li-wid">
                <div class="infor_name">
                    <label class="info">安全库存：</label>
                </div>
                <div class="info-name">
                    <label>${prepareStock.safeStock}&nbsp${prepareStock.unit}</label>
                </div>
            </li>
            <li class="li-wid">
                <div class="infor_name">
                    <label class="info">库存数量：</label>
                </div>
                <div class="info-name">
                    <label>${prepareStock.stock}&nbsp${prepareStock.unit}</label>
                </div>
            </li>
            <li class="li-wid" style="width: 230px;">
                <div class="infor_name" style="width: 135px">
                    <label class="info">建议补充库存数量：</label>
                </div>
                <div class="info-name">
                    <label>${prepareStock.supplementStock}&nbsp${prepareStock.unit}</label>
                </div>
            </li>
        </ul>
    </div>
    <div class="sku-opr">
        <form id="deal_safe_stock">
            <input type="hidden" name="dealTypeVal" id="dealTypeVal" value="${prepareStock.dealType}">
            <input type="hidden" name="safeStock" id="safeStock" value="${prepareStock.safeStock}">
            <input type="hidden" name="originSafeStock" id="originSafeStock" value="${prepareStock.safeStock}">
            <input type="hidden" name="regularId" id="regularId" value="${prepareStock.regularId}">
            <input type="hidden" name="skuId" id="skuId" value="${prepareStock.skuId}">
            <input type="hidden" name="skuNo" id="skuNo" value="${prepareStock.skuNo}">
            <input type="hidden" name="skuName" id="skuName" value="${prepareStock.skuName}">
            <input type="hidden" name="brandName" id="brandName" value="${prepareStock.brandName}">
            <input type="hidden" name="spuType" id="spuType" value="${prepareStock.spuType}">
            <input type="hidden" name="categoryName" id="categoryName" value="${prepareStock.categoryName}">
            <input type="hidden" name="spec" id="spec" value="${prepareStock.spec}">
            <input type="hidden" name="unit" id="unit" value="${prepareStock.unit}">
            <input type="hidden" name="cost" id="cost" value="${prepareStock.cost}">
            <input type="hidden" name="owerUserId" id="owerUserId" value="${prepareStock.owerUserId}">
            <input type="hidden" name="owerUser" id="owerUser" value="${prepareStock.owerUser}">
            <input type="hidden" name="supplementStock" id="supplementStock" value="${prepareStock.supplementStock}">
            <input type="hidden" name="lastAddTime" id="lastAddTime" value="${prepareStock.lastAddTime}">
            <input type="hidden" name="amount" id="amount" value="${prepareStock.amount}">
            <input type="hidden" name="yxgAmount" id="yxgAmount" value="${prepareStock.yxgAmount}">
            <input type="hidden" name="threeMonthAmount" id="threeMonthAmount" value="${prepareStock.threeMonthAmount}">
            <input type="hidden" name="yxgThreeMonthAmount" id="yxgThreeMonthAmount" value="${prepareStock.yxgThreeMonthAmount}">
            <input type="hidden" name="oneMonthAmount" id="oneMonthAmount" value="${prepareStock.oneMonthAmount}">
            <input type="hidden" name="yxgOneMonthAmount" id="yxgOneMonthAmount" value="${prepareStock.yxgOneMonthAmount}">
            <c:choose>
                <c:when test="${empty prepareStock.warnLevel || prepareStock.warnLevel == 0}">
                    <input type="hidden" name="stockWarn" id="stockWarn" value="安全">
                </c:when>
                <c:when test="${prepareStock.warnLevel == 1}">
                    <input type="hidden" name="stockWarn" id="stockWarn" value="二级预警-需补货">
                </c:when>
                <c:when test="${prepareStock.warnLevel == 2}">
                    <input type="hidden" name="stockWarn" id="stockWarn" value="一级预警-缺货">
                </c:when>
            </c:choose>
            <input type="hidden" name="stock" id="stock" value="${prepareStock.stock}">
            <input type="hidden" name="intransitStock" id="intransitStock" value="${prepareStock.intransitStock}">
            <input type="hidden" name="orderStock" id="orderStock" value="${prepareStock.orderStock}">
            <input type="hidden" name="forecastSafeStock" id="forecastSafeStock" value="${prepareStock.forecastSafeStock}">
            <input type="hidden" name="receiveTimes" id="receiveTimes" value="${prepareStock.receiveTimes}">
            <input type="hidden" name="outStockTimes" id="outStockTimes" value="${prepareStock.outStockTimes}">
            <ul class="lastResult">
                <li>
                    <div class="infor_name label-c" style="width: 150px;">
                        <span>*</span><label class="info">暂不备货原因：</label>
                    </div>
                    <div class="f_left">
                        <select class="input-middle label-c select-c" name="dealType" id="dealType" onchange="changeDealType(this.options[this.options.selectedIndex].value)">
                            <option value="-1">请选择</option>
                            <option value="3">暂不备货</option>
                            <option value="1">替换</option>
                            <option value="2">库存转换</option>
                            <option value="0">删除</option>
                        </select>
                    </div>
                </li>
                <li class="deal_type_error" style="display:none; margin: 0;">
                    <div class="infor_name label-c" style="width: 150px;">
                    </div>
                    <div class="f_left">
                        <span style="color:red;">请选择暂不备货原因!</span>
                    </div>
                </li>
                <li id="replace_sku" style="display: none;">
                    <div class="infor_name label-c" style="width: 150px;">
                        <span>*</span><label class="info">替换SKU：</label>
                    </div>
                    <div class="f_left">
                        <input type="text" class="input-c label-c mr5 "
                               placeholder="请输入订货号" name="replaceSkuNo" id="replaceSkuNo"/>
                        <button type="button" class="label-c bg-cancle-blue bt-bg-style bt-largest" style="width: 80px;border-radius: 6px;" onclick="searchSku()">查询</button>
                    </div>
                </li>
                <li class="replace-sku" style="display: none;">
                    <div class="infor_name label-c" style="width: 150px;">
                    </div>
                    <div class="f_left">
                    </div>
                </li>
                <li class="replace_sku_error" style="display:none; margin: 0;">
                    <div class="infor_name label-c" style="width: 150px;">
                    </div>
                    <div class="f_left">
                        <span style="color:red;">请输入替换SKU!</span>
                    </div>
                </li>
                <li>
                    <div class="infor_name label-c" style="width: 150px;">
                        原因：
                    </div>
                    <div class="f_left">
                        <textarea class="input-textarea" cols="f_left" id="operateReason" name="operateReason" placeholder="请输入调整原因,最多不超过500字" rows=4 maxLength="500"></textarea>
                    </div>
                </li>
            </ul>
            <div class="submit-c tcenter">
                <button class="bt-largest bt-bg-style bg-light-blue" type="button" onclick="dealStock();">确定</button>
                <button id="close-layer" type="button" class="bg-cancle-blue bt-bg-style bt-largest">取消</button>
            </div>
        </form>
    </div>
</div>
<style>
    .sku-info {
        width: 100%;
        padding: 20px;
        font-size: 15px;
        border-bottom: 1px dotted ;
    }

    .sku-info ul li {
        float: left;
        height: 30px;
        line-height: 30px;
    }

    .info {
        font-weight: 500;
    }

    .sku-info .info-name {
        float: left;
        margin: 3px 10px 0 0;
    }

    .sku-opr {
        padding: 20px;
        font-size: 15px;
    }

    .sku-opr ul li {
        margin-top: 20px;
    }

    .li-wid {
        width: 200px;
        margin-top: 10px;
    }

    .label-c {
        height: 40px;
        line-height: 40px;
    }

    .input-c {
        width: 400px;
        border-color: darkgray;
    }

    .select-c {
        width: 400px;
    }

    .input-textarea {
        width: 600px;
    }

    .submit-c {
        margin-top:30px;
    }

    .bg-cancle-blue {
        background: white;
        color: #1b76ed;
        border: 1px solid #1b76ed;
    }
</style>
<%@ include file="../../common/footer.jsp"%>
