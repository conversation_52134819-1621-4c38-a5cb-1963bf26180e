package com.vedeng.finance.service.impl;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.common.constants.Contant;
import com.vedeng.billsync.dao.BankBillExtMapper;
import com.vedeng.billsync.task.model.entity.BankBillExtDo;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.util.DateUtil;
import com.vedeng.erp.aftersale.service.BuyorderExpenseItemApiService;
import com.vedeng.erp.finance.dto.*;
import com.vedeng.erp.finance.service.BankBillApiService;
import com.vedeng.erp.finance.service.BankBillIgnoreRecordApiService;
import com.vedeng.erp.finance.service.PayVedengBankApiService;
import com.vedeng.erp.kingdee.dto.result.KingDeeCustomerResultDto;
import com.vedeng.erp.kingdee.enums.KingdeeIgnoreUnitTypeEnums;
import com.vedeng.finance.dao.CapitalBillMapper;
import com.vedeng.finance.model.CapitalBill;
import com.vedeng.finance.service.CapitalBillService;
import com.vedeng.finance.dto.BankNoDto;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryExtParam;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryFilterDto;
import com.vedeng.infrastructure.oss.service.OssUtilsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.formula.functions.T;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.http.HttpClientUtils;
import com.vedeng.common.key.CryptBase64Tool;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.util.HttpRequest;
import com.vedeng.common.util.XmlExercise;
import com.vedeng.finance.model.BankBill;
import com.vedeng.finance.model.NjBankInterfaceInfo;
import com.vedeng.finance.model.PayApply;
import com.vedeng.finance.service.BankBillService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

@Service("bankBillService")
public class BankBillServiceImpl extends BaseServiceimpl implements BankBillService {
	public static Logger logger = LoggerFactory.getLogger(BankBillServiceImpl.class);

	public static final String PDF = "pdf";

	@Autowired
	@Qualifier("userMapper")
	private UserMapper userMapper;

	@Autowired
	private BankBillExtMapper bankBillExtMapper;

	@Autowired
	private CapitalBillMapper capitalBillMapper;

	@Autowired
	private OssUtilsService ossUtilsService;

	@Autowired
	private BankBillIgnoreRecordApiService bankBillIgnoreRecordApiService;

	@Autowired
	private PayVedengBankApiService payVedengBankApiService;

	@Autowired
	private KingDeeBaseApi kingDeeBaseApi;
	@Autowired
	private BankBillApiService bankBillApiService;

	/**
     * 获取银行流水列表
     */
    @Override
    public Map<String, Object> getBankBillListPage(BankBill bankBill, Page page) {
	Map<String, Object> map = new HashMap<>();
	// 调用银行流水列表
	String url = httpUrl + "finance/bankbill/getbankbilllistpage.htm";
	// 定义反序列化 数据格式
	List<BankBill> bankBillList = null;
	final TypeReference<ResultInfo<List<BankBill>>> TypeRef = new TypeReference<ResultInfo<List<BankBill>>>() {
	};
	final TypeReference<ResultInfo<Map<String, Object>>> TypeRefM = new TypeReference<ResultInfo<Map<String, Object>>>() {
	};
	try {
	    // 获取银行流水列表
	    ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, bankBill, clientId, clientKey, TypeRef, page);

	    if (result.getCode() == 0) {
		bankBillList = (List<BankBill>) result.getData();
		map.put("list", bankBillList);
		//匹配金蝶凭证号
		if (CollUtil.isNotEmpty(bankBillList)){
			bankBillList.forEach(b -> {
				String financeVoucherDate = b.getFinanceVoucherDate();
				String financeVoucherNo = b.getFinanceVoucherNo();
				if (StrUtil.isNotBlank(financeVoucherDate)){
					if (StrUtil.isNotBlank(financeVoucherNo)){
						b.setFinanceVoucherNo(financeVoucherDate +" "+ financeVoucherNo);
					}else {
						b.setFinanceVoucherNo(financeVoucherDate);
					}
				}
				BankBillDto dto = new BankBillDto();
				dto.setBankBillId(b.getBankBillId());
				dto.setStatus(b.getStatus());
				dto.setTranFlow(b.getTranFlow());
				VoucherNoAndDateDto voucherNoAndDateDto = bankBillApiService.matchVoucherNo(dto);
				b.setVoucherNo(voucherNoAndDateDto.getVoucherNo());
				b.setVoucherDate(voucherNoAndDateDto.getVoucherDate());
				
				
			});
		}
		map.put("page", result.getPage());
		String urlM = httpUrl + "finance/bankbill/getbankbilllistinfo.htm";
		ResultInfo<?> resultM = (ResultInfo<?>) HttpClientUtils.post(urlM, bankBill, clientId, clientKey,
			TypeRefM, page);
		Map<String, Object> result_map = null;
		result_map = (Map<String, Object>) resultM.getData();
		BigDecimal getAmount = new BigDecimal(result_map.get("getAmount").toString());
		BigDecimal payAmount = new BigDecimal(result_map.get("payAmount").toString());
		BigDecimal orderAmount = new BigDecimal(result_map.get("orderAmount").toString());
		BigDecimal matchAmount = new BigDecimal(result_map.get("matchAmount").toString());
		Integer orderNum = new Integer(result_map.get("orderNum").toString());
		map.put("getAmount", getAmount);
		map.put("payAmount", payAmount);
		map.put("orderAmount", orderAmount);
		map.put("matchAmount", matchAmount);
		map.put("orderNum", orderNum);

	    }
	} catch (IOException e) {
	    logger.error(Contant.ERROR_MSG, e);
	    return null;
	}
	return map;
    }
	
    /**
     * 获取银行流水匹配列表
     */
    @Override
    public Map<String, Object> getBankBillMatchListPage(BankBill bankBill, Page page,boolean calCount) {
	Map<String, Object> map = new HashMap<>();
	// 调用银行流水列表
	String url = httpUrl + "finance/bankbill/getbankbillmatchlistpage.htm";
	// 定义反序列化 数据格式
	List<BankBill> bankBillList = null;
	final TypeReference<ResultInfo<List<BankBill>>> TypeRef = new TypeReference<ResultInfo<List<BankBill>>>() {
	};
	final TypeReference<ResultInfo<Map<String, Object>>> TypeRefM = new TypeReference<ResultInfo<Map<String, Object>>>() {
	};
	try {
	    // 获取匹配过后的银行流水列表
	    ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post120(url, bankBill, clientId, clientKey, TypeRef,
		    page);
	    if (result.getCode() == 0) {
		bankBillList = (List<BankBill>) result.getData();
		map.put("list", bankBillList);
		map.put("page", result.getPage());
		if(calCount){
			String urlM = httpUrl + "finance/bankbill/getbankbilllistinfo.htm";
			ResultInfo<?> resultM = (ResultInfo<?>) HttpClientUtils.post120(urlM, bankBill, clientId, clientKey,
				TypeRefM, page);
			Map<String, Object> result_map = null;
			result_map = (Map<String, Object>) resultM.getData();
			BigDecimal getAmount = new BigDecimal(result_map.get("getAmount").toString());
			BigDecimal payAmount = new BigDecimal(result_map.get("payAmount").toString());
			BigDecimal orderAmount = new BigDecimal(result_map.get("orderAmount").toString());
			BigDecimal matchAmount = new BigDecimal(result_map.get("matchAmount").toString());
			Integer orderNum = new Integer(result_map.get("orderNum").toString());
			map.put("getAmount", getAmount);
			map.put("payAmount", payAmount);
			map.put("orderAmount", orderAmount);
			map.put("matchAmount", matchAmount);
			map.put("orderNum", orderNum);
			}
	    }
	} catch (IOException e) {
	    logger.error(Contant.ERROR_MSG, e);
	    return null;
	}
	return map;
    }

    @Override
    public ResultInfo editBankBill(BankBill bankBill) {
	String url = httpUrl + "finance/bankbill/editbankbill.htm";
	final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
	};
	try {
	    ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, bankBill, clientId, clientKey, TypeRef);
	    // 接口返回条码生成的记录
	    if (result.getCode() == 0) {
		return new ResultInfo(0, "修改成功");
	    } else {
		return new ResultInfo();
	    }
	} catch (IOException e) {
	    logger.error(Contant.ERROR_MSG, e);
	    return new ResultInfo();
	}
    }

    @Override
    public BankBill getBankBillById(Integer bankBillId) {
	String url = httpUrl + "finance/bankbill/getbankbillbyid.htm";
	// 定义反序列化 数据格式
	final TypeReference<ResultInfo<BankBill>> TypeRef = new TypeReference<ResultInfo<BankBill>>() {
	};
	ResultInfo<?> result;
	BankBill bankBill = new BankBill();
	try {
	    result = (ResultInfo<?>) HttpClientUtils.post(url, bankBillId, clientId, clientKey, TypeRef);
	    bankBill = (BankBill) result.getData();
	} catch (IOException e) {
	    logger.error(Contant.ERROR_MSG, e);
	}
	return bankBill;
    }

    @Override
    public ResultInfo addBankPayApply(PayApply payApplyInfo) {
	String url = httpUrl + "finance/bankbill/addbankpayapply.htm";
	// 定义反序列化 数据格式
	final TypeReference<ResultInfo<NjBankInterfaceInfo>> TypeRef = new TypeReference<ResultInfo<NjBankInterfaceInfo>>() {
	};
	ResultInfo<?> result = new ResultInfo<>();
	try {
	    result = (ResultInfo<?>) HttpClientUtils.post(url, payApplyInfo, clientId, clientKey, TypeRef);
	} catch (IOException e) {
	    logger.error(Contant.ERROR_MSG+"addBankPayApply", e);
	}
	return result;
    }

	@SuppressWarnings("unchecked")
	@Override
	public ResultInfo sendBankBillList(BankBill bankBill, Page page, HttpSession session) throws Exception {
		String bankName="";
		if(bankBill.getBankTag()==null){
			bankName="南京银行#+";
		}else if(bankBill.getBankTag()==1){
			bankName="建设银行#+";
		}
		else if(bankBill.getBankTag()==4){
			bankName="支付宝#+";
		}else if(bankBill.getBankTag()==5){
			bankName="微信#+";
		}
		SimpleDateFormat sdf = new SimpleDateFormat("yyMMdd");
		ResultInfo result = new ResultInfo();
		List<BankBill> mapBillList = null;

		User user = (User) session.getAttribute(Consts.SESSION_USER);

		//获取发送至金蝶银行流水
		ResultInfo<List<BankBill>> resultMap = (ResultInfo<List<BankBill>>) this.sendBankBillListPage(bankBill, page);
		mapBillList = (List<BankBill>) resultMap.getData();
		mapBillList = mapBillList.stream().filter(res-> !StringUtils.isEmpty( res.getUserId())).collect(Collectors.toList());
		if(null != mapBillList && mapBillList.size() > 0){
			for (BankBill bankBill2 : mapBillList) {
				//查询销售人员名称
				String[] userId = bankBill2.getUserId().split(",");
				bankBill2.setUserId(userId[0]);

				//可优化（批量查）
				if(userId.length > 1) {
					bankBill2.setUserName(userMapper.getUserNameByUserId(Integer.parseInt(userId[0].trim())) + "等");
				}else {
					bankBill2.setUserName(userMapper.getUserNameByUserId(Integer.parseInt(userId[0].trim())));
				}
				//销售订单编号
				String[] orderNo = bankBill2.getSaleorderNo().split(",");
				if(orderNo.length > 1){
					bankBill2.setSaleorderNo(orderNo[0] + "等");
				}

				//客户名称
				if(null != bankBill2.getTraderName() && !("".equals(bankBill2.getTraderName()))){
					String[] traderName = bankBill2.getTraderName().split(",");
					if(traderName.length > 1) {
						bankBill2.setTraderName(traderName[0]);
					}
				}
				//客户Id
				if(null != bankBill2.getTraderId() && !("".equals(bankBill2.getTraderId()))){
					String[] traderId = bankBill2.getTraderId().split(",");
					if(traderId.length > 0) {
						bankBill2.setTraderId(traderId[0]);
					}
				}

				String time ="";
				if(!StringUtils.isEmpty(bankBill2.getRealTrandatetime())){
					Date date = DateUtil.StringToDate(bankBill2.getRealTrandatetime(), "yyyy-MM-dd");
					time =sdf.format(date);
				}
				//备注
				bankBill2.setComments(bankName + bankBill2.getTranFlow() +  "#+" +time + "#+" + bankBill2.getSaleorderNo() + "+" + bankBill2.getUserName());

				bankBill2.setUserId(user.getUserId().toString());
			}
		}

		Integer pageCount =  resultMap.getPage().getTotalPage();
		for (int i = 2; i <= pageCount; i++) {
			page.setPageNo(i);
			ResultInfo<List<BankBill>> listMap = (ResultInfo<List<BankBill>>) this.sendBankBillListPage(bankBill, page);
			List<BankBill> list = (List<BankBill>) listMap.getData();
			if(null != list && list.size() > 0){
				for (BankBill bankBill2 : list) {
					//查询销售人员名称
					String[] userId = bankBill2.getUserId().split(",");
					if(userId.length > 1) {
						bankBill2.setUserName(userMapper.getUserNameByUserId(Integer.valueOf(userId[0])) + "等");
					}else {
						bankBill2.setUserName(userMapper.getUserNameByUserId(Integer.valueOf(userId[0])));
					}
					//销售订单编号
					String[] orderNo = bankBill2.getSaleorderNo().split(",");
					if(orderNo.length > 1){
						bankBill2.setSaleorderNo(orderNo[0] + "等");
					}
					//客户名称
					if(null != bankBill2.getTraderName() && !("".equals(bankBill2.getTraderName()))){
						String[] traderName = bankBill2.getTraderName().split(",");
						if(traderName.length > 1) {
							bankBill2.setTraderName(traderName[0]);
						}
					}
					//客户Id
					if(null != bankBill2.getTraderId() && !("".equals(bankBill2.getTraderId()))){
						String[] traderId = bankBill2.getTraderId().split(",");
						if(traderId.length > 0) {
							bankBill2.setTraderId(traderId[0]);
						}
					}

					String time ="";
					if(!StringUtils.isEmpty(bankBill2.getRealTrandatetime())){
						Date date = DateUtil.StringToDate(bankBill2.getRealTrandatetime(), "yyyy-MM-dd");
						time =sdf.format(date);
					}
					//备注
					bankBill2.setComments(bankName + bankBill2.getTranFlow() + "#+" +time+"#+" + bankBill2.getSaleorderNo() + "+" + bankBill2.getUserName());

					bankBill2.setUserId(user.getUserId().toString());
				}
				mapBillList.addAll(list);
			}
		}

		//保存发送至金蝶数据至本地库
		if(null != mapBillList && mapBillList.size() > 0){
			XmlExercise xmlExercise = new XmlExercise();
			Map data = new LinkedHashMap<>();
			data.put("companyid", 1);

			List<Map<String,Object>> dataList = new ArrayList<>();
			for(BankBill bankBill2 : mapBillList){
				//不允许traderId为null
				if(null == bankBill2.getTraderId() || "".equals(bankBill2.getTraderId()) || "0".equals(bankBill2.getTraderId())){
					result.setMessage("推送失败，不允许存在traderId为空的值！");
					result.setCode(-1);
					return result;
				}
				bankBill2.setUserIdNow(bankBill.getUserIdNow());
				Map data1 = new LinkedHashMap<>();
				Map data2 = new HashMap<>();
				data1.put("id", bankBill2.getBankBillId());
				data1.put("date", bankBill2.getTrandate());
				data1.put("bankTag",(bankBill2.getBankTag()==null)?1:bankBill2.getBankTag());
				data1.put("tranFlow", bankBill2.getTranFlow());
				data1.put("orderNo", bankBill2.getSaleorderNo());
				data1.put("saler", bankBill2.getUserName());
				data1.put("amount", bankBill2.getAmt());
				data1.put("traderName", bankBill2.getTraderName());
				data1.put("traderId", bankBill2.getTraderId());
				data1.put("remark", bankBill2.getComments());
				data1.put("realTrandatetime",bankBill2.getRealTrandatetime());
				data2.put("info", data1);
				dataList.add(data2);
			}
			data.put("list", dataList);
			String dataXmlStr = xmlExercise.mapToListXml(data, "data");
			String params = CryptBase64Tool.desEncrypt(dataXmlStr, kingdleeKey);
			try {
				String sendPost = HttpRequest.sendPost(kingdleeReceamountUrl, "msg="+URLEncoder.encode(params, "utf-8"));
				Map info = xmlExercise.xmlToMap(sendPost);
				if (null != info && info.get("code").equals("0")) {
					page.setTotalRecord(mapBillList.size());
					result = this.saveBankBillList(mapBillList);
					result.setPage(page);
				}
			} catch (Exception e) {
				result.setCode(-1);
				result.setMessage("推送失败，对方接口异常！");
				return result;
			}
		}
		return result;
	}

	private ResultInfo saveBankBillList(List<BankBill> mapBillList) {
		ResultInfo<?> result = null;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Map<String,Object>>> TypeRef = new TypeReference<ResultInfo<Map<String,Object>>>() {};
		String url=httpUrl + "finance/bankbill/savesendbankbilllistpage.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, mapBillList,clientId,clientKey, TypeRef);
			if(result != null){
				if(result.getCode() == 0 && result.getData() != null){
					return result;
				}
			}
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}

	//获取发送至金蝶银行流水
	private ResultInfo<?> sendBankBillListPage(BankBill bankBill, Page page) {
		String url = httpUrl + "report/finance/getsendbankbilllistpage.htm";
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<List<BankBill>>> TypeRef = new TypeReference<ResultInfo<List<BankBill>>>(){};
		try {
		    ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, bankBill, clientId, clientKey, TypeRef, page);
		    return result;
		} catch (IOException e) {
		    logger.error(Contant.ERROR_MSG, e);
		}
		return null;
	}

	@Override
	public Map<String, Object> getBankBillPayMatchListPage(BankBill bankBill, Page page) {
	    Map<String, Object> map = new HashMap<>();
		// 调用银行流水列表
		String url = httpUrl + "finance/bankbill/getbankbillpaymatchlistpage.htm";
		// 定义反序列化 数据格式
		List<BankBill> bankBillList = null;
		final TypeReference<ResultInfo<List<BankBill>>> TypeRef = new TypeReference<ResultInfo<List<BankBill>>>() {
		};
		final TypeReference<ResultInfo<Map<String, Object>>> TypeRefM = new TypeReference<ResultInfo<Map<String, Object>>>() {
		};
		try {
			    // 获取匹配过后的银行流水列表
			    ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, bankBill, clientId, clientKey, TypeRef, page);
			    if (result.getCode() == 0) {
				bankBillList = (List<BankBill>) result.getData();
				map.put("list", bankBillList);
				map.put("page", result.getPage());
				String urlM = httpUrl + "finance/bankbill/getbankbilllistinfo.htm";
				ResultInfo<?> resultM = (ResultInfo<?>) HttpClientUtils.post(urlM, bankBill, clientId, clientKey,
					TypeRefM, page);
				Map<String, Object> result_map = null;
				result_map = (Map<String, Object>) resultM.getData();
				BigDecimal getAmount = new BigDecimal(result_map.get("getAmount").toString());
				BigDecimal payAmount = new BigDecimal(result_map.get("payAmount").toString());
				BigDecimal orderAmount = new BigDecimal(result_map.get("orderAmount").toString());
				BigDecimal matchAmount = new BigDecimal(result_map.get("matchAmount").toString());
				Integer orderNum = new Integer(result_map.get("orderNum").toString());
				map.put("getAmount", getAmount);
				map.put("payAmount", payAmount);
				map.put("orderAmount", orderAmount);
				map.put("matchAmount", matchAmount);
				map.put("orderNum", orderNum);
		    }
		} catch (IOException e) {
		    logger.error(Contant.ERROR_MSG, e);
		    return null;
		}
		return map;
	}

	//发送付款记录至金蝶
	@SuppressWarnings("unchecked")
	@Override
	public ResultInfo sendPayBillToKindlee(BankBill bankBill, Page page, HttpSession session) throws UnsupportedEncodingException {
		SimpleDateFormat sdf = new SimpleDateFormat("yyMMdd");

		String bankName="";
		if(bankBill.getBankTag()==null){
			bankName="南京银行#+";
		}else if(bankBill.getBankTag()==1){
			bankName="建设银行#+";
		}
		else if(bankBill.getBankTag()==4){
			bankName="支付宝#+";
		}else if(bankBill.getBankTag()==5){
			bankName="微信#+";
		}
		ResultInfo result = new ResultInfo();
		List<BankBill> mapBankBillList = null;
		//获取发送至金蝶的付款记录
		ResultInfo<List<BankBill>> resultMap = (ResultInfo<List<BankBill>>) this.sendPayBilllistPage(bankBill, page);

		mapBankBillList = (List<BankBill>) resultMap.getData();
		if(null != mapBankBillList && mapBankBillList.size() > 0){
			for (BankBill bankBill2 : mapBankBillList) {

				String time ="";
				if(!StringUtils.isEmpty(bankBill2.getRealTrandatetime())){
					Date date = DateUtil.StringToDate(bankBill2.getRealTrandatetime(), "yyyy-MM-dd");
					time =sdf.format(date);
				}

				//备注
				bankBill2.setComments(bankName + bankBill2.getTranFlow() +  "#+" +time +"#+" + bankBill2.getBuyorderNo());
			}
		}

		Integer pageCount =  resultMap.getPage().getTotalPage();
		for (int i = 2; i <= pageCount; i++) {
			page.setPageNo(i);
			ResultInfo<List<BankBill>> listMap = (ResultInfo<List<BankBill>>) this.sendPayBilllistPage(bankBill, page);
			List<BankBill> list = (List<BankBill>) listMap.getData();
			if(null != list && list.size() > 0){
				for (BankBill bankBill2 : list) {
					String time ="";
					if(!StringUtils.isEmpty(bankBill2.getRealTrandatetime())){
						Date date = DateUtil.StringToDate(bankBill2.getRealTrandatetime(), "yyyy-MM-dd");
						time =sdf.format(date);
					}
					//备注
					bankBill2.setComments(bankName + bankBill2.getTranFlow() + "#+" +time +"#+" + bankBill2.getBuyorderNo());
				}
				mapBankBillList.addAll(list);
			}
		}

		//记录推送至金蝶的付款记录
		if(null != mapBankBillList && mapBankBillList.size() > 0){
			XmlExercise xmlExercise = new XmlExercise();
			Map data = new LinkedHashMap<>();
			data.put("companyid", 1);

			List<Map<String,Object>> dataList = new ArrayList<>();

			for(BankBill bankBill2 : mapBankBillList){
				//不允许traderId为null
				if(null == bankBill2.getTraderId() || "".equals(bankBill2.getTraderId()) || "0".equals(bankBill2.getTraderId())){
					result.setMessage("推送失败，不允许存在traderId为空的值！");
					result.setCode(-1);
					return result;
				}
				bankBill2.setUserIdNow(bankBill.getUserIdNow());
				Map data1 = new LinkedHashMap<>();
				Map data2 = new HashMap<>();
				if(null !=bankBill.getBankTag()&&bankBill.getBankTag().equals(4)){
					data1.put("bank", "1002.18");
					data1.put("bankName", "支付宝");
					data1.put("bankTag",4);
				}else if(null !=bankBill.getBankTag()&&bankBill.getBankTag().equals(5)){
					data1.put("bank", "1002.19");
					data1.put("bankName", "微信");
					data1.put("bankTag",5);
				}else{
					data1.put("bank", "1002.04");
					data1.put("bankName", "南京银行");
					data1.put("bankTag",2);
				}
				data1.put("id", bankBill2.getBankBillId());
				data1.put("date", bankBill2.getTrandate());
				data1.put("type", bankBill2.getFlag2());
				data1.put("tranFlow", bankBill2.getTranFlow());
				data1.put("orderNo", bankBill2.getBuyorderNo());
				data1.put("amount", bankBill2.getAmt());
				data1.put("traderName", bankBill2.getTraderName());
				data1.put("traderId", bankBill2.getTraderId());
				data1.put("remark", bankBill2.getComments());
				data1.put("realTrandatetime",bankBill2.getRealTrandatetime());
				data2.put("info", data1);
				dataList.add(data2);
			}

			data.put("list", dataList);
			String dataXmlStr = xmlExercise.mapToListXml(data, "data");

			String params = CryptBase64Tool.desEncrypt(dataXmlStr, kingdleeKey);

			try {
				String sendPost = HttpRequest.sendPost(kingdleePayamountUrl, "msg="+URLEncoder.encode(params, "utf-8"));
				Map info = xmlExercise.xmlToMap(sendPost);
				if (null != info && info.get("code").equals("0")) {
					page.setTotalRecord(mapBankBillList.size());
					result = this.savePayBillToKindlee(mapBankBillList);
					result.setPage(page);
				}
			} catch (Exception e) {
				result.setCode(-1);
				result.setMessage("推送失败，对方接口异常！");
				// add by Randy.Xu 2020/12/26 11:06 .Desc: . begin
				//打印日志
				logger.error("推送失败,错误原因：",e);
				// add by Randy.Xu 2020/12/26 11:06 .Desc: . end


				return result;
			}
		}
		return result;
	}


	private ResultInfo savePayBillToKindlee(List<BankBill> mapBankBillList) {
		ResultInfo<?> result = null;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Map<String, Object>>> TypeRef = new TypeReference<ResultInfo<Map<String, Object>>>() {};
		String url=httpUrl + "finance/bankbill/savepaybilltokindlee.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, mapBankBillList,clientId,clientKey, TypeRef);
			if(result != null){
				if(result.getCode() == 0 && result.getData() != null){
					return result;
				}
			}
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}

	private ResultInfo<?> sendPayBilllistPage(BankBill bankBill, Page page) {
		String url = httpUrl + "finance/bankbill/sendpaybilllistpage.htm";
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<List<BankBill>>> TypeRef = new TypeReference<ResultInfo<List<BankBill>>>(){};
		try {
		    ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, bankBill, clientId, clientKey, TypeRef, page);

		    return result;
		} catch (IOException e) {
		    logger.error(Contant.ERROR_MSG, e);
		}
		return new ResultInfo<>();
	}


	@SuppressWarnings("unchecked")
	@Override
	public ResultInfo<T> sendChPayBillToKindlee(BankBill bankBill, Page page, HttpSession session){
		SimpleDateFormat sdf = new SimpleDateFormat("yyMMdd");
		String bankName="中国银行#+";
		ResultInfo<T> result = new ResultInfo<T>();
		List<BankBill> mapBankBillList = null;
		//获取发送至金蝶的付款记录
		ResultInfo<List<BankBill>> resultMap = (ResultInfo<List<BankBill>>) this.sendChPayBilllistPage(bankBill, page);

		mapBankBillList = (List<BankBill>) resultMap.getData();
		if(null != mapBankBillList && mapBankBillList.size() > 0){
			for (BankBill bankBill2 : mapBankBillList) {
				String time ="";
				if(!StringUtils.isEmpty(bankBill2.getRealTrandatetime())){
					Date date = DateUtil.StringToDate(bankBill2.getRealTrandatetime(), "yyyy-MM-dd");
					time =sdf.format(date);
				}
				//备注
				bankBill2.setComments(bankName + bankBill2.getTranFlow() + "#+" +time + "#+" + bankBill2.getBuyorderNo());
			}
		}

		Integer pageCount =  resultMap.getPage().getTotalPage();
		for (int i = 2; i <= pageCount; i++) {
			page.setPageNo(i);
			@SuppressWarnings("unchecked")
			ResultInfo<List<BankBill>> listMap = (ResultInfo<List<BankBill>>) this.sendChPayBilllistPage(bankBill, page);
			List<BankBill> list = (List<BankBill>) listMap.getData();
			if(null != list && list.size() > 0){
				for (BankBill bankBill2 : list) {

					String time ="";
					if(!StringUtils.isEmpty(bankBill2.getRealTrandatetime())){
						Date date = DateUtil.StringToDate(bankBill2.getRealTrandatetime(), "yyyy-MM-dd");
						time =sdf.format(date);
					}
					//备注
					bankBill2.setComments(bankName + bankBill2.getTranFlow() + "#+" +time + "#+" + bankBill2.getBuyorderNo());
				}
				mapBankBillList.addAll(list);
			}
		}

		//记录推送至金蝶的付款记录
		if(null != mapBankBillList && mapBankBillList.size() > 0){
			XmlExercise xmlExercise = new XmlExercise();
			Map data = new LinkedHashMap<>();
			data.put("companyid", 1);

			List<Map<String,Object>> dataList = new ArrayList<>();

			for(BankBill bankBill2 : mapBankBillList){
				//不允许traderId为null
				if(null == bankBill2.getTraderId() || "".equals(bankBill2.getTraderId()) || "0".equals(bankBill2.getTraderId())){
					result.setMessage("推送失败，不允许存在traderId为空的值！");
					result.setCode(-1);
					return result;
				}
				bankBill2.setUserIdNow(bankBill.getUserIdNow());
				Map data1 = new LinkedHashMap<>();
				Map data2 = new HashMap<>();
				data1.put("bank", "1002.07");
				data1.put("bankName", "中国银行");
				data1.put("bankTag",3);
				data1.put("id", bankBill2.getBankBillId());
				data1.put("date", bankBill2.getTrandate());
				data1.put("type", bankBill2.getFlag2());
				data1.put("tranFlow", bankBill2.getTranFlow());
				data1.put("orderNo", bankBill2.getBuyorderNo());
				data1.put("amount", bankBill2.getAmt());
				data1.put("traderName", bankBill2.getTraderName());
				data1.put("traderId", bankBill2.getTraderId());
				data1.put("remark", bankBill2.getComments());
				data1.put("realTrandatetime",bankBill2.getRealTrandatetime());
				data2.put("info", data1);
				dataList.add(data2);
			}

			data.put("list", dataList);
			String dataXmlStr = xmlExercise.mapToListXml(data, "data");

			String params = CryptBase64Tool.desEncrypt(dataXmlStr, kingdleeKey);

			try {
				String sendPost = HttpRequest.sendPost(kingdleePayamountUrl, "msg="+URLEncoder.encode(params, "utf-8"));
				Map info = xmlExercise.xmlToMap(sendPost);
				if (null != info && info.get("code").equals("0")) {
					page.setTotalRecord(mapBankBillList.size());
					result = this.saveChPayBillToKindlee(mapBankBillList);
					result.setPage(page);
				}
			} catch (Exception e) {
				result.setCode(-1);
				result.setMessage("推送失败，对方接口异常！");
				return result;
			}
		}
		return result;
	}

	@Override
	public ResultInfo<T> updataBankBillMatchedAmount(Integer bankBillId, Integer capitalBillId) {

		CapitalBill capitalBill = capitalBillMapper.selectByPrimaryKey(capitalBillId);
		BigDecimal amount = capitalBill.getAmount();
		BigDecimal absAmount = amount.abs();
		BankBillExtDo bankBillExtDo =bankBillExtMapper.getBankBillById(bankBillId);
		BigDecimal newAmount = bankBillExtDo.getMatchedAmount().add(absAmount);
		if(newAmount.compareTo(bankBillExtDo.getAmt())>0){
			return new ResultInfo<>(-1,"匹配金额超过剩余结款金额");
		}
		bankBillExtMapper.updataBankBillMatchedAmount( bankBillId, newAmount);
    	return new ResultInfo<>(0,"修改匹配金额成功");
	}

	@Override
    @Transactional(rollbackFor = Exception.class)
	public ResultInfo<T> batchUpdateBankBillMatchedAmount(Integer bankBillId, List<Integer> capitalBillIdList) {
    	if(CollectionUtils.isNotEmpty(capitalBillIdList)){
			BigDecimal newAmount = BigDecimal.ZERO;
			for (Integer cpitalBillId : capitalBillIdList) {
				CapitalBill capitalBill = capitalBillMapper.selectByPrimaryKey(cpitalBillId);
				BigDecimal amount = capitalBill.getAmount();
				BigDecimal absAmount = amount.abs();
				newAmount= newAmount.add(absAmount);
			}
			BankBillExtDo bankBillExtDo =bankBillExtMapper.getBankBillById(bankBillId);
            newAmount=bankBillExtDo.getMatchedAmount().add(newAmount);
			if(newAmount.compareTo(bankBillExtDo.getAmt())>0){
                logger.info("匹配金额超过剩余结款金额,订单ID：{}",bankBillId);
				return new ResultInfo<>(-1,"匹配金额超过剩余结款金额");
			}
			bankBillExtMapper.updataBankBillMatchedAmount( bankBillId, newAmount);
		}else {
            logger.info("没有选择交易流水,订单ID：{}",bankBillId);
    		return new ResultInfo(-1,"没有选择交易流水");
		}
        capitalBillMapper.batchUpdateCapitalBill(bankBillId,capitalBillIdList);
    	return new ResultInfo<>(0,"匹配成功");
	}

    @Override
    public ResultInfo<List<BankNoDto>> getChinaBanKNo() {
        String url = httpUrl + "finance/bankbill/getChinaBankNo.htm";
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<BankBill>>> TypeRef = new TypeReference<ResultInfo<List<BankBill>>>() {
        };
        try {
            ResultInfo<List<BankNoDto>> result = (ResultInfo<List<BankNoDto>>) HttpClientUtils.post(url, null, clientId, clientKey, TypeRef);
            return result;
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
        }
        return new ResultInfo<>();

    }
	private ResultInfo<?> sendChPayBilllistPage(BankBill bankBill, Page page) {
		String url = httpUrl + "finance/bankbill/sendchpaybilllistpage.htm";
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<List<BankBill>>> TypeRef = new TypeReference<ResultInfo<List<BankBill>>>(){};
		try {
		    ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, bankBill, clientId, clientKey, TypeRef, page);

		    return result;
		} catch (IOException e) {
		    logger.error(Contant.ERROR_MSG, e);
		}
		return new ResultInfo<>();
	}

	private ResultInfo saveChPayBillToKindlee(List<BankBill> mapBankBillList) {
		ResultInfo<?> result = null;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Map<String, Object>>> TypeRef = new TypeReference<ResultInfo<Map<String, Object>>>() {};
		String url=httpUrl + "finance/bankbill/savechpaybilltokindlee.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, mapBankBillList,clientId,clientKey, TypeRef);
			if(result != null){
				if(result.getCode() == 0 && result.getData() != null){
					return result;
				}
			}
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}
	@Override
	public ResultInfo getBankFile(List<BankBillExtDo> bankBillList) {
		String url = httpUrl + "finance/bankbill/getBankFileList.htm";
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, bankBillList, clientId, clientKey,
					TypeRef2);
			return result;
		} catch (IOException e) {
			return null;
		}
	}


	@Override
	public List<BankBillExtDo> queryNeedDownLoadBankBill(Date beginTime, Date endTime, Integer bankTag) {
		return bankBillExtMapper.queryNeedDownLoadBankBill(beginTime, endTime, bankTag);
	}

	@Override
	public List<BankBillExtDo> queryBankBillOfNoReceiptUrl(String beginTime, String endTime, List<Integer> bankTagList,Integer flag1) {
		return bankBillExtMapper.queryBankBillOfNoReceiptUrl(beginTime, endTime, bankTagList,flag1);
	}

	@Override
	public void updataBankBillReceiptUrl(Integer bankBillId, String receiptUrl) {
		bankBillExtMapper.updataBankBillReceiptUrl(bankBillId,receiptUrl);
	}

	@Override
	public List<BankBillExtDo> queryBankBillByIdList(List<Integer> bankBillList) {
		return bankBillExtMapper.queryBankBillByIdList(bankBillList);
	}

	@Override
	public List<BankBillExtDo> findBankByTranFlow(String receiveRecordBankNum) {
		return bankBillExtMapper.findBankByTranFlow(receiveRecordBankNum);
	}

	@Override
	public List<BankBillExtDo> findAlipayByCapitalSearchFlow(String receiveRecordAlipayNum) {
		return bankBillExtMapper.findAlipayByCapitalSearchFlow(receiveRecordAlipayNum);
	}

	@Override
	public void downloadBankFile(List<BankBillExtDo> bankBillList, String cbcBankFileUrl) {
		logger.info("调用回单获取{},{},",cbcBankFileUrl,JSON.toJSONString(bankBillList));
		InputStream inputStream = null;
		String suffix = PDF;
		String fileName = "";
		for(BankBillExtDo bankBillExtDo : bankBillList){
			bankBillExtDo = bankBillExtMapper.getBankBillById(bankBillExtDo.getBankBillId());
			logger.info("下载回单流水信息{},", JSON.toJSONString(bankBillExtDo));
			fileName = bankBillExtDo.getReceiptName();
			if (StrUtil.isBlank(fileName)){
				logger.info("下载建行回单流水信息,流水号:{},未能从前置机获取回单名称receiptName,请安排手动执行,bankBillId:{}",bankBillExtDo.getTranFlow(),bankBillExtDo.getBankBillId());
				continue;
			}
			byte[] bytes = HttpUtil.downloadBytes(cbcBankFileUrl + bankBillExtDo.getReceiptName());
			inputStream = new ByteArrayInputStream(bytes);
			// 上传SSO
			String fileUrl = ossUtilsService.upload2OssForInputStream(suffix, fileName, inputStream);
			logger.info("上传oss返回url{},",fileUrl);
			bankBillExtMapper.updataReceiptUrl(bankBillExtDo.getBankBillId(),fileUrl);
		}
	}

	@Override
	@Transactional
	public Boolean saveIgnoreRecord(User user, BankBillIgnoreRecordVo ignoreRecordVo) {
		//T_BANK_BILL_IGNORE_RECORD新增记录
		BankBillIgnoreRecordDto addDto = new BankBillIgnoreRecordDto();
		addDto.setBankBillId(ignoreRecordVo.getBankBillId());
		addDto.setAccName(ignoreRecordVo.getName());
		addDto.setContactUnitType(KingdeeIgnoreUnitTypeEnums.getUnitType(ignoreRecordVo.getSelectedUnitTypeId()));
		//如果是银行，ContactUnitNo用erp维护的银行编码
		if (KingdeeIgnoreUnitTypeEnums.BANK.getUnitType().equals(addDto.getContactUnitType())){
			addDto.setContactUnitNo(payVedengBankApiService.getKingDeeBankCodeByBankNo(ignoreRecordVo.getSelectFnumber()));
		}else {
			addDto.setContactUnitNo(ignoreRecordVo.getSelectFnumber());
		}
		addDto.setContactUnit(ignoreRecordVo.getSelectFname().replace(ignoreRecordVo.getSelectFnumber()+"-",""));
		addDto.setTradeSubject(ignoreRecordVo.getTraderSubject());
		addDto.setIgnoreReason(ignoreRecordVo.getSelectedIgnoreReasonId());
		addDto.setIsDelete(ErpConst.ZERO);
		addDto.setAddTime(new Date());
		addDto.setModTime(new Date());
		addDto.setCreator(user.getUserId());
		addDto.setCreatorName(user.getUsername());
		addDto.setUpdater(user.getUserId());
		addDto.setUpdaterName(user.getUsername());
		bankBillIgnoreRecordApiService.insertSelective(addDto);
		//原业务'忽略'逻辑
		BankBill bankBill = new BankBill();
		bankBill.setBankBillId(ignoreRecordVo.getBankBillId());
		bankBill.setMatchedObject(ignoreRecordVo.getSelectedIgnoreReasonId());
		bankBill.setStatus(ErpConst.ONE);
		this.editBankBill(bankBill);
		return true;
	}

	@Override
	public List<KingDeeCustomerResultDto> getKingDeeUnitNameList(String name, Integer unitTypeId, Integer bankBillId) {
		List<KingDeeCustomerResultDto> resultList = new ArrayList<>();
		KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
		switch (unitTypeId){
			//客户
			case 1:
				queryParam.setFormId(KingDeeFormConstant.BD_CUSTOMER);
				break;
			//供应商
			case 2:
				queryParam.setFormId(KingDeeFormConstant.BD_SUPPLIER);
				break;
			//其他客户
			case 3:
				queryParam.setFormId(KingDeeFormConstant.FIN_OTHERS);
				break;
			//银行
			case 4:
				BankBillExtDo bankBillById = bankBillExtMapper.getBankBillById(bankBillId);
				KingDeeCustomerResultDto dto = new KingDeeCustomerResultDto();
				PayVedengBankDto payVedengBankDto = payVedengBankApiService.queryBankInfo(bankBillById.getBankTag());
				dto.setFName(payVedengBankDto.getPayBankNo());
				dto.setFNumber(payVedengBankDto.getPayBankNo());
				resultList.add(dto);
				return resultList;
		}

		List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
		queryFilterDtos.add(KingDeeQueryFilterDto.builder().fieldName("fName").compare("like").left("(").right(")").value(name).logic("AND").build());
		queryFilterDtos.add(KingDeeQueryFilterDto.builder().fieldName("FUseOrgId.FNUMBER").value(String.valueOf(KingDeeConstant.ORG_ID)).build());
		queryFilterDtos.add(KingDeeQueryFilterDto.builder().fieldName("FDocumentStatus").value("C").build());
		queryParam.setFilterString(queryFilterDtos);
		List<KingDeeCustomerResultDto> queryResult = kingDeeBaseApi.query(queryParam, KingDeeCustomerResultDto.class);
		queryResult.forEach(q -> q.setFName(q.getFNumber()+"-"+q.getFName()));
		return queryResult;
	}

	@Override
	public List<BankBillExtDo> queryCcbByTranDate(String beginDate, String endDate) {
		return bankBillExtMapper.queryCcbByTranDate(beginDate,endDate);
	}
}
