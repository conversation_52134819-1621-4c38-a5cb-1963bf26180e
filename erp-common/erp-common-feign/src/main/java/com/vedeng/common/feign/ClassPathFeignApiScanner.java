package com.vedeng.common.feign;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.common.feign.domain.FeignFactoryBean;
import com.vedeng.common.feign.exception.FeignException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.AnnotatedBeanDefinition;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.config.BeanDefinitionHolder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.GenericBeanDefinition;
import org.springframework.context.annotation.ClassPathBeanDefinitionScanner;
import org.springframework.core.annotation.AnnotationAttributes;
import org.springframework.core.type.AnnotationMetadata;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.util.Assert;

import java.lang.annotation.Annotation;
import java.util.Arrays;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: feign扫描器
 * @date 2022/8/12 14:09
 */
@Slf4j
public class ClassPathFeignApiScanner extends ClassPathBeanDefinitionScanner {


    private Class<? extends Annotation> annotationClass;


    private FeignFactoryBean feignFactoryBean = new FeignFactoryBean();


    public ClassPathFeignApiScanner(BeanDefinitionRegistry registry) {
        super(registry, false);
    }

    public void setAnnotationClass(Class<? extends Annotation> annotationClass) {
        this.annotationClass = annotationClass;
    }


    public void setFeignFactoryBean(FeignFactoryBean feignFactoryBean) {
        this.feignFactoryBean = (feignFactoryBean != null ? feignFactoryBean : new FeignFactoryBean());
    }

    public void registerFilters() {
        boolean acceptAllInterfaces = true;

        if (this.annotationClass != null) {
            addIncludeFilter(new AnnotationTypeFilter(this.annotationClass));
            acceptAllInterfaces = false;
        }


        if (acceptAllInterfaces) {
            addIncludeFilter((metadataReader, metadataReaderFactory) -> true);
        }

        addExcludeFilter((metadataReader, metadataReaderFactory) -> {
            String className = metadataReader.getClassMetadata().getClassName();
            return className.endsWith("package-info");
        });
    }


    @Override
    public Set<BeanDefinitionHolder> doScan(String... basePackages) {
        Assert.notEmpty(basePackages, "必须制定一个基础包路径");
        Set<BeanDefinitionHolder> beanDefinitions = super.doScan(basePackages);

        if (beanDefinitions.isEmpty()) {
            log.error("在 '" + Arrays.toString(basePackages) + "' 路径中未找到feignApi. 请检查你的 configuration.");
        } else {
            processBeanDefinitions(beanDefinitions);
        }

        return beanDefinitions;
    }

    private void processBeanDefinitions(Set<BeanDefinitionHolder> beanDefinitions) {
        GenericBeanDefinition definition;
        for (BeanDefinitionHolder holder : beanDefinitions) {
            definition = (GenericBeanDefinition) holder.getBeanDefinition();
            String realBeanClassName = definition.getBeanClassName();
            // definition.getConstructorArgumentValues()，BeanClass需要提供包含该属性的构造方法，否则会注入失败
            definition.getConstructorArgumentValues().addGenericArgumentValue(Objects.requireNonNull(definition.getBeanClassName()));
            // 获取注解的元注解对象
            AnnotationMetadata beanMetadata = ((AnnotatedBeanDefinition) definition).getMetadata();
            AnnotationAttributes attributes = AnnotationAttributes.fromMap(beanMetadata.getAnnotationAttributes(FeignApi.class.getName()));

            String serverUrl = StrUtil.isNotBlank(attributes.get("serverUrl").toString()) ? attributes.get("serverUrl").toString() : "";
            Object serverNameObj = attributes.get("serverName");
            if (ObjectUtil.isEmpty(serverNameObj)) {
                log.error("serverName不能为空:{}", realBeanClassName);
                throw new FeignException("serverName不能为空:" + realBeanClassName);
            }
            String serverName = serverNameObj.toString();
            boolean defaultFeign = Boolean.parseBoolean(attributes.get("defaultFeign").toString());
            int maxConnTotal = Integer.parseInt(attributes.get("maxConnTotal").toString());
            int timeout = Integer.parseInt(attributes.get("timeout").toString());
            int retry = Integer.parseInt(attributes.get("retry").toString());
            boolean keepAlive = Boolean.parseBoolean(attributes.get("keepAlive").toString());
            definition.getPropertyValues().addPropertyValue("serverUrl", serverUrl);
            definition.getPropertyValues().addPropertyValue("serverName", serverName);
            definition.getPropertyValues().addPropertyValue("defaultFeign", defaultFeign);
            definition.getPropertyValues().addPropertyValue("maxConnTotal", maxConnTotal);
            definition.getPropertyValues().addPropertyValue("timeout", timeout);
            definition.getPropertyValues().addPropertyValue("retry", retry);
            definition.getPropertyValues().addPropertyValue("keepAlive", keepAlive);
            definition.setBeanClass(this.feignFactoryBean.getClass());
        }
    }

    @Override
    protected boolean isCandidateComponent(AnnotatedBeanDefinition beanDefinition) {
        AnnotationMetadata beanMetadata = beanDefinition.getMetadata();
        AnnotationAttributes attributes = AnnotationAttributes.fromMap(beanMetadata.getAnnotationAttributes(FeignApi.class.getName()));
        return attributes != null && beanDefinition.getMetadata().isInterface() && beanDefinition.getMetadata().isIndependent();
    }

    @Override
    protected boolean checkCandidate(String beanName, BeanDefinition beanDefinition) {
        if (super.checkCandidate(beanName, beanDefinition)) {
            return true;
        } else {
            log.error("Skipping HystrixFeignFactoryBean with name '" + beanName
                    + "' and '" + beanDefinition.getBeanClassName() + "' mapperInterface"
                    + ". Bean already defined with the same name!");
            return false;
        }
    }

}