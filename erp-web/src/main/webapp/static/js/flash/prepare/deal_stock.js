$(function () {

});

function searchSku() {
    let skuNo = $('#replaceSkuNo').val();
    if (!skuNo.startsWith("V")) {
        skuNo = "V" + skuNo;
    }
    let param = {
        skuNo: skuNo
    }
    $.ajax({
        url: page_url + '/flash/prepare/searchSku.do',
        type: 'POST',
        dataType :'json',
        contentType: "application/json",
        async : false,
        data: JSON.stringify(param),
        success: function (data) {
            if(data.code == 0){
                let html = "<span>商品名称：" + data.data.goodsName + "</span>\n" +
                            "<span style='margin-left: 30px;'>库存数量：" + data.data.stockNum + "&nbsp" + data.data.unitName  + "</span>";
                $('.replace-sku').css("display", "block");
                $('.replace-sku').find('.f_left').empty();
                $('.replace-sku').find('.f_left').append(html);
            }else{
                let html = "<span style='color: red;'>" + data.message + "</span>"
                $('.replace-sku').css("display", "block");
                $('.replace-sku').find('.f_left').empty();
                $('.replace-sku').find('.f_left').append(html);
            }
        },error:function (data) {
            if(data.code==-1){
                layer.alert("查询商品失败！！！")
            }else if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }

        }
    })
}

function changeDealType(val) {
    console.log(val)
    if (1 === Number(val) || 2 === Number(val)) {
        $('#replace_sku').css("display", "block");
    } else {
        $('#replaceSkuNo').val('');
        $('#replace_sku').css("display", "none");
        $('.replace-sku').css("display", "none");
    }
}

function dealStock() {
    checkLogin();
    if (!checkForm()){
        return false;
    }
    $.ajax({
        url: page_url + '/flash/prepare/dealPrepareStock.do',
        type: 'POST',
        dataType :'json',
        async : false,
        data:$('#deal_safe_stock').serialize(),
        success: function (data) {
            if(data.code == 0){
                // layerPFF(window.parent.location.href);
                window.parent.location.reload();
                $("#close-layer").click();
            }else{
                layer.alert(data.message)
            }
        },error:function (data) {
            if(data.code==-1){
                layer.alert("处理暂不备货失败！！！")
            }else if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }

        }
    })
}

function checkForm() {
    $('.deal_type_error').css("display", "none");
    $('.replace_sku_error').css("display", "none");
    let dealType = $('#dealType').val();
    if (dealType === '-1') {
        $('.deal_type_error').css("display", "block");
        return false;
    }
    if (1 === Number(dealType) || 2 === Number(dealType)) {
        let replaceSkuNo = $('#replaceSkuNo').val();
        if (!replaceSkuNo) {
            $('.replace_sku_error').css("display", "block");
            return false;
        }
    }
    return true;
}


