package com.vedeng.erp.aftersale.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.rabbitmq.MsgProducer;
import com.rabbitmq.RabbitConfig;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.exception.BusinessException;
import com.vedeng.erp.aftersale.domain.dto.PushAfterSalesClientStatusDto;
import com.vedeng.erp.aftersale.domain.entity.AfterSalesBillToYxbEntity;
import com.vedeng.erp.aftersale.domain.entity.AfterSalesClientStatusRecord;
import com.vedeng.erp.aftersale.domain.entity.AfterSalesEntity;
import com.vedeng.erp.aftersale.enums.PushClientStatusEnum;
import com.vedeng.erp.aftersale.mapper.AfterSalesBillToYxbMapper;
import com.vedeng.erp.aftersale.mapper.AfterSalesBizMapper;
import com.vedeng.erp.aftersale.mapper.AfterSalesClientStatusRecordMapper;
import com.vedeng.erp.aftersale.service.AfterSalesBillToYxbApiService;
import com.vedeng.erp.aftersale.service.AfterSalesClientStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class AfterSalesClientStatusServiceImpl implements AfterSalesClientStatusService {

    @Autowired
    private AfterSalesClientStatusRecordMapper afterSalesClientStatusRecordMapper;
    @Autowired
    private AfterSalesBizMapper afterSalesBizMapper;
    @Autowired
    MsgProducer msgProducer;

    @Override
    public List<AfterSalesClientStatusRecord> getClientStatusRecordList(Integer afterSalesId) {
        return afterSalesClientStatusRecordMapper.findByAfterSalesId(afterSalesId);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void updateClientStatus(Integer afterSalesId, Integer oldClientStatus, Integer newClientStatus) {
        log.info("更新售后单的客户端状态,afterSalesId:{},oldClientStatus:{},newClientStatus:{}", afterSalesId, oldClientStatus, newClientStatus);
        AfterSalesEntity afterSalesEntity = afterSalesBizMapper.selectByPrimaryKey(afterSalesId);
        if (Objects.isNull(afterSalesEntity) || !afterSalesEntity.getClientStatus().equals(oldClientStatus) || afterSalesEntity.getClientStatus().equals(newClientStatus)) {
            log.error("更新售后单的客户端状态,状态请求错误,afterSalesId:{},oldClientStatus:{},newClientStatus:{}", afterSalesId, oldClientStatus, newClientStatus);
            throw new ServiceException("更新售后单的客户端状态,状态请求错误");
        }
        String content = PushClientStatusEnum.getContentByCode(newClientStatus);
        Integer foregroundCode = PushClientStatusEnum.getForegroundCodeByCode(newClientStatus);
        PushAfterSalesClientStatusDto pushDto = PushAfterSalesClientStatusDto.builder()
                .afterSaleOrderNo(afterSalesEntity.getAfterSalesNo())
                .flowType(foregroundCode)
                .flowComment(content)
                .build();
        try {
            log.info("更新售后单的客户端状态,发送消息至mq,推送信息:{}", JSONUtil.toJsonStr(pushDto));
            msgProducer.sendMsg(RabbitConfig.AFTER_SALES_CLIENT_STATUS_EXCHANGE, RabbitConfig.AFTER_SALES_CLIENT_STATUS_ROUTINGKEY, JSONUtil.toJsonStr(pushDto));
        } catch (Exception e) {
            log.error("更新售后单的客户端状态,发送消息至mq异常,推送信息:{}", JSONUtil.toJsonStr(pushDto), e);
            throw new ServiceException("更新售后单的客户端状态,发送消息至mq异常");
        }

        CurrentUser currentUser = CurrentUser.getCurrentUser();
        AfterSalesClientStatusRecord afterSalesClientStatusRecord = AfterSalesClientStatusRecord.builder()
                .afterSalesId(afterSalesId)
                .pushClientStatus(newClientStatus)
                .pushTime(new Date())
                .message(content)
                .creator(currentUser.getId())
                .creatorName(currentUser.getUsername())
                .updater(currentUser.getId())
                .updaterName(currentUser.getUsername())
                .build();
        afterSalesClientStatusRecordMapper.insertSelective(afterSalesClientStatusRecord);
        afterSalesBizMapper.updateClientStatusByAfterSalesId(newClientStatus, afterSalesId);
    }

}
