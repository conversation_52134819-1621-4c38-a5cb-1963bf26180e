$(function () {
    //选择框实例化
    // Select.use('.J-select');
    //上传组件初始化 上传营业执照  生产企业生产许可证  备案凭证 生产企业生产产品登记表
    timeout = null;
    var attachmentFunction = [1302,1303,1309,1304]; //营业执照（新） 生产企业生产许可证（新） 备案凭证(新) 生产企业生产产品登记表(即登记表附近)（新）
    var attachmentsName = ['yzBAttachments','scBAttachments','rcBAttachments' ,'djbBAttachments'];
    $('.J-upload').each(function (i) {
        var _this = this;
        new Upload({
            limit: 50,
            url: GLOBAL.IMGUPLOADURL,
            wrapper: $(this),
            uploadName: 'upload' + i,
            list: JSON.parse($(this).siblings('.J-upload-data').val() || '[]'),
            onchange: function () {
                $(_this).find('.J-upload-item').each(function (ii) {
                    var data = $(this).data('item');
                    $(this).find('.J-item-name').remove();
                    $(this).append('<input type="hidden" class="J-item-name" name="' + attachmentsName[i] + '[' + ii + '].attachmentFunction" value="' + attachmentFunction[i] + '">');
                    $(this).append('<input type="hidden" class="J-item-name" name="' + attachmentsName[i] + '[' + ii + '].uri" value="' + data.filePath +'">');
                    $(this).append('<input type="hidden" class="J-item-name" name="' + attachmentsName[i] + '[' + ii + '].domain" value="' + data.domain + '">');
                })
                if (i == 0 || i == 1) {
                    $(_this).find('[name^=upload]').valid();
                }
            },
            filters: {
                mime_types: [
                    { title: "Image files", extensions: "jpg,jpeg,png" }
                ],
                max_file_size: '5MB'
            },
            onError: function (error) {
                var errorMsg = {
                    TYPE: '上传图片格式为：JPG、PNG、JEPG格式',
                    SIZE: '图片大小不超过5M'
                }
                if (error) {
                    var $error = $(_this).siblings('.J-upload-error');
                    $error.show().find('label').html(errorMsg[error]).show();
                    timeout && clearTimeout(timeout);
                    timeout = setTimeout(function () {
                        $error.hide();
                    }, 3000)
                }
            }
        });
    })

    window.localStorage.removeItem('addsuccess');

    //增加关闭提示
    GLOBAL.addtip();
})

//点击取消
function cancel() {
    window.parent.location.reload();
    // var _self=self;
    // var dialog = artDialog.confirm('取消后，所填写的信息，系统将不做保存', '', {
    //     fn: function () {
    //         // $(window.parent.document).find('[role=presentation].active .glyphicon.small').click();
    //         window.opener.top.close()
    //         // window.parent.closableTab.close($(window.parent.document).find('[role=presentation].active .glyphicon.small'),window.parent.closableTab.resizeMove);
    //     }, text: '确定'
    // }, {
    //     fn: function () {
    //         dialog.close();
    //     }, text: '取消'
    // });
}


//点击保存
function subForm() {
    $.ajax({
        url: page_url + '/goods/manufacturer/upLoadOfficialB.do',
        type: 'POST',
        dataType :'json',
        async : false,
        data:$('#form_submit').serialize(),
        success: function (data) {
            if(data.code == 0){
                //成功
                layer.alert('保存成功',
                    function(index){
                        layer.closeAll();
                        window.parent.location.reload();
                    });
            }else{
                layer.alert(data.message,
                    function(index){
                        layer.closeAll();
                        window.parent.location.reload();
                    });
            }
        },error:function (data) {
            if(data.status==1001){
                layer.alert('保存失败,权限不足',
                    function(index){
                        layer.closeAll();
                        window.parent.location.reload();
                    });
            }

        }
    })
}
