package com.vedeng.finance.model.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class PreviewInvoiceVo {
    private Integer saleorderId;
    private Integer afterSalesId;
    private String comments;
    private List<GoodsInfo> goodsList;
    @Data
    public static class GoodsInfo {
        private String goodsName;
        private String oldGoodsName;
        private String spec;
        private String oldSpec;
        private String unitName;
        private String taxRate;
        private String taxAmount;
        private String lastPrice;
        private BigDecimal price;
        private Integer canInvoicedNum;
        private Integer maxCanApplyNum;
        private Integer applyNum;
        private Integer applyAmount;
        private String taxCategoryNo;
        private String taxCategoryName="";
        public GoodsInfo() {
        }
    }
}
