<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.wms.mapper.WmsWarehouseGoodsOperateLogMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.wms.domain.entity.WmsWarehouseGoodsOperateLogEntity">
    <!--@mbg.generated-->
    <!--@Table T_WAREHOUSE_GOODS_OPERATE_LOG-->
    <id column="WAREHOUSE_GOODS_OPERATE_LOG_ID" jdbcType="INTEGER" property="warehouseGoodsOperateLogId" />
    <result column="BARCODE_ID" jdbcType="INTEGER" property="barcodeId" />
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
    <result column="OPERATE_TYPE" jdbcType="TINYINT" property="operateType" />
    <result column="RELATED_ID" jdbcType="INTEGER" property="relatedId" />
    <result column="WAREHOUSE_PICKING_DETAIL_ID" jdbcType="INTEGER" property="warehousePickingDetailId" />
    <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId" />
    <result column="BARCODE_FACTORY" jdbcType="VARCHAR" property="barcodeFactory" />
    <result column="NUM" jdbcType="INTEGER" property="num" />
    <result column="WAREHOUSE_ID" jdbcType="INTEGER" property="warehouseId" />
    <result column="STORAGE_ROOM_ID" jdbcType="INTEGER" property="storageRoomId" />
    <result column="STORAGE_AREA_ID" jdbcType="INTEGER" property="storageAreaId" />
    <result column="STORAGE_LOCATION_ID" jdbcType="INTEGER" property="storageLocationId" />
    <result column="STORAGE_RACK_ID" jdbcType="INTEGER" property="storageRackId" />
    <result column="BATCH_NUMBER" jdbcType="VARCHAR" property="batchNumber" />
    <result column="EXPIRATION_DATE" jdbcType="BIGINT" property="expirationDate" />
    <result column="CHECK_STATUS" jdbcType="INTEGER" property="checkStatus" />
    <result column="CHECK_STATUS_USER" jdbcType="INTEGER" property="checkStatusUser" />
    <result column="CHECK_STATUS_TIME" jdbcType="BIGINT" property="checkStatusTime" />
    <result column="RECHECK_STATUS" jdbcType="INTEGER" property="recheckStatus" />
    <result column="RECHECK_STATUS_USER" jdbcType="INTEGER" property="recheckStatusUser" />
    <result column="RECHECK_STATUS_TIME" jdbcType="BIGINT" property="recheckStatusTime" />
    <result column="COMMENTS" jdbcType="VARCHAR" property="comments" />
    <result column="IS_ENABLE" jdbcType="INTEGER" property="isEnable" />
    <result column="IS_EXPRESS" jdbcType="INTEGER" property="isExpress" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="IS_PROBLEM" jdbcType="TINYINT" property="isProblem" />
    <result column="PROBLEM_REMARK" jdbcType="VARCHAR" property="problemRemark" />
    <result column="PRODUCT_DATE" jdbcType="BIGINT" property="productDate" />
    <result column="COST_PRICE" jdbcType="DECIMAL" property="costPrice" />
    <result column="IS_USE" jdbcType="INTEGER" property="isUse" />
    <result column="LOGICAL_WAREHOUSE_ID" jdbcType="INTEGER" property="logicalWarehouseId" />
    <result column="VEDENG_BATCH_NUMER" jdbcType="VARCHAR" property="vedengBatchNumer" />
    <result column="LAST_STOCK_NUM" jdbcType="INTEGER" property="lastStockNum" />
    <result column="STERILZATION_BATCH_NUMBER" jdbcType="VARCHAR" property="sterilzationBatchNumber" />
    <result column="LOG_TYPE" jdbcType="INTEGER" property="logType" />
    <result column="NEW_COST_PRICE" jdbcType="DECIMAL" property="newCostPrice" />
    <result column="TAG_SOURCES" jdbcType="VARCHAR" property="tagSources" />
    <result column="DEDICATED_BUYORDER_NO" jdbcType="VARCHAR" property="dedicatedBuyorderNo" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    WAREHOUSE_GOODS_OPERATE_LOG_ID, BARCODE_ID, COMPANY_ID, OPERATE_TYPE, RELATED_ID, 
    WAREHOUSE_PICKING_DETAIL_ID, GOODS_ID, BARCODE_FACTORY, NUM, WAREHOUSE_ID, STORAGE_ROOM_ID, 
    STORAGE_AREA_ID, STORAGE_LOCATION_ID, STORAGE_RACK_ID, BATCH_NUMBER, EXPIRATION_DATE, 
    CHECK_STATUS, CHECK_STATUS_USER, CHECK_STATUS_TIME, RECHECK_STATUS, RECHECK_STATUS_USER, 
    RECHECK_STATUS_TIME, COMMENTS, IS_ENABLE, IS_EXPRESS, ADD_TIME, CREATOR, MOD_TIME, 
    UPDATER, IS_PROBLEM, PROBLEM_REMARK, PRODUCT_DATE, COST_PRICE, IS_USE, LOGICAL_WAREHOUSE_ID, 
    VEDENG_BATCH_NUMER, LAST_STOCK_NUM, STERILZATION_BATCH_NUMBER, LOG_TYPE, NEW_COST_PRICE, 
    TAG_SOURCES, DEDICATED_BUYORDER_NO
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_WAREHOUSE_GOODS_OPERATE_LOG
    where WAREHOUSE_GOODS_OPERATE_LOG_ID = #{warehouseGoodsOperateLogId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_WAREHOUSE_GOODS_OPERATE_LOG
    where WAREHOUSE_GOODS_OPERATE_LOG_ID = #{warehouseGoodsOperateLogId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="WAREHOUSE_GOODS_OPERATE_LOG_ID" keyProperty="warehouseGoodsOperateLogId" parameterType="com.vedeng.erp.wms.domain.entity.WmsWarehouseGoodsOperateLogEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_WAREHOUSE_GOODS_OPERATE_LOG (BARCODE_ID, COMPANY_ID, OPERATE_TYPE, 
      RELATED_ID, WAREHOUSE_PICKING_DETAIL_ID, GOODS_ID, 
      BARCODE_FACTORY, NUM, WAREHOUSE_ID, 
      STORAGE_ROOM_ID, STORAGE_AREA_ID, STORAGE_LOCATION_ID, 
      STORAGE_RACK_ID, BATCH_NUMBER, EXPIRATION_DATE, 
      CHECK_STATUS, CHECK_STATUS_USER, CHECK_STATUS_TIME, 
      RECHECK_STATUS, RECHECK_STATUS_USER, RECHECK_STATUS_TIME, 
      COMMENTS, IS_ENABLE, IS_EXPRESS, 
      ADD_TIME, CREATOR, MOD_TIME, 
      UPDATER, IS_PROBLEM, PROBLEM_REMARK, 
      PRODUCT_DATE, COST_PRICE, IS_USE, 
      LOGICAL_WAREHOUSE_ID, VEDENG_BATCH_NUMER, LAST_STOCK_NUM, 
      STERILZATION_BATCH_NUMBER, LOG_TYPE, NEW_COST_PRICE, 
      TAG_SOURCES, DEDICATED_BUYORDER_NO)
    values (#{barcodeId,jdbcType=INTEGER}, #{companyId,jdbcType=INTEGER}, #{operateType,jdbcType=TINYINT}, 
      #{relatedId,jdbcType=INTEGER}, #{warehousePickingDetailId,jdbcType=INTEGER}, #{goodsId,jdbcType=INTEGER}, 
      #{barcodeFactory,jdbcType=VARCHAR}, #{num,jdbcType=INTEGER}, #{warehouseId,jdbcType=INTEGER}, 
      #{storageRoomId,jdbcType=INTEGER}, #{storageAreaId,jdbcType=INTEGER}, #{storageLocationId,jdbcType=INTEGER}, 
      #{storageRackId,jdbcType=INTEGER}, #{batchNumber,jdbcType=VARCHAR}, #{expirationDate,jdbcType=BIGINT}, 
      #{checkStatus,jdbcType=INTEGER}, #{checkStatusUser,jdbcType=INTEGER}, #{checkStatusTime,jdbcType=BIGINT}, 
      #{recheckStatus,jdbcType=INTEGER}, #{recheckStatusUser,jdbcType=INTEGER}, #{recheckStatusTime,jdbcType=BIGINT}, 
      #{comments,jdbcType=VARCHAR}, #{isEnable,jdbcType=INTEGER}, #{isExpress,jdbcType=INTEGER}, 
      #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, 
      #{updater,jdbcType=INTEGER}, #{isProblem,jdbcType=TINYINT}, #{problemRemark,jdbcType=VARCHAR}, 
      #{productDate,jdbcType=BIGINT}, #{costPrice,jdbcType=DECIMAL}, #{isUse,jdbcType=INTEGER}, 
      #{logicalWarehouseId,jdbcType=INTEGER}, #{vedengBatchNumer,jdbcType=VARCHAR}, #{lastStockNum,jdbcType=INTEGER}, 
      #{sterilzationBatchNumber,jdbcType=VARCHAR}, #{logType,jdbcType=INTEGER}, #{newCostPrice,jdbcType=DECIMAL}, 
      #{tagSources,jdbcType=VARCHAR}, #{dedicatedBuyorderNo,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="WAREHOUSE_GOODS_OPERATE_LOG_ID" keyProperty="warehouseGoodsOperateLogId" parameterType="com.vedeng.erp.wms.domain.entity.WmsWarehouseGoodsOperateLogEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_WAREHOUSE_GOODS_OPERATE_LOG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="barcodeId != null">
        BARCODE_ID,
      </if>
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="operateType != null">
        OPERATE_TYPE,
      </if>
      <if test="relatedId != null">
        RELATED_ID,
      </if>
      <if test="warehousePickingDetailId != null">
        WAREHOUSE_PICKING_DETAIL_ID,
      </if>
      <if test="goodsId != null">
        GOODS_ID,
      </if>
      <if test="barcodeFactory != null and barcodeFactory != ''">
        BARCODE_FACTORY,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="warehouseId != null">
        WAREHOUSE_ID,
      </if>
      <if test="storageRoomId != null">
        STORAGE_ROOM_ID,
      </if>
      <if test="storageAreaId != null">
        STORAGE_AREA_ID,
      </if>
      <if test="storageLocationId != null">
        STORAGE_LOCATION_ID,
      </if>
      <if test="storageRackId != null">
        STORAGE_RACK_ID,
      </if>
      <if test="batchNumber != null and batchNumber != ''">
        BATCH_NUMBER,
      </if>
      <if test="expirationDate != null">
        EXPIRATION_DATE,
      </if>
      <if test="checkStatus != null">
        CHECK_STATUS,
      </if>
      <if test="checkStatusUser != null">
        CHECK_STATUS_USER,
      </if>
      <if test="checkStatusTime != null">
        CHECK_STATUS_TIME,
      </if>
      <if test="recheckStatus != null">
        RECHECK_STATUS,
      </if>
      <if test="recheckStatusUser != null">
        RECHECK_STATUS_USER,
      </if>
      <if test="recheckStatusTime != null">
        RECHECK_STATUS_TIME,
      </if>
      <if test="comments != null and comments != ''">
        COMMENTS,
      </if>
      <if test="isEnable != null">
        IS_ENABLE,
      </if>
      <if test="isExpress != null">
        IS_EXPRESS,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="isProblem != null">
        IS_PROBLEM,
      </if>
      <if test="problemRemark != null and problemRemark != ''">
        PROBLEM_REMARK,
      </if>
      <if test="productDate != null">
        PRODUCT_DATE,
      </if>
      <if test="costPrice != null">
        COST_PRICE,
      </if>
      <if test="isUse != null">
        IS_USE,
      </if>
      <if test="logicalWarehouseId != null">
        LOGICAL_WAREHOUSE_ID,
      </if>
      <if test="vedengBatchNumer != null and vedengBatchNumer != ''">
        VEDENG_BATCH_NUMER,
      </if>
      <if test="lastStockNum != null">
        LAST_STOCK_NUM,
      </if>
      <if test="sterilzationBatchNumber != null and sterilzationBatchNumber != ''">
        STERILZATION_BATCH_NUMBER,
      </if>
      <if test="logType != null">
        LOG_TYPE,
      </if>
      <if test="newCostPrice != null">
        NEW_COST_PRICE,
      </if>
      <if test="tagSources != null and tagSources != ''">
        TAG_SOURCES,
      </if>
      <if test="dedicatedBuyorderNo != null and dedicatedBuyorderNo != ''">
        DEDICATED_BUYORDER_NO,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="barcodeId != null">
        #{barcodeId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=TINYINT},
      </if>
      <if test="relatedId != null">
        #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="warehousePickingDetailId != null">
        #{warehousePickingDetailId,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null">
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="barcodeFactory != null and barcodeFactory != ''">
        #{barcodeFactory,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="warehouseId != null">
        #{warehouseId,jdbcType=INTEGER},
      </if>
      <if test="storageRoomId != null">
        #{storageRoomId,jdbcType=INTEGER},
      </if>
      <if test="storageAreaId != null">
        #{storageAreaId,jdbcType=INTEGER},
      </if>
      <if test="storageLocationId != null">
        #{storageLocationId,jdbcType=INTEGER},
      </if>
      <if test="storageRackId != null">
        #{storageRackId,jdbcType=INTEGER},
      </if>
      <if test="batchNumber != null and batchNumber != ''">
        #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="expirationDate != null">
        #{expirationDate,jdbcType=BIGINT},
      </if>
      <if test="checkStatus != null">
        #{checkStatus,jdbcType=INTEGER},
      </if>
      <if test="checkStatusUser != null">
        #{checkStatusUser,jdbcType=INTEGER},
      </if>
      <if test="checkStatusTime != null">
        #{checkStatusTime,jdbcType=BIGINT},
      </if>
      <if test="recheckStatus != null">
        #{recheckStatus,jdbcType=INTEGER},
      </if>
      <if test="recheckStatusUser != null">
        #{recheckStatusUser,jdbcType=INTEGER},
      </if>
      <if test="recheckStatusTime != null">
        #{recheckStatusTime,jdbcType=BIGINT},
      </if>
      <if test="comments != null and comments != ''">
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null">
        #{isEnable,jdbcType=INTEGER},
      </if>
      <if test="isExpress != null">
        #{isExpress,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="isProblem != null">
        #{isProblem,jdbcType=TINYINT},
      </if>
      <if test="problemRemark != null and problemRemark != ''">
        #{problemRemark,jdbcType=VARCHAR},
      </if>
      <if test="productDate != null">
        #{productDate,jdbcType=BIGINT},
      </if>
      <if test="costPrice != null">
        #{costPrice,jdbcType=DECIMAL},
      </if>
      <if test="isUse != null">
        #{isUse,jdbcType=INTEGER},
      </if>
      <if test="logicalWarehouseId != null">
        #{logicalWarehouseId,jdbcType=INTEGER},
      </if>
      <if test="vedengBatchNumer != null and vedengBatchNumer != ''">
        #{vedengBatchNumer,jdbcType=VARCHAR},
      </if>
      <if test="lastStockNum != null">
        #{lastStockNum,jdbcType=INTEGER},
      </if>
      <if test="sterilzationBatchNumber != null and sterilzationBatchNumber != ''">
        #{sterilzationBatchNumber,jdbcType=VARCHAR},
      </if>
      <if test="logType != null">
        #{logType,jdbcType=INTEGER},
      </if>
      <if test="newCostPrice != null">
        #{newCostPrice,jdbcType=DECIMAL},
      </if>
      <if test="tagSources != null and tagSources != ''">
        #{tagSources,jdbcType=VARCHAR},
      </if>
      <if test="dedicatedBuyorderNo != null and dedicatedBuyorderNo != ''">
        #{dedicatedBuyorderNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.wms.domain.entity.WmsWarehouseGoodsOperateLogEntity">
    <!--@mbg.generated-->
    update T_WAREHOUSE_GOODS_OPERATE_LOG
    <set>
      <if test="barcodeId != null">
        BARCODE_ID = #{barcodeId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="operateType != null">
        OPERATE_TYPE = #{operateType,jdbcType=TINYINT},
      </if>
      <if test="relatedId != null">
        RELATED_ID = #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="warehousePickingDetailId != null">
        WAREHOUSE_PICKING_DETAIL_ID = #{warehousePickingDetailId,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null">
        GOODS_ID = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="barcodeFactory != null and barcodeFactory != ''">
        BARCODE_FACTORY = #{barcodeFactory,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="warehouseId != null">
        WAREHOUSE_ID = #{warehouseId,jdbcType=INTEGER},
      </if>
      <if test="storageRoomId != null">
        STORAGE_ROOM_ID = #{storageRoomId,jdbcType=INTEGER},
      </if>
      <if test="storageAreaId != null">
        STORAGE_AREA_ID = #{storageAreaId,jdbcType=INTEGER},
      </if>
      <if test="storageLocationId != null">
        STORAGE_LOCATION_ID = #{storageLocationId,jdbcType=INTEGER},
      </if>
      <if test="storageRackId != null">
        STORAGE_RACK_ID = #{storageRackId,jdbcType=INTEGER},
      </if>
      <if test="batchNumber != null and batchNumber != ''">
        BATCH_NUMBER = #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="expirationDate != null">
        EXPIRATION_DATE = #{expirationDate,jdbcType=BIGINT},
      </if>
      <if test="checkStatus != null">
        CHECK_STATUS = #{checkStatus,jdbcType=INTEGER},
      </if>
      <if test="checkStatusUser != null">
        CHECK_STATUS_USER = #{checkStatusUser,jdbcType=INTEGER},
      </if>
      <if test="checkStatusTime != null">
        CHECK_STATUS_TIME = #{checkStatusTime,jdbcType=BIGINT},
      </if>
      <if test="recheckStatus != null">
        RECHECK_STATUS = #{recheckStatus,jdbcType=INTEGER},
      </if>
      <if test="recheckStatusUser != null">
        RECHECK_STATUS_USER = #{recheckStatusUser,jdbcType=INTEGER},
      </if>
      <if test="recheckStatusTime != null">
        RECHECK_STATUS_TIME = #{recheckStatusTime,jdbcType=BIGINT},
      </if>
      <if test="comments != null and comments != ''">
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null">
        IS_ENABLE = #{isEnable,jdbcType=INTEGER},
      </if>
      <if test="isExpress != null">
        IS_EXPRESS = #{isExpress,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="isProblem != null">
        IS_PROBLEM = #{isProblem,jdbcType=TINYINT},
      </if>
      <if test="problemRemark != null and problemRemark != ''">
        PROBLEM_REMARK = #{problemRemark,jdbcType=VARCHAR},
      </if>
      <if test="productDate != null">
        PRODUCT_DATE = #{productDate,jdbcType=BIGINT},
      </if>
      <if test="costPrice != null">
        COST_PRICE = #{costPrice,jdbcType=DECIMAL},
      </if>
      <if test="isUse != null">
        IS_USE = #{isUse,jdbcType=INTEGER},
      </if>
      <if test="logicalWarehouseId != null">
        LOGICAL_WAREHOUSE_ID = #{logicalWarehouseId,jdbcType=INTEGER},
      </if>
      <if test="vedengBatchNumer != null and vedengBatchNumer != ''">
        VEDENG_BATCH_NUMER = #{vedengBatchNumer,jdbcType=VARCHAR},
      </if>
      <if test="lastStockNum != null">
        LAST_STOCK_NUM = #{lastStockNum,jdbcType=INTEGER},
      </if>
      <if test="sterilzationBatchNumber != null and sterilzationBatchNumber != ''">
        STERILZATION_BATCH_NUMBER = #{sterilzationBatchNumber,jdbcType=VARCHAR},
      </if>
      <if test="logType != null">
        LOG_TYPE = #{logType,jdbcType=INTEGER},
      </if>
      <if test="newCostPrice != null">
        NEW_COST_PRICE = #{newCostPrice,jdbcType=DECIMAL},
      </if>
      <if test="tagSources != null and tagSources != ''">
        TAG_SOURCES = #{tagSources,jdbcType=VARCHAR},
      </if>
      <if test="dedicatedBuyorderNo != null and dedicatedBuyorderNo != ''">
        DEDICATED_BUYORDER_NO = #{dedicatedBuyorderNo,jdbcType=VARCHAR},
      </if>
    </set>
    where WAREHOUSE_GOODS_OPERATE_LOG_ID = #{warehouseGoodsOperateLogId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.wms.domain.entity.WmsWarehouseGoodsOperateLogEntity">
    <!--@mbg.generated-->
    update T_WAREHOUSE_GOODS_OPERATE_LOG
    set BARCODE_ID = #{barcodeId,jdbcType=INTEGER},
      COMPANY_ID = #{companyId,jdbcType=INTEGER},
      OPERATE_TYPE = #{operateType,jdbcType=TINYINT},
      RELATED_ID = #{relatedId,jdbcType=INTEGER},
      WAREHOUSE_PICKING_DETAIL_ID = #{warehousePickingDetailId,jdbcType=INTEGER},
      GOODS_ID = #{goodsId,jdbcType=INTEGER},
      BARCODE_FACTORY = #{barcodeFactory,jdbcType=VARCHAR},
      NUM = #{num,jdbcType=INTEGER},
      WAREHOUSE_ID = #{warehouseId,jdbcType=INTEGER},
      STORAGE_ROOM_ID = #{storageRoomId,jdbcType=INTEGER},
      STORAGE_AREA_ID = #{storageAreaId,jdbcType=INTEGER},
      STORAGE_LOCATION_ID = #{storageLocationId,jdbcType=INTEGER},
      STORAGE_RACK_ID = #{storageRackId,jdbcType=INTEGER},
      BATCH_NUMBER = #{batchNumber,jdbcType=VARCHAR},
      EXPIRATION_DATE = #{expirationDate,jdbcType=BIGINT},
      CHECK_STATUS = #{checkStatus,jdbcType=INTEGER},
      CHECK_STATUS_USER = #{checkStatusUser,jdbcType=INTEGER},
      CHECK_STATUS_TIME = #{checkStatusTime,jdbcType=BIGINT},
      RECHECK_STATUS = #{recheckStatus,jdbcType=INTEGER},
      RECHECK_STATUS_USER = #{recheckStatusUser,jdbcType=INTEGER},
      RECHECK_STATUS_TIME = #{recheckStatusTime,jdbcType=BIGINT},
      COMMENTS = #{comments,jdbcType=VARCHAR},
      IS_ENABLE = #{isEnable,jdbcType=INTEGER},
      IS_EXPRESS = #{isExpress,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      IS_PROBLEM = #{isProblem,jdbcType=TINYINT},
      PROBLEM_REMARK = #{problemRemark,jdbcType=VARCHAR},
      PRODUCT_DATE = #{productDate,jdbcType=BIGINT},
      COST_PRICE = #{costPrice,jdbcType=DECIMAL},
      IS_USE = #{isUse,jdbcType=INTEGER},
      LOGICAL_WAREHOUSE_ID = #{logicalWarehouseId,jdbcType=INTEGER},
      VEDENG_BATCH_NUMER = #{vedengBatchNumer,jdbcType=VARCHAR},
      LAST_STOCK_NUM = #{lastStockNum,jdbcType=INTEGER},
      STERILZATION_BATCH_NUMBER = #{sterilzationBatchNumber,jdbcType=VARCHAR},
      LOG_TYPE = #{logType,jdbcType=INTEGER},
      NEW_COST_PRICE = #{newCostPrice,jdbcType=DECIMAL},
      TAG_SOURCES = #{tagSources,jdbcType=VARCHAR},
      DEDICATED_BUYORDER_NO = #{dedicatedBuyorderNo,jdbcType=VARCHAR}
    where WAREHOUSE_GOODS_OPERATE_LOG_ID = #{warehouseGoodsOperateLogId,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_WAREHOUSE_GOODS_OPERATE_LOG
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="BARCODE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.barcodeId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="COMPANY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.companyId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="OPERATE_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.operateType,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="RELATED_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.relatedId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="WAREHOUSE_PICKING_DETAIL_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.warehousePickingDetailId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="GOODS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.goodsId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="BARCODE_FACTORY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.barcodeFactory,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.num,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="WAREHOUSE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.warehouseId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="STORAGE_ROOM_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.storageRoomId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="STORAGE_AREA_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.storageAreaId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="STORAGE_LOCATION_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.storageLocationId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="STORAGE_RACK_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.storageRackId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="BATCH_NUMBER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.batchNumber,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="EXPIRATION_DATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.expirationDate,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="CHECK_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.checkStatus,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CHECK_STATUS_USER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.checkStatusUser,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CHECK_STATUS_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.checkStatusTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="RECHECK_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.recheckStatus,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="RECHECK_STATUS_USER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.recheckStatusUser,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="RECHECK_STATUS_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.recheckStatusTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.comments,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="IS_ENABLE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.isEnable,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="IS_EXPRESS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.isExpress,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.addTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.modTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="IS_PROBLEM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.isProblem,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="PROBLEM_REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.problemRemark,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="PRODUCT_DATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.productDate,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="COST_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.costPrice,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="IS_USE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.isUse,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="LOGICAL_WAREHOUSE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.logicalWarehouseId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="VEDENG_BATCH_NUMER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.vedengBatchNumer,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="LAST_STOCK_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.lastStockNum,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="STERILZATION_BATCH_NUMBER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.sterilzationBatchNumber,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="LOG_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.logType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="NEW_COST_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.newCostPrice,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="TAG_SOURCES = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.tagSources,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="DEDICATED_BUYORDER_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OPERATE_LOG_ID = #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER} then #{item.dedicatedBuyorderNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where WAREHOUSE_GOODS_OPERATE_LOG_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.warehouseGoodsOperateLogId,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="WAREHOUSE_GOODS_OPERATE_LOG_ID" keyProperty="warehouseGoodsOperateLogId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_WAREHOUSE_GOODS_OPERATE_LOG
    (BARCODE_ID, COMPANY_ID, OPERATE_TYPE, RELATED_ID, WAREHOUSE_PICKING_DETAIL_ID, GOODS_ID, 
      BARCODE_FACTORY, NUM, WAREHOUSE_ID, STORAGE_ROOM_ID, STORAGE_AREA_ID, STORAGE_LOCATION_ID, 
      STORAGE_RACK_ID, BATCH_NUMBER, EXPIRATION_DATE, CHECK_STATUS, CHECK_STATUS_USER, 
      CHECK_STATUS_TIME, RECHECK_STATUS, RECHECK_STATUS_USER, RECHECK_STATUS_TIME, COMMENTS, 
      IS_ENABLE, IS_EXPRESS, ADD_TIME, CREATOR, MOD_TIME, UPDATER, IS_PROBLEM, PROBLEM_REMARK, 
      PRODUCT_DATE, COST_PRICE, IS_USE, LOGICAL_WAREHOUSE_ID, VEDENG_BATCH_NUMER, LAST_STOCK_NUM, 
      STERILZATION_BATCH_NUMBER, LOG_TYPE, NEW_COST_PRICE, TAG_SOURCES, DEDICATED_BUYORDER_NO
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.barcodeId,jdbcType=INTEGER}, #{item.companyId,jdbcType=INTEGER}, #{item.operateType,jdbcType=TINYINT}, 
        #{item.relatedId,jdbcType=INTEGER}, #{item.warehousePickingDetailId,jdbcType=INTEGER}, 
        #{item.goodsId,jdbcType=INTEGER}, #{item.barcodeFactory,jdbcType=VARCHAR}, #{item.num,jdbcType=INTEGER}, 
        #{item.warehouseId,jdbcType=INTEGER}, #{item.storageRoomId,jdbcType=INTEGER}, #{item.storageAreaId,jdbcType=INTEGER}, 
        #{item.storageLocationId,jdbcType=INTEGER}, #{item.storageRackId,jdbcType=INTEGER}, 
        #{item.batchNumber,jdbcType=VARCHAR}, #{item.expirationDate,jdbcType=BIGINT}, #{item.checkStatus,jdbcType=INTEGER}, 
        #{item.checkStatusUser,jdbcType=INTEGER}, #{item.checkStatusTime,jdbcType=BIGINT}, 
        #{item.recheckStatus,jdbcType=INTEGER}, #{item.recheckStatusUser,jdbcType=INTEGER}, 
        #{item.recheckStatusTime,jdbcType=BIGINT}, #{item.comments,jdbcType=VARCHAR}, #{item.isEnable,jdbcType=INTEGER}, 
        #{item.isExpress,jdbcType=INTEGER}, #{item.addTime,jdbcType=BIGINT}, #{item.creator,jdbcType=INTEGER}, 
        #{item.modTime,jdbcType=BIGINT}, #{item.updater,jdbcType=INTEGER}, #{item.isProblem,jdbcType=TINYINT}, 
        #{item.problemRemark,jdbcType=VARCHAR}, #{item.productDate,jdbcType=BIGINT}, #{item.costPrice,jdbcType=DECIMAL}, 
        #{item.isUse,jdbcType=INTEGER}, #{item.logicalWarehouseId,jdbcType=INTEGER}, #{item.vedengBatchNumer,jdbcType=VARCHAR}, 
        #{item.lastStockNum,jdbcType=INTEGER}, #{item.sterilzationBatchNumber,jdbcType=VARCHAR}, 
        #{item.logType,jdbcType=INTEGER}, #{item.newCostPrice,jdbcType=DECIMAL}, #{item.tagSources,jdbcType=VARCHAR}, 
        #{item.dedicatedBuyorderNo,jdbcType=VARCHAR})
    </foreach>
  </insert>


<!--auto generated by MybatisCodeHelper on 2023-12-05-->
  <select id="findByAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_WAREHOUSE_GOODS_OPERATE_LOG
    <where>
      <if test="warehouseGoodsOperateLogId != null">
        and WAREHOUSE_GOODS_OPERATE_LOG_ID=#{warehouseGoodsOperateLogId,jdbcType=INTEGER}
      </if>
      <if test="barcodeId != null">
        and BARCODE_ID=#{barcodeId,jdbcType=INTEGER}
      </if>
      <if test="companyId != null">
        and COMPANY_ID=#{companyId,jdbcType=INTEGER}
      </if>
      <if test="operateType != null">
        and OPERATE_TYPE=#{operateType,jdbcType=TINYINT}
      </if>
      <if test="relatedId != null">
        and RELATED_ID=#{relatedId,jdbcType=INTEGER}
      </if>
      <if test="warehousePickingDetailId != null">
        and WAREHOUSE_PICKING_DETAIL_ID=#{warehousePickingDetailId,jdbcType=INTEGER}
      </if>
      <if test="goodsId != null">
        and GOODS_ID=#{goodsId,jdbcType=INTEGER}
      </if>
      <if test="barcodeFactory != null and barcodeFactory != ''">
        and BARCODE_FACTORY=#{barcodeFactory,jdbcType=VARCHAR}
      </if>
      <if test="num != null">
        and NUM=#{num,jdbcType=INTEGER}
      </if>
      <if test="warehouseId != null">
        and WAREHOUSE_ID=#{warehouseId,jdbcType=INTEGER}
      </if>
      <if test="storageRoomId != null">
        and STORAGE_ROOM_ID=#{storageRoomId,jdbcType=INTEGER}
      </if>
      <if test="storageAreaId != null">
        and STORAGE_AREA_ID=#{storageAreaId,jdbcType=INTEGER}
      </if>
      <if test="storageLocationId != null">
        and STORAGE_LOCATION_ID=#{storageLocationId,jdbcType=INTEGER}
      </if>
      <if test="storageRackId != null">
        and STORAGE_RACK_ID=#{storageRackId,jdbcType=INTEGER}
      </if>
      <if test="batchNumber != null and batchNumber != ''">
        and BATCH_NUMBER=#{batchNumber,jdbcType=VARCHAR}
      </if>
      <if test="expirationDate != null">
        and EXPIRATION_DATE=#{expirationDate,jdbcType=BIGINT}
      </if>
      <if test="checkStatus != null">
        and CHECK_STATUS=#{checkStatus,jdbcType=INTEGER}
      </if>
      <if test="checkStatusUser != null">
        and CHECK_STATUS_USER=#{checkStatusUser,jdbcType=INTEGER}
      </if>
      <if test="checkStatusTime != null">
        and CHECK_STATUS_TIME=#{checkStatusTime,jdbcType=BIGINT}
      </if>
      <if test="recheckStatus != null">
        and RECHECK_STATUS=#{recheckStatus,jdbcType=INTEGER}
      </if>
      <if test="recheckStatusUser != null">
        and RECHECK_STATUS_USER=#{recheckStatusUser,jdbcType=INTEGER}
      </if>
      <if test="recheckStatusTime != null">
        and RECHECK_STATUS_TIME=#{recheckStatusTime,jdbcType=BIGINT}
      </if>
      <if test="comments != null and comments != ''">
        and COMMENTS=#{comments,jdbcType=VARCHAR}
      </if>
      <if test="isEnable != null">
        and IS_ENABLE=#{isEnable,jdbcType=INTEGER}
      </if>
      <if test="isExpress != null">
        and IS_EXPRESS=#{isExpress,jdbcType=INTEGER}
      </if>
      <if test="addTime != null">
        and ADD_TIME=#{addTime,jdbcType=BIGINT}
      </if>
      <if test="creator != null">
        and CREATOR=#{creator,jdbcType=INTEGER}
      </if>
      <if test="modTime != null">
        and MOD_TIME=#{modTime,jdbcType=BIGINT}
      </if>
      <if test="updater != null">
        and UPDATER=#{updater,jdbcType=INTEGER}
      </if>
      <if test="isProblem != null">
        and IS_PROBLEM=#{isProblem,jdbcType=TINYINT}
      </if>
      <if test="problemRemark != null and problemRemark != ''">
        and PROBLEM_REMARK=#{problemRemark,jdbcType=VARCHAR}
      </if>
      <if test="productDate != null">
        and PRODUCT_DATE=#{productDate,jdbcType=BIGINT}
      </if>
      <if test="costPrice != null">
        and COST_PRICE=#{costPrice,jdbcType=DECIMAL}
      </if>
      <if test="isUse != null">
        and IS_USE=#{isUse,jdbcType=INTEGER}
      </if>
      <if test="logicalWarehouseId != null">
        and LOGICAL_WAREHOUSE_ID=#{logicalWarehouseId,jdbcType=INTEGER}
      </if>
      <if test="vedengBatchNumer != null and vedengBatchNumer != ''">
        and VEDENG_BATCH_NUMER=#{vedengBatchNumer,jdbcType=VARCHAR}
      </if>
      <if test="lastStockNum != null">
        and LAST_STOCK_NUM=#{lastStockNum,jdbcType=INTEGER}
      </if>
      <if test="sterilzationBatchNumber != null and sterilzationBatchNumber != ''">
        and STERILZATION_BATCH_NUMBER=#{sterilzationBatchNumber,jdbcType=VARCHAR}
      </if>
      <if test="logType != null">
        and LOG_TYPE=#{logType,jdbcType=INTEGER}
      </if>
      <if test="newCostPrice != null">
        and NEW_COST_PRICE=#{newCostPrice,jdbcType=DECIMAL}
      </if>
      <if test="tagSources != null and tagSources != ''">
        and TAG_SOURCES=#{tagSources,jdbcType=VARCHAR}
      </if>
      <if test="dedicatedBuyorderNo != null and dedicatedBuyorderNo != ''">
        and DEDICATED_BUYORDER_NO=#{dedicatedBuyorderNo,jdbcType=VARCHAR}
      </if>
    </where>
  </select>
</mapper>