package com.wms.service.chain.step;

import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.wms.constant.LogicalEnum;
import com.wms.constant.WmsLogicalOperateTypeEnum;
import com.wms.dao.WmsLogicalOrdergoodsMapper;
import com.wms.dto.PutSaleOrderGoodsDto;
import com.wms.model.po.WmsLogicalOrdergoods;
import com.wms.service.chain.AbstractHandlerStep;
import com.wms.service.chain.HandlerStepContext;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 采购退货出库 逻辑单取消处理
 */
@Service
public class PurchaseReturnOutLogicOrderReverseHandler extends AbstractHandlerStep {

    private static final Logger LOGGER = LoggerFactory.getLogger(PurchaseReturnOutLogicOrderReverseHandler.class);

    @Resource
    private WmsLogicalOrdergoodsMapper wmsLogicalOrdergoodsMapper;

    @Override
    protected void doDealWith(HandlerStepContext handlerStepContext) throws Exception {

        List<WmsLogicalOrdergoods> wmsLogicalOrdergoods = handlerStepContext.get("logicalOrdergoodList");

        if(CollectionUtils.isEmpty(wmsLogicalOrdergoods)){
            return;
        }

        //直接删除
        wmsLogicalOrdergoods.stream()
                            .forEach(wmsLogicalOrdergood -> {

                                LOGGER.info("采购退货出库审核通过，然后关闭，删除逻辑订单数据========================:" + JSON.toJSONString(wmsLogicalOrdergood));

                                WmsLogicalOrdergoods update = new WmsLogicalOrdergoods();
                                update.setLogicalOrderGoodsId(wmsLogicalOrdergood.getLogicalOrderGoodsId());
                                update.setIsDelete(1);
                                update.setOccupyNum(0);
                                wmsLogicalOrdergoodsMapper.updateByPrimaryKeySelective(update);
                            });

    }
}
