package com.vedeng.erp.kingdee.domain.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
    * 快递成本表
    */
@Data
public class ExpressCostEntity implements Serializable {
    /**
    * 主键ID（单据编号）
    */
    private Integer expressCostId;

    /**
    * 唯一键（贝登单据编号）
    */
    private String uniqueKey;

    /**
    * 快递详情ID
    */
    private Integer expressDetailId;

    /**
    * 快递主键ID
    */
    private Integer expressId;

    /**
    * 销售单号（销售单号）
    */
    private String saleorderNo;

    /**
    * 出库单号（出入库单号）
    */
    private String outInNo;

    /**
    * 承运商
    */
    private String logistics;

    /**
    * 收件地址（收件人地址）
    */
    private String takeTraderAddress;

    /**
    * 收件人（收件人）
    */
    private String takeTraderContactName;

    /**
    * 收件人电话（收件人电话）
    */
    private String takeTraderContactTelephone;

    /**
    * 物流单号（快递单号）
    */
    private String logisticsNo;

    /**
    * 数据类型（ 0实物普发，1实物直发，2纯虚拟商品销售订单，3混虚拟商品销售订单）
    */
    private Integer dataType;

    /**
    * 订货号（物料编码）
    */
    private String sku;

    /**
    * 批次号（批次号）
    */
    private String batchNumber;

    /**
    * SN码（物料序列号）
    */
    private String barcodeFactory;

    /**
    * 分摊数量（发货数量）
    */
    private Integer shareNum;

    /**
    * 公允价
    */
    private BigDecimal fairPrice;

    /**
    * 销售商品ID
    */
    private Integer saleorderGoodsId;

    /**
    * 是否赠品 0否1是
    */
    private Integer isGift;

    /**
    * 快递费总额
    */
    private BigDecimal totalExpressAmount;

    /**
    * 分摊快递费
    */
    private BigDecimal shareExpressAmount;

    /**
    * 分摊快递费(不含税)（快递成本）
    */
    private BigDecimal shareExpressAmountNoTax;

    /**
    * 成本计算标记（1新增，2更新，4删除）
    */
    private Integer costActFlag;

    /**
    * 计算时间
    */
    private String etlTime;

    /**
    * 推送状态（0未推送，1已推送）
    */
    private Integer pushStatus;

    /**
    * 是否删除 0否 1是
    */
    private Integer isDelete;

    /**
    * 创建时间
    */
    private Date addTime;

    /**
    * 修改时间
    */
    private Date modTime;

    /**
    * 创建者ID
    */
    private Integer creator;

    /**
    * 修改者ID
    */
    private Integer updater;

    /**
    * 创建者名称
    */
    private String creatorName;

    /**
    * 修改者名称
    */
    private String updaterName;

    /**
    * 更新备注
    */
    private String updateRemark;

    private static final long serialVersionUID = 1L;
}