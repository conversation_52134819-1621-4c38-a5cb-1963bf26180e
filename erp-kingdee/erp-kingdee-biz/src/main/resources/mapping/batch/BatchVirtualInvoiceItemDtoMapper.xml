<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchVirtualInvoiceItemDtoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchVirtualInvoiceItemDto">
    <!--@mbg.generated-->
    <!--@Table T_VIRTUAL_INVOICE_ITEM-->
    <id column="VIRTUAL_INVOICE_ITEM_ID" jdbcType="INTEGER" property="virtualInvoiceItemId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="VIRTUAL_INVOICE_ID" jdbcType="INTEGER" property="virtualInvoiceId" />
    <result column="SOURCE_INVOICE_ITEM_ID" jdbcType="INTEGER" property="sourceInvoiceItemId" />
    <result column="BUSINESS_ORDER_ITEM_ID" jdbcType="INTEGER" property="businessOrderItemId" />
    <result column="PRICE" jdbcType="DECIMAL" property="price" />
    <result column="NUM" jdbcType="DECIMAL" property="num" />
    <result column="TOTAL_AMOUNT" jdbcType="DECIMAL" property="totalAmount" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    VIRTUAL_INVOICE_ITEM_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, 
    VIRTUAL_INVOICE_ID, SOURCE_INVOICE_ITEM_ID, BUSINESS_ORDER_ITEM_ID, PRICE, NUM, TOTAL_AMOUNT
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_VIRTUAL_INVOICE_ITEM
    where VIRTUAL_INVOICE_ITEM_ID = #{virtualInvoiceItemId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_VIRTUAL_INVOICE_ITEM
    where VIRTUAL_INVOICE_ITEM_ID = #{virtualInvoiceItemId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="VIRTUAL_INVOICE_ITEM_ID" keyProperty="virtualInvoiceItemId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchVirtualInvoiceItemDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_VIRTUAL_INVOICE_ITEM (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      VIRTUAL_INVOICE_ID, SOURCE_INVOICE_ITEM_ID, 
      BUSINESS_ORDER_ITEM_ID, PRICE, NUM, 
      TOTAL_AMOUNT)
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{virtualInvoiceId,jdbcType=INTEGER}, #{sourceInvoiceItemId,jdbcType=INTEGER}, 
      #{businessOrderItemId,jdbcType=INTEGER}, #{price,jdbcType=DECIMAL}, #{num,jdbcType=DECIMAL}, 
      #{totalAmount,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" keyColumn="VIRTUAL_INVOICE_ITEM_ID" keyProperty="virtualInvoiceItemId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchVirtualInvoiceItemDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_VIRTUAL_INVOICE_ITEM
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="virtualInvoiceId != null">
        VIRTUAL_INVOICE_ID,
      </if>
      <if test="sourceInvoiceItemId != null">
        SOURCE_INVOICE_ITEM_ID,
      </if>
      <if test="businessOrderItemId != null">
        BUSINESS_ORDER_ITEM_ID,
      </if>
      <if test="price != null">
        PRICE,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="totalAmount != null">
        TOTAL_AMOUNT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="virtualInvoiceId != null">
        #{virtualInvoiceId,jdbcType=INTEGER},
      </if>
      <if test="sourceInvoiceItemId != null">
        #{sourceInvoiceItemId,jdbcType=INTEGER},
      </if>
      <if test="businessOrderItemId != null">
        #{businessOrderItemId,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="num != null">
        #{num,jdbcType=DECIMAL},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchVirtualInvoiceItemDto">
    <!--@mbg.generated-->
    update T_VIRTUAL_INVOICE_ITEM
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="virtualInvoiceId != null">
        VIRTUAL_INVOICE_ID = #{virtualInvoiceId,jdbcType=INTEGER},
      </if>
      <if test="sourceInvoiceItemId != null">
        SOURCE_INVOICE_ITEM_ID = #{sourceInvoiceItemId,jdbcType=INTEGER},
      </if>
      <if test="businessOrderItemId != null">
        BUSINESS_ORDER_ITEM_ID = #{businessOrderItemId,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        PRICE = #{price,jdbcType=DECIMAL},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=DECIMAL},
      </if>
      <if test="totalAmount != null">
        TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
      </if>
    </set>
    where VIRTUAL_INVOICE_ITEM_ID = #{virtualInvoiceItemId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchVirtualInvoiceItemDto">
    <!--@mbg.generated-->
    update T_VIRTUAL_INVOICE_ITEM
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      VIRTUAL_INVOICE_ID = #{virtualInvoiceId,jdbcType=INTEGER},
      SOURCE_INVOICE_ITEM_ID = #{sourceInvoiceItemId,jdbcType=INTEGER},
      BUSINESS_ORDER_ITEM_ID = #{businessOrderItemId,jdbcType=INTEGER},
      PRICE = #{price,jdbcType=DECIMAL},
      NUM = #{num,jdbcType=DECIMAL},
      TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL}
    where VIRTUAL_INVOICE_ITEM_ID = #{virtualInvoiceItemId,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2023-11-30-->
  <select id="selectByBusinessOrderItemIds" resultMap="BaseResultMap">
        select
    VIRTUAL_INVOICE_ITEM_ID,
    TVII.VIRTUAL_INVOICE_ID, SOURCE_INVOICE_ITEM_ID, BUSINESS_ORDER_ITEM_ID, PRICE, NUM, TOTAL_AMOUNT
        from T_VIRTUAL_INVOICE_ITEM TVII left join T_VIRTUAL_INVOICE TVI  on TVII.VIRTUAL_INVOICE_ID = TVI.VIRTUAL_INVOICE_ID
        where TVII.BUSINESS_ORDER_ITEM_ID in
    <foreach collection="list" open="(" close=")" separator="," item="item" >
      #{item,jdbcType=INTEGER}
    </foreach>
    and TVI.COLOR_TYPE = #{colorType,jdbcType=INTEGER}
    </select>

<!--auto generated by MybatisCodeHelper on 2023-12-01-->
  <select id="selectBySourceInvoiceItemId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_VIRTUAL_INVOICE_ITEM
    where SOURCE_INVOICE_ITEM_ID=#{sourceInvoiceItemId,jdbcType=INTEGER}
  </select>

  <select id="selectBySourceInvoiceItemIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_VIRTUAL_INVOICE_ITEM
    where SOURCE_INVOICE_ITEM_ID in
    <foreach collection="list" item="item" separator="," close=")" open="(">
      #{item,jdbcType=INTEGER}
    </foreach>
  </select>

  <select id="selectBindableInvoiceItem" resultMap="BaseResultMap">
    select
    TVII.VIRTUAL_INVOICE_ITEM_ID,
    TVII.VIRTUAL_INVOICE_ID, TVII.SOURCE_INVOICE_ITEM_ID, TVII.BUSINESS_ORDER_ITEM_ID, TVII.PRICE,  TVII.TOTAL_AMOUNT,
    ABS(TVII.NUM)-IFNULL(SUM(ABS(TRVIJW.NUM)),0) canBindNum,ABS(TVII.NUM)-IFNULL(ABS(TRVIJW.NUM),0) NUM
    from T_VIRTUAL_INVOICE_ITEM TVII left join T_R_VIRTUAL_INVOICE_J_WAREHOUSE TRVIJW on TVII.VIRTUAL_INVOICE_ITEM_ID = TRVIJW.VIRTUAL_INVOICE_ITEM_ID
    where TVII.VIRTUAL_INVOICE_ID =#{virtualInvoiceId,jdbcType=INTEGER}
    group by TVII.VIRTUAL_INVOICE_ITEM_ID having canBindNum>0
  </select>

<!--auto generated by MybatisCodeHelper on 2023-12-02-->
  <select id="selectByVirtualInvoiceId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_VIRTUAL_INVOICE_ITEM
    where VIRTUAL_INVOICE_ID=#{virtualInvoiceId,jdbcType=INTEGER}
  </select>

<!--auto generated by MybatisCodeHelper on 2023-12-06-->
  <select id="selectByVirtualInvoiceItemIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_VIRTUAL_INVOICE_ITEM
        where VIRTUAL_INVOICE_ITEM_ID in
    <foreach collection="list" open="(" close=")" separator="," item="item" >
      #{item,jdbcType=INTEGER}
    </foreach>
    </select>

<!--auto generated by MybatisCodeHelper on 2023-12-12-->
  <delete id="deleteByVirtualInvoiceId">
    delete from T_VIRTUAL_INVOICE_ITEM
    where VIRTUAL_INVOICE_ID=#{virtualInvoiceId,jdbcType=INTEGER}
  </delete>
</mapper>