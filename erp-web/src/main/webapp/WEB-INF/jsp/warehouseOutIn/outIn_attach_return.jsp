<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="送货单回传" scope="application" />
<%@ include file="../common/common.jsp"%>
<script type="text/javascript" src="<%= basePath %>static/js/jquery/ajaxfileupload.js?rnd=${resourceVersionKey}"></script>
<script>
    function deliveryOrderReturnSubmit (num) {
        checkLogin();
        var file_name = $("#file_name_" + num).val();
        if (file_name == '') {
            layer.alert("请先上传入库附件",{ icon: 2 });
            return;
        }

        $.ajax({
            url:'./outInAttachReturnSave.do',
            data:$('#outIn_attach_return').serialize(),
            type:"POST",
            dataType : "json",
            async: false,
            success:function(data)
            {
                if(data.code==0){
                    window.parent.location.reload();
                }else{
                    layer.alert(data.message);
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });

    }

    function uploadFile(obj,num){
        checkLogin();
        var imgPath = $(obj).val();
        if(imgPath == '' || imgPath == undefined){
            return false;
        }
        //判断上传文件的后缀名
        var strExtension = imgPath.substr(imgPath.lastIndexOf('.') + 1);
        if (strExtension != 'jpg' && strExtension != 'gif' && strExtension != 'png' && strExtension != 'bmp'&& strExtension != 'pdf' && strExtension != 'jpeg') {
            layer.alert("上传文件格式错误");
            return;
        }
        if($(obj)[0].files[0].size > 5*1024*1024){//字节
            layer.alert("上传文件格式错误",{ icon: 2 });
            return;
        }
        $("#img_icon_" + num).attr("class", "iconloading mt5").show();
        $("#img_view_" + num).hide();
        $("#img_del_" + num).hide();
        $.ajaxFileUpload({
            url : page_url + '/order/saleorder/deliveryOrderReturnUpload.do', //用于文件上传的服务器端请求地址
            secureuri : false, //一般设置为false
            fileElementId : $(obj).attr("id"), //文件上传控件的id属性  <input type="file" id="file" name="file" /> 注意，这里一定要有name值   //$("form").serialize(),表单序列化。指把所有元素的ID，NAME 等全部发过去
            dataType : 'json',//返回值类型 一般设置为json
            complete : function() {//只要完成即执行，最后执行
            },
            //服务器成功响应处理函数
            success : function(data) {
                if (data.code == 0) {
                    $("#uri_" + num).val(data.filePath);
                    $("#domain_" + num).val(data.httpUrl);
                    $("#file_name_" + num).val(data.fileName);
                    $("#img_view_" + num).attr("href", 'http://' + data.httpUrl + data.filePath).show();
                    $("#img_icon_" + num).attr("class", "iconsuccesss mt5").show();
                    $("#img_del_" + num).show();
                    //layer.msg('上传成功');
                } else {
                    layer.alert(data.message);
                }
            },
            //服务器响应失败处理函数
            error : function(data, status, e) {
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }else{

                    layer.alert(data.responseText);
                }
            }
        })
    }

    function delProductImg(num) {
        checkLogin();
        index = layer.confirm("您是否确认该操作？", {
            btn: ['确定','取消'] //按钮
        }, function(){
            $("#img_icon_" + num).hide();
            $("#img_view_" + num).hide();
            $("#img_del_" + num).hide();
            $("#uri_" + num).val('');
            $("#domain_" + num).val('');
            $("#file_name_" + num).val('');
            layer.close(index);
        }, function(){
        });
    }

</script>
	 <div class="addElement">
        <div class="add-main adddepart">
        <form id="outIn_attach_return" method="post" enctype="multipart/form-data">
            <div class="overflow-hidden">
                <div class="infor_name ml47 mb4">
					入库附件
                </div>
                <div class='f_left'>
                	<div class='pos_rel f_left'>
	                    <input type="file" class="uploadErp" name="lwfile" id="lwfile" onchange="uploadFile(this, 4212)">
	                    <input type="text" class="input-middle" id="uri_4212" placeholder="" name="uri">
	                    <label class="bt-bg-style bt-middle bg-light-blue ml4" type="file">浏览</label>
	                    <input type="hidden" id="domain_4212" name="domain" value="">
	                    <input type="hidden" id="file_name_4212" name="name" value="">
                    </div>
                    
                    <div class="f_left">
	                    <i class="iconsuccesss mt5 none" id="img_icon_4212"></i>
	              		<a href="" target="_blank" class="font-blue cursor-pointer mr5 ml10 mt4 none" id="img_view_4212">查看</a>
	                   	<span class="font-red cursor-pointer mt4 none" onclick="delProductImg(4212)" id="img_del_4212">删除</span>
                    </div>
                    <div class='clear'></div>
                    
                	     <div class="font-grey9  mb10 mt7">
							友情提示：<br/>
							1、只允许上传JPG等图片格式文件<br>
							2、文件不得大于5M；
			            </div>
                </div>
            </div>
       
            <div class="add-tijiao tcenter">
            	<input type="hidden" value="${warehouseGoodsOutInId}" name="warehouseGoodsOutInId" id="warehouseGoodsOutInId">
                <button type="button" class="bt-bg-style bg-deep-green" id="outIn_attach_return_submit" onclick="deliveryOrderReturnSubmit(4212)">提交</button>
                <button class="dele" type="button" id="close-layer">取消</button>
            </div>
        </form>
    </div>
      </div>
<%@ include file="../common/footer.jsp"%>