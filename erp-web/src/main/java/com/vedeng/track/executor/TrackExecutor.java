package com.vedeng.track.executor;

import java.util.concurrent.ThreadPoolExecutor;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * 自定义线程池
 * @ClassName:  TrackExecutor
 * @author: <PERSON>.yang
 * @date:   2024年6月3日 下午3:07:14
 * @Copyright:
 */
//@EnableAsync
//@Configuration
@Deprecated
public class TrackExecutor {
	
	private static final int SIXTY = 60;

	private static final int HUNDRED = 100;

	/**核心线程数*/
	@Value("${track.core.pool.size:2}")
	private Integer corePoolSize;
	
	/**核心线程数*/
	@Value("${track.max.pool.size:4}")
	private Integer maxPoolSize;

	@Bean
    public TaskExecutor trackThreadPoolExecutor(){
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(HUNDRED);
        executor.setKeepAliveSeconds(SIXTY);
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setThreadNamePrefix("track-thread-");
        //被线程拒绝策略：由调用者线程执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
}
