package com.vedeng.erp.finance.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * 蓝字发票信息返回值dto
 */
@Data
public class SaleInvoiceBlueInfoResponseDto extends TaxesReturnInfo {

    private static final long serialVersionUID = -4855237883483350703L;

    // 1. 销售方税号
    private String xsfnsrsbh;

    // 2. 销售方名称
    private String xsfmc;

    // 3. 销售方开户行
    private String xsfkhh;

    // 4. 销售方银行账号
    private String xsfyhzh;

    // 5. 销售方地址
    private String xsfdz;

    // 6. 销售方联系电话
    private String xsflxdh;

    // 7. 购买方税号
    private String gmfnsrsbh;

    // 8. 购买方名称
    private String gmfmc;

    // 9. 购买方开户行 (可选)
    private String gmfkhh;

    // 10. 购买方银行账号 (可选)
    private String gmfyhzh;

    // 11. 购买方地址 (可选)
    private String gmfdz;

    // 12. 购买方联系电话 (可选)
    private String gmflxdh;

    // 13. 发票号码
    private String fphm;

    // 14. 开票日期
    private String kprq;

    // 15. 合计金额
    private String hjje;

    // 16. 合计税额
    private String hjse;

    // 17. 价税合计大写
    private String jshjchn;

    // 18. 扣除额 (可选)
    private String kce;

    // 19. 开票人
    private String kpr;

    // 20. 收款人 (可选)
    private String skr;

    // 21. 复核人 (可选)
    private String fhr;

    // 22. 备注 (可选)
    private String bz;

    // 23. 二维码
    private String ewm;

    // 24. 明细列表
    private List<Mxzb> mxzbList;

    @Data
    public static class Mxzb {
        // 25. 序号
        private String xh;

        // 26. 简称
        private String spfwjc;

        // 27. 项目名称
        private String xmmc;

        // 28. 名称
        private String hwhyslwfwmc;

        // 29. 税收分类编码
        private String sphfwssflhbbm;

        // 30. 单价
        private String dj;

        // 31. 单位 (可选)
        private String dw;

        // 32. 规格型号 (可选)
        private String ggxh;

        // 33. 金额
        private String je;

        // 34. 税额
        private String se;

        // 35. 税率
        private String slv;

        // 36. 数量
        private String spsl;
    }
}
