package com.vedeng.flash.service.message;

import com.vedeng.authorization.model.User;
import com.vedeng.flash.dto.SendMessageDto;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.model.BuyorderGoods;
import com.vedeng.order.model.vo.BuyorderVo;
import com.vedeng.system.service.UserService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
public class ExpeditionTaskOverHalfdayMessageSender extends AbstractMessageSender {

    @Resource
    BuyorderMapper buyorderMapper;

    @Resource
    BuyorderGoodsMapper buyorderGoodsMapper;

    @Resource
    UserService userService;

    @Override
    protected String getUrl(SendMessageDto sendMessageDto) {
        String url = "";
        Integer buyorderGoodsId = sendMessageDto.getEarlyWarningTask().getRelateBusinessId();
        BuyorderGoods buyorderGoods = buyorderGoodsMapper.selectByPrimaryKey(sendMessageDto.getEarlyWarningTask().getRelateBusinessId());
        BuyorderVo buyorderVoById = buyorderMapper.getBuyorderVoById(buyorderGoods.getBuyorderId());
        if(buyorderVoById.getValidStatus() != null && buyorderVoById.getValidStatus() == 1) {
            url = "/order/buyorder/viewBuyordersh.do?buyorderId=" + buyorderVoById.getBuyorderId();
        }else{
            url = "/order/buyorder/viewBuyorder.do?buyorderId=" + buyorderVoById.getBuyorderId();
        };
        return url;
    }

    @Override
    protected List<Integer> getMessageUserIdList(SendMessageDto sendMessageDto) {
        List<Integer> messageUserId = new ArrayList<>();
        Integer skuProductAssitantId = buyorderGoodsMapper.getskuProductAssitantIdByBuyorderGoodsId(sendMessageDto.getEarlyWarningTask().getRelateBusinessId());
        if (skuProductAssitantId != null) {
            messageUserId.add(skuProductAssitantId);
        }
        List<Integer> supplyChainCoordinatorIdList = buyorderGoodsMapper.getSpplyChainCoordinatorIdList();
        if (CollectionUtils.isNotEmpty(supplyChainCoordinatorIdList)) {
            messageUserId.addAll(supplyChainCoordinatorIdList);
        }
        return messageUserId;
    }

    @Override
    protected Integer getMessageTemplateId() {
        return 180;
    }

    @Override
    protected Map getTemplateVariableMap(SendMessageDto sendMessageDto) {
        Map<String,String> params = new HashMap<>();
        BuyorderGoods buyorderGoods = buyorderGoodsMapper.selectByPrimaryKey(sendMessageDto.getEarlyWarningTask().getRelateBusinessId());
        String taskDealer = sendMessageDto.getEarlyWarningTask().getTaskDealer();
        User userById = userService.getUserById(Integer.valueOf(taskDealer));

        params.put("orderNo",sendMessageDto.getEarlyWarningTask().getBusinessExtra1());
        params.put("sku",buyorderGoods.getSku());
        params.put("userName",userById.getUsername());
        return params;
    }
}
