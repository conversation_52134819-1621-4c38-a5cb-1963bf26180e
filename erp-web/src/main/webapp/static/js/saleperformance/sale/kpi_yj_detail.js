$(function()
{
    var userId = $("#others_userId_id").val();
    init_page(userId);
    
});

/**
 * @description: 初始化页面.
 * @jira: VDERP-2376【五行剑法】规则修改.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/6/17 5:48 下午.
 * @author: Tomcat.Hui.
 */
function init_page(userId) {

    var month = $("#hisMonth").val();

    // 本月概况
    $.ajax(
    {
        type : "POST",
        url : page_url + "/kpi/query/queryKpiDetailYj.do",
        data :
            {
                'userId' : userId,
                'month' : month
            },
        dataType : 'json',
        success : function(result)
        {
            if (result.code == 0)
            {
                //用户
                $("#kpi_amount_in_user").text((result.data.personalKpi.yjAmount / 10000 ).toFixed(2));
                $("#kpi_amount_target_user").text(result.data.personalKpi.monthTarget.toFixed(2));
                $("#kpi_amount_progress_user").text((result.data.personalKpi.progress * 100).toFixed(2) + '%');
                $("#kpi_amount_score_user").text(result.data.personalKpi.score.toFixed(2));
                $("#kpi_amount_sort_user").text(result.data.personalKpi.sort);

                //团队
                $("#kpi_amount_in_group").text((result.data.groupKpi.yjAmount / 10000 ).toFixed(2));
                $("#kpi_amount_target_group").text(result.data.groupKpi.monthTarget.toFixed(2));
                $("#kpi_amount_progress_group").text((result.data.groupKpi.progress * 100).toFixed(2) + '%');
                $("#kpi_amount_score_group").text(result.data.groupKpi.score.toFixed(2));
                //$("#kpi_amount_sort_group").text(result.data.groupKpi.sort);

                //榜首
                $("#first_one_span_name").text(result.data.firstKpi.userName)
                $("#kpi_amount_in_first").text((result.data.firstKpi.yjAmount / 10000 ).toFixed(2));
                $("#kpi_amount_target_first").text(result.data.firstKpi.monthTarget.toFixed(2));
                $("#kpi_amount_progress_first").text((result.data.firstKpi.progress * 100).toFixed(2) + '%');
                $("#kpi_amount_score_first").text(result.data.firstKpi.score.toFixed(2));
                $("#kpi_amount_sort_first").text(result.data.firstKpi.sort);

                //昨天
                $("#kpi_amount_in_yesterday").text((result.data.yesterdayKpi.yjAmount / 10000 ).toFixed(2));
                $("#kpi_amount_target_yesterday").text(result.data.yesterdayKpi.monthTarget.toFixed(2));
                $("#kpi_amount_progress_yesterday").text((result.data.yesterdayKpi.progress * 100).toFixed(2) + '%');
                $("#kpi_amount_score_yesterday").text(result.data.yesterdayKpi.score.toFixed(2));
                $("#kpi_amount_sort_yesterday").text(result.data.yesterdayKpi.sort);

                //上月
                $("#kpi_amount_in_lastmonth").text((result.data.lastMonthKpi.yjAmount / 10000 ).toFixed(2));
                $("#kpi_amount_target_lastmonth").text(result.data.lastMonthKpi.monthTarget.toFixed(2));
                $("#kpi_amount_progress_lastmonth").text((result.data.lastMonthKpi.progress * 100).toFixed(2) + '%');
                $("#kpi_amount_score_lastmonth").text(result.data.lastMonthKpi.score.toFixed(2));
                $("#kpi_amount_sort_lastmonth").text(result.data.lastMonthKpi.sort);

                //明细(到款)
                var saleOrderLogList = result.data.saleOrderLogList;
                show_detail(saleOrderLogList,'log_detail_saleorder');

                //明细(退款)
                var afterSaleLogList = result.data.afterSaleLogList;
                show_detail(afterSaleLogList,'log_detail_aftersale');

                // 已计入业绩&未到全款
                var sendAndNotPaid = result.data.sendAndNotPaid;
                show_detail(sendAndNotPaid,'send_and_not_paid','send_and_not_paid_page');

                // 可发货&未计入业绩
                var notSendAndNotKpi = result.data.paidNotKpi;
                show_detail(notSendAndNotKpi,'not_send_and_not_kpi','not_send_and_not_kpi_page');

                //历史记录
                var hisMonthList = result.data.hisMonthList;
                show_his(hisMonthList,'amount_log_list');

                loadMoreAddTitle();

            }
            else {
                layer.alert("返回码错误");
            }

        },
        error : function(data)
        {

            if (data.status == 1001)
            {
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
            } else {
                layer.alert("请求出现异常");
            }

        }
    });


    function show_detail(logList,table_id,page_id){

        $("#" + page_id).html('');
        var emptyFlag = false;
        var ht = '';
        if(null == logList || logList.length == 0)
        {
            emptyFlag = true;
        }

        if(!emptyFlag)
        {
            for(var i = 0; i < logList.length; i++)
            {
                var mod = logList[i];

                if(null == mod)
                    continue;
                ht += '<tr>';
                ht += '<td><a class="loadMoreAddtitle" href="javascript:void(0);" tabTitle=\'{"num":"viewsaleorder' + mod.orderId + '","link":"./order/saleorder/view.do?saleorderId=' +  mod.orderId + '","title":"订单信息"}\'>' + mod.orderNo + '</a></td>';
                ht += '<td><a class="loadMoreAddtitle" href="javascript:void(0);" tabTitle=\'{"num":"viewcustomer' + mod.traderId + '","link":"./trader/customer/baseinfo.do?traderId=' +  mod.traderId + '","title":"客户信息"}\'>' + mod.traderName + '</a></td>';
                if (table_id == 'log_detail_saleorder') {
                    ht += '<td>'+new Date(mod.validDate).format('yyyy-MM-dd hh:mm:ss')+'</td>';
                    ht += '<td>'+ mod.orderRealAmount.toFixed(2) +'</td>';
                    ht += '<td>'+mod.kpiAmount.toFixed(2)+'</td>';
                }
                if (table_id == 'log_detail_aftersale') {
                    ht += '<td>'+new Date(mod.shValidDate).format('yyyy-MM-dd hh:mm:ss')+'</td>';
                    ht += '<td>'+ mod.amount.toFixed(2)  +'</td>';
                    ht += '<td>'+mod.kpiAmount.toFixed(2)+'</td>';
                }
                if (table_id == 'send_and_not_paid') {
                    ht += '<td>'+new Date(mod.validDate).format('yyyy-MM-dd hh:mm:ss')+'</td>';
                    ht += '<td>'+ mod.orderRealAmount.toFixed(2)  +'</td>';
                    ht += '<td>'+ mod.orderPaidAmount.toFixed(2)  +'</td>';
                }
                if (table_id == 'not_send_and_not_kpi') {
                    ht += '<td>'+new Date(mod.validDate).format('yyyy-MM-dd hh:mm:ss')+'</td>';
                    ht += '<td>'+ mod.orderRealAmount.toFixed(2)  +'</td>';
                    ht += '<td>'+ mod.orderPaidAmount.toFixed(2)  +'</td>';
                }

                ht += '</tr>';
                emptyFlag = false;
            }
        }

        if(emptyFlag)
        {
            ht += '<tr><td colspan="5">暂无数据</td></tr>';
        }

        $("#" + table_id).html(ht);
    }

    function show_his(logList,table_id){
        var emptyFlag = false;
        var ht = '';
        if(null == logList || logList.length == 0)
        {
            emptyFlag = true;
        }

        if(!emptyFlag)
        {
            for(var i = 0; i < logList.length; i++)
            {
                var mod = logList[i];

                if(null == mod)
                    continue;
                ht += '<tr>';
                ht += '<td>' + mod.monthStr +'</td>';
                ht += '<td>' + mod.monthTarget +'</td>';
                ht += '<td>' + (mod.yjAmount / 10000).toFixed(2) +'</td>';
                ht += '<td>' + (mod.progress * 100).toFixed(2) + '%' +'</td>';
                ht += '<td>' + mod.groupAverageProgress.toFixed(2) +'</td>';
                ht += '<td>' + mod.score.toFixed(2) +'</td>';
                ht += '<td>' + mod.sort +'</td>';
                ht += '</tr>';
                emptyFlag = false;
            }
        }

        if(emptyFlag)
        {
            ht += '<tr><td colspan="7">暂无数据</td></tr>';
        } else {
            loadMoreAddTitle();
        }

        $("#" + table_id).html(ht);
    }
}
