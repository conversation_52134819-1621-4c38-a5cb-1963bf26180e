package com.wms.constant;

/**
 * <AUTHOR>
 * @Description 开票结果标记信息
 * @createTime 2020年08月12日 11:28:00
 */
public enum WmsInvoiceFlagEnum {
    OPENING_INVOICE(2, "符合票货同行，发票正在开具中", "C"),
    ALL_COMPLETE(1, "复核完成！请下载发票并随货寄出", "Y"),
    NO_NEED_TO_INVOCIE(0, "复核完成！无需开具发票", "N"),
    FAILED_AUDIT(-1, "开票失败，请处理后重新申请开票", "W");

    WmsInvoiceFlagEnum(Integer status, String statusStr, String code) {
        this.status = status;
        this.statusStr = statusStr;
        this.code = code;
    }

    private Integer status;

    private String statusStr;

    private String code;

    public static String getStatusStr(Integer status) {
        String statusStr = "";

        for (WmsInvoiceFlagEnum enumItem : WmsInvoiceFlagEnum.values()) {
            if (enumItem.getStatus().equals(status)) {
                statusStr = enumItem.getStatusStr();
                break;
            }
        }

        return statusStr;
    }

    public static String getCode(Integer status) {
        String code = "";

        for (WmsInvoiceFlagEnum enumItem : WmsInvoiceFlagEnum.values()) {
            if (enumItem.getStatus().equals(status)) {
                code = enumItem.getCode();
                break;
            }
        }

        return code;
    }

    public Integer getStatus() {
        return status;
    }

    public String getStatusStr() {
        return statusStr;
    }

    public String getCode() {
        return code;
    }}
