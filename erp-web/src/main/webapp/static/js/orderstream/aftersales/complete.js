function complementTask(){
	var comment = $("input[name='comment']").val()
	var taskId = $("input[name='taskId']").val()
	var pass = $("input[name='pass']").val()
	var type = $("input[name='type']").val()
	var virtualAudit = $("input[name='virtualAudit']").val()
	var isReturn = $("input[name='isReturn']:checked").val()
	var afterSalesId = $("input[name='afterSalesId']").val()
	var isConsistent=1;
	if(pass =="false" && comment == ""){
		warnTips("comment","请填写备注");
		return false;
	}
	if(pass =="true" && virtualAudit =="true" && comment == "" && isReturn =="0"){
		warnTips("comment","请填写备注");
		return false;
	}
	if(comment.length > 256){
		warnTips("comment","备注内容不允许超过256个字符");
		return false;
	}
	checkLogin();
	if(type == 1){
		//销售订单审核
		$.ajax({
			type: "POST",
			url: "./complementTask.do",
			data: $('#complement').serialize(),
			dataType:'json',
			success: function(data){
				refreshPageList(data)
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
	}else if(type == 2){
		//销售售后审核
		if(pass =="true" && virtualAudit =="true"){
			delWarnTips("audit_warn");
			$.ajax({
				type: "POST",
				url: './checkIsReturnAuditStatus.do',
				data: {
					'afterSalesId': afterSalesId,'isReturn':isReturn
				},
				dataType: 'json',
				async: false,
				success: function(data){
					if(data.code==-1){
						isConsistent=0;
						warnTips("audit_warn","退货虚拟商品有分歧，请线下协商一致！");
					}
				}
			});
		}
 		if(isConsistent==1){
			$.ajax({
				type: "POST",
				url: "./complementAfterSaleTask.do",
				data: $('#complement').serialize(),
				dataType:'json',
				success: function(data){
					refreshPageList(data)
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
		}
	}
}