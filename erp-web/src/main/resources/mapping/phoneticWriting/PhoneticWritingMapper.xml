<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.phoneticWriting.dao.PhoneticWritingMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.phoneticWriting.model.PhoneticWriting" >
    <!--          -->
    <id column="PHONETIC_WRITING_ID" property="phoneticWritingId" jdbcType="INTEGER" />
    <result column="RELATED_ID" property="relatedId" jdbcType="INTEGER" />
    <result column="ORIGINAL_CONTENT" property="originalContent" jdbcType="LONGVARCHAR" />
    <result column="UPDATED_CONTENT" property="updatedContent" jdbcType="LONGVARCHAR" />
    <result column="STATE" property="state" jdbcType="INTEGER" />
    <result column="TASK_ID" property="taskId" jdbcType="VARCHAR" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
  </resultMap>

  <sql id="Base_Column_List" >
    PHONETIC_WRITING_ID, RELATED_ID,ORIGINAL_CONTENT,UPDATED_CONTENT, STATE, TASK_ID, ADD_TIME, CREATOR, MOD_TIME, UPDATER
  </sql>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from T_PHONETIC_WRITING
    where PHONETIC WRITING_ID = #{phoneticWritingId,jdbcType=INTEGER}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from T_PHONETIC_WRITING
    where PHONETIC WRITING_ID = #{phoneticWritingId,jdbcType=INTEGER}
  </delete>

  <insert id="insert" parameterType="com.vedeng.phoneticWriting.model.PhoneticWriting" >
    <!--          -->
    insert into T_PHONETIC_WRITING (PHONETIC_WRITING_ID, RELATED_ID,STATE,
      TASK_ID, ADD_TIME, CREATOR, 
      MOD_TIME, UPDATER, ORIGINAL_CONTENT,
      UPDATED_CONTENT)
    values (#{phoneticWritingId,jdbcType=INTEGER}, #{relatedId,jdbcType=INTEGER}, #{state,jdbcType=INTEGER},
      #{taskId,jdbcType=VARCHAR}, #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER},
      #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}, #{originalContent,jdbcType=LONGVARCHAR}, 
      #{updatedContent,jdbcType=LONGVARCHAR})
  </insert>

  <insert id="insertSelective" parameterType="com.vedeng.phoneticWriting.model.PhoneticWriting" >
    <!--          -->
    insert into T_PHONETIC_WRITING
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="phoneticWritingId != null" >
        PHONETIC_WRITING_ID,
      </if>
      <if test="relatedId != null" >
        RELATED_ID,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="taskId != null" >
        TASK_ID,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
      <if test="originalContent != null" >
        ORIGINAL_CONTENT,
      </if>
      <if test="updatedContent != null" >
        UPDATED_CONTENT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="phoneticWritingId != null" >
        #{phoneticWritingId,jdbcType=INTEGER},
      </if>
      <if test="relatedId != null" >
        #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="state != null" >
        #{state,jdbcType=INTEGER},
      </if>
      <if test="taskId != null" >
        #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="originalContent != null" >
        #{originalContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="updatedContent != null" >
        #{updatedContent,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.phoneticWriting.model.PhoneticWriting" >
    <!--          -->
    update T_PHONETIC_WRITING
    <set >
      <if test="relatedId != null" >
        RELATED_ID = #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="state != null" >
        STATE = #{state,jdbcType=INTEGER},
      </if>
      <if test="taskId != null" >
        TASK_ID = #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="originalContent != null" >
        ORIGINAL_CONTENT = #{originalContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="updatedContent != null" >
        UPDATED_CONTENT = #{updatedContent,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where PHONETIC_WRITING_ID = #{phoneticWritingId,jdbcType=INTEGER}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.vedeng.phoneticWriting.model.PhoneticWriting" >
    <!--          -->
    update T_PHONETIC_WRITING
    set RELATED_ID = #{relatedId,jdbcType=INTEGER},
      ORIGINAL_CONTENT = #{originalContent,jdbcType=LONGVARCHAR},
      UPDATED_CONTENT = #{updatedContent,jdbcType=LONGVARCHAR},
      STATE = #{state,jdbcType=INTEGER},
      TASK_ID = #{taskId,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER}
    where PHONETIC_WRITING_ID = #{phoneticWritingId,jdbcType=INTEGER}
  </update>

  <insert id="insertBatch">
    insert into T_PHONETIC_WRITING (RELATED_ID,STATE, TASK_ID, ADD_TIME)
    values
    <foreach collection="list" item="g" separator="," index="index">
      (#{g.relatedId,jdbcType=INTEGER},#{g.state,jdbcType=INTEGER},
      #{g.taskId,jdbcType=VARCHAR},#{g.addTime,jdbcType=BIGINT})
    </foreach>
  </insert>

  <select id="selectWritingTask" resultType="com.vedeng.phoneticWriting.model.PhoneticWriting">
    SELECT
      <include refid="Base_Column_List" />
    FROM
        T_PHONETIC_WRITING
    WHERE
        STATE = 1
    AND
      TASK_ID != ''
  </select>

  <!--查询争议数据-->
  <select id="getPhoneticWriting" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from T_PHONETIC_WRITING
    where RELATED_ID = #{communicateRecordId,jdbcType=INTEGER}
    limit 1
  </select>

  <select id="getPhoneticWritingList" parameterType="java.util.List" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_PHONETIC_WRITING
    where RELATED_ID IN
        <foreach collection="communicateRecordIdList" item="communicateRecordId" open="(" close=")" separator=",">
          #{communicateRecordId,jdbcType=INTEGER}
        </foreach>
  </select>

  <update id="updateContent" parameterType="com.vedeng.phoneticWriting.model.PhoneticWriting">
    update T_PHONETIC_WRITING
    <set >
      <if test="state != null" >
        STATE = #{state,jdbcType=INTEGER},
      </if>

      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="originalContent != null" >
        ORIGINAL_CONTENT = #{originalContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="updatedContent != null" >
        UPDATED_CONTENT = #{updatedContent,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where TASK_ID = #{taskId,jdbcType=VARCHAR}
  </update>
  <!--文本替换-->
  <update id="updatePhoneticWritingInfo" parameterType="com.vedeng.phoneticWriting.model.ModificationRecord" >
    UPDATE T_PHONETIC_WRITING
    SET UPDATED_CONTENT = REPLACE (
        UPDATED_CONTENT,
        #{controversialContent,jdbcType=VARCHAR},
        #{modifyContent,jdbcType=VARCHAR}
    )
    WHERE
        PHONETIC_WRITING_ID = #{phoneticWritingId,jdbcType=INTEGER}
  </update>
  <select id="getPhoneticWritingIndex" resultType="java.lang.Integer" parameterType="com.vedeng.phoneticWriting.model.ModificationRecord" >
    select INSTR(UPDATED_CONTENT,#{controversialContent,jdbcType=VARCHAR} ) FROM T_PHONETIC_WRITING
    WHERE PHONETIC_WRITING_ID = #{phoneticWritingId,jdbcType=INTEGER}
  </select>
</mapper>