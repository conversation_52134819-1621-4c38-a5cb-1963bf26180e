$(function () {
    //let url = "http://file.ivedeng.com/file/display?resourceId=d51a90e6859241bebde20bffd7b06904";

    
    function createPdfContainer(id, className) {
        var pdfContainer = document.getElementById('pdf-container');
        var canvasNew = document.createElement('canvas');
        canvasNew.id = id;
        canvasNew.className = className;
        pdfContainer.appendChild(canvasNew);
    };
    let pageW = $('body').width();
    let pageH = pageW / 210 * 297;
    console.log(pageW, pageH);
    //渲染pdf
    //建议给定pdf宽度
    function renderPDF(pdf, i, id) {
        pdf.getPage(i).then(function (page) {

            var scale = 2;
            var viewport = page.getViewport(scale);
            console.log(viewport.width, viewport.height);
            //
            //  准备用于渲染的 canvas 元素
            //
            // scale = pageW / viewport.width;
            // viewport = page.getViewport(scale);
            var canvas = document.getElementById(id);
            var context = canvas.getContext('2d');

            canvas.height = viewport.height - 10;
            canvas.width = viewport.width;


            //
            // 将 PDF 页面渲染到 canvas 上下文中
            //
            var renderContext = {
                // transform: [scaleNum, 0, 0, scaleNum, 0, 0],
                canvasContext: context,
                viewport: viewport
            };

            page.render(renderContext);
        });
    };
    //创建和pdf页数等同的canvas数
    function createSeriesCanvas(num, template) {
        var id = '';
        for (var j = 1; j <= num; j++) {
            id = template + j;
            createPdfContainer(id, 'pdfClass');
        }
    }
    //读取pdf文件，并加载到页面中
    function loadPDF(fileURL) {
        pdfjsLib.getDocument(fileURL).then(function (pdf) {
            $('.J-test').html(111)
            //用 promise 获取页面
            var id = '';
            var idTemplate = 'cw-pdf-';
            var pageNum = pdf.numPages;
            //根据页码创建画布
            createSeriesCanvas(pageNum, idTemplate);
            //将pdf渲染到画布上去
            for (var i = 1; i <= pageNum; i++) {
                id = idTemplate + i;
                renderPDF(pdf, i, id);
            }

        });
    }
    //如果在本地运行，需要从服务器获取pdf文件
    loadPDF(pdfurl);
    //如果在服务端运行，需要再不跨域的情况下，获取pdf文件
    // loadPDF('234.pdf');
    
})