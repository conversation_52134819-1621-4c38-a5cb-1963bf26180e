<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.repository.mappers.KingDeePayBillMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.domain.entity.KingDeePayBillEntity">
    <!--@mbg.generated-->
    <!--@Table KING_DEE_PAY_BILL-->
    <id column="KING_DEE_PAY_BILL_ID" jdbcType="INTEGER" property="kingDeePayBillId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="F_ID" jdbcType="VARCHAR" property="fId" />
    <result column="F_BILL_NO" jdbcType="VARCHAR" property="fBillNo" />
    <result column="F_BILL_TYPE_ID" jdbcType="VARCHAR" property="fBillTypeId" />
    <result column="F_SETTLE_ORG_ID" jdbcType="VARCHAR" property="fSettleOrgId" />
    <result column="F_PAY_ORG_ID" jdbcType="VARCHAR" property="fPayOrgId" />
    <result column="F_DATE" jdbcType="VARCHAR" property="fDate" />
    <result column="F_CONTACT_UNIT_TYPE" jdbcType="VARCHAR" property="fContactUnitType" />
    <result column="F_CONTACT_UNIT" jdbcType="VARCHAR" property="fContactUnit" />
    <result column="F_RECT_UNIT_TYPE" jdbcType="VARCHAR" property="fRectUnitType" />
    <result column="F_RECT_UNIT" jdbcType="VARCHAR" property="fRectUnit" />
    <result column="F_CURRENCY_ID" jdbcType="VARCHAR" property="fCurrencyId" />
    <result column="F_EXCHANGE_RATE" jdbcType="DECIMAL" property="fExchangeRate" />
    <result column="F_SETTLE_RATE" jdbcType="DECIMAL" property="fSettleRate" />
    <result column="F_BUSINESS_TYPE" jdbcType="VARCHAR" property="fBusinessType" />
    <result column="F_IS_SAME_ORG" jdbcType="BOOLEAN" property="fIsSameOrg" />
    <result column="F_IS_CREDIT" jdbcType="BOOLEAN" property="fIsCredit" />
    <result column="F_SETTLE_CUR" jdbcType="VARCHAR" property="fSettleCur" />
    <result column="F_IS_WRITE_OFF" jdbcType="BOOLEAN" property="fIsWriteOff" />
    <result column="F_REAL_PAY" jdbcType="BOOLEAN" property="fRealPay" />
    <result column="F_REMARK" jdbcType="VARCHAR" property="fRemark" />
    <result column="F_IS_CARRY_RATE" jdbcType="BOOLEAN" property="fIsCarryRate" />
    <result column="F_SETTLE_MAIN_BOOK_ID" jdbcType="VARCHAR" property="fSettleMainBookId" />
    <result column="F_MORE_RECEIVE" jdbcType="BOOLEAN" property="fMoreReceive" />
    <result column="F_SOURCE_SYSTEM" jdbcType="VARCHAR" property="fSourceSystem" />
    <result column="F_PAY_BILL_ENTRY" javaType="com.vedeng.erp.kingdee.dto.KingDeePayBillEntryDto"
            property="fPayBillEntry"
            typeHandler="com.vedeng.common.mybatis.handler.CustomSqlJsonArrayHandler"/>
    <result column="F_PAY_BILL_SRC_ENTRY"
            javaType="com.vedeng.erp.kingdee.dto.KingDeePayBillSrcEntryDto"
            property="fPayBillSrcEntry"
            typeHandler="com.vedeng.common.mybatis.handler.CustomSqlJsonArrayHandler"/>
    <result column="F_OPPOSITE_BANK_ACCOUNT" jdbcType="VARCHAR" property="fOppositeBankAccount" />
    <result column="F_OPPOSITE_CCOUNT_NAME" jdbcType="VARCHAR" property="fOppositeCcountName" />
    <result column="F_OPPOSITE_BANK_NAME" jdbcType="VARCHAR" property="fOppositeBankName" />
    <result column="F_CNAPS" jdbcType="VARCHAR" property="fCnaps" />
    <result column="F_QZOK_LSH" jdbcType="VARCHAR" property="fQzokLsh" />
    <result column="F_QZOK_DZHZD" jdbcType="VARCHAR" property="fQzokDzhzd" />
    <result column="F_QZOK_DR" jdbcType="VARCHAR" property="fQzokDr"/>
    <result column="F_QZOK_JYLX" jdbcType="VARCHAR" property="fQzokJylx"/>
    <result column="F_QZOK_JYZT" jdbcType="VARCHAR" property="fQzokJyzt"/>
    <result column="F_QZOK_CGDDH" jdbcType="VARCHAR" property="fQzokCgddh"/>
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    KING_DEE_PAY_BILL_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME,
    F_ID, F_BILL_NO, F_BILL_TYPE_ID, F_SETTLE_ORG_ID, F_PAY_ORG_ID, F_DATE, F_CONTACT_UNIT_TYPE,
    F_CONTACT_UNIT, F_RECT_UNIT_TYPE, F_RECT_UNIT, F_CURRENCY_ID, F_EXCHANGE_RATE, F_SETTLE_RATE,
    F_BUSINESS_TYPE, F_IS_SAME_ORG, F_IS_CREDIT, F_SETTLE_CUR, F_IS_WRITE_OFF, F_REAL_PAY,
    F_REMARK, F_IS_CARRY_RATE, F_SETTLE_MAIN_BOOK_ID, F_MORE_RECEIVE, F_SOURCE_SYSTEM,
    F_PAY_BILL_ENTRY, F_PAY_BILL_SRC_ENTRY, F_OPPOSITE_BANK_ACCOUNT, F_OPPOSITE_CCOUNT_NAME,
    F_OPPOSITE_BANK_NAME, F_CNAPS, F_QZOK_LSH, F_QZOK_DZHZD,F_QZOK_DR,F_QZOK_JYLX,F_QZOK_JYZT,F_QZOK_CGDDH,IS_DELETE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from KING_DEE_PAY_BILL
    where KING_DEE_PAY_BILL_ID = #{kingDeePayBillId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from KING_DEE_PAY_BILL
    where KING_DEE_PAY_BILL_ID = #{kingDeePayBillId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="KING_DEE_PAY_BILL_ID" keyProperty="kingDeePayBillId" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeePayBillEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_PAY_BILL (ADD_TIME, MOD_TIME, CREATOR,
      UPDATER, CREATOR_NAME, UPDATER_NAME,
      F_ID, F_BILL_NO, F_BILL_TYPE_ID,
      F_SETTLE_ORG_ID, F_PAY_ORG_ID, F_DATE,
      F_CONTACT_UNIT_TYPE, F_CONTACT_UNIT, F_RECT_UNIT_TYPE,
      F_RECT_UNIT, F_CURRENCY_ID, F_EXCHANGE_RATE,
      F_SETTLE_RATE, F_BUSINESS_TYPE, F_IS_SAME_ORG,
      F_IS_CREDIT, F_SETTLE_CUR, F_IS_WRITE_OFF,
      F_REAL_PAY, F_REMARK, F_IS_CARRY_RATE,
      F_SETTLE_MAIN_BOOK_ID, F_MORE_RECEIVE, F_SOURCE_SYSTEM,
      F_PAY_BILL_ENTRY, F_PAY_BILL_SRC_ENTRY, F_OPPOSITE_BANK_ACCOUNT,
      F_OPPOSITE_CCOUNT_NAME, F_OPPOSITE_BANK_NAME,
      F_CNAPS, F_QZOK_LSH, F_QZOK_DZHZD,F_QZOK_DR,F_QZOK_JYLX,F_QZOK_JYZT,F_QZOK_CGDDH
      )
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER},
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR},
      #{fId,jdbcType=VARCHAR}, #{fBillNo,jdbcType=VARCHAR}, #{fBillTypeId,jdbcType=VARCHAR},
      #{fSettleOrgId,jdbcType=VARCHAR}, #{fPayOrgId,jdbcType=VARCHAR}, #{fDate,jdbcType=VARCHAR},
      #{fContactUnitType,jdbcType=VARCHAR}, #{fContactUnit,jdbcType=VARCHAR}, #{fRectUnitType,jdbcType=VARCHAR},
      #{fRectUnit,jdbcType=VARCHAR}, #{fCurrencyId,jdbcType=VARCHAR}, #{fExchangeRate,jdbcType=DECIMAL},
      #{fSettleRate,jdbcType=DECIMAL}, #{fBusinessType,jdbcType=VARCHAR}, #{fIsSameOrg,jdbcType=BOOLEAN},
      #{fIsCredit,jdbcType=BOOLEAN}, #{fSettleCur,jdbcType=VARCHAR}, #{fIsWriteOff,jdbcType=BOOLEAN},
      #{fRealPay,jdbcType=BOOLEAN}, #{fRemark,jdbcType=VARCHAR}, #{fIsCarryRate,jdbcType=BOOLEAN},
      #{fSettleMainBookId,jdbcType=VARCHAR}, #{fMoreReceive,jdbcType=BOOLEAN}, #{fSourceSystem,jdbcType=VARCHAR},
      #{fPayBillEntry,jdbcType=VARCHAR}, #{fPayBillSrcEntry,jdbcType=VARCHAR}, #{fOppositeBankAccount,jdbcType=VARCHAR},
      #{fOppositeCcountName,jdbcType=VARCHAR}, #{fOppositeBankName,jdbcType=VARCHAR},
      #{fCnaps,jdbcType=VARCHAR}, #{fQzokLsh,jdbcType=VARCHAR}, #{fQzokDzhzd,jdbcType=VARCHAR},
      #{fQzokDr,jdbcType=VARCHAR},#{fQzokJylx,jdbcType=VARCHAR},#{fQzokJyzt,jdbcType=VARCHAR},
      #{fQzokCgddh,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="KING_DEE_PAY_BILL_ID" keyProperty="kingDeePayBillId" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeePayBillEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_PAY_BILL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="fId != null">
        F_ID,
      </if>
      <if test="fBillNo != null">
        F_BILL_NO,
      </if>
      <if test="fBillTypeId != null">
        F_BILL_TYPE_ID,
      </if>
      <if test="fSettleOrgId != null">
        F_SETTLE_ORG_ID,
      </if>
      <if test="fPayOrgId != null">
        F_PAY_ORG_ID,
      </if>
      <if test="fDate != null">
        F_DATE,
      </if>
      <if test="fContactUnitType != null">
        F_CONTACT_UNIT_TYPE,
      </if>
      <if test="fContactUnit != null">
        F_CONTACT_UNIT,
      </if>
      <if test="fRectUnitType != null">
        F_RECT_UNIT_TYPE,
      </if>
      <if test="fRectUnit != null">
        F_RECT_UNIT,
      </if>
      <if test="fCurrencyId != null">
        F_CURRENCY_ID,
      </if>
      <if test="fExchangeRate != null">
        F_EXCHANGE_RATE,
      </if>
      <if test="fSettleRate != null">
        F_SETTLE_RATE,
      </if>
      <if test="fBusinessType != null">
        F_BUSINESS_TYPE,
      </if>
      <if test="fIsSameOrg != null">
        F_IS_SAME_ORG,
      </if>
      <if test="fIsCredit != null">
        F_IS_CREDIT,
      </if>
      <if test="fSettleCur != null">
        F_SETTLE_CUR,
      </if>
      <if test="fIsWriteOff != null">
        F_IS_WRITE_OFF,
      </if>
      <if test="fRealPay != null">
        F_REAL_PAY,
      </if>
      <if test="fRemark != null">
        F_REMARK,
      </if>
      <if test="fIsCarryRate != null">
        F_IS_CARRY_RATE,
      </if>
      <if test="fSettleMainBookId != null">
        F_SETTLE_MAIN_BOOK_ID,
      </if>
      <if test="fMoreReceive != null">
        F_MORE_RECEIVE,
      </if>
      <if test="fSourceSystem != null">
        F_SOURCE_SYSTEM,
      </if>
      <if test="fPayBillEntry != null">
        F_PAY_BILL_ENTRY,
      </if>
      <if test="fPayBillSrcEntry != null">
        F_PAY_BILL_SRC_ENTRY,
      </if>
      <if test="fOppositeBankAccount != null">
        F_OPPOSITE_BANK_ACCOUNT,
      </if>
      <if test="fOppositeCcountName != null">
        F_OPPOSITE_CCOUNT_NAME,
      </if>
      <if test="fOppositeBankName != null">
        F_OPPOSITE_BANK_NAME,
      </if>
      <if test="fCnaps != null">
        F_CNAPS,
      </if>
      <if test="fQzokLsh != null">
        F_QZOK_LSH,
      </if>
      <if test="fQzokDzhzd != null">
        F_QZOK_DZHZD,
      </if>
      <if test="fQzokDr != null">
        F_QZOK_DR,
      </if>
      <if test="fQzokJylx != null">
        F_QZOK_JYLX,
      </if>
      <if test="fQzokJyzt != null">
        F_QZOK_JYZT,
      </if>
      <if test="fQzokCgddh != null">
        F_QZOK_CGDDH,
      </if>
      <if test="fileIsPush != null">
        FILE_IS_PUSH,
      </if>
      <if test="erpBankBillId != null and erpBankBillId != ''">
        ERP_BANK_BILL_ID,
      </if>
      <if test="fQzokZdtjyh != null and fQzokZdtjyh != ''">
        F_QZOK_ZDTJYH,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fId != null">
        #{fId,jdbcType=VARCHAR},
      </if>
      <if test="fBillNo != null">
        #{fBillNo,jdbcType=VARCHAR},
      </if>
      <if test="fBillTypeId != null">
        #{fBillTypeId,jdbcType=VARCHAR},
      </if>
      <if test="fSettleOrgId != null">
        #{fSettleOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fPayOrgId != null">
        #{fPayOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fDate != null">
        #{fDate,jdbcType=VARCHAR},
      </if>
      <if test="fContactUnitType != null">
        #{fContactUnitType,jdbcType=VARCHAR},
      </if>
      <if test="fContactUnit != null">
        #{fContactUnit,jdbcType=VARCHAR},
      </if>
      <if test="fRectUnitType != null">
        #{fRectUnitType,jdbcType=VARCHAR},
      </if>
      <if test="fRectUnit != null">
        #{fRectUnit,jdbcType=VARCHAR},
      </if>
      <if test="fCurrencyId != null">
        #{fCurrencyId,jdbcType=VARCHAR},
      </if>
      <if test="fExchangeRate != null">
        #{fExchangeRate,jdbcType=DECIMAL},
      </if>
      <if test="fSettleRate != null">
        #{fSettleRate,jdbcType=DECIMAL},
      </if>
      <if test="fBusinessType != null">
        #{fBusinessType,jdbcType=VARCHAR},
      </if>
      <if test="fIsSameOrg != null">
        #{fIsSameOrg,jdbcType=BOOLEAN},
      </if>
      <if test="fIsCredit != null">
        #{fIsCredit,jdbcType=BOOLEAN},
      </if>
      <if test="fSettleCur != null">
        #{fSettleCur,jdbcType=VARCHAR},
      </if>
      <if test="fIsWriteOff != null">
        #{fIsWriteOff,jdbcType=BOOLEAN},
      </if>
      <if test="fRealPay != null">
        #{fRealPay,jdbcType=BOOLEAN},
      </if>
      <if test="fRemark != null">
        #{fRemark,jdbcType=VARCHAR},
      </if>
      <if test="fIsCarryRate != null">
        #{fIsCarryRate,jdbcType=BOOLEAN},
      </if>
      <if test="fSettleMainBookId != null">
        #{fSettleMainBookId,jdbcType=VARCHAR},
      </if>
      <if test="fMoreReceive != null">
        #{fMoreReceive,jdbcType=BOOLEAN},
      </if>
      <if test="fSourceSystem != null">
        #{fSourceSystem,jdbcType=VARCHAR},
      </if>
      <if test="fPayBillEntry != null">
        #{fPayBillEntry,jdbcType=VARCHAR},
      </if>
      <if test="fPayBillSrcEntry != null">
        #{fPayBillSrcEntry,jdbcType=VARCHAR},
      </if>
      <if test="fOppositeBankAccount != null">
        #{fOppositeBankAccount,jdbcType=VARCHAR},
      </if>
      <if test="fOppositeCcountName != null">
        #{fOppositeCcountName,jdbcType=VARCHAR},
      </if>
      <if test="fOppositeBankName != null">
        #{fOppositeBankName,jdbcType=VARCHAR},
      </if>
      <if test="fCnaps != null">
        #{fCnaps,jdbcType=VARCHAR},
      </if>
      <if test="fQzokLsh != null">
        #{fQzokLsh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokDzhzd != null">
        #{fQzokDzhzd,jdbcType=VARCHAR},
      </if>
      <if test="fQzokDr != null">
        #{fQzokDr,jdbcType=VARCHAR},
      </if>
      <if test="fQzokJylx != null">
        #{fQzokJylx,jdbcType=VARCHAR},
      </if>
      <if test="fQzokJyzt != null">
        #{fQzokJyzt,jdbcType=VARCHAR},
      </if>
      <if test="fQzokCgddh != null">
        #{fQzokCgddh,jdbcType=VARCHAR},
      </if>
      <if test="fileIsPush != null">
        #{fileIsPush,jdbcType=INTEGER},
      </if>
      <if test="erpBankBillId != null and erpBankBillId != ''">
        #{erpBankBillId,jdbcType=VARCHAR},
      </if>
      <if test="fQzokZdtjyh != null and fQzokZdtjyh != ''">
        #{fQzokZdtjyh,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeePayBillEntity">
    <!--@mbg.generated-->
    update KING_DEE_PAY_BILL
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fId != null">
        F_ID = #{fId,jdbcType=VARCHAR},
      </if>
      <if test="fBillNo != null">
        F_BILL_NO = #{fBillNo,jdbcType=VARCHAR},
      </if>
      <if test="fBillTypeId != null">
        F_BILL_TYPE_ID = #{fBillTypeId,jdbcType=VARCHAR},
      </if>
      <if test="fSettleOrgId != null">
        F_SETTLE_ORG_ID = #{fSettleOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fPayOrgId != null">
        F_PAY_ORG_ID = #{fPayOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fDate != null">
        F_DATE = #{fDate,jdbcType=VARCHAR},
      </if>
      <if test="fContactUnitType != null">
        F_CONTACT_UNIT_TYPE = #{fContactUnitType,jdbcType=VARCHAR},
      </if>
      <if test="fContactUnit != null">
        F_CONTACT_UNIT = #{fContactUnit,jdbcType=VARCHAR},
      </if>
      <if test="fRectUnitType != null">
        F_RECT_UNIT_TYPE = #{fRectUnitType,jdbcType=VARCHAR},
      </if>
      <if test="fRectUnit != null">
        F_RECT_UNIT = #{fRectUnit,jdbcType=VARCHAR},
      </if>
      <if test="fCurrencyId != null">
        F_CURRENCY_ID = #{fCurrencyId,jdbcType=VARCHAR},
      </if>
      <if test="fExchangeRate != null">
        F_EXCHANGE_RATE = #{fExchangeRate,jdbcType=DECIMAL},
      </if>
      <if test="fSettleRate != null">
        F_SETTLE_RATE = #{fSettleRate,jdbcType=DECIMAL},
      </if>
      <if test="fBusinessType != null">
        F_BUSINESS_TYPE = #{fBusinessType,jdbcType=VARCHAR},
      </if>
      <if test="fIsSameOrg != null">
        F_IS_SAME_ORG = #{fIsSameOrg,jdbcType=BOOLEAN},
      </if>
      <if test="fIsCredit != null">
        F_IS_CREDIT = #{fIsCredit,jdbcType=BOOLEAN},
      </if>
      <if test="fSettleCur != null">
        F_SETTLE_CUR = #{fSettleCur,jdbcType=VARCHAR},
      </if>
      <if test="fIsWriteOff != null">
        F_IS_WRITE_OFF = #{fIsWriteOff,jdbcType=BOOLEAN},
      </if>
      <if test="fRealPay != null">
        F_REAL_PAY = #{fRealPay,jdbcType=BOOLEAN},
      </if>
      <if test="fRemark != null">
        F_REMARK = #{fRemark,jdbcType=VARCHAR},
      </if>
      <if test="fIsCarryRate != null">
        F_IS_CARRY_RATE = #{fIsCarryRate,jdbcType=BOOLEAN},
      </if>
      <if test="fSettleMainBookId != null">
        F_SETTLE_MAIN_BOOK_ID = #{fSettleMainBookId,jdbcType=VARCHAR},
      </if>
      <if test="fMoreReceive != null">
        F_MORE_RECEIVE = #{fMoreReceive,jdbcType=BOOLEAN},
      </if>
      <if test="fSourceSystem != null">
        F_SOURCE_SYSTEM = #{fSourceSystem,jdbcType=VARCHAR},
      </if>
      <if test="fPayBillEntry != null">
        F_PAY_BILL_ENTRY = #{fPayBillEntry,jdbcType=VARCHAR},
      </if>
      <if test="fPayBillSrcEntry != null">
        F_PAY_BILL_SRC_ENTRY = #{fPayBillSrcEntry,jdbcType=VARCHAR},
      </if>
      <if test="fOppositeBankAccount != null">
        F_OPPOSITE_BANK_ACCOUNT = #{fOppositeBankAccount,jdbcType=VARCHAR},
      </if>
      <if test="fOppositeCcountName != null">
        F_OPPOSITE_CCOUNT_NAME = #{fOppositeCcountName,jdbcType=VARCHAR},
      </if>
      <if test="fOppositeBankName != null">
        F_OPPOSITE_BANK_NAME = #{fOppositeBankName,jdbcType=VARCHAR},
      </if>
      <if test="fCnaps != null">
        F_CNAPS = #{fCnaps,jdbcType=VARCHAR},
      </if>
      <if test="fQzokLsh != null">
        F_QZOK_LSH = #{fQzokLsh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokDzhzd != null">
        F_QZOK_DZHZD = #{fQzokDzhzd,jdbcType=VARCHAR},
      </if>
      <if test="fQzokDr != null">
        F_QZOK_DR = #{fQzokDr,jdbcType=VARCHAR},
      </if>
      <if test="fQzokJylx != null">
        F_QZOK_JYLX = #{fQzokJylx,jdbcType=VARCHAR},
      </if>
      <if test="fQzokJyzt != null">
        F_QZOK_JYZT = #{fQzokJyzt,jdbcType=VARCHAR},
      </if>
      <if test="fQzokCgddh != null">
        F_QZOK_CGDDH = #{fQzokCgddh,jdbcType=VARCHAR},
      </if>
    </set>
    where KING_DEE_PAY_BILL_ID = #{kingDeePayBillId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeePayBillEntity">
    <!--@mbg.generated-->
    update KING_DEE_PAY_BILL
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      F_ID = #{fId,jdbcType=VARCHAR},
      F_BILL_NO = #{fBillNo,jdbcType=VARCHAR},
      F_BILL_TYPE_ID = #{fBillTypeId,jdbcType=VARCHAR},
      F_SETTLE_ORG_ID = #{fSettleOrgId,jdbcType=VARCHAR},
      F_PAY_ORG_ID = #{fPayOrgId,jdbcType=VARCHAR},
      F_DATE = #{fDate,jdbcType=VARCHAR},
      F_CONTACT_UNIT_TYPE = #{fContactUnitType,jdbcType=VARCHAR},
      F_CONTACT_UNIT = #{fContactUnit,jdbcType=VARCHAR},
      F_RECT_UNIT_TYPE = #{fRectUnitType,jdbcType=VARCHAR},
      F_RECT_UNIT = #{fRectUnit,jdbcType=VARCHAR},
      F_CURRENCY_ID = #{fCurrencyId,jdbcType=VARCHAR},
      F_EXCHANGE_RATE = #{fExchangeRate,jdbcType=DECIMAL},
      F_SETTLE_RATE = #{fSettleRate,jdbcType=DECIMAL},
      F_BUSINESS_TYPE = #{fBusinessType,jdbcType=VARCHAR},
      F_IS_SAME_ORG = #{fIsSameOrg,jdbcType=BOOLEAN},
      F_IS_CREDIT = #{fIsCredit,jdbcType=BOOLEAN},
      F_SETTLE_CUR = #{fSettleCur,jdbcType=VARCHAR},
      F_IS_WRITE_OFF = #{fIsWriteOff,jdbcType=BOOLEAN},
      F_REAL_PAY = #{fRealPay,jdbcType=BOOLEAN},
      F_REMARK = #{fRemark,jdbcType=VARCHAR},
      F_IS_CARRY_RATE = #{fIsCarryRate,jdbcType=BOOLEAN},
      F_SETTLE_MAIN_BOOK_ID = #{fSettleMainBookId,jdbcType=VARCHAR},
      F_MORE_RECEIVE = #{fMoreReceive,jdbcType=BOOLEAN},
      F_SOURCE_SYSTEM = #{fSourceSystem,jdbcType=VARCHAR},
      F_PAY_BILL_ENTRY = #{fPayBillEntry,jdbcType=VARCHAR},
      F_PAY_BILL_SRC_ENTRY = #{fPayBillSrcEntry,jdbcType=VARCHAR},
      F_OPPOSITE_BANK_ACCOUNT = #{fOppositeBankAccount,jdbcType=VARCHAR},
      F_OPPOSITE_CCOUNT_NAME = #{fOppositeCcountName,jdbcType=VARCHAR},
      F_OPPOSITE_BANK_NAME = #{fOppositeBankName,jdbcType=VARCHAR},
      F_CNAPS = #{fCnaps,jdbcType=VARCHAR},
      F_QZOK_LSH = #{fQzokLsh,jdbcType=VARCHAR},
      F_QZOK_DZHZD = #{fQzokDzhzd,jdbcType=VARCHAR},
      F_QZOK_DR = #{fQzokDr,jdbcType=VARCHAR},
      F_QZOK_JYLX = #{fQzokJylx,jdbcType=VARCHAR},
      F_QZOK_JYZT = #{fQzokJyzt,jdbcType=VARCHAR},
      F_QZOK_CGDDH = #{fQzokCgddh,jdbcType=VARCHAR}
    where KING_DEE_PAY_BILL_ID = #{kingDeePayBillId,jdbcType=INTEGER}
  </update>
  <select id="findByAll" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from KING_DEE_PAY_BILL
        <where>
            <if test="kingDeePayBillId != null">
                and KING_DEE_PAY_BILL_ID=#{kingDeePayBillId,jdbcType=INTEGER}
            </if>
            <if test="addTime != null">
                and ADD_TIME=#{addTime,jdbcType=TIMESTAMP}
            </if>
            <if test="modTime != null">
                and MOD_TIME=#{modTime,jdbcType=TIMESTAMP}
            </if>
            <if test="creator != null">
                and CREATOR=#{creator,jdbcType=INTEGER}
            </if>
            <if test="updater != null">
                and UPDATER=#{updater,jdbcType=INTEGER}
            </if>
            <if test="creatorName != null">
                and CREATOR_NAME=#{creatorName,jdbcType=VARCHAR}
            </if>
            <if test="updaterName != null">
                and UPDATER_NAME=#{updaterName,jdbcType=VARCHAR}
            </if>
            <if test="fId != null">
                and F_ID=#{fId,jdbcType=VARCHAR}
            </if>
            <if test="fBillNo != null">
                and F_BILL_NO=#{fBillNo,jdbcType=VARCHAR}
            </if>
            <if test="fBillTypeId != null">
                and F_BILL_TYPE_ID=#{fBillTypeId,jdbcType=VARCHAR}
            </if>
            <if test="fSettleOrgId != null">
                and F_SETTLE_ORG_ID=#{fSettleOrgId,jdbcType=VARCHAR}
            </if>
            <if test="fPayOrgId != null">
                and F_PAY_ORG_ID=#{fPayOrgId,jdbcType=VARCHAR}
            </if>
            <if test="fDate != null">
                and F_DATE=#{fDate,jdbcType=TIMESTAMP}
            </if>
            <if test="fContactUnitType != null">
                and F_CONTACT_UNIT_TYPE=#{fContactUnitType,jdbcType=VARCHAR}
            </if>
            <if test="fContactUnit != null">
                and F_CONTACT_UNIT=#{fContactUnit,jdbcType=VARCHAR}
            </if>
            <if test="fRectUnitType != null">
                and F_RECT_UNIT_TYPE=#{fRectUnitType,jdbcType=VARCHAR}
            </if>
            <if test="fRectUnit != null">
                and F_RECT_UNIT=#{fRectUnit,jdbcType=VARCHAR}
            </if>
            <if test="fCurrencyId != null">
                and F_CURRENCY_ID=#{fCurrencyId,jdbcType=VARCHAR}
            </if>
            <if test="fExchangeRate != null">
                and F_EXCHANGE_RATE=#{fExchangeRate,jdbcType=DECIMAL}
            </if>
            <if test="fSettleRate != null">
                and F_SETTLE_RATE=#{fSettleRate,jdbcType=DECIMAL}
            </if>
            <if test="fBusinessType != null">
                and F_BUSINESS_TYPE=#{fBusinessType,jdbcType=VARCHAR}
            </if>
            <if test="fIsSameOrg != null">
                and F_IS_SAME_ORG=#{fIsSameOrg,jdbcType=BOOLEAN}
            </if>
            <if test="fIsCredit != null">
                and F_IS_CREDIT=#{fIsCredit,jdbcType=BOOLEAN}
            </if>
            <if test="fSettleCur != null">
                and F_SETTLE_CUR=#{fSettleCur,jdbcType=VARCHAR}
            </if>
            <if test="fIsWriteOff != null">
                and F_IS_WRITE_OFF=#{fIsWriteOff,jdbcType=BOOLEAN}
            </if>
            <if test="fRealPay != null">
                and F_REAL_PAY=#{fRealPay,jdbcType=BOOLEAN}
            </if>
            <if test="fRemark != null">
                and F_REMARK=#{fRemark,jdbcType=VARCHAR}
            </if>
            <if test="fIsCarryRate != null">
                and F_IS_CARRY_RATE=#{fIsCarryRate,jdbcType=BOOLEAN}
            </if>
            <if test="fSettleMainBookId != null">
                and F_SETTLE_MAIN_BOOK_ID=#{fSettleMainBookId,jdbcType=VARCHAR}
            </if>
            <if test="fMoreReceive != null">
                and F_MORE_RECEIVE=#{fMoreReceive,jdbcType=BOOLEAN}
            </if>
            <if test="fSourceSystem != null">
                and F_SOURCE_SYSTEM=#{fSourceSystem,jdbcType=VARCHAR}
            </if>
            <if test="fPayBillEntry != null">
                and F_PAY_BILL_ENTRY=#{fPayBillEntry,jdbcType=VARCHAR}
            </if>
            <if test="fPayBillSrcEntry != null">
                and F_PAY_BILL_SRC_ENTRY=#{fPayBillSrcEntry,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update KING_DEE_PAY_BILL
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fId != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_BILL_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fBillNo != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fBillNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_BILL_TYPE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fBillTypeId != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fBillTypeId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_SETTLE_ORG_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fSettleOrgId != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fSettleOrgId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_PAY_ORG_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fPayOrgId != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fPayOrgId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_DATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fDate != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fDate,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_CONTACT_UNIT_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fContactUnitType != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fContactUnitType,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_CONTACT_UNIT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fContactUnit != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fContactUnit,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_RECT_UNIT_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fRectUnitType != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fRectUnitType,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_RECT_UNIT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fRectUnit != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fRectUnit,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_CURRENCY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fCurrencyId != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fCurrencyId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_EXCHANGE_RATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fExchangeRate != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fExchangeRate,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_SETTLE_RATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fSettleRate != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fSettleRate,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_BUSINESS_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fBusinessType != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fBusinessType,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_IS_SAME_ORG = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fIsSameOrg != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fIsSameOrg,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_IS_CREDIT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fIsCredit != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fIsCredit,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_SETTLE_CUR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fSettleCur != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fSettleCur,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_IS_WRITE_OFF = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fIsWriteOff != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fIsWriteOff,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_REAL_PAY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fRealPay != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fRealPay,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fRemark != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fRemark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_IS_CARRY_RATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fIsCarryRate != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fIsCarryRate,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_SETTLE_MAIN_BOOK_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fSettleMainBookId != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fSettleMainBookId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_MORE_RECEIVE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fMoreReceive != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fMoreReceive,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_SOURCE_SYSTEM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fSourceSystem != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fSourceSystem,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_PAY_BILL_ENTRY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fPayBillEntry != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fPayBillEntry,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_PAY_BILL_SRC_ENTRY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fPayBillSrcEntry != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fPayBillSrcEntry,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_OPPOSITE_BANK_ACCOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fOppositeBankAccount != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fOppositeBankAccount,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_OPPOSITE_CCOUNT_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fOppositeCcountName != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fOppositeCcountName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_OPPOSITE_BANK_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fOppositeBankName != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fOppositeBankName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_CNAPS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fCnaps != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fCnaps,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_LSH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokLsh != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fQzokLsh,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_DZHZD = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokDzhzd != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fQzokDzhzd,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>

      <if test="fQzokDr != null">
        #{
      </if>
      <if test=" != null">
        #{fQzokJylx,jdbcType=VARCHAR},
      </if>
      <if test=" != null">
        #{fQzokJyzt,jdbcType=VARCHAR},
      </if>
      <if test=" != null">
        #{fQzokCgddh,jdbcType=VARCHAR},
      </if>
      <trim prefix="F_QZOK_DR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokDr != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fQzokDr,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_JYLX = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokJylx != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fQzokJylx,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_JYZT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokJyzt != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fQzokJyzt,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_CGDDH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokCgddh != null">
            when KING_DEE_PAY_BILL_ID = #{item.kingDeePayBillId,jdbcType=INTEGER} then #{item.fQzokCgddh,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where KING_DEE_PAY_BILL_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.kingDeePayBillId,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="KING_DEE_PAY_BILL_ID" keyProperty="kingDeePayBillId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_PAY_BILL
    (ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, F_ID, F_BILL_NO,
    F_BILL_TYPE_ID, F_SETTLE_ORG_ID, F_PAY_ORG_ID, F_DATE, F_CONTACT_UNIT_TYPE, F_CONTACT_UNIT,
    F_RECT_UNIT_TYPE, F_RECT_UNIT, F_CURRENCY_ID, F_EXCHANGE_RATE, F_SETTLE_RATE, F_BUSINESS_TYPE,
    F_IS_SAME_ORG, F_IS_CREDIT, F_SETTLE_CUR, F_IS_WRITE_OFF, F_REAL_PAY, F_REMARK,
    F_IS_CARRY_RATE, F_SETTLE_MAIN_BOOK_ID, F_MORE_RECEIVE, F_SOURCE_SYSTEM, F_PAY_BILL_ENTRY,
    F_PAY_BILL_SRC_ENTRY, F_OPPOSITE_BANK_ACCOUNT, F_OPPOSITE_CCOUNT_NAME, F_OPPOSITE_BANK_NAME,
    F_CNAPS, F_QZOK_LSH, F_QZOK_DZHZD,F_QZOK_DR,F_QZOK_JYLX,F_QZOK_JYZT,F_QZOK_CGDDH)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER},
      #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, #{item.updaterName,jdbcType=VARCHAR},
      #{item.fId,jdbcType=VARCHAR}, #{item.fBillNo,jdbcType=VARCHAR}, #{item.fBillTypeId,jdbcType=VARCHAR},
      #{item.fSettleOrgId,jdbcType=VARCHAR}, #{item.fPayOrgId,jdbcType=VARCHAR}, #{item.fDate,jdbcType=VARCHAR},
      #{item.fContactUnitType,jdbcType=VARCHAR}, #{item.fContactUnit,jdbcType=VARCHAR},
      #{item.fRectUnitType,jdbcType=VARCHAR}, #{item.fRectUnit,jdbcType=VARCHAR}, #{item.fCurrencyId,jdbcType=VARCHAR},
      #{item.fExchangeRate,jdbcType=DECIMAL}, #{item.fSettleRate,jdbcType=DECIMAL}, #{item.fBusinessType,jdbcType=VARCHAR},
      #{item.fIsSameOrg,jdbcType=BOOLEAN}, #{item.fIsCredit,jdbcType=BOOLEAN}, #{item.fSettleCur,jdbcType=VARCHAR},
      #{item.fIsWriteOff,jdbcType=BOOLEAN}, #{item.fRealPay,jdbcType=BOOLEAN}, #{item.fRemark,jdbcType=VARCHAR},
      #{item.fIsCarryRate,jdbcType=BOOLEAN}, #{item.fSettleMainBookId,jdbcType=VARCHAR},
      #{item.fMoreReceive,jdbcType=BOOLEAN}, #{item.fSourceSystem,jdbcType=VARCHAR},
      #{item.fPayBillEntry,jdbcType=VARCHAR}, #{item.fPayBillSrcEntry,jdbcType=VARCHAR},
      #{item.fOppositeBankAccount,jdbcType=VARCHAR}, #{item.fOppositeCcountName,jdbcType=VARCHAR},
      #{item.fOppositeBankName,jdbcType=VARCHAR}, #{item.fCnaps,jdbcType=VARCHAR}, #{item.fQzokLsh,jdbcType=VARCHAR},
      #{item.fQzokDzhzd,jdbcType=VARCHAR},#{fQzokDr,jdbcType=VARCHAR},#{fQzokJylx,jdbcType=VARCHAR},
      #{fQzokJyzt,jdbcType=VARCHAR},#{fQzokCgddh,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="queryKingDeePayBillNoReturnUrl" resultType="com.vedeng.erp.kingdee.dto.KingDeePayBillDto">
    select
      KING_DEE_PAY_BILL_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME,
      F_ID, F_BILL_NO, F_BILL_TYPE_ID, F_SETTLE_ORG_ID, F_PAY_ORG_ID, F_DATE, F_CONTACT_UNIT_TYPE,
      F_CONTACT_UNIT, F_RECT_UNIT_TYPE, F_RECT_UNIT, F_CURRENCY_ID, F_EXCHANGE_RATE, F_SETTLE_RATE,
      F_BUSINESS_TYPE, F_IS_SAME_ORG, F_IS_CREDIT, F_SETTLE_CUR, F_IS_WRITE_OFF, F_REAL_PAY,
      F_REMARK, F_IS_CARRY_RATE, F_SETTLE_MAIN_BOOK_ID, F_MORE_RECEIVE, F_SOURCE_SYSTEM,
      F_OPPOSITE_BANK_ACCOUNT, F_OPPOSITE_CCOUNT_NAME,
      F_OPPOSITE_BANK_NAME, F_CNAPS, F_QZOK_LSH, F_QZOK_DZHZD,F_QZOK_DR,F_QZOK_JYLX,F_QZOK_JYZT,F_QZOK_CGDDH
    from KING_DEE_PAY_BILL a
    where
        <!--查询金蝶返回由判断电子回执单为空变为由流水号判断，不再拉取金蝶电子回单(a.F_QZOK_DZHZD is null or a.F_QZOK_DZHZD ='')-->
        (a.F_QZOK_LSH is null or a.F_QZOK_LSH ='')
    AND IS_DELETE = 0
  </select>

  <select id="queryKingDeePayBillNoReturnUrlAndIds" resultType="com.vedeng.erp.kingdee.dto.KingDeePayBillDto">
    select
      KING_DEE_PAY_BILL_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME,
      F_ID, F_BILL_NO, F_BILL_TYPE_ID, F_SETTLE_ORG_ID, F_PAY_ORG_ID, F_DATE, F_CONTACT_UNIT_TYPE,
      F_CONTACT_UNIT, F_RECT_UNIT_TYPE, F_RECT_UNIT, F_CURRENCY_ID, F_EXCHANGE_RATE, F_SETTLE_RATE,
      F_BUSINESS_TYPE, F_IS_SAME_ORG, F_IS_CREDIT, F_SETTLE_CUR, F_IS_WRITE_OFF, F_REAL_PAY,
      F_REMARK, F_IS_CARRY_RATE, F_SETTLE_MAIN_BOOK_ID, F_MORE_RECEIVE, F_SOURCE_SYSTEM,
      F_OPPOSITE_BANK_ACCOUNT, F_OPPOSITE_CCOUNT_NAME,
      F_OPPOSITE_BANK_NAME, F_CNAPS, F_QZOK_LSH, F_QZOK_DZHZD,F_QZOK_DR,F_QZOK_JYLX,F_QZOK_JYZT,F_QZOK_CGDDH
    from KING_DEE_PAY_BILL a where (a.F_QZOK_DZHZD is null or a.F_QZOK_DZHZD ='') and IS_DELETE = 0 and  a.KING_DEE_PAY_BILL_ID in
      <foreach collection="list" item="item" close=")" open="(" separator="," >
        #{item,jdbcType=INTEGER}
      </foreach>
  </select>

  <update id="deleteKingDeeInfoByFBillNo">
    UPDATE KING_DEE_PAY_BILL SET IS_DELETE = 1 WHERE F_BILL_NO = #{fBillNo,jdbcType=VARCHAR}
  </update>
  <update id="updatePushStatus">
    UPDATE KING_DEE_PAY_BILL
    SET FILE_IS_PUSH = 1
    where KING_DEE_PAY_BILL_ID = #{kingDeePayBillId,jdbcType=INTEGER}
  </update>
  <select id="getKingDeePayByBankBillId" resultType="com.vedeng.erp.kingdee.dto.KingDeePayBillDto">
    select
      KING_DEE_PAY_BILL_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME,
      F_ID, F_BILL_NO, F_BILL_TYPE_ID, F_SETTLE_ORG_ID, F_PAY_ORG_ID, F_DATE, F_CONTACT_UNIT_TYPE,
      F_CONTACT_UNIT, F_RECT_UNIT_TYPE, F_RECT_UNIT, F_CURRENCY_ID, F_EXCHANGE_RATE, F_SETTLE_RATE,
      F_BUSINESS_TYPE, F_IS_SAME_ORG, F_IS_CREDIT, F_SETTLE_CUR, F_IS_WRITE_OFF, F_REAL_PAY,
      F_REMARK, F_IS_CARRY_RATE, F_SETTLE_MAIN_BOOK_ID, F_MORE_RECEIVE, F_SOURCE_SYSTEM,
      F_OPPOSITE_BANK_ACCOUNT, F_OPPOSITE_CCOUNT_NAME,
      F_OPPOSITE_BANK_NAME, F_CNAPS, F_QZOK_LSH, F_QZOK_DZHZD,F_QZOK_DR,F_QZOK_JYLX,F_QZOK_JYZT,F_QZOK_CGDDH
    from KING_DEE_PAY_BILL a
    where  a.IS_DELETE = 0
      <if test="fileIsPush == 0">
        AND  a.FILE_IS_PUSH=0
      </if>
      and  a.ERP_BANK_BILL_ID= #{bankBillId}
  </select>

  <select id="queryKingDeeReceiveRefundBillNoReturnUrl" resultType="com.vedeng.erp.kingdee.dto.KingDeePayBillDto">
    select
    KING_DEE_RECEIVE_REFUND_BILL_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME,
    F_ID, F_BILL_NO, F_BILL_TYPE_ID, F_SETTLE_ORG_ID, F_PAY_ORG_ID, F_DATE, F_CONTACT_UNIT_TYPE,
    F_CONTACT_UNIT, F_RECT_UNIT_TYPE, F_RECT_UNIT,
    F_BUSINESS_TYPE, F_QZOK_LSH, F_QZOK_DZHZD
    from KING_DEE_RECEIVE_REFUND_BILL a
    where
    <!--查询金蝶返回由判断电子回执单为空变为由流水号判断，不再拉取金蝶电子回单(a.F_QZOK_DZHZD is null or a.F_QZOK_DZHZD ='')-->
    (a.F_QZOK_LSH is null or a.F_QZOK_LSH ='')
    AND IS_DELETE = 0
  </select>

  <update id="deleteReceiveRefundInfoByFBillNo">
    UPDATE KING_DEE_RECEIVE_REFUND_BILL SET IS_DELETE = 1 WHERE F_BILL_NO = #{fBillNo,jdbcType=VARCHAR}
  </update>

  <select id="selectByFBillNo" resultType="com.vedeng.erp.kingdee.dto.KingDeePayBillDto">
    select * from KING_DEE_PAY_BILL where F_BILL_NO = #{fBillNo,jdbcType=VARCHAR} and IS_DELETE = 0
    </select>
</mapper>
