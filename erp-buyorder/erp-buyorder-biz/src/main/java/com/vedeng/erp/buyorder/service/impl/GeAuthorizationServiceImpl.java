package com.vedeng.erp.buyorder.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.redis.utils.RedisUtil;
import com.vedeng.erp.buyorder.domain.entity.*;
import com.vedeng.erp.buyorder.mapper.*;
import com.vedeng.erp.buyorder.service.GeAuthorizationService;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * ge授权书接口实现类
 *
 * <AUTHOR>
 * @date 2022/2/15 11:03
 **/
@Service
@Slf4j
public class GeAuthorizationServiceImpl implements GeAuthorizationService {

    @Resource
    private GeAuthorizationCertificateMapper geAuthorizationCertificateMapper;

    @Resource
    private GeAuthorizationMapper geAuthorizationMapper;

    @Resource
    private GeActionLogMapper geActionLogMapper;

    @Resource
    private GeBusinessChanceMapper geBusinessChanceMapper;

    @Resource
    private GeAuthorizationVerifyMapper geAuthorizationVerifyMapper;

    @Value("${oss_url}")
    private String ossUrl;


    private static String zero =  "0000000";

    @Override
    @Transactional
    public List<GeAuthorizationCertificate> saveYyFile(HttpServletRequest request, GeAuthorization geAuthorization, Integer userId) {
        return saveTypeFile(request,geAuthorization,userId,"yyName","yybusUri","name_1","uri_1",1);
    }

    @Override
    public List<GeAuthorizationCertificate> saveElFile(HttpServletRequest request, GeAuthorization geAuthorization, Integer userId) {
        return saveTypeFile(request,geAuthorization,userId,"elName","elUri","name_2","uri_2",2);
    }

    @Override
    public List<GeAuthorizationCertificate> saveSlFile(HttpServletRequest request, GeAuthorization geAuthorization, Integer userId) {
        return saveTypeFile(request,geAuthorization,userId,"slName","slUri","name_3","uri_3",3);
    }

    @Override
    public List<GeAuthorizationCertificate> saveCxFile(HttpServletRequest request, GeAuthorization geAuthorization, Integer userId) {
        return saveTypeFile(request,geAuthorization,userId,"cxName","cxUri","name_5","uri_5",4);
    }

    @Override
    public List<GeAuthorizationCertificate> saveSyFile(HttpServletRequest request, GeAuthorization geAuthorization, Integer userId) {
        return saveTypeFile(request, geAuthorization, userId,"syName","syUri","name_6", "uri_6",5);
    }

    @Override
    public List<GeAuthorizationCertificate> saveXyFile(HttpServletRequest request, GeAuthorization geAuthorization, Integer userId) {
        return saveTypeFile(request, geAuthorization, userId,"xyName","xyUri","name_7", "uri_7",6);
    }

    @Override
    public List<GeAuthorizationCertificate> saveZbFile(HttpServletRequest request, GeAuthorization geAuthorization, Integer userId) {
        return saveTypeFile(request, geAuthorization, userId,"zbName","zbUri","name_8", "uri_8",7);
    }

    @Override
    public List<GeAuthorizationCertificate> saveQtFile(HttpServletRequest request, GeAuthorization geAuthorization, Integer userId) {
        return saveTypeFile(request, geAuthorization, userId,"qtName","qtUri","name_9", "uri_9",8);
    }

    private List<GeAuthorizationCertificate> saveTypeFile(HttpServletRequest request, GeAuthorization geAuthorization, Integer userId, String name, String uri, String names, String uris, Integer type) {

        List<GeAuthorizationCertificate> cxCertificates = new ArrayList<>();
        // 域名
        String domain = request.getParameter("domain");
        String oneName = request.getParameter(name);
        String oneUri = request.getParameter(uri);
        if (!StringUtils.isEmpty(oneName) && !StringUtils.isEmpty(oneUri)) {
            GeAuthorizationCertificate geAuthorizationCertificate = buildGeAuthorizationCertificate(domain, oneName, oneUri, userId, geAuthorization, type);
            cxCertificates.add(geAuthorizationCertificate);
        }
        String[] name_s = request.getParameterValues(names);
        String[] uri_s = request.getParameterValues(uris);
        if (name_s != null && uri_s != null && name_s.length == uri_s.length) {

            for (int i = 0; i < uri_s.length; i++) {
                if (StringUtils.isEmpty(uri_s[i])) {
                    continue;
                }
                GeAuthorizationCertificate ge = buildGeAuthorizationCertificate(domain, name_s[i], uri_s[i], userId, geAuthorization, type);
                cxCertificates.add(ge);
            }
        }
        if (!CollectionUtils.isEmpty(cxCertificates)) {
            cxCertificates.forEach(c -> geAuthorizationCertificateMapper.insertSelective(c));
        }

        return cxCertificates;
    }



    public GeAuthorizationCertificate buildGeAuthorizationCertificate(@NonNull String domain, @NonNull String name, @NonNull String uri, Integer userId, GeAuthorization geAuthorization, @NonNull Integer type) {

        log.info("入参：domain:{},name:{},uri:{},userId:{},geAuthorization:{},type:{}", domain, name, uri, userId, geAuthorization, type);
        GeAuthorizationCertificate geAuthorizationCertificate = new GeAuthorizationCertificate();
        if (!StringUtils.isEmpty(name)) {
            String[] split = name.split("\\.");
            geAuthorizationCertificate.setName(name);
            if (split.length == 2) {
                geAuthorizationCertificate.setSuffix(split[1]);
            }
        }
        if (!StringUtils.isEmpty(uri)) {
            geAuthorizationCertificate.setUri(uri);
        }
        if (!Objects.isNull(geAuthorization.getAuthorizationId())) {
            geAuthorizationCertificate.setAuthorizationId(geAuthorization.getAuthorizationId());
        }

        geAuthorizationCertificate.setType(type);
        geAuthorizationCertificate.setDomain(ossUrl);
        geAuthorizationCertificate.setCreator(userId);
        geAuthorizationCertificate.setUpdater(userId);
        geAuthorizationCertificate.setAddTime(new Date());
        geAuthorizationCertificate.setModTime(new Date());

        log.info("根据入参绑定授权书信息=={}", JSON.toJSONString(geAuthorizationCertificate));
        return geAuthorizationCertificate;
    }

    @Override
    public String getGeAuthorizationNoByRedis(String key, String prefix, Integer length) {

        // 时间
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        // 自增
        long l = RedisUtil.StringOps.incrBy(key, 1);
        if (l == 0) {
             l = RedisUtil.StringOps.incrBy(key, 1);
        }
        // 当日
        int expireTime = (int) ((cal.getTimeInMillis() - System.currentTimeMillis()) / 1000);
        RedisUtil.KeyOps.expire(key, expireTime, TimeUnit.SECONDS);
        StringBuilder suffix = new StringBuilder();
        StringBuilder append = suffix.append(zero).append(l);
        // 规则 GESQ 220215001
        String str = append.substring(append.length() - length, append.length());
        SimpleDateFormat formatter = new SimpleDateFormat("yyMMdd");
        String timeStr = formatter.format(cal.getTimeInMillis());
        String authorizationNo = prefix + timeStr + str;
        log.info("authorizationNo:{}",authorizationNo);
        return authorizationNo;
    }

    @Override
    public Map<String, Object> getGeAuthorizationData(Integer authorizationId) {

        // 授权书主信息
        GeAuthorization geAuthorization = geAuthorizationMapper.selectByPrimaryKey(authorizationId);
        GeAuthorizationCertificate param = new GeAuthorizationCertificate();
        param.setAuthorizationId(authorizationId);
        param.setIsDelete(0);
        List<GeAuthorizationCertificate> geAuthorizationCertificates = geAuthorizationCertificateMapper.selectByGeAuId(param);
        List<GeAuthorizationCertificate> yy = geAuthorizationCertificates.stream().filter(c -> c.getType() == 1).collect(Collectors.toList());
        List<GeAuthorizationCertificate> el = geAuthorizationCertificates.stream().filter(c -> c.getType() == 2).collect(Collectors.toList());
        List<GeAuthorizationCertificate> sl = geAuthorizationCertificates.stream().filter(c -> c.getType() == 3).collect(Collectors.toList());
        List<GeAuthorizationCertificate> cx = geAuthorizationCertificates.stream().filter(c -> c.getType() == 4).collect(Collectors.toList());
        List<GeAuthorizationCertificate> sy = geAuthorizationCertificates.stream().filter(c -> c.getType() == 5).collect(Collectors.toList());
        List<GeAuthorizationCertificate> xy = geAuthorizationCertificates.stream().filter(c -> c.getType() == 6).collect(Collectors.toList());
        List<GeAuthorizationCertificate> zb = geAuthorizationCertificates.stream().filter(c -> c.getType() == 7).collect(Collectors.toList());
        List<GeAuthorizationCertificate> qt = geAuthorizationCertificates.stream().filter(c -> c.getType() == 8).collect(Collectors.toList());


        Map<String, Object> data = new HashMap<>();
        if (geAuthorization != null && geAuthorization.getGeBussinessChanceId() != null) {
            GeBusinessChance geBusinessChance = geBusinessChanceMapper.selectByPrimaryKey(geAuthorization.getGeBussinessChanceId());
            data.put("geBusinessChance", geBusinessChance);
        }
        data.put("geAuthorization", geAuthorization);
        data.put("yy", yy);
        data.put("el", el);
        data.put("sl", sl);
        data.put("cx", cx);
        data.put("sy", sy);
        data.put("xy", xy);
        data.put("zb", zb);
        data.put("qt", qt);
        return data;
    }

    @Override
    public GeAuthorization saveGeAuthorization(GeAuthorization geAuthorization) {
        List<GeAuthorization> count = countGeBussinessChanceHaveNoCloseGeAuthorization(geAuthorization);
        if (CollectionUtils.isEmpty(count)) {
            geAuthorizationMapper.insertSelective(geAuthorization);
            log.info("geAuthorization:{}",geAuthorization);
        }
        return geAuthorization;
    }

    @Override
    public List<GeAuthorization> countGeBussinessChanceHaveNoCloseGeAuthorization(GeAuthorization geAuthorization) {
        return geAuthorizationMapper.countGeBussinessChanceHaveNoCloseGeAuthorization(geAuthorization);
    }

    @Override
    public void updateAuthorizationById(GeAuthorization geAuthorization) {
        geAuthorizationMapper.updateByPrimaryKeySelective(geAuthorization);
    }

    @Override
    public void deleteOldTypeFile(GeAuthorization geAuthorization) {

        if (null != geAuthorization && geAuthorization.getAuthorizationId() != null) {
            GeAuthorizationCertificate geAuthorizationCertificate = new GeAuthorizationCertificate();
            geAuthorizationCertificate.setAuthorizationId(geAuthorization.getAuthorizationId());
            geAuthorizationCertificateMapper.updateByParam(geAuthorizationCertificate);
        }
    }

    @Override
    public boolean checkBusinessChance(Integer geBussinessChanceId) {
        GeBusinessChance geBusinessChance = geBusinessChanceMapper.selectByPrimaryKey(geBussinessChanceId);
        if (geBusinessChance.getStatus() == 1) {
            return true;
        }
        return false;
    }

    @Override
    public String getGeAuthorizationLogContent(String[] changes) {
        StringBuilder logContent = new StringBuilder();
        if (changes == null) {
            return "";
        } else {
            for (String change : changes) {
                logContent.append(getLogContent(change));
            }
        }
        if (logContent.length()>0) {
            return logContent.substring(0, logContent.length() - 1);
        }
        return logContent.toString();
    }

    @Override
    public List<GeActionLog> getGeAuthorizationLogData(Integer authorizationId) {
        GeActionLog geActionLog = new GeActionLog();
        geActionLog.setRelatedId(authorizationId);
        geActionLog.setRelatedType(2);
        return geActionLogMapper.queryByRelatedTypeAndRelatedId(geActionLog);
    }

    @Override
    public boolean doForAudit(Integer authorizationId,Integer status) {
        GeAuthorization geAuthorization = geAuthorizationMapper.selectByPrimaryKey(authorizationId);
        if (geAuthorization.getStatus() == 0 || geAuthorization.getStatus() == 3) {
            GeAuthorization update = new GeAuthorization();
            update.setAuthorizationId(authorizationId);
            update.setStatus(status);
            update.setUpdateTime(new Date());
            return geAuthorizationMapper.updateByPrimaryKeySelective(update)>0;
        }
        return false;
    }

    @Override
    public void saveGeAuthorizationAuitLog(GeAuthorizationVerify geAuthorizationVerify) {
        geAuthorizationVerifyMapper.insertSelective(geAuthorizationVerify);
    }

    @Override
    public boolean makeAudit(GeAuthorization geAuthorization) {
        return geAuthorizationMapper.updateByPrimaryKeySelective(geAuthorization)>0;
    }

    @Override
    public List<GeAuthorizationVerify> getGeAuthorizationVerifyData(Integer authorizationId) {
       return geAuthorizationVerifyMapper.selectByAuthorizationId(authorizationId);
    }

    private String getLogContent(String change) {
        if ("0".equals(change)) {
            return "基本信息，";
        }
        if ("1".equals(change)) {
            return "营业执照，";
        }
        if ("2".equals(change)) {
            return "二类医疗资质，";
        }
        if ("3".equals(change)) {
            return "三类医疗资质，";
        }
        if ("4".equals(change)) {
            return "诚信声明，";
        }
        if ("5".equals(change)) {
            return "使用二级分销商声明，";
        }
        if ("6".equals(change)) {
            return "信用查询结果，";
        }
        if ("7".equals(change)) {
            return "招标文件，";
        }
        if ("8".equals(change)) {
            return "其他附件，";
        }
        if ("11".equals(change)) {
            return "备注，";
        }
        return "";
    }

    @Override
    public void saveAuthorizationLog(GeActionLog geActionLog) {
        log.info("授权书日志信息：{}", JSON.toJSONString(geActionLog));
        geActionLogMapper.insertSelective(geActionLog);
    }


}
