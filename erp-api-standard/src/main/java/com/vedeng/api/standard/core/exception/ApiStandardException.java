package com.vedeng.api.standard.core.exception;

import com.vedeng.api.standard.core.BaseResponseCode;

/**
 * API标准模块专用异常类
 * 用于替代直接使用RuntimeException的场景，提供更好的语义化和错误码支持
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-18
 */
public class ApiStandardException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 错误码
     */
    private Integer code;
    
    /**
     * 错误消息
     */
    private String message;
    
    /**
     * 详细错误信息
     */
    private String detailMessage;
    
    /**
     * 构造函数 - 使用默认系统错误码
     */
    public ApiStandardException(String message) {
        super(message);
        this.code = BaseResponseCode.SYSTEM_BUSY.getCode();
        this.message = message;
    }
    
    /**
     * 构造函数 - 自定义错误码和消息
     */
    public ApiStandardException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }
    
    /**
     * 构造函数 - 使用响应码枚举
     */
    public ApiStandardException(BaseResponseCode responseCode) {
        super(responseCode.getMessage());
        this.code = responseCode.getCode();
        this.message = responseCode.getMessage();
    }
    
    /**
     * 构造函数 - 使用响应码枚举和自定义消息
     */
    public ApiStandardException(BaseResponseCode responseCode, String customMessage) {
        super(customMessage);
        this.code = responseCode.getCode();
        this.message = customMessage;
    }
    
    /**
     * 构造函数 - 包含原因异常
     */
    public ApiStandardException(String message, Throwable cause) {
        super(message, cause);
        this.code = BaseResponseCode.SYSTEM_BUSY.getCode();
        this.message = message;
    }
    
    /**
     * 构造函数 - 包含原因异常和错误码
     */
    public ApiStandardException(Integer code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
    }
    
    /**
     * 构造函数 - 包含详细错误信息
     */
    public ApiStandardException(String message, String detailMessage) {
        super(message);
        this.code = BaseResponseCode.SYSTEM_BUSY.getCode();
        this.message = message;
        this.detailMessage = detailMessage;
    }
    
    /**
     * 静态工厂方法 - 系统错误（最常用）
     */
    public static ApiStandardException systemError(String message) {
        return new ApiStandardException(BaseResponseCode.SYSTEM_BUSY, message);
    }
    
    /**
     * 静态工厂方法 - 系统错误，包含原因异常
     */
    public static ApiStandardException systemError(String message, Throwable cause) {
        return new ApiStandardException(BaseResponseCode.SYSTEM_BUSY.getCode(), message, cause);
    }
    
    /**
     * 静态工厂方法 - 数据转换错误
     */
    public static ApiStandardException dataConversionError(String message) {
        return new ApiStandardException(BaseResponseCode.PARAMETER_FORMAT_ERROR, message);
    }
    
    /**
     * 静态工厂方法 - 数据转换错误，包含原因异常
     */
    public static ApiStandardException dataConversionError(String message, Throwable cause) {
        return new ApiStandardException(BaseResponseCode.PARAMETER_FORMAT_ERROR.getCode(), message, cause);
    }
    
    /**
     * 静态工厂方法 - 服务执行错误
     */
    public static ApiStandardException serviceExecutionError(String message) {
        return new ApiStandardException(BaseResponseCode.OPERATION_ERROR, message);
    }
    
    /**
     * 静态工厂方法 - 服务执行错误，包含原因异常
     */
    public static ApiStandardException serviceExecutionError(String message, Throwable cause) {
        return new ApiStandardException(BaseResponseCode.OPERATION_ERROR.getCode(), message, cause);
    }
    
    /**
     * 静态工厂方法 - 配置错误
     */
    public static ApiStandardException configurationError(String message) {
        return new ApiStandardException(BaseResponseCode.SYSTEM_BUSY, message);
    }
    
    /**
     * 静态工厂方法 - 配置错误，包含原因异常
     */
    public static ApiStandardException configurationError(String message, Throwable cause) {
        return new ApiStandardException(BaseResponseCode.SYSTEM_BUSY.getCode(), message, cause);
    }
    
    // Getter methods
    public Integer getCode() {
        return code;
    }
    
    @Override
    public String getMessage() {
        return message;
    }
    
    public String getDetailMessage() {
        return detailMessage;
    }
    
    public void setDetailMessage(String detailMessage) {
        this.detailMessage = detailMessage;
    }
    
    @Override
    public String toString() {
        return "ApiStandardException{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", detailMessage='" + detailMessage + '\'' +
                '}';
    }
}
