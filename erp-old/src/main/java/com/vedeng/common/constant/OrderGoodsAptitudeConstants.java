package com.vedeng.common.constant;

public class OrderGoodsAptitudeConstants {

    /**
     * 证书缺失
     */
    public static final int CERTIFICATE_IS_NULL = 1;
    /**
     * 证书过期
     */
    public static final int CERTIFICATE_DATE_OUT = 2;
    /**
     * 证书有效
     */
    public static final int CERTIFICATE_VAILD = 3;
    /**
     * 营业执照不包括医疗器械
     */
    public static final int CERTIFICATE_WITHOUT_MED = 4;
    /**
     * 商品是医疗器械
     */
    public static final Integer GOODS_IS_INSTRUMENT = 1;
    /**
     * 客户为营利机构
     */
    public static final Integer TRADER_IS_PROFIT = 0;
    /**
     * 资质通过审核
     */
    public static final Integer APTITTUDE_IS_PASSED = 1;
    /**
     * 二类旧国标类别
     */
    public static final Integer LEVEL_SECOND_OLD_MEDICAL_CATEGORY = 239;
    /**
     * 三类旧国标类别
     */
    public static final Integer LEVEL_THIRD_OLD_MEDICAL_CATEGORY = 240;
    /**
     * 二类新国标类别
     */
    public static final Integer LEVEL_SECOND_NEW_MEDICAL_CATEGORY = 250;
    /**
     * 三类新国标类别
     */
    public static final Integer LEVEL_THIRD_NEW_MEDICAL_CATEGORY = 251;
    /**
     * 启动自动审核
     */
    public static final String KEY_AUTO_CHECK_APTITUDE = "autoCheckAptitude";

    public static final String KEY_SALE_DIRECTOR = "销售主管审核";
    public static final String KEY_AUTOCHECK_APTITUDE = "资质自动审核";
    public static final String KEY_OPERATE_CHECK = "运营部审核";

    public static final String KEY_FAWU_CHECK = "法务审批";

    public static final String KEY_QUALITY_DEPART_CHECK = "质量管理部审核";

    public static final String KEY_SALE_MANAGER="销售经理审核";
    public static final String KEY_SALE_CSO = "销售总监审核";
    public static final String DIRECT_SUPERIOR_APPROVAL = "直接上级审批";
    public static final String SALES_MANAGER_POST = "销售经理岗";
    public static final String SALES_DIRECTOR_POST = "销售总监岗";
    public static final String GOODS_MANAGEMENT_USER_APTITUDE = "产品经理或助理审核";

    /** 新国标20医疗器械 **/
    public static final Integer LEVEL_CHN_MEDICAL_NEW = 1044;
    /** 新国标20医疗器械 **/
    public static final Integer LEVEL_CHN_MEDICAL_OLD = 216;
}
