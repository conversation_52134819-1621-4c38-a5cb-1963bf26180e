<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.SupplierAssetMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.SupplierAssetEntity">
    <!--@mbg.generated-->
    <!--@Table T_SUPPLIER_ASSET-->
    <id column="SUPPLIER_ASSET_ID" jdbcType="INTEGER" property="supplierAssetId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_SUPPLIER_ID" jdbcType="INTEGER" property="traderSupplierId" />
    <result column="ASSET_TYPE" jdbcType="INTEGER" property="assetType" />
    <result column="ASSET" jdbcType="DECIMAL" property="asset" />
    <result column="APPLY_ASSET" jdbcType="DECIMAL" property="applyAsset" />
    <result column="OCCUPY_ASSET" jdbcType="DECIMAL" property="occupyAsset" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    SUPPLIER_ASSET_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, 
    TRADER_ID, TRADER_SUPPLIER_ID, ASSET_TYPE, ASSET, APPLY_ASSET, OCCUPY_ASSET
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_SUPPLIER_ASSET
    where SUPPLIER_ASSET_ID = #{supplierAssetId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_SUPPLIER_ASSET
    where SUPPLIER_ASSET_ID = #{supplierAssetId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="SUPPLIER_ASSET_ID" keyProperty="supplierAssetId" parameterType="com.vedeng.erp.trader.domain.entity.SupplierAssetEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SUPPLIER_ASSET (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      TRADER_ID, TRADER_SUPPLIER_ID, ASSET_TYPE, 
      ASSET, APPLY_ASSET, OCCUPY_ASSET
      )
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{traderId,jdbcType=INTEGER}, #{traderSupplierId,jdbcType=INTEGER}, #{assetType,jdbcType=INTEGER}, 
      #{asset,jdbcType=DECIMAL}, #{applyAsset,jdbcType=DECIMAL}, #{occupyAsset,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" keyColumn="SUPPLIER_ASSET_ID" keyProperty="supplierAssetId" parameterType="com.vedeng.erp.trader.domain.entity.SupplierAssetEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SUPPLIER_ASSET
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderSupplierId != null">
        TRADER_SUPPLIER_ID,
      </if>
      <if test="assetType != null">
        ASSET_TYPE,
      </if>
      <if test="asset != null">
        ASSET,
      </if>
      <if test="applyAsset != null">
        APPLY_ASSET,
      </if>
      <if test="occupyAsset != null">
        OCCUPY_ASSET,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderSupplierId != null">
        #{traderSupplierId,jdbcType=INTEGER},
      </if>
      <if test="assetType != null">
        #{assetType,jdbcType=INTEGER},
      </if>
      <if test="asset != null">
        #{asset,jdbcType=DECIMAL},
      </if>
      <if test="applyAsset != null">
        #{applyAsset,jdbcType=DECIMAL},
      </if>
      <if test="occupyAsset != null">
        #{occupyAsset,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.SupplierAssetEntity">
    <!--@mbg.generated-->
    update T_SUPPLIER_ASSET
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderSupplierId != null">
        TRADER_SUPPLIER_ID = #{traderSupplierId,jdbcType=INTEGER},
      </if>
      <if test="assetType != null">
        ASSET_TYPE = #{assetType,jdbcType=INTEGER},
      </if>
      <if test="asset != null">
        ASSET = #{asset,jdbcType=DECIMAL},
      </if>
      <if test="applyAsset != null">
        APPLY_ASSET = #{applyAsset,jdbcType=DECIMAL},
      </if>
      <if test="occupyAsset != null">
        OCCUPY_ASSET = #{occupyAsset,jdbcType=DECIMAL},
      </if>
    </set>
    where SUPPLIER_ASSET_ID = #{supplierAssetId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.SupplierAssetEntity">
    <!--@mbg.generated-->
    update T_SUPPLIER_ASSET
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      TRADER_SUPPLIER_ID = #{traderSupplierId,jdbcType=INTEGER},
      ASSET_TYPE = #{assetType,jdbcType=INTEGER},
      ASSET = #{asset,jdbcType=DECIMAL},
      APPLY_ASSET = #{applyAsset,jdbcType=DECIMAL},
      OCCUPY_ASSET = #{occupyAsset,jdbcType=DECIMAL}
    where SUPPLIER_ASSET_ID = #{supplierAssetId,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2023-11-23-->
  <select id="findBySupplierAssetId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_SUPPLIER_ASSET
    where SUPPLIER_ASSET_ID=#{supplierAssetId,jdbcType=INTEGER}
  </select>

<!--auto generated by MybatisCodeHelper on 2023-11-27-->
  <select id="findBySupplierAssetIdAndAssetType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_SUPPLIER_ASSET
    where SUPPLIER_ASSET_ID=#{supplierAssetId,jdbcType=INTEGER} and ASSET_TYPE=#{assetType,jdbcType=INTEGER}
  </select>

<!--auto generated by MybatisCodeHelper on 2023-11-29-->
  <select id="findByTraderSupplierIdAndAssetType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_SUPPLIER_ASSET
    where TRADER_SUPPLIER_ID=#{traderSupplierId,jdbcType=INTEGER} and ASSET_TYPE=#{assetType,jdbcType=INTEGER}
  </select>

  <!--auto generated by MybatisCodeHelper on 2023-11-27-->
</mapper>