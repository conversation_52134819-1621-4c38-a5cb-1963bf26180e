<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.infrastructure.bank.api.mapper.MinshengBankTransferRecordMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.infrastructure.bank.api.domain.entity.MinshengBankTransferRecord">
    <!--@mbg.generated-->
    <!--@Table T_MINSHENG_BANK_TRANSFER_RECORD-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="PAY_APPLY_ID" jdbcType="INTEGER" property="payApplyId" />
    <result column="CREDIT_TYPE" jdbcType="VARCHAR" property="creditType" />
    <result column="INS_ID" jdbcType="VARCHAR" property="insId" />
    <result column="SVR_ID" jdbcType="VARCHAR" property="svrId" />
    <result column="RESPONSE_CODE" jdbcType="VARCHAR" property="responseCode" />
    <result column="RESPONSE_MESSAGE" jdbcType="VARCHAR" property="responseMessage" />
    <result column="TRANSFER_CODE" jdbcType="VARCHAR" property="transferCode" />
    <result column="TRANSFER_MESSAGE" jdbcType="VARCHAR" property="transferMessage" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, ADD_TIME, CREATOR, CREATOR_NAME, MOD_TIME, UPDATER, UPDATER_NAME, PAY_APPLY_ID, 
    CREDIT_TYPE, INS_ID, SVR_ID, RESPONSE_CODE, RESPONSE_MESSAGE, TRANSFER_CODE, TRANSFER_MESSAGE, 
    IS_DELETE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_MINSHENG_BANK_TRANSFER_RECORD
    where ID = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_MINSHENG_BANK_TRANSFER_RECORD
    where ID = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.infrastructure.bank.api.domain.entity.MinshengBankTransferRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_MINSHENG_BANK_TRANSFER_RECORD (ADD_TIME, CREATOR, CREATOR_NAME, 
      MOD_TIME, UPDATER, UPDATER_NAME, 
      PAY_APPLY_ID, CREDIT_TYPE, INS_ID, 
      SVR_ID, RESPONSE_CODE, RESPONSE_MESSAGE, 
      TRANSFER_CODE, TRANSFER_MESSAGE, IS_DELETE
      )
    values (#{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, 
      #{modTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR}, 
      #{payApplyId,jdbcType=INTEGER}, #{creditType,jdbcType=VARCHAR}, #{insId,jdbcType=VARCHAR}, 
      #{svrId,jdbcType=VARCHAR}, #{responseCode,jdbcType=VARCHAR}, #{responseMessage,jdbcType=VARCHAR}, 
      #{transferCode,jdbcType=VARCHAR}, #{transferMessage,jdbcType=VARCHAR}, #{isDelete,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.infrastructure.bank.api.domain.entity.MinshengBankTransferRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_MINSHENG_BANK_TRANSFER_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="payApplyId != null">
        PAY_APPLY_ID,
      </if>
      <if test="creditType != null and creditType != ''">
        CREDIT_TYPE,
      </if>
      <if test="insId != null and insId != ''">
        INS_ID,
      </if>
      <if test="svrId != null and svrId != ''">
        SVR_ID,
      </if>
      <if test="responseCode != null and responseCode != ''">
        RESPONSE_CODE,
      </if>
      <if test="responseMessage != null and responseMessage != ''">
        RESPONSE_MESSAGE,
      </if>
      <if test="transferCode != null and transferCode != ''">
        TRANSFER_CODE,
      </if>
      <if test="transferMessage != null and transferMessage != ''">
        TRANSFER_MESSAGE,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="payApplyId != null">
        #{payApplyId,jdbcType=INTEGER},
      </if>
      <if test="creditType != null and creditType != ''">
        #{creditType,jdbcType=VARCHAR},
      </if>
      <if test="insId != null and insId != ''">
        #{insId,jdbcType=VARCHAR},
      </if>
      <if test="svrId != null and svrId != ''">
        #{svrId,jdbcType=VARCHAR},
      </if>
      <if test="responseCode != null and responseCode != ''">
        #{responseCode,jdbcType=VARCHAR},
      </if>
      <if test="responseMessage != null and responseMessage != ''">
        #{responseMessage,jdbcType=VARCHAR},
      </if>
      <if test="transferCode != null and transferCode != ''">
        #{transferCode,jdbcType=VARCHAR},
      </if>
      <if test="transferMessage != null and transferMessage != ''">
        #{transferMessage,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.infrastructure.bank.api.domain.entity.MinshengBankTransferRecord">
    <!--@mbg.generated-->
    update T_MINSHENG_BANK_TRANSFER_RECORD
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="payApplyId != null">
        PAY_APPLY_ID = #{payApplyId,jdbcType=INTEGER},
      </if>
      <if test="creditType != null and creditType != ''">
        CREDIT_TYPE = #{creditType,jdbcType=VARCHAR},
      </if>
      <if test="insId != null and insId != ''">
        INS_ID = #{insId,jdbcType=VARCHAR},
      </if>
      <if test="svrId != null and svrId != ''">
        SVR_ID = #{svrId,jdbcType=VARCHAR},
      </if>
      <if test="responseCode != null and responseCode != ''">
        RESPONSE_CODE = #{responseCode,jdbcType=VARCHAR},
      </if>
      <if test="responseMessage != null and responseMessage != ''">
        RESPONSE_MESSAGE = #{responseMessage,jdbcType=VARCHAR},
      </if>
      <if test="transferCode != null and transferCode != ''">
        TRANSFER_CODE = #{transferCode,jdbcType=VARCHAR},
      </if>
      <if test="transferMessage != null and transferMessage != ''">
        TRANSFER_MESSAGE = #{transferMessage,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
    </set>
    where ID = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.infrastructure.bank.api.domain.entity.MinshengBankTransferRecord">
    <!--@mbg.generated-->
    update T_MINSHENG_BANK_TRANSFER_RECORD
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      PAY_APPLY_ID = #{payApplyId,jdbcType=INTEGER},
      CREDIT_TYPE = #{creditType,jdbcType=VARCHAR},
      INS_ID = #{insId,jdbcType=VARCHAR},
      SVR_ID = #{svrId,jdbcType=VARCHAR},
      RESPONSE_CODE = #{responseCode,jdbcType=VARCHAR},
      RESPONSE_MESSAGE = #{responseMessage,jdbcType=VARCHAR},
      TRANSFER_CODE = #{transferCode,jdbcType=VARCHAR},
      TRANSFER_MESSAGE = #{transferMessage,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=INTEGER}
    where ID = #{id,jdbcType=BIGINT}
  </update>

<!--auto generated by MybatisCodeHelper on 2024-10-18-->
  <select id="findByPayApplyId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from T_MINSHENG_BANK_TRANSFER_RECORD
        where PAY_APPLY_ID=#{payApplyId,jdbcType=INTEGER}
    </select>

    <select id="batchFindByPayApplyIds" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from T_MINSHENG_BANK_TRANSFER_RECORD
      where PAY_APPLY_ID IN
      <foreach close=")" collection="payApplyIds" index="index" item="payApplyId" open="(" separator=",">
        PAY_APPLY_ID=#{payApplyId,jdbcType=INTEGER}
      </foreach>
    </select>
</mapper>