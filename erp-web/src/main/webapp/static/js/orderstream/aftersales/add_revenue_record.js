$(function() {
    $("#submit").click(function(){
        checkLogin();
        $(".warning").remove();
        $("input").removeClass("errorbor");

        if ($("#payer").val().length > 100){
            warnTips("payerError"," 支付方最多输入200字符，请检查后提交");
            return false;
        }
        var remark = $("#remark").val();
        if(remark.length > 200){
            warnTips("remarkError"," 备注最多输入200字符，请检查后提交");
            return false;
        }
        var amount = $("#amount").val();
        if (amount.length > 14){
            warnTips("amountError"," 金额最多输入14位字符，请检查后提交");
            return false;
        }
        var regPos = /^\d+(\.\d+)?$/; //非负浮点数
        var regNeg = /^(-(([0-9]+\.[0-9]*[1-9][0-9]*)|([0-9]*[1-9][0-9]*\.[0-9]+)|([0-9]*[1-9][0-9]*)))$/; //负浮点数

        if(!regPos.test(amount) && !regNeg.test(amount)) {
            warnTips("amountError"," 请输入数字字符，保留小数点后两位");
            return false;
        }
        /*根据数据库,限制14, 12整数,2小数*/
        var reg = /^[1-9]\d{0,11}(\.\d{1,2})?$|^0(\.\d{1,2})?$/;
        if (!reg.test(amount)){
            warnTips("amountError"," 请输入数字字符，保留小数点后两位,最多12位整数");
            return false;
        }
        /*var reg = /^[0-9]+.?[0-9]*$/;
        if(!reg.test(amount)){
            warnTips("amountError"," 请输入输入数字字符，保留小数点后两位");
            return false;
        }*/

        $.ajax({
            url:page_url+'/order/afterSalesCommon/saveRevenueRecord.do',
            data:$('#addRevenueRecord').serialize(),
            type:"POST",
            dataType : "json",
            async: false,
            success:function(data){
                if (null!=data && data.code != 0) {
                    layer.alert(data.message);
                }else {
                    $('#cancle').click();
                    parent.location.reload();
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
        return false;
    });
});

/**
 * 控制只能输入小数点后2位
 * @param obj
 */
function clearNoNum(obj) {
    obj.value = obj.value.replace(/[^\d.]/g, "");
    obj.value = obj.value.replace(/\.{2,}/g, ".");
    obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
    if (obj.value.indexOf(".") < 0 && obj.value != "") {
        obj.value = parseFloat(obj.value);
    }
}

function onlyNumber1(input, n) {
    var ret = "";
    var number =input.value;
    if (number != ""&& number!=null&&number!="0") {
        var unit = "仟佰拾亿仟佰拾万仟佰拾元角分",
        str = "";
        number += "00";
        var point = number.indexOf('.');
        if (point >= 0) {
            number = number.substring(0, point) + number.substr(point + 1, 2);
        }
        unit = unit.substr(unit.length - number.length);
        for (var i = 0; i < number.length; i++) {
            str += '零壹贰叁肆伍陆柒捌玖'.charAt(number.charAt(i)) + unit.charAt(i);
        }
        ret = str.replace(/零(仟|佰|拾|角)/g, "零").replace(/(零)+/g, "零")
            .replace(/零(万|亿|元)/g, "$1").replace(/(亿)万|(拾)/g, "$1$2")
            .replace(/^元零?|零分/g, "").replace(/元$/g, "元") + "整";
    }
    var a = document.getElementsByName("MoneyCapital")[0].id;
    document.getElementById(a).value=ret;
}