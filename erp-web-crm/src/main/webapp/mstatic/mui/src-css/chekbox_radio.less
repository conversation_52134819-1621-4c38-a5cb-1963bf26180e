@import (less) '../common.css';

.vd-ui-checkbox-group {
    
    &.is-margin- {
        margin-bottom: -10px;
    }

    .vd-ui-checkbox-item {
        margin-right: 20px;
        margin-bottom: 10px;

        &:last-child {
            margin-right: 0;
        }
    }
    
    .vd-ui-input-error {
        margin-top: -5px !important;
    }

    &.is-label {
        margin-bottom: -5px;
        
        .vd-ui-checkbox-item {
            margin-right: 5px;
            margin-bottom: 5px;
    
            &:last-child {
                margin-right: 0;
            }
        }    
    }
}

.vd-ui-checkbox-item {
    display: inline-block;
    cursor: pointer;
    color: @Text-4;
    vertical-align: top;

    * {
        box-sizing: border-box;
    }

    .vd-ui-checkbox-icon {
        position: relative;
        width: 16px;
        height: 16px;
        border-radius: 2px;
        border: 1px solid @Gray-9;
        margin-right: 5px;
        transition: all 0.1s;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 1px;

        .vd-ui-checkbox-icon-selected2 {
            width: 12px;
            height: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            transform: scale(0);
            transition: transform 0.1s;

            .icon-selected2 {
                font-size: 12px;
                color: @Text-7;
            }
        }
    }

    .vd-ui-checkbox-inner {
        display: flex;

        .strong {
            color: #f60;
        }
    }

    &.vd-ui-checkbox-item-checked {
        .vd-ui-checkbox-icon {
            background: @Brand-6;
            border-color: @Brand-6;

            .vd-ui-checkbox-icon-selected2 {
                transform: scale(1);
            }
        }
    }

    &.vd-ui-checkbox-item-progress {
        .vd-ui-checkbox-icon {
            background: @Brand-6;
            border-color: @Brand-6;

            .vd-ui-checkbox-icon-selected2 {
                transform: scale(1);

                .icon-selected2 {
                    display: inline-block;
                    width: 8px;
                    height: 2px;
                    background: #fff;
                    &::before {
                        display: none;
                    }
                }
            }
        }
    }

    &.vd-ui-checkbox-item-disabled {
        color: @Text-2;
        cursor: not-allowed;

        .vd-ui-checkbox-icon {
            border-color: @Gray-5;
            background: @Gray-2;
        }

        &.vd-ui-checkbox-item-checked {
            .vd-ui-checkbox-icon {
                border-color: @Gray-5;
                background: @Gray-5;
            }
        }
    }

    &.vd-ui-checkbox-item-labeltype {
        .vd-ui-checkbox-icon {
            display: none;
        }

        .vd-ui-checkbox-label {
            padding: 6px 10px;
            border-radius: 3px;
            background: #F5F7FA;
        }

        &.vd-ui-checkbox-item-checked {
            .vd-ui-checkbox-label {
                background: #09f;
                color: #fff;
            }
        }
    }

    &.single-row {
        display: block;
        padding: 10px;
        margin: 0;
        border-bottom: solid 1px #F5F7FA;

        &:last-child {
            border-bottom: none;
        }

        &.vd-ui-checkbox-item-checked {
            background: #E0F3FF;
        }
    }
}



.vd-ui-radio-group {
    margin-bottom: -10px;

    .vd-ui-radio-item {
        margin-right: 20px;
        margin-bottom: 10px;

        &:last-child {
            margin-right: 0;
        }
    }

    .vd-ui-input-error {
        margin-top: -5px !important;
    }

    &.is-label {
        margin-bottom: -5px;
        
        .vd-ui-radio-item {
            margin-right: 5px;
            margin-bottom: 5px;
    
            &:last-child {
                margin-right: 0;
            }
        }    
    }
}

.vd-ui-radio-item {
    display: inline-block;
    cursor: pointer;
    color: @Text-4;
    vertical-align: top;

    * {
        box-sizing: border-box;
    }

    .vd-ui-radio-icon {
        flex-shrink: 0;
        position: relative;
        width: 14px;
        height: 14px;
        border-radius: 50%;
        border: 1px solid @Gray-9;
        margin-right: 5px;
        transition: all 0.1s;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 2px;

        .vd-ui-radio-icon-selected {
            width: 0;
            height: 0;
            background: @Brand-6;
            transition: all .1s;
            border-radius: 50%;
        }
    }

    .vd-ui-radio-label {
        white-space: nowrap;
    }

    .vd-ui-radio-tip {
        color: #999;
    }

    .vd-ui-radio-inner {
        display: flex;
    }

    &.vd-ui-radio-item-checked {
        .vd-ui-radio-icon {
            border-color: @Brand-6;

            .vd-ui-radio-icon-selected {
                width: 6px;
                height: 6px;
            }
        }
    }

    &.vd-ui-radio-item-disabled {
        color: @Text-2;
        cursor: not-allowed;

        .vd-ui-radio-icon {
            border-color: @Gray-5;

            .vd-ui-radio-icon-selected {
                background: @Gray-5;
            }
        }
    }

    &.vd-ui-radio-item-labeltype {
        .vd-ui-radio-icon {
            display: none;
        }

        .vd-ui-radio-label {
            padding: 6px 10px;
            border-radius: 3px;
            background: #F5F7FA;
        }

        &.vd-ui-radio-item-checked {
            .vd-ui-radio-label {
                background: #09f;
                color: #fff;
            }
        }
    }
}
