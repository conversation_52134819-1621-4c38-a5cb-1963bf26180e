package com.vedeng.common.core.utils;

import cn.hutool.core.util.StrUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Description com.vedeng.common.core.utils
 * @Date 2022/9/5 14:52
 */
public class CustomUtils {

    public static Logger logger = LoggerFactory.getLogger(CustomUtils.class);

    /**
     * like查询
     */
    public static String like(String str) {
        if (StrUtil.isBlank(str)) {
            return null;
        }
        str = StrUtil.removeAll(str, "\\");
        str = StrUtil.removeAll(str, "%");
        str = StrUtil.removeAll(str, "_");
        StringBuilder stringBuffer = new StringBuilder(str);
        int length = str.length();
        for (int i = 0; i < length; i++) {
            stringBuffer.insert(i, "%");
            i++;
            length++;
        }
        stringBuffer.append("%");
        return stringBuffer.toString();
    }
    public static String hideMiddleFourDigits(String phoneNumber) {
        try {
            if (StringUtils.isBlank(phoneNumber)) {
                return "";
            }
            StringBuilder sb = new StringBuilder();
            for (char c : phoneNumber.toCharArray()) {
                if (Character.isDigit(c)) {
                    sb.append(c);
                }
            }
            if ( StringUtils.isBlank(sb.toString())) {
                return "";
            }
            StringBuilder result = new StringBuilder();
            for (int i = 0; i < sb.toString().toCharArray().length; i++) {
                if (i >= 3 && i < 7) {
                    result.append("*");
                } else {
                    result.append(sb.toString().toCharArray()[i]);
                }
            }
            return result.toString();
        }catch (Exception e){
            logger.warn("隐藏手机号错误",e);
        }
        return "****";
    }

}
