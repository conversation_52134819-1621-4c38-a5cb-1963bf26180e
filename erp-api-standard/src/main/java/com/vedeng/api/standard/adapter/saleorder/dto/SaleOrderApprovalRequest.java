package com.vedeng.api.standard.adapter.saleorder.dto;

import com.vedeng.api.standard.adapter.buyorder.dto.BaseBuyOrderRequest;
import com.vedeng.api.standard.approval.ApprovalRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 采销售审核请求
 * 专门用于审核操作，只包含审核相关字段
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SaleOrderApprovalRequest extends SaleOrderRequest implements ApprovalRequest {

    private static final long serialVersionUID = 1L;

    // ========== 审核核心字段 ==========
    /**
     * 任务ID（审核时必填）
     */
    private String taskId;

    /**
     * 审核通过标志（true-通过，false-拒绝）
     */
    private Boolean pass;

    /**
     * 审核备注/意见
     */
    private String comment;

    // ========== 审核详细信息 ==========
    /**
     * 提交备注
     */
    private String submitRemark;

    /**
     * 审核备注
     */
    private String approveRemark;

    /**
     * 拒绝原因
     */
    private String rejectReason;

    /**
     * 取消原因
     */
    private String cancelReason;

    /**
     * 删除原因
     */
    private String deleteReason;

    @Override
    public String getOperationType() {
        return "approve";
    }

    @Override
    public void validate() {
        super.validate();
        
        if (getSaleOrderId() == null) {
            throw new IllegalArgumentException("采购单ID不能为空");
        }
        
        if (taskId == null || taskId.trim().isEmpty()) {
            throw new IllegalArgumentException("任务ID不能为空");
        }
        
        if (pass == null) {
            throw new IllegalArgumentException("审核结果不能为空");
        }
        
        // 如果是拒绝，必须提供拒绝原因
        if (!pass && (rejectReason == null || rejectReason.trim().isEmpty())) {
            throw new IllegalArgumentException("拒绝审核时必须提供拒绝原因");
        }
    }

    /**
     * 创建用于审核通过的请求对象
     */
    public static SaleOrderApprovalRequest forApprove(Integer buyOrderId, String taskId, String approveRemark) {
        SaleOrderApprovalRequest request = new SaleOrderApprovalRequest();
        request.setSaleOrderId(request.getSaleOrderId());
        request.setTaskId(taskId);
        request.setPass(true);
        request.setApproveRemark(approveRemark);
        request.setComment(approveRemark);
        return request;
    }

    /**
     * 创建用于审核拒绝的请求对象
     */
    public static SaleOrderApprovalRequest forReject(Integer buyOrderId, String taskId, String rejectReason) {
        SaleOrderApprovalRequest request = new SaleOrderApprovalRequest();
        request.setSaleOrderId(request.getSaleOrderId());
        request.setTaskId(taskId);
        request.setPass(false);
        request.setRejectReason(rejectReason);
        request.setComment(rejectReason);
        return request;
    }

    /**
     * 创建用于提交审核的请求对象
     */
    public static SaleOrderApprovalRequest forSubmit(Integer buyOrderId, String submitRemark) {
        SaleOrderApprovalRequest request = new SaleOrderApprovalRequest();
        request.setSaleOrderId(request.getSaleOrderId());
        request.setSubmitRemark(submitRemark);
        return request;
    }
}
