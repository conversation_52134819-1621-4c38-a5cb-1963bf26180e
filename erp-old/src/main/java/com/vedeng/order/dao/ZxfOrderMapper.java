package com.vedeng.order.dao;

import com.newtask.model.SkuSaleNum;
import com.vedeng.authorization.model.User;
import com.vedeng.order.model.*;
import com.vedeng.order.model.coupon.CouponOrderData;
import com.vedeng.order.model.coupon.CouponOrderDetailData;
import com.vedeng.trader.group.model.GoodsQueryParam;
import com.vedeng.trader.group.model.SaleorderSum;
import com.vedeng.trader.model.Period;
import com.vedeng.trader.model.TraderOrderGoods;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 销售订单
 *
 * <AUTHOR>
 */
public interface ZxfOrderMapper {



	/**
	 * <b>Description:</b><br>
	 * 获取订单基本信息
	 *
	 * @param saleorderId
	 * @return
	 * @Note <b>Author:</b> leo.yang <br>
	 *       <b>Date:</b> 2017年7月5日 下午2:06:36
	 */
	Saleorder getBaseSaleorderInfo(Integer saleorderId);

	int insertSelective(Saleorder record);

	/**
	 * <b>Description:</b>获取一定时间段内下单天数和订单总额<br>
	 *
	 * @param
	 * @return
	 * @Note <b>Author:calvin</b>
	 * <br><b>Date:</b> 2019/10/30
	 */
	SaleorderCountResult getDaysCountSum(SaleorderCountParam param);

	/**
	 * <b>Description:</b>根据客户标识查询订单所属商品的sku<br>
	 *
	 * @param
	 * @return
	 * @Note <b>Author:calvin</b>
	 * <br><b>Date:</b> ${date} ${time}
	 */
	List<String> getOrderGoodsSkuByTraderId(TraderOrderGoods param);

	/**
	 * <b>Description:</b>根据订单时间段和TraderId查询订单个数<br>
	 *
	 * @param
	 * @return
	 * @Note <b>Author:calvin</b>
	 * <br><b>Date:</b> 2019/10/29
	 */
	Integer getSaleorderCountByTime(SaleorderCountParam saleorder);

	/**
	 * 查询销售订单id
	 * <b>Description:</b><br>
	 *
	 * @param request
	 * @param session
	 * @param saleorderId
	 * @return
	 * @Note <b>Author:</b> bill.bo
	 * <br><b>Date:</b> 2019年2月27日
	 */
	List<Integer> getSaleOrderIdListByParam(Map<String, Object> paraMap);

	/**
	 * 根据订单id查询订单信息
	 * <b>Description:</b><br>
	 *
	 * @param request
	 * @param session
	 * @param saleorderId
	 * @return
	 * @Note <b>Author:</b> bill.bo
	 * <br><b>Date:</b> 2019年2月27日
	 */
	List<Saleorder> getOrderListInfoById(Map<String, Object> paraMap);

	/**
	 * <b>Description:</b>查询销售订单合同回传相关信息
	 *
	 * @param contract
	 * @return List<SaleorderContract>
	 * @Note <b>Author：</b> chuck
	 * <b>Date:</b> 2019年3月5日 下午3:31:09
	 */
	List<SaleorderContract> getContractReturnOrderListPage(Map<String, Object> paraMap);

	/**
	 * <b>Description:</b>查询销售订单合同回传总数
	 *
	 * @param paraMap
	 * @return Integer
	 * @Note <b>Author：</b> chuck
	 * <b>Date:</b> 2019年3月12日 下午1:24:19
	 */
	Integer getContractReturnOrderListCount(Map<String, Object> paraMap);

	/**
	 * <b>Description:</b>查询销售订单合同回传不合格
	 *
	 * @param paraMap
	 * @return List<SaleorderContract>
	 * @Note <b>Author：</b> chuck
	 * <b>Date:</b> 2019年3月12日 下午1:25:03
	 */
	List<SaleorderContract> getContractReturnOrderNoqualityListPage(Map<String, Object> paraMap);

	/**
	 * <b>Description:</b>查询销售订单合同回传不合格总数
	 *
	 * @param paraMap
	 * @return Integer
	 * @Note <b>Author：</b> chuck
	 * <b>Date:</b> 2019年3月12日 下午1:25:28
	 */
	Integer getContractReturnOrderNoqualityListCount(Map<String, Object> paraMap);

	/* *
	 * 功能描述: 修改订单税率保存
	 * @param: [orderId, invoiceApplyId, invoiceType]
	 * @return: int
	 * @auther: duke.li
	 * @date: 2019/3/29 14:47
	 */
	int saveOrderRatioEdit(@Param(value = "orderId") Integer orderId, @Param(value = "invoiceType") Integer invoiceType);

	/**
	 * @Description: 根据参数查询耗材订单数据
	 * @Param: [saleorder]
	 * @return: java.util.List<com.vedeng.order.model.Saleorder>
	 * @Author: scott.zhu
	 * @Date: 2019/5/14
	 */
    List<Saleorder> getHcOrderList(Saleorder saleorder);

	/**
	 * <b>Description:</b><br>根据字段更新销售订单
	 *
	 * @param :[record]
	 * @return :int
	 * @Note <b>Author:</b> Michael <br>
	 * <b>Date:</b> 2019/5/21 7:58 PM
	 */
	int updateByPrimaryKeySelective(Saleorder record);

	/**
	 * @param @param  traderId
	 * @param @return 参数
	 * @return List<Saleorder>    返回类型
	 * @throws
	 * @Title: getSaleOrderlistByStatus
	 * @Description: TODO(获取客户订单状态带确认, 非审核中的订单)
	 * <AUTHOR>
	 * @date 2019年7月24日
	 */
	List<Saleorder> getSaleOrderlistByStatusTraderId(Integer traderId);

	/**
	 * @param @param  saleorderId
	 * @param @return 参数
	 * @return Saleorder    返回类型
	 * @throws
	 * @Title: getSaleOrderById
	 * @Description: TODO(订单id获取订单)
	 * <AUTHOR>
	 * @date 2019年7月24日
	 */
	Saleorder getSaleOrderById(Integer saleorderId);

	List<Saleorder> getSaleOrersByIdList(List<Integer> ids);

	/**
	 * @param @param  saleorder
	 * @param @return 参数
	 * @return Saleorder    返回类型
	 * @throws
	 * @Title: getSaleorderBySaleorderNo
	 * @Description: TODO(订单号获取订单信息)
	 * <AUTHOR>
	 * @date 2019年7月31日
	 */
	Saleorder getSaleorderBySaleorderNo(Saleorder saleorder);

	/**
	 * @param @param  status
	 * @param @param  orderType
	 * @param @return 参数
	 * @return List<Saleorder>    返回类型
	 * @throws
	 * @Title: getSaleorderListByStatus
	 * @Description: TODO(获取待用户确认状态BD订单)
	 * <AUTHOR>
	 * @date 2019年8月1日
	 */
	List<Saleorder> getSaleorderListByStatus(@Param(value = "status") Integer status, @Param(value = "orderType") Integer orderType);

	/**
	 * @param @param  createMobile
	 * @param @return 参数
	 * @return List<Saleorder>    返回类型
	 * @throws
	 * @Title: getSaleOrderlistByStatusMobile
	 * @Description: TODO(获取客户订单状态带确认, 非审核中的订单)
	 * <AUTHOR>
	 * @date 2019年8月8日
	 */
	List<Saleorder> getSaleOrderlistByStatusMobile(@Param(value = "createMobile") String createMobile);

	/**
	 * @Description: 获取订单编号
	 * @Param: [saleorder]
	 * @return: com.vedeng.order.model.Saleorder
	 * @Author: addis
	 * @Date: 2019/8/13
	 */
	List<Saleorder> selectSaleorderNo(Saleorder saleorder);

	List<GoodsData> getGoodsOccupyNumList(@Param("goodsIds") List<Integer> goodsIds);

	Integer getGoodsOccupyNum(@Param("goodsId") Integer goodsId);

	/**
	 * @Description: 查询订单里是否有该报价存在
	 * @Param: [quoteorderId]
	 * @return: java.lang.Integer
	 * @Author: addis
	 * @Date: 2019/8/30
	 */
	Integer isExistQuoteorderId(Integer quoteorderId);

	/**
	 * <b>Description:</b><br>
	 * 剩余账期未还金额
	 * add by Tomcat.Hui 2019/9/5 10:44 .Desc: VDERP-1053 .
	 *
	 * @param saleorderId
	 * @return
	 * @Note <b>Author:</b> Jerry <br>
	 * <b>Date:</b> 2017年10月16日 上午10:27:20
	 */
	BigDecimal getSaleorderLackAccountPeriodAmount(@Param("saleorderId") Integer saleorderId);

	/**
	 * <b>Description:</b><br>
	 * 获取订单已收款账期金额
	 * add by Tomcat.Hui 2019/9/5 10:44 .Desc: VDERP-1053 .
	 *
	 * @param saleorderId
	 * @return
	 * @Note <b>Author:</b> Jerry <br>
	 * <b>Date:</b> 2017年10月23日 上午11:06:47
	 */
	BigDecimal getPeriodAmount(@Param("saleorderId") Integer saleorderId);

	/**
	 * @param @param  orderNoList
	 * @param @return
	 * @return List<Saleorder>
	 * @throws
	 * @Description: TODO(订单号获取订单基本信息)
	 * <AUTHOR>
	 * @date 2019年10月12日
	 */
	List<Saleorder> getSaleorderBySaleorderNoList(ArrayList<String> orderNoList);

	/**
	 * @param @param  saleorder
	 * @param @return
	 * @return Integer
	 * @throws
	 * @Description: TODO(更改订单收货状态)
	 * <AUTHOR>
	 * @date 2019年10月14日
	 */
	Integer updateDeliveryStatusBySaleorderNo(Saleorder saleorder);

	/**
	 * 非备货非关闭订单id
	 *
	 * @Author:strange
	 * @Date:19:34 2019-11-11
	 */
	Integer getSaleorderidByStatus(Integer day);

	/**
	 * @Description: 根据订单id查询用户id
	 * @Param:
	 * @return:
	 * @Author: addis
	 * @Date: 2019/11/8
	 */
	Saleorder getWebAccountId(Integer saleorderId);

	/**
	 * 分页查询订单id
	 *
	 * @Author:strange
	 * @Date:01:20 2019-11-28
	 */
	List<Integer> getSaleorderidByStatusLimit(@Param("day") Integer day, @Param("limit") Integer limit);

	void updateSaleorderStatusById(@Param("idList") List<Integer> idList, @Param("modTime") Long modTime, @Param("userId") Integer userId);

	/**
	 * 根据订单号查询Id
	 *
	 * @param saleorderNo
	 * @return
	 * @Author:Rock
	 */
    Saleorder getSaleOrderId(String saleorderNo);


	/**
	 * 订单商品id获取订单信息
	 *
	 * @Author:strange
	 * @Date:17:36 2019-12-04
	 */
    Saleorder getSaleorderBySaleorderGoodsId(Integer saleorderGoodsId);

	/**
	 * 分页获取活动订单id
	 *
	 * @Author:strange
	 * @Date:15:43 2019-12-05
	 */
	List<Integer> getActionOrderLimit(@Param("day") Integer day, @Param("limit") Integer limit);

	/**
	 * 根据订单号取消订单（用于耗材商城订单状态同步）
	 * <b>Description:</b>
	 *
	 * @param saleorder
	 * @return Integer
	 * @Note <b>Author：</b> barry.xu
	 * <b>Date:</b> 2018年11月27日 上午10:05:32
	 */
    int updateOrderStatusByOrderNo(Saleorder saleorder);

	/**
	 * 获取所有订单活动id
	 *
	 * @Author:strange
	 * @Date:13:08 2019-12-23
	 */
	List<Integer> getAllActionId(Integer day);

	//第一次物流增加评论
    int updateLogisticsComments(@Param("saleorderId") Integer saleorderId, @Param("logisticsComments") String s);

    List<Saleorder> getSaleorderByExpressDetailId(@Param("list") List<Integer> list);

    Saleorder getSaleorderByOrderListId(Integer orderListId);

	List<SaleorderGoods> getSaleorderGoodsByOrderListId(List<Integer> orderListId);

	/**
	 * @param ownerUserId
	 * @return
	 */
    User getUserDetailInfoByUserId(Integer ownerUserId);

	User getUserInfoByTraderId(Integer traderId);

	/**
	 * 宝石花出库单列表
	 *
	 * @Author:strange
	 * @Date:16:58 2020-02-20
	 */
    List<Saleorder> getFlowerPrintOutListPage(Map<String, Object> map);

    int cleanSaleOrder();

	int cleanAfterSale();

	/**
	 * 更新updateDataTime
	 *
	 * @Author:strange
	 * @Date:11:37 2020-04-06
	 */
    Integer updateDataTimeByOrderId(Integer orderId);

	/**
	 * 更新updateDataTime
	 *
	 * @Author:strange
	 * @Date:11:37 2020-04-06
	 */
	Integer updateDataTimeByDetailId(Integer orderDetailId);

    int clearBussiness();

	/*校验锁的状态*/
	void updateLockedStatus(Integer saleorderId);

	/**
	 * 获取订单实付金额
	 *
	 * @return
	 * <AUTHOR>
	 * @Date 11:26 上午 2020/5/19
	 * @Param
	 **/
    BigDecimal getPaymentAmount(Integer orderId);

	/**
	 * 获取订单退款金额
	 *
	 * @return
	 * <AUTHOR>
	 * @Date 1:41 下午 2020/5/19
	 * @Param
	 **/
    BigDecimal getReturnAmount(Integer orderId);

	/**
	 * 更新订单金额信息
	 *
	 * @return
	 * <AUTHOR>
	 * @Date 3:52 下午 2020/5/19
	 * @Param
	 **/
	int updateAmountInfo(Saleorder saleorder);

	/**
	 * 获取近两天修改过的订单
	 *
	 * @return
	 * <AUTHOR>
	 * @Date 6:16 下午 2020/5/19
	 * @Param
	 **/
    List<Integer> getrecentDayOrder(@Param("num") Integer num);

	int getSaleorderidAll();

	List<Integer> getSaleorderidAllLimit(int i);

	/**
	 *   根据手机 获取6月16日 0 点以后的订单
	 * @param mobile
	 * @return
	 */
	List<Saleorder>  selectLatestSaleOrderByMobile(String mobile);

	int findByElorderNo(String orderNumber);


	/**
	 * <b>Description:</b>最近下单<br>
	 * @param
	 * @return
	 * @Note
	 * <b>Author:calvin</b>
	 * <br><b>Date:</b> 2020/5/27
	 */
	List<Integer> getTraderIdOrderedInPeriod(Period period);

	/**
	 * <b>Description:</b>最近购买<br>
	 * @param
	 * @return
	 * @Note
	 * <b>Author:calvin</b>
	 * <br><b>Date:</b> 2020/5/27
	 */
	List<Integer> getTraderIdBuyInPeriod(Period period);
	/**
	 * <b>Description:</b>下单商品<br>
	 * @param
	 * @return
	 * @Note
	 * <b>Author:calvin</b>
	 * <br><b>Date:</b> 2020/5/27
	 */
	List<Integer> getTraderIdsOrderGoods(GoodsQueryParam param);
	/**
	 * <b>Description:</b>购买商品<br>
	 * @param
	 * @return
	 * @Note
	 * <b>Author:calvin</b>
	 * <br><b>Date:</b> 2020/5/27
	 */
	List<Integer> getTraderIdsBuyGoods(GoodsQueryParam param);
	/**
	 * <b>Description:</b>成交金额<br>
	 * @param
	 * @return
	 * @Note
	 * <b>Author:calvin</b>
	 * <br><b>Date:</b> 2020/5/27
	 */
	List<Integer> getTraderIdsByAmount(SaleorderSum saleorderSum);
	/**
	 * <b>Description:</b>成交次数<br>
	 * @param
	 * @return
	 * @Note
	 * <b>Author:calvin</b>
	 * <br><b>Date:</b> 2020/5/27
	 */
	List<Integer> getTraderIdsByTimes(SaleorderSum saleorderSum);
	/**
	 * <b>Description:</b>最近成交<br>
	 * @param
	 * @return
	 * @Note
	 * <b>Author:calvin</b>
	 * <br><b>Date:</b> 2020/5/27
	 */
	List<Integer> getTraderIdsByDealRecently(SaleorderSum saleorderSum);

//	/**
//	 * 获取实付金额大于0的有效状态订单
//	 * <AUTHOR>
//	 * @Date 2:43 下午 2020/5/26
//	 * @Param
//	 * @return
//	 **/
//    List<Saleorder> getSaleorderRealAmountByTraderId(@Param("traderId")Integer traderId);

	/**
	 * 获取使用优惠券订单统计数据
	 * <AUTHOR>
	 * @Date 7:53 下午 2020/6/3
	 * @Param
	 * @return
	 **/
    CouponOrderData getCouponOrderDataByCouponId(Integer couponId);

	/**
	 * 获取使用优惠券订单明细数据
	 * <AUTHOR>
	 * @Date 7:54 下午 2020/6/3
	 * @Param
	 * @return
	 **/
	List<CouponOrderDetailData> getCouponOrderDataDetailListByCouponId(Integer couponId);

	List<Saleorder> getBDSaleOrderAndHasCoupon();
	/**
	 * 获取使用优惠券订单明细数据
	 * <AUTHOR>
	 * @Date 7:54 下午 2020/6/3
	 * @Param
	 * @return
	 **/
	List<CouponOrderDetailData> getCouponOrderDataDetailListBycouponCodeList(List<String> couponCodeList);

	List<SaleorderGoods> getSaleorderGoodsById(Integer saleorderId);


	List<Saleorder> getSaleorderByQuoteorderId(Integer quoteorderId);

	void updateNotSalesPerformanceOfSaleorder(Integer saleorderId);

	/**
	 * @description: 获取生效销售单未指定逻辑仓数据
	 * @return:  List<Integer>
	 * @author: Strange
	 * @date: 2020/7/29
	 **/
    List<Integer> getVaildOrderAndNoChooseOrderId();

    /**
     * <b>Description:</b>根据订单编号更新订单删除状态<br>
     * @param
     * @return
     * @Note
     * <b>@Author:calvin</b>
     * <br><b>@Date:</b> 2020/9/7
     */
    Integer updateIsDeleteByOrderNo(Saleorder saleorder);
	/**
	 * @description: 获取实付金额大于0的有效状态订单
	 * @return: List<Saleorder>
	 * @author: Strange
	 * @date: 2020/7/3
	 **/
	List<Saleorder> getSaleorderRealAmountByTraderId(Integer traderId);

	/**
	 * 根据发票ID获取票货同行订单信息
	 *
	 * @param invoiceIds
	 * @return
	 */
    List<Saleorder> getHcSaleorderInfoByInvoiceIds(@Param("invoiceIds") List<Integer> invoiceIds);

    List<String> findRelateSaleOrderNo(@Param("buyOrderGoodId") Integer buyOrderGoodId);

    List<Saleorder> getSaleorderRealAmountByTraderIdAndSaleorderId(Saleorder saleorder);

	/**
	 * @description: 获取订单实际金额
	 * @return: BigDecimal
	 * @author: Strange
	 * @date: 2020/9/24
	 **/
    BigDecimal getRealAmount(Integer saleorderId);
	/**
	 * <b>Description:</b> 订单付款信息修改（耗材商城）
	 *
	 * @param orderInfo
	 * @return int
	 * @Note <b>Author：</b> lijie <b>Date:</b> 2018年11月5日 下午3:19:42
	 */
    int updateSaleorderPayStatus(Saleorder orderInfo);

	/**
	 * 查询销售订单的销售部门是医械购及其子部门，且该订单是该客户非已关闭，且非全部退货的销售订单
	 * @param saleorder
	 * @return
	 */
    List<Saleorder> getHcOrgValidStatus(Saleorder saleorder);

	List<Saleorder> getHCSaleOrderAndHasCoupon();

	/**
	 * <b>Description:</b>分页获取bd订单，除去已完结和已关闭<br>
	 * @param
	 * @return
	 * @Note
	 * <b>@Author:calvin</b>
	 * <br><b>@Date:</b> 2020/9/28
	 */
	List<SimpleBdOrderChooseRes> getBdOrderListPage(Map<String, Object> param);

	/**
	 * @description: 获取订单账期未还金额
	 * @return: BigDecimal
	 * @author: Strange
	 * @date: 2020/11/4
	 **/
    BigDecimal getNoPayBackAmount(Integer orderId);

	List<Integer> getManageCategoryOfSkuBySaleorderId(Integer saleorderId);

	Saleorder getSaleorderByOrderNo(String saleorderNo);

	Saleorder getSaleorderBySaleorderId(Integer saleorderId);

	void updateContractUrlOfSaleorder(@Param("saleorderId") Integer saleorderId, @Param("contractUrl") String contractUrl);

	/**
	 * 获取客户需要更新合同模板的订单
	 * @param traderId 客户id
	 * @return 订单id集合
	 */
	List<Integer> getSaleorderOfNeedUpdateContract(Integer traderId);

	Integer getSaleorderIdByModifyApplyId(Integer saleorderModifyApplyId);

	/**
	 * <b>Description:</b>分页获取近一年的销量<br>
	 * @param
	 * @return
	 * @Note
	 * <b>@Author:calvin</b>
	 * <br><b>@Date:</b> 2020/11/14
	 */
	List<SkuSaleNum> getOneYearSkuSaleNumListPage(Map<String, Object> param);

    List<SaleorderModifyApply> getSaleorderModifyApplyList(Integer saleorderId);

	/**
	 * 获取订单中提交审核但未审核通过的sku数量
	 * @param saleorderId 订单id
	 * @return 数量
	 */
	List<String> getUncheckedSkuCountOfSaleorder(Integer saleorderId);

    void updateHcOrderDefaultValue(Saleorder updateSaleorderInfo);

    Saleorder getSalerOfSaleorderByOrderNo(String saleorderNo);

    List<Saleorder> getFinishOrderStatus(Saleorder searchSaleorder);

	/**
	 *获取订单的审核状态
	 * @param saleorderId 订单id
	 * @return 0审核中1审核通过2审核不通过,-1或3为待审核状态
	 */
	Integer getSaleordergetVerifyStatus(Integer saleorderId);

	/**
	 * 根据订单关联业务更新时间获取IDs
	 */
	List<Integer> getLastWeekIdsByUpdateDateTime(@Param("lastWeek") String lastWeek);

	/**
	 *  获取所有IdS
	 * @return
	 */
	List<Integer> getAllSaleOrderIds();

}

