package com.vedeng.infrastructure.logistics.api;

import com.vedeng.base.api.dto.kuaidi.KuaiDiReqDTO;
import com.vedeng.base.api.dto.kuaidi.LogisticsDTO;
import com.vedeng.base.api.dto.kuaidi.SubscribeResVO;
import com.vedeng.base.api.service.KuaiDiApiService;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.common.feign.constants.ServerConstants;
import feign.Headers;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 *
 */
@FeignApi(serverName = ServerConstants.BASE_SERVER)
public interface KuaiDiApi extends KuaiDiApiService {

    @Override
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /kuaiDiServices/getWaybillState")
    RestfulResult<LogisticsDTO> getWaybillState(@RequestBody KuaiDiReqDTO var1);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /kuaiDiServices/subscribe")
    @Override
    RestfulResult<SubscribeResVO.Body> subscribe(@RequestBody KuaiDiReqDTO kuaiDiReqDTO);
}
