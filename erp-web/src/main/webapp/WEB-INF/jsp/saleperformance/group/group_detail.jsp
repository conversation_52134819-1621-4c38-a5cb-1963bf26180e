<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<c:set var="title" value="团队详情页" scope="application" />	
<%@ include file="../../common/common.jsp"%>


	
	<div class="main-container" style='padding-top:15px;'>
		<input type="hidden" name="dataMonth" id="dataMonth" value="${dataMonth}">
		<div class="title-container">

			<label class="table-title nobor">历史月份:</label>
			&nbsp;&nbsp;
			<select class="input-middle" name="monthStrList" id="monthList">
				<c:forEach items="${monthStrList}" var="month">
					<option value="${month}" <c:if test="${month eq dataMonth}">selected="selected"</c:if>>${month}</option>
				</c:forEach>
			</select>
		</div>
			<div class="parts">
			
			
			<c:if test="${empty data}">
				<div class="title-container">
	                <div class="table-title nobor">
	                   	部门本月五行剑法概况
	                </div>
	            </div>
	            <table class="table">
	                <thead>
	                  <tr>
						  <th style="width:120px;">人员</th>
						  <th>本月目标（万）</th>
						  <th>本月业绩额（万）</th>
						  <th>本月完成度 （%）</th>
						  <th>业绩排名</th>
						  <th style="width: 146px;">小组合作客户数均值（近90天）</th>
						  <th>客户排名</th>
						  <th style="width: 81px;">小组本月BD客户数均值</th>
						  <th>BD客户数排名</th>
						  <th>小组本月询价转化率 (%)</th>
						  <th>询价转化率排名</th>
						  <th>小组综合得分均值</th>
						  <th>综合排名</th>
	                  </tr>
	                </thead> 
	            	<tr><td colspan="13">查询无结果</td></tr>
	            </table>
            </c:if>
            
            
            
			<c:if test="${!empty data}">
            	<c:forEach var="vo" items="${data}" >
		            <div class="title-container">
		                <div class="table-title nobor">
		                   	${vo.key.groupName}
		                </div>
		            </div>
		            <table class="table">
		            	<input type="hidden" class="groupIdClass" value="${vo.key.groupId}"/>
		                <thead>
		                  <tr>
							  <th style="width:120px;">人员</th>
							  <th>本月目标（万）</th>
							  <th>本月业绩额（万）</th>
							  <th>本月完成度 （%）</th>
							  <th>业绩排名</th>
							  <th style="width: 146px;">小组合作客户数均值（近90天）</th>
							  <th>客户排名</th>
							  <th style="width: 81px;">小组本月BD客户数均值</th>
							  <th>BD客户数排名</th>
							  <th>小组本月询价转化率 (%)</th>
							  <th>询价转化率排名</th>
							  <th>小组综合得分均值</th>
							  <th>综合排名</th>
		                    </tr>
		                </thead> 
	                	<c:if test="${empty vo.value }">
	                		<tr><td colspan="13">查询无结果</td></tr>
	                	</c:if>
	                	<c:if test="${!empty vo.value}">
		                	<tbody>
		                		<c:forEach var="dept" items="${vo.value}">
		                			<tr>
										<td class="font-blue">
											<a class="addtitle" href="javascript:void(0);"
											   tabtitle='{"num":"sales_five_pingtai_zhuguan_${dept.key.teamId}","link":"./kpi/query/queryTeamPageData.do?groupId=${vo.key.groupId }&teamId=${dept.key.teamId}","title":"小组详情页"}'>${dept.key.teamName }</a>
										</td>
				                		<td><fmt:formatNumber type="number" value="${dept.value.amountTarget}" pattern="0.00" maxFractionDigits="2" /></td>
				                		<td><fmt:formatNumber type="number" value="${dept.value.sumAmount / 10000}" pattern="0.00" maxFractionDigits="2" /></td>
										<td><fmt:formatNumber type="number" value="${dept.value.amountProgress * 100}" pattern="0.00" maxFractionDigits="2" />%</td>
										<td>${dept.value.amountSort}</td>
										<td><fmt:formatNumber type="number" value="${dept.value.aveCustomerNum}" pattern="0.00" maxFractionDigits="2" /></td>
				                		<td>${dept.value.customerSort}</td>
										<td><fmt:formatNumber type="number" value="${dept.value.aveBdCustomerNum}" pattern="0.00" maxFractionDigits="2" /></td>
				                		<td>${dept.value.bdCustomerSort}</td>
										<td><fmt:formatNumber type="number" value="${dept.value.aveTransProportion * 100}" pattern="0.00" maxFractionDigits="2" />%</td>
										<td>${dept.value.chanceSort}</td>
										<td><fmt:formatNumber type="number" value="${dept.value.integrateScore}" pattern="0.00" maxFractionDigits="2" /></td>
				                		<td>${dept.value.integrateSort}</td>
			                		</tr> 
		                		</c:forEach>
			                </tbody>
		                </c:if> 	
		            </table>
	            </c:forEach>
            </c:if>
        </div>
        
        <div class="table-friend-tip mb15" style="margin-top:0;">
                  
			说明：<br/>
			1. 本月目标、本月业绩额为各个小组内所有参与五行剑法计算的成员的本月目标总和、本月业绩额总和；本月完成度为小组业绩目标完成率<br/>
			2. 小组合作客户数均值（近90天）为各个小组内所有参与五行剑法计算的成员的近90天已合作客户数平均值<br/>
			3. 小组本月BD客户数均值为各个小组内所有参与五行剑法计算的成员的BD客户数平均值<br/>
			4. 小组本月询价转化率为各个小组内所有参与五行剑法计算的成员的询价转化率平均值<br/>
			5. 上表所有得分、排名均以小组为单位，将各个小组的平均成绩进行计算得出<br/>

		</div>
        
        <div class="parts" id="historyDatas1">
            <c:if test="${empty dataAll}">
                <div class="title-container">
                    <div class="table-title nobor">
                       	历史数据
                    </div>
                </div>
                <table class="table">
                    <thead>
                    <tr>
                        <th style="width:120px;">时间</th>
						<th>本月目标（万）</th>
						<th>本月业绩额（万）</th>
						<th>本月完成度 （%）</th>
						<th style="width: 146px;">小组合作客户数均值（近90天）</th>
						<th style="width: 81px;">小组本月BD客户数均值</th>
						<th>小组本月询价转化率 (%)</th>
                    </tr>
                    </thead>
                    <tr><td colspan="7">查询无结果</td></tr>
                </table>
            </c:if>

			<c:if test="${!empty dataAll}">
					<div class="title-container">
						<div class="table-title nobor">
							历史数据
						</div>
					</div>
					<table class="table">
						<thead>
						<tr>
							<th style="width:120px;">时间</th>
							<th>本月目标（万）</th>
							<th>本月业绩额（万）</th>
							<th>本月完成度 （%）</th>
							<th style="width: 146px;">小组合作客户数均值（近90天）</th>
							<th style="width: 81px;">小组本月BD客户数均值</th>
							<th>小组本月询价转化率 (%)</th>
						</tr>
						</thead>

						<c:if test="${!empty dataAll}">
							<tbody>
							<c:forEach var="dept" items="${dataAll}">
								<tr>
									<td>${dept.key}</td>
									<td><fmt:formatNumber type="number" value="${dept.value.amountTarget}" pattern="0.00" maxFractionDigits="2" /></td>
									<td><fmt:formatNumber type="number" value="${dept.value.sumAmount / 10000}" pattern="0.00" maxFractionDigits="2" /></td>
									<td><fmt:formatNumber type="number" value="${dept.value.amountProgress * 100}" pattern="0.00" maxFractionDigits="2" />%</td>
									<td><fmt:formatNumber type="number" value="${dept.value.aveCustomerNum}" pattern="0.00" maxFractionDigits="2" /></td>
									<td><fmt:formatNumber type="number" value="${dept.value.aveBdCustomerNum}" pattern="0.00" maxFractionDigits="2" /></td>
									<td><fmt:formatNumber type="number" value="${dept.value.aveTransProportion * 100}" pattern="0.00" maxFractionDigits="2" />%</td>
								</tr>
							</c:forEach>
							</tbody>
						</c:if>
					</table>
			</c:if>

        </div>
        <div class="table-friend-tip mb15" style="margin-top:0;">
                  
			说明：<br/>
			1. 本月目标、本月业绩额为部门内所有参与五行剑法计算的成员的本月目标总和、本月业绩额总和；本月完成度为部门业绩目标完成率<br/>
			2. 小组合作客户数均值（近90天）为部门内所有参与五行剑法计算的成员的近90天已合作客户数平均值<br/>
			3. 小组本月BD客户数均值为部门内所有参与五行剑法计算的成员的BD客户数平均值<br/>
			4. 小组本月询价转化率为部门内所有参与五行剑法计算的成员的询价转化率平均值<br/>
		</div>
	</div>
<script>
	$(function(){
		$("#monthList").change(function(){
			$("#dataMonth").val($(this).val());
			window.location.href='<%= basePath %>/kpi/query/queryGroupPageData.do?queryMonth=' + $(this).val();
		});

	})
</script>
<%--	<script type="text/javascript" src='<%= basePath %>static/js/saleperformance/group/group_detail.js?rnd=${resourceVersionKey}'></script>--%>
</body>
</html>