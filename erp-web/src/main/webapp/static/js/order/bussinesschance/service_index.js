$(function() {
	if($("#isRest").val()==1){
		reset();
	}
	//单个checkbox 联动全选
	$("input[name='bussinessChanceId']").click(function(){
		checkLogin();
		if(!$(this).prop("checked")){
			$("#selectAll").prop("checked",false);
		}else{
			var all = true;
			$.each($("input[name='bussinessChanceId']"),function(i,n){
				if(!$(n).prop("checked")){
					all = false;
				}
			});
			if(all){
				$("#selectAll").prop("checked",true);
			}
		}
	});

	$("select[name='newSource']").change(function(){
		checkLogin();
		var parentId = $(this).val();
		if(parentId > 0){
			$.ajax({
				type : "POST",
				url : "/order/bussinesschance/getRadioData.do",
				data :parentId,
				dataType : 'json',
				contentType: 'application/json;charset=utf-8',
				success : function(data) {
					$option = "<option value='0'>全部</option>";
					$.each(data.data,function(i,n){

						$option += "<option value='"+data.data[i]['sysOptionDefinitionId']+"'>"+data.data[i]['title']+"</option>";
					});
					$("select[name='newCommunication'] option:gt(0)").remove();

					$("select[name='newCommunication']").html($option);
				},
				error:function(data){
					if(data.code ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
		}else if(parentId==0){
			$("select[name='newCommunication'] option:gt(0)").remove();
		}
	});
});

function reset(){
	$("form").find("input[type='text']").val('');
	$("form").find(".hidden-reset").val('');
	$.each($("form select"),function(i,n){
		$(this).children("option:first").prop("selected",true);
	});
	//客户性质重置
	if($("select[name='customerProperty']") != undefined){
		$("select[name='customerProperty'] option:gt(0)").remove();
	}
	$("#cusProperty").val("");
	//地区市 区信息重置
	if($("select[name='city']") != undefined){
		$("select[name='city'] option:gt(0)").remove();
	}
	if($("select[name='zone']") != undefined){
		$("select[name='zone'] option:gt(0)").remove();
	}
	if($("select[name='newCommunication']") != undefined){
		$("select[name='newCommunication'] option:gt(0)").remove();
	}
}
//全选
function selectAll(obj){
	checkLogin();
	if($(obj).prop("checked")){
		$.each($("input[name='bussinessChanceId']"),function(){
			$(this).prop("checked",true);
		});
	}else{
		$.each($("input[name='bussinessChanceId']"),function(){
			$(this).prop("checked",false);
		});
	}
}
//批量分配
function assignAll(){
	checkLogin();
	if($("input[name='bussinessChanceId']:checked").length == 0){
		layer.alert("选择分配商机的条数不能为0");
		return false;
	}
	var userId = $("#toSaler").val();
	if(userId == 0){
		layer.alert("分配的销售不能为空");
		return false;
	}
	var ids = "";var nos = "";
	$.each($("input[name='bussinessChanceId']:checked"),function(i,n){
		if(i+1 != $("input[name='bussinessChanceId']:checked").length){
			ids += $(this).val() + ",";
			nos += $(this).attr("id") + ",";
		}else{
			ids += $(this).val();
			nos += $(this).attr("id");
		}
	});
	
	var index=layer.confirm("您是否确认该操作？", {
		  btn: ['确定','取消'] //按钮
		}, function(){
			$.ajax({
				type: "POST",
				url: page_url+"/order/bussinesschance/assignbussinesschance.do",
				data: {'userId':userId,'ids':ids,'nos':nos},
				dataType:'json',
				success: function(data){
					layer.close(index);
					if(data.code>=0){
						$.each($("input[name='bussinessChanceId']:checked"),function(i,n){
							$(this).parents("tr").find(".saleUser").html($("#toSaler option:selected").text());
							$(this).parents("tr").find(".bc-status").html("<span class=\"warning-color1\">未处理</span>");
							$(this).remove()
						});
					}else{
						alert("分配失败，"+data.message);
					}
					//refreshPageList(data)

				},
				error:function(){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}else{
						alert('操作失败');
					}
				}	
			});
		}, function(){
	});
}

function showOld(a){
	if (a) {
		$("#old-li").attr("style", "display:block;");
		$("#unshow-a").attr("style", "display:block;");
		$("#show-a").attr("style", "display:none;");
	} else {
		$("#old-li").attr("style", "display:none;");
		$("#unshow-a").attr("style", "display:none;");
		$("#show-a").attr("style", "display:block;");
	}
}

function exportList(){
	checkLogin();
	location.href = page_url + '/report/service/exportbussinesschancelist.do?' + $("#search").serialize();
}