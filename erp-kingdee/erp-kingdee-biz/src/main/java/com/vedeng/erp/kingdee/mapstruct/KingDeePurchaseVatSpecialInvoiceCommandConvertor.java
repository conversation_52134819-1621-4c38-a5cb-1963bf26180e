package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.domain.command.KingDeePurchaseVatSpecialInvoiceCommand;
import com.vedeng.erp.kingdee.dto.PurchaseVatSpecialInvoiceDetailDto;
import com.vedeng.erp.kingdee.dto.PurchaseVatSpecialInvoiceDetailLinkDto;
import com.vedeng.erp.kingdee.dto.PurchaseVatSpecialInvoiceDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 金蝶 采购专票 dto 转 command
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface KingDeePurchaseVatSpecialInvoiceCommandConvertor extends BaseCommandMapStruct<KingDeePurchaseVatSpecialInvoiceCommand,PurchaseVatSpecialInvoiceDto> {
    /**
     * KingDeePurchaseVatSpecialInvoiceCommand
     *
     * @param dto PurchaseVatSpecialInvoiceDto
     * @return KingDeePurchaseVatSpecialInvoiceCommand
     */
    @Mapping(target = "FID", source = "fid")
    @Mapping(target = "FDATE", source = "fdate")
    @Mapping(target = "FINVOICEDATE", source = "finvoicedate")
    @Mapping(target = "f_QZOK_BDDJTID", source = "FQzokBddjtid")
    @Mapping(target = "FINVOICENO", source = "finvoiceno")
    @Mapping(target = "f_QZOK_FPDM", source = "FQzokFpdm")
    @Mapping(target = "FSUPPLIERID.FNumber", source = "fsupplierid")
    @Mapping(target = "FDOCUMENTSTATUS", source = "fdocumentstatus")
    @Mapping(target = "FBillTypeID.FNumber", source = "FBillTypeID")
    @Mapping(target = "FSETTLEORGID.FNumber", source = "fsettleorgid")
    @Mapping(target = "FPURCHASEORGID.FNumber",source = "fpurchaseorgid")
    @Mapping(target = "FCancelStatus", source = "FCancelStatus")
    @Override
    KingDeePurchaseVatSpecialInvoiceCommand toCommand(PurchaseVatSpecialInvoiceDto dto);

    /**
     * PurchaseVatSpecialInvoiceDetailCommand
     *
     * @param dto PurchaseVatSpecialInvoiceDetailDto
     * @return PurchaseVatSpecialInvoiceDetailCommand
     */
    @Mapping(target = "FMATERIALID.FNumber", source = "fmaterialid")
    @Mapping(target = "FPRICEQTY", source = "fpriceqty")
    @Mapping(target = "FAUXTAXPRICE", source = "fauxtaxprice")
    @Mapping(target = "FTAXRATE", source = "ftaxrate")
    @Mapping(target = "FAMOUNTFOR", source = "famountfor")
    @Mapping(target = "FDETAILTAXAMOUNTFOR", source = "fdetailtaxamountfor")
    @Mapping(target = "FALLAMOUNTFOR", source = "fallamountfor")
    @Mapping(target = "f_QZOK_BDDJHID", source = "FQzokBddjhid")
    @Mapping(target = "f_QZOK_YSDDH", source = "FQzokYsddh")
    @Mapping(target = "f_QZOK_GSYWDH", source = "FQzokGsywdh")
    @Mapping(target = "f_QZOK_YWLX", source = "FQzokYwlx")
    @Mapping(target = "FSOURCETYPE", source = "fsourcetype")
    KingDeePurchaseVatSpecialInvoiceCommand.PurchaseVatSpecialInvoiceDetailCommand toCommand(PurchaseVatSpecialInvoiceDetailDto dto);

    /**
     * PurchaseVatSpecialInvoiceDetailLinkCommand
     *
     * @param dto PurchaseVatSpecialInvoiceDetailLinkDto
     * @return PurchaseVatSpecialInvoiceDetailLinkCommand
     */
    @Mapping(target = "FLinkId", source = "FLinkId")
    @Mapping(target = "FPURCHASEICENTRY_Link_FFlowId", source = "fpurchaseicentryLinkFflowid")
    @Mapping(target = "FPURCHASEICENTRY_Link_FFlowLineId", source = "fpurchaseicentryLinkFflowlineid")
    @Mapping(target = "FPURCHASEICENTRY_Link_FRuleId", source = "fpurchaseicentryLinkFruleid")
    @Mapping(target = "FPURCHASEICENTRY_Link_FSTableId", source = "fpurchaseicentryLinkFstableid")
    @Mapping(target = "FPURCHASEICENTRY_Link_FSTableName", source = "fpurchaseicentryLinkFstablename")
    @Mapping(target = "FPURCHASEICENTRY_Link_FSBillId", source = "fpurchaseicentryLinkFsbillid")
    @Mapping(target = "FPURCHASEICENTRY_Link_FSId", source = "fpurchaseicentryLinkFsid")
    @Mapping(target = "FPURCHASEICENTRY_Link_FBASICUNITQTYOld", source = "fpurchaseicentryLinkFbasicunitqtyold")
    @Mapping(target = "FPURCHASEICENTRY_Link_FBASICUNITQTY", source = "fpurchaseicentryLinkFbasicunitqty")
    @Mapping(target = "FPURCHASEICENTRY_Link_FALLAMOUNTFOROld", source = "fpurchaseicentryLinkFallamountforold")
    @Mapping(target = "FPURCHASEICENTRY_Link_FALLAMOUNTFOR", source = "fpurchaseicentryLinkFallamountfor")
    KingDeePurchaseVatSpecialInvoiceCommand.PurchaseVatSpecialInvoiceDetailLinkCommand toCommand(PurchaseVatSpecialInvoiceDetailLinkDto dto);
}
