package com.vedeng.todolist.dto;

import com.vedeng.todolist.model.OrganizationSelect;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class DailyManagementTodoDto {

    private List<OrganizationSelect> skuPushSelectList = new ArrayList();

    private List<OrganizationSelect> skuAndSpuSelectList = new ArrayList();

    private List<OrganizationSelect> receiptRecordSelectList = new ArrayList();

    private String skuAndSpuSubordinateList;

    private String skuPushSubordinateList;

    private String receiptRecordSubordinateList;

    /**
     * 按照等级划分，统计SPU/SKU待维护的待办事项数
     */
    private Map<Long, Long> skuAndSpuCountGroupByGrade = new HashMap<>();

    /**
     * 按照等级划分，统计待维护的预计可发货时间的待办数量
     */
    private Map<Long, Long> deliveryTimeCountGroupByGrade = new HashMap<>();

    /**
     * 按照等级划分，统计待维护核价的待办数量
     */
    private Map<Long, Long> priceCountGroupByGrade = new HashMap<>();

    /**
     * 待维护价格的sku集合，逗号分隔
     */
    private Map<Long, String> priceSkuList = new HashMap<>();

    /**
     * 按照等级划分，统计待维护售后政策的待办数量
     */
    private Map<Long, Long> aftersalePolicyCountGroupByGrade = new HashMap<>();

    /**
     * 按照等级划分，统计待维护供应商售后政策的待办数量
     */
    private Map<Long, Long> supplyAftersalePolicyCountGroupByGrade = new HashMap<>();

    /**
     * 按照等级划分，统计待维护的运营信息的待办数量
     */
    private Map<Long, Long> operationInfoCountGroupByGrade = new HashMap<>();

    /**
     * 按照分类划分，统计代录入同行单的待办数量
     */
    private Map<Long, Long> receiptRecordCountGroupByCategory = new HashMap<>();
}
