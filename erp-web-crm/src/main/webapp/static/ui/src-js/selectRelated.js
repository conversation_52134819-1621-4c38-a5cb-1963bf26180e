// 下拉联想 - 可输入
Vue.component('ui-select-related', {
    template: `
        <div class="vd-ui-select-related">
            <div class="vd-ui-input" :style="{'width': width}">
                <ui-input
                    :type="type"
                    :maxlength="maxlength"
                    :placeholder="placeholder"
                    :disabled="disabled"
                    v-bind="$attrs"
                    v-model="inputValue"

                    @focus="focus"
                    @blur="handlerBlur"
                    @input.native="handleInput"
                    @compositionend.native="commentPress"
                    width="100%"
                    autocomplete="off"
                    :errorable="!!errorMsg" 
                    :error-msg="errorMsg" 
                ></ui-input>
            </div>

            <transition>
                <ul
                    class="vd-ui-search-related"
                    @click.stop
                    v-show="rotate"
                    :style="{'width': width}"
                >
                    <template v-if="loading">
                        <li class="loading">
                            <i class="vd-ui_icon icon-loading" ref="loading"></i>
                            <span>加载中...</span>
                        </li>
                    </template>

                    <template v-else-if="loadingFail">
                        <li class="failed-li">
                            <i class="vd-ui_icon icon-error2"></i>
                            <span>加载失败</span>
                            <span class="reload" @click="handleReload">重新加载</span>
                        </li>
                    </template>

                    <template v-else>
                        <div class="search-list" v-if="relateList.length">
                            <div class="local-data">本地数据匹配</div>
                            <div 
                                class="sr-item" 
                                v-for="(item, index) in relateList" :key="index"
                                @click.stop="chooseItem(item)"
                            >
                                <div class="sr-item-left">
                                    <p class="text-line-1" v-html="light(item[remoteInfo.parseLabel])"></p>
                                </div>
                                <div class="sr-item-right">{{ item[remoteInfo.parseLabelRight] }}</div>
                            </div>
                        </div>
                        <li class="empty-li" v-else>
                            <p>暂无数据</p>
                        </li>
                    </template>
                </ul>
            </transition>
        </div>
    `,

    props: {
        value: {
            type: String,
            default: ''
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        width: {
            type: String,
            default: '300px'
        },
        // input类型
        type: {
            type: String,
            default: 'text'
        },
        placeholder: {
            type: String,
            default: ''
        },
        maxlength: {
            type: [String, Number],
            default: 5000
        },
        errorMsg: {
            type: String
        },

        
        // 已建档客户id： 有id才联想，否则只做简单输入
        traderId: {
            type: [String, Number],
            default: ''
        },
        remoteInfo: {
            type: Object,
            default: () => {
                return {}
            }
            // 可选参数
            // 请求路径    url
            // 请求方式    paramsMethod: 'POST'
            // 请求入参    paramsKey: 'name',
            // 面板label   parseLabel: 'username',
            // 面板value   parseValue: 'userId',
            // 面板头像字段 parseAvatar: 'aliasHeadPicture', 不传不展示
            // 是否需要分页 - 默认第一页100条 hasPage
        },
        // 接口取值层级 - 外层控制return需要展示的数组
        remoteDataParse: {
            type: Function,
            default: ()=> {}
        },
        // 需要展示头像
        avatar: {
            type: Boolean,
            default: false
        },
        // 右侧展示内容是否需要拼接多个字段
        rightKeys: {
            type: Array,
            default() {
                return [];
            }
        },
    },
    data() {
        return {
            inputValue: '',
            // erp本地匹配
            rotate: false,
            relateList: [], // 搜索联想
            loading: false,
            loadingFail: false,
            timer: null,
        }
    },
    watch: {
        value (newV) {
            this.inputValue = newV;
        },
        rotate (newV) {
            if (!newV) {
                this.loading = false;
                this.loadingFail = false;
            }
        }
    },
    computed: {
        light () {
            return (name) => {
                if (!this.inputValue) return name;
                const regExp = new RegExp(this.inputValue, 'g');
                name = name.replace(regExp, `<font color='#FF6600'">${this.inputValue}</font>`);
                return name;
            }
        },
        tags () {
            let arr = this.companyInfo.tags.split(';') || [];
            return arr
        },
        category_Big_Middle_Small () {
            let arr = [];
            this.companyInfo.category && arr.push(this.companyInfo.category); 
            this.companyInfo.categoryBig && arr.push(this.companyInfo.categoryBig); 
            this.companyInfo.categoryMiddle && arr.push(this.companyInfo.categoryMiddle); 
            this.companyInfo.categorySmall && arr.push(this.companyInfo.categorySmall);
            return arr.join(' / ')
        }
    },
    filters: {
        // 高亮
        filterLight (val) {
            if (!val) return '-';
            let year = new Date(val).getFullYear();
            let month = new Date(val).getMonth() + 1;
            let day = new Date(val).getDate();
            return `${year}-${month > 9? month: '0'+month}-${day > 9? day: '0'+day}`;
        },
    },
    mounted() {
        this.inputValue = this.value;
        document.addEventListener('click', (e)=>{
            if (!this.$el.contains(e.target)) {
                this.rotate = false;
            }
        })
    },
    methods: {
        focus () {
            // this.clearFlag = this.clearValue ? true : false;
            // this.getSearchList(this.inputValue);
        },
        handlerBlur () {
            this.$emit('blur');
        },

        // 输入停顿300毫秒后搜素
        handleInput (e) {
            if (e.inputType != 'insertCompositionText') {
                this.$emit('input', e.target.value);
                this.$emit('change', {});

                this.timer && clearTimeout(this.timer);
                this.timer = setTimeout(()=> {
                    this.getSearchList(e.target.value);
                }, 300);
            }
        },
        commentPress(e) {
            if (e.target.value) {
                this.$emit('input', e.target.value);

                this.timer && clearTimeout(this.timer);
                this.timer = setTimeout(()=> {
                    this.getSearchList(e.target.value);
                }, 300);
            }
        },
        getSearchList (str) {
            console.log('搜索', str);
            if (this.remoteInfo.url) {
                let value = str.trim();
                this.rotate = true;
                this.loading = true;
                this.loadingFail = false;

                let params = {};
                let url = this.remoteInfo.url;
                let reqMethod = this.remoteInfo.paramsMethod || 'post';

                if (this.remoteInfo.paramsType == 'url') { // get入参
                    if(this.remoteInfo.url.indexOf('?') !== -1) {
                        url += '&' + this.remoteInfo.paramsKey + '=' + value;
                    } else {
                        url += '?' + this.remoteInfo.paramsKey + '=' + value;
                    }
                } else { // post入参
                    params[this.remoteInfo.paramsKey] = value;

                    // 分页
                    if (this.remoteInfo.hasPage) {
                        params['pageNum'] = 1;
                        params['pageSize'] = 100;
                    }
                }


                let reqData = {
                    url, 
                    method: reqMethod,
                }

                if (reqMethod == 'get') {
                    reqData.params = params
                } else if (reqMethod == 'post') {
                    reqData.data = params;
                }

                // 增加timestramp时间戳 判断最新一次 ???
                this.$axios(reqData).then(({ data }) => {
                    if (data.success) {
                        let list = [];
                        let resList = data.data;

                        let remoteParseData = this.remoteDataParse(resList)
                        if (remoteParseData) {
                            resList = remoteParseData;
                        }

                        resList.forEach(item => {
                            let itemData = {
                                avatar: this.avatar ? item[this.remoteInfo.parseAvatar] : '',
                                ...item
                            }

                            if(this.rightKeys.length) {
                                let names = [];
                                this.rightKeys.forEach(key => {
                                    if(item[key]) {
                                        names.push(item[key])
                                    }
                                })
    
                                itemData.rightContent = names.join(' / ');
                            }
    
                            list.push(itemData);
                        });
                        this.relateList = list;
                        this.loading = false;
                    } else {
                        this.loadingFail = true;
                    }
                }).catch(err=> {
                    this.loading = false;
                    this.loadingFail = true;
                })
            }
        },

        // 重新加载
        handleReload () {
            this.getSearchList(this.inputValue);
        },

        // 选择已建档客户
        async chooseItem (item) {
            console.log('item：', item);

            this.inputValue = item[this.remoteInfo.parseLabel] || '';
            this.rotate = false;

            this.$emit("input", item[this.remoteInfo.parseValue] || '');
            this.$emit('change', Object.assign({}, item, {isChoosed: true}));
        },
    }
})