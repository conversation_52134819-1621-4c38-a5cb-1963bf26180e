<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.repository.mappers.KingDeeAllocationMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.domain.entity.KingDeeAllocationEntity">
    <!--@mbg.generated-->
    <!--@Table KING_DEE_ALLOCATION-->
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="F_ID" jdbcType="VARCHAR" property="fId" />
    <result column="F_BILL_NO" jdbcType="VARCHAR" property="fBillNo" />
    <result column="F_BILL_TYPE_ID" jdbcType="VARCHAR" property="fBillTypeId" />
    <result column="F_TRANSFER_DIRECT" jdbcType="VARCHAR" property="fTransferDirect" />
    <result column="F_TRANSFER_BIZ_TYPE" jdbcType="VARCHAR" property="fTransferBizType" />
    <result column="F_STOCK_OUT_ORG_ID" jdbcType="VARCHAR" property="fStockOutOrgId" />
    <result column="F_OWNER_TYPE_OUT_ID_HEAD" jdbcType="VARCHAR" property="fOwnerTypeOutIdHead" />
    <result column="F_STOCK_ORG_ID" jdbcType="VARCHAR" property="fStockOrgId" />
    <result column="F_DATE" jdbcType="VARCHAR" property="fDate" />
    <result column="F_QZOK_JDR" jdbcType="VARCHAR" property="fQzokJdr" />
    <result column="F_QZOK_BDDJTID" jdbcType="VARCHAR" property="fQzokBddjtid" />
    <result column="ID" jdbcType="INTEGER" property="id" />
    <result column="F_BILL_ENTRY"
            javaType="com.vedeng.erp.kingdee.dto.KingDeePurchaseReceiptDetailDto"
            property="fBillEntry"
            typeHandler="com.vedeng.common.mybatis.handler.CustomSqlJsonArrayHandler"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, F_ID, F_BILL_NO, 
    F_BILL_TYPE_ID, F_TRANSFER_DIRECT, F_TRANSFER_BIZ_TYPE, F_STOCK_OUT_ORG_ID, F_OWNER_TYPE_OUT_ID_HEAD, 
    F_STOCK_ORG_ID, F_DATE, F_QZOK_JDR, F_QZOK_BDDJTID, F_BILL_ENTRY, ID
  </sql>
  <insert id="insert" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeAllocationEntity">
    <!--@mbg.generated-->
    insert into KING_DEE_ALLOCATION (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      F_ID, F_BILL_NO, F_BILL_TYPE_ID, 
      F_TRANSFER_DIRECT, F_TRANSFER_BIZ_TYPE, F_STOCK_OUT_ORG_ID, 
      F_OWNER_TYPE_OUT_ID_HEAD, F_STOCK_ORG_ID, F_DATE, 
      F_QZOK_JDR, F_QZOK_BDDJTID, F_BILL_ENTRY, 
      ID)
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{fId,jdbcType=VARCHAR}, #{fBillNo,jdbcType=VARCHAR}, #{fBillTypeId,jdbcType=VARCHAR}, 
      #{fTransferDirect,jdbcType=VARCHAR}, #{fTransferBizType,jdbcType=VARCHAR}, #{fStockOutOrgId,jdbcType=VARCHAR}, 
      #{fOwnerTypeOutIdHead,jdbcType=VARCHAR}, #{fStockOrgId,jdbcType=VARCHAR}, #{fDate,jdbcType=VARCHAR}, 
      #{fQzokJdr,jdbcType=VARCHAR}, #{fQzokBddjtid,jdbcType=VARCHAR}, #{fBillEntry,jdbcType=VARCHAR}, 
      #{id,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeAllocationEntity">
    <!--@mbg.generated-->
    insert into KING_DEE_ALLOCATION
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="fId != null">
        F_ID,
      </if>
      <if test="fBillNo != null">
        F_BILL_NO,
      </if>
      <if test="fBillTypeId != null">
        F_BILL_TYPE_ID,
      </if>
      <if test="fTransferDirect != null">
        F_TRANSFER_DIRECT,
      </if>
      <if test="fTransferBizType != null">
        F_TRANSFER_BIZ_TYPE,
      </if>
      <if test="fStockOutOrgId != null">
        F_STOCK_OUT_ORG_ID,
      </if>
      <if test="fOwnerTypeOutIdHead != null">
        F_OWNER_TYPE_OUT_ID_HEAD,
      </if>
      <if test="fStockOrgId != null">
        F_STOCK_ORG_ID,
      </if>
      <if test="fDate != null">
        F_DATE,
      </if>
      <if test="fQzokJdr != null">
        F_QZOK_JDR,
      </if>
      <if test="fQzokBddjtid != null">
        F_QZOK_BDDJTID,
      </if>
      <if test="fBillEntry != null">
        F_BILL_ENTRY,
      </if>
      <if test="id != null">
        ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fId != null">
        #{fId,jdbcType=VARCHAR},
      </if>
      <if test="fBillNo != null">
        #{fBillNo,jdbcType=VARCHAR},
      </if>
      <if test="fBillTypeId != null">
        #{fBillTypeId,jdbcType=VARCHAR},
      </if>
      <if test="fTransferDirect != null">
        #{fTransferDirect,jdbcType=VARCHAR},
      </if>
      <if test="fTransferBizType != null">
        #{fTransferBizType,jdbcType=VARCHAR},
      </if>
      <if test="fStockOutOrgId != null">
        #{fStockOutOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fOwnerTypeOutIdHead != null">
        #{fOwnerTypeOutIdHead,jdbcType=VARCHAR},
      </if>
      <if test="fStockOrgId != null">
        #{fStockOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fDate != null">
        #{fDate,jdbcType=VARCHAR},
      </if>
      <if test="fQzokJdr != null">
        #{fQzokJdr,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBddjtid != null">
        #{fQzokBddjtid,jdbcType=VARCHAR},
      </if>
      <if test="fBillEntry != null">
        #{fBillEntry,jdbcType=VARCHAR},
      </if>
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into KING_DEE_ALLOCATION
    (ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, F_ID, F_BILL_NO, 
      F_BILL_TYPE_ID, F_TRANSFER_DIRECT, F_TRANSFER_BIZ_TYPE, F_STOCK_OUT_ORG_ID, F_OWNER_TYPE_OUT_ID_HEAD, 
      F_STOCK_ORG_ID, F_DATE, F_QZOK_JDR, F_QZOK_BDDJTID, F_BILL_ENTRY, ID)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, 
        #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, #{item.updaterName,jdbcType=VARCHAR}, 
        #{item.fId,jdbcType=VARCHAR}, #{item.fBillNo,jdbcType=VARCHAR}, #{item.fBillTypeId,jdbcType=VARCHAR}, 
        #{item.fTransferDirect,jdbcType=VARCHAR}, #{item.fTransferBizType,jdbcType=VARCHAR}, 
        #{item.fStockOutOrgId,jdbcType=VARCHAR}, #{item.fOwnerTypeOutIdHead,jdbcType=VARCHAR}, 
        #{item.fStockOrgId,jdbcType=VARCHAR}, #{item.fDate,jdbcType=VARCHAR}, #{item.fQzokJdr,jdbcType=VARCHAR}, 
        #{item.fQzokBddjtid,jdbcType=VARCHAR}, #{item.fBillEntry,jdbcType=VARCHAR}, #{item.id,jdbcType=INTEGER}
        )
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2022-12-16-->
  <select id="findByFBillNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from KING_DEE_ALLOCATION
    where F_BILL_NO=#{fBillNo,jdbcType=VARCHAR}
  </select>
</mapper>