<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>
        编辑商品风控信息
    </title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/suggestSelect.css?rnd=${resourceVersionKey}">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/pikaday.css?rnd=${resourceVersionKey}">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/pages/goods/vgoods/sku/sku_edit.css?rnd=${resourceVersionKey}">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/pages/goods/vgoods/sku/chosen.min.css?rnd=${resourceVersionKey}">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/select2.css?rnd=${resourceVersionKey}" />
    <style>
        .form-container .form-col {
            padding-right: 0;
        }
        .base-form .form-item {
            margin-bottom: 20px;
        }

        .display-inline {
            display: inline-block;
        }
    </style>
</head>

<body>
<form action="${pageContext.request.contextPath}/todolist/supplyChain/saveRiskSkuInfo.do" id="form_submit" class="J-form" method="POST">
    <input type="hidden" name="skuId" value="${coreSku.skuId}">
    <input type="hidden" name="spuId" value="${spu.spuId}">
    <input type="hidden" name="registrationNumberId" value="${registrationNumber.registrationNumberId}">
    <input type="hidden" name="firstEngageId" value="${firstEngage.firstEngageId }">
    <input type="hidden" name="productCompanyId" value="${productCompany.productCompanyId }">


    <div class="form-wrap">
        <div class="form-container base-form form-span-7">

            <%--首营信息--%>
                <div class="form-wrap">
                    <div class="form-container base-form form-span-8">
                        <div class="form-block">
                            <div class="J-optional-more show">
                                <div class="form-block-title">首营信息</div>
                                <div class="form-item">
                                    <div class="form-label">
                                        <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="28px">
                                        <span class="must">*</span>注册证号/备案凭证号：
                                    </div>
                                    <div class="form-fields">
                                        <div class="form-col col-6">
                                            <input type="text" name="registrationNumber" autocomplete="off"
                                                   placeholder="请输入注册证号/备案凭证号" class="input-text J-suggest-input" valid-max="128"
                                                   value="${registrationNumber.registrationNumber }">
                                            <input type="hidden" name="registrationNumberId"
                                                   value="${registrationNumber.registrationNumberId }">
                                            <input type="hidden" id="preId">
                                        </div>
                                        <div class="form-col">
                                            <span class="disappear" style="color: red" id="tip"></span>
                                        </div>
                                    </div>
                                </div>

                                <%--<div class="form-item">
                                    <div class="form-label">
                                        <img src="${pageContext.request.contextPath}/static/images/risk.png" width="28px">
                                        <span class="must">*</span>注册证/备案凭证类别：
                                    </div>
                                    <div class="form-fields">
                                        <div class="input-radio">
                                            <label class="input-wrap">
                                                <input type="radio" name="category"
                                                       <c:if test="${0 eq registrationNumber.category}">checked</c:if>
                                                       value="0">
                                                <span class="input-ctnr"></span>国产注册证
                                            </label>
                                            <label class="input-wrap">
                                                <input type="radio" name="category"
                                                       <c:if test="${2 eq registrationNumber.category}">checked</c:if>
                                                       value="2">
                                                <span class="input-ctnr"></span>进口注册证
                                            </label>
                                            <label class="input-wrap">
                                                <input type="radio" name="category"
                                                       <c:if test="${1 eq registrationNumber.category}">checked</c:if>
                                                       value="1">
                                                <span class="input-ctnr"></span>国产备案
                                            </label>
                                            <label class="input-wrap">
                                                <input type="radio" name="category"
                                                       <c:if test="${3 eq registrationNumber.category}">checked</c:if>
                                                       value="3">
                                                <span class="input-ctnr"></span>进口备案
                                            </label>
                                        </div>
                                        <div class="feedback-block" wrapfor="registrationNumber.category"></div>
                                    </div>
                                </div>--%>

                                <div class="form-item">
                                    <div class="form-label">
                                        <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="28px">
                                        <span class="must">*</span>注册人/备案人名称：
                                    </div>
                                    <div class="form-fields">
                                        <div class="form-col sa col-6">
                                            <input type="text" name="productCompanyChineseName"
                                                   class="input-text" valid-max="128" placeholder="请输入"
                                                   value="${productCompany.productCompanyChineseName }">
                                            <input type="hidden" name="productCompanyId"
                                                   class="input-text">
                                        </div>
                                    </div>
                                </div>

                                <div class="form-item">
                                    <div class="form-label">
                                        <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="28px">
                                        <span class="must">*</span>产品名称（注册证/备案凭证）：
                                    </div>
                                    <div class="form-fields">
                                        <div class="form-col sa col-6">
                                            <input type="text" name="productChineseName" valid-max="256"
                                                   placeholder="请输入注册证/备案凭证上的产品名称" class="input-text"
                                                   value="${registrationNumber.productChineseName }">
                                        </div>
                                    </div>
                                </div>

                                <div class="form-item">
                                    <div class="form-label">
                                        <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="28px">
                                        <span class="must">*</span>管理类别：
                                    </div>
                                    <div class="form-fields">
                                        <div class="input-radio">
                                            <c:set var="list" value="${sysOptionMap['1090']}"/>
                                            <c:forEach var="systemOption" items="${list }">
                                                <label class="input-wrap">
                                                    <c:if test="${systemOption.sysOptionDefinitionId eq registrationNumber.manageCategoryLevel }">
                                                        <input type="radio" name="manageCategoryLevel" checked
                                                               value="${systemOption.sysOptionDefinitionId }">
                                                    </c:if>
                                                    <c:if test="${systemOption.sysOptionDefinitionId ne registrationNumber.manageCategoryLevel }">
                                                        <input type="radio" name="manageCategoryLevel"
                                                               value="${systemOption.sysOptionDefinitionId }">
                                                    </c:if>
                                                    <span class="input-ctnr"></span>${systemOption.title }
                                                </label>
                                            </c:forEach>
                                        </div>
                                        <div class="feedback-block" wrapfor="manageCategoryLevel"></div>
                                    </div>
                                </div>

                                <div class="form-item">
                                    <div class="form-label">
                                        <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="28px">
                                        <span class="must">*</span>规格、型号（注册证/备案凭证）：
                                    </div>
                                    <div class="form-fields">
                                        <div class="form-col sa col-6">
                                <textarea  name="model" valid-max="1024"
                                           placeholder="请输入注册证/备案凭证上的规格型号，以“、”区分" class="input-textarea" >${registrationNumber.model}</textarea>
                                        </div>
                                    </div>
                                </div>


                                <div class="form-item" id="exd">
                                    <div class="form-label">
                                        <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="28px">
                                        <span class="must">*</span>批准日期/备案日期：</div>
                                    <div class="form-fields">
                                        <div class="form-col span-6 input-date">
                                            <input type="text" class="input-text J-date J-date-2"
                                                   name="issuingDateStr" placeholder="请选择" readonly
                                                   value="${registrationNumber.issuingDateStr }">
                                        </div>
                                    </div>
                                </div>

                               <div class="form-item J-inter-type">
                                    <div class="form-label">
                                        <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="28px">
                                        <span class="must">*</span>国标分类：
                                    </div>
                                   <div class="form-fields">
                                       <div class="input-radio">
                                           <label class="input-wrap">
                                               <c:if test="${firstEngage.standardCategoryType eq 2 }">
                                                   <input type="radio" name="standardCategoryType" checked value='2' >
                                               </c:if>
                                               <c:if test="${firstEngage.standardCategoryType ne 2 }">
                                                   <input type="radio" name="standardCategoryType" value='2' disabled="disabled">
                                               </c:if>
                                               <span class="input-ctnr"></span>旧国标
                                           </label>
                                           <div class="J-inter-old display-inline ignore" style="display: none">
                                               <div class="J-old-wrap"></div>
                                               <input type="hidden" name="oldStandardCategoryId" class="J-old-value"
                                                      value="<c:if test='${firstEngage.standardCategoryType eq 2 }'>${firstEngage.oldStandardCategoryId }</c:if>">
                                           </div>
                                           <label class="input-wrap">
                                               <c:if test="${firstEngage.standardCategoryType eq 1 }">
                                                   <input type="radio" name="standardCategoryType" checked value="1">
                                               </c:if>
                                               <c:if test="${firstEngage.standardCategoryType ne 1 }">
                                                   <input type="radio" name="standardCategoryType" value="1">
                                               </c:if>
                                               <span class="input-ctnr"></span>新国标
                                           </label>
                                           <div class="J-inter-new display-inline ignore" style="display: none">

                                               <div class="J-new-wrap"></div>
                                               <input type="hidden" name="newStandardCategoryId" class="J-new-value"
                                                      value="<c:if test='${firstEngage.standardCategoryType eq 1 }'>${firstEngage.newStandardCategoryId }</c:if>">

                                           </div>
                                       </div>
                                       <div class="feedback-block" wrapfor="standardCategoryType"></div>
                                       <div class="feedback-block" wrapfor="newStandardCategoryId"></div>
                                       <div class="feedback-block" wrapfor="oldStandardCategoryId"></div>
                                   </div>

                            <div class="form-item" style="margin-top: 20px">
                                <div class="form-label">
                                    <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="28px">
                                    <span class="must">*</span>注册证附件/备案凭证附件：
                                </div>
                                <div class="form-fields">
                                    <div class="form-col">
                                        <div class="J-upload"></div>
                                        <input type="hidden" class="J-upload-data" value='${zczMapList}'>
                                        <div class="form-fields-tip">-最多上传5张。</div>
                                        <div class="feedback-block" wrapfor="upload0"></div>
                                        <div class="feedback-block J-upload-error">
                                            <label class="error" for="" style="display: none;"></label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                                <div class="form-item one">
                                    <div class="form-label">
                                        <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="28px">
                                        <span class="must">*</span>是否委托生产：
                                    </div>
                                    <div class="form-fields">
                                        <div class="input-radio">
                                            <label class="input-wrap">
                                                <input type="radio" name="isSubcontractProduction"  disabled="disabled"
                                                       <c:if test="${1 eq registrationNumber.isSubcontractProduction}">checked</c:if>
                                                       value="1">
                                                <span class="input-ctnr"></span>是
                                            </label>
                                            <label class="input-wrap">
                                                <input type="radio" name="isSubcontractProduction" disabled="disabled"
                                                       <c:if test="${1 ne registrationNumber.isSubcontractProduction}">checked</c:if>
                                                       value="0">
                                                <span class="input-ctnr"></span>否
                                            </label>
                                        </div>
                                        <div class="feedback-block" wrapfor="isSubcontractProduction"></div>
                                    </div>
                                </div>

                                <div class="form-item">
                                    <div class="form-label">
                                        <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="28px">
                                        <span class="c-title" data-a="生产企业名称："
                                              data-b="受委托生产的企业名称">生产企业名称：</span>
                                    </div>
                                    <div class="form-fields">
                                            <select class="input-xx mr10 contact" name="manufacturerId" id="manufacturerId" >
                                                <option style="height: 30px" value=""   >请选择
                                                </option>
                                                <c:if test="${not empty manufacturerList}">
                                                    <c:forEach items="${manufacturerList}" var="rm" varStatus="status">
                                                        <option style="height: 30px" value="${rm.manufacturerId}" <c:if test="${rm.manufacturerId eq registrationNumber.manufacturerId}">selected</c:if>  >${rm.manufacturerName}
                                                        </option>
                                                    </c:forEach>
                                                </c:if>
                                            </select>

                                    </div>
                                </div>

                                <div class="form-item one ple">
                                    <div class="form-label">
                                        <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="28px">
                                        <span class="c-title" data-a="生产企业生产许可证或备案凭证编号："
                                                                  data-b="受委托生产企业生产许可证或备案凭证编号：">生产企业生产许可证或备案凭证编号：</span>
                                    </div>
                                    <div class="form-fields">
                                        <div class="form-col col-6">
                                            <input type="text" name="productCompanyLicence" id="productCompanyLicence"
                                                   class="input-text" valid-max="50"
                                                   value="${manufacturer.productCompanyLicence }">
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>

            <%--SPU--%>
            <div class="form-wrap">
                <div class="form-container base-form form-span-8">
                    <div class="form-block">
                        <div class="J-optional-more show">
                            <div class="form-block-title">SPU信息</div>
                            <div class="form-item">
                                <div class="form-label">
                                    <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="28px">
                                    <span class="must">*</span>是否在《医疗器械分类目录》：
                                </div>
                                <div class="form-fields">
                                    <div class="input-radio">
                                        <label class="input-wrap">
                                            <input type="radio" class="J-prod-type" name="medicalInstrumentCatalogIncluded" value="1"<c:if test="${spu.medicalInstrumentCatalogIncluded eq 1 }"> checked </c:if> >
                                            <span class="input-ctnr"></span>是
                                        </label>
                                        <label class="input-wrap">
                                            <input type="radio" class="J-prod-type" name="medicalInstrumentCatalogIncluded" value="0" <c:if test="${not empty spu.spuId and spu.medicalInstrumentCatalogIncluded eq 0 }"> checked </c:if>  >
                                            <span class="input-ctnr"></span>否
                                        </label>
                                    </div>
                                    <div class="feedback-block" wrapfor="medicalInstrumentCatalogIncluded"></div>
                                </div>
                            </div>


                            <div class="form-item">
                                <div class="form-label">
                                    <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="28px">
                                    <span class="must">*</span>是否有注册证/备案凭证：
                                </div>
                                <div class="form-fields">
                                    <div class="input-radio">
                                        <label class="input-wrap">
                                            <input type="radio" class="J-prod-type" name="hasRegistrationCert" disabled="disabled"   value="1" <c:if test="${hasRegistrationCert}">checked</c:if> >
                                            <span class="input-ctnr"></span>有
                                        </label>
                                        <label class="input-wrap">
                                            <input type="radio" class="J-prod-type" name="hasRegistrationCert" disabled="disabled"  value="0" <c:if test="${spu.spuId ne null and !hasRegistrationCert}">checked</c:if> >
                                            <span class="input-ctnr"></span>无
                                        </label>
                                    </div>
                                    <div class="feedback-block" wrapfor="hasRegistrationCert"></div>
                                </div>
                            </div>
                            <div class="form-item J-registration-name" >
                                <div class="form-label">
                                    <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="28px">
                                    <span class="must">*</span>产品名称（注册证/备案凭证）：
                                </div>
                                <div class="form-fields">
                                    <div class="form-col col-6">
                                        <input type="text" class="input-text J-common-name" placeholder="请输入注册证/备案证上的产品名称" name="spuName" value="${spu.spuName}">
                                    </div>
                                </div>
                            </div>
                            <div class="form-item J-registration-specs" >
                                <div class="form-label">
                                    <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="28px">
                                    <span class="must">*</span>规格、型号（注册证/备案凭证）：
                                </div>
                                <div class="form-fields">
                                    <div class="form-col col-6">
                                        <input type="text" class="input-text J-common-name" placeholder="请输入注册证/备案凭证上的规格型号，以“、”区分" name="specsModel" value="${spu.specsModel}">
                                    </div>
                                </div>
                            </div>

                            <div class="form-item">
                                <div class="form-label">
                                    <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="28px">
                                    <span class="must">*</span>是否厂家赋SN码：
                                </div>
                                <div class="form-fields">
                                    <div class="input-radio">
                                        <label class="input-wrap">
                                            <input type="radio" class="J-prod-type" name="isFactorySnCode" value="1">
                                            <span class="input-ctnr"></span>是
                                        </label>
                                        <label class="input-wrap">
                                            <input type="radio" class="J-prod-type" name="isFactorySnCode" value="0">
                                            <span class="input-ctnr"></span>否
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <%--SKU--%>
            <div class="form-block">
                <div class="J-optional-more show">
                    <div class="form-block-title">SKU信息</div>
                </div>
                <c:set var="goodsStorageConditionVo" value="${goodsStorageConditionVo}" scope="request"/>
                <c:set var="fromViewType" value="sku" scope="request"/>
                <jsp:include page="../goods/vgoods/commons/goodsStorageCondition.jsp" flush="true" ></jsp:include>

                <div class="J-optional-more show">

                    <div class="form-item">
                        <div class="form-label">
                            <span class="must">*</span>是否启用效期管理：
                        </div>
                        <div class="form-fields">
                            <div class="input-radio">
                                    <label class="input-wrap">
                                        <input type="radio" name="isEnableValidityPeriod" value="1" <c:if test="${ coreSku.isEnableValidityPeriod != null  }">disabled="disabled" </c:if> <c:if test="${ coreSku.isEnableValidityPeriod ==1  }"> checked </c:if> >
                                        <span class="input-ctnr"></span>是
                                    </label>
                                    <label class="input-wrap">
                                        <input type="radio" name="isEnableValidityPeriod" value="0" <c:if test="${ coreSku.isEnableValidityPeriod != null  }">disabled="disabled" </c:if> <c:if test="${ coreSku.isEnableValidityPeriod ==0  }"> checked </c:if> >
                                        <span class="input-ctnr"></span>否
                                    </label>
                                <input type="hidden" id="isEnableValidityPeriod" value="${coreSku.isEnableValidityPeriod }">
                            </div>
                            <div class="feedback-block" wrapfor="isEnableValidityPeriod"></div>

                        </div>
                    </div>

                    <div id="isEnableValidityPeriodDiv" style="display: none;">
                        <div class="form-item">
                            <div class="form-label">
                                <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                                <span class="must">*</span>有效期：
                            </div>
                            <div class="form-fields">
                                <div class="form-col col-2">
                                    <input type="text" autocomplete="false" class="input-text defaultZero" id="effectiveDays" name="effectiveDays" value="${coreSku.effectiveDays}">
                                </div>
                                <span class="unit">天</span>
                                <input type="hidden"   name="effectiveDayUnit" value="1">
                            </div>
                        </div>

                        <div class="form-item">
                            <div class="form-label"> 近效期预警天数：</div>
                            <div class="form-fields">
                                <span class="unit" id="nearTermWarnDaysSpan">${coreSku.nearTermWarnDays}天</span>
                                <input type="hidden"  name="nearTermWarnDays" id="nearTermWarnDays" value="${coreSku.nearTermWarnDays}">

                            </div>
                        </div>

                        <div class="form-item">
                            <div class="form-label"> 超近效期预警天数：</div>
                            <div class="form-fields">
                                <span class="unit" id="overNearTermWarnDaysSpan">${coreSku.overNearTermWarnDays}天</span>
                                <input type="hidden"  name="overNearTermWarnDays" id="overNearTermWarnDays" value="${coreSku.overNearTermWarnDays}">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-btn">
                <div class="form-fields">
                    <button type="button" class="btn btn-large close-spu-edit">取消</button>
                    <button type="submit" class="btn btn-blue btn-large" style="margin-left: 10px">提交</button>
                </div>
            </div>
        </div>
    </div>
</form>
<script type="text/tmpl" class="J-sort-tmpl">
        <div class="sort-item-wrap J-sort-item cf">
            <div class="sort-item col-1 item-center">
                <input type="text" name="sort" class="input-text J-sort-num" maxlength="2">
            </div>
            <div class="sort-item col-2">
                <input type="text" name="{{=itemName}}" autocomplete="off" valid-max="30" class="input-text J-sort-name" value="{{=name}}">
                <div class="feedback-block"></div>
            </div>
            <div class="sort-item col-3">
                <input type="text" name="{{=itemValue}}" autocomplete="off" valid-max="30" class="input-text J-sort-value" value="{{=value}}">
            </div>
            <div class="col-1">
                <i class="vd-icon icon-recycle J-sort-del"></i>
            </div>
        </div>
    </script>
<script type="text/tmpl" class="J-import-tmpl">
        <div class="import-wrap form-span-4 base-form">
            <div class="form-item">
                <div class="form-label">参数文本：</div>
                <div class="form-fields">
                    <div class="form-col col-11">
                        <textarea name="" id="" cols="30" rows="10" class="input-textarea J-import-cnt" placeholder='填写“属性1:属性值1;属性2:属性值2;属性3:属性值3;”将进行自动拆分'></textarea>
                    </div>
                </div>
            </div>
        </div>
    </script>
<script type="text/tmpl" class="J-shouquan-tmpl">
        <div class="form-fields J-shouquan-item" style="display: flex">
            <div class="fanwei-item">
                <div class="J-muiti-select fields-select-suggest"></div>
                <input type="hidden" class="J-value J-shouquan-input-1" name="region_value"
                       value="">
                <input type="hidden" class="J-shouquan-input-2" name="snowFlake_value"
                       value="">
                <select class="J-select" name="" id="">
                    <c:forEach items="${regions}" var="region">
    <option value="${region.regionId}">${region.regionName}</option>
</c:forEach>
                </select>
            </div>
            <div class="fanwei-item">
                <div class="J-muiti-select fields-select-suggest"></div>
                <input type="hidden" class="J-value J-shouquan-input-3" name="terminalType_value"
                       value="">
                <select class="J-select" name="terminalType">
                    <c:forEach items="${terminalTypes}" var="terminalType">
    <option value="${terminalType.sysOptionDefinitionId}">${terminalType.title}</option>
</c:forEach>
                </select>
            </div>
            <i class="vd-icon icon-recycle J-sort-del authorization-del"  style="display: inline;"></i>
            <div class="feedback-block" wrapfor="curingType"></div>
        </div>
    </script>


<script type="text/json" class="J-old-data">
        ${oldStandCategoryList}
</script>

<script type="text/json" class="J-new-data">
        ${newStandCategoryList}
</script>
<script src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/util.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/select.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/upload.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/pikaday.2.1.0.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/jquery.validate.js"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/artDialog/2.0.0/artDialog.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/js/todolist/editSkuInfo.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/suggestSelect.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/js/Sortable.min.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/js/chosen.jquery.js"></script>
<script src="${pageContext.request.contextPath}/static/new/js/pages/goods/commons/goodsStorageCondition.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/pages/goods/commons/goodsCommon.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src='${pageContext.request.contextPath}/static/libs/jquery/plugins/layer/layer.js'></script>
</body>

</html>