package com.vedeng.erp.finance.domain.entity;

import java.util.Date;
import lombok.Data;

/**
    * 银行别名表
    */
@Data
public class BankAliasEntity {
    /**
    * 主键
    */
    private Long bankReceiptAliasId;

    /**
    * 银行ID
    */
    private Integer bankId;

    /**
    * 别名
    */
    private String alias;

    /**
    * 是否删除 0否 1是
    */
    private Integer isDelete;

    /**
    * 创建时间
    */
    private Date addTime;

    /**
    * 修改时间
    */
    private Date modTime;

    /**
    * 添加人ID
    */
    private Integer creator;

    /**
    * 添加人名称
    */
    private String creatorName;

    /**
    * 更新人ID
    */
    private Integer updater;

    /**
    * 更新人名称
    */
    private String updaterName;

    /**
    * 更新备注
    */
    private String updateRemark;
}
