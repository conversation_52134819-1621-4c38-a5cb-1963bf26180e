package com.vedeng.erp.finance.web.api;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.base.ResultInfo;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.finance.dto.TaxClassificationDto;
import com.vedeng.erp.finance.dto.TaxcodeClassificationDto;
import com.vedeng.erp.finance.service.TaxClassificationService;
import com.vedeng.erp.finance.service.TaxcodeClassificationApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 税务编码分类接口
 */
@ExceptionController
@RestController
@Slf4j
@RequestMapping("/taxClassification/api")
public class TaxClassificationApi {
    @Autowired
    private TaxClassificationService taxClassificationService;
    @Autowired
    private TaxcodeClassificationApiService taxcodeClassificationApiService;


    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public ResultInfo<List<TaxcodeClassificationDto>> page(@RequestBody PageParam<TaxcodeClassificationDto> pageParam) {
        PageInfo<TaxcodeClassificationDto> pageList = taxClassificationService.findPageList(pageParam);
        ResultInfo<List<TaxcodeClassificationDto>> success = ResultInfo.success(pageList.getList());
        success.setCount(pageList.getTotal());
        return success;
    }

    /**
     * 根据编码获取税收分类
     */
    @RequestMapping(value = "/findByCode")
    @NoNeedAccessAuthorization
    public R<TaxcodeClassificationDto> findByCode(String code) {
        TaxcodeClassificationDto byCode = taxcodeClassificationApiService.findByCode(code);
        return R.success(byCode);
    }
}
