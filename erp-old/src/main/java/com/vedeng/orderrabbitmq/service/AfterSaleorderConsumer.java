package com.vedeng.orderrabbitmq.service;

import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.common.trace.mq.AbstractMessageListener;
import com.vedeng.order.model.vo.SaleAfterOrderVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @ClassName AfterSaleorderConsumer.java
 * @Description TODO 前台提交售后申请
 * @createTime 2021年02月24日 15:16:00
 */
@Component
public class AfterSaleorderConsumer extends AbstractMessageListener {

    public static final Logger logger = LoggerFactory.getLogger(AfterSaleorderConsumer.class);

    @Autowired
    private AfterSalesService afterSalesService;

    @Override
    public void doBusiness(Message message, Channel channel) throws Exception {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);

        logger.info("前台提交售后申请：{}",messageBody);

         try {
             SaleAfterOrderVo saleAfterOrderVo = JSONObject.parseObject(messageBody,SaleAfterOrderVo.class);

             if(saleAfterOrderVo == null){
                 throw new RuntimeException("前台提交售后申请为空");
             }

            afterSalesService.addApplySaleAfterOrder(saleAfterOrderVo);

             channel.basicAck(message.getMessageProperties().getDeliveryTag(),false);
            } catch (Exception e) {
             logger.error("AfterSaleorderConsumer 前台提交售后申请 error:",e);
             channel.basicNack(message.getMessageProperties().getDeliveryTag(),false,false);
         }
    }
}
