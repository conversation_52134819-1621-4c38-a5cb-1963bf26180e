package com.vedeng.erp.kingdee.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeeStorageOutCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeStorageOutEntity;
import com.vedeng.erp.kingdee.dto.KingDeeStorageOutDto;
import com.vedeng.erp.kingdee.dto.result.KingDeeStorageOutQueryResultDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeStorageOutCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeStorageOutConvertor;
import com.vedeng.erp.kingdee.repository.KingDeeStorageOutRepository;
import com.vedeng.erp.kingdee.service.KingDeeStorageOutApiService;
import com.vedeng.erp.kingdee.service.KingDeeStorageOutService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryExtParam;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryFilterDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶其他出入库Service实现
 * @date 2022/8/26 15:18
 */
@Service
@Slf4j
public class KingDeeStorageOutServiceImpl extends KingDeeBaseServiceImpl<
        KingDeeStorageOutEntity,
        KingDeeStorageOutDto,
        KingDeeStorageOutCommand,
        KingDeeStorageOutRepository,
        KingDeeStorageOutConvertor,
        KingDeeStorageOutCommandConvertor>
        implements KingDeeStorageOutService, KingDeeStorageOutApiService {


    @Override
    public List<KingDeeStorageOutQueryResultDto> getKingDeeStorageOut(String f_qzok_bddjtid, String f_qzok_bddjhid) {
        // 调用查询金蝶接口
        KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
        queryParam.setFormId(KingDeeFormConstant.STK_MIS_DELIVERY);
        List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
        queryFilterDtos.add(KingDeeQueryFilterDto.builder()
                .fieldName("F_QZOK_BDDJTID")
                .value(f_qzok_bddjtid).build());
        queryFilterDtos.add(KingDeeQueryFilterDto.builder()
                .fieldName("F_QZOK_BDDJHID")
                .value(f_qzok_bddjhid).build());
        queryParam.setFilterString(queryFilterDtos);
        log.info("金蝶销售出库单查询入参：{}", JSON.toJSONString(queryParam));
        return kingDeeBaseApi.query(queryParam, KingDeeStorageOutQueryResultDto.class);
    }
}
