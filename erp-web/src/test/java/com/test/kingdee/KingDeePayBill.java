package com.test.kingdee;

import io.swagger.models.auth.In;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@NoArgsConstructor
@Data
public class KingDeePayBill {


    /**
     * 单据内码非0：云星空系统单据FID值，表示修改
     */
    private Integer fid;
    /**
     * 单据编号
     */
    private String fBillNo;
    /**
     * 结算组织
     */
    private KingDeeNumber fBillTypeID = new KingDeeNumber();

    /**
     * 单据类型
     */
    private KingDeeNumber FSETTLEORGID = new KingDeeNumber();
    /**
     * 付款组织
     */
    private KingDeeNumber FPAYORGID = new KingDeeNumber();

    /**
     * 单据日期
     */
    private String fdate;
    /**
     * 往来单位类型
     */
    private String FCONTACTUNITTYPE;

    /**
     * 往来单位
     */
    private KingDeeNumber FCONTACTUNIT = new KingDeeNumber();
    /**
     * 收款单位类型
     */
    private String frectunittype;
    /**
     * 收款单位
     */
    private KingDeeNumber FRECTUNIT = new KingDeeNumber();
    /**
     * 结算币别
     */
    private KingDeeNumber fcurrencyid = new KingDeeNumber();
    /**
     * 汇率
     */
    private String fexchangerate;
    /**
     * 结算汇率
     */
    private String fsettlerate;
    /**
     * 业务类型
     */
    private String fbusinesstype;
    /**
     * 是否相同组织
     */
    private boolean fissameorg;
    /**
     * 是否信贷业务
     */
    private boolean fIsCredit;
    /**
     * 结算币别
     */
    private KingDeeNumber fsettlecur;
    /**
     * 是否转销
     */
    private boolean fIsWriteOff;
    /**
     * 实报实付
     */
    private boolean frealpay;
    /**
     * 备注
     */
    private String fremark;
    /**
     * 是否下推携带汇率到结算汇率
     */
    private boolean fiscarryrate;
    /**
     * 结算本位币
     */
    private KingDeeNumber fsettlemainbookid;
    /**
     * 多收款人
     */
    private boolean fMoreReceive;
    /**
     * 来源系统
     */
    private String fsourcesystem;
    /**
     * fpaybillentry
     */
    private List<FPAYBILLENTRY> fpaybillentry = new ArrayList<>();
    /**
     * fpaybillsrcentry
     */
    private List<FPAYBILLSRCENTRY> fpaybillsrcentry = new ArrayList<>();


    @NoArgsConstructor
    @Data
    public static class FPAYBILLENTRY {
        /**
         * 结算方式
         */
        private KingDeeNumber FSETTLETYPEID = new KingDeeNumber();
        /**
         * 付款用途
         */
        private KingDeeNumber fpurposeid;
        /**
         * 应付金额
         */
        private String fpaytotalamountfor;
        /**
         * 长短款
         */
        private String fovershortagefor;
        /**
         * 手续费
         */
        private String fhandlingchargefor;
        /**
         * 费用项目
         */
        private KingDeeNumber fcostid;
        /**
         * 费用承担部门
         */
        private KingDeeNumber fexpensedeptidE;
        /**
         * 我方银行账号
         */
        private KingDeeNumber faccountid = new KingDeeNumber();
        /**
         * 结算号
         */
        private String fsettleno;
        /**
         * 备注
         */
        private String fcomment;
        /**
         * 登账日期
         */
        private String fpostdate;
        /**
         * 入账类型
         */
        private String fRuZhangType;
        /**
         * 支付类型
         */
        private String fPayType;

    }


    @NoArgsConstructor
    @Data
    public static class FPAYBILLSRCENTRY {
        /**
         * fEntryID
         */
        private String fEntryID;
        /**
         * 源单类型
         */
        private String fsourcetype;
        /**
         * 源单编号
         */
        private String fsrcbillno;
        /**
         * 计划付款金额
         */
        private String fplanpayamount;
        /**
         * 本次付款金额
         */
        private String frealpayamountS;
        /**
         * 应付金额
         */
        private String fafttaxtotalamount;
        /**
         * 结算金额
         */
        private String fsettleamount;
        /**
         * 到期日
         */
        private String fexpiry;
        /**
         * 源单内码
         */
        private int fsrcbillid;
        /**
         * 源单行内码
         */
        private int fsrcrowid;
        /**
         * 源单行号
         */
        private String fsrcseq;
        /**
         * fsrcqty
         */
        private String fsrcqty;
        /**
         * 备注
         */
        private String fsrcremark;
        /**
         * 源单币别
         */
        private KingDeeNumber fsrccurrencyid;
        /**
         * fpaybillsrcentryLink
         */
        private List<FPAYBILLSRCENTRYLink> fpaybillsrcentryLink;


        /**
         * FPAYBILLSRCENTRYLink
         */
        @NoArgsConstructor
        @Data
        public static class FPAYBILLSRCENTRYLink {
            /**
             * 关联关系实体主键
             */
            private int fLinkId;
            /**
             * 转换规则
             */
            private String fpaybillsrcentryLinkFruleid;
            /**
             * 推进路线
             */
            private int fpaybillsrcentryLinkFflowlineid;
            /**
             * 源单表内码
             */
            private int fpaybillsrcentryLinkFstableid;
            /**
             * 源单表
             */
            private String fpaybillsrcentryLinkFstablename;
            /**
             * 源单内码
             */
            private int fpaybillsrcentryLinkFsbillid;
            /**
             * 源单分录内码
             */
            private int fpaybillsrcentryLinkFsid;
        }
    }


}
