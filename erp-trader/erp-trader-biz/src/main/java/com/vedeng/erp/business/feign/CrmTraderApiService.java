package com.vedeng.erp.business.feign;

import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.common.feign.constants.ServerConstants;
import com.vedeng.crm.admin.api.dto.customer.TradeInfoDto;
import com.vedeng.crm.admin.api.dto.tuoke.BusinessCluesDetailDTO;
import feign.Param;
import feign.RequestLine;

import java.util.List;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.business.feign
 * @Date 2022/7/25 19:29
 */
@FeignApi(serverName = ServerConstants.CRM_ADMIN_SERVER)
public interface CrmTraderApiService {

    /**
     * 根据traderId查询对应的中标信息
     *
     * @param traderId traderId
     * @return 中标信息list
     */
    @RequestLine("GET /api/leads/chance/getBidInfo?traderId={traderId}")
    RestfulResult<List<BusinessCluesDetailDTO>> getBidInfoByTraderId(@Param("traderId") Integer traderId);

    /**
     * 根据traderId查询客户历史交易信息
     *
     * @param traderId traderId
     * @return TradeInfoDto
     */
    @RequestLine("GET /api/leads/chance/getTraderInfoByTraderId?traderId={traderId}")
    RestfulResult<TradeInfoDto> getTraderInfoByTraderId(@Param("traderId") Integer traderId);

}
