package com.vedeng.trader.group.order;

import com.vedeng.common.util.DateUtil;
import com.vedeng.order.dao.QuoteorderMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.trader.group.GoodsBehavior;
import com.vedeng.trader.group.model.GoodsQueryParam;
import com.vedeng.trader.model.Period;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import java.util.BitSet;
import java.util.List;

/**
 * <b>Description:</b>下单商品但未付款行为<br>
 * @Note
 * <b>Author:calvin</b>
 * <br><b>Date:</b> 2020/5/25
 */
public class OrderGoodsBehavior extends GoodsBehavior {
    // 运行时注入Mapper
    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
    private SaleorderMapper saleorderMapper=(SaleorderMapper) context.getBean(SaleorderMapper.class);
    @Override
    public BitSet calculate() {
        BitSet enquirySet=new BitSet();
        GoodsQueryParam param=new GoodsQueryParam();
        Integer type=getChooseType();
        Period period= DateUtil.getStartAndEndPointForDays(getPeriod());
        param.setPeriod(period);
        if(type==0){
            param.setCategoryIds(getCategoryIds());
        }else if(type==1){
            param.setBrandId(getBrandId());
        }else if(type==2){
            param.setSkuList(getSkuList());
        }
        List<Integer> traderIds=saleorderMapper.getTraderIdsOrderGoods(param);
        if(CollectionUtils.isNotEmpty(traderIds)){
            for(Integer id:traderIds){
                if(id!=null){
                    enquirySet.set(id);
                }
            }
        }
        BitSet res=getBaseTraderSet();
        res.and(enquirySet);
        return res;
    }
}
