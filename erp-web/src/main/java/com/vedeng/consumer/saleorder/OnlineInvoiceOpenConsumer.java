package com.vedeng.consumer.saleorder;

import com.alibaba.fastjson.JSON;
import com.rabbitmq.client.Channel;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.trace.mq.AbstractMessageListener;
import com.vedeng.erp.finance.enums.CheckChainEnum;
import com.vedeng.erp.finance.service.InvoiceCheckApiService;
import com.vedeng.erp.saleorder.constant.OnlineBusinessTypeEnum;
import com.vedeng.erp.saleorder.dao.OnlineInvoiceMessageMapper;
import com.vedeng.erp.saleorder.exception.OnlineInvoiceOpenException;
import com.vedeng.erp.saleorder.model.dto.InvoiceOpenInfoDto;
import com.vedeng.erp.saleorder.model.po.OnlineInvoiceMessagePo;
import com.vedeng.erp.saleorder.service.OnlineInvoiceOpenService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.CompletableFuture;

/**
 * 在线开票消费者
 *
 * <AUTHOR>
 */
public class OnlineInvoiceOpenConsumer extends AbstractMessageListener {

    public static final Logger logger = LoggerFactory.getLogger(OnlineInvoiceOpenConsumer.class);


    @Autowired
    private OnlineInvoiceOpenService onlineInvoiceOpenService;

    @Resource
    private OnlineInvoiceMessageMapper onlineInvoiceMessageMapper;

    @Autowired
    InvoiceCheckApiService invoiceCheckApiService;

    @Override
    public void doBusiness(Message message, Channel channel) throws Exception {

        String jsonString = new String(message.getBody(), StandardCharsets.UTF_8);

        logger.info("在线开票信息推送 invoiceOpenInfo:{}", jsonString);

        if (StringUtils.isBlank(jsonString)) {
            logger.error("要处理，在线开票前台消息数据异常 jsonString:{}", jsonString);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            return;
        }

        InvoiceOpenInfoDto invoiceOpenInfoDto = null;
        try {
            invoiceOpenInfoDto = JSON.parseObject(jsonString, InvoiceOpenInfoDto.class);
        } catch (Exception e) {
            logger.error("在线开票接收报文错误", e);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        }

        OnlineInvoiceMessagePo onlineInvoiceMessagePo = new OnlineInvoiceMessagePo();
        onlineInvoiceMessagePo.setBusinessKey(OnlineBusinessTypeEnum.ONLINE_INVOICE_OPEN.getCode());
        onlineInvoiceMessagePo.setMessageBody(jsonString);
        onlineInvoiceMessagePo.setAddTime(System.currentTimeMillis());
        onlineInvoiceMessagePo.setMessageId(invoiceOpenInfoDto.getMessageId());

        if (StringUtils.isBlank(invoiceOpenInfoDto.getMessageId())) {
            logger.error("在线开票消息ID为空异常，中断处理 jsonString:{}", jsonString);
            onlineInvoiceMessagePo.setConsumeResult(ErpConst.TWO);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            return;
        }


        try {
            onlineInvoiceOpenService.dealDataBeforeInvoiceApply(invoiceOpenInfoDto);
            onlineInvoiceOpenService.dealInvoiceOpenBusiness(invoiceOpenInfoDto);

            try {
                //校验开票规则
                invoiceCheckApiService.refreshInvoiceApplyCheck(CheckChainEnum.INVOICE_OPEN_SALES, invoiceOpenInfoDto.getInvoiceApplyId());
            } catch (Exception e) {
                logger.error("异步校验开票规则异常", e);
            }

            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            onlineInvoiceMessagePo.setConsumeResult(ErpConst.ONE);
        } catch (OnlineInvoiceOpenException e) {
            logger.warn("在线开票处理业务异常，已下传前台 jsonString:{},e:{}", jsonString, e.getMessage());
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            onlineInvoiceMessagePo.setConsumeResult(ErpConst.TWO);
        } catch (RuntimeException e) {
            logger.warn("在线开票消息问题异常 jsonString:{}", jsonString);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            onlineInvoiceMessagePo.setConsumeResult(ErpConst.TWO);
        } catch (Exception e) {
            logger.error("在线开票ERP本地处理异常异常,将进行补偿处理 jsonString:{},e:{}", jsonString, e.getMessage());
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            onlineInvoiceMessagePo.setConsumeResult(ErpConst.ZERO);
        } finally {
            try {
                OnlineInvoiceMessagePo messageInfoByMessageId = onlineInvoiceMessageMapper.getMessageInfoByMessageId(invoiceOpenInfoDto.getMessageId());
                if (messageInfoByMessageId == null) {
                    onlineInvoiceMessageMapper.insert(onlineInvoiceMessagePo);
                } else {
                    onlineInvoiceMessageMapper.updateOnlineInvoiceMessage(messageInfoByMessageId.getId(), onlineInvoiceMessagePo.getConsumeResult(), System.currentTimeMillis());
                }
            } catch (Exception e) {
                logger.error("保存在线开票消息记录异常 onlineInvoiceMessagePo:{},e:{}", JSON.toJSONString(onlineInvoiceMessagePo), e);
            }
        }

    }
}
