package com.vedeng.infrastructure.bank.api.domain.api;

import com.vedeng.infrastructure.bank.api.domain.B2eBasicDraftApplyReq;
import com.vedeng.infrastructure.bank.api.domain.base.CMBCRequest;
import com.vedeng.infrastructure.bank.api.enums.BankApiType;

import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlSeeAlso;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 融资预申请
 * @date 2024/10/12 11:26
 */
@XmlRootElement(name = "CMBC")
@XmlSeeAlso({B2eBasicDraftApplyReq.class})
public class B2eBasicDraftApplyApiReq extends CMBCRequest<B2eBasicDraftApplyReq> {

    private static final String API_CODE = BankApiType.B2E_BASIC_DRAFT_APPLY.getTrnCode();

    public B2eBasicDraftApplyApiReq() {
        initTrnCode();
    }

    public B2eBasicDraftApplyApiReq(B2eBasicDraftApplyReq xDataBody) {
        super.setXDataBody(xDataBody);
        initTrnCode();
    }

    @Override
    protected void initTrnCode() {
        setTrnCode(API_CODE);
    }
}
