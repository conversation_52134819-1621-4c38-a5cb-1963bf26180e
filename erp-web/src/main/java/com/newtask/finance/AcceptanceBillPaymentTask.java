package com.newtask.finance;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.finance.dto.PayApplyDto;
import com.vedeng.erp.finance.dto.PayVedengBankDto;
import com.vedeng.erp.finance.service.PayApplyApiService;
import com.vedeng.erp.finance.service.PayVedengBankApiService;
import com.vedeng.erp.settlement.mapper.PayApplyMapper;
import com.vedeng.finance.model.CapitalBill;
import com.vedeng.finance.model.CapitalBillDetail;
import com.vedeng.infrastructure.bank.api.domain.B2eQueryBasicDraftResultReq;
import com.vedeng.infrastructure.bank.api.domain.B2eQueryBasicDraftResultRes;
import com.vedeng.infrastructure.bank.api.service.AcceptanceBillApiService;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.vo.BuyorderVo;
import com.vedeng.order.service.BuyorderService;
import com.vedeng.system.service.UserService;
import com.vedeng.system.service.VerifiesRecordService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.TaskService;
import org.activiti.engine.task.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 承兑汇票提交结果更新-基于付款单
 * @date 2024/10/21 13:34
 */
@JobHandler(value = "AcceptanceBillPaymentTask")
@Component
@Slf4j
public class AcceptanceBillPaymentTask extends AbstractJobHandler {

    @Autowired
    private BuyorderService buyorderService;
    @Autowired
    private UserService userService;
    @Autowired
    private PayApplyMapper payApplyMapper;
    @Autowired
    private PayApplyApiService payApplyApiService;
    @Autowired
    private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
    @Autowired
    private ActionProcdefService actionProcdefService;
    @Autowired
    private VerifiesRecordService verifiesRecordService;
    @Autowired
    private PayVedengBankApiService payVedengBankApiService;
    @Autowired
    private com.vedeng.finance.service.CapitalBillService oldCapitalBillService;
    @Autowired
    private AcceptanceBillApiService acceptanceBillApiService;


    @Value("${custAccount}")
    private String custAccount;

    public static final String END_EVENT = "endEvent";


    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        List<PayApplyDto> payApplyDtoList = payApplyMapper.findAllByValidStatusAndPayStatus();
        log.info("查询到待处理付款申请: {}", JSON.toJSONString(payApplyDtoList));
        if (CollUtil.isNotEmpty(payApplyDtoList)) {
            this.processPaymentOrder(payApplyDtoList);
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 处理付款订单
     */
    private void processPaymentOrder(List<PayApplyDto> payApplyDtoList) {
        log.info("处理付款订单，付款申请:{}", JSON.toJSONString(payApplyDtoList));

        payApplyDtoList.forEach(payApplyDto -> {
            B2eQueryBasicDraftResultReq queryReq = new B2eQueryBasicDraftResultReq();
            queryReq.setTrnId(IdUtil.simpleUUID());
            queryReq.setApplicationNo(payApplyDto.getPayApplyId().toString());

            B2eQueryBasicDraftResultRes resultRes = acceptanceBillApiService.queryDraftResult(queryReq);
            if (resultRes.isFail()) {
                log.error("融资申请结果查询失败，响应: {}", JSON.toJSONString(resultRes));
                return;
            }

            List<B2eQueryBasicDraftResultRes.B2eQueryBasicDraft> draftList = resultRes.getXDataBody().getList();
            if (CollUtil.isEmpty(draftList)) {
                log.error("未找到融资申请结果，付款申请ID: {}", payApplyDto.getPayApplyId());
                return;
            }

            B2eQueryBasicDraftResultRes.B2eQueryBasicDraft draft = CollUtil.getFirst(draftList);
            handleDraftStatus(draft, payApplyDto);
        });
    }


    private void handleDraftStatus(B2eQueryBasicDraftResultRes.B2eQueryBasicDraft draft, PayApplyDto payApplyDto) {
        String status = draft.getStatus();
        String auditMessage = draft.getAuditMessage();
        Integer payApplyId = payApplyDto.getPayApplyId();

        switch (status) {
            case "S":
                log.info("审批通过，付款申请ID: {}, 审核信息: {}", payApplyId, auditMessage);
                audit(payApplyDto, auditMessage);
                break;
            case "E":
                log.info("审批拒绝，付款申请ID: {}, 审核信息: {}", payApplyId, auditMessage);
                unAudit(payApplyDto, auditMessage);
                break;
            case "R":
                log.info("审批中，付款申请ID: {}", payApplyId);
                break;
            default:
                log.warn("未知的审批状态: {}, 付款申请ID: {}", status, payApplyId);
        }
    }

    private void audit(PayApplyDto payApplyDto, String auditMessage) {
        Integer payApplyId = payApplyDto.getPayApplyId();
        // 审核操作
        this.auditTask(payApplyId, true, auditMessage);
        // 更新付款审核状态
        payApplyApiService.updateValidStatus(1, payApplyId);
        payApplyApiService.updatePayStatusBill(1, payApplyId);

        // 处理付款申请后续操作
        this.processBuyOrderPayApply(payApplyDto);
    }

    private void unAudit(PayApplyDto payApplyDto, String auditMessage) {
        auditTask(payApplyDto.getPayApplyId(), false, auditMessage);
        payApplyApiService.updateValidStatus(2, payApplyDto.getPayApplyId());
        actionProcdefService.updateInfo("T_BUYORDER", "BUYORDER_ID", payApplyDto.getRelatedId(), "LOCKED_STATUS", 0, 2);
    }


    /**
     * 审核任务
     *
     * @param payApplyId 付款申请ID
     */
    private void auditTask(Integer payApplyId, boolean pass, String msg) {
        try {

            String processInstanceBusinessKey = "paymentVerify_" + payApplyId;
            TaskService taskService = processEngine.getTaskService();
            Task taskInfoPay = taskService.createTaskQuery()
                    .processInstanceBusinessKey(processInstanceBusinessKey)
                    .singleResult();
            Integer status = pass ? 0 : 2;
            if (taskInfoPay != null && StrUtil.isNotEmpty(taskInfoPay.getId())) {
                String taskId = taskInfoPay.getId();
                log.info("处理审核任务号：{}", taskId);
                Map<String, Object> variables = new HashMap<>();
                variables.put("pass", pass);
                verifiesRecordService.saveVerifiesInfo(taskId, status);
                ResultInfo<?> result = actionProcdefService.complementTask(null, taskId, msg, "njadmin", variables);
            } else {
                log.info("未查到审核任务号：{}", processInstanceBusinessKey);
                throw new ServiceException("定时任务触发审核失败");
            }
        } catch (Exception e) {
            log.error("定时任务触发审核失败:", e);
            throw new ServiceException("定时任务触发审核失败");
        }
    }


    /**
     * 处理采购订单付款申请
     *
     * @param payApplyDto 付款申请DTO
     */
    private void processBuyOrderPayApply(PayApplyDto payApplyDto) {
        try {

            Buyorder buyorder = new Buyorder();
            buyorder.setBuyorderId(payApplyDto.getRelatedId());
            buyorder.setCompanyId(1);
            BuyorderVo bv = buyorderService.getBuyOrderPrintInfo(buyorder);

            User belongUser = getBelongUser(bv.getTraderId(), ErpConst.TWO);
            this.saveCapitalBill(payApplyDto, belongUser, bv);

            actionProcdefService.updateInfo("T_BUYORDER", "BUYORDER_ID", payApplyDto.getRelatedId(), "LOCKED_STATUS", 0, 2);
        } catch (Exception e) {
            log.error("采购单后续流程异常:", e);
            throw new ServiceException("采购单后续流程异常");
        }
    }


    /**
     * 获取归属用户
     *
     * @param traderId   交易者ID
     * @param traderType 交易者类型
     * @return 归属用户
     */
    private User getBelongUser(Integer traderId, Integer traderType) {
        if (traderId == null) {
            return new User();
        }
        User belongUser = userService.getUserByTraderId(traderId, traderType);
        return belongUser != null && belongUser.getUserId() != null ? userService.getUserById(belongUser.getUserId()) : new User();
    }

    /**
     * 保存资金流水记录
     *
     * @param payApplyDto 付款申请DTO
     * @param belongUser  归属用户
     * @param bv          采购订单VO
     */
    private void saveCapitalBill(PayApplyDto payApplyDto, User belongUser, BuyorderVo bv) {
        CapitalBill capitalBill = this.createCapitalBill(payApplyDto);
        List<CapitalBillDetail> capitalBillDetails = new ArrayList<>();
        CapitalBillDetail capitalBillDetail = this.createCapitalBillDetail(payApplyDto, bv, belongUser);
        capitalBillDetails.add(capitalBillDetail);
        capitalBill.setCapitalBillDetails(capitalBillDetails);

        capitalBill.setCapitalBillDetail(this.createCapitalBillDetail(payApplyDto, bv, belongUser));

        ResultInfo<?> resultInfo = oldCapitalBillService.saveAddCapitalBill(capitalBill);
        if (resultInfo.getCode() != 0) {
            log.error("保存资金流水记录失败:{}", resultInfo.getMessage());
            throw new ServiceException("保存资金流水记录失败");
        }
    }

    /**
     * 创建资金流水记录
     *
     * @param payApplyDto 付款申请DTO
     * @return 资金流水记录
     */
    private CapitalBill createCapitalBill(PayApplyDto payApplyDto) {
        CapitalBill capitalBill = new CapitalBill();
        capitalBill.setCreator(1);
        capitalBill.setAddTime(System.currentTimeMillis());
        capitalBill.setCompanyId(1);
        capitalBill.setCurrencyUnitId(1);
        capitalBill.setTraderTime(System.currentTimeMillis());
        capitalBill.setPaymentType(0);
        capitalBill.setTraderType(payApplyDto.getTraderMode() == 528 ? 5 : 2);
        capitalBill.setTraderSubject(payApplyDto.getTraderSubject());
        capitalBill.setTraderMode(payApplyDto.getTraderMode());
        capitalBill.setAmount(payApplyDto.getAmount());
        capitalBill.setPayee(payApplyDto.getTraderName());
        capitalBill.setPayeeBankAccount(payApplyDto.getBankAccount());
        capitalBill.setPayeeBankName(payApplyDto.getBank());

        PayVedengBankDto payVedengBankDto = payVedengBankApiService.queryInfoByPayBankNo(custAccount);
        if (Objects.nonNull(payVedengBankDto)) {
            capitalBill.setPayerBankName(payVedengBankDto.getPayBankName());
            capitalBill.setPayerBankAccount(payVedengBankDto.getPayBankNo());
        }
        capitalBill.setPayer("南京贝登医疗股份有限公司");
        capitalBill.setComments("");

        return capitalBill;
    }

    /**
     * 创建资金流水明细
     *
     * @param payApplyDto 付款申请DTO
     * @param bv          采购订单VO
     * @param belongUser  归属用户
     * @return 资金流水明细
     */
    private CapitalBillDetail createCapitalBillDetail(PayApplyDto payApplyDto, BuyorderVo bv, User belongUser) {
        CapitalBillDetail capitalBillDetail = new CapitalBillDetail();
        capitalBillDetail.setAmount(payApplyDto.getAmount());
        capitalBillDetail.setBussinessType(525);
        capitalBillDetail.setOrderType(2);
        capitalBillDetail.setRelatedId(payApplyDto.getRelatedId());
        capitalBillDetail.setOrderNo(bv.getBuyorderNo());
        capitalBillDetail.setTraderId(bv.getTraderId());
        capitalBillDetail.setTraderType(2);
        capitalBillDetail.setUserId(bv.getUserId());
        if (belongUser != null && belongUser.getOrgName() != null && belongUser.getOrgId() != null) {
            capitalBillDetail.setOrgName(belongUser.getOrgName());
            capitalBillDetail.setOrgId(belongUser.getOrgId());
        }
        return capitalBillDetail;
    }
}
