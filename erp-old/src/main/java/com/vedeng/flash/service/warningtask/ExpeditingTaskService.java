package com.vedeng.flash.service.warningtask;

import com.vedeng.common.model.ResultInfo;
import com.vedeng.flash.constant.FlashConstant;
import com.vedeng.flash.dao.EarlyWarningTaskMapper;
import com.vedeng.flash.dto.EarlyWarningTaskDto;
import com.vedeng.flash.dto.ExpeditingCountingDto;
import com.vedeng.flash.dto.SendMessageDto;
import com.vedeng.flash.dto.EarlyTaskGoodsQueryDto;
import com.vedeng.flash.model.EarlyWarningTask;
import com.vedeng.flash.service.message.*;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.BuyorderGoods;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.order.model.vo.BuyorderVo;
import com.vedeng.order.service.BuyorderService;
import com.vedeng.order.service.impl.BuyorderServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 催货任务的service
 */
@Service("expeditingTaskService")
public class ExpeditingTaskService extends AbstractWarningtaskService {

    @Autowired
    private ExpeditingTaskCreateMessageSender expeditingTaskCreateMessageSender;

    @Resource
    private EarlyWarningTaskMapper earlyWarningTaskMapper;

    @Autowired
    ExpeditionTaskAdventMessageSender expeditionTaskAdventMessageSender;

    @Autowired
    ExpeditionTaskOverdueMessageSender expeditionTaskOverdueMessageSender;

    @Autowired
    ExpeditionTaskForSaleorderMessageSender expeditionTaskForSaleorderMessageSender;

    @Autowired
    ExpeditionTaskOverHalfdayMessageSender expeditionTaskOverHalfdayMessageSender;

    @Autowired
    private BuyorderServiceImpl buyorderService;

    @Autowired
    private BuyorderMapper buyorderMapper;

    @Resource
    private BuyorderGoodsMapper buyorderGoodsMapper;

    @Override
    protected void customPropertySet(EarlyWarningTask earlyWarningTask, EarlyWarningTaskDto earlyWarningTaskDto) {
        //设置消息类型
        earlyWarningTask.setEarlyWarningType(1);
        BeanUtils.copyProperties(earlyWarningTaskDto,earlyWarningTask);
    }

    @Override
    protected void sendMessageForWarningtaskCreate(EarlyWarningTask earlyWarningTask) {
        SendMessageDto sendMessageDto = new SendMessageDto();
        sendMessageDto.setEarlyWarningTask(earlyWarningTask);
        //临期
        if (FlashConstant.TASK_STATUS_ADVENT.equals(earlyWarningTask.getTaskStatus())) {
            expeditionTaskAdventMessageSender.sendMesssage(sendMessageDto);
        }
        //逾期
        if (FlashConstant.TASK_STATUS_OVERDUE.equals(earlyWarningTask.getTaskStatus())) {
            expeditionTaskOverdueMessageSender.sendMesssage(sendMessageDto);
            List<Saleorder> saleorderByBuyorderGoodsId = buyorderMapper.getSaleorderByBuyorderGoodsId(earlyWarningTask.getRelateBusinessId());
            if(CollectionUtils.isNotEmpty(saleorderByBuyorderGoodsId)){
                for (Saleorder saleorder : saleorderByBuyorderGoodsId) {
                    sendMessageDto.setSaleorderUser(saleorder.getUserId());
                    expeditionTaskForSaleorderMessageSender.sendMesssage(sendMessageDto);
                }
            }
        }
        //逾期12小时
        if (FlashConstant.TASK_STATUS_OVERDUE_HALFDAY.equals(earlyWarningTask.getTaskStatus())) {
            expeditionTaskOverHalfdayMessageSender.sendMesssage(sendMessageDto);
        }
    }

    public EarlyWarningTaskDto getFollowInfoDetailById(EarlyTaskGoodsQueryDto earlyTaskGoodsQueryDto) {
        return earlyWarningTaskMapper.getFollowInfoDetailById(earlyTaskGoodsQueryDto);
    }

    public ResultInfo updateFollowInfoById(EarlyWarningTask earlyWarningTask) {
        int i = earlyWarningTaskMapper.updateByPrimaryKeySelective(earlyWarningTask);

        //更新采购单催货跟进状态信息
        Buyorder byuorder = buyorderMapper.getByuorderByBuyorderGoodsId(earlyWarningTask.getRelateBusinessId());
        List<BuyorderGoodsVo> buyorderGoodsVoList = buyorderGoodsMapper.getBuyorderGoodsVoListByBuyorderId(byuorder.getBuyorderId());
        if (CollectionUtils.isNotEmpty(buyorderGoodsVoList)){
            List<Integer> buyorderGoodsIdList = buyorderGoodsVoList.stream().map(e -> e.getBuyorderGoodsId()).collect(Collectors.toList());
            List<EarlyWarningTask> existEarlyWarningGoodsTaskList = earlyWarningTaskMapper.getExistEarlyWarningGoodsTaskListByBuyorderGoodsIdList(buyorderGoodsIdList);
            if (CollectionUtils.isNotEmpty(existEarlyWarningGoodsTaskList)){
                EarlyWarningTask earlyWarningTask1 = existEarlyWarningGoodsTaskList.stream().filter(e -> e.getFollowUpNum() != null && e.getFollowUpNum() == 0).findAny().orElse(null);
                if(earlyWarningTask1 != null){
                    byuorder.setExpeditingFollowStatus(2);
                } else {
                    byuorder.setExpeditingFollowStatus(3);
                }
                buyorderMapper.updateByPrimaryKeySelective(byuorder);
            }
        }

        if (i > 0) {
            return new ResultInfo(0, "更新成功");
        } else {
            return new ResultInfo(-1,"更新失败");
        }
    }

    public void renovateExpeditionStatus() {
       List<EarlyWarningTask> earlyWarningTaskList = earlyWarningTaskMapper.getAllExpedingTask();
       if(CollectionUtils.isNotEmpty(earlyWarningTaskList)){
           SendMessageDto sendMessageDto = new SendMessageDto();
           for (EarlyWarningTask earlyWarningTask : earlyWarningTaskList) {
               sendMessageDto.setEarlyWarningTask(earlyWarningTask);
               BuyorderGoods buyorderGoods = buyorderGoodsMapper.getbuyorderGoodsInfoByBuyorderGoodsId(earlyWarningTask.getRelateBusinessId());
               //计算数目
               ExpeditingCountingDto expeditingCountingDto = buyorderService.caculateCountNum(buyorderGoods);
               expeditingCountingDto.setDeliveryCycle(Integer.toString(earlyWarningTask.getGoodsDay()));
               expeditingCountingDto.setSendGoodsTime(earlyWarningTask.getDeliveryTime());
               //计算状态
               Integer buyorderGoodsStatus = buyorderService.getBuyorderGoodsStatus(expeditingCountingDto);
               //催货完成
               EarlyWarningTask updatetask = new EarlyWarningTask();
               if (FlashConstant.TASK_COMPLETED.equals(buyorderGoodsStatus)) {
                   updatetask.setEarlyWarningTaskId(earlyWarningTask.getEarlyWarningTaskId());
                   updatetask.setIsDeleted(1);
                   earlyWarningTaskMapper.updateByPrimaryKeySelective(updatetask);
                   List<Integer> buyorderGoodsIdList = buyorderGoodsMapper.getBuyorderGoodsIdsByBuyorderId(buyorderGoods.getBuyorderId());
                   buyorderGoodsIdList = buyorderGoodsIdList.stream().filter(e->!e.equals(buyorderGoods.getBuyorderGoodsId())).collect(Collectors.toList());
                   if (CollectionUtils.isNotEmpty(buyorderGoodsIdList)) {
                       List<EarlyWarningTask> existEarlyWarningTaskList =earlyWarningTaskMapper.getExistEarlyWarningGoodsTaskListByBuyorderGoodsIdList(buyorderGoodsIdList);
                        if (CollectionUtils.isEmpty(existEarlyWarningTaskList)) {
                            Buyorder buyorder = new Buyorder();
                            buyorder.setBuyorderId(buyorderGoods.getBuyorderId());
                            buyorder.setExpeditingStatus(0);
                            buyorder.setExpeditingFollowStatus(0);
                            buyorderMapper.updateByPrimaryKeySelective(buyorder);
                        } else {
                            EarlyWarningTask earlyWarningTask1 = existEarlyWarningTaskList.stream().filter(e -> e.getFollowUpNum() == 0).findAny().orElse(null);
                            if(earlyWarningTask1 == null){
                                Buyorder buyorder = new Buyorder();
                                buyorder.setBuyorderId(buyorderGoods.getBuyorderId());
                                buyorder.setExpeditingFollowStatus(3);
                                buyorderMapper.updateByPrimaryKeySelective(buyorder);
                            }
                        }
                   } else {
                       Buyorder buyorder = new Buyorder();
                       buyorder.setBuyorderId(buyorderGoods.getBuyorderId());
                       buyorder.setExpeditingStatus(0);
                       buyorder.setExpeditingFollowStatus(0);
                       buyorderMapper.updateByPrimaryKeySelective(buyorder);
                   }
               }
               //不考虑临期
               //逾期
               if(FlashConstant.TASK_STATUS_OVERDUE.equals(buyorderGoodsStatus)){
                   if (earlyWarningTask.getTaskStatus() < buyorderGoodsStatus){
                       updatetask.setTaskStatus(buyorderGoodsStatus);
                       expeditionTaskOverdueMessageSender.sendMesssage(sendMessageDto);
                       List<Saleorder> saleorderByBuyorderGoodsId = buyorderMapper.getSaleorderByBuyorderGoodsId(earlyWarningTask.getRelateBusinessId());
                       if (CollectionUtils.isNotEmpty(saleorderByBuyorderGoodsId)) {
                           for (Saleorder saleorder : saleorderByBuyorderGoodsId) {
                               sendMessageDto.setSaleorderUser(saleorder.getUserId());
                               expeditionTaskForSaleorderMessageSender.sendMesssage(sendMessageDto);
                           }
                       }
                       updatetask.setEarlyWarningTaskId(earlyWarningTask.getEarlyWarningTaskId());
                       updatetask.setSendOverdueMessage(1);
                       earlyWarningTaskMapper.updateByPrimaryKeySelective(updatetask);
                       BuyorderVo buyorderVoById = buyorderMapper.getBuyorderVoById(buyorderGoods.getBuyorderId());
                       if(buyorderVoById.getExpeditingStatus() == null || buyorderVoById.getExpeditingStatus().equals(1)){
                           buyorderVoById.setExpeditingStatus(2);
                           buyorderMapper.updateExpeditingStatusById(buyorderVoById);
                       }
                   }
               }
               //逾期12小时
               if(FlashConstant.TASK_STATUS_OVERDUE_HALFDAY.equals(buyorderGoodsStatus)){
                   if(earlyWarningTask.getTaskStatus() < buyorderGoodsStatus){
                       updatetask.setTaskStatus(buyorderGoodsStatus);
                       updatetask.setEarlyWarningTaskId(earlyWarningTask.getEarlyWarningTaskId());
                       updatetask.setSendOverdueMessage(1);
                       if (earlyWarningTask.getFollowUpNum() == null || earlyWarningTask.getFollowUpNum() < 1) {
                           expeditionTaskOverHalfdayMessageSender.sendMesssage(sendMessageDto);
                       }
                       earlyWarningTaskMapper.updateByPrimaryKeySelective(updatetask);
                       BuyorderVo buyorderVoById = buyorderMapper.getBuyorderVoById(buyorderGoods.getBuyorderId());
                       if(buyorderVoById.getExpeditingStatus() == null || buyorderVoById.getExpeditingStatus().equals(1)){
                           buyorderVoById.setExpeditingStatus(2);
                           buyorderMapper.updateExpeditingStatusById(buyorderVoById);
                       }
                   }
               }
           }
       }
    }

    public EarlyWarningTask getFollowInfoDetail(EarlyWarningTask earlyWarningTask) {
            return earlyWarningTaskMapper.getFollowInfoDetail(earlyWarningTask);
    }
}
