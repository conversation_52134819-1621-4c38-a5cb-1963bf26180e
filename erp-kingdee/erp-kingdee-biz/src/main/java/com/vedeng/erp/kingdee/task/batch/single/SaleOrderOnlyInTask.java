package com.vedeng.erp.kingdee.task.batch.single;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.kingdee.batch.job.SaleOrderAfterSaleBatchJob;
import com.vedeng.erp.kingdee.task.batch.TaskBatchHandle;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/30 14:11
 *
 * 销售正逆项流程
 */
@JobHandler(value = "SaleOrderOnlyInTask")
@Component
public class SaleOrderOnlyInTask extends AbstractJobHandler {

    @Autowired
    private JobLauncher jobLauncher;

    @Autowired
    private SaleOrderAfterSaleBatchJob saleOrderAfterSaleBatchJob;



    /**
     * {"beginTime":"2022-11-01 00:00:00",
     * "endTime":"2022-12-01 00:00:00",
     * "timestamp":"1666687179395"}
     */
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        JobParameters jobParameters = new TaskBatchHandle().buildJobParameters(param);
        XxlJobLogger.log("====================销售仅入库流程开始==================");
        Job saleOnlyBackJob = saleOrderAfterSaleBatchJob.saleOrderAfterSaleOnlyBackJob();
        jobLauncher.run(saleOnlyBackJob, jobParameters);
        XxlJobLogger.log("====================销售仅出库流程结束==================");


        return SUCCESS;
    }
}
