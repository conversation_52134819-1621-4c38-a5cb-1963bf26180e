package com.vedeng.erp.trader.dto;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 *
 */
@NoArgsConstructor
@Setter
@Getter
public class TraderCustomerMarketingNodeDto {

    private Node fristCustomerType;


    private Node secondCustomerType;

    private Node threeCustomerType;

    private List<Node> institutionLevel;

    private List<Node> institutionType;




    @Getter
    @Setter
    @NoArgsConstructor
    public static class Node{
        private String label;
        private String value;

        private List<Node> children;
    }
}