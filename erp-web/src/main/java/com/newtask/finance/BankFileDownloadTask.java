package com.newtask.finance;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.billsync.task.model.entity.BankBillExtDo;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.finance.service.BankBillService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 定时任务调用前置机下载银行回单
 * <AUTHOR>
 */
@JobHandler("BankFileDownloadTask")
@Component
@Slf4j
public class BankFileDownloadTask extends AbstractJobHandler {

    @Autowired
    private BankBillService bankBillService;

    @Value("${cbc_bank_file_url}")
    private String cbcBankFileUrl;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        List<BankBillExtDo> bankBillExtDoList = new ArrayList<>();
        if(StringUtils.isNotBlank(param)){
            XxlJobLogger.log("下载回单定时任务执行参数{},",param);
            /**
             *  {
             *      "beginDate": "2023-01-01",
             *      "endDate": "2023-10-28"
             *  }
             */
            JSONObject jsonObject = JSON.parseObject(param);
            String beginDate = jsonObject.getString("beginDate");
            String endDate = jsonObject.getString("endDate");
            if(StrUtil.isNotBlank(beginDate) && StrUtil.isNotBlank(endDate)){
                bankBillExtDoList = bankBillService.queryCcbByTranDate(beginDate,endDate);
            }else {
                String[] bankBillIds = param.split(",");
                List<Integer> bankBillIdList = Arrays.stream(bankBillIds).map(Integer::parseInt).collect(Collectors.toList());
                bankBillExtDoList = bankBillService.queryBankBillByIdList(bankBillIdList);
            }
            log.info("获取建行流水(指定bankBillId),需查询回单的流水集合:{}", JSONUtil.toJsonStr(bankBillExtDoList));
            //防止db连接超时(60s),将请求拆分单独处理
            for (BankBillExtDo bankBillExtDo : bankBillExtDoList) {
                List<BankBillExtDo> singleList = Collections.singletonList(bankBillExtDo);
                log.info("获取建行流水(指定bankBillId),正在处理的流水:{}",JSONUtil.toJsonStr(singleList));
                ResultInfo resultInfo = bankBillService.getBankFile(singleList);
                log.info("获取建行流水(指定bankBillId),流水信息:{},db返回结果:{}",JSONUtil.toJsonStr(singleList),JSONUtil.toJsonStr(resultInfo));
                if(resultInfo != null){
                    //调用成功，下载回单
                    bankBillService.downloadBankFile(singleList, cbcBankFileUrl);
                }
            }
        }else {
            //获取昨天的日期
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DATE,-2);
            Date time = calendar.getTime();
            Date beginDate = DateUtil.beginOfDay(time);
            Date endDate = DateUtil.endOfDay(DateUtil.yesterday());
            //获取建行昨日流水查询回单-1建行收款（转入）流水
            log.info("获取建行昨日流水,beginDate:{},endDate:{}",beginDate,endDate);
            bankBillExtDoList = bankBillService.queryNeedDownLoadBankBill(beginDate, endDate, ErpConstant.ONE);
            log.info("获取建行昨日流水,需查询回单的流水集合:{}", JSONUtil.toJsonStr(bankBillExtDoList));
            for (BankBillExtDo bankBillExtDo : bankBillExtDoList) {
                List<BankBillExtDo> singleList = Collections.singletonList(bankBillExtDo);
                log.info("获取建行流水,正在处理的流水:{}",JSONUtil.toJsonStr(singleList));
                ResultInfo resultInfo = bankBillService.getBankFile(singleList);
                log.info("获取建行昨日流水,流水信息:{},db返回结果:{}",JSONUtil.toJsonStr(singleList),JSONUtil.toJsonStr(resultInfo));
                if(resultInfo != null){
                    //调用成功，下载回单
                    bankBillService.downloadBankFile(singleList, cbcBankFileUrl);
                }
            }
        }
        return SUCCESS;
    }
}
