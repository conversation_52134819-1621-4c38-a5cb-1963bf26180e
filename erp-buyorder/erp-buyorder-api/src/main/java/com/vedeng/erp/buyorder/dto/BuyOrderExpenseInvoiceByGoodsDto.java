package com.vedeng.erp.buyorder.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description 采购费用单发票主表根据关联的商品信息聚合
 * <AUTHOR>
 * @date 2022/8/27 16:01
 **/

@Data
public class BuyOrderExpenseInvoiceByGoodsDto {
    private Integer invoiceId;

    /**
     * ERP公司ID(T_COMPANY)
     */
    private Integer companyId;

    /**
     * 发票代码
     */
    private String invoiceCode;

    /**
     * 电子发票流水号
     */
    private String invoiceFlow;

    /**
     * 1纸质发票2电子发票
     */
    private Integer invoiceProperty;

    /**
     * 电子发票链接
     */
    private String invoiceHref;

    /**
     * 开票申请类型 字典库
     */
    private Integer type;

    /**
     * 录票/开票 1开票 2录票
     */
    private Integer tag;

    /**
     * 关联表ID
     */
    private Integer relatedId;

    /**
     * 售后订单ID
     */
    private Integer afterSalesId;

    /**
     * 发票号码
     */
    private String invoiceNo;

    /**
     * 发票类型 字典库
     */
    private Integer invoiceType;

    /**
     * 发票税率
     */
    private BigDecimal ratio;

    /**
     * 红蓝字 1红2蓝
     */
    private Integer colorType;

    /**
     * 发票金额
     */
    private BigDecimal amount;

    /**
     * 是否有效 0否 1是
     */
    private Integer isEnable;

    /**
     * 审核人ID
     */
    private Integer validUserid;

    /**
     * 审核 0待审核 1通过 不通过
     */
    private Integer validStatus;

    /**
     * 最后一次审核时间
     */
    private Long validTime;

    /**
     * 审核备注
     */
    private String validComments;

    /**
     * 发票打印状态 0：未打印 1：已打印
     */
    private Integer invoicePrintStatus;

    /**
     * 发票作废状态 0：未作废 1：已作废
     */
    private Integer invoiceCancelStatus;

    /**
     * 快递单ID
     */
    private Integer expressId;

    /**
     * 是否认证0未认证 1已认证 2 认证失败
     */
    private Integer isAuth;

    /**
     * 是否当月认证1是2否
     */
    private Integer isMonthAuth;

    /**
     * 认证时间
     */
    private Long authTime;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 添加人
     */
    private Integer creator;

    /**
     * 最近一次编辑时间
     */
    private Long modTime;

    /**
     * 最近一次编辑人
     */
    private Integer updater;

    /**
     * 开票申请ID
     */
    private Integer invoiceApplyId;

    /**
     * 丢票寄送发票时间
     */
    private Date sendTime;

    /**
     * 售后快递单ID
     */
    private Integer afterExpressId;

    /**
     * oss文件链接
     */
    private String ossFileUrl;

    /**
     * oss文件资源ID
     */
    private String resourceId;

    /**
     * 发票来自哪儿，1：航信
     */
    private Byte invoiceFrom;

    /**
     * 关联的 T_HX_INVOICE_ID
     */
    private Integer hxInvoiceId;

    /**
     * 认证方式 0 线下认证 1 接口认证
     */
    private Integer authMode;

    /**
     * 认证失败原因
     */
    private String authFailReason;

    /**
     * 是否正在认证 0 否 1 是
     */
    private Integer isAuthing;

    /**
     * 红蓝字补充类型 1冲销
     */
    private Integer colorComplementType;

    /**
     * @发票类型 字典库 隐射title@
     */
    private String invoiceTypeStr;

    /**
     * @添加人@
     */
    private String creatorName;


    /**
     * @invoiceDetail根据商品明细或票维度开票数量聚合数量@
     */
    private BigDecimal num;

    /**
     * @invoiceDetail 的DETAILGOODS_ID 订单商品明细id@
     */
    private Integer detailgoodsId;


}