package com.newtask.customerBillPeriodtask;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.customerbillperiod.exception.CustomerBillPeriodException;
import com.vedeng.order.service.OrderAccountPeriodService;
import com.vedeng.trader.dao.PeriodUseNodeRecordMapper;
import com.vedeng.trader.model.po.PeriodUseNodeRecordPo;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 账期节点记录处理器
 *
 * <AUTHOR>
 */
@Component
@JobHandler(value = "periodUseNodeRecordHandleTask")
public class PeriodUseNodeRecordHandleTask extends AbstractJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(PeriodUseNodeRecordHandleTask.class);

    @Autowired
    private OrderAccountPeriodService orderAccountPeriodService;

    @Resource
    private PeriodUseNodeRecordMapper periodUseNodeRecordMapper;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        XxlJobLogger.log("PeriodUseNodeRecordHandleTask-JOB, START");

        List<PeriodUseNodeRecordPo> unHandledRecords = periodUseNodeRecordMapper.getUnHandledRecord();
        logger.info("periodUseNodeRecordHandleTask unHandledRecords:{}", JSON.toJSONString(unHandledRecords));
        if (CollectionUtils.isEmpty(unHandledRecords)) {
            return SUCCESS;
        }
        unHandledRecords.forEach(item -> {
            try {
                orderAccountPeriodService.dealCustomerBillPeriodManagement(
                        item.getOrderId().intValue(),
                        item.getOrderType(),
                        item.getRelatedId().intValue(),
                        item.getAmount());
                periodUseNodeRecordMapper.updateHandleStatusByRecordId(item.getRecordId());
            } catch (CustomerBillPeriodException e) {
                logger.error("periodUseNodeRecordHandleTask warn", e);
            }
        });

        return SUCCESS;
    }
}
