<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.mapper.GoodsLevelMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.goods.domain.entity.GoodsLevelEntity">
    <!--@mbg.generated-->
    <!--@Table V_GOODS_LEVEL-->
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="UNIQUE_IDENTIFIER" jdbcType="CHAR" property="uniqueIdentifier" />
    <result column="LEVEL_NAME" jdbcType="VARCHAR" property="levelName" />
    <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
    <result column="ORDINAL" jdbcType="INTEGER" property="ordinal" />
    <result column="IS_DELETED" jdbcType="BOOLEAN" property="isDeleted" />
    <result column="ALLOW_SYNC_FRONTEND" jdbcType="BOOLEAN" property="allowSyncFrontend" />
    <result column="CREATOR_ID" jdbcType="INTEGER" property="creatorId" />
    <result column="UPDATER_ID" jdbcType="INTEGER" property="updaterId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, UNIQUE_IDENTIFIER, LEVEL_NAME, DESCRIPTION, ORDINAL, IS_DELETED, ALLOW_SYNC_FRONTEND, 
    CREATOR_ID, UPDATER_ID, ADD_TIME, MOD_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from V_GOODS_LEVEL
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from V_GOODS_LEVEL
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.goods.domain.entity.GoodsLevelEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into V_GOODS_LEVEL (UNIQUE_IDENTIFIER, LEVEL_NAME, DESCRIPTION, 
      ORDINAL, IS_DELETED, ALLOW_SYNC_FRONTEND, 
      CREATOR_ID, UPDATER_ID, ADD_TIME, 
      MOD_TIME)
    values (#{uniqueIdentifier,jdbcType=CHAR}, #{levelName,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{ordinal,jdbcType=INTEGER}, #{isDeleted,jdbcType=BOOLEAN}, #{allowSyncFrontend,jdbcType=BOOLEAN}, 
      #{creatorId,jdbcType=INTEGER}, #{updaterId,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, 
      #{modTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.goods.domain.entity.GoodsLevelEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into V_GOODS_LEVEL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="uniqueIdentifier != null and uniqueIdentifier != ''">
        UNIQUE_IDENTIFIER,
      </if>
      <if test="levelName != null and levelName != ''">
        LEVEL_NAME,
      </if>
      <if test="description != null and description != ''">
        DESCRIPTION,
      </if>
      <if test="ordinal != null">
        ORDINAL,
      </if>
      <if test="isDeleted != null">
        IS_DELETED,
      </if>
      <if test="allowSyncFrontend != null">
        ALLOW_SYNC_FRONTEND,
      </if>
      <if test="creatorId != null">
        CREATOR_ID,
      </if>
      <if test="updaterId != null">
        UPDATER_ID,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="uniqueIdentifier != null and uniqueIdentifier != ''">
        #{uniqueIdentifier,jdbcType=CHAR},
      </if>
      <if test="levelName != null and levelName != ''">
        #{levelName,jdbcType=VARCHAR},
      </if>
      <if test="description != null and description != ''">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="ordinal != null">
        #{ordinal,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BOOLEAN},
      </if>
      <if test="allowSyncFrontend != null">
        #{allowSyncFrontend,jdbcType=BOOLEAN},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.domain.entity.GoodsLevelEntity">
    <!--@mbg.generated-->
    update V_GOODS_LEVEL
    <set>
      <if test="uniqueIdentifier != null and uniqueIdentifier != ''">
        UNIQUE_IDENTIFIER = #{uniqueIdentifier,jdbcType=CHAR},
      </if>
      <if test="levelName != null and levelName != ''">
        LEVEL_NAME = #{levelName,jdbcType=VARCHAR},
      </if>
      <if test="description != null and description != ''">
        DESCRIPTION = #{description,jdbcType=VARCHAR},
      </if>
      <if test="ordinal != null">
        ORDINAL = #{ordinal,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        IS_DELETED = #{isDeleted,jdbcType=BOOLEAN},
      </if>
      <if test="allowSyncFrontend != null">
        ALLOW_SYNC_FRONTEND = #{allowSyncFrontend,jdbcType=BOOLEAN},
      </if>
      <if test="creatorId != null">
        CREATOR_ID = #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="updaterId != null">
        UPDATER_ID = #{updaterId,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.domain.entity.GoodsLevelEntity">
    <!--@mbg.generated-->
    update V_GOODS_LEVEL
    set UNIQUE_IDENTIFIER = #{uniqueIdentifier,jdbcType=CHAR},
      LEVEL_NAME = #{levelName,jdbcType=VARCHAR},
      DESCRIPTION = #{description,jdbcType=VARCHAR},
      ORDINAL = #{ordinal,jdbcType=INTEGER},
      IS_DELETED = #{isDeleted,jdbcType=BOOLEAN},
      ALLOW_SYNC_FRONTEND = #{allowSyncFrontend,jdbcType=BOOLEAN},
      CREATOR_ID = #{creatorId,jdbcType=INTEGER},
      UPDATER_ID = #{updaterId,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update V_GOODS_LEVEL
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="UNIQUE_IDENTIFIER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.uniqueIdentifier != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.uniqueIdentifier,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="LEVEL_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.levelName != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.levelName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="DESCRIPTION = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.description != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.description,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ORDINAL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.ordinal != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.ordinal,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DELETED = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDeleted != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.isDeleted,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="ALLOW_SYNC_FRONTEND = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.allowSyncFrontend != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.allowSyncFrontend,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorId != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.creatorId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterId != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.updaterId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
    </trim>
    where ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="ID" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into V_GOODS_LEVEL
    (UNIQUE_IDENTIFIER, LEVEL_NAME, DESCRIPTION, ORDINAL, IS_DELETED, ALLOW_SYNC_FRONTEND, 
      CREATOR_ID, UPDATER_ID, ADD_TIME, MOD_TIME)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.uniqueIdentifier,jdbcType=CHAR}, #{item.levelName,jdbcType=VARCHAR}, #{item.description,jdbcType=VARCHAR}, 
        #{item.ordinal,jdbcType=INTEGER}, #{item.isDeleted,jdbcType=BOOLEAN}, #{item.allowSyncFrontend,jdbcType=BOOLEAN}, 
        #{item.creatorId,jdbcType=INTEGER}, #{item.updaterId,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP}, 
        #{item.modTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2024-12-07-->
  <select id="findAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from V_GOODS_LEVEL
    where IS_DELETED = 0
  </select>
</mapper>