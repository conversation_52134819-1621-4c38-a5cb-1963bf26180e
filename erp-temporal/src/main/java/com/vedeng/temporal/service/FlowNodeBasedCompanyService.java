package com.vedeng.temporal.service;

import com.vedeng.temporal.domain.dto.CompanySequenceInfo;
import com.vedeng.temporal.mapper.FlowNodeCompanyMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * 基于T_FLOW_NODE的公司顺序服务
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-20
 */
@Service
@Slf4j
public class FlowNodeBasedCompanyService {

    @Autowired
    private FlowNodeCompanyMapper flowNodeCompanyMapper;

    /**
     * 根据流转单ID获取公司执行顺序
     * 
     * @param flowOrderId 流转单ID
     * @return 公司代码列表（按执行顺序排序）
     */
    public List<String> getCompanySequence(Long flowOrderId) {
            log.info("从T_FLOW_NODE获取公司执行顺序，flowOrderId: {}", flowOrderId);
            List<CompanySequenceInfo> companyInfos = flowNodeCompanyMapper.selectCompanySequenceByFlowOrderId(flowOrderId);
            // 检查并处理公司简称缺失的情况
            List<String> companySequence = processCompanySequence(companyInfos);
            log.info("获取到公司执行顺序，flowOrderId: {}, sequence: {}", flowOrderId, companySequence);
            return companySequence;
    }
    
    /**
     * 仅从流程节点获取公司执行顺序（不包含来自T_FLOW_ORDER的0级公司）
     * 
     * @param flowOrderId 流转单ID
     * @return 公司代码列表（按执行顺序排序）
     */
    public List<String> getCompanySequenceFromFlowNodeOnly(Long flowOrderId) {
        log.info("仅从T_FLOW_NODE获取公司执行顺序，flowOrderId: {}", flowOrderId);
        List<CompanySequenceInfo> companyInfos = flowNodeCompanyMapper.selectCompanySequenceFromFlowNodeOnly(flowOrderId);
        // 处理公司序列，不过滤非子公司节点
        List<String> companySequence = processCompanySequenceWithoutSubcompanyFilter(companyInfos);
        log.info("获取到公司执行顺序，flowOrderId: {}, sequence: {}", flowOrderId, companySequence);
        return companySequence;
    }

    /**
     * 根据流转单ID和业务类型获取公司执行顺序
     * 
     * @param flowOrderId 流转单ID
     * @param businessType 业务类型：1-采购，2-销售
     * @return 公司代码列表（按执行顺序排序）
     */
    public List<String> getCompanySequenceByBusinessType(Long flowOrderId, Integer businessType) {
            log.info("根据业务类型获取公司执行顺序，flowOrderId: {}, businessType: {}", flowOrderId, businessType);

            List<CompanySequenceInfo> companyInfos = flowNodeCompanyMapper.selectCompanySequenceByBusinessType(flowOrderId, businessType);

            // 检查并处理公司简称缺失的情况
            List<String> companySequence = processCompanySequence(companyInfos);

            log.info("获取到公司执行顺序，flowOrderId: {}, businessType: {}, sequence: {}", flowOrderId, businessType, companySequence);
            return companySequence;

    }

    /**
     * 处理公司序列，检查公司简称缺失情况
     * 规则：只有最后一个节点（最高级别）没有 companyShortName 时才过滤掉，
     *      如果是中间节点没有则报错
     */
    private List<String> processCompanySequence(List<CompanySequenceInfo> companyInfos) {
        if (companyInfos == null || companyInfos.isEmpty()) {
            return new ArrayList<>();
        }

        // 按节点级别排序，确保顺序正确
        companyInfos.sort(Comparator.comparingInt(CompanySequenceInfo::getNodeLevel));

        // 找到最高级别（最后一个节点）
        int maxLevel = companyInfos.get(companyInfos.size() - 1).getNodeLevel();

        List<String> validCompanies = new ArrayList<>();

        for (CompanySequenceInfo info : companyInfos) {
            boolean hasCompanyShortName = info.getCompanyShortName() != null && !info.getCompanyShortName().trim().isEmpty();

            if (!hasCompanyShortName) {
                if (info.getNodeLevel() == maxLevel) {
                    // 最后一个节点没有公司简称，过滤掉（不是子公司）
                    log.info("最后一个节点（级别: {}）没有公司简称，过滤掉", info.getNodeLevel());
                    continue;
                } else {
                    // 中间节点没有公司简称，报错
                    String errorMsg = String.format("中间节点（级别: %d）缺少公司简称，数据配置错误", info.getNodeLevel());
                    log.error(errorMsg);
                    throw new RuntimeException(errorMsg);
                }
            }

            validCompanies.add(info.getCompanyCode());
        }

        log.info("处理后有效公司数量: {}, 公司序列: {}", validCompanies.size(), validCompanies);
        return validCompanies;
    }

    /**
     * 处理公司序列，不过滤非子公司节点
     * 规则：保持所有节点，对于缺少companyShortName的节点使用替代标识
     */
    private List<String> processCompanySequenceWithoutSubcompanyFilter(List<CompanySequenceInfo> companyInfos) {
        if (companyInfos == null || companyInfos.isEmpty()) {
            return new ArrayList<>();
        }

        // 按节点级别排序，确保顺序正确
        companyInfos.sort(Comparator.comparingInt(CompanySequenceInfo::getNodeLevel));

        List<String> companySequence = new ArrayList<>();

        for (CompanySequenceInfo info : companyInfos) {
            String companyIdentifier = getCompanyIdentifier(info);
            
            if (companyIdentifier != null) {
                companySequence.add(companyIdentifier);
            } else {
                log.warn("节点（级别: {}）无法获取有效的公司标识，跳过该节点", info.getNodeLevel());
            }
        }

        log.info("处理后有效公司数量: {}, 公司序列: {}", companySequence.size(), companySequence);
        return companySequence;
    }

    /**
     * 获取公司标识，优先级：companyShortName > companyName > traderName > companyId
     */
    private String getCompanyIdentifier(CompanySequenceInfo info) {
        // 优先使用公司简称
        if (info.getCompanyShortName() != null && !info.getCompanyShortName().trim().isEmpty()) {
            return info.getCompanyShortName();
        }
        
        // 其次使用公司名称
        if (info.getCompanyName() != null && !info.getCompanyName().trim().isEmpty()) {
            log.info("节点（级别: {}）使用公司名称作为标识: {}", info.getNodeLevel(), info.getCompanyName());
            return info.getCompanyName();
        }
        
        // 再次使用交易者名称
        if (info.getTraderName() != null && !info.getTraderName().trim().isEmpty()) {
            log.info("节点（级别: {}）使用交易者名称作为标识: {}", info.getNodeLevel(), info.getTraderName());
            return info.getTraderName();
        }
        
        // 最后使用公司ID
        if (info.getCompanyId() != null) {
            String companyIdStr = "COMPANY_" + info.getCompanyId();
            log.info("节点（级别: {}）使用公司ID作为标识: {}", info.getNodeLevel(), companyIdStr);
            return companyIdStr;
        }
        
        return null;
    }

    /**
     * 获取流程节点ID
     *
     * 标准化的 flowNodeId 获取逻辑，通过 businessId（flowOrderId）和公司代码获取对应的流程节点ID
     *
     * @param businessId 业务ID（实际上是 flowOrderId）
     * @param currentCompany 当前公司代码
     * @return flowNodeId，如果获取失败则返回null
     */
    public Long getFlowNodeId(String businessId, String currentCompany) {
            Long flowOrderId = Long.valueOf(businessId);
            return flowNodeCompanyMapper.getFlowNodeIdByFlowOrderAndCompany(flowOrderId, currentCompany);
    }

    /**
     * 根据流程节点ID获取公司代码
     *
     * @param flowNodeId 流程节点ID
     * @return 公司代码，如果获取失败则返回null
     */
    public String getCompanyCodeByFlowNodeId(Long flowNodeId) {
        log.info("根据flowNodeId获取公司代码，flowNodeId: {}", flowNodeId);
        try {
            String companyCode = flowNodeCompanyMapper.getCompanyCodeByFlowNodeId(flowNodeId);
            log.info("获取到公司代码，flowNodeId: {}, companyCode: {}", flowNodeId, companyCode);
            return companyCode;
        } catch (Exception e) {
            log.error("根据flowNodeId获取公司代码异常，flowNodeId: {}", flowNodeId, e);
            return null;
        }
    }



}
