package com.pricecenter.service.validator;

import com.pricecenter.dto.PriceInfoUploadValidatorDto;
import com.pricecenter.dto.UserInfo;
import com.pricecenter.dto.ValidatorResult;
import com.pricecenter.service.BasePriceMaintainService;
import com.pricecenter.service.Validator;
import com.vedeng.authorization.model.User;
import com.vedeng.goods.dao.CoreSkuGenerateMapper;
import com.vedeng.goods.model.CoreSkuGenerate;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


/**
 * skuNo相关校验器
 */
@Service
public class SkuNoValidatorForReference implements Validator<PriceInfoUploadValidatorDto> {

    @Autowired
    @Qualifier("coreSkuGenerateMapper")
    private CoreSkuGenerateMapper coreSkuGenerateMapper;

    @Override
    public ValidatorResult validator(PriceInfoUploadValidatorDto priceInfoUploadValidatorDto) {

        List<String> exsitSkuList = new ArrayList<>();

        ValidatorResult result = ValidatorResult.newBuild();

        //获取所有的行
        List<Row> rowList = priceInfoUploadValidatorDto.getRows();
        User user = priceInfoUploadValidatorDto.getUser();

        Row thisRow = null;

        int rowNum = 0;
        int cellNum = 0;

        for(int i = 0;i < rowList.size();i++){

            rowNum = i;

            thisRow = rowList.get(i);

            Cell cell = thisRow.getCell(cellNum);

            if (cell == null || cell.getCellType() ==  CellType.BLANK) {
                return result.setMessage("第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：不允许为空，请验证！");
            }

            if(cell.getCellType() != CellType.STRING){
                return result.setMessage("第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：不允许为数字，请验证！");
            }

            cell.setCellType(CellType.STRING);
            String skuNo = cell.getStringCellValue();

            //订货号XXX不存在，提交失败
            CoreSkuGenerate skuInDb = coreSkuGenerateMapper.selectBySkuNo(skuNo);
            if(skuInDb == null){
                return result.setMessage("订货号"+skuNo+"不存在，提交失败");
            }

            Long skuId =  Long.valueOf(skuNo.substring(1));

            //Sku XXX不归属于当前用户，操作失败
            UserInfo userInfo = coreSkuGenerateMapper.getAssignAndManageId(skuId);
            if(!user.getUserId().equals(userInfo.getAssistantId())  &&  !user.getUserId().equals(userInfo.getManagerId())){
                return result.setMessage("Sku "+skuNo+"不归属于当前用户，操作失败");
            }

            //订货号XXX重复，提交失败
            if(exsitSkuList.contains(skuNo)){
                return result.setMessage("订货号"+skuNo+"重复，提交失败");
            }

            exsitSkuList.add(skuNo);
        }

        return result.setResult(true);
    }

}
