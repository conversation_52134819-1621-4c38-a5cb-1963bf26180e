package com.vedeng.activiti.taskassign;

import com.vedeng.todolist.service.impl.ZlbCheckBuyOrder;
import com.vedeng.todolist.service.impl.ZlbCheckSaleOrder;
import com.vedeng.todolist.service.impl.ZlbCheckTraderCustomerCertificate;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

/**
 * @Author: daniel
 * @Date: 2020/12/23 09 42
 * @Description:
 */
public class PreQualityDepartmentCheckListener implements ExecutionListener {

    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();

    private ZlbCheckTraderCustomerCertificate zlbCheckTraderCustomerCertificate = (ZlbCheckTraderCustomerCertificate) context.getBean("zlbCheckTraderCustomerCertificate");

    private ZlbCheckSaleOrder zlbCheckSaleOrder = (ZlbCheckSaleOrder) context.getBean("zlbCheckSaleOrder");

    private ZlbCheckBuyOrder zlbCheckBuyOrder = (ZlbCheckBuyOrder) context.getBean("zlbCheckBuyOrder");

    @Override
    public void notify(DelegateExecution delegateExecution) throws Exception {
        Integer relateTableKey = (Integer) delegateExecution.getVariable("relateTableKey");
        String processDefinitionKey = (String) delegateExecution.getVariable("processDefinitionKey");

        if ("customerAptitudeVerify".equals(processDefinitionKey)){
            zlbCheckTraderCustomerCertificate.add(relateTableKey,null,null,null);
        } else if ("hc_order_auto_verify".equals(processDefinitionKey)){
            zlbCheckSaleOrder.add(relateTableKey,null,null,null);
        } else if ("saleorderVerify".equals(processDefinitionKey)){
            zlbCheckSaleOrder.add(relateTableKey,null,null,null);
        } else if ("buyorderVerify_HC".equals(processDefinitionKey)){
            zlbCheckBuyOrder.add(relateTableKey,null,null,null);
        } else if ("buyorderVerify".equals(processDefinitionKey)){
            zlbCheckBuyOrder.add(relateTableKey,null,null,null);
        } else if("bd_order_auto_verify".equals(processDefinitionKey)){
            zlbCheckSaleOrder.add(relateTableKey,null,null,null);
        }
    }
}
