package com.vedeng.logistics.service;

import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.service.BaseService;

import java.util.List;

/**
 * <b>Description:</b><br>
 * 订单流升级-service
 *
 * <AUTHOR>
 * @Note <b>ProjectName:</b> erp <br>
 * <b>PackageName:</b> com.vedeng.orderstream.aftersales.service <br>
 * <b>ClassName:</b> AfterSalesUpgradeService <br>
 * <b>Date:</b> 2021/10/11 19:58 <br>
 */
public interface AfterSalesService extends BaseService {
    /**
     * <b>Description:</b><br>
     * 获取当前售后单的节点状态
     *
     * @param
     * @return java.util.List<java.lang.Integer>
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/10/11 20:00
     */
    List<Integer> getAfterSaleNowStatus(AfterSalesVo afterSalesVo);
    /**
     * <b>Description:</b><br>
     * 设置售后退货入库信息及状态展示
     *
     * @param afterSalesVo
     * @return void
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/10/14 15:05
     */
    void setAfterSalesTHInfo(AfterSalesVo afterSalesVo);

    /**
     * <b>Description:</b><br>
     * 设置售后换货出入库信息及状态展示
     *
     * @param afterSalesVo
     * @return void
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/10/14 15:05
     */
    void setAfterSalesHHInfo(AfterSalesVo afterSalesVo);

    /**
     * 更新售后单退票状态
     * @param afterSalesId
     */
    void updateBackInvoiceStatus(Integer afterSalesId,Integer status);

    /**
     * 更新售后单是否可关闭
     * @param afterSalesVo
     */
    void setIsCloseStatus(AfterSalesVo afterSalesVo);

    /**
     * <b>Description:</b><br> 申请审核
     * @param afterSales
     * @return
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2017年10月19日 下午4:43:37
     */
    ResultInfo<?> editApplyAudit(AfterSalesVo afterSales);

    /**
     * .订单解锁时更新预警状态
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2021/5/6 9:56.
     * @author: Randy.Xu.
     * @param saleorderId
     * @return: com.vedeng.common.ResultInfo.
     * @throws:  .
     */
    ResultInfo<?> updateUnlockSaleOrderWarning(Integer saleorderId);

    /**
     * 影响范围 ：
     * 1、url：/order/aftersalesUpgrade/editApplyAudit 售后单申请审核
     * <AUTHOR>
     * @desc 校验安调商品信息（合同-含安调，非合同-不含安调）
     * @param afterSales
     * @return
     */
    ResultInfo<?> checkAtGoodsInfo(AfterSales afterSales);

    /**
     * 校验售后单是否有直发采购售后
     * 0、关联直发采购售后单不满足关闭条件
     * 1、关联直发采购售后单满足关闭条件，或没有直发采购售后单
     * <AUTHOR>
     * @param afterSalesVo
     * @return
     */
    Integer verifyCloseStatusByBuyorderAfter(AfterSalesVo afterSalesVo);
}
