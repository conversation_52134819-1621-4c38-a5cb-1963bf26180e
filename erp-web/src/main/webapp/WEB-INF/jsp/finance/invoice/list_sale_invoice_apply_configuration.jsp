<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="开票配置" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<link rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/static/layui/css/layui.css">
<style>
    .checkbox-container {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
    }

    .checkbox-item {
        margin-bottom: 10px;
    }

    .layui-table-grid-down {
        display: none;
    }

    .layui-icon {
        height: auto;
        background: none;
    }

    .layui-table {
        table-layout: fixed;
    }

</style>
<div class="layui-container">
    <fieldset class="layui-elem-field layui-field-title" style="margin-top: 30px;">
        <legend>开票配置</legend>
    </fieldset>
    <div class="checkbox-item">
        <input type="checkbox" id="option1" name="option1" checked disabled>
        <label for="option1">销售订单启用自动申请&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;定时任务配置:
            &nbsp;执行频率-每日1点 &nbsp;&nbsp;&nbsp;&nbsp;执行开始时间-2023-12-19 01:00:00</label>
    </div>
    <div class="checkbox-item">
        <input type="checkbox" id="option2" name="option2" checked disabled>
        <label for="option2">提前申请启用自动审核&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            定时任务配置:&nbsp;
            执行频率-每15分钟&nbsp;&nbsp;
            执行开始时间-2023-12-19 01:00:00</label>
    </div>
    <div class="checkbox-item">
        <input type="checkbox" id="option3" name="option3" <c:if test="${sales}">checked</c:if>>
        <label for="option3">销售订单启用自动开票&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            定时任务配置:&nbsp;
            执行频率-每30分钟&nbsp;&nbsp;
            执行开始时间-2023-12-19 01:00:00</label>
    </div>
    <div class="checkbox-item">
        <input type="checkbox" id="option4" name="option4" <c:if test="${after}">checked</c:if>>
        <label for="option4">售后订单启用自动开票&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            定时任务配置:&nbsp;
            执行频率-每30分钟&nbsp;&nbsp;
            执行开始时间-2023-12-19 01:00:00</label>
    </div>


    <fieldset class="layui-elem-field layui-field-title" style="margin-top: 30px;">
        <legend>申请规则</legend>
    </fieldset>
    <p style="color: gray; font-size: small;">以下规则需全部满足，不适用于”票货同行</p>
    <table id="dataTable1" lay-filter="dataTable"></table>

    <fieldset class="layui-elem-field layui-field-title" style="margin-top: 30px;">
        <legend>开票规则</legend>
    </fieldset>
    <p style="color: gray; font-size: small;">以下规则需全部满足，不适用于”票货同行</p>
    <table id="dataTable2" lay-filter="dataTable"></table>

    <fieldset class="layui-elem-field layui-field-title" style="margin-top: 30px;">
        <legend>合同及确认单</legend>
    </fieldset>
    <span class="checkbox-item">
    <input type="checkbox" id="option5" name="option5" <c:if test="${confirmation}">checked</c:if>>
    <label for="option4">确认单仅最后一次申请生效&nbsp;&nbsp;&nbsp;<span
            style="color: gray; font-size: small;">勾选后，仅最后一次申请时，才会对确认单状态进行要求</span></label>
</span>
    <table id="dataTable3" lay-filter="dataTable"></table>
    <button id="saveButton" class="layui-btn layui-btn-normal" style="display: block; margin-left: auto; margin-right: auto;">保存</button>
</div>
<script src="/webjars/ezadmin/plugins/layui/layui.js" charset="utf-8"></script>
<script>
    // 创建一个变量来存储固定数据
    var fixedData = [
        {
            originalAmount: '不限制',
            creationDate: '小于2023-01-01',
            paymentTerm: '不限',
            orderSource: '不限',
            contractStatus: '不限',
            confirmationStatus: '不限'
        },
        {
            originalAmount: '不限制',
            creationDate: '不限',
            paymentTerm: '是',
            orderSource: '不限',
            contractStatus: '不限',
            confirmationStatus: '不限'
        },
        {
            originalAmount: '小于5000',
            creationDate: '不限',
            paymentTerm: '不限',
            orderSource: '不限',
            contractStatus: '不限',
            confirmationStatus: '不限'
        },
        {
            originalAmount: '介于5000-49999',
            creationDate: '大于等于2023-01-01',
            paymentTerm: '否',
            orderSource: '线上',
            contractStatus: '不限',
            confirmationStatus: '审核通过'
        },
        {
            originalAmount: '介于5000-49999',
            creationDate: '大于等于2023-01-01',
            paymentTerm: '否',
            orderSource: '线下',
            contractStatus: '审核通过',
            confirmationStatus: '审核通过'
        },
        {
            originalAmount: '大于等于50000',
            creationDate: '大于等于2023-01-01',
            paymentTerm: '否',
            orderSource: '不限',
            contractStatus: '审核通过',
            confirmationStatus: '审核通过'
        },
        {
            originalAmount: '其他:不符合以上所有条件时',
            creationDate: '',
            paymentTerm: '',
            orderSource: '',
            contractStatus: '审核通过',
            confirmationStatus: '审核通过'
        },
    ];
    layui.use('table', function () {
        var table = layui.table;
        table.render({
            elem: '#dataTable1',
            page: false,
            url: '/invoiceApply/api//getCheckRuleList.do?ruleType=1',
            cols: [
                [
                    {field: 'ruleCode', title: '规则编码', width: '10%', align: 'center'},
                    {field: 'ruleName', title: '规则名称', width: 300,align: 'center'},
                    {field: 'ruleContent', title: '规则内容', align: 'center'},
                    {field: 'usageScene', title: '适用场景', align: 'center'}
                ]
            ]
        });
        table.render({
            elem: '#dataTable2',
            page: false,
            url: '/invoiceApply/api/getCheckRuleList.do?ruleType=2',
            cols: [
                [
                    {field: 'ruleCode', title: '规则编码', width: '10%', align: 'center'},
                    {field: 'ruleName', title: '规则名称',width: 300, align: 'center'},
                    {field: 'ruleContent', title: '规则内容', align: 'center'},
                    {field: 'usageScene', title: '适用场景', align: 'center'}
                ]
            ]
        });
        table.render({
            elem: '#dataTable3',
            page: false,
            data: fixedData, // 将外部变量作为 data 属性的值
            cols: [
                [
                    {field: 'condition', title: '条件', colspan: 4, align: 'center'},
                    {field: 'rule', title: '规则', colspan: 2, align: 'center'}
                ],
                [
                    {field: 'originalAmount', title: '订单原金额', width: '30%', align: 'center'},
                    {field: 'creationDate', title: '订单创建日期', width: 300},
                    {field: 'paymentTerm', title: '账期支付'},
                    {field: 'orderSource', title: '订单来源'},
                    {field: 'contractStatus', title: '合同状态'},
                    {field: 'confirmationStatus', title: '确认单状态'}
                ]
            ],
            done: function (res, curr, count) {
                var index = res.data.findIndex(function (item) {
                    return item.originalAmount === '其他:不符合以上所有条件时';
                });
                if (index !== -1) {
                    var trElem = this.elem.next().find('.layui-table-box tbody tr[data-index="' + index + '"]');
                    trElem.find('td[data-field="originalAmount"]').attr('colspan', 4);
                    trElem.find('td[data-field="creationDate"]').remove();
                    trElem.find('td[data-field="paymentTerm"]').remove();
                    trElem.find('td[data-field="orderSource"]').remove();
                }
            }
        });
    });

    // 获取保存按钮元素
    var saveButton = document.getElementById('saveButton');

    saveButton.addEventListener('click', function () {
        var option3Value = document.getElementById('option3').checked ? 1 : 0;
        var option4Value = document.getElementById('option4').checked ? 1 : 0;
        var option5Value = document.getElementById('option5').checked ? 1 : 0;

        // 构建要发送给后端接口的数据对象
        var data = {
            salesOrderAutoInvoice: option3Value,
            afterSalesOrderAutoInvoice: option4Value,
            confirmationEffectiveOnlyLast: option5Value,
        };

        // 发送数据给后端接口
        var apiUrl = '/finance/invoice/saveCheckRule.do';

        $.ajax({
            url: apiUrl,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(data),
            success: function (response) {
                layui.layer.msg('保存成功', {
                    time: 2000 // 2秒后自动关闭
                }, function () {
                    // 刷新页面
                    window.location.reload();
                });
            },
            error: function (jqXHR, textStatus, errorThrown) {
                layui.layer.msg('保存失败', {
                    time: 2000 // 2秒后自动关闭
                }, function () {
                    // 刷新页面
                    window.location.reload();
                });
            }
        });
    });

</script>
<%@ include file="../../common/footer.jsp" %>
