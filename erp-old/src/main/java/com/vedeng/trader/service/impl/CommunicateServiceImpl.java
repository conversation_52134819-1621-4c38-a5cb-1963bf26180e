package com.vedeng.trader.service.impl;

import com.alibaba.fastjson.JSON;
import com.common.constants.Contant;
import com.fasterxml.jackson.core.type.TypeReference;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.call.service.CallService;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.http.HttpClientUtils;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.redis.RedisUtils;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.util.BitsetUtils;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.erp.market.api.MarketPlanApiService;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.erp.trader.service.PublicCustomerRecordApiService;
import com.vedeng.order.model.Quoteorder;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.dto.CustomerCountDto;
import com.vedeng.order.service.QuoteService;
import com.vedeng.trader.dao.CommunicateRecordMapper;
import com.vedeng.trader.dao.LandLineRecordMapper;
import com.vedeng.trader.dao.TraderContactMapper;
import com.vedeng.trader.enums.LandLineCodeEnum;
import com.vedeng.trader.model.CommunicateRecord;
import com.vedeng.trader.model.LandLineCallRecord;
import com.vedeng.trader.model.dto.CallingLineDto;
import com.vedeng.trader.model.dto.RecentLandLineRecordDto;
import com.vedeng.trader.service.CommunicateService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Service("communicateService")
public class CommunicateServiceImpl extends BaseServiceimpl implements CommunicateService {

    @Autowired
    @Qualifier("communicateRecordMapper")
    private CommunicateRecordMapper communicateRecordMapper;
    //循环依赖

//	@Autowired
//	@Qualifier("saleorderService")
//	protected SaleorderService saleorderService;

    @Autowired
    @Qualifier("quoteService")
    private QuoteService quoteService;

    @Autowired
    private RedisUtils redisUtils;

    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private LandLineRecordMapper landLineRecordMapper;

    @Resource
    private TraderContactMapper traderContactMapper;

	@Autowired
	private CallService callService;

	@Autowired
	private PublicCustomerRecordApiService publicCustomerRecordApiService;

    @Autowired
    private MarketPlanApiService marketPlanApiService;

    /**
     * 配置二次校验线路未接通次数
     */
    @Value("${callcenter.callOutNumberForCallPull:1}")
    private Integer unConnectRecordCount ;

	@Override
	public Integer updateCommunicateDone(CommunicateRecord communicateRecord, HttpSession session) {
		User user = (User) session.getAttribute(ErpConst.CURR_USER);
		Long time = DateUtil.sysTimeMillis();

		communicateRecord.setModTime(time);
		communicateRecord.setUpdater(user.getUserId());
		if (communicateRecord.getRelatedId() == null||communicateRecord.getRelatedId()==0) {
			return 0;
		}
		return communicateRecordMapper.updateCommunicateDone(communicateRecord);
	}

    @Override
    public Integer addCommunicate(CommunicateRecord communicateRecord, HttpSession session) {

        //新增沟通记录
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        Long time = DateUtil.sysTimeMillis();

        communicateRecord.setAddTime(time);
        communicateRecord.setCreator(user.getUserId());
        communicateRecord.setModTime(time);
        communicateRecord.setUpdater(user.getUserId());

        int insertRows = communicateRecordMapper.insert(communicateRecord);
        if(StringUtils.isNotEmpty(communicateRecord.getContentSuffix()) ||
                StringUtils.isNotEmpty(communicateRecord.getContactContent())
        ){
            //工作台-精准营销活动，需要更新活动-销售任务中的是否已沟通更新为已沟通。-Kerwin VDERP-15559
            marketPlanApiService.updateMarketPlanTraderSendMsg(communicateRecord.getTraderId());
        }

        if (StringUtil.isNotBlank(communicateRecord.getCoid())) {
            redisUtils.sadd(ErpConst.RECORD_USER_KEY, user.getUserId());
        }

		//VDERP-10080新增商机，解锁公海锁定客户start
		if (StringUtils.isNotBlank(communicateRecord.getCoid())){
			List<Map<String,Object>> coidInfo = callService.getRecordListByCoid(Collections.singletonList(communicateRecord.getCoid()));
			if (coidInfo.size() > 0) {
				int coidLength = coidInfo.stream().map(item -> Integer.valueOf(item.get("FILELEN").toString())).reduce(0,Math::max);
				if (coidLength > 120){
					publicCustomerRecordApiService.unLockTrader(communicateRecord.getTraderId(),communicateRecord.getCommunicateRecordId(),ErpConst.TWO,user.getUserId());
				}
			}
		}
		//VDERP-10080新增商机，解锁公海锁定客户end


        //add by brianna 2020/3/11 VDERP-1964-------------start
        //销售订单
        if (SysOptionConstant.ID_246.equals(communicateRecord.getCommunicateType())) {
            //修改订单报价(有沟通记录0无 1有)
            Saleorder saleorder = new Saleorder();
            saleorder.setUpdater(user.getUserId());
            saleorder.setModTime(DateUtil.sysTimeMillis());
            saleorder.setHaveCommunicate(1);
            saleorder.setSaleorderId(communicateRecord.getRelatedId());
            //saleorderService.editSaleorderHaveCommunicate(saleorder);
            final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
            };
            String url = httpUrl + "order/saleorder/editsaleorderhavecommunicate.htm";
            try {
                HttpClientUtils.post(url, saleorder, clientId, clientKey, TypeRef);
            } catch (IOException e) {
                logger.error(Contant.ERROR_MSG, e);
            }
        }

        //报价单
        if (communicateRecord.getCommunicateType() == SysOptionConstant.ID_245) {
            //修改报价主表信息(有沟通记录0无 1有)
            Quoteorder quote = new Quoteorder();
            quote.setUpdater(user.getUserId());
            quote.setModTime(DateUtil.sysTimeMillis());
            quote.setQuoteorderId(communicateRecord.getRelatedId());
            quoteService.editQuoteHaveCommunicate(quote);
        }
        //add by brianna 2020/3/11 VDERP-1964-------------end

        return insertRows;
    }

    @Override
    public Integer countTtNumberCountToday(String ttNumber) {

        return communicateRecordMapper.countTtNumberCountToday(ttNumber);
    }

    @Override
    public void addCallCenterCommunicate(CommunicateRecord communicateRecord, Integer loginUserId) {
        try {
            communicateRecord.setAddTime(DateUtil.sysTimeMillis());
            communicateRecord.setCreator(loginUserId);
            communicateRecord.setModTime(DateUtil.sysTimeMillis());
            communicateRecord.setUpdater(loginUserId);

            communicateRecordMapper.insertCallCenter(communicateRecord);

            //工作台-精准营销活动，需要更新活动-销售任务中的是否已沟通更新为已沟通。-Kerwin VDERP-15559
            //marketPlanApiService.updateMarketPlanTraderSendMsg(communicateRecord.getTraderId());

        } catch (Exception e) {
            logger.error("通话记录保持异常", e);
        }
    }

    @Override
    public Map<Integer, Integer> getTraderCommunicateCountByRecentDaysMap(List<Integer> customerIds, Integer communicationDays) {
        logger.info("getTraderCommunicateCountByRecentDaysMap customerIds:{}, communicationDays:{}", JSON.toJSONString(customerIds), communicationDays);
        if (CollectionUtils.isEmpty(customerIds)) {
            return new HashMap<>(8);
        }
        communicationDays = communicationDays - 9 > 0 ? communicationDays - 9 : 0;
        List<CustomerCountDto> traderCommunicateCountByRecentDaysList = communicateRecordMapper
                .getTraderCommunicateCountByRecentDaysMap(customerIds, communicationDays);
        if (CollectionUtils.isEmpty(traderCommunicateCountByRecentDaysList)) {
            logger.info("配置时间内暂无有效沟通记录 communicationDays:{}", communicationDays);
            return customerIds.stream().collect(Collectors.toMap(i -> i, i -> 0));
        }
        Map<Integer, Integer> resultMap = traderCommunicateCountByRecentDaysList.stream()
                .collect(Collectors.toMap(CustomerCountDto::getTraderCustomerId, CustomerCountDto::getMyCount));

        BitSet customerBitSet = BitsetUtils.collection2BitSet(customerIds);
        customerBitSet.andNot(BitsetUtils.collection2BitSet(resultMap.keySet()));
        resultMap.putAll(BitsetUtils.bitSet2List(customerBitSet).stream().collect(Collectors.toMap(i -> i, j -> 0)));

        return resultMap;
    }

    @Override
    public boolean isCommunicateSavedByCondition(String ccId, Integer userId) {
        if (StringUtil.isBlank(ccId) || userId == null || ErpConst.ZERO.equals(userId)) {
            return false;
        }
        CommunicateRecord communicate = null;
        try {
            communicate = communicateRecordMapper.getCommunicateByCoidAndUserId(ccId, userId);
        } catch (Exception e) {
            logger.error("ccId检索通话记录异常 ccId:{},userId:{},e:{}", ccId, userId, e);
        }
        return communicate != null;
    }

    @Override
    public CallingLineDto getCalloutNumber(String phone, Integer userId) {
        logger.info("getCalloutNumber phone:{}, userId:{}", phone, userId);
        if (StringUtil.isBlank(phone) || userId == null) {
            throw new RuntimeException("获取呼出主叫号码参数异常 phone:{}, userId:{}" + phone + userId);
        }

        User userInfo = userMapper.getUserByUserId(userId);
        if (userInfo == null) {
            throw new RuntimeException("id获取用户信息异常 userId:" + userId);
        }

        //老客户专线-联通线路停用-2025年4月 Kerwin
        Integer totalOrderNumber = saleorderMapper.getOrderNumberByPhone(phone);
        if (totalOrderNumber > 0) {
            logger.info("getOrderNumberByPhone totalOrderNumber:{}", totalOrderNumber  );
//            if (StringUtil.isBlank(userInfo.getCustomerLine())){
//                throw new RuntimeException("老客户线路未进行配置 userId：" + userId);
//            }
            if(StringUtils.isNotBlank(userInfo.getCustomerLine())) {
                logger.info("getCustomerLine:{}", userInfo.getCustomerLine());
                return new CallingLineDto(LandLineCodeEnum.CUSTOMER_LINE.getLineCode(), userInfo.getCustomerLine());
            }
        }

        HashMap<Integer, String> lineNumberMap = new HashMap<>(16);
        if (StringUtil.isNotBlank(userInfo.getUnicomLine())) {
            lineNumberMap.put(LandLineCodeEnum.UNICOM_LINE.getLineCode(), userInfo.getUnicomLine());
        }
        if (StringUtil.isNotBlank(userInfo.getMobileLine())) {
            lineNumberMap.put(LandLineCodeEnum.MOBILE_LINE.getLineCode(), userInfo.getMobileLine());
        }
        if (StringUtil.isNotBlank(userInfo.getTelecomLine())) {
            lineNumberMap.put(LandLineCodeEnum.TELECOM_LINE.getLineCode(), userInfo.getTelecomLine());
        }

        if (MapUtils.isEmpty(lineNumberMap)) {
            throw new RuntimeException("三大运营商均未进行配置 userId：" + userId);
        }

        //该号码最近5同电话通话总时长大于2min？
        RecentLandLineRecordDto recentRecordWithCondition = landLineRecordMapper.getRecentRecordWithCondition(phone);

        if (recentRecordWithCondition != null) {
            LandLineCallRecord recordInfo = landLineRecordMapper.getRecordInfoById(recentRecordWithCondition.getRecentRecordId());
            if (recordInfo == null) {
                logger.error("该号码大于2min最近一通电话信息异常 phone:{}", phone);
                throw new RuntimeException("该号码大于2min最近一通电话信息异常" + phone);
            }

            if (userId.equals(recordInfo.getCreator())) {
                if(lineNumberMap.keySet().contains(recordInfo.getLineCode())) {//前提是当前人有这个线路
                    logger.info("calloutNumber:{}", recordInfo.getCallingNumber());
                    return new CallingLineDto(recordInfo.getLineCode(), recordInfo.getCallingNumber());
                }
            }

            List<Integer> belongUserIdList = traderContactMapper.getBelongUserIdListByPhone(phone);
            //当前客户属于关联客户的归属销售或者最近一通呼出人 则使用最近一次接通线路记录呼出
            if ((CollectionUtils.isNotEmpty(belongUserIdList) && belongUserIdList.contains(userId))) {
                logger.info("calloutNumber:{}", recordInfo.getCallingNumber());
                if(lineNumberMap.keySet().contains(recordInfo.getLineCode())) {//前提是当前人有这个线路
                    return new CallingLineDto(recordInfo.getLineCode(), recordInfo.getCallingNumber());
                }
            }
            logger.info("calloutNumber:{}", lineNumberMap.get(recordInfo.getLineCode()));
            return new CallingLineDto(LandLineCodeEnum.NUMBER_POOL.getLineCode(), LandLineCodeEnum.NUMBER_POOL.getLineDesc());
        }

        Set<Integer> lineCodes = lineNumberMap.keySet();
        for (Integer lineCode : lineCodes) {
            Integer toadyUnConnectTimesTodayByLineCode = landLineRecordMapper.getToadyUnConnectTimesByLineCode(phone, lineCode);
            if (toadyUnConnectTimesTodayByLineCode < 2) {
                logger.info("calloutNumber:{}", lineNumberMap.get(lineCode));
                return new CallingLineDto(lineCode, lineNumberMap.get(lineCode));
            }
            Integer toadyUnConnectTimes = landLineRecordMapper.getToadyUnConnectTimesByLineCode(phone, null);
            if (toadyUnConnectTimes > lineCodes.size() + 1) {
                logger.info("calloutNumber:{}", LandLineCodeEnum.NUMBER_POOL.getLineDesc());
                return new CallingLineDto(LandLineCodeEnum.NUMBER_POOL.getLineCode(), LandLineCodeEnum.NUMBER_POOL.getLineDesc());
            }
        }
        return new CallingLineDto(LandLineCodeEnum.NUMBER_POOL.getLineCode(), LandLineCodeEnum.NUMBER_POOL.getLineDesc());
    }

    @Override
    public CallingLineDto callingLineSecondCheck(CallingLineDto callingLine, String phone, Integer userId) {
        if (callingLine == null || StringUtil.isBlank(phone) || userId == null){
            logger.info("callingLineSecondCheck phone:{},userId:{}", phone, userId);
            return callingLine;
        }
        List<LandLineCallRecord> phoneToadyLineRecords = landLineRecordMapper.getPhoneToadyLineRecords(phone, callingLine.getLineCode());
        if (CollectionUtils.isEmpty(phoneToadyLineRecords) || phoneToadyLineRecords.get(0).getIsConnect() > 1){
            logger.info("callingLineSecondCheck phone:{},userId:{}", phone, userId);
            return callingLine;
        }
        if (phoneToadyLineRecords.size() < unConnectRecordCount){
            logger.info("callingLineSecondCheck phone:{},userId:{}", phone, userId);
            return callingLine;
        }
        List<LandLineCallRecord> connectLineRecords = phoneToadyLineRecords.subList(0, unConnectRecordCount)
                .stream().filter(item -> item.getIsConnect() < 2).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(connectLineRecords) || connectLineRecords.size() < unConnectRecordCount){
            logger.info("callingLineSecondCheck phone:{},userId:{}", phone, userId);
            return callingLine;
        }
        return new CallingLineDto(LandLineCodeEnum.NUMBER_POOL.getLineCode(), LandLineCodeEnum.NUMBER_POOL.getLineDesc());
    }
}
