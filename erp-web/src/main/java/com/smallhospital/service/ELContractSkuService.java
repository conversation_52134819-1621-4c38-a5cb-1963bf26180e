package com.smallhospital.service;

import com.smallhospital.model.ContractSku;
import com.smallhospital.model.ElContractSku;
import com.smallhospital.model.vo.ElContractSkuVO;

import java.util.List;

public interface ELContractSkuService {

    List<ElContractSkuVO> getModifySkus(Integer contractId);

    List<ElContractSkuVO> findByContractId(Integer contractId);

    ElContractSkuVO findSkuNameById(Integer skuId);

    void batchAddContractSkus(List<ElContractSku> skuLists);

    void batchUpdateContractSkus(List<ContractSku> skuLists);

    void batchModifyContractSkus(List<ElContractSku> skuLists);
    void deleteById(Integer contactSkuId);

    void deleteModifySkuById(Integer contactModifySkuId);

    ElContractSku findById(Integer contactSkuId);

    void modify(ElContractSku sku);
}
