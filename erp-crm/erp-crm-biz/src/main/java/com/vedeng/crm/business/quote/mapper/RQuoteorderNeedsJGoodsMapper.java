package com.vedeng.crm.business.quote.mapper;

import com.vedeng.crm.business.quote.domain.entity.QuoteorderNeedsEntity;
import com.vedeng.crm.business.quote.domain.entity.RQuoteorderNeedsJGoodsEntity;

import java.util.List;

public interface RQuoteorderNeedsJGoodsMapper {
    int deleteByPrimaryKey(Long rQuoteorderNeedsJGoodsId);

    int insertSelective(RQuoteorderNeedsJGoodsEntity record);

    RQuoteorderNeedsJGoodsEntity selectByPrimaryKey(Long rQuoteorderNeedsJGoodsId);

    int updateByPrimaryKeySelective(RQuoteorderNeedsJGoodsEntity record);

    int updateByPrimaryKey(RQuoteorderNeedsJGoodsEntity record);

    RQuoteorderNeedsJGoodsEntity selectByQuoteorderGoodsId(Integer rQuoteorderNeedsJGoodsId);

    List<RQuoteorderNeedsJGoodsEntity> selectByQuoteorderId(Integer quoteorderId);

    int updateByQuoteGoodsId(RQuoteorderNeedsJGoodsEntity record);

    void batchDeleteByQuoteorderNeedsId(List<QuoteorderNeedsEntity> extraOldList);
}
