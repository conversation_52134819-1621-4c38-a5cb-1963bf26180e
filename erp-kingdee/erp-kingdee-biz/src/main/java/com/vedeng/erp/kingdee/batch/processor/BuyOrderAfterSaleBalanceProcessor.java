package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.vedeng.erp.kingdee.batch.dto.BatchCapitalBillDto;
import com.vedeng.erp.kingdee.dto.KingDeeNeedPayDto;
import com.vedeng.erp.kingdee.dto.KingDeeNeedPayEntityDto;
import com.vedeng.erp.kingdee.dto.KingDeeSupplierDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeSupplierMapper;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 采购售后收款
 */
@Slf4j
@Service
public class BuyOrderAfterSaleBalanceProcessor implements ItemProcessor<BatchCapitalBillDto, KingDeeNeedPayDto> {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private KingDeeSupplierMapper kingDeeSupplierMapper;

    private static final String TK = "退款";
    private static final String RebateTK = "结算返利余额收款";
    private static final String RebateTK531 = "返利余额收款";

    @Override
    public KingDeeNeedPayDto process(BatchCapitalBillDto batchCapitalBillDto) throws Exception {
        // 参数校验
        KingDeeNeedPayDto kingDeeNeedPayDto = paramCheck(batchCapitalBillDto);
        if (!Objects.isNull(kingDeeNeedPayDto)){
            // 组转数据
            buildEntity(kingDeeNeedPayDto,batchCapitalBillDto);
        }
        return kingDeeNeedPayDto;
    }

    /**
     * 组转数据
     * @param kingDeeNeedPayDto
     * @param batchCapitalBillDto
     */
    private void buildEntity(KingDeeNeedPayDto kingDeeNeedPayDto,BatchCapitalBillDto batchCapitalBillDto) {
        List<KingDeeNeedPayEntityDto> fEntity = Lists.newArrayList();
        KingDeeNeedPayEntityDto kingDeeNeedPayEntityDto = new KingDeeNeedPayEntityDto();
        kingDeeNeedPayEntityDto.setFQzokYsddh(batchCapitalBillDto.getOrderNo());
        kingDeeNeedPayEntityDto.setFQzokGsywdh(batchCapitalBillDto.getAfterSaleNo());
        boolean equals = batchCapitalBillDto.getTraderMode().equals(10000);
        boolean equals1 = batchCapitalBillDto.getBussinessType().equals(531);
        kingDeeNeedPayEntityDto.setFQzokYwlx(equals?(equals1?RebateTK531:RebateTK):TK);
        kingDeeNeedPayEntityDto.setFQzokTzje(batchCapitalBillDto.getAmount().abs().negate());
        fEntity.add(kingDeeNeedPayEntityDto);

        kingDeeNeedPayDto.setFid(KingDeeConstant.ZERO.toString());
        kingDeeNeedPayDto.setFQzokDate(DateUtil.formatDate(DateUtil.date(batchCapitalBillDto.getTraderTime())));
        kingDeeNeedPayDto.setFQzokJg(KingDeeConstant.ORG_ID.toString());
        kingDeeNeedPayDto.setFQzokBase(batchCapitalBillDto.getTraderSupplierId().toString());
        kingDeeNeedPayDto.setFEntity(fEntity);
    }

    /**
     * 参数校验
     * @param batchCapitalBillDto
     * @return
     */
    private KingDeeNeedPayDto paramCheck(BatchCapitalBillDto batchCapitalBillDto) {
        log.info("BuyOrderAfterSaleBalanceProcessor.process：{}" , JSON.toJSONString(batchCapitalBillDto));
        KingDeeSupplierDto kingDeeSupplierDto = kingDeeSupplierMapper.queryInfoBySupplierId(batchCapitalBillDto.getTraderSupplierId());
        if (Objects.isNull(kingDeeSupplierDto)){
            log.warn("采购售后收款对接应付余额调整单，ERP未查询到供应商推送记录，供应商ID:{}",batchCapitalBillDto.getTraderSupplierId());
            KingDeeSupplierDto dto = new KingDeeSupplierDto();
            dto.setFNumber(batchCapitalBillDto.getTraderSupplierId());
            boolean isExist = kingDeeBaseApi.isExist(dto);
            if (!isExist){
                log.warn("采购售后收款对接应付余额调整单，未查询到金蝶供应商数据，{}", JSON.toJSONString(dto));
                return null;
            }
        }

        KingDeeNeedPayDto kingDeeNeedPayDto = new KingDeeNeedPayDto();
        kingDeeNeedPayDto.setFBillNo(batchCapitalBillDto.getCapitalBillNo());
        boolean isExist = kingDeeBaseApi.isExist(kingDeeNeedPayDto);
        if (isExist){
            log.warn("采购售后收款对接应付余额调整单，发现数据已存在，{}", JSON.toJSONString(batchCapitalBillDto));
            return null;
        }
        return kingDeeNeedPayDto;
    }
}
