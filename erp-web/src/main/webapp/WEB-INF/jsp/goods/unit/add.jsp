<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="新增单位" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='<%= basePath %>static/js/unit/add.js?rnd=${resourceVersionKey}'></script>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<script src="/webjars/ezadmin/plugins/cascader/xm-select.js"></script>
<div class="addElement">
	<form action="" method="post" id="addUnitform">
		<ul class="add-detail">
			<li>
				<div class="infor_name">
					<span>*</span> 
					<lable for='unitGroupId'>单位分组</lable>
				</div>
				<div class="f_left">
					<select name="unitGroupId" id="unitGroupId" class="input-middle">
						<c:forEach var="list" items="${unitGroupList}" varStatus="status">
							<option value="${list.unitGroupId}">${list.groupName}</option>
						</c:forEach>
					</select>
				</div>
				<div class="clear"></div>
			</li>
			<li>
				<div class="infor_name">
					<span>*</span> 
					<lable for='unitName'>单位名称</lable>
				</div>
				<div class="f_left">
					<input type="text" name='unitName' id='unitName' class="input-middle" maxlength="10" />
				</div>
				<div class="clear"></div>
			</li>
			<li>
				<div class="infor_name">
					<span>*</span>
					<lable for='unitKingDee'>金蝶单位</lable>
				</div>
				<div class="f_left">
					<div id="mx-select" name="unitKingDee" style="width: 200px"></div>
					<input type="hidden" name="unitKingDeeNo" id="unitKingDeeNo" >
					<input type="hidden" name="unitKingDeeName" id="unitKingDeeName">

				</div>
				<span style="color: green">无对应单位，联系财务增加</span>
				<div class="clear"></div>
			</li>
			<li>
				<div class="infor_name">
					<lable for='unitNameEn'>英文名称</lable>
				</div>
				<div class="f_left">
					<input type="text" name='unitNameEn' id='unitNameEn' class="input-middle" />
				</div>
				<div class="clear"></div>
			</li>
			<li>
				<div class="infor_name">
					<span>*</span> 
					<lable for='sort' style="width:80px;">排序</lable>
				</div>
				<div class="f_left">
					<input type="text" name='sort' id='sort' class="input-middle" value="100" />
				</div>
				<div class="clear"></div>
			</li>
			<div class="clear"></div>
		</ul>
		<div class="add-tijiao tcenter">
			<button type="submit">提交</button>
			<button type="button" class="dele" id="cancle">取消</button>
		</div>
	</form>
</div>
<%@ include file="../../common/footer.jsp"%>