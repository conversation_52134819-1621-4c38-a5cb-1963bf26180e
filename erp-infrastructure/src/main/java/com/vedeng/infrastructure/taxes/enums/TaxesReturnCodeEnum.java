package com.vedeng.infrastructure.taxes.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 税金系统返回code
 * @date 2023/9/22 15:43
 */
public enum TaxesReturnCodeEnum {

    /**
     * 返回枚举
     */
    SUCCESS("0000", "成功"),
    FAIL("-1", "失败"),
    NO_LOGIN("0001", "未获取登录cookie信息"),
    AUTHENTICATION_FAIL("0002", "登录认证失效，请前往税务局登录认证"),
    POSSIBLE_EXCEPTION_FAIL("4444", "该发票存在异常的可能，请登录电子发票服务平台查看确认"),
    LOGIN_FAIL("9999", "登录失效"),

    /**
     * 金蝶返回500订单流水号不能重复=ERP超时
     */
    REPEAT("500", "流水号重复");

    /**
     * 错误代码
     */
    private final String code;

    /**
     * 错误信息
     */
    private final String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    TaxesReturnCodeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getMsg(String code) {
        TaxesReturnCodeEnum taxesReturnCodeEnum = Arrays.stream(TaxesReturnCodeEnum.values())
                .filter(enums -> enums.getCode().equals(code))
                .findFirst().orElse(TaxesReturnCodeEnum.FAIL);
        return taxesReturnCodeEnum.name;
    }

    public static TaxesReturnCodeEnum getEnum(String code) {
        return Arrays.stream(TaxesReturnCodeEnum.values())
                .filter(enums -> enums.getCode().equals(code))
                .findFirst().orElse(TaxesReturnCodeEnum.FAIL);
    }

}
