package com.vedeng.erp.kingdee.batch.writer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.erp.kingdee.batch.common.exception.BatchException;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.batch.dto.BatchKingDeePurchaseReceiptDto;
import com.vedeng.erp.kingdee.domain.entity.KingDeeStorageInEntity;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeStorageInMapper;
import com.vedeng.erp.kingdee.mapstruct.KingDeeStorageInCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeStorageInConvertor;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.RepoStatus;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购换货入库
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchPurchaseExchangeInWriter extends BaseWriter<KingDeeStorageInDto> {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;
    @Autowired
    private KingDeeStorageInCommandConvertor commandConvertor;
    @Autowired
    private KingDeeStorageInConvertor kingDeeStorageInConvertor;
    @Autowired
    private KingDeeStorageInMapper kingDeeStorageInMapper;

    @Override
    public void doWrite(KingDeeStorageInDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("采购换货入库 PurchaseExchangeInWriterService.doWrite：" + JSON.toJSONString(dto));
        ArrayList<String> needReturnFields = new ArrayList<>();
        needReturnFields.add("FEntity.FEntryID");
        needReturnFields.add("FEntity.F_QZOK_BDDJHID");

        RepoStatus save = kingDeeBaseApi.save(new SaveExtCommand<>(commandConvertor.toCommand(dto), dto.getFormId(), needReturnFields));

        ArrayList<SuccessEntity> successEntities = save.getSuccessEntitys();

        if (CollUtil.isEmpty(successEntities)) {
            //todo:无数据的异常处理
            throw new BatchException("调用金蝶其他入库接口生成失败");
        }

        SuccessEntity successEntity = CollUtil.getFirst(successEntities);
        // 回写主表id
        dto.setFId(successEntity.getId());
        // 回写明细id
        List<Map<String, Object>> returnData = successEntity.returnData(needReturnFields);
        if (CollUtil.isNotEmpty(returnData)) {
            // 单条新增所以仅有一条记录
            Map<String, Object> returnMap = CollUtil.getFirst(returnData);
            List<JSONObject> jsonObjectList = Convert.toList(JSONObject.class, returnMap.get("FEntity"));
            dto.getFEntity().forEach(c -> {
                String fEntryId = jsonObjectList.stream().filter(o -> o.get("F_QZOK_BDDJHID").equals(c.getFQzokBddjhId()))
                        .map(o -> o.getString("FEntryID"))
                        .findFirst()
                        .orElseThrow(() -> new BatchException("金蝶其他入库详细行id错误:" + JSON.toJSONString(jsonObjectList)));
                c.setFEntryId(fEntryId);
            });
        }
        KingDeeStorageInEntity kingDeeStorageInEntity = kingDeeStorageInConvertor.toEntity(dto);
        log.info("采购换货入库存入数据库:{}", JSON.toJSONString(kingDeeStorageInEntity));
        kingDeeStorageInMapper.insertSelective(kingDeeStorageInEntity);

        // 传递参数
        BatchKingDeePurchaseReceiptDto build = BatchKingDeePurchaseReceiptDto.builder()
                .outInNo(dto.getFBillNo())
                .warehouseGoodsOutInId(Long.valueOf(dto.getFQzokBddjtId()))
                .fId(dto.getFId())
                .build();
        List<BatchKingDeePurchaseReceiptDto> purchaseInData = (List<BatchKingDeePurchaseReceiptDto>) getStepParameter("purchaseInData");
        List<BatchKingDeePurchaseReceiptDto> batchKingDeePurchaseReceiptDtos = new ArrayList<>();
        if (CollUtil.isEmpty(purchaseInData)) {
            batchKingDeePurchaseReceiptDtos.add(build);
            saveStepParameter("purchaseInData", batchKingDeePurchaseReceiptDtos);
            saveStepParameter("formId", dto.getFormId());
        } else {
            purchaseInData.add(build);
        }

    }


}
