<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
            + path + "/";
%>
<c:set var="path" value="<%=basePath%>" scope="application" />
<link rel="stylesheet" href="<%=basePath%>static/css/general.css?rnd=${resourceVersionKey}" />
<script src="${pageContext.request.contextPath}/static/js/echarts.min.js"></script>
<link rel="stylesheet" href="<%=basePath%>static/css/manage.css" />
<link rel="stylesheet" href="<%=basePath%>static/new/css/common/font/font.css" />
<link rel="stylesheet" href="<%=basePath%>static/css/trader/iconfont.css" />

<style type="text/css">
    .el-descriptions-item__container{
        padding-left: 15px;
    }
    .el-descriptions__body{
        padding-top: 10px;
    }
    .el-tag--small {
        margin-left: 10px;
    }
    .el-table .el-table__cell {
        padding: 6px 0;
    }

    .parts{
        padding-top: 10px;
    }
    .vd-tip.detail-block-tip {
        border: 0;
        border-radius: 0;
        padding: 9px 20px 9px 46px;
        margin-bottom: 10px; }
    .vd-tip.detail-block-tip .vd-tip-icon {
        left: 20px;
        top: 7px; }

    .detail-block-option {
        padding: 0 20px;
        margin-bottom: 10px; }
    .vd-icon {
        /*font-family: 'HC' !important;*/
        speak: none;
        font-style: normal;
        font-weight: normal;
        font-variant: normal;
        text-transform: none;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 16px;
    }
    .icon-info2:before {
        content: "\e91e";
    }


</style>

<%@ include file="../../../jsp/trader/customer/terminal_tag.jsp"%>

<div id="app" style="display: none;">
    <el-card class="box-card J-block" data-ref="jbxx" ref="jbxx">
        <el-descriptions title="基本信息" :column="2">
            <el-descriptions-item label="客户名称" v-if="terminalPortrait.hosName">{{terminalPortrait.hosName}}</el-descriptions-item>
            <el-descriptions-item label="别称" v-if="terminalPortrait.aliasName">{{terminalPortrait.aliasName}}</el-descriptions-item>

            <el-descriptions-item label="法人" v-if="terminalPortrait.legalRepresentative">{{terminalPortrait.legalRepresentative}}</el-descriptions-item>
            <el-descriptions-item label="省市区" v-if="terminalPortrait.province || terminalPortrait.city || terminalPortrait.area">
                {{terminalPortrait.province + " " + terminalPortrait.city + " " + terminalPortrait.area}}
            </el-descriptions-item>

            <el-descriptions-item label="终端大类" v-if="terminalPortrait.hosTerminalType">{{terminalPortrait.hosTerminalType}}</el-descriptions-item>
            <el-descriptions-item label="机构等级" v-if="terminalPortrait.hosLevel">{{terminalPortrait.hosLevel}}</el-descriptions-item>

            <el-descriptions-item label="机构性质" v-if="terminalPortrait.hosModel">{{terminalPortrait.hosModel}}</el-descriptions-item>
            <el-descriptions-item label="机构类型" v-if="terminalPortrait.hosType || terminalPortrait.hosSmallType">
                {{terminalPortrait.hosType}} <span v-if="terminalPortrait.hosSmallType">{{"（" + terminalPortrait.hosSmallType + "）"}}</span>
            </el-descriptions-item>

            <el-descriptions-item label="经营状态" v-if="terminalPortrait.businessStatus">{{terminalPortrait.businessStatus}}</el-descriptions-item>
            <el-descriptions-item label="成立时间" v-if="terminalPortrait.firstRegDate">{{parseTime(terminalPortrait.firstRegDate, '{y}-{m}-{d}')}}</el-descriptions-item>

            <el-descriptions-item label="科室" v-if="terminalPortrait.hosDepartmentList">{{terminalPortrait.hosDepartmentList}}</el-descriptions-item>
            <el-descriptions-item label="注册资本" v-if="terminalPortrait.funding">{{terminalPortrait.funding}}</el-descriptions-item>

            <el-descriptions-item label="注册地址" v-if="terminalPortrait.regAddr">{{terminalPortrait.regAddr}}</el-descriptions-item>
            <el-descriptions-item label="电话" v-if="terminalPortrait.tel">{{terminalPortrait.tel}}</el-descriptions-item>

            <el-descriptions-item label="手机" :span="2" v-if="terminalPortrait.mobile">{{terminalPortrait.mobile}}</el-descriptions-item>

            <el-descriptions-item label="经营范围" :span="2" v-if="terminalPortrait.businessScope">{{terminalPortrait.businessScope}}</el-descriptions-item>
        </el-descriptions>
    </el-card>

    <el-card class="box-card J-block" data-ref="jxll" ref="jxll" v-if="showDistributionLink">
        <div slot="header" class="clearfix">
            <span>经销链路</span>
        </div>
        <div>
            <div style="margin-top: 10px;margin-bottom: 10px;">
                <span>来源：</span>
                <select id="linkSourceType" name="linkSourceType" onchange="javascript:vm.linkSourceType=this.value;refreshRela2();">
                    <option value="0">全部</option>
                    <option value="1">中标数据</option>
                    <option value="2">贝登交易</option>
                    <option value="3">商机</option>
                    <option value="4">报价</option>
                </select>
                <span>时间：</span>
                <select id="cooperationTimeFrame" name="cooperationTimeFrame" onchange="javascript:vm.cooperationTimeFrame=this.value;refreshRela2();">
                    <option value="3">近三年</option>
                    <option value="2">近二年</option>
                    <option value="1">近一年</option>
                </select>
            </div>
            <div id="distributionLink" style="">
            </div>
        </div>
    </el-card>

    <div id="category-wrap-div" class="category-wrap" :class="{hide: !isShowCategory}">
        <div class="slide-btn" @click="toggleShowCategory">
            <i class="el-icon-arrow-right" v-if="isShowCategory"></i>
            <i class="el-icon-arrow-left" v-else></i>

        </div>
        <div class="category-item"  :class="{active: item.ref === categoryIndex}" v-for="(item, index) in categoryList" @click="scrollTo(item.ref, index)">{{item.label}}</div>
    </div>
</div>

<script src="${pageContext.request.contextPath}/static/api/trader/traderCustomer.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/js/d3.js" charset="utf-8"></script>

<script type="text/javascript">
    const searchName = '${searchName}';
    let vm = new Vue({
        el: '#app',
        data() {
            return {
                timer:null,//定时器，进行圆环关系处理
                cards: [],
                activities: [],
                isShowTmeMore: false,
                reverse: false,
                terminalPortrait: {},
                categoryList: [{
                    label: '基本信息',
                    ref: 'jbxx'
                }
                ],
                brandTagList: [],
                // 根据客户类型 和 客户性质分类。1： 科研终端；2：科研分销；3：临床终端；4：临床分销
                customerClass: 0,
                customerTypeSpan: 2,
                timeLinePage: 1,
                isShowCategory: true,
                categoryIndex: 0,

                // 经销链路
                linkSourceType: 0,
                cooperationTimeFrame: 3,
                showDistributionLink: false
            }
        },

        mounted() {
            loadingApp();
            this.$nextTick(() => {
                this.checkCategoryIndex();
            })
            let _this = this;
            $(window).on('scroll', function () {
                _this.checkCategoryIndex();
            })
        },

        created() {
            this.initPageData();
        },

        methods: {
            async initPageData() {

                await getTraderCustomerTerminalPortrait({"searchName": searchName}).then(res => {
                    this.terminalPortrait = res.data.data;
                });

                await getTerminalDistributionLinkD3(searchName, 0, 3).then(res => {
                    if (res.data.data.edges.length > 0) {
                        this.showDistributionLink = true;
                        this.categoryList.splice(4, 0, {
                            label: '经销链路',
                            ref: 'jxll'
                        });
                    }
                });

                const that = this;
                that.timer = setInterval(function () {
                    if (document.readyState === 'complete') {
                        window.clearInterval(that.timer);
                        refreshRela2();
                    }
                }, 1000);
            },

            scrollTo(ref, index){
                if(!this.$refs[ref]){
                    return;
                }
                let top = this.$refs[ref].$el.offsetTop;
                window.scrollTo(0, top);

                setTimeout(() => {
                    this.categoryIndex = ref;
                }, 200)
            },

            toggleShowCategory(){
                this.isShowCategory = !this.isShowCategory;
            },

            checkCategoryIndex(){
                let top = $(window).scrollTop();
                let ref = "";

                $('.J-block').each(function(i, item){
                    if($(this).offset().top - 40 < top){
                        ref = $(this).data('ref');
                    }
                });

                this.categoryIndex = ref;
            }
        }
    })
</script>

<script>
    var colorArrayAll = ["#ec7c7d","#ef6191","#b969ca","#9d7ced","#7777db","#5d81a5","#4cb7ff","#58c9c9","#5ec9a6","#59d25a","#b3dd62","#f5d44d","#fe934d","#a47d70","#E1E5E8"];//定义15个不同的颜色值，不变
    var colorArray = colorArrayAll;   //随机数取颜色值，取一个移除一个
    function getColor() {
        var index = Math.floor(Math.random()*colorArray.length);
        var current = colorArray[index];
        colorArray = colorArray.filter(function(item) {
            return item !== current;
        });
        if(colorArray.length == 0){
            colorArray =colorArrayAll;
        }
        return current;
    }

    var width = 800;
    var height = 600;
    var img_w = 60;
    var img_h = 60;
    var radius = 20;    //圆形半径调整为20



    // 查询组装关联信息
    var currentSearchName = '${searchName}';
    $(document).ready(function() {

    });

    function refreshRela2() {
        d3.select("#distributionLink").html('');
        var linkSourceType = vm.linkSourceType;
        var cooperationTimeFrame = vm.cooperationTimeFrame;
        var url = '/traderCustomerBase/terminalDistributionLink/d3.do?searchName='+currentSearchName + '&linkSourceType=' + linkSourceType + '&cooperationTimeFrame=' + cooperationTimeFrame;
        var svg = d3.select("#distributionLink").append("svg")
            .attr("width",width)
            .attr("height",height);
        d3.json(url,function(error,root){
            root = root.data;
            if(root.nodes.length <=1){
                d3.select("#distributionLink").html("<div style='justify-content: center;display: flex;align-items: center;text-align:center;width:"+width+"px;height:"+height+"px;'>暂无数据</div>");
                return ;
            }
//D3力导向布局
            var force = d3.layout.force()
                .nodes(root.nodes)
                .links(root.edges)
                .size([width,height])
                .linkDistance(200)
                .charge(-1500)
                .start();

//边
            var edges_line = svg.selectAll("line")
                .data(root.edges)
                .enter()
                .append("line")
                .style("stroke","#ccc")
                .style("stroke-width",1);

//边上的文字（人物之间的关系）
            var edges_text = svg.selectAll(".linetext")
                .data(root.edges)
                .enter()
                .append("text")
                .attr("class","linetext")
                .text(function(d){
                    switch (linkSourceType) {
                        case "0":
                            return "";
                        case "1":
                            return (d.relation==undefined || d.relation == ""|| d.relation == null)?"中标0次":("中标"+d.relation+"次");
                        case "2":
                            return (d.relation==undefined || d.relation == ""|| d.relation == null)?"交易0次":("交易"+d.relation+"次");
                        case "3":
                            return (d.relation==undefined || d.relation == ""|| d.relation == null)?"商机0次":("商机"+d.relation+"次");
                        case "4":
                            return (d.relation==undefined || d.relation == ""|| d.relation == null)?"报价0次":("报价"+d.relation+"次");
                        default:
                            return "";
                    }
                });

            let maxRelation = 10; // 初始值设置10

            root.nodes.forEach(node => {
                    if (node.relation !== null && node.relation > maxRelation) {
                    maxRelation = node.relation;
                }
            });

            // 圆形图片节点（人物头像）
            var nodes_img = svg.selectAll("image")
                .data(root.nodes)
                .enter()
                .append("circle")
                .attr("class", "circleImg")
                .attr("r", function (d,i) {
                    if(d.relation==undefined || d.relation == ""|| d.relation == null){
                        return radius;
                    }else{
                        return radius +((d.relation>100)?10:d.relation/(maxRelation>10?10:1));

                    }
                })
                .attr("fill", function(d, i){
//创建圆形图片
                    var defs = svg.append("defs").attr("id", "imgdefs")
                    var catpattern = defs.append("pattern")
                        .attr("id", "catpattern" + i)
                        .attr("height", 1)
                        .attr("width", 1)
                    catpattern.append("image")
                        .attr("x", - (img_w / 2 - radius))
                        .attr("y", - (img_h / 2 - radius))
                        .attr("width", img_w)
                        .attr("height", img_h)
                    return getColor();
                }).call(force.drag);


            var text_dx = -20;
            var text_dy = 20;

            var nodes_text = svg.selectAll(".nodetext")
                .data(root.nodes)
                .enter()
                .append("text")
                .attr("class","nodetext")
                .attr("dx",text_dx)
                .attr("dy",text_dy)
                .text(function(d){
                    return d.name;
                });


            force.on("tick", function(){

//限制结点的边界
                root.nodes.forEach(function(d,i){
                    d.x = d.x - img_w/2 < 0     ? img_w/2 : d.x ;
                    d.x = d.x + img_w/2 > width ? width - img_w/2 : d.x ;
                    d.y = d.y - img_h/2 < 0      ? img_h/2 : d.y ;
                    d.y = d.y + img_h/2 + text_dy > height ? height - img_h/2 - text_dy : d.y ;
                });

//更新连接线的位置
                edges_line.attr("x1",function(d){ return d.source.x; });
                edges_line.attr("y1",function(d){ return d.source.y; });
                edges_line.attr("x2",function(d){ return d.target.x; });
                edges_line.attr("y2",function(d){ return d.target.y; });

//更新连接线上文字的位置
                edges_text.attr("x",function(d){ return (d.source.x + d.target.x) / 2 ; });
                edges_text.attr("y",function(d){ return (d.source.y + d.target.y) / 2 ; });


//更新结点图片和文字
                nodes_img.attr("cx",function(d){ return d.x });
                nodes_img.attr("cy",function(d){ return d.y });

                nodes_text.attr("x",function(d){ return d.x });
                nodes_text.attr("y",function(d){ return d.y + img_w/2; });
            });
        });
    }
</script>

<style>
    .el-descriptions__title {
        margin-left: 15px;
    }

    .el-descriptions__header {
        margin-bottom: 9px;
        padding-top: 9px;
    }

    .el-descriptions {
        background-color: #c5ddfb;
    }

    .el-descriptions-item__cell.el-descriptions-item__content {
        text-align: center !important;
    }

    .el-descriptions-item__cell.el-descriptions-item__label.is-bordered-label {
        text-align: center;
        width: 10%;
    }

    .el-input--mini .el-input__icon {
        line-height: 0px;
    }

    .el-card__header {
        height: 34px;
        background-color: #c5ddfb;
        box-sizing: border-box;
        padding-left: 15px;
        font-weight: 700;
        padding-top: 9px;
        padding-bottom: 30px;
        color: #303133;
    }

    .el-card__body {
        padding: 0;
    }

    .el-dialog__header {
        background-color: #f3f3f3;
        border-bottom: 1px solid #ddd;
        border-radius: 2px 2px 0 0;
        height: 35px;
        padding: 7px 15px 0;
    }

    .el-dialog__title {
        font-size: 14px;
        margin-left: 0;
    }

    .el-dialog__headerbtn {
        margin-top: -5px;
    }

    .el-dialog__body {
        padding: 20px 20px;
    }

    .my-content {
        width: 40%;
    }

    .category-wrap {
        position: fixed;
        width: 150px;
        background: #fff;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        z-index: 11;
        border: 1px solid #ced3d9;
        padding: 0;
        transition: right .22s ease;
    }

    .category-wrap.hide {
        right: -150px;
    }

    .category-wrap .category-item {
        padding: 0 20px;
        line-height: 40px;
        height: 40px;
        cursor: pointer;
    }

    .category-wrap .category-item:hover {
        background: #eee;
    }

    .category-wrap .category-item.active {
        background: #c5ddfb;
    }

    .category-wrap .slide-btn {
        position: absolute;
        left: -30px;
        top: calc(50% - 25px);
        width: 30px;
        background: #fff;
        border: 1px solid #ced3d9;
        border-radius: 3px 0 0 3px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }

    .category-wrap .slide-btn:hover {
        color: #09f;
    }

    .nodetext {
        font-size: 12px ;
        font-family: SimSun;
        fill:#000000;
    }

    .linetext {
        font-size: 12px ;
        font-family: SimSun;
        fill:#1f77b4;
        fill-opacity:1.0;
    }

    .circleImg {
        stroke-width: 1.5px;
    }

    .card {
        width: 200px;
        height: 100px;
        background-color: #a6a6a6;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        margin: 10px;
        user-select: none;
    }

    .selected {
        background-color: #1e80ff;
        color: white;
    }

    .el-timeline-item__node {
        position: absolute;
        background-color: #1e80ff;
        border-radius: 50%;
        display: flex;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
    }

    .el-card__header .clearfix{
        font-size: 16px;
        font-weight: 700;
    }
    .el-timeline-item__timestamp {
        width: 130px;
        text-align: right;
    }
    .el-timeline-item__timestamp {
        position: absolute;
        left: -140px;
        top: 0;
        transform: translateY(-50%);
    }

    .el-dialog__headerbtn {
        font-size: 13px;
    }

    .el-backtop i {
        background: none !important;
    }

    .el-backtop {
        width: 65px;
        height: 65px;
        font-size: 12px;
        border: 2px solid;
        color: #7c8083;
    }

    .my-card .el-card__header {
        background-color: #d5d9dd;
    }

    .container {
        display: flex;
        align-items: center;
    }

    .circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #D5D9DD;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .text {
        color: rgba(0, 0, 0, 0.97);
        font-size: 12px;
    }

    .row {
        margin-left: 10px;
    }
</style>