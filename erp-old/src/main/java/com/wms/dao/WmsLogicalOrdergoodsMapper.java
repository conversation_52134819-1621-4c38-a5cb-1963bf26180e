package com.wms.dao;

import com.vedeng.aftersales.model.AfterSales;
import com.wms.model.po.WmsLogicalOrdergoods;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface WmsLogicalOrdergoodsMapper {
    int deleteByPrimaryKey(Integer logicalOrderGoodsId);

    int insert(WmsLogicalOrdergoods record);

    int insertSelective(WmsLogicalOrdergoods record);

    WmsLogicalOrdergoods selectByPrimaryKey(Integer logicalOrderGoodsId);

    int updateByPrimaryKeySelective(WmsLogicalOrdergoods record);

    int updateByPrimaryKey(WmsLogicalOrdergoods record);

    /**
     * 更新ERP库存占用信息(根据库存转移单ID)
     * @param wmsLogicalOrdergoods
     * @return
     */
    int updateOccupyNum(WmsLogicalOrdergoods wmsLogicalOrdergoods);
    int updateIsdelte(WmsLogicalOrdergoods wmsLogicalOrdergoods);

    /**
     * @description: 获取以保存逻辑仓订单商品数据
     * @return: List<WmsLogicalOrdergoods>
     * @author: Strange
     * @date: 2020/7/27
     **/
    List<WmsLogicalOrdergoods> getLogicalOrderInfoGroupByRelateId( @Param("list") List<Integer> relateIdList,@Param("operateType") Integer operateType);

    /**
     * @description: 单据类型获取数据
     * @return:
     * @author: Strange
     * @date: 2020/7/30
     **/
    List<WmsLogicalOrdergoods> getorderChooseInfoByOptType(@Param("optType")Integer optType);

    /**
     * @description: 获取销售单选择逻辑仓信息
     * @return:  List<WmsLogicalOrdergoods>
     * @author: Strange
     * @date: 2020/8/3
     **/
    List<WmsLogicalOrdergoods> getSaleorderLogicalChooseInfoByNo(String saleorderNo);

    WmsLogicalOrdergoods getOrderByCon(WmsLogicalOrdergoods wmsLogicalOrdergoods);

    /**
     * @description: 获取信息通过关联id和业务类型
     * @return: List<WmsLogicalOrdergoods>
     * @author: Strange
     * @date: 2020/8/6
     **/
    List<WmsLogicalOrdergoods> getLogicalInfoByTypeAndRelate(WmsLogicalOrdergoods search);

    List<WmsLogicalOrdergoods> getAfterSaleRelateLogicOrderGood(Map afterSaleDB);

    int updateLogicalNum(WmsLogicalOrdergoods wmsLogicalOrdergoods);

    /**
     * @description: 售后单号获取信息
     * @return:
     * @author: Strange
     * @date: 2020/8/14
     **/
    List<WmsLogicalOrdergoods> getAfterorderLogicalChooseInfoByNo(@Param("afterNo") String afterNo);

    /**
     * @description: 初始化销售
     * @return:
     * @author: Strange
     * @date: 2020/8/20
     **/
    int updateSaleIsdelte();
    /**
     * @description: 初始化售后
     * @return:
     * @author: Strange
     * @date: 2020/8/20
     **/
    int updateAfterIsdelte();

    /**
     * @description: 获取已关闭但依然有占用的订单
     * @return: List<WmsLogicalOrdergoods>
     * @author: Strange
     * @date: 2021/1/25
     **/
    List<WmsLogicalOrdergoods> getCloseOrderErrorOccupy();

    List<WmsLogicalOrdergoods> getorderErrorOccupy();

    /**
     * @description: 获取售后类型逻辑仓
     * @return:
     * @author: Strange
     * @date: 2021/7/7
     **/
    List<WmsLogicalOrdergoods> getAfterorderLogicalChooseInfo(@Param("afterSalesId") Integer afterSalesId, @Param("operaType") Integer operaType);

    WmsLogicalOrdergoods getOutOrderByCon(WmsLogicalOrdergoods queryCon);

    /**
     * 查询之前的分摊逻辑
     * @param wmsUnitConversionId
     * @param operateType
     * @return
     */
    List<WmsLogicalOrdergoods> selectUnitConversionData(@Param("wmsUnitConversionId") Integer wmsUnitConversionId, @Param("operateType") Integer operateType);

    List<WmsLogicalOrdergoods> selectByRelateIdAndOperateType(@Param("relateId") Integer relateId,@Param("operateType") Integer operateType);

}
