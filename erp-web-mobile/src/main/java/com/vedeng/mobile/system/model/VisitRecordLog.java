package com.vedeng.mobile.system.model;

import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2023/12/6
 */
/**
    * 操作记录表
    */
public class VisitRecordLog {
    /**
    * 记录ID
    */
    private Integer id;

    /**
    * 用户ID
    */
    private Integer userId;

    /**
    * 操作时间
    */
    private Date operationTime;

    /**
    * 操作类型A创建B修改C打卡D添加拜访记录E删除
    */
    private String operationType;

    /**
    * 描述
    */
    private String description;

    /**
    * 关联表的ID
    */
    private Integer recordId;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Date getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(Date operationTime) {
        this.operationTime = operationTime;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getRecordId() {
        return recordId;
    }

    public void setRecordId(Integer recordId) {
        this.recordId = recordId;
    }
}