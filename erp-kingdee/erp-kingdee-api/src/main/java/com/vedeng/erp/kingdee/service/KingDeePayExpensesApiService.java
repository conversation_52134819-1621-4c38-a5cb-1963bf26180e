package com.vedeng.erp.kingdee.service;

import com.vedeng.erp.kingdee.dto.KingDeePayExpensesDto;
import com.vedeng.erp.kingdee.dto.result.KingDeePayCommonQueryResultDto;
import com.vedeng.erp.kingdee.dto.result.KingDeePayExpensesQueryResultDto;
import com.vedeng.infrastructure.kingdee.service.KingDeeMqBaseService;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/3/21 19:34
 **/
public interface KingDeePayExpensesApiService extends KingDeeMqBaseService<KingDeePayExpensesDto> {

    /**
     * 根据采购票的id 查询应付的那
     * @param invoiceId 票id
     * @return List<KingDeePayCommonQueryResultDto>
     */
    List<KingDeePayExpensesQueryResultDto> getKingDeePayExpenses(Integer invoiceId);
}
