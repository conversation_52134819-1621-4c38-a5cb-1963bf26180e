package com.vedeng.billsync.dao;

import com.vedeng.billsync.dao.generate.BankBillMapper;
import com.vedeng.billsync.task.model.entity.BankBillExtDo;
import com.vedeng.order.model.Saleorder;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface BankBillExtMapper extends BankBillMapper {

    BankBillExtDo getAliBankBillExtDoByTranFlow(BankBillExtDo bankBillExtDo);

    void insertSingleByObject(BankBillExtDo bankBillExtDo);

    BankBillExtDo getWechatBankBillExtDoByTranFlow(BankBillExtDo e);

    void deleteByDate(@Param("queryDate") Date queryDate, @Param("tag") Integer tag);

    void updateSingleByObjectSelective(BankBillExtDo bankBillExtDo);


    BankBillExtDo getBankBillById(Integer bankBillId);

    void updataBankBillMatchedAmount(@Param("bankBillId") Integer bankBillId, @Param("newAmount") BigDecimal newAmount);

    void updataBankBillReceiptUrl(@Param("bankBillId") Integer bankBillId, @Param("receiptUrl") String receiptUrl);

    /**
     * 查询需要下载回单的银行流水
     * @param beginTime
     * @param endTime
     * @param bankTag
     * @param flag1
     * @return
     */
    List<BankBillExtDo> queryNeedDownLoadBankBill(@Param("beginTime") Date beginTime, @Param("endTime") Date endTime,
                                        @Param("bankTag") Integer bankTag);

    List<BankBillExtDo> queryBankBillOfNoReceiptUrl(@Param("beginTime") String beginTime,
                                                    @Param("endTime") String endTime,
                                                    @Param("bankTagList") List<Integer> bankTagList,
                                                    @Param("bankBillId") Integer bankBillId);

    /**
     * 根据银行流水id查询需要推送的流水
     * @param bankBillIds
     * @return
     */
    List<BankBillExtDo> queryBankBillByIdList(@Param("bankBillIds") List<Integer> bankBillIds);

    /**
     * 根据流水号查询银行流水信息
     */
    List<BankBillExtDo> findBankByTranFlow(@Param("receiveRecordBankNum") String receiveRecordBankNum);

    /**
     * 更新回单url
     * <AUTHOR>
     * @param bankBillId
     * @param receiptUrl
     * @return
     */
    int updataReceiptUrl(@Param("bankBillId")Integer bankBillId,@Param("receiptUrl")String receiptUrl);


    /**
     *
     * <b>Description:</b><br>匹配可能是的订单
     * @param amt
     * @param accName1
     * @param bankBillId
     * @return
     * @Note
     * <b>Author:</b> Michael
     * <br><b>Date:</b> 2017年9月18日 上午10:49:18
     */
    List<Saleorder> getMatchInfo(@Param("amt") BigDecimal amt, @Param("accName1")String accName1, @Param("bankBillId")Integer bankBillId);


    /** @description: VDERP-1447 匹配出的订单，在订单结款列表页点击“确认”无反应.
     * @notes: .
     * @author: Tomcat.Hui.
     * @date: 2019/11/14 13:38.
     * @param saleorderNo
     * @return: java.util.List<com.vedeng.model.order.Saleorder>.
     * @throws: .
     */
    List<Saleorder> getMatchInfoByOrderNo(@Param("saleorderNo")String saleorderNo);

    /**
     * 根据资金匹配流水号查询支付宝流水信息
     */
    List<BankBillExtDo> findAlipayByCapitalSearchFlow(@Param("receiveRecordAlipayNum") String receiveRecordAlipayNum);

    List<BankBillExtDo> queryCcbByTranDate(@Param("beginDate") String beginDate, @Param("endDate") String endDate);

}