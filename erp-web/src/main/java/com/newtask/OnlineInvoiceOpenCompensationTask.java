package com.newtask;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.saleorder.constant.OnlineBusinessTypeEnum;
import com.vedeng.erp.saleorder.dao.OnlineInvoiceMessageMapper;
import com.vedeng.erp.saleorder.exception.OnlineInvoiceOpenException;
import com.vedeng.erp.saleorder.model.dto.ExpressReceiptDto;
import com.vedeng.erp.saleorder.model.dto.InvoiceOpenInfoDto;
import com.vedeng.erp.saleorder.model.po.OnlineInvoiceMessagePo;
import com.vedeng.erp.saleorder.service.OnlineInvoiceOpenService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 在线签收开票补偿定时任务
 *
 * <AUTHOR>
 */
@Component
@JobHandler(value = "onlineInvoiceOpenCompensationTask")
public class OnlineInvoiceOpenCompensationTask extends AbstractJobHandler {

    public static final Logger logger = LoggerFactory.getLogger(OnlineInvoiceOpenCompensationTask.class);

    @Autowired
    private OnlineInvoiceOpenService onlineInvoiceOpenService;

    @Resource
    private OnlineInvoiceMessageMapper onlineInvoiceMessageMapper;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        XxlJobLogger.log("CategorySalesCalculationTask -----------" + s);

        List<OnlineInvoiceMessagePo> onlineInvoiceMessages = StringUtils.isBlank(s) ? onlineInvoiceMessageMapper.getUnHandleRecords() :
                onlineInvoiceMessageMapper.getOnlineInvoiceMessageByIds(JSON.parseArray(s, Integer.class).stream().distinct().collect(Collectors.toList()));

        if (CollectionUtils.isEmpty(onlineInvoiceMessages)) {
            return SUCCESS;
        }

        onlineInvoiceMessages.forEach(item -> {
            switch (OnlineBusinessTypeEnum.getInstance(item.getBusinessKey())) {
                case ONLINE_EXPRESS_RECEIPT: {
                    try {
                        onlineInvoiceOpenService.dealExpressReceiptBusiness(JSON.parseArray(item.getMessageBody(), ExpressReceiptDto.class));
                        logger.info("在线签收业务补偿成功 message:{}", item.getMessageBody());
                        onlineInvoiceMessageMapper.updateOnlineInvoiceMessage(item.getId(), ErpConst.ONE, System.currentTimeMillis());
                    } catch (RuntimeException e) {
                        logger.error("在线签收出现报文类异常，已记入数据库 message:{}, e:{}", item.getMessageBody(), e);
                        onlineInvoiceMessageMapper.updateOnlineInvoiceMessage(item.getId(), ErpConst.TWO, System.currentTimeMillis());
                    } catch (Exception e) {
                        logger.error("在线签收业务补偿出现本地异常 message:{}, e:{}", item.getMessageBody(), e);
                        onlineInvoiceMessageMapper.updateOnlineInvoiceMessage(item.getId(), ErpConst.ZERO, System.currentTimeMillis());
                    }
                    break;
                }
                case ONLINE_INVOICE_OPEN: {
                    try {
                        InvoiceOpenInfoDto invoiceOpenInfoDto = JSON.parseObject(item.getMessageBody(), InvoiceOpenInfoDto.class);
                        onlineInvoiceOpenService.dealDataBeforeInvoiceApply(invoiceOpenInfoDto);
                        onlineInvoiceOpenService.dealInvoiceOpenBusiness(invoiceOpenInfoDto);
                        logger.info("在线开票补偿消费成功 item:{}", JSON.toJSONString(item));
                        onlineInvoiceMessageMapper.updateOnlineInvoiceMessage(item.getId(), ErpConst.ONE, System.currentTimeMillis());
                    } catch (OnlineInvoiceOpenException e) {
                        logger.error("在线开票出现业务类异常，已下传结果 item:{}, e:{}", JSON.toJSONString(item), e);
                        onlineInvoiceMessageMapper.updateOnlineInvoiceMessage(item.getId(), ErpConst.TWO, System.currentTimeMillis());
                        return;
                    } catch (RuntimeException e) {
                        logger.error("在线开票出现报文信息错误 item:{},e:{}", JSON.toJSONString(item), e.getMessage());
                        onlineInvoiceMessageMapper.updateOnlineInvoiceMessage(item.getId(), ErpConst.TWO, System.currentTimeMillis());
                        return;
                    } catch (Exception e) {
                        logger.error("在线开票补偿处理失败，后面进行重试 item:{},e:{}", JSON.toJSONString(item), e);
                        onlineInvoiceMessageMapper.updateOnlineInvoiceMessage(item.getId(), ErpConst.ZERO, System.currentTimeMillis());
                        return;
                    }
                    break;
                }
                default: {
                    logger.warn("消息类型错误本次跳过 item:{}", JSON.toJSONString(item));
                }
            }
        });

        return SUCCESS;
    }
}
