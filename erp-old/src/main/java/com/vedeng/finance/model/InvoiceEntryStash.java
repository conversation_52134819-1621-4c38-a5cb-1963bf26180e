package com.vedeng.finance.model;

/**
 * 发票录入暂存类
 * <AUTHOR>
 * @date created in 2020/5/18 15:07
 */
public class InvoiceEntryStash {

    private Integer invoiceEntryStashId;

    private Integer hxInvoiceId;

    private Integer hxInvoiceDetailId;

    /**
     * 采购订单商品id
     */
    private Integer buyorderGoodsId;

    /**
     * 录入数量
     */
    private Integer hasEntryCount;

    /**
     * 录入者
     */
    private String creator;

    private Long createTime;

    private String updater;

    private Long updateTime;

    private Integer isDelete;

    public Integer getInvoiceEntryStashId() {
        return invoiceEntryStashId;
    }

    public void setInvoiceEntryStashId(Integer invoiceEntryStashId) {
        this.invoiceEntryStashId = invoiceEntryStashId;
    }

    public Integer getHxInvoiceId() {
        return hxInvoiceId;
    }

    public void setHxInvoiceId(Integer hxInvoiceId) {
        this.hxInvoiceId = hxInvoiceId;
    }

    public Integer getHxInvoiceDetailId() {
        return hxInvoiceDetailId;
    }

    public void setHxInvoiceDetailId(Integer hxInvoiceDetailId) {
        this.hxInvoiceDetailId = hxInvoiceDetailId;
    }

    public Integer getBuyorderGoodsId() {
        return buyorderGoodsId;
    }

    public void setBuyorderGoodsId(Integer buyorderGoodsId) {
        this.buyorderGoodsId = buyorderGoodsId;
    }

    public Integer getHasEntryCount() {
        return hasEntryCount;
    }

    public void setHasEntryCount(Integer hasEntryCount) {
        this.hasEntryCount = hasEntryCount;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public String getUpdater() {
        return updater;
    }

    public void setUpdater(String updater) {
        this.updater = updater;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    @Override
    public String toString() {
        return "InvoiceEntryStash{" +
                "invoiceEntryStashId=" + invoiceEntryStashId +
                ", hxInvoiceId=" + hxInvoiceId +
                ", hxInvoiceDetailId=" + hxInvoiceDetailId +
                ", buyorderGoodsId=" + buyorderGoodsId +
                ", hasEntryCount=" + hasEntryCount +
                ", creator='" + creator + '\'' +
                ", createTime=" + createTime +
                ", updater='" + updater + '\'' +
                ", updateTime=" + updateTime +
                ", isDelete=" + isDelete +
                '}';
    }
}
