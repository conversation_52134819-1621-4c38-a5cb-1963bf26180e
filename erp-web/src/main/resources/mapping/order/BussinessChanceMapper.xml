<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.order.dao.BussinessChanceMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.order.model.BussinessChance" >
    <id column="BUSSINESS_CHANCE_ID" property="bussinessChanceId" jdbcType="INTEGER" />
    <result column="WEB_BUSSINESS_CHANCE_ID" property="webBussinessChanceId" jdbcType="INTEGER" />
    <result column="BUSSINESS_CHANCE_NO" property="bussinessChanceNo" jdbcType="VARCHAR" />
    <result column="WEB_ACCOUNT_ID" property="webAccountId" jdbcType="INTEGER" />
    <result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
    <result column="USER_ID" property="userId" jdbcType="INTEGER" />
    <result column="TRADER_ID" property="traderId" jdbcType="INTEGER" />
    <result column="CHECK_TRADER_NAME" property="checkTraderName" jdbcType="VARCHAR" />
    <result column="CHECK_TRADER_AREA" property="checkTraderArea" jdbcType="VARCHAR" />
  	<result column="CHECK_TRADER_CONTACT_NAME" property="checkTraderContactName" jdbcType="VARCHAR" />
  	<result column="CHECK_TRADER_CONTACT_MOBILE" property="checkMobile" jdbcType="VARCHAR" />
  	<result column="CHECK_TRADER_CONTACT_TELEPHONE" property="checkTraderContactTelephone" jdbcType="VARCHAR" />
    <result column="TYPE" property="type" jdbcType="INTEGER" />
    <result column="INQUIRY" property="inquiry" jdbcType="INTEGER" />
    <result column="RECEIVE_TIME" property="receiveTime" jdbcType="BIGINT" />
    <result column="SOURCE" property="source" jdbcType="INTEGER" />
    <result column="COMMUNICATION" property="communication" jdbcType="INTEGER" />
    <result column="CONTENT" property="content" jdbcType="VARCHAR" />
    <result column="GOODS_CATEGORY" property="goodsCategory" jdbcType="INTEGER" />
    <result column="GOODS_BRAND" property="goodsBrand" jdbcType="VARCHAR" />
    <result column="GOODS_NAME" property="goodsName" jdbcType="VARCHAR" />
    <result column="TRADER_NAME" property="traderName" jdbcType="VARCHAR" />
    <result column="AREA_ID" property="areaId" jdbcType="INTEGER" />
    <result column="AREA_IDS" property="areaIds" jdbcType="VARCHAR" />
    <result column="TRADER_CONTACT_ID" property="traderContactId" jdbcType="INTEGER" />
    <result column="TRADER_CONTACT_NAME" property="traderContactName" jdbcType="VARCHAR" />
    <result column="MOBILE" property="mobile" jdbcType="VARCHAR" />
    <result column="TELEPHONE" property="telephone" jdbcType="VARCHAR" />
    <result column="OTHER_CONTACT" property="otherContact" jdbcType="VARCHAR" />
    <result column="COMMENTS" property="comments" jdbcType="VARCHAR" />
    <result column="ASSIGN_TIME" property="assignTime" jdbcType="BIGINT" />
    <result column="FIRST_VIEW_TIME" property="firstViewTime" jdbcType="BIGINT" />
    <result column="STATUS" property="status" jdbcType="INTEGER" />
    <result column="STATUS_COMMENTS" property="statusComments" jdbcType="INTEGER" />
    <result column="CLOSED_COMMENTS" property="closedComments" jdbcType="VARCHAR" />
    <result column="WENXIN_OPEN_ID" property="wenxinOpenId" jdbcType="VARCHAR" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
    <result column="ORG_ID" property="orgId" jdbcType="INTEGER" />
    <result column="BUSSINESS_LEVEL" property="bussinessLevel" jdbcType="INTEGER" />
    <result column="BUSSINESS_STAGE" property="bussinessStage" jdbcType="INTEGER" />
    <result column="ENQUIRY_TYPE" property="enquiryType" jdbcType="INTEGER" />
    <result column="ORDER_RATE" property="orderRate" jdbcType="INTEGER" />
    <result column="BUSINESS_CHANCE_ACCURACY" property="businessChanceAccuracy" jdbcType="INTEGER" />
    <result column="AMOUNT" property="amount" jdbcType="DECIMAL"/>
    <result column="ORDER_TIME" property="orderTime" jdbcType="BIGINT" />
    <result column="PRODUCT_COMMENTS" property="productComments" jdbcType="VARCHAR" />
    <result column="PRODUCT_COMMENTS_SALE" property="productCommentsSale" jdbcType="VARCHAR" />
    <result column="COOKIE_ID" property="cookieId" jdbcType="VARCHAR"/>
    <result column="PAGE_FROM" property="pageFrom" jdbcType="VARCHAR"/>
  </resultMap>
  
  <resultMap type="com.vedeng.order.model.vo.BussinessChanceVo" id="VoBaseResultMap" extends="BaseResultMap">
  		<result column="SOURCE_NAME" property="sourceName" jdbcType="VARCHAR" />
  		<result column="COMMUNICATION_NAME" property="communicationName" jdbcType="VARCHAR" />
  		<result column="SALE_USER" property="saleUser" jdbcType="VARCHAR" />
  		<result column="QUOTEORDER_ID" property="quoteorderId" jdbcType="INTEGER" />
  		<result column="QUOTEORDER_NO" property="quoteorderNo" jdbcType="VARCHAR" />
  		<result column="QUOTEORDER_ADD_TIME" property="quoteorderAddTime" jdbcType="BIGINT" />
  		<result column="QUOTEORDER_TOTAL_AMOUNT" property="quoteorderTotalAmount" jdbcType="DECIMAL" />
  		<result column="QUOTEORDER_STATUS" property="quoteorderStatus" jdbcType="INTEGER" />
  		<result column="PAYMENT_TYPE" property="paymentType" jdbcType="INTEGER" />
  		<result column="SALEORDER_ID" property="saleorderId" jdbcType="INTEGER" />
  		<result column="SALEORDER_NO" property="saleorderNo" jdbcType="VARCHAR" />
  		<result column="SALEORDER_ADD_TIME" property="saleorderAddTime" jdbcType="BIGINT" />
  		<result column="SALEORDER_TOTAL_AMOUNT" property="saleorderTotalAmount" jdbcType="DECIMAL" />
	    <result column="SALEORDER_STATUS" property="saleorderstatus" jdbcType="INTEGER" />
	    <result column="TIME_TYPE" property="timeType" jdbcType="INTEGER" />
	    <result column="STARTTIME" property="starttime" jdbcType="VARCHAR" />
	    <result column="ENDTIME" property="endtime" jdbcType="VARCHAR" />
	    <result column="PROVINCE" property="province" jdbcType="INTEGER"/>
	    <result column="CITY" property="city" jdbcType="INTEGER"/>
	    <result column="ZONE" property="zone" jdbcType="INTEGER"/>
	    <result column="CLOSED_COMMENTS" property="closedComments" jdbcType="VARCHAR" />
	    <result column="TYPENAME" property="typeName" jdbcType="VARCHAR" />
	    <result column="SOURCENAME" property="sourceName" jdbcType="VARCHAR" />
	    <result column="COMMUNICATIONNAME" property="communicationName" jdbcType="VARCHAR" />
	    <result column="GOODSTYPENAME" property="goodsCategoryName" jdbcType="VARCHAR" />
	    <result column="ATTACHMENT_NAME" property="attachmentName" jdbcType="VARCHAR" />
	    <result column="ATTACHMENT_URI" property="attachmentUri" jdbcType="VARCHAR" />
        <result column="BUSINESS_CHANCE_ACCURACY" property="businessChanceAccuracy" jdbcType="INTEGER" />
	    <result column="ATTACHMENT_DOMAIN" property="attachmentDomain" jdbcType="VARCHAR" />
	    <result column="CLOSEREASON" property="closeReason" jdbcType="VARCHAR" />
	    <result column="QUOTE_VALID_STATUS" property="quoteValidStatus" jdbcType="INTEGER"/>
	    <result column="LAST_VERIFY_USERNAME" property="lastVerifyUsermae" jdbcType="VARCHAR" />
		<result column="VERIFY_USERNAME" property="verifyUsername" jdbcType="VARCHAR" />
		<result column="VERIFY_STATUS" property="verifyStatus" jdbcType="INTEGER" />	
  </resultMap>
  <sql id="Base_Column_List" >
    BUSSINESS_CHANCE_ID, WEB_BUSSINESS_CHANCE_ID, BUSSINESS_CHANCE_NO, WEB_ACCOUNT_ID, 
    COMPANY_ID, USER_ID, TRADER_ID, TYPE, RECEIVE_TIME, SOURCE, COMMUNICATION, CONTENT, GOODS_CATEGORY, 
    GOODS_BRAND, GOODS_NAME, TRADER_NAME, AREA_ID, AREA_IDS, TRADER_CONTACT_ID, TRADER_CONTACT_NAME, 
    MOBILE, TELEPHONE, OTHER_CONTACT, COMMENTS, ASSIGN_TIME, FIRST_VIEW_TIME, STATUS, 
    BUSSINESS_LEVEL,BUSSINESS_STAGE,ENQUIRY_TYPE,ORDER_RATE,AMOUNT,ORDER_TIME,
    STATUS_COMMENTS, WENXIN_OPEN_ID, ADD_TIME, CREATOR, MOD_TIME, UPDATER,CLOSED_COMMENTS,ORG_ID,
    CHECK_TRADER_NAME, CHECK_TRADER_AREA, CHECK_TRADER_CONTACT_NAME, CHECK_TRADER_CONTACT_MOBILE, CHECK_TRADER_CONTACT_TELEPHONE,
    PRODUCT_COMMENTS,PRODUCT_COMMENTS_SALE,INQUIRY,COOKIE_ID,PAGE_FROM,BUSINESS_CHANCE_ACCURACY
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from T_BUSSINESS_CHANCE
    where BUSSINESS_CHANCE_ID = #{bussinessChanceId,jdbcType=INTEGER}
  </select>
    <select id="getBusinessChanceLimit" resultType="com.vedeng.order.model.BussinessChance">
      SELECT * FROM T_BUSSINESS_CHANCE LIMIT #{start},#{limit}
    </select>
  <select id="getBusinessChanceCountByMobileInNinetyDays" resultType="java.lang.Integer">
    SELECT COUNT(*)
    FROM T_BUSSINESS_CHANCE
    WHERE (
        MOBILE like CONCAT('%', #{mobile}, '%')
        or TELEPHONE like CONCAT('%', #{mobile}, '%')
        or OTHER_CONTACT like CONCAT('%', #{mobile}, '%')
        or TRADER_CONTACT_NAME like CONCAT('%', #{mobile}, '%')
        or CHECK_TRADER_CONTACT_MOBILE like CONCAT('%', #{mobile}, '%')
        or CHECK_TRADER_CONTACT_NAME like CONCAT('%', #{mobile}, '%')
        or CHECK_TRADER_CONTACT_TELEPHONE like CONCAT('%', #{mobile}, '%')
      )
      AND ADD_TIME &gt; (#{now} - 90 * 60 * 60 * 24 * 1000)
      AND ADD_TIME &lt; #{now}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from T_BUSSINESS_CHANCE
    where BUSSINESS_CHANCE_ID = #{bussinessChanceId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.order.model.BussinessChance" >
    insert into T_BUSSINESS_CHANCE (BUSSINESS_CHANCE_ID, WEB_BUSSINESS_CHANCE_ID, 
      BUSSINESS_CHANCE_NO, WEB_ACCOUNT_ID, USER_ID, COMPANY_ID, 
      TRADER_ID, TYPE, RECEIVE_TIME, 
      SOURCE, COMMUNICATION, CONTENT, 
      GOODS_CATEGORY, GOODS_BRAND, GOODS_NAME, 
      TRADER_NAME, AREA_ID, AREA_IDS, 
      TRADER_CONTACT_ID, TRADER_CONTACT_NAME, MOBILE, 
      TELEPHONE, OTHER_CONTACT, COMMENTS, 
      ASSIGN_TIME, FIRST_VIEW_TIME, STATUS, 
      STATUS_COMMENTS, WENXIN_OPEN_ID, ADD_TIME, 
      CREATOR, MOD_TIME, UPDATER,CLOSED_COMMENTS,ORG_ID,
      CHECK_TRADER_NAME, CHECK_TRADER_AREA, CHECK_TRADER_CONTACT_NAME,
      CHECK_TRADER_CONTACT_MOBILE, CHECK_TRADER_CONTACT_TELEPHONE,INQUIRY,COOKIE_ID,PAGE_FROM
      )
    values (#{bussinessChanceId,jdbcType=INTEGER}, #{webBussinessChanceId,jdbcType=INTEGER}, 
      #{bussinessChanceNo,jdbcType=VARCHAR}, #{webAccountId,jdbcType=INTEGER}, #{userId,jdbcType=INTEGER}, #{companyId,jdbcType=INTEGER}, 
      #{traderId,jdbcType=INTEGER}, #{type,jdbcType=INTEGER}, #{receiveTime,jdbcType=BIGINT}, 
      #{source,jdbcType=INTEGER}, #{communication,jdbcType=INTEGER}, #{content,jdbcType=VARCHAR}, 
      #{goodsCategory,jdbcType=INTEGER}, #{goodsBrand,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR}, 
      #{traderName,jdbcType=VARCHAR}, #{areaId,jdbcType=INTEGER}, #{areaIds,jdbcType=VARCHAR}, 
      #{traderContactId,jdbcType=INTEGER}, #{traderContactName,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, 
      #{telephone,jdbcType=VARCHAR}, #{otherContact,jdbcType=VARCHAR}, #{comments,jdbcType=VARCHAR}, 
      #{assignTime,jdbcType=BIGINT}, #{firstViewTime,jdbcType=BIGINT}, #{status,jdbcType=INTEGER}, 
      #{statusComments,jdbcType=INTEGER}, #{wenxinOpenId,jdbcType=VARCHAR}, #{addTime,jdbcType=BIGINT}, 
      #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER},#{closedComments,jdbcType=VARCHAR}, #{orgId,jdbcType=INTEGER},
      #{checkTraderName,jdbcType=VARCHAR},#{checkTraderArea,jdbcType=VARCHAR},#{checkTraderContactName,jdbcType=VARCHAR},
      #{checkMobile,jdbcType=VARCHAR},#{checkTraderContactTelephone,jdbcType=VARCHAR},#{inquiry},#{cookieId,jdbcType=VARCHAR},#{pageFrom,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.order.model.BussinessChance" >
    insert into T_BUSSINESS_CHANCE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="bussinessChanceId != null" >
        BUSSINESS_CHANCE_ID,
      </if>
      <if test="webBussinessChanceId != null" >
        WEB_BUSSINESS_CHANCE_ID,
      </if>
      <if test="bussinessChanceNo != null" >
        BUSSINESS_CHANCE_NO,
      </if>
      <if test="webAccountId != null" >
        WEB_ACCOUNT_ID,
      </if>
      <if test="companyId != null" >
        COMPANY_ID,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="traderId != null" >
        TRADER_ID,
      </if>
      <if test="type != null" >
        TYPE,
      </if>
      <if test="receiveTime != null" >
        RECEIVE_TIME,
      </if>
      <if test="source != null" >
        SOURCE,
      </if>
      <if test="communication != null" >
        COMMUNICATION,
      </if>
      <if test="content != null" >
        CONTENT,
      </if>
      <if test="goodsCategory != null" >
        GOODS_CATEGORY,
      </if>
      <if test="goodsBrand != null" >
        GOODS_BRAND,
      </if>
      <if test="goodsName != null" >
        GOODS_NAME,
      </if>
      <if test="traderName != null" >
        TRADER_NAME,
      </if>
      <if test="areaId != null" >
        AREA_ID,
      </if>
      <if test="areaIds != null" >
        AREA_IDS,
      </if>
      <if test="traderContactId != null" >
        TRADER_CONTACT_ID,
      </if>
      <if test="traderContactName != null" >
        TRADER_CONTACT_NAME,
      </if>
      <if test="mobile != null" >
        MOBILE,
      </if>
      <if test="telephone != null" >
        TELEPHONE,
      </if>
      <if test="otherContact != null" >
        OTHER_CONTACT,
      </if>
      <if test="comments != null" >
        COMMENTS,
      </if>
      <if test="assignTime != null" >
        ASSIGN_TIME,
      </if>
      <if test="firstViewTime != null" >
        FIRST_VIEW_TIME,
      </if>
      <if test="status != null" >
        STATUS,
      </if>
      <if test="statusComments != null" >
        STATUS_COMMENTS,
      </if>
      <if test="wenxinOpenId != null" >
        WENXIN_OPEN_ID,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
      <if test="closedComments != null" >
        CLOSED_COMMENTS,
      </if>
      <if test="orgId != null" >
        ORG_ID,
      </if>
      <if test="checkTraderName != null" >
        CHECK_TRADER_NAME,
      </if>
      <if test="checkTraderArea != null" >
        CHECK_TRADER_AREA,
      </if>
      <if test="checkTraderContactName != null" >
        CHECK_TRADER_CONTACT_NAME,
      </if>
      <if test="checkMobile != null" >
        CHECK_TRADER_CONTACT_MOBILE,
      </if>
      <if test="checkTraderContactTelephone != null" >
        CHECK_TRADER_CONTACT_TELEPHONE,
      </if>
      <if test="productComments != null">
        PRODUCT_COMMENTS,
      </if>
      <if test="productCommentsSale != null">
        PRODUCT_COMMENTS_SALE,
      </if>
      <if test="inquiry != null">
        INQUIRY,
      </if>
      <if test="cookieId != null">
        COOKIE_ID,
      </if>
      <if test="pageFrom != null">
        PAGE_FROM,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="bussinessChanceId != null" >
        #{bussinessChanceId,jdbcType=INTEGER},
      </if>
      <if test="webBussinessChanceId != null" >
        #{webBussinessChanceId,jdbcType=INTEGER},
      </if>
      <if test="bussinessChanceNo != null" >
        #{bussinessChanceNo,jdbcType=VARCHAR},
      </if>
      <if test="webAccountId != null" >
        #{webAccountId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null" >
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="traderId != null" >
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="type != null" >
        #{type,jdbcType=INTEGER},
      </if>
      <if test="receiveTime != null" >
        #{receiveTime,jdbcType=BIGINT},
      </if>
      <if test="source != null" >
        #{source,jdbcType=INTEGER},
      </if>
      <if test="communication != null" >
        #{communication,jdbcType=INTEGER},
      </if>
      <if test="content != null" >
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="goodsCategory != null" >
        #{goodsCategory,jdbcType=INTEGER},
      </if>
      <if test="goodsBrand != null" >
        #{goodsBrand,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null" >
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="traderName != null" >
        #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="areaId != null" >
        #{areaId,jdbcType=INTEGER},
      </if>
      <if test="areaIds != null" >
        #{areaIds,jdbcType=VARCHAR},
      </if>
      <if test="traderContactId != null" >
        #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null" >
        #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null" >
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null" >
        #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="otherContact != null" >
        #{otherContact,jdbcType=VARCHAR},
      </if>
      <if test="comments != null" >
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="assignTime != null" >
        #{assignTime,jdbcType=BIGINT},
      </if>
      <if test="firstViewTime != null" >
        #{firstViewTime,jdbcType=BIGINT},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="statusComments != null" >
        #{statusComments,jdbcType=INTEGER},
      </if>
      <if test="wenxinOpenId != null" >
        #{wenxinOpenId,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="closedComments != null" >
        #{closedComments,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null" >
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="checkTraderName != null" >
        #{checkTraderName,jdbcType=VARCHAR},
      </if>
      <if test="checkTraderArea != null" >
        #{checkTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="checkTraderContactName != null" >
       #{checkTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="checkMobile != null" >
        #{checkMobile,jdbcType=VARCHAR},
      </if>
      <if test="checkTraderContactTelephone != null" >
        #{checkTraderContactTelephone,jdbcType=VARCHAR},
      </if>
        <if test="productComments != null">
          #{productComments,jdbcType=VARCHAR},
        </if>
      <if test="productCommentsSale != null">
        #{productCommentsSale,jdbcType=VARCHAR},
      </if>
      <if test="inquiry != null">
        #{inquiry},
      </if>
      <if test="cookieId != null">
        #{cookieId,jdbcType=VARCHAR},
      </if>
      <if test="pageFrom != null">
        #{pageFrom,jdbcType=VARCHAR},
      </if>
    </trim>
    <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="bussinessChanceId">
		SELECT LAST_INSERT_ID() AS bussinessChanceId
    </selectKey>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.order.model.BussinessChance" >
    update T_BUSSINESS_CHANCE
    <set >
      <if test="webBussinessChanceId != null" >
        WEB_BUSSINESS_CHANCE_ID = #{webBussinessChanceId,jdbcType=INTEGER},
      </if>
      <if test="bussinessChanceNo != null" >
        BUSSINESS_CHANCE_NO = #{bussinessChanceNo,jdbcType=VARCHAR},
      </if>
      <if test="webAccountId != null" >
        WEB_ACCOUNT_ID = #{webAccountId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null" >
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="userId != null" >
        USER_ID = #{userId,jdbcType=INTEGER},
      </if>
      <if test="traderId != null" >
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="type != null and type != 0"  >
        TYPE = #{type,jdbcType=INTEGER},
      </if>
      <if test="receiveTime != null" >
        RECEIVE_TIME = #{receiveTime,jdbcType=BIGINT},
      </if>
      <if test="source != null" >
        SOURCE = #{source,jdbcType=INTEGER},
      </if>
      <if test="communication != null" >
        COMMUNICATION = #{communication,jdbcType=INTEGER},
      </if>
      <if test="content != null" >
        CONTENT = #{content,jdbcType=VARCHAR},
      </if>
      <if test="goodsCategory != null" >
        GOODS_CATEGORY = #{goodsCategory,jdbcType=INTEGER},
      </if>
      <if test="goodsBrand != null" >
        GOODS_BRAND = #{goodsBrand,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null" >
        GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="traderName != null" >
        TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="areaId != null" >
        AREA_ID = #{areaId,jdbcType=INTEGER},
      </if>
      <if test="areaIds != null" >
        AREA_IDS = #{areaIds,jdbcType=VARCHAR},
      </if>
      <if test="traderContactId != null" >
        TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null" >
        TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null" >
        MOBILE = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null" >
        TELEPHONE = #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="otherContact != null" >
        OTHER_CONTACT = #{otherContact,jdbcType=VARCHAR},
      </if>
      <if test="comments != null" >
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="assignTime != null" >
        ASSIGN_TIME = #{assignTime,jdbcType=BIGINT},
      </if>
      <if test="firstViewTime != null" >
        FIRST_VIEW_TIME = #{firstViewTime,jdbcType=BIGINT},
      </if>
      <if test="status != null" >
        STATUS = #{status,jdbcType=INTEGER},
      </if>
      <if test="statusComments != null" >
        STATUS_COMMENTS = #{statusComments,jdbcType=INTEGER},
      </if>
      <if test="wenxinOpenId != null" >
        WENXIN_OPEN_ID = #{wenxinOpenId,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="closedComments != null" >
        CLOSED_COMMENTS = #{closedComments,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null" >
        ORG_ID = #{orgId,jdbcType=INTEGER},
      </if>
      <if test="checkTraderName != null" >
       CHECK_TRADER_NAME = #{checkTraderName,jdbcType=VARCHAR},
      </if>
      <if test="checkTraderArea != null" >
        CHECK_TRADER_AREA = #{checkTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="checkTraderContactName != null" >
       CHECK_TRADER_CONTACT_NAME = #{checkTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="checkMobile != null" >
        CHECK_TRADER_CONTACT_MOBILE = #{checkMobile,jdbcType=VARCHAR},
      </if>
      <if test="checkTraderContactTelephone != null" >
        CHECK_TRADER_CONTACT_TELEPHONE = #{checkTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="bussinessLevel != null" >
        BUSSINESS_LEVEL = #{bussinessLevel,jdbcType=INTEGER},
      </if>
      <if test="bussinessStage != null" >
        BUSSINESS_STAGE = #{bussinessStage,jdbcType=INTEGER},
      </if>
      <if test="stage != null" >
        STAGE = #{stage,jdbcType=INTEGER},
      </if>
      <if test="winningOrderTime != null">
        WINNING_ORDER_TIME = #{winningOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enquiryType != null" >
        ENQUIRY_TYPE = #{enquiryType,jdbcType=INTEGER},
      </if>
      <if test="orderRate != null" >
        ORDER_RATE = #{orderRate,jdbcType=INTEGER},
      </if>
      <if test="amount != null" >
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="orderTime != null" >
        ORDER_TIME = #{orderTime,jdbcType=BIGINT},
      </if>
      <if test="bussinessParentId != null" >
        BUSSINESS_PARENT_ID = #{bussinessParentId,jdbcType=INTEGER},
      </if>
      <if test="isNew != null">
        IS_NEW = #{isNew, jdbcType=INTEGER}
      </if>
      <if test="businessChanceAccuracy != null">
        BUSINESS_CHANCE_ACCURACY = #{businessChanceAccuracy, jdbcType=INTEGER}
      </if>
      <if test="productComments != null">
        PRODUCT_COMMENTS = #{productComments, jdbcType=VARCHAR}
      </if>
      <if test="productCommentsSale != null">
        PRODUCT_COMMENTS_SALE = #{productCommentsSale, jdbcType=VARCHAR},
      </if>
      <if test="bncLink !=null">
        BNC_LINK =#{bncLink},
      </if>
      <if test="isLinkBd != null">
        IS_LINK_BD = #{isLinkBd, jdbcType=INTEGER},
      </if>
      <if test="inquiry !=null">
        INQUIRY = #{inquiry},
      </if>
      <if test="cookieId != null">
        COOKIE_ID = #{cookieId,jdbcType=VARCHAR},
      </if>
      <if test="pageFrom != null">
        PAGE_FROM = #{pageFrom,jdbcType=VARCHAR},
      </if>
    </set>
    where BUSSINESS_CHANCE_ID = #{bussinessChanceId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.order.model.BussinessChance" >
    update T_BUSSINESS_CHANCE
    set WEB_BUSSINESS_CHANCE_ID = #{webBussinessChanceId,jdbcType=INTEGER},
      BUSSINESS_CHANCE_NO = #{bussinessChanceNo,jdbcType=VARCHAR},
      WEB_ACCOUNT_ID = #{webAccountId,jdbcType=INTEGER},
      COMPANY_ID = #{companyId,jdbcType=INTEGER},
      USER_ID = #{userId,jdbcType=INTEGER},
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      TYPE = #{type,jdbcType=INTEGER},
      RECEIVE_TIME = #{receiveTime,jdbcType=BIGINT},
      SOURCE = #{source,jdbcType=INTEGER},
      COMMUNICATION = #{communication,jdbcType=INTEGER},
      CONTENT = #{content,jdbcType=VARCHAR},
      GOODS_CATEGORY = #{goodsCategory,jdbcType=INTEGER},
      GOODS_BRAND = #{goodsBrand,jdbcType=VARCHAR},
      GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      AREA_ID = #{areaId,jdbcType=INTEGER},
      AREA_IDS = #{areaIds,jdbcType=VARCHAR},
      TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      MOBILE = #{mobile,jdbcType=VARCHAR},
      TELEPHONE = #{telephone,jdbcType=VARCHAR},
      OTHER_CONTACT = #{otherContact,jdbcType=VARCHAR},
      COMMENTS = #{comments,jdbcType=VARCHAR},
      ASSIGN_TIME = #{assignTime,jdbcType=BIGINT},
      FIRST_VIEW_TIME = #{firstViewTime,jdbcType=BIGINT},
      STATUS = #{status,jdbcType=INTEGER},
      STATUS_COMMENTS = #{statusComments,jdbcType=INTEGER},
      WENXIN_OPEN_ID = #{wenxinOpenId,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      CLOSED_COMMENTS = #{closedComments,jdbcType=VARCHAR},
      ORG_ID = #{orgId,jdbcType=INTEGER},
      CHECK_TRADER_NAME = #{checkTraderName,jdbcType=VARCHAR},
      CHECK_TRADER_AREA = #{checkTraderArea,jdbcType=VARCHAR},
      CHECK_TRADER_CONTACT_NAME = #{checkTraderContactName,jdbcType=VARCHAR},
      CHECK_TRADER_CONTACT_MOBILE = #{checkMobile,jdbcType=VARCHAR},
      CHECK_TRADER_CONTACT_TELEPHONE = #{checkTraderContactTelephone,jdbcType=VARCHAR},
      INQUIRY = #{inquiry},
      COOKIE_ID = #{cookieId,jdbcType=VARCHAR},
      PAGE_FROM = #{pageFrom,jdbcType=VARCHAR}
    where BUSSINESS_CHANCE_ID = #{bussinessChanceId,jdbcType=INTEGER}
  </update>
    <update id="updateSaleUserOfBussinessChanceByTraderList">
      UPDATE T_BUSSINESS_CHANCE SET USER_ID = #{targetUserId} WHERE TRADER_ID IN
        <foreach collection="traderIdList" index="index" item="item" open="(" close=")" separator=",">
          #{item,jdbcType=INTEGER}
        </foreach>
    </update>

  <select id="getBussCommList" resultType="com.newtask.model.BqoSysAutoCloseDto">
	SELECT
			b.BUSSINESS_CHANCE_ID bussinessChanceId ,
			b.ADD_TIME orderCreateTime,
			C2.ADD_TIME comRecordCreateTime,
			UNIX_TIMESTAMP(C2.NEXT_CONTACT_DATE)*1000 comRecordNextConTime,
			b.USER_ID
    FROM
        T_BUSSINESS_CHANCE b
        LEFT JOIN T_QUOTEORDER q ON b.BUSSINESS_CHANCE_ID = q.BUSSINESS_CHANCE_ID
	    LEFT JOIN (
                    SELECT
                        C1.RELATED_ID AS K,
                        MAX( C1.COMMUNICATE_RECORD_ID ) AS MAXID
                    FROM
                        T_COMMUNICATE_RECORD C1
                    WHERE
                        C1.COMMUNICATE_TYPE = 244 AND COMPANY_ID = 1
                    GROUP BY
                        C1.RELATED_ID
                  ) M2 ON b.BUSSINESS_CHANCE_ID = M2.K
		LEFT JOIN T_COMMUNICATE_RECORD C2 ON C2.COMMUNICATE_RECORD_ID = M2.MAXID
    WHERE
        b.USER_ID != 0 and
        (
        (b.STATUS !=4 and q.FOLLOW_ORDER_STATUS =2  ) or (b.STATUS !=4 AND q.FOLLOW_ORDER_STATUS is null)
        )
        LIMIT #{start},#{limit}
	</select>


  <update id="batchCloseBussChance" parameterType="java.util.List">
    UPDATE T_BUSSINESS_CHANCE
    SET
    STATUS = 4,
    STATUS_COMMENTS = #{closeReason,jdbcType=INTEGER},
    CLOSED_COMMENTS = #{closeComment,jdbcType=VARCHAR},
    MOD_TIME = REPLACE(unix_timestamp(current_timestamp(3)),'.','')
    WHERE BUSSINESS_CHANCE_ID IN
    <foreach collection="BqoList" item="batchCloseQuoteOrder" index="index" separator="," open="(" close=")">
      #{batchCloseQuoteOrder.bussinessChanceId}
    </foreach>
  </update>

    <select id="getTraderHistoryListPage" resultType="com.vedeng.order.model.vo.BussinessChanceVo" parameterType="Map">
        SELECT
    A.*,
	D.TITLE AS typeName,
	E.TITLE AS sourceName ,
	F.TITLE AS entranceName,
	G.TITLE AS functionName,
	H.TITLE AS bussinessLevelStr,
	I.TITLE AS orderRateStr,
	QU.QUOTEORDER_NO AS quoteorderNo,
	QU.QUOTEORDER_ID AS quoteorderId
    FROM
	T_BUSSINESS_CHANCE A
	LEFT JOIN T_R_USER_POSIT B ON A.USER_ID=B.USER_ID
	LEFT JOIN T_POSITION BB ON BB.POSITION_ID=B.POSITION_ID
	LEFT JOIN T_ORGANIZATION C ON C.ORG_ID=BB.ORG_ID
	LEFT JOIN T_QUOTEORDER QU ON QU.BUSSINESS_CHANCE_ID=A.BUSSINESS_CHANCE_ID
	LEFT JOIN T_SYS_OPTION_DEFINITION D ON A.TYPE = D.SYS_OPTION_DEFINITION_ID
	LEFT JOIN T_SYS_OPTION_DEFINITION E ON A.SOURCE = E.SYS_OPTION_DEFINITION_ID
	LEFT JOIN T_SYS_OPTION_DEFINITION F ON A.ENTRANCES=F.SYS_OPTION_DEFINITION_ID
	LEFT JOIN T_SYS_OPTION_DEFINITION G ON A.FUNCTIONS=G.SYS_OPTION_DEFINITION_ID
	LEFT JOIN T_SYS_OPTION_DEFINITION H ON A.BUSSINESS_LEVEL=H.SYS_OPTION_DEFINITION_ID
	LEFT JOIN T_SYS_OPTION_DEFINITION I ON A.ORDER_RATE=I.SYS_OPTION_DEFINITION_ID
    WHERE
	A.`STATUS` IN ( 0, 1 )
	AND A.TRADER_ID = #{bussinessChanceVo.traderId}
    <if test="bussinessChanceVo.bussinessChanceNo != null and bussinessChanceVo.bussinessChanceNo != '' " >
      and A.BUSSINESS_CHANCE_NO like CONCAT('%',#{bussinessChanceVo.bussinessChanceNo},'%' )
    </if>
    <if test="bussinessChanceVo.type != null and bussinessChanceVo.type != 0 " >
      and A.TYPE = #{bussinessChanceVo.type,jdbcType=INTEGER}
    </if>
    <if test="bussinessChanceVo.source != null and bussinessChanceVo.source != 0 " >
      and A.SOURCE = #{bussinessChanceVo.source,jdbcType=INTEGER}
    </if>
    <if test="bussinessChanceVo.traderContactName != null and bussinessChanceVo.traderContactName != ''">
      and (
      A.MOBILE like CONCAT('%',#{bussinessChanceVo.traderContactName},'%' )
      or A.TELEPHONE like CONCAT('%',#{bussinessChanceVo.traderContactName},'%' )
      or A.OTHER_CONTACT like CONCAT('%',#{bussinessChanceVo.traderContactName},'%' )
      or A.TRADER_CONTACT_NAME like CONCAT('%',#{bussinessChanceVo.traderContactName},'%' )
      or A.CHECK_TRADER_CONTACT_MOBILE like CONCAT('%',#{bussinessChanceVo.traderContactName},'%' )
      or A.CHECK_TRADER_CONTACT_NAME like CONCAT('%',#{bussinessChanceVo.traderContactName},'%' )
      or A.CHECK_TRADER_CONTACT_TELEPHONE like CONCAT('%',#{bussinessChanceVo.traderContactName},'%' )
      )
    </if>
      <if test="bussinessChanceVo.starttimeLong != null">
        and A.ADD_TIME <![CDATA[>=]]> #{bussinessChanceVo.starttimeLong}
      </if>
      <if test="bussinessChanceVo.endtimeLong != null">
        and A.ADD_TIME <![CDATA[<=]]> #{bussinessChanceVo.endtimeLong}
      </if>
    <if test="bussinessChanceVo.cdstarttimeLong != null">
      and A.ORDER_TIME <![CDATA[>=]]> #{bussinessChanceVo.cdstarttimeLong}
    </if>
    <if test="bussinessChanceVo.cdendtimeLong != null">
      and A.ORDER_TIME <![CDATA[<=]]> #{bussinessChanceVo.cdendtimeLong}
    </if>
    <if test="bussinessChanceVo.content != null and bussinessChanceVo.content != ''" >
      and A.CONTENT like CONCAT('%',#{bussinessChanceVo.content},'%' )
    </if>
  </select>
  <select id="getTraderHasHistoryBussiness" resultType="com.vedeng.order.model.vo.BussinessChanceVo">
     SELECT
    A.BUSSINESS_CHANCE_NO,
	D.TITLE AS typeName,
	E.TITLE AS sourceName ,
	F.TITLE AS entranceName,
	G.TITLE AS functionName,
	H.TITLE AS bussinessLevelStr,
	A.AMOUNT,
	I.TITLE AS orderRateStr,
	C.ORG_NAME,
	A.CONTENT,
	A.ASSIGN_TIME,
	A.FIRST_VIEW_TIME,
	A.`STATUS`,
	QU.QUOTEORDER_NO,
	QU.QUOTEORDER_ID,
    J.TITLE AS  inquiryName
    FROM
	T_BUSSINESS_CHANCE A
	LEFT JOIN T_R_USER_POSIT B ON A.USER_ID=B.USER_ID
	LEFT JOIN T_POSITION BB ON BB.POSITION_ID=B.POSITION_ID
	LEFT JOIN T_ORGANIZATION C ON C.ORG_ID=BB.ORG_ID
	LEFT JOIN T_QUOTEORDER QU ON QU.BUSSINESS_CHANCE_ID=A.BUSSINESS_CHANCE_ID
	LEFT JOIN T_SYS_OPTION_DEFINITION D ON A.TYPE = D.SYS_OPTION_DEFINITION_ID
	LEFT JOIN T_SYS_OPTION_DEFINITION E ON A.SOURCE = E.SYS_OPTION_DEFINITION_ID
	LEFT JOIN T_SYS_OPTION_DEFINITION F ON A.ENTRANCES=F.SYS_OPTION_DEFINITION_ID
	LEFT JOIN T_SYS_OPTION_DEFINITION G ON A.FUNCTIONS=G.SYS_OPTION_DEFINITION_ID
	LEFT JOIN T_SYS_OPTION_DEFINITION H ON A.BUSSINESS_LEVEL=H.SYS_OPTION_DEFINITION_ID
	LEFT JOIN T_SYS_OPTION_DEFINITION I ON A.ORDER_RATE=I.SYS_OPTION_DEFINITION_ID
	LEFT JOIN T_SYS_OPTION_DEFINITION J ON A.INQUIRY=I.SYS_OPTION_DEFINITION_ID
    WHERE
	A.`STATUS` IN ( 0, 1 )
	AND A.TRADER_ID = #{traderId}
  </select>


  <select id="getChanceCanMerge" parameterType="com.vedeng.order.model.MergeBussinessChanceQuery" resultMap="BaseResultMap">
     SELECT A.BUSSINESS_CHANCE_NO,A.`STATUS`,A.MOBILE,A.ADD_TIME,A.USER_ID FROM(
     SELECT BUSSINESS_CHANCE_NO,`STATUS`,MOBILE,ADD_TIME,USER_ID FROM T_BUSSINESS_CHANCE WHERE 1=1
     AND `TYPE` IN (391,394) AND `STATUS` IN (0,1,2)
     AND MOBILE=#{mobile} AND ADD_TIME <![CDATA[>=]]> #{beginTime} AND ADD_TIME <![CDATA[<=]]> #{endTime}
     order by ADD_TIME DESC ) A
     GROUP BY A.MOBILE
  </select>

  <select id="getMergeStatus" resultMap="BaseResultMap">
    SELECT BUSSINESS_CHANCE_ID,BUSSINESS_CHANCE_NO,MERGE_STATUS FROM T_BUSSINESS_CHANCE
    WHERE BUSSINESS_CHANCE_ID=#{chanceId}
  </select>

  <select id="getNewMergeChanceId" resultType="java.lang.Integer">
    SELECT BUSSINESS_CHANCE_ID FROM T_BUSSINESS_CHANCE
    WHERE BUSSINESS_CHANCE_NO=#{chanceNo} AND MERGE_STATUS=2
  </select>
  <select id="getBusinessChanceInfo" resultMap="VoBaseResultMap">
     SELECT
    A.*,
	QU.QUOTEORDER_NO AS quoteorderNo,
	QU.QUOTEORDER_ID AS quoteorderId
    FROM
	T_BUSSINESS_CHANCE A
	LEFT JOIN T_QUOTEORDER QU ON QU.BUSSINESS_CHANCE_ID=A.BUSSINESS_CHANCE_ID
    WHERE
	A.BUSSINESS_CHANCE_ID=#{bussinessChanceId}
  </select>
  <select id="getTraderIdsByPeriod" parameterType="com.vedeng.trader.model.Period" resultType="java.lang.Integer">
      select TRADER_ID FROM T_BUSSINESS_CHANCE
      WHERE ADD_TIME <![CDATA[>=]]> #{beginTime}
      and ADD_TIME <![CDATA[<=]]> #{endTime}
      AND TRADER_ID IS NOT NULL
  </select>

  <update id="updateBCStatus" parameterType="com.vedeng.order.model.BussinessChance">
    UPDATE T_BUSSINESS_CHANCE SET `STATUS`=#{status} WHERE BUSSINESS_CHANCE_ID =#{bussinessChanceId}
  </update>

    <update id="relateCloseBussChanceById">
      UPDATE
      T_BUSSINESS_CHANCE
      SET
      STATUS = 4,
      STAGE = 6,
      LOSE_ORDER_TIME = now(),
      STATUS_COMMENTS = #{closeReason,jdbcType=INTEGER},
      CLOSED_COMMENTS = #{closeComment,jdbcType=VARCHAR},
      MOD_TIME	= REPLACE(unix_timestamp(current_timestamp(3)),'.','')
      WHERE BUSSINESS_CHANCE_ID = #{bussinessChanceId}
    </update>

    <select id="getSimpleBcListPage" resultMap="BaseResultMap" parameterType="java.util.Map">
     SELECT BUSSINESS_CHANCE_ID,USER_ID,STATUS FROM T_BUSSINESS_CHANCE WHERE STATUS IN (0,3) AND MERGE_STATUS IN (0,2)
  </select>

  <select id="getSimpleBcByNo" resultMap="BaseResultMap">
     SELECT BUSSINESS_CHANCE_ID,USER_ID,STATUS FROM T_BUSSINESS_CHANCE WHERE MERGE_STATUS IN (0,2) AND BUSSINESS_CHANCE_NO=#{chanceNo}
  </select>
  <select id="getLastMonthBussinessChance" resultMap="VoBaseResultMap" parameterType="com.vedeng.order.model.vo.BussinessChanceVo">
    select
    BUSSINESS_CHANCE_ID,BUSSINESS_CHANCE_NO,GOODS_NAME,USER_ID,TRADER_ID,ADD_TIME
    from
    T_BUSSINESS_CHANCE
    where 1=1
    <if test="traderId !=null and traderId !=0 ">
      and TRADER_ID = #{traderId,jdbcType=INTEGER}
    </if>
    <if test="addTime !=null and addTime > 0 ">
      and ADD_TIME <![CDATA[>=]]> #{addTime}
    </if>
    <if test="content !=null and content !='' ">
      and CONTENT like CONCAT('%',#{content},'%' )
    </if>
    order by
    ADD_TIME desc
  </select>
    <select id="getBCBySaleorderId" resultType="com.vedeng.order.model.BussinessChance">
      SELECT B.BUSSINESS_CHANCE_ID,B.`STATUS` FROM T_BUSSINESS_CHANCE B
     LEFT JOIN T_QUOTEORDER Q ON B.BUSSINESS_CHANCE_ID=Q.BUSSINESS_CHANCE_ID
     LEFT JOIN T_SALEORDER S ON Q.QUOTEORDER_ID=S.QUOTEORDER_ID
     WHERE S.SALEORDER_ID =#{orderId}
    </select>

    <update id="updateEditComments" parameterType="com.vedeng.order.model.BussinessChance">
    update T_BUSSINESS_CHANCE
    <set >
      PRODUCT_COMMENTS =#{productComments,jdbcType=VARCHAR}
    </set>
    where BUSSINESS_CHANCE_ID = #{bussinessChanceId,jdbcType=INTEGER}


  </update>

  <select id="getCreateBusinessChanceRecentlyCustomerIds" resultType="java.lang.Integer">
    SELECT DISTINCT
      T2.TRADER_CUSTOMER_ID
    FROM
      T_BUSSINESS_CHANCE T1
      LEFT JOIN T_TRADER_CUSTOMER T2 ON T1.TRADER_ID = T2.TRADER_ID
    WHERE
      T2.TRADER_CUSTOMER_ID IS NOT NULL
      AND date_sub( curdate( ), INTERVAL 30 DAY ) <![CDATA[<=]]> date( FROM_UNIXTIME( T1.ADD_TIME / 1000, '%Y-%m-%d ' ) );
  </select>
  <select id="selectBusinessChanceTimesByTraderId" resultType="com.vedeng.order.model.BussinessChance">
    select *
    from T_BUSSINESS_CHANCE
    where TRADER_ID = #{traderId,jdbcType=INTEGER}
      and ADD_TIME >= #{addTime}
    limit 1
  </select>
    <select id="listBussinessByMobileAndSource" resultType="com.vedeng.order.model.BussinessChance">
      SELECT
        COMMUNICATION AS communication,
        MOBILE AS mobile,
        USER_ID AS userId,
        ADD_TIME AS addTime
      FROM
        T_BUSSINESS_CHANCE
      WHERE
        MOBILE = #{loginMobile}
        AND COMMUNICATION=#{source}
      ORDER BY ADD_TIME desc
      LIMIT 1
    </select>

  <select id="getBusinessChanceByChanceNo" resultType="com.vedeng.order.model.BussinessChance">
    SELECT
    *
    FROM T_BUSSINESS_CHANCE B
    WHERE BUSSINESS_CHANCE_NO = #{bussinessChanceNo}
    ORDER BY BUSSINESS_CHANCE_ID ASC LIMIT 1
  </select>

  <select id="queryForassignBussinessChance" parameterType="java.util.List"  resultType="com.vedeng.order.model.BussinessChance">
    select
    *
    from
    T_BUSSINESS_CHANCE
    where
    BUSSINESS_CHANCE_ID IN
    <foreach item="bussinessChanceId" index="index" collection="bussinessChanceIds" open="(" separator="," close=")">
      #{bussinessChanceId}
    </foreach>
  </select>

  <select id="getCrmTaskCount" resultType="java.lang.Integer">
    select count(*)
    from T_TASK
    where CREATOR  = #{userId,jdbcType=INTEGER}
    and IS_DELETE = 0
  </select>

  <select id="getCrmTodoTaskCount" resultType="java.lang.Integer">
    select count(*)
    from T_TASK_ITEM
    where TASK_USER  = #{userId,jdbcType=INTEGER}
    and DONE_STATUS = 0
    and IS_DELETE = 0
  </select>

  <select id="getCrmTaskGroupCount" resultType="com.vedeng.order.model.vo.TaskGroupVo">
    <![CDATA[
    select DATE(DEADLINE) AS dateStr,
    SUM(CASE WHEN TASK_ITEM.DONE_STATUS = 0 THEN 1 ELSE 0 END) AS undoneCount,
    SUM(CASE WHEN TASK_ITEM.DONE_STATUS = 1 THEN 1 ELSE 0 END) AS doneCount,
    SUM(CASE WHEN TASK_ITEM.DONE_STATUS = 2 THEN 1 ELSE 0 END) AS closeCount
    from T_TASK_ITEM TASK_ITEM
    JOIN T_TASK TASK_HEAD ON TASK_ITEM.TASK_ID = TASK_HEAD.TASK_ID
    where TASK_ITEM.TASK_USER  = #{userId,jdbcType=INTEGER}

    and TASK_ITEM.IS_DELETE = 0
    AND TASK_HEAD.DEADLINE >= concat(#{startDate,jdbcType=VARCHAR},' 00:00:00')
    AND TASK_HEAD.DEADLINE <= concat(#{endDate,jdbcType=VARCHAR},' 23:59:59')
    group by DATE(DEADLINE)
    ]]>
  </select>

  <select id="getCrmTaskForOneDay" resultType="com.vedeng.order.model.vo.TaskDetailVo">
    <![CDATA[
    select
        TASK_HEAD.TASK_CONTENT as taskContent,
        TASK_HEAD.BIZ_NO AS bizNo,
        TASK_ITEM.TASK_ID as taskId,
        TASK_ITEM.TASK_ITEM_ID as taskItemId,
        TASK_ITEM.DONE_STATUS as doneStatus
    from T_TASK_ITEM TASK_ITEM
    JOIN T_TASK TASK_HEAD ON TASK_ITEM.TASK_ID = TASK_HEAD.TASK_ID
    where TASK_ITEM.TASK_USER  = #{userId,jdbcType=INTEGER}
    and TASK_ITEM.IS_DELETE = 0
    AND TASK_HEAD.DEADLINE >= concat(#{dateStr,jdbcType=VARCHAR},' 00:00:00')
    AND TASK_HEAD.DEADLINE <= concat(#{dateStr,jdbcType=VARCHAR},' 23:59:59')
    ORDER BY TASK_ITEM.DONE_STATUS asc,TASK_ITEM.ADD_TIME  DESC
    ]]>
  </select>

</mapper>