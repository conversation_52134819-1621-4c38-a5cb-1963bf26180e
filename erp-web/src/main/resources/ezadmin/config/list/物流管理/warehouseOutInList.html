<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>入库单列表</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="warehouseOutInList" datasource="erp-datasourcetarget" empty_show="" fixednumber="0" firstcol="numbers">
<div id="appendHead">
</div>

<div class="layui-fluid">
    <form class="layui-form" id="search">
        <div class="layui-inline ">
            <label>入库单号</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder="请输入入库单号"  oper="like"
                       name="OUT_IN_NO" empty_show="-" validate_rules="{&quot;maxlength&quot;:&quot;50&quot;}" validate_messages="{&quot;maxlength&quot;:&quot;长度不能超过50&quot;}">
            </div>
        </div>

        <div class="layui-inline ">
            <label>发货方</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder="请输入发货方名称"  oper="like"
                       name="OUT_IN_COMPANY" empty_show="-" validate_rules="{&quot;maxlength&quot;:&quot;50&quot;}" validate_messages="{&quot;maxlength&quot;:&quot;长度不能超过50&quot;}">
            </div>
        </div>

        <div class="layui-inline ">
            <label>WMS入库单号</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder="请输入WMS入库单号"  oper="like"
                       name="WMS_NO" empty_show="-" validate_rules="{&quot;maxlength&quot;:&quot;50&quot;}" validate_messages="{&quot;maxlength&quot;:&quot;长度不能超过50&quot;}">
            </div>
        </div>

        <div class="layui-inline ">
            <label>关联单号</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder="请输入关联单号"  oper="like"
                       name="RELATE_NO" empty_show="-" validate_rules="{&quot;maxlength&quot;:&quot;50&quot;}" validate_messages="{&quot;maxlength&quot;:&quot;长度不能超过50&quot;}">
            </div>
        </div>

        <div class="layui-inline ">
            <label>入库时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" jdbctype="DATETIME" type="search"
                        placeholder="请输入" name="OUT_IN_TIME" oper="BETWEEN"></object>
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">入库单类型</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="OUT_IN_TYPE"  jdbctype="INTEGER" placeholder="全部"
                        data='[{"K":"1","V":"采购入库单"},{"K":"8","V":"采购售后换货入库单"},{"K":"9","V":"外借归还入库"},{"K":"12","V":"盘盈入库单"},{"K":"3","V":"销售换货入库单"},{"K":"5","V":"销售退货入库单"},{"K":"17","V":"采购赠品入库单"},{"K":"20","V":"库存转换入库"}]'
                        datatype="json"></select>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">数据来源</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="SOURCE"  jdbctype="VARCHAR" placeholder="全部"
                        data='[{"K":"ERP","V":"ERP"},{"K":"WMS","V":"WMS"}]'
                        datatype="json"></select>
            </div>
        </div>
    </form>
    <hr class="layui-border-blue">

    <table id="table" class="layui-table" style=" width:100%">
        <thead>

        <tr id="column">
            <th name="OUT_IN_NO" body="td-link"  windowname="入库单详情" url="/warehouseOutIn/detail.do?warehouseGoodsOutInId=${WAREHOUSE_GOODS_OUT_IN_ID}" opentype="PARENT" datatype="" data="" style="word-break: break-all;">入库单号</th>
            <th name="RELATE_NO" body="td-link"  url="/warehouseOutIn/to/relatedPage.do?warehouseGoodsOutInId=${WAREHOUSE_GOODS_OUT_IN_ID}" opentype="PARENT" head="th">关联单号</th>
            <th name="WMS_NO">WMS入库单号</th>
            <th name="OUT_IN_TYPE">入库单类型</th>
            <th name="OUT_IN_COMPANY">发货方</th>
            <th name="ADD_TIME">入库时间</th>
            <th name="SOURCE">数据来源</th>

<!--            <th type="rowbutton" id="rowbutton">-->
<!--                <button class="layui-btn list-row-button" type="single" opentype="MODEL"-->
<!--                        url="/bank/edit.do?bankId=${BANK_ID}" windowname="编辑" area="600px,400px"-->
<!--                        name="update">编辑-->
<!--                </button>-->
<!--                <button class="layui-btn list-row-button" type="single" opentype="CONFIRM_AJAX"-->
<!--                        url="/bank/delete.do?bankId=${BANK_ID}" windowname="删除" name="update">删除-->
<!--                </button>-->
<!--            </th>-->
        </tr>
        </thead>
        <tbody>
        <tr>
            <td colspan="100">
                express:
                <pre id="express" class="layui-code" orderby="order by WAREHOUSE_GOODS_OUT_IN_ID desc" groupby="">
                    select WAREHOUSE_GOODS_OUT_IN_ID,OUT_IN_NO,RELATE_NO,WMS_NO,
                    case OUT_IN_TYPE
                    when '1' then '采购入库单'
                    when '3' then '销售换货入库单'
                    when '5' then '销售退货入库单'
                    when '8' then '采购售后换货入库单'
                    when '9' then '外借归还入库'
                    when'12' then '盘盈入库单'
                    when '17' then '采购赠品入库单'
                    when '20' then '库存转换入库'end as OUT_IN_TYPE,
                    OUT_IN_COMPANY, DATE_FORMAT(OUT_IN_TIME, '%Y-%m-%d %H:%i:%s') ADD_TIME,SOURCE,UPDATE_REMARK from T_WAREHOUSE_GOODS_OUT_IN
                             where IS_DELETE = 0 AND OUT_IN_TYPE in (1,3,5,8,9,12,17,20)
                 </pre>
            </td>
        </tr>
        </tbody>
    </table>
</div>
<div id="appendFoot">
    <script type="text/javascript">
        $(function(){
            //console.log($("#mytable tbody [item_name='RELATE_NO']").length);
            $("#mytable tbody tr").each(function(index,obj){
                    var UPDATE_REMARK = $(obj).find("[item_name='UPDATE_REMARK']")[0].value;
                    if(UPDATE_REMARK != undefined && '21-22其他' == UPDATE_REMARK) {
                        var content = $(obj).find("[item_name='RELATE_NO']").text();
                        var objtd =  $(obj).find("[item_name='RELATE_NO']:visible");
                        objtd.text(content);
                    }
                }
            )
        })
    </script>
</div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    })
</script>
</body>
</html>