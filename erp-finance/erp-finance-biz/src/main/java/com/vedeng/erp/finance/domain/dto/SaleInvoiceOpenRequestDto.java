package com.vedeng.erp.finance.domain.dto;

import com.vedeng.infrastructure.taxes.base.ITaxesParam;
import com.vedeng.infrastructure.taxes.utils.TaxesUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 销项开票dto
 */
@Data
public class SaleInvoiceOpenRequestDto implements ITaxesParam {

    private static final long serialVersionUID = 8326710371092895916L;

    /**
     * 纳税人识别号
     */
    private String nsrsbh;

    private DataInfo data;

    @Data
    public static class DataInfo {
        /**
         * 订单流水号(唯一)
         */
        private String orderNo;
        /**
         * 01-电子专票 02-电子普票
         */
        private String fppzDm;
        /**
         * 特殊业务类型代码
         */
        private String tdyslxDm;
        /**
         * 销售方税号
         */
        private String xsfnsrsbh;
        /**
         * 销售方名称
         */
        private String xsfmc;
        /**
         * 销售方地址
         */
        private String xsfdz;
        /**
         * 销售方电话
         */
        private String xsflxdh;
        /**
         * 销售方开户行
         */
        private String xsfkhh;
        /**
         * 销售方银行账号
         */
        private String xsfyhzh;
        /**
         * 购买方税号
         */
        private String gmfnsrsbh;
        /**
         * 购买方名称
         */
        private String gmfmc;
        /**
         * 购买方地址
         */
        private String gmfdz;
        /**
         * 购买方电话
         */
        private String gmflxdh;
        /**
         * 购买方开户行
         */
        private String gmfkhh;
        /**
         * 购买方银行账号
         */
        private String gmfyhzh;
        /**
         * 开票人
         */
        private String kpr;
        /**
         * 收款人
         */
        private String skr;
        /**
         * 审核人
         */
        private String fhr;
        /**
         * 合计金额
         */
        private BigDecimal hjje;
        /**
         * 合计税额
         */
        private BigDecimal hjse;
        /**
         *默认传0
         */
        private int jehj;
        /**
         *价税合计
         */
        private String jshj;
        /**
         * 扣除额
         */
        private int kce;
        /**
         * 开票方纳税人识别
         */
        private String kpfnsrsbh;
        /**
         * 明细总行数
         */
        private int spsl;
        /**
         * 备注
         */
        private String bz;
        /**
         * 含税标志 1-不含税 2-含税
         */
        private String hsbz;
        /**
         * 是否展示购方银行账号
         */
        private String sfzsgmfyhzh;
        /**
         * 是否展示销方银行账号
         */
        private String sfzsxsfyhzh;
        private List<Mxzb> mxzbList;
        private List<Hwysfwdzfpmxb> hwysfwdzfpmxbList;

        public DataInfo(){
            this.tdyslxDm = "";
            this.xsfnsrsbh = TaxesUtil.taxesConfig.taxNo;
            this.xsfmc = TaxesUtil.taxesConfig.taxName;
            this.xsfdz = TaxesUtil.taxesConfig.address;
            this.xsflxdh = TaxesUtil.taxesConfig.telephone;
            this.xsfkhh = TaxesUtil.taxesConfig.bankName;
            this.xsfyhzh = TaxesUtil.taxesConfig.bankAccount;
            this.kpr = "";
            this.skr = "";
            this.fhr = "";
            this.jehj = 0;
            this.kce = 0;
            this.kpfnsrsbh = TaxesUtil.taxesConfig.taxNo;
            this.bz = "";
            this.hsbz = "1";
            this.sfzsgmfyhzh = "N";
            this.sfzsxsfyhzh = "N";
            this.hwysfwdzfpmxbList = new ArrayList<>();
        }
    }

    @Data
    public static class Mxzb {
        private int xh;
        /**
         * 发票行性质
         * 0-正常行 1-被折扣行 2-折扣行
         */
        private String fphxzDm;
        /**
         * 项目名称
         */
        private String xmmc;
        /**
         * 货物或应税劳务、服务名称
         *格式：*简称*项目名称
         */
        private String hwhyslwfwmc;
        /**
         *税收分类编码
         */
        private String sphfwssflhbbm;
        /**
         * 税收分类编码简称
         */
        private String spfwjc;
        /**
         * 规格型号
         */
        private String ggxh;
        /**
         * 单位
         */
        private String dw;
        /**
         * 商品数量
         */
        private String spsl;
        /**
         * 单价
         */
        private BigDecimal dj;
        /**
         * 含税单价
         */
        private BigDecimal je;
        /**
         *含税金额
         */
        private BigDecimal hsje;
        /**
         * 税率
         */
        private String slv;
        /**
         *税额
         */
        private BigDecimal se;
        /**
         * 扣除额
         */
        private String kce;
        /**
         * 含税单价:开票申请单价
         */
        private BigDecimal hsdj;
        /**
         * 不含税单价  :不含税金额/商品数量：je/spsl 保留8位小数
         */
        private BigDecimal bhsdj;
        /**
         * 不含税金额:含税金额 - 税额
         */
        private BigDecimal bhsje;
        /**
         * 零税率标识
         */
        private String lslbz;
        /**
         * 享受优惠政策标志
         */
        private String xsyhzcbz;
        /**
         * 享受优惠政策类型代码
         */
        private String ssyhzclxDm;
        /**
         * 增值税特殊管理
         */
        private String zzstsgl;
        /**
         * 特定征税方式代码
         */
        private String tdzsfsDm;

        public Mxzb(){
            this.fphxzDm = "0";
            this.kce = "";
            this.lslbz = "";
            this.xsyhzcbz = "";
            this.ssyhzclxDm = "";
            this.zzstsgl = "";
            this.tdzsfsDm = "";
        }
    }

    @Data
    public class Hwysfwdzfpmxb {
        private String xh;
        private String qyd;
        private String ddd;
        private String ysgjzl;
        private String ysgjph;
        private String yshwmc;
    }

    public SaleInvoiceOpenRequestDto(){
        this.nsrsbh = TaxesUtil.taxesConfig.taxNo;
    }

}
