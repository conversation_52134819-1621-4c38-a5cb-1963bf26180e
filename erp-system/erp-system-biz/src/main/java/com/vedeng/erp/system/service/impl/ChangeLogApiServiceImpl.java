package com.vedeng.erp.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.vedeng.erp.system.common.annotation.ChangeLogAnnotation;
import com.vedeng.erp.system.common.enums.ChangeRelatedTableEnums;
import com.vedeng.erp.system.domain.dto.ChangeLog;
import com.vedeng.erp.system.dto.ChangeLogDto;
import com.vedeng.erp.system.mapper.ChangeLogMapper;
import com.vedeng.erp.system.service.ChangeLogApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;

/**
 * <AUTHOR>
 * @ClassName ChangeLogApiServiceImpl.java
 * @Description TODO
 * @createTime 2023年02月07日 14:02:00
 */
@Slf4j
@Service
public class ChangeLogApiServiceImpl implements ChangeLogApiService {

    @Resource
    private ChangeLogMapper changeLogMapper;

    @Override
    public void saveLog(ChangeRelatedTableEnums enums, List<?> newData, List<?> oldData, Integer creator) {
        Integer relatedId = enums.getRelatedId();
        String realtedTable = enums.getRealtedTable();
        if (relatedId == null){
            log.info("saveLog relatedId为空"+ realtedTable);
            return;
        }
        if(ObjectUtil.isEmpty(newData) && ObjectUtil.isEmpty(oldData)){
            log.info("saveLog 数据为空"+ realtedTable +" chanid: "+enums.getRelatedId() );
            return;
        }
        ChangeLog saveLog = new ChangeLog();
        saveLog.setRelatedId(relatedId);
        saveLog.setRelatedTable(realtedTable);
        saveLog.setCreator(creator);

        HashMap<String,HashMap<String,String>> newMap = new HashMap<>();
        HashMap<String,HashMap<String,String>> oldMap = new HashMap<>();
        try {
            getDataChange(newData, newMap);
            getDataChange(oldData, oldMap);

            Set<String> newKey = newMap.keySet();
            Set<String> oldKey = oldMap.keySet();

            StringBuilder logMessage = new StringBuilder("");

            List<String> addKey = CollUtil.subtractToList(newKey, oldKey);
            for (String add : addKey) {
                logMessage.append("新增: ").append(add).append("</br> ");
            }

            List<String> delKey = CollUtil.subtractToList(oldKey, newKey);
            for (String del : delKey) {
                logMessage.append("删除: ").append(del).append("</br> ");
            }
            //编辑文案
            Collection<String> editKey = CollUtil.intersection(newKey, oldKey);
            editLogMessage(newMap, oldMap, logMessage, editKey);

            saveLog.setLogMessage(logMessage.toString());
        }catch (Exception e){
            log.error("saveLog error",e);
        }
        if(!StrUtil.isBlank(saveLog.getLogMessage())){
            changeLogMapper.insertSelective(saveLog);
        }
    }

    private static void editLogMessage(HashMap<String, HashMap<String, String>> newMap, HashMap<String, HashMap<String, String>> oldMap, StringBuilder logMessage, Collection<String> editKey) {
        for (String edit : editKey) {
            HashMap<String, String> newValueMap = newMap.get(edit);
            HashMap<String, String> oldValueMap = oldMap.get(edit);

            Set<String> valueKeySet = CollUtil.unionDistinct(newValueMap.keySet(), oldValueMap.keySet());
            for (String valueKey : valueKeySet) {
                String newValue = newValueMap.get(valueKey);
                String oldValue = oldValueMap.get(valueKey);
                if(!ObjectUtil.equals(newValue,oldValue)){
                    logMessage.append("编辑: ").append(edit).append(" ").append(valueKey).
                            append(" 原值: ").append(oldValue).append(" 新值: ").append(newValue).append("</br> ");
                }
            }
        }
    }

    private static void getDataChange(List<?> newData, HashMap<String, HashMap<String,String>> newMap) throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
        if(CollUtil.isEmpty(newData)){
            return;
        }
        for (Object newDatum : newData) {
            Class<?> aClass = newDatum.getClass();
            Field[] declaredFields = aClass.getDeclaredFields();
            String keyStr = "";
            HashMap<String,String> fieldValue = new HashMap<>();
            for (Field declaredField : declaredFields) {
                ChangeLogAnnotation annotation = declaredField.getAnnotation(ChangeLogAnnotation.class);
                if(annotation != null){
                    boolean key = annotation.isKey();
                    String compareName = annotation.compareName();
                    Method declaredMethod = aClass.getDeclaredMethod(captureName(declaredField.getName()));
                    Object invoke = declaredMethod.invoke(newDatum, null);
                    String value = invoke != null ? invoke.toString() : "" ;
                    if (key){
                        keyStr = keyStr + value;
                    }else {
                        fieldValue.put(compareName,value);
                    }
                }
            }
            newMap.put(keyStr,fieldValue);
        }
    }

    private static String captureName(String str){
        char[] cs=str.toCharArray();
        cs[0]-=32;
        return "get"+String.valueOf(cs);
    }

    @Override
    public List<ChangeLogDto> getInfo(ChangeRelatedTableEnums enums) {
        return changeLogMapper.getInfo(enums.getRealtedTable(),enums.getRelatedId());
    }

    @Override
    public void insertSelective(ChangeRelatedTableEnums enums, String logMessage, Integer creator) {
        Integer relatedId = enums.getRelatedId();
        String realtedTable = enums.getRealtedTable();
        if (relatedId == null){
            log.info(" ChangeLogApiServiceImpl insertSelective relatedId为空"+ realtedTable);
            return;
        }
        if(StrUtil.isBlank(logMessage)){
            log.info("ChangeLogApiServiceImpl insertSelective 数据为空"+ realtedTable +" chanid: "+enums.getRelatedId() );
            return;
        }
        ChangeLog saveLog = new ChangeLog();
        saveLog.setRelatedId(relatedId);
        saveLog.setRelatedTable(realtedTable);
        saveLog.setCreator(creator);
        saveLog.setLogMessage(logMessage);
        changeLogMapper.insertSelective(saveLog);
    }
}
