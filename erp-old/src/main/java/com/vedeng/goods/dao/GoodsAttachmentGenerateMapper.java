package com.vedeng.goods.dao;

import com.vedeng.goods.model.GoodsAttachmentGenerate;
import com.vedeng.goods.model.GoodsAttachmentGenerateExample;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;
import java.util.List;

@Named("GoodsAttachmentGenerateMapper")
public interface GoodsAttachmentGenerateMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_ATTACHMENT
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    int countByExample(GoodsAttachmentGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_ATTACHMENT
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    int deleteByExample(GoodsAttachmentGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_ATTACHMENT
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    int deleteByPrimaryKey(Integer goodsAttachmentId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_ATTACHMENT
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    int insert(GoodsAttachmentGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_ATTACHMENT
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    int insertSelective(GoodsAttachmentGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_ATTACHMENT
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    List<GoodsAttachmentGenerate> selectByExample(GoodsAttachmentGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_ATTACHMENT
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    GoodsAttachmentGenerate selectByPrimaryKey(Integer goodsAttachmentId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_ATTACHMENT
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    int updateByExampleSelective(@Param("record") GoodsAttachmentGenerate record, @Param("example") GoodsAttachmentGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_ATTACHMENT
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    int updateByExample(@Param("record") GoodsAttachmentGenerate record, @Param("example") GoodsAttachmentGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_ATTACHMENT
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    int updateByPrimaryKeySelective(GoodsAttachmentGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_ATTACHMENT
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    int updateByPrimaryKey(GoodsAttachmentGenerate record);
}