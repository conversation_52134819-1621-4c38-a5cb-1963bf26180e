package com.vedeng.trader.controller;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.trader.domain.dto.TraderCustomerInfoDto;
import com.vedeng.erp.trader.domain.dto.TraderCustomerRelationQueryDto;
import com.vedeng.erp.trader.service.RSalesJTraderApiService;
import com.vedeng.erp.trader.service.TraderCustomerBaseService;
import com.vedeng.system.model.vo.AccountSalerToGo;
import com.vedeng.trader.dao.TraderCustomerMapper;
import com.vedeng.trader.dao.TraderMapper;
import com.vedeng.trader.model.TraderCustomer;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import com.vedeng.trader.service.TraderCustomerService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.UnsupportedEncodingException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping(value = "/trader")
public class TraderCustomerBaseController extends BaseController {

    @Autowired
    private TraderMapper traderMapper;

    @Autowired
    private TraderCustomerMapper traderCustomerMapper;

    @Autowired
    TraderCustomerBaseService traderCustomerBaseService;

    @Autowired
    private TraderCustomerService traderCustomerService;

    @Autowired
    private UserApiService userApiService;
    
    @Autowired
    private RSalesJTraderApiService rSalesJTraderApiService;
    
    
    /**
     * .
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2022/2/16 9:44.
     * @author: Randy.Xu.
     * @param modelAndView
     * @return: org.springframework.web.servlet.ModelAndView.
     * @throws:  .
     */
    @RequestMapping(value = "/relation/info")
    public ModelAndView getRelatedInfo(ModelAndView modelAndView, TraderCustomerRelationQueryDto traderQueryDto, HttpServletRequest request, HttpSession session) {
        User user =(User)session.getAttribute(ErpConst.CURR_USER);
        TraderCustomerVo traderCustomerVo = new TraderCustomerVo();
        traderCustomerVo.setTraderCustomerId(traderQueryDto.getTraderCustomerId());
        traderCustomerVo.setTraderId(traderQueryDto.getTraderId());
        traderCustomerVo.setBelongPlatform(traderMapper.getTraderByTraderId(traderQueryDto.getTraderId()).getBelongPlatform());
        List<TraderCustomerInfoDto> relatedTraderCustomerList = traderCustomerBaseService.getTraderRelationInfo(traderQueryDto);
        // 数据权限 查看自己和下属和下下属的数据 + 分享客户
        if (CollUtil.isNotEmpty(relatedTraderCustomerList)){
            List<Integer> traderId = relatedTraderCustomerList.stream().map(TraderCustomerInfoDto::getTraderId).distinct().collect(Collectors.toList());
            List<Integer> allSubUserId = userApiService.getAllSubUserList(CurrentUser.getCurrentUser());
            List<Integer> traderIdList =  rSalesJTraderApiService.getShareAndBelongTrader(allSubUserId,traderId);

            for (TraderCustomerInfoDto traderCustomerInfoDto : relatedTraderCustomerList) {
                if (traderIdList.contains(traderCustomerInfoDto.getTraderId())){
                    traderCustomerInfoDto.setIsAuth(true);
                }else {
                    traderCustomerInfoDto.setIsAuth(false);
                }
            }
        }
        
        
        modelAndView.addObject("traderList",relatedTraderCustomerList);
        modelAndView.addObject("traderCustomer",traderCustomerVo);
        modelAndView.addObject("method","relation");
        modelAndView.addObject("user",user);
        modelAndView.setViewName("trader/relation/relationInfo");
        return modelAndView;
    }


    @RequestMapping(value = "/publicCustomer/isPrivatized",method = RequestMethod.GET)
    @ResponseBody
    @NoNeedAccessAuthorization
    public ResultInfo checkTraderCustomerIsPrivatized(@RequestParam Integer traderId){
        TraderCustomer traderCustomer = traderCustomerMapper.getBaseCustomerByTraderId(traderId);
        if (traderCustomer == null) {
            return ResultInfo.error("该客户不存在！");
        }
        if (traderCustomerBaseService.checkTraderCustomerIsPrivatizedAndNotUnlocked(Collections.singletonList(traderCustomer.getTraderCustomerId()))){
            return ResultInfo.error("该客户尚未解锁，请解锁后再关联客户");
        }
        return ResultInfo.success();
    }


    @RequestMapping(method = RequestMethod.GET, value = "/relation/associate")
    @ResponseBody
    public ResultInfo<String> associateTraderCustomer(@RequestParam Integer traderCustomerId, @RequestParam Integer associatedTraderCustomerId){
        ResultInfo<String> resultInfo;
        String result = traderCustomerBaseService.associateTraderCustomer(traderCustomerId, associatedTraderCustomerId);
        if (result == null) {
            resultInfo = ResultInfo.success();
        } else {
            resultInfo = ResultInfo.error(result);
        }
        return resultInfo;
    }


    @RequestMapping(method = RequestMethod.GET, value = "/relation/cancel_associate")
    @ResponseBody
    public ResultInfo<String> cancelAssociateTraderCustomer(@RequestParam Integer traderCustomerId){
        ResultInfo<String> resultInfo;
        String result = traderCustomerBaseService.cancelAssociateTraderCustomer(traderCustomerId);
        if (result == null) {
            resultInfo = ResultInfo.success();
        } else {
            resultInfo = ResultInfo.error(result);
        }
        return resultInfo;
    }

    /**
     * 关联客户页面，搜索客户
     * @param request
     * @param traderId
     * @param searchTraderName
     * @param pageNo
     * @param pageSize
     * @return
     * @throws UnsupportedEncodingException
     */
    @RequestMapping(value = "/relation/searchCustomerList")
    public ModelAndView getOptionalTrader(HttpServletRequest request,
                                          @RequestParam Integer traderId,
                                          @RequestParam String searchTraderName,
                                          @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                          @RequestParam(required = false, defaultValue = "10") Integer pageSize) throws UnsupportedEncodingException {
        ModelAndView mv = new ModelAndView("/trader/customer/search_customer_relation");
        TraderCustomerVo traderCustomerVo = new TraderCustomerVo();
        if (StringUtils.isNoneBlank(searchTraderName)){
            traderCustomerVo.setSearchTraderName((java.net.URLDecoder.decode(java.net.URLDecoder.decode(searchTraderName, "UTF-8"), "UTF-8")).trim());
        }
        traderCustomerVo.setCompanyId(1);
        traderCustomerVo.setTraderId(traderId);
        traderCustomerVo.setTraderCustomerId(traderCustomerMapper.getCustomerInfo(traderId).getTraderCustomerId());
        traderCustomerVo.setIsPrivatized(1);
        List<AccountSalerToGo> sales = traderMapper.getAccountSaler(Collections.singletonList(traderId));
        if (!sales.isEmpty()){
            traderCustomerVo.setUserId(sales.get(0).getUserId());
        }
        Page page = getPageTag(request, pageNo, pageSize);
        Map<String, Object> map = traderCustomerService.searchCustomerPageList(traderCustomerVo, page);
        List<TraderCustomerVo> searchCustomerList = (List<TraderCustomerVo>) map.get("searchCustomerList");
        mv.addObject("searchCustomerList", searchCustomerList);
        mv.addObject("page", map.get("page"));
        mv.addObject("traderCustomer",traderCustomerVo);
        return mv;
    }

}
