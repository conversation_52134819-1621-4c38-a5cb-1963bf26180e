$(function() {
    debugger
    document.getElementById('otherCheckbox').addEventListener('change', function() {
        var otherInput = document.getElementById('otherPosition');
        if (this.checked) {
            otherInput.style.display = 'inline-block'; // 显示输入框
        } else {
            otherInput.style.display = 'none'; // 隐藏输入框
        }
    });

    debugger
    // 假设这是你从后端获取的值
    var selectedPositions = $('#allPosition').val();

    // 将字符串按逗号分割成数组
    var positionsArray = selectedPositions.split(',');

    // 用于存储未匹配的值
    var unmatchedPositions = [];

    // 遍历数组，设置对应的复选框为选中状态
    positionsArray.forEach(position => {
        // 去除首尾空格
        position = position.trim();
        
        // 查找对应的复选框
        var checkbox = document.querySelector(`input[name="position"][value="${position}"]`);

        if (checkbox) {
            // 如果找到对应的复选框，则选中
            checkbox.checked = true;
        } else if (position) {
            // 如果没有找到对应的复选框，则收集到未匹配的数组中
            unmatchedPositions.push(position);
        }
    });

    // 如果有未匹配的值，则显示“其他”输入框并填入值
    if (unmatchedPositions.length > 0) {
        document.getElementById('otherCheckbox').checked = true;
        document.getElementById('otherPosition').style.display = 'inline-block';
        document.getElementById('otherPosition').value = unmatchedPositions.join(', ');
       
    }
    debugger
    if (positionsArray.includes("其他")){
        var otherInput = document.getElementById('otherPosition');
        otherInput.style.display = 'inline-block'; // 显示输入框
    }

    $('.J-upload').each(function (i) {
        var _this = this;
        new Upload({
            limit: 3,
            url: GLOBAL.IMGUPLOADURL,
            wrapper: $(this),
            uploadName: 'upload' + i,
            list: JSON.parse($(this).siblings('.J-upload-data').val() || '[]'),
            onchange: function () {
                $(_this).find('.J-upload-item').each(function (idx, elem) {
                    var data = $(this).data('item');
                    $(this).find('.J-item-name').remove();
                    var targetElement=$(_this).attr("type")+"["+idx+"]";
                    $(this).append('<input type="hidden" class="J-item-name" name="'+targetElement+'.relativePath"  value="' + data.filePath + '" >');
                    $(this).append('<input type="hidden" class="J-item-name" name="'+targetElement+'.displayName"  value="' + data.fileName + '" >');
                    var traderCertificateId = data.traderCertificateId != null && data.traderCertificateId != undefined ? data.traderCertificateId:"";
                    $(this).append('<input type="hidden" class="J-item-name" name="'+targetElement+'.traderCertificateId"  value="' + traderCertificateId + '" >');

                })
                if (i == 0) {
                    $(_this).find('[name^=upload]').valid();
                }
            },
            filters: {
                mime_types: [
                    { title: "Image files", extensions: "jep,png,jpeg" }
                ],
                max_file_size: '10MB'
            },
            onError: function (error) {
                var errorMsg = {
                    TYPE: '上传图片格式为：jep、png、jpeg格式',
                    SIZE: '图片大小不超过10M'
                }
                if (error) {
                    var $error = $(_this).siblings('.J-upload-error');
                    $error.show().find('label').html(errorMsg[error]).show();
                    timeout && clearTimeout(timeout);
                    timeout = setTimeout(function () {
                        $error.hide();
                    }, 3000)
                }
            }
        });
    })

    window.localStorage.removeItem('addsuccess');

    //增加关闭提示
    GLOBAL.addtip();
    $("#dept span").click(function(){
        checkLogin();
        $("#department").val($(this).text());
    })

    $("#posi span").click(function(){
        checkLogin();
        $("#position").val($(this).text());
    })

    $("#myform").submit(function(){
        checkLogin();
        //姓名
        var realName = $("#name").val();
        var realNameReg = /^[a-zA-Z0-9\u4e00-\u9fa5\.]{2,16}$/;
        var realMessage = "员工姓名不允许使用特殊字符";
        if(realName == ''){
            warnTips("name","员工姓名不允许为空");
            return  false;
        }else{
            delWarnTips("name");
        }
        if(realName.length < 2 || realName.length > 16){
            warnTips("name","员工姓名长度应该在2-16个字符之间");
            return false;
        }else{
            delWarnTips("name");
        }
        if(!realNameReg.test(realName)){
            warnTips("name",realMessage);
            return  false;
        }else{
            delWarnTips("name");
        }
        var pattern = new RegExp("[/]");
        // if($("#department").val().length > 0 && pattern.test($("#department").val())){
        //     warnTips("department","部门名称不允许使用特殊字符");
        //     return  false;
        // }else{
        //     delWarnTips("department");
        // }
        // if($("#department").val().length > 64){
        //     warnTips("department","部门名称长度不能超过64个字符");
        //     return  false;
        // }else{
        //     delWarnTips("department");
        // }


        var checkboxes = document.querySelectorAll('input[name="position"]:checked');
        var otherCheckbox = document.getElementById('otherCheckbox');
        var otherInput = document.getElementById('otherPosition');
        var errorMessage = document.getElementById('positionError');
        if (checkboxes.length === 0) {
            errorMessage.textContent = '请选择联系人职位！';
            return false; // 阻止表单提交
        }

        // 校验“其他”选项是否填写
        if (otherCheckbox.checked && otherInput.value.trim() === '') {
            errorMessage.textContent = '请填写具体职位';
            return false; // 阻止表单提交
        }
        if (otherCheckbox.checked && otherInput.value.length > 50) {
            errorMessage.textContent = '其他职位不能超过50字！';
            return false; // 阻止表单提交
        }
        errorMessage.textContent = '';
        if (!otherCheckbox.checked){
            // 其他职位
            otherInput.value = "";
        }

        //电话
        var telephone = $("#telephone").val();
        var telephoneReg = /^(\d{3,4}-?)?\d{7,9}(-?\d{2,6})?$|^$/;
        var telephoneMessage = "电话格式错误";

        if(telephone != '' && !telephoneReg.test(telephone)){
            warnTips("telephone",telephoneMessage);
            return  false;
        }else{
            delWarnTips("telephone");
        }
        if($("#fax").val() != undefined && $("#fax").val().length > 0 && pattern.test($("#fax").val())){
            warnTips("fax","传真不允许使用特殊字符");
            return  false;
        }else{
            delWarnTips("fax");
        }
        if($("#fax").val() != undefined && $("#fax").val().length > 32){
            warnTips("fax","传真不允许超过32个字符");
            return  false;
        }else{
            delWarnTips("fax");
        }

        //手机
        if ($('#isVedengMember').val() != 1){
            var mobile = $("#mobile").val();
            var mobileReg = /^1\d{10}$|^$/;
            var mobileMessage = "手机格式错误";
            if(mobile == ''){
                warnTips("mobile","手机号不允许为空");
                return  false;
            }else{
                delWarnTips("mobile");
            }
            if(!mobileReg.test(mobile)){
                warnTips("mobile",mobileMessage);
                return  false;
            }else{
                delWarnTips("mobile");
            }
        }
      //  window.parent.location.reload();
        //手机2
        var mobile2 = $("#mobile2").val();
        if(!mobileReg.test(mobile2)){
            warnTips("mobile2",mobileMessage);
            return  false;
        }else{
            delWarnTips("mobile2");
        }

        //邮箱
        var email = $("#email").val();
        var emailReg = /^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+.[a-zA-Z0-9-.]+$/;
        var emailMessage = "邮箱格式错误";
        if(email != '' && !emailReg.test(email)){
            warnTips("email",emailMessage);
            return  false;
        }else{
            delWarnTips("email");
        }
        var qqReg=/^\d{5,16}$/;
        if($("#qq").val()!='' && !qqReg.test($("#qq").val())){
            warnTips("qq","qq格式不正确");
            return  false;
        }else{
            delWarnTips("qq");
        }
        if($("#weixin").val() != undefined && $("#weixin").val().length > 0 && pattern.test($("#weixin").val())){
            warnTips("weixin","微信不允许使用特殊字符");
            return  false;
        }else{
            delWarnTips("weixin");
        }
        if($("#weixin").val() != undefined && $("#weixin").val().length > 32){
            warnTips("weixin","微信不允许超过32个字符");
            return  false;
        }else{
            delWarnTips("weixin");
        }

        var comments = $("[name='comments']").val();
        if(comments !='' && comments.length > 128){
            warnTips("comments","备注长度不能超过128字符");
            return  false;
        }else{
            delWarnTips("comments");
        }

        if($("#mod").val()!="mod"){
            $.ajax({
                url:page_url+'/trader/customer/addSaveContact.do',
                data:$('#myform').serialize(),
                type:"POST",
                dataType : "json",
                async: false,
                success:function(data)
                {
                    if(data.code ==0){
                        if($("#pageType").val() == 1){
                            var st = data.data.split(",");
                            window.parent && window.parent.newContact({
                                traderContactId: st[2],
                                name: st[3],
                                telephone: st[5],
                                mobile: st[4]
                            });
                        }else {
                            var st = data.data.split(",");
                            var str = page_url + "/trader/customer/getContactsAddress.do?traderId=" + st[1] + "&traderCustomerId=" + st[0];
                            $("#contact").attr('href', str);
                            window.parent.location.reload();
                        }
                    }else{
                        layer.alert(data.message);
                    }
                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });
            return false;
        }

    })

});
