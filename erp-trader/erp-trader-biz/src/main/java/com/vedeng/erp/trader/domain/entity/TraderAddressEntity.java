package com.vedeng.erp.trader.domain.entity;

import lombok.Data;

/**
 * @description 交易者联系地址
 * <AUTHOR>
 * @date 2022/10/20 19:38
 **/

@Data
public class TraderAddressEntity {
    /**
     * 交易者地址ID
     */
    private Integer traderAddressId;

    /**
     * 交易者ID
     */
    private Integer traderId;

    /**
     * 所属类型 1::经销商（包含终端）2:供应商
     */
    private Integer traderType;

    /**
     * 是否有效 0否 1是
     */
    private Integer isEnable;

    /**
     * 最小级地区ID
     */
    private Integer areaId;

    /**
     * 多级地址逗号“,”拼接（冗余字段）
     */
    private String areaIds;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 是否默认联系地址
     */
    private Integer isDefault;

    /**
     * 邮编
     */
    private String zipCode;

    /**
     * 备注
     */
    private String comments;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 添加人
     */
    private Integer creator;

    /**
     * 最近一次编辑时间
     */
    private Long modTime;

    /**
     * 最近一次编辑人
     */
    private Integer updater;

    /**
     * 是否置顶0否 1是
     */
    private Integer isTop;
}